{"name": "flash-express/backyard", "type": "project", "description": "Backyard", "keywords": ["backyard", "flashexpress"], "homepage": "http://backyard.flashexpress.com", "license": "proprietary", "config": {"preferred-install": "dist", "allow-plugins": {"composer/installers": true}}, "require": {"vlucas/phpdotenv": "^2.4", "danielstjules/stringy": "~3.1.0", "mpdf/mpdf": "^7.1", "phpoffice/phpspreadsheet": "^1.5", "paragraph1/php-fcm": "^0.7.0", "webgeeker/validation": "^0.3.0", "hprose/hprose": ">=2.0.0", "aliyunmq/mq-http-sdk": "^1.0", "league/oauth2-server": "7.2", "lcobucci/jwt": "3.3.3", "phalcon/incubator": "^3.4", "guzzlehttp/guzzle": "6.5.5", "endroid/qr-code": "3.9.1", "composer/installers": "^2.2"}, "autoload": {"psr-4": {"": ""}, "files": ["app/util/helpers.php"]}, "scripts": {"post-autoload-dump": ["chmod 777 ./vendor/mpdf/mpdf/tmp/"]}, "require-dev": {"phpunit/phpunit": "^5.0", "ramsey/collection": "1.1.4", "ramsey/uuid": "4.1.1", "brick/math": "0.9.2", "fguillot/json-rpc": "^1.2"}, "repositories": {"packagist": {"type": "composer", "url": "https://mirrors.aliyun.com/composer/"}}}