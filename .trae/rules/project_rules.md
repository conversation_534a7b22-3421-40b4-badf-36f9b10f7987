# Backyard API 项目开发规范
- **项目名称**: Backyard API 系统
- **开发语言**: PHP 7.2.34
- **框架**: Phalcon 3.4
- **数据库**: MySQL 5.6.16 （backyard库）
- **架构模式**: MVC + Repository + Server

# 角色
你是PHP开发专家和测试专家拥有10年以上的开发经验，熟悉Phalcon框架，MySQL数据库，有项目开发经验。

# 项目开发前置说明
    1. **需求分析**: 遇到飞书文档使用feishu-mcp解析链接，获取需求文档内容，文档中的图片也要解析。
    2. **需求拆解**: 通过MCP下sequential-thinking顺序思维拆解需求，理解需求。
    3. **需求确认**: 搜索完成后打印需求标题给用户确认，用户确认后开始项目开发。
    4. **数据库验证**: 涉及models时先连接MCP模块下的mysql服务验证表结构，确认无误后开始项目开发。
    5. **代码注释**: 生成注释时author写AI，date通过mcp服务time获取最新时间“YYYY-MM-DD”，description根据需求文档内容，根据需求文档内容，根据需求文档内容。
    6. **代码质量**: 所有代码必须符合PSR-4自动加载规范，所有代码必须符合PSR-12规范。
    7. **记忆**:重点方法自动加入记忆。
    8. **单元测试**:默认不生成单元测试，有明确说明在生成单元测试，需要生成的测试方法生成到单元测试中，不需要调用本地脚本测试编写文件。
    9. **接口文档**:遇到yapi.flashexpress.pub接口文档，需要使用yapi-devloper-mcp 解析。
    10. **注释**:编写万方法要调整方法头注释。
    11. **风格**:生成的方法优先按照rules 规则内的风格编写，逻辑放到service层。controller只处理参数验证。
    12. **忽略**:方法带有@NotAi注释的方法忽略该方法，不作为上下文参考。

# 项目架构

## 项目结构说明

### 🏗️ 整体架构
├── app/                            # 应用核心目录
│   ├── asset/                      # 静态资源文件
│   │   ├── asset_courier_v14.json      # 快递员资源配置v14
│   │   ├── asset_courier.json          # 快递员资源配置
│   │   └── asset_district.json         # 地区资源配置
│   ├── cli.php                     # 命令行入口文件和任务注册
│   ├── common/                     # 公共函数和工具
│   │   └── functions.php               # 公共函数库（项目函数优先查找此目录）
│   ├── config/                     # 系统配置目录
│   │   ├── config.ini                  # 主配置文件
│   │   ├── config.php                  # PHP运行时配置
│   │   ├── loader.php                  # 类自动加载配置
│   │   ├── modules.php                 # 多国家模块配置
│   │   ├── router.php                  # 路由规则配置
│   │   └── services.php                # 依赖注入服务配置
│   ├── controllers/                # 控制器层 - API接口入口
│   │   ├── *Controller.php             # 各业务控制器类
│   │   ├── OpenApi/                    # 开放API控制器
│   │   │   ├── AuthController.php          # 认证控制器
│   │   │   ├── BaseController.php          # 开放API基类
│   │   │   └── DepartmentController.php    # 部门控制器
│   │   ├── Osm/                        # OSM系统控制器
│   │   │   ├── AttendanceController.php    # 考勤控制器
│   │   │   ├── ControllerBase.php          # OSM控制器基类
│   │   │   └── LoginController.php         # 登录控制器
│   │   ├── Pay/                        # 支付相关控制器
│   │   │   ├── BaseController.php          # 支付基类控制器
│   │   │   └── InteriorgoodsController.php # 内购商品控制器
│   │   └── School/                     # 培训控制器
│   ├── core/                       # 核心基础类库
│   │   ├── Config.php                  # 配置管理器
│   │   ├── PhalBaseController.php      # Phalcon控制器基类
│   │   ├── PhalBaseFilter.php          # Phalcon过滤器基类
│   │   ├── PhalBaseModel.php           # Phalcon模型基类
│   │   ├── PhalBaseRedis.php           # Redis操作基类
│   │   └── PhalBaseService.php         # Phalcon服务基类
│   ├── country/                    # 国家相关工具
│   │   └── Tools.php                   # 国家工具类
│   ├── enums/                      # 枚举定义目录
│   │   ├── ActivityEnums.php           # 活动枚举
│   │   ├── AiStateEnums.php            # AI状态枚举
│   │   ├── ApprovalFinderEnums.php     # 审批查找枚举
│   │   ├── modules/                    # 模块级枚举
│   │   │   ├── La/                         # 老挝枚举
│   │   │   ├── My/                         # 马来西亚枚举
│   │   │   ├── Ph/                         # 菲律宾枚举
│   │   │   └── Th/                         # 泰国枚举
│   │   └── [56个枚举文件]
│   ├── eventsListener/             # 事件监听器
│   │   └── TaskListener.php           # 任务事件监听器
│   ├── interfaces/                 # 接口定义
│   │   ├── AuditDetailRequest.php      # 审核详情请求接口
│   │   ├── AuditInterface.php          # 审核业务接口
│   │   ├── LeaveInterface.php          # 请假业务接口
│   │   └── WorkflowInterface.php       # 工作流接口
│   ├── library/                    # 第三方类库和通用工具
│   │   ├── ApiClient.php               # API客户端工具
│   │   ├── BCrypt.php                  # 密码加密工具
│   │   ├── Base62.php                  # Base62编码工具
│   │   ├── Excel.php                   # Excel处理工具
│   │   ├── Mail.php                    # 邮件发送服务
│   │   ├── MongodbClient.php           # MongoDB客户端
│   │   ├── RedisEnums.php              # Redis键值枚举
│   │   ├── Tools.php                   # 通用工具集
│   │   ├── ApprovalHandlers/           # 审批处理器
│   │   ├── Exception/                  # 异常处理类
│   │   │   ├── AuthenticationException.php    # 认证异常
│   │   │   ├── AuthorizationException.php     # 授权异常
│   │   │   ├── BusinessException.php          # 业务异常
│   │   │   ├── ParameterException.php         # 参数异常
│   │   │   ├── SystemException.php            # 系统异常
│   │   │   └── ValidationException.php        # 验证异常
│   │   ├── mns_sdk/                    # 阿里云消息服务SDK
│   │   ├── OSS/                        # 阿里云对象存储SDK
│   │   └── phpHMS/                     # 华为推送服务SDK
│   ├── messages/                   # 多语言消息包
│   ├── models/                     # 数据模型层
│   │   ├── BaseModel.php               # 模型基类
│   │   ├── BackyardImg.php             # 后台图片模型
│   │   ├── backyard/                   # backyard数据库模型
│   │   │   ├── AccessTokenModel.php        # 访问令牌模型
│   │   │   ├── ActivityRecordModel.php     # 活动记录模型
│   │   │   └── [395个backyard模型文件]
│   │   ├── bi/                         # 商业智能数据库模型
│   │   │   ├── BiBaseModel.php             # BI模型基类
│   │   │   ├── DeliveryAvgCountModel.php   # 配送平均数量模型
│   │   │   └── [21个bi模型文件]
│   │   ├── coupon/                     # 优惠券数据库模型
│   │   │   ├── CouponBaseModel.php         # 优惠券模型基类
│   │   │   ├── MessageContentModel.php    # 消息内容模型
│   │   │   └── MessageCourierModel.php    # 消息快递员模型
│   │   ├── fle/                        # FLE数据库模型
│   │   │   ├── FleBaseModel.php            # FLE模型基类
│   │   │   ├── DeviceKickedRecordModel.php # 设备踢出记录模型
│   │   │   └── [10个fle模型文件]
│   │   ├── oa/                         # OA办公数据库模型
│   │   │   ├── HcBudgetPerMonthModel.php   # HC月度预算模型
│   │   │   ├── LoanModel.php               # 借款模型
│   │   │   └── [27个oa模型文件]
│   │   └── xxljob/                     # 任务调度数据库模型
│   │   │    ├── XxljobBaseModel.php         # 任务调度模型基类
│   │   │    ├── XxljobInfoModel.php         # 任务信息模型
│   │   │    └── [3个xxljob模型文件]
│   ├── modules/                    # 多国家业务模块
│   │   ├── Id/                         # 印尼业务模块
│   │   │   ├── controllers/                # 印尼控制器 [12个文件]
│   │   │   ├── library/                    # 印尼专用类库
│   │   │   ├── models/                     # 印尼专用模型
│   │   │   ├── Module.php                  # 印尼模块定义
│   │   │   ├── server/                     # 印尼业务逻辑层 [17个文件]
│   │   │   └── tasks/                      # 印尼任务脚本 [4个文件]
│   │   ├── La/                         # 老挝业务模块
│   │   │   ├── controllers/                # 老挝控制器 [17个文件]
│   │   │   ├── library/                    # 老挝专用类库
│   │   │   ├── models/                     # 老挝专用模型
│   │   │   ├── Module.php                  # 老挝模块定义
│   │   │   ├── server/                     # 老挝业务逻辑层 [20个文件]
│   │   │   └── tasks/                      # 老挝任务脚本 [4个文件]
│   │   ├── My/                         # 马来西亚业务模块
│   │   │   ├── controllers/                # 马来西亚控制器 [43个文件]
│   │   │   ├── enums/                      # 马来西亚枚举 [2个文件]
│   │   │   ├── library/                    # 马来西亚专用类库
│   │   │   ├── models/                     # 马来西亚专用模型
│   │   │   ├── Module.php                  # 马来西亚模块定义
│   │   │   ├── repository/                 # 马来西亚数据仓库 [2个文件]
│   │   │   ├── server/                     # 马来西亚业务逻辑层 [59个文件]
│   │   │   └── tasks/                      # 马来西亚任务脚本 [9个文件]
│   │   ├── Ph/                         # 菲律宾业务模块
│   │   │   ├── controllers/                # 菲律宾控制器 [39个文件]
│   │   │   ├── enums/                      # 菲律宾枚举 [2个文件]
│   │   │   ├── library/                    # 菲律宾专用类库
│   │   │   ├── models/                     # 菲律宾专用模型
│   │   │   ├── Module.php                  # 菲律宾模块定义
│   │   │   ├── server/                     # 菲律宾业务逻辑层 [44个文件]
│   │   │   └── tasks/                      # 菲律宾任务脚本 [10个文件]
│   │   ├── Th/                         # 泰国业务模块
│   │   │   ├── controllers/                # 泰国控制器 [32个文件]
│   │   │   ├── language/                   # 泰国语言包
│   │   │   ├── library/                    # 泰国专用类库
│   │   │   ├── models/                     # 泰国专用模型
│   │   │   ├── Module.php                  # 泰国模块定义
│   │   │   ├── server/                     # 泰国业务逻辑层
│   │   │   └── tasks/                      # 泰国任务脚本
│   │   └── Vn/                         # 越南业务模块
│   │   │   ├── controllers/                # 越南控制器
│   │   │   ├── library/                    # 越南专用类库
│   │   │   ├── models/                     # 越南专用模型
│   │   │   ├── Module.php                  # 越南模块定义
│   │   │   ├── server/                     # 越南业务逻辑层
│   │   │   └── tasks/                      # 越南任务脚本
│   ├── plugins/                    # 插件系统
│   │   ├── CORSPlugin.php              # 跨域处理插件
│   │   ├── DispatchPlugin.php          # 请求分发插件
│   │   ├── ExceptionPlugin.php         # 异常处理插件
│   │   └── SecurityPlugin.php          # 安全验证插件
│   ├── repository/                 # 数据仓库层 - 数据访问抽象
│   │   ├── AccessTokenRepository.php   # 访问令牌仓库
│   │   ├── AdministrationLogRepository.php # 管理日志仓库
│   │   └── [93个Repository文件]
│   ├── runtime/                    # 运行时文件目录
│   │   ├── back/                       # 备份文件
│   │   ├── exceptionlog/               # 异常日志
│   │   ├── log/                        # 应用日志
│   │   └── tasklog/                    # 任务日志
│   ├── server/                     # 业务逻辑层 (核心业务处理)
│   │   ├── AbnormalExpenseServer.php   # 异常费用服务
│   │   ├── AccidentServer.php          # 事故处理服务
│   │   ├── ActivityServer.php          # 活动管理服务
│   │   ├── Message/                    # 消息服务模块 [35个文件]
│   │   ├── Osm/                        # OSM系统服务 [6个文件]
│   │   ├── Penalty/                    # 罚款处理服务 [7个文件]
│   │   ├── Rpc/                        # RPC远程调用服务 [15个文件]
│   │   ├── Vacation/                   # 休假管理服务 [6个文件]
│   │   └── [201个Server文件]
│   ├── tasks/                      # 任务脚本目录
│   │   ├── AaTask.php                  # AA任务脚本
│   │   ├── AccidentTask.php            # 事故处理任务
│   │   ├── ActivityTask.php            # 活动任务脚本
│   │   └── [95个Task文件]
│   ├── traits/                     # PHP特性和复用代码
│   │   ├── AuditStateTrait.php         # 审核状态特性
│   │   ├── ComplexFormulaTrait.php     # 复杂公式特性
│   │   ├── ConditionsConfigTrait.php   # 条件配置特性
│   │   └── [5个Trait文件]
│   ├── uconfig/                    # 用户配置目录
│   │   ├── abortList.php               # 中止列表配置
│   │   ├── applyJobTitle.php           # 申请职位配置
│   │   ├── fleetInfo.php               # 车队信息配置
│   │   └── [9个配置文件]
│   ├── util/                       # 工具类目录
│   │   ├── ErrorHandler.php           # 错误处理器
│   │   ├── helpers.php                 # 助手函数
│   │   └── WorkflowFormula.php         # 工作流公式计算
│   └── views/                      # 视图模板目录
│   │   ├── activity/                   # 活动模板 [2个文件]
│   │   ├── backyard/                   # Backyard模板
│   │   ├── base_notop_noleft.phtml     # 基础模板(无顶部无左侧)
│   │   ├── base.phtml                  # 基础模板
│   │   ├── fuelbudget/                 # 燃料预算模板 [4个文件]
│   │   ├── hirechange/                 # 雇佣变更模板 [3个文件]
│   │   ├── index/                      # 首页模板
│   │   ├── job_transfer/               # 转岗模板 [3个文件]
│   │   ├── layouts/                    # 布局模板 [2个文件]
│   │   ├── loginuser/                  # 登录用户模板
│   │   ├── probation/                  # 试用期模板
│   │   ├── resign/                     # 离职模板 [4个文件]
│   │   └── termination_contract/       # 合同终止模板 [3个文件]
├── cache/                          # 缓存目录
├── cloud/                          # 云服务相关
│   ├── api/                        # Go语言API服务
│   │   ├── build.sh                    # 构建脚本
│   │   ├── common/                     # 公共模块
│   │   ├── config/                     # 配置管理
│   │   ├── controllers/                # 控制器 [4个Go文件]
│   │   ├── db/                         # 数据库操作 [2个Go文件]
│   │   ├── go.mod                      # Go模块定义
│   │   ├── library/                    # 类库 [2个Go文件]
│   │   ├── main.go                     # 主程序入口
│   │   ├── messages/                   # 消息处理 [3个Go文件]
│   │   ├── models/                     # 数据模型 [5个Go文件]
│   │   ├── routers/                    # 路由配置
│   │   ├── server/                     # 业务服务 [3个Go文件]
│   │   └── utils/                      # 工具函数
│   └── cli/                        # Go命令行工具
│   ├── build.sh                    # CLI构建脚本
│   ├── conf/                       # CLI配置
│   └── main.go                     # CLI主程序
├── composer.json                   # Composer依赖配置
├── composer.lock                   # Composer版本锁定
├── doc/                            # 项目文档
│   ├── auth2.0.md                      # 认证2.0文档
│   ├── JT_workflow.md                  # JT工作流文档
│   └── OS_workflow.md                  # OS工作流文档
├── docker/                         # Docker容器配置
│   ├── docker-compose.yml.example     # Docker编排示例
│   ├── index.php                       # Docker入口文件
│   └── run.sh                          # Docker运行脚本
├── docs/                           # API文档目录
├── examples/                       # 示例代码目录
├── init.sh                         # 项目初始化脚本
├── keys/                           # 密钥文件目录
├── phpunit.xml                     # PHPUnit测试配置
├── public/                         # Web公共目录
│   ├── index.php                       # 应用入口文件
│   ├── css/                            # 样式文件 [13个CSS文件]
│   ├── fonts/                          # 字体文件 [18个字体文件]
│   ├── images/                         # 图片资源 [5个图片文件]
│   ├── pdf_template/                   # PDF模板 [12个模板文件]
│   ├── privacy/                        # 隐私政策页面 [15个HTML文件]
│   └── [11个Excel、PHP等文件]
├── pull_lang.sh                    # 语言包拉取脚本
├── schemas/                        # 数据库架构和迁移文件
│   ├── feature.lzw.8725_hc.sql        # 功能特性SQL
│   ├── PH/                             # 菲律宾数据库迁移 [5个SQL文件]
│   ├── TH/                             # 泰国数据库迁移 [6个SQL文件]
│   ├── tsql/                           # 历史SQL文件 [20个SQL文件]
│   └── [18个全局SQL文件]
├── tests/                          # 测试目录
│   ├── bootstrap.php                   # 测试引导文件
│   ├── integration/                    # 集成测试
│   ├── README.md                       # 测试说明文档
│   ├── unit/                           # 单元测试
│   │   ├── ExampleTest.php                 # 示例测试
│   │   ├── modules/                        # 模块测试
│   │   ├── server/                         # 服务层测试 [2个测试文件]
│   │   └── service/                        # 服务测试目录
│   └── phpunit.xml                     # 测试配置文件
├── vendor/                         # Composer依赖包
└── .env                            # 环境变量配置文件
│   ├── modules/                    # 多国家业务模块
│   │   ├── ID/                         # 印尼业务模块
│   │   │   ├── controllers/                # 印尼控制器
│   │   │   ├── library/                    # 印尼专用类库
│   │   │   ├── server/                     # 印尼业务逻辑层
│   │   │   └── tasks/                      # 印尼任务脚本
│   │   ├── LA/                         # 老挝业务模块
│   │   │   ├── controllers/
│   │   │   ├── library/
│   │   │   ├── server/
│   │   │   └── tasks/
│   │   ├── MY/                         # 马来西亚业务模块
│   │   │   ├── controllers/
│   │   │   ├── library/
│   │   │   ├── server/
│   │   │   └── tasks/
│   │   ├── PH/                         # 菲律宾业务模块
│   │   │   ├── controllers/                # 菲律宾控制器
│   │   │   ├── library/                    # 菲律宾专用类库
│   │   │   ├── server/                     # 菲律宾业务逻辑层
│   │   │   └── tasks/                      # 菲律宾任务脚本
│   │   ├── TH/                         # 泰国业务模块
│   │   │   ├── controllers/                # 泰国控制器
│   │   │   ├── library/                    # 泰国专用类库
│   │   │   ├── server/                     # 泰国业务逻辑层
│   │   │   └── tasks/                      # 泰国任务脚本
│   │   └── VN/                         # 越南业务模块
│   │       ├── controllers/
│   │       ├── library/
│   │       ├── server/
│   │       └── tasks/
│   ├── plugins/                    # 插件系统
│   │   ├── CORSPlugin.php              # 跨域处理插件
│   │   ├── DispatchPlugin.php          # 分发插件
│   │   ├── ExceptionPlugin.php         # 异常处理插件
│   │   └── SecurityPlugin.php          # 安全插件
│   ├── repository/                 # 数据仓库层 - 数据访问抽象
│   │   ├── BaseRepository.php          # 仓库基类
│   │   ├── *Repository.php             # 各业务数据仓库
│   │   └── ...
│   ├── runtime/                    # 运行时文件
│   │   └── ...                         # 日志、缓存等运行时文件
│   ├── script_go/                  # Go脚本
│   ├── server/                     # 业务逻辑层 (原services)
│   │   ├── BaseServer.php              # 服务基类
│   │   ├── *Server.php                 # 各业务服务类
│   │   ├── Rpc/                        # RPC服务
│   │   └── ...
│   ├── tasks/                      # 任务脚本
│   ├── traits/                     # PHP特性
│   ├── uconfig/                    # 用户配置
│   └── views/                      # 视图模板
├── attachments/                    # 附件存储目录
├── composer.json                   # Composer依赖配置
├── composer.lock                   # Composer锁定文件
├── docker/                         # Docker配置
├── public/                         # Web根目录
│   ├── index.php                       # 应用入口文件
│   └── ...
├── schemas/                        # 数据库架构
├── tests/                          # 测试目录
│   ├── unit/                           # 单元测试
│   │   ├── server/                        # 服务层测试
│   │   │   └── th/                             # 泰国模块测试
│   └── phpunit.xml                     # PHPUnit配置
└── .env                            # 环境配置文件

## 项目架构层次说明

### controllers层（控制器）说明
**职责**: API接口入口，处理HTTP请求和响应

1**规范**:
- 存放目录: `app/controllers/`
- 命名空间: `namespace FlashExpress\bi\App\Controllers`
- 继承: `ControllerBase`
- 命名规则: 驼峰式 + Controller后缀 (如: `AttendanceWhiteListController`)
- 方法命名: 动作名 + Action后缀

**代码示例**:
```php
/**
 * @description: 考勤白名单管理
 * @author: AI
 * @date: 2024-01-15 10:30:00
 */
public function getWhiteListAction()
{
    $params = $this->request->get();
    $params = array_filter($params);
    
    // 参数验证
    $validation = [
        'staff_id' => 'Int|>>>:[staff_id] params error',
        'status' => 'Int|>>>:[status] params error'
    ];
    
    Validation::validate((array)$params, $validation);
    
    $service = new AttendanceService();
    $data = $service->getWhiteList($params);
    
    return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
}
```


### function（函数）说明

**职责**: 提供项目通用的工具函数和辅助方法

**规范**:
- 存放位置: 所有公共函数必须写到 `app/common/functions.php` 文件中。
- 重复检查: 函数要验证是否已经存在，已存在不进行覆盖。
- 注释要求: 需要添加超过40%的注释，包括功能描述、参数说明、返回值说明。
- 防重复: 必须添加 `function_exists` 语句，避免函数重复定义。
- 兼容性: 如遇到相同功能的函数，不修改已经存在的函数，可考虑新增带版本号的函数。
- 命名规范: 函数名使用驼峰命名法，见名知意。
- 参数验证: 对输入参数进行必要的类型和有效性检查。
- 错误处理: 合理处理异常情况，返回有意义的错误信息。

**代码示例**:
```php
/**
 * 批量多数字求和
 * @description: 对多个数字进行高精度求和运算
 * @author: AI
 * @date: 2024-01-15 10:30:00
 * @param array $numbers 需要求和的数字数组
 * @param int $scale 小数点后保留位数，默认2位
 * @return string 返回求和结果（字符串格式保证精度）
 * @example: bcAddBatch([1.23, 2.45, 3.67], 2) => "7.35"
 */
if (!function_exists('bcAddBatch')) {
    function bcAddBatch($numbers, $scale = 2)
    {
        // 参数验证
        if (!is_array($numbers) || empty($numbers)) {
            return '0';
        }
        
        // 单个数字直接返回
        if (count($numbers) === 1) {
            return bcadd('0', $numbers[0] ?? 0, $scale);
        }
        
        // 批量求和
        $return = '0';
        foreach ($numbers as $num) {
            // 确保数字格式正确
            if (is_numeric($num)) {
                $return = bcadd($num, $return, $scale);
            }
        }
        
        return $return;
    }
}
```

## models层（模型）说明

**职责**: 定义数据模型，处理数据库表结构映射和基础数据操作

**规范要求**:
1. **存放位置**: models文件夹存放项目的model类，按数据库分类存放。
2. **命名空间**: 根据数据库类型设置命名空间
    - backyard库: `FlashExpress\bi\App\Models\backyard`
    - 其他库: `FlashExpress\bi\App\Models\{库名}`
3. **命名规则**: 数据库表名首字母大写 + Model，使用驼峰命名法
    - 例如: `hr_staff_info` → `HrStaffInfoModel`
4. **继承关系**: 继承对应的BaseModel类
    - backyard库: 继承 `BackyardBaseModel`
    - 其他库: 继承对应的BaseModel
5. **注释要求**: 需要添加超过30%的注释，包括类说明、属性说明、常量说明
6. **必需属性**: 必须定义 `$table_name` 属性指定表名
7. **常量定义**: 状态、类型等枚举值使用类常量定义，并添加中文注释

**代码示例**:
```php
<?php
/**
 * HR员工信息表模型
 * @description: 处理员工基础信息的数据模型
 * @author: AI
 * @date: 2024-01-15 10:30:00
 * @table: hr_staff_info
 */
namespace FlashExpress\bi\App\Models\backyard;

class HrStaffInfoModel extends BackyardBaseModel
{
    /**
     * 数据表名
     * @var string
     */
    protected $table_name = 'hr_staff_info';
    
    // 员工状态常量定义
    const STATUS_ACTIVE     = 1;  // 在职
    const STATUS_INACTIVE   = 0;  // 离职
    const STATUS_PROBATION  = 2;  // 试用期
    const STATUS_SUSPENDED  = 3;  // 停职
    
    // 性别常量定义
    const GENDER_MALE   = 1;  // 男性
    const GENDER_FEMALE = 2;  // 女性
    const GENDER_OTHER  = 3;  // 其他
    
    /**
     * 获取状态文本
     * @param int $status 状态值
     * @return string 状态文本
     */
    public static function getStatusText($status)
    {
        $statusMap = [
            self::STATUS_ACTIVE    => '在职',
            self::STATUS_INACTIVE  => '离职',
            self::STATUS_PROBATION => '试用期',
            self::STATUS_SUSPENDED => '停职',
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }
}
```

## repository层（仓库）说明

**职责**: 封装数据访问逻辑，处理复杂的数据库查询操作和数据持久化

**规范要求**:
1. **存放位置**: `app/repository` 目录
2. **命名空间**: `FlashExpress\bi\App\Repository`
3. **继承关系**: 继承 `BaseRepository` 类
4. **命名规则**: 驼峰命名 + Repository后缀
    - 例如: `AuditLogRepository`、`HrStaffInfoRepository`
5. **注释要求**: 需要添加超过30%的注释，包括类说明、方法说明、参数说明、返回值说明
6. **方法规范**:
    - 使用静态方法处理数据查询
    - 方法名见名知意，使用动词+名词形式
    - 参数使用类型提示和默认值
    - 返回值使用类型提示
7. **查询优化**:
    - 简单查询优先使用 `findFirst` 和 `find` 方法
    - 复杂查询使用builder模式
    - 避免在Repository中写业务逻辑
8. **返回格式**: 统一返回格式，数组或对象
9. **异常处理**: 合理处理数据库异常，记录错误日志
10. **性能优化**: 合理使用索引，避免N+1查询问题

**代码示例**:
```php
<?php
/**
 * 审批日志数据仓库
 * @description: 处理审批日志相关的数据查询操作
 * @author: AI
 * @date: 2024-01-15 10:30:00
 */
namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Library\BaseRepository;
use FlashExpress\bi\App\Models\backyard\AuditLogModel;
use Phalcon\Mvc\Model\Query\Builder;

class AuditLogRepository extends BaseRepository
{
    /**
     * 获取审批日志列表
     * @param array $flowIds 流程ID数组
     * @param int $auditAction 审批动作 0-全部 1-提交 2-通过 3-拒绝
     * @param array $columns 查询字段
     * @param array $orderBy 排序字段
     * @return array 审批日志列表
     */
    public static function getAuditLogsList(
        array $flowIds = [], 
        int $auditAction = 0, 
        array $columns = ['*'],
        array $orderBy = ['created_at DESC']
    ): array {
        try {
            $conditions = [];
            $bind = [];
            
            // 构建查询条件
            if (!empty($flowIds)) {
                $conditions[] = 'flow_id IN ({flowIds:array})';
                $bind['flowIds'] = $flowIds;
            }
            
            if ($auditAction > 0) {
                $conditions[] = 'audit_action = :auditAction:';
                $bind['auditAction'] = $auditAction;
            }
            
            // 添加软删除条件
            $conditions[] = 'is_deleted = 0';
            
            // 执行查询
            $whereClause = !empty($conditions) ? implode(' AND ', $conditions) : '';
            
            return AuditLogModel::find([
                'conditions' => $whereClause,
                'bind' => $bind,
                'columns' => implode(',', $columns),
                'order' => implode(',', $orderBy)
            ])->toArray();
            
        } catch (\Exception $e) {
            // 记录错误日志
            error_log('AuditLogRepository::getAuditLogsList Error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 根据流程ID获取最新审批记录
     * @param int $flowId 流程ID
     * @return AuditLogModel|null 审批记录
     */
    public static function getLatestAuditLog(int $flowId): ?AuditLogModel
    {
        if ($flowId <= 0) {
            return null;
        }
        
        return AuditLogModel::findFirst([
            'conditions' => 'flow_id = :flowId: AND is_deleted = 0',
            'bind' => ['flowId' => $flowId],
            'order' => 'created_at DESC'
        ]);
    }
    
    /**
     * 复杂查询示例 - 获取审批统计信息
     * @param array $params 查询参数
     * @return array 统计结果
     */
    public static function getAuditStatistics(array $params): array
    {
        try {
            // 使用Builder进行复杂查询
            $builder = new Builder();
            
            $builder->from(['audit' => AuditLogModel::class]);
            $builder->columns([
                'audit.audit_action',
                'COUNT(*) as count',
                'DATE(audit.created_at) as audit_date'
            ]);
            
            // 添加时间范围条件
            if (!empty($params['start_date']) && !empty($params['end_date'])) {
                $builder->betweenWhere(
                    'audit.created_at',
                    $params['start_date'] . ' 00:00:00',
                    $params['end_date'] . ' 23:59:59'
                );
            }
            
            // 添加部门条件
            if (!empty($params['department_id'])) {
                $builder->andWhere(
                    'audit.department_id = :deptId:',
                    ['deptId' => $params['department_id']]
                );
            }
            
            // 软删除条件
            $builder->andWhere('audit.is_deleted = 0');
            
            // 分组和排序
            $builder->groupBy('audit.audit_action, DATE(audit.created_at)');
            $builder->orderBy('audit_date DESC, audit.audit_action ASC');
            
            return $builder->getQuery()->execute()->toArray();
            
        } catch (\Exception $e) {
            error_log('AuditLogRepository::getAuditStatistics Error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 批量插入审批日志
     * @param array $logsData 日志数据数组
     * @return bool 是否成功
     */
    public static function batchInsertAuditLogs(array $logsData): bool
    {
        if (empty($logsData)) {
            return false;
        }
        
        try {
            $connection = AuditLogModel::getWriteConnection();
            $connection->begin();
            
            foreach ($logsData as $logData) {
                $auditLog = new AuditLogModel();
                $auditLog->assign($logData);
                
                if (!$auditLog->save()) {
                    $connection->rollback();
                    return false;
                }
            }
            
            $connection->commit();
            return true;
            
        } catch (\Exception $e) {
            if (isset($connection)) {
                $connection->rollback();
            }
            error_log('AuditLogRepository::batchInsertAuditLogs Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 根据条件统计记录数
     * @param array $conditions 查询条件
     * @return int 记录数
     */
    public static function countByConditions(array $conditions): int
    {
        try {
            $whereClause = [];
            $bind = [];
            
            foreach ($conditions as $field => $value) {
                if ($value !== null && $value !== '') {
                    $whereClause[] = "{$field} = :{$field}:";
                    $bind[$field] = $value;
                }
            }
            
            // 添加软删除条件
            $whereClause[] = 'is_deleted = 0';
            
            return AuditLogModel::count([
                'conditions' => implode(' AND ', $whereClause),
                'bind' => $bind
            ]);
            
        } catch (\Exception $e) {
            error_log('AuditLogRepository::countByConditions Error: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 软删除审批日志
     * @param int $logId 日志ID
     * @param int $operatorId 操作人ID
     * @return bool 是否成功
     */
    public static function softDeleteAuditLog(int $logId, int $operatorId): bool
    {
        try {
            $auditLog = AuditLogModel::findFirst($logId);
            if (!$auditLog) {
                return false;
            }
            
            $auditLog->is_deleted = 1;
            $auditLog->deleted_by = $operatorId;
            $auditLog->deleted_at = date('Y-m-d H:i:s');
            
            return $auditLog->save();
            
        } catch (\Exception $e) {
            error_log('AuditLogRepository::softDeleteAuditLog Error: ' . $e->getMessage());
            return false;
        }
    }
}
```

## server层（业务）说明

**职责**: server层是业务逻辑的核心处理层，负责封装复杂的业务规则、协调多个Repository和Model的交互，为Controller层提供高级业务接口。该层应包含所有业务逻辑处理、数据验证、业务规则校验和跨模块的数据操作。

**存放目录**: `app/server`

**命名规范**:
1. **命名空间**: `namespace FlashExpress\bi\App\Server`
2. **继承**: 必须继承 `BaseServer` 类
3. **文件命名**: 采用驼峰形式，并以 `Server` 结尾，例如：`BlackListServer.php`、`ApprovalWorkflowServer.php`
4. **类命名**: 与文件名保持一致，例如：`BlackListServer`、`ApprovalWorkflowServer`

**规范要求**:
1. **业务逻辑封装**: 所有复杂的业务逻辑都应在Service层处理，Controller层只负责参数接收和响应返回
2. **数据验证**: Service层应进行业务级别的数据验证，包括业务规则校验、权限检查等
3. **事务管理**: 涉及多个数据操作的业务流程应在Service层进行事务管理
4. **异常处理**: Service层应捕获并处理业务异常，转换为有意义的业务错误信息
5. **注释覆盖率**: 需要添加超过40%的注释，包括类注释、方法注释和关键业务逻辑注释
6. **单一职责**: 每个Service类应专注于一个业务领域，避免职责过于宽泛
7. **依赖注入**: 优先使用依赖注入方式获取其他Service或Repository实例

**查询规范**:
1. **简单查询**: 表查询优先使用 `findFirst` 和 `find` 方法
2. **复杂查询**: 需要连表查询时，使用 `modelsManager` 的 `builder` 方式
3. **查询优化**: 避免N+1查询问题，合理使用预加载和批量查询

**异常处理**
1. 多个表插入或者更新操作启用try catch,否则不编写try catch
2. catch中记录日志
3. catch中抛出异常

**代码示例**:

```php
<?php
/**
 * Author: AI
 * Date  : 2024-01-15 10:00
 * Description: 审批业务服务类
 */
namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Library\Server;
use FlashExpress\bi\App\Repository\ApprovalRepository;
use FlashExpress\bi\App\Repository\AuditLogRepository;
use FlashExpress\bi\App\Models\ApprovalModel;
use FlashExpress\bi\App\Models\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\HrEntryModel;
use FlashExpress\bi\App\Models\HcModel;
use FlashExpress\bi\App\Models\HrResumeModel;

class ApprovalServer extends BaseServer
{
    /**
     * 获取审核信息
     * @param int $staffId 员工ID
     * @param array $options 查询选项
     * @return array 审核信息
     */
    public function getApprovalInfo(int $staffId, array $options = []): array
    {
        // 参数验证
        if ($staffId <= 0) {
            throw new \InvalidArgumentException('员工ID不能为空');
        }
        
        // 设置默认状态
        $defaultState = self::APPROVE_STATE_NOTUPLOAD;
        
        try {
            // 简单查询使用findFirst
            $approval = ApprovalModel::findFirst([
                'conditions' => 'staff_id = :staffId: AND is_deleted = 0',
                'bind' => ['staffId' => $staffId]
            ]);
            
            if (!$approval) {
                return [
                    'status' => $defaultState,
                    'message' => '未找到审核信息'
                ];
            }
            
            // 获取审核日志
            $auditLogs = AuditLogRepository::getAuditLogsByStaffId($staffId);
            
            return [
                'approval_info' => $approval->toArray(),
                'audit_logs' => $auditLogs,
                'status' => $approval->status
            ];
            
        } catch (\Exception $e) {
            // 记录错误日志
            error_log('ApprovalService::getApprovalInfo Error: ' . $e->getMessage());
            throw new \RuntimeException('获取审核信息失败');
        }
    }
    
    /**
     * 获取复杂的Offer统计数据（使用Builder方式）
     * @param array $params 查询参数
     * @return array 统计结果
     */
    public function getOfferStatistics(array $params): array
    {
        try {
            // 生成数据库model对象
            $builder = $this->modelsManager->createBuilder();
            
            // 组装参数
            $builder->from(['offer' => HrInterviewOfferModel::class]);
            $builder->columns('count(1) as total');
            $builder->innerJoin(HrEntryModel::class, 'offer.id=entry.interview_offer_id', 'entry');
            $builder->leftJoin(HcModel::class, 'offer.hc_id=hc.hc_id', 'hc');
            $builder->leftJoin(HrResumeModel::class, 'offer.resume_id=resume.id', 'resume');
            
            // 添加where条件
            $builder = $this->formatWhere($builder, $params);
            
            // 数据权限控制
            $authoritySql = $this->getAuthorityCondition($params);
            if ($authoritySql) {
                $builder->andWhere($authoritySql);
            }
            
            // 设置排序
            $builder->orderBy('offer.id desc');
            
            // 获取总数
            $totalInfo = $builder->getQuery()->getSingleResult();
            $count = $totalInfo->total;
            
            // 重新设置查询字段
            $columns = [
                'offer.resume_id as resume_id',
                'offer.hc_id as hc_id',
                'offer.salary as salary',
                'hc.hc_name as hc_name',
                'resume.name as candidate_name'
            ];
            
            // 分页处理
            $pageSize = $params['page_size'] ?? 20;
            $page = $params['page'] ?? 1;
            $pageOffset = ($page - 1) * $pageSize;
            
            $builder->limit($pageSize, $pageOffset);
            $builder->columns($columns);
            
            // 获取结果
            $items = $builder->getQuery()->execute()->toArray();
            
            return [
                'total' => $count,
                'items' => $items,
                'page' => $page,
                'page_size' => $pageSize
            ];
            
        } catch (\Exception $e) {
            error_log('ApprovalService::getOfferStatistics Error: ' . $e->getMessage());
            throw new \RuntimeException('获取Offer统计数据失败');
        }
    }
    
    /**
     * 批量审批处理
     * @param array $approvalIds 审批ID数组
     * @param int $action 审批动作
     * @param int $operatorId 操作人ID
     * @param string $remark 备注
     * @return bool 处理结果
     */
    public function batchApproval(array $approvalIds, int $action, int $operatorId, string $remark = ''): bool
    {
        // 参数验证
        if (empty($approvalIds) || $action <= 0 || $operatorId <= 0) {
            throw new \InvalidArgumentException('参数不完整');
        }
        
        // 开启事务
        $connection = $this->db->getWriteConnection();
        $connection->begin();
        
        try {
            foreach ($approvalIds as $approvalId) {
                // 更新审批状态
                $result = ApprovalRepository::updateApprovalStatus(
                    $approvalId, 
                    $action, 
                    $operatorId, 
                    $remark
                );
                
                if (!$result) {
                    throw new \RuntimeException("审批ID {$approvalId} 处理失败");
                }
                
                // 记录审批日志
                AuditLogRepository::createAuditLog([
                    'approval_id' => $approvalId,
                    'action' => $action,
                    'operator_id' => $operatorId,
                    'remark' => $remark,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
            
            $connection->commit();
            return true;
            
        } catch (\Exception $e) {
            $connection->rollback();
            error_log('ApprovalService::batchApproval Error: ' . $e->getMessage());
            throw new \RuntimeException('批量审批处理失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 格式化查询条件
     * @param \Phalcon\Mvc\Model\Query\Builder $builder 查询构建器
     * @param array $params 参数
     * @return \Phalcon\Mvc\Model\Query\Builder
     */
    private function formatWhere($builder, array $params)
    {
        // 时间范围条件
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $builder->betweenWhere(
                'offer.created_at',
                $params['start_date'] . ' 00:00:00',
                $params['end_date'] . ' 23:59:59'
            );
        }
        
        // 状态条件
        if (isset($params['status']) && $params['status'] !== '') {
            $builder->andWhere('offer.status = :status:', ['status' => $params['status']]);
        }
        
        // HC条件
        if (!empty($params['hc_id'])) {
            $builder->andWhere('offer.hc_id = :hcId:', ['hcId' => $params['hc_id']]);
        }
        
        // 软删除条件
        $builder->andWhere('offer.is_deleted = 0');
        
        return $builder;
    }
    
    /**
     * 获取数据权限条件
     * @param array $params 参数
     * @return string|null 权限SQL条件
     */
    private function getAuthorityCondition(array $params): ?string
    {
        // 根据用户权限生成SQL条件
        $userId = $params['user_id'] ?? 0;
        if ($userId <= 0) {
            return null;
        }
        
        // 这里可以根据实际权限系统生成相应的SQL条件
        // 例如：部门权限、区域权限等
        return "offer.created_by = {$userId} OR offer.department_id IN (SELECT department_id FROM user_departments WHERE user_id = {$userId})";
    }
}
```

## modules层（多国家业务模块）说明

**职责**: Modules层是多国家业务的模块化架构层，负责按国家/地区划分业务逻辑，实现不同国家的本地化需求和特定业务规则。每个模块都是一个独立的业务单元，包含完整的MVC架构和业务逻辑处理能力。

**存放目录**: `app/modules`

**模块结构**: 按国家代码组织，目前支持的国家模块：
- `ID/` - 印尼（Indonesia）业务模块
- `LA/` - 老挝（Laos）业务模块
- `MY/` - 马来西亚（Malaysia）业务模块
- `PH/` - 菲律宾（Philippines）业务模块
- `TH/` - 泰国（Thailand）业务模块
- `VN/` - 越南（Vietnam）业务模块

**命名规范**:
1. **模块目录**: 使用国家代码大写，例如：`MY`、`PH`、`TH`
2. **命名空间**: `namespace FlashExpress\bi\App\Modules\{CountryCode}\{Layer}`
3. **类命名**: 遵循各层的命名规范，在模块内保持一致性
4. **文件组织**: 每个模块内部采用标准的MVC+Service架构

**模块内部结构**:
{CountryCode}/
├── Module.php                    # 模块定义和自动加载配置
├── config/                       # 模块专用配置
│   └── config.php                    # 模块配置文件
├── controllers/                  # 模块控制器层
│   ├── ControllerBase.php            # 模块控制器基类
│   ├── API/                          # API子目录（可选）
│   └── *Controller.php               # 各业务控制器
├── library/                      # 模块专用类库
│   ├── enums.php                     # 模块枚举定义
│   ├── enumsContract.php             # 合同相关枚举
│   └── *Filter.php                   # 过滤器类
├── messages/                     # 多语言消息文件
│   ├── en.php                        # 英文语言包
│   ├── th.php                        # 泰语语言包
│   └── zh-CN.php                     # 中文语言包
├── models/                       # 模块专用模型（通常为空，使用全局模型）
├── server/                       # 模块业务逻辑层
│   ├── BaseServer.php                # 模块服务基类
│   └── *Server.php                   # 各业务服务类
└── tasks/                        # 模块任务脚本
└── *Task.php                     # 定时任务和批处理脚本


**规范要求**:

### 1. 模块定义规范
- 每个模块必须包含 `Module.php` 文件，实现 `ModuleDefinitionInterface` 接口
- 必须正确配置命名空间自动加载
- 支持模块级别的配置覆盖

### 2. 控制器规范
- 模块控制器必须继承模块内的 `ControllerBase`
- 模块 `ControllerBase` 必须继承全局 `ControllerBase`
- 实现国家特定的业务逻辑和验证规则
- 处理本地化的数据格式和验证

### 3. 业务逻辑规范
- 模块内的 `Server` 类处理国家特定的业务逻辑
- 必须继承模块内的 `BaseServer`
- 实现本地化的业务规则和流程
- 处理国家特定的第三方集成

### 4. 本地化规范
- 支持多语言消息文件
- 实现国家特定的数据格式验证
- 处理本地化的日期、货币、地址格式
- 支持国家特定的业务流程

### 5. 配置管理
- 模块配置可以覆盖全局配置
- 支持国家特定的参数设置
- 环境变量和敏感信息的安全管理


# 单元测试规范说明

## 测试文件存储位置规范

### 1. 全局层级测试文件位置
- **server层测试**: `app/server/` 下的类测试文件要放到 `tests/unit/server/` 目录下
- **repository层测试**: `app/repository/` 下的类测试文件要放到 `tests/unit/repository/` 目录下
- **controller层测试**: `app/controllers/` 下的类测试文件要放到 `tests/unit/controllers/` 目录下
- **helper层测试**: `app/helper/` 下的类测试文件要放到 `tests/unit/helper/` 目录下
- **library层测试**: `app/library/` 下的类测试文件要放到 `tests/unit/library/` 目录下

### 2. 模块层级测试文件位置
- **泰国模块**: `app/modules/TH/server/` 下的类单元测试放到 `tests/unit/modules/th/server/` 下
- **菲律宾模块**: `app/modules/PH/server/` 下的类单元测试放到 `tests/unit/modules/ph/server/` 下
- **越南模块**: `app/modules/VN/server/` 下的类单元测试放到 `tests/unit/modules/vn/server/` 下
- **老挝模块**: `app/modules/LA/server/` 下的类单元测试放到 `tests/unit/modules/la/server/` 下
- **印尼模块**: `app/modules/ID/server/` 下的类单元测试放到 `tests/unit/modules/id/server/` 下
- **马来西亚模块**: `app/modules/MY/server/` 下的类单元测试放到 `tests/unit/modules/my/server/` 下

## 命名空间和继承规范

### 1. 命名空间规范
- **全局测试类**: 使用 `tests\unit\{layer}` 为命名空间前缀，全部小写
    - 例如: `tests\unit\server`、`tests\unit\repository`
- **模块测试类**: 使用 `tests\unit\modules\{country}\{layer}` 为命名空间前缀
    - 例如: `tests\unit\modules\th\server`

### 2. 类命名规范
- 单元测试类名称和测试的类同名，后面加上 `Test` 后缀
- 例如: `HrStaffServer` 的测试类为 `HrStaffServerTest`
- 例如: `ApprovalRepository` 的测试类为 `ApprovalRepositoryTest`

### 3. 继承关系
- 所有单元测试类必须继承 `UnitTestCase` 类
- 同级目录下的类不需要额外引入

## 测试开发规范

### 1. 数据准备规范
- 遇到需要提供测试数据时，连接MCP模块的mysql服务
- 数据库名称为 `backyard`
- 使用真实数据进行测试，确保测试的有效性
- 测试数据应覆盖正常、边界和异常情况

### 2. 业务理解规范
- 遇到需要了解业务逻辑时，连接MCP模块的dify服务进行查询
- 确保测试用例覆盖核心业务场景
- 理解业务规则后编写相应的断言

### 3. 复杂方法处理规范
- 遇到代码超过1000行的方法且业务复杂时，连接MCP模块sequential-thinking服务进行拆解
- 将复杂测试场景分解为多个简单的测试用例
- 每个测试用例专注于测试一个特定功能点

### 4. 代码修改限制
- 所有的更改必须在 `tests/unit/` 目录及其子目录下进行
- 不可以改动其他文件下的代码
- 保持测试代码与业务代码的分离

### 5. 注释规范
- 单元测试逻辑代码注释需大于40%
- 每个测试方法必须包含详细的注释说明
- 注释应包括：测试目的、测试场景、预期结果
- 复杂的测试逻辑需要逐步注释

## 编码规则

### 1. 时间处理规范
- 所有时间使用UTC时区
- 测试中涉及时间比较时，统一使用UTC时间
- 时间格式统一使用 `Y-m-d H:i:s` 格式

### 2. 文件处理规范
- 所有文件上传测试适应分布式部署
- 使用OSS存储进行文件相关测试
- 测试文件路径使用相对路径

### 3. 异常处理规范
- 所有try代码块要分别验证业务错误和系统错误
- 测试异常场景时，使用 `expectException` 方法
- 验证异常消息的准确性

### 4. 数据库操作规范
- 遇到SQL操作，要测试是否正确使用索引
- 使用 `EXPLAIN` 语句验证查询性能
- 测试数据库操作的事务一致性

### 5. 并发测试规范
- 测试接口要检查是否正确加原子锁
- 模拟并发场景进行压力测试
- 验证数据一致性和完整性

### 6. SQL安全规范
- SQL不能拼接，必须使用参数绑定
- 测试SQL注入防护机制
- 使用 `insertAsDict`、`updateAsDict`、`delete` 方法，不允许 `execute`
- 参考Phalcon数据库抽象层最佳实践

### 7. 查询优化规范
- SQL中使用 `IN` 进行范围查询时，检查元素数量控制在500以内
- 测试大数据量查询的性能表现
- 验证分页查询的正确性

### 8. 代码标准规范
- 严格遵守PSR-4自动加载规范
- 代码风格遵循PSR-12编码标准
- 使用静态代码分析工具检查代码质量

## 基本代码规范

### 1. 命名规范
- **类属性命名**: 小写开头的驼峰式 (`$camelCase`)
- **类方法命名**: 小写开头的驼峰式 (`getUserInfo`)
- **方法参数命名**: 使用下划线分隔式 (`$user_id`)
- **测试方法命名**: `test` + 功能描述，使用驼峰式 (`testGetUserInfo`)

### 2. 类管理规范
- 遇到相同名称的类，在类内部更新方法，不覆盖源文件
- 使用版本控制管理测试代码变更
- 保持测试类的单一职责原则

### 3. 测试方法规范
- 每个测试方法只测试一个功能点
- 测试方法名要清晰表达测试意图
- 使用 `setUp()` 和 `tearDown()` 方法管理测试环境

## 测试类型和策略

### 1. 单元测试
- 测试单个方法或函数的功能
- 使用Mock对象隔离外部依赖
- 覆盖正常流程、边界条件和异常情况

### 2. 集成测试
- 测试多个组件之间的交互
- 验证数据流的正确性
- 测试第三方服务集成

### 3. 功能测试
- 测试完整的业务流程
- 验证用户场景的正确性
- 端到端的功能验证

### 4. 性能测试
- 测试关键方法的执行时间
- 验证内存使用情况
- 数据库查询性能测试

## 测试工具和框架

### 1. PHPUnit配置
- 使用项目根目录下的 `phpunit.xml` 配置文件
- 配置测试覆盖率报告
- 设置测试数据库连接

### 2. Mock和Stub
- 使用PHPUnit的Mock功能模拟外部依赖
- 创建测试替身隔离测试环境
- 验证方法调用次数和参数

### 3. 数据提供者
- 使用 `@dataProvider` 注解提供测试数据
- 创建可重用的测试数据集
- 支持参数化测试

## 代码示例

### 1. 基础测试类示例
```php
<?php
/**
 * 员工服务测试类
 * @description: 测试员工相关业务逻辑
 * @author: AI
 * @date: 2024-01-15 10:30:00
 */
namespace tests\unit\server;

use FlashExpress\bi\App\Server\HrStaffServer;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

class HrStaffServerTest extends UnitTestCase
{
    /**
     * 测试前置设置
     * 初始化测试环境和依赖
     */
    public function setUp(): void
    {
        parent::setUp();
        // 初始化测试数据
        $this->initTestData();
    }
    
    /**
     * 测试获取员工信息 - 正常情况
     * @description: 验证根据员工ID获取员工信息的功能
     * @return void
     */
    public function testGetHrStaffInfoSuccess()
    {
        // 准备测试数据
        $staffId = 17245;
        
        // 执行测试方法
        $staffServer = new HrStaffServer();
        $staffInfo = $staffServer->getHrStaffInfo($staffId);
        
        // 验证返回结果
        $this->assertIsArray($staffInfo, '返回结果应该是数组');
        $this->assertArrayHasKey('staff_info_id', $staffInfo, '应该包含员工ID字段');
        $this->assertEquals($staffId, $staffInfo['staff_info_id'], '员工ID应该匹配');
        $this->assertArrayHasKey('staff_name', $staffInfo, '应该包含员工姓名字段');
        $this->assertNotEmpty($staffInfo['staff_name'], '员工姓名不应该为空');
    }
    
    /**
     * 测试获取员工信息 - 员工不存在
     * @description: 验证查询不存在员工时的处理
     * @return void
     */
    public function testGetHrStaffInfoNotFound()
    {
        // 准备测试数据 - 使用不存在的员工ID
        $nonExistentStaffId = 999999;
        
        // 执行测试方法
        $staffServer = new HrStaffServer();
        $staffInfo = $staffServer->getHrStaffInfo($nonExistentStaffId);
        
        // 验证返回结果
        $this->assertEmpty($staffInfo, '不存在的员工应该返回空结果');
    }
    
    /**
     * 测试获取员工信息 - 参数异常
     * @description: 验证传入无效参数时的异常处理
     * @return void
     */
    public function testGetHrStaffInfoInvalidParameter()
    {
        // 测试无效的员工ID
        $invalidStaffIds = [0, -1, null, '', 'invalid'];
        
        $staffServer = new HrStaffServer();
        
        foreach ($invalidStaffIds as $invalidId) {
            // 验证异常抛出
            $this->expectException(\InvalidArgumentException::class);
            $staffServer->getHrStaffInfo($invalidId);
        }
    }
    
    /**
     * 测试数据提供者示例
     * @return array 测试数据集
     */
    public function staffDataProvider(): array
    {
        return [
            '正常员工' => [17245, true],
            '另一个员工' => [22000, true],
            '不存在员工' => [999999, false],
        ];
    }
    
    /**
     * 使用数据提供者的测试方法
     * @dataProvider staffDataProvider
     * @param int $staffId 员工ID
     * @param bool $shouldExist 是否应该存在
     */
    public function testGetHrStaffInfoWithDataProvider(int $staffId, bool $shouldExist)
    {
        $staffServer = new HrStaffServer();
        $staffInfo = $staffServer->getHrStaffInfo($staffId);
        
        if ($shouldExist) {
            $this->assertNotEmpty($staffInfo, "员工ID {$staffId} 应该存在");
            $this->assertArrayHasKey('staff_info_id', $staffInfo);
        } else {
            $this->assertEmpty($staffInfo, "员工ID {$staffId} 不应该存在");
        }
    }
    
    /**
     * 初始化测试数据
     * @return void
     */
    private function initTestData(): void
    {
        // 清理测试数据
        $this->cleanTestData();
        
        // 创建测试数据
        // 这里可以插入必要的测试数据
    }
    
    /**
     * 清理测试数据
     * @return void
     */
    private function cleanTestData(): void
    {
        // 清理可能影响测试的数据
        // 确保测试环境的干净
    }
    
    /**
     * 测试后置清理
     * 清理测试环境
     */
    public function tearDown(): void
    {
        // 清理测试数据
        $this->cleanTestData();
        parent::tearDown();
    }
}
```