<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditCCModel;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\PublicRepository;

class ReminderServer extends BaseServer
{
    private static $instance = null;

    //获取实例
    public static function getInstance($lang, $timezone): ReminderServer
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self($lang, $timezone);
        }
        return self::$instance;
    }

    /**
     * @description 发送抄送【在列表中追加数据，并发送Push提醒】
     * @param AuditApplyModel $request
     * @param $ccStaffIdList
     * @return bool
     */
    public function sendCc(AuditApplyModel $request, $ccStaffIdList): bool
    {
        if (empty($request) || empty($ccStaffIdList)) {
            return false;
        }

        //循环发送抄送 和 push
        foreach ($ccStaffIdList as $toStaffId) {
            $auditCcModel               = new AuditCCModel();
            $auditCcModel->biz_type     = $request->getBizType();
            $auditCcModel->biz_value    = $request->getBizValue();
            $auditCcModel->flow_node_id = $request->getCurrentFlowNodeId();
            $auditCcModel->submitter_id = $request->getSubmitterId();
            $auditCcModel->cc_staff_id  = $toStaffId;
            $auditCcModel->state        = $request->getState(); //1 是待审批 2 是通过
            $auditCcModel->summary      = $request->getSummary();
            $auditCcModel->save();
        }
        //发送 push
        $this->doSendCcPush($ccStaffIdList);

        return true;
    }

    /**
     * @description 发送pussh提醒
     * @param $ccList
     * @return bool
     */
    public function doSendCcPush($ccList): bool
    {
        if (empty($ccList)) {
            return false;
        }
        //push 您有新的用人审批抄送，请查看！
        $HcRepository     = new HcRepository($this->timeZone);
        $PublicRepository = new PublicRepository();

        //获取cc人是什么语言
        $staffAccount = $HcRepository->getStaffAcceptLang($ccList);
        $langArr      = array_column($staffAccount, 'accept_language', 'staff_info_id');

        $this->logger->write_log('send_cc_push send CC message:' . json_encode($langArr), 'info');
        foreach ($ccList as $v) {
            $lang      = $langArr[$v] ?? 'th';
            $t         = $this->getTranslation($lang);
            $pushParam = [
                'staff_info_id'   => $v,
                'message_title'   => $t->_('6006'),
                'message_content' => $t->_('workflow_cc_message'),
            ];
            $PublicRepository->pushMessageAndJumpToCC($pushParam);
        }
        return true;
    }

    /**
     * @description
     * @param array $params
     * @return array
     */
    public function sendCcAndMessage(array $params)
    {
        $staffInfoId  = $params['staff_info_id'];
        $remindTitle  = $params['remind_title'];
        $remindContent= $params['remind_content'];
        $auditType    = $params['audit_type'];
        $serialNo     = $params['serial_no'];
        $category     = $params['message_category'];

        $this->logger->write_log('remindReceiveMaterial params:' . json_encode($params), 'info');

        //抄送提醒
        $info = SystemExternalApprovalModel::getOneBySerialNo($serialNo);
        if (empty($info)) {
            throw new ValidationException('not valid serial no.');
        }

        $repository = new ApplyRepository();
        $applyObj = $repository->getApplyObject($auditType, $info->id);
        if (empty($applyObj)) {
            return $this->checkReturn(['data' => false]);
        }
        $this->sendCc($applyObj, [$staffInfoId]);

        //追加参数
        if (is_numeric($remindContent)) {
            $remindContent = sprintf('%d,%d', $remindContent, $info->id);
        }

        //收货提醒
        //{申请人}发起了耗材申请，单号{申请单号}，需要你收货，请注意查收！
        $param['staff_users']        = [$staffInfoId];
        $param['message_title']      = $remindTitle;
        $param['message_content']    = $remindContent;
        $param['staff_info_ids_str'] = $staffInfoId;
        $param['id']                 = time() . $staffInfoId . rand(1000000, 9999999);
        $param['category']           = $category ?? MessageEnums::MESSAGE_CATEGORY_SYS;

        //发送消息
        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($param);
        $res = $bi_rpc->execute();

        $this->logger->write_log(sprintf('remindReceiveMaterial params: %s, result: %s', json_encode($params), json_encode($res)), 'info');

        if ($res && $res['result']['code'] == 1) {
            $this->logger->write_log(sprintf('remindReceiveMaterial to %s success!', $staffInfoId), 'info');
        } else {
            $this->logger->write_log(sprintf('remindReceiveMaterial to %s failure!', $staffInfoId), 'info');
        }
        return $this->checkReturn(['data' => true]);
    }
}