<?php
/**
 * Created by PhpStor<PERSON>.
 * User: nick
 * Date: 2019/11/20
 * Time: 下午2:51
 */

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\ActivityEnums;
use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Enums\InventoryCheckEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\PushEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\MobileHelper;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\CeoMailFollowModel;
use FlashExpress\bi\App\Models\backyard\HrAgreementSignRecordModel;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\MessagePdfModel;
use FlashExpress\bi\App\Models\backyard\MessageQaAnswerLogModel;
use FlashExpress\bi\App\Models\backyard\MessageSendMnsLogModel;
use FlashExpress\bi\App\Models\backyard\MsgAssetModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\StaffHikvisionModel;
use FlashExpress\bi\App\Models\backyard\StaffSalaryTaxLogModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Models\MailToCeo;
use FlashExpress\bi\App\Models\oa\MaterialWmsPlantaskModel;
use FlashExpress\bi\App\Modules\My\Server\HrStaffTp3Server;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\SickServer;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\JobTitleRepository;
use FlashExpress\bi\App\Repository\MessageCenterRepository;
use FlashExpress\bi\App\Repository\ResumeRecommendRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\library\Image;
use FlashExpress\bi\App\Server\AdministrationOrderServer;
use Exception;
use FlashExpress\bi\App\Models\oa\MaterialWmsApplyModel;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\Message\BaseMessageServer;
use FlashExpress\bi\App\Server\Message\MessageDataServer;
use FlashExpress\bi\App\Server\Message\MessageFactoryServer;

class BackyardServer extends BaseServer{

    public $timezone;

    // 缓存key前缀：下班打卡时仅展示一次的问卷消息: 缓存时间到 当天23:59:59 失效
    public static $cache_temporary_show_undone_questionnaire_msg_key_prefix = 'BY_MSG_UNDONE_QUESTIONNAIRE_';

    // 打卡调用获取新消息接口次数
    public static $cache_off_work_call_count_key_prefix = 'BY_OFF_WORK_CALL_GET_NEW_MSG_COUNT_';

    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
    }


    //1 发生事故 有受伤者 2 关于汽车/叉车 3 高处坠落 4 被触电 5 被撞击，被夹，被拉 6 遭受都刺激性化学品 7 其他
    public static $emergency_type = array(
        1 => 'emergency_type_1',
        2 => 'emergency_type_2',
        3 => 'emergency_type_3',
        4 => 'emergency_type_4',
        5 => 'emergency_type_5',
        6 => 'emergency_type_6',
        7 => 'emergency_type_7',
    );

    //1 无受伤者 2 有受伤者 3 送医院 4 未送医院 5 请假 6 未请假
    public static $personal_type = array(
        1 => 'personal_type_1',
        2 => 'personal_type_2',
        3 => 'personal_type_3',
        4 => 'personal_type_4',
        5 => 'personal_type_5',
        6 => 'personal_type_6',
    );



    //ceo 信箱 列表页
    public function get_mail_list($param){
        $staff_id = $param['staff_id'];
        $sql = "select wte.id as mail_id
                  ,wte.content as ask_content
                  ,wte.type_v2 as ask_type 
                  ,wte.is_read 
                  ,wte.is_reply  
                  ,wte.create_time as ask_time -- 提问时间
                from mail_to_ceo   as wte 
                  where wte.staff_id = {$staff_id} 
                  order by  wte.id desc
                   ";
        $data = $this->getDI()->get('db_rby')->query($sql);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);


        //问题类型:1=建议；2=工资问题；3=提成问题；4=投诉&举报；5=其他
        //$type_arr=[1=>'建议',2=>'工资问题',3=>'提成问题',4=>'投诉&举报',5=>'其他'];
        $type_arr=[
            1=>$this->getTranslation()->_('advice'),
            2=>$this->getTranslation()->_('salary'),
            3=>$this->getTranslation()->_('incentive'),
            4=>$this->getTranslation()->_('complaint'),
            5=>$this->getTranslation()->_('other'),
        ];

        //$read_arr=['未读','已读'];
        $read_arr=[
            $this->getTranslation()->_('un_read'),
            $this->getTranslation()->_('has_read'),
        ];
        //$reply_arr=['未回复','已回复'];
        $reply_arr=[
            $this->getTranslation()->_('wait_reply'),
            $this->getTranslation()->_('already_reply'),
        ];
        if (!empty($data)){
            foreach ($data as $k => $v){
                $data[$k]['reply_text'] = in_array($v['is_reply'],array_keys($reply_arr)) ? $reply_arr[$v['is_reply']] : '';
                $data[$k]['read_text'] = in_array($v['is_read'],array_keys($read_arr)) ? $read_arr[$v['is_read']]:'';
                $data[$k]['ask_type_text'] = in_array($v['ask_type'],array_keys($type_arr)) ? $type_arr[$v['ask_type']] : 'type 为空';
            }
        }
        $d['data']['total_num'] = count($data);
        $d['data']['data'] = $data;
        return $this->checkReturn($d);
    }


    //ceo 信箱 详情
    public function get_mail_detail($param){

        $mail_id = intval($param['mail_id']);
        $di = $this->getDI();
        $sql = "select wte.id as mail_id
                  ,wte.content as ask_content
                  ,wte.type_v2 as ask_type 
                 -- ,if(wte.is_reply=0,'未回复','已回复') as is_reply 
                  ,wte.is_reply  
                  ,wte.is_read  
                  ,wte.img_url  
                  ,wte.file_url  
                  ,wte.create_time as ask_time -- 提问时间
                 ,mrfc.content as answer -- 回答内容
                 ,mrfc.create_time as answer_time  -- 回答时间
                 ,mrfc.img_url reply_img_url
                 ,mrfc.file_url reply_file_url
                from mail_to_ceo   as wte 
                left join mail_reply_from_ceo as mrfc on wte.id=mrfc.mail_id 
                where wte.id = {$mail_id} 
                 ; ";

        $data = $this->getDI()->get('db_rby')->query($sql);
        $data = $data->fetch(\Phalcon\Db::FETCH_ASSOC);


        //$type_arr=[1=>'建议',2=>'工资问题',3=>'提成问题',4=>'投诉&举报',5=>'其他'];
        $type_arr=[
            1=>$this->getTranslation()->_('advice'),
            2=>$this->getTranslation()->_('salary'),
            3=>$this->getTranslation()->_('incentive'),
            4=>$this->getTranslation()->_('complaint'),
            5=>$this->getTranslation()->_('other'),
        ];
        //$read_arr=['未读','已读'];
        $read_arr=[
            $this->getTranslation()->_('un_read'),
            $this->getTranslation()->_('has_read'),
        ];
        // $reply_arr=['未回复','已回复'];
        $reply_arr=[
            $this->getTranslation()->_('wait_reply'),
            $this->getTranslation()->_('already_reply'),
        ];

        if (!empty($data)){
            $data['reply_text']=in_array($data['is_reply'],array_keys($reply_arr))?$reply_arr[$data['is_reply']]:'';
            $data['read_text']=in_array($data['is_read'],array_keys($read_arr))?$read_arr[$data['is_read']]:'';
            $data['ask_type_text']=in_array($data['ask_type'],array_keys($type_arr))?$type_arr[$data['ask_type']]:'type 为空';
            if(!empty($data['file_url'])){
                $format = array();
                $arr = explode(',', $data['file_url']);
                foreach ($arr as $k => $v){
                    $ex = explode('|' , $v);
                    $format[$k]['file_name'] = $ex[0];
                    $format[$k]['file_url'] = $ex[1];
                }
                $data['file_json'] = $format;
            }

            if(!empty($data['reply_file_url'])){
                $format = array();
                $arr = explode(',', $data['reply_file_url']);
                foreach ($arr as $k => $v){
                    $ex = explode('|' , $v);
                    $format[$k]['file_name'] = $ex[0];
                    $format[$k]['file_url'] = $ex[1];
                }
                $data['reply_file_json'] = $format;
            }

            if ($data['is_reply']==1){
                $sql_query = "update mail_to_ceo  set is_read=1  where id={$mail_id}   ;";
                $result_query = $di->get('db')->execute($sql_query);
                if ($result_query){
                    $data['is_read']=1;
                    $data['read_text']=$read_arr[1];
                    $d['data'] = $data;
                    return $this->checkReturn($d);
                }
            }
            $d['data'] = $data;
            return $this->checkReturn($d);
        }else{
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }
    }

    //添加 ceo 信件
    public function add_ceo_mail($paramIn){

        try {
            $userinfo = $paramIn['user_info'];
            $staff_id = $userinfo['id'];
            $staff_name = $userinfo['name'];
            $content = $paramIn['content'];
            $content = strip_tags($content);
            $type = $paramIn['type'];
            $file_str = '';

            if(!empty($paramIn['file_json'])){
                foreach ($paramIn['file_json'] as $file){
                    $file_str .= $file['file_name'] . '|' . $file['file_url'].',';
                }
                $file_str = rtrim($file_str,',');
            }


            //内容长度 校验
            if (mb_strlen($content) > 2000 || mb_strlen($content) < 10){
                return $this->checkReturn(-3, $this->getTranslation()->_('string_len'));
            }
            //创建时间转换泰国时间
            $add_hour = $this->getDI()['config']['application']['add_hour'];
            $create_time = gmdate("Y-m-d H:i:s", time() + $add_hour * 3600);

            if (empty($type)){
                return $this->checkReturn(-3, $this->getTranslation()->_('select_type'));
            }

            $MailToCeo = new MailToCeo();
            $data_mail_to_ceo['staff_id'] = $staff_id;
            $data_mail_to_ceo['staff_name'] = $staff_name;
            $data_mail_to_ceo['content'] = $content;
            $data_mail_to_ceo['type_v2'] = $type;
            $data_mail_to_ceo['create_time'] = $create_time;

            $data_mail_to_ceo['img_url'] = empty($paramIn['img_json']) ? '' : implode(',', $paramIn['img_json']);
            $data_mail_to_ceo['file_url'] = $file_str;

            if($MailToCeo -> create($data_mail_to_ceo)) {
                $MailToCeo -> getWriteConnection() -> lastInsertId($MailToCeo -> getSource());
            }
            return $this->checkReturn([]);
        } catch (Exception $e) {
            $this->wLog('add_ceo_mail', $e->getMessage(), 'backyard_server');
            return $this->checkReturn(-3, $this->getTranslation()->_('no_server'));
        }

    }

    //未读消息接口 (ceo 和 msg)
    public function un_read($param){
        $staff_id = $param['staff_id'];
        //ceo 信箱: 已回复 未读
        $ceo_sql = "
            -- Ceo Mail: 系统已回复, 员工未读的数量
            SELECT 
                COUNT(mail.id) 
            FROM 
                ceo_mail_staff_problem_order AS mail 
            WHERE 
                mail.staff_id = {$staff_id} AND mail.problem_status = 1 AND mail.is_read_sys_reply = 0
            ";
        $data['ceo_unread'] = $this->getDI()->get('db_rby')->fetchColumn($ceo_sql);

        //backyard 消息未读
        $msg_un_read = "select count(0)
                        from  message_courier as mc 
                        where mc.staff_info_id = {$staff_id} and mc.read_state=0  and  mc.is_del=0 ";
        $data['msg_unread'] = $this->getDI()->get('db_coupon_r')->fetchColumn($msg_un_read);
        return $data;
    }


    /**
     * 消息列表导航
     * @return array[]
     */
    public function getMsgTypeInit()
    {
        return  [
            [
                'un_read'=>0,
                'name'=>$this->getTranslation()->_('msg_type_name_1'),//全部
                'type'=>'1',
            ],
            [
                'un_read'=>0,
                'name'=>$this->getTranslation()->_('msg_type_name_2'),//普通
                'type'=>'2',
            ],
            [
                'un_read'=>0,
                'name'=>$this->getTranslation()->_('msg_type_name_3'),//问卷
                'type'=>'3',
            ],
            [
                'un_read'=>0,
                'name'=>$this->getTranslation()->_('msg_type_name_4'),//签字
                'type'=>'4',
            ],
            [
                'un_read'=>0,
                'name'=>$this->getTranslation()->_('msg_type_name_5'),//答题
                'type'=>'5',
            ],
            [
                'un_read'=>0,
                'name'=>$this->getTranslation()->_('msg_type_name_6'),//系统消息
                'type'=>'6',
            ],
        ];

    }

    /**
     * 员工未读消息分类
     * @param $param
     * @return array[]
     */
    public function getStaffUnReadMessageTypeList($param)
    {
        $staff_id = $param['staff_id'];
        $msg_un_read = "select count(0) as num ,mc.category
                        from  message_courier as mc  
                        where mc.staff_info_id = {$staff_id} and mc.is_del=0   and  mc.read_state = 0  group by mc.category ";
        $data = $this->getDI()->get('db_coupon_r')->query($msg_un_read);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $masterData = $this->getMasterUnreadStaffData($param);

        if(!empty($masterData)) {
            $data = array_merge($data, $masterData);
        }

        $msg_type_list = $this->getMsgTypeInit();
        if(empty($data)){
            return $msg_type_list;
        }
        foreach ($msg_type_list as &$list) {
            foreach ($data as $datum) {
                $type = $this->msg_category_to_type($datum['category']);
                if($type == $list['type']){
                    $list['un_read'] += intval($datum['num']);
                }
            }

        }
        return $msg_type_list;

    }

    /**
     * 查看支援信息 如果是支援账号，则找到其 主账号
     * @param $params
     * @return array
     */
    public function getMasterUnreadStaffData($params)
    {
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($params['staff_id']);
        if(empty($supportStaffInfo)) {
            return [];
        }
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            if($supportStaffInfo['staff_info_id'] == $params['staff_id']) {
                return [];
            }
            $params['staff_id'] = $supportStaffInfo['staff_info_id'];
        }

        $sql = "select count(0) as num ,mc.category
                        from  message_courier as mc  
                        where mc.staff_info_id = {$params['staff_id']} and mc.is_del=0  and mc.push_state = 2 and  mc.read_state = 0  group by mc.category ";

        $data = $this->getDI()->get('db_coupon_r')->query($sql);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        return $data;
    }


    /**
     * 类型转换
     * @param $category
     * @return string
     */
    public function msg_category_to_type($category)
    {
        switch ($category){
            case 0:
            case 144:
                $type = '2';
                break;
            case 6:
                $type = '3';
                break;
            case 7:
            case 33:
            case 36:
	        case 47:
            case 153:
            case 158:
            case 161:
            case MessageEnums::MESSAGE_CATEGORY_PROBATION_SEND_PDF:
                $type = '4';
                break;
            case 34:
                $type = '5';
                break;
            default:
                $type = '6';
                break;
        }
        return $type;
    }


    //消息列表 客户端接口
    public function msg_list($param){
        $type_sql = $this->getCategorySql($param['type']);

        $page = $param['page'];
        $size = 10;
        $start = ($page - 1) * $size;

        $where['staff_id'] = $param['staff_id'];
        $where['start']    = $start;
        $where['size']     = $size;
        $where['sql']      = $type_sql;

        $data = $this->getCurrentStaffData($where);
        $masterData = $this->getMasterStaffData($where);
        $allData = [];

        if(!empty($data)) {
            $allData = array_merge($allData, $data);
        }

        if(!empty($masterData)) {
            $allData = array_merge($allData, $masterData);
        }

        //给端上返回消息类型
        foreach ($allData as &$datum) {
            $datum['push_time'] = intval($datum['push_time']);
            $datum['type'] = $this->msg_category_to_type($datum['category']);
        }

        $return['data'] = $allData;

        $last_page = (count($data) < $size) && (count($masterData) < $size) ? 1 : 0;
        $return['last'] = $last_page;

        return $return;


    }

    public function getCategorySql($type)
    {
        switch ($type) {
            case 2:
                $type_sql = " and mc.category   in (0,144) ";//通知
                break;
            case 3:
                $type_sql = " and mc.category = 6 ";//问卷
                break;
            case 4:
                $type_sql = " and mc.category in (7, 33 ,36,47,153,158,161,145) ";//签字
                break;
            case 5:
                $type_sql = " and mc.category = 34 ";//答题
                break;
            case 6:
                $type_sql = " and mc.category not in (0,6,7,33,34,36,49,153,158,161,163,145) ";//系统消息
                break;
            default:
                $type_sql = '';
                break;
        }

        return $type_sql;
    }

    /**
     * 获取当前工号消息
     * @param $params
     * @return mixed
     */
    public function getCurrentStaffData($params)
    {
        $sql = "select mc.id as msg_id,mc.title,UNIX_TIMESTAMP(DATE_FORMAT(mc.created_at, '%Y-%m-%d %H:%i:%s')) as push_time,mc.read_state,mc.top_state, mc.category,
                mc.update_state
                from message_courier   as mc 
                where mc.staff_info_id = {$params['staff_id']} and mc.is_del = 0  {$params['sql']} 
                order by mc.top_state desc,
                         mc.read_state asc,
                         mc.created_at desc
                limit  {$params['start']}, {$params['size']}
                ";


        $data = $this->getDI()->get('db_coupon_r')->query($sql);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        return $data;
    }

    /**
     * 查看支援信息 如果是支援账号，则找到其 主账号
     * @param $params
     * @return array
     */
    public function getMasterStaffData($params)
    {
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($params['staff_id']);
        if(empty($supportStaffInfo)) {
            return [];
        }
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            if($supportStaffInfo['staff_info_id'] == $params['staff_id']) {
                return [];
            }
            $params['staff_id'] = $supportStaffInfo['staff_info_id'];
        }

        $sql = "select mc.id as msg_id,mc.title,UNIX_TIMESTAMP(DATE_FORMAT(mc.created_at, '%Y-%m-%d %H:%i:%s')) as push_time,mc.read_state,mc.top_state, mc.category,
                mc.update_state
                from message_courier   as mc 
                where mc.staff_info_id = {$params['staff_id']} and mc.is_del = 0 and mc.push_state = 2 {$params['sql']} 
                order by mc.top_state desc,
                         mc.read_state asc,
                         mc.created_at desc
                limit  {$params['start']}, {$params['size']}
                ";


        $data = $this->getDI()->get('db_coupon_r')->query($sql);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        return $data;
    }

    //消息详情 客户端接口
    public function msg_detail($param){
        $msg_id = $param['msg_id'];
        $staff_id = $param['staff_id'];

        $sql = "select mc.id as msg_id,mc.title,UNIX_TIMESTAMP(DATE_FORMAT(mc.created_at, '%Y-%m-%d %H:%i:%s')) as push_time,mc.read_state
                       , m.message content
                       , m.related_id
                       , mc.staff_info_id
                       , mc.category
                       , mc.category_code
                       , mc.message_content_id
                       , mc.by_show_type
                       , mc.created_at
                       , mc.updated_at
                       , mc.update_state
                      
                from message_courier   as mc 
                inner join  message_content as m on mc.message_content_id = m.id
                where mc.id = :message_id and mc.is_del = 0
                ";

        $data = $this->getDI()->get('db_coupon_r')->query($sql, ['message_id' => $msg_id]);
        $data = $data->fetch(\Phalcon\Db::FETCH_ASSOC);

        if(empty($data)){
            return $this->checkReturn(array('data' => $data));
        }

        unset($data['created_at']);
        unset($data['updated_at']);

        // 从push进来详情 需要判断 是否为当前用户消息
        if (!empty($data) && $data['staff_info_id'] != $staff_id) {
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        // 新逻辑：获取下一条待处理消息
        $all_undone_msg = $this->get_new_msg(['staff_id' => $staff_id]);

        if (!empty($all_undone_msg[array_search($msg_id, $all_undone_msg) + 1])) {
            $next_msg_id = $all_undone_msg[array_search($msg_id, $all_undone_msg) + 1];
        } else {
            $next_msg_id = '';
        }

        $data['next_msg_id'] = $next_msg_id;
        $data['must_submit'] = 0;
        $data['top_text'] = $this->getTranslation()->_('You_have_a_new_message_that_is_not_read_please_read_the_clock_again');
        $data['foot_text'] = $this->getTranslation()->_('closed_after_reading');

        // content 去掉转义字符
        $data['title'] = stripslashes($data['title']);
        $data['content'] = stripslashes($data['content']);

        // 问卷/签字/签字消息 - 不需加水印
        if (in_array($data['category'], [6, 33, 34])) {
            return $this->checkReturn(array('data' => $data));
        }

        // TODO jia 水印 协助kit 改 水印，本处去掉
//        Image::waterMark($staff_id,$param['staff_name'] ?? '');
//        $data['content'] = ImageServer::addWaterTxtToOSS($data['content'], $staff_id);
//        $data['content'] = '
//        <style>
//            body{background:url('.env.my('pre_url').'/staffwater/'.md5($staff_id).'.png);}
//        </style>'.$data['content'];

        return $this->checkReturn(array('data' => $data));

    }

    //设置已读消息
    public function has_read_operation($msg_id, $isUpdateTopState = false){
        //改置顶状态
        if ($isUpdateTopState) {
            $sql_query = "update message_courier  set read_state = 1, top_state = 0 where id = ? ";
        } else {
            $sql_query = "update message_courier  set read_state = 1  where id = ? and read_state = 0";
        }
        $msg_update_param = [$msg_id];
        return $this->getDI()->get('db_coupon')->execute($sql_query,$msg_update_param);
    }

    //设置已读消息
    public function has_read_operation_by_staffid($msg_id,$staff_id){
        //因为使用 message_content_id 走索引 会锁住多行数据
        //修改为先查主键
        $message_ids = MessageCourierModel::find([
                                                         'columns' => 'id',
                                                         'conditions' => 'staff_info_id = :staff_info_id: and message_content_id = :message_content_id: and read_state = 0  ',
                                                         'bind' => ['message_content_id' =>$msg_id,'staff_info_id'=>$staff_id ],
                                                     ])->toArray();
        $res = false;
        if($message_ids){
            foreach($message_ids as $v) {
                $res = $this->has_read_operation($v['id'],true);
            }
        }
        return $res;
    }


    /**
     * 下班卡 验证消息
     * @param $param
     * @return array
     */
    public function punch_out_msg($param)
    {
        return $this->get_new_msg($param);
    }

    /**
     * copy from guiyatao
     * 过滤掉 离职资产消息 且 资产拥有人没有离职的
     * @param $msg_ids
     * @return array
     */
    public function filterLeaveMsg($msg_ids){
        $msgAssets = MsgAssetModel::find([
                                             'conditions' => ' msg_id in ({ids:array}) ',
                                             'bind' => [
                                                 'ids' => $msg_ids,
                                             ],
                                         ])->toArray();
        if ($msgAssets) {
            $date=date('Y-m-d H:i:s');
            $staffs = HrStaffInfoModel::find([
                                                 'conditions' => " staff_info_id in ({staff_ids:array}) and (state = 1 or state = 3 or (state = 2 and leave_date > '{$date}'))", // 取没有离职的人或者离职 打卡时间小于离职日期
                                                 'bind' => [
                                                     'staff_ids' => array_column($msgAssets, 'staff_id'),
                                                 ],
                                             ])->toArray();
            if ($staffs) {
                $diff_msg_ids = [];
                foreach ($staffs as $staff) {
                    foreach ($msgAssets as $asset){
                        if($staff['staff_info_id']==$asset['staff_id']){
                            $diff_msg_ids[]=$asset['msg_id'];
                        }
                    }
                }
                $msg_ids = array_values(array_diff($msg_ids, $diff_msg_ids));
            }
        }
        $this->getDI()->get('logger')->write_log("离职过滤后的id".json_encode($msg_ids,JSON_UNESCAPED_UNICODE),'info' );

        return $msg_ids;
    }

    /**
     * ToDo 改成从redis里设置缓存的方式做到延迟拦截打卡
     * @param $msgIds
     * @return array|mixed
     */
    public function filterProbationFollowUpMsg($msgIds)
    {
        //1,2,3
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        // 取所有 跟进转正类型消息 和 转正评估跟进结果
        // 1,2
        $messages = MessageCourierModel::find([
            'conditions' => ' id in ({ids:array}) and category in ({categories:array}) ',
            'bind' => [
                'ids' => $msgIds,
                'categories' => [MessageEnums::MESSAGE_CATEGORY_70, MessageEnums::MESSAGE_CATEGORY_71],
            ]])->toArray();

        if ($messages) {
            //3
            $msgIds = array_values(array_diff($msgIds, array_column($messages, 'id'))); // 过滤分此类型的消息
            //1 到截止日期了
            $messages = MessageCourierModel::find([
                'conditions' => ' id in ({ids:array}) and created_at < :created_at:',
                'bind' => [
                    'ids' => array_column($messages, 'id'),
                    'created_at' => gmdate('Y-m-d 23:59:59', time() + $add_hour * 3600 - 86400 * 2),
                ],
            ])->toArray();
            //1,3
            $msgIds = array_values(array_merge($msgIds, array_column($messages, 'id')));
        }

        return $msgIds;

    }


    /**
     * 下班打卡拦截的消息
     * @param $staff_id
     * @return mixed
     */
    protected function getPunchOutMsg($staff_id){
        $db  = $this->getDI()->get('db_coupon_r');
        $sql = "select mc.id, mc.category, mc.category_code, message_content_id, m.related_id, m.message
                    from message_courier mc 
                    inner join  message_content as m on mc.message_content_id = m.id
                    where mc.staff_info_id = :staff_info_id and mc.read_state = 0 and mc.is_del = 0 and !(mc.category = 7 and (m.related_id = '' or m.related_id = 0))
                    order by mc.created_at desc";
        $currentData = $db->query($sql, ['staff_info_id' => $staff_id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($staff_id);
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            if($supportStaffInfo['staff_info_id'] != $staff_id) {
                $sql = "select mc.id, mc.category, mc.category_code, message_content_id, m.related_id, m.message
                    from message_courier mc 
                    inner join  message_content as m on mc.message_content_id = m.id
                    where mc.staff_info_id = :staff_info_id and mc.read_state = 0 and mc.is_del = 0 and mc.push_state = :push_state
                    order by mc.created_at desc";

                $masterData = $db->query($sql, ['staff_info_id' => $supportStaffInfo['staff_info_id'], 'push_state' => MessageCourierModel::PUSH_STATE_SUB_YES])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if(!empty($masterData)) {
                    $currentData = array_merge($currentData, $masterData);
                }
            }
        }

        return $currentData;
    }

    /**
     * 获取新消息 客户端接口
     * @param $param
     * @return array
     */
    public function get_new_msg($param)
    {
        $staff_id        = $param['staff_id'];
        $request_channel = $param['request_channel'] ?? '';
        $info            = $this->getPunchOutMsg($staff_id);
        $redis_obj       = $this->getDI()->get('redisLib');
        $result   = [];
        if (!empty($info)) {
            $questionnaire_msg_id = [];

            //合同审核业务类型
            $messageCategoryBusiness = (int) EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_BUSINESS_CONTRACT_APPLY');
            $messageCategoryIcSettlementMsg = (int) EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_IC_SETTLEMENT_MSG');
            $messageCategoryProbationTarget = (int) EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_TARGET');
            $probationTargetServer = new ProbationTargetServer($this->lang, $this->timezone);
            foreach ($info as $msg) {
                if ($msg['category'] == 6 && $msg['category_code'] == 3) {
                    // 问卷消息
                    $questionnaire_msg_id[$msg['message_content_id']] = $msg['id'];
                }
                elseif (in_array($msg['category'],
                        [
                            MessageEnums::MESSAGE_CATEGORY_CONTRACT,
                            MessageEnums::MESSAGE_CATEGORY_COMPLETE_RESUME,
                            MessageEnums::MESSAGE_CATEGORY_RENEW_CONTRACT_PROPOSE,
                            MessageEnums::CATEGORY_SIGN_CODE_PENALTY_LEAVE_EARLY,
                            $messageCategoryBusiness,
                            $messageCategoryIcSettlementMsg,
                        ]
                    ) && strlen($msg['related_id']) == 10 && $msg['related_id'] > time()) {
                    $delay_msg_info[] = $msg['id'];
                } elseif (isCountry('MY') && ($msg['category'] == MessageEnums::CATEGORY_SIGN) && ($msg['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_ASSET_CONTRACT_AGENT)  && strlen($msg['related_id']) == 10 && $msg['related_id'] > time()) {
                    $delay_msg_info[] = $msg['id'];
                } elseif ($msg['category'] == $messageCategoryProbationTarget && $msg_id = $probationTargetServer->checkMessageDelay($staff_id,$msg)) {
                    $delay_msg_info[] = $msg_id;
                } else {
                    $result[] = $msg['id'];
                }
            }

            $curr_time = date('Y-m-d H:i:s');
            $curr_date = substr($curr_time, 0, 10);

            // 有问卷，进行过滤
            if (!empty($questionnaire_msg_id)) {
                $questionnaire_msg_ids = "'".implode("','", array_keys($questionnaire_msg_id))."'";

                $questionnaire_msg_sql = "
                                        SELECT feedback_setting, end_time, remote_message_id 
                                        FROM message 
                                        WHERE remote_message_id IN ({$questionnaire_msg_ids})
                                        ";

                $message = $this->getDI()->get('db_rby')->query($questionnaire_msg_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($message)) {
                    // 打卡时，仅展示一次的问卷消息入缓存
                    // 打卡调用最新消息接口次数
                    $off_work_call_count_key = md5(self::$cache_off_work_call_count_key_prefix.$staff_id);
                    $off_work_call_count     = $redis_obj->get($off_work_call_count_key);
                    $off_work_call_count     = $off_work_call_count ? $off_work_call_count : 0;

                    // 打卡仅需展示一次的问卷消息ID清单
                    $cache_questionnaire_data_key = md5(self::$cache_temporary_show_undone_questionnaire_msg_key_prefix.$staff_id);
                    $cache_questionnaire_data     = $redis_obj->get($cache_questionnaire_data_key);
                    $cache_questionnaire_data     = !empty($cache_questionnaire_data) ? json_decode($cache_questionnaire_data,
                        true) : [];


                    foreach ($message as $value) {
                        $end_date = !empty($value['end_time']) ? substr($value['end_time'], 0, 10) : '';

                        // 用户消息ID
                        $staff_msg_id = $questionnaire_msg_id[$value['remote_message_id']];

                        // 问卷消息缓存数据
                        $curr_msg_cache_count = $cache_questionnaire_data[$staff_msg_id] ?? 0;

                        // 设置了截止日期: 截止日期当天 或 已过期：必填的，打卡时，一直展示
                        if ($end_date && ($curr_date >= $end_date) && $value['feedback_setting']) {
                            $result[] = $staff_msg_id;
                        } elseif (($end_date == $curr_date) && !$value['feedback_setting']) {
                            // 非必填的，打卡当天且为过截止时间的，仅在打卡时展示一次

                            // 新问卷 或 第一次下班打卡，则展示类该问卷
                            if (($value['end_time'] > $curr_time) && (!$curr_msg_cache_count || !$off_work_call_count)) {
                                $result[] = $staff_msg_id;

                                $cache_questionnaire_data[$staff_msg_id] = $curr_msg_cache_count + 1;
                            }
                        } elseif (($curr_time < $value['end_time']) || empty($value['end_time'])) {
                            // 未到期 或 未设置截止日期的: 每天，此类问卷消息，在打卡时仅提醒一次

                            // 新问卷 或 第一次下班打卡，则展示类该问卷，但
                            if (!$curr_msg_cache_count || !$off_work_call_count) {
                                $result[] = $staff_msg_id;
                            }

                            // 未过期的消息: 只有从打卡渠道访问，才计数处理
                            if ($request_channel == 'off_work_call') {
                                $cache_questionnaire_data[$staff_msg_id] = $curr_msg_cache_count + 1;
                            }
                        }
                    }

                    // 缓存时长：当天的剩余秒数
                    $cache_time = strtotime($curr_date.' 23:59:59') - strtotime($curr_time);

                    // 下班打卡调用该接口 计数
                    if ($request_channel == 'off_work_call') {
                        $off_work_call_count++;
                        $redis_obj->set($off_work_call_count_key, $off_work_call_count, $cache_time);

                        // 写日志观测
                        $this->wLog('backyard - get_new_msg: 工号: '.$staff_id.', 下班打卡调用次数: ',
                            $off_work_call_count, '', 'info');
                    }

                    // 缓存问卷仅展示一次的消息ID
                    if (!empty($cache_questionnaire_data)) {
                        $cache_questionnaire_data = json_encode($cache_questionnaire_data);
                        $redis_obj->set($cache_questionnaire_data_key, $cache_questionnaire_data, $cache_time);

                        // 写日志观测
                        $this->wLog('backyard - get_new_msg: 工号: '.$staff_id.', 符合仅展示一次的问卷消息ID清单: ',
                            $cache_questionnaire_data, '', 'info');
                    }
                }
            }

            rsort($result);

            if(!empty($delay_msg_info) && in_array($request_channel,['off_work_call','need_delay_message'])){
                if(count($result) == 1){
                    $result = array_merge($result, [$delay_msg_info[0]]);
                }
                if(count($result) == 0){
                    $delay_msg_cache_key = sprintf(RedisEnums::DELAY_MESSAGE, $staff_id);
                    $cache_delay_data    = $redis_obj->get($delay_msg_cache_key);
                    $cache_time             = strtotime($curr_date.' 23:59:59') - strtotime($curr_time);
                    if (empty($cache_delay_data)) {
                        //缓存里没有 代表没看过 则加到给到查看的结果集里
                        $result = $delay_msg_info;
                        //然后存到缓存 每次存一个
                        $redis_obj->set($delay_msg_cache_key, json_encode([$result[0]]), $cache_time);
                    } else {
                        //有缓存 看是否有新的消息进来
                        $cache_delay_data = json_decode($cache_delay_data, true);
                        if ($new = array_diff($delay_msg_info, $cache_delay_data)) {
                            $result= array_values($new);
                            //有就把新的给到查看的结果集里
                            //然后把全量的再存到缓存
                            $redis_obj->set($delay_msg_cache_key, json_encode(array_merge($cache_delay_data,[$result[0]])), $cache_time);
                        }
                    }
                }
            }
        }

        return $result;
    }

    //获取是否还有未读消息
    public function get_unread($staff_id){
        $db = $this->getDI()->get('db_coupon_r');
        $check_sql = "select count(1)
                        from message_courier mc 
                        where mc.staff_info_id = {$staff_id} and mc.read_state = 0 and mc.is_del=0 ";
        return $db->fetchColumn($check_sql);

    }


    //紧急事故类型
    public function get_emergency_type(){
        $all = self::$emergency_type;
        foreach ($all as &$li){
            $li = $this->getTranslation()->_($li);
        }

        return $all;
    }

    //紧急事故类型
    public function get_personal_type(){
        $all = self::$personal_type;
        foreach ($all as &$li){
            $li = $this->getTranslation()->_($li);
        }

        return $all;
    }

    //添加紧急事故
    public function add_emergency($param){
        try{
            $data['staff_info_id'] = $param['staff_id'];
            $data['phone'] = $param['phone'];
            $date = $param['date_at'];
            $time = $param['time_at'];

            if(!empty($date) && !empty($time))
                $data['date_at'] = date('Y-m-d H:i:s', strtotime($date. $time));

            //验证工号是否正确
            $staff_re = new StaffRepository($this->lang);
            $post_info = $staff_re->getStaffpositionV2($param['staff_id']);
            if(empty($post_info))
                return -2008;

            $data['type'] = $param['type'];
            //是否选中个人
//            $is_person = empty($param['is_person']) ? 0 :intval($param['is_person']);
//            if(!empty($is_person) && $is_person > 0)
//                $data['personal_type'] = intval($param['ps_type']);

            $data['personal_type'] = empty($param['ps_type']) ? 0 :intval($param['ps_type']);

            //是否选中财产
//            $is_property = empty($param['is_property']) ? 0 :intval($param['is_property']);
//            if(!empty($is_property) && $is_property > 0){
//                $data['num'] = intval($param['num']);
//                $data['worth'] = $param['worth'] * 100;
//            }

            $data['num'] = empty($param['num']) ? 0 : intval($param['num']);
            $data['worth'] = empty($param['worth']) ? 0 : $param['worth'] * 100;

            $data['content'] = $param['content'];
            if(!empty($param['img_url']))
                $data['img_url'] = implode(',', $param['img_url']);

            //添加人
            $data['operator'] = $param['user_info']['id'];

            $re = new ApplyRepository($this->lang);
            return $re->add_emergency($data);
        }catch (\Exception $e){
            $this->wLog('add_emergency', $e->getMessage(), 'backyard_server');
            return false;
        }

    }

    public function get_emergency_manager($param){
        $staff = $param['staff_id'];
        $staff_info = $param['user_info'];
        $re = new StaffRepository($this->lang);
        if($staff != $param['user_info']['id']){//输入工号不是当前登陆人
            $staff_info = $re->getStaffpositionV2($staff);
        }

        if(empty($staff_info))
            return false;

        //获取负责人工号
        if($staff_info['organization_type'] == 1){//找网点负责人
            $store_server = new SysStoreServer($this->lang);
            $store_info = $store_server->getStoreByid($staff_info['organization_id']);
            $manager_id = $store_info['manager_id'];

        }else{//总部员工 找直接上级
            $or = new OvertimeRepository($this->timezone);
            $manager_id  = $or->getHigherStaffId(['staff_info_id' => $staff]);
            $manager_id = empty($manager_id['value']) ? 0 : $manager_id['value'];
        }

        $manager_info = '';
        if(!empty($manager_id))
            $manager_info = $re->getStaffpositionV2($manager_id);

        return $manager_info;

    }

    //紧急事故联系 是否展示菜单权限方法
    public function emergency_permission($staff_id){
        $re = new StaffRepository($this->lang);
        $staff_info = $re->getStaffPosition($staff_id);
        if(!in_array($staff_info['formal'],array(0,1,4)))
            return false;
        return true;
    }

    /**
     * 获取问卷消息详情
     *
     */
    public function get_questionnaire_detail($param){
        if (empty($param['remote_msg_id'])) {
            return [];
        }

        // 获取问卷消息 和 问卷库内容
        $sql = "SELECT m.title,m.end_time, m.content, m.feedback_setting, lib.qn_lib_content, m.remote_message_id, lib.qn_lib_desc
                FROM message AS m LEFT JOIN questionnaire_lib AS lib 
                ON m.questionnaire_lib_id = lib.id
                WHERE m.remote_message_id = :remote_msg_id LIMIT 1";
        $query_param = [
            'remote_msg_id' => $param['remote_msg_id'],
        ];

        $res = $this->getDI()->get('db_rby')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);


        return $res ? $res : [];
    }

    public function get_question_courier_detail($param){
        $remote_msg_id = $param['remote_msg_id'];
        $staff_info_id = $param['staff_info_id'];
        if (empty($staff_info_id) || empty($remote_msg_id)) {
            return [];
        }

        $sql = "SELECT id, title, read_state AS submit_status
                FROM message_courier
                WHERE staff_info_id = :staff_id 
                and  message_content_id = :msg_id AND is_del = :is_del
                LIMIT 1";
        $query_param = [
            'msg_id' => $remote_msg_id,
            'staff_id' => $staff_info_id,
            'is_del' => 0,
        ];

        $res = $this->getDI()->get('db_coupon_r')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);

        return $res ? $res : [];
    }

    /**
     * 获取员工问卷消息
     */
    public function get_staff_questionnaire($param){
        $remote_msg_id = $param['msg_id'];
        $staff_info_id = $param['staff_info_id'];
        if (empty($staff_info_id) || empty($remote_msg_id)) {
            return [];
        }

        $sql = "SELECT id, title, read_state AS submit_status
                FROM message_courier
                WHERE staff_info_id = :staff_id 
                    AND message_content_id = :msg_id AND  category = :category AND category_code = :category_code
                    AND is_del = :is_del
                LIMIT 1";
        $query_param = [
            'msg_id' => $remote_msg_id,
            'staff_id' => $staff_info_id,
            'category' => 6,
            'category_code' => 3,
            'is_del' => 0,
        ];

        $res = $this->getDI()->get('db_coupon_r')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $res ? $res : [];
    }

    // 获取该员工已提交的问卷答案
    public function get_staff_answer($param){
        if (empty($param['remote_message_id']) || empty($param['staff_info_id'])) {
            return [];
        }

        // 获取问卷消息 和 问卷库内容
        $sql = "SELECT id,answer_content, submit_time FROM questionnaire_answer 
                WHERE staff_info_id = :staff_id AND remote_message_id = :remote_message_id LIMIT 1";
        $query_param = [
            'staff_id' => $param['staff_info_id'],
            'remote_message_id' => $param['remote_message_id'],
        ];

        $res = $this->getDI()->get('db_rby')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $res ? $res : [];
    }

    // 添加问卷结果
    public function add_answer($param){
        if (empty($param)) {
            return false;
        }

        // 添加问卷 并 修改问卷消息提交状态
        $db_bi = $this->getDI()->get('db');
        $db_coupon = $this->getDI()->get('db_coupon');
        $db_bi->begin();
        $db_coupon->begin();
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        try {
            $answer_sql = "INSERT INTO questionnaire_answer 
                            (remote_message_id, answer_content, staff_info_id, submit_time) 
                            VALUES (?, ?, ?, ?)";
            $answer_data = [
                $param['remote_message_id'],
                $param['answer'],
                $param['staff_info_id'],
                gmdate('Y-m-d H:i:s', time()+ $add_hour * 3600),
            ];
            $db_bi->execute($answer_sql, $answer_data);

            $msg_update_sql = "UPDATE message_courier SET read_state = ? WHERE id = ? LIMIT 1";
            $msg_update_param = [1, $param['message_courier_id']];
            $db_coupon->execute($msg_update_sql, $msg_update_param);

            $db_bi->commit();
            $db_coupon->commit();

            return true;
        } catch (\Exception $e) {
            $db_bi->rollback();
            $db_coupon->rollback();

            $this->wLog('backyard: questionnaire add_answer - ', $e->getMessage(), 'backyard_server');
            return false;
        }
    }

    // 更新问卷结果-答案，提交时间
    public function update_answer($param){
        if (empty($param)) {
            return false;
        }
        $answer_info = $this->get_staff_answer($param);
        if(empty($answer_info)) {
            return false;

        }

        // 添加问卷 并 修改问卷消息提交状态
        $db_bi = $this->getDI()->get('db');
        $db_coupon = $this->getDI()->get('db_coupon');

        try {
            $db_bi->begin();
            $db_coupon->begin();

            $answer_data['answer_content'] = $param['answer'];
            $answer_data['submit_time'] = date('Y-m-d H:i:s', time());
            $db_bi->updateAsDict('questionnaire_answer', $answer_data, ['conditions' => "id =?", 'bind' => [$answer_info['id']]]);

            $msg_update_sql = "UPDATE message_courier SET read_state = ? WHERE id = ? LIMIT 1";
            $msg_update_param = [1, $param['message_courier_id']];
            $db_coupon->execute($msg_update_sql, $msg_update_param);

            $db_bi->commit();
            $db_coupon->commit();

            return true;
        } catch (\Exception $e) {
            $db_bi->rollback();
            $db_coupon->rollback();

            $this->wLog('backyard: questionnaire update_answer - ', $e->getMessage(), 'backyard_server');
            return false;
        }
    }

    // 添加问卷结果-只添加答案数据，没有答案内容
    public function add_answer_question($param){
        if (empty($param)) {
            return false;
        }

        // 添加问卷 并 修改问卷消息提交状态
        $db_bi = $this->getDI()->get('db');
        try {
            $answer_sql = "INSERT INTO questionnaire_answer
                            (remote_message_id, staff_info_id, create_time)
                            VALUES (?, ?, ?)";
            $answer_data = [
                $param['remote_message_id'],
                $param['staff_info_id'],
                date('Y-m-d H:i:s', time()),
            ];
            $db_bi->execute($answer_sql, $answer_data);

            return true;
        } catch (\Exception $e) {
            $this->wLog('backyard: questionnaire add_answer_question - ', $e->getMessage(), 'backyard_server');
            return false;
        }
    }

    // 更新问卷结果
    public function update_answer_question($answerId){
        if (empty($answerId)) {
            return false;
        }

        // 添加问卷 并 修改问卷消息提交状态
        $db_bi = $this->getDI()->get('db');
        try {
            $db_bi->updateAsDict(
                'questionnaire_answer',
                ['create_time' => date('Y-m-d H:i:s', time())],
                'id = ' . $answerId
            );
            return true;
        } catch (\Exception $e) {
            $this->wLog('backyard: questionnaire update_answer_question - ', $e->getMessage(), 'backyard_server');
            return false;
        }
    }

    // 获取员工签字消息
    public function get_staff_sign_msg($param){
        $remote_msg_id = $param['msg_id'];
        $staff_info_id = $param['staff_info_id'];
        if (empty($staff_info_id) || empty($remote_msg_id)) {
            return [];
        }

        $sql = "SELECT id, staff_info_id, category,category_code, push_state, read_state, message_content_id
                FROM message_courier
                WHERE id = :msg_id";
        $query_param = [
            'msg_id' => $remote_msg_id,
        ];

        $res = $this->getDI()->get('db_coupon_r')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $res ? $res : [];
    }

    // 获取签字消息结果
    public function get_sign_msg_result($param)
    {
        if (empty($param['remote_message_id']) || empty($param['staff_info_id'])) {
            return [];
        }

        $sql = "SELECT sign_url,sign_pdf_url,created_at FROM sign_msg_result
                WHERE staff_info_id = :staff_id AND remote_message_id = :remote_message_id LIMIT 1";
        $query_param = [
            'staff_id' => $param['staff_info_id'],
            'remote_message_id' => $param['remote_message_id'],
        ];

        $res = $this->getDI()->get('db_rby')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $res ? $res : [];

    }

    // 添加签字结果
    public function add_sign($param){
        if (empty($param)) {
            return false;
        }

        // 添加签名 并 修改签名消息提交状态
        $db_bi = $this->getDI()->get('db');
        $db_coupon = $this->getDI()->get('db_coupon');
        $db_bi->begin();
        $db_coupon->begin();

        try {
            $sign_sql = "INSERT INTO sign_msg_result
                            (staff_info_id, remote_message_id, sign_url)
                            VALUES (?, ?, ?)";
            $sign_data = [
                $param['staff_info_id'],
                $param['remote_message_id'],
                $param['sign_url'],
            ];
            $db_bi->execute($sign_sql, $sign_data);

            $msg_update_sql = "UPDATE message_courier SET read_state = ? WHERE id = ? LIMIT 1";
            $msg_update_param = [1, $param['message_courier_id']];
            $db_coupon->execute($msg_update_sql, $msg_update_param);

            //修改签字状态
            if (
                isCountry('MY')
                && $param['category'] == MessageEnums::CATEGORY_SIGN
                && $param['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_AGREEMENT
            ) {
                $db_bi->updateAsDict(
                    (new HrAgreementSignRecordModel())->getSource(),
                    ["state" => HrAgreementSignRecordModel::STATE_WAIT],
                    [
                        "conditions" => 'message_id=?',
                        'bind'       => [
                            $param['msg_id'],
                        ],
                        'bindTypes'  => [\PDO::PARAM_STR],
                    ]
                );
            }
            if (
                isCountry('MY')
                && $param['category'] == MessageEnums::CATEGORY_SIGN
                && in_array($param['category_code'], [
                    MessageEnums::CATEGORY_SIGN_CODE_CONTRACT_EXPIRE,
                    MessageEnums::CATEGORY_SIGN_CODE_ASSET_INDEPENDENT,
                    MessageEnums::CATEGORY_SIGN_CODE_ASSET_CONTRACT_AGENT,
                    EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_PDPA'),
                    EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_BEBAS'),
                ])
            ) {
                $db_bi->updateAsDict(
                    (new MessagePdfModel())->getSource(),
                    ["state" => MessagePdfModel::STATE_WAIT],
                    [
                        "conditions" => 'msg_id=?',
                        'bind'       => [
                            $param['msg_id'],
                        ],
                        'bindTypes'  => [\PDO::PARAM_STR],
                    ]
                );
            }

            //培训系统签字消息
            if (
                isCountry()
                && $param['category'] == MessageEnums::CATEGORY_SIGN
                && $param['category_code'] == EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_SCHOOL_CERTIFICATE_SEND')
            ) {
                $apiClient = new ApiClient('school_rpc', '', 'staff_learn_plan_cert_sign', $this->lang);
                $apiClient->setParams([
                    'msg_id'        => $param['msg_id'],
                    'staff_info_id' => $param['staff_info_id'],
                    'sign_url'      => $param['sign_url'],
                ]);

                $result = $apiClient->execute();

                if (!isset($result['result']['code']) || ($result['result']['code'] != 1)) {
                    throw new BusinessException('school rpc error!');
                }
            }

            //新增 菲律宾 mobile dc 签字 回调fbi
            if(isCountry('PH') && $param['category'] == MessageEnums::CATEGORY_SIGN && $param['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_MOBILE_DC_DSHEET){
                $this->sendDsheetMQ($param);
            }

            $db_bi->commit();
            $db_coupon->commit();

            if($param['category'] == MessageEnums::CATEGORY_SIGN && in_array($param['category_code'], [MessageEnums::CATEGORY_SIGN_CODE, MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE])) {
                $ac                    = new ApiClient('hcm_rpc', '', 'make_message_sign_pdf', $this->lang);
                $setParams['staff_info_id']     = $param['staff_info_id'];
                $setParams['remote_message_id'] = $param['remote_message_id'];
                $ac->setParams($setParams);
                $ac->execute();
            }

            return true;
        } catch (\Exception $e) {
            $db_bi->rollback();
            $db_coupon->rollback();

            $this->wLog('backyard: sign_msg add_sign - ', $e->getMessage(), 'backyard_server');
            return false;
        }
    }

    // 获取消息详情
    public function get_msg_detail($param){
        if (empty($param['remote_msg_id'])) {
            return [];
        }

        // 获取问卷消息 和 问卷库内容
        $sql = "SELECT title, content,extend,send_type
                FROM message
                WHERE remote_message_id = :remote_msg_id";
        $query_param = [
            'remote_msg_id' => $param['remote_msg_id'],
        ];

        $res = $this->getDI()->get('db_rby')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $res ? $res : [];
    }

    // 获取员工信息
    public function get_staff_info($staff_info_id)
    {
        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoOne($staff_info_id);

        if (empty($staffInfo)) {
            return [];
        }

        $jobInfo = JobTitleRepository::getJobTitleInfo($staffInfo['job_title']);

        $data['name']          = $staffInfo['name'];
        $data['staff_info_id'] = $staff_info_id;
        $data['hire_type']     = $staffInfo['hire_type'];
        $data['job_title']     = $jobInfo['job_name'] ?? '';

        return $data;
    }

    // 获取员工信息
    public function get_staff_tool_info($staff_info_id)
    {
        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoToolOne($staff_info_id);

        if (empty($staffInfo)) {
            return [];
        }

        $jobInfo = JobTitleRepository::getJobTitleInfo($staffInfo['job_title']);

        $data['name']          = $staffInfo['name'];
        $data['staff_info_id'] = $staff_info_id;
        $data['hire_type']     = '';
        $data['job_title']     = $jobInfo['job_name'] ?? '';

        return $data;
    }

    /**
     * 检测员工是否有未完成的必填问卷[feedback_setting 1必填] 或 签字消息[均为必填项]
     * @param $staff_info_id int 工号
     * @return $result array
     */
    public function undoneMsgCheck($staff_info_id)
    {
        if (empty($staff_info_id)) {
            return [];
        }

        try {

            // 获取未处理的所有消息
            $message_sql = "SELECT mc.category, mc.category_code, mc.message_content_id
                        FROM message_courier as mc
                inner join  message_content as m on mc.message_content_id = m.id
                        WHERE mc.staff_info_id = {$staff_info_id} AND mc.read_state = 0 AND mc.is_del=0 ";
            $all_unread_msg = $this->getDI()->get('db_coupon_r')->query($message_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            if (empty($all_unread_msg)) {
                return [];
            }

            // 筛选未填的问卷消息、签字消息
            $undone_msg = [];
            foreach ($all_unread_msg as $value) {
                // 问卷
                if ($value['category'] == 6 && $value['category_code'] == 3) {
                    $undone_msg[$value['message_content_id']] = 'questionnare';
                } else if ($value['category'] == 33) {
                    // 直接返回
                    return [
                        'undone_msg_type' => 'sign',
                    ];
                }
            }

            if (empty($undone_msg)) {
                return [];
            }

            unset($all_unread_msg);

            // 验证上述消息是否必填
            $undone_msg_content_id_list = array_keys($undone_msg);
            $undone_msg_content_ids = "'".implode("','", $undone_msg_content_id_list)."'";
            $message_attr_sql = "SELECT feedback_setting
                                FROM message
                                WHERE remote_message_id IN ($undone_msg_content_ids)
                                AND isdel = 9";
            $undone_questionnaire_msg = $this->getDI()->get('db_rby')->query($message_attr_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (!empty($undone_questionnaire_msg)) {
                foreach ($undone_questionnaire_msg as $messge_val) {
                    if ($messge_val['feedback_setting'] == 1) {
                        return [
                            'undone_msg_type' => 'questionnare',
                        ];
                    }
                }
            }

            return [];
        } catch (\Exception $e) {
            $this->wLog('backyard: leaveAdd undoneMsgCheck - ', $e->getMessage(), 'backyard_server');
            return [];
        }
    }

    //请假前 先验证 有没有未读消息
    public function leaveCheckMsg($staffId){
        $data = $this->undoneMsgCheck($staffId);
        if (empty($data)) {
            return true;
        }

        switch ($data['undone_msg_type']) {
            case 'sign':
                $translation_key = 'leave_add_undone_sign_msg';
                break;
            default:
                $translation_key = 'leave_add_undone_questionnaire_msg';
                break;
        }

        throw new ValidationException($this->getTranslation()->_($translation_key));
    }


    /**
     * 获取考试详情和进度
     * @param $param
     * @return array
     */
    public function get_qa_exam_detail($param){
        $remote_msg_id = $param['msg_id'];
        $staff_info_id = $param['staff_info_id'];
        if (empty($staff_info_id) || empty($remote_msg_id)) {
            return [];
        }

        $sql = "SELECT qa.question_content, qa.answer_content
                FROM questionnaire_answer qa
                WHERE qa.remote_message_id = :msg_id and qa.staff_info_id = :staff_id LIMIT 1
                ";
        $query_param = [
            'msg_id' => $remote_msg_id,
            'staff_id' => intval($staff_info_id),
        ];

        $res = $this->getDI()->get('db_rby')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $res ? $res : [];


    }

    /**
     * 存储考试详情
     * @param $param
     * @return array
     */
    public function save_qa_answer($param)
    {
        $question_id = $param['question_id'];
        $redis = $this->getDI()->get('redisLib');
        //读取答题进度
        $detail = $this->get_qa_exam_detail(['msg_id' => $param['remote_message_id'],'staff_info_id' => $param['staff_id']]);
        $is_right = 0;
        if(!empty($detail)){
            //TODO题目存入reids
            $qa_key = 'qalist:'.$param['remote_message_id'].$param['staff_id'];
            $question_all = $redis->get($qa_key);
            if(empty($question_all)){
                $question_all = json_decode($detail['question_content'] ?? [],true);
                $redis->set($qa_key,serialize($question_all),60*5);
            }else{
                $question_all = unserialize($question_all);
            }
            $question_list = $question_all['question'];
            //对比正确答案
            $question_list = array_column($question_list,null,'question_uuid');
            //正确答案
            $correct_answer = $question_list[$param['question_id']]['correct_answer'] ?? [];

            //多选
            if(is_array($correct_answer)){
                //类型要一样
                if(is_array($param['answer_content'])){
                    sort($correct_answer);
                    sort($param['answer_content']);
                    if(md5(json_encode($correct_answer)) == md5(json_encode($param['answer_content']))){
                        $is_right = 1;
                    }
                }
            }else{
                //兼容一个牛逼的bug 类型是多选，但是正确答案不是数组
                if(is_array($param['answer_content']) &&  count($param['answer_content']) == 1){
                    $param['answer_content'] = current($param['answer_content']);
                }

                //单选
                if($correct_answer == $param['answer_content']){
                    $is_right = 1;
                }
            }
            //回答正确
            if($is_right){
                $keys = array_keys($question_list);
                //获取下一题
                $next_id = 0;
                foreach ($keys as $k => $v) {
                    if($v == $param['question_id'] ){
                        $next_id = $k;
                        $next_id ++;
                    }
                }
                $is_r = $this->save_qa_exam_answer($detail,$param);
                //回答完成了
                if($next_id >= count($keys)){
                    //更新最后的已读状态
                    if($is_r){
                        $msg_read = $this->has_read_operation_by_staffid($param['remote_message_id'],$param['staff_id']);
                        //发送mns
                        $c_detail = $this->get_question_courier_detail(['remote_msg_id' => $param['remote_message_id'],
                            'staff_info_id' => $param['staff_id']]);
                        if(!empty($c_detail) && $c_detail['id']){
                            $this->addHaveReadMsgToMns($c_detail['id']);
                        }
                    }
                    $question_id =  '';
                }else{
                    $question_id = $keys[$next_id];
                    //更新答案
                }
                //保存答案

            }else{
                $question_id = $param['question_id'];
            }

        }
        //记录每次的答案
        $this->save_qa_exam_log($param);
        $data['is_right'] = $is_right;
        $data['question_id'] = $question_id;

        return $data ?? [];
    }

    /**
     * 问题详情保存答案
     * @param $detail
     * @param $param
     * @return bool
     */
    public function save_qa_exam_answer($detail,$param)
    {
        // 添加签名 并 修改签名消息提交状态
        $db_bi = $this->getDI()->get('db');
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        try {
            if(empty($detail['answer_content']) || !json_decode($detail['answer_content'],true)){
                //从未填写过
                $answer_content = [];
            }else{
                $answer_content = json_decode($detail['answer_content'] ?? [],true);
            }
            $answer_content[$param['question_id']] = $param['answer_content'];

            $msg_update_sql = "UPDATE questionnaire_answer SET answer_content = ? ,submit_time = ? WHERE remote_message_id = ? and staff_info_id = ?  LIMIT 1";
            $msg_update_param = [json_encode($answer_content), gmdate('Y-m-d H:i:s', time() + $add_hour * 3600), $param['remote_message_id'],$param['staff_id']];
            $db_bi->execute($msg_update_sql, $msg_update_param);
            return true;
        } catch (\Exception $e) {
            $this->wLog('backyard: sign_msg add_sign - ', $e->getMessage(), 'backyard_server');
            return false;
        }
    }

    /**
     * 保存log
     * @param $param
     */
    public function save_qa_exam_log($param)
    {
        $redis = $this->getDI()->get('redisLib');
        $res   = $redis->lpush(RedisEnums::MESSAGE_QA_LOG, json_encode($param));
        $this->getDI()->get("logger")->write_log(['param' => $param, 'func' => 'save_qa_exam_log', 'res' => $res], "info");
    }


    public function get_qa_exam_log($param)
    {
        $remote_msg_id = $param['msg_id'];
        $staff_info_id = $param['staff_info_id'];
        if (empty($staff_info_id) || empty($remote_msg_id)) {
            return [];
        }
        $conditions = "remote_message_id = :remote_message_id: and staff_id = :staff_id:";
        $bind = ["remote_message_id" => $remote_msg_id, 'staff_id' => $staff_info_id];
        $_model = MessageQaAnswerLogModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return !empty($_model) ? $_model->toArray() : [];
    }

    /**
     *
     * 消息读取状态入队
     * 对应FBI端的消息中心：普通消息、签字消息、问卷消息 证书消息
     *
     * @param string $msg_id message_courier id
     * @return bool $result
     *
     */
    public function addHaveReadMsgToMns(string $msg_id)
    {
        if (empty($msg_id)) {
            return false;
        }

        $redis = $this->getDI()->get('redisLib');
        $res   = $redis->lpush(RedisEnums::MESSAGE_READ_CONT, $msg_id);
        $this->getDI()->get("logger")->write_log(['msg_id' => $msg_id, 'func' => 'addHaveReadMsgToMns', 'res' => $res],
            "info");
        return $res;
    }


    /**
     * ceo恢复未读 和 消息未读 IT工单数量
     * @param $paramIn
     * @param $userinfo
     * @return array
     */
    public function un_read_and_audit($paramIn,$userinfo ,$source = PushEnums::PUSH_OPT_SOURCE_TASK)
    {
        try{
            if(empty($paramIn)){
                return [];
            }
            //user
            $staffInfo = StaffInfoModel::findFirst([
                'conditions' => 'id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $paramIn['staff_id'],
                ],
            ]);
            if(!empty($staffInfo)){
                $staffInfo = $staffInfo->toArray();
                $userinfo = array_merge($userinfo,$staffInfo);
            }else{
                return [];
            }
            $checkStaff = (new StaffServer())->getStaffInfo(['staff_info_id' => $paramIn['staff_id']], 'staff_info_id');
            if (empty($checkStaff)) {
                return [];
            }


            //开启待审批数开关
            if (env('enable_audit_num', 1) == 1) {
                $returnArr = (new AuditServer($this->lang,$this->timezone))->waitAuditNum($paramIn);
            } else {
                $returnArr['data']['num']    = 0;
                $returnArr['data']['rs_num'] = 0;
            }
            $unAuditNum = $returnArr['data']['num'] + $returnArr['data']['rs_num'];
            $return['un_audit_num'] = empty($unAuditNum) ? '0' : (string)$unAuditNum;
            $return['un_only_audit_num'] = (string)$returnArr['data']['num'] ?? 0;

            $ticketServer = new TicketServer($this->lang, $this->timezone, $userinfo);
            $ticketServer = Tools::reBuildCountryInstance($ticketServer, [$this->lang, $this->timezone,$userinfo]);
            //IT工单数量
            $ticket = $ticketServer->getNums();
            if($ticket['code']==1){
                $return['un_audit_num'] = bcadd($return['un_audit_num'],$ticket['data']['reply_num']);
                $return['un_audit_num'] = bcadd($return['un_audit_num'],$ticket['data']['audit_num']);
            }
            $wait_probation_num = (new ProbationServer($this->lang,$this->timezone))->getProbationNum($paramIn['staff_id']);
            $return['un_audit_num'] = bcadd($return['un_audit_num'],$wait_probation_num);
            //我的面试-未处理
            $interview_num = (new InterviewRepository($this->timezone))->myCountInterview($paramIn);

            $return['un_audit_num'] = bcadd($return['un_audit_num'],$interview_num['data']['num']);

            if ($source == PushEnums::PUSH_OPT_SOURCE_SVC) {
                //OA审核数量
                $oaCommonServer = new OACommonServer();
                $oaFolderNum = $oaCommonServer->getOaFolderNum([
                    'staff_info_id' => $paramIn['staff_id'],
                    'job_title'     => $staffInfo['job_title'],
                    'formal'        => $staffInfo['formal'],
                    'src'           => PushEnums::OA_REDDOT_SRC_PUSH,
                ]);

                $this->getDI()->get('logger')->write_log('oaFolderNumTask:' . $oaFolderNum . ' staff_info_id:' . $paramIn['staff_id'], 'info');
                $return['un_audit_num'] = bcadd($return['un_audit_num'],$oaFolderNum);

                // 报销申请待办红点
                $reimbursementApplyNum = $oaCommonServer->getReimbursementApplyNum([
                    'staff_info_id' => $paramIn['staff_id'],
                    'formal'        => $staffInfo['formal'] ?? 0,
                    'hire_type'     => $staffInfo['hire_type'] ?? 0,
                    'src'           => PushEnums::OA_REDDOT_SRC_PUSH,
                ]);

                $this->logger->write_log('reimbursementApplyNum: ' . $reimbursementApplyNum . ' staff_info_id:' . $paramIn['staff_id'], 'info');
                $return['un_audit_num'] = bcadd($return['un_audit_num'], $reimbursementApplyNum);
            }

            // TH flash box 投诉跟进
            $followNum = 0;
            if(isCountry('TH')) {
                $followData = (new CeoMailServer($this->lang, $this->timezone))->getAuditNum($paramIn['staff_id']);
                $followNum = $followData['num'];
            }

            $return['un_audit_num'] = bcadd($return['un_audit_num'], $followNum);

            //试用期目标红点数
            $probationTargetNum = 0;
            if(isCountry(['TH','PH','MY'])) {
                $targetCountInfo = (new ProbationTargetServer($this->lang, $this->timezone))->redCount(['user_id' => $paramIn['staff_id']]); //推送展示
                $probationTargetNum = $targetCountInfo['setting_start_count'] ?? 0;
            }

            $return['un_audit_num'] = bcadd($return['un_audit_num'], $probationTargetNum);

            return $return ?? [];
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('backyard: 获取已读消息mns发送日志: 异常原因可能是 - ' . $e->getMessage(), 'error');
            throw $e;
        }
        return [];

    }



    /**
     * 获取红点数量，
     * 现状：除泰国重写，其他国家共用此方法，如果需要定制可重写方法，参考泰国。
     * @param $req
     * @return string[]
     */
    public function getRedDotsNum($req){
        //CEO回复未读 & 消息未读
        $unReadMsgRes = $this->un_read(['staff_id' => $req['staff_id']]);
        $ceoUnReadNum = $unReadMsgRes['ceo_unread'] ?? 0;
        //审批
        if (isCountry('MY') && in_array(RolesModel::SALES_AGENT, $req['userinfo']['positions'])) {
            return [
                'un_read_num'             => '0',                                       // 我的-CEO信箱
                'backyard_unread_num'     => (string)($unReadMsgRes['msg_unread'] ?? 0),// 消息tab
                'un_audit_num'            => '0',                                       // 审批tab
                "un_only_audit_num"       => '0',                                       //首页弹出提示
                "un_myinfo_count"         => '0',                                       // 我的-个人信息
                "un_asset_management_num" => '0',                                       //我的-资产管理
                "un_myabout_all"          => '0',                                       //我的tab （总和）
            ];
        }
        $unAuditNumRes = $this->getWaitAuditData($req);
        //我的
        if($this->redCountCompareMobileVersion()) {
            $tool_server = Tools::reBuildCountryInstance(new ToolServer($this->lang, $this->timezone),[$this->lang, $this->timezone]);
            $myinfoCount = $tool_server->getPersonalInformationRedCount($req['staff_id']);
        } else {
            $myinfoCount = $this->getMyInfoCount($req['staff_id']);
        }

        //资产管理小红点
        $unAssetManagementNum = $this->getAssetManagementNum($req['staff_id']);
        $return = [
            'un_read_num' => (string)$ceoUnReadNum,// 我的-CEO信箱
            'backyard_unread_num' => (string)($unReadMsgRes['msg_unread'] ?? 0),// 消息tab
            'un_audit_num' => (string)($unAuditNumRes['un_audit_num'] ?? 0),// 审批tab
            "un_only_audit_num" => (string)($unAuditNumRes['waitAuditNum'] ?? 0),//首页弹出提示
            "un_myinfo_count" => (string)$myinfoCount,// 我的-个人信息
            "un_asset_management_num" => (string)$unAssetManagementNum,//我的-资产管理
            "un_myabout_all" => (string)($ceoUnReadNum + $myinfoCount + $unAssetManagementNum),//我的tab （总和）
        ];
        return $return;
    }

    /**
     * @deprecated
     * 个人信息菜单角标数量
     * @param $staffId
     * @return int
     */
    public function getMyInfoCount($staffId){
        $myinfoCount = 1;

        $annexRet    = HrStaffAnnexInfoModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => ['staff_info_id' => $staffId],
        ])->toArray();
        $annexList   = array_column($annexRet, null, 'type');

        if (!empty($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]) && in_array($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['audit_state'],[HrStaffAnnexInfoModel::AUDIT_STATE_NOT_REVIEWED,HrStaffAnnexInfoModel::AUDIT_STATE_PASSED])) {
            $myinfoCount = 0;
        }

        //个人信息菜单数量提醒 增加 马来tp3待填写提醒数量
        if(isCountry('MY')) {
            $tp3_state_can_submit = (new HrStaffTp3Server($this->lang, $this->timezone))->_validateStateIsCanSubmit($staffId);
            if($tp3_state_can_submit){
                $myinfoCount++;
            }
        }

        // th 银行卡审核状态
        if (isCountry(['TH','MY'])) {
            $hr_staff_info = HrStaffInfoModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind' => [
                    'staff_info_id'  => $staffId,
                ],
                'columns' => 'bank_no,hire_type',
            ]);

            // 银行卡号为空 或者 审核被拒绝 都需要有数字1的提示
            if (
                empty($hr_staff_info->bank_no) ||
                empty($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['annex_path_front']) ||
                (!empty($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]) && $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT)
            ){
                if (isCountry('MY') && ($hr_staff_info->hire_type != HrStaffInfoModel::HIRE_TYPE_5)){
                    $myinfoCount++;
                }elseif (isCountry('TH')){
                    $myinfoCount++;
                }
            }
        }

        // 户口簿 有一张没上传 则视为 待上传。
        if (isCountry() && (empty($annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD])
                || is_null($annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['audit_state'])
                || !in_array($annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['audit_state'],[HrStaffAnnexInfoModel::AUDIT_STATE_NOT_REVIEWED,HrStaffAnnexInfoModel::AUDIT_STATE_PASSED]))
        ) {
            $myinfoCount++;
        }


        return $myinfoCount;
    }

    /**
     * 获取公共资产
     * @param $staffId
     * @return int
     */
    public function getPubAssetNum($staffId){
        $pubAssetNum = 0;
        $staffModel = new StaffRepository($this->lang);
        $staffInfo = $staffModel->getStaffPosition($staffId ?? '');
        if ($staffInfo) {
            $assetServer = new AssetServer($this->lang, $this->timezone);
            $pubAssetNum = $assetServer->getTransferNum($staffId, AssetServer::SOURCE_TYPE_PUBLIC_ASSETS);
            //公共资产->公共资产转交总部被拒绝数量
            $refuseRes = $assetServer->getRefuseNum( AssetServer::SOURCE_TYPE_PUBLIC_ASSETS, $staffId);
            $get_refuse_num = $refuseRes['total'] ?? 0;
            $pubAssetNum += $get_refuse_num;
        }
        return $pubAssetNum;
    }

    /**
     * 个人资产红点数=待接收个人资产数量+个人资产转交总部被拒绝数量
     * @param $staffId
     * @return int
     */
    public function getMyAssetNum($staffId){
        $assetServer = new AssetServer($this->lang, $this->timezone);
        //待接收个人资产数量
        $transferNum = $assetServer->getTransferNum($staffId);
        //个人资产转交总部被拒绝数量
        $refuseNumRes = $assetServer->getRefuseNum( '', $staffId);
        $refuseNum = $refuseNumRes['total'] ?? 0;
        $total = $refuseNum + $transferNum;
        return $total;
    }

    /**
     * 资产管理红点数=待接收个人资产数量+个人资产转交总部被拒绝数量
     * @param $staff_id
     * @return int
     */
    public function getAssetManagementNum($staff_id){
        $asset_server = new MaterialAssetServer($this->lang, $this->timezone);
        //待接收资产数量
        $transfer_num = $asset_server->setExpire(300)->getToBeReceiverFromCache([], ['id' => $staff_id]);
        return $transfer_num;
    }

    /**
     * 获取待审批数量
     * @param $staffId
     * @param $jobTitle
     * @return array
     */
    public function getWaitAuditData($paramIn){
        $jobTitle = $paramIn['userinfo']['job_title'];
        $staffId = $paramIn['staff_id'];

        //待审批的数量 & 离职资产待确认数
        //开启待审批数开关
        if (env('enable_audit_num', 1) == 1) {
            $returnArr      = (new AuditServer($this->lang, $this->timezone))->waitAuditNum(['staff_id' => $staffId]);
            $waitAuditNum   = $returnArr['data']['num'] ?? 0;
            $resignAssetNum = $returnArr['data']['rs_num'] ?? 0;
        } else {
            $waitAuditNum = $resignAssetNum = 0;
        }

        //IT工单数量 & 转交工单
        $ticketServer = new TicketServer($this->lang, $this->timezone, $paramIn['userinfo']);
        $ticketServer = Tools::reBuildCountryInstance($ticketServer, [$this->lang, $this->timezone,$paramIn['userinfo']]);
        $ticket = $ticketServer->getNums($paramIn['userinfo']);
        $ticketReplyNum = $ticket['data']['reply_num'] ?? 0;
        $ticketAuditNum = $ticket['data']['audit_num'] ?? 0;

        $totalXzNums = 0;
        if (isCountry('TH') || isCountry('PH') || isCountry('MY') || isCountry('VN') || isCountry('ID')) {
            // 行政工单
            $administrationSever = new AdministrationOrderServer($this->lang);
            $administrationSever = Tools::reBuildCountryInstance($administrationSever, [$this->lang, $this->timezone]);
            $xzData = $administrationSever->getXzNums($paramIn['userinfo']);
            $ticket['data'] = array_merge($ticket['data'], $xzData);
            $totalXzNums = $xzData['xz_reply_num'] + $xzData['xz_submit_num'];
        }

        //资产工单-待办总数
        $totalAssetWorkOrderNum = 0;
        if (isCountry('TH')) {
            //资产工单
            $assetWorkOrderServer = new AssetWorkOrderServer();
            $assetWorkOrderServer = Tools::reBuildCountryInstance($assetWorkOrderServer, [$this->lang, $this->timezone]);
            $replyAssetWorkOrderData = $assetWorkOrderServer->getAssetWorkOrderData($paramIn['userinfo']);
            $ticket['data'] = array_merge($ticket['data'], $replyAssetWorkOrderData);
            $totalAssetWorkOrderNum = $replyAssetWorkOrderData['asset_work_order_reply_num'] + $replyAssetWorkOrderData['asset_work_order_submit_num'];
        }

        //转正评估待审批的数量
        $probationRes = (new ProbationServer($this->lang,$this->timezone))->getAuditNum($staffId);
        $probationNum = $probationRes['data']['num'] ?? 0;

        //我的面试
        $interviewCountRes = (new InterviewRepository($this->timezone))->myCountInterview(['staff_id' => $staffId]);
        $interviewCount = $interviewCountRes['data']['num'] ?? 0;

        //我的简历筛选
        $resumeFilter = (new ResumeServer($this->timezone))->getStaffResumeFilterNum($staffId);
        $waitFilterResumeNum = $resumeFilter['num'];

        //ph推荐简历待提交和驳回数量
        $commitmentNum = 0;
        $followData = $commitmentRes = $transferInfo = ['is_show' => 0, 'num' => 0];
        if(isCountry('PH') || isCountry('MY') || isCountry('TH')) {
            $commitmentRes = (new ResumeRecommendRepository())->getShowTotal(['staff_id' => $staffId, 'job_title' => $jobTitle]);
            if (!empty($commitmentRes['is_show']) && $commitmentRes['num']) {
                $commitmentNum = $commitmentRes['num'];
            }
        }
        // 资产盘点～盘点单～未完成的盘点任务数
        $taskCountRet = (new MaterialInventoryCheckServer())->getTaskCount(['staff_id' => $staffId]);
        $taskCount = (isset($taskCountRet['data']) && $taskCountRet['data']) ? $taskCountRet['data'] : 0;

        //审批-人事-KPI目标
        $kpiCount = (new KpiServer())->getKpiWaitConfirmCount($staffId);

        //盘点
        $planWmsTaskCount = $this->setExpire(300)->planWmsTaskCountMsgFromCache($staffId);

        //海康人脸 未上传数量。
        $hik_not_face_count = 0;
        $StaffHikServer = new StaffHikServer($this->lang, $this->timezone);
        //有入口权限的再去查。
        $hikFaceCheck = $StaffHikServer->getHikInputPermission($paramIn);
        if($hikFaceCheck) {
            $paramIn['is_sync'] = StaffHikvisionModel::SYNC_NO;
            $hik_not_face_count = $StaffHikServer->getStaffList($paramIn, true)['total'];
            unset($paramIn['is_sync']);
        }

        $staffInfo = (new HrStaffInfoModel())->getOneByStaffId($staffId);

        //转岗确认数
        $transferConfirmNum = 0;
        if (isCountry(['TH', 'MY', 'PH'])) {
            $transferInfo       = (new JobTransferConfirmServer($this->lang, $this->timezone))->getConfirmInfo($staffInfo);
            $transferConfirmNum = $transferInfo['num'];
        }

        //OA审核数量
        $oaCommonServer = new OACommonServer();
        $oaFolderNum = $oaCommonServer->getOaFolderNum([
            'staff_info_id' => $staffId,
            'job_title'     => $staffInfo['job_title'] ?? 0,
            'formal'        => $staffInfo['formal'] ?? 0,
            'src'           => PushEnums::OA_REDDOT_PUSH_SRC_APP,
        ]);

        // 报销申请待办红点
        $reimbursementApplyNum = $oaCommonServer->getReimbursementApplyNum([
            'staff_info_id' => $staffId,
            'formal'        => $staffInfo['formal'] ?? 0,
            'hire_type'     => $staffInfo['hire_type'] ?? 0,
            'src'           => PushEnums::OA_REDDOT_PUSH_SRC_APP,
        ]);

        //病假资料审批待补充数量
        $sickCertificateNum = 0;
        if(isCountry('TH')){
            $sickServer = new SickServer($this->lang, $this->timezone);
            $sickCertificateNum = $sickServer->getSickCertificateUploadNum($staffId);
        }

        //Flash box 投诉跟进 红点
        $followNum = 0;
        if(isCountry('TH')) {
            $followData = (new CeoMailServer($this->lang, $this->timezone))->getAuditNum($staffId);
            $followNum = $followData['num'];
        }
        $vehicleCompanyInfoNum = 0;
        //租车 完善车辆信息
        if (isCountry(['MY','PH'])) {
            $vehicleCompanyInfoServer = new VehicleCompanyInfoServer($this->lang, $this->timezone);
            $vehicleCompanyInfo       = $vehicleCompanyInfoServer->getWaitDealNum($paramIn['userinfo']);
            $vehicleCompanyInfoNum    = $vehicleCompanyInfo['num'];
        }

        //试用期目标红点数
        $probationTargetNum = 0;
        if (isCountry(['TH','PH','MY'])) {
            $targetCountInfo = (new ProbationTargetServer($this->lang, $this->timezone))->redCount(['user_id' => $staffId]);//审核页展示
            $probationTargetNum = intval($targetCountInfo['setting_start_count'] ?? 0);
        }

        //交车&验车
        $vehicleDeliveryAndInspectionNum = 0;
        if (isCountry(['MY', 'PH'])) {
            $vehicleDeliveryAndInspectionNum = (new VehicleCompanyInfoServer($this->lang, $this->timezone))->getDeliveryAndInspectionWaitDealNum($paramIn['userinfo']);
        }

        //耗材调拨小红点数
        $packageAllotNum = $this->packageAllotNum($staffInfo);

        //总和
        $total_num = $waitAuditNum
            + $resignAssetNum
            + $followNum
            + $ticketReplyNum
            + $ticketAuditNum
            + $probationNum
            + $interviewCount
            + $waitFilterResumeNum
            + $commitmentNum
            + $taskCount
            + $totalXzNums
            + $totalAssetWorkOrderNum
            + $kpiCount
            + $planWmsTaskCount
            + $hik_not_face_count
            + $transferConfirmNum
            + $oaFolderNum
            + $reimbursementApplyNum
            + $vehicleCompanyInfoNum
            + $sickCertificateNum
            + $probationTargetNum
            + $packageAllotNum
            + $vehicleDeliveryAndInspectionNum;

        return [
            'follow_data'                     => $followData,
            'ticket_data'                     => $ticket['data'],
            'interview_data'                  => $interviewCountRes['data'],
            'resume_filter_data'              => $resumeFilter,
            'commitment_data'                 => $commitmentRes,
            'wait_audit_data'                 => $returnArr['data'],
            'probation_data'                  => $probationRes['data'],
            'waitAuditNum'                    => $waitAuditNum,
            'inventory_check_task_count'      => $taskCount,
            'kpi_count'                       => $kpiCount,
            'hik_not_face_count'              => $hik_not_face_count,
            'transfer_data'                   => $transferInfo,
            'oa_folder_num'                   => $oaFolderNum,
            'reimbursement_apply_num'         => $reimbursementApplyNum,
            'sick_certificate'                => $sickCertificateNum,
            'vehicle_company_info'            => $vehicleCompanyInfo ?? [],
            'probation_target_num'            => $probationTargetNum,
            'package_allot_num'               => $packageAllotNum,//耗材调拨小红点数
            'vehicle_delivery_and_inspection' => $vehicleDeliveryAndInspectionNum,//交车&验车
            'un_audit_num'                    => $total_num,                      // 总和
        ];
    }


    /**
    * 获取物品盘点的任务数量
    * @Date: 2022-05-31 18:12
    * @author: peak pan
    * @return:
    **/
    public function planWmsTaskCountMsg($staff_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialWmsPlantaskModel::class]);
        $builder->where('main.plan_manager_id = :plan_manager_id: and main.is_deleted = :is_deleted: and main.end_time > :end_time: and  status in (1,2) and is_task = :is_task:', ['plan_manager_id' => $staff_id, 'is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'end_time' => date('Y-m-d H:i:s', time()), 'is_task' => InventoryCheckEnums::PLAN_IS_TASK_YES]);
        $totalCount = $builder->columns('COUNT(1) AS total')->getQuery()->execute()->getFirst();
        return intval($totalCount->total);
    }

    /**
     * 获取消息详情
     * @param $params
     * @return array
     */
    public function getMessageCourierDetailV2($params):array
    {
        $msg_id   = $params['msg_id'];
        $staff_id = $params['staff_info_id'];
        if (empty($msg_id) || empty($staff_id)) {
            return [];
        }

        $sql = "select mc.id as msg_id,mc.title,mc.read_state
                       , m.message content
                       , mc.staff_info_id
                       , mc.category
                       , mc.message_content_id   
                       , mc.category_code
                       , m.related_id   
                from message_courier as mc 
                inner join  message_content as m on mc.message_content_id = m.id
                where mc.id = :msg_id and mc.staff_info_id = :staff_info_id  and mc.is_del = 0
                ";
        $query_param['msg_id']        = $msg_id;
        $query_param['staff_info_id'] = $staff_id;
        $data                         = $this->getDI()->get('db_coupon_r')->query($sql,
            $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data ?: [];
    }


    /**
     * 获取消息详情
     * @param $param
     * @return array
     */
    public function getMessageCourierDetail($param)
    {
        $msg_id   = $param['msg_id'];
        $staff_id = $param['staff_info_id'];
        if (empty($msg_id) || empty($staff_id)) {
            return [];
        }
        $sql = "select mc.id as msg_id,mc.title,mc.read_state
                       , m.message content
                       , mc.staff_info_id
                       , mc.category
                       , mc.message_content_id   
                       , mc.category_code
                       , m.related_id   
                from message_courier as mc 
                inner join  message_content as m on mc.message_content_id = m.id
                where mc.id = '{$msg_id}'  and mc.is_del = 0
                ";

        $data = $this->getDI()->get('db_coupon_r')->query($sql);
        $data = $data->fetch(\Phalcon\Db::FETCH_ASSOC);

        if(empty($data)){
            return $this->checkReturn(array('data' => $data));
        }
        if (!empty($data) && ((is_array($staff_id) && !in_array($data['staff_info_id'],$staff_id)) || (!is_array($staff_id) && $data['staff_info_id'] != $staff_id))) {
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        return $data;
    }


    /**
     * @description 获取消息详情
     * @return array
     * @throws BusinessException
     */
    public function getMessageDetailV2($params = []): array
    {
        //获取消息详情
        $msgServer = new MessageFactoryServer($this->lang, $this->timezone);
        $message = (new MessageDataServer($this->lang))->init($params);
        if (!is_numeric($message->getMessageResponse()->getCategory()) && empty($message->getMessageResponse()->getCategory())) {
            $this->has_read_operation($params['msg_id'],true);
            return [];
        }
        return $msgServer->getMessageDetail($message);
    }

    /**
     * 处理警告书消息详情
     * @param $data
     * @param $warning_info
     * @param $msg_id
     * @param $link
     * @return mixed
     */
    public function warningMessageDetail($data, $warning_info, $msg_id, $link)
    {
        if (isCountry('MY') && $data['category'] == MessageEnums::MESSAGE_CATEGORY_76 ) {
            return $data;
        }
        if ($warning_info['role'] == MessageServer::MSG_STAFF_TYPE_STAFF && !$warning_info['img_url']) {
            $url             = env('sign_url')."/#/eleWarnState1";
            $data['content'] .= "<a href='{$url}?msg_id={$msg_id}' style='display: inline-block; cursor: pointer; margin-left: 10px;font-size: 15px;'>{$link}</a>";
        } elseif ($warning_info['role'] == MessageServer::MSG_STAFF_TYPE_SUPERIOR && !$warning_info['superior_img']) {
            if ($warning_info['img_url'] || date("Y-m-d",
                    strtotime($warning_info['created_at']." + 3 days")) > date("Y-m-d")) {
                // 已签字 或者 消息下发时间在三天内
                $url             = env('sign_url')."/#/eleWarnState2";
                $data['content'] .= "<a href='{$url}?msg_id={$msg_id}' style='display: inline-block; cursor: pointer; margin-left: 10px;font-size: 15px;'>{$link}</a>";
            } else {
                $jobTile         = $this->getDI()->get("db_rbi")->fetchOne("select hr_job_title.* from hr_job_title left join hr_staff_info on hr_staff_info.job_title = hr_job_title.id where hr_staff_info.staff_info_id = ".$warning_info['superior_id'],
                    \Phalcon\Db::FETCH_ASSOC);
                $jobTile         = ($jobTile && isset($jobTile['job_name'])) ? $jobTile['job_name'] : '';
                $url             = env('sign_url')."/#/eleWarnState4?name=".$warning_info['superior_name']."&job_name=".$jobTile."&day=".date("Y-m-d");
                $data['content'] .= "<p style='color: #ff0000'>".$this->getTranslation()->_('warning_more_3days')."</p>";
                $data['content'] .= "<a href='{$url}&msg_id={$msg_id}' style='display: inline-block; cursor: pointer; margin-left: 10px;font-size: 15px;'>{$link}</a>";
                $url             = $warning_info['img_url'];
                $data['content'] .= "<div style='width: 100%; text-align: right;'><img src='{$url}' /></div>";
            }
        } elseif (
            $warning_info['role'] == MessageServer::MSG_STAFF_TYPE_WITNESS_1 && !$warning_info['witness1_img']
            ||
            $warning_info['role'] == MessageServer::MSG_STAFF_TYPE_WITNESS_2 && !$warning_info['witness2_img']
        ) {
            $url             = env('sign_url')."/#/eleWarnState3";
            $data['content'] .= "<a href='{$url}?msg_id={$msg_id}' style='display: inline-block; cursor: pointer; margin-left: 10px;font-size: 15px;'>{$link}</a>";
        }
        return $data;
    }

    /**
     * @description 获取
     * @param $data
     * @return string
     */
    public function getSignMessageUrl($data): string
    {
        $categoryCode = $data['category_code'] ?? 0;
        switch ($categoryCode) {
            case MessageEnums::MESSAGE_CATEGORY_CODE_LATE:
            case MessageEnums::MESSAGE_CATEGORY_CODE_EARLY:
            case MessageEnums::MESSAGE_CATEGORY_CODE_ABSENT:
                $url = sprintf("%s/#/punish?msg_id=%s", env('sign_url'), $data['msg_id']);
                break;
            case MessageEnums::CATEGORY_SIGN_CODE_PUNISH:
                $url = sprintf("%s/#/staff-penalty-notice?msg_id=%s", env('sign_url'), $data['msg_id']);
                break;
            default:
                $url = sprintf("%s/#/signNewsInfo?msg_id=%s", env('sign_url'), $data['msg_id']);
                break;
        }
        return $url;
    }

    /**
     * 红点计数比较版本号
     * @return false|mixed
     */
    public function redCountCompareMobileVersion() {
        $equipment_info = [];
        $set_model = SettingEnvModel::findFirst("code = 'red_count_equipment_info_config' ");
        if (!empty($set_model) && $set_model->set_val) {
            $equipment_info = json_decode($set_model->set_val, true);
        }
        if(!$equipment_info) {
            return false;
        }

        return MobileHelper::compareVersion($equipment_info);
    }

    public function sendDsheetMQ($param)
    {
        $body['msg_id']        = $param['msg_id'];
        $body['sign_time']     = time();
        $body['staff_info_id'] = intval($param['staff_info_id']);

        //通知fbi 发送 mobile dc pdf
        $sendData['jsonCondition']    = json_encode($body);
        $sendData['handleType']       = RocketMQ::TAG_SIGN_MOBILE_DC;
        $sendData['shardingOrderKey'] = $param['staff_info_id'];
        $rmq                          = new RocketMQ('message_sign_dsheet');
        $rid                          = $rmq->sendOrderlyMsg($sendData);//有序
        $this->logger->write_log('sign_dsheet '.$rid.'data:'.json_encode($sendData), $rid ? 'info' : 'error');
        return true;
    }

    /**
     * 阅读时长的普通消息-置已读
     * @param $params
     * @return bool
     */
    public function readMsgNormal($params)
    {
        if (empty($params['msg_id'])) {
            return false;
        }
        //查看支援信息
        //如果是支援账号，则找到其 主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($params['user_id']);
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            $params['user_id'] = $supportStaffInfo['staff_info_id'];
        }

        $messageInfo = MessageCenterRepository::getMessageCourierInfo(['id' => $params['msg_id'], 'staff_info_id' => $params['user_id'], 'category' => 0]);
        if(!empty($messageInfo) && $messageInfo['read_state'] == MessageCourierModel::READ_STATE_UNREAD) {
            $this->has_read_operation($params['msg_id'],true);
            $this->addHaveReadMsgToMns($params['msg_id']);
        }

        return  true;
    }

    /**
     * 获取耗材调拨小红点数
     * @param array $staff_info 登陆者用户组
     * @return int
     */
    public function packageAllotNum($staff_info)
    {
        $is_has_permission = (new AuditServer($this->lang, $this->timezone))->isShowPackageAllotPermission($staff_info);
        //没有耗材调拨权限,直接返回0
        if (!$is_has_permission) {
            return 0;
        }

        try {
            $oaRpc = (new ApiClient('oa_rpc', '', 'package_allot_wait_handle_num', $this->lang));
            $oaRpc->setParams([
                'staff_id'     => $staff_info['staff_info_id'],
                'sys_store_id' => $staff_info['sys_store_id'],
            ]);
            $res = $oaRpc->execute();

            if (!empty($res['result']) && $res['result']['code'] == ErrCode::SUCCESS) {
                return (int) $res['result']['data'] ?? 0;
            }
        } catch (Exception $exception) {
            $this->logger->write_log('packageAllotNum error ' . $exception->getMessage() .'-'. $exception->getTraceAsString() . $exception->getLine(), 'error');
        }

        return 0;
    }
}
