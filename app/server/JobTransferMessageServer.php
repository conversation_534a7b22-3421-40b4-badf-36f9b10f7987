<?php

namespace FlashExpress\bi\App\Server;


//转岗消息通知服务
use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStoreApplySupportModel;
use FlashExpress\bi\App\Models\backyard\VanContainerModel;
use FlashExpress\bi\App\Server\PushServer;

class JobTransferMessageServer extends BaseServer
{
    const SEND_MESSAGE_TO_SUBMITTER = 1; //给申请人
    const SEND_MESSAGE_TO_TRANSFER = 2; //给被转岗人
    const SEND_MESSAGE_TO_MANAGER = 3; //给上级
    const SEND_MESSAGE_TO_HRBP = 4; //给HrBP
    const SEND_MESSAGE_TO_AGENT_TRANSFER = 5; //给个人代理被转岗人


    private static $instance = null;

    protected function __construct($lang = 'zh-CN', $timezone = '+08:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * @return JobTransferMessageServer
     */
    public static function getInstance(): JobTransferMessageServer
    {
        if (!isset(self::$instance) || self::$instance instanceof self) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 转岗成功提醒
     * 根据指定员工的最后一次登陆语言决定发送语言
     *
     * @return void
     */
    public function noticeTransferSuccess($staff_info_id, $data, $lang, $type = self::SEND_MESSAGE_TO_SUBMITTER)
    {
        switch ($type) {
            case self::SEND_MESSAGE_TO_SUBMITTER:
                $this->sendSucMsgToSubmitter($staff_info_id, $data, $lang);
                break;
            case self::SEND_MESSAGE_TO_TRANSFER:
                $this->sendSucMsgToTransfer($staff_info_id, $data, $lang);
                break;
            case self::SEND_MESSAGE_TO_AGENT_TRANSFER:
                $this->sendSucMsgToAgentTransfer($staff_info_id, $data, $lang);
                break;
            case self::SEND_MESSAGE_TO_MANAGER:
                $this->sendSucMsgToManager($staff_info_id, $data, $lang);
                break;
        }
        $this->logger->write_log(sprintf('send success message to %s', $staff_info_id),'info');
    }

    /**
     * 转岗失败提醒
     * @return void
     */
    public function noticeTransferFail($staff_info_id, $lang, $data, $fail_reason, $type = self::SEND_MESSAGE_TO_SUBMITTER)
    {
        switch ($type) {
            case self::SEND_MESSAGE_TO_SUBMITTER:
                $this->sendFailMsgToSubmitter($staff_info_id, $data, $lang, $fail_reason);
                break;
            case self::SEND_MESSAGE_TO_TRANSFER:
                $this->sendFailMsgToTransfer($staff_info_id, $data, $lang, $fail_reason);
                break;
            case self::SEND_MESSAGE_TO_HRBP:
                $this->sendFailMsgToHrBp($staff_info_id, $data, $lang, $fail_reason);
                break;
            case self::SEND_MESSAGE_TO_AGENT_TRANSFER:
                $this->sendFailMsgToAgentTransfer($staff_info_id, $data, $lang, $fail_reason);
                break;
        }
        $this->logger->write_log(sprintf('send fail message to %s', $staff_info_id), 'info');

    }

    /**
     * 审批完成提醒
     * @return void
     */
    public function noticeAuditFinish($staff_info_id, $lang, $data, $type)
    {
        switch ($type) {
            case JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL:
                $this->sendAuditFinishMsgSpecial($staff_info_id, $data, $lang);
                break;
            default:
                $this->sendAuditFinishMsgNormal($staff_info_id, $data, $lang);
                break;
        }
        $this->logger->write_log(sprintf('send fail message to %s', $staff_info_id), 'info');
    }

    /**
     * 发送转岗成功消息给申请人
     * 内容：您好，您为{工号}{转岗员工姓名}提交的{转岗日期}转岗申请{审批编号}，由{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}的转岗已完成。
     * @param $staff_info_id
     * @param $data
     * @param $lang
     * @return void
     */
    protected function sendSucMsgToSubmitter($staff_info_id, $data, $lang)
    {
        $sendParams = [
            'staff_info_id' => $staff_info_id,
            'title_key'     => 'job_transfer.transfer_success',
            'content_key'   => 'job_transfer.suc_msg_to_sub',
            'language'      => $lang,
            'data'          => [
                'staff_id'                => $data['staff_id'],
                'staff_name'              => $data['staff_name'],
                'after_date'              => $data['after_date'],
                'serial_no'               => $data['serial_no'],
                'current_department_name' => $data['current_department_name'],
                'current_store_name'      => $data['current_store_name'],
                'current_position_name'   => $data['current_position_name'],
                'after_department_name'   => $data['after_department_name'],
                'after_store_name'        => $data['after_store_name'],
                'after_position_name'     => $data['after_position_name'],
            ],
        ];
        $this->pushAndSendMessage($sendParams);
    }

    /**
     * 发送转岗成功消息给被转岗人
     * 内容：您好，您已从{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}，转岗日期为{转岗日期} 。
     * @param $staff_info_id
     * @param $data
     * @param $lang
     * @return void
     */
    protected function sendSucMsgToTransfer($staff_info_id, $data, $lang)
    {
        $sendParams = [
            'staff_info_id' => $staff_info_id,
            'title_key'     => 'job_transfer.transfer_success',
            'content_key'   => 'job_transfer.suc_msg_to_tra',
            'language'      => $lang,
            'data'          => [
                'current_department_name' => $data['current_department_name'],
                'current_store_name'      => $data['current_store_name'],
                'current_position_name'   => $data['current_position_name'],
                'after_department_name'   => $data['after_department_name'],
                'after_store_name'        => $data['after_store_name'],
                'after_position_name'     => $data['after_position_name'],
                'after_date'              => $data['actual_after_date'],
            ],
        ];
        $this->pushAndSendMessage($sendParams);
    }

    /**
     * 发送转岗成功消息给被转岗人
     * 内容：您好，您已从{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}，转岗日期为{转岗日期} 。
     * @param $staff_info_id
     * @param $data
     * @param $lang
     * @return void
     */
    protected function sendSucMsgToAgentTransfer($staff_info_id, $data, $lang)
    {
        $sendParams = [
            'staff_info_id' => $staff_info_id,
            'title_key'     => 'job_transfer.transfer_agent_success',
            'content_key'   => 'job_transfer.suc_msg_to_agent_tra',
            'language'      => $lang,
            'data'          => [
                'current_department_name' => $data['current_department_name'],
                'current_store_name'      => $data['current_store_name'],
                'current_position_name'   => $data['current_position_name'],
                'after_department_name'   => $data['after_department_name'],
                'after_store_name'        => $data['after_store_name'],
                'after_position_name'     => $data['after_position_name'],
                'after_date'              => $data['actual_after_date'],
            ],
        ];
        $this->pushAndSendMessage($sendParams);
    }

    /**
     * 发送转岗成功消息给上级
     * 内容：您好，您的下级{工号}{转岗员工姓名}已从{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}，转岗日期为{转岗日期} 。
     * @param $staff_info_id
     * @param $data
     * @param $lang
     * @return void
     */
    protected function sendSucMsgToManager($staff_info_id, $data, $lang)
    {
        $sendParams = [
            'staff_info_id' => $staff_info_id,
            'title_key'     => 'job_transfer.transfer_success',
            'content_key'   => 'job_transfer.suc_msg_to_mgr',
            'language'      => $lang,
            'data'          => [
                'staff_id'                => $data['staff_id'],
                'staff_name'              => $data['staff_name'],
                'current_department_name' => $data['current_department_name'],
                'current_store_name'      => $data['current_store_name'],
                'current_position_name'   => $data['current_position_name'],
                'after_department_name'   => $data['after_department_name'],
                'after_store_name'        => $data['after_store_name'],
                'after_position_name'     => $data['after_position_name'],
                'after_date'              => $data['actual_after_date'],
            ],
        ];
        $this->pushAndSendMessage($sendParams);
    }

    /**
     * 发送转岗失败消息给申请人
     * 内容：您好，您为{工号}{转岗员工姓名}提交的由{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}的转岗申请，转岗失败，原因为{失败原因}
     * @param $staff_info_id
     * @param $data
     * @param $lang
     * @param $fail_reason
     * @return void
     */
    protected function sendFailMsgToSubmitter($staff_info_id, $data, $lang, $fail_reason)
    {
        $sendParams = [
            'staff_info_id' => $staff_info_id,
            'title_key'     => 'job_transfer.transfer_failure',
            'content_key'   => 'job_transfer.fail_msg_to_sub',
            'language'      => $lang,
            'data'          => [
                'staff_id'                => $data['staff_id'],
                'staff_name'              => $data['staff_name'],
                'after_date'              => $data['after_date'],
                'current_department_name' => $data['current_department_name'],
                'current_store_name'      => $data['current_store_name'],
                'current_position_name'   => $data['current_position_name'],
                'after_department_name'   => $data['after_department_name'],
                'after_store_name'        => $data['after_store_name'],
                'after_position_name'     => $data['after_position_name'],
                'fail_reason'             => $this->getTranslation($lang)->_($fail_reason),
            ],
        ];
        $this->pushAndSendMessage($sendParams);
    }

    /**
     * 发送转岗成功消息给被转岗人
     * 内容：您好，您已从{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}，转岗日期为{转岗日期} 。
     * @param $staff_info_id
     * @param $data
     * @param $lang
     * @param $fail_reason
     * @return void
     */
    protected function sendFailMsgToTransfer($staff_info_id, $data, $lang, $fail_reason)
    {
        $sendParams = [
            'staff_info_id' => $staff_info_id,
            'title_key'     => 'job_transfer.transfer_failure',
            'content_key'   => 'job_transfer.fail_msg_to_tra',
            'language'      => $lang,
            'data'          => [
                'staff_id'                => $data['staff_id'],
                'staff_name'              => $data['staff_name'],
                'after_date'              => $data['after_date'],
                'serial_no'               => $data['serial_no'],
                'current_department_name' => $data['current_department_name'],
                'current_store_name'      => $data['current_store_name'],
                'current_position_name'   => $data['current_position_name'],
                'after_department_name'   => $data['after_department_name'],
                'after_store_name'        => $data['after_store_name'],
                'after_position_name'     => $data['after_position_name'],
                'fail_reason'             => $this->getTranslation($lang)->_($fail_reason),
            ],
        ];
        $this->pushAndSendMessage($sendParams);
    }

    /**
     * 发送转岗成功消息给被转岗人
     * 内容：您好，您已从{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}，转岗日期为{转岗日期} 。
     * @param $staff_info_id
     * @param $data
     * @param $lang
     * @param $fail_reason
     * @return void
     */
    protected function sendFailMsgToAgentTransfer($staff_info_id, $data, $lang, $fail_reason)
    {
        $sendParams = [
            'staff_info_id' => $staff_info_id,
            'title_key'     => 'job_transfer.transfer_agent_failure',
            'content_key'   => 'job_transfer.fail_msg_to_agent_tra',
            'language'      => $lang,
            'data'          => [
                'staff_id'                => $data['staff_id'],
                'staff_name'              => $data['staff_name'],
                'after_date'              => $data['after_date'],
                'serial_no'               => $data['serial_no'],
                'current_department_name' => $data['current_department_name'],
                'current_store_name'      => $data['current_store_name'],
                'current_position_name'   => $data['current_position_name'],
                'after_department_name'   => $data['after_department_name'],
                'after_store_name'        => $data['after_store_name'],
                'after_position_name'     => $data['after_position_name'],
                'fail_reason'             => $this->getTranslation($lang)->_($fail_reason),
            ],
        ];
        $this->pushAndSendMessage($sendParams);
    }

    /**
     * 发送转岗成功消息给HrBP
     * 内容：您好，您已从{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}，转岗日期为{转岗日期} 。
     * @param $staff_info_id
     * @param $data
     * @param $lang
     * @param $fail_reason
     * @return void
     */
    protected function sendFailMsgToHrBp($staff_info_id, $data, $lang, $fail_reason)
    {
        $sendParams = [
            'staff_info_id' => $staff_info_id,
            'title_key'     => 'job_transfer.transfer_failure',
            'content_key'   => 'job_transfer.fail_msg_to_hrbp',
            'language'      => $lang,
            'data'          => [
                'staff_id'                => $data['staff_id'],
                'staff_name'              => $data['staff_name'],
                'after_date'              => $data['after_date'],
                'serial_no'               => $data['serial_no'],
                'current_department_name' => $data['current_department_name'],
                'current_store_name'      => $data['current_store_name'],
                'current_position_name'   => $data['current_position_name'],
                'after_department_name'   => $data['after_department_name'],
                'after_store_name'        => $data['after_store_name'],
                'after_position_name'     => $data['after_position_name'],
                'fail_reason'             => $this->getTranslation($lang)->_($fail_reason),
            ],
        ];
        $this->pushAndSendMessage($sendParams);
    }

    /**
     * push&发送消息
     * @param $params
     * @return void
     */
    protected function pushAndSendMessage($params)
    {
        $sendTo     = $params['staff_info_id'];
        $titleKey   = $params['title_key'];
        $contentKey = $params['content_key'];
        $language   = $params['language'];
        $keyMap     = $params['data'];

        $checkErrorParam = [
            'staff_info_id'   => $sendTo,
            'message_title'   => $this->getTranslation($language)->_($titleKey),
            'message_content' => $this->getTranslation($language)->_($contentKey, $keyMap),
            'path'            => 'message_list',
        ];
        PushServer::getInstance($language, $this->timeZone)->pushAndSendMessageToSubmitter($checkErrorParam, $language);
    }

    protected function sendAuditFinishMsgNormal($staff_info_id, $data, $lang)
    {
        $sendParams = [
            'staff_info_id' => $staff_info_id,
            'title_key'     => 'job_transfer_result',
            'content_key'   => 'job_transfer.audit_finish',
            'language'      => $lang,
            'data'          => [
                'staff_id'                => $data['staff_id'],
                'staff_name'              => $data['staff_name'],
                'after_date'              => $data['after_date'],
                'serial_no'               => $data['serial_no'],
                'current_department_name' => $data['current_department_name'],
                'current_store_name'      => $data['current_store_name'],
                'current_position_name'   => $data['current_position_name'],
                'after_department_name'   => $data['after_department_name'],
                'after_store_name'        => $data['after_store_name'],
                'after_position_name'     => $data['after_position_name'],
                'audit_state'             => $data['audit_state'],
            ],
        ];
        $this->pushAndSendMessage($sendParams);
    }

    protected function sendAuditFinishMsgSpecial($staff_info_id, $data, $lang)
    {
        $sendParams = [
            'staff_info_id' => $staff_info_id,
            'title_key'     => 'job_transfer.audit_result_special',
            'content_key'   => 'job_transfer.finish_special',
            'language'      => $lang,
            'data'          => [
                'created_at'              => $data['created_at'],
                'after_date'              => $data['after_date'],
                'audit_state'             => $data['audit_state'],
            ],
        ];
        $this->pushAndSendMessage($sendParams);
    }

    /**
     * 其他职位转岗到Van Courier[110]职位转岗成功后，提醒员工更新车辆信息
     * @param $staff_info_id
     * @return void
     */
    public function sendUpdateVehicleInfoMessage($staff_info_id)
    {
        if (!isCountry('TH')) {
            return;
        }
        if (empty($staff_info_id)) {
            return;
        }
        //转岗都是主账号
        $isSubmitVanContainerInfo = VanContainerModel::hasSubmitVanContainer($staff_info_id);
        if (!empty($isSubmitVanContainerInfo)) { //如果存在就不再发送了
            return;
        }

        //找出子账号
        $staffInfo[]  = $staff_info_id;
        $subStaffInfo = HrStaffApplySupportStoreModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and support_status = :support_status:',
            'bind'       => [
                'staff_info_id'  => $staff_info_id,
                'support_status' => HrStoreApplySupportModel::SUPPORT_STATUS_EFFICIENT,
            ],
        ]);
        if (!empty($subStaffInfo)) {
            $staffInfo[] = $subStaffInfo->sub_staff_info_id;
        }

        $staffServer = new StaffServer();
        foreach ($staffInfo as $staffInfoId) {
            $lang                        = $staffServer->getLanguage($staffInfoId);
            $id                          = time() . $staffInfoId . rand(1000000, 9999999);
            $param['staff_users']        = [$staffInfoId];
            $param['message_title']      = $this->getTranslation($lang)->_('van_container_complete_msg_title');
            $param['message_content']    = 'type=1';
            $param['staff_info_ids_str'] = $staffInfoId;
            $param['id']                 = $id;
            $param['category']           = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_COMPLETE_VAN_CONTAINER');

            $this->logger->write_log('sendUpdateVehicleInfoMessage param:' . json_encode($param), 'info');
            $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $lang));
            $bi_rpc->setParams($param);
            $res = $bi_rpc->execute();
            $this->getDI()->get('logger')->write_log('sendUpdateVehicleInfoMessage result:' . json_encode($res), 'info');
            if ($res && $res['result']['code'] == 1) {
                $kitId = $res['result']['data'][0];
                $this->logger->write_log('sendUpdateVehicleInfoMessage 写入message成功' . $staffInfoId . " message_id" . $kitId,
                    'info');
            } else {
                $this->logger->write_log('sendUpdateVehicleInfoMessage 写入message失败' . $staffInfoId, 'error');
            }
        }
    }
}