<?php


namespace FlashExpress\bi\App\Server;


use App\Country\Tools;
use FlashExpress\bi\App\Enums\ClockEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\AttendanceWhiteListModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffRenewContractApplyModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Server\KpiServer;

class CheckPunchOutServer extends BaseServer
{

    const ALL_DAY_SHIFT_DURATION = 9;
    const HALF_DAY_SHIFT_DURATION = 4;
    public $timezone;
    public $lang;

    public function __construct($lang, $timezone)
    {
        $this->timezone = $timezone;
        $this->lang     = $lang;
        parent::__construct($lang);
    }


    //java的key   => 客户端需要的详情的key
    const JAVA_TASK_CONFIG = [
        //未回公款【kit by都在返回】
        'un_remittance'           => 'remittance_detail',
        //未完成的揽件任务  kit 要跳转  by要提示去kit 【kit by都在返回】
        'un_finished_task'        => 'task_detail',
        //未完成的派件任务
        'un_finished_assign_task' => 'task_detail',
        //未结束的计数批次 kit by都是提示 【kit by都没有返回】目前用的未回款的
        'un_count_batch'          => 'remittance_detail',
        //派送任务未关闭 kit 要跳转  by要提示去kit 【kit 返回】
        'un_close_dispatch_task'  => 'task_detail',
        //网点回访任务
        'store_revisit'           => 'task_detail',
        'ic_parcel_num'       => 'ic_parcel_num',//个人代理没完成的数据 https://flashexpress.feishu.cn/wiki/Dk1ZwCAbTiFuB2klLV1cage4neg
    ];

    //客户端需要的详情的key
    const FBI_TASK_CONFIG = [
        //staff checklist
        'un_remittance'          => 'remittance_detail',
    ];


    protected $staffInfo          = [];
    protected $platform           = 'backyard';
    protected $business_type      = '';//业务类型
    protected $init_business_type = '';//原本的业务类型
    protected $dialog_status      = 0; //是否弹窗
    protected $is_ces_tra         = 0; //是否ces培训
    protected $dialog_must_status = 0; //是否可以跳过 0 能跳过 1不能跳过
    protected $dialog_msg         = '';//弹窗类型
    protected $dialog_data        = null;//定制数据 需要前端 画页面


    protected function build_data(): array
    {
        if ($this->dialog_status == 0) {
            return [];
        }

        return [
            'business_type'                              => $this->business_type,
            self::JAVA_TASK_CONFIG[$this->business_type] => [
                'dialog_status'      => $this->dialog_status,//0不弹，1弹
                'dialog_msg'         => $this->dialog_msg,
                'dialog_must_status' => $this->dialog_must_status,// 0 能跳过 1不能跳过
                'is_ces_tra'         => $this->is_ces_tra,
                'init_business_type' => $this->init_business_type,
                'dialog_data'        => $this->dialog_data,//定制数据 弹窗显示用
            ],
        ];
    }


    protected function punch_out_check_java(): CheckPunchOutServer
    {
        if ((new AttendanceServer($this->lang,
            $this->timezone))->isSeniorBranchSupervisorVirtualId($this->staffInfo['id'])) {
            $this->business_type      = 'un_remittance';
            $this->dialog_must_status = 1;                                                           //不能跳过
            $this->dialog_status      = 1;                                                           //要展示弹窗
            $this->dialog_msg         = $this->getTranslation()->_('virtual_staff_id_error_01');     //有提示 拼在一起
            return $this;
        }

        //获取该员工 该天考勤信息
        if (isCountry('MY')) {
            $server = new \FlashExpress\bi\App\Modules\My\Server\AttendanceServer($this->lang, $this->timezone);
            $attendanceInfo = $server->newAttendanceInfo([
                'user_info' => $this->staffInfo,
                'platform'  => enums::RB_KIT,
            ]);
        } else {
            $attendanceServer = (new AttendanceServer($this->lang, $this->timezone));
            $attendanceInfo   = $attendanceServer->attendance_info(['user_info' => $this->staffInfo]);
        }

        //如果是个人代理 && 泰国 有个优先接口 https://flashexpress.feishu.cn/wiki/Dk1ZwCAbTiFuB2klLV1cage4neg
        if(isCountry('TH') && in_array($this->staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            $ic_res = $this->check_ic_from_java($this->staffInfo['id'], $attendanceInfo['attendance_date']);
        }
        if(!empty($ic_res) && (!empty($ic_res['num']) || !empty($ic_res['abnormalNum']))){
            $this->business_type      = 'ic_parcel_num';
            $this->dialog_must_status = 1;                                                           //不能跳过
            $this->dialog_status      = 1;                                                           //要展示弹窗
            $this->dialog_msg         = $this->getTranslation()->_('attendance_punch_ic_notice',['delivery' => $ic_res['num'],'not_in' => $ic_res['abnormalNum']]);
            $this->dialog_data        = $ic_res;
            return $this;
        }


        $shiftServer = new HrShiftServer();
        $shiftServer = Tools::reBuildCountryInstance($shiftServer);
        $shiftInfo   = $shiftServer->getShiftInfos($this->staffInfo['id'], [$attendanceInfo['attendance_date']]);
        $shift_start = $shiftInfo[$attendanceInfo['attendance_date']]['start'] ?? '09:00';
        $shift_end   = $shiftInfo[$attendanceInfo['attendance_date']]['end'] ?? '18:00';

        $shiftStartTime  = date('Y-m-d H:i:s',strtotime($attendanceInfo['attendance_date'] . ' '.$shift_start));
        $shiftEndTime  = date('Y-m-d H:i:s',strtotime($attendanceInfo['attendance_date'] . ' '.$shift_end));
        if($shift_start > $shift_end){
            $shiftEndTime  = date('Y-m-d H:i:s',strtotime($attendanceInfo['attendance_date'] . ' '.$shift_end)+86400);
        }
        //请求参数
        $params= ['staff_info_id' => $this->staffInfo['id'],'platform'=>$this->platform,'attendance_date'=>$attendanceInfo['attendance_date'],'shift_start'=>$shiftStartTime,'shift_end'=>$shiftEndTime];
        $fle_rpc = new ApiClient('fle', 'com.flashexpress.fle.svc.api.StaffInfoSvc', 'offDuty', $this->lang);
        $fle_rpc->setParams($params);
        $fle_return = $fle_rpc->execute();
        $this->logger->write_log('punch_out_check_java offDuty staff_id:'.$this->staffInfo['id'].' java return:'.json_encode($fle_return,
                JSON_UNESCAPED_UNICODE).' staff_info:'.json_encode($params, JSON_UNESCAPED_UNICODE),
            'info');
        if ($fle_return['code'] == 1) {
            $data = $fle_return['result'];
            //本业务不验证，可以下班打卡
            if ($data['off_duty_enabled'] == true) {
                //没有提示，直接流转到下一个流程
                if (empty($data['notes'])) {
                    return $this;
                }

                $msg = '';
                $i = 0;
                foreach ($data['notes'] as $note) {
                    $i++;
                    $msg .= $i . '.' . $note['message'] . " \r\n";
                }
                $this->dialog_status      = 1;//要展示弹窗
                $this->dialog_msg         = $msg;
                $this->dialog_must_status = 0;              //可以跳过
                $this->business_type      = 'un_remittance';//可以跳过都用未回款的
                return $this;
            }


            //不能下班 必然是有原因的
            if (!empty($data['notes'])) {
                $msg                  = [];
                $change_business_type = false;
                //可以下班
                $flag = true;
                $i    = 0;
                foreach ($data['notes'] as $key => $note) {

                    if(empty($this->business_type)){
                        $this->business_type = in_array($note['business_type'],
                            array_keys(self::JAVA_TASK_CONFIG)) ? $note['business_type'] : 'un_remittance';
                        $this->init_business_type = $this->business_type ;
                    }

                    $flag = $note['sub_off_duty_enabled'] && $flag;
                    $i++;
                    $note['message'] = $i.'.'.$note['message'];
                    //不是kit 就要换提示，去kit操作
                    switch ($note['business_type']) {
                        case 'un_finished_task'://kit by都有
                            $msg[] = $note['message'];
                            break;
                        case 'un_finished_assign_task'://kit by都有
                            $msg[] = $note['message'];
                            $this->business_type = 'un_finished_task';
                            break;
                        case 'un_close_dispatch_task'://仅kit有
                            //如果是by 要切换一下类型 TODO 这里一定要处理
                            $this->business_type = 'un_finished_task';
                            $msg[]                = $note['message'];
                            break;
                        case 'store_un_remittance':
                        case 'un_count_batch':
                            $change_business_type = 'un_remittance';
                            $msg[]                = $note['message'];
                            break;
                        default:
                            $msg[] = $note['message'];
                            break;
                    }
                }

                $this->business_type      = $change_business_type ?: $this->business_type;
                $this->dialog_must_status = intval(!$flag);              //不能跳过
                $this->dialog_status      = $msg?1:0;                   //要展示弹窗
                $this->dialog_msg         = implode("\r\n", $msg);            //有提示 拼在一起

                //打卡开关 就是只提示，可以跳过
                $set_model = SettingEnvModel::findFirstByCode("by_clock_open");
                if (!empty($set_model) && $set_model->set_val) {
                    $this->dialog_must_status = 0;
                }

                //验证用户是否在ces培训 TODO 未回款
                if ((new OtherServer($this->lang, $this->timezone))->checkStaffIsCesTraining($this->staffInfo)) {
                    $this->is_ces_tra         = 1;
                    $this->dialog_must_status = 0;
                }
                return $this;
            }
        }
        return $this;
    }
    //https://yapi.flashexpress.pub/project/436/interface/api/86678
    public function check_ic_from_java($staffId, $date){
        $param                      = ['staffInfoId' => $staffId, 'date' => $date];
        $uri                           = '/svc/ticket/delivery/not_warehouse_pno';
        $url = $this->config->api->java_pms_url;
        $url .= $uri;
        $res = $this->httpPost($url, $param);
        $this->logger->write_log("sendFleRequest {$url} ".json_encode($param).json_encode($res), 'info');
        if (!empty($res['code']) && $res['code'] == 1) {
            return $res['data'];
        }
        return [];
    }

    /**
     * 验证java业务
     * @param $is_skip
     * @return array
     */
    protected function check_java_task($is_skip): array
    {
        //主播职位 跳过限制逻辑
        $jobId = (new SettingEnvServer())->getSetVal('free_shift_position');
        $jobId = empty($jobId) ? [] : explode(',', $jobId);
        if (!empty($this->staffInfo) && in_array($this->staffInfo['job_title'], $jobId)) {
            return [];
        }
        $returnData = [];
        if (!env('check_ms_task', 1)) {
            return $returnData;
        }
        $res        = $this->punch_out_check_java()->build_data();
        if(empty($res)){
            return [];
        }
        if ($is_skip && $res[self::JAVA_TASK_CONFIG[$res['business_type']]]['dialog_must_status'] == 0) {
            return $returnData;
        }
        return $res;
    }

    /**
     * 早退提示方法
     * @throws BusinessException
     */
    public function check_early_off(
        $staff_info,
        $attendance_date,
        $shift_index,
        $is_confirm_early_off
    ): bool {
        if (!in_array($staff_info['formal'], [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN])) {
            return true;
        }
        $staff_id  = $staff_info['staff_id'];
        $job_title = $staff_info['job_title'];

        if ($is_confirm_early_off == 1) {
            return true;
        }

        if (empty($attendance_date)) {
            return true;
        }

        $attendanceServer = (new AttendanceServer($this->lang, $this->timezone));
        $freeShiftConfig  = $attendanceServer->initFreeShiftConfig();

        $attendanceInfo = $attendanceServer->get_att_by_date($staff_id,
            $attendance_date);
        if (empty($attendanceInfo) || empty($attendanceInfo['started_at'])) {
            return true;
        }

        $shiftDuration = self::ALL_DAY_SHIFT_DURATION;
        $now           = date('Y-m-d H:i:s');
        $isLive        = false;
        if (in_array($job_title, $freeShiftConfig['free_shift_position'])) {
            $isLive    = true;
            $hour      = date( 'H',strtotime($attendanceInfo['started_at']));
            $dayHour   = $freeShiftConfig['free_shift_day_shift_duration'];
            $nightHour = $freeShiftConfig['free_shift_night_shift_duration'];
            //4-16点 白班
            if ($hour >= 4 && $hour < 16) {
                $shiftDuration = $dayHour;
            } else {
                $shiftDuration = $nightHour;
            }
        }
        $leaveInfo           = (new LeaveServer($this->lang, $this->timezone))->getStaffLeaveInfoFromCache($staff_id,
            $attendance_date);
        $seconds = self::ALL_DAY_SHIFT_DURATION * 3600;
        switch ($leaveInfo) {
            case 3:
                $seconds = 0;
                break;
            case 1:
            case 2:
                if ($isLive) {
                    $seconds = ($shiftDuration / 2) * 3600;
                } else {
                    $seconds = self::HALF_DAY_SHIFT_DURATION * 3600;
                }
                break;
        }
        if (empty($seconds)) {
            return true;
        }

        $time = strtotime($now) - strtotime($attendanceInfo['started_at']);
        if ($time < $seconds) {
            $this->makeAttendanceDurationLessNotice($staff_info,$seconds);
        }
        return true;
    }

    /**
     * @param $staffInfo
     * @param $shiftSeconds
     * @return void
     * @throws BusinessException
     */
    protected function makeAttendanceDurationLessNotice($staffInfo, $shiftSeconds): void
    {
        $t        = $this->getTranslation();
        $timeInfo = DateHelper::dealSecondsToHoursAndMinutes($shiftSeconds);
        $timeStr  = '';
        if ($timeInfo['hours'] > 0) {
            $timeStr .= $t->_('early_off_hour', ['x' => $timeInfo['hours']]);
        }
        if ($timeInfo['minutes'] > 0) {
            $timeStr .= $t->_('early_off_minutes', ['x' => $timeInfo['minutes']]);
        }
        if (!empty($timeStr)) {
            http_response_code(422);
            $t_key         = 'early_off_tips_content';
            $settingConfig = (new SettingEnvServer())->getSetVal('early_off_tips_task_job_title', ',');
            if (in_array($staffInfo['job_title'], $settingConfig)) {
                $t_key = in_array($staffInfo['hire_type'],
                    HrStaffInfoModel::$agentTypeTogether) ? 'early_off_tips_content_agent' : 'early_off_tips_content_task';
            }
            if (isCountry('MY')) {
                $t_key .= '_my';
            }
            throw new BusinessException($t->_($t_key, ['time' => $timeStr]), ErrCode::PUNCH_OUT_EARLY_OFF_ERROR);
        }
    }

    /**
     * @param $staffInfo
     * @param $paramsIn
     * @return array
     * @throws BusinessException
     */
    public function check_staff_punch_out($staffInfo, $paramsIn)
    {
        $returnData      = [
            'data' => (object)[],
            'code' => -3,
            'msg'  => 'fail',
        ];
        $this->staffInfo = $staffInfo;
        $this->platform  = $paramsIn['platform'];
        if (env('close_punch_out_check', 0)) {
            return $returnData;
        }
        //验证java
        $check_java_task = $this->check_java_task($paramsIn['is_skip']);
        if (!empty($check_java_task)) {
            $returnData['data'] = $check_java_task;
            return $returnData;
        }

        // Fbi的任务
        $check_fbi = $this->check_bi_task();
        if(!empty($check_fbi)){
            $returnData['data'] = $check_fbi;
            return $returnData;
        }

        //验证未读的消息
        $check_un_read_message = $this->check_un_read_message();
        if (!empty($check_un_read_message)) {
            $returnData['data'] = $check_un_read_message;
            return $returnData;
        }

        // 验证未完成学习计划
        $check_un_finished_study = $this->check_un_finished_study();
        if (!empty($check_un_finished_study)) {
            $returnData['data'] = $check_un_finished_study;
            return $returnData;
        }
        // 验证kpi 是否已经完成
        $verifyKpiResult = (new KpiServer($this->lang, $this->timezone))->verifyKpiIsCompleted($staffInfo);
        if (!empty($verifyKpiResult)) {
            $returnData['data'] = $verifyKpiResult;
            return $returnData;
        }

        //验证是否有未处理完成资产
        $verify_has_resign_assets = $this->check_un_has_resign_assets();
        if (!empty($verify_has_resign_assets)) {
            $returnData['data'] = $verify_has_resign_assets;
            return $returnData;
        }

        //验证是否有未盘任务
        $verify_has_undone_inventory_task = (new MaterialInventoryCheckServer($this->lang, $this->timezone))->verifyHasUndoneInventoryTask($staffInfo);
        if (!empty($verify_has_undone_inventory_task)) {
            $returnData['data'] = $verify_has_undone_inventory_task;
            return $returnData;
        }


        $returnData['code'] = 1;
        $returnData['msg']  = 'success';
        return $returnData;
    }

    public function check_bi_task(): array
    {
        return [];
    }


    protected function build_fbi_data(): array
    {
       return [
            'business_type'                              => $this->business_type,
            self::FBI_TASK_CONFIG[$this->business_type] => [
                'dialog_status'      => $this->dialog_status,//0不弹，1弹
                'dialog_msg'         => $this->dialog_msg,
                'dialog_must_status' => $this->dialog_must_status,// 0 能跳过 1不能跳过
                'is_ces_tra'         => $this->is_ces_tra,
                'init_business_type' => $this->init_business_type,
            ],
        ];
    }



    /**
     *
     * 验证未弯沉学习计划
     * @return array
     */
    protected function check_un_finished_study()
    {
        $returnData       = [];
        $settingEnvServer = new SettingEnvServer();
        $switch           = $settingEnvServer->getSetVal('is_check_finished_study');

        $this->logger->write_log('check_un_finished_study '.$this->staffInfo['id'].' switch :'.$switch, 'info');

        if ($switch) {
            $apiClient = new ApiClient('school_rpc', '', 'staff_check_staff_overtime', $this->lang);
            $apiClient->setParams(['staff_info_id' => $this->staffInfo['id']]);
            $result = $apiClient->execute();
            if ($result && isset($result['code']) && $result['code'] == 1 && isset($result['result']) && $result['result']['code'] == 1 && $result['result']['data']['is_overtime']) {
                $returnData['business_type']   = 'un_finished_study';
                $returnData['training_detail'] = [
                    'message' => $result['result']['data']['overtime_info'],
                    'url'     => $result['result']['data']['h5_learning_plan_url'],
                ];
            }
        }

        return $returnData;
    }


    /**
     * 验证未完成的揽收任务
     * @return array
     */
    protected function check_un_finished_task()
    {
        $returnData       = [];
        $settingEnvServer = new SettingEnvServer();
        $switch           = $settingEnvServer->getSetVal('is_check_ticket_pickup');

        $this->logger->write_log('check_un_finished_task '.$this->staffInfo['id'].' staff_info:'.json_encode($this->staffInfo,
                JSON_UNESCAPED_UNICODE).' switch:'.$switch, 'info');

        //开关没有或者是0就不验证
        if (!$switch) {
            return $returnData;
        }
        //非快递员不验证
        if (!in_array(1, $this->staffInfo['positions'])) {
            return $returnData;
        }

        $fle_rpc = new ApiClient('fle', 'com.flashexpress.fle.svc.api.TicketPickupSvc', 'checkStaffTicketPickupFinish',
            $this->lang);
        $fle_rpc->setParams($this->staffInfo['id']);
        $fle_return = $fle_rpc->execute();
        if (isset($fle_return['result']) && $fle_return['result']) {
            $data['dialog_msg'] = $this->platform == 'RB_KIT' ? $this->getTranslation()->_('kit_check_take_express_delivery_task') : $this->getTranslation()->_('check_take_express_delivery_task');//TODO 增加kit和by的不同提示
            //构建业务类型返回参数
            $returnData['business_type'] = 'un_finished_task';
            $returnData['task_detail']   = $data;
        }

        $this->logger->write_log('check_un_finished_task '.$this->staffInfo['id'].' java return:'.json_encode($fle_return,
                JSON_UNESCAPED_UNICODE).' result:'.json_encode($returnData, JSON_UNESCAPED_UNICODE), 'info');
        return $returnData;
    }

    /**
     * 验证未读消息
     * @return array
     */
    protected function check_un_read_message()
    {
        $returnData     = [];
        $backyardServer = new BackyardServer($this->lang, $this->timezone);
        $messageInfo    = $backyardServer->punch_out_msg([
            'staff_id'        => $this->staffInfo['id'],
            'request_channel' => 'off_work_call',
        ]);
        if (!empty($messageInfo)) {
            $data['msg_id'] = strval(current($messageInfo));
            //构建业务类型返回参数
            $returnData['business_type']  = 'un_read_message';
            $returnData['message_detail'] = $data;
        }
        $this->logger->write_log('check_un_read_message '.$this->staffInfo['id'].' result:'.json_encode($returnData,
                JSON_UNESCAPED_UNICODE), 'info');

        return $returnData;
    }

    /**
     * 验证主管名下是否有未处理资产 
     * @return array
     */
    protected function check_un_has_resign_assets()
    {
        $returnData       = [];
        $settingEnvServer = new SettingEnvServer();
        $setting_val      = $settingEnvServer->listByCode([
            'is_check_has_resign_assets',
            'check_has_resign_assets_job_title',
        ]);

        $this->logger->write_log('check_un_has_resign_assets staff_info:' . json_encode($this->staffInfo,
                JSON_UNESCAPED_UNICODE) . ' setting_val:' . json_encode($setting_val, JSON_UNESCAPED_UNICODE), 'info');

        $setting_val = array_column($setting_val, 'set_val', 'code');

        $is_check_has_resign_assets        = $setting_val['is_check_has_resign_assets'] ?? 0;
        $check_has_resign_assets_job_title = $setting_val['check_has_resign_assets_job_title'] ?? '';
        //开关没有或者是0就不验证
        if (!$is_check_has_resign_assets) {
            return $returnData;
        }

        //如果未配置职位id 则不校验是否有待处理资产
        if (empty($check_has_resign_assets_job_title)) {
            return $returnData;
        }

        //如果职位不为空。并且不是指定职位则不验证
        $job_title_arr = explode(',', $check_has_resign_assets_job_title);
        if (!in_array($this->staffInfo['job_title'], $job_title_arr)) {
            return $returnData;
        }

        $ret = new ApiClient('oa_rpc', '', 'has_leave_assets', $this->lang);
        $ret->setParams(['staff_id' => $this->staffInfo['id'], 'clock_in_date' => date('Y-m-d')]);
        $rpc_result = $ret->execute();

        if (isset($rpc_result['result']) && isset($rpc_result['result']['data']) && $rpc_result['result']['data'] === true) {
            //有未处理资产
            $returnData['business_type']     = 'un_remittance';
            $returnData['remittance_detail'] = [
                'dialog_msg'         => $this->getTranslation()->_('has_resign_assets_error_1'),
                'dialog_status'      => 1,
                'dialog_must_status' => 1,
                'is_ces_tra'         => 0,
            ];
        }
        $this->logger->write_log('check_un_has_resign_assets ' . $this->staffInfo['id'] . ' result:' . json_encode($returnData,
                JSON_UNESCAPED_UNICODE), 'info');
        return $returnData;
    }

    //验证是否有未完成续签合同审批
    protected function check_un_has_approval_renew_contract() {
        $returnData       = [];
        $staff_info_id = $this->staffInfo['id'];
        $date          = gmdate('Y-m-d 00:00:00', strtotime('-6 days'));

        $pending_list = AuditApprovalModel::find([
            'conditions' => "biz_type = :type: and approval_id = :approval_id: and state = :state: and created_at < :date:",
            'bind'       => [
                'type'  => enums::$audit_type['RC'],
                'state' => enums::$audit_status['panding'],
                'approval_id' => $staff_info_id,
                'date'  => $date,
            ],
        ])->toArray();

        if(!empty($pending_list)) {
            $audit_ids = array_column($pending_list, 'biz_value');
            $renew_contract_list = HrStaffRenewContractApplyModel::find([
                'conditions' => "id in ({ids:array})",
                'bind'       => [
                    'ids' => $audit_ids,
                ],
            ])->toArray();
            $is_intercept = false;
            $renew_contract_list = array_column($renew_contract_list, null, 'id');
            foreach ($pending_list as $key => $value) {
                $renew_contract = $renew_contract_list[$value['biz_value']] ?? [];
                if(!empty($renew_contract) && !empty($renew_contract['first_approval_id'])) {
                    $first_approval_ids = explode(',', $renew_contract['first_approval_id']);
                    if (in_array($value['approval_id'], $first_approval_ids)) {
                        //是一级节点审批人，拦截下班打卡
                        $is_intercept = true;
                        break;
                    }
                }
            }
            if($is_intercept) {
                //有未处理续签合同审批
                $returnData['business_type']     = 'un_remittance';
                $returnData['remittance_detail'] = [
                    'dialog_msg'         => $this->getTranslation()->_('has_approval_renew_contract_error_1'),// 提示消息内容
                    'dialog_status'      => 1, //弹窗
                    'dialog_must_status' => 1, //是否跳过 1不能跳过
                    'is_ces_tra'         => 0, //是否ces培训 0否
                ];
            }
        }

        return $returnData;
    }

    /**
     * 组装backyard url - 各国通用
     * @param $businessName
     * @param $messageId
     * @return string
     */
    public function assembleUiUrl($messageId, $platform)
    {
        if ($platform == enums::RB_KIT) {
            return 'flashbackyard://fe/page?path=message&message_id=' . $messageId;
        } else {
            return 'flashbackyard://fe/page?path=message&messageid=' . $messageId;
        }
    }

    /**
     * 组装跳过接口url - 各国通用
     * @param $businessName
     * @param $messageId
     * @return string
     */
    public function assembleSkipUrl($businessType, $platform)
    {
        if ($platform == enums::RB_KIT) {
            $preUrl = trim(env('app_backyard_api'), '/');
        } else {
            $preUrl = trim(env('app_service_api'), '/');
        }

        return $preUrl . '/attendance/set_clock_in_skip?business_type=' . $businessType;
    }

    /**
     * 组装接口拦截返回数据 - 各国通用
     * @param $data
     * @return array
     */
    public function formatClockInBeforeData($data)
    {
        $returnData                 = [];
        $returnData['display_type'] = $data['display_type'] ?? ClockEnums::DISPLAY_TYPE_ALTER;
        $returnData['detail']       = [
            'title'       => $data['title'] ?? '',
            'message'     => $data['message'] ?? '',
            'is_can_skip' => $data['is_can_skip'] ?? false,
            'buttons'     => [$data['buttons']] ?? [],
        ];

        return $returnData;
    }

    /**
     * 设置跳过的key - 各国通用
     * @param $params
     * @param $staffInfoId
     * @return array
     */
    public function getClockSkip($businessType, $staffInfoId)
    {
        $redisObj = $this->di->get('redis');
        $cacheKey = $this->getClockSkipCacheKey($businessType, $staffInfoId);
        return $redisObj->get($cacheKey);
    }

    /**
     * 设置跳过的key - 各国通用
     * @param $params
     * @param $staffInfoId
     * @return array
     */
    public function setClockSkip($params, $userInfo)
    {
        $redisObj = $this->di->get('redis');
        $cacheKey = $this->getClockSkipCacheKey($params['business_type'], $userInfo['id']);
        $redisObj->save($cacheKey, 1, RedisEnums::CLOCK_KEY_EXPIRE);
        return $this->checkReturn(1);
    }

    /**
     * 获取key - 各国通用
     * @param $businessType
     * @param $staffInfoId
     * @return string
     */
    public function getClockSkipCacheKey($businessType, $staffInfoId)
    {
        return RedisEnums::CLOCK_KEY . '_' . $businessType . '_' . $staffInfoId;
    }

    /**
     * 验证是否有未完成转正评估目标制定
     * @return array
     */
    protected function check_probation_target(): array
    {
        $returnData       = [];
        $staff_info_id = $this->staffInfo['id'];
        if (empty($staff_info_id) || !isCountry(['TH','PH','MY'])) {
            return $returnData;
        }

        if (env('close_probation_target', 0)) {
            return $returnData;
        }

        // 考勤打卡白名单
        $isInfWhite = (new WhiteListServer())->isInfWhiteList($staff_info_id, date('Y-m-d'),
            [AttendanceWhiteListModel::TYPE_PAID_LOCALLY, AttendanceWhiteListModel::TYPE_NOT_PAID_LOCALLY]);
        if ($isInfWhite) {
            return $returnData;
        }

        $date = date('Y-m-d 00:00:00', strtotime('-6 days'));
        // 转正白名单
        $probation_staff = (new SettingEnvServer())->getSetValToArray('probation_staff');

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.staff_info_id,s.name,s.manger,s.state,s.node_department_id,s.job_title,s.hire_date,s.hire_type");
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->leftjoin(HrProbationModel::class, "p.staff_info_id = s.staff_info_id", 'p');
        $builder->leftjoin(HrProbationTargetModel::class, "t.staff_info_id = s.staff_info_id", 't');
        $builder->andWhere('t.is_hidden =:is_hidden: and t.is_deleted=0 and t.setting_state=:setting_state: and p.status != :p_status: and p.probation_channel_type = :probation_channel_type: and s.manger = :manger: and s.state != :state: and s.hire_date <= :hire_date:',
            [
                'manger'                 => $staff_info_id,
                'state'                  => HrStaffInfoModel::STATE_2,
                'hire_date'              => $date,
                'probation_channel_type' => HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE,
                'p_status'               => HrProbationModel::STATUS_FORMAL,
                'setting_state'          => HrProbationTargetModel::SETTING_STATE_NOT_START,
                'is_hidden'              => 0,
            ]);
        if (!empty($probation_staff)) {
            $builder->notInWhere('s.staff_info_id ', $probation_staff);
        }
        $staffData = $builder->getQuery()->execute()->toArray();
        if (empty($staffData)) {
            return $returnData;
        }
        //有未完成转正评估目标制定
        $returnData['business_type']     = 'un_remittance';
        $returnData['remittance_detail'] = [
            'dialog_msg'         => $this->getTranslation()->_('probation_target_error'),// 提示消息内容
            'dialog_status'      => 1,                                                   //弹窗
            'dialog_must_status' => 1,                                                   //是否跳过 1不能跳过
            'is_ces_tra'         => 0,                                                   //是否ces培训 0否
        ];
        return $returnData;
    }

    /**
     * @return array
     */
    public function check_probation_criminal(): array
    {
        $returnData       = [];
        $staff_info_id = $this->staffInfo['id'];
        if (empty($staff_info_id) || !isCountry(['TH','PH','MY'])){
            return $returnData;
        }
        // 考勤打卡白名单
        $isInfWhite =  (new WhiteListServer())->isInfWhiteList($staff_info_id,date('Y-m-d'),[AttendanceWhiteListModel::TYPE_PAID_LOCALLY,AttendanceWhiteListModel::TYPE_NOT_PAID_LOCALLY]);
        if ($isInfWhite){
            return $returnData;
        }
        // 转正白名单
        $probation_staff = (new SettingEnvServer())->getSetValToArray('probation_staff');

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("a.audit_status,a.staff_info_id");
        $builder->from(['a' => HrProbationAuditModel::class]);
        $builder->leftjoin(HrProbationModel::class, "p.staff_info_id = a.staff_info_id", 'p');
        $builder->leftjoin(HrStaffInfoModel::class, "p.staff_info_id = s.staff_info_id", 's');
        $builder->andWhere('p.status = :p_status: and p.probation_channel_type = :probation_channel_type: and a.audit_id = :audit_id: and a.audit_level = :audit_level: and a.cur_level = :cur_level: and s.state != :state: and p.second_manger_restrictions_start_date is not null and p.second_manger_restrictions_start_date <= :second_manger_restrictions_start_date:', [
            'audit_id' => $staff_info_id,
            'state'=>HrStaffInfoModel::STATE_2,
            'probation_channel_type'=>HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE,
            'p_status'=>HrProbationModel::STATUS_PROBATION,
            'second_manger_restrictions_start_date' => date('Y-m-d'),
            'audit_level' => HrProbationAuditModel::AUDIT_LEVEL_FIRST,
            'cur_level' => HrProbationModel::CUR_LEVEL_SECOND,
        ]);
        if (!empty($probation_staff)){
            $builder->notInWhere('s.staff_info_id ',$probation_staff);
        }
        $builder->orderby('a.id desc');
        $builder->groupBy("a.staff_info_id");
        $staffData = $builder->getQuery()->execute()->toArray();
        if (empty($staffData)){
            return $returnData;
        }
        $is_check_probation_criminal = false;
        foreach ($staffData as $value){
            if ($value['audit_status'] == HrProbationAuditModel::AUDIT_STATUS_TIMEOUT){
                $is_check_probation_criminal = true;
            }
        }
        if ($is_check_probation_criminal){
            $returnData['business_type']     = 'un_remittance';
            $returnData['remittance_detail'] = [
                'dialog_msg'         => $this->getTranslation()->_('probation_criminal_error'),// 提示消息内容
                'dialog_status'      => 1, //弹窗
                'dialog_must_status' => 1, //是否跳过 1不能跳过
                'is_ces_tra'         => 0, //是否ces培训 0否
            ];
        }
        return $returnData;
    }

    /**
     * 验证是否有到最后一天期限没有完成评估或有已超时的评估
     * @return array
     */
    public function check_probation_timeout(): array
    {
        $returnData       = [];
        $staff_info_id = $this->staffInfo['id'];
        if (empty($staff_info_id) || !isCountry(['TH','PH','MY'])){
            return $returnData;
        }
        // 考勤打卡白名单
        $isInfWhite =  (new WhiteListServer())->isInfWhiteList($staff_info_id,date('Y-m-d'),[AttendanceWhiteListModel::TYPE_PAID_LOCALLY,AttendanceWhiteListModel::TYPE_NOT_PAID_LOCALLY]);
        if ($isInfWhite){
            return $returnData;
        }

        $before_check = HrProbationAuditModel::findFirst([
            "audit_id = :staff_info_id:",
            "bind" => [
                "staff_info_id" => $staff_info_id,
            ],
        ]);
        if (empty($before_check)) {
            return $returnData;
        }
        // 转正白名单
        $probation_staff = (new SettingEnvServer())->getSetValToArray('probation_staff');
        $deadline = date('Y-m-d', strtotime('+1 day'));
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("a.audit_status,a.staff_info_id,a.deadline_at,a.is_active");
        $builder->from(['a' => HrProbationAuditModel::class]);
        $builder->leftjoin(HrProbationModel::class, "p.staff_info_id = a.staff_info_id", 'p');
        $builder->leftjoin(HrStaffInfoModel::class, "p.staff_info_id = s.staff_info_id", 's');
        $builder->andWhere('p.status != :p_status: and p.probation_channel_type = :probation_channel_type: and a.audit_id = :audit_id: and s.state != :state: and p.is_system = :is_system:', [
            'audit_id' => $staff_info_id,
            'state'=>HrStaffInfoModel::STATE_2,
            'probation_channel_type'=>HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE,
            'p_status'=>HrProbationModel::STATUS_FORMAL,
            'is_system'=>HrProbationModel::IS_NOT_SYSTEM,
        ]);
        if (!empty($probation_staff)){
            $builder->notInWhere('s.staff_info_id ',$probation_staff);
        }
        
        $builder->orderby('a.id desc');
        $staffData = $builder->getQuery()->execute()->toArray();
        if (empty($staffData)){
            return $returnData;
        }
        $is_check_probation_timeout = false;
        foreach ($staffData as $value){
            if ($value['audit_status'] == HrProbationAuditModel::AUDIT_STATUS_TIMEOUT && $value['is_active'] == 0){
                $is_check_probation_timeout = true;
                break;
            }
            if (!empty($value['deadline_at']) && $value['audit_status'] == HrProbationAuditModel::AUDIT_STATUS_PENDING && $value['deadline_at'] <= $deadline){
                $is_check_probation_timeout = true;
                break;
            }
        }
        if ($is_check_probation_timeout){
            $returnData['business_type']     = 'un_remittance';
            $returnData['remittance_detail'] = [
                'dialog_msg'         => $this->getTranslation()->_('probation_timeout_error'),// 提示消息内容
                'dialog_status'      => 1, //弹窗
                'dialog_must_status' => 1, //是否跳过 1不能跳过
                'is_ces_tra'         => 0, //是否ces培训 0否
            ];
        }
        return $returnData;
    }
}