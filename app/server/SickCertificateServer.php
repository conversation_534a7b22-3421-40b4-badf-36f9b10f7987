<?php
/**
 * Created by PhpStor<PERSON>.
 * User: nick
 * Date: 6/11/24
 * Time: 5:03 PM
 */

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SickLeaveCertificateDetailModel;
use FlashExpress\bi\App\Models\backyard\SickLeaveCertificateModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditImageModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\SickServer;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

class SickCertificateServer extends AuditBaseServer
{

    public $leaveInfo;
    public $isCertificate = false;//是否有病例证明 添加用
    public $imageBody = [];//定制详情页 证明图片模块

    public function addRecord($param)
    {
        $leaveInfo = StaffAuditModel::findFirst((int)$param['leave_id']);
        if (empty($leaveInfo)) {
            throw new ValidationException('leave info error');
        }
        $this->leaveInfo = $leaveInfo;

        //如果 是大于3天 并且提交了 certificate 要改成已提交
        $this->checkApply($param);

        $db = $this->getDI()->get("db");
        try {
            $db->begin();
            //把其他的申请 is new = 0
            SickLeaveCertificateModel::find([
                'conditions' => 'audit_id = :audit_id:',
                'bind' => ['audit_id' => (int)$param['leave_id']]
            ])->update(['is_new' => 0]);

            $serialNo                  = $this->getRandomId();
            $model                     = new SickLeaveCertificateModel();
            $insert['serial_no']       = $serialNo;
            $insert['staff_info_id']   = $param['staff_info_id'];
            $insert['audit_id']        = (int)$param['leave_id'];
            $insert['leave_serial_no'] = $leaveInfo->serial_no;
            $insert['other_content']   = empty($param['other_content']) ? null : $param['other_content'];
            $insert['is_new']          = 1;
            $insert['time_out']        = date('Y-m-d 00:00:00', strtotime('+3 day'));
//            $insert['approval_time']   = date('Y-m-d H:i:s');//审批状态
            $model->create($insert);

            $detailData  = $this->formatImgDetail($param, $model->id);

            //如果没提交状态 并且上传了 证明 改为已提交
            if($this->isCertificate && $leaveInfo->sub_status == StaffAuditModel::SICK_SUB_STATUS_NO){
                //请假主表 已提交状态
                $leaveInfo->sub_status = StaffAuditModel::SICK_SUB_STATUS_YES;
                $leaveInfo->updated_at = new \Phalcon\Db\RawValue('updated_at');
                $leaveInfo->update();
            }

            $detailModel = new SickLeaveCertificateDetailModel();
            $detailModel->batch_insert($detailData, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);

            $lastInsertId = $model->id;
            if (!$lastInsertId) {
                $db->rollBack();
                throw new \Exception('apply failed');
            }

            $flag = (new ApprovalServer($this->lang, $this->timeZone))->create($lastInsertId, AuditListEnums::APPROVAL_TYPE_SICK_CERTIFICATE, $param['staff_info_id']);
            if (!$flag) {
                throw new \Exception('创建审批流失败');
            }

            $db->commit();
            return $this->checkReturn(['data' => $lastInsertId]);
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log('add sick certificate error '.$e->getMessage());
            return $this->checkReturn(-3, $this->getTranslation()->_('2109'));
        }
    }

    //添加操作验证
    public function checkApply($param)
    {
        $category = SickServer::$sickImgCategory;
        $flag     = false;
        //判断是否上传 至少一项材料
        foreach ($category as $item) {
            if (!empty($param[$item])) {
                $flag = true;
            }

            //每项不能超过3张
            if (!empty($param[$item]) && count($param[$item]) > 3) {
                throw new ValidationException($this->getTranslation()->_('at most 3 images'));
            }
        }
        if ($flag === false) {
            throw new ValidationException($this->getTranslation()->_('sick_at_least_one'));
        }


        //只能存在一个带审批
        $exist = SickLeaveCertificateModel::findFirst([
            'conditions' => 'audit_id = :audit_id: and status = 1',
            'bind' => ['audit_id' => $param['leave_id']]
        ]);
        if(!empty($exist)){
            throw new ValidationException($this->getTranslation()->_('sick_check_exist'));
        }

        if ($this->leaveInfo->leave_day >= 3 && empty($param['certificate'])) {
            throw new ValidationException($this->getTranslation()->_('sick_certificate_need'));
        }

        //如果其他类别上传 需写文字描述
        if (!empty($param['other']) && empty($param['other_content'])) {
            throw new ValidationException($this->getTranslation()->_('other_content_notice'));
        }

        //最多100字
        if (!empty($param['other_content']) && mb_strlen($param['other_content']) > 100) {
            throw new ValidationException($this->getTranslation()->_('other_content_too_lang'));
        }

        //如果请假记录 已经超过上传最后日期 不能提交
        if($this->leaveInfo->sub_status == StaffAuditModel::SICK_SUB_STATUS_OVER){
            throw new ValidationException($this->getTranslation()->_('sick_certificate_timeout_notice'));
        }


        return true;
    }

    //整理详情图片数据
    public function formatImgDetail($param, $cid)
    {
        $server   = new SickServer($this->lang, $this->timeZone);
        $return   = [];
        $category = $server->categoryEnum;
        foreach ($category as $c => $c_enum) {
            if (empty($param[$c])) {
                continue;
            }
            if($c_enum == SickServer::SICK_CERTIFICATE){
                $this->isCertificate = true;
            }
            foreach ($param[$c] as $url) {
                $row['cid']           = $cid;
                $row['image_path']    = $url;
                $row['audit_id']      = $this->leaveInfo->audit_id;
                $row['category']      = $c_enum;
                $return[]             = $row;
            }
        }
        return $return;
    }

    //审批操作
    public function updateSickCertificate($param)
    {
        $info = $this->getLeaveCertificateData($param['audit_id']);
        if (empty($info)) {
            throw new ValidationException('leave info error');
        }
        $info    = $info->toArray();

        $audit_type = AuditListEnums::APPROVAL_TYPE_SICK_CERTIFICATE;

        if ($info['c_status'] != enums::APPROVAL_STATUS_PENDING) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }
        $userInfo = $param['user_info'];

        $res    = false;
        $server = new ApprovalServer($this->lang, $this->timeZone);
        if ($param['status'] == enums::APPROVAL_STATUS_APPROVAL) {
//            //先审批病假 再材料 产品说 这逻辑先去掉 不然影响打卡
//            if($info['status'] == enums::$audit_status['panding']){
//                throw new ValidationException($this->getTranslation()->_('approve_leave_first'));
//            }
            //如果请假已经是  最终状态 并且非审核通过 就不能审核了
            if(!in_array($info['status'], [enums::$audit_status['panding'],enums::$audit_status['approved']])){
                throw new ValidationException($this->getTranslation()->_('wrong_leave_status_notice'));
            }
            $res = $server->approval($param['audit_id'], $audit_type, $userInfo['id'],null);
        } elseif ($param['status'] == enums::APPROVAL_STATUS_REJECTED) {
            $res = $server->reject($param['audit_id'], $audit_type, $param['reject_reason'], $userInfo['id']);
        } elseif ($param['status'] == enums::APPROVAL_STATUS_CANCEL) {
            $res = $server->cancel($param['audit_id'], $audit_type,'' , $info['staff_info_id']);
        }
        if ($res === false) {
            return $this->checkReturn(-3, 'server error');
        }
        return $this->checkReturn([]);
    }

    //根据病假id 病假详情图片 信息 添加页面显示病假信息用
    public function getLeaveInfo($param)
    {
        $leaveInfo = StaffAuditModel::findFirst((int)$param['leave_id']);
        if (empty($leaveInfo)) {
            throw new ValidationException('leave info error');
        }
        //只有病假有
        if($leaveInfo->leave_type != enums::LEAVE_TYPE_38){
            throw new ValidationException('leave type error');
        }

        $addHour     = $this->config->application->add_hour;
        $leaveServer = new LeaveServer($this->lang, $this->timeZone);
        [$leave_start_time, $leave_end_time] = $leaveServer->formatLeaveTime($leaveInfo->toArray());

        $data['audit_id']        = $leaveInfo->audit_id;
        $data['leave_serial_no'] = $leaveInfo->serial_no;
        $data['leave_type_text'] = $this->getTranslation()->_('2005');
        $data['start_time']      = $leave_start_time;
        $data['end_time']        = $leave_end_time;
        $data['leave_day']       = $leaveInfo->leave_day;
        $data['audit_reason']    = $leaveInfo->audit_reason;
        $data['created_at']      = date('Y-m-d H:i:s', strtotime($leaveInfo->created_at) + ($addHour * 3600));

        //获取 is new 的信息
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('c.serial_no,c.created_at,cd.image_path,cd.category as business_category,c.other_content');
        $builder->from(['c' => SickLeaveCertificateModel::class]);
        $builder->join(SickLeaveCertificateDetailModel::class, "c.id = cd.cid and c.is_new = 1", 'cd');
        $builder->andWhere('c.audit_id = :audit_id:', ['audit_id' => (int)$param['leave_id']]);
        $imgData = $builder->getQuery()->execute()->toArray();
        if(!empty($imgData)){
            $res = $this->formatImgData($imgData,$imgData[0]['other_content']);
            $data = array_merge($data, $res);
            //编号和时间 用材料申请的
            $data['leave_serial_no'] = $imgData[0]['serial_no'];
            $data['created_at']      = date('Y-m-d H:i:s', strtotime($imgData[0]['created_at']) + ($addHour * 3600));
        }else{
            $imgData = StaffAuditImageModel::find("audit_id = {$leaveInfo->audit_id}")->toArray();
            $res = $this->formatImgData($imgData,$leaveInfo->other_content);
            $data = array_merge($data, $res);
        }
        return $this->checkReturn(['data' => $data]);
    }



    public function formatImgData($imgData, $content = ''){
        $data = [];
        foreach ($imgData as $datum){
            $key = SickServer::$sickImgCategory[$datum['business_category']];
            $data[$key][] = $datum['image_path'];
            if($key == 'other'){
                $data['other_content'] = $content;
            }
        }
        return $data;
    }

    /**
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        $rule = new AuditOptionRule(false, false, false, false, false, false);
        return $rule;
    }
    //审批 的详情页
    public function getDetail(int $cid, $user, $comeFrom)
    {
        $info = $this->getLeaveCertificateData($cid);
        if (empty($info)) {
            throw new ValidationException('[hire type change] info error');
        }
        $info = $info->toArray();

        //获取提交人用户信息
        $staff_info      = (new StaffRepository())->getStaffPosition($info['staff_info_id']);
        if($staff_info['sys_store_id'] == enums::HEAD_OFFICE_ID){
            $storeName = enums::HEAD_OFFICE;
        }else{
            $storeInfo       = SysStoreModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $staff_info['sys_store_id']],
            ]);
            $storeName = empty($storeInfo) ? '' : $storeInfo->name;
        }



        $leave_server = Tools::reBuildCountryInstance(new LeaveServer($this->lang, $this->timeZone), [$this->lang, $this->timeZone]);
        [$leave_start_time,$leave_end_time] = $leave_server->formatLeaveTime($info);

        //职位
        $detailLists['leave_serial_no']    = $info['leave_serial_no'];//关联病假审批编号
        $detailLists['apply_parson']       = $staff_info['name']."({$staff_info['staff_info_id']})";
        $detailLists['department']         = $staff_info['depart_name'];
        $detailLists['dot']                = $storeName;
        $detailLists['start_time']         = $leave_start_time;
        $detailLists['end_time']           = $leave_end_time;
        $detailLists['days']               = $info['leave_day'];
        $detailLists['audit_leave_reason'] = $info['audit_reason'];

        //补充的图片
        $imgData     = $this->getImgData($cid);
        $res         = $this->formatImgData($imgData, $info['other_content']);
        $detailLists = array_merge($detailLists, $res);

        $addHour       = $this->config->application->add_hour;
        $audit_list_re = new AuditlistRepository($this->lang, $this->timeZone);
        $headData          = [
            'title'       => $audit_list_re->getAudityType(AuditListEnums::APPROVAL_TYPE_SICK_CERTIFICATE),
            'id'          => $info['cid'],
            'staff_id'    => $info['staff_info_id'],
            'type'        => AuditListEnums::APPROVAL_TYPE_SICK_CERTIFICATE,
            'created_at'  => date('Y-m-d H:i:s', strtotime($info['created_at']) + $addHour * 3600),
            'updated_at'  => $info['approval_time'] ?? '',//审批时间
            'status'      => $info['status'],
            'status_text' => $audit_list_re->getAuditStatus('10'.$info['status']),
            'serial_no'   => $info['serial_no'] ?? '',
        ];

        //详情页特殊处理 之前的结构 前端处理不了
        unset($detailLists['other_content']);
        $returnData['data']['detail'] = $this->formatSickOther($detailLists, $info['other_content']);

        $returnData['data']['head'] = $headData;
        return $returnData;
    }
    //根据 cid 获取图片信息
    public function getImgData($cid){
        //获取 is new 的信息
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('cd.image_path,cd.category as business_category,c.other_content');
        $builder->from(['c' => SickLeaveCertificateModel::class]);
        $builder->join(SickLeaveCertificateDetailModel::class, "c.id = cd.cid", 'cd');
        $builder->andWhere('c.id = :cid:', ['cid' => (int)$cid]);
        $imgData = $builder->getQuery()->execute()->toArray();
        return $imgData;
    }

    //列表页 概要字段
    public function genSummary(int $auditId, $user)
    {
        //获取加班详情
        $info = $this->getLeaveCertificateData($auditId);
        if (empty($info)) {
            return '';
        }
        $info = $info->toArray();
        //天数
        $item['key']   = $this->getTranslation()->_('days');
        $item['value'] = $info['leave_day'];
        $return[]      = $item;
        $leaveServer = new LeaveServer($this->lang, $this->timeZone);
        [$leave_start_time, $leave_end_time] = $leaveServer->formatLeaveTime($info);

        $item['key']   = $this->getTranslation()->_('start_time');
        $item['value'] = $leave_start_time;
        $return[]      = $item;

        $item['key']   = $this->getTranslation()->_('end_time');
        $item['value'] = $leave_end_time;
        $return[]      = $item;
        return $return;
    }

    public function setProperty(int $cid, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $info = $this->getLeaveCertificateData($cid);
            if (empty($info)) {
                throw new ValidationException('leave info error');
            }
            $info    = $info->toArray();

            //需要更新业务表的字段
            $update = ['status' => $state, 'approval_time' => date('Y-m-d H:i:s')];

            $auditId = $info['audit_id'];
            //非审核通过 超过3天 看是否存在审批通过的 病例证明 如果没有 改成没提交 没超过3天不管
            if ($info['leave_day'] >= 3 && $state != enums::$audit_status['approved']) {
                //看之前有没有已经提交过 病例证明
                $allData = $this->getAllCertificate($auditId);
                $this->getLeaveSubStatus($allData, $auditId);
                if ($this->isCertificate === false) {//需要改成没提交
                    StaffAuditModel::findFirst($info['audit_id'])->update(['sub_status' => StaffAuditModel::SICK_SUB_STATUS_NO]);
                }
            }
            //非审核通过 需要发消息 只有驳回 和超时 没有撤销
            if(in_array($state,[enums::$audit_status['dismissed'], enums::$audit_status['timedout']])){
                $staffId = $info['staff_info_id'];
                $staffRe = new StaffRepository($this->lang);
                $staffInfo = $staffRe->getStaffPosition($info['staff_info_id']);

                //看有没有支援
                $attendanceRe = new AttendanceRepository($this->lang, $this->timeZone);
                if ($staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
                    $supportInfo = $attendanceRe->getSupportInfoBySubStaff($staffId);
                    if (empty($supportInfo)) {
                        $this->logger->write_log("c_no_sub_staff_info {$staffId}",'info');
                        return true;
                    }
                    $staffId = $supportInfo['staff_info_id'];
                } else {
                    $supportInfo = $attendanceRe->getSupportOsStaffInfo($staffId);
                }

                $contentParam['send_type'] = SickServer::$sendTypeCertificate;
                $contentParam['url_type'] = 'url';//url 需要点击跳转 text 文本
                $contentParam['leave_day'] = $info['leave_day'];
                $contentParam['date_at'] = '';
                $extend['category'] = MessageEnums::MESSAGE_CATEGORY_CODE_SICK_CERTIFICATE;
                $server = new SickServer($this->lang, $this->timeZone);
                if($info['leave_day'] >= $server->dayLine){
                    $server->staffInfo = $staffInfo;
                    $firstDay = $server->getFirstWorkDay(date('Y-m-d',strtotime($info['leave_end_time'])));
                    if(empty($firstDay)){
                        $this->logger->write_log("no workday reject certificate {$staffId}");
                        return true;
                    }
                    $contentParam['date_at'] = $firstDay;
                }
                //员工语言环境
                $model      = new StaffAccountModel();
                $lang = $model->getAcceptLanguage($staffId);

                //主账号发送
                $title   = $this->getTranslation($lang)->_('sick_certificate_add_title');
                $content = 'sick-leave-materials-message?' . http_build_query($contentParam);
                //发送
                $server = new MessageServer($this->lang, $this->timeZone);
                $server->sendMessage($staffId, $title, $content, $extend);

                //子账号
                if (!empty($supportInfo)) {
                    $subStaffId = $supportInfo['sub_staff_info_id'];
                    if (empty($subStaffId)) {
                        //还没生成工号 不发
                        $this->logger->write_log("c_no_sub_staff_id {$staffId}", 'info');
                        return true;
                    }
                    $contentParam['url_type'] = 'text';//url 需要点击跳转 text 文本
                    $content = 'sick-leave-materials-message?' . http_build_query($contentParam);
                    $server->sendMessage($subStaffId, $title, $content, $extend);
                }

                $update['approve_id'] = $extend['staff_id'] ?? null;//超时 或者驳回 审批人
                if(!empty($extend['remark'])){
                    $update['reject_reason'] = $extend['remark'];
                }
                if(!empty($extend['staff_id'])){
                    $approveInfo = HrStaffInfoModel::findFirst("staff_info_id={$extend['staff_id']}");
                    $update['approve_name'] = $approveInfo->name;
                }
            }

            SickLeaveCertificateModel::findFirst($cid)->update($update);
        }

        return true;
    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        return [];
    }

    //看是否存在已审批通过的 病例证明
    public function getLeaveSubStatus($data, $auditId){
        foreach ($data as $da){
            if(empty($da['cid'])){//一条申请都没有 要看病假的图片
                continue;
            }
            //非审核通过
            if(!in_array($da['c_status'], [enums::$audit_status['panding']], enums::$audit_status['approved'])){
                continue;
            }
            //已经上传了 证明
            if($da['category'] == SickServer::SICK_CERTIFICATE){
                $this->isCertificate = true;
                break;
            }
        }
        if($this->isCertificate === false){
            //查询病假的材料看有没有病例证明
            $imgData = StaffAuditImageModel::find([
                'conditions' => 'audit_id = :audit_id: and business_category = :business_category:',
                'bind' => ['audit_id' => $auditId, 'business_category' => SickServer::SICK_CERTIFICATE]
            ])->toArray();
            if(!empty($imgData)){
                $this->isCertificate = true;
            }
        }
        return ;
    }


    //病假维度的 当前登陆人的 材料申请列表页 审批状态是 材料 is new的状态 后来改成只取代审和通过的病假
    public function getSickCertificateList($param)
    {
        $pageNum = empty($param['page_num']) ? 1 : (int)$param['page_num'];
        $pageSize = 10;//固定10
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('a.audit_id as leave_id,a.serial_no as leave_serial_no, a.staff_info_id,a.leave_start_time,a.leave_start_type, a.leave_end_time,a.leave_end_type,a.leave_day,a.status,a.sub_status,
                         a.audit_reason, c.created_at,a.created_at as leave_create_at, c.id as cid,c.status as c_status');
        $builder->from(['a' => StaffAuditModel::class]);
        $builder->leftjoin(SickLeaveCertificateModel::class, "a.audit_id = c.audit_id and c.is_new = 1", 'c');
        $builder->andWhere('a.staff_info_id = :staff_info_id:', ['staff_info_id' => $param['staff_info_id']]);
        $builder->andWhere('a.leave_type = :leave_type:', ['leave_type' => enums::LEAVE_TYPE_38]);
        $builder->inWhere('a.status', [enums::$audit_status['panding'], enums::$audit_status['approved']]);
        //材料提交状态 超时状态 不展示出来
        if(!empty($param['sub_status'])){
            if($param['sub_status'] == StaffAuditModel::SICK_SUB_STATUS_NO){
                $builder->inWhere('a.sub_status', [StaffAuditModel::SICK_SUB_STATUS_NO, StaffAuditModel::SICK_SUB_STATUS_OVER]);
            }else{
                $builder->inWhere('a.sub_status', [StaffAuditModel::SICK_SUB_STATUS_YES]);
            }
        }
        //材料审批状态
        if(!empty($param['status'])){
            $builder->andWhere('c.status = :status:', ['status' => (int)$param['status']]);
        }
        //分页
        $builder->limit($pageSize, ($pageNum - 1) * $pageSize);

        //材料提交状态 倒序
        $builder->orderBy('c.created_at desc,a.created_at desc');
        $leaveData = $builder->getQuery()->execute()->toArray();

        if(empty($leaveData)){
            return $this->checkReturn(['data' => []]);
        }

        $leaveServer = new LeaveServer($this->lang, $this->timeZone);
        $addHour     = $this->config->application->add_hour;
        $auditRepo = new AuditlistRepository($this->lang, $this->timeZone);
        $staffInfo = $param['user_info'];
        $commitEnum = StaffAuditModel::$subState;
        //看有几个休息日
        $endDates = array_column($leaveData, 'leave_end_time');
        $startDay = min($endDates);//最早的结束日期
        $startDay = date('Y-m-d', strtotime($startDay));
        $endDay   = max($endDates);//最晚的结束日期
        $endDay   = date('Y-m-d', strtotime("{$endDay} +1 month"));//今天日期往后 推1个月
        $restPh   = $leaveServer->staff_off_days($staffInfo['id'], $startDay, $endDay);//ph 和 休息日
        $today    = date('Y-m-d');
        $sickServer = new SickServer($this->lang, $this->timeZone);
        $return = [];
        foreach ($leaveData as $da){
            [$leave_start_time, $leave_end_time] = $leaveServer->formatLeaveTime($da);
            $commitFlag = true;//是否能补充资料的按钮标志
            $lastDate = '';
            //没审批完成 不能添加 (要排除 没提交过材料的情况)
            if(!empty($da['c_status']) && $da['c_status'] == enums::$audit_status['panding']){
                $commitFlag = false;
            }
            //超时不能添加
            if($da['sub_status'] == StaffAuditModel::SICK_SUB_STATUS_OVER){
                $commitFlag = false;
            }else{
                $leaveEnd = date('Y-m-d', strtotime($da['leave_end_time']));
                $lastDate = $this->getRealRestDay($restPh, $leaveEnd, $sickServer->workDayLimit);//根据结束日期往后推到第一个工作日
                //今天已经超时了 可能还没跑完任务
                if($today > $lastDate){
                    $commitFlag = false;
                }
            }

            //审批流接口用的字段
            $row['audit_id']   = $da['cid'];
            $row['audit_type'] = enums::$audit_type['SICK_CERTIFICATE'];

            $row['leave_serial_no'] = $da['leave_serial_no'];//请假单编号
            $row['staff_name']      = "({$staffInfo['id']}){$staffInfo['name']}";//（工号）姓名
            $row['created_at']      = empty($da['created_at']) ? '' : date('Y-m-d H:i:s', strtotime($da['created_at']) + ($addHour * 3600));//材料提交时间
            $row['leave_day']       = $da['leave_day'];//请假天数
            $row['start_time']      = $leave_start_time;//开始时间
            $row['end_time']        = $leave_end_time;//结束时间
            $row['leave_id']        = $da['leave_id'];//请假 id
            $row['cid']             = $da['cid'];//审批cid
            $row['c_status']        = $da['c_status'];
            $row['c_status_text']   = $auditRepo->getAuditState($da['c_status']);//材料审批状态
            $row['sub_status']      = $da['sub_status'];
            $row['sub_status_text'] = $this->getTranslation()->_($commitEnum[$da['sub_status']]);
            $row['button_flag']     = (int)$commitFlag;//补充按钮状态 1 可提交  0 不能提交
            $row['under_text']      = '';
            //下面小字
            if($da['leave_day'] >=3 && $da['sub_status'] == StaffAuditModel::SICK_SUB_STATUS_NO){
                //>=3天 且 未提交病历证明材料 或 已提交材料但审批状态=已驳回、超时关闭，显示待提交时间“需在xxxx-xx-xx前补充材料”
                $row['under_text'] = $this->getTranslation()->_('sick_before_timeout',['date' => $lastDate]);//下面小字文案 超时
            }
            if($da['sub_status'] == StaffAuditModel::SICK_SUB_STATUS_OVER || (!empty($lastDate) && $today > $lastDate && $da['sub_status'] == StaffAuditModel::SICK_SUB_STATUS_NO)){
                //超时
                $row['under_text'] = $this->getTranslation()->_('sick_timeout');//下面小字文案
            }

            if(!$commitFlag && $da['c_status'] == enums::$audit_status['panding']){
                $row['under_text'] = $this->getTranslation()->_('sick_in_approving');//下面小字文案
            }

//            if($commitFlag){
//                $row['under_text'] = $this->getTranslation()->_('sick_before_timeout',['date' => $lastDate]);//下面小字文案 超时
//                //如果 小于3天 不展示
//                if($da['leave_day'] < 3){
//                    $row['under_text'] = '';
//                }
//            }else{
//                //没审批 您的病假材料正在审批中
//                if($da['c_status'] == enums::$audit_status['panding']){
//                    $row['under_text'] = $this->getTranslation()->_('sick_in_approving');//下面小字文案
//                }else{//超时
//                    $row['under_text'] = $this->getTranslation()->_('sick_timeout');//下面小字文案
//                }
//            }
            $return[] = $row;
        }

        return $this->checkReturn(['data' => $return]);


    }
    //看假期结束日期到今天 有几个休息日
    public function getRealRestDay($restDates, $start, $addDays){
        if(empty($restDates)){
            //没休息日 就推7天
            return date('Y-m-d',strtotime("{$start} +{$addDays} days"));
        }
        sort($restDates);
        $step = $start;
        $realAdd = 0;
        $endDate = date('Y-m-d', strtotime("{$start} +1 month"));

        while ($realAdd < $addDays && $step < $endDate){
            $step = date('Y-m-d', strtotime("{$step} +1 day"));//假期结束日期 +1 天开始
            if(!in_array($step, $restDates)){
                $realAdd++;
            }
        }
        $realDate = $step;
        return $realDate;
    }

    //病假维度的 材料申请详情页
    public function getSickCertificateDetail($param)
    {
        $auditId = $param['leave_id'];
        $data = $this->getAllCertificate($auditId);
        $oneData = $data[0];//获取请假基本信息用
        [$masterData, $subData] = $this->formatDetailData($data, $param);


        $isCertificate = true;//是否有上传过信息
        if(empty($oneData['cid'])){
            $isCertificate = false;
        }
        $auditListRe                 = new AuditlistRepository($this->lang, $this->timeZone);
        $headData['title']           = $auditListRe->getAudityType(AuditListEnums::APPROVAL_TYPE_SICK_CERTIFICATE);
        $headData['id']              = $isCertificate ? $masterData['cid'] : 0;
        $headData['staff_id']        = $oneData['staff_info_id'];
        $headData['type']            = AuditListEnums::APPROVAL_TYPE_SICK_CERTIFICATE;
        $headData['created_at']      = $isCertificate ? $masterData['created_at'] : '';
        $headData['status']          = $isCertificate ? $masterData['c_status'] : 0;
        $headData['status_text']     = $isCertificate ? $auditListRe->getAuditStatus('10'.$masterData['c_status']) : '';//材料状态
        $headData['sub_status']      = $oneData['sub_status'];
        $headData['sub_status_text'] = $this->getTranslation()->_(StaffAuditModel::$subState[$oneData['sub_status']] ?? '');
        $headData['serial_no']       = $isCertificate ? $masterData['serial_no'] : '';//材料编号
        $headData['leave_serial_no'] = $oneData['leave_serial_no'];//请假编号
        $headData['cancel_button']   = (!empty($masterData['c_status']) && $masterData['c_status'] == enums::$audit_status['panding']) ? true : false;
        $headData['other_info']      = [];
        $headData['avator']          = (new AuditlistRepository($this->lang, $this->timeZone))->getPersonAvator($param['staff_info_id']);
        if (!empty($subData)) {
            foreach ($subData as $sub) {
                $row['cid'] = $row['audit_id']  = $sub['cid'];
                $row['audit_type'] = enums::$audit_type['SICK_CERTIFICATE'];
                $row['serial_no']         = $sub['serial_no'];
                $row['c_status'] = $sub['c_status'];
                $row['c_status_text'] = $auditListRe->getAuditStatus('10'.$sub['c_status']) ?? '';
                $headData['other_info'][] = $row;
            }
        }

        $returnData['data']['head'] = $headData;//头信息

        //获取提交人用户信息
        $staff_info      = (new StaffRepository())->getStaffPosition($param['staff_info_id']);
        if($staff_info['sys_store_id'] == enums::HEAD_OFFICE_ID){
            $storeName = enums::HEAD_OFFICE;
        }else{
            $storeInfo       = SysStoreModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $staff_info['sys_store_id']],
            ]);
            $storeName = empty($storeInfo) ? '' : $storeInfo->name;
        }

        $leave_server = Tools::reBuildCountryInstance(new LeaveServer($this->lang, $this->timeZone), [$this->lang, $this->timeZone]);
        [$leave_start_time,$leave_end_time] = $leave_server->formatLeaveTime($oneData);
        $auditRe = new AuditRepository($this->lang);
        $typeName = $auditRe->typeName($oneData['audit_type'])[$oneData['leave_type']];

        //详情上半部分
        $detailLists['apply_parson']       = $staff_info['name']."({$staff_info['staff_info_id']})";
        $detailLists['department']         = $staff_info['depart_name'];
        $detailLists['dot']                = $storeName;
        $detailLists['leave_type']         = $typeName;
        $detailLists['start_time']         = $leave_start_time;
        $detailLists['end_time']           = $leave_end_time;
        $detailLists['days']               = $oneData['leave_day'];
        $detailLists['audit_leave_reason'] = $oneData['audit_reason'];

        $returnData['data']['detail'] = $this->format($detailLists);

        //详情页下面图片部分 {key : 证明, value:[图片],text:描述}
        $imageData = [];
        if(!empty($this->imageBody)){
            $tempData = [];
            foreach ($this->imageBody as $item){
                $key = SickServer::$sickImgCategory[$item['business_category']];
                $tempData[$key]['img'][] = $item['image_path'];
                if($key == 'other' && !empty($item['other_content'])){
                    $tempData[$key]['other_content'] = $item['other_content'];
                }
            }
            //再整理成 key value text
            foreach ($tempData as $k => $v){
                $row['key'] = $this->getTranslation()->_($k);
                $row['value'] = $v['img'];
                $row['text'] = $v['other_content'] ?? '';
                $imageData[] = $row;
            }
        }
        $returnData['data']['image_detail'] = $imageData;

        return self::checkReturn($returnData);
    }


    public function formatDetailData($data, $param){
        //详情页根据有没有传 证明id 判断 主体是否是 is new
        $masterData = $subData = [];
        $addHour     = $this->config->application->add_hour;
        foreach ($data as $da){
            //没申请过
            if(empty($da['cid'])){
                continue;
            }
            $row['cid'] = $da['cid'];
            $row['serial_no'] = $da['serial_no'];
            $row['created_at'] = empty($da['created_at']) ? '' : date('Y-m-d H:i:s', strtotime($da['created_at']) + ($addHour * 3600));
            $row['c_status'] = $da['c_status'];
            //点页面编号进来的
            if(!empty($param['cid']) && $da['cid'] == $param['cid']){
                $imgData = $this->getImgData($da['cid']);
                $this->imageBody = $imgData;
                $masterData = $row;
                continue;
            }
            //没点编号 用is new 当主体
            if(empty($param['cid']) && $da['is_new']){
                $imgData = $this->getImgData($da['cid']);
                $this->imageBody = $imgData;
                $masterData = $row;
                continue;
            }
            //其他数据
            $subData[] = $row;
        }
        return [$masterData, $subData];
    }


    //根据材料id 获取 只有一条记录
    public function getLeaveCertificateData($cid){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('a.audit_id,a.serial_no as leave_serial_no, a.staff_info_id,a.leave_start_time,
                            a.leave_start_type, a.leave_end_time,a.leave_end_type,a.leave_day,a.status,a.sub_status,
                            a.audit_reason, c.id as cid,c.status as c_status, c.is_new, c.other_content,c.created_at,c.serial_no');
        $builder->from(['a' => StaffAuditModel::class]);
        $builder->join(SickLeaveCertificateModel::class, "a.audit_id = c.audit_id and c.id = {$cid}", 'c');
        $data = $builder->getQuery()->execute()->getFirst();
        return $data;
    }

    //根据请假id  查询 所有的材料申请
    public function getAllCertificate($auditId){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('a.audit_id,a.serial_no as leave_serial_no, a.staff_info_id,a.leave_start_time,a.leave_start_type, 
                           a.leave_end_time,a.leave_end_type,a.leave_day,a.status,a.sub_status,a.audit_type,a.leave_type,
                           a.audit_reason, c.id as cid,c.status as c_status,c.serial_no,c.created_at, c.is_new,c.other_content
                           ');
        $builder->from(['a' => StaffAuditModel::class]);
        $builder->andWhere('a.audit_id = :audit_id:', ['audit_id' => (int)$auditId]);
        $builder->leftJoin(SickLeaveCertificateModel::class, "a.audit_id = c.audit_id", 'c');
//        $builder->leftJoin(SickLeaveCertificateDetailModel::class, "c.id = cd.cid", 'cd');

        $data = $builder->getQuery()->execute()->toArray();
        return $data;
    }


    public function statusEnums(){
        $res = [];
        $res['status'] = [
            ['code' => 0,'val' => $this->getTranslation()->_('is_order_all')],
            ['code' => 1,'val' => $this->getTranslation()->_('5104')],
            ['code' => 2,'val' => $this->getTranslation()->_('4017')],
            ['code' => 3,'val' => $this->getTranslation()->_('5105')],
            ['code' => 4,'val' => $this->getTranslation()->_('cancel')],
            ['code' => 5,'val' => $this->getTranslation()->_('closed')],
        ];
        //提交状态
        $res['sub_status'] = [
            ['code' => 0, 'val' => $this->getTranslation()->_('is_order_all')],
            ['code' => StaffAuditModel::SICK_SUB_STATUS_NO, 'val' => $this->getTranslation()->_('not_commit')],
            ['code' => StaffAuditModel::SICK_SUB_STATUS_YES, 'val' => $this->getTranslation()->_('commit')],
        ];

        return $this->checkReturn(['data' => $res]);
    }


}