<?php

namespace FlashExpress\bi\App\Server;
use Exception;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\TicketRepository;


class TicketServer extends BaseServer
{
    public $timezone;
    public $ticket;
    public $userInfo;

    public function __construct($lang, $timezone,$userInfo =null)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
        $this->ticket = new TicketRepository();
        if(!empty($userInfo)){
            $this->userInfo= $userInfo;
            $this->userInfo['avatar'] = (new StaffRepository())->getAvatar($this->userInfo['id'],"PROFILE_OBJECT_KEY");

        }

        //userInfo用到的字段
        //organization_type
        //organization_id=用来判断是不是it部门
        //id=用户id
        //name=用户名字
    }


    /**
     * IT工单 + 转交工单
     * @return array
     * @throws ValidationException
     */
    public function getNums()
    {
        $data = $this->ticket->getNums($this->userInfo);

        if($this->ticket->isDM($this->userInfo)){
            $data['is_dm'] = 1;
            $data['audit_num'] = $this->getCsNumSvc(1, $this->userInfo['id']);
        }
        return $this->checkReturn(["data"=>$data]);
    }


    public function getDefault(){
        $data = (new StaffRepository())->checkoutStaff($this->userInfo['id']);
        $res['mobile'] = "";
        $res['sys_store_id'] = "";
        $res['sys_store_name'] = "";
        if(!empty($data)){
            $res['mobile'] = $data['mobile_company']??"";
        }

        if (!empty($data) && $data['organization_type'] == 1 && !empty($data['organization_id'])) {
            $storeObj = SysStoreModel::findFirst([
                'conditions' => "id = :store_id:",
                'bind'       => [
                    'store_id' => $data['organization_id'],
                ],
                "columns" => 'id,name'
            ]);

            // 所属网点
            $res['sys_store_id']    = $data['organization_id'];
            $res['sys_store_name']  = (is_object($storeObj) && !empty($storeObj)) ? $storeObj->name : "";
        }

        if (!empty($data) && $data['organization_type'] == 2) { // 总部
            $res['sys_store_id']    = "-1";
            $res['sys_store_name']  = enums::HEAD_OFFICE;
        }

        return $this->checkReturn(["data"=>$res]);
    }

    public function add($paramIn){
        $item = $this->ticket->getItemType($paramIn['item_type']);
        if(!$item){
            return $this->checkReturn(["code"=>-3,"msg"=>"item type not exist"]);
        }

        $pics_str = $paramIn['pics']??"";
        if(!empty($pics_str)){
            $picArr = explode(",",$pics_str);
            if(count($picArr)>3){
                return $this->checkReturn(["code"=>-3,"msg"=>"pics more than three"]);
            }

            //3张图片的地址，应该不会超过1000
            if(strlen($pics_str)>1000){
                return $this->checkReturn(["code"=>-3,"msg"=>"pics len more than one hundreds"]);
            }
        }


        $key = "by_ticket_".$this->userInfo['id']."_add_flag";
        $redis = $this->getDI()->get('redisLib');
        $val = $redis->get($key);
        //已经存在
        if(!empty($val)){
            return $this->checkReturn(["code"=>-3,"msg"=>$this->getTranslation()->_("ticket_repeat_msg")]);
        }else{
            //不存在立即加锁
            $redis->set($key,1,60);
        }


        $userInfo = $this->userInfo;
        //网点
        if($userInfo['organization_type']==1){
            $paramIn['created_store_id'] = $userInfo['organization_id'];
            $paramIn['created_department_id'] = $userInfo['department_id'];
            $paramIn['created_store_name'] ="";
        }else{
            //部门
            $paramIn['created_store_id'] = -1;
            $paramIn['created_department_id'] = $userInfo['organization_id'];
            $paramIn['created_store_name'] ="Head Office";
        }

        //泰国时间
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $paramIn['created_at'] = gmdate("Y-m-d H:i:s",time()+$add_hour * 3600);
        $paramIn['updated_at'] = $paramIn['created_at'];
        $paramIn['created_id'] = $userInfo['id'];
        $paramIn['created_name'] = $userInfo['name'];
        $paramIn['created_job_title_id'] = $userInfo['job_title'];
        $paramIn['created_job_title_name'] = "";
        $paramIn['created_department_name'] = "";
        $paramIn['pics'] = $pics_str;

        $nums = $this->ticket->getNumByDay(gmdate("Y-m-d", time()+$add_hour * 3600));
        $nums++;
        $country_code = strtoupper(env('country_code', 'Th'));
        $country_code = substr($country_code, 0, 2);
        $paramIn['ticket_id'] = $country_code . gmdate("Ymd", time()+$add_hour * 3600) . str_pad($nums, 4,0, STR_PAD_LEFT);
        if(!empty($paramIn['created_job_title_id'])){
            $temp = (new StaffRepository())->getStaffInfoById($userInfo['id']);
            if(!empty($temp)){
                $paramIn['created_job_title_name'] = $temp['job_name']??"";
                if($paramIn['created_store_id']!=-1){
                    $paramIn['created_store_name'] = $temp['name']??"";
                }
            }
        }

        if(!empty($paramIn['created_department_id'])){
            $paramIn['created_department_name'] = (new DepartmentRepository())->getDepartmentNameById($paramIn['created_department_id']);
        }


        $ticket_id = $this->ticket->add($paramIn);
        if(!empty($ticket_id)){
            $data = [];
            $data['created_id'] = $paramIn['created_id'];
            $data['created_at'] = $paramIn['created_at'];
            $data['created_type'] = 1;
            $data['mark'] = $paramIn['info'];
            $data['avatar'] = $this->userInfo['avatar'];
            $this->ticket->addLog($ticket_id,$data);
            return $this->checkReturn(1);
        }else{
            $redis->del($key);
            return $this->checkReturn(0);
        }
    }

    /**
     * 系统增加工单
     * @param $paramIn
     * @return bool
     */
    public function addBySystem($paramIn){
        try{
            $item = $this->ticket->getItemType($paramIn['item_type']);
            if(!$item) throw  new \Exception('item_type not found');
            $userInfo = $this->userInfo;
            $add_hour = $this->getDI()['config']['application']['add_hour'];
            $paramIn['created_at'] = gmdate("Y-m-d H:i:s",time()+$add_hour*3600);
            $paramIn['updated_at'] = $paramIn['created_at'];
            $paramIn['created_id'] = $userInfo['id'];
            $paramIn['created_name'] = $userInfo['name'];

            $nums = $this->ticket->getNumByDay(gmdate("Y-m-d", time()+$add_hour * 3600));
            $nums++;
            $country_code = strtoupper(env('country_code', 'Th'));
            $country_code = substr($country_code, 0, 2);
            $paramIn['ticket_id'] = $country_code . gmdate("Ymd", time()+$add_hour * 3600) . str_pad($nums, 4, 0, STR_PAD_LEFT);

            $ticket_id = $this->ticket->add($paramIn);
            if(!empty($ticket_id)){
                $data = [];
                $data['created_id'] = $paramIn['created_id'];
                $data['created_at'] = $paramIn['created_at'];
                $data['created_type'] = 1;
                $data['mark'] = $paramIn['info'];
                $data['avatar'] = $this->userInfo['avatar'];
                $this->ticket->addLog($ticket_id,$data);
            }else{
                throw new \Exception("insert table ticket fail√ ");
            }
            return true;
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('ticket_server error '.$e->getMessage() ,'error');
            return false;
        }

    }

    public function list($paramIn,$audit=false,$sort=0){
        if($audit){
            $flag = $this->ticket->isIt($this->userInfo);
            if(!$flag){
                return $this->checkReturn(0);
            }
        }

        $data = $this->ticket->list($paramIn,$audit,$this->userInfo['id'],$sort);
        $deviceStoreIds = array_column($data['dataList'],'device_store_id');
        $createdStoreIds = array_column($data['dataList'],'created_store_id');
        $allStoreIds = array_merge($deviceStoreIds,$createdStoreIds);
        $storeUniqueIds = array_values(array_unique(array_filter($allStoreIds)));
        $storeList = [];
        if ($storeUniqueIds) {
            $storeDB = SysStoreModel::find([
                'conditions' => "id IN ({store_ids:array})",
                'bind'       => [
                    'store_ids' => $storeUniqueIds,
                ],
                "columns" => 'id,name'
            ])->toArray();
            if (!empty($storeDB)) {
                $storeList = array_column($storeDB,null,'id');
            }
        }

        foreach ($data['dataList'] as $k=>$item){
            $data['dataList'][$k]['device_store_name'] = "";

            if ('-1' == $item['device_store_id']) {
                $data['dataList'][$k]['device_store_name'] = enums::HEAD_OFFICE;
            }

            // 设备所在网点名称
            if (!empty($storeList[$item['device_store_id']]['name'])) {
                $data['dataList'][$k]['device_store_name'] = trim($storeList[$item['device_store_id']]['name']);
            }

            // 如果设备所在网点为空，则返回申请人所在网点
            if (empty($storeList[$item['device_store_id']]) && !empty($storeList[$item['created_store_id']]['name'])) {
                $data['dataList'][$k]['device_store_name'] = trim($storeList[$item['created_store_id']]['name']);
            }

            // 如果是系统提交时 设备网点展示字符 "-"
            if ($item['created_id'] == '10003') {   // 系统自动提交
                $data['dataList'][$k]['device_store_name'] = "-";
            }
            $this->handleData($data['dataList'][$k]);
        }

        return $this->checkReturn(["data"=>$data]);
    }


    public function detail($id,$audit=false,$is_log=true){
        if($audit){
            $flag = $this->ticket->isIt($this->userInfo);
            if(!$flag){
                return $this->checkReturn(0);
            }
        }

        $item = $this->ticket->detail($id,$audit,$this->userInfo['id']);
        if(empty($item)){
            return $this->checkReturn(0);
        }

        //OA带logs直接返回,backyard不用
        if($is_log){
            $item['logs'] = $this->ticket->getLog($item['id']);
            if ($item['pics']) {
                $pics = explode(',', $item['pics']);
                foreach ($pics as $pic) {
                    $item['logs'] = array_merge($item['logs'], [[
                        'avatar' => '',
                        'created_at' => $item['created_at'],
                        'created_id' => $item['created_id'],
                        'created_type' => '1',
                        'id' => $item['id'],
                        'is_pic' => '1',
                        'mark' => $pic,
                        'status' => '1',
                        'ticket_id' => $item['id']
                    ]]);
                }
            }
            $t = $this->getTranslation();
            $allNUm = count($item['logs']);
            foreach ($item['logs'] as $k=>$v){
                $item['logs'][$k]['file_name'] = "";

                if($v['is_pic']==1){
                    $item['logs'][$k]['file_name'] = ($allNUm-$k).".".$this->getExtension($v['mark']);
                }

                if($v['mark'] === 'system_close_it_order'){
                    $item['logs'][$k]['mark'] = $t->_('system_close_it_order');
                }
            }
        }

        // 如果是系统提交时 设备网点展示字符 "-"
        if ($item['created_id'] == '10003') {   // 系统自动提交
            $item['device_store_name'] = "-";
        }

        $storeIds = [
            $item['created_store_id'],
            $item['device_store_id']
        ];
        $storeIds = array_values(array_unique($storeIds));
        $storeDB = SysStoreModel::find([
            'conditions' => "id IN ({store_ids:array})",
            'bind'       => [
                'store_ids' => $storeIds,
            ],
            "columns" => 'id,name'
        ])->toArray();
        if (!empty($storeDB)) {
            $storeList = array_column($storeDB,null,'id');

            // 设备所在网点
            if (!empty($storeList[$item['device_store_id']]['name'])) {
                $item["device_store_name"] = $storeList[$item['device_store_id']]['name'];
            }

            // 如果设备所在网点不存在，则默认返回申请人所在网点。
            if (empty($storeList[$item['device_store_id']]['name']) && !empty($storeList[$item['created_store_id']]['name'])) {
                $item["device_store_name"] = $storeList[$item['created_store_id']]['name'];
            }
        }

        if ('-1' == $item['device_store_id']) {
            $item["device_store_name"] = enums::HEAD_OFFICE;
        }

        $this->handleData($item);
        return $this->checkReturn(["data"=>$item]);
    }


    public function getLog($param,$audit){
        if($audit){
            $flag = $this->ticket->isIt($this->userInfo);
            if(!$flag){
                return $this->checkReturn(0);
            }
        }

        $item = $this->ticket->detail($param['id'],$audit,$this->userInfo['id']);
        if(empty($item)){
            return $this->checkReturn(0);
        }


        $pageNum = 1;
        if(!empty($param['page_num'])){
            $pageNum = intval($param['page_num']);
        }

        $pageSize = 10;
        if(!empty($param['page_size'])){
            $pageSize = intval($param['page_size']);
        }

        $data = $this->ticket->getLogs($item['id'],$pageNum,$pageSize);

        $data['dataList'] = array_reverse($data['dataList']);

        $is_right = 0;
        if($this->userInfo['id'] == $item['created_id']){
            $is_right = 1;
        }
        if ($item['pics'] && $pageNum == 1) {
            $pics = explode(',', $item['pics']);
            foreach ($pics as $pic) {
                $data['dataList'] = array_merge([[
                    'avatar' => '',
                    'created_at' => $item['created_at'],
                    'created_id' => $item['created_id'],
                    'created_type' => '1',
                    'id' => $item['id'],
                    'is_pic' => '1',
                    'is_right' => $is_right,
                    'mark' => $pic,
                    'status' => '2',
                    'ticket_id' => $item['id']
                ]], $data['dataList']);
            }
        }
        $t = $this->getTranslation();
        foreach ($data['dataList'] as $k=>$v){
            $is_right = 0;
            //如果是员工，且当前是非审核
            if($v['created_type']==1 && !$audit){
                $is_right= 1;
            }

            //是IT部门，且是审核
            if($v['created_type']==2 && $audit){
                $is_right= 1;
            }

            //如果是我提交的，在右边
            if($this->userInfo['id'] == $v['created_id']){
                $is_right = 1;
            }
            $data['dataList'][$k]['is_right'] = "".$is_right;

            if($v['mark'] === 'system_close_it_order'){
                $data['dataList'][$k]['mark'] = $t->_('system_close_it_order');
            }

            if(!empty($v['avatar'])){
                $data['dataList'][$k]['avatar'] =env("img_prefix").$v['avatar'];
            }else{
                $data['dataList'][$k]['avatar'] = "";
            }
        }
        return $this->checkReturn(["data"=>$data]);
    }







    public function reply($paramIn,$status=2){
        $audit = false;
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        if($this->ticket->isIt($this->userInfo)){
            $audit = true;
        }

        $item = $this->ticket->detail($paramIn['id'],$audit,$this->userInfo['id']);
        if(empty($item)){
            //未找到该工单或你无权限
            return $this->checkReturn(["code"=>-3,"msg"=>"not have auth"]);
        }

        if($item['status']==enums::TICKET_STATUS_CLOSED){
            //该工单已关闭
            return $this->checkReturn(["code"=>-3,"msg"=>"have already close"]);
        }

        //如果是自己评论自己，
        if($item['created_id']==$this->userInfo['id']){
            $audit = false;
        }

        if(empty($paramIn['is_pic'])){
            $paramIn['is_pic'] = 0;
        }else{
            $paramIn['is_pic'] = 1;
        }

        $data = [];
        //$data['ticket_id'] = $paramIn['id'];
        $data['created_id'] = $this->userInfo['id'];
        $data['created_type'] = $audit?2:1;
        $data['mark'] = $paramIn['mark'];
        $data['created_at'] = gmdate("Y-m-d H:i:s",time() + $add_hour * 3600);
        $data['status'] = $status;//1提交，2回复，3关闭
        $data['is_pic'] = $paramIn['is_pic'];
        $data['avatar'] = $this->userInfo['avatar'];

        $res= $this->ticket->addLog($paramIn['id'],$data);
        if($res){
            //如果关闭
            if($status==3){
                $item_status = 3;
            }else{
                //审核的话，已回复，否则待回复
                $item_status = $audit?2:1;  //如果是审核，就是已回复，否则就是待回复
            }

            $updateData = ["status"=>$item_status];
            if($audit){
                if(empty($item['first_deal_id'])){
                    $updateData['first_deal_id'] = $this->userInfo['id'];
                    $updateData['first_deal_name'] = $this->userInfo['name'];
                }
                $updateData['deal_id'] = $this->userInfo['id'];
                $updateData['deal_name'] = $this->userInfo['name'];
                $updateData['updated_at'] = gmdate("Y-m-d H:i:s",time() + $add_hour * 3600);
            }

            $this->ticket->update($paramIn['id'],$updateData);

            if($status == 3 && $item['item_type'] ==5){
                $this->ticket->closeEmail($item['id']);
            }
        }
        return $this->checkReturn(1);
    }

    public function batchClose($paramIn){
        $ids = $paramIn['ids'];
        $idArr = explode(",",$ids);
        $add_hour = $this->getDI()['config']['application']['add_hour'];

        if(!$this->ticket->isIt($this->userInfo)){
            //不是IT部门
            return $this->checkReturn(['code'=>-3,"msg"=>"not it"]);
        }


        //如果只有一个
        if(count($idArr)==1){
            //走一个关闭接口
            return $this->reply(["id"=>$idArr[0],"mark"=>$paramIn['mark']],3);
        }

        if(!$this->ticket->isAll($idArr)){
            //工单数不对
            return $this->checkReturn(['code'=>-3,"msg"=>"num is error"]);
        }

        $flag = $this->ticket->batchClose($idArr,$paramIn['mark'],$this->userInfo['id'],gmdate("Y-m-d H:i:s",time() + $add_hour * 3600));
        if($flag===true){
            return $this->checkReturn(1);
        }else{
            return $this->checkReturn(['code'=>-3,"msg"=>"error"]);
        }
    }

    public function getItemType(){
        $temp = $this->ticket->getItemTypes();
        $data = [];
        $lang = $this->lang;
        foreach ($temp as $k=>$v){
            $tmp = [];
            $tmp['id'] = $v['id'];
            $tmp['name'] = $this->getTranslation()->_(enums::$it_ticket_item_type[$v['id']]);    //$v[$lang];//上一版用数据表字段，当前版从翻译系统取
            $data[] = $tmp;
        }
        return $this->checkReturn(["data"=>$data]);
    }

    private function handleData(&$item){
        $temp = $this->getTranslation();
        $item['mobile'] = $item['mobile']??"";
        $item['status_text'] = $temp[enums::$ticket_status[$item['status']]];
        $item['item_type_text'] = $this->getItemTypeById($item['item_type']);

        if(!empty($item['pics'])){
            $item['pics'] = explode(",",$item['pics']);
        }else{
            $item['pics'] = [];
        }
        $item['avatar'] = $this->userInfo['avatar'];
        $item['now_user_id'] = $this->userInfo['id'];
    }

    private function getExtension($file_name){
        $parts = explode(".", basename($file_name));
        return end($parts);
    }

    private function getItemTypeById($id){
        static $arr = [];
        if(empty($arr)){
            $temp = $this->ticket->getItemTypes();
            $arr =array_column($temp,null,"id");
        }
        if ($this->lang == 'zh') {
            $this->lang = 'zh-CN';
        }
        return $arr[$id][$this->lang]??'';
    }

    ####################cs ticket list #####################

    /**
     * 获取工单列表
     * @param $paramIn
     * @return array
     * @throws Exception
     */
    public function getCsList($paramIn): array
    {
        if (empty($paramIn['page_num'])) {
            $paramIn['page_num'] = 1;
        }

        if (empty($paramIn['page_size'])) {
            $paramIn['page_size'] = 20;
        }
        if(empty($paramIn['status'])) {
            $paramIn['status'] = 1;
        }
        $pageNum = intval($paramIn['page_num']);
        $pageSize = intval($paramIn['page_size']);

        $bi_rpc = (new ApiClient('bi_rpcv2','','workorder.get_work_order_list', $this->lang));
        $bi_params['user_id'] = $this->userInfo['id'];
        $bi_params['status'] = $paramIn['status'];
        $bi_params['page_num'] = $pageNum;
        $bi_params['page_size'] = $pageSize;
        $bi_rpc->setParams($bi_params);
        $bi_return = $bi_rpc->execute();
        if(isset($bi_return['error']) || !isset($bi_return['result']) || $bi_return['result']['code'] != 1){
            $this->getDI()->get('logger')->write_log("get_cs_list_svc_error:params" . json_encode($bi_params) . ', result: ' . json_encode($bi_return), 'notice');
            throw new ValidationException($this->getTranslation()->_('server_error'));
        }
        return $this->checkReturn(['data' => $bi_return['result']['data']]);
    }

    public function isDM()
    {
        return  $this->ticket->isDM($this->userInfo);
    }
    /**
     * 获取推送工单枚举
     * @return array|mixed
     * @throws Exception
     */
    public function getOrderType(){
        $redisKey = 'ticket_order_type_const';
        $redis_obj = $this->getDI()->get('redis');
        $rst = $redis_obj->get($redisKey);
        if( empty( $rst ) ){
            $bi_rpc = (new ApiClient('bi_rpcv2','','workorder.get_work_ordertype', $this->lang));
            $bi_params['task_id'] = random_int(1,99999);
            $bi_rpc->setParams($bi_params);
            $bi_return = $bi_rpc->execute();
            if($bi_return['result']['code'] != 1){
                $this->getDI()->get('logger')->write_log("exception:getOrderTypeRequest:" .json_encode($bi_params)."===get_work_ordertype:". $bi_return, 'error');
                return [];
            }
            $redis_obj->save($redisKey, $bi_return['result']['data'], 86400);
            return  $bi_return['result']['data'];
        }
        return empty( $rst ) ? [] : $rst;
    }

    public function getCsReply($order_id)
    {
        try{
            $bi_rpc = (new ApiClient('bi_rpcv2','','workorder.work_order_info', $this->lang));
            $bi_params['user_id'] = $this->userInfo['id'];
            $bi_params['user_name'] = $this->userInfo['name'];
            $bi_params['order_id'] = $order_id;
            $bi_rpc->setParams($bi_params);
            $bi_return = $bi_rpc->execute();
            if(isset($bi_return['error']) || !isset($bi_return['result']) || $bi_return['result']['code'] != 1){
                $this->getDI()->get('logger')->write_log("get_cs_reply_detail_svc_error:params" . json_encode($bi_params) . ', result: ' . json_encode($bi_return), 'notice');
                throw new ValidationException($this->getTranslation()->_('server_error'));
            }

            return $this->checkReturn(["data"=>$bi_return['result']['data']]);
        }catch(Exception $e){
            $this->getDI()->get('logger')->write_log("getCsReply:" . $e->getMessage(), 'info');
            return $this->checkReturn(-3,$e->getMessage());
        }

    }

    public function replyCs($params)
    {
        try{
            $img_arr = $params['img_arr'] ?? [];
            $bi_rpc = (new ApiClient('bi_rpc','','saveWorkOrderReply', $this->lang));
            $bi_params['order_id'] = $params['id'];
            $bi_params['staff_id'] = $this->userInfo['id'];
            $bi_params['content'] = $params['content'];
            $bi_params['img_arr'] = $img_arr;
            $bi_rpc->setParams($bi_params);
            $bi_return = $bi_rpc->execute();

            if(isset($bi_return['error']) || !isset($bi_return['result']) || $bi_return['result']['code'] != 1){
                $this->getDI()->get('logger')->write_log("reply_cs_svc_error:params" . json_encode($bi_params) . ', result: ' . json_encode($bi_return), 'notice');
                throw new ValidationException($this->getTranslation()->_('server_error'));
            }

            return $this->checkReturn(1);
        }catch(Exception $e){
            $this->getDI()->get('logger')->write_log("ReplyCs:" . $e->getMessage(), 'info');
            return $this->checkReturn(-3,$e->getMessage());
        }
    }

    /**
     * 获取cs工单未回复数量
     * @param $state
     * @param $staffId
     * @return mixed
     * @throws ValidationException
     */
    public function getCsNumSvc($state, $staffId)
    {
        $bi_rpc = (new ApiClient('bi_rpcv2','','workorder.get_work_order_num', $this->lang));
        $bi_params['user_id'] = $staffId;
        $bi_params['status'] = $state;
        $bi_rpc->setParams($bi_params);
        $bi_return = $bi_rpc->execute();
        if(isset($bi_return['error']) || !isset($bi_return['result']) || $bi_return['result']['code'] != 1){
            $this->getDI()->get('logger')->write_log("get_cs_num_svc_error:params" . json_encode($bi_params) . ', result: ' . json_encode($bi_return), 'notice');
            return 0;
        }

        return $bi_return['result']['data']['audit_num'];
    }


}
