<?php
// CEO 信箱 2.0 版本 2020.11

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\CeoMailEnums;
use Exception;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\CeoMailProblemCategoryModel;
use FlashExpress\bi\App\Models\backyard\CeoMailProblemCategoryRelationModel;
use FlashExpress\bi\App\Models\backyard\CeoMailStaffProblemOrderModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffConsentAgreementLogModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\CeoMailProblemCategoryRepository;
use FlashExpress\bi\App\Repository\JobTitleRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Models\backyard\CeoMailFollowModel;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Repository\SysManageRepository;

class CeoMailServer extends BaseServer
{
    public $timezone;

    // 泰国时间
    public $th_datetime = '';

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $this->th_datetime = gmdate('Y-m-d H:i:s', time() + $add_hour * 3600);
    }


    /**
     * 验证员工对指定分类是否需要同意协议
     * @param $params
     * @return string
     * @throws BusinessException
     */
    public function checkIsConfirmByCategoryId($params)
    {
        $agreement = '';
        if (empty($params['category_id']) || empty($params['staff_info_id'])) {
            throw new BusinessException($this->getTranslation()->_('miss_args'));
        }

        if (!in_array($params['category_id'], CeoMailEnums::NEED_AGREE_CATEGORY_ID)) {
            return false;
        }
//
//        $model = StaffConsentAgreementLogModel::findFirst([
//            "conditions" => " staff_info_id = :staff_info_id: and type = :type: and related_id  = :related_id:",
//            'bind'       => [
//                'staff_info_id' => $params['staff_info_id'],
//                'type'          => StaffConsentAgreementLogModel::TYPE_FLASH_BOX,
//                'related_id'    => $params['category_id'],
//            ],
//        ]);
//        if ($model) {
//            return $agreement;
//        }

        return $this->getTranslation()->_('flash_box_agreement_category_'.$params['category_id']);
    }

    /**
     * 同意协议
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function saveConsentAgreement($params): bool
    {
        if (empty($params['category_id']) || empty($params['staff_info_id'])) {
            throw new BusinessException($this->getTranslation()->_('miss_args'));
        }

        return true;
        if (!in_array($params['category_id'], CeoMailEnums::NEED_AGREE_CATEGORY_ID)) {
        }
        $model = StaffConsentAgreementLogModel::findFirst([
            "conditions" => " staff_info_id = :staff_info_id: and type = :type: and related_id  = :related_id:",
            'bind'       => [
                'staff_info_id' => $params['staff_info_id'],
                'type'          => StaffConsentAgreementLogModel::TYPE_FLASH_BOX,
                'related_id'    => $params['category_id'],
            ],
        ]);
        if ($model) {
            return true;
        }
        $model                = new StaffConsentAgreementLogModel();
        $model->staff_info_id = $params['staff_info_id'];
        $model->type          = StaffConsentAgreementLogModel::TYPE_FLASH_BOX;
        $model->related_id    = $params['category_id'];
        $model->content       = $this->getTranslation()->_('flash_box_agreement_category_'.$params['category_id']);
        return $model->save();
    }

    /**
     * 获取问题分类列表
     *
     * @param  $params
     * @return array $data
     */
    public function getCategoryList($params)
    {
        $lang = $this->getLang();
        $columns = [
            'id AS category_id',
            'category_name_' . $lang . ' AS category_name',
            'category_desc_' . $lang . ' AS category_desc',
            'parent_id',
        ];
        //查看支援信息 换成主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timeZone))->getSupportInfoBySubStaff($params['staff_id']);
        if (!empty($supportStaffInfo)) {
            $params['staff_id'] = $supportStaffInfo['staff_info_id'];
        }

        $staffInfo = (new StaffRepository())->getStaffInfoOne($params['staff_id']);
        if(empty($staffInfo)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//未找到数据
        }

        $conditions = 'mobile_status = :mobile_status:';
        $bind['mobile_status'] = CeoMailProblemCategoryModel::MOBILE_STATE_YES;

        if((new StaffServer())->isLntStaff($params['staff_id'])) {
            $relation['module_type'] = CeoMailProblemCategoryRelationModel::MODULE_TYPE_LNT;
            $categoryIds = CeoMailProblemCategoryRepository::getCategoryRelationList($relation, true);
            if (empty($categoryIds)) {
                return [];
            }
            $conditions .= ' and id in ({ids:array})';
            $bind['ids'] = $categoryIds;
        } else {
            //区分 个人代理。
            $use_type = in_array($staffInfo['hire_type'],
                HrStaffInfoModel::$agentTypeTogether) ? CeoMailProblemCategoryModel::USE_TYPE_INDEPENDENT : CeoMailProblemCategoryModel::USE_TYPE_FORMAL;
            $conditions .= ' and use_type in ({use_types:array})';
            $bind['use_types'] = [CeoMailProblemCategoryModel::USE_TYPE_PUBLIC, $use_type];
        }

        $order = 'parent_id ASC, sort_index DESC';

        $list = CeoMailProblemCategoryRepository::getCategoryList($columns, $conditions, $bind, $order);

        $data = [];
        $read_commitment_category_ids = $this->getCommitmentCategoryInfo();

        //找出显示的父类ID
        $displayParentCategoryId = [];
        foreach ($list as $value) {
            if ($value['parent_id'] == 0) {
                $displayParentCategoryId[] = $value['category_id'];
            }
        }

        /**
         * 如果 父类下的 子类 存在 个人代理，则父类 use_type 要改为 公共类型
         */
        foreach ($list as $value) {
            $parent_id = $value['parent_id'];
            unset($value['parent_id']);

            // 该分类是否需阅读信息承诺书
            $value['is_read_commitment'] = 0;
            if (in_array($parent_id, $read_commitment_category_ids) || in_array($value['category_id'], $read_commitment_category_ids)) {
                $value['is_read_commitment'] = 1;
            }

            if ($parent_id == 0) {
                $value['sub_category'] = [];
                $data[$value['category_id']] = $value;
            } else {
                if (!in_array($parent_id, $displayParentCategoryId)) {
                    continue;
                }
                unset($value['category_desc']);
                $data[$parent_id]['sub_category'][] = $value;
            }

        }

        $compensationCategory = $this->getCompensationCategory($params);
        //如果是工资条，或者提成消息，返回 关于薪酬 子类
        if(!empty($params['source']) && $params['source'] == 'salary') {
            if(!empty($data[$compensationCategory]['sub_category'])) {
                return $data[$compensationCategory]['sub_category'];
            }
        }

        //如果是invoice 返回 network 提成下的
        $compensationCategory = $this->getInvoiceCategory();

        if(!empty($params['source']) && $params['source'] == 'invoice') {
            if(!empty($data[$compensationCategory]['sub_category'])) {
                return $data[$compensationCategory]['sub_category'];
            }
        }

        //如果是contract 返回 电子合同下
        $compensationCategory = $this->getContractCategory(in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether));

        if(!empty($params['source']) && $params['source'] == 'contract') {
            if(!empty($data[$compensationCategory]['sub_category'])) {
                return $data[$compensationCategory]['sub_category'];
            }
        }

        //如果是contract 返回 电子合同下
        $compensationCategory = $this->getVoucherCategory();

        if(!empty($params['source']) && $params['source'] == 'voucher') {
            if(!empty($data[$compensationCategory]['sub_category'])) {
                return $data[$compensationCategory]['sub_category'];
            }
        }

        //如果是update_info，返回关于更改信息分类
        $compensationCategory = $this->getUpdateInfo();

        if(!empty($params['source']) && $params['source'] == 'update_info') {
            if(!empty($data[$compensationCategory]['sub_category'])) {
                return $data[$compensationCategory]['sub_category'];
            }
        }
        //incentive 关于提成
        $incentiveCategory = $this->getIncentiveCategory();
        if (!empty($params['source']) && $params['source'] == 'incentive') {
            if (!empty($data[$incentiveCategory]['sub_category'])) {
                return $data[$incentiveCategory]['sub_category'];
            }
        }

        $data = array_values($data);
        unset($list);

        return $data;
    }

    /**
     *
     */
    public function getVoucherCategory()
    {
        return CeoMailEnums::ABOUT_INDEPENDENT_VOUCHER_CATEGORY_ID;
    }

    /**
     * 获取 关于薪酬分类
     * @param array $params
     * @return int
     */
    public function getCompensationCategory($params = [])
    {
        return CeoMailEnums::ABOUT_COMPENSATION_CATEGORY_ID;
    }

    /**
     *获取关于更改信息分类
     */
    public function getUpdateInfo()
    {
        return CeoMailEnums::ABOUT_UPDATE_INFO;
    }
    /**
     *获取关于提成分类
     * th 使用 薪酬
     *
     */
    public function getIncentiveCategory(): int
    {
        return CeoMailEnums::ABOUT_COMPENSATION_CATEGORY_ID;
    }

    /**
     * 获取 network 提成
     * @return int
     */
    public function getInvoiceCategory()
    {
        return 0;

    }

    /**
     * 获取电子合同
     * @param $isIndependent
     * @return int
     */
    public function getContractCategory($isIndependent = false)
    {
        if(!isCountry()) {
            return 0;
        }
        return $isIndependent ? CeoMailEnums::ABOUT_INDEPENDENT_CONTRACT_CATEGORY_ID : CeoMailEnums::ABOUT_CONTRACT_CATEGORY_ID;
    }

    /**
     * 获取需要阅读承诺书的信息
     * @return array
     */
    public function getCommitmentCategoryInfo()
    {
       return CeoMailEnums::MUST_READ_INFORMATION_COMMITMENT_CATEGORY_IDS;
    }

    /**
     * 获取问题分类信息
     *
     * @param string $category_id
     * @return array $data
     */
    public function getCategoryInfo(string $category_id)
    {
        $lang = $this->getLang();

        $field = 'category_name_' . $lang . ' AS category_name';
        $sql = "
              -- 获取问题分类列表
              SELECT 
                $field
              FROM 
                ceo_mail_problem_category
              WHERE 
                id = :id
              ";
        $query_param = [
            'id' => $category_id,
        ];

        return $this->getDI()->get('db_rby')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
    }


    /**
     * 获取问题工单列表
     *
     * @param array $param
     * @return array $data
     */
    public function getProblemOrderList(array $param)
    {
        $staff_id = $param['staff_id'];
        //查看支援信息
        //如果是支援账号，则找到其 主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($param['staff_id']);
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            $staff_id = $supportStaffInfo['staff_info_id'];
        }

        $result = [
            'is_over' => 1,
            'list' => [],
        ];
        if (empty($staff_id)) {
            return $result;
        }

        $page = $param['page'] ?? 1;
        $page_length = $param['page_num'] ?? 20;
        $start_page = ($page - 1) * $page_length;

        // 获取该渠道的问题分类ID清单
        $category_id_item = $this->getCategoryIdList(1);

        if(empty($category_id_item)) {
            $result['is_over'] = 1;
            $result['list'] = [];
            return $result;
        }

        $category_ids = implode(',', array_keys($category_id_item));

        $db = $this->getDI()->get('db_rby');

        // 查询参数
        $query_param = [
            'staff_id' => $staff_id,
        ];

        // 获取总数
        $count_sql = "
                -- 获取员工问题工单总数
                SELECT COUNT(id) FROM ceo_mail_staff_problem_order WHERE staff_id = :staff_id AND problem_category_v2 IN ($category_ids)     
            ";
        $count = $db->fetchColumn($count_sql, $query_param);
        if ($count < 1) {
            return $result;
        }


        // 获取指定员工/指定渠道的问题工单列表
        $sql = "
            -- 获取员工指定分类的问题工单列表
            SELECT 
                id, problem_no, problem_desc, problem_image, problem_status, is_evaluate, latest_feedback_id, latest_feedback_time AS create_time,problem_category_v2 as problem_category
            FROM 
                ceo_mail_staff_problem_order
            WHERE
                staff_id = :staff_id AND problem_category_v2 IN ($category_ids)     
            ORDER BY latest_feedback_time DESC
            LIMIT $start_page, $page_length
            ";
        $data = $db->query($sql, $query_param)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        // 获取员工最新的反馈信息
        $latest_feedback_id_item = array_column($data, 'latest_feedback_id');
        $latest_feedback_info_list = $this->getFeedbackListByIds($latest_feedback_id_item);
        if (!empty($latest_feedback_info_list)) {
            $latest_feedback_info_list = array_column($latest_feedback_info_list, null, 'id');
        }

        // 获取问题工单的分类
        $category_list = $this->getCategoryIdListFromCache();
        $t = $this->getTranslation();
        foreach ($data as $key => &$val) {
            $val['problem_category_text'] = $category_list[$val['problem_category']]??'';
            // 已评价 == 已完成
            // 超时未反馈 == 已完成
            if ($val['is_evaluate'] || $val['problem_status'] == 3) {
                $val['problem_status'] = 2;
            }
            //关闭处理中 用户侧 变成 已回复
            if($val['problem_status'] == 4){
                $val['problem_status'] = 1;
            }

            // 获取最新反馈的描述/图片
            $latest_problem_image = $latest_feedback_info_list[$val['latest_feedback_id']]['problem_image'] ?? '';
            $latest_problem_desc = $latest_feedback_info_list[$val['latest_feedback_id']]['problem_desc'] ?? '';
            if (empty($latest_problem_image) && empty($latest_problem_desc)) {
                $latest_problem_image = $val['problem_image'];
                $latest_problem_desc = $val['problem_desc'];
            }

            $val['problem_image'] = $latest_problem_image ? explode(',', $latest_problem_image) : [];
            $val['problem_desc'] = $latest_problem_desc;

            //系统侧回复超时，用户侧展示待回复
            if($val['problem_status'] == CeoMailEnums::HAVE_TIMED_OUT_SYS_STATUS) {
                $val['problem_status'] = CeoMailEnums::HAVE_NO_REPLY_STATUS;
            }
            // 问题工单状态翻译
            $val['problem_status_text'] = $t[CeoMailEnums::REPLY_STATUE_ITEM[$val['problem_status']]];

            unset($val['is_evaluate']);
            unset($val['latest_feedback_id']);

            $data[$key] = $val;
        }

        $result['is_over'] = $page * $page_length >= $count ? 1 : 0;
        $result['list'] = $data;
        return $result;
    }

    /**
     * 获取员工反馈列表
     * @param array $id_list
     * @return array $list
     */
    public function getFeedbackListByIds(array $id_list)
    {
        if (empty($id_list)) {
            return [];
        }

        $ids = implode(',', $id_list);
        $sql = "
            -- 获取员工反馈列表
            SELECT 
                id, content AS problem_desc, img_url AS problem_image
            FROM 
                mail_to_ceo
            WHERE
                id IN ($ids);        
        ";

        return $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 根据访问渠道, 获取问题分类ID列表
     *
     * @param string $access_channel
     * @return array $data
     */
    public function getCategoryIdList($display_status = 0)
    {
        $lang = $this->getLang();

        $field = 'category_name_' . $lang . ' AS category_name , id';

        $sql = "
                -- 根据信箱访问渠道, 获取对应的问题分类ID列表
                SELECT 
                    $field 
                FROM 
                    ceo_mail_problem_category WHERE 1 = :common
                  ";

        $query_param['common'] = 1;
        if($display_status){
            $query_param['display_status'] = $display_status;
            $sql .= "  AND display_status = :display_status";
        }


        $data = $this->getDI()->get('db_rby')->query($sql, $query_param)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return array_column($data, 'category_name','id');
    }

    /**
     * 问题工单创建
     * @param array $param
     * @throws Exception
     */
    public function problemOrderCreate(array $param): bool
    {
        $conditions = 'id = :problem_category: ';
        $bind = [
            'problem_category' => $param['problem_category'],
        ];
        $categoryData = CeoMailProblemCategoryRepository::getCategoryOne(['*'], $conditions, $bind);
        if(!empty($categoryData) && $categoryData['parent_id'] == 0) {
            throw new ValidationException($this->getTranslation()->_('data_error'));

        }

        $user_info = $param['staff_info'];

        //查看支援信息
        //如果是支援账号，则找到其 主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($user_info['staff_id']);
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            $user_info['staff_id'] = $supportStaffInfo['staff_info_id'];
        }
        // 获取员工基本信息
        $staff_base_info = $this->getStaffBaseInfo($user_info['staff_id'], 'name,node_department_id,mobile,formal');
        if (!empty($staff_base_info)) {
            $user_info['mobile']        = !empty($staff_base_info['mobile']) ? $staff_base_info['mobile'] : $user_info['mobile'];
            $user_info['department_id'] = $staff_base_info['node_department_id'];
            $user_info['name']          = $staff_base_info['name'];
        }

        //只有 formal 类型为 1，4 才可以提交
        if(!in_array($staff_base_info['formal'], [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN])) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }


        try {
            $problem_category = $param['problem_category'];
            $problem_desc = $param['problem_desc'];
            $problem_image = $param['problem_image'] ? implode(',', $param['problem_image']) : '';

            // 获取员工基本信息 和 部门信息
            $user_info['department_name'] = '';
            if (!empty($user_info['department_id'])) {
                // 获取部门名称
                $get_department_name = "
                    -- 获取部门名称
                  SELECT name FROM sys_department WHERE id = '{$user_info['department_id']}';";
                $user_info['department_name'] = $this->getDI()->get('db_rby')->fetchColumn($get_department_name);
            }

            array_walk($user_info, function (&$val) {
                $val = $val ? $val : '';
            });

            $curr_problem_no = $this->genProblemOrderNo();



            // * 存储为空字符串
            if (stripos($user_info['mobile'], '*') !== false) {
                $user_info['mobile'] = '';
            }

            // backyard数据处理
            $backyard_db = $this->getDI()->get('db');

            $backyard_db->begin();

            // 初始化工单第一条反馈消息数据
            $init_feedback_sql = "
                -- 初始化第一条反馈消息
                INSERT INTO mail_to_ceo
                    (problem_no, content, img_url, create_time, staff_id, staff_name, mobile, type_v2)                
                VALUES
                    (?, ?, ?, ?, ?, ?, ?, ?)    
                ";
            $init_feedback_data = [
                $curr_problem_no,
                $problem_desc,
                $problem_image,
                $this->th_datetime,
                $user_info['staff_id'],
                $user_info['name'],
                $user_info['mobile'],
                $problem_category
            ];
            $init_result = $backyard_db->execute($init_feedback_sql, $init_feedback_data);
            $last_insert_id = $backyard_db->lastInsertId();

            $sourceType = self::$flePlatform;

            // 创建工单
            $insert_sql = "
                -- 创建问题工单
                INSERT INTO ceo_mail_staff_problem_order
                    (problem_no, staff_id, staff_name, staff_mobile, avatar_url, problem_category_v2, problem_desc, problem_image, department_id, department_name, create_time, latest_feedback_id, latest_feedback_time, sys_category_id_v2, source_type)
                VALUES 
                    (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ";
            $insert_data = [
                $curr_problem_no,
                $user_info['staff_id'],
                $user_info['name'],
                $user_info['mobile'],
                is_null($user_info['avatar_url']) ? '': $user_info['avatar_url'],
                $problem_category,
                $problem_desc,
                $problem_image,
                $user_info['department_id'] ? $user_info['department_id'] : 0,
                $user_info['department_name'],
                $this->th_datetime,
                $last_insert_id,
                $this->th_datetime,
                $problem_category,
                $sourceType,
            ];

            $order_result = $backyard_db->execute($insert_sql, $insert_data);
            if ($order_result && $init_result) {
                $backyard_db->commit();
                return true;
            } else {
                $backyard_db->rollback();
                throw new Exception($this->getTranslation()->_('4008'));
            }
        } catch (\Exception $e) {
            $backyard_db->rollback();
            $this->logger->write_log('CEO MAIL: problemOrderCreate - ', $e->getMessage(), 'ceo_mail_server');
            throw $e;
        }
    }

    /**
     * 校验：已存在相同类型问题的工单，工作人员正在加急回复中，请您耐心等待
     * @param $param
     * @return mixed
     * @throws ValidationException
     */
    public function problemOrderCreateCheck($param)
    {
        $conditions   = 'id = :problem_category: ';
        $bind         = [
            'problem_category' => $param['problem_category'],
        ];
        $categoryData = CeoMailProblemCategoryRepository::getCategoryOne(['*'], $conditions, $bind);
        if (!empty($categoryData) && $categoryData['parent_id'] == 0) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        $conditions = 'staff_id = :staff_id: and problem_category_v2 = :problem_category: and problem_status not in ({problem_status:array})';
        $bind       = [
            'staff_id'         => $param['staff_info']['staff_id'],
            'problem_category' => $param['problem_category'],
            'problem_status'   => [
                CeoMailStaffProblemOrderModel::PROBLEM_STATUS_COMPLETE,
                CeoMailStaffProblemOrderModel::PROBLEM_STATUS_STAFF_TIME_OUT,
            ],
        ];
        //如果存在相同分类，非已完成的工单，则 tips 提示：已存在相同类型问题的工单，工作人员正在加急回复中，请您耐心等待
        $orderData      = CeoMailProblemCategoryRepository::getOrderOne(['*'], $conditions, $bind);
        $data['result'] = !empty($orderData) ? true : false;

        return $data;
    }

    /**
     * 获取评价常用语
     */
    public function getEvaluateCommonWords()
    {
        $common_words = [];

        $lang = $this->getLang();
        switch ($lang) {
            case 'zh':
                $common_words = CeoMailEnums::EVALUATE_COMMON_WORDS_ZH;
                break;
            case 'th':
                $common_words = CeoMailEnums::EVALUATE_COMMON_WORDS_TH;
                break;
            case 'en':
                $common_words = CeoMailEnums::EVALUATE_COMMON_WORDS_EN;
                break;
        }

        return array_values($common_words);
    }

    /**
     * 生成问题工单号
     */
    public function genProblemOrderNo()
    {
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        return 'PNO' . mt_rand(10000, 99999) . gmdate('YmdHis', time() + $add_hour * 3600) . mt_rand(100000, 999999);
    }

    /**
     * 获取问题工单详情 - 反馈<->回复 对话列表
     *
     * @param string $problem_no
     * @param string $staff_id
     * @param bool $isSelf
     *
     * @return array $data
     */
    public function getProblemOrderDetail(string $problem_no, string $staff_id, $isSelf = true)
    {
        if (empty($problem_no) || empty($staff_id)) {
            return [];
        }

        //查看支援信息
        //如果是支援账号，则找到其 主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($staff_id);
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            $staff_id = $supportStaffInfo['staff_info_id'];
        }

        // 获取工单基本信息
        $base_info = $this->getProblemOrderBaseInfo($problem_no);
        if ($base_info['staff_id'] != $staff_id) {
            return [];
        }

        // 获取问题工单的分类
        $category_info = $this->getCategoryInfo($base_info['problem_category_v2']);
        $base_info['category_name'] = $category_info['category_name'] ?? '';

        // 获取工单反馈<->回复对话列表
        // 反馈列表 + 回复列表 -> create_time 正序
        $staff_msg_sql = "
                -- 获取问题工单员工反馈列表
                SELECT
                    id AS staff_msg_id, content AS text_content, img_url AS image_item, create_time
                FROM 
                    mail_to_ceo
                WHERE 
                    problem_no = :problem_no        
                ";
        $staff_msg_param = [
            'problem_no' => $problem_no
        ];

        $staff_feedback_list = $this->getDI()->get('db_rby')->query($staff_msg_sql, $staff_msg_param)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $sys_msg_sql = "
                -- 获取问题工单管理员回复列表
                SELECT
                    id AS sys_msg_id, content AS text_content, img_url AS image_item, file_url, create_time
                FROM 
                    mail_reply_from_ceo
                WHERE 
                    problem_no = :problem_no        
                ";
        $staff_msg_param = [
            'problem_no' => $problem_no
        ];

        $sys_reply_list = $this->getDI()->get('db_rby')->query($sys_msg_sql, $staff_msg_param)->fetchAll(\Phalcon\Db::FETCH_ASSOC);



        $sys_notice_sql = "
                -- 获取问题工单系统通知列表
                SELECT
                    id AS auto_sys_msg_id, content as text_content ,  create_time , status
                FROM 
                    ceo_mail_sys_notice
                WHERE 
                    problem_no = :problem_no        
                ";
        $staff_notice_param = [
            'problem_no' => $problem_no
        ];

        $sys_notice_list = $this->getDI()->get('db_rby')->query($sys_notice_sql, $staff_notice_param)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        // 合并用户 与 系统两侧消息, 并时间正序
        $msg_list = array_merge($staff_feedback_list, $sys_reply_list,$sys_notice_list);
        $create_times = array_column($msg_list, 'create_time');
        array_multisort($create_times, SORT_ASC, $msg_list);
        $t  = $this->getTranslation();
        if (!empty($msg_list)) {
            foreach ($msg_list as $key => $value) {
                if (isset($value['staff_msg_id'])) {
                    $value['avatar_url'] = $base_info['avatar_url'];
                    $value['msg_from'] = 'staff';
                }elseif (isset($value['auto_sys_msg_id'])) {
                    $value['avatar_url'] = '';
                    $value['text_content'] = $t->_($value['text_content']);
                    $value['msg_from'] = 'auto_sys';
                    $value['category_name'] = $base_info['category_name'];
                } else {
                    $value['avatar_url'] = '';
                    $value['msg_from'] = 'sys';
                }

                // 图片
                if (!empty($value['image_item'])) {
                    $value['image_item'] = explode(',', $value['image_item']);
                } else {
                    $value['image_item'] = [];
                }
                //文件
                $value['file_url'] = !empty($value['file_url']) ? $value['file_url'] : '';

                $msg_list[$key] = $value;
            }
        }

        // 更新问题工单状态: 若有回复，则更为已读
        if ($base_info['problem_status'] == 1 && !$base_info['is_read_sys_reply'] && $isSelf) {
            $update_problem_order_status_sql = "
                -- 更新问题工单阅读状态为已读
                UPDATE 
                    ceo_mail_staff_problem_order
                SET
                    is_read_sys_reply = 1
                WHERE
                    id = {$base_info['id']}
                ";
            $this->getDI()->get('db')->execute($update_problem_order_status_sql);

            $update_feedback_status_sql = "
                -- 更新反馈信息的阅读状态为已读
                UPDATE 
                    mail_to_ceo
                SET
                    is_read = 1
                WHERE
                    problem_no = '{$problem_no}'
                ";
            $this->getDI()->get('db')->execute($update_feedback_status_sql);
        }

        if ($base_info['is_evaluate'] || $base_info['problem_status'] == 3) {
            $base_info['problem_status'] = 2;
        }

        $problem_order_info = [
            'problem_no' => $base_info['problem_no'],
            'category_name' => $base_info['category_name'],
            'problem_status' => $base_info['problem_status'],
            'is_evaluate' => $base_info['is_evaluate'],
            'staff_score' => $base_info['staff_score'],
            'staff_evaluate' => $base_info['staff_evaluate'] ? explode(',', $base_info['staff_evaluate']) : [],
            'staff_evaluate_remark' => $base_info['staff_evaluate_remark'],
            'evaluate_time' => $base_info['evaluate_time'],
        ];

        return [
            'problem_order_info' => $problem_order_info,
            'common_words' => $this->getEvaluateCommonWords(),
            'problem_order_item' => $msg_list,
        ];
    }

    /**
     * 获取问题工单基本信息
     *
     * @param string $problem_no
     *
     * @return array $data
     */
    public function getProblemOrderBaseInfo(string $problem_no)
    {
        if (empty($problem_no)) {
            return [];
        }

        $sql = "
            -- 获取问题工单基本信息
            SELECT 
                *
            FROM 
                ceo_mail_staff_problem_order
            WHERE 
                problem_no = :problem_no         
        ";
        $query_param = [
            'problem_no' => $problem_no
        ];

        return $this->getDI()->get('db_rby')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 继续反馈
     *
     * @param array $param
     *
     * @return bool $result
     */
    public function continueFeedback(array $param)
    {
        try {
            $problem_no = $param['problem_no'];
            $problem_desc = $param['problem_desc'] ?? '';
            $problem_image = $param['problem_image'] ? implode(',', $param['problem_image']) : '';
            $staff_info = $param['staff_info'];

            //查看支援信息
            //如果是支援账号，则找到其 主账号
            $supportStaffInfo = (new AttendanceRepository($this->lang,
                $this->timezone))->getSupportInfoBySubStaff($staff_info['id']);
            if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
                $staff_info['id'] = $supportStaffInfo['staff_info_id'];
            }

            // [1] 获取问题工单信息
            $base_info = $this->getProblemOrderBaseInfo($problem_no);
            if ($base_info['staff_id'] != $staff_info['id']) {
                return false;
            }

            // [1.1] 问题工单已完成 或 超时，不可继续反馈
            if (in_array($base_info['problem_status'], [2, 3])) {
                return false;
            }

            // [2] 获取问题工单系统侧的最后一条回复信息
            $sys_last_reply_info = $this->getSysLastReply($problem_no);

            $from_ceo_reply_id = 0;
            $reply_interval_time = 0;
            if (!empty($sys_last_reply_info)) {
                $from_ceo_reply_id = $sys_last_reply_info['id'];
                $reply_interval_time = strtotime($this->th_datetime) - strtotime($sys_last_reply_info['create_time']);
                $reply_interval_time = $reply_interval_time > 0 ? $reply_interval_time : 0;
            }

            // backyard数据处理
            $backyard_db = $this->getDI()->get('db');
            $backyard_db->begin();

            // [3] 添加员工反馈记录
            $feedback_sql = "
                    -- 员工继续反馈
                    INSERT INTO 
                        mail_to_ceo (staff_id, staff_name, mobile, type_v2, content, img_url, problem_no, from_ceo_reply_id, reply_interval_time, create_time)
                    VALUES
                        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);    
                    ";
            $feedback_param = [
                $staff_info['id'],
                $base_info['staff_name'],
                $base_info['staff_mobile'],
                $base_info['problem_category_v2'],
                $problem_desc,
                $problem_image,
                $problem_no,
                $from_ceo_reply_id,
                $reply_interval_time,
                $this->th_datetime
            ];
            $feedback_result = $backyard_db->execute($feedback_sql, $feedback_param);
            $last_insert_id = $backyard_db->lastInsertId();

            // 更新问题工单状态为待回复
            $update_problem_order_sql = "
                        -- 更新问题工单状态为 待回复
                        UPDATE 
                            ceo_mail_staff_problem_order
                        SET 
                            problem_status = ?, latest_feedback_id = ?, latest_feedback_time = ?
                        WHERE
                            id = ?        
                        ";
            $update_problem_order_param = [
                0,
                $last_insert_id,
                $this->th_datetime,
                $base_info['id']
            ];

            //更新 系统通知为未处理
            $update_sys_notice_sql = "
                        -- 更新系统通知状态
                        UPDATE 
                            ceo_mail_sys_notice
                        SET 
                            status = ?
                        WHERE
                            problem_no = ? order by id desc limit 1       
                        ";
            $update_sys_notice_param = [
                2,
                $problem_no
            ];
            $backyard_db->execute($update_sys_notice_sql, $update_sys_notice_param);

            $problem_order_status_result = $backyard_db->execute($update_problem_order_sql, $update_problem_order_param);
            if ($feedback_result && $problem_order_status_result) {
                $backyard_db->commit();

                return true;
            } else {
                $backyard_db->rollback();

                return false;
            }

        } catch (Exception $e) {
            $backyard_db->rollback();

            $this->wLog('CEO MAIL: continueFeedback - ', $e->getMessage(), 'ceo_mail_server', 'error');
        }
    }

    /**
     * 问题工单评价
     * @param array $param
     * $return bool $result
     */
    public function problemOrderEvaluate(array $param)
    {
        try {
            $staff_id = $param['staff_id'];
            $problem_no = $param['problem_no'];
            $staff_score = $param['staff_score'];
            $staff_evaluate = $param['staff_evaluate'] ? implode(',', $param['staff_evaluate']) : '';
            $staff_evaluate_remark = $param['staff_evaluate_remark'];

            //查看支援信息
            //如果是支援账号，则找到其 主账号
            $supportStaffInfo = (new AttendanceRepository($this->lang,
                $this->timezone))->getSupportInfoBySubStaff($staff_id);
            if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
                $staff_id = $supportStaffInfo['staff_info_id'];
            }

            // [1] 获取问题工单信息
            $base_info = $this->getProblemOrderBaseInfo($problem_no);
            if ($base_info['staff_id'] != $staff_id) {
                return false;
            }

            // [1.1] 问题工单已评价, 不可重复评价
            if ($base_info['is_evaluate']) {
                throw  new ValidationException($this->getTranslation()->_('ceo_mail_008'));
            }


            $update_sql = "
                    -- 更新工单评价
                    UPDATE 
                        ceo_mail_staff_problem_order
                    SET 
                        problem_status = ?, is_evaluate = ?, staff_score = ?, staff_evaluate = ?, staff_evaluate_remark = ?, evaluate_time = ?, feedback_waiting_interval_time = ?,update_time = update_time
                    WHERE 
                        id = ?
                   ";
            $update_param = [
                2,
                1,
                $staff_score,
                $staff_evaluate,
                $staff_evaluate_remark,
                $this->th_datetime,
                0,
                $base_info['id']
            ];
            if ($this->getDI()->get('db')->execute($update_sql, $update_param)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception $e) {
            throw $e;
        }

    }

    /**
     * 获取系统侧最后一条回复信息
     * @param string $problem_no
     * @return array $data
     */
    public function getSysLastReply(string $problem_no)
    {
        $sql = "
            -- 获取系统侧 某问题工单的最后一条回复
            SELECT 
                id, create_time
            FROM 
                mail_reply_from_ceo
            WHERE
                problem_no = :problem_no
            ORDER BY 
                create_time
            DESC
            LIMIT 1                
            ";
        $query_param = [
            'problem_no' => $problem_no
        ];

        return $this->getDI()->get('db_rby')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
    }


    /**
     * 获取语言简写
     */
    public function getLang()
    {
        $default_language = 'en';
        switch ($this->lang) {
            case 'th':
                $default_language = 'th';
                break;
            case 'zh':
            case 'zh-CN':
                $default_language = 'zh';
                break;
            case 'en':
                $default_language = 'en';
                break;
        }

        return $default_language;
    }

    /**
     * 获取问题反馈渠道：已回消息的未读数统计
     * @param array $param
     * @return array $data
     */
    public function getFeedbackUnreadCount(array $param)
    {
        $data = [
            'feedback_unread_count' => 0
        ];

        $staff_id = $param['staff_id'];

        //查看支援信息
        //如果是支援账号，则找到其 主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($staff_id);
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            $staff_id = $supportStaffInfo['staff_info_id'];
        }

        if (empty($staff_id)) {
            return $data;
        }

        //ceo 信箱: 问题反馈 已回复 未读
        $feedback_sql = "
            -- Ceo Mail: 系统已回复, 员工未读的数量
            SELECT 
                COUNT(mail.id) 
            FROM 
                ceo_mail_staff_problem_order AS mail 
            WHERE 
                mail.staff_id = {$staff_id} AND mail.problem_status = 1 AND mail.is_read_sys_reply = 0
        ";
        $data['feedback_unread_count'] = $this->getDI()->get('db_rby')->fetchColumn($feedback_sql);

        return $data;
    }

    /**
     * 获取员工基本信息
     * @param string $staff_id
     * @param string $fields
     * @return array $result
     */
    public function getStaffBaseInfo(string $staff_id, string $fields = '*')
    {
        if (empty($staff_id)) {
            return [];
        }

        $result =  HrStaffInfoServer::getUserInfoByStaffInfoId($staff_id, $fields);

        return !empty($result) ? $result->toArray() : [];
    }

    /**
     * 关闭工单
     * @param array $param
     * @throws Exception
     */
    public function  problemOrderClose(array $param)
    {
        // backyard数据处理
        $backyard_db = $this->getDI()->get('db');
        $backyard_db->begin();

        try {
            $problem_no = $param['problem_no'];
            $auto_sys_msg_id = $param['auto_sys_msg_id'];
            $state = $param['state'];
            $staff_info = $param['staff_info'];

            //查看支援信息
            //如果是支援账号，则找到其 主账号
            $supportStaffInfo = (new AttendanceRepository($this->lang,
                $this->timezone))->getSupportInfoBySubStaff($staff_info['id']);
            if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
                $staff_info['id'] = $supportStaffInfo['staff_info_id'];
            }

            // [1] 获取问题工单信息
            $base_info = $this->getProblemOrderBaseInfo($problem_no);
            if ($base_info['staff_id'] != $staff_info['id']) {
                return false;
            }


            // [1.1] 问题工单已完成 或 超时，不可继续反馈
            if (in_array($base_info['problem_status'], [2, 3])) {
                throw new ValidationException($this->getTranslation()->_('dont_click_repeatedly'));
            }


            // [2] 更改系统通知状态
            // 更新问题工单状态为待回复
            $update_sys_notice_sql = "
                        -- 更新系统通知状态
                        UPDATE 
                            ceo_mail_sys_notice
                        SET 
                            status = ?
                        WHERE
                            id = ?        
                        ";
            $update_sys_notice_param = [
                intval($state),
                $auto_sys_msg_id
            ];
            $problem_sys_notice_status_result = $backyard_db->execute($update_sys_notice_sql, $update_sys_notice_param);

            //同意关闭  工单改成 已完成  不同意 改单变成 待回复
            $updateData['problem_status'] = $state == CeoMailStaffProblemOrderModel::IS_SOLVE_YES ? CeoMailStaffProblemOrderModel::PROBLEM_STATUS_COMPLETE : CeoMailStaffProblemOrderModel::PROBLEM_STATUS_PENDING;

            // [2] 更新处理时长
            $all_interval_time = strtotime($this->th_datetime) - strtotime($base_info['create_time']);
            $all_interval_time = max($all_interval_time, 0);
            if ($updateData['problem_status'] == CeoMailStaffProblemOrderModel::PROBLEM_STATUS_COMPLETE) {
                $updateData['feedback_waiting_interval_time'] = 0;
                $updateData['all_interval_time'] = $all_interval_time;
            }

            $this->logger->write_log(['ceo_mail_staff_problem_order' => $updateData, 'id' => $base_info['id']], 'info');

            $problem_order_status_result = $backyard_db->updateAsDict(
                'ceo_mail_staff_problem_order',
                $updateData,
                'id = '.$base_info['id']
            );

            if ($problem_sys_notice_status_result && $problem_order_status_result) {
                $backyard_db->commit();
                return true;
            } else {
                $backyard_db->rollback();

                return false;
            }

        } catch (Exception $e) {
            $backyard_db->rollback();
            throw $e;
        }
    }

    /**
     * 消息详情查看工单
     * @param string $problem_no
     * @param string $staff_id
     * @return array
     */
    public function getProblemOrderDetailFromMessage(string $problem_no, string $staff_id): array
    {
        if (empty($problem_no) || empty($staff_id)) {
            return [];
        }

        //查看支援信息
        //如果是支援账号，则找到其 主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($staff_id);
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            $staff_id = $supportStaffInfo['staff_info_id'];
        }

        // 获取工单基本信息
        $base_info = $this->getProblemOrderBaseInfo($problem_no);
        if ($base_info['staff_id'] != $staff_id) {
            return [];
        }

        // 获取问题工单的分类
        $category_info = $this->getCategoryInfo($base_info['problem_category_v2']);
        $base_info['category_name'] = $category_info['category_name'] ?? '';
        $base_info['create_time'] = date('Y-m-d',strtotime($base_info['create_time']));
        return  $base_info;
    }


    /**
     * 获取 flash box 提交表单页地址。
     * @return mixed
     */
    public function getFlashBoxUrlIncrease()
    {
        $result['flash_box_url'] = '';

        $info = $this->getCategory(CeoMailEnums::ABOUT_SYSTEM_CATEGORY);
        if(empty($info)) {
            return $result;
        }
        $name = urlencode($info['category_name_th']);
        $result['flash_box_url'] = env('h5_endpoint') . CeoMailEnums::FLASH_BOX_SUBMIT_PAGE . '?source=fbi&is_read_commitment=0&problem_category=' . CeoMailEnums::ABOUT_SYSTEM_CATEGORY . '&seconProblemName=' . $name;
        return $result;
    }

    public function getCategory($problem_category)
    {
        if(empty($problem_category)) {
            return [];
        }
        $conditions = 'id = :problem_category: ';
        $bind = [
            'problem_category' => $problem_category,
        ];
        return CeoMailProblemCategoryRepository::getCategoryOne(['*'], $conditions, $bind);
    }

    /**
     * 获取投诉跟进列表
     * @param $params
     * @return mixed
     */
    public function followList($params)
    {
        $params['page_num']        = empty($params['page_num']) ? 1 : $params['page_num'];
        $params['page_size']       = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size']       = ($params['page_size'] > 100) ? 100 : $params['page_size'];
        $params['follow_staff_id'] = $params['staff_id'];

        $total = $this->getFollowQuery($params, true);
        $list  = $this->getFollowQuery($params);
        if ($list) {
            $list = $this->formatFollowList($list);
        }

        $data['total'] = !empty($total) ? intval($total['count']) : 0;
        $data['list']  = $list;

        return $data;
    }

    /**
     * 查询数据
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getFollowQuery($params, $isCount = false)
    {
        $columns = [
            'cmf.id',
            'cmf.staff_info_id',
            'cmf.node_department_id',
            'cmf.region_id',
            'cmf.piece_id',
            'cmf.store_id',
            'cmf.order_created_at',
            "DATE_FORMAT(CONVERT_TZ(cmf.created_at, '+00:00', '{$this->timezone}'), '%Y-%m-%d %H:%i:%s') as created_at",
            'cmf.status',
            'cmf.fix_result',
            'cmf.fix_time',
        ];
        if ($isCount) {
            $columns = 'count(1) as count';
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['cmf' => CeoMailFollowModel::class]);
        $builder = $this->getFollowBuilderWhere($builder, $params);
        if ($isCount) {
            return $builder->getQuery()->getSingleResult()->toArray();
        }

        $builder->limit($params['page_size'], $params['page_size'] * ($params['page_num'] - 1));


        $builder->orderBy('cmf.id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 查询条件
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getFollowBuilderWhere($builder, $params)
    {
        //网点
        if (!empty($params['status'])) {
            $builder->andWhere('cmf.status = :status:', ['status' => $params['status']]);
        }

        //网点
        if (!empty($params['follow_staff_id'])) {
            $builder->andWhere('cmf.follow_staff_id = :follow_staff_id:', ['follow_staff_id' => $params['follow_staff_id']]);
        }

        return $builder;
    }

    /**
     * 格式化数据
     * @param $data
     * @return array
     */
    public function formatFollowList($data)
    {
        $staffInfoIds = array_values(array_unique(array_column($data, 'staff_info_id')));
        $departmentIds = array_values(array_unique(array_column($data, 'node_department_id')));
        $storeIds      = array_values(array_filter(array_unique(array_column($data, 'store_id'))));
        $regionIds     = array_values(array_filter(array_unique(array_column($data, 'region_id'))));
        $pieceIds      = array_values(array_filter(array_unique(array_column($data, 'piece_id'))));

        $sysListRepository = new SysListRepository();

        $departmentInfo     = $sysListRepository->getDepartmentList(["ids" => $departmentIds]);

        $departmentInfoToId = !empty($departmentInfo) ? array_column($departmentInfo, 'name', 'id') : [];

        $storeInfo     = $sysListRepository->getStoreList(["ids" => $storeIds]);
        $storeInfoToId = !empty($storeInfo) ? array_column($storeInfo, 'name', 'id') : [];

        $this->logger->write_log(['$regionIds'=>$regionIds,'$pieceIds'=>$pieceIds],'info');

        $regionInfo = !empty($regionIds) ? SysManageRepository::getRegionList(['ids' => $regionIds]) : [];
        $pieceInfo  = !empty($pieceIds) ? SysManageRepository::getPieceList(['ids' => $pieceIds]) : [];

        $staffInfos = (new StaffRepository())->getStaffListByStaffIds($staffInfoIds, ['staff_info_id', 'name']);


        $list = [];
        $t    = $this->getTranslation();
        foreach ($data as $key => $oneData) {
            $name = isset($staffInfos[$oneData['staff_info_id']]) ? "(" . $staffInfos[$oneData['staff_info_id']]['name'] . ")" : '';

            $list[$key]['id']               = $oneData['id'];
            $list[$key]['staff_name']       = $oneData['staff_info_id'] . $name;
            $list[$key]['status_text']      = $t[CeoMailFollowModel::$follow_status[$oneData['status']]];
            $list[$key]['department_name']  = $departmentInfoToId[$oneData['node_department_id']] ?? '';
            $list[$key]['region_name']      = !empty($regionInfo[$oneData['region_id']]) ? $regionInfo[$oneData['region_id']]['name'] : '';
            $list[$key]['piece_name']       = !empty($pieceInfo[$oneData['piece_id']]) ? $pieceInfo[$oneData['piece_id']]['name'] : '';
            $list[$key]['store_name']       = $storeInfoToId[$oneData['store_id']] ?? '';
            $list[$key]['order_created_at'] = $oneData['order_created_at'] ?? '';
            $list[$key]['created_at']       = $oneData['created_at'] ?? '';
            $list[$key]['fix_time']         = $oneData['fix_time'] ?? '';
        }

        return $list;
    }

    /**
     * BY 审批页签 页面 是否展示 FLASH BOX 投诉跟进 入口 及红点数量
     * @param $staffId
     * @return mixed
     */
    public function getAuditNum($staffId)
    {
        $followParams['follow_staff_id'] = $staffId;
        $followParams['status'] = CeoMailFollowModel::STATUS_PENDING;
        $total = $this->getFollowQuery($followParams, true);
        $followData['num'] = !empty($total) ? intval($total['count']) : 0;

        $followInfo = CeoMailProblemCategoryRepository::getFollowOne(['follow_staff_id' => $staffId]);
        $followData['is_show'] = empty($followInfo) ? CeoMailEnums::DISPLAY_NO : CeoMailEnums::DISPLAY_YES;//0不展示，1展示

        return $followData;
    }

    /**
     * 获取投诉跟进详情
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function followDetail($params)
    {
        $followInfo = CeoMailProblemCategoryRepository::getFollowOne(['id' => $params['follow_id']]);
        if(empty($followInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        if($followInfo['follow_staff_id'] != $params['staff_id']) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        $staffInfo = (new StaffRepository())->getStaffInfoOne($followInfo['staff_info_id']);
        if(empty($staffInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));

        }


        $sysListRepository = new SysListRepository();

        $departmentInfo     = $sysListRepository->getDepartmentList(["ids" => [$followInfo['node_department_id']]]);
        $departmentInfoToId = !empty($departmentInfo) ? array_column($departmentInfo, 'name', 'id') : [];


        $storeInfo     = $sysListRepository->getStoreList(["ids" => [$followInfo['store_id']]]);
        $storeInfoToId = !empty($storeInfo) ? array_column($storeInfo, 'name', 'id') : [];

        $regionInfo = !empty($followInfo['region_id']) ? SysManageRepository::getRegionList(['ids' => [$followInfo['region_id']]]) : [];
        $pieceInfo  = !empty($followInfo['piece_id']) ? SysManageRepository::getPieceList(['ids' => [$followInfo['piece_id']]]) : [];

        $jobTitle = JobTitleRepository::getJobTitleInfo($followInfo['job_title']);
        $jobTitleName = !empty($jobTitle) ? $jobTitle->job_name : '';

        $state = $staffInfo['state'];
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_ON_JOB && $staffInfo['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE) {//待离职
            $state = HrStaffInfoModel::STATE_PENDING_RESIGNATION;
        }

        $result['id'] = $followInfo['id'];
        $result['staff_name'] = $staffInfo['staff_info_id'] . "(" . $staffInfo['name'] . ")";
        $result['department_name'] = $departmentInfoToId[$followInfo['node_department_id']] ?? '';
        $result['job_title_name'] = $jobTitleName;
        $result['region_name'] = !empty($regionInfo[$followInfo['region_id']]) ? $regionInfo[$followInfo['region_id']]['name'] : '';
        $result['piece_name'] = !empty($pieceInfo[$followInfo['piece_id']]) ? $pieceInfo[$followInfo['piece_id']]['name'] : '';
        $result['store_name'] = !empty($storeInfoToId[$followInfo['store_id']]) ? $storeInfoToId[$followInfo['store_id']] : '';
        $result['mobile'] = $followInfo['mobile'] ?? '';
        $result['state_text'] = $this->getTranslation()->_(HrStaffInfoModel::$hris_working_state[$state]);

        $result['hire_type_text'] = !empty($staffInfo['hire_type']) ? $this->getTranslation()->_('hire_type_' . $staffInfo['hire_type']) : '';
        $result['remark'] = $followInfo['remark'] ?? '';
        $result['fix_result'] = intval($followInfo['fix_result']);
        $result['fix_result_text'] = empty($followInfo['fix_result']) ? '' : $this->getTranslation()->_(CeoMailFollowModel::$follow_result[$followInfo['fix_result']]);
        $result['category_text'] = empty($followInfo['category']) ? '' : $this->getTranslation()->_('follow_category_'.$followInfo['category']);
        $result['category'] = intval($followInfo['category']);

        $result['follow_message'] = $followInfo['follow_message'];
        $result['image_url'] = !empty($followInfo['image_url']) ? json_decode($followInfo['image_url'], true) : [];

        return $result;
    }

    public function getFollowSysInfo()
    {
        $resultData = [];
        foreach (CeoMailFollowModel::$follow_result as $key => $value) {
            $oneData['value'] = $key;
            $oneData['label'] = $this->getTranslation()->_($value);
            $resultData[] = $oneData;
        }

        $resultCategory = [];
        $follow_category = range(1, 11);
        foreach ($follow_category as $value) {
            $oneData['value'] = $value;
            $oneData['label'] = $this->getTranslation()->_('follow_category_'.$value);
            $resultCategory[] = $oneData;
        }

        $result['fix_result'] = $resultData;
        $result['category'] = $resultCategory;
        return $result;
    }

    /**
     * 处理跟进
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function addFollow($params)
    {
        $followInfo = CeoMailProblemCategoryRepository::getFollowOne(['id' => $params['follow_id']]);

        if(empty($followInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        if($followInfo['follow_staff_id'] != $params['staff_id']) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        if($followInfo['status'] == CeoMailFollowModel::STATUS_PROCESSED) {
            throw new ValidationException($this->getTranslation()->_('follow_status_processed'));

        }

        if(!in_array($params['fix_result'], array_keys(CeoMailFollowModel::$follow_result))) {
            throw new ValidationException('fix_result:' . $this->getTranslation()->_('data_error'));

        }


        $data['status'] = CeoMailFollowModel::STATUS_PROCESSED;
        $data['fix_result'] = $params['fix_result'];
        $data['category'] = $params['category'];
        $data['follow_message'] = $params['follow_message'];
        $data['image_url'] = json_encode($params['image_url'], JSON_UNESCAPED_UNICODE);
        $data['fix_time'] = date('Y-m-d H:i:s');

        $this->getDI()->get('db')->updateAsDict(
            'ceo_mail_follow',
            $data,
            'id = '.$params['follow_id']
        );

        return true;
    }

    /**
     * 跟进投诉详情 进入
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getOrderDetailByFollow($params)
    {
        $followInfo = CeoMailProblemCategoryRepository::getFollowOne(['id' => $params['follow_id']]);

        if(empty($followInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        if($followInfo['follow_staff_id'] != $params['staff_id']) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        return $this->getProblemOrderDetail($followInfo['problem_no'], $followInfo['staff_info_id'], false);
    }



}
