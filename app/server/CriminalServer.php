<?php

namespace FlashExpress\bi\App\Server;

use Dotenv\Exception\ValidationException;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\StaffCriminalRecordModel;
use FlashExpress\bi\App\Models\backyard\StaffCriminalTypesModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use Phalcon\DiInterface;

class CriminalServer extends BaseServer
{

    // 犯罪记录状态
    const RECORD_STATUS_0 = 0;
    const RECORD_STATUS_1 = 1;
    const RECORD_STATUS_DESC = [
        self::RECORD_STATUS_0 => '未检查',
        self::RECORD_STATUS_1 => '已检查'
    ];


    // 是否有犯罪记录
    const IS_HAS_CRIMINAL_1 = 1;
    const IS_HAS_CRIMINAL_2 = 2;
    const IS_HAS_CRIMINAL_DESC = [
        self::IS_HAS_CRIMINAL_1 => '有',
        self::IS_HAS_CRIMINAL_2 => '无',
    ];

    // 信息审核状态
    const REVIEW_STATUS_0 = 0;
    const REVIEW_STATUS_1 = 1;
    const REVIEW_STATUS_2 = 2;
    const REVIEW_STATUS_3 = 3;
    const REVIEW_STATUS_DESC = [
        self::REVIEW_STATUS_0 => '待提交',
        self::REVIEW_STATUS_1 => '待审核',
        self::REVIEW_STATUS_2 => '已通过',
        self::REVIEW_STATUS_3 => '已驳回',
    ];


    // 犯罪记录状态
    const RECORD_CASE_STATUS_0 = 0;
    const RECORD_CASE_STATUS_1 = 1;
    const RECORD_CASE_STATUS_2 = 2;
    const RECORD_CASE_STATUS_DESC = [
        self::RECORD_CASE_STATUS_0 => '未结束',
        self::RECORD_CASE_STATUS_1 => '已结束',
        self::RECORD_CASE_STATUS_2 => '未结束',
    ];


    /**
     * 员工犯罪记录: 0 删除 1 有效
     */
    const STATUS_0 = 0;
    const STATUS_1 = 1;





    const MAX_EXCEL_ROWS = 1001;


    const OPERATOR_TYPE = 'criminal';


    // 犯罪记录类型
    const RECORD_TYPE_1 = 1;
    const RECORD_TYPE_2 = 2;
    const RECORD_TYPE_3 = 3;
    const RECORD_TYPE_4 = 4;
    const RECORD_TYPE_5 = 5;
    const RECORD_TYPE_6 = 6;
    const RECORD_TYPE_7 = 7;
    const RECORD_TYPE_8 = 8;    // 侵犯知识产权
    const RECORD_TYPE_9 = 9;    // 赌博罪
    const RECORD_TYPE_10 = 10;  //销售各类毒品

    const RECORD_TYPE_DESC = [
        self::RECORD_TYPE_1 => '有任何类型药物使用史（包括大麻和卡痛叶）',
        self::RECORD_TYPE_2 => '持有各类毒品',
        self::RECORD_TYPE_3 => '财产犯罪',
        self::RECORD_TYPE_4 => '侵犯生命和身体的犯罪',
        self::RECORD_TYPE_5 => '非法持有战争武器与枪支罪',
        self::RECORD_TYPE_6 => '轻罪',
        self::RECORD_TYPE_7 => '其他',
        self::RECORD_TYPE_8 => '侵犯知识产权',
        self::RECORD_TYPE_9 => '赌博罪',
        self::RECORD_TYPE_10 => '销售各类毒品',
    ];

    // 记录案例
    const RECORD_CASE_1 = 1;
    const RECORD_CASE_2 = 2;
    const RECORD_CASE_3 = 3;
    const RECORD_CASE_4 = 4;
    const RECORD_CASE_5 = 5;
    const RECORD_CASE_6 = 6;
    const RECORD_CASE_7 = 7;
    const RECORD_CASE_8 = 8;
    const RECORD_CASE_9 = 9;
    const RECORD_CASE_10 = 10;
    const RECORD_CASE_11 = 11;
    const RECORD_CASE_12 = 12;
    const RECORD_CASE_13 = 13;
    const RECORD_CASE_14 = 14;
    const RECORD_CASE_15 = 15;
    const RECORD_CASE_16 = 16;
    const RECORD_CASE_17 = 17;
    //const RECORD_CASE_18 = 18;
    const RECORD_CASE_19 = 19;
    //const RECORD_CASE_20 = 20;
    const RECORD_CASE_21 = 21;
    const RECORD_CASE_22 = 22;
    const RECORD_CASE_23 = 23;
    //const RECORD_CASE_24 = 24;
    const RECORD_CASE_25 = 25;
    const RECORD_CASE_26 = 26;
    const RECORD_CASE_27 = 27;
    const RECORD_CASE_28 = 28;
    const RECORD_CASE_29 = 29;
    const RECORD_CASE_30 = 30;
    const RECORD_CASE_31 = 31;
    const RECORD_CASE_32 = 32;
    const RECORD_CASE_33 = 33;
    const RECORD_CASE_34 = 34;

    const RECORD_CASE_35 = 35;  // 侵犯知识产权
    const RECORD_CASE_36 = 36;  // 敲诈勒索
    const RECORD_CASE_37 = 37;  // 诈骗
    const RECORD_CASE_38 = 38;  // 破坏财务
    const RECORD_CASE_39 = 39;  // 收取窃贼的东西


    const RECORD_CASE_40 = 40;  //合伙抢劫
    const RECORD_CASE_41 = 41;  // 暴力抢劫
    const RECORD_CASE_42 = 42;  // 恐吓
    const RECORD_CASE_43 = 43;  // 持有儿童色情制品
    const RECORD_CASE_44 = 44;  //猥亵行为
    const RECORD_CASE_45 = 45;  //因打架斗殴导致他人死亡
    const RECORD_CASE_46 = 46;  //无意中杀害他人罪
    const RECORD_CASE_47 = 47;  //持有/售卖武器
    const RECORD_CASE_48 = 48;  //战争武器
    const RECORD_CASE_49 = 49;  // 迷魂药
    const RECORD_CASE_50 = 50;  // 制卡痛叶
    const RECORD_CASE_51 = 51;  // 冰毒
    const RECORD_CASE_52 = 52;  // 大麻
    const RECORD_CASE_53 = 53;  // 海洛因
    const RECORD_CASE_54 = 54;  // 氯胺酮
    const RECORD_CASE_55 = 55;  // 甲基苯丙胺
    const RECORD_CASE_56 = 56;  // 甲基苯丙胺

    const RECORD_CASE_DESC = [
        self::RECORD_TYPE_1 => [
            self::RECORD_CASE_1 => '安非他命',
            self::RECORD_CASE_2 => '海洛因',
            self::RECORD_CASE_3 => '氯胺酮',
            self::RECORD_CASE_4 => '迷魂药',
            self::RECORD_CASE_5 => '冰毒',
            self::RECORD_CASE_6 => '大麻',
            self::RECORD_CASE_7 => '卡痛叶'
        ],
        self::RECORD_TYPE_2 => [
            self::RECORD_CASE_8 => '迷魂药',
            self::RECORD_CASE_9 => '制卡痛叶',
            self::RECORD_CASE_10 => '冰毒',
            self::RECORD_CASE_11 => '大麻',
            self::RECORD_CASE_12 => '海洛因',
            self::RECORD_CASE_13 => '氯胺酮',
            self::RECORD_CASE_56 => '甲基苯丙胺',
        ],
        self::RECORD_TYPE_3 => [
            self::RECORD_CASE_14 => '盗用资产',
            self::RECORD_CASE_15 => '偷窃',
            self::RECORD_CASE_16 => '抢劫',
            self::RECORD_CASE_36 => '敲诈勒索',
            self::RECORD_CASE_37 => '诈骗',
            self::RECORD_CASE_38 => '破坏财务',
            self::RECORD_CASE_39 => '收取窃贼的东西',
            self::RECORD_CASE_40 => '合伙抢劫',
            self::RECORD_CASE_41 => '暴力抢劫',
            self::RECORD_CASE_42 => '恐吓',
        ],
        self::RECORD_TYPE_4 => [
            self::RECORD_CASE_17 => '伤害儿童罪',
            //self::RECORD_CASE_18 => '故意伤害罪',
            self::RECORD_CASE_19 => '施暴导致他人自杀或自杀未遂',
            //self::RECORD_CASE_20 => '入侵罪',
            self::RECORD_CASE_21 => '强奸罪',
            self::RECORD_CASE_22 => '过失致死罪',
            self::RECORD_CASE_43 => '持有儿童色情制品',
            self::RECORD_CASE_44 => '猥亵行为',
            self::RECORD_CASE_45 => '因打架斗殴导致他人死亡',
            self::RECORD_CASE_46 => '无意中杀害他人罪',
        ],
        self::RECORD_TYPE_5 => [
            self::RECORD_CASE_23 => '携带武器',
            //self::RECORD_CASE_24 => '非法持有枪支弹药罪',
            self::RECORD_CASE_47 => '持有/售卖武器',
            self::RECORD_CASE_48 => '战争武器',
        ],
        self::RECORD_TYPE_6 => [
            self::RECORD_CASE_25 => '违反紧急状态罪',
            self::RECORD_CASE_26 => '军事法',
            self::RECORD_CASE_27 => '酒后驾驶罪',
            self::RECORD_CASE_28 => '混合罪',
            //self::RECORD_CASE_29 => '赌博罪',
            self::RECORD_CASE_30 => '盗版',
            self::RECORD_CASE_31 => '出售假冒商品罪',
            self::RECORD_CASE_32 => '持有淫秽物品罪',
            self::RECORD_CASE_33 => '收取利息超越法律罪',
            self::RECORD_CASE_34 => '开车过失罪',
        ],
        self::RECORD_TYPE_7 => [],

        self::RECORD_TYPE_8 => [
            self::RECORD_CASE_35 => '侵犯知识产权',
        ],
        self::RECORD_TYPE_9 => [
            self::RECORD_CASE_29 => '赌博罪',
        ],
        self::RECORD_TYPE_10 => [
            self::RECORD_CASE_49 => '迷魂药',
            self::RECORD_CASE_50 => '制卡痛叶',
            self::RECORD_CASE_51 => '冰毒',
            self::RECORD_CASE_52 => '大麻',
            self::RECORD_CASE_53 => '海洛因',
            self::RECORD_CASE_54 => '氯胺酮',
            self::RECORD_CASE_55 => '甲基苯丙胺',
        ]
    ];

    /**
     *
     * 签字状态
     */
    const SIGN_STATUS_1 = 1;
    const SIGN_STATUS_2 = 2;
    const SIGN_STATUS_DESC = [
        self::SIGN_STATUS_1 => '签字',
        self::SIGN_STATUS_2 => '拒绝签字',
    ];

    public function __construct($lang = 'zh-CN', DiInterface $di = null)
    {
        parent::__construct($lang, $di);
    }


    /**
     * @param $staffId
     */
    public function info($staffId)
    {
        $result = [];

        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staffId
            ]
        ]);
        if (!empty($staffInfo)) {
            $staffInfo = $staffInfo->toArray();
        } else {
            $staffInfo = [];
        }

        $result['userinfo'] = [
            'staff_info_id' => $staffId,
            'name' => $staffInfo['name'] ?? '',
            'identity' => $staffInfo['identity'] ?? '',
        ];

        $criminalRecord = StaffCriminalRecordModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staffId
            ]
        ]);
        if ($criminalRecord) {
            $result['criminal_record'] = [
                'review_status' => $criminalRecord->review_status,
                'review_status_text' => $this->getTranslation()->_('criminal_info_approval_status_' . $criminalRecord->review_status),
                'record_status' => $criminalRecord->record_status,
                'record_status_text' => $this->getTranslation()->_('criminal_check_' . $criminalRecord->record_status),
                'is_has_criminal' => $criminalRecord->is_has_criminal,
                'is_has_criminal_text' =>  $this->getTranslation()->_('criminal_has_criminal_' . $criminalRecord->is_has_criminal),
                'inspection_certificate' => $criminalRecord->inspection_certificate ? json_decode($criminalRecord->inspection_certificate, true) : [],
                'company_inspection_certificate' => $criminalRecord->company_inspection_certificate ? json_decode($criminalRecord->company_inspection_certificate, true) : [],
                'reject_reason' => $criminalRecord->review_status == StaffCriminalRecordModel::REVIEW_STATUS_PASS ? '' : $criminalRecord->reject_reason ?? ''
            ];
        } else {
            $result['criminal_record'] = [
                'review_status' => self::REVIEW_STATUS_0,
                'review_status_text' => $this->getTranslation()->_('criminal_info_approval_status_0'),
                'record_status' => self::RECORD_STATUS_0,
                'record_status_text' => $this->getTranslation()->_('criminal_check_' . self::RECORD_STATUS_0),
                'is_has_criminal' => 0,
                'is_has_criminal_text' =>  $this->getTranslation()->_('criminal_has_criminal_2'),
                'inspection_certificate' => [],
                'company_inspection_certificate' => [],
                'reject_reason' => ''
            ];
        }

        $criminalTypes = StaffCriminalTypesModel::find([
            'conditions' => ' staff_info_id = :staff_id: and status = 1 ',
            'bind' => [
                'staff_id' => $staffId
            ]
        ])->toArray();
        $result['criminal_types'] = [];
        foreach ($criminalTypes as $criminalType) {
            $result['criminal_types'][] = [
                'id' => $criminalType['id'],
                'record_type' => (int) $criminalType['record_type'],
                'record_type_text' => $this->getTranslation()->_('criminal_record_' . $criminalType['record_type']),
                'record_case' => (int) $criminalType['record_case'],
                'record_case_text' => $criminalType['record_type'] == self::RECORD_TYPE_7 ? $criminalType['record_text'] : $this->getTranslation()->_('criminal_case_' . $criminalType['record_case']),
                'record_text' => $criminalType['record_text'],
                'record_status' => (int) $criminalType['record_status'],
                'record_status_text' => $this->getTranslation()->_('record_status_' . $criminalType['record_status']),
                // 当为 毒品、拥有毒品/销售毒品、非法持有战争武器与枪支罪,案例状态为“已结束”时 时展示 结束日期字段
                'display_end_date' => ($criminalType['record_status'] == self::RECORD_CASE_STATUS_1 && in_array($criminalType['record_type'],
                        [self::RECORD_TYPE_1, self::RECORD_TYPE_2, self::RECORD_TYPE_5])),
                'end_date'         => is_null($criminalType['end_date']) ? "" : $criminalType['end_date']                                                                                                   // 结束日期
            ];
        }
        return $result;
    }


    public function CriminalStatus()
    {

        $criminals = [];
        foreach (self::RECORD_CASE_DESC as $recordType => $recordCases) {
            $tmp = ['key' => $recordType, 'value' => $this->getTranslation()->_('criminal_record_' . $recordType), 'cases' => []];
            foreach ($recordCases as $k => $recordCase) {
                $tmp['cases'][] = [
                    'key' => $k,
                    'value' => $this->getTranslation()->_('criminal_case_' . $k)
                ];
            }

            $criminals[] = $tmp;
        }


        return [
            'record_status' => [ // 检查犯罪记录状态
                [
                    'key' => self::RECORD_STATUS_0,
                    'value' => $this->getTranslation()->_('criminal_check_0')
                ], [
                    'key' => self::RECORD_STATUS_1,
                    'value' => $this->getTranslation()->_('criminal_check_1')
                ],
            ],
            'is_has_criminal' => [ // 是否有犯罪记录
                [
                    'key' => self::IS_HAS_CRIMINAL_1,
                    'value' => $this->getTranslation()->_('criminal_has_criminal_1')
                ], [
                    'key' => self::IS_HAS_CRIMINAL_2,
                    'value' => $this->getTranslation()->_('criminal_has_criminal_2')
                ]
            ],
            'criminals' => $criminals
        ];


    }


    public function edit($params)
    {
        if ($params['is_has_criminal'] == self::IS_HAS_CRIMINAL_1 && (empty($params['criminals']) || !is_array($params['criminals']))) {

            throw new ValidationException('criminals is required and must array type');
        }

        $before = $this->info($params['staff_id']);
        if ($before) {
            $before = array_merge($before['criminal_record'], ['criminal_types' => $before['criminal_types']]);
        }

        $cur_date = date('Y-m-d');
        foreach ($params['criminals'] as $k => $criminal) {
            if (
                empty($criminal['record_type'])
                ||
                !isset(self::RECORD_TYPE_DESC[$criminal['record_type']])
                ||
                (
                    $criminal['record_type'] != self::RECORD_TYPE_7
                    &&
                    !isset(self::RECORD_CASE_DESC[$criminal['record_type']][$criminal['record_case']])
                )
                ||
                (
                    $criminal['record_type'] == self::RECORD_TYPE_7
                    &&
                    empty($criminal['record_text'])
                )
            ) {

                unset($params['criminals'][$k]);
            }

            // 当 类型为“毒品、拥有毒品/销售毒品、非法持有战争武器与枪支罪”，案例状态为“已结束”时 验证结束日期
            if ($criminal['record_status'] == self::RECORD_CASE_STATUS_1 && in_array($criminal['record_type'],
                    [self::RECORD_TYPE_1, self::RECORD_TYPE_2, self::RECORD_TYPE_5, self::RECORD_TYPE_10])) {
                if (empty($criminal['end_date'])) {
                    // 结束日期不能为空
                    throw new ValidationException($this->getTranslation()->_('end_date_cannot_be_empty'));
                }

                if (!preg_match("/^([0-9]{4}-[0-9]{2}-[0-9]{2})$/", $criminal['end_date'])) {
                    // 结束日期格式不正确
                    throw new ValidationException($this->getTranslation()->_('end_date_format_error'));
                }

                if (strtotime($criminal['end_date']) > strtotime($cur_date)) {
                    // 只能选择今天及今天之前的日期
                    throw new ValidationException($this->getTranslation()->_('end_date_error'));
                }
            }
        }

        $staffCriminalRecordModel = StaffCriminalRecordModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $params['staff_id']
            ]
        ]);
        if (!$staffCriminalRecordModel) {
            $staffCriminalRecordModel = new StaffCriminalRecordModel();
            $staffCriminalRecordModel->staff_info_id = $params['staff_id'];
            $staffCriminalRecordModel->created_time = gmdate('Y-m-d H:i:s');
        } else {
            // 信息审核状态
            $reviewStatus = $staffCriminalRecordModel->review_status;
            if (
                $reviewStatus == self::REVIEW_STATUS_2
                &&
                $reviewStatus != $params['review_status']
            ) {
                // 如果是审核通过状态 且 更改了状态

                throw new ValidationException('审核通过状态是最终状态！');
            }
        }
        $criminalIds = array_values(array_filter(array_column($params['criminals'], 'id')));
        if ($criminalIds) {
            // 存在删除的
            $staffCriminalTypes = StaffCriminalTypesModel::find([
                'conditions' => ' id not in ({ids:array}) and staff_info_id = :staff_id:  ',
                'bind' => [
                    'ids' => $criminalIds,
                    'staff_id' => $params['staff_id']
                ]
            ]);
        } else {
            $staffCriminalTypes = StaffCriminalTypesModel::find([
                'conditions' => ' staff_info_id = :staff_id: ',
                'bind' => [
                    'staff_id' => $params['staff_id']
                ]
            ]);
        }
        if ($staffCriminalTypes && isset($reviewStatus) &&  $reviewStatus == self::REVIEW_STATUS_2) {
            // 如果存在删除的数据 并且 已经是已处理状态
            throw new ValidationException('审核通过状态不允许删除案例');
        }

        $staffCriminalRecordModel->review_status = self::REVIEW_STATUS_1;
        $staffCriminalRecordModel->record_status = $params['record_status'];
        $staffCriminalRecordModel->is_has_criminal = $params['is_has_criminal'];
        $staffCriminalRecordModel->inspection_certificate = $params['inspection_certificate'] ?? "";
        $staffCriminalRecordModel->operator_id = $params['userinfo_id'];
        $staffCriminalRecordModel->updated_time = gmdate('Y-m-d H:i:s');
        $staffCriminalRecordModel->save();

        if ($staffCriminalTypes) foreach ($staffCriminalTypes as $staffCriminalType) {
            $staffCriminalType->status = self::STATUS_0;
            $staffCriminalType->save();
        }

        foreach ($params['criminals'] as $criminal) {
            if (isset($criminal['id']) && $criminal['id']) {
                $staffCriminalTypesModel = StaffCriminalTypesModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => [
                        'id' => $criminal['id']
                    ]
                ]);
            } else {
                $staffCriminalTypesModel = new StaffCriminalTypesModel();
                $staffCriminalTypesModel->staff_info_id = $params['staff_id'];
                $staffCriminalTypesModel->created_time = gmdate('Y-m-d H:i:s');
            }
            $staffCriminalTypesModel->status = self::STATUS_1;
            $staffCriminalTypesModel->record_type = $criminal['record_type'];
            $staffCriminalTypesModel->record_case = $criminal['record_case'];
            $staffCriminalTypesModel->record_text = isset($criminal['record_text']) ? $criminal['record_text'] : '';
            $staffCriminalTypesModel->record_status = $criminal['record_status'];
            $staffCriminalTypesModel->operator_id = $params['userinfo_id'];
            $staffCriminalTypesModel->updated_time = gmdate('Y-m-d H:i:s');
            // 只有 毒品、拥有毒品/销售毒品、非法持有战争武器与枪支罪、销售各类毒品
            if ($criminal['record_status'] == self::RECORD_CASE_STATUS_1 && in_array($criminal['record_type'],
                    [self::RECORD_TYPE_1, self::RECORD_TYPE_2, self::RECORD_TYPE_5, self::RECORD_TYPE_10])) {
                $staffCriminalTypesModel->end_date = !empty($criminal['end_date']) ? $criminal['end_date'] : null;
            }
            $staffCriminalTypesModel->save();
        }

        $after = $this->info($params['staff_id']);
        $after = array_merge($after['criminal_record'], ['criminal_types' => $after['criminal_types']]);
        $this->addOperateLog($params['staff_id'], $before, $after);
        return true;
    }

    /**
     *
     * 添加操作记录
     *
     * @param $staffId
     * @param $before
     * @param $after
     * @return bool
     *
     */
    public function addOperateLog($staffId, $before, $after)
    {


        $data =[
            "operater" => $staffId,
            "staff_info_id" => $staffId,
            "type" =>  self::OPERATOR_TYPE,
            "before" => json_encode(['body' => $before], JSON_UNESCAPED_UNICODE),
            "after" => json_encode(['body' => $after], JSON_UNESCAPED_UNICODE),
        ];
        $client = new ApiClient('hr_rpc', '', 'add_operate_logs');
        $client->setParams($data);
        return  $client->execute();

        return true;
    }
    /**
     * 签字保存接口
     *
     * @param $param
     *
     */
    public function signCriminal($param)
    {
        $staffCriminalTypesModel = StaffCriminalTypesModel::findFirst([
            'conditions' => ' ( staff_info_id = :staff_id: and kit_id = :msg_id: ) or ( superior_id = :staff_id: and superior_kit_id = :msg_id: ) ',
            'bind' => [
                'staff_id' => $param['staff_id'],
                'msg_id' => $param['msg_id']
            ]
        ]);
        $staffCriminalTypes = $staffCriminalTypesModel->toArray();
        if ($staffCriminalTypes['superior_kit_id'] == $param['msg_id'] && $param['staff_id'] == $staffCriminalTypes['superior_id']) {

            // 上级签字
            $staffCriminalTypesModel->superior_img = $param['url'];
            // 发送个人行为警告书
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id: ',
                'bind' => [
                    'staff_id' => $staffCriminalTypes['staff_info_id']
                ]
            ])->toArray();
//            $title = $staffInfo['staff_info_id'] . ' ' . $staffInfo['name'] . ' 个人行为整改书';
            $title = str_replace(["#id#", "#name#"], [$staffInfo['staff_info_id'], $staffInfo['name']], $this->getTranslation()->_('criminal_rectificate_book'));

            $src = env("hcm_url") . "/Datatmp/rectification_book?id=" . $staffCriminalTypes['id'];
            $content = "<meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no\" /><iframe src='{$src}' width='100%' height='85%'></iframe>";

            // 添加消息
            $kit_param = [];
            $kit_param['staff_info_ids_str'] = $staffCriminalTypes['staff_info_id'];
            $kit_param['staff_users'] = [0 => ['id' => $staffCriminalTypes['staff_info_id']]];
            $kit_param['message_title'] = $title;
            $kit_param['message_content'] = $content;
            $kit_param['add_userid'] = $staffCriminalTypes['superior_id'];
            $kit_param['category'] = MessageEnums::MESSAGE_CATEGORY_CORRECTION_LETTER; // 个人行为整改书

            $bi_rpc = (new ApiClient('bi_rpc','','add_kit_message', $this->lang));
            $bi_rpc->setParams($kit_param);
            $res = $bi_rpc->execute();

            if ($res && $res['result']['code'] == 1) {
                $kitId = $res['result']['data'][0];

                $staffCriminalTypesModel->kit_id = $kitId;
                $staffCriminalTypesModel->superior_sign_status = self::SIGN_STATUS_1;

            }
        } else if ($staffCriminalTypes['kit_id'] == $param['msg_id'] && $param['staff_id'] == $staffCriminalTypes['staff_info_id']) {
            // 员工签字
            $staffCriminalTypesModel->img_url = $param['url'];
            $staffCriminalTypesModel->sign_status = self::SIGN_STATUS_1;
        }
        $staffCriminalTypesModel->save();

        $up_sql = "update message_courier set read_state = 1 where id = '{$param['msg_id']}'";
        $this->getDI()->get('db_coupon')->execute($up_sql);


        return true;
    }

    /**
     *
     * 拒绝签字
     *
     * @param $param
     *
     */
    public function refusalSign($param)
    {
        $staffCriminalTypesModel = StaffCriminalTypesModel::findFirst([
            'conditions' => ' ( staff_info_id = :staff_id: and kit_id = :msg_id: ) or ( superior_id = :staff_id: and superior_kit_id = :msg_id: ) ',
            'bind' => [
                'staff_id' => $param['staff_id'],
                'msg_id' => $param['msg_id']
            ]
        ]);
        $staffCriminalType = $staffCriminalTypesModel->toArray();

        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => ['staff_id' => $staffCriminalType['staff_info_id']]
        ])->toArray();

        $hrbpIds = (new WorkflowServer($this->lang, $this->timeZone))->findHRBP($staffInfo['node_department_id'], ['store_id' => $staffInfo['sys_store_id'] == -1 ? '' : $staffInfo['sys_store_id']]);
        if ($hrbpIds) {

            $positions = HrStaffInfoPositionModel::find([
                'conditions' => ' staff_info_id in ({staff_ids:array}) and position_category = :position: ',
                'bind' => [
                    'staff_ids' => explode(',', $hrbpIds),
                    'position' => 68
                ]
            ])->toArray();
            $hrbpIds = array_values(
                array_unique(array_column($positions, 'staff_info_id'))
            );
        }

        $staffServer = new StaffServer();
        $staffInfo = $staffServer->get_staff($staffCriminalType['staff_info_id']);
        $staffInfo = $staffInfo['data'];

        if ($staffCriminalType['superior_kit_id'] == $param['msg_id'] && $param['staff_id'] == $staffCriminalType['superior_id']) {
            $superiorInfo = $staffServer->get_staff($staffCriminalType['superior_id']);
            $superiorInfo = $superiorInfo['data'];
            // 上级拒绝签字
            $staffCriminalTypesModel->superior_sign_status = self::SIGN_STATUS_2;


            // 发送个人行为警告书
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id: ',
                'bind' => [
                    'staff_id' => $staffCriminalType['staff_info_id']
                ]
            ])->toArray();
//            $title = $staffInfo['staff_info_id'] . ' ' . $staffInfo['name'] . $this->getTranslation()->_('criminal_process_result_1');
            $title = str_replace(["#id#", "#name#"], [$staffInfo['staff_info_id'], $staffInfo['name']], $this->getTranslation()->_('criminal_rectificate_book'));

            $src = env("hcm_url") . "/Datatmp/rectification_book?id=" . $staffCriminalType['id'];
            $content = "<meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no\" /><iframe src='{$src}' width='100%' height='85%'></iframe>";

            // 添加消息
            $kit_param = [];
            $kit_param['staff_info_ids_str'] = $staffCriminalType['staff_info_id'];
            $kit_param['staff_users'] = [0 => ['id' => $staffCriminalType['staff_info_id']]];
            $kit_param['message_title'] = $title;
            $kit_param['message_content'] = $content;
            $kit_param['add_userid'] = $staffCriminalType['superior_id'];
            $kit_param['top'] = 1;
            $kit_param['category'] = MessageEnums::MESSAGE_CATEGORY_CORRECTION_LETTER; // 个人行为整改书

            $bi_rpc = (new ApiClient('bi_rpc','','add_kit_message', $this->lang));
            $bi_rpc->setParams($kit_param);
            $res = $bi_rpc->execute();

            if ($res && $res['result']['code'] == 1) {
                $kitId = $res['result']['data'][0];

                $staffCriminalTypesModel->kit_id = $kitId;

            }
           // 上级拒绝签字消息
//            $title = $staffCriminalType['staff_info_id'] . $this->getTranslation()->_('criminal_reject_title');
//            $content .= ' ' . $this->getTranslation()->_('superior') . $superiorInfo['staff_info_id'] . '-' . $superiorInfo['name'] . '-' . $superiorInfo['department_name'] . '-' . $superiorInfo['store_name'] . '拒绝签字，请知悉';

            $titleToHr['replace_area'] = ["#id#", "#name#"];
            $titleToHr['replace_data'] = [$staffCriminalType['staff_info_id'], $staffInfo['name']];
            $titleToHr['replace_key'] = 'criminal_rectificate_book_sign';

            $contentToHr['replace_area'] = ["#id#", "#name#", "#department#", "#store#", "#superior_id#", "#superior_name#", "#superior_department#", "superior_store"];
            $contentToHr['replace_data'] = [$staffCriminalType['staff_info_id'], $staffInfo['name'], $staffInfo['department_name'], $staffInfo['store_name'], $superiorInfo['staff_info_id'], $superiorInfo['name'], $superiorInfo['department_name'], $superiorInfo['store_name']];
            $contentToHr['replace_key'] = 'criminal_rectificate_book_superior_reject';

        } else {
            // 员工本人拒绝签字
            $staffCriminalTypesModel->sign_status = self::SIGN_STATUS_2;

//            $title = $staffCriminalType['staff_info_id'] . $this->getTranslation()->_('criminal_reject_title');

            $titleToHr['replace_area'] = ["#id#", "#name#"];
            $titleToHr['replace_data'] = [$staffCriminalType['staff_info_id'], $staffInfo['name']];
            $titleToHr['replace_key'] = 'criminal_rectificate_book_sign';

            $contentToHr['replace_area'] = ["#id#", "#name#", "#department#", "#store#"];
            $contentToHr['replace_data'] = [$staffCriminalType['staff_info_id'], $staffInfo['name'], $staffInfo['department_name'] , $staffInfo['store_name']];
            $contentToHr['replace_key'] = 'criminal_rectificate_book_reject';
        }

        $kitIds = $this->sendKitMessageToHrBp($hrbpIds, $param['staff_id'], $titleToHr, $contentToHr);

        $staffCriminalTypesModel->hrbp_kit_ids = implode(',', $kitIds);
        $staffCriminalTypesModel->save();


        $up_sql = "update message_courier set read_state = 1 where id = '{$param['msg_id']}'";
        $this->getDI()->get('db_coupon')->execute($up_sql);

        return true;
    }

    /**
     * 发送消息给hr
     * @param $hrbpIds
     * @param $staff_id
     * @param $title
     * @param $content
     * @return array
     */
    public function sendKitMessageToHrBp($hrbpIds, $staff_id, $title, $content)
    {
        $kitIds = [];
        foreach ($hrbpIds as $hrbpId) {
            // 添加消息
            $lang = (new StaffServer())->getLanguage($hrbpId);
            $kit_param = [];
            $kit_param['staff_info_ids_str'] = $hrbpId;
            $kit_param['staff_users'] = [0 => ['id' => $hrbpId]];
            $kit_param['message_title'] = str_replace($title['replace_area'], $title['replace_data'], $this->getTranslation($lang)->_($title['replace_key']));
            $kit_param['message_content'] = addslashes("<div style='font-size: 25px'>" . str_replace($content['replace_area'], $content['replace_data'], $this->getTranslation($lang)->_($content['replace_key'])) . "</div>");
            $kit_param['add_userid'] = $staff_id;
            $kit_param['top'] = 1;
            $kit_param['category'] = -1;

            $bi_rpc = (new ApiClient('bi_rpc','','add_kit_message', $this->lang));
            $bi_rpc->setParams($kit_param);
            $res = $bi_rpc->execute();

            if ($res && $res['result']['code'] == 1) {
                $kitId = $res['result']['data'][0];
                $kitIds[] = $kitId;
            }
        }
        return $kitIds;
    }


    /**
     * 是否需要签字
     *
     * @param $staffId
     * @param $msgId
     */
    public function isNeedSign($staffId, $msgId)
    {
        $staffCriminalTypesModel = StaffCriminalTypesModel::findFirst([
            'conditions' => ' ( staff_info_id = :staff_id: and kit_id = :msg_id: ) or ( superior_id = :staff_id: and superior_kit_id = :msg_id: ) ',
            'bind' => ['staff_id' => $staffId, 'msg_id' => $msgId]
        ]);
        if ($staffCriminalTypesModel) {
            $staffCriminalType = $staffCriminalTypesModel->toArray();
            if (
                // 已经签字
                $staffCriminalType['superior_id'] == $staffId && $staffCriminalType['superior_kit_id'] == $msgId && $staffCriminalType['superior_sign_status']
                ||
                $staffCriminalType['staff_info_id'] == $staffId && $staffCriminalType['kit_id'] == $msgId && $staffCriminalType['sign_status']
            ) {
                return false;
            }

            return true;
        }

        return false;
    }

    /**
     *
     * 转正评估是否弹出 弹出框
     *
     * @param $staffId
     */
    public function isPop($staffId)
    {


        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staffId
            ]
        ]);
        $isCanCheck = false;
        if (!empty($staffInfo)) {
            $staffInfo = $staffInfo->toArray();
            if ($staffInfo['hire_date'] >= '2021-08-01 00:00:00') {
                $isCanCheck = true;
            }
            if($staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID){
                $isCanCheck = false;
            }
        }


        if (strtolower(env('country_code', 'Th')) == 'th' && $isCanCheck) {
        	//1  弹框 0  不弹框
            // 只有泰国 必须员工犯罪记录检查数据 已通过  和 已检查 才返回 0  如果不存在记录 需要员工在个人信息里上传记录 并且审核通过
	        //https://l8bx01gcjr.feishu.cn/docs/doccnJNFX7Zgvpqza8BPVAAg02c
	        return StaffCriminalRecordModel::findFirst([
		                                                   'conditions' => ' staff_info_id = :staff_id: and review_status = :review_status:  and record_status = :record_status: ',
		                                                   'bind' => [
			                                                   'staff_id' => $staffId, //工号
			                                                   'review_status' => self::REVIEW_STATUS_2, //已通过
			                                                   'record_status'=> self::RECORD_STATUS_1, //已检查
		                                                   ]
	                                                   ]) ? 0 : 1;
        }
        

        return 0;
    }


}
