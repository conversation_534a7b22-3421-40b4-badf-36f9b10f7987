<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\backyard\HrStaffManagePieceModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageRegionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

class StaffManageServer extends BaseServer
{


    /**
     * 获取批量获取员工管辖的大区片区
     * @param $staff_ids
     * @param $type
     * @return array
     */
    public function getStaffManageRegionAndPiece($staff_ids, $type): array
    {
        $region = $this->batchStaffManageRegion($staff_ids, $type);
        $piece  = $this->batchStaffManagePiece($staff_ids, $type);
        $result = [];
        foreach ($staff_ids as $staff_id) {
            $result[] = [
                'staff_info_id' => intval($staff_id),
                'region_ids'     => $region[$staff_id] ?? [],
                'piece_ids'      => $piece[$staff_id] ?? [],
            ];
        }
        return $result;
    }




    /**
     * 批量获取管辖大区
     * @param $staff_ids
     * @param $type
     * @return mixed
     */
    public function batchStaffManageRegion($staff_ids, $type)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('region.staff_info_id,region.region_id');
        $builder->from(['staff' => HrStaffInfoModel::class]);
        $builder->leftjoin(HrStaffManageRegionModel::class, 'staff.staff_info_id = region.staff_info_id', 'region');
        //在职 在编的
        $builder->where(
            'staff.staff_info_id in ({staff_ids:array}) and staff.formal in (1,4) and staff.is_sub_staff = 0 and staff.state = 1  and region.deleted = 0 and region.type = :type: ',
            ['staff_ids' => $staff_ids, 'type' => $type]
        );
        $builder->groupBy('region.staff_info_id,region.region_id');
        $data =  $builder->getQuery()->execute()->toArray();
        $result  = [];
        foreach ($data as $item) {
            $result[$item['staff_info_id']][] = $item['region_id'];
        }
        return $result;
    }

    /**
     * 批量获取管辖片区
     * @param $staff_ids
     * @param $type
     * @return mixed
     */
    public function batchStaffManagePiece($staff_ids, $type)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('piece.staff_info_id,piece.piece_id');
        $builder->from(['staff' => HrStaffInfoModel::class]);
        $builder->leftjoin(HrStaffManagePieceModel::class, 'staff.staff_info_id = piece.staff_info_id', 'piece');
        //在职 在编的
        $builder->where(
            'staff.staff_info_id in ({staff_ids:array}) and staff.formal in (1,4) and staff.is_sub_staff = 0 and staff.state = 1  and piece.deleted = 0 and piece.type = :type: ',
            ['staff_ids' => $staff_ids, 'type' => $type]
        );
        $builder->groupBy('piece.staff_info_id,piece.piece_id');
        $data =  $builder->getQuery()->execute()->toArray();
        $result = [];
        foreach ($data as $item) {
            $result[$item['staff_info_id']][] = $item['piece_id'];
        }
        return $result;
    }


}
