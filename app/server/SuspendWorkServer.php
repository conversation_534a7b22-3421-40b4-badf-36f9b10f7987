<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\AuditDetailOperationsEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AdvanceFuelModel;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrOrganizationDepartmentStoreRelationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffPickupDeliveryDataModel;
use FlashExpress\bi\App\Models\backyard\SuspendWorkModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

class SuspendWorkServer extends AuditBaseServer
{
    const SERIAL_PRE = "ASW";

    /**
     * 添加暂停个人代理接单
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function addSuspendWork($param): array
    {
        $staff_info_id  = $param['staff_info_id'] ?? '';
        $reason_type    = $param['reason_type'] ?? '';
        $reason_remarks = $param['reason_remarks'] ?? '';
        $days           = $param['days'] ?? '';
        $submitter_id   = $param['submitter_id'] ?? '';
        if (empty($staff_info_id) || empty($submitter_id) || empty($days) || empty($reason_type) || empty($reason_remarks)) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $staff_info = (new StaffServer($this->lang, $this->timeZone))->get_staff($staff_info_id);
        if (empty($staff_info['data'])) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $staff_info            = $staff_info['data'];
        $checkSubmitLimitation = $this->checkSubmitLimitation($staff_info, $submitter_id);
        // 再次检测提交权限
        if ($checkSubmitLimitation['is_submit'] === false) {
            throw new ValidationException($checkSubmitLimitation['tip']);
        }
        $db = $this->getDI()->get("db");
        try {
            //获取业务数据
            $biz_data       = $this->getBizData($staff_info_id, $staff_info['sys_store_id']);
            $suspension_num = $this->getStoreSuspensionNum($staff_info['sys_store_id']);

            $db->begin();
            $model                  = new SuspendWorkModel();
            $model->serial_no       = static::SERIAL_PRE . $this->getRandomId();
            $model->staff_info_id   = $staff_info_id;
            $model->department_id   = $staff_info['node_department_id'] ?? 0;
            $model->job_title       = $staff_info['job_title'] ?? 0;
            $model->store_id        = $staff_info['sys_store_id'] ?? '';
            $model->store_category  = $staff_info['store_category'] ?? 0;
            $model->suspension_num  = $suspension_num;
            $model->reason_type     = $reason_type;
            $model->reason_remarks  = $reason_remarks;
            $model->days            = $days;
            $model->status          = SuspendWorkModel::STATUS_DEFAULT;
            $model->approval_status = enums::APPROVAL_STATUS_PENDING;
            $model->submitter_id    = $submitter_id;
            $model->biz_data        = json_encode($biz_data, JSON_UNESCAPED_UNICODE);
            $model->save();
            $lastInsertId = $model->id;
            if (!$lastInsertId) {
                throw new \Exception('apply failed');
            }
            $extend['from_submit'] = [
                'sys_store_id'  => $staff_info['sys_store_id'],  //被暂停人网点
                'department_id' => $staff_info['node_department_id'], //被暂停人部门
                'staff_info_id' => $staff_info['staff_info_id'], //被暂停人工号
                'audit_type'    => AuditListEnums::APPROVAL_TYPE_SUSPEND_WORK,
            ];
            $extend['time_out']    = date('Y-m-d 00:00:00', strtotime('+1 day'));
            //创建审批流
            $flag = (new ApprovalServer($this->lang, $this->timeZone))->create($lastInsertId,
                AuditListEnums::APPROVAL_TYPE_SUSPEND_WORK, $submitter_id, null, $extend);
            if (!$flag) {
                throw new \Exception('添加暂停个人代理接单创建审批流失败');
            }
            $db->commit();
            return $this->checkReturn(['data' => $lastInsertId]);
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDI()->get("logger")->write_log('AdvanceFuel signAdvanceFuel 添加暂停个人代理接单失败 ' . $e->getMessage());
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * 获取业务数据
     * @param $staff_info_id
     * @param $store_id
     * @return array
     * @throws BusinessException
     */
    public function getBizData($staff_info_id, $store_id): array
    {
        $ac = new ApiClient('ard_api', '', 'ProxyInvoice.getStaffWorkInfo', $this->lang);
        $ac->setParams(['staff_info_id' => $staff_info_id]);
        $ac_result = $ac->execute();
        if (empty($ac_result['result']['data'])) {
            throw new BusinessException($this->getTranslation()->_('server_error'));
        }
        $result                              = $ac_result['result']['data'];
        $data['2week_delivery_avg_duration'] = $result['duration'];      //派送时长
        $data['2week_handover_avg_amount']   = $result['handover_count'];//交接量
        $data['2week_delivery_avg_amount']   = $result['delivery_count'];//妥投量
        $data['customer_complaints_num']     = $result['complain_count'];//客户投诉数量
        $data['store_suspension_num']        = $this->getStoreSuspensionNum($store_id);
        return $data;
    }

    /**
     * 获取同网点编制的停职数
     * @param $store_id
     * @return mixed
     */
    public function getStoreSuspensionNum($store_id)
    {
        $staff_num = HrStaffInfoModel::count([
            'conditions' => "sys_store_id = :sys_store_id: and formal = :formal: and is_sub_staff = :is_sub_staff: and state = :state:",
            'bind'       => [
                'sys_store_id' => $store_id,
                'formal'       => HrStaffInfoModel::FORMAL_1,
                'is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0,
                'state'        => HrStaffInfoModel::STATE_3,
            ],
        ]);
        return $staff_num;
    }

    /**
     * 验证提交暂停个人代理接单限制
     * @param $staff_info
     * @param $submitter_id
     * @return array
     */
    public function checkSubmitLimitation($staff_info, $submitter_id): array
    {
        $return_data = ['is_submit' => false, 'tip' => ''];
        if (!isCountry('TH') || empty($staff_info) || empty($staff_info['staff_info_id']) || empty($staff_info['hire_type']) || !in_array($staff_info['hire_type'],
                HrStaffInfoModel::$agentTypeTogether) || $staff_info['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            // 请输入个人代理员工工号
            $return_data['tip'] = $this->getTranslation()->_('agent_suspend_work_error_1');
            return $return_data;
        }
        $departmentIds = (new SysDepartmentModel())->getSpecifiedDeptAndSubDept(enums::$department['Network Management']);
        $relation      = HrOrganizationDepartmentStoreRelationModel::findFirst([
            'conditions' => "department_id in ({department_ids:array}) and store_id = :store_id: and is_deleted = :deleted: and state = :state: and level_state = :level_state:",
            'bind'       => [
                'department_ids' => $departmentIds,
                'store_id'       => $staff_info['sys_store_id'],
                'deleted'        => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
                'state'          => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
                'level_state'    => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            ],
        ]);
        if (empty($relation)) {
            $return_data['tip'] = $this->getTranslation()->_('agent_suspend_work_error_2');
            return $return_data;
        }
        $checkStaffManageInfo = (new StaffServer())->checkStaffManageInfo($submitter_id, [$relation->region_id],
            [$relation->piece_id],
            [$relation->store_id]);
        if (!$checkStaffManageInfo) {
            $return_data['tip'] = $this->getTranslation()->_('agent_suspend_work_error_2');
            return $return_data;
        }

        if ($staff_info['state'] == HrStaffInfoModel::STATE_RESIGN) {
            // 个人代理已解约，不能申请
            $return_data['tip'] = $this->getTranslation()->_('agent_suspend_work_error_3');
            return $return_data;
        }

        if ($staff_info['state'] == HrStaffInfoModel::STATE_SUSPENSION) {
            // 个人代理已被暂停接单，不能申请
            $return_data['tip'] = $this->getTranslation()->_('agent_suspend_work_error_4');
            return $return_data;
        }

        // 有审批状态为待审批的或者状态为待停职的 不能申请
        $suspend_work = SuspendWorkModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and (approval_status = :approval_status: or status = :status:)",
            'bind'       => [
                'staff_info_id'   => $staff_info['staff_info_id'],
                'approval_status' => enums::APPROVAL_STATUS_PENDING,
                'status'          => SuspendWorkModel::STATUS_TO_BE_SUSPEND,
            ],
        ]);
        if (!empty($suspend_work)) {
            // 已为该员工申请过暂停接单，请勿重复申请
            $return_data['tip'] = $this->getTranslation()->_('agent_suspend_work_error_5');
            return $return_data;
        }
        $return_data['is_submit'] = true;
        $this->getDI()->get('logger')->write_log("AgentSuspendWork checkSubmitLimitation staff_info_id : " . $staff_info['staff_info_id'] . ",submitter_id : " . $submitter_id . " ,return_data : " . json_encode($return_data),
            'info');
        return $return_data;
    }

    /**
     * 暂停原因下拉
     * @return array|mixed
     */
    public function getSuspendWorkReasonType()
    {
        $suspend_work_reason_type = (new SettingEnvServer())->getSetVal('suspend_work_reason_type');
        $suspend_work_reason_type = $suspend_work_reason_type ? json_decode($suspend_work_reason_type, true) : [];
        foreach ($suspend_work_reason_type as $k => $v) {
            $suspend_work_reason_type[$k] = $this->getTranslation()->_($v);
        }
        return $suspend_work_reason_type;
    }

    /**
     * @param $params
     * @param $staffId
     * @return array
     * @throws \Exception
     */
    public function audit($params, $staffId): array
    {
        $auditType   = AuditListEnums::APPROVAL_TYPE_SUSPEND_WORK;
        $approval_id = $staffId;//操作人工号
        $audit_id    = $params['audit_id'];
        $status      = intval($params['status']);
        $server      = new ApprovalServer($this->lang, $this->timeZone);
        if ($status == enums::$audit_status['approved']) {//审核通过
            $res = $server->approval($audit_id, $auditType, $approval_id);
        } elseif ($status == enums::$audit_status['dismissed']) {//驳回
            $res = $server->reject($audit_id, $auditType, $params['reject_reason'], $approval_id);
        } elseif ($status == enums::$audit_status['revoked']) {//撤销
            $info = SuspendWorkModel::findFirst($audit_id);
            if (empty($info)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
            }
            $info = $info->toArray();
            if (!in_array($info['status'],
                [SuspendWorkModel::STATUS_DEFAULT, SuspendWorkModel::STATUS_TO_BE_SUSPEND])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('2206'));
            }
            if ($info['approval_status'] == enums::APPROVAL_STATUS_PENDING) {
                // 正常撤销
                $res = $server->cancel($audit_id, $auditType, $params['reason'], $approval_id);
            } elseif ($info['approval_status'] == enums::APPROVAL_STATUS_APPROVAL && $info['status'] == SuspendWorkModel::STATUS_TO_BE_SUSPEND) {
                // 终审同意后撤销
                $res = $server->cancel($audit_id, $auditType, $params['reason'], $approval_id);
                if (!empty($res)) {
                    $this->approvalAfterCancel($info);
                }
            }
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }
        if (empty($res)) {
            $this->getDI()->get('logger')->write_log('audit_update_status error ' . json_encode($params) . json_encode($res),
                'error');
            return $this->checkReturn(-3, 'data error');
        } else {
            return $this->checkReturn(1);
        }
    }

    /**
     * 终审同意后撤销
     * @param $info
     * @return mixed
     */
    public function approvalAfterCancel($info)
    {
        $staff_info = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => [
                'staff_id' => $info['staff_info_id'],
            ],
        ]);
        $staff_info = $staff_info ? $staff_info->toArray() : [];
        if ($staff_info['state'] != HrStaffInfoModel::STATE_1) {
            $this->getDI()->get('logger')->write_log("SuspendWorkServer approvalAfterCancel 状态不等于在职 无需发送push staff_info_id:" . $info['staff_info_id'],
                'info');
            return true;
        }
        
        //获取语言
        $lang = (new StaffServer($this->lang, $this->timeZone))->getLanguage($info['staff_info_id']);
        $t    = $this->getTranslation($lang);
        // 产品规定是当天申请当天全审批完成，隔天凌晨5点跑停职脚本 所以需要加1天
        $stop_duties_date     = date('Y-m-d', strtotime(date('Y-m-d') . ' +1 day'));
        $stop_duties_date_end = date('Y-m-d', strtotime(date('Y-m-d') . ' +' . $info['days'] . ' day'));

        // 暂停接单员工本人 发送BY系统消息
        $cancel_msg_title   = $t->_('suspend_work_notice_cancel_title');
        $cancel_msg_content = $t->_('suspend_work_notice_cancel_content', [
            'serial_no'            => $info['serial_no'] ?? '',
            'stop_duties_date'     => $stop_duties_date,
            'stop_duties_date_end' => $stop_duties_date_end,
        ]);
        $msg_id             = $this->sendNoticePush($info['staff_info_id'], $cancel_msg_title,
            $cancel_msg_content);
        if ($msg_id) {
            // 暂停接单员工本人 发送push
            $scheme = "flashbackyard://fe/page?path=message&messageid=" . $msg_id;
            PushServer::getInstance($this->lang, $this->timeZone)->sendPush($info['staff_info_id'],
                $cancel_msg_title,
                $cancel_msg_content, $scheme, 'backyard');
            PushServer::getInstance($this->lang, $this->timeZone)->sendPush($info['staff_info_id'],
                $cancel_msg_title,
                $cancel_msg_content, $scheme, 'kit');
            $this->getDI()->get('logger')->write_log("SuspendWorkServer sendPush 发送push成功 : msg_id:{$msg_id} ,staff_info_id:" . $info['staff_info_id'],
                'info');
        }
        return true;
    }


    /**
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     * @throws ValidationException
     */
    public function getDetail(int $auditId, $user, $comeFrom): array
    {
        $info = SuspendWorkModel::findFirst($auditId);
        if (empty($info)) {
            throw new ValidationException('data error');
        }
        $info = $info->toArray();
        //获取提交人用户信息
        $submitter_staff_info = (new StaffServer($this->lang, $this->timeZone))->get_staff($info['submitter_id']);
        $submitter_staff_info = !empty($submitter_staff_info['data']) ? $submitter_staff_info['data'] : [];
        // 获取被暂停人用户信息
        $staff_info      = (new StaffServer($this->lang, $this->timeZone))->get_staff($info['staff_info_id']);
        $staff_info      = !empty($staff_info['data']) ? $staff_info['data'] : [];
        $regionPieceInfo = [];
        if (!empty($staff_info['sys_store_id'])) {
            $regionPieceRe   = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
            $regionPieceInfo = $regionPieceRe->getOrganizationRegionPieceManagerId($staff_info['sys_store_id']);
        }
        $suspendWorkReasonType                    = $this->getSuspendWorkReasonType();
        $detailLists['submitter_staff_name']      = $submitter_staff_info['name'] . "({$submitter_staff_info['staff_info_id']})";
        $detailLists['submitter_department_name'] = ($submitter_staff_info['department_name'] ?? '') . '-' . ($submitter_staff_info['job_name'] ?? '');
        $detailLists['sw_staff_name']             = $staff_info['name'] . "({$staff_info['staff_info_id']})";
        $detailLists['staff_store']               = $staff_info['store_name'] ?? '';
        $detailLists['staff_region_piece_name']   = ($regionPieceInfo['region_name'] ?? '') . '-' . ($regionPieceInfo['piece_name'] ?? '');
        $detailLists['staff_job_title']           = !empty($staff_info['job_name']) ? $staff_info['job_name'] : '';
        $detailLists['staff_hire_type']           = !empty($staff_info['hire_type_text']) ? $staff_info['hire_type_text'] : '';
        $detailLists['sw_reason_type_text']       = !empty($suspendWorkReasonType[$info['reason_type']]) ? $suspendWorkReasonType[$info['reason_type']] : '';
        $detailLists['sw_reason_remarks']         = !empty($info['reason_remarks']) ? $info['reason_remarks'] : '';
        $detailLists['sw_days']                   = !empty($info['days']) ? $info['days'] : '';
        $addHour                                  = $this->config->application->add_hour;
        $audit_list_re                            = new AuditlistRepository($this->lang, $this->timeZone);
        $data                                     = [
            'title'       => $audit_list_re->getAudityType(AuditListEnums::APPROVAL_TYPE_SUSPEND_WORK),
            'id'          => $info['id'],
            'staff_id'    => $info['staff_info_id'],
            'type'        => AuditListEnums::APPROVAL_TYPE_SUSPEND_WORK,
            'created_at'  => date('Y-m-d H:i:s', strtotime($info['created_at']) + $addHour * 3600),
            'updated_at'  => date('Y-m-d H:i:s', strtotime($info['updated_at']) + $addHour * 3600),
            'status'      => $info['approval_status'],
            'status_text' => $audit_list_re->getAuditStatus('10' . $info['approval_status']),
            'serial_no'   => $info['serial_no'] ?? '',
        ];
        $biz_data                     = $info['biz_data'] ? json_decode($info['biz_data'], true) : [];
        $detailLists                  = array_merge($detailLists, $biz_data);
        $returnData['data']['detail'] = $this->format($detailLists);
        $returnData['data']['head']   = $data;
        return $returnData;
    }

    /**
     * @param $list
     * @return array
     */
    public function format($list): array
    {
        $return = [];
        foreach ($list as $key => $v) {
            $item             = [];
            $item['key']      = $this->getTranslation()->_($key) ?? '';
            $item['key_tips'] = is_array($v) && isset($v['key_tips']) ? $v['key_tips'] : null;
            $item['key_icon'] = is_array($v) && isset($v['key_icon']) ? $v['key_icon'] : null;
            $item['value']    = is_array($v) && isset($v['value']) ? $v['value'] : $v;
            $item['tips']     = is_array($v) && isset($v['tips']) ? $v['tips'] : null;
            $item['color']    = is_array($v) && isset($v['color']) ? $v['color'] : null;
            $return[]         = $item;
        }
        return $return;
    }

    /**
     * @param int $auditId
     * @param $user
     * @return array[]
     */
    public function genSummary(int $auditId, $user): array
    {
        $info = SuspendWorkModel::findFirst($auditId);
        if (empty($info)) {
            return '';
        }
        $info        = $info->toArray();
        $staff_info  = (new StaffServer($this->lang, $this->timeZone))->get_staff($info['staff_info_id']);
        $staff_info  = !empty($staff_info['data']) ? $staff_info['data'] : [];
        $storeInfo   = SysStoreModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $staff_info['sys_store_id']],
        ]);
        $return_data = [
            [
                'key'   => "sw_staff_name",
                'value' => $staff_info['name'] . "({$staff_info['staff_info_id']})",
            ],
            [
                'key'   => "staff_store",
                'value' => $storeInfo->name ?? '',
            ],
            [
                'key'   => "staff_job_title",
                'value' => empty($staff_info['job_name']) ? '' : $staff_info['job_name'],
            ],
        ];
        return $return_data;
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return true
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true): bool
    {
        if ($isFinal) {
            $model                  = SuspendWorkModel::findFirst($auditId);
            $model->approval_status = $state;

            //获取语言
            $lang = (new StaffServer($this->lang, $this->timeZone))->getLanguage($model->staff_info_id);
            $t    = $this->getTranslation($lang);

            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id:',
                'bind'       => [
                    'staff_id' => $model->staff_info_id,
                ],
            ]);
            $staff_info = $staff_info ? $staff_info->toArray() : [];
            // 产品规定是当天申请当天全审批完成，隔天凌晨5点跑停职脚本 所以需要加1天
            $stop_duties_date     = date('Y-m-d', strtotime(date('Y-m-d') . ' +1 day'));
            $stop_duties_date_end = date('Y-m-d', strtotime(date('Y-m-d') . ' +' . $model->days . ' day'));

            if ($state == enums::$audit_status['approved']) {
                // 审批通过
                $model->status                  = SuspendWorkModel::STATUS_TO_BE_SUSPEND;
                $add_hour                       = $this->getDI()['config']['application']['add_hour'];
                $model->approval_completed_date = gmdate("Y-m-d H:i:s", time() + $add_hour * 3600);
                $model->suspend_date            = $stop_duties_date;
                $model->restore_work_date       = date('Y-m-d',
                    strtotime(date('Y-m-d') . ' +' . ($model->days + 1) . ' day'));

                // 暂停接单员工本人 发送暂停接单通知消息 
                $suspendWorkReasonType = $this->getSuspendWorkReasonType();
                $reason                = !empty($suspendWorkReasonType[$model->reason_type]) ? $suspendWorkReasonType[$model->reason_type] : '';

                $msg_title   = $t->_('suspend_work_notice_push_title');
                $msg_content = $t->_('suspend_work_notice_push_content', [
                    'serial_no'            => $model->serial_no ?? '',
                    'reason'               => $reason,
                    'stop_duties_date'     => $stop_duties_date,
                    'stop_duties_date_end' => $stop_duties_date_end,
                ]);
                if ($staff_info['state'] == HrStaffInfoModel::STATE_1) {
                    // 暂停接单员工本人 发送暂停接单通知系统消息 
                    $msg_id = $this->sendNoticePush($model->staff_info_id, $msg_title, $msg_content);
                    if ($msg_id) {
                        // 暂停接单员工本人 发送push
                        $scheme = "flashbackyard://fe/page?path=message&messageid=" . $msg_id;
                        PushServer::getInstance($this->lang, $this->timeZone)->sendPush($model->staff_info_id,
                            $msg_title,
                            $msg_content, $scheme, 'backyard');
                        PushServer::getInstance($this->lang, $this->timeZone)->sendPush($model->staff_info_id,
                            $msg_title,
                            $msg_content, $scheme, 'kit');
                        $this->getDI()->get('logger')->write_log("SuspendWorkServer sendPush 发送push成功 : msg_id:{$msg_id} , staff_info_id :" . $model->staff_info_id,
                            'info');
                    }
                    // 暂停接单员工本人 发送短信
                    $sms_content = $this->getTranslation('th')->_('suspend_work_notice_sms_content', [
                        'reason'               => $reason,
                        'stop_duties_date'     => $stop_duties_date,
                        'stop_duties_date_end' => $stop_duties_date_end,
                    ]);
                    $this->sendSms($staff_info, $sms_content);
                }
                // 申请人 发送暂停接单审批通过系统消息 
                $applicant_msg_title   = $t->_('suspend_work_notice_approval_title');
                $applicant_msg_content = $t->_('suspend_work_notice_approval_content', [
                    'serial_no'            => $model->serial_no ?? '',
                    'staff_id'             => $staff_info['staff_info_id'] ?? '',
                    'staff_name'           => $staff_info['name'] ?? '',
                    'stop_duties_date'     => $stop_duties_date,
                    'stop_duties_date_end' => $stop_duties_date_end,
                ]);
                $this->sendNoticePush($model->submitter_id, $applicant_msg_title, $applicant_msg_content);
            } elseif ($state == enums::$audit_status['dismissed']) {
                // 已驳回
                $model->status        = SuspendWorkModel::STATUS_DEFAULT;
                $model->reject_reason = $extend['remark'] ?? '';
                // 申请人 发送驳回消息 
                $applicant_msg_title   = $t->_('suspend_work_notice_reject_title');
                $applicant_msg_content = $t->_('suspend_work_notice_reject_content', [
                    'serial_no'     => $model->serial_no ?? '',
                    'staff_id'      => $staff_info['staff_info_id'] ?? '',
                    'staff_name'    => $staff_info['name'] ?? '',
                    'reject_reason' => $model->reject_reason ?? '',
                ]);
                $this->sendNoticePush($model->submitter_id, $applicant_msg_title, $applicant_msg_content);
            } elseif ($state == enums::$audit_status['revoked']) {
                // 撤销
                $model->status        = SuspendWorkModel::STATUS_DEFAULT;
                $model->cancel_reason = $extend['remark'] ?? '';
            }
            if (!$model->save()) {
                $this->getDI()->get('logger')->write_log("SuspendWorkServer setProperty error auditId:" . $auditId . ',extend : ' . json_encode($extend) . ',state : ' . $state,
                    'error');
            }
        }
        return true;
    }


    /**
     * 发送短信
     * @param $data
     * @return bool
     */
    public function sendSms($staff_info, $content)
    {
        if (empty($staff_info) || empty($staff_info['mobile'])) {
            $this->getDI()->get('logger')->write_log("SuspendWorkServer sendSms 没有电话号 staff_info_id:{$staff_info['staff_info_id']} , content:{$content}",
                'info');
            return false;
        }
        $return = curlJsonRpc($this->config->api->api_send_sms,
            curlJsonRpcStr($staff_info['mobile'], $content, 'by_suspend_work'));
        if (isset($return['result'])) {
            $this->getDI()->get('logger')->write_log("SuspendWorkServer sendSms 短信成功 staff_info_id:{$staff_info['staff_info_id']} , content:{$content}",
                'info');
            return true;
        } else {
            $this->getDI()->get('logger')->write_log("SuspendWorkServer sendSms 短信失败 staff_info_id:{$staff_info['staff_info_id']} , content:{$content}",
                'info');
            return false;
        }
    }

    /**
     * 发送消息
     * @param $staff_info_id
     * @param $msg_title
     * @param $msg_content
     */
    public function sendNoticePush($staff_info_id, $msg_title, $msg_content)
    {
        $this->getDI()->get('logger')->write_log("SuspendWorkServer sendNoticePush 发送消息开始 staff_info_id :" . $staff_info_id . " msg_title:" . $msg_title,
            'info');
        $id       = time() . $staff_info_id . rand(1000000, 9999999);
        $msg_data = [
            "id"                 => $id,
            "staff_users"        => [$staff_info_id],
            "message_title"      => $msg_title,
            "message_content"    => addslashes("<div style='font-size: 30px'>" . $msg_content . "</div>"),
            "staff_info_ids_str" => $staff_info_id,
            "category"           => MessageEnums::MESSAGE_CATEGORY_SYS,
        ];
        $bi_rpc   = (new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($msg_data);
        $res = $bi_rpc->execute();
        if ($res && $res['result']['code'] == 1) {
            $msg_id = $res['result']['data'][0];
            $this->getDI()->get('logger')->write_log("SuspendWorkServer sendNoticePush 发送消息成功 : msg_id:{$msg_id},staff_info_id :" . $staff_info_id . " msg_data : " . json_encode($res),
                'info');
            return $msg_id;
        } else {
            $this->getDI()->get('logger')->write_log("SuspendWorkServer sendNoticePush 发送消息失败 : msg_data:" . json_encode($msg_data) . ',消息返回 : ' . json_encode($res) . 'staff_info_id:' . $staff_info_id,
                'error');
            return false;
        }
    }

    /**
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param $state
     * @return array
     */
    public function getWorkflowParams($auditId, $user, $state = null): array
    {
        $request = SuspendWorkModel::findFirst($auditId);
        return [
            'sw_department_id'      => $request['department_id'] ?? '',
            'sw_job_title'          => $request['job_title'] ?? '',
            'sw_sys_store_category' => $request['store_category'] ?? '',
            'sw_sys_store_id'       => $request['store_id'] ?? '',
            'sw_reason_type'        => $request['reason_type'] ?? '',
            'sw_suspension_num'     => $request['suspension_num'] ?? 0,
        ];
    }

    /**
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId): AuditOptionRule
    {
        return new AuditOptionRule(true, true, false, false, false, false);
    }


    /**
     * 申请人自定义按钮
     * @param $auditId
     * @return bool
     */
    public function applicantCustomiseOptions($auditId): bool
    {
        $info = SuspendWorkModel::findFirst($auditId);
        $info = !empty($info) ? $info->toArray() : [];
        if (!empty($info) && in_array($info['status'],[SuspendWorkModel::STATUS_TO_BE_SUSPEND,SuspendWorkModel::STATUS_DEFAULT])) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 处理审批超时
     * 每天0点运行
     * @return array
     */
    public function time_out_task(): array
    {
        $date    = DateHelper::localToUtc(date('Y-m-d 23:59:59', strtotime('-1 days')));
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['aa' => AuditApplyModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'aa.submitter_id = hr.staff_info_id', 'hr');
        $builder->leftJoin(SuspendWorkModel::class, 'aa.biz_value = sw.id', 'sw');
        $builder->where("aa.biz_type = :biz_type: and aa.state = :state: and aa.created_at <= :date:", [
            'biz_type' => enums::$audit_type['SUSPEND_WORK'],
            'state'    => enums::$audit_status['panding'],
            'date'     => $date,
        ]);
        $builder->columns("aa.biz_value,aa.biz_type,sw.serial_no,aa.state,aa.submitter_id,aa.created_at,hr.staff_info_id,hr.name");
        $pending_list = $builder->getQuery()->execute()->toArray();
        $approve        = new ApprovalServer($this->lang, $this->timeZone);
        $success_num    = 0;
        $fail_num       = 0;
        $fail_staff_ids = '';
        $count_num      = count($pending_list);
        foreach ($pending_list as $value) {
            $result = $approve->timeOut($value['biz_value'], enums::$audit_type['SUSPEND_WORK']);
            if (!$result) {
                //超时关闭失败
                $this->getDI()->get('logger')->write_log('SuspendWorkServer time_out_task 超时关闭失败 params:' . json_encode($value,
                        JSON_UNESCAPED_UNICODE) . ',result:' . json_encode($result, JSON_UNESCAPED_UNICODE), 'error');
                $fail_num++;
                $fail_staff_ids .= $value['submitter_id'] . ',';
            } else {
                //获取语言
                $lang = (new StaffServer($this->lang, $this->timeZone))->getLanguage($value['submitter_id']);
                $t    = $this->getTranslation($lang);
                // 申请人 发送BY系统消息
                $cancel_msg_title   = $t->_('suspend_work_notice_time_out_title');
                $cancel_msg_content = $t->_('suspend_work_notice_time_out_content', [
                    'serial_no'  => $value['serial_no'] ?? '',
                    'staff_id'   => $value['staff_info_id'] ?? '',
                    'staff_name' => $value['name'] ?? '',
                ]);
                $this->sendNoticePush($value['submitter_id'], $cancel_msg_title, $cancel_msg_content);
                $success_num++;
            }
        }
        return [
            'count_num'      => $count_num,
            'success_num'    => $success_num,
            'fail_num'       => $fail_num,
            'fail_staff_ids' => $fail_staff_ids,
        ];
    }

    /**
     * 暂停接单审批通过进行停职
     * @return array
     */
    public function staff_suspension_task(): array
    {
        $suspend_work   = SuspendWorkModel::find([
            'conditions' => "status = :status: and approval_status = :approval_status: and suspend_date = :suspend_date:",
            'bind'       => [
                'status'          => SuspendWorkModel::STATUS_TO_BE_SUSPEND,
                'suspend_date'    => date('Y-m-d'),
                'approval_status' => enums::APPROVAL_STATUS_APPROVAL,
            ],
        ])->toArray();
        $success_num    = 0;
        $fail_num       = 0;
        $fail_staff_ids = '';
        $count_num      = count($suspend_work);
        $logger         = $this->getDI()->get('logger');
        foreach ($suspend_work as $k => $v) {
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id:',
                'bind'       => [
                    'staff_id' => $v['staff_info_id'],
                ],
            ]);
            $staff_info = !empty($staff_info) ? $staff_info->toArray() : [];
            if (!empty($staff_info['state']) && $staff_info['state'] != HrStaffInfoModel::STATE_1) {
                $logger->write_log('staff_suspension_task 该工号状态已不是在职或待离职跳过 staff_info_id:' . $v['staff_info_id'] . 'staff_state:' . $staff_info['state'],
                    'info');
                $re = $this->getDI()->get('db')->updateAsDict('suspend_work',
                    [
                        'status'         => SuspendWorkModel::STATUS_ALREADY_SUSPEND,
                        'suspend_source' => SuspendWorkModel::SOURCE_DEFAULT,
                    ], 'id = ' . $v['id']);
                if (!$re) {
                    $logger->write_log('staff_suspension_task 修改暂停接单表信息失败 id:' . $v['id'], 'error');
                }
                $success_num++;
                continue;
            }
            $updateStateData = [
                "staff_info_id"    => $v['staff_info_id'],
                "type"             => HrStaffInfoModel::STATE_3,
                "day"              => date('Y-m-d'),
                'stop_duty_reason' => HrStaffInfoModel::STOP_DUTY_REASON_VIOLATION_CONTRACT,
                'src'              => 'backyard',
            ];
            $client          = new ApiClient('hr_rpc', '', 'sync_staff_state');
            $client->setParams([$updateStateData]);
            $result = $client->execute();
            if (isset($result['result']['code']) && $result['result']['code'] == 1 && isset($result['result']['data'][0]['msg']) && $result['result']['data'][0]['msg'] == 'ok') {
                $before_data_json = [
                    'wait_leave_state'    => $staff_info['wait_leave_state'],
                    'state'               => $staff_info['state'],
                    'leave_date'          => $staff_info['leave_date'],
                    'leave_reason'        => $staff_info['leave_reason'],
                    'leave_source'        => $staff_info['leave_source'],
                    'leave_type'          => $staff_info['leave_type'],
                    'leave_reason_remark' => $staff_info['leave_reason_remark'],
                    'leave_scenario'      => $staff_info['leave_scenario'],
                ];
                $re               = $this->getDI()->get('db')->updateAsDict(
                    'suspend_work',
                    [
                        'before_data_json' => json_encode($before_data_json, JSON_UNESCAPED_UNICODE),
                        'status'           => SuspendWorkModel::STATUS_ALREADY_SUSPEND,
                        'suspend_source'   => SuspendWorkModel::SOURCE_SUSPEND_WORK_TASK,
                        'stop_duties_date' => date('Y-m-d'),
                    ],
                    'id = ' . $v['id']
                );
                if (!$re) {
                    $logger->write_log('staff_suspension_task 修改暂停接单表信息失败 id:' . $v['id'], 'error');
                }
                $success_num++;
            } else {
                $logger->write_log('staff_suspension_task 暂停接单审批通过进行停职失败 params:' . json_encode($updateStateData,
                        JSON_UNESCAPED_UNICODE) . ',result:' . json_encode($result, JSON_UNESCAPED_UNICODE), 'error');
                $fail_num++;
                $fail_staff_ids .= $v['staff_info_id'] . ',';
            }
        }
        return [
            'count_num'      => $count_num,
            'success_num'    => $success_num,
            'fail_num'       => $fail_num,
            'fail_staff_ids' => $fail_staff_ids,
        ];
    }

    /**
     * 暂停接单停职后再根据暂停天数恢复在职或待离职脚本
     * @return array
     */
    public function staff_restore_work_task(): array
    {
        $suspend_work   = SuspendWorkModel::find([
            'conditions' => "status = :status: and approval_status = :approval_status: and restore_work_date = :restore_work_date: and suspend_source = :suspend_source:",
            'bind'       => [
                'restore_work_date' => date('Y-m-d'),
                'status'            => SuspendWorkModel::STATUS_ALREADY_SUSPEND,
                'suspend_source'    => SuspendWorkModel::SOURCE_SUSPEND_WORK_TASK,
                'approval_status'   => enums::APPROVAL_STATUS_APPROVAL,
            ],
        ])->toArray();
        $success_num    = 0;
        $fail_num       = 0;
        $fail_staff_ids = '';
        $count_num      = count($suspend_work);
        $logger         = $this->getDI()->get('logger');
        foreach ($suspend_work as $v) {
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id:',
                'bind'       => [
                    'staff_id' => $v['staff_info_id'],
                ],
            ]);
            $staff_info = !empty($staff_info) ? $staff_info->toArray() : [];
            if (!empty($staff_info['state']) && $staff_info['state'] != HrStaffInfoModel::STATE_3 || $staff_info['stop_duty_reason'] != HrStaffInfoModel::STOP_DUTY_REASON_VIOLATION_CONTRACT) {
                $logger->write_log('staff_restore_work_task 该工号状态已不是停职或停职来源不是违反合同的跳过 staff_info_id:' . $v['staff_info_id'] . 'staff_state:' . $staff_info['state'] . 'stop_duty_reason:' . $staff_info['stop_duty_reason'],
                    'info');
                $re = $this->getDI()->get('db')->updateAsDict('suspend_work',
                    ['status' => SuspendWorkModel::STATUS_NO_RESTORE_WORK], 'id = ' . $v['id']);
                if (!$re) {
                    $logger->write_log('staff_restore_work_task 修改暂停接单表信息失败 id:' . $v['id'], 'error');
                }
                $success_num++;
                continue;
            }
            $before_data_arr                  = !empty($v['before_data_json']) ? json_decode($v['before_data_json'],
                true) : [];
            $_is_success                      = false;
            $updateStateData                  = [];
            $updateStateData['staff_info_id'] = $v['staff_info_id'];
            if ($before_data_arr['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE) {
                if (date('Y-m-d', strtotime($before_data_arr['leave_date'])) > date('Y-m-d')) {
                    // 恢复成待离职 还原待离职数据
                    $updateStateData['wait_leave_date']     = $before_data_arr['leave_date'];
                    $updateStateData['leave_reason']        = $before_data_arr['leave_reason'];
                    $updateStateData['leave_source']        = $before_data_arr['leave_source'];
                    $updateStateData['leave_type']          = $before_data_arr['leave_type'];
                    $updateStateData['leave_scenario']      = $before_data_arr['leave_scenario']??0;
                    $updateStateData['leave_reason_remark'] = $before_data_arr['leave_reason_remark'] ?? '';
                    $updateStateData['from']                = 'suspend_work';
                    $client                                 = new ApiClient('hr_rpc', '', 'hr_staff_wait_leave');
                    $client->setParams($updateStateData);
                    $result = $client->execute();
                    if (!empty($result['result'])) {
                        $_is_success = true;
                    }
                    $logger->write_log('staff_restore_work_task 恢复成待离职 params:' . json_encode($updateStateData,
                            JSON_UNESCAPED_UNICODE) . ',result:' . json_encode($result, JSON_UNESCAPED_UNICODE),
                        'info');
                } else {
                    // 恢复在职
                    $updateStateData['type']             = HrStaffInfoModel::STATE_1;
                    $updateStateData['src']              = 'backyard';
                    $updateStateData['wait_leave_state'] = HrStaffInfoModel::WAITING_LEAVE_NO;
                    $client                              = new ApiClient('hr_rpc', '', 'sync_staff_state');
                    $client->setParams([$updateStateData]);
                    $result = $client->execute();
                    if (isset($result['result']['code']) && $result['result']['code'] == 1 && isset($result['result']['data'][0]['msg']) && $result['result']['data'][0]['msg'] == 'ok') {
                        $_is_success = true;
                    }
                    $logger->write_log('staff_restore_work_task 恢复成在职 params:' . json_encode($updateStateData,
                            JSON_UNESCAPED_UNICODE) . ',result:' . json_encode($result, JSON_UNESCAPED_UNICODE),
                        'info');
                }
            } else {
                // 恢复在职
                $updateStateData['type']             = HrStaffInfoModel::STATE_1;
                $updateStateData['src']              = 'backyard';
                $updateStateData['wait_leave_state'] = HrStaffInfoModel::WAITING_LEAVE_NO;
                $client                              = new ApiClient('hr_rpc', '', 'sync_staff_state');
                $client->setParams([$updateStateData]);
                $result = $client->execute();
                if (isset($result['result']['code']) && $result['result']['code'] == 1 && isset($result['result']['data'][0]['msg']) && $result['result']['data'][0]['msg'] == 'ok') {
                    $_is_success = true;
                }
                $logger->write_log('staff_restore_work_task 恢复成在职 params:' . json_encode($updateStateData,
                        JSON_UNESCAPED_UNICODE) . ',result:' . json_encode($result, JSON_UNESCAPED_UNICODE), 'info');
            }


            if ($_is_success) {
                $re = $this->getDI()->get('db')->updateAsDict(
                    'suspend_work',
                    [
                        'status' => SuspendWorkModel::STATUS_RESTORE_WORK,
                    ],
                    'id = ' . $v['id']
                );
                if (!$re) {
                    $logger->write_log('staff_suspension_task 暂停接单恢复在职失败1 id:' . $v['id'], 'error');
                }
                $success_num++;
            } else {
                $logger->write_log('staff_suspension_task 暂停接单恢复在职失败2 params:' . json_encode($updateStateData,
                        JSON_UNESCAPED_UNICODE) . ',result:' . json_encode($result, JSON_UNESCAPED_UNICODE), 'error');
                $fail_num++;
                $fail_staff_ids .= $v['staff_info_id'] . ',';
            }
        }
        return [
            'count_num'      => $count_num,
            'success_num'    => $success_num,
            'fail_num'       => $fail_num,
            'fail_staff_ids' => $fail_staff_ids,
        ];
    }

    /**
     * 暂停接单停职后再根据暂停天数恢复在职或待离职 后给被暂停接单员工发送短信
     * @return array
     */
    public function staff_restore_work_send_sms_task()
    {
        $suspend_work   = SuspendWorkModel::find([
            'conditions' => "restore_work_date = :restore_work_date: and status = :status: and approval_status = :approval_status: and suspend_source = :suspend_source:",
            'bind'       => [
                'restore_work_date' => date('Y-m-d', strtotime('+1 day')),
                'status'            => SuspendWorkModel::STATUS_ALREADY_SUSPEND,
                'suspend_source'    => SuspendWorkModel::SOURCE_SUSPEND_WORK_TASK,
                'approval_status'   => enums::APPROVAL_STATUS_APPROVAL,
            ],
        ])->toArray();
        $success_num    = 0;
        $fail_num       = 0;
        $fail_staff_ids = '';
        $count_num      = count($suspend_work);
        foreach ($suspend_work as $v) {
            // 暂停接单员工本人 发送短信
            $stop_duties_date     = date('Y-m-d', strtotime($v['suspend_date'] . ' +' . ($v['days'] - 1) . ' day'));
            $stop_duties_date_end = date('Y-m-d', strtotime($v['suspend_date'] . ' +' . $v['days'] . ' day'));
            $sms_content          = $this->getTranslation('th')->_('suspend_work_sms_cntent_task', [
                'stop_duties_date'     => $stop_duties_date,
                'stop_duties_date_end' => $stop_duties_date_end,
            ]);
            $staff_info           = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id:',
                'bind'       => [
                    'staff_id' => $v['staff_info_id'],
                ],
            ]);
            $staff_info           = $staff_info ? $staff_info->toArray() : [];
            if (!empty($staff_info['state']) && $staff_info['state'] != HrStaffInfoModel::STATE_3 || $staff_info['stop_duty_reason'] != HrStaffInfoModel::STOP_DUTY_REASON_VIOLATION_CONTRACT) {
                $this->getDI()->get('logger')->write_log('该员工已经恢复在职无需再发短信 staff_info_id:' . $staff_info['staff_info_id'],
                    'info');
                $re = true;
            } else {
                $re = $this->sendSms($staff_info, $sms_content);
            }
            if ($re) {
                $success_num++;
            } else {
                $fail_num++;
                $fail_staff_ids .= $v['staff_info_id'] . ',';
            }
        }
        return [
            'count_num'      => $count_num,
            'success_num'    => $success_num,
            'fail_num'       => $fail_num,
            'fail_staff_ids' => $fail_staff_ids,
        ];
    }
}