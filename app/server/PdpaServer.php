<?php


namespace FlashExpress\bi\App\Server;

use app\enums\LangEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\MobileHelper;
use FlashExpress\bi\App\Models\backyard\HrStaffPdPaLogModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use Ramsey\Uuid\Uuid;

class PdpaServer extends BaseServer
{
    private static $instance;

    public function __construct($lang = 'zh-CN', $timezone='+08:00')
    {
        parent::__construct($lang,$timezone);
    }

    /**
     * @return PdpaServer
     */
    public static function getInstance($lang = 'zh-CN', $timezone='+08:00')
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self($lang, $timezone);
        }
        return self::$instance;
    }

    public function staffCancelPDPA($param)
    {
        try {
            $staff_id = $param['staff_id'] ?? '';
            if (empty($staff_id)) throw new \Exception('staff_id不能为空');
            //获取取消员工信息
            $sql = " 
            --
            SELECT
                si.id as staff_id,
                si.name as staff_name,
                ss.manage_piece,
                ss.manage_region,
                sd.manager_id as manager_department_id,
                si.department_id,
                sd.ancestry_v3,
                sd.type
            FROM
                staff_info AS si
                LEFT JOIN sys_store AS ss ON si.organization_id = ss.id
                LEFT JOIN sys_department sd on si.department_id = sd.id
            WHERE
                si.id = %s and si.state = 1;
        ";
            $sql = sprintf($sql, $param['staff_id']);
            $staff_info = $this->getDI()->get('db_fle')->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC);
            if (empty($staff_info)) throw new \Exception('获取staff_id:' . $staff_id . '错误');
            //默认通知hrm助理
            $ids = explode(',', env('cancel_pdpa_notice_hrm', 10000));
            $hrbp_ids = [];

            $dep_sql = " 
                    --
                    SELECT
                    manager_id
                    FROM
                        sys_department 
                    WHERE
                        id = %s ;
                ";
            //HRM部门负责人：取OA组织架构HR Management部门的负责人
            $sql = sprintf($dep_sql, 47);
            $hrm_department = $this->getDI()->get('db_fle')->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC);
            if (!empty($hrm_department['manager_id'])) {
                $ids[] = $hrm_department['manager_id'];
            }
            //该名员工的一级部门负责人：该名员工的所属部门对应的一级部门负责人
            if (!empty($staff_info['ancestry_v3'])) {
                $depArr = explode('/', $staff_info['ancestry_v3']);

                //CEO - COO - BU - 一级部门负责人
                $depAncestry = $staff_info['type'] == 2 ? $depArr[3] : array_pop($depArr);
                if ($depAncestry) {
                    $sql = sprintf($dep_sql, $depAncestry);
                    $first_department = $this->getDI()->get('db_fle')->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC);
                    if (!empty($first_department['manager_id'])) {
                        $ids[] = $first_department['manager_id'];
                    }
                }
            }
            /**
             * 按员工所属部门管辖关系，和所属大区、片区或网点管辖关系的HRBP
             * 1. 网点-片区-大区：找所属片区的管辖HRBP
             * 2. 网点-大区：找所属大区的管辖HRBP
             * 3. 网点-部门：（如果 1，2 不存在则用3）
             * 按员工的所属部门找管辖HRBP
             */
            $position_category = 68; //角色 hrbp
            $WorkflowServer = (new WorkflowServer($this->lang, $this->timeZone));
            //片区
            if (!empty($staff_info['manage_piece'])) {
                //[2]获取片区对应的HRBP
                $hrbp = $WorkflowServer->findJurisdictionPiece($staff_info['manage_piece'],$position_category);
                if (!empty($hrbp)) {
                    $hrbp_ids = array_merge($hrbp_ids, $hrbp);
                }
            }
            //大区
            if (!empty($staff_info['manage_region'])) {
                //[3]获取大区对应的HRBP
                $hrbp = $WorkflowServer->findJurisdictionRegion($staff_info['manage_region'],$position_category);
                if (!empty($hrbp)) {
                    $hrbp_ids = array_merge($hrbp_ids, $hrbp);
                }
            }
            //如果不存在片区/大区的HRBP
            if (empty($hrbp_ids) && !empty($staff_info['department_id'])) {
                //这里是获取部门的 hrbp
                $hrbp = $WorkflowServer->findJurisdictionDepartment($staff_info['department_id'], $position_category);
                if (!empty($hrbp)) {
                    $ids = array_merge($ids, $hrbp);
                }
            }
            $ids = array_merge($ids, $hrbp_ids);
            $ids = array_unique($ids);
            //获取hrbp的语言环境
            $lang_list = [];
            foreach ($ids as $v) {
                $id_language = StaffAccountModel::findFirst([
                        'conditions' => 'staff_info_id = :id: and equipment_type in ({equipment_types:array})',
                        'bind' => [
                            'id' => $v, 'equipment_types' => [LangEnums::$equipment_type['kit'], LangEnums::$equipment_type['backyard']]
                        ],
                        'order' => 'updated_at desc',
                        'columns' => 'accept_language'
                    ]
                );
                $id_language = empty($id_language) ? [] : $id_language->toArray();
                $lang = $id_language['accept_language'] ?? 'en';
                $lang_list[$lang][] = $v;
            }
            //调用bi rpc 写入msg
            foreach ($lang_list as $k => $v) {
                [$t, $c] = $this->getMessageTitleContent($k);
                $c = sprintf($c, $staff_info['staff_name'], $staff_info['staff_id']);
                $c = "<div style='font-size: 40px'>" . $c . "</div>";
                $kit_param = [];
                $kit_param['staff_info_ids_str'] = implode(',', $v);
                $staff_users = [];
                foreach ($v as $vv) {
                    $staff_users[] = ['id' => $vv];
                }
                $kit_param['staff_users'] = $staff_users;
                $kit_param['message_title'] = $t;
                $kit_param['message_content'] = $c;
                $kit_param['add_userid'] = $staff_id;
                $kit_param['category'] = MessageEnums::MESSAGE_CATEGORY_CANCEL_PDPA;

                $bi_rpc = (new ApiClient('bi_rpc','','add_kit_message', $this->lang));
                $bi_rpc->setParams($kit_param);
                $res = $bi_rpc->execute();
                $this->getDI()->get('logger')->write_log('staffCancelPDPA-result:' . json_encode($res), 'info');
            }
            return true;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('staffCancelPDPA-error:' . $e->getMessage(), 'notice');
            return false;

        }
    }

    private function getMessageTitleContent($lang)
    {
        $lang = substr($lang, 0, 2);
        if ($lang == 'zh') {
            $lang = 'zh-CN';
        }
        if (!in_array($lang, ['en', "th", "zh-CN",], 1)) {
            $lang = 'en';
        }
        $content = [
            'zh-CN' => '您好，员工 姓名：{%s}  工号：{%u} ，撤销了隐私政策协议的同意按钮，现已无法登陆Backyard, 若要不影响工作正常登陆app请联系该名员工进行沟通重新勾选隐私政策协议后便可正常使用Backyard。',
            'en' => "Hello, employee's name: {%s} job number: {%u}, the consent button of the privacy policy agreement has been cancelled, and now he/she can't log in backyard. Please contact the employee to have communication for not effecting work, and check the privacy policy agreement again, then you can use backyard normally.",
            'th' => 'สวัสดี พนักงานชื่อ {%s} รหัสพนักงาน {%u} ได้ยกเลิกการยินยอมข้อตกลงเกี่ยวกับการคุ้มครองข้อมูลส่วนบุคคล ปัจจุบันไม่สามารถเข้าใช้งาน Backyard เพื่อไม่ให้เกิดผลกระทบต่อการทำงานของพนักงาน กรุณาติดต่อพนักงานเพื่อทำการชี้แจงและให้พนักงานยินยอมอีกครั้ง'
        ];
        $title = [
            'zh-CN' => '隐私政策协议撤销通知',
            'en' => 'Privacy policy agreement revocation notice',
            'th' => 'ประกาศเรื่องยกเลิกการยินยอมเกี่ยวกับการให้ข้อมูลส่วนบุคคลของพนักงาน'
        ];
        #$c = str_replace('XX', $count, $content[$lang]);
        return [$title[$lang], $content[$lang]];
    }

    /**
     * @param $staff_id //员工id
     * @param $lang //当前语言环境
     * @return string
     */
    public function getViewLang($staff_id, $lang)
    {
        $staffInfo = HrStaffItemsModel::findFirst([
            'conditions' => "staff_info_id = ?1 and item = 'NATIONALITY' ",
            'bind' => [
                1 => $staff_id
            ]
        ]);
        //中国
        if($staffInfo && $staffInfo->value == 2){
            $lang = 'zh';
        }else{
            $lang = substr($lang, 0, 2);
        }

        $lang = $lang == 'zh' ? 'zh' : 'th';
        return $this->checkReturn(['data'=>['lang'=>$lang]]);
    }

    /**
     * 用户修改是否同意pdpa
     * @param $staff_info_id
     * @param int $value
     * @param int $operationType 0同意，1不同意，2撤销同意
     * @return mixed
     */
    public function staffEditIsAgreePdpa($staff_info_id, $value = 1, $operationType = HrStaffPdPaLogModel::OPERATION_TYPE_0)
    {

        $db_migration = (new SettingEnvServer())->getSetVal('db_migration_20220515');
        if($db_migration){
            throw  new ValidationException($this->getTranslation()->_('system_upgraded'));
        }

        $compareResult = $this->pdPaCompareMobileVersion();
        $key = $compareResult ? 'IS_AGREE_PDPA_202204' : 'IS_AGREE_PDPA';

        $result = (new StaffRepository())->setStaffPdpaItems($staff_info_id, $key, $value);
        if($result && $key == 'IS_AGREE_PDPA_202204') {
            //添加日志
            $log = [
                'staff_info_id' => $staff_info_id,
                'mobile' => '',
                'client_type' => HrStaffPdPaLogModel::CLIENT_TYPE['BY'],
                'operation_type' => $operationType,//0同意，1不同意，2撤销同意
                'version' => HrStaffPdPaLogModel::VERSION['IS_AGREE_PDPA_202204'],//1:IS_AGREE_PDPA_202203; 2:IS_AGREE_PDPA_202204
            ];

            $this->getDI()->get("db")->insertAsDict("hr_staff_pdpa_log", $log);
        }
        return ;
    }

    /**
     * 获取用户是否同意 pdpa
     * @param $staff_id
     * @return string
     */
    public function getIsAgreePdPa($staff_id)
    {
        $compareResult = $this->pdPaCompareMobileVersion();
        $key = $compareResult ? 'IS_AGREE_PDPA_202204' : 'IS_AGREE_PDPA';

        return (new StaffRepository($this->lang))->getAvatar($staff_id, $key);
    }

    /**
     * 对比用户版本是否是新版本。
     * @return bool
     */
    public function pdPaCompareMobileVersion()
    {
        $equipment_info = [];
        $set_model = SettingEnvModel::findFirst("code = 'pdpa_equipment_info_config' ");
        if (!empty($set_model) && $set_model->set_val) {
            $equipment_info = json_decode($set_model->set_val, true);
        }
        if(!$equipment_info) {
            return false;
        }

        return MobileHelper::compareVersion($equipment_info);
    }

    /**
     * 获取PdPa个人隐私协议
     * @return mixed
     */
    public function getAgreementInfo()
    {
        $result['url'] = '';
        $set_model = SettingEnvModel::findFirst("code = 'pdpa_url' ");
        if (!empty($set_model) && $set_model->set_val) {
            $pdpaAgreementUrl = json_decode($set_model->set_val, true);
            $result['url'] = $pdpaAgreementUrl['version_202204'];
        }

        return $result;
    }
}
