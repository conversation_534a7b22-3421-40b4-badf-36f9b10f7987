<?php


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\SysStoreCateEnums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleRoleModel;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;

class HrStaffInfoServer extends BaseServer
{
    /**
     * 获取用户信息
     * @param $staffId
     * @param string $fieldsStr
     * @return mixed
     */
    public static function getUserInfoByStaffInfoId($staffId, string $fieldsStr = '*')
    {
        return HrStaffInfoModel::findFirst([
            "conditions" => "staff_info_id = :staff_info_id:",
            "bind"       => array('staff_info_id' => $staffId),
            "columns"    => $fieldsStr
        ]);
    }

    /**
     * 查询用户网点类别
     * @param array $userInfo
     * @return int|string
     */
    public static function getStaffStoreCateCode(array $userInfo)
    {
        if ($userInfo['sys_store_id'] == -1) {
            return SysStoreCateEnums::HO_CODE;
        }
        $SysStore =self::getStaffStoreBySysStoreId($userInfo['sys_store_id']);
        if(empty($SysStore)){
            return [
                'msg' => 'the staff store can not find',
            ];
        }
        $sysStoreCateMap = SysStoreCateEnums::getCodeTxtMap();
        foreach ($sysStoreCateMap as $storeCateCode => $storeCateIdArr) {
            if (in_array($SysStore->category, $storeCateIdArr)) {
                return $storeCateCode;
            }
        }
        return '';
    }

    /**
     * 根据网点ID获取网点信息
     * @param $sysStoreId
     * @param $fields
     * @return mixed
     */
    public static function getStaffStoreBySysStoreId($sysStoreId = '', $fields = '*')
    {
        return SysStoreModel::findFirst(
            [
                "conditions" => "id = :sys_store_id:",
                "bind"       => array('sys_store_id' => $sysStoreId),
                "columns"    => $fields
            ]
        );
    }

    /**
     * 辅导员搜索: 只搜索正式员工
     * ph 不包括待离职
     * @param array $param
     * @return array $result
     */
    public  function getStaffListBySearch($param): array
    {
        $sys_store_id = $param['sys_store_id'];
        $keyword      = $param['keyword'];
        $show_num     = $param['show_num'] ?? 20;
        $fields       = $param['fields'] ?? '*';

        if (empty($sys_store_id) || empty($keyword)) {
            return [];
        }

        $conditionsHireSql = ' AND hire_type !=' . HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT;
        $conditions        = 'sys_store_id = :sys_store_id: AND is_sub_staff = :is_sub_staff: AND state = :state: AND formal = :formal:';

        if (isCountry('MY')) {
            $conditions .= $conditionsHireSql;
        }

        $storeOnJobStaffNum = HrStaffInfoModel::count([
            "conditions" => $conditions,
            "bind"       =>  [
                'sys_store_id'  => $sys_store_id,
                'is_sub_staff'  => 0,
                'state'         => 1,
                'formal'        => 1,
            ],
        ]);

        $bind = [
            'sys_store_id'  => $sys_store_id,
            'is_sub_staff'  => 0,
            'state'         => 1,
            'formal'        => 1,
            'staff_info_id' => "%$keyword%",
            'name'          => "%$keyword%",
        ];

        $result = HrStaffInfoModel::find([
            "conditions" => $conditions . " AND (staff_info_id LIKE :staff_info_id: OR name LIKE :name:)",
            "bind"       => $bind,
            "columns"    => $fields,
            "order"      => "hire_date ASC",
            'limit'      => $show_num,
        ])->toArray();

        if (empty($result) && $storeOnJobStaffNum == 0) {
            $region_piece_manager          = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getOrganizationRegionPieceManagerId($sys_store_id);
            $this->getDI()->get('logger')->write_log(['getOrganizationRegionPieceManagerId'=>$region_piece_manager,'sys_store_id'=>$sys_store_id], 'info');
            $region_piece_manager_staff_id = [];
            if (!empty($region_piece_manager['region_manager_id'])) {
                $region_piece_manager_staff_id[] = $region_piece_manager['region_manager_id'];
            }
            if (!empty($region_piece_manager['piece_manager_id'])) {
                $region_piece_manager_staff_id[] = $region_piece_manager['piece_manager_id'];
            }

            if (!empty($region_piece_manager_staff_id)) {
                $conditions = 'staff_info_id in ({staff_info_id_array:array})  AND state = :state:  AND (staff_info_id LIKE :staff_info_id: OR name LIKE :name:)';

                if (isCountry('MY')) {
                    $conditions .= $conditionsHireSql;
                }

                $result = HrStaffInfoModel::find([
                    "conditions" => $conditions,
                    "bind"       => ['staff_info_id_array'    => $region_piece_manager_staff_id,
                                     'state'            => HrStaffInfoModel::STATE_ON_JOB,
                                     'staff_info_id' => "%$keyword%",
                                     'name'          => "%$keyword%",
                    ],
                    "columns"    => $fields,
                    "order"      => "hire_date ASC",
                ])->toArray();
            }
        }
        return $result;
    }

    /**
     * 获取用户信息
     * @param $staffId
     * @param string $fieldsStr
     * @return mixed
     */
    public static function getUserInfoByStaffInfoIds(array $staffIds, string $fieldsStr = '*')
    {
        if (empty($staffIds)) {
            return [];
        }

        $staffInfos = HrStaffInfoModel::find([
                                                    "conditions" => "staff_info_id in ({staffs:array})",
                                                    "bind"       => array('staffs' => $staffIds),
                                                    "columns"    => $fieldsStr
                                                ]);

        return !empty($staffInfos)? $staffInfos->toArray() : [];
    }

    /**
     * @description 查询员工信息
     * @param $staff_info_id
     * @return array
     */
    public function getStaffInfo($staff_info_id): array
    {
        //查询
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("hsi.staff_info_id,
            hsi.job_title,
            hsi.node_department_id,
            group_concat(hsip.position_category) as position_category,
            hsi.name as staff_info_name,
            hsi.mobile,
            hsi.hire_type,
            hsi.hire_times");
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftjoin(HrStaffInfoPositionModel::class, "hsip.staff_info_id = hsi.staff_info_id", "hsip");
        $builder->andWhere('hsi.staff_info_id = :staff_id:', ['staff_id' => $staff_info_id]);
        $builder->groupby('hsi.staff_info_id');
        $staffInfo = $builder->getQuery()->getSingleResult();
        if (empty($staffInfo)) {
            return [];
        }
        return $staffInfo->toArray();
    }

    /**
     * 获取职位-更具OA关联体系获取
     * @param $params
     * @return array
     */
    public function rolesListRelation($params): array
    {
        $rolesList = HrJobTitleRoleModel::find([
            'columns'    => 'role_id',
            'conditions' => 'sys_depeartment_id = :department_id: and job_title_id = :job_title_id:',
            'bind'       => [
                'department_id' => $params['department_id'],
                'job_title_id'  => $params['position_id']
            ],
        ])->toArray();

        return array_column($rolesList, 'role_id');
    }
}