<?php
/**
 * Created by PhpStorm.
 * User: zhaowei
 * Date: 2021/3/16
 * Time: 15:56
 */

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\HrStaffContractEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractLogModel;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SubStaffContractMessageModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;

class HrStaffContractServer extends BaseServer
{
    private static $instance;

    public static function getInstance($lang , $timezone)
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self($lang , $timezone);
        }
        return self::$instance;

    }


    /**
     * 合同签字或反馈 处理
     * @param $paramIn
     * @param $type 1: 签字，2：反馈
     * @throws \Exception
     */
    public function doContractSignAndFeedback($paramIn, $type, $staff_id)
    {
        $ct_id          = $paramIn['ct_id'] ?? 0;           //合同ID
        $sign_img       = $paramIn['sign_img'] ?? '';       //签字消息图片（签字操作有此参数）
        $operate_reason = $paramIn['operate_reason'] ?? ''; //拒绝内容

        //当前登录人可能是主账号也可能是子账号
        $staff_info_ids[] = intval($staff_id);
        $staff_info_ids[] = $master_staff_id = $this->getMasterStaffId($staff_id);

        //根据合同ID查询当前合同数据
        $contractObj = HrStaffContractModel::FindFirst([
            'conditions' => 'id = :id: and staff_id in ({staff_ids:array}) and contract_is_need = 1 and contract_is_deleted = 0',
            'bind'       => [
                'id'       => intval($ct_id),
                'staff_ids' => $staff_info_ids,
            ],
        ]);
        if (empty($contractObj)) {
            throw new BusinessException($this->getTranslation()->_('contract_empty_error'));
        }
        $contract_detail   = $contractObj->toArray();

        $status_limit = [enums::CONTRACT_STATUS_SIGNATURE, enums::CONTRACT_STATUS_UNSIGNED];
        //判断合同状态是否有效（ 只有 待签字、未签字的合同可以进行此操作）
        if (!in_array($contract_detail['contract_status'], $status_limit)) {
            $this->getDI()->get('logger')->write_log("当前合同状态无效，不能进行签字或反馈。合同ID：{$ct_id},当前合同状态：{$contract_detail['contract_status']}",'info');
            throw new BusinessException($this->getTranslation()->_('4012'));
        }
        //合同发送消息的ID
        $contract_msg_id = $contract_detail['contract_msg_id'] ?? '';
        if (empty($contract_msg_id)) {
            $this->getDI()->get('logger')->write_log("合同数据异常，缺少签字消息ID。合同ID：{$ct_id}");
            throw new BusinessException($this->getTranslation()->_('message_no_exists'));
        }

        switch ($type) {
            case HrStaffContractEnums::CONTRACT_OPT_TYPE_AGREE: //签字处理（更改状态为待审核、保存签字消息图片）
                $contract_up_data = ['contract_status' => enums::CONTRACT_STATUS_SIGN_PROCESSING, 'contract_signature_img' => $sign_img];
                break;
            case 2: //反馈处理（更改合同装填为"已反馈"
                $contract_up_data = ['contract_status' => enums::CONTRACT_STATUS_FEEDBACK];
                break;
            case HrStaffContractEnums::CONTRACT_OPT_TYPE_REFUSE: //拒绝
                $contract_up_data = ['contract_status' => enums::CONTRACT_STATUS_REFUSE];
                break;
            case HrStaffContractEnums::CONTRACT_OPT_TYPE_TIME_OUT:
                $contract_up_data = ['contract_status' => enums::CONTRACT_STATUS_UNSIGNED];
                break;
        }


        $db = $this->getDI()->get('db');
        $db->begin();
        $db_coupon = $this->getDI()->get('db_coupon');
        $do_coupon = false; //是否开启coupon库事物处理
        try {

            //todo step1 更新合同表
            $contract_up_res = $db->updateAsDict('hr_staff_contract',
                $contract_up_data,
                ['conditions' => 'id =' . $ct_id]
            );
            if (!$contract_up_res) {
                throw new \Exception("合同 签字/反馈 操作 更新合同表操作失败，更新数据：" . json_encode($contract_up_data) . ", 合同更新条件：" . $contract_up_res);
            }


            //todo step2  消息处理
            //批量发送：多个合同共用同一个消息ID
            //单个发送：每个合同单独对应一个消息ID
            //消息变为已读逻辑：没有待签署或未签字 状态的合同时更新消息为已读
            //检查除当前合同外还有没有其他 待签署、未签字 状态的合同，如果没有则将消息置为已读，否则只更新合同状态
            $contractOtherModel = HrStaffContractModel::Find([
                'conditions' => 'contract_msg_id = :contract_msg_id: and id != :id: and contract_is_deleted=0 and contract_is_need = 1 and contract_status in ({contract_status:array})',
                'bind'       => [
                    'id'              => $ct_id,
                    'contract_msg_id' => $contract_msg_id,
                    'contract_status' => $status_limit,
                ],
            ])->toArray();
            //为空 代表是 当前处理的合同是 单个发送的合同或者是批量发送的最后一个 （待签字或未签字）状态的合同
            if (empty($contractOtherModel)) {
                $do_coupon = true;
                $db_coupon->begin();
                //合同表的，一定是主账号的消息
                $msg_up_data = ['read_state' => 1];
                $msg_up_cond = "id ='{$contract_msg_id}'";
                $msg_up_res  = $db_coupon->updateAsDict('message_courier', $msg_up_data, ['conditions' => $msg_up_cond]);

                //获取子账号的消息
                $contractMessage = SubStaffContractMessageModel::findFirst([
                    'conditions' => 'staff_info_id in ({staff_info_ids:array}) and main_account_msg_id = :main_account_msg_id:',
                    'bind'       => [
                        'staff_info_ids'      => $staff_info_ids,
                        'main_account_msg_id' => $contract_msg_id,
                    ],
                    'order'      => 'id desc',
                ]);
                if ($contractMessage) {
                    //子账号的
                    $this->getDI()->get('db_coupon')->updateAsDict('message_courier', ['read_state' => 1], [
                        'conditions' => "id = ? and category = ? and read_state = 0",
                        'bind'       => [$contractMessage->sub_msg_id, MessageEnums::MESSAGE_CATEGORY_CONTRACT],
                    ]);
                }

                if (!$msg_up_res) {
                    throw new \Exception("合同 签字/反馈 操作 更新coupon消息表操作失败，更新数据：" . json_encode($msg_up_data) . ", 更新条件：" . $msg_up_cond);
                }

            }

            //马来新增拒绝消息
            if (isCountry('MY') && ($type == HrStaffContractEnums::CONTRACT_OPT_TYPE_REFUSE)) {
                //通知上级
                $winhrRpc = (new ApiClient("winhr_rpc", '', 'setContractRefuse', $this->lang));
                $winhrRpc->setParams($paramIn);
                $result = $winhrRpc->execute();

                if (empty($result['result']['code']) || $result['result']['code'] != 1) {
                    throw new \Exception("合同拒绝操作，消息发送失败 " . json_encode($result));
                }

                $staff_id = $contract_detail['staff_id'] ?? 0;
                $staff_info = (new HrStaffInfoModel())->getOneByStaffId($staff_id);

                //插入一条日志
                $logModel = (new HrStaffContractLogModel());

                $logModel->staff_id           = $staff_id;
                $logModel->operate_staff_id   = $staff_id;
                $logModel->operate_staff_name = $staff_info['name'] ?? '';
                $logModel->contract_id        = $contract_detail['id'] ?? 0;
                $logModel->contract_type      = $contract_detail['contract_type'] ?? 0;
                $logModel->action             = HrStaffContractLogModel::CONTRACT_LOG_OPTION_REFUSE;
                $logModel->operate_reason     = $operate_reason;

                if (!$logModel->save()) {
                    throw new BusinessException($this->getTranslation()->_('4012'));
                }
            }
            if (isCountry('MY') && $type == HrStaffContractEnums::CONTRACT_OPT_TYPE_AGREE && $contract_detail['contract_date_is_long'] == HrStaffContractModel::CONTRACT_DATE_IS_LONG_NO && $contract_detail['contract_type'] == HrStaffContractModel::CONTRACT_TYPE_LABOR_CONTRACT){
                // 向hcm同步合同到期日
                $syncData = [
                    'staff_info_id'        => $staff_id,
                    'contract_expiry_date' => $contract_detail['contract_end_date'],
                ];
                $ac = new ApiClient('hr_rpc', '', 'update_staff_info', $this->lang);
                $ac->setParams($syncData);
                $result = $ac->execute();
                $this->getDI()->get("logger")->write_log('contract 同步hris返回信息 ' .json_encode($result, JSON_UNESCAPED_UNICODE), "info");
                if (!isset($result['result']['code']) || $result['result']['code'] != 1) {
                    $this->getDI()->get('logger')->write_log("向hcm同步合同到期日失败 syncData:".json_encode($syncData, JSON_UNESCAPED_UNICODE) ." ;contract_detail:".json_encode($contract_detail, JSON_UNESCAPED_UNICODE),'error');
                }
            }

            //提交事物
            $db->commit();
            $do_coupon && $db_coupon->commit();

            //没有待签署的  通知一下ms
            if (isCountry(['TH','PH','MY'])) {
                $staffInfo = (new StaffServer())->getStaffById($staff_id);
                if (in_array($staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
                    //获取子账号
                    if (!$master_staff_id) {
                        $sub_staff_id     = $this->getSubStaffId($staff_id);
                        $staff_info_ids[] = $sub_staff_id;
                    }
                    $wait_sign_num = $this->getWaitSingContractNum($staff_info_ids);
                    if ($wait_sign_num == 0) {
                        foreach (array_unique($staff_info_ids) as $value) {
                            $this->syncContractSignToJava($value);
                        }
                    }
                    if (isCountry('MY') && $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                        $need_staff_id = $master_staff_id ? : $staff_id;
                        $push_data = json_encode(['staff_info_id' => $need_staff_id, 'sign_date' => date('Y-m-d')]);
                        $this->getDI()->get("logger")->write_log('after sign contract need short notice reduction  staff id : ' .$push_data, "info");
                        $this->getDI()->get('redisLib')->lpush(RedisEnums::LIST_MY_REDUCTION_SHORT_NOTICE_PDF, $push_data);
                    }
                }
            }

            return ['wait_sign_num' => $wait_sign_num ?? 0];

        } catch (\Exception $e) {
            //回滚事物
            $db->rollback();
            $do_coupon && $db_coupon->rollback();
            throw new \Exception($e->getMessage() . $e->getTraceAsString());

        }


    }


    public function getWaitSingContractNum(array $staff_info_ids): int
    {
        if(empty($staff_info_ids)){
            return 0;
        }
        $contract_status = [
            enums::CONTRACT_STATUS_SIGNATURE,
            enums::CONTRACT_STATUS_UNSIGNED,
            enums::CONTRACT_STATUS_REFUSE,
        ];
        $sql             = "select count(*) as num from hr_staff_contract where staff_id in (" . implode(',',
                $staff_info_ids) . ") and contract_is_need = 1 and contract_is_deleted = 0 and contract_status in (" . implode(',',
                $contract_status) . ")";
        $arr             = $this->getDI()->get("db")->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC);
        return intval($arr['num']);
    }


    /**
     * 获取合同详情
     * @param $ct_id
     * @return array
     */
    public function getContractDetail($ct_id, $staff_id): array
    {
        $staff_info_ids[] = intval($staff_id);
        $staff_info_ids[] = $this->getMasterStaffId($staff_id);

        $contract_detail = HrStaffContractModel::FindFirst([
            'conditions' => 'id = :id: and staff_id in ({staff_ids:array}) and contract_is_need=1 and contract_is_deleted=0',
            'bind'       => [
                'id'       => intval($ct_id),
                'staff_ids' => $staff_info_ids,
            ],
        ]);
        if (empty($contract_detail)) {
            return [];
        }
        $contract_detail   = $contract_detail->toArray();
        $img_prefix        = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
        $contract_pdf_path = $contract_detail['contract_path'] ? $img_prefix . $contract_detail['contract_path'] : '';
        return [
            'staff_id'      => $contract_detail['staff_id'],
            'staff_name'    => $contract_detail['staff_name'],
            'ct_id'         => $contract_detail['id'],
            'ct_status'     => $contract_detail['contract_status'],
            'ct_pdf_url'    => $contract_pdf_path,
        ];

    }

    /**
     * 获取子账号
     * @param $staff_info_id
     * @return int
     */
    protected function getSubStaffId($staff_info_id): int
    {
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timeZone))->getSupportOsStaffInfo($staff_info_id);
        return intval($supportStaffInfo['sub_staff_info_id'] ?? 0);
    }


    /**
     * 获取主账号
     * @param $staff_info_id
     * @return int
     */
    protected function getMasterStaffId($staff_info_id): int
    {
        //查看支援信息
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timeZone))->getSupportInfoBySubStaff($staff_info_id);
        return intval($supportStaffInfo['staff_info_id'] ?? 0);
    }


    /**
     * 获取该签字消息下是否还有待签字、未签字的合同
     * @param $staff_info_id
     * @param $msg_id
     */
    public function isShowSignBtn($staff_info_id,$msg_id)
    {
        $staff_info_ids[] = intval($staff_info_id);
        $staff_info_ids[] = $master_staff_id =   $this->getMasterStaffId($staff_info_id);
        $staffInfo = (new StaffServer())->getStaffById($staff_info_id);

        $status = [enums::CONTRACT_STATUS_SIGNATURE, enums::CONTRACT_STATUS_UNSIGNED];

        if (isCountry('MY')) {
            $status = [enums::CONTRACT_STATUS_SIGNATURE];
        }

        $contract_list = HrStaffContractModel::Find([
            'conditions' => 'staff_id in ({staff_info_ids:array}) and contract_status in ({contract_status:array}) and contract_is_deleted = 0 and contract_is_need = 1',
            'bind'       => [
                'staff_info_ids' => $staff_info_ids,
                'contract_status' => $status,
            ],
        ])->toArray();

        $staff_info_ids_str = implode(',',$staff_info_ids);
        $reject_content = "";
        if (empty($contract_list)){
            $is_show_sign_btn = false;
            $this->getDI()->get('db_coupon')->updateAsDict('message_courier', ['read_state' => 1], ['conditions' => "staff_info_id = ? and read_state = 0 and category = 36 ",'bind'=>[$staff_info_id]]);
            if ($master_staff_id) {
                $this->getDI()->get('db_coupon')->updateAsDict('message_courier', ['read_state' => 1],
                    ['conditions' => "staff_info_id = ? and read_state = 0 and category = 36 ",'bind'=>[$master_staff_id]]);
            }
        }else{
            $is_show_sign_btn = true;
            //获取主账号的消息
            $contractMessage = SubStaffContractMessageModel::findFirst([
                'conditions' => 'staff_info_id in ({staff_info_ids:array}) and sub_msg_id = :sub_msg_id:',
                'bind'       => [
                    'staff_info_ids' => $staff_info_ids,
                    'sub_msg_id'     => $msg_id,
                ],
                'order'      => 'id desc',
            ]);
            if ($contractMessage) {
                $msg_id = $contractMessage->main_account_msg_id;
            }

            // 取驳回原因
            $sql = "SELECT b.reject_content FROM hr_staff_contract AS a LEFT JOIN hr_staff_contract_log AS b ON a.id=b.contract_id WHERE a.staff_id in (".$staff_info_ids_str.") and a.contract_msg_id = '$msg_id' AND b.action = 10 AND b.is_deleted = 0 order by b.id desc";
            $data = $this->getDI()->get("db_rby")->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
            if ($data){
                $reject_content = $data["reject_content"];
            }
        }

        $returnArr['data'] = ['is_show_sign_btn' => $is_show_sign_btn,'reject_content'=>$reject_content,'hire_type'=>$staffInfo['hire_type']];
        return $this->checkReturn($returnArr);

    }

    /**
     * 删除合同对应的消息
     * 已发送消息后对合同进行了删除，此时消息置为已读，否则一直有未读消息（影响打卡）
     * @param $msg_id
     */
    public function changeMsgReadStatus($msg_id)
    {
        $return         = ['code' => 1];
        $messageCourier = MessageCourierModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => [
                'id' => $msg_id,
            ]
        ]);

        if ($messageCourier) {
            //获取子账号的消息
            $contractMessage = SubStaffContractMessageModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and main_account_msg_id = :main_account_msg_id:',
                'bind'       => [
                    'staff_info_id'       => $messageCourier->staff_info_id,
                    'main_account_msg_id' => $msg_id,
                ],
                'order'      => 'id desc',
            ]);
            if ($contractMessage) {
                $this->getDI()->get('db_coupon')->updateAsDict('message_courier', ['read_state' => 1],
                    ['conditions' => "id = ?", 'bind' => [$contractMessage->sub_msg_id]]);
            }
            $this->getDI()->get('db_coupon')->updateAsDict('message_courier', ['read_state' => 1],
                ['conditions' => "id = ?", 'bind' => [$msg_id]]);
        }

        return $this->checkReturn($return);
    }

    public function syncContractSignToJava($staff_info_id,$signing_state = 1)
    {
        if(empty($staff_info_id)){
            return true;
        }
        //通知ms员工合同未签署
        $sendData['jsonCondition']    = json_encode(['staff_id' => intval($staff_info_id), 'signing_state' => $signing_state]);
        $sendData['handleType']       = RocketMQ::TAG_HR_MODIFY_STAFF_SIGNING_STATUS;
        $sendData['shardingOrderKey'] = $staff_info_id;
        $rmq                          = new RocketMQ('staff-contract-change-to-java');
        $rmq->setShardingKey($staff_info_id);
        $rid                          = $rmq->sendOrderlyMsg($sendData);//有序
        $this->logger->write_log('staff-contract-change-to-java rid:' . $rid . 'data:' . json_encode($sendData),
            $rid ? 'info' : 'error');
        return $rid;

    }

}