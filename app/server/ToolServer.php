<?php
/**
 * Author: Bruce
 * Date  : 2023-04-18 22:20
 * Description:
 */

namespace FlashExpress\bi\App\Server;


use Exception;
use FlashExpress\bi\App\Enums\MenuEnums;
use FlashExpress\bi\App\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\FNumLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffCriminalRecordModel;
use FlashExpress\bi\App\Models\backyard\VanContainerModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

class ToolServer extends BaseServer
{
    public $timezone;

    protected $staffInfo;
    protected $masterStaffFlag;//子账号登录时的时候，这个值就是主账号的token

    /**
     * 个人信息-提成展示
     * @var
     */
    protected $incentiveShow = false;
    public function setStaffInfo($staffInfo)
    {
        $this->staffInfo = $staffInfo;
    }
    //子账号登录时的时候，这个值就是主账号的token
    public function setMasterStaffFlag($masterStaffFlag)
    {
        $this->masterStaffFlag = $masterStaffFlag;
    }

    public function setShowIncentive($staffId)
    {
        if (!isCountry(['PH', 'TH'])) {
            return;
        }
        $incentiveData = (new PersoninfoServer($this->lang, $this->timezone))->getIncentiveFromBI($staffId);
        if ($incentiveData) {
            $this->incentiveShow = true;
        }
    }

    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
    }

    /**
     *
     * @param $paramIn
     * @return mixed
     * @throws BusinessException
     */
    public function uploadToTMS($paramIn)
    {
        $params['fileName'] = $paramIn['file_name'];
        $api    = new RestClient('tms');
        $res    = $api->execute(RestClient::METHOD_GET, '/svc/file/upload/img_url',
            $params, ['Accept-Language' => $this->lang]);
        if (!isset($res['code']) || $res['code'] != 1 || empty($res['data'])) {
            throw new BusinessException($this->getTranslation()->_('server_error'));
        }
        return $res['data'];
    }


    /**
     * 文件上传
     * @param $paramIn
     * @return array
     * @throws Exception
     */
    public function img_upload($paramIn)
    {
        //接受文件名字
        $filename = $this->processingDefault($paramIn, 'file_name');
        $fileType = $this->processingDefault($paramIn, 'file_type');
        if (isset($paramIn['bucketType']) &&  $paramIn['bucketType'] === 'TO_TMS') {
            $result = $this->uploadToTMS($paramIn);
            return $this->checkReturn(['data' => $result]);
        }

        $bucketType = $fileType == 'FLEET_AUDIT' ? 'FLEET_AUDIT': 'BACKYARD_UPLOAD';

        if (env('break_away_from_ms')) {
            return $this->checkReturn(['data' => OssHelper::uploadFileHcm($bucketType, $filename)]);
        }

        $param    = [
            $bucketType,
            uniqid().'.'.pathinfo($filename, PATHINFO_EXTENSION),
            $fileType,
        ];

        $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.OssSvc','buildPutObjectUrl', $this->lang);
        $fle_rpc->setParam($param);
        $return = $fle_rpc->execute();

        //文件上传成功
        if(isset($return['result'])) {
            return $this->checkReturn(['data' => $return['result']]);
        }
        //文件上传失败
        return $this->checkReturn(-3, '远端接口出错');
    }
    //带 签名和 accsee key 客户端要用
    public function uploadFromJava($staffId, $filename){
        if (empty($filename)) {
            throw new ValidationException('parameter error need fileName');
        }

        $api = new RestClient('pms');
        $param['bucketType'] = 'VAN_CAR_FUEL_SUPPLEMENT_FEE';
        $param['fileName'] = $filename;
        $param['prefix'] = '';
        $res = $api->execute(RestClient::METHOD_POST, '/svc/oss/public/file/prepare/upload', $param);
        if(empty($res['code']) || $res['code'] != ErrCode::SUCCESS){
            throw new ValidationException('uploadFromJava from java error');
        }
        return $this->checkReturn(['data' => $res['data']]);
    }

    /**
     * 获取  PDF
     * @param $pdfTempFile
     * @param $expire
     * @return mixed
     * @throws \Exception
     */
    public function getPdfTemp($pdfTempFile, $expire = 86400 * 30)
    {
        $redis_key = md5_file($pdfTempFile);

        $cache       = $this->getDI()->get('redisLib');
        $redis_value = $cache->get($redis_key);

        if (!empty($redis_value)) {
            $pdfTempUrl = $redis_value;
        } else {
            //上传oss 更新表
            $result     = $this->uploadFileOss($pdfTempFile, self::OSS_DIR_MAPS[self::CERTIFICATE_PDF]);
            $pdfTempUrl = $result['object_url'];//oss地址
            //30天
            $cache->set($redis_key, $result['object_url'], $expire);
        }

        return $pdfTempUrl;
    }

    /**
     * 获取个人信息菜单列表
     * @param $params
     * @return array
     */
    public function getPersonalInformationMenuList($params): array
    {
        $platform = $params['platform'] ?? enums::FB_BACKYARD;
        //姓名工号
        $menu_list[] = $this->getMenuStaffName($params);
        //基本信息
        $menu_list[] = $this->getMenuBaseInformation($params);
        //工资
        $menu_list[] = $this->getMenuSalary($params);
        //电子合同
        if ($platform != enums::RB_KIT) {
            $menu_list[] = $this->getMenuElectronicContract($params);
        }

        // 车辆信息(v21270 增加BY入口)
        $menu_list[] = $this->getMenuVehicleInfo($params);

        return array_values(array_filter($menu_list));
    }

    //type = 1:不跳转 2:原生页面 3:原生页面需要输入密码再跳转 4:h5页面 5:h5页面需要输入密码再跳转
    /**
     * 个人信息菜单 - 姓名/工号 默认显示 无跳转 无红点 无左侧状态文本
     * @param $params
     * @return array
     */
    public function getMenuStaffName($params): array
    {
        $title = 'personal_information_menu_staff_name_id';
        if (isCountry('TH') && $this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            $title = 'agent_staff_id_call_name';
        }
        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_staff_name_id',
            'title' => $this->t->_($title), //菜单标题
            'type'  => MenuEnums::MENU_DST_TYPE_NONE, //跳转类型
            'right_text' => $this->staffInfo['name'].'/'.$this->staffInfo['id'], //右侧文字
        ]);
    }

    /**
     * 个人信息菜单 - 基本信息 默认显示 h5链接 有红点 无右侧文字 无右侧状态文本
     * @param $params
     * @return array
     */
    public function getMenuBaseInformation($params): array
    {

        $staff_info_id = $this->staffInfo['id'];
        $read_count    = $this->getMenuBaseInformationRedCount($staff_info_id);
        return $this->combinationMenuParams([
            'id'         => 'personal_information_menu_base_information',
            'title'      => $this->t->_('personal_information_menu_base_information'), //菜单标题
            'type'       => MenuEnums::MENU_DST_TYPE_H5_PAGE,                          //跳转类型
            'dst'        => env('sign_url') . '/#/BasicInfo',                   //跳转链接
            'read_count' => !empty($read_count) ? (string)$read_count : '',            //红点数
        ]);
    }

    /**
     * 个人信息菜单提成
     * @return array
     */
    public function getMenuCommission(): array
    {
        if (empty($this->staffInfo)) {
            return [];
        }
        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_salary',
            'title' => $this->t->_('personal_information_menu_commission'), //菜单标题
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE_PASSWORD, //跳转类型
            'dst'   => env('h5_endpoint') . 'view-commission', //跳转链接
        ]);
    }
    /**
     * 个人信息菜单 - 工资 默认显示 输入密码跳转h5 无红点 无右侧文字 无右侧文本状态
     * @param $params
     * @return array
     */
    public function getMenuSalary($params): array
    {
        if (empty($this->staffInfo)) {
            return [];
        }

        if ($this->staffInfo['organization_type'] == 1 && ($this->staffInfo['formal'] == 0 || $this->staffInfo['category'] == 6 || $this->staffInfo['is_sub_staff'] == 1)) {
            return [];
        }

        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_salary',
            'title' => $this->t->_('personal_information_menu_salary'), //菜单标题
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE_PASSWORD, //跳转类型
            'dst'   => env('h5_endpoint') . 'payslip', //跳转链接
        ]);
    }

    /**
     * 全勤奖
     * @param $params
     * @return array
     */
    public function getMenuFullAttendanceReward()
    {
        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_full_reward',
            'title' => $this->t->_('personal_information_menu_full_reward'), //菜单标题
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE_PASSWORD,            //跳转类型
            'dst'   => env('h5_endpoint') . 'hub-full-attendance',           //跳转链接
        ]);
    }

    /**
     * 个人信息菜单 - 电子合同 默认显示 h5链接 无红点 无右侧文字 无右侧文本状态
     * @param $params
     * @return array
     */
    public function getMenuElectronicContract($params): array
    {
        $right_icon = '';
        $staff_info_id = $this->staffInfo['staff_info_id'];

        if($this->isUnhandleContract($staff_info_id)) {
            $right_icon = 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1693907635-940b6fbd221c4298a372dd0ec1da4a2b.png';
        }

        $title = 'personal_information_menu_electronic_contract';
        if ($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            $title = 'personal_information_menu_electronic_contract_agent';
        }

        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_electronic_contract',
            'title' => $this->t->_($title), //菜单标题,
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE_PASSWORD, //菜单类型 需要输入密码再跳转h5
            'dst'   => env('sign_url') . '/#/electronic-contract', //跳转链接
            'right_icon' => $right_icon, //右侧图标
        ]);
    }

    /**
     * 个人信息菜单 - 犯罪记录 除ph之外其他国家（新版只有th） 默认显示 h5链接 无红点 无右侧文本 有右侧状态文本
     * @param $params
     * @return array
     */
    public function getMenuCriminalRecord($params): array
    {
        $staff_info_id = $this->staffInfo['id'];
        $criminalRecord = StaffCriminalRecordModel::findFirst([
            'conditions' => ' staff_info_id = :staff_info_id: ',
            'bind' => [
                'staff_info_id' => $staff_info_id,
            ],
        ]);

        $right_state_text = '';
        $right_state_text_background_color = '';
        if (!empty($criminalRecord)) {
            if(in_array($criminalRecord->review_status, [CriminalServer::REVIEW_STATUS_0,CriminalServer::REVIEW_STATUS_3])) {
                $right_state_text = $this->t->_('criminal_info_approval_status_' . $criminalRecord->review_status);
                $right_state_text_background_color = 'FFEA33';
            }
        } else {
            $right_state_text = $this->t->_('criminal_info_approval_status_0');
            $right_state_text_background_color = 'FFEA33';
        }

        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_criminal_record',
            'title' => $this->t->_('personal_information_menu_criminal_record'), //菜单标题
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE_PASSWORD, //菜单类型 需要输入密码再跳转
            'dst'   => env('h5_endpoint') . 'check-criminal', //跳转链接
            'right_state_text' => $right_state_text, //右侧状态文本
            'right_state_text_background_color' => $right_state_text_background_color, //状态文字背景色
        ]);
    }

    /**
     * kit 车辆信息 kit 默认显示 h5链接 无红点 无右侧文本 有右侧状态文本
     * @param $params
     * @return array
     */
    public function getMenuVehicleInfo($params): array
    {
        $staff_info_id = $this->staffInfo['staff_info_id'];
        $staffInfo     = $this->staffInfo;
        if (empty($staffInfo) || $staffInfo['formal'] != 1) {
            // 不是编制内，需要隐藏车辆信息栏的填写。
            return [];
        }
        
        if (isCountry(['TH','PH','MY','LA'])) {
            // 当前登入者职位id
            $curLoginUserJobTitleId = trim($staffInfo['job_title']);
            // 快递员职位
            $courierJobStr          = (new SettingEnvServer())->getSetVal('job_title_vehicle_type');
            $courierJobIds          = explode(',', $courierJobStr);
            if (!in_array($curLoginUserJobTitleId, $courierJobIds)) {
                // 如果当前登入者职位不是快递员，则隐藏车辆信息编辑栏
                return [];
            }
        }

        $right_state_text = $this->t->_('personal_information_vehicle_information_tips_1');//请提交车辆信息
        $right_state_text_background_color = 'FFEA33';

        $info_data = VehicleInfoModel::findFirst([
            'conditions' => 'uid = :uid: ',
            'bind' => ['uid' => $staff_info_id],
            'columns' => ['id', 'approval_status'],
        ]);

        if (!empty($info_data) && in_array($info_data->approval_status, [1, 2,VehicleInfoEnums::APPROVAL_WAIT_NW_CODE])) {
            $right_state_text = '';
            $right_state_text_background_color = '';
        }

        $h5NewPage = (new VehicleServer($this->lang, $this->timezone))->h5NewPageMobileVersion();
        $url       = env('sign_url') . '#/VehicleInfo.pages';
        if ($h5NewPage) {
            $url = env('sign_url') . '#/VehicleInfo.pages.new';
        }
        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_vehicle_info',
            'title' => $this->t->_('personal_information_menu_vehicle_info'), //菜单标题
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE, //菜单类型
            'dst'   => $url, //跳转链接
            'right_state_text' => $right_state_text, //右侧状态文本
            'right_state_text_background_color' => $right_state_text_background_color, //状态文字背景色
        ]);
    }

    /**
     * 车厢信息 by kit 都有 van 职位
     * @param $param
     * @return array
     */
    public function getVanContainer($param){

        $s = new VanContainerServer($this->lang, $this->timezone);
        if (!$s->isShowVanContainer($this->staffInfo,false)) {
            return [];
        }

        //如果入职日期 < 2024-12-07 不展示入口
//        $limitDate = (new SettingEnvServer())->getSetVal('van_apply_date');
//        if(empty($limitDate) || $this->staffInfo['hire_date'] < $limitDate){
//            return [];
//        }

        //找最新的一天记录 如果是驳回 有提示语
        $right_state_text = '';
        $info = VanContainerModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: ',
            'bind' => ['staff_id' => $this->staffInfo['staff_info_id']],
            'order' => 'id desc',
        ]);
        if(!empty($info) && $info->state == enums::$audit_status['dismissed']){
            $right_state_text = $this->t->_('personal_information_van_container_text');
        }
        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_van_container',
            'title' => $this->t->_('personal_information_menu_van_container'), //菜单标题
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE, //菜单类型
            'dst'   => env('h5_endpoint') . 'car-information', //跳转链接
            'right_state_text' => $right_state_text, //右侧状态文本
            'right_state_text_background_color' => '', //状态文字背景色 没说颜色 走默认
        ]);
    }

    /**
     * 组合菜单参数
     * @param $params
     * @return array
     */
    public function combinationMenuParams($params): array
    {
        $id               = $params['id'] ?? '';    //菜单id标识
        $icon             = $params['icon'] ?? '';  //菜单图标 默认空 可传图片url链接
        $title            = $params['title'] ?? ''; //菜单标题
        $type             = $params['type'] ?? '1'; //跳转类型type = 1:不跳转 2:原生页面 3:原生页面需要输入密码再跳转 4:h5页面 5:h5页面需要输入密码再跳转
        $dst              = $params['dst'] ?? '';   //跳转地址
        $read_count       = $params['read_count'] ?? ''; //红点数量 默认空 不显示红点
        $right_text       = $params['right_text'] ?? ''; //右侧显示文本
        $right_state_text = $params['right_state_text'] ?? ''; //右侧显示状态文本
        $right_state_text_background_color = $params['right_state_text_background_color'] ?? ''; //右侧显示状态文本背景色
        $right_icon       = $params['right_icon'] ?? ''; //右侧图标 例如电子合同感叹号
        if ($this->masterStaffFlag && in_array($type,[MenuEnums::MENU_DST_TYPE_H5_PAGE,MenuEnums::MENU_DST_TYPE_H5_PAGE_PASSWORD])) {
            $questionMarkPosition = strpos($dst, '?');
            // 查找是否有问号
            if ($questionMarkPosition !== false) {
                $dst = $dst. '&sub_to_master_token='. $this->masterStaffFlag;
            } else {
                $dst = $dst. '?sub_to_master_token='. $this->masterStaffFlag;
            }
        }
        $right_font_color = $params['right_font_color'] ?? '333333';//右侧文字颜色
        return [
            'id'                                => $id,                                //菜单id标识
            'icon'                              => $icon,                              //菜单图标
            'title'                             => $title,                             //菜单标题
            'type'                              => $type,                              //跳转类型
            'dst'                               => $dst,                               //跳转链接
            'read_count'                        => $read_count,                        //红点数
            'right_text'                        => $right_text,                        //右侧文字
            'right_state_text'                  => $right_state_text,                  //右侧状态文本
            'right_state_text_background_color' => $right_state_text_background_color, //状态文字背景色
            'right_icon'                        => $right_icon,                        //右侧图标
            'right_font_color'                  => $right_font_color,                  //右侧文字颜色
        ];
    }

    /**
     * 基本信息红点菜单
     * @param $staff_info_id
     * @return int
     */
    public function getMenuBaseInformationRedCount($staff_info_id): int
    {
        $read_count          = 0;
        // vn id my 该表都没有银行卡数据
//        $identity_annex_info = HrStaffIdentityAnnexModel::findFirst([
//            'conditions' => 'staff_info_id = :staff_info_id:',
//            'bind'       => ['staff_info_id' => $staff_info_id],
//        ]);
        $identity_annex_info  = HrStaffAnnexInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type'=>HrStaffAnnexInfoModel::TYPE_ID_CARD],
        ]);

        if(empty($identity_annex_info)) {
            $read_count++;
        } else {
            if(!in_array($identity_annex_info->audit_state, [0, 1])) {
                $read_count++;
            }
        }
        return $read_count;
    }

    /**
     * 获取个人信息红点总数
     * @param $staff_info_id
     * @return int
     */
    public function getPersonalInformationRedCount($staff_info_id): int
    {
        return $this->getMenuBaseInformationRedCount($staff_info_id);
    }

    /**
     * 是否有未签署的合同
     * @param $params
     * @return bool
     */
    public function isUnhandleContract($staff_info_id): bool
    {
        $contractObj = HrStaffContractModel::find([
            'conditions' => 'staff_id = :staff_id: and contract_is_deleted = 0 and contract_is_need = 1 and contract_status=30',
            'bind'       => [
                'staff_id' => $staff_info_id,
            ],
        ])->toArray();
        return !empty($contractObj);
    }

    public function saveFNumData($param){
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        if ($param["f_num_log_id"]){
            $f_num_log = FNumLogModel::findFirst([
                'conditions' => 'id = :f_num_log_id:',
                'bind'       => ['f_num_log_id' => $param["f_num_log_id"]],
            ]);
            $f_num_log->is_send_sms = $param["is_send_sms"];
            $f_num_log->send_sms_time = date("Y-m-d H:i:s",time() - ($add_hour * 3600));
            if ($f_num_log->save()){
                return $param["f_num_log_id"];
            }else{
                $this->getDI()->get('logger')->write_log(" saveFNumData param : ".json_encode($param, JSON_UNESCAPED_UNICODE), 'error');
                return 0;
            }
        }else{
            if (empty($param["staff_info_id"])){
                return 0;
            }
            $save_param["staff_info_id"] = $param["staff_info_id"];
            $save_param["real_phone"] = $param["real_phone"] ?? '';
            $save_param["f_num"] = $param["f_num"] ?? '';
            $save_param["call_time"] = $param["call_time"] ? date("Y-m-d H:i:s", strtotime($param["call_time"]) - ($add_hour * 3600)) : null;
            $save_param["ringing_duration"] = $param["ringing_duration"] ?? 0;
            $save_param["is_dialing_through"] = $param["is_dialing_through"] ?? FNumLogModel::IS_DIALING_THROUGH_NO;
            $save_param["call_duration"] = $param["call_duration"] ?? 0;
            $model = new FNumLogModel();
            $result = $model->create($save_param);
            $id = $result ? $model->id : 0;
        }
        return $id;
    }

    //是否隐藏【设置】入口
    public static function isHiddenSettingConfig($formal, $is_sub_staff, $staff_id): bool
    {
        $staffInfo = (new StaffServer())->getStaffById($staff_id);
        if (empty($staffInfo)) {
            return true;
        }

        if (in_array($formal, [
                HrStaffInfoModel::FORMAL_FRANCHISEE,
                HrStaffInfoModel::FORMAL_FRANCHISEE_OTHER,
            ]) || $is_sub_staff == 1) {
            return true;
        }
        return !self::compareMobileVersionByConfig('edit_pwd_app_version_config', $staff_id);
    }

    /**
     * 删除Redis key;
     * @param $cache_key
     * @return array
     */
    public function del_redis_key($cache_key)
    {
        if(empty($cache_key)) {
            return ['res' => false];
        }
        $cache     = $this->getDI()->get('redisLib');
        //密码超过5次，则当天不再登录
        $data = $cache->get($cache_key);
        $this->logger->write_log(" del_redis_key param : ". $cache_key . '=>' . $data, 'info');
        if(empty($data)) {
            return ['res' => false];
        }

        $cache->del($cache_key);

        return ['res' => true];
    }

    /**
     * Quick sort implementation
     * @param array $arr Array to be sorted
     * @param int $left Left boundary index
     * @param int $right Right boundary index
     * @return array Sorted array
     */
    protected function quickSort(array $arr, int $left = 0, int $right = null): array
    {
        if ($right === null) {
            $right = count($arr) - 1;
        }

        if ($left < $right) {
            $pivot = $this->partition($arr, $left, $right);
            $arr = $this->quickSort($arr, $left, $pivot - 1);
            $arr = $this->quickSort($arr, $pivot + 1, $right);
        }

        return $arr;
    }

    /**
     * Helper function for quickSort to partition the array
     * @param array $arr Array to be partitioned
     * @param int $left Left boundary index
     * @param int $right Right boundary index
     * @return int Final position of pivot
     */
    private function partition(array &$arr, int $left, int $right): int
    {
        $pivot = $arr[$right];
        $i = $left - 1;

        for ($j = $left; $j < $right; $j++) {
            if ($arr[$j] <= $pivot) {
                $i++;
                $temp = $arr[$i];
                $arr[$i] = $arr[$j];
                $arr[$j] = $temp;
            }
        }

        $temp = $arr[$i + 1];
        $arr[$i + 1] = $arr[$right];
        $arr[$right] = $temp;

        return $i + 1;
    }

}