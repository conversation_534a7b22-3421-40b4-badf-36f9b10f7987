<?php

/**
 * 登录用户业务仓库
 */

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\PasswordHash;

class LoginUserServer extends BaseServer
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取用户数据
     * @param string $username
     * @param array $ext
     * @return \Phalcon\Mvc\Model
     * @throws \Exception
     */
    public function detail($username, array $ext = array())
    {
        /*$user = $this->get_model('UsersModel')->detail($username, $ext);
        if (!$user->uid) {
        throw new \Exception('获取用户信息失败');
        }
        return $user;*/
    }

    public function verify_fle($data = '')
    {
        // 判断是否为空
        if( empty($data['account']) || empty($data['pwd']) || !is_numeric($data['account']) )
            return '';
        $hasher = new PasswordHash(10, false);
        // 查询数据库
        $pusher_sql = 'select id,name,email,encrypted_password,organization_id,department_id as position_category from staff_info where id = :staff_id ';
        $dataFind = $this->getDI()->get('db_fle')->query($pusher_sql,['staff_id'=>$data['account']])->fetch(\Phalcon\Db::FETCH_ASSOC);
        if( empty($dataFind) ) return '';

        $bool = $hasher->checkPassword($data['pwd'],$dataFind['encrypted_password']);
        if (!$bool)return '';
        unset($dataFind['encrypted_password']);
        //TODO 调用FLE发TOKEN
        $paramIn = [
            'account'      => $data['account'],
            'pwd'      => $data['pwd']
        ];
        $fle_rpc = (new ApiClient('fle','com.flashexpress.fle.svc.api.StaffAuthSvc','generateToken', $this->lang));
        $fle_rpc->setParams($data['account']);
        $fle_return = $fle_rpc->execute();
        if ($fle_return['result']){
            $dataFind['auth'] = $fle_return['result'];

        }else{
            $dataFind['auth'] = '';
        }
        $staff =  (new PersoninfoServer($this->lang,$this->timezone))->getPerson(['staff_id' => $data['account']]);
        $dataFind['info'] = $staff['data'] ?? null;

        return $dataFind;
    }

	/**
	 * 拼一个session_id
	 */
	public function structure_session_id($staff_id)
	{
        $time = time()+86400;
        $str = 'fbi' . env('runtime') . 'api';
        $auth = sha1(md5($time.$str.$staff_id));
        $session_id = $time . '_' . $auth . '_' . $staff_id;
        return $session_id;
	}

	/**
	 * 校验session_id
	 */
	public function verify_session_id($session_id)
	{
		//校验session_id
        $session_arr = explode('_',trim($session_id,'_'));
		$time = $session_arr[0];

		if($time < time()) //过期
			return false;

		$staff_id = $session_arr[2];
        $str = 'fbi' . env('runtime') . 'api';
        $auth = sha1(md5($time.$str.$staff_id));
		if($auth !== $session_arr[1]) //校验失败
			return false;

		return $staff_id;
	}



}
