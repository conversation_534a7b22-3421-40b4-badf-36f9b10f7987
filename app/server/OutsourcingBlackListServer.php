<?php

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrOutSourcingBlacklistModel;

class OutsourcingBlackListServer extends BaseServer
{
    /**
     * @param $identity
     * @param string $source
     * @param bool $switchValidation
     * @param string $lang
     * @return array
     * @throws ValidationException
     */
    public function check($identity, string $source = 'winhr', bool $switchValidation = true, string $lang = ''): array
    {
        $return = [
            'is_black' => false,
            'tip'      => '',
        ];
        if (empty($identity) || empty($source) || !isCountry(['TH', 'MY', 'PH'])) {
            return $return;
        }
        if (!empty($lang)) {
            $t = $this->getTranslation($lang);
        } else {
            $t = $this->getTranslation();
        }
        $blackface_os_reason = (new SettingEnvServer())->getSetValToArray('blackface_os_reason', ',');
        if (empty($blackface_os_reason)) {
            return $return;
        }
        $black_list = HrOutSourcingBlacklistModel::Find([
            'columns'    => 'reason_code,identity',
            'conditions' => 'identity=:identity: and status = :status: and reason_code in ({reason_codes:array})',
            'bind'       => [
                'identity'     => $identity,
                'status'       => HrOutSourcingBlacklistModel::STATUS_TAKE_EFFECT,
                'reason_codes' => $blackface_os_reason,
            ],
            'order'      => 'id desc',
        ])->toArray();
        if (empty($black_list)) {
            return $return;
        }
        $osBlackReason = $this->getOsBlackReasonData(!empty($lang) ? $lang : $this->lang);
        $_tip_arr      = [];
        foreach ($black_list as $item) {
            if (count($_tip_arr) == 10) {
                $_tip_arr[] = '...';
                break;
            }
            if (
                !empty($item['reason_code']) &&
                isset($osBlackReason[$item['reason_code']]) &&
                !in_array($osBlackReason[$item['reason_code']], $_tip_arr)
            ) {
                $_tip_arr[] = $osBlackReason[$item['reason_code']];
            }
        }
        if ($_tip_arr) {
            $return['is_black'] = true;
            $reason_code_str    = implode(',', $_tip_arr);
            $return['tip']      = $t->_('out_sourcing_blacklist_err_1',
                ['identity' => $identity, 'reason_code_str' => $reason_code_str]);
            if ($source == 'h5') {
                $return['tip'] = $t->_('out_sourcing_blacklist_err_3', ['identity' => $identity]);
            }
        }
        if ($switchValidation && $return['is_black']) {
            throw new ValidationException($return['tip']);
        }
        return $return;
    }

    /**
     * @param $identity
     * @param string $source
     * @param bool $switchValidation
     * @param string $lang
     * @return array
     * @throws ValidationException
     */
    public function check_v1($mobile, string $source = 'winhr', bool $switchValidation = true, string $lang = ''): array
    {
        $return = [
            'is_black' => false,
            'tip'      => '',
        ];
        if (empty($mobile) || empty($source) || !isCountry(['TH', 'MY', 'PH'])) {
            return $return;
        }
        if (!empty($lang)) {
            $t = $this->getTranslation($lang);
        } else {
            $t = $this->getTranslation();
        }
        $blackface_os_reason = (new SettingEnvServer())->getSetValToArray('blackface_os_reason', ',');
        if (empty($blackface_os_reason)) {
            return $return;
        }
        $black_list = HrOutSourcingBlacklistModel::Find([
            'columns'    => 'reason_code,identity',
            'conditions' => 'mobile=:mobile: and status = :status: and reason_code in ({reason_codes:array})',
            'bind'       => [
                'mobile'     => $mobile,
                'status'       => HrOutSourcingBlacklistModel::STATUS_TAKE_EFFECT,
                'reason_codes' => $blackface_os_reason,
            ],
            'order'      => 'id desc',
        ])->toArray();
        if (empty($black_list)) {
            return $return;
        }
        $osBlackReason = $this->getOsBlackReasonData(!empty($lang) ? $lang : $this->lang);
        $_tip_arr      = [];
        foreach ($black_list as $item) {
            if (count($_tip_arr) == 10) {
                $_tip_arr[] = '...';
                break;
            }
            if (
                !empty($item['reason_code']) &&
                isset($osBlackReason[$item['reason_code']]) &&
                !in_array($osBlackReason[$item['reason_code']], $_tip_arr)
            ) {
                $_tip_arr[] = $osBlackReason[$item['reason_code']];
            }
        }
        if ($_tip_arr) {
            $return['is_black'] = true;
            $return['tip']      = $t->_('qo_error_msg_003',
                ['mobile' => $mobile]);
            if ($source == 'h5') {
                $return['tip'] = $t->_('qo_error_msg_003', ['mobile' => $mobile]);
            }
        }
        if ($switchValidation && $return['is_black']) {
            throw new ValidationException($return['tip']);
        }
        return $return;
    }

    public function getOsBlackReasonData($lang)
    {
        $cache_key  = "get_os_black_reason_data" . $lang;
        $redis      = $this->getDI()->get('redisLib');
        $cache_data = $redis->get($cache_key);
        if (!empty($cache_data)) {
            return json_decode($cache_data, true);
        }
        $osBlackReason = (new SysServer())->getTotalDictionaryRegionByDictCode('os_black_reason',$lang);
        $osBlackReason = array_column($osBlackReason, 'label', 'value');
        $redis->set($cache_key, json_encode($osBlackReason, JSON_UNESCAPED_UNICODE), 300); //缓存内容
        return $osBlackReason;
    }

}
