<?php
namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\BusinesstripRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use Phalcon\Db;

class BusinesstripServer extends AuditBaseServer
{
    //Security & Safety
    const DEPARTMENT_SECURITY_SAFE = 93;
//    const DEPARTMENT_SECURITY_SAFE = 32;

    protected $trip;
    public $timezone;
    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
        $this->trip = new BusinesstripRepository($lang,$timezone);
        $this->public = new PublicRepository();
        $this->auditlist = new AuditListServer($lang, $timezone);
        $this->auditlistRep = new AuditlistRepository($lang, $timezone);
        $this->ov = new OvertimeRepository($timezone);
    }

    private static $paramsIn;

    /**
     * 创建订单
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addTrip($paramIn = [], $userinfo)
    {
        //格式化订单数据
        $serialNo = $this->getRandomId();

        $returnData['data'] = [];
        $tripData['reason_application'] = $paramIn['reason_application'] ?? "";//出差理由 & 外出事由
        $tripData['traffic_tools'] = $paramIn['traffic_tools'] ?? 0;//交通工具 1飞机 2火车 3汽车 4其他
        $tripData['other_traffic_name'] = $paramIn['other_traffic_name'] ?? '';//其他交通工具
        $tripData['oneway_or_roundtrip'] = $paramIn['oneway_or_roundtrip'] ?? 1;//单程1往返2
        $tripData['departure_city'] = $paramIn['departure_city'] ?? '';//出发城市
        $tripData['destination_city'] = $paramIn['destination_city'] ?? '';//目的城市 & 外出地点
        $tripData['start_time'] = $paramIn['start_time'] ?? '';//开始时间
        $tripData['end_time'] = $paramIn['end_time'] ?? '';//结束时间
        $tripData['days_num'] = (strtotime($tripData['end_time']) - strtotime($tripData['start_time'])) / 3600 / 24 + 1;//出差天数
        $tripData['remark'] = $paramIn['remark'] ?? '';//备注
        $tripData['status'] = $paramIn['status'] ?? 1;//订单状态
        $tripData['apply_user'] = $userinfo['id'] ?? 1;//申请人
        $tripData['serial_no']  = !empty($serialNo) ?'BT'.$serialNo : NULL;
        $tripData['business_trip_type'] = $paramIn['business_trip_type'] ?? BusinessTripModel::BTY_NORMAL;//1普通出差2黄牌项目出差
        $tripData['reason_application_type'] = $paramIn['reason_application_type'] ?? 0;//1办理黄牌2考驾照
        $tripData['car_no'] = $paramIn['car_no'] ?? '';
        $tripImgData = $paramIn['image_path'] ?? [];
        $tripData['destination_country'] = 0; //目的地国家
        $tripData['destination_country_name'] = '';  //目的地国家名称

        //只有境外出差的时候才保存 目的地国家和国家名称
        if ($tripData['business_trip_type'] == BusinessTripModel::BTY_FOREIGN) {
            $tripData['destination_country']      = $paramIn['destination_country'];   //目的地国家
            $tripData['destination_country_name'] = $tripData['destination_country'] == HrStaffInfoModel::WORKING_COUNTRY_OTHER ? $paramIn['destination_country_name'] : '';        //目的地国家名称 选择其他的时候 才保存 国家名称
        }

        if($tripData['business_trip_type'] == BusinessTripModel::BTY_YELLOW) {
            $tripData['reason_application'] = $this->getTranslation()->_('reason_application_type_'.$paramIn['reason_application_type']);
        }

        $db = $this->getDI()->get("db");
        try {
            $db->begin();
            $db->insertAsDict('business_trip', $tripData);
            $lastInsertId = $db->lastInsertId();
            if ($tripImgData && $lastInsertId) {
                $tripImgInster = [];
                foreach ($tripImgData as $k => $v) {
                    $tripImgInster[$k]['business_trip_id'] = $lastInsertId;
                    $tripImgInster[$k]['img_path'] = $v;
                }
                $insetOrderDetail = (new BaseRepository($this->lang, null))->batch_insert('business_trip_img', $tripImgInster);
                if (!$insetOrderDetail) {
                    throw new \Exception('插入 business_trip_img 失败');
                }
            }
            if (!$lastInsertId) {
                throw new \Exception('插入 business_trip 失败');
            }
            $audit_type =  $this->getAuditTypeByBTY((int)$tripData['business_trip_type']);

            $flag = (new ApprovalServer($this->lang, $this->timezone))->create($lastInsertId, $audit_type, $userinfo['id']);
            if (!$flag) {
                throw new \Exception('创建审批流失败');
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log('addtrip error ' . $e->getMessage() . ' ' . json_encode($tripData, JSON_UNESCAPED_UNICODE));
            return $this->checkReturn(-3, $this->getTranslation()->_('2109'));
        }

        return $this->checkReturn(['data' => $lastInsertId]);
    }

    /**
     *
     * @param $id
     *
     */
    public function businessTripInfo($id)
    {
       $db = $this->getDI()->get("db");
       return $db->fetchOne("select * from business_trip where id = " . $id, Db::FETCH_ASSOC);
    }

    /**
     *
     * @param $staffData
     *
     */
    public function businessTripWorkRole($staffData)
    {
        if ($staffData && $staffData['department_id'] == self::DEPARTMENT_SECURITY_SAFE) {
            return 'bt_approve_amanda';
        }

        return 'bt_approve';
    }

    /**
     * 审批同意的出差 撤销时验证
     * @param $paramIn
     * @return bool
     * @throws ValidationException
     */
    public function beforeCancelCheck($paramIn): bool
    {
        //获取记录信息
        $result = $this->trip->getTripR($paramIn);
        if (empty($result)) {
            throw new ValidationException("can not find info");
        }
        if($result['status'] != enums::APPROVAL_STATUS_APPROVAL){
            return true;
        }
        //调用OA
        $cli = new ApiClient('oa_rpc', '', 'check_bt_is_reimbursed', $this->lang);
        $cli->setParams(['serial_no' => $result['serial_no']]);
        $rpc_result = $cli->execute();
        if (isset($rpc_result['error'])) {
            throw new ValidationException($rpc_result['error']);
        }
        if ($rpc_result['result'] === false) {
            return true;
        }
        throw new ValidationException($this->getTranslation()->_('bt_cancel_check_1'));
    }



    /**
     * 修改记录状态记录日志
     * @Access  public
     * @Param   request
     * @Return  array
     * @throws ValidationException
     */
    public function updateTripStatus($paramIn, $userinfo)
    {
        //获取记录信息
        $result = $this->trip->getTripR($paramIn);
        if(empty($result))
            throw new ValidationException("can not find info");

        $audit_type = $this->getAuditTypeByBTY((int)$result['business_trip_type']);
        //当前状态如果已经审批，申请人不可撤销    '2206' => '当前申请不能撤销',
//        if( $result['status'] != 1 && $paramIn['status'] == 4 ){
//            return $this->checkReturn(-3, $this->getTranslation()->_('2206'));
//        }
        //申请人撤销后,审批人不能审批
        if( $result['status'] == 4 ){
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        self::$paramsIn = array_merge($paramIn, ['userinfo' => $userinfo]);
        $res = false;
        $appr_server = new ApprovalServer($this->lang,$this->timezone);
        if ($paramIn['status'] == enums::APPROVAL_STATUS_APPROVAL) {
            $res = $appr_server->approval($paramIn['id'], $audit_type, $userinfo['id']);
        }elseif ($paramIn['status'] == enums::APPROVAL_STATUS_REJECTED) {
            $res = $appr_server->reject($paramIn['id'], $audit_type, $paramIn['reason'], $userinfo['id']);
        }elseif ($paramIn['status'] == enums::APPROVAL_STATUS_CANCEL || $paramIn['status'] == enums::APPROVAL_STATUS_PENDING) {
            $type = $this->determineCancelType($paramIn['id'],$audit_type);
            if ($type == 1) {
                //审批中撤销
                $res = (new ApprovalServer($this->lang, $this->timezone))->cancel($paramIn['id'], $audit_type, '', $userinfo['id']);
            }else{
                //判断是否 已经审核通过
                if($result['status'] == 2 && $result['business_trip_type'] != BusinessTripModel::BTY_YELLOW){
                    //审批通过的出差 撤销时候验证是否已经报销了
                    $this->beforeCancelCheck($paramIn);

                    $db = $this->getDI()->get("db");
                    $db->begin();

                    $bus_info = BusinessTripModel::findFirst($result['id']);
                    $bus_info->status = 1;
                    $bus_info->created_at = DateHelper::localToUtc();//创建时间 里面和外面要更新为 撤销审批申请时间
                    $res = $bus_info->update();

                    if(empty($res)){
                        $db->rollBack();
                        throw new ValidationException("sys error");
                    }
                    //操作 审批流 apply 和 approval 变更为撤销审批状态
                    $res = $appr_server->cancel_create($paramIn['audit_id'],$audit_type,$paramIn['reason'],$result['apply_user'],'id_'.$paramIn['audit_id']);
                    $res ? $db->commit() : $db->rollBack();
                }else
                    throw new ValidationException("record status error");
            }

        }
        return (bool) $res;
    }

    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result    = $this->trip->getTripR(['id' => $auditId]);//通过id获取详情
        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['apply_user']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }

        $audit_type =  $this->getAuditTypeByBTY((int)$result['business_trip_type']);

        $resultImg = $this->trip->getTripImgR(['id' => $auditId]);//附件
        $resultImg = array_column($resultImg, 'img_path');
        //多语言切换
        $tran_type   = $this->trip->getTransportationType();
        $single_type = $this->trip->getSingleroundtripType();
        if ($result['traffic_tools'] == 4) {//如果是其他类型交通工具显示录入的具体名称
            $traffic_tools = ($tran_type[$result['traffic_tools']] ?? '') . "[" . $result['other_traffic_name'] . "]";
        } else {
            $traffic_tools = $tran_type[$result['traffic_tools']] ?? '';
        }

        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('apply_parson'), 'value' => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? '')];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('apply_department'),'value' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? '')];
        if (in_array($result['business_trip_type'],[BusinessTripModel::BTY_DOMESTIC,BusinessTripModel::BTY_FOREIGN])) {
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('business_trip_type'), 'value' => $result['business_trip_type'] == BusinessTripModel::BTY_DOMESTIC ? $this->getTranslation()->_('business_trip_domestic') : $this->getTranslation()->_('business_trip_foreign')];
        }
        if($result['business_trip_type'] == BusinessTripModel::BTY_YELLOW) {
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('reason_application'), 'value' => $this->getTranslation()->_('reason_application_type_'.$result['reason_application_type'])];
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('plate_number'), 'value' => $result['car_no']];
        } else {
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('reason_application'), 'value' => $result['reason_application']];
        }
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('traffic_tools'), 'value' => $traffic_tools];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('oneway_or_roundtrip'), 'value' => $single_type[$result['oneway_or_roundtrip']]];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('departure_city'), 'value' => $result['departure_city']];
        //境外出差并且选择了国家
        if($result['business_trip_type'] == BusinessTripModel::BTY_FOREIGN && !empty($result['destination_country'])) {
            $countryRegion = $this->getEnumsList();
            $countryNameList = array_column($countryRegion['destination_country'], 'key', 'value');
 
            //境外出差
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('destination_country'), 'value' => $countryNameList[$result['destination_country']] ?? ''];
           //出差国家是其他- 显示国家名称
            if($result['destination_country'] == HrStaffInfoModel::WORKING_COUNTRY_OTHER){
                $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('destination_country_name'), 'value' => $result['destination_country_name']];
            }
        }
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('destination_city'), 'value' => $result['destination_city']];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('start_time'), 'value' => $result['start_time']];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('end_time'), 'value' => $result['end_time']];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('days_num'), 'value' => $result['days_num']];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('remark'), 'value' => $result['remark']];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('photo'), 'value' => $resultImg];

        if ($result['business_trip_type'] == BusinessTripModel::BTY_GO_OUT) {
            $returnData['data']['detail'] = [
                ['key' => $this->getTranslation()->_('apply_parson'), 'value' => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? '')],
                ['key' => $this->getTranslation()->_('apply_department'),'value' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? '')],
                ['key' => $this->getTranslation()->_('start_time'), 'value' => $result['start_time']],
                ['key' => $this->getTranslation()->_('end_time'), 'value' => $result['end_time']],
                ['key' => $this->getTranslation()->_('days_num'), 'value' => $result['days_num']],
                ['key' => $this->getTranslation()->_('go_out_reason_application'), 'value' => $result['reason_application']],
                ['key' => $this->getTranslation()->_('go_out_location'), 'value' => $result['destination_city']],
                ['key' => $this->getTranslation()->_('photo'), 'value' => $resultImg],
            ];
        }

        //字段解释 ：申请理由，交通工具，单程1往返2 ,出发城市,到达城市,开始时间,结束时间,出差天数,备注
        $data                         = [
            'title'       => $this->auditlistRep->getAudityType($audit_type),
            'origin_id'   => $result['id'],
            'id'          => $result['id'],
            'staff_id'    => $result['apply_user'],
            'type'        => $audit_type,
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['status'],
            'status_text' => $this->auditlistRep->getAuditStatus('10' . $result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
            'is_pop'      => $result['status'] == 2,
        ];

        $returnData['data']['head']   = $data;
        return $returnData;
    }

    /**
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        $result    = $this->trip->getTripR(['id' => $auditId]);//通过id获取详情
        if ($result['business_trip_type'] != BusinessTripModel::BTY_YELLOW){
            $rule = new AuditOptionRule(true, true, false, false, false, false);
        }else{
            $rule = new AuditOptionRule(true, false, false, false, false, false);
        }
        return $rule;
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $result = $this->trip->getTripR(['id' => $auditId]);//通过id获取详情
        $audit_type = $this->getAuditTypeByBTY((int)$result['business_trip_type']);
        return (new AuditListServer($this->lang, $this->timezone))->generateSummary($auditId, $audit_type);
    }

    /**
     * 通申请类型寻找审批类型
     * @param $business_trip_type
     * @return int
     */
    public function getAuditTypeByBTY($business_trip_type): int
    {
        switch ($business_trip_type){
            case BusinessTripModel::BTY_YELLOW:
                $audit_type = AuditListEnums::APPROVAL_TYPE_YCBT;
                break;
            case BusinessTripModel::BTY_GO_OUT:
                $audit_type = AuditListEnums::APPROVAL_TYPE_GO;
                break;
            default:
                $audit_type = AuditListEnums::APPROVAL_TYPE_BT;
                break;
        }
         return $audit_type;
    }
    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {


        if ($isFinal) {
            if(!empty($extend['is_cancel']) && $extend['is_cancel'] > 0){
                //撤销审批流 同意 最终状态改为撤销
                if($state == enums::APPROVAL_STATUS_APPROVAL)
                    $state = enums::APPROVAL_STATUS_CANCEL;
                //撤销审批流 驳回 最终状态改回 审批通过
                if($state == enums::APPROVAL_STATUS_REJECTED)
                    $state = enums::APPROVAL_STATUS_APPROVAL;
            }

            $business_trip['uid'] = self::$paramsIn && isset(self::$paramsIn['userinfo']) ?  self::$paramsIn['userinfo']['id'] : enums::SYSTEM_STAFF_ID;
            $business_trip['id'] = $auditId;
            $business_trip['status'] = $state;
            $business_trip['reason'] = isset(self::$paramsIn['reason']) ? self::$paramsIn['reason'] : "";
            $res = $this->trip->cancel([], $business_trip);
            $this->getDI()->get('logger')->write_log('business_setproperty' . (string) $res, 'info');
        }

        return true;
    }

    /**
     * 样例
     * from_node_id | to_node_id | valuate_formula | valuate_codp
     * -------------+------------+-----------------+-------------
     *      4       |     5      |    $p1 == 4     | getSubmitterDepartment
     *
     * 表示当提交人的部门为4时，审批节点4的下一个节点是5
     * 需要在 getWorkflowParams 中返回申请人所在的部门字段
     *
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $businessDetail = BusinessTripModel::findFirst([
            'conditions' => 'id = :id: ',
            'bind' => ['id' => $auditId]
        ])->toArray();
        $staffId = $businessDetail['apply_user'];
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: ',
            'bind' => ['staff_id' => $staffId]
        ]);
        $staffInfo = $staffInfo ? $staffInfo->toArray() : [];

        $departmentId = 0;
        if ($staffInfo) {
            if (isset($staffInfo['node_department_id']) && $staffInfo['node_department_id']) {
                $departmentId = $staffInfo['node_department_id'];
            } else {
                $departmentId = $staffInfo['sys_department_id'];
            }
        }
        
        return [
            'department_id' => $departmentId,
            'w_f_condition_days_num'=> $businessDetail['days_num'] ?? 0, //获取出差和外出天数
        ];

    }

    /**
     * @param $staff_id
     * @param $date
     */
    public function check_info_by_date($staff_id,$date){
        $re = new BusinesstripRepository($this->lang,$this->timezone);
        return $re->trip_info($staff_id,$date);

    }

    /**
     * 获取当前日期出差类型和目的地国家
     * @param $staff_id
     * @param $date
     * @return array
     */
    public function getExitTypeByDate($staff_id,$date): array
    {
        $instance = BusinessTripModel::findFirst([
            'conditions' => ' apply_user= :apply_user: and status = :status:   and start_time <= :date: and end_time >= :date: ',
            'bind' => ['apply_user' => $staff_id, 'status'=> enums::APPROVAL_STATUS_APPROVAL,'date' => $date],
            'columns' => 'business_trip_type,destination_country'
        ]);
        return $instance ? $instance->toArray() : [];
    }
    /**
     * 当日是否为外出（出差的一种类型）
     * @param $staff_id
     * @param $date
     * @return mixed
     */
    public function check_go_out_by_date($staff_id,$date){
      $id = BusinessTripModel::find([
            'conditions' => ' apply_user= :apply_user: and status = :status:   and start_time <= :date: and end_time >= :date: and business_trip_type= :type: ',
            'bind' => ['apply_user' => $staff_id, 'status'=> enums::APPROVAL_STATUS_APPROVAL,'date' => $date, 'type' => BusinessTripModel::BTY_GO_OUT ],
            'columns' => 'id'
        ])->toArray();
        return !empty($id);
    }
    /**
     * 判断所选日期内是否有出差申请记录
     * @param $start
     * @param $end
     * @param $user_id
     * @return bool
     */
    public function checkTime($start,$end,$user_id): bool
    {
       $intersection  =  BusinessTripModel::find([
            'conditions' => 'status in (1,2) and apply_user= :apply_user:
            and (
            (start_time<= :s: and end_time>= :s:) or (start_time<= :e: and end_time>= :e:) or (start_time<= :s: and end_time>= :e:) or (start_time>= :s: and end_time<= :e:)
            )',
            'bind' => ['s' => $start, 'e' => $end , 'apply_user' => $user_id],
            'columns' => 'id'
        ])->toArray();
       return !empty($intersection);
    }

    public function getTypeList(): array
    {
        return [
          ['key' =>  $this->getTranslation()->_('business_trip_domestic'),'value' => BusinessTripModel::BTY_DOMESTIC],
          ['key' =>  $this->getTranslation()->_('business_trip_foreign'),'value' => BusinessTripModel::BTY_FOREIGN]
        ];
    }


    /**
     * @description: 获取静态枚举
     * @param null
     * @return:
     * @author: L.J
     * @time: 2023/1/10 15:32
     */
    public function getEnumsList()
    {
        //获取国家方式统一从hcm-api获取
        //目的地国家
        //$destination_country = [];
        //foreach (BusinessTripModel::DESTINATION_COUNTRY as $k => $v) {
        //    $destination_country[] = [
        //        'key'   => $this->getTranslation()->_('nationality_'.$k),
        //        'value' => $k,
        //    ];
        //}

        $ac = new ApiClient('hcm_rpc', '', 'getDictionaryByDictCode', $this->lang);
        $ac->setParams(['dict_code' => 'working_country']);
        $result = $ac->execute();

        $this->getDI()->get("logger")->write_log('getRegionByDictCode result: ' . json_encode($result, JSON_UNESCAPED_UNICODE), 'info');
        if ($result['result'] && $result['result']['code'] != 1) {
            throw new \Exception($result['result']['msg'],100);
        }

        $response = [];
        foreach ($result['result']['data'] as $v) {
            $response[] = [
                'key'    => $v['label'],
                'value'  => $v['value'],
            ];
        }
        return [
            //目的地国家
            'destination_country' => $response,
        ];
    }


    public function formatProvinceCity($cityCode, $delimiter = ' - ')
    {
        if(empty($cityCode)){
            return ['key' => $this->getTranslation()->_('departure_city'), 'value' => ''];
        }
        $sysServer = new SysServer($this->lang, $this->timezone);
        $departure_city_info = $sysServer->getProvinceCityInfo($cityCode);
        $dcName = "{$departure_city_info['p_name']} > {$departure_city_info['c_name']}";
        if($this->lang != 'th'){//如果不是泰文 用英文 然后英文 没有 还用泰文
            $dcName = empty($departure_city_info['p_en_name']) ? $departure_city_info['p_name'] : $departure_city_info['p_en_name'];
            $dcName .= $delimiter . (empty($departure_city_info['c_en_name']) ? $departure_city_info['c_name'] : $departure_city_info['c_en_name']);
        }
        return $dcName;
    }


}
