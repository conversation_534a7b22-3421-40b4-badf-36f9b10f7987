<?php

namespace FlashExpress\bi\App\Server;


class HrPenaltyServer extends AuditBaseServer
{
    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
    }

    /**
     * @description 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
    }

    /**
     * @description 获取审批参数
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
    }
}