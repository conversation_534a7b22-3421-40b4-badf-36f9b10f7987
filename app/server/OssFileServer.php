<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/7/8
 * Time: 下午7:38
 */

namespace FlashExpress\bi\App\Server;

use OSS\OssClient;
use OSS\Core\OssException;
use FlashExpress\bi\App\Server\AttendanceServer;

class OssFileServer extends BaseServer{


    //oss 文件夹 每个模块起一个
    private $space_name = array(
        1 => 'workAttendanceSource',
    );
    /**
     *
     *
     * fle.app.oss.access_key_id=LTAI7MMCu3h4oaGD
        fle.app.oss.access_key_secret=******************************
        fle.app.oss.bucket.internal=fle-staging-asset-internal
     * @var string
     */
//    protected $oss_id = 'LTAI4GDstV4E9awWTDgnz7RV';
//    protected $oss_key = '******************************';
//    protected $end_point = 'https://oss-ap-southeast-1.aliyuncs.com';//服务器节点地址

    //java 原oss
    protected $oss_id = 'LTAI7MMCu3h4oaGD';
    protected $oss_key = '******************************';
    protected $end_point = 'https://oss-ap-southeast-1.aliyuncs.com';//服务器节点地址


    /**
     * @param $dir_type 根据模块 创建文件夹 stace name
     * @param $file_name 客户端固定上传文件名  //原图 source.jpg  对比图 duty.jpg
     * @return array|bool
     * @throws \OSS\Http\RequestCore_Exception
     */
    public function oss_put($dir_type,$file_name){
        $accessKeyId = $this->oss_id;
        $accessKeySecret = $this->oss_key;
        $endpoint = $this->end_point;
        // 存储空间名称 每个项目建议单独建一个 找运维
        $bucket =  $this->getDI()['config']['application']['oss_bucket'];

        if(empty($dir_type) || empty($this->space_name[$dir_type]))
            return false;

        try {
            $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
            $folder = $this->space_name[$dir_type];
            //非线上环境 统一 test 文件夹
            if(in_array(RUNTIME,['dev','test','tra']))
                $folder = 'test';
            $ext = explode('.',$file_name);
            $tmp = time();
            $file_name = $tmp . '_' . md5($file_name.$tmp) . '.' . end($ext);
            $file_name = $folder.'/'.$file_name;
//            $path = '/usr/share/nginx/lao-backyard/public/xx.png';
//            $res = $ossClient->uploadFile($bucket,$file_name,$path);
            $res = (array)$ossClient->formatUrl($bucket, $file_name);
//            $url = $ossClient->signUrl($bucket, $file_name);//string(211)"https://fle-staging-asset-internal.oss-ap-southeast.aliyuncs.com/test/1602502774_3a05fa5aceeb0fdad30213674f7c6b8c.jpg?OSSAccessKeyId=LTAI7MMCu3h4oaGD&Expires=1602502834&Signature=nkdaq%2Fmfec0EqGuupaT9Ej8MjHs%3D"
//            $query = parse_url($url);
//            $queryParts = explode('&', $query['query']);
//            $params = array();
//            foreach ($queryParts as $param) {
//                $item = explode('=', $param);
//                $params[$item[0]] = $item[1];
//            }
//            $params['sid'] = $this->oss_id;
//            $params['bucket'] = $bucket;
//            $params['point'] = $this->end_point;
//            $params['file_name'] = $file_name;

            if($res['status'] == 200){//构建成功 拼接 id bucket
                $res['data']['sid'] = $this->oss_id;
                $res['data']['bucket'] = $bucket;
                $res['data']['point'] = $this->end_point;
                $res['data']['file_name'] = $file_name;

            }
            return $res;
        } catch (OssException $e) {

        }

    }

}