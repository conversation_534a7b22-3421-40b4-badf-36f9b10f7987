<?php

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;


class StopServer extends SystemExternalApprovalServer
{
    /**
     * @description: 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态      const APPROVAL_STATUS_PENDING   = 1;    //待审批 const APPROVAL_STATUS_APPROVAL  =
     *                      2;    //审批同意const APPROVAL_STATUS_REJECTED  = 3;    //审批驳回const APPROVAL_STATUS_CANCEL    =
     *                      4;    //审批撤销const APPROVAL_STATUS_TIMEOUT   = 5;    //审批超时
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        try {
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('setProperty  没有找到数据  '.$auditId);
            }
            $server = new SystemExternalApprovalServer($this->lang, $this->timeZone);

            if ($isFinal) {
                //最终审批需要做的操作
                $SystemExternalApprovalModel->state      = $state;
                $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());
                $SystemExternalApprovalModel->save();
            }
            //如果是申请人节点 不进行调用 fms  因为这时候 还没有审批编号
            if (isset($extend['is_start_node']) && $extend['is_start_node']) {
                return true;
            }

            if ($state == enums::APPROVAL_STATUS_REJECTED) {
                $rejectReason = $extend['remark'];
            }
            //查询一下 操作人名称
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: ',
                'bind'       => [
                    'staff_info_id' => $extend['staff_id'] ?? '',
                ],
                'columns'    => 'staff_info_id,name',
            ]);

            $forwardParamIn = [
                'param_operation_type'     => $server::param_operation_type_2,
                'param_operation_staff_id' => $extend['staff_id'],
                'serial_no'                => $SystemExternalApprovalModel->serial_no,
                'biz_type'                 => (int)$SystemExternalApprovalModel->biz_type,
                'state'                    => (int)$state,
                'eventual'                 => $isFinal === true ? 1 : 0,
                'reason'                   => $rejectReason ?? '',
                'operator_name'            => $staff_info->name ?? '', //操作人名称
            ];
            AuditCallbackServer::createData($SystemExternalApprovalModel->biz_type, $forwardParamIn);

        } catch (\Exception $e) {
            $this->logger->write_log('DriverBlackListServer  setProperty msg '.$e->getMessage().' file '.$e->getFile().' line '.$e->getLine().$e->getTraceAsString().'auditId => '.$auditId,
                'notice');
            return false;
        }

        return true;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false, false, false, false, false, false);
    }

    /**
     * 详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     * @throws \Exception
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $system_external = new SystemExternalApprovalServer($this->lang, $this->timezone);
        //获取详情
        $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
        if (!$SystemExternalApprovalModel) {
            throw new \Exception('getDetail  没有找到数据  '.$auditId);
        }
        $result = $SystemExternalApprovalModel->toArray();
        //获取详情
        $forwardParamIn = [
            'biz_type'                 => $result['biz_type'],
            'param_operation_type'     => $system_external::param_operation_type_4,
            'param_operation_staff_id' => $user,
            'serial_no'                => $result['serial_no'],
        ];
        $svc_result     = $system_external->forwardParamIn($forwardParamIn);
        if (!isset($svc_result['code']) || $svc_result['code'] != 1) {
            throw new \Exception('getDetail  获取详情失败  '.$auditId);
        }
        $auditList = new AuditlistRepository($this->lang, $this->timezone);
        $_detail   = [];
        if ($svc_result['data']) {
            foreach ($svc_result['data']['list'] as $k => $v) {
                $_detail[$v["key"]] = $v["value"];
            }
        }

        $returnData['data']['detail'] = $this->format($_detail ?? []);
        $add_hour                     = $this->getDI()['config']['application']['add_hour'];
        $data                         = [
            'title'      => $auditList->getAudityType($result['biz_type']),
            'id'         => $result['id'],
            'staff_id'   => $result['submitter_id'],
            'type'       => $result['biz_type'],
            'created_at' => date('Y-m-d H:i:s', (strtotime($result['created_at']) + $add_hour * 3600)),
            'updated_at' => date('Y-m-d H:i:s', (strtotime($result['updated_at']) + $add_hour * 3600)),
            'status'     => $result['state'],
            'serial_no'  => $result['serial_no'] ?? '',
            'biz_type'   => $result['biz_type'],
            'is_update'  => 0,  // 1 可以编辑 0 不能编辑
        ];
        $returnData['data']['head'] = $data;

        return $returnData;
    }
}
