<?php
/**
 * Author: Bruce
 * Date  : 2024-07-06 19:01
 * Description:
 */

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Models\backyard\HrStaffContractBusinessBaseModel;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\HrStaffContractBusinessApplyModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\HrStaffContractBusinessApplyRepository;
use FlashExpress\bi\App\Repository\HrStaffContractBusinessBaseRepository;
use FlashExpress\bi\App\Repository\HrStaffContractRepository;
use FlashExpress\bi\App\Repository\JobTitleRepository;
use FlashExpress\bi\App\Repository\StaffPickupDeliveryDataRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;
use Exception;

class RenewContractBusinessServer extends AuditBaseServer
{
    public $timezone;

    //公告展示入口
    const NOTICE_IS_DISPLAY_NO = '0';//不展示
    const NOTICE_IS_DISPLAY_YES = '1';//展示

    //TH 是否缴纳押金
    const NOTICE_IS_DEPOSIT_NO = '0';//未缴纳
    const NOTICE_IS_DEPOSIT_YES = '1';//已缴纳

    //MY 公告入口 进入的 提交续约页面
    const RENEW_CONTRACT_SUBMIT_PAGE = 'ic-contract-expries-apply-renewal-msg';// MY入口；TH 选择了不续约的入口页面
    const RENEW_CONTRACT_SUBMIT_PAGE_SELECT_PENDING = 'personal-renewal-reminder-select';//TH 使用，员工未选择 是否续约 入口页面
    //MY 公告入口 是否展示的 缓存key
    const RENEW_CONTRACT_ENTRY_REDIS_KEY = 'renew_contract_business_entry_';

    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
    }

    /**
     * 校验数据
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function validateData($params)
    {
        $businessData = HrStaffContractBusinessApplyRepository::getOneById($params['id']);

        if (empty($businessData) || $businessData['staff_info_id'] != $params['staff_id']) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//未找到数据
        }

        $staffRepository = new StaffRepository();
        $staffData       = $staffRepository->getStaffInfoOne($params['staff_id'], ['staff_info_id', 'name']);

        if (empty($staffData)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//未找到数据
        }

        return [$businessData, $staffData];
    }

    /**
     * 获取消息详情数据
     * @param $params
     * @return mixed
     * @throws BusinessException
     */
    public function getContractInfo($params)
    {
        [$businessData, $staffData] = $this->validateData($params);

        //如果 业务状态 已处理，则直接将消息 置为已读
        if(!empty($businessData['business_status'])) {
            (new BackyardServer($this->lang,$this->timezone))->has_read_operation($businessData['msg_id'],false);

        }

        $result['staff_info_id'] = $staffData['staff_info_id'] ?? '';
        $result['name']          = $staffData['name'] ?? '';
        $result['date']          = $businessData['contract_end_date'];
        $result['status']        = intval($businessData['business_status']);//是否同意续期：1同意，2不同意

        return $result;
    }

    /**
     * 同意 不同意 续约---处理消息
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function submit($params)
    {
        [$businessData, $staffData] = $this->validateData($params);

        if($businessData['business_status'] != HrStaffContractBusinessApplyModel::BUSINESS_STATUS_PENDING) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//已处理的数据，就不能再处理了
        }

        $data['id']              = $businessData['id'];
        $data['contract_id']     = $businessData['contract_id'];
        $data['business_type']   = $businessData['business_type'];
        $data['business_status'] = $params['status'];

        $update['business_status'] = $data['business_status'];
        $update['business_operation_time'] = date('Y-m-d H:i:s');
        if($data['business_status'] == HrStaffContractBusinessApplyModel::BUSINESS_STATUS_REJECT) {
            $contract = HrStaffContractRepository::getOne(['id' => $data['contract_id']]);
            if(empty($contract)) {
                throw new BusinessException($this->getTranslation()->_('data_error'));//已处理的数据，就不能再处理了
            }
            if($contract['contract_status'] != HrStaffContractModel::CONTRACT_STATUS_TO_BE_RENEWED) {
                $this->getDI()->get('db')->updateAsDict(
                    'hr_staff_contract_business_apply',
                    $update,
                    'id = '.$businessData['id']
                );

                (new BackyardServer($this->lang,$this->timezone))->has_read_operation($businessData['msg_id'],false);
                $this->logger->write_log(['renew-contract-business exception-submit-2' => $params], 'info');

                return true;
            }
        }

        //如果 非第一条消息 同意续约 要走 创建审批流流程
        if($data['business_type'] == HrStaffContractBusinessApplyModel::BUSINESS_TYPE_NOT_FIRST && $data['business_status'] == HrStaffContractBusinessApplyModel::BUSINESS_STATUS_AGREE) {
            $data['create_audit'] = true;
        }

        $rmq = new RocketMQ('renew-contract-business');
        $rmq->setType(RocketMQ::TAG_NAME_RENEW_CONTRACT);
        $rid = $rmq->sendMsgByTag($data);
        $this->logger->write_log('backyard to hcm rmq-renew-contract-business exception: ' . $rid, 'info');
        if(!$rid) {//mq 发送失败
            throw new BusinessException($this->getTranslation()->_('data_error'));
        }

        $this->getDI()->get('db')->updateAsDict(
            'hr_staff_contract_business_apply',
            $update,
            'id = '.$businessData['id']
        );

        (new BackyardServer($this->lang,$this->timezone))->has_read_operation($businessData['msg_id'],false);

        return true;
    }


    /**
     * 非第一条消息，续约，要走审批流
     * 创建审批流
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function addAudit($params)
    {
        if(empty($params) || empty($params['id'])) {
            throw new BusinessException('params id is not empty:' . $this->getTranslation()->_('data_error'));//未找到数据
        }
        $businessData = HrStaffContractBusinessApplyRepository::getOneById($params['id']);

        if (empty($businessData)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//未找到数据
        }

        $where['contract_id'] = $businessData['contract_id'];
        $where['audit_status'] = [enums::$audit_status['panding']];//待审批
        $pendingInfo = HrStaffContractBusinessApplyRepository::getOneByContractId($where);

        $staffRepository = new StaffRepository();
        $staffData       = $staffRepository->getStaffInfoOne($businessData['staff_info_id']);

        if (empty($staffData)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//未找到数据
        }

        //该合同 已经存在 待审批的审批流无需再创建
        if (!empty($pendingInfo)) {
            $this->logger->write_log("个人代理续约劳动合同 当前合同id已存在待审批，无需审批 id:" . $params['id'] . ', contract_id:' . $businessData['contract_id'], 'notice');
            return [];
        }

        $storeInfo = SysStoreRepository::getSysStoreInfo($staffData['sys_store_id'], ['manage_region', 'manage_piece']);

        $staffCurrentData['sys_store_id']      = $staffData['sys_store_id'] ?? '';
        $staffCurrentData['job_title']         = $staffData['job_title'] ?? 0;
        $staffCurrentData['hire_type']         = $staffData['hire_type'] ?? 0;
        $staffCurrentData['hire_date']         = !empty($staffData['hire_date']) ? date('Y-m-d', strtotime($staffData['hire_date'])) : '';
        $staffCurrentData['contract_end_date'] = $businessData['contract_end_date'] ?? '';
        $staffCurrentData['region_id']         = $storeInfo['manage_region'] ?? 0;
        $staffCurrentData['piece_id']          = $storeInfo['manage_piece'] ?? 0;
        $contract_end_date = $businessData['contract_end_date'] ?? '';
        $staffCurrentData['leave_date']        = !empty($contract_end_date) ? date('Y-m-d', strtotime("{$contract_end_date} +1 days")) : '';

        //固化当前 员工信息 ；
        $audit_json = json_encode($staffCurrentData, JSON_UNESCAPED_UNICODE);

        $serialNo   = $this->getRandomId();
        $updateData = [
            'serial_no'        => (!empty($serialNo) ? 'EC' . $serialNo : null),
            'status'           => enums::$audit_status['panding'],//待审批
            'audit_json'       => $audit_json,//待审批
            'audit_created_at' => date('Y-m-d H:i:s'),//审批创建时间
        ];

        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $db->updateAsDict('hr_staff_contract_business_apply', $updateData,
                [
                    'conditions' => 'id = ?',
                    'bind'       => [$businessData['id']],
                ]
            );

            //创建
            $server    = new ApprovalServer($this->lang, $this->timeZone);
            $requestId = $server->create($businessData['id'], AuditListEnums::APPROVAL_TYPE_IC_RENEWAL, $businessData['staff_info_id'], null, []);
            if (!$requestId) {
                throw new Exception($this->getTranslation()->_('4101'));
            }

            $db->commit();
        } catch (Exception $exception) {
            $db->rollBack();
            throw new Exception($this->getTranslation()->_('4101'));
        }

        return [];
    }

    /**
     * 审批
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function update($paramIn)
    {
        $staffId       = $this->processingDefault($paramIn, 'staff_id', 2);
        $id            = $this->processingDefault($paramIn, 'audit_id', 2);
        $status        = $this->processingDefault($paramIn, 'status', 2);
        $reject_reason = $this->processingDefault($paramIn, 'reject_reason');

        $hrStaffContractBusinessApplyRepository = new HrStaffContractBusinessApplyRepository($this->timezone);
        $contractInfo       = $hrStaffContractBusinessApplyRepository->getOneById($id, 'status');

        if (empty($contractInfo)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }

        if ($contractInfo['status'] == enums::APPROVAL_STATUS_APPROVAL) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        if ($contractInfo['status'] == enums::APPROVAL_STATUS_REJECTED) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        $server = new ApprovalServer($this->lang, $this->timezone);
        if ($status == enums::$audit_status['approved']) {
            // 同意
            $server->approval($id, AuditListEnums::APPROVAL_TYPE_IC_RENEWAL, $staffId);
        } else {
            // 驳回
            $server->reject($id, AuditListEnums::APPROVAL_TYPE_IC_RENEWAL, $reject_reason, $staffId);
        }

        return $this->checkReturn(['data' => ['id' => $id]]);
    }

    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $auditListRepository = new AuditlistRepository($this->lang, $this->timezone);

        $businessData = HrStaffContractBusinessApplyRepository::getOneById($auditId);

        $staffInfo = !empty($businessData['audit_json']) ? json_decode($businessData['audit_json'], true) : [];

        $staffRepository = new StaffRepository();
        $staffData       = $staffRepository->getStaffInfoOne($businessData['staff_info_id'], ['staff_info_id', 'name', 'job_title']);

        $jobTitle        = JobTitleRepository::getJobTitleInfo($staffInfo['job_title']);
        $jobTitleInfo    = empty($jobTitle) ? [] : $jobTitle->toArray();

        $staffRegionPieceName = [];
        $sysStoreServer = new SysStoreServer();
        $region = !empty($staffInfo['region_id']) ? $sysStoreServer->getStoreRegionName($staffInfo['region_id']) : [];
        if(!empty($region)) {
            $staffRegionPieceName[] = $region['manage_region'];
        }
        $piece = !empty($staffInfo['piece_id']) ? $sysStoreServer->getStorePieceName($staffInfo['piece_id']) : [];
        if(!empty($piece)) {
            $staffRegionPieceName[] = $piece['manage_piece'];
        }

        $detailLists['apply_parson']                      = sprintf('%s ( %s )', $staffData['name'], $staffData['staff_info_id']);
        $detailLists['agent_store']                       = $staffInfo['sys_store_id'] == -1 ? enums::HEAD_OFFICE : SysStoreRepository::getStoreName($staffInfo['sys_store_id']);
        $detailLists['staff_region_piece_name']           = !empty($staffRegionPieceName) ? implode('-', $staffRegionPieceName) : '';
        $detailLists['agent_type']                        = $jobTitleInfo['job_name'] ?? '';
        $detailLists['staff_hire_type']                   = !empty($staffInfo['hire_type']) ? $this->getTranslation()->_('hire_type_' . $staffInfo['hire_type']) : '';
        $detailLists['staff_hire_date']                   = !empty($staffInfo['hire_date']) ? $staffInfo['hire_date'] : '';
        $detailLists['contract_expiry_date']              = !empty($staffInfo['contract_end_date']) ? $staffInfo['contract_end_date'] : '';
        $detailLists['waiting_leave_date']                = !empty($staffInfo['leave_date']) ? date('Y-m-d', strtotime($staffInfo['leave_date'])) : '';

        $returnData['data']['detail'] = $this->format($detailLists);
        $add_hour = $this->config->application->add_hour;
        $data = [
            'title'       => $auditListRepository->getAudityType(enums::$audit_type['EC']),
            'id'          => $businessData['id'],
            'staff_id'    => $businessData['staff_info_id'],
            'type'        => enums::$audit_type['EC'],
            'created_at'  => date('Y-m-d H:i:s',strtotime("{$businessData['created_at']}") + $add_hour * 3600),
            'updated_at'  => $businessData['updated_at'],
            'status'      => $businessData['status'],
            'status_text' => $auditListRepository->getAuditStatus('10' . $businessData['status']),
            'serial_no'   => $businessData['serial_no'] ?? '',
        ];
        $returnData['data']['head'] = $data;

        $auditCreated = date('Y-m-d', strtotime($businessData['audit_created_at']));
        //自审批创建之日前一天起，近七天的 揽派件

        $endDate = date('Y-m-d', strtotime("{$auditCreated} - 1 days"));

        $startDate = date('Y-m-d', strtotime("{$endDate} - 6 days"));

        $returnData['data']['pickup_delivery_list'] = StaffPickupDeliveryDataRepository::getStaffData($businessData['staff_info_id'], $startDate, $endDate);

        return $returnData;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        //申请人不可撤销
        return new AuditOptionRule(false,
            false,
            false,
            false,
            false,
            false);
    }


    public function genSummary(int $auditId, $user)
    {
        $businessData = HrStaffContractBusinessApplyRepository::getOneById($auditId);

        $staffRepository = new StaffRepository();
        $staffData       = $staffRepository->getStaffInfoOne($businessData['staff_info_id'], ['staff_info_id', 'name', 'job_title', 'sys_store_id']);

        $jobTitle        = JobTitleRepository::getJobTitleInfo($staffData['job_title']);
        $jobTitleInfo    = empty($jobTitle) ? [] :$jobTitle->toArray();

        return [
            [
                'key'   => 'corr_store',//申请人所属网点
                'value' => $staffData['sys_store_id'] == Enums::HEAD_OFFICE_ID ? Enums::HEAD_OFFICE : SysStoreRepository::getStoreName($staffData['sys_store_id']),
            ],
            [
                'key'   => 'agent_type', //申请人职位-类型
                'value' => $jobTitleInfo['job_name'] ?? '',
            ]
        ];
    }

    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $hrStaffContractBusinessApplyRepository = new HrStaffContractBusinessApplyRepository($this->timezone);
            $contractInfo       = $hrStaffContractBusinessApplyRepository->getOneById($auditId);

            if (empty($contractInfo)) {
                throw new ValidationException($this->getTranslation()->_('1015') . '[From RenewContractBusiness 审批终态设置]');
            }

            if ($contractInfo['status'] == enums::APPROVAL_STATUS_APPROVAL) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From RenewContractBusiness 审批终态设置]');
            }

            if ($contractInfo['status'] == enums::APPROVAL_STATUS_REJECTED) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From RenewContractBusiness 审批终态设置]');
            }

            if($state == enums::APPROVAL_STATUS_APPROVAL) {
                $data['id']              = $contractInfo['id'];
                $data['contract_id']     = $contractInfo['contract_id'];
                $data['business_type']   = $contractInfo['business_type'];
                $data['business_status'] = $contractInfo['business_status'];
                //修改 在职状态， 释放hold
                AuditCallbackServer::createData(AuditListEnums::APPROVAL_TYPE_IC_RENEWAL, $data);
            }

            $updateData['status']           = $state;
            $updateData['audit_time']       = date('Y-m-d H:i:s');

            $db                   = $this->getDI()->get('db');
            $db->updateAsDict('hr_staff_contract_business_apply', $updateData,
                [
                    'conditions' => 'id = ?',
                    'bind'       => [$auditId],
                ]
            );
        }

    }

    /**
     * 修改 在职状态， 释放hold
     * @param $data
     * @return bool
     * @throws BusinessException
     */
    public function delayCallBack($data)
    {
        $rmq = new RocketMQ('renew-contract-business');
        $rmq->setType(RocketMQ::TAG_NAME_RENEW_CONTRACT);
        $rid = $rmq->sendMsgByTag($data);
        $this->logger->write_log('backyard to hcm rmq-renew-contract-business exception: ' . $rid, 'info');
        if(!$rid) {//mq 发送失败
            $this->logger->write_log(['RenewContractBusinessServer-setProperty-mq-fail' => $data]);
            throw new BusinessException($this->getTranslation()->_('data_error'));
        }

        return true;
    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        // TODO: Implement getWorkflowParams() method.
        return [];
    }

    /**
     * PH每天 00点 05分 执行
     * 处理超时 待审批
     * @param $timeAt
     * @return bool
     */
    public function getPendingAudit($timeAt)
    {
        $data['status'] = enums::APPROVAL_STATUS_PENDING;
        $pendingData = HrStaffContractBusinessApplyRepository::getPendingData($data);

        $approvalServer = new ApprovalServer($this->lang, $this->timezone);
        $number = 0;
        foreach ($pendingData as $oneData) {
            if(empty($oneData['audit_json'])) {
                continue;
            }
            $jsonData = json_decode($oneData['audit_json'], true);
            if(empty($jsonData['leave_date'])) {
                continue;
            }
            //非待离职日期 不关闭
            if(strtotime($jsonData['leave_date']) != strtotime($timeAt)) {
                continue;
            }
            $number++;
            //待离职日期 还是 待审批 置为 审批超时
            $result = $approvalServer->timeOut($oneData['id'], AuditListEnums::APPROVAL_TYPE_IC_RENEWAL);
            if (!$result){
                $this->logger->write_log("RenewContractBusinessTask 超时关闭续约审批任务失败 id:".$oneData['id']);
                continue;
            }
            $this->logger->write_log(['RenewContractBusinessTask-timeOut-success ' => $oneData['id']], 'info');
        }

        $this->logger->write_log('RenewContractBusinessTask-timeOut-end', 'info');
        echo 'success';
        return true;
    }

    /**
     * MY 个人代理 合同到期 打卡页是否展示入口---加缓存
     * @param $params
     * @param float|int $timeOut
     * @return mixed
     */
    public function isDisplayEntry($params, $timeOut = 5 * 60)
    {
        //切主账号
        if ($master_staff_id = StaffRepository::getMasterStaffIdBySubStaff($params['staff_id'])) {
            $params['staff_id'] = $master_staff_id;
            //主账号的token
            $params['master_staff_flag'] = $this->setSessionPrefix(self::$sub_to_master_session_prefix)->generateSessionId($master_staff_id);
        }

        $cache = $this->getDI()->get('redisLib');
        $key   = self::RENEW_CONTRACT_ENTRY_REDIS_KEY . $params['staff_id'];
        $data = $cache->get($key);
        if(!empty($data)){
            return json_decode($data, true);
        }
        //todo 删除
        //$cache     = $this->getDI()->get('redisLib');
        //$cache->del($key);
        $data = $this->noticeData($params);
        $cache->set($key, json_encode($data, JSON_UNESCAPED_UNICODE), $timeOut);
        return $data;

    }

    /**
     * MY 个人代理 合同到期 打卡页是否展示入口
     * @param $params
     * @return mixed
     */
    public function noticeData($params)
    {
        $data['is_display'] = self::NOTICE_IS_DISPLAY_NO;
        $data['url'] = '';
        $data['contract_end_date'] = '';
        $data['contract_id'] = '';
        $data['fail_message'] = '';
        $data['staff_info_id'] = '';
        $data['name'] = '';
        $staffRepository = new StaffRepository();
        $staffData       = $staffRepository->getStaffInfoOne($params['staff_id'], ['staff_info_id', 'name', 'hire_type']);

        if (empty($staffData)) {
            return $data;
        }

        $data['staff_info_id'] = $staffData['staff_info_id'];
        $data['name'] = $staffData['name'];

        //个人代理，兼职个人代理
        if(!in_array($staffData['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            $data['fail_message'] = '雇佣类型，非个人代理，兼职个人代理';
            return $data;
        }

        //查询待续约的合同--最新的合同
        $contractWhere['staff_id'] = $params['staff_id'];
        $contractWhere['contract_status'] = HrStaffContractModel::CONTRACT_STATUS_TO_BE_RENEWED;
        $contractInfo = HrStaffContractRepository::getOne($contractWhere);

        if(empty($contractInfo)) {
            $data['fail_message'] = '未找到合同信息';
            return $data;
        }

        $data['contract_id'] = $contractInfo['id'];
        $data['contract_end_date'] = $contractInfo['contract_end_date'];

        $baseWhere['contract_id'] = $contractInfo['id'];
        $baseInfo = HrStaffContractBusinessBaseRepository::getOneByContractId($baseWhere);

        if(empty($baseInfo)) {
            $data['fail_message'] = 'base 数据错误';
            return $data;
        }

        //业务状态：待处理
        if(in_array($baseInfo['business_status'], [HrStaffContractBusinessBaseModel::BUSINESS_STATUS_PENDING])) {
            $data['fail_message'] = 'base 业务状态：待处理';
            return $data;
        }

        $submitPageUrl = env('h5_endpoint') . self::RENEW_CONTRACT_SUBMIT_PAGE;

        //如果 没有传则 用主账号获取
        if(empty($params['master_staff_flag'])) {
            $params['master_staff_flag'] = $this->setSessionPrefix(self::$sub_to_master_session_prefix)->generateSessionId($params['staff_id']);
        }

        if ($params['master_staff_flag']) {
            $questionMarkPosition = strpos($submitPageUrl, '?');
            // 查找是否有问号
            if ($questionMarkPosition !== false) {
                $submitPageUrl = $submitPageUrl. '&sub_to_master_token='. $params['master_staff_flag'];
            } else {
                $submitPageUrl = $submitPageUrl. '?sub_to_master_token='. $params['master_staff_flag'];
            }
        }

        //业务状态：同意， 业务类型一定是 审批流
        if($baseInfo['business_status'] == HrStaffContractBusinessBaseModel::BUSINESS_STATUS_AGREE && $baseInfo['business_type'] == HrStaffContractBusinessBaseModel::BUSINESS_TYPE_WORKFLOW && strtotime(date('Y-m-d')) <= strtotime($contractInfo['contract_end_date'])) {
            $applyWhere['contract_id'] = $contractInfo['id'];
            $applyWhere['business_type'] = HrStaffContractBusinessBaseModel::BUSINESS_TYPE_WORKFLOW;
            $applyWhere['audit_status'] = [enums::$audit_status['panding'], enums::$audit_status['approved']];//待审批，已同意。
            $applyData = HrStaffContractBusinessApplyRepository::getOneByContractId($applyWhere);
            //审批状态是 待审批及已同意，不展示入口，否则展示
            if(!empty($applyData)) {
                $data['fail_message'] = 'base 审批状态是 待审批或已同意';
                return $data;
            }
            $data['is_display'] = self::NOTICE_IS_DISPLAY_YES;
            $data['url'] = $submitPageUrl;
            return $data;
        }

        //最新状态是不续约，则展示入口
        if($baseInfo['business_status'] == HrStaffContractBusinessBaseModel::BUSINESS_STATUS_REJECT && strtotime(date('Y-m-d')) <= strtotime($contractInfo['contract_end_date'])) {
            $data['is_display'] = self::NOTICE_IS_DISPLAY_YES;
            $data['url'] = $submitPageUrl;
            return $data;
        }
        $data['fail_message'] = '不展示入口信息';
        return $data;
    }

    /**
     * 详情再实时查一遍，是否允许提交, 清除缓存，将最新信息存入缓存
     * @param $params
     * @param $timeOut
     * @return mixed
     */
    public function noticeDetail($params, $timeOut = 5 * 60)
    {
        $key   = self::RENEW_CONTRACT_ENTRY_REDIS_KEY . $params['staff_id'];
        $cache     = $this->getDI()->get('redisLib');
        $cache->del($key);

        $data = $this->noticeData($params);

        $cache->set($key, json_encode($data, JSON_UNESCAPED_UNICODE), $timeOut);
        return $data;
    }

    /**
     * 打卡页直接提交
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function noticeSubmit($params)
    {
        $this->delCache($params['staff_id']);

        $noticeData = $this->noticeData($params);
        if($noticeData['is_display'] == self::NOTICE_IS_DISPLAY_NO) {
            $this->logger->write_log(['renew-contract-business-noticeSubmit-error' => $noticeData], 'info');
            throw new BusinessException($this->getTranslation()->_('renew_contract_submit_data_error'));//当前合同状态，无法提交
        }

        $addAuditParams['contract_id'] = $noticeData['contract_id'];
        $addAuditParams['staff_info_id'] = $noticeData['staff_info_id'];
        return $this->addAudit($addAuditParams);
    }

    /**
     * 清除入口缓存
     * @param $staff_id
     * @return bool
     */
    public function delCache($staff_id)
    {
        $key   = self::RENEW_CONTRACT_ENTRY_REDIS_KEY . $staff_id;
        $cache     = $this->getDI()->get('redisLib');
        $cache->del($key);
        return true;
    }

}