<?php


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\ApprovalFinderEnums;
use FlashExpress\bi\App\Models\backyard\ApprovalFinderMapModel;
use FlashExpress\bi\App\Models\backyard\HcmStaffManageRegionModel;
use FlashExpress\bi\App\Models\backyard\HrOrganizationDepartmentStoreRelationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\HubOtAuditRelateModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditApprovalRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;

/**
 * Class ApprovalFinderServer
 * @package FlashExpress\bi\App\Server
 * @description 查找审批人server，这个server只找人！找审批人的相关信息，请移步StaffSever; 返回结果尽量以Array的形式返回
 */
class ApprovalFinderServer extends BaseServer
{
    private const FIND_COLUMNS = 'staff_info_id';

    /**
     * @var null
     */
    private static $instance = null;

    //获取实例
    public static function getInstance(): ApprovalFinderServer
    {
        if (!isset(self::$instance) || self::$instance instanceof self) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @description 根据网点ID, 查找分拨大区经理
     * @param int $audit_type 审批类型
     * @param int $find_type 查询类型 1=根据网点查找 2=根据部门查找，查找类型可后续扩展
     * @param mixed $find_value 查找值
     * @return array
     */
    public function findAreaManagerFromHashMap($audit_type, $find_type, $find_value): array
    {
        if (empty($audit_type) || empty($find_type) || empty($find_value)) {
            return [];
        }

        //查找类型
        switch ($find_type) {
            case ApprovalFinderEnums::FINDER_BY_STORE:
            default:
                $approvalList = $this->findAreaManagerByStoreId($audit_type, $find_value);
        }

        return $approvalList;
    }

    /**
     * @description 根据网点ID, 查找分拨大区经理
     * 具体逻辑为
     *  网点对应的部门（3级部门），查找上级部门的部门负责人
     *
     *
     * @param $sys_store_id
     * @return array
     */
    public function findHubAreaManagerByStoreId($sys_store_id): array
    {
        if (empty($sys_store_id)) {
            return [];
        }
        $relevanceDepartmentInfo = $this->findRelevanceDepartmentByStoreId($sys_store_id);
        if (empty($relevanceDepartmentInfo)) {
            return [];
        }

        //获取Hub网点关联的部门的上级部门的负责人
        if (empty($relevanceDepartmentInfo->ancestry_v3)) {
            return [];
        }
        $hubAreaManagerList = (new DepartmentRepository())->getHubAreaManger($relevanceDepartmentInfo->ancestry_v3, SysDepartmentModel::DEPARTMENT_LEVEL_2);
        return $hubAreaManagerList ? : [];
    }

    /**
     * @description 根据网点查找分拨标准化&行政
     * @param $sys_store_id
     * @return array
     */
    public function findHubSubDepartmentStaff($sys_store_id): array
    {
        $relevanceDepartmentInfo = $this->findRelevanceDepartmentByStoreId($sys_store_id);
        if (empty($relevanceDepartmentInfo)) {
            return [];
        }

        //获取Hub网点关联的部门下的 Hub Standardization(标准化)、Hub Admin(行政)子部门中的全部成员
        $departmentIds           = (new SysDepartmentModel())->getSpecifiedDeptAndSubDept($relevanceDepartmentInfo->id);
        $hubStandardizationStaff = $this->findDepartmentFromSubByName($departmentIds, 'Hub Standardization');
        $hubAdmin                = $this->findDepartmentFromSubByName($departmentIds, 'Hub Admin');
        $departmentList          = array_values(array_unique(array_merge($hubStandardizationStaff, $hubAdmin)));
        return $this->findSpecDepartmentStaff($departmentList);
    }

    /**
     * @description 查找指定部门下的全部员工
     * @param $department_id
     * @return array
     */
    public function findSpecDepartmentStaff($department_id): array
    {
        if (empty($department_id)) {
            return [];
        }

        //在职、在编、非子账户
        $condition = 'state = 1 and formal = 1 and is_sub_staff = 0 and ';
        if (is_array($department_id)) {
            $condition .= 'node_department_id in({department_id:array})';
        } else {
            $condition .= 'node_department_id = :department_id:';
        }
        $staffInfo = HrStaffInfoModel::find([
            'conditions' => $condition,
            'bind' => [
                'department_id' => $department_id
            ],
            'columns' => self::FIND_COLUMNS
        ])->toArray();
        return array_column($staffInfo, 'staff_info_id');
    }

    /**
     * @description 查找指定网点下的全部员工
     * @param $store_id
     * @return array
     */
    public function findSpecStoreStaff($store_id): array
    {
        if (empty($store_id)) {
            return [];
        }

        //在职、在编、非子账户
        $condition = 'state = 1 and formal = 1 and is_sub_staff = 0 and ';
        if (is_array($store_id)) {
            $condition .= 'sys_store_id in({store_id:array})';
        } else {
            $condition .= 'sys_store_id = :store_id:';
        }
        $staffInfo = HrStaffInfoModel::find([
            'conditions' => $condition,
            'bind' => [
                'store_id' => $store_id
            ],
            'columns' => self::FIND_COLUMNS
        ])->toArray();
        return array_column($staffInfo, 'staff_info_id');
    }

    /**
     * @description 查找hub网点关联的部门负责人
     * @param $sys_store_id
     * @return string
     */
    public function findHubStoreManager($sys_store_id): string
    {
        $relevanceDepartmentInfo = $this->findRelevanceDepartmentByStoreId($sys_store_id);
        if (empty($relevanceDepartmentInfo)) {
            return '';
        }
        return $relevanceDepartmentInfo->manager_id ?? '';
    }

    /**
     * @description 根据网点ID查找指定审批人
     * @param int $audit_type
     * @param $find_value
     * @return array
     */
    private function findAreaManagerByStoreId(int $audit_type, $find_value): array
    {
        $approvalList = ApprovalFinderMapModel::find([
            'conditions' => 'audit_type = :audit_type: and store_id = :store_id: and deleted = 0',
            'bind'       => [
                'audit_type' => $audit_type,
                'store_id'   => $find_value,
            ],
            'columns'    => self::FIND_COLUMNS,
        ])->toArray();
        return array_column($approvalList, 'staff_info_id');
    }

    /**
     * @description 根据关联网点查找部门ID
     * @param $store_id
     * @return mixed
     */
    private function findRelevanceDepartmentByStoreId($store_id)
    {
        return SysDepartmentModel::findFirst([
            'conditions' => 'relevance_store_id = :store_id:',
            'bind' => [
                'store_id' => $store_id
            ]
        ]);
    }

    /**
     * @description 根据部门名称在子部门中查找员工
     * @param $sub_department_ids
     * @param $department_name
     * @return array
     */
    private function findDepartmentFromSubByName($sub_department_ids, $department_name): array
    {
        $departmentInfo = SysDepartmentModel::find([
            'conditions' => 'name like :name: and id in ({department_id:array})',
            'bind' => [
                'name'          => '%' . $department_name . '%',
                'department_id' => $sub_department_ids,
            ],
            'columns' => 'id',
        ])->toArray();
        return array_column($departmentInfo, 'id');
    }

    /**
     * @description 获取片区负责人
     * @param $store_id
     * @return string
     */
    public function getPieceManagerByStoreId($store_id)
    {
        $manageInfo = $this->getManageInfoByStoreId($store_id);
        if (empty($manageInfo)) {
            return '';
        }
        $pieceInfo = SysManagePieceModel::findFirst($manageInfo->piece_id);
        return $pieceInfo ?  $pieceInfo->manager_id : '';
    }

    /**
     * @description 获取片区负责人
     * @param $store_id
     * @return string
     */
    public function getRegionManagerByStoreId($store_id)
    {
        $manageInfo = $this->getManageInfoByStoreId($store_id);
        if (empty($manageInfo)) {
            return '';
        }
        $regionInfo = SysManageRegionModel::findFirst($manageInfo->region_id);
        return $regionInfo ?  $regionInfo->manager_id : '';
    }

    /**
     * 根据网点获取管辖信息
     * @param $store_id
     * @return \Phalcon\Mvc\Model
     */
    public function getManageInfoByStoreId($store_id)
    {
        $bind = [
            'deleted'     => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
            'state'       => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
            'level_state' => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            'store_id'    => $store_id,
        ];
        return HrOrganizationDepartmentStoreRelationModel::findFirst([
            'conditions' => 'is_deleted = :deleted: and state = :state: and level_state = :level_state: and store_id = :store_id:',
            'bind'       => $bind,
        ]);
    }

    /**
     * 获取网点负责人
     * @param $sys_store_id
     * @param array $extend
     * @return string
     */
    public function findHeadOfStore($sys_store_id, array $extend): string
    {
        if (empty($sys_store_id)) {
            return "";
        }

        $storeInfo = SysStoreModel::findFirst([
            'conditions' => "id = :store_id: and state = 1",
            'bind' => [
                'store_id' => $sys_store_id
            ],
            'columns' => 'manager_id'
        ]);
        return $storeInfo->manager_id ?? "";
    }

    /**
     * @description 查找指定工号是否属于指定部门
     */
    public function isSpecifiedStaffBelongDepartment($staff_info_id, $department_id): bool
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and node_department_id = :department_id:',
            'bind' => [
                'staff_info_id' => $staff_info_id,
                'department_id' => $department_id,
            ],
            'columns' => self::FIND_COLUMNS
        ]);
        return !empty($staffInfo);
    }

    /**
     * 获取HrBP
     * @param $department_id
     * @param $store_id
     * @return array
     */
    public function findHrBp($department_id, $store_id): array
    {
        if (empty($department_id) || empty($store_id)) {
            return [];
        }

        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind'       => ['store_id' => $store_id],
        ]);
        $regionId  = !empty($storeInfo->manage_region) ? $storeInfo->manage_region : 0;
        $pieceId   = !empty($storeInfo->manage_piece) ? $storeInfo->manage_piece : 0;
        do {
            //网点不能管辖总部网点
            if (!empty($store_id) && $store_id != '-1') {
                //[1]先获取网点对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns("position.staff_info_id");
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, "position.staff_info_id = staff.staff_info_id", 'staff');
                $builder->leftjoin(HcmStaffManageRegionModel::class, "position.staff_info_id = region.staff_info_id",
                    'region');
                //在职 在编的
                $builder->Where('staff.state = 1 and staff.formal = 1 and position.position_category = :role_id: and 
                region.deleted = 0 and region.category = :category:',
                    ['role_id' => HrStaffInfoPositionModel::HRBP, 'category' => HcmStaffManageRegionModel::CATEGORY_5]);
                $builder->inWhere('region.store_id', [HcmStaffManageRegionModel::STORE_ALL, $store_id]);
                $hrBpList = $builder->getQuery()->execute()->toArray();
                if (!empty($hrBpList)) {
                    break;
                }
            }

            if (!empty($pieceId)) {
                //[2]获取片区对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns("position.staff_info_id");
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, "position.staff_info_id = staff.staff_info_id", 'staff');
                $builder->leftjoin(HcmStaffManageRegionModel::class, "position.staff_info_id = region.staff_info_id",
                    'region');
                //在职 在编的
                $builder->Where('staff.state = 1 and staff.formal = 1 and position.position_category = :role_id: and 
                region.deleted = 0 and region.category = :category: and region.value = :value:',
                    [
                        'role_id'  => HrStaffInfoPositionModel::HRBP,
                        'category' => HcmStaffManageRegionModel::CATEGORY_4,
                        'value'    => $pieceId,
                    ]);
                $hrBpList = $builder->getQuery()->execute()->toArray();
                if (!empty($hrBpList)) {
                    break;
                }
            }

            if (!empty($regionId)) {
                //[3]获取大区对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns("position.staff_info_id");
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, "position.staff_info_id = staff.staff_info_id", 'staff');
                $builder->leftjoin(HcmStaffManageRegionModel::class, "position.staff_info_id = region.staff_info_id",
                    'region');
                //在职 在编的
                $builder->Where('staff.state = 1 and staff.formal = 1 and position.position_category = :role_id: and 
                region.deleted = 0 and region.category = :category: and region.value = :value:',
                    [
                        'role_id'  => HrStaffInfoPositionModel::HRBP,
                        'category' => HcmStaffManageRegionModel::CATEGORY_3,
                        'value'    => $regionId,
                    ]);
                $hrBpList = $builder->getQuery()->execute()->toArray();
                if (!empty($hrBpList)) {
                    break;
                }
            }

            //[4]获取部门对应的HRBP
            $builder = $this->modelsManager->createBuilder();
            $builder->columns("position.staff_info_id");
            $builder->from(['position' => HrStaffInfoPositionModel::class]);
            $builder->leftjoin(HrStaffInfoModel::class, "position.staff_info_id = staff.staff_info_id", 'staff');
            $builder->leftjoin(HcmStaffManageRegionModel::class, "position.staff_info_id = region.staff_info_id",
                'region');
            //在职 在编的
            $builder->Where('staff.state = 1 and staff.formal = 1 and position.position_category = :role_id: and 
                region.deleted = 0 and region.category = :category: and region.value = :value:',
                [
                    'role_id'  => HrStaffInfoPositionModel::HRBP,
                    'category' => HcmStaffManageRegionModel::CATEGORY_2,
                    'value'    => $department_id,
                ]);
            $hrBpList = $builder->getQuery()->execute()->toArray();
        } while (0);
        return array_column($hrBpList, 'staff_info_id');
    }

    /**
     * hub 加班审批  根据网点找抄送人
     * @param $storeId
     * @return string
     */
    public function getHubOtCCStaffByStoreId($storeId)
    {
        $staffInfoRelate = HubOtAuditRelateModel::findFirst([
            'conditions' => 'store_id = :store_id: and is_deleted = :is_deleted:',
            'bind' => [
                'store_id'   => $storeId,
                'is_deleted' => HubOtAuditRelateModel::IS_DELETED_NO,
            ],
            'columns' => self::FIND_COLUMNS
        ]);
        if(empty($staffInfoRelate)) {
            return '';
        }

        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => [
                'staff_info_id' => $staffInfoRelate->staff_info_id
            ],
            'columns' => self::FIND_COLUMNS
        ]);


        return !empty($staffInfo) ? $staffInfo->staff_info_id : '';
    }

    /**
     * 指定N级上级
     * @param $user
     * @param $level
     * @return string
     */
    public function findSuperior($user, $level): string
    {
        if (empty($user)) {
            return '';
        }
        $staffInfo = HrStaffItemsModel::findFirst([
            'conditions' => "staff_info_id = ?1 and item = 'MANGER' ",
            'bind' => [
                1 => $user
            ]
        ]);
        if ($level == 1) {
            return $staffInfo && $staffInfo->value ? $staffInfo->value : '';
        }

        if ($staffInfo) {
            return $this->findSuperior($staffInfo->value, $level - 1);
        }
        return '';
    }


    /**
     * form员工本人
     * @param $staff_info_id
     * @return mixed
     */
    public function getStaffSelf($staff_info_id)
    {
        return $staff_info_id;
    }

    public function getApprovalList($params)
    {
        if(empty($params['biz_type']) || empty($params['biz_value'])) {
            return [];
        }

        $list = [];
        $auditApprovalList = AuditApprovalRepository::getAuditApprovalList($params, ['approval_id'], true);
        if(!empty($auditApprovalList)) {
            $list = array_merge($list, array_column($auditApprovalList, 'approval_id'));
        }

        $auditCCList = AuditApprovalRepository::getAuditCCList($params, ['cc_staff_id'], true);

        if(!empty($auditCCList)) {
            $list = array_merge($list, array_column($auditCCList, 'cc_staff_id'));
        }

        return array_values(array_unique($list));
    }
}