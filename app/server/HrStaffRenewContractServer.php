<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrOrganizationDepartmentStoreRelationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffRenewContractApplyModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;

/**
 * 员工续签合同
 */
class HrStaffRenewContractServer extends AuditBaseServer
{
    public $timezone;
    public $lang;

    public function __construct($lang = 'en', $timezone)
    {
        $this->timezone = $timezone;
        $this->lang     = $lang;

        parent::__construct($lang, $timezone);
    }

    /**
     * 添加续签合同申请
     * @param $params
     * @return array
     */
    public function addRenewContractApply($params): array
    {
        $renew_staff_info_id     = $params['renew_staff_info_id'];    //续签人id
        $contract_id             = $params['contract_id'];            //合同id
        $contract_name           = $params['contract_name'];          //合同名称
        $contract_start_date     = $params['contract_start_date'];    //合同开始时间
        $contract_end_date       = $params['contract_end_date'];      //合同结束时间
        $contract_ld_no          = $params['contract_ld_no'];         //合同编号

        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $model                          = new HrStaffRenewContractApplyModel();
            $model->renew_staff_info_id     = $renew_staff_info_id;
            $model->serial_no               = 'RC' . $this->getRandomId();
            $model->contract_id             = $contract_id;
            $model->contract_name           = $contract_name;
            $model->contract_start_date     = $contract_start_date;
            $model->contract_end_date       = $contract_end_date;
            $model->contract_ld_no          = $contract_ld_no;
            $model->status                  = enums::$audit_status['panding'];

            $result  = $model->create();
            $auditId = $result ? $model->id : 0;

            //创建审批
            $server    = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($auditId, AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT, $renew_staff_info_id);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $this->logger->write_log([
                'function' => 'addRenewContractApply',
                'message'  => $e->getMessage(),
                'line'     => $e->getLine(),
                'file'     => $e->getFile(),
                'params'   => $params,
            ]);
            return $this->checkReturn(-3, $e->getMessage());
        }
        return $this->checkReturn(["audit_id" => $auditId]);
    }

    /**
     * 更新申请
     * @param $paramIn
     * @return array
     * @throws Exception
     */
    public function updateRenewContract($paramIn): array
    {
        $staff_info_id     = $this->processingDefault($paramIn, 'staff_id', 2);
        $status            = $this->processingDefault($paramIn, 'status', 2);
        $reason            = $this->processingDefault($paramIn, 'reject_reason');
        $renew_contract_id = $this->processingDefault($paramIn, 'audit_id', 2);
        $reason            = addcslashes(stripslashes($reason), "'");

        //详情
        $detail = HrStaffRenewContractApplyModel::findFirst([
            'conditions' => "id = :id:",
            'bind'       => ['id' => $renew_contract_id],
        ]);
        if (empty($detail)) {
            throw new Exception($this->getTranslation()->_('4008'), enums::$ERROR_CODE['1000']);
        }
        $detail = $detail->toArray();
        if ($detail['status'] != enums::$audit_status['panding'] && $status == enums::$audit_status['revoked']) {
            throw new Exception($this->getTranslation()->_('please try again'), enums::$ERROR_CODE['1000']);
        }

        //同意或者驳回等分开处理
        if ($status == enums::$audit_status['approved']) {
            //同意
            $server = new ApprovalServer($this->lang, $this->timezone);
            $server->approval($renew_contract_id, AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT, $staff_info_id);
        }

        if ($status == enums::$audit_status['dismissed']) {
            //驳回
            $server = new ApprovalServer($this->lang, $this->timezone);
            $server->reject($renew_contract_id, AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT, $reason, $staff_info_id);
        }

        if ($status == enums::$audit_status['revoked']) {
            //撤销
            $server = new ApprovalServer($this->lang, $this->timezone);
            $server->cancel($renew_contract_id, AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT, $reason, $staff_info_id);
        }

        return $this->checkReturn([]);
    }

    /**
     * 审批详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array|mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $detail = HrStaffRenewContractApplyModel::findFirst([
            'conditions' => "id = :id:",
            'bind'       => ['id' => $auditId],
            'order'      => 'id desc'
        ]);

        if (empty($detail)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        $result = $detail->toArray();

        //申请人信息
        $staffInfo = (new StaffServer())->getStaffInfoById($result['renew_staff_info_id']);

        $detailLists = [
            'staff_info_id'     => $result['renew_staff_info_id'], //工号
            'staff_name'        => $staffInfo['staff_name'],       //姓名
            'position_name'     => $staffInfo['job_name'],         //职位名称
            'department'        => $staffInfo['department_name'],  //所属部门
            'staff_store'       => $staffInfo['store_name'],       //所属网点
            'contract_end_date' => $result['contract_end_date'],   //合同到期时间
        ];

        $returnData['data']['detail'] = $this->format($detailLists);

        $auditRepo = new AuditlistRepository($this->lang, $this->timezone);
        $data      = [
            'title'       => $auditRepo->getAudityType(AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT),
            'id'          => $result['id'],
            'staff_id'    => $result['renew_staff_info_id'],
            'type'        => AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT,
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['status'],
            'status_text' => $auditRepo->getAuditStatus('10' . $result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        //获取审批人的审批状态
        if ($comeFrom == 2) {
            $audit_approval = AuditApprovalModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and approval_id = :approval_id:",
                'bind'       => [
                    'type'        => enums::$audit_type['RC'],
                    'value'       => $auditId,
                    'approval_id' => $user,
                ],
                'order'      => 'id desc',
            ]);

            $state            = $audit_approval->getState() ?? 0;
            $add_hour         = $this->getDI()['config']['application']['add_hour'];
            $audit_created_at = date('Y-m-d', strtotime($audit_approval->created_at) + ($add_hour * 3600));
            $day              = date('Y-m-d', strtotime(date('Y-m-d') . '-7 day'));
            if ($state == enums::$audit_status['panding'] && $day > $audit_created_at) {
                $data['options'] = (object)[];
            }
        }

        if($comeFrom == 1) {
            $data['options'] = (object)[];
        }

        $returnData['data']['head'] = $data;

        return $returnData;
    }

    /**
     * winhr获取审批详情
     * @param $userinfo
     * @param $contract_id
     * @return array
     */
    public function winHrApprovalDetail($userinfo, $contract_id , $version = 1)
    {
        $detail = HrStaffRenewContractApplyModel::findFirst([
            'conditions' => "contract_id = :contract_id:",
            'bind'       => [
                'contract_id' => $contract_id,
            ],
        ]);
        if (empty($detail)) {
            $this->getDI()->get('logger')->write_log([
                'function' => 'winHrApprovalDetail',
                'message'  => '未找到指定合同审批数据',
                'params'   => ['userinfo' => $userinfo, 'contract_id' => $contract_id],
            ], 'notice');
            return $this->checkReturn([]);
        }
        //version 1:原始版本 2：同一节点多个人 3：在第二个版本的基础之上显示待审批节点
        $server = new AuditListServer($this->lang, $this->timezone);
        return $server->getAuditDetailByType([
            'id'       => 'rc_' . $detail->id,
            'type'     => enums::$audit_type['RC'],
            'staff_id' => $userinfo['id'],
            'version'  => $version,
            'isCommit' => 1,
        ]);
    }

    /**
     * 撤销审批
     * @param $params
     * @return array
     * @throws Exception
     */
    public function winHrRevokeApply($params): array
    {
        $userinfo      = $params['userinfo'];
        $contract_id   = $params['contract_id'];
        $revoke_remark = $params['revoke_remark'];

        $detail = HrStaffRenewContractApplyModel::findFirst([
            'conditions' => "contract_id = :contract_id:",
            'bind'       => ['contract_id' => $contract_id],
        ]);

        if (empty($detail)) {
            $this->getDI()->get('logger')->write_log([
                'function' => 'winHrRevokeApply',
                'message'  => '未找到指定合同审批数据',
                'params'   => $params,
            ], 'notice');
            return $this->checkReturn([]);
        }

        //撤销
        $server = new ApprovalServer($this->lang, $this->timezone);
        $extend['super'] = 1;
        $server->cancel($detail->id, AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT, $revoke_remark, $userinfo['id'], $extend);
        return $this->checkReturn([]);
    }

    /**
     * 概要信息
     * @param int $auditId
     * @param $user
     * @return array|array[]
     */
    public function genSummary(int $auditId, $user): array
    {
        $detail = HrStaffRenewContractApplyModel::findFirst(['id = :id:', 'bind' => ['id' => $auditId]]);
        $result = [];
        if (!empty($detail)) {
            $staffInfo = (new StaffServer())->getStaffInfoById($detail->renew_staff_info_id);
            $result    = [
                [
                    'key'   => "renew_contract_staff",
                    'value' => sprintf('%s ( %s )', $detail->renew_staff_info_id, $staffInfo['staff_name']),
                ],
                [
                    'key'   => "contract_end_date",
                    'value' => $detail->contract_end_date,
                ],
            ];
        }
        return $result;
    }

    /**
     * 审批流回调属性
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $detail = HrStaffRenewContractApplyModel::findFirst([
            'conditions' => "id = :id:",
            'bind'       => ['id' => $auditId],
        ]);

        if (empty($detail->first_approval_id)) {
            $approval_ids              = $this->getFirstApproval($auditId);
            $detail->first_approval_id = $approval_ids;
            $detail->save();
        }

        if ($isFinal) {
            $detail->status = $state;
            $detail->save();

            //同意
            if ($state == enums::$audit_status['approved']) {
                //系统自动发送给员工 发送消息调用接口
                $data = ['contract_id' => $detail->contract_id];
                $rmq  = new RocketMQ('hr-contract-add');
                $rid  = $rmq->sendToMsg($data);
                $this->getDI()->get('logger')->write_log([
                    'function' => 'HrStaffRenewContractServer-setProperty',
                    'rid'      => $rid,
                    'auditId'  => $auditId,
                    'state'    => $state,
                ], 'info');
            }

            //驳回
            if($state == enums::$audit_status['dismissed']) {
                $detail = $detail->toArray();
                $staff_info_id = $extend['staff_id'];
                $staff_info_ids = [$staff_info_id, $detail['renew_staff_info_id']];
                $staff_list     = (new StaffServer())->getStaffInfoList($staff_info_ids);

                $approval_name         = '';
                $renew_staff_name      = '';
                $renew_staff_job_title = '';
                $renew_staff_store_id  = '';
                $renew_staff_store     = '';
                if (isset($staff_list[$staff_info_id])) {
                    $approval_name         = $staff_list[$staff_info_id]['staff_name'];
                    $renew_staff_name      = $staff_list[$detail['renew_staff_info_id']]['staff_name'];
                    $renew_staff_job_title = $staff_list[$detail['renew_staff_info_id']]['job_name'];
                    $renew_staff_store_id  = $staff_list[$detail['renew_staff_info_id']]['sys_store_id'];
                    $renew_staff_store     = $staff_list[$detail['renew_staff_info_id']]['store_name'];
                }

                //审批结果为不同意驳回 发送消息给AM和DM 文案四
                $message_title   = $this->getTranslation()->_('renew_contract_am_dm_dismissed_message_title');
                $message_content = $this->getTranslation()->_('renew_contract_am_dm_dismissed_message_content', [
                    'approval_name'         => $approval_name,                  //审批人姓名
                    'approval_id'           => $staff_info_id,                  //审批人工号
                    'renew_staff_info_id'   => $detail['renew_staff_info_id'],  //申请人工号
                    'renew_staff_name'      => $renew_staff_name,               //申请人姓名
                    'renew_staff_job_title' => $renew_staff_job_title,          //申请人职位
                    'renew_staff_store'     => $renew_staff_store,              //申请人网点
                    'contract_end_date'     => date('d/m/Y', strtotime($detail['contract_end_date'])), //合同到期日期

                ]);

                $region_piece_manager = (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getOrganizationRegionPieceManagerId($renew_staff_store_id);
                $this->sendMessage([
                    'staff_info_ids'   => [
                        $region_piece_manager['region_manager_id'],
                        $region_piece_manager['piece_manager_id'],
                    ],
                    'message_title'   => $message_title,
                    'message_content' => $message_content,
                ]);
            }
        }
    }

    /**
     * 获取一级审批人
     * @param $audit_id
     * @return string
     */
    public function getFirstApproval($audit_id): string
    {
        $approval_ids   = '';
        $audit_approval = AuditApprovalModel::findFirst([
            'conditions' => "biz_type = :biz_type: and biz_value = :biz_value:",
            'bind'       => [
                'biz_type'  => enums::$audit_type['RC'],
                'biz_value' => $audit_id,
            ],
            'order'      => 'id asc',
        ]);
        if (!empty($audit_approval)) {
            $audit_approval_list = AuditApprovalModel::find([
                'conditions' => "flow_node_id = :flow_node_id:",
                'bind'       => [
                    'flow_node_id' => $audit_approval->flow_node_id,
                ],
            ])->toArray();

            $approval_id_list = array_column($audit_approval_list, 'approval_id');
            $approval_ids     = implode(',', $approval_id_list);
        } else {
            $this->getDI()->get('logger')->write_log([
                'function' => 'HrStaffRenewContractServer-getFirstApproval',
                'message'  => '未找到一级节点审批数据',
                'audit_id' => $audit_id,
            ], 'notice');
        }

        return $approval_ids;
    }

    /**
     * @description 获取审批参数
     * @param int $auditId
     * @param $user
     * @param null $state
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        return [];
    }

    /**
     * 添加续签合同审批
     * @param $params
     * @return array
     */
    public function createStaffRenewContractApply($params): array
    {
        $date = $params['date'] ?? date('Y-m-d', strtotime('+30 days'));

        $department_ids = (new SysDepartmentServer())->getDepartmentIds(316);
        $builder        = $this->modelsManager->createBuilder();
        $builder->columns('
            hr_staff_contract.id as contract_id,
            hr_staff_contract.contract_name,
            hr_staff_contract.contract_path,
            hr_staff_contract.contract_start_date,
            hr_staff_contract.contract_end_date,
            hr_staff_contract.contract_ld_no,
            hr_staff_info.staff_info_id,
            hr_staff_info.name as staff_info_name,
            hr_staff_info.formal,
            hr_staff_info.hire_type,
            hr_staff_info.job_title,
            hr_staff_info.sys_store_id,
            hr_staff_info.node_department_id,
            hr_staff_info.manger
        ');
        $builder->from(['hr_staff_contract' => HrStaffContractModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hr_staff_contract.staff_id = hr_staff_info.staff_info_id ', 'hr_staff_info');
        //合同类型 1=>劳动合同
        $builder->where('hr_staff_contract.contract_type = :contract_type:', ['contract_type' => HrStaffContractModel::CONTRACT_TYPE_LABOR_CONTRACT]);
        //合同状态 80=>待续签
        $builder->andWhere('hr_staff_contract.contract_status = :contract_status:', ['contract_status' => HrStaffContractModel::CONTRACT_STATUS_TO_BE_RENEWED]);
        //合同删除状态 0=>未删除
        $builder->andWhere('hr_staff_contract.contract_is_deleted = :contract_is_deleted:', ['contract_is_deleted' => HrStaffContractModel::CONTRACT_DELETED_NO]);
        //是否长期合同 2=>否
        $builder->andWhere('hr_staff_contract.contract_date_is_long = :contract_date_is_long:', ['contract_date_is_long' => HrStaffContractModel::CONTRACT_DATE_IS_LONG_NO]);
        //合同截止日期
        $builder->andWhere('hr_staff_contract.contract_end_date = :contract_end_date:', ['contract_end_date' => $date]);
        //非离职
        $builder->andWhere('hr_staff_info.state != :state:', ['state' => HrStaffInfoModel::STATE_2]);
        //员工雇佣类型 2=>月薪制合同工
        $builder->andWhere('hr_staff_info.hire_type = :hire_type:', ['hire_type' => HrStaffInfoModel::HIRE_TYPE_2]);
        //员工类型 formal 1=>正式员工
        $builder->andWhere('hr_staff_info.formal = :formal:', ['formal' => HrStaffInfoModel::FORMAL_1]);
        //Malaysia Network Management 及子部门
        $builder->inWhere('hr_staff_info.node_department_id', $department_ids);
        //职位 110(van courier),1199(car courier)
        $builder->inWhere('hr_staff_info.job_title', [enums::$job_title['van_courier'], enums::$job_title['car_courier']]);

        $list = $builder->getQuery()->execute()->toArray();

        $data_result = [];
        if(empty($list)) {
            return $data_result;
        }

        $contract_ids = array_column($list, 'contract_id');
        //查找如果合同有创建过审批流则不再进行创建
        $apply_list = HrStaffRenewContractApplyModel::find([
            'conditions' => "contract_id in ({contract_ids:array})",
            'bind'       => [
                'contract_ids' => $contract_ids,
            ],
        ])->toArray();

        $apply_list = array_column($apply_list, null, 'contract_id');

        foreach ($list as $key => $value) {
            if(isset($apply_list[$value['contract_id']])) {
                $this->logger->write_log([
                    'function'      => 'addRenewContractApply',
                    'message'       => '该合同已经创建过审批，不再新创建审批',
                    'staff_info_id' => $value['staff_info_id'],
                    'contract_id'   => $value['contract_id'],
                ], 'info');
                $data_result[] = [
                    'message'       => '该合同已经创建过审批，不再新创建审批',
                    'staff_info_id' => $value['staff_info_id'],
                    'contract_id'   => $value['contract_id'],
                ];
                continue;
            }

            $create_result = $this->addRenewContractApply([
                'renew_staff_info_id'     => $value['staff_info_id'],
                'contract_id'             => $value['contract_id'],
                'contract_name'           => $value['contract_name'],
                'contract_start_date'     => $value['contract_start_date'],
                'contract_end_date'       => $value['contract_end_date'],
                'contract_ld_no'          => $value['contract_ld_no'],
            ]);
            $this->logger->write_log([
                'function' => 'addRenewContractApply',
                'params'   => $value,
                'result'   => $create_result,
            ], 'info');
            $data_result[] = [
                'message'       => '创建续签合同审批完成',
                'create_result' => json_encode($create_result, JSON_UNESCAPED_UNICODE),
                'staff_info_id' => $value['staff_info_id'],
                'contract_id'   => $value['contract_id'],
            ];
        }
        return $data_result;
    }

    /**
     * $data
     * @param $params
     * @return array
     */
    public function timeOutCloseRenewContractApply($params): array
    {
        $data_result = [];
        $date          = $params['date'] ?? DateHelper::localToUtc(date('Y-m-d 00:00:00', strtotime('-7 days')));
        $time_out_list = HrStaffRenewContractApplyModel::find([
            'conditions' => 'updated_at < :date: and status = :status:',
            'bind'       => [
                'date'   => $date,
                'status' => enums::$audit_status['panding'],
            ],
        ])->toArray();
        $ids = array_column($time_out_list, 'id');
        if(empty($ids)) {
            return $data_result;
        }
        $time_out_audit_approval = AuditApprovalModel::find([
            'conditions' => "biz_type = :type: and biz_value in({biz_value:array}) and state = :state:",
            'bind'       => [
                'type'  => enums::$audit_type['RC'],
                'state' => enums::$audit_status['panding'],
                'biz_value'  => $ids,
            ],
        ])->toArray();
        $approval_staff_info_id = array_column($time_out_audit_approval, 'approval_id');
        $time_out_audit_approval_list = [];
        foreach ($time_out_audit_approval as $key => $value) {
            $time_out_audit_approval_list[$value['biz_value']][] = $value;
        }

        $staff_info_ids = array_column($time_out_list, 'renew_staff_info_id');
        $staff_info_ids = array_merge($staff_info_ids, $approval_staff_info_id);
        $staff_list     = (new StaffServer())->getStaffInfoList($staff_info_ids);

        $approve = new ApprovalServer($this->lang, $this->timezone);
        $t = $this->getTranslation('en');

        foreach ($time_out_list as $key => $value) {
            $result = $approve->timeOut($value['id'], enums::$audit_type['RC']);
            //给am/dm发送消息 文案三
            //renew_contract_time_out_am_dm_message_title
            //renew_contract_time_out_am_dm_message_content
            if (isset($staff_list[$value['renew_staff_info_id']])) {
                $renew_staff_info_id    = $value['renew_staff_info_id']; //申请人工号
                $renew_staff_name       = '';//申请人姓名
                $renew_staff_job_title  = '';//申请人职位
                $renew_staff_store_id   = '';//申请人网点id
                $renew_staff_store_name = '';//申请人网点名称

                //多个审批人处理
                $approval_name_arr = [];
                $approval_list     = $time_out_audit_approval_list[$value['id']] ?? [];
                if(empty($approval_list)) {
                    //未找到审批人信息
                    $data_result[] = [
                        'time_out_result'      => $result,
                        'renew_staff_info_id'  => $value['renew_staff_info_id'],
                        'message'              => '未找到审批人信息',
                        'renew_contract_apply' => $value,
                    ];
                    continue;
                }

                foreach ($approval_list as $a_key => $a_value) {
                    $approval_staff_info = $staff_list[$a_value['approval_id']] ?? [];
                    $approval_name       = $approval_staff_info['staff_name'] ?? '';
                    $approval_name_arr[] = sprintf('%s ( %s )', $approval_name, $a_value['approval_id']);
                }

                $staff_info = $staff_list[$value['renew_staff_info_id']] ?? [];
                if (!empty($staff_info)) {
                    $renew_staff_name      = $staff_info['staff_name'] ?? '';
                    $renew_staff_job_title = $staff_info['job_name'] ?? '';
                    if ($staff_info['sys_store_id'] == enums::HEAD_OFFICE_ID) {
                        $renew_staff_store_name = enums::HEAD_OFFICE;
                    } else {
                        $renew_staff_store_id   = $staff_info['sys_store_id'] ?? '';
                        $renew_staff_store_name = $staff_info['store_name'] ?? '';
                    }
                }

                $am_dm_message_title   = $t->_('renew_contract_time_out_am_dm_message_title');
                $am_dm_message_content = $t->_('renew_contract_time_out_am_dm_message_content', [
                    'approval_name'         => implode(',', $approval_name_arr),
                    'renew_staff_name'      => $renew_staff_name,
                    'renew_staff_info_id'   => $renew_staff_info_id,
                    'renew_staff_job_title' => $renew_staff_job_title,
                    'renew_staff_store'     => $renew_staff_store_name,
                    'contract_end_date'     => date('d/m/Y', strtotime($value['contract_end_date'])),
                ]);

                $region_piece_manager =  (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getOrganizationRegionPieceManagerId($renew_staff_store_id);
                $this->sendMessage([
                    'staff_info_ids'   => [
                        $region_piece_manager['region_manager_id'],
                        $region_piece_manager['piece_manager_id'],
                    ],
                    'message_title'   => $am_dm_message_title,
                    'message_content' => $am_dm_message_content,
                ]);

                $this->getDI()->get('logger')->write_log([
                    'function' => 'timeOutCloseRenewContractApply',
                    'params'   => $value,
                    'result'   => $result,
                ], 'info');
                $data_result[] = [
                    'time_out_result'      => $result,
                    'renew_staff_info_id'  => $value['renew_staff_info_id'],
                    'approval_name_arr'    => $approval_name_arr,
                    'region_piece_manager' => $region_piece_manager,
                ];
            }
        }
        return $data_result;
    }

    /**
     * 给待审批人发送消息,如果前四天没有审批，从第五天开始至第七天，每天北京时间早9:00提醒审批人。文案一；并发消息AM和DM。文案二
     * @param $params
     * @return array
     */
    public function renewContractPendingApprovalSendMessage($params): array
    {
        $date         = $params['date'] ?? DateHelper::localToUtc(date('Y-m-d 00:00:00', strtotime('-3 days')));
        $data_result  = [];
        $pending_list = AuditApprovalModel::find([
            'conditions' => "biz_type = :type: and state = :state: and created_at < :date:",
            'bind'       => [
                'type'  => enums::$audit_type['RC'],
                'state' => enums::$audit_status['panding'],
                'date'  => $date,
            ],
        ])->toArray();

        if (!empty($pending_list)) {
            $approval_staff_info_ids = array_column($pending_list, 'approval_id'); //审批人
            $renew_staff_info_ids    = array_column($pending_list, 'submitter_id');//申请人
            $audit_ids               = array_column($pending_list, 'biz_value');   //续签审批id

            $staff_info_ids = array_merge($approval_staff_info_ids, $renew_staff_info_ids);
            $staff_list     = (new StaffServer())->getStaffInfoList($staff_info_ids);

            $renew_list = HrStaffRenewContractApplyModel::find([
                'conditions' => "id in ({ids:array})",
                'bind'       => [
                    'ids' => $audit_ids,
                ],
            ])->toArray();

            $renew_list = array_column($renew_list, null, 'id');
            $t          = $this->getTranslation('zh-CN');
            foreach ($pending_list as $key => $value) {
                $result = [];
                $renew = $renew_list[$value['biz_value']] ?? [];
                $result['audit_approval_pending'] = $value;
                $result['renew_contract'] = $renew;

                if (!empty($renew) && !empty($renew['first_approval_id'])) {
                    $first_approval_ids = explode(',', $renew['first_approval_id']);
                    if (!in_array($value['approval_id'], $first_approval_ids)) {
                        //跳过非一级节点审批人
                        continue;
                    }
                }

                //如果前四天没有审批，从第五天开始至第七天，每天北京时间早9:00提醒审批人。文案一
                $approval_name          = '';                     //审批人姓名
                $renew_staff_info_id    = $value['submitter_id']; //申请人工号
                $renew_staff_name       = '';                     //申请人姓名
                $renew_staff_job_title  = '';                     //申请人职位
                $renew_staff_store_id   = '';                     //申请人网点id
                $renew_staff_store_name = '';                     //申请人网点名称

                if (isset($staff_list[$value['approval_id']])) {
                    $approval_name = $staff_list[$value['approval_id']]['staff_name'] ?? '';
                }

                if (isset($staff_list[$value['submitter_id']])) {
                    $staff_submitter_info = $staff_list[$value['submitter_id']];
                    $renew_staff_name      = $staff_submitter_info['staff_name'];
                    $renew_staff_job_title = $staff_submitter_info['job_name'];
                    if ($staff_submitter_info['sys_store_id'] == enums::HEAD_OFFICE_ID) {
                        $renew_staff_store_name = enums::HEAD_OFFICE;
                    } else {
                        $renew_staff_store_id   = $staff_submitter_info['sys_store_id'];
                        $renew_staff_store_name = $staff_submitter_info['store_name'];
                    }
                }

                $contract_end_date = '';
                if (!empty($renew)) {
                    $contract_end_date = $renew['contract_end_date'] ?? '';
                }

                $message_title   = $t->_('renew_contract_pending_approval_message_title');
                $message_content = $t->_('renew_contract_pending_approval_message_content', [
                    'approval_name'         => $approval_name,
                    'renew_staff_name'      => $renew_staff_name,
                    'renew_staff_info_id'   => $renew_staff_info_id,
                    'renew_staff_job_title' => $renew_staff_job_title,
                    'renew_staff_store'     => $renew_staff_store_name,
                    'contract_end_date'     => date('d/m/Y', strtotime($contract_end_date)),
                ]);

                $this->sendMessage([
                    'staff_info_ids'   => [$value['approval_id']],
                    'message_title'   => $message_title,
                    'message_content' => $message_content,
                ]);

                //并发消息AM和DM。文案二
                if (!empty($renew_staff_store_id)) {
                    $am_dm_message_title   = $t->_('renew_contract_pending_am_dm_message_title');
                    $am_dm_message_content = $t->_('renew_contract_pending_am_dm_message_content', [
                        'approval_name'         => $approval_name,
                        'renew_staff_name'      => $renew_staff_name,
                        'renew_staff_info_id'   => $renew_staff_info_id,
                        'renew_staff_job_title' => $renew_staff_job_title,
                        'renew_staff_store'     => $renew_staff_store_name,
                        'contract_end_date'     => date('d/m/Y', strtotime($contract_end_date)),
                    ]);

                    $region_piece_manager =  (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getOrganizationRegionPieceManagerId($renew_staff_store_id);
                    $this->sendMessage([
                        'staff_info_ids'   => [
                            $region_piece_manager['region_manager_id'],
                            $region_piece_manager['piece_manager_id'],
                        ],
                        'message_title'   => $am_dm_message_title,
                        'message_content' => $am_dm_message_content,
                    ]);
                    $result['region_piece_manager'] = $region_piece_manager;
                }
                $data_result[] = $result;
            }
        }
        return $data_result;
    }

    /**
     * 废弃
     * @deprecated
     * 查找组织架构上大区片区负责人
     * @param $store_id
     * @return int[]
     */
    public function getOrganizationRegionPieceManagerId($store_id): array
    {
        $data = ['region_manager_id' => 0, 'piece_manager_id' => 0];

        $relation = HrOrganizationDepartmentStoreRelationModel::findFirst([
            'conditions' => "store_id = :store_id: ",
            'bind'       => ['store_id' => $store_id],
        ]);

        if (!empty($relation)) {
            if (!empty($relation->region_id)) {
                $region_detail = SysManageRegionModel::findFirst([
                    'conditions' => "id = :region_id: ",
                    'bind'       => ['region_id' => $relation->region_id],
                ]);
                $data['region_manager_id'] = !empty($region_detail) ? $region_detail->manager_id : 0;
            }

            if (!empty($relation->piece_id)) {
                $piece_detail = SysManagePieceModel::findFirst([
                    'conditions' => "id = :piece_id: ",
                    'bind'       => ['piece_id' => $relation->piece_id],
                ]);
                $data['piece_manager_id'] = !empty($piece_detail) ? $piece_detail->manager_id : 0;
            }
        }
        return $data;
    }

    /**
     * 发送by消息
     * @param $params
     * @return void
     */
    public function sendMessage($params)
    {
        $staff_info_ids  = $params['staff_info_ids'] ?? [];  //接收工号
        $message_title   = $params['message_title'] ?? '';   //消息标题
        $message_content = $params['message_content'] ?? ''; //消息内容

        if (!empty($staff_info_ids)) {
            $staff_users = [];
            foreach ($staff_info_ids as $staff_info_id) {
                if (!empty($staff_info_id)) {
                    $staff_users[] = ['id' => $staff_info_id];
                }
            }

            if (!empty($staff_users)) {
                $message_params['staff_users']        = $staff_users;//数组 多个员工id
                $message_params['message_title']      = $message_title;
                $message_params['message_content']    = "<div style='font-size: 40px'>" . $message_content . "</div>";
                $message_params['staff_info_ids_str'] = implode(',', $staff_info_ids);;
                $message_params['category']           = -1;

                $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
                $bi_rpc->setParams($message_params);
                $bi_rpc->execute();
            }
        }
    }

    /**
     * 超时关闭续签申请
     * @param $params
     * @return array
     */
    public function timeOutCloseRenewContractApplyV2($params): array
    {
        if (empty($params)) {
            return [];
        }
        $timeoutDate = $params['date'];
        $data_result = [];

        //获取审批数据
        $timeoutList = AuditApplyModel::find([
            'conditions' => "time_out = :date: and state = :state: and biz_type = :biz_type:",
            'bind'       => [
                'date'     => $timeoutDate,
                'state'    => enums::APPROVAL_STATUS_TIMEOUT,
                'biz_type' => AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT,
            ],
            'columns'    => 'biz_value,submitter_id',
        ])->toArray();

        $auditIds = array_column($timeoutList, 'biz_value');
        if(empty($auditIds)) {
            return $data_result;
        }
        $timeoutAuditApproval = AuditApprovalModel::find([
            'conditions' => "biz_type = :type: and biz_value in({biz_value:array}) and state = :state:",
            'bind'       => [
                'type'      => AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT,
                'state'     => enums::APPROVAL_STATUS_TIMEOUT,
                'biz_value' => $auditIds,
            ],
            'columns'    => 'approval_id,biz_value',
        ])->toArray();
        $approvalStaffInfoId = array_column($timeoutAuditApproval, 'approval_id');
        $timeoutAuditApprovalList = [];
        foreach ($timeoutAuditApproval as $value) {
            $timeoutAuditApprovalList[$value['biz_value']][] = $value;
        }

        $staffInfoIds  = array_column($timeoutList, 'submitter_id');
        $staffInfoIds  = array_merge($staffInfoIds, $approvalStaffInfoId);
        $staffInfoList = (new StaffServer())->getStaffInfoList($staffInfoIds);
        $t             = $this->getTranslation('en');
        $contractInfo  = HrStaffRenewContractApplyModel::find([
            'conditions' => 'id in ({audit_ids:array})',
            'bind'       => [
                'audit_ids' => $auditIds
            ],
        ])->toArray();
        $contractInfo = array_column($contractInfo, 'contract_end_date', 'id');

        foreach ($timeoutList as $value) {
            //给am/dm发送消息 文案三
            //renew_contract_time_out_am_dm_message_title
            //renew_contract_time_out_am_dm_message_content
            if (!isset($staffInfoList[$value['submitter_id']])) {
                continue;
            }

            $renew_staff_info_id    = $value['submitter_id']; //申请人工号
            $renew_staff_name       = '';//申请人姓名
            $renew_staff_job_title  = '';//申请人职位
            $renew_staff_store_id   = '';//申请人网点id
            $renew_staff_store_name = '';//申请人网点名称

            //多个审批人处理
            $approval_name_arr = [];
            $approval_list     = $timeoutAuditApprovalList[$value['biz_value']] ?? [];
            if(empty($approval_list)) {
                //未找到审批人信息
                $data_result[] = [
                    'renew_staff_info_id'  => $value['submitter_id'],
                    'message'              => '未找到审批人信息',
                    'renew_contract_apply' => $value,
                ];
                continue;
            }

            foreach ($approval_list as $approval_info) {
                $approval_staff_info = $staffInfoList[$approval_info['approval_id']] ?? [];
                $approval_name       = $approval_staff_info['staff_name'] ?? '';
                $approval_name_arr[] = sprintf('%s ( %s )', $approval_name, $approval_info['approval_id']);
            }

            $staff_info = $staffInfoList[$value['submitter_id']];
            if (!empty($staff_info)) {
                $renew_staff_name      = $staff_info['staff_name'] ?? '';
                $renew_staff_job_title = $staff_info['job_name'] ?? '';
                if ($staff_info['sys_store_id'] == enums::HEAD_OFFICE_ID) {
                    $renew_staff_store_name = enums::HEAD_OFFICE;
                } else {
                    $renew_staff_store_id   = $staff_info['sys_store_id'] ?? '';
                    $renew_staff_store_name = $staff_info['store_name'] ?? '';
                }
            }
            //合同结束时间
            $contractEndDate = $contractInfo[$value['biz_value']] ?? '';

            $am_dm_message_title   = $t->_('renew_contract_time_out_am_dm_message_title');
            $am_dm_message_content = $t->_('renew_contract_time_out_am_dm_message_content', [
                'approval_name'         => implode(',', $approval_name_arr),
                'renew_staff_name'      => $renew_staff_name,
                'renew_staff_info_id'   => $renew_staff_info_id,
                'renew_staff_job_title' => $renew_staff_job_title,
                'renew_staff_store'     => $renew_staff_store_name,
                'contract_end_date'     => date('d/m/Y', strtotime($contractEndDate)),
            ]);

            $region_piece_manager =  (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getOrganizationRegionPieceManagerId($renew_staff_store_id);
            $this->sendMessage([
                'staff_info_ids'   => [
                    $region_piece_manager['region_manager_id'],
                    $region_piece_manager['piece_manager_id'],
                ],
                'message_title'   => $am_dm_message_title,
                'message_content' => $am_dm_message_content,
            ]);

            $this->getDI()->get('logger')->write_log([
                'function' => 'timeOutCloseRenewContractApply',
                'params'   => $value,
                //'result'   => $result,
            ], 'info');
            $data_result[] = [
                //'time_out_result'      => $result,
                'renew_staff_info_id'  => $value['submitter_id'],
                'approval_name_arr'    => $approval_name_arr,
                'region_piece_manager' => $region_piece_manager,
            ];
        }
        return $data_result;
    }
}