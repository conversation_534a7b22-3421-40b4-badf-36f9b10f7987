<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\ApprovalFinderEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\WorkflowConfigEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalDelayModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\AuditCCModel;
use FlashExpress\bi\App\Models\backyard\AuditLogModel;
use FlashExpress\bi\App\Models\backyard\HcmStaffManageListModel;
use FlashExpress\bi\App\Models\backyard\HcmStaffManageRegionModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel as ByHrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageDepartmentModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManagePieceModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageRegionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageStoreCategoryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageStoreModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditUnionModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeAccidentBusinessModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeAttachmentModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeBaseModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeBaseMultiTypeCcModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeBaseMultiTypeModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeCcBaseModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeOvertimeConfigModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeRelateBaseModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeRelateModel;
use FlashExpress\bi\App\Models\bi\HrStaffInfoModel as BiHrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\bi\HrStaffManageRegionsModel;
use FlashExpress\bi\App\Models\bi\SysStoreModel as BiSysStoreModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Models\oa\WorkflowBasicConfigModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffAuditToolLog;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Traits\ComplexFormulaTrait;
use FlashExpress\bi\App\Util\WorkflowFormula;
use Phalcon\Db;
use Ramsey\Uuid\Provider\Node\RandomNodeProvider;
use Ramsey\Uuid\Uuid;


class WorkflowServer extends BaseServer
{
    use ComplexFormulaTrait;
    public    $staffRepository;
    public    $timezone;
    public $isUnpaid = false;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
        $this->staffRepository= new StaffRepository();
    }

    /**
     *
     * @param $idUnion
     * @param $typeUnion
     */
    private function getApproveGrpByStatus($idUnion, $typeUnion)
    {
        $sql = "--
        select
            *,
            group_concat(approval_id) as approval_ids
        from
            staff_audit_union
        where id_union = :id_union and type_union = :type_union group by status_union for update;
        ";
        $db = $this->getDI()->get('db');
        $list = $db->fetchAll($sql, Db::FETCH_ASSOC, [
            "id_union" => $idUnion,
            "type_union" => $typeUnion,
        ]);

        return array_column($list, null, 'status_union');
    }

    /**
     * 获取options 控制按钮
     * @param $comFrom
     * @param $idUnion
     * @param $typeUnion
     * @param $userinfo
     *
     */
    public function getOptions($comFrom, $idUnion, $typeUnion, $userinfo)
    {
        $lastApprovel = $this->getDI()->get("db")->fetchOne("select * from staff_audit_approval where audit_id = " . explode("_", $idUnion)[1] . " and  type =  " . $typeUnion . " and status = 2 order by level desc", Db::FETCH_ASSOC);
        $lastApprovelIds = $lastApprovel ? explode(",", $lastApprovel['staff_ids']) : [];
        $list = $this->getApproveGrpByStatus($idUnion, $typeUnion);

        $options = [];
        if ($comFrom == 2) {// comfrom 我审批的
            if (isset($list[enums::$audit_list_status['panding_approval']])) {
                $approvalIds = explode(",", $list[enums::$audit_list_status['panding_approval']]['approval_ids']);
                if (in_array($userinfo['staff_id'], $approvalIds)) {
                    $options = [1,2];
                }
            }
//            if (!$options && !isset($list[enums::$audit_list_status['revoked']]) && !isset($list[enums::$audit_list_status['panding_approval']]) && in_array($userinfo['staff_id'], $lastApprovelIds)) {
//                $options = [3];
//            }
        } else { // 我申请的
            if (isset($list[enums::$audit_list_status['panding']]) && !$lastApprovelIds) {
                $options = [3];
            }
        }
        $result = [];
        foreach ($options as $i) {
            $result['code'. $i] = $i;
        }
        return $result;

    }

    /**
     *
     * @param $idUnion
     * @param $approvalId
     * @param $typeUnion
     *
     */
    public function generateStream($idUnion, $approvalId, $typeUnion)
    {
        $auditLogs = (new StaffAuditToolLog())->getAuditRecords(['id' => $approvalId, 'type' => $typeUnion]);

        $result = [];
        $list = $this->getApproveGrpByStatus($idUnion, $typeUnion);
        $staffRepository = new StaffRepository();
        $repo = new AuditlistRepository($this->lang, $this->timezone);
        foreach ($auditLogs as $k => $auditLog) {
            $staff = $staffRepository -> getStaffpositionV2($auditLog['operator']);
            if ($k == 0) {
                $result[] = [
                    'staff_id'    => $auditLog['operator'],
                    'name'        => $staff['name'] ?? '',
                    'position'    => $staff['job_name'] ?? '',
                    'department'  => $staff['department_name'] ?? '',
                    'store_id'    => $staff['organization_id'] ?? '',
                    "status" => $this->getTranslation()->_('send_request'),
                    'status_code' => 8,
                    'time'        => $auditLog['created_at'],
                    'remark'=>'',
                    "is_ok"             => 0,
                    "audit_info"        => '',
                    'is_show_icon'      => 1,
                    'is_OK'       => 0,
                ];
            } else {
                $result[] = [
                    'staff_id'    => $auditLog['operator'],
                    'name'        => $staff['name'] ?? '',
                    'position'    => $staff['job_name'] ?? '',
                    'department'  => $staff['department_name'] ?? '',
                    'store_id'    => $staff['organization_id'] ?? '',
                    'status'      => $repo->getAuditStatus('10' . $auditLog['to_status_type']),
                    'status_code' => (int) $auditLog['to_status_type'],
                    'time'        => $auditLog['created_at'],
                    'remark'=>'',
                    "is_ok"             => 0,
                    "audit_info"        => '',
                    'is_show_icon'      => 0,
                    'is_OK'       => 0,
                ];

            }
        }

        if (isset($list[enums::$audit_list_status['panding_approval']]['approval_ids'])) {
            $approvalIds = explode(",", $list[enums::$audit_list_status['panding_approval']]['approval_ids']);
            $staff = $staffRepository -> getStaffpositionV2($approvalIds[0]);
            $time = date("Y-m-d H:i:s", strtotime($list[enums::$audit_list_status['panding_approval']]['created_at'] . " +7 hours"));
            $result[] = [
                'staff_id'    => $approvalIds[0],
                'name'        => $staff['name'] ?? '',
                'position'    => $staff['job_name'] ?? '',
                'department'  => $staff['department_name'] ?? '',
                'store_id'    => $staff['organization_id'] ?? '',
                'status'      => $repo->getAuditStatus(107),
                'status_code' => intval(1),
                'time'        => $time,
                'remark'=>'',
                "is_ok"             => 1,
                "audit_info"        => '',
                'is_show_icon'      => 1,
                'is_OK'       => 1,
            ];
        }
        //追加昵称字段
        $staffInfoArrs = array_column($result, 'staff_id');
        $staffArr = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in({staffs:array})',
            'bind'       => ['staffs' => $staffInfoArrs],
            'columns'    => ['staff_info_id', 'nick_name'],
        ])->toArray();
        $staffArr = array_column($staffArr, 'nick_name', 'staff_info_id');
        foreach ($result as $k => $v) {
            $result[$k]['name'] =  isset($staffArr[$v['staff_id']]) && $staffArr[$v['staff_id']]
                ? $v['name'] . "({$staffArr[$v['staff_id']]})"
                : $v['name'];
        }
        return $result;

    }

    /**
     * 获取节点审核人ID
     *
     * @param $node
     * @param $user
     * @param array $extend
     * @param int $type ==1 时是 审批流获取审核人  2 时是 抄送获取审核人
     *
     * @return array
     * @throws InnerException
     */
    public function parseNode($node, $user, array $extend = [], int $type = Enums::NODE_WORKFLOW_APPROVER): array
    {
        //获取个人信息
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = ?1',
            'bind' => [
                1 => $user,
            ],
        ]);
        //  如果不存在 并且  申请人不等于 10000 系统 id
        if (empty($staffInfo) && $user != enums::SYSTEM_STAFF_ID) {
            $this->logger->write_log(sprintf("Staff info id %s DO NOT EXIST!", $user));
            throw new InnerException($this->getTranslation()->_('4008'), ErrCode::WORKFLOW_STAFF_NOT_EXIST_ERROR);
        }

        $nodeDeptId = '';
        switch ($node->auditor_type) {
            case enums::WF_NODE_DESIGNATE_OTHER: //指定的员工工号
                $auditors = $node->auditor_id;
                break;
            case Enums::WF_NODE_MANAGER: //上级
                $auditors = $this->findSuperior($user, $node->auditor_level);
                break;
            case Enums::WF_NODE_DEPARTMENT_MANAGER: //部门负责人
                [$auditors, $nodeDeptId] = $this->findDepartmentManager($staffInfo->node_department_id, $node->auditor_level, $extend);
                break;
            case Enums::WF_NODE_SUPERVISOR: //网点负责人
                $auditors = $this->findStoreManager($staffInfo->sys_store_id, $extend);
                break;
            case Enums::WF_NODE_DM: //DM 片区经理
                $auditors = $this->findDM($staffInfo->sys_store_id, $extend);
                break;
            case Enums::WF_NODE_RM: //大区经理
                $auditors = isset($staffInfo->sys_store_id) && $staffInfo->sys_store_id ? $this->findRM($staffInfo->sys_store_id) : "";
                break;
            case Enums::WF_NODE_AM:
                $auditors = isset($staffInfo->sys_store_id) && $staffInfo->sys_store_id
                    ? $this->findAM($staffInfo->sys_store_id, $extend)
                    : '';
                break;
            case Enums::WF_NODE_GROUP_BOSS:
                $auditors = $this->findGroupBoss($staffInfo->node_department_id, $extend);
                break;
            case Enums::WF_NODE_CEO: //获取C Level BOSS
            case Enums::WF_NODE_COO:
            case Enums::WF_NODE_CFO:
            case Enums::WF_NODE_CPO:
                $auditors = $this->findCLevelManager($node->auditor_type);
                break;
            case Enums::WF_NODE_SPEC_DEPARTMENT_MANAGER: //获取指定部门负责人
                [$auditors, $nodeDeptId] = $this->findSpecifiyDepartmentManager($node->auditor_level);
                break;
            case Enums::WF_NODE_SPEC_JOB_TITLE: //申请人上级或上上级，有Sales Manager职位
                $auditors = $this->findSpecifiyJobTitle($user);
                break;
            case Enums::WF_NODE_SS: // 网点 Shop Supervisor 职位
                $auditors = $this->findShopSupervisor($staffInfo->sys_store_id);
                break;
            case Enums::WF_NODE_SOM: // shop operations manager 职位
                $auditors = $this->findShopOperationsManager();
                break;
            case Enums::WF_NODE_HRBP: //HRBP
                //如果没有申请时没有提交网点，则默认使用申请人所在的网点
                $extend['store_id'] = $extend['store_id'] ?? (isset($staffInfo) ? $staffInfo->sys_store_id : "");
                if (isset($node->auditor_level) && $node->auditor_level) {
                    $auditors = $this->findHRBP($staffInfo->node_department_id ?? 0, $extend, $node->auditor_level);
                } else {
                    $auditors = $this->findHRBP($staffInfo->node_department_id ?? 0, $extend);
                }
                break;
            case Enums::WF_NODE_HR_SERVICE: //Hr Service
                //如果没有申请时没有提交网点，则默认使用申请人所在的网点
                $extend['store_id'] = $extend['store_id'] ?? (isset($staffInfo) ? $staffInfo->sys_store_id : "");
                if (isset($node->auditor_level) && $node->auditor_level) {
                    $auditors = $this->findJurisdictionAreaStaffIds($staffInfo->node_department_id ?? 0, $extend, $node->auditor_level, RolesModel::ROLE_HR_SERVICE);
                } else {
                    $auditors = $this->findJurisdictionAreaStaffIds($staffInfo->node_department_id ?? 0, $extend , 0,RolesModel::ROLE_HR_SERVICE);
                }
                break;
            case Enums::WF_NODE_STORE_MANAGER: //网点负责人
                if (isset($node->auditor_level) && $node->auditor_level) {
                    $auditors = isset($staffInfo->sys_store_id) && $staffInfo->sys_store_id
                        ? $this->findHeadOfStoreEx($staffInfo->sys_store_id, $node->auditor_level, $extend)
                        : '';
                } else {
                    $auditors = isset($staffInfo->sys_store_id) && $staffInfo->sys_store_id
                        ? $this->findHeadOfStore($staffInfo->sys_store_id, $extend)
                        : '';
                }
                break;
            case Enums::WF_NODE_MULTI_DEPARTMENT_MANAGER:
                [$auditors, $nodeDeptId] = $this->findDepartmentManagerEx($staffInfo->node_department_id, $node->auditor_level, $extend);
                break;
            case Enums::WF_NODE_DM_BY_ORG: //根据组织架构去找DM
                $auditors = $this->findDMEx($staffInfo->sys_store_id, $node->auditor_level, $extend);
                break;
            case Enums::WF_NODE_AM_BY_ORG: //根据组织架构去找AM
                $auditors = $this->findAMEx($staffInfo->sys_store_id, $node->auditor_level, $extend);
                break;
            case Enums::WF_NODE_DI:
                // 节点审批人动态传入 Dynamic incoming
                $auditors = $this->getNodeDynamicAuditor($node->auditor_level, $extend);
                break;
            case Enums::WF_NODE_SUPERVISOR_MANAGER:
                $auditors = $this->findStoreManagerOrHigher($staffInfo->sys_store_id, $user,$extend);
                break;
            case Enums::WF_NODE_ASSET_HUB_MANAGER: // 网点 Shop Supervisor 职位 --todo
                $auditors = $this->findStoreJobStaff($staffInfo->sys_store_id,$node->auditor_level, $extend);
                break;
            case Enums::WF_FIRST_LEVEL_DEPARTMENT_MANAGER: //部门是BU级别，找当前BU负责人；如果是 C-Level级别 找当前c-level负责人；如果是普通部门则直接找一级部门负责人
                $auditors = $this->findFirstLevelDepartmentManager($staffInfo->node_department_id, $extend);
                break;
            case Enums::WF_NODE_FIRST_BU_DEPARTMENT_MANAGER:   // 部门属于BU找 BU负责人 ； 如果部门属于cLevel找 clevel负责人； 否则找GroupCEO
            case Enums::WF_NODE_FIRST_BU_DEPARTMENT_MANAGER_BY_FORM:   // 部门属于BU找 BU负责人 ； 如果部门属于cLevel找 clevel负责人； 否则找GroupCEO
                @list($auditors, $nodeDeptId) = $this->findFirstBuDepartmentManager($staffInfo->node_department_id, $extend);
                break;
            case Enums::WF_NODE_CLEVEL_MANAGER: // 查找部门的 clevel
                [$auditors, $nodeDeptId] = $this->findCLevelsManager($staffInfo->node_department_id, $extend);
                break;
	        case Enums::WF_NODE_HUB_HEADQUARTER_JOB_TITLE: // 部门Hub Headquarter 职位 --todo
		        $auditors = $this->findHubHeadquarterJobStaff($node->auditor_id,$extend);
		        break;
	        case Enums::WF_NODE_DATA_SUPPORT_JOB_TITLE: //属于属于Data Support小组的 xx职位的人 --todo
		        $auditors = $this->findDataSupportJobStaff($node->auditor_id);
		        break;
	        case Enums::WF_NODE_JOB_IDS: //获取根据网点类型获取职位下的人
		        $auditors = $this->findCategoryJobStaff($node->auditor_id,$extend);
		        break;
	        case Enums::WF_NODE_JOB_TITLE: //获取职位下的人
		        $auditors = $this->findJobStaff($node->auditor_id);
		        break;
	        case Enums::WF_APPLICANT: //获取他自己
		        $auditors = $user;
		        break;
            case Enums::WF_NODE_DEPARTMENT_MANAGER_V3: //获取指定部门负责人
                [$auditors, $nodeDeptId] = $this->findDepartmentManagerV3($staffInfo->node_department_id ?? 0, $node->auditor_level, $extend,$node->auditor_id);
                break;
            case Enums::WF_NODE_BP_HEAD: //职能管理
                $auditors = $this->findFuncManagement($staffInfo->node_department_id, $node->auditor_level, $extend);
                break;
	        case Enums::WF_NODE_DEPARTMENT_JOB_TITLE_FORM: //获取指定部门职位下的人  auditor_id职位  auditor_level部门
		        $auditors = $this->findDepartmentJobStaff($node->auditor_level,$node->auditor_id);
		        break;
            case Enums::WF_NODE_JOB_TITLE_SAME_DEPT: //根据申请人部门，查找指定职位的审批人
                $auditors = $this->findDepartmentJobStaff($staffInfo->node_department_id, $node->auditor_level);
                break;
            case Enums::WF_NODE_JOB_TITLE_SAME_FIRST_DEPT: //根据申请人所在一级部门，查找指定职位的审批人
                $auditors = $this->findJobStaffBySubmitterFirstDepartment($staffInfo->sys_department_id, $node->auditor_level);
                break;
            case Enums::WF_NODE_ROLE:
                $auditors = $this->findRoles($node->auditor_level);
                break;
            case Enums::WF_NODE_DEPARTMENT_MANAGER_V3_BY_FORM: //根据FORM 获取指定部门负责人
                if(!isset($extend['from_submit']['department_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $node_department_id = $extend['from_submit']['department_id'];
                [$auditors, $nodeDeptId] = $this->findDepartmentManagerV3($node_department_id, $node->auditor_level);
                break;
            case Enums::WF_NODE_MANAGER_FROM: //根据表单 staff 找上级
                if(!isset($extend['from_submit']['staff_info_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $user = $extend['from_submit']['staff_info_id'];
                $auditors = $this->findSuperior($user, $node->auditor_level);
                break;
            case Enums::WF_NODE_STORE_MANAGER_FROM: //根据表单  找网点负责人
                if(!isset($extend['from_submit']['sys_store_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'];
                $auditors = ApprovalFinderServer::getInstance()->findHeadOfStore($sys_store_id, []);
                break;
            case Enums::WF_NODE_DM_BY_ORG_FROM: //根据表单 找组织架构去找DM
                if(!isset($extend['from_submit']['sys_store_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'];
                $auditors = $this->findDMEx($sys_store_id, $node->auditor_level,[]);
                break;
            case Enums::WF_NODE_AM_BY_ORG_FROM: //根据表单  找组织架构去找AM
                if(!isset($extend['from_submit']['sys_store_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'];
                $auditors = $this->findAMEx($sys_store_id, $node->auditor_level, []);
                break;
            case Enums::WF_NODE_HRBP_FROM: //根据表单  找组织架构去找AM
                if(!isset($extend['from_submit']['sys_store_id'], $extend['from_submit']['department_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'];//统一变量名
                $node_department_id = $extend['from_submit']['department_id'] ?? '';//统一变量名
                $auditors = $this->findHRBP($node_department_id, ['store_id'=>$sys_store_id]);
                break;
            case Enums::WF_NODE_HR_SERVICE_FROM: // 根据表单找部门对应的HR SERVICE
                if(!isset($extend['from_submit']['sys_store_id'], $extend['from_submit']['department_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'];//统一变量名
                $node_department_id = $extend['from_submit']['department_id'];//统一变量名
                $extend['store_id'] = $sys_store_id;
                $auditors = $this->findJurisdictionAreaStaffIds($node_department_id, $extend , 0,RolesModel::ROLE_HR_SERVICE);
                break;
            case Enums::WF_NODE_HUB_STANDARDIZATION: //分拨总部标准化部门下全部员工(For: 分拨外协加班、分拨外协补卡)
                $auditors = ApprovalFinderServer::getInstance()->findSpecDepartmentStaff($node->auditor_level);
                break;
            case Enums::WF_NODE_HUB_STORE_MANAGER: //分拨经理(For: 分拨外协补卡)
                if(!isset($extend['from_submit']['sys_store_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'];
                $auditors = ApprovalFinderServer::getInstance()->findHubStoreManager($sys_store_id);
                break;
            case Enums::WF_NODE_HUB_SUB_DEPARTMENT_BY_NAME: //分拨标准化or行政
                if(!isset($extend['from_submit']['sys_store_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'];
                $auditors = ApprovalFinderServer::getInstance()->findHubSubDepartmentStaff($sys_store_id);
                break;
            case Enums::WF_NODE_HASHTABLE_FROM: //根据表单找分拨大区经理
                if(!isset($extend['from_submit']['sys_store_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'];
                $auditType    = $extend['audit_type'];
                $auditors     = ApprovalFinderServer::getInstance()->findAreaManagerFromHashMap($auditType,
                    ApprovalFinderEnums::FINDER_BY_STORE, $sys_store_id);
                break;
            case Enums::WF_NODE_HUB_AREA_MANAGER:
                if(!isset($extend['from_submit']['sys_store_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'];
                $auditors     = ApprovalFinderServer::getInstance()->findHubAreaManagerByStoreId($sys_store_id);
                break;
            case Enums::WF_NODE_STORE_MANAGER_MUL_FROM: //根据表单找网点负责人(多个网点版本)
                if(!isset($extend['from_submit']['sys_store_id'][$node->auditor_level-1])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'][$node->auditor_level-1];
                $auditors = ApprovalFinderServer::getInstance()->findHeadOfStore($sys_store_id, ['']);
                break;
            case Enums::WF_NODE_DM_BY_ORG_MUL_FROM: //根据表单组织架构去找DM(多个网点版本)
                if(!isset($extend['from_submit']['sys_store_id'][$node->auditor_level-1])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'][$node->auditor_level-1];
                $auditors = $this->findDMEx($sys_store_id, $node->auditor_level,[]);
                break;
            case Enums::WF_NODE_AM_BY_ORG_MUL_FROM: //根据表单组织架构去找AM(多个网点版本)
                if(!isset($extend['from_submit']['sys_store_id'][$node->auditor_level-1])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'][$node->auditor_level-1];
                $auditors = $this->findAMEx($sys_store_id, $node->auditor_level, []);
                break;
            case Enums::WF_NODE_DEPARTMENT_MANAGER_MUL_BY_FORM: //根据FORM提交的部门查找申请人组织负责人(多部门) [1 ~ 4级部门负责人 or BU or Clevel]
                if(!isset($extend['from_submit']['department_id'][$node->auditor_id-1])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $node_department_id = $extend['from_submit']['department_id'][$node->auditor_id-1];
                [$auditors, $nodeDeptId] = $this->findDepartmentManagerV3($node_department_id, $node->auditor_level);
                break;
            case Enums::WF_NODE_HRBP_MUL_FROM: //根据表单找部门对应的HRBP(多个部门、网点版本)
                if(!isset($extend['from_submit']['sys_store_id'][$node->auditor_level-1], $extend['from_submit']['department_id'][$node->auditor_level-1])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'][$node->auditor_level-1];//统一变量名
                $node_department_id = $extend['from_submit']['department_id'][$node->auditor_level-1] ?? '';//统一变量名
                $auditors = $this->findHRBP($node_department_id, ['store_id' => $sys_store_id]);
                break;
            case Enums::WF_NODE_BU_C_LEVEL_FROM:   // 部门属于BU找 BU负责人 ； 如果部门属于cLevel找 clevel负责人； 否则找GroupCEO
                if(!isset($extend['from_submit']['department_id'][$node->auditor_level-1])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend));
                }
                $node_department_id = $extend['from_submit']['department_id'][$node->auditor_level-1] ?? '';//统一变量名
                [$auditors, $nodeDeptId] = $this->findFirstBuDepartmentManager($node_department_id, $extend);
                break;

            case Enums::WF_NODE_HUB_OT_CC_BY_STORE:   // hub 加班审批根据 网点id 找 指定抄送人。
                if(!isset($extend['from_submit']['sys_store_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend) .' sys_store_id :'.$extend['from_submit']['sys_store_id']??'');
                }
                $sys_store_id = $extend['from_submit']['sys_store_id'];
                $auditors = ApprovalFinderServer::getInstance()->getHubOtCCStaffByStoreId($sys_store_id);
                break;
            case Enums::WF_NODE_STAFF_SELF_FROM:
                if(!isset($extend['from_submit']['staff_info_id'])){
                    throw new \Exception('from_submit missing field auditor_type=> '.$node->auditor_type.' extend => '.json_encode($extend) .' staff_info_id :'.$extend['from_submit']['staff_info_id']??'');
                }
                $formStaffInfoId = $extend['from_submit']['staff_info_id'];
                $auditors = ApprovalFinderServer::getInstance()->getStaffSelf($formStaffInfoId);
                break;
            default:
                $auditors = [];
                break;
        }

        //获取不到节点，则找备选审批人
        if ($type == Enums::NODE_WORKFLOW_APPROVER && empty($auditors) && $node->approval_policy == Enums::WF_EMPTY_POLICY_DESIGNATE_OTHER) {
            $auditors = !empty($node->specify_approver) ? explode(',', $node->specify_approver) : '';
        }

        return [is_array($auditors) ? implode(',', $auditors) : (string)$auditors, $nodeDeptId];
    }

    /**
     * 找到审批流起始节点
     * @param $flowId
     * @param int $type         1-从固化节点中查找 2-从模板节点中查找
     * @return array|mixed
     */
    public function getStartNode($flowId, $type = 1, $version = '')
    {
        if ($type == 1) {
            return WorkflowNodeModel::FindFirst(
                [
                    'conditions' => 'flow_id = :flow_id: and type = :type: and deleted = 0',
                    'bind' => [
                        'flow_id' => $flowId,
                        'type'    => Enums::NODE_SUBMITTER,
                    ],
                ]
            );
        } else {
            return WorkflowNodeBaseModel::FindFirst(
                [
                    'conditions' => 'flow_id = :flow_id: and type = :type: and deleted = 0 AND version = :version:',
                    'bind' => [
                        'flow_id' => $flowId,
                        'type'    => Enums::NODE_SUBMITTER,
                        'version' => trim($version),
                    ],
                ]
            );
        }
    }

    /**
     * 工作流处理
     * @param object $request 申请信息
     * @param int $user 当前审批人
     * @param int $action 审批操作
     * @param array $info 影响审批流的参数
     * @param string $remark 申请、驳回原因
     * @param bool $auto
     * @return mixed
     * @throws InnerException
     * @throws \Exception
     */
    public function process(object $request, int $user, int $action, $info = [], $remark = null,$extend=[])
    {
        //自动通过标志位
        $approvalState   = 0;
        //抄送列表
        $ccList          = [];

        //[1]获取当前节点
        //[1.1]校验当前进行到的节点
        $flowNodes      = $this->getFlowNodes($request->getFlowId());
        $currentNode    = $this->pickupNode($flowNodes, $request->getCurrentFlowNodeId());
        if(empty($currentNode)){
            throw new InnerException('currentNode not found!', ErrCode::WORKFLOW_CAN_NOT_FIND_CURRENT_NODE);
        }
        if ($currentNode->getType() == Enums::NODE_FINAL
            && !in_array($action, [Enums::WF_ACTION_CANCEL, Enums::WF_ACTION_APPROVAL_CANCEL,Enums::WF_ACTION_EDIT_RECREATE,])) {
            throw new ValidationException('审批已经结束', ErrCode::WORKFLOW_APPROVAL_HAS_ENDED);
        }

        //[2]获取必要信息
        //[2.1]获取当前节点超时配置
        $isOvertimeNeedProcessNextNode = WorkflowOvertimeServer::getInstance($this->lang, $this->timezone)->isNeedProcessNextNode($request, $currentNode, $action);

	    //[2.2]换action
        if ($currentNode->getType() == Enums::NODE_COUNTERSIGN) { //当前节点类型是会签
            //获取当前的会签节点的审批状态，还存在多少个在职的人去审批
            $approvalState = $this->checkCounterSignApproval($request->getBizType(), $request->getBizValue(), $currentNode);

            //如果是审批同意，转换为会签同意
            if (in_array($action, [Enums::WF_ACTION_APPROVE, Enums::WF_ACTION_APPROVAL_EMPTY])) { //会签同意
                $action = Enums::WF_ACTION_APPROVE_COUNTERSIGN;
            } else if ($action == Enums::WF_ACTION_TIMEOUT && $isOvertimeNeedProcessNextNode) { //会签超时同意
                $action = Enums::WF_ACTION_OVERTIME_AUTO_APPROVAL_COUNTERSIGN;
            }
        } else if ($action == Enums::WF_ACTION_TIMEOUT && $isOvertimeNeedProcessNextNode) { //超时 & 超时自动通过
            $action = Enums::WF_ACTION_OVERTIME_AUTO_APPROVAL;
        }

        //[2.3]获取下一个节点
        //不找下一个节点的case
        //1-撤销、驳回操作
        //2-会签时，当前节点还存在多于1个人在职的人审批
        //3-超时操作并且超时策略不为“自动通过”
        //4-当前节点为最终节点
        if ($currentNode->getType() == Enums::NODE_FINAL
            || in_array($action, [Enums::WF_ACTION_REJECT, Enums::WF_ACTION_CANCEL])
            || $action == Enums::WF_ACTION_TIMEOUT && !$isOvertimeNeedProcessNextNode
            || ($currentNode->getType() == Enums::NODE_COUNTERSIGN && $approvalState > 1)
        ) {
            $nextNode = $currentNode;
        } else {
            $nextNodeId = $this->findNextNode($request->getFlowId(), $request->getCurrentFlowNodeId(), $info);
            $nextNode   = $this->pickupNode($flowNodes, $nextNodeId);

            //如果当前节点是抄送节点，则继续寻找，直到找到一个审批节点
            if ($nextNode->getType() == Enums::NODE_CC) {
                //直到找到一个审批节点
                $currentNodeId = $nextNode->getId();
                do {
                    $ccList[]      = [
                        'request_id'    => $request->getId(),
                        'flow_id'       => $request->getFlowId(),
                        'flow_node_id'  => $currentNodeId,
                        'auditor_id'    => $nextNode->getAuditorId(),
                        'auditor_level' => $nextNode->getAuditorLevel(),
                        'auditor_type'  => $nextNode->getAuditorType(),
                    ];
                    $nextNodeId    = $this->findNextNode($request->getFlowId(), $currentNodeId, $info);
                    $nextNode      = $this->pickupNode($flowNodes, $nextNodeId);
                    $currentNodeId = $nextNode->getId();
                } while ($nextNode->getType() == Enums::NODE_CC);
            }
        }
        if(empty($nextNode)) {
            throw new InnerException('next node not found!', ErrCode::WORKFLOW_CAN_NOT_FIND_NEXT_NODE);
        }
        $WorkflowNodeAccidentBusinessModel = new WorkflowNodeAccidentBusinessModel();

        //[3]应对[审批同意]操作过程中，下一级审批人为空或者重复的情况
	    //int $autoProcessType 自动审批通过通类型
	    //array $approvals     下一级审批人
	    //array $superiorIds   不在职的上级 (会插入转交 log)
        [$autoProcessType, $approvals, $superiorIds, $auditInfo, $auditInfoHandOver] = $this->processAutoAuditV2($request, $currentNode, $nextNode, $action, $user);

        //[4]重新计算超时日期
        //这里仅计算正常审批的超时时间，超时转交的超时时间是在超时Action里面计算
        if ($action != Enums::WF_ACTION_TIMEOUT || $isOvertimeNeedProcessNextNode) {
            $timeOutDate = WorkflowOvertimeServer::getInstance($this->lang, $this->timezone)->getOvertimeDate($request, $currentNode, $nextNode, $action);
            //if (!is_null($timeOutDate)) { //当超时配置为 3=设置单一节点超时时间 4=单独设置每个节点超时时间时，需要更新超时时间
            //    $request->setTimeOut($timeOutDate);
            //}
            $this->logger->write_log('process==>setTimeOut date:' . $timeOutDate, 'info');
            $request->setTimeOut($timeOutDate);
        }
        $request->setCurrentFlowNodeId($nextNode->getId());

        //如果各个国家实现了[ApprovalServer]
        //然后根据其[method][getInstance]方法找对应的业务server
        $server = new ApprovalServer($this->lang, $this->timezone);

        //延时审批，即审批人同意后，下一级审批人不会马上收到待审批，会在一定时间之后才收到
        //各个审批通过实现 "checkDelay"方法，返回true/false 来告知是否存在延时审批
        $isApprovalNeedDelayAudit = $this->checkDelayApproval($request, $action);

        //审批回调参数
        $localFinalApprovalTime = date('Y-m-d H:i:s'); //本地时区
        $auditCallbackParams = [
            'approval'                => $approvals,
            'staff_id'                => $user,
            'is_cancel'               => $request->getIsCancel(),
            'is_edit'                 => $request->getIsEdit(),
            'is_start_node'           => $currentNode->getType() == Enums::NODE_SUBMITTER,
            'next_node_id'            => $nextNode->getId(),
            'super'                   => $extend['super'] ?? 0,
            'remark'                  => $remark,
            'final_approval_time_loc' => $localFinalApprovalTime,
            'final_approval_time_utc' => gmdate('Y-m-d H:i:s', strtotime($localFinalApprovalTime)),//0时区
        ];

        switch ($action) {
            case Enums::WF_ACTION_CREATE:               //创建
            case Enums::WF_ACTION_CREATE_CANCEL:        //创建撤销申请
            case Enums::WF_ACTION_EDIT_RECREATE:        //创建修改申请
            case Enums::WF_ACTION_APPROVE:              //或签同意
            case Enums::WF_ACTION_APPROVAL_EMPTY:       //审批人为空，自动同意
            case Enums::WF_ACTION_FORCE_APPROVAL_PASS:  //系统强制同意 与审批人为空含义
            case Enums::WF_ACTION_CREATE_WITHOUT_LOG:   //转岗专用
            case Enums::WF_ACTION_OVERTIME_AUTO_APPROVAL:
            case Enums::WF_ACTION_SYS_APPROVAL_PASS:
                //待审批的变成已同意
                $this->processState($request, Enums::APPROVAL_STATUS_APPROVAL,$user);

                if ($nextNode->getType() == Enums::NODE_FINAL) { //最终审批

                    //如果存在多阶段审批，那么在前一个阶段完成后，申请人列表审批状态并不设置为已审批
                    $workflow = $this->getWorkflowInfo($request->getWorkflowId());
                    if (empty($workflow) || $workflow->next_stage_flow_id == WorkflowModel::WORKFLOW_NEXT_STAGE_FLOW_ID) {
                        $request->setState(Enums::APPROVAL_STATUS_APPROVAL);
                        $request->setFinalApprovalTime($localFinalApprovalTime);
                        $request->setFinalApprover($user);
                    }
                    //如果是撤销申请或者修改申请 apply 记录应该是 原状态 审核通过
                    if($request->getIsCancel() > 0) {
                        $request->setState(Enums::APPROVAL_STATUS_CANCEL);
                    }

                    //最终审批同意
                    $server->setAuditProperty($request->getBizValue(), $request->getBizType(),
                        Enums::APPROVAL_STATUS_APPROVAL, $auditCallbackParams);

                    //处理旧列表数据
                    //$this->processUnionFinalState($request, Enums::$audit_list_status['approved']);
                } else {
                    //追加新的节点
                    $this->processApproval($request, $approvals, $isApprovalNeedDelayAudit);

                    //非最终审批同意
                    $server->setAuditProperty($request->getBizValue(), $request->getBizType(),
                        Enums::APPROVAL_STATUS_APPROVAL, $auditCallbackParams, false);

                    //更新审批状态为已同意
                    //添加下一级审批人
                    //$this->processUnionState($request, Enums::$audit_list_status['approved_approval'], $approvals);
                }

                //当前节点是否存在抄送逻辑,也就是审核通过之后需要抄送的人
                $WorkflowNodeAccidentBusinessModel->saveCcStatus($currentNode->getId(), $action);

                if ($currentNode->getNodeMark() == WorkflowNodeModel::NODE_MARK_AUDIT) { //标记审核
                    $action = Enums::WF_ACTION_AUDIT_APPROVE;
                }
                if ($action == enums::WF_ACTION_OVERTIME_AUTO_APPROVAL) {
                    $remark = 'err_msg_wf_process_approval';
                }

                break;
            case Enums::WF_ACTION_APPROVE_COUNTERSIGN: //会签同意
            case Enums::WF_ACTION_OVERTIME_AUTO_APPROVAL_COUNTERSIGN: //会签超时同意
                if ($nextNode->getType() == Enums::NODE_FINAL) { //最终同意
                    $request->setState(Enums::APPROVAL_STATUS_APPROVAL);
                    $request->setFinalApprovalTime($localFinalApprovalTime);
                    $request->setFinalApprover($user);

                    //如果是撤销申请或者修改申请 apply 记录应该是 原状态 审核通过
                    if($request->getIsCancel() > 0) {
                        $request->setState(Enums::APPROVAL_STATUS_CANCEL);
                    }

                    //待审批的变成已同意
                    $this->processState($request, Enums::APPROVAL_STATUS_APPROVAL,$user);

                    //最终审批同意
                    $server->setAuditProperty($request->getBizValue(), $request->getBizType(), Enums::APPROVAL_STATUS_APPROVAL,
                        $auditCallbackParams, true);

                    //处理旧列表数据
                    //$this->processUnionFinalState($request, Enums::$audit_list_status['approved']);

                    //下一个节点是否存在抄送逻辑,也就是下个节点审核之前需要抄送的人
                    $WorkflowNodeAccidentBusinessModel->saveCcStatus($currentNode->getId(), $action);

                } else if ($approvalState > 1) { //还存在多于1个人会签
                    //指定审批人变成已同意
                    $this->processSingleApprovalState($request, $user,Enums::APPROVAL_STATUS_APPROVAL);

                    //$this->processUnionSingleApprovalState($request, [$user],Enums::$audit_list_status['approved_approval']);
                } else {

                    //待审批的变成已同意
                    $this->processState($request, Enums::APPROVAL_STATUS_APPROVAL,$user);

                    //追加新的节点
                    $this->processApproval($request, $approvals, $isApprovalNeedDelayAudit);

                    //最终审批同意
                    $server->setAuditProperty($request->getBizValue(), $request->getBizType(),Enums::APPROVAL_STATUS_APPROVAL, $auditCallbackParams, false);

                    //处理旧列表数据
                    //$this->processUnionState($request, Enums::$audit_list_status['approved_approval'], $approvals);

                    //下一个节点是否存在抄送逻辑,也就是下个节点审核之前需要抄送的人
                    $WorkflowNodeAccidentBusinessModel->saveCcStatus($currentNode->getId(), $action);
                }

                if ($currentNode->getNodeMark() == WorkflowNodeModel::NODE_MARK_AUDIT) { //标记审核
                    $action = Enums::WF_ACTION_AUDIT_APPROVE;
                }
                if ($action == enums::WF_ACTION_OVERTIME_AUTO_APPROVAL_COUNTERSIGN) {
                    $remark = 'err_msg_wf_process_approval';
                }
                break;
            case Enums::WF_ACTION_REJECT: //或签驳回
            case Enums::WF_ACTION_APPROVAL_EMPTY_AUTO_REJECT: //审批人为空，系统自动驳回
                $request->setState(Enums::APPROVAL_STATUS_REJECTED);
                $request->setRejectReason($remark);
                $request->setFinalApprovalTime($localFinalApprovalTime);
                $request->setFinalApprover($user);
                $server->setAuditProperty($request->getBizValue(), $request->getBizType(),Enums::APPROVAL_STATUS_REJECTED, $auditCallbackParams);

                //如果是撤销申请或者修改申请 apply 记录应该是 原状态 审核通过
                if($request->getIsCancel() > 0 || $request->getIsEdit() > 0) {
                    $request->setState(Enums::APPROVAL_STATUS_APPROVAL);
                }
                //全部变成已驳回
                $this->processState($request, Enums::APPROVAL_STATUS_REJECTED,$user);

                //处理旧列表数据
                //$this->processUnionFinalState($request, Enums::$audit_list_status['dismissed']);

                //下一个节点是否存在抄送逻辑
                $WorkflowNodeAccidentBusinessModel->saveCcStatus($currentNode->getId(), $action);

                if ($currentNode->getNodeMark() == WorkflowNodeModel::NODE_MARK_AUDIT) { //标记审核
                    $action = Enums::WF_ACTION_AUDIT_REJECT;
                }

                break;
            case Enums::WF_ACTION_REJECT_ROLL_BACK:
                $action = Enums::WF_ACTION_REJECT;
                //添加驳回原因
                $request->setRejectReason($remark);

                //待审批变成已驳回
                $this->processPendingStateToState($request, Enums::APPROVAL_STATUS_REJECTED);

                //追加新的节点
                $this->processApproval($request, $approvals, $isApprovalNeedDelayAudit);

                //非最终审批驳回
                $server->setAuditProperty($request->getBizValue(), $request->getBizType(),Enums::APPROVAL_STATUS_REJECTED, $auditCallbackParams, false);

                //更新审批状态为已驳回
                //添加下一级审批人
                //$this->processUnionState($request, Enums::$audit_list_status['dismissed'], $approvals);

                //下一个节点是否存在抄送逻辑
                $WorkflowNodeAccidentBusinessModel->saveCcStatus($currentNode->getId(), $action);

                if ($currentNode->getNodeMark() == WorkflowNodeModel::NODE_MARK_AUDIT) { //标记审核
                    $action = Enums::WF_ACTION_AUDIT_REJECT;
                }
                break;
            case Enums::WF_ACTION_CANCEL: //或签撤销
            case Enums::WF_ACTION_APPROVAL_CANCEL:
                $action = Enums::WF_ACTION_CANCEL;
                $request->setState(Enums::APPROVAL_STATUS_CANCEL);
                $request->setCancelReason($remark);
                $request->setFinalApprovalTime($localFinalApprovalTime);
                $request->setFinalApprover($user);
                $server->setAuditProperty($request->getBizValue(), $request->getBizType(), Enums::APPROVAL_STATUS_CANCEL, $auditCallbackParams);

                //全部变成已撤销
                $this->processState($request, Enums::APPROVAL_STATUS_CANCEL, $user);
                break;
            case Enums::WF_ACTION_TIMEOUT: //超时
                //处理超时
                [$action, $remark, $user] = $this->processTimeOut($request, $auditCallbackParams, $currentNode, $nextNode);
                //如果是撤销申请或者修改申请 apply 记录应该是 原状态 审核通过
                if($request->getIsCancel() > 0 || $request->getIsEdit() > 0){
                    $request->setState(Enums::APPROVAL_STATUS_APPROVAL);
                }
                break;
            default:
                break;
        }

        //超时关闭不再记录日志，改为根据最后节点查询审批人
        //并展示审批人超时关闭
        if (in_array($currentNode->getType(), [Enums::NODE_SUBMITTER, Enums::NODE_APPROVER, Enums::NODE_COUNTERSIGN, Enums::NODE_FINAL])
        ){
            //审核日志记录
            $this->saveAuditLog($request, $user, $action, $remark);

            //如存在转交人
            if(isset($superiorIds) && $superiorIds) {
                //更新节点表中的审批人（即：转交人）
                $nextNode->setAuditorId(implode(',', $approvals));
                $nextNode->save();
                foreach ($superiorIds as $superiorId) {
                    //追加转交日志
                    $this->saveAuditLog($request, $superiorId, enums::WF_ACTION_APPROVAL_SUPERIOR, $auditInfoHandOver ?? null);
                }
            }

            //发送审批push
            $pushParams = [
                'request'                      => $request,
                'action'                       => $action,
                'is_approval_need_delay_audit' => $isApprovalNeedDelayAudit,
                'user'                         => $user,
                'next_node'                    => $nextNode,
                'current_node'                 => $currentNode,
                'approvals'                    => $approvals,
            ];
            $this->sendAuditPush($pushParams);
        }
        $request->save();

        //[2.4]处理抄送节点
        if (!empty($ccList)) {
            $staffRepository = new StaffRepository();
            foreach ($ccList as $ccNode) {
                //当前节点是抄送节点
                $ccStaffInfo = isset($ccNode['auditor_id']) && $ccNode['auditor_id'] ? explode(',', $ccNode['auditor_id']) : '';
                if (empty($ccStaffInfo)) {
                    continue;
                }

                //处理抄送节点异步抄送、追加抄送日志
                $request_data = $this->packCcStaffData($ccStaffInfo, $ccNode, $request);
                if (!empty($request_data)) {
                    $staffRepository->batch_insert('workflow_node_accident_business', $request_data);

                    //获取有效的抄送人
                    $ccStaffInfoList = array_column($request_data, 'auditor_id');

                    //抄送日志记录
                    foreach ($ccStaffInfoList as $staffId) {
                        $this->saveAuditLog($ccNode, $staffId, enums::WF_ACTION_CC, $remark);
                    }
                }
            }
        }

        //存在自动审批/审批人重复/转交等情况
        if ($autoProcessType) {
            //如果是审批人重复则使用当前节点的工号
            //否则是系统默认工号

            //[1]审批人为空或离职,系统处理为通过/驳回
            if ($autoProcessType == Enums::WF_AUTO_PROCESS_TYPE_4) {
                $staffApp = isset($approvals) && $approvals ? current($approvals) : Enums::SYSTEM_STAFF_ID;
            } else if ($autoProcessType == Enums::WF_AUTO_PROCESS_TYPE_2) {
                $staffApp = $user;
            } else {
                $staffApp = Enums::SYSTEM_STAFF_ID;
            }

            //判断是否审批人等于申请人  则申请人通过
	        $staffApp = $autoProcessType == Enums::WF_AUTO_PROCESS_TYPE_3 ? $request->getSubmitterId() : $staffApp;
            if ($autoProcessType == Enums::WF_AUTO_PROCESS_TYPE_5) {

                //追加驳回原因
                $autoRejectReason = $this->getAutoRejectReason($nextNode);

                $this->process($request, $staffApp, Enums::WF_ACTION_APPROVAL_EMPTY_AUTO_REJECT, $info, $autoRejectReason);
            } else if ($autoProcessType == Enums::WF_AUTO_PROCESS_TYPE_6) {

                if ($nextNode->getType() != Enums::NODE_FINAL) {
                    $this->process($request, $staffApp, Enums::WF_ACTION_FORCE_APPROVAL_PASS, $info, $auditInfo);
                }
            } else if ($autoProcessType == Enums::WF_AUTO_PROCESS_TYPE_7) {

                if ($nextNode->getType() != Enums::NODE_FINAL) {
                    $this->process($request, $staffApp, Enums::WF_ACTION_SYS_APPROVAL_PASS, $info, $auditInfo);
                }
            } else {
                $this->process($request, $staffApp, Enums::WF_ACTION_APPROVAL_EMPTY, $info, $auditInfo);
            }
        }
        return $request;
    }


	/**
	 * @description: 判断是否为审批人等于申请人  并且只有申请人自己审批 自动通过
	 *
	 * @param array $approve_staff 审批人
	 * @param int $submitter_staff 申请人
	 * @param int $action 操作类型
	 * @param int $biz_type 关卡类型
	 * @param int $autoProcessType 是否自动通过
	 *
	 * @return     : int $autoProcessType
	 * <AUTHOR> L.J
	 * @time       : 2021/11/2 13:47
	 */
	public function ApproveToApplicant($approve_staff, $submitter_staff, $action, $biz_type, $autoProcessType)
    {
		//[3.4]  审批人==申请人 并且流程设置了自动审批通过  并且 审批人只有 申请人自己
		if(is_array($approve_staff) && count($approve_staff) == 1 && in_array($submitter_staff,$approve_staff) && in_array($action, [Enums::WF_ACTION_CREATE, Enums::WF_ACTION_APPROVE,Enums::WF_ACTION_APPROVAL_EMPTY])){
			//查询审批流
            $flowBase = WorkflowModel::findFirst([
                'conditions' => "type = 1 and relate_type = :type: and is_view = :is_view:",
                'bind'       => [
                    'type'    => $biz_type,
                    'is_view' => WorkflowModel::WORKFLOW_VISUALIZE,
                ],
            ]);
			if($flowBase) {
                //版本号
                $version = $this->getConfigureVersion($flowBase);

				//查询审批流是否配置了 自动审批通过
                $workflowBasicConfigModel = WorkflowConfigServer::getInstance($this->lang, $this->timezone)
                    ->getSpecConfigType($version, $flowBase->id);

				//存在数据则自动通过
				if(!empty($workflowBasicConfigModel) && $workflowBasicConfigModel->configure_value == 1){
					$autoProcessType = Enums::WF_AUTO_PROCESS_TYPE_3; //自动通过

                    $msg = 'err_msg_wf_approver_eq_submitter';
				}
			}
		}
		return [$autoProcessType, $msg ?? ""];
	}

    /**
     * 获取配置版本
     * @param $flowBase
     * @return string
     */
    public function getConfigureVersion($flowBase): string
    {
        return isset($flowBase->version) && $flowBase->version ? $flowBase->version: 'default';
    }

	/**
     * 发送推送消息
     * @param $send_to
     * @param $request
     */
    public function sendPush($send_to, $request)
    {
        if (in_array($request->getBizType(), AuditListEnums::getAuditTypeWithOutPush())) {
            return;
        }

        //获取收信人语言环境
        $model = new StaffAccountModel();
        $lang = $model->getAcceptLanguage($send_to);

        //异步push消息=>审批人通知
        $t              = $this->getTranslation($lang);
        $message_title  = $t->_('6006');
        $msg_1          = $t->_('6001');
        $msg_2          = $t->_('6002');
        $auditType      = $t->_(Enums::$at[$request->getBizType()]);
        $staffData      = (new StaffRepository())->checkoutStaff($request->getSubmitterId());

        //push标题： Backyard 消息提醒
        //push内容： xxxx (工号) 提交了 (xxxx类型申请) 请你审批
        $pushParam = [
            'staff_info_id' => $send_to,
            'message_title' => $message_title,
            'message_content' => sprintf("%s(%s) %s {%s} %s", $staffData['name'],
                $staffData['id'],
                $msg_1,
                $auditType,
                $msg_2),
        ];
        (new PublicRepository())->pushMessage($pushParam);
    }

    /**
     * 上级审批，上级不在职 取上级的上级直到取到在职的上级审批
     *
     * @param $request
     * @param $auditId
     * @param array $superiorIds
     * @return array
     */
    public function hightManageAudit($request, $auditId, array $superiorIds = []): array
    {
        $superiorIds[] = $auditId;
        $auditId = $this->staffRepository->getHightManager($auditId);
        if ($auditId && !in_array($auditId, $superiorIds)) {
            if (!$this->staffRepository->isStaffStateOnJob($auditId)) { //不在职
                return $this->hightManageAudit($request, $auditId, $superiorIds);
            }
            return [$auditId, $superiorIds];
        }

        return ['', $superiorIds];
    }

    /**
     * 上级部门负责人审批
     * 如果上级部门负责人不在职，则取上级的上级部门负责人
     * 直到取到在职的上级部门负责人审批
     *
     * @param $request
     * @param $auditId
     * @param array $superiorIds
     * @return array
     */
    public function hightDeptManageAudit($deptId, array $superiorIds = []): array
    {
        $managerIds = $superiorIds;
        [$auditorId, $ancestry] = $this->staffRepository->getHigherDeptManager($deptId);
        if ($auditorId && !in_array($auditorId, $managerIds)) {

            if (!$this->staffRepository->isOnJob($auditorId)) { //不在职
                $managerIds[] = $auditorId;
                return $this->hightDeptManageAudit($ancestry, $managerIds);
            }
            return [$auditorId, $ancestry, $superiorIds];
        }
        return ['', $deptId, $superiorIds];
    }

    /**
     * 获取审批流全部节点
     * @param $flowId
     * @param int $type     1-固化的审批节点 2-模板审批节点
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getFlowNodes($flowId, $type = 1, $version = '', $is_cancel = false)
    {
        if ($type == 1) {
            $bind = [
                'flow_id' => $flowId,
            ];
            $conditions = "flow_id = :flow_id:";
            return WorkflowNodeModel::find(
                [
                    'conditions' => $conditions,
                    'bind'       => $bind,
                ]
            );
        } else {
            return WorkflowNodeBaseModel::find(
                [
                    'conditions' => 'flow_id = :flow_id: and deleted = 0 AND version = :version:',
                    'bind' => [
                        'flow_id' => $flowId,
                        'version' => trim($version),
                    ],
                ]
            );
        }
    }

    /** 获取当前审核节点
     * @param $nodes
     * @param $currentNodeId
     * @return mixed
     */
    public function pickupNode($nodes, $currentNodeId)
    {
        foreach ($nodes as $k => $v){
            if ($v->id == $currentNodeId)
                return $v;
        }
        return '';
    }

    /**
     * 获取下一个节点
     * @param $flowId
     * @param $nodeId
     * @param $info
     * @return mixed
     * @throws InnerException
     */
    public function findNextNode($flowId, $nodeId, $info)
    {
        $workflowPersistServer = WorkflowPersistentServer::getInstance($this->lang, $this->timezone);
        $lines = $workflowPersistServer->getFlowNodeRelate($flowId, $nodeId, 1)->toArray();
        if (empty($lines)) { //无下一个节点了
            return  [];
        }
        //注册stream_wrapper
        registerStream();
        $class  = new WorkflowFormula();
        $nodeId = null;
        foreach ($lines as $v) {

            if (empty($v['valuate_code']) || empty($v['valuate_formula'])) { //无有效条件、参数
                return $v['to_node_id'];
            }

            $params  = [];
            $methods = explode(',', $v['valuate_code']);
            foreach ($methods as $k => $method) {
                $method = trim($method);
                if (!method_exists($class, $method)){
                    throw new InnerException(sprintf("Handler Method %s not exists", $method), ErrCode::WORKFLOW_VALUATE_FORMULA_METHOD_NOT_EXIST);
                }
                $key = 'p'. ($k + 1);
                $params[$key] = $class->$method($info);
            }

            //当前关系的全部
            $formula = $v['valuate_formula'] ?? '';
            $result = include 'var://<?php extract($params); return '. $formula .';';
            if ($result === true) {
                $nodeId = $v['to_node_id'];
                break;
            }
        }

        if (empty($nodeId)) {
            throw new InnerException("Invalid node ID, current node = {$nodeId} , params = ". json_encode($info), ErrCode::WORKFLOW_VALUATE_FORMULA_NO_VALID_ROUTE);
        }

        return $nodeId;
    }

    /**
     * 是否当前节点为最终审批节点
     * @param $request
     * @param $user
     * @param int $node_id 当前节点ID
     * @return bool
     * @throws InnerException
     * @throws ValidationException
     */
    public function isFinalNode($request, $user, $node_id = null): bool
    {
        if (!($request instanceof AuditApplyModel)) {
            return false;
        }

        $approvalServer = new ApprovalServer($this->lang, $this->timezone);
        $auditParameters = $approvalServer->getWorkflowParams($request->getBizValue(), $request->getBizType(), $user, enums::WF_ACTION_APPROVE);
        if (empty($auditParameters)) {
            return false;
        }

        //获取下一个节点信息
        if (!empty($node_id)) {
            $nextNodeId = $this->findNextNode($request->getFlowId(), $node_id, $auditParameters);
        } else {
            $nextNodeId = $this->findNextNode($request->getFlowId(), $request->getCurrentFlowNodeId(), $auditParameters);
        }

        $flowNodes      = $this->getFlowNodes($request->getFlowId());
        $nextNode       = $this->pickupNode($flowNodes, $nextNodeId);

        if (empty($nextNode)) {
            return false;
        }

        if ($nextNode->getType() == enums::NODE_CC) {
            do {
                $nextNodeId = $this->findNextNode($request->getFlowId(), $nextNodeId, $auditParameters);
                $nextNode   = $this->pickupNode($flowNodes, $nextNodeId);
            } while ($nextNode->getType() == enums::NODE_CC);

            if (empty($nextNode)) {
                return false;
            }
        }

        return $nextNode->getType() == enums::NODE_FINAL;
    }

    /**
     * 获取节点审核人ID
     * @param $node
     * @return array
     */
    public function getNodeStaffIds($node) : array
    {
        $auditors = empty($node->auditor_id) ? []: explode(',', $node->auditor_id);
        return is_array($auditors) ? $auditors : (!empty($auditors) ? (array)$auditors : []);
    }

    /**
     * 调整审批状态
     * @param $request
     * @param $state
     * @param int $approval
     */
    public function processState($request, $state, $approval = 0)
    {
        $this->processDuplicatedApproval($request, [$approval]);

        $condition = "biz_value = :value: and biz_type = :type: and is_cancel=:is_cancel: and is_edit=:is_edit: and state = :state: and deleted = 0 ";
        $data = AuditApprovalModel::find([
            'conditions' => $condition,
            'bind' => [
                'type'  => $request->biz_type,
                'value' => $request->biz_value,
                'is_cancel' => $request->is_cancel,
                'is_edit' => $request->is_edit,
                'state' => Enums::APPROVAL_STATUS_PENDING, //处理待审批的
            ],
        ]);
        //全部待审批变更为已审批
        $data->update(['state' => $state,'audit_time'=>gmdate("Y-m-d H:i:s")]);
    }

    /**
     * 调整审批状态
     * @param $request
     * @param $state
     */
    public function processPendingStateToState($request, $state)
    {
        $data = AuditApprovalModel::find([
            'conditions' => "biz_value = :value: and biz_type = :type: and state = 1 and deleted = 0",
            'bind' => [
                'type'  => $request->biz_type,
                'value' => $request->biz_value,
            ],
        ]);
        $data->update(['state' => $state,'audit_time'=>gmdate("Y-m-d H:i:s")]);
    }

    /**
     * 调整审批状态
     * @param $request
     * @param $approval
     * @param $state
     */
    public function processSingleApprovalState($request, $approval, $state)
    {
        $this->processDuplicatedApproval($request,[$approval]);
        $data = AuditApprovalModel::find([
            'conditions' => "biz_value = :value: and biz_type = :type: and flow_node_id = :flow_node_id: and approval_id = :approval_id: and deleted = 0",
            'bind' => [
                'type'  => $request->getBizType(),
                'value' => $request->getBizValue(),
                'flow_node_id' => $request->getCurrentFlowNodeId(),
                'approval_id' => $approval,
            ],
        ]);
        $data->update(['state' => $state,'audit_time'=>gmdate("Y-m-d H:i:s")]);
    }

    /**
     * @param $request
     * @param $approvalIds
     * @return void
     */
    private function processDuplicatedApproval($request, $approvalIds)
    {
        if (!empty($approvalIds)) {
            //将待审批人中，存在已经审批的人找出来软删除
            $dataNeedToSoftDelete = AuditApprovalModel::find([
                'conditions' => "biz_value = :value: and biz_type = :type: and is_cancel=:is_cancel: and is_edit=:is_edit: and approval_id in({approval_id:array}) and deleted = 0 and state IN(2,3,4,5)",
                'bind' => [
                    'approval_id' => $approvalIds,
                    'type'        => $request->getBizType(),
                    'value'       => $request->getBizValue(),
                    'is_cancel' => $request->getIsCancel(),
                    'is_edit' => $request->getIsEdit(),
                ],
            ]);
            $dataNeedToSoftDelete->update(['deleted' => enums::DATA_DELETED]);
        }
    }

    /**
     * 保存申请的审批日志
     * @param $request
     * @param $user
     * @param $action
     * @param $remark
     * @return void
     */
    public function saveAuditLog($request, $user, $action, $remark)
    {
        if (in_array($action, [enums::WF_ACTION_CREATE_WITHOUT_LOG])){
            return;
        }
        $staffRepository = new StaffRepository();
        $t               = $this->getTranslation();
        // 保持节点与相应人的对应关系
        if ($request instanceof AuditApplyModel) {
            $curRequest = AuditApplyModel::findFirst($request->getId());
            $requestId  = $request->getId();
            $flowId     = $request->getFlowId();
            $flowNodeId = !empty($curRequest) ? $curRequest->getCurrentFlowNodeId() : "";
        } else {
            $requestId  = $request['request_id'];
            $flowId     = $request['flow_id'];
            $flowNodeId = $request['flow_node_id'];
        }

        if (is_array($user) && !empty($user)) { //只有超时会是数组

            //获取员工详细详情
            $staffInfo = $staffRepository->getSpecifyStaffInfo($user, 'hsi.job_title_grade_v2 DESC');
            foreach ($staffInfo as $staff) {
                $log = new AuditLogModel();
                $log->save(
                    [
                        'request_id'           => $requestId,
                        'flow_id'              => $flowId,
                        'flow_node_id'         => $flowNodeId,
                        'staff_id'             => $staff['staff_info_id'] ?? '',
                        'staff_name'           => $staff['staff_name'] ?? '',
                        'staff_department'     => $staff['department_name'] ?? '',
                        'staff_job_title_name' => $staff['job_name'] ?? '',
                        'audit_action'         => $action,
                        'audit_info'           => $remark,
                        'audit_at'             => date('Y-m-d H:i:s'),
                    ]
                );
            }
        } else {

            //获取员工详细详情
            $staffDetailInfo = $staffRepository->getSpecifyStaffInfo([$user]);
            $staffInfo       = current($staffDetailInfo);
            if ($staffInfo) {
                $staff_id   = $staffInfo['staff_info_id'];
                $staff_name = $staffInfo['staff_name'] ?? '';
            } else {
                $staff_id   = enums::SYSTEM_STAFF_ID;
                $staff_name = $t->_('system');
            }

            $log = new AuditLogModel();
            $log->save(
                [
                    'request_id'           => $requestId,
                    'flow_id'              => $flowId,
                    'flow_node_id'         => $flowNodeId,
                    'staff_id'             => $staff_id,
                    'staff_name'           => $staff_name ?? '',
                    'staff_department'     => $staffInfo['department_name'] ?? '',
                    'staff_job_title_name' => $staffInfo['job_name'] ?? '',
                    'audit_action'         => $action,
                    'audit_info'           => $remark,
                    'audit_at'             => date('Y-m-d H:i:s'),
                ]
            );
        }
    }

    /**
     * @description 更新log
     * @param $request
     * @param $user
     * @param $action
     * @param $remark
     */
    public function updateAuditLog($request, $user, $action, $remark)
    {
        $log = AuditLogModel::findFirst([
            'conditions' => 'flow_id = :flow_id: and audit_action = :action:',
            'bind' => [
                'flow_id' => $request->flow_id,
                'action' => enums::WF_ACTION_CONFIRM_PENDING,
            ],
        ]);
        if (empty($log)) {
            return;
        }
        $log->audit_action = $action;
        $log->audit_at = date('Y-m-d H:i:s');
        $log->save();
    }

    /**
     * @description 删除指定action的log
     * @param $request
     * @param $action
     */
    public function removeAuditLog($request, $action)
    {
        $log = AuditLogModel::findFirst([
            'conditions' => 'flow_id = :flow_id: and audit_action = :action:',
            'bind' => [
                'flow_id' => $request->flow_id,
                'action'  => $action,
            ],
        ]);
        if (empty($log)) {
            return;
        }
        $log->delete();
    }

    /**
     * 指定N级上级
     * @param $user
     * @param $level
     * @return string
     */
    public function findSuperior($user, $level)
    {

        if (empty($user)) {
            return '';
        } else {
            $staffInfo = HrStaffItemsModel::findFirst([
                'conditions' => "staff_info_id = ?1 and item = 'MANGER' ",
                'bind' => [
                    1 => $user,
                ],
            ]);
        }

        if ($level == 1) {
            return $staffInfo && $staffInfo->value ? $staffInfo->value : '';
        } else {
            if ($staffInfo) {
                return $this->findSuperior($staffInfo->value, $level - 1);
            } else {
                return '';
            }
        }
    }

    /**
     * 指定N级部门负责人
     * @param $departmentId
     * @param $level
     * @param $extend
     * @return array
     */
    public function findDepartmentManager($departmentId, $level, $extend)
    {
        //[1]如果存在指定部门，则获取指定部门的负责人
        $extendDepartmentId = $extend['department_id'] ?? '';
        if (!empty($extendDepartmentId)) {
            $departmentId = $extendDepartmentId;
        }
        if (empty($departmentId)) {
            return ['', ''];
        }

        //[2]获取申请人当前部门信息
        $departmentInfo = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1 and deleted = 0',
            'bind' => [
                1 => $departmentId,
            ],
        ]);
        if (!(isset($departmentInfo->ancestry_v3) && $departmentInfo->ancestry_v3)) {
            return ['', ''];
        }

        //[2]获取部门链
        $depmentList = explode('/', $departmentInfo->ancestry_v3);

        //[3]查询指定级别
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.id,s.manager_id,s.level');
        $builder->from(['s' => SysDepartmentModel::class]);
        $builder->inWhere('s.id', $depmentList);
        $builder->andWhere('s.level = :level:', ['level' => $level]);
        $depInfo = $builder->getQuery()->execute()->getFirst();
        $managerId = $depInfo->manager_id ?? '';
        if ($managerId) {
            return [$managerId, $depInfo->id];
        } else {
            return ['', $depInfo->id ?? ''];
        }
    }

    /**
     * 查找网点正主管
     * @param $storeId
     */
    public function findStoreManager($storeId, $extend)
    {
        //[1]如果存在指定网点，则获取指定网点的DM
        $extendStoreId = $extend['store_id'] ?? '';
        if (!empty($extendStoreId)) {
            $storeId = $extendStoreId;
        }

        if (!$storeId) {
            return "";
        }

        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'sys_store_id = ?1 and job_title = 16 and state = 1 and formal = 1 and is_sub_staff = 0',
            'bind' => [
                1 => $storeId,
            ],
        ]);
        return isset($staffInfo) && $staffInfo ? $staffInfo->staff_info_id : '';
    }

    /**
     *
     * 查找网点的shop supervisor 职位
     * @param $storeId
     * @return string
     */
    public function findShopSupervisor($storeId)
    {
        $staffs = HrStaffInfoModel::find([
            'conditions' => 'sys_store_id = ?1 and job_title = ?2 and state = 1 and formal = 1 and is_sub_staff = 0',
            'bind' => [
                1 => $storeId,
                2 => enums::$job_title['shop_supervisor'],
            ],
        ])->toArray();
        $staffIds = [];
        foreach ($staffs as $staff) {
            $staffIds[] = $staff['staff_info_id'];
        }

        return $staffIds ? implode(",", $staffIds) : "";
    }

    /**
     *
     * 找职位 shop operations manager
     * @return string
     */
    public function findShopOperationsManager()
    {
        $staffs = HrStaffInfoModel::find([
            'conditions' => ' job_title = ?1 and state = 1 and formal = 1 and is_sub_staff = 0',
            'bind' => [
                1 => enums::$job_title['shop_operations_manager'],
            ],
        ])->toArray();
        $staffIds = [];
        foreach ($staffs as $staff) {
            $staffIds[] = $staff['staff_info_id'];
        }

        return $staffIds ? implode(",", $staffIds) : "";

    }

    /**
     * 查找对应网点关联的片区的片区经理
     * @param $storeId
     * @param $extend
     * @return string
     */
    public function findDM($storeId, $extend)
    {
        //[1]如果存在指定网点，则获取指定网点的DM
        $extendStoreId = $extend['store_id'] ?? '';
        if (!empty($extendStoreId)) {
            $storeId = $extendStoreId;
        }

        if (!$storeId) {
            return "";
        }

        //查找指定网点的DM
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.*');
        $builder->from(['ss' => BiSysStoreModel::class]);
        $builder->join(HrStaffManageRegionsModel::class, 'ss.manage_region = hsmr.region_id and ss.manage_piece = hsmr.piece_id', 'hsmr');
        $builder->join(BiHrStaffInfoModel::class, 'hsmr.staff_info_id = hsi.staff_info_id', 'hsi');
        $builder->where("ss.id = :store_id: and hsi.job_title = 269 and ss.state = 1 and hsi.state = 1", [
            'store_id' => $storeId,
        ]);
        $returnData = $builder->getQuery()->execute();

        if (empty($returnData)) {
            return "";
        } else {
            $returnData = $returnData->getFirst();
            return $returnData->staff_info_id ?? "";
        }
    }

    /**
     * 查找对应网点关联的大区的大区经理
     * @param $storeId
     * @return string
     */
    public function findRM($storeId)
    {
        if (!$storeId) {
            return "";
        }
        $store = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind'       => ['store_id' => $storeId],
        ]);
        $store = $store ? $store->toArray() : [];
        if ($store) {
            if (in_array($store['category'], [
                enums::$stores_category['shop_pickup_only'],
                enums::$stores_category['shop_pickup_delivery'],
                enums::$stores_category['shop_ushop'],
            ])) {
                $sql        = "--
                SELECT 
                    hsmr.*, 
                    ss.category
                FROM sys_store ss
                JOIN `hr_staff_manage_regions` hsmr ON ss.manage_region = hsmr.region_id
                JOIN `hr_staff_info` hsi on hsi.`staff_info_id` = hsmr.`staff_info_id` 
                WHERE ss.id = ? and hsi.job_title = 11 and ss.state = 1 and hsi.`state` = 1";
            } else {
                $sql        = "--
                SELECT 
                    hsmr.*, 
                    ss.category
                FROM sys_store ss
                JOIN `hr_staff_manage_regions` hsmr ON ss.manage_region = hsmr.region_id
                JOIN `hr_staff_info` hsi on hsi.`staff_info_id` = hsmr.`staff_info_id` 
                WHERE ss.id = ? and hsi.job_title = 79 and ss.state = 1 and hsi.`state` = 1";

            }
            $returnData = $this->getDI()->get('db_rbi')->query($sql, [$storeId])->fetch(\Phalcon\Db::FETCH_ASSOC);
            if (empty($returnData)) {
                return "";
            } else {
                return $returnData['staff_info_id'];
            }
        } else {
            return "";
        }

    }

    /**
     * 查找对应网点关联的大区的大区经理
     * @param $storeId
     * @param $extend
     * @return string
     */
    public function findAM($storeId, $extend)
    {
        //[1]如果存在指定网点，则获取指定网点的DM
        $extendStoreId = $extend['store_id'] ?? '';
        if (!empty($extendStoreId)) {
            $storeId = $extendStoreId;
        }

        if (!$storeId) {
            return "";
        }

        //查找指定网点的DM
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsmr.*');
        $builder->from(['ss' => BiSysStoreModel::class]);
        $builder->join(HrStaffManageRegionsModel::class, 'hsmr.region_id = ss.manage_region', 'hsmr');
        $builder->join(BiHrStaffInfoModel::class, 'hsmr.staff_info_id = hsi.staff_info_id', 'hsi');
        $builder->where("ss.id = :store_id: and hsi.job_title = 11 and ss.state = 1 and hsi.state = 1", [
            'store_id' => $storeId,
        ]);
        $returnData = $builder->getQuery()->execute();
        if (empty($returnData)) {
            return "";
        } else {
            $returnData = $returnData->getFirst();
            return $returnData->staff_info_id ?? "";
        }
    }

    /**
     * 查找GM
     * @param int $departmentId 申请人所属部门
     * @param array $extend     扩展参数 如果包含department_id字段，则以包含的字段为准，如果不包含则以$departmentId变量为准
     *                          该字段为了解决 不以申请人所在部门为准去找部门负责人
     * @return string
     */
    public function findGroupBoss($departmentId, $extend)
    {
        //如果包含department_id字段，则以包含的字段为准，如果不包含则以$departmentId变量为准
        $extendDepartmentId = $extend['department_id'] ?? '';
        if (!empty($extendDepartmentId)) {
            $departmentId = $extendDepartmentId;
        }

        if (!$departmentId) {
            return "";
        }

        //获取部门详情
        $departmentInfo = SysDepartmentModel::findFirst([
            'conditions' => "id = :department_id:",
            'bind' => [
                'department_id'  => $departmentId,
            ],
        ]);
        if (empty($departmentInfo)) {
            return "";
        }

        if ($departmentInfo->type == 2) { //属于公司的部门
            $ret = SysDepartmentModel::findFirst([
                'conditions' => "id = :department_id:",
                'bind' => [
                    'department_id'  => $departmentInfo->company_id,
                ],
            ]);
            $returnData = $ret->manager_id ?? '';
        } else if ($departmentInfo->type == 3) { //直属部门
            $ret = SysDepartmentModel::findFirst([
                'conditions' => "id = :department_id:",
                'bind' => [
                    'department_id'  => $departmentInfo->group_boss_id,
                ],
            ]);
            $returnData = $ret->manager_id ?? '';
        } else {
            $returnData = $departmentInfo->manager_id ?? '';
        }

        if (empty($returnData)) {
            return "";
        } else {
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id: and state = 1 and formal = 1 ',
                'bind' => [
                    'staff_id' => $returnData,
                ],
            ]);

            return !empty($staffInfo) ? $staffInfo->staff_info_id : "";
        }
    }

    /**
     * 处理审批列表
     * @param $request
     * @param $approvals
     * @param bool $delayState
     * @return void
     */
    public function processApproval($request, $approvals, bool $delayState = false)
    {
        //判断延时审批
        if ($delayState) { //需要延时处理
            foreach ($approvals as $approval) {
                $insertData = [
                    'biz_value'    => $request->getBizValue(),
                    'biz_type'     => $request->getBizType(),
                    'flow_id'      => $request->getFlowId(),
                    'flow_node_id' => $request->getCurrentFlowNodeId(),
                    'submitter_id' => $request->getSubmitterId(),
                    'approval_id'  => $approval,
                    'state'        => Enums::APPROVAL_STATUS_PENDING,
                    'summary'      => $request->getSummary(),
                    'is_cancel'    => $request->getIsCancel(),
                    'is_edit'      => $request->getIsEdit(),
                    'time_out'     => $request->getTimeOut(),
                    'created_at'   => gmdate("Y-m-d H:i:s"),
                    'updated_at'   => gmdate("Y-m-d H:i:s"),
                ];

                $app = new AuditApprovalDelayModel();
                $app->setBizType($request->getBizType());
                $app->setBizValue($request->getBizValue());
                $app->setDate(date("Y-m-d", strtotime('+1 day')));
                $app->setInsertData($insertData);
                $app->setCreatedAt(gmdate('Y-m-d H:i:s'));
                $app->setUpdatedAt(gmdate('Y-m-d H:i:s'));
                $app->save();
            }
        } else {
            foreach ($approvals as $approval) {
                $app = new AuditApprovalModel();
                $app->setBizType($request->getBizType());
                $app->setBizValue($request->getBizValue());
                $app->setFlowId($request->getFlowId());
                $app->setFlowNodeId($request->getCurrentFlowNodeId());
                $app->setSubmitterId($request->getSubmitterId());
                $app->setApprovalId($approval);
                $app->setState(Enums::APPROVAL_STATUS_PENDING);
                $app->setSummary($request->getSummary());
                $app->setIsCancel($request->getIsCancel());
                $app->setIsEdit($request->getIsEdit());
                $app->setTimeOut($request->getTimeOut());
                $app->save();
            }
        }
    }

    /**
     * 获取申请的审批日志
     * @param $request
     * @param $user
     * @return array
     * @throws InnerException
     */
    public function getAuditLogs($request, $user)
    {
        $id_staff_info_id_hide = 56780;
        $data = [];
        if(empty($request)){
            $this->logger->write_log("getAuditLogs request 不存在! ",'info');
            return $data;
        }
        $add_hour = $this->getDI()['config']['application']['add_hour'];

        // 获取日志数据
        $logs = $this->getAuditLogDetail($request);
        if ($logs){
            $t      = $this->getTranslation();
            $start  = array_shift($logs);

            //申请节点处理
            $staff_id = $start['staff_id'];
            $name = $start['staff_name'];
            if (isCountry('ID')){
                $name = $staff_id == $id_staff_info_id_hide ? '' : $name;
                $staff_id = $staff_id == $id_staff_info_id_hide ? '' : $staff_id;
            }
            $data[] = [
                'staff_id'          => $staff_id,
                'name'              => $name,
                'position'          => $start['staff_job_title_name'],
                'department'        => $start['staff_department'],
                'status'            => $t->_('flow_audit_action.0'),
                'status_code'       => 8,
                'time'              => gmdate("Y-m-d H:i:s", strtotime($start['audit_at']) + $add_hour * 3600),
                'times_tamp'        => (string)  strtotime(gmdate("Y-m-d H:i:s", strtotime($start['audit_at']) + $add_hour * 3600)), // 时间戳
                'remark'            => '',
                "is_ok"             => 0,
                "audit_info"        => '',
                'is_show_icon'      => 1,
            ];

            // 已经展示的节点 和 审批人
            $already_displayed_nodes = [];

            //已经处理的log
            foreach ($logs as $log) {
                $auditInfo = "";
                $state_txt = $t->_(Enums::$wf_action_status[$log['audit_action']]);
                //如果是撤销审批申请
                if($request->submitter_id == $start['staff_id'] && $request->is_cancel > 0 && $log['audit_action'] == Enums::APPROVAL_STATUS_PENDING) {
                    $state_txt = $t->_(Enums::$wf_action_status[Enums::WF_ACTION_CREATE_CANCEL]);
                }

                if (!empty($log['audit_action']) && in_array($log['audit_action'], Enums::$wf_action_need_parse)) {
                    if (strpos($log['audit_info'], '|')) {
                        $info = explode('|', $log['audit_info']);
                        $auditInfo = str_replace("{x}", $info[1], $t->_($info[0]));
                    } else {
                        $auditInfo = $t->_($log['audit_info']);
                    }
                }
                $auditInfo = !empty($auditInfo) ? $auditInfo: $log['audit_info'];
                if ($log['audit_action'] == enums::WF_ACTION_CC){
                    $auditInfo = '';
                }

                $staff_id = $log['staff_id'];
                $name = $log['staff_name'];
                if (isCountry('ID')){
                    $name = $staff_id == $id_staff_info_id_hide ? '' : $name;
                    $staff_id = $staff_id == $id_staff_info_id_hide ? '' : $staff_id;
                }
                $data[] = [
                    'staff_id'      => $staff_id,
                    'name'          => $name,
                    'department'    => $log['staff_department'],
                    'position'      => $log['staff_job_title_name'],
                    'status'        => $state_txt,
                    'status_code'   => $log['audit_action'],
                    'time'          => gmdate("Y-m-d H:i:s", strtotime($log['audit_at']) + $add_hour * 3600),
                    'times_tamp'    => (string) strtotime(gmdate("Y-m-d H:i:s", strtotime($log['audit_at']) + $add_hour * 3600)),// 时间戳
                    'remark'        => $auditInfo, //与audit_info统一
                    "audit_info"    => $auditInfo, //与remark统一
                    "is_ok"         => 0,
                    'is_show_icon'  => array_key_exists($log['flow_node_id'], $already_displayed_nodes) ? 0 : 1,
                ];

                $already_displayed_nodes[$log['flow_node_id']][] = $log['staff_id'];
            }

            //待审批的log
            if ($request->getState() == Enums::APPROVAL_STATUS_PENDING
            ) {
                $submitter = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :uid:',
                    'bind' => [
                        'uid' => $start['staff_id'],
                    ],
                ]);
                $current = $this->getCurrentAuditor($request, $submitter->staff_info_id ?? '');
                if ($current) {
                    // 获取当前节点类型: 会签类型展示样式单独处理
                    $current_node = WorkflowNodeModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => [
                            'id' => $request->current_flow_node_id,
                        ],
                    ]);

                    // 会签节点: 日志样式单独调整
                    if ($current_node->type == enums::NODE_COUNTERSIGN) {
                        foreach ($current as $key => $item) {
                            // 如果当前节点，当前人已审批，则剔除
                            $current_node_already_auditor_ids = $already_displayed_nodes[$request->current_flow_node_id] ?? [];
                            if (!empty($current_node_already_auditor_ids) && in_array($item['staff_info_id'], $current_node_already_auditor_ids)) {
                                continue;
                            }

                            $staff_id = $item['staff_info_id'] ?? '';
                            $name = $item['staff_name'] ?? '';
                            if (isCountry('ID')){
                                $name = $staff_id == $id_staff_info_id_hide ? '' : $name;
                                $staff_id = $staff_id == $id_staff_info_id_hide ? '' : $staff_id;
                            }
                            $approval = [
                                'staff_id'    => $staff_id,
                                'name'        => $name,
                                'department'  => $item['department_name'] ?? '',
                                'position'    => $item['job_title_name'],
                                'status'        => $t->_('audit_status.' . $request->getState()),
                                'status_code'   => $request->getState(),
                                'time'          => null,
                                'times_tamp'    => '',
                                'remark'        => '',
                                "is_ok"         => 1,
                                "audit_info"    => "",
                            ];

                            // 如果当前节点还没有审批, 那么只有第一个待审批人前面的圆点需要展示
                            if (empty($already_displayed_nodes)) {
                                $approval['is_show_icon'] = $key == 0 ? 1 : 0;
                            } else {
                                $approval['is_show_icon'] = array_key_exists($request->current_flow_node_id, $already_displayed_nodes) ? 0 : 1;
                            }

                            array_push($data, $approval);
                        }

                    } else {
                        $staffs = [];
                        $staffIds = [];
                        foreach ($current as $item) {
                            $staff_id = $item['staff_info_id'] ?? '';
                            $name = $item['staff_name'] ?? '';
                            if (isCountry('ID')){
                                $name = $staff_id == $id_staff_info_id_hide ? '' : $name;
                                $staff_id = $staff_id == $id_staff_info_id_hide ? '' : $staff_id;
                            }

                            $staffIds[] = $item['staff_info_id'];
                            $staffs = [
                                'staff_id'    => $staff_id,
                                'name'        => $name,
                                'department'  => $item['department_name'] ?? '',
                                'position'    => $item['job_title_name'],
                            ];
                        }
                        if (!(isCountry('ID') && in_array($id_staff_info_id_hide , $staffIds))){
                            $staffs['staff_id'] = implode(',', array_filter($staffIds));
                        }
                        $approval = [
                            'status'        => $t->_('audit_status.' . $request->getState()),
                            'status_code'   => $request->getState(),
                            'time'          => null,
                            'times_tamp'    => '',
                            'remark'        => '',
                            "is_ok"         => 1,
                            "audit_info"    => "",
                            'is_show_icon'  => 1,
                        ];
                        $approval = array_merge($approval, $staffs);
                        array_push($data, $approval);
                    }
                }
            }

            $staffInfoArrs = array_column($data, 'staff_id');

            //追加昵称字段
            $staffArr = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in({staffs:array})',
                'bind'       => ['staffs' => $staffInfoArrs],
                'columns'    => ['staff_info_id', 'nick_name'],
            ])->toArray();
            $staffArr = array_column($staffArr, 'nick_name', 'staff_info_id');
            foreach ($data as $k => $v) {
                $data[$k]['name'] =  isset($staffArr[$v['staff_id']]) && $staffArr[$v['staff_id']]
                    ? $v['name'] . "({$staffArr[$v['staff_id']]})"
                    : $v['name'];
                if (isCountry('ID')) {
                    $data[$k]['name'] = $v['staff_id'] == $id_staff_info_id_hide ? '' : $data[$k]['name'];
                }
            }
        }
        return $data;
    }

    /**
     * 获取申请的审批日志
     * @param $request
     * @param $user
     * @param int $datetime_formatter
     * @return array
     * @throws InnerException
     * @throws \Exception
     */
    public function getAuditLogsV2($request, $user, int $datetime_formatter = AuditListEnums::DATETIME_FORMATTER_DEFAULT): array
    {
        if(empty($request)){
            $this->logger->write_log("getAuditLogs request 不存在! ",'info');
            return [];
        }

        // 获取日志数据
        $logs = $this->getAuditLogDetail($request);
        if (empty($logs)) {
            return [];
        }

        $t = $this->getTranslation();
        $alreadyDisplayedNodes = []; // 已经展示的节点和审批人
        $nickNameList = []; //员工昵称
        $staffJobTitleGrade = []; //员工职级
        $staffIdsFromLogs = array_column($logs, 'staff_id'); //获取审批流中全部员工ID
        $startNodeLog = $logs[0];
        $nodeIds = array_column($logs, 'flow_node_id'); //获取全部节点
        $nodeIds = array_merge($nodeIds, [$request->current_flow_node_id]);

        $nodeInfo = $this->getNodeByIds($nodeIds);
        $nodeMark = array_column($nodeInfo, 'node_mark', 'id');
        $nodeInfo = array_column($nodeInfo, 'type', 'id');
        [$ccFront, $ccBack, $ccNodeStaffIds] = $this->getCcNodeStaffIds($nodeIds);

        if ($request->getState() == Enums::APPROVAL_STATUS_PENDING) { //待审批工号
            $currentNodeApprovalIds = $this->getSpecifyNodeStaffIds($request->current_flow_node_id, $startNodeLog['staff_id'] ?? '');
        }
        $staffIds = array_merge($staffIdsFromLogs ?: [], $currentNodeApprovalIds ?? [], $ccNodeStaffIds ?: []);
        $staffIds = !empty($staffIds) ? array_values(array_unique(array_filter($staffIds))) : [];
        if (!empty($staffIds)) { //追加昵称字段

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('hsi.staff_info_id,hsi.name,hsi.nick_name,hsi.job_title_grade_v2,hjt.job_name,sd.name as department_name');
            $builder->from(['hsi' => HrStaffInfoModel::class]);
            $builder->leftjoin(HrJobTitleModel::class, 'hjt.id = hsi.job_title', 'hjt');
            $builder->leftjoin(SysDepartmentModel::class, 'sd.id = hsi.node_department_id', 'sd');
            $builder->inWhere('hsi.staff_info_id', $staffIds);
            $staffInfoArr = $builder->getQuery()->execute()->toArray();

            $staffNameList = array_column($staffInfoArr, 'name', 'staff_info_id');
            $nickNameList = array_column($staffInfoArr, 'nick_name', 'staff_info_id');
            $staffJobTitleGrade = array_column($staffInfoArr, 'job_title_grade_v2', 'staff_info_id');
            $jobTitleList = array_column($staffInfoArr, 'job_name', 'staff_info_id');
            $deptList = array_column($staffInfoArr, 'department_name', 'staff_info_id');
        }

        $params = [
            'nick_name' => $nickNameList ?? [],
            'staff_name' => $staffNameList ?? [],
            'staff_job_title_name' => $jobTitleList ?? [],
            'staff_department' => $deptList ?? [],
            'staff_job_title_grade' => $staffJobTitleGrade ?? [],
        ];
        $independentAction = [enums::WF_ACTION_CANCEL,
            enums::WF_ACTION_APPROVAL_SUPERIOR,
            enums::WF_ACTION_CONFIRM_PENDING,
            enums::WF_ACTION_CONFIRM_PASS,
            enums::WF_ACTION_CONFIRM_REJECT,
            enums::WF_ACTION_CONFIRM_CANCEL,
            enums::WF_ACTION_CONFIRM_OVERTIME,
        ];

        //已经处理的log
        foreach ($logs as $log) {

            $uniqueKey = $log['flow_node_id'];
            $auditInfo = "";

            //如果是撤销审批申请
            if($request->submitter_id == $log['staff_id'] && $request->is_cancel > 0 && $log['audit_action'] == Enums::WF_ACTION_CREATE_CANCEL) {
                $state_txt = $t->_(Enums::$wf_action_status[Enums::WF_ACTION_CREATE_CANCEL]);
            } else {
                $state_txt = $t->_(Enums::$wf_action_status[$log['audit_action']]);
            }

            if (!empty($log['audit_action']) && in_array($log['audit_action'], Enums::$wf_action_need_parse)) {
                if (strpos($log['audit_info'], '|')) {
                    $info = explode('|', $log['audit_info']);
                    $auditInfo = str_replace("{x}", $info[1], $t->_($info[0]));
                } else {
                    $auditInfo = $t->_($log['audit_info']);
                }
            }
            $auditInfo = !empty($auditInfo) ? $auditInfo: $log['audit_info'];
            if ($log['audit_action'] == enums::WF_ACTION_CC){
                $auditInfo = '';
            }

            if (isset($data[$uniqueKey]) &&  !in_array($log['audit_action'], $independentAction)) { //撤销节点要独立为一个节点
                $data[$uniqueKey]["approval_info"][] = [
                    'staff_id'    => $log['staff_id'],
                    'staff_name'  => $this->formatStaffName($log['staff_name'], $nickNameList[$log['staff_id']] ?? ''),
                    'position'    => $log['staff_job_title_name'],
                    'department'  => $log['staff_department'],
                    'sort'        => intval($staffJobTitleGrade[$log['staff_id']]) ?? 0,
                    'state_txt'   => $state_txt,
                    'status_code' => $log['audit_action'],
                    'audit_info'  => $auditInfo,
                ];
                $data[$uniqueKey]["action_time"] = $this->formatDateTime($log['audit_at'], $datetime_formatter);
                $data[$uniqueKey]["status_code"] = $log['audit_action'];
            } else {
                //撤销节点要独立为一个节点
                if (in_array($log['audit_action'], $independentAction)) {
                    $uniqueKey = $uniqueKey . '-' . $log['audit_action'];
                }

                $data[$uniqueKey] = [
                    'state_txt'         => $this->formatStateCodeTxt($log['audit_action'], $log['flow_node_id'], $nodeMark),
                    'status_code'       => $log['audit_action'],
                    'action_time'       => $this->formatDateTime($log['audit_at'], $datetime_formatter),
                    "approval_type"     => intval($log['audit_action'] != enums::WF_ACTION_CANCEL ? ($nodeInfo[$log['flow_node_id']] ?? 0) : 0), //或签 会签 抄送
                    "process_state"     => 1,
                    "approval_info"     => [
                        [
                            'staff_id'    => $log['staff_id'],
                            'staff_name'  => $this->formatStaffName($log['staff_name'], $nickNameList[$log['staff_id']] ?? ''),
                            'position'    => $log['staff_job_title_name'],
                            'department'  => $log['staff_department'],
                            'sort'        => !empty($staffJobTitleGrade[$log['staff_id']])? intval($staffJobTitleGrade[$log['staff_id']]) : 0,
                            'state_txt'   => $state_txt,
                            'status_code' => $log['audit_action'],
                            'audit_info'  => $auditInfo,
                        ],
                    ],
                ];
            }
            $alreadyDisplayedNodes[$uniqueKey][] = $log['staff_id'];

            //会签同意+判断一下当前节点是否审批完成
            //或签同意肯定抄送
            //驳回操作+同意/驳回操作都抄送
            if (isset($ccBack[$uniqueKey]) && $ccBack[$uniqueKey] &&
                (in_array($log['audit_action'], [enums::WF_ACTION_APPROVE, enums::WF_ACTION_APPROVAL_EMPTY]) ||
                in_array($log['audit_action'], [
                    enums::WF_ACTION_APPROVE_COUNTERSIGN,
                    enums::WF_ACTION_OVERTIME_AUTO_APPROVAL_COUNTERSIGN]) && $request->current_flow_node_id != $log['flow_node_id'] ||
                in_array($log['audit_action'], [
                        enums::APPROVAL_STATUS_REJECTED,
                        enums::APPROVAL_STATUS_REJECTED_ROLL_BACK,
                        enums::APPROVAL_STATUS_APPROVAL_EMPTY_AUTO_REJECT,
                    ]) && $ccBack[$uniqueKey]['type'] == WorkflowNodeAccidentBusinessModel::CC_BEFORE_NODE
                )
            ) {
                $params['cc_time']       = $this->formatDateTime($log['audit_at'], $datetime_formatter);
                $params['process_state'] = AuditLogModel::PROCESS_BAR_HIGHLIGHT;
                $data[$uniqueKey . '-1'] = $this->generateCcLog($uniqueKey, $ccBack, $params);
            }
        }

        //待审批的log
        if ($request->getState() == Enums::APPROVAL_STATUS_PENDING) {
            do {
                $current = $this->getCurrentAuditorV2($currentNodeApprovalIds ?? []);
                if (empty($current)) {
                    break;
                }

                // 获取当前节点类型: 会签类型展示样式单独处理
                $currentNode = WorkflowNodeModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => [
                        'id' => $request->current_flow_node_id,
                    ],
                ]);
                if (empty($currentNode)) {
                    $this->logger->write_log(sprintf('workflow node id %s do not exist!', $request->current_flow_node_id));
                    throw new \Exception("workflow node data error, please check!");
                }

                //标记审核
                $stateTxt = $currentNode->getNodeMark() == WorkflowNodeModel::NODE_MARK_AUDIT
                    ? $t->_('audit_status.' . Enums::WF_ACTION_AUDIT_PENDING)
                    : $t->_('audit_status.' . Enums::WF_ACTION_PENDING);

                if ($currentNode->type == enums::NODE_COUNTERSIGN) {
                    foreach ($current as $key => $item) {
                        // 如果当前节点，当前人已审批，则剔除
                        $currentNodeAlreadyAuditorIds = $alreadyDisplayedNodes[$request->current_flow_node_id] ?? [];
                        if (!empty($currentNodeAlreadyAuditorIds) && in_array($item['staff_info_id'], $currentNodeAlreadyAuditorIds)) {
                            continue;
                        }
                        $approval[] = [
                            'staff_id'    => $item['staff_info_id'] ?? '',
                            'staff_name'  => $this->formatStaffName($item['staff_name'], $nickNameList[$item['staff_info_id']] ?? ''),
                            'position'    => $item['job_title_name'],
                            'department'  => $item['department_name'],
                            'sort'        => intval($staffJobTitleGrade[$item['staff_info_id']]) ?? 0,
                            'state_txt'   => $stateTxt,
                            'status_code' => $request->getState(),
                            'audit_info'  => '',
                        ];
                    }

                    if (isset($data[$request->current_flow_node_id])) {

                        $approval = array_merge($data[$request->current_flow_node_id]["approval_info"], $approval);
                        $data[$request->current_flow_node_id] = [
                            'state_txt'         => $this->formatStateCodeTxt($request->getState(), $request->getCurrentFlowNodeId(), $nodeMark),
                            'status_code'       => $request->getState(),
                            'action_time'       => null,
                            "process_state"     => 1,
                            "approval_type"     => intval($nodeInfo[$request->current_flow_node_id] ?? 0), //或签 会签 抄送
                            "approval_info"     => $approval ?? [],
                        ];
                    } else {
                        $data[$request->current_flow_node_id] = [
                            'state_txt'         => $this->formatStateCodeTxt($request->getState(), $request->getCurrentFlowNodeId(), $nodeMark),
                            'status_code'       => $request->getState(),
                            'action_time'       => null,
                            "process_state"     => 1,
                            "approval_type"     => intval($nodeInfo[$request->current_flow_node_id] ?? 0), //或签 会签 抄送
                            "approval_info"     => $approval ?? [],
                        ];
                    }

                } else {
                    foreach ($current as $item) {

                        $approval[] = [
                            'staff_id'    => $item['staff_info_id'] ?? '',
                            'staff_name'  => $item['staff_name'] ?? '',
                            'position'    => $item['job_title_name'],
                            'department'  => $item['department_name'] ?? '',
                            'sort'        => !empty($staffJobTitleGrade[$item['staff_info_id']]) ? intval($staffJobTitleGrade[$item['staff_info_id']]) : 0,
                            'state_txt'   => $stateTxt,
                            'status_code' => 1,
                            "audit_info"  => "",
                        ];
                    }

                    if (isset($data[$request->current_flow_node_id])) {

                        $approval = array_merge($data[$request->current_flow_node_id]["approval_info"], $approval ?? []);
                        $data[$request->current_flow_node_id] = [
                            'state_txt'         => $this->formatStateCodeTxt($request->getState(), $request->getCurrentFlowNodeId(), $nodeMark),
                            'status_code'       => $request->getState(),
                            'action_time'       => null,
                            "approval_type"     => intval($nodeInfo[$request->current_flow_node_id] ?? 0), //或签 会签 抄送
                            "process_state"     => 1,
                            "approval_info"     => $approval ?? [],
                        ];
                    } else {
                        $data[$request->current_flow_node_id] = [
                            'state_txt'     => $this->formatStateCodeTxt($request->getState(), $request->getCurrentFlowNodeId(), $nodeMark),
                            'status_code'   => $request->getState(),
                            'action_time'   => null,
                            'approval_type' => intval($nodeInfo[$request->current_flow_node_id] ?? 0), //或签 会签 抄送
                            "process_state" => 1,
                            'approval_info' => $approval ?? [],
                        ];
                    }
                }
            } while(0);
        }

        foreach ($data as &$item) {
            $count = count($item['approval_info']);
            if ($count > 1) {

                //如果同时存在同意的 + 待审批的，那么
                //已经审批同意的sort追加 100在排序因为
                //已经同意的在最上面
                $keySort = array_map(function ($v, $k) use($count) {
                    if (!in_array($v['status_code'], [enums::WF_ACTION_PENDING, enums::WF_ACTION_CC])) {
                        $v['sort'] = ($count - $k) * 100;
                    }
                    return $v;
                }, $item['approval_info'], array_keys($item['approval_info']));

                $keySort = array_column($keySort, 'sort');
                array_multisort($keySort, SORT_NUMERIC, SORT_DESC, $item['approval_info']);
            }
        }

        return array_values($data);
    }

    /**
     * 获取申请的审批日志
     * @param $request
     * @param $user
     * @param int $datetime_formatter
     * @return array
     * @throws InnerException
     * @throws \Exception
     */
    public function getAuditLogsV3($request, $user, int $datetime_formatter = AuditListEnums::DATETIME_FORMATTER_DEFAULT)
    {
        $data = $this->publicLog($request, $user, $datetime_formatter);
        if(empty($data)){
            return [];
        }
        if (in_array($request->getBizType(), [AuditListEnums::APPROVAL_TYPE_LE, AuditListEnums::APPROVAL_TYPE_BT])) {
            $data = $this->chunkLogs($data);
        } else {
            $data = $this->defaultChunkLogs($request, $data);
        }
        return array_values($data);
    }
    /**
     * 获取申请的审批日志
     * @param $request
     * @param $user
     * @param int $datetime_formatter
     * @return array
     * @throws InnerException
     * @throws \Exception
     */
    public function getAuditLogsV4($request, $user, int $datetime_formatter = AuditListEnums::DATETIME_FORMATTER_DEFAULT)
    {
        $data = $this->publicLog($request, $user, $datetime_formatter);
        if(empty($data)){
            return [];
        }

        if (in_array($request->getBizType(), AuditListEnums::getAuditTypeTrunk())) {
            $data = $this->chunkLogsV4($request, $data);
        } else {
            $data = $this->defaultChunkLogs($request, $data);
        }
        return array_values($data);
    }

    /**
     * @description 获取审批日志
     * @param $request
     * @param $user
     * @param int $datetime_formatter
     * @return array
     * @throws InnerException
     * @throws ValidationException
     */
    public function publicLog($request, $user, int $datetime_formatter = AuditListEnums::DATETIME_FORMATTER_DEFAULT)
    {
        if(empty($request)){
            $this->logger->write_log("getAuditLogs request 不存在! ",'info');
            return [];
        }

        // 获取日志数据
        $logs = $this->getAuditLogDetail($request);
        if (empty($logs)) {
            return [];
        }

        $t                     = $this->getTranslation();
        $alreadyDisplayedNodes = [];                              //已经展示的节点和审批人
        $nickNameList          = [];                              //员工昵称
        $staffJobTitleGrade    = [];                              //员工职级
        $startNodeLog          = $logs[0];
        $staffIdsFromLogs      = array_column($logs, 'staff_id');

        //获取全部节点
        $nodeInfo = $this->getFlowNodes($request->getFlowId())->toArray();

        $totalStaffAuditor = array_column($nodeInfo, 'auditor_id');
        $nodeIds           = array_column($nodeInfo, 'id');
        $nodeMark          = array_column($nodeInfo, 'node_mark', 'id');
        $nodeInfo          = array_column($nodeInfo, 'type', 'id');
        [$ccFront, $ccBack, $ccNodeStaffIds] = $this->getCcNodeStaffIds($nodeIds);

        //1. 获取一条完整审批流上的申请人、审批人、抄送人
        if ($request->getState() == Enums::APPROVAL_STATUS_PENDING) { //待审批工号
            $currentNodeApprovalIds = $this->getSpecifyNodeStaffIds($request->getCurrentFlowNodeId(), $startNodeLog['staff_id'] ?? '');
        }
        $totalStaffAuditorInfo = array_map(function ($v) {
            return !empty($v) ? explode(',', $v) : [];
        }, $totalStaffAuditor);
        $totalStaffAuditorInfo = $totalStaffAuditorInfo ? array_values(array_unique(array_merge(...$totalStaffAuditorInfo))): [];

        $staffIds = array_merge($totalStaffAuditorInfo, $ccNodeStaffIds ?? [], $staffIdsFromLogs);
        $staffIds = array_values(array_unique(array_filter($staffIds)));
        $isUnpaid = $currentUserUnpaid = false;
        if (!empty($staffIds)) {
            $staffRepository    = new StaffRepository();
            $staffInfoArr       = $staffRepository->getSpecifyStaffInfo($staffIds);
            $staffNameList      = array_column($staffInfoArr, 'staff_name', 'staff_info_id');
            $nickNameList       = array_column($staffInfoArr, 'nick_name', 'staff_info_id');
            $staffJobTitleGrade = array_column($staffInfoArr, 'job_title_grade_v2', 'staff_info_id');
            $jobTitleList       = array_column($staffInfoArr, 'job_name', 'staff_info_id');
            $deptList           = array_column($staffInfoArr, 'department_name', 'staff_info_id');
            $hireTypeList       = array_column($staffInfoArr, 'hire_type', 'staff_info_id');
            $this->isUnpaid = $isUnpaid = in_array($hireTypeList[$request->getSubmitterId()],HrStaffInfoModel::$agentTypeTogether) ;//这条申请是不是 个人代理
            $currentUserUnpaid  = !empty($hireTypeList[$user]) && in_array($hireTypeList[$user],
                    HrStaffInfoModel::$agentTypeTogether);                                                                          //当前登陆人是不是 个人代理
            if($currentUserUnpaid){
                $jobTitleList = $deptList = [];
            }
        }
        $params = [
            'nick_name'             => $nickNameList,
            'staff_name'            => $staffNameList ?? [],
            'staff_job_title_name'  => $jobTitleList ?? [],
            'staff_department'      => $deptList ?? [],
            'staff_job_title_grade' => $staffJobTitleGrade,
        ];
        //撤销操作、上级离职转交需要展示为独立的节点
        $independentAction = [enums::WF_ACTION_CANCEL,
            enums::WF_ACTION_APPROVAL_SUPERIOR,
            enums::WF_ACTION_CONFIRM_PENDING,
            enums::WF_ACTION_CONFIRM_PASS,
            enums::WF_ACTION_CONFIRM_REJECT,
            enums::WF_ACTION_CONFIRM_CANCEL,
            enums::WF_ACTION_CONFIRM_OVERTIME,
        ];

        //2. 根据审批日志，整理已经审批完部分
        foreach ($logs as $log) {
            //审批流节点ID作为唯一键值，用于聚合同一节点下的多个审批人或抄送人
            $uniqueKey = $log['flow_node_id'];
            //撤销节点要独立为一个节点
            if (in_array($log['audit_action'], $independentAction)) {
                $uniqueKey = $uniqueKey . '-' . $log['audit_action'];
            }
            //用于保存自动审批、自动驳回、自动转交等系统操作的情况时，应该给予的提示语
            $auditInfo   = '';
            $state_array = $this->getStateTranslateGroup();
            $state_txt   = $t->_($state_array[$log['audit_action']]) ?? '';

            //处理特殊提示语
            //例如: 部门负责人找不到的情况，需要提示"审批流中缺少{xxxx}部门负责人信息，请联系HR处理"
            //数据以 "error_message_translation_key|department_id" 格式保存数据, [ err_msg_wf_department_manager_not_exist｜4  ]
            if (!empty($log['audit_action']) && in_array($log['audit_action'], Enums::$wf_action_need_parse)) {
                if (strpos($log['audit_info'], '|')) {
                    $info = explode('|', $log['audit_info']);
                    $auditInfo = str_replace("{x}", $info[1], $t->_($info[0]));
                } else {
                    $auditInfo = $t->_($log['audit_info']);
                }
            }
            $auditInfo = !empty($auditInfo) ? $auditInfo: $log['audit_info'];
            if ($log['audit_action'] == enums::WF_ACTION_CC){
                $auditInfo = '';
            }

            if (isset($data[$uniqueKey]) && !in_array($log['audit_action'], $independentAction)) {
                $indexKey = isset($data[$uniqueKey . '-' . enums::WF_ACTION_APPROVAL_SUPERIOR])
                    ? $uniqueKey . '-' . enums::WF_ACTION_APPROVAL_SUPERIOR
                    : $uniqueKey;
                $data[$indexKey]["approval_info"][] = [
                    'staff_id'    => $log['staff_id'],
                    'staff_name'  => $this->formatStaffName($log['staff_name'], $nickNameList[$log['staff_id']] ?? ''),
                    'position'    => $currentUserUnpaid ? '' : $log['staff_job_title_name'],
                    'department'  => $currentUserUnpaid ? '' : $log['staff_department'],
                    'sort'        => intval($staffJobTitleGrade[$log['staff_id']] ?? 0),
                    'state_txt'   => $state_txt,
                    'status_code' => $log['audit_action'],
                    'audit_info'  => $auditInfo,
                ];
                $data[$indexKey]["action_time"] = $this->formatDateTime($log['audit_at'], $datetime_formatter);
                $data[$indexKey]["status_code"] = $log['audit_action'];
            } elseif (isset($data[$uniqueKey]) && in_array($log['audit_action'], $independentAction)) {
                $data[$uniqueKey]["approval_info"][] = [
                    'staff_id'    => $log['staff_id'],
                    'staff_name'  => $this->formatStaffName($log['staff_name'], $nickNameList[$log['staff_id']] ?? ''),
                    'position'    => $currentUserUnpaid ? '' : $log['staff_job_title_name'],
                    'department'  => $currentUserUnpaid ? '' : $log['staff_department'],
                    'sort'        => intval($staffJobTitleGrade[$log['staff_id']] ?? 0) ,
                    'state_txt'   => $state_txt,
                    'status_code' => $log['audit_action'],
                    'audit_info'  => $auditInfo,
                ];
                $data[$uniqueKey]["action_time"] = $this->formatDateTime($log['audit_at'], $datetime_formatter);
                $data[$uniqueKey]["status_code"] = $log['audit_action'];
            } else {
                $data[$uniqueKey] = [
                    'state_txt'         => $this->formatStateCodeTxt($log['audit_action'], $log['flow_node_id'], $nodeMark),
                    'status_code'       => $log['audit_action'],
                    'action_time'       => $this->formatDateTime($log['audit_at'], $datetime_formatter),
                    "approval_type"     => intval($log['audit_action'] != enums::WF_ACTION_CANCEL ? ($nodeInfo[$log['flow_node_id']] ?? 0) : 0), //或签 会签 抄送
                    "process_state"     => AuditLogModel::PROCESS_BAR_HIGHLIGHT,
                    "approval_info"     => [
                        [
                            'staff_id'    => $log['staff_id'],
                            'staff_name'  => $this->formatStaffName($log['staff_name'], $nickNameList[$log['staff_id']] ?? ''),
                            'position'    => $currentUserUnpaid ? '' : $log['staff_job_title_name'],
                            'department'  => $currentUserUnpaid ? '' : $log['staff_department'],
                            'sort'        => intval($staffJobTitleGrade[$log['staff_id']] ?? 0) ,
                            'state_txt'   => $state_txt,
                            'status_code' => $log['audit_action'],
                            'audit_info'  => $auditInfo,
                        ],
                    ],
                ];
            }
            $alreadyDisplayedNodes[$uniqueKey][] = $log['staff_id'];

            //处理节点后抄送,需要当前节点审批同意
            //或签同意、自动通过、会签完全同意、驳回操作(不勾选仅同意抄送)
            if (isset($ccBack[$uniqueKey]) && $ccBack[$uniqueKey] &&
                (in_array($log['audit_action'], [enums::WF_ACTION_APPROVE, enums::WF_ACTION_APPROVAL_EMPTY, enums::WF_ACTION_AUDIT_APPROVE]) ||
                    in_array($log['audit_action'], [enums::WF_ACTION_APPROVE_COUNTERSIGN, enums::WF_ACTION_OVERTIME_AUTO_APPROVAL_COUNTERSIGN]) && $request->getCurrentFlowNodeId() != $log['flow_node_id'] ||
                    $log['audit_action'] == enums::WF_ACTION_REJECT && $ccBack[$uniqueKey]['type'] == WorkflowNodeAccidentBusinessModel::CC_TYPE_NORMAL
                ) && in_array($ccBack[$uniqueKey]['status'], [WorkflowNodeAccidentBusinessModel::STATUS_TO_BE_PROCESSED, WorkflowNodeAccidentBusinessModel::STATUS_HAS_PROCESSED])
            ) {
                $params['cc_time']       = $this->formatDateTime($log['audit_at'], $datetime_formatter);
                $params['process_state'] = AuditLogModel::PROCESS_BAR_HIGHLIGHT;
                $data[$uniqueKey . '-1'] = $this->generateCcLog($uniqueKey, $ccBack, $params);
            }
        }

        //3. 如果未审批完成，整理未审批完部分
        if ($request->getState() == Enums::APPROVAL_STATUS_PENDING) {
            do {
                $current = $this->getCurrentAuditorV2($currentNodeApprovalIds ?? []);
                if (empty($current)) {
                    break;
                }

                // 获取当前节点类型: 会签类型展示样式单独处理
                $currentNodeInfo = $this->getNodeByIds([$request->getCurrentFlowNodeId()]);
                $currentNode     = !empty($currentNodeInfo) ? current($currentNodeInfo) : [];
                if (empty($currentNode)) {
                    $this->logger->write_log(sprintf('workflow node id %s do not exist!',
                        $request->getCurrentFlowNodeId()));
                    throw new \Exception("workflow node data error, please check!");
                }

                //标记审核
                $statusKey = $this->getStateTranslateKey();
                $stateTxt  = $currentNode['node_mark'] == WorkflowNodeModel::NODE_MARK_AUDIT
                    ? $t->_($statusKey . Enums::WF_ACTION_AUDIT_PENDING)
                    : $t->_($statusKey . Enums::WF_ACTION_PENDING);

                if ($currentNode['type'] == enums::NODE_COUNTERSIGN) {
                    foreach ($current as $key => $item) {
                        // 如果当前节点，当前人已审批，则剔除
                        $currentNodeAlreadyAuditorIds = $alreadyDisplayedNodes[$request->getCurrentFlowNodeId()] ?? [];
                        if (!empty($currentNodeAlreadyAuditorIds) && in_array($item['staff_info_id'],
                                $currentNodeAlreadyAuditorIds)) {
                            continue;
                        }
                        $approval[] = [
                            'staff_id'    => $item['staff_info_id'] ?? '',
                            'staff_name'  => $this->formatStaffName($item['staff_name'],
                                $nickNameList[$item['staff_info_id']] ?? ""),
                            'position'    => $currentUserUnpaid ? '' : $item['job_title_name'],
                            'department'  => $currentUserUnpaid ? '' : $item['department_name'],
                            'sort'        => intval($staffJobTitleGrade[$item['staff_info_id']] ?? 0),
                            'state_txt'   => $stateTxt,
                            'status_code' => $request->getState(),
                            'audit_info'  => '',
                        ];
                    }

                    if (isset($data[$request->getCurrentFlowNodeId()])) {
                        $approval = array_merge($data[$request->getCurrentFlowNodeId()]["approval_info"],
                            ($approval ?? []));
                    }
                    $data[$request->getCurrentFlowNodeId()] = [
                        'state_txt'     => $this->formatStateCodeTxt($request->getState(),
                            $request->getCurrentFlowNodeId(), $nodeMark),
                        'status_code'   => $request->getState(),
                        'action_time'   => null,
                        "process_state" => AuditLogModel::PROCESS_BAR_HIGHLIGHT,
                        "approval_type" => intval($nodeInfo[$request->getCurrentFlowNodeId()] ?? 0), //或签 会签 抄送
                        "approval_info" => $approval ?? [],
                    ];
                } else {
                    foreach ($current as $item) {
                        $approval[] = [
                            'staff_id'    => $item['staff_info_id'] ?? '',
                            'staff_name'  => $item['staff_name'] ?? '',
                            'position'    => $item['job_title_name'],
                            'department'  => $item['department_name'] ?? '',
                            'sort'        => intval($staffJobTitleGrade[$item['staff_info_id']] ?? 0),
                            'state_txt'   => $stateTxt,
                            'status_code' => $request->getState(),
                            "audit_info"  => "",
                        ];
                    }

                    if (isset($data[$request->getCurrentFlowNodeId()])) {
                        $data[$request->getCurrentFlowNodeId() . "-10"] = [
                            'state_txt'     => $this->formatStateCodeTxt($request->getState(),
                                $request->getCurrentFlowNodeId(), $nodeMark),
                            'status_code'   => $request->getState(),
                            'action_time'   => null,
                            "approval_type" => intval($nodeInfo[$request->getCurrentFlowNodeId()] ?? 0), //或签 会签 抄送
                            "process_state" => AuditLogModel::PROCESS_BAR_HIGHLIGHT,
                            "approval_info" => $approval ?? [],
                        ];
                    } else {
                        $data[$request->getCurrentFlowNodeId()] = [
                            'state_txt'     => $this->formatStateCodeTxt($request->getState(),
                                $request->getCurrentFlowNodeId(), $nodeMark),
                            'status_code'   => $request->getState(),
                            'action_time'   => null,
                            'approval_type' => intval($nodeInfo[$request->getCurrentFlowNodeId()] ?? 0), //或签 会签 抄送
                            "process_state" => AuditLogModel::PROCESS_BAR_HIGHLIGHT,
                            'approval_info' => $approval ?? [],
                        ];
                    }
                }
            } while (0);

            //处理节点后抄送
            if (isset($ccBack[$request->getCurrentFlowNodeId()]) && $ccBack[$request->getCurrentFlowNodeId()]) {
                $params['process_state'] = AuditLogModel::PROCESS_BAR_DARKNESS;
                $data[$request->getCurrentFlowNodeId() . '-1'] = $this->generateCcLog($request->getCurrentFlowNodeId(), $ccBack, $params);
            }

            //剩余节点
            $leftNode = $this->getWorkflowNodeByFlowId($request, $user);
            $leftNodeInfo = $this->getNodeByIds($leftNode, true);

            foreach ($leftNodeInfo as $node) {
                $uniqueKey = $node['id'];

                $approval = [];
                switch ($node['type']){
                    case enums::NODE_CC:
                        $action = enums::WF_ACTION_CC;
                        break;
                    default:
                        $action = enums::WF_ACTION_APPROVE;
                        break;
                }

                if (isset($node['auditor_id']) && $node['auditor_id']) {
                    $auditorIds = explode(',', $node['auditor_id']);
                    foreach ($auditorIds as $auditorId) {
                        $approval[] = [
                            'staff_id'    => $auditorId ?? enums::SYSTEM_STAFF_ID,
                            'staff_name'  => $this->formatStaffName($staffNameList[$auditorId] ?? '', $nickNameList[$auditorId] ?? ''),
                            'position'    => $currentUserUnpaid ? '' : $jobTitleList[$auditorId] ?? '',
                            'department'  => $currentUserUnpaid ? '' : $deptList[$auditorId] ?? '',
                            'sort'        => intval($staffJobTitleGrade[$auditorId] ?? 0) ,
                            'state_txt'   => "",
                            'status_code' => "",
                            "audit_info"  => "",
                        ];
                    }
                } else {
                    $approval[] = [
                        'staff_id'    => enums::SYSTEM_STAFF_ID,
                        'staff_name'  => 'System',
                        'position'    => '',
                        'department'  => '',
                        'sort'        => 0,
                        'state_txt'   => "",
                        'status_code' => "",
                        "audit_info"  => "",
                    ];
                }

                $lastNodeKey = $uniqueKey;
                if (isset($data[$uniqueKey])) {
                    $lastNodeKey = $lastNodeKey . '-10';
                    $data[$lastNodeKey] = [
                        'state_txt'         => $this->formatStateCodeTxt($action, $node['id'], $nodeMark),
                        'status_code'       => $request->getState(),
                        'action_time'       => null,
                        "process_state"     => AuditLogModel::PROCESS_BAR_DARKNESS,
                        "approval_type"     => intval($nodeInfo[$node['id']] ?? 0), //或签 会签 抄送
                        "approval_info"     => $approval ?? [],
                    ];
                } else {
                    $data[$lastNodeKey] = [
                        'state_txt'         => $this->formatStateCodeTxt($action, $node['id'], $nodeMark),
                        'status_code'       => $request->getState(),
                        'action_time'       => null,
                        "process_state"     => AuditLogModel::PROCESS_BAR_DARKNESS,
                        "approval_type"     => intval($nodeInfo[$node['id']] ?? 0), //或签 会签 抄送
                        "approval_info"     => $approval ?? [],
                    ];
                }


                //处理节点后抄送,需要当前节点审批同意
                //审批同意、审批
                if (isset($ccBack[$lastNodeKey]) && $ccBack[$lastNodeKey]) {
                    $data[$lastNodeKey . '-1'] = $this->generateCcLog($lastNodeKey, $ccBack, $params);
                }
            }
        }

        if (empty($data)) {
            return [];
        }

        //4. 多个人的情况需要根据职级排序
        foreach ($data as &$item) {
            $count = count($item['approval_info']);
            if ($count > 1) {

                //如果同时存在同意的 + 待审批的，那么
                //已经审批同意的sort追加 100在排序因为
                //已经同意的在最上面
                $keySort = array_map(function ($v, $k) use($count) {
                    if (!in_array($v['status_code'], [enums::WF_ACTION_PENDING, enums::WF_ACTION_CC])) {
                        $v['sort'] = ($count - $k) * 100;
                    }
                    return $v;
                }, $item['approval_info'], array_keys($item['approval_info']));

                $keySort = array_column($keySort, 'sort');
                array_multisort($keySort, SORT_NUMERIC, SORT_DESC, $item['approval_info']);
            }
        }
        return $data;
    }

    /**
     * @description 获取详细审批日志
     * @param $request
     * @return array
     */
    public function getAuditLogDetail($request): array
    {
        (new AuditLogModel())->switchTableName($request->created_at, $request->getId());
        return AuditLogModel::find([
            'conditions' => 'request_id = :req_id:',
            'bind' => [
                'req_id' => $request->getId(),
            ],
            'order' => 'id asc',
        ])->toArray();
    }

    /**
     * 组织抄送日志
     * @param $node_id
     * @param $cc_list
     * @param $params
     * @return array
     */
    public function generateCcLog($node_id, $cc_list, $params): array
    {
        $nickNameList       = $params['nick_name'];
        $staffNameList      = $params['staff_name'];
        $staffJobTitleName  = $params['staff_job_title_name'];
        $staffDepartment    = $params['staff_department'];
        $staffJobTitleGrade = $params['staff_job_title_grade'];
        $processState       = $params['process_state'] ?? AuditLogModel::PROCESS_BAR_DARKNESS;
        $ccTime             = $params['cc_time'] ?? null;
        $suffix             = $this->getStateTranslateSuffix();

        $stateTxt = $this->getTranslation()->_(Enums::$wf_action_status[Enums::WF_ACTION_CC]);
        foreach ($cc_list[$node_id]['auditor_id'] as $staffId) {

            $cc[] = [
                'staff_id'    => $staffId,
                'staff_name'  => $this->formatStaffName($staffNameList[$staffId] ?? '', $nickNameList[$staffId] ?? ''),
                'position'    => $staffJobTitleName[$staffId] ?? '',
                'department'  => $staffDepartment[$staffId] ?? '',
                'sort'        => $staffJobTitleGrade[$staffId] ?? 0,
                'state_txt'   => $stateTxt,
                'status_code' => enums::WF_ACTION_CC,
                'audit_info'  => '',
            ];
        }
        return [
            'state_txt'         => $this->getTranslation()->_('audit_cc' . $suffix),
            'status_code'       => enums::WF_ACTION_CC,
            'action_time'       => $ccTime,
            "approval_type"     => enums::NODE_CC, //抄送
            "process_state"     => $processState,
            "approval_info"     => $cc ?? [],
        ];
    }

    /**
     * 获取当前审批人
     * @param $request
     * @param int $user 申请人
     * @return array
     * @throws InnerException
     */
    public function getCurrentAuditor($request, $user)
    {
        //获取指定节点的审批人
        $staff_ids = $this->getSpecifyNodeStaffIds($request->current_flow_node_id, $user);

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.staff_info_id,s.name as staff_name,s.nick_name,s.node_department_id,d.name as department_name,s.job_title,j.job_name as job_title_name');
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->leftjoin(HrJobTitleModel::class, 's.job_title = j.id', 'j');
        $builder->leftjoin(SysDepartmentModel::class, 's.node_department_id = d.id', 'd');
        if (is_array($staff_ids)) {
            $builder->inWhere('staff_info_id', $staff_ids);
        } else {
            $builder->andWhere('staff_info_id = :staff_id:', ['staff_id' => $staff_ids]);
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取当前审批人
     * @param $staff_ids
     * @return array
     */
    public function getCurrentAuditorV2($staff_ids): array
    {
        if (empty($staff_ids)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.staff_info_id,s.name as staff_name,s.nick_name,s.node_department_id,d.name as department_name,s.job_title,j.job_name as job_title_name');
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->leftjoin(HrJobTitleModel::class, 's.job_title = j.id', 'j');
        $builder->leftjoin(SysDepartmentModel::class, 's.node_department_id = d.id', 'd');
        if (is_array($staff_ids)) {
            $builder->inWhere('staff_info_id', $staff_ids);
        } else {
            $builder->andWhere('staff_info_id = :staff_id:', ['staff_id' => $staff_ids]);
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @description 获取指定节点的审批人
     * @param $node_id
     * @param $user
     * @return mixed
     * @throws InnerException
     */
    public function getSpecifyNodeStaffIds($node_id, $user)
    {
        $nodes = WorkflowNodeModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => [
                'id' => $node_id,
            ],
        ]);
        if (empty($nodes)) {
            return [];
        }

        //获取待审批人
        if (isset($nodes->auditor_id) && $nodes->auditor_id ) {
            $staff_ids = explode(',', $nodes->auditor_id);
            if ($this->staffRepository->isMultiStaffsOnJob($staff_ids) &&
                $nodes->approval_policy == Enums::WF_EMPTY_POLICY_DESIGNATE_OTHER) { //该节点审批人全部离职

                $staff_ids = explode(',', $nodes->specify_approver);
            }
        } else {
            $staff_ids = [];
        }

        if (empty($staff_ids)) {
            [$staff_ids, $ext] = $this->parseNode($nodes, $user);
            $staff_ids = !empty($staff_ids) ? explode(',', $staff_ids) : [];
        }
        return $staff_ids;
    }

    /**
     * @description 获取指定节点
     * @param $node_ids
     * @param bool $is_need_sort
     * @return array
     */
    public function getNodeByIds($node_ids, bool $is_need_sort = false): array
    {
        if (empty($node_ids)) {
            return [];
        }

        // Get nodes from database
        $nodes = WorkflowNodeModel::find([
            'conditions' => 'id in ({node_id:array})',
            'bind' => [
                'node_id' => $node_ids,
            ],
        ])->toArray();
        
        // Create a map for sorting
        if ($is_need_sort) {
            $nodeMap = [];
            foreach ($nodes as $node) {
                $nodeMap[$node['id']] = $node;
            }

            // Sort results according to the order in node_ids
            $sortedNodes = [];
            foreach ($node_ids as $id) {
                if (isset($nodeMap[$id])) {
                    $sortedNodes[] = $nodeMap[$id];
                }
            }
            return $sortedNodes;
        }

        return $nodes;
    }

    /**
     * 获取抄送节点包含员工ID
     * @param array $node_ids 审批节点
     * @return array
     */
    public function getCcNodeStaffIds(array $node_ids): array
    {
        if (empty($node_ids)) {
            return [[], [], []];
        }

        $ccNodeList = WorkflowNodeAccidentBusinessModel::find([
            'conditions' => 'node_id in ({node_id:array}) and business = 2',
            'bind' => [
                'node_id' => $node_ids,
            ],
            'columns' => "node_id,type,auditor_id,status",
        ])->toArray();
        if (empty($ccNodeList)) {
            return [[], [], []];
        }

        $ccNodeToBack = [];
        $totalStaffIds = [];
        foreach ($ccNodeList as $node) {
            $nodeId = $node['node_id'];
            $nodeIds = explode(',', $node['auditor_id']);

//            if ($node['type'] == 1) { //1=触发节点时 2=节点通过后
//                if (isset($result[$nodeId])) {
//                    $nodeIds = array_merge($ccNodeToFront[$nodeId]['auditor_id'], $nodeIds);
//                }
//                $ccNodeToFront[$nodeId] = $nodeIds;
//            } else {
//                if (isset($result[$nodeId])) {
//                    $nodeIds = array_merge($ccNodeToBack[$nodeId]['auditor_id'], $nodeIds);
//                }
                $ccNodeToBack[$nodeId] = [
                    'status'     => $node['status'],
                    'type'       => $node['type'],
                    'auditor_id' => $nodeIds,
                ];
//            }
            $totalStaffIds = array_merge($totalStaffIds, $nodeIds);
        }
        return [[], $ccNodeToBack, $totalStaffIds];
    }

    /**
     * 追加昵称
     * @param $staff_name
     * @param $nike_name
     * @return string
     */
    public function formatStaffName($staff_name, $nike_name): string
    {
        if (isset($nike_name) && $nike_name) {
            return sprintf("%s(%s)", $staff_name, $nike_name);
        } else {
            return $staff_name;
        }
    }

    /**
     * 格式化日期
     * @param $datetime
     * @param int $datetime_formatter
     * @return string
     */
    public function formatDateTime($datetime, int $datetime_formatter = AuditListEnums::DATETIME_FORMATTER_DEFAULT): string
    {
        $curTime = gmdate("Y-m-d H:i:s", strtotime($datetime) + $this->config->application->add_hour * 3600);
        if ($datetime_formatter == AuditListEnums::DATETIME_FORMATTER_DEFAULT) {
             return $curTime;
        } else if ($datetime_formatter == AuditListEnums::DATETIME_FORMATTER_TIMESTAMP) {
            return strtotime($curTime);
        } else {
            return "";
        }
    }

    /**
     * @description 翻译state
     * @param $state_code
     * @param null $node_id
     * @param array $nodeMark
     * @return string
     */
    public function formatStateCodeTxt($state_code, $node_id = null, $nodeMark = []): string
    {
        $t      = $this->getTranslation();
        $suffix = $this->getStateTranslateSuffix();
        switch ($state_code) {
            case enums::WF_ACTION_CREATE_CANCEL:
            case enums::WF_ACTION_CANCEL:
                $text = $t->_('audit_cancel' . $suffix); //申请撤销
                break;
            case enums::WF_ACTION_CREATE:
            case enums::WF_ACTION_EDIT_RECREATE:
                $text = $t->_('send_request' . $suffix); //发起申请
                break;
            case enums::WF_ACTION_CC:
                $text = $t->_('audit_cc' . $suffix); //抄送人
                break;
            case enums::WF_ACTION_FINAL_COMPLETED:
                $text = $t->_('audit_final' . $suffix); //结束
                break;
            case enums::WF_ACTION_CONFIRM_PENDING:
            case enums::WF_ACTION_CONFIRM_PASS:
            case enums::WF_ACTION_CONFIRM_REJECT:
            case enums::WF_ACTION_CONFIRM_CANCEL:
            case enums::WF_ACTION_CONFIRM_OVERTIME:
                $text = $t->_('audit_confirm' . $suffix); //信息确认
                break;
            default:
                //只有审批节点才走标记
                $text = !empty($nodeMark[$node_id]) && $nodeMark[$node_id] == WorkflowNodeModel::NODE_MARK_AUDIT
                    ? $t->_('audit_audit' . $suffix)
                    : $t->_('audit_apply' . $suffix);
                break;
        }
        return $text;
    }

    /**
     * 获取翻译key 前缀
     * @return string
     */
    protected function getStateTranslateSuffix(): string
    {
        return $this->isUnpaid ? '_unpaid' : '';
    }

    /**
     * @description 获取翻译 key
     * @return string
     */
    protected function getStateTranslateKey(): string
    {
        return $this->isUnpaid ? 'audit_status_unpaid_' : 'audit_status.';
    }

    /**
     * @description 获取翻译组
     * @return string[]
     */
    protected function getStateTranslateGroup(): array
    {
        if ($this->isUnpaid) {
            return Enums::$wf_action_status_unpaid;
        } else {
            return Enums::$wf_action_status;
        }
    }

    /**
     * 获取C level级别
     * @param $auditor_type
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|string
     */
    public function findCLevelManager($auditor_type)
    {
        if ($auditor_type == enums::WF_NODE_CEO) {
            $id = 999;
        } else if ($auditor_type == enums::WF_NODE_COO) {
            $id = 222;
        } else if ($auditor_type == enums::WF_NODE_CFO) {
            $id = 333;
        } else if ($auditor_type == enums::WF_NODE_CPO) {
            $id = 444;
        } else {
            $id = 222;
        }

        $auditor = SysDepartmentModel::findFirst([
            'conditions' => "id = :department_id:",
            'bind' => [
                'department_id'  => $id,
            ],
        ]);
        if ($auditor) {

            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id: and state = 1 and formal = 1 ',
                'bind' => [
                    'staff_id' => $auditor->manager_id,
                ],
            ]);

            return !empty($staffInfo) ? $staffInfo->staff_info_id : "";
        }
        return "";
    }

    /**
     * 获取指定部门的部门负责人
     * @param $department_id
     * @return array
     */
    public function findSpecifiyDepartmentManager($department_id): array
    {
        //获取申请人当前部门信息
        $departmentInfo = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1 and deleted = 0',
            'bind' => [
                1 => $department_id,
            ],
        ]);
        if ($departmentInfo) {
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id: and state = 1 and formal = 1 ',
                'bind' => [
                    'staff_id' => $departmentInfo->manager_id,
                ],
            ]);

            return [!empty($staffInfo) ? $staffInfo->staff_info_id : "", $department_id];
        }
        return ['', $department_id];
    }

    /**
     * 申请人上级或上上级，有Sales Manager职位
     * @param $staff_id
     * @return string
     */
    public function findSpecifiyJobTitle($staff_id): string
    {
        if (empty($staff_id)) {
            return "";
        }

        //获取申请人的上2级上级
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.staff_info_id,s.value as staff_high_1,b.value as staff_high_2");
        $builder->from(['s' => HrStaffItemsModel::class]);
        $builder->join(HrStaffItemsModel::class,"s.value = b.staff_info_id and b.item = 'MANGER'",'b');
        $builder->andWhere('s.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_id]);
        $builder->andWhere('s.item = :item:', ['item' => 'MANGER']);
        $staffList = $builder->getQuery()->execute()->getFirst()->toArray();

        $staffArr = [$staffList['staff_high_1'] ?? null, $staffList['staff_high_2'] ?? null];
        $staffArr = array_filter($staffArr);
        if (empty($staffArr)) {
            return "";
        }

        //获取Sales Manager职位
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id IN({staff_info_arr:array}) and job_title = 83 and state = 1 and formal = 1 and is_sub_staff = 0',
            'bind' => [
                'staff_info_arr' => $staffArr,
            ],
            'columns' => "staff_info_id",
        ]);
        return $staffInfo->staff_info_id ?? '';
    }

    /**
     * 接收审批节点动态传入的审批人
     * @param int $auditor_level
     * @param array $extend
     * @mixed
     */
    public function getNodeDynamicAuditor(int $auditor_level, array $extend)
    {
        // 审批人节点下标 比 审批人级别 小 1
        return $extend['node_auditor'][$auditor_level-1] ?? '';
    }

    /**
     * 获取当前节点还存在多少在职的审批人
     * @param $type
     * @param $value
     * @param $currentNode
     * @return int
     */
    public function checkCounterSignApproval($type, $value, $currentNode): int
    {
        //获取当前节点待审批的人数
        $approvalList = AuditApprovalModel::find([
            'conditions' => 'flow_node_id = :flow_node_id: and biz_type = :type: and biz_value = :value: and state = 1 and deleted = 0',
            'bind'       => [
                'flow_node_id' => $currentNode->getId(),
                'type'        => $type,
                'value'       => $value,
            ],
            'columns'    => "approval_id",
        ]);

        $staff_info_id = [];
        foreach ($approvalList as $item){
            $staff_info_id[] = $item->approval_id;
        }
        if (empty($staff_info_id)) {
            return 0;
        }

        //获取在职的待审批人数
        $onJobState = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in({staff_info_id:array}) and state = 1',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
            ],
            'columns'    => "staff_info_id",
        ])->toArray();

        return count($onJobState) ?? 0;
    }
	
	/**
	 * 获取指定部门的hrbp
	 * @param     $department_id
	 * @param     $extend array
	 * @param int $level
	 * @return string
	 */
    public function findHRBP($department_id, array $extend = [], int $level = 0) {
        return $this->findHRBPV2($department_id,$extend,$level);
    }


    /**
     * 获取指定部门的hrbp
     * @param $department_id
     * @param $extend
     * @param int $level
     * @return string
     */
    private function findHRBPV2($department_id, $extend, $level = 0) {
        $storeId       = $extend['store_id'] ?? '';
        $department_id = $extend['transfer_department_id'] ?? $department_id;
        //如果 $level 存在  并且 $extend['Array_department_id'] 存在 则获取 部门的 hrbp
        if (!empty($level) && isset($extend['Array_department_id'][$level - 1])) {
            $department_id = $extend['Array_department_id'][$level - 1];
        }
        //如果 $level 存在  并且 $extend['Array_store_id'] 存在 则获取 网点的 hrbp
        if (!empty($level) && isset($extend['Array_store_id'][$level - 1])) {
            $storeId = $extend['Array_store_id'][$level - 1];
        }
        $storeInfo = SysStoreModel::findFirst([
                                                  'conditions' => 'id = :store_id:',
                                                  'bind'       => ['store_id' => $storeId],
                                              ]);
        $region    = isset($storeInfo->manage_region) && !empty($storeInfo->manage_region) ? $storeInfo->manage_region : 0;
        $piece     = isset($storeInfo->manage_piece) && !empty($storeInfo->manage_piece) ? $storeInfo->manage_piece : 0;
        $hrbp      = [];
        do {
            //网点不能管辖总部网点
            if (!empty($storeId) && $storeId != '-1') {
                //[1]先获取网点对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns("position.staff_info_id");
                $builder->from(['position' => ByHrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, "position.staff_info_id = staff.staff_info_id", 'staff');
                $builder->leftjoin(HcmStaffManageRegionModel::class, "position.staff_info_id = region.staff_info_id", 'region');
                //在职 在编的
                $builder->Where('staff.state = 1 and staff.formal = 1 and position.position_category = :hrbp: and region.deleted = 0 and region.category = :category:', ['hrbp' => ByHrStaffInfoPositionModel::HRBP,'category' => HcmStaffManageRegionModel::CATEGORY_5]);
                $builder->inWhere('region.store_id', [HcmStaffManageRegionModel::STORE_ALL, $storeId]);
                $hrbp = $builder->getQuery()->execute()->toArray();
                if (!empty($hrbp)) {
                    break;
                }

            }

            if (!empty($piece)) {

                //[2]获取片区对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns("position.staff_info_id");
                $builder->from(['position' => ByHrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, "position.staff_info_id = staff.staff_info_id", 'staff');
                $builder->leftjoin(HcmStaffManageRegionModel::class, "position.staff_info_id = region.staff_info_id", 'region');
                //在职 在编的
                $builder->Where('staff.state = 1 and staff.formal = 1 and position.position_category = :hrbp: and region.deleted = 0 and region.category = :category: and region.value = :value:', ['hrbp' => ByHrStaffInfoPositionModel::HRBP, 'category' => HcmStaffManageRegionModel::CATEGORY_4, 'value' => $piece]);
                $hrbp = $builder->getQuery()->execute()->toArray();
                if (!empty($hrbp)) {
                    break;
                }
            }

            if (!empty($region)) {
                //[3]获取大区对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns("position.staff_info_id");
                $builder->from(['position' => ByHrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, "position.staff_info_id = staff.staff_info_id", 'staff');
                $builder->leftjoin(HcmStaffManageRegionModel::class, "position.staff_info_id = region.staff_info_id", 'region');
                //在职 在编的
                $builder->Where('staff.state = 1 and staff.formal = 1 and position.position_category = :hrbp: and region.deleted = 0 and region.category = :category: and region.value = :value:', ['hrbp' => ByHrStaffInfoPositionModel::HRBP, 'category' => HcmStaffManageRegionModel::CATEGORY_3, 'value' => $region]);
                $hrbp = $builder->getQuery()->execute()->toArray();
                if (!empty($hrbp)) {
                    break;
                }
            }

            if (!empty($department_id)) {
                //[4]获取部门对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns("position.staff_info_id");
                $builder->from(['position' => ByHrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, "position.staff_info_id = staff.staff_info_id", 'staff');
                $builder->leftjoin(HcmStaffManageRegionModel::class, "position.staff_info_id = region.staff_info_id", 'region');
                //在职 在编的
                $builder->Where('staff.state = 1 and staff.formal = 1 and position.position_category = :hrbp: and region.deleted = 0 and region.category = :category: and region.value = :value:', ['hrbp' => ByHrStaffInfoPositionModel::HRBP, 'category' => HcmStaffManageRegionModel::CATEGORY_2, 'value' => $department_id]);
                $hrbp = $builder->getQuery()->execute()->toArray();
            }
        } while (0);


        $staffInfoIds = array_unique(array_filter(array_column($hrbp, 'staff_info_id')));
        $staffInfoIds = $staffInfoIds ? implode(',', $staffInfoIds) : "";
        return $staffInfoIds;
    }


    /**
     * @description 获取指定角色在对管辖范围下的在人
     * @param $department_id
     * @param $extend
     * @param int $level
     * @param int $role_id 角色 id
     * @return string
     */
    public function findJurisdictionAreaStaffIds($department_id, $extend, $level = 0, $role_id = 42)
    {
        $storeId = $extend['store_id'] ?? '';
        $department_id = $extend['transfer_department_id'] ?? $department_id;
        //如果 $level 存在  并且 $extend['Array_department_id'] 存在 则获取 部门的 hrbp
        if(!empty($level) &&  isset($extend['Array_department_id'][$level-1])){
            $department_id = $extend['Array_department_id'][$level-1];
        }
        //如果 $level 存在  并且 $extend['Array_store_id'] 存在 则获取 网点的 hrbp
        if(!empty($level) &&  isset($extend['Array_store_id'][$level-1])){
            $storeId = $extend['Array_store_id'][$level-1];
        }
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind'       => ['store_id' => $storeId],
        ]);
        $region = !empty($storeInfo->manage_region) ? $storeInfo->manage_region: 0;
        $piece  = !empty($storeInfo->manage_piece) ? $storeInfo->manage_piece: 0;
        $hrbp   = [];

        $staff_ids = [];
        do {
            //网点不能管辖总部网点
            if (!empty($storeId) && $storeId != '-1') {
                //[1]先获取网点对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns("position.staff_info_id");
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, "position.staff_info_id = staff.staff_info_id", 'staff');
                $builder->leftjoin(HcmStaffManageRegionModel::class, "position.staff_info_id = region.staff_info_id", 'region');
                //在职 在编的
                $builder->Where('staff.state = 1 and staff.formal = 1 and position.position_category = :hrbp: and region.deleted = 0 and region.category = :category:', ['hrbp' => $role_id,'category' => HcmStaffManageRegionModel::CATEGORY_5]);
                $builder->inWhere('region.store_id', [HcmStaffManageRegionModel::STORE_ALL, $storeId]);
                $hrbp = $builder->getQuery()->execute()->toArray();
                if (!empty($hrbp)) {
                    break;
                }

            }

            if (!empty($piece)) {

                //[2]获取片区对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns("position.staff_info_id");
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, "position.staff_info_id = staff.staff_info_id", 'staff');
                $builder->leftjoin(HcmStaffManageRegionModel::class, "position.staff_info_id = region.staff_info_id", 'region');
                //在职 在编的
                $builder->Where('staff.state = 1 and staff.formal = 1 and position.position_category = :hrbp: and region.deleted = 0 and region.category = :category: and region.value = :value:', ['hrbp' => $role_id, 'category' => HcmStaffManageRegionModel::CATEGORY_4, 'value' => $piece]);
                $hrbp = $builder->getQuery()->execute()->toArray();
                if (!empty($hrbp)) {
                    break;
                }
            }

            if (!empty($region)) {
                //[3]获取大区对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns("position.staff_info_id");
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, "position.staff_info_id = staff.staff_info_id", 'staff');
                $builder->leftjoin(HcmStaffManageRegionModel::class, "position.staff_info_id = region.staff_info_id", 'region');
                //在职 在编的
                $builder->Where('staff.state = 1 and staff.formal = 1 and position.position_category = :hrbp: and region.deleted = 0 and region.category = :category: and region.value = :value:', ['hrbp' => $role_id, 'category' => HcmStaffManageRegionModel::CATEGORY_3, 'value' => $region]);
                $hrbp = $builder->getQuery()->execute()->toArray();
                if (!empty($hrbp)) {
                    break;
                }
            }

            if (!empty($department_id)) {
                //[4]获取部门对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns("position.staff_info_id");
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, "position.staff_info_id = staff.staff_info_id", 'staff');
                $builder->leftjoin(HcmStaffManageRegionModel::class, "position.staff_info_id = region.staff_info_id", 'region');
                //在职 在编的
                $builder->Where('staff.state = 1 and staff.formal = 1 and position.position_category = :hrbp: and region.deleted = 0 and region.category = :category: and region.value = :value:', ['hrbp' => $role_id, 'category' => HcmStaffManageRegionModel::CATEGORY_2, 'value' => $department_id]);
                $hrbp = $builder->getQuery()->execute()->toArray();
            }

        } while(0);

        $staffInfoIds = array_unique(array_filter(array_column($hrbp, 'staff_info_id')));
        $staffInfoIds = $staffInfoIds ? implode(',', $staffInfoIds) : "";
        return $staffInfoIds;
    }

    /**
     * @description:根据部门管辖范围 查找固定角色的人
     * @param int $department_id 部门id
     * @param int $role_id 角色 id
     * @return    array :['17245','17245']
     * <AUTHOR> L.J
     * @time       : 2022/10/11 17:35
     */
    public function findJurisdictionDepartment( $department_id, $role_id ) {
        $position_category = [];
        if (empty($department_id) || empty($role_id)) {
            return $position_category;
        }
        //获取部门对应的角色
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('position.staff_info_id');
        $builder->from(['position' => ByHrStaffInfoPositionModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
        $builder->leftjoin(HrStaffManageDepartmentModel::class, 'position.staff_info_id = region.staff_info_id', 'region');
        //在职 在编的
        $builder->Where(
            'staff.formal = 1 and position.position_category = :position_category: and region.deleted = 0 and region.type = :type: and region.department_id = :department_id:',
            ['position_category' => $role_id,'department_id'=>$department_id,'type'=>HrStaffManageDepartmentModel::$type_1]
        );
        $builder->inWhere('staff.state', [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPENSION]);
        $position_category = $builder->getQuery()->execute()->toArray();

        //获取部门的部门链
        $department  = SysDepartmentModel::findFirst($department_id);
        $ancestry_v3 = isset($department->ancestry_v3) ? explode('/', $department->ancestry_v3) : [];
        //获取是否为包含子部门的管辖范围
        if (!empty($ancestry_v3)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('position.staff_info_id');
            $builder->from(['position' => ByHrStaffInfoPositionModel::class]);
            $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
            $builder->leftjoin(HrStaffManageDepartmentModel::class, 'position.staff_info_id = region.staff_info_id', 'region');
            //在职 在编的  并且包含子部门的
            $builder->Where(
                'staff.formal = 1 and position.position_category = :position_category: and region.deleted = 0 and region.is_include_sub = :is_include_sub: and region.type = :type: ',
                ['position_category' => $role_id,'type'=>HrStaffManageDepartmentModel::$type_1,'is_include_sub'=>HrStaffManageDepartmentModel::$is_include_sub_1]
            );
            $builder->inWhere('staff.state', [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPENSION]);
            $builder->inWhere('region.department_id', $ancestry_v3);
            $position_category_include_sub = $builder->getQuery()->execute()->toArray();
            if (!empty($position_category_include_sub)) {
                $position_category = array_merge($position_category, $position_category_include_sub);
            }
        }
        return empty($position_category) ? $position_category :  array_values(array_unique(array_filter(array_column($position_category, 'staff_info_id'))));
    }

    /**
     * @description:根据大区管辖范围 查找固定角色的人
     * @param int $region_id 大区id
     * @param int $role_id 角色 id
     * @return    array :['17245','17245']
     * <AUTHOR> L.J
     * @time       : 2022/10/11 17:39
     * SELECT position.staff_info_id FROM hr_staff_info_position as position
     * LEFT JOIN hr_staff_info as staff ON position.staff_info_id = staff.staff_info_id
     * LEFT JOIN hr_staff_manage_region as  region ON position.staff_info_id = region.staff_info_id
     * where staff.state = 1 and staff.formal = 1 and position.position_category = 68 and region.deleted = 0 and region.type = 1 and region.region_id in ('-2','1');
     *
     */
    public function findJurisdictionRegion($region_id,$role_id){
        $position_category = [];
        if (empty($region_id) || empty($role_id)) {
            return $position_category;
        }
        //获取大区对应的角色的人
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('position.staff_info_id');
        $builder->from(['position' => ByHrStaffInfoPositionModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
        $builder->leftjoin(HrStaffManageRegionModel::class, 'position.staff_info_id = region.staff_info_id', 'region');
        //在职 在编的
        $builder->Where(
            'staff.formal = 1 and position.position_category = :position_category: and region.deleted = 0 and region.type = :type: ',
            ['position_category' => $role_id,'type'=>HrStaffManageRegionModel::$type_1]
        );
        $builder->inWhere('staff.state', [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPENSION]);
        $builder->inWhere('region.region_id', [HrStaffManageRegionModel::$all_id, $region_id]);
        $position_category = $builder->getQuery()->execute()->toArray();
        return empty($position_category) ? $position_category :  array_values(array_unique(array_filter(array_column($position_category, 'staff_info_id'))));
    }

    /**
     * @description:根据片区管辖范围 查找固定角色的人
     * @param int $piece_id 片区id
     * @param int $role_id 角色 id
     * @return    array :['17245','17245']
     * <AUTHOR> L.J
     * @time       : 2022/10/11 17:39
     */
    public function findJurisdictionPiece($piece_id,$role_id)
    {
        $position_category = [];
        if (empty($piece_id) || empty($role_id)) {
            return $position_category;
        }
        //获取片区对应的HRBP
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('position.staff_info_id');
        $builder->from(['position' => ByHrStaffInfoPositionModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
        $builder->leftjoin(HrStaffManagePieceModel::class, 'position.staff_info_id = region.staff_info_id', 'region');
        //在职 在编的
        $builder->Where(
            'staff.formal = 1 and position.position_category = :position_category: and region.deleted = 0 and region.type = :type: ',
            ['position_category' => $role_id,'type'=>HrStaffManagePieceModel::$type_1]
        );
        $builder->inWhere('staff.state', [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPENSION]);
        $builder->inWhere('region.piece_id', [HrStaffManagePieceModel::$all_id, $piece_id]);
        $position_category = $builder->getQuery()->execute()->toArray();

        return empty($position_category) ? $position_category : array_values(array_unique(array_filter(array_column($position_category, 'staff_info_id'))));
    }

    /**
     * 获取网点负责人
     * @param $sys_store_id
     * @param array $extend
     */
    public function findHeadOfStore($sys_store_id, array $extend)
    {
        if (empty($sys_store_id)) {
            return "";
        }

        $storeInfo = SysStoreModel::findFirst([
            'conditions' => "id = :store_id: and state = 1",
            'bind' => [
                'store_id' => $sys_store_id,
            ],
            'columns' => 'manager_id',
        ]);
        return isset($storeInfo->manager_id) ? $storeInfo->manager_id: "";
    }

    /**
     * 获取AM 加强版
     * 在传入时，AM_store_ids指定多个网点的id
     *
     * 在解析的时候，根据节点的 auditor_level 获取对应的网点ID
     *
     * 比如 AM_store_ids => ['TH001', 'TH002']
     * 节点配置为
     *      node_id    |    auditor_type  |  auditor_level
     *      ----------------------------------------------
     *       0001      |          8       |         1
     *      ----------------------------------------------
     *       0002      |          8       |         2
     *
     * 则节点
     *      0001 的AM对应TH001网点的AM
     *      0002 的AM对应TH002网点的AM
     *
     * @param $sys_store_id
     * @param $level
     * @param array $extend
     * @return string
     */
    public function findAMEx($sys_store_id, $level, array $extend)
    {
        //[1]如果存在指定网点，则获取指定网点的DM
        $extendStoreId = $extend['AM_store_ids'] ?? [];
        if (!empty($extendStoreId)) {
            //此处有兼容处理，兼容下面2种情况
            //1.在可视化配置时，不会指定{$level}
            //2.转岗审批会一次指定2个网点，并在审批流中配置{$level}
            $storeId = isset($level) ? $extendStoreId[$level - 1] : $extendStoreId[0];
        } else {
            $storeId = $sys_store_id;
        }

        if (!$storeId) {
            return "";
        }

        //查找指定网点的DM
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.*');
        $builder->from(['ss' => SysStoreModel::class]);
        $builder->join(SysManageRegionModel::class, 'smr.id = ss.manage_region', 'smr');
        $builder->join(HrStaffInfoModel::class, 'smr.manager_id = hsi.staff_info_id', 'hsi');
        $builder->where("ss.id = :store_id: and ss.state = 1 and hsi.state = 1", [
            'store_id' => $storeId,
        ]);
        $returnData = $builder->getQuery()->execute();
        if (empty($returnData)) {
            return "";
        } else {
            $returnData = $returnData->getFirst();
            return $returnData->staff_info_id ?? "";
        }
    }

    /**
     * 获取DM
     * @param $sys_store_id
     * @param $level
     * @param array $extend
     * @return string
     */
    public function findDMEx($sys_store_id, $level, array $extend)
    {
        //[1]如果存在指定网点，则获取指定网点的DM
        $extendStoreId = $extend['DM_store_ids'] ?? [];
        if (!empty($extendStoreId)) {
            //此处有兼容处理，兼容下面2种情况
            //1.在可视化配置时，不会指定{$level}
            //2.转岗审批会一次指定2个网点，并在审批流中配置{$level}
            $storeId = isset($level) ? $extendStoreId[$level - 1] : $extendStoreId[0];
        } else {
            $storeId = $sys_store_id;
        }

        if (!$storeId) {
            return "";
        }

        //查找指定网点的DM
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.*');
        $builder->from(['ss' => SysStoreModel::class]);
        $builder->join(SysManagePieceModel::class, 'smp.id = ss.manage_piece', 'smp');
        $builder->join(HrStaffInfoModel::class, 'smp.manager_id = hsi.staff_info_id', 'hsi');
        $builder->where("ss.id = :store_id: and ss.state = 1 and hsi.state = 1", [
            'store_id' => $storeId,
        ]);
        $returnData = $builder->getQuery()->execute()->getFirst();

        if (empty($returnData)) {
            return "";
        } else {
            return $returnData->staff_info_id;
        }
    }

    /**
     * 获取网点负责人
     * @param \Phalcon\Mvc\Phalcon\Mvc\Model $sys_store_id
     * @param $level
     * @param array $extend
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|string
     */
    public function findHeadOfStoreEx($sys_store_id, $level, array $extend)
    {
        //[1]如果存在指定网点，则获取指定网点的DM
        $extendStoreId = $extend['Head_store_ids'] ?? [];
        if (!empty($extendStoreId)) {
            $storeId = $extendStoreId[$level - 1];
        } else {
            $storeId = $sys_store_id;
        }

        if (!$storeId) {
            return "";
        }

        $storeInfo = SysStoreModel::findFirst([
            'conditions' => "id = :store_id: and state = 1",
            'bind' => [
                'store_id' => $storeId,
            ],
            'columns' => 'manager_id',
        ]);
        return $storeInfo->manager_id ?? "";
    }

    /**
     * 获取多个节点的部门负责人
     * @param $department_id
     * @param $level
     * @param array $extend
     * @return array
     */
    public function findDepartmentManagerEx($department_id, $level, array $extend): array
    {
        //[1]如果存在指定部门，则获取指定部门的负责人
        $extendDepartmentId = $extend['Array_department_id'] ?? '';
        if (!empty($extendDepartmentId)) {
            $departmentId = $extendDepartmentId[$level - 1];;
        } else {
            $departmentId = $department_id;
        }

        if (!$departmentId) {
            return [];
        }

        //[3]查询指定级别
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.id,s.manager_id,s.level');
        $builder->from(['s' => FleSysDepartmentModel::class]);
        $builder->leftJoin(StaffInfoModel::class, 'si.id = s.manager_id', 'si');
        $builder->where('s.id = :department_id: and si.state = 1', ['department_id' => $departmentId]);
        $depInfo = $builder->getQuery()->execute();

        if (isset($depInfo) && $depInfo) {
            $depInfo = $depInfo->getFirst();
        }

        return [isset($depInfo) && $depInfo ? $depInfo->manager_id : '', $departmentId];
    }

    /**
     * 找网点正主管
     * 如果找不到则找申请人的上2级上级
     *
     * @param $storeId
     * @param $user
     * @param $extend
     * @return mixed|string
     */
    public function findStoreManagerOrHigher($storeId,$user,$extend)
    {
        $extendStoreId = $extend['store_id'] ?? '';
        if (!empty($extendStoreId)) {
            $storeId = $extendStoreId;
        }

        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'sys_store_id = ?1 and job_title = 16 and state = 1 and formal = 1 and is_sub_staff = 0',
            'bind' => [
                1 => $storeId,
            ],
        ]);
        $staff_id = isset($staffInfo) && $staffInfo ? $staffInfo->staff_info_id : '';
        if(!$staff_id){
            //获取申请人的上2级上级
            $builder = $this->modelsManager->createBuilder();
            $builder->columns("s.staff_info_id,s.value as staff_high_1,b.value as staff_high_2");
            $builder->from(['s' => HrStaffItemsModel::class]);
            $builder->join(HrStaffItemsModel::class,"s.value = b.staff_info_id and b.item = 'MANGER'",'b');
            $builder->andWhere('s.staff_info_id = :staff_info_id:', ['staff_info_id' => $user]);
            $builder->andWhere('s.item = :item:', ['item' => 'MANGER']);
            $staffList = $builder->getQuery()->execute()->getFirst();
            $staffList = !empty($staffList) ? $staffList->toArray() : [];
            if(!empty($staffList['staff_high_1'])){
                $staff_id = $staffList['staff_high_1'];
            }elseif(!empty($staffList['staff_high_2'])){
                $staff_id = $staffList['staff_high_2'];
            } else{
                $staff_id= '';
            }
        }
        $this->getDI()->get('logger')->write_log("penalty_staff_id" . $staff_id ,'info');
        return $staff_id;
    }

    public function findStoreJobStaff($store_id,$job_title, $extend){

        $store_id = $extend['store_id'] ?? $store_id;
        $staffs = HrStaffInfoModel::find([
            'conditions' => 'sys_store_id = ?1 and job_title = ?2 and state = 1 and formal = 1 and is_sub_staff = 0',
            'bind' => [
                1 => $store_id,
                2 => $job_title,
            ],
        ])->toArray();

        $this->getDI()->get('logger')->write_log("workflow_asset {$store_id}_{$job_title} ".json_encode($staffs) ,'info');
        if(empty($staffs))
            return [];

        $staffIds = [];
        foreach ($staffs as $staff) {
            $staffIds[] = $staff['staff_info_id'];
        }

        return $staffIds ? implode(",", $staffIds) : "";
    }

    /**
     * 查找部门的clevel
     *
     * @param $departmentId
     * @param $extend
     * @return array
     */
    public function findCLevelsManager($departmentId, $extend)
    {
        //[1]如果存在指定部门，则获取指定部门的负责人
        $extendDepartmentId = $extend['department_id'] ?? '';
        if (!empty($extendDepartmentId)) {
            $departmentId = $extendDepartmentId;
        }

        if (!$departmentId) {
            return ['', ''];
        }
        $departmentInfo = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1',
            'bind' => [
                1 => $departmentId,
            ],
        ]);

        if (empty($departmentInfo)) {
            return ['', ''];
        }
        $depmentList = explode('/', $departmentInfo->ancestry_v3);

        // 找type=4clevel的
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.id,s.manager_id,s.level');
        $builder->from(['s' => SysDepartmentModel::class]);
        $builder->inWhere('s.id', $depmentList);
        $builder->andWhere('s.type = :type:', ['type' => 4]);
        $depInfo = $builder->getQuery()->execute()->getFirst();
        return [$depInfo->manager_id ?? '', $depInfo->id ?? ''];

    }

    /**
     * 部门属于BU找 BU负责人 ； 如果部门属于cLevel找 clevel负责人； 否则找GroupCEO
     *
     * @param $departmentId
     * @param $extend
     * @return array
     */
    public function findFirstBuDepartmentManager($departmentId, $extend): array
    {
        //[1]如果存在指定部门，则获取指定部门的负责人
        $extendDepartmentId = $extend['department_id'] ?? '';
        if (!empty($extendDepartmentId)) {
            $departmentId = $extendDepartmentId;
        }

        if (!$departmentId) {
            return [];
        }
        $departmentInfo = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1',
            'bind' => [
                1 => $departmentId,
            ],
        ]);
        if (empty($departmentInfo)) {
            return [];
        }

        // 找type=1bu的
        $depmentList = explode('/', $departmentInfo->ancestry_v3);

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.id,s.manager_id,s.level');
        $builder->from(['s' => SysDepartmentModel::class]);
        $builder->inWhere('s.id', $depmentList);
        $builder->andWhere('s.type = :type:', ['type' => 1]);
        $depInfo = $builder->getQuery()->execute()->getFirst();
        $manager_id = $depInfo->manager_id ?? '';
        if ($manager_id) {
            return [$manager_id, $depInfo->id];
        }

        // 找type=4clevel的
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.id,s.manager_id,s.level');
        $builder->from(['s' => SysDepartmentModel::class]);
        $builder->inWhere('s.id', $depmentList);
        $builder->andWhere('s.type = :type:', ['type' => 4]);
        $depInfo = $builder->getQuery()->execute()->getFirst();
        $manager_id = $depInfo->manager_id ?? '';
        if ($manager_id) {
            return [$manager_id, $depInfo->id];
        }

        // 找type=5的
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.id,s.manager_id,s.level');
        $builder->from(['s' => SysDepartmentModel::class]);
        $builder->inWhere('s.id', $depmentList);
        $builder->andWhere('s.type = :type:', ['type' => 5]);
        $depInfo = $builder->getQuery()->execute()->getFirst();
        $manager_id = $depInfo->manager_id ?? '';
        if ($manager_id) {
            return [$manager_id, $depInfo->id];
        }

        return [];
    }

    /**
     *  用人部门是BU级别，找当前BU负责人；如果是 C-Level级别 找当前c-level负责人；如果是普通部门则直接找一级部门负责人
     */
    public function findFirstLevelDepartmentManager($departmentId, $extend) {
        //[1]如果存在指定部门，则获取指定部门的负责人
        $extendDepartmentId = $extend['department_id'] ?? '';
        if (!empty($extendDepartmentId)) {
            $departmentId = $extendDepartmentId;
        }

        if (!$departmentId) {
            return "";
        }

        //[2]获取申请人当前部门信息
        $departmentInfo = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1',
            'bind' => [
                1 => $departmentId,
            ],
        ]);

        if (empty($departmentInfo)) {
            return "";
        }

        switch ($departmentInfo->type) {
            case 2: //公司类型部门
            case 3: //组织下的部门
                //[2]获取部门链
                $depmentList = explode('/', $departmentInfo->ancestry_v3);
                //[3]查询指定级别
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('s.id,s.manager_id,s.level');
                $builder->from(['s' => SysDepartmentModel::class]);
                $builder->inWhere('s.id', $depmentList);
                $builder->andWhere('s.level = :level:', ['level' => 1]);
                $depInfo = $builder->getQuery()->execute()->getFirst();
                $manager_id = $depInfo->manager_id ?? '';
                break;
            case 1: //BU
            case 4: //c-level
            case 5: //Group Boss
                $manager_id = $departmentInfo->manager_id ?? '';
                break;
            default:
                $manager_id = $departmentInfo->manager_id ?? '';
        }

        return $manager_id;
    }


	/**
	 * @description:获取属于Hub Headquarter的 xx职位的人
	 *
	 * @param $job_title  str 职位 id ,号分隔
	 * @param $extend   'job_title' => 0,
						'node_department_id' => 0,
						'sys_store_id' => 0,
						'ancestry' => '',
						'category' => '',
						'manage_region' => '',
						'k1' => '',
	*
	 * @return     :$staffIds str ,号分隔
	 * <AUTHOR> L.J
	 * @time       : 2021/8/17 14:38
     *
     * 12257【BY|TH】HUB与ASSET资产与耗材审批流变更P0需求
     * https://l8bx01gcjr.feishu.cn/docs/doccneIwW7NFOD4MxPT51vDgU7b#ecd2WF
     * 由原来的HubHeadquarter部门下的指定职位修改为Hub Admin下的所有人可审批
	 */
	public  function findHubHeadquarterJobStaff($job_title,$extend){
		//获取Hub Admin 的子部门
		//Hub Admin 部门等于 68
		$hub_headquarter = (new SettingEnvServer())->getSetVal('hub_headquarter_id');
		$dept_list = [];
		$dept_detail = SysDepartmentModel::findFirst(['conditions' => "id = :id: and deleted = 0",
		                                              'bind'       => [
			                                              'id' => $hub_headquarter,
		                                              ],
		                                              'columns'    => 'id,ancestry_v3']);
		if(!empty($dept_detail)) {
			$ancestry_v3 = empty($dept_detail->ancestry_v3) ? $dept_detail->id : $dept_detail->ancestry_v3;
			$dept_list = SysDepartmentModel::find([
				                                            'conditions' => ' ancestry_v3 like :ancestry: or id = :id: ',
				                                            'bind' => [
					                                            'ancestry' => $ancestry_v3.'/%',
					                                            'id' =>  $dept_detail->id,
				                                            ],
				                                            'columns' => ['id'],
			                                            ])->toArray();
			$dept_list = array_column($dept_list,'id');
		}
		$where = [ 'dept_list' => $dept_list];

		$staffs = HrStaffInfoModel::find([
			                                 'conditions' => ' node_department_id in ({dept_list:array}) and state = 1 and formal = 1 and is_sub_staff = 0',
			                                 'bind' => $where,
			                                 'columns'    => 'staff_info_id',
		                                 ])->toArray();

		$this->getDI()->get('logger')->write_log("findHubHeadquarterJobStaff ".json_encode($where).'-->'.json_encode($staffs) ,'info');
		if(empty($staffs))
			return [];
		$staffIds = array_column($staffs,'staff_info_id');

		return $staffIds ? implode(",", $staffIds) : "";
	}

	/**
	 * @description:获取属于Data Support的 xx职位的人
	 *
	 * @param $job_title  str 职位 id ,号分隔
	 *
	 * @return     :$staffIds str ,号分隔
	 * <AUTHOR> L.J
	 * @time       : 2021/8/17 14:38
	 */
	public  function findDataSupportJobStaff($job_title){
		if(empty($job_title)) { return [];}
		//获取Data Support 的子部门
		//Hub Headquarter 部门等于 138
		$data_support = (new SettingEnvServer())->getSetVal('data_support_id');
		$dept_list = [];
		$dept_detail = SysDepartmentModel::findFirst(['conditions' => "id = :id: and deleted = 0",
		                                              'bind'       => [
			                                              'id' => $data_support,
		                                              ],
		                                              'columns'    => 'id,ancestry_v3']);
		if(!empty($dept_detail)) {
			$ancestry_v3 = empty($dept_detail->ancestry_v3) ? $dept_detail->id : $dept_detail->ancestry_v3;
			$dept_list = SysDepartmentModel::find([
				                                      'conditions' => ' ancestry_v3 like :ancestry: or id = :id: ',
				                                      'bind' => [
					                                      'ancestry' => $ancestry_v3.'/%',
					                                      'id' =>  $dept_detail->id,
				                                      ],
				                                      'columns' => ['id'],
			                                      ])->toArray();
			$dept_list = array_column($dept_list,'id');
		}
		$where = [ 'dept_list' => $dept_list,
		           'ids' => explode(',',$job_title),];
		if(empty($dept_list)){
			$this->getDI()->get('logger')->write_log("findDataSupportJobStaff ".json_encode($where).' 部门为空' ,'info');
			return "";
		}
		$staffs = HrStaffInfoModel::find([
			                                 'conditions' => 'node_department_id in({dept_list:array}) and job_title in ({ids:array}) and state = 1 and formal = 1 and is_sub_staff = 0',
			                                 'bind' => $where,
			                                 'columns'    => 'staff_info_id',
		                                 ])->toArray();

		$this->getDI()->get('logger')->write_log("findDataSupportJobStaff ".json_encode($where).'-->'.json_encode($staffs) ,'info');
		if(empty($staffs))
			return [];

		$staffIds = array_column($staffs,'staff_info_id');

		return $staffIds ? implode(",", $staffIds) : "";
	}

	/**
	 * @description:获取职位下的人 根据网点类型
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/8/17 20:33
	 */
	public function findCategoryJobStaff($job_title,$extend){
		if(empty($job_title)) { return [];}
		$where = [
		           'ids' => explode(',',$job_title),
		           'category' => $extend['category'],
			];

		$builder = $this->modelsManager->createBuilder();
		$builder->columns('hr_staff_info.staff_info_id');
		$builder->from(['hr_staff_info' => HrStaffInfoModel::class]);
		$builder->leftJoin(SysStoreModel::class, 'sys_store.id = hr_staff_info.sys_store_id', 'sys_store');
		$builder->inWhere('hr_staff_info.job_title', $where['ids']);
		$builder->andWhere("hr_staff_info.state = ".enums::$service_status['incumbency']." and hr_staff_info.formal = 1 and hr_staff_info.is_sub_staff = 0");
		$builder->andWhere("sys_store.category = :category: ", ['category' => $extend['category']]);
		$staffs = $builder->getQuery()->execute()->toArray();

		$this->getDI()->get('logger')->write_log("findCategoryJobStaff -->".json_encode($staffs) ,'info');
		if(empty($staffs))
			return [];

		$staffIds = array_column($staffs,'staff_info_id');

		return $staffIds ? implode(",", $staffIds) : "";
	}

	/**
	 * @description:获取某部门下某职位的人
	 *
	 * @param $dep_id 部门 id
	 * @param $job_title 职位  id
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/8/17 20:33
	 */
	public function findDepartmentJobStaff($dep_id = 0, $job_title = 0)
	{
		if(empty($job_title) || empty($dep_id)) { return '';}
		$where = [
			'ids' => explode(',', $job_title),
			'node_department_id'=>$dep_id,
		];

		$staffs = HrStaffInfoModel::find([
             'conditions' => ' job_title in ({ids:array}) and state = 1 and formal = 1 and is_sub_staff = 0 and node_department_id = :node_department_id: ',
             'bind'       => $where,
             'columns'    => 'staff_info_id',
         ])->toArray();

		$this->logger->write_log("findDepartmentJobStaff " . json_encode($where) . '-->' . json_encode($staffs), 'info');
		if (empty($staffs)) {
            return '';
        }

		$staffIds = array_column($staffs,'staff_info_id');

		return $staffIds ? implode(",", $staffIds) : "";
	}

    /**
     * 根据申请人部门，查找指定职位的审批人
     * @param $job_title int 指定职位
     * @param $department_id int 申请人工号
     * @return string
     */
    public function findJobStaffBySubmitterFirstDepartment($department_id, $job_title): string
    {
        if(empty($job_title) || empty($department_id)) { return '';}
        $where = [
            'job_title' => $job_title,
            'sys_department_id'=> $department_id,
        ];

        $staffs = HrStaffInfoModel::find([
            'conditions' => ' job_title = :job_title: and state = 1 and formal = 1 and is_sub_staff = 0 and sys_department_id = :sys_department_id: ',
            'bind'       => $where,
            'columns'    => 'staff_info_id',
        ])->toArray();

        $this->logger->write_log("findJobStaffBySubmitterDepartment " . json_encode($where) . '-->' . json_encode($staffs), 'info');
        if (empty($staffs)) {
            return '';
        }

        $staffIds = array_column($staffs,'staff_info_id');

        return $staffIds ? implode(",", $staffIds) : "";
    }

    /**
     * 获取部门负责人
     * 申请人组织负责人 1 ~ 4级部门负责人 or BU or Clevel
     * @param $department_id
     * @param $level
     * @param array $extend
     * @return array
     */
    public function findDepartmentManagerV3($department_id, $level, array $extend = [],$auditor_id=0): array
    {
        //[1]如果存在指定部门，则获取指定部门的负责人
        $extendDepartmentId = $extend['department_id'] ?? '';
        if (!empty($extendDepartmentId)) {
            $department_id = $extendDepartmentId;
        }
        //转岗用
        if(!empty($auditor_id) && isset($extend['Array_department_id'][$auditor_id-1])){
            $extend['department_id'] =  $department_id = $extend['Array_department_id'][$auditor_id-1];
        }

        $auditorIds = "";
        $nodeDeptId = "";
        switch ($level) {
            case 0: //所属部门负责人
                [$auditorIds, $nodeDeptId] = $this->findSpecifiyDepartmentManager($department_id);
                break;
            case 1: //1 ~ 4级部门负责人
            case 2:
            case 3:
            case 4:
                [$auditorIds, $nodeDeptId] = $this->findDepartmentManager($department_id, $level, $extend);
                break;
            case 101: //获取申请人所在部门的BU负责人
                [$auditorIds, $nodeDeptId] = $this->findBuManager($department_id);
                break;
            case 150: //获取申请人所在部门的Clevel负责人
                [$auditorIds, $nodeDeptId] = $this->findClevelManagerBySubmitterDept($department_id);
                break;
            default:
                break;
        }

        $this->logger->write_log("findDepartmentManagerV3 department_id:{$department_id},level:{$level},extend:".json_encode($extend,JSON_UNESCAPED_UNICODE).',auditor_id:'.$auditor_id. ' return =>'.json_encode([$auditorIds, $nodeDeptId]) , 'info');

        
        return [$auditorIds, $nodeDeptId];
    }

    /**
     * 根据申请人部门找BU部门负责人
     */
    public function findBuManager($department_id): array
    {
        $info = SysDepartmentModel::findFirst('id = ' . $department_id);
        if (empty($info)) {
            return [];
        }
        $info = $info->toArray();

        //查找部门链上的BU
        $deptChain = explode('/', $info['ancestry_v3']);
        $department = SysDepartmentModel::findFirst([
            'conditions' => 'id in ({ids:array}) and type = 1 and deleted = 0',
            'bind'       => [
                'ids'=> $deptChain,
            ],
            'columns' => "id,manager_id",
        ]);
        if ($department) {
            $department = $department->toArray();
            $auditorIds = $department['manager_id'];
        } else {
            $auditorIds = "";
        }
        return [$auditorIds, $department['id'] ?? ''];
    }

    /**
     * 根据申请人部门找Clevel部门负责人
     * @param $department_id
     * @return array
     */
    public function findClevelManagerBySubmitterDept($department_id): array
    {
        $info = SysDepartmentModel::findFirst('id = ' . $department_id);
        if (empty($info)) {
            return [];
        }
        $info = $info->toArray();

        //查找部门链上的BU
        $deptChain = explode('/', $info['ancestry_v3']);
        $department = SysDepartmentModel::findFirst([
            'conditions' => 'id in ({ids:array}) and type = 4 and deleted = 0',
            'bind'       => [
                'ids'=> $deptChain,
            ],
            'columns' => "id,manager_id",
        ]);
        if ($department) { //默认是一定可以找到Clevel的
            $department = $department->toArray();
            $auditorIds = $department['manager_id'];
            $deptId = $department['id'];
        } else { //如果找不到就是CEO Group直属
            $auditorIds = $this->findCLevelManager(enums::WF_NODE_CEO);
            $deptId = 999;
        }
        return [$auditorIds, $deptId];
    }

    /**
    * 根据审核人id返回对应的等级部门数据
    * @Date: 2021-12-01 19:36
    * @author: peak pan
    * @return:
    **/
    public function getDepartment($departmentId,$level)
    {
        $department = SysDepartmentModel::findFirst($departmentId);
        if(empty($department->ancestry_v3)){ return []; }
        $parents = SysDepartmentModel::query()
            ->inWhere('id',explode('/',$department->ancestry_v3))
            ->execute();
        if(empty($parents->toArray())){ return []; }
        foreach ($parents->toArray() as $parent) {
            if ($parent['level'] == $level){
                return $parent;
            }
        }
    }

    /**
     * 处理跳过审批逻辑
     * @param $request
     * @param $currentNode
     * @param $nextNode
     * @param $action
     * @param $user
     * @return array $autoProcessType int  自动通类型
     */
    private function processAutoAuditV2($request , $currentNode, $nextNode, $action, $user): array
    {
        $autoProcessType = false; //自动审批状态
        $isApprovalOnJob = false; //审批人全部为空或离职状态
        $superiorIds     = [];
        $msg             = "";
        $handOverMsg     = "";

        //[1]校验下一个节点的审批人是否全部离职
        $approvals = $this->getNodeStaffIds($nextNode);
        if (!empty($approvals)) {
            $isApprovalOnJob = $this->staffRepository->isMultiStaffsOnJob($approvals);
        }

        //[2]强制自动审批有最高优先级，不需要再判断自动处理、审批人重复直接强制自动审批通过
        if ($action == enums::WF_ACTION_CREATE || $action == enums::WF_ACTION_SYS_APPROVAL_PASS) {
            // 强制自动通过
            $isForceApproval = $this->checkAutoPass($request);
            if($isForceApproval) {
                $autoProcessType = Enums::WF_AUTO_PROCESS_TYPE_7;
                // 强制自动通过 标记为系统id
                $approvals = [Enums::SYSTEM_STAFF_ID];
                $msg = 'system_approval_pass'; //系统自动通过
                return [$autoProcessType, $approvals, $superiorIds, $msg, $handOverMsg];
            }
        }
        //[2]强制自动审批有最高优先级，不需要再判断自动处理、审批人重复直接强制自动审批通过
        if ($action == enums::WF_ACTION_FORCE_APPROVAL_PASS) {
            // 强制自动通过
            $autoProcessType = Enums::WF_AUTO_PROCESS_TYPE_6;
            // 强制自动通过 标记为系统id
            $approvals = [Enums::SYSTEM_STAFF_ID];
            $msg = 'system_force_approval_pass'; // 紧急订单审批超时，系统处理为自动通过
            return [$autoProcessType, $approvals, $superiorIds, $msg, $handOverMsg];
        }
        //[2]当前节点是否
//        if ($action == Enums::WF_ACTION_TIMEOUT) {
//
//            $processPolicy = WorkflowOvertimeServer::getInstance($this->lang, $this->timezone)
//                ->getCurrentApplyAuditOvertimePolicy($request, $currentNode);
//            if ($processPolicy == WorkflowConfigEnums::WF_HANDLING_AFTER_OT_AUTO_PASS) {
//
//                //自动通过
//                $autoProcessType = Enums::WF_AUTO_PROCESS_TYPE_1;
//                $msg = 'wf_auto_process_audit_overtime_auto_pass'; //审批人超过审批时间，自动通过
//                return [$autoProcessType, $approvals, $superiorIds, $msg, $handOverMsg];
//            }
//        }

        //[3]下一级节点为空、审批人不存在或全部离职
        //则根据配置[自动通过 | 自动驳回 | 转交上级 | 转交指定人]
        if (in_array($action, [
                Enums::WF_ACTION_CREATE,
                Enums::WF_ACTION_APPROVE,
                Enums::WF_ACTION_APPROVE_COUNTERSIGN,
                Enums::WF_ACTION_APPROVAL_EMPTY,
                Enums::WF_ACTION_CREATE_CANCEL,
                Enums::WF_ACTION_CREATE_WITHOUT_LOG,
                Enums::WF_ACTION_EDIT_RECREATE,
            ])
            && $nextNode->getType() != Enums::NODE_FINAL && (empty($approvals) || $isApprovalOnJob)
        ) {

            //[3.1]自动通过|驳回
            if ($nextNode->getApprovalPolicy() == Enums::WF_EMPTY_POLICY_AUTO_PASS) { //配置自动通过
                $autoProcessType = Enums::WF_AUTO_PROCESS_TYPE_1;

                //审批人为空,系统处理为通过。
                $msg = 'err_msg_wf_auto_process_approval';
            } else if ($nextNode->getApprovalPolicy() == Enums::WF_EMPTY_POLICY_AUTO_REJECT) { //配置自动驳回
                $autoProcessType = Enums::WF_AUTO_PROCESS_TYPE_5;

                //审批人为空,系统处理为驳回。
                $msg = 'err_msg_wf_auto_process_reject';
            }

            //[3.2]转交指定人
            if ($nextNode->getApprovalPolicy() == Enums::WF_EMPTY_POLICY_DESIGNATE_OTHER
            ) { //让指定的人去审批
                $superiorIds = $approvals;
                $approvals = !empty($nextNode->getSpecifyApprover()) ? explode(',', $nextNode->getSpecifyApprover()) : [Enums::SYSTEM_STAFF_ID];

                //审批人为空或离职,系统自动转交给指定成员
                $handOverMsg = 'err_msg_wf_handover_upper_spec_staff';
            }

            //[3.2]转交上级 & 节点类型是申请人上级
            if (($nextNode->getApprovalPolicy() == Enums::WF_EMPTY_POLICY_DESIGNATE_MANAGER)
                && ($nextNode->getAuditorType() == Enums::WF_NODE_MANAGER ||
                    $nextNode->getAuditorType() == Enums::WF_NODE_DESIGNATE_OTHER && count(explode(',', $nextNode->getAuditorId())) == 1
                )
                && $isApprovalOnJob) { //下一级审批人是上级，但是上级不在职，无限取上级的上级审批直到找到在职的上级

                [$approval, $superiorIds] = $this->hightManageAudit($request, $nextNode->getAuditorId());
                if ($approval) {
                    $nextNode->setAuditorId($approval);
                    $nextNode->save();
                    $approvals = [$approval];

                    //审批人为空或离职,系统自动转交给上级
                    $handOverMsg = 'err_msg_wf_handover_upper';
                } else { //上级异常情况，自动通过
                    $autoProcessType = Enums::WF_AUTO_PROCESS_TYPE_1;
                }
            }

            //[3.3]转交上级 & 节点类型是组织负责人
            if (in_array($nextNode->getAuditorType(), Enums::$nodeDeptType)
                && $nextNode->getApprovalPolicy() == Enums::WF_EMPTY_POLICY_DESIGNATE_MANAGER
                && !$this->staffRepository->isOnJob($nextNode->getAuditorId())
            ) {

                //获取上级组织部门负责人
                $managerIds[] = $nextNode->getAuditorId();
                [$approval, $deptId, $superiorIds] = $this->hightDeptManageAudit($nextNode->getAuditorLevel(), $managerIds);
                if ($approval) {
                    $nextNode->setAuditorLevel($deptId);
                    $nextNode->setAuditorId($approval);
                    $nextNode->save();
                    $approvals = [$approval];

                    //审批人为空或离职,系统自动转交给上级组织部门负责人
                    $handOverMsg = 'err_msg_wf_handover_upper_org';
                } else {
                    $autoProcessType = Enums::WF_AUTO_PROCESS_TYPE_5; //如果不存在则自动驳回
                }
            }
        }

        //如果存在可编辑字段，
        //则不会触发后面的审批人重复跳过的逻辑
        $server = new ApprovalServer($this->lang, $this->timezone);
        if ($server->isNodeExistCanEditField($nextNode)) {
            return [$autoProcessType, $approvals, $superiorIds, $msg, $handOverMsg];
        }

        //获取审批跳过配置
        $config = $this->getAuditConfigure($request->getBizType());
        $this->logger->write_log(sprintf("get workflow config: %s", json_encode($config)) , 'info');

        //[4.1]第一种跳过的情况，审批人已经审批的情况下，自动审批通过
        //原逻辑：[不含多人的情况]例如 A - B- (B C)，第二个B出现时，不自动通过
        //新逻辑：A - B- (B C)，第二个B出现时，自动通过
        //https://flashexpress.feishu.cn/wiki/VB7vwN2CfiIvNVk53cvcRFzInLh
        if ((
                in_array($action, [Enums::WF_ACTION_CREATE, Enums::WF_ACTION_APPROVE, Enums::WF_ACTION_APPROVAL_EMPTY])
                && $currentNode->getType() != Enums::NODE_SUBMITTER || $action == Enums::WF_ACTION_CREATE_WITHOUT_LOG
            )
            && isset($config[Enums::WF_OA_AUTO_CNF_PASS]) && $config[Enums::WF_OA_AUTO_CNF_PASS] == 1
            && count($approvals) >= 1
        ) {

            //如果是撤消审批，则只查询撤消审批流程
            if ($request->getIsCancel()) {
                $conditions = "approval_id IN ({approval_id:array}) and biz_type = :type: and biz_value = :audit_id: and state = :state: and is_cancel = :is_cancel: and deleted = 0";
                $bind = [
                    'approval_id' => $approvals,
                    'type'        => $request->getBizType(),
                    'audit_id'    => $request->getBizValue(),
                    'state'       => enums::APPROVAL_STATUS_APPROVAL,
                    'is_cancel'   => $request->getIsCancel(),
                ];
            }else if($request->getIsEdit()){
                $conditions = "approval_id IN ({approval_id:array}) and biz_type = :type: and biz_value = :audit_id: and state = :state: and is_edit = :is_edit: and deleted = 0";
                $bind = [
                    'approval_id' => $approvals,
                    'type'        => $request->getBizType(),
                    'audit_id'    => $request->getBizValue(),
                    'state'       => enums::APPROVAL_STATUS_APPROVAL,
                    'is_edit'     => $request->getIsEdit(),
                ];
            }else {
                $conditions = "approval_id IN ({approval_id:array}) and biz_type = :type: and biz_value = :audit_id: and state = :state: and deleted = 0";
                $bind = [
                    'approval_id' => $approvals,
                    'type'        => $request->getBizType(),
                    'audit_id'    => $request->getBizValue(),
                    'state'       => enums::APPROVAL_STATUS_APPROVAL,
                ];
            }

            //获取下一个节点对应审批人，是否是多个人或着已经审批过
            $auditInfo = AuditApprovalModel::findFirst([
                'conditions' => $conditions,
                'bind' => $bind,
            ]);
            if ($auditInfo || in_array($user, $approvals)) {
                $autoProcessType = Enums::WF_AUTO_PROCESS_TYPE_4;

                //审批人重复出现，自动同意
                $msg = 'err_msg_wf_approver_repeat';
            }
        }

        //[4.2]下一级节点审批人存在,但是当前节点与下一个节点重复
        //如果申请节点与下一级审批节点是同一个节点，不能自动审批通过
        //https://flashexpress.feishu.cn/wiki/VB7vwN2CfiIvNVk53cvcRFzInLh
        if (in_array($action, [Enums::WF_ACTION_CREATE, Enums::WF_ACTION_APPROVE, Enums::WF_ACTION_APPROVAL_EMPTY])
            && in_array($user, $approvals)
            && $currentNode->getType() != Enums::NODE_SUBMITTER
            && isset($config[Enums::WF_OA_AUTO_CNF_PASS]) && $config[Enums::WF_OA_AUTO_CNF_PASS] == 2
            && count($approvals) >= 1
        ) {
            $autoProcessType = Enums::WF_AUTO_PROCESS_TYPE_2;

            //审批人连续重复出现，自动同意
            $msg = 'err_msg_wf_approver_cont_repeat';
        }

        //[4.3]  审批人==申请人 并且流程设置了自动审批通过
        //没有触发转交、自动审批时，才判断是否等于申请人
        //若审批人等于申请人，且连续出现，则显示：审批人等于发起人，自动同意。
        if (!$autoProcessType || in_array($autoProcessType, [Enums::WF_AUTO_PROCESS_TYPE_2, Enums::WF_AUTO_PROCESS_TYPE_4])) {
            [$autoProcessType, $message] = $this->ApproveToApplicant($approvals, $request->getSubmitterId(), $action, $request->getBizType(), $autoProcessType);

            $msg = !empty($message) ? $message: ($msg ?? "");
        }

        return [$autoProcessType, $approvals, $superiorIds, $msg, $handOverMsg];
    }

    /**
     * 获取审批流相关配置
     * @param int $type
     * @param int $config_items
     * @return array
     */
    public function getAuditConfigure(int $type, int $config_items = Enums::WF_OA_AUTO_CNF_PASS): array
    {
        if (empty($type)) {
            return [];
        }
        $flowBase = WorkflowModel::findFirst([
            'conditions' => "type = 1 and relate_type = :type: and is_view = :is_view:",
            'bind' => [
                'type'    => $type,
                'is_view' => WorkflowModel::WORKFLOW_VISUALIZE,
            ],
        ]);
        if (empty($flowBase)) {
            return [];
        }

        //版本号
        $version = $this->getConfigureVersion($flowBase);

        //查询审批流是否配置了 自动审批通过
        $workflowConfig = WorkflowConfigServer::getInstance($this->lang, $this->timezone)->getAuditConfigByVersion($version, $flowBase->id);
        return array_column($workflowConfig, 'configure_value', 'configure_type');
    }

    /**
     * 获取bp head
     * @param $department_id
     * @param array $extend
     * @return string
     */
    public function findFuncManagement($department_id, $level, array $extend = []): string
    {
        //[1]如果存在指定部门，则获取指定部门的负责人
        $extendDepartmentId = $extend['department_id'] ?? '';
        if (!empty($extendDepartmentId)) {
            $department_id = $extendDepartmentId;
        }

        //获取申请人是否为Bp Head
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('a.staff_info_id');
        $builder->from(['a' => HcmStaffManageListModel::class]);
        $builder->leftJoin(HcmStaffManageRegionModel::class, 'a.id = b.pid', 'b');
        $builder->where('a.manage_type = :manage_type: and b.category = 1 and b.deleted = 0 and b.value = :dept_id:',
            ['manage_type' => $level, 'dept_id' => $department_id]);
        $staffInfo = $builder->getQuery()->execute()->toArray();

        //此处需要去重
        $staffInfoArr = array_values(array_unique(array_column($staffInfo, 'staff_info_id')));

        return $staffInfoArr ? implode(",", $staffInfoArr) : "";
    }

    /**
     * 处理转交
     * @param $request
     * @param $superiorIds
     * @return bool
     * @throws ValidationException
     */
    public function processHandOver($request, $superiorIds, $remark = '')
    {
        if (empty($superiorIds)) {
            return false;
        }

        // 找到上级审批人
        // 替换 workflow_node 表 audit_id
        $workflowNodeModel = WorkflowNodeModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind' => ['id' => $request->getCurrentFlowNodeId()],
        ]);
        if (empty($workflowNodeModel)) {
            return false;
        }
        $currentApproval = $workflowNodeModel->getAuditorId() ? explode(',', $workflowNodeModel->getAuditorId()) : '';
        if (in_array($superiorIds, $currentApproval)) { //存在交集 ，就不转交了
            return false;
        }

        // 替换 audit_approval 表 approval_id
        $auditApprovalModel = AuditApprovalModel::findFirst([
            'conditions' => ' biz_type = :biz_type: and biz_value = :biz_value: and flow_node_id = :node_id: and state = :state: and deleted = 0',
            'bind' => [
                'biz_type'  => $request->getBizType(),
                'biz_value' => $request->getBizValue(),
                'node_id'   => $request->getCurrentFlowNodeId(),
                'state'     => enums::APPROVAL_STATUS_PENDING,
            ],
        ]);
        if (empty($auditApprovalModel)) {
            return false;
        }
        $originApprovalId = $auditApprovalModel->getApprovalId();
        $auditApprovalModel->setApprovalId($superiorIds);
        $auditApprovalModel->save();
        $this->logger->write_log('superior_audit' . $superiorIds . ' audit approval 待审批上级离职转交上级', 'info');

        //替换审批人
        $workflowNodeModel->setAuditorId($superiorIds);
        $workflowNodeModel->save();
        $this->logger->write_log('superior_audit' . $superiorIds . ' workflow node 待审批上级离职转交上级', 'info');

        //记录转交日志
        $this->logger->write_log('superior_audit' . json_encode($superiorIds, JSON_UNESCAPED_UNICODE) . ' 待审批上级离职转交上级', 'info');
        $this->saveAuditLog($request, $originApprovalId, enums::WF_ACTION_APPROVAL_SUPERIOR, $remark);

        $server = WorkflowOvertimeServer::getInstance($this->lang, $this->timezone);
        $config = $server->getOvertimeConfig($request);
        if (is_null($config)) {
            return true;
        }
        $overtimeDate = $server->recalculateHandOverOvertimeDate($request, $workflowNodeModel, $config);
        if (!empty($overtimeDate)) {
            $request->setTimeOut($overtimeDate);
            $request->save();
        }

        return $request;
    }

    /**
     * @description:获取指定职位的人
     * @param $job_title array 指定职位
     * @return string
     * <AUTHOR> L.J
     * @time       : 2021/8/17 20:33
     */
	public function findJobStaff($job_title)
	{
		if(empty($job_title)) { return '';}
		$where = [
			'ids' => explode(',', $job_title),
		];
		
		$staffs = HrStaffInfoModel::find([
             'conditions' => ' job_title in ({ids:array}) and state = 1 and formal = 1 and is_sub_staff = 0',
             'bind'       => $where,
             'columns'    => 'staff_info_id',
         ])->toArray();
		
		$this->getDI()->get('logger')->write_log("findJobStaff " . json_encode($where) . '-->' . json_encode($staffs), 'info');
		if (empty($staffs))
			return '';
		
		$staffIds = array_column($staffs,'staff_info_id');
		
		return $staffIds ? implode(",", $staffIds) : "";
	}

    /**
     * 获取自动驳回原因
     * @param $node
     * @return string
     */
    private function getAutoRejectReason($node): string
    {
        if (empty($node)) {
            return '';
        }
        if (in_array($node->getAuditorType(), enums::$nodeDeptType)) {
            $deptName = (new DepartmentRepository())->getDepartmentNameById($node->getAuditorLevel());
            $errMessage = sprintf("err_msg_wf_department_manager_not_exist|%s", $deptName);
        } else if ($node->getAuditorType() == enums::WF_NODE_BP_HEAD) {
            $errMessage = 'err_msg_wf_bp_head_not_exist';
        } else {
            $errMessage = 'err_msg_wf_auto_process_reject';
        }
        return $errMessage ?? '';
    }

    /**
     * 获取历史的审批日志
     * @param $approvalId
     * @param $typeUnion
     * @return array
     */
    public function getAuditLogsHistory($approvalId, $typeUnion)
    {
        $auditLogs = (new StaffAuditToolLog())->getAuditRecords(['id' => $approvalId, 'type' => $typeUnion]);
        $result = [];
        if(empty($auditLogs)){
            return $result;
        }
        $staffRepository = new StaffRepository();
        $repo = new AuditlistRepository($this->lang, $this->timezone);
        foreach ($auditLogs as $k => $auditLog) {
            $staff = $staffRepository -> getStaffpositionV2($auditLog['operator']);
            if ($k == 0) {
                $result[] = [
                    'staff_id'    => $auditLog['operator'],
                    'name'        => $staff['name'] ?? '',
                    'position'    => $staff['job_name'] ?? '',
                    'department'  => $staff['department_name'] ?? '',
                    'store_id'    => $staff['organization_id'] ?? '',
                    "status" => $this->getTranslation()->_('send_request'),
                    'status_code' => 8,
                    'time'        => $auditLog['created_at'],
                    'remark'=>'',
                    "is_ok"             => 0,
                    "audit_info"        => '',
                    'is_show_icon'      => 1,
                    'is_OK'       => 0,
                ];
            } else {
                $result[] = [
                    'staff_id'    => $auditLog['operator'],
                    'name'        => $staff['name'] ?? '',
                    'position'    => $staff['job_name'] ?? '',
                    'department'  => $staff['department_name'] ?? '',
                    'store_id'    => $staff['organization_id'] ?? '',
                    'status'      => $repo->getAuditStatus('10' . $auditLog['to_status_type']),
                    'status_code' => (int) $auditLog['to_status_type'],
                    'time'        => $auditLog['created_at'],
                    'remark'=>'',
                    "is_ok"             => 0,
                    "audit_info"        => '',
                    'is_show_icon'      => 0,
                    'is_OK'       => 0,
                ];

            }
        }

        //追加昵称字段
        $staffInfoArrs = array_column($result, 'staff_id');
        $staffArr = HrStaffInfoModel::find([
                                               'conditions' => 'staff_info_id in({staffs:array})',
                                               'bind'       => ['staffs' => $staffInfoArrs],
                                               'columns'    => ['staff_info_id', 'nick_name'],
                                           ])->toArray();
        $staffArr = array_column($staffArr, 'nick_name', 'staff_info_id');
        foreach ($result as $k => $v) {
            $result[$k]['name'] =  isset($staffArr[$v['staff_id']]) && $staffArr[$v['staff_id']]
                ? $v['name'] . "({$staffArr[$v['staff_id']]})"
                : $v['name'];
        }
        return $result;

    }


    /**
     *
     * 针对 审批结束 最终节点 抄送其他人的审批流 只有审批通过时候 抄送
     * @param $staff_ids 要抄送的 工号
     * @param $apply_obj 对应审批的 audit apply 对象记录
     * @param array $extend 扩展字段 可以改 push 信息  等
     * @return mixed
     */
    public function final_approval_cc(array $staff_ids,$apply_obj,$extend = array()){
        try{
            if(empty($staff_ids) || empty($apply_obj))
                return true;

            $model = new AuditCCModel();
            foreach ($staff_ids as $staff_id) {
                $obj = clone $model;
                $obj->biz_type     = $apply_obj->biz_type;
                $obj->biz_value    = $apply_obj->biz_value;
                $obj->flow_node_id = $apply_obj->getCurrentFlowNodeId();
                $obj->submitter_id = $apply_obj->getSubmitterId();
                $obj->cc_staff_id  = $staff_id;
                $obj->state        = enums::APPROVAL_STATUS_APPROVAL;
                $obj->summary      = $apply_obj->getSummary();
                $obj->save();
            }

            //抄送人 push
            $hc_re = new HcRepository($this->timezone);
            $staffAccount = $hc_re->getStaffAcceptLang($staff_ids);
            $langArr      = array_column($staffAccount, 'accept_language', 'staff_info_id');

            $this->logger->write_log("send CC message:" . json_encode($langArr), "info");
            $public_re = new PublicRepository($this->lang);
            foreach ($staff_ids as $staff_id) {
                $lang = $langArr[$staff_id] ?? 'th';
                $t          = $this->getTranslation($lang);
                $pushParam  = [
                    'staff_info_id'   => $staff_id,
                    'message_title'   => $t->_('6006'),
                    'message_content' => $t->_('cc_message'),
                ];
                $public_re->pushMessageAndJumpToCC($pushParam);
            }

            return true;
        }catch (\Exception $e){
            $this->logger->write_log("final_approval_cc 抄送失败 " . $e->getMessage() . json_encode($apply_obj,true));
            return true;
        }
    }

    /**
     * 获取关联审批类型
     */
    public function getWorkflowRelateType(): array
    {
        //查询列表
        $result = WorkflowModel::Find([
            'conditions' => 'is_view=1 and state =1 and type=1 ',
            'columns'    => ['relate_type AS code', 'name AS name_w', 'id','relate_type'],
        ])->toArray();
        foreach ($result as $k => &$v) {
            $v['name'] = $this->getTranslation($this->lang)->_("workflow_type_{$v['code']}");
        }
        return $result;
    }

    /**
     * @throws InnerException
     */
    public function getPreview(int $staffId, int $relateType)
    {
        // 根据workflowId 检索数据，确认用户传入的workflowId的正确性
        $flowBase = WorkflowModel::findFirst([
            'conditions' => "type = 1 AND relate_type = :type: AND is_view = 1",
            'bind' => ['type' => $relateType],
        ]);

        if (!is_object($flowBase) || empty($flowBase)) {
            throw new InnerException (
                $this->getTranslation($this->lang)->_('approval_no_workflow'),
                ErrCode::WORKFLOW_IS_NULL_ERROR
            );
        }

        // 根据员工id 检索数据，确认用户传入的员工id是在职 且有效的数据
        $staffModelObj = new HrStaffInfoModel();
        $staffInfo = $staffModelObj->getOneByStaffId($staffId);

        if (!is_array($staffInfo) || empty($staffInfo)) {
            // 员工不存在
            throw new InnerException (
                $this->getTranslation($this->lang)->_('outsourcing_company_staff_no_exists')
            );
        }

        // 已经离职
        if (is_array($staffInfo) && !empty($staffInfo) && $staffInfo['state'] == enums::$service_status['dimission']) {
            // 已于 {xxxx-xx-xx} 离职
            throw new InnerException (
                str_replace('{xxxx-xx-xx}', $staffInfo['leave_date'], $this->getTranslation()->_('4027'))
            );
        }

        // 已经停职 4025 => '已于 {xxxx-xx-xx} 停职',
        if (is_array($staffInfo) && !empty($staffInfo) && $staffInfo['state'] == enums::$service_status['suspension']) {
            // 已于 {xxxx-xx-xx} 停职
            throw new InnerException (
                str_replace('{xxxx-xx-xx}', $staffInfo['stop_duties_date'], $this->getTranslation()->_('4025'))
            );
        }

        //获取全部节点
        $list = [];

        //获取审批流

        $workflowPersistentServer = WorkflowPersistentServer::getInstance($this->lang, $this->timezone);
        [$conditions, $binds] = $workflowPersistentServer->generateConditions($flowBase->toArray(), []);
        $data = WorkflowNodeBaseModel::find([
            'conditions' => $conditions,
            'bind' => $binds,
        ]);
        //初始化开始节点
        $startNodeId = $workflowPersistentServer->pickupSpecNodeId($data, enums::NODE_SUBMITTER);
        // 结束节点
        $finalNodeId = $workflowPersistentServer->pickupSpecNodeId($data, enums::NODE_FINAL);
        // 当前节点id
        $currentNodeId = $startNodeId;

        //$app          = new ApprovalServer($this->lang, $this->timezone);
        //$auditParameters = $app->getWorkflowParams('', $flowBase->relate_type, $staffId, null, false);
        $app = new ApprovalServer($this->lang, $this->timezone);
        $auditParameters = $app->getSubmitterInfo($staffId);
        //$currentNode = $this->pickupNode($data, $currentNodeId);

        // 开始节点信息放入返回的数组中
        array_push($list, [
            'staff_info' => [$staffId],
            'state' => $this->getTranslation($this->lang)->_('send_request'),   // 发起申请
            'node_audit_type' => 0,
            'node_id' => (int)$currentNodeId,
        ]);
        $staffIdList = [$staffId];

        // 循环逐级获取下级节点数据
        do {
            [$relateId, $nextNodeId, $isTermination] = $workflowPersistentServer->findNextBaseNode($flowBase->id,
                $currentNodeId,
                $auditParameters,
                $staffId,
                $finalNodeId
            );

            $nextNode = $this->pickupNode($data, $nextNodeId);
            $currentNode = $nextNode;

            if ($currentNode->auditor_type == Enums::WF_NODE_AUDITOR_TYPE_COMPLEX) {
                // 解析复合条件审批人
                $auditorIds = $workflowPersistentServer->parseMultiNodeType($currentNode, $staffId, []);
            } else {
                // 解析单条审批人
                [$auditorIds, $ext] = $this->parseNode($currentNode, $staffId, []);
            }

            // 查询抄送人信息
            $workflowNodeCCBaseResult = WorkflowNodeCcBaseModel::find([
                'conditions' => "flow_id = :flow_id: and version = :version: and deleted = 0",
                'bind' => [
                    'flow_id' => (int)$flowBase->id,
                    'version' => trim($flowBase->version),
                ],
            ]);
            $workflowNodeCCBaseList = [];
            if (is_object($workflowNodeCCBaseResult) && !empty($workflowNodeCCBaseResult)) {
                $workflowNodeCCBaseList = array_column($workflowNodeCCBaseResult->toArray(), null, 'node_base_id');
            }

            $ccIds = [];
            if (
                is_array($workflowNodeCCBaseList) &&
                !empty($workflowNodeCCBaseList[$currentNode->id]) &&
                $workflowNodeCCBaseList[$currentNode->id]['auditor_type'] == Enums::WF_NODE_AUDITOR_TYPE_COMPLEX) {
                // 解析复合条件抄送人
                $ccIds = $workflowPersistentServer->parseMultiNodeTypeCc((object)$workflowNodeCCBaseList[$currentNode->id], $staffId, []);
            }

            if (
                is_array($workflowNodeCCBaseList) &&
                !empty($workflowNodeCCBaseList[$currentNode->id]) &&
                $workflowNodeCCBaseList[$currentNode->id]['auditor_type'] != Enums::WF_NODE_AUDITOR_TYPE_COMPLEX) {
                // 解析但条件抄送人
                $ccIds = $workflowPersistentServer->parseNodeCc((object)$workflowNodeCCBaseList[$currentNode->id], $staffId, []);
            }

            if (!empty($ccIds)) {
                $ccIdList = explode(',', $ccIds);
                $staffIdList = array_merge($staffIdList, $ccIdList);
                $ccInfo = [
                    'staff_info' => $ccIdList,
                    'state' => $this->getTranslation($this->lang)->_('cc_node_name'), //抄送
                    'node_audit_type' => 2,  // 独立cc节点 不展示 会签 或签
                    'node_id' => (int)$currentNode->id,
                ];
            }

            // 节点前抄送
            if ((!empty($workflowNodeCCBaseList[$currentNode->id]['type']) && $workflowNodeCCBaseList[$currentNode->id]['type'] == 1) && !empty($ccIds)) {
                array_push($list, $ccInfo);
            }

            if (!empty($auditorIds)) {
                $auditorIdList = is_array($auditorIds) ? $auditorIds : explode(',', $auditorIds);
                $staffIdList = array_merge($staffIdList, $auditorIdList);
                $appendAry = [
                    'staff_info' => $auditorIdList,
                    'state' => $this->getTranslation($this->lang)->_('audit_apply'), //审批
                    'node_audit_type' => (int)$currentNode->type, // 0-开始节点 1-或签审批节点 2-抄送节点 3-会签审批节点 99-结束节点
                    'node_id' => (int)$currentNode->id,
                ];
                array_push($list, $appendAry);
            }

            // 节点后抄送
            if ((!empty($workflowNodeCCBaseList[$currentNode->id]['type']) && $workflowNodeCCBaseList[$currentNode->id]['type'] == 2) && !empty($ccIds)) {
                array_push($list, $ccInfo);
            }

            $currentNodeId = $currentNode->id;
        } while ($nextNode->type != enums::NODE_FINAL);

        if (empty($list)) {
            throw new InnerException($this->getTranslation($this->lang)->_('approval_no_workflow'));
        }

        // 根据所有审批人id和抄送人id 查询姓名
        $staffArr = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in({staffs:array})',
            'bind' => ['staffs' => array_values(array_unique($staffIdList))],
            'columns' => ['staff_info_id', 'nick_name', 'name', 'name_en'],
        ])->toArray();
        $staffInfoList = array_column($staffArr, null, 'staff_info_id');

        // 对返回数据做预处理
        foreach ($list as $infoKey => &$info) {
            foreach ($info['staff_info'] as $staffKey => $staffId) {
                $info['staff_info'][$staffKey] = [
                    'staff_id' => (int)$staffId,
                    'staff_name' => isset($staffInfoList[$staffId]) ? trim($staffInfoList[$staffId]['name']) : "",
                ];
            }
        }
        return $list;
    }
    
    /**
     * 获取全部待审批人
     * @param $audit_id
     * @param $audit_type
     * @return array
     */
    public function getAllPendingApproval($audit_id, $audit_type): array
    {
        $staffInfo = AuditApprovalModel::find([
            'columns' => 'approval_id',
            'conditions' => "biz_value = :value: and biz_type = :type: and state = 1 and deleted = 0",
            'bind' => [
                'type'  => $audit_type,
                'value' => $audit_id,
            ],
        ])->toArray();
        return array_column($staffInfo, 'approval_id');
    }

    /**
     * @description 检查是否延时审批
     * @param $request
     * @param $action
     * @return bool
     * @throws \ReflectionException
     */
    private function checkDelayApproval($request, $action): bool
    {
        //这种延时审批的方式已经废弃
        return false;
        $result = (new ApprovalServer($this->lang, $this->timezone))->checkAuditDelay($request, $action);
        $this->logger->write_log("checkDelayApproval ==" . json_encode($request->toArray()) . ' ,result ==' . $result, 'info');
        return $result;
    }

    private function checkAutoPass($request): bool
    {
        $result = (new ApprovalServer($this->lang, $this->timezone))->checkAutoPass($request);
        $this->logger->write_log("checkAutoPass ==" . json_encode($request->toArray()) . ' ,result ==' . $result, 'info');
        return $result;
    }

    /**
     * @description 打包抄送数据
     * @param $ccStaffInfo
     * @param $ccNode
     * @param $request
     * @return array
     */
    public function packCcStaffData($ccStaffInfo, $ccNode, $request): array
    {
        if (empty($ccStaffInfo)) {
            return [];
        }
        $ccStaffInfoList = HrStaffInfoModel::find([
            'conditions' => "staff_info_id IN({staff_info_id:array}) and state in(1,3)",
            'bind' => [
                'staff_info_id' => $ccStaffInfo,
            ],
            'columns' => 'staff_info_id',
        ])->toArray();
        $ccStaffInfoList = array_column($ccStaffInfoList, 'staff_info_id');
        if (empty($ccStaffInfoList)) {
            return [];
        }
        foreach ($ccStaffInfoList as $ccStaffId) {
            $request_data[] = [
                'node_id'       => $ccNode['flow_node_id'],
                'auditor_type'  => $ccNode['auditor_type'],
                'auditor_level' => $ccNode['auditor_level'],
                'auditor_id'    => $ccStaffId,
                'biz_type'      => $request->getBizType(),
                'submitter_id'  => $request->getSubmitterId(),
                'status'        => WorkflowNodeAccidentBusinessModel::STATUS_TO_BE_PROCESSED,
                'biz_value'     => $request->getBizValue(),
                'flow_id'       => $request->getFlowId(),
                'type'          => WorkflowNodeAccidentBusinessModel::CC_TYPE_ONLY_APPROVAL,
                'business'      => WorkflowNodeAccidentBusinessModel::BUSINESS_TYPE_CC,
            ];
        }
        return $request_data ?? [];
    }

    /**
     * @description 根据flow_id获取剩余审批节点
     * @param $request
     * @param $user
     * @return array
     * @throws InnerException
     */
    public function getWorkflowNodeByFlowId($request, $user): array
    {
        if (!($request instanceof AuditApplyModel)) {
            return [];
        }
        $unArrivedNodeList = [];

        //全量固化与部分固化
        if (isCountry('PH') && in_array($request->getBizType(), AuditListEnums::getTotalPersistentWorkflowType())) {
            //全部节点
            $flowNodes = $this->getFlowNodes($request->getFlowId());

            $app       = new ApprovalServer($this->lang, $this->timezone);
            $workflowParameters = $app->getWorkflowParams($request->getBizValue(), $request->getBizType(), $user);
            $nextNodeId = $request->getCurrentFlowNodeId();
            do {
                $unArrivedNodeId = $this->findNextNode($request->getFlowId(), $nextNodeId, $workflowParameters);
                $unArrivedNode   = $this->pickupNode($flowNodes, $unArrivedNodeId);

                if (empty($unArrivedNode)) {
                    break;
                }

                if ($unArrivedNode->getType() != enums::NODE_FINAL) {
                    $unArrivedNodeList[] = $unArrivedNodeId;
                }
                $nextNodeId = $unArrivedNodeId;
            } while ($unArrivedNode->getType() != enums::NODE_FINAL);

        } else {
            //全部节点
            $flowNodes = $this->getFlowNodes($request->getFlowId());
            $nodeRelate = WorkflowNodeRelateModel::find([
                "flow_id = :flow_id:",
                "bind" => [
                    "flow_id" => $request->getFlowId(),
                ],
            ])->toArray();
            $nodeRelate = array_column($nodeRelate, 'from_node_id', 'to_node_id');
            $nextNodeId = $request->getCurrentFlowNodeId();

            do {
                $unArrivedNodeId = array_search($nextNodeId, $nodeRelate);
                $unArrivedNode   = $this->pickupNode($flowNodes, $unArrivedNodeId);

                if (empty($unArrivedNode)) {
                    break;
                }

                if ($unArrivedNode->getType() != enums::NODE_FINAL) {
                    $unArrivedNodeList[] = $unArrivedNodeId;
                }
                $nextNodeId = $unArrivedNodeId;
            } while ($unArrivedNode->getType() != enums::NODE_FINAL);
        }

        return $unArrivedNodeList;
    }


    /**
     * @description 软删除审批流
     * @param $flow_id
     * @return void
     */
    public function processWorkflowStateToFinish($flow_id)
    {
        $workflowNodeList = WorkflowNodeModel::find([
            "flow_id = :flow_id:",
            "bind" => [
                "flow_id" => $flow_id,
            ],
        ]);
        if (!empty($workflowNodeList)) {
            $workflowNodeList->update(['deleted' => enums::DATA_DELETED]);
        }

        $workflowNodeRelateList = WorkflowNodeRelateModel::find([
            "flow_id = :flow_id:",
            "bind" => [
                "flow_id" => $flow_id,
            ],
        ]);
        if (!empty($workflowNodeRelateList)) {
            $workflowNodeRelateList->update(['deleted' => enums::DATA_DELETED]);
        }
    }

    public function delAuditLog($flow_id)
    {
        if (empty($flow_id)) {
            return;
        }
        $logs = AuditLogModel::find([
            'conditions' => 'flow_id = :flow_id:',
            'bind' => [
                'flow_id' => $flow_id,
            ],
        ]);
        $logs->delete();
    }

    /**
     * @description 获取指定角色的人
     * @param $role_id
     * @return array
     */
    protected function findRoles($role_id)
    {
        $build = $this->modelsManager->createBuilder();
        $build->columns(['hsi.staff_info_id']);
        $build->from(['hsi' => HrStaffInfoModel::class]);
        $build->leftJoin(HrStaffInfoPositionModel::class, 'hsi.staff_info_id = hsip.staff_info_id', 'hsip');
        $build->where('hsi.state = 1 and hsi.formal = 1 and hsi.is_sub_staff = 0 and hsip.position_category = :role_id:',
            ['role_id' => $role_id]);
        $staffInfos = $build->getQuery()->execute()->toArray();
        return !empty($staffInfos) ? array_column($staffInfos, 'staff_info_id'): [];
    }


    /**
     * 审批日志分组
     * @param $request
     * @param $data
     * @return array
     * @throws ValidationException
     */
    protected function chunkLogs($request, $data)
    {
        //审批已经完成，追加最终节点
        $isShowEndNode = true;
        $chunked       = [];
        $index         = 0;
        $server        = new ApprovalServer($this->lang, $this->timezone);
        $instance      = $server->getInstance($request->getBizType());
        if (method_exists($instance, 'isShowEndNode')) {
            $isShowEndNode = $instance->isShowEndNode($request);
        }
        foreach ($data as $item) {
            if ($item['status_code'] == 6) {
                $index++;
            }
            $chunked[$index][] = $item;

            if (next($data) === false) { // 最后一个节点
                if ($isShowEndNode) {
                    $finalNode = [
                        'state_txt'         => $this->formatStateCodeTxt(enums::WF_ACTION_FINAL_COMPLETED),
                        'status_code'       => enums::WF_ACTION_FINAL_COMPLETED,
                        'action_time'       => null,
                        "process_state"     => $request->getState() != Enums::APPROVAL_STATUS_PENDING? AuditLogModel::PROCESS_BAR_HIGHLIGHT: AuditLogModel::PROCESS_BAR_DARKNESS,
                        "approval_type"     => enums::NODE_FINAL,
                        "approval_info"     => [],
                    ];
                    $chunked[$index][] = $finalNode;
                }
            }
        }
        return $chunked;
    }

    /**
     * 日志分组
     * @param $request
     * @param $data
     * @return array
     * @throws ValidationException
     */
    protected function chunkLogsV4($request, $data)
    {
        $chunked       = [];
        $index         = 0;
        $isShowEndNode = true;
        $server        = new ApprovalServer($this->lang, $this->timezone);
        $instance      = $server->getInstance($request->getBizType());
        if (method_exists($instance, 'isShowEndNode')) {
            $isShowEndNode = $instance->isShowEndNode($request);
        }

        //日志分组 特殊情况 泰国出差审批流 需要改成map 同时存在 正常审批 撤销审批 和修改审批
        $title = $this->getTranslation()->_('normal_log');
        foreach ($data as $item) {
            if ($item['status_code'] == enums::WF_ACTION_EDIT_RECREATE) {
                $title = $this->getTranslation()->_('edit_log');
                $index++;
            }

            if ($item['status_code'] == enums::WF_ACTION_CREATE_CANCEL) {
                $title = $this->getTranslation()->_('cancel_log');
                $index++;
            }

            $chunked[$index]['title'] = $title;
            $chunked[$index]['data'][] = $item;

            if (next($data) === false) { // 最后一个节点
                if ($isShowEndNode) {
                    $finalNode = [
                        'state_txt'         => $this->formatStateCodeTxt(enums::WF_ACTION_FINAL_COMPLETED),
                        'status_code'       => enums::WF_ACTION_FINAL_COMPLETED,
                        'action_time'       => null,
                        "process_state"     => $request->getState() != Enums::APPROVAL_STATUS_PENDING? AuditLogModel::PROCESS_BAR_HIGHLIGHT: AuditLogModel::PROCESS_BAR_DARKNESS,
                        "approval_type"     => enums::NODE_FINAL,
                        "approval_info"     => [],
                    ];
                    $chunked[$index]['data'][] = $finalNode;
                }
            }
        }
        return $chunked;
    }

    public function delApprovalData($audit_id, $audit_type)
    {
        $data = AuditApprovalModel::find([
            'conditions' => 'biz_type = :biz_type: and biz_value = :biz_value:',
            'bind' => [
                'biz_value' => $audit_id,
                'biz_type'  => $audit_type,
            ],
        ]);
        $data->delete();
    }

    /**
     * @description 获取审批流详情
     * @param $workflow_id
     * @return mixed
     */
    public function getWorkflowInfo($workflow_id)
    {
        //获取下一个阶段不为空的那个ID
        //TODO：暂不支持2个以上审批阶段的审批
        $workflowInfo = WorkflowModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => [
                'id' => $workflow_id,
            ],
        ]);
        if (empty($workflowInfo)) {
            return false;
        }
        return $workflowInfo;
    }

    /**
     * 发送审批push
     * @param $params
     * @return void
     */
    private function sendAuditPush($params)
    {
        $request                  = $params['request'];
        $action                   = $params['action'];
        $isApprovalNeedDelayAudit = $params['is_approval_need_delay_audit'];
        $user                     = $params['user'];
        $nextNode                 = $params['next_node'];
        $currentNode              = $params['current_node'];
        $approvals                = $params['approvals'];
        $request_data             = [];

        // 获取节点审批后的操作：push审批人 增加审批创建操作 &&
        // 不需要延时审批(延时审批需要到审批时间时才能看到审批数据并且发送Push)
        if (in_array($action, [
                Enums::WF_ACTION_CREATE,
                Enums::WF_ACTION_APPROVE,
                Enums::WF_ACTION_APPROVAL_EMPTY,
            ]) && is_array($approvals) && false === $isApprovalNeedDelayAudit) {
            //不发送 push 的人 enums::SYSTEM_STAFF_ID , 只有一个审批人并且审批人==上次审批人

            foreach ($approvals as $approval) {
                if (empty($approval) || $approval == enums::SYSTEM_STAFF_ID) {
                    continue;
                }
                if ($approval != $user) {
                    $businessType = WorkflowNodeAccidentBusinessModel::BUSINESS_TYPE_PUSH;
                } else {
                    $businessType = WorkflowNodeAccidentBusinessModel::BUSINESS_TYPE_HIDE_PUSH;
                }
                $request_data[] = [
                    'node_id'       => $nextNode->getId(),
                    'auditor_type'  => $nextNode->getAuditorType(),
                    'auditor_level' => $nextNode->getAuditorLevel(),
                    'auditor_id'    => $approval,
                    'biz_type'      => $request->getBizType(),
                    'submitter_id'  => $request->getSubmitterId(),
                    'status'        => WorkflowNodeAccidentBusinessModel::STATUS_TO_BE_PROCESSED,
                    'biz_value'     => $request->getBizValue(),
                    'flow_id'       => $request->getFlowId(),
                    'type'          => WorkflowNodeAccidentBusinessModel::CC_BEFORE_NODE,
                    'business'      => $businessType,
                ];
            }

            //TODO 临时方案 发送隐式push
            if (in_array($request->getBizType(), array_merge(AuditListEnums::$oa_biz_type_item_by_common_audit,
                    [AuditListEnums::APPROVAL_TYPE_HC_BUDGET]))) {

                $currentApprovals = $this->getNodeStaffIds($currentNode);
                foreach ($currentApprovals as $approval) {
                    if (empty($approval)) {
                        continue;
                    }
                    $request_data[] = [
                        'node_id'       => $nextNode->getId(),
                        'auditor_type'  => $nextNode->getAuditorType(),
                        'auditor_level' => $nextNode->getAuditorLevel(),
                        'auditor_id'    => $approval,
                        'biz_type'      => $request->getBizType(),
                        'submitter_id'  => $request->getSubmitterId(),
                        'status'        => WorkflowNodeAccidentBusinessModel::STATUS_TO_BE_PROCESSED,
                        'biz_value'     => $request->getBizValue(),
                        'flow_id'       => $request->getFlowId(),
                        'type'          => WorkflowNodeAccidentBusinessModel::CC_BEFORE_NODE,
                        'business'      => WorkflowNodeAccidentBusinessModel::BUSINESS_TYPE_HIDE_PUSH,
                    ];
                }
            }
        }

        //TODO 临时方案 发送隐式push
        //https://flashexpress.feishu.cn/wiki/M63lwkNeOixBxSkjMVjcNua3nUh
        if (in_array($request->getBizType(), array_merge(AuditListEnums::$oa_biz_type_item_by_common_audit,
                [AuditListEnums::APPROVAL_TYPE_HC_BUDGET])) && in_array($action, [
                Enums::WF_ACTION_REJECT,
                Enums::WF_ACTION_CANCEL,
            ])) {

            foreach ($approvals as $approval) {
                if ($approval == enums::SYSTEM_STAFF_ID) {
                    continue;
                }
                $request_data[] = [
                    'node_id'       => $nextNode->getId(),
                    'auditor_type'  => $nextNode->getAuditorType(),
                    'auditor_level' => $nextNode->getAuditorLevel(),
                    'auditor_id'    => $approval,
                    'biz_type'      => $request->getBizType(),
                    'submitter_id'  => $request->getSubmitterId(),
                    'status'        => WorkflowNodeAccidentBusinessModel::STATUS_TO_BE_PROCESSED,
                    'biz_value'     => $request->getBizValue(),
                    'flow_id'       => $request->getFlowId(),
                    'type'          => WorkflowNodeAccidentBusinessModel::CC_BEFORE_NODE,
                    'business'      => WorkflowNodeAccidentBusinessModel::BUSINESS_TYPE_HIDE_PUSH,
                ];
            }
        }
        if (!empty($request_data)) {
            $model = new StaffRepository();
            $model->batch_insert('workflow_node_accident_business', $request_data);
        }
    }

    /**
     * 处理超时
     * @throws ValidationException
     * @throws \Exception
     */
    private function processTimeOut($request, array $auditCallbackParams, $current_node, $nextNode)
    {
        //获取全部的待审批人, 记录哪些人超时了
        $action   = Enums::WF_ACTION_TIMEOUT;
        $auditLog = '';
        $user     = $this->getAllPendingApproval($request->getBizValue(), $request->getBizType());

        $overtimeProcessPolicy = WorkflowOvertimeServer::getInstance($this->lang,$this->timezone)->getCurrentApplyAuditOvertimePolicy($request, $current_node);
        if (is_null($overtimeProcessPolicy)) { //兼容处理
            $overtimeProcessPolicy = WorkflowConfigEnums::WF_HANDLING_AFTER_OT_AUTO_CLOSED;
        }
        switch ($overtimeProcessPolicy) {
            case WorkflowConfigEnums::WF_HANDLING_AFTER_OT_AUTO_CLOSED:
                $request->setState(Enums::APPROVAL_STATUS_TIMEOUT);
                $request->setFinalApprovalTime($auditCallbackParams['final_approval_time_loc']);
                $request->setFinalApprover($auditCallbackParams['staff_id']);

                $this->processState($request, Enums::APPROVAL_STATUS_TIMEOUT);

                (new ApprovalServer($this->lang, $this->timezone))->setAuditProperty($request->getBizValue(),
                    $request->getBizType(),
                    Enums::APPROVAL_STATUS_TIMEOUT,
                    $auditCallbackParams);

                $auditLog = 'err_msg_wf_process_timeout';
                break;
            case WorkflowConfigEnums::WF_HANDLING_AFTER_OT_AUTO_REJECT:
                $request->setState(Enums::APPROVAL_STATUS_REJECTED);
                $request->setFinalApprovalTime($auditCallbackParams['final_approval_time_loc']);
                $request->setFinalApprover($auditCallbackParams['staff_id']);

                $this->processState($request, Enums::APPROVAL_STATUS_REJECTED);

                (new ApprovalServer($this->lang, $this->timezone))->setAuditProperty($request->getBizValue(),
                    $request->getBizType(),
                    Enums::APPROVAL_STATUS_REJECTED,
                    $auditCallbackParams);

                $action = Enums::WF_ACTION_OVERTIME_AUTO_REJECT;
                $auditLog = 'err_msg_wf_process_reject';
                break;
            case WorkflowConfigEnums::WF_HANDLING_AFTER_OT_AUTO_HANDLE:
                [$action, $auditLog, $handoverStaffIds] = WorkflowOvertimeServer::getInstance($this->lang, $this->timezone)
                    ->processOvertimeHandOver($request, $current_node);

                if (!empty($handoverStaffIds)) { //创建转交人数据
                    $this->processApproval($request, $handoverStaffIds);
                }
                break;
            default:
                break;
        }

        return [$action, $auditLog, $user];
    }

    /**
     * 默认日志分组处理
     * @param $request
     * @param array $data
     * @return array
     * @throws ValidationException
     */
    protected function defaultChunkLogs($request, array $data): array
    {
        $isShowEndNode = true;
        $server        = new ApprovalServer($this->lang, $this->timezone);
        $instance      = $server->getInstance($request->getBizType());
        if (method_exists($instance, 'isShowEndNode')) {
            $isShowEndNode = $instance->isShowEndNode($request);
        }
        if ($isShowEndNode) {
            $finalNode = [
                'state_txt'     => $this->formatStateCodeTxt(enums::WF_ACTION_FINAL_COMPLETED),
                'status_code'   => enums::WF_ACTION_FINAL_COMPLETED,
                'action_time'   => null,
                "process_state" => $request->getState() != Enums::APPROVAL_STATUS_PENDING ? AuditLogModel::PROCESS_BAR_HIGHLIGHT : AuditLogModel::PROCESS_BAR_DARKNESS,
                "approval_type" => enums::NODE_FINAL,
                "approval_info" => [],
            ];
            $data[]    = $finalNode;
        }
        return $data;
    }
}
