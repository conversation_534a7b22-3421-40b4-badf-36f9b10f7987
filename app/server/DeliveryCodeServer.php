<?php

namespace FlashExpress\bi\App\Server;

//派件码server
class DeliveryCodeServer extends BaseServer
{
    /**
     * 获取派送码
     * @param $store_ids
     * @return array
     * @throws \Exception
     */
    public function getDeliveryCodeByStoreIds($store_ids): array
    {
        $param = [
            'store_ids' => $store_ids
        ];
        $url   = $this->config->api->java_http_url . "/svc/store/delivery/group/list";
        $res   = $this->httpPost($url, $param);
        if (empty($res['code']) || $res['code'] != 1) {
            $this->logger->write_log(sprintf("getDeliveryCodeByStoreIds params:%s,response:%s", json_encode($param),
                json_encode($res)));
            throw new \Exception('svc getDeliveryCodeByStoreIds err');
        }
        if (empty($res['data'])) {
            return [];
        }

        $result = [];
        foreach ($res['data'] as $item) {
            $tmp['bindLimit']     = $item['bindLimit'];
            $tmp['storeId']       = $item['storeId'];
            $tmp['deliveryCodes'] = [];
            foreach ($item['deliveryCodes'] as $v) {
                if (isset($v['deliveryCode']) && $v['deliveryCode']) {
                    $tmp['deliveryCodes'][] = [
                        'label' => $v['deliveryCode'],
                        'value' => $v['deliveryCode'],
                    ];
                }
            }
            $result[] = $tmp;
        }
        return $result;
    }

    /**
     * @param $params
     * @return bool|mixed|string
     * @throws \Exception
     */
    public function bindDeliveryCode($params)
    {
        $url   = $this->config->api->java_http_url . "/svc/store/delivery/group/bind";
        $res   = $this->httpPost($url, $params);
        if (empty($res['code']) || $res['code'] != 1) {
            $this->logger->write_log(sprintf("bindDeliveryCode params:%s,response:%s", json_encode($params),
                json_encode($res)));
            throw new \Exception('svc bindDeliveryCode err');
        }
        return $res;
    }
}