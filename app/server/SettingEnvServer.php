<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\BusinessSettingModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;


class SettingEnvServer extends BaseServer
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws BusinessException
     */
    public function saveSetVal($code,$set_val)
    {
        $setting = SettingEnvModel::findFirst([
            'conditions' => "code = :code:",
            'bind' => ['code' => $code],
        ]);
        if(empty($setting)){
            $setting = new SettingEnvModel();
            $setting->code= $code;
        }
        $setting->set_val = $set_val;
        return $setting->save();
    }


    /**
     * 获取配置项
     * @param  $code
     * @param null $separator
     * @return array|false|string|string[]
     */
    public function getSetVal($code, $separator = null)
    {
        if (empty($code)) {
            return '';
        }
        $setting = SettingEnvModel::findFirst([
            'conditions' => "code = :code:",
            'bind'       => ['code' => $code],
            'columns'    => ['set_val'],
        ]);

        if ($separator !== null) {
            return !empty($setting) && !empty($setting->set_val) ? explode($separator, $setting->set_val) : [];
        }

        return !empty($setting) ? $setting->set_val : '';
    }

    /**
     * 获取配置项返回数组
     * @param  $code
     * @param string $separator
     * @return array|false|string[]
     */
    public static function getSetValToArray($code, string $separator= ',')
    {
        if (empty($code)) {
            return [];
        }

        $setting = SettingEnvModel::findFirst([
            'conditions' => "code = :code:",
            'bind' => ['code' => $code],
            'columns' => ['set_val']
        ]);

        return !empty($setting) ? explode($separator,$setting->set_val) : [];
    }

    /**
     * like  获取 多个配置项
     * @param $code
     * @return array|string
     */
    public function getLikeVal($code){
        if (empty($code)) {
            return [];
        }
        $setting = SettingEnvModel::find([
            'conditions' => "code like :code:",
            'bind'       => ['code' => $code . '%'],
            'columns'    => ['code', 'set_val'],
        ])->toArray();
        if(empty($setting)){
            return [];
        }

        $setting = array_column($setting, 'set_val', 'code');
        return $setting;
    }

    /**
     * 获取多个配置项
     * @param array $codes
     * @return array
     */
    public function listByCode(array $codes = []): array
    {
        if (empty($codes) || !is_array($codes)) {
            return [];
        }
        return SettingEnvModel::find([
            'conditions' => "code in ({codes:array})",
            'bind'       => ['codes' => $codes],
            'columns'    => ['set_val', 'code'],
        ])->toArray();
    }


    /**
     * 获取配置项
     * @param string $code
     * @return string $return
     */
    public function getBusinessValue($code = '')
    {
        if (empty($code)) {
            return '';
        }

        $setting = BusinessSettingModel::findFirst([
            'conditions' => "code = :code: and is_delete = 0",
            'bind' => ['code' => $code],
            'columns' => ['set_val']
        ]);

        return !empty($setting) ? $setting->set_val : '';
    }


    /**
     * 获取多个配置项
     * @param array $codes
     * @return array
     */
    public function listByBusinessCode(array $codes = []): array
    {
        if (empty($codes) || !is_array($codes)) {
            return [];
        }

        return BusinessSettingModel::find([
            'conditions' => "code in ({codes:array}) and is_delete = 0",
            'bind'       => ['codes' => $codes],
            'columns'    => ['set_val', 'code'],
        ])->toArray();
    }

    /**
     * 获取setting_env json
     * @param string $code
     * @return array|mixed
     */
    public function getSettingEnvValueMap(string $code)
    {
        $res = $this->getSetVal($code);
        return !empty($res) ? json_decode($res, true) : [];
    }

    public function getMultiEnvByCode(array $codes ): array
    {
        if (empty($codes)) {
            return [];
        }

        $result = SettingEnvModel::find([
            'conditions' => "code in ({codes:array})",
            'bind'       => ['codes' => $codes],
            'columns'    => ['set_val', 'code'],
        ])->toArray();
        return array_column($result, 'set_val', 'code');
    }
}
