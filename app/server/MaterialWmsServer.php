<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;

/**
 * 耗材相关操作服务层
 * Class MaterialWmsServer
 * @package FlashExpress\bi\App\Server
 */
class MaterialWmsServer extends AuditBaseServer
{
    public $timezone;
    const OA_VALIDATE_CODE = 2;

    /**
     * MaterialWmsServer constructor.
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * rpc请求OA端
     * @param array $params 请求参数组
     * @param string $method 请求方法
     * @return array
     */
    private function rpcOa($params, $method)
    {
        $api_client = new ApiClient("oa_rpc", '', $method, $this->lang);
        $api_client->setParams($params);
        $res = $api_client->execute();
        if (!isset($res['result'])) {
            return [
                'code' => self::OA_VALIDATE_CODE,
                'msg'  => $this->getTranslation()->_('please try again'),
                'data' => [],
            ];
        }
        $res['result']['msg'] = $res['result']['message'] ?? '';
        unset($res['result']['message']);
        return $res['result'];
    }

    /**
     * 耗材申请-选择耗材
     * @param array $params 请求参数组
     * @param array $user 用户数据
     * @return array
     */
    public function getWmsList($params)
    {
        return $this->rpcOa($params, 'get_wms_list');
    }

    /**
     * 耗材基本信息回显
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAddDefault($user)
    {
        return $this->rpcOa(['id' => $user['id'], 'name' => $user['name']], 'get_wms_add_default');
    }


    /**
     * 耗材申请-提交
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function applyAdd($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'wms_apply_add');
    }

    /**
     * 耗材申请-撤回
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function cancel($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'wms_apply_cancel');
    }

    /**
     * 耗材申请-审核通过
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function pass($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'wms_apply_pass');
    }

    /**
     * 耗材申请-审核拒绝
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function reject($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'wms_apply_reject');
    }

    /**
     * 耗材申请-详情
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function detail($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'wms_apply_detail');
    }

    /**
     * 耗材申请-查看出库信息
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws ValidationException
     */
    public function getWmsOutStorageList($params)
    {
        $auditId = $params['audit_id'] ?? '';

        //申请信息
        $info = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($info)) {
            throw new ValidationException('invalid data');
        }
        $params['workflow_no'] = $info->serial_no ?? '';
        return $this->rpcOa($params, 'get_wms_out_storage_list');
    }
    /**
     * 耗材申请-查看路由
     * @param array $params 请求参数组
     * @return array
     */
    public function getOutboundTrackingInfo($params)
    {
        return $this->rpcOa($params, 'get_outbound_tracking_info');
    }

    /**
     * @description 获取详情接口
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     * @throws BusinessException
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //获取详情数据
        $result = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($result)) {
            throw new BusinessException('invalid data');
        }
        $result = $result->toArray();
        $detailInfo = $this->detail(['workflow_no' => $result['serial_no'], 'type' => $comeFrom], ['id' => $user]);
        $detailInfo = $detailInfo['data'];

        //申请人信息
        $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
        if ($staff_info['data']) {
            $staff_info = $staff_info['data'];
        }

        //组织详情数据
        $detailLists = [
            'apply_parson'                      => sprintf('%s ( %s )', $staff_info['name'] ?? '',
                $staff_info['id'] ?? ''),
            'apply_department'                  => sprintf('%s - %s', $staff_info['depart_name'] ?? '',
                $staff_info['job_name'] ?? ''),
            'belong_company'                    => $detailInfo['company_name'],
            'department'                        => $detailInfo['node_department_name'],
            'hr_probation_field_sys_store_name' => $detailInfo['store_name'],
            'wms_use_land_name'                  => $detailInfo['use_land_name'],
            'consignee'                      => $detailInfo['consignee_name'] . '('. $detailInfo['consignee_id'] . ')',
            'delivery_way_name'              => $detailInfo['delivery_way_name'] ?? '',
            'reason_application'                => $detailInfo['reason'],
            'wms_serial_no'                   => $detailInfo['apply_no'] ?? '',
        ];

        if ($result['status'] == enums::$audit_status['dismissed']) { //已经驳回，需要显示驳回原因
            $detailLists = array_merge($detailLists, ['reject_reason' => $detailInfo['reject_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        $auditlist = new AuditlistRepository($this->lang, $this->timezone);
        $add_hour = $this->config->application->add_hour;
        $data      = [
            'title'       => $auditlist->getAudityType(enums::$audit_type['WMS']),
            'id'          => $result['id'],
            'staff_id'    => $result['submitter_id'],
            'type'        => enums::$audit_type['WMS'],
            'created_at'  => date('Y-m-d H:i:s', (strtotime($result['created_at']) + $add_hour * 3600)),
            'updated_at'  => date('Y-m-d H:i:s', (strtotime($result['updated_at']) + $add_hour * 3600)),
            'status'      => $result['status'],
            'status_text' => $auditlist->getAuditStatus('10'.$result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        $returnData['data']['head']   = $data;
        $returnData['data']['products'] = $detailInfo['products'] ?? []; //产品明细
        $returnData['data']['attachments'] = $detailInfo['attachments'] ?? []; //产品明细

        return $returnData;
    }

    /**
     * @description 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info = SystemExternalApprovalModel::findFirst($auditId);
        if (!empty($info)) {
            $summary = json_decode($info->summary, true);
        } else {
            $summary = json_decode([], true);
        }
        return $summary;
    }

    /**
     * @description 回调接口
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return mixed|void
     * @throws \Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('setProperty  没有找到数据  '.$auditId);
            }
            $SystemExternalApprovalModel->state      = $state;
            $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());
            $SystemExternalApprovalModel->save();

            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                //审批同意
                $server = new MessageServer($this->lang, $this->timeZone);
                $title = isCountry('TH') ? 'การแจ้งเตือนการอนุมัติแอปพลิเคชันวัสดุสิ้นเปลือง耗材申请审批通过提醒' : 'Reminder for approval of consumables application耗材申请审批通过提醒';
                $server->sendMessage($SystemExternalApprovalModel->submitter_id, $title, $auditId, ['category' => EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_WMS_APPLY_AUDIT_PASS')]);
            } elseif ($state == enums::APPROVAL_STATUS_REJECTED) {
                //审批驳回
                $server = new MessageServer($this->lang, $this->timeZone);
                $title = isCountry('TH') ? 'การแจ้งเตือนการปฏิเสธการอนุมัติแอปพลิเคชันวัสดุสิ้นเปลือง耗材申请审批驳回提醒' : 'Reminder of rejection of consumables application耗材申请审批驳回提醒';
                $server->sendMessage($SystemExternalApprovalModel->submitter_id, $title, $auditId, ['category' => EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_WMS_APPLY_AUDIT_REJECT')]);
            }

            //创建异步回调
            $paramIn = [
                'workflow_no'   => $SystemExternalApprovalModel->serial_no,
                'biz_type'      => $SystemExternalApprovalModel->biz_type,
                'state'         => $state,
                'approval_id'   => !empty($extend['approval']) && is_array($extend['approval'])? current($extend['approval']): enums::SYSTEM_STAFF_ID,
                'approval_time' => !empty($extend['final_approval_time_utc']) ? $extend['final_approval_time_utc']: '',
            ];
            AuditCallbackServer::createData($SystemExternalApprovalModel->biz_type, $paramIn);
        }

        return true;
    }

    /**
     * @description  获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $auditInfo = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return [];
        }
        $parameters = json_decode($auditInfo->approval_parameters, true);
        return $parameters ?? [];
    }


    /**
     * 耗材申请-获取收货人列表
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getWmsConsigneeList(array $params, array $user)
    {
        $params['user_id'] = $user['id'];
        $params['user_name'] = $user['name'];
        return $this->rpcOa($params, 'wms_apply_consignee_list');
    }

    /**
     * 耗材申请-获取抄送收货人站内信消息详情
     * @param array $params 请求参数组
     * @return array
     */
    public function getConsigneeCCMsg(array $params)
    {
        return $this->rpcOa($params, 'wms_consignee_cc_msg');
    }

    /**
     * 耗材申请-获取审批通过或驳回站内信消息详情
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAuditMsg(array $params, array $user)
    {
        $returnData['data']['apply_no'] = '';
        //获取详情数据
        $result = SystemExternalApprovalModel::findFirst($params['audit_id']);
        if (empty($result)) {
            return $returnData;
        }
        $result = $result->toArray();
        $detailInfo = $this->detail(['workflow_no' => $result['serial_no'], 'type' => 1], $user);
        $detailInfo = $detailInfo['data'] ?? [];
        $returnData['data']['apply_no'] = $detailInfo['apply_no'] ?? '';
        return $returnData;
    }

    /**
     * 异步回调 （需要回调接口做幂等）
     *
     * @description 在审批到达终态时，延迟调用各个业务server实现的delayCallBack方法
     * @param $params
     * @return bool
     */
    public function delayCallBack($params): bool
    {
        $fle_rpc = (new ApiClient('oa_rpc', '', 'approval_callback', $this->lang));
        $fle_rpc->setParams($params);
        $res = $fle_rpc->execute();
        $this->logger->write_log(sprintf('approval_callback result:%s', json_encode($res, JSON_UNESCAPED_UNICODE)), 'info');

        return true;
    }

    /**
     * 耗材申请-根据快递单号查看物料详情
     * @param array $params 请求参数组
     * @return array
     */
    public function getOutboundExpressProduct($params)
    {
        return $this->rpcOa($params, 'wms_get_outbound_express_product');
    }

    /**
     * 耗材调拨单-站内信详情
     * @param array $params 请求参数组
     * @return array
     */
    public function getPackageAllotMsgInfo($params)
    {
        return $this->rpcOa($params, 'package_allot_get_msg_info');
    }

    /**
     * 耗材调拨单-获取枚举
     * @param array $params 请求参数组
     * @return array
     */
    public function getPackageAllotOptionsDefault($params)
    {
        return $this->rpcOa($params, 'package_allot_get_options_default');
    }

    /**
     * 耗材调拨单-待处理-总数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getPackageAllotCount(array $user)
    {
        $params['staff_id'] = $user['id'];
        $params['sys_store_id'] = $user['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2'] ? enums::HEAD_OFFICE_ID : ($user['organization_id'] ?? '');//所属网点
        return $this->rpcOa($params, 'package_allot_wait_handle_num');
    }

    /**
     * 耗材调拨单-待处理/已处理-列表
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getPackageAllotList(array $params, array $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'package_allot_get_list');
    }

    /**
     * 耗材调拨单-查看
     * @param array $params 请求参数组
     * @return array
     */
    public function getPackageAllotInfo(array $params)
    {
        return $this->rpcOa($params, 'package_allot_detail');
    }

    /**
     * 耗材调拨单-查看-查看物流
     * @param array $params 请求参数组
     * @return array
     */
    public function getPackageAllotExpressList(array $params)
    {
        return $this->rpcOa($params, 'package_allot_detail_express_list');
    }

    /**
     * 耗材调拨单-查看-查看路由
     * @param array $params 请求参数组
     * @return array
     */
    public function getPackageAllotExpressRoute(array $params)
    {
        return $this->rpcOa($params, 'package_allot_detail_express_route');
    }

    /**
     * 耗材调拨单-无法调出
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function cancelPackageAllot(array $params, array $user)
    {
        $params['user_id']   = $user['id'];
        $params['user_name'] = $user['name'];
        return $this->rpcOa($params, 'package_allot_cancel');
    }

    /**
     * 耗材调拨单-确认调出
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function confirmOutPackageAllot(array $params, array $user)
    {
        $params['user_id']   = $user['id'];
        $params['user_name'] = $user['name'];
        return $this->rpcOa($params, 'package_allot_confirm_out');
    }

    /**
     * 耗材调拨单-确认调入
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function confirmInPackageAllot(array $params, array $user)
    {
        $params['user_id']   = $user['id'];
        $params['user_name'] = $user['name'];
        return $this->rpcOa($params, 'package_allot_confirm_in');
    }
}
