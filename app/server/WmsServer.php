<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\WmsOrderModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use  FlashExpress\bi\App\Repository\WmsRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use  FlashExpress\bi\App\Repository\OtherRepository;
use Phalcon\Db;

class WmsServer extends AuditBaseServer
{

    protected $wms;
    public $timezone;


    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);

        $this->timezone = $timezone;
        $this->wms = new WmsRepository();
        $this->public = new PublicRepository();
        $this->auditlist = new AuditListServer($lang, $timezone);
        $this->auditlistRep = new AuditlistRepository($lang, $timezone);
        $this->ov = new OvertimeRepository($timezone);
        $this->storeS = new SysStoreServer($timezone);
        $this->wf = new WorkflowServer($this->lang, $this->timezone);
        $this->other = new OtherRepository($timezone, $lang);
        $this->pub = new PublicRepository();
        $this->staffRep = new StaffRepository($this->lang);
    }

    /**
     *
     * 蓝色部分资产
     */
    /**
     * 定值  N
     */
    const FEXCODES_BLUE_N = [
        'FEX00167' => 20,
        'FEX00166' => 20,
        'FEX00165' => 20,
        'FEX00160' => 20,
        'FEX00162' => 20,
        'FEX00161' => 10,
        'FEX00057' => 50,
        'FEX00025' => 20,
        'FEX00026' => 20,
        'FEX00027' => 20,
        'FEX00028' => 20,
        'FEX00029' => 20,
        'FEX00030' => 10,
        'FEX00038' => 100,
        'FEX00104' => 1,
        'FEX00045' => 1
    ];
    /**
     * 定值 应申请数量
     */
    const FEXCODES_BLUE = [
        'FEX00167' => 400,
        'FEX00166' => 400,
        'FEX00165' => 400,
        'FEX00160' => 400,
        'FEX00162' => 400,
        'FEX00161' => 400,
        'FEX00057' => 800,
        'FEX00025' => 400,
        'FEX00026' => 400,
        'FEX00027' => 400,
        'FEX00028' => 400,
        'FEX00029' => 400,
        'FEX00030' => 400,
        'FEX00038' => 800,
        'FEX00104' => 2,
        'FEX00045' => 2
    ];

    /**
     *
     * 橘色部分资产
     */
    /**
     *
     * 定值 N
     */
    const FEXCODES_OREGIN_N = [
        'FEX00138' => 500,
        'FEX00168' => 500,
        'FEX00238' => 10,
        'FEX00088' => 20,
        'FEX00065' => 8,
        'FEX00289' => 2,
        'FEX00022' => 500,
        'FEX00031' => 6,
        'FEX00032' => 6,
        'FEX00033' => 100,
        'FEX00034' => 100,
        'FEX00040' => 500,
        'FEX00186' => 100,
        'FEX00042' => 50,
        'FEX00180' => 50,
        'FEX00182' => 8,

    ];

    /**
     *
     * 橙色部分资产 应申请数量不定
     */
    const FEXCODES_OREGIN = [
        'FEX00138',
        'FEX00168',
        'FEX00238',
        'FEX00088',
        'FEX00065',
        'FEX00289',
        'FEX00022',
        'FEX00031',
        'FEX00032',
        'FEX00033',
        'FEX00034',
        'FEX00040',
        'FEX00186',
        'FEX00042',
        'FEX00180',
        'FEX00182',
    ];

    /**
     *
     * 黄色部分
     */
    /**
     * 定值 N
     */
    const FEXCODES_YELLOW_N = [
        'FEX00048' => 1600
    ];
    /**
     *
     * 黄色部分资产 应申请数量不定
     */
    const FEXCODES_YELLOW = [
        'FEX00048'
    ];


    /**
     *
     * 绿色部分
     */

    /**
     *
     * 绿色部分 应申请数量计算
     */
    const FEXCODES_GREEN = [
        'FEX00228',
        'FEX00226',
        'FEX00188',
        'FEX00225',
    ];

    /**
     *
     * 泡沫袋子
     */
    const FEXCODE_238 = "FEX00238";

    /**
     *
     * PC 打印纸
     */
    const FEXCODE_289 = "FEX00289";

    /**
     *
     * 透明胶带
     */
    const FEXCODE_031 = "FEX00031";

    /**
     *
     *  Flash胶带
     */
    const FEXCODE_032 = "FEX00032";

    /**
     *
    塑料袋A4*
     */
    const FEXCODE_033 = "FEX00031";

    /**
     *
     * 塑料袋 A3
     */
    const FEXCODE_034 = "FEX00034";

    /**
     *
     * 纸袋
     */
    const FEXCODE_042 = "FEX00042";

    /**
     * 蓝牙面单纸 2层
     */
    const FEXCODE_180 = "FEX00180";

    /**
     * PC面单纸 2 层
     */
    const FEXCODE_182 = "FEX00182";

    /**
     * 空白扎带
     */
    const FEXCODE_226 = "FEX00226";
    /**
     * 简易包装袋
     */
    const FEXCODE_188 = "FEX00188";
    /**
     * 包装袋
     */
    const FEXCODE_288 = "FEX00228";
    /**
     *
     * 贴轧带的纸贴
     */
    const FEXCODE_225 = "FEX00225";

    /**
     * 大区类型
     */
    const BIG_AREA_B = 1;
    const BIG_AREA_CE = 2;
    const BIG_AREA_CW = 3;
    const BIG_AREA_N = 4;
    const BIG_AREA_NE = 5;
    const BIG_AREA_S = 6;


    public static $approval_arr;

    public static $paramsIn;


    /**
     * 订单审核
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkOrderS($paramIn = [], $userInfo)
    {
        //获取商品订单信息
        $wmsModel = new WmsRepository();
        $orderData = $wmsModel->getWmsOrderR($paramIn['order_id']);
        $orderDetailData = $wmsModel->getWmsOrderDetailR($paramIn['order_id']);

        //接口说明
        $auth_pwd = env('wms_pwd');
        $url = env('api_wms_order');

        $param['mchId'] = env('wms_mchId');
        $param['nonceStr'] = time();
        $param['lang'] = $this->lang;//语言
//        $param['warehouseId'] = '1';//发货仓库ID
        $param['warehouseId'] = env('wms_warehouseId','1');//发货仓库ID

        $param['orderSn'] = $orderData['order_id'];//外部订单编号
        //14771需求，先判断pc_code是否有值，有值传递pc_code无值传递organization_id
        $param['nodeSn'] = !empty($orderData['pc_code']) ? $orderData['pc_code'] : (isset($orderData['organization_id']) ? $orderData['organization_id'] : ''); //网点ID
        $param['status'] = '1';//状态 [1]待审核
        $param['type'] = '1';//出库类型 [1]普通出库
        $consigneeArr = $wmsModel->getUserByid($orderData['apply_user']);
        $param['consigneeName'] = $consigneeArr['name'];
        $param['consigneePhone'] = $consigneeArr['mobile'];
        $param['postalCode'] = $orderData['postal_code'];
        $param['consigneeAddress'] = $orderData['consignee_address'];
        $param['goodsStatus'] = 'normal';
        //$param['remark'] = isset($userInfo['organization_name']) ? $userInfo['organization_name'] : '';

        $new_arr_department = $wmsModel->getUserDepartment($consigneeArr['department_id']);

        $this->wLog('gaofeng_wms', 'gaofeng1021'.json_encode($consigneeArr) .'---lujia'. json_encode($new_arr_department));

        $param['remark'] = !empty($new_arr_department['consignee_name']) ? $new_arr_department['consignee_name'] : '';


        //附加原因
        $param['remark'].=";".$orderData['reason_application'];
        $param['remark'] = trim($param['remark'],";");

        $param['channelSource'] = "backyard";
        /**
         * 12257【BY|TH】HUB与ASSET资产与耗材审批流变更P1 逻辑修改
         * https://l8bx01gcjr.feishu.cn/docs/doccneIwW7NFOD4MxPT51vDgU7b#
         * 在向wms同步库存的时候需要获取总部或网点的最新省、市、区、邮编信息
         */
        list($province, $city, $district, $postal_code) = $this->wms->getOrderNewAddressInfo($orderData, 'wms_order');
        // 省市区名称
        $param['province'] = isset($province['name']) ? $province['name'] : '';
        $param['city'] = isset($city['name']) ? $city['name'] : '';
        $param['district'] = isset($district['name']) ? $district['name'] : '';
        //记录省、市、区code用于更新订单信息
        $param['province_code'] = isset($province['code']) ? $province['code'] : '';
        $param['city_code'] = isset($city['code']) ? $city['code'] : '';
        $param['district_code'] = isset($district['code']) ? $district['code'] : '';
        $param['postalCode'] = $postal_code;
        $param['goods'] = array();

        /**
         * 2022-05-02线上问题bug修复【scm与oa后台物料审批通过数量不一致】
         * 原因是由于oa和by审批过程中都可以修改barcode审批通过的数量，
         * 现在的逻辑是先同步到scm后果更新库里的数量，而同步到scm的数量是直接读取的库里数据，
         * 在审批通过后又将by表的审批数量做了更新，结果就导致了scm和oa两端展示不一致
         * 所以，需要修改为依据审批人提交过来的审批数量为准提交到scm，保持两端一致
         */
        $approval_arr = [];//审批人提交过来的数据
        if (self::$approval_arr) {
            $approval_arr = (array)self::$approval_arr;
            //依barcode为key，申请数量为值
            $approval_arr = array_column($approval_arr, 'approval_num','bar_code');
        }

        if (!empty($orderDetailData)) {
            foreach ($orderDetailData as $k => $v) {
                //如果审批人传递了审批数量依传递数量为准，否则依库里记录为准
                $v['approval_num'] = isset($approval_arr[$v['bar_code']]) ? $approval_arr[$v['bar_code']] : $v['approval_num'];
                if($v['approval_num'] <= 0) {
                    continue;
                }
                $param['goods'][$k]['i'] = $v['sort'];
                $param['goods'][$k]['barCode'] = $v['bar_code'];
                $param['goods'][$k]['goodsName'] = $v['goods_name'];
                $param['goods'][$k]['specification'] = $v['nuit'];
                $param['goods'][$k]['num'] = $v['approval_num'];
                $param['goods'][$k]['price'] = $v['price'];
                $goodsRemark = $wmsModel->getUserByids($v['employ_user']);
                $param['goods'][$k]['remark'] = $goodsRemark;//json_encode($goodsRemark); //商品备注是使用者工号，姓名，职位
            }
        }
        $param['goods'] = json_encode($param['goods']);
        return $param;
    }


    /**
     * 创建订单
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addOrderS($paramIn = [], $userinfo)
    {
        $time = date("YmdHis");
        $rand = rand(1000, 9999);
        $orderId = $time . $rand;

        //获取订单相关人信息
        $admin_group = UC('wmsRole')['admin_group'];
        $organization_id = (isset($paramIn['organization_id']) && !empty($paramIn['organization_id'])) ? $paramIn['organization_id'] : $userinfo['organization_id'];
        $uInfo = $this->wms->userStoreInfo($organization_id);
        $uInfoArr = array_merge(empty($uInfo) ? [] : $uInfo, $userinfo, [
            "store_id" => $organization_id
        ]);

        //格式化订单数据
        $serialNo = $this->getRandomId();
        $returnData['data']['dataList'] = [];
        $orderData['order_id'] = $orderId;//订单号
        $orderData['organization_id'] = $organization_id;//网点编号
        $orderData['reason_application'] = isset($paramIn['reason_application']) ? $paramIn['reason_application'] : "";//申请理由字数限制
        $orderData['consignee_phone'] = $uInfoArr['mobile'];//联系方式
        $orderData['consignee_address'] = $uInfoArr['detail_address']?? '';//详细地址
        $orderData['province_code'] = $uInfoArr['province_code']?? '';//省
        $orderData['city_code'] = $uInfoArr['city_code']??'';//市
        $orderData['district_code'] = $uInfoArr['district_code']??'';//区
        $orderData['postal_code'] = $uInfoArr['postal_code']??'';//邮编
        $orderData['apply_user'] = $uInfoArr['id'];//申请人
        $orderData['shipping_user'] = $uInfoArr['name'];//收货人姓名
        $orderData['order_status'] = isset($paramIn['order_status']) ? $paramIn['order_status'] : 1;//订单状态
        $orderData['serial_no'] = (!empty($serialNo) ? 'MA' . $serialNo : NULL); //序列号
//        $orderData['wf_role'] = $wf_role;
        $orderData['wf_role'] = '';

        //格式化订单商品详情数据
        $paramIn['goods_order_detail'] = isset($paramIn['goods_order_detail']) ? $paramIn['goods_order_detail'] : "";
        $orderDetailData = $paramIn['goods_order_detail'];
        $barCodeArr = array_column($orderDetailData, "bar_code");

        //获取订单相关人货物
        $goodsInfo = $this->wms->goodsInfo($barCodeArr);
        $orderDetailDataFormat = array();
        $goodsArr = array();
        //$orderDetailDataFormat = array();
        if ($orderDetailData) {
            foreach ($goodsInfo as $k => $v) {
                $goodsArr[$v['bar_code']]['price'] = $v['price'];
                $goodsArr[$v['bar_code']]['specification'] = $v['specification'];
                //根据语言存储商品名称
                if ($this->lang == 'en') {
                    $goodsArr[$v['bar_code']]['goods_name'] = $v['goods_name_en'];
                    $goodsArr[$v['bar_code']]['nuit'] = $v['nuit_en'];
                } elseif ($this->lang == 'th') {
                    $goodsArr[$v['bar_code']]['goods_name'] = $v['goods_name_th'];
                    $goodsArr[$v['bar_code']]['nuit'] = $v['nuit_th'];
                } else {
                    $goodsArr[$v['bar_code']]['goods_name'] = $v['goods_name_zh'];
                    $goodsArr[$v['bar_code']]['nuit'] = $v['nuit_zh'];
                }
            }
        }
        if ($orderDetailData) {
            foreach ($orderDetailData as $k => $v) {
                $orderDetailDataFormat[$k]['sort'] = isset($v['sort']) ? intval($v['sort']) : 0;
                $orderDetailDataFormat[$k]['bar_code'] = isset($v['bar_code']) ? $v['bar_code']: 0;
                $orderDetailDataFormat[$k]['num'] = isset($v['num']) ? intval($v['num']): 0;
                $orderDetailDataFormat[$k]['branch_repertory'] = isset($v['branch_repertory']) ? intval($v['branch_repertory']) : 0;//网点库存
                $orderDetailDataFormat[$k]['recomment_num'] = isset($v['recommend_num']) ? intval($v['recommend_num']) : 0;//推荐数量
                $orderDetailDataFormat[$k]['approval_num'] = $orderDetailDataFormat[$k]['num'];//申请数->审批数
                if ($v['num'] <= 0) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('7101'));
                }
                $orderDetailDataFormat[$k]['order_id'] = $orderId;
                $orderDetailDataFormat[$k]['employ_user'] = isset($v['employ_user']) ? implode(",", $v['employ_user']) : "";
                //判断是否都是本网点员工
                $userSumArr = $this->wms->notAllOrganizationR($orderDetailDataFormat[$k]['employ_user'], $organization_id);
                if (sizeof($userSumArr) < sizeof($v['employ_user']) || empty($v['employ_user'])) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('7100'));
                }
                $orderDetailDataFormat[$k]['price'] = isset($goodsArr[$v['bar_code']]['price']) ? $goodsArr[$v['bar_code']]['price'] : 0;
                $orderDetailDataFormat[$k]['goods_name'] = isset($goodsArr[$v['bar_code']]['goods_name']) ? $goodsArr[$v['bar_code']]['goods_name'] : '';
                $orderDetailDataFormat[$k]['nuit'] = isset($goodsArr[$v['bar_code']]['nuit']) ? $goodsArr[$v['bar_code']]['nuit'] : '';
            }
        }

        $db = WorkflowModel::beginTransaction($this);
        try {
            $wmsOrderData = $this->wms->addOrderR($orderData, $orderDetailDataFormat);
            if (!$wmsOrderData) {
                $db->rollBack();
                $this->wLog('order_err4101_s', json_encode($paramIn) . json_encode($userinfo));
                return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
            }

            $orderData['id'] = $wmsOrderData;
            $returnData['data'] = $orderId;
            $requestType = 9;
            $staffId = $uInfoArr['id'];
            $organization_type = $uInfoArr['organization_type'];
            $store_id = $uInfoArr['organization_id'];
            //[4]同步数据到列表
            $info = $this->wms->getWmsOrderMongoR($orderData['id']);
            $server = new \FlashExpress\bi\App\Server\AuditListServer($this->lang, $this->timezone);
            $summary = $server->generateSummary($info['id'], $requestType);

            $insertAuditData[] = [
                'id_union' => 'id_' . $info['id'],
                'staff_id_union' => $staffId,
                'type_union' => $requestType,
                'status_union' => 101,
                'store_id' => $organization_type == 2 ? '' : $store_id,
                'data' => json_encode($info),
                'table' => 'wms_order',
                'created_at' => gmdate('Y-m-d H:i:s', time()),
                'origin_id' => $info['id'],
                'summary' => json_encode($summary, JSON_UNESCAPED_UNICODE),
                'approval_id' => 0,
            ];
            if (!($this->other->insterUnion($insertAuditData) &&
                (new ApprovalServer($this->lang, $this->timezone))
                    ->create($wmsOrderData, enums::$audit_type['MA'], $userinfo['staff_id'])
            )) {
                throw new \Exception('addOrder_s 插入审批流失败 ' . json_encode($paramIn, JSON_UNESCAPED_UNICODE) . " " . json_encode($userinfo, JSON_UNESCAPED_UNICODE));
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }




//        foreach ($approvalIDArr as $finalApprover) {
//            $insertAuditData[] = [
//                'id_union' => 'id_' . $info['id'],
//                'staff_id_union' => $staffId,
//                'type_union' => $requestType,
//                'status_union' => 107,
//                'store_id' => $organization_type == 2 ? '' : $store_id,
//                'data' => json_encode($info),
//                'table' => 'wms_order',
//                'created_at' => gmdate('Y-m-d H:i:s', time()),
//                'origin_id' => $info['id'],
//                'summary' => json_encode($summary, JSON_UNESCAPED_UNICODE),
//                'approval_id' => $finalApprover,
//            ];
//        }

        //[5]追加审批日志
//        $staff_name = $this->pub->getStaffName($staffId);
//        $log['staff_id'] = $staffId;
//        $log['type'] = $requestType;
//        $log['original_type'] = 1;
//        $log['original_id'] = $info['id'];
//        $log['operator'] = $staffId;
//        $log['operator_name'] = $staff_name;
//        $log['to_status_type'] = 1;
//        $this->other->insertLog($log);
//
//        [6]追加审批流
//        $ApprovalArr['audit_id'] = $info['id'];
//        $ApprovalArr['type'] = $requestType;
//        $ApprovalArr['level'] = 1;
//        $ApprovalArr['status'] = 7;
//        $ApprovalArr['submitter_id'] = $staffId;
//        $ApprovalArr['staff_ids'] = $userinfo['staff_ids'] ?? 0;
//        $this->other->insertApproval($ApprovalArr);

        return $this->checkReturn($returnData);
    }

    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result = (new WmsRepository($this->timezone))->getWmsOrderMongoR($auditId);
        $barCodes  = array_column($result['dataList'], "bar_code");
        $backModel = $this->getDI()->get("db");
        $goods     = $backModel->fetchAll("
                --
                select * from wms_goods where find_in_set(bar_code, :bar_codes)
                ", Db::FETCH_ASSOC, [
            "bar_codes" => implode(",", $barCodes)
        ], [
            "bar_codes" => Db\Column::BIND_PARAM_STR
        ]);
        $goods     = array_column($goods, null, "bar_code");

        $staff_info = (new StaffServer())->get_staff($result['apply_user']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }
        $getStaffByOrganization = $this->staffRep->getStaffByOrganization($result['organization_id']);

        $stock_flag = false;
        $params['lang'] = $this->lang;
        $stock_data = (new BaseRepository())->getDataFromWms(env("api_wms_goodsStock"),$params);
        if($stock_data['code']===1){
            $stock_flag = true;
        }
        foreach ($result['dataList'] as $k => $v) {
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('apply_parson'), 'value' => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? '')];
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('apply_department'),'value' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? '')];
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('goods_name'), 'value' => $goods[$v['bar_code']]["goods_name_" . str_replace("-CN", "", $this->lang)]];
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('num'), 'value' => $v['num']];
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('employ_user'), 'value' => $v['employ_user']];
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('branch_repertory'), 'value' => $v['branch_repertory']];
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('recomment_num'), 'value' => $v['recomment_num']];
            if($stock_flag && isset($stock_data['data'][$v['bar_code']])) {
                $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('scm_current_inventory'), 'value' => intval($stock_data['data'][$v['bar_code']]['availableInventory'])];
            } else {
                $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('scm_current_inventory'), 'value' => 0];
            }
        }
        foreach ($result['dataList'] as $k => $v) {
            $returnData['data']['edit'][$k]['goods_name']    = $goods[$v['bar_code']]["goods_name_" . str_replace("-CN", "", $this->lang)];
            $returnData['data']['edit'][$k]['num']           = $v['num'];
            $returnData['data']['edit'][$k]['employ_user']   = $v['employ_user'];
            $returnData['data']['edit'][$k]['bar_code']      = $v['bar_code'];
            $returnData['data']['edit'][$k]['recomment_num'] = $v['recomment_num'];
            $returnData['data']['edit'][$k]['approval_num']  = $v['approval_num'];
        }
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('reason_application'), 'value' => $result['reason_application']];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('branch_staff_number'), 'value' => sizeof($getStaffByOrganization)];
        //BI请求接口揽件量、派件量和中转量
        $req                            = ['locale' => $this->lang, 'store_id' => $result['organization_id']];

        $fle_rpc = (new ApiClient('bi_rpc','','pickup_delivery_count_aweek_bystoreid', $this->lang));
        $fle_rpc->setParams($req);
        $biData = $fle_rpc->execute();

        $deliveryCount                  = $biData['result']['deliveryCount'] ?? 0;
        $pickupCountt                   = $biData['result']['pickupCount'] ?? 0;
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('deliveryCount'), 'value' => $deliveryCount];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('pickupCount'), 'value' => $pickupCountt];
        //近三个月领用记录
        $returnArr = [
            'mchId'     => env('wms_mchId'),
            'startTime' => date("Y-m-d", strtotime("-3 month")),
            'endTime'   => date("Y-m-d"),
            'nodeSn'    => $result['organization_id'],//TH21060201
            'barCode'   => implode(',', array_column($result['dataList'], 'bar_code'))   //implode(',', array_column( $result['dataList'] , 'bar_code') )
        ];
        $wmsData            = httpPostFun(env('api_wms_outBoundGoods'), $returnArr, null, env('wms_pwd'));
        $newArr    = [];
        if ($wmsData['code'] == 1) {
            foreach ($wmsData['data'] as $m => $n) {
                foreach ($n as $x => $y) {
                    if ($y['outNumber'])
                        $newArr[] = [$y['name'], $y['outNumber'], $y['outWarehouseTime']];
                }
            }
            $returnData['data']['extend'][0]['title'] = $this->getTranslation()->_('material_requisition_record');
            $returnData['data']['extend'][0]['th']    = [$this->getTranslation()->_('goods_name'), $this->getTranslation()->_('num'), $this->getTranslation()->_('time')];;
            $returnData['data']['extend'][0]['td'] = $newArr;
        }
        //获取网点名
        $storeInfo = (new \FlashExpress\bi\App\Server\SysStoreServer())->getStoreName([$result['organization_id']]);

        //物品名称 ，物品数量 ，使用者，申请理由，审批人，状态
        $data = [
            'title'       => $this->auditlistRep->getAudityType(enums::$audit_type['MA']),
            'origin_id'   => $result['id'],
            'id'          => $result['order_id'],
            'staff_id'    => $result['apply_user'],
            'type'        => enums::$audit_type['MA'],
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['order_status'],
            'status_text' => $this->auditlistRep->getAuditStatus('10' . $result['order_status']),
            'serial_no'   => $result['serial_no'] ?? '',
            'store_id'    => $result['organization_id'],
            'store_name'  => $storeInfo[$result['organization_id']] ?? '',
        ];
        //已经驳回，需要显示驳回原因
        if ($result['order_status'] == 3) {
            array_push($returnData['data']['detail'], ['key' => $this->getTranslation()->_('reject_reason'), 'value' => $result['reason'] ?? '']);
        }

        if ($comeFrom == 2) { //获取审批人的审批状态
            $infos = AuditApprovalModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and approval_id = :approval_id: and deleted = 0",
                'bind' => [
                    'type'  => enums::$audit_type['MA'],
                    'value' => $auditId,
                    'approval_id' => $user
                ],
                 'order' => 'id desc'
            ]);
            $infos = empty($infos) ? [] : $infos->toArray();
            $state = $infos['state'] ?? enums::APPROVAL_STATUS_REJECTED; // 如果不是待审批人,按照已驳回按钮处理
            $option = ['beforeApproval' => "1,2,7", 'afterApproval' => '0'];
        } else { //获取申请的审批状态
            $state = $result['order_status'];
            $option = ['beforeApproval' => '3', 'afterApproval' => '0'];
        }

        $data['options'] = $this->getStaffOptions(['option' => $option, 'status' => $state]);

        $returnData['data']['head'] = $data;

        $params     = [];
        $params['orderSn'] = $result['order_id'];
        $params['lang'] = $this->lang;
        $result = (new BaseRepository())->getDataFromWms(env("api_wms_url") . "/open/getOutboundTrackingInfo", $params);
        $tracking = '';
        $this->getDI()->get("logger")->write_log("getOutboundTrackingInfo " . json_encode($result, JSON_UNESCAPED_UNICODE), "info");
        if ($result && $result['code'] == 1 && isset($result['data']) && $result['data'] && isset($result['data']['status'])) {
            if ($result['data']['status'] == 'Wait for approval') {
                $tracking = $this->getTranslation()->_("shipment_be_delivered");
            } else if ($result['data']['status'] == 'Outbounded') {
                $tracking = $this->getTranslation()->_("shipment_be_received") . ' (' . $this->getTranslation()->_("shipment_number") . '：' . $result['data']['expressSn'] . ")";
            }
        }
        $returnData['data']['detail'][] = ["key" => $this->getTranslation()->_("shipment_status"), "value" => $tracking];

        return $returnData;
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $server = new \FlashExpress\bi\App\Server\AuditListServer($this->lang, $this->timezone);
        return $server->generateSummary($auditId, enums::$audit_type['MA']);
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     * @throws InnerException
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $order = WmsOrderModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind' => ['id' => $auditId]
        ]);
        if ($isFinal) {
            if ($order) {
                $order = $order->toArray();
                if ($state == enums::$audit_status['approved']) {
                    /**
                     * 12257【BY|TH】HUB与ASSET资产与耗材审批流变更P1逻辑修改
                     * https://l8bx01gcjr.feishu.cn/docs/doccneIwW7NFOD4MxPT51vDgU7b#
                     */
                    $checkArr = self::checkOrderS(self::$paramsIn, self::$paramsIn['userinfo']);
                    $province_code = $checkArr['province_code'];
                    $city_code = $checkArr['city_code'];
                    $district_code = $checkArr['district_code'];
                    unset($checkArr['province_code'], $checkArr['city_code'], $checkArr['district_code']);
                    $resPost = httpPostFun(env('api_wms_order'), $checkArr, null, env('wms_pwd'));
                    //由于scm那边会出现返回值为null的，审批人员再次审批时scm那边可能其实已经成功了，所以兼容下1003="订单已存在"的数据
                    if ($resPost['code'] != 1 && $resPost['code'] != 1003) {
                        $this->wLog('updateWmsOrder WarehouseAdd failed', json_encode($checkArr)."==>>".json_encode($resPost),'Wms');
                        //物料审批最后一步审批通过要先出库成功才可审批成功，这个异常返回可以让审批流回滚
                        throw new InnerException($resPost['msg']);
                    } else {
                        $this->wLog('updateWmsOrder', json_encode($checkArr)."==>>".json_encode($resPost),'Wms');
                        $paramData = [
                            'id' => $auditId,
                            'order_status' => $state,
                            'reason' => isset(self::$paramsIn['reject_reason']) ? self::$paramsIn['reject_reason'] : '',
                            'succeed' => 1,
                            'result' => json_encode($resPost,JSON_UNESCAPED_UNICODE),
                            'province_code' => $province_code,
                            'city_code' => $city_code,
                            'district_code' => $district_code,
                            'postal_code' => $checkArr['postalCode'],
                            //  'final_audit_num'=> $status == 2 ? ($approvalData ?? $info['demend_num']) : 0
                        ];
                        //操作记录需要的数据
                        $log['staff_id'] = $order['apply_user'];
                        $log['type'] = enums::$audit_type['MA'];
                        $log['original_type'] = enums::$audit_status['panding_approval'];
                        $log['to_status_type'] = $state;
                        $log['original_id'] = $auditId;
                        $log['operator'] = self::$paramsIn['userinfo']['id'];
                        $log['operator_name'] = self::$paramsIn['userinfo']['name'];
                        if (self::$approval_arr)
                            $this->wms->approvalOrderDtail($order['order_id'], (array) self::$approval_arr);
                        $res = $this->other->lastApproval($log, $paramData, 'wms_order');
                    }
                } else {
                    //staff_audit_tool_log日志需要的数据
                    $log['staff_id'] = $order['apply_user'];
                    $log['type'] = enums::$audit_type['MA'];
                    $log['original_type'] = enums::$audit_status['panding_approval'];
                    $log['to_status_type'] = $state;
                    $log['original_id'] = $auditId;
                    $log['operator'] = self::$paramsIn['userinfo']['id'];
                    $log['operator_name'] = self::$paramsIn['userinfo']['name'];

                    $updateArr['id'] = $auditId;
                    $updateArr['order_status'] = $state;
                    $updateArr['reason'] = isset(self::$paramsIn['reject_reason']) ? self::$paramsIn['reject_reason'] : '';
                    $res = $this->other->lastApproval($log, $updateArr, 'wms_order');
                }
            }
        } else {
            // 非最终审批
            if ($order) {
                $order = $order->toArray();
                if (self::$approval_arr)
                    $this->wms->approvalOrderDtail($order['order_id'], (array) self::$approval_arr);
            }
        }

        return true;
    }

    /**
     * 样例
     * from_node_id | to_node_id | valuate_formula | valuate_code
     * -------------+------------+-----------------+-------------
     *      4       |     5      |    $p1 == 4     | getSubmitterDepartment
     *
     * 表示当提交人的部门为4时，审批节点4的下一个节点是5
     * 需要在 getWorkflowParams 中返回申请人所在的部门字段
     *
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $wmsOrder = WmsOrderModel::findFirst([
            'conditions' => ' id = :id:',
            'bind' => ['id' => $auditId]
        ])->toArray();

        $userInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id:',
            'bind' => ['staff_id' => $wmsOrder['apply_user']]
        ]);

        if(!empty($userInfo)){
            $userInfo = $userInfo->toArray();
            $department_info = SysDepartmentModel::findFirst($userInfo['node_department_id']);
            $ancestry = empty($department_info) ? '' : $department_info->ancestry_v3;
            $store_info = $this->storeS->getStoreByid($wmsOrder['organization_id']);
            $staff_info = (new StaffRepository())->getStaffPositionv3($wmsOrder['apply_user']);
	        //判断相应职位获取本身负责的大区
	        $region_area = $this->getJobCategory( $wmsOrder['apply_user'],$userInfo['job_title']);
            $level = 0;
            if (in_array($department_info->type, [2, 3]) && $department_info->level == 1) {
                $level = 1 ;
            }
            return [
                'category'=>$staff_info['category'],
                'job_title' => $userInfo['job_title'],
                'node_department_id' => $userInfo['node_department_id'],
                'sys_store_id' => $userInfo['sys_store_id'],
                'ancestry' => $ancestry,
                'manage_region' => $store_info['manage_region'],
                'k1' => $this->wf_role2(['id' => $wmsOrder['apply_user']], $store_info),
                'region_area'=>$region_area,
                'level'         => $level,//部门等级
            ];
        }
        //没找到申请人信息 返回空
        return [
            'category' => 0,
            'job_title' => 0,
            'node_department_id' => 0,
            'sys_store_id' => 0,
            'ancestry' => '',
            'manage_region' => '',
            'k1' => '',
            'region_area'=>[],
            'level' => 0,

        ];

    }

    /**
     * 商品列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function wmsList($userinfo, $paramIn = [])
    {
        //[1]参数定义
        $returnData = ['data' => []];
        /*        //[2]查询商品列表[如果不是网点经理或主管限制商品]
                if( ! self::organizationManagerS($userinfo) ){
                    $wmsData = $this->wms->wmsGoodsList($this->lang ,0);
                }else{
                    $wmsData = $this->wms->wmsGoodsList($this->lang );
                }*/
        $wmsData = $this->wms->wmsGoodsList($this->lang);
        if (!empty($wmsData)) {
            $returnData['data']['dataList'] = $wmsData;
        }

        return $this->checkReturn($returnData);
    }


    /**
     * 网点员工列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function organizationUserS($paramIn = [])
    {
        $organization_id = isset($paramIn['organization_id']) ? $paramIn['organization_id'] : "";
        $di = $this->getDI();
        $store_sql = "select id from staff_info where organization_id = '{$organization_id}'";
        $db = $di->get('db_fle')->query($store_sql);
        $infoArr = $db->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $returnData['data']['dataList'] = array_column($infoArr, "id");
        return $this->checkReturn($returnData);
    }


    /**
     * 是网点经理或者主管
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function organizationManagerS($paramIn = [])
    {
        $uid = isset($paramIn['id']) ? $paramIn['id'] : "";
        $di = $this->getDI();
        $sql = "select id from hr_staff_info_position where staff_info_id = '{$uid}' and position_category in (3,18)";
        $db = $di->get('db_rby')->query($sql);
        $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $infoArr;
    }


    /**
     * 如果是网点主管或者网点经理
     *
     * @param $staffId
     *
     */
    public function isManagers($staffId)
    {
        $info = $this->staffRep->getStaffPositionv3($staffId);
        //1(DC)，2(SP)，457(shop)，8(HUB)
        if(empty($info) || !in_array($info['category'],[1,2,4,5,7,8,9,10])){
            return false;
        }

        switch($info['category']){
            // DC/SP
            case 1:
            case 2:
                // BDC
            case 10:
                if(in_array($info['job_title'], [enums::$job_title['branch_supervisor']])){
                    return true;
                }
                break;
            // shop
            case 4:
            case 5:
            case 7:
                //OS
            case 9:
                if(in_array($info['job_title'], [enums::$job_title['shop_supervisor']])){
                    return true;
                }
                break;
            //hub
            case 8:
                if(in_array($info['job_title'], [enums::$job_title['hub_supervisor']])){
                    return true;
                }
                break;
        }

        return false;

    }

    /**
     * 商品订单
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getOrder($paramIn = [])
    {
        $sql = "SELECT * FROM wms_order where order_id = {$paramIn['order_id']} ";
        $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        $sqlDetail = "SELECT * FROM wms_order_detail where order_id = {$paramIn['order_id']}   ORDER BY sort ASC";
        $Detail_data = $this->getDI()->get('db')->query($sqlDetail)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $order_list = $info_data;
        $order_list['dataList'] = $Detail_data;
        return $order_list;
    }

    /**
     * 商品是否需要微量单位给wms
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getNuitDetail($data)
    {
        $goodsArr = json_decode($data['goods'], true);
        if ($goodsArr) {
            foreach ($goodsArr as $k => $v) {
                $goodsRes = $this->wms->goodsInfo($v['barCode']);
                if ($goodsRes['nuit_detail']) {
                    $goodsArr[$k]['num'] = $goodsRes['nuit_detail'] * $v['num'];
                }
            }
        }
        $data['goods'] = json_encode($goodsArr);
        return $data;
    }

    /**
     * 调用push
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function pushOrderMsg($paramIn, $userinfo)
    {
        try {
            //获取上级ID
            //$newUserInfo['staff_info_id'] = $userinfo['id'];
            //$higherUId = $this->ov->getHigherStaffId($newUserInfo);
            $buyerArr = UC('wmsRole')['buyer_id'];           //物料审批的采购
            $staffLang = (new StaffServer())->getBatchStaffLanguage(array_values($buyerArr));
            foreach ($buyerArr as $v) {
                $lang = $staffLang[$v] ?? '';
                $t = $this->getTranslation($lang);
                //调用push 发消息
                $message_title = $t->_('7005');
                $audit_type = $t->_('7005');

                $pushParam = [
                    'staff_info_id' => $v,    //接收push信息人id
                    'message_title' => $message_title,    //push标题
                    'userinfo' => $userinfo,    //当前登陆用户信息
                    'lastInsert_id' => $userinfo['id'],    //操作id
                    'audit_type' => $audit_type,    //模块名称
                    'is_audit' => 1,    //xx状态
                    'lang' => $lang
                ];
                $success = $this->public->pushMessage($pushParam);
                if ($success) {
                    /* 执行修改操作 */
                    $sql = "update wms_order set is_push = 1 where order_id = {$paramIn['order_id']} ";
                    $this->getDI()->get('db')->query($sql);
                }
            }
        } catch (\Exception $e) {
            $this->wLog('pushError', $e->getMessage(), 'wms', 'push');
        }
        return true;
    }

    /**
     * 一周的申请记录
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function existsWeek($userinfo = [])
    {
        $time = weekStart();
        $sql = "SELECT * FROM wms_order where order_status = 2 and apply_user = {$userinfo['id']}   and  `created_at` > '{$time}'";
        $Detail_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $Detail_data;
    }

    /**
     *
     * 一个月的申请记录
     *
     * @param array $userinfo
     *
     */
    public function existsMonth($userinfo = [])
    {

        $server = new WmsServer($this->lang,$this->timezone);
        $server = Tools::reBuildCountryInstance($server, [$this->lang, $this->timezone]);

        return $server->check_times($userinfo['id']);

    }

    /**
     * 获得角色配置
     *
     * @param $uInfoArr
     * @param array $store_info
     *
     */
    public function wf_role2($uInfoArr, $store_info = [])
    {
        $admin_group = UC('wmsRole')['admin_group'];
        $manage_region = $store_info['manage_region'];
        $store_category = $store_info['category'];
        $staff_info_id = $uInfoArr['id'];

        $wf_role = '';
        if (in_array($store_category, [1,2,10])) {
            //dc/sp/bdc
            if (isset($admin_group[$staff_info_id])) {
                $wf_role = '';
            } else {
                if ($this->getDistrictManagerId(['store_id'=>$store_info['id']])) {
                    if (in_array($manage_region, [1,2,3,4,25])) {
                        $wf_role = 'wmsv2_dcspbdc_dm_area1';
                    } else if (in_array($manage_region, [5,6,7,8,24,26])) {
                        $wf_role = 'wmsv2_dcspbdc_dm_area2';
                    }
                } else {
                    if (in_array($manage_region, [1,2,3,4,25])) {
                        $wf_role = 'wmsv2_dcspbdc_am_area1';
                    } else if (in_array($manage_region, [5,6,7,8,24,26])) {
                        $wf_role = 'wmsv2_dcspbdc_am_area2';
                    }
                }
            }
            //$wf_role = isset($admin_group[$staff_info_id]) ? 'wmsv2_dcsp_admin' : 'wmsv2_dcsp';
        } elseif (in_array($store_category, [4, 5, 7])) {
            //shop os
            // $wf_role = isset($admin_group[$staff_info_id]) ? 'wmsv2_shop_admin' : 'wmsv2_shop';
            if (isset($admin_group[$staff_info_id])) {
                $wf_role = '';
            } else {
                if ($store_category == enums::$stores_category['shop_ushop']) {
                    $wf_role = 'wmsv_shop_am_ushop';
                } else {
                    $wf_role = 'wmsv_shop_am_oshop';
                }
            }
        } elseif (in_array($store_category, [9])) {
            $wf_role = "wmsv3_os";
        }
//        elseif (in_array($store_category, [8])) {
//            //hub
//            $wf_role = isset($admin_group[$staff_info_id]) ? 'wmsv2_hub_admin' : 'wmsv2_hub';
//        }

        return $wf_role;
    }


    /**
     * 获取网点对应的DistrictManager
     * @Access  public
     * @param   $paramIn
     * @return  string
     */
    public function getDistrictManagerId($paramIn = [])
    {
        $storeId = $paramIn["store_id"] ?? "";
        if (!$storeId) {
            return "";
        }
        $sql        = "--
                SELECT 
                    hsmr.* 
                FROM sys_store ss
                JOIN `hr_staff_manage_regions` hsmr ON ss.manage_region = hsmr.region_id and ss.manage_piece = hsmr.piece_id 
                JOIN `hr_staff_info` hsi on hsi.`staff_info_id` = hsmr.`staff_info_id` 
                WHERE ss.id = ? and ss.state = 1 and hsi.`state` = 1";
        $returnData = $this->getDI()->get('db_bi')->query($sql, [$storeId])->fetch(\Phalcon\Db::FETCH_ASSOC);

        if (empty($returnData)) {
            return "";
        } else {
            return $returnData['staff_info_id'];
        }
    }

    /**
     * 获取网点对应的RegionManager
     * @Access  public
     * @param string $storeId
     * @param int $jobTitle
     * @return  string
     */
    public function getAreaManagerId(string $storeId, int $jobTitle)
    {
        if (!$storeId || !$jobTitle) {
            return "";
        }
        $sql        = "--
                SELECT 
                    hsmr.* 
                FROM sys_store ss
                JOIN `hr_staff_manage_regions` hsmr ON ss.manage_region = hsmr.region_id
                JOIN `hr_staff_info` hsi on hsi.`staff_info_id` = hsmr.`staff_info_id` 
                WHERE ss.id = ? and hsi.job_title = ? and ss.state = 1 and hsi.`state` = 1";
        $returnData = $this->getDI()->get('db_bi')->query($sql, [$storeId, $jobTitle])->fetch(\Phalcon\Db::FETCH_ASSOC);

        if (empty($returnData)) {
            return "";
        } else {
            return $returnData['staff_info_id'];
        }
    }

    /**
     * 获得角色配置
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function wf_role($uInfoArr, $store_info = [])
    {
        $admin_group = UC('wmsRole')['admin_group'];
        $manage_region = $store_info['manage_region'];
        $store_category = $store_info['category'];
        $organization_id = $store_info['id'];
        $staff_info_id = $uInfoArr['id'];
        $wf_role = '';
        if (isset($admin_group[$staff_info_id])) {
            //dc sp
            if ($store_category == 1 || $store_category == 2) {
                if ($manage_region == 1 || $manage_region == 2) {
                    $wf_role = 'wms_dcsp12';
                } else if ($manage_region == 3 || $manage_region == 8) {
                    $wf_role = 'wms_dcsp38';
                } else if ($manage_region == 4 || $manage_region == 5) {
                    $wf_role = 'wms_dcsp45';
                } else if ($manage_region == 6 || $manage_region == 7) {
                    $wf_role = 'wms_dcsp67';
                }
            } else if ($store_category == 4 || $store_category == 5) {
                $wf_role = 'wms_shop';
            } else if ($store_category == 7) {
                $wf_role = 'wms_ushop';
            } else if ($admin_group[$staff_info_id] == $organization_id) {
                $wf_role = 'wms_hub_' . $organization_id;
            }
        } else {
            //dc sp
            if ($store_category == 1 || $store_category == 2) { //Area1 ~ Area4
                if (in_array($manage_region, [1, 2, 3, 4])) {
                    $wf_role = 'wms3_dcsp12';
                } else if (in_array($manage_region, [5, 6, 7, 8])) { //Area5 ~ Area8
                    $wf_role = 'wms3_dcsp38';
                } else if ($manage_region == 24) { //Area9
                    $wf_role = 'wms3_dcsp45';
                }
            } else if ($store_category == 4 || $store_category == 5) {
                $wf_role = 'wms3_shop';
            } else if ($store_category == 7) {
                $wf_role = 'wms3_ushop';
            } else if ( in_array( $organization_id,$admin_group )  ) {
                $wf_role = 'wms3_hub_' . $organization_id;
            }
        }
        return $wf_role ?? '';
    }

    /**
     * 审批物料申请
     * @throws \Exception
     */
    public function updateWmsOrder($paramIn, $userinfo)
    {
        $staffId = $this->processingDefault($userinfo, 'id', 2);
        $status = $this->processingDefault($paramIn, 'status', 2);
        $reason = $this->processingDefault($paramIn, 'reject_reason', 1);
        $id = $this->processingDefault($paramIn, 'audit_id', 2);
        $approvalData = $this->processingDefault($paramIn, 'approval_data', 2);
        $logType = 9;

        //获取详情
        $info = $this->wms->getWmsOrderR($id);
        if (empty($info)) {
            throw new \Exception($this->getTranslation()->_('4008'));
        }
        //已经是最终状态,不能撤销
        if ($info['order_status'] != 1 && $status == 4) {
            throw new \Exception($this->getTranslation()->_('4008'));
        }
        if ($approvalData > 0) {
            throw new \Exception("'approval_data' invalid input");
        }

        self::$approval_arr = isset($paramIn['approval_arr']) && $paramIn['approval_arr'] ? $paramIn['approval_arr'] : [];
        self::$paramsIn = array_merge($paramIn, ['userinfo' => $userinfo]);

        if ($paramIn['status'] == enums::$audit_status['approved']) {
            $res = (new ApprovalServer($this->lang, $this->lang))
                ->approval($info['id'], enums::$audit_type['MA'], $userinfo['id']);
        } else if ($paramIn['status'] == enums::$audit_status['dismissed']) {
            $res = (new ApprovalServer($this->lang, $this->lang))
                ->reject($info['id'], enums::$audit_type['MA'], $paramIn['reject_reason'], $userinfo['id']);

        } else if ($paramIn['status'] == enums::$audit_status['revoked']) {
            $res = (new ApprovalServer($this->lang, $this->lang))
                ->cancel($info['id'], enums::$audit_type['MA'], $paramIn['reject_reason'], $userinfo['id']);
        }

        //无法获取审批流
        if (!isset($res) || empty($res)) {
            throw new \Exception($this->getTranslation()->_('asset_or_wms_to_scm_error'));
        }
        return $this->checkReturn(['data' => ['audit_id' => $id]]);
    }

    public function goodsRecommnetNumSV2($organization_id, $paramIn = [])
    {
        $req = ['locale' => $this->lang, 'store_id' => $organization_id];
        $applyNum = $paramIn['num']; //申请数量
        $inventory = $paramIn['inventory'];  //网点库存
        if (array_key_exists($paramIn['bar_code'], self::FEXCODES_BLUE)) {
            if ($inventory >= self::FEXCODES_BLUE[$paramIn['bar_code']]) {
                return 0;
            } else {
                if ((self::FEXCODES_BLUE[$paramIn['bar_code']] - $inventory) > ($applyNum - $inventory)) {
                    $k = ceil(($applyNum - $inventory) / self::FEXCODES_BLUE_N[$paramIn['bar_code']]) * self::FEXCODES_BLUE_N[$paramIn['bar_code']];
                    if ($k < 0) {
                        if ((self::FEXCODES_BLUE[$paramIn['bar_code']] - $inventory) > $applyNum) {
                            return $applyNum;
                        } else {
                            return self::FEXCODES_BLUE[$paramIn['bar_code']] - $inventory;
                        }
                    } else {
                        return $k;
                    }
                } else {
                    return ceil((self::FEXCODES_BLUE[$paramIn['bar_code']] - $inventory) / self::FEXCODES_BLUE_N[$paramIn['bar_code']]) * self::FEXCODES_BLUE_N[$paramIn['bar_code']];
                }
            }
        } else if (in_array($paramIn['bar_code'], self::FEXCODES_OREGIN)) {
            $fle_rpc = (new ApiClient('bi_rpc','','pickup_delivery_count_amonth_bystoreid', $this->lang));
            $fle_rpc->setParams($req);
            $biData = $fle_rpc->execute();


            $pickupCount = $biData['result']['pickupCount'] ?? 0;//上个月网点发件量
            $nextPickup = ceil($pickupCount * 1.4); //预计发件
            if ($paramIn['bar_code'] == self::FEXCODE_238) {
                $nextPickup = ceil($nextPickup * 0.6);
            } else if ($paramIn['bar_code'] == self::FEXCODE_289) {
                $nextPickup = ceil($nextPickup / 500 );
            } else if ($paramIn['bar_code'] == self::FEXCODE_031) {
                $nextPickup = ceil($nextPickup / 200 );
            } else if ($paramIn['bar_code'] == self::FEXCODE_032) {
                $nextPickup = ceil($nextPickup / 200 );
            } else if ($paramIn['bar_code'] == self::FEXCODE_033) {
                $nextPickup = ceil($nextPickup / 2);
            } else if ($paramIn['bar_code'] == self::FEXCODE_034) {
                $nextPickup = ceil($nextPickup / 2);
            } else if ($paramIn['bar_code'] == self::FEXCODE_042) {
                $nextPickup = ceil($nextPickup * 0.6);
            } else if ($paramIn['bar_code'] == self::FEXCODE_180) {
                $nextPickup = ceil($nextPickup / 32);
            } else if ($paramIn['bar_code'] == self::FEXCODE_182) {
                $nextPickup = ceil($nextPickup / 500 );
            }

            if ($inventory >= $nextPickup) {
                return 0;
            } else {
                if (($nextPickup-$inventory) > ($applyNum - $inventory)) {
                    return ceil($applyNum / self::FEXCODES_OREGIN_N[$paramIn['bar_code']]) * self::FEXCODES_OREGIN_N[$paramIn['bar_code']];
                } else {
                    return ceil(($nextPickup - $inventory) / self::FEXCODES_OREGIN_N[$paramIn['bar_code']]) * self::FEXCODES_OREGIN_N[$paramIn['bar_code']];
                }
            }
        } else if (in_array($paramIn['bar_code'], self::FEXCODES_YELLOW)) {
            $fle_rpc = (new ApiClient('bi_rpc','','pickup_delivery_count_amonth_bystoreid', $this->lang));
            $fle_rpc->setParams($req);
            $biData = $fle_rpc->execute();

            $pickupCount = $biData['result']['pickupCount'] ?? 0;//上个月网点发件量
            $nextPickup = ceil($pickupCount * 1.4); //预计发件
            if ($applyNum < $nextPickup) {
                return ceil($applyNum / self::FEXCODES_YELLOW_N[$paramIn['bar_code']]) * self::FEXCODES_YELLOW_N[$paramIn['bar_code']];
            } else {
                return ceil($nextPickup / self::FEXCODES_YELLOW_N[$paramIn['bar_code']]) * self::FEXCODES_YELLOW_N[$paramIn['bar_code']];
            }
        } else if (in_array($paramIn['bar_code'], self::FEXCODES_GREEN)) {

            $fle_rpc = (new ApiClient('bi_rpc','','pickup_delivery_count_amonth_bystoreid', $this->lang));
            $fle_rpc->setParams($req);
            $biData = $fle_rpc->execute();

            $pickupCount = $biData['result']['pickupCount'] ?? 0;//上个月网点发件量
            $nextPickup = ceil($pickupCount * 1.4); //预计发件
            //空白扎带应申请量
            $store = $this->getDI()->get("db_rbi")->query("
                --
                select 
                    ss.id, 
                    ss.category, 
                    sp.code, 
                    sp.manage_geography_code 
                from 
                    sys_store ss 
                    left join sys_province sp on ss.province_code = sp.code 
                where ss.id = '" . $organization_id . "'")->fetch(\PDO::FETCH_ASSOC);
            if ($store['category'] == 1 && in_array($store['manage_geography_code'], [
                    self::BIG_AREA_CE,
                    self::BIG_AREA_CW,
                    self::BIG_AREA_NE,
                    self::BIG_AREA_B
                ])) {
                //dc
                if ($nextPickup <= 199*30) {
                    $fex266Num = $nextPickup / 12;
                } else if ($nextPickup <= 399 * 30) {
                    $fex266Num = $nextPickup / 20;
                } else if ($nextPickup <= 599 * 30) {
                    $fex266Num = $nextPickup / 25;
                } else if ($nextPickup <= 799 * 30) {
                    $fex266Num = $nextPickup / 30;
                } else if ($nextPickup <= 1000 * 30) {
                    $fex266Num = $nextPickup / 35;
                } else {
                    $fex266Num = $nextPickup / 40;
                }
            } else {
                if ($nextPickup <= 199*30) {
                    $fex266Num = $nextPickup / 8;
                } else if ($nextPickup <= 399 * 30) {
                    $fex266Num = $nextPickup / 12;
                } else if ($nextPickup <= 599 * 30) {
                    $fex266Num = $nextPickup / 15;
                } else if ($nextPickup <= 799 * 30) {
                    $fex266Num = $nextPickup / 20;
                } else if ($nextPickup <= 1000 * 30) {
                    $fex266Num = $nextPickup / 25;
                } else {
                    $fex266Num = $nextPickup / 30;
                }
            }

            if ($paramIn['bar_code'] == self::FEXCODE_226) {
                if ($inventory >= $fex266Num) {
                    return 0;
                } else {
                    if (($fex266Num - $inventory) > ($applyNum - $inventory)) {
                        return ceil($applyNum / 100) * 100;
                    } else {
                        return ceil(($fex266Num - $inventory) / 100) * 100;
                    }
                }
            } else if ($paramIn['bar_code'] == self::FEXCODE_188) {
                if ($inventory >= $fex266Num) {
                    return 0;
                } else {
                    if (($fex266Num - $inventory) > ($applyNum - $inventory)) {
                        return ceil($applyNum / 100) * 100;
                    } else {
                        return ceil(($fex266Num - $inventory) / 100) * 100;
                    }
                }
            } else if ($paramIn['bar_code'] == self::FEXCODE_288) {
                $fex266Num = $fex266Num * 0.1;
                if ($inventory >= $fex266Num) {
                    return 0;
                } else {
                    if (($fex266Num - $inventory) > ($applyNum - $inventory)) {
                        return ceil($applyNum / 50) * 50;
                    } else {
                        return ceil(($fex266Num - $inventory) / 50) * 50;
                    }
                }
            } else if ($paramIn['bar_code'] == self::FEXCODE_225) {
                $fex266Num = $fex266Num / 18;
                if ($inventory >= $fex266Num) {
                    return 0;
                } else {
                    if (($fex266Num - $inventory) > ($applyNum - $inventory)) {
                        return ceil($applyNum / 5) * 5;
                    } else {
                        return ceil(($fex266Num - $inventory) / 5) * 5;
                    }
                }
            }


        }

        return 0;
    }


    public function goodsRecommentNumS($organization_id, $paramIn = [])
    {
        //BI请求接口揽件量、派件量和中转量
        $req = ['locale' => $this->lang, 'store_id' => $organization_id];

        $fle_rpc = (new ApiClient('bi_rpc','','pickup_delivery_count_aweek_bystoreid', $this->lang));
        $fle_rpc->setParams($req);
        $biData = $fle_rpc->execute();

        $pickupCount = $biData['result']['pickupCount'] ?? 0;//本周网点发件量
        $nextPickup = $pickupCount * 1.2; //预计发件
        $bar_code = $paramIn['bar_code'];
        $goodsInfo = $this->wms->goodsInfo($paramIn['bar_code']);
        $applyNum = $paramIn['num']; //申请数量
        $inventory = $paramIn['inventory'];  //网点库存
        $nuit = $goodsInfo['nuit_detail'] ?? 1; //单位明细
        $M = $K = $L = $J = 0;
        /* 需求见        https://shimo.im/sheets/3RVhw6dWHWqKJTKr/4qVCk        */
        if ($bar_code == 'FEX00007') {//封箱胶带切割器 如果网点库存≥3，取0，反之取【3-网点库存】
            if ($inventory < 3) {
                $J = 3 - $inventory;
            }
            $M = $J;
        } else if ($bar_code == 'FEX00022' || $bar_code == 'FEX00040') {//小心易碎纸贴 COD贴纸
            $J = ($nextPickup > 0) ? self::nuitNum($nextPickup, $nuit) : 0;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00104') {//气泡膜 1.30 m.x100 m.
            if ($inventory < 1) {
                $J = $applyNum;
            }
            $K = 1;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
        } else if ($bar_code == 'FEX00148' || $bar_code == 'FEX00187' || $bar_code == 'FEX00188') {//简易集包袋
            $M = $applyNum;
        } else if ($bar_code == 'FEX00025' || $bar_code == 'FEX00026') {//Mini号纸箱  S号纸
            $J = ($applyNum > 0) ? self::nuitNum($applyNum, $nuit) : 60;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00027' || $bar_code == 'FEX00028' || $bar_code == 'FEX00029') {//S+号纸 M号纸箱  M+号
            $J = ($applyNum < 60) ? self::nuitNum($applyNum, $nuit) : 60;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00030') {//L号纸箱 [其实和上面FEX00027等相同，快速上线]
            $J = ($applyNum < 60) ? self::nuitNum($applyNum, $nuit) : 60;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00031' || $bar_code == 'FEX00032') {//透明胶带  Flash胶带
            $J = ($applyNum > 0) ? self::nuitNum($nextPickup, 200) : 0;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00033' || $bar_code == 'FEX00034') {//塑料袋A4 塑料袋A3
            $J = ($applyNum > 0) ? self::nuitNum($nextPickup / 2, 100) : 0;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00036') {//黑色尼龙袋
            $J = $applyNum;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00038') {//白色集包和封车扎带
            $J = $applyNum;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00186') {//面单保护塑料袋
            $J = ($nextPickup > 0) ? self::nuitNum($nextPickup, $nuit) : 0;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00042') {//纸袋
            $J = ($nextPickup > 0) ? self::nuitNum($nextPickup * 0.3, $nuit) : 0;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00180') {//蓝牙面单纸 2层
            $J = ($nextPickup > 0) ? self::nuitNum($nextPickup / 32, $nuit) : 0;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00182') {//PC面单纸 2 层
            $J = ($nextPickup > 0) ? self::nuitNum($nextPickup / 500, $nuit) : 0;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00045') {//拉伸膜
            $J = ($inventory >= 1) ? 0 : $applyNum;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        } else if ($bar_code == 'FEX00048') {//小标签
            $J = ($applyNum > 0) ? self::nuitNum($applyNum, $nuit) : 0;
            $K = $J - $inventory;
            $L = $applyNum - $inventory;
            $M = ($L - $K) > 0 ? $K : $L;
            $M = self::nuitNum($M, $nuit);
        }
        $M = $M > 0 ? $M : 0;
        return ['recomment_num' => strval($M) ];
    }


    public function nuitNum($num, $nuit)
    {
        if ($nuit && ($num % $nuit)) {
            $n = ceil($num / $nuit);
            return $n * $nuit;
        } else {
            return $num;
        }
    }

	/**
	 * @description: 这些职位获取负责的大区
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/9/15 15:57
	 */

	public function getJobCategory($staffId = '', $job_title = '')
	{
		try {
			if (empty($staffId) || empty($job_title)) {
				throw new \Exception('传入的信息不对!');
			}
			$region = [];
			//判断是否属于查询片区的职位
			if (in_array($job_title, [enums::$job_title['district_manager'],
			])) {
				//查询片区
				$list = SysManagePieceModel::find(
					[
						'conditions' => 'manager_id = :manager_id:  and deleted = :deleted:',
						'bind'       => ['manager_id' => $staffId, 'deleted' => '0'],
						'columns'    => 'manage_region_id'
					]
				)->toArray();
				if (!empty($list)) {
					$region = array_unique(array_column($list, 'manage_region_id'));
				}
			}
			//判断是否查询大区
			if (in_array($job_title, [enums::$job_title['regional_manager'],
			])) {
				//查询片区
				$list = SysManageRegionModel::find(
					[
						'conditions' => 'manager_id = :manager_id:  and deleted = :deleted:',
						'bind'       => ['manager_id' => $staffId, 'deleted' => '0'],
						'columns'    => 'id'
					]
				)->toArray();
				if (!empty($list)) {
					$region = array_unique(array_column($list, 'id'));
				}
			}
			//获取本人所属的大区
			if(in_array($job_title, [enums::$job_title['branch_supervisor']])){
				//[3]查询指定级别
				$builder = $this->modelsManager->createBuilder();
				$builder->columns('hsi.staff_info_id,hsi.sys_store_id,ss.manage_region as manage_region ');
				$builder->from(['hsi' => HrStaffInfoModel::class]);
				$builder->leftJoin(SysStoreModel::class,'hsi.sys_store_id = ss.id','ss');
				$builder->where('hsi.staff_info_id = :staff_info_id:', ['staff_info_id' => $staffId]);
				$manage_region_info = $builder->getQuery()->execute()->getFirst();
				if(!empty($manage_region_info) && !empty($manage_region_info->manage_region) ){
					$region[] = $manage_region_info->manage_region;
				}

			}


			//DM/AM，任何大区/片区都不负责，19060收到审批    强制归属 1 大区
			return empty($region) ? ['1'] : $region;
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('getJobCategory file ' . $e->getFile() . ' line ' . $e->getLine() . ' msg ' . $e->getMessage(), 'info');
			return [];
		}


	}

// ------------- 泰国的 country  搬过来的

    public static $symbol = '฿';

    public function check_times($staff_id)
    {
        $time = date("Y-m-01");
        $Detail_data = WmsOrderModel::find([
            'conditions' => 'order_status in (1, 2)  and apply_user = :staff_id:   and  created_at > :time:',
            'bind' => ['staff_id' => $staff_id,'time' => $time]
        ])->toArray();
//        $sql = "SELECT * FROM wms_order where order_status in (1, 2)  and apply_user = {$staff_id}   and  `created_at` > '{$time}'";
//        $Detail_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $Detail_data;
    }


    public function format_amount($amount)
    {
        return self::$symbol . sprintf('%.2f', $amount);
    }

    // ------------country  迁移 结束 ----------

}
