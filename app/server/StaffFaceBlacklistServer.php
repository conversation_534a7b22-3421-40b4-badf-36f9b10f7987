<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\Mail;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HireTypeImportListModel;
use FlashExpress\bi\App\Models\backyard\HoldStaffManageModel;
use FlashExpress\bi\App\Models\backyard\HrBlackGreyListModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\LeaveScenarioModel;
use FlashExpress\bi\App\Models\backyard\StaffFaceBlacklistHitRecordModel;
use FlashExpress\bi\App\Models\backyard\StaffFaceBlacklistModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReasonModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\TmpCheckFaceBlacklistModel;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\JobTitleRepository;
use FlashExpress\bi\App\Repository\StaffRepository;


class StaffFaceBlacklistServer extends BaseServer
{

    protected $staff_info_id      = 0;
    protected $staffInfo          = [];
    protected $matchStaffInfo     = [];
    protected $params             = [];
    protected $matchFaceBlacklist = null;

    protected $hold_reason = 'suspicion_of_corruption/theft_of_public_property';

    const TMP_SEND_STAFF_DATA_FIELD = [
        'staff_info_id',
        'identity',
        'staff_name',
        'formal_text',
        'hire_type_text',
        'job_name',
        'department_name',
        'region_name',
        'piece_name',
        'store_name',
        'staff_state_text',
        'hire_date',
        'leave_date',
        'stop_duties_date',
        'leave_source_text',
        'leave_reason_text',
        'leave_type_text',
        'manger',
        'face_img_url',
    ];


    /**
     * 设置员工照片信息
     * @throws Exception
     */
    public function setFaceUrl()
    {
        $this->staffInfo['face_img_url'] = $this->getDI()->getConfig()->application['img_prefix'] . $this->params['face_img_url'];
        $blackModel                      = StaffFaceBlacklistModel::findFirst($this->params['match_face_blacklist_id']);
        if (empty($blackModel)) {
            throw new Exception('StaffFaceBlacklistModel 数据异常。ID:' . $this->params['match_face_blacklist_id']);
        }
        $this->matchStaffInfo['face_img_url'] = $blackModel->face_negatives_url;
    }

    /**
     * 设置员工停职离职信息
     * @param $updateInfo
     * @return void
     */
    protected function setStaffInfo($updateInfo)
    {
        $this->staffInfo = array_merge($this->staffInfo, $updateInfo);
    }


    /**
     * 是否进行人脸黑名单比对
     * @param $staff_info_id
     * @return bool
     */
    public function isCheckFaceBlackList($staff_info_id): bool
    {
        if (!isCountry(['TH', 'PH', 'MY'])) {
            return false;
        }

        if ($_master_staff_id = StaffRepository::getMasterStaffIdBySubStaff($staff_info_id)) {
            $staff_info_id = $_master_staff_id;
        }

        $staffInfo = (new StaffServer())->getStaffById($staff_info_id,
            ['staff_info_id', 'job_title', 'node_department_id','formal']);
        if (empty($staffInfo) || !in_array($staffInfo['formal'], [HrStaffInfoModel::FORMAL_0,HrStaffInfoModel::FORMAL_1,HrStaffInfoModel::FORMAL_INTERN])) {
            return false;
        }
        $settingServer       = new SettingEnvServer();
        $config              = $settingServer->getMultiEnvByCode([
            'Blackface_verification_position',
            'Blackface_verification_department',
        ]);
        $faceBlackJobTitle   = empty($config['Blackface_verification_position']) ? [] : explode(',',
            $config['Blackface_verification_position']);
        $faceBlackDepartment = empty($config['Blackface_verification_department']) ? [] : explode(',',
            $config['Blackface_verification_department']);

        $departmentInfo = (new SysDepartmentServer())->getDepartmentDetail($staffInfo['node_department_id'],
            ['ancestry_v3']);
        $deptList       = explode('/', $departmentInfo['ancestry_v3']);
        return in_array($staffInfo['job_title'], $faceBlackJobTitle) || array_intersect($deptList,
                $faceBlackDepartment);
    }


    /**
     * 匹配后是否跳过
     * @return false
     */
    public function isSkip($staff_info_id,$match_face_blacklist): bool
    {
        $staffInfo = (new StaffServer())->getStaffById($staff_info_id);

        if (isCountry('TH') && in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            $identity[] = $staffInfo['identity'];
            if ($match_face_blacklist && $match_face_blacklist->identity != $staffInfo['identity']) {
                $identity[] = $match_face_blacklist->identity;
            }
            $agent_black_list_category = (new SettingEnvServer())->getSetVal('resume_hire_type_agent_black_list_category',
                ',');
            if (empty($agent_black_list_category)) {
                return false;
            }
            $grey_blacklist_identity = HrBlackGreyListModel::findFirst([
                'conditions' => 'identity in ({identity:array}) and status = :status: and behavior_type = :behavior_type: and category in ({category:array})',
                'bind'       => [
                    'identity'      => $identity,
                    'status'        => HrBlackGreyListModel::STATUS_ON,
                    'behavior_type' => HrBlackGreyListModel::BEHAVIOR_TYPE_BLACK,
                    'category'      => $agent_black_list_category,
                ],
            ]);
            if (empty($grey_blacklist_identity)) {
                $this->logger->write_log(['staff_info_id'=>$staff_info_id,'match_face_blacklist'=>$match_face_blacklist->id,'isSkip'=>1], 'info');
                return true;
            }
        }
        return false;

    }



    /**
     * 是否匹配
     * @return bool
     */
    protected function isMatch(): bool
    {
        if (empty($this->params['match_face_blacklist_id'])) {
            return false;
        }
        return true;
    }

    protected function checkIsSendContract(): bool
    {
        if (isCountry('PH')) {
            return $this->staffInfo['sys_store_id'] != '-1';
        }
        return false;
    }


    /**
     * 不匹配的逻辑
     * @return bool
     */
    protected function notMatchLogic(): bool
    {
        $staffInfo = $this->staffInfo;
        //不在人脸黑名单 则发送合同
        if (in_array($staffInfo['formal'],
            [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN])) {
            //查看合同日志，没有就发
            $contractObj = HrStaffContractLogModel::FindFirst([
                'conditions' => 'staff_id = :staff_id: ',
                'bind'       => [
                    'staff_id' => $staffInfo['staff_info_id'],
                ],
            ]);
            if (!empty($contractObj)) {
                $this->logger->write_log("HrStaffContractLogModel-存在数据 " . $staffInfo['staff_info_id'], 'info');
                return true;
            }
            if (!$this->checkIsSendContract()) {
                // 职位不属于HCM-设置中心-系统设置-WinHR-first_line_jobs配置职位，且雇佣类型≠个人代理/兼职个人代理
                $this->logger->write_log("checkIsSendContract staff is not first_line_jobs and not ic " . $staffInfo['staff_info_id'], 'info');
                return true;
            }

            $redis = $this->getDI()->get('redisLib');
            $key = 'check_face_blacklist_send_contract:' . $staffInfo['staff_info_id'];
            if (!$redis->get($key)) {
                //电子合同入队列
                $data = ['type' => 'send_contract', 'staff_info_id' => $staffInfo['staff_info_id']];
                $rmq  = new RocketMQ('hr-contract-add');
                $redis->set($key, $staffInfo['staff_info_id'], 1200);
                return !empty($rmq->sendToMsg($data,60));
            }

        }
        return true;
    }



    /**
     * 处理员工基本信息
     * @param $staffInfo
     * @return array
     */
    protected function dealStaffBaseInfo($staffInfo): array
    {
        $result = [
            "staff_info_id"    => $staffInfo['staff_info_id'],
            "identity"         => $staffInfo['identity'],
            "staff_name"       => $staffInfo['name'],
            "job_name"         => $staffInfo['job_title'],
            "department_name"  => $staffInfo['node_department_id'],
            "region_name"      => '',
            "piece_name"       => '',
            "store_name"       => '',
            "hire_date"        => substr($staffInfo['hire_date'], 0, 10),
            "leave_date"       => substr($staffInfo['leave_date'], 0, 10),
            "stop_duties_date" => substr($staffInfo['stop_duties_date'], 0, 10),
            "manger"           => $staffInfo['manger'],
        ];
        $jobInfo = JobTitleRepository::getJobTitleInfo($staffInfo['job_title']);
        if ($jobInfo) {
            $result['job_name'] = $jobInfo->job_name;
        }
        $dept = (new SysDepartmentServer())->getDepartmentDetail($staffInfo['node_department_id']);
        if ($dept) {
            $result['department_name'] = $dept['name'];
        }

        $storeInfos = (new SysStoreServer())->batchStorePieceRegion([$staffInfo['sys_store_id']]);
        $storeInfo  = $storeInfos[$staffInfo['sys_store_id']] ?? [];
        if ($staffInfo) {
            $result['store_name']  = $storeInfo['store_name'] ?? enums::HEAD_OFFICE;
            $result['region_name'] = $storeInfo['region_name'] ?? '';
            $result['piece_name']  = $storeInfo['piece_name'] ?? '';
        }

        return $result;
    }

    /**
     * 处理员工需要翻译的信息
     * @param $staffInfo
     * @param $t
     * @return array
     */
    protected function dealStaffTranslationData(&$staffInfo, $lang): array
    {

        $t = $this->getTranslation($lang);
        $state                          = $staffInfo['wait_leave_state'] == 1 && $staffInfo['state'] == HrStaffInfoModel::STATE_ON_JOB ? 4 : $staffInfo['state'];
        $staffInfo['hire_type_text']    = $staffInfo['hire_type'] ? $t->_('hire_type_' . $staffInfo['hire_type']) : '';
        $staffInfo['formal_text']       = $t->_('formal_' . $staffInfo['formal']);
        $staffInfo['staff_state_text']  = $t->_('hris_working_state_' . $state);
        if($staffInfo['state'] == HrStaffInfoModel::STATE_2 && !empty($staffInfo['leave_reason'])) {
            $hcmCli      = new ApiClient('hcm_rpc', '', 'get_leave_source_reason',$lang);
            $hcmCli->setParams([
                'leave_source' => $staffInfo['leave_source'],
                'leave_reason' => $staffInfo['leave_reason'],
            ]);
            $hcmReturn = $hcmCli->execute();
            if (!isset($hcmReturn['error'])) {
                $staffInfo['leave_source_text'] = $hcmReturn['result']['leave_source_text'] ?? '';
                $staffInfo['leave_reason_text'] = $hcmReturn['result']['leave_reason_text'] ?? '';
            }
            $staffInfo['leave_type_text'] = $staffInfo['leave_type'] ? $t->_('leave_type_'. $staffInfo['leave_type']) :'';
        } else {
            $staffInfo['leave_source_text'] = '';
            $staffInfo['leave_reason_text'] = '';
            $staffInfo['leave_type_text']   = '';
        }

        return $staffInfo;
    }


    /**
     * 写数据表
     * @return bool
     */
    protected function handleDataRelated(): bool
    {
        $params                     = $this->params;
        $staffInfo                  = $this->staffInfo;
        $matchStaffInfo             = $this->matchStaffInfo;
        $model                      = new StaffFaceBlacklistHitRecordModel();
        $model->staff_info_id       = $staffInfo['staff_info_id'];
        $model->face_img_url        = env('img_prefix'). $params['face_img_url'];
        $model->face_blacklist_id   = $params['match_face_blacklist_id'];
        $model->date_at             = substr($params['action_time'], 0, 10);
        $model->black_staff_info_id = $params['match_staff_id'];
        $model->black_face_img_url  = $params['match_face_img_url'];
        $model->black_identity      = $this->matchFaceBlacklist->identity;
        $model->black_staff_name    = $matchStaffInfo['name'];
        $model->black_formal        = $matchStaffInfo['formal'];
        $model->black_hire_type     = $matchStaffInfo['hire_type'];
        $model->black_job_title     = $matchStaffInfo['job_title'];
        $model->black_department_id = $matchStaffInfo['node_department_id'];
        $model->black_store_id      = $matchStaffInfo['sys_store_id'];
        $model->black_staff_state   = $matchStaffInfo['state'] == HrStaffInfoModel::STATE_ON_JOB && $matchStaffInfo['wait_leave_state'] == 1 ? HrStaffInfoModel::STATE_PENDING_RESIGNATION : $matchStaffInfo['state'];
        $model->black_hire_date     = substr($matchStaffInfo['hire_date'], 0, 10);
        $model->black_leave_date    = $matchStaffInfo['leave_date'] ? substr($matchStaffInfo['leave_date'], 0, 10):null;
        $model->black_leave_source  = $matchStaffInfo['leave_source'];
        $model->black_leave_reason  = $matchStaffInfo['leave_reason'];
        $model->black_leave_type    = $matchStaffInfo['leave_type'];
        $model->black_manger        = $matchStaffInfo['manger'];
        $model->created_at          = zero_time_zone($params['action_time']);
        $model->updated_at          = zero_time_zone($params['action_time']);
        $model->save();
        $this->params['hit_record_id'] = $model->id;

        return true;
    }


    /**
     * 处理员工状态
     * @return bool
     * @throws BusinessException
     */
    protected function handleStaffState(): bool
    {
        $params    = $this->params;
        $staffInfo = $this->staffInfo;
        //[2.1]员工改离职
        if ($this->staffInfo['formal'] == 0) {
            $updateStaffInfo = [
                'staff_info_id' => $staffInfo['staff_info_id'],
                'state'         => HrStaffInfoModel::STATE_2,
                'leave_date'    => substr($params['action_time'], 0, 10),
                'operater' => '-1',
            ];
        } else {
            $updateStaffInfo = [
                'staff_info_id' => $staffInfo['staff_info_id'],
                'state'         => HrStaffInfoModel::STATE_2,
                'leave_date'    => substr($params['action_time'], 0, 10),
                'leave_source'  => HrStaffInfoModel::LEAVE_SOURCE_FACE_BLACKLIST,
                'leave_reason'  => StaffLeaveReasonModel::LEAVE_REASON_83,
                'leave_scenario' => LeaveScenarioModel::LEAVE_SCENARIO_48,
                'leave_type'    => HrStaffInfoModel::LEAVE_TYPE_DISMISSAL_NO_COMPENSATION,
                'operater'      => '-1',
            ];
        }
        $this->doUpdateStaffInfo($updateStaffInfo);

        return true;
    }

    /**
     * @throws BusinessException
     */
    protected function doUpdateStaffInfo($updateStaffInfo): bool
    {
        $this->setStaffInfo($updateStaffInfo);
        $hr_rpc = (new ApiClient('hr_rpc', '', 'update_staff_info', $this->lang));
        $hr_rpc->setParams($updateStaffInfo);
        $result = $hr_rpc->execute();
        if ($result['result']['code'] != 1) {
            throw new BusinessException(implode('', $result['result']['msg']));
        }
        return true;
    }


    /**
     * 处理hold
     * @return bool
     */
    protected function handleHold(): bool
    {
        $staffInfo = $this->staffInfo;
        //正式员工
        if (in_array($staffInfo['formal'], [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN])) {
            //[2.2]hold
            $hold_type = '1,2';                                // hold  1 工资 2提成
            if (in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
                $hold_type = '2';
            }
            $hold_params = [
                'staff_info_id' => $staffInfo['staff_info_id'],                     //员工id
                'hold_reason'   => $this->hold_reason,                              //hold原因
                'hold_remark'   => '',                                              //hold备注
                'hold_time'     => date('Y-m-d H:i:s'),                      //hold时间操作时间
                'hold_source'   => HoldStaffManageModel::HOLD_SOURCE_FACE_BLACKLIST,//来源 人脸黑名单
                'type'          => $hold_type,
                'handle_people' => 'HRO',
            ];
            $hcmCli      = (new ApiClient('hcm_rpc', '', 'synchronize_hold'));
            $hcmCli->setParams($hold_params);
            $hcmReturn = $hcmCli->execute();
        }
        return true;
    }

    /**
     * 给员工发通知消息
     * @return bool
     */
    protected function sendMessageToStaff(): bool
    {
        return true;
    }

    /**
     * 给员工的管理者发通知消息
     * @param $msg_to_other_staff
     * @return bool
     */
    protected function sendMessageToManager($msg_to_other_staff): bool
    {
        $staffInfo = $this->staffInfo;

        //获取员工所属部门OPS负责人
        $departmentInfo = (new DepartmentRepository())->getSpecDepartmentInfo($staffInfo['node_department_id'],
            'name,manager_id');
        if (!empty($departmentInfo['manager_id'])) {
            $msg_to_other_staff[] = $departmentInfo['manager_id'];
        }
        //对应区域的AM负责人
        $regionPieceManager = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getOrganizationRegionPieceManagerId($staffInfo['sys_store_id']);
        if (!empty($regionPieceManager['region_manager_id'])) {
            $msg_to_other_staff[] = $regionPieceManager['region_manager_id'];
        }

        //给该员工发送offer的TA
        $entryInfo = HrEntryModel::findFirst([
            'conditions' => 'staff_id = :staff_id: and status = :status:',
            'bind'       => ['staff_id' => $staffInfo['staff_info_id'], 'status' => HrEntryModel::STATUS_EMPLOYED],
            'columns'    => 'interview_offer_id',
        ]);
        if (!empty($entryInfo)) {
            $offerTa = HrInterviewOfferModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $entryInfo->interview_offer_id],
                'columns'    => 'submitter_id',
            ]);
            if (!empty($offerTa)) {
                $msg_to_other_staff[] = $offerTa->submitter_id;
            }
        }
        $msg_to_other_staff = array_values(array_filter(array_unique($msg_to_other_staff)));
        $this->logger->write_log(['msg_to_other_staff'=>$msg_to_other_staff],'info');
        $this->sendMessage($msg_to_other_staff);
        return true;
    }

    /**
     * 发送站内信
     * @param $msg_to_other_staff
     * @param string $title
     * @return void
     */
    protected function sendMessage($msg_to_other_staff, string $title = '21119_face_blacklist_msg_title')
    {
        $staffsLang               = (new StaffServer())->getBatchStaffLanguage(array_values($msg_to_other_staff));
        $current_base_staff_info = $this->dealStaffBaseInfo($this->staffInfo);
        $black_base_staff_info   = $this->dealStaffBaseInfo($this->matchStaffInfo);

        foreach ($msg_to_other_staff as $staff_id) {
            $staff_lang = $staffsLang[$staff_id] ?? "en";
            $t = $this->getTranslation($staff_lang);
            //消息
            $msgParams['staff_info_id'] = $staff_id;//数组 多个员工id
            $msgParams['title']         = $t->_($title);
            $send_data                  = $this->getSendStaffData($current_base_staff_info, $black_base_staff_info, $staff_lang);
            $msgParams['content']       = json_encode($send_data, JSON_UNESCAPED_UNICODE);
            $msgParams['category']      = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_FACE_BLACKLIST');
            $client                     = (new ApiClient('hcm_rpc', '', 'send_staff_message', 'en'));
            $client->setParams($msgParams);
            $client->execute();
        }
    }

    /**
     * 获取员工发送数据
     * @param $current_base_staff_info
     * @param $black_base_staff_info
     * @param $lang
     * @return array
     */
    protected function getSendStaffData($current_base_staff_info, $black_base_staff_info, $lang): array
    {
        $send_data['current_staff_info'] = array_only(
            array_merge(
                $this->dealStaffTranslationData($this->staffInfo, $lang),$current_base_staff_info), self::TMP_SEND_STAFF_DATA_FIELD);
        $send_data['black_staff_info']   = array_only(
            array_merge(
                $this->dealStaffTranslationData($this->matchStaffInfo, $lang),$black_base_staff_info), self::TMP_SEND_STAFF_DATA_FIELD);
        $this->logger->write_log(['send_data' => $send_data], 'info');

        return $send_data;
    }


    protected function sendEmail($email_box): bool
    {
        if (empty($email_box)) {
            return true;
        }
        $lang = 'en';
        $t = $this->getTranslation($lang);

        $current_base_staff_info = $this->dealStaffBaseInfo($this->staffInfo);
        $black_base_staff_info   = $this->dealStaffBaseInfo($this->matchStaffInfo);
        $send_data               = $this->getSendStaffData($current_base_staff_info, $black_base_staff_info, $lang);
        $send_data = $this->dealEmailData($send_data);

        //发送邮件
        $email_title = $t->_('21119_face_blacklist_email_title');
        $email_content = $t->_('21119_face_blacklist_email_content', $send_data);
        $sendResult     = Mail::send(explode(';', $email_box), $email_title, $email_content);
        $this->logger->write_log(['email_box'=>$email_box,'result'=>$sendResult], 'info');
        return true;
    }

    protected function dealEmailData($send_data): array
    {
        $result = [];
        [$current_staff_info, $black_base_staff_info] = [
            $send_data['current_staff_info'],
            $send_data['black_staff_info'],
        ];
        foreach ($current_staff_info as $key => $value) {
            $result['current_' . $key] = $value;
        }
        foreach ($black_base_staff_info as $key => $value) {
            $result['black_' . $key] = $value;
        }
        return $result;
    }



    /**
     * 获取发送消息的配置
     * @return array
     */
    protected function getSendMessageConfig(): array
    {
        $config = (new SettingEnvServer())->getMultiEnvByCode([
            'Blackface_verification_Email',
            'Blackface_verification_getmsg_ID',
        ]);
        if ($config) {
            $email_box          = $config['Blackface_verification_Email'] ?? '';
            $msg_to_other_staff = !empty($config['Blackface_verification_getmsg_ID']) ? array_filter(explode(',',
                $config['Blackface_verification_getmsg_ID'])) : [];
        }
        return [$email_box ?? '', $msg_to_other_staff ?? []];
    }


    /**
     * 发送消息通知
     * @return bool
     * @throws Exception
     */
    protected function handleNotice(): bool
    {
        [$email_box, $msg_to_other_staff] = $this->getSendMessageConfig();
        $this->setFaceUrl();
        $this->sendMessageToStaff();
        $this->sendMessageToManager($msg_to_other_staff);
        $this->sendEmail($email_box);
        return true;
    }



    /**
     * 匹配的逻辑
     * @return bool
     * @throws BusinessException
     */
    protected function matchLogic(): bool
    {

        $this->logger->write_log(["人脸黑名单命中" => $this->params], 'info');

        $db = StaffFaceBlacklistHitRecordModel::beginTransaction($this);

        try {
            $this->handleDataRelated();
            $this->handleStaffState();
            $this->handleHold();
            $this->handelFaceBlackList();
            $this->handleNotice();
            $this->createHC();
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 外协员工直接进人脸黑名单
     */
    protected function handelFaceBlackList()
    {

        if($this->staffInfo['formal'] == HrStaffInfoModel::FORMAL_0){
            $data = [
                'business_type'      => 'add_face_blacklist',
                'staff_info_id'      => $this->staffInfo['staff_info_id'],
                'identity'           => $this->staffInfo['identity'],
                'mobile'             => $this->staffInfo['mobile'],
                'submitter_staff_id' => 10000,
                'type'               => 'os_face_blacklist',
                'remark'             => '',
            ];
            $rmq  = new RocketMQ('add-face-blacklist');
            $rmq->sendOrderlyMsg($data, 3);
        }
    }

    protected function createHC(): bool
    {
        return true;
    }


    protected function initData($params): bool
    {
        $this->staff_info_id = $params['staff_info_id'];
        $this->staffInfo     = (new StaffServer())->getStaffById($params['staff_info_id']);
        $this->params        = $params;
        if (!empty($params['match_staff_id'])) {
            $this->matchStaffInfo = (new StaffServer())->getStaffById($params['match_staff_id']);
        }
        if (!empty($params['match_face_blacklist_id'])) {
            //看是否已经被移除了
            $checkFaceBlacklist = StaffFaceBlacklistModel::findFirst([
                'conditions' => 'id = :match_face_blacklist_id: and is_deleted = 0 ',
                'bind'       => ['match_face_blacklist_id' => $params['match_face_blacklist_id']],
            ]);
            if (empty($checkFaceBlacklist)) {
                $this->logger->write_log(['StaffFaceBlacklistModel-无生效中的数据' => $params], 'info');
            } else {
                $this->matchFaceBlacklist           = $checkFaceBlacklist;
                $this->params['match_face_img_url'] = $checkFaceBlacklist->face_negatives_url;
            }
        }
        return true;
    }

    /**
     * $params['staff_info_id']           = $staff_info_id;
     * $params['face_blacklist_identity'] = $faceBlacklist ? $faceBlacklist->identity : '';
     * $params['match_face_blacklist_id'] = $faceBlacklist ? $faceBlacklist->id : 0;
     * $params['match_staff_id']          = $faceBlacklist ? $faceBlacklist->staff_info_id : 0;
     * $params['action_time']             = date('Y-m-d H:i:s');
     * $params['business_type']           = 'deal_face_blacklist';
     * $params['face_img_url']           = 'sss.jpg';
     */

    /**
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function processFaceBlacklist($params): bool
    {
        $this->initData($params);

        $this->logger->write_log(['processFaceBlacklistStaffInfo' => $this->staffInfo], 'info');

        if (empty($this->staffInfo) || $this->staffInfo['state'] != HrStaffInfoModel::STATE_ON_JOB) {
            return true;
        }

        if ($this->isMatch()) {
            $result = $this->matchLogic();
        } else {
            $result = $this->notMatchLogic();
        }
        $this->afterExecute();
        return $result;
    }

    protected function afterExecute()
    {
        $this->setStaffAnnexInfoCheckState();
    }


    /**
     * 设置员工信息附件表人脸黑名单状态
     * @return bool
     */
    protected function setStaffAnnexInfoCheckState(): bool
    {
        return true;
    }



    /**
     */
    public function checkAll($staff_info_id = 0, $max_id = 0)
    {
        $server = new AttendanceServer('en', '+08:00');
        //调用ai接口 返回工号和证件号
        while (true) {
            $staff_list = $this->getStaffList(intval($staff_info_id), intval($max_id));
            if (empty($staff_list)) {
                break;
            }
            $list = [];
            foreach ($staff_list as $staff) {
                $format['bucket'] = $staff['work_attendance_bucket'];
                $format['path']   = $staff['work_attendance_path'];
                if (RUNTIME == 'pro') {
                    $flag = 1;
                }
                $source_url = $server->format_oss($format, $flag ?? 0);
                $this->logger->write_log(['checkAllStaffFaceBlacklist' => [$staff['staff_info_id'], $source_url]],
                    'info');
                try {
                    $faceBlacklist = $server->checkInFaceBlacklist($staff['staff_info_id'], $source_url);
                    if (empty($faceBlacklist)) {
                        $max_id = $staff['staff_info_id'];
                        continue;
                    }
                    [$black_identity, $face_negatives_url] = [
                        $faceBlacklist->identity,
                        $faceBlacklist->face_negatives_url,
                    ];
                } catch (Exception $e) {
                    [$black_identity, $face_negatives_url] = ['', $e->getMessage()];
                }
                $listItem                   = [];
                $listItem['staff_info_id']  = $staff['staff_info_id'];
                $listItem['staff_face_img'] = $this->getDI()->getConfig()->application['img_prefix'] . $staff['work_attendance_path'];
                $listItem['black_identity'] = $black_identity;
                $listItem['black_face_img'] = $face_negatives_url;
                $max_id                     = $staff['staff_info_id'];
                $list[]                     = $listItem;
            }
            if ($list) {
                (new TmpCheckFaceBlacklistModel())->batch_insert($list, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
            }
            sleep(2);
        }
    }


    /**
     * 获取员工数据
     * @param int $staff_info_id
     * @param int $max_id
     * @return mixed
     */
    protected function getStaffList(int $staff_info_id = 0, int $max_id = 0)
    {
        $limit = 300;
        if (RUNTIME == 'dev') {
            $limit = 2;
        }
        $sql = "select i.staff_info_id,i.identity,i.job_title,i.node_department_id,a.work_attendance_path,a.work_attendance_bucket,i.sys_store_id,i.formal,i.hire_type,i.state,i.wait_leave_state from hr_staff_info as i inner join staff_work_attendance_attachment as a on i.staff_info_id = a.staff_info_id and a.deleted = 0  where  i.staff_info_id > {$max_id} ";
        if ($staff_info_id) {
            $sql .= " and i.staff_info_id = $staff_info_id";
        }
        $sql .= " and i.state in (1,3)  and is_sub_staff = 0 order by i.staff_info_id asc limit $limit";
        return $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }


    /**
     * 绑定where条件
     * @param $params
     * @return null
     */
    protected function createBuilder($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['b' => StaffFaceBlacklistModel::class]);
        $builder->andWhere("b.is_deleted = :is_deleted:", ['is_deleted' => 0]);
        //添加时间 开始
        if (!empty($params['start_date'])) {
            $_start_time = zero_time_zone($params['start_date']);
            $builder->andWhere("b.created_at >= :start_time:", ['start_time' => $_start_time]);
        }
        //添加时间 结束
        if (!empty($params['end_date'])) {
            $_end_time = zero_time_zone(date('Y-m-d H:i:s', strtotime($params['end_date'] . ' + 1 days') - 1));
            $builder->andWhere("b.created_at <= :end_time:", ['end_time' => $_end_time]);
        }
        return $builder;
    }

    /**
     * 列表总数
     * @param $params
     * @return int
     */
    protected function countData($params)
    {
        $builder = $this->createBuilder($params);
        $count   = $builder->columns('count(1) as count')->getQuery()->getSingleResult();
        return intval($count->count);
    }

    /**
     * 列表数据
     * @param $params
     * @return array
     */
    protected function listData($params)
    {
        $size    = $params['size'] ?? 20;
        $page    = $params['page'] ?? 1;
        $offset  = $size * ($page - 1);
        $builder = $this->createBuilder($params);
        //查询列表
        $builder->columns([
            'b.id',
            'b.staff_info_id',
            'b.face_negatives_url',
            'b.created_at',
        ]);
        $builder->limit($size, $offset);
        $builder->orderBy('b.id desc');
        $rest = $builder->getQuery()->execute()->toArray();

        $data = [];
        foreach ($rest as $item) {
            $models      = StaffWorkAttendanceModel::find([
                'conditions' => 'staff_info_id = :staff_info_id: and (started_path is not null or end_path is not null)',
                'bind'       => ['staff_info_id' => $item['staff_info_id']],
                'order'      => 'id desc',
                'limit'      => 5,
            ]);
            $image_array = $item['face_negatives_url'] ? [$item['face_negatives_url']] : [];
            foreach ($models as $model) {
                if (!empty($model->started_path)) {
                    $image_array[] = env('img_prefix') . $model->started_path;
                }
                if (!empty($model->end_path)) {
                    $image_array[] = env('img_prefix') . $model->end_path;
                }
            }
            if (empty($image_array)) {
                continue;
            }
            $data[] = [
                'staff_info_id' => $item['staff_info_id'],
                'img_url'       => array_splice($image_array, 0, 5),
                'created_at'    => show_time_zone($item['created_at']),
            ];
        }
        return $data;
    }

    /**
     * 人脸黑名单 人脸图片数据
     * @param $params
     * @return array
     */
    public function getFaceNegativesData($params): array
    {
        $result['list']  = $this->listData($params);
        $result['total'] = $this->countData($params);
        return $result;
    }

    /**
     * 员工离职 复制hc
     * @param $staff_info_id
     */
    public function copyHc($staff_info_id)
    {

        $entry              = HrEntryModel::findFirst([
            'conditions' => 'staff_id = :staff_id: and status = :status:',
            'bind'       => ['staff_id' => $staff_info_id, 'status' => HrEntryModel::STATUS_EMPLOYED],
            'order'      => 'entry_id desc',
        ]);

        $hireTypeImportList = HireTypeImportListModel::findFirst([
            'conditions' => 'new_staff_id = :staff_id:',
            'bind'       => ['staff_id' => $staff_info_id],
        ]);

        if (!empty($entry)) {
            $hc_id = $entry->hc_id;
        }
        if (!empty($hireTypeImportList)) {
            $hc_id = $hireTypeImportList->hc_id;
        }

        if (empty($hc_id)) {
            $this->logger->write_log(['人脸黑名单复制hc 入职信息未找到hc id' => $staff_info_id]);
            return false;
        }
        $oldHcModel = HrHcModel::findFirst([
            'conditions' => 'hc_id = :hc_id:',
            'bind'       => ['hc_id' => $hc_id],
        ]);
        if (empty($oldHcModel)) {
            $this->logger->write_log(['人脸黑名单复制hc 未找到hc id' => $staff_info_id]);
            return false;
        }
        $hcInfo = $oldHcModel->toArray();

        unset($hcInfo['hc_id']);
        $old_create_time               = show_time_zone($hcInfo['createtime'], 'Y-m-d');
        $time_long                     = strtotime($hcInfo['expirationdate']) - strtotime($old_create_time);
        $newHcModel                    = new HrHcModel();
        $hcInfo['demandnumber']        = 1;
        $hcInfo['surplusnumber']       = 1;
        $hcInfo['state_code']          = HrHcModel::STATE_RECRUITING;
        $hcInfo['approval_state_code'] = 6;
        $hcInfo['submitter_id']        = 10000;
        $hcInfo['create_src']          = HrHcModel::CREATE_SRC_FACE_BLACKLIST_COPY;
        $hcInfo['createtime']          = gmdate('Y-m-d H:i:s');
        $hcInfo['updated_at']          = gmdate('Y-m-d H:i:s');
        $hcInfo['expirationdate']      = date('Y-m-d 00:00:00', time() + $time_long);
        if ($newHcModel->create($hcInfo)) {
            return $newHcModel->hc_id;
        }
        throw new Exception('create hc fail staff_id '.$staff_info_id);

    }

}
