<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\ActivityEnums;
use FlashExpress\bi\App\Models\backyard\PosterPushSetModel;
use FlashExpress\bi\App\Models\backyard\PosterPushReadModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

/**
 * 内部海报推送服务
 * Author: AI
 * Date: 2025-01-27
 * Description: 处理内部海报推送相关业务逻辑
 */
class PosterPushServer extends BaseServer
{
    const ACTIVITY_NAME = 'poster_push';

    /**
     * 获取海报推送活动列表
     * @param array $params 查询参数，包含staff_id用于过滤已读状态
     * @return array
     */
    public function activityList(array $params = []): array
    {
        $staffId = isset($params['staff_info_id']) ? (int)$params['staff_info_id'] : 0;
        if (empty($staffId)) {
            return [];
        }
        $currentDate = date('Y-m-d');
        try {
            $result = $this->getActivityListFromDatabase($currentDate, $staffId);
            return empty($result) ? [] : $this->buildReturnData($result, $staffId, $currentDate, $params);
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log([
                'function'   => 'PosterPushServer activityList',
                'staff_id'   => $staffId,
                "error_msg"  => $e->getMessage(),
                "error_line" => $e->getLine(),
                "error_file" => $e->getFile(),
            ], "error");
            return [];
        }
    }
    
    /**
     * @param $data
     * @param $staffId
     * @param $currentDate
     * @param $param
     * @return array
     */
    public function buildReturnData($data, $staffId, $currentDate, $param): array
    {
        $lang            = strtolower(substr($this->lang, 0, 2));
        $returnData      = [];
        $activityServer  = new ActivityServer();
        $whiteListServer = new WhiteListServer();
        //判断白名单 白名单用户 直接弹窗 最后确定 用当前日期 获取白名单
        $is_white_list = $whiteListServer->isInfWhiteList($staffId, $currentDate);
        $is_activity   = $activityServer->isAttendanceStart($param);
        foreach ($data as $key => $value) {
            if ($lang == 'zh') {
                $image_url = $value['url_zh'] ?? '';
            } elseif ($lang == 'my') {
                $image_url = $value['url_my'] ?? '';
            } else {
                $image_url = $value['url_en'] ?? '';
            }
            if (empty($image_url)) {
                continue;
            }

            if ($is_white_list) {
                $state = ActivityEnums::STATE_IS_ACTIVITY;
            } else {
                //非白名单 要判断 打没打完上班卡 如果打了 弹窗
                if ($is_activity) {
                    $state = ActivityEnums::STATE_IS_ACTIVITY;
                } else {
                    $state = ActivityEnums::STATE_DELAY_ACTIVITY;
                }
            }
            $msg_id = '';
            if ($value['actualize_type'] == PosterPushSetModel::ACTUALIZE_TYPE_INTERNAL) {
                if (!empty($value['actualize_msg_id'])) {
                    $messageInfo = MessageCourierModel::findFirst(
                        [
                            'conditions' => 'message_content_id = :msg_id: and is_del = 0 and category in (0,33) and staff_info_id = :staff_info_id:',
                            'bind'       => ['msg_id' => $value['actualize_msg_id'], 'staff_info_id' => $staffId],
                        ]
                    );
                    $msg_id      = empty($messageInfo) ? '' : $messageInfo->id;
                }
            }

            $returnData[] = [
                'activity_name' => self::ACTIVITY_NAME,
                'type'          => intval($value['actualize_type'] ?? PosterPushSetModel::ACTUALIZE_TYPE_NO),
                // 跳转方式,1=内部跳转,2=外部跳转,3=不跳转
                'page_url'      => $value['actualize_type'] == PosterPushSetModel::ACTUALIZE_TYPE_EXTERNAL && !empty($value['loadingpage']) ? $value['loadingpage'] : '',
                // 跳转链接
                'state'         => $state,
                'image_url'     => $image_url,
                'msg_id'        => $msg_id,
                'data'          => null,
            ];
            if ($state == ActivityEnums::STATE_IS_ACTIVITY) {
                // 标记为已读
                PosterPushReadModel::markAsRead($value['id'], $staffId, $currentDate,
                    PosterPushReadModel::READ_STATE_READ);
            } else {
                // 标记为未读
                PosterPushReadModel::markAsRead($value['id'], $staffId, $currentDate,
                    PosterPushReadModel::READ_STATE_UNREAD);
            }
        }
        $this->getDI()->get("logger")->write_log([
            'function'    => 'PosterPushServer buildReturnData',
            'staff_id'    => $staffId,
            'return_data' => $returnData,
        ], "info");
        return $returnData;
    }


    /**
     * 获取海报推送活动列表（从数据库查询）
     * 推送机制说明：
     * 1. 推送时间：按照配置的推送时间开始至结束之间的日期，按照推送频率当日00:30进行推送
     *    首次推送为开始日期当天，之后推送按照频率+X天，仅推送1次的即为当天执行一次
     * 2. 发送对象：根据当前员工的合同公司、所属公司、部门、大区、片区、网点、职位、雇佣类型
     *    与海报配置的发送对象条件进行匹配（交集），所有配置的条件都必须匹配
     * 3. 工号：如果海报配置中指定了工号，则直接匹配（并集）
     * 4. 防重复弹出：基于推送日期的已读状态管理，同一海报在同一推送日期仅弹出1次，
     *    弹出后标记当日已读，不影响后续推送日期的显示（如每天推送、每两天推送等）
     * @param string $currentDate 当前日期
     * @param int $staffId 员工ID，用于过滤已读状态
     * @return array
     */
    private function getActivityListFromDatabase(string $currentDate, int $staffId): array
    {
        // 获取当前员工的组织信息
        $staffOrgInfo = $this->getStaffOrganizationInfo($staffId);
        if (empty($staffOrgInfo)) {
            return []; // 如果获取不到员工信息，返回空数组
        }

        // 构建查询条件
        $conditions = [
            'state = :state:',
            'push_start_date <= :current_date:',
            'push_end_date >= :current_date:',
        ];

        $bind = [
            'state'        => PosterPushSetModel::STATE_ENABLED,
            'current_date' => $currentDate,
        ];

        // 查询当前有效的海报推送配置
        $posterList = PosterPushSetModel::find([
            'conditions' => implode(' AND ', $conditions),
            'bind'       => $bind,
            'order'      => 'created_at DESC',
        ]);

        $result = [];
        foreach ($posterList as $poster) {
            // 检查是否需要推送（根据推送频次计算）
            if ($this->shouldPushToday($poster, $staffId)) {
                // 检查当前员工是否符合推送条件（发送对象取交集，工号取并集）
                if ($this->isStaffMatchPosterConditions($poster, $staffId, $staffOrgInfo)) {
                    $result[] = [
                        'id'               => $poster->id,
                        'url_en'           => $poster->url_en,
                        'url_zh'           => $poster->url_zh,
                        'url_my'           => $poster->url_my,
                        'actualize_type'   => $poster->actualize_type,
                        'actualize_msg_id' => $poster->actualize_msg_id,
                        'loadingpage'      => $poster->loadingpage,
                    ];
                }
            }
        }

        // 过滤已读的海报推送（防重复弹出）
        if (!empty($result)) {
            $posterIds     = array_column($result, 'id');
            $readPosterIds = PosterPushReadModel::getReadPosterIds($posterIds, $staffId, $currentDate);
            // 过滤掉已读的海报推送
            $result = array_filter($result, function ($poster) use ($readPosterIds) {
                return !in_array($poster['id'], $readPosterIds);
            });

            // 重新索引数组
            $result = array_values($result);
        }

        return $result;
    }

    /**
     * 判断今天是否需要推送
     * @param PosterPushSetModel $poster
     * @return bool
     */
    private function shouldPushToday($poster, $staffId)
    {
        $startDate   = strtotime($poster->push_start_date);
        $endDate     = strtotime($poster->push_end_date);
        $currentDate = strtotime(date('Y-m-d'));

        // 检查是否在推送时间范围内
        if ($currentDate < $startDate || $currentDate > $endDate) {
            return false;
        }

        // 计算从开始日期到当前日期的天数差
        $daysDiff = ($currentDate - $startDate) / (24 * 3600);

        // 仅推送1次：只在开始日期当天推送
        if ($poster->push_frequency_type == PosterPushSetModel::PUSH_FREQUENCY_ONCE) {
            return $daysDiff == 0;
        }

        // 根据推送频次类型计算间隔天数
        $intervalDays = $this->getIntervalDaysByFrequencyType($poster->push_frequency_type);

        // 检查是否到了推送日期：首次推送为开始日期当天，之后按频率间隔推送
        $is_push = $daysDiff >= 0 && $daysDiff % $intervalDays == 0;
        $this->getDI()->get("logger")->write_log([
            'function'  => 'shouldPushToday',
            'staff_id'  => $staffId,
            'poster_id' => $poster->id ?? 0,
            'is_push'   => $is_push,
        ], "info");
        return $is_push;
    }

    /**
     * 获取员工组织信息
     * @param int $staffId 员工ID
     * @return array 员工组织信息
     */
    private function getStaffOrganizationInfo($staffId)
    {
        if (empty($staffId)) {
            return [];
        }

        // 查询员工基本信息和组织信息
        $builder   = $this->modelsManager->createBuilder();
        $staffInfo = $builder->columns('
                 h.staff_info_id,
                 h.name,
                 h.sys_store_id,
                 h.contract_company_id,
                 h.node_department_id,
                 h.job_title,
                 h.hire_type,
                 s.manage_region,
                 s.manage_piece
             ')
            ->from(['h' => HrStaffInfoModel::class])
            ->leftJoin(SysStoreModel::class, 'h.sys_store_id = s.id', 's')
            ->where('h.staff_info_id = :staff_id:', ['staff_id' => $staffId])
            ->getQuery()->execute()->getFirst();

        return $staffInfo ? $staffInfo->toArray() : [];
    }

    /**
     * 检查员工是否匹配海报推送条件
     * @param PosterPushSetModel $poster 海报配置
     * @param int $staffId 员工ID
     * @param array $staffOrgInfo 员工组织信息
     * @return bool
     */
    private function isStaffMatchPosterConditions(PosterPushSetModel $poster, int $staffId, array $staffOrgInfo): bool
    {
        // 1. 检查工号（并集）- 如果指定了工号，直接匹配
        if (!empty($poster->staff_ids)) {
            $staffIds = explode(',', $poster->staff_ids);
            if (in_array($staffId, $staffIds)) {
                $this->getDI()->get("logger")->write_log([
                    'function' => 'isStaffMatchPosterConditions',
                    'staff_id' => $staffId,
                    'return'   => true,
                ], "info");
                return true; // 工号匹配，直接返回true
            }
        }

        // 2. 检查发送对象条件（交集）- 所有条件都必须匹配
        $conditions = [];
        // 合同公司条件
        if (!empty($poster->company_ids)) {
            $companyIds            = explode(',', $poster->company_ids);
            $conditions['company'] = in_array($staffOrgInfo['contract_company_id'], $companyIds);
        }

        // 部门条件
        if (!empty($poster->department_ids)) {
            $departmentIds            = explode(',', $poster->department_ids);
            $conditions['department'] = in_array($staffOrgInfo['node_department_id'], $departmentIds);
        }

        // 大区条件
        if (!empty($poster->manage_region_ids)) {
            $regionIds            = explode(',', $poster->manage_region_ids);
            $conditions['region'] = in_array($staffOrgInfo['manage_region'], $regionIds);
        }

        // 片区条件
        if (!empty($poster->manage_piece_ids)) {
            $pieceIds            = explode(',', $poster->manage_piece_ids);
            $conditions['piece'] = in_array($staffOrgInfo['manage_piece'], $pieceIds);
        }

        // 网点条件
        if (!empty($poster->store_ids)) {
            $storeIds            = explode(',', $poster->store_ids);
            $conditions['store'] = in_array($staffOrgInfo['sys_store_id'], $storeIds);
        }

        // 职位条件
        if (!empty($poster->job_title_ids)) {
            $jobTitleIds             = explode(',', $poster->job_title_ids);
            $conditions['job_title'] = in_array($staffOrgInfo['job_title'], $jobTitleIds);
        }

        // 雇佣类型条件
        if (!empty($poster->hire_types)) {
            $hireTypes               = explode(',', $poster->hire_types);
            $conditions['hire_type'] = in_array($staffOrgInfo['hire_type'], $hireTypes);
        }

        // 如果没有设置任何发送对象条件，且也没有指定工号，属于错误数据 则不推送
        if (empty($conditions) && empty($poster->staff_ids)) {
            return false;
        } elseif (empty($conditions)) {
            return false;
        }
        $this->getDI()->get("logger")->write_log([
            'function'   => 'isStaffMatchPosterConditions',
            'staff_id'   => $staffId,
            'conditions' => $conditions,
        ], "info");
        // 所有设置的条件都必须匹配（交集）
        foreach ($conditions as $condition) {
            if (!$condition) {
                return false;
            }
        }
        $this->getDI()->get("logger")->write_log([
            'function'   => 'isStaffMatchPosterConditions',
            'staff_id'   => $staffId,
            'return'     => true,
            'conditions' => $conditions,
        ], "info");
        return true;
    }

    /**
     * 根据推送频次类型获取间隔天数
     * @param int $frequencyType
     * @return int
     */
    private function getIntervalDaysByFrequencyType($frequencyType)
    {
        switch ($frequencyType) {
            case PosterPushSetModel::PUSH_FREQUENCY_ONCE:
                return 0; // 仅1次，不需要间隔
            case PosterPushSetModel::PUSH_FREQUENCY_DAILY:
                return 1; // 每1天1次
            case PosterPushSetModel::PUSH_FREQUENCY_2DAYS:
                return 2; // 每2天1次
            case PosterPushSetModel::PUSH_FREQUENCY_3DAYS:
                return 3; // 每3天1次
            case PosterPushSetModel::PUSH_FREQUENCY_4DAYS:
                return 4; // 每4天1次
            case PosterPushSetModel::PUSH_FREQUENCY_5DAYS:
                return 5; // 每5天1次
            case PosterPushSetModel::PUSH_FREQUENCY_6DAYS:
                return 6; // 每6天1次
            case PosterPushSetModel::PUSH_FREQUENCY_7DAYS:
                return 7; // 每7天1次
            default:
                return 1; // 默认每天1次
        }
    }
}