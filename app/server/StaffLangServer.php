<?php
/**
 * Author: Bruce
 * Date  : 2022-02-26 16:11
 * Description:
 */

namespace FlashExpress\bi\App\Server;


class StaffLangServer extends BaseServer
{
    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取员工用户移动设备最新的语言环境
     * @param $staff_id
     * @param $key-翻译key
     * @param array $params 内容中用来替换变量的参数
     * @return mixed|string
     */
    public function getMsgTemplateByStaffId($staff_id, $key, $params = [])
    {
        try {
            $lang = (new StaffServer())->getLanguage($staff_id);
            $lang = substr($lang, 0, 2);
            if ($lang == 'zh') {
                $lang = 'zh-CN';
            }

            $this->getDI()->get('logger')->write_log('staff_id===' . $staff_id . "===lang=" . $lang, 'info');

            return $this->getTranslationContent($key, $lang, $params);

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('getMsgTemplateByStaffId===' . $e->getMessage());
            return '';
        }
    }

    public function getTranslationContent($key, $lang, $params)
    {
        $content = $this->getTranslation($lang)->_($key);

        foreach ($params as $key_name => $value) {
            $content = str_replace('{' . $key_name . '}', $value, $content);
        }

        return $content;
    }
}