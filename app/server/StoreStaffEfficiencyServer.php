<?php

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\StoreStaffEfficiencyModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Modules\Ph\library\Enums\HrJobTitleEnums;

/**
 *网点人效计算相关(仅PH)
 */
class StoreStaffEfficiencyServer extends BaseServer
{
    public function solidify_data($statDate, $storeIds)
    {
        $conditions    = 'category in ({cg:array}) and  state=:state:';
        $bind['state'] = SysStoreModel::STATE_1;
        $bind['cg']    = [
            enums::$stores_category['sp'],
            enums::$stores_category['pdc'],
            enums::$stores_category['bdc'],
        ];//1 14 10
        if ($storeIds) {
            $conditions  .= ' and id in ({ids:array})';
            $bind['ids'] = $storeIds;
        }
        $list = SysStoreModel::find([
            'columns'    => 'id,category,manage_piece',
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
        if (empty($list)) {
            return;
        }
        $overTimeService = (new \FlashExpress\bi\App\Modules\Ph\Server\OvertimeExtendServer($this->lang,
            $this->timeZone));
        $storeIds        = array_column($list, 'id');
        $storeMap        = array_column($list, null, 'id');
        $storeIdsChunk   = array_chunk($storeIds, 100);
        $getFailStoreIds = [];
        $model           = new StoreStaffEfficiencyModel();
        foreach ($storeIdsChunk as $storeIdChunkItem) {
            //出勤人数
            $statData   = $overTimeService->attendance_num_statistic($statDate, HrJobTitleEnums::$limit_ot_job_title,
                $storeIdChunkItem);
            $insertData = [];
            echo ' handling count:' . count($storeIdChunkItem) . PHP_EOL;
            foreach ($storeIdChunkItem as $storeId) {
                $storeAttenNum = $statData[$storeId] ?? 0;
                if ($storeMap[$storeId]['category'] == enums::$stores_category['pdc']) {
                    $storeTotalOperation = $this->getPuckUpAndDeliveryNumber($storeId, $statDate);
                    if (!isset($storeTotalOperation['pickup_operation_count'])) {
                        $getFailStoreIds[] = $storeId;
                        continue;
                    }
                    $parcel_num = $storeTotalOperation['pickup_operation_count'] ?: 0;
                } else {
                    $storeTotalOperation = $this->getSpBdcRateDate($storeId, $storeMap[$storeId]['category'],
                        $storeMap[$storeId]['manage_piece'], $statDate);
                    if (!isset($storeTotalOperation['store_pickup_count'], $storeTotalOperation['store_delivery_count'])) {
                        $getFailStoreIds[] = $storeId;
                        continue;
                    }
                    $parcel_num = $storeTotalOperation['store_pickup_count'] + $storeTotalOperation['store_delivery_count'];
                }
                $storeEffect  = $overTimeService->cal_store_today_effect($parcel_num, $storeAttenNum);
                $insertData[] = [
                    'store_id'               => $storeId,
                    'stat_date'              => $statDate,
                    'store_pickup_count'     => $storeTotalOperation['store_pickup_count'] ?? null,
                    'store_delivery_count'   => $storeTotalOperation['store_delivery_count'] ?? null,
                    'pickup_operation_count' => isset($storeTotalOperation['pickup_operation_count']) ? ($storeTotalOperation['pickup_operation_count'] ?: 0) : null,
                    'atten_num'              => $storeAttenNum,
                    'store_effect'           => $storeEffect,
                ];
            }
            $storeIdsStr = getIdsStr($storeIdChunkItem);
            $this->db->execute("delete from store_staff_efficiency where  stat_date='$statDate' and store_id IN ($storeIdsStr)");
            $this->logger->info(json_encode(array_column($insertData, 'store_id')));
            if ($insertData) {
                $model->batch_insert($insertData, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
            }
        }
        if ($getFailStoreIds) {
            $this->logger->error(json_encode($getFailStoreIds));
        }
    }

    /**
     * 网点揽件总操作量
     * @param $storeId
     * @param $date
     * @return array|void
     */
    public function getPuckUpAndDeliveryNumber($storeId, $date)
    {
        $params['store_id']   = $storeId;
        $params['begin_date'] = $date;
        $params['end_date']   = $date;
        $ac                   = new ApiClient('ard_api', '', 'deliverycount.get_latest_delivery_by_store_date',
            $this->lang);
        $ac->setParams($params);
        $ac_result = $ac->execute();
        $this->logger->write_log(json_encode(['p' => $params, 'r' => $ac_result], JSON_UNESCAPED_UNICODE), 'info');
        if (empty($ac_result) || !isset($ac_result['result'])) {
            return [];
        }

        return empty($ac_result['result']['data'][0]) ? [] : $ac_result['result']['data'][0];
    }

    public function getSpBdcRateDate($storeId, $category, $managePiece, $date)
    {
        $params['store_id']       = $storeId;
        $params['store_category'] = $category;
        $params['piece_id']       = $managePiece;
        $params['date']           = $date;

        $ac = new ApiClient('bi_rpcv2', '', 'dc.get_sp_pdc_rate_date', $this->lang);
        $ac->setParams($params);
        $ac_result = $ac->execute();
        $this->logger->write_log(json_encode(['p' => $params, 'r' => $ac_result], JSON_UNESCAPED_UNICODE), 'info');
        if (empty($ac_result) || !isset($ac_result['result'])) {
            return [];
        }

        return empty($ac_result['result']['data'][0]['store_data']) ? [] : $ac_result['result']['data'][0]['store_data'];
    }
}