<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/29/23
 * Time: 8:31 PM
 */

namespace FlashExpress\bi\App\Server;

use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\QrCode;
use FlashExpress\bi\App\Enums\ActivityEnums;
use FlashExpress\bi\App\Models\backyard\ActivityRecordModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;

class BirthdayServer extends ActivityServer
{
    protected $activityCode = ActivityRecordModel::CODE_BIRTHDAY;
    public function initData($param)
    {
        parent::initData($param);
        $info = HrStaffItemsModel::findFirst([
            'columns'    => 'staff_info_id,item,value',
            'conditions' => 'staff_info_id = :staff_info_id: and item = :code:',
            'bind'       => [
                'staff_info_id' => $param['staff_info_id'],
                'code'          => 'BIRTHDAY',
            ],
        ]);

        //真实生日
        $realBirthday = $info->value ?? '';
        $year         = date('Y');
        
        if (!empty($realBirthday) && preg_match("/\d{4}\-\d{2}\-\d{2}/", $realBirthday)) {
            //当年生日
            $this->activityDate = $year.date('-m-d', strtotime($realBirthday));
        }
    }


    //获取图片参数
    public function getImgParam($staff){
        $file_path = APP_PATH.'/views/activity/birthday.ftl';
        //模板里面的参数
        $p['file_name']      = $this->activityCode;
        $p['data']['bg_img'] = $this->setExpire(600)->getBgImgFromCache('birthday_tpl_url');

        //翻译文案变量
        $t_param['name']                      = $staff['name'];
        $p['data']['birthday_img_content_p1'] = $this->getTranslation('en')->_('birthday_img_content_p1', $t_param);
        $p['data']['birthday_img_content_p2'] = $this->getTranslation('en')->_('birthday_img_content_p2', $t_param);

        //图片尺寸
        $p['viewport']['width'] = ActivityEnums::IMG_WIDTH;
        $p['viewport']['height'] = ActivityEnums::IMG_HEIGHT;

        //新增 二维码图片
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('Recruit_Birthday')
            ->setParameters(['staff_info_id' => $staff['staff_info_id']])
            ->getConfig();
        $qrCodeUrl = $res['response_data'] ?? '';
        $qrCode = new QrCode($qrCodeUrl);
        $qrCode->setErrorCorrectionLevel(ErrorCorrectionLevel::HIGH());
        $p['data']['qr_code'] = $qrCode->writeDataUri();
        $p['data']['activity_click_here'] = $this->getTranslation('en')->_('activity_click_here');

        //获取 html 模板 oss
        if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
            $p['tpl_url'] = $this->getTplPath($file_path);
        } else {
            $p['tpl_url'] = $this->getTplPathFromCache($file_path);
        }
        return $p;
    }

}