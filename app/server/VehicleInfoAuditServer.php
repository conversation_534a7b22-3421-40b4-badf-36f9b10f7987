<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Models\backyard\VanContainerModel;
use FlashExpress\bi\App\Modules\Th\Server\VehicleServer;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\VehicleInfoAuditModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\StaffRepository;


class VehicleInfoAuditServer extends AuditBaseServer
{

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
    }



    /**
     * 创建车厢申请
     * @param $staff_info_id
     * @param $vehicle_audit_id
     * @return bool|null
     */
    public function createContainerAudit($staff_info_id,$vehicle_audit_id)
    {
        $staffInfo = (new StaffServer())->getStaffInfo(['staff_info_id' => $staff_info_id]);

        $isShow = (new VanContainerServer($this->lang, $this->timeZone))->isShowVanContainer($staffInfo);

        $this->logger->write_log([
            'staffInfo'        => $staffInfo,
            'isShow'           => $isShow,
            'vehicle_audit_id' => $vehicle_audit_id,
        ], 'info');

        if (empty($isShow)) {
            return false;
        }

        $audit            = VehicleInfoAuditModel::findFirst([
            'conditions' => 'id = :id: ',
            'bind'       => ['id' => $vehicle_audit_id],
        ]);
        if (empty($audit)) {
            return true;
        }
        if (empty($audit->container_type)) {
            return true;
        }
        $vehicle_info = VehicleInfoModel::findFirst([
            'conditions' => 'uid = :uid: ',
            'bind'       => ['uid' => $staff_info_id],
        ]);
        if(empty($vehicle_info->vehicle_check_video)){
            $this->logger->write_log(['createContainerAudit' => '没有视频，不创建车厢申请'], 'info');
            return true;
        }
        //写表
        $insert['staff_info_id']    = $audit->staff_info_id;
        $insert['operator']         = $audit->staff_info_id;
        $insert['type']             = $audit->container_type;
        $insert['created_at']       = $audit->created_at;
        $insert['date_at']          = show_time_zone($audit->created_at, 'Y-m-d');
        $insert['source_type']      = VanContainerModel::SOURCE_TYPE_CHANGE_CAR;
        $insert['video_url']        = $vehicle_info->vehicle_check_video;
        $insert['plate_number']     = $vehicle_info->plate_number;
        $insert['license_location'] = $vehicle_info->license_location;
        $model                      = new VanContainerModel();
        return $model->create($insert);
    }



    /**
     * 修改车辆信息审批
     * @param $staff_info_id
     * @param $before
     * @param $isQuickAudit
     * @throws ValidationException
     */
    public function add($staff_info_id, $before,$isQuickAudit)
    {
        $old = VehicleInfoAuditModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and status = :status:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'status' => enums::APPROVAL_STATUS_PENDING],
        ]);
        if (!empty($old)) {
            $approvalServer = new ApprovalServer($this->lang, $this->timeZone);
            $approvalServer->cancel($old->id, AuditListEnums::APPROVAL_TYPE_VEHICLE_EDIT, 'auto cancel',
                $staff_info_id);
        }

        $model                            = new VehicleInfoAuditModel();
        $model->staff_info_id             = $staff_info_id;
        $model->status                    = $isQuickAudit ? enums::APPROVAL_STATUS_APPROVAL : enums::APPROVAL_STATUS_PENDING;
        $model->before_vehicle_brand      = $before['vehicle_brand'];
        $model->before_vehicle_brand_text = $before['vehicle_brand_text'];
        $model->before_plate_number       = $before['plate_number'];
        $model->before_license_location   = $before['license_location'];
        $model->container_type            = $before['container_type'];
        $model->serial_no                 = 'CC' . $this->getID();
        if (!$model->save()) {
            throw new \Exception($this->getTranslation()->_('4008'));
        }
        if ($isQuickAudit) {
            return $model->id;
        }

        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);
        $requestId      = $approvalServer->create($model->id, AuditListEnums::APPROVAL_TYPE_VEHICLE_EDIT,
            $staff_info_id);
        if (!$requestId) {
            throw new Exception('创建审批流失败');
        }
        return $model->id;
    }

    /**
     * 获取审批详情详细信息
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     * @throws Exception
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result                              = $this->getVehicleAuditInfo($auditId);
        $staffInfo                           = (new StaffRepository($this->lang))->getStaffPosition($result['staff_info_id']);
        $auditListRepository                 = new AuditlistRepository($this->lang, $this->timeZone);
        $detailLists                         = [
            'apply_parson'     => sprintf('%s ( %s )', $staffInfo['name'] ?? '', $staffInfo['id'] ?? ''),
            'apply_department' => sprintf('%s - %s', $staffInfo['depart_name'] ?? '',
                $staffInfo['job_name'] ?? ''),
            'application_date'=> show_time_zone($result['created_at'],'Y-m-d'),
        ];
        $server                              = new VehicleServer($this->lang);
        $brandMap                            = $server->getVehicleBrandMap();
        $returnData['data']['detail']        = $this->format($detailLists);
        $returnData['data']['before_detail'] = $this->formatBefore($result, $brandMap);
        $returnData['data']['after_detail']  = $this->formatAfter($result, $brandMap);

        $data                          = [
            'title'         => $auditListRepository->getAudityType(AuditListEnums::APPROVAL_TYPE_VEHICLE_EDIT),
            'id'            => $result->id,
            'staff_id'      => $result['staff_info_id'],
            'type'          => (string)AuditListEnums::APPROVAL_TYPE_VEHICLE_EDIT,
            'created_at'    => show_time_zone($result['created_at']),
            'updated_at'    => show_time_zone($result['updated_at']),
            'approvalLevel' => 0,
            'status_text'   => $auditListRepository->getAuditState($result['status']),
            'serial_no'     => $result['serial_no'] ?? '',
        ];
        $returnData['data']['head']    = $data;
        $returnData['data']['extend']  = [];
        $returnData['data']['confirm'] = [];
        return $returnData;
    }

    protected function formatBefore($model, $brandMap): array
    {

        $before['vehicle_brand']    = ( $brandMap[$model->before_vehicle_brand] ?? '' ). $model->before_vehicle_brand_text;
        $before['plate_number']     = $model->before_plate_number;
        $before['license_location'] = $model->before_license_location;
        return $before;
    }

    protected function formatAfter($model, $brandMap): array
    {
        $vehicleInfo = VehicleInfoModel::findFirst([
            'conditions' => 'uid = :uid: ',
            'bind'       => ['uid' => $model->staff_info_id],
        ]);

        $after['vehicle_brand']       = ($brandMap[$vehicleInfo['vehicle_brand']] ?? '' ). $vehicleInfo->vehicle_brand_text;
        $after['plate_number']        = $vehicleInfo->plate_number;
        $after['license_location']    = $vehicleInfo->license_location;
        $after['vehicle_check_img_1'] = $vehicleInfo->vehicle_check_img_1;
        $after['vehicle_check_img_2'] = $vehicleInfo->vehicle_check_img_2;
        $after['vehicle_check_video'] = $vehicleInfo->vehicle_check_video;
        return $after;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false, false, false, false, false, false);
    }

    public function genSummary(int $auditId, $user)
    {
        $model = $this->getVehicleAuditInfo($auditId);

        if (!empty($model)) {
            $staffInfoInit = (new StaffRepository())->getStaffPosition($model->staff_info_id);
            $param         = [
                [
                    'key'   => "staff_job_title",
                    'value' => $staffInfoInit['job_name'],
                ],
                [
                    'key'   => "hr_probation_field_sys_store_name",
                    'value' => $staffInfoInit['store_name'],
                ],
            ];
        }
        return $param ?? [];
    }

    /**
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return true
     * @throws BusinessException
     * @throws Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $model         = VehicleInfoAuditModel::findFirstById($auditId);
            $model->status = $state;
            // 原因
            if ($state == enums::$audit_status['dismissed']) {
                $model->reject_reason = $extend['remark'];
                $approval_status      = VehicleInfoEnums::APPROVAL_REJECT_CODE;
            } elseif ($state == enums::$audit_status['revoked']) {
                $model->cancel_reason = $extend['remark'];
                $approval_status      = VehicleInfoEnums::APPROVAL_UN_SUBMITTED_CODE;
            } elseif ($state == enums::$audit_status['approved']) {
                $approval_status = VehicleInfoEnums::APPROVAL_WAIT_NW_CODE;
            }

            if (!empty($approval_status)) {
                $vehicleInfo                  = VehicleInfoModel::findFirst([
                    'conditions' => 'uid = :uid: ',
                    'bind'       => ['uid' => $model->staff_info_id],
                ]);
                $vehicleInfo->approval_status = $approval_status;
                $vehicleInfo->approval_remark = $extend['remark'] ?? '';
                $vehicleInfo->save();
            }

            //驳回发个消息
            if (isset($approval_status) && $approval_status == VehicleInfoEnums::APPROVAL_REJECT_CODE) {
                $messageServer = new MessageServer($this->lang, $this->timeZone);
                $staffLang     = (new StaffServer())->getLanguage($model->staff_info_id);
                $t             = $this->getTranslation($staffLang);
                $title         = $t->_('vehicle_audit_rejected_title');
                $content       = $t->_('vehicle_audit_rejected_content', ['reason' => $extend['remark'] ?? '']);
                $messageServer->sendMessage($model->staff_info_id, $title, $content);
            }


            $model->save();
        }
        return true;
    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        return  [];
    }

    /**
     * 更新[包含操作：同意|驳回]
     * @param array $paramIn 传入参数
     * @return array
     * @throws Exception
     */
    public function update($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id');
        $auditId = $this->processingDefault($paramIn, 'audit_id');
        $status  = $this->processingDefault($paramIn, 'status');
        $reason  = $this->processingDefault($paramIn, 'reject_reason');
        $reason  = addcslashes(stripslashes($reason), "'");  //单引号过滤

        $model = $this->getVehicleAuditInfo($auditId);

        //[2]验证数据
        if (empty($model)) {
            throw new BusinessException($this->getTranslation()->_('1015'));
        }

        //已经是最终状态,不能修改
        if ($model->status != enums::$audit_status['panding']) {
            throw new BusinessException($this->getTranslation()->_('1016'));
        }

        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);
        if ($status == enums::$audit_status['dismissed']) {
            $approvalServer->reject($auditId, AuditListEnums::APPROVAL_TYPE_VEHICLE_EDIT, $reason,
                $staffId);
        } elseif ($status == enums::$audit_status['approved']) {
            $approvalServer->approval($auditId, AuditListEnums::APPROVAL_TYPE_VEHICLE_EDIT, $staffId);
        }
        return $this->checkReturn([]);
    }

    /**
     * 取消
     * @param array $paramIn 传入参数
     * @return array
     * @throws BusinessException|ValidationException
     * @throws Exception
     */
    public function cancel($paramIn)
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id');
        $auditId = $this->processingDefault($paramIn, 'audit_id');
        $reason  = $this->processingDefault($paramIn, 'cancel_reason');
        $reason  = addcslashes(stripslashes($reason), "'");

        //[2]验证数据
        $model = $this->getVehicleAuditInfo($auditId);
        if (empty($model)) {
            throw new \Exception($this->getTranslation()->_('data_error'));
        }

        //验证审批状态
        //只有待审批的可以撤销
        if ($model->status != enums::$audit_status['panding']) {
            throw new BusinessException($this->getTranslation()->_('4018'));
        }

        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);
        $approvalServer->cancel($auditId, AuditListEnums::APPROVAL_TYPE_VEHICLE_EDIT, $reason, $staffId);
        return $this->checkReturn([]);
    }

    /**
     * 获取解约详情
     * @throws Exception
     */
    public function getVehicleAuditInfo($auditId)
    {
        $model = VehicleInfoAuditModel::findFirstById($auditId);
        if (empty($model)) {
            throw new Exception($this->getTranslation()->_('data_error'));
        }
        return $model;
    }

}
