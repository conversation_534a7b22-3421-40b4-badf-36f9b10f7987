<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrInternalBonusDetailsModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\InternalPositionHcModel;
use FlashExpress\bi\App\Models\backyard\InternalPositionSetModel;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Repository\ResumeRecommendRepository;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\QrCode;
use Exception;


class CourierResumeRecommendServer extends BaseServer
{
    public        $timezone;
    public        $country_server;
    public static $paramIn;
    public        $lang;


    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->lang     = $lang;
        $this->timezone = $timezone;
    }

    /**
     * 快递员推荐 - 简历提交
     * 逻辑来自ResumeRecommendServer()->resumeRecommendSubmit()
     * @param $paramIn
     * @param $userinfo
     * @return array
     */
    public function resumeRecommendSubmit($paramIn, $userinfo): array
    {
        $paramIn['recommend_source'] = HrResumeModel::RECOMMEND_SOURCE_3;
        $paramIn['reserve_type']     = HrResumeModel::RESERVE_TYPE_FORMAL;
        $paramIn['recruit_channel'] = 3;
        $validations                 = [
            "first_name"        => "Required|StrLenGeLe:0,50|>>>:" . $this->t->_('4038'),
            "estimate_store_id" => "Required|StrLenGeLe:0,50|>>>:" . $this->t->_('4028'),
            "hc_id"             => "Required|StrLenGeLe:0,50|>>>:" . $this->t->_('4028'),
            "phone"             => "Required|StrLenGeLe:10,11|>>>:" . $this->t->_('4117'),
            "expect_job"        => 'Required|Int',
        ];

        $this->validateCheck($paramIn, $validations, -3);

        //[1]发送RPC请求
        $paramIn['user_info'] = $userinfo;
        $rpcClient            = new ApiClient('winhr_rpc', '', 'resume_recommend_submit', $this->lang);
        $rpcClient->setParams($paramIn);

        $return = $rpcClient->execute();
        $this->getDI()->get('logger')->write_log("winhr_rpc CourierResumeRecommendServer resumeRecommendSubmit:" . json_encode($return,
                JSON_UNESCAPED_UNICODE), 'info');
        if (!empty($return['result']) && $return['result']['code']) {
            return self::checkReturn($return['result']);
        }
        $this->getDI()->get('logger')->write_log("winhr_rpc CourierResumeRecommendServer paramIn:" . json_encode($paramIn,
                JSON_UNESCAPED_UNICODE) . " return:" . json_encode($return, JSON_UNESCAPED_UNICODE), 'info');
        return self::checkReturn(-3, $return['result']['msg'] ?? 'error');
    }

    public function getStoreList($params, $userInfo): array
    {
        $params['recommend_source'] = HrResumeModel::RECOMMEND_SOURCE_3;
        $params['store_id']         = $userInfo['organization_type'] == 1 ? $userInfo['organization_id'] : '';
        if (empty($params['store_tab']) || $params['store_tab'] == 1) {
            //当前登录主管所在hc job
            $data = (new ResumeRecommendRepository($this->lang))->getHcByStore($params);
        } else {
            //其他所有网点hc
            $data = (new ResumeRecommendRepository($this->lang))->getHcListV1($params);
        }
        return $data;
    }

    /**
     * 岗位列表
     * @param array $state
     * @return array
     */
    public function getJobList(array $state = [1]): array
    {
        return HrJdModel::find([
            'conditions' => 'state in ({state:array})',
            'bind'       => [
                'state' => $state,
            ],
            'order'      => 'sort desc',
            'columns'    => 'job_id,job_name',
        ])->toArray();
    }

    public function internalJdList($paramIn)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('position_jd.job_id,position_jd.sort,position_jd.is_hot,position_jd.priority,jd.job_name');
        $builder->orderby('position_jd.sort asc, position_jd.is_hot asc, position_jd.job_id asc');
        $builder->from(['position_jd' => InternalPositionSetModel::class]);
        $builder->leftjoin(HrJdModel::class, 'position_jd.job_id = jd.job_id', 'jd');
        $builder->leftjoin(InternalPositionHcModel::class, 'position_jd.job_id = i_hc.job_id', 'i_hc');
        $builder->leftjoin(HrHcModel::class, 'hc.hc_id = i_hc.hc_id', 'hc');
        $builder->where("jd.state = 1 and hc.state_code = 2");
        $builder->groupby('position_jd.job_id');
        return $builder->getQuery()->execute();
    }

    /**
     * @param $paramIn
     * @return string[]
     * @throws ValidationException
     */
    public function internalRecruitGenerateImg($paramIn): array
    {
        if (
            empty($paramIn['job_name']) ||
            empty($paramIn['url']) ||
            empty($paramIn['address_text']) ||
            empty($paramIn['jump_url'])
        ) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        //对应国家要指定字体 改成这个字体了 
        $fontFamily = [
            'en' => 'Prompt Medium',
            'my' => 'Prompt Medium',
            'zh' => 'Noto Sans SC SemiBold',
        ];
        try {
            $p                             = $this->getImgParam($paramIn);
            $lang                          = substr(strtolower($this->lang), 0, 2);
            $p['data']['font_family']      = $fontFamily[$lang] ?? '';
            $p['imageOptions']['fullPage'] = true;//截全部
            $activityImg                   = (new FormImgServer())->htmlToImg($p);
            $activityImgUrl                = empty($activityImg) ? '' : $activityImg['object_url'];
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log([
                'function' => 'internalRecruitGenerateImg',
                'param'    => $paramIn,
                'error'    => $e->getMessage(),
            ], 'info');
            throw new ValidationException($this->getTranslation()->_('22464_internal_recruit_err_1'));
        }
        $this->getDI()->get('logger')->write_log("internalRecruitGenerateImg paramIn:" . json_encode($paramIn,
                JSON_UNESCAPED_UNICODE) . " return:" . json_encode(["url" => $activityImgUrl], JSON_UNESCAPED_UNICODE),
            'info');
        return ["url" => $activityImgUrl];
    }

    public function getImgParam($paramIn): array
    {
        $file_path = APP_PATH . '/views/internal/internal_recruit_poster.ftl';
        //模板里面的参数
        $p['file_name'] = 'courier_resume_recommend' . date('YmdHis');

        $is_show_qr_code         = '';
        $p['data']['font_color'] = '#fff';
//        $p['data']['bg_img'] = 'https://fex-my-asset-dev.oss-ap-southeast-3.aliyuncs.com/workOrder/1757906501-a3780abb5300473eb1e8138255cdcca8.png';
        $p['data']['bg_img'] = $paramIn['url'];
        //翻译文案变量

        $p['data']['hb_title']               = $this->getTranslation()->_('22464_internal_recruit_img_hb_title');
        $p['data']['hb_bottom']              = $this->getTranslation()->_('22464_internal_recruit_img_hb_bottom');
        $p['data']['job_name']               = $paramIn['job_name'] ?? '';
        $p['data']['address_text']           = $paramIn['address_text'] ?? '';
        $p['data']['recommender_staff_name'] = $paramIn['recommender_staff_name'] ?? '';
        $p['data']['is_hot']                 = !empty($paramIn['is_hot']) && $paramIn['is_hot'] == 1 ? '1' : '';
        //图片尺寸
        $p['viewport']['width']  = 800;
        $p['viewport']['height'] = 1418;
        //新增 二维码图片
        $qrCodeUrl = $paramIn['jump_url'] ?? '';
        $qrCode    = new QrCode($qrCodeUrl);
        $qrCode->setErrorCorrectionLevel(ErrorCorrectionLevel::HIGH());
        $p['data']['qr_code'] = $qrCode->writeDataUri();
        if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
            $p['tpl_url'] = $this->getTplPath($file_path);
        } else {
            $p['tpl_url'] = $this->getTplPathFromCache($file_path);
        }
        $this->getDI()->get('logger')->write_log("getImgParam1 paramIn:" . json_encode($paramIn,
                JSON_UNESCAPED_UNICODE) . " return:" . json_encode($p, JSON_UNESCAPED_UNICODE), 'info');
        return $p;
    }

    /**
     * @param $jump_url
     * @return array
     * @throws ValidationException
     */
    public function generateQrCode($params): array
    {
        if (empty($params['jump_url'])) {
            throw new ValidationException($this->getTranslation()->_('param_error'));
        }
        $qrCode = new QrCode($params['jump_url']);
        $qrCode->setErrorCorrectionLevel(ErrorCorrectionLevel::HIGH());
        $qr_code = $qrCode->writeDataUri();
        $this->getDI()->get('logger')->write_log("generateQrCode paramIn:" . json_encode($params,
                JSON_UNESCAPED_UNICODE) . " return:" . json_encode(["qr_code" => $qr_code], JSON_UNESCAPED_UNICODE),
            'info');
        return ["qr_code" => $qr_code];
    }

    public function progress($params)
    {
        if (empty($params['userinfo']['staff_id'])) {
            throw new ValidationException($this->getTranslation()->_('param_error'));
        }
        $data = HrInternalBonusDetailsModel::find([
            'conditions' => 'recommender_staff_id = :recommender_staff_id:',
            'bind'       => [
                'recommender_staff_id' => $params['userinfo']['staff_id'],
            ],
        ])->toArray();
        if (empty($data)) {
            return [];
        }
        $staff_ids  = array_merge(array_column($data, 'recommender_staff_id'), array_column($data, 'candidate_staff_id'));
        $resume_ids = array_column($data, 'candidate_resume_id');
        $hc_ids     = array_column($data, 'candidate_hc_id');
        $resumeData = [];
        if (!empty($resume_ids)) {
            $resumeData = HrResumeModel::find(
                [
                    'conditions' => 'id in ({resume_ids:array})',
                    'bind'       => ['resume_ids' => $resume_ids],
                ]
            )->toArray();
            $resumeData = array_column($resumeData, null, 'id');
        }


        $staffData = [];
        if (!empty($staff_ids)) {
            $staffData = HrStaffInfoModel::find(
                [
                    'conditions' => 'staff_info_id in ({staff_ids:array})',
                    'bind'       => ['staff_ids' => $staff_ids],
                ]
            )->toArray();
            $staffData = array_column($staffData, null, 'staff_info_id');
        }


        $hcData = [];
        if (!empty($hc_ids)) {
            $hcData = HrHcModel::find(
                [
                    'conditions' => 'hc_id in ({hc_ids:array})',
                    'bind'       => ['hc_ids' => $hc_ids],
                ]
            )->toArray();
            $hcData = array_column($hcData, null, 'hc_id');
        }
        foreach ($data as $key => $value) {
            if(empty($resumeData[$value['candidate_resume_id']])){
                unset($data[$key]);
                continue;
            }
            $data[$key]['candidate_resume_phone']      = $resumeData[$value['candidate_resume_id']]['phone'] ?? '';
            $data[$key]['recommend_source']            = $resumeData[$value['candidate_resume_id']]['recommend_source'] ?? '';
            $data[$key]['recommend_source_text']       = !empty($data[$key]['recommend_source']) ? $this->getTranslation()->_('22464_recommend_source_' . $data[$key]['recommend_source']) : '';
            $data[$key]['resume_created_at']           = show_time_zone($resumeData[$value['candidate_resume_id']]['created_at'] ?? '');
            $data[$key]['recommender_staff_name']      = $staffData[$value['recommender_staff_id']]['name'] ?? '';
            $data[$key]['candidate_resume_name']       = ($resumeData[$value['candidate_resume_id']]['first_name'] ?? '') . " " . ($resumeData[$value['candidate_resume_id']]['last_name'] ?? '');
            $data[$key]['candidate_hc_store_id']       = $hcData[$value['candidate_hc_id']]['worknode_id'] ?? '';
            $data[$key]['candidate_hc_store_name']     = !empty($hcData[$value['candidate_hc_id']]['worknode_id']) ? $this->showStoreName($hcData[$value['candidate_hc_id']]['worknode_id']) : '';
            $data[$key]['candidate_hc_job_title_id']   = $hcData[$value['candidate_hc_id']]['job_title'] ?? '';
            $data[$key]['candidate_hc_job_title_name'] = !empty($hcData[$value['candidate_hc_id']]['job_title']) ? $this->showJobTitleName($hcData[$value['candidate_hc_id']]['job_title']) : '';
            $data[$key]['bonus_amount']                = bcdiv($value['bonus_amount'], 100, 0);

            $data[$key]['staff_hire_date'] = $value['staff_hire_date'] = !empty($staffData[$value['candidate_staff_id']]['hire_date']) ? date('Y-m-d',
                strtotime($staffData[$value['candidate_staff_id']]['hire_date'])) : '';

            // 进度状态数组
            $progress_status_data            = $this->buildProgressStatus($value);
            $data[$key]['bonus_status_text'] = $progress_status_data['candidate_progress_status'] ?? [];
            $data[$key]['tip_text']          = $progress_status_data['tip_text'] ?? '';
        }
        $data = array_values($data);
        // 按照resume_created_at倒序排序
        usort($data, function($a, $b) {
            return strtotime($b['resume_created_at']) - strtotime($a['resume_created_at']);
        });
        
        // 头部展示 汇总
        $head_tip = [
            'num'          => count($data),
            'bonus_amount' => array_sum(array_column($data, 'bonus_amount')),
        ];
        return ['head_tip' => $head_tip, 'data' => $data];
    }

    /**
     * @param $value
     * @return array
     */
    private function buildProgressStatus($value): array
    {
        // 对应的进度颜色 1=绿色 2=黄色 3=灰色 4= 红色
        if ($value['candidate_resume_status'] == HrInternalBonusDetailsModel::CANDIDATE_RESUME_STATUS_EMPLOYED) {
            // 已入职
            if ($value['bonus_status'] == HrInternalBonusDetailsModel::BONUS_STATUS_QUALIFIED) {
                // 已入职 - 达到1阶段发放标准
                $candidate_progress_status = [
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_1'),
                    ],
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_2'),
                    ],
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_progress_status_2'),
                    ],
                    [
                        'progress_color'  => 2,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_progress_status_3'),
                    ],
                ];
                $tip_text                  = $this->getTranslation()->_('22464_candidate_progress_tip_1');
            } elseif ($value['bonus_status'] == HrInternalBonusDetailsModel::BONUS_STATUS_QUALIFIED_2) {
                // 已入职 - 达到2阶段发放标准
                $candidate_progress_status = [
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_1'),
                    ],
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_2'),
                    ],
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_progress_status_2'),
                    ],
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_progress_status_3'),
                    ],
                ];
                $tip_text                  = $this->getTranslation()->_('22464_candidate_progress_tip_2');
            } else {
                // 已入职 - 未达到发放标准
                $candidate_progress_status = [
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_1'),
                    ],
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_2'),
                    ],
                    [
                        'progress_color'  => 2,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_progress_status_2'),
                    ],
                    [
                        'progress_color'  => 3,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_progress_status_3'),
                    ],
                ];
                $tip_text                  = $this->getTranslation()->_('22464_candidate_progress_tip_3',
                    ['staff_hire_date' => $value['staff_hire_date']]);
            }
        } elseif ($value['candidate_resume_status'] == HrInternalBonusDetailsModel::CANDIDATE_RESUME_STATUS_NOT_EMPLOYED) {
            // 未入职
            $candidate_progress_status = [
                [
                    'progress_color'  => 1,
                    'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_1'),
                ],
                [
                    'progress_color'  => 4,
                    'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_3'),
                ],
            ];
            if (!empty($value['candidate_approve_reject_reason'])) {
                $tip_text = $this->getTranslation()->_('22464_candidate_progress_tip_4',
                    ['reject_reason' => $value['candidate_approve_reject_reason']]);
            } else {
                $tip_text = $this->getTranslation()->_('22464_candidate_progress_tip_5');
            }
        } elseif ($value['candidate_resume_status'] == HrInternalBonusDetailsModel::CANDIDATE_RESUME_STATUS_LEFT) {
            // 已离职
            if ($value['bonus_status'] == HrInternalBonusDetailsModel::BONUS_STATUS_QUALIFIED) {
                // 已离职- 达到1阶段发放标准
                $candidate_progress_status = [
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_1'),
                    ],
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_2'),
                    ],
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_progress_status_2'),
                    ],
                    [
                        'progress_color'  => 4,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_4'),
                    ],
                ];
                $tip_text                  = $this->getTranslation()->_('22464_candidate_progress_tip_6');
            } elseif ($value['bonus_status'] == HrInternalBonusDetailsModel::BONUS_STATUS_QUALIFIED_2) {
                // 已离职- 达到2阶段发放标准
                $candidate_progress_status = [
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_1'),
                    ],
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_2'),
                    ],
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_progress_status_2'),
                    ],
                    [
                        'progress_color'  => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_progress_status_3'),
                    ],
                ];
                $tip_text                  = $this->getTranslation()->_('22464_candidate_progress_tip_2');
            } else {
                // 已离职- 未达到发放标准
                $candidate_progress_status = [
                    [
                        'progress_color'      => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_1'),
                    ],
                    [
                        'progress_color'      => 1,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_2'),
                    ],
                    [
                        'progress_color'      => 4,
                        'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_4'),
                    ],
                ];
                $tip_text                  = $this->getTranslation()->_('22464_candidate_progress_tip_8');
            }
        } else {
            // 已推荐-处理中
            $candidate_progress_status = [
                [
                    'progress_color'      => 1,
                    'progress_status' => $this->getTranslation()->_('22464_candidate_resume_status_1'),
                ],
                [
                    'progress_color'      => 2,
                    'progress_status' => $this->getTranslation()->_('22464_candidate_progress_status_1'),
                ],
                [
                    'progress_color'      => 3,
                    'progress_status' => $this->getTranslation()->_('22464_candidate_progress_status_2'),
                ],
                [
                    'progress_color'      => 3,
                    'progress_status' => $this->getTranslation()->_('22464_candidate_progress_status_3'),
                ],
            ];
            $tip_text                  = $this->getTranslation()->_('22464_candidate_progress_tip_9');
        }
        return [
            'candidate_progress_status' => $candidate_progress_status,
            'tip_text'                  => $tip_text,
        ];
    }

}
