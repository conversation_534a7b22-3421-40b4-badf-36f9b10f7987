<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AssetsInfoLogModel;
use FlashExpress\bi\App\Models\backyard\AssetsOrderModel;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\AssetsGoodsModel;
use FlashExpress\bi\App\Models\backyard\AssetsOrderDetailModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditUnionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\HrStaffContractRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\WmsRepository;
use FlashExpress\bi\App\Repository\AssetRepository;
use FlashExpress\bi\App\Models\backyard\HeadquartersAddressModel;
use Matrix\Exception;
use Mpdf\Mpdf;
use Phalcon\Db;
use Phalcon\Db\Column;

class AssetServer extends AuditBaseServer
{
    public $timezone;
    public $asset;

    public static $paramsIn = [];
    public static $paramsUserInfo = [];

    const DC_SP_SITE_TYPE = 1;
    const HUB_SITE_TYPE = 2;
    const SHOP_SITE_TYPE = 3;
    const HO_SITE_TYPE = 4;
    const OS_SITE_TYPE = 5;
    const BDC_SITE_TYPE = 6;
    const BHUB_SITE_TYPE = 7;
    const FFM_SITE_TYPE = 8;
    /**
     *
     * 个人资产大礼包列表
     */
    const SPREE_PRIVATE_LIST_MAP = [
        self::DC_SP_SITE_TYPE => [
            13 => [
                'FEX00010',
                'FEX00015',
                'FEX00016',
                'FEX00047',
                'FEX00232',
                'FEX00266'
            ],
            110 => [
                'FEX00010',
                'FEX00015',
                'FEX00047',
                'FEX00067',
                'FEX00232',
                'FEX00266'
            ]
        ],
        self::SHOP_SITE_TYPE => [
            155 => [
                'FEX00010',
                'FEX00015',
                'FEX00016',
                'FEX00047',
                'FEX00232',
                'FEX00266'
            ]
        ]
    ];

    /**
     *
     * 公共资产大礼包列表
     */
    const SPREE_PUBLIC_LIST_MAP = [
        self::DC_SP_SITE_TYPE => [
            ['FEX00277', 5],
            ['FEX00283', 3],
            ['FEX00050', 3],
            ['FEX00278', 1],
            ['FEX00144', 2],
            ['FEX00008', 2],
            ['FEX00279', 2],
            ['FEX00142', 1],
            ['FEX00248', 1],
            ['FEX00292', 1],
            ['FEX00158', 1],
            ['FEX00002', 1],
            ['FEX00011', 1],
            ['FEX00285', 1],
            ['FEX00264', 1],
            ['FEX00265', 2],
            ['FEX00307', 2],
        ]
    ];

    /**
     *
     * 大礼包名称
     */
    const SPREE_TRANSLATE_NAMES = [
        'bike_courier' => [
            'th' => 'ชุดเซ็ท Bike Courier',
            'en' => 'ชุดเซ็ท Bike Courier',
            'zh' => 'Bike Courier 大礼包',
        ],
        'van_courier' => [
            'th' => 'ชุดเซ็ท Van Courier',
            'en' => 'ชุดเซ็ท Van Courier',
            'zh' => 'Van Courier大礼包',
        ],
        'shop_bike' => [
            'th' => 'ชุดเซ็ท Shop Courier',
            'en' => 'ชุดเซ็ท Shop Courier',
            'zh' => 'Shop快递员大礼包',
        ],
        'ho_dc' => [
            'th' => 'ชุดเซ็ท DC/SP',
            'en' => 'ชุดเซ็ท DC/SP',
            'zh' => 'DC/SP大礼包',
        ]
    ];

    const DC_SP_SITE = [1, 2];    // dc/sp 网点类型
    const SHOP_SITE = [4, 5, 7]; //shop 网点类型
    const HUB_SITE = [8];       // hub 网点类型
    const OS_SITE = [9];       // os 网点类型
    const BDC_SITE = [10];       // BDC 网点类型
    const BHUB_SITE = [12];       // BHUB 网点类型
    const FFM_SITE = [11];       // ffm 网点类型
    const CDC_SITE = [13];       // CDC 网点类型
    const PDC_SITE = [14];       // PDC 网点类型

    const DC_SP_MANAGER_ID = 16;// dc/sp网点负责人ID值
    const SOURCE_TYPE_PUBLIC_ASSETS = 'public_assets'; //来源 公共资产
    const ASSET_TYPE = [0, 1]; //资产类型

    const ENABLE_NONE_ASSETS = 0;
    const ENABLE_PERSONAL_ASSETS = 1;
    const ENABLE_PUBLIC_ASSETS = 2;
    const ENABLE_DOUBLE_ASSETS = 3;
    const ENABLE_ASSETS_DESC = [
        self::ENABLE_NONE_ASSETS => "没有资产权限",
        self::ENABLE_PERSONAL_ASSETS => "个人资产权限",
        self::ENABLE_PUBLIC_ASSETS => "公共资产权限",
        self::ENABLE_DOUBLE_ASSETS => "个人/公共资产权限",
    ];

    //1 使用中 2 待接收 3 转交中 4 已报修 5 已挂失 6 已报废 7 已停用,8已入库,9报修待接收，10多余待接收，11离职待接收
    const OPERATE_STATUS_USEING = 1;
    const OPERATE_STATUS_WAIT_PULL = 2;
    const OPERATE_STATUS_TRANSFER = 3;
    const OPERATE_STATUS_REPAIR = 4;
    const OPERATE_STATUS_REPORT_LOSE = 5;
    const OPERATE_STATUS_REPORT_SCRAPPED = 6;
    const OPERATE_STATUS_REPORT_DISABLE = 7;
    const OPERATE_STATUS_IN = 8;
    const OPERATE_STATUS_REPAIR_WAIT_PULL = 9;
    const OPERATE_STATUS_MORE_WAIT_PULL = 10;
    const OPERATE_STATUS_LEAVE_WAIT_PULL = 11;

    const OPERATE_DESC = [
        self::OPERATE_STATUS_USEING => "使用中",
        self::OPERATE_STATUS_WAIT_PULL => "待接收",
        self::OPERATE_STATUS_TRANSFER => "转交中",
        self::OPERATE_STATUS_REPAIR => "已报修",
        self::OPERATE_STATUS_REPORT_LOSE => "已挂失",
        self::OPERATE_STATUS_REPORT_SCRAPPED => "已报废",
        self::OPERATE_STATUS_REPORT_DISABLE => "已停用",
        self::OPERATE_STATUS_IN=>"已入库",
        self::OPERATE_STATUS_REPAIR_WAIT_PULL=>"报修待接收",
        self::OPERATE_STATUS_MORE_WAIT_PULL=>"10多余待接收",
        self::OPERATE_STATUS_LEAVE_WAIT_PULL=>"离职待接收"
    ];

    /**
     * 个人资产
     */
    const ASSETS_PERSONAL_MAP = [
        "FEX00266" => 4990,
        "FEX00015" => 2500,
        "FEX00012" => 300,
        "FEX00232" => 300,
        "FEX00016" => 1300,
        "FEX00013" => 3500,
        "FEX00345" => 22500
    ];

    /**
     * 公共资产
     */
    const ASSETS_PRICE_MAP = [
        "FEX90068" => 4300,
        "FEX00400" => 7200,
        "FEX00399" => 2200,
        "FEX00398" => 2500,
        "FEX00397" => 2500,
        "FEX00396" => 6500,
        "FEX00395" => 6000,
        "FEX00394" => 5000,
        "FEX00393" => 2200,
        "FEX00392" => 2200,
        "FEX00391" => 8000,
        "FEX00390" => 5200,
        "FEX00389" => 4400,
        "FEX00388" => 13000,
        "FEX00344" => 3400,
        "FEX00343" => 4300,
        "FEX00342" => 450,
        "FEX00338" => 3800,
        "FEX00332" => 2300,
        "FEX00330" => 11200,
        "FEX00329" => 2000,
        "FEX00327" => 7200,
        "FEX00326" => 5500,
        "FEX00325" => 7200,
        "FEX00321" => 14500,
        "FEX00320" => 1000,
        "FEX00319" => 5700,
        "FEX00317" => 1300,
        "FEX00316" => 4000,
        "FEX00315" => 2300,
        "FEX00314" => 2400,
        "FEX00308" => 2300,
        "FEX00307" => 1500,
        "FEX00303" => 3000,
        "FEX00299" => 550,
        "FEX00295" => 4000,
        "FEX00292" => 2400,
        "FEX00291" => 8000,
        "FEX00290" => 5000,
        "FEX00288" => 2000,
        "FEX00287" => 2000,
        "FEX00285" => 4700,
        "FEX00283" => 2000,
        "FEX00281" => 6500,
        "FEX00280" => 2600,
        "FEX00279" => 2700,
        "FEX00278" => 1700,
        "FEX00277" => 200,
        "FEX00276" => 5900,
        "FEX00275" => 6600,
        "FEX00274" => 5000,
        "FEX00273" => 6600,
        "FEX00272" => 2800,
        "FEX00271" => 3000,
        "FEX00270" => 4500,
        "FEX00265" => 22500,
        "FEX00264" => 4300,
        "FEX00262" => 5000,
        "FEX00257" => 4200,
        "FEX00256" => 6500,
        "FEX00255" => 4500,
        "FEX00254" => 4500,
        "FEX00253" => 4500,
        "FEX00252" => 4500,
        "FEX00249" => 3700,
        "FEX00248" => 500,
        "FEX00234" => 6500,
        "FEX00233" => 2500,
        "FEX00232" => 300,
        "FEX00231" => 26500,
        "FEX00227" => 5000,
        "FEX00191" => 1300,
        "FEX00190" => 1500,
        "FEX00158" => 1200,
        "FEX00144" => 8400,
        "FEX00142" => 700,
        "FEX00139" => 6500,
        "FEX00133" => 2000,
        "FEX00131" => 2600,
        "FEX00130" => 2600,
        "FEX00129" => 2900,
        "FEX00078" => 2400,
        "FEX00072" => 1900,
        "FEX00064" => 400,
        "FEX00054" => 600,
        "FEX00053" => 600,
        "FEX00051" => 6700,
        "FEX00050" => 1000,
        "FEX00021" => 6200,
        "FEX00016" => 1700,
        "FEX00013" => 3700,
        "FEX00011" => 800,
        "FEX00008" => 2600,
        "FEX00004" => 700,
        "FEX00002" => 5000
    ];


    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
        $this->other    = new OtherRepository($timezone, $lang);
        $this->wms      = new WmsRepository();
        $this->storeS   = new SysStoreServer($timezone);
        $this->audit    = new AuditRepository($this->lang);
        $this->staff    = new StaffRepository($this->lang);
        $this->asset    = new AssetRepository($this->lang);
        $this->contract = new HrStaffContractRepository($this->lang);
    }

    /**
     * 获取盘点列表数据
     * @param array $paramIn
     * @return array
     */
    public function getInventoryAssetList($paramIn = [])
    {
        //[1]获取参数
        $staffInfo      = $this->processingDefault($paramIn, 'staff_info', 3); //显示类型  1-我的审批 2-我的申请
        $storeCategory  = $staffInfo['store_category'] ?? 0;
        $storeId        = $staffInfo['organization_id'] ?? "";
        $staffId        = $staffInfo['id'] ?? 0;
        $jobTitle       = $staffInfo['job_title'] ?? 0;
        $resultData     = [];

        //[2]数据校验
        if ($staffInfo['organization_type'] != 1) { //总部员工无权操作
            throw new \Exception($this->getTranslation()->_('8000'));
        }

        if (empty($storeCategory)) { //网点没有指定类型
            throw new \Exception($this->getTranslation()->_('please try again'));
        }

        $hubManagerList = env('hub_manager', '17377,25921,27424,27118,24513,23917,33489,27011,17139,35180');
        $hubManagerList = explode(',', $hubManagerList);
        $server         = new \FlashExpress\bi\App\Server\StaffServer();

        $sql       = "SELECT category, manager_id FROM sys_store where id = '{$storeId}'";
        $sysStore = $this->getDI()->get('db_fle')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);

        if (in_array($storeCategory, self::HUB_SITE) && in_array($staffId, $hubManagerList)) { // 判断是否是hub类型负责人
            $hub_manager = [
                $hubManagerList[0] => 'TH01470301',
                $hubManagerList[1] => 'TH02030204',
                $hubManagerList[2] => 'TH56010102',
                $hubManagerList[3] => 'TH68010201',
                $hubManagerList[4] => 'TH27011602',
                $hubManagerList[5] => 'TH49030503',
                $hubManagerList[6] => 'TH37010701',
                $hubManagerList[7] => 'TH71111201',
                $hubManagerList[8] => 'TH38040201',
                $hubManagerList[9] => 'TH47190703',
            ];
            $storeId = $hub_manager[$staffId] ?? '';
            if (empty($storeId)) { //指定网点不存在
                throw new \Exception($this->getTranslation()->_('8000'));
            }
        } else if (!self::isDcSpManager($storeCategory, $jobTitle) && !self::isShopManager($storeCategory, $sysStore['manager_id'], $staffId)) { //DC、SP
            throw new \Exception($this->getTranslation()->_('8000'));
        }

        //[3]获取列表数据
        $batchNumber = $this->getBatchNo();
        $data        = $this->getInventoryList($batchNumber, $storeId);
        if ($data) {
            $data       = current($data);
            $resultData = $this->getInventoryAsset($data['id']);
        } else {
            throw new \Exception($this->getTranslation()->_('4008'));
        }
        //是否在在盘点期间内
        $inPeriod = true;

        return $this->checkReturn(['data' => ['dataList' => $resultData, 'inPeriod' => ($inPeriod ? 1 : 2), 'relate_id' => $data['id']]]);
    }

    /**
     * 获取资产盘点详细数据
     */
    public function getInventoryAsset($pid)
    {
        $sql = "--
            select 
                is.asset_id,
                is.available_cnt,
                is.unavailable_cnt,
                is.total_cnt,
                CONVERT_TZ(is.updated_at,'+00:00', '{$this->timezone}') AS updated_at
            from inventory_asset `is`
            left join inventory_asset_item isi on isi.asset_id = is.asset_id
            where is.pid = {$pid} and isi.status = 1
            order by isi.sort asc
            ";
        $ret = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        if ($ret) {
            $_t = $this->getTranslation();
            foreach ($ret as &$item) {
                $item['asset_title']     = $_t->_('inventory_asset_' . $item['asset_id']);
                $item['available_cnt']   = intval($item['available_cnt']);
                $item['unavailable_cnt'] = intval($item['unavailable_cnt']);
                $item['total_cnt']       = intval($item['total_cnt']);
            }
        }
        return $ret;
    }

    /**
     * 获取资产盘点表
     * @param string $batchNumber   批次ID 目前就一个批次
     * @param string $store_id      网点ID
     * @return mixed
     */
    public function getInventoryList($batchNumber, $store_id = null)
    {
        if (!empty($store_id)) {
            $where = " and store_id = '{$store_id}'";
        } else {
            $where = "";
        }
        $sql = "select * from inventory where batch_no = {$batchNumber} {$where}";
        $ret = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $ret;
    }

    /**
     * 获取资产参考数据
     * @param $store_id
     * @param $pid
     * @return mixed
     */
    public function getInventoryBase($store_id, $pid)
    {
        $sql       = "--
            SELECT 
                asset_id,
                {$pid} as pid,
                sum(reference_cnt) as total_cnt
            FROM inventory_base 
            WHERE store_id  = '{$store_id}' or store_id = 'All' 
            GROUP BY asset_id
        ";
        $info_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }

    /**
     * 更新资产数
     */
    public function updateAssetCnt($paramIn = [])
    {
        $data        = $this->processingDefault($paramIn, 'dataList', 3);
        $staffInfoId = $this->processingDefault($paramIn, 'staff_id', 2);
        $pid         = $this->processingDefault($paramIn, 'relateId', 2);

        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $data = array_column($data, null, 'asset_id');
            $sql  = "UPDATE inventory_asset SET available_cnt = CASE asset_id ";
            foreach ($data as $asset_id => $v) {
                $sql .= sprintf("WHEN %d THEN %d ", $asset_id, $v['available_cnt']);
            }
            $sql .= "END, unavailable_cnt = CASE asset_id ";
            foreach ($data as $asset_id => $v) {
                $sql .= sprintf("WHEN %d THEN %d ", $asset_id, $v['unavailable_cnt']);
            }
            $sql .= "END, total_cnt = CASE asset_id ";
            foreach ($data as $asset_id => $v) {
                $sql .= sprintf("WHEN %d THEN %d ", $asset_id, $v['total_cnt']);
            }
            $sql .= "END, last_operator = {$staffInfoId} WHERE pid  = {$pid}";
            $db->execute($sql);
            $db->commit();
        } catch (\Exception $e) {
            /* 有异常回滚 */
            $db->rollback();
            $this->wLog('error',$e->getMessage(),'updateInventoryAsset');
        }
        return $this->checkReturn([]);
    }

    /**
     * 获取网点
     */
    public function getNullMangerStores()
    {
        $sql       = "--
            SELECT 
                id,
                store_id
            FROM `inventory`
            WHERE `store_manager_id` IS NULL";
        $info_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }

    /**
     * 获取批次
     * 目前不需要历次批次，所以取用固定批次
     * @return string
     */
    public function getBatchNo()
    {
        return date("Ymd", strtotime(env('inventory_start_time', '2019-11-20'))) . date("Ymd", strtotime(env('inventory_end_time', '2019-11-21')));
    }

    /**
     * 验证用户是否是网点负责人角色
     * @param $staff_info
     * @return bool
     */

    public function isSiteManager($userinfo)
    {
        $staff_info_id = $userinfo['id'];// 登录用户ID（员工ID）
        $organization_id = $userinfo['organization_id'];//网点ID
        $job_title = $userinfo['job_title'];//职位ID

        //todo 根据员工ID获取出对应的网点信息
        $sql = "SELECT category, manager_id FROM sys_store where id = '{$organization_id}'";
        $sys_store = $this->getDI()->get('db_fle')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        // 判断是否总部员工
        if ($userinfo['organization_type'] == 2 && empty($sys_store) ) return true;

        // 判断是否是dc/sp 负责人
        if (self::isDcSpManager($sys_store['category'], $job_title)) return true;

        // 判断是否是shop类型负责人
        if (self::isShopManager($sys_store['category'], $sys_store['manager_id'], $staff_info_id)) return true;

        // 判断是否是hub类型负责人
        if (self::isHubManager($sys_store['category'], $staff_info_id)) return true;

        return false;
    }


    /**
     * 根据用户权限过滤资产ids
     * @param $assetGoodsIds
     * @param $userinfo
     *
     */
    public function filterAssetsIds($assetGoodsIds, $userinfo)
    {
        $organization_id = $userinfo['organization_id'];//网点ID
        $auditServer = new AuditServer($this->lang, $this->timezone);
        $auditServer = Tools::reBuildCountryInstance($auditServer, [$this->lang, $this->timezone]);
        $isHasAssets = $auditServer->getASPermission(['staff_id' => $userinfo['id']]);

        //白名单
        if ($auditServer->getASPermission(['staff_id' => $userinfo['id']], true)) {
            $type="1,2,3,4,5,6,7";
        }else{
            $sql       = "SELECT category, manager_id FROM sys_store where id = '{$organization_id}'";
            $sys_store = $this->getDI()->get('db_fle')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
            $type = $this->getUserSite($sys_store['category'], $userinfo);
        }
        $assetGoodsIdsStr = implode(", ", $assetGoodsIds);
        $assetType = self::ASSET_TYPE[0];
        if ($isHasAssets) {
            $sql = "select id from assets_goods where type in ({$type}) and deleted =0 and category = 1 and is_public = {$assetType} and id in ({$assetGoodsIdsStr})";
            $assetGoodsIds= $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (!$assetGoodsIds) {
                $assetType = self::ASSET_TYPE[1];
                $sql = "select id from assets_goods where type in ({$type}) and deleted =0 and category = 1 and is_public = {$assetType} and id in ({$assetGoodsIdsStr})";
                $assetGoodsIds= $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }
            return [$assetType, array_column($assetGoodsIds, 'id')];
        }
        return [$assetType, []];
    }


    /**
     * 验证用户是否有交接权限
     * @param $staff_info
     * @return bool
     */
    public function isSiteEmailManager($user_id)
    {
        if ($user_id == 29792) {
            return true;
        }
        $db = $this->getDI()->get('db_fle');
        $sql = "SELECT id,organization_id,job_title FROM staff_info where id = {$user_id}";
        $user_info = $db->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (empty($user_info)) {
            return false;
        }
        $staff_info_id = $user_info['id'];// 登录用户ID（员工ID）
        $organization_id = $user_info['organization_id'];//网点ID
        $job_title = $user_info['job_title'];//职位ID

        $sql = "SELECT category, manager_id FROM sys_store where id = '{$organization_id}'";
        $sys_store = $db->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);

        //总部员工
        if ($organization_id == -1) return true;

        // 判断是否是dc/sp 负责人
        if (self::isDcSpManager($sys_store['category'], $job_title)) return true;

        // 判断是否是shop类型负责人
//        if (self::isShopManager($sys_store['category'], $sys_store['manager_id'], $staff_info_id)) return true;

        // 判断是否是hub类型负责人
//        if (self::isHubManager($sys_store['category'], $staff_info_id)) return true;

        if (self::isShopHubManager($organization_id, $staff_info_id)) return true;


        return false;
    }

    /**
     *
     * @param $storeId
     * @param $uid
     * @return \Phalcon\Mvc\Model
     *
     */
    private static function isShopHubManager($storeId, $uid)
    {
        return SysStoreModel::findFirst([
            'conditions' => ' id = :store_id: and manager_id = :uid: ',
            'bind' => [
                'store_id' => $storeId,
                'uid' => $uid
            ]
        ]);
    }


    public function getUserSite($sysCategory, $userinfo)
    {

        if (in_array($sysCategory, self::HUB_SITE)) {
            return self::HUB_SITE_TYPE;
        }
        if (in_array($sysCategory, self::DC_SP_SITE)) {
            return self::DC_SP_SITE_TYPE;
        }
        if (in_array($sysCategory, self::SHOP_SITE)) {
            return self::SHOP_SITE_TYPE;
        }
        if (in_array($sysCategory, self::OS_SITE)) {
            return self::OS_SITE_TYPE;
        }
        if (in_array($sysCategory, self::BDC_SITE)) {
            return self::BDC_SITE_TYPE;
        }
        if (in_array($sysCategory, self::CDC_SITE)) {
            return self::BDC_SITE_TYPE;
        }
        if (in_array($sysCategory, self::BHUB_SITE)) {
            return self::BHUB_SITE_TYPE;
        }
        if (in_array($sysCategory, self::FFM_SITE)) {
            return self::FFM_SITE_TYPE;
        }
        if (in_array($sysCategory, self::PDC_SITE)) {
            return self::BDC_SITE_TYPE;
        }

        if ($userinfo['organization_type'] == 2) {

            return self::HO_SITE_TYPE;
        }

        return 0;
    }

    /**
     *
     *  大礼包列表
     *
     * @param $staffInfoId
     * @param null $sourceType
     * @return array
     */
    public function getSpressList($staffInfoId, $sourceType = null) {

        $staffInfo = $this->staff->getStaffInfoById($staffInfoId);

        $params     = [];
        //现在改成不传barCode,返回全部，实时
        $params['lang'] = $this->lang;
        $stock_data     = (new BaseRepository())->getDataFromWms(env("api_wms_goodsStock"), $params);
        $this->getDI()->get('logger')->write_log(json_encode($stock_data), "info");
        if ($stock_data['code'] != 1) {
            return [];
        }

        $stock_data = $stock_data['data'];
        $results = [
            "bar_code" => "",
            "id" => "",
        ];
        if ($sourceType && $sourceType == self::SOURCE_TYPE_PUBLIC_ASSETS) {
            if (
                $staffInfo
                &&
                isset($staffInfo['category'])
            ) {
                // 公共资产 dc/sp
                if (in_array($staffInfo['category'],
                    self::DC_SP_SITE)
                ) {
                    if (isset(self::SPREE_PUBLIC_LIST_MAP[self::DC_SP_SITE_TYPE])
                    ) {
                        $results['goods_name'] = self::SPREE_TRANSLATE_NAMES['ho_dc'][substr(strtolower($this->lang), 0, 2)];
                        $barCodes = self::SPREE_PUBLIC_LIST_MAP[self::DC_SP_SITE_TYPE];

                        $sql = "select * from assets_goods where bar_code in ('" . implode("', '", array_column($barCodes, 0)) . "') and is_public = 1 and deleted = 0  and type = " . self::DC_SP_SITE_TYPE;
                        $assetsGoods = $this->getDI()->get('db')->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
                        $assetsGoods = array_column($assetsGoods, null, 'bar_code');

                        foreach ($barCodes as $barCode) {
                            if (isset($assetsGoods[$barCode[0]])) {
                                $skuBarCodes = $assetsGoods[$barCode[0]]['sku_bar_code'];
                                $skuBarCodes = explode(",", $skuBarCodes);
                                $isHas = false;
                                foreach ($skuBarCodes as $skuBarCode) {
                                    if (isset($stock_data[$skuBarCode]) && $stock_data[$skuBarCode]['availableInventory']) {
                                        $isHas = true;
                                        $results['goods_list'][] = [
                                            'id' => $assetsGoods[$barCode[0]]['id'],
                                            'bar_code' => $assetsGoods[$barCode[0]]['bar_code'],
                                            'goods_name' => $assetsGoods[$barCode[0]]['goods_name_' . substr(strtolower($this->lang), 0, 2) ],
                                            'available_inventory' => $stock_data[$skuBarCode]['availableInventory'],
                                            'num' => $barCode[1],
                                        ];
                                    }
                                }
                                if (!$isHas) {
                                    $results['available_inventory'] = 0;
                                    $results['goods_list'] = [];
                                    return $results;
                                }
                            }
                        }

                    }
                } else {
                    return [];
                }
            }
        } else {
            if (
                $staffInfo
                &&
                isset($staffInfo['category'])
            ) {
                $barCodes = [];
                $type = 0;
                if ( in_array($staffInfo['category'],
                    self::DC_SP_SITE)
                ) {
                    //DC/SP
                    if (isset(self::SPREE_PRIVATE_LIST_MAP[self::DC_SP_SITE_TYPE][$staffInfo['job_title']])
                    ) {
                        $barCodes =self::SPREE_PRIVATE_LIST_MAP[self::DC_SP_SITE_TYPE][$staffInfo['job_title']];
                        if ($staffInfo['job_title'] == enums::$job_title['bike_courier']
                        ) {

                            $results['goods_name'] = self::SPREE_TRANSLATE_NAMES['bike_courier'][substr(strtolower($this->lang), 0, 2)];
                        } else if ($staffInfo['job_title'] == enums::$job_title['van_courier']
                        ) {
                            $results['goods_name'] = self::SPREE_TRANSLATE_NAMES['van_courier'][substr(strtolower($this->lang), 0, 2)];
                        }
                        $type = self::DC_SP_SITE_TYPE;
                    }
                } else if (in_array($staffInfo['category'],
                    self::SHOP_SITE)
                ) {
                    //SHOP
                    $results['goods_name'] = self::SPREE_TRANSLATE_NAMES['shop_bike'][substr(strtolower($this->lang), 0, 2)];
                    if (isset(self::SPREE_PRIVATE_LIST_MAP[self::SHOP_SITE_TYPE][$staffInfo['job_title']])
                    ) {
                        $barCodes =self::SPREE_PRIVATE_LIST_MAP[self::SHOP_SITE_TYPE][$staffInfo['job_title']];
                        $type = self::SHOP_SITE_TYPE;
                    }
                }

                if (!$barCodes) {
                    return [];
                }

                $sql = "select * from assets_goods where bar_code in ('" . implode("', '", $barCodes) . "') and is_public = 0 and deleted =  0 and type = " . $type ;
                $assetsGoods = $this->getDI()->get('db')->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
                $assetsGoods = array_column($assetsGoods, null, 'bar_code');

                foreach ($barCodes as $barCode) {
                    if (isset($assetsGoods[$barCode])) {
                        $skuBarCodes = $assetsGoods[$barCode]['sku_bar_code'];
                        $skuBarCodes = explode(",", $skuBarCodes);
                        $isHas = false;
                        foreach ($skuBarCodes as $skuBarCode) {
                            if (isset($stock_data[$skuBarCode]) && $stock_data[$skuBarCode]['availableInventory']) {
                                $isHas = true;
                                $results['goods_list'][] = [
                                    'id' => $assetsGoods[$barCode]['id'],
                                    'bar_code' => $assetsGoods[$barCode]['bar_code'],
                                    'goods_name' => $assetsGoods[$barCode]['goods_name_' . substr(strtolower($this->lang), 0, 2) ],
                                    'available_inventory' => $stock_data[$skuBarCode]['availableInventory'],
                                    'num' => 1,
                                ];
                            }
                        }
                        if (!$isHas) {
                            $results['available_inventory'] = 0;
                            $results['goods_list'] = [];
                            return $results;
                        }
                    }
                }

            } else {
                return [];
            }
        }

        $results['available_inventory'] = 1;
        return $results;
    }

    public function getSpressListV2($staffInfoId, $source)
    {

        $staffInfo = $this->staff->getStaffInfoById($staffInfoId);

        $params     = [];
        //现在改成不传barCode,返回全部，实时
        $params['lang'] = $this->lang;
        $stock_data     = (new BaseRepository())->getDataFromWms(env("api_wms_goodsStock"), $params);
        $this->getDI()->get('logger')->write_log(json_encode($stock_data), "info");
        if ($stock_data['code'] != 1) {
            return [];
        }

        $stock_data = $stock_data['data'];
        $result = [];
        if (!$source || $source != self::SOURCE_TYPE_PUBLIC_ASSETS) {
            if (
                $staffInfo
                &&
                isset($staffInfo['category'])
            ) {
                if ( in_array($staffInfo['category'],
                    self::DC_SP_SITE)
                ) {
                    $result[] = $this->getBikeCourierSpress($stock_data);
                    $result[] = $this->getVanCourierSpree($stock_data);
                } else if (in_array($staffInfo['category'], self::SHOP_SITE)) {
                    $result[] = $this->getShopBikeSpree($stock_data);
                }
            }
        }

        return array_filter($result);
    }

    private function getBikeCourierSpress($stock_data)
    {
        $result = [
            "bar_code" => "",
            "id" => "",
        ];
        if (isset(self::SPREE_PRIVATE_LIST_MAP[self::DC_SP_SITE_TYPE][enums::$job_title['bike_courier']])){
            $barCodes = self::SPREE_PRIVATE_LIST_MAP[self::DC_SP_SITE_TYPE][enums::$job_title['bike_courier']];
            $result['goods_name'] = self::SPREE_TRANSLATE_NAMES['bike_courier'][substr(strtolower($this->lang), 0, 2)];

            $sql = "select * from assets_goods where bar_code in ('" . implode("', '", $barCodes) . "') and is_public = 0 and deleted =  0 and type = " . self::DC_SP_SITE_TYPE;
            $assetsGoods = $this->getDI()->get('db')->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
            $assetsGoods = array_column($assetsGoods, null, 'bar_code');

            foreach ($barCodes as $barCode) {
                if (isset($assetsGoods[$barCode])) {
                    $skuBarCodes = $assetsGoods[$barCode]['sku_bar_code'];
                    $skuBarCodes = explode(",", $skuBarCodes);
                    $isHas = false;
                    foreach ($skuBarCodes as $skuBarCode) {
                        if (isset($stock_data[$skuBarCode]) && $stock_data[$skuBarCode]['availableInventory'] && $stock_data[$skuBarCode]['availableInventory'] > 0) {
                            $isHas = true;
                            $result['goods_list'][] = [
                                'id' => $assetsGoods[$barCode]['id'],
                                'bar_code' => $assetsGoods[$barCode]['bar_code'],
                                'goods_name' => $assetsGoods[$barCode]['goods_name_' . substr(strtolower($this->lang), 0, 2) ],
                                'available_inventory' => (int) $stock_data[$skuBarCode]['availableInventory'],
                                'num' => 1,
                            ];
                        }
                    }
                    if (!$isHas) {
                        $result['available_inventory'] = 0;
                        $result['goods_list'] = [];
                        $this->getDI()->get("logger")->write_log("Spress 大礼包存在库存为空资产" . $barCode . " : " . json_encode($skuBarCodes, JSON_UNESCAPED_UNICODE) . " : " . json_encode($stock_data, JSON_UNESCAPED_UNICODE), "info");
                        return $result;
                    }
                }
            }
        }

        $result['available_inventory'] = 1;
        return $result;
    }


    private function getVanCourierSpree($stock_data)
    {

        $result = [
            "bar_code" => "",
            "id" => "",
        ];
        if (isset(self::SPREE_PRIVATE_LIST_MAP[self::DC_SP_SITE_TYPE][enums::$job_title['van_courier']])){
            $barCodes = self::SPREE_PRIVATE_LIST_MAP[self::DC_SP_SITE_TYPE][enums::$job_title['van_courier']];
            $result['goods_name'] = self::SPREE_TRANSLATE_NAMES['van_courier'][substr(strtolower($this->lang), 0, 2)];

            $sql = "select * from assets_goods where bar_code in ('" . implode("', '", $barCodes) . "') and is_public = 0 and deleted =  0 and type = " . self::DC_SP_SITE_TYPE;
            $assetsGoods = $this->getDI()->get('db')->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
            $assetsGoods = array_column($assetsGoods, null, 'bar_code');

            foreach ($barCodes as $barCode) {
                if (isset($assetsGoods[$barCode])) {
                    $skuBarCodes = $assetsGoods[$barCode]['sku_bar_code'];
                    $skuBarCodes = explode(",", $skuBarCodes);
                    $isHas = false;
                    foreach ($skuBarCodes as $skuBarCode) {
                        if (isset($stock_data[$skuBarCode]) && $stock_data[$skuBarCode]['availableInventory'] && $stock_data[$skuBarCode]['availableInventory'] > 0) {
                            $isHas = true;
                            $result['goods_list'][] = [
                                'id' => $assetsGoods[$barCode]['id'],
                                'bar_code' => $assetsGoods[$barCode]['bar_code'],
                                'goods_name' => $assetsGoods[$barCode]['goods_name_' . substr(strtolower($this->lang), 0, 2) ],
                                'available_inventory' => (int) $stock_data[$skuBarCode]['availableInventory'],
                                'num' => 1,
                            ];
                        }
                    }
                    if (!$isHas) {
                        $result['available_inventory'] = 0;
                        $result['goods_list'] = [];
                        $this->getDI()->get("logger")->write_log("Spress 大礼包存在库存为空资产" . $barCode . " : " . json_encode($skuBarCodes, JSON_UNESCAPED_UNICODE) . " : " . json_encode($stock_data, JSON_UNESCAPED_UNICODE), "info");
                        return $result;
                    }
                }
            }
        }

        $result['available_inventory'] = 1;
        return $result;

    }

    private function getShopBikeSpree($stock_data)
    {

        $result = [
            "bar_code" => "",
            "id" => "",
        ];
        if (isset(self::SPREE_PRIVATE_LIST_MAP[self::SHOP_SITE_TYPE][enums::$job_title['shop_bike']])){
            $barCodes = self::SPREE_PRIVATE_LIST_MAP[self::SHOP_SITE_TYPE][enums::$job_title['shop_bike']];
            $result['goods_name'] = self::SPREE_TRANSLATE_NAMES['shop_bike'][substr(strtolower($this->lang), 0, 2)];

            $sql = "select * from assets_goods where bar_code in ('" . implode("', '", $barCodes) . "') and is_public = 0 and deleted =  0 and type = " . self::SHOP_SITE_TYPE;
            $assetsGoods = $this->getDI()->get('db')->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
            $assetsGoods = array_column($assetsGoods, null, 'bar_code');

            foreach ($barCodes as $barCode) {
                if (isset($assetsGoods[$barCode])) {
                    $skuBarCodes = $assetsGoods[$barCode]['sku_bar_code'];
                    $skuBarCodes = explode(",", $skuBarCodes);
                    $isHas = false;
                    foreach ($skuBarCodes as $skuBarCode) {
                        if (isset($stock_data[$skuBarCode]) && $stock_data[$skuBarCode]['availableInventory'] && $stock_data[$skuBarCode]['availableInventory'] > 0) {
                            $isHas = true;
                            $result['goods_list'][] = [
                                'id' => $assetsGoods[$barCode]['id'],
                                'bar_code' => $assetsGoods[$barCode]['bar_code'],
                                'goods_name' => $assetsGoods[$barCode]['goods_name_' . substr(strtolower($this->lang), 0, 2) ],
                                'available_inventory' => (int) $stock_data[$skuBarCode]['availableInventory'],
                                'num' => 1,
                            ];
                        }
                    }
                    if (!$isHas) {
                        $result['available_inventory'] = 0;
                        $result['goods_list'] = [];
                        $this->getDI()->get("logger")->write_log("Spress 大礼包存在库存为空资产" . $barCode . " : " . json_encode($skuBarCodes, JSON_UNESCAPED_UNICODE) . " : " . json_encode($stock_data, JSON_UNESCAPED_UNICODE), "info");
                        return $result;
                    }
                }
            }
        }

        $result['available_inventory'] = 1;
        return $result;

    }
    /**
     * 获取网点可申请资产列表
     * @param $userinfo
     * @return array
     */
    public function getList($userinfo, $sourceType = null)
    {
        $staff_info_id   = $userinfo['id'];// 登录用户ID（员工ID）
        $organization_id = $userinfo['organization_id'];//网点ID
        $job_title       = $userinfo['job_title'];//职位ID

        $audit_server = Tools::reBuildCountryInstance((new AuditServer($this->lang, $this->timezone)), [$this->lang, $this->timezone]);
        $permission = $audit_server->getASPermission(["staff_id" => $staff_info_id], true);
        //白名单
        if ($permission) {
            if ($sourceType && $sourceType == self::SOURCE_TYPE_PUBLIC_ASSETS) {
                $sql = "--
                            SELECT
                                id,
                                bar_code,
                                goods_name_en,
                                goods_name_th,
                                goods_name_zh,
                                sku_bar_code 
                            FROM
                                assets_goods 
                            WHERE
                                type IN ( 1, 2, 3, 5, 6, 7, 8) 
                                AND deleted = 0 
                                AND category = 1 and is_public = 1 GROUP BY bar_code";
            } else {
                $sql = "--
                            SELECT
                                id,
                                bar_code,
                                goods_name_en,
                                goods_name_th,
                                goods_name_zh,
                                sku_bar_code 
                            FROM
                                assets_goods 
                            WHERE
                                type IN ( 1, 2, 3, 5, 6, 7, 8) 
                                AND deleted = 0 
                                AND category = 1 and is_public = 0 GROUP BY bar_code";
            }

        } else {
            //todo 根据员工ID获取出对应的网点信息
            $sql       = "SELECT category, manager_id FROM sys_store where id = '{$organization_id}'";
            $sys_store = $this->getDI()->get('db_fle')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
            $type      = $this->getUserSite($sys_store['category'], $userinfo);

            if ($sourceType && $sourceType == self::SOURCE_TYPE_PUBLIC_ASSETS) {
                $sql = "SELECT id,bar_code, goods_name_en,goods_name_th,goods_name_zh,sku_bar_code FROM assets_goods where type = '{$type}' and deleted = 0 and category = 1 and is_public = 1 ";
            } else {
                $sql = "SELECT id,bar_code, goods_name_en,goods_name_th,goods_name_zh,sku_bar_code FROM assets_goods where type = '{$type}' and deleted = 0 and category = 1 and is_public = 0 ";
            }
        }
        $assets_list = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $list        = [];
        if (is_array($assets_list) && !empty($assets_list)) {

            $stock_flag = false;
            $params     = [];
            //现在改成不传barCode,返回全部，实时
            $params['lang'] = $this->lang;
            $stock_data     = (new BaseRepository())->getDataFromWms(env("api_wms_goodsStock"), $params);
            if ($stock_data['code'] == 1) {
                $stock_flag = true;
            }

            foreach ($assets_list as $key => $value) {
                $list[$key] = [
                    'id'                  => $value['id'],
                    'bar_code'            => $value['bar_code'],
                    'goods_name'          => '',
                    'available_inventory' => 0
                ];
                if ($stock_flag) {
                    $barCodeArr = [];

                    //如果规格的bar_code为空，则用bar_code
                    if (empty($value['sku_bar_code'])) {
                        //如果bar_code不为空，则放进数组
                        if (!empty($value['bar_code'])) {
                            $barCodeArr[] = $value['bar_code'];
                        }
                    } else {
                        $barCodeArr = explode(",", $value['sku_bar_code']);
                    }

                    if (!empty($barCodeArr)) {
                        foreach ($barCodeArr as $bar_code) {
                            if (isset($stock_data['data'][$bar_code])) {
                                $list[$key]['available_inventory'] += intval($stock_data['data'][$bar_code]['availableInventory']);
                            }
                        }
                    }
                }

                switch ($this->lang) {
                    case 'en':
                        $list[$key]['goods_name'] = $value['goods_name_en'];
                        break;
                    case 'th':
                        $list[$key]['goods_name'] = $value['goods_name_th'];
                        break;
                    default:
                        $list[$key]['goods_name'] = $value['goods_name_zh'];
                }


            }
        }

        return $list;

    }

    public function getMyBatchAssets($staff_info_id)
    {
        $lang = substr(strtolower($this->lang), 0, 2);
        $lang = empty($lang) ? 'zh' : $lang;
        //没有语言 默认英文
        $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);
        $sql = "--
                SELECT
                    ag.goods_name_{$lang} as goods_name,
                    1 as count,
                    ag.id,
                    ai.operate_status,
                    ai.id as assets_info_id,
                    ai.staff_info_id as staff_info_id
                FROM
                    assets_info ai
                left join
                    assets_goods ag
                ON
                    ai.assets_goods_id = ag.id 
                WHERE
                    ai.staff_info_id = '{$staff_info_id}' AND ai.operate_status not in (2,3) AND ai.state=1 AND ag.is_public = 1 AND ag.deleted = 0 AND ag.is_batch = 1 ";
        //GROUP BY ai.assets_goods_id, ai.operate_status
        $assets_list = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $result = [];


        $assetsIds = array_column($assets_list, "assets_info_id");

        //数量过多拆分为 1000
        $assetsIds_chunk = array_chunk($assetsIds, 1000);
        $assetsLog = [];
        foreach($assetsIds_chunk as $k_chunk=>$v_chunk) {
            $sql = " --
                select 
                    max(id) as id 
                from 
                    assets_info_log 
                where 
                find_in_set(assets_id, :assets_ids) and transfer_staff_id = :transfer_id and transfer_state = 1 group by assets_id ; ";

            $ids_info = $this->getDI()->get("db_rby")->fetchAll($sql, Db::FETCH_ASSOC, [
                "assets_ids"  => implode(",", $v_chunk),
                "transfer_id" => $staff_info_id
            ],                                               [
                "assets_ids"  => Db\Column::BIND_PARAM_STR,
                "transfer_id" => Db\Column::BIND_PARAM_INT
            ]);

            $ids = array_column($ids_info, 'id');
            $assetsLog_chunk = [];
            if (!empty($ids)) {
                $assetsLog_chunk = AssetsInfoLogModel::find([
                    'conditions' => 'id in ({ids:array})',
                    'bind' => [
                        'ids' => $ids,
                    ],
                    'columns' => ['id', 'staff_info_id', 'assets_id']
                ])->toArray();
            }
            $assetsLog = array_merge($assetsLog,$assetsLog_chunk);
        }

        $assetsLog = array_column($assetsLog, null, "assets_id");

        foreach ($assets_list as $item) {
            $staff_info_id = isset($assetsLog[$item['assets_info_id']]) ? $assetsLog[$item['assets_info_id']]['staff_info_id'] : $item['staff_info_id'];
            if (!isset($result[$item['id'].'_'.$staff_info_id])) {
                $result[$item['id'].'_'.$staff_info_id] = [
                    'goods_id' => $item['id'],
                    'goods_name' => $item['goods_name'],
                    'useing_count' => 0,
                    'unavailable_count' => 0,
                    'repair_count' => 0,
                    'scrapped_count' => 0,
                    'disable_count' => 0,
                    'lose_count' => 0,
                    'source_staff_id' => isset($assetsLog[$item['assets_info_id']]) ? $assetsLog[$item['assets_info_id']]['staff_info_id'] : $item['staff_info_id'],

                ];
            }
            if (in_array($item['operate_status'], [
                self::OPERATE_STATUS_USEING,
            ])) {
                $result[$item['id'].'_'.$staff_info_id]['useing_count'] += $item['count'];
            } else if ($item['operate_status'] == self::OPERATE_STATUS_REPAIR) {
                $result[$item['id'].'_'.$staff_info_id]['repair_count'] += $item['count'];
                $result[$item['id'].'_'.$staff_info_id]['unavailable_count'] += $item['count'];
            } else if ($item['operate_status'] == self::OPERATE_STATUS_REPORT_SCRAPPED) {
                $result[$item['id'].'_'.$staff_info_id]['scrapped_count'] += $item['count'];
                $result[$item['id'].'_'.$staff_info_id]['unavailable_count'] += $item['count'];
            } else if ($item['operate_status'] == self::OPERATE_STATUS_REPORT_DISABLE) {
                $result[$item['id'].'_'.$staff_info_id]['disable_count'] += $item['count'];
                $result[$item['id'].'_'.$staff_info_id]['unavailable_count'] += $item['count'];
            } else if ($item['operate_status'] == self::OPERATE_STATUS_REPORT_LOSE) {
                $result[$item['id'].'_'.$staff_info_id]['lose_count'] += $item['count'];
                $result[$item['id'].'_'.$staff_info_id]['unavailable_count'] += $item['count'];
            }
        }

        return array_values($result);
    }

    /**
     * 获取我的资产列表信息
     * @param $staff_info_id 员工ID（员工表staff_info表ID）
     * @return array
     */
    public function getMyAssets($staff_info_id, $sourceType = null)
    {

        $lang = substr(strtolower($this->lang), 0, 2);
        $lang = empty($lang) ? 'zh' : $lang;
        //没有语言 默认英文
        $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);
        $sql = "--
                SELECT
                    ai.id,
                    ai.express,
                    ai.express_sn,
                    ai.handover_type,
                    ai.out_time,
                    ag.bar_code,
                    ag.goods_name_{$lang} as goods_name,
                    ag.goods_name_en,
                    ag.goods_name_zh,
                    ai.assets_goods_id,
                    ai.asset_code,
                    CONVERT_TZ( ai.transfer_at, '+00:00', '{$this->timezone}' ) AS transfer_at,
                    ai.transfer_state,
                    ai.transfer_type,
                    ai.staff_info_id,
                    ai.sn_code,
                    ai.pno,
                    ai.operate_status
                FROM
                    assets_info ai
                left join
                    assets_goods ag
                ON
                    ai.assets_goods_id = ag.id
                WHERE
                    ai.staff_info_id = '{$staff_info_id}' AND ai.state=1";

        if ($sourceType && $sourceType == self::SOURCE_TYPE_PUBLIC_ASSETS) {
            $sql .= " AND ag.is_public = 1 AND ag.is_batch = 0 ";
        } else {
            $sql .= " AND ag.is_public = 0 AND ag.is_batch = 0 ";
        }

        $assets_list = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $list = [];
        if (empty($assets_list)) {
            return $list;
        }

        $_t = $this->getTranslation();

        $assetsIds = array_column($assets_list, "id");
        //数量过多拆分为 1000
        $assetsIds_chunk = array_chunk($assetsIds, 1000);
        $assetsLog = [];
        foreach($assetsIds_chunk as $k_chunk=>$v_chunk) {

        $sql = " --
                select 
                    max(id) as id 
                from 
                    assets_info_log 
                where 
                find_in_set(assets_id, :assets_ids) and transfer_staff_id = :transfer_id and transfer_state = 1 group by assets_id; ";
            $ids_info = $this->getDI()->get("db_rby")->fetchAll($sql, Db::FETCH_ASSOC, [
            "assets_ids" => implode(",", $v_chunk),
            "transfer_id" => $staff_info_id
        ], [
            "assets_ids" => Db\Column::BIND_PARAM_STR,
            "transfer_id" => Db\Column::BIND_PARAM_INT
        ]);
            $ids = array_column($ids_info, 'id');
            $assetsLog_chunk = [];
            if (!empty($ids)) {
                $assetsLog_chunk = AssetsInfoLogModel::find([
                    'conditions' => 'id in ({ids:array})',
                    'bind' => [
                        'ids' => $ids,
                    ],
                    'columns' => ['id', 'staff_info_id', 'assets_id']
                ])->toArray();
            }
            $assetsLog = array_merge($assetsLog,$assetsLog_chunk);
        }
        $assetsLog = array_column($assetsLog, null, "assets_id");

        foreach ($assets_list as $key => $value) {

            $state = $value['operate_status'];
            //前端没有待接收，7之前，都-1。
            $state = $state == 2 || $state == 3 ? 2 : $state;
            $state = $state == 4 ? 3 : $state;
            $state = $state == 5 ? 4 : $state;
            $state = $state == 6 ? 5 : $state;
            $state = $state == 7 ? 6 : $state;

            //9，10，11不减
            $state = intval($state);

            //判断当该资产最后的log日志 transfer_state = 4 说明是被总部拒绝。state修改为50
            //只有是使用中的才需要判断，因为在FBI后台点拒绝时 直接吧资产状态改为了 operate_status = 1 向assets_info_log日志表添加了一条状态为 transfer_state = 4 的记录
            if ($value['operate_status'] == 1){
                if ($this->isHeadquartersRefuse($value['id'])){
                    $state = 50; //未接收状态
                }
            }

            $item = [
                'id' => $value['id'],
                'bar_code' => $value['bar_code'],
                'goods_name' => $value['goods_name'],
                'goods_name_en' => $value['goods_name_en'],
                'goods_name_zh' => $value['goods_name_zh'],
                'out_time' => $value['out_time'],
                'express_type' => $_t->_('handover_type'),
                'express' => $value['express'],
                'express_sn' => $value['express_sn'],
                'asset_code' => $value['asset_code'],
                'sn' => $value['sn_code'],
                'source_staff_id' => isset($assetsLog[$value['id']]) ? $assetsLog[$value['id']]['staff_info_id'] : $value['staff_info_id'],
                'state' => $state,
                'assets_goods_id'=>$value['assets_goods_id']
            ];

            //是否转移过，转移过则回传转移信息
            if (!empty($value['transfer_at'])) {
                $item['out_time'] = $value['transfer_at'];

                if ($value['transfer_type'] == 1) {
                    $item['express'] = '';
                    $item['express_sn'] = '';
                    $item['express_type'] = $_t->_('express_type_face');
                }
                if ($value['transfer_type'] == 2) {
                    $item['express'] = '';
                    $item['express_sn'] = $value['pno'];
                    $item['express_type'] = $_t->_('handover_type');
                }
            }
            $list[] = $item;
        }

        return $list;

    }

    /**
     * 判断是否是 dc/sp 网点负责人
     * @param $sys_store_category 网点表（sys_store)表中网点分类
     * @param $staff_info_job_title 员工基础表（staff_info) 职位ID
     * @return bool
     */
    private function isDcSpManager($sys_store_category, $staff_info_job_title)
    {

        if (in_array($sys_store_category, self::DC_SP_SITE) && $staff_info_job_title == self::DC_SP_MANAGER_ID) {
            return true;
        }
        return false;

    }

    /**
     * 判断是否是 shop 网点 负责人
     * @param $sys_store_category 网点表（sys_store)表中网点分类
     * @param $sys_store_manager_id 网点表中shop类型网点 负责人ID（对应员工基础表中ID）
     * @param $staff_info_id 员工基础表（staff_info) 员工ID
     * @return bool
     */
    private function isShopManager($sys_store_category, $sys_store_manager_id, $staff_info_id)
    {

        if (in_array($sys_store_category, self::SHOP_SITE) && $sys_store_manager_id == $staff_info_id) {
            return true;
        }
        return false;

    }

    /**
     * 判断是否是 hub 网点 负责人
     * @param $sys_store_category 网点表（sys_store)表中网点分类
     * @param $staff_info__id 员工基础表（staff_info) 员工ID
     * @return bool
     */
    private function isHubManager($sys_store_category, $staff_info_id)
    {
        $positionData = UC('wmsRole')['assetData'];
        if (in_array($sys_store_category, self::HUB_SITE) && in_array($staff_info_id, $positionData)) {
            return true;
        }
        return false;

    }

    /*
     * 更新审批状态
     */
    public function updateAssetInfo($paramIn, $userInfo)
    {
        $staff_id = $paramIn['staff_info_id'];
        //获取详情
        $typeUnion = strpos($paramIn['audit_id'], "asp_") !== false ? enums::$audit_type['ASP']: enums::$audit_type['AS'];
        $info = (new AuditlistRepository($this->lang, $this->timezone))
            ->getStaffAuditUnion(['id_union' => $paramIn['audit_id'], 'type_union' => $typeUnion]);

        //已经是最终状态,不能操作
        if ($info['status_union'] - 100 != 1) {
            throw new Exception($this->getTranslation()->_('1016'));
        }

        $paramIn['apply_staff_id'] = $info['staff_id_union'];
        self::$paramsIn = $paramIn;
        self::$paramsUserInfo = $userInfo;

        $originId = $info['origin_id'];
        $auditApplyInfo = AuditApplyModel::findFirst([
            'conditions' => ' biz_type = :type: and biz_value = :value: ',
            'bind' => ['type' => $typeUnion, 'value' => $originId]
        ]);
        if ($auditApplyInfo) {

            if ($paramIn['status'] == 2) {
                $res = (new ApprovalServer($this->lang, $this->timezone))->approval(
                    (int) str_replace(['asp_', 'as_'], '', $paramIn['audit_id']),
                    $typeUnion,
                    $staff_id
                );
                //如果审批返回异常需要给予提示
                if (!isset($res) || empty($res)) {
                    return [false, $this->getTranslation()->_('asset_or_wms_to_scm_error')];
                }
            } else if ($paramIn['status'] == 3) {
                (new ApprovalServer($this->lang, $this->timezone))->reject(
                    (int) str_replace(['asp_', 'as_'], '', $paramIn['audit_id']),
                    $typeUnion,
                    $paramIn['reject_reason'],
                    $staff_id
                );
            } else if ($paramIn['status'] == 4) {

                (new ApprovalServer($this->lang, $this->timezone))->cancel(
                    (int) str_replace(['asp_', 'as_'], '', $paramIn['audit_id']),
                    $typeUnion,
                    $paramIn['reject_reason'],
                    $staff_id
                );
            } else {
                return [false, 'not exist status'];
            }
        } else {
            $publicApprovers = $this->getSetEnv('asset_public_approver');
            $personalApprovers = $this->getSetEnv('asset_personal_approver');

            if (empty($info)
                || ($paramIn['status'] == 4 && $staff_id != $info['staff_id_union'])
                || (in_array($paramIn['status'], [2, 3]) &&  $typeUnion == enums::$audit_type['AS'] && !in_array($staff_id, $personalApprovers))
                || (in_array($paramIn['status'], [2, 3]) &&  $typeUnion == enums::$audit_type['ASP'] && !in_array($staff_id, $publicApprovers))
            ) {
                throw new Exception($this->getTranslation()->_('4009'));
            }

            list($isSucc, $msg) = $this->updateAssetAuditStatus($paramIn,$userInfo);
            if (!$isSucc) {
                throw new Exception($this->getTranslation()->_('2109'));
                return [false, $msg];
            }
            //[5]追加日志
            $log['staff_id'] = $info['staff_id_union'];
            $log['type'] = $typeUnion;
            $log['original_type'] = $info['status_union'] - 100;
            $log['original_id'] = $info['origin_id'];
            $log['operator'] = $staff_id;
            $log['operator_name'] = $paramIn['staff_name'];
            $log['to_status_type'] = $paramIn['status'];
            $this->other->insertLog($log);
        }

        return [true, ''];
    }

    //请求入库api接口

    /**
     * 组装资产出库数据以及完成出库操作
     * @param array $paramIn 审批信息
     * @param array $userInfo 用户信息
     * @return array
     */
    public function httpPostFunToWMS($paramIn, $userInfo)
    {
        //同意
        $checkArr = self::checkAssetOrder($paramIn, $userInfo);
        //记录省、市、区code用于更新订单信息
        $update_order_arr['province_code'] = $checkArr['province_code'];
        $update_order_arr['city_code'] = $checkArr['city_code'];
        $update_order_arr['district_code'] = $checkArr['district_code'];
        $update_order_arr['postal_code'] = $checkArr['postalCode'];
        if (!empty($checkArr['goods']) && $checkArr['goods']) {
            $goods = json_decode($checkArr['goods'], true);
            $barCodes = array_column($goods, 'barCode');
            $sql = " select `sku_bar_code` from `assets_goods` WHERE id in (SELECT MAX(id) from `assets_goods` WHERE `bar_code` in ('" . implode("', '", $barCodes) . "') GROUP BY `bar_code` );";
            $assetsGoods = $this->getDI()->get('db')->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
            $barCodes = [];
            foreach ($assetsGoods as $assetsGood) {
                $barCodes = array_merge($barCodes, explode(",", $assetsGood['sku_bar_code']));
            }

            $sql = " select `bar_code` , `sku_bar_code`, `goods_name_th` from `assets_goods` WHERE id in (SELECT MAX(id) from `assets_goods` WHERE `bar_code` in ('" . implode("', '", $barCodes) . "') GROUP BY `bar_code` );";
            $assetsGoods = $this->getDI()->get('db')->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
            $assetsGoods = array_column($assetsGoods, null, 'bar_code');

            $params     = [];
            $params['lang'] = $this->lang;
            $stock_data = (new BaseRepository())->getDataFromWms(env("api_wms_goodsStock"), $params);
            if ($stock_data['code'] != 1) {
                // 库存获取失败
                $this->getDI()->get('logger')->write_log(
                    "库存接口返回失败 " . env("api_wms_goodsStock") . " " . json_encode($stock_data,JSON_UNESCAPED_UNICODE).'--'.json_encode($params, JSON_UNESCAPED_UNICODE),
                    'info'
                );
                return [false, '库存接口检查失败' . $stock_data['code']];
            } else {
                $stock_data = $stock_data['data'];
                foreach ($goods as $k => &$good) {
                    if (isset($stock_data[$good['barCode']]) && $stock_data[$good['barCode']]['availableInventory']) {
                        // 如果用户申请的barcode有库存 继续 检查下个资产库存
                        continue;
                    } else {
                        //替换
                        $skuBarCodes = explode(',', $assetsGoods[$good['barCode']]['sku_bar_code']);
                        $this->getDI()->get('logger')->write_log(
                            'assets_order_info_request_no_stock =>' .
                            $good['barCode'] ." -> ". $assetsGoods[$good['barCode']]['sku_bar_code'],
                            'info'
                        );
                        // 执行遍历查找替换
                        foreach ($skuBarCodes as $skuBarCode) {
                            if ($skuBarCode != $good['barCode'] && isset($assetsGoods[$skuBarCode]) && isset($stock_data[$skuBarCode]) && $stock_data[$skuBarCode]['availableInventory']) {
                                // 如果申请barcode不等于库存返回barcode 并且有库存 替换
                                $this->getDI()->get('logger')->write_log(
                                    'assets_order_info_request_replace =>' .
                                    $good['barCode'] ." -> ".$skuBarCode,
                                    'info'
                                );
                                $good['barCode'] = $skuBarCode;
                                $good['goodsName'] = $assetsGoods[$skuBarCode]['goods_name_th'];
                                break;
                            }
                        }
                        // 循环查找替换操作完成后检查是否替换或者是否有库存
                        if (!empty($good['barCode']) || !empty($stock_data) || !isset($stock_data[$good['barCode']]) || !$stock_data[$good['barCode']]['availableInventory']) {
                                $this->getDI()->get('logger')->write_log(
                                    '没有库存=>' .
                                    json_encode($good,JSON_UNESCAPED_UNICODE) .
                                    json_encode($stock_data,JSON_UNESCAPED_UNICODE),
                                    'info'
                                );
                            //todo pangaofeng
                           // return [false, $good['barCode'] . " " . $assetsGoods[$good['barCode']]['goods_name_th'] . " 没有库存"];

                        }
                    }

                }
            }

            $checkArr['goods'] = json_encode($goods);
            unset($checkArr['province_code'], $checkArr['city_code'], $checkArr['district_code']);
            $url = env('api_wms_order');
            $resPost = httpPostFun($url, $checkArr, null, env('wms_pwd'));
            $this->getDI()->get('logger')->write_log(
                'assets_order_info_request =>' .
                json_encode($paramIn) .
                json_encode($userInfo) . ' request=>' .
                json_encode($checkArr) . ' response=>' .
                json_encode($resPost) . ' url =>' . $url,
                'info'
            );

            //由于scm那边会出现返回值为null的，审批人员再次审批时scm那边可能其实已经成功了，所以兼容下1003="订单已存在"的数据
            if ($resPost['code'] != 1 && $resPost['code'] != 1003) {
                $this->getDI()->get('logger')->write_log(
                    'assets_order_info_request_error =>' .
                    json_encode($checkArr, JSON_UNESCAPED_UNICODE) . ' response=>' .
                    json_encode($resPost),
                    'info'
                );
                return [false, $resPost['msg'] . $resPost['code']];
            }
            return [$update_order_arr, ''];
        } else {
            return [false, '同步库存失败，未找到资产订单【'.str_replace(["asp_", "as_"], "", $paramIn['audit_id']).'】的详情信息'];
        }
    }

    /**
     * 更新资产审批状态
     * @param $paramIn
     * @param $userInfo
     * @param bool $isFinal
     * @return array|jsonData
     * @throws Exception
     */
	public function updateAssetAuditStatus($paramIn, $userInfo, $isFinal = false)
	{
		$db = $this->getDI()->get('db');
		$db->begin();
		try {
			if(empty($paramIn)) {
				throw new \Exception('没有效参数');
			}
			$time                     = gmdate('Y-m-d H:i:s');
			$paramIn['reject_reason'] = isset($paramIn['reject_reason']) ? $paramIn['reject_reason'] : '';
			$reject_reason            = $paramIn['status'] == 3 ? addslashes($paramIn['reject_reason']) : "";
			if ($paramIn['status'] == 2) {
                //如果存在存在订单详情信息修改则优先修改资产订单详情表
                if (!empty($paramIn['modify_assets_num'])) {
                    foreach ($paramIn['modify_assets_num'] as $key => $value) {
                        if(!isset($value['id'])){
                            throw new \Exception('没有assets_order_detail =>id'.json_encode($paramIn['modify_assets_num']));
                        }
                        $this->getDI()->get('db')->updateAsDict(
                            'assets_order_detail',
                            ['approval_num' => (int)$value['nums']],
                            [
                                'conditions' => 'id = ?',
                                'bind'       => [$value['id']],
                            ]
                        );
                    }

                    $staff_info = (new AuditRepository($this->lang))->checkoutStaff($paramIn['apply_staff_id']);
                    $order_ids  = array_column($paramIn['modify_assets_num'], 'id');
                    $lang       = strtolower(substr($this->lang, 0, 2));
                    $order_id   = str_replace(["asp_", "as_"], "", $paramIn['audit_id']);
                    //没有语言 默认英文
                    $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);

                    $builder = $this->modelsManager->createBuilder();
                    $builder->columns('a.bar_code,a.id,a.goods_name_en,goods_name_th,goods_name_zh,b.id as order_id');
                    $builder->from(['a' => AssetsGoodsModel::class]);
                    $builder->leftjoin(AssetsOrderDetailModel::class, 'a.id = b.goods_id', 'b');
                    $builder->inWhere('b.id', $order_ids);
                    $builder->andWhere('b.order_id = :order_id:', ['order_id' => $order_id]);
                    $asstsInfo = $builder->getQuery()->execute()->toArray();
                    if (empty($asstsInfo)) {
                        throw new ValidationException($this->getTranslation()->_('7101'));
                    }

                    $assetsNum = array_column($paramIn['modify_assets_num'], 'nums', 'id');
                    $summary   = [];
                    $values    = [];
                    if (!empty($asstsInfo)) {
                        foreach ($asstsInfo as $value) {
                            if (isset($assetsNum[$value['order_id']])) {
                                $summary[] = [
                                    'key'   => ($value["goods_name_{$lang}"] ?? $value['goods_name_en']),
                                    'value' => "{$assetsNum[$value['order_id']]}",
                                ];
                                if ($assetsNum[$value['order_id']] > 0) {
                                    for ($i = 0; $i < $assetsNum[$value['order_id']]; $i++) {
                                        $values[] = [
                                            'staff_info_id'   => $paramIn['apply_staff_id'],
                                            'order_id'        => $order_id,
                                            'store_id'        => $staff_info['organization_id'],
                                            'assets_goods_id' => $value['id'],
                                            'bar_code'        => $value['bar_code'],
                                            'handover_type'   => 1,
                                            'state'           => 0,
                                            'created_at'      => $time,
                                        ];
                                    }
                                }
                            }
                        }
                    }
                    //获取类型
                    $typeUnion = strpos($paramIn['audit_id'], "asp_") !== false ? enums::$audit_type['ASP']: enums::$audit_type['AS'];
                    //修改审批的申请概要
                    $result =  (new ApprovalServer($this->lang, $this->timezone))->setAuditSummary($typeUnion,$order_id,$summary);

                    if (!empty($values)) {
                        $flag = (new BaseRepository())->batch_insert("assets_info", $values);
                        if(!$flag){
                            throw new \Exception('插入assets_info 表失败!'.json_encode($values));
                        }
                    }
                }
			    //wms出库
                list($isSucc, $msg) = $this->httpPostFunToWMS($paramIn, $userInfo);
                if($isSucc === false) {
                    //出库失败
                    throw new \Exception($this->getTranslation()->_('2109') . " " . $msg, 1000);
                } else {
                    //出库成功,修改资产订单表信息
                    $db->updateAsDict(
                        'assets_order',
                        [
                            'status' => $paramIn['status'],
                            'approve_user' => $paramIn['staff_info_id'],
                            'reject_reason' => $reject_reason,
                            'province_code' => $isSucc['province_code'],
                            'city_code' => $isSucc['city_code'],
                            'district_code' => $isSucc['district_code'],
                            'postal_code' => $isSucc['postal_code'],
                        ],
                        [
                            'conditions' => 'wf_role = ?',
                            'bind'       => [$paramIn['audit_id']],
                        ]
                    );
                }
            } else {
                //修改资产订单表信息
                $db->updateAsDict(
                    'assets_order',
                    ['status' => $paramIn['status'], 'approve_user' => $paramIn['staff_info_id'], 'reject_reason' => $reject_reason],
                    [
                        'conditions' => 'wf_role = ?',
                        'bind'       => [$paramIn['audit_id']],
                    ]
                );
            }
		} catch (ValidationException $v) {
			$db->rollback();
			return $this->jsonReturn($this->checkReturn(-3, $v->getMessage()));
		} catch (\Exception $e) {
			$db->rollback();
			if ($e->getCode() == 1000) {
				$this->getDI()->get('logger')->write_log("AssetServer:updateAuditMainStatus:" . $e->getMessage(), 'info');
				throw new InnerException($e->getMessage());
			} else {
				$this->getDI()->get('logger')->write_log("AssetServer:updateAuditMainStatus:   E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString());
				throw new Exception( $e->getMessage());
			}
			return [false, $e->getMessage()];
		}
		$db->commit();
		return [true, ''];
	}

    /**
     * 创建订单
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addAssetsOrder($paramIn = [], $userinfo)
    {
        $time = date("YmdHis");
        $create = gmdate('Y-m-d H:i:s');
        $rand = rand(1000, 9999);
        $orderId = $time . $rand;
        $auditBLL = new \FlashExpress\bi\App\Server\AuditServer($this->lang, $this->timezone);
        if (!$auditBLL->getASPermission($userinfo)) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }
        //获取订单相关人信息
        //$admin_group = UC('wmsRole')['admin_group'];
        $organization_id = $userinfo['organization_id'];
        if(preg_match("/^\d*$/",$paramIn['use_store_id']) && $paramIn['use_store_id']<100 && !empty($paramIn['use_store_id'])){
        //如果用户选择的地址是总部地址
            $addressModel = HeadquartersAddressModel::findFirst([
                'columns' => "office_name as name,address as detail_address,province_code,city_code,postal_code,district_code",
                'conditions' => "id = :id:",
                'bind' => [
                    'id'  => $paramIn['use_store_id']
                ]
            ]);
            if (empty($addressModel)) {
                $uInfo = $this->wms->userStoreInfo($organization_id);
            } else {
                $uInfo = $addressModel->toArray();
            }
        } else {
            $uInfo = $this->wms->userStoreInfo($organization_id);
        }
        $uInfoArr = array_merge(empty($uInfo) ? [] : $uInfo, $userinfo);
        $staffInfo = (new StaffServer())->getStaffById($userinfo['staff_id']);
//        $store_info = $this->storeS->getStoreByid($organization_id);
//        $manage_region = isset($store_info['manage_region']) ? $store_info['manage_region'] : '';
//        $store_category = isset($store_info['category']) ? $store_info['category'] : '';

        $address = SettingEnvModel::findFirst("code = 'asset_address'");
        $place = 'อาคารฟอรั่มทาวเวอร์ ชั้น 36 ห้วยขวาง ห้วยขวาง กทม';
        if(!empty($address))
            $place = $address->set_val;
        $mail_code = SettingEnvModel::findFirst("code = 'asset_mail_code'");
        $mail = '10310';
        if(!empty($mail_code))
            $mail = $mail_code->set_val;
        $store_code = SettingEnvModel::findFirst("code = 'asset_store_code'");
        $store = 'TH015103';
        if(!empty($store_code))
            $store = $store_code->set_val;

        $pro = substr($store,0,4);
        $city = substr($store,0,6);
        //格式化订单数据
        $serialNo = uniqid('as');
        $returnData['data']['dataList'] = [];
        $orderData['order_union_id'] = $serialNo;//订单号
        $orderData['organization_id'] = $organization_id ;//网点编号
        $orderData['staff_info_id'] = $userinfo['staff_id'];
        $orderData['status'] = 1;
        $orderData['created_at'] = $create;
        $orderData['reason'] = $paramIn['reason'];//原因
        $orderData['shipping_user'] = $uInfoArr['name'];//收货人姓名
        $orderData['consignee_phone'] = $staffInfo && isset($staffInfo['mobile']) ? $staffInfo['mobile'] : '';
        $orderData['consignee_address'] = $uInfoArr['organization_type'] == 2 ? $place : $uInfoArr['detail_address'];
        $orderData['province_code'] = $uInfoArr['organization_type'] == 2 ? $pro : $uInfoArr['province_code'];//省
        $orderData['city_code'] = $uInfoArr['organization_type'] == 2 ? $city : $uInfoArr['city_code'];//市
        $orderData['postal_code'] = $uInfoArr['organization_type'] == 2 ? $mail : $uInfoArr['postal_code'];//邮编
        $orderData['district_code'] = $uInfoArr['organization_type'] == 2 ? $store :$uInfoArr['district_code'];//区
        $orderData['use_people_id'] = empty($paramIn['use_people_id']) ? 0 : $paramIn['use_people_id'];
        $orderData['use_store_id'] = empty($paramIn['use_store_id']) ? '' : $paramIn['use_store_id'];
        if (empty($paramIn['assets'])) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }
        $orderDetailData = [];
        $assetIds = array_column($paramIn['assets'], "id");
        // 根据用户的权限 过滤 不合法的ID
        list($assetType, $assetIds) = $this->filterAssetsIds($assetIds, $userinfo);
        if (!$assetIds) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        //获取资产
        $goodsInfo = $this->goodsInfo($assetIds);
        if (empty($goodsInfo)) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        foreach ($goodsInfo as $key => $value) {
            $orderDetailData[$value['id']] = $value;
        }

        $orderDetailDataFormat = array();
        foreach ($paramIn['assets'] as $key => $value) {

            if (!isset($value['nums']) || $value['nums'] <= 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('7101'));
            }
            if (isset($orderDetailData[$value['id']])) {
                $orderDetailDataFormat[$key]['goods_id'] = isset($value['id']) ? $value['id'] : 0;
                $orderDetailDataFormat[$key]['recomment_num'] = isset($value['nums']) ? $value['nums'] : 0;
                //$orderDetailDataFormat[$key]['approval_num'] = isset($value['nums']) ? $value['nums'] : 0;
                $orderDetailDataFormat[$key]['created_at'] = $create;
            }
        }


        $db = WorkflowModel::beginTransaction($this);
        try {
            $orderId = $this->audit->addOrderInfo($orderData, $orderDetailDataFormat, $assetType);
            if (!$orderId) {
                $this->getDI()->get('logger')->write_log('assets_order_info =>' . json_encode($paramIn) . json_encode($userinfo), 'error');
                return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
            }

            $assetData = UC('wmsRole')['assetApprover'];
            $returnData['data'] = $orderId;
            $staffId = $uInfoArr['id'];
            $storeId = $uInfoArr['organization_type'] == 2 ? '-1' : $uInfoArr['organization_id'];
            $server = new \FlashExpress\bi\App\Server\AuditListServer($this->lang, $this->timezone);
            $summary = $server->generateSummary($orderId, 16);
            $summary = json_encode($summary, JSON_UNESCAPED_UNICODE);
            $insertAuditData[] = [
                'id_union' =>  $assetType == self::ASSET_TYPE[0] ? 'as_' . $orderId : 'asp_' . $orderId,
                'staff_id_union' => $staffId,
                'type_union' => $assetType == self::ASSET_TYPE[0] ? enums::$audit_type['AS'] : enums::$audit_type['ASP'],
                'status_union' => 101,
                'store_id' => $storeId,
                'table' => 'assets_order',
                'created_at' => gmdate('Y-m-d H:i:s', time()),
                'origin_id' => $orderId,
                'summary' => $summary,
                'approval_id' => 0,
            ];

            if (!($this->other->insterUnion($insertAuditData) &&
                (new ApprovalServer($this->lang, $this->timezone))->create($orderId,
                    $assetType == self::ASSET_TYPE[0] ? enums::$audit_type['AS'] : enums::$audit_type['ASP'],
                    $userinfo['staff_id']
                ))
            ) {
                throw new \Exception('create workflow failing');
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('assets_order_info =>' . json_encode($paramIn) . json_encode($userinfo), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }

//        if (!$this->other->insterUnion($insertAuditData)) {
//            $this->getDI()->get('logger')->write_log('assets_order_info =>' . json_encode($paramIn) . json_encode($userinfo), 'error');
//            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
//        }
//
//        //[5]追加审批日志
//        $log['staff_id'] = $staffId;
//        $log['type'] = $assetType == self::ASSET_TYPE[0] ? enums::$audit_type['AS'] : enums::$audit_type['ASP'];
//        $log['original_type'] = 1;
//        $log['original_id'] = $orderId;
//        $log['operator'] = $staffId;
//        $log['operator_name'] = $userinfo['name'];
//        $log['to_status_type'] = 1;
//        $this->other->insertLog($log);
        return $this->checkReturn($returnData);
    }

    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result = (new AuditListServer($this->lang, $this->timezone))->getAssetOrderDetail($auditId, strtolower(substr($this->lang, 0, 2)));
        $statusUnion = $result[0]['status'];
        $staff_info = (new StaffServer())->get_staff($result[0]['staff_info_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }
        //查询 个人资产使用人信息
        $use_staff_info = [];
        if(!empty($result[0]['use_people_id']))
            $use_staff_info = (new StaffServer())->get_staff($result[0]['use_people_id']);


        //查询公共资产 使用地网点 信息
        $use_store_info = [];
        if(!empty($result[0]['use_store_id'])){
            if(preg_match("/^\d*$/",$result[0]['use_store_id']) && $result[0]['use_store_id']<100){
                $headquartersAddress   = HeadquartersAddressModel::find([
                    'conditions' => "id =:id:",
                    'bind'       => [
                        'id' => $result[0]['use_store_id']
                    ],
                    'columns'    => 'id,office_name'
                ])->toArray();
                if(!empty($headquartersAddress)){
                    $use_store_info = array_column($headquartersAddress,'office_name','id');
                }else{
                    $use_store_info = (new SysStoreServer($this->lang,$this->timezone))->getStoreName(array($result[0]['use_store_id']));
                }
            }else{
                $use_store_info = (new SysStoreServer($this->lang,$this->timezone))->getStoreName(array($result[0]['use_store_id']));
            }
        }
        $is_public = 0;
        foreach ($result as $k => $v) {
            $is_public = $v['is_public'];//任意一个资产属性 决定 这单子是公共还是个人

            $returnData['data']['detail'][]     = [
                'key'   => $v['goods_name'],
                'value' => $statusUnion == 1 ? $v['recomment_num'] : $v['approval_num'],
            ];

            $returnData['data']['asset_edit'][] = ['id' => $v['item_id'], 'key' => $v['goods_name'], 'value' => $v['recomment_num']];
        }

        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('apply_parson'), 'value' => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? '')];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('apply_department'),'value' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? '')];

        //个人资产新增 两个字段显示  使用人信息 和 使用人网点
        if(empty($is_public) && !empty($use_staff_info)){
            $show_name = $use_staff_info['data']['name'] . " ({$use_staff_info['data']['id']})";
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('use_people_info'), 'value' => $show_name];
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('store_info'), 'value' => $use_staff_info['data']['store_name']];
        }

        //公共资产新增 使用地信息 （就是网点信息）
        if($is_public == 1 && !empty($use_store_info)){
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('use_area_info'), 'value' => $use_store_info[$result[0]['use_store_id']]];
        }

        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('reason_application'), 'value' => $result[0]['reason']];
        //驳回
        if ($statusUnion == 3) {
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('reject_reason'), 'value' => $result[0]['reject_reason']];
        }

        $type = strpos($result[0]['wf_role'], "asp_") !== false ? enums::$audit_type['ASP'] : enums::$audit_type['AS'];

        //获取网点名
        $storeInfo = (new \FlashExpress\bi\App\Server\SysStoreServer())->getStoreName([$result[0]['organization_id']]);
        $data = [
            'title'       => $type == enums::$audit_type['AS'] ? $this->getTranslation()->_('abort_list_myasset') : $this->getTranslation()->_('abort_list_pubasset'),
            'origin_id'   => $result[0]['id'],
            'id'          => $result[0]['wf_role'],
            'staff_id'    => $result[0]['staff_info_id'],
            'type'        => $type,
            'created_at'  => $result[0]['created_at'],
            'updated_at'  => $result[0]['updated_at'],
            'status'      => $statusUnion,
            'status_text' => (new AuditlistRepository($this->lang, $this->timezone))->getAuditStatus( "10" . $result[0]['status']),
            'serial_no'   => $result[0]['order_union_id'],
            'store_id'    => $result[0]['organization_id'],
            'store_name'  => $storeInfo[$result[0]['organization_id']] ?? '',
        ];
        $returnData['data']['head'] = $data;

        $params     = [];
        $params['orderSn'] = $result[0]['order_union_id'];
        $params['lang'] = $this->lang;
        $result = (new BaseRepository())->getDataFromWms(env("api_wms_url") . "/open/getOutboundTrackingInfo", $params);
        $tracking = '';
        $this->getDI()->get("logger")->write_log("getOutboundTrackingInfo " . json_encode($result, JSON_UNESCAPED_UNICODE), "info");
        if ($result && $result['code'] == 1 && isset($result['data']) && $result['data'] && isset($result['data']['status'])) {
            if ($result['data']['status'] == 'Wait for approval') {
                $tracking = $this->getTranslation()->_("shipment_be_delivered");
            } else if ($result['data']['status'] == 'Outbounded') {
                $tracking = $this->getTranslation()->_("shipment_be_received") . ' (' . $this->getTranslation()->_("shipment_number") . '：' . $result['data']['expressSn'] . ")";
            }
        }
        $returnData['data']['detail'][] = ["key" => $this->getTranslation()->_("shipment_status"), "value" => $tracking];

        return $returnData;

    }

    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        // TODO: Implement setProperty() method.
        if ($isFinal) {

            list($isSucc, $msg) = $this->updateAssetAuditStatus(self::$paramsIn, self::$paramsUserInfo, $isFinal);
            if (!$isSucc) {

                return false;
            }
        }
        return true;
    }

    public function genSummary(int $auditId, $user)
    {
        // TODO: Implement genSummary() method.
        $server = new \FlashExpress\bi\App\Server\AuditListServer($this->lang, $this->timezone);
        return $server->generateSummary($auditId, 16);
    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $assets = AssetsOrderModel::findFirst([
            'conditions' => ' id = :id:',
            'bind' => ['id' => $auditId]
        ])->toArray();

        $userInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id:',
            'bind' => ['staff_id' => $assets['staff_info_id']]
        ]);
        if(!empty($userInfo)){
            $userInfo = $userInfo->toArray();
            $department_info = SysDepartmentModel::findFirst($userInfo['node_department_id']);
            $ancestry = empty($department_info) ? '' : $department_info->ancestry_v3;
            $staff_info = (new StaffRepository())->getStaffPositionv3($assets['staff_info_id']);
            $level = 0;
            if (in_array($department_info->type, [2, 3]) && $department_info->level == 1) {
                $level = 1 ;
            }
            return [
                'category'=>$staff_info['category'],
                'job_title' => $userInfo['job_title'],
                'node_department_id' => $userInfo['node_department_id'],
                'sys_store_id' => $userInfo['sys_store_id'],
                'ancestry' => $ancestry,
                'staff_info_id' => $assets['staff_info_id'],
                'level'         => $level,//部门等级
            ];
        }
        //没找到申请人信息 返回空
        return [
                'category' => 0,
                'job_title' => 0,
                'node_department_id' => 0,
                'sys_store_id' => 0,
                'ancestry' => '',
                'staff_info_id' => 0,
                'level'         => 0,

        ];

    }


    /**
     * [userInfo 通过id获取货物信息]
     * @param string
     * @return
     */
    public function goodsInfo($ids)
    {
        if (is_array($ids)) {
            $barCodeIn = implode("','", $ids);
            $sql       = "select * from assets_goods where id in ('{$barCodeIn}') ";
            $db        = $this->getDI()->get('db_rby')->query($sql);
            $infoArr   = $db->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        } else {
            $sql     = "select * from assets_goods where id = '{$ids}' ";
            $db      = $this->getDI()->get('db_rby')->query($sql);
            $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        }
        return $infoArr;
    }


    /**
     * 订单审核
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkAssetOrder($paramIn = [], $userInfo)
    {
        $wms_server = new SyncWarehoseServer($this->lang,$this->timezone);
        $wms_conf = $wms_server->getWmsCnf(false);
        $order_id = str_replace(["asp_", "as_"], "", $paramIn['audit_id']);
        //获取商品订单信息
        $orderData = $this->wms->getAssetOrder($order_id);
        $orderDetailData = $this->wms->getAssetOrderDetail($order_id);
        $staffInfo = (new StaffServer())->getStaffById($orderData['staff_info_id']);

        $param['mchId'] = env('wms_mchId');
        $param['nonceStr'] = time();
        $param['lang'] = $this->lang;//语言
        $param['warehouseId'] = $wms_conf['warehouseId'];//发货仓库ID
        $param['orderSn'] = $orderData['order_union_id'];//外部订单编号
        //14771需求，先判断pc_code是否有值，有值传递pc_code无值传递organization_id
        $param['nodeSn'] = !empty($orderData['pc_code']) ? $orderData['pc_code'] : (isset($orderData['organization_id']) ? $orderData['organization_id'] : ''); //网点ID
        $param['status'] = '1';//状态 [1]待审核
        $param['type'] = '1';//出库类型 [1]普通出库
        $param['consigneeName'] = $orderData['shipping_user'];//收货人
        $param['consigneePhone'] = $staffInfo && isset($staffInfo['mobile']) ? $staffInfo['mobile'] : '';
        $param['postalCode'] = $orderData['postal_code'];
        $param['consigneeAddress'] = $orderData['consignee_address'];
        $param['goodsStatus'] = 'normal';
        /**
         * 2022-07-27邮件需求
         * 1、 当最后一个节点审批通过后，调用SCM创建出库通知单的接口
         * 2、 查询申请人所在的信息：
         *      1）当申请人为总部员工时，传输该员工的所属部门的名称
         *      2）当申请人为非总部员工时，传输该员工的所在网点的名称
         * 3、BY上的申请理由，拼接到备注信息上传输给SCM（此处逻辑不变）
         */
        $organization_name = '';
        if ($staffInfo['sys_store_id'] == -1) {
            if (!empty($staffInfo['node_department_id'])) {
                $department_info =  SysDepartmentModel::findFirst([
                    'conditions' => 'id = :department_id:',
                    'bind' => [
                        'department_id' => $staffInfo['node_department_id'],
                    ]
                ]);
                $organization_name = !empty($department_info) ? $department_info->name : $organization_name;
            }
        } else {
            $store_info = SysStoreModel::findFirst([
                'conditions' => 'id = :store_id:',
                'bind'       => ['store_id' => $staffInfo['sys_store_id']]
            ]);
            $organization_name = !empty($store_info) ? $store_info->name : $organization_name;
        }
        $param['remark'] = $organization_name;

        //附加原因
        $param['remark'].=";".$orderData['reason'];
        $param['remark'] = trim($param['remark'],";");
        $param['channelSource'] = "backyard";
        /**
         * 12257【BY|TH】HUB与ASSET资产与耗材审批流变更P1 逻辑修改
         * https://l8bx01gcjr.feishu.cn/docs/doccneIwW7NFOD4MxPT51vDgU7b#
         * 在向wms同步库存的时候需要获取总部或网点的最新省、市、区、邮编信息
         */
        list($province, $city, $district, $postal_code) = $this->wms->getOrderNewAddressInfo($orderData, 'assets_order');
        // 省市区名称
        $param['province'] = isset($province['name']) ? $province['name'] : '';
        $param['city'] = isset($city['name']) ? $city['name'] : '';
        $param['district'] = isset($district['name']) ? $district['name'] : '';
        //记录省、市、区code用于更新订单信息
        $param['province_code'] = isset($province['code']) ? $province['code'] : '';
        $param['city_code'] = isset($city['code']) ? $city['code'] : '';
        $param['district_code'] = isset($district['code']) ? $district['code'] : '';

        $param['postalCode'] = $postal_code ?? $param['postalCode'];
        $param['goods'] = array();
        if (!empty($orderDetailData)) {
            $i = 0;
            foreach ($orderDetailData as $k => $v) {
                if ($v['approval_num'] <= 0) {
                    continue;
                }
                $param['goods'][$k]['i'] = ++$i;
                $param['goods'][$k]['barCode'] = $v['bar_code'];
                $param['goods'][$k]['goodsName'] = $v['goods_name_th'];
                $param['goods'][$k]['specification'] = '';
                $param['goods'][$k]['num'] = $v['approval_num'];
                $param['goods'][$k]['price'] = '';
                $goodsRemark = $this->wms->getUserByids($orderData['staff_info_id']);
                $param['goods'][$k]['remark'] = $goodsRemark;//json_encode($goodsRemark); //商品备注是使用者工号，姓名，职位
            }
        }
        $param['goods'] = !empty($param['goods']) ? json_encode($param['goods']) : [];
        return $param;
    }


    /**
     * [userInfo 通过id获取待接收资产数量]
     * type = 0 个人资产 1 公共资产
     * @param string
     * @return
     */
    public function getTransferNum($staff_id, $sourceType = null)
    {
        $sql = "select count(1) as count from assets_info ai left join assets_goods ag on ai.assets_goods_id = ag.id where ai.transfer_staff_id = {$staff_id} and ai.operate_status in (2,3)";
        if ($sourceType && $sourceType == self::SOURCE_TYPE_PUBLIC_ASSETS) {
            $sql .= " AND ag.is_public = 1";
        } else {
            $sql .= " AND ag.is_public = 0";
        }
        $db = $this->getDI()->get('db_rby')->query($sql);
        $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $infoArr['count'] ?? 0;
    }

    /**
     * [userInfo 通过id获取资产ID]
     * @param string
     * @return
     */
    public function getAssetsByStaff($options)
    {
        $staff_id = $options['user_id'];
        $id = $options['id'];
        $sql = "--
        select
            ai.id,
            ag.is_public
        from
            assets_info ai
        left join
            assets_goods ag
        on
            ai.assets_goods_id = ag.id
        where
            ai.id = {$id} and ai.staff_info_id = {$staff_id} and ai.operate_status = 1";
        $infoArr = $this->getDI()->get('db_rby')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $infoArr;
    }


    public function updateBatchAssetsTransfer($options)
    {
        $sql = "--
            SELECT
                ai.id
            FROM
                assets_info ai
            LEFT JOIN
                assets_goods ag
            ON
                ai.assets_goods_id = ag.id
            WHERE
                ai.staff_info_id = :staff_info_id AND ag.id = :goods_id AND ai.operate_status = 1 AND ag.is_public = 1 AND ag.is_batch = 1
        ";
        $pdo = $this->getDI()->get('db')->prepare($sql);
        $pdo->bindValue(":staff_info_id", $options['user_id']);
        $pdo->bindValue(":goods_id", $options['goods_id']);
        $pdo->execute();
        $assetIds = $pdo->fetchAll(\PDO::FETCH_ASSOC);
        $assetIds = array_column($assetIds, 'id');
        if (count($assetIds) < $options['nums']) {
            return false;
        }

        $pdo = $this->getDI()->get('db');
        try {
            $pdo->begin();
            $assetIds = array_slice($assetIds, 0, count($assetIds));
            $pdo->query("select id from assets_info where id in (" . implode(", ", $assetIds) .") for update ")->fetchAll(\PDO::FETCH_ASSOC); // 排他锁
            for ($i = 0; $i < $options['nums']; $i++) {
                $time = gmdate('Y-m-d H:i:s');
                $sql = "--
                        update
                                assets_info
                        set
                                transfer_staff_id={$options['staff_id']},
                        transfer_type={$options['type']},
                                pno='{$options['pno']}',
                                transfer_at = '{$time}',
                                operate_status = 3,
                                transfer_state = 2
                         where
                                id = {$assetIds[$i]} ";
                $result = $this->getDI()->get('db')->execute($sql);
                if ($result) {
                    $this->insertAssetsInfoLog([
                        'assets_id'=>$assetIds[$i],
                        'staff_info_id'=>$options['user_id'],
                        'transfer_staff_id'=>$options['staff_id'],
                        'transfer_type'=>$options['type'],
                        'pno'=> $options['type'] == 2 ? $options['pno'] : '',
                        'transfer_state' => 2,
                        'created_at' => gmdate('Y-m-d H:i:s',time()),
                    ]);
                }
            }
        } catch (\Exception $e) {
            $pdo->rollBack();

            return false;
        }
        $pdo->commit();
        return true;
    }


    /**
     * [userInfo 更新资产转交状态]
     * @param string
     * @return
     */
    public function updaeAssetsTransfer($options)
    {
        $time = gmdate('Y-m-d H:i:s');
        $sql = "--
        update
            assets_info
        set
            transfer_staff_id={$options['staff_id']},
            transfer_type={$options['type']},
            pno='{$options['pno']}',
            transfer_at = '{$time}',
            operate_status = {$options['operate_status']},
            transfer_state = 2
        where
            id = {$options['id']} ";
        $result = $this->getDI()->get('db')->execute($sql);

        if($result){
            $this->insertAssetsInfoLog([
                'assets_id'=>$options['id'],
                'staff_info_id'=>$options['user_id'],
                'transfer_staff_id'=>$options['staff_id'],
                'transfer_type'=>$options['type'],
                'pno'=> $options['type'] == 2 ? $options['pno'] : '',
                'transfer_state' => 2,
                'created_at' => gmdate('Y-m-d H:i:s',time()),
            ]);
            //当转交给总部的时候向  资产操作日志表 插入一天转交记录
            if ($options['staff_id'] == -1){
                $insetSql = $this->other->getInsertDbSql('assets_operate_log', [
                    'assets_info_id'=>$options['id'],
                    'staff_info_id'=>$options['user_id'],
                    'assets_goods_id'=>$options['assets_goods_id'],
                    'operator_id'=>$options['user_id'],
                    'operate_time'=> gmdate('Y-m-d H:i:s',time()),
                    'operate_status'=> 1,
                    'remarks' => '',
                    'created_at' => $time,
                ]);
                $this->getDI()->get('db')->execute($insetSql);
            }



        }
        return $result;
    }

    /**
     * 待接收资产 撤销转交
     * @param $options
     */
    public function updateAssetsTransferRevoke($asset_info,$staff_info_id,$remark) {

        $log_sql = "--
            select * from assets_info_log where assets_id = {$asset_info['id']} and transfer_state = 1 order by created_at desc limit 1";

        $log_info = $this->getDI()->get('db')->query($log_sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        $transfer_status = 0;
        $transfer_at = null ;
        if(!empty($log_info)){
            $transfer_status = 1;
            $transfer_at = $log_info['created_at'];
        }

        $sql = "--
        update
            assets_info
        set
            transfer_staff_id={$staff_info_id},
            operate_status = 1,
            transfer_state = {$transfer_status},
            transfer_at =
            ";
        if ($transfer_at == null){
            $sql .= 'NUll';
        }else{
            $sql .= "'".$transfer_at."'";
        }
        $sql .="
        where
            id = {$asset_info['id']}
            AND staff_info_id = {$staff_info_id}";

        $result = $this->getDI()->get('db')->execute($sql);
        if($result){
            $this->insertAssetsInfoLog([
                'assets_id'=>$asset_info['id'],
                'staff_info_id'=>$staff_info_id,
                'transfer_staff_id'=>$asset_info['transfer_staff_id'],
                'transfer_type'=>$asset_info['transfer_type'],
                'pno'=> $asset_info['transfer_type'] == 2 ? $asset_info['pno'] : '',
                'transfer_state' => 3,
                'created_at' => gmdate('Y-m-d H:i:s',time()),
                'remark'=>$remark
            ]);
        }
        return $result;
    }

    /**
     * 返回员工assets_info待接收资产详情
     * @param $staff_info_id
     * @param $asset_id
     * @return mixed
     */
    public function getTransferAssetInfobyStaffInfoId($staff_info_id, $asset_id) {
        $sql = "
        --
        select
            *
        from
            assets_info
        where
            id = {$asset_id} and staff_info_id = {$staff_info_id} and transfer_state = 2 and operate_status in(3,9,11)  ;
        ";
        $asset = $this->getDI()->get("db")->query($sql)->fetch(\PDO::FETCH_ASSOC);
        return $asset;
    }

    public function transferBatchConfirm($options)
    {

        $sql = "--
                SELECT
                    ai.id,
                    ag.id as goods_id,
                    ag.bar_code
                FROM
                    assets_info ai
                left join
                    assets_goods ag
                ON
                    ai.assets_goods_id = ag.id
                WHERE
                    ai.transfer_staff_id = {$options['staff_id']} AND ag.id = {$options['goods_id']} AND ai.staff_info_id = {$options['transfer_id']} AND ai.operate_status in (2,3) AND ag.is_public = 1 AND ag.is_batch = 1";
        $assets_list = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $assetsIds = array_column($assets_list, 'id');
        foreach ($assetsIds as $assetsId) {
            $this->transferConfirm([
                'id' => $assetsId,
                'staff_id' => $options['staff_id'],
                'revoke' => $options['revoke'],
                'reason' =>isset($options['reason']) ? $options['reason'] : ''
            ]);
        }

        return true;
    }

    /**
     * [userInfo 资产接收]
     * @param string
     * @return
     */
    public function transferConfirm($options)
    {
        $db = $this->getDI()->get('db');
        $sql = "--
            select * from assets_info where id = {$options['id']}";
        $line = $db->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (empty($line)) {
            return false;
        }

        $time = gmdate("Y-m-d H:i:s");
        //接收
        $sql = "--
        update
            assets_info
        set
            transfer_state=1,
            staff_info_id={$options['staff_id']},
            transfer_at='{$time}',
            operate_status = 1
        where
            id = {$options['id']} and operate_status in (2,3)";
        //撤销
        if($options['revoke'] == 1){
            $sql = "--
            select * from assets_info_log where id = {$options['id']} and transfer_state = 1";
            $log_info = $db->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
            $transfer_status = 0;
            $transfer_at = 'transfer_at = null';
            if(!empty($log_info)){
                $transfer_status = 1;
                $transfer_at = "transfer_at = '{$line['transfer_at']}'" ;
            }
            $sql = "--
            update
                assets_info
            set
                operate_status = 1,
                transfer_state = {$transfer_status},
                {$transfer_at}
            where
                id = {$options['id']} and operate_status in (2,3)";
        }

        $result = $db->execute($sql);
        if ($result) {
                $this->insertAssetsInfoLog([
                    'assets_id' => $options['id'],
                    'staff_info_id' => $line['staff_info_id'],
                    'transfer_staff_id' => $line['transfer_staff_id'],
                    'transfer_type' => $line['transfer_type'],
                    'pno' => $line['transfer_type'] == 2 ? $line['pno'] : '',
                    'transfer_state' => $options['revoke'] == 1 ? 3 : 1,
                    'created_at' => $time,
                    'remark' => isset($options['reason']) ? $options['reason'] : '',
                ]);
        }
        return $result;
    }

    public function getBatchTransferInfo($staffInfoId)
    {
        $lang = substr(strtolower($this->lang), 0, 2);
        $lang = empty($lang) ? 'zh' : $lang;
        //没有语言 默认英文
        $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);
        $sql = "--
                SELECT
                    ag.id,
                    ag.bar_code,
                    ag.goods_name_{$lang} as goods_name,
                    ai.staff_info_id,
                    count(*) as count
                FROM
                    assets_info ai
                left join
                    assets_goods ag
                ON
                    ai.assets_goods_id = ag.id
                WHERE
                    ai.transfer_staff_id = {$staffInfoId} AND ai.operate_status in (2,3) AND ag.is_public = 1 AND ag.is_batch = 1 GROUP BY ag.id, ai.staff_info_id ";

        $assets_list = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $list = [];
        foreach ($assets_list as $item) {
            $list[] = [
                'goods_id' => $item['id'],
                'goods_name' => $item['goods_name'],
                'bar_code' => $item['bar_code'],
                'transfer_id' => $item['staff_info_id'],
                'nums' => $item['count'],
            ];
        }

        return $list;
    }


    /**
     * 待接收资产列表
     * @param $staff_info_id 员工ID（员工表staff_info表ID）
     * @return array
     */
    public function getTransferInfo($staff_info_id, $sourceType = null)
    {

        $lang = substr(strtolower($this->lang), 0, 2);
        $lang = empty($lang) ? 'zh' : $lang;
        //没有语言 默认英文
        $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);
        $sql = "--
                SELECT
                    ai.id,
                    ai.express,
                    ai.express_sn,
                    ai.staff_info_id,
                    ai.handover_type,
                    CONVERT_TZ( ai.out_time, '+00:00', '{$this->timezone}' ) AS out_time,
                    ag.bar_code,
                    ag.goods_name_{$lang} as goods_name,
                    ai.asset_code,
                    CONVERT_TZ( ai.transfer_at, '+00:00', '{$this->timezone}' ) AS transfer_at,
                    ai.transfer_state,
                    ai.transfer_type,
                    ai.sn_code,
                    ai.pno
                FROM
                    assets_info ai
                left join
                    assets_goods ag
                ON
                    ai.assets_goods_id = ag.id
                WHERE
                    ai.transfer_staff_id = {$staff_info_id} AND ai.operate_status in (2,3)";
        if ($sourceType && $sourceType == self::SOURCE_TYPE_PUBLIC_ASSETS) {
            $sql .= " AND ag.is_public = 1 AND ag.is_batch = 0 ";
        } else {
            $sql .= " AND ag.is_public = 0 " ;
        }

        $assets_list = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $list = [];
        if (empty($assets_list)) {
            return $list;
        }

        $_t = $this->getTranslation();

        foreach ($assets_list as $key => $value) {

            $staff_item = $this->staff->getStaffPosition($value['staff_info_id']);
            $store_name = $staff_item['depart_name'];

            if ($staff_item['sys_store_id'] != '-1') {
                $store_info = $this->staff->getStaffStoreInfo($staff_item['sys_store_id']);
                $store_name = isset($store_info['name']) ? $store_info['name'] : '';
            }

            $item = [
                'id' => $value['id'],
                //'bar_code' => empty($value['asset_code']) ? $value['bar_code'] : $value['asset_code'],
                'bar_code' => $value['bar_code'],
                'goods_name' => $value['goods_name'],
                'transfer_type' => $value['transfer_type'] == 1 ? $this->getTranslation()->_('express_type_face') : $this->getTranslation()->_('handover_type'),
                'pno' => $value['transfer_type'] == 1 ? '' : $value['pno'],
                'staff_id' => $value['staff_info_id'],
                'asset_code' => $value['asset_code'],
                'sn' => $value['sn_code'],
                'staff_name' => $staff_item['name'],
                'store_name' => $store_name,
            ];

            $list[] = $item;
        }

        return $list;

    }


    /**
     * [检查运单]
     * @param string
     * @return
     */
    public function checkParcelPno($pno)
    {
        if(empty($pno)){
            return false;
        }
        $sql = "--
        select
            pno
        from
            parcel_info
        where
            pno = '{$pno}' limit 1";
        $result = $this->getDI()->get('db_fle')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        if(!empty($result)){
            return true;
        }
        return false;
    }


    /**
     * [资产转移写入log]
     * @param string
     * @return
     */
    public function insertAssetsInfoLog($options)
    {
        $insetSql = $this->other->getInsertDbSql('assets_info_log', $options);
        return $this->getDI()->get('db')->execute($insetSql);
    }

    /**
     * 附件上传
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function uploadAnnex($paramIn = [])
    {
        $staffId  = $this->processingDefault($paramIn, 'staff_id', 2);
        $file_url = $this->processingDefault($paramIn, 'file_url', 1);
        if (!$file_url) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4188'));
        }
        $data = $this->asset->uploadAnnex([
            "id" => $staffId,
            "file_url" => $file_url,
        ]);
        $return['data'] = $data;
        if ($data) {
            $this->asset->addAssetsHandoer([
                "staff_id" => $staffId
            ]);
            return $this->checkReturn($return);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4188'));
        }
    }

    /**
     * 附件列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getAnnexList($paramIn = []){
        $id  = $this->processingDefault($paramIn, 'staff_id', 2);
        $fileType  = $this->processingDefault($paramIn, 'file_type', 2);

        if (!$id) {
            return [];
        }
        $data = $this->asset->getAnnexList([
            "id" => $id,
            "file_type" => $fileType,
        ]);
        return $data;
    }

    /**
     * @param array $paramIn
     * @return array
     * 获取winhr 签名 照片
     */
    public function getWinhrProve($paramIn = []){
        $id  = $this->processingDefault($paramIn, 'staff_id', 2);
        $fileType  = $this->processingDefault($paramIn, 'file_type', 2);

        if (!$id) {
            return [];
        }
        $data = $this->contract->winhrApprove([
            "staff_id" => $id,
        ]);
        return $data;
    }
    public function getAssetsHandoer($paramIn = []){
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $state = $this->processingDefault($paramIn, 'state', 2);
        if (!$staffId){
            return [];
        }
        $data = $this->asset->getAssetsHandoer([
            "staff_id" => $staffId,
            "state" => $state,
        ]);
        return $data;
    }

    public function getAssetsHanderByStaffId($staffId)
    {
        if (!$staffId) {
            return [];
        }
        return $this->asset->getAssetsHandoer([
            'staff_id' => $staffId
        ]);
    }

    public function getTransferStatusList($paramIn){
        $ids = [self::OPERATE_STATUS_REPAIR_WAIT_PULL,self::OPERATE_STATUS_LEAVE_WAIT_PULL];
        //如果是网点正主管
        $job_title = $paramIn['job_title'];
        if($job_title==16){
            $ids = array_merge($ids,[self::OPERATE_STATUS_MORE_WAIT_PULL,self::OPERATE_STATUS_LEAVE_WAIT_PULL]);
        }
        $t = $this->getTranslation();
        $ids = array_unique($ids);
        $data = [];
        foreach ($ids as $id){
            $temp = [];
            $temp['id'] = $id;
            $temp['name'] = $t['operate_status_'.$id];

            $data[] = $temp;
        }
        return $this->checkReturn(["data"=>$data]);
    }

    /**
     *
     * 获取待接收资产详情
     *
     * @param $assetId
     *
     *
     */
    public function getTransferAsset($assetId)
    {
        $sql = "
        --
        select
            assets_goods.goods_name_th,
            assets_goods.goods_name_en,
            assets_goods.goods_name_zh,
            assets_goods.bar_code,
            assets_info.asset_code,
            assets_info.sn_code,
            assets_info.transfer_staff_id,
            assets_info.transfer_state,
            assets_info.transfer_type,
            assets_info.pno,
            assets_info.operate_status,
            ar.consignee_address,
            CONVERT_TZ(assets_info.transfer_at, '+00:00', '{$this->timezone}') as transfer_at
        from
            assets_info
            left join assets_goods on assets_info.assets_goods_id = assets_goods.id
            left join assets_repair as ar on ar.bar_code =  assets_info.bar_code and ar.type = assets_info.operate_status
        where
            assets_info.id = {$assetId} and ( (assets_info.transfer_state = 2 and assets_info.operate_status in(3,9,11)) or (assets_info.transfer_state in(0,1) and assets_info.operate_status = 1))
        ";

        $asset = $this->getDI()->get("db")->query($sql)->fetch(\PDO::FETCH_ASSOC);

        $result = [];
        if ($asset) {
            $remark = '';
            //当查看的资产operate_status=1时说明是想查看被总部拒绝的资产（未接收状态）
            if ($asset['operate_status'] == 1){
                $assets_info_log_data = $this->isHeadquartersRefuse($assetId);
                 if($assets_info_log_data){
                     $remark = $assets_info_log_data['remark'];
                     $asset['transfer_staff_id'] = $assets_info_log_data['transfer_staff_id'];
                     $assets_repair_data = $this->getOperateStatus($assetId);
                     if ($assets_repair_data){
                         $sql = "select consignee_address from assets_repair where  bar_code = '".$asset['bar_code']."'". " and  type = ".$assets_repair_data['operate_status'];
                         $consignee_address = $this->getDI()->get("db")->query($sql)->fetch(\PDO::FETCH_ASSOC);
                         $asset['consignee_address'] = $consignee_address['consignee_address'] ?? '';
                     }

                 }else{
                     return $result;
                 }
            }
            if (!empty($asset['transfer_staff_id']) && $asset['transfer_staff_id'] != -1){
            $staff = $this->getDI()->get("db_rby")->query("
            --
            select
                *
            from
                hr_staff_info
            where
                staff_info_id = {$asset['transfer_staff_id']} ")->fetch(\PDO::FETCH_ASSOC);
            }


            $result = [
                "name" => $asset['goods_name_' . strtolower(str_replace("-CN", "", $this->lang ? $this->lang : 'zh-CN'))],
                "asset_code" => $asset['asset_code'],
                "sn_code" => $asset['sn_code'],
                "transfer_id" => $asset['transfer_staff_id'],
                "transfer_name" => $staff['name'] ?? '',
                "transfer_mobile" => $staff['mobile'] ?? '',
                "transfer_at" => $asset['transfer_at'],
                "bar_code"=> $asset['bar_code'],
                "pno" => $asset['pno'],
                "consignee_address" => $asset['consignee_address'] ?? '',//只有查询转交给总部的资产才可能有值
                "transfer_type" => $asset['transfer_type'] == 1 ? $this->getTranslation()->_('express_type_face') : $this->getTranslation()->_('handover_type'),
                "transfer_type_num" => $asset['transfer_type'] ,
                'remark'=>$remark,//查询转交给总部且被总部拒绝时的拒绝原因
            ];
        }
        return $result;
    }

    /**
     *
     * 资产价目表
     *
     * @param $staffId
     */
    public function assetsMoney($staffId)
    {
        $sql = "
        --
        select * from hr_staff_info where staff_info_id = :staff_info_id ";
        $biConnetions = $this->getDI()->get("db_rby");
        $staffInfo = $biConnetions->fetchOne($sql, Db::FETCH_ASSOC, [
            "staff_info_id" => $staffId
        ], [
            "staff_info_id" => Column::BIND_PARAM_INT
        ]);

        $sql = "
        --
        select
            bar_code,
            is_public,
            goods_name_zh,
            goods_name_en,
            goods_name_th
        from
            assets_goods
        where find_in_set(bar_code, :bar_codes) and is_public = :is_public and deleted = 0 group by bar_code;
        ";
        $backConnection = $this->getDI()->get("db");

        //个人资产
        $assets = $backConnection->fetchAll($sql, Db::FETCH_ASSOC, [
            "bar_codes" => implode(",", array_keys(self::ASSETS_PERSONAL_MAP)),
            "is_public" => 0,
        ], [
            "bar_codes" => Column::BIND_PARAM_STR,
            "is_public" => Column::BIND_PARAM_INT
        ]);
        if ($staffInfo && in_array($staffInfo['job_title'], [
                enums::$job_title['branch_supervisor'],
                enums::$job_title['shop_supervisor'],
            ])) {

            $assets = array_merge($backConnection->fetchAll($sql, Db::FETCH_ASSOC, [
                "bar_codes" => implode(",", array_keys(self::ASSETS_PRICE_MAP)),
                "is_public" => 1
            ], [
                "bar_codes" => Column::BIND_PARAM_STR,
                "is_public" => Column::BIND_PARAM_INT
            ]), $assets);

        }

        $results = [
            "public_goods" => [],
            "personal_goods" => [],
        ];
        foreach ($assets as $asset) {
            if ($asset['is_public']) {
                $results['public_goods'][] = [
                    "bar_code" => $asset['bar_code'],
                    "name" => $asset['goods_name_' . str_replace("-CN", "", $this->lang)],
                    "price" => self::ASSETS_PRICE_MAP[$asset['bar_code']]
                ];
            } else {
                $results['personal_goods'][] = [
                    "bar_code" => $asset['bar_code'],
                    "name" => $asset['goods_name_' . str_replace("-CN", "", $this->lang)],
                    "price" => self::ASSETS_PERSONAL_MAP[$asset['bar_code']]
                ];

            }
        }

        return $results;
    }

    public function assetsByStaffId($staffId)
    {

        $lang = substr(strtolower($this->lang), 0, 2);
        $lang = empty($lang) ? 'zh' : $lang;
        //没有语言 默认英文
        $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);
        $sql = "--
                SELECT
                    ai.id,
                    ai.express,
                    ai.express_sn,
                    ai.handover_type,
                    CONVERT_TZ( ai.out_time, '+00:00', '{$this->timezone}' ) AS out_time,
                    ag.bar_code,
                    ag.goods_name_{$lang} as goods_name,
                    ag.goods_name_en,
                    ag.goods_name_zh,
                    ag.goods_name_th,
                    ag.value_th,
                    ag.is_public,
                    ai.assets_goods_id,
                    ai.asset_code,
                    CONVERT_TZ( ai.transfer_at, '+00:00', '{$this->timezone}' ) AS transfer_at,
                    ai.transfer_state,
                    ai.transfer_type,
                    ai.staff_info_id,
                    ai.sn_code,
                    ai.pno,
                    ai.operate_status
                FROM
                    assets_info ai
                left join
                    assets_goods ag
                ON
                    ai.assets_goods_id = ag.id
                WHERE
                    ai.staff_info_id = '{$staffId}' AND ai.state=1";

            $sql .= " AND ag.is_public in(0, 1 )AND ag.is_batch = 0 ";

        $batchsql = "--
                SELECT
                    ai.id,
                    ai.express,
                    ai.express_sn,
                    ai.handover_type,
                    CONVERT_TZ( ai.out_time, '+00:00', '{$this->timezone}' ) AS out_time,
                    ag.bar_code,
                    ag.goods_name_{$lang} as goods_name,
                    ag.goods_name_en,
                    ag.goods_name_zh,
                    ag.goods_name_th,
                    ag.value_th,
                    ag.is_public,
                    ai.assets_goods_id,
                    ai.asset_code,
                    CONVERT_TZ( ai.transfer_at, '+00:00', '{$this->timezone}' ) AS transfer_at,
                    ai.transfer_state,
                    ai.transfer_type,
                    ai.staff_info_id,
                    ai.sn_code,
                    ai.pno,
                    ai.operate_status,
                    count(*) as count
                FROM
                    assets_info ai
                left join
                    assets_goods ag
                ON
                    ai.assets_goods_id = ag.id
                WHERE
                    ai.staff_info_id = '{$staffId}' AND ai.state=1";

        $batchsql .= " AND ag.is_public =1 AND ag.is_batch = 1 group by ai.assets_goods_id ";
        $batch_assets = $this->getDI()->get('db')->query($batchsql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $assets = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $results = [
            "public_goods" => [],
            "personal_goods" => [],
            "batch_goods" => [],
        ];
        if($batch_assets){
            foreach ($batch_assets as $item){
                $results['batch_goods'][] = [
                    "bar_code" => $item['bar_code'],
                    "name" => $item['goods_name_' . str_replace("-CN", "", $this->lang)],
                    "asset_code" => $item['asset_code'],
                    "sn_code" => $item['sn_code'],
                    "price" => $item['value_th'],
                    "num" => $item['count'],
                ];
            }
        }

        foreach ($assets as $asset) {
            if ($asset['is_public']) {
                $results['public_goods'][] = [
                    "bar_code" => $asset['bar_code'],
                    "name" => $asset['goods_name_' . str_replace("-CN", "", $this->lang)],
                    "asset_code" => $asset['asset_code'],
                    "sn_code" => $asset['sn_code'],
                    "price" => $asset['value_th'],
                    "num" => 1,
                ];
            } else {
                $results['personal_goods'][] = [
                    "bar_code" => $asset['bar_code'],
                    "name" => $asset['goods_name_' . str_replace("-CN", "", $this->lang)],
                    "asset_code" => $asset['asset_code'],
                    "sn_code" => $asset['sn_code'],
                    "price" => $asset['value_th'],
                    "num" => 1
                ];

            }
        }
        return $results;

    }

    /**
     *
     * @param $envKey
     */
    public function getSetEnv($envKey)
    {
        $rBi = $this->getDI()->get('db_rbi');

        $sql = "--
        select * from setting_env where code = :code ";
        $result = $rBi->fetchOne($sql, Db::FETCH_ASSOC, [
            'code' => $envKey
        ]);

        return $result && isset($result['set_val']) ? explode(",", $result['set_val']) : [];
    }

    /**
     *
     * 扣除薪金、工资与其他收入赔偿损失的同意书
     *
     * @param $userId
     */
    public function salaryDeductionPdf($userId)
    {

        $rpcClient = new ApiClient('hr_rpc', '', 'staff_view');
        $rpcClient->setParams([
            'staff_info_id' => $userId
        ]);
        $resultData = $rpcClient->execute();

        $objectUrl = '';
        if (isset($resultData['result'])
            && isset($resultData['result']['code'])
            && $resultData['result']['code'] == 0
            && isset($resultData['result']['body'])
        ) {
            $staffInfo = $resultData['result']['body'];

            $annex = HrAnnexModel::findFirst([
                'conditions' => ' oss_bucket_key = :staff_id: and file_type = :type: and deleted = 0',
                'bind' => [
                    'staff_id' => $userId,
                    'type' => 15,
                ]
            ]);
            $signImg = '';
            if ($annex) {
                $annex = $annex->toArray();
                $img_prefix = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
                $signImg = $img_prefix . $annex['object_key'];
            }

            $assets = $this->getMyAssets($userId);


            $pdfData['date'] = date('Ymd');
            $pdfData['name'] = $staffInfo['name'];
            $pdfData['identity'] = $staffInfo['identity'];
            $pdfData['job_title_name'] = $staffInfo['job_title_name'];
            $pdfData['staff_info_id'] = $staffInfo['staff_info_id'];
            $pdfData['node_department_name'] = $staffInfo['node_department_name'];
            $pdfData['sys_store_name'] = $staffInfo['sys_store_name'];

            $pdfData['register_district_name'] = $staffInfo['register_district_name'];
            $pdfData['residence_district_name'] = $staffInfo['residence_district_name'];

            $pdfData['register_city_name'] = $staffInfo['register_city_name'];
            $pdfData['residence_city_name'] = $staffInfo['residence_city_name'];

            $pdfData['register_province_name'] = $staffInfo['register_province_name'];
            $pdfData['residence_province_name'] = $staffInfo['residence_province_name'];

            $pdfData['residence_postcodes'] = $staffInfo['residence_postcodes'];
            $pdfData['residence_street'] = $staffInfo['residence_street'];

            $pdfData['goods_name_ens'] = implode(',', array_unique(array_column($assets, 'goods_name_en')));
            $pdfData['goods_name_zhs'] = implode(',', array_unique(array_column($assets, 'goods_name_zh')));

            $pdfData['sign_img'] = '';
            if ($signImg) {
                $pdfData['sign_img'] = "<img style='width: 80px;60px' src='" . $signImg . "' >";
            }

            $view = new \Phalcon\Mvc\View();
            $path = APP_PATH . '/views';
            $view->setViewsDir($path);
            foreach ($pdfData as $key => $value){
                $view->setVar($key, $value);
            }
            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT      => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );
            $view->render("layouts", "assets_salary_deduction");
            $view->finish();
            $content = $view->getContent();

            $filePath = BASE_PATH . "/public/tmp/"; // 生成的目录
//            $filePath = "/tmp/"; // 生成的目录
            $fileName = "salary_deduction_" . $userId . ".pdf";

            if ($content) {
                $mpdf = new Mpdf([
                    'mode' => 'zh-CN',
                ]);
                $mpdf->useAdobeCJK = true;
                $mpdf->autoScriptToLang = true;
                $mpdf->autoLangToFont   = true;
                $mpdf->SetDisplayMode('fullpage');
                $mpdf->WriteHTML($content);
                $mpdf->Output($filePath . $fileName ,\Mpdf\Output\Destination::FILE);

                //上传oss 更新表
                $result = $this->uploadFileOss($filePath . $fileName, self::OSS_DIR_MAPS[self::HRIS_STAFF_PROFILE]);

                $objectUrl = $result['object_url'];
            }

        }

        return $objectUrl;

    }


    /**
     * DESC: 获取总部可接受的资产邮寄地址
     * @param $bar_code
     * @param $type
     * @return array
     */
    public function assetsRepairAddress($bar_code,$type){
        if (empty($bar_code) || empty($type)){
            return [];
        }
        $backConnection  =  $this->getDI()->get('db');
        $sql = 'select id , consignee_address  from assets_repair where bar_code = :bar_code and type = :type and is_receive = 1 ';
        $data = $backConnection->fetchOne($sql,Db::FETCH_ASSOC,[
            'bar_code'=>$bar_code,
            'type'=>$type,
        ]);
        return $data ?: [];
    }


    /**
     * DESC:获取总部拒绝转交数量
     * @param $sourceType
     * @param $staffId
     * @return int
     */
    public function getRefuseNum($sourceType,$staffId)
    {
        $backConnection = $this->getDI()->get('db');
        // transfer_state 之所以要包含1 （已转移） 是因为 当我转交给总部，总部拒收，但是我的这个资产在 assets_info_log 表中有 transfer_state = 1 已交接状态的数据，那么 assets_info.transfer_state会被修改成 1 。
        $sql1 = 'select  ai.id  from assets_info as ai
                left join assets_goods as ag on ai.assets_goods_id = ag.id
                where ai.staff_info_id = :staff_info_id and ai.`state` = 1  and  ai.`transfer_state` in(0,1) and ai.operate_status = 1 ';

        if ($sourceType && $sourceType == self::SOURCE_TYPE_PUBLIC_ASSETS) {
            $sql1 .= " AND ag.is_public = 1";
        } else {
            $sql1 .= " AND ag.is_public = 0";
        }

        $assets_info_data = $backConnection->fetchALl($sql1, Db::FETCH_ASSOC, [
            'staff_info_id' => $staffId
        ]);

        if(!$assets_info_data){
            return 0;
        }
        $assets_ids = implode(',',array_column((array)$assets_info_data,'id'));

        //查询每个资产的最后一条日志记录
        $sql2 = " select max(id) as id from  assets_info_log  where assets_id in ({$assets_ids})  group by assets_id   ";
        $assets_info_log_max_ids = $backConnection->fetchALl($sql2, Db::FETCH_ASSOC);
        if (!$assets_info_log_max_ids){
            return 0;
        }
        $assets_log_ids = implode(',',array_column((array)$assets_info_log_max_ids,'id'));
        $sql3 = "select  count(1) as total from  assets_info_log  where id in ({$assets_log_ids})  and  transfer_state = 4";
        $data = $backConnection->fetchOne($sql3, Db::FETCH_ASSOC);
        return $data ?: 0;
    }

    /**
     * DESC: 该资产最近一条日志是否是被总部拒绝的日志
     * @param $assets_id
     * @return false
     */
    public function isHeadquartersRefuse($assets_id){
        $assets_info_log_sql = 'select transfer_state,remark,transfer_staff_id from assets_info_log where assets_id = '.intval($assets_id) .' order by id desc limit 1';
        $data = $this->getDI()->get("db")->fetchOne($assets_info_log_sql, Db::FETCH_ASSOC);
        if (isset($data['transfer_state']) && $data['transfer_staff_id'] == -1  && $data['transfer_state'] == 4){
            return $data;
        }
        return false;
    }

    /**
     * DESC: 获取用户转交给总部被拒绝后，当时转交状态是什么
     * @param $assets_id
     * @return false
     */
    public function getOperateStatus($assets_id){
        $assets_info_log_sql = 'select operate_status,staff_info_id from assets_operate_log where assets_info_id = '.intval($assets_id) .' order by id desc limit 1';
        $data = $this->getDI()->get("db")->fetchOne($assets_info_log_sql, Db::FETCH_ASSOC);
        if ( $data['staff_info_id'] == -1 ){
            return $data;
        }
        return false;
    }

}
