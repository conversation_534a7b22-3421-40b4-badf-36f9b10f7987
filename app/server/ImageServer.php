<?php
/**
 *ImageServer.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/8/25 0025 19:27
 */

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Server\BaseServer;

class ImageServer extends BaseServer
{
    public static function addWaterTxt(string $alyOssImgPath, $loginUser)
    {
//        $waterTxt    = 'และทำ';
        $waterTxt = $staffid ?? '10000';
        //1. 读取远程文件，创建画布
        ob_start();
        $isRead  = readfile($alyOssImgPath);
        $imgCode = ob_get_contents();
        if (!$isRead) {
            return ['msg' => '读取文件失败'];
        }
        list($width, $height, $type, $attr) = getimagesize($alyOssImgPath);
        $image = imagecreatefromstring($imgCode);
        //2. 在画布中绘制图像
        $bai = imagecolorallocate($image, 220, 220, 220);
        //使用指定的字体文件绘制文字
        if (preg_match('/[\x{4e00}-\x{9fa5}]+/u', $waterTxt, $m)) {
            //中文
            $fonts = BASE_PATH . '/public/fonts/msyh.ttc';
        } else {
            //泰语
            $fonts = BASE_PATH . '/public/fonts/THSarabun.ttf';
        }
        // 平铺水印
        //文字水印的宽度
        $waterW = 80;
        // 文字水印的高度
        $waterH = 80;
        // 倾斜角度
        $angle = 45;
        //水印文字大小
        $fontSize = 16;
        for ($x = 10; $x < $width; $x) {
            for ($y = 20; $y < $height; $y) {
                imagettftext($image, $fontSize, $angle, $x, $y, $bai, $fonts, $waterTxt);
                $y += $waterH;
            }
            $x += $waterW;
        }
        //在浏览器直接输出图像资源
        ob_clean();
        header("Content-Type:image/png");
        imagepng($image);
        //销毁图像资源
        imagedestroy($image);
        ob_end_flush();
        return true;
    }

    public static function addWaterTxtToOSS($content, $waterTxt, $element = 'img')
    {
//        $waterTxt = 'flash '. $waterTxt;
        if($element == 'href'){
            $pattern = "/<[A|a].*?href=[\'|\"](.*?(?:[\.gif|\.jpg|\.png|\.jpeg]))[\'|\"].*?[\/]?>/i";
        } else {
            $pattern = "/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png|\.jpeg]))[\'|\"].*?[\/]?>/i";
        }

        $content = preg_replace_callback($pattern, function ($ma) use ($waterTxt) {
            $newUrl = self::_oss($ma[1], $waterTxt);
            return str_replace($ma[1], $newUrl, $ma[0]);
        }, $content);

        return $content;
    }

    public static function _oss($alyOssImgPath, $waterTxt)
    {
        $suffix = '';
        if (preg_match('/oss-/', $alyOssImgPath)) {
            $waterTxt = base64_encode($waterTxt);
            $waterTxt = str_replace(['+', '/'], '_', $waterTxt);
            $waterTxt = rtrim($waterTxt, '=');
            $color    = '000000';
            $t        = '10';       // 	[0,100] 默认值：100， 表示透明度100%（不透明）
            $size     = 16;
            $rotate   = 325;           //[0,360] 默认值：0，表示不旋转
            $fill     = 1;            // 1：表示将文字水印铺满原, 0（默认值）：表示不将文字水印铺满全图。
            $order    = 0;            // 0（默认值）：表示图片水印在前：1：表示文字水印在前。
            $suffix   = "?x-oss-process=image/watermark,text_{$waterTxt},color_{$color},t_{$t},size_{$size},rotate_{$rotate},fill_{$fill},order_{$order}";
        }
        return $alyOssImgPath . $suffix;
    }

}