<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/29/23
 * Time: 8:32 PM
 */

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\ActivityEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\Models\backyard\ActivityClickLogModel;
use FlashExpress\bi\App\Models\backyard\ActivityRecordModel;
use FlashExpress\bi\App\Models\backyard\AttendanceWhiteListModel;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use Exception;

class ActivityServer extends BaseServer
{

    public    $activityDate;//活动日期
    public    $today;//当天日期
    public    $staffInfo;
    public    $imgServer;
    protected $activityCode;
    //默认返回字段
    public $defaultResult = [
        'activity_name' => '',
        'state'         => ActivityEnums::STATE_NOT_ACTIVITY,
        'image_url'     => '',
        'msg_id'        => '',
        'data'          => null,
    ];


    public function initData($param)
    {
        $this->defaultResult['activity_name'] = $this->activityCode;
        $staffId                              = $param['staff_info_id'];
        $staffRe                              = new StaffRepository($this->lang);
        $this->staffInfo                      = $staffRe->getStaffPosition($staffId);
        $this->today                          = date('Y-m-d');
    }

    //获取周年活动信息
    public function activityInfo($param)
    {
        $this->initData($param);
        return $this->info($param);
    }

    public function getActivities($param)
    {
        //马来 开斋节
        if(isCountry('MY')){
            $server = new FastingServer($this->lang, $this->timeZone);
            $return[]       = $server->activityInfo($param);
        }

        //泰国 宋干节
        if(isCountry('TH')){
            $server = new SongganServer($this->lang, $this->timeZone);
            $return[]       = $server->activityInfo($param);
        }

        //生日快乐 活动弹窗
        $birthdayServer = new BirthdayServer($this->lang, $this->timeZone);
        $return[]       = $birthdayServer->activityInfo($param);

        //周年祝福
        $anniversaryServer = new AnniversaryServer($this->lang, $this->timeZone);
        $return[]          = $anniversaryServer->activityInfo($param);

        // 海报
        if (isCountry('MY')){
            $posterPushServer = new PosterPushServer($this->lang, $this->timeZone);
            $posterPushList          = $posterPushServer->activityList($param);
            $return = array_merge($return, $posterPushList);
        }
        return $return;
    }


    //获取 生日 或者 满周年活动 方法
    public function info($param)
    {
        $staffId = $param['staff_info_id'];
        //加缓存
        $key = "{$staffId}_{$this->defaultResult['activity_name']}_new";
        $redis = $this->getDI()->get('redis');
        $info = $redis->get($key);
//        if(!empty($info)){
//            return json_decode($info, true);
//        }
        //没日期
        if (empty($this->activityDate)) {
            return $this->defaultResult;
        }

        //还没到
        if (strtotime($this->activityDate) > strtotime($this->today)) {
            $this->saveCache($staffId, $redis, $this->defaultResult);
            return $this->defaultResult;
        }

        //查询 发送记录 如果没有 说明没到
        $activity = ActivityRecordModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and activity_date = :date_at: and code = :code:',
            'bind'       => [
                'staff_id' => $staffId,
                'date_at'  => $this->activityDate,
                'code'     => $this->defaultResult['activity_name'],
            ],
        ]);
        if (empty($activity)) {
            $this->saveCache($staffId, $redis, $this->defaultResult);
            return $this->defaultResult;
        }

        $this->defaultResult['image_url'] = $activity->img_path;
        $this->defaultResult['msg_id']    = $activity->message_id;
        //已经弹过了
        if ($activity->state == ActivityEnums::STATE_HAD) {
            $this->defaultResult['state'] = $activity->state;
            $this->saveCache($staffId, $redis, $this->defaultResult);
            return $this->defaultResult;
        }

        //还没弹
        if ($activity->state == ActivityRecordModel::ALERT_YET) {
            //已经过期 失效了 改状态
            $limit = date('Y-m-d', strtotime('-5 day'));
            if (strtotime($this->activityDate) < strtotime($limit)) {
                $this->saveCache($staffId, $redis, $this->defaultResult);
                return $this->defaultResult;
            }

            //判断白名单 白名单用户 直接弹窗 最后确定 用当前日期 获取白名单
            if ((new WhiteListServer())->isInfWhiteList($staffId, $this->today)) {
                $this->defaultResult['state'] = ActivityEnums::STATE_IS_ACTIVITY;
                $this->updateActivity($activity);
                return $this->defaultResult;
            }
            //非白名单 要判断 打没打完上班卡 如果打了 弹窗
            if ($this->isAttendanceStart($param)) {
                $this->defaultResult['state'] = ActivityEnums::STATE_IS_ACTIVITY;
                $this->updateActivity($activity);
                return $this->defaultResult;
            }
            //没打卡 延时弹窗
            $this->defaultResult['state'] = ActivityEnums::STATE_DELAY_ACTIVITY;
            return $this->defaultResult;
        }
        return $this->defaultResult;
    }




    //非白名单用户 要看打没打 当前时间考勤日的上班卡 如果没打 不弹窗
    public function isAttendanceStart($param)
    {
        //需要考勤日期 马来 不一样 注意！
        $server = new AttendanceServer($this->lang, $this->timeZone);
        $server = Tools::reBuildCountryInstance($server, [$this->lang, $this->timeZone]);
        if (isCountry('MY')) {
            $info = $server->newAttendanceInfo($param);
            //没按钮数据 不用打卡的用户 直接弹
            if (empty($info['button_list'])) {
                return true;
            }
            //马来有两个 上班卡 看用哪个
            foreach ($info['button_list'] as $r) {
                if ($r['shift_index'] == enums::ATTENDANCE_SHIFT_INDEX_1 && !empty($r['time_stamp'])) {
                    return true;
                }
            }
        } else {
            $info = $server->attendance_info($param);
            if (!empty($info['started_at'])) {
                return true;
            }
        }
        //没上班卡 返回 false
        return false;
    }


    //活动弹出后 回写状态标记已经参与过活动
    public function updateActivity($activity)
    {
        $activity->state = ActivityRecordModel::ALERT_HAD;
        $activity->update();
    }


    //已读消息统计
    public function readIncr($activityName, $createTime = '')
    {
        $month = date('Y-m');
        $info  = ActivityClickLogModel::findFirst([
            'conditions' => 'code = :name: and month = :month:',
            'bind'       => ['name' => $activityName, 'month' => $month],
        ]);

        if (empty($info)) {
            //新增
            $row['code']     = $activityName;
            $row['month']    = $month;
            $row['read_num'] = 1;
            $model           = new ActivityClickLogModel();
            $model->create($row);
            return true;
        }

        //修改
        $info->read_num = $info->read_num + 1;
        $info->update();
        return true;
    }

    //点击首页 下载 或者分享 计数
    public function clickIncr($param)
    {
        //$activityName,$type
        $activityName = $param['activity_name'];
        $type         = (int)$param['type'];
        $month        = date('Y-m');
        $info         = ActivityClickLogModel::findFirst([
            'conditions' => 'code = :name: and month = :month:',
            'bind'       => [
                'name'  => $activityName,
                'month' => $month,
            ],
        ]);

        $columnName = ActivityEnums::$clickType[$type] ?? '';
        if (empty($columnName)) {
            $this->logger->write_log("clickIncr wrong type ".json_encode($param));
            return true;
        }
        if (empty($info)) {
            $model = new ActivityClickLogModel();
            //新增一条记录
            $insert['code']      = $activityName;
            $insert['month']     = $month;
            $insert[$columnName] = 1;
            $model->create($insert);
            return true;
        }
        //update
        $info->$columnName = $info->$columnName + 1;
        $info->update();
        return true;
    }


    //获取消息信息 前端 h5 消息 frame 用
    public function getMsgInfo($params)
    {
        //获取消息详情
        $server = new BackyardServer($this->lang, $this->timeZone);
        $res    = $server->msg_detail($params);

        if ($res['code'] != 1) {
            throw new ValidationException($res['msg']);
        }

        $data = $res['data'];
        if (empty($data)) {
            throw new ValidationException('no message info');
        }

        $res = [];
        //员工生日消息
        if ($data['category'] == MessageEnums::MESSAGE_CATEGORY_CODE_BIRTHDAY) {
            $res['activity_name'] = ActivityRecordModel::CODE_BIRTHDAY;
        }
        //员工满周年消息
        if ($data["category"] == MessageEnums::MESSAGE_CATEGORY_CODE_ANNIVERSARY) {
            $res['activity_name'] = ActivityRecordModel::CODE_ANNIVERSARY;
        }

        //马来过年
        if ($data["category"] == MessageEnums::MESSAGE_CATEGORY_CODE_FASTING) {
            $res['activity_name'] = ActivityRecordModel::CODE_FASTING;
        }

        //泰国新年
        if ($data["category"] == MessageEnums::MESSAGE_CATEGORY_CODE_SONGGAN) {
            $res['activity_name'] = ActivityRecordModel::CODE_SONGGAN;
        }

        $res['title'] = $data['title'];

        //获取公告图片url
        $actInfo              = ActivityRecordModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and code = :name: and message_id = :msg_id:',
            'bind'       => [
                'staff_id' => $params['staff_id'],
                'name'     => $res['activity_name'],
                'msg_id'   => $params['msg_id'],
            ],
        ]);
        $res['image_url']     = $actInfo->img_path ?? '';
        $res['share_url']    = $actInfo->share_path ?? '';
        $res['staff_info_id'] = $params['staff_id'];
        return $res;
    }


    //公共方法 发送消息
    public function sendBirthOrAnniversary($staffData, $type)
    {
        //周年活动 获取登录语言包 对应翻译
        $langArr     = [];
        $msgCategory = -1;
        $staff_ids    = array_column($staffData, 'staff_info_id');
        if ($type == ActivityRecordModel::CODE_ANNIVERSARY) {
            $hc_re        = new HcRepository($this->timeZone);
            $staffAccount = $hc_re->getStaffAcceptLang($staff_ids);
            foreach ($staffAccount as $account) {
                $langArr[$account['staff_info_id']] = strtolower(substr($account['accept_language'], 0, 2)) ?: 'en';
            }
            $msgCategory = MessageEnums::MESSAGE_CATEGORY_CODE_ANNIVERSARY;
        }

        if ($type == ActivityRecordModel::CODE_BIRTHDAY) {
            $msgCategory = MessageEnums::MESSAGE_CATEGORY_CODE_BIRTHDAY;
        }
        $today = date('Y-m-d');
        //查询当天是否已经发送过
        $existData = ActivityRecordModel::find([
            'conditions' => 'staff_info_id in ({ids:array}) and activity_date = :date_at: and code = :code:',
            'bind' => ['ids' => $staff_ids, 'date_at' => $today, 'code' => $type]
        ])->toArray();
        $existData = empty($existData) ? [] : array_column($existData, 'staff_info_id');


        //图片服务 生成图片url 入库用
        $imgServer = new FormImgServer();
        $actModel  = new ActivityRecordModel();
        $sendNum = 0;
        foreach ($staffData as $staff) {
            $staffId   = $staff['staff_info_id'];
            //已经发过了 不发了
            if(in_array($staffId, $existData)){
                continue;
            }
            $title_key = '';
            //发消息
            $locale = $staff['locale'] = empty($langArr[$staffId]) ? $this->lang : $langArr[$staffId];
            //获取图片 java 图片服务
            $activityImgUrl = $this->getImgByParam($imgServer, $staff);
            if(empty($activityImgUrl)){
                $this->logger->write_log("getImgByParam {$staffId} 图片空 ", 'info');
                continue;
            }

            //获取title
            if ($type == ActivityRecordModel::CODE_BIRTHDAY) {
                $title_key = 'birthday_title';
            }
            if ($type == ActivityRecordModel::CODE_ANNIVERSARY) {
                $title_key = 'anniversary_title';
            }

            $id                          = time().$staffId.rand(1000000, 9999999);
            $param['staff_users']        = [$staffId];//数组 多个员工id
            $param['message_title']      = $this->getTranslation($locale)->_($title_key, ['name' => $staff['name']]);
            $param['message_content']    = 'blessing?msg_id=' . $id;
            $param['staff_info_ids_str'] = $staffId;
            $param['id']                 = $id;
            $param['category']           = $msgCategory;

            $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', $locale));
            $bi_rpc->setParams($param);
            $res = $bi_rpc->execute();
            $this->logger->write_log("sendBirthOrAnniversary {$staffId} ".json_encode($res), 'info');

            if (!empty($res['result']) && $res['result']['code'] == 1) {
                //写活动主表 activity_record
                $insert['staff_info_id'] = $staffId;
                $insert['code']          = $type;
                $insert['cycle']         = $this->getCycle($staff['activity_date'], $type);
                $insert['activity_date'] = $today;//发送消息 活动日期
                $insert['img_path']      = $activityImgUrl;
                $insert['message_id']    = $id;
                $clone                   = clone $actModel;
                $clone->create($insert);
            }
            $sendNum++;
        }
        //记录 发送数量 活动点击统计表
        $this->saveCount($sendNum, $type);
    }

    //发送节日 开斋节 宋干节 等 图片模板 根据员工语言环境的
    public function sendFestival($staffData, $type)
    {
        //周年活动 获取登录语言包 对应翻译
        $langArr     = [];
        $msgCategory = -1;
        $staff_ids   = array_column($staffData, 'staff_info_id');
        if ($type == ActivityRecordModel::CODE_FASTING) {
            $msgCategory = MessageEnums::MESSAGE_CATEGORY_CODE_FASTING;
        }

        if ($type == ActivityRecordModel::CODE_SONGGAN) {
            $msgCategory = MessageEnums::MESSAGE_CATEGORY_CODE_SONGGAN;
        }
        $today = date('Y-m-d');
        //查询当天是否已经发送过
        $existData = ActivityRecordModel::find([
            'conditions' => 'staff_info_id in ({ids:array}) and activity_date = :date_at: and code = :code:',
            'bind'       => ['ids' => $staff_ids, 'date_at' => $today, 'code' => $type],
        ])->toArray();
        $existData = empty($existData) ? [] : array_column($existData, 'staff_info_id');
        //登陆最后语言环境
        $hc_re        = new HcRepository($this->timeZone);
        $staffAccount = $hc_re->getStaffAcceptLang($staff_ids);
        foreach ($staffAccount as $account) {
            $langArr[$account['staff_info_id']] = strtolower(substr($account['accept_language'], 0, 2)) ?: 'en';
        }

        //获取图片 主图片和 分享图片 map list
        $imgData = (new SettingEnvServer())->getLikeVal($type);
        //默认图片 key
        $defaultKey   = $type.'_default';
        $shareDefault = "{$type}_share_default";

        $actModel = new ActivityRecordModel();
        $sendNum  = 0;
        foreach ($staffData as $staff) {
            $staffId = $staff['staff_info_id'];
            //已经发过了 不发了
            if (in_array($staffId, $existData)) {
                continue;
            }
            $title_key = '';
            //发消息
            $locale = $staff['locale'] = empty($langArr[$staffId]) ? $this->lang : $langArr[$staffId];
            //马来 语言环境有ms
            if($locale == 'ms'){
                $locale = $staff['locale'] = 'my';
            }
            $k = "{$type}_{$locale}";
            $activityImgUrl = $imgData[$k] ?? $imgData[$defaultKey] ?? '';

            if (empty($activityImgUrl)) {
                $this->logger->write_log("getImgByParam {$staffId} 图片空 ", 'info');
                continue;
            }
            $shareK = "{$type}_share_{$locale}";
            $shareUrl = $imgData[$shareK] ?? $imgData[$shareDefault] ?? '';

            //获取title
            if ($type == ActivityRecordModel::CODE_FASTING) {
                $title_key = 'fasting_title';
            }
            if ($type == ActivityRecordModel::CODE_SONGGAN) {
                $title_key = 'songgan_title';
            }
            $title = $this->getTranslation($locale)->_($title_key);
            //马来 语标题定制
            if($type == ActivityRecordModel::CODE_FASTING && $locale == 'my'){
                $title = 'Selamat Hari Raya Aidilfitri';
            }

            $id                          = time().$staffId.rand(1000000, 9999999);
            $param['staff_users']        = [$staffId];//数组 多个员工id
            $param['message_title']      = $title;
            $param['message_content']    = 'blessing?msg_id='.$id;
            $param['staff_info_ids_str'] = $staffId;
            $param['id']                 = $id;
            $param['category']           = $msgCategory;

            $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', $locale));
            $bi_rpc->setParams($param);
            $res = $bi_rpc->execute();
            $this->logger->write_log("sendFestival {$staffId} ".json_encode($res), 'info');

            if (!empty($res['result']) && $res['result']['code'] == 1) {
                //写活动主表 activity_record
                $insert['staff_info_id'] = $staffId;
                $insert['code']          = $type;
                $insert['cycle']         = date('Y');//就24年有
                $insert['activity_date'] = $today;//发送消息 活动日期
                $insert['img_path']      = $activityImgUrl;
                $insert['share_path']    = $shareUrl;
                $insert['message_id']    = $id;
                $clone                   = clone $actModel;
                $clone->create($insert);
                $sendNum++;
            }
        }
        //记录 发送数量 活动点击统计表
        $this->saveCount($sendNum, $type);
    }


    //获取 对应语言环境的 图片地址
    public function getFestivalUrl($type){
        $code = "{$type}";
        $data =
        $url = (new SettingEnvServer())->getLikeVal($code);
        if(empty($url)){
            $code = "{$type}_default";
            $url = (new SettingEnvServer())->getSetVal($code);
        }
        return $url;

    }

    //获取 对应语言环境的 分享图片地址
    public function getFestivalShareUrl($type, $locale = ''){
        $code = "share_{$type}";
        $url = (new SettingEnvServer())->getSetVal($code);
        if(empty($url)){
            $code = "{$type}_default";
            $url = (new SettingEnvServer())->getSetVal($code);
        }
        return $url;
    }


    public function saveCount($num, $type)
    {
        $month = date('Y-m');
        //先查询有没有这个月的记录
        $info = ActivityClickLogModel::findFirst([
            'conditions' => 'code = :name: and month = :month:',
            'bind'       => [
                'name'  => $type,
                'month' => $month,
            ],
        ]);

        //新增
        if (empty($info)) {
            $insert['code']     = $type;
            $insert['month']    = $month;
            $insert['send_num'] = $num;
            $model              = new ActivityClickLogModel();
            $model->create($insert);
            return true;
        }

        //更新
        $info->send_num += $num;
        $info->update();
        return true;
    }

    //调用图片服务获取 图片oss 地址
    public function getImgByParam(FormImgServer $imgServer, $staff)
    {
        //子类 的方法
        $p = $this->getImgParam($staff);

        //对应语言环境的字体不同
        $lang                     = empty($staff['locale']) ? $this->lang : $staff['locale'];
        $lang                     = substr(strtolower($lang), 0, 2);
        $p['data']['font_family'] = ActivityEnums::$fontFamily[$lang] ?? '';
        //配置项
        $p['imageOptions']['fullPage'] = true;//截全部

        //接口异常 不影响下一个
        try{
            $activityImg = $imgServer->htmlToImg($p);
        }catch (Exception $e){
            $this->logger->write_log("htmlToImg 生成图片失败 " . $e->getTraceAsString());
            return '';
        }
        $url         = empty($activityImg) ? '' : $activityImg['object_url'];
//        echo $url;exit;
        return $url;
    }


    //根据环境获取模板地址
    public function getTplPath($file_path)
    {
        // 上传OSS
        $upload_result = OssHelper::uploadFile($file_path, true);
        return $upload_result['object_url'];
    }

    //获取背景图
    public function getBgImg($code)
    {
        return (new SettingEnvServer())->getSetVal($code);
    }

    //根据活动类型 获取对应活动周期
    public function getCycle($date, $type)
    {
        if ($type == ActivityRecordModel::CODE_BIRTHDAY) {
            return (int)date('Y', strtotime($date));
        }

        if ($type == ActivityRecordModel::CODE_ANNIVERSARY) {
            $currentYear = date('Y');
            $year        = date('Y', strtotime($date));
            return $currentYear - $year;
        }

        return 0;
    }

    //活动加缓存 测试环境先不加
    public function saveCache($staffId, $redis, $result){
        $current = date('Y-m-d H:i:s');
        $cacheTime = strtotime(date('Y-m-d 23:59:59')) - strtotime($current);
        $key = "{$staffId}_{$this->defaultResult['activity_name']}_new";
        $redis->save($key, json_encode($result),$cacheTime);
    }


}