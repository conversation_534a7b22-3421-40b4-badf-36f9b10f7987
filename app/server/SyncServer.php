<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/1/7
 * Time: 2:35 PM
 */


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Repository\BusinesstripRepository;

class SyncServer extends BaseServer{
    //1-考勤 2-请假 3-出差 4-OT 5-轮休
    const ATTENDANCE = 1;
    const LEAVE = 2;
    const BUSINESS = 3;
    const OVERTIME = 4;
    const WORKDAY = 5;

    /**
     * 同步数据
     * @param $type
     * @param $data
     * @return array 整理好的数据结构
     */
    public function save_data($type,$data)
    {
        try{
            $in_all = count($data);
            if($type == self::ATTENDANCE)
                $in_all = count($data['data']);
            $this->logger->write_log("sync_statistic {$type} 同步监控日志总数 {$in_all}",'info');
            if(empty($data))
                return [];

            $flag = 0;
            $ab_staff = [];
            //考勤同步
            if($type == self::ATTENDANCE){
                list($flag, $ab_staff) = $this->save_att($data);
            }
            //同步请假
            if($type == self::LEAVE){
                $flag = $this->save_leave($data);
            }
            //出差
            if($type == self::BUSINESS){
                $flag = $this->save_bus($data);
            }
            //加班
            if($type == self::OVERTIME) {
                $flag = $this->save_ot($data);
            }

            if($type == self::WORKDAY){
                $insert = $this->check_work_day($data);
                if(!empty($insert))
                    $flag = $this->getDI()->get('db')->insertAsDict(
                        'hr_staff_work_days', $insert
                    );
            }

            if($flag != $in_all)
                $this->logger->write_log("sync_statistic {$type} 部分失败: {$in_all}/{$flag}",'info');
            return ['total'=>$in_all,'success'=>$flag,'ab_staff'=>$ab_staff];
        }catch (\Exception $e){
            $this->logger->write_log("sync 同步 {$type} 失败 ".$e->getTraceAsString());
            return ['total'=>0,'success'=>0,'ab_staff'=>[]];
        }

    }


    //验证

    public function save_att($data){
        //查询 考勤信息
        $att_date = $data['attendance_date'];
        $data = $data['data'];
        $staff_ids = array_column($data,'staff_info_id');
        //员工信息
        $staff_info = $this->get_staff_info($staff_ids);
        $num = 0;
        $ab_staff = [];
        foreach ($data as $k => $da){
            if(empty($staff_info[$da['staff_info_id']])){
                $this->logger->write_log("sync att 没有该员工数据 {$da['staff_info_id']} ",'info');
                continue;
            }
            if ($staff_info[$da['staff_info_id']]['state'] != '1'){
                $ab_staff[] = $da['staff_info_id'];
            }
            $conditions = "staff_info_id =:staff_id: and attendance_date = :attendance_date:";
            $bind           = ["staff_id" => $da['staff_info_id'],'attendance_date' => $att_date];
            $att = StaffWorkAttendance::findFirst([
                'conditions' => $conditions,
                'bind'       => $bind,
            ]);
            if (!$att){
                $att = new StaffWorkAttendance();
            }

            if(!empty($da['started_at']))
                $da['started_remark'] = '(sync)'.$data[$k]['started_remark'];
            if(!empty($da['end_at']))
                $da['end_remark'] = '(sync)'.$data[$k]['end_remark'];
            //1 网点 2 总部
            $data[$k]['organization_type'] = $staff_info[$da['staff_info_id']]['sys_store_id'] == '-1' ? 2 : 1;
            $data[$k]['organization_id'] = $staff_info[$da['staff_info_id']]['sys_store_id'] == '-1' ?
                $staff_info[$da['staff_info_id']]['sys_department_id'] : $staff_info[$da['staff_info_id']]['sys_store_id'];

            $flag = $att->save($da);
            if ($flag){
                $num++;
            }
            $this->logger->write_log("sync_att_staff {$da['staff_info_id']} {$flag}",'info');
        }
        return [$num,$ab_staff];
    }


    public function save_leave($data){
        $staff_ids = array_column($data,'staff_info_id');
        //员工信息
        $staff_info = $this->get_staff_info($staff_ids);

        $audit_model = new StaffAuditModel();
        $split_model = new StaffAuditLeaveSplitModel();
        $work_day_model = new HrStaffWorkDayModel();
        $num = 0;
        foreach ($data as $da){
            if(empty($staff_info[$da['staff_info_id']])){
                $this->logger->write_log("sync leave 没有该员工数据 {$da['staff_info_id']} ",'info');
                continue;
            }

            $start_time = $da['start_type'] == 1 ? "{$da['start_date']} 09:00:00" :  "{$da['start_date']} 13:00:00";
            $end_time = $da['end_type'] == 1 ? "{$da['end_date']} 12:00:00" :  "{$da['end_date']} 18:00:00";

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['s' => StaffAuditModel::class]);
            $builder->where('s.serial_no = :serial_no: and s.audit_type = 2 and s.staff_info_id = :staff_info_id: ',
                            [
                                'serial_no' => $da['serial_no'] . 'SYNC',
                                'staff_info_id' => $da['staff_info_id']
                            ]);
            $count = $builder->getQuery()->execute()->count();

            if($count>0){
                $num++;
                $this->logger->write_log("sync leave 存在请假记录 {$da['staff_info_id']}" . json_encode($da),'info');
                continue;
            }

            $db = StaffAuditModel::beginTransaction($this);
            try {
                //轮休日类型的请假  先同时写
                if($da['leave_type'] == 15){
                    $work_day_model_clone = clone $work_day_model;
                    $info = $work_day_model::findFirst([
                                                           'conditions' => "staff_info_id = :staff_info_id: and date_at = :date_at:",
                                                           'bind' => [
                                                               'staff_info_id'  => $da['staff_info_id'],
                                                               'date_at'  => $da['start_date'],
                                                           ]
                                                       ]);
                    if(empty($info)){
                        //轮休表
                        $w_d_item = [];
                        $w_d_item['staff_info_id'] = $da['staff_info_id'];
                        $w_d_item['month'] = date('Y-m',strtotime($da['start_date']));
                        $w_d_item['date_at'] = $da['start_date'];
                        $w_d_item['operator'] = 10000;
                        $w_d_item['remark'] = '(sync)';
                        if ($work_day_model_clone->create($w_d_item)) {
                            $num++;
                        }
                    } else {
                        // 存在 视为成功
                        $num++;
                    }
                }else{
                    //请假表
                    $row['serial_no'] = isset($da['serial_no']) && $da['serial_no'] ? $da['serial_no'] . 'SYNC' : '';
                    $row['staff_info_id'] = $da['staff_info_id'];
                    $row['audit_type'] = 2;
                    $row['leave_type'] = $da['leave_type'];
                    $row['leave_start_time'] = $start_time;
                    $row['leave_start_type'] = $da['start_type'];
                    $row['leave_end_time'] = $end_time;
                    $row['leave_end_type'] = $da['end_type'];
                    $row['leave_day'] = $da['leave_day'];
                    $row['status'] = 2;
                    $row['audit_reason'] = '(sync)' . $da['audit_reason'];

                    $clone_m = clone $audit_model;
                    $flag = $clone_m->create($row);
                    if($flag){
                        $audit_id = $clone_m->audit_id;
                        //split 表
                        foreach ($da['split'] as $s){
                            $s['audit_id'] = $audit_id;
                            $clone_s = clone $split_model;
                            $clone_s->create($s);
                        }
                        $num++;
                    }
                }

                $db->commit();

            }catch (\Exception $e){
                $db->rollBack();
            }
            $this->logger->write_log("sync_leave_staff {$da['staff_info_id']}  data ".json_encode($da,JSON_UNESCAPED_UNICODE),'info');
        }
        return $num;
    }


    public function save_bus($data)
    {
        $staff_ids = array_column($data,'apply_user');
        //员工信息
        $staff_info = $this->get_staff_info($staff_ids);

//        $bus_re = new BusinesstripRepository($this->lang, $this->timeZone);
        $num = 0;
        foreach ($data as $k => $da){
            if(empty($staff_info[$da['apply_user']])){
                $this->logger->write_log("sync bus 没有该员工数据 {$da['apply_user']} ",'info');
                continue;
            }
            $exist = BusinessTripModel::findFirst([
                'conditions' => ' serial_no = :serial_no: and apply_user = :staff_id:',
                'bind' => ['serial_no' => $da['serial_no'] . 'SYNC', 'staff_id' => $da['staff_id']]
            ]);
            if(!empty($exist)){
                $num++;
                $this->logger->write_log("sync business 存在出差记录 {$da['apply_user']}" . json_encode($da),'info');
                continue;
            }
            $att_model = new BusinessTripModel();
            $da['serial_no'] = isset($da['serial_no']) && $da['serial_no'] ? $da['serial_no'] . 'SYNC' : '';
            $da['status'] = 2;
            $da['remark'] = '(sync)' . $da['remark'];
            $flag = $att_model->create($da);
            if ($flag){
                $num++;
            }
            $this->logger->write_log("sync_business_staff {$da['apply_user']} : {$flag}",'info');
        }
        return $num;
    }

    public function save_ot($data)
    {
        $staff_ids = array_column($data,'staff_id');
        //员工信息
        $staff_info = $this->get_staff_info($staff_ids);
        $model = new HrOvertimeModel();
        $num = 0;
        foreach ($data as $k => $da){
            if(empty($staff_info[$da['staff_id']])){
                $this->logger->write_log("sync ot 没有该员工数据 {$da['staff_id']} ",'info');
                continue;
            }

            $exist = $model->findFirst([
                'conditions' => ' serial_no = :serial_no: and staff_id = :staff_id:',
                'bind' => ['serial_no' => $da['serial_no'] . 'SYNC', 'staff_id' => $da['staff_id']]
            ]);
            if(!empty($exist)){
                if($da['duration'] != $exist->duration){
                    $exist->duration = $da['duration'];
                    $exist->end_time = $da['end_time'];
                    $exist->start_time = $da['start_time'];
                    $exist->save();
                    $this->logger->write_log("sync ot 更新加班申请 {$da['staff_id']} before duration ". $exist->duration ." after info ".json_encode($da,JSON_UNESCAPED_UNICODE),'info');
                }
                $num++;
                $this->logger->write_log("sync ot 已经存在加班申请 {$da['staff_id']} ".json_encode($da,JSON_UNESCAPED_UNICODE),'info');
                continue;
            }
            $da['serial_no'] = isset($da['serial_no']) && $da['serial_no'] ? $da['serial_no'] . 'SYNC' : '';
            $da['reason'] = '(sync) '.$da['reason'];
            $da['is_anticipate'] = 0;
            $da['state'] = 2;

            $att_model = new HrOvertimeModel();
            $flag = $att_model->create($da);
            if ($flag){
                $num++;
            }
            $this->logger->write_log("sync_ot_staff {$da['staff_id']} : {$flag}",'info');

        }
        return $num;
    }

    public function check_work_day($data){
        $staff_ids = array_column($data,'staff_info_id');
        //员工信息
        $staff_info = $this->get_staff_info($staff_ids);
        $model = new HrOvertimeModel();
        $insert = array();
    }

    //批量获取 员工信息
    protected function get_staff_info($staff_ids){
        if(empty($staff_ids))
            return array();
        //查询员工信息
        $conditions = "staff_info_id in ({staff_ids:array})";
        $bind           = ["staff_ids" => $staff_ids];
        $staff_info = HrStaffInfoModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns' => 'staff_info_id,sys_store_id,sys_department_id,state'
        ])->toArray();
        if(!empty($staff_info))
            $staff_info = array_column($staff_info,null,'staff_info_id');

        return $staff_info;
    }

    public function sync_status($params, $type)
    {
        $num = 0;
        if ($type == self::LEAVE) {
            foreach ($params as $param) {
                $staffAuditModel =
                StaffAuditModel::findFirst([
                    'conditions' => ' serial_no = :serial_no: ',
                    'bind' => ['serial_no' => $param['serial_no'] . 'SYNC']
                ]);
                if ($staffAuditModel) {
                    $staffAuditModel->status = $param['status'];
                    $staffAuditModel->save();
                }
                $num++;
                $this->logger->write_log("sync_leave_status {$param['serial_no']} {$param['status']}",'info');
            }
        } else if ($type == self::BUSINESS) {
            foreach ($params as $param) {
                $businessTripModel =
                BusinessTripModel::findFirst([
                    'conditions' => ' serial_no = :serial_no: ',
                    'bind' => ['serial_no' => $param['serial_no'] . 'SYNC']
                ]);
                if ($businessTripModel) {
                    $businessTripModel->status = $param['status'];
                    $businessTripModel->save();
                }
                $this->logger->write_log("sync_business_status {$param['serial_no']} {$param['status']}",'info');
                $num++;
            }
        } else if ($type == self::OVERTIME) {
            foreach ($params as $param) {
                $hrOvertimeModel =
                HrOvertimeModel::findFirst([
                    'conditions' => ' serial_no = :serial_no: ',
                    'bind' => ['serial_no' => $param['serial_no'] . 'SYNC']
                ]);
                if ($hrOvertimeModel) {
                    $hrOvertimeModel->state = $param['status'];
                    $hrOvertimeModel->save();
                }
                $num++;
                $this->logger->write_log("sync_overtime_status {$param['serial_no']} {$param['status']}",'info');
            }
        }

        return $num;
    }

    /**
     * 请假/补卡审批通过同步bi，重算处罚数据
     * https://flashexpress.feishu.cn/docx/Tcw8dzXraoWFqhxJvEIcIxqznxb
     * @param $params
     * @param $method
     * @return bool
     */
    public function sync_fbi_attendance($params, $method)
    {
        try {
            $bi_rpc = (new ApiClient('bi_rpcv2', '', $method, $this->lang));
            $bi_rpc->setParams($params);
            $bi_return = $bi_rpc->execute();
            $this->logger->write_log([
                'function' => 'sync_fbi_attendance',
                'message'  => '同步fbi请假补卡',
                'params'   => $params,
                'method'   => $method,
                'result'   => $bi_return,
            ], 'info');
        } catch (\Exception $e) {
            $this->logger->write_log([
                'function' => 'sync_fbi_attendance',
                'message'  => $e->getMessage(),
                'params'   => $params,
                'method'   => $method,
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
                'trace'    => $e->getTraceAsString(),
            ]);
            return false;
        }
        return true;
    }

}