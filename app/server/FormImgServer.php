<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\HttpCurl;
use phpDocumentor\Reflection\Types\True_;

class FormImgServer extends BaseServer
{
    public static $instance;

    public function __construct()
    {
        parent::__construct($this->lang, $this->timeZone);
    }

    /**
     * 获取实例
     * @param int $type
     * @return AiServer
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }
        return self::$instance;
    }


    /**
     * html 生成图片  https://dev01-th-pdf-svc.fex.pub/swagger-ui/index.html#/pdf-convert-controller/createPdfByJvppeteerUsingPOST
     * 新版 根据html页面和 form表单数据 生成pdf，
     * 参数说明，请详细阅读后使用：
     * @param array $param
     * @return string $pdf_path 返回一个上传阿里云后的一个资源地址，完整路径，可以直接访问
     */
    public function htmlToImg($param)
    {
        $host = $this->getDI()->getConfig()->api->api_form_pdf;
        $url  = $host.'/api/image/createImageByJvppeteer';
        //组装接口提交参数
        $sendParam = [
            'imageName'          => $param['file_name'] ?? time(),         //返回的pdf名称，目前没用到
            'templateUrl'        => $param['tpl_url'], //pdf 模板oss路径（html页面）
            'data'               => $param['data'],     //表单变量数据
            'contentDisposition' => $param['disposition'] ?? 'inline',//inline 或 attchment (直接下载)
        ];
        //配置项如果没有 就不传 那边不兼容
        if(!empty($param['imageOptions'])){
            $sendParam['imageOptions'] = $param['imageOptions'];
        }
        if(!empty($param['viewport'])){
            $sendParam['viewport'] = $param['viewport'];
        }

        $post_data_json = json_encode($sendParam, JSON_UNESCAPED_UNICODE);
        //发送请求
        $header[] = "content-type: "."application/json;charset=UTF-8";
        $result   = HttpCurl::httpPost($url, $post_data_json, $header, null, 20);
        $this->logger->write_log("htmlToImg result {$result} url {$url} param {$post_data_json}", 'info');
        $result   = json_decode($result, true);
        $pdf_path = [];

        if (isset($result['code']) && $result['code'] == 1) {
            $pdf_path = $result['result'];
        }
        return $pdf_path;
    }


}
