<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 6/11/24
 * Time: 5:03 PM
 */

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\WorkFromHomeApplyModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Traits\AuditStateTrait;
use FlashExpress\bi\App\Traits\FindApprovalNodeTrait;

class WorkHomeServer extends AuditBaseServer
{
    use FindApprovalNodeTrait;
    use AuditStateTrait;

    public function addRecord($param)
    {
        $this->checkApply($param);
        $db = $this->getDI()->get('db');
        try{
            $db->begin();
            $serialNo                = $this->getRandomId();
            $model                   = new WorkFromHomeApplyModel();
            $insert['serial_no']     = $serialNo;
            $insert['staff_info_id'] = $param['staff_id'];
            $insert['home_date']     = $param['home_date'];
            $insert['place_code']    = $param['place_code'];
            $insert['state']         = enums::$audit_status['panding'];
            $insert['audit_reason']  = $param['audit_reason'] ?? '';
            $model->create($insert);

            $lastInsertId = $model->id;
            $flag         = (new ApprovalServer($this->lang, $this->timeZone))->create($lastInsertId, AuditListEnums::APPROVAL_TYPE_WORK_HOME_APPLY, $param['staff_id']);
            if (!$flag) {
                throw new \Exception('创建审批流失败');
            }
            $db->commit();
        }catch (\Exception $e){
            $db->rollback();
            throw $e;
        }
        return $this->checkReturn(['data' => $lastInsertId]);
    }

    //添加操作验证
    public function checkApply($param)
    {
        $t = $this->getTranslation();
        $today = date('Y-m-d');
        if ($param['home_date'] < $today) {//后来有又改需求为 可以选今天
            throw new ValidationException($t->_('home_date_error'));
        }
        //如果 申请日期 存在出差（状态 1，2） 不让申请
        $checkTrip = BusinessTripModel::findFirst([
            'conditions' => 'apply_user = :staff_info_id: and status in (1,2) and start_time <= :date_at: and end_time >= :date_at:',
            'bind'       => ['staff_info_id' => $param['staff_id'], 'date_at' => $param['home_date']],
        ]);

        if (!empty($checkTrip)) {
            throw new ValidationException($t->_('business_trip_exist'));
        }

        //是否存在居家申请
        $exist = WorkFromHomeApplyModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and state in ({state:array}) and home_date = :date_at:',
            'bind'       => [
                'staff_info_id' => $param['staff_id'],
                'state'         => [enums::$audit_status['panding'], enums::$audit_status['approved']],
                'date_at'       => $param['home_date'],
            ],
        ]);
        if (!empty($exist)) {
            throw new ValidationException($t->_('work_home_exist'));
        }
        return true;
    }


    /**
     * 审批操作
     * @throws ValidationException
     * @throws \Exception
     */
    public function approve($param)
    {
        $t    = $this->getTranslation();
        $info = WorkFromHomeApplyModel::findFirst($param['audit_id']);
        if (empty($info)) {
            throw new ValidationException('id error');
        }
        $userInfo  = $param['user_info'];
        $auditType = AuditListEnums::APPROVAL_TYPE_WORK_HOME_APPLY;
        $res       = false;
        $server    = new ApprovalServer($this->lang, $this->timeZone);
        if ($param['status'] == enums::APPROVAL_STATUS_APPROVAL) {//通过
            $res = $server->approval($param['audit_id'], $auditType, $userInfo['id']);
        } elseif ($param['status'] == enums::APPROVAL_STATUS_REJECTED) {//驳回
            $res = $server->reject($param['audit_id'], $auditType, $param['reject_reason'], $userInfo['id']);
        } elseif ($param['status'] == enums::APPROVAL_STATUS_CANCEL) {//撤销
            $type = $this->determineCancelType($param['audit_id'], $auditType);
            //是否是最后审批人 审批通过的撤销 不走撤销审批 直接撤销
            $nodeParam['auditType'] = $auditType;
            $nodeParam['auditId']   = $param['audit_id'];
            $nodeParam['staff_id']  = $userInfo['id'];
            $flag                   = $this->findLastApprovalNode($nodeParam);
            if ($type == 1 || $flag) {
                //审批中撤销
                $res = $server->cancel($param['audit_id'], $auditType, $param['reject_reason'] ?? '', $userInfo['id']);
            } elseif ($type == 2) {
                //自己撤销审批完成的 需要写原因
                if (empty($param['reject_reason'])) {
                    throw new ValidationException($t->_('need_cancel_reason'));
                }

                if (mb_strlen($param['reject_reason']) > 500) {
                    throw new ValidationException($t->_('1030'));
                }
                $db = $this->getDI()->get("db");
                $db->begin();
                $info->state         = enums::$audit_status['panding'];
                $info->created_at    = DateHelper::localToUtc();//创建时间 里面和外面要更新为 撤销审批申请时间
                $info->approval_time = null;
                $info->update();
                //操作 审批流 apply 和 approval 变更为撤销审批状态
                $res = $server->cancel_create($param['audit_id'], $auditType, $param['reject_reason'] ?? '',
                    $userInfo['id']);
                $res ? $db->commit() : $db->rollBack();
            }
        }
        if ($res === false) {
            return $this->checkReturn(-3, 'server error');
        }
        return $this->checkReturn([]);
    }

    /**
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        //审批同意之后 允许审批人撤销
        return new AuditOptionRule(true, true, false, true, false, false);
    }

    //审批 的详情页
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $info = WorkFromHomeApplyModel::findFirst($auditId);
        if (empty($info)) {
            throw new ValidationException('audit id error');
        }
        $info = $info->toArray();

        //获取提交人用户信息
        $staff_info = (new StaffRepository())->getStaffPosition($info['staff_info_id']);
        if ($staff_info['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            $storeName = enums::HEAD_OFFICE;
        } else {
            $storeInfo = SysStoreModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $staff_info['sys_store_id']],
            ]);
            $storeName = empty($storeInfo) ? '' : $storeInfo->name;
        }

        $businessServer = new BusinesstripServer($this->lang, $this->timeZone);
        //职位
        $detailLists['apply_parson']    = $staff_info['name'] . "({$staff_info['staff_info_id']})";
        $detailLists['department']      = $staff_info['depart_name'];
        $detailLists['staff_job_title'] = $staff_info['job_name'];
        $detailLists['dot']             = $storeName;
        //居家地点
        $detailLists['work_home_place'] = $businessServer->formatProvinceCity($info['place_code']);
        //居家日期
        $week = date('N', strtotime($info['home_date']));
        $detailLists['work_home_date'] = $info['home_date'] . '(' . $this->getTranslation()->_('default_rest_day_' . $week) . ')';
        //原因
        $detailLists['reason'] = $info['audit_reason'];

        $audit_list_re = new AuditlistRepository($this->lang, $this->timeZone);
        $headData      = [
            'title'       => $audit_list_re->getAudityType(AuditListEnums::APPROVAL_TYPE_WORK_HOME_APPLY),
            'id'          => $info['id'],
            'staff_id'    => $info['staff_info_id'],
            'type'        => AuditListEnums::APPROVAL_TYPE_WORK_HOME_APPLY,
            'created_at'  => show_time_zone($info['created_at']),
            'updated_at'  => $info['approval_time'] ?? '',//审批时间
            'status'      => $info['state'],
            'status_text' => $audit_list_re->getAuditStatus('10' . $info['state']),
            'serial_no'   => $info['serial_no'] ?? '',
        ];

        //详情页特殊处理
        $returnData['data']['detail'] = $this->format($detailLists);
        $returnData['data']['head']   = $headData;
        return $returnData;
    }

    //列表页 概要字段
    public function genSummary(int $auditId, $user)
    {
        $info = WorkFromHomeApplyModel::findFirst($auditId);
        if (empty($info)) {
            return [];
        }
        $cityName = "code {$info['place_code']}";
        //日期后面 增加 星期展示
        $week  = date('N', strtotime($info['home_date']));
        $t     = $this->getTranslation();
        $param = [
            [
                'key'   => "work_home_date",
                'value' => $info['home_date'] . '(' .$t->_('default_rest_day_' . $week) . ')',
            ],
            [
                'key'   => "work_home_place",
                'value' => $cityName,
            ],
        ];
        return $param;
    }

    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if (!$isFinal) {
            return true;
        }
        $info = WorkFromHomeApplyModel::findFirst($auditId);
        if (empty($info)) {
            throw new ValidationException('data info error');
        }

        $state = $this->transformState($state, $extend['is_cancel'] ?? 0);

        $info->state       = $state;
        $info->approver_id = $extend['staff_id'];//审批人
        if (!empty($extend['staff_id'])) {
            $approveInfo         = HrStaffInfoModel::findFirst("staff_info_id={$extend['staff_id']}");
            $info->approver_name = $approveInfo->name;
        }
        $info->approval_time = date("Y-m-d H:i:s");
        if (!empty($extend['remark'])) {//驳回原因
            $info->reject_reason = $extend['remark'];
        }
        $info->update();
        return true;
    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        return [];
    }

    //是否有居家办公申请权限
    public function getWhPermission($param)
    {
        $staffInfo = $param['staff_info'];

        $settings      = (new SettingEnvServer())->listByCode(['WorkHomeType', 'WorkHomeDep']);
        $settingEnv    = array_column($settings, 'set_val', 'code');
        $types         = !empty($settingEnv['WorkHomeType']) ? explode(',', $settingEnv['WorkHomeType']) : [];

        $departmentIds = !empty($settingEnv['WorkHomeDep']) ? explode(',', $settingEnv['WorkHomeDep']) : [];
        //查询部门对应的子部门
        $depServer = new SysDepartmentServer($this->lang, $this->timeZone);
        $allIds    = [];
        foreach ($departmentIds as $id) {
            $res    = $depServer->getDepartmentIds($id);
            $allIds = array_merge($allIds, $res);
        }
        $allIds = array_values(array_unique($allIds));

        $staffType          = $staffInfo['week_working_day'] . $staffInfo['rest_type'];//员工类型
        $staffDepartmentIds = $staffInfo['node_department_id'];//员工部门id

        //如果没配置类型 不能申请
        if(empty($types)){
            return false;
        }

        //符合配置条件
        if (in_array($staffType, $types) && !empty($allIds) && in_array($staffDepartmentIds, $allIds)) {
            return true;
        }

//        if (in_array($staffType, $types) && empty($allIds)) {
//            return true;
//        }
        return false;
    }

    public function getEnumsList()
    {
        //城市和省联动
        $data['province_city'] = (new SysServer($this->lang, $this->timeZone))->getProvinceCitySelectFromCache();
        return $data;
    }


    //泰国 获取居家办公日期的方法 取审批表 只要审核通过的 如果是走撤销审批流 不算
    public function getWhDayTH($dateList, $staffId)
    {
        if (empty($dateList) || empty($staffId)) {
            return [];
        }
        $data = WorkFromHomeApplyModel::find([
            'columns'    => 'staff_info_id,home_date',
            'conditions' => 'staff_info_id = :staff_id: and home_date in ({dates:array}) and state = 2',
            'bind'       => ['staff_id' => $staffId, 'dates' => $dateList],
        ])->toArray();
        if (empty($data)) {
            return [];
        }
        return array_column($data, 'home_date');
    }


}