<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;


class SysDepartmentServer extends BaseServer
{

    public function __construct($lang = 'zh-CN', $timezone='+08:00')
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 根据settinenv获取部门及子部门
     * @param $code
     * @return array
     */
    public function getDepartmentIdsUseSettingEnv($code): array
    {
        return (new SysDepartmentModel())->getDepartmentIdsList($code);
    }


    /**
     * 获取负责的组织以及下级组织
     * @param $manager_id
     * @return array
     */
    public function getManagerDepartmentIds($manager_id): array
    {
        $result = [];
        if(empty($manager_id)){
            return $result;
        }

        $list = SysDepartmentModel::find([
            'columns' => 'id,name,ancestry,ancestry_v3,type,level,manager_id,manager_name,manager_phone,manager_position_state,assistant_id,assistant_name',
            "conditions" => "deleted = 0 and manager_id = :manager_id:",
            'bind' => [
                "manager_id" => $manager_id
            ],
        ])->toArray();

        if (!empty($list)) {

            $list_department_ids = array_column($list, 'id');
            $where = ['id in ({ids:array})'];
            $bind_where['ids'] = $list_department_ids;
            foreach ($list as $key => $value) {
                $where[] = ' ancestry_v3 like :ancestry_v3_'.$key.': ';
                $bind_where["ancestry_v3_".$key] = $value['ancestry_v3'] . '/%';
            }

            if(!empty($where) && !empty($bind_where)) {
                $where_str = implode(" or ", $where);
                $department_list = SysDepartmentModel::find([
                    'columns' => 'id,name,ancestry,ancestry_v3,type,level,manager_id,manager_name,manager_phone,manager_position_state,assistant_id,assistant_name',
                    "conditions" => "deleted = 0 and (" . $where_str . ")",
                    'bind' => $bind_where,
                ])->toArray();

                $result = array_column($department_list,'id');
            }
        }

        return $result;
    }

    //获取所负责网点
    public function getStaffManageDepartment($staffId, $belongDepartmentId = 0){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('d.id,d.name, d.relevance_store_id,s.name as store_name');
        $builder->from(['d' => SysDepartmentModel::class]);
        $builder->leftJoin(SysStoreModel::class, 's.id = d.relevance_store_id', 's');
        $builder->andWhere('d.deleted = 0 and d.manager_id = :manager_id:', ['manager_id' => $staffId]);
        if(!empty($belongDepartmentId)){
            $builder->orWhere('d.deleted = 0 and d.id = :belong_id:', ['belong_id' => $belongDepartmentId]);
        }
        $list = $builder->getQuery()->execute()->toArray();

//        $list = SysDepartmentModel::find([
//            'columns' => 'id,name, relevance_store_id',
//            "conditions" => "deleted = 0 and manager_id = :manager_id:",
//            'bind' => [
//                "manager_id" => $staffId
//            ],
//        ])->toArray();
        return $list;
    }

    /**
     * 根据部门ID获取关联网点
     * @param $department_ids
     * @return array
     */
    public function getRelevanceStoreByDepartmentId($department_ids)
    {
        if (empty($department_ids) || !is_array($department_ids)) {
            return [];
        }
        $manageStoreIds = SysDepartmentModel::find([
            'conditions' => 'relevance_store_id != \'\' and id in({ids:array})',
            'bind' => [
                'ids' => $department_ids
            ],
        ])->toArray();
        return array_column($manageStoreIds, 'relevance_store_id');
    }


    /**
     * 获取指定部门及子部门ID
     * @param $department_id
     * @return mixed
     */
    public function getDepartmentIds($department_id)
    {
        if(empty($department_id)){
            return [];
        }
        return (new SysDepartmentModel())->getSpecifiedDeptAndSubDept($department_id);
    }

    //根据部门id 查找所属一级部门id
    public function searchSysDeptId($department_id)
    {
        $sys_department_id = 0;
        //type扩展，原有1：公司；2：公司下的部门；3：是组织下的部门，新增扩展 4：boss级别（coo/cto/c..o）；5:gropu ceo 级别
        $dept_detail = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1',
            'bind'       => [
                1 => $department_id,
            ],
        ]);

        if (empty($dept_detail)) {
            return $sys_department_id;
        }
        $dept_detail                    = $dept_detail->toArray();
        $ancestry_v3_arr                = array_map('intval', explode('/', $dept_detail['ancestry_v3']));
        $dept_detail['ancestry_v3_arr'] = $ancestry_v3_arr;
        switch ($dept_detail['type']) {
            case 1:
                //公司
                $sys_department_id = $dept_detail['ancestry'];
                break;
            case 2:
                //公司下的部门
                $ids               = $this->getTypeDeptList([1, 4, 5]);
                $ancestry_v3       = array_values(array_diff($dept_detail['ancestry_v3_arr'], $ids));
                $sys_department_id = $ancestry_v3[0];
                break;
            case 3:
                //组织下的部门
                $ids               = $this->getTypeDeptList([4, 5]);
                $ancestry_v3       = array_values(array_diff($dept_detail['ancestry_v3_arr'], $ids));
                $sys_department_id = $ancestry_v3[0];
                break;
            case 4:
                //boss级别（coo/cto/c..o）
                $sys_department_id = $dept_detail['ancestry'];
                break;
            case 5:
                //group ceo 级别
                $sys_department_id = $department_id;
                break;
        }

        return $sys_department_id;
    }

    //根据部门类型获取部门列表
    private function getTypeDeptList($type = [])
    {
        if (empty($type)) {
            return [];
        }

        $list = SysDepartmentModel::find([
            'conditions' => 'type IN ({type:array})',
            'bind'       => [
                'type' => $type,
            ],
        ])->toArray();

        return array_column($list, 'id');
    }

    //根据 部门id 查询 部门信息
    public function getDepartmentByIds($ids)
    {
        if(empty($ids)) {
            return [];
        }
        $list = SysDepartmentModel::find([
            'conditions' => 'id IN ({ids:array})',
            'bind'       => [
                'ids' => $ids,
            ],
        ])->toArray();

        return array_column($list, null, 'id');
    }

    //部门详情
    public function getDepartmentDetail($department_id, $is_del = 0)
    {
        $where = '';
        if(!$is_del) {
            $where = ' and deleted=0';
        }
        $detail = SysDepartmentModel::findFirst(
            [
                'conditions' => 'id =:id: '.$where,
                'bind'=> [
                    'id' => $department_id
                ]
            ]
        );
        return !empty($detail) ? $detail->toArray() : [];
    }

    /**
     * @param $department_id
     * @param $only_id
     * @return array
     */
    public function getChildrenListByDepartmentIdV2($department_id, $only_id = false)
    {
        $department  = $this->getDepartmentDetail($department_id);
        if($department_id == 999) {
            $ancestry_v3 = 999;
        } else {
            $ancestry_v3 = $department['ancestry_v3'] ?? '';
        }

        if(empty($department) || empty($ancestry_v3)) return [];

        $child_list = SysDepartmentModel::find([
            'conditions' => 'deleted = 0 and ancestry_v3 like :ancestry_v3:',
            'bind' => [
                'ancestry_v3' => $ancestry_v3 . '/%'
            ],
            'order' => 'id asc'
        ])->toArray();

        if ($only_id) {
            return array_column($child_list, 'id');
        } else {
            return $child_list;
        }
    }

    /**
     * flashlink 部门列表
     * @param $page_num
     * @return array
     */
    public function getListForFlashLink($page_num): array
    {
        $page_size = 2000;
        return SysDepartmentModel::find([
            'columns'    => 'id, ancestry_v3 as ancestry, name, type',
            'conditions' => 'deleted = 0',
            'order'      => 'id asc',
            'offset'     => ($page_num - 1) * $page_size,
            'limit'      => $page_size,
        ])->toArray();
    }


    public function getDepartmentLevelNamesV2($department_id,$department_list) {
        $department_info = $department_list[$department_id] ?? [];
        if(empty($department_info)){
            return [];
        }
        $ancestry_arr = explode('/',$department_info['ancestry_v3'] ?? '');

        $dept = [
            0 => '-', //组织
            1 => '-', //公司名称
            2 => '-', //一级部门
            3 => '-', //二级部门
            4 => '-', //三级部门
            5 => '-', //四级部门
        ];
        //type扩展，原有1：公司；2：公司下的部门；3：是组织下的部门，新增扩展 4：boss级别（coo/cto/c..o）；5:gropu ceo 级别
        switch ($department_info['type']) {
            case 1:
                $dept = [
                    0 => isset($ancestry_arr[1]) ? $department_list[$ancestry_arr[1]]['name'] : '-',
                    1 => isset($ancestry_arr[2]) ? $department_list[$ancestry_arr[2]]['name'] : '-',
                    2 => '-',
                    3 => '-',
                    4 => '-',
                    5 => '-',
                ];
                break;
            case 2:
                $dept = [
                    0 => isset($ancestry_arr[1]) ? $department_list[$ancestry_arr[1]]['name'] : '-',
                    1 => isset($ancestry_arr[2]) ? $department_list[$ancestry_arr[2]]['name'] : '-',
                    2 => isset($ancestry_arr[3]) ? $department_list[$ancestry_arr[3]]['name'] : '-',
                    3 => isset($ancestry_arr[4]) ? $department_list[$ancestry_arr[4]]['name'] : '-',
                    4 => isset($ancestry_arr[5]) ? $department_list[$ancestry_arr[5]]['name'] : '-',
                    5 => isset($ancestry_arr[6]) ? $department_list[$ancestry_arr[6]]['name'] : '-',
                ];
                break;
            case 3:
                $department_ancestry_type = $department_list[$ancestry_arr[1]]['type'] ?? 3;
                if($department_info['ancestry'] == 999 || $department_ancestry_type == 3) {
                    $dept = [
                        0 => isset($ancestry_arr[0]) ?$department_list[$ancestry_arr[0]]['name'] : '-',
                        1 => '-',
                        2 => isset($ancestry_arr[1]) ? $department_list[$ancestry_arr[1]]['name'] : '-',
                        3 => isset($ancestry_arr[2]) ? $department_list[$ancestry_arr[2]]['name'] : '-',
                        4 => isset($ancestry_arr[3]) ? $department_list[$ancestry_arr[3]]['name'] : '-',
                        5 => isset($ancestry_arr[4]) ? $department_list[$ancestry_arr[4]]['name'] : '-',
                    ];
                } else {
                    $dept = [
                        0 => $department_list[$ancestry_arr[1]]['name'] ?? '-',
                        1 => '-',
                        2 => isset($ancestry_arr[2]) ? $department_list[$ancestry_arr[2]]['name'] : '-',
                        3 => isset($ancestry_arr[3]) ? $department_list[$ancestry_arr[3]]['name'] : '-',
                        4 => isset($ancestry_arr[4]) ? $department_list[$ancestry_arr[4]]['name'] : '-',
                        5 => isset($ancestry_arr[5]) ? $department_list[$ancestry_arr[5]]['name'] : '-',
                    ];
                }
                break;
            case 4:
                $dept = [
                    0 => isset($ancestry_arr[1]) ? $department_list[$ancestry_arr[1]]['name'] : '-',
                    1 => '-',
                    2 => '-',
                    3 => '-',
                    4 => '-',
                    5 => '-',
                ];
                break;
            case 5:
                $dept = [
                    0 => $department_list[$department_id]['name'] ?? '-',
                    2 => '-',
                    3 => '-',
                    4 => '-',
                    5 => '-',
                ];
                break;
        }
        return $dept;
    }

}