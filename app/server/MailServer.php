<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/11/5
 * Time: 6:44 PM
 */

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\Mail;
use Phalcon\Mvc\User\Component;

class MailServer extends Component
{

    private $default ;
    private $flash_home ;
    private $lnt ;


    public function __construct()
    {
        $this->default['host']         = env('mail_host', 'smtpdm-ap-southeast-1.aliyun.com');
        $this->default['name']         = env('mail_name', 'Flash Express DEV');
        $this->default['username']     = env('mail_address', '<EMAIL>');
        $this->default['port']         = env('mail_port', 465);
        $this->default['password']     = env('mail_pass', '4Ry4MTT3c38P0sWs');
        $this->default['company_type'] = 1;
        $this->default['ssl']          = 1;

        $this->flash_home['host']         = env('home_mail_host', 'sg-smtp.qcloudmail.com');
        $this->flash_home['name']         = env('home_mail_name', 'FlashHome Tra');
        $this->flash_home['username']     = env('home_mail_address', '<EMAIL>');
        $this->flash_home['port']         = env('home_mail_port', 465);
        $this->flash_home['password']     = env('home_mail_pass', 'Y0DC3jC5EHv5UoCN');
        $this->flash_home['company_type'] = 1;
        $this->flash_home['ssl']          = 1;

        $this->lnt['host']         = env('lnt_mail_smtp_host', 'sg-smtp.qcloudmail.com');
        $this->lnt['port']         = env('lnt_mail_smtp_port', 465);
        $this->lnt['username']     = env('lnt_mail_username', '<EMAIL>');
        $this->lnt['password']     = env('lnt_mail_password', 'C4NrJCwsGqA5');
        $this->lnt['name']         = env('lnt_mail_show_name', 'LNT');
        $this->lnt['company_type'] = env('lnt_mail_company_type', '6');
        $this->lnt['ssl']          = 1;

    }


    /**
     * @param $tomail
     * @param $title
     * @param $content
     * @param string $attachment_path 附件路径
     * @param string $attachment_name
     * @param string $ccmail 抄送
     * @param string $newname
     * @return mixed
     */
    public function send_mail($tomail,$title,$content,$attachment_path="",$attachment_name="",$ccmail='',$newname=''){
        $account = $this->default;
        $account['username'] = [$account['username']];
        $account['username'] = $account['username'][array_rand($account['username'],1)];
        $content = (new SysServer())->getEmailSignature($content,$account['company_type']);
        $flag = Mail::send_mail($account,$tomail,$title,$content,$attachment_path,$attachment_name,$ccmail,$newname);
        $this->getDI()->get('logger')->write_log("send_mail  ". json_encode($tomail).'_result_' . $flag,'info');
        return $flag;
    }

    /**
     * flash home 发送
     * @param $account
     * @param $tomail
     * @param $title
     * @param $content
     * @param string $attachment_path 附件路径
     * @param string $attachment_name
     * @param string $ccmail 抄送
     * @param string $newname
     * @return mixed
     */
    public function send_mail_from_home($tomail,$title,$content,$attachment_path="",$attachment_name="",$ccmail='',$newname=''){
        $account = $this->flash_home;
        $account['username'] = [$account['username']];
        $account['username'] = $account['username'][array_rand($account['username'],1)];
        $content = (new SysServer())->getEmailSignature($content,$account['company_type']);
        $flag = Mail::send_mail($account,$tomail,$title,$content,$attachment_path,$attachment_name,$ccmail,$newname);
        $this->getDI()->get('logger')->write_log("send_mail_from_home_title  ".json_encode($title,JSON_UNESCAPED_UNICODE),'info');
        $this->getDI()->get('logger')->write_log("send_mail_from_home  ".json_encode($account) . json_encode($tomail).'_result_' . $flag,'info');
        return $flag;
    }


    /**
     * lnt 发送
     * @param $tomail
     * @param $title
     * @param $content
     * @param string $attachment_path 附件路径
     * @param string $attachment_name
     * @param string $ccmail 抄送
     * @param string $newname
     * @return mixed
     */
    public function send_mail_lnt($tomail,$title,$content,$attachment_path="",$attachment_name="",$ccmail='',$newname=''){
        $account = $this->lnt;
        $account['username'] = [$account['username']];
        $account['username'] = $account['username'][array_rand($account['username'],1)];
        $content = (new SysServer())->getEmailSignature($content,$account['company_type']);
        $flag = Mail::send_mail($account,$tomail,$title,$content,$attachment_path,$attachment_name,$ccmail,$newname);
        $this->getDI()->get('logger')->write_log([$tomail,$title,$content,$attachment_path,'result'=>$flag],'info');
        return $flag;
    }




}