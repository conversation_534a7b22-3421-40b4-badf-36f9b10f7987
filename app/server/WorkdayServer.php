<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/1/5
 * Time: 2:36 PM
 */


namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftOperateLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffDefaultRestDayModel;

class WorkdayServer extends BaseServer
{

    /**
     * @param $type
     * @param $data
     * @param array $extend ['before' => 'xxx','after' => 'xxx', 't_key' => 'xxx']
     * @return bool
     * @throws \Exception
     */
    public function addShiftLog($type, $data, $extend = [])
    {
        $insert['staff_info_id'] = $data['staff_info_id'];
        $insert['date_at']       = $data['date_at'];
        $insert['operate_id']    = $data['operate_id'];
        $insert['edit_type']     = $type;
        $insert['remark']        = $extend['t_key']??'';//翻译key
        if (in_array($type, [
            HrStaffShiftOperateLogModel::EDIT_TYPE_DEFAULT_REST,
            HrStaffShiftOperateLogModel::EDIT_TYPE_CURRENT_SHIFT,
            HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT,
        ])) {
            $insert['remark'] = $extend['before'] . '->' . $extend['after'];//3类型 翻译key 4，5 只有值
        }
        $model = new HrStaffShiftOperateLogModel();
        $model->create($insert);
        return true;
    }

    /**
     * @param $type
     * @param $data ['staffId' => ['operate_id' => xxx, ['dates' => ['date1','date2']]]];
     * @param array $extend ['before' => 'xxx','after' => 'xxx', 't_key' => 'xxx']
     * @return bool
     * @throws \Exception
     */
    public function addShiftLogBatch($type, $data, $extend = [])
    {
        if (empty($data)) {
            return true;
        }

        $insert = [];
        foreach ($data as $staffId => $item) {
            $operateId = $item['operate_id'] ?: 10000;
            $dateList  = $item['dates'] ?? [];

            foreach ($dateList as $date) {
                $row['staff_info_id'] = $staffId;
                $row['date_at']       = $date;
                $row['operate_id']    = $operateId;
                $row['edit_type']     = $type;
                $row['remark']        = $extend['t_key'] ?? '';//翻译key
                if (in_array($type, [
                    HrStaffShiftOperateLogModel::EDIT_TYPE_DEFAULT_REST,
                    HrStaffShiftOperateLogModel::EDIT_TYPE_CURRENT_SHIFT,
                    HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT,
                ])) {
                    $row['remark'] = $extend['before'] . '->' . $extend['after'];//3类型 翻译key 4，5 只有值
                }
                $insert[] = $row;
            }
        }
        $model = new HrStaffShiftOperateLogModel();
        $model->batch_insert($insert);
        return true;
    }


    //到岗确认 加日志
    public function entryAdd($param)
    {
        //没有这个就不加
        if (empty($param['default_rest_day_date'])) {
            return true;
        }
        //加默认休日志 default_rest_day_date ["2"]
        $logData['staff_info_id'] = $param['new_id'];//新生成的工号
        $logData['operate_id']    = $param['staff_id'];//操作人
        $logData['date_at']       = date('Y-m-d');//默认休没有具体日期 取当前操作时间
        $extend['before']         = '';
        $extend['after']          = implode('', $param['default_rest_day_date']);

        $this->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_DEFAULT_REST, $logData, $extend);
        return true;
    }


}