<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/5/31
 * Time: 7:10 PM
 */

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;

class FranchiseesServer extends AuditBaseServer
{

    public $timezone;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $info = SystemExternalApprovalModel::findFirst($auditId);
        if (!empty($info)) {
            $summary = json_decode($info->summary, true);
        } else {
            $summary = json_decode([], true);
        }
        return $summary;
    }

    /**
     * @description: 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态      const APPROVAL_STATUS_PENDING   = 1;    //待审批 const APPROVAL_STATUS_APPROVAL  =
     *                      2;    //审批同意const APPROVAL_STATUS_REJECTED  = 3;    //审批驳回const APPROVAL_STATUS_CANCEL    =
     *                      4;    //审批撤销const APPROVAL_STATUS_TIMEOUT   = 5;    //审批超时
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     * @throws Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
        if (!$SystemExternalApprovalModel) {
            throw new \Exception('setProperty  没有找到数据  ' . $auditId);
        }

        if ($isFinal) {
            //最终审批需要做的操作
            $SystemExternalApprovalModel->state      = $state;
            $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());      //时间
            // 回调 异步脚本
            $params = [
                'approvalNo'         => $SystemExternalApprovalModel->serial_no,
                'approvalStatus'     => $state,
                'id'                 => $auditId,
            ];
            AuditCallbackServer::createData(AuditListEnums::APPROVAL_TYPE_FRANCHISEES, $params);
            return $SystemExternalApprovalModel->save();
        }
    }

    /**
     * 写入redis队列
     * @param $data
     * @return mixed
     */
    public function pushRedis($data)
    {
        $redis = $this->getDI()->get('redisLib');
        $res   = $redis->lpush(RedisEnums::LIST_FLE_FRANCHISEES, json_encode($data));
        $this->getDI()->get("logger")->write_log(['data' => $data, 'func' => 'pushRedis', 'res' => $res], "info");
        return $res;
    }

    
    /**
     * @param $params
     * @return bool
     * @throws Exception
     */
    public function delayCallBack($params): bool
    {
        $model     = SystemExternalApprovalModel::findFirst($params['id']);
        $api    = new RestClient('fra');
        $res    = $api->execute(RestClient::METHOD_POST, '/svc/franchisee/approval/franchiseesApprovalResult',
            $params, ['Accept-Language' => $this->lang]);
        $this->getDI()->get("logger")->write_log("delayCallBack fra api_svc : ". json_encode($res, JSON_UNESCAPED_UNICODE) ." paramIn:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        if (isset($res['code']) && $res['code'] !=  ErrCode::SUCCESS) {
            //这里需要判断 如果是失败了 记录一下  后期要处理数据
            $model->is_call_third_party = SystemExternalApprovalModel::is_call_third_party_2;
            $model->save();
            throw new Exception($res['message'] ?? $this->getTranslation()->_('4008'));
        }
        return true;
    }
    
    /**
     * 详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     * @throws Exception
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $system_external = new SystemExternalApprovalServer($this->lang, $this->timezone);
        //获取详情
        $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
        if (!$SystemExternalApprovalModel) {
            throw new \Exception('getDetail  没有找到数据  ' . $auditId);
        }
        $result = $SystemExternalApprovalModel->toArray();
        //获取详情
        $forwardParamIn = [
            'biz_type'                 => $result['biz_type'],
            'param_operation_type'     => $system_external::param_operation_type_4,
            'param_operation_staff_id' => $user,
            'approvalNo'               => $result['serial_no'],
        ];
        $svc_result     = $system_external->forwardParamIn($forwardParamIn);
        if (!isset($svc_result['code']) || $svc_result['code'] != 1) {
            throw new \Exception('getDetail  获取详情失败  ' . $auditId);
        }
        $auditList = new AuditlistRepository($this->lang, $this->timezone);
        $_detail   = [];
        if ($svc_result['data']) {
            foreach ($svc_result['data'] as $k => $v) {
                $_detail['franchisees_'.strtolower($k)] = $v;
            }
        }
        $returnData['data']['detail'] = $this->formatDetail($_detail ?? []);
        
        $add_hour                     = $this->getDI()['config']['application']['add_hour'];
        $data                         = [
            'title'      => $auditList->getAudityType($result['biz_type']),
            'id'         => $result['id'],
            'staff_id'   => $result['submitter_id'],
            'type'       => $result['biz_type'],
            'created_at' => date('Y-m-d H:i:s', (strtotime($result['created_at']) + $add_hour * 3600)),
            'updated_at' => date('Y-m-d H:i:s', (strtotime($result['updated_at']) + $add_hour * 3600)),
            'status'     => $result['state'],
            'serial_no'  => $result['serial_no'] ?? '',
            'biz_type'   => $result['biz_type'],
        ];

        $returnData['data']['head'] = $data;

        return $returnData;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false, false, false, false, false, false);
    }

    //审批详情页组装 pdf 链接数据
    public function formatDetail($list){
        $return = [];
        $t      = $this->getTranslation();
        foreach ($list as $key => $v) {
            $row =[
                'key'      => $t->_($key) ?? '',
                'key_tips' => is_array($v) && isset($v['key_tips']) ? $v['key_tips'] : null,
                'key_icon' => is_array($v) && isset($v['key_icon']) ? $v['key_icon'] : null,
                'value'    => is_array($v) && isset($v['value']) ? $v['value'] : $v,
                'tips'     => is_array($v) && isset($v['tips']) ? $v['tips'] : null,
                'color'    => is_array($v) && isset($v['color']) ? $v['color'] : null,
            ];
            if(strtolower($key) == 'franchisees_storelocation'){
                $row['type'] = enums::APPROVAL_DETAIL_TYPE_COORDINATE;
                $row['simple_val'] = $row['value'] ?? '';
            }
            $return[] = $row;
        }
        return $return;
    }
}