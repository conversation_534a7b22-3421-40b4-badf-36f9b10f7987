<?php

/**
 * 行政工单问题类型
 */

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Repository\AdministrationQuestionTypeRepository;

class AdministrationQuestionTypeServer extends BaseServer
{

    public function __construct($lang = 'zh-CN')
    {
        parent::__construct($lang);
    }

    /**
     * 获取问题类型数据
     * @return array|false
     */
    public function getQuestionType()
    {
        $questionTypeRet = (new AdministrationQuestionTypeRepository())->getQuestionTypeByParams([
            'is_deleted' => 0,
        ], ['id', 't_key']);

        if (empty($questionTypeRet) || !is_array($questionTypeRet)) {
            return false;
        }
        $data = [];
        foreach ($questionTypeRet as $key => $val) {
            $data[] = [
                "id"   => $val["id"],
                "name" => $this->getTranslation()->_($val['t_key']),
            ];
        }
        return $data;
    }

}