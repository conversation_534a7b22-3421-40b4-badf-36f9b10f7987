<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Server\AuditListServer;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\library\ApiClient;

class OtherServer extends BaseServer
{

    protected $other;
    public      $timezone;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
    }

    /**
     * 验证是否在ces培训
     * @param $userinfo
     * @return bool
     */
    public function checkStaffIsCesTraining($userinfo): bool
    {
        //培训需求
        if(!in_array(RUNTIME,['tra','dev'])){
            $this->logger->write_log('checkStaffIsCesTraining staff_id:'.$userinfo['id']. ' RUNTIME : false','info');
            return false;
        }
        //如果是ces培训 则可以跳过
        $is_ces = (new OtherRepository())->checkStaffIsCesTraining($userinfo);
        $this->logger->write_log('checkStaffIsCesTraining staff_id:'.$userinfo['id']. ' result : '.(!empty($is_ces)?1:0),'info');

        return  !empty($is_ces);
    }

    /**
     * 网点快递员出纳角色/ 网点经理/ 主管打卡提醒消息
     * @param $paramIn
     * @param $userinfo
     * @return array
     */
    public function remittanceDialogS($paramIn, $userinfo)
    {
        $returnArr['dialog_status'] = 0;
        $returnArr['dialog_msg'] = '';
        $returnArr['dialog_must_status'] = 0;
        $returnArr['is_ces_tra'] = 0;
        $country_pre = '_'.strtolower(env('country_code', 'th'));

        if ($paramIn['clock_type'] == 2) {
            if ( in_array(3, $userinfo['positions']) || in_array(18, $userinfo['positions'])) {
                //网点经理/ 主管打上班卡时显示快递员因回公款停职的员工
                $res = (new OtherRepository())->stopDutiestaff($userinfo);
                if( $res ){
                    $staffids = implode( array_column($res,'staff_info_id') ,"\r\n" );
                    $show_msg = $this->getTranslation()->_('remittanceDialogMsg_5'.$country_pre)??$this->getTranslation()->_('remittanceDialogMsg_5');
                    $show_msg = str_replace('{staffids}',$staffids,$show_msg);
                    $returnArr['dialog_status'] = 1;
                    $returnArr['dialog_msg'] = $show_msg;
                }
            }else if (in_array(4, $userinfo['positions'])   ) {

                if(isCountry('MY')){
                    $returnData['data'] = $returnArr;
                    return $this->checkReturn($returnData);
                }

                //网点出纳角色/ 打上班卡时显示快递员未上缴的金额和应汇款的金额：
                $res = (new OtherRepository())->courierReceivable($userinfo, 2);
                if(isset($res)){
                    //查询根据网点员工id查询回款情况
                    $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.StoreReceivableBillSvc','findUnpaidByStoreId', $this->lang);
                    $fle_rpc->setParams($userinfo['organization_id']);
                    $fle_return = $fle_rpc->execute();
                    if(isset($fle_return['result']['unpaid_amount_count']) && $fle_return['result']['unpaid_amount_count'] > 0){
                        $res['receivable_amount'] = round($fle_return['result']['unpaid_total_amount']/100,2);
                        $res['receivable_count'] = $fle_return['result']['unpaid_amount_count'];
                    }
                }
                $resCod = (new OtherRepository())->onCodReceivable($userinfo);
                if(isset($resCod)){
                    //查询根据网点员工id查询回款情况
                    $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.StoreReceivableBillSvc','findUnpaidStoreCodBillTotalById', $this->lang);
                    $fle_rpc->setParams($userinfo['organization_id']);
                    $fle_return = $fle_rpc->execute();
                    if(isset($fle_return['result']['unpaid_amount_count']) && $fle_return['result']['unpaid_amount_count'] > 0){
                        $res['receivable_amount'] = $fle_return['result']['unpaid_total_amount'];
                        $res['receivable_count'] = $fle_return['result']['unpaid_amount_count'];
                    }
                }
                $resParce = (new OtherRepository())->onParcelReceivable($userinfo);
                if(isset($resCod)){
                    //查询根据网点员工id查询回款情况
                    $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.StoreReceivableBillSvc','findUnpaidStoreBillTotalById', $this->lang);
                    $fle_rpc->setParams($userinfo['organization_id']);
                    $fle_return = $fle_rpc->execute();
                    if(isset($fle_return['result']['unpaid_amount_count']) && $fle_return['result']['unpaid_amount_count'] > 0){
                        $res['receivable_amount'] = $fle_return['result']['unpaid_total_amount'];
                        $res['receivable_count'] = $fle_return['result']['unpaid_amount_count'];
                    }
                }
                if( $res['receivable_count'] > 0){
                    $amount = bcadd( $resCod['cod_amount'] , $resParce['parcel_amount'] , 2 ) ;
                    $show_msg = $this->getTranslation()->_('remittanceDialogMsg_3'.$country_pre)??$this->getTranslation()->_('remittanceDialogMsg_3');
                    $show_msg = str_replace('{receivable_amount}',$res['receivable_amount'],$show_msg);
                    $show_msg = str_replace('{amount}',$amount,$show_msg);
                    $returnArr['dialog_status'] = 1;
                    $returnArr['dialog_msg'] = $show_msg;
                }
            }
        }
        $returnData['data'] = $returnArr;
        return $this->checkReturn($returnData);
    }
}
