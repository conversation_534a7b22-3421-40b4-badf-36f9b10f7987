<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;

class StatisticsAttendanceServer extends BaseServer
{
    public function __construct($lang = 'zh-CN', $timezone = 'Asia/Bangkok')
    {
        $this->timezone = $timezone;
        parent::__construct($lang, $timezone);
    }

    const ACTION_JOB_TITLE = ['Warehouse Staff','Warehouse Staff Leader','Warehouse supervisor','Forklift Driver'];
    const WAREHOUSE_JOB_TITLE = ['Warehouse Manager','Assistant Warehouse Manager','Warehouse Operations Director'];

    /**
     * 获取特定网点原
     * @param $params
     */
    public function getStoreAttendance($params)
    {
        ini_set('memory_limit', '3072M'); // 临时设置最大内存占用为3G
        set_time_limit(0);      // 设置脚本最大执行时间 为0 永不过期
        $data = [];

        if (empty($params['start_date']) || !preg_match("/\d{4}\-\d{2}\-\d{2}/", $params['start_date'])) {
            throw new \Exception('查询起始时间参数有误 格式为Y-m-d', ErrCode::VALIDATE_ERROR);
        }

        if (empty($params['end_date']) || !preg_match("/\d{4}\-\d{2}\-\d{2}/", $params['end_date'])) {
            throw new \Exception('查询截止时间参数有误 格式为Y-m-d', ErrCode::VALIDATE_ERROR);
        }

        if($params['end_date'] > gmdate('Y-m-d')){
            throw new \Exception('查询截止时间参数有误', ErrCode::VALIDATE_ERROR);
        }
        $action_job_title = self::ACTION_JOB_TITLE;
        $warehouse_job_title = self::WAREHOUSE_JOB_TITLE;

        $store_name = $params['store_name'] ?? [];
        if(!empty($store_name) && !is_array($store_name)){
            throw new \Exception('网点参数有误', ErrCode::VALIDATE_ERROR);
        }

        $store_param_id = $params['store_id'] ?? [];
        if(!empty($store_param_id) && !is_array($store_param_id)){
            throw new \Exception('网点 id 参数有误', ErrCode::VALIDATE_ERROR);
        }
        //2 传 1 或者都传 不能都空
        if(empty($store_name) && empty($store_param_id))
            throw new \Exception('网点 id name 至少传一个', ErrCode::VALIDATE_ERROR);


        $store_ids = $store_info = array();
        if(!empty($store_name)){//传的网点
            //获取网点
            $store_info = $this->getStoreInfoByName($store_name);
            $this->logger->write_log("store_staff_attendance_for_boss  store_ids: ".json_encode($store_info, JSON_UNESCAPED_UNICODE), 'info');

            $store_ids = array_column($store_info, 'id');
        }

        if(!empty($store_param_id)){//传了 网点id  和 name的 id 并集
            $store_ids = array_merge($store_ids,$store_param_id);
            $store_ids = array_values(array_unique($store_ids));

            $store_info = SysStoreModel::find(['conditions' => 'id in ({ids:array}) and state = 1',
                'bind'=> ['ids'=>$store_ids],
                'columns' => "id,name",
            ])->toArray();

        }

        if (empty($store_ids)) {
            return  $data;
        }

        //获取职位
        $job_title_info  =  $this->getJobTitleInfoByName(array_merge($action_job_title, $warehouse_job_title));

        if (empty($job_title_info)) {
            return  $data;
        }
        $this->logger->write_log("store_staff_attendance_for_boss  job_title: ".json_encode($job_title_info, JSON_UNESCAPED_UNICODE), 'info');

        $action_job_title_id = $warehouse_job_title_id = [];
        foreach ($job_title_info as $item) {
            if (in_array($item['job_name'], $action_job_title)) {
                $action_job_title_id[] = $item['id'];
            } else {
                $warehouse_job_title_id[] = $item['id'];
            }
        }
        //获取员工；
        $hr_staff_info = HrStaffInfoModel::find(['conditions'=> 'job_title in  ({job_title:array}) and sys_store_id in ({sys_store_id:array}) and state in (1,2,3)',
                                                    'bind'=>['job_title'=>array_merge($action_job_title_id, $warehouse_job_title_id),'sys_store_id'=>$store_ids],
                                                    'columns' => 'staff_info_id,sys_store_id,job_title',
                                                ]);

        if (!$hr_staff_info) {
            return  $data;
        }
        $hr_staff_info = $hr_staff_info->toArray();

        if(empty($hr_staff_info))
            throw new \Exception('网点下没有仓储在职员工 ', ErrCode::VALIDATE_ERROR);

        $this->logger->write_log("store_staff_attendance_for_boss  hr_staff_info: ".json_encode($hr_staff_info, JSON_UNESCAPED_UNICODE), 'info');

        $staff_info_ids = $store_staff_ids = $staff_job_title =[];
        foreach ($hr_staff_info as $item) {
            $staff_info_ids[] = $item['staff_info_id'];
            $store_staff_ids[$item['sys_store_id']][] = $item['staff_info_id'];
            $staff_job_title[$item['staff_info_id']] = $item['job_title'];
        }
        $this->logger->write_log("store_staff_attendance_for_boss  store_staff_ids: ".json_encode($store_staff_ids, JSON_UNESCAPED_UNICODE), 'info');

        //获取考勤数据
        $work_attendance = StaffWorkAttendance::find(['conditions'=>'staff_info_id in ({staff_info_id:array}) and attendance_date >= :start_date: and attendance_date <= :end_date:',
                                                    'bind'=>['staff_info_id'=>$staff_info_ids,'start_date'=>$params['start_date'],'end_date'=>$params['end_date']],
                                                    'columns' => 'staff_info_id,attendance_date',
                                              ]);

        if (!$work_attendance) {
            return  $data;
        }
        $work_attendance = $work_attendance->toArray() ;

        $this->logger->write_log("store_staff_attendance_for_boss  staff_work_attendance: ".json_encode($work_attendance, JSON_UNESCAPED_UNICODE), 'info');
        
        $date = DateHelper::DateRange(strtotime($params['start_date']), strtotime($params['end_date']));

        foreach ($date as $day) {
            foreach ($store_info as $item) {
                $tmp['date'] = $day;
                $tmp['store_id'] = $item['id'];
                $tmp['store_name'] = $item['name'];
                $tmp['action_staff_total'] = 0;
                $tmp['warehouse_staff_total'] = 0;
                foreach ($work_attendance as  $w) {
                    if ($day == $w['attendance_date'] && isset($store_staff_ids[$item['id']]) &&  in_array($w['staff_info_id'], $store_staff_ids[$item['id']])) {
                        if ( isset($staff_job_title[$w['staff_info_id']]) &&  in_array($staff_job_title[$w['staff_info_id']], $action_job_title_id)) {
                            $tmp['action_staff_total'] += 1;
                        } elseif ( isset($staff_job_title[$w['staff_info_id']]) &&  in_array($staff_job_title[$w['staff_info_id']], $warehouse_job_title_id)) {
                            $tmp['warehouse_staff_total'] += 1;
                        }
                    }
                }
                $data[] = $tmp;
            }
        }
        return  $data;
    }

    /**
     * 获取职位信息
     * @param array $job_name
     * @return array
     */
    private function getJobTitleInfoByName(array $job_name)
    {
        if (!empty($job_name)) {
            $job_title_info  =  HrJobTitleModel::find(['conditions'=> 'job_name in  ({job_name:array}) and status = 1',
                                                          'bind'=>['job_name'=>$job_name],
                                                          'columns' => 'id,job_name',
                                                      ]);
            return $job_title_info ? $job_title_info->toArray():[];
        }
        return  [];
    }

    /**
     * 获取网点信息
     * @param array $store_name
     * @return array
     */
    private function getStoreInfoByName(array $store_name)
    {
        if (!empty($store_name)) {
            $store_info = SysStoreModel::find(['conditions' => 'name in ({name:array}) and state = 1',
                                                     'bind'=> ['name'=>$store_name],
                                                     'columns' => "id,name",
                                                 ]);
            return $store_info ? $store_info->toArray() : [];
        }
        return  [];
    }
}
