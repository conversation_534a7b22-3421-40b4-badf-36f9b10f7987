<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;

class AssetV2Server  extends SystemExternalApprovalServer
{
    /**
     * 审批回调
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return bool|mixed|void
     * @throws \Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('setProperty  没有找到数据  '.$auditId);
            }
            $SystemExternalApprovalModel->state = $state;
            $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());
            $SystemExternalApprovalModel->save();
        }
        return true;
    }
}