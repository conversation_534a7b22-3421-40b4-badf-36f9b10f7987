<?php


namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Enums\WorkingCountryEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HRStaffingModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;

class HcStatisticServer extends BaseServer
{
    const DATA_TYPE_NORMAL = 1;      //普通数据
    const DATA_TYPE_AGGREGATION = 2; //汇总数据
    const DATA_TYPE_SHARE = 3;       //共享数据

    const COLUMN_KEY_DEPARTMENT_TREE_CEO_GROUP = 0;
    const COLUMN_KEY_DEPARTMENT_TREE_CEO_LEVEL = 1;
    const COLUMN_KEY_DEPARTMENT_TREE_BU = 2;
    const COLUMN_KEY_DEPARTMENT_TREE_LEVEL_1ST = 3;
    const COLUMN_KEY_DEPARTMENT_TREE_LEVEL_2ND = 4;
    const COLUMN_KEY_DEPARTMENT_TREE_LEVEL_3RD = 5;
    const COLUMN_KEY_DEPARTMENT_TREE_LEVEL_4TH = 6;

    protected static $instance;

    /**
     * @return HcStatisticServer
     */
    public static function getInstance(): HcStatisticServer
    {
        if (!isset(self::$instance) || self::$instance instanceof self) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @description 全量HC预算数据
     */
    public function getBudget()
    {
        //SELECT
        //    concat(B.job_title_id,'-',B.dept_id,'-',B.store_id) as uniques,B.count as cou,B.job_title_id,B.dept_id,B.store_id
        //FROM
        //    ( SELECT max( id ) AS id FROM hr_staffing WHERE count > 0 GROUP BY job_title_id,dept_id, store_id ) AS A
        //LEFT JOIN hr_staffing AS B ON B.id = A.id ORDER BY B.id ASC
        return HRStaffingModel::find([
            'columns' => 'job_title_id,dept_id,count as cou,concat(job_title_id,"-",dept_id) as unique',
        ])->toArray();
    }

    /**
     * @description 全量HC预算数据在职统计数据
     * @return mixed
     */
    public function getOnJob()
    {
        //SELECT concat(job_title,'-',node_department_id,'-',sys_store_id) as uniques,count(1) as cou,job_title,node_department_id,sys_store_id
        //FROM hr_staff_info WHERE
        //state = 1  -- 在职
        //AND formal in (1,4) -- 编制和实习生
        //AND is_sub_staff = 0 -- 不是子账号
        //AND job_title IS NOT NULL  -- 存在职位
        //AND working_country = :working_country
        //AND wait_leave_state = 0 -- 非待离职
        //GROUP BY job_title,node_department_id,sys_store_id
        $workingCountry = WorkingCountryEnums::getWorkingCountry();

        return HrStaffInfoModel::find([
            'conditions' => 'state = 1 and formal in(1,4) and is_sub_staff = 0 and wait_leave_state = 0 and working_country = :working_country:',
            'bind' => [
                'working_country' => $workingCountry,
            ],
            'columns' => "concat(job_title,'-',node_department_id) as unique,count(1) as cou,job_title,node_department_id",
            'group' => 'job_title,node_department_id',
        ])->toArray();
    }

    /**
     * @description 停职人数
     * @return mixed
     */
    public function getSuspend()
    {
        $workingCountry = WorkingCountryEnums::getWorkingCountry();

        return HrStaffInfoModel::find([
            'conditions' => 'state = 3 and formal in(1,4) and is_sub_staff = 0 and wait_leave_state = 0 and working_country = :working_country:',
            'bind' => [
                'working_country' => $workingCountry,
            ],
            'columns' => "concat(job_title,'-',node_department_id) as unique,count(1) as cou,job_title,node_department_id",
            'group' => 'job_title,node_department_id',
        ])->toArray();
    }

    /**
     * @description 待离职人数
     * @return mixed
     */
    public function getWaitLeave()
    {
        $workingCountry = WorkingCountryEnums::getWorkingCountry();

        return HrStaffInfoModel::find([
            'conditions' => 'state = 1 and formal in(1,4) and is_sub_staff = 0 and wait_leave_state = 1 and working_country = :working_country:',
            'bind' => [
                'working_country' => $workingCountry,
            ],
            'columns' => "concat(job_title,'-',node_department_id) as unique,count(1) as cou,job_title,node_department_id",
            'group' => 'job_title,node_department_id',
        ])->toArray();
    }

    /**
     * @description 职位部门关联关系
     * @return mixed
     */
    public function getJobDepartmentRelate()
    {
        return HrJobDepartmentRelationModel::find([
            'columns' => "department_id,job_id,concat(job_id,'-',department_id) as unique",
        ])->toArray();
    }

    /**
     * @description 获取待转入
     */
    public function getPendingTransfer()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("concat(after_position_id,'-',after_department_id) as unique,count(1) as cou,after_position_id,after_department_id");
        $builder->from(['s' => JobTransferModel::class]);
        $builder->inWhere('s.approval_state', [enums::APPROVAL_STATUS_PENDING,enums::APPROVAL_STATUS_APPROVAL]);//审核通过
        $builder->andWhere('s.state = :state:', ['state' => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED]); //待转岗
        $builder->andWhere('s.after_position_id IS NOT NULL');                                           //拥有job
        $builder->groupBy("after_position_id,after_department_id");
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @description 获取全部职位部门网点转岗待转出人数
     */
    public function getTransfer()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("concat(current_position_id,'-',current_department_id) as unique,count(1) as cou,current_position_id,current_department_id");
        $builder->from(['s' => JobTransferModel::class]);
        $builder->andWhere('s.approval_state = :approval_state:', ['approval_state' => enums::APPROVAL_STATUS_APPROVAL]);//审核通过
        $builder->andWhere('s.state = :state:', ['state' => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED]); //待转岗
        $builder->andWhere('s.current_position_id IS NOT NULL');                                         //拥有job
        $builder->groupBy("current_position_id,current_department_id");
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @description 获取待入职人数
     */
    public function getPendingEntry()
    {

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("
            concat(h.job_title,'-',h.department_id) as unique,
            count(1) as cou,
            h.job_title,
            h.department_id");
        $builder->from(['h' => HrHcModel::class]);
        $builder->leftJoin(HrEntryModel::class, 'h.hc_id = e.hc_id', 'e');
        $builder->andWhere('e.deleted = :deleted:', ['deleted' => Enums::IS_DELETED_NO]);
        $builder->andWhere('e.status = :status:', ['status' => HrEntryModel::STATUS_TO_BE_EMPLOYED]);
        $builder->groupBy("h.job_title,h.department_id");
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @description 获取全部招聘中人数
     * @return mixed
     */
    public function getSurplus()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("
            	concat(s.job_title, '-', s.department_id) AS unique,
				sum(surplusnumber) AS cou,
				s.job_title,
				s.department_id");
        $builder->from(['s' => HrHcModel::class]);
        $builder->andWhere('s.state_code = :state:', ['state' => HrHcModel::STATE_RECRUITING]); //招聘中
        $builder->andWhere('s.job_title IS NOT NULL  and s.deleted = 1');
        $builder->groupBy("s.job_title,s.department_id");
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @description 获取已提交的HC总人数（待审批）
     */
    public function getBudgetAdopt()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("
                concat(s.job_title, '-', s.department_id) AS unique,
				sum( demandnumber ) AS cou,
				s.job_title,
				s.department_id");
        $builder->from(['s' => HrHcModel::class]);
        $builder->andWhere('s.state_code = :state: and approval_state_code = 7 ',
            ['state' => HrHcModel::STATE_NOT_EFFECTIVE]);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          //1 招聘中
        $builder->andWhere('s.job_title IS NOT NULL  and s.deleted = 1');
        $builder->groupBy("s.job_title,s.department_id");
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @description 解析共享配置
     * @param $share_department_position
     * @return array
     */
    public function getShareConfig($share_department_position): array
    {
        $share_list = [];
        foreach ($share_department_position as $dept_id => $job_title_list) {
            foreach ($job_title_list as $job_title_id) {
                //索引
                $uniqueKey = sprintf("%d-%d", $job_title_id, $dept_id);
                $share_list[$dept_id][$uniqueKey] = [
                    'job_title_id' => $job_title_id,
                    'dept_id'      => $dept_id,
                ];
            }
        }
        return $share_list;
    }

    /**
     * @description 获取部门链
     * @return mixed
     */
    public function getDepartmentTreeList()
    {
        $departmentList = SysDepartmentModel::find([
            'conditions' => 'deleted = 0',
            'columns' => 'id,name,ancestry_v3,level,type,group_boss_id',
        ])->toArray();

        $departmentTotalList = SysDepartmentModel::find([
            'columns' => 'id,name,ancestry_v3,level,type,group_boss_id',
        ])->toArray();
        $departmentListArr = array_column($departmentTotalList, null, 'id');

        $c_level = SysDepartmentModel::find([
            'conditions' => 'deleted = 0 and type = 4',
            'columns' => 'id',
        ])->toArray();
        $c_level = array_column($c_level, 'id');

        foreach ($departmentList as &$department) {
            $departmentTree = $this->genDepartmentTree($department['id'], $departmentListArr, $c_level);
            if (empty($departmentTree)) {
                continue;
            }
            $department = array_merge($department, $departmentTree);
        }
        return $departmentList;
    }

    public function getTotalValidDepartment()
    {
        return SysDepartmentModel::find([
            'conditions' => 'deleted = 0',
            'columns' => 'id',
        ])->toArray();
    }

    /**
     * @description 生成部门树
     * 共7级
     * 第一级 CEO Group
     * 第二级 C Level
     * 第三级 BU
     * 第四级 一级部门
     * 第五级 二级部门
     * 第六级 三级部门
     * 第七级 四级部门
     *
     * @param $department_id
     * @param $department_list
     * @param $c_level_list
     * @return array
     */
    private function genDepartmentTree($department_id, $department_list, $c_level_list): array
    {
        $departmentInfo = $department_list[$department_id];
        if (empty($departmentInfo)) {
            return [];
        }
        if (empty($departmentInfo['ancestry_v3'])) {
            return [];
        }
        $chainList = explode('/', $departmentInfo['ancestry_v3']);

        $result = [];
        foreach ($chainList as $k => $v) {

            if (!isset($department_list[$v])) {
                continue;
            }

            if ($k == 0) {
                $columnKey = self::COLUMN_KEY_DEPARTMENT_TREE_CEO_GROUP;
            } else if ($k == 1) {

                if (in_array($v, $c_level_list)) {
                    $columnKey = self::COLUMN_KEY_DEPARTMENT_TREE_CEO_LEVEL;
                } else {
                    $columnKey = $this->getKey($department_list[$v]['level']);
                }
            } else {
                $columnKey = $this->getKey($department_list[$v]['level']);
            }
            $result[$columnKey . '_id'] = $v;
            $result[$columnKey . '_name'] = $department_list[$v]['name'] ?? '';
        }

        //补全
        for ($i = 0;$i <= 6; $i++) {

            if (!isset($result[$i . '_id'])) {
                $result[$i . '_id']   = 0;
                $result[$i . '_name'] = '-';
            }
        }
        return $result;
    }

    private function getKey($level)
    {
        $key = self::COLUMN_KEY_DEPARTMENT_TREE_LEVEL_4TH;
        switch ($level) {
            case 0:
                $key = self::COLUMN_KEY_DEPARTMENT_TREE_BU;
                break;
            case 1:
                $key = self::COLUMN_KEY_DEPARTMENT_TREE_LEVEL_1ST;
                break;
            case 2:
                $key = self::COLUMN_KEY_DEPARTMENT_TREE_LEVEL_2ND;
                break;
            case 3:
                $key = self::COLUMN_KEY_DEPARTMENT_TREE_LEVEL_3RD;
                break;
            case 4:
                $key = self::COLUMN_KEY_DEPARTMENT_TREE_LEVEL_4TH;
                break;
            default:
                break;

        }
        return $key;
    }

    /**
     * @param $list
     * @param $versionId
     * @param $params
     * @return array
     */
    public function calculate($list, $versionId, $params)
    {
        $budgetCount         = $params['budgetCount'];
        $onJobCountTrans     = $params['onJobCountTrans'];
        $onJobCount          = $params['onJobCount'];
        $pendingCount        = $params['pendingCount'];
        $demandCount         = $params['demandCount'];
        $demandCountWait     = $params['demandCountWait'];
        $departmentList      = $params['departmentList'];
        $validDepartment     = $params['validDepartment'];
        $suspendCount        = $params['suspendCount'];
        $waitLeaveCount      = $params['waitLeaveCount'];
        $jobDepartmentRelate = $params['jobDepartmentRelate'];
        $pendingJobTransfer  = $params['pendingJobTransfer'];
        $insertArray         = $insertShareGroupArray = []; //待插入数据
        $dataShare           = []; //共享预算的数据

        //共享预算server
        $shareServer = HcShareBudgetServer::getInstance()->init();

        //拼装入库数据
        foreach ($list as $value) {
            //索引
            $uniquesIndex = sprintf('%d-%d', $value['job_title_id'], $value['dept_id']);
            if (empty($value['job_title_id']) || empty($value['dept_id'])) { //过滤数据
                continue;
            }

            if (!isset($validDepartment[$value['dept_id']]) || !isset($departmentList[$value['dept_id']]) || !isset($jobDepartmentRelate[$uniquesIndex])) {
                continue;
            }
            $departmentInfo               = $departmentList[$value['dept_id']];
            $data['version_id']           = $versionId;
            $data['job_title_id']         = $value['job_title_id'];
            $data['department_id']        = $value['dept_id'];
            $data['store_id']             = '';
            $data['department_id_0']      = $departmentInfo['0_id'];
            $data['department_name_0']    = $departmentInfo['0_name'];
            $data['department_id_1']      = $departmentInfo['1_id'];
            $data['department_name_1']    = $departmentInfo['1_name'];
            $data['department_id_2']      = $departmentInfo['2_id'];
            $data['department_name_2']    = $departmentInfo['2_name'];
            $data['department_id_3']      = $departmentInfo['3_id'];
            $data['department_name_3']    = $departmentInfo['3_name'];
            $data['department_id_4']      = $departmentInfo['4_id'];
            $data['department_name_4']    = $departmentInfo['4_name'];
            $data['department_id_5']      = $departmentInfo['5_id'];
            $data['department_name_5']    = $departmentInfo['5_name'];
            $data['department_id_6']      = $departmentInfo['6_id'];
            $data['department_name_6']    = $departmentInfo['6_name'];
            $data['budget']               = $budgetCount[$uniquesIndex] ?? 0;                                       //获取全部预算人数
            $data['job_trans']            = $onJobCountTrans[$uniquesIndex] ?? 0;                                   //转岗待转出人数
            $data['employee_in_service']  = ($onJobCount[$uniquesIndex] ?? 0) - $data['job_trans'];                 //在职人数 = 在职人数 - 转岗待转出人数
            $data['hc_pending']           = $pendingCount[$uniquesIndex] ?? 0;                                      //获取待入职人数
            $data['hc_demand']            = $demandCount[$uniquesIndex] ?? 0;                                       //获取全部招聘中人数
            $data['hc_demand_wait']       = $demandCountWait[$uniquesIndex] ?? 0;                                   //获取已提交的HC总人数（待审批）
            $data['share_job_title_ids']  = '';                                                                     //共享职位
            $data['share_department_ids'] = '';                                                                     //共享部门
            $data['type']                 = self::DATA_TYPE_NORMAL;                                                 //类型 1=普通数据,2=汇总数据,3=共享数据
            $data['budget_real']          = $data['budget'];                                                        //真实非共享预算的预算总数,如果是共享预算的话,只有一个职位-部门存在值,其他为0
            $data['employee_suspend']     = $suspendCount[$uniquesIndex] ?? 0;                                      //停职人数
            $data['employee_wait_leave']  = $waitLeaveCount[$uniquesIndex] ?? 0;                                    //待离职人数
            $data['share_group_id']       = null;

            if (isCountry($this->getPendingEntryCountry())) {
                $data['job_trans_entry'] = $pendingJobTransfer[$uniquesIndex] ?? 0;                                //待转入人数

                //在职人数 + 获取全部招聘中人数 + 的获取已提交的HC总人数 + 停职人数 + 待转入人数
                $total = $data['employee_in_service'] + $data['hc_pending'] + $data['hc_demand'] + $data['hc_demand_wait']
                    + $data['employee_suspend'] + $data['job_trans_entry'];
            } else {

                //在职人数 + 获取全部招聘中人数 + 的获取已提交的HC总人数 + 停职人数
                $total = $data['employee_in_service'] + $data['hc_pending'] + $data['hc_demand'] + $data['hc_demand_wait']
                    + $data['employee_suspend'];
            }

            //可提交的HC使用数 = 获取全部预算人数 - 已占坑数
            $data['hc_request'] = $data['hc_request_real'] = $data['budget'] - $total;

            //HC预算状态
            $data['hc_budget_state'] = $data['hc_request'] >= 0
                ? HrJobDepartmentRelationModel::HC_BUDGET_STATE_NORMAL
                : HrJobDepartmentRelationModel::HC_BUDGET_STATE_OVERRUN;

            //如部门是共享部门
            //主要是要计算 hc_request、hc_request_real、budget_real、hc_budget_state
            //hc_request 如果职位A、B共享，则，A、B的hc_request值是一致的，即：共享预算 - (A已占坑数 + B已占坑数)
            //hc_request_real用于统计总和;如果职位A、B共享, hc_request_real = hc_request, 但是只有一个职位有值,其他为0
            //budget_real用于统计总和
            //hc_budget_state用于展示是否超预算,需要根据hc_request来判断

            if ($shareServer->isShare($value['dept_id'], $value['job_title_id'])) {

                //如果存在数据或者 该共享职位的部门就没申请过预算
                if (isset($insertArray[$uniquesIndex])) {
                    continue;
                }
                $data['share_group_id'] = $shareServer->getShareGroupId($value['dept_id'], $value['job_title_id']);
                $shareConfig = $shareServer->getShareGroup($value['dept_id'], $value['job_title_id']);

                //共享数据类型，共享部门，共享职位
                $data['type']                 = self::DATA_TYPE_SHARE;
                $data['share_department_ids'] = join(',', array_keys($shareConfig));
                $data['share_job_title_ids']  = join(',', current($shareConfig));

                //获取共享组ID
                $shareGroupId = $shareServer->getShareGroupId($value['dept_id'], $value['job_title_id']);

                //计算共享职位-部门可提交HC数量
                $data['budget_real'] = 0;
                if (isset($dataShare[$shareGroupId])) {
                    $dataShare[$shareGroupId]['hc_request_real'] -= $total;
                } else {
                    $dataShare[$shareGroupId] = [
                        'budget_real'     => $data['budget'],
                        'hc_request_real' => $data['hc_request'],
                    ];
                }
            }

            ksort($data);//排序
            $insertArray[$uniquesIndex] = $data;
        }

        //共享数据计算可提交的HC使用数
        if (!empty($dataShare)) {
            foreach ($dataShare as $shareGroupId => $shareGroupData) {

                //共享组
                $shareGroupArr = $shareServer->getUniqueKeyByGroupId($shareGroupId);
                foreach ($shareGroupArr as $index => $uniqueKey) {

                    //如果当前共享预算不存在
                    if (!isset($insertArray[$uniqueKey])) {
                        continue;
                    }

                    //共享组中的每一项的剩余人数相同
                    $insertArray[$uniqueKey]['hc_request']      = $shareGroupData['hc_request_real'];
                    $insertArray[$uniqueKey]['hc_request_real'] = 0;
                    if (!isset($insertShareGroupArray[$shareGroupId])) {
                        $insertShareGroupArray[$shareGroupId] = [
                            'version_id'      => $versionId,
                            'share_group_id'  => $shareGroupId,
                            'budget_real'     => $shareGroupData['budget_real'],
                            'hc_request_real' => $shareGroupData['hc_request_real'],
                        ];
                    }

                    //共享组中的每一项的是否超出预算状态是相同的
                    $insertArray[$uniqueKey]['hc_budget_state'] = $insertArray[$uniqueKey]['hc_request'] >= 0
                        ? HrJobDepartmentRelationModel::HC_BUDGET_STATE_NORMAL
                        : HrJobDepartmentRelationModel::HC_BUDGET_STATE_OVERRUN;
                }
            }
        }
        $this->logger->write_log("[assembly_list]" . json_encode($insertArray), 'info');

        //列表数据汇总
        //位置：列表最下一行有一行数据汇总行
        $sum_data = [
            'job_title_id'         => '0',
            'department_id'        => '0',
            'store_id'             => '',
            'department_id_0'      => 0,
            'department_name_0'    => '',
            'department_id_1'      => 0,
            'department_name_1'    => '',
            'department_id_2'      => 0,
            'department_name_2'    => '',
            'department_id_3'      => 0,
            'department_name_3'    => '',
            'department_id_4'      => 0,
            'department_name_4'    => '',
            'department_id_5'      => 0,
            'department_name_5'    => '',
            'department_id_6'      => 0,
            'department_name_6'    => '',
            'version_id'           => $versionId,
            //获取全部预算人数
            'budget'               => array_sum((array_column($insertArray, 'budget_real'))),
            //转岗待转出人数
            'job_trans'            => array_sum((array_column($insertArray, 'job_trans'))),
            //在职人数 == 在职人数-转岗待转出人数
            'employee_in_service'  => array_sum((array_column($insertArray, 'employee_in_service'))),
            //获取待入职人数
            'hc_pending'           => array_sum((array_column($insertArray, 'hc_pending'))),
            //获取全部预算人数
            'hc_demand'            => array_sum((array_column($insertArray, 'hc_demand'))),
            //获取 部门 网点 职位 的获取已提交的HC总人数（待审批）
            'hc_demand_wait'       => array_sum((array_column($insertArray, 'hc_demand_wait'))),
            'share_job_title_ids'  => '',
            'share_department_ids' => '',
            //1=普通数据,2=汇总数据,3=共享数据
            'type'                 => self::DATA_TYPE_AGGREGATION,
            'budget_real'          => 0,
            //可提交的HC使用数
            'hc_request'           => array_sum((array_column($insertArray, 'hc_request_real'))),
            //可提交的HC使用数
            'hc_request_real'      => 0,
            'hc_budget_state'      => HrJobDepartmentRelationModel::HC_BUDGET_STATE_NORMAL,
            'employee_suspend'     => array_sum((array_column($insertArray, 'employee_suspend'))),
            'employee_wait_leave'  => array_sum((array_column($insertArray, 'employee_wait_leave'))),
            'share_group_id'       => null,
        ];

        if (isCountry($this->getPendingEntryCountry())) {
            $sum_data['job_trans_entry'] = array_sum((array_column($insertArray, 'job_trans_entry')));
        }

        ksort($sum_data); ////排序
        $insertArray[] = $sum_data;

        return [$insertArray, array_values($insertShareGroupArray)];
    }

    /**
     * @description 获取在职人数
     * 指定部门、职位、工作所在国家, 在职、在编、不含子账号、不含实习生、不含待离职
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getOnJobStaffCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        $conditions = 'node_department_id = :node_department_id: and job_title in ({job_title_id:array}) and
                       formal in(1,4) and state = 1 and is_sub_staff = 0 and wait_leave_state = 0 and 
                       working_country = :working_country:';
        $bind       = [
            'node_department_id' => $departmentId,
            'job_title_id'       => $jobTitle,
            'working_country'    => WorkingCountryEnums::getWorkingCountry(),
        ];

        //获取指定部门、网点、职位的，在职、在编、非子账号的人数
        return HrStaffInfoModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * @description 获取停职人数
     * 指定部门、职位、工作所在国家, 在职、在编、不含子账号、不含实习生、不含待离职
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getSuspendCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        $conditions = 'node_department_id = :node_department_id: and job_title in ({job_title_id:array}) and
                       formal in(1,4) and state = 3 and is_sub_staff = 0 and wait_leave_state = 0 and 
                       working_country = :working_country:';
        $bind       = [
            'node_department_id' => $departmentId,
            'job_title_id'       => $jobTitle,
            'working_country'    => WorkingCountryEnums::getWorkingCountry(),
        ];

        //获取指定部门、网点、职位的，在职、在编、非子账号的人数
        return HrStaffInfoModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * @description 获取待离职人数
     * 指定部门、职位、工作所在国家, 在职、在编、不含子账号、不含实习生、不含待离职
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getWaitLeaveCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        $conditions = 'node_department_id = :node_department_id: and job_title in ({job_title_id:array}) and
                       formal in(1,4) and state = 1 and is_sub_staff = 0 and wait_leave_state = 1 and 
                       working_country = :working_country:';
        $bind       = [
            'node_department_id' => $departmentId,
            'job_title_id'       => $jobTitle,
            'working_country'    => WorkingCountryEnums::getWorkingCountry(),
        ];

        //获取指定部门、网点、职位的，在职、在编、非子账号的人数
        return HrStaffInfoModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * @description 待转入人数
     * @return mixed
     */
    public function getJobTransferPendingEntry($departmentId, $storeId, $jobTitle): int
    {
        if (!isCountry($this->getPendingEntryCountry())) {
            return 0;
        }
        //审核通过，待转岗
        $conditions = 'approval_state in(1,2) and state = 1 and after_department_id = :department_id: and after_position_id in ({job_title_id:array}) ';
        $bind = [];
        $bind['department_id'] = $departmentId;
        $bind['job_title_id'] = $jobTitle;

        return JobTransferModel::count(
            [
                'conditions' => $conditions,
                'bind'       => $bind,
            ]
        );
    }

    /**
     * @description 获取待入职人数
     * 新逻辑：进入到入职表数据
     *  旧逻辑存在漏洞，存在部分总部的人不上传附件还发了offer，却没有入到入职表
     * 旧逻辑：已发offer人数
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getPendingEntryCount($departmentId, $storeId, $jobTitle = null): int
    {
        if (empty($departmentId)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("count(1) as cou");
        $builder->from(['h' => HrHcModel::class]);
        $builder->leftJoin(HrEntryModel::class,'e.hc_id = h.hc_id','e');
        $builder->andWhere('e.deleted = :deleted:', ['deleted' => Enums::IS_DELETED_NO]);
        $builder->andWhere('e.status = :status:', ['status' => HrEntryModel::STATUS_TO_BE_EMPLOYED]);
        $builder->andWhere('h.department_id = :dept_id:', ['dept_id' => $departmentId]);
        $builder->inWhere('h.job_title', $jobTitle);
        $count = $builder->getQuery()->execute()->getFirst();

        return intval($count->cou);
    }

    /**
     * 获取招聘中人数
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return int
     */
    public function getSurplusCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }
        $conditions = 'state_code = 2 and department_id = :department_id: and
                            job_title in ({job_title_id:array})  and deleted = 1';
        $bind       = [
            'department_id' => $departmentId,
            'job_title_id'  => $jobTitle,
        ];
        $count      = HrHcModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => "sum(surplusnumber) as cou",
        ])->toArray();

        return $count['cou'] ?? 0;
    }

    /**
     * @desc 获取已提交的HC总人数（待审批）
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return int
     */
    public function getBudgetAdoptCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }
        $conditions = 'state_code = 1 and approval_state_code = 7 and department_id = :department_id: and job_title IN({job_title_id:array})  and deleted = 1';
        $bind = [
            'department_id' => $departmentId,
            'job_title_id'  => $jobTitle,
        ];
        $count = HrHcModel::sum([
            'conditions' => $conditions,
            'bind'       => $bind,
            'column'     => 'demandnumber',
        ]);
        return $count ?? 0;
    }

    /**
     * 获得待转岗人数
     * @param $department_id
     * @param $store_id
     * @param $jobTitleIds
     * @return int
     */
    public function getJobTransferCount($department_id, $store_id, $jobTitleIds)
    {
        //审核通过，待转岗
        $conditions = 'approval_state =2 and state=1 and current_department_id = :department_id: and current_position_id in ({job_title_id:array}) ';
        $bind = [];
        $bind['department_id'] = $department_id;
        $bind['job_title_id'] = $jobTitleIds;

        return JobTransferModel::count(
            [
                'conditions' => $conditions,
                'bind'=>$bind
            ]
        );
    }

    /**
     * 获取预算人数
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getHrStaffing($departmentId, $storeId, $jobTitle) : int
    {
        $this->logger->write_log('getHrStaffing: params - departmentId:' . $departmentId . '-jobTitle:' . $jobTitle,
            'info');

        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        //获取指定部门、职位的预算数
        //https://flashexpress.feishu.cn/docx/HqlSdfGeLo1eJ0xPMQ7cTQBJnkd
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("count");
        $builder->from(HRStaffingModel::class);
        $builder->andWhere('dept_id = :dept_id:', ['dept_id' => $departmentId]);
        $builder->andWhere('job_title_id = :job_title_id:', ['job_title_id' => $jobTitle]);
        $list = $builder->getQuery()->execute()->getFirst();
        $count = 0;
        if (!empty($list)) {
            $listArr = $list->toArray();
            $count   = $listArr['count'] ?? 0;
        }

        $this->logger->write_log('getHrStaffing: params - departmentId:' . $departmentId . '-jobTitle:' . $jobTitle . ' count:' . $count,
            'info');

        return $count;
    }

    /**
     * @description 获取HC预算剩余数量
     * @param $total
     * @param $departmentId
     * @param $storeId
     * @param $jobTitleId
     * @return mixed
     */
    public function getSurplusHcRequestCount($total, $departmentId, $storeId, $jobTitleId)
    {
        if ($total <= 0 || empty($departmentId) || empty($jobTitleId)) {
            return 0;
        }
        $occupyBudgetStaffCount = 0;

        //是否在共享组
        $shareServer = HcShareBudgetServer::getInstance()->init();
        if ($shareServer->isShare($departmentId, $jobTitleId)) {
            $shareConfig = $shareServer->getShareGroup($departmentId, $jobTitleId);
            foreach ($shareConfig as $shareDepartmentId => $shareJobTitleId) {
                $occupyBudgetStaffCount += $this->getOccupyBudgetStaffCount($shareDepartmentId, $storeId, $shareJobTitleId);
            }
        } else {
            $occupyBudgetStaffCount = $this->getOccupyBudgetStaffCount($departmentId, $storeId, [$jobTitleId]);
        }

        //HC预算剩余数量: 预算数 - 占用预算数
        return $total - $occupyBudgetStaffCount;
    }

    /**
     * 获取占用预算人数
     * @description 占用预算数: 在职人数 + 待入职人数 + 招聘中人数 + 已提交HC总人数(待审批) - 待转出转岗人数 + 停职人数 + 待转入人数
     * @param $department_id
     * @param $store_id
     * @param $job_title_ids
     * @return int
     */
    public function getOccupyBudgetStaffCount($department_id, $store_id, $job_title_ids)
    {
        //1.获取在职人数
        $onJobCnt = $this->getOnJobStaffCount($department_id, null, $job_title_ids);
        //2.获取待入职人数
        $pendingEntryCnt = $this->getPendingEntryCount($department_id, null, $job_title_ids);
        //3.获取招聘中人数
        $surplusCnt = $this->getSurplusCount($department_id, null, $job_title_ids);
        //4.获取已提交的HC总人数（待审批）
        $adoptCon = $this->getBudgetAdoptCount($department_id, null, $job_title_ids);
        //5.待转出转岗人数
        $transferCnt = $this->getJobTransferCount($department_id, null, $job_title_ids);
        //6.停职人数
        $suspend = $this->getSuspendCount($department_id, null, $job_title_ids);
        //7.待离职人数
        //$waitLeave = $this->getWaitLeaveCount($departmentId, null, $jobTitle);
        //8.待转入人数
        $jobTransferPendingEntry = $this->getJobTransferPendingEntry($department_id, null, $job_title_ids);

        $leftCount = $onJobCnt + $pendingEntryCnt + $surplusCnt + $adoptCon - $transferCnt + $suspend + $jobTransferPendingEntry;
        $this->logger->write_log(sprintf("getSurplusHcRequestCount %s %s: onJobCnt: %s,pendingEntryCnt: %s,surplusCnt: %s,adoptCon:%s,transferCnt:%s,suspend:%s,pendingEntry:%s,leftCount:%s.",
            $department_id, json_encode($job_title_ids), $onJobCnt, $pendingEntryCnt, $surplusCnt, $adoptCon, $transferCnt,
            $suspend, $jobTransferPendingEntry, $leftCount), 'info');
        $this->logger->write_log(['获取在职人数'=>$onJobCnt,'获取待入职人数'=>$pendingEntryCnt,'获取招聘中人数'=>$surplusCnt,'获取已提交的HC总人数'=>$adoptCon,'获取待转出转岗人数'=>$transferCnt,'获取停职人数'=>$suspend,'获取待转入人数'=>$jobTransferPendingEntry,'占用预算数'=>$leftCount],'info');


        return $leftCount;
    }

    /**
     * 实装待入职人数国家
     * @return string[]
     */
    public function getPendingEntryCountry(): array
    {
        return ['TH', 'MY', 'PH'];
    }
}