<?php

namespace FlashExpress\bi\App\Server;


use App\Country\Tools;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use Exception;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Models\backyard\SysDistrictModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;


class SystemExternalApprovalServer extends AuditBaseServer
{
    public const param_operation_type_1 = 1; //添加审批
    public const param_operation_type_2 = 2; ////审批操作
    public const param_operation_type_3 = 3; //编辑
    public const param_operation_type_4 = 4; //获取详情
    public const param_operation_type_5 = 5; //获取其他接口(众包 变更记录)
    public const param_operation_type_6 = 6; //获取其他接口(众包 获取车辆规格)
    public const param_operation_type_7 = 7; //获取其他接口(众包 获取详情)
    public const param_operation_type_8 = 8; //获取费用类型
    public const param_operation_type_9 = 9; //获取出车凭证
    public const param_operation_type_10 = 10; //添加审批
    public const param_operation_type_11 = 11; ////审批操作
    public const param_operation_type_12 = 12; ////上传图片
    public const param_operation_type_13 = 13; //众包结算审批 详情

    public $timezone;
    protected $bizTypeArr = [];

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone   = $timezone;
        $this->setApprovalType(AuditListEnums::getAuditTypeOfOnlyProvideService()); //外部审核
    }

    /**
     * 设置有效的审批类型
     * @return void
     */
    public function setApprovalType($type)
    {
        $this->bizTypeArr = $type;
    }

    /**
     * 设置有效的审批类型
     * @return array
     */
    public function getApprovalType(): array
    {
        return $this->bizTypeArr;
    }

    /**
     * @description: 转发机制
     * @param $paramIn  array
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/20 18:07
     */
    public function forwardParamIn($paramIn = [])
    {
        $bizType              = $this->processingDefault($paramIn, 'biz_type', 1);                      // 申请类型
        $param_operation_type = $this->processingDefault($paramIn, 'param_operation_type', 1);          // 操作类型
        $user                 = $this->processingDefault($paramIn, 'param_operation_staff_id', 1);      // 操作人
        $result['code']       = ErrCode::FAIL;
        $result['msg']        = $this->getTranslation()->_('no_server');

        try {
            if (!in_array($bizType, $this->getApprovalType())) {
                throw new ValidationException($this->getTranslation()->_('2201'));
            }

            //判断转发地址, 回传给外部系统
            $api_svc = [];
            switch ($bizType) {
                //FMS
                case AuditListEnums::APPROVAL_TYPE_SYSTEM_CS:
                case AuditListEnums::APPROVAL_TYPE_SYSTEM_DRIVER_BLACKLIST:
                case AuditListEnums::APPROVAL_TYPE_CS_PAYMENT:
                    if(!isset($paramIn['external_staff_id'])){
                        $paramIn['external_staff_id'] = $user;
                    }
                    //增加操作人
                    $paramIn['operator_id'] = $user;
                    //获取众包的回调地址
                    $api_svc = $this->getCrowdsourcesSvc($param_operation_type, $paramIn);
                    break;
                //turboRoute
                case AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE:
                case AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE_FREIGHT:
                case AuditListEnums::APPROVAL_TYPE_FLEET_GENERATE_RECORDS:
                case AuditListEnums::APPROVAL_TYPE_VEHICLE_APPROVAL:
                case AuditListEnums::APPROVAL_TYPE_TRANSPORTATION_CONTRACT:
                case AuditListEnums::APPROVAL_TYPE_PRICE_LIST:
                case AuditListEnums::APPROVAL_TYPE_SINGLE_FARE_ADJUSTMENT:
                    //添加申请人
                    if(!isset($paramIn['external_staff_id'])){
                        $paramIn['external_staff_id'] = $user;
                    }
                    //增加操作人
                    $paramIn['operator_id'] = $user;
                    //获取众包的回调地址
                    $api_svc = $this->getTurboRouteSvc($param_operation_type, $paramIn);
                    break;
                case AuditListEnums::APPROVAL_TYPE_MILEAGE: // 虚假里程
                    $result['method'] = 'approval.get_info';
                    $result['paramIn'] = $paramIn;
                    $result['sys'] =  isCountry('TH') ? 'ard_api' : 'bi_rpcv2';
                    $api_svc = $result;
                    break;
                case AuditListEnums::APPROVAL_TYPE_STOP:
                    // 停职审批
                    $result['method']  = 'corruption.approvalCallBack';
                    $result['paramIn'] = $paramIn;
                    $result['sys']     = 'ard_api';
                    $api_svc           = $result;
                    break;
                case Enums::$audit_type['FRANCHISEES']:
                    $result['paramIn'] = $paramIn;
                    $result['sys'] =  'fra';
                    $api_svc = $result;
                    break;
                case AuditListEnums::APPROVAL_TYPE_SCHOOL_PLAN_SEND:
                    // 培训计划
                    $result['method']  = 'learn_plan_audit_sync_data';
                    $result['paramIn'] = $paramIn;
                    $result['sys']     = 'school_rpc';
                    $api_svc           = $result;
                    break;
                case AuditListEnums::APPROVAL_TYPE_VEHICLE_REPAIR_REQUEST:
                    //车辆维修申请 - 回调地址
                    $api_svc = $this->getVehicleRepairRequestRouteSvc($param_operation_type, $paramIn);
                    break;
                default:
                    break;
            }
            if (empty($api_svc)) {
                throw new \Exception('获取 svc 地址异常  ');
            }
            $sys              = $api_svc['sys'] ?? '';
            $module           = $api_svc['module'] ?? '';
            $method           = $api_svc['method'] ?? '';
            $paramIn          = $api_svc['paramIn'] ?? $paramIn;  //这里重新获取了  getCrowdsourcesSvc 的
            $http_url         = $api_svc['http_url'] ?? '';
            $url_method       = $api_svc['url_method'] ?? '';
            $http_url_is_post = $api_svc['http_url_is_post'] ?? true;
            $this->logger->write_log("forwardParamIn_rpc api_svc : " . json_encode($api_svc,
                    JSON_UNESCAPED_UNICODE) . " paramIn:" . json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');
            if(!empty($sys)) {
                if ($sys == 'fra'){
                    $api = new RestClient('fra');
                    $res = $api->execute(RestClient::METHOD_POST, 'svc/franchisee/approval/franchiseesApprovalInfo',
                        $paramIn, ['Accept-Language' => $this->lang]);
                    $this->logger->write_log("forwardParamIn fra api_svc : " . json_encode($res,
                            JSON_UNESCAPED_UNICODE) . " paramIn:" . json_encode($paramIn, JSON_UNESCAPED_UNICODE),
                        'info');
                    $return['result']    = $res;
                }else{
                    //[3]发送RPC请求
                    $rpcClient = new ApiClient($sys, $module, $method, $this->lang);
                    $rpcClient->setParams($paramIn);
                    $return = $rpcClient->execute();
                }
            }else {
                //发送 http 请求
                //默认 post  $http_url_is_post
                $http_url         .= $url_method;
                $return['result'] = $this->httpPost($http_url, $paramIn, [], 30, $http_url_is_post);
            }
            $this->logger->write_log("forwardParamIn_rpc return:" . json_encode($return, JSON_UNESCAPED_UNICODE), 'info');


	        if (isset($return['result']['code']) && $return['result']['code'] ==  ErrCode::SUCCESS) {
		        $result['code'] =  ErrCode::SUCCESS;
		        $result['msg'] = $return['result']['message'] ?? $this->getTranslation()->_('5001');
		        $result['data'] =  $return['result']['data'] ?? [];
	        } else if((isset($return['result']['code']) && $return['result']['code'] ==  ErrCode::ERROR) || (isset($paramIn['is_use_redis_list_key']) && !empty($paramIn['is_use_redis_list_key']))){
		        //== 0 的是可提示的数据  || 走队列的数据 会重试  不在这走 Exception
		        throw new ValidationException( $return['result']['message'] ?? $result['msg']);

	        }  else {
		        $result['msg'] = $return['result']['message'] ?? $result['msg'];
		        //throw new \Exception('返回数据异常  ' . json_encode($return, JSON_UNESCAPED_UNICODE));
	        }
        } catch (ValidationException $ve) {
            $this->logger->write_log('forwardParamIn_rpc  msg  ' . $ve->getMessage() . ' paramIn=> ' . json_encode($paramIn),
                "info");
            $result['code'] = ErrCode::VALIDATE_ERROR;
            $result['msg']  = $ve->getMessage();
        } catch (\Exception $e) {
            $this->logger->write_log('forwardParamIn_rpc  ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString() . ' paramIn=> ' . json_encode($paramIn),
                "error");
        }
        return $this->checkReturn($result);
    }

    /**
     * 详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        try {
            //获取详情
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('getDetail  没有找到数据  ' . $auditId);
            }
            $result = $SystemExternalApprovalModel->toArray();
            //获取详情
            $forwardParamIn = [
                'biz_type'=>$result['biz_type'],
                'param_operation_type'=>self::param_operation_type_4,
                'param_operation_staff_id'=>$user,
                'serial_no'=>$result['serial_no'],
            ];
            $svc_result = $this->forwardParamIn($forwardParamIn);
            if(!isset($svc_result['code']) || $svc_result['code'] != 1){
                throw new \Exception('getDetail  获取详情失败  ' . $auditId);
            }
            $auditList = new AuditlistRepository($this->lang, $this->timezone);
            $returnData['data'] = $svc_result['data'] ?? [];
            $add_hour = $this->getDI()['config']['application']['add_hour'];
            $data = [
                'title'                => $auditList->getAudityType($result['biz_type']),
                'id'                   => $result['id'],
                'staff_id'             => $result['submitter_id'],
                'type'                 => $result['biz_type'],
                'created_at'           => date('Y-m-d H:i:s',(strtotime($result['created_at']) + $add_hour * 3600)),
                'updated_at'           => date('Y-m-d H:i:s',(strtotime($result['updated_at']) + $add_hour * 3600)),
                'status'               => $result['state'],
                'serial_no'            => $result['serial_no'] ?? '',
                'biz_type'             => $result['biz_type'],
                'is_update'            => 0,  // 1 可以编辑 0 不能编辑
            ];
            $state = 0;
            //当前用户是否已经审批
            if ($comeFrom == 2) { //获取审批人的审批状态
                $data['is_update'] = $state == enums::APPROVAL_STATUS_PENDING ? enums::APPROVAL_STATUS_PENDING : $data['is_update'];
            }
            $returnData['data']['head']             = $data;

            return $returnData;
        }catch (ValidationException $e){
            $this->getDI()->get("logger")->write_log('getDetail  msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString(), "error");
            return [];
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log('getDetail msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString(), "error");
            return [];
        }
        return $returnData;
    }


    /**
     * @description:众包申请回调svc 地址  和重新封装$paramIn
     * @param $param_operation_type  int
     * @param $paramIn               array
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/23 10:01
     */
    public function  getCrowdsourcesSvc(int $param_operation_type, array $paramIn)
    {
        $result=[];
        //获取fms地址
        $setting_env = new SettingEnvServer();
        $result['http_url'] = $setting_env->getSetVal('fms_http_url');

        switch ($param_operation_type) {
            case self::param_operation_type_1 :
                //创建
                $result['url_method'] = '/svc/crowdsourcing/apply/add';
                $paramIn['type'] = 1;//添加

                break;
            case self::param_operation_type_2 :
                //审批
                $result['url_method'] = '/svc/approve/state';
                $paramIn['initiate_time'] =  time(); //审批时间

                break;
            case self::param_operation_type_3 :
                //编辑
                $result['url_method'] = '/svc/crowdsourcing/apply/add';
                $paramIn['initiate_time'] =  time(); //审批时间
                $paramIn['type'] = 2;//编辑

                break;
            case self::param_operation_type_4 :
                //获取详情
                $serial_no = $paramIn['serial_no'];
                $result['url_method'] = '/svc/approve/detail';
                $result['url_method'] .= "?serial_no={$serial_no}";
                $result['http_url_is_post'] = false; // get 请求

                break;
            case self::param_operation_type_5 :
                //获取其他接口(众包 变更记录)
                $result['url_method'] = '/svc/crowdsourcing/apply/record';
                $serial_no = $paramIn['serial_no'];
                $result['url_method'] .= "?serial_no={$serial_no}";
                $result['http_url_is_post'] = false; // get 请求

                break;
            case self::param_operation_type_6 :
                //获取其他接口(众包 车辆规格)  已经弃用
                $result['url_method'] = '/svc/crowdsourcing/apply/carSizeList';
                $result['http_url_is_post'] = false; // get 请求

                break;
            case self::param_operation_type_7 :
                //获取其他接口(众包 获取详情)
                $result['url_method'] = '/svc/approve/detailUpdate';
                $serial_no = $paramIn['serial_no'];
                $result['url_method'] .= "?serial_no={$serial_no}";
                $result['http_url_is_post'] = false; // get 请求

                break;
            case self::param_operation_type_8 :
                //获取其他接口(众包 变更记录)
                $result['url_method'] = '/svc/abnormal/type';
                break;
            case self::param_operation_type_9:
                //获取其他接口(众包 变更记录)
                $result['url_method'] = '/svc/abnormal/proof/detail';
                $proofId = $paramIn['proof_id'];
                $result['url_method'] .= "?proofId={$proofId}";
                $result['http_url_is_post'] = false; // get 请求
                break;
            case self::param_operation_type_10:
                //获取其他接口(众包 变更记录)
                $result['url_method'] = '/svc/abnormal/apply';
                break;
            case self::param_operation_type_11:
                //获取其他接口(众包 变更记录)
                $result['url_method'] = '/svc/approve/update';
                break;
            case self::param_operation_type_12:
                $result['url_method'] = '/svc/abnormal/upload';
                break;
            default:
                $result = [];
                break;
        }
        if(!empty($result)){
            $result['paramIn'] = $paramIn;
        }
        return $result;
    }

    /**
     * @param int $param_operation_type
     * @param array $paramIn
     * @return array
     */
    public function getTurboRouteSvc(int $param_operation_type, array $paramIn): array
    {
        //获取turboroute地址
        $result             = [];
        $setting_env        = new SettingEnvServer();
        $result['http_url'] = $setting_env->getSetVal('turbo_route_http_url');

        switch ($param_operation_type) {
            case self::param_operation_type_2 :
                //审批
                $result['url_method'] = '/svc/approve/state';
                $paramIn['initiate_time'] =  time(); //审批时间
                break;
            case self::param_operation_type_4 :
                //获取详情
                $serial_no = $paramIn['serial_no'];
                $result['url_method'] = '/svc/approve/detail';
                $result['url_method'] .= "?serial_no={$serial_no}";
                $result['http_url_is_post'] = false; // get 请求
                break;
            case self::param_operation_type_8 :
                //获取其他接口(众包 变更记录)
                $result['url_method'] = '/svc/abnormal/type';
                break;
            case self::param_operation_type_9:
                //获取其他接口(众包 变更记录)
                $result['url_method'] = '/svc/abnormal/proof/detail';
                $proofId = $paramIn['proof_id'];
                $result['url_method'] .= "?proofId={$proofId}";
                $result['http_url_is_post'] = false; // get 请求
                break;
            case self::param_operation_type_10:
                //获取其他接口(众包 变更记录)
                $result['url_method'] = '/svc/abnormal/apply';
                break;
            case self::param_operation_type_11:
                //获取其他接口(众包 变更记录)
                $result['url_method'] = '/svc/approve/update';
                break;
            case self::param_operation_type_12:
                $result['url_method'] = '/svc/abnormal/upload';
                break;
            default:
                $result = [];
                break;
        }
        if(!empty($result)){
            $result['paramIn'] = $paramIn;
        }
        return $result;
    }

    /**
     * @description: 众包编辑
     * @param $paramIn array
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/23 15:49
     */
    public function CrowdsourcingUpdate($paramIn){
        try {
            $this->getDI()->get("logger")->write_log('CrowdsourcingUpdate paramIn => '.json_encode($paramIn), "info");
            $audit_id = $this->processingDefault($paramIn, 'audit_id', 1);
            //查询数据
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($audit_id);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('CrowdsourcingUpdate  没有找到数据  '.$audit_id);
            }

            $is_update_approve     = $this->processingDefault($paramIn, 'is_update_approve', 1);      // 是否为编辑
            if($is_update_approve){ //如果存在 则是编辑审批
                //先进行编辑
                $paramIn['param_operation_type'] = self::param_operation_type_3;  //这是类型
                $paramIn['biz_type'] = enums::$audit_type['SYSTEM_CS'];  //这是众包
                $paramIn['external_staff_id'] = $SystemExternalApprovalModel->submitter_id; //申请人
                $forwardParamIn_result                          = $this->forwardParamIn($paramIn);
                if(!isset($forwardParamIn_result['code']) || $forwardParamIn_result['code'] != 1){
                    throw new \Exception($forwardParamIn_result['msg'] ?? 'CrowdsourcingUpdate update fail!');
                }
            }
            //在进行审批
            $updateApprove_paramIn = [
                'biz_type'    => Enums::$audit_type['SYSTEM_CS'],
                'id'          => $audit_id,
                'reason'      => $this->processingDefault($paramIn, 'reject_reason', 1), //驳回原因
                'status'      => $this->processingDefault($paramIn, 'status', 1),
                'operator_id' => $this->processingDefault($paramIn, 'param_operation_staff_id', 1),
            ];

            $result = $this->updateApprove($updateApprove_paramIn);
        }catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log('CrowdsourcingUpdate msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString().' paramIn => '.json_encode($paramIn), "error");
            return $this->checkReturn(-3, $e->getMessage() );
        }
        return $this->checkReturn([]);
    }

    /**
     * @description: 众包获取申请页面初始信息
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/21 15:36
     */
    public function getCrowdsourcingInformation($paramIn=[]){
        $result=[
            'store_id'=>'',
            'store_name'=>'',
            'store_address'=>'',
            'car_size_list'=>[],
        ];
        try {
            $staff_id = $this->processingDefault($paramIn, 'param_operation_staff_id', 1);      // 操作人

            //获取申请人网点
            $staff_info   = HrStaffInfoModel::findFirst(['conditions' => "staff_info_id = :staff_info_id: ",
                                                         'bind'       => [
                                                             'staff_info_id' => $staff_id,
                                                         ],
                                                         'columns'    => 'sys_store_id']);
            $sys_store_id = $staff_info ? $staff_info->sys_store_id : '';
            //获取网点名称和地址
            //网点名
            $store_info = SysStoreModel::findFirst([
                                                       'conditions' => "id = :store_id:",
                                                       'bind'       => [
                                                           'store_id' => $sys_store_id,
                                                       ],
                                                   ]);
            //网点一级地址
            $province_code = $store_info ? $store_info->province_code : '';
            //网点二级地址
            $city_code = $store_info ? $store_info->city_code : '';
            //网点三级地址
            $district_code = $store_info ? $store_info->district_code : '';
            //详细地址
            $detail_address = $store_info ? $store_info->detail_address : '';

            $provinceArr = SysProvinceModel::getProvincesArrByCodeArr([$province_code], 'code,name');
            $cityArr     = SysCityModel::getCitiesArrByCodeArr([$city_code], 'code,name');
            $districtArr = SysDistrictModel::getDistrictsArrByCodeArr([$district_code], 'code,name');
            $province    = trim($provinceArr[$province_code]['name'] ?? '');
            $city        = trim($cityArr[$city_code]['name'] ?? '');
            $district    = trim($districtArr[$district_code]['name'] ?? '');
            //网点名称
            $result['store_id'] = $sys_store_id;
            $result['store_name'] = $store_info ? $store_info->name : '';
            //网点详细地址 格式：详细地址+省市乡
            $result['store_address'] = " {$detail_address} {$province} {$city} {$district} ";



        }catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log('getCrowdsourcingInformation msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString(), "error");
            return $this->checkReturn(-3, $this->getTranslation()->_('no_server'));
        }
        return $this->checkReturn(['data'=>$result]);
    }

    /**
     * @description: 众包获取申请页面申请人负责的网点列表
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/21 15:36
     * select id from sys_manage_region where manager_id = '17245' and deleted = 0
     * select id from sys_manage_piece where manager_id = '17245' and deleted = 0
     * select id,name,province_code,city_code,district_code,detail_address from sys_store where  state = 1 and name like :store_name: and ( manager_id = '17245'  or manage_region in (1,2)  or manage_piece in (3,4) ) limit 20  offset 0
     */

    public function getJurisdictionStoreList( $paramIn = [] ) {
        $store_name = $this->processingDefault($paramIn, 'store_name', 1);      //搜索的网点名称
        $staff_id   = $this->processingDefault($paramIn, 'staff_id', 2);      // 操作人
        $page_num   = $this->processingDefault($paramIn, 'page_num', 2, 1);
        $page_size  = $this->processingDefault($paramIn, 'page_size', 2, 20);
        $offset     = $page_size * ($page_num - 1);
        $return_data = ['data' => ['list'=>[]]];
        if ((empty($store_name) && $store_name === '') || empty($staff_id)) { // 网点名字必须传哈
            return $this->checkReturn($return_data);
        }

        $staffsRoles = HrStaffInfoPositionModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => ['staff_info_id' => $staff_id],
            'columns' => 'position_category',
        ])->toArray();

        if(!in_array(RolesModel::ROLE_SYSTEM_MANAGER,array_column($staffsRoles,'position_category'))){
            //查询 负责的大区
            $region_list = SysManageRegionModel::find([
                'conditions' => 'manager_id = :staff_id: and deleted = 0',
                'bind'       => [
                    'staff_id' => $staff_id,
                ],
                'columns'    => 'id',
            ])->toArray();
            //查询 负责的片区
            $piece_list       = SysManagePieceModel::find([
                'conditions' => 'manager_id = :staff_id: and deleted = 0',
                'bind'       => [
                    'staff_id' => $staff_id,
                ],
                'columns'    => 'id',
            ])->toArray();
            $store_conditions = ' state = 1 and name like :store_name: and ( manager_id = :staff_id: ';
            $store_bind       = ['staff_id' => $staff_id, 'store_name' => "{$store_name}%"];
            if (!empty($region_list)) {
                $store_conditions .= ' or manage_region in ({manage_region_ids:array}) ';
                $store_bind['manage_region_ids']       = array_column($region_list, 'id');
            }
            if (!empty($piece_list)) {
                $store_conditions .= ' or manage_piece in ({piece_ids:array}) ';
                $store_bind['piece_ids']     = array_column($piece_list, 'id');
            }
            $store_conditions .= ' )';
        }else{
            $store_conditions = 'state = 1 and name like :store_name: and category in ({category:array})';
            $store_bind       = [
                'store_name' => "{$store_name}%",
                'category'   => [
                    enums::$stores_category['sp'],
                    enums::$stores_category['dc'],
                    enums::$stores_category['bdc'],
                ],
            ];
        }

        //查询负责的网点
        $store_list = SysStoreModel::find([
                                              'conditions' => $store_conditions,
                                              'bind'       => $store_bind,
                                              'columns'    => 'id,name,province_code,city_code,district_code,detail_address',
                                              'limit'      => $page_size,
                                              'offset'     => $offset,
                                          ])->toArray();
        if (!empty($store_list)) {
            $sysServer = new SysServer();

            $city_code     = array_values(array_unique(array_column($store_list, 'city_code')));
            $district_code = array_values(array_unique(array_column($store_list, 'district_code')));
            //查询省市区
            $provinceArr = array_column($sysServer->getSysProvinceList(), null, 'code');
            $cityArr       = $sysServer->getCitiesArrByCodeArrFromCache($city_code, 'code,name');
            $districtArr   = $sysServer->getDistrictsArrByCodeArrFromCache($district_code, 'code,name');

            foreach ($store_list as $k => &$v) {
                //网点一级地址
                $province = $provinceArr[$v['province_code']]['name'] ?? '';
                //网点二级地址
                $city = $cityArr[$v['city_code']]['name'] ?? '';
                //网点三级地址
                $district = $districtArr[$v['district_code']]['name'] ?? '';
                //网点详细址
                $detail_address = $v['detail_address'] ?? '';
                //网点详细地址 格式：详细地址+省市乡
                $return_data['data']['list'][] = [
                    'store_id'      => $v['id'],
                    'store_name'    => $v['name'],
                    'store_address' => "{$detail_address} {$province} {$city} {$district} ",
                    ];
            }
        }
        return $this->checkReturn($return_data);
    }



    /**
     * @description: 众包获取变更记录
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/27 16:21
     */
    public function getCrowdsourcingChangeLog($paramIn){
        try{
            $result              = $this->forwardParamIn($paramIn);
            if($result['code'] == 1 && !empty($result['data'])){
                $add_hour = $this->getDI()['config']['application']['add_hour'];
                foreach($result['data'] as  &$v){
                    //格式化时间  0 时区转换为本地时区
                    $v['external_time'] = isset($v['change_time']) ? date('Y-m-d H:i:s',$v['change_time'] + $add_hour * 3600) : '';
                }
            }

        }catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log('getCrowdsourcingChangeLog msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString(), "error");
            return $this->checkReturn(-3, $this->getTranslation()->_('no_server'));
        }
        return $this->checkReturn($result);

    }

    /**
     * 添加审批
     * @Access  public
     * @Param   array [submitter_id=> 提交人,summary_data=>'申请概要','biz_type'=>'申请类型']
     * @Return  array ['data'=>['serial_no' => '审批编号','create_time'=> '时间戳,没有任何转换']]
     */
    public function addApproval($paramIn = [])
    {
        //[1]参数定义
        $submitterId = $this->processingDefault($paramIn, 'submitter_id', 1);  // 提交人
        $summaryData = $this->processingDefault($paramIn, 'summary_data', 3);  // 申请概要 数组
        $bizType     = $this->processingDefault($paramIn, 'biz_type', 1);      // 申请类型
        $auditParams = $this->processingDefault($paramIn, 'audit_params', 3);  // 审批参数
        $formParams  = $this->processingDefault($paramIn, 'form_params', 3);   // 审批人查找参数
		$time        = time();

        $db = $this->getDI()->get("db");
        $db->begin();
        try {
            if (!in_array($bizType, $this->getApprovalType())) {
                throw new ValidationException($this->getTranslation()->_('err_msg_audit_type_not_valid'));
            }
            //获取个人信息
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = ?1',
                'bind' => [
                    1 => $submitterId,
                ],
            ]);
            if(empty($staffInfo)){
                throw new ValidationException($this->getTranslation()->_('1001'));
            }

            $serialNo = 'STEA' . $this->getRandomId();
            //插入业务表
            $insetData = [
                'serial_no'    => $serialNo,
                'submitter_id' => $submitterId,
                'biz_type'     => $bizType,
                'approval_parameters' => json_encode($auditParams ?? [], JSON_UNESCAPED_UNICODE),
                'form_parameters' => json_encode($formParams ?? [], JSON_UNESCAPED_UNICODE),
                'summary'      => json_encode($summaryData, JSON_UNESCAPED_UNICODE),
                'created_at'   => gmdate('Y-m-d H:i:s', $time),
                'state'        => enums::APPROVAL_STATUS_PENDING,
            ];
            $db->insertAsDict("system_external_approval", $insetData);
            $audit_id = $db->lastInsertId();
            $rst      = (new ApprovalServer($this->lang, $this->timezone))->create($audit_id, $bizType, $submitterId,null, ['from_submit' => $formParams], $serialNo);
            if (!$rst) {
                throw new \Exception('SystemExternalApprovalServer insert Add  workflow fail ' . $rst);
            }
            $db->commit();
        } catch (ValidationException $e){
            $db->rollBack();
            $this->logger->write_log('SystemExternalApprovalServer  msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString() .  " paramIn: " . json_encode($paramIn,JSON_UNESCAPED_UNICODE), "notice");
            return $this->checkReturn(-3, $e->getMessage());
        } catch (\Exception $e) {
            $db->rollBack();
            $this->logger->write_log('SystemExternalApprovalServer msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString(), "error");
            return $this->checkReturn(-3, $this->getTranslation()->_('no_server'));
        }
        return $this->checkReturn(['data'=>['serial_no' => $serialNo,'create_time'=> (string)$time]]);
    }

    /**
     *
     * @param $paramIn
     * @return array
     * @throws ValidationException
     * @throws InnerException
     */
    public function editApproval($paramIn = [])
    {
        //[1]参数定义
        $submitterId = $this->processingDefault($paramIn, 'submitter_id', 1);    // 提交人
        $serialNo    = $this->processingDefault($paramIn, 'serial_no', 1);       // 编号
        $bizType     = $this->processingDefault($paramIn, 'biz_type', 1);        // 申请类型
        $summaryData = $this->processingDefault($paramIn, 'summary_data', 3);  // 申请概要 数组
        $auditParams = $this->processingDefault($paramIn, 'audit_params', 3);  // 审批参数
        $formParams  = $this->processingDefault($paramIn, 'form_params', 3);  // 审批参数
        $time        = time();
        if (!in_array($bizType, $this->getApprovalType())) {
            throw new ValidationException($this->getTranslation()->_('err_msg_audit_type_not_valid'));
        }
        //获取个人信息
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = ?1',
            'bind' => [
                1 => $submitterId,
            ],
        ]);
        if(empty($staffInfo)){
            throw new ValidationException($this->getTranslation()->_('1001'));
        }

        $auditInfo = SystemExternalApprovalModel::findFirst([
            'conditions' => 'serial_no = :serial_no:',
            'bind' => [
                'serial_no' => $serialNo,
            ],
        ]);
        if (empty($auditInfo)) {
            throw new ValidationException('invalid serial_no');
        }
        if ($auditInfo['state'] == enums::APPROVAL_STATUS_PENDING) {
            throw new ValidationException('invalid audit status');
        }

        $db = $this->getDI()->get("db");
        $db->begin();

        try {
            $auditInfo->approval_parameters = json_encode($auditParams ?? [], JSON_UNESCAPED_UNICODE);
            $auditInfo->form_parameters     = json_encode($formParams ?? [], JSON_UNESCAPED_UNICODE);
            $auditInfo->summary             = json_encode($summaryData, JSON_UNESCAPED_UNICODE);
            $auditInfo->state               = enums::APPROVAL_STATUS_PENDING;
            $auditInfo->save();
            $createParams = [
                'audit_id'     => $auditInfo['id'],
                'audit_type'   => $bizType,
                'submitter_id' => $submitterId,
                'extend'       => ['from_submit'  => $formParams],
            ];
            $rst          = (new ApprovalServer($this->lang, $this->timezone))->recreate($createParams);
            if (!$rst) {
                throw new \Exception('SystemExternalApprovalServer insert Add  workflow fail ' . $rst);
            }
            $db->commit();
        } catch (InnerException $e) {
            $db->rollBack();
            $this->logger->write_log('SystemExternalApprovalServer  msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString() .  " paramIn: " . json_encode($paramIn,JSON_UNESCAPED_UNICODE), "notice");
            return $this->checkReturn(-3, $this->getTranslation()->_('no_server'));
        } catch (ValidationException $e) {
            return $this->checkReturn(-3, $e->getMessage());
        }
        return $this->checkReturn(['data'=>['serial_no' => $serialNo,'create_time'=> (string)$time]]);
    }


    /**
     * @description:审批接口 (通过 驳回 撤销)
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/18 15:56
     */
    public function updateApprove($paramIn = [])
    {
        $bizType     = $this->processingDefault($paramIn, 'biz_type');         // 申请类型
        $bizValue    = $this->processingDefault($paramIn, 'id');               // 主键 id
        $serialNo    = $this->processingDefault($paramIn, 'serial_no');        // 编号
        $remark      = $this->processingDefault($paramIn, 'reason');           // 备注
        $status      = $this->processingDefault($paramIn, 'status');           // 操作: 2-同意 3-驳回 4-撤销
        $operator_id = $this->processingDefault($paramIn, 'operator_id');      // 操作人
        $isForce     = $this->processingDefault($paramIn, 'is_force', 2, 0);   // 是否跳过权限验证强制审批

        try {
            if (!in_array($bizType, $this->getApprovalType()) || (empty($bizValue) && empty($serialNo))) {
                throw new ValidationException($this->getTranslation()->_('2201'));
            }
            $conditions = ' 1=1 ';
            $bind = [];
            if ($bizValue){
                $conditions .=" and id = :id: ";
                $bind['id'] = $bizValue;
            }
            if ($serialNo){
                $conditions .=" and serial_no = :serial_no:  ";
                $bind['serial_no'] = $serialNo;
            }

            //查询数据
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst([
                'conditions' => $conditions,
                'bind'       => $bind,
            ]);
            if (!$SystemExternalApprovalModel) {
                throw new ValidationException($this->getTranslation()->_('2201'));
            }
            $info = $SystemExternalApprovalModel->toArray();

            //审批状态S
            if ($info['state'] != enums::APPROVAL_STATUS_PENDING) {
                throw new ValidationException($this->getTranslation()->_('err_msg_status_updated'), ErrCode::WORKFLOW_AUDIT_HAS_FINISHED);
            }

            $app_server = new ApprovalServer($this->lang, $this->timeZone);
            $res        = $errInfo = $extend = [];

            // 是否跳过权限验证
            if ($isForce) {
                $extend['super'] = true;
            }

            if ($status == enums::$audit_status['approved']) {//审核通过
                $res = $app_server->approval($info['id'], $bizType, $operator_id, $remark, null, $errInfo);
            } elseif ($status == enums::$audit_status['dismissed']) {//驳回

                $res = $app_server->reject($info['id'], $bizType, $remark, $operator_id, null, $errInfo);

            } elseif ($status == enums::$audit_status['revoked']) {//撤销
                $res = $app_server->cancel($info['id'], $bizType, $remark, $operator_id, $extend, $errInfo);
            }

            if (!isset($res) || empty($res)) {
                // 如果是 SYSTEM_ERROR  才走 Exception
                if(!isset($errInfo['code']) || $errInfo['code'] ==  ErrCode::SYSTEM_ERROR){
                    throw new \Exception($errInfo['message'], $errInfo['code']);
                }
                //否则 走 ValidationException
                throw new ValidationException($errInfo['message'], $errInfo['code']);
            }

            $data = [
                'is_final' => $res->state != enums::APPROVAL_STATUS_PENDING ? 1 : 0,
            ];

        }  catch (ValidationException $e){
            $this->logger->write_log('SystemExternalApprovalServer  updateApprove msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString() .  " paramIn: " . json_encode($paramIn,JSON_UNESCAPED_UNICODE), "notice");
            return $this->checkReturn(["code" => $e->getCode(), "msg" => $e->getMessage(), 'data' => []]);
        } catch (\Exception $e) {

            $this->logger->write_log("SystemExternalApprovalServer updateApprove  : 事务未提交"
                                                     . " paramIn: " . json_encode($paramIn,JSON_UNESCAPED_UNICODE)
                                                     . " uid: " . $operator_id
                                                     . " message: " . $e->getMessage()
                                                     . " line: " . $e->getLine()
                . " file: " . $e->getFile());
            return $this->checkReturn(["code" => $e->getCode(), "msg" => $e->getMessage(), 'data' => []]);
        }

        return $this->checkReturn(["code" => 1, "msg" => 'success','data'=> $data]);

    }

    /**
     * @description: 获取审批 log
     * @param biz_type  审批类型
     * @param serial_no  审批编号
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/18 15:57
     */
    public function getApprovalLog($paramIn = [])
    {
        $bizType     = $this->processingDefault($paramIn, 'biz_type', 1); // 申请类型
        $serialNo    = $this->processingDefault($paramIn, 'serial_no', 1); // 编号
        $operator_id = $this->processingDefault($paramIn, 'operator_id', 1); // 操作人
        $version     = $this->processingDefault($paramIn, 'version', 1); // 版本

        try {
            if (!in_array($bizType, $this->getApprovalType())) {
                throw new ValidationException($this->getTranslation()->_('2201'));
            }

            //查询数据
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst([
                'conditions' => ' serial_no = :serial_no: ',
                'bind'       => [
                  'serial_no' => $serialNo,
                ],
            ]);
            if (empty($SystemExternalApprovalModel)) {
                throw new ValidationException($this->getTranslation()->_('2201'));
            }
            $info = $SystemExternalApprovalModel->toArray();
            //获取申请数据
            $parameters = [
                'conditions' =>  "biz_value = :value: and biz_type = :type:",
                'bind' => [
                    'type'  => $bizType,
                    'value' => $info['id'],
                ],
            ];
            //切表
            (new AuditApplyModel())->switchTableName($info['created_at'],$bizType,$info['id']);
            $request = AuditApplyModel::findFirst($parameters);

            //[2]获取审批流
            switch (strtolower($version)) {
                case 'v2':
                    $server = new WorkflowServer($this->lang, $this->timezone);
                    $server = Tools::reBuildCountryInstance($server,[$this->lang,$this->timezone]);
                    $auditLog = $server->getAuditLogsV2($request, $operator_id);
                    break;
                default:
                    $auditLog = (new WorkflowServer($this->lang, $this->timezone))->getAuditLogs($request, $operator_id);
                    break;
            }
            $data['stream'] = $auditLog;

        } catch (ValidationException $e){
            $this->logger->write_log('SystemExternalApprovalServer  getApprovalLog msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString() . ' biz_type=>'.$bizType.' serial_no=>'.$serialNo, "error");
            return $this->checkReturn(-3, $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->write_log('SystemExternalApprovalServer  getApprovalLog msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString(), "error");
            return $this->checkReturn(-3, $this->getTranslation()->_('no_server'));
        }
        return $this->checkReturn(['data'=>$data]);
    }

    /**
     * @description: 审批结束回调函数,设置审批状态等
     * @param int  $auditId 审批ID
     * @param int  $state   审批状态      const APPROVAL_STATUS_PENDING   = 1;    //待审批 const APPROVAL_STATUS_APPROVAL  =
     *                      2;    //审批同意const APPROVAL_STATUS_REJECTED  = 3;    //审批驳回const APPROVAL_STATUS_CANCEL    =
     *                      4;    //审批撤销const APPROVAL_STATUS_TIMEOUT   = 5;    //审批超时
     * @param null $extend  扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/18 15:57
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        try {
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('setProperty  没有找到数据  '.$auditId);
            }

            if ($isFinal) {
                //最终审批需要做的操作
                $SystemExternalApprovalModel->state      = $state;
                $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());      //时间
                $SystemExternalApprovalModel->save();

                //追加驳回原因
                if ($state == enums::APPROVAL_STATUS_REJECTED) {
                    $rejectReason = $extend['remark'];
                }
                //查询一下 操作人名称
                $staff_info = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id: ',
                    'bind'       => [
                        'staff_info_id' => $extend['staff_id'] ?? '',
                    ],
                    'columns'    => 'staff_info_id,name',
                ]);

                $forwardParamIn = [
                    'biz_type'                 => (int)$SystemExternalApprovalModel->biz_type, //类型
                    'param_operation_type'     => self::param_operation_type_2,                //审批
                    'param_operation_staff_id' => $extend['staff_id'],                         //操作用户
                    'serial_no'                => $SystemExternalApprovalModel->serial_no,     //审批编号
                    'state'                    => (int)$state,                                 //状态
                    'eventual'                 => $isFinal === true ? 1 : 0,
                    'reason'                   => $rejectReason ?? '',
                    'operator_name'            => $staff_info->name ?? '', //操作人名称
                ];
                AuditCallbackServer::createData($SystemExternalApprovalModel->biz_type, $forwardParamIn);
                $SystemExternalApprovalModel->save();
            }
        } catch (\Exception $e) {
            $this->logger->write_log('SystemExternalApprovalServer  setProperty msg '.$e->getMessage()." file ".$e->getFile()." line ".$e->getLine().$e->getTraceAsString().'auditId => '.$auditId,
                "error");
            return false;
        }

        return true;
    }

    /**
     * 异步回调
     * @throws Exception
     */
    public function delayCallBack($params): bool
    {
        $model  = SystemExternalApprovalModel::findFirstBySerialNo($params['serial_no']);
        $result = (new SystemExternalApprovalServer($this->lang, $this->timeZone))->forwardParamIn($params);
        if (!isset($result['code']) || $result['code'] != 1) {
            //这里需要判断 如果是失败了 记录一下  后期跑脚本
            $model->is_call_third_party = SystemExternalApprovalModel::is_call_third_party_2;
            $model->save();
            throw new \Exception('setProperty  回调审批失败!  ' . $params['serial_no']);
        }
        return true;
    }

    /**
     * 获取待审批数量，并返回待审批的serialNo
     * @return array
     */
    public function systemExternalApprovalGetList($paramIn = [])
    {
        //[1]获取传入参数
        $auditType   = $this->processingDefault($paramIn, 'biz_type');            //审批类型 array
        $approvalId  = $this->processingDefault($paramIn, 'approval_id',2);         //审批人ID
        $submitterId = $this->processingDefault($paramIn, 'submitter_id');        //申请人ID
        $pageNum     = $this->processingDefault($paramIn, 'page_num');            //页数
        $pageSize    = $this->processingDefault($paramIn, 'page_size');           //每页个数
        $serialNo    = $this->processingDefault($paramIn, 'serial_no');           //申请编码
        $startDate   = $this->processingDefault($paramIn, 'approval_start_date'); //开始日期
        $endDate     = $this->processingDefault($paramIn, 'approval_end_date');   //结束日期
        $state       = $this->processingDefault($paramIn, 'state');               //审批状态
        $sort        = $this->processingDefault($paramIn, 'sort');                //排序 1=倒序 2=正序

        if (empty($state)) {
            $state = [enums::APPROVAL_STATUS_PENDING];
        }
        if (empty($sort)) { //默认排序
            $sort = 'sea.serial_no desc';
        } else {
            if ($sort == AuditListEnums::LIST_SORT_ASC) { // 根据序列号正叙排序
                $sort = 'sea.serial_no asc';
            } else if ($sort == AuditListEnums::LIST_APPROVAL_SORT_DESC) { // 根据审批创建时间倒叙排序
                $sort = 'aa.created_at desc';
            } else if ($sort == AuditListEnums::LIST_APPROVAL_SORT_ASC) { // 根据审批创建时间正叙排序
                $sort = 'aa.created_at asc';
            } else {
                $sort = 'sea.serial_no desc';
            }
        }

        //[2]获取全部的待审批数据
        $countData = $this->getSystemAuditList($approvalId, [
            'state'        => $state,
            'audit_type'   => $auditType,
            'submitter_id' => $submitterId,
            'serial_no'    => $serialNo,
            'start_date'   => $startDate,
            'end_date'     => $endDate,
            'is_count'     => true,
        ], '');

        $result = $this->getSystemAuditList($approvalId, [
            'state'        => $state,
            'audit_type'   => $auditType,
            'submitter_id' => $submitterId,
            'serial_no'    => $serialNo,
            'start_date'   => $startDate,
            'end_date'     => $endDate,
        ], 'sea.serial_no,aa.state,sea.biz_type', $sort, $pageNum, $pageSize);

        return self::checkReturn(['data' => [
            'total_count' => intval($countData) ?? 0,
            'list'        => $result ?? [],
            'page_num'    => $pageNum,
            'page_size'   => $pageSize,
        ]]);
    }

    /**
     * 获取我的审批待审批列表
     * @param int $staffId
     * @param array $conditions
     * @param string $columns
     * @param string $order
     * @param int $pageNum
     * @param int $pageSize
     * @return mixed
     */
    public function getSystemAuditList(int $staffId, array $conditions, string $columns, string $order = '', int $pageNum = 0, int $pageSize = 10)
    {
        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['aa' => AuditApprovalModel::class]);
        $builder->innerjoin(SystemExternalApprovalModel::class, "sea.biz_type = aa.biz_type and sea.id = biz_value", "sea");
        if (!empty($conditions['submitter_id'])) {
            $builder->andWhere('aa.submitter_id = :submitter_id:', ['submitter_id' => $conditions['submitter_id']]);
        }
        $builder->andWhere('aa.approval_id = :staff_id: and aa.deleted = 0', ['staff_id' => $staffId]);
        $builder->inWhere('aa.state', $conditions['state']);

        if (!empty($conditions['audit_type'])) {
            $builder->inWhere('aa.biz_type', $conditions['audit_type']);
        }

        if (!empty($conditions['serial_no']) && is_string($conditions['serial_no'])) {
            $builder->andWhere('sea.serial_no = :serial_no:', ['serial_no' => $conditions['serial_no']]);
        }

        if (!empty($conditions['serial_no']) && is_array($conditions['serial_no'])) {
            $builder->inWhere('sea.serial_no', $conditions['serial_no']);
        }

        if (!empty($conditions['start_date']) && !empty($conditions['end_date'])) {
            $builder->andWhere('sea.created_at >= :start_date: and sea.created_at < :end_date:', [
                'start_date' => date("Y-m-d H:s:i", $conditions['start_date']),
                'end_date' => date("Y-m-d H:s:i", $conditions['end_date']),
            ]);
        }

        if (isset($conditions['is_count']) && $conditions['is_count']) {
            $builder->columns('count(1) as cou');
            $result = $builder->getQuery()->getSingleResult()->cou ?? 0;
        } else {
            $builder->columns($columns);
            $builder->limit($pageSize, ($pageNum - 1) * $pageSize);
            $builder->orderby($order);
            $result = $builder->getQuery()->execute()->toArray();
        }
        return $result;
    }

    /**
     * 获取全部数据列表
     * @param array $paramIn
     * @return array
     */
    public function systemExternalApprovalGetAllList($paramIn = []): array
    {
        //[1]获取传入参数
        $pageNum = $this->processingDefault($paramIn, 'page_num'); //页数
        $pageSize = $this->processingDefault($paramIn, 'page_size'); //每页个数

        //获取总数count
        $builder = $this->getBuilder('count(1) as cou', $paramIn);
        $count = $builder->getQuery()->getSingleResult()->cou ?? 0;

        //获取列表
        $builder = $this->getBuilder('sea.serial_no', $paramIn);
        $builder->limit($pageSize, ($pageNum - 1) * $pageSize);
        $builder->orderby('sea.created_at DESC');
        $result = $builder->getQuery()->execute()->toArray();
        if (!empty($result)) {
            $result = array_column($result, 'serial_no');
        }

        return self::checkReturn(['data' => [
            'total_count' => $count,
            'list' => $result ?? [],
            'page_num' => $pageNum,
            'page_size' => $pageSize,
        ]]);
    }

    /**
     * 获取builder
     * @param $column
     * @param $paramIn
     * @return mixed $builder
     */
    public function getBuilder($column, $paramIn)
    {
        $auditType = $this->processingDefault($paramIn, 'biz_type'); //审批类型 array
        $submitterId = $this->processingDefault($paramIn, 'submitter_id'); //申请人ID
        $serialNo = $this->processingDefault($paramIn, 'serial_no'); //申请编码
        $startDate = $this->processingDefault($paramIn, 'approval_start_date'); //开始日期
        $endDate = $this->processingDefault($paramIn, 'approval_end_date'); //结束日期
        $state = $this->processingDefault($paramIn, 'state'); //审批状态

        //获取列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($column);
        $builder->from(['sea' => SystemExternalApprovalModel::class]);
        if (!empty($submitterId)) {
            $builder->andWhere('sea.submitter_id = :submitter_id:', ['submitter_id' => $submitterId]);
        }
        $builder->inWhere('sea.state', $state);

        if (!empty($auditType)) {
            $builder->inWhere('sea.biz_type', $auditType);
        }

        if (!empty($serialNo)) {
            $builder->inWhere('sea.serial_no', $serialNo);
        }

        if (!empty($startDate) && !empty($endDate)) {
            $builder->andWhere('sea.created_at >= :start_date: and sea.created_at < :end_date:', [
                'start_date' => date("Y-m-d H:s:i", $startDate),
                'end_date' => date("Y-m-d H:s:i", $endDate),
            ]);
        }
        return $builder;
    }

    /**
     * @description: 获取审批条件所必须的数据
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/18 17:50
     */

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $auditInfo = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return [];
        }
        $parameters = json_decode($auditInfo->approval_parameters, true);
        return $parameters ?? [];
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $info = SystemExternalApprovalModel::findFirst($auditId);
        if (!empty($info)) {
            $summary = json_decode($info->summary, true);
        } else {
            $summary = json_decode([], true);
        }
        return $summary;
    }


    /**
     *
     * 获取 ph 众包页面 详情参考数据 https://flashexpress.feishu.cn/docx/RNlOdxJYOo1nGixuu6RcsFo5nVb
     * 取 前3天 - 前1天 的数据 以及 申请当前时间点的 在职人数等
     * @param $param
     * @return array
     * @throws Exception
     */
    public function getReferenceData($param)
    {
        $storeId   = strtoupper($param['store_id']);
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind'       => ['store_id' => $storeId],
        ]);
        if (empty($storeInfo)) {
            return $this->checkReturn(['data' => null]);
        }

        $today = date('Y-m-d');
        //统计 -1天 到 -3 天的日期 数据
        $stat_date = [];
        for ($i = -1; $i >= -3; $i--) {
            $stat_date[] = date('Y-m-d', strtotime("{$today} {$i} day"));
        }

        $paramFormat['store_id']       = $storeId;
        $paramFormat['date']           = $stat_date;
//        $paramFormat['store_category'] = $storeInfo->category;
        $paramFormat['store_category'] = enums::$stores_category['sp'];//固定 sp类型 非网点所属类型

        //当前时间点 在职的 指定职位的员工数
        $staffCountData = $this->getCurrentStaffNum($paramFormat);


        //正式员工 出勤人数统计 有打卡就算
        $stat_attendance = $this->getTransferNum($paramFormat);

        //fbi 各种数据
        $apiData = $this->getFbiData($paramFormat);

        //对应网点或者片区的 出勤人数
        $stores[] = $storeId;
        $pieceId  = $storeInfo->manage_piece;
        if (!empty($pieceId)) {
            $storeData = SysStoreModel::find([
                'columns'    => 'id',
                'conditions' => 'manage_piece = :piece_id: and category = :category: and state = 1',
                'bind'       => [
                    'piece_id' => intval($pieceId),
                    'category' => enums::$stores_category['sp'],
                ],
            ])->toArray();
            $storeData = empty($storeData) ? [] : array_column($storeData, 'id');
            $stores    = array_merge($stores, $storeData);
        }

        //获取 正式 外协 众包 片区的 出勤人数
        $pieceParam['date']   = $stat_date;
        $pieceParam['stores'] = $stores;
        $attendanceNum        = $this->getAttendanceNum($pieceParam, $storeId,$storeInfo->category);

        //整理数据 结构
        $return['van_num']      = $staffCountData[enums::$job_title['van_courier']];
        $return['bike_num']     = $staffCountData[enums::$job_title['bike_courier']];
        $return['tricycle_num'] = $staffCountData[enums::$job_title['tricycle_courier']];

        //三方会师
        foreach ($stat_date as $date) {
            $row['date']                 = $date;//统计日期
            $row['staff_num']            = $stat_attendance[$date]['num'] ?? 0;//在职人数总数
            $row['attendance_num']       = $stat_attendance[$date]['attendance_num'] ?? 0;//在职的出勤人数
            $row['pickup_num']           = $apiData[$date]['store_pickup_count'] ?? 0;//网点揽收数
            $row['should_delivery_num']  = $apiData[$date]['store_should_delivery_count'] ?? 0;//网点应派件数
            $row['store_delivered_num']  = $apiData[$date]['store_delivery_count'] ?? 0;//网点妥投量
            $row['store_attendance_num'] = $attendanceNum[$date]['store_attendance_num'] ?? 0;//网点出勤人数
            $row['store_duration']       = $apiData[$date]['store_avg_duration'] ?? 0;//网点工作时长
            $row['piece_delivered_num']  = $apiData[$date]['piece_delivery_count'] ?? 0;//片区妥投量
            $row['piece_attendance_num'] = $attendanceNum[$date]['piece_attendance_num'] ?? 0;//片区出勤人数
            $row['piece_duration']       = $apiData[$date]['piece_avg_duration'] ?? 0;//片区工作时长
            $return['list'][]            = $row;
        }

        return $this->checkReturn(['data' => $return]);
    }


    //当前时间 指定规则的 在职人数
    protected function getCurrentStaffNum($param)
    {
        $jobTitles = [
            enums::$job_title['van_courier'],
            enums::$job_title['bike_courier'],
            enums::$job_title['tricycle_courier'],
        ];
        $storeId   = $param['store_id'];
        $data      = HrStaffInfoModel::find([
            'columns'    => 'job_title,count(1) as num',
            'conditions' => 'state = :state: and formal = :formal: and is_sub_staff = :sub_staff: and hire_type in ({types:array}) and sys_store_id = :store_id: and job_title in ({titles:array})',
            'bind'       => [
                'state'     => HrStaffInfoModel::STATE_1,
                'formal'    => HrStaffInfoModel::FORMAL_1,
                'sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0,
                'types'     => [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2],
                'store_id'  => $storeId,
                'titles'    => $jobTitles,
            ],
            'group'      => 'job_title',
        ])->toArray();

        $return[enums::$job_title['van_courier']] = $return[enums::$job_title['bike_courier']] = $return[enums::$job_title['tricycle_courier']] = 0;

        if (empty($data)) {
            return $return;
        }

        $data = array_column($data, 'num', 'job_title');

        $return[enums::$job_title['van_courier']]      = $data[enums::$job_title['van_courier']] ?? 0;
        $return[enums::$job_title['bike_courier']]     = $data[enums::$job_title['bike_courier']] ?? 0;
        $return[enums::$job_title['tricycle_courier']] = $data[enums::$job_title['tricycle_courier']] ?? 0;

        return $return;
    }

    /**
     *
     * //蓝派件 和 时长（非hrs 出勤时长 是第一个包裹妥投什么的 fbi 记录的时长）
     * //各种件数 和工作时长 接口文档 https://flashexpress.feishu.cn/docx/Y9aHdxKNuoSMD9xcZ3TcN60inSe
     * @param $param
     * @return array|null
     * @throws Exception
     */
    protected function getFbiData($param)
    {
        $method    = 'dc.get_system_external_data';
        $rpcHandle = (new ApiClient('bi_rpcv2', '', $method, $this->lang));
        $rpcHandle->setParams($param);
        $res = $rpcHandle->execute();
        $this->logger->write_log("system_external {$param['store_id']} ".json_encode($param).json_encode($res), 'info');

        if (empty($res['result']) || empty($res['result']['code']) || $res['result']['code'] != 1) {
            throw new Exception($res['error'] ?? $res['msg']);
        }
        $data = $res['result']['data'];
        if (empty($data)) {
            return null;
        }

        return array_column($data, null, 'date');
    }




    /**
     * 获取历史日期 指定规则 正式员工 在职人数数据  作为基数 查询打卡的人 补卡的也算 或者 打卡网点要在 当前所属网点的也算
    SELECT `t`.`stat_date` AS `stat_date`, `t`.`staff_info_id` AS `staff_info_id`
    FROM `hr_staff_transfer` AS `t`
    LEFT JOIN `hr_staff_info` AS `s` ON `t`.`staff_info_id` = `s`.`staff_info_id`
    WHERE (`t`.`state` = 1 AND `t`.`job_title` IN (13,110,1000)
    AND `t`.`store_id` = 'ph01020105' AND `t`.`formal` = 1
    AND `t`.`stat_date` IN ('2022-11-28','2022-11-27','2022-11-26'))
    AND (`s`.`is_sub_staff` = 0);
     *

    select count(1) from staff_work_attendance where
    attendance_date = '2022-11-11' and staff_info_id in (124886,124888,125033,125522,125523,126961,127217,127288,128475)
     and ((started_store_id = 'PH01010104' or end_store_id = 'PH01010104') or (started_state = 3 or end_state = 3));
     * @param $param
     * @return array
     */
    protected function getTransferNum($param)
    {
        $dateList  = $param['date'];
        $storeId   = $param['store_id'];
        $jobTitles = [
            enums::$job_title['van_courier'],
            enums::$job_title['bike_courier'],
            enums::$job_title['tricycle_courier'],
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['t' => HrStaffTransferModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, "t.staff_info_id = s.staff_info_id", "s");
        $builder->columns('t.stat_date,t.staff_info_id');
        $builder->andWhere('t.state = :state: and t.job_title in ({titles:array}) and t.store_id = :store_id: and t.formal = :formal: and t.stat_date in ({stat_date:array})',
            [
                'state'     => HrStaffInfoModel::STATE_1,
                'titles'    => $jobTitles,
                'store_id'  => $storeId,
                'formal'    => HrStaffInfoModel::FORMAL_1,
                'stat_date' => $dateList,
            ]
        );

        $builder->andWhere('s.is_sub_staff = 0');
        $res = $builder->getQuery()->execute()->toArray();
        if(empty($res)){
            return [];
        }

        $data = array();
        foreach ($res as $r){
            if(empty($data[$r['stat_date']]['num'])){
                $data[$r['stat_date']]['num'] = 1;
            }else{
                $data[$r['stat_date']]['num']++;
            }
            $data[$r['stat_date']]['stat_date'] = $r['stat_date'];
            $data[$r['stat_date']]['staff_ids'][] = $r['staff_info_id'];
        }

        $data = array_values($data);
        //根据取出来的 当时在职工号 查询对应日期的 打卡数据 不能查询一次 group
        foreach ($data as &$da) {
            if (empty($da['staff_ids']) || empty($da['stat_date'])) {
                $this->logger->write_log("getTransferNum 计算正式员工 出勤人数为空 ".json_encode($da, JSON_UNESCAPED_UNICODE),
                    'info');
                continue;
            }
            $count = StaffWorkAttendanceModel::count([
                'conditions' => 'attendance_date = :date: and staff_info_id in ({ids:array}) and ((started_store_id = :store_id: or end_store_id = :store_id:) or (started_state = 3 or end_state = 3))',
                'bind'       => [
                    'date'     => $da['stat_date'],
                    'ids'      => $da['staff_ids'],
                    'store_id' => $storeId,
                ],
            ]);

            $da['attendance_num'] = $count;
        }

        return empty($data) ? [] : array_column($data, null, 'stat_date');
    }

    //网点 或者 片区的 出勤人数 包括 外协的 不要 支援的 还得是打卡网点 在所属网点的

    /**
     * @param $param
     * @param $currentStore
     * @param $storeCategory 有可能 当前网点不是 sp 要排除掉当前网点算出勤人数
     * @return array
     *
      select attendance_date, organization_id,count(1) as num ,concat(attendance_date,organization_id) k_index
      from staff_work_attendance where  attendance_date  in ('2022-11-12','2022-12-13') and organization_id  in ('PH61090501','PH62110500','PH61204X03') and job_title  in (13,110,1000)
      and ( started_store_id = organization_id  or end_store_id = organization_id )  GROUP BY organization_id,attendance_date
     */
    protected function getAttendanceNum($param, $currentStore,$storeCategory)
    {
        $jobTitles = [
            enums::$job_title['van_courier'],
            enums::$job_title['bike_courier'],
            enums::$job_title['tricycle_courier'],
        ];
        $attData   = StaffWorkAttendanceModel::find([
            'columns'    => 'attendance_date, organization_id,count(1) as num ,concat(attendance_date,organization_id) as k_index',
            'conditions' => "attendance_date  in ({dates:array}) and organization_id  in ({stores:array}) and job_title  in ({jobs:array}) 
                            and ( started_store_id = organization_id  or end_store_id = organization_id )",
            'bind'       => [
                'dates'  => $param['date'],
                'stores' => $param['stores'],
                'jobs'   => $jobTitles,
            ],
            'group'      => 'organization_id,attendance_date',
        ])->toArray();
        $attData   = empty($attData) ? [] : array_column($attData, null, 'k_index');

        //是否当前网点是 sp  true  是 false 否
        $flag = true;
        if($storeCategory != enums::$stores_category['sp']){
            $flag = false;
        }

        //众包 出勤员工 网点 日期维度
        $extStaffData = $this->getExternalAttendanceNum($param);
        $extStaffData = empty($extStaffData) ? [] : array_column($extStaffData, null, 'k_index');
        $return       = [];//以日期维度 分组

        foreach ($param['date'] as $day) {
            //当前网点 的数量
            $k                                    = $day.$currentStore;
            $numIn                                = $attData[$k]['num'] ?? 0;//正式 和外协的 打卡人数
            $numOut                               = $extStaffData[$k]['num'] ?? 0;//众包 出勤人数
            $return[$day]['store_attendance_num'] = $numIn + $numOut;

            //当前网点之外的 剩余 其他网点求和
            $return[$day]['piece_attendance_num'] = 0;
            foreach ($attData as $att) {
                if ($att['attendance_date'] != $day) {
                    continue;
                }
                //当前网点 非sp  不参与计算
                if(!$flag && $att['organization_id'] == $currentStore){
                    continue;
                }
                $return[$day]['piece_attendance_num'] += $att['num'];
            }

            foreach ($extStaffData as $ext) {
                if ($ext['hire_date'] != $day) {
                    continue;
                }
                //当前网点 非sp  不参与计算
                if(!$flag && $ext['sys_store_id'] == $currentStore){
                    continue;
                }
                $return[$day]['piece_attendance_num'] += $ext['num'];
            }
        }
        return $return;
    }

    /**
     * 网点或者片区 获取 众包的 出勤人数 只有存在工号 并且 入职是统计日期 的就算
      select date_format(hire_date,'%Y-%m-%d') as hire_date,sys_store_id,count(1) as num , concat(date_format(hire_date,'%Y-%m-%d'),sys_store_id) as k_index
      from hr_staff_info where sys_store_id in ('PH40320100','PH62110500','PH61204X03') and hire_date in  ('2022-11-12','2022-11-13') and formal = 0  and hire_type = 12
     * @param $param
     * @return mixed
     */
    protected function getExternalAttendanceNum($param)
    {
        $storeIds = $param['stores'];
        $date     = $param['date'];
        //入职日期 是带 000的
        foreach ($date as &$da) {
            $da = $da.' 00:00:00';
        }
        return HrStaffInfoModel::find([
            'columns'    => "date_format(hire_date,'%Y-%m-%d') as hire_date,sys_store_id,count(1) as num , concat(date_format(hire_date,'%Y-%m-%d'),sys_store_id) as k_index",
            'conditions' => 'sys_store_id in ({stores:array}) and hire_date in ({date:array}) and formal = :formal: and hire_type = :hire_type:',
            'bind'       => [
                'stores'    => $storeIds,
                'date'      => $date,
                'formal'    => HrStaffInfoModel::FORMAL_0,
                'hire_type' => HrStaffInfoModel::HIRE_TYPE_12,//众包 外协类型
            ],
            'group'      => 'hire_date,sys_store_id',
        ])->toArray();
    }

    /**
     * @description 根据serial_no获取指定审批人的待审批数据
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function systemExternalGetStaffPendingBySerialNo($param): array
    {
        $auditType  = $param['audit_type'];
        $serialNo   = $param['serial_no'];
        $approvalId = $param['approval_id'];

        if (empty($auditType) || empty($serialNo) || empty($approvalId)) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }

        $auditInfo    = SystemExternalApprovalModel::find([
            "conditions" => 'serial_no in({serial_no_arr:array})',
            'bind'       => [
                'serial_no_arr' => $serialNo,
            ],
            'columns'    => 'id,serial_no',
        ])->toArray();
        $auditInfoArr = array_column($auditInfo, 'serial_no', 'id');
        $auditIds     = array_column($auditInfo, 'id');
        if (empty($auditIds)) {
            return [
                'pending'           => [],
                'approval_complete' => [],
            ];
        }
        $approvalInfo = AuditApprovalModel::find([
            "conditions" => 'biz_value in({biz_value:array}) and biz_type = :biz_type: and state = :state: and approval_id = :approval_id: and deleted = 0',
            'bind' => [
                'biz_value'   => $auditIds,
                'biz_type'    => $auditType,
                'state'       => enums::APPROVAL_STATUS_PENDING,
                'approval_id' => $approvalId,
            ],
            'columns' => 'biz_value',
        ])->toArray();
        $approvalValueIds = array_column($approvalInfo, 'biz_value');
        if (empty($approvalValueIds)) {
            return [
                'pending'           => [],
                'approval_complete' => $serialNo,
            ];
        }

        foreach ($auditInfoArr as $audit_id => $serial_no) {
            if (in_array($audit_id, $approvalValueIds)) {
                $pendingSerialArr[] = $serial_no;
            } else {
                $approvalCompleteArr[] = $serial_no;
            }
        }

        return [
            'pending'           => $pendingSerialArr ?? [],
            'approval_complete' => $approvalCompleteArr ?? [],
        ];
    }

    /**
     * 超时关闭
     * @param array $params
     * @return array
     * @throws ValidationException
     */
    public function timeOut(array $params): array
    {
        $serial_no   = trim($params['serial_no']) ?? '';
        $operator_id = $params['operator'] ?? Enums::SYSTEM_STAFF_ID;

        if (empty($serial_no)) {
            // 缺少必须的参数
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }

        $audit_info_obj = SystemExternalApprovalModel::findFirst([
            "conditions" => 'serial_no = :serial_no:',
            'bind'       => [
                'serial_no' => $serial_no,
            ],
            'columns'    => 'id,serial_no,biz_type',
        ]);
        if (empty($audit_info_obj)) {
            throw new \Exception('审批流超时失败，编号为：'.$serial_no);
        }

        $app_server = new ApprovalServer($this->lang, $this->timeZone);
        $result     = $app_server->timeOut($audit_info_obj->id, $audit_info_obj->biz_type, $operator_id);
        if (!$result) {
            $this->logger->write_log('关闭审批流失败！！,审批编号为：'.$serial_no, 'info');
            throw new \Exception('审批流超时失败，编号为；'.$serial_no);
        }
        return $this->checkReturn(['data' => ['serial_no' => $serial_no]]);
    }

    /**
     * 车辆维修申请 - 回调地址
     * @param int $param_operation_type
     * @param array $paramIn
     * @return array
     */
    public function getVehicleRepairRequestRouteSvc(int $param_operation_type, array $paramIn): array
    {
        $result['sys'] = 'ard_api';
        switch ($param_operation_type) {
            case self::param_operation_type_2 :
                //审批结果 - 通过、驳回、撤回 - 终态 - 异步回调
                $result['method'] = 'approval.approvalRepairApply';
                break;
            case self::param_operation_type_4 :
                //我的申请/我的审批-审批详情
                $result['method'] = 'approval.auditRepairApplyDetail';
                break;
            default:
                $result = [];
                break;
        }
        if(!empty($result)) {
            $result['paramIn'] = $paramIn;
        }
        return $result;
    }
}
