<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AdvanceFuelModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffPickupDeliveryDataModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

class AdvanceFuelServer extends AuditBaseServer
{
    const SERIAL_PRE = "AF";

    /**
     * 预支油费签字 创建审批流
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function signAdvanceFuel($param)
    {
        $staff_info_id = $param['staff_info_id'] ?? '';
        $sign_url      = $param['sign_url'] ?? '';
        if (empty($sign_url) || empty($staff_info_id)) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $checkSubmitLimitation = $this->checkSubmitLimitation($staff_info_id);
        $staff_info            = !empty($checkSubmitLimitation['staff_info']) ? $checkSubmitLimitation['staff_info'] : [];
        if ($checkSubmitLimitation['is_submit'] === false) {
            throw new ValidationException($checkSubmitLimitation['tip']);
        }
        $template_list = (new SettingEnvServer())->listByCode([
            'advance_fuel_pdf_template_url',
            'advance_fuel_pdf_template_header',
            'advance_fuel_pdf_template_footer',
        ]);
        if (!empty($template_list)) {
            $template_list = array_column($template_list, 'set_val', 'code');
        }
        $pdf_template_url                 = !empty($template_list['advance_fuel_pdf_template_url']) ? json_decode($template_list['advance_fuel_pdf_template_url'],
            true) : [];
        $pdf_template_url                 = $pdf_template_url['pdf_template_url'] ?? '';
        $advance_fuel_pdf_template_header = $template_list['advance_fuel_pdf_template_header'] ?? '';
        $advance_fuel_pdf_template_footer = $template_list['advance_fuel_pdf_template_footer'] ?? '';
        if (empty($pdf_template_url)) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $db = $this->getDI()->get("db");
        try {
            $db->begin();
            $form_data                 = [
                'name'          => $staff_info['name'] ?? '',
                'staff_info_id' => "$staff_info_id",
                'new_date'      => date('Y-m-d'),
            ];
            $img_data                  = [['name' => 'sign_img', 'url' => $sign_url]];
            $pdf_header_footer_setting = [
                'displayHeaderFooter' => true,
                'headerTemplate'      => $advance_fuel_pdf_template_header,
                'footerTemplate'      => $advance_fuel_pdf_template_footer,
            ];
            $pdf_file_data             = (new formPdfServer())->generatePdf($pdf_template_url, $form_data, $img_data,
                $staff_info_id, $pdf_header_footer_setting, 'attchment');
            if (empty($pdf_file_data['object_url'])) {
                throw new \Exception('pdf error ');
            }
            $param['pdf_url']       = $pdf_file_data['object_url'];
            $model                  = new AdvanceFuelModel();
            $model->serial_no       = static::SERIAL_PRE . $this->getRandomId();
            $model->staff_info_id   = $staff_info_id;
            $model->approval_status = enums::APPROVAL_STATUS_PENDING;
            $model->advance_status  = AdvanceFuelModel::ADVANCE_STATUS_IN_APPROVAL;
            $model->signature_url   = $sign_url;
            $model->pdf_url         = $param['pdf_url'];
            $model->save();
            $lastInsertId = $model->id;
            if (!$lastInsertId) {
                throw new \Exception('apply failed');
            }
            //创建审批流
            $flag = (new ApprovalServer($this->lang, $this->timeZone))->create($lastInsertId,
                AuditListEnums::APPROVAL_TYPE_ADVANCE_FUEL, $staff_info_id);
            if (!$flag) {
                throw new \Exception('创建审批流失败');
            }
            $db->commit();
            return $this->checkReturn(['data' => $lastInsertId]);
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDI()->get("logger")->write_log('AdvanceFuel signAdvanceFuel 预支油费申请提交失败 ' . $e->getMessage());
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * 获取预支油费详情
     * @param $param
     * @return array
     */
    public function getAdvanceFuelInfo($param)
    {
        $staff_info_id                 = $param['staff_info_id'] ?? '';
        $advance_fuel_pdf_template_url = (new SettingEnvServer())->getSetVal('advance_fuel_pdf_template_url');
        $pdf_url                       = $advance_fuel_pdf_template_url ? json_decode($advance_fuel_pdf_template_url,
            true) : [];
        $checkSubmitLimitation         = $this->checkSubmitLimitation($staff_info_id);
        $personal_email                = $checkSubmitLimitation['staff_info']['personal_email'] ?? '';
        unset($checkSubmitLimitation['staff_info']);
        $return_data = array_merge(
            $checkSubmitLimitation,
            [
                'pdf_url'        => $pdf_url['pdf_url'] ?? '',
                'personal_email' => $personal_email,
            ]
        );
        return $return_data;
    }

    /**
     * 验证提交预支油费限制
     * @param $staff_info_id
     * @return array
     */
    public function checkSubmitLimitation($staff_info_id)
    {
        $return_data               = ['is_submit' => false, 'tip' => ''];
        $staff_info                = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and hire_type in ({hire_type:array}) and state != :state:",
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'hire_type'     => HrStaffInfoModel::$agentTypeTogether,
                'state'         => HrStaffInfoModel::STATE_RESIGN,
            ],
        ]);
        $staff_info                = $staff_info ? $staff_info->toArray() : [];
        $return_data['staff_info'] = $staff_info;
        if (!isCountry('MY') || empty($staff_info) || empty($staff_info['hire_date'])) {
            // 根据您的信息判定您目前还不符合申请条件
            $return_data['tip'] = $this->getTranslation()->_('advance_fuel_error_1');
            return $return_data;
        }

        // 有审批状态为已通过、待审批的记录（含历史数据导入）不能申请
        $advance_fuel = AdvanceFuelModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $staff_info_id,
            ],
            'order'      => 'id DESC',
        ]);
        $advance_fuel = $advance_fuel ? $advance_fuel->toArray() : [];
        if (in_array($advance_fuel['approval_status'],
            [enums::APPROVAL_STATUS_PENDING, enums::APPROVAL_STATUS_APPROVAL])) {
            // 您已申请过预支油费，不能再次申请 \n当前状态：%预支状态%
            $advance_status_text = $this->getTranslation()->_(AdvanceFuelModel::ADVANCE_STATUS_ITEM[$advance_fuel['advance_status']]);
            $return_data['tip']  = $this->getTranslation()->_('advance_fuel_error_2',
                ['advance_status' => $advance_status_text]);
            return $return_data;
        }
        // 从入职日期开始到此时，是否打上班卡3天及以上，未达到3天及以上 不能申请
        
        $attendanceInfo = StaffWorkAttendanceModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'staff_info_id = :staff_id: and attendance_date >= :date: and started_at is not null',
            'bind'       => [
                'staff_id' => $staff_info_id,
                'date'     => $staff_info['hire_date'],
            ],
            'group'    => "attendance_date",
        ])->toArray();
        if (count($attendanceInfo) < 3) {
            // 每个个人代理必须在公司完成至少3天的服务才能参加本次活动，您还不满足条件
            $return_data['tip'] = $this->getTranslation()->_('advance_fuel_error_3');
            return $return_data;
        }
        $return_data['is_submit'] = true;
        $this->getDI()->get('logger')->write_log("AdvanceFuel checkSubmitLimitation " . json_encode($return_data),
            'info');
        return $return_data;
    }

    /**
     * @param $params
     * @param $staffId
     * @return array
     * @throws \Exception
     */
    public function audit($params, $staffId)
    {
        $auditType   = AuditListEnums::APPROVAL_TYPE_ADVANCE_FUEL;
        $approval_id = $staffId;//操作人工号
        $audit_id    = $params['audit_id'];
        $status      = intval($params['status']);
        $server      = new ApprovalServer($this->lang, $this->timeZone);
        if ($status == enums::$audit_status['approved']) {//审核通过
            $res = $server->approval($audit_id, $auditType, $approval_id);
        } elseif ($status == enums::$audit_status['dismissed']) {//驳回
            $res = $server->reject($audit_id, $auditType, $params['reject_reason'], $approval_id);
        } elseif ($status == enums::$audit_status['revoked']) {//撤销
            $res = $server->cancel($audit_id, $auditType, $params['reason'], $approval_id);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }
        if (empty($res)) {
            $this->getDI()->get('logger')->write_log('audit_update_status error ' . json_encode($params) . json_encode($res),
                'error');
            return $this->checkReturn(-3, 'server error');
        } else {
            return $this->checkReturn(1);
        }
    }

    /**
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     * @throws ValidationException
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $info = AdvanceFuelModel::findFirst($auditId);
        if (empty($info)) {
            throw new ValidationException('data error');
        }
        $info = $info->toArray();
        //获取提交人用户信息
        $staff_info      = (new StaffRepository())->getStaffPosition($info['staff_info_id']);
        $storeInfo       = SysStoreModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $staff_info['sys_store_id']],
        ]);
        $regionPieceInfo = [];
        if (!empty($storeInfo)) {
            $regionPieceRe   = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
            $regionPieceInfo = $regionPieceRe->getOrganizationRegionPieceManagerId($storeInfo->id);
        }
        $jobInfo                                = HrJobTitleModel::findFirst($staff_info['job_title']);
        $detailLists['apply_parson']            = $staff_info['name'] . "({$staff_info['staff_info_id']})";
        $detailLists['staff_store_name']        = $storeInfo->name ?? '';
        $detailLists['staff_region_piece_name'] = ($regionPieceInfo['region_name'] ?? '') . '-' . ($regionPieceInfo['piece_name'] ?? '');
        $detailLists['staff_position']          = empty($jobInfo) ? '' : $jobInfo->job_name;
        $detailLists['staff_hire_type']         = !empty($staff_info['hire_type']) ? $this->getTranslation()->_('hire_type_' . $staff_info['hire_type']) : '';
        $detailLists['staff_hire_date']         = !empty($staff_info['hire_date']) ? date('Y-m-d',
            strtotime($staff_info['hire_date'])) : '';
        $detailLists['view_agreement']          = $info['pdf_url'] ?? '';
        $detailLists['staff_signature']         = $info['signature_url'] ?? '';
        $addHour                                = $this->config->application->add_hour;
        $audit_list_re                          = new AuditlistRepository($this->lang, $this->timeZone);
        $data                                   = [
            'title'       => $audit_list_re->getAudityType(AuditListEnums::APPROVAL_TYPE_ADVANCE_FUEL),
            'id'          => $info['id'],
            'staff_id'    => $info['staff_info_id'],
            'type'        => AuditListEnums::APPROVAL_TYPE_ADVANCE_FUEL,
            'created_at'  => date('Y-m-d H:i:s', strtotime($info['created_at']) + $addHour * 3600),
            'updated_at'  => date('Y-m-d H:i:s', strtotime($info['updated_at']) + $addHour * 3600),
            'status'      => $info['approval_status'],
            'status_text' => $audit_list_re->getAuditStatus('10' . $info['approval_status']),
            'serial_no'   => $info['serial_no'] ?? '',
        ];
        $returnData['data']['detail']           = $this->format($detailLists);
        $returnData['data']['head']             = $data;
        // 近七天揽派件统计
        $staff_pickup_delivery_data                                 = $this->get_staff_pickup_delivery_data($staff_info['staff_info_id']);
        $returnData['data']['extend']['staff_pickup_delivery_data'] = $staff_pickup_delivery_data;
        return $returnData;
    }

    /**
     * @param $list
     * @return array
     */
    public function format($list): array
    {
        $return = [];
        foreach ($list as $key => $v) {
            $item = [];
            if ($key === 'view_agreement') {
                $item['key']        = $this->getTranslation()->_($key) ?? '';
                $item['key_tips']   = is_array($v) && isset($v['key_tips']) ? $v['key_tips'] : null;
                $item['key_icon']   = is_array($v) && isset($v['key_icon']) ? $v['key_icon'] : null;
                $item['value']      = is_array($v) && isset($v['value']) ? $v['value'] : $v;
                $item['tips']       = is_array($v) && isset($v['tips']) ? $v['tips'] : null;
                $item['type']       = 4;
                $item['simple_val'] = $v;
                $item['color']      = is_array($v) && isset($v['color']) ? $v['color'] : null;
                $return[]           = $item;
                continue;
            }
            $item['key']      = $this->getTranslation()->_($key) ?? '';
            $item['key_tips'] = is_array($v) && isset($v['key_tips']) ? $v['key_tips'] : null;
            $item['key_icon'] = is_array($v) && isset($v['key_icon']) ? $v['key_icon'] : null;
            if ($key === 'staff_signature'){
                $item['value']    = is_array($v) && isset($v['value']) ? [$v['value']] : [$v];
            }else{
                $item['value']    = is_array($v) && isset($v['value']) ? $v['value'] : $v;
            }
            $item['tips']     = is_array($v) && isset($v['tips']) ? $v['tips'] : null;
            $item['color']    = is_array($v) && isset($v['color']) ? $v['color'] : null;
            $return[]         = $item;
        }
        return $return;
    }

    /**
     * @param $staff_info_id
     * @return array
     */
    public function get_staff_pickup_delivery_data($staff_info_id)
    {
        $stat_date               = date('Y-m-d', strtotime("-7 day"));
        $staffPickupDeliveryData = StaffPickupDeliveryDataModel::find([
            'conditions' => 'staff_info_id = :staff_info_id: and stat_date > :stat_date: and is_deleted = :is_deleted:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'stat_date'     => $stat_date,
                'is_deleted'    => StaffPickupDeliveryDataModel::IS_DELETED_NO,
            ],
        ])->toArray();
        $staffPickupDeliveryData = !empty($staffPickupDeliveryData) ? array_column($staffPickupDeliveryData, null,
            'stat_date') : [];
        $return_data             = [];
        for ($i = 0; $i < 7; $i++) {
            $_date_m_d                         = date('m/d', strtotime("-$i day"));
            $_date_y_m_d                       = date('Y-m-d', strtotime("-$i day"));
            $return_data[$i]['date']           = $_date_m_d;
            $return_data[$i]['pickup_count']   = !empty($staffPickupDeliveryData[$_date_y_m_d]) ? $staffPickupDeliveryData[$_date_y_m_d]['pickup_count'] : 0;
            $return_data[$i]['delivery_count'] = !empty($staffPickupDeliveryData[$_date_y_m_d]) ? $staffPickupDeliveryData[$_date_y_m_d]['delivery_count'] : 0;
        }
        return $return_data;
    }

    /**
     * @param int $auditId
     * @param $user
     * @return array|string
     */
    public function genSummary(int $auditId, $user)
    {
        $info = AdvanceFuelModel::findFirst($auditId);
        if (empty($info)) {
            return '';
        }
        $info       = $info->toArray();
        $staff_info = (new StaffRepository())->getStaffPosition($info['staff_info_id']);
        //职位
        $item['key']   = 'staff_position';
        $item['value'] = empty($staff_info['job_name']) ? '' : $staff_info['job_name'];
        $return[]      = $item;
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $staff_info['sys_store_id']],
        ]);
        if (empty($storeInfo)) {
            return $return;
        }
        //网点
        $item['key']   = 'staff_store';
        $item['value'] = $storeInfo->name ?? '';
        $return[]      = $item;
        return $return;
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return true
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $model                  = AdvanceFuelModel::findFirst($auditId);
            $model->approval_status = $state;
            if ($state == enums::$audit_status['approved']) {
                // 审批通过
                if (!empty($model->staff_info_id)) {
                    $vehicleInfo = VehicleInfoModel::findFirst([
                        'conditions' => 'uid = :uid: ',
                        'bind'       => ['uid' => $model->staff_info_id],
                    ]);
                    if (!empty($vehicleInfo) && !empty($vehicleInfo->oil_number)) {
                        $model->advance_status = AdvanceFuelModel::ADVANCE_STATUS_TO_BE_RECHARGED;
                    } else {
                        $model->advance_status = AdvanceFuelModel::ADVANCE_STATUS_TO_BE_SUBMITTED;
                    }
                } else {
                    $model->advance_status = AdvanceFuelModel::ADVANCE_STATUS_TO_BE_SUBMITTED;
                }
                // 给邮箱发送协议
                $this->sendMail($model->toArray());
            } elseif ($state == enums::$audit_status['dismissed']) {
                // 已驳回
                $model->reject_reason  = $extend['remark'] ?? '';
                $model->advance_status = AdvanceFuelModel::ADVANCE_STATUS_REJECTED;
                // 发送驳回消息 
                $this->sendNoticePush($model->toArray(), $extend['remark']);
            }
            $model->save();
        }
        return true;
    }

    /**
     * 发送消息
     * @param $advance_fuel
     * @param $reason
     * @return true
     */
    public function sendNoticePush($advance_fuel, $reason)
    {
        $this->getDI()->get('logger')->write_log("AdvanceFuel sendNoticePush 发送消息开始", 'info');
        //获取语言
        $lang        = (new StaffServer())->getLanguage($advance_fuel['staff_info_id']);
        $t           = $this->getTranslation($lang);
        $msg_title   = $t->_('advance_fuel_dismissed_message_title');
        $msg_content = $t->_('advance_fuel_dismissed_message_content', ['reason' => $reason]);

        $msg_data = [
            "id"                 => time() . $advance_fuel['staff_info_id'] . rand(1000000, 9999999),
            "staff_users"        => [$advance_fuel['staff_info_id']],
            "message_title"      => $msg_title,
            "message_content"    => addslashes("<div style='font-size: 30px'>" . $msg_content . "</div>"),
            "staff_info_ids_str" => $advance_fuel['staff_info_id'],
            "category"           => MessageEnums::MESSAGE_CATEGORY_SYS,
        ];
        $bi_rpc   = (new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($msg_data);
        $res = $bi_rpc->execute();
        if ($res && $res['result']['code'] == 1) {
            $msg_id = $res['result']['data'][0];
            $this->getDI()->get('logger')->write_log("AdvanceFuel sendNoticePush 发送消息成功 : msg_id:{$msg_id},msg_data : " . json_encode($res),
                'info');
        } else {
            $this->getDI()->get('logger')->write_log("AdvanceFuel sendNoticePush 发送消息失败 : msg_data:" . json_encode($msg_data) . ',消息返回 : ' . json_encode($res),
                'error');
        }
        return true;
    }

    /**
     * 发送邮件
     * @param $advance_fuel
     * @return true
     */
    public function sendMail($advance_fuel)
    {
        $this->getDI()->get('logger')->write_log("AdvanceFuel sendMail 发送邮件开始", 'info');
        // 查询个人邮箱
        $staff_info = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind' => [
                'staff_id' => $advance_fuel['staff_info_id']
            ]
        ]);
        $staff_info = !empty($staff_info) ? $staff_info->toArray() : [];
        if (!empty($staff_info['personal_email'])) {
            //获取语言
            $lang                       = (new StaffServer())->getLanguage($advance_fuel['staff_info_id']);
            $t                          = $this->getTranslation($lang);
            $advance_fuel_email_content = $t->_('advance_fuel_email_content',
                ['created_at' => show_time_zone($advance_fuel['created_at'],'Y-m-d')]);
            $toUsers                    = $staff_info['personal_email'];
            if ($toUsers && is_string($toUsers)) {
                $toUsers = explode(',', $toUsers);
            }
            $title = $t->_('advance_fuel_email_title');
            
            $fileName   = 'ANNEX'.$advance_fuel['staff_info_id'].'.pdf';
            $tmpFilePath = sys_get_temp_dir() . '/' . basename($advance_fuel['pdf_url']).'.pdf';
            if (!file_put_contents($tmpFilePath, file_get_contents($advance_fuel['pdf_url']))) {
                $this->getDI()->get("logger")->write_log('AdvanceFuel sendMail 附件下载失败 ','error');
            }
            $ret   = (new MailServer())->send_mail($toUsers, $title, $advance_fuel_email_content,$tmpFilePath,$fileName);
            $this->getDI()->get('logger')->write_log("AdvanceFuel sendMail 发送邮件结束 toUsers:" . implode(',',
                    $toUsers) . ', email_content:' . $advance_fuel_email_content . ',result:' . $ret, 'info');
        }
        return true;
    }

    /**
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param $state
     * @return array
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        return [];
    }

    /**
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false, false, false, false, false, false);
    }
}