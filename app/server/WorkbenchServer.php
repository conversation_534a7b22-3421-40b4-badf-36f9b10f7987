<?php


namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\PublicRepository;

class WorkbenchServer extends BaseServer{

    public function __construct($lang ,$timezone)
    {
        parent::__construct($lang,$timezone);
    }

    public function getInfo($paramIn): array
    {

        $data['url'] = '';
        $organizationId = $this->processingDefault($paramIn, 'organization_id');
        $device_id = $this->processingDefault($paramIn, 'device_id');
        $department_id = $this->processingDefault($paramIn, 'department_id', 2);
        $setting_model = new BySettingRepository();
        $dc_auth_code=  $setting_model->get_setting('dc_staff_statistics_auth_code');
        $dc_staff_statistics_url=  $setting_model->get_setting('dc_staff_statistics_url');

        if($dc_auth_code && isset($paramIn['from_kit'])){
            $build['from'] = 'kit';
            if(!empty($device_id)){
                $build['c'] =  $device_id;
            }
            if(!empty($organizationId)){
                $build['s'] =  auth_code($organizationId, 'ENCODE', $dc_auth_code);
            }
            $data['url'] = $dc_staff_statistics_url . '?'. http_build_query($build);
            return $data;
        }
        $headerData       = $this->request->getHeaders();
        $device_id = $this->processingDefault($headerData, 'X-Device-Id');
        $job_title = $setting_model->get_setting('workbench_job_title');
        $workbench_sys_department_id =  $setting_model->get_setting('workbench_sys_department_id');
        if($dc_auth_code && $department_id && $job_title && $workbench_sys_department_id && in_array($paramIn['job_title'],explode(',',$job_title)) && in_array($department_id,explode(',',$workbench_sys_department_id))){
            $build['from'] = 'backyard';
            if(!empty($device_id)){
                $build['c'] =  $device_id;
            }
            if(!empty($organizationId)){
                $build['s'] =  auth_code($organizationId, 'ENCODE', $dc_auth_code);
            }
            $data['url'] = $dc_staff_statistics_url . '?'. http_build_query($build);
        }
        $this->getDI()->get('logger')->write_log(['workbench'=>$data['url'],'paramIn'=>$paramIn],'info');
        return $data;

    }

    /**
     * @description: 获取按钮
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/8/9 21:33
     */
    public function getMenu($paramIn): array{

        $result = [];
        //获取用户信息
        $staff            = HrStaffInfoModel::findFirst([
                                                            'conditions' => ' staff_info_id = :staff_id: ',
                                                            'bind'       => [
                                                                'staff_id' => $paramIn['staff_id'] ?? '',
                                                            ]
                                                        ]);
        $staff            = $staff ? $staff->toArray() : [];

        $setting_env = new SettingEnvServer();

        //获取 home_banner_menu 菜单
        $home_banner_menu =  $setting_env->getSetValFromCache('by_home_banner_menu');
        //不存在账号或者是外协
        if(empty($staff) || $staff['formal'] == HrStaffInfoModel::FORMAL_0 ) {
            //获取外协的home_banner_menu 菜单
            $home_banner_menu = $setting_env->getSetValFromCache('by_outsourcing_home_banner_menu') ?? $home_banner_menu;
        }

        //请假首页入口
        if (isCountry(['TH','PH', 'MY']) && !empty($staff) && in_array($staff['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
            $home_banner_menu = $setting_env->getSetValFromCache('by_agent_home_banner_menu') ?? $home_banner_menu;
        }

        //如果都没有 默认获取 配置文件里的
        if(empty($home_banner_menu)){
            $home_banner_menu          = UC('workbenchBannerMenu');
        }

        //马来 销售代理 只有crm入口
        if(isCountry('MY') && in_array(RolesModel::SALES_AGENT,$paramIn['positions'])){
            $home_banner_menu = $setting_env->getSetValFromCache('sales_agent_home_banner_menu');
        }


        //获取学习计划的前端域名
        $school_web_url =  $setting_env->getSetValFromCache('school_web_url');
        $h5_v3 = env('h5_endpoint');
        $crm_h5_v3 = env('crm_h5_endpoint');

        $use_new_make_up_page = 'replen-card';

        //新增 请假图片权限判断
        $leaveServer = new LeaveServer($this->lang, $this->timeZone);
        $leaveFlag = $leaveServer->leavePermissionFromCache($staff);

        //iOS审核账号
        if ($paramIn['staff_id'] == 87441) {
            $home_banner_menu = $setting_env->getSetValFromCache('by_home_banner_menu');
            $leaveFlag = true;
        }
        $home_banner_menu  =  json_decode($home_banner_menu,true);
        //获取翻译
        $t  = $this->getTranslation();
        foreach($home_banner_menu as $k=>&$v){
            if($leaveFlag == false && $v['id'] == 'home_leave'){
                unset($home_banner_menu[$k]);
                continue;
            }
            if ($v['id'] == 'home_study_plan') {
                //学习计划域名需要单独拼装
                $v['dst'] = $school_web_url . $v['dst'];
            } elseif ($use_new_make_up_page && $v['id'] == 'home_make_up') {
                //补卡前段换到v3
                $v['dst'] = $h5_v3 . $use_new_make_up_page;
            } elseif ($v['id'] == 'home_crm') {
                $v['dst'] = $crm_h5_v3 . $v['dst'];
            } else {
                $v['dst'] = env('sign_url') . $v['dst'];
            }
            //获取翻译
            $v['title'] = $t->_($v['title_lang_key']);
            if(isCountry('TH') && $v['id'] == 'home_leave'){
                $v['dst'] = env('h5_endpoint') . 'leave';
            }

        }
        //banner 按钮
        $result['home_banner_menu'] = array_values($home_banner_menu);
        //工作台
        $result['workbench'] = $this->getInfo($paramIn);
        return $result;
    }

}