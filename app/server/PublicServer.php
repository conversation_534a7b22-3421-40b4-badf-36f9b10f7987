<?php
/**
 * Created by PhpStorm.
 * User: zyp
 * Date: 2020-01-07
 * Time: 14:21
 */


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\BanklistModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Repository\StaffRepository;

class PublicServer extends BaseServer
{
    public $timezone;
    public $public;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;

        $this->public = new PublicRepository();
    }

    /**
     * 获取动态资源列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function sysList($paramIn = [])
    {
        $typeCode    = $paramIn['type_code'] ?? '';
        if (!$typeCode) {
            $data              = [
                'getProvinceList',
                'getStoreList',
                'getRepeatHcList',
                'getProvinceCityList'
            ];
            $returnArr['data'] = $data;
            return $this->checkReturn($returnArr);
        }
        $typeCodeArr = explode(',', $typeCode);
        foreach ($typeCodeArr as $k => $v) {
            $state = method_exists($this, $v);
            if ($state) {
                $data = $this->$v($paramIn);
                $returnArr['data'][$v] = $data;
            }
        }

        return $this->checkReturn($returnArr);
    }

    public function getBankList($params)
    {
        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoOne($params['staff_id'], 'hire_type');

        $bankIdEnvCode = in_array($staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether) && isCountry(['MY','PH','TH']) ? 'by_staff_bank_id_list_independent' : 'by_staff_bank_id_list';

        //[1]获取银行枚举选项数据
        $bank_ids = (new SettingEnvServer())->getSetVal($bankIdEnvCode);
        $bank_list = [];
        if($bank_ids){
            $bank_list = BanklistModel::find([
                'conditions' => 'bank_id in ({bank_ids:array})',
                'bind'=> ['bank_ids'=>explode(',',$bank_ids)],
                'columns' => "bank_id as value ,bank_name as label,max_length,min_length",
                'order' => 'sort_num desc,bank_id desc',
            ])->toArray();
        }
        return $bank_list;
    }

    public function getAllBankList($params)
    {
        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoOne($params['staff_id'], 'hire_type');
        $bankIdEnvCode = in_array($staffInfo['hire_type'] , HrStaffInfoModel::$agentTypeTogether) ? 'by_staff_bank_id_list_independent' : 'by_staff_bank_id_list';
        //[1]获取银行枚举选项数据
        $bank_ids = (new SettingEnvServer())->getSetVal($bankIdEnvCode);
        $bank_list = [];
        if($bank_ids){
            $bank_list = BanklistModel::find([
                'conditions' => 'bank_id in ({bank_ids:array})',
                'bind'=> ['bank_ids'=>explode(',',$bank_ids)],
                'columns' => "bank_id as value ,bank_name as label,max_length,min_length",
                'order' => 'sort_num desc,bank_id desc',
            ])->toArray();
        }
        return $bank_list;
    }


    /**
     * 获取静态资源列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function staticSysList($params)
    {

        $data['bank_enums'] = $this->getBankList($params);

        //[2] 获取xxx列表

        //返回静态资源数据
        $returnArr['data'] = $data;
        return $this->checkReturn($returnArr);
    }

    /**
     * 获取省份下拉列表
     */
    public function getProvinceList($paramIn = [])
    {
        $returnData = $this->public->getProvinceCode();
        return $returnData;
    }

    /**
     * 获取DC、SP网点列表
     * @param array $paramIn
     * @return array
     */
    public function getStoreList($paramIn = [])
    {
        return $this->public->getStoreList(['category' => '1,2']);
    }

    /**
     * 检查HC申请是否重复
     */
    public function getRepeatHcList($paramIn = [])
    {
        //[1]数据验证
        /*$validations = [
            "worknode_id"       => "Required|Str|>>>:" . $this->getTranslation()->_('miss_args'),
            "department_id"     => "Required|Int|>>>:" . $this->getTranslation()->_('miss_args'),
            "reason_type"       => "Required|Int|>>>:" . $this->getTranslation()->_('miss_args'),
            "job_id"            => "Required|Int|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);*/

        try {
            //发送rpc请求，获取重复hc列表
            $params = [
                'worknode_id'   => $paramIn['worknode_id'] ?? '',
                'department_id' => $paramIn['department_id'] ?? 0,
                'reason_type'   => $paramIn['reason_type'] ?? 0,
                'job_id'        => $paramIn['job_id'] ?? 0,
            ];
            $rpcClient = (new ApiClient('winhr_rpc','','getRepeatHc', $this->lang));
            $rpcClient->setParams($params);
            $return = $rpcClient->execute();

            if (isset($return['result'])) {
                return $this->jsonReturn(self::checkReturn(['data' => $return['result']]));
            } else {
                return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
            }
        }catch (\Exception $e) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008').$e->getMessage()));
        }
    }

    /**
     * 获取省份和城市的集合
     */
    public function getProvinceCityList()
    {
        //查询所有省份信息
        $provinceList = SysProvinceModel::find([
            'conditions' => 'deleted = :deleted:',
            'bind'       => ['deleted' => enums::IS_DELETED_NO],
            'columns'    => 'code as province_code,name'
        ])->toArray();

        $provinceList = array_column($provinceList, null, 'province_code');

        //查询所有城市
        $cityList = SysCityModel::find([
            'conditions' => 'deleted = :deleted:',
            'bind'       => ['deleted' => enums::IS_DELETED_NO],
            'columns'    => 'code as city_code,name,province_code'
        ])->toArray();

        foreach ($cityList as $v) {
            if (!empty($provinceList[$v['province_code']])) {
                $v['province_name']                               = $provinceList[$v['province_code']]['name'] ?? '';
                $provinceList[$v['province_code']]['city_list'][] = $v;
            }
        }

        return array_values($provinceList);
    }
}
