<?php

namespace FlashExpress\bi\App\Server;

use Exception;

class TrainingMessageServer extends BaseServer
{
    public $timezone;
    public $lang;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
        $this->lang     = $lang;
    }

    public function dealMessage($staff_id,$msg_id,$is_deal_read = false)
    {
        if(empty($staff_id) || empty($msg_id)){
            throw  new Exception('params error');
        }
        $staff_id = intval($staff_id);

        $sql = "select m.message as content , m.related_id, mc.read_state
                from message_courier  as mc 
                inner join  message_content as m on mc.message_content_id = m.id
                where mc.id = '{$msg_id}'  and mc.staff_info_id = {$staff_id} and mc.is_del = 0
                ";
        $message = $this->getDI()->get('db_coupon_r')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);

        if($message['read_state'] == 0 && $is_deal_read){
            $sql_query = "update message_courier  set read_state=1  where id= '{$msg_id}' ";
            $this->getDI()->get('db_coupon')->execute($sql_query);
        }
        return $message;
    }

}