<?php

/**
 * 业务仓库基类
 */

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\CacheService;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\MobileHelper;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\BanklistModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Traits\FactoryTrait;
use Hprose\Http\Client;
use Phalcon\Mvc\Model\Query\BuilderInterface;
use Phalcon\Mvc\Model\ResultsetInterface;
use Phalcon\Paginator\Adapter\Model;
use Phalcon\Paginator\Adapter\QueryBuilder;
use Phalcon\Translate\Adapter;
use Phalcon\Translate\Adapter\NativeArray;
use WebGeeker\Validation\Validation;
use FlashExpress\bi\App\Traits\FlashTokenTrait;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\SysStoreTypeModel;
use FlashExpress\bi\App\library\Exception\ValidationException;

class BaseServer extends CacheService
{
    use FlashTokenTrait;
    use FactoryTrait;

    /**
     * 部门
     * @var array
     */
    protected static $departmentNameBox = [];
    /**
     * 职位
     * @var array
     */
    protected static $jobTitleNameBox = [];
    /**
     * 银行类型
     * @var array
     */
    protected static $bankTypeNameBox = [];
    /**
     * 大区
     * @var array
     */
    protected static $regionNameBox = [];
    /**
     * 片区
     * @var array
     */
    protected static $pieceNameBox = [];
    /**
     * 网点
     * @var array
     */
    protected static $storeNameBox = [];
    /**
     * 网点类型
     * @var array
     */
    protected static $storeCategoryNameBox = [];


    public $timeZoneOfThailand;

    public $lang = 'zh-CN'; // 语言变量
    public $timeZone;
    public $languagePack;
    /**
     * @var Adapter
     */
    public $t;

    public static $flePlatform = 0;//平台区分来源：1. by 2. kit


    public function __construct($lang = '', $timezone='+08:00')
    {
        parent::__construct($lang);

        //获取全局语言对象
        $this->t    = $this->languagePack->t;
        $this->lang = $this->languagePack->lang;

        $this->timeZone = empty($timezone) ? $this->config['application']['timeZone'] : $timezone;
        $this->timeZoneOfThailand = $this->getDI()->getConfig()->application['timeZoneOfThailand'];
        date_default_timezone_set($this->getDI()->getConfig()->application['timeZoneGMT']);
    }

    //设置平台来源 1:by 2 kit 在 请求头中获取， 任务执行为 0
    public static function setPlatform($flePlatform = 0)
    {
        self::$flePlatform = $flePlatform;
    }
    /**
     * @param $lang
     * @return NativeArray
     */
    public function getTranslationByLang($lang)
    {
        $this->lang = $this->languagePack->formatLang($lang);
        return $this->languagePack->getTranslation($lang);
    }

    public function getTranslation($lang = '')
    {
        // 返回一个语言包
        return $this->languagePack->getTranslation($lang ?: $this->lang);
    }

    /**
     * @param $sec
     * @return false|float
     */
    public function getHours($sec)
    {
        if ($sec >= 3600) {
            $data = floor($sec / 3600) + round(($sec - floor($sec / 3600) * 3600) / 3600, 2);
        } else {
            $data = round($sec / 3600, 2);
        }
        return round($data, 2);
    }

    const HRIS_STAFF_PROFILE = 1;
    const WORK_ORDER = 2;
    const PROBLEMATIC_ITEM = 3;
    const INSURE_DOC_CLAIM_F = 4;
    const INSURE_DOC_SHOW_PRODUCT = 5;
    const SS_COURT_FILE = 6;
    const PARCEL_CHANGE = 7;
    const CERTIFICATE_PDF = 8;

    const OSS_DIR_MAPS = [
        self::HRIS_STAFF_PROFILE => 'HRIS_STAFF_PROFILE', // fbi 用户类型dir type
        self::WORK_ORDER => 'WORK_ORDER',//工单
        self::PROBLEMATIC_ITEM => 'PROBLEMATIC_ITEM',
        self::INSURE_DOC_CLAIM_F => 'INSURE_DOC_CLAIM_F',//索赔说明(保价险索赔需要)
        self::INSURE_DOC_SHOW_PRODUCT => 'INSURE_DOC_SHOW_PRODUCT',//声明价值证明材料(保价险索赔需要)
        self::SS_COURT_FILE => 'SS_COURT_FILE',//闪速判案附件
        self::PARCEL_CHANGE => 'PARCEL_CHANGE',//lazada超时包裹
        self::CERTIFICATE_PDF => 'CERTIFICATE_PDF',//证明下载 工资条 pdf 文件夹
    ];




    /**
     * 处理默认值
     * @param $paramIn
     * @param $paramIn 数组|$parameter 参数名称 |$type 类型 1 字符串 2整型 3数组 4 布尔类型
     * @return string
     */
    public function processingDefault($paramIn, $parameter, $type = 1, $default = '')
    {
        switch($type)
        {
            case 1:
                $default = !empty($default) ? trim($default) : '';
                break;
            case 2:
                $default = !empty($default) ? $default : 0;
                break;
            case 3:
                $default = !empty($default) ? $default : [];
                break;
            case 4:
                $default = !empty($default) ? $default : false;
                break;
        }

        if ($type == 4) {
            return isset($paramIn[$parameter]) ? $paramIn[$parameter] : $default;
        } else {
            return isset($paramIn[$parameter]) && !empty($paramIn[$parameter]) ? $paramIn[$parameter] : $default;
        }
    }

    /**
     * 参数校验 校验错误抛出
     * @Access  public
     * @Param   $paramIn 入参校验数组 | $validations 校验规则
     * @Return  array
     */
    public function validateCheck($paramIn = [], $validations = [],$err = -1)
    {
        try
        {
            Validation::validate($paramIn, $validations);
        } catch(\Exception $e)
        {
            $this->jsonReturn($this->checkReturn($err, $e->getMessage()));
        }
    }

    /**
     * 校验返回
     * @param $paramIn
     * @param $parameter
     * @return array
     */
    public function checkReturn($paramIn, $parameter = null)
    {
        if (is_array($paramIn)) {
            $code = isset($paramIn['code']) && !empty($paramIn['code']) ? $paramIn['code'] : 1;
            $msg = isset($paramIn['msg']) && !empty($paramIn['msg']) ? $paramIn['msg'] : $this->getTranslation()->_('5001');
            $data = isset($paramIn['data']) && !empty($paramIn['data']) ? $paramIn['data'] : null;
        } elseif ($paramIn == -1) {
            //[2] 参数不正确时返回
            $code = 0;
            $msg = '参数' . $parameter . '不能为空!';
        } elseif ($paramIn == -2) {
            //[2] 参数不正确时返回
            $code = 0;
            $msg = '参数' . $parameter . '错误,json格式不正确!';
        } elseif ($paramIn == -3) {
            //[3] 参数不正确时返回
            $code = 0;
            $msg = $parameter;
        } elseif ($paramIn == 1) {
            //[4] 请求成功时返回
            $code = 1;
            $msg = $this->getTranslation()->_('5001');
        }
        return [
            'code'    => isset($code) ? $code : 0,
            'msg'     => isset($msg) ? $msg : '',
            'message' => isset($msg) ? $msg : '',
            'data'    => isset($data) ? $data : null,
            'tid'     => molten_get_traceid(),
        ];
    }

    /**
     * JSON返回
     * @param $paramIn array | $jsonCallback 处理跨域
     * @param bool $jsonCallback
     * @return void
     */
    public function jsonReturn($paramIn, $jsonCallback = false)
    {
        header('Content-type: application/json;charset=utf-8');
        if ($jsonCallback) {
            $json_data = json_encode($paramIn, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            echo $jsonCallback . "(" . $json_data . ")";
            exit();
        } else {
            $json_data = json_encode($paramIn, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            echo $json_data;
            exit();
        }
    }

    /**
     * 记录日志
     * @Access  public
     * @Param   request
     * @Return  array
     * @param string $title
     * @param $message
     * @param string $model
     * @param string $level
     */
    public function wLog($title = '', $message, $model = '', $level = 'info')
    {
        // 记录日志
        if (!empty($message)) {
            if (is_array($message)) {
                $message = json_encode($message);
            }

            //默认级别 为info
            if ($level != 'error')
                $level = 'info';


            $debugInfo = debug_backtrace();
            $debugInfo = empty($debugInfo[1]) ? array() : $debugInfo[1];

            $function = empty($debugInfo['function']) ? '' : $debugInfo['function'];
            $class = empty($debugInfo['class']) ? '' : $debugInfo['class'];

            //截取 最后 类名  FlashExpress\\bi\\App\\Controllers\\ControllerBase
            if (strstr($class, '\\')) {
                $arr = explode('\\', $class);
                $class = end($arr);
            }

            //写入日志 统一方法
            $message = "{$model}:{$title}-{$message}";
            $logger = $this->getDI()->get('logger');
            $logger->write_log($message, $level, $class, $function);
        }
    }

    /**
     * 获取唯一ID
     * @Access  public
     * @Param   request
     * @Return  string
     */
    public function getID()
    {
        $returnData = null;
        try{
            $client      = new Client(env('go_rpc_server','http://127.0.0.1:8051/svc/call'), false);
            $ret         = json_decode($client->GetUniqueID(1));
            if (isset($ret) && $ret && $ret->code == 1) {
                $returnData = substr($ret->data,4, 15);
            }
        } catch (\Exception $e) {
           // $this->wLog("error--生成ID失败", $e->getMessage(), 'BaseServer');
        }
        return $returnData ?? $this->getRandomId();
    }

    /**
     * 获取唯一ID
     * @Access  public
     * @Param   request
     * @Return  string
     */
    public function getRandomId()
    {
        $returnData = null;
        try{
            //获取ip地址
            $returnData = date("YmdHi") . str_pad($this->getRandomIncrSalt('workflow_random_id', 3600), 7, 0, STR_PAD_LEFT);
        }catch (\Exception $e) {
            $this->wLog("error--生成ID失败", $e->getMessage(), 'BaseServer');
        }
        return $returnData;
    }

    public function getRandomIncrSalt($key, $expire)
    {
        $redis = $this->getDI()->get('redisLib');
        $luaScript = <<<LUA
local key = KEYS[1]
local ttl = ARGV[1]
local salt = 0
if (redis.call('EXISTS', key) == 1) then
    return redis.call('INCR', key)
else
    salt = redis.call('INCR', key)
    redis.call('EXPIRE', key, ttl)
end
return salt
LUA;
        return $redis->eval($luaScript, [$key, $expire], 1);
    }

    /**
     * 根据 语言环境 获取对应字段名 涉及表 share_center_dir  share_center_file 字段名(name_th,name_en,name_cn)
     * @param $lang
     * @return string
     */
    public function get_lang_column($lang = 'th')
    {
        $lang_arr = [
            'en'    => 'name_en',
            'th'    => 'name_th',
            'zh-CN' => 'name_cn',
            'vi'    => 'name_vn',
        ];
        return empty($lang_arr[$lang]) ? '' : $lang_arr[$lang];
    }

    /**
     * 拼组数据
     * @param $list
     * @return array
     */
    public function format($list): array
    {
        $return = [];
        $t      = $this->getTranslation();
        foreach ($list as $key => $v) {
            if (isset($v['serial_num']) && $v['serial_num'] > 0){
                // 外协逻辑
                $_key = ($t->_($v['key']) ?? '').$v['serial_num'];
            }else{
                $_key = $t->_($key) ?? '';
            }
            $return[] = [
                'key'      => $_key,
                'key_tips' => is_array($v) && isset($v['key_tips']) ? $v['key_tips'] : null,
                'key_icon' => is_array($v) && isset($v['key_icon']) ? $v['key_icon'] : null,
                'value'    => is_array($v) && isset($v['value']) ? $v['value'] : $v,
                'tips'     => is_array($v) && isset($v['tips']) ? $v['tips'] : null,
                'color'    => is_array($v) && isset($v['color']) ? $v['color'] : null,
            ];
        }
        return $return;
    }
    //审批详情页组装 pdf 链接数据
    public function formatDetailPdf($list){
        $return = [];
        $t      = $this->getTranslation();
        foreach ($list as  $v) {
            $return[] = [
                'key'      =>  $t->_($v['key']),
                'key_tips' => is_array($v) && isset($v['key_tips']) ? $v['key_tips'] : null,
                'key_icon' => is_array($v) && isset($v['key_icon']) ? $v['key_icon'] : null,
                'value'    => is_array($v) && isset($v['value']) ? $v['value'] : $v,
                'tips'     => is_array($v) && isset($v['tips']) ? $v['tips'] : null,
                'color'    => is_array($v) && isset($v['color']) ? $v['color'] : null,
                'type' => enums::APPROVAL_DETAIL_TYPE_PDF,//pdf 展示类型
                'simple_val' => $v['url'],
            ];
        }
        return $return;
    }

    //审批详情页组装 pdf 链接数据
    public function formatSickOther($list, $content = ''){
        $return = [];
        $t      = $this->getTranslation();
        foreach ($list as  $key => $v) {
            $row =[
                'key'      => $t->_($key),
                'value'    => is_array($v) && isset($v['value']) ? $v['value'] : $v,
            ];
            if($key == 'other'){
                $row['type'] = enums::APPROVAL_DETAIL_TYPE_OTHER_CONTENT;
                $row['other_content'] = $content;
            }
            $return[] = $row;

        }
        return $return;
    }

    /**
     * 获取审批行为 TODO 状态 5 超时关闭
     * @param array $paramIn
     * @return mixed
     */
    public function getStaffOptions($paramIn = [])
    {
        $status  = $this->processingDefault($paramIn, 'status');
        $options = $this->processingDefault($paramIn, 'option');
        if (in_array($status, [2, 3, 4, 5, 6])) {
            $option = $options['afterApproval'] ?? 0;
        } else {
            $option = $options['beforeApproval'] ?? 0;
        }
        if ($option != 0) {
            foreach (explode(',', $option) as $v) {
                $returnData['code' . $v] = intval($v);
            }
        }
        if (empty($returnData)) {
            $returnData = (object)null;
        }
        return $returnData;
    }

    /**
     * 上传文件到oss
     *
     * @param $filePath
     * @param $ossDir
     *
     */
    public function uploadFileOss($filePath, $ossDir)
    {
        if (!file_exists($filePath)) {
            throw new \Exception("不存在的文件路径 " . $filePath . json_encode(debug_backtrace(false, 2), JSON_UNESCAPED_UNICODE));
        }

        $pathInfo = pathinfo($filePath);

        $url    = env('oss_fle_url', 'http://192.168.0.228:8090/fle-svc/com.flashexpress.fle.svc.api.OssSvc');


        if (env('break_away_from_ms')) {
            $return['result'] = OssHelper::uploadFileHcm('BACKYARD_UPLOAD', $pathInfo['basename']);
        } else {
            $fle_rpc = (new ApiClient($url, '', 'buildPutObjectUrl', $this->lang));
            $fle_rpc->setParam([$ossDir, $pathInfo['basename'], '']);
            $return = $fle_rpc->execute();
        }

        if ($return && isset($return['result'])) {
            $putUrl = $return['result']['put_url'];
            $contentType = $return['result']['content_type'];
        } else {
            throw new \Exception( $filePath . " file upload fail" . json_encode(debug_backtrace(false, 2), JSON_UNESCAPED_UNICODE) . json_encode($return, JSON_UNESCAPED_UNICODE));
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $putUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: ' . $contentType]);

        curl_setopt($ch, CURLOPT_PUT, true);
        curl_setopt($ch, CURLOPT_INFILE, fopen($filePath, 'r'));
        curl_setopt($ch, CURLOPT_INFILESIZE, filesize($filePath));

        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 3600);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 0);

        $response = curl_exec($ch);
        $error_code = curl_errno($ch);

        $this->logger->write_log("uploadFileOss info " . $error_code . " response " . $response, "info");
        curl_close($ch);
        if ($error_code == 0) {
            return $return['result'];
        } else {
            throw new \Exception("上传文件失败" . json_encode($response) . json_encode(debug_backtrace(false, 2), JSON_UNESCAPED_UNICODE));
        }
    }

    public function uploadImage($staff_id,$url)
    {
        $offer_attachment_path = sys_get_temp_dir() . "/" . uniqid($staff_id. md5( time()).rand(10,10000).'img') . '.jpg';
        file_put_contents($offer_attachment_path,file_get_contents($url));
        $upload_result = OssHelper::uploadFile($offer_attachment_path, false);
        return $upload_result['object_url'];
    }

    /**
     * http POST 请求
     * @param $url
     * @param $param
     * @param null $header 如果带头信息 返回http状态和响应json 字符串  如果传null 返回json解析后的数组(不带状态码)
     * @param null $pwd
     * @param int $timeout
     * @return bool|mixed|string
     */
    public function httpPostWithHttpCode($url, $param, $header = null,$pwd = null, $timeout = 10)
    {
        $curl     = curl_init();
        $data     = $param;
        $log_data = is_array($param) ? json_encode($param) : $param;

        if (!isset($header)) {
            $header[] = "Content-type: application/x-www-form-urlencoded";
            $header[] = "Accept: application/json";
            $header[] = "Accept-Language: " . $this->lang;
            $escape   = true;
        }

        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, true);        // post
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data); // post data
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);

        $responseText = curl_exec($curl);
        if (curl_errno($curl)) {
            $responseText .= "error: " . curl_error($curl);
            $this->getDI()->get('logger')->write_log("url:" . $url . " http_post_error {$log_data} " . curl_error($curl),
                'error');
        }
        //http 状态码
        $return['code_status'] = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        $this->getDI()->get('logger')->write_log("url " . $url . " http_post {$return['code_status']} {$log_data} response {$responseText} " . json_encode($header),
            'info');
        if (isset($escape)) {
            $responseText = json_decode($responseText, true);
            return $responseText;
        }

        $return['response'] = $responseText;
        return $return;
    }

    /**
     *
     * 获取短链接
     *
     * @param $url
     *
     */
    public function getShortUrl($url)
    {
        $apiClient = new ApiClient('hr_rpc', '', 'get_short_url', $this->lang);
        $apiClient->setParams([
            'url' => $url
        ]);
        $data = $apiClient->execute();
        if (isset($data['code']) && $data['code'] == 1) {
            $url = $data['result']['data'] ?? $url;
        }

        return $url;
    }


    /**
     * http POST 请求 针对java 新服务调用 参数要json格式化之后 跟别人不一样
     * @param $url
     * @param $data
     * @param null $header 如果带头信息
     * @param $is_post  ture  post   false  get
     * @param int $timeout
     * @return bool|mixed|string
     */
    public function httpPost($url, $data, $header = null, $timeout = 10,$is_post = true)
    {
        $curl = curl_init();
        $log_data = is_array($data) ? json_encode($data) : $data;
        $header[] = "Content-type: application/json";
        $header[] = "Accept: application/json";
        $header[] = "Accept-Language: " . $this->lang;

        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        if($is_post) {
            curl_setopt($curl, CURLOPT_POST, true);                     // post
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data)); // post data
        }
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);

        $responseText = curl_exec($curl);

        if (curl_errno($curl)) {
            $this->getDI()->get('logger')->write_log("http_url ".$url. " http_post_error {$log_data} " . curl_error($curl),'error');
            throw new \Exception('curl error' .curl_error($curl) .curl_getinfo($curl,CURLINFO_HTTP_CODE) );
        }
        $this->getDI()->get('logger')->write_log("http_url ".$url. " http_post {$log_data} response {$responseText} " .json_encode($header),'info');

        curl_close($curl);
        $responseText = json_decode($responseText,true);
        return $responseText;
    }

    /**
     * 查询分页
     * @param $query
     * @param $page
     * @param $pageSize
     * @return array
     */
    public function paginate($query, $page, $pageSize)
    {
        switch (true) {
            case $query instanceof BuilderInterface:
                $paginator = new QueryBuilder(
                    [
                        'builder' => $query,
                        'limit'   => $pageSize ?? 10,
                        'page'    => $page ?? 1
                    ]
                );
                break;
            case $query instanceof ResultsetInterface:
                $paginator = new Model(
                    [
                        'data'  => $query,
                        'limit' => $pageSize ?? 10,
                        'page'  => $page ?? 1
                    ]
                );
                break;
            default:
                throw new \InvalidArgumentException('Arg #1 should be a Builder or Resultset');
        }

        $result = $paginator->getPaginate();
        return [$result->items->toArray(), $result->total_items];
    }

    /**
     * 文件下载到本地
     * @param $file_url
     * @param $path
     * @param string $save_file_name
     * @return string
     */
    public function downloadFile($file_url, $path = '', $save_file_name = '')
    {
        $basPath = sys_get_temp_dir();
        if ($path) {
            $basPath = $basPath . '/' . $path;
        }
        $basPath = $basPath . '/' .date('Ymd');
        if (!is_dir($basPath)) {
            mkdir($basPath, 0777, true);
        }

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $file_url);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);

        $file = curl_exec($ch);

        curl_close($ch);

        //传入保存文件的名称
        $filename = $save_file_name ?: pathinfo($file_url, PATHINFO_BASENAME);

        $resource = fopen($basPath. '/'. $filename, 'a');

        fwrite($resource, $file);

        fclose($resource);

        return $basPath . '/' . $filename;
    }

    /**
     * pdf 转 img
     * @param $localPdfUrl
     * @param $fileName
     * @param string $path
     * @return array
     * @throws \ImagickException
     */
    public function pdfToImg($localPdfUrl, $path = '', $fileName = '')
    {
        $basPath = sys_get_temp_dir();
        if ($path) {
            $basPath = $basPath . '/' . $path;
        }
        $basPath = $basPath . '/' .date('Ymd');
        if (!is_dir($basPath)) {
            mkdir($basPath, 0777, true);
        }

        $imagick = new \Imagick();
        $imagick->setResolution(120, 120); //设置分辨率
        $imagick->setCompressionQuality(100); //压缩质量
        $imagick->readImage($localPdfUrl);

        $filenameUrl = $basPath . '/' . $fileName . '.png';
        $imagick->writeImage($filenameUrl);
        $return = [];
        foreach ($imagick as $key => $var){
            $var->setImageFormat('png');
            $filenameUrl = $basPath . '/' . $fileName . '_' . $key . '.png';
            if($var->writeImage($filenameUrl) == true){
                $return[] = $filenameUrl;
            }
        }

        return $return;
    }

    /**
     * 用户设备当前版本 大于等于 配置版本 返回 true
     * @param $code
     * @param $staff_id
     * @return bool|mixed
     */
    public static function compareMobileVersionByConfig($code, $staff_id)
    {
        $see    = new SettingEnvServer();
        $config = $see->getMultiEnvByCode([$code, $code . '_white_list']);

        if (!empty($config[$code . '_white_list'])) {
            if (in_array($staff_id, explode(',', $config[$code . '_white_list']))) {
                return true;
            }
        }

        $equipment_info = [];
        if (!empty($config[$code])) {
            $equipment_info = json_decode($config[$code], true);
        }

        if (!$equipment_info) {
            return false;
        }
        return MobileHelper::compareVersion($equipment_info);
    }


    public function getExcelData($filePath, $setColumnType = [])
    {

        $config       = ['path' => dirname($filePath)];
        $fileRealName = basename($filePath);
        $excel        = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($fileRealName)->openSheet();
        if (!empty($setColumnType)) {
            $excel->setType($setColumnType);
        }
        return $excel->getSheetData();
    }

    /**
     * 获取临时url
     * @param $file_path
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getTplPath($file_path)
    {
        // 上传OSS
        $upload_result = OssHelper::uploadFile($file_path, true);
        return $upload_result['object_url'];
    }


    /**
     * 获取部门名称
     * @param $department_id
     * @return mixed|string
     */
    public function showDepartmentName($department_id)
    {
        if (empty(self::$departmentNameBox)) {
            $departmentList = SysDepartmentModel::find(['columns'=>'id,name'])->toArray();
            self::$departmentNameBox = array_column($departmentList, 'name', 'id');
        }
        return self::$departmentNameBox[$department_id] ?? '';
    }

    /**
     * 获取职位名称
     * @param $job_title_id
     * @return mixed|string
     */
    public function showJobTitleName($job_title_id)
    {
        if (empty(self::$jobTitleNameBox)) {
            $jobTitleList = HrJobTitleModel::find(['columns'=>'id, job_name'])->toArray();
            self::$jobTitleNameBox = array_column($jobTitleList, 'job_name', 'id');
        }
        return self::$jobTitleNameBox[$job_title_id] ?? '';
    }

    /**
     * 获取银行类型名称
     * @param $bank_id
     * @return mixed|string
     */
    public function showBankTypeName($bank_id)
    {
        if (empty(self::$bankTypeNameBox)) {
            $bankTypeList = BanklistModel::find([
                'columns'    => "bank_id,bank_name",
            ])->toArray();
            self::$bankTypeNameBox = array_column($bankTypeList, 'bank_name', 'bank_id');
        }
        return self::$bankTypeNameBox[$bank_id] ?? '';
    }



    /**
     * 获取网点名称
     * @param $store_id
     * @return mixed|string
     */
    public function showStoreName($store_id)
    {
        if (empty(self::$storeNameBox)) {
            $storeList = SysStoreModel::find([
                'columns' => 'id,name,manage_region,manage_piece,category',
            ])->toArray();
            self::$storeNameBox = array_column($storeList, null, 'id');
            self::$storeNameBox['-1'] = [
                'name' => enums::HEAD_OFFICE,
            ];
        }
        return self::$storeNameBox[$store_id]['name'] ?? '';
    }
    /**
     * 根据网点ID获取大区名称
     * @param $store_id
     * @return mixed|string
     * @throws ValidationException
     */
    public function showRegionNameByStoreId($store_id)
    {
        if (empty(self::$storeNameBox)) {
            throw new ValidationException('请先初始化$storeNameBox');
        }
        $region_id = self::$storeNameBox[$store_id]['manage_region']??0;
        if (empty(self::$regionNameBox)) {
            $regionList = SysManageRegionModel::find([
                'columns' => 'id,name',
            ])->toArray();
            self::$regionNameBox = array_column($regionList, 'name', 'id');
        }
        return self::$regionNameBox[$region_id] ?? '';
    }


    /**
     * 根据网点ID获取片区名称
     * @param $store_id
     * @return mixed|string
     * @throws ValidationException
     */
    public function showPieceNameByStoreId($store_id)
    {
        if (empty(self::$storeNameBox)) {
            throw new ValidationException('请先初始化$storeNameBox');
        }
        $piece_id = self::$storeNameBox[$store_id]['manage_piece']??0;
        if (empty(self::$pieceNameBox)) {
            $pieceList = SysManagePieceModel::find([
                'columns' => 'id,name',
            ])->toArray();
            self::$pieceNameBox = array_column($pieceList, 'name', 'id');
        }
        return self::$pieceNameBox[$piece_id] ?? '';
    }
    /**
     * 根据网点ID获取网点类型
     * @param $store_id
     * @return mixed|string
     * @throws ValidationException
     */
    public function showStoreCategoryNameByStoreId($store_id)
    {
        if (empty(self::$storeNameBox)) {
            throw new ValidationException('请先初始化$storeNameBox');
        }
        $category = self::$storeNameBox[$store_id]['category']??0;
        if (empty(self::$storeCategoryNameBox)) {
            $storeCategoryList = SysStoreTypeModel::find([
                'columns' => 'id,name',
            ])->toArray();
            self::$storeCategoryNameBox = array_column($storeCategoryList, 'name', 'id');
        }
        return self::$storeCategoryNameBox[$category] ?? '';
    }
}
