<?php

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Enums\PdfEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;

class PdfHelperServer extends BaseServer
{
    /**
     * 根据公司 ID 获取相应公司的页眉页脚
     * @param $paramsIn
     * @param int $response_type
     * @return array
     * @throws BusinessException
     */
    public function getHeaderAndFooter($paramsIn, $response_type = PdfEnums::RESPONSE_TYPE_DIRECT): array
    {
        $result = [
            'header_template'   => '',
            'footer_template'   => '',
            'company_full_name' => '',
            'company_short_name' => '',
            'company_phone' => '',
            'company_web_url' => '',
            'company_logo_url' => '',
        ];
        if (empty($paramsIn)) {
            return $result;
        }

        $fle_rpc = (new ApiClient('hcm_rpc', '', 'get_company_config_info', $this->lang));
        $fle_rpc->setParams($paramsIn);
        $res = $fle_rpc->execute();
        if (isset($res['error'])) {
            throw new BusinessException($res['error']);
        }
        $companyInfo = $res['result']['data'];

        return [
            'header_template'    => $companyInfo['company_logo_url_base64'] ?? '',
            'footer_template'    => $companyInfo['company_address'] ?? '',
            'company_full_name'  => $companyInfo['company_name'] ?? '',
            'company_short_name' => $companyInfo['company_short_name'] ?? '',
            'company_phone'      => $companyInfo['company_phone'] ?? '',
            'company_web_url'    => $companyInfo['company_web_url'] ?? '',
            'company_logo_url'   => $companyInfo['company_logo_url'] ?? '',
        ];
    }

    /**
     * 根据公司 ID 获取相应公司的页眉页脚
     * @param $company_id
     * @param int $response_type
     * @return array
     * @throws BusinessException
     */
    public function getHeaderAndFooterByCompanyId($paramsIn, $response_type = PdfEnums::RESPONSE_TYPE_DIRECT): array
    {
        $result = [
            'header_template'   => '',
            'footer_template'   => '',
            'company_full_name' => '',
            'company_short_name' => '',
            'company_phone' => '',
            'company_web_url' => '',
            'company_logo_url' => '',
        ];
        if (empty($paramsIn)) {
            return $result;
        }

        $fle_rpc = (new ApiClient('hcm_rpc', '', 'get_company_config_info_by_company_id', $this->lang));
        $fle_rpc->setParams($paramsIn);
        $res = $fle_rpc->execute();
        if (isset($res['error'])) {
            throw new BusinessException($res['error']);
        }
        $companyInfo = $res['result'];
        return [
            'header_template'    => $companyInfo['company_logo_url_base64'] ?? '',
            'footer_template'    => $companyInfo['company_address'] ?? '',
            'company_full_name'  => $companyInfo['company_name'] ?? '',
            'company_short_name' => $companyInfo['company_short_name'] ?? '',
            'company_phone'      => $companyInfo['company_phone'] ?? '',
            'company_web_url'    => $companyInfo['company_web_url'] ?? '',
            'company_logo_url'   => $companyInfo['company_logo_url'] ?? '',
        ];
    }

    /**
     * 匹配图片
     * @param $date
     * @param $type
     * @return string
     */
    private function extractImage($date, $type)
    {
        if ($type == PdfEnums::RESPONSE_TYPE_IMAGE) {
            preg_match('/<img[^>]+src="([^"]+)"/', $date, $matches);
            return $matches[1];
        }
        return $date;
    }
}