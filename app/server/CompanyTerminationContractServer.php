<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\CompanyTerminationContractModel;
use FlashExpress\bi\App\Models\backyard\HrOrganizationDepartmentStoreRelationModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReasonModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\StaffRepository;


class CompanyTerminationContractServer extends CancelContractServer
{

    public static $current_state;


    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
        $this->timezone = $timezone;
    }

    /**
     * 是否有解约个人代理的权限
     * @param $staff_info_id
     * @return bool
     */
    public function checkPermission($staff_info_id): bool
    {
        $department_ids = (new SysDepartmentServer())->getDepartmentIdsUseSettingEnv('dept_network_management_id');
        $manageInfo     = (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getDepartmentRelateRegionPieceStore($department_ids);
        return (new StaffServer())->checkStaffManageInfo($staff_info_id, $manageInfo['region_id'], $manageInfo['piece_id'],
            $manageInfo['store_id']);
    }


    /**
     * @param $staff_info_id
     * @param $submitter_id
     * @return array
     * @throws BusinessException
     */
    public function checkLogic($staff_info_id, $submitter_id): array
    {
        //根据工号获取详情
        $staffInfo = (new StaffRepository())->getStaffPosition($staff_info_id);

        if (empty($staffInfo)) {
            throw new BusinessException($this->getTranslation()->_('18974_error_msg_2'));
        }

        // 管线范围权限验证
        $relation = HrOrganizationDepartmentStoreRelationModel::findFirst([
            'conditions' => "store_id = :store_id: and is_deleted = :deleted: and state = :state: and level_state = :level_state:",
            'bind'       => [
                'store_id'    => $staffInfo['sys_store_id'],
                'deleted'     => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
                'state'       => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
                'level_state' => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            ],
        ]);
        if ($relation) {
            if (!(new StaffServer())->checkStaffManageInfo($submitter_id, [$relation->region_id], [$relation->piece_id],
                [$relation->store_id])) {
                throw new BusinessException($this->getTranslation()->_('18974_error_msg_1'));
            }
        }

        //非个人代理的不能提交
        if ($staffInfo['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            throw new BusinessException($this->getTranslation()->_('18974_error_msg_2'));
        }

        //校验在职状态
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_RESIGN) {
            throw new BusinessException($this->getTranslation()->_('18974_error_msg_3'));
        }
        //校验是否存在解约申请
        $num = CompanyTerminationContractModel::count([
            'conditions' => 'staff_info_id = :staff_info_id: AND status IN ({status:array}) ',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'status'        => [enums::$audit_status['panding'], enums::$audit_status['approved']],
            ],
        ]);
        if ($num > 0) {
            throw new BusinessException($this->getTranslation()->_('19036_termination_5202'));
        }
        return $staffInfo;
    }


    /**
     * 添加解约申请
     * @param array $paramIn
     * @return array
     * @throws Exception
     */
    public function add(array $paramIn): array
    {
        //[1]获取传入参数
        $submitter_id = $this->processingDefault($paramIn, 'submitter_id');
        $staffId      = $this->processingDefault($paramIn, 'staff_info_id');
        $reason       = $this->processingDefault($paramIn, 'reason');
        $remark       = $this->processingDefault($paramIn, 'remark');


        //[2]逻辑验证

        $staffInfo = $this->checkLogic($staffId, $submitter_id);


        //获取业务数据
        $biz_data = $this->getBizData($staffId, $staffInfo['sys_store_id']);

        $db = $this->getDI()->get("db");
        //开启事务
        $db->begin();
        try {
            //[3]保存申请数据

            $model                = new CompanyTerminationContractModel();
            $model->submitter_id  = $submitter_id;
            $model->staff_info_id = $staffId;
            $model->store_id      = $staffInfo['sys_store_id'];
            $model->reason        = $reason;
            $model->remark        = $remark;
            $model->biz_data      = json_encode($biz_data, JSON_UNESCAPED_UNICODE);
            $model->status        = enums::$audit_status['panding'];
            $model->serial_no     = 'CTC' . $this->getID();;
            if (!$model->save()) {
                throw new \Exception($this->getTranslation()->_('4008'));
            }

            $approvalServer = new ApprovalServer($this->lang, $this->timezone);
            $requestId      = $approvalServer->create($model->id,
                AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT,
                $submitter_id);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
        } catch (Exception $e) {
            $db->rollBack(); //回滚
            throw $e;
        }
        //提交事务
        $db->commit();

        return $this->checkReturn(['data' => ['id' => $model->id]]);
    }

    /**
     * 获取业务数据
     * @throws BusinessException
     */
    protected function getBizData($staff_info_id, $store_id)
    {
        $ac = new ApiClient('ard_api', '', 'ProxyInvoice.getStaffWorkInfo', $this->lang);
        $ac->setParams(['staff_info_id' => $staff_info_id]);
        $ac_result = $ac->execute();
        if (empty($ac_result['result']['data'])) {
            throw new BusinessException($this->getTranslation()->_('server_error'));
        }
        $result                              = $ac_result['result']['data'];
        $data['2week_delivery_avg_duration'] = $result['duration'];      //派送时长
        $data['2week_handover_avg_amount']   = $result['handover_count'];//交接量
        $data['2week_delivery_avg_amount']   = $result['delivery_count'];//妥投量
        $data['customer_complaints_num']     = $result['complain_count'];//客户投诉数量
        $data['3day_store_termination_num']  = $this->getLast3DayStoreTerminationNum($store_id);
        return $data;
    }

    /**
     * 3天内同一网点解约人数
     * @param $store_id
     * @param int $auditId
     * @return mixed
     */
    public function getLast3DayStoreTerminationNum($store_id, $auditId = 0)
    {
        $addHour    = $this->config->application->add_hour;
        $start_time = date('Y-m-d H:i:s', strtotime('-2 day midnight') - ($addHour * 3600));
        $condition  = 'store_id = :store_id: AND status IN ({status:array}) AND created_at >= :created_at:';
        if ($auditId > 0) {
            $condition .= 'and id != ' . $auditId;
        }
        return CompanyTerminationContractModel::count([
            'conditions' => $condition,
            'bind'       => [
                'store_id'   => $store_id,
                'created_at' => $start_time,
                'status'     => [
                    enums::$audit_status['panding'],
                    enums::$audit_status['approved'],
                    enums::$audit_status['timedout'],
                ],
            ],
        ]);
    }


    /**
     * 整理员工信息
     * @param $staffInfo
     * @return array
     */
    protected function dealStaffInfo($staffInfo): array
    {
        $storeServer                             = new SysStoreServer();
        $storeInfo                               = $storeServer->getRegionAndPieceName($staffInfo['sys_store_id']);
        $returnData['hire_date']                 = substr($staffInfo['hire_date'], 0, 10);
        $returnData['independent_contractor_id'] = $staffInfo['staff_info_id'];
        $returnData['agent_name']                = $staffInfo['name'];
        $returnData['agent_department']          = $staffInfo['depart_name'];
        $returnData['manager_staff_id']          = $staffInfo['manger'];
        $returnData['staff_job_title']           = $staffInfo['job_name'];
        $returnData['manage_region_name']        = $storeInfo['manage_region'];
        $returnData['manage_piece_name']         = $storeInfo['manage_piece'];
        $returnData['agent_store']               = $storeInfo['store_name'];
        return $returnData;
    }


    /**
     * 解约原因
     * @return array[]
     */
    protected function getReasonEnum(): array
    {
        $t = $this->getTranslation();
        return [
            ['value' => '1', 'label' => $t->_('termination_reason_1')],
            ['value' => '2', 'label' => $t->_('termination_reason_2')],
            ['value' => '3', 'label' => $t->_('termination_reason_3')],
            ['value' => '4', 'label' => $t->_('termination_reason_4')],
        ];
    }

    /**
     * 预览信息
     * @param $staff_info_id
     * @param $submitter_id
     * @return array
     * @throws BusinessException
     */
    public function previewInfo($staff_info_id, $submitter_id): array
    {
        $staffInfo                 = $this->checkLogic($staff_info_id, $submitter_id);
        $returnData                = $this->getBizData($staff_info_id, $staffInfo['sys_store_id']);
        $returnData                += $this->dealStaffInfo($staffInfo);
        $returnData['reason_enum'] = $this->getReasonEnum();
        return $returnData;
    }

    /**
     * 赋值审批详情详细信息 主体参数
     * @param $detailLists
     * @param $result
     */
    public function detailMainAssign(&$detailLists,$result)
    {
        $detailLists['independent_contractor_id'] = $result['independent_contractor_id'];
        $detailLists['agent_name']                = $result['agent_name'];
        $detailLists['agent_department']          = $result['agent_department'];
        $detailLists['staff_job_title']           = $result['staff_job_title'];
        $detailLists['manage_region_name']        = $result['manage_region_name'];
        $detailLists['manage_piece_name']         = $result['manage_piece_name'];
        $detailLists['agent_store']               = $result['agent_store'];
        $detailLists['hire_date']                 = $result['hire_date'];

        $detailLists['2week_delivery_avg_duration'] = $result['2week_delivery_avg_duration'];//派送时长
        $detailLists['2week_handover_avg_amount']   = $result['2week_handover_avg_amount'];//交接量
        $detailLists['2week_delivery_avg_amount']   = $result['2week_delivery_avg_amount'];//妥投量
        $detailLists['customer_complaints_num']     = $result['customer_complaints_num'];//客户投诉数量
        $detailLists['3day_store_termination_num']  = $result['3day_store_termination_num'];//3天内解约人数

        $detailLists['termination_reason'] = $result['reason_text'];
        $detailLists['termination_remark'] = $result['remark'];
    }
    /**
     * 获取审批详情详细信息
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     * @throws Exception
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result                                   = $this->getTerminationDetail($auditId);
        $submitter_info                           = (new StaffServer())->getStaffById($result['submitter_id']);
        $detailLists                              = [
            'apply_parson' => sprintf('%s ( %s )', $submitter_info['name'] ?? '',
                $submitter_info['staff_info_id'] ?? ''),
        ];
        $this->detailMainAssign($detailLists,$result);

        //驳回状态，需要显示驳回原因
        if ($result['status'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
        }
        //撤销状态，需要显示撤销原因
        if ($result['status'] == enums::$audit_status['revoked']) {
            $detailLists = array_merge($detailLists, ["cancel_reason" => $result['cancel_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        $data                          = [
            'title'         => $this->getTranslation()->_("workflow_type_63"),
            'id'            => $result['id'],
            'staff_id'      => $result['submitter_id'],
            'type'          => (string)AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT,
            'created_at'    => $result['created_at'],
            'updated_at'    => $result['updated_at'],
            'approvalLevel' => 0,
            'status_text'   => (new AuditlistRepository($this->lang,
                $this->timezone))->getAuditStatus('10' . $result['status']),
            'serial_no'     => $result['serial_no'] ?? '',
        ];
        $returnData['data']['head']    = $data;
        $returnData['data']['extend']  = [];
        $returnData['data']['confirm'] = [];
        return $returnData;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false, false, false, false, false, false);
    }

    public function genSummary(int $auditId, $user)
    {
        $model = CompanyTerminationContractModel::findFirstById($auditId);

        if (!empty($model)) {
            $staffInfoInit = (new StaffRepository())->getStaffPosition($model->staff_info_id);
            $staffInfo     = $this->dealStaffInfo($staffInfoInit);
            $param         = [
                [
                    'key'   => "staff_job_title",
                    'value' => $staffInfo['staff_job_title'],
                ],
                [
                    'key'   => "manage_region_name",
                    'value' => $staffInfo['manage_region_name'],
                ],
                [
                    'key'   => "manage_piece_name",
                    'value' => $staffInfo['manage_piece_name'],
                ],
                [
                    'key'   => "agent_store",
                    'value' => $staffInfo['agent_store'],
                ],
            ];
        }
        return $param ?? [];
    }

    /**
     * 拼组数据
     * @param $list
     * @return array
     */
    public function format($list): array
    {
        $return = [];
        foreach ($list as $key => $v) {
            $item           = [];
            $item['key']    = $this->getTranslation()->_($key) ?? '';
            $item['value']  = is_array($v) && isset($v['value']) ? $v['value'] : $v;
            $item['tips']   = is_array($v) && isset($v['tips']) ? $v['tips'] : null;
            $item['remark'] = is_array($v) && isset($v['remark']) ? $v['remark'] : null;
            $return[]       = $item;
        }
        return $return;
    }

    /**
     * 获取解约离职日期(有重写)
     * @param $model
     * @return false|string
     */
    public function getLeaveDate(CompanyTerminationContractModel $model){
        return  date('Y-m-d', strtotime('+1 day'));
    }
    /**
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return true
     * @throws BusinessException
     * @throws Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            self::$current_state   = $state;
            $model                 = CompanyTerminationContractModel::findFirstById($auditId);
            $leave_date            = $this->getLeaveDate($model);
            $model->status         = $state;
            $model->updated_at     = gmdate('Y-m-d H:i:s');
            $model->last_work_date = date('Y-m-d',strtotime($leave_date . ' last day'));
            $model->leave_date     = $leave_date;

            // 原因
            if ($state == enums::$audit_status['dismissed']) {
                $model->reject_reason = $extend['remark'];
            } elseif ($state == enums::$audit_status['revoked']) {
                $model->cancel_reason = $extend['remark'];
            }

            //提交申请就同步数据到HRIS
            if ($state == enums::$audit_status['approved']) {
                $staffServer = (new StaffServer())->getStaffById($model->staff_info_id,'state');
                //离职不做后续处理
                if ($staffServer && $staffServer['state'] == HrStaffInfoModel::STATE_RESIGN) {
                    //更新为离职过 申请就算
                    $this->getDI()->get('db')->updateAsDict('leave_manager',
                        ['approval_status' => $state],
                        [
                            'conditions' => "staff_info_id = ?",
                            'bind'       => [$model->staff_info_id],
                        ]);
                    return $model->save();
                }
                $params = [
                    'staff_info_id'       => $model->staff_info_id,
                    'wait_leave_date'     => $leave_date,
                    'leave_reason'        => StaffLeaveReasonModel::LEAVE_REASON_97,//解约个人代理
                    'leave_scenario'      => 0,//离职场景
                    'leave_source'        => HrStaffInfoModel::LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT, //解约个人代理
                    'leave_type'          => isCountry('MY') ? 4 : 2,//2辞退（不赔偿） 4  合同终止
                    'leave_reason_remark' => $model->remark,
                    'operaterId'          => $model->submitter_id,
                    'from'                => 'company_termination_contract',
                ];
                //这里会调用OA 资产
                $rpcClient = new ApiClient('hr_rpc', '', 'hr_staff_wait_leave');
                $rpcClient->setParams($params);
                $resultData = $rpcClient->execute();
                if ($resultData['result'] !== true) {
                    $this->getDI()->get("logger")->write_log([
                        'staff_id' => $model->staff_info_id,
                        'result'   => $resultData['result'],
                    ], "alert");
                }
                $this->dealHold($model->staff_info_id, $leave_date,
                    HrStaffInfoModel::LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT, 2);
                //发送消息通知
                $this->sendNoticePush([
                    'staff_info_id'    => $model->staff_info_id,
                    'reason'           => $model->reason,
                    'leave_date'       => $model->leave_date,
                    'last_work_date'   => $model->last_work_date,
                    'submitter_id'     => $model->submitter_id,
                    'termination_type' => $model->termination_type,//only my use
                ]);
                // 发送邮件1
                
            }
            $model->save();
        }
        return true;
    }


    /**
     * @throws BusinessException
     */
    protected function dealHold($staffId, $leaveDate,$leave_source = 5,$src= 1): bool
    {
        $hold_reason = 'incomplete_resignation_procedures';
        $hold_params = [
            'staff_info_id' => $staffId,
            //员工id
            'type'          => '',
            //hold类型(1.工资hold,2.提成hold,同时就传’1,2’)
            'hold_reason'   =>  $hold_reason,
            //hold原因(BY申请离职)
            'hold_remark'   => 1,
            //hold备注
            'hold_time'     => date('Y-m-d H:i:s'),
            //hold时间操作时间
            'hold_source'   => $leave_source,
        ];
        // 如果离职日期在下个月5号之前  同步工资hold
        // 如果离职日期在下个月15号之前 同步提成hold
        $nextMonthType1 = date("Y-m-05", strtotime("+1 month"));
        $nextMonthType2 = date("Y-m-15", strtotime("+1 month"));
        if ($leaveDate <= $nextMonthType1) {
            $hold_params['type'] = "2";
        } elseif ($leaveDate <= $nextMonthType2) {
            $hold_params['type'] = "2";
        }
        $this->getDI()->get('logger')->write_log(['synchronize_hold' => $hold_params], 'info');
        if (!empty($hold_params['type'])) {
            $hcm_rpc = (new ApiClient('hcm_rpc', '', 'synchronize_hold'));
            $hcm_rpc->setParams($hold_params);
            $return = $hcm_rpc->execute();
            if (!isset($return['result']['code']) || $return['result']['code'] != 1) {
                throw new BusinessException($this->getTranslation()->_('server_error'));
            }
        }

        //同步状态
        $res = $this->syncApproval([
            'approval_status' => self::$current_state ?: enums::$audit_status['panding'],
            'staff_info_id'   => $staffId,
            'leave_date'      => $leaveDate,
            'leave_source'    => $leave_source,
            'src'             => $src,
        ]);
        if (!$res) {
            throw new BusinessException($this->getTranslation()->_('server_error'));
        }
        return true;
    }

    //发送消息
    protected function sendNoticePush($paramIn)
    {
        $redis = $this->getDI()->get('redisLib');
        $data  = json_encode($paramIn);
        $this->getDI()->get("logger")->write_log('afterApproved sendNotice push data : ' . $data, "info");
        return $redis->lpush(RedisEnums::LIST_COMPANY_TERMINATION_CONTRACT, $data);
    }

    //发送消息(有重写)
    public function sendNoticePop($info)
    {
        //上级
        $staffInfoServer  = new StaffServer();
        $staffInfo        = $staffInfoServer->getStaffById($info['staff_info_id']);

        //获取员工和上级的语言包
        $staffServer   = new StaffServer();
        $staffLangInfo = $staffServer->getBatchStaffLanguage([$info['staff_info_id'], $info['submitter_id']]);
        $t             = $this->getTranslation($staffLangInfo[$info['staff_info_id']]);
        //给员工发消息
        $title                               = $t->_('company_termination_contract_notice');                                                                                                                                                                                                                                                                                                                                                                                              //终止合同通知
        $content                             = $t->_('company_termination_contract_notice_content', [
            'termination_reason' => $t->_('termination_reason_' . $info['reason']),
            'last_work_day'      => $info['last_work_date'],
            'end_date'           => $info['leave_date'],
        ]);
        $message_param['staff_users']        = [$info['staff_info_id']];
        $message_param['message_title']      = $title;
        $message_param['message_content']    = addslashes("<div style='font-size: 35px'>" . $content . '</div>');
        $message_param['staff_info_ids_str'] = $info['staff_info_id'];
        $message_param['id']                 = time() . $info['staff_info_id'] . rand(1000000, 9999999);;
        $message_param['category'] = -1;
        $bi_rpc                    = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
        $bi_rpc->setParams($message_param);
        $bi_rpc->execute();

        //给上级发消息
        $t             = $this->getTranslation($staffLangInfo[$info['submitter_id']]);
        $title                               = $t->_('company_termination_contract_approved_notice');                                                                                                                                                                                                                                                                                                                                                                                                                   //终止合同通知
        $content                             = $t->_('company_termination_contract_approved_notice_content', [
            'staff_info_id' => $info['staff_info_id'],
            'name'          => $staffInfo['name'],
        ]);
        $message_param['staff_users']        = [$info['submitter_id']];
        $message_param['message_title']      = $title;
        $message_param['message_content']    = addslashes("<div style='font-size: 35px'>" . $content . '</div>');
        $message_param['staff_info_ids_str'] = $info['submitter_id'];
        $message_param['id']                 = time() . $info['submitter_id'] . rand(1000000, 9999999);;
        $message_param['category'] = -1;
        $bi_rpc                    = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
        $bi_rpc->setParams($message_param);
        $bi_rpc->execute();
        return true;
    }


    public function getWorkflowParams($auditId, $user, $state = null): array
    {
        $model = CompanyTerminationContractModel::findFirstById($auditId);
        $num   = $this->getLast3DayStoreTerminationNum($model->store_id, $auditId);
        //3天内同一网点解约个人代理人数是否大于等于3人
        return ['is_termination_3_num' => $num >= 3 ? 1 : 2];
    }

    /**
     * 更新[包含操作：同意|驳回]公司解约申请
     * @param array $paramIn 传入参数
     * @return array
     * @throws Exception
     */
    public function update($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id');
        $auditId = $this->processingDefault($paramIn, 'audit_id');
        $status  = $this->processingDefault($paramIn, 'status');
        $reason  = $this->processingDefault($paramIn, 'reject_reason');
        $reason  = addcslashes(stripslashes($reason), "'");  //单引号过滤

        $model = CompanyTerminationContractModel::findFirstById($auditId);

        //[2]验证数据
        if (empty($model)) {
            throw new BusinessException($this->getTranslation()->_('1015'));
        }
        //已经是最终状态,不能修改
        if ($model->status != enums::$audit_status['panding']) {
            throw new BusinessException($this->getTranslation()->_('1016'));
        }

        $approvalServer = new ApprovalServer($this->lang, $this->timezone);
        if ($status == enums::$audit_status['dismissed']) {
            $approvalServer->reject($auditId, AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT, $reason,
                $staffId);
        } elseif ($status == enums::$audit_status['approved']) {
            $approvalServer->approval($auditId, AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT, $staffId);
        }
        return $this->checkReturn([]);
    }

    /**
     * 取消离职申请
     * @param array $paramIn 传入参数
     * @return array
     * @throws BusinessException|ValidationException
     * @throws Exception
     */
    public function cancel($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id');
        $auditId = $this->processingDefault($paramIn, 'audit_id');
        $status  = $this->processingDefault($paramIn, 'status');
        $reason  = $this->processingDefault($paramIn, 'cancel_reason');
        $reason  = addcslashes(stripslashes($reason), "'");

        //[2]验证数据
        $model = CompanyTerminationContractModel::findFirstById($auditId);
        if (empty($model)) {
            throw new \Exception($this->getTranslation()->_('data_error'));
        }
        //验证是不是撤销操作
        if ($status != enums::$audit_status['revoked']) {
            throw new BusinessException($this->getTranslation()->_('4018'));
        }

        //验证审批状态
        //只有待审批的、审批通过的可以撤销
        if (!in_array($model->status, [
            enums::$audit_status['panding'],
            enums::$audit_status['approved'],
            enums::$audit_status['timedout'],
        ])) {
            throw new BusinessException($this->getTranslation()->_('4018'));
        }

        // 新老数据
        $auditApply = AuditApplyModel::findFirst([
            'conditions' => ' biz_value = :value: and biz_type = :type:',
            'bind'       => [
                'value' => $auditId,
                'type'  => AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT,
            ],
        ]);
        if (empty($auditApply)) {
            throw new \Exception($this->getTranslation()->_('4018'));
        }

        $approvalServer = new ApprovalServer($this->lang, $this->timezone);
        if ($auditApply->submitter_id == $staffId) {
            $approvalServer->cancel($auditId, AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT, $reason,
                $staffId);
        } else {
            $approvalServer->approvalCancel($auditId, AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT,
                $reason, $staffId);
        }

        return $this->checkReturn([]);
    }

    /**
     * 获取解约详情
     * @throws Exception
     */
    public function getTerminationDetail($auditId)
    {
        $model = CompanyTerminationContractModel::findFirstById($auditId);
        if (empty($model)) {
            throw new Exception($this->getTranslation()->_('data_error'));
        }
        $returnData                = $model->toArray();
        $reasonEnum                = array_column($this->getReasonEnum(), 'label', 'value');
        $returnData['reason_text'] = $reasonEnum[$returnData['reason']] ?? '';
        $staffInfoInit             = (new StaffRepository())->getStaffPosition($model->staff_info_id);
        $returnData                += $this->dealStaffInfo($staffInfoInit);
        $returnData                += json_decode($returnData['biz_data'], true);
        return $returnData;
    }

    /**
     * 可视化超时，按照表单去超时
     * @param $audit_id
     * @return string
     */
    public function getAuditFormOvertimeDate($audit_id): string
    {
        $companyTerminationContractInfo = CompanyTerminationContractModel::findFirstById($audit_id);
        if (empty($companyTerminationContractInfo)) {
            return '';
        }
        return $companyTerminationContractInfo->leave_date ? : '';
    }
    
    /**
     * 获取偷窃类型下拉
     * @return array[]
     */
    public function getTheftTypeList(): array
    {
        $t = $this->getTranslation();
        return [
            [
                'value'  => 1,
                'label' => $t->_('theft_type_1'),//COD
            ],
            [
                'value'  => 2,
                'label' => $t->_('theft_type_2'),//包裹
            ],
            [
                'value'  => 3,
                'label' => $t->_('theft_type_3'),//资产
            ],
            [
                'value'  => 4,
                'label' => $t->_('theft_type_4'),//其他
            ],
        ];
    }
    /**
     * 获取骚扰对象下拉
     * @return array[]
     */
    public function getHarassmentTargetList(): array
    {
        $t = $this->getTranslation();
        return [
            [
                'value'  => 1,
                'label' => $t->_('harassment_target_1'),//网点工作人员
            ],
            [
                'value'  => 2,
                'label' => $t->_('harassment_target_2'),//客户
            ],
            [
                'value'  => 3,
                'label' => $t->_('harassment_target_3'),//其他人
            ],
        ];
    }
}
