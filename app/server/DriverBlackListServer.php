<?php

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use Exception;
use FlashExpress\bi\App\Server\SystemExternalApprovalServer;


class DriverBlackListServer extends SystemExternalApprovalServer
{




    /**
     * @description: 审批结束回调函数,设置审批状态等
     * @param int  $auditId 审批ID
     * @param int  $state   审批状态      const APPROVAL_STATUS_PENDING   = 1;    //待审批 const APPROVAL_STATUS_APPROVAL  =
     *                      2;    //审批同意const APPROVAL_STATUS_REJECTED  = 3;    //审批驳回const APPROVAL_STATUS_CANCEL    =
     *                      4;    //审批撤销const APPROVAL_STATUS_TIMEOUT   = 5;    //审批超时
     * @param null $extend  扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/18 15:57
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        try {
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('setProperty  没有找到数据  '.$auditId);
            }
            $server = new SystemExternalApprovalServer($this->lang, $this->timeZone);

            if ($isFinal) {
                //最终审批需要做的操作
                $SystemExternalApprovalModel->state      = $state;
                $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());
                $SystemExternalApprovalModel->save();
            }
            //如果是申请人节点 不进行调用 fms  因为这时候 还没有审批编号
            if(isset($extend['is_start_node']) && $extend['is_start_node']){
                return true;
            }

            if ($state == enums::APPROVAL_STATUS_REJECTED) {
                $rejectReason = $extend['remark'];
            }
            //查询一下 操作人名称
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: ',
                'bind'       => [
                    'staff_info_id' => $extend['staff_id'] ?? '',
                ],
                'columns'    => 'staff_info_id,name'
            ]);

            $forwardParamIn = [
                'param_operation_type'     => $server::param_operation_type_2,
                'param_operation_staff_id' => $extend['staff_id'],
                'serial_no'                => $SystemExternalApprovalModel->serial_no,
                'biz_type'                 =>  (int)  $SystemExternalApprovalModel->biz_type,
                'state'                    =>  (int)  $state,
                'eventual'                 => $isFinal === true ? 1 : 0,
                'reason'                   => $rejectReason ?? '',
                'operator_name'            => $staff_info->name ?? '', //操作人名称
            ];
            AuditCallbackServer::createData($SystemExternalApprovalModel->biz_type, $forwardParamIn);

        } catch (\Exception $e) {
            $this->logger->write_log('DriverBlackListServer  setProperty msg '.$e->getMessage().' file '.$e->getFile().' line '.$e->getLine().$e->getTraceAsString().'auditId => '.$auditId,
                'error');
            return false;
        }

        return true;
    }

}
