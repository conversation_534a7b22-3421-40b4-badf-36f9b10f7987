<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\backyard\HcmAppModel;

class AppDownloadServer extends BaseServer
{

    /**
     * 获取app 列表
     * @return mixed
     */
    public  function getAppDownloadList(){

        $data_list = HcmAppModel::find([
            'conditions' => 'is_show=1',
            'order'      =>'sort asc,updated_at desc',
            'columns'    => ['app_id', 'app_name','pack_name','icon_url','download_url','app_version','app_size']
        ])->toArray();
        $returnArr = ['data'=>$data_list ? $data_list : []];
        return $this->checkReturn($returnArr);
    }


}