<?php

namespace FlashExpress\bi\App\Server;


use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;

class AbnormalExpenseServer extends AuditBaseServer
{
    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * 获取类型
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function getExpenseType(array $paramIn = []): array
    {
        $staffInfoId = $this->processingDefault($paramIn, 'param_operation_staff_id');

        //获取类型
        $server = new SystemExternalApprovalServer($this->lang, $this->timeZone);
        $params = [
            'param_operation_type' => $server::param_operation_type_8,
            'biz_type' => AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE,
            'param_operation_staff_id' => $staffInfoId,
            'externalStaffId' => $paramIn['externalStaffId'],
            'roleIdList' => $paramIn['role'],
        ];
        $result = $server->forwardParamIn($params);

        if(!isset($result['code']) || $result['code'] != 1) {
            throw new \Exception('获取费用类型失败');
        }

        $return = [];
        foreach ($result['data'] as $code => $value) {
            $return[] = [
                'key' => $code,
                'value' => $value,
            ];
        }
        return self::checkReturn(['data' => $return]);
    }

    /**
     * 获取类型
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function getProofInfo(array $paramIn = []): array
    {
        $staffInfoId = $this->processingDefault($paramIn, 'param_operation_staff_id');
        $proofId = $this->processingDefault($paramIn, 'proof_id');

        //获取类型
        $server = new SystemExternalApprovalServer($this->lang, $this->timeZone);

        $params = [
            'param_operation_type' => $server::param_operation_type_9,
            'biz_type' => AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE,
            'param_operation_staff_id' => $staffInfoId,
            'proof_id' => $proofId
        ];
        $result = $server->forwardParamIn($params);
        if(!isset($result['code']) || $result['code'] != 1) {
            throw new ValidationException($result['msg'], ErrCode::VALIDATE_ERROR);
        }

        if (isset($result['data']['abnormalProofDetailList']) && $result['data']['abnormalProofDetailList']) {

            foreach ($result['data']['abnormalProofDetailList'] as $k => $v) {
                $result['data']['abnormalProofDetailList'][$k]['estimateStartTime'] = $this->format_time($v['estimateStartTime']);
                $result['data']['abnormalProofDetailList'][$k]['estimateEndTime'] = $this->format_time($v['estimateEndTime']);
                $result['data']['abnormalProofDetailList'][$k]['actualStartTime'] = $this->format_time($v['actualStartTime']);
                $result['data']['abnormalProofDetailList'][$k]['actualEndTime'] = $this->format_time($v['actualEndTime']);
            }
        }

        return self::checkReturn(['data' => $result['data'] ?? []]);
    }

    /**
     * 组织时间
     * @param $timestamp
     * @return string
     */
    public function format_time($timestamp): string
    {
        if (empty($timestamp)) {
            return "";
        }
        $add_hour = $this->config->application->add_hour;
        return date("Y-m-d H:i:s", $timestamp + 3600 * $add_hour);
    }

    /**
     * @description:审批接口 (通过 驳回 撤销)
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/18 15:56
     */
    public function updateApprove($paramIn = [])
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $auditId = $this->processingDefault($paramIn, 'audit_id', 2);
        $status  = $this->processingDefault($paramIn, 'status', 2);
        $reason  = $this->processingDefault($paramIn, 'reject_reason', 1);
        $operator_id = $this->processingDefault($paramIn, 'operator_id', 1);      // 操作人

        try {

            $info = SystemExternalApprovalModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $auditId
                ]
            ]);
            if (empty($info)) {
                throw new ValidationException('no valid data');
            }
            if ($info->state != enums::APPROVAL_STATUS_PENDING) {
                throw new ValidationException($this->getTranslation()->_('1016'));
            }


            $params = [
                'serial_no' => $info->serial_no,
                'biz_type' => $info->biz_type,
                'state' => $status,
                'reason' => $reason,
                'operator_id' => $operator_id,
                'param_operation_type' => SystemExternalApprovalServer::param_operation_type_11,
                'param_operation_staff_id' => $operator_id
            ];

            $server = new SystemExternalApprovalServer($this->lang, $this->timeZone);
            $result = $server->forwardParamIn($params);
            $this->jsonReturn($result);
        }  catch (ValidationException $e){
            $this->logger->write_log('SystemExternalApprovalServer  updateApprove msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString() .  " paramIn: " . json_encode($paramIn,JSON_UNESCAPED_UNICODE), "error");
            return $this->checkReturn(-3, $e->getMessage());
        } catch (\Exception $e) {

            $this->logger->write_log("SystemExternalApprovalServer  updateApprove  : 事务未提交"
                . " paramIn: " . json_encode($paramIn,JSON_UNESCAPED_UNICODE)
                . " uid: " . $operator_id
                . " message: " . $e->getMessage()
                . " line: " . $e->getLine()
                . " file: " . $e->getFile());
            return $this->checkReturn(["code" => -1, "msg" => $this->getTranslation()->_("4008"),'data'=>[]]);
        }

        return $this->checkReturn(["code" => 1, "msg" => 'sucess','data'=>[]]);

    }

    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId,$user,$comeFrom)
    {
        try {
            //获取详情
            $detailInfo = SystemExternalApprovalModel::findFirst($auditId);
            if (empty($detailInfo)) {
                throw new \Exception($this->getTranslation()->_('message_no_exists'));
            }
            $result = $detailInfo->toArray();

            //获取详情
            $forwardParamIn = [
                'biz_type' => $result['biz_type'],
                'param_operation_type' => SystemExternalApprovalServer::param_operation_type_4,
                'param_operation_staff_id' => $user,
                'serial_no' => $result['serial_no']
            ];

            $server    = new SystemExternalApprovalServer($this->lang, $this->timeZone);
            $svcResult = $server->forwardParamIn($forwardParamIn);
            if(!isset($svcResult['code']) || $svcResult['code'] != 1) {
                throw new \Exception('getDetail  获取详情失败  ' . $auditId);
            }

            //申请人信息
            $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
            if($staff_info['data']){
                $staff_info = $staff_info['data'];
            }

            //组织详情数据
            $detailLists = [
                'apply_parson'       => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? ''),
                'apply_department'   => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
            ];
            $detailList = $this->format($detailLists);

            $auditRepo = new AuditlistRepository($this->lang, $this->timeZone);
            $returnData['data']['detail'] = array_merge($detailList, $svcResult['data']['detail'] ?? []);
	        $add_hour = $this->getDI()['config']['application']['add_hour'];
	        $data = [
                'title'                => $auditRepo->getAudityType($result['biz_type']),
                'id'                   => $result['id'],
                'staff_id'             => $result['submitter_id'],
                'type'                 => $result['biz_type'],
                'created_at'           => date('Y-m-d H:i:s',(strtotime($result['created_at']) + $add_hour * 3600)),
                'updated_at'           => date('Y-m-d H:i:s',(strtotime($result['updated_at']) + $add_hour * 3600)),
                'status'               => $result['state'],
                'serial_no'            => $result['serial_no'] ?? '',
                'biz_type'             => $result['biz_type'],
                'is_update'            => 0,  // 1 可以编辑 0 不能编辑
            ];
            $option = ['beforeApproval' => '0', 'afterApproval' => '0'];
            $state = 0;

            //当前用户是否已经审批
            if ($comeFrom == 2) { //获取审批人的审批状态
                $infos  = AuditApprovalModel::getApprovalDetailInfo($auditId, $result['biz_type'], $user);
                $state  = $infos instanceof AuditApprovalModel ? $infos->getState() : 0;
                $option = $state == enums::APPROVAL_STATUS_PENDING ? ['beforeApproval' => "1,2", 'afterApproval' => '0'] : $option;
                $data['is_update'] = $state == enums::APPROVAL_STATUS_PENDING ? enums::APPROVAL_STATUS_PENDING : $data['is_update'];
            } else { //获取申请的审批状态
                $option = $user == $result['submitter_id'] && $result['state'] == enums::APPROVAL_STATUS_PENDING  ? ['beforeApproval' => '3', 'afterApproval' => '0'] : $option;
            }

            $data['options'] = $this->getStaffOptions(['option' => $option, 'status' => $state]);

            $proofData = [];
            if (isset($svcResult['data']['list']) && $svcResult['data']['list']) {
                $proofDetail = $svcResult['data']['list'];
                $proofData = &$proofDetail[0]['abnormalProofDetailList'];
                if (!empty($proofData)) {
                    foreach ($proofData as $k => $v) {
                        $proofData[$k]['estimateStartTime'] = $this->format_time($v['estimateStartTime']);
                        $proofData[$k]['estimateEndTime'] = $this->format_time($v['estimateEndTime']);
                        $proofData[$k]['actualStartTime'] = $this->format_time($v['actualStartTime']);
                        $proofData[$k]['actualEndTime'] = $this->format_time($v['actualEndTime']);
                    }
                    $svcResult['data']['list'] = $proofDetail;
                }
            }
            $data['proof_info'] = $svcResult['data']['list'] ?? [];
            $returnData['data']['head'] = $data;

            return $returnData;
        }catch (ValidationException $e){
            $this->logger->write_log('getDetail  msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString(), "error");
            return [];
        } catch (\Exception $e) {
            $this->logger->write_log('getDetail msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString(), "error");
            return [];
        }
    }

    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId,$user)
    {
        $info = SystemExternalApprovalModel::findFirst($auditId);
        if (!empty($info)) {
            $summary = json_decode($info->summary, true);
        } else {
            $summary = json_decode('[]', true);
        }
        return $summary;
    }

    /**
     * 回调
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId,int $state,$extend = null,$isFinal = true)
    {
        try {
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('setProperty  没有找到数据  '.$auditId);
            }
            if ($isFinal) {
                //最终审批需要做的操作
                $SystemExternalApprovalModel->state = $state;
                $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());
                $SystemExternalApprovalModel->save();
            }
            //如果是申请人节点 不进行调用 fms  因为这时候 还没有审批编号
            if(isset($extend['is_start_node']) && $extend['is_start_node']){
                return true;
            }

            if ($state == enums::APPROVAL_STATUS_REJECTED) {
                $rejectReason = $extend['remark'];
            }
            //查询一下 操作人名称
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: ',
                'bind'       => [
                    'staff_info_id' => $extend['staff_id'] ?? '',
                ],
                'columns'    => 'staff_info_id,name',
            ]);

            $forwardParamIn = [
                'param_operation_type'     => SystemExternalApprovalServer::param_operation_type_2,
                'param_operation_staff_id' => $extend['staff_id'],
                'serial_no'                => $SystemExternalApprovalModel->serial_no,
                'biz_type'                 => (int)$SystemExternalApprovalModel->biz_type,
                'state'                    => (int)$state,
                'eventual'                 => $isFinal === true ? 1 : 0,
                'reason'                   => $rejectReason ?? "",
                'operator_name'            => $staff_info->name ?? '', //操作人名称
            ];
            AuditCallbackServer::createData($SystemExternalApprovalModel->biz_type, $forwardParamIn);

        } catch (\Exception $e) {
            $this->logger->write_log('SystemExternalApprovalServer  setProperty msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString().'auditId => '.$auditId, "error");
            return false;
        }

        return true;
    }

    /**
     * 异步回调
     * @throws Exception
     */
    public function delayCallBack($params): bool
    {
        $model  = SystemExternalApprovalModel::findFirstBySerialNo($params['serial_no']);
        $result = (new SystemExternalApprovalServer($this->lang, $this->timeZone))->forwardParamIn($params);
        if (!isset($result['code']) || $result['code'] != 1) {
            //这里需要判断 如果是失败了 记录一下  后期跑脚本
            $model->is_call_third_party = SystemExternalApprovalModel::is_call_third_party_2;
            $model->save();
            throw new \Exception('setProperty  回调审批失败!  ' . $params['serial_no']);
        }
        return true;
    }

    /**
     * 获取审批参数
     * @param $auditId
     * @param $user
     * @param $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId,$user,$state = null)
    {
        $auditInfo = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return [];
        }
        $parameters = json_decode($auditInfo->approval_parameters, true);
        return $parameters ?? [];
    }
}