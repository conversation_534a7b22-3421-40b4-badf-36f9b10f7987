<?php

namespace FlashExpress\bi\App\Server;

class AuditOptionRule
{

    //在审批过程中，是否允许申请人撤销
    private $is_applier_allowed_cancel_in_approval_process;
    //在审批完成后，是否允许申请人撤销
    private $is_applier_allowed_cancel_after_approval_complete;
    //在审批过程中，是否允许审批人撤销
    private $is_approver_allowed_cancel_in_approval_process;
    //在审批完成后，是否允许审批人撤销
    private $is_approver_allowed_cancel_after_approval_complete;
    //撤销审批过程中,是否允许申请人撤销
    private $is_applier_allowed_cancel_in_revoke_process ;
    //撤销审批驳回或者超时后，是否允许申请人撤销
    private $is_applier_allowed_cancel_after_revoke_complete;

    /**
     * @param bool $is_applier_allowed_cancel_in_approval_process
     * @param bool $is_allowed_cancel_after_completion_approval
     * @param bool $is_approval_allowed_cancel_in_approval_process
     * @param bool $is_approval_allowed_cancel_after_completion_approval
     * @param bool $is_applier_allowed_cancel_in_revoke_process
     * @param bool $is_applier_allowed_cancel_after_revoke_complete
     */
    public function __construct(
        $is_applier_allowed_cancel_in_approval_process,
        $is_applier_allowed_cancel_after_approval_complete,
        $is_approval_allowed_cancel_in_approval_process,
        $is_approval_allowed_cancel_after_completion_approval,
        $is_applier_allowed_cancel_in_revoke_process,
        $is_applier_allowed_cancel_after_revoke_complete
    ) {
        $this->is_applier_allowed_cancel_in_approval_process = $is_applier_allowed_cancel_in_approval_process;
        $this->is_applier_allowed_cancel_after_approval_complete = $is_applier_allowed_cancel_after_approval_complete;
        $this->is_approver_allowed_cancel_in_approval_process = $is_approval_allowed_cancel_in_approval_process;
        $this->is_approver_allowed_cancel_after_approval_complete = $is_approval_allowed_cancel_after_completion_approval;
        $this->is_applier_allowed_cancel_in_revoke_process = $is_applier_allowed_cancel_in_revoke_process;
        $this->is_applier_allowed_cancel_after_revoke_complete = $is_applier_allowed_cancel_after_revoke_complete;
    }


    /**
     * @return mixed
     */
    public function getIsApplierAllowedCancelInApprovalProcess()
    {
        return $this->is_applier_allowed_cancel_in_approval_process;
    }

    /**
     * @param mixed $is_applier_allowed_cancel_in_approval_process
     */
    public function setIsApplierAllowedCancelInApprovalProcess($is_applier_allowed_cancel_in_approval_process): void
    {
        $this->is_applier_allowed_cancel_in_approval_process = $is_applier_allowed_cancel_in_approval_process;
    }

    /**
     * @return mixed
     */
    public function getIsApplierAllowedCancelAfterApprovalComplete()
    {
        return $this->is_applier_allowed_cancel_after_approval_complete;
    }

    /**
     * @param mixed $is_applier_allowed_cancel_after_approval_complete
     */
    public function setIsApplierAllowedCancelAfterApprovalComplete($is_applier_allowed_cancel_after_approval_complete): void
    {
        $this->is_applier_allowed_cancel_after_approval_complete = $is_applier_allowed_cancel_after_approval_complete;
    }

    /**
     * @return mixed
     */
    public function getIsApproverAllowedCancelInApprovalProcess()
    {
        return $this->is_approver_allowed_cancel_in_approval_process;
    }

    /**
     * @param mixed $is_approver_allowed_cancel_in_approval_process
     */
    public function setIsApproverAllowedCancelInApprovalProcess($is_approver_allowed_cancel_in_approval_process): void
    {
        $this->is_approver_allowed_cancel_in_approval_process = $is_approver_allowed_cancel_in_approval_process;
    }

    /**
     * @return mixed
     */
    public function getIsApproverAllowedCancelAfterApprovalComplete()
    {
        return $this->is_approver_allowed_cancel_after_approval_complete;
    }

    /**
     * @param mixed $is_approver_allowed_cancel_after_approval_complete
     */
    public function setIsApproverAllowedCancelAfterApprovalComplete($is_approver_allowed_cancel_after_approval_complete): void
    {
        $this->is_approver_allowed_cancel_after_approval_complete = $is_approver_allowed_cancel_after_approval_complete;
    }

    /**
     * @return false|mixed
     */
    public function getIsApplierAllowedCancelInRevokeProcess()
    {
        return $this->is_applier_allowed_cancel_in_revoke_process;
    }

    /**
     * @param false|mixed $is_applier_allowed_cancel_in_revoke_process
     */
    public function setIsApplierAllowedCancelInRevokeProcess($is_applier_allowed_cancel_in_revoke_process): void
    {
        $this->is_applier_allowed_cancel_in_revoke_process = $is_applier_allowed_cancel_in_revoke_process;
    }

    /**
     * @return mixed
     */
    public function getIsApplierAllowedCancelAfterRevokeComplete()
    {
        return $this->is_applier_allowed_cancel_after_revoke_complete;
    }

    /**
     * @param mixed $is_applier_allowed_cancel_after_revoke_complete
     */
    public function setIsApplierAllowedCancelAfterRevokeComplete($is_applier_allowed_cancel_after_revoke_complete): void
    {
        $this->is_applier_allowed_cancel_after_revoke_complete = $is_applier_allowed_cancel_after_revoke_complete;
    }

}