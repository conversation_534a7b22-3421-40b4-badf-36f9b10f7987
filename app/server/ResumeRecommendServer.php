<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrResumeExtentModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrResumeRecommendModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Models\backyard\SysDistrictModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\coupon\MessageContentModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Modules\Ph\library\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\ResumeRecommendRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use Phalcon\Db;
use Phalcon\Db\Column;
use function GuzzleHttp2\Psr7\str;
use Exception;


class ResumeRecommendServer extends BaseServer
{
    public $timezone;
    public $country_server;
    public static $paramIn;
    public $lang;
    public $recommendRes;

    const STORE_TAB_ONE = 1;//我的网点
    const STORE_TAB_TWO = 2;//其它网点

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->lang = $lang;
        $this->timezone = $timezone;
        $this->recommendRes = (new ResumeRecommendRepository());
    }

    public function getStoreHc($params,$userInfo){
        //store_id : 我的网点
        $params['store_id'] = $userInfo['organization_type'] == 1 ? $userInfo['organization_id'] : '';
        if (empty($params['store_tab']) || $params['store_tab'] == 1) {
            //当前登录主管所在hc job
            $data               = (new ResumeRecommendRepository($this->lang))->getHcByStore($params, $userInfo);
        } else {
            //其他所有网点hc
            $data = (new ResumeRecommendRepository($this->lang))->getHcListV1($params);
        }
        return $data;

    }



    /**
     * 获取简历详情
     * @param $paramIn
     * @param $userInfo
     */
    public function getInfo( $paramIn,$userInfo ){

        $paramIn['userInfo'] = $userInfo;
        $info = (new ResumeRecommendRepository())->getInfo($paramIn);
        return $info;

    }

    /**
     * 简历删除
     * @param $paramIn
     * @return array
     */
    public function delete($paramIn){
        $recommendId = $paramIn['recommend_id'];
        $recommend = HrResumeRecommendModel::findFirst(
            [
                'conditions'=> 'id = :id:',
                'bind'=>['id'=>$recommendId]
            ]
        );
        if( empty( $recommend ) ){
            return $this->checkReturn(-3, 'this is resume not exists');
        }
        $recommend->is_delete = HrResumeRecommendModel::IS_DELETE_YES;
        if(!$recommend->save()){
            return $this->checkReturn(-3, 'operation failed please try again');
        }
        return $this->checkReturn(1, 'operation success');
    }

    /**
     * 获取推荐简历下拉初始化
     * return array
     */
    public function getResumeRecommendInit($paramsIn){
        $returnArr = [
            'code'=>1,
            'data'=>'',
            'msg'=>''
        ];
        $settingServer = new SettingEnvServer();
        //获取推荐简历 期待职位  hcm配置快递员职位id
        if (isCountry(['TH','MY'])){
            $recommendKdJd = $settingServer->getSetVal('h5_th_head_office_no_like_job');
        }else{
            $recommendKdJd = $settingServer->getSetVal('by_recommender_resume_jd_kd');
        }

        //获取推荐简历期待职位  hcm配置其他职位id
        $recommendOtherJd = $settingServer->getSetVal('by_recommender_resume_jd_other');

        $recommendKdJd = !empty( $recommendKdJd ) ? explode(',',$recommendKdJd) : [] ;
        $recommendOtherJd = !empty( $recommendOtherJd ) ? explode(',',$recommendOtherJd) : [] ;
        $jdIds = array_merge($recommendOtherJd,$recommendKdJd);

        //查询hr_hc表 组装下拉选项数据
        $builderHrHc = $this->modelsManager->createBuilder();
        $builderHrHc->columns([
            'jd.job_name',
            'jd.job_id',
        ])->from(['jd' => HrJdModel::class]);
        $builderHrHc->inWhere('jd.job_id',$jdIds);
        $hrJdRst = $builderHrHc->getQuery()->execute()->toArray();

        foreach ( $hrJdRst as $k=>$val ){

            if( in_array( $val['job_id'],$recommendKdJd ) ){
                $hrJdRst[$k]['job_type'] = ResumeRecommendRepository::EXPROESS_JOB_YES;
            }
            if( in_array( $val['job_id'],$recommendOtherJd ) ){
                $hrJdRst[$k]['job_type'] = ResumeRecommendRepository::EXPROESS_JOB_OTHER;
            }

        }
        $data['expect_job'] = $hrJdRst;
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => [ 'staff_info_id' => $paramsIn['userinfo']['staff_id'] ],
            'columns' => 'sys_store_id'
        ]);
        //初始化 网点数据
        $data['estimate_store'] = [
            'organization_id'=>'',
            'organization_name'=> ''
        ];
        if( !empty( $staffInfo ) ){
            $staffInfo = $staffInfo->toArray();
            if( $staffInfo['sys_store_id'] == -1 ){

                $data['estimate_store'] ['organization_id'] = -1;

            }else{

                $storeInfo = (new SysStoreServer())->getStoreByid($staffInfo['sys_store_id']);
                if( !empty( $storeInfo ) ){
                    $data['estimate_store'] ['organization_id'] = $storeInfo['id'];
                }

            }
        }

        $recommend = new ResumeRecommendRepository();
        //简历驳回数量
        $data['resumeRecommendRejectNum'] = $recommend->getResumeRejectNum($paramsIn['userinfo']);

        //简历待提交数量
        $data['resumeRecommendSubmittedNum'] = $recommend->getResumeSumittedNum($paramsIn['userinfo']);

        $returnArr['data'] = $data;
        return $this->checkReturn($returnArr);
    }

    /**
     * 获取推荐简历列表
     * @param $paramIn
     * @param $userInfo
     * @return array
     */
    public function getResumeRecommendList($paramIn,$userInfo){

        $paramIn['userinfo'] = $userInfo;
        $list = (new ResumeRecommendRepository($this->lang))->getResumeRecommendList($paramIn);
        return $list;
    }


    /**
     * 简历提交
     * @param $paramIn
     * @param $userinfo
     * @return array|jsonData|void
     */
    public function resumeRecommendSubmit($paramIn,$userinfo){

        if (isCountry(['TH','MY'])){
            $paramIn['reserve_type'] = HrResumeModel::RESERVE_TYPE_FORMAL;
        }
        $country_code = strtoupper(env('country_code'));
        if ($this->isRecommendCountry()){
            // 菲律宾校验逻辑
            $validations = [
                "first_name"         => "Required|StrLenGeLe:0,50|>>>:" . $this->t->_('4038'),
                "last_name"          => "Required|StrLenGeLe:0,50|>>>:" . $this->t->_('4037'),
                "estimate_store_id"  => "Required|StrLenGeLe:0,50|>>>:" . $this->t->_('4028'),
                "hc_id"              => "Required|StrLenGeLe:0,50|>>>:" . $this->t->_('4028'),
                "phone"              => "Required|StrLenGeLe:10,10|>>>:". $this->t->_('4117'),  // 手机号
                "work_province_code" => "StrLenGeLe:0,100|>>>:" . $this->t->_('resume.work_province_code_input_error'),
                "work_city_code"     => "StrLenGeLe:0,100|>>>:" . $this->t->_('resume.work_city_code_input_error'),
            ];

//            if (isCountry(['TH','MY'])) {
//                $validations['reserve_type']  = 'Required|IntIn:' . HrResumeModel::RESERVE_TYPE_FORMAL . ','.HrResumeModel::RESERVE_TYPE_AGENT. '|>>>:vehicle_type_category' . $this->getTranslation()->_('reserve_type_error');
//            }

            if (isCountry('MY')) {
                unset($validations['last_name']);
                //页面没有表单 招聘渠道需要给值 Referral https://flashexpress.feishu.cn/wiki/WTvVw0GLSiab7skVtltchRUDnac
                $paramIn['recruit_channel'] = HrResumeModel::RECRUIT_REFERRAL;
            }
        }else{
            // 其他国家校验逻辑
            if(isset($paramIn['credentials_num']) && $paramIn['credentials_num'] && !preg_match('/^[A-Za-z0-9\-]{1,15}$/',$paramIn['credentials_num'])){
                return $this->checkReturn(-3,$this->getTranslation()->_('4131'));
            }
            $validations = [
//                "social_security_num" => "StrLenGeLe:0,10|>>>:" . $this->getTranslation()->_('4029'),
//                "medical_insurance_num" => "StrLenGeLe:0,12|>>>:" . $this->getTranslation()->_('4030'),
//                "tax_no" => "StrLenGeLe:0,9|>>>:" . $this->getTranslation()->_('4036'),
                "first_name" => "Required|StrLenGeLe:0,50|>>>:" . $this->getTranslation()->_('4038'),
                "last_name" => "Required|StrLenGeLe:0,50|>>>:" . $this->getTranslation()->_('4037'),
                "estimate_store_id" => "Required|StrLenGeLe:1,50|>>>:" . $this->getTranslation()->_('4028'),
                "phone" => "Required|StrLenGeLe:10,10|>>>:" . $this->getTranslation()->_('4117'),  // 手机号
                //                "is_owner_or_cr" => "Required|StrLenGeLe:1,1|>>>:" . "is_owner_or_cr err",  //车辆是否本人所有
            ];
        }

        $this->validateCheck($paramIn, $validations, -3);

        $recommendKdJd = (new SettingEnvServer())->getSetVal('by_recommender_resume_jd_kd');

        $recommendKdJd = !empty( $recommendKdJd ) ? explode(',',$recommendKdJd) : [] ;
        if( empty( $paramIn['expect_job'] ) ){
            return self::checkReturn(-3, 'expect_job not empty');
        }

        //如果是快递职位 同时展示以下字段
        if (in_array($paramIn['expect_job'], $recommendKdJd) && !$this->isRecommendCountry()) {
            if (empty($paramIn['driver_license_front']) || empty($paramIn['driver_license_side'])) {
                return self::checkReturn(-3, 'driver_license not empty');
            }
        }

        //[1]发送RPC请求
        $paramIn['user_info'] = $userinfo;
        $rpcClient = new ApiClient('winhr_rpc', '', 'resume_recommend_submit', $this->lang);
        $rpcClient->setParams($paramIn);

        $return = $rpcClient->execute();
        $this->getDI()->get('logger')->write_log("winhr_rpc resumeRecommendSubmit:" . json_encode($return, JSON_UNESCAPED_UNICODE), 'info');
        if( !empty( $return['result'] ) && $return['result']['code'] ){
            return self::checkReturn($return['result']);
        }
        return self::checkReturn(-3,$return['result']['msg']);
    }


    /**
     * 保存内推简历接口
     * @param $paramIn
     * @param $userinfo
     * @return jsonData|void
     */
    public function createResumeScratch($paramIn,$userinfo){

        $validations = [];

        //候选人名
        if (!empty($paramIn['first_name'])) {
            $validations = array_merge($validations, [
                "first_name" => "StrLenGeLe:0, 50|>>>:" . $this->getTranslation()->_('4038')
            ]);
        }
        if (!isCountry("MY")){
            //候选人姓
            if (!empty($paramIn['last_name'])) {
                $validations = array_merge($validations, [
                    "last_name" => "StrLenGeLe:0, 50|>>>:" . $this->getTranslation()->_('4037')
                ]);
            }
        }
        //预计工作网点
        if (!empty($paramIn['estimate_store_id'])) {
            $validations = array_merge($validations, [
                "estimate_store_id" => "StrLenGeLe:1,50|>>>:" . $this->getTranslation()->_('4028')
            ]);
        }

        if (!empty($paramIn['phone'])) {
            $validations = array_merge($validations, [
                "phone" => "Required|StrLenGeLe:10,10|>>>:" . $this->getTranslation()->_('4117'),  // 手机号
            ]);
        }

        if (isCountry(['TH','MY']) && !empty($paramIn['reserve_type'])) {
            $validations = array_merge($validations, [
                "reserve_type" => 'Required|IntIn:' . HrResumeModel::RESERVE_TYPE_FORMAL . ','.HrResumeModel::RESERVE_TYPE_AGENT.'|>>>:vehicle_type_category' . $this->getTranslation()->_('reserve_type_error'),  // 手机号
            ]);
        }

        //新增工作网点验证
        if ($this->isRecommendCountry()) {
            if (!empty($paramIn['work_province_code'])) {
                $validations['work_province_code'] = "StrLenGeLe:0,10|>>>:" . $this->t->_('resume.work_province_code_input_error');
            }

            if (!empty($paramIn['work_city_code'])) {
                $validations['work_city_code'] = "StrLenGeLe:0,10|>>>:" . $this->t->_('resume.work_city_code_input_error');
            }
        }

        $this->validateCheck($paramIn, $validations,-3);
        if (isCountry("PH")) {
            if (!empty($paramIn['phone'])) {
                $paramIn['phone'] = str_pad($paramIn['phone'], 11, "0", STR_PAD_LEFT);;
            }
        }
        //[1]发送RPC请求
        $paramIn['user_info'] = $userinfo;

        $rpcClient = new ApiClient('winhr_rpc', '', 'resume_scratch', $this->lang);
        $rpcClient->setParams($paramIn);

        $return = $rpcClient->execute();
        if( !empty( $return['result'] ) && $return['result']['code'] ){
            return self::checkReturn($return['result']);
        }
        return self::checkReturn(-3,$return['result']['msg']);

    }

    /**
     * 获取承诺书电子签待签署数量
     * @param $userinfo
     * @return int
     */
    public function getCommitmentNum( $userinfo ){
        $staffInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($userinfo['staff_id'], 'hire_type');
        if (isCountry('PH') && $staffInfo->hire_type == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return 0;
        }
        $num = 0;
        //查询签署状态
        $info = (new ResumeRecommendRepository())->getResumeExtend( $userinfo['staff_id'] );
        if( !empty( $info['commitment_sign_state'] ) && $info['commitment_sign_state'] == enums::COMMITMENT_SIGN_STATE_NO ){

            $num = 1;

        }
        return $num;
    }

    /**
     * 获取承诺书详情
     * @param $userinfo
     * @return array
     */
    public function getCommitmentInfo( $userinfo ){

        $recommendRepository = new ResumeRecommendRepository();
        $info = (new ResumeRecommendRepository())->getResumeExtend( $userinfo['staff_id'] );

        $data = [];
        $config        = $this->getDI()->getConfig();
        $img_prefix = $config->application['img_prefix'];
        if( !empty( $info ) && !empty( $info['commitment_sign_state'] ) ){

            $vehicleinfoIsempty = VehicleInfoModel::VEHICLE_INFO_NO;
            //getStaffVehicleModel
            $staffVehicle = $recommendRepository->getStaffVehicleModel( $userinfo['staff_id'] );

            $staffInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($userinfo['staff_id'],'job_title');

            if( !empty( $staffInfo->job_title ) && (enums::$job_title['van_courier'] != $staffInfo->job_title ) ){

                $vehicleinfoIsempty = VehicleInfoModel::VEHICLE_INFO_YES;

            }

            if( !empty( $staffVehicle ) && !empty( $staffVehicle['vehicle_model'] ) ){

                $vehicleinfoIsempty = VehicleInfoModel::VEHICLE_INFO_YES;

            }

            $data = [
                'commitment_sign_state' => $info['commitment_sign_state'],
                'commitment_sign_state_text' => $this->getTranslation()->_('commitment_sign_state_'.$info['commitment_sign_state']),
                'commitment_url' => $img_prefix.$info['commitment_url'],
                'resume_id'     => $info['resume_id'],
                'commitment_name' => $this->getTranslation()->_('commitment'),
                'vehicle_info_err' => $vehicleinfoIsempty,
                'staff_id' => $userinfo['staff_id'],
                'staff_name' => $userinfo['name']
            ];

        }
        return $data;

    }

    /**
     * 获取推荐简历 承诺书电子签 是否展示菜单入口
     * @param $userinfo
     * @return array
     */
    public function getCommitment($userinfo){

        $data = [
            'is_show' => 0,
            'is_show_commitment' => 0,
            'number' => 0,
        ];

        //判断是否是快递员职位
//        $job_title = $userinfo['job_title'];
        //获取推荐简历 期待职位  hcm配置快递员职位id
        //        $recommendKdJd = SettingEnvServer::getSetVal('by_recommender_resume_jd_kd');
        //        $recommendKdJd = !empty( $recommendKdJd ) ? explode(',',$recommendKdJd) : [] ;
        $staffInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($userinfo['staff_id'],'job_title');
        if ( !empty( $staffInfo->job_title ) && in_array( $staffInfo->job_title, [enums::$job_title['van_courier'], enums::$job_title['bike_courier'], enums::$job_title['tricycle_courier'] ] ) ) {
            $data['is_show_commitment'] = 1;
        }

        //查询签署状态
        $info = (new ResumeRecommendRepository())->getResumeExtend( $userinfo['staff_id'] );
        if($data['is_show_commitment'] == 1 && !empty( $info['commitment_sign_state'] ) && $info['commitment_sign_state'] == enums::COMMITMENT_SIGN_STATE_NO ){
            $data['is_show'] = 1;
            $data['number'] = 1;
        }

        return $data;
    }

    //测试demo
    public function demoPdf(){
        $form_data = [
            "employeeName"=>"段向明",
            "date"=>"2020-12-11",
            "job"=>"开发",
            "name"=>"ักหน้าหล",
            "proofType"=>"idcard",
            "jobNumber"=>"2324",
            "currentDate"=>"2020-12-11 12:11:22",
        ];
        $html_oss_url = 'https://shopwinner-dev.oss-ap-southeast-1.aliyuncs.com/10010020-1473564798540247121.ftl';
        $pdffile_data = (new formPdfServer())->generatePdf($html_oss_url,$form_data);
        return $pdffile_data;
    }

    private function getResumeIdByStaff($staff_id){
        $resume_extend = $this->recommendRes->getResumeExtend( $staff_id);
        $resume_id = $resume_extend['resume_id'] ?? '';
        return $resume_id;
    }

    /**
     *
     * @param $userinfo
     */
    public function saveCommitmentSign($userinfo,$staff_sign_img){
        //获取简历ID
        $resume_id = $this->getResumeIdByStaff($userinfo['staff_id']);
        if(empty($resume_id)){
            throw new BusinessException('简历信息获取失败,cvid:'.$resume_id);
        }

        $dbcon = $this->getDI()->get('db');
        $dbcon->begin();
        try{

            //重新生成带有签名的pdf
            $pdf_file_data = $this->getCommitmentPdf($userinfo,$staff_sign_img);
            $this->logger->write_log("saveCommitmentSign-pdf返回数据(cvid：{$resume_id})".json_encode($pdf_file_data), 'info');
            $pdf_file = $pdf_file_data ? $pdf_file_data['object_key'] : '';
            if(!isset($pdf_file_data['bucket_name']) || !isset($pdf_file_data['content_type'])){
                $dbcon->rollback();
                return;
            }

            //todo 1 更新简历拓展表中的签名状态和签名路径
            $commitment_update = [
                'commitment_sign_state' => enums::COMMITMENT_SIGN_STATE_YES,
                'commitment_url' => $pdf_file,
            ];
            $dbcon->updateAsDict('hr_resume_extend',$commitment_update , 'resume_id = ' . $resume_id);

            //todo 2 同步插入一条数据到附件表
            $commitment_annex  = HrAnnexModel::findFirst(
                [
                    "conditions" => " oss_bucket_key = :oss_bucket_key: and file_type = 22 and type = 1 and deleted=0 ",
                    'bind' => [
                        'oss_bucket_key' => $resume_id,

                    ],
                ]
            );
            if(!empty($commitment_annex)){
                $dbcon->updateAsDict('hr_annex',['deleted'=>1] , 'id = ' . $commitment_annex->id);
            }
            $annex_insert = [
                'oss_bucket_key'=>$resume_id,
                'object_key'=>$pdf_file,
                'oss_bucket_type'=>'COMMITMENT',
                'original_name'=>'',
                'bucket_name'=>$pdf_file_data['bucket_name'],
                'file_content_type'=>$pdf_file_data['content_type'],
                'file_type'=>22,// 22：承诺书,关联winhr项目中枚举文件中的$resume_file_type
                'type'=>1,//1：简历附件
            ];
            $dbcon->insertAsDict('hr_annex',$annex_insert);

            //todo 3 写入winhr操作日志
            $dbcon->insertAsDict("hr_log", [
                "staff_info_id" => $userinfo['staff_id'],
                "module_id" => $resume_id,
                "module_type" => 17,//承诺书附件 关联winhr项目中枚举 $module_type
                "action" => 23, //签署动作       关联winhr项目中枚举 $log_operation
            ]);

            $dbcon->commit();
        }catch (Exception $e){
            $dbcon->rollback();
            throw new Exception("saveCommitmentSign异常(cvid-$resume_id)：".$e->getMessage());
        }

        return true;

    }


    /**
     * 获取承诺书pdf
     * @param $userinfo
     * @return mixed
     */
    public function getCommitmentPdf($userinfo,$staff_sign_img = ''){

        $resume_extend = (new ResumeRecommendRepository())->getResumeExtend( $userinfo['staff_id'] );

        if(empty($resume_extend)){
            throw new BusinessException('简历信息获取失败,用户ID:'.$userinfo['staff_id']);
        }
        $resume_id = $resume_extend['resume_id'];

        //如果未签署状态下文件为空，可以重复生成
        if(empty(trim($resume_extend['commitment_url']))){
            //如果首次未生成，则生成pdf
            //todo 生成承诺书pdf
            //获取pdf页面数据
            $form_data = $this->getCommitmentFormData($resume_id,$userinfo);
            $img_data = [];
            if($staff_sign_img){
                $img_data = [['name'=>'staff_sign_img','url'=>$staff_sign_img]];
            }

            //todo 上传html模板
            $html_path = BASE_PATH.'/public/pdf_template/commitment.ftl';
            //上传oss 更新表
            $result = $this->uploadFileOss($html_path , self::OSS_DIR_MAPS[self::HRIS_STAFF_PROFILE]);
            $html_oss_url = $result['object_url'];//oss地址
            //生成pdf
            $pdf_file_data = (new formPdfServer())->generatePdf($html_oss_url,$form_data,$img_data);

            if(empty($pdf_file_data)){
                throw new Exception('getCommitmentPdf-generatePdf生成pdf失败');
            }
            $pdf_file_data['is_first_sign']= 1;
        }else{
            $pdf_file_data = ['object_key'=>$resume_extend['commitment_url'],'is_first_sign'=>0];

        }

        return $pdf_file_data;
    }



    /**
     * 获取授权书pdf页面所需数据
     */
    private function getCommitmentFormData($resume_id,$userinfo){
        $staff_id = $userinfo['staff_id'];
        $staff_job_title_id = $userinfo['job_title'];
        $resume_info = $this->recommendRes->getResumeInfo($resume_id);

        //获取车辆品牌型号
        $vehicle_info = $this->recommendRes->getStaffVehicleModel($staff_id);
        if(empty($vehicle_info)) {
            $this->getDI()->get('logger')->write_log("getCommitmentFormData-用户（{$staff_id}）车辆信息为空:", 'info');
        }else{
            $vehicle_setting = VehicleInfoEnums::CONFIG_VEHICLE_INFO;
            if($vehicle_info['vehicle_brand']){
                $vehicle_brand_conf = array_column($vehicle_setting['vehicle_brand'], null, 'value');
                $vehicle_model_conf = array_column($vehicle_brand_conf[$vehicle_info['vehicle_brand']]['data'], null, 'value');
            }

            //van快递员才会有车辆型号
            if ($vehicle_info['vehicle_type'] && $vehicle_info['vehicle_type'] == VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE) {

                //品牌
                if($vehicle_info['vehicle_brand'] == 100){ //如果品牌选择的其他
                    $vehicle_brand_text = $vehicle_info['vehicle_brand_text'];
                }else{
                    $vehicle_brand_text = $vehicle_brand_conf[$vehicle_info['vehicle_brand']]['label'] ?? '';
                }

                //车型
                if($vehicle_info['vehicle_model'] == 100){//如果车型选择的其他
                    $vehicle_model_text = $vehicle_info['vehicle_model_text'];
                }else{
                    $vehicle_model_text = $vehicle_model_conf[$vehicle_info['vehicle_model']]['label'] ?? '';
                }

                $car_brand_model = $vehicle_brand_text.' '.$vehicle_model_text;

            }

        }

        //获取简历中车牌号
        $hrEconomyAbility = $this->recommendRes->getHrEconomyAbility($resume_id);
        $car_number = $hrEconomyAbility['car_number'] ?? '';
        //政府ID
        $government_id =  '';
        if($resume_info['is_have_social_security'] == 1 && $resume_info['social_security_num']){ //社保好
            $government_id = $resume_info['social_security_num'];
        }elseif ($resume_info['credentials_num']){ //护照号
            $government_id = $resume_info['credentials_num'];
        }elseif ($hrEconomyAbility['driver_number']){
            $government_id = $hrEconomyAbility['driver_number'];
        }
        //获取职位名称
        $jobInfo = HrJobTitleModel::findFirst($staff_job_title_id);
        if($jobInfo){
            $job_title_name = $jobInfo->job_name;
        }

        //入职表中办理日期
        $entry_info = $this->recommendRes->getEntryInfo($staff_id);
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        if($entry_info){
            //获取员工表中入职日期
            $staff_info = HrStaffInfoModel::findFirst([
                'columns'=>'hire_date',
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => ['staff_info_id' => $staff_id]
            ]);
            $staff_info = $staff_info ? $staff_info->toArray() : [];
            if($staff_info){
                //入职日期
                $hire_date_timestamp = strtotime($staff_info['hire_date']) + $add_hour * 3600;
                $hire_date = date('d.m.Y',$hire_date_timestamp);
                $hire_date_day = date('d',$hire_date_timestamp);
                $hire_date_month = date('m',$hire_date_timestamp);
                $hire_date_year = date('Y',$hire_date_timestamp);
            }

            //获取hc工作网点
            $hc_info = (new HcRepository($this->timezone))->checkHc(['hc_id'=>$entry_info['hc_id']]);
            $worknode_id = $hc_info['worknode_id'];
            $storeInfo = (new SysStoreServer())->getStoreName([$worknode_id]);
            $store_name = $storeInfo[$worknode_id] ?? 'Head Office';

        }
        //获取居住地地址
        $staff_address = $this->getResidenceAddress($resume_info);



        //返回的授权书所需字段数据
        $form_data = [
            'staff_name'=>$resume_info['first_name'].' '.$resume_info['last_name'],
            'staff_address'=>$staff_address,
            'car_model'=>$car_brand_model ?? '', //车辆品牌+车辆型号
            'car_number'=>$car_number ?? '', //车牌号
            'job_title_name'=>$job_title_name ?? '',//职位名称
            'entry_operate_date'=>$hire_date ?? '',//办理日期
            'entry_operate_date_day'=>$hire_date_day ?? '',//办理日期-日
            'entry_operate_date_month'=>$hire_date_month ?? '', //办理日期-月
            'entry_operate_date_year'=>$hire_date_year ?? '', //办理日期-年
            'worknode_name'=>$store_name ?? '',//工作网点
            'government_id'=> $government_id, //政府ID

        ];

        return $form_data;
    }

    public function getResidenceAddress($resume_info)
    {
        $country_code = strtoupper(env('country_code'));
        switch ($country_code){
            case 'TH': //泰国
                $country_value = "1";
                break;
            case 'PH': //菲律宾
                $country_value = "4";
                break;
            case 'LA': //老挝
                $country_value = "6";
                break;
            case 'MY': //马来
                $country_value = "3";
                break;
            case 'VN': //越南
                $country_value = "5";
                break;
            case 'ID': //印尼
                $country_value = "7";
                break;
            default: //默认泰国
                $country_value = "";
                break;

        }

        $register_country = $resume_info['residence_country'];
        $province = $resume_info['residence_government'] ?? ''; //居住地所在省
        $city     = $resume_info['residence_city'] ?? '';//居住地所在市
        $district = $resume_info['residence_town'] ?? '';//居住地所在乡

        //户籍地 所属国家是泰国的则获取对应的行政区域名称，非泰国的直接显示行政区域
        if($register_country == $country_value){

            //获取省


            $province_obj = SysProvinceModel::findFirst([
                'columns'    => 'code,name,en_name',
                'conditions' => 'code = :code:',
                'bind'       => ['code' => $province,],
            ]);

            $province_data = $province_obj ? $province_obj->toArray():[];



            $city_obj     = SysCityModel::findFirst([
                'columns'    => 'code,name,en_name',
                'conditions' => 'code = :code:',
                'bind'       => ['code' => $city,],
            ]);

            $city_data     = $city_obj ? $city_obj->toArray(): [];

            $district_obj = SysDistrictModel::findFirst([
                'columns'    => 'code,name,en_name',
                'conditions' => 'code = :code:',
                'bind'       => ['code' => $district,],
            ]);
            $district_data = $district_obj ? $district_obj->toArray(): [];



            $district_name = $district_data['name'] ?? ''; //乡
            $city_name     = $city_data['name'] ?? ''; //市
            $province_name = $province_data['name'] ?? ''; //省
        }else{
            $district_name = $district; //乡
            $city_name     = $city; //市
            $province_name = $province; //省
        }
        //返回内容 门牌号，村庄，巷，街道，乡，市，省
        $address = "{$resume_info['residence_house_num']} {$resume_info['residence_village']} {$resume_info['residence_alley']} {$resume_info['residence_street']} {$district_name} {$city_name} {$province_name}";

        return $address;


    }

    public function jdList($reserve_type = 0)
    {
        $settingServer = new SettingEnvServer();
        $jds = $settingServer->getSetVal('by_recommender_resume_jd');
        $jds = explode(',', $jds);
        $jdOther = $settingServer->getSetVal('by_recommender_resume_jd_other');
        $jdOther = explode(',', $jdOther);

        if (isCountry(['TH','MY'])){
            $recommendKdJd = $settingServer->getSetVal('h5_th_head_office_no_like_job');
        }else{
            $recommendKdJd = $settingServer->getSetVal('by_recommender_resume_jd_kd');
        }
        $recommendKdJd = !empty( $recommendKdJd ) ? explode(',',$recommendKdJd) : [] ;

        $jdList = HrJdModel::find([
            'conditions' => ' state = 1'
        ])->toArray();
        $result = ['hot_list' => [], 'list' => []];
        $list = [];
        foreach ($jdList as $jd) {
            if (in_array($jd['job_id'], $jdOther) && (in_array('ALL', $jds) || in_array($jd['job_id'], $jds))) {
                $result['hot_list'][] = [
                    'job_id' => $jd['job_id'],
                    'job_name' => $jd['job_name'],
                    'job_type' => in_array($jd['job_id'], $recommendKdJd) ? 1 :2,
                ];
            }

            if ((in_array('ALL', $jds) || in_array($jd['job_id'], $jds))) {
                $list[substr(strtolower($jd['job_name']), 0, 1)][] = [
                    'job_id' => $jd['job_id'],
                    'job_name' => $jd['job_name'],
                    'job_type' => in_array($jd['job_id'], $recommendKdJd) ? 1 : 2,
                ];
            }
        }

        $range =
        range('A', 'Z');
        foreach ($range as $item) {
            $result['list'][] = [
                'index' => $item,
                'list' => isset($list[strtolower($item)]) &&  $list[strtolower($item)] ? $list[strtolower($item)] : []
            ];
        }

        return $result;
    }



    public function SearchList($name,$reserve_type = 0)
    {
        $settingServer = new SettingEnvServer();
        $jds = $settingServer->getSetVal('by_recommender_resume_jd');
        $jds = explode(',', $jds);

        if (isCountry(['TH','MY'])){
            $recommendKdJd = $settingServer->getSetVal('h5_th_head_office_no_like_job');
        }else{
            $recommendKdJd = $settingServer->getSetVal('by_recommender_resume_jd_kd');
        }
        $recommendKdJd = !empty( $recommendKdJd ) ? explode(',',$recommendKdJd) : [] ;

        $jdList = HrJdModel::find([
            'conditions' => ' state = 1'
        ])->toArray();

        $list = [];
        foreach ($jdList as $jd) {
            if (in_array('ALL', $jds) || in_array($jd['job_id'], $jds)) {
                if (strpos(strtolower($jd['job_name']), strtolower($name)) !== false) {
                    $list[] = [
                        'job_id' => $jd['job_id'],
                        'job_name' => $jd['job_name'],
                        'job_type' => in_array($jd['job_id'], $recommendKdJd) ? 1 : 2,
                    ];
                }
            }
        }

        return $list;
    }


    /**
     * 获取简历推荐紧急发版国家
     * 紧急狄总需求-不做代码优化以及处理
     * @return bool
     */
    public function isRecommendCountry()
    {
        return isCountry('TH') || isCountry('PH') || isCountry('MY');
    }

    /**
     * 获取推荐简历提交后BY消息通知TA消息详情
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function resumeRecommendSubmitTaMs($param): array
    {
        $userId     = $param['user_id'] ?? 0;
        $msgId      = $param['msg_id'] ?? '';
        if (empty($msgId)){
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $server = new BackyardServer($this->lang, $this->timezone);
        // 验证该员工是否收到签字消息
        $sign_msg = $server->get_staff_sign_msg([
            'msg_id'=>$msgId,
            'staff_info_id'=>$userId,
        ]);
        if (empty($sign_msg) || $sign_msg['staff_info_id'] != $userId) {
            throw new ValidationException('员工没有收到该消息');
        }
        $contentModel = MessageContentModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $sign_msg['message_content_id']],
        ]);
        if (empty($contentModel)){
            throw new ValidationException('员工没有收到该消息');
        }

        $return_data['msg_id'] = $msgId;
        $return_data['state'] = 1;
        $return_data['message_data'] =!empty($contentModel->message) ? json_decode($contentModel->message,true) : [];
        if ($sign_msg['read_state'] != MessageCourierModel::READ_STATE_HAS_READ){
            $backyardServer = new BackyardServer($this->lang, $this->timeZone);
            $backyardServer->has_read_operation($msgId,true);
        }
        return $return_data;
    }
}
