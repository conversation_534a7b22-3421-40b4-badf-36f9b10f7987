<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use app\enums\LangEnums;
use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Enums\InteriorGoodsEnums;
use FlashExpress\bi\App\Enums\InteriorOrderFundStatusEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersWarehouseSetModel;
use FlashExpress\bi\App\Models\oa\SettingEnvModel as OASettingEnvModel;
use FlashExpress\bi\App\Modules\Ph\library\Enums\InteriorGoodsEnums as PhInteriorGoodsEnums;
use FlashExpress\bi\App\Modules\La\library\Enums\InteriorGoodsEnums as LaInteriorGoodsEnums;
use FlashExpress\bi\App\Modules\My\library\Enums\InteriorGoodsEnums as MyInteriorGoodsEnums;
use FlashExpress\bi\App\Enums\InteriorGoodsPayMethodEnums;
use FlashExpress\bi\App\Enums\InteriorGoodsStatusEnums;
use FlashExpress\bi\App\Enums\InteriorOrderAuditedEnums;
use FlashExpress\bi\App\Enums\InteriorOrderStatusEnums;
use FlashExpress\bi\App\Enums\ReturnMsgEnums;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HeadquartersAddressModel;
use FlashExpress\bi\App\Models\backyard\InteriorDepartmentModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsSkuLogModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsSkuModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersGoodsSkuModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersModel;
use FlashExpress\bi\App\Models\backyard\InteriorStaffsShoppingCartModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Models\backyard\SysDistrictModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\oa\SysDepartmentPcCode;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\InteriorGoodsRepository;
use FlashExpress\bi\App\Repository\WmsRepository;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Models\backyard\SysStoreGoodsModel;
use FlashExpress\bi\App\library\Exception\ValidationException;
use ReflectionException;


class InteriorGoodsServer extends BaseServer
{
    const IS_FREE = 1;//免费
    const IS_NO_FREE = 0;
    const FREE_COUNT_NUB= 1; //限免个数
    //by-员工商城-商品列表
    public static $validate_goods_list = [
        'goods_type' => 'IntIn:' . InteriorGoodsEnums::GOODS_TYPE_ORDER_ALL . ',' . InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES . ',' . InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE . '|>>>:params error goods_type', //商品类型,
        'goods_name' => 'StrLenGeLe:1,80|>>>:goods_name error',
        'page'       => 'IntGt:0',
        'limit'      => 'IntGt:0',
    ];
    private static $hire_limit_days = 7;

    /**
     * 所有工作日满7天的员工，第8天开始可以有购买权限。
     * @param array $loginUser
     * @return bool
     */
    public function getStaffBuyAuth(array $loginUser)
    {
        //TODO 如果是马来国家次校验永远为真 校验放在了提交订单接口里面  原因是前端人力不足
        if (isCountry('MY') == 'MY' || isCountry('PH') || isCountry('LA') ) {
            return true;
        }

        $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($loginUser['staff_id'], 'state,hire_date');
        if (!$userInfo || $userInfo['state'] != 1 || !$userInfo['hire_date']) {
            return false;
        }
        $nowDate  = date('Y-m-d H:i:s');
        $nowTime  = strtotime($nowDate);
        $hireTime = strtotime($userInfo->hire_date);
        $days     = ($nowTime - $hireTime) / 86400;
        $hireDays = sprintf("%.2f", $days);
        if ($hireDays <= self::$hire_limit_days) {
            return false;
        }
        return true;
    }


    /**
     * 所有工作日满7天的员工，第8天开始可以有购买权限。
     * @param array $loginUser
     * @return bool
     * 'free_goods_can_only' => '免费的商品仅可单独购买。',
     * 'employees_who_have_worked' => '入职7天内的员工，仅可免费购买1件工服。'
     * 'only_employees_who_have' => '只有工作满7天的员工才可以购买哦'
     * 'this_purchase_of_free_goods' => '本次购买的免费商品超过额度，不可购买。'
     * 'sale_and_non_sale_not_allowed' => '借用商品不允许与购买商品一起提交！'
     * 'each_employee_can_only' => '每人每月仅可购买5件工服。'
     */
    public function getStaffBuyAuthNew($rs)
    {
        $loginUser=$rs['loginUser'];
        $goodsIdsArr=$rs['goodsIdsArr'];//提交的数据
        $freeBuyNum=$rs['freeBuyNum'];//本次购买总数
        $has_buy_free_num = $rs['has_buy_free_num'];
        $goodsIds=$rs['goodsIds'];//本费不免费的数据
        $free_goods_ids=$rs['free_goods_ids'];//免费goods_ids


        $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($loginUser['staff_id'], 'staff_info_id, state,hire_date,node_department_id,sys_store_id,formal,job_title');
        if (!$userInfo || $userInfo['state'] != 1 || !$userInfo['hire_date']) {
            return ['msg' => 'The account is deactivated!'];
        }
        $nowDate = date('Y-m-d H:i:s');
        $nowTime = strtotime($nowDate);
        $hireTime = strtotime($userInfo->hire_date);
        $days = ($nowTime - $hireTime) / 86400;
        $hireDays = sprintf("%.2f", $days);

        $is_free_arr = array_column($goodsIdsArr, 'is_free');
        //不全是免费购买的商品，则无法下订单
        if (in_array(self::IS_NO_FREE, $is_free_arr) && in_array(self::IS_FREE, $is_free_arr)) {
            return ['msg' => $this->getTranslation()->_('free_goods_can_only')];
        }

        //全是正常的商品 收费的
        if ($hireDays <= 7 && !in_array(self::IS_FREE, $is_free_arr) && in_array(self::IS_NO_FREE, $is_free_arr)) {
            return ['msg' => $this->getTranslation()->_('only_employees_who_have')];
        }

        //借用商品不允许与购买商品一起提交！
        $interior_non_sale_goods_ids = (new SettingEnvServer())->getSetVal('interior_non_sale_goods_id', ',');
        $non_sale_goods = [];//借用商品组
        $sale_goods = [];//购买商品组
        foreach ($goodsIds as $goods_id) {
            if (in_array($goods_id, $interior_non_sale_goods_ids)) {
                $non_sale_goods[] = $goods_id;
            } else {
                $sale_goods[] = $goods_id;
            }
        }
        //同时存在拦截
        if ($non_sale_goods && $sale_goods) {
            return ['msg' => $this->getTranslation()->_('sale_and_non_sale_not_allowed')];
        }

        //全是免费的商品
        if (in_array(self::IS_FREE, $is_free_arr) && !in_array(self::IS_NO_FREE, $is_free_arr) && $freeBuyNum) {
            foreach ($goodsIds as $goods_id) {
                if (!in_array($goods_id, $free_goods_ids)) {
                    //本次提交的商品不在限免的商品组里
                    return ['msg' => $this->getTranslation()->_('this_purchase_of_free_goods')];
                }
            }

            //19803【PH|OA BY KIT|员工商城】员工商城支持雨衣售卖
            $interior_goods_raincoat_set = (new SettingEnvServer())->getSettingEnvValueMap('interior_goods_raincoat_set');
            $raincoat_set_good_ids = ($interior_goods_raincoat_set && $interior_goods_raincoat_set['good_ids']) ? explode(',', $interior_goods_raincoat_set['good_ids']) : [];
            $raincoat_free_num = 0;
            foreach ($goodsIdsArr as $goods_info) {
                if (in_array($goods_info['goods_id'], $raincoat_set_good_ids)) {
                    $raincoat_free_num = bcadd($raincoat_free_num, $goods_info['buy_num']);
                }
            }
            //雨衣售卖校验
            if ($raincoat_free_num) {
                $interior_goods_raincoat_set['good_ids'] = $raincoat_set_good_ids;
                $raincoat_can_free_buy_num = $this->getCanFreeBuyGoodsRaincoatNums($interior_goods_raincoat_set, $userInfo);
                if ($raincoat_free_num > $raincoat_can_free_buy_num) {
                    return ['msg' => $this->getTranslation()->_('this_purchase_of_free_goods')];
                }
            }

            //存在非雨衣售卖校验
            $freeBuyNum = bcsub($freeBuyNum, $raincoat_free_num);
            if ($freeBuyNum) {
                //获取可购买免费工服的件数
                $free_buy_limit = $this->getCanFreeBuyGoodsNums($userInfo);
                if ($free_buy_limit) {
                    //免费工服件数设置存在,判断已购买免费工服数+本次购买件数，超过设置的可购买工服件数
                    $total_buy_num = bcadd($freeBuyNum, $has_buy_free_num);
                    if ($total_buy_num > $free_buy_limit) {
                        return ['msg' => $this->getTranslation()->_('this_purchase_of_free_goods')];
                    }
                    //获取可购买免费工服的入职天数
                    self::$hire_limit_days = $this->getCanFreeBuyGoodsEntryDays($userInfo);
                    if ($hireDays <= self::$hire_limit_days && $total_buy_num > self::FREE_COUNT_NUB) {
                        //未超过配置天数内，购买的工服超过1件，则要给予提示
                        return ['msg' => $this->getTranslation()->_('employees_who_have_worked', ['day' => self::$hire_limit_days])];
                    }
                } else {
                    //没有免费工服件数设置
                    return ['msg' => $this->getTranslation()->_('this_purchase_of_free_goods')];
                }
            }
        }
        return [];
    }
    /**
     * 获取员工商城里面的语言后缀
     * @Date: 7/17/23 5:03 PM
     * @param string $lang 语种
     * @return string
     **/
    public function getLangFix(string $lang = 'en')
    {
        switch ($lang) {
            case LangEnums::LANG_CODE_ZH_CN :
                $lang = 'zh';
                break;
            case LangEnums::LANG_CODE_EN:
            default:
                $lang = 'en';
                break;
        }
        return $lang;
    }

    public function getGoodsList(array $loginUser, array $params = [])
    {
        $page     = $params['page'] ?? 1;
        $limit    = $params['limit'] ?? 10;
        $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($loginUser['staff_id']);
        if (!$userInfo) {
            $pageData = [
                'goods_list'   => null,
                'current_page' => 1,
                'count'        => 0,
            ];
            return $pageData;
        }

        $intersect_goods_ids = (new InteriorGoodsRepository())->getIsBuyAndDepartmentSet($userInfo->toArray());
        $intersect_goods_ids = !empty($intersect_goods_ids) ? $intersect_goods_ids : [0];

        $goodsStatusArr = [
            InteriorGoodsStatusEnums::GOODS_STATUS_ON_SALE,
        ];
        $conditions = "goods_cate in ({goodsCate:array}) and status in ({status:array}) and (FIND_IN_SET(:buy_hire_type:, buy_hire_type) OR buy_hire_type = :buy_hire_type_empty:)";
        $bind = ["goodsCate" => $intersect_goods_ids, 'status' => $goodsStatusArr, 'buy_hire_type' => $userInfo->hire_type, 'buy_hire_type_empty' => ''];

        if (!empty($params['goods_type'])) {
            $conditions = $conditions . ' and goods_type = :goods_type:';
            $bind       = array_merge($bind, ['goods_type' => $params['goods_type']]);
        }
        if (isset($params['goods_name']) && $params['goods_name'] != '') {
            $fix        = $this->getLangFix($this->lang);
            $goods_name = 'goods_name_' . $fix;
            $conditions = $conditions . ' and ' . $goods_name . ' like :goods_name:';
            $bind = array_merge($bind, ['goods_name' => '%' . $params['goods_name'] . '%']);
        }
        $goodsList = InteriorGoodsModel::find(
            array(
                'conditions' => $conditions,
                'bind'       => $bind,
                'order'      => 'id desc, status asc',
                'limit'      => $limit,
                'offset'     => ($page - 1) * $limit,
            )
        );
        $goodsList = $goodsList->toArray();
        if ($goodsList) {
            $goodsIds = array_column($goodsList, 'id');
            // 按照某条件取出某一个作为首页展示: 只取在售状态的sku
            $skusObjs  = InteriorGoodsSkuModel::find([
                'conditions' => 'goods_id in ({goods_id:array}) AND status IN ({status:array})',
                'bind'       => ['goods_id' => $goodsIds, 'status' => $goodsStatusArr],
                'order'      => 'id ASC',
                'group'      => 'goods_id',
                'columns'    => 'goods_id, min(id) as id, max(surplus_num) as max_surplus_num',
            ]);
            $skuIdsArr = $keyGoodsIdSkusArr = [];
            $skusArr   = $skusObjs->toArray();
            if ($skusArr) {
                $skuIdsArr         = array_column($skusArr, 'id');
                $keyGoodsIdSkusArr = array_column($skusArr, null, 'goods_id');
            }
            $goodsSkus    = InteriorGoodsSkuModel::find([
                'conditions' => 'id in ({id:array})',
                'bind'       => ['id' => $skuIdsArr],
                'order'      => 'id desc',
            ]);
            $goodsSkusArr = [];
            if ($goodsSkus) {
                $goodsSkusArr = array_column($goodsSkus->toArray(), null, 'goods_id');
                foreach ($goodsSkusArr as &$goodsSku) {
                    $goodsSku['price_fmt']       = InteriorGoodsEnums::fmtAmount($goodsSku['price'], $this->lang);
                    $goodsSku['show_goods_name'] = self::convertGoodsNameByLang($this->lang, $goodsSku);
                    //折扣为(商品价值-销售价格)/商品价值*100%，四舍五入取两位小数加Off
                    $goodsSku['discount'] = round(bcmul(bcdiv(bcsub($goodsSku['commodity_value'], $goodsSku['price'], 6), $goodsSku['commodity_value'], 6), 100, 6), 2);
                }
            }
            foreach ($goodsList as &$goods) {
                $goods['goods_sku']       = $goodsSkusArr[$goods['id']] ?? null;
                $goods['max_surplus_num'] = $keyGoodsIdSkusArr[$goods['id']]['max_surplus_num'] ?? 0;
                $goods['show_goods_name'] = self::convertGoodsNameByLang($this->lang, $goods);
                $goods['img_path']        = !empty($goods['img_path']) ? explode(',', $goods['img_path'])[0] : '';
            }
        }
        $count    = InteriorGoodsModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        $pageData = [
            'goods_list'   => $goodsList,
            'current_page' => $page,
            'count'        => $count,
        ];
        return $pageData;
    }

    public function getGoodsDetail(array $loginUser, $reqParams)
    {
        $goodsId  = $reqParams['goods_id'];
        $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($loginUser['staff_id']);
        if (empty($userInfo)) {
            return ['msg' => 'staff_id not find'];
        }

        $goodsInfo = self::_getUserEnableBuyGoods($goodsId);
        if (is_array($goodsInfo) && isset($goodsInfo['msg'])) {
            return $goodsInfo['msg'];
        }
        if (!$goodsInfo) {
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_goods_was_not_found),
            ];
        }
        $goodsInfoArr             = $goodsInfo->toArray();
        $goodsInfoArr['img_path'] = explode(',', $goodsInfoArr['img_path']);
        $goodsInfoArr['free_num'] = (int)$goodsInfoArr['free_num'];
        //增加员工所属网点大区判断
        $setting_env         = new SettingEnvServer();
        $store_manage_region = $setting_env->getSetVal('shop_free_manage_region_ids');

        if (!empty($store_manage_region)) {
            $store                   = (new SysStoreModel())->getOneStoreById($userInfo->sys_store_id);
            $store_manage_region_arr = explode(',', $store_manage_region);
            if (in_array($store['manage_region'], $store_manage_region_arr)) {
                $goodsInfoArr['free_num'] = 0;
            }
        }
        //限免费个数>0 逻辑判读是否展示 免费按钮
        if ($goodsInfoArr['free_num'] > 0) {
            //先判断商品身上设置的免费权限雇佣类型，若员工的雇佣类型不满足设置里则没有免费按钮
            if ($goodsInfoArr['free_hire_type'] && !in_array($userInfo->hire_type, explode(',', $goodsInfoArr['free_hire_type']))) {
                $goodsInfoArr['free_num'] = 0;
            } else {
                //网点查询是否限免 计算 剩余限免
                $freeGoodsId = (new InteriorGoodsRepository())->getFreeGoodsIdsAll($userInfo->toArray()) ?? [0];
                if (in_array($goodsId, $freeGoodsId)) {
                    //19803【PH|OA BY KIT|员工商城】员工商城支持雨衣售卖
                    $interior_goods_raincoat_set = $setting_env->getSettingEnvValueMap('interior_goods_raincoat_set');
                    $raincoat_set_good_ids = ($interior_goods_raincoat_set && $interior_goods_raincoat_set['good_ids']) ? explode(',', $interior_goods_raincoat_set['good_ids']) : [];
                    if (in_array($goodsId, $raincoat_set_good_ids)) {
                        $interior_goods_raincoat_set['good_ids'] = $raincoat_set_good_ids;
                        (int)$goodsInfoArr['free_num'] = $this->getCanFreeBuyGoodsRaincoatNums($interior_goods_raincoat_set, $userInfo);
                    } else {
                        //查询已购买
                        $has_buy_free_num = $this->getUserFreeByNumNew($loginUser);
                        $free_buy_limit = $this->getCanFreeBuyGoodsNums($userInfo);
                        (int)$goodsInfoArr['free_num'] = (int)$free_buy_limit - (int)$has_buy_free_num;
                    }
                } else {
                    $goodsInfoArr['free_num'] = 0;
                }
            }
        }

        $goodsInfoArr['show_goods_name'] = self::convertGoodsNameByLang($this->lang, $goodsInfoArr);
        $goodsSkus                       = $goodsInfo->getGoodsSkus();
        if (!$goodsSkus) {
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_goods_was_not_found),
            ];
        }

        //查询库存
        $goods_stock                = [];
        $goodsInfoArr['goods_size'] = [];

        if ($goodsInfoArr['goods_type'] == InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
            $goods_stock = $this->getGoodsBarcodeStock(implode(',', array_column($goodsSkus, 'goods_sku_code')), $goodsInfoArr['goods_type']);
        } else {
            $goodsInfoArr['goods_size'] = $this->goodsSize($goodsInfoArr['id']);
        }

        foreach ($goodsSkus as &$goodsSku) {
            //如果是无头件 查询时时库存
            if ($goodsInfoArr['goods_type'] == InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
                $goodsSku['surplus_num'] = $goods_stock[$goodsSku['goods_sku_code']] ?? '0';
            }
            $goodsSku['price_fmt']       = InteriorGoodsEnums::fmtAmount($goodsSku['price'], $this->lang);
            $goodsSku['show_goods_name'] = self::convertGoodsNameByLang($this->lang, $goodsSku);
            $goodsSku['img_path']        = [$goodsSku['img_path']];
            //折扣为(商品价值-销售价格)/商品价值*100%，四舍五入取两位小数加Off
            $goodsSku['discount'] = round(bcmul(bcdiv(bcsub($goodsSku['commodity_value'], $goodsSku['price'], 6), $goodsSku['commodity_value'], 6), 100, 6), 2);
        }
        $hasSurplusNum = InteriorGoodsSkuModel::findFirst([
            'conditions' => 'goods_id = :goodsId: and surplus_num > 0',
            'bind'       => ['goodsId' => $goodsId],
        ]);
        if ($hasSurplusNum) {
            $goodsInfoArr['is_sold_out'] = false;
        } else {
            $goodsInfoArr['is_sold_out'] = true;
        }
        $goodsInfoArr['goods_sku'] = $goodsSkus;
        //商品是否属于配置的缴纳押金SPU(非售卖商品)
        $interior_non_sale_goods_ids = (new SettingEnvServer())->getSetVal('interior_non_sale_goods_id', ',');
        $goodsInfoArr['is_non_sale'] = in_array($goodsInfoArr['id'], $interior_non_sale_goods_ids) ? true : false;
        return $goodsInfoArr;
    }

    /**
     * 实时获取指定类型商品的库存
     *
     * @param string $bar_code
     * @param int $goods_type
     * @return array
     */
    public function getGoodsBarcodeStock(string $bar_code, int $goods_type)
    {
        $item_goods_sku_code = $new_goods_stock = [];
        $bar_code_arr        = array_values(array_unique(explode(',', $bar_code)));
        $goods_stock = $this->getGoodsStock($bar_code, $goods_type);
        if (!empty($bar_code_arr) && !empty($goods_stock)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('iogs.goods_sku_code, iogs.buy_num, iogs.pay_amount');
            $builder->from(['iogs' => InteriorOrdersGoodsSkuModel::class]);
            $builder->leftjoin(InteriorOrdersModel::class, 'iogs.order_code = io.order_code', 'io');
            $builder->where('io.goods_type = :goods_type: and io.pay_method = :pay_method: and  io.order_status IN ({order_status:array})', [
                'goods_type'             => InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE,
                'pay_method'             => InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE,
                'order_status'           => [
                    InteriorOrderStatusEnums::ORDER_STATUS_WARITING_SUBMIT_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_PAYING_CODE,
                ],
            ]);
            $builder->inWhere('iogs.goods_sku_code', $bar_code_arr);
            $data = $builder->getQuery()->execute()->toArray();
            if (!empty($data)) {
                foreach ($data as $item) {
                    $item_goods_sku_code[$item['goods_sku_code']] = ($item_goods_sku_code[$item['goods_sku_code']] ?? 0) + $item['buy_num'];
                }
            }
            $this->getDI()->get('logger')->write_log("查询barcode {$bar_code} 的可购买库存, goods_stock=" . json_encode($goods_stock, JSON_UNESCAPED_UNICODE), 'info');
            foreach ($goods_stock as $key => $value) {
                //barcode 对应的待上传支付凭证的数量
                $old_order_num = $item_goods_sku_code[$key] ?? 0;
                //当前库存减去barcode 对应的待上传支付凭证的数量为当前剩余库存
                $mul_stock_num         = $value - $old_order_num;
                $new_goods_stock[$key] = $mul_stock_num > 0 ? (string)$mul_stock_num : '0';
            }
        }
        $this->getDI()->get('logger')->write_log('减去已购买数量之后的当前库存：' . json_encode($new_goods_stock, JSON_UNESCAPED_UNICODE), 'info');
        return $new_goods_stock;
    }

    /**
     * 获取员工可买商品类别
     * @param array $userInfo
     * @return array|string[]
     */
    public static function getUserEnableBuyGoodsCate(array $userInfo)
    {
        $storeCateCode = HrStaffInfoServer::getStaffStoreCateCode($userInfo);
        (new BaseServer())->wLog('StoreCateCode', $storeCateCode);
        if(isset($storeCateCode['msg'])){
            return $storeCateCode;
        }
        $goods_ids =  (new InteriorGoodsRepository())->getInteriorGoodsStoreCateRelAll($storeCateCode);
        (new BaseServer())->wLog('log_goods_ids:', $goods_ids);
        return  $goods_ids;
    }

    /**
     * @param $goodsId
     * @return mixed
     */
    private static function _getUserEnableBuyGoods($goodsId)
    {
        $goodsStatusArr        = [
            InteriorGoodsStatusEnums::GOODS_STATUS_ON_SALE,
        ];
        $conditions            = "status in ({status:array}) and id = :id:";
        $bind                  = ['status' => $goodsStatusArr, 'id' => $goodsId];

        $goodsInfo = InteriorGoodsModel::findFirst(
            array(
                'conditions' => $conditions,
                'bind'       => $bind,
            )
        );
        return $goodsInfo;
    }

    /**
     * 加入购物车, 当buy_num 为负数时 购物车数量递减当为0时从购物车中删除
     */
    public function addGoodsSkuToCart(array $loginUser, array $params)
    {
        $goodsId    = $params['goods_id'];
        $goodsSkuId = $params['goods_sku_id'];
        $buyNum     = $params['buy_num'];
        $is_free    = $params['is_free'] ?? 0;
        $staffId    = $loginUser['staff_id'];
        $userInfo   = HrStaffInfoServer::getUserInfoByStaffInfoId($staffId);
        $goodsInfo  = self::_getUserEnableBuyGoods($goodsId);
        if(is_array($goodsInfo) && isset($goodsInfo['msg'])){
            return $goodsInfo;
        }
        $goodsSku   = self::_getGoodsSku($goodsId, $goodsSkuId);
        if (!$goodsInfo || !$goodsSku) {
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_goods_was_not_found),
            ];
        }
        $conditions   = 'staff_id = :staffId: and goods_sku_id = :skuId:';
        $bind         = [
            'staffId' => $staffId,
            'skuId'   => $goodsSkuId,
        ];
        $staffCartSku = InteriorStaffsShoppingCartModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        if (empty($staffCartSku)) {
            if ($buyNum <= 0) {
                return [
                    'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_goods_not_in_cart),
                ];
            }
            $staffCartSku          = new InteriorStaffsShoppingCartModel();
            $staffCartSku->buy_num = $buyNum;
        } else {
            //购物车非0
            if ($is_free != $staffCartSku->is_free) {
                $isMsg = true;
                $staffCartSku->buy_num = $buyNum;
            } else {
                $staffCartSku->buy_num += $buyNum;

            }

        }
        $staffCartSku->is_free       = $is_free;
        $staffCartSku->staff_id      = $staffId;
        $staffCartSku->goods_sku_id  = $goodsSkuId;
        $staffCartSku->created_price = $goodsSku->price;
        $staffCartSku->staff_id      = $staffId;

        if ($staffCartSku->buy_num <= 0) {
            $staffCartSku->delete();
            $res = [
                'is_delete' => true,
            ];
        } else {
            $staffCartSku->save();
            $res = [
                'is_add_cart' => true,
                'tip_msg' =>$isMsg??false,
            ];
        }
        return $res;
    }

    public static function _getGoodsSku($goodsId, $goodsSkuId)
    {
        $conditions = 'goods_id = :goodsId: and id = :skuId:';
        $bind       = [
            'goodsId' => $goodsId,
            'skuId'   => $goodsSkuId,
        ];
        $goodsSku   = InteriorGoodsSkuModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        return $goodsSku;
    }

    /**
     * 查看购物车列表
     */
    public function getCartGoodsList(array $loginUser, array $params)
    {
        $page             = $params['page'];
        $limit            = $params['limit'];
        $staffId          = $loginUser['staff_id'];
        $conditions       = "staff_id = :staffId: ";
        $bind             = ["staffId" => $staffId];
        $cartGoodsSkuList = InteriorStaffsShoppingCartModel::find(
            array(
                'conditions' => $conditions,
                'bind'       => $bind,
                'order'      => 'id desc',
                'limit'      => $limit,
                'offset'     => ($page - 1) * $limit,
            )
        );
        $cartGoodsSkuList = $cartGoodsSkuList->toArray();
        if ($cartGoodsSkuList) {
            $skuIdsArr = array_column($cartGoodsSkuList, 'goods_sku_id');
            $builder   = $this->modelsManager->createBuilder();
            $builder->columns('ig.goods_type, igs.id, igs.goods_sku_code, igs.goods_name_en, igs.goods_name_th, igs.goods_name_zh, igs.img_path, igs.attr_1, igs.attr_2, igs.unit_en, igs.unit_th, igs.unit_zh, igs.unit_num, igs.sale_num, igs.surplus_num, igs.total_num, igs.price, igs.status, igs.goods_id');
            $builder->from(['igs' => InteriorGoodsSkuModel::class]);
            $builder->leftjoin(InteriorGoodsModel::class, 'igs.goods_id = ig.id', 'ig');
            $builder->Where('igs.id in ({id:array})', ['id' => $skuIdsArr]);
            $builder->orderby('igs.id desc');

            $goodsSkus    = $builder->getQuery()->execute()->toArray();
            $goodsSkusArr = [];
            if (!empty($goodsSkus)) {
                $goodsSkusArr = array_column($goodsSkus, null, 'id');

                $goods_stock = $sku_arr = [];
                foreach ($goodsSkus as $goods_sku_item) {
                    if ($goods_sku_item['goods_type'] == InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
                        $sku_arr[] = $goods_sku_item['goods_sku_code'];
                    }
                }
                if (!empty($sku_arr)) {
                    $goods_stock = $this->getGoodsBarcodeStock(implode(',', $sku_arr), InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE);
                }
                foreach ($goodsSkusArr as &$goodsSku) {
                    if ($goodsSku['goods_type'] == InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
                        $goodsSku['surplus_num'] = $goods_stock[$goodsSku['goods_sku_code']] ?? 0;
                    }
                    $goodsSku['price_fmt']       = InteriorGoodsEnums::fmtAmount($goodsSku['price'], $this->lang);
                    $goodsSku['show_goods_name'] = self::convertGoodsNameByLang($this->lang, $goodsSku);
                }
            }
            foreach ($cartGoodsSkuList as &$cartGoodsSku) {
                $cartGoodsSku['goods_sku']         = $goodsSkusArr[$cartGoodsSku['goods_sku_id']] ?? null;
                $cartGoodsSku['created_price_fmt'] = InteriorGoodsEnums::fmtAmount($cartGoodsSku['created_price'], $this->lang);
            }
        }
        $count    = InteriorStaffsShoppingCartModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        $pageData = [
            'cart_goods_list' => $cartGoodsSkuList,
            'current_page'    => $page,
            'count'           => $count,
            'limit'           => $limit,
        ];
        return $pageData;
    }

    /**
     * 批量删除购物车商品
     * @param array $loginUser
     * @param array $params
     * @return array|bool
     * CreateTime: 2020/8/7 0007 19:51
     */
    public function deleteCartGoods(array $loginUser, array $params)
    {
        $cartIds             = $params['cart_ids'];
        $staffId             = $loginUser['staff_id'];
        $conditions          = "staff_id = :staffId:  and id in ({cartIds:array}) ";
        $bind                = ["staffId" => $staffId, 'cartIds' => $cartIds];
        $cartGoodsSkuList    = InteriorStaffsShoppingCartModel::find(
            array(
                'conditions' => $conditions,
                'bind'       => $bind,
            )
        );
        $cartGoodsSkuListArr = $cartGoodsSkuList->toArray();
        if ($cartGoodsSkuListArr) {
            foreach ($cartGoodsSkuList as $cartGoods) {
                if ($cartGoods->delete() === false) {
                    return [
                        'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_goods_can_not_delete),
                    ];
                }
            }
        } else {
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_goods_not_in_cart),
            ];
        }
        return ['is_delete' => true];
    }

    /**
     * 获取待结算订单信息
     * @param array $loginUser
     * @param array $params
     * @return array
     * @throws ValidationException
     * @throws ReflectionException
     */
    public function getNoSubmitOrderInfo(array $loginUser, array $params)
    {
        $goodsType      = $this->unclaimedValidation($params);
        $staffId        = $loginUser['staff_id'];
        $goodsIdsArr    = $params['buy_goods_ids_arr'];
        $isPreSale      = $params['is_pre_sale'] ?? false;
        $csrfServer     = new CsrfTokenServer();
        $csrfToken      = $csrfServer->getCsrfToken();
        $totalPayAmount = 0;
        $buyGoodsSkus   = [];
        $wrs_server = new WmsServer($this->lang,$this->timeZone);
        $wrs_server = Tools::reBuildCountryInstance($wrs_server, [$this->lang, $this->timeZone]);
        foreach ($goodsIdsArr as $k => $reqItems) {
            $goodsId     = $reqItems['goods_id'];
            $skuId       = $reqItems['goods_sku_id'];
            $buyNum      = $reqItems['buy_num'];
            $goodsSkuObj = $this->getGoodsSkuInfo($goodsId, $skuId);
            if (!$goodsSkuObj) {
                return [
                    'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_goods_was_not_found),
                ];
            }
            $goodsSku                    = $goodsSkuObj->toArray();
            $goodsSku['show_goods_name'] = self::convertGoodsNameByLang($this->lang, $goodsSku);

            // 计算订单总金额
            $price = $goodsSkuObj->price;
            if (isset($reqItems['is_free']) && self::IS_FREE == $reqItems['is_free']) {
                $price = 0;
                $goodsSku['price'] = 0;
            }
            $amount                 = $price * $buyNum;
            $goodsSku['buy_num']    = $buyNum;
            $goodsSku['amount']     = $amount;
            $goodsSku['amount_fmt'] = $wrs_server->format_amount($amount);
            $buyGoodsSkus[]         = $goodsSku;
            $totalPayAmount         += $amount;
        }

        $payMethodMap = InteriorGoodsPayMethodEnums::getCodeTxtMap($this->lang);
        $userInfo          = HrStaffInfoServer::getUserInfoByStaffInfoId($staffId);
        $userInfo          = $userInfo->toArray();
        $departmentId = $userInfo['sys_department_id'];
        $conditions   = "department_id = :department_id:";
        $bind         = ["department_id" => $departmentId];
        $dept         = InteriorDepartmentModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        $isHeadquarters = $userInfo['sys_store_id'] == -1;
        $storeInfo = [];
        if (!$isHeadquarters && $userInfo['sys_store_id']) {
            // 网点员工展示网点的地址
            $storeInfo = SysStoreModel::findFirst([
                'conditions' => "id = :storeId:",
                'bind' => [
                    'storeId' => $userInfo['sys_store_id'],
                ],
                'columns' => 'id staff_store_id,name as store_name,detail_address,province_code,city_code,district_code,category,postal_code',
            ]);
            $storeInfo = $storeInfo ? $storeInfo->toArray() : [];
            $storeInfo = $this->getOrderReceiveStoreInfo($storeInfo, $goodsType);
        }

        // 支付方式: 免费为工资扣除, 自费为FlashPay支付 [不区分商品类型]
        $pay_method = $totalPayAmount > 0 ? InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE : InteriorGoodsPayMethodEnums::PAY_METHOD_DEDUCTION_OF_WAGES_CODE;
        $pay_method_list = [
            [
                'pay_method' => $pay_method,
                'pay_method_name' => $payMethodMap[$pay_method],
            ],
        ];

        $data = [
            'goods_skus'           => $buyGoodsSkus,
            'total_pay_amount'     => $totalPayAmount,
            'total_pay_amount_fmt' => InteriorGoodsEnums::fmtAmount($totalPayAmount, $this->lang),
            'is_pre_sale'     => $isPreSale,
            'csrf_token'      => $csrfToken,
            'is_headquarters' => $isHeadquarters,
            'address'         => $dept->address ?? '',
            'pay_method_list'      => $pay_method_list,
            'pay_method'           => $pay_method_list[0]['pay_method'],
            'pay_method_name'      => $pay_method_list[0]['pay_method_name'],
        ];
        $this->getOrderOtherInfo($data, $userInfo, $goodsIdsArr);

        $result_data = array_merge($data, $storeInfo);

        $this->getDi()->get('logger')->write_log("下单员工详情日志 => " . json_encode($result_data, JSON_UNESCAPED_UNICODE), 'info');

        return $result_data;
    }

    /**
     * 21466 仅无头件时-判断员工所属网点是否属于配置的禁用网点类型或者网点名称属于配置的禁用网点名称(全模糊、不区分大小写进行匹配)，如果属于则无默认值，可以编辑
     * @param array $storeInfo 网点信息
     * @param integer $goodsType 订单类型 1 工服 2 无头件
     * @return array
     */
    public function getOrderReceiveStoreInfo($storeInfo, $goodsType)
    {
        if ($storeInfo && $goodsType == InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
            $settingEnvServer = new SettingEnvServer();
            $interiorOrderForbiddenStoreCategory = array_filter($settingEnvServer->getSetVal('interior_order_forbidden_store_category', ','));
            //判断所属员工网点是否属于配置的禁用网点类型,如果属于则无默认值，可以编辑
            if ($interiorOrderForbiddenStoreCategory && in_array($storeInfo['category'], $interiorOrderForbiddenStoreCategory)) {
                $storeInfo = [];
            } else {
                //判断所属员工网点网点名称是否属于配置的禁用网点名称(全模糊、不区分大小写进行匹配),如果属于则无默认值，可以编辑
                $interiorOrderForbiddenStoreName = array_filter($settingEnvServer->getSetVal('interior_order_forbidden_store_name', ','));
                foreach ($interiorOrderForbiddenStoreName as $item) {
                    if (stristr($storeInfo['store_name'], $item) !== false) {
                        $storeInfo = [];
                        break;
                    }
                }
            }
        }
        return $storeInfo;
    }

    /**
     * 获取订单其他附属信息
     * @param array $data 返回结果参数组
     * @param array $userInfo 登陆者信息组
     * @param array $goodsIdsArr 请求参数组
     */
    public function getOrderOtherInfo(&$data, $userInfo, $goodsIdsArr)
    {
        $data['hire_type'] = $userInfo['hire_type'];//雇佣类型
        $settingEnvServer = new SettingEnvServer();
        $interior_non_sale_goods_ids = $settingEnvServer->getSetVal('interior_non_sale_goods_id', ',');
        $non_sale_goods = [];//借用商品组
        $sale_goods = [];//购买商品组
        foreach ($goodsIdsArr as $item) {
            if (in_array($item['goods_id'], $interior_non_sale_goods_ids)) {
                $non_sale_goods[] = $item['goods_id'];
            } else {
                $sale_goods[] = $item['goods_id'];
            }
        }
        //同时存在拦截
        $data['is_can_submit'] = ($non_sale_goods && $sale_goods) ? false : true;//是否可提交订单
        $data['is_non_sale'] = ($non_sale_goods && !$sale_goods) ? true : false;//是否是借用订单
        $data['deposit_handling_fee'] = $settingEnvServer->getSetVal('interior_deposit_handling_fee');//押金手续费
    }

    /**
     * 工服和无头件不可以混合提交
     * @param array $params
     * @return int    1 工服  2无头件
     **@throws ValidationException
     */
    public function unclaimedValidation(array $params)
    {
        //校验提交过来的订单数据
        $goods_arr = array_column($params['buy_goods_ids_arr'], 'goods_type');
        if (empty(array_diff([InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES, InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE], $goods_arr))) {
            throw new ValidationException($this->getTranslation()->_('work_clothes_and_unclaimed_mix_submit'));
        }
        return $goods_arr[0];
    }

    /**
     *确认提交订单
     * Created by: Lqz.
     * @param array $loginUser
     * @param array $params
     * @return array|string[]
     * CreateTime: 2020/8/10 0010 14:47
     */
    public function submitOrder(array $loginUser, array $params)
    {
        $this->getDI()->get('logger')->write_log("InteriorOrder=submit_order==login_user" . json_encode($loginUser, JSON_UNESCAPED_UNICODE) . 'time' . time() . '; submit params => ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        $db            = InteriorOrdersModel::beginTransaction($this);
        try {
            $orderRemark       = '';
            $goodsIdsArr       = $params['buy_goods_ids_arr'];
            $isPreSale         = $params['is_pre_sale'];
            $csrfToken         = $params['csrf_token'];
            $reqTotalPayAmount = $params['total_pay_amount'];
            $staffId           = $loginUser['staff_id'];
            $provinceCode      = $params['province_code'];
            $cityCode          = $params['city_code'];
            $districtCode      = $params['district_code'];
            $address           = $params['detail_address'];
            $postalCode        = $params['postal_code'];
            $storeId           = $params['staff_store_id']; // 前端传网点编号或总部id收货地址ID
            $userInfo          = HrStaffInfoServer::getUserInfoByStaffInfoId($staffId);
            $userInfo          = $userInfo->toArray();
            $provinceArr = SysProvinceModel::getProvincesArrByCodeArr([$provinceCode], 'code,name');
            $cityArr     = SysCityModel::getCitiesArrByCodeArr([$cityCode], 'code,name');
            $districtArr = SysDistrictModel::getDistrictsArrByCodeArr([$districtCode], 'code,name');
            $province    = trim($provinceArr[$provinceCode]['name'] ?? '');
            $city        = trim($cityArr[$cityCode]['name'] ?? '');
            $district    = trim($districtArr[$districtCode]['name'] ?? '');

            if (!$userInfo['mobile']) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_mobile_not_null'));
            }
            if (!$province) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_province_not_null'));
            }
            if (!$city) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_city_not_null'));
            }
            if (!$district) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_district_not_null'));
            }
            if (!$address) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_address_not_null'));
            }
            if (!$postalCode) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_postalcode_not_null'));
            }
            $this->authorityCheck($userInfo, $params);
            //如果是购物车提交的订单 删除购物对应的订单 ，1 表示购物车过来 0表示直接下单
            if ($params['source'] == 1) {
                $this->delShoppingCart($userInfo, $params);
            }
            $nodeSn = '';
            // 泰国或菲律宾取nodesn逻辑
            if (isCountry('TH') || isCountry('PH')) {
                if ($userInfo['sys_store_id'] != -1) {
                    // 网点员工传入网点编号
                    $nodeSn = $userInfo['sys_store_id'];
                } else {
                    // 总部员工传入成本中心,获取费用所属中心 先用子部门查 没有的话 拿顶级部门
                    $pcCode = SysDepartmentPcCode::findFirst("department_id = {$userInfo['node_department_id']}");
                    if (empty($pcCode)) {
                        $pcCode = SysDepartmentPcCode::findFirst("department_id = {$userInfo['sys_department_id']}");
                    }
                    $nodeSn = empty($pcCode) ? '' : $pcCode->pc_code;
                }
                if (!$nodeSn) {//目前只有泰国或者菲律宾有费用中心
                    throw new ValidationException(LangEnums::getTranslation($this->lang, ReturnMsgEnums::NO_PC_CODE_FOUND));
                }
            }

            // 多个商品生成一个订单对应多个商品
            $orderCode     = $this->generateOrderCode();
            $currentTime   = date('Y-m-d H:i:s');
            $setting_model = new SettingEnvServer();

            $totalPayAmount  = 0;
            $syncWmsPostData = $goods = [];

            //计算订单总金额，存入订单商品库
            $_hasSkuPreSale = 0;
            $_thisByNum     = 0;
            $total_num      = 0;//购买总数
            foreach ($goodsIdsArr as $k => $reqItems) {
                $currentSkuPreSale = 0;
                $goodsId           = $reqItems['goods_id'];
                $skuId             = $reqItems['goods_sku_id'];
                $buyNum            = $reqItems['buy_num'];
                if ($buyNum <= 0) {
                    continue;
                }
                $goodsSkuObj = $this->getGoodsSkuInfo($goodsId, $skuId, true);
                if (!$goodsSkuObj) {
                    throw new ValidationException(LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_goods_was_not_found));
                }
                $price = $goodsSkuObj->price;
                // 计算订单总金额
                if ((isCountry() || isCountry('MY') || isCountry('LA') || isCountry('PH')) && isset($reqItems['is_free']) && self::IS_FREE == $reqItems['is_free']) {
                    $price = 0;
                }
                $amount         = sprintf('%.2f', $price * $buyNum);
                $totalPayAmount += $amount;

                // 修改后库存
                $toSurplusNum = $goodsSkuObj->surplus_num - $buyNum;
                if (!$isPreSale && $toSurplusNum < 0) {
                    throw new ValidationException(LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_inventory_not_enough));
                }
                if ($toSurplusNum < 0) {
                    $currentSkuPreSale = 1;
                    $_hasSkuPreSale    = 1;
                }
                // 修改库存
                $goodsSkuObj->sale_num += $buyNum;

                // 记录库存修改日志
                self::saveSkuSurplusNumAndLog($goodsSkuObj, $staffId, $buyNum, $toSurplusNum, 'staff_buy');
                //加入订单商品表
                $orderGoodsSkuObj     = new InteriorOrdersGoodsSkuModel();
                $order_goods_sku_data = [];
                if (self::IS_NO_FREE == $reqItems['is_free']) {
                    $_thisByNum += $buyNum;
                }
                $total_num += $buyNum;

                if (isset($reqItems['is_free']) && self::IS_FREE == $reqItems['is_free']) {
                    $order_goods_sku_data['is_free'] = 1;
                }
                $order_goods_sku_data['order_code']           = $orderCode;
                $order_goods_sku_data['goods_id']             = $goodsSkuObj->goods_id;
                $order_goods_sku_data['goods_sku_id']         = $goodsSkuObj->id;
                $order_goods_sku_data['goods_sku_code']       = $goodsSkuObj->goods_sku_code;
                $order_goods_sku_data['goods_name_en']        = $goodsSkuObj->goods_name_en;
                $order_goods_sku_data['goods_name_th']        = $goodsSkuObj->goods_name_th;
                $order_goods_sku_data['goods_name_zh']        = $goodsSkuObj->goods_name_zh;
                $order_goods_sku_data['img_path']             = $goodsSkuObj->img_path;
                $order_goods_sku_data['attr_1']               = $goodsSkuObj->attr_1;
                $order_goods_sku_data['attr_2']               = $goodsSkuObj->attr_2;
                $order_goods_sku_data['unit_en']              = $goodsSkuObj->unit_en;
                $order_goods_sku_data['unit_th']              = $goodsSkuObj->unit_th;
                $order_goods_sku_data['unit_zh']              = $goodsSkuObj->unit_zh;
                $order_goods_sku_data['unit_num']             = $goodsSkuObj->unit_num;
                $order_goods_sku_data['buy_price']            = $price;
                $order_goods_sku_data['buy_num']              = $buyNum;
                $order_goods_sku_data['pay_amount']           = $amount;
                $order_goods_sku_data['current_sku_pre_sale'] = $currentSkuPreSale;
                $success                                      = $db->insertAsDict(
                    $orderGoodsSkuObj->getSource(),
                    $order_goods_sku_data
                );
                if ($success === false) {
                    throw new BusinessException('订单提交 - interior_orders_goods_sku 写入失败, data = ' . json_encode($order_goods_sku_data, JSON_UNESCAPED_UNICODE), ErrCode::DATABASE_ERROR);
                }
                $goods[] = [
                    'i'             => $k,
                    'barCode'       => $goodsSkuObj->goods_sku_code,
                    'goodsName'     => $goodsSkuObj->goods_name_th,
                    'specification' => $goodsSkuObj->attr_1,
                    'num'           => $buyNum,
                    'price'         => ($price * 100),//出库商品json，其中price的单位为萨当，即1泰铢=100萨当
                    'remark'        => $amount,
                ];
            }
            if (isCountry('MY') || isCountry('LA') || isCountry('PH') || isCountry()) {
                $goodsIds         = array_column($goodsIdsArr, 'goods_id');
                $has_buy_free_num = $this->getUserFreeByNumNew($loginUser);
                $goodsList        = InteriorGoodsModel::find([
                    'conditions' => 'id in({goods_id:array}) and free_num >0',
                    'bind'       => ['goods_id' => $goodsIds],
                    'columns'    => ['id', 'free_num'],
                ]);
                if (empty($goodsList)) {
                    throw new ValidationException($this->getTranslation()->_('interior_goods_data_not_null'));
                }
                $goodsList      = $goodsList->toArray();
                $free_goods_ids = array_column($goodsList, 'id');

                $myDataArr = [
                    'loginUser'        => $loginUser,
                    'goodsIdsArr'      => $goodsIdsArr,//提交的数据
                    'freeBuyNum'       => $total_num,//本次购买总数
                    'has_buy_free_num' => $has_buy_free_num,//已经购买免费数
                    'goodsIds'         => $goodsIds,//本费不免费的数据
                    'free_goods_ids'   => $free_goods_ids, //限免goods_id
                ];
                $res_rs    = $this->getStaffBuyAuthNew($myDataArr);
                if (isset($res_rs['msg'])) {
                    throw new ValidationException($res_rs['msg']);
                }
            }
            // 检测一下是不是购买超出数量限制
            $canBuy = $this->getUserByNum($_thisByNum, $loginUser, $setting_model);

            if ($canBuy == false && $_thisByNum > 0) {
                throw new ValidationException($this->getTranslation()->_('each_employee_can_only'));
            }
            // 生成订单数据
            $orderStatus = InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE;
            if ($_hasSkuPreSale) {
                $orderStatus = InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE;
            }
            if ($this->checkStaffIsFirstOrder($staffId) && $goodsIdsArr[0]['goods_type'] ==  InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES) {
                $syncWmsPostData['markName'] = 'FirstOrder';
            }

            $add_hour   = $this->getDI()['config']['application']['add_hour'];
            $nowDate    = gmdate('Y-m-d H:i:s', time() + $add_hour * 3600);
            $orderObj   = new InteriorOrdersModel();
            $order_data = [
                'staff_id'              => $staffId,
                'node_department_id'    => $userInfo['node_department_id'] ?: $userInfo['sys_department_id'],//子部门ID
                'staff_name'            => $userInfo['name'],
                'staff_mobile'          => $userInfo['mobile'],
                'staff_store_id'        => $userInfo['sys_store_id'], //员工属性的总部ID或者所属网点ID
                'receive_store_id'      => $storeId,//员工下单时提交过来的收货地址ID（总部地址ID或网点ID）
                'receive_province_name' => $province,
                'receive_city_name'     => $city,
                'receive_district_name' => $district,
                'receive_address'       => $address,
                'receive_postal_code'   => $postalCode,
                'order_code'            => $orderCode,
                'pay_amount'            => $totalPayAmount,
                'pay_method'            => InteriorGoodsPayMethodEnums::PAY_METHOD_DEDUCTION_OF_WAGES_CODE,
                'submit_at'             => $currentTime,
                'order_status'          => $orderStatus,
                'delivery_way'          => 'express',
                'remark'                => "GF_order：【staff_id：{$staffId}】" . $orderRemark,
                'created_at'            => $nowDate,
                'updated_at'            => $nowDate,
                'node_sn'               => $nodeSn,
                'mach_code'             => env('wms_mchId'),
            ];

            $success = $db->insertAsDict(
                $orderObj->getSource(),
                $order_data
            );
            if ($success === false) {
                throw new BusinessException('订单提交 - interior_orders 写入失败, data = ' . json_encode($order_data, JSON_UNESCAPED_UNICODE), ErrCode::DATABASE_ERROR);
            }
            $orderObj->id                          = $db->lastInsertId();
            $syncWmsPostData['nodeSn']             = $nodeSn;
            $syncWmsPostData['consigneeName']      = $userInfo['name'];
            $syncWmsPostData['consigneePhone']     = $userInfo['mobile'];
            $syncWmsPostData['province']           = $province;
            $syncWmsPostData['city']               = $city;
            $syncWmsPostData['district']           = $district;
            $syncWmsPostData['postalCode']         = $postalCode;
            $syncWmsPostData['consigneeAddress']   = $address;
            $syncWmsPostData['orderSn']            = $orderCode;
            $syncWmsPostData['node_department_id'] = $order_data['node_department_id'];
            $syncWmsPostData['deliveryWay']        = 'express';
            $syncWmsPostData['goods']              = json_encode($goods, JSON_UNESCAPED_UNICODE);
            $syncWmsPostData['remark']             = "GF_order：【staff_id：{$staffId}】" . $orderRemark;
            $syncWmsPostData['lang']               = $this->lang;

            // 向wms同步出库订单,如果有预售就不往仓储同步
            $_orderObj = InteriorOrdersModel::findFirst([
                'conditions' => 'id = ' . $orderObj->id,
            ]);

            if (!$_hasSkuPreSale) {
                $syncWarehoseServer = new SyncWarehoseServer();
                $res                = $syncWarehoseServer->syncAddOrderToWmsReturnWarehouseAdd($syncWmsPostData);
                if (empty($res) || $res['code'] != 1 || !$res['data']) {
                    $_orderObj->fail_num    = 1;
                    $_orderObj->out_status  = 2;//出库单失败，但有可能是超时失败，后续重试
                    $_orderObj->fail_reason = $res['msg'] ?? '--';
                } else {
                    $_orderObj->out_sn = $res['data'];
                    $_orderObj->remark = $_orderObj->remark . "【wms_out_sn：{$res['data']}】";
                }
            }
            $_orderObj->is_audited   = $_orderObj->is_audited ?? 0;
            $_orderObj->audited_desc = $_orderObj->audited_desc ?? '';
            $_orderObj->out_sn       = $_orderObj->out_sn ?? '';
            if ($_orderObj->save() === false) {
                throw new BusinessException('订单提交interior_orders 修改失败, data = ' . json_encode($_orderObj->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::DATABASE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $this->wLog('submitOrder', $e->getFile()
                . 'line' . $e->getLine()
                . 'message' . $e->getMessage()
                . 'trace' . $e->getTraceAsString(), "warning");
            return [
                'msg' => $e->getMessage(),
            ];
        } catch (BusinessException $e) {
            $db->rollback();
            $this->wLog('submitOrder', $e->getFile()
                . 'line' . $e->getLine()
                . 'message' . $e->getMessage()
                . 'trace' . $e->getTraceAsString(), 'warning');
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_creation_failed),
            ];
        } catch (\Exception $e) {
            $db->rollback();
            $this->wLog('submitOrder', $e->getFile()
                . 'line' . $e->getLine()
                . 'message' . $e->getMessage()
                . 'trace' . $e->getTraceAsString(), 'error');
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_creation_failed),
            ];
        }
        $payMethodTxt = InteriorGoodsPayMethodEnums::getCodeTxtMap();
        return [
            'orde_code'            => $orderCode,
            'total_pay_amount'     => $totalPayAmount,
            'total_pay_amount_fmt' => InteriorGoodsEnums::fmtAmount($totalPayAmount, $this->lang),
            'pay_method'           => InteriorGoodsPayMethodEnums::PAY_METHOD_DEDUCTION_OF_WAGES_CODE,
            'pay_method_txt'       => $payMethodTxt[InteriorGoodsPayMethodEnums::PAY_METHOD_DEDUCTION_OF_WAGES_CODE],
            'order_created_at'     => $currentTime,
        ];
    }

    /**
     * 出库单接口中nodeSn传参-取值逻辑
     * @param integer $goodsType 订单类型 1 工服 2 无头件
     * @param array $userInfo 员工信息组
     * @param int|string $receiveStoreId 收货地址，总部地址ID或网点ID
     * @return array|false|string|string[]
     * @throws ValidationException
     */
    public function getNodeSn($goodsType, $userInfo, $receiveStoreId)
    {
        if ($goodsType == InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
            //21466-订单类型为无头件,收货地址非总部地址，则取收货地址的网点编码
            if (!is_numeric($receiveStoreId)) {
                $nodeSn = $receiveStoreId;
            } else {
                //如果收货地址为总部地址,取配置的无头件成本中心
                $nodeSn = (new SettingEnvServer())->getSetVal('interior_order_unclaimed_node_sn');
            }
        } else {
            if ($userInfo['sys_store_id'] != -1) {
                // 网点员工传入网点编号
                $nodeSn = $userInfo['sys_store_id'];
            } else {
                // 总部员工传入成本中心,获取费用所属中心 先用子部门查 没有的话 拿顶级部门
                $pcCode = SysDepartmentPcCode::findFirst("department_id = {$userInfo['node_department_id']}");
                if (empty($pcCode)) {
                    $pcCode = SysDepartmentPcCode::findFirst("department_id = {$userInfo['sys_department_id']}");
                }
                $nodeSn = empty($pcCode) ? '' : $pcCode->pc_code;
            }
        }
        if (!$nodeSn) {
            throw new ValidationException(LangEnums::getTranslation($this->lang, ReturnMsgEnums::NO_PC_CODE_FOUND));
        }
        return $nodeSn;
    }

    /**
     *生成订单号
     * Created by: Lqz.
     * @return mixed|string
     * CreateTime: 2020/8/9 0009 22:17
     */
    public function generateOrderCode()
    {
        // 长度20
        $orderCode  = 'GF' . date('YmdHis') . mt_rand(1000, 9999);
        $conditions = "order_code = :order_code:";
        $bind       = ["order_code" => $orderCode];
        $order      = InteriorOrdersModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        if ($order) {
            $orderCode = $this->generateOrderCode();
        }
        return $orderCode;
    }

    /**
     * 获取单个sku数据
     * Created by: Lqz.
     * @param $skuId
     * @return mixed
     * CreateTime: 2020/8/10 0010 14:47
     */
    public function getGoodsSkuInfo($goodsId, $skuId, $forUpdate = false)
    {
        $conditions = "id = :skuId: and status=:status: and goods_id = :goodsId:";
        $bind       = ["goodsId" => $goodsId, "skuId" => $skuId, "status" => 1];
        $skuObj     = InteriorGoodsSkuModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            "for_update" => $forUpdate,
        ]);
        return $skuObj;
    }

    /**
     * 获取订单列表
     * Created by: Lqz.
     * @param array $loginUser
     * @param array $params
     * @return array
     * CreateTime: 2020/8/10 0010 14:43
     */
    public function getOrderList(array $loginUser, array $params)
    {
        $page         = $params['page'];
        $limit        = $params['limit'];
        $staffId      = $loginUser['staff_id'];
        $conditions   = "staff_id = :staffId: ";
        $bind         = ["staffId" => $staffId];
        $orderListObj = InteriorOrdersModel::find(
            array(
                'conditions' => $conditions,
                'bind'       => $bind,
                'order'      => 'id desc',
                'limit'      => $limit,
                'offset'     => ($page - 1) * $limit,
            )
        );
        $orderList    = $orderListObj->toArray();
        if ($orderList) {
            $orderCodesArr     = array_column($orderList, 'order_code');
            $orderGoodsSkusObj = InteriorOrdersGoodsSkuModel::find([
                'conditions' => 'order_code in({order_code:array})',
                'bind'       => ['order_code' => $orderCodesArr],
                'order'      => 'id desc',
            ]);
            $orderGoodsSkus    = $orderGoodsSkusObj->toArray();
            // 查找每个订单下面的全部商品
            $orderStatusEnums = InteriorOrderStatusEnums::getCodeTxtMap($this->lang);
            $auditedEnums     = InteriorOrderAuditedEnums::getCodeTxtMap($this->lang);
            foreach ($orderList as &$order) {
                $order['order_status_txt'] = $orderStatusEnums[$order['order_status']];
                $order['is_audited_txt']   = $auditedEnums[$order['is_audited']];
                $order['pay_amount_fmt']   = InteriorGoodsEnums::fmtAmount($order['pay_amount'], $this->lang);
//                $order['created_at']       = $order['created_at'];


                $_orderCode = $order['order_code'];
                foreach ($orderGoodsSkus as $orderGoodsSku) {
                    if ($orderGoodsSku['order_code'] == $_orderCode) {
                        $orderGoodsSku['buy_price_fmt']   = InteriorGoodsEnums::fmtAmount($orderGoodsSku['buy_price'], $this->lang);
                        $orderGoodsSku['pay_amount_fmt']  = InteriorGoodsEnums::fmtAmount($orderGoodsSku['pay_amount'], $this->lang);
                        $orderGoodsSku['show_goods_name'] = self::convertGoodsNameByLang($this->lang, $orderGoodsSku);

                        $order['goods_skus'][] = $orderGoodsSku;
                    }
                }
            }
        }
        $count    = InteriorOrdersModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        $pageData = [
            'order_list'   => $orderList,
            'current_page' => $page,
            'count'        => $count,
            'limit'        => $limit,
        ];
        return $pageData;
    }

    /**
     * 获取订单支付状态
     *
     * @param array $loginUser
     * @param array $params
     * @return array
     * @throws ValidationException
     */
    public function getOrderStatus(array $loginUser, array $params)
    {
        $orderCode = $params['order_code'];
        $staffId   = $loginUser['staff_id'];
        $orderObj  = $this->getStaffOrder($staffId, $orderCode);
        if (!$orderObj) {
            throw new ValidationException($this->getTranslation()->_('interior_orders_null', ErrCode::VALIDATE_ERROR));
        }

        return [
            'order_status' => $orderObj->order_status,
        ];
    }

    /**
     * 获取订单
     * Created by: Lqz.
     * @param array $loginUser
     * @param array $params
     * CreateTime: 2020/8/10 0010 15:08
     */
    public function getOrderInfo(array $loginUser, array $params)
    {
        $orderCode = $params['order_code'];
        $staffId   = $loginUser['staff_id'];
        $orderObj  = $this->getStaffOrder($staffId, $orderCode);
        if (!$orderObj) {
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_was_not_found),
            ];
        }
        $payMethodMap              = InteriorGoodsPayMethodEnums::payMethod($this->lang);
        $orderSkusBoj              = $orderObj->getOrdersGoodsSku();
        $order                     = $orderObj->toArray();
        $orderSkus                 = $orderSkusBoj->toArray();
        $order['pay_method_name']  =  $payMethodMap[$order['pay_method']];
        $orderStatusEnums          = InteriorOrderStatusEnums::getCodeTxtMap($this->lang);
        $auditedEnums              = InteriorOrderAuditedEnums::getCodeTxtMap($this->lang);
        $order['order_status_txt'] = $orderStatusEnums[$order['order_status']];
        $order['is_audited_txt']   = $auditedEnums[$order['is_audited']];
        $order['pay_amount_fmt']   = InteriorGoodsEnums::fmtAmount($order['pay_amount'], $this->lang);
        $order['goods_skus']       = null;
        $wrs_server = new WmsServer($this->lang,$this->timeZone);
        $wrs_server = Tools::reBuildCountryInstance($wrs_server, [$this->lang, $this->timezone]);
        foreach ($orderSkus as $orderGoodsSku) {
            $orderGoodsSku['show_goods_name'] = self::convertGoodsNameByLang($this->lang, $orderGoodsSku);
            if ($orderGoodsSku['order_code'] == $order['order_code']) {
                $orderGoodsSku['buy_price_fmt']  = $wrs_server->format_amount($orderGoodsSku['buy_price']);
                $orderGoodsSku['pay_amount_fmt'] = $wrs_server->format_amount($orderGoodsSku['pay_amount']);
                $order['goods_skus'][]           = $orderGoodsSku;
            }
        }
        $tracking = '';
        $result = (new SyncWarehoseServer())->outBoundTracking($order['order_code']);
        if ($result && isset($result['expressSn'])) {
            $tracking = $result['expressSn'];
        }

        $order['tracking'] = $tracking;
        return ['order_info' => $order];
    }

    /**
     *取消订单
     * Created by: Lqz.
     * @param array $loginUser
     * @param array $params
     * @return bool|string[]
     * CreateTime: 2020/8/10 0010 19:43
     */
    public function cancelOrder(array $loginUser, array $params, $remark = 'staff_cancel_order')
    {
        $orderCode = $params['order_code'];
        $staffId   = $loginUser['staff_id'];
        $db        = InteriorOrdersModel::beginTransaction($this);
        try {
            $orderObj = $this->getStaffOrder($staffId, $orderCode, true);
            if (!$orderObj) {
                return [
                    'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_was_not_found),
                ];
            }
            if ($orderObj->is_audited != InteriorOrderAuditedEnums::IS_AUDITED_WARITING_CODE || !in_array($orderObj->order_status, [InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE, InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE])) {
                $db->rollback();
                return [
                    'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_can_not_cancel),
                ];
            }
            $orderStatus = $orderObj->order_status;
            // 取消订单
            $orderObj->order_status = InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE;
            $date                   = date('Y-m-d H:i:s');
            $orderObj->remark       = $orderObj->remark . "【{$remark}:{$date}】";
            $orderObj->cancel_reason = $params['cancel_reason'] ?? InteriorOrderStatusEnums::ORDER_CANCEL_REASON_SELF;
            $orderObj->canceled_at = $date;
            $orderObj->updated_at = $date;
            $orderObj->save();
            // 回滚订单goods_sku库存
            $orderSkusObj = $orderObj->getOrdersGoodsSku();
            $orderSkusArr = $orderSkusObj->toArray();
            foreach ($orderSkusArr as $orderSku) {
                $goodsSkuObj           = $this->getGoodsSkuInfo($orderSku['goods_id'], $orderSku['goods_sku_id'], true);
                if(!$goodsSkuObj){
                    continue;
                }
                $toSurplusNum          = $goodsSkuObj->surplus_num + $orderSku['buy_num'];
                $_saleNum              = ($goodsSkuObj->sale_num - $orderSku['buy_num']);
                $goodsSkuObj->sale_num = $_saleNum >= 0 ? $_saleNum : 0;
                // 记录库存修改日志
                self::saveSkuSurplusNumAndLog($goodsSkuObj, $staffId, $orderSku['buy_num'], $toSurplusNum, $remark);
            }
            // 预售单取消订单不需要同步,为了保险起见
            if ($orderStatus == 2) {
                $db->commit();
                $syncData           = [
                    'orderSn' => $orderCode,
                    'lang'    => $this->lang,
                ];
                $syncWarehoseServer = new SyncWarehoseServer();
                $wmsRes             = $syncWarehoseServer->syncCancelOrderToWmsCancelOutbound($syncData);
            } else {
                $syncData           = [
                    'orderSn' => $orderCode,
                    'lang'    => $this->lang,
                ];
                $syncWarehoseServer = new SyncWarehoseServer();
                $wmsRes             = $syncWarehoseServer->syncCancelOrderToWmsCancelOutbound($syncData);
                if ($wmsRes['code'] == 1) {
                    $db->commit();
                } else {
                    $db->rollback();
                    return ['msg' => "取消订单失败【{$wmsRes['msg']}】", 'data' => $wmsRes];
                }
            }
        } catch (\Exception $e) {
            $db->rollback();
            $this->wLog('cancelOrder', $e->getMessage());
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_cancel_error),
            ];
        }
        return true;
    }

    public function getStaffOrder($staffId, $orderCode, $forUpdate = false)
    {
        $conditions = "staff_id = :staffId: and order_code = :orderCode:";
        $bind       = ["staffId" => $staffId, "orderCode" => $orderCode];
        $orderObj   = InteriorOrdersModel::findFirst(
            array(
                'conditions' => $conditions,
                'bind'       => $bind,
                "for_update" => $forUpdate,
            )
        );
        return $orderObj;
    }

    /**
     * 完成收货
     * Created by: Lqz.
     * @param $loginUser
     * @param $params
     * @return bool|string[]
     * CreateTime: 2020/8/10 0010 19:46
     */
    public function saveOrderRecevied($loginUser, $params, $remark = 'staff_received_order')
    {
        $orderCode = $params['order_code'];
        $staffId   = $loginUser['staff_id'];
        $orderObj  = $this->getStaffOrder($staffId, $orderCode);
        if (!$orderObj) {
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_was_not_found),
            ];
        }
        if (!in_array($orderObj->order_status, [InteriorOrderStatusEnums::ORDER_STATUS_SEND_CODE])) {
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_was_delivered),
            ];
        }
        $orderObj->order_status = InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_RECEIVED_CODE;
        $orderObj->received_at  = date('Y-m-d H:i:s');
        $orderObj->remark = $orderObj->remark . "【{$remark}】";
        $res              = $orderObj->save();
        return $res;
    }

    /**
     * 修改sku 库存并记录日志
     * Created by: Lqz.
     * @param InteriorGoodsSkuModel $goodsSkuObj
     * @param $staffId
     * @param $toSurplusNum
     * @param $remark
     * CreateTime: 2020/8/13 0013 11:53
     */
    public static function saveSkuSurplusNumAndLog(InteriorGoodsSkuModel $goodsSkuObj, $staffId, $chNum, $toSurplusNum, $remark)
    {
        if ($goodsSkuObj->surplus_num == $toSurplusNum) {
            return;
        }
        $logObj                   = new InteriorGoodsSkuLogModel();
        $logObj->staff_id         = $staffId;
        $logObj->goods_id         = $goodsSkuObj->goods_id;
        $logObj->goods_sku_id     = $goodsSkuObj->id;
        $logObj->from_surplus_num = $goodsSkuObj->surplus_num;
        $logObj->to_surplus_num   = $toSurplusNum;
        $logObj->current_ch_num   = $chNum;
        $logObj->remark           = $remark;
        $logObj->save();
        // 回滚goods_sku库存
        $goodsSkuObj->surplus_num = $toSurplusNum;
        $goodsSkuObj->save();
    }


    public static function convertGoodsNameByLang(string $lang, array $goods)
    {
        switch ($lang) {
            case LangEnums::LANG_CODE_EN :
                $currentName = $goods['goods_name_en'];
                break;
            case LangEnums::LANG_CODE_ZH_CN :
                $currentName = $goods['goods_name_zh'];
                break;
            default:
                // v13847需求, 若当地语言为空, 则取英文
                $currentName = !empty($goods['goods_name_th']) ? $goods['goods_name_th'] : $goods['goods_name_en'];
        }

        return $currentName;
    }

    public function getUserByNum($thisBuyNum, $loginUser,$setting_model)
    {
        $limit    = $setting_model->getSetVal($code = 'interior_buy_limit_num');
        $orderSkuNum = 0;
        $staffId     = $loginUser['staff_id'];
        $ordesObj    = InteriorOrdersModel::find([
            'conditions' => 'staff_id = :staffId: and order_status not in ({orderStatus:array}) and goods_type = :goods_type: and created_at >= :createdAtLeft: and created_at <= :createdAtRight:',
            'bind'       => [
                'staffId'        => $staffId,
                'orderStatus'    => [InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE, InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE],
                'goods_type' => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES,
                'createdAtLeft'  => date('Y-m-1 00:00:00'),
                'createdAtRight' => date('Y-m-t 23:59:59'),
            ],
            'columns'    => 'order_code',
        ]);
        $orders      = $ordesObj->toArray();
        $codes       = array_column($orders, 'order_code');
        if ($codes) {
            $conditions = 'order_code in ({orderCode:array}) and is_free = 0';
            $orderSkuNum = InteriorOrdersGoodsSkuModel::sum([
                'conditions' => $conditions,
                'bind'       => [
                    'orderCode' => $codes,
                ],
                'column'     => 'buy_num',
            ]);
        }
        $canBuyNum = $limit - $thisBuyNum - $orderSkuNum;
        return $canBuyNum >= 0 ? true : false;
    }

    public function getAddressList($loginUser, $paramIn)
    {
        $goodsType = $paramIn['goods_type'] ?? InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES;
        $userInfo  = HrStaffInfoServer::getUserInfoByStaffInfoId($loginUser['staff_id'], 'sys_store_id');
        if ($userInfo['sys_store_id'] != -1 && $goodsType == InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES) {
            return [
                'msg' => 'You are not a member of the headquarters!',
            ];
        }
        $searchStr  = $paramIn['search_str'];
        $conditions = "(name like :searchStr: or detail_address like :searchStr: ) and state=:state:";
        $bind       = [
            'searchStr' => "%$searchStr%",
            'state'     => 1,
        ];
        //21466 - 无头件时网点表中所有状态为激活并且网点类型不属于配置的禁用网点类型并且网点名称不包含配置的禁用网点名称(全模糊、不区分大小写匹配)
        if ($goodsType == InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
            $settingEnvServer                    = new SettingEnvServer();
            $interiorOrderForbiddenStoreCategory = array_filter($settingEnvServer->getSetVal('interior_order_forbidden_store_category', ','));
            if ($interiorOrderForbiddenStoreCategory) {
                $conditions       .= ' AND category not in ({category:array})';
                $bind ['category'] = $interiorOrderForbiddenStoreCategory;
            }

            $interiorOrderForbiddenStoreName = array_filter($settingEnvServer->getSetVal('interior_order_forbidden_store_name', ','));
            if ($interiorOrderForbiddenStoreName) {
                foreach ($interiorOrderForbiddenStoreName as $key => $item) {
                    $conditions         .= "  AND name not like :name{$key}:";
                    $bind ["name{$key}"] = "%$item%";
                }
            }
        }
        $storeAddress = SysStoreModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => 'id,name,detail_address,province_code,city_code,district_code,postal_code',
            'limit'      => 10,
        ]);

        // 搜索总部地址
        $headquartersAddress = HeadquartersAddressModel::find([
            'conditions' => "office_name like :searchStr: or address like :searchStr:",
            'bind'       => [
                'searchStr' => "%$searchStr%",
            ],
            'columns'    => 'id,office_name as name,province_code,city_code,district_code,address as detail_address,postal_code',
            'limit'      => 10,
        ]);

        $this->getDi()->get('logger')->write_log("总部员工地址列表日志 => " . json_encode(array_merge($headquartersAddress->toArray(),
                $storeAddress->toArray())), 'info');

        return array_merge($headquartersAddress->toArray(), $storeAddress->toArray());
    }

    /**
     * 检查员工是否是首次下单工服
     * @param $staffId
     * @return bool
     */
    private function checkStaffIsFirstOrder($staffId): bool
    {
        $count = InteriorOrdersModel::count([
            'staff_id = :staff_id: AND order_status IN ({order_status:array}) and goods_type = :goods_type: ',
            'bind' => [
                'staff_id' => $staffId,
                'order_status' => [
                    InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_SEND_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_RECEIVED_CODE,
                ],
                'goods_type' => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES,
            ],
        ]);
        $this->wLog('checkStaffIsFirstOrder', '工号:' . $staffId . '是否首次下单count:' . $count);
        if ($count == 0) {
            return true;
        }
        return false;
    }

   public  static function  getStaffStoreCateEnableGoods()
    {
        $goods_cate = SysStoreGoodsModel::find();
        $goods_cate = $goods_cate->toArray();

        foreach ($goods_cate as &$item){
            $item['store_cate_id'] = explode(",",$item['store_cate_id'])??'';
            $item['goods_id'] =explode(",",$item['goods_id'])??'';
        }

        return $goods_cate;
    }

    public function getCodeTxtMap()
    {
        $goodsCates = self::getStaffStoreCateEnableGoods();
        $goodsCates = array_column($goodsCates,'store_cate_id','sys_store_cate')??[];
        return $goodsCates;

    }
    /**
     * 限免免费数量
     * */
    public function getUserFreeByNum($loginUser,$goodsIds,$whereTime=0)
    {

        $orderSkuNum = 0;
        $staffId     = $loginUser['staff_id'];
        if(empty($whereTime)){
            $ordesObj    = InteriorOrdersModel::find([
                'conditions' => 'staff_id = :staffId: and order_status != :orderStatus: ',
                'bind'       => [
                    'staffId'        => $staffId,
                    'orderStatus'    => InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE,
                ],
                'columns'    => 'order_code',
            ]);

        }else{
            $date = date("Y-m-d");
            $staTime = date('Y-m-d 00:00:00',strtotime($date));
            $endTime = date('Y-m-d 23:59:59',strtotime("$staTime +1 month -1 day"));
            $ordesObj    = InteriorOrdersModel::find([
                'conditions' => 'staff_id = :staffId: and order_status != :orderStatus: and created_at BETWEEN :staTime: and :endTime:',
                'bind'       => [
                    'staffId'        => $staffId,
                    'orderStatus'    => InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE,
                    'staTime' => $staTime,
                    'endTime' => $endTime,
                ],
                'columns'    => 'order_code',
            ]);
        }


        $orders      = $ordesObj->toArray();
        $codes       = array_column($orders, 'order_code');
        if ($codes) {
            $orderSkuNum = InteriorOrdersGoodsSkuModel::find([
                'conditions' => 'order_code in ({orderCode:array}) and goods_id in ({goodsId:array}) and is_free =1  group by goods_id',
                'bind'       => [
                    'orderCode' => $codes,
                    'goodsId'=>$goodsIds,
                ],
                'columns'     => 'goods_id,sum(buy_num) as buy_nums',
            ])->toArray();
        }
        $order_sku_num = [];
        if (!empty($orderSkuNum)) {
            $order_sku_num = array_column($orderSkuNum, 'buy_nums', 'goods_id');
        }

        return $order_sku_num;
    }


    public   function  getStaffStoreCateFreeGoods()
    {
        $goods_cate = SysStoreGoodsModel::find();
        $goods_cate = $goods_cate->toArray();

        foreach ($goods_cate as &$item){
            $item['store_cate_id'] = explode(",",$item['store_cate_id'])??'';
            $item['free_goods_id'] =explode(",",$item['free_goods_id'])??'';
        }

        return $goods_cate;
    }

    /**
     * 获取员工限免商品类别
     * @param array $userInfo
     * @return array|string[]
     */
    public  function getUserEnableFreeGoodsCate(array $userInfo)
    {
        $storeCateCode = HrStaffInfoServer::getStaffStoreCateCode($userInfo);
        (new BaseServer())->wLog('StoreCateCode', $storeCateCode);

        if(isset($storeCateCode['msg'])){
            return $storeCateCode;
        }
        $goods_ids =  (new InteriorGoodsRepository())->getInteriorGoodsStoreCateRelAll($storeCateCode);
        return  $goods_ids;
    }


    /**
     * 新需求逻辑变更 需求11533 https://l8bx01gcjr.feishu.cn/docs/doccnXPQF9RegncaEhbOH63aUS0
     * 限免免费数量总和
     * */
    public function getUserFreeByNumNew($loginUser)
    {
        $order_sku_num = 0;
        $staffId       = $loginUser['staff_id'];
        $orders_obj    = InteriorOrdersModel::find([
            'conditions' => 'staff_id = :staffId: and order_status not in ({orderStatus:array}) and goods_type = :goods_type: ',
            'bind'       => [
                'staffId'     => $staffId,
                'orderStatus' => [InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE, InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE],
                'goods_type' => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES,
            ],
            'columns'    => 'order_code',
        ])->toArray();

        if (empty($orders_obj)) {
            return $order_sku_num;
        }
        $codes = array_values(array_unique(array_column($orders_obj, 'order_code')));
        if (!empty($codes)) {
            $order_sku_obj = InteriorOrdersGoodsSkuModel::findFirst([
                'conditions' => 'order_code in ({orderCode:array})  and is_free =1',
                'bind'       => [
                    'orderCode' => $codes,
                ],
                'columns'    => 'sum(buy_num) as buy_nums',
            ]);
            if (!empty($order_sku_obj)) {
                $order_sku_num = $order_sku_obj->toArray();
            }
        }
        return $order_sku_num['buy_nums'] ?? 0;
    }

    public function goodsSize($goods_id)
    {
        $country_code = strtoupper(env('country_code', 'Th'));
        $res          = [];
        switch ($country_code) {
            case 'PH':
                $res = PhInteriorGoodsEnums::getGoodsSize($goods_id, $this->lang);

                break;
            case 'LA':
                $res = LaInteriorGoodsEnums::getGoodsSize($goods_id, $this->lang);

                break;
            case 'MY':
                $res = MyInteriorGoodsEnums::getGoodsSize($goods_id, $this->lang);

                break;
            default:
                $res = InteriorGoodsEnums::getGoodsSize($goods_id, $this->lang);
                break;

        }
        return $res;

    }

    /**
     * 在向wms同步库存的时候需要获取总部或网点的最新省、市、区、邮编信息
     * @param array $orderData 物料订单/资产订单信息组
     * @return array
     */
    public function getOrderNewAddressInfo($staff_store_id)
    {
        $province = [];
        $city = [];
        $district = [];
        $postal_code = '';

        $wmsRepository = new WmsRepository();
        if (is_numeric($staff_store_id)) {
            //如果此值是个数字则表示总部
            $new_order_address = $this->getHeadquartersAddress($staff_store_id);
        } else {
            //代表网点
            $new_order_address = $wmsRepository->userStoreInfo($staff_store_id);
        }
        if ($new_order_address) {
            //获取新地址的省、市、区、信息
            $province = $wmsRepository->pdcInfo($new_order_address['province_code'], 1);
            $city = $wmsRepository->pdcInfo($new_order_address['city_code'], 2);
            $district = $wmsRepository->pdcInfo($new_order_address['district_code'], 3);
            //如果网点邮编为空，取得市区邮编
            $district_postal_code = $district['postal_code'] ? explode(',', $district['postal_code']) :
                [$new_order_address['postal_code']];
            $postal_code = $district_postal_code ? $district_postal_code[0] : '';
        }

        return [$province, $city, $district, $postal_code, $new_order_address['detail_address'] ?? ''];
    }

    /**
     * 获取当前国家的总部数据
     * @Date: 2022-03-05 15:44
     * @author: wangqi
     * @return:
     **/
    public function getHeadquartersAddress($staff_store_id){

        $headOffice = HeadquartersAddressModel::findFirst([
            'columns' => "office_name as name,address detail_address,province_code,city_code,postal_code,district_code",
            'conditions' => "id = :id:",
            'bind' => [
                'id'  => $staff_store_id,
            ],
            'limit' =>1,
            'order'      => 'id asc',
        ]);

        if(!empty($headOffice)){
            return  $headOffice->toArray();
        }else{
            return [];
        }
    }

    /**
     * 提交订单权限校验
     * @param array $user 登录人信息
     * @param array $params 提交的数据
     * @throws ValidationException
     **/
    public function authorityCheck(array $user, array $params)
    {
        $goods_ids = array_values(array_unique(array_column($params['buy_goods_ids_arr'], 'goods_id')));
        $goods_free_arr = array_column($params['buy_goods_ids_arr'], null, 'goods_id');

        //获取商品列表
        $goods_arr = InteriorGoodsModel::find([
            'conditions' => 'id in({id:array})',
            'bind' => ['id' => $goods_ids],
        ])->toArray();

        //校验免费权限
        $is_free = array_column($params['buy_goods_ids_arr'], 'is_free');
        if (in_array(self::IS_FREE, $is_free)) {
            //提交过来的免费商品有不是免费的 || 商品设置了免费雇佣类型 && 员工的雇佣类型不在设置里的不可提交
            foreach ($goods_arr as $goods_info) {
                if ($goods_info['is_free'] != $goods_free_arr[$goods_info['id']]['is_free'] || ($goods_info['free_hire_type'] && !in_array($user['hire_type'], explode(',', $goods_info['free_hire_type'])))) {
                    throw new ValidationException($this->getTranslation()->_('interior_goods_free_type_err'));
                }
            }

            if ((new InteriorGoodsRepository())->isFreeManageRegion($user)) {
                ///校验免费商品必须在非免费大区里面
                throw new ValidationException($this->getTranslation()->_('interior_user_not_at_free_manage_region'));
            }

            //判断免费的商品必须在类型和部门里面
            $free_goods_ids = (new InteriorGoodsRepository())->getFreeGoodsIdsAll($user);
            foreach ($params['buy_goods_ids_arr'] as $info) {
                if ($info['is_free'] == self::IS_FREE && !in_array($info['goods_id'], $free_goods_ids)) {
                    throw new ValidationException($this->getTranslation()->_('interior_goods_free_manage_department_permission_not'));
                }
            }
        }

        //检验购买权限
        //商品设置了购买雇佣类型 && 员工的雇佣类型不在设置里的不可提交
        foreach ($goods_arr as $goods_info) {
            if ($goods_info['buy_hire_type'] && !in_array($user['hire_type'], explode(',', $goods_info['buy_hire_type']))) {
                throw new ValidationException($this->getTranslation()->_('interior_not_store_cate_exist_cate_goods'));
            }
        }

        //存在下线的商品
        $goods_sku_ids = array_values(array_unique(array_column($params['buy_goods_ids_arr'], 'goods_sku_id')));
        if (!empty($goods_sku_ids)) {
            $goods_sku_arr = InteriorGoodsSkuModel::find([
                'conditions' => 'id in({id:array})',
                'bind' => ['id' => $goods_sku_ids],
            ])->toArray();
            if (!empty($goods_sku_arr)) {
                $status_arr = array_column($goods_sku_arr, 'status');
                if (in_array(InteriorGoodsStatusEnums::GOODS_STATUS_DISABLED, $status_arr)) {
                    //存在下线的商品
                    throw new ValidationException($this->getTranslation()->_('interior_exist_online_goods'));
                }
            }
        }

        //判断购买的商品是否在购买权限设置的网点类型、部门组里
        $intersect_goods_ids = (new InteriorGoodsRepository())->getIsBuyAndDepartmentSet($user) ?? [0];
        foreach ($goods_ids as $goods_id) {
            if (!in_array($goods_id, $intersect_goods_ids)) {
                throw new ValidationException($this->getTranslation()->_('interior_not_store_cate_exist_cate_goods'));
            }
        }
    }


    /**
     * 购物车删除
     * @param array $user 登录人信息
     * @param array $params 提交的数据
     * @throws ValidationException
     **/
    public function delShoppingCart(array $user, array $params)
    {
        $goods_sku_ids           = array_values(array_unique(array_column($params['buy_goods_ids_arr'], 'goods_sku_id')));
        $cart_goods_sku_list     = InteriorStaffsShoppingCartModel::find(
            [
                'conditions' => 'staff_id = :staffId: and goods_sku_id in ({goods_sku_id:array}) ',
                'bind'       => ['staffId' => $user['staff_info_id'], 'goods_sku_id' => $goods_sku_ids],
            ]);
        $cart_goods_sku_list_arr = $cart_goods_sku_list->toArray();
        if ($cart_goods_sku_list_arr) {
            foreach ($cart_goods_sku_list as $cart_goods) {
                if ($cart_goods->delete() === false) {
                    //您当前所在网点类型不允许下单此类型商品，请更换商品后再下单
                    throw new ValidationException($this->getTranslation()->_('interior_staffs_shopping_cart_del_err'));
                }
            }
        }

    }


    /**
     * 员工商城 共用枚举
     **/
    public function getShopEnums()
    {
        //工服、无头件
        $goods_type_item = $pay_method = [];
        foreach (InteriorGoodsEnums::$goods_type as $type_value => $type_lang_key) {
            $goods_type_item[] = [
                'value' => $type_value,
                'label' => $this->getTranslation()->_($type_lang_key),
            ];
        }
        //支付方式
        $unclaimed_pay_list = InteriorGoodsPayMethodEnums::unclaimedPayMethod($this->lang);
        foreach ($unclaimed_pay_list as $pay_value => $pay_lang_key) {
            $pay_method[] = [
                'value' => $pay_value,
                'label' => $this->getTranslation()->_($pay_lang_key),
            ];
        }

        $res = [
            'goods_type' => $goods_type_item,
            'pay_method_list' => $pay_method,
        ];
        return ['data' => $res];

    }

    /**
     * 根据订单 显示订单数据 支付银行
     * @param array $params  条件
     * @return array
     */
    public function getOrderByBank( array $params)
    {
        $order     = [];
        $orderObj  = $this->getStaffOrder($params['staff_id'], $params['order_code']);
        if (!$orderObj) {
            throw new ValidationException(ReturnMsgEnums::The_order_was_not_found);
        }
        $setting_env                   = new SettingEnvServer();
        $bank_list                     = json_decode($setting_env->getSetVal('interior_goods_bank_list'), true);
        $order['order_code']           = $orderObj->order_code;
        $order['order_created_at']     = $orderObj->created_at;
        $order['pay_method']           = $orderObj->pay_method;
        $order['goods_type']           = $orderObj->goods_type;
        $order['total_pay_amount']     = $orderObj->pay_amount;
        $order['total_pay_amount_fmt'] = InteriorGoodsEnums::fmtAmount($orderObj->pay_amount, $this->lang);
        $order['bank_list']            = $bank_list ?? [];
        return $order;
    }


    /**
     * 查看路由
     * @param array $params 条件
     * @return array
     */
    public function getOutboundTrackingInfo(array $params)
    {
        if (empty($params['order_code'])) {
            return [];
        }
        $data['no'] = $params['order_code'];
        $orders     = InteriorOrdersModel::findFirst([
            'conditions' => 'order_code = :order_code:',
            'bind'       => [
                'order_code' => $params['order_code'],
            ],
        ]);
        if (empty($orders->out_sn)) {
            return [];
        }
        $tracking_info['code'] = 0;
        try {
            $cnf_warehose_server = new SyncWarehoseServer();
            $postData          = ['outSn' => $orders->out_sn];
            $postData['lang']  = $this->lang;
            $postData['mchId'] = $orders->mach_code;
            $tracking_info['data']['status_name'] = '';
            $order_status = $cnf_warehose_server->syncOrderStatusFromWmsGetOutboundOrderStatus($postData, false);
            if (!empty($order_status['data']['status'])) {
                if (in_array($order_status['data']['status'], [InteriorOrderStatusEnums::SCM_DELIVERY_DELIVERED, InteriorOrderStatusEnums::SCM_DELIVERY_COMPLETED])) {
                    $data['mach_code'] = $orders->mach_code;
                    $tracking_info     = (new MaterialWmsServer($this->lang))->getOutboundTrackingInfo($data, 'get_outbound_tracking_info');

                } else {
                    $tracking_info['code'] = 1;
                    $tracking_info['data']['outSn'] = $orders->out_sn;
                    $tracking_info['data']['status'] = '';
                    $tracking_info['data']['orderSn'] = $params['order_code'];
                    $tracking_info['data']['expressCompany'] = '';
                    $tracking_info['data']['expressSn'] = '';
                    $tracking_info['data']['trackingInfo'] = [];
                }
                $tracking_info['data']['status_name'] = $this->getTranslation()->_(InteriorOrderStatusEnums::$delivery_status[$order_status['data']['status']]);
            }
        } catch (\Exception $e) {
            //查询出库状态失败 ，捕获网络超时获取，可忽略，所以用日志info
            $this->wLog('sync_order_status_from_wms_get_out_bound_order_status', $e->getMessage(), 'info');
        }

        //前端不显示错误信息
        if ($tracking_info['code'] != 1) {
            $tracking_info['code'] = 1;
            $tracking_info['msg']  = $tracking_info['msg'] ?? 'success';
            $tracking_info['data']  = null;
        }
        return $tracking_info;
    }

    /**
     * 获取可购买免费工服的入职天数[入职%day%天内的员工，仅可免费购买1件工服。]
     * @param object $userInfo 员工信息组
     * @return int
     */
    public function getCanFreeBuyGoodsEntryDays($userInfo)
    {
        //若是总部，则直接返回7天
        if ($userInfo->sys_store_id == enums::HEAD_OFFICE_ID) {
            return self::$hire_limit_days;
        }

        //获取by端对应oa端部门下的可购买大于1件免费工服天数限制
        $day_item = SettingEnvModel::findFirst([
            'conditions' => 'code = :code:',
            'bind' => ['code' => 'interior_entry_days'],
        ]);
        if (empty($day_item) || empty($day_item->set_val)) {
            return self::$hire_limit_days;
        }

        //获取oa端指定网点一级部门id配置
        $item = OASettingEnvModel::findFirst([
            'conditions' => 'code = :code:',
            'bind' => ['code' => 'appoint_store_by_department_id'],
        ]);
        if (empty($item) || empty($item->val)) {
            return self::$hire_limit_days;
        }

        //获取员工所属部门所在的一级部门ID
        $sys_department_id = (new SysDepartmentServer())->searchSysDeptId($userInfo->node_department_id);
        if (!$sys_department_id) {
            return self::$hire_limit_days;
        }

        //找到一级部门ID后，找到部门对应的配置天数即可
        $department_arr = json_decode($item->val, true);
        $department_key = array_search($sys_department_id, $department_arr);
        $day_arr = json_decode($day_item->set_val, true);
        if ($department_key === false) {
            return $day_arr['other'] ?? self::$hire_limit_days;
        }
        return $day_arr[$department_key] ?? ($day_arr['other'] ?? self::$hire_limit_days);
    }

    /**
     * 根据订单提交时间获取订单超时未支付的过期时间
     * @param string $submit_at 订单提交时间
     * @return false|string
     */
    public function getOrderExpireTime($submit_at)
    {
        return date("Y-m-d H:i:s", strtotime("+".InteriorOrderStatusEnums::ORDER_EXPIRE_HOUR." hour", strtotime($submit_at)));
    }

    /**
     * 调取Flash Pay在线支付创建H5收银台订单接口
     *
     * @param array $create_order_param ['staff_id'=>'商户用户ID', 'order_code'=>'商户订单号',
     * 'order_submit_at'=>'商户订单时间','order_expire_at'=>'订单过期时间', 'order_pay_amount'=>'商户订单金额','goods_flash_pay'=>'商品明细组']
     *
     * @param string $payment_method FlashPay支付方式: h5-收银台支付(默认), qrcode-扫码付
     * @return bool|mixed|string
     * @throws BusinessException
     */
    public function getFlashPayCreateOrderResult(array $create_order_param, string $payment_method = '')
    {
        // 通用参数
        $countryCode = ucfirst(strtolower(env('country_code', 'Th')));
        $cur = enums::$country_conf[$countryCode]['currency_iso'];
        $flash_pay_create_order = [
            'outUserId' => (string)$create_order_param['staff_id'],//商户用户ID
            'outTradeNo' => $create_order_param['order_code'],//商户订单号
            'outTradeTime' => $create_order_param['order_submit_at'],//商户订单时间
            'paymentAmount' => intval(bcmul($create_order_param['order_pay_amount'], 100)),//商户订单金额(最小单位)*100
            'cur' => $cur,//币种泰国THB,马来西亚：MYR
            'subject' => $this->getTranslation()->_('flash_pay_order_title'),//订单标题
            'body' => $this->getTranslation()->_('flash_pay_order_desc'),//订单描述
            'expireTime' => $create_order_param['order_expire_at'],//商户订单过期时间,下单时间+1个小时没有付款的话，自动关闭
            'notifyUrl' => env('by_rpc') . '/pay/Interiorgoods/payTradeNoSync',//异步回调地址
            'operatorNo' => "10000",//操作员工号,默认10000
            'goodsDetails' => $create_order_param['goods_flash_pay'],//商品明细
        ];

        // 扫码付
        if ($payment_method == InteriorOrderStatusEnums::FLASH_PAY_METHOD_QRCODE) {
            $create_payment_api = '/upay/create-qrcode-payment';

            // QR类型 1-P2P; 2:P2M
            $flash_pay_create_order['transQrType'] = '2';
        } else {
            // H5支付
            $create_payment_api = '/upay/cashier/precreate-payment';

            // 同步回调地址
            $flash_pay_create_order['returnUrl'] = env('by_rpc') . '/pay/Interiorgoods/payTradeSync/' . $this->lang . '/' . $create_order_param['platform'];
        }

        $flash_pay_config = $this->getFlashConfig($create_order_param['goods_type'], $create_order_param['mach_code']);

        $flash_pay_server = new FlashPayServer($this->lang);
        return $flash_pay_server->createOrder($flash_pay_create_order, $flash_pay_config, $create_payment_api);
    }

    /**
     * FlashPay支付
     * @param array $loginUser 当前登录者信息组
     * @param array $params 支付参数组
     * @return array
     */
    public function payOrder($loginUser, $params)
    {
        try {
            $orderCode = $params['order_code'];
            $platform = $params['platform'] ?? '';
            $staffId   = $loginUser['staff_id'];
            $orderObj  = $this->getStaffOrder($staffId, $orderCode);
            if (!$orderObj) {
                return [
                    'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_was_not_found),
                ];
            }

            //非FlashPay在线支付的订单||非待付款状态的订单 || 是付款状态的订单但订单时间超过1小时的均不可付款
            if ($orderObj->pay_method != InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE
                || $orderObj->order_status != InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE
                || ($orderObj->order_status == InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE && strtotime($orderObj->order_expire_at) < time())) {
                return [
                    'msg' => LangEnums::getTranslation($this->lang, 'order_status_paid'),
                ];
            }
            //获取订单商品列表
            $orderSkusBoj = $orderObj->getOrdersGoodsSku();
            if (!$orderSkusBoj) {
                return [
                    'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_goods_skus_was_not_found),
                ];
            }
            //用于向FlashPay在线支付传递的商品明细组
            $goods_flash_pay = [];
            foreach ($orderSkusBoj as $goodsSkuObj) {
                //用于向FlashPay在线支付传递的参数
                $goods_flash_pay[] = [
                    'goodsId' => $goodsSkuObj->goods_sku_code,//商品的barcode
                    'goodsName' => $goodsSkuObj->goods_name_en,//商品的名称
                    'quantity' => intval($goodsSkuObj->buy_num),//商品数量
                    'price' => intval($goodsSkuObj->buy_price * 100),//商品的单价*100
                ];
            }

            $flash_pay_method = InteriorOrderStatusEnums::FLASH_PAY_METHOD_H5;

            //调取FlashPay创建收银台接口
            $create_order_param = [
                'staff_id'         => $staffId,
                'order_code'       => $orderCode,
                'order_submit_at'  => $orderObj->submit_at,
                'order_expire_at'  => $orderObj->order_expire_at,
                'order_pay_amount' => $orderObj->pay_amount,
                'goods_flash_pay'  => $goods_flash_pay,
                'platform'         => $platform,
                'goods_type'       => $orderObj->goods_type,
                'mach_code'        => $orderObj->mach_code,
            ];
            $flash_pay_create_order_result = $this->getFlashPayCreateOrderResult($create_order_param, InteriorOrderStatusEnums::FLASH_PAY_METHOD_H5);
            if ($flash_pay_create_order_result['code'] == 0) {
                $flash_pay_data = $flash_pay_create_order_result['data'];
                $orderObj->flash_pay_code = !empty($flash_pay_data['tradeNo']) ? $flash_pay_data['tradeNo'] : '';
                $orderObj->save();
            } else {
                throw new ValidationException($flash_pay_create_order_result['message'], $flash_pay_create_order_result['code']);
            }

            return [
                'order_code'           => $orderObj->order_code,
                'total_pay_amount'     => $orderObj->pay_amount,
                'total_pay_amount_fmt' => InteriorGoodsEnums::fmtAmount($orderObj->pay_amount, $this->lang),
                'pay_method'           => $orderObj->pay_method,
                'pay_method_txt'       => InteriorGoodsPayMethodEnums::payMethod()[$orderObj->pay_method],
                'order_created_at'     => $orderObj->submit_at,
                'flash_pay_method'     => $flash_pay_method,
                'flash_pay_data'       => $flash_pay_data,
            ];

        } catch (ValidationException $e) {
            $this->wLog('payOrder', $e->getFile()
                . " code " .$e->getCode()
                . " line " . $e->getLine()
                . " message " . $e->getMessage()
                . " trace " . $e->getTraceAsString());
            return [
                'msg' => $e->getMessage(),//flash pay返回值会有翻译
            ];
        } catch (\Exception $e) {
            $this->wLog('payOrder', $e->getFile()
                . " code " .$e->getCode()
                . " line " . $e->getLine()
                . " message " . $e->getMessage()
                . " trace " . $e->getTraceAsString(), "error");
            return [
                'msg' => LangEnums::getTranslation($this->lang, 'flash_pay_failed'),
            ];
        }
    }

    /**
     * 取消pay支付订单(0交易待支付、4交易失败、5交易关闭可直接取消；2交易处理中不可取消；3交易成功可取消，款项状态=退款中)
     * @param object $orderObj 订单状态
     * @return object
     * @throws \Exception
     */
    public function cancelPayOrder(object $orderObj)
    {
        try {
            $flash_pay_config = $this->getFlashConfig($orderObj->goods_type, $orderObj->mach_code);

            $flash_pay_server = new FlashPayServer($this->lang);
            $pay_result = $flash_pay_server->getPaymentResult(['outTradeNo' => $orderObj->order_code], $flash_pay_config);
            $pay_trade_status = $pay_result['data']['tradeStatus'];
            if ($pay_trade_status == InteriorOrderStatusEnums::FLASH_PAY_TRADE_STATUS_PAYING) {
                //交易处理中,不可取消
                throw new ValidationException($this->getTranslation()->_('interior_order_flash_paying'));
            } elseif ($pay_trade_status == InteriorOrderStatusEnums::FLASH_PAY_TRADE_STATUS_SUCCESS) {
                //交易成功
                if ($orderObj->goods_type == InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
                    throw new ValidationException($this->getTranslation()->_('interior_order_flash_payed'),
                        ErrCode::VALIDATE_ERROR);
                }

                $orderObj->fund_status = InteriorOrderFundStatusEnums::FUND_STATUS_REFUNDING;
            }
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw $e;
        }
        return $orderObj;
    }

    /**
     * 获取可购买免费工服的件数
     * @param object $userInfo 员工信息组
     * @return array|false|int|mixed|string|string[]
     */
    public function getCanFreeBuyGoodsNums($userInfo)
    {
        $interior_free_limit_num = 0;
        $setting_env_server = new SettingEnvServer();
        //免费工服员工formal类型配置
        $interior_free_staff_formal = $setting_env_server->getSetVal($code = 'interior_free_staff_formal', ',');
        if (empty($interior_free_staff_formal) || !in_array($userInfo->formal, $interior_free_staff_formal)) {
            return $interior_free_limit_num;
        }
        //系统配置可购买免费工服数量
        $interior_free_limit_num = $setting_env_server->getSetVal($code = 'interior_free_limit_num');

        //总计免费工服件数配置
        $interior_free_department_set = $setting_env_server->getSettingEnvValueMap('interior_free_department_set');

        //默认免费工服件数统一走其它【包含总部、一级部门未找到、一级部门ID配置未设置等情况】
        $free_limit_key = 'other';
        if ($userInfo->sys_store_id != enums::HEAD_OFFICE_ID) {
            //非总部，获取员工所属部门所在的一级部门ID
            $sys_department_id = (new SysDepartmentServer())->searchSysDeptId($userInfo->node_department_id);
            if ($sys_department_id) {
                //获取oa端指定网点一级部门id配置
                $appoint_store_by_department_id = OASettingEnvModel::findFirst([
                    'conditions' => 'code = :code:',
                    'bind' => ['code' => 'appoint_store_by_department_id'],
                ]);
                if ($appoint_store_by_department_id && $appoint_store_by_department_id->val) {
                    //一级部门id配置
                    $department_arr = json_decode($appoint_store_by_department_id->val, true);
                    $department_key = array_search($sys_department_id, $department_arr);
                    if ($department_key !== false && $interior_free_department_set && isset($interior_free_department_set[$department_key])) {
                        $free_limit_key = $department_key;
                    }
                }
            }
        }
        return ($interior_free_department_set && isset($interior_free_department_set[$free_limit_key])) ? $interior_free_department_set[$free_limit_key] : $interior_free_limit_num;
    }

    /**
     * 19024需求，员工商城分仓配置，用于配置不同商品类型的出库仓库ID
     * @param int $goods_type 商品类型0返回配置，1工服，2无头件
     * @return array|mixed|string
     */
    public function getGoodsTypeStockId($goods_type = 0)
    {
        $setting_env_server = new SettingEnvServer();
        $interior_goods_scm_stock_ids = $setting_env_server->getSettingEnvValueMap('interior_goods_scm_stock_ids');
        $this->getDI()->get('logger')->write_log("商城的分仓规则[goods_type={$goods_type}]" . json_encode($interior_goods_scm_stock_ids, JSON_UNESCAPED_UNICODE), 'info');

        if ($goods_type) {
            //传递了具体的商品类型则返回对应的仓库ID
            $stock_ids = $interior_goods_scm_stock_ids[$goods_type] ?? '';
        } else {
            //否则返回配置项
            $stock_ids = $interior_goods_scm_stock_ids;
        }

        if (empty($stock_ids)) {
            $this->getDI()->get('logger')->write_log("商城的分仓规则配置异常[[goods_type={$goods_type}], by.setting_env.interior_goods_scm_stock_ids], 请通知相关产品负责人", 'error');
        }

        return $stock_ids;
    }

    /**
     * 获取当前库存
     *
     * @param string $bar_code
     * @param int $goods_type 商品类型1工服，2无头件
     * @return array
     */
    public function getGoodsStock(string $bar_code, int $goods_type)
    {
        $sync_server          = new SyncWarehoseServer();
        $post_data            = [
            'barCode'     => $bar_code,
            'goodsStatus' => 'normal',
            'lang'        => $this->lang,
        ];
        //19024需求，员工商城分仓配置，用于配置不同商品类型的出库仓库ID
        $post_data['warehouseId'] = $this->getGoodsTypeStockId($goods_type);
        $scm_result          = $sync_server->syncGoodsStockWithErrBarcode($post_data);
        $scm_result_data     = $scm_result['data'];
        $available_inventory = [];
        if (!empty($scm_result_data) && !empty($scm_result_data['stock'])) {
            foreach ($scm_result_data['stock'] as $key => $inventory) {
                $available_inventory[$key] = $inventory['availableInventory'] > 0 ? $inventory['availableInventory'] : 0;
            }
        }
        return $available_inventory;
    }

    /**
     * 雨衣免费发放标识显隐逻辑(剩余可购买件数 = 设置的限免件数 - 已购买件数)
     * @param array $interior_goods_raincoat_set 员工商城雨衣相关配置信息，SPU-ID组、职位ID组、入职天数、最大免费件数
     * @param object $user_info 当前购买者信息组
     * @return int
     */
    public function getCanFreeBuyGoodsRaincoatNums($interior_goods_raincoat_set, $user_info)
    {
        $free_num = 0;
        if ($interior_goods_raincoat_set) {
            //员工职位属于配置的免费职位
            $raincoat_job_ids = $interior_goods_raincoat_set['job_ids'] ? explode(',', $interior_goods_raincoat_set['job_ids']) : [];
            if (in_array($user_info->job_title, $raincoat_job_ids)) {
                //员工的入职天数大于配置的免费发放天数
                $now_time = strtotime(date('Y-m-d H:i:s'));
                $hire_time = strtotime($user_info->hire_date);
                $days = ($now_time - $hire_time) / 86400;
                $hire_days = sprintf('%.2f', $days);
                $raincoat_set_hire_day = $interior_goods_raincoat_set['hire_day'] ?? 0;
                if ($raincoat_set_hire_day && $hire_days > $raincoat_set_hire_day) {
                    //员工已购买的免费雨衣件数(SPU属于配置并且非取消订单的SKU的数量)小于配置的最大免费件数
                    $raincoat_set_limit_num = $interior_goods_raincoat_set['limit_num'] ?? 0;
                    if ($raincoat_set_limit_num) {
                        $raincoat_has_buy_num = $this->getUserFreeRaincoatByNum($user_info->staff_info_id, $interior_goods_raincoat_set['good_ids']);
                        $free_num = (int)$raincoat_set_limit_num - $raincoat_has_buy_num;
                    }
                }
            }
        }
        return $free_num;
    }

    /**
     * 员工已购买的免费雨衣件数(SPU属于配置并且非取消订单的SKU的数量)
     * @param integer $staff_id 工号
     * @param array $good_ids 雨衣SPU-ID组
     * @return int
     */
    public function getUserFreeRaincoatByNum($staff_id, $good_ids)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['o' => InteriorOrdersModel::class]);
        $builder->leftjoin(InteriorOrdersGoodsSkuModel::class, 's.order_code = o.order_code', 's');
        $builder->columns('sum(buy_num) as buy_nums');
        $builder->Where('o.staff_id = :staff_id: and o.goods_type = :goods_type:', ['staff_id' => $staff_id, 'goods_type' => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES]);
        $builder->notInWhere('o.order_status', [InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE, InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE ]);
        $builder->andWhere('s.is_free = :is_free:', ['is_free' => InteriorOrderStatusEnums::ORDER_TYPE_FREE]);
        $builder->inWhere('s.goods_id', $good_ids);
        return (int) $builder->getQuery()->getSingleResult()->buy_nums;
    }

    /**
     * 根据商品类型, 获取FlashPay相关配置
     *
     * @param int $goods_type
     * @param string $mach_code
     * @return array|mixed
     * @throws BusinessException
     */
    public function getFlashConfig(int $goods_type, string $mach_code)
    {
        static $config_list = [];
        if (!empty($config_list[$goods_type])) {
            return $config_list[$goods_type];
        }

        //V22367 泰国 FFM货主则读取配置类型为3的flash pay配置信息
        if (isCountry('TH') && $this->getFfmMachCode() == $mach_code) {
            $goods_type = 3;
        }

        $setting_env_server = new SettingEnvServer();
        $flashpay_config = $setting_env_server->getSettingEnvValueMap('interior_flashpay_config')[$goods_type] ?? [];
        if (empty($flashpay_config)) {
            throw new BusinessException("获取商城FlashPay配置失败,code = interior_flashpay_config, goods_type = {$goods_type}", ErrCode::ERROR);
        }

        $config_list[$goods_type] = $flashpay_config;
        return $flashpay_config;
    }

    /**
     * 根据单号获取订单详情
     * @param $orderCode
     * @return mixed
     */
    public function getOrderByOrderCode($orderCode)
    {
        return InteriorOrdersModel::findFirst([
            'conditions' => 'order_code = :orderCode:',
            'bind' => ["orderCode" => $orderCode],
        ]);
    }

    /**
     * 自动审核出库单
     *
     * @param object $orderObj
     * @return bool
     */
    public function autoAuditOutboundOrder(object $orderObj)
    {
        $autoAuditRes = false;

        $logTitle = "商城订单自动审核出库[{$orderObj->order_code} - {$orderObj->out_sn}]";

        // 自动审核出库
        $auditRes = (new SyncWarehoseServer())->syncAuditFromWmsAuditOutbound($orderObj);

        // 审核出库成功
        if (isset($auditRes['code']) && $auditRes['code'] == ErrCode::SUCCESS) {
            $this->wLog($logTitle, "SCM出库单审核处理成功[{$auditRes['msg']}]");

            $saveData = [
                'auto_audit_outbound_status' => InteriorOrderStatusEnums::ORDER_AUTO_AUDIT_OUTBOUND_STATUS_YES,
                'auto_audit_outbound_at'     => date('Y-m-d H:i:s'),
            ];
            if ($orderObj->save($saveData) === false) {
                $this->wLog($logTitle, '订单表自动审核出库状态标记失败', '', 'warning');
            } else {
                $this->wLog($logTitle, '订单表自动审核出库状态标记成功');
                $autoAuditRes = true;
            }
        } else {
            $this->wLog($logTitle, "SCM出库单审核处理异常[{$auditRes['msg']}]", '', 'notice');
        }

        return $autoAuditRes;
    }

    /**
     * 合成扫码付待付款图片
     *
     * @param array $params
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function generateQrcodePayImage(array $params)
    {
        // 提取 ftl 模板
        $ftlFileUrl = (new SettingEnvServer())->getSetVal('interior_flash_pay_qrcode_image_ftl');
        if (empty($ftlFileUrl)) {
            throw new BusinessException('interior_flash_pay_qrcode_image_ftl 未配置, 请快速联系产品', ErrCode::ERROR);
        }

        // 组合变量
        $htmlToImgParams = [
            'file_name'   => 'FlashPayQrCodeImg',
            'tpl_url'     => $ftlFileUrl, // ftl模板oss路径（html页面）

            // 表单变量数据
            'data'        => [
                'order_code'             => $params['order_code'],
                'order_code_label'       => $params['order_code_label'],
                'total_pay_amount_fmt'   => $params['total_pay_amount_fmt'],
                'total_pay_amount_label' => $params['total_pay_amount_label'],
                'qr_image'               => $params['qr_image'],
            ],
            'disposition' => 'inline',// inline 或 attachment (直接下载)

            // 图片尺寸
            'viewport'    => [
                'width'  => '375',
                'height' => '528',
            ],

            'imageOptions' => [
                'fullPage' => false,
            ],
        ];

        // 合成
        $payImgData = FormImgServer::getInstance()->htmlToImg($htmlToImgParams);
        if (empty($payImgData['object_url'])) {
            throw new ValidationException($this->getTranslation()->_('interior_flash_pay_qrcode_download_error'),
                ErrCode::VALIDATE_ERROR);
        }

        // 返回合成图片地址
        return ['data' => $payImgData['object_url']];
    }

    /**
     * 商城上新通知
     * @param $date_at
     * @return true
     */
    public function newGoodsNotice($date_at): bool
    {
        if (!$this->checkIsSendNotice($date_at)) {
            $this->logger->write_log([
                'title' => '商城上新通知',
                'func'  => 'newGoodsNotice',
                'msg'   => '无数据不发送通知',
            ], 'info');
            return true;
        }
        $max_id = 0;
        while (true) {
            $staffs = $this->getNeedSendStaff($max_id);
            if (empty($staffs)) {
                break;
            }
            $max_id = end($staffs)['staff_info_id'];
            echo $max_id.PHP_EOL;
            $this->sendNotice($staffs);
        }
        return true;
    }

    /**
     * 商城上新 待发送消息员工
     * @param $max_id
     * @return array
     */
    protected function getNeedSendStaff($max_id = 0): array
    {
        $without_staffs = (new SettingEnvServer)->getSetVal('mail_new_goods_not_msg_staff_ids', ',');

        $conditions = "state = :state: and is_sub_staff = :is_sub_staff: and formal in ({formal:array}) and staff_info_id > :max_id:";
        $bind       = [
            'max_id'       => $max_id,
            'formal'       => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
            'state'        => HrStaffInfoModel::STATE_ON_JOB,
            'is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0,
        ];
        if (!empty($without_staffs)) {
            $conditions             .= ' and staff_info_id not in ({without_staffs:array})';
            $bind['without_staffs'] = $without_staffs;
        }

        if (isCountry(['TH', 'PH'])) {
            $conditions        .= " and hire_type not in ({hire_type:array}) ";
            $bind['hire_type'] = HrStaffInfoModel::$agentTypeTogether;
        }
        return HrStaffInfoModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => 'staff_info_id',
            'order'      => 'staff_info_id asc',
            'limit'      => 1000,
        ])->toArray();
    }


    /**
     * 是否有上新 有才发通知
     * @param $date_at
     * @return bool
     */
    protected function checkIsSendNotice($date_at): bool
    {

        $start_time = zero_time_zone(date('Y-m-d 00:00:00', strtotime($date_at . ' -7 day')));
        $end_time   = zero_time_zone(date('Y-m-d 23:59:59', strtotime($date_at . ' -1 day')));

        $conditions = "created_at > :start_time: and created_at <= :end_time: and  status = :status: and goods_type = :goods_type:";
        $bind       = [
            "start_time" => $start_time,
            'end_time'   => $end_time,
            'status'     => InteriorGoodsStatusEnums::GOODS_STATUS_ON_SALE,
            'goods_type' => InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE,
        ];
        var_dump($bind);
        $exist = InteriorGoodsModel::findFirst([
                'conditions' => $conditions,
                'bind'       => $bind,
            ]
        );
        return !empty($exist);
    }

    /**
     * 商城上新消息标题
     * @return string
     */
    protected function getNewGoodsMessageTitle(): string
    {
        if (isCountry('TH')) {
            return 'อัพเดตสินค้าใหม่ประจำสัปดาห์นี้/New Product Launch Notification';
        } elseif (isCountry('PH')) {
            return 'New Product Launch Notification/商场上新通知';
        } else {
            return 'New Product Launch Notification/商场上新通知';
        }
    }


    /**
     * 发送消息
     * @param array $staffs
     * @return void
     */
    private function sendNotice(array $staffs): void
    {
        $category = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_NEW_GOODS_NOTICE');
        $title    = $this->getNewGoodsMessageTitle();
        foreach ($staffs as $staff) {
            $staff_info_id               = $staff['staff_info_id'];
            $id                          = time() . $staff_info_id . rand(1000000, 9999999);
            $param['staff_users']        = [$staff_info_id];//数组 多个员工id
            $param['message_title']      = $title;
            $param['message_content']    = '';
            $param['staff_info_ids_str'] = $staff_info_id;
            $param['id']                 = $id;
            $param['category']           = $category;
            (new MessageCourierServer())->add_kit_message($param);
        }
    }

    /**
     * 获取FFM货主编码
     * @description: 获取FFM货主编码
     * @author: AI
     * @date: 2025-08-05 20:30:00
     * @return string|null 货主代码，FFM商品返回FFM 货主
     */
    public function getFfmMachCode(): ?string
    {
        return (new SettingEnvServer())->getSetVal('interior_goods_scm_ffm_mach_code');
    }

    /**
     * 判断商品是否为FFM商品
     * @description: 根据商品ID判断是否在FFM商品配置列表中
     * @author: AI
     * @date: 2025-08-05 20:30:00
     * @param int $goodsId 商品ID
     * @return bool 是否为FFM商品
     */
    public function isFfmGoods(int $goodsId): bool
    {
        try {
            $settingEnvServer = new SettingEnvServer();
            $ffmGoodsIds = $settingEnvServer->getSetVal('interior_goods_ffm_good_ids', ',');

            if (empty($ffmGoodsIds)) {
                return false;
            }

            // 转换为整数数组进行比较
            $ffmGoodsIds = array_map('intval', array_filter($ffmGoodsIds));
            return in_array($goodsId, $ffmGoodsIds);

        } catch (\Exception $e) {
            return false;
        }
    }


    /**
     * 根据网点ID获取分仓配置
     * @description: 从interior_orders_warehouse_set表获取分仓配置
     * @author: AI
     * @date: 2025-08-05 20:30:00
     * @param string $storeId 网点ID
     * @return array 分仓配置信息
     */
    public function getWarehouseConfigByStoreId(string $storeId): array
    {
        $warehouseConfig = InteriorOrdersWarehouseSetModel::findFirst([
            'conditions' => 'sys_store_id = :sys_store_id:',
            'bind'       => ['sys_store_id' => $storeId],
        ]);

        return $warehouseConfig ? $warehouseConfig->toArray() : [];
    }

}
