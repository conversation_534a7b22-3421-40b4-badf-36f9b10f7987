<?php
/**
 * Created by PhpStor<PERSON>.
 * User: nick
 * Date: 2021/5/31
 * Time: 7:10 PM
 */

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\StaffMileageRecord;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\SysAttachmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Enums\MilesEnums;

class MilesServer extends BaseServer{

    protected $miles_limit = 'miles_limit_store'; //对应java fle:app:staff_mileage_code:store_ids 配置的是 70000 其他网点是 450000
    const MAX_CHANGE_CAR_COUNT = 2;
    const MILES_OSS_TYPE = 'STAFF_MILEAGE_WORK_RECORD';

//            //上班里程
//            private static final String START_MILEAGE_IMAGE = "SMI";
//            //上班大门
//            private static final String START_GATE_IMAGE = "SGI";
//            //下班里程
//            private static final String END_MILEAGE_IMAGE = "EMI";
//            //下班大门
//            private static final String END_GATE_IMAGE = "EGI";
    const START_MILEAGE_IMAGE = "SMI";
    const START_GATE_IMAGE = "SGI";
    const END_MILEAGE_IMAGE = "EMI";
    const END_GATE_IMAGE = "EGI";

    const MILES_START_TYPE = 1;//上班里程汇报
    const MILES_END_TYPE = 2;//下班里程汇报

    public $timezone;
    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }


    /**
     *
     * 获取里程信息
     * * data class MileData(
            val end_at: String,
            val end_img: String,
            val end_kilometres: String,
            val mileage_date: String,
            val start_img: String,
            val start_kilometres: String?,
            val started_at: String,
            val limited_mileage: String, 最大限制里程
            val change_car_count: Int, 已经换车次数
            val limited_change_car_cnt: Int // 每月最大换车数
    )
     * @param $staff_info
     * @param $date
     * @throws \Exception
     */
    public function miles_info($staff_info,$date){
        //格式化 日期
        $date = date('Y-m-d',strtotime($date));
        $staff_re = new StaffRepository($this->lang);

        if((isCountry('TH') || isCountry('PH') || isCountry('MY')) &&  $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportOsStaffInfo($staff_info['id'])){
            $this->logger->write_log("get_miles_info Emp.No changed os_staff_id:{$staff_info['id']}  formal_staff_id:".$supportStaffInfo['staff_info_id'],'info');
            $staff_info['id'] = $supportStaffInfo['staff_info_id'];
        }
        $staff_info = $staff_re->getStaffpositionV2($staff_info['id']);
        if(empty($staff_info))
            throw new \Exception("staff info error");

        //获取网点 里程数上限
        $return['limited_mileage'] = $this->get_limit_miles($staff_info);

        //获取员工换车次数和 最大次数
        $return['limited_change_car_cnt'] = self::MAX_CHANGE_CAR_COUNT;
        $return['change_car_count'] = $this->get_change_car_num($staff_info['id']);

        $return['started_at'] =  $return['start_img'] = $return['end_at'] = $return['end_img'] = $return['mileage_date'] = '';
        $return['start_kilometres'] = $return['end_kilometres'] = '';
        $info = StaffMileageRecord::findFirst("staff_info_id = {$staff_info['id']} and mileage_date = '{$date}'");
        if(!empty($info)){
            $return['mileage_date'] = $date;
            $add_hour = $this->config->application->add_hour;
            $return['started_at'] = empty($info->started_at) ? '' : date('Y-m-d H:i:s', strtotime($info->started_at) + $add_hour * 3600);
            $return['start_kilometres'] = $info->start_kilometres;
            $return['end_at'] = empty($info->end_at) ? '' : date('Y-m-d H:i:s', strtotime($info->end_at) + $add_hour * 3600);
            $return['end_kilometres'] = $info->end_kilometres;

            //格式化里程图片
            /**
             * SELECT *
                FROM sys_attachment
                WHERE oss_bucket_type = #{ossBucketType}
                AND oss_bucket_key = #{ossBucketKey}
                AND deleted = 0
                ORDER BY created_at ASC
             */
            $bucket_type = self::MILES_OSS_TYPE;
            $img_list = SysAttachmentModel::find("oss_bucket_type = '{$bucket_type}' and oss_bucket_key = '{$info->id}' and deleted = 0 ")->toArray();
            if(!empty($img_list)){
                $att_server = new AttendanceServer($this->lang,$this->timezone);
                foreach ($img_list as $img){
                    $param['bucket'] = $img['bucket_name'];
                    $param['path'] = $img['object_key'];
                    if(strstr($img['object_key'],self::START_MILEAGE_IMAGE)){
                        $return['start_img'] = $att_server->format_oss($param);
                    }else{
                        $return['end_img'] = $att_server->format_oss($param);
                    }
                }
            }

        }
        return $return;
    }

    /**
     * 里程保存
     *
     * kilometres	里程数 用户输入的*1000，例： 5 * 1000
        mileage_record_type 	1上班； 2下班
        mileage_images [["object_key":objectKey]]  里程拍照图片
        mileage_date	打卡时间 字符串格式
        change_car   true false  是否换车
     * @param $param
     * @throws \Exception
     * @return boolean
     */
    public function add($param){
        $this->logger->write_log("miles_record {$param['user_info']['id']} " . json_encode($param),'info');


        if((isCountry('TH') || isCountry('PH') || isCountry('MY')) &&  $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportOsStaffInfo($param['user_info']['id'])){
            $this->logger->write_log("add miles  Emp.No changed os_staff_id:{$param['user_info']['id']}  formal_staff_id:".$supportStaffInfo['staff_info_id'],'info');
            $param['user_info']['id'] = $supportStaffInfo['staff_info_id'];
        }

        $insert['mileage_record_type'] = intval($param['mileage_record_type']);
        if(empty($insert['mileage_record_type']))
            return $this->getTranslation()->_('miles_C100100');

        //里程汇报类型错误
        if(!in_array($insert['mileage_record_type'],array(self::MILES_START_TYPE,self::MILES_END_TYPE)))
            return $this->getTranslation()->_('miles_C100100');

        $img_list = $param['mileage_images'];
        if(count($img_list) > 1)
            return $this->getTranslation()->_('miles_C102006');

        $insert['change_car'] = empty($param['change_car']) ? 0 : 1;

        //只有上班里程上报可以换车
        if($insert['change_car'] && $insert['mileage_record_type'] == self::MILES_END_TYPE)
            return $this->getTranslation()->_('miles_C100100');

        $kilometres = $param['kilometres'];

        if(intval($kilometres) < 1) //里程必须大于0
            return $this->getTranslation()->_('miles_C102011');

        //验证次数 每月最多2次
        if($insert['change_car']){
            $count = $this->get_change_car_num($param['user_info']['id']);
            if(self::MILES_END_TYPE <= $count)
                return $this->getTranslation()->_('miles_C102010');
        }else{
            $check_info = $this->check_change_car($param['user_info']['id']);
            if(!empty($check_info)){
//                if (nonNull(newestStaffMileageRecord.getEndKilometres()) && newestStaffMileageRecord.getEndKilometres() > kilometre) {
//                    throw new ServiceEcaException(ErrorCodeEnum.C102009);
//                } else if (nonNull(newestStaffMileageRecord.getStartKilometres()) && newestStaffMileageRecord.getStartKilometres() > kilometre) {
//                    throw new ServiceEcaException(ErrorCodeEnum.C102009);
//                }
                if(!empty($check_info['end_kilometres']) && $check_info['end_kilometres'] > $kilometres)
                    return $this->getTranslation()->_('miles_C102009');
                else if(!empty($check_info['start_kilometres']) && $check_info['start_kilometres'] > $kilometres)
                    return $this->getTranslation()->_('miles_C102009');
            }
        }

        $current_time = gmdate('Y-m-d H:i:s');
        $date = $param['mileage_date'];
        $staff_re = new StaffRepository($this->lang);
        $staff_info = $staff_re->getStaffpositionV2($param['user_info']['id']);
        $info = StaffMileageRecord::findFirst("staff_info_id = {$staff_info['id']} and mileage_date = '{$date}'");

        //片区 查询
        $store_server = new SysStoreServer($this->lang,$this->timezone);
        $region_info = $store_server->getStoreRegion($staff_info['organization_id']);

        $model = new StaffMileageRecord();
        $db = $this->getDI()->get('db');
        $db->begin();
        //上班汇报
        if($param['mileage_record_type'] == self::MILES_START_TYPE){
            if(!empty($info) && !empty($info->start_kilometres))
                return $this->getTranslation()->_('miles_C102004');

            $insert['staff_info_id'] = $staff_info['id'];
            $insert['store_id'] = $staff_info['organization_id'];
            $insert['sorting_no'] = empty($region_info) ? null : $region_info['region_name'];
            $insert['mileage_date'] = $date;
            $insert['start_kilometres'] = $kilometres;
            $insert['started_at'] = $current_time;
            $flag = $model->create($insert);

            $insert_flag = false;
            if($flag){
                //获取 主键id 回写 图片表
                $img_insert['id'] = md5("{$current_time}_{$staff_info['id']}_{$insert['mileage_record_type']}");
                $img_insert['oss_bucket_type'] = self::MILES_OSS_TYPE;
                $img_insert['oss_bucket_key'] = $model->id;
                $img_insert['bucket_name'] = $this->config->application->oss_bucket;//固定
                $img_insert['object_key'] = $img_list[0]['object_key'];
                $img_model = new SysAttachmentModel();
                $insert_flag = $img_model->create($img_insert);
            }else{
                $db->rollback();
            }
            //日志
            $this->logger->write_log("miles_record {$staff_info['id']} {$flag}_{$insert_flag} {$model->id} " . json_encode($insert),'info');
        }else{//下班汇报
            if(!empty($info) && !empty($info->end_kilometres))
                return $this->getTranslation()->_('miles_C102005');
            //如果有上班里程
            if(!empty($info) && !empty($info->start_kilometres)){
                $limit_mile = $this->get_limit_miles($staff_info);
                //TODO PH的调整到1万公里 变相不限制路程除非是飞机
                $countryCode = ucfirst(strtolower($this->config->application->country_code));
                if( $countryCode == "Ph"){
                    $limit_mile = 1000 * 1000;//1千公里
                }
                $today_miles = $kilometres - $info->start_kilometres;
                if($today_miles < 0 || $today_miles > $limit_mile){
                    $str = $this->getTranslation()->_('miles_C102008');
                    $str = str_replace('{0}',$limit_mile/1000,$str);
                    return $str;
                }

            }

            //如果存在 当天记录 update
            if(!empty($info)){
//                if (staffMileageRecord.getStartKilometres() > kilometre) {
//                    throw new ServiceEcaException(ErrorCodeEnum.C102007);
//                }

                if($info->start_kilometres > $kilometres)//不能比上班里程小
                    return $this->getTranslation()->_('miles_C102007');

                $info->end_kilometres = $kilometres;
                $info->end_at = $info->updated_at = $current_time;
                $flag = $info->update();
                $insert_flag = false;
                if($flag){//入库图片
                    $img_insert['id'] = md5("{$current_time}_{$staff_info['id']}_{$insert['mileage_record_type']}");
                    $img_insert['oss_bucket_type'] = self::MILES_OSS_TYPE;
                    $img_insert['oss_bucket_key'] = $info->id;
                    $img_insert['bucket_name'] = $this->config->application->oss_bucket;//固定
                    $img_insert['object_key'] = $img_list[0]['object_key'];
                    $img_model = new SysAttachmentModel();
                    $insert_flag = $img_model->create($img_insert);
                }else
                    $db->rollback();
                //日志
                $this->logger->write_log("miles_record {$staff_info['id']} {$flag}_{$insert_flag} {$info->id} " . json_encode($param),'info');
            }else{//插入新数据
                $insert['staff_info_id'] = $staff_info['id'];
                $insert['store_id'] = $staff_info['organization_id'];
                $insert['sorting_no'] = empty($region_info) ? null : $region_info['region_name'];
                $insert['mileage_date'] = $date;
                $insert['end_kilometres'] = $kilometres;
                $insert['end_at'] = $current_time;

                $flag = $model->create($insert);
                $insert_flag = false;
                if($flag){
                    //获取 主键id 回写 图片表
                    $img_insert['id'] = md5("{$current_time}_{$staff_info['id']}_{$insert['mileage_record_type']}");
                    $img_insert['oss_bucket_type'] = self::MILES_OSS_TYPE;
                    $img_insert['oss_bucket_key'] = $model->id;
                    $img_insert['bucket_name'] = $this->config->application->oss_bucket;//固定
                    $img_insert['object_key'] = $img_list[0]['object_key'];
                    $img_model = new SysAttachmentModel();
                    $insert_flag = $img_model->create($img_insert);
                }else
                    $db->rollback();
                //日志
                $this->logger->write_log("miles_record {$staff_info['id']} {$flag}_{$insert_flag} {$model->id} " . json_encode($insert),'info');
            }
        }
        $db->commit();
        return true;

    }

    public function get_limit_miles($staff_info){
        $limit = 450000;
        $open_store = SettingEnvModel::findFirst("code = '{$this->miles_limit}'");
        if(!empty($open_store)){
            $open_store = explode(',',$open_store->set_val);
            if(in_array($staff_info['organization_id'],$open_store))
                $limit = 700000;
        }
        return $limit;
    }

    //获取当月 换车次数
    public function get_change_car_num($staff_id){
        //原java sql 不会转orm
        $sql = "SELECT count(*)
                FROM staff_mileage_record
                WHERE staff_info_id = {$staff_id}
                AND change_car = 1
                AND mileage_date >= (
                SELECT date_add(curdate(), interval - day(curdate()) + 1 day)
                )";
        return $this->getDI()->get('db')->fetchColumn($sql);
    }
    //没仔细看 这sql 干啥的 大概意思 就是 换车以后 不用校验什么逻辑之类的
    public function check_change_car($staff_id){
        $sql = "SELECT
                  CASE
                    WHEN
                      amm.start_kilometres IS NOT NULL THEN
                      amm.start_kilometres ELSE smr.start_kilometres
                    END start_kilometres,
                  CASE
                    WHEN amm.end_kilometres IS NOT NULL THEN
                      amm.end_kilometres ELSE smr.end_kilometres
                    END end_kilometres
                FROM staff_mileage_record smr
                LEFT JOIN approve_modify_mileage amm on smr.id = amm.origin_id
                WHERE smr.staff_info_id = {$staff_id}
                ORDER BY smr.mileage_date DESC
                LIMIT 1";
        return $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);

    }


    /**
     * 测试环境专用
     * 里程上报
     * @param $params
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function testAdd($params): bool
    {
        if(!in_array(RUNTIME,['dev','tra','tra'])){
            throw new ValidationException('接口只适用于测试、training 环境');
        }
        $staffMileageRecord = StaffMileageRecord::findFirst([
                                                       'conditions' => 'staff_info_id = :staff_info_id: and mileage_date = :mileage_date: ',
                                                       'bind' => ['staff_info_id' => $params['staff_info_id'] ,'mileage_date' => $params['mileage_date']]
                                                   ]);
        if(!$staffMileageRecord){
            $staffMileageRecord = new StaffMileageRecord();
        }
        //员工信息
        $staff_info = (new StaffRepository($this->lang))->getStaffpositionV2($params['staff_info_id']);
        //片区 查询
        $region_info = (new SysStoreServer($this->lang,$this->timezone))->getStoreRegion($staff_info['organization_id']);
        $staffMileageRecord->staff_info_id = $staff_info['id'];
        $staffMileageRecord->store_id = $staff_info['organization_id'];
        $staffMileageRecord->sorting_no = empty($region_info) ? null : $region_info['region_name'];
        $staffMileageRecord->mileage_date = $params['mileage_date'];

        if($params['mileage_record_type'] ==  self::MILES_START_TYPE){
            $staffMileageRecord->start_kilometres = $params['kilometres'];
            $staffMileageRecord->started_at = gmdate('Y-m-d H:i:s');
        }else{
            $staffMileageRecord->end_kilometres = $params['kilometres'];
            $staffMileageRecord->end_at = gmdate('Y-m-d H:i:s');
        }
        if($staffMileageRecord->save() === false){
            throw new BusinessException('save error');
        }
        return true;
        $img_insert['id'] = md5(time()."_{$staff_info['id']}_{$params['mileage_record_type']}");
        $img_insert['oss_bucket_type'] = self::MILES_OSS_TYPE;
        $img_insert['oss_bucket_key'] = $staffMileageRecord->id;
        $img_insert['bucket_name'] = $this->config->application->oss_bucket;//固定
        $img_insert['object_key'] = $params['image'];
        $img_model = new SysAttachmentModel();
        return  $img_model->create($img_insert);

    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        //获取申请人信息
        $system_external_approval_data = SystemExternalApprovalModel::findFirst("id = {$auditId}");
        $system_external_approval_data = $system_external_approval_data ? $system_external_approval_data->toArray() : [];
        $submitter_id                  = $system_external_approval_data['submitter_id'] ?? '';

        //获取申请人网点信息
        $staff_info = HrStaffInfoModel::findFirst("staff_info_id = {$submitter_id}");
        $staff_info = $staff_info ? $staff_info->toArray() : [];
        $store_id   = $staff_info['sys_store_id'] ?? '';

        $new_date = date('Y-m-d');

        // 获取网点负责人
        $store_data               = SysStoreModel::findFirst("id = '{$store_id}'");
        $store_data               = $store_data ? $store_data->toArray() : [];
        $store_manager_id         = $store_data['manager_id'] ?? '';
        $is_store_manager_to_work = 2;
        $is_piece_mgr_to_work     = 2;
        if ($store_manager_id) {
            // 所属网点负责人有没有上班打卡信息
            $is_store_manager_to_work = $this->staffIsToWork($store_manager_id, $new_date);
            $is_store_manager_to_work = $is_store_manager_to_work ? MilesEnums::IS_STORE_MANAGER_TO_WORK_YES : MilesEnums::IS_STORE_MANAGER_TO_WORK_NO; // 1=打卡，2=未打卡
        }

        // 获取网点片区负责人
        $store_manage_piece_id = $store_data['manage_piece'] ?? 0;
        if ($store_manage_piece_id) {
            $store_manage_piece_data = SysManagePieceModel::findFirst("id = {$store_manage_piece_id}");
            $store_manage_piece_data = $store_manage_piece_data ? $store_manage_piece_data->toArray() : [];
            $store_piece_manager_id  = $store_manage_piece_data['manager_id'] ?? '';
            if ($store_piece_manager_id) {
                // 所属网点片区负责人有没有上班打卡信息
                $is_piece_mgr_to_work = $this->staffIsToWork($store_piece_manager_id, $new_date);
                $is_piece_mgr_to_work = $is_piece_mgr_to_work ? MilesEnums::IS_STORE_MANAGER_TO_WORK_YES : MilesEnums::IS_STORE_MANAGER_TO_WORK_NO; // 1=打卡，2=未打卡
            }
        }

        // 获取网点片区负责人


        // SystemExternalApprovalServer.getDetail
        // SystemExternalApprovalServer.updateApprove
        // SystemExternalApprovalServer.setProperty
        // SystemExternalApprovalServer.genSummary
        //

        return [
            'is_store_manager_to_work' => $is_store_manager_to_work,
            'is_piece_mgr_to_work'     => $is_piece_mgr_to_work,
        ];
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $info = SystemExternalApprovalModel::findFirst($auditId);
        if (!empty($info)) {
            $summary = json_decode($info->summary, true);
        } else {
            $summary = json_decode([], true);
        }
        return $summary;
    }

    /**
     * @description: 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态      const APPROVAL_STATUS_PENDING   = 1;    //待审批 const APPROVAL_STATUS_APPROVAL  =
     *                      2;    //审批同意const APPROVAL_STATUS_REJECTED  = 3;    //审批驳回const APPROVAL_STATUS_CANCEL    =
     *                      4;    //审批撤销const APPROVAL_STATUS_TIMEOUT   = 5;    //审批超时
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $system_external             = new SystemExternalApprovalServer($this->lang, $this->timezone);
        $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
        if (!$SystemExternalApprovalModel) {
            throw new \Exception('setProperty  没有找到数据  '.$auditId);
        }

        if ($isFinal) {
            //最终审批需要做的操作
            $SystemExternalApprovalModel->state      = $state;
            $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());      //时间

            //追加驳回原因
            if ($state == enums::APPROVAL_STATUS_REJECTED) {
                $rejectReason = $extend['remark'];
            }
            // 回调 异步脚本
            $params   = [
                'serial_no_list'         => [$SystemExternalApprovalModel->serial_no],
                'status'                 => $state == 2 ? 1 : 2,
                'approval_staff_info_id' => $extend['staff_id'],
                'type'                   => $extend['staff_id'] == 10000 ? 2 : 1,
                'id'                     => $auditId,
            ];
            AuditCallbackServer::createData(AuditListEnums::APPROVAL_TYPE_MILEAGE,$params);
            return $SystemExternalApprovalModel->save();
        }
    }

    /**
     * 写入redis队列
     * @param $data
     * @return mixed
     */
    public function pushRedis($data)
    {
        $redis   = $this->getDI()->get('redisLib');
        $res = $redis->lpush(RedisEnums::LIST_BI_FALSE_MILES_DAILY, json_encode($data));
        $this->getDI()->get("logger")->write_log(['data'=>$data,'func'=>'pushRedis','res'=>$res], "info");
        return $res;
    }


    /**
     * @throws Exception
     */
    public function delayCallBack($params): bool
    {
        $model     = SystemExternalApprovalModel::findFirst($params['id']);
        $sys       = isCountry('TH') ? 'ard_api' : 'bi_rpcv2';
        $rpcClient = new ApiClient($sys, '', 'approval.approval_mileage', $this->lang);
        $rpcClient->setParams($params);
        $res = $rpcClient->execute();
        if (isset($res['error']) || !isset($res['result']) || $res['result']['code'] != 1) {
            //这里需要判断 如果是失败了 记录一下  后期跑脚本
            $model->is_call_third_party = SystemExternalApprovalModel::is_call_third_party_2;
            $model->save();
            throw new Exception($res['result']['msg']);
        }
        return true;
    }


    /**
     * @description:审批接口 (通过 驳回 撤销)
     */
    public function updateApprove($paramIn = [])
    {
        $bizType     = $this->processingDefault($paramIn, 'biz_type', 1);      // 申请类型
        $bizValue    = $this->processingDefault($paramIn, 'id', 1);      // 主键 id
        $serialNo    = $this->processingDefault($paramIn, 'serial_no', 1);      // 编号
        $remark      = $this->processingDefault($paramIn, 'reason', 1);      // 备注
        $status      = $this->processingDefault($paramIn, 'status', 1);      // 操作  2 同意  3 驳回   4 撤销
        $operator_id = $this->processingDefault($paramIn, 'operator_id', 1);      // 操作人

        $system_external = new SystemExternalApprovalServer($this->lang, $this->timezone);
        if (!in_array($bizType, $system_external->getApprovalType()) || (empty($bizValue) && empty($serialNo))) {
            throw new ValidationException($this->getTranslation()->_('2201'));
        }
        $conditions = ' 1=1 ';
        $bind       = [];
        if ($bizValue) {
            $conditions .= " and id = :id: ";
            $bind['id'] = $bizValue;
        }
        if ($serialNo) {
            $conditions        .= " and serial_no = :serial_no:  ";
            $bind['serial_no'] = $serialNo;
        }

        //查询数据
        $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        if (!$SystemExternalApprovalModel) {
            throw new ValidationException($this->getTranslation()->_('2201'));
        }
        $info       = $SystemExternalApprovalModel->toArray();
        $app_server = new ApprovalServer($this->lang, $this->timeZone);
        $res        = [];
        $errInfo    = [];
        if ($status == enums::$audit_status['approved']) {//审核通过
            $res = $app_server->approval($info['id'], $bizType, $operator_id, $remark, null, $errInfo);
        } elseif ($status == enums::$audit_status['dismissed']) {//驳回

            $res = $app_server->reject($info['id'], $bizType, $remark, $operator_id, null, $errInfo);
        } elseif ($status == enums::$audit_status['revoked']) {//撤销
            $res = $app_server->cancel($info['id'], $bizType, $remark, $operator_id, null, $errInfo);
        }

        if (!isset($res) || empty($res)) {
            // 如果是 SYSTEM_ERROR  才走 Exception
            if (!isset($errInfo['code']) || $errInfo['code'] == ErrCode::SYSTEM_ERROR) {
                throw new \Exception($errInfo['message'], $errInfo['code']);
            }
            //否则 走 ValidationException
            throw new ValidationException($errInfo['message'], $errInfo['code']);
        }

        $data = [
            'is_final' => $res->state != enums::APPROVAL_STATUS_PENDING ? 1 : 0,
        ];

        return $this->checkReturn(["code" => 1, "msg" => 'success', 'data' => $data]);
    }

    /**
     * 详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $system_external = new SystemExternalApprovalServer($this->lang, $this->timezone);
        //获取详情
        $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
        if (!$SystemExternalApprovalModel) {
            throw new \Exception('getDetail  没有找到数据  '.$auditId);
        }
        $result = $SystemExternalApprovalModel->toArray();
        //获取详情
        $forwardParamIn = [
            'biz_type'                 => $result['biz_type'],
            'param_operation_type'     => $system_external::param_operation_type_4,
            'param_operation_staff_id' => $user,
            'serial_no'                => $result['serial_no'],
        ];
        $svc_result     = $system_external->forwardParamIn($forwardParamIn);
        if (!isset($svc_result['code']) || $svc_result['code'] != 1) {
            throw new \Exception('getDetail  获取详情失败  '.$auditId);
        }
        $auditList = new AuditlistRepository($this->lang, $this->timezone);
        $_detail   = [];
        if ($svc_result['data']) {
            foreach ($svc_result['data'] as $k => $v) {
                $_detail[$v["key"]] = $v["value"];
            }
        }
        $returnData['data']['detail'] = $this->format($_detail ?? []);
        $add_hour                     = $this->getDI()['config']['application']['add_hour'];
        $data                         = [
            'title'      => $auditList->getAudityType($result['biz_type']),
            'id'         => $result['id'],
            'staff_id'   => $result['submitter_id'],
            'type'       => $result['biz_type'],
            'created_at' => date('Y-m-d H:i:s', (strtotime($result['created_at']) + $add_hour * 3600)),
            'updated_at' => date('Y-m-d H:i:s', (strtotime($result['updated_at']) + $add_hour * 3600)),
            'status'     => $result['state'],
            'serial_no'  => $result['serial_no'] ?? '',
            'biz_type'   => $result['biz_type'],
            'is_update'  => 0,  // 1 可以编辑 0 不能编辑
        ];
        $option                       = ['beforeApproval' => '0', 'afterApproval' => '0'];
        $state                        = 0;
        //当前用户是否已经审批
        if ($comeFrom == 2) { //获取审批人的审批状态
            $infos = AuditApprovalModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and approval_id = :approval_id: and deleted = 0",
                'bind'       => [
                    'type'        => $result['biz_type'],
                    'value'       => $auditId,
                    'approval_id' => $user,
                ],
                'order'      => 'id desc',
            ]);

            $state             = $infos instanceof AuditApprovalModel ? $infos->getState() : 0;
            $option            = $state == enums::APPROVAL_STATUS_PENDING ? [
                'beforeApproval' => "1,2",
                'afterApproval'  => '0',
            ] : $option;
            $data['is_update'] = $state == enums::APPROVAL_STATUS_PENDING ? enums::APPROVAL_STATUS_PENDING : $data['is_update'];
        } else { //获取申请的审批状态
            $option = $user == $result['submitter_id'] && $result['state'] == enums::APPROVAL_STATUS_PENDING ? [
                'beforeApproval' => '0',
                'afterApproval'  => '0',
            ] : $option;
        }

        $data['options']            = $this->getStaffOptions(['option' => $option, 'status' => $state]);
        $returnData['data']['head'] = $data;

        return $returnData;
    }


    /**
     * 员工是否打卡
     * @param $staffId $attendanceDate
     * @return bool
     */
    public function staffIsToWork($staffId, $attendanceDate)
    {
        if (empty($staffId) || empty($attendanceDate)) {
            return false;
        }

        // 判断是否在职
        $staff = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staffId: and state = :state:',
            'bind'       => ['staffId' => $staffId, 'state' => HrStaffInfoModel::STATE_1],
            'columns'    => 'staff_info_id',
        ]);
        if (!$staff){
            return false;
        }

        $staffAuditReissueForBusiness = StaffAuditReissueForBusinessModel::findFirst([
            'conditions' => "staff_info_id = :staffId: and attendance_date = :attendanceDate:",
            'bind'       => ['staffId' => $staffId, 'attendanceDate' => $attendanceDate],
        ]);
        if (!empty($staffAuditReissueForBusiness)){
            return true;
        }

        $data = StaffWorkAttendanceModel::findFirst([
            'conditions' => 'staff_info_id = :staffId: and attendance_date = :attendanceDate:',
            'bind'       => ['staffId' => $staffId, 'attendanceDate' => $attendanceDate],
            'columns'    => 'started_state',
        ]);
        return $data ? ($data->started_state ? true : false) : false;
    }


    /**
     * 里程上报详情
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getMilesInfo($params)
    {
        if (!empty($params['target'])) {
            return $this->milesLogicV2('mileage.get_mileage_info', $params);
        }
        return $this->milesLogic('mileage.get_info', $params);
    }


    /**
     * 里程上报提交
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function addMileageData($params)
    {
        if (!empty($params['target'])) {
            return $this->milesLogicV2('mileage.add_mileage_data', $params);
        }
        return $this->milesLogic('mileage.add_mileage', $params);
    }



    /**
     * 里程上报
     * @param $path
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function milesLogic($path,$params)
    {
        $ac = new ApiClient('bi_rpcv2', '', $path,$this->lang);
        $ac->setParams($params);
        $ac_result = $ac->execute();
        if (isset($ac_result['result']['code'])) {
            return $ac_result['result'];
        }
        throw new ValidationException($this->getTranslation()->_('server_error'));
    }


    /**
     * 新版里程上报
     * @param $path
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function milesLogicV2($path,$params)
    {
        $ac = new ApiClient('ard_api', '', $path, $this->lang);
        $ac->setParams($params);
        $ac_result = $ac->execute();
        if (isset($ac_result['result']['code'])) {
            return $ac_result['result'];
        }
        throw new ValidationException($this->getTranslation()->_('server_error'));
    }



}