<?php

namespace FlashExpress\bi\App\Server\Rpc;

use App\Country\Tools;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\JobtransferServer;
use FlashExpress\bi\App\Server\JobTransferSpecialServer;
use FlashExpress\bi\App\Server\JobTransferV2Server;

class JobTransfer extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

    /**
     * 获取转岗HC
     * @param $locale
     * @param $params
     * @return array
     */
    public function getHcList($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc get_job_transfer_hc_list 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $server = new JobTransferV2Server($locale['locale'], $this->timeZone);
            $data   = $server->getHcList($params);
            $logger->write_log("svc get_job_transfer_hc_list 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE),
                'info');

            return $data;
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc get_job_transfer_hc_list 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc get_job_transfer_hc_list 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 获取转岗HC
     * @param $locale
     * @param $params
     * @return array
     */
    public function getHcListV2($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc get_job_transfer_hc_list 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $server = new JobTransferV2Server($locale['locale'], $this->timeZone);
            $data   = $server->getHcListV2($params);
            $logger->write_log("svc get_job_transfer_hc_list 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE),
                'info');

            return $data;
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc get_job_transfer_hc_list 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc get_job_transfer_hc_list 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 更改转岗相关数据
     * @param $locale
     * @param $params
     * @return array
     */
    public function editJobTransferInfo($locale, $params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc update_job_transfer 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new JobTransferV2Server($locale['locale'], $this->timeZone);
            $data   = $server->editJobTransferInfo($params);
            $logger->write_log("svc update_job_transfer 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE),
                'info');
            return $data;
        } catch (\Exception $e) {
            $logger->write_log("svc update_job_transfer 异常信息:" . $e->getMessage(), 'info');
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 审批转岗申请
     * @param $locale
     * @param $params
     * @return array
     */
    public function updateJobTransfer($locale, $params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc audit_job_transfer 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $server = new JobtransferServer($locale['locale'], $this->timeZone);
            $data   = $server->updateJobtransfer($params);
            $logger->write_log("svc audit_job_transfer 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');

            return $data;

        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc audit_job_transfer 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc audit_job_transfer 异常信息:" . $e->getMessage(), 'notice');
            }

            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 转岗申请审批V2
     * @param $locale
     * @param $params
     * @return array
     */
    public function approvalTransfer($locale, $params)
    {
        $logger = $this->logger;
        $logger->write_log("svc audit_job_transfer_v2 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $server = new JobTransferV2Server($locale['locale'], $this->timeZone);
            $server = Tools::reBuildCountryInstance($server, [$locale['locale'], $this->timeZone]);
            $data   = $server->approvalTransfer($params);
            $logger->write_log("svc audit_job_transfer_v2 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $data;
        } catch (\Exception $e) {
            $logger->write_log("svc audit_job_transfer_v2 异常信息:" . $e->getMessage(), 'info');
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * BP转岗申请审批
     * @param $locale
     * @param $params
     * @return array
     */
    public function bpApproval($locale, $params)
    {
        $logger = $this->logger;
        $logger->write_log("svc bp_audit_job_transfer 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $server = new JobTransferV2Server($locale['locale'], $this->timeZone);
            $server = Tools::reBuildCountryInstance($server, [$locale['locale'], $this->timeZone]);
            $data   = $server->bpApproval($params);
            $logger->write_log("svc bp_audit_job_transfer 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $data;
        } catch (ValidationException $ve) {
            $logger->write_log("svc bp_audit_job_transfer 异常信息:" . $ve->getMessage(), 'info');
            $return['code'] = -1;
            $return['msg']  = $ve->getMessage();
            return $return;
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc bp_audit_job_transfer 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc bp_audit_job_transfer 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 获取转岗进程
     * @param $locale
     * @param $params
     * @return array|bool
     */
    public function getJobTransferProcess($locale, $params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc get_transfer_process 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $server = new JobTransferV2Server($locale['locale'], $this->timeZone);
            $data   = $server->getJobtransferProcess($params);
            $logger->write_log("svc get_transfer_process 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE),
                'info');

            return $data;
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc get_transfer_process 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc get_transfer_process 异常信息:" . $e->getMessage(), 'notice');
            }

            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 获取转岗后上级
     * @param $locale
     * @param $params
     * @return array|mixed
     */
    public function getAfterManagerId($locale, $params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc getAfterManagerId 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $server = new JobTransferSpecialServer($locale['locale'], $this->timeZone);
            $data   = $server->getAfterManagerIdInfo($params);
            $logger->write_log("svc getAfterManagerId 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');

            return $data;
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc getAfterManagerId 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc getAfterManagerId 异常信息:" . $e->getMessage(), 'notice');
            }

            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 获取转岗操作日志
     * @param $locale
     * @param $params
     * @return array|mixed
     */
    public function getJobTransferOperateLog($locale, $params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc get_apply_detail 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $server = new ApprovalServer($locale['locale'], $this->timeZone);
            $data   = $server->getAuditDetail($params['id'], $params['type'], $params['user'], $params['come_from'],
                null, $params['date_created'] ?? '', 2);
            $logger->write_log("svc get_apply_detail 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');

            $jobTransferServer = new JobTransferV2Server($locale['locale'], $this->timeZone);
            $log               = $jobTransferServer->getJobTransferOperateLog(['id' => $params['id']]);
            $data['log']       = $log;
            return $data;
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc get_apply_detail 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc get_apply_detail 异常信息:" . $e->getMessage(), 'notice');
            }

            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 获取转岗操作日志
     * @param $locale
     * @param $params
     * @return array|mixed
     */
    public function getTransferOperateLog($locale, $params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc getTransferOperateLog 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $jobTransferServer = new JobtransferServer($locale['locale'], $this->timeZone);
            $log               = $jobTransferServer->getJobTransferOperateLog(['id' => $params['id']]);
            $data['log']       = $log;
            return $data;
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc getTransferOperateLog 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc getTransferOperateLog 异常信息:" . $e->getMessage(), 'notice');
            }

            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 校验是否可以转岗
     * @param $locale
     * @param $params
     * @return array
     */
    public function checkApply($locale, $params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc check_apply 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $jobTransferServer = new JobtransferV2Server($locale['locale'], $this->timeZone);
            $jobTransferServer = Tools::reBuildCountryInstance($jobTransferServer, [$locale['locale'], $this->timeZone]);
            return $jobTransferServer->checkApply($params);
        } catch (\Exception $e) {
            $logger->write_log("svc check_apply 异常信息:" . $e->getMessage(), 'info');
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 批量校验转岗
     * @param $locale
     * @param $params
     * @return array
     */
    public function checkJobTransferApplyV2($locale, $params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc check_batch_apply 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $jobTransferServer = new JobTransferV2Server($locale['locale'], $this->timeZone);
            $jobTransferServer = Tools::reBuildCountryInstance($jobTransferServer, [$locale['locale'], $this->timeZone]);

            if (isCountry(['TH', 'MY', 'PH'])) {
                $data = $jobTransferServer->checkJobTransferApplyV2($params);
            } else {
                $data = $jobTransferServer->checkJobTransferApply($params);
            }
            return $data;
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc check_batch_apply 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc check_batch_apply 异常信息:" . $e->getMessage(), 'notice');
            }

            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 获取转岗进程
     * @param $locale
     * @param $params
     * @return array
     */
    public function getBatchAddProgress($locale, $params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc get_batch_add_progress 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $jobTransferServer = new JobTransferV2Server($locale['locale'], $this->timeZone);
            $data = $jobTransferServer->getBatchAddProgress($params);

            return $data;

        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc get_batch_add_progress 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc get_batch_add_progress 异常信息:" . $e->getMessage(), 'notice');
            }

            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 设置批量添加编号
     * @param $locale
     * @param $params
     * @return array|bool
     */
    public function setBatchAddNumber($locale, $params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc set_batch_add_number 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');

        try {
            $jobTransferServer = new JobTransferV2Server($locale['locale'], $this->timeZone);
            return $jobTransferServer->setBatchAddNumber($params);

        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc set_batch_add_number 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc set_batch_add_number 异常信息:" . $e->getMessage(), 'notice');
            }

            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 激活转岗
     * @param $locale
     * @param $params
     * @return array|bool
     */
    public function activateTransfer($locale, $params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc activate_job_transfer 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $jobTransferServer = new JobTransferV2Server($locale['locale'], $this->timeZone);
            $jobTransferServer = Tools::reBuildCountryInstance($jobTransferServer, [$locale['locale'], $this->timeZone]);
            $result = $jobTransferServer->activate($params['id'], $params['operator_id']);
            return $jobTransferServer->checkReturn(['data' => $result]);
        } catch (\Exception $e) {
            $logger->write_log("svc activate_job_transfer 异常信息:" . $e->getMessage(), 'info');
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 创建特殊转岗申请
     * @param $locale
     * @param $params
     * @return array
     */
    public function createSpecialJobTransfer($locale, $params): ?array
    {
        $this->logger->write_log("svc createSpecialJobTransfer 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $jobTransferServer = new JobTransferSpecialServer($locale['locale'], $this->timeZone);
            return $jobTransferServer->create($params);
        } catch (\Exception $e) {
            $this->logger->write_log("svc createSpecialJobTransfer 异常信息:" . $e->getMessage(), 'info');
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 获取有效的雇佣类型
     * @param $locale
     * @param $params
     * @return array|null
     */
    public function getTransferHireTypeList($locale, $params): ?array
    {
        $this->logger->write_log("svc getTransferHireTypeList 参数列表 locale:" . json_encode($locale,
                JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $jobTransferServer = new JobTransferV2Server($locale['locale'], $this->timeZone);
            $validateHireType =  $jobTransferServer->getDefaultHireType($params);
            $result = [];
            foreach ($jobTransferServer->getHireType() as $hireType) {
                if (in_array($hireType['value'], $validateHireType['hire_type'])) {
                    $result[] = $hireType;
                }
            }
            return $jobTransferServer->checkReturn(['data' => $result]);
        } catch (\Exception $e) {
            $this->logger->write_log("svc getTransferHireTypeList 异常信息:" . $e->getMessage(), 'info');
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }
}

