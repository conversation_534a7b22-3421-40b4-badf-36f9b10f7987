<?php

namespace FlashExpress\bi\App\Server\Rpc;

use App\Country\Tools;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\BusinesstripServer;

class BusinessTrip extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

    /**
     * 验证员工是否出差
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function checkStaffIsTrip($locale, $params)
    {
        if(empty($params['staff_info_id']) || empty($params['date_at'])){
            throw new ValidationException('params error [staff_info_id] or [date_at] empty');
        }

        $businessServe = new BusinesstripServer($locale['locale'], $this->timeZone);
        $businessInfo = $businessServe->check_info_by_date($params['staff_info_id'], $params['date_at']);
        return $businessServe->checkReturn(['data' =>['is_trip' => !empty($businessInfo)]]);
    }

    public function addTripRpc($locale, $params){
        $server = new BusinesstripServer($locale['locale'], $this->timeZone);
        $server = Tools::reBuildCountryInstance($server,[$locale['locale'], $this->timeZone]);
        $staffRe = new StaffRepository($locale['locale']);

        $staffInfo = $staffRe->getStaffPosition($params['staff_info_id']);
        $params['is_hcm'] = 1;
        $res = $server->addTrip($params,$staffInfo);
        return $res;
    }

    public function getBusinessTripListRpc($locale, $params)
    {
        $server = new BusinesstripServer($locale['locale'], $this->timeZone);
        $server = Tools::reBuildCountryInstance($server, [$locale['locale'], $this->timeZone]);

        return $server->getTripListBySerialNoItem($params['serial_no_item'] ?? []);
    }


}