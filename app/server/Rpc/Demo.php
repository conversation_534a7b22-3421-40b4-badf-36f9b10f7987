<?php

namespace FlashExpress\bi\App\Server\Rpc;

use FlashExpress\bi\App\Server\AuditServer;

class Demo extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

    public function test($locale, $params)
    {
        return "This is Demo::test";
    }

    public function getPendingCountV1($locale, $paramIn)
    {
        $server = new AuditServer($locale['locale'], $this->timeZone);
        return $server->getWaitAuditNum($paramIn['staff_id']);
    }

    public function getPendingCountV2($locale, $paramIn)
    {
        $server = new AuditServer($locale['locale'], $this->timeZone);
        return $server->getWaitAuditNumV2($paramIn['staff_id']);
    }
}