<?php

namespace FlashExpress\bi\App\Server\Rpc;

use App\Country\Tools;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\BusinesstripServer;
use FlashExpress\bi\App\Server\SysStoreServer;

class Store extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

    /**
     * 获取网点负责人信息
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getStoreManagerContactPersonInfo($locale, $params)
    {
        if (empty($params['store_name']) && empty($params['store_id'])) {
            throw new ValidationException('params error [store_name] & [store_id] empty');
        }
        $storeServer              = new SysStoreServer($locale['locale'], $this->timeZone);
        $storeInfo                = $storeServer->getStoreInfo($params, 'id,lat,lng,detail_address');
        $result['store_info']     = $storeInfo;
        $result['contact_person'] = [];
        if (!empty($storeInfo)) {
            $staffInfo                = HrstaffInfoModel::find([
                'conditions' => 'sys_store_id = :sys_store_id: and state = 1 and formal = 1 and is_sub_staff = 0 and job_title = :job_title:',
                'bind'       => ['sys_store_id' => $storeInfo['id'], 'job_title' => $params['job_title'] ?? 0],
                'columns'    => 'staff_info_id,name,mobile,email,personal_email,job_title',
            ]);
            $result['contact_person'] = $staffInfo;
        }
        return $result;

    }



}