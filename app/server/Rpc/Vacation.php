<?php

namespace FlashExpress\bi\App\Server\Rpc;

use App\Country\Tools;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Server\LeaveServer;

class Vacation extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

    public function getFamilyDeathEnum($locale, $param)
    {
        try {
            $server = new LeaveServer($locale['locale'], $this->timeZone);
            $server = Tools::reBuildCountryInstance($server, [$locale['locale'], $this->timeZone]);
            $data   = $server->getDeathEnum($param);
            $this->logger->write_log("getFamilyDeathEnum svc " . json_encode($param), 'info');
            return ['code' => ErrCode::SUCCESS, 'msg' => '', 'data' => $data];
        } catch (\Exception $e) {
            $this->logger->write_log("getFamilyDeathEnum svc 异常信息:" . $e->getMessage());
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }


}

