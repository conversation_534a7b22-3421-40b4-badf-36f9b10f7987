<?php

namespace FlashExpress\bi\App\Server\Rpc;

use App\Country\Tools;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\CourierResumeRecommendServer;
use FlashExpress\bi\App\Server\PersoninfoServer;

class InternalRecruitRpc extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;
    
    public function internalRecruitGenerateImg($locale, $params): array
    {
        if (!isCountry('MY')) {
            throw new ValidationException('country error');
        }
        $server = (new CourierResumeRecommendServer($locale['locale'], $this->timezone));
        $data = $server->internalRecruitGenerateImg($params);
        return $server->checkReturn(['data' => $data]);
    }

    public function internalRecruitGenerateQrCode($locale, $params): array
    {
        if (!isCountry('MY')) {
            throw new ValidationException('country error');
        }
        $server = (new CourierResumeRecommendServer($locale['locale'], $this->timezone));
        $data = $server->generateQrCode($params);
        return $server->checkReturn(['data' => $data]);
    }
}

