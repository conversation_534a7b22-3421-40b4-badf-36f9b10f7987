<?php

namespace FlashExpress\bi\App\Server\Rpc;

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Server\ProbationServer;

class Probation extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;


    /**
     * 转正评估激活
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function active($locale, $params): array
    {
        $server = (new ProbationServer($locale['locale'], $this->timeZone));
        /**
         * @see ProbationServer::active()
         */
        return $server->activeUseLock($params);

    }
    /**
     * ph 合同工v3 系统后台编辑分数 给被评估人发信息
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function submitProbationEvaluationContractV3FromHcm($locale, $params): array
    {
        if (!isCountry('PH')) {
            throw new ValidationException('submitProbationEvaluationContractV3FromHcm is not a PH country');
        }
        $server = (new \FlashExpress\bi\App\Modules\Ph\Server\ProbationServer($locale['locale'], $this->timeZone));
        return $server->submitProbationEvaluationContractV3FromHcmUseLock($params['probation_audit_id']);
    }

    /**
     * 详情 - 非一线
     * @throws BusinessException
     * @throws ValidationException
     */
    public function detailNonFrontLine($locale, $params): array
    {
        $server = (new ProbationServer($locale['locale'], $this->timeZone));
        return $server->detailNonFrontLine($params);
    }

    /**
     * by的分数提交
     * @throws BusinessException
     * @throws ValidationException
     */
    public function scoreSubmitNonFrontLine($locale, $params): array
    {
        $server = (new ProbationServer($locale['locale'], $this->timeZone));
        /**
         * @see ProbationServer::scoreSubmitNonFrontLineHcm()
         */
        return $server->scoreSubmitNonFrontLineHcmUseLock($params);
    }
    
    public function sendProbationFormalMessage($locale, $params)
    {
        $server = (new ProbationServer($locale['locale'], $this->timeZone));
        /**
         * @see ProbationServer::sendProbationFormalMessage()
         */
        return $server->sendProbationFormalMessageUseLock($params);
    }
}

