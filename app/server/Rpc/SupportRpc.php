<?php

namespace FlashExpress\bi\App\Server\Rpc;

use App\Country\Tools;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Server\OsOvertimeServer;
use FlashExpress\bi\App\Server\OvertimeServer;
use FlashExpress\bi\App\Server\StaffSupportStoreServer;

class SupportRpc extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;


    /**
     * 获取打了上班卡的员工，并排除支援的员工
     * @param $locale
     * @param $param
     * @return array
     */
    public function onSupportStaff($locale, $param)
    {
        try {
            $server = $this->class_factory('StaffSupportStoreServer', $locale['locale'], $this->timeZone);
            $data   = $server->getMissionStaff($param);
            $this->logger->write_log("onSupportStaff ".json_encode($param), 'info');
            return $server->checkReturn(['data' => $data]);
        } catch (\Exception $e) {
            $this->logger->write_log("overtimeApprovalList 异常信息:".$e->getMessage());
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }
}

