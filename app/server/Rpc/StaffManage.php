<?php

namespace FlashExpress\bi\App\Server\Rpc;

use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffManageRegionModel;
use FlashExpress\bi\App\Server\StaffManageServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\WorkflowServer;

class StaffManage extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

    /**
     * 管辖员工的HRBP
     * @param $locale
     * @param $params
     * @return string
     */
    public function findHRBP($locale, $params)
    {
        $staffInfo = (new StaffServer())->getStaffById($params['staff_info_id'], 'node_department_id,sys_store_id');
        return (new WorkflowServer($locale['locale'], $this->timeZone))->findHRBP($staffInfo['node_department_id'],
            ["store_id" => $staffInfo['sys_store_id']]);
    }

    /**
     * 给crm提供的员工列表
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getStaffListForCRM($locale, $params): array
    {
        $params['src'] = 'crm';
        $result = (new StaffServer($locale['locale']))->getStaffFullInfoList($params);
        return $this->checkReturn(['data'=>$result,'msg'=>'success']);
    }

    /**
     * 获取员工关系的大区 片区
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getStaffManageRegionPieceList($locale, $params): array
    {
        if (empty($params['staff_ids']) || !is_array($params['staff_ids'])) {
            throw new ValidationException('miss args staff_ids array');
        }
        $server = new StaffManageServer($locale['locale']);
        $result = $server->getStaffManageRegionAndPiece($params['staff_ids'],HrStaffManageRegionModel::TYPE_STAFF_MANAGEMENT);
        return $this->checkReturn(['data'=>$result,'msg'=>'success']);
    }

}