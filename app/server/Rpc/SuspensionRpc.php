<?php
/**
 * Author: Bruce
 * Date  : 2025-01-06 21:28
 * Description:
 */

namespace FlashExpress\bi\App\Server\Rpc;


use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\ReinstatementServer;
use FlashExpress\bi\App\Server\SuspensionServer;
use GuzzleHttp2\Promise\RejectionException;
use ReflectionException;

/**
 * 停职、恢复在职相关 Rpc 调用
 */
class SuspensionRpc extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

    public function suspensionAudit($locale, $params)
    {
        $validations = [
            "staff_id"   => "Required|StrLenGe:1",
            "staff_name" => "Required|StrLenGe:1",
            "status"   => "Required|IntIn:2,3",
            "audit_id" => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",
        ];
        $suspensionServer = new SuspensionServer($locale['locale'], $this->timeZone);
        $approvalServer = new ApprovalServer($locale['locale'], $this->timeZone);
        if ($params['status'] == enums::APPROVAL_STATUS_APPROVAL &&
            $approvalServer->isExistCanEditField($params['audit_id'], AuditListEnums::APPROVAL_TYPE_SUSPENSION)) {
            //开始校验提交字段
            $erValidations = $suspensionServer->getErValidate($params);
            $this->validateCheck($params, array_merge($validations, $erValidations));
            //提交审核
            $returnArr = $suspensionServer->auditSubmitUseLock($params);

        } else {
            $this->validateCheck($params, $validations);

            $returnArr = $suspensionServer->approvalSubmitUseLock($params);
        }

        return $suspensionServer->checkReturn($returnArr);
    }

    /**
     * 验 停职工号是否有 有效停职的申请
     * @param $locale
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function suspensionAuditCheck($locale, $params)
    {
        $data['result'] = true;
        $data['date'] = '';

        $validations = [
            "staff_id"   => "Required|StrLenGe:1",
            "staff_name" => "Required|StrLenGe:1",
            "status"   => "Required|IntIn:2,3",
            "audit_id" => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",
        ];

        $suspensionServer = new SuspensionServer($locale['locale'], $this->timeZone);
        $approvalServer = new ApprovalServer($locale['locale'], $this->timeZone);
        if ($params['status'] == enums::APPROVAL_STATUS_APPROVAL &&
            $approvalServer->isExistCanEditField($params['audit_id'], AuditListEnums::APPROVAL_TYPE_SUSPENSION)) {

            //开始校验提交字段
            $erValidations = $suspensionServer->getErValidate($params);
            $this->validateCheck($params, array_merge($validations, $erValidations));
            $data = $suspensionServer->checkInfoEr($params);
        }

        return $suspensionServer->checkReturn(['data' => $data]);
    }

    /**
     * 恢复在职
     * @param $locale
     * @param $params
     * @return array
     * @throws ReflectionException|ValidationException
     */
    public function reinstatement($locale, $params) : array
    {
        try {
            $reinstatementServer = new ReinstatementServer($locale['locale'], $this->timeZone);
            return $reinstatementServer->reinstatementWithoutHold($params);
        } catch (ValidationException|RejectionException $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $this->logger->write_log("svc reinstatement 异常信息:" . $e->getMessage(), 'info');
            } else {
                $this->logger->write_log("svc reinstatement 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = ErrCode::VALIDATE_ERROR;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }
}