<?php

namespace FlashExpress\bi\App\Server\Rpc;

use FlashExpress\bi\App\Enums\WorkflowEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditListServer;
use FlashExpress\bi\App\Server\SystemExternalApprovalServer;

class ApprovalRpc extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

    /**
     * 系统驳回
     * @description 将待审批、审批已同意的审批驳回
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function systemReject($locale, $params): array
    {
        $server = new ApprovalServer($locale['locale'], $this->timeZone);
        $res    = $server->approvalSystemReject($params['audit_id'], $params['audit_type'],
            $params['reject_reason'], enums::SYSTEM_STAFF_ID, ['super' => 1]);
        return $server->checkReturn(['data' => $res]);
    }

    /**
     * 创建审批
     * @param $locale
     * @param $params
     * @return array
     */
    public function SystemExternalApprovalAdd($locale, $params): array
    {
        //外部系统创建审批
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc SystemExternalApprovalAdd 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new SystemExternalApprovalServer($locale['locale'], $this->timeZone);
            $data = $server->addApproval($params);
            $logger->write_log("svc SystemExternalApprovalAdd 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $data;
        } catch (\Exception $e) {

            $logger->write_log("svc SystemExternalApprovalAdd 异常信息:" . $e->getMessage(), 'error');

            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 编辑业务数据后，重新提交
     * @description 提交的审批编号不变，重新创建审批流重新审批；
     * 本次审批状态与前一次审批无关联；
     * 但是，审批日志要求与前一次在一起
     *
     * @param $locale
     * @param $params
     * @return array
     */
    public function SystemExternalApprovalEdit($locale, $params): array
    {
        //外部系统创建审批
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc SystemExternalApprovalEdit 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new SystemExternalApprovalServer($locale['locale'], $this->timeZone);
            $data = $server->editApproval($params);
            $logger->write_log("svc SystemExternalApprovalEdit 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $data;
        } catch (\Exception $e) {

            $logger->write_log("svc SystemExternalApprovalEdit 异常信息:" . $e->getMessage(), 'error');

            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 对外审批接口
     * @param $locale
     * @param $params
     * @return array
     */
    public function SystemExternalApprovalUpdate($locale, $params): array
    {
        //外部系统操作审批
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc SystemExternalApprovalUpdate 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new SystemExternalApprovalServer($locale['locale'], $this->timeZone);
            $data = $server->updateApprove($params);
            $logger->write_log("svc SystemExternalApprovalUpdate 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $data;
        } catch (\Exception $e) {

            $logger->write_log("svc SystemExternalApprovalUpdate 异常信息:" . $e->getMessage(), 'error');

            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 对外获取审批日志接口
     * @param $locale
     * @param $params
     * @return array
     */
    public function SystemExternalApprovalGetLog($locale, $params): array
    {
        //外部系统操作审批
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc SystemExternalApprovalGetLog 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new SystemExternalApprovalServer($locale['locale'], $this->timeZone);
            $data = $server->getApprovalLog($params);
            $logger->write_log("svc SystemExternalApprovalGetLog 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $data;
        } catch (\Exception $e) {

            $logger->write_log("svc SystemExternalApprovalGetLog 异常信息:" . $e->getMessage(), 'error');

            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 对外获取待审批列表
     * @param $locale
     * @param $params
     * @return array
     */
    public function SystemExternalApprovalGetList($locale, $params): array
    {
        //外部系统操作审批
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc SystemExternalApprovalGetList 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new SystemExternalApprovalServer($locale['locale'], $this->timeZone);
            $data = $server->systemExternalApprovalGetList($params);
            $logger->write_log("svc SystemExternalApprovalGetList 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $data;
        } catch (\Exception $e) {

            $logger->write_log("svc SystemExternalApprovalGetList 异常信息:" . $e->getMessage(), 'error');

            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 对外获取待审批count
     * @param $locale
     * @param $params
     * @return array
     */
    public function SystemExternalApprovalGetPendingCount($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc SystemExternalApprovalGetPendingCount 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new AuditListServer($locale['locale'], $this->timeZone);
            $data = $server->getAuditListPendingCountV2($params);
            $logger->write_log("svc SystemExternalApprovalGetPendingCount 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc SystemExternalApprovalGetPendingCount 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc SystemExternalApprovalGetPendingCount 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 对外获取待审批全部数据列表
     * @param $locale
     * @param $params
     * @return array
     */
    public function SystemExternalApprovalGetAllList($locale, $params): array
    {
        //外部系统操作审批
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc SystemExternalApprovalGetAllList 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new SystemExternalApprovalServer($locale['locale'], $this->timeZone);
            $data = $server->systemExternalApprovalGetAllList($params);
            $logger->write_log("svc SystemExternalApprovalGetAllList 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $data;
        } catch (\Exception $e) {

            $logger->write_log("svc SystemExternalApprovalGetAllList 异常信息:" . $e->getMessage(), 'error');

            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 获取众包参考数据
     * @param $locale
     * @param $params
     * @return array
     */
    public function SystemExternalReferenceData($locale, $params): array
    {
        //外部系统操作审批
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc SystemExternalReferenceData 参数列表 locale " . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new SystemExternalApprovalServer($locale['locale'], $this->timeZone);
            $data = $server->getReferenceData($params);
            $logger->write_log("svc SystemExternalReferenceData 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $data;
        } catch (\Exception $e) {
            $logger->write_log("svc SystemExternalReferenceData 异常信息:" . $e->getMessage(), 'error');
            $return['data'] = null;
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 根据serial No获取指定工号的待审批
     * @param $locale
     * @param $params
     * @return array
     */
    public function SystemExternalGetStaffPendingBySerialNo($locale, $params): array
    {
        //外部系统操作审批
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc SystemExternalGetStaffPendingBySerialNo 参数列表 locale " . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new SystemExternalApprovalServer($locale['locale'], $this->timeZone);
            $data = $server->systemExternalGetStaffPendingBySerialNo($params);
            $logger->write_log("svc SystemExternalGetStaffPendingBySerialNo 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $data;
        } catch (\Exception $e) {
            $logger->write_log("svc SystemExternalGetStaffPendingBySerialNo 异常信息:" . $e->getMessage(), 'error');
            $return['data'] = null;
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 对外提供超时接口
     * @param $locale
     * @param $params
     * @return array
     */
    public function SystemExternalTimeOut($locale, $params): array
    {
        //对外提供审批超时接口
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc SystemExternalTimeOut 参数列表 locale:".json_encode($locale,
                JSON_UNESCAPED_UNICODE).";param:".json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new SystemExternalApprovalServer($locale['locale'], $this->timeZone);
            $data   = $server->timeOut($params);
            $logger->write_log("svc SystemExternalTimeOut 数据返回:".json_encode($data, JSON_UNESCAPED_UNICODE),
                'info');
            return $data;
        } catch (\Exception $e) {
            $logger->write_log("svc SystemExternalTimeOut 异常信息:".$e->getMessage(), 'error');
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 获取 审批流编号
     * @param $locale
     * @param $params
     * @return array
     */
    public function getRandomIdNo($locale, $params): array
    {
        $server = new ApprovalServer($locale['locale'], $this->timeZone);
        return $server->checkReturn(['data' => $this->getRandomId()]);
    }
}