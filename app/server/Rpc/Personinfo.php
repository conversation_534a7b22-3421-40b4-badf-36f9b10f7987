<?php

namespace FlashExpress\bi\App\Server\Rpc;

use App\Country\Tools;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\PersoninfoServer;

class Personinfo extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;
    
    public function aiBankCard($locale, $params): array
    {
        if (!isCountry('TH')) {
            throw new ValidationException('aiBankCard is not a TH country');
        }
        $server = Tools::reBuildCountryInstance(new PersoninfoServer($locale['locale'], $this->timeZone), [$locale['locale'], $this->timeZone]);
        $bank_card_url = $params['bank_card_url'] ?? '';
        $staff_info_id = $params['staff_info_id'] ?? '';
        $bank_no = $params['bank_no'] ?? '';
        $bank_type = $params['bank_type'] ?? 0;
        $data = $server->ai_bank_card($bank_card_url, $staff_info_id, $bank_no,$bank_type);
        return $server->checkReturn(['data' => $data]);
    }
}

