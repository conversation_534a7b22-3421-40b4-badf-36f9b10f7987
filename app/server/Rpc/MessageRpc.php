<?php
/**
 * Author: Bruce
 * Date  : 2025-06-26 13:05
 * Description:
 */

namespace FlashExpress\bi\App\Server\Rpc;


use FlashExpress\bi\App\Server\MessageCourierServer;

class MessageRpc extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

    /**
     * 计算kit 未读消息红点
     * @param $locale
     * @param $params
     * @return array
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function getMessageHotNum($locale, $params): array
    {
        $server = new MessageCourierServer($locale['locale'], $this->timeZone);
        return $server->checkReturn(['data' => $server->getMessageHotNum($params)]);
    }
}