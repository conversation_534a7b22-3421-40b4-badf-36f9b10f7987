<?php

namespace FlashExpress\bi\App\Server;

/**
 * HC 共享预算服务
 * @description 将配置"32,18|13,110#50|78,153",转换为下面的数据结构
 *
 *  Array
 *  (
 *       [group] => Array
 *           (
 *    共享组 ==>   [0 <==共享组ID] => Array
 *                   (
 *                       [32 <==部门ID] => Array
 *                           (
 *                               [0] => 13  <==职位ID
 *                               [1] => 110 <==职位ID
 *                           )
 *                       [18] => Array
 *                           (
 *                               [0] => 13
 *                               [1] => 110
 *                           )
 *                   )
 *               [1] => Array
 *                   (
 *                       [50] => Array
 *                           (
 *                               [0] => 78
 *                               [1] => 153
 *                          )
 *                  )
 *          )
 *       [index] => Array
 *           (
 *               [部门ID-职位ID] => 共享组ID, //示例
 *               [32-13] => 0
 *               [32-110] => 0
 *               [18-13] => 0
 *               [18-110] => 0
 *               [50-78] => 1
 *               [50-153] => 1
 *           )
 *  )
 */
class HcShareBudgetServer extends BaseServer
{
    private static $single = null;
    private $share_group = [];
    private $share_group_index = [];
    public static function getInstance(): HcShareBudgetServer
    {
        if (!self::$single) {
            self::$single = new self();
        }
        return self::$single;
    }

    /**
     * 初始化共享配置
     * @return HcShareBudgetServer
     */
    public function init(): HcShareBudgetServer
    {
        $envModel = new SettingEnvServer();
        $shareCnf = $envModel->getSetVal('hc_share_department_position', '#');
        $this->parseDepartmentJobTitleShareConfig($shareCnf);
        return $this;
    }

    /**
     * 获取共享组
     * @param $department_id
     * @param $job_title_id
     * @return array|mixed
     */
    public function getShareGroup($department_id, $job_title_id)
    {
        if (empty($department_id) || empty($job_title_id)) {
            return [];
        }
        $searchKey = $this->getUniqueKey($department_id, $job_title_id);
        $groupId = $this->share_group_index[$searchKey] ?? null;
        if (is_null($groupId)) {
            return [];
        }
        return $this->share_group[$groupId];
    }

    /**
     * 获取共享组 ID
     * @param $department_id
     * @param $job_title_id
     * @return mixed|null
     */
    public function getShareGroupId($department_id, $job_title_id)
    {
        if (empty($department_id) || empty($job_title_id)) {
            return null;
        }
        $searchKey = $this->getUniqueKey($department_id, $job_title_id);
        $groupId = $this->share_group_index[$searchKey] ?? null;
        if (is_null($groupId)) {
            return null;
        }
        return $groupId;
    }

    /**
     * 根据共享组 ID 获取索引 key （部门ID-职位ID）
     * @param $group_id
     * @return array
     */
    public function getUniqueKeyByGroupId($group_id): array
    {
        if (!is_numeric($group_id)) {
            return [];
        }

        if (!isset($this->share_group[$group_id])) {
            return [];
        }
        return array_keys($this->share_group_index, $group_id);
    }

    /**
     * 是否存在共享
     * @param $department_id
     * @param $job_title_id
     * @return bool
     */
    public function isShare($department_id, $job_title_id): bool
    {
        if (empty($department_id) || empty($job_title_id)) {
            return false;
        }
        $searchKey = $this->getUniqueKey($department_id, $job_title_id);
        $groupId   = $this->share_group_index[$searchKey] ?? null;
        return !is_null($groupId);
    }

    /**
     * @param $department_id
     * @param $job_title_id
     * @return string
     */
    private function getUniqueKey($department_id, $job_title_id): string
    {
        return sprintf('%d-%d', $job_title_id, $department_id);
    }

    /**
     * 解析共享配置
     * @param array $share_config
     * @return void
     */
    private function parseDepartmentJobTitleShareConfig(array $share_config)
    {
        if (empty($share_config)) {
            return;
        }

        $groupIndex = [];
        $group      = [];
        foreach ($share_config as $group_id => $group_config) {
            if (!strpos($group_config, '|')) {
                $this->logger->write_log('hc_share_department_position config incomplete err:' . json_encode($group_config));
                continue;
            }
            [$departmentStr, $jobTitleStr] = explode('|', $group_config);
            $departmentIds = !empty($departmentStr) ? explode(',', $departmentStr) : [];
            $jobTitleIds   = !empty($jobTitleStr) ? explode(',', $jobTitleStr) : [];
            [$tmp_group, $index] = $this->formatGroup($departmentIds, $jobTitleIds, $group_id, $groupIndex);
            $groupIndex += $index;
            !empty($tmp_group) && $group[$group_id] = $tmp_group;
        }
        $this->share_group       = $group;
        $this->share_group_index = $groupIndex;
        //print_r($group);
        //print_r($groupIndex);
    }

    /**
     * 构建索引，共享组数据
     * @param array $departmentIds 共享部门 IDS
     * @param array $jobTitleIds 共享职位 IDS
     * @param int $group_id 共享组 ID
     * @param array $group_index 共享组索引
     * @return array[]
     */
    private function formatGroup(array $departmentIds, array $jobTitleIds, int $group_id, array $group_index): array
    {
        $shareGroup = $shareGroupIndex = [];
        foreach ($departmentIds as $departmentId) {

            //获取“部门ID-职位ID”键值
            $shareGroupIndexKeys = array_map(function ($jobTitleId) use ($departmentId) {
                return $this->getUniqueKey($departmentId, $jobTitleId);
            }, $jobTitleIds);

            //获取是否存在重复的配置，如存在则该配置失效
            $intersect = array_intersect(array_keys($group_index), $shareGroupIndexKeys);
            if (!empty($intersect)) {
                $this->logger->write_log('hc_share_department_position config repeat err:' . json_encode($intersect));
                break;
            }

            //生成共享组数据 [部门 ID] => [职位ID1, 职位ID2]
            $shareGroup[$departmentId] = $jobTitleIds;

            //生成索引数据
            $shareGroupIndex += array_combine(
                $shareGroupIndexKeys,
                array_fill(0, count($jobTitleIds), $group_id)
            );
        }
        return [$shareGroup, $shareGroupIndex];
    }
}