<?php

namespace FlashExpress\bi\App\Server;

use Phalcon\DiInterface;

class ReissueCardServer extends AuditBaseServer
{

    public function __construct($lang = 'zh-CN', DiInterface $di = null)
    {
        parent::__construct($lang, $di);
    }

    public function addReissueCard()
    {

    }
    
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        // TODO: Implement setProperty() method.
    }

    public function getDetail(int $auditId, $user, $comeFrom)
    {
        // TODO: Implement getDetail() method.
    }

    public function genSummary(int $auditId, $user)
    {

    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {

    }
}

