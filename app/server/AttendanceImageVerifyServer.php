<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Enums\AiStateEnums;
use FlashExpress\bi\App\Enums\AttendanceEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\StaffWorkAttendanceRepository;
use FlashExpress\bi\App\Traits\FactoryTrait;

class AttendanceImageVerifyServer extends BaseServer
{
    use FactoryTrait;
    const ALIVE_CONFIG_ALL = 1; //活体检测-全体
    const REDIS_KEY_IMAGE_VERIFY_ERR_NO = 'face_verify_error';

    const REDIS_KEY_FORCE_SWITCH_TO_TENCENT = 'face_two_hour';
    const COMPARE_FACE_SCORE = 80; //人脸比对分数


    //配置名单
    protected $setting_code = [
        'live_list',                 //活体检测范围
        'alive_score',               //活体检测分数
        'attendance_type',           //人脸比对接口配置 0=腾讯与内部AI按百分比配置 1=腾讯接口 3=内部AI接口
        'face_percent',              //腾讯与内部AI按百分比配置,例如： {1:9: 3:1}含义是：腾讯90%，内部AI 10%
        'attendance_face_top',       //内部AI报错最高次数(达到最高次数将触发，人脸比对接口强制切换)
        'ai_mask_switch',            //口罩开关
        'ai_mask_score',             //口罩检测分数
        'server_point_internal',     //图片内部地址
        'server_point',              //图片通用地址
        'facial_recognition_mailbox',//人脸识别反馈渠道
    ];

    //保存配置
    protected $configure = [];
    //用户底片
    protected $staff_face_image = '';
    //用户信息
    protected $user_info = [];
    //用户详情
    protected $staff_info = [];
    //是否需要将底片保存到主账号
    protected $isSaveMasterFlag = false;
    //主账号工号
    protected $masterStaffInfoId = null;

    /**
     * @var AttendanceServer
     */
    protected $attendance_server = null;
    protected $params = [];

    public function __construct($lang, $timezone = '+08:00')
    {
        parent::__construct($lang, $timezone);
        $this->configure         = (new SettingEnvServer())->listByCodeFromCache($this->setting_code);
        $this->configure         = array_column($this->configure, 'set_val', 'code');
        $this->attendance_server = $this->class_factory('AttendanceServer', $this->lang, $this->timeZone);
    }

    /**
     * 校验传入图片与底片的匹配
     * @param array $params
     * @return void
     * @throws ValidationException
     */
    public function verifyImage($params = [])
    {
        $staffInfoId        = $params['staff_info_id'];
        $sourceUrl          = $params['source_url'] ?? '';
        $clickUrl           = $params['click_url'];
        $this->user_info    = $params['user_info'];
        $useInternal        = $params['use_internal'] = false;
        $attendanceCategory = $params['attendance_category'];
        $params['source']   = AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_ATTENDANCE;
        $params['store_id'] = $this->user_info['organization_id'];
        $this->params       = $params;

        //[1]活体检测
        if ($this->checkLiveDetect($staffInfoId)) {
            $this->doLiveDetect($params);
        }

        //获取配置的人脸比对类型
        //获取员工人脸底片 & 如使用内部AI(服务)类型 & 生产环境，则地址使用内部访问地址
        $faceCompareType = $this->getFaceCompareType();
        if ($faceCompareType == AiStateEnums::COMPARE_FACE_TYPE_INTERNAL_AI && RUNTIME == 'pro') {
            $useInternal = $params['use_internal'] = true;
        }
        $this->staff_face_image = $this->getStaffFaceImage($staffInfoId, $useInternal);

        //[2]图片质量检测
        if (empty($this->staff_face_image)) { //无底片时，校验口罩
            $this->attendance_server->setValidateFaceWithMskTips();
        }
        //$this->attendance_server->analyzeFace($clickUrl, $staffInfoId);

        //[3]外协作弊检测
        //外协员工在打上班卡时，进行作弊检测
        $this->staff_info = (new StaffServer())->getStaffById($params['staff_info_id']);
        if ($this->staff_info && $this->staff_info['formal'] == HrStaffInfoModel::FORMAL_0 && $this->attendance_server->checkStaffInfo($staffInfoId) &&
            $attendanceCategory == StaffWorkAttendanceModel::ATTENDANCE_ON) {
            $this->attendance_server->analyzeFace($clickUrl, $staffInfoId);
            $this->attendance_server->detectOutSourceStaffCheating($params);
        } else if (empty($this->staff_face_image) && $this->configure['ai_mask_switch'] == CommonEnums::SWITCH_OPENED) {
            $this->logger->write_log(sprintf('attendance attachment empty!, staff id %d', $staffInfoId), 'info');
            $this->attendance_server->analyzeFace($clickUrl, $staffInfoId);
        }

        if (empty($this->staff_face_image)) { //不存在底片

            if (empty($sourceUrl)) {
                http_response_code(422);
                throw new ValidationException($this->getTranslation()->_('miss_args') . ' source_url', ErrCode::AI_IMAGE_VERIFY_MISS_ARGS);
            }

            $isCheckFaceBlackList = (new StaffFaceBlacklistServer())->isCheckFaceBlackList($params['staff_info_id']);
            if ($isCheckFaceBlackList) {
                //处理黑名单底片逻辑
                $this->attendance_server->dealFaceBlackListLogic($staffInfoId, $sourceUrl);
            }


            //[4]保存底片
            $staffInfoArr = [$staffInfoId];
            if ($this->isSaveMasterFlag && !is_null($this->masterStaffInfoId)) { // 主/子账号同时为空的情况，保存底片到主账号
                $staffInfoArr[] = $this->masterStaffInfoId;
            }
            $this->logger->write_log(sprintf('save face image, staff id %s', json_encode($staffInfoArr)), 'info');
            $insert = [];
            foreach ($staffInfoArr as $item) {
                $insert['staff_info_id']          = $item;
                $insert['work_attendance_path']   = $sourceUrl;
                $insert['work_attendance_bucket'] = $this->config->application->oss_bucket;
                $this->attendance_server->save_face_img($insert);
            }

            //[5]作弊检测
            if ($this->isAgent() || $this->isFormal()) {
                $this->logger->write_log(sprintf('%s attachment is empty,and do cheating detect', $staffInfoId), 'info');
                //开关
                $switch = (new SettingEnvServer())->getSetValFromCache('backyard_check_face_in_all_store_switch');
                if (empty($switch) || $switch == CommonEnums::SWITCH_CLOSED) {
                    return;
                }
                $params['source']   = AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_SAVE_ATTACHMENT;
                $params['store_id'] = null; //录入底片，要全网检测作弊
                $this->attendance_server->detectAgentCheating($params);
            }
            return;
        }

        //[4]人脸比对
        $format['bucket'] = $this->config->application->oss_bucket;//固定
        $format['path']   = $clickUrl;
        $currentUrl       = $this->formatOssUrl($format, $useInternal);
        $compareScore     = $this->doCompareFaceImage($staffInfoId, $this->staff_face_image, $currentUrl);

        $this->logger->write_log("image_verify_{$staffInfoId} {$compareScore} odlURL:{$this->staff_face_image} currentUrl:{$currentUrl}" . json_encode($params),'info');

        //ai组 需要数据跑用例 记录下对比后的日志 staff_work_face_verify_record 保存
        $params['success_enabled'] = intval($compareScore > self::COMPARE_FACE_SCORE);
        $this->saveFaceLog($this->user_info, $this->getFaceCompareType(), $params);

        if($compareScore < self::COMPARE_FACE_SCORE) { //不匹配

            //[5]个人代理作弊检测
            if ($this->isAgent()) {
                $this->attendance_server->detectAgentCheating($params);
            }

            http_response_code(422);
            throw new ValidationException($this->getVerifyFailMessage(), ErrCode::AI_IMAGE_VERIFY_ERROR);
        }
    }

    /**
     * 获取员工底片
     * @param $staff_info_id
     * @param bool $is_url_internal 是否内部 true/false
     * @return string
     * @throws ValidationException
     */
    public function getStaffFaceImage($staff_info_id, bool $is_url_internal = false): string
    {
        //如果当前工号是子账户，则获取主账户底片
        $staffServer                   = new StaffServer();
        $staffWorkAttendanceRepository = new StaffWorkAttendanceRepository();
        $staffInfo                     = $staffServer->getStaffById($staff_info_id);

        //获取底片
        $currentStaffFaceImagePath = $staffWorkAttendanceRepository->getStaffFaceImage($staff_info_id);

        //如当前账号是子账号
        if (!empty($staffInfo) && $staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            //获取主账号信息
            $supportInfo = HrStaffApplySupportStoreModel::findFirst("sub_staff_info_id = $staff_info_id");

            //获取底片
            $staffAttendanceImagePath = $staffWorkAttendanceRepository->getStaffFaceImage($supportInfo['staff_info_id']);
            if (empty($staffAttendanceImagePath) && !empty($currentStaffFaceImagePath)) {
                //如果是子账号用主账号底片比对，当子账号读主账号没有人脸底片时，向主账户同步底片
                $attendanceAttachment = [
                    'staff_info_id'     => $supportInfo['staff_info_id'],
                    'attachment_bucket' => $currentStaffFaceImagePath['bucket'],
                    'attachment_path'   => $currentStaffFaceImagePath['path'],
                ];
            }
            if (!empty($staffAttendanceImagePath) && empty($currentStaffFaceImagePath)) {
                //如果是子账号用主账号底片比对，当子账号没有人脸底片时，向子账户同步底片
                $attendanceAttachment = [
                    'staff_info_id'  => $staff_info_id,
                    'attachment_bucket' => $staffAttendanceImagePath['bucket'],
                    'attachment_path'   => $staffAttendanceImagePath['path'],
                ];
            }
            if (empty($staffAttendanceImagePath) && empty($currentStaffFaceImagePath)) {
                $this->isSaveMasterFlag  = true;
                $this->masterStaffInfoId = $supportInfo['staff_info_id'];
            }

            //补底片
            if (!empty($attendanceAttachment)) {
                $redis   = $this->getDI()->get('redisLib');
                $content = json_encode($attendanceAttachment, JSON_UNESCAPED_UNICODE);
                $this->logger->write_log("[async master staff attachment lpush]" . json_encode($content,
                        JSON_UNESCAPED_UNICODE),
                    'info');
                $redis->lpush(AttendanceServer::REDIS_ASYNC_TRANSFER_ATTENDANCE_ATTACHMENT, $content);
            }

            if (!empty($staffAttendanceImagePath)) {
                return $this->formatOssUrl($staffAttendanceImagePath, $is_url_internal);
            }
        }
        return !empty($currentStaffFaceImagePath)
            ? $this->formatOssUrl($currentStaffFaceImagePath, $is_url_internal)
            : '';
    }

    /**
     * 是否开启活体检测
     * @param $staff_info_id
     * @return bool
     */
    private function checkLiveDetect($staff_info_id): bool
    {
        if (!empty($this->configure['live_list'])) {

            //全员或者当前工号在指定工号中
            if ($this->configure['live_list'] == self::ALIVE_CONFIG_ALL || in_array($staff_info_id, explode(',', $this->configure['live_list']))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 活体检测
     * @param $params
     * @throws ValidationException
     */
    private function doLiveDetect($params)
    {
        $staffInfoId = $params['staff_info_id'];
        $clickUrl    = $params['click_url'];
        $useInternal = $params['use_internal'];
        //关闭活体检测
        if (env('close_live_face', 0)) {
            return true;
        }
        //活体检测
        $format['bucket'] = $this->config->application->oss_bucket;//固定
        $format['path']   = $clickUrl;
        $liveCheckParam = sprintf('url=%s', $this->formatOssUrl($format, $useInternal));

        $liveDetectInfo = AiServer::getInstance()->setConfig(enums::IDENTIFY_LIVE_CHECK)
            ->sendRequestWithHttpCode($liveCheckParam, $staffInfoId);

        $liveDetectScore = 0;
        if (empty($liveDetectInfo) || $liveDetectInfo['http_code'] == 0 || $liveDetectInfo['http_code'] >= 500) {
            $liveDetectScore = -0.03;
        }
        if(!empty($liveDetectInfo['response']['result']) && !empty($liveDetectInfo['response']['result']['score'])) {//调用成功
            $liveDetectScore = $liveDetectInfo['response']['result']['score'];
        }
        if (!empty($liveDetectInfo['error'])) {
            $liveDetectScore = $this->attendance_server->ai_code_format($liveDetectInfo['error'], enums::AI_INTERFACE_2);
        }

        //活体检测基础分数
        $liveDetectConfigScore = $this->configure['alive_score'];
        $this->logger->write_log(sprintf('[doLiveDetect] live check staff id: {%s}, score: {%s}, params in %s',
            $staffInfoId, $liveDetectScore, $clickUrl), 'info');

        $this->params['success_enabled'] = min(intval($liveDetectScore * 100),10);
        $this->saveFaceLog($this->user_info, AiStateEnums::COMPARE_FACE_TYPE_LIVE_CHECK, $this->params);

        //如活体检测分数小于设定活体检测分数，为非活体
        if ($liveDetectScore >= 0 && $liveDetectConfigScore > $liveDetectScore) { //非活体
            http_response_code(422);
            throw new ValidationException($this->getVerifyFailMessage(), ErrCode::AI_IMAGE_VERIFY_ERROR);
        }
    }

    //保存人脸对比地址、对比结果、来源等
    //验证渠道：阿里=0,腾讯=1,百度=2,内部AI=3,保存原图底片=4,静默活体=5
    private function saveFaceLog($user_info, $attendance_type, $param)
    {
        $insertParams['staff_info_id']     = $user_info['staff_id'];
        $insertParams['organization_id']   = $user_info['organization_id'];
        $insertParams['organization_type'] = $user_info['organization_type'];
        $insertParams['attendance_date']   = $param['attendance_date'];
        $insertParams['verify_channel']    = $attendance_type;
        $insertParams['success_enabled']   = $param['success_enabled'];
        $insertParams['image_path']        = $param['click_url'];//验证渠道
        $insertParams['image_bucket']      = $this->config->application->oss_bucket;
        $insertParams['device_type']       = $param['device'];
        $this->logger->write_log("add_face_log f_param:" . json_encode($insertParams), 'info');
        try {
            return $this->getDI()->get('db')->insertAsDict('staff_work_face_verify_record', $insertParams);
        } catch (Exception $e) {
            $this->logger->write_log(['add_face_log' => $e->getMessage(), 'param' => $insertParams], 'error');
        }
        return false ;
    }

    /**
     * 拼接图片地址
     * @param $image_path
     * @param bool $is_url_internal
     * @return string
     */
    public function formatOssUrl($image_path, bool $is_url_internal = false): string
    {

        //拼接图片路径
        if($is_url_internal) {
            $hostName = $this->configure['server_point_internal'];
        } else {
            $hostName = $this->configure['server_point'];
        }
        if (empty($hostName)) {
            return '';
        }
        return sprintf('https://%s%s%s', $image_path['bucket'], $hostName, $image_path['path']);
    }

    /**
     * 获取人脸比对类型
     * @return int
     * @throws ValidationException
     */
    private function getFaceCompareType(): int
    {
        //根据配置获取接口类型
        $interfaceType = $this->configure['attendance_type'];
        if (empty($interfaceType)) { //没有配置默认是按照百分比分配
            $interfaceType = $this->getAttendanceTypeByPercentage();
        }
        if (empty($interfaceType) || $interfaceType == AiStateEnums::COMPARE_FACE_TYPE_TENCENT) {
            $interfaceType = AiStateEnums::COMPARE_FACE_TYPE_TENCENT;
        } else if ($interfaceType == AiStateEnums::COMPARE_FACE_TYPE_INTERNAL_AI) {
            $interfaceType = AiStateEnums::COMPARE_FACE_TYPE_INTERNAL_AI;
        }
        if (empty($interfaceType)) {
            throw new ValidationException('invalid config attendance_type');
        }

        //如内部AI错误数超过了配置的最大限度，或已触发强制切换腾讯AI接口
        //则，使用腾讯接口
        $internalAiMaxErrNo   = $this->configure['attendance_face_top'];
        $redis                = $this->getDI()->get('redisLib');
        $internalAiErrNo      = $redis->get(self::REDIS_KEY_IMAGE_VERIFY_ERR_NO);
        $forceSwitchToTencent = $redis->get(self::REDIS_KEY_FORCE_SWITCH_TO_TENCENT);
        if ($internalAiErrNo > $internalAiMaxErrNo || !empty($forceSwitchToTencent)) {
            $interfaceType = AiStateEnums::COMPARE_FACE_TYPE_TENCENT;
        }
        return $interfaceType;
    }

    /**
     * 根据百分比，随机获取人脸接口类型
     * 例如：{"1":9,"3":1}，意思是腾讯 90% ai 10%
     *
     * @return mixed
     */
    private function getAttendanceTypeByPercentage()
    {
        //10 满分 {"1":9,"3":1}
        $facePercent = $this->configure['face_percent'];
        if (empty($facePercent)) {
            $facePercent = '{"1":9,"3":1}';
        }
        $facePercent = json_decode($facePercent);
        $weight      = 0;
        $rand_data   = [];
        foreach ($facePercent as $type => $p) {
            $weight += $p;
            for ($i = 0; $i < $p; $i++) {
                $rand_data[] = $type;
            }
        }
        $use = rand(0, $weight - 1);
        return $rand_data[$use];
    }

    /**
     * 人脸比对接口
     * @return float
     * @throws ValidationException
     */
    private function doCompareFaceImage($staff_info_id, $old_image_url, $new_image_url)
    {
        $faceCompareType = $this->getFaceCompareType();
        if ($faceCompareType == AiStateEnums::COMPARE_FACE_TYPE_TENCENT) {
            $compareResult = $this->tencentInterFace($old_image_url, $new_image_url);
        } else {
            $compareResult = $this->internalAiInterFace($staff_info_id, $old_image_url, $new_image_url);
            if($compareResult['http_code'] >= 500) {
                //记录 ai 失败日志
                $this->params['success_enabled'] = -3;
                $this->saveFaceLog($this->user_info, $this->getFaceCompareType(), $this->params);

                //失败数量递增 并且写延时队列
                $messageQueue = (new QueueMQServer($this->lang, $this->timeZone));
                $data = [
                    "staff_info_id" => $staff_info_id,
                    "src"           => "backyard",
                ];
                $messageQueue->sendToMsg($data,5 * 60,'attendance-fail');

                //失败次数+1
                $redis = $this->getDI()->get('redisLib');
                $redis->incr(self::REDIS_KEY_IMAGE_VERIFY_ERR_NO);

                //如果 已经达到峰值 配置2小时实效切换策略
                $errNo = $redis->get(self::REDIS_KEY_IMAGE_VERIFY_ERR_NO);
                $internalAiMaxErrNo   = $this->configure['attendance_face_top'];
                if($errNo > $internalAiMaxErrNo) {
                    $hasChangedToTencent = $redis->get(self::REDIS_KEY_FORCE_SWITCH_TO_TENCENT);
                    if(empty($hasChangedToTencent)) {
                        $redis->set(self::REDIS_KEY_FORCE_SWITCH_TO_TENCENT, 1, 2 * 60 * 60); //2小时 切换腾讯开关
                    }
                    $this->logger->write_log("face_switch_changed！！！！！！！ ");//已经启动 切换腾讯 2 让ai 检查问题 2小时之后切回来
                }
                $this->logger->write_log("image_verify_failed_{$staff_info_id} {$compareResult['http_code']} {$compareResult['score']}_{internal_ai} odlURL:{$old_image_url} 
                    currentUrl:{$new_image_url}", 'info');

                //如果失败了，切换到腾讯
                $compareResult = $this->tencentInterFace($old_image_url, $new_image_url);
            }
        }
        return $compareResult['score'];
    }

    /**
     * 人脸比对失败后的提示
     * @return mixed
     */
    private function getVerifyFailMessage()
    {
        $server = (new FaceCompareServer($this->lang, $this->timeZone));
        return $server->getVerifyFailMessage($this->user_info['id']);
    }

    /**
     * 内部接口人脸比对
     * @param $staff_info_id
     * @param $old_image_url
     * @param $new_image_url
     * @return array
     * @throws ValidationException
     */
    public function internalAiInterFace($staff_info_id, $old_image_url, $new_image_url): array
    {
        //人脸比对
        $param         = sprintf('urlA=%s&urlB=%s', $old_image_url, $new_image_url);
        $compareResult = AiServer::getInstance()->setConfig(enums::IDENTIFY_LIVE_COMPARE_FACE)
            ->sendRequestWithHttpCode($param, $staff_info_id);
        if (!empty($compareResult) && $compareResult['http_code'] == 200 && $compareResult['response']['result']) {
            return [
                'score'     => 100,
                'http_code' => $compareResult['http_code'],
                'distance'  => $compareResult['response']['distance'] ?? 100,
            ];
        }
        return ['score' => 0, 'http_code' => $compareResult['http_code'], 'distance' => $compareResult['response']['distance'] ?? 100];
    }

    /**
     * 腾讯人脸比对接口
     * @param $old_image_url
     * @param $new_image_url
     * @return array|int[]
     */
    private function tencentInterFace($old_image_url, $new_image_url)
    {
        $tencent_url  = env('tencent_url', 'https://iai.ap-singapore.tencentcloudapi.com');
        $tencent_id   = env('tencent_id', '');
        $tencent_key  = env('tencent_key', '');
        $tencent_host = env('tencent_host', 'iai.ap-singapore.tencentcloudapi.com');
        $crypt        = 'TC3-HMAC-SHA256';
        try {
            //照片比对
            date_default_timezone_set('UTC');//零时区 与腾讯服务器 不能相差5分钟以上
            $secretId  = $tencent_id;
            $secretKey = $tencent_key;
            $host      = $tencent_host;
            $tmp       = time();
            $date      = date('Y-m-d', $tmp);
            $server    = 'iai';
            $action    = 'CompareFace';
            $version   = '2020-03-03';
            $region    = 'ap-singapore';
            //底片
            $param['UrlA'] = $old_image_url;
            //app 人脸图片
            $param['UrlB'] = $new_image_url;
            $param         = str_replace("\\/", "/", json_encode($param));

            //生成 签名 认证信息
            $signature = $this->sign($secretKey, $host, $server, $tmp, $param);
            $auth      = $crypt . " Credential={$secretId}/{$date}/{$server}/tc3_request, SignedHeaders=content-type;host, Signature={$signature}";
            $header[]  = "Authorization: " . $auth;
            $header[]  = "Host: {$host}";
            $header[]  = "Content-Type: application/json";
            $header[]  = "X-TC-Action: {$action}";
            $header[]  = "X-TC-Timestamp: " . $tmp;
            $header[]  = "X-TC-Version: " . $version;
            $header[]  = "X-TC-Region: " . $region;

            //返回结果结构
            //{"Response":{"Score":8.954370498657227,"FaceModelVersion":"2.0","RequestId":"963b7486-86eb-43bb-9af9-cffebb7194d5"}}
            $res = $this->httpPostWithHttpCode($tencent_url, $param, $header, null, 5);
            $this->logger->write_log("tencent_interface " . json_encode($param) . ' ' . $res['response'], 'info');
            $http_code = $res['code_status'];
            $res       = json_decode($res['response'], true);
            if (!empty($res) && !empty($res['Response']['Score'])) {
                return ['score' => $res['Response']['Score'], 'http_code' => $http_code];
            } else {
                $this->getDI()->get('logger')->write_log("图片对比失败 " . json_encode($res), 'info');
                return ['score' => 0, 'http_code' => $http_code];
            }
        } catch (Exception $e) {
            $this->logger->write_log("图片对比失败 " . $e->getMessage(), 'info');
            return ['score' => 0, 'http_code' => 508];
        }
    }

    //签名 加密 参考文档 https://cloud.tencent.com/document/product/867/32802
    protected function sign($secretKey,$host,$service,$timestamp,$param)
    {
        $crypt = 'TC3-HMAC-SHA256';
        $algorithm = $crypt;//固定加密算法
        // step 1: build canonical request string
        $httpRequestMethod = "POST";
        $canonicalUri = "/";
        $canonicalQueryString = "";
        $canonicalHeaders = "content-type:application/json\n"."host:".$host."\n";
        $signedHeaders = "content-type;host";
        $hashedRequestPayload = hash("SHA256", $param);
        $canonicalRequest = $httpRequestMethod."\n"
            .$canonicalUri."\n"
            .$canonicalQueryString."\n"
            .$canonicalHeaders."\n"
            .$signedHeaders."\n"
            .$hashedRequestPayload;

        // step 2: build string to sign
        $date = gmdate("Y-m-d", $timestamp);
        $credentialScope = $date."/".$service."/tc3_request";
        $hashedCanonicalRequest = hash("SHA256", $canonicalRequest);
        $stringToSign = $algorithm."\n"
            .$timestamp."\n"
            .$credentialScope."\n"
            .$hashedCanonicalRequest;

        // step 3: sign string
        $secretDate = hash_hmac("SHA256", $date, "TC3".$secretKey, true);
        $secretService = hash_hmac("SHA256", $service, $secretDate, true);
        $secretSigning = hash_hmac("SHA256", "tc3_request", $secretService, true);
        $signature = hash_hmac("SHA256", $stringToSign, $secretSigning);
        return $signature;
    }

    /**
     * 是否是跟人代理
     * @return bool
     */
    public function isAgent(): bool
    {
        if ($this->staff_info && $this->staff_info['formal'] == HrStaffInfoModel::FORMAL_1 &&
            in_array($this->staff_info['hire_type'], HrStaffInfoModel::$agentTypeTogether) &&
            in_array($this->staff_info['job_title'], $this->attendance_server->getAgentCourierJobTitle())) {
            return true;
        }
        return false;
    }

    public function isFormal(): bool
    {
        //正式员工、月薪制特殊合同工、个人代理
        if ($this->staff_info && $this->staff_info['formal'] == HrStaffInfoModel::FORMAL_1 &&
            in_array($this->staff_info['hire_type'], [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2]) &&
            in_array($this->staff_info['job_title'], $this->attendance_server->getAgentCourierJobTitle())) {
            return true;
        }
        return false;
    }

    /**
     * 个人代理
     * 保存底片验证
     * @param $staffInfoId
     * @param $source_url
     * @return void
     */
    public function saveFaceNegativesValidation($staffInfoId,$source_url)
    {
        $this->staff_info = (new StaffServer())->getStaffById($staffInfoId);
        //[5]作弊检测
        if ($this->isAgent() || $this->isFormal()) {
            $this->logger->write_log(sprintf('%s attachment is empty,and do cheating detect', $staffInfoId), 'info');
            $switch = (new SettingEnvServer())->getSetVal('backyard_check_face_in_all_store_switch');
            if (empty($switch) || $switch == CommonEnums::SWITCH_CLOSED) {
                return;
            }
            $params['source']    = AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_SAVE_ATTACHMENT;
            $params['store_id']  = null; //录入底片，要全网检测作弊
            $params['click_url'] = $source_url;
            $params['staff_info_id'] = $staffInfoId;
            $this->attendance_server->detectAgentCheating($params);
        }
    }

}