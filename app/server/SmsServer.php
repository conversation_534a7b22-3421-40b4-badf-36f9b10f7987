<?php

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\StaffRepository;

class SmsServer extends BaseServer
{

    const SMS_BIZ_TYPE_MOBILE_VERIFY = 2;//手机号码认证
    const SMS_BIZ_TYPE_F_NUM_TEMPLATE = 3;//F-num 短信
    const SMS_BIZ_TYPE_PASSWORD = 4;//修改密码
    const SMS_BIZ_TYPE_CHECK_MOBILE = 5;//th 手机号码周期验证
    const SMS_BIZ_TYPE_EDIT_PERSON_MOBILE = 6;//更换个人号码

    public $timezone;

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 获取验证码内容
     * @param $biz_type
     * @param $code
     * @return mixed
     */
    protected function getSmsContent($biz_type, $bind)
    {
        return $this->getTranslation()->_('sms_tpl_' . $biz_type, $bind);
    }
    protected function getSmsContentV2($biz_type, $bind)
    {
        return $this->getTranslation()->_('v2_sms_tpl_' . $biz_type, $bind);
    }

    /**
     * 获取验证码缓存key
     * @param $biz_type
     * @param $mobile
     * @return string
     */
    protected function getSmsCacheKey($biz_type, $mobile): string
    {
        return 'tool_sms_' . $biz_type . '_' . $mobile;
    }

    /**
     * 获取验证码超时分钟
     * @param $biz_type
     * @return int
     */
    protected function getExpirationDuration($biz_type): int
    {
        switch ($biz_type) {
            case self::SMS_BIZ_TYPE_MOBILE_VERIFY:
                $duration = 3;
                break;
            case self::SMS_BIZ_TYPE_EDIT_PERSON_MOBILE:
                $duration = 10;
                break;
            default:
                $duration = 5;
                break;
        }
        return $duration;
    }

    /**
     * 验证短信发送业务限制
     * @throws BusinessException
     */
    protected function bizTypeBusinessValidation($staff_info_id,$biz_type, $mobile): bool
    {
        switch ($biz_type) {
            case self::SMS_BIZ_TYPE_MOBILE_VERIFY:
                $isExist = HrStaffInfoModel::count(
                    [
                        'conditions' => 'mobile = :mobile: and state in (1,3) and is_sub_staff = 0 and staff_info_id != :staff_info_id:',
                        'bind'       => ['mobile' => $mobile, 'staff_info_id' => $staff_info_id],
                    ]
                );
                if ($isExist) {
                    throw new BusinessException($this->getTranslation()->_('mobile_existed'));
                }
                break;
            case self::SMS_BIZ_TYPE_EDIT_PERSON_MOBILE:
                (new PersoninfoServer($this->lang,$this->timezone))->checkUpdatePersonMobile($staff_info_id,$mobile);
                break;
            default:
                break;
        }
        return true;
    }

    /**
     * 验证短信发送频率
     * @throws ValidationException
     */
    protected function bizTypeFrequencyValidation($biz_type, $mobile): bool
    {
        $cache = $this->getDI()->get('redisLib');
        switch ($biz_type) {
            case self::SMS_BIZ_TYPE_MOBILE_VERIFY:

                //验证码发送频繁，1分钟后再试
                $num_key_1min  = 'tool_sms_1min_' . $biz_type . '_' . $mobile;
                $num_data_1min = $cache->get($num_key_1min);

                //5min 最多发送 3 次验证码，否则提示：验证码发送频繁，5min后再试
                $num_key  = 'tool_sms_num_' . $biz_type . '_' . $mobile;
                $num_data = $cache->get($num_key);

                if (!$num_data) {
                    $cache->set($num_key_1min, 1, 59);
                    $cache->set($num_key, json_encode(['num' => 1, 'time' => time()]), 5 * 60);
                    return true;
                }

                $num_data        = json_decode($num_data, true);
                $time_diff       = (time() - $num_data['time']);
                $num_data['num'] += 1;
                //5分钟超过三次 提示频繁
                if ($num_data['num'] > 3 && $time_diff <= 5 * 60) {
                    throw new ValidationException($this->getTranslation()->_('sms_verify_code_error_1',
                        ['num' => 5]));//验证码发送频繁，5min后再试
                }
                if ($num_data_1min) {
                    throw new ValidationException($this->getTranslation()->_('sms_verify_code_error_5'));//验证码发送频繁，1分钟后再试
                }
                $cache->set($num_key_1min, 1, 59);
                $cache->set($num_key, json_encode($num_data), 5 * 60 - $time_diff);
                break;
            case self::SMS_BIZ_TYPE_PASSWORD:
            case self::SMS_BIZ_TYPE_EDIT_PERSON_MOBILE:
                //验证码发送频繁，1分钟后再试
                $num_key_1min  = 'tool_sms_1min_' . $biz_type . '_' . $mobile;
                $num_data_1min = $cache->get($num_key_1min);
                if ($num_data_1min) {
                    throw new ValidationException($this->getTranslation()->_('sms_verify_code_error_1',
                        ['num' => 1]));//验证码发送频繁，1min后再试
                }
                $cache->set($num_key_1min, 1, 59);
                break;
            case self::SMS_BIZ_TYPE_CHECK_MOBILE:
                //验证码发送频繁，2分钟后再试
                $num_key_2min  = 'tool_sms_2min_' . $biz_type . '_' . $mobile;
                $num_data_2min = $cache->get($num_key_2min);
                if ($num_data_2min) {
                    throw new ValidationException($this->getTranslation()->_('sms_verify_code_error_1',
                        ['num' => 2]));//验证码发送频繁，1min后再试
                }
                $cache->set($num_key_2min, 1, 119);
                break;
            default:
                break;
        }
        return true;
    }

    protected function setSmsData($biz_type, $mobile, $code)
    {
        $cache      = $this->getDI()->get('redisLib');
        $cacheKey   = $this->getSmsCacheKey($biz_type, $mobile);
        $smsTimeout = $this->getExpirationDuration($biz_type);
        $cache->set($cacheKey, $code, $smsTimeout * 60);
    }

    /**
     * 发送内容
     * @param $biz_type
     * @param string $content
     * @return array
     */
    protected function makeSendSmsContent($staff_id,$biz_type, string $content = ''): array
    {
        $code    = verificationCode();
        switch ($biz_type) {
            case self::SMS_BIZ_TYPE_MOBILE_VERIFY:
                $content = $this->getSmsContent($biz_type, ['code' => $code, 'num' => $this->getExpirationDuration($biz_type)]);
                break;
            case self::SMS_BIZ_TYPE_PASSWORD:

                $content = $this->getSmsContentV2($biz_type, ['staff_id' => $staff_id, 'code' => $code, 'num' => $this->getExpirationDuration($biz_type)]);
                break;
            case self::SMS_BIZ_TYPE_CHECK_MOBILE:
            case self::SMS_BIZ_TYPE_EDIT_PERSON_MOBILE:
                $content = $this->getSmsContent($biz_type, ['code' => $code]);
                break;

            case self::SMS_BIZ_TYPE_F_NUM_TEMPLATE:
            default:
                break;
        }
        return [$code, $content];
    }

    /**
     * 返回提示
     * @param $biz_type
     * @param $mobile
     * @param $result
     * @return array
     */
    protected function makeResult($biz_type, $mobile, $result): array
    {
        switch ($biz_type) {
            case self::SMS_BIZ_TYPE_EDIT_PERSON_MOBILE:
                $msg = $this->getTranslation()->_(isCountry('PH') ? 'viber_verify_code_new_phone' :  'verify_code_new_phone', ['phone_number' => $mobile]);
                break;
            default:
                $msg = 'success';
                break;
        }
        return ['code' => ErrCode::SUCCESS, 'msg' => $msg, 'data' => $result];
    }

    /**
     * 发送短信
     * @param $params
     * @return mixed
     * @throws ValidationException|BusinessException
     */
    public function sendSms($params)
    {
        $biz_type   = $this->processingDefault($params, 'biz_type', 2);
        $mobile     = $this->processingDefault($params, 'mobile');
        $staff_id   = $this->processingDefault($params, 'staff_id');
        $smsContent = $this->processingDefault($params, 'sms_content');
        //一段时间内次数的限制
        $this->bizTypeFrequencyValidation($biz_type, $mobile);
        //发送业务的限制
        $this->bizTypeBusinessValidation($staff_id,$biz_type, $mobile);
        //获取发送内容
        [$code,$content] = $this->makeSendSmsContent($staff_id,$biz_type, $smsContent);
        //保存发送信息
        $this->setSmsData($biz_type, $mobile, $code);

        if ((new StaffServer())->isLntStaff($staff_id)) {
            $src = 'backyard_lnt';
        } else {
            $src = 'backyard_'.$biz_type;
        }

        $sms_rpc = new ApiClient('sms_rpc', '', 'send', $this->lang, $src);
        $smsParams = ['mobile' => $mobile, 'msg' => $content, 'code' => 'th', 'delay' => 0,'type'=>1];
        //viber发送
        if (isCountry('PH')) {
            $smsParams['nation'] = 'PH';
            $smsParams['type'] =  0;// 文本消息， 固定类型
            $smsParams['service_provider'] =  9;// 服务商 Viber 固定值
        }
        $sms_rpc->setParams($smsParams);
        $data = $sms_rpc->execute();

        if (!empty($data['result'])) {
            if (RUNTIME != 'pro' && $biz_type != self::SMS_BIZ_TYPE_F_NUM_TEMPLATE) {
                $data['result'] .= '[' . $content . ']';
            }
            return  $this->makeResult($biz_type,$mobile,$data['result']);
        }
        if ($biz_type == self::SMS_BIZ_TYPE_F_NUM_TEMPLATE) {
            throw new BusinessException($this->getTranslation()->_(isCountry('PH')? 'viber_send_error':'sms_send_error'));//短信发送失败，请重新发送
        } else {
            throw new BusinessException($this->getTranslation()->_(isCountry('PH')? 'viber_verify_code_error_2':'sms_verify_code_error_2'));//验证码发送失败，请重新发送
        }
    }

    /**
     * 验证验证码是否正确
     * @throws ValidationException
     */
    public function checkSmsCode($biz_type, $mobile, $input_code): bool
    {
        $cache    = $this->getDI()->get('redisLib');
        $cacheKey = $this->getSmsCacheKey($biz_type, $mobile);
        $sendCode = $cache->get($cacheKey);
        if (!$sendCode) {
            throw new ValidationException($this->getTranslation()->_('sms_verify_code_error_3'));//验证码已过期，请重新发送
        }
        if ($sendCode != $input_code) {
            throw new ValidationException($this->getTranslation()->_('sms_verify_code_error_4'));//验证码错误，请重新输入
        }
        $cache->del($cacheKey);
        return true;
    }
    
    /**
     * F-Num电话拨号 获取短信模版
     * @param $params
     * @return string[]
     * @throws ValidationException
     */
    public function getEntryFNumSmsTemplate($params): array
    {
        $return_data = ['sms_template' => ''];
        $userinfo = $params['userinfo'] ?? [];
        // type : 1=传简历id ,type：2=传转个人代理id
        if (empty($params['id']) || !in_array($params['type'],[1,2]) || !isCountry(['TH','MY','PH'])){
            return $return_data;
        }
        $t = $this->getTranslation();
        if ($params['type'] == 1){
            $resumeData = HrResumeModel::findFirst([
                'conditions' => 'id = :resume_id:',
                'bind' => ['resume_id' => $params['id']],
            ]);
            if (empty($resumeData)){
                return $return_data;
            }
            $resumeData = $resumeData->toArray();

            $entryData = HrEntryModel::findFirst([
                'conditions' => 'resume_id = :resume_id:',
                'bind' => ['resume_id' => $params['id']],
                "order" => " entry_id desc",
            ]);
            if (empty($entryData) || empty($entryData->hc_id)){
                return $return_data;
            }
            $entryData = $entryData->toArray();

            $hcData = HrHcModel::findFirst([
                'conditions' => 'hc_id = :hc_id:',
                'bind' => ['hc_id' => $entryData['hc_id']],
            ]);
            if (empty($hcData) || empty($hcData->hire_type)){
                return $return_data;
            }
            $hcData = $hcData->toArray();

            
            $record_entry = $entryData['record_entry'] ? json_decode($entryData['record_entry'], true) : [];
            $hire_type = !empty($record_entry['hire_type']) ? $record_entry['hire_type'] : ($hcData['hire_type'] ?? '');
            $name = !empty($record_entry['name']) ? $record_entry['name'] : ($resumeData['name'] ?? '');
            $entry_date = !empty($entryData['entry_date']) ? date('Y-m-d',strtotime($entryData['entry_date'])) : '';
            $storeInfo = (new SysStoreServer())->getStoreInfoByid($hcData['worknode_id'] ?? '');
            $store_name = $storeInfo['name'] ?? enums::HEAD_OFFICE;

            if (isCountry('TH')){
                if (!in_array($hire_type, HrStaffInfoModel::$agentTypeTogether)) {
                    $return_data['sms_template'] = $t->_('entry_f_num_sms_template_th_no_agent', [
                        'name'       => $name,
                        'entry_date' => $entry_date
                    ]);
                }
            }
            if (isCountry(['MY','PH'])){
                if (in_array($hire_type, HrStaffInfoModel::$agentTypeTogether)){
                    $return_data['sms_template'] = $t->_('entry_f_num_sms_template_'.strtolower(env('country_code', 'Th')).'_agent', [
                        'name'       => $name,
                        'staff_name' => $userinfo['name'] ?? '',
                        'store_name' => $store_name,
                        'entry_date' => $entry_date,
                    ]);
                }else{
                    $return_data['sms_template'] = $t->_('entry_f_num_sms_template_'.strtolower(env('country_code', 'Th')).'_no_agent', [
                        'name'       => $name,
                        'staff_name' => $userinfo['name'] ?? '',
                        'store_name' => $store_name,
                        'entry_date' => $entry_date,
                    ]);
                }
            }
        }else{
            if (isCountry('TH')){
                return $return_data;
            }
            $result = (new HireTypeChangeServer($this->lang, $this->timezone))->getAgentEntryDetail($params['id']);
            if (empty($result)){
                return $return_data;
            }
            $name = $result['name'] ?? '';
            $entry_date = $result['work_time'] ? date('Y-m-d',strtotime($result['work_time'])) : '';
            $store_name = $result['store_name'] ?? 'Head Office';
            $return_data['sms_template'] = $t->_('entry_f_num_sms_template_'.strtolower(env('country_code', 'Th')).'_agent', [
                'name'       => $name,
                'staff_name' => $userinfo['name'] ?? '',
                'store_name' => $store_name,
                'entry_date' => $entry_date,
            ]);
        }
        return $return_data;
    }


}