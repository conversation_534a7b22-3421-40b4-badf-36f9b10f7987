<?php

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Enums\CeoMailEnums;
use FlashExpress\bi\App\Models\backyard\HubFullAttendanceModel;
use FlashExpress\bi\App\Repository\StaffRepository;

class FullAttendanceRewardServer extends BaseServer
{

    protected $start_month = '2025-05';


    /**
     * @param null $currentTime
     * @return array
     */
    public function hubFullRewardMonthList($currentTime = null): array
    {
        //每月1号-15号11点以前，默认月份为当前月份的上上月
        //每月15号11点及以后，默认月份为当前月份的上月
        //可选范围为默认月份及以前的连续4个月份，最早月份支持查询到2025-05

        $currentTime  = $currentTime ?: date('Y-m-d H:i:s');
        $currentMonth = date('Y-m', strtotime($currentTime));
        $limit_day = env('full_attendance_limit_day',15);
        if (RUNTIME == 'pro') {
            $limit_day = 15;
        } else {
          //  $this->start_month = '2025-01';
        }
        // 确定默认月份
        if ($currentTime <= date("Y-m-{$limit_day} 11:00:00")) {
            // 1-15号12点(北京时间)前，默认为上上月
            $defaultMonth = date('Y-m', strtotime("$currentMonth -2 month"));
        } else {
            // 15号12点后，默认为上月
            $defaultMonth = date('Y-m', strtotime("$currentMonth -1 month"));
        }
        // 计算可选范围（默认月份及以前的连续4个月）
        $availableMonths = [];
        for ($i = 0; $i < 4; $i++) {
            $month = date('Y-m', strtotime("$defaultMonth -$i month"));
            // 确保不早于最早支持的月份
            if ($month >= $this->start_month) {
                $availableMonths[] = $month;
            }
        }

        return $availableMonths;
    }

    /**
     * 是否展示全勤奖励菜单
     * @param $staff_info_id
     * @return bool
     */
    public function isShowHubFullAttendanceReward($staff_info_id): bool
    {
        $month_list = $this->hubFullRewardMonthList();
        if(empty($month_list)){
            return false;
        }
        $fullReward = HubFullAttendanceModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and stat_month in ({month_list:array})',
            'bind'       => [
                'staff_id'   => $staff_info_id,
                'month_list' => $month_list,
            ],
        ]);

        if (empty($fullReward)) {
            return false;
        }
        return true;
    }

    /**
     * 获取员工全勤奖励数据
     * @param $staff_info_id
     * @param $month
     * @return array
     */
    public function hubFullAttendanceData($staff_info_id, $month): array
    {
        $month_list = $this->hubFullRewardMonthList();
        $result['flash_box_url'] =  env('h5_endpoint') . CeoMailEnums::FLASH_BOX_CATEGORY_INCENTIVE;
        $result['is_has_data'] = false;
        if (!in_array($month, $month_list)) {
            return $result;
        }

        $model = HubFullAttendanceModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and stat_month = :month:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'month'         => $month,
            ],
        ]);
        if (empty($model)) {
            return $result;
        }
        //
        if (array_intersect(explode(',', $model->cancel_remark), ['当月入离职', '网点or职位不在全勤奖配置中'])) {
            return $result;
        }

        $result['is_has_data'] = true;
        $result['is_get_reward'] = $model->reward_money > 0;
        $result['reward_money']  = $model->reward_money;
        $result['length']        = $model->length;
        $result['detail']        = [];
        if ($result['is_get_reward']) {
            return $result;
        }

        $ab_date       = $this->trimDate($model->ab_date);
        $leave_date    = $this->trimDate($model->leave_date);
        $late_date     = $this->trimDate($model->late_date);
        $early_date    = $this->trimDate($model->early_date);

        $all_date = array_unique(array_merge($ab_date, $leave_date, $late_date, $early_date));

        $all_date = array_map(function ($date) {
            return trim($date);
        }, $all_date);

        sort($all_date);
        foreach ($all_date as $date) {

            $tmp_type = [];
            //ab_date
            if (in_array($date, $ab_date)) {
                $tmp_type[] = 'ab';
            }
            if (in_array($date, $leave_date)) {
                $tmp_type[] = 'leave';
            }
            if (in_array($date, $late_date) || in_array($date, $early_date)) {
                $tmp_type[] = 'late_early';
            }

            $result['detail'][] = [
                'date_at'    => $date,
                'error_type' => $tmp_type,
            ];
        }
        return $result;
    }


    protected function trimDate($date_string): array
    {
        $date_string = trim($date_string);
        if (empty($date_string)) {
            return [];
        }
        $date_list = explode(',', $date_string);
        return array_map(function ($date) {
            return trim($date);
        }, $date_list);
    }


}