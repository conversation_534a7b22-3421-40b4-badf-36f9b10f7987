<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 6/26/23
 * Time: 4:45 PM
 */


namespace FlashExpress\bi\App\Server;

use DateTime;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\WorkingCountryEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffSalaryTaxExtendModel;
use FlashExpress\bi\App\Models\backyard\StaffSalaryTaxModel;
use FlashExpress\bi\App\Models\backyard\StaffSalaryTaxLogModel;
use FlashExpress\bi\App\Repository\StaffRepository;

class TaxCollectionServer extends BaseServer
{
    const ID_CHECK_DEFAULT = 0;
    const ID_CHECK_UNDER_18 = 1;
    const ID_CHECK_OVER_18 = 2;
    public $limitDay = 24;//每月24号以后不让编辑

    public function __construct($lang = '', $timezone = '+08:00')
    {
        if (get_runtime() == 'tra') {
            $this->limitDay = 29;
        }
        parent::__construct($lang, $timezone);
    }


    //健康的孩子信息
    public static $childHealthyKeyList = [
        'child_under_18',     //18周岁以下，不包括残疾儿童的数量
        'child_over_18_study',//18岁及以上在读(含证书/预科班)，不包括残疾儿童的数量
        'child_over_18_end',  //18岁以上全日制在文凭以上 (马来西亚)或学士以上(马来西亚以外) ，不包括残疾儿童的数量
    ];
    //残疾的孩子的信息
    public static $childDisabledKeyList = [
        'child_under_18_disabled',    //残疾儿童的数量 小于18岁
        'child_disabled_and_studying',//就读文凭或更高级别 (马来西亚) /学位或同等学历 (马来西亚境外)的残疾孩子数量
    ];

    /**
     * 构建返回结构
     * @return array
     */
    public function getChildKeyArrList(): array
    {
        $result = [];
        foreach (array_merge(self::$childHealthyKeyList,self::$childDisabledKeyList) as $childKey) {
            $result[$childKey . '_arr'] =[];
        }
        return $result;
    }



    public function taxCollectMenu()
    {
        $r['id']    = 'tax_collection';//编码
        $r['icon']  = '';//图标
        $r['title'] = $this->getTranslation()->_('tax_info_collect');//菜单名称

        //读配置
        $url      = env('h5_endpoint');
        $r['dst'] = $url.'person-tax-info';//h5 url 地址
        return $r;
    }


    /**
     * 获取扩展信息字段
     * @param $info
     * @return array
     */
    protected function getExtendInfo($info): array
    {
        $pid                                             = $info['id'] ?? 0;
        $result['employee_disability_card_no']           = '';
        $result['employee_disability_card_photo']        = '';
        $result['employee_spouse_disability_card_no']    = '';
        $result['employee_spouse_disability_card_photo'] = '';
        $result['child_under_18_arr']                    = [];
        $result['child_over_18_study_arr']               = [];
        $result['child_over_18_end_arr']                 = [];
        $result['child_under_18_disabled_arr']           = [];
        $result['child_disabled_and_studying_arr']       = [];

        //扩展信息
        $extendInfo = StaffSalaryTaxExtendModel::find([
            'conditions' => 'pid = :id:',
            'bind'       => ['id' => $pid],
        ])->toArray();

        foreach ($extendInfo as $v) {
            switch ($v['category']) {
                case StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_EMPLOYEE:
                    $result['employee_disability_card_no']    = $v['disability_card_no'];
                    $result['employee_disability_card_photo'] = $v['disability_card_photo'];
                    break;
                case StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_EMPLOYEE_SPOUSE:
                    $result['employee_spouse_disability_card_no']    = $v['disability_card_no'];
                    $result['employee_spouse_disability_card_photo'] = $v['disability_card_photo'];
                    break;
                case StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_CHILD_UNDER_18:
                    $result['child_under_18_arr'][] = [
                        'identity_name' => $v['identity_name'],
                        'identity_no'   => $v['identity_no'],
                    ];
                    break;
                case StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_CHILD_OVER_18_STUDY:
                    $result['child_over_18_study_arr'][] = [
                        'identity_name' => $v['identity_name'],
                        'identity_no'   => $v['identity_no'],
                    ];
                    break;
                case  StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_CHILD_OVER_18_END:
                    $result['child_over_18_end_arr'][] = [
                        'identity_name' => $v['identity_name'],
                        'identity_no'   => $v['identity_no'],
                    ];
                    break;
                case StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_CHILD_UNDER_18_DISABLED:
                    $result['child_under_18_disabled_arr'][] = [
                        'identity_name'         => $v['identity_name'],
                        'identity_no'           => $v['identity_no'],
                        'disability_card_no'    => $v['disability_card_no'],
                        'disability_card_photo' => $v['disability_card_photo'],
                    ];
                    break;
                case StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_CHILD_DISABLED_AND_STUDYING:
                    $result['child_disabled_and_studying_arr'][] = [
                        'identity_name'         => $v['identity_name'],
                        'identity_no'           => $v['identity_no'],
                        'disability_card_no'    => $v['disability_card_no'],
                        'disability_card_photo' => $v['disability_card_photo'],
                    ];
                    break;
            }
        }

        foreach (array_merge(self::$childHealthyKeyList, self::$childDisabledKeyList) as $field) {
//            var_dump($field.' num '.$info[$field].' count '.count($result[$field . '_arr']));
            if (isset($info[$field]) && $info[$field] != count($result[$field . '_arr'])) {
                $result[$field . '_arr'] = $this->fixItemNumber($field, $info[$field], $result[$field . '_arr']);
            }
        }

        return $result;
    }

    private function fixItemNumber($field,$total,$list)
    {
        $default_item = [
            'identity_name'         => '',
            'identity_no'           => '',
            'disability_card_no'    => '',
            'disability_card_photo' => '',
        ];
        if (in_array($field, self::$childHealthyKeyList)) {
            $default_item = ['identity_name' => '', 'identity_no' => ''];
        }

        //总数 超过 list长度
        $list_num =  count($list);
        if($total <= $list_num) {
            return array_slice($list, 0, $total);
        }
        $add_num = $total - $list_num;
        for($i = 0; $i < $add_num; $i++) {
            $list[] = $default_item;
        }
        return $list;
    }



    //个税信息回显页面
    public function collectInfo($param)
    {
        //如果没数据 用默认值
        $info = StaffSalaryTaxModel::findFirst([
            'columns'    => 'id,is_disabled,marital,spouse_work,spouse_is_disabled,child_under_18,
                            child_over_18_study,child_over_18_end,child_under_18_disabled,child_disabled_and_studying',
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $param['staff_info_id']],
        ]);

        //权限
        //用户信息
        $staffRe   = new StaffRepository($this->lang);
        $staffInfo = $staffRe->getStaffPosition($param['staff_info_id']);
        $data      = $this->checkStaff($staffInfo);


        //保存过数据
        if (!empty($info)) {
            $info = $info->toArray();
            $extendInfo = $this->getExtendInfo($info);

            $info = array_map('strval',$info);
            return array_merge($info, $data,$extendInfo);
        }

        //没数据 取默认
        $data['is_disabled']        = (string)StaffSalaryTaxModel::NO;//默认否
        $data['marital']            = (string)StaffSalaryTaxModel::SINGLE;//默认单身
        $data['spouse_work']        = (string)StaffSalaryTaxModel::YES;//默认否 光辉要改成是
        $data['spouse_is_disabled'] = (string)StaffSalaryTaxModel::NO;//默认否
        //数量 默认 0
        $data['child_under_18']              = '0';
        $data['child_over_18_study']         = '0';
        $data['child_over_18_end']           = '0';
        $data['child_under_18_disabled']     = '0';
        $data['child_disabled_and_studying'] = '0';
        return array_merge($data,$this->getExtendInfo([]));
    }

    /**
     * 新增更新接口
     * @param $param
     * @return null
     * @throws ValidationException
     * @throws \DateMalformedStringException
     */
    public function collectSave($param)
    {
        $staffRe   = new StaffRepository($this->lang);
        $staffInfo = $staffRe->getStaffPosition($param['staff_info_id']);
        $check = $this->checkStaff($staffInfo);
        if (!$check['is_work_at']) {
            //非工作所在马来 不能修改
            throw new ValidationException($this->getTranslation()->_('country_check_notice'));
        }
        if (!$check['is_edit']) {
            //24号-月底期间 不能修改
            throw new ValidationException($this->getTranslation()->_('day_check_notice'));
        }

        $insert['staff_info_id']               = $insert['operator_id'] = $param['staff_info_id'];
        $insert['is_disabled']                 = $param['is_disabled'];
        $insert['marital']                     = (string)$param['marital'];
        $insert['spouse_work']                 = intval($param['spouse_work']);
        $insert['spouse_is_disabled']          = intval($param['spouse_is_disabled']);
        $insert['child_under_18']              = $param['child_under_18'];
        $insert['child_over_18_study']         = $param['child_over_18_study'];
        $insert['child_over_18_end']           = $param['child_over_18_end'];
        $insert['child_under_18_disabled']     = $param['child_under_18_disabled'];
        $insert['child_disabled_and_studying'] = $param['child_disabled_and_studying'];


        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $info = StaffSalaryTaxModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id:',
                'bind'       => ['staff_id' => $param['staff_info_id']],
            ]);

            if (empty($info)) {
                $model   = new StaffSalaryTaxModel();
                $flag    = $model->create($insert);
                $oldData = [];
                $param['pid'] = $model->id;
            } else {
                //原数据
                $oldData['is_disabled']                 = $info->is_disabled;
                $oldData['marital']                     = (string)$info->marital;
                $oldData['spouse_work']                 = $info->spouse_work;
                $oldData['spouse_is_disabled']          = $info->spouse_is_disabled;
                $oldData['child_under_18']              = $info->child_under_18;
                $oldData['child_over_18_study']         = $info->child_over_18_study;
                $oldData['child_over_18_end']           = $info->child_over_18_end;
                $oldData['child_under_18_disabled']     = $info->child_under_18_disabled;
                $oldData['child_disabled_and_studying'] = $info->child_disabled_and_studying;
                //更新
                $flag = $info->update($insert);
                $extendInfo = StaffSalaryTaxExtendModel::find([
                    'conditions' => 'pid = :id:',
                    'bind'       => ['id' => $info->id],
                ]);
                $oldData['extend_info'] = $extendInfo->toArray();
                $extendInfo->delete();
                $param['pid'] = $info->id;
            }

            $extendInsert = $this->checkStaffExtend($param, $staffInfo);
            $extendModel = new StaffSalaryTaxExtendModel();
            if (!empty($extendInsert)) {
                $extendModel->batch_insert($extendInsert, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
                $insert['extend_info'] = $extendInsert;
            }

            //日志
            $log['staff_info_id'] = $param['staff_info_id'];
            $log['source_type']   = StaffSalaryTaxLogModel::SOURCE_BY;
            $log['operator_id']   = $param['staff_info_id'];
            $log['old_data']      = json_encode($oldData,JSON_UNESCAPED_UNICODE);
            unset($insert['staff_info_id']);
            unset($insert['operator_id']);
            $log['new_data']      = json_encode($insert,JSON_UNESCAPED_UNICODE);
            $logModel             = new StaffSalaryTaxLogModel();
            $logModel->create($log);
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }
        return $flag;
    }

    /**
     * 获取枚举
     * @param $params
     * @return array
     */
    public function enumData($params): array
    {
        $staffInfoId = $params['staff_info_id'];

        $data        = StaffSalaryTaxModel::$maritalEnum;
        $enums       = [];
        foreach ($data as $k => $da) {
            $row['code']  = (string)$k;
            $row['label'] = (string)$this->getTranslation()->_($da);
            $enums[]      = $row;
        }
        $return['enums']      = $enums;
        $return['staff_info'] = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId, 'staff_info_id,nationality');
        $sys_nationality = (new SettingEnvServer())->getSetVal('s_f_d_nationality');
        //是否是外籍
        $return['staff_info']['is_foreign_nationality'] = !empty($sys_nationality) && $return['staff_info']['nationality'] != $sys_nationality;

        return $this->checkReturn(['data' => $return]);
    }

    protected function checkStaff($staffInfo)
    {
        //如果 每月 24号-月底最后一天期间 返回 is_edit false
        $data['is_edit'] = true;
        $today           = date('d');
        if ($today >= $this->limitDay) {
            $data['is_edit'] = false;
        }

        //如果 工作所在地 非马来 返回 is_work_at false
        $data['is_work_at'] = true;
        if ($staffInfo['working_country'] != HrStaffInfoModel::WORKING_COUNTRY_MY) {
            $data['is_work_at'] = false;
        }
        $data['is_permission'] = true;
        if ($staffInfo['formal'] != HrStaffInfoModel::FORMAL_1 || $staffInfo['is_sub_staff'] != HrStaffInfoModel::IS_SUB_STAFF_0
            || !in_array($staffInfo['state'], [HrStaffInfoModel::STATE_1, HrStaffInfoModel::STATE_3])) {
            $data['is_permission'] = false;
        }
        return $data;
    }


    //个税信息 消息发放 初始化任务调用 每天发送任务调用
    public function sendMsg($staffIds, $typeCode)
    {
        if (empty($staffIds)) {
            return true;
        }
        $title = '';
        if ($typeCode == StaffSalaryTaxLogModel::MSG_CODE_INIT) {
            $title = $this->getTranslation()->_('salary_tax_confirm');
        }
        if ($typeCode == StaffSalaryTaxLogModel::MSG_CODE_NEW_HIRE) {
            $title = $this->getTranslation()->_('salary_tax_input');
        }
        //工号转成map
        $ids = [];
        foreach ($staffIds as $id){
            $ids[] = ['id' => $id];
        }

        $kit_param                       = [];
        $kit_param['staff_info_ids_str'] = implode(',', $staffIds);
        $kit_param['staff_users']        = $ids;
        $kit_param['message_title']      = $title;
        $kit_param['message_content']    = $typeCode;
        $kit_param['category']           = MessageEnums::MESSAGE_CATEGORY_CODE_SALARY_TAX;

        $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($kit_param);
        $res = $bi_rpc->execute();
        if (!isset($res['result']['code']) || $res['result']['code'] != ErrCode::SUCCESS) {
            $this->getDI()->get('logger')->write_log([
                'function' => 'salaryTaxSendAction',
                'message'  => '消息发送失败',
                'params'   => $kit_param,
                'result'   => $res,
            ]);
            return false;
        }
        return true;
    }

    /**
     * 获取扩展数据
     * @throws ValidationException|\DateMalformedStringException
     */
    private function checkStaffExtend($params, array $staffInfo): array
    {
        $sys_nationality = (new SettingEnvServer())->getSetVal('s_f_d_nationality');
        //会否是外籍
        $staffInfo['is_foreign_nationality'] = $is_foreign_nationality = $sys_nationality != $staffInfo['nationality'];

        $t = $this->getTranslation();
        $result = $id_no_list  = [];
        if ($params['is_disabled'] == StaffSalaryTaxModel::YES) { //员工本人是否残疾
            if (empty($params['employee_disability_card_no'])) {
                throw new ValidationException($t->_('employee_disability_card_no_error_1'));
            }
            if (empty($params['employee_disability_card_photo'])) {
                throw new ValidationException($t->_('employee_disability_card_photo_error'));
            }
            // 14位字母+数字
            if (!$is_foreign_nationality && !preg_match("/^[a-zA-Z0-9]{14}$/u", $params['employee_disability_card_no'])) {
                throw new ValidationException($t->_('employee_disability_card_no_error2'));
            }
            $result[] = [
                'pid'                   => $params['pid'],
                'category'              => StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_EMPLOYEE,
                'identity_name'         => '',
                'identity_no'           => '',
                'disability_card_no'    => $params['employee_disability_card_no'],
                'disability_card_photo' => $params['employee_disability_card_photo'],
            ];
        }

        if ($params['spouse_is_disabled'] == StaffSalaryTaxModel::YES) { //员工配偶是否残疾
            if (empty($params['employee_spouse_disability_card_no'])) {
                throw new ValidationException($t->_('employee_spouse_disability_card_no_error'));
            }
            if (empty($params['employee_spouse_disability_card_photo'])) {
                throw new ValidationException($t->_('employee_spouse_disability_card_photo_error'));
            }
            // 14位字母+数字
            if (!$is_foreign_nationality && !preg_match("/^[a-zA-Z0-9]{14}$/u", $params['employee_spouse_disability_card_no'])) {
                throw new ValidationException($t->_('employee_spouse_disability_card_no_error'));
            }
            $result[] = [
                'pid'                   => $params['pid'],
                'category'              => StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_EMPLOYEE_SPOUSE,
                'identity_name'         => '',
                'identity_no'           => '',
                'disability_card_no'    => $params['employee_spouse_disability_card_no'],
                'disability_card_photo' => $params['employee_spouse_disability_card_photo'],
            ];
        }
        $disabled_card_list  = array_column($result, 'disability_card_no');

        //健康的孩子信息
        foreach (self::$childHealthyKeyList as $childHealthyKey) {
            $tmpChildList = $this->getChildList($childHealthyKey, $params, $staffInfo);
            if (empty($tmpChildList)) {
                continue;
            }
            $id_no_list   = array_merge($id_no_list, array_column($tmpChildList, 'identity_no'));
            $result       = array_merge($result, $tmpChildList);
        }


        //残疾的孩子的信息
        foreach (self::$childDisabledKeyList as $childDisabledKey) {
            $tmpChildList       = $this->getChildList($childDisabledKey, $params, $staffInfo, false);
            if (empty($tmpChildList)) {
                continue;
            }
            $id_no_list         = array_merge($id_no_list, array_column($tmpChildList, 'identity_no'));
            $disabled_card_list = array_merge($disabled_card_list, array_column($tmpChildList, 'disability_card_no'));
            $result             = array_merge($result, $tmpChildList);
        }

        //全部孩子的数量
        $totalIdCardNoCnt = $params['child_under_18'] + $params['child_over_18_study'] + $params['child_over_18_end']
            + $params['child_under_18_disabled'] + $params['child_disabled_and_studying'];

        //验证每个孩子的证件必须不能重复
        if ($totalIdCardNoCnt != count(array_unique($id_no_list))) {
            throw new ValidationException($t->_('identity_no_repeat'));
        }
        //本人 、配偶 、残疾孩子的残疾证必须不能重复
        $totalDisabledCardNoCnt = $params['child_under_18_disabled'] + $params['child_disabled_and_studying']
            + intval($params['is_disabled'] == StaffSalaryTaxModel::YES)
            + intval($params['spouse_is_disabled'] == StaffSalaryTaxModel::YES);
        if ($totalDisabledCardNoCnt != count(array_unique($disabled_card_list))) {
            throw new ValidationException($t->_('disability_card_no_repeat'));
        }
        if ($result) {
            //处理一下item key的排序 不然会导致插入数据异常
            $desiredKeyOrder = array_keys(current($result));
            return array_map(function ($item) use ($desiredKeyOrder) {
                return array_combine($desiredKeyOrder, array_map(function ($key) use ($item) {
                    return $item[$key] ?? null;
                }, $desiredKeyOrder));
            }, $result);
        }

        return [];
    }

    /**
     * 校验身份证上年龄
     * @param string $id_card_no
     * @param $isChildUnder18
     * @return bool
     * @throws ValidationException
     * @throws \DateMalformedStringException
     */
    public function checkCardNo(string $id_card_no, $isChildUnder18,$key): bool
    {
        // 提取年份、月份和日期
        $year = substr($id_card_no, 0, 2);
        $month = substr($id_card_no, 2, 2);
        $day = substr($id_card_no, 4, 2);

        // 校验日期格式
        if (!ctype_digit($year) || !ctype_digit($month) || !ctype_digit($day)) {
            throw new ValidationException($this->getTranslation()->_($key.'_invalid_id_card_date'));
        }

        // 补全年份逻辑
        $fullYear = (int)$year + 2000; // 默认 2000 年之后
        if ($fullYear > (int)date('Y')) {
            $fullYear -= 100; // 如果大于当前年份，认为是 1900 年代
        }
        // 校验日期是否有效
        if (!checkdate((int)$month, (int)$day, $fullYear)) {
            throw new ValidationException($this->getTranslation()->_($key.'_invalid_id_card_date'));
        }
        // 计算完整出生日期和当前日期差距
        $birthDate = sprintf('%04d-%02d-%02d', $fullYear, $month, $day);
        $currentDate = new DateTime();
        $birthDateTime = new DateTime($birthDate);
        $age = $currentDate->diff($birthDateTime)->y; // 计算年龄

        if ($isChildUnder18 === self::ID_CHECK_UNDER_18) {
            return $age < 18; // 是否小于18岁
        } else {
            return $age >= 18; // 是否大于等于18岁
        }
    }

    /**
     * 获取孩子的信息
     * @param string $key
     * @param $params
     * @param $staff_info
     * @param bool $is_healthy 是否健康，true=健康/false=残疾
     * @return array
     * @throws ValidationException
     * @throws \DateMalformedStringException
     */
    private function getChildList(string $key, $params, $staff_info, bool $is_healthy = true): array
    {
        $t =$this->getTranslation();
        if (!isset($params[$key])) {
            throw new ValidationException($t->_($key.'_not_exists'));
        }
        if ($params[$key] < 0 || $params[$key] > 20) {
            throw new ValidationException($t->_($key.'_value_invalid'));
        }

        if ($params[$key] == 0) {
            return [];
        }

        $childDetailListKey = $key . '_arr';
        if (count($params[$childDetailListKey]) != $params[$key]) {
            throw new ValidationException($t->_($key.'_miss_args'));
        }
        $checkCardType  = $this->getValidateCardType($key, $staff_info);
        $result         = [];
        foreach ($params[$childDetailListKey] as $value) {
            //残疾就要残疾证图
            if (!$is_healthy && (empty($value['disability_card_no']) || empty($value['disability_card_photo']))) {
                throw new ValidationException($t->_($key . '_miss_args'));
            }
            //非外籍的 验证 证件号和残疾证
            if (!$staff_info['is_foreign_nationality']) {
                if (!is_numeric($value['identity_no']) || mb_strlen($value['identity_no']) > 12) {
                    throw new ValidationException($t->_($key . '_identity_no_error'));
                }
                //验证18岁
                if ($checkCardType && !$this->checkCardNo($value['identity_no'], $checkCardType,$key)) {
                    throw new ValidationException($t->_($key . '_identity_no_error'));
                }
                // 14位字母+数字
                if (!$is_healthy && !preg_match("/^[a-zA-Z0-9]{14}$/u", $value['disability_card_no'])) {
                    throw new ValidationException($t->_($key . '_disability_card_no_error'));
                }
            }

            $result[] = [
                'pid'                   => $params['pid'],
                'category'              => $this->getCategoryByKey($key),
                'identity_name'         => $value['identity_name'],
                'identity_no'           => $value['identity_no'],
                'disability_card_no'    => $is_healthy ? '' : $value['disability_card_no'],
                'disability_card_photo' => $is_healthy ? '' : $value['disability_card_photo'],
            ];
        }
        return $result;
    }

    private function getCategoryByKey(string $key)
    {
        $category = '';
        switch ($key) {
            case 'child_under_18':
                $category = StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_CHILD_UNDER_18;
                break;
            case 'child_over_18_study':
                $category = StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_CHILD_OVER_18_STUDY;
                break;
            case 'child_over_18_end':
                $category = StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_CHILD_OVER_18_END;
                break;
            case 'child_under_18_disabled':
                $category = StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_CHILD_UNDER_18_DISABLED;
                break;
            case 'child_disabled_and_studying':
                $category = StaffSalaryTaxExtendModel::TAX_INFO_CATEGORY_CHILD_DISABLED_AND_STUDYING;
                break;
        }
        return $category;
    }

    /**
     * 获取校验类型
     * @param $key
     * @param $staffInfo
     * @return int 0=不校验 1=小于18 2=大于等于18
     */
    private function getValidateCardType($key, $staffInfo): int
    {
        //外籍
        if ($staffInfo['is_foreign_nationality']) {
            return self::ID_CHECK_DEFAULT;
        }
        switch ($key) {
            case 'child_under_18':
            case 'child_under_18_disabled':
                $category = self::ID_CHECK_UNDER_18;
                break;
            case 'child_disabled_and_studying':
                $category = self::ID_CHECK_DEFAULT;
                break;
            default:
                $category = self::ID_CHECK_OVER_18;
                break;
        }
        return $category;
    }
}