<?php

namespace FlashExpress\bi\App\Server;


use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Enums\AuditDetailOperationsEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\WorkflowEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\AuditLogModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditUnionModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\WorkflowFormModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeAccidentBusinessModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use Ramsey\Uuid\Provider\Node\RandomNodeProvider;
use Ramsey\Uuid\Uuid;

class ApprovalServer extends BaseServer
{
    public $timezone;
    public $lang;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
        $this->lang     = $lang;
    }

    /**
     * 创建申请
     * @param int $audit_id 审批ID
     * @param int $audit_type 审批类型
     * @param int $user 登录人
     * @param null $workflow 自定义审批流
     * @param array $extend
     * @param string $serialNo
     * @param string $workflow_id
     * @return int
     * @throw ValidationException|InnerException
     * @throws ValidationException
     */
    public function create(
        int $audit_id,
        int $audit_type,
        int $user,
        $workflow = null,
        array $extend = [],
        $serialNo = '',
        $workflow_id = ''
    ) {
        try {
            //[1]检验传入参数
            $this->validateCheck([
                'audit_id'   => $audit_id,
                'audit_type' => $audit_type,
            ], [
                'audit_id'   => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
                'audit_type' => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            ]);

            //[2]处理审批流
            $workflowService = new WorkflowServer($this->lang, $this->timezone);
            $workflowPersistService = WorkflowPersistentServer::getInstance($this->lang, $this->timezone);

            //[2.2]使用审批流模板
            //[2.2.1]获取审批流模板
            $workflowModel = new WorkflowModel();
            $flowBase = $workflowModel->getWorkflowModel($audit_type, $workflow_id);

            if (empty($flowBase)) {
                $this->logger->write_log("workflow_create {$audit_id}_{$audit_type}_{$user} find null" , 'info');
                //没有有效的审批流，请联系管理员
                throw new Exception('There is no effective approval process, please contact the administrator.', ErrCode::WORKFLOW_IS_NULL_ERROR);
            }
            $this->logger->write_log("workflow_create audit_type:{$audit_type},flowBase_id:{$flowBase->id},user:".json_encode($user,JSON_UNESCAPED_UNICODE).',extend:'.json_encode($extend,JSON_UNESCAPED_UNICODE) , 'info');

            //[[2.2.2]固化当前审批流 & 返回固化审批流ID
            //TODO: 暂时方案 ，后续调整为传入一个AuditRequest对象(对象中包含audit_id、audit_type、user等数据);本期调整变更较大
            $extend['audit_id']   = $audit_id;
            $extend['audit_type'] = $audit_type;
            if (!isCountry(['TH', 'MY', 'PH']) && in_array($audit_type, AuditListEnums::getTotalPersistentWorkflowType())) { //全量固化
                $flowId = $workflowPersistService->persistentWorkflow((int)$flowBase->id, $user, $extend, $audit_type,
                    $audit_id);
            } else { //简单固化
                $flowId = $workflowPersistService->persistentWorkflowV2((int)$flowBase->id, $user, $extend, $audit_type,
                    $audit_id, $workflow_id);
            }
	        $this->logger->write_log(sprintf("workflow_create 固化 flow id {%s}", $flowId) , 'info');

            //[4]获取审批流起始节点
            $startNode = $workflowService->getStartNode($flowId);
            if (empty($startNode)) {
                $this->logger->write_log("workflow_create start node {$startNode} find null" , 'info');
                throw new InnerException($this->getTranslation()->_('4008'), ErrCode::WORKFLOW_DATA_ERROR);
            }

            //[3]生成概要信息
            $summary = $this->getSummaryDetail($audit_id, $audit_type, $user);
            //[4]获取指定审批类型的超时配置
            if ($flowBase->is_view == WorkflowModel::WORKFLOW_MANUAL) {
                $timeOutDate = empty($extend['time_out']) ? null : $extend['time_out'];
            } else {
                $timeOutDate = WorkflowOvertimeServer::getInstance($this->lang,
                    $this->timezone)->getLatestAuditOvertimeConfig($audit_id, $audit_type, $workflow_id);
            }

            $this->logger->write_log('getLatestAuditOvertimeConfig date:' . $timeOutDate, 'info');

            //[5]创建请求
            $request = new AuditApplyModel();
            $request->setBizType($audit_type);
            $request->setBizValue($audit_id);
            $request->setSerialNo($serialNo);
            $request->setState(Enums::APPROVAL_STATUS_PENDING);
            $request->setSubmitterId($user);
            $request->setFlowId($flowId);
            $request->setSummary(json_encode($summary, JSON_UNESCAPED_UNICODE));
            $request->setCurrentFlowNodeId($startNode->id);
            $request->setIsCancel(0);
            $request->setIsEdit(0);
            $request->setWorkflowId($flowBase->id);
            $request->setIsDeleted(CommonEnums::IS_DELETED_NO);
            if (!is_null($timeOutDate)) {
                $request->setTimeOut($timeOutDate);
            }
            //固化 base 审批流的版本号，用于回溯审批流相关配置
            if (!empty($flowBase->version)) {
                $request->setWorkflowBaseVersionId($flowBase->version);
            }
            $request->save();

            //[6]校验固化数据
            if (!$this->isWorkflowValid($audit_id, $audit_type, $flowId)) {
                $this->logger->write_log("workflow_create {$audit_id}_{$audit_type}_{$user} Valid failed" , 'info');
                throw new ValidationException($this->getTranslation()->_('err_msg_no_valid_approver'));
            }

            //[7]启动工作流
            //获取参与审批参数
            $parameters = $this->getWorkflowParams($audit_id, $audit_type, $user, null, true, $flowBase->id);
            $workflowService->process($request, $user , enums::WF_ACTION_CREATE, $parameters);

            return $request->getId();
        } catch (ValidationException $vException) {
            $this->logger->write_log("error:" . $vException->getMessage(), 'info');
            throw new ValidationException($vException->getMessage());
        } catch (InnerException $iException) {
            $log_data = [
                'code'           => $iException->getCode(),
                'message'        => $iException->getMessage(),
                'file'           => $iException->getFile(),
                'line'           => $iException->getLine(),
                'trace'          => $iException->getTraceAsString(),
            ];
            $this->logger->write_log($log_data);
            return false;
        } catch (Exception $e) {
            $log_data = [
                'code'           => $e->getCode(),
                'message'        => $e->getMessage(),
                'file'           => $e->getFile(),
                'line'           => $e->getLine(),
                'trace'          => $e->getTraceAsString(),
            ];
            $this->logger->write_log($log_data);
            return false;
        }
    }

    /**
     * 创建审批流
     * @param $params
     * @return mixed
     * @throws ValidationException
     * @throws InnerException
     */
    public function createEx($params = [])
    {
        //[1]检验传入参数
        $this->validateCheck($params, [
            'audit_id'   => 'Required|Int|>>>:' . $this->getTranslation()->_('arg_input_error'),
            'audit_type' => 'Required|Int|>>>:' . $this->getTranslation()->_('arg_input_error'),
        ]);
        //创建审批
        //根据不同模式，定制化创建
        $createModel = $params['model'] ?? WorkflowEnums::WORKFLOW_CREATE_MODEL_NORMAL;
        $result      = false;
        switch ($createModel) {
            case WorkflowEnums::WORKFLOW_CREATE_MODEL_NORMAL:
                $result = $this->create($params['audit_id'],
                    $params['audit_type'],
                    $params['user'],
                    null,
                    $params['extend'] ?? [],
                    $params['serial_no'] ?? '',
                    $params['workflow_id'] ?? ''
                );
                break;
            case WorkflowEnums::WORKFLOW_CREATE_MODEL_DELAY_STAGE_1:
                $result = $this->createDelayStageOne($params);
                break;
            case WorkflowEnums::WORKFLOW_CREATE_MODEL_DELAY_STAGE_2:
                $result = $this->createDelayStageTwo($params);
                break;
            case WorkflowEnums::WORKFLOW_CREATE_MODEL_EDIT:
                $result = $this->createEditApproval($params);
                break;
        }
        return $result;
    }

    /**
     * @description 重新申请
     * @param array $paramIn
     * @return mixed
     * @throws InnerException
     * @throws ValidationException
     * @throws Exception
     */
    public function recreate(array $paramIn = [])
    {
        //[1]检验传入参数
        $this->validateCheck($paramIn, [
            'audit_id'     => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'audit_type'   => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'submitter_id' => 'Required|Int|>>>:' . $this->getTranslation()->_('miss_args'),
        ]);
        $audit_id    = $paramIn['audit_id'];
        $audit_type  = $paramIn['audit_type'];
        $workflow_id = $paramIn['workflow_id'] ?? null;
        $user        = $paramIn['submitter_id'];
        $reason      = $paramIn['reason'] ?? "";
        $extend      = $paramIn['extend'] ?? [];

        //[2]检验审批状态
        $request = AuditApplyModel::findFirst([
            'conditions' => "biz_type = :type: and biz_value = :id:",
            'bind'       => ['type' => $audit_type, 'id' => $audit_id],
        ]);
        if (empty($request)) {
            throw new ValidationException('invalid data');
        }

        //[3]把原有审批流 delete & 重新固化当前审批流 & 返回固化审批流ID
        $workflowModel = new WorkflowModel();
        $flowBase = $workflowModel->getWorkflowModel($audit_type, $workflow_id);
        if (empty($flowBase)) {
            $this->logger->write_log("workflow_create {$audit_id}_{$audit_type}_{$user} find null" , 'info');
            //没有有效的审批流，请联系管理员
            throw new Exception('There is no effective approval process, please contact the administrator.', ErrCode::WORKFLOW_IS_NULL_ERROR);
        }

        $workflowService = new WorkflowServer($this->lang, $this->timezone);
        $workflowPersistentService = WorkflowPersistentServer::getInstance($this->lang, $this->timezone);
        $workflowService->processWorkflowStateToFinish($request->getFlowId());

        $extend['is_recreate'] = WorkflowEnums::RECREATE_WORKFLOW;
        $extend['flow_id']     = $request->getFlowId();
        $flowId                = $workflowPersistentService->persistentWorkflowV2((int)$flowBase->id, $user, $extend, $audit_type, $audit_id);
        $this->logger->write_log("workflow_cancel_create 固化 flow id {$flowId}", 'info');

        //[4]校验固化数据
        if (!$this->isWorkflowValid($audit_id, $audit_type, $flowId)) {
            $this->logger->write_log("workflow_create {$audit_id}_{$audit_type}_{$user} Valid failed" , 'info');
            throw new ValidationException($this->getTranslation()->_('err_msg_no_valid_approver'));
        }

        //获取审批流起始节点
        $startNode = $workflowService->getStartNode($flowId);
        if (empty($startNode)) {
            $this->logger->write_log("workflow_create start node {$startNode} find null" , 'info');
            throw new InnerException($this->getTranslation()->_('4008'), ErrCode::WORKFLOW_DATA_ERROR);
        }
        $request->setCurrentFlowNodeId($startNode->id);
        $request->setFinalApprover(null);
        $request->setFinalApprovalTime(null);
        $request->setRejectReason(null);
        $request->setState(Enums::APPROVAL_STATUS_PENDING);
        $request->created_at = gmdate("Y-m-d H:i:s");
        if (!empty($flowBase->version)) {
            $request->setWorkflowBaseVersionId($flowBase->version);
        }
        $request->update();

        //启动工作流
        $workflowService->process($request, $user, enums::WF_ACTION_CREATE,
            $this->getWorkflowParams($audit_id, $audit_type, $user), $reason);
        return $request->getId();
    }

    /**
     * @description 重新申请
     * @param array $paramIn
     * @return mixed
     * @throws InnerException
     * @throws ValidationException
     * @throws Exception
     */
    public function renew(array $paramIn = [])
    {
        //[1]检验传入参数
        $this->validateCheck($paramIn, [
            'audit_id'     => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'audit_type'   => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'submitter_id' => 'Required|Int|>>>:' . $this->getTranslation()->_('miss_args'),
        ]);
        $audit_id    = $paramIn['audit_id'];
        $audit_type  = $paramIn['audit_type'];
        $workflow_id = $paramIn['workflow_id'] ?? null;
        $user        = $paramIn['submitter_id'];
        $reason      = $paramIn['reason'] ?? "";
        $extend      = $paramIn['extend'] ?? [];

        //[2]检验审批状态
        $request = AuditApplyModel::findFirst([
            'conditions' => "biz_type = :type: and biz_value = :id:",
            'bind'       => ['type' => $audit_type, 'id' => $audit_id],
        ]);
        if ($request->state != enums::APPROVAL_STATUS_PENDING) { //非驳回状态，无法重新申请
            throw new ValidationException('please try again');
        }

        //[3]把原有审批流 delete & 重新固化当前审批流 & 返回固化审批流ID
        $workflowModel = new WorkflowModel();
        $flowBase = $workflowModel->getWorkflowModel($audit_type, $workflow_id);
        if (empty($flowBase)) {
            $this->logger->write_log("workflow_create {$audit_id}_{$audit_type}_{$user} find null" , 'info');
            //没有有效的审批流，请联系管理员
            throw new Exception('There is no effective approval process, please contact the administrator.', ErrCode::WORKFLOW_IS_NULL_ERROR);
        }

        $workflowService = new WorkflowServer($this->lang, $this->timezone);
        $workflowPersistentService = WorkflowPersistentServer::getInstance($this->lang, $this->timezone);
        $workflowService->processWorkflowStateToFinish($request->getFlowId());
        $workflowService->delAuditLog($request->getFlowId());
        $workflowService->delApprovalData($audit_id,$audit_type);

        $extend['is_recreate'] = WorkflowEnums::RECREATE_WORKFLOW;
        $extend['flow_id']     = $request->getFlowId();
        $flowId                = $workflowPersistentService->persistentWorkflowV2((int)$flowBase->id, $user, $extend, $audit_type, $audit_id);
        $this->logger->write_log("workflow_cancel_create 固化 flow id {$flowId}", 'info');

        //[4]校验固化数据
        if (!$this->isWorkflowValid($audit_id, $audit_type, $flowId)) {
            $this->logger->write_log("workflow_create {$audit_id}_{$audit_type}_{$user} Valid failed" , 'info');
            throw new ValidationException($this->getTranslation()->_('err_msg_no_valid_approver'));
        }

        //获取审批流起始节点
        $startNode = $workflowService->getStartNode($flowId);
        if (empty($startNode)) {
            $this->logger->write_log("workflow_create start node {$startNode} find null" , 'info');
            throw new InnerException($this->getTranslation()->_('4008'), ErrCode::WORKFLOW_DATA_ERROR);
        }
        $request->setCurrentFlowNodeId($startNode->id);
        $request->setFinalApprover(null);
        $request->setFinalApprovalTime(null);
        $request->setRejectReason(null);
        $request->setState(Enums::APPROVAL_STATUS_PENDING);
        $request->setWorkflowId($flowBase->id);
        $request->created_at = gmdate("Y-m-d H:i:s");
        if (!empty($flowBase->version)) {
            $request->setWorkflowBaseVersionId($flowBase->version);
        }
        $request->update();

        //启动工作流
        $workflowService->process($request, $user, enums::WF_ACTION_CREATE,
            $this->getWorkflowParams($audit_id, $audit_type, $user), $reason);
        return $request->getId();
    }

    /**
     * 撤销操作 需要重复走原申请的审批流 操作重新固化 并且置当前节点为初始节点
     * @param int $audit_id
     * @param int $audit_type
     * @param int $user
     * @return bool
     * @throws ValidationException
     */
    public function cancel_create(int $audit_id, int $audit_type, $reason,int $user, $id_union = '',$extend = [],$workflow_id='')
    {
        if(empty($audit_id) || empty($audit_type) || empty($user)) {
            return false;
        }

        $request = AuditApplyModel::findFirst([
            'conditions' => "biz_type = :type: and biz_value = :id:",
            'bind'       => ['type' => $audit_type, 'id' => $audit_id],
            "for_update" => true,
        ]);

        //如果 该申请已经操作过一次 撤销审批流 不能再次操作撤销
        if ($request->getState() != enums::APPROVAL_STATUS_APPROVAL && $request->getIsCancel() > AuditApplyModel::AUDIT_SEQUENCE_NORMAL) {
            throw new ValidationException('can not cancel this record again');
        }

        $workflowModel = new WorkflowModel();
        $flowBase = $workflowModel->getWorkflowModel($audit_type, $workflow_id);

        if (empty($flowBase)) { //无有效的审批流，请联系管理员
            $this->logger->write_log("workflow_create {$audit_id}_{$audit_type}_{$user} find null" , 'info');
            throw new ValidationException('There is no effective approval process, please contact the administrator.', ErrCode::WORKFLOW_IS_NULL_ERROR);
        }
        //把原有审批流 delete & 重新固化当前审批流 & 返回固化审批流ID
        $node_sql = "update workflow_node set deleted = 1 where flow_id = '{$request->flow_id}'";
        $relate_sql = "update workflow_node_relate set deleted = 1 where flow_id = '{$request->flow_id}'";
        $this->getDI()->get('db')->execute($node_sql);
        $this->getDI()->get('db')->execute($relate_sql);

        $workflowService = new WorkflowServer($this->lang, $this->timezone);
        $workflowPersistentService = WorkflowPersistentServer::getInstance($this->lang, $this->timezone);
        $extend['is_cancel'] = true;
        $extend['flow_id'] = $request->flow_id;

        $flowId = $workflowPersistentService->persistentWorkflowV2((int)$flowBase->id, $user, $extend,$audit_type,$audit_id,$workflow_id);
        $this->getDI()->get('logger')->write_log("workflow_cancel_create 固化 flow id {$flowId}" , 'info');

        //[4]校验固化数据
        if (!$this->isWorkflowValid($audit_id, $audit_type, $flowId)) {
            $this->getDI()->get('logger')->write_log("workflow_create {$audit_id}_{$audit_type}_{$user} Valid failed" , 'info');
            throw new ValidationException($this->getTranslation()->_('err_msg_no_valid_approver'));
        }

        //获取审批流起始节点
        $startNode = $workflowService->getStartNode($flowId);
        if (empty($startNode)) {
            $this->getDI()->get('logger')->write_log("workflow_create start node {$startNode} find null" , 'info');
            throw new InnerException($this->getTranslation()->_('4008'), ErrCode::WORKFLOW_DATA_ERROR);
        }
        $request->setCurrentFlowNodeId($startNode->id);
        $request->setState(enums::APPROVAL_STATUS_PENDING);
        $request->setIsCancel($request->getIsCancel() + 1);
        $request->setIsEdit(0);
        $request->setSummary($this->genNewSummary($request->getSummary()));
        $request->setCreatedAt(gmdate("Y-m-d H:i:s"));
        if (!empty($flowBase->version)) {
            $request->setWorkflowBaseVersionId($flowBase->version);
        }
        $request->update();

        //启动工作流
        $workflowService->process($request, $user , enums::WF_ACTION_CREATE_CANCEL, $this->getWorkflowParams($audit_id, $audit_type, $user),$reason);
        return $request->getId();
    }

    /**
     * @description 多阶段创建
     * @throws ValidationException
     */
    public function createMultiStage(array $paramIn = [])
    {
        $auditId    = $paramIn['audit_id'];
        $auditType  = $paramIn['audit_type'];
        $user       = $paramIn['submitter_id'];
        $workflowId  = $paramIn['workflow_id'];
        $extend     = $paramIn['extend'];

        //[1]检验传入参数
        $this->validateCheck([
            'audit_id'   => $auditId,
            'audit_type' => $auditType,
        ], [
            'audit_id'   => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'audit_type' => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
        ]);

        //[2]检验审批状态
        $request = AuditApplyModel::findFirst([
            'conditions' => "biz_type = :audit_type: and biz_value = :audit_id:",
            'bind'       => ['audit_type' => $auditType, 'audit_id' => $auditId],
        ]);
        if (empty($request)) { //数据不存在
            throw new ValidationException('please try again');
        }

        if ($request->getState() != enums::APPROVAL_STATUS_PENDING) { //非待审批状态，无法创建多阶段审批流
            throw new ValidationException('please try again');
        }

        //[3]把原有审批流 delete & 重新固化当前审批流 & 返回固化审批流ID
        $workflowService = new WorkflowServer($this->lang, $this->timezone);
        $workflowPersistentService = WorkflowPersistentServer::getInstance($this->lang, $this->timezone);
        $workflowService->processWorkflowStateToFinish($request->getFlowId());

        $extend['is_recreate'] = WorkflowEnums::RECREATE_WORKFLOW;
        $extend['flow_id']     = $request->getFlowId();
        $flowId                = $workflowPersistentService->persistentWorkflowV2((int)$workflowId, $user, $extend, $auditType, $auditId);
        $this->logger->write_log("workflow_cancel_create 固化 flow id {$flowId}", 'info');

        //[4]校验固化数据
        if (!$this->isWorkflowValid($auditId, $auditType, $flowId)) {
            $this->logger->write_log("workflow_create {$auditId}_{$auditType}_{$user} Valid failed" , 'info');
            throw new ValidationException($this->getTranslation()->_('err_msg_no_valid_approver'));
        }

        //获取详情
        $flowBase = WorkflowModel::findFirst($workflowId);

        //[4]获取指定审批类型的超时配置
        if ($flowBase->is_view == WorkflowModel::WORKFLOW_MANUAL) {
            $timeOutDate = empty($extend['time_out']) ? null : $extend['time_out'];
        } else {
            $timeOutDate = WorkflowOvertimeServer::getInstance($this->lang,
                $this->timezone)->getLatestAuditOvertimeConfig($auditId, $auditType, $workflowId);
        }
        $this->logger->write_log('getLatestAuditOvertimeConfig date:' . $timeOutDate, 'info');

        //获取审批流起始节点
        $startNode = $workflowService->getStartNode($flowId);
        if (empty($startNode)) {
            $this->logger->write_log("workflow_create start node {$startNode} find null" , 'info');
            throw new InnerException($this->getTranslation()->_('4008'), ErrCode::WORKFLOW_DATA_ERROR);
        }
        $request->setCurrentFlowNodeId($startNode->id);
        $request->setFinalApprover(null);
        $request->setFinalApprovalTime(null);
        $request->setRejectReason(null);
        $request->setState(Enums::APPROVAL_STATUS_PENDING);
        $request->setWorkflowId($workflowId);
        if (!is_null($timeOutDate)) {
            $request->setTimeOut($timeOutDate);
        }
        if (!empty($flowBase->version)) {
            $request->setWorkflowBaseVersionId($flowBase->version);
        }
        $request->update();

        //启动工作流
        $workflowService->process($request, $user, enums::WF_ACTION_CREATE_WITHOUT_LOG,
            $this->getWorkflowParams($auditId, $auditType, $user), '');
        return $request->getId();
    }

    /**
     * 审批同意
     * @param $audit_id
     * @param $audit_type
     * @param $user
     * @param null $approval_reason
     * @param null $extend
     * @param array $errInfo
     * @return mixed
     * @throws Exception
     */
    public function approval($audit_id, $audit_type, $user, $approval_reason = null, $extend = null, &$errInfo = [])
    {

        //[1]检验传入参数
        $this->validateCheck([
            'audit_id'   => $audit_id,
            'audit_type' => $audit_type
        ], [
            'audit_id'   => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'audit_type' => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
        ]);
        $this->getDI()->get('db')->begin();
        try {
            //验证申请数据
            $request = AuditApplyModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and state = :state:",
                'bind' => [
                    'type'  => $audit_type,
                    'value' => $audit_id,
                    'state' => enums::APPROVAL_STATUS_PENDING
                ],
                'for_update' => true,
            ]);

            if (empty($request)) {
                throw new ValidationException('no valid data', ErrCode::WORKFLOW_DATA_ERROR);
            }

            //验证审批节点
            //这里提供一个不验证审批人的入口
            //如果提供super 将不再验证审批节点中存在审批人

            //使用该入口的审批  [加班车审批=中控审批]
            //
            if (!(isset($extend['super']) && $extend['super'])) {
                $workFlowNode = WorkflowNodeModel::findFirst([
                    'conditions' => ' id = :id: ',
                    'bind' => ['id' => $request->getCurrentFlowNodeId()]
                ])->toArray();

                if (isset($workFlowNode['type']) && $workFlowNode['type'] == enums::NODE_COUNTERSIGN) {
                    //会签无待审批
                    $approvalInfo = AuditApprovalModel::findFirst([
                        'conditions' => 'biz_type = :type: and biz_value = :value: and state = 1 and flow_node_id = :node_id: and approval_id = :staff_id: and deleted = 0',
                        'bind' => [
                            'type' => $audit_type,
                            'value' => $audit_id,
                            'node_id' => $request->getCurrentFlowNodeId(),
                            'staff_id' => $user,
                        ]
                    ]);
                    if (empty($approvalInfo)) {
                        throw new ValidationException($this->getTranslation()->_('please try again'), ErrCode::WORKFLOW_CURRENT_USER_DO_NOT_HAVE_PERMISSION);
                    }
                }

                if (!in_array($user, explode(',', $workFlowNode['auditor_id']))) {
                    //当前用户无审批权限 1=双击时的第二次请求 2=当前审批人无权限
                    $this->logger->write_log("当前审批人: {$user}, 申请类型: {$audit_type}, 申请ID: {$audit_id}", 'info');
                    throw new ValidationException($this->getTranslation()->_('please try again'), ErrCode::WORKFLOW_CURRENT_USER_DO_NOT_HAVE_PERMISSION);
                }
            }

            $return = (new WorkflowServer($this->lang, $this->timezone))->process($request, $user, Enums::WF_ACTION_APPROVE,
                                                                                 $this->getWorkflowParams($audit_id, $audit_type, $user, enums::WF_ACTION_APPROVE), $approval_reason);

            $this->getDI()->get('db')->commit();
            return $return;
        } catch (ValidationException $vException) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log('approval exception :'  . $vException->getFile() . ' line ' . $vException->getLine() . ' msg ' . $vException->getMessage() . $vException->getTraceAsString(), 'info');
            $errInfo['code'] = $vException->getCode();
            $errInfo['message'] = $vException->getMessage();
            return false;
        } catch (InnerException $iException) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log('approval exception :'  . $iException->getFile() . ' line ' . $iException->getLine() . ' msg ' . $iException->getMessage() . $iException->getTraceAsString());
            $errInfo['code'] = ErrCode::SYSTEM_ERROR;
            $errInfo['message'] = $this->getTranslation()->_('4008');
            return false;
        }
    }

    /**
     * 审批驳回
     * @param $audit_id
     * @param $audit_type
     * @param $reject_reason
     * @param $user
     * @param null $extend
     * @param array $errInfo
     * @return mixed
     */
    public function reject($audit_id, $audit_type, $reject_reason, $user, $extend = null, &$errInfo = [])
    {
        //[1]检验传入参数
        $this->validateCheck([
            'audit_id'   => $audit_id,
            'audit_type' => $audit_type
        ], [
            'audit_id'   => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'audit_type' => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
        ]);
        $this->getDI()->get('db')->begin();
        try {

            $request = AuditApplyModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and state = :state:",
                'bind' => [
                    'type'  => $audit_type,
                    'value' => $audit_id,
                    'state' => enums::APPROVAL_STATUS_PENDING
                ],
                'for_update' => true,
            ]);

            if (empty($request)) {
                throw new ValidationException('no valid data', ErrCode::WORKFLOW_DATA_ERROR);
            }

            if (! (isset($extend['super']) && $extend['super'])) {
                $workFlowNode = WorkflowNodeModel::findFirst([
                    'conditions' => ' id = :id: ',
                    'bind' => ['id' => $request->getCurrentFlowNodeId()]
                ])->toArray();
                if (!in_array($user, explode(',', $workFlowNode['auditor_id']))) {
                    $this->getDI()->get('logger')->write_log("当前审批人: {$user}, 申请类型: {$audit_type}, 申请ID: {$audit_id}", 'info');
                    throw new ValidationException($this->getTranslation()->_('please try again'), ErrCode::WORKFLOW_CURRENT_USER_DO_NOT_HAVE_PERMISSION);
                }
            }

            $return   = (new WorkflowServer($this->lang, $this->timezone))->process($request, $user, Enums::WF_ACTION_REJECT,
                                                                                 $this->getWorkflowParams($audit_id, $audit_type, $user, enums::APPROVAL_STATUS_REJECTED), $reject_reason);

            $this->getDI()->get('db')->commit();
            return $return;
        } catch (InnerException $iException) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log('err message:' . $iException->getMessage() . ' file ' . $iException->getFile() . ' line ' . $iException->getLine(). $iException->getTraceAsString());
            $errInfo['code'] = ErrCode::SYSTEM_ERROR;
            $errInfo['message'] = $this->getTranslation()->_('4008');
            return false;
        } catch (ValidationException | Exception $e) {
            $this->logger->write_log('err message:' . $e->getMessage() . ' file ' . $e->getFile() . ' line ' . $e->getLine() . $e->getTraceAsString(), 'notice');
            $this->getDI()->get('db')->rollback();
            $errInfo['code'] = $e->getCode();
            $errInfo['message'] = $e->getMessage();
            return false;
        }
    }

    /**
     * 审批驳回(驳回到某个节点重新开始)
     * 不允许驳回至申请人！！！！
     * @param $audit_id
     * @param $audit_type
     * @param $reject_reason
     * @param $user
     * @param null $extend
     * @return mixed
     * @throws ValidationException
     */
    public function rejectRollBack($audit_id, $audit_type, $reject_reason, $user, $extend = null)
    {
        //[1]检验传入参数
        $this->validateCheck([
            'audit_id'   => $audit_id,
            'audit_type' => $audit_type
        ], [
            'audit_id'   => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'audit_type' => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
        ]);
        $this->getDI()->get('db')->begin();
        try {
            $request = AuditApplyModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type:",
                'bind' => [
                    'type'  => $audit_type,
                    'value' => $audit_id,
                ],
                'for_update' => true,
            ]);

            if (empty($request)) {
                throw new ValidationException('no valid data');
            }


            $return = (new WorkflowServer($this->lang, $this->timezone))->process($request, $user, Enums::WF_ACTION_REJECT_ROLL_BACK,
                $this->getWorkflowParams($audit_id, $audit_type, $user, enums::APPROVAL_STATUS_REJECTED), $reject_reason);
            $this->getDI()->get('db')->commit();
            return $return;
        } catch (InnerException $iException) {
            $this->getDI()->get('logger')->write_log('err message:' . $iException->getMessage() . ' file ' . $iException->getFile() . ' line ' . $iException->getLine(). $iException->getTraceAsString());
            $this->getDI()->get('db')->rollback();
            return false;
        } catch (ValidationException $e) {
            $this->getDI()->get('logger')->write_log('err message:' . $e->getMessage() . ' file ' . $e->getFile() . ' line ' . $e->getLine() . $e->getTraceAsString(), 'notice');
            $this->getDI()->get('db')->rollback();
            return false;
        }
    }

    /**
     * 待审批、审批同意，系统自动驳回
     * @param $audit_id
     * @param $audit_type
     * @param $reject_reason
     * @param $user
     * @param $extend
     * @return mixed
     * @throws ValidationException
     * @throws Exception
     */
    public function approvalSystemReject($audit_id, $audit_type, $reject_reason, $user, $extend = null)
    {
        //[1]检验传入参数
        $this->validateCheck([
            'audit_id'   => $audit_id,
            'audit_type' => $audit_type
        ], [
            'audit_id'   => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'audit_type' => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
        ]);

        $request = AuditApplyModel::findFirst([
            'conditions' => "biz_value = :value: and biz_type = :type:",
            'bind' => [
                'type'  => $audit_type,
                'value' => $audit_id,
            ],
        ]);
        if (empty($request)) {
            throw new ValidationException('no valid data');
        }

        //如待审批，则直接驳回
        if ($request->getState() == enums::APPROVAL_STATUS_PENDING) {
            return $this->reject($audit_id, $audit_type, $reject_reason, $user, $extend);
        }

        //如果不是已同意不允许驳回
        if ($request->getState() != enums::APPROVAL_STATUS_APPROVAL) {
            throw new ValidationException('please try again');
        }

        $this->getDI()->get('db')->begin();
        $request = AuditApplyModel::findFirst([
            'conditions' => 'biz_value = :value: and biz_type = :type:',
            'bind' => [
                'type'  => $audit_type,
                'value' => $audit_id,
            ],
            'for_update' => true,
        ]);
        if ($request->getState() == enums::APPROVAL_STATUS_APPROVAL) {
            $request->setState(enums::APPROVAL_STATUS_REJECTED);
            $request->setFinalApprovalTime(date('Y-m-d H:i:s'));
            $request->setFinalApprover($user);
            $request->setRejectReason($reject_reason);
            $request->save();

            $auditCallbackParams = [
                'approval'      => [],
                'staff_id'      => $user,
                'is_cancel'     => $request->getIsCancel(),
                'is_start_node' => false,
                'next_node_id'  => 0,
                'super'         => 0,
                'remark'        => $reject_reason,
            ];
            $this->setAuditProperty($request->getBizValue(), $request->getBizType(), Enums::APPROVAL_STATUS_REJECTED,
                $auditCallbackParams);

            //创建日志
            //审核日志记录
            (new WorkflowServer($this->lang, $this->timezone))->saveAuditLog($request, $user,
                enums::WF_ACTION_REJECT, $reject_reason);

            $this->getDI()->get('db')->commit();
        }
        return true;
    }

    /**
     * 申请人撤销
     * @param $audit_id
     * @param $audit_type
     * @param $cancel_reason
     * @param $user
     * @param null $extend
     * @param int $errInfo
     * @return mixed
     * @throws Exception
     */
    public function cancel($audit_id, $audit_type, $cancel_reason, $user, $extend = null, &$errInfo = 0)
    {
        //[1]检验传入参数
        $this->validateCheck([
            'audit_id'   => $audit_id,
            'audit_type' => $audit_type
        ], [
            'audit_id'   => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'audit_type' => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
        ]);
        $this->getDI()->get('db')->begin();
        try {

            $request = AuditApplyModel::findFirst([
                'conditions' => 'biz_value = :value: and biz_type = :type:',
                'bind' => [
                    'type'  => $audit_type,
                    'value' => $audit_id,
                ],
                'for_update' => true,
            ]);

            if (empty($request)) {
                throw new ValidationException('no valid data', ErrCode::WORKFLOW_DATA_ERROR);
            }

            if ($request->getDelayState() == WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_PENDING) { //延时审批撤销
                $request->setState(enums::APPROVAL_STATUS_CANCEL);
                $request->setDelayState(WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_CANCELED);
                $request->setFinalApprovalTime(date('Y-m-d H:i:s'));
                $request->setFinalApprover($user);
                $request->save();

                $auditCallbackParams = [
                    'approval'      => [],
                    'staff_id'      => $user,
                    'is_cancel'     => $request->getIsCancel(),
                    'is_start_node' => false,
                    'next_node_id'  => 0,
                    'super'         => 0,
                    'remark'        => '',
                ];
                $this->setAuditProperty($request->getBizValue(), $request->getBizType(),Enums::APPROVAL_STATUS_CANCEL, $auditCallbackParams);

                //创建日志
                //审核日志记录
                (new WorkflowServer($this->lang, $this->timezone))->saveAuditLog($request, $user, enums::WF_ACTION_CANCEL, '');

                $this->getDI()->get('db')->commit();
                return $request;
            }

            //[1]校验权限
            //[1.1]校验撤消权限
            //HC申请可以由非申请人撤销
            if (empty($extend['super']) && $request->getSubmitterId() != $user
                && !in_array($request->getBizType(), [
                    enums::$audit_type['HC'],
                    enums::$audit_type['TFOA'],
                    enums::$audit_type['TF'],
                    enums::$audit_type['MSG'],
                    enums::$audit_type['SA'],
                    enums::$audit_type['ST'],
                    enums::$audit_type['RE'],
                    enums::$audit_type['WH_APPLY'],//居家办公申请 审批人可以撤销
                ])
            ) {
                throw new ValidationException($this->getTranslation()->_('2107'), ErrCode::WORKFLOW_CURRENT_USER_DO_NOT_HAVE_PERMISSION);
            }

            //如果正在或者已经走过 撤销审批流程 不允许操作撤销
            //if ($request->is_cancel == enums::IS_CANCEL) {
            //    throw new ValidationException($this->getTranslation()->_('cancel_notice'), ErrCode::WORKFLOW_EXIST_CANCEL_REQUEST);
            //}

            $return = (new WorkflowServer($this->lang, $this->timezone))->process($request, $user, Enums::WF_ACTION_CANCEL,
                $this->getWorkflowParams($audit_id,$audit_type,$user,enums::APPROVAL_STATUS_CANCEL), $cancel_reason,$extend);
            $this->getDI()->get('db')->commit();
            return $return;
        } catch (InnerException $iException) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log('err message:' . $iException->getMessage() . ' file ' . $iException->getFile() . ' line ' . $iException->getLine(). $iException->getTraceAsString());
            $errCode = ErrCode::SYSTEM_ERROR;
            return false;
        } catch (ValidationException $e) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log('err message:' . $e->getMessage() . ' file ' . $e->getFile() . ' line ' . $e->getLine() . $e->getTraceAsString(), 'info');
            $errInfo['code'] = $e->getCode();
            $errInfo['message'] = $e->getMessage();
            return false;
        }
    }
    /**
     * 审批人撤销
     * @param $audit_id
     * @param $audit_type
     * @param $cancel_reason
     * @param $user
     * @param null $extend
     * @return mixed
     * @throws ValidationException
     */
    public function approvalCancel($audit_id, $audit_type, $cancel_reason, $user, $extend = null)
    {
        //[1]检验传入参数
        $this->validateCheck([
            'audit_id'   => $audit_id,
            'audit_type' => $audit_type
        ], [
            'audit_id'   => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'audit_type' => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
        ]);
        $this->getDI()->get('db')->begin();
        try {

        $request = AuditApplyModel::findFirst([
            'conditions' => "biz_value = :value: and biz_type = :type:",
            'bind' => [
                'type'  => $audit_type,
                'value' => $audit_id,
            ],
            'for_update' => true,
        ]);

        if (empty($request)) {
            throw new ValidationException('no valid data');
        }
            $workFlowParam = $this->getWorkflowParams($audit_id, $audit_type, $user, enums::APPROVAL_STATUS_CANCEL);
            $return        = (new WorkflowServer($this->lang, $this->timezone))->process($request, $user, Enums::WF_ACTION_APPROVAL_CANCEL, $workFlowParam, $cancel_reason, $extend);
            $this->getDI()->get('db')->commit();
            return $return;
        } catch (InnerException $iException) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('err message:' . $iException->getTraceAsString());
            return false;
        } catch (ValidationException $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('err message:' . $e->getTraceAsString(), 'info');
            return false;
        }
    }

    /**
     * 超时关闭
     *
     * @param $audit_id
     * @param $audit_type
     * @param $user
     *
     * @return mixed
     */
    public function timeOut($audit_id, $audit_type, $user = Enums::SYSTEM_STAFF_ID)
    {
        //[1]检验传入参数
        $this->validateCheck([
            'audit_id'   => $audit_id,
            'audit_type' => $audit_type
        ], [
            'audit_id'   => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'audit_type' => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
        ]);
        $this->getDI()->get('db')->begin();
        try {
            //验证申请数据
            $request = AuditApplyModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and state = :state:",
                'bind' => [
                    'type'  => $audit_type,
                    'value' => $audit_id,
                    'state' => enums::APPROVAL_STATUS_PENDING,
                ],
                'for_update' => true,
            ]);
            if (empty($request)) {
                throw new ValidationException('no valid data');
            }

            //获取当前的审批人
            $approval = AuditApprovalModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and state = 1 and deleted = 0",
                'bind' => [
                    'type'  => $audit_type,
                    'value' => $audit_id,
                ]
            ]);
            if (empty($approval)) {
                $approvalId = enums::SYSTEM_STAFF_ID;
                //throw new ValidationException('no valid approval');
                $this->logger->write_log(sprintf('approval not exist: audit info:%s, %s', $audit_type, $audit_id), 'info');
            } else {
                $approvalId = $approval->getApprovalId();
            }

            $return = (new WorkflowServer($this->lang, $this->timezone))->process($request, $approvalId, Enums::WF_ACTION_TIMEOUT,
                $this->getWorkflowParams($audit_id, $audit_type, $approvalId), '');
            $this->getDI()->get('db')->commit();
            return $return;

        } catch (ValidationException $vException) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('approval exception :'  . $vException->getFile() . ' line ' . $vException->getLine() . ' msg ' . $vException->getMessage() . $vException->getTraceAsString(), 'info');
            return false;
        } catch (InnerException $iException) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('approval exception :'  . $iException->getFile() . ' line ' . $iException->getLine() . ' msg ' . $iException->getMessage() . $iException->getTraceAsString());
            return false;
        } catch (Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('approval exception :'  . $e->getFile() . ' line ' . $e->getLine() . ' msg ' . $e->getMessage() . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 转交
     * 转交时机：
     * 1. 审批人为空时，并且“审批人为空”的处理配置为“转交“
     * 2. 当审批人审批超时时，超时处理配置的为 “转交”
     *
     * @param $audit_id
     * @param $audit_type
     * @param $to_user
     * @return bool
     */
    public function handOver($audit_id, $audit_type, $to_user, $remark = ''): bool
    {
        //[1]检验传入参数
        $this->validateCheck([
            'audit_id'   => $audit_id,
            'audit_type' => $audit_type
        ], [
            'audit_id'   => "Required|Int|>>>:" . $this->getTranslation()->_('7127'),
            'audit_type' => "Required|Int|>>>:" . $this->getTranslation()->_('7127'),
        ]);
        $this->getDI()->get('db')->begin();

        try {
            //验证申请数据
            $request = AuditApplyModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type:",
                'bind' => [
                    'type'  => $audit_type,
                    'value' => $audit_id,
                ],
                "for_update" => true,
            ]);

            if (empty($request)) {
                throw new ValidationException('no valid data');
            }
            $return = (new WorkflowServer($this->lang, $this->timezone))->processHandOver($request, $to_user, $remark);
            $this->getDI()->get('db')->commit();
            return true;

        } catch (ValidationException $vException) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('approval exception :'  . $vException->getFile() . ' line ' . $vException->getLine() . ' msg ' . $vException->getMessage() . $vException->getTraceAsString(), 'info');
            return false;
        } catch (InnerException $iException) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('approval exception :'  . $iException->getFile() . ' line ' . $iException->getLine() . ' msg ' . $iException->getMessage() . $iException->getTraceAsString());
            return false;
        } catch (Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('approval exception :'  . $e->getFile() . ' line ' . $e->getLine() . ' msg ' . $e->getMessage() . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 获取概要
     * @param $auditId
     * @param $auditType
     * @param $user
     * @return mixed
     */
    public function getSummaryDetail($auditId, $auditType, $user)
    {
        $instance = $this->getInstance($auditType);
        return $instance->genSummary($auditId, $user);
    }

    /**
     * 获取概要
     * @param $auditId
     * @param $auditType
     * @param $user
     * @param $auditShowType
     * @param null $auditStateType
     * @return mixed
     * @throws InnerException
     */
    public function getAuditDetail($auditId, $auditType, $user, $auditShowType, $auditStateType = null, $date_created = '', $log_version = 1,$request_src = '')
    {
        //[1]获取审批详情
        $parameters = [
            'conditions' =>  "biz_value = :value: and biz_type = :type:",
            'bind' => [
                'type'  => $auditType,
                'value' => $auditId,
            ]
        ];
        //切表
        (new AuditApplyModel())->switchTableName($date_created,$auditType,$auditId);
        $request = AuditApplyModel::findFirst($parameters);

        //获取详情
        $instance = $this->getInstance($auditType);

        //创建AuditDetailRequestServer对象,并注入到instance中
        $auditDetailRequest = new AuditDetailRequestServer();
        $auditDetailRequest->init([
            'audit_id'         => $auditId,
            'audit_type'       => $auditType,
            'user'             => $user,
            'audit_show_type'  => $auditShowType,
            'audit_state_type' => $auditStateType,
            'request_src'      => $request_src,
        ]);
        if (method_exists($instance, "setAuditDetailRequest")) {
            $instance->setAuditDetailRequest($auditDetailRequest);
        }
        $data = $instance->getDetail($auditId, $user, $auditShowType);
        //处理显示哪些操作按钮，如果具体审批业务没有处理的话，走新的处理逻辑
        if (empty($data['data']['head']['options'])){
            $rule = $instance->getOptionsRule($auditId);
            $options = (new AuditDetailOptionServer($this->lang, $this->timezone))->init($auditDetailRequest, $rule, $instance)->getDetailOptions();
            $data['data']['head']['options'] = $options;
        }
        $data['data']['head']['is_cancel'] = empty($request) ? 0 : $request->getIsCancel();
        $data['data']['head']['avator']  = 'https://' . env('hris_profile_uri') . env('default_avator');
        if (!empty($request)) {
            //[2]获取审批流
            $workflowStream = $this->switchAuditLogVersion($log_version, $request, $user);

            //[3]获取提交人头像
            $data['data']['head']['avator'] = (new AuditlistRepository($this->lang,$this->timezone))->getPersonAvator($request->getSubmitterId());
        }else{
            //[2]获取历史老审批流
            $workflowStream = (new WorkflowServer($this->lang, $this->timezone))->getAuditLogsHistory($auditId, $auditType);
        }
        $data['data']['stream'] = $workflowStream ?? [];


        return $data;
    }

    /**
     * @description 获取审批流详情(获取审批流日志)-v2
     * 跟第一版不同之处在于：将审批流日志从详情中分离出来
     *
     * @param int $auditId
     * @param int $auditType
     * @param int $user
     * @param int $auditShowType
     * @param int|null $auditStateType
     * @return mixed
     * @throws InnerException
     */
    public function getAuditDetailV2(int $auditId, int $auditType, int $user, int $auditShowType, int $auditStateType = null, $date_created = '')
    {
        //[1]获取审批详情
        $parameters = [
            'conditions' =>  "biz_value = :value: and biz_type = :type:",
            'bind' => [
                'type'  => $auditType,
                'value' => $auditId,
            ]
        ];
        //切表
        (new AuditApplyModel())->switchTableName($date_created,$auditType,$auditId);

        $request = AuditApplyModel::findFirst($parameters);
        $instance = $this->getInstance($auditType);

        //创建AuditDetailRequestServer对象,并注入到instance中
        $auditDetailRequest = new AuditDetailRequestServer();
        $auditDetailRequest->init([
            'audit_id'         => $auditId,
            'audit_type'       => $auditType,
            'user'             => $user,
            'audit_show_type'  => $auditShowType,
            'audit_state_type' => $auditStateType,
        ]);
        if (method_exists($instance, "setAuditDetailRequest")) {
            $instance->setAuditDetailRequest($auditDetailRequest);
        }
        $data = $instance->getDetail($auditId, $user, $auditShowType);
        //处理显示哪些操作按钮，如果具体审批业务没有处理的话，走新的处理逻辑
        if (empty($data['data']['head']['options'])){
            $rule = $instance->getOptionsRule($auditId);
            $options = (new AuditDetailOptionServer($this->lang, $this->timezone))->init($auditDetailRequest, $rule, $instance)->getDetailOptions();
            $data['data']['head']['options'] = $options;
        }
        $data['data']['head']['is_cancel'] =  empty($request) ? 0 : $request->getIsCancel();
        $request  = empty($request) ? [] :  $request->toArray();

        //[3]获取提交人头像
        $data['data']['head']['avator'] = (new AuditlistRepository($this->lang,$this->timezone))->getPersonAvator($request['submitter_id'] ?? '');
        return $data;
    }

    /**
     * 获取概要
     * @param $auditId
     * @param $auditType
     * @param $user
     * @param string $date_created
     * @return mixed
     * @throws InnerException
     */
    public function getAuditLogs($auditId, $auditType, $user, $date_created = '', $log_version = 1)
    {
        //[1]获取审批详情
        $parameters = [
            'conditions' => "biz_value = :value: and biz_type = :type:",
            'bind'       => [
                'type'  => $auditType,
                'value' => $auditId,
            ]
        ];
        //切表
        (new AuditApplyModel())->switchTableName($date_created, $auditType, $auditId);

        $request = AuditApplyModel::findFirst($parameters);
        if (!empty($request)) {
            //[2]获取审批流
            $stream = $this->switchAuditLogVersion($log_version, $request, $user);
        } else {
            //[2]获取历史老审批流
            $stream = (new WorkflowServer($this->lang, $this->timezone))->getAuditLogsHistory($auditId, $auditType);
        }
        return $stream ?? [];
    }

    /**
     * @description 选择指定版本的审批流
     * @param $version
     * @param $request
     * @param $user
     * @return mixed
     * @throws \ReflectionException
     */
    public function switchAuditLogVersion($version, $request, $user)
    {
        $server = new WorkflowServer($this->lang, $this->timezone);
        $server = Tools::reBuildCountryInstance($server, [$this->lang, $this->timezone]);
        switch ($version) {
            case 1:
                $stream = $server->getAuditLogs($request, $user);
                break;
            case 2:
                $stream = $server->getAuditLogsV2($request, $user);
                break;
            case 3:
                $stream = $server->getAuditLogsV3($request, $user);
                break;
            case 4:
                $stream = $server->getAuditLogsV4($request, $user);
                break;
            default:
                break;
        }
        return $stream ?? [];
    }

    /**
     * 获取参与审批条件的数据， 数据将合并为一个数组返回
     *
     * 数据将分为3个部分
     * - 申请人相关的数据
     * - 申请表单相关的数据
     * - 用户自定义数据
     *
     * @param $auditId
     * @param $auditType
     * @param $user
     * @param null $state
     * @param bool $isNeedFindSubmitter
     * @param string $workflow_id
     * @return mixed
     */
    public function getWorkflowParams($auditId, $auditType, $user, $state = null, $isNeedFindSubmitter = true, $workflow_id = '')
    {
	    //查询审批流
        $workflowModel = new WorkflowModel();
        $flowBase = $workflowModel->getWorkflowModel($auditType, $workflow_id);

	    //判断是否已经对接至可视化
        if (!empty($flowBase) && !empty($flowBase->version)) {

            if ($isNeedFindSubmitter) {
                $request = AuditApplyModel::findFirst([
                    'conditions' => "biz_value = :value: and biz_type = :type:",
                    'bind' => [
                        'type'  => $auditType,
                        'value' => $auditId,
                    ]
                ]);
                if (empty($request)) {
                    return [];
                }
                $submitterId = $request->getSubmitterId();
            } else {
                $submitterId = $user;
            }

            //获取申请人相关
            $submitterInfo = $this->getSubmitterInfo($submitterId);

            //获取自定义
            $instance = $this->getInstance($auditType);
            $return   = $instance->getWorkflowParams($auditId, $user, $state);
            return array_merge($return, $submitterInfo);
        } else {
            //获取自定义
            $instance = $this->getInstance($auditType);
            return $instance->getWorkflowParams($auditId, $user, $state);
        }
    }

    /**
     * 获取实例
     * @param $auditType
     * @return mixed
     */
    public function getInstance($auditType)
    {
        switch ($auditType) {
            case Enums::$audit_type['OT']:
            case Enums::$audit_type['BONUS']:
                $class = new OvertimeServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case AuditListEnums::APPROVAL_TYPE_OVERTIME_OS:
                $class = $this->class_factory('OsOvertimeServer',$this->lang, $this->timezone);
                break;
            case AuditListEnums::APPROVAL_TYPE_CLAIMER:
                $class = ClaimerServer::getInstance($this->lang);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['HC']:
                $class = new HcServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['AS']:
            case Enums::$audit_type['ASP']:
                $class = new AssetServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['MA']:
                $class = new WmsServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['AT']:
            case Enums::$audit_type['LE']:
            case Enums::$audit_type['ICAT']:
                $class = new AuditServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['SICK_CERTIFICATE']:
                $class = new SickCertificateServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['BT']:
            case Enums::$audit_type['YCBT']:
            case Enums::$audit_type['GO']:
                $class = new BusinesstripServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case enums::$audit_type['HCB']:
                $class = new HcBudgetServer($this->lang,$this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case enums::$audit_type['RN']:
                $class = new ResignServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case enums::$audit_type['RE']:
                $class = new ReportServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case enums::$audit_type['FDS']:
            case enums::$audit_type['FDP']:
                $class = new FreightDiscountServer($this->lang,$this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['ATB']:
            case Enums::$audit_type['GOC']:
            case Enums::$audit_type['WH']:
                $class = new AttendanceBusinessServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['MSG']:
                $class = new MessageCenterServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['FUEL']:
                $class = new FuelService($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['TF']:
            case Enums::$audit_type['TFOA']:
                if (isCountry(['TH', 'MY', 'PH'])) {
                    $class = $this->class_factory('JobTransferV2Server',$this->lang, $this->timezone);
                    break;
                } else {
                    $class = new JobtransferServer($this->lang, $this->timezone);
                    $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                    break;
                }
            case Enums::$audit_type['OS']:
                $class = new OsStaffServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['PA']:
                $class = new PenaltyAppealServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['VA']:
                $class = new FleetServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['SA']:
                $class = new SalaryServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['AR']:
                $class = new AdjustRoleServer($this->lang, $this->timezone);
	            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['ST']:
                $class = Tools::reBuildCountryInstance(new OfferSignApproveServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['OPR']:
                $class = new OsPriceServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['SAS']:
                $class = new StoreSupportServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['SASS']:
                $class = new StaffSupportStoreServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['MI']:
                $class = new VehicleServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['SYSTEM_CS']:
            case Enums::$audit_type['VEHICLE_APPROVAL']:
            case Enums::$audit_type['FLEET_GENERATE_RECORDS']:
            case AuditListEnums::APPROVAL_TYPE_CS_PAYMENT:
            case AuditListEnums::APPROVAL_TYPE_TRANSPORTATION_CONTRACT:
            case AuditListEnums::APPROVAL_TYPE_PRICE_LIST:
            case AuditListEnums::APPROVAL_TYPE_SINGLE_FARE_ADJUSTMENT:
            case AuditListEnums::APPROVAL_TYPE_VEHICLE_REPAIR_REQUEST://车辆维修申请
            case AuditListEnums::APPROVAL_TYPE_WAREHOUSE_CHANGE_STATUS:
            case AuditListEnums::APPROVAL_TYPE_WAREHOUSE_CHANGE_STORE:
            case AuditListEnums::APPROVAL_TYPE_WAREHOUSE_THREAD_PRICE:
                $class = new SystemExternalApprovalServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['NAS']:
                $class = new MaterialAssetServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['ABE']:
            case Enums::$audit_type['ABEF']:
                $class = new AbnormalExpenseServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['DRIVER_BLACKLIST']:
                $class = new DriverBlackListServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['WMS']:
                $class = new MaterialWmsServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case AuditListEnums::APPROVAL_TYPE_HR_PENALTY_APPEAL:
                $class = new HrPenaltyServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['OS_OT'];
                $class = new OutsourcingOTServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case enums::$audit_type['OSAT']:
                $class = new \FlashExpress\bi\App\Server\Osm\AttendanceServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case Enums::$audit_type['MILEAGE']:
                $class = new MilesServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case enums::$audit_type['QC']:
                $class = new QuitclaimServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case enums::$audit_type['RC']:
                $class = new HrStaffRenewContractServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case AuditListEnums::APPROVAL_TYPE_REINSTATEMENT:
                $class = $this->class_factory('ReinstatementServer',$this->lang, $this->timezone);
                break;
            case enums::$audit_type['STOP']:
                $class = new StopServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case enums::$audit_type['CANCEL_CONTRACT']:
                $class = new CancelContractServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case enums::$audit_type['COMPANY_TERMINATION_CONTRACT']:
                $class = new CompanyTerminationContractServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            case AuditListEnums::APPROVAL_TYPE_VEHICLE_EDIT:
                $class = $this->class_factory('VehicleInfoAuditServer',$this->lang, $this->timezone);
                break;
            case AuditListEnums::APPROVAL_TYPE_TRANSFER_CAR:
                $class = $this->class_factory('TransferCarServer',$this->lang, $this->timezone);
                break;
            case enums::$audit_type['HIRE_CHANGE']:
                $class = new HireTypeChangeServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;

            case AuditListEnums::APPROVAL_TYPE_REEMPLOYMNET:
                $class = $this->class_factory('ReemploymentServer',$this->lang, $this->timezone);
                break;
            case AuditListEnums::APPROVAL_TYPE_ADVANCE_FUEL:
                $class = $this->class_factory('AdvanceFuelServer',$this->lang, $this->timezone);
                break;
            case AuditListEnums::APPROVAL_TYPE_JT_SPECIAL:
                $class = $this->class_factory('JobTransferSpecialServer',$this->lang, $this->timezone);
                break;
            case AuditListEnums::APPROVAL_TYPE_SUSPEND_WORK:
                $class = $this->class_factory('SuspendWorkServer',$this->lang, $this->timezone);
                break;
            case AuditListEnums::APPROVAL_TYPE_IC_RENEWAL:
                $class = $this->class_factory('RenewContractBusinessServer',$this->lang, $this->timezone);
                break;
            case AuditListEnums::APPROVAL_TYPE_FRANCHISEES:
                $class = $this->class_factory('FranchiseesServer',$this->lang, $this->timezone);
                break;
            case AuditListEnums::APPROVAL_TYPE_SUSPENSION:
                $class = $this->class_factory('SuspensionServer',$this->lang, $this->timezone);
                break;
            case AuditListEnums::APPROVAL_TYPE_WORK_HOME_APPLY:
                $class = $this->class_factory('WorkHomeServer',$this->lang, $this->timezone);
                break;
            case AuditListEnums::APPROVAL_TYPE_SCHOOL_PLAN_SEND: //学习计划配置执行类
                $class = new SchoolPlanSendServer($this->lang, $this->timezone);
                $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
                break;
            default:
                $class = '';
                break;
        }

        // 走通用审批流程的OA业务
        if (in_array($auditType, AuditListEnums::$oa_biz_type_item_by_common_audit)) {
            $class = new OACommonAuditServer($this->lang, $this->timezone);
            $class = Tools::reBuildCountryInstance($class, [$this->lang, $this->timezone]);
        }

        if (empty($class)) {
            throw new ValidationException($this->getTranslation()->_('select_class_not_null'));
        }
        return $class;
    }

    /**
     * 回写审批状态
     * @param $auditId
     * @param $auditType
     * @param $state
     * @param $extend
     * @param bool $isFinial
     * @return mixed
     * @throws Exception
     */
    public function setAuditProperty($auditId, $auditType, $state, $extend, bool $isFinial = true)
    {
        $instance = $this->getInstance($auditType);
        return $instance->setProperty($auditId, $state, $extend, $isFinial);
    }

    /**
     * @description  检查是否延时审批
     * @param object $request auditApplyModel对象
     * @return false
     */
    public function checkAuditDelay($request, $action): bool
    {
        if (empty($request)) {
            return false;
        }

        $instance = $this->getInstance($request->getBizType());
        if (method_exists($instance, "checkDelay")) {
            return $instance->checkDelay($request, $action);
        } else {
            return false;
        }
    }

    /**
     * @description  检查是否延时审批
     * @param object $request auditApplyModel对象
     * @return false
     */
    public function checkAutoPass($request): bool
    {
        if (empty($request)) {
            return false;
        }

        $instance = $this->getInstance($request->getBizType());
        if (method_exists($instance, "checkAutoPass")) {
            return $instance->checkAutoPass($request->getBizValue());
        } else {
            return false;
        }
    }

    /**
     * 获取超时配置-设置表单中字段为超时时间-指定审批类型的 form 字段的日期
     * @param $audit_id
     * @param $audit_type
     * @return string
     * @throws ValidationException
     */
    public function getAuditFormOvertimeDate($audit_id, $audit_type): string
    {
        if (empty($audit_type)) {
            return '';
        }

        $instance = $this->getInstance($audit_type);
        if (method_exists($instance, "getAuditFormOvertimeDate")) {
            return $instance->getAuditFormOvertimeDate($audit_id);
        } else {
            return '';
        }
    }

    /**
     * 校验已经固化的审批流
     * 返回true：  将创建审批流
     * 返回false   将throw异常
     * @param int $auditType
     * @param string $flowId
     * @return bool
     * @throws ValidationException
     */
    private function isWorkflowValid(int $auditId, int $auditType, string $flowId): bool
    {
        $instance = $this->getInstance($auditType);

        if (!method_exists($instance, 'checkPersistWorkflow')) {
            return true;
        }

        //自定义校验审批流的逻辑
        //例如：「外协员工申请」审批人为空,则不能申请
        return $instance->checkPersistWorkflow($flowId, $auditId);

    }

    /**
     * 获取申请人相关信息
     * @param $auditId
     * @param $auditType
     * @return array
     */
    public function getSubmitterInfo($submitterId): array
    {
        return (new StaffRepository())->getStaffInfoV3($submitterId);
    }

    /**
     * 获取审批类型翻译
     * @param $lang
     * @return array
     */
    public function getAuditType($lang): array
    {
        $this->lang = $lang;
        $t = $this->getTranslation();
        $result = [];
        foreach (Enums::$at as $k => $v) {
            $result[] = [
                'type' => $k,
                'label' =>  $t->_($v) ?? ''
            ];
        }

        return $result;
    }

    /**
     * @description: 修改申请概要 json数据
     * @param $biz_type  审批类型
     * @param $biz_value  业务数据ID
     * @param $summary  申请概要 数组
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/4/11 21:51
     */
    public function setAuditSummary($biz_type = 0 , $biz_value = 0 , $summary=[]){
        try{
            //如果是数组 转换为 json
            $summary = is_array($summary) ? json_encode($summary,JSON_UNESCAPED_UNICODE) : $summary;
            $conditions = [
                'conditions' => " biz_type = ? and  biz_value =  ?  ",
                'bind'       => [$biz_type,$biz_value],
            ];
            $save_data = [
                "summary" => $summary,
            ];

            //修改
            if(empty($biz_type) || empty($biz_value)){
                throw new Exception('修改申请概要失败! ==>conditions => '.json_encode($conditions,JSON_UNESCAPED_UNICODE).' data=>'.json_encode($save_data,JSON_UNESCAPED_UNICODE));
            }
            //先查询一下 保存修改前记录
            $audit_apply_data = AuditApplyModel::findFirst([
                                                               'conditions' => '  biz_type = :biz_type: and  biz_value =  :biz_value: ',
                                                               'bind' => [
                                                                   'biz_type' => $biz_type,
                                                                   'biz_value' => $biz_value,
                                                               ]
                                                           ]);
            if(empty($audit_apply_data)){
                throw new Exception('没有找到申请数据! ==>conditions => '.json_encode($conditions,JSON_UNESCAPED_UNICODE).' data=>'.json_encode($save_data,JSON_UNESCAPED_UNICODE));
            }
            $log_summary = $audit_apply_data->summary;

            //修改申请数据
            $audit_apply_res = $this->getDI()->get('db')->updateAsDict(
                'audit_apply',
                $save_data,
                $conditions
            );

            //修改审批数据
            $audit_approval_res  = $this->getDI()->get('db')->updateAsDict(
                'audit_approval',
                $save_data,
                $conditions
            );
            //记录修改数据
            $this->getDI()->get('logger')->write_log('approval setAuditSummary :conditions => '.json_encode($conditions,JSON_UNESCAPED_UNICODE).' data=>'.json_encode($save_data,JSON_UNESCAPED_UNICODE).' audit_apply => '.$audit_apply_res.' audit_approval_res =>'.$audit_approval_res .' log_summary => '.$log_summary , 'info');

        }catch (Exception $e) {
            $this->getDI()->get('logger')->write_log('approval setAuditSummary :'  . $e->getFile() . ' line ' . $e->getLine() . ' msg ' . $e->getMessage() . $e->getTraceAsString(), 'error');
        }
        return true;
    }

    /**
     * @param $auditId
     * @param $bizType
     * @param $forUpdate
     * @return \Phalcon\Mvc\Model
     */
    public function getApply($auditId,$bizType, $forUpdate = false)
    {
        $param = [
            'conditions' => "biz_value = :value: and biz_type = :type:",
            'bind' => [
                'type'  => $bizType,
                'value' => $auditId,
            ],
        ];
        if ($forUpdate){
            $param = array_merge($param,['for_update' => true,]);
        }
        return AuditApplyModel::findFirst($param);
    }

    /**
     * @description 获取部分对接可视化的审批的审批流ID (部分对接的在workflow表会有2条数据)
     * @param $audit_type
     * @param int $view_state
     * @return int
     */
    public function getWorkflowId($audit_type, int $view_state = WorkflowModel::WORKFLOW_MANUAL): int
    {
        //非对接可视化的审批类型直接返回
        if (empty($audit_type) || !in_array($audit_type, AuditListEnums::getPartialVisualizeApprovalType())) {
            return 0;
        }

        $workflowInfo = WorkflowModel::find([
            'conditions' => 'relate_type = :relate_type:',
            'bind'       => [
                'relate_type' => $audit_type,
            ],
            'columns'    => 'id,is_view',
        ])->toArray();
        $workflowInfo = array_column($workflowInfo, 'id', 'is_view');

        if (empty($workflowInfo)) {
            return 0;
        }
        return $view_state == WorkflowModel::WORKFLOW_VISUALIZE ? $workflowInfo[WorkflowModel::WORKFLOW_VISUALIZE] : $workflowInfo[WorkflowModel::WORKFLOW_MANUAL];
    }

    /**
     * @param $summary
     * @return false|string
     */
    public function genNewSummary($summary)
    {
        $summary = json_decode($summary, true);
        foreach ($summary as $k=>$v){
            if ($v['key'] == 'created_at'){
                $summary[$k]['value'] = gmdate("Y-m-d H:i:s");
            }
        }
        return json_encode($summary, JSON_UNESCAPED_UNICODE);
    }

    /**
     * @description 当前审批人是否存在可编辑字段
     * @param $audit_id
     * @param $audit_type
     * @return bool
     */
    public function isExistCanEditField($audit_id, $audit_type): bool
    {
        $nodeInfo = self::getCurrentNodeByAuditId($audit_id, $audit_type);
        if (empty($nodeInfo) || empty($nodeInfo['can_edit_field'])) {
            return false;
        }
        return true;
    }

    /**
     * 指定节点是否存在可编辑字段
     * @param $node_info
     * @return bool
     */
    public function isNodeExistCanEditField($node_info): bool
    {
        if (empty($node_info) || empty($node_info->getCanEditField())) {
            return false;
        }
        return true;
    }


    /**
     * @description 获取当前审批人的可编辑字段
     * @param $audit_id
     * @param $audit_type
     * @param array $filter_columns
     * @return array
     */
    private function getCanEditField($audit_id, $audit_type, array $filter_columns = []): array
    {
        $nodeInfo = $this->getCurrentNodeByAuditId($audit_id, $audit_type);
        $canEditField = !empty($nodeInfo['can_edit_field']) ? $nodeInfo['can_edit_field'] : '';

        if (empty($canEditField)) {
            return [];
        }
        $canEditFieldData = (array)json_decode($canEditField, true);
        if (empty($canEditFieldData)) {
            return [];
        }
        $canEditFieldColumns = $canEditFieldData['basic'];
        $canEditFieldColumns = array_values(array_diff($canEditFieldColumns, $filter_columns));

        return WorkflowFormModel::find([
            'conditions' => 'column in({column_item:array}) and flow_id = :audit_type:',
            'bind' => [
                'column_item' => $canEditFieldColumns,
                'audit_type'  => $audit_type,
            ],
        ])->toArray();
    }

    /**
     * @description 获取可编辑字段
     * @param $audit_id
     * @param $audit_type
     * @param int $response_type
     * @return array
     */
    public function getCanEditFieldColumns($audit_id, $audit_type, int $response_type = AuditDetailOperationsEnums::RESPONSE_STRUCTURE_COLUMN_ID): array
    {
        $canEditField = $this->getCanEditField($audit_id, $audit_type);

        if ($response_type == AuditDetailOperationsEnums::RESPONSE_STRUCTURE_COLUMN_ID) {
            return array_column($canEditField, 'relate_button_id');
        } else {
            return array_column($canEditField, 'column');
        }
    }

    /**
     * @description 获取当前审批人的可编辑字段校验规则
     * @param $audit_id
     * @param $audit_type
     * @param array $filter_columns
     * @param array $customValidations
     * @return mixed
     */
    public function getCanEditFieldValidation($audit_id, $audit_type, array $filter_columns = [], array $customValidations = [])
    {
        if ($this->isExistCanEditField($audit_id, $audit_type)) {
            $formColumns = $this->getCanEditField($audit_id, $audit_type, $filter_columns);

            //format 校验规则
            $validation = [];
            foreach ($formColumns as $column) {
                if (isset($customValidations[$column['column']])) { //自定义规则，自己写！
                    $validation[$column['column']] = $customValidations[$column['column']];
                    continue;
                }
                if ($column['data_type'] == WorkflowFormModel::DATA_TYPE_CUSTOM) {
                    continue;
                }
                $validation[$column['column']] = sprintf('Required|%s|>>>: miss args %s', WorkflowFormModel::DATA_TYPE_MAP[$column['data_type']], $column['column']);
            }
        }
        return $validation ?? [];
    }

    /**
     * @description 根据audit_id获取当前审批进行到的审批节点
     * @param $audit_id
     * @param $audit_type
     * @return array
     */
    private function getCurrentNodeByAuditId($audit_id, $audit_type): array
    {
        $applyInfo = AuditApplyModel::findFirst([
            'conditions' => 'biz_type = :audit_type: and biz_value = :audit_value:',
            'bind' => [
                'audit_type' => $audit_type,
                'audit_value'=> $audit_id,
            ],
        ]);
        if (empty($applyInfo)) {
            return [];
        }
        $nodeInfo = WorkflowNodeModel::findFirst([
            'conditions' => 'id = :node_id:',
            'bind' => [
                'node_id'  => $applyInfo->getCurrentFlowNodeId()
            ]
        ]);
        return !empty($nodeInfo) ? $nodeInfo->toArray(): [];
    }

    /**
     * 创建延时审批，一阶段仅创建audit_apple表数据
     *
     * @param $params
     * @return array
     */
    private function createDelayStageOne($params)
    {
        $summary = $this->getSummaryDetail($params['audit_id'], $params['audit_type'], $params['user']);

        //使用文档链接: https://uuid.ramsey.dev/_/downloads/en/latest/pdf/
        if (class_exists('Ramsey\Uuid\Provider\Node\RandomNodeProvider')) {
            $nodeProvider = new RandomNodeProvider();
            $newFlowId    = Uuid::uuid1($nodeProvider->getNode(), mt_rand(1,16000))->toString();
        } else {
            $newFlowId    = $this->getRandomId();
        }

        //[6]创建请求
        $request = new AuditApplyModel();
        $request->setFlowId($newFlowId);
        $request->setBizType($params['audit_type']);
        $request->setBizValue($params['audit_id']);
        $request->setSerialNo($params['serial_no'] ?? '');
        $request->setState(Enums::APPROVAL_STATUS_PENDING);
        $request->setSubmitterId($params['user']);
        $request->setSummary(json_encode($summary, JSON_UNESCAPED_UNICODE));
        $request->setCurrentFlowNodeId(0);
        $request->setIsCancel(0);
        $request->setIsEdit(0);
        $request->setWorkflowId($params['workflow_id'] ?? 0);
        $request->setIsDeleted(0);
        $request->setDelayState(WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_PENDING);

        //TODO 超时时间怎么算？
        //$request->setTimeOut();
        $request->save();

        return $request->getId();
    }

    /**
     * @throws ValidationException
     * @throws InnerException
     */
    private function createDelayStageTwo($params)
    {
        //[1]检验传入参数
        $this->validateCheck($params, [
            'audit_id'   => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
            'audit_type' => 'Required|Int|>>>:' . $this->getTranslation()->_('7127'),
        ]);

        $audit_type  = $params['audit_type'];
        $audit_id    = $params['audit_id'];
        $user        = $params['user'];
        $workflow_id = $params['workflow_id'] ?? '';
        $extend      = $params['extend'] ?? [];

        //锁数据，防止并发
        $request = (new ApplyRepository())->getApplyObject($audit_type, $audit_id, true);
        if ($request->getState() != enums::APPROVAL_STATUS_PENDING) {
            $this->logger->write_log("createDelayStageTwo {$audit_id}_{$audit_type}_{$user} failed,audit state changed", 'info');
            return false;
        }

        //[2]处理审批流
        $workflowService = new WorkflowServer($this->lang, $this->timezone);
        $workflowPersistService = WorkflowPersistentServer::getInstance($this->lang, $this->timezone);

        //[2.2]使用审批流模板
        //[2.2.1]获取审批流模板
        $workflowModel = new WorkflowModel();
        $flowBase = $workflowModel->getWorkflowModel($audit_type, $workflow_id);
        if (empty($flowBase)) {
            $this->logger->write_log("workflow_create {$audit_id}_{$audit_type}_{$user} find null", 'info');
            //没有有效的审批流，请联系管理员
            throw new Exception('There is no effective approval process, please contact the administrator.',
                ErrCode::WORKFLOW_IS_NULL_ERROR);
        }
        $this->logger->write_log("workflow_create audit_type:{$audit_type},flowBase_id:{$flowBase->id},user:" . json_encode($user,
                JSON_UNESCAPED_UNICODE) . ',extend:' . json_encode($extend, JSON_UNESCAPED_UNICODE), 'info');

        //[[2.2.2]固化当前审批流 & 返回固化审批流ID
        //TODO: 暂时方案 ，后续调整为传入一个AuditRequest对象(对象中包含audit_id、audit_type、user等数据);本期调整变更较大
        $extend['audit_id']   = $audit_id;
        $extend['audit_type'] = $audit_type;
        $extend['is_recreate'] = true;
        $extend['flow_id'] = $request->getFlowId();
        $flowId = $workflowPersistService->persistentWorkflowV2((int)$flowBase->id, $user, $extend, $audit_type,
            $audit_id, $workflow_id);

        $this->logger->write_log(sprintf("workflow_create 固化 flow id {%s}", $flowId) , 'info');

        //[3]获取指定审批类型的超时配置
        if ($flowBase->is_view == WorkflowModel::WORKFLOW_MANUAL) {
            $timeOutDate = empty($extend['time_out']) ? null : $extend['time_out'];
        } else {
            $timeOutDate = WorkflowOvertimeServer::getInstance($this->lang,
                $this->timezone)->getLatestAuditOvertimeConfig($audit_id, $audit_type, $workflow_id);
        }

        //[4]获取审批流起始节点
        $startNode = $workflowService->getStartNode($flowId);
        if (empty($startNode)) {
            $this->logger->write_log("workflow_create start node {$startNode} find null" , 'info');
            throw new InnerException($this->getTranslation()->_('4008'), ErrCode::WORKFLOW_DATA_ERROR);
        }

        //[6]创建请求
        $request->setDelayState(WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_HAS_CREATED);
        $request->setCurrentFlowNodeId($startNode->id);
        $request->setWorkflowId($flowBase->id);
        if (!is_null($timeOutDate)) {
            $request->setTimeOut($timeOutDate);
        }
        //固化 base 审批流的版本号，用于回溯审批流相关配置
        if (!empty($flowBase->version)) {
            $request->setWorkflowBaseVersionId($flowBase->version);
        }
        $request->save();

        //[7]校验固化数据
        if (!$this->isWorkflowValid($audit_id, $audit_type, $flowId)) {
            $this->logger->write_log("workflow_create {$audit_id}_{$audit_type}_{$user} Valid failed" , 'info');
            throw new ValidationException($this->getTranslation()->_('err_msg_no_valid_approver'));
        }

        //[8]启动工作流
        //获取参与审批参数
        $parameters = $this->getWorkflowParams($audit_id, $audit_type, $user, null, true, $flowBase->id);
        $workflowService->process($request, $user , enums::WF_ACTION_CREATE, $parameters);

        return $request->getId();
    }


    /**
     * 修改操作 重新发起新审批
     * @throws ValidationException
     * @throws InnerException
     */
    private function createEditApproval($params)
    {
        $audit_id   = $params['audit_id'];
        $audit_type = $params['audit_type'];
        $user       = $params['user'];
        if (empty($audit_id) || empty($audit_type) || empty($user)) {
            return false;
        }

        $request = AuditApplyModel::findFirst([
            'conditions' => "biz_type = :type: and biz_value = :id:",
            'bind'       => ['type' => $audit_type, 'id' => $audit_id],
            "for_update" => true,
        ]);

        $workflowModel = new WorkflowModel();
        $flowBase      = $workflowModel->getWorkflowModel($audit_type);
        if (empty($flowBase)) { //无有效的审批流，请联系管理员
            $this->logger->write_log("workflow_create {$audit_id}_{$audit_type}_{$user} find null", 'info');
            throw new ValidationException('There is no effective approval process, please contact the administrator.',
                ErrCode::WORKFLOW_IS_NULL_ERROR);
        }
        //把原有审批流 delete & 重新固化当前审批流 & 返回固化审批流ID
        $node_sql   = "update workflow_node set deleted = 1 where flow_id = '{$request->flow_id}'";
        $relate_sql = "update workflow_node_relate set deleted = 1 where flow_id = '{$request->flow_id}'";
        $this->getDI()->get('db')->execute($node_sql);
        $this->getDI()->get('db')->execute($relate_sql);

        $workflowService           = new WorkflowServer($this->lang, $this->timezone);
        $workflowPersistentService = WorkflowPersistentServer::getInstance($this->lang, $this->timezone);
        $extend['is_recreate']     = true;
        $extend['flow_id']         = $request->flow_id;

        $flowId = $workflowPersistentService->persistentWorkflowV2((int)$flowBase->id, $user, $extend, $audit_type, $audit_id);
        $this->getDI()->get('logger')->write_log("workflow_edit_create 固化 flow id {$flowId}", 'info');

        //[4]校验固化数据
        if (!$this->isWorkflowValid($audit_id, $audit_type, $flowId)) {
            $this->getDI()->get('logger')->write_log("workflow_create {$audit_id}_{$audit_type}_{$user} Valid failed", 'info');
            throw new ValidationException($this->getTranslation()->_('err_msg_no_valid_approver'));
        }

        // 编辑审批产品要求与撤销审批保持一致，即不需要超时
//        if ($flowBase->is_view == WorkflowModel::WORKFLOW_MANUAL) {
//            $timeOutDate = empty($extend['time_out']) ? null : $extend['time_out'];
//        } else {
//            $timeOutDate = WorkflowOvertimeServer::getInstance($this->lang,
//                $this->timezone)->getLatestAuditOvertimeConfig($audit_id, $audit_type, $request->workflow_id);
//        }

        //获取审批流起始节点
        $startNode = $workflowService->getStartNode($flowId);
        if (empty($startNode)) {
            $this->getDI()->get('logger')->write_log("workflow_edit_create start node {$startNode} find null", 'info');
            throw new InnerException($this->getTranslation()->_('4008'), ErrCode::WORKFLOW_DATA_ERROR);
        }
        $request->setCurrentFlowNodeId($startNode->id);
        $request->setState(enums::APPROVAL_STATUS_PENDING);
        $request->setSummary($this->genNewSummary(json_encode($params['summary_data'], JSON_UNESCAPED_UNICODE)));
        $request->setCreatedAt(gmdate("Y-m-d H:i:s"));
//        if (!is_null($timeOutDate)) {
//            $request->setTimeOut($timeOutDate);
//        }
        if (!empty($flowBase->version)) {
            $request->setWorkflowBaseVersionId($flowBase->version);
        }
        $request->setIsEdit(1);
        $request->setIsCancel(0);
        $request->update();

        //启动工作流
        $workflowService->process($request, $user, enums::WF_ACTION_EDIT_RECREATE, $this->getWorkflowParams($audit_id, $audit_type, $user));
        return $request->getId();
    }

    /**
     * 通用审批
     * @param $params
     * @return array
     * @throws Exception
     */
    protected function auditApprovalReject($params): array
    {
        $auditId = $params['id'];
        $staffId = $params['operator_id'];
        $reason  = $params['reason'] ?? '';
        $status  = $params['status'];
        if (empty($auditId)) {
            throw new ValidationException('no valid audit_id');
        }
        if (empty($staffId)) {
            throw new ValidationException('no valid operator_id');
        }
        if (empty($status)) {
            throw new ValidationException('no valid status');
        }
        if ($status == enums::$audit_status['dismissed']) {
            $this->reject($auditId, $params['biz_type'], $reason, $staffId);
        } elseif ($status == enums::$audit_status['approved']) {
            $this->approval($auditId, $params['biz_type'], $staffId);
        }
        return $this->checkReturn(["code" => 1, "msg" => 'success','data'=> []]);
    }


    /**
     * 通用审批
     * @param $params
     * @return array
     * @throws Exception
     */
    public function commonAudit($params): array
    {
        switch ($params['biz_type']) {
            case AuditListEnums::APPROVAL_TYPE_TRANSFER_CAR:
                $result = $this->auditApprovalReject($params);
                break;
            default:
                $result = (new SystemExternalApprovalServer($this->lang, $this->timezone))->updateApprove($params);
                break;
        }
        return $result;
    }


}
