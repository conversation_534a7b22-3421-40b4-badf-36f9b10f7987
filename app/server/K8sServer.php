<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel as BySysDepartmentModel;
use FlashExpress\bi\App\Models\bi\SysDepartmentModel as BiSysDepartmentModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;


class K8sServer extends BaseServer
{

    public function checkServerOnline()
    {
        try {
            $this->checkRedis();
            $this->checkMysql();
        }catch (Exception $e){
            throw $e;
        }
    }

    private function checkRedis()
    {
        $cache = $this->getDI()->get('redisLib');
        $cache->set('by_k8s_test', 1, 60);
        if(!$cache->get('by_k8s_test')){
            throw new Exception('redisLib error');
        }
    }

    private function checkMysql()
    {
        //连接backyard数据库
        BySysDepartmentModel::findFirst(['columns'=>'id']);
        //连接bi数据库
        BiSysDepartmentModel::findFirst(['columns'=>'id']);
        //连接fle数据库
        FleSysDepartmentModel::findFirst(['columns'=>'id']);
    }
}