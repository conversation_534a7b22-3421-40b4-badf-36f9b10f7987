<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\AuditDetailOperationsEnums;
use FlashExpress\bi\App\Enums\WorkflowEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeRelateModel;

class AuditDetailOptionServer extends BaseServer
{
    const AUDIT_TAB_PENDING = 1; //待审批列表
    const AUDIT_TAB_FINISH = 2; //已完成列表

    const OPERATION_KEY_PREFIX = 'code';
    protected $auditDetailRequest;
    //审批终态
    protected $auditState;

    protected $isCancel;
    protected $isEdit;

    protected $currentStaffAuditState;
    //当前审批人是否为最终审批节点审批人
    protected $is_current_user_final_approval;
    //当前审批人是否为当前节点的前一个审批节点上的审批人
    protected $is_current_user_prefix_node;
    //审批流节点
    protected $node_list;
    //审批流节点之间关系
    protected $node_relate_list;

    //AuditBaseServer instance
    protected $instance;

    /**
     * @var AuditOptionRule
     */
    private $operationRule ;

    /**
     * @description 初始化
     * @param AuditDetailRequestServer $auditDetailObj
     * @param AuditOptionRule $rule
     * @param $instance
     * @return AuditDetailOptionServer
     */
    public function init(AuditDetailRequestServer $auditDetailObj, AuditOptionRule $rule, $instance): AuditDetailOptionServer
    {
        //加载配置
        $this->setAuditDetailRequest($auditDetailObj);
        $this->setOperationRule($rule);
        $this->setInstance($instance);
        $this->iniAuditInfo();
        return $this;
    }

    /**
     * @description 初始化审批数据
     * @return void
     */
    protected function iniAuditInfo()
    {
        $auditType = $this->getAuditDetailRequest()->getAuditType();
        $auditId   = $this->getAuditDetailRequest()->getAuditId();
        $user      = $this->getAuditDetailRequest()->getCurrentUser();

        $auditApplyInfo = AuditApplyModel::findFirst([
            "conditions" => "biz_type = :biz_type: and biz_value = :biz_value:",
            "bind" => [
                "biz_type"  => $auditType,
                "biz_value" => $auditId,
            ],
        ]);
        if (empty($auditApplyInfo)) { //没有数据按照已撤销返回
            $this->setAuditState(enums::APPROVAL_STATUS_CANCEL);
            return;
        }
        $this->setAuditState($auditApplyInfo->getState());
        $this->setIsCancel($auditApplyInfo->getIsCancel());
        $this->setIsEdit($auditApplyInfo->getIsEdit());
        if ($auditApplyInfo->getDelayState() == WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_PENDING) { //兼容PH延时创建审批
            return;
        }

        //非待审批、审批已完成状态 不需要判断当前审批人审批状态
        if (!in_array($auditApplyInfo->getState(), [enums::APPROVAL_STATUS_PENDING, enums::APPROVAL_STATUS_APPROVAL])) {
            $this->setIsCurrentUserPrefixNode(false);
            $this->setIsCurrentUserFinalApproval(false);
            return;
        }

        //获取全部节点
        $workflowNodeRelate = WorkflowNodeRelateModel::find([
            "conditions" => "flow_id = :flow_id: and deleted = 0",
            "bind" => [
                "flow_id" => $auditApplyInfo->getFlowId()
            ],
            "columns" => "from_node_id,to_node_id"
        ])->toArray();
        $workflowNodeRelateList = array_column($workflowNodeRelate, 'from_node_id', 'to_node_id');
        $this->setNodeRelateList($workflowNodeRelateList);

        $workflowNode = WorkflowNodeModel::find([
            "conditions" => "flow_id = :flow_id: and deleted = 0",
            "bind" => [
                "flow_id" => $auditApplyInfo->getFlowId(),
            ],
            "columns" => "id,type"
        ])->toArray();
        $workflowNodeList = array_column($workflowNode, 'type', 'id');
        $this->setNodeList($workflowNodeList);

        //当前节点前一个节点
        $prefixNodeId = $this->findPrefixNodeId($auditApplyInfo->getCurrentFlowNodeId());
        if (!empty($prefixNodeId)) {
            //前一个审批节点的审批人是否有当前登陆人
            $auditInfo = $this->getApprovalStateByNodeId($prefixNodeId, $user);
            if ($auditInfo) {
                $this->setIsCurrentUserPrefixNode(true);
            } else {
                $this->setIsCurrentUserPrefixNode(false);
            }
        } else {
            $this->setIsCurrentUserPrefixNode(false);
        }
        //获取当前登陆人是不是当前审批节点的待审批人
        $currentStaffApprovalInfo = AuditApprovalModel::findFirst([
            "conditions" => "biz_type = :biz_type: and biz_value = :biz_value: and approval_id = :approval_id: and flow_node_id = :node_id: and state = :state: and deleted = 0",
            "bind"       => [
                "biz_type"    => $auditType,
                "biz_value"   => $auditId,
                "approval_id" => $user,
                "node_id"     => $auditApplyInfo->getCurrentFlowNodeId(),
                "state"       => enums::APPROVAL_STATUS_PENDING,
            ],
            "columns"    => "approval_id",
        ]);
        if (!empty($currentStaffApprovalInfo)) {
            $this->setCurrentStaffAuditState(true);
        } else {
            $this->setCurrentStaffAuditState(false);
        }

        //获取最后一个审批节点及审批人
        //获取终节点ID
        $finalNode = WorkflowNodeModel::findFirst([
            "conditions" => "flow_id = :flow_id: and type = 99 and deleted = 0",
            "bind" => [
                "flow_id" => $auditApplyInfo->getFlowId()
            ],
        ]);
        if (empty($finalNode)) {
            $this->setIsCurrentUserFinalApproval(false);
        } else {
            $prefixFinalNodeId = $this->findPrefixNodeId($finalNode->getId());
            if (empty($prefixFinalNodeId)) {
                $this->setIsCurrentUserFinalApproval(false);
            }
            $finalAuditInfo = $this->getApprovalStateByNodeId($prefixFinalNodeId, $user);
            if ($finalAuditInfo) {
                $this->setIsCurrentUserFinalApproval(true);
            } else {
                $this->setIsCurrentUserFinalApproval(false);
            }
        }
    }

    /**
     * @description 获取指定节点的前一个审批节点
     * @param $current_node_id
     * @return int|mixed
     */
    protected function findPrefixNodeId($current_node_id)
    {
        $nodeList = $this->getNodeList();
        $nodeRelateList = $this->getNodeRelateList();

        $count = count($nodeRelateList);

        $currentNode = $current_node_id;
        $prefixNode = 0;
        while ($count > 0) {
            //前一个节点为$nodeRelateList[$currentNode]
            $prefixNode = $nodeRelateList[$currentNode];

            //echo sprintf("current node id %d", $currentNode), PHP_EOL;
            if (!isset($nodeRelateList[$prefixNode]) || !isset($nodeList[$prefixNode])) {
                break;
            }

            //有效的审批节点
            if (in_array($nodeList[$prefixNode], [enums::NODE_APPROVER,enums::NODE_COUNTERSIGN])) {
                break;
            }
            $currentNode = $prefixNode;
            $count--;
        }

        return $prefixNode;
    }

    /**
     * @description 获取当前登陆人在指定节点是否为审批人，并且已经审批同意
     * @param $node_id
     * @param $user
     * @return mixed
     */
    protected function getApprovalStateByNodeId($node_id, $user)
    {
        return AuditApprovalModel::findFirst([
            "conditions" => "flow_node_id = :node_id: and approval_id = :approval_id: and state = 2 and deleted = 0",
            "bind" => [
                "node_id"     => $node_id,
                "approval_id" => $user,
            ]
        ]);
    }

    /**
     * @description 获取当前用户操作
     * @return mixed
     */
    public function getDetailOptions()
    {
        $auditShowType  = $this->getAuditDetailRequest()->getAuditShowType();
        $auditStateType = $this->getAuditDetailRequest()->getAuditStateType();

        //当前用户是否已经审批
        switch ($auditShowType . $auditStateType) {
            case '11': //申请人-待审批
            case '12': //申请人-已完成
                $operations = $this->getSubmitterOperations($this->getAuditDetailRequest());
                break;
            case '21': //审批人-待审批
            case '22': //审批人-已完成
                $operations = $this->getApprovalOperations($this->getAuditDetailRequest());
                break;
            default:
                $operations = [];
                break;
        }
        if (empty($operations)) {
            $operations = (object)null;
        }
        return $operations;
    }

    /**
     * @description 获取申请人操作
     * @param AuditDetailRequestServer $auditDetailObj
     * @return array
     */
    protected function getSubmitterOperations(AuditDetailRequestServer $auditDetailObj): array
    {
        //notice: 这里不能用在哪儿Tab去判断按钮，如果在待审批Tab停留并且审批状态变更
        //的情况下，会出现按钮无法刷新的情况
        //如果业务存在 修改审批 这里面判断 针对 情况【先申请撤销被驳回，再申请修改通过】判断不准确

        if ($this->getIsCancel() == 0){
            if ($this->getAuditState() == enums::APPROVAL_STATUS_PENDING && $this->operationRule->getIsApplierAllowedCancelInApprovalProcess()
                && $this->getInstance()->applicantCustomiseOptionsInApprovalProcess($auditDetailObj->getAuditId())
            ) {
                return $this->response(AuditDetailOperationsEnums::BUTTON_CANCEL);
            }
            if ($this->getAuditState() == enums::APPROVAL_STATUS_APPROVAL && $this->operationRule->getIsApplierAllowedCancelAfterApprovalComplete()
            && $this->getInstance()->applicantCustomiseOptions($auditDetailObj->getAuditId())
            ){
                return $this->response(AuditDetailOperationsEnums::BUTTON_CANCEL);
            }
        }
        if ($this->getIsCancel() > 0){
            if ($this->getAuditState() == enums::APPROVAL_STATUS_PENDING && $this->operationRule->getIsApplierAllowedCancelInRevokeProcess()){
                return $this->response(AuditDetailOperationsEnums::BUTTON_CANCEL);
            }
            if ((in_array($this->getAuditState(), [enums::APPROVAL_STATUS_APPROVAL, enums::APPROVAL_STATUS_REJECTED, enums::APPROVAL_STATUS_TIMEOUT]))
                && $this->operationRule->getIsApplierAllowedCancelAfterRevokeComplete()){
                return $this->response(AuditDetailOperationsEnums::BUTTON_CANCEL);
            }
        }

        return $this->response(AuditDetailOperationsEnums::BUTTON_NULL);

    }

    /**
     * @description 获取审批人操作
     * @param AuditDetailRequestServer $auditDetailObj
     * @return array
     */
    protected function getApprovalOperations(AuditDetailRequestServer $auditDetailObj): array
    {
        $auditStateType = $auditDetailObj->getAuditStateType();
        if ($this->getAuditState() == enums::APPROVAL_STATUS_APPROVAL) { //最终审批已经终态，并且审批同意
            //审批完成后，是否允许撤销， 当前的操作人为最终审批人
            if ($this->operationRule->getIsApproverAllowedCancelAfterApprovalComplete() && $this->getIsCurrentUserFinalApproval()) {
                return $this->response(AuditDetailOperationsEnums::BUTTON_CANCEL);
            } else {
                return $this->response(AuditDetailOperationsEnums::BUTTON_NULL);
            }
        } elseif ($this->getAuditState() == enums::APPROVAL_STATUS_PENDING) { //目前审批还是待审批

            if ($auditStateType == self::AUDIT_TAB_PENDING && $this->getCurrentStaffAuditState()) { //登陆人是当前节点的审批人，审批状态为待审批

                //审批人审批时,自定义按钮
                $customiseOptions = $this->getInstance()->customiseOptions($auditDetailObj->getAuditId());
                if (!empty($customiseOptions)) {
                    return $this->response($customiseOptions);
                } else {
                    return $this->response(AuditDetailOperationsEnums::BUTTON_COMMON_APPROVAL);
                }
            } else {
                //在审批进行中时，是否允许撤销、当前登陆人为当前审批节点前一个审批节点的审批人
                if ($this->operationRule->getIsApproverAllowedCancelInApprovalProcess() && $this->getIsCurrentUserPrefixNode()) {
                    return $this->response(AuditDetailOperationsEnums::BUTTON_CANCEL);
                } else {
                    return $this->response(AuditDetailOperationsEnums::BUTTON_NULL);
                }
            }

        } else { //最终审批已经终态，驳回、撤销、超时
            return $this->response(AuditDetailOperationsEnums::BUTTON_NULL);
        }
    }

    /**
     * @description 获取返回数据结构
     * @param $operation
     * @return array
     */
    protected function response($operation): array
    {
        if (empty($operation)) {
            return [];
        }
        $response = [];
        if (is_array($operation)) {
            foreach ($operation as $item) {
                $key            = sprintf("%s%d", self::OPERATION_KEY_PREFIX, $item);
                $response[$key] = $item;
            }
        } else {
            $key      = sprintf("%s%d", self::OPERATION_KEY_PREFIX, $operation);
            $response = [$key => $operation];
        }
        return $response;
    }


    /**
     * @return mixed
     */
    public function getAuditDetailRequest(): AuditDetailRequestServer
    {
        return $this->auditDetailRequest;
    }

    /**
     * @param mixed $auditDetailRequest
     */
    public function setAuditDetailRequest($auditDetailRequest): void
    {
        $this->auditDetailRequest = $auditDetailRequest;
    }


    /**
     * @return mixed
     */
    public function getAuditState()
    {
        return $this->auditState;
    }

    /**
     * @param mixed $auditState
     */
    public function setAuditState($auditState): void
    {
        $this->auditState = $auditState;
    }

    /**
     * @return mixed
     */
    public function getIsCancel()
    {
        return $this->isCancel;
    }

    /**
     * @param mixed $isCancel
     */
    public function setIsCancel($isCancel): void
    {
        $this->isCancel = $isCancel;
    }

    /**
     * @return mixed
     */
    public function getIsEdit()
    {
        return $this->isEdit;
    }

    /**
     * @param mixed $isEdit
     */
    public function setIsEdit($isEdit): void
    {
        $this->isEdit = $isEdit;
    }

    /**
     * @return mixed
     */
    public function getIsCurrentUserFinalApproval()
    {
        return $this->is_current_user_final_approval;
    }

    /**
     * @param mixed $is_current_user_final_approval
     */
    public function setIsCurrentUserFinalApproval($is_current_user_final_approval): void
    {
        $this->is_current_user_final_approval = $is_current_user_final_approval;
    }

    /**
     * @return mixed
     */
    public function getIsCurrentUserPrefixNode()
    {
        return $this->is_current_user_prefix_node;
    }

    /**
     * @param mixed $is_current_user_prefix_node
     */
    public function setIsCurrentUserPrefixNode($is_current_user_prefix_node): void
    {
        $this->is_current_user_prefix_node = $is_current_user_prefix_node;
    }

    /**
     * @return mixed
     */
    public function getNodeList()
    {
        return $this->node_list;
    }

    /**
     * @param mixed $node_list
     */
    public function setNodeList($node_list): void
    {
        $this->node_list = $node_list;
    }

    /**
     * @return mixed
     */
    public function getNodeRelateList()
    {
        return $this->node_relate_list;
    }

    /**
     * @param mixed $node_relate_list
     */
    public function setNodeRelateList($node_relate_list): void
    {
        $this->node_relate_list = $node_relate_list;
    }

    public function setOperationRule($rule)
    {
        $this->operationRule = $rule;
    }

    /**
     * @return mixed
     */
    public function getCurrentStaffAuditState()
    {
        return $this->currentStaffAuditState;
    }

    /**
     * @param mixed $currentStaffAuditState
     */
    public function setCurrentStaffAuditState($currentStaffAuditState): void
    {
        $this->currentStaffAuditState = $currentStaffAuditState;
    }

    /**
     * @return mixed
     */
    public function getInstance()
    {
        return $this->instance;
    }

    /**
     * @param mixed $instance
     */
    public function setInstance($instance): void
    {
        $this->instance = $instance;
    }
}