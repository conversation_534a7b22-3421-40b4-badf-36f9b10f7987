<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\backyard\MessageIdentityDetailModel;

class MessageIdentityDetailServer extends BaseServer
{
    /**
     * @param $params
     * @param $colum
     * @return false
     *  EXPLAIN SELECT `m`.`staff_info_attribute` AS `staff_info_attribute`, `m`.`id` AS `id` FROM
     * `message_identity_detail` AS `m` WHERE (((1 = 1) AND (`m`.`staff_info_id` = '19515')) AND
     * (`m`.`remote_message_id` = '1667997277828728099')) AND (`m`.`is_del` = 0);
     *
     * EXPLAIN SELECT `m`.`staff_info_id` AS `staff_info_id` FROM `message_identity_detail` AS `m` WHERE (((1 = 1) AND
     * (`m`.`oss_file_id` = 6)) AND (`m`.`send_staff_id` = 10000)) AND (`m`.`is_del` IN (0,1));
     *
     */
    public function getIdentityDetailByParams($params, $colum = [])
    {
        if (!is_array($params) || empty($params)) {
            return false;
        }
        $fields  = (!is_array($colum) || empty($colum)) ? "*" : implode(',', $colum);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($fields);
        $builder->from(['m' => MessageIdentityDetailModel::class]);
        //$builder->where('1 = 1');
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere($field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere($field." IN ({{$field}:array})", [$field => $val]);
            }
        }
        return $builder->getQuery()->getSingleResult();
        //return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 组装模板发送员工消息体
     * @param $staffId
     * @param $remoteMessageId
     * @param $originContent
     * @return array|string|string[]|null
     */
    public function makeTplSendMsgContent($staffId, $remoteMessageId, $originContent)
    {
        // 查询附件明细表数据
        $identityData = (new MessageIdentityDetailServer())->getIdentityDetailByParams([
            "staff_info_id"     => $staffId,
            "remote_message_id" => $remoteMessageId,
            "is_del"            => 0,
        ], ["staff_info_attribute", "id"]);
        if (empty($identityData) || !is_object($identityData)) {
            return $originContent;
        }

        $staffInfoAttributes = json_decode($identityData->staff_info_attribute, true);
        preg_match_all("/\{\{\s*[A-Z]\s*\}\}/", $originContent, $matches);
        $pattern = $matches[0];
        array_walk($pattern, function (&$item, $key) {
            $item = '/' . trim($item) . '/';
        });
        // 获取到模板的 变量 并 去掉 /{{}}/
        $replaceData = [];
        foreach ($pattern as $key => $val) {
            $seat = trim($val, '/{{}}/');
            // 按照模板变量的顺序 进行重序
            $replaceData[] = $staffInfoAttributes[$seat];
        }
        // 替换后重新赋值
        return preg_replace($pattern, $replaceData, $originContent);
    }
}