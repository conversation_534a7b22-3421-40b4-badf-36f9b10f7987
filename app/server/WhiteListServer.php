<?php
/**
 * Author: Bruce
 * Date  : 2023-01-09 21:28
 * Description:
 */

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\Models\backyard\AttendanceWhiteListModel;

class WhiteListServer extends BaseServer
{
    /**
     * 考勤白名单
     * @return array|string
     */
    public function attendanceList($params = [])
    {
        //获取打卡白名单
        $hcm_rpc = new ApiClient('hcm_rpc', '', 'getAttendanceWhiteListData');
        $hcm_rpc->setParams($params);
        return $hcm_rpc->execute();
    }

    /**
     * 单人 考勤白名单
     * @param $staffInfoId
     * @return array
     */
    public function staffValidDateRange($staffInfoId): array
    {
        return AttendanceWhiteListModel::find([
            'columns'    => 'start_at,end_at',
            'conditions' => 'staff_info_id = :id:',
            'bind'       => ['id' => $staffInfoId],
        ])->toArray();
    }

    /**
     * $date 是否在白名单
     * @param $DateMap
     * @param $date
     * @return bool
     */
    public function isInclude($DateMap, $date): bool
    {
        $isInclude = false;
        foreach ($DateMap as $item) {
            if ($date >= $item['start_at'] && $date < $item['end_at']) {
                $isInclude = true;
                break;
            }
        }
        return $isInclude;
    }

    /**
     * 白名单天数
     * @param $staffInfoId
     * @param $statDates
     * @param $attendanceWhiteList
     * @return array
     */
    public function whiteListDays($staffInfoId, $statDates, $attendanceWhiteList): array
    {
        $whiteDays = [];
        foreach ($statDates as $date) {
            if (in_array($staffInfoId,
                    $attendanceWhiteList[$date]['type_paid_locally'])
                ||
                in_array($staffInfoId,
                    $attendanceWhiteList[$date]['type_not_paid_locally'])) {
                $whiteDays[] = $date;
            }
        }
        return $whiteDays;
    }

    //获取考勤薪酬白名单
    public function isInfWhiteList($staffId, $date, $type = []): bool
    {
        $conditions = 'staff_info_id = :staff_id: and start_at <= :date: and end_at > :date:';
        $bind       = [
            'staff_id' => $staffId,
            'date'     => $date,
        ];
        if (!empty($type)) {
            $conditions   .= ' and type in ({type:array})';
            $bind['type'] = $type;
        }
        $info = AttendanceWhiteListModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        return !empty($info);
    }
}