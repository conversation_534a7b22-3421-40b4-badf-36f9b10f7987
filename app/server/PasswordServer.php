<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\HttpCurl;
use FlashExpress\bi\App\library\PasswordHash;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;

class PasswordServer extends BaseServer
{

    const CAPTCHA_HOST = 'captcha.tencentcloudapi.com';
    const CAPTCHA_SERVICE = 'captcha';
    const CAPTCHA_ACTION = 'DescribeCaptchaResult';
    const CAPTCHA_VERSION = '2019-07-22';
    const CAPTCHA_CAPTCHA_TYPE = 9;


    const BIZ_TYPE_FIND_PASSWORD_TICKET = 'FP-%s-';
    const BIZ_TYPE_EDIT_PASSWORD_TICKET = 'EP-%s-';
    const BIZ_TYPE_FACE_COMPARE_TICKET = 'FC-%s-';
    const BIZ_TYPE_CHECK_CAPTCHA_TICKET = 'CC-%s-';

    public function makeTicket($bitType, $staffId, $expire = 300): string
    {
        $key   = sprintf($bitType, $staffId) . uuid();
        $cache = $this->getDI()->get('redisLib');
        $cache->set($key, $staffId, $expire);
        return $key;
    }

    public function deleteTicket($ticket)
    {
        return $this->getDI()->get('redisLib')->del($ticket);
    }



    /**
     * @param $facial_compare_ticket
     * @param $face_image
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function facialCompare($facial_compare_ticket, $face_image): array
    {
        $cache   = $this->getDI()->get('redisLib');
        $staffId = $cache->get($facial_compare_ticket);
        if (empty($staffId)) {
            throw new BusinessException($this->getTranslation()->_('find_password_error_msg_11'),ErrCode::PASSWORD_FACE_COMPARE_TICKET_TIMEOUT_ERROR);
        }

        $pwd_src = substr($facial_compare_ticket, 0, 2);


        $flag = 0;                                                 //是否拼接内网图片地址
        if (RUNTIME == 'pro') {
            $flag = 1;
        }
        $server     = new AttendanceServer($this->lang, $this->timeZone);
        $source_url = $server->get_face_img($staffId, $flag);
        if (empty($source_url)) {
            throw new BusinessException('底片');
        }

        //拼接图片 全路径
        $format['bucket'] = $this->config->application->oss_bucket;//固定
        $format['path']   = $face_image;
        $face_image       = $server->format_oss($format, $flag);
        $faceCompareSever = new FaceCompareServer($this->lang);
        $faceCompareSever->fire($staffId, $source_url, $face_image,FaceCompareServer::BIZ_TYPE_EDIT_PASSWORD);
        $ticket = $this->makeTicket(self::BIZ_TYPE_FACE_COMPARE_TICKET, $staffId);
        $this->deleteTicket($facial_compare_ticket);
        return ['redirect_url' => urlencode(env('h5_endpoint') . 'update-password?edit_password_ticket=' . $ticket.'&pwd_src='.$pwd_src)];
    }


    /**
     * 设置=>修改登录密码临时凭证
     * @param $staffId
     * @return array
     * @throws BusinessException
     */
    public function editPasswordGetTicket($staffId): array
    {
        $t         = $this->getTranslation();
        $staffInfo = (new StaffServer())->getStaffById($staffId);
        if(empty($staffInfo) || $staffInfo['is_sub_staff'] == 1 || in_array($staffInfo['formal'],
                [HrStaffInfoModel::FORMAL_FRANCHISEE, HrStaffInfoModel::FORMAL_FRANCHISEE_OTHER])){
            throw new BusinessException($t->_('find_password_error_msg_03'));
        }

        $data['facial_compare_ticket']  = $this->makeTicket(self::BIZ_TYPE_EDIT_PASSWORD_TICKET, $staffId);
        $data['is_have_face_negatives'] = !empty((new AttendanceRepository($this->lang,
            $this->timeZone))->get_attendance_photo($staffId));
        return $data;
    }

    /**
     * 底片上传
     * @param $facial_compare_ticket
     * @return mixed
     * @throws \ReflectionException
     * @throws BusinessException
     */
    public function faceFormatUrlFle($facial_compare_ticket)
    {
        $cache   = $this->getDI()->get('redisLib');
        $staffId = $cache->get($facial_compare_ticket);
        //操作超时，请返回重新操作
        if (empty($staffId)) {
            throw new BusinessException($this->getTranslation()->_('find_password_error_msg_11'),ErrCode::PASSWORD_FACE_COMPARE_TICKET_TIMEOUT_ERROR);
        }
        $server = Tools::reBuildCountryInstance(new AttendanceServer($this->lang, $this->timeZone),
            [$this->lang, $this->timeZone]);
        return $server->faceFormatUrlFle($staffId, $this->request->get('fileName'));
    }


    /**
     * 忘记密码临时凭证
     * @param $staffId
     * @param $mobile
     * @return array
     * @throws BusinessException
     */
    public function findPasswordGetTicket($staffId, $mobile): array
    {
        $this->validationInputData($staffId, $mobile);
        $data['facial_compare_ticket']  = $this->makeTicket(self::BIZ_TYPE_FIND_PASSWORD_TICKET, $staffId);
        $data['is_have_face_negatives'] = !empty((new AttendanceRepository($this->lang,
            $this->timeZone))->get_attendance_photo($staffId));
        return $data;
    }

    /**
     * @param $staffId
     * @param $mobile
     * @return void
     * @throws BusinessException
     */
    protected function validationInputData($staffId, $mobile)
    {
        $t         = $this->getTranslation();
        if (!is_staff_info_id($staffId)) {
            throw new BusinessException($t->_('not_staff_info_id'));
        }
        
        $staffInfo = (new StaffServer())->getStaffInfo(['staff_info_id' => $staffId]);
        if (empty($staffInfo)) {
            throw new BusinessException($t->_('employee_no_not_exist'));
        }
        if (empty($staffInfo['mobile']) || $staffInfo['mobile'] != $mobile) {
            throw new BusinessException($t->_('find_password_error_msg_01'));
        }

        if ($staffInfo['state'] != HrStaffInfoModel::STATE_ON_JOB) {
            throw new BusinessException($t->_('find_password_error_msg_02'));
        }
        //仅支持正式工号
        if ($staffInfo['is_sub_staff'] == 1 || in_array($staffInfo['formal'],
                [HrStaffInfoModel::FORMAL_FRANCHISEE, HrStaffInfoModel::FORMAL_FRANCHISEE_OTHER])) {
            throw new BusinessException($t->_('find_password_error_msg_03'));
        }
    }


    /**
     * @param $params
     * @return true
     * @throws ValidationException
     */
    protected function inputPasswordValidation($staffId, $params): bool
    {
        $t = $this->getTranslation();
        if (!is_numeric($params['password']) || !is_numeric($params['confirm_password'])) {
            throw new ValidationException($t->_('find_password_error_msg_05'));
        }
        if ($params['password'] !== $params['confirm_password']) {
            throw new ValidationException($t->_('find_password_error_msg_06'));
        }

        //不能是  123456，654321，222222  这三种case
        $box = [];
        for ($i = 1; $i < strlen($params['password']); $i++) {
            $box[] = $params['password'][$i] - $params['password'][$i - 1];
        }
        $box = array_unique($box);
        if (count($box) == 1 && array_intersect($box, [0, 1, -1])) {
            throw new ValidationException($t->_('find_password_error_msg_07'));
        }

        //输入密码不能与原密码相同
        if ($this->checkIsSameOldPassword($staffId, $params['password'])) {
            throw new ValidationException($t->_('find_password_error_msg_09'));
        }

        //新密码不能和工号相似
        if (strrpos(strval($staffId), strval($params['password'])) !== false) {
            throw new ValidationException($t->_('find_password_error_msg_12'));
        }

        return true;
    }

    /**
     * @param $staff_id
     * @return bool|mixed
     * @throws ValidationException
     */
    protected function checkIsNewDevice($staff_id)
    {
        $headerData = $this->request->getHeaders();
        $device_id  = $this->processingDefault($headerData, 'X-Device-Id');

        if (env('break_away_from_ms')) {
            $accountInfo = StaffAccountModel::findFirst([
                'conditions' => "staff_info_id = :staff_id: and clientid = :clientid:",
                'bind'       => [
                    'staff_id' => $staff_id,
                    'clientid' => $device_id,
                ],
            ]);
            return empty($accountInfo);
        }
        $params = ['staff_id' => $staff_id, 'device_id' => $device_id];
        $api    = new RestClient('nws');
        $res    = $api->execute(RestClient::METHOD_POST, '/svc/staff/valid_new_device',
            $params, ['Accept-Language' => $this->lang]);

        if (!isset($res['code']) || $res['code'] != 1) {
            throw new ValidationException($this->getTranslation()->_('server_error'));
        }
        return $res['data'];
    }

    /**
     * 验证密码是否与旧密码相同
     * @param $staff_id
     * @param $new_password
     * @return bool|mixed
     * @throws ValidationException
     */
    protected function checkIsSameOldPassword($staff_id, $new_password)
    {
        if (env('break_away_from_ms')) {
            $staffInfo = StaffInfoModel::findFirst([
                'conditions' => "id = :staff_id:",
                'bind'       => [
                    'staff_id' => $staff_id,
                ],
            ]);
            if (empty($staffInfo)) {
                throw new ValidationException($this->getTranslation()->_('data_error'));
            }
            $hash = new PasswordHash(10, false);
            return $hash->checkPassword($new_password, $staffInfo->encrypted_password);
        }
        $params = ['staff_id' => $staff_id, 'new_password' => $new_password];
        $api    = new RestClient('nws');
        $res    = $api->execute(RestClient::METHOD_POST, '/svc/staff/valid_password',
            $params, ['Accept-Language' => $this->lang]);

        if (!isset($res['code']) || $res['code'] != 1) {
            throw new ValidationException($this->getTranslation()->_('server_error'));
        }
        return $res['data'];
    }

    /**
     * 验证图形验证码
     * @param $appid
     * @param $ticket
     * @param $randstr
     * @return mixed
     * @throws BusinessException
     */
    public function checkCaptcha($appid, $ticket, $randstr)
    {
        $captcha_secret_id  = env('captcha_secret_id', 'IKIDudCYhf6RYQ7dy63qVzEF5mI5g7MNEsPl');
        $captcha_secret_key = env('captcha_secret_key', 'DF8sZ5P1b1HMIAr25yfARYS7ZIuLcktu');
        $captcha_app_secret_key = env('captcha_app_secret_key', 'OuLdFioCuH4aka7PLY4J4I3wM');
        $timestamp = time();
        $data               = [
            'CaptchaType'  => self::CAPTCHA_CAPTCHA_TYPE,
            'Ticket'       => $ticket,
            'Randstr'      => $randstr,
            'UserIp'       => get_real_ip(),
            'CaptchaAppId' => intval($appid),
            'AppSecretKey' => $captcha_app_secret_key,
        ];


        $secretId  = $captcha_secret_id;
        $secretKey = $captcha_secret_key;
        // step 1: build canonical request string
        $canonicalQueryString = "";
        $canonicalHeaders     = implode("\n", [
            "content-type:application/json; charset=utf-8",
            "host:" . self::CAPTCHA_HOST,
            "x-tc-action:" . strtolower(self::CAPTCHA_ACTION),
            "",
        ]);
        $signedHeaders        = implode(";", [
            "content-type",
            "host",
            "x-tc-action",
        ]);
        $payload              = json_encode($data);
        $hashedRequestPayload = hash("SHA256", $payload);
        $canonicalRequest     = 'POST' . "\n"
            . '/' . "\n"
            . $canonicalQueryString . "\n"
            . $canonicalHeaders . "\n"
            . $signedHeaders . "\n"
            . $hashedRequestPayload;
        // step 2: build string to sign
        $date                   = gmdate("Y-m-d", $timestamp);
        $credentialScope        = $date . "/" . self::CAPTCHA_SERVICE . "/tc3_request";
        $hashedCanonicalRequest = hash("SHA256", $canonicalRequest);
        $stringToSign           = 'TC3-HMAC-SHA256' . "\n"
            . $timestamp . "\n"
            . $credentialScope . "\n"
            . $hashedCanonicalRequest;

        // step 3: sign string
        $secretDate    = hash_hmac("SHA256", $date, "TC3" . $secretKey, true);
        $secretService = hash_hmac("SHA256", self::CAPTCHA_SERVICE, $secretDate, true);
        $secretSigning = hash_hmac("SHA256", "tc3_request", $secretService, true);
        $signature     = hash_hmac("SHA256", $stringToSign, $secretSigning);

        // step 4: build authorization
        $authorization = "TC3-HMAC-SHA256"
            . " Credential=" . $secretId . "/" . $credentialScope
            . ", SignedHeaders=" . $signedHeaders . ", Signature=" . $signature;

        $header = [
            'Authorization: ' . $authorization,
            'Content-Type: application/json; charset=utf-8',
            'Host: ' . self::CAPTCHA_HOST,
            'X-TC-Action: ' . self::CAPTCHA_ACTION,
            'X-TC-Timestamp: ' . $timestamp,
            'X-TC-Version: ' . self::CAPTCHA_VERSION,
            'X-TC-Language: zh-CN',
        ];
        $result = HttpCurl::callInterface('https://' . self::CAPTCHA_HOST, json_encode($data), 'POST', $header);
        $this->logger->write_log(['checkCaptcha'=>$data,'timestamp'=>$timestamp,'result'=>$result], 'info');
        if (is_json($result)) {
            return json_decode($result, true);
        }
        throw new BusinessException($this->getTranslation()->_('find_password_error_msg_10'));
    }


    /**
     * @throws BusinessException
     * @throws ValidationException
     */
    protected function doEditPassword($staff_id, $new_password,$pwd_src): bool
    {
        if (env('break_away_from_ms')) {
            $client = new ApiClient('hcm_rpc', '', 'update_staff_password', $this->lang);
            $client->setParams(['staff_id' => $staff_id, 'new_password' => $new_password]);
            $result = $client->execute();
            if (isset($result['error'])) {
                throw new BusinessException($result['error']);
            }
            (new LoginServer())->unlock($staff_id);
        } else {
            $headerData     = $this->request->getHeaders();
            $flePlatform    = $this->processingDefault($headerData, 'Fle-Platform');//平台来源，区分 kit,by;
            $equipment_type = strtoupper($flePlatform);
            $params         = ['staff_id'                => $staff_id,
                               'new_password'            => $new_password,
                               'equipment_type_category' => enums::EQM_TYPE[$equipment_type] ?? 1,
            ];
            $api            = new RestClient('nws');
            $res            = $api->execute(RestClient::METHOD_POST, '/svc/staff/update_password',
                $params, ['Accept-Language' => $this->lang]);
            if (!isset($res['code']) || $res['code'] != 1) {
                throw new ValidationException($res['message']);
            }
        }

        $key = $pwd_src == 'FP' ? 'forget_password' : 'edit_password';

        $data   = [
            "operater"      => $staff_id,
            "staff_info_id" => $staff_id,
            "type"          => 'staff',
            "after"         => json_encode(['body' => [$key => 'Y']], JSON_UNESCAPED_UNICODE),
        ];
        $client = new ApiClient('hr_rpc', '', 'add_operate_logs');
        $client->setParams($data);
        $client->execute();

        (new LoginServer())->deleteStaffSessionCache($staff_id);

        return true;
    }


    /**
     * @param $params
     * @return array
     * @throws ValidationException|BusinessException
     */
    public function edit($params): array
    {
        $t = $this->getTranslation();

        $result  = [];
        $cache   = $this->getDI()->get('redisLib');
        $staffId = $cache->get($params['edit_password_ticket']);
        //操作超时，请返回重新操作
        if (empty($staffId)) {
            $result['state'] = 3;
            return $result;
        }

        //密码格式验证
        $this->inputPasswordValidation($staffId, $params);

        //图形验证码
        $configInfo = (new SettingEnvServer())->getMultiEnvByCode([
            'edit_password_change_otp',
            'edit_password_captcha',
        ]);
        //是否进行图形验证
        $is_check_captcha = !empty($configInfo['edit_password_captcha']);
        if ($is_check_captcha) {
            if ((empty($params['randstr']) || empty($params['appid']) || empty($params['ticket']))) {
                $result['state'] = 2;
                return $result;
            }
            if (empty($params['check_captcha_ticket'])) {
                $result = $this->checkCaptcha($params['appid'], $params['ticket'], $params['randstr']);
                if (isset($result['Response']) && $result['Response']['CaptchaCode'] != 1) {
                    $this->logger->write_log($result, 'notice');
                    throw new BusinessException($t->_('find_password_error_msg_10'));
                }
            } else {
                if (empty($cache->get($params['check_captcha_ticket']))) {
                    $result['state'] = 3;
                    return $result;
                }
            }
        }

        //opt验证 & 新设备
        if (!empty($configInfo['edit_password_change_otp']) && $this->checkIsNewDevice($staffId)) {
            if ((empty($params['mobile']) && empty($params['personal_email']))) {
                $result          = (new StaffServer())->getStaffInfo(['staff_info_id' => $staffId],
                    ['mobile', 'personal_email']);
                $result['check_captcha_ticket'] = $this->makeTicket( self::BIZ_TYPE_CHECK_CAPTCHA_TICKET,$staffId,600);
                $result['state'] = 4;
                return $result;
            }
            if (!isset($params['code'])) {
                throw new BusinessException($t->_('find_password_error_msg_08'));
            }
            if (!empty($params['mobile'])) {
                (new SmsServer($this->lang, $this->timeZone))->checkSmsCode(SmsServer::SMS_BIZ_TYPE_PASSWORD,
                    $params['mobile'], $params['code']);
            }
            if (!empty($params['personal_email'])) {
                (new EmailServer($this->lang, $this->timeZone))->checkCode(SmsServer::SMS_BIZ_TYPE_PASSWORD,
                    $params['personal_email'], $params['code']);
            }
        }
        if($this->doEditPassword($staffId,$params['password'],$params['pwd_src'])){
            $result['state'] = 1;
            return $result;
        }
        $this->deleteTicket($params['edit_password_ticket']);
        $result['state'] = 3;
        return $result;
    }


}