<?php
/**
 * Author: Bruce
 * Date  : 2024-12-19 16:50
 * Description:
 */

namespace FlashExpress\bi\App\Server;


use DateTime;
use Exception;
use FlashExpress\bi\App\Enums\AuditDetailOperationsEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SuspensionAuditModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SuspensionRepository;

class SuspensionServer extends AuditBaseServer
{
    public static $hireTypes = [
        HrStaffInfoModel::HIRE_TYPE_1,
        HrStaffInfoModel::HIRE_TYPE_2,
        HrStaffInfoModel::HIRE_TYPE_3,
        HrStaffInfoModel::HIRE_TYPE_4,
        HrStaffInfoModel::HIRE_TYPE_5,
    ];

    const HOLD_LIMIT = 14;//// 1号-14号：hold限制月可选择上月及以后的月份

    /**
     * 停职申请入口权限
     * @param $staffInfo
     * @return bool
     */
    public function isShowSuspensionAuditPermission($staffInfo)
    {

        $suspensionAuditConfig = $this->getAuditConfigInfo();

        $roleIds = [];
        if (!empty($suspensionAuditConfig['suspension_audit_department_role_id'])) {//【停职申请】可选择指定部门员工的角色id：角色:部门,部门|角色:部门,部门
            $department_role_list = explode('|', $suspensionAuditConfig['suspension_audit_department_role_id']);
            foreach ($department_role_list as $oneData) {
                if (empty($oneData)) {
                    continue;
                }
                $dataArr = explode(':', $oneData);
                if (empty($dataArr)) {
                    continue;
                }
                $roleIds[] = $dataArr[0];
            }
        }

        if (!empty($suspensionAuditConfig['suspension_audit_jurisdiction_role_id'])) {//【停职申请】可选择管辖范围员工的角色id
            $jurisdiction_role_list = explode(',', $suspensionAuditConfig['suspension_audit_jurisdiction_role_id']);
            if (!empty($jurisdiction_role_list)) {
                $roleIds = array_merge($roleIds, $jurisdiction_role_list);
            }
        }

        $job_title = [];
        if (!empty($suspensionAuditConfig['suspension_audit_jurisdiction_job_title_id'])) {//【停职申请】可选择管辖范围员工的职位id
            $jurisdiction_job_list = explode(',', $suspensionAuditConfig['suspension_audit_jurisdiction_job_title_id']);
            if (!empty($jurisdiction_job_list)) {
                $job_title = array_merge($job_title, $jurisdiction_job_list);
            }
        }

        if (!empty($suspensionAuditConfig['suspension_audit_subordinate_job_title_id'])) {//【停职申请】可选择下级员工的职位id
            $subordinate_job_list = explode(',', $suspensionAuditConfig['suspension_audit_subordinate_job_title_id']);
            if (!empty($subordinate_job_list)) {
                $job_title = array_merge($job_title, $subordinate_job_list);
            }
        }

        if (array_intersect($staffInfo['positions'], $roleIds)) {
            return true;
        }

        if (in_array($staffInfo['job_title'], $job_title)) {
            return true;
        }

        return false;
    }

    /**
     * 获取配置信息
     * @return array
     */
    public function getAuditConfigInfo()
    {
        $suspensionAudit = (new SettingEnvServer())->listByCode([
            'suspension_audit_department_role_id',//【停职申请】可选择指定部门员工的角色id：角色:部门,部门|角色:部门,部门
            'suspension_audit_jurisdiction_role_id',//【停职申请】可选择管辖范围员工的角色id
            'suspension_audit_jurisdiction_job_title_id',//【停职申请】可选择管辖范围员工的职位id
            'suspension_audit_subordinate_job_title_id',//【停职申请】可选择下级员工的职位id
        ]);

        return array_column($suspensionAudit, 'set_val', 'code');
    }

    /**
     * 校验数据权限
     * @param $params
     * @return bool
     */
    public function getPermission($params)
    {
        $suspensionAuditConfig = $this->getAuditConfigInfo();

        if (!empty($suspensionAuditConfig['suspension_audit_department_role_id'])) {//【停职申请】可选择指定部门员工的角色id：角色:部门,部门|角色:部门,部门
            $result = $this->get_audit_department_role_id($params, $suspensionAuditConfig['suspension_audit_department_role_id']);
            if($result) {
                return true;
            }
        }

        if (!empty($suspensionAuditConfig['suspension_audit_jurisdiction_role_id'])) {//【停职申请】可选择管辖范围员工的角色id
            $result = $this->get_audit_jurisdiction_role_id($params, $suspensionAuditConfig['suspension_audit_jurisdiction_role_id']);
            if($result) {
                return true;
            }
        }

        if (!empty($suspensionAuditConfig['suspension_audit_jurisdiction_job_title_id'])) {//【停职申请】可选择管辖范围员工的职位id
            $result = $this->get_audit_jurisdiction_job_title_id($params, $suspensionAuditConfig['suspension_audit_jurisdiction_job_title_id']);
            if($result) {
                return true;
            }
        }

        if (!empty($suspensionAuditConfig['suspension_audit_subordinate_job_title_id'])) {//【停职申请】可选择管辖范围员工的职位id
            $result = $this->get_audit_subordinate_job_title_id($params, $suspensionAuditConfig['suspension_audit_subordinate_job_title_id']);
            if($result) {
                return true;
            }
        }

        return false;
    }

    /**
     * 直属下级+下下级
     * @param $params
     * @param $config
     * @return bool
     */
    public function get_audit_subordinate_job_title_id($params, $config)
    {
        $job_title = [];
        $subordinate_job_list = explode(',', $config);
        if (!empty($subordinate_job_list)) {
            $job_title = array_merge($job_title, $subordinate_job_list);
        }

        if (!in_array($params['current_job_title_id'], $job_title)) {
            return false;
        }

        //找下级+ 下下级
        $subordinateStaffIds = [];
        $reportServer = new ReportServer($this->lang, $this->timeZone);
        $staffSubordinate = $reportServer->getSubordinateInfo([$params['staff_id']]);
        if($staffSubordinate) {
            $staffSubordinateIds = array_column($staffSubordinate, 'staff_info_id');

            $staffSubordinateSecond = $reportServer->getSubordinateInfo($staffSubordinateIds);
            $staffSubordinateSecondIds = array_column($staffSubordinateSecond, 'staff_info_id');

            $subordinateStaffIds = array_merge($staffSubordinateIds, $staffSubordinateSecondIds);
        }

        if(in_array($params['staff_info_id'], $subordinateStaffIds)) {
            return true;
        }

        return false;
    }

    /**
     * 【停职申请】可选择管辖范围员工的职位id
     * @param $params
     * @param $config
     * @return bool
     */
    public function get_audit_jurisdiction_job_title_id($params, $config)
    {
        $job_title = [];
        $jurisdiction_job_list = explode(',', $config);
        if (!empty($jurisdiction_job_list)) {
            $job_title = array_merge($job_title, $jurisdiction_job_list);
        }

        if (!in_array($params['current_job_title_id'], $job_title)) {
            return false;
        }

        $relations        = (new StaffServer($this->lang, $this->timeZone))->setExpire(60 * 3)->dominDepartmentsAndStoresFromCache($params['staff_id']);

        if($params['staff_sys_store_id'] == enums::HEAD_OFFICE_ID && in_array($params['staff_node_department_id'], $relations['flash_home_departments'])) {
            return true;
        }

        if($params['staff_sys_store_id'] != enums::HEAD_OFFICE_ID && in_array(SysStoreModel::ALL_STORE_PERMISSION, $relations['stores'])) {
            return true;
        }

        if($params['staff_sys_store_id'] != enums::HEAD_OFFICE_ID && in_array($params['staff_sys_store_id'], $relations['stores'])) {
            return true;
        }

        return false;
    }

    /**
     * 【停职申请】可选择管辖范围员工的角色id
     * @param $params
     * @param $config
     * @return bool
     */
    public function get_audit_jurisdiction_role_id($params, $config)
    {
        $roleIds = [];
        $jurisdiction_role_list = explode(',', $config);
        if (!empty($jurisdiction_role_list)) {
            $roleIds = array_merge($roleIds, $jurisdiction_role_list);
        }

        //没有在当前配置的角色中
        if (empty(array_intersect($params['positions'], $roleIds))) {
            return false;
        }

        $relations        = (new StaffServer($this->lang, $this->timeZone))->setExpire(60 * 3)->dominDepartmentsAndStoresFromCache($params['staff_id']);

        if($params['staff_sys_store_id'] == enums::HEAD_OFFICE_ID && in_array($params['staff_node_department_id'], $relations['flash_home_departments'])) {
            return true;
        }

        if($params['staff_sys_store_id'] != enums::HEAD_OFFICE_ID && in_array(SysStoreModel::ALL_STORE_PERMISSION, $relations['stores'])) {
            return true;
        }

        if($params['staff_sys_store_id'] != enums::HEAD_OFFICE_ID && in_array($params['staff_sys_store_id'], $relations['stores'])) {
            return true;
        }

        return false;
    }

    /**
     * 【停职申请】可选择指定部门员工的角色id：角色:部门,部门|角色:部门,部门
     * @param $params
     * @param $config
     * @return bool
     */
    public function get_audit_department_role_id($params, $config)
    {
        $roleIds = [];
        $departmentIds = [];
        $department_role_list = explode('|', $config);
        foreach ($department_role_list as $oneData) {
            if (empty($oneData)) {
                continue;
            }
            $dataArr = explode(':', $oneData);
            if (empty($dataArr)) {
                continue;
            }
            $roleIds[] = $dataArr[0];
            $departmentIds = array_values(array_unique(array_merge($departmentIds, explode(',', $dataArr[1]))));
        }

        //没有在当前配置的角色中
        if (empty(array_intersect($params['positions'], $roleIds))) {
            return false;
        }

        if(empty($departmentIds)) {
            return false;
        }

        $departmentConfig = (new DepartmentRepository())->getDepartmentChildInfo($departmentIds);

        if(!in_array($params['staff_node_department_id'], $departmentConfig)) {
            return false;
        }

        return true;
    }

    /**
     * 获取员工信息
     * @param $params
     * @return mixed
     * @throws BusinessException
     */
    public function getStaffInfo($params)
    {
        $staffRepository  = new StaffRepository();
        $staffData        = $staffRepository->getStaffInfoOne($params['staff_info_id']);//输入工号
        $currentStaffData = $staffRepository->getStaffInfoOne($params['staff_id']);//当前登录人

        if (empty($staffData)) {
            throw new BusinessException($this->getTranslation()->_('reinstatement_check_msg1'));//未找到数据
        }

        if ($staffData['formal'] == HrStaffInfoModel::FORMAL_0 || $staffData['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF || !in_array($staffData['hire_type'],
                self::$hireTypes)) {
            throw new BusinessException($this->getTranslation()->_('hire_type_error'));//仅可选择正式员工/月薪制合同工/日薪制合同工/时薪制合同工/实习生

        }

        // staff_info_id 停职 权限
        $params['staff_node_department_id'] = $staffData['node_department_id'];
        $params['staff_sys_store_id']       = $staffData['sys_store_id'];
        $params['current_job_title_id']     = $currentStaffData['job_title'];
        if (!$this->getPermission($params)) {
            throw new BusinessException($this->getTranslation()->_('no_permission_suspension'));//您无权为此工号申请停职
        }

        if ($staffData['state'] != HrStaffInfoModel::STATE_ON_JOB) {
            throw new BusinessException($this->getTranslation()->_('staff_info_state_error'));//员工非在职状态，不可提交停职申请
        }

        if ($staffData['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            throw new BusinessException($this->getTranslation()->_('staff_info_not_head_office'));//不可选择Head Office员工
        }
        $sysServer = new SysServer($this->lang, $this->timeZone);

        $job_title = array_column($sysServer->getJobTitleList(), 'job_name', 'id');

        $storeInfo = array_column($sysServer->getSysStoreList(), 'name', 'id');


        $data['staff_info_id']  = $staffData['staff_info_id'];
        $data['name']           = $staffData['name'];
        $data['job_title']      = $staffData['job_title'];
        $data['job_title_name'] = $job_title[$staffData['job_title']] ?? '';
        $data['hire_type']      = $staffData['hire_type'];
        $data['hire_type_text'] = !empty($staffData['hire_type']) ? $this->getTranslation()->_('hire_type_' . $staffData['hire_type']) : '';
        $data['store_id']       = $staffData['sys_store_id'];
        $data['store_name']     = $storeInfo[$staffData['sys_store_id']] ?? '';

        return $data;
    }

    /**
     * 获取 hold 枚举信息
     * @return array
     */
    public function selectInfo()
    {
        $ac = new ApiClient('hcm_rpc', '', 'get_hold_enums_info', $this->lang);
        $ac->setParams([]);
        $ac_result = $ac->execute();
        $data      = [];
        if (!empty($ac_result) && !empty($ac_result['result']['data'])) {
            $data['hold_type_list']   = $ac_result['result']['data']['hold_type_list'];
            $data['hold_reason_list'] = $ac_result['result']['data']['hold_reason_list'];
        }

        return $data;
    }

    /**
     * 创建申请，发起审批流
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function addAuditSuspension($params)
    {
        $suspension_staff_info = $this->getStaffInfo($params);

        $serialNo = $this->getRandomId();

        $db = $this->getDI()->get('db');
        try {
            $db->begin();

            $suspensionAudit                      = new SuspensionAuditModel();
            $suspensionAudit->serial_no           = 'RNTE' . $serialNo;
            $suspensionAudit->staff_info_id       = $params['staff_info_id'];
            $suspensionAudit->description         = $params['description'];
            $suspensionAudit->file_url            = json_encode($params['file_url'], JSON_UNESCAPED_UNICODE);
            $suspensionAudit->apply_staff_info_id = $params['staff_id'];
            $suspensionAudit->apply_name          = $params['staff_name'];
            $suspensionAudit->audit_state         = enums::APPROVAL_STATUS_PENDING;
            $suspensionAudit->status              = SuspensionAuditModel::STATUS_APPROVAL;//审批中

            $suspensionAudit->save();
            $id = $suspensionAudit->id;

            //这里是 from 表单的内容,用于查找审批人
            $extend['from_submit'] = [
                'sys_store_id' => $suspension_staff_info['store_id'],
                'audit_type'   => AuditListEnums::APPROVAL_TYPE_SUSPENSION,
            ];
            //创建审批
            $server    = new ApprovalServer($this->lang, $this->timeZone);
            $requestId = $server->create($id, AuditListEnums::APPROVAL_TYPE_SUSPENSION, $params['staff_id'], null, $extend);
            if (!$requestId) {
                throw new Exception($this->getTranslation()->_('4101'));
            }

            $db->commit();
        } catch (Exception $exception) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('addAuditSuspension ' . sprintf("Err_msg: %s, Err_File: %s, Err_Line:%s ",
                    $exception->getMessage(),
                    $exception->getFile(),
                    $exception->getLine()
                ), 'error');
            throw new Exception($this->getTranslation()->_('4101'));
        }

        return ['id' => $id];
    }


    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $suspensionInfo = SuspensionRepository::getInfo($auditId);
        if (empty($suspensionInfo)) {
            throw new Exception('audit not found ' . $this->getTranslation()->_('data_error'));
        }

        $staffRepository = new StaffRepository();
        $staffApplyData       = $staffRepository->getStaffInfoOne($suspensionInfo['apply_staff_info_id']);//申请人工号

        $departmentRepository = new DepartmentRepository($this->timeZone);
        $departmentInfoName   = $departmentRepository->getDepartmentNameById($staffApplyData['node_department_id']);

        $staffData       = $staffRepository->getStaffInfoOne($suspensionInfo['staff_info_id']);//输入工号

        $sysServer = new SysServer($this->lang, $this->timeZone);

        $job_title = array_column($sysServer->getJobTitleList(), 'job_name', 'id');

        $storeInfo = array_column($sysServer->getSysStoreList(), 'name', 'id');

        $detailLists['apply_parson']                    = sprintf('%s ( %s )', $staffApplyData['name'], $staffApplyData['staff_info_id']);
        $detailLists['apply_department']                = $departmentInfoName . '-' . $job_title[$staffApplyData['job_title']];
        $detailLists['staff_info_id']                   = $suspensionInfo['staff_info_id'];
        $detailLists['staff_name']                      = $staffData['name'];
        $detailLists['staff_job_title']                 = $job_title[$staffData['job_title']] ?? '';
        $detailLists['staff_store_name']                = $storeInfo[$staffData['sys_store_id']] ?? '';
        $detailLists['hire_type']                       = !empty($staffData['hire_type']) ? $this->getTranslation()->_('hire_type_' . $staffData['hire_type']) : '';
        $detailLists['description']                     = $suspensionInfo['description'];
        $detailLists['accident_report_and_attachments'] = json_decode($suspensionInfo['file_url'], true);

        $returnData['data']['detail'] = $this->format($detailLists);

        $auditListRepository = new AuditlistRepository($this->lang, $this->timeZone);
        $headData = [
            'title'       => $auditListRepository->getAudityType(enums::$audit_type['RNTE']),
            'id'          => $suspensionInfo['id'],
            'staff_id'    => $suspensionInfo['staff_info_id'],
            'type'        => enums::$audit_type['RNTE'],
            'created_at'  => DateHelper::utcToLocal($suspensionInfo['created_at']),
            'updated_at'  => DateHelper::utcToLocal($suspensionInfo['updated_at']),
            'status'      => $suspensionInfo['audit_state'],
            'status_text' => $auditListRepository->getAuditStatus('10' . $suspensionInfo['audit_state']),
            'serial_no'   => $suspensionInfo['serial_no'] ?? '',
        ];

        //如果存在可编辑字段，需返回默认参数
        $auditShowType  = $this->getAuditDetailRequest()->getAuditShowType();
        $auditStateType = $this->getAuditDetailRequest()->getAuditStateType();
        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);

        //审批状态为待审批 && 并且查看我的待审批 && 存在可编辑字段时，
        //返回当前节点的可编辑字段
        if ($suspensionInfo['audit_state'] == enums::APPROVAL_STATUS_PENDING &&
            $auditShowType . $auditStateType == '21' &&
            $approvalServer->isExistCanEditField($auditId, AuditListEnums::APPROVAL_TYPE_SUSPENSION)
        ) {
            //获取可编辑字段
            $canEditField = $approvalServer->getCanEditFieldColumns($auditId, AuditListEnums::APPROVAL_TYPE_SUSPENSION,
                AuditDetailOperationsEnums::RESPONSE_STRUCTURE_COLUMN_NAME);
        }

        //未审批时，返回NULL, 审批完成返回相关信息
        $suspension_info =  $this->getDefaultParameter($suspensionInfo);

        $returnData['data']['head'] = $headData;

        $returnData['data']['can_edit_field'] = $canEditField ?? [];
        $returnData['data']['suspension_info'] = $suspension_info;

        return $returnData;
    }

    /**
     * @param $list
     * @return array
     */
    public function format($list): array
    {
        $return = [];
        foreach ($list as $key => $v) {
            $item             = [];
            $item['key']      = $this->getTranslation()->_($key) ?? '';
            $item['key_tips'] = is_array($v) && isset($v['key_tips']) ? $v['key_tips'] : null;
            $item['key_icon'] = is_array($v) && isset($v['key_icon']) ? $v['key_icon'] : null;
            $item['value']    = is_array($v) && isset($v['value']) ? $v['value'] : $v;
            $item['tips']     = is_array($v) && isset($v['tips']) ? $v['tips'] : null;
            $item['color']    = is_array($v) && isset($v['color']) ? $v['color'] : null;
            if ($key == 'accident_report_and_attachments') {
                $item['type'] = enums::APPROVAL_DETAIL_TYPE_SUSPENSION;
            }
            $return[]         = $item;
        }
        return $return;
    }

    /**
     * 获取数据
     * @param $params
     * @return |null
     */
    public function getDefaultParameter($params)
    {
        $data['stop_duties_date'] = $params['stop_duties_date'];
        $data['hold_type'] = $params['hold_type'];
        $data['hold_reason'] = $params['hold_reason'];
        $data['violation_content'] = $params['violation_content'];
        $data['violation_clause'] = $params['violation_clause'];
        $data['month_begin'] = empty($params['month_begin']) ? '' : date('Y-m', strtotime($params['month_begin']));
        $data['month_end'] = empty($params['month_end']) ? '' : date('Y-m', strtotime($params['month_end']));
        if(empty($data['stop_duties_date'])) {
            return null;
        }
        return $data;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        //申请人不可撤销
        return new AuditOptionRule(false,
            false,
            false,
            false,
            false,
            false);
    }

    public function genSummary(int $auditId, $user)
    {
        $suspensionInfo = SuspensionRepository::getInfo($auditId);
        if (empty($suspensionInfo)) {
            throw new Exception('audit not found ' . $this->getTranslation()->_('data_error'));
        }

        $staffInfo = (new StaffRepository())->getStaffByStaffInfoId($suspensionInfo['staff_info_id']);

        if (empty($staffInfo)) {
            throw new Exception('staff_info not found ' . $this->getTranslation()->_('data_error'));
        }

        $managePiece = $manageRegion = '';
        if ($staffInfo['manage_region']) {
            $manageRegion = SysManageRegionModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind'       => [
                    'id' => $staffInfo['manage_region'],
                ],
            ]);
            $manageRegion = $manageRegion ? $manageRegion->toArray()['name'] : '';
        }

        if ($staffInfo['manage_piece']) {
            $managePiece = SysManagePieceModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind'       => ['id' => $staffInfo['manage_piece']],
            ]);
            $managePiece = $managePiece ? $managePiece->toArray()['name'] : '';
        }

        return [
            [
                'key'   => 'corr_stop_staff_name',//停职员工
                'value' => $staffInfo['name'] . "({$staffInfo['staff_id']})",
            ],
            [
                'key'   => 'hr_probation_field_sys_store_name', // 所属网点
                'value' => $staffInfo['store_name'] ?? '',
            ],
            [
                'key'   => 'region_piece_name', //  大区片区
                'value' => $manageRegion . ' ' . $managePiece,
            ],
        ];
    }

    public function getErValidate($params = [])
    {
        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);
        $validations['month_begin'] = 'StrLenGeLe:0,100|>>>:month_begin';
        $validations['month_end'] = 'StrLenGeLe:0,100|>>>:month_end';

        //对于存在可以编辑字段的情况，取出可编辑字段的类型
        return $approvalServer->getCanEditFieldValidation($params['audit_id'], AuditListEnums::APPROVAL_TYPE_SUSPENSION, [], $validations);
    }

    /**
     * er审批--审批流设置编辑表单
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function auditSubmit($paramIn)
    {
        $signInfo = SuspensionRepository::getSignConfigInfo($paramIn['staff_id']);
        if(empty($signInfo)) {
            throw new ValidationException('maintain_signature_config');//请先维护您的签字配置后再审批
        }

        $updateParams['stop_duties_date']  = $paramIn['stop_duties_date'];
        $updateParams['hold_type']         = $paramIn['hold_type'];
        $updateParams['hold_reason']       = $paramIn['hold_reason'];
        $updateParams['month_begin']       = empty($paramIn['month_begin']) ? null : date('Y-m-01', strtotime($paramIn['month_begin']));
        $updateParams['month_end']         = empty($paramIn['month_end']) ? null : date('Y-m-t', strtotime( $paramIn['month_end'] ."-01"));
        $updateParams['violation_content'] = $paramIn['violation_content'];
        $updateParams['violation_clause']  = $paramIn['violation_clause'];
        $updateParams['sign_staff_id']     = $signInfo['staff_info_id'] ?? 0;

        if (!empty($updateParams['month_begin']) && !$this->getStartMonth($updateParams['month_begin'])) {
            throw new ValidationException('limit_cycle_error');//限制周期错误

        }

        $db = $this->getDI()->get('db');
        try {
            $db->begin();

            $db->updateAsDict("suspension_audit",
                $updateParams, [
                    'conditions' => "id = ?",
                    'bind' => [
                        $paramIn['audit_id'],
                    ],
                ]);

            $this->approvalSubmit($paramIn);
            $db->commit();
            return $this->checkReturn(['data' => $paramIn['audit_id']]);
        } catch (ValidationException|\Exception $e) {
            $db->rollback();
            $this->logger->write_log('[auditSubmit]' . $e->getMessage() . $e->getTraceAsString(), 'notice');
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * 申请日期为在1号-14号，可选择上月及以后的月份；申请日期在15号-月底，可选择当月及以后的月份
     * @param $starMonth
     * @return bool
     * @throws Exception
     */
    public function getStartMonth($starMonth)
    {
        $today        = new DateTime();
        $day          = (int)$today->format('d');
        $currentMonth = (int)$today->format('m');
        $currentYear  = (int)$today->format('Y');

        $limitMonth = sprintf('%04d-%02d-01', $currentYear, $currentMonth);

        if ($day >= 1 && $day <= self::HOLD_LIMIT) {
            // 1号-14号：可选择上月及以后的月份
            $month = $currentMonth - 1;
            $year  = $currentYear;

            if ($month < 1) {
                $month = 12;
                $year--;
            }

            $limitMonth = sprintf('%04d-%02d-01', $year, $month);
        }

        if (strtotime($starMonth) < strtotime($limitMonth)) {
            return false;
        }
        return true;
    }

    /**
     * 审批
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function approvalSubmit($paramIn)
    {
        $staffId       = $this->processingDefault($paramIn, 'staff_id', 2);
        $id            = $this->processingDefault($paramIn, 'audit_id', 2);
        $status        = $this->processingDefault($paramIn, 'status', 2);
        $reject_reason = $this->processingDefault($paramIn, 'reject_reason');

        $suspensionRepository = new SuspensionRepository($this->timeZone);
        $suspensionInfo       = $suspensionRepository->getInfo($id, 'audit_state');

        if (empty($suspensionInfo)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }

        if ($suspensionInfo['audit_state'] == enums::APPROVAL_STATUS_APPROVAL) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        if ($suspensionInfo['audit_state'] == enums::APPROVAL_STATUS_REJECTED) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        $server = new ApprovalServer($this->lang, $this->timeZone);
        if ($status == enums::$audit_status['approved']) {
            // 同意
            $server->approval($id, AuditListEnums::APPROVAL_TYPE_SUSPENSION, $staffId);
        } else {
            // 驳回
            $server->reject($id, AuditListEnums::APPROVAL_TYPE_SUSPENSION, $reject_reason, $staffId);
        }

        return $this->checkReturn(['data' => ['id' => $id]]);
    }

    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $suspensionRepository = new SuspensionRepository($this->timeZone);
            $suspensionInfo       = $suspensionRepository->getInfo($auditId);

            if (empty($suspensionInfo)) {
                throw new ValidationException($this->getTranslation()->_('1015') . '[From suspensionAudit 审批终态设置]');
            }

            if ($suspensionInfo['audit_state'] == enums::APPROVAL_STATUS_APPROVAL) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From suspensionAudit 审批终态设置]');
            }

            if ($suspensionInfo['audit_state'] == enums::APPROVAL_STATUS_REJECTED) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From suspensionAudit 审批终态设置]');
            }

            if ($suspensionInfo['audit_state'] == enums::APPROVAL_STATUS_TIMEOUT) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From suspensionAudit 审批终态设置]');
            }

            if($state == enums::APPROVAL_STATUS_APPROVAL) {
                $data['audit_id']              = $auditId;
                $data['staff_info_id']     = $suspensionInfo['staff_info_id'];
                //修改 在职状态， 释放hold
                AuditCallbackServer::createData(AuditListEnums::APPROVAL_TYPE_SUSPENSION, $data);
            }


            $staffInfo = (new StaffRepository())->getStaffByStaffInfoId($extend['staff_id']);

            //超时的话，把处理状态也置为超时。
            $status = $state;
            if($state == enums::APPROVAL_STATUS_TIMEOUT) {
                $status = SuspensionAuditModel::STATUS_TIME_OUT;
            }

            $updateData['audit_state']              = $state;
            $updateData['status']                   = $status;//只会是，同意，驳回
            $updateData['audit_time']               = date('Y-m-d H:i:s');
            $updateData['last_audit_approval_id']   = $extend['staff_id'];
            $updateData['last_audit_approval_name'] = $staffInfo['name'] ?? '';

            $db                   = $this->getDI()->get('db');
            $db->updateAsDict('suspension_audit', $updateData,
                [
                    'conditions' => 'id = ?',
                    'bind'       => [$auditId],
                ]
            );
        }
    }


    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $suspensionInfo = SuspensionRepository::getInfo($auditId);
        if (empty($suspensionInfo)) {
            throw new Exception('suspension_audit not found ' . $this->getTranslation()->_('data_error'));
        }

        $staffInfo = (new StaffRepository())->getStaffInfoOne($suspensionInfo['staff_info_id']);
        if (empty($staffInfo)) {
            throw new Exception('suspension_audit staff_info not found ' . $this->getTranslation()->_('data_error'));
        }

        return ['request_department_id' => $staffInfo['node_department_id']];
    }

    /**
     * 同意停职，给员工 上级、DM、AM、网点对应HRBP
     * @param $data
     * @return bool
     * @throws Exception
     */
    public function delayCallBack($data)
    {
        $suspensionRepository = new SuspensionRepository($this->timeZone);
        $suspensionInfo       = $suspensionRepository->getInfo($data['audit_id']);
        if(empty($suspensionInfo)) {
            throw new Exception('suspension_audit apply_staff_info not found ' . $this->getTranslation()->_('data_error'));
        }

        $staffRepository = new StaffRepository();
        $staffData       = $staffRepository->getStaffInfoAllOne($suspensionInfo['staff_info_id']);//工号
        $managerData       = $staffRepository->getStaffInfoOne($staffData['manger']);//上级

        $managerInfo = $this->getManager($staffData);
        if(empty($managerInfo['manager_ids'])) {
            return true;
        }



        //获取员工和上级的语言包
        $staffServer   = new StaffServer();
        $staffLangInfo = $staffServer->getBatchStaffLanguage($managerInfo['manager_ids']);

        foreach ($managerInfo['manager_ids'] as $oneId) {
            $t             = $this->getTranslation($staffLangInfo[$oneId]);

            $title   = $t->_('suspension_remind_title',
                ['staff_info_id' => $suspensionInfo['staff_info_id'], 'name' => $staffData['name']]);
            $content = $t->_('suspension_remind_content',
                [
                    'staff_info_id' => $suspensionInfo['staff_info_id'],
                    'name' => $staffData['name'],
                    'stop_duties_date' => $suspensionInfo['stop_duties_date'],
                    'job_title_name' => $staffData['job_title_name'],
                    'store_name' => $staffData['store_name'],
                    'piece_name' => $managerInfo['piece_name'],
                    'region_name' => $managerInfo['region_name'],
                    'manager_name' => $managerData['name'] ?? '',
                    'manager_id' => $staffData['manger'],
                ]);

            $message_param['staff_users']        = [$oneId];
            $message_param['message_title']      = $title;
            $message_param['message_content']    = '<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no,viewport-fit=cover"><p style="font-size: 15px;">' . $content . "</p>";
            $message_param['staff_info_ids_str'] = $oneId;
            $message_param['id']                 = time() . $oneId . rand(1000000, 9999999);;
            $message_param['category'] = -1;
            $bi_rpc                    = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
            $bi_rpc->setParams($message_param);
            $bi_rpc->execute();
        }

        return true;
    }

    /**
     * 获取上级信息
     * @param $staffInfo
     * @return mixed
     */
    public function getManager($staffInfo)
    {
        $data['manager_ids'] = [];
        $data['region_name'] = '';
        $data['piece_name']  = '';
        if (empty($staffInfo)) {
            return $data;
        }
        $manager = [];

        //直线上级
        if (!empty($staffInfo['manger'])) {
            $manager[] = $staffInfo['manger'];
        }

        if (empty($staffInfo['sys_store_id']) || $staffInfo['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            return $data;
        }

        //找 hrbp;
        $hrbps = (new WorkflowServer($this->lang,$this->timeZone))->findHRBP($staffInfo['node_department_id'],['store_id'=>$staffInfo['sys_store_id']]);
        if(!empty($hrbps)) {
            $hrbpIds = explode(',', $hrbps);
            if(!empty($hrbpIds)) {
                $manager = array_merge($manager, $hrbpIds);
            }
        }

        $managerInfo = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getOrganizationRegionPieceManagerId($staffInfo['sys_store_id']);
        if(!empty($managerInfo['piece_manager_id'])) {
            $manager[] = $managerInfo['piece_manager_id'];
        }

        if(!empty($managerInfo['region_manager_id'])) {
            $manager[] = $managerInfo['region_manager_id'];
        }
        $data['manager_ids'] = array_values(array_unique($manager));
        $data['region_name'] = empty($managerInfo['region_name']) ? '' : $managerInfo['region_name'];
        $data['piece_name'] = empty($managerInfo['piece_name']) ? '' : $managerInfo['piece_name'];

        return $data;
    }

    /**
     * 校验 停职工号是否有 有效停职的申请
     * @param $paramIn
     * @return mixed
     * @throws Exception
     */
    public function checkInfoEr($paramIn)
    {
        $data['result'] = true;
        $data['date'] = '';
        $suspensionRepository = new SuspensionRepository($this->timeZone);
        $suspensionInfo       = $suspensionRepository->getInfo($paramIn['audit_id']);
        if(empty($suspensionInfo)) {
            throw new Exception('suspension_audit apply_staff_info not found ' . $this->getTranslation()->_('data_error'));
        }

        $where['staff_info_id'] = $suspensionInfo['staff_info_id'];
        $where['audit_states']   = [SuspensionAuditModel::NO_NEED_AUDIT, enums::APPROVAL_STATUS_APPROVAL];
        $suspensionInfo       = $suspensionRepository->getAuditInfo($where);


        if(empty($suspensionInfo)) {
            return $data;
        }

        $dates = [];
        foreach ($suspensionInfo as $one) {
            if($one['status'] == SuspensionAuditModel::STATUS_CANCEL) {
                continue;
            }
            if(strtotime($one['stop_duties_date']) > strtotime(date('Y-m-d'))) {
                $data['result'] = false;
                $dates[] = $one['stop_duties_date'];
            }
        }
        if (!empty($dates)) {
            $data['date'] = implode(',', $dates);
        }

        return $data;
    }

}