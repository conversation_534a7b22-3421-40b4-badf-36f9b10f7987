<?php


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\WorkflowConfigEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeAccidentBusinessModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeAttachmentModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeBaseModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeBaseMultiTypeCcModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeBaseMultiTypeModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeBaseOvertimeConfigModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeCcBaseModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeOvertimeConfigModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeRelateBaseModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeRelateModel;
use FlashExpress\bi\App\Traits\ComplexFormulaTrait;
use FlashExpress\bi\App\Util\WorkflowFormula;
use Ramsey\Uuid\Provider\Node\RandomNodeProvider;
use Ramsey\Uuid\Uuid;

/**
 * @description 审批流固化服务
 * Class WorkflowPersistentServer
 * @package FlashExpress\bi\App\Server
 */
class WorkflowPersistentServer extends BaseServer
{
    use ComplexFormulaTrait;
    const CONDITION_DEFAULT_SORT = 10; //审批条件默认排序
    private static $instance = null;

    /**
     * @var WorkflowServer
     */
    public $workflowServer = null;

    /**
     * @description 获取实例
     * @param $lang
     * @param $timezone
     * @return WorkflowPersistentServer
     */
    public static function getInstance($lang, $timezone): WorkflowPersistentServer
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self($lang, $timezone);
            self::$instance->init();
        }
        return self::$instance;
    }

    /**
     * 初始化
     * @return $this
     */
    private function init(): WorkflowPersistentServer
    {
        $this->workflowServer = new WorkflowServer($this->lang, $this->timeZone);
        return $this;
    }

    /**
     * 固化审批流
     * @param int $flowBaseId
     * @param int $user
     * @param array $extend
     * @param int $audit_type  类型
     * @param int $audit_id  业务 id
     * @return string  固化的审批流ID
     * @throws InnerException|\Exception
     */
    public function persistentWorkflow(int $flowBaseId, int $user, array $extend = [],int $audit_type=0,int $audit_id=0) : string
    {
        //生成新的审批流ID
        //uuid1 第二个参数的是避免同一台机器上同一微妙生成id重复的解决方案
        //有效值 0 ~ 16,383
        //使用文档链接: https://uuid.ramsey.dev/_/downloads/en/latest/pdf/
        if (class_exists('Ramsey\Uuid\Provider\Node\RandomNodeProvider')) {
            $nodeProvider = new RandomNodeProvider();
            $newFlowId    = Uuid::uuid1($nodeProvider->getNode(), mt_rand(1,16000))->toString();
        } else {
            $newFlowId    = $this->getRandomId();
        }

        //新增 撤销申请 审批流流程
        if (!empty($extend['is_cancel']) && !empty($extend['flow_id'])) {
            $newFlowId = $extend['flow_id'];
        }

        $relateList   = [];
        $termination  = [];

        //如果一个审批类型下存在多条审批流，则取得指定子审批流
        //如果一个审批类型下只有一条审批流，则取得默认的审批流
        //获取审批流
        $flowBase = WorkflowModel::findFirst($flowBaseId);
        if (empty($flowBase)) {
            throw new ValidationException('workflow flow not found');
        }
        $flowInfo = $flowBase->toArray();
        [$conditions, $binds] = $this->generateConditions($flowInfo, $extend);
        $data = WorkflowNodeBaseModel::find([
            'conditions' => $conditions,
            'bind' => $binds
        ]);

        //保存节点关系
        foreach ($data as $baseNodeItem) {
            $workflowNode = new WorkflowNodeModel();
            $workflowNode->setFlowId($newFlowId);
            $workflowNode->setName($baseNodeItem->name);
            $workflowNode->setType($baseNodeItem->type);
            $workflowNode->setAuditorType($baseNodeItem->auditor_type);

            //解析节点
            $ext = '';
            if ($baseNodeItem->auditor_type == Enums::WF_NODE_AUDITOR_TYPE_COMPLEX) {
                $workflowNode->setAuditorId($this->parseMultiNodeType($baseNodeItem, $user, $extend));
            } else {
                [$auditorId, $ext] = $this->workflowServer->parseNode($baseNodeItem, $user, $extend);
                $workflowNode->setAuditorId($auditorId);
            }

            //解析附加参数
            if (in_array($baseNodeItem->auditor_type, Enums::$nodeDeptType)) { //如果类型为部门类型的节点，则固化部门id
                $workflowNode->setAuditorLevel($ext ?? '');
            } else {
                $workflowNode->setAuditorLevel($baseNodeItem->auditor_level);
            }

            $workflowNode->setApprovalPolicy($baseNodeItem->approval_policy);
            $workflowNode->setSpecifyApprover($baseNodeItem->specify_approver);
            $workflowNode->save();
            $relateList[$baseNodeItem->id] = $workflowNode->id;

            if ($baseNodeItem->is_termination == WorkflowNodeAttachmentModel::TERMINATION_STATE_EXIST) { //存在终止逻辑
                $termination[] = [
                    'flow_id' => $newFlowId,
                    'node_id' => $workflowNode->id,
                    'termination_staff_id' => $baseNodeItem->termination_staff_id,
                ];
            }
        }
        //固化截止逻辑
        if (!empty($termination)) {
            foreach ($termination as $item) {
                $model = new WorkflowNodeAttachmentModel();
                $model->flow_id = $item['flow_id'];
                $model->node_id = $item['node_id'];
                $model->termination_staff_id = $item['termination_staff_id'];
                $model->save();
            }
        }

        //这里固化一下 抄送的数据相关
        $this->persistentWorkflowCc($binds, $relateList, $newFlowId,$user, $extend, $audit_type, $audit_id);

        $relateData = WorkflowNodeRelateBaseModel::find([
            'conditions' => $conditions,
            'bind' => $binds
        ]);

        //保存节点关系
        foreach ($relateData as $baseNodeItem) {
            $workflowNodeRelate = new WorkflowNodeRelateModel();
            $workflowNodeRelate->flow_id = $newFlowId;
            $workflowNodeRelate->from_node_id = $relateList[$baseNodeItem->from_node_id] ?? null;
            $workflowNodeRelate->to_node_id = $relateList[$baseNodeItem->to_node_id] ?? null;
            if(empty($baseNodeItem->valuate_formula) || !strpos($baseNodeItem->valuate_formula, '#')) { //存在待解析的条件
                $workflowNodeRelate->valuate_formula = $baseNodeItem->valuate_formula;
            } else {
                $workflowNodeRelate->valuate_formula = $this->parseComplexCondition($baseNodeItem->valuate_formula);
            }
            $workflowNodeRelate->valuate_code = $baseNodeItem->valuate_code;
            $workflowNodeRelate->remark = $baseNodeItem->remark;
            $workflowNodeRelate->sort = $baseNodeItem->sort;
            $workflowNodeRelate->save();
        }
        return $newFlowId;
    }

    /**
     * @description  固化审批流
     * @param int $flowBaseId
     * @param int $user
     * @param array $extend
     * @param int $auditType
     * @param int $auditId
     * @param string $workflow_id
     * @return string  固化的审批流ID
     * @throws InnerException
     * @throws ValidationException
     */
    public function persistentWorkflowV2(int $flowBaseId, int $user, array $extend = [], int $auditType = 0, int $auditId = 0,
        $workflow_id = ''): string
    {
        //生成审批流ID
        //[is_cancel]撤销审批需要走审批流、[is_recreate]重新创建审批需要走审批流
        if ((!empty($extend['is_cancel']) || !empty($extend['is_recreate'])) && !empty($extend['flow_id'])) {
            $newFlowId = $extend['flow_id'];
        } else {
            //使用文档链接: https://uuid.ramsey.dev/_/downloads/en/latest/pdf/
            if (class_exists('Ramsey\Uuid\Provider\Node\RandomNodeProvider')) {
                $nodeProvider = new RandomNodeProvider();
                $newFlowId    = Uuid::uuid1($nodeProvider->getNode(), mt_rand(1,16000))->toString();
            } else {
                $newFlowId    = $this->getRandomId();
            }
        }
        $this->logger->write_log("persistentWorkflowV2 start, flow id : {$newFlowId}", 'info');

        $app              = new ApprovalServer($this->lang, $this->timeZone);
        $nodeList         = [];
        $relateList       = [];
        $relateNodeIds    = [];
        $relateNeedToAdd  = [];
        $relateData       = [];

        //获取审批流
        $flowBase = WorkflowModel::findFirst($flowBaseId);
        if (empty($flowBase)) {
            throw new ValidationException('workflow flow not found');
        }
        $flowInfo = $flowBase->toArray();
        [$conditions, $binds] = $this->generateConditions($flowInfo, $extend);
        $data = WorkflowNodeBaseModel::find([
            'conditions' => $conditions,
            'bind' => $binds
        ]);

        //获取审批流超时配置
        $workflowNodeOtCnf = WorkflowNodeBaseOvertimeConfigModel::find([
            'conditions' => 'version = :version:',
            'bind'       => [
                'version' => $flowInfo['version'],
            ],
        ]);

        //初始化开始节点
        $startNodeId = $this->pickupSpecNodeId($data, enums::NODE_SUBMITTER);
        $finalNodeId = $this->pickupSpecNodeId($data, enums::NODE_FINAL);
        $currentNodeId = $startNodeId;

        //当前节点加到
        $auditParameters = $app->getWorkflowParams($auditId, $auditType, $user, null, false, $workflow_id);
        $currentNode     = $this->pickupNode($data, $currentNodeId);
        $nodeList[]      = $currentNode;
        $passNodeState   = false;

        $this->logger->write_log(sprintf("persistentWorkflowV2, flow id : %s,  startNodeId: %s, finalNodeId: %s ,auditParameters: %s", $newFlowId,$startNodeId,$finalNodeId, json_encode($auditParameters)), 'info');
        do {
            [$relateId, $nextNodeId] = $this->findNextBaseNode(
                $flowBaseId,
                $currentNodeId,
                $auditParameters,
                $user
            );

            //记录节点关系
            if (is_array($relateId)) {
                $nextNodeId = $this->reverseNextNodeIdByRelateIds($relateId);
                $relateNeedToAdd[] = [
                    'from_node_id'    => $currentNodeId,
                    'to_node_id'      => $nextNodeId,
                    'sort'            => self::CONDITION_DEFAULT_SORT,
                    'valuate_formula' => '',
                    'valuate_code'    => '',
                    'remark'          => '已触发跳过逻辑，直接跳转到下一个节点',
                ];
                $passNodeState = true;
            } else {
                $relateNodeIds[] = $relateId;
            }

            $nextNode = $this->pickupNode($data, $nextNodeId);
            $currentNodeId = $nextNodeId;

            $nodeList[] = $nextNode;
        } while ($nextNode->type != enums::NODE_FINAL);

        $this->logger->write_log(sprintf("persistentWorkflowV2, newFlowId: %s, nodeList %s", $newFlowId, json_encode($nodeList)), 'info');

        //获取超时配置
        $auditOvertimeConfig = $this->workflowServer->getAuditConfigure($auditType, enums::WF_OA_AUTO_CNF_OT_TYPE);

        //保存节点关系
        foreach ($nodeList as $baseNodeItem) {
            $workflowNode = new WorkflowNodeModel();
            $workflowNode->setFlowId($newFlowId);
            $workflowNode->setName($baseNodeItem->name);
            $workflowNode->setType($baseNodeItem->type);
            $workflowNode->setAuditorType($baseNodeItem->auditor_type);

            //解析节点
            $ext = '';
            if ($baseNodeItem->auditor_type == Enums::WF_NODE_AUDITOR_TYPE_COMPLEX) {
                $workflowNode->setAuditorId($this->parseMultiNodeType($baseNodeItem, $user, $extend));
            } else {
                [$auditorId, $ext] = $this->workflowServer->parseNode($baseNodeItem, $user, $extend);
                $workflowNode->setAuditorId($auditorId);
            }

            //解析附加参数
            if (in_array($baseNodeItem->auditor_type, Enums::$nodeDeptType)) { //如果类型为部门类型的节点，则固化部门id
                $workflowNode->setAuditorLevel($ext ?? '');
            } else {
                $workflowNode->setAuditorLevel($baseNodeItem->auditor_level);
            }

            $workflowNode->setApprovalPolicy($baseNodeItem->approval_policy);
            $workflowNode->setSpecifyApprover($baseNodeItem->specify_approver);
            $workflowNode->setNodeMark($baseNodeItem->node_mark);
            $workflowNode->setCanEditField($baseNodeItem->can_edit_field);
            $workflowNode->setNodeBaseId($baseNodeItem->id);
            $workflowNode->save();
            $relateList[$baseNodeItem->id] = $workflowNode->id;
        }

        //超时设置=单独设置每个节点超时时间
        //保存审批超时设置
        if (!empty($auditOvertimeConfig) && isset($auditOvertimeConfig[enums::WF_OA_AUTO_CNF_OT_TYPE]) &&
            $auditOvertimeConfig[enums::WF_OA_AUTO_CNF_OT_TYPE] == WorkflowConfigEnums::WF_OVERTIME_TYPE_INDIVIDUAL_NODE) {
            foreach ($workflowNodeOtCnf as $otCnfItem) {
                $workflowNodeCnf = new WorkflowNodeOvertimeConfigModel();

                //审批超时的配置是全部base节点的配置，固化仅仅固化当前条件分支经过的节点
                //所以可能会存在，部分审批超时的节点不在经过的节点。所以pass这样的节点即可
                if (!isset($relateList[$otCnfItem->node_id])) {
                    continue;
                }
                $workflowNodeCnf->setNodeId($relateList[$otCnfItem->node_id]);
                $workflowNodeCnf->setFlowId($newFlowId);
                $workflowNodeCnf->setIsSwitchOpen($otCnfItem->getIsSwitchOpen());
                $workflowNodeCnf->setOvertimeSubType($otCnfItem->getOvertimeSubType());
                $workflowNodeCnf->setOvertimeColumn($otCnfItem->getOvertimeColumn());
                $workflowNodeCnf->setOvertimeDays($otCnfItem->getOvertimeDays());
                $workflowNodeCnf->setOvertimePolicy($otCnfItem->getOvertimePolicy());
                $workflowNodeCnf->setHandoverPolicy($otCnfItem->getHandoverPolicy());
                $workflowNodeCnf->setHandoverConfig($otCnfItem->getHandoverConfig());
                $workflowNodeCnf->setHandoverAuditDays($otCnfItem->getHandoverAuditDays());
                $workflowNodeCnf->setHandoverOvertimePolicy($otCnfItem->getHandoverOvertimePolicy());
                $workflowNodeCnf->setIsHandoverTermination($otCnfItem->getIsHandoverTermination());
                $workflowNodeCnf->setHandoverTerminationStaffIds($otCnfItem->getHandoverTerminationStaffIds());
                $workflowNodeCnf->save();
            }
        }

        //固化抄送相关的数据
        $this->persistentWorkflowCc($binds, $relateList, $newFlowId,$user, $extend, $auditType, $auditId);

        //固化节点关系
        if (!empty($relateNodeIds)) {
            $relateData = WorkflowNodeRelateBaseModel::find([
                'conditions' => $conditions . " and id in({node_ids:array})",
                'bind' => array_merge($binds, [
                    'node_ids' => $relateNodeIds,
                ])
            ])->toArray();
        }

        if ($passNodeState) {
            $relateData = array_merge($relateData ?? [], $relateNeedToAdd);
        }

        //保存节点关系
        foreach ($relateData as $baseNodeItem) {
            $workflowNodeRelate = new WorkflowNodeRelateModel();
            $workflowNodeRelate->flow_id = $newFlowId;
            $workflowNodeRelate->from_node_id = $relateList[$baseNodeItem['from_node_id']] ?? null;
            $workflowNodeRelate->to_node_id = $relateList[$baseNodeItem['to_node_id']] ?? null;
            $workflowNodeRelate->remark = $baseNodeItem['remark'];
            $workflowNodeRelate->sort = $baseNodeItem['sort'];

            //审批过程前置到申请阶段
            $workflowNodeRelate->valuate_formula = '';
            $workflowNodeRelate->valuate_code = '';
            $workflowNodeRelate->save();
        }

        $this->logger->write_log("persistentWorkflowV2 end, flow id : {$newFlowId}", 'info');
        return $newFlowId;
    }

    /**
     * 获取下一个base节点
     * @param $flowId
     * @param $nodeId
     * @param $args
     * @param $user
     * @return mixed
     * @throws InnerException
     */
    public function findNextBaseNode($flowId, $nodeId, $args, $user)
    {
        $lines = $this->getFlowNodeRelate($flowId, $nodeId, 2)->toArray();
        if (empty($lines)) { //无下一个节点了
            return [];
        }
        //注册stream_wrapper
        registerStream();
        $class    = new WorkflowFormula();
        $nodeId   = null;
        $relateId = null;

        foreach ($lines as $v) {
            if (empty($v['valuate_code']) || empty($v['valuate_formula'])) { //无有效条件、参数
                $nodeId   = $v['to_node_id'];
                $relateId = $v['id'];
                break;
            }

            //当前节点关系对应的code
            $params  = [];
            $methods = explode(',', $v['valuate_code']);
            foreach ($methods as $k => $method) {
                $method = trim($method);
                if (!method_exists($class, $method)) {
                    throw new InnerException(sprintf("Handler Method %s not exists", $method));
                }
                $key          = 'p' . ($k + 1);
                $params[$key] = $class->$method($args);
            }

            //当前节点关系对应的formula
            if (!strpos($v['valuate_formula'], '#')) { //存在待解析的条件
                $formula = $v['valuate_formula'] ?? '';
            } else {
                $formula = $this->parseComplexCondition($v['valuate_formula']);
            }

            $result = include 'var://<?php extract($params); return ' . $formula . ';';
            if ($result === true) {
                $nodeId   = $v['to_node_id'];
                $relateId = $v['id'];
                break;
            }
        }

        if (empty($nodeId)) {
            throw new InnerException("Invalid Base node ID, current node = {$nodeId} , params = " . json_encode($args));
        }

        //如果该节点需要跳过，则根据当前节点继续往下找一个有效节点
        if ($this->isCurrentNodeNeedPass($nodeId, $user)) {
            [$passNodeNextRelateId, $passNodeNextNodeId] = $this->findNextBaseNode($flowId, $nodeId, $args, $user);
            $originNodeId = $nodeId;
            $nodeId       = $passNodeNextNodeId;
            if (is_array($passNodeNextRelateId)) {
                $relateId = array_merge($passNodeNextRelateId, [$relateId]);
            } else {
                $relateId = [$relateId, $passNodeNextRelateId];
            }

            $this->logger->write_log("persistentWorkflowV2 finding next node exists PASS NODE, a -> b: {$originNodeId} -> {$nodeId}",
                'info');
        }

        $this->logger->write_log(sprintf("persistentWorkflowV2 finding next node, current node id : %s, next node id: %d", json_encode($relateId), $nodeId),
            'info');

        return [$relateId, $nodeId];
    }

    /**
     * @description 当前节点是否需要跳过
     * @param int $nodeId
     * @param int $user
     * @return bool
     */
    public function isCurrentNodeNeedPass(int $nodeId, int $user): bool
    {
        //获取节点详情
        $nodeInfo = WorkflowNodeBaseModel::findFirst([
            'conditions' => "id = :node:",
            'bind' => [
                'node' => $nodeId
            ],
        ]);
        //是否配置了节点跳过
        if (empty($nodeInfo) || $nodeInfo->is_termination == WorkflowNodeBaseModel::NODE_PASS_OFF) {
            return false;
        }
        $nodePassStaffIds = explode(',', $nodeInfo->termination_staff_id);
        $auditorId        = ApprovalFinderServer::getInstance()->findSuperior($user, $nodeInfo->auditor_level);
        return $nodeInfo->auditor_type == enums::WF_NODE_MANAGER && in_array($auditorId, $nodePassStaffIds);
    }

    /**
     * 获取审批流节点关系
     * @param $flow_id
     * @param $node_id
     * @param $type
     * @return mixed
     */
    public function getFlowNodeRelate($flow_id, $node_id, $type)
    {
        if ($type == 1) {
            return WorkflowNodeRelateModel::find(
                [
                    'conditions' => 'from_node_id = :node: and flow_id = :flow: and deleted = 0',
                    'bind' => [
                        'node' => $node_id,
                        'flow' => $flow_id
                    ],
                    'order' => 'sort desc'
                ]
            );
        } else {
            return WorkflowNodeRelateBaseModel::find(
                [
                    'conditions' => 'from_node_id = :node: and flow_id = :flow: and deleted = 0',
                    'bind' => [
                        'node' => $node_id,
                        'flow' => $flow_id
                    ],
                    'order' => 'sort desc'
                ]
            );
        }
    }

    /**
     * @description  获取特殊节点节点
     * 根据节点类型 0=开始节点 1=审批节点 2=抄送节点 3=会签节点 99=结束节点
     *
     * @param $flowData
     * @param $nodeType
     * @return int
     * @throws InnerException
     */
    public function pickupSpecNodeId($flowData, $nodeType): int
    {
        $nodeInfo = [];
        foreach ($flowData as $item) {
            if ($item->type == $nodeType) {
                $nodeInfo = $item;
            }
        }

        if (empty($nodeInfo)) {
            throw new InnerException(sprintf('node type is %d, do not have spec node!', $nodeType));
        }
        return $nodeInfo->id;
    }

    /**
     * 获取当前审核节点
     * @param $nodes
     * @param $currentNodeId
     * @return mixed
     */
    public function pickupNode($nodes, $currentNodeId)
    {
        foreach ($nodes as $v){
            if ($v->id == $currentNodeId)
                return $v;
        }
        return '';
    }

    /**
     * @description 刷新当前节点后面已经固化的节点
     * @param $request
     * @param $user
     * @param array $extend
     * @throws \FlashExpress\bi\App\library\Exception\InnerException
     */
    public function refreshHasPersistentNode($request, $user, $extend = [])
    {
        if (empty($request)) {
            return;
        }
        $leftNode = $this->workflowServer->getWorkflowNodeByFlowId($request, $user);
        if (empty($leftNode)) {
            return;
        }

        //获取节点(含：审批节点、抄送节点)
        $nodesInfo = WorkflowNodeModel::find([
            'conditions' => 'id in({node_ids:array})',
            'bind' => [
                'node_ids' => $leftNode,
            ],
        ]);
        foreach ($nodesInfo as $node) {
            if ($node->getAuditorType() == enums::WF_NODE_AUDITOR_TYPE_COMPLEX) {
                $auditorId = $this->parseMultiNodeTypeByNode($node, $user, $extend);
            } else {
                [$auditorId, $ext] = $this->workflowServer->parseNode($node, $user, $extend);
            }
            $node->setAuditorId($auditorId);
            $node->save();
            $this->logger->write_log(sprintf('[refreshHasPersistentNode][WorkflowNodeModel][nodeId = %d,auditorId = %s]',
                $node->getId(), $auditorId), 'info');
        }

        //节点后跟抄送节点
        $nodeAttachedCc = WorkflowNodeAccidentBusinessModel::find([
            'conditions' => 'node_id in({node_ids:array}) and business = 2',
            'bind' => [
                'node_ids' => $leftNode,
            ],
        ]);
        if (empty($nodeAttachedCc)) {
            return;
        }
        foreach ($nodeAttachedCc as $node) {
            if ($node->auditor_type == Enums::WF_NODE_AUDITOR_TYPE_COMPLEX) {
                $auditorId = $this->parseMultiNodeTypeByCcNode($node, $user, $extend);
            } else {
                $auditorId = $this->parseNodeCc($node, $user, $extend);
            }

            $node->setAuditorId($auditorId);
            $node->save();
            $this->logger->write_log(sprintf('[refreshHasPersistentNode][WorkflowNodeAccidentBusinessModel][nodeId = %d,auditorId = %s]',
                $node->getId(), $auditorId), 'info');
        }
    }

    /**
     * 根据当前节点获取base节点配置，重新获取复杂节点的审批人
     * @param $node
     * @param $user
     * @param $extend
     * @return string
     * @throws InnerException
     */
    private function parseMultiNodeTypeByNode($node, $user, $extend): string
    {
        if (!($node instanceof WorkflowNodeModel)) {
            return '';
        }
        $multiTypes = WorkflowNodeBaseMultiTypeModel::find([
            'conditions' => 'node_id = :node_id:',
            'bind' => [
                'node_id' => $node->getNodeBaseId(),
            ]
        ]);
        if (empty($multiTypes)) {
            return "";
        }

        $approvals = [];
        foreach ($multiTypes as $v) {
            //这里是 new WorkflowNodeModel 没有保存只是为了获取审批人
            $newNode = new WorkflowNodeModel();
            $newNode->setAuditorType($v->getAuditorType());
            $newNode->setAuditorLevel($v->getAuditorLevel());
            $newNode->setAuditorId($v->getAuditorId());
            $newNode->setSpecifyApprover($node->specify_approver);
            $newNode->setApprovalPolicy($node->approval_policy);

            //请求节点审批人
            [$auditorIds, $ext] = $this->workflowServer->parseNode($newNode, $user, $extend);
            $approvals  = array_merge($approvals, explode(',', $auditorIds));
            $approvals  = array_filter(array_unique($approvals));
        }
        return $approvals ? implode(',', $approvals) : '';
    }

    /**
     * 根据当前节点获取base节点配置，重新获取复杂节点的抄送人
     * @param $node
     * @param $user
     * @param $extend
     * @return string
     * @throws InnerException
     */
    private function parseMultiNodeTypeByCcNode($node, $user, $extend): string
    {
        if (!($node instanceof WorkflowNodeAccidentBusinessModel)) {
            return '';
        }

        $multiTypes = WorkflowNodeBaseMultiTypeCcModel::find([
            'conditions' => 'node_cc_id = :node_cc_id:',
            'bind'       => [
                'node_cc_id' => $node->getNodeBaseCcId(),
            ],
        ]);
        if (empty($multiTypes)) {
            return "";
        }

        $approvals = [];
        foreach ($multiTypes as $v) {
            //这里是 new WorkflowNodeCcModel 没有保存只是为了获取审批人
            $newNode = new WorkflowNodeAccidentBusinessModel();
            $newNode->setAuditorType($v->getAuditorType());
            $newNode->setAuditorLevel($v->getAuditorLevel());
            $newNode->setAuditorId($v->getAuditorId());

            //请求节点审批人
            $auditorIds = $this->parseNodeCc($newNode, $user, $extend);
            $approvals  = array_merge($approvals, explode(',', $auditorIds));
            $approvals  = array_filter(array_unique($approvals));
        }
        return $approvals ? implode(',', $approvals) : '';
    }

    /**
     * @description 组织查询条件
     * @param $flowBase
     * @param $extend
     * @return array
     */
    public function generateConditions($flowBase, $extend): array
    {
        if (!empty($flowBase['version'])) {
            //对接到可视化审批流的 没有 code
            $conditions = "flow_id = :flow_id: and ifnull(code, '') = '' and version = :version: and deleted = 0";
            $binds = [
                'flow_id'  => $flowBase['id'],
                'version' => $flowBase['version'],
            ];

        } else {
            if (isset($extend['flow_code']) && $extend['flow_code']) {
                $conditions = "flow_id = :flow_id: and code = :code: and deleted = 0";
                $binds = [
                    'flow_id'  => $flowBase['id'],
                    'code'    => $extend['flow_code']
                ];
            } else {
                $conditions = "flow_id = :flow_id: and ifnull(code, '') = '' and deleted = 0";
                $binds = [
                    'flow_id'  => $flowBase['id'],
                ];
            }
        }
        return [$conditions, $binds];
    }

    /**
     * @description 根据节点之间的线计算下一个节点ID
     * @param $relate_ids
     * @return int
     */
    private function reverseNextNodeIdByRelateIds($relate_ids): int
    {
        $relateData = WorkflowNodeRelateBaseModel::find([
            'conditions' => "id in({relate_ids:array})",
            'bind' => [
                'relate_ids' => $relate_ids,
            ],
            'columns' => 'from_node_id, to_node_id',
        ])->toArray();

        $fromNodeList = array_column($relateData, 'from_node_id');
        foreach ($relateData as $item) {
            if (!in_array($item['to_node_id'], $fromNodeList)) {
                return $item['to_node_id'];
            }
        }
        return 0;
    }

    /**
     * 解析节点复合逻辑
     * @param $node
     * @param $user
     * @param array $extend
     * @return string
     * @throws InnerException
     */
    public function parseMultiNodeType($node, $user, array $extend = []): string
    {
        $multiTypes = WorkflowNodeBaseMultiTypeModel::find([
            'conditions' => 'node_id = :node_id:',
            'bind' => [
                'node_id' => $node->id,
            ]
        ]);
        if (empty($multiTypes)) {
            return "";
        }

        $approvals = [];
        foreach ($multiTypes as $v) {
            //这里是 new WorkflowNodeModel 没有保存只是为了获取审批人
            $newNode = new WorkflowNodeModel();
            $newNode->setAuditorType($v->getAuditorType());
            $newNode->setAuditorLevel($v->getAuditorLevel());
            $newNode->setAuditorId($v->getAuditorId());
            $newNode->setSpecifyApprover($node->specify_approver);
            $newNode->setApprovalPolicy($node->approval_policy);

            //请求节点审批人
            [$auditorIds, $ext] = $this->workflowServer->parseNode($newNode, $user, $extend);
            $approvals  = array_merge($approvals, explode(',', $auditorIds));
            $approvals  = array_filter(array_unique($approvals));
        }
        return $approvals ? implode(',', $approvals) : '';
    }

    /**
     * 解析节点复合逻辑
     * @param $node
     * @param $user
     * @param array $extend
     * @return string
     * @throws InnerException
     */
    public function parseMultiNodeTypeCc($node, $user, array $extend = []): string
    {
        $multiTypes = WorkflowNodeBaseMultiTypeCcModel::find([
            'conditions' => 'node_cc_id = :node_cc_id:',
            'bind'       => [
                'node_cc_id' => $node->id,
            ],
        ]);
        if (empty($multiTypes)) {
            return "";
        }

        $approvals = [];
        foreach ($multiTypes as $v) {
            //这里是 new WorkflowNodeCcModel 没有保存只是为了获取审批人
            $newNode = new WorkflowNodeAccidentBusinessModel();
            $newNode->setAuditorType($v->getAuditorType());
            $newNode->setAuditorLevel($v->getAuditorLevel());
            $newNode->setAuditorId($v->getAuditorId());

            //请求节点审批人
            $auditorIds = $this->parseNodeCc($newNode, $user, $extend);
            $approvals  = array_merge($approvals, explode(',', $auditorIds));
            $approvals  = array_filter(array_unique($approvals));
        }
        return $approvals ? implode(',', $approvals) : '';
    }

    /**
     * @description:保存审批流抄送节点相关
     *
     * @param array $binds 查询条件
     * @param array $relateList 这里是 固化的审批流节点 ['审批流 节点id'=>'固化的节点 id']
     * @param string $newFlowId 审批流FlowId
     * @param int $user 创建人
     * @param array $extend 参数
     * @param int $audit_type
     * @param int $audit_id
     * @return bool :
     * @throws InnerException
     * <AUTHOR> L.J
     * @time       : 2022/1/20 14:15
     */
    public function persistentWorkflowCc(
        $binds = [],
        $relateList = [],
        $newFlowId = '',
        $user = 0,
        $extend = [],
        $audit_type = 0,
        $audit_id = 0)
    {
        if (!isset($binds['flow_id']) || !isset($binds['version']) || empty($relateList)) {
            return false;
        }
        if(empty($audit_type) || empty($newFlowId) || empty($audit_id)){
            $this->logger->write_log("persistentWorkflowCc 固化抄送失败!" . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
            return false;
        }
        //查询审批流抄送关系
        $where_binds = [
            'flow_id' => $binds['flow_id'],
            'version' => $binds['version']
        ];
        $data = WorkflowNodeCcBaseModel::find([
            'conditions' => "flow_id = :flow_id: and version = :version: and deleted = 0",
            'bind'       => $where_binds,
        ]);
        if (empty($data)) {
            return true;
        }
        foreach ($data as $k => $v) {
            if (isset($relateList[$v->node_base_id])) {
                $workflowNode = new WorkflowNodeAccidentBusinessModel();
                $workflowNode->setFlowId($newFlowId);
                $workflowNode->setNodeId($relateList[$v->node_base_id]);
                $workflowNode->setType($v->type);
                $workflowNode->setAuditorType($v->auditor_type);
                $workflowNode->setAuditorLevel($v->auditor_level);
                if ($v->auditor_type == Enums::WF_NODE_AUDITOR_TYPE_COMPLEX) {
                    $workflowNode->setAuditorId($this->parseMultiNodeTypeCc($v, $user, $extend));
                } else {
                    $workflowNode->setAuditorId($this->parseNodeCc($v, $user, $extend));
                }
                $workflowNode->setBusiness(WorkflowNodeAccidentBusinessModel::BUSINESS_TYPE_CC); //2 是抄送
                $workflowNode->setBizType($audit_type);
                $workflowNode->setSubmitterId($user);
                $workflowNode->setBizValue($audit_id);
                $workflowNode->setVersion($binds['version']);
                $workflowNode->setNodeBaseCcId($v->node_base_id);
                $workflowNode->save();
            }
        }
        return true;
    }

    /**
     * @throws InnerException
     */
    public function parseNodeCc($node, $user, $extend = [])
    {
        $staff_ids = '';
        [$staff_ids, $ext] = $this->workflowServer->parseNode($node, $user, $extend,Enums::NODE_WORKFLOW_CC);
        return $staff_ids;
    }


}