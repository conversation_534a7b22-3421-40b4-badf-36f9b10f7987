<?php
namespace FlashExpress\bi\App\Server;

use app\modules\Rpc\callback\Kpi;
use FlashExpress\bi\App\Models\backyard\KpiLeaderMsgSendLogModel;
use FlashExpress\bi\App\Enums\KpiEnums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\KpiActivityModel;
use FlashExpress\bi\App\Models\backyard\KpiActivityStaffModel;
use FlashExpress\bi\App\Models\backyard\KpiStaffIndicatorsRelModel;
use FlashExpress\bi\App\Models\backyard\SysAttachmentModel;
use FlashExpress\bi\App\Models\backyard\KpiValuesModel;
use FlashExpress\bi\App\Server\BaseServer AS GlobalBaseServer;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

/**
 * 个人绩效服务层
 * Class KpiServer
 * @package FlashExpress\bi\App\Modules\Th\Server
 */

class KpiServer extends GlobalBaseServer
{
    public $timezone;

    /**
     * InventoryCheckServer constructor.
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone='+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * 个人绩效-通知消息详情页面
     * @param integer $activity_id 活动ID
     * @return array
     */
    public function getMsgDetail($activity_id)
    {
        $data = [
            'activity_id' => '',
            'years' => '',
            'period'=>'',
            'period_text_zh' => '',
            'period_text_en' => '',
            'period_text_th' => ''
        ];
        //获取活动信息
        $activity_info = $this->getActivityInfoById($activity_id);
        if ($activity_info) {
            $data['activity_id'] = $activity_info['id'];
            $data['years'] = $activity_info['years'];
            $data['period'] = $activity_info['period'];
            $data['period_text_zh'] = $this->getTranslation('zh-CN')->_(KpiEnums::$kpi_activity_period[$activity_info['period']]);
            $data['period_text_en'] = $this->getTranslation('en')->_(KpiEnums::$kpi_activity_period[$activity_info['period']]);
            $data['period_text_th'] = $this->getTranslation('th')->_(KpiEnums::$kpi_activity_period[$activity_info['period']]);
        }
        return $data;
    }

    /**
     * 根据活动ID获取活动信息
     * @param integer $activity_id 活动ID
     * @return array
     */
    public function getActivityInfoById($activity_id)
    {
        $activity = KpiActivityModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $activity_id]
        ]);
        return empty($activity) ? [] : $activity->toArray();
    }

    /**
     * 获取活动的价值观列表
     * @param integer $activity_id 活动ID
     * @return mixed
     */
    public function getValues($activity_id)
    {
        return KpiValuesModel::find([
            'conditions' => 'activity_id = :activity_id: ',
            'bind' => ['activity_id' => $activity_id],
            'columns' => ['concept', 'criterion', 'score', 'sort'],
            'order' => 'sort, score ASC'
        ])->toArray();
    }

    /**
     * 获取员工某个活动的绩效指标列表
     * @param integer $activity_id 活动ID
     * @param integer $staff_id 员工工号
     * @return array
     */
    public function getIndicators($activity_id, $staff_id)
    {
        $data = [
            'info' => [
                'id' => '',
                'stage_status' => '',
                'confirm_url' =>''
            ],
            'indicators_list' => []
        ];
        $staff = KpiActivityStaffModel::findFirst([
            'conditions' => 'activity_id = :activity_id: and staff_id = :staff_id: and stage IN ({stage_default:array})',
            'bind' => [
                'activity_id' => $activity_id,
                'staff_id' => $staff_id,
                'stage_default' => [KpiEnums::STAGE_LEADER,KpiEnums::STAGE_STAFF,KpiEnums::STAGE_DONE]
            ],
            'columns' => 'id, stage, sys_attachment_id',
        ]);
        if (!empty($staff)) {
            $indicators_list = KpiStaffIndicatorsRelModel::find([
                'conditions' => 'activity_id = :activity_id: and staff_id = :staff_id:',
                'bind' => [
                    'activity_id' => $activity_id,
                    'staff_id' => $staff_id
                ],
                'columns' => 'name, importance, method, target, standard',
            ])->toArray();
            $data['info']['id'] = $staff->id;
            $data['info']['stage_status'] = $staff->stage;
            $data['indicators_list'] = $indicators_list;
            $sys_attachment_id = $staff->sys_attachment_id;
            if ($staff->stage == KpiEnums::STAGE_DONE && $sys_attachment_id) {
                //如果员工是已确认签字状态，需要获取签名url
                $attachment_info = SysAttachmentModel::findFirst([
                    'conditions' => 'id = :id: AND deleted = :deleted:',
                    'bind' => ['id' => $sys_attachment_id, 'deleted' => KpiEnums::IS_DELETED_NO],
                    'columns' => ['object_key']
                ]);
                $data['info']['confirm_url'] = !empty($attachment_info) ? $this->getDI()->getConfig()->application['img_prefix'] . $attachment_info->object_key : '';
            }
        }
        return $data;
    }

    /**
     * 获取员工参与的活动列表
     * @param array $user 当前登陆者信息组
     * @return mixed
     */
    public function getParticipateActivityList($user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['kas' => KpiActivityStaffModel::class]);
        $builder->leftJoin(KpiActivityModel::class, 'kas.activity_id = ka.id', 'ka');
        $builder->columns('kas.activity_id, kas.staff_id, kas.stage, kas.confirm_at, kas.sys_attachment_id, ka.years, ka.period');
        $builder->where('kas.staff_id = :staff_id: and kas.stage IN ({stage_default:array})', ['staff_id' => $user['staff_id'], 'stage_default' => [KpiEnums::STAGE_LEADER,KpiEnums::STAGE_STAFF,KpiEnums::STAGE_DONE]]);
        $builder->orderBy('kas.stage ASC, kas.id DESC');
        $list = $builder->getQuery()->execute()->toArray();
        if (!empty($list)) {
            foreach ($list as &$values) {
                $values['period_text'] = $this->getTranslation()->_(KpiEnums::$kpi_activity_period[$values['period']]);
            }
        }
        return $list;
    }

    /**
     * 员工签名
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws ValidationException
     */
    public function staffSignature($params, $user)
    {
        //先检查能否查到签名信息
        $kpiStaff = KpiActivityStaffModel::findFirst([
            'conditions' => 'id = :id: and staff_id = :staff_id: and stage IN ({stage_default:array})',
            'bind' => [
                'id' => $params['id'],
                'staff_id' => $user['staff_id'],
                'stage_default' => [KpiEnums::STAGE_LEADER, KpiEnums::STAGE_STAFF, KpiEnums::STAGE_DONE]
            ]
        ]);
        if (empty($kpiStaff)) {
            //未找到员工信息
            throw new ValidationException($this->getTranslation()->_('kpi_activity_staff_not_found'));
        }
        if ($kpiStaff->stage != KpiEnums::STAGE_STAFF) {
            //非待确认状态无需签名
            throw new ValidationException($this->getTranslation()->_('kpi_activity_staff_cannot_confirm'));
        }

        //开始签名
        $db = $this->getDI()->get('db');
        $db->begin();
        $addHour = $this->getDI()['config']['application']['add_hour'];
        $curTime = gmdate("Y-m-d H:i:s", time() + $addHour * 3600);

        //插入附件信息
        $attachmentId = md5("{$curTime}_{$user['staff_id']}_{$params['id']}");
        $data = [
            'id' => $attachmentId,
            'oss_bucket_type' => KpiEnums::OSS_BUCKET_TYPE_KPI_SIGNATURE,
            'oss_bucket_key' => $params['id'],
            'bucket_name' => $params['bucket_name'],
            'object_key' => $params['object_key'],
            'original_name' => $params['file_name'],
            'created_at' => $curTime
        ];
        $attachment = new SysAttachmentModel();
        $bool = $attachment->create($data);
        if (!$bool) {
            //插入附件信息失败，事务回滚
            $db->rollback();
            return $this->checkReturn(['code' => ErrCode::FAIL, 'msg' => $this->getTranslation()->_('please try again')]);
        }

        //更新参与活动员工确认信息
        $kpiStaff->stage = KpiEnums::STAGE_DONE;
        $kpiStaff->confirm_at = $curTime;
        $kpiStaff->sys_attachment_id = $attachmentId;
        $kpiStaff->updated_at = $curTime;
        $bool = $kpiStaff->update();
        if (!$bool) {
            //更新签名信息信息失败，事务回滚
            $db->rollback();
            return $this->checkReturn(['code' => ErrCode::FAIL, 'msg' => $this->getTranslation()->_('please try again')]);
        }

        //均成功事务回滚
        $db->commit();
        return $this->checkReturn(['code' => ErrCode::SUCCESS, 'msg' => 'success']);
    }

    /**
     * 获取员工待确认状态的总数
     * @param int $staff_info_id 员工工号
     * @return mixed
     */
    public function getKpiWaitConfirmCount($staff_info_id)
    {
        return KpiActivityStaffModel::count([
            'conditions' => 'staff_id = :staff_id: and stage = :stage:',
            'bind' => ['staff_id' => $staff_info_id, 'stage' => KpiEnums::STAGE_STAFF]
        ]);
    }

    /**
     * 获取kpi权限
     * @param array $user 当前登陆者信息组
     * @return bool
     */
    public function getKpiPermission($user)
    {
        $count = KpiActivityStaffModel::count([
            'conditions' => 'staff_id = :staff_id: and stage IN ({stage_default:array})',
            'bind' => ['staff_id' => $user['staff_id'], 'stage_default' => [KpiEnums::STAGE_LEADER, KpiEnums::STAGE_STAFF, KpiEnums::STAGE_DONE]]
        ]);
        return $count ? true : false;
    }

    /**
     * 获取正在进行中的活动
     * @return mixed
     */
    public function getInProgressActivity()
    {
        return KpiActivityModel::find([
            'conditions' => 'status = :status: ',
            'bind'       => ['status' => KpiEnums::ACTIVITY_STATUS_ING],
            'columns'    => [
                'id',
                'name',
                'years',
                'period',
                'period_start',
                'period_end',
                'config',
                'created_at',
                'updated_at',
            ],
        ])->toArray();
    }

    /**
     * 查询登入者正在参与待确认的kpi活动数据
     * @param $loginUserId
     * @param $activityIds
     * @return mixed
     */
    public function getToBeConfirmedKpi($loginUserId, $activityIds)
    {
        return KpiActivityStaffModel::find([
            'conditions' => 'staff_id = :staff_id: AND activity_id IN ({activity_ids:array}) AND stage = :stage:',
            'bind'       => [
                'staff_id'     => $loginUserId,
                'activity_ids' => $activityIds,
                'stage'        => KpiEnums::STAGE_STAFF,
            ],
            'columns'    => [
                'id',
                'activity_id',
                'template_id',
                'staff_id',
                'leader_id',
                'leader_name',
                'stage',
                'is_send_leader',
                'is_change_leader',
                'submit_at',
                'confirm_at',
                'created_at',
                'updated_at',
            ],
            'order' => 'submit_at asc',
        ])->toArray();
    }

    /**
     * 验证参与kpi活动的员工是否有待确认的kpi，存在且到了限制打卡日期时，则限制打下班卡
     * @param $loginUserId
     * @param $activityList
     * @return array
     */
    public function verifyStaffToBeConfirmedKpi($loginUserId, $activityList, $curDate): array
    {
        $data             = [];
        $confirmedKpiList = $this->getToBeConfirmedKpi($loginUserId, array_keys($activityList));
        if (empty($confirmedKpiList)) {
            return $data;
        }
        foreach ($confirmedKpiList as $key => $val) {
            // 活动信息
            // 此处无需再次判断是否开启了打卡的限制，数据在进入这儿之前已经做了处理
            $activityInfo = $activityList[$val['activity_id']];
            // 上级提交kpi的时间加上活动设置中的截止天数
            $start    = date("Y-m-d", strtotime($val['submit_at']));
            $lastDate = date("Y-m-d", strtotime("+".(int)$activityInfo['staff_configs']['day']." days", strtotime($start)));
            //$lastDate   = "2023-01-06";
            // 如果当前日期与限制打卡的最后日期一致 或者 大于最后限制打卡的日期 则限制用户打卡。
            if (strtotime($curDate) >= strtotime($lastDate)) {
                $this->getDI()->get('logger')->write_log("staff_kpi_to_be_confirmed:".json_encode([
                        'activity_info' => $activityInfo,
                        'staff_id'      => $loginUserId,
                        'limit_info'    => $val,
                        'kpi_lase_date' => $lastDate,
                    ], JSON_UNESCAPED_UNICODE), 'info');
                $data['business_type']          = 'un_finished_study'; //'staff_kpi_to_be_confirmed';
                $data['training_detail']        = [
                    'message'       => $this->getTranslation()->_('staff_kpi_to_be_confirmed'),
                    'kpi_lase_date' => $lastDate,
                ];
                $backyard_kpi_url               = env('sign_url').'/#/KPIManagement';
                $data['training_detail']['url'] = $backyard_kpi_url."?activity_id={$val['activity_id']}"; // 跳转地址
                $data['activity_id'] = $val['activity_id'];
                $data['day'] = (int)$activityInfo['staff_configs']['day'];
                $data['start'] = $start;
                break;
            }
        }
        return $data;
    }

    /**
     * 根据活动id kpi制定人id检索推送记录
     * @param int $leaderId
     * @param array $activityIds
     * @return array
     */
    public function getKpiLeaderMsgSendLog(int $leaderId, array $activityIds,array $staffIds): array
    {
        if (empty($leaderId) || empty($activityIds) || empty($staffIds)) {
            return [];
        }
        return KpiLeaderMsgSendLogModel::find([
            'conditions' => 'leader_id = :leader_id: AND staff_id IN ({staff_ids:array}) AND activity_id IN ({activity_ids:array})',
            'bind'       => [
                'leader_id'    => $leaderId,
                'activity_ids' => $activityIds,
                'staff_ids'    => $staffIds
            ],
            'columns'    => [
                'id',
                'activity_id',
                'staff_id',
                'leader_id',
                'created_at',
                'updated_at',
            ],
            'group'      => 'activity_id, created_at',
            'order'      => 'created_at ASC',
        ])->toArray();
    }

    /**
     * 当前登入者为Kpi制定人的身份 是否存在未提交的kpi，如果存在且到了限制打卡的时间 则限制打下班卡
     * @return void
     */
    public function verifyLeaderFormulate($loginUserId, $activityList, $curDate): array
    {
        $data = [];
        // todo  需要确认当前leader下存不存在未提交的kpi，假如该员工都已经提交了，则不再限制
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(
            'kas.id,kas.activity_id, 
                kas.staff_id,  
                IF(kas.leader_id = 0,hsi.manger,kas.leader_id) AS leader_id,
                kas.stage'
        );
        $builder->from(['kas' => KpiActivityStaffModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, "hsi.staff_info_id = kas.staff_id", 'hsi');
        $builder->andWhere('kas.activity_id IN ({activity_ids:array})', ['activity_ids' => array_keys($activityList)]);
        $builder->andWhere('kas.stage = :stage:', ['stage' => KpiEnums::STAGE_LEADER]);
        $builder->andWhere('kas.leader_id = :leader_id: OR (hsi.manger = :leader_id: AND kas.leader_id = :default_leader_id:)', ['leader_id' => $loginUserId, "default_leader_id" => 0]);
        $waitPlanData = $builder->getQuery()->execute()->toArray();

        // 如果当leader不存在未指定的kpi计划时 则直接返回
        if (empty($waitPlanData)) {
            return $data;
        }

        // 拿到存在未指定计划的活动id
        $activity_ids = array_column($waitPlanData, 'activity_id');
        // 根据未指定kpi的员工和leader 去发送记录表中获取数据
        $staff_ids              = array_column($waitPlanData, 'staff_id');
        $leaderMsgSendLogFromDB = $this->getKpiLeaderMsgSendLog($loginUserId, $activity_ids, $staff_ids);
        // 如果没有发送记录, 则直接返回
        if (empty($leaderMsgSendLogFromDB)) {
            return $data;
        }

        // 存在待制定计划的活动id
        //$waitPlanActivityIds = array_values(array_unique(array_column($leaderMsgSendLogFromDB, 'activity_id')));
        // db 中存储的是Y-m-d H:i:s ,会出现同一天不同批次的数据，sql为了防止慢查询没有使用格式化日期的函数，在这里做时间的处理
        foreach ($leaderMsgSendLogFromDB as $key => $logInfo) {
            if (empty($logInfo['created_at'])) {
                continue;
            }

            // 如果该活动已经发起了，且存在未制定完成计划的数据时
            if (in_array($logInfo['activity_id'], $activity_ids)) {
                $createdAt  = $logInfo['created_at'];
                $createDate = explode(' ', $createdAt)[0];
                // 活动信息
                // 此处不需要再次判断是否开启打卡限制，数据已经在进入前，做了处理
                $activityInfo = $activityList[$logInfo['activity_id']];
                $start        = $createDate; //$val['create_date'];//date("Y-m-d", strtotime($val['created_at']));
                $lastDate     = date("Y-m-d",
                    strtotime("+".(int)$activityInfo['leader_configs']['day']." days", strtotime($start)));
                // 如果当前日期与限制打卡的最后日期一致 或者 大于最后限制打卡的日期 则限制用户打卡。
                if (strtotime($curDate) >= strtotime($lastDate)) {
                    $this->getDI()->get('logger')->write_log("leader_kpi_to_be_submitted:".json_encode([
                            'activity_info' => $activityInfo,
                            'staff_id'      => $loginUserId,
                            'log_info'      => $logInfo,
                            'kpi_last_date' => $lastDate,
                        ], JSON_UNESCAPED_UNICODE), 'info');
                    $data['business_type'] = 'un_remittance'; //'un_finished_task';
                    $data['remittance_detail']   = [
                        'dialog_msg'    => $this->getTranslation()->_('leader_kpi_to_be_submitted'),
                        'dialog_status' => 1,
                        'dialog_must_status' => 1,
                        'is_ces_tra' => 0,
                        'kpi_last_date' => $lastDate,
                        'activity_id'   => $logInfo['activity_id'],
                        'day'           => (int)$activityInfo['leader_configs']['day'],
                        'start'         => $start
                    ];
                    break;
                }
            }
        }
        return $data;
    }

    /**
     * 验证登入用户的kpi是否已经完成 note：分两种角色求处理 1 直线上级、kpi制定人 2 参与kpi活动的员工
     * @return void
     */
    public function verifyKpiIsCompleted($userInfo)
    {
        $returnData = [];
        // 登入者的员工id
        $loginUserId = $userInfo['staff_id'];

        // [1] 查询正在进行中的活动
        $activityResult = $this->getInProgressActivity();
        // 如果没有正在进行中的活动 则不限制打卡，直接返回
        if (empty($activityResult)) {
            return $returnData;
        }
        //$activityList = array_column($activityResult, null, 'id');
        $leaderActivityList = [];
        $staffActivityList = [];
        // 在所有进行中的活动中取到 开启限制打卡的数据，过滤掉无用数据
        foreach ($activityResult AS $key => $activityInfo) {
            // 解析活动的配置
            $activityConfig = json_decode($activityInfo['config'], true);
            // 解析失败时跳过
            if (json_last_error() != JSON_ERROR_NONE) {
                continue;
            }
            $leaderTimeNode = $activityConfig['time_node']['leader'];
            $staffTimeNode = $activityConfig['time_node']['staff'];
            // 开启了限制leader身份的打卡限制
            if ($leaderTimeNode["on-off"] == KpiEnums::ACTIVITY_TIME_NODE_ON) {
                $leaderActivityList[$activityInfo['id']] = $activityInfo;
                $leaderActivityList[$activityInfo['id']]['leader_configs'] = $leaderTimeNode;
            }

            // 开启了限制员工身份的打卡限制
            if ($staffTimeNode["on-off"] ==  KpiEnums::ACTIVITY_TIME_NODE_ON) {
                $staffActivityList[$activityInfo['id']] = $activityInfo;
                $staffActivityList[$activityInfo['id']]['staff_configs'] = $staffTimeNode;
            }
        }

        $curDate = date("Y-m-d");
        // [2] 当前登入者为Kpi制定人的身份
        if (!empty($leaderActivityList)) {
            $verifyLeaderFormulateRet = $this->verifyLeaderFormulate($loginUserId, $leaderActivityList, $curDate);
            if (!empty($verifyLeaderFormulateRet)) {
                return $verifyLeaderFormulateRet;
            }
        }

        // [3] 当前登入者为参与kpi活动的身份
        if (!empty($staffActivityList)) {
            $verifyStaffToBeConfirmedRet = $this->verifyStaffToBeConfirmedKpi($loginUserId, $staffActivityList, $curDate);
            if (!empty($verifyStaffToBeConfirmedRet)) {
                return $verifyStaffToBeConfirmedRet;
            }
        }

        return $returnData;
    }

}