<?php
namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\AttendanceEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffPickupDeliveryDataModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkDetectFaceRecordModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use Ramsey\Uuid\Provider\Node\RandomNodeProvider;
use Ramsey\Uuid\Uuid;
class AttendanceDetectCheatServer extends BaseServer
{

    const SYNC_FMS_FACE_CHECK = 1; //外协作弊
    const SYNC_FMS_LIVE_CHECK = 2; //外协活体


    public function getOsCheatData($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.id,s.staff_info_id,s.organization_id,s.attendance_date,s.match_staff_info_id,
            s.os_face_image_source_path,s.os_submit_face_image_path,s.work_attendance_path,s.state,
            s.created_at,s.updated_at
        ");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->innerJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = s.staff_info_id', 'hsi');

        $builder->where(' s.type = :type: ', ['type'  => StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS,]);

        if ($params['biz_type'] == self::SYNC_FMS_FACE_CHECK) {
            $builder->andWhere('s.state = :state: ', ['state' => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_DETECTED,]);
        }
        if ($params['biz_type'] == self::SYNC_FMS_LIVE_CHECK) {
            $builder->andWhere('s.live_score is not null and  s.live_score < :live_score: ', ['live_score' => '0.7',]);
        }
        if (!empty($params['start_time'])) {
            $builder->andWhere('s.created_at > :start_time:', ['start_time' => $params['start_time']]);
        }
        if (!empty($params['end_time'])) {
            $builder->andWhere('s.created_at <= :end_time:', ['end_time' => $params['end_time']]);
        }
        if (!empty($params['need_hire_type'])) {
            $builder->andWhere('hsi.hire_type = :need_hire_type:', ['need_hire_type' => $params['need_hire_type']]);
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 外协作弊同步到fms
     * @param $biz_type
     * @param $date_at
     * @return bool
     */
    public function syncCrowdSourcingCheatToFMS($biz_type, $date_at): bool
    {
        $params['start_time']     = date('Y-m-d 00:00:00', strtotime($date_at));
        $params['end_time']       = date("Y-m-d 00:00:00", strtotime($params['start_time'] . " +1 day"));
        $params['need_hire_type'] = HrStaffInfoModel::HIRE_TYPE_12;
        $params['biz_type']       = $biz_type;
        $data                     = $this->getOsCheatData($params);
        if (empty($data)) {
            $this->logger->write_log(['syncCrowdSourcingCheatToFMS' => 'nodata'], 'info');
            return true;
        }
        $sendData = [];
        foreach ($data as $datum) {
            $sendData[$datum['staff_info_id']][] = [
                'pic_url'       => $datum['os_submit_face_image_path'],
                'check_face_at' => strtotime(($datum['created_at'])),
            ];
        }
        $api = new RestClient('fms');
        foreach ($sendData as $staff_id => $item) {
            $send_params['type']              = $biz_type;
            $send_params['external_staff_id'] = $staff_id;
            $send_params['face_pic_list']     = $item;
            $res                              = $api->execute(RestClient::METHOD_POST,
                '/svc/driver/crowdsourcing/task/suspected/false',
                $send_params, ['Accept-Language' => $this->lang]);
            $this->logger->write_log(['send_params' => $send_params, 'res' => $res], 'info');
        }
        return true;
    }


    /**
     * 有效的支援数据
     * @param $date
     * @param $staffIds
     * @return array
     */
    public function supportInfo($date, $staffIds): array
    {
        if (empty($staffIds)) {
            return [];
        }

        return HrStaffApplySupportStoreModel::find([
            'columns'    => 'id, staff_info_id, sub_staff_info_id',
            'conditions' => '(staff_info_id in ({ids:array}) or sub_staff_info_id in ({ids:array})) and employment_begin_date <= :date_at: 
                            and employment_end_date >= :date_at: and support_status in ({support_status:array})',
            'bind'       => [
                'ids'            => $staffIds,
                'date_at'        => $date,
                'support_status' => [
                    HrStaffApplySupportStoreModel::SUPPORT_STATE_EFFECTIVE,
                    HrStaffApplySupportStoreModel::SUPPORT_STATE_INVALID,
                ],
            ],
        ])->toArray();
    }


    //轮休信息
    public function workdayInfo($date, $staffIds): array
    {
        if (empty($staffIds)) {
            return [];
        }

        $data = HrStaffWorkDayModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'staff_info_id in ({ids:array}) and date_at = :date_at:',
            'bind'       => ['ids' => $staffIds, 'date_at' => $date],
        ])->toArray();
        return empty($data) ? [] : array_column($data, 'staff_info_id');
    }

    /**
     * 揽派件数据
     * @param $date_at
     * @param $allStaffIds
     * @return array
     */
    public function getPickupDeliveryData($date_at, $allStaffIds): array
    {
        if (empty($allStaffIds)) {
            return [];
        }

        //揽派件信息 所有员工的
        $parcelNum = StaffPickupDeliveryDataModel::find([
            'columns'    => 'staff_info_id, (pickup_count + fh_pickup_count + delivery_count) as num',
            'conditions' => 'staff_info_id in ({ids:array}) and stat_date = :date_at:',
            'bind'       => [
                'ids'     => $allStaffIds,
                'date_at' => $date_at,
            ],
        ])->toArray();
        return empty($parcelNum) ? [] : array_column($parcelNum, 'num', 'staff_info_id');
    }
    /**
     * 获取员工作弊检测记录数据
     * @param $params
     * @return array
     */
    public function getDetectFaceRecordData($params): array
    {
        $result['list']  = $this->getDetectFaceRecordList($params);
        $result['total'] = $this->getDetectFaceRecordCount($params);
        return $result;
    }

    /**
     * 获取作弊检测记录列表
     * @param $params
     * @return array
     */
    protected function getDetectFaceRecordList($params): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.staff_info_id, s.organization_id, s.attendance_date, 
            s.os_face_image_source_path, s.os_submit_face_image_path, 
            s.state,s.created_at, s.live_score, hsi.hire_type 
        ");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = s.staff_info_id', 'hsi');

        $this->buildDetectFaceRecordConditions($builder, $params);

        // 分页
        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $size = isset($params['size']) ? (int)$params['size'] : 20;
        $offset = ($page - 1) * $size;

        $builder->limit($size, $offset);
        $builder->orderBy('s.id asc');
        $t = $this->getTranslation('zh');
        $list =  $builder->getQuery()->execute()->toArray();
        $sort_box= [];
        foreach ($list as $key => &$item) {
            switch ($item['state']) {
                case StaffWorkDetectFaceRecordModel::MATCH_STATE_NOT_MATCH:
                    $item['state'] = '未匹配到正式员工';
                    break;
                case StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_MATCH:
                    $item['state'] = '匹配到正式员工';
                    break;
                case StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_DETECTED:
                    $item['state'] = '2小时检测与底片不符';
                    break;
               default:
                    $item['state'] = '';
                    break;
            }
            $item['hire_type'] = $t->_('hire_type_'.$item['hire_type']);
            //处理一下每天数据的次数 从1开始
            @$sort_box[$item['staff_info_id'] . '_' . $item['attendance_date']] += 1;
            $item['order_num'] = $sort_box[$item['staff_info_id'] . '_' . $item['attendance_date']];
        }
        return $list;
    }

    /**
     * 获取作弊检测记录总数
     * @param $params
     * @return int
     */
    protected function getDetectFaceRecordCount($params): int
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("COUNT(*) as total");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);

        $this->buildDetectFaceRecordConditions($builder, $params);

        $result = $builder->getQuery()->execute()->getFirst();
        return (int)$result['total'];
    }

    /**
     * 构建查询条件
     * @param $builder
     * @param $params
     */
    protected function buildDetectFaceRecordConditions($builder, $params)
    {
        if (!empty($params['staff_info_id'])) {
            $builder->andWhere('s.staff_info_id = :staff_info_id:', ['staff_info_id' => $params['staff_info_id']]);
        }

        if (!empty($params['type'])) {
            $builder->inWhere('s.type', is_array($params['type']) ? $params['type'] : [$params['type']]);
        }

        if (!empty($params['check_live_score'])) {
            $builder->andWhere('s.live_score <= :live_score:', ['live_score' => $params['check_live_score']]);
        }

        if (!empty($params['state'])) {
            $builder->andWhere('s.state = :state:', ['state' => $params['state']]);
        }

        if (!empty($params['start_date'])) {
            $builder->andWhere('s.attendance_date >= :start_date:', ['start_date' => $params['start_date']]);
        }

        if (!empty($params['end_date'])) {
            $builder->andWhere('s.attendance_date <= :end_date:', ['end_date' => $params['end_date']]);
        }

        if (!empty($params['attendance_date'])) {
            $builder->andWhere('s.attendance_date = :attendance_date:', ['attendance_date' => $params['attendance_date']]);
        }
    }



    /**
     * 获取2小时作弊检测记录列表
     * @param $params
     * @return array
     */
    public function getCycleFaceCheckRecord($params): array
    {
        $list          = $this->getCycleFaceCheckRawDataList($params);
        $total         = $this->getCycleFaceCheckRawDataCount($params);
        $processedList = $this->processCycleFaceCheckData($list);

        return [
            'list'  => $processedList,
            'total' => $total,
        ];
    }

    /**
     * 获取原始数据（支持分页）
     * @param $params
     * @return array
     */
    protected function getCycleFaceCheckRawData($params): array
    {
        $list  = $this->getCycleFaceCheckRawDataList($params);
        $total = $this->getCycleFaceCheckRawDataCount($params);

        return [
            'list' => $list,
            'total' => $total
        ];
    }

    /**
     * 获取原始数据列表
     * @param $params
     * @return array
     */
    protected function getCycleFaceCheckRawDataList($params): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'r.staff_info_id',
            'r.attendance_date',
            'r.created_at',
            'r.os_submit_face_image_path',
            'r.os_face_image_source_path',
            'a.started_clientid',
            'a.started_path',
            'a.end_clientid',
            'a.end_path',
        ]);

        $builder->from(['r' => StaffWorkDetectFaceRecordModel::class]);
        $builder->innerJoin(
            StaffWorkAttendanceModel::class,
            'r.staff_info_id = a.staff_info_id AND r.attendance_date = a.attendance_date',
            'a'
        );

        $builder= $this->buildCycleFaceCheckConditions($builder, $params);

        // 分页
        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $size = isset($params['size']) ? (int)$params['size'] : 20;
        $offset = ($page - 1) * $size;

        $builder->limit($size, $offset);
        $builder->orderBy('r.id DESC');

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取原始数据总数
     * @param $params
     * @return int
     */
    protected function getCycleFaceCheckRawDataCount($params): int
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("COUNT(*) as total");
        $builder->from(['r' => StaffWorkDetectFaceRecordModel::class]);
        $builder->innerJoin(
            StaffWorkAttendanceModel::class,
            'r.staff_info_id = a.staff_info_id AND r.attendance_date = a.attendance_date',
            'a'
        );
        $this->buildCycleFaceCheckConditions($builder, $params);

        $result = $builder->getQuery()->execute()->getFirst();
        return (int)$result['total'];
    }

    /**
     * 构建查询条件
     * @param $builder
     * @param $params
     */
    protected function buildCycleFaceCheckConditions($builder, $params)
    {
        $builder->where('r.state = :state:', ['state' => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_DETECTED]);
        $builder->andWhere('r.type IN ({type:array})', [
            'type' => [
                StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS,
                StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_AGENT_CYCLE
            ]
        ]);
        $builder->andWhere('r.attendance_date >= :start_date: AND r.attendance_date <= :end_date:', [
            'start_date' => $params['start_date'],
            'end_date' => $params['end_date']
        ]);
        return $builder;
    }

    /**
     * 处理2小时作弊检测数据
     * @param array $rawData
     * @return array
     */
    protected function processCycleFaceCheckData(array $rawData): array
    {
        $t          = $this->getTranslation('zh');
        $returnData = [];

        $staffServer = new StaffServer();
        $storeServer = new SysStoreServer();
        $server      = new AttendanceServer('zh-CN', $this->timeZone);
        $source      = AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_SAVE_ATTACHMENT;
        $countryCode = $server->getCountryCode($source);
        $groupIdList = $server->getRegionList();

        foreach ($rawData as $k => $v) {
            $record = $this->initializeCycleFaceCheckRecord($v);
            $record = $this->processFaceComparison($record, $v, $countryCode, $groupIdList);
            if (empty($record)) {
                continue;
            }
            $record = $this->processStaffInfo($record, $v, $staffServer, $storeServer, $t);

            $returnData[] = $record;
        }

        return $returnData;
    }

    /**
     * 初始化记录数据
     * @param array $v
     * @return array
     */
    protected function initializeCycleFaceCheckRecord(array $v): array
    {
        $img_prefix = env('img_prefix') ;
        return [
            'staff_info_id'               => $v['staff_info_id'],
            'main_staff_info_id'          => StaffRepository::getMasterStaffIdBySubStaff($v['staff_info_id']) ?: null,
            'attendance_date'             => $v['attendance_date'],
            'detection_time'              => date('Y-m-d H:i:s', strtotime($v['created_at'])),
            'cheat_check_submitted_image' => $v['os_submit_face_image_path'],
            'cheat_detection_base_image'  => $v['os_face_image_source_path'],
            'clock_in_device'             => $v['started_clientid'],
            'clock_in_photo'              => $v['started_path']? ( $img_prefix. $v['started_path']) : '',
            'clock_out_device'            => $v['end_clientid'],
            'clock_out_photo'             => $v['end_path'] ? ($img_prefix . $v['end_path']) : '',
            // 初始化为null的字段
            'staff_type'                  => null,
            'employment_status'           => null,
            'employment_type'             => null,
            'department'                  => null,
            'position'                    => null,
            'branch'                      => null,
            'piece'                       => null,
            'region'                      => null,
            'match_staff_id'              => null,
            'match_staff_type'            => null,
            'match_employment_status'     => null,
            'match_employment_type'       => null,
            'match_department'            => null,
            'match_position'              => null,
            'match_branch'                => null,
            'match_piece'                 => null,
            'match_region'                => null,
            'match_clock_in_photo'        => null,
            'match_clock_in_device'       => null,
            'match_clock_out_photo'       => null,
            'match_clock_out_device'      => null,
        ];
    }

    /**
     * 处理人脸比对
     * @param array $record
     * @param array $v
     * @param string $countryCode
     * @param array $groupIdList
     * @return array
     */
    protected function processFaceComparison(array $record, array $v, string $countryCode, array $groupIdList): array
    {
        $staff1 = $v['staff_info_id'];

        // 作弊检查比对
        [$res, $distance] = $this->checkStaffByUrlV2('', $staff1, $v['os_submit_face_image_path'], $v['os_face_image_source_path']);
        $record['cheat_check_is_same_person'] = $res;
        $record['cheat_check_difference_value'] = $distance;

        // 上班打卡比对
        [$start_res, $distance] = $this->checkStaffByUrlV2('', $staff1, $v['os_submit_face_image_path'], $record['clock_in_photo']);
        $record['clock_in_vs_cheat_check_same_person'] = $start_res;
        $record['clock_in_vs_cheat_check_difference_value'] = $distance;

        // 下班打卡比对
        [$end_res, $distance] = $this->checkStaffByUrlV2('', $staff1, $v['os_submit_face_image_path'], $record['clock_out_photo']);
        $record['clock_out_vs_cheat_check_same_person'] = $end_res;
        $record['clock_out_vs_cheat_check_difference_value'] = $distance;

        // 如果上班或下班比对结果为"是"，跳过后续处理
        if ($start_res == '是' || $end_res == '是') {
            return [];
        }

        // 进行AI人脸匹配
        $matchStaffInfoId = $this->performFaceMatching($v, $countryCode, $groupIdList);
        if ($matchStaffInfoId) {
            $record['match_staff_id'] = $matchStaffInfoId;
        }

        return $record;
    }

    /**
     * 执行人脸匹配
     * @param array $v
     * @param string $countryCode
     * @param array $groupIdList
     * @return int|null
     */
    protected function performFaceMatching(array $v, string $countryCode, array $groupIdList): ?int
    {
        // 生成唯一ID
        $nodeProvider = new RandomNodeProvider();
        $uuid = Uuid::uuid1($nodeProvider->getNode(), mt_rand(1, 16000))->toString();

        $params = [
            "url" => $v['os_submit_face_image_path'],
            "request_id" => $uuid,
            "country" => $countryCode,
            "group_id_list" => $groupIdList,
        ];

        $result = AiServer::getInstance()->setConfig(enums::IDENTIFY_ANALYZE_SEARCH_FACE)->sendEx(json_encode($params), $v['staff_info_id']);

        if (empty($result)) {
            $this->logger->write_log(['2小时作弊根据人脸匹配工号异常' => $params]);
            return null;
        }

        // score字段大于等于93分，flash_hr_exist = true
        if (isset($result['result']['flash_hr_exist']) &&
            $result['result']['flash_hr_exist'] &&
            $result['result']['person_id'] != $v['staff_info_id']) {
            return $result['result']['person_id'];
        }

        return null;
    }

    /**
     * 处理员工信息
     * @param array $record
     * @param array $v
     * @param $staffServer
     * @param $storeServer
     * @param $t
     * @return array
     */
    protected function processStaffInfo(array $record, array $v, $staffServer, $storeServer, $t): array
    {
        $staff1 = $v['staff_info_id'];
        $tmpStaff = [$staff1];

        // 如果有匹配的员工ID，添加到查询列表
        if (!empty($record['match_staff_id'])) {
            $tmpStaff[] = $record['match_staff_id'];
        }

        $staffInfos = $staffServer->getStaffInfoList($tmpStaff);

        // 处理原员工信息
        $record = $this->fillStaffInfo($record, $staffInfos[$staff1], $storeServer, $t);

        // 处理匹配员工信息
        if (!empty($record['match_staff_id'])) {
            $matchStaffInfoId = $record['match_staff_id'];

            // 检查匹配员工状态
            if ($staffInfos[$matchStaffInfoId]['state'] == HrStaffInfoModel::STATE_2) {
                return $record;
            }

            $record = $this->fillMatchStaffInfo($record, $staffInfos[$matchStaffInfoId], $storeServer, $t);
            $record = $this->fillMatchAttendanceInfo($record, $matchStaffInfoId, $v['attendance_date']);
        }

        return $record;
    }

    /**
     * 填充员工基本信息
     * @param array $record
     * @param array $staffInfo
     * @param $storeServer
     * @param $t
     * @return array
     */
    protected function fillStaffInfo(array $record, array $staffInfo, $storeServer, $t): array
    {
        $staffState = $staffInfo['state'] == 1 && $staffInfo['wait_leave_state'] == 1 ? 4 : $staffInfo['state'];

        $record['staff_type']        = $t->_('formal_' . $staffInfo['formal']);
        $record['employment_status'] = $t->_('hris_working_state_' . $staffState);
        $record['employment_type']   = $staffInfo['hire_type'] ? $t->_('hire_type_' . $staffInfo['hire_type']) : '';
        $record['department']        = $staffInfo['department_name'];
        $record['position']          = $staffInfo['job_name'];
        $record['branch']            = $staffInfo['store_name'];

        $storeInfo        = $storeServer->getStorePieceAndRegionInfo($staffInfo['sys_store_id']);
        $record['piece']  = $storeInfo['piece_name'];
        $record['region'] = $storeInfo['region_name'];

        return $record;
    }

    /**
     * 填充匹配员工信息
     * @param array $record
     * @param array $matchStaffInfo
     * @param $storeServer
     * @param $t
     * @return array
     */
    protected function fillMatchStaffInfo(array $record, array $matchStaffInfo, $storeServer, $t): array
    {
        $matchStaffState = $matchStaffInfo['state'] == 1 && $matchStaffInfo['wait_leave_state'] == 1 ? 4 : $matchStaffInfo['state'];

        $record['match_staff_type'] = $t->_('formal_' . $matchStaffInfo['formal']);
        $record['match_employment_status'] = $t->_('hris_working_state_' . $matchStaffState);
        $record['match_employment_type'] = $matchStaffInfo['hire_type'] ? $t->_('hire_type_' . $matchStaffInfo['hire_type']) : '';
        $record['match_department'] = $matchStaffInfo['department_name'];
        $record['match_position'] = $matchStaffInfo['job_name'];
        $record['match_branch'] = $matchStaffInfo['store_name'];

        $storeInfo = $storeServer->getStorePieceAndRegionInfo($matchStaffInfo['sys_store_id']);
        $record['match_piece'] = $storeInfo['piece_name'];
        $record['match_region'] = $storeInfo['region_name'];

        return $record;
    }

    /**
     * 填充匹配员工考勤信息
     * @param array $record
     * @param int $matchStaffInfoId
     * @param string $attendanceDate
     * @return array
     */
    protected function fillMatchAttendanceInfo(array $record, int $matchStaffInfoId, string $attendanceDate): array
    {
        $attendanceInfo = StaffWorkAttendanceModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and attendance_date = :attendance_date:',
            'bind' => ['staff_info_id' => $matchStaffInfoId, 'attendance_date' => $attendanceDate],
        ]);

        if (!empty($attendanceInfo)) {
            $attendanceInfo = $attendanceInfo->toArray();
            $record['match_clock_in_photo'] = $attendanceInfo['started_path'] ? env('img_prefix') . $attendanceInfo['started_path'] : '';
            $record['match_clock_in_device'] = $attendanceInfo['started_clientid'];
            $record['match_clock_out_photo'] = $attendanceInfo['end_path'] ? env('img_prefix') . $attendanceInfo['end_path'] : '';
            $record['match_clock_out_device'] = $attendanceInfo['end_clientid'];
        }

        return $record;
    }

    public function checkStaffByUrlV2($date,$staff1, $currentUrl1, $currentUrl2)
    {
        $verifyServer = new \FlashExpress\bi\App\Server\AttendanceImageVerifyServer('zh',$this->timeZone);

        if (empty($currentUrl1) || empty($currentUrl2)) {
            return ['照片不全',''];
        }
        $res = $verifyServer->internalAiInterFace($staff1, $currentUrl1, $currentUrl2);
        if ($res['score'] == 100) {
            if ($res['distance'] <= 0.7) {
                return ['是', $res['distance']];
            } elseif ($res['distance'] <= 1.2) {
                return ['疑似', $res['distance']];
            } else {
                return ['可能不是', $res['distance']];
            }
        }
        return ['否', $res['distance']??''];
    }
}