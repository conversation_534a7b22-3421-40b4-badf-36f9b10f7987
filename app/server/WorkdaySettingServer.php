<?php
/**
 * Created by PhpStor<PERSON>.
 * User: nick
 * Date: 2021/1/5
 * Time: 2:36 PM
 */


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageStoreCategoryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\WorkdaySettingDailyDetailModel;
use FlashExpress\bi\App\Models\backyard\WorkdaySettingV2Model;
use Phalcon\Mvc\Model;

class WorkdaySettingServer extends BaseServer
{

    const JOB_TYPE_WAREHOUSE = 1; // 仓管员
    const JOB_TYPE_COURIER = 2;   // 快递员
    const JOB_TYPE_DAY_DRIVER = 3; // 白班司机
    const JOB_TYPE_NIGHT_DRIVER = 4; // 夜班司机

    /**
     * 生效中的自由轮休配置
     * @param $store_id
     * @param $job_title
     */
    public function getFreeLeaveConfig($store_id, $job_title)
    {
        if (empty($store_id) || empty($job_title)) {
            return false;
        }
        if($store_id == enums::HEAD_OFFICE_ID){
            return false;
        }
       return WorkdaySettingV2Model::findFirst([
            'conditions' => 'store_id = :store_id: and job_title = :job_title: and is_delete = 0 and (max_days_num > 0 or max_days_num is null) and if(ISNULL(end_date),true,end_date != effect_date)  and  (end_date >= :end_date: or end_date is null) and effect_date <= :effect_date:',
            'bind'       => [
                'store_id'   => $store_id,
                'job_title'   => $job_title,
                'end_date'    => date('Y-m-d'),
                'effect_date' => date('Y-m-d'),
            ],
            'order'      => 'effect_date desc',
        ]);
    }
    /**
     * 获取下级
     * @param $staff_info_id
     * @return array
     */
    protected function getManageStaff($staff_info_id): array
    {
        if (empty($staff_info_id)) {
            return [];
        }
        //上周日
        $lastSunday = date('Y-m-d', strtotime('last Sunday'));
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->join(HrJobTitleModel::class, 'j.id = s.job_title', 'j');
        $builder->andWhere("s.manger = :staff_id:", ['staff_id' => $staff_info_id]);
        $builder->andWhere("s.sys_store_id != '-1' and s.hire_date <= :hire_date:", ['hire_date' =>$lastSunday]);
        $builder->andWhere("s.state in (1,3) and formal in (1,4) and is_sub_staff = 0");
        $builder->andWhere("s.week_working_day = :week_working_day:",
            ['week_working_day' => HrStaffInfoModel::WEEK_WORKING_DAY_FREE]);
        $builder->columns(
            "s.staff_info_id,s.name,j.job_name, s.sys_store_id ,s.job_title, s.hire_date"
        );
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 检查自由轮休设置
     * @param $staffInfo
     * @return array
     */
    public function checkWorkdaySetting($staffInfo): array
    {
        //是否符合配置
        $settingEnv = (new SettingEnvServer())->listByCode([
            'White_List_SetFreeRest',
            'SetFreeRest_Position',
            'suggestion_mode',
            'TruckDriverNight_Manager',
        ]);
        $settingEnv = array_column($settingEnv, 'set_val', 'code');
        $switch     = empty($settingEnv['suggestion_mode']) ? 0 : $settingEnv['suggestion_mode'];
        $white      = empty($settingEnv['White_List_SetFreeRest']) ? [] : explode(',', $settingEnv['White_List_SetFreeRest']);
        $job        = empty($settingEnv['SetFreeRest_Position']) ? [] : explode(',', $settingEnv['SetFreeRest_Position']);
        $truckStaff = empty($settingEnv['TruckDriverNight_Manager']) ? [] : explode(',', $settingEnv['TruckDriverNight_Manager']);
        if (empty($switch) || $switch == 0) {
            return [];
        }
        //不限制打卡 白名单 White_List_SetFreeRest
        if (empty($job) && empty($truckStaff)) {
            return [];
        }

        //是否再配置职位范围内 SetFreeRest_Position
        if (!empty($white) && in_array($staffInfo['staff_id'], $white)) {
            return [];
        }

        //不属于两个配置
        if (!in_array($staffInfo['job_title'], $job) && !in_array($staffInfo['staff_id'], $truckStaff)) {
            return [];
        }

        // 根据 suggestion_mode 执行不同的逻辑
        $result = [];
        if ($switch == 1) {//按日配置
            //拆分 夜班司机 获取 管辖网点 的未来1天的配置数据 优先验证
            if(in_array($staffInfo['staff_id'], $truckStaff)){
                $result = $this->checkTruckSetting($staffInfo);
            }else{
                // 使用新的 RPC 验证逻辑 非夜班司机 用原来的 3天的 并且要剔除 夜班司机的逻辑
                $result = $this->checkDailySetting($staffInfo);
            }
        }
        //原逻辑
        if ($switch == 2) {//按周配置
            $result = $this->checkWeekSetting($staffInfo);
        }
        return $result;
    }

    public function checkWeekSetting($staffInfo)
    {
        //是否是周1，2，3
        $week    = date('w');
        $weekArr = [1, 2, 3];
        if (get_runtime() != 'pro') {
            $weekArr = [1, 2, 3, 4, 5];
        }
        if (!in_array($week, $weekArr)) {
            return [];
        }
        $allStaff = $this->getManageStaff($staffInfo['staff_id']);
        // suggestion_mode 为 2 或其他值，使用现有逻辑


        //下级员工 在职 并且是自由轮休 可能有多个网点
        if (empty($allStaff)) {
            return [];
        }
        $workdaySetting = $this->workdaySettingLogic($allStaff);

        if (empty($workdaySetting['staff_list'])) {
            return [];
        }
        $t = $this->getTranslation();
        $jump_url                 = 'rest-setting';
        $base_url                 = env('h5_endpoint');
        $result['business_type']  = 'jump_h5';
        $result['jump_h5_detail'] = [
            'dialog_status'      => 1,
            'dialog_must_status' => 1,
            'dialog_msg'         => $t->_('21841_workday_setting_message'),
            'dialog_btn_msg'     => $t->_('21841_workday_setting_btn'),
            'dialog_jump_url'    => $base_url . $jump_url,
        ];
        return $result;
    }

    /**
     * 验证逻辑
     * @param $allStaff
     * @return array
     */
    public function workdaySettingLogic($allStaff): array
    {
        $date_at  = date('Y-m-d');
        $storeIds = array_unique(array_column($allStaff, 'sys_store_id')); //所有网点id
        $staffIds = array_column($allStaff, 'staff_info_id');//所有工号
        $weekStart = weekStart();
        $weekEnd   = weekEnd();

        $offDayData  = $this->getOffDayData($staffIds, $weekStart, $weekEnd);
        $leaveData   = $this->getStaffLeaveData($staffIds, $weekStart, $weekEnd);
        $supportData = $this->getSupportData($staffIds, $weekStart, $weekEnd);

        $workdaySettingMap = $this->getWorkdaySetting($storeIds, $date_at);

        if (empty($workdaySettingMap)) {
            return [];
        }
        $needStaff = [];
        foreach ($allStaff as $staff) {
            if (!isset($workdaySettingMap[$staff['sys_store_id'] . '_' . $staff['job_title']])) {
                continue;
            }
            $workdaySetting      = $workdaySettingMap[$staff['sys_store_id'] . '_' . $staff['job_title']];
            $staffAllJumpDateNum = $this->countStaffDate($staff['staff_info_id'], $offDayData, $leaveData,
                $supportData);
            $staff['all_jump_date_num'] = $staffAllJumpDateNum;
            $staff['off_data'] = $offDayData[$staff['staff_info_id']] ?? [];
            $staff['leave_data'] = $leaveData[$staff['staff_info_id']] ?? [];
            $staff['support_data'] = $supportData[$staff['staff_info_id']] ?? [];
            if (!empty($workdaySetting['min_days_num']) && $staffAllJumpDateNum < $workdaySetting['min_days_num']) {
                $needStaff[$staff['staff_info_id']] = $staff;
            }

            if (!empty($workdaySetting['max_days_num']) && count($offDayData[$staff['staff_info_id']] ?? []) > $workdaySetting['max_days_num']) {
                $needStaff[$staff['staff_info_id']] = $staff;
            }
        }
        $configList = [];
        foreach ($workdaySettingMap as $item) {
            $configList[] = [
                'store_id'  => $item['store_id'],
                'job_title' => $item['job_title'],
                'min'       => $item['min_days_num'],
                'max'       => $item['max_days_num'],
            ];
        }


        return ['staff_list' => array_values($needStaff), 'config_list' => $configList];
    }

    /**
     * 汇总员工off leave support数据
     * @param $staff_info_id
     * @param $offDayData
     * @param $leaveData
     * @param $supportData
     * @return int
     */
    private function countStaffDate($staff_info_id, $offDayData, $leaveData, $supportData): int
    {
        $date = [];
        if (isset($offDayData[$staff_info_id])) {
            $date = array_merge($date, $offDayData[$staff_info_id]);
        }
        if (isset($leaveData[$staff_info_id])) {
            $date = array_merge($date, $leaveData[$staff_info_id]);
        }
        if (isset($supportData[$staff_info_id])) {
            $date = array_merge($date, $supportData[$staff_info_id]);
        }
        return count(array_unique($date));
    }


    /**
     * 获取轮休配置
     * @param $storeIds
     * @param $date_at
     * @return array
     */
    protected function getWorkdaySetting($storeIds, $date_at): array
    {
        $list = WorkdaySettingV2Model::find([
            'conditions' => 'store_id in ({store_ids:array})  and is_delete = 0 and (max_days_num > 0 or max_days_num is null) and if(ISNULL(end_date),true,end_date != effect_date)  and  (end_date >= :end_date: or end_date is null) and effect_date <= :effect_date:',
            'bind'       => [
                'store_ids'   => $storeIds,
                'end_date'    => $date_at,
                'effect_date' => $date_at,
            ],
            'order'      => 'effect_date desc',
        ])->toArray();

        if (empty($list)) {
            return [];
        }
        $result = [];
        foreach ($list as $item) {
            //取最新的
            if (!isset($result[$item['store_id'] . '_' . $item['job_title']])) {
                $result[$item['store_id'] . '_' . $item['job_title']] = [
                    'max_days_num' => $item['max_days_num'],
                    'min_days_num' => $item['days_num'],
                    'store_id'     => $item['store_id'],
                    'job_title'    => $item['job_title'],
                ];
            }
        }
        return $result;
    }


    /**
     * 获取支援信息
     * @param $staffIds
     * @param $startDate
     * @param $endDate
     * @return array
     */
    protected function getSupportData($staffIds, $startDate, $endDate)
    {
        $supportList = HrStaffApplySupportStoreModel::find([
            'columns'    => 'staff_info_id,employment_begin_date,employment_end_date',
            'conditions' => 'staff_info_id in ({staff_info_id:array}) and support_status in (1,2,3)  and  !(employment_begin_date > :end_date: or employment_end_date < :effect_date:)  ',
            'bind'       => ['staff_info_id' => $staffIds, 'end_date' =>$endDate, 'effect_date' =>  $startDate,],
        ])->toArray();
        $result      = [];
        foreach ($supportList as $item) {
            $dataList = DateHelper::DateRange(strtotime(max($item['employment_begin_date'], $startDate)),
                strtotime(min($item['employment_end_date'], $endDate)));
            foreach ($dataList as $date) {
                $result[$item['staff_info_id']][] = $date;
            }
        }
        return $result;
    }


    /**
     * 获取请假信息
     * @param $staffIds
     * @param $startDate
     * @param $endDate
     * @return array
     */
    protected function getStaffLeaveData($staffIds, $startDate, $endDate): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.date_at,s.staff_info_id");
        $builder->from(['s' => StaffAuditLeaveSplitModel::class]);
        $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
        $builder->inWhere("s.staff_info_id", $staffIds);
        $builder->andWhere('s.date_at >= :start_date: and s.date_at <= :end_date: ',
            ['start_date' => $startDate, 'end_date' => $endDate]);
        $builder->andWhere('a.audit_type = 2');
        $builder->inWhere('a.status', [enums::APPROVAL_STATUS_PENDING, enums::APPROVAL_STATUS_APPROVAL]);
        $builder->groupBy("s.staff_info_id,s.date_at");
        $data   = $builder->getQuery()->execute()->toArray();
        $result = [];
        foreach ($data as $datum) {
            $result[$datum['staff_info_id']][] = $datum['date_at'];
        }
        return $result;
    }

    /**
     * 获取轮休数据
     * @param $staffIds
     * @param $startDate
     * @param $endDate
     * @return array
     */
    protected function getOffDayData($staffIds, $startDate, $endDate)
    {
        $data   = HrStaffWorkDayModel::find([
            'columns'    => 'staff_info_id,date_at',
            'conditions' => 'staff_info_id in ({ids:array}) and date_at >= :weekStart: and date_at <= :weekEnd:',
            'bind'       => ['ids' => $staffIds, 'weekStart' => $startDate, 'weekEnd' => $endDate],
        ])->toArray();
        $result = [];
        foreach ($data as $datum) {
            $result[$datum['staff_info_id']][] = $datum['date_at'];
        }
        return $result;
    }

    /**
     * 获取轮休配置详情
     * @param $staffInfo
     * @return array
     */
    public function workdaySettingDetail($staffInfo): array
    {
        $allStaff     = $this->getManageStaff($staffInfo['staff_id']);
        //下级员工 在职 并且是自由轮休 可能有多个网点
        if (empty($allStaff)) {
            return [];
        }

        $data = $this->workdaySettingLogic($allStaff);
        if (empty($data['staff_list'])) {
            return [];
        }

        $storeIds  = array_column($data['config_list'], 'store_id');
        $jobTitles = array_column($data['config_list'], 'job_title');

        $storeInfo    = array_column((new SysStoreServer())->get_batch_store($storeIds), 'name', 'id');
        $jobTitleList = array_column((new HrJobTitleModel())->getListByIds($jobTitles), 'job_name', 'id');
        foreach ($data['config_list'] as &$datum) {
            $datum['store_name'] = $storeInfo[$datum['store_id']] ?? '';
            $datum['job_name']  = $jobTitleList[$datum['job_title']] ?? '';
        }
        foreach ($data['staff_list'] as &$datum) {
            $datum['store_name'] = $storeInfo[$datum['sys_store_id']] ?? '';
        }
        $this->logger->write_log(['workdaySettingDetail' => $data,'staffInfo'=>$staffInfo], 'info');
        return $data;
    }

    /**
     * 新的验证逻辑 - 按日配置
     * @param $staffInfo
     * @return array
     */
    public function checkDailySetting($staffInfo): array
    {
        // 创建 RPC 客户端 - 这里需要根据实际的 RPC 服务配置调整
        $dateList = [];
        for ($i = 1; $i <= 3; $i++) {
            $dateList[] = date('Y-m-d', strtotime("+{$i} day"));
        }
        $hcmParam['date_at']  = $dateList;
        $hcmParam['store_id'] = $staffInfo['organization_id'];
        $apiClient            = new ApiClient('hcm_rpc', '', 'workday_daily_detail', $this->lang);
        $apiClient->setParams($hcmParam);
        $rpcResult = $apiClient->execute();
        if (empty($rpcResult['result']['data']) || $rpcResult['result']['code'] != 1) {
            return [];
        }
        $dailyData   = $rpcResult['result']['data'];
        $onDutyStaff = [];//所有 在职的工号
        foreach ($dailyData as $date => $da) {
            foreach ($da as $d) {
                $onDutyStaff = array_merge($onDutyStaff, $d['on_duty_only_staff_list']);
            }
        }
        $onDutyStaff = array_values(array_unique($onDutyStaff));
        if (empty($onDutyStaff)) {
            return [];
        }
        //第一天所在周入职的
        $firstDate = $dateList[0];
        $weekStart = weekStart($firstDate);
        $weekEnd   = weekEnd($firstDate);
        $hireStaff = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'hire_date >= :weekStart: and hire_date <= :weekEnd: and staff_info_id in ({staff_ids:array})',
            'bind'       => ['weekStart' => $weekStart, 'weekEnd' => $weekEnd, 'staff_ids' => $onDutyStaff],
        ])->toArray();
        $hireStaff = array_column($hireStaff, 'staff_info_id');

        // 验证每一天的数据
        $flag = false;//是否需要弹窗
        foreach ($dailyData as $date => $dayData) {
            if (empty($dayData)) {
                continue;
            }
            foreach ($dayData as $jobData) {
                //如果是 夜班司机类型的 跳过
                if ($jobData['job_type'] == self::JOB_TYPE_NIGHT_DRIVER) {
                    continue;
                }

                $actualCount      = $jobData['actual_count'] ?? 0;//实际排班人数
                $schedulableCount = $jobData['schedulable_count'] ?? 0;//可排班人数
                $supportInCount   = $jobData['support_in_count'] ?? 0;//来支援人数
                //去 html 标签 有可能标红
                $actualCount     = strip_tags($actualCount);
                $minNum          = $jobData['min_num'];
                $maxNum          = $jobData['max_num'];
                $isFull          = $jobData['is_full'] ?? 'N';
                $onDutyOnlyCount = $jobData['on_duty_only_count'] ?? 0;

                // 特殊情况处理：如果 min_num 和 max_num 都为 null 且 is_full 为 Y
                if (is_null($minNum) && is_null($maxNum) && $isFull === 'Y') {
                    $minNum = $onDutyOnlyCount;
                    $maxNum = null; // 最大值不验证
                }

                // 检查最小值
                if (!is_null($minNum) && $actualCount < $minNum) {
                    $flag = true;
                }

                // 检查最大值
                if (!is_null($maxNum) && $actualCount > $maxNum) {
                    $flag = true;
                }

                //如果不符合条件 如果 剔除对应周入职的员工 剩下的数量小于 配置最小值 也不拦截
                $left    = array_diff($jobData['on_duty_only_staff_list'], $hireStaff);
                $leftNum = count($left);
                if ($flag && count($left) < $minNum) {
                    $flag = false;
                }
                //新增公式 验证 如果符合公式 也不拦截 邮件需求 可排班人数 < 最小值 并且 实际排班人数-来支援人数 >= 可排班人数
                if ($flag && $schedulableCount < $minNum && ($actualCount - $supportInCount) >= $schedulableCount) {
                    $flag = false;
                }
                //日志
                $this->logger->write_log([
                    'checkDailySetting' => "{$staffInfo['id']}_{$jobData['job_type']} {$date} {$minNum}_{$maxNum}_{$actualCount}_{$leftNum}",
                    'left'              => $left,
                    'flag'              => $flag,
                ], 'info');

                if ($flag) {
                    break 2;
                }
            }
        }
        if ($flag === false) {
            return [];
        }
        $t                        = $this->getTranslation();
        $jump_url                 = 'rest-setting-day';
        $base_url                 = env('h5_endpoint');
        $result['business_type']  = 'jump_h5';
        $result['jump_h5_detail'] = [
            'dialog_status'      => 1,
            'dialog_must_status' => 1,
            'dialog_msg'         => $t->_('21841_workday_setting_message'),
            'dialog_btn_msg'     => $t->_('21841_workday_setting_btn'),
            'dialog_jump_url'    => $base_url . $jump_url,
        ];
        return $result;
    }

    public function checkTruckSetting($staffInfo)
    {
        // 创建 RPC 客户端 - 这里需要根据实际的 RPC 服务配置调整
        $date = date('Y-m-d', strtotime("+1 day"));
        //获取管辖网点
        $category = HrStaffManageStoreCategoryModel::find([
            'conditions' => 'staff_info_id = :staff_id: and deleted = 0 and type = 1',
            'bind'       => ['staff_id' => $staffInfo['id']],
        ])->toArray();
        $stores   = HrStaffManageStoreModel::find([
            'conditions' => 'staff_info_id = :staff_id: and deleted = 0 and type = 1',
            'bind'       => ['staff_id' => $staffInfo['id']],
        ])->toArray();
        $category = empty($category) ? [] : array_column($category, 'store_category');
        $stores   = empty($stores) ? [] : array_column($stores, 'store_id');
        if (empty($category) && empty($stores)) {
            return [];
        }
        $conditions = 'state = 1 and ';
        $str        = $bind = [];
        if (!empty($category)) {
            $str[]            = 'category in ({category:array})';
            $bind['category'] = $category;
        }
        if (!empty($stores)) {
            $str[]       = 'id in ({ids:array})';
            $bind['ids'] = $stores;
        }
        $conditions .= '(' . implode(' or ', $str) . ')';
        $allStores  = SysStoreModel::find([
            'columns'    => 'id,name',
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
        if (empty($allStores)) {
            return [];
        }
        $storeName = array_column($allStores,'name', 'id');
        $allStores = array_column($allStores, 'id');

        //筛选出 明天有配置项的网点 并且 职位类型是 夜班司机
        $settingData = WorkdaySettingDailyDetailModel::find([
            'columns'    => 'store_id',
            'conditions' => 'date_at = :date_at: and job_type = :job_type: and store_id in ({ids:array}) and is_delete = 0',
            'bind'       => ['date_at' => $date, 'job_type' => self::JOB_TYPE_NIGHT_DRIVER, 'ids' => $allStores],
        ])->toArray();
        if (empty($settingData)) {
            return [];
        }
        $allStores             = array_column($settingData, 'store_id');
        $hcmParam['store_ids'] = $allStores;
        $hcmParam['date_at']   = $date;

        $apiClient = new ApiClient('hcm_rpc', '', 'workday_daily_detail_truck', $this->lang);
        $apiClient->setParams($hcmParam);
        $rpcResult = $apiClient->execute();
        if (empty($rpcResult['result']['data']) || $rpcResult['result']['code'] != 1) {
            return [];
        }
        $dailyData   = $rpcResult['result']['data'];
        $onDutyStaff = [];//所有 在职的工号
        foreach ($dailyData as $storeId => $da) {
            $onDutyStaff = array_merge($onDutyStaff, $da[$date]['on_duty_only_staff_list']);
        }
        $onDutyStaff = array_values(array_unique($onDutyStaff));
        if (empty($onDutyStaff)) {
            return [];
        }
        //第一天所在周入职的
        $weekStart = weekStart($date);
        $weekEnd   = weekEnd($date);
        $hireStaff = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'hire_date >= :weekStart: and hire_date <= :weekEnd: and staff_info_id in ({staff_ids:array})',
            'bind'       => ['weekStart' => $weekStart, 'weekEnd' => $weekEnd, 'staff_ids' => $onDutyStaff],
        ])->toArray();
        $hireStaff = array_column($hireStaff, 'staff_info_id');

        // 验证每一天的数据
        $msgStores = [];
        foreach ($allStores as $storeId) {
            $flag = false;//是否需要弹窗
            if (empty($dailyData[$storeId][$date])) {
                continue;
            }
            $jobData          = $dailyData[$storeId][$date];
            $actualCount      = $jobData['actual_count'] ?? 0;//实际排班人数
            $schedulableCount = $jobData['schedulable_count'] ?? 0;//可排班人数
            $supportInCount   = $jobData['support_in_count'] ?? 0;//来支援人数
            //去 html 标签 有可能标红
            $actualCount     = strip_tags($actualCount);
            $minNum          = $jobData['min_num'];
            $maxNum          = $jobData['max_num'];
            $isFull          = $jobData['is_full'] ?? 'N';
            $onDutyOnlyCount = $jobData['on_duty_only_count'] ?? 0;

            // 特殊情况处理：如果 min_num 和 max_num 都为 null 且 is_full 为 Y
            if (is_null($minNum) && is_null($maxNum) && $isFull === 'Y') {
                $minNum = $onDutyOnlyCount;
                $maxNum = null; // 最大值不验证
            }

            // 检查最小值
            if (!is_null($minNum) && $actualCount < $minNum) {
                $flag = true;
            }

            // 检查最大值
            if (!is_null($maxNum) && $actualCount > $maxNum) {
                $flag = true;
            }

            //如果不符合条件 如果 剔除对应周入职的员工 剩下的数量小于 配置最小值 也不拦截
            $left    = array_diff($jobData['on_duty_only_staff_list'], $hireStaff);
            $leftNum = count($left);
            if ($flag && count($left) < $minNum) {
                $flag = false;
            }
            //新增公式 验证 如果符合公式 也不拦截 邮件需求 可排班人数 < 最小值 并且 实际排班人数-来支援人数 >= 可排班人数
            if ($flag && $schedulableCount < $minNum && ($actualCount - $supportInCount) >= $schedulableCount) {
                $flag = false;
            }
            //日志
            $this->logger->write_log([
                'checkDailySetting' => "{$staffInfo['id']} {$date} {$minNum}_{$maxNum}_{$actualCount}_{$leftNum}",
                'left'              => $left,
                'flag'              => $flag,
            ], 'info');

            if ($flag) {//不符合条件的
                $msgStores[] = $storeName[$storeId];
            }
        }
        if (empty($msgStores)) {
            return [];
        }

        $returnArr['business_type']        = 'un_remittance';
        $returnArr['remittance_detail'] = [
            'dialog_title'       => $this->getTranslation()->_('22694_workday_setting_notice'),
            'dialog_status'      => 1,
            'dialog_msg'         => $this->getTranslation()->_('22694_chuck_punch_notice', ['store_id' => implode(',', $msgStores)]),
            'dialog_must_status' => 1,
            'is_ces_tra'         => 0,
        ];
        return $returnArr;
    }

    //排班建议 按日配置 接口获取数据详情
    public function dailyDetail($param)
    {
        for ($i = 1; $i <= 3; $i++) {
            $dateList[] = date('Y-m-d', strtotime("+{$i} day"));
        }
        $hcmParam['date_at']  = $dateList;
        $hcmParam['store_id'] = $param['store_id'];
        $apiClient            = new ApiClient('hcm_rpc', '', 'workday_daily_detail', $this->lang);
        $apiClient->setParams($hcmParam);
        $result = $apiClient->execute();
        if (empty($result['result']['data']) || $result['result']['code'] != 1) {
            return [];
        }
        $dailyData = $result['result']['data'];
        $data      = [];
        foreach ($dailyData as $date => $da) {
            //如果是夜班司机职位类型 则跳过不展示
            foreach ($da as $k => $d) {
                if ($d['job_type'] == self::JOB_TYPE_NIGHT_DRIVER) {
                    unset($da[$k]);
                }
            }
            $row['date_at'] = $date;
            $row['data']    = array_values($da);
            $data[]         = $row;
        }
        return $data;
    }

    //支援操作 验证已排 人数 + 本次申请人 是否在配置范围内
    public function checkSupportWorkday($supportInfo)
    {
        $jobId                = $supportInfo['job_title_id'];//申请支援的 职位id
        $dateList             = DateHelper::DateRange(strtotime($supportInfo['employment_begin_date']), strtotime($supportInfo['employment_end_date']));

        $hcmParam['date_at']  = $dateList;
        $hcmParam['store_id'] = $supportInfo['store_id'];
        $apiClient            = new ApiClient('hcm_rpc', '', 'workday_daily_detail', $this->lang);

        $apiClient->setParams($hcmParam);
        $result = $apiClient->execute();
        if (empty($result['result']['data']) || $result['result']['code'] != 1) {
            return;
        }
        $dailyData = $result['result']['data'];

        // 验证每一天的数据
        $logDate = [];//需要记录下来的日期 弹窗要用
        foreach ($dailyData as $date => $dayData) {
            if (empty($dayData)) {
                continue;
            }
            foreach ($dayData as $jobData) {
                $jobIds = $jobData['job_ids'] ?? [];
                if (empty($jobIds)) {//没有配置职位
                    continue;
                }
                if (!in_array($jobId, $jobIds)) {//非当前申请职位类型
                    continue;
                }
                $actualCount     = $jobData['actual_count'] ?? 0;
                $actualCount     = strip_tags($actualCount);
                $minNum          = $jobData['min_num'];
                $maxNum          = $jobData['max_num'];
                $isFull          = $jobData['is_full'] ?? 'N';
                $onDutyOnlyCount = $jobData['on_duty_only_count'] ?? 0;

                // 特殊情况处理：如果 min_num 和 max_num 都为 null 且 is_full 为 Y
                if (is_null($minNum) && is_null($maxNum) && $isFull === 'Y') {
                    $minNum = $onDutyOnlyCount;
                    $maxNum = null; // 最大值不验证
                }
//                // 检查最小值
//                if (!is_null($minNum) && $actualCount < $minNum) {
//                    $logDate[] = $date;
//                    continue;
//                }

                // 检查最大值 加上本次申请人数
                if (!is_null($maxNum) && ($actualCount + 1) > $maxNum) {
                    $logDate[] = $date;
                    continue;
                }
            }
        }
        if (empty($logDate)) {
            return;
        }
        $logDate = array_unique($logDate);
        sort($logDate);
        throw new BusinessException($this->getTranslation()->_('workday_daily_support_notice', ['date_list' => implode(',', $logDate)]));//被申请网点%date_list%的建议出勤人数已达到上限，无需支援；如有特殊情况，请联系网点主管或管理员修改排班人数。
    }


}