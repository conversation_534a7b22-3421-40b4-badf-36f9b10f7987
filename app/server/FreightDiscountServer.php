<?php


namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Controllers\ControllerBase;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\FreightDiscountCouponModel;
use FlashExpress\bi\App\Models\backyard\FreightDiscountModel;
use FlashExpress\bi\App\Models\backyard\SysAttachmentModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DiscountRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;

class FreightDiscountServer extends AuditBaseServer
{
    protected $timezone;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->timezone = $timezone;
        $this->lang = $lang;
        parent::__construct($this->lang);
    }

    /**
     * 新增运费折扣申请
     * @param array $paramIn
     * @return mixed
     * @throws \Exception
     */
    public function addFreightDiscount($paramIn = [])
    {
        //[1]获取参数
        $submitterId = $this->processingDefault($paramIn, 'staff_id');
        $costomerId = $this->processingDefault($paramIn, 'costomer_id');
        $costomerName = $this->processingDefault($paramIn, 'costomer_name');
        $costomerMobile = $this->processingDefault($paramIn, 'costomer_mobile');
        $costomerType = $this->processingDefault($paramIn, 'costomer_type');
        $costomerCreatedAt = $this->processingDefault($paramIn, 'costomer_created_at');
        $costomerParcelCount = $this->processingDefault($paramIn, 'costomer_parcel_count', 2);
        $costomerEstimateParcelCount = $this->processingDefault($paramIn, 'costomer_estimate_parcel_count');
        $applyRreasonType = $this->processingDefault($paramIn, 'apply_reason_type', 3);
        $currentDisc = $this->processingDefault($paramIn, 'current_disc', 2);
        $priceRuleCategory = $this->processingDefault($paramIn, 'price_rule_category', 2);
        $validMonth = $this->processingDefault($paramIn, 'valid_month', 2, 0);
        $priceType = $this->processingDefault($paramIn, 'price_type');
        $remark = $this->processingDefault($paramIn, 'remark');
        $coupon = $this->processingDefault($paramIn, 'coupon');
        $filePathArr = $this->processingDefault($paramIn, 'file_path', 3);
        $currentCodPoundageRateStr = $this->processingDefault($paramIn, 'current_cod_poundage_rate_str');
        $currentReturnDiscountRate = $this->processingDefault($paramIn, 'current_return_discount_rate',2);
        $currentCreditTerm = $this->processingDefault($paramIn, 'current_credit_term');
        $requestCreditTerm = $this->processingDefault($paramIn, 'request_credit_term');
        $codEnabled = $this->processingDefault($paramIn, 'cod_enabled',2);
        $channel = $this->processingDefault($paramIn, 'channel');
        $discountEffectiveDate = $this->processingDefault($paramIn, 'discount_effective_date');
        $settlementCategory = $this->processingDefault($paramIn, 'settlement_category', 2);
        $requestDisc = $paramIn['request_disc'] ?? '';
        $requestReturnDiscountRate = $paramIn['request_return_discount_rate'] ?? '';
        $requestCodPoundageRateStr = $paramIn['request_cod_poundage_rate_str'] ?? '';

        //[2]校验
        //校验申请营销产品
        $discServer = new DiscountServer($this->lang, $this->timezone);
        $discServer->ShopRequestCheck($paramIn);

        //获取审批类型 & 子审批流
        if ($channel == 2) {
            $requestType = enums::$audit_type['FDS'];
            $extend      = [];
        } else {
            $requestType = enums::$audit_type['FDP'];
            $submitterInfo = HrStaffInfoModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind' => [
                    'staff_info_id'  => $submitterId,
                ]
            ])->toArray();

            //申请人所在一级部门是Sales 则选择子审批流1
            //否则选择子审批流2
            if ($submitterInfo['sys_department_id'] == enums::$department['Sales']) {
                $extend['flow_code'] = '1';
            } else {
                $extend['flow_code'] = '2';
            }
        }

        //计算占比
        $t = $this->getTranslation();
        $proportion = (new DiscountRepository())->calcProportion($costomerId);
        if (!empty($proportion)) {
            $proportionArr[] = $t->_('discount_detail_7') . $proportion['insured'] * 100 . '%';
            $proportionArr[] = $t->_('discount_detail_8') . $proportion['express'] * 100 . '%';
            $proportionArr[] = $t->_('discount_detail_9') . $proportion['freight'] * 100 . '%';

            $proportionText = implode("<br />", $proportionArr);
        }

        //[3]保存数据
        $insertData = [
            'submitter_id' => $submitterId,
            'serial_no' => 'FD' . $this->getRandomId(),
            'costomer_id' => $costomerId,
            'costomer_name' => $costomerName,
            'costomer_mobile' => $costomerMobile,
            'costomer_type' => $costomerType,
            'costomer_created_at' => $costomerCreatedAt,
            'costomer_parcel_count' => $costomerParcelCount,
            'costomer_estimate_parcel_count' => $costomerEstimateParcelCount,
            'price_type' => $priceType,
            'current_disc' => $currentDisc === '' ? -1 : $currentDisc,
            'apply_reason_type' => implode(',', $applyRreasonType),
            'proportion' => $proportionText ?? '',
            'state' => enums::$audit_status['panding'],
            'remark' => $remark ?? '',
            'current_cod_poundage_rate_str' => $codEnabled == 1 ? $currentCodPoundageRateStr : null,
            'request_cod_poundage_rate_str' => $codEnabled == 1 ? $requestCodPoundageRateStr : null,
            'current_return_discount_rate' => $currentReturnDiscountRate == '' ? -1 : $currentReturnDiscountRate,
            'request_return_discount_rate' => $requestReturnDiscountRate == '' ? -1 : $requestReturnDiscountRate,
            'current_credit_term' => $currentCreditTerm === '' ? -1 : $currentCreditTerm,
            'request_credit_term' => $requestCreditTerm === '' ? -1 : $requestCreditTerm,
            'channel' => $channel,
            'discount_effective_date' => $discountEffectiveDate ? $discountEffectiveDate : '1970-01-01',
            'settlement_category' => $settlementCategory,
            'wf_role' => 'fd_new',
        ];
        if (isset($priceRuleCategory)) {
            $insertData = array_merge($insertData, [
                'price_rule_category' => 1,
                'request_disc' => $requestDisc === '' ? -1 : $requestDisc,
                'valid_days' => $validMonth * 30,
            ]);
        }

        //[3]组织审批数据
        try {
            $this->getDI()->get('db')->begin();
            //保存申请数据
            $repo = new OtherRepository();
            $pubRepo = new PublicRepository();
            $fdId = $repo->InsertAudit('freight_discount', $insertData);
            if (empty($fdId)) {
                throw new \Exception($this->getTranslation()->_('4008'));
            }

            //保存优惠券
            if (isset($coupon) && $coupon && is_array($coupon)) {
                $insertCoupon = [];
                foreach ($coupon as $v) {
                    $insertCoupon[] = [
                        'pid' => $fdId,
                        'coupon_type' => $v['type'],
                        'coupon_days_type' => $v['days_type'],
                        'coupon_valid_days' => $v['valid_days'],
                        'coupon_name' => $v['name'],
                        'coupon_num' => $v['num'],
                        'created_at' => gmdate("Y-m-d H:i:s", time()),
                    ];
                }
                $repo->batch_insert('freight_discount_coupon', $insertCoupon);
            }

            //保存上传附件
            if (!empty($filePathArr)) {
                $insertFileData = [];

                foreach ($filePathArr as $file) {
                    $insertFileData[] = [
                        'id' => $fdId,
                        'image_path' => $file
                    ];
                }
                $pubRepo->batchInsertImgs($insertFileData, "FREIGHT_DISCOUNT");
            }

            //创建
            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($fdId, $requestType, $submitterId, null, $extend);
            if (!$requestId) {
                throw new \Exception('创建审批流失败');
            }
            $this->getDI()->get('db')->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log("add discount failure:" . $e->getMessage(), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        return $this->checkReturn([]);
    }

    /**
     * 审批申请运费折扣
     * @param array $paramIn
     * @return mixed
     * @throws \Exception
     */
    public function updateFreightDisc($paramIn = [])
    {
        //[1]获取参数
        $staffId           = $this->processingDefault($paramIn, 'staff_id', 2);
        $status            = $this->processingDefault($paramIn, 'status', 2);
        $rejectReason      = $this->processingDefault($paramIn, 'reject_reason', 1);
        $approvalReason    = $this->processingDefault($paramIn, 'reason', 1);
        $auditId           = $this->processingDefault($paramIn, 'audit_id', 2);
        $rejectReason      = addcslashes(stripslashes($rejectReason),"'");
        $approvalReason    = addcslashes(stripslashes($approvalReason),"'");

        //获取详情
        $fdInfo    = FreightDiscountModel::findFirst($auditId)->toArray();
        if (empty($fdInfo)) {
            throw new \Exception($this->getTranslation()->_('4008'));
        }

        //已经是最终状态,不能撤销
        if ($fdInfo['state'] != enums::$audit_status['panding'] && $status == 4) {
            throw new \Exception($this->getTranslation()->_('4008'));
        }

        if ($fdInfo['channel'] == 2) {
            $requestType = enums::$audit_type['FDS'];
        } else {
            $requestType = enums::$audit_type['FDP'];
        }

        //同意或者驳回等分开处理
        $server = new ApprovalServer($this->lang, $this->timezone);

        try {
            if ($status == enums::$audit_status['approved']) { //审批同意

                $server->approval($auditId, $requestType, $staffId, $approvalReason);
            } else if ($status == enums::$audit_status['dismissed']) { //驳回

                $server->reject($auditId, $requestType, $rejectReason, $staffId);
            } else { //撤销

                $server->cancel($auditId, $requestType, '', $staffId);
            }
        } catch (InnerException $e) {
            $this->getDI()->get('logger')->write_log("add freight discount failure:" . $e->getMessage(), $e->getTraceAsString(), 'notice');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        return $this->checkReturn(['data' => $auditId]);
    }

    /**
     * 生成详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom): array
    {
        $server = new DiscountServer($this->lang, $this->timezone);
        $repo = new AuditlistRepository($this->lang, $this->timezone);
        //获取详情
        $result = FreightDiscountModel::findFirst($auditId);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        $result->coupon = $result->getRelated('Items');
        $result = $result->toArray();

        //获取申请类型
        if ($result['channel'] == 2) {
            $requestType = enums::$audit_type['FDS'];
        } else {
            $requestType = enums::$audit_type['FDP'];
        }

        //申请营销产品
        $t = $this->getTranslation();
        //申请原因转义
        $reasons = [];
        if (isset($result['apply_reason_type']) && $result['apply_reason_type']) {
            $applyReasonTypeArr = explode(',', $result['apply_reason_type']);
            foreach ($applyReasonTypeArr as $type) {
                $reasons[] = $t->_('disc_reason_' . $type) ?? '';
            }
        }
        $result['apply_reason_type_text'] = isset($reasons) && $reasons ? implode("<br/>", $reasons) . ($result['remark'] ?? '') : '';

        //价格类型转换
        $result['price_type_text'] = $t->_('price_type_' . $result['price_type']) ?? '';

        //有效期限
        $validMonths = $result['valid_days'] / 30;
        $result['valid_month'] = $t->_('month_period_' . $validMonths);

        //添加用户的相关信息
        $staff = (new StaffRepository())->getStaffInfo($result['submitter_id']);
        $result['store_name'] = $staff['store_name'];
        $result['manage_region_name'] = $staff['manage_region_name'];
        $result['manage_piece_name'] = $staff['manage_piece_name'];
        $result['image_path'] = [];
        //获取图片
        $images = SysAttachmentModel::find([
            'conditions' => "oss_bucket_type = 'FREIGHT_DISCOUNT' and oss_bucket_key = :oss_bucket_key: and deleted = 0",
            'bind' => [
                'oss_bucket_key'  => $auditId,
            ],
        ])->toArray();
        if (!empty($images)) {
            $detailInfo['image_path'] = [];
            foreach ($images as $image) {
                array_push($result['image_path'], convertImgUrl($image['bucket_name'], $image['object_key']));
            }
        }

        //已经申请的折扣产品
        $request = [];
        if ($result['price_rule_category'] != 0 && $result['request_disc'] != -1) {
            $request[] = ( $t->_('discount_type_' . $result['price_rule_category'])  ?? '') . $result['request_disc'] . "%({$result['valid_days']} days)";
        }
        if ($result['request_cod_poundage_rate_str'] !== "") {
            $request[] =  $t->_('cod_rate') . $result['request_cod_poundage_rate_str'] . "%({$result['valid_days']} days)";
        }
        if ($result['request_return_discount_rate'] != -1) {
            $request[] =  $t->_('return_rate') . $result['request_return_discount_rate'] . "%({$result['valid_days']} days)";
        }
        if ($result['request_credit_term'] != -1) {
            $request[] =  $t->_('credit_period') . $result['request_credit_term'] . " days";
        }

        //获取优惠券
        $coupons = FreightDiscountCouponModel::find([
            'conditions' => 'pid = :id:',
            'bind'       => ['id' => $auditId],
        ])->toArray();
        if ($coupons) {
            foreach ($coupons as $coupon) {
                if (isset($coupon['coupon_name']) && $coupon['coupon_name']) {
                    $request[] = $coupon['coupon_name'] . "({$coupon['coupon_num']})" ?? '';
                }
            }
        }
        $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
        if ($staff_info['data']) {
            $staff_info = $staff_info['data'];
        }

        //组织详情数据
        $detailLists = [
            'apply_parson' => sprintf('%s ( %s )', $staff_info['name'], $staff_info['id']),
            'apply_department' => sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? ''),
            'costomer_id' => $result['costomer_id'] ?? '',
            'costomer_name' => $result['costomer_name'] ?? '',
            'phone_no' => $result['costomer_mobile'] ?? '',
            'price_type' => $result['price_type_text'] ?? '',
            'current_disc' => $result['current_disc'] . '%' ?? '',
            'discount_detail_10'=> !empty($result['settlement_category']) ? $this->getTranslation()->_('settlement_category_' . $result['settlement_category']): '',
            'discount_detail_2' => $result['costomer_parcel_count'],
            'discount_detail_3' => $result['proportion'] ?? '',
            'discount_detail_4' => $result['costomer_created_at'],
            'discount_detail_5' => $result['costomer_estimate_parcel_count'],
            'reason_application' => $result['apply_reason_type_text'] ?? '',
            'discount_detail_11' => (empty($request) ? '': implode("<br />", $request)) ?? '',
            'discount_effective_date' => $result['discount_effective_date'] == '1970-01-01' ? "" : $result['discount_effective_date'],
            'photo' => $result['image_path'] ?? [],
        ];

        //驳回状态，需要显示驳回原因
        if ($result['state'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        $data = [
            'title' => $repo->getAudityType($requestType),
            'id' => $result['id'],
            'staff_id' => $result['submitter_id'],//申请人
            'type' => $requestType,
            'created_at' => $result['created_at'],
            'updated_at' => $result['updated_at'],
            'status' => $result['state'],
            'status_text' => $repo->getAuditStatus('10' . $result['state']),
            'serial_no' => $result['serial_no'] ?? '',
        ];
        $returnData['data']['head']   = $data;
        return $returnData;
    }

    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user): array
    {
        //获取详情
        $info = FreightDiscountModel::findFirst($auditId)->toArray();
        if (!empty($info)) {
            $param = [
                [
                    'key' => "phone_no",
                    'value' => $info['costomer_mobile']
                ],
                [
                    'key' => "costomer_id",
                    'value' => $info['costomer_id']
                ]
            ];
        }
        return $param ?? [];
    }

    /**
     * 设置回调
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        if ($isFinal) {

            //更新审批状态
            $fdDetail = FreightDiscountModel::findFirst($auditId);
            $fdDetail->state = $state;
            $fdDetail->sync_state = 2;
            $fdDetail->save();
            if ($state == enums::$audit_status['approved']) { //同意
                $fdInfo = $fdDetail->toArray();
                try {
                    $nowTime = time();
                    $nowTimeTH = gmdate('Y-m-d 00:00:00', $nowTime + $add_hour * 3600);
                    $nowTimeTH = $nowTimeTH > $fdInfo['discount_effective_date'] . " 00:00:00" ? $nowTimeTH : $fdInfo['discount_effective_date'] . " 00:00:00" ;
                    $endTimeTH = gmdate('Y-m-d 00:00:00', strtotime($nowTimeTH) + 86400 * $fdInfo['valid_days']);

                    if (isset($fdInfo['coupon']) && $fdInfo['coupon'] && is_array($fdInfo['coupon'])) {
                        foreach ($fdInfo['coupon'] as $v) {
                            $this->getDI()->get('db')->updateAsDict('freight_discount_coupon', [
                                'coupon_start_date' => gmdate('Y-m-d', $nowTime),
                                'coupon_end_date'   => gmdate('Y-m-d', $nowTime + 86400 * $v['coupon_valid_days']),
                            ], "pid = {$fdInfo['id']} and coupon_type = {$v['coupon_type']}");
                        }
                    }

                    //跟MS同步
                    $logger = $this->getDI()->get('logger');
                    //                    $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.DiscountApplySvc','saveDiscountApply', $this->lang);
//                    $params = [
//                        "related_id"        => $fdInfo['id'],
//                        "client_id"         => $fdInfo['costomer_id'],
//                        "customer_type_category" => $fdInfo['costomer_type'],
//                        "customer_name"     => $fdInfo['costomer_name'],
//                        "customer_mobile"   => $fdInfo['costomer_mobile'],
//                        "price_type"        => $fdInfo['price_type'],
//                        "price_rule_category" => 1, //1 是普通折扣，2是水果折扣
//                        "current_disc"      => $fdInfo['current_disc'] == -1 ? null: $fdInfo['current_disc'], //-1是没有选择运费折扣，此种情况需要同步null,
//                        "request_disc"      => $fdInfo['request_disc'] == -1 ? null: $fdInfo['request_disc'], //-1是没有选择运费折扣，此种情况需要同步null
//                        "disc_start_date"   => strtotime($nowTimeTH),
//                        "disc_end_date"     => strtotime($endTimeTH),
//                        "valid_dates"       => $fdInfo['valid_days'],
//                        "current_cod_poundage_rate_str"  => $fdInfo['current_cod_poundage_rate_str'] == '' ? null: $fdInfo['current_cod_poundage_rate_str'],
//                        "request_cod_poundage_rate_str"  => $fdInfo['request_cod_poundage_rate_str'] == '' ? null: $fdInfo['request_cod_poundage_rate_str'],
//                        "current_return_discount_rate"   => $fdInfo['current_return_discount_rate'] == -1 ? null: $fdInfo['current_return_discount_rate'],
//                        "request_return_discount_rate"   => $fdInfo['request_return_discount_rate'] == -1 ? null: $fdInfo['request_return_discount_rate'],
//                        "current_credit_term" => $fdInfo['current_credit_term'] == -1 ? null: $fdInfo['current_credit_term'],
//                        "request_credit_term" => $fdInfo['request_credit_term'] == -1 ? null: $fdInfo['request_credit_term'],
//                        "channel"             => $fdInfo['channel'],
//                    ];
//                    $fle_rpc->setParams($params);
//                    $fle_return = $fle_rpc->execute();
                    $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.DiscountApplySvc','saveDiscountApplyV2', $this->lang);
                    $publicParam = [
                        "related_id"        => (int) $fdInfo['id'],
                        "client_id"         => $fdInfo['costomer_id'],
                        "customer_type_category" => (int) $fdInfo['costomer_type'],
                        "customer_name"     => $fdInfo['costomer_name'],
                        "customer_mobile"   => $fdInfo['costomer_mobile'],
                        "price_type"        => (int) $fdInfo['price_type'],
                        "disc_start_date"   => strtotime($nowTimeTH),
                        "disc_end_date"     => strtotime($endTimeTH),
                        "valid_dates"       => (int) $fdInfo['valid_days'],
                        "channel"             => (int) $fdInfo['channel'],
                    ];
                    $params['discount_apply_list'] = [];
                    if ($fdInfo['request_disc'] != -1) { // 普通运费折扣类型 /水果运费折扣类型
                        $params['discount_apply_list'][] = array_merge($publicParam, [
                            "price_rule_category" => $fdInfo['price_rule_category'], //1 是普通折扣，2是水果折扣
                            "current_disc"      => in_array($fdInfo['current_disc'], [0 , -1, ""]) ? null : (float) $fdInfo['current_disc'], //-1是没有选择运费折扣，此种情况需要同步null,
                            "request_disc"      => in_array($fdInfo['request_disc'], [0 , -1, ""]) ? null : (float) $fdInfo['request_disc'], //-1是没有选择运费折扣，此种情况需要同步null
                            "current_disc_str"      => (string) (in_array($fdInfo['current_disc'], [0 , -1, ""]) ? 0: $fdInfo['current_disc']), //-1是没有选择运费折扣，此种情况需要同步null,
                            "request_disc_str"      => (string) (in_array($fdInfo['request_disc'], [0 , -1, ""]) ? 0: $fdInfo['request_disc']), //-1是没有选择运费折扣，此种情况需要同步null
                        ]);
                    }
                    if ($fdInfo['request_return_discount_rate'] != -1) {
                        $params['discount_apply_list'][] = array_merge($publicParam, [
                            "price_rule_category" =>  3, //3退件运费折扣
                            "current_disc"      => in_array($fdInfo['current_return_discount_rate'], [0, -1, ""]) ? null : (float) $fdInfo['current_return_discount_rate'],
                            "request_disc"      => in_array($fdInfo['request_return_discount_rate'], [0, -1, ""]) ? null : (float) $fdInfo['request_return_discount_rate'],
                            "current_disc_str"      => (string) (in_array($fdInfo['current_return_discount_rate'], [0, -1, ""]) ? 0 : $fdInfo['current_return_discount_rate']),
                            "request_disc_str"      => (string) (in_array($fdInfo['request_return_discount_rate'], [0, -1, ""]) ? 0 : $fdInfo['request_return_discount_rate']),
                        ]);
                    }
                    if ($fdInfo['request_cod_poundage_rate_str'] !== "") {
                        $params['discount_apply_list'][] = array_merge($publicParam, [
                            "price_rule_category" =>  4, //3COD手续费
                            "current_disc"      => in_array($fdInfo['current_cod_poundage_rate_str'], [0, -1, ""]) ? null : (float) $fdInfo['current_cod_poundage_rate_str'],
                            "request_disc"      => in_array($fdInfo['request_cod_poundage_rate_str'], [0, -1, ""]) ? null : (float) $fdInfo['request_cod_poundage_rate_str'],
                            "current_disc_str"      => (string) (in_array($fdInfo['current_cod_poundage_rate_str'], [0, -1, ""]) ? 0 : $fdInfo['current_cod_poundage_rate_str']),
                            "request_disc_str"      => (string) (in_array($fdInfo['request_cod_poundage_rate_str'], [0, -1, ""]) ? 0 : $fdInfo['request_cod_poundage_rate_str']),
                        ]);
                    }
                    if ($fdInfo['request_credit_term'] != -1) {
//                        if (!$fdInfo['valid_days']) {
                        $publicParam['valid_dates'] = 999 * 30;
                        $publicParam['disc_end_date'] = strtotime(gmdate('Y-m-d 00:00:00', strtotime($nowTimeTH) + 86400 * 999 * 30));
//                        }
                        $params['discount_apply_list'][] = array_merge($publicParam, [
                            "price_rule_category" =>  5, //5信用期限
                            "current_disc"      => in_array($fdInfo['current_credit_term'], [0, -1, ""]) ? null : (float) $fdInfo['current_credit_term'],
                            "request_disc"      => in_array($fdInfo['request_credit_term'], [0, -1, ""]) ? null : (float) $fdInfo['request_credit_term'],
                            "current_disc_str"      => (string) (in_array($fdInfo['current_credit_term'], [0, -1, ""]) ? 0 : $fdInfo['current_credit_term']),
                            "request_disc_str"      => (string) (in_array($fdInfo['request_credit_term'], [0, -1, ""]) ? 0 : $fdInfo['request_credit_term']),
                        ]);
                    }

                    $fle_rpc->setParams($params);
                    $fle_return = $fle_rpc->execute();
                    if (isset($fle_return['result'])) {
                        $logger->write_log('freightDiscountRequest:' . json_encode($params) . ":freightDiscountresponse:" . json_encode($fle_return), 'info');
                    } else {
                        $logger->write_log('freightDiscountRequest:' . json_encode($params) . ":freightDiscountresponse:" . json_encode($fle_return), 'notice');
                    }

                    if (isset($fdInfo['coupon']) && $fdInfo['coupon'] && is_array($fdInfo['coupon'])) {
                        $paramCoupon = [];
                        foreach ($fdInfo['coupon'] as $coupon) {
                            $coupon['coupon_validity_time'] = $coupon['coupon_days_type'];
                            $coupon['coupon_category'] = $coupon['coupon_type'];
                            $coupon['code_number'] = $coupon['coupon_num'];

                            $paramCoupon[] = $coupon;
                        }

                        $syncParams = [
                            'customer_id' => $fdInfo['costomer_id'],
                            'customer_type' => $fdInfo['costomer_type'],
                            'apply_key' => $fdInfo['serial_no'],
                            'coupon_apply'  => $paramCoupon
                        ];

                        //跟Coupon同步
                        $lang = $this->lang == 'zh-CN' ? 'zh' : $this->lang;
                        $rpc = new ApiClient('coupon_rpc','','backyardApplyCoupon', $lang);
                        $rpc->setParams($syncParams);
                        $data = $rpc->execute();

                        if (!isset($data['result']['code']) || $data['result']['code'] != 1) {
                            $logger->write_log("coupon rpc error request:" . json_encode($syncParams) . ", response:" . json_encode($data) , 'error');
                        } else {
                            //设置为已同步
                            $this->getDI()->get('db')->updateAsDict('freight_discount', [
                                'coupon_sync_state' => 2,
                            ], "id = {$fdInfo['id']}");
                        }
                    }


                } catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log("sync discount failure,reason :" . $e->getMessage());
                }
            }
        }
    }

    /**
     * 提供申请、审批条件
     * @param $auditId
     * @param $user
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null): array
    {
        //获取申请信息
        $params = FreightDiscountModel::findFirst($auditId)->toArray();

        //获取申请人的职位
        $submitterInfo = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind' => [
                'staff_info_id'  => $params['submitter_id'],
            ]
        ])->toArray();

        return [
            'submitter_id' => $params['submitter_id'],
            'job_title' => $submitterInfo['job_title'] ?? 0,
            'request_disc' => $params['request_disc'] ?? 0,
            'request_cod_poundage_rate_str' => $params['request_cod_poundage_rate_str'] ?? 0,
            'request_return_discount_rate' => $params['request_return_discount_rate'] ?? 0,
            'request_credit_term'       => $params['request_credit_term'] ?? 0,
        ];
    }
}