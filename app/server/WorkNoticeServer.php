<?php
/**
 * Author: Bruce
 * Date  : 2024-11-14 00:16
 * Description:
 */

namespace FlashExpress\bi\App\Server;


use App\Country\Tools;

class WorkNoticeServer extends BaseServer
{
    public $timezone;

    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
    }

    public function list($params)
    {
        $renewContractBusinessData = Tools::reBuildCountryInstance(new RenewContractBusinessServer($this->lang, $this->timezone), [$this->lang, $this->timezone])->isDisplayEntry($params);
        $data['renew_contract_business'] = $renewContractBusinessData;
        return $data;
    }
}