<?php
/**
 * Created by <PERSON>p<PERSON><PERSON><PERSON>.
 * User: weijian
 * Date: 2019-04-08
 * Time: 17:35
 */

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Enums\CeoMailEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AttendanceDataV2RecalSolidModel;
use FlashExpress\bi\App\Models\backyard\BanklistModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftHistoryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftV2Model;
use FlashExpress\bi\App\Models\backyard\SalaryGongZiModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\StaffDeviceInfo;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\StaffInfoAuditCheckModel;
use FlashExpress\bi\App\Models\backyard\StaffPayrollModel;
use FlashExpress\bi\App\Models\backyard\StaffResignModel;
use FlashExpress\bi\App\Models\backyard\StaffSalaryEpfSocsoModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\HrStaffAnnexInfoRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffInfoAuditCheckRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\Message\UnReceiveParcelMessage;

class PersoninfoServer extends BaseServer
{
    public $timezone;

    public function __construct($lang = 'zh-CN', $timezone, $userInfo = [])
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 从 BI 获取一线员工提成数据
     * @param $staffId
     * @return array|mixed
     */
    public function getIncentiveFromBI($staffId)
    {
        $ac = new ApiClient('ard_api', '', 'incentive.incentive_data', $this->lang);
        $ac->setParams([
            'staffInfoId' => $staffId,
        ]);
        $ac_result = $ac->execute();
        $this->logger->write_log([$staffId, $ac_result], 'info');
        if (!empty($ac_result['result']['code']) && $ac_result['result']['code'] == 1) {
            return empty($ac_result['result']['data']) ? null : $ac_result['result']['data'];
        }
        return null;
    }
    /**
     * 当日班次
     * @param $staff_id
     * @return string
     */
    protected function getStaffCurrentShiftInfo($staff_id, $hire_type): string
    {
        //获取班次
        $shiftServer = new HrShiftServer();
        $shift_info  = $shiftServer->getShiftInfos($staff_id);
        $today       = date('Y-m-d');
        $shift_info  = $shift_info[$today] ?? [];
        $shift_name  = '';
        if (!empty($shift_info)) {
            if (!empty($shift_info['shift_type'])) {
                $key        = 'shift_' . strtolower($shift_info['shift_type']);
                $shift_name = $this->getTranslation()->_($key);
            }
            $start      = empty($shift_info['start']) ? '' : $shift_info['start'];
            $end        = empty($shift_info['end']) ? '' : $shift_info['end'];
            $shift_name = empty($start) ? '' : "{$shift_name}: {$start} - {$end}";
        }
        return $shift_name;
    }


    /**
     * 获取个人信息
     * @param array $paramIn
     * @return array
     */
    public function getPerson($paramIn = [])
    {
        $staff_id = $paramIn['staff_id'];

        $staff_model = new StaffRepository($this->lang);
        $info = $staff_model->getStaffPosition($staff_id);
        $returnData['data'] = array();
        if (empty($info)) {
            return $this->checkReturn($returnData);
        }

        //查看支援信息 换成主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timeZone))->getSupportInfoBySubStaff($staff_id);
        if ($supportStaffInfo) {
            $masterStaffInfo   = $staff_model->getStaffPosition($supportStaffInfo['staff_info_id']);
            $info['bank_no']   = $masterStaffInfo['bank_no'];
            $info['bank_type'] = $masterStaffInfo['bank_type'];
        }

        //获取上级信息
        $ot_model     = new OvertimeRepository($this->timezone);
        $manager_id   = $ot_model->getHigherStaffId(['staff_info_id' => $staff_id]);
        $manager_id   = intval($manager_id['value']);
        $manager_info = $staff_model->getStaffPosition($manager_id);

        $staff_car_no = $staff_model->getAvatar($staff_id, 'CAR_NO');

        $person_data['name']           = $info['name'];
        $person_data['name_en']        = $info['name_en'];
        $person_data['signing_date']   = $info['signing_date'];
        $person_data['job_number']     = $info['staff_info_id'];
        $person_data['entry_date']     = empty($info['hire_date']) ? '' : date('Y-m-d', strtotime($info['hire_date']));
        $person_data['department_id']  = $info['sys_department_id'];
        $person_data['store_id']       = $info['sys_store_id'];
        $person_data['position_id']    = $info['job_title'];
        $person_data['superior_id']    = empty($manager_info) ? '' : $manager_info['staff_info_id'];
        $person_data['superior_name']  = empty($manager_info) ? '' : $manager_info['name'];
        $person_data['id_number']      = $info['identity'];
        $person_data['mobile']         = $info['mobile'];
        $person_data['formal']         = intval($info['formal']);
        $person_data['bank_no']        = $info['bank_no'];
        $person_data['nick_name']      = $info['nick_name'];
        $person_data['hire_type']      = $info['hire_type'];
        [
            $person_data['mobile_company'],
            $person_data['is_show_mobile_company'],
        ] = self::isEditCompanyMobile($info['formal'],$info['is_sub_staff'], $info['hire_type'], $info['mobile_company']);

        //新增银行卡
        $type         = intval($info['bank_type']);
        $default_type = getCountryDefaultBank();
        $type         = empty($type) ? $default_type : $type; //银行ID

        //获取银行名称
        $bank      = BanklistModel::findFirst($type);
        $bank_name = $bank->bank_name ?? '';

        $person_data['bank_type'] = $type; //银行ID

        $person_data['bank'] = $bank_name; //银行名称


        $person_data['department_name'] = $info['depart_name'];
        $staff_bank_no_name            = $staff_model->getAvatar($staff_id, 'BANK_NO_NAME');
        $person_data['bank_account']   = $staff_bank_no_name;
        $person_data['email']          = $info['email'] ?? '';
        $person_data['personal_email'] = $info['personal_email'] ?? '';
        if ($info['sys_store_id'] != -1) {
            $store_info = $staff_model->getStaffStoreInfo($info['sys_store_id']);
        }
        $person_data['store_category'] = empty($store_info) ? '' : $store_info['category'];
        $person_data['store_name']    = empty($store_info) ? '' : $store_info['name'];
        $person_data['position_name'] = $info['job_name'];
        $person_data['car_no']        = $staff_car_no;

//            $sever = Tools::reBuildCountryInstance(new self($this->lang,$this->timezone),[$this->lang,$this->timezone]);
        //获取班次 这字段去掉
        $person_data['shift_info'] = '';

        //新增 职等 和职级
        $level                          = $staff_model::$level;
        $person_data['job_title_grade'] = $info['job_title_grade_v2'];
        $person_data['job_title_level'] = $level[$info['job_title_level']] ?? "";
        //获取户籍照号,PTKP状态,税卡号,银行分行名称
        $staffItems                            = HrStaffItemsModel::find([
            'conditions' => ' staff_info_id = :staff_id: and item in ({items:array}) ',
            'bind'       => [
                'staff_id' => $staff_id,
                'items'    => ['BANK_BRANCH_NAME', 'HOUSEHOLD_REGISTRATION', 'PTKP_STATE', 'TAX_CARD'],
            ],
            'columns'    => ['item', 'value'],
        ])->toArray();
        $staffItems                            = array_column($staffItems, 'value', 'item');
        $person_data['bank_branch_name']       = $staffItems['BANK_BRANCH_NAME'] ?? '';                                                                        //银行分行名称
        $person_data['household_registration'] = $staffItems['HOUSEHOLD_REGISTRATION'] ?? '';                                                                  //获取户籍照号
        $person_data['ptkp_state_text']        = !empty($staffItems['PTKP_STATE']) ? $this->getTranslation()->_("ptkp_state_{$staffItems['PTKP_STATE']}") : '';//PTKP状态
        $person_data['tax_card']               = $staffItems['TAX_CARD'] ?? '';                                                                                //税卡号

        //,社保号,医保号
        $person_data['social_security_num']   = $info['social_security_num'] ?? '';
        $person_data['medical_insurance_num'] = $info['medical_insurance_num'] ?? '';

        //试用期状态，1试用期，2已通过，3未通过，4已转正
        $person_data['is_pop'] = in_array($info['status'] ?? ProbationServer::STATUS_PROBATION,
            [ProbationServer::STATUS_PROBATION, ProbationServer::STATUS_NOT_PASS]) ? 0 : 1;
        //是否真实公积金号码填写
        $person_data['epf_no_show'] = isCountry('MY') && $info['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID;
        //是否有底片
        $person_data['is_has_face_negatives'] = !empty((new AttendanceRepository($this->lang, $this->timeZone))->get_attendance_photo($staff_id));

        $person_data['is_lnt'] = (new StaffServer())->isLntStaff($staff_id);

        $identity_annex    = $this->get_staff_identity_annex($paramIn);
        $person_data         = array_merge($person_data, $identity_annex);
        if (isCountry('My')) {
            $person_data          = array_merge($person_data,
                $this->resignationNotice($info, $staff_id));
            $findEpf                      = StaffSalaryEpfSocsoModel::findFirst([
                'columns'    => 'epf_no',
                'conditions' => 'staff_info_id =:staff_info_id:',
                'bind'       => ['staff_info_id' => $staff_id],
            ]);
            $person_data['epf_no'] = empty($findEpf) ? '' : $findEpf->epf_no;
        }

        return $this->checkReturn(['data' => $person_data]);
    }

    /**
     * 是否修改企业号码
     * @param $formal
     * @param $is_sub_staff
     * @param $hire_type
     * @param $mobile_company
     * @return array
     */
    public static function isEditCompanyMobile($formal, $is_sub_staff, $hire_type, $mobile_company): array
    {
        if (!isCountry('TH')) {
            return [strval($mobile_company), true];
        }

        if ($is_sub_staff == 0 && in_array($formal,
                [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN]) && in_array($hire_type, [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
                HrStaffInfoModel::HIRE_TYPE_3,
                HrStaffInfoModel::HIRE_TYPE_4,
                HrStaffInfoModel::HIRE_TYPE_5,
            ])) {
            return [strval($mobile_company), true];
        }
        return ['', false];
    }


    /**
     * 获取员工基本信息
     * @param array $paramIn
     * @return array
     */
    public function getStaff($paramIn = [])
    {
        $staff_id = $paramIn['staff_id'];

        //查看支援信息 换成主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timeZone))->getSupportInfoBySubStaff($staff_id);
        if (!empty($supportStaffInfo)) {
            $staff_id = $supportStaffInfo['staff_info_id'];
        }

        $staff_model = new StaffRepository($this->lang);
        $info        = $staff_model->getStaffPosition($staff_id);
        if (empty($info)) {
            return [];
        }

        $config = (new SettingEnvServer())->listByCode(['No_Show_Regulation_CompanyID','No_Show_Help_CompanyID']);
        $config = !empty($config) ? array_column($config,'set_val','code') : [];

        $show_regulation = (empty($config['No_Show_Regulation_CompanyID']) || !in_array($info['contract_company_id'], explode(',',$config['No_Show_Regulation_CompanyID']))) ? true : false;
        $show_show_help = (empty($config['No_Show_Help_CompanyID']) || !in_array($info['contract_company_id'], explode(',',$config['No_Show_Help_CompanyID']))) ? true : false;

        //kit 是否展示
        $info['is_show_regulation'] = $show_regulation;//显示规章制度:true 展示, false 不展示
        $info['is_show_help']       = $show_show_help;//显示帮助与客服:true 展示, false 不展示
        $info['is_show_inquiry']    = (new StaffServer())->isLntStaff($staff_id);//LNT 展示 inquiry, 非LNT 展示 flash box

        return $info;
    }

    /**
     * @param $info
     * @param $staffId
     * @param bool $isAfter 是否固定 取转正后 不看员工状态
     * @return array
     */
    public function resignationNotice($info, $staffId, $isAfter = false)
    {
        $probation = HrProbationModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $staffId],
        ]);
        $probation = $probation ? $probation->toArray() : [];
        //马来转正评估 发送pdf 固定取转正后 不管员工状态
        if(isCountry('MY') && $isAfter === true){
            $probation['status'] = HrProbationModel::STATUS_FORMAL;
        }

        if ($info['formal'] == 4) {
            // 实习生
            return [];
        }
        $result = [];
        if ($info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_1) {
            // 正式员工
            if ($info['job_title_grade_v2'] == 16) {
                //职级=16 试用期和转正后都是30天
                $resignation        = 1 . $this->getTranslation()->_('monthly');
                $resignation_notice = StaffResignModel::resignation_notice_1_monthly;
                $result             = $this->getResignationNoticeResult($resignation, $resignation_notice);
            } else {
                if ($info['job_title_grade_v2'] == 17) {
                    //职级=17 试用期30天 转正后45天
                    if ($probation && $probation['status'] == HrProbationModel::STATUS_FORMAL) {
                        // 已转正
                        $resignation        = '1.5' . $this->getTranslation()->_('monthlies');
                        $resignation_notice = StaffResignModel::resignation_notice_1_5_monthly;
                    } else {
                        $resignation        = 1 . $this->getTranslation()->_('monthly');
                        $resignation_notice = StaffResignModel::resignation_notice_1_monthly;
                    }
                    $result = $this->getResignationNoticeResult($resignation, $resignation_notice);
                } else {
                    if ($info['job_title_grade_v2'] > 17) {
                        if ($probation && $probation['status'] == HrProbationModel::STATUS_FORMAL) {
                            // 以转正
                            $resignation        = 2 . $this->getTranslation()->_('monthlies');
                            $resignation_notice = StaffResignModel::resignation_notice_2_monthlies;
                        } else {
                            $resignation        = 1 . $this->getTranslation()->_('monthly');
                            $resignation_notice = StaffResignModel::resignation_notice_1_monthly;
                        }
                        $result = $this->getResignationNoticeResult($resignation, $resignation_notice);
                    } else {
                        if ($info['job_title_grade_v2'] && $info['job_title_grade_v2'] <= 15) {
                            if ($probation && $probation['status'] == HrProbationModel::STATUS_FORMAL) {
                                // 以转正
                                $resignation        = 1 . $this->getTranslation()->_('monthly');
                                $resignation_notice = StaffResignModel::resignation_notice_1_monthly;
                            } else {
                                $resignation        = 2 . $this->getTranslation()->_('weeklies');
                                $resignation_notice = StaffResignModel::resignation_notice_2_weeklies;
                            }
                            $result = $this->getResignationNoticeResult($resignation, $resignation_notice);
                        }
                    }
                }
            }

            $settingEnvServer = new SettingEnvServer();
            $jobIds           = $settingEnvServer->getSetVal('resignation_notice_first_line_jobs');
            $jobIds           = explode(',', $jobIds);

            if (in_array($info['job_title'], $jobIds)) {
                if ($probation && $probation['status'] == HrProbationModel::STATUS_FORMAL) {
                    // 以转正
                    $resignation        = 2 . $this->getTranslation()->_('weeklies');
                    $resignation_notice = StaffResignModel::resignation_notice_2_weeklies;
                } else {
                    $resignation        = 1 . $this->getTranslation()->_('weekly');
                    $resignation_notice = StaffResignModel::resignation_notice_1_weekly;
                }
                $result = $this->getResignationNoticeResult($resignation, $resignation_notice);
            }
        } elseif ($info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            $resignation        = StaffResignModel::resignation_notice_15_day . $this->getTranslation()->_('day');
            $resignation_notice = StaffResignModel::resignation_notice_15_days;
            $result             = $this->getResignationNoticeResult($resignation, $resignation_notice);
        } elseif ($info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT) {
            $resignation        = '';
            $resignation_notice = '';
            $result             = $this->getResignationNoticeResult($resignation, $resignation_notice);
        } else {
            $resignation        = 1 . $this->getTranslation()->_('weekly');
            $resignation_notice = StaffResignModel::resignation_notice_1_weekly;
            $result             = $this->getResignationNoticeResult($resignation, $resignation_notice);
        }

        return $result;
    }

    public function getResignationNoticeResult($resignation, $resignation_notice)
    {
        $days = StaffResignModel::$short_notice[$resignation_notice] ?? 1;
        return [
            'resignation'        => $resignation,
            'resignation_notice' => $resignation_notice,
            'leave_day'          => date('Y-m-d', strtotime(" +{$days} days")),
            'work_day'           => date('Y-m-d', strtotime(" +{$days} days -1 days")),
        ];
    }

    public function calculDay($addDayStr, $testDay = '')
    {
        $add_hour   = $this->getDI()['config']['application']['add_hour'];
        $nextMonth  = gmdate('Y-m',
            strtotime(substr($testDay, 0, -3) . "-01 " . $addDayStr) + $add_hour * 3600); // 获取下个月的月份
        $currentDay = gmdate('d', strtotime($testDay) + $add_hour * 3600);                // 当前日期

        $nextDay = gmdate("Y-m-d", strtotime($testDay . " " . $addDayStr) + $add_hour * 3600);
        if ($currentDay > gmdate('t', strtotime($nextMonth . "-01") + $add_hour * 3600)) {
            // 当前日期 大于 下个月份的月末日期 取月末日期
            $nextDay = $nextMonth . "-" . gmdate('t', strtotime($nextMonth) + $add_hour * 3600);
        }

        return $nextDay;
    }


    public function update_name_en($param)
    {
        $en_name = filter_param($param['name_en']);
        if (empty($en_name) || empty($param['staff_id'])) {
            return $this->checkReturn(['msg' => $this->getTranslation()->_('miss_args')]);
        }

        $re   = new StaffRepository($this->lang);
        $flag = $re->update_name_en($param['staff_id'], $en_name);
        $this->getDI()->get('logger')->write_log('update_name -' . json_encode($param, JSON_UNESCAPED_UNICODE), 'info');
        return $flag;
    }


    /**
     * 修改基本资料
     * @Access  public
     * @Param   request
     * @Return  array
     * @throws ValidationException
     */
    public function updatePersonInfobymobile($paramIn = [])
    {
        //[1]参数定义
        $mobile         = empty($paramIn['mobile']) ? '' : $paramIn['mobile'];
        $bank_no        = empty($paramIn['bank_no']) ? '' : $paramIn['bank_no'];
        $id_number      = empty($paramIn['id_number']) ? '' : $paramIn['id_number'];
        $personal_email = empty($paramIn['personal_email']) ? '' : $paramIn['personal_email'];
        $backup_bank_no = empty($paramIn['backup_bank_no']) ? '' : $paramIn['backup_bank_no'];

        if (!empty($mobile)) {
            $PostData['mobile'] = $mobile;
        }
        if (!empty($bank_no)) {
            $PostData['bank_type'] = isset($paramIn['bank_type']) ? intval($paramIn['bank_type']) : 0;
            $PostData['bank_no']   = $bank_no;
        }
        if (!empty($id_number)) {
            $PostData['identity'] = $id_number;
        }
        if (isset($paramIn['nick_name'])) {
            $nick_name             = empty($paramIn['nick_name']) ? '' : $paramIn['nick_name'];
            $PostData['nick_name'] = $nick_name;
        }
        if (isset($paramIn['personal_email'])) {
            $PostData['personal_email'] = $personal_email;
        }
        if (isset($backup_bank_no) && $backup_bank_no) {
            $PostData['backup_bank_no'] = $backup_bank_no;
        }
        //税卡号
        if (isset($paramIn['tax_card'])) {
            $PostData['tax_card'] = $paramIn['tax_card'];
        }
        //银行分行名称
        if (isset($paramIn['bank_branch_name'])) {
            $PostData['bank_branch_name'] = $paramIn['bank_branch_name'];
        }

        // 允许为空的字段
        if (isset($paramIn['empty_field']) && is_array($paramIn['empty_field']) && !empty($paramIn['empty_field'])) {
            $PostData['empty_field'] = $paramIn['empty_field'];
        }

        if (!empty($PostData)) {
            $PostData['fbid'] = $paramIn['staff_id'];
            $apiClient        = new ApiClient('hr_rpc', '', 'sync-item', $this->lang);
            $apiClient->setParams($PostData);
            $result = $apiClient->execute();
            if (isset($result['result']['code']) && $result['result']['code'] == 0) {
                return $this->checkReturn(['msg' => $this->getTranslation()->_('5002')]);
            } else {
                return $this->checkReturn([
                    'code' => -3,
                    'msg'  => $result['result']['msg'] ?? $this->getTranslation()->_('update_person_info_fail'),
                ]);
            }
        }

        return $this->checkReturn(['msg' => $this->getTranslation()->_('5002')]);
    }

    public function modifyHrStaffIdentityAnnex($params)
    {
        $db = $this->getDI()->get("db");
        $db->begin();
        try {
            $annexInfo = HrStaffAnnexInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: AND type = :type:',
                'bind'       => [
                    'staff_info_id' => $params['staff_id'],
                    'type'          => HrStaffAnnexInfoModel::TYPE_BANK_CARD,
                ],
            ]);

            // 如果未查询到该用户信息则新建
            if (empty($annexInfo)) {
                $annexInfo                = new HrStaffAnnexInfoModel();
                $annexInfo->staff_info_id = $params['staff_id'];
                $annexInfo->type          = HrStaffAnnexInfoModel::TYPE_BANK_CARD;
            }

            if (isCountry([
                    'TH',
                    'MY',
                ]) && isset($params['bank_card_photo']) && !empty(trim($params['bank_card_photo']))) {
                $annexInfo->annex_path_front = trim($params['bank_card_photo']);
                $annexInfo->card_number      = trim($params['bank_no']);
                if (isset($params['ai_audit_state']) && $params['ai_audit_state'] == 1) {
                    $annexInfo->audit_state_date    = date('Y-m-d H:i:s');
                    $annexInfo->audit_staff_info_id = 10000;
                    $annexInfo->ai_audit_state      = 1;                                         // ai审核状态 1通过 2未通过
                    $annexInfo->audit_state         = HrStaffAnnexInfoModel::AUDIT_STATE_PASSED; // 审核通过
                } else {
                    $annexInfo->ai_audit_state = 2;                                               // ai审核状态 1通过 2未通过
                    $annexInfo->audit_state    = HrStaffAnnexInfoModel::AUDIT_STATE_NOT_REVIEWED; // 待审核
                }
                $annexInfo->ai_audit_state_date = date('Y-m-d H:i:s');
                if ($annexInfo->save() !== true) {
                    throw new Exception('HrStaffAnnexInfoModel update error ');
                }

                $conditions = 'staff_info_id = :staff_info_id: and type = :type: ';
                $bind       = [
                    'staff_info_id' => $params['staff_id'],
                    'type'          => StaffInfoAuditCheckModel::TYPE_BANK_CAR,
                ];

                //ai识别后，提交 将最后一次 ai识别到的数据，更新到 ai_recognition_end_data 字段，给 人工审核时做参考
                $auditInfo = StaffInfoAuditCheckRepository::getStaffAuditCheckInfo('*', $conditions, $bind);
                if (!empty($auditInfo) && !empty($auditInfo['ai_recognition_data']) && isCountry('TH')) {
                    $updateData['ai_recognition_end_data'] = $auditInfo['ai_recognition_data'];
                    $flag                                  = $db->updateAsDict("staff_info_audit_check", $updateData,
                        ["conditions" => "id='{$auditInfo['id']}'"]);
                    if (!$flag) {
                        throw new Exception('staff_info_audit_check update error ');
                    }
                }
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();
        }
        return true;
    }


    public function updateMobileCompany($paramIn = [])
    {
        try {
            $staffId        = $paramIn['staff_id'];
            $mobile_company = empty($paramIn['mobile_company']) ? '' : $paramIn['mobile_company'];

            if (!empty($mobile_company)) {
                if (!isCountry()) {
                    $mobileMaxLen = 11;
                    $mobileMixLen = 10;
                } else {
                    $mobileMaxLen = 10;
                    $mobileMixLen = 10;
                }
                if (!preg_match("/(^[0-9]{{$mobileMixLen},{$mobileMaxLen}}$)/u", $mobile_company)) {
                    return $this->checkReturn(-3, 'mobile company wrong format');
                }

                $sql        = "select staff_info_id,mobile_company from hr_staff_info where staff_info_id != :staff_info_id and mobile_company = :mobile_company and formal in (1, 4) and is_sub_staff = 0 and state in (1, 3);";
                $staff_list = $this->getDI()->get('db_rby')->query($sql, [
                    'staff_info_id'  => $staffId,
                    'mobile_company' => $mobile_company,
                ])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($staff_list)) {
                    $staff_ids = implode(",", array_column($staff_list, 'staff_info_id'));
                    $msg       = str_replace('{staff_info_id}', $staff_ids,
                        $this->getTranslation()->_('update_mobile_company_error_1'));
                    $this->getDI()->get('logger')->write_log('企业手机号' . $paramIn['mobile_company'] . '被使用，工号：' . $staff_ids,
                        'info');
                    return $this->checkReturn(-3, $msg);
                }
            }

            $ac = new ApiClient('hr_rpc', '', 'update_mobile_company');
            $ac->setParams([
                "staff_info_id"  => $staffId,
                "mobile_company" => $mobile_company,
            ]);
            $ac_result = $ac->execute();
            if ($ac_result["result"]['code'] == 1) {
                return $this->checkReturn(['msg' => $this->getTranslation()->_('5002')]);
            } else {
                return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('updateMobileCompany:' . $e->getMessage() . '--行号：' . $e->getLine());
            return $this->checkReturn(['msg' => $this->getTranslation()->_('5002')]);
        }
    }



    /**
     * 修改个人号码
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function updatePersonMobile($params): array
    {
        //人脸认证
        (new FaceCompareServer($this->lang, $this->timeZone))->checkTicket($params['ticket'], $params['staff_id']);
        //没抛异常就是验证通过了
        (new SmsServer($this->lang, $this->timeZone))->checkSmsCode(SmsServer::SMS_BIZ_TYPE_EDIT_PERSON_MOBILE,
            $params['mobile'],
            $params['verify_code']);

        return $this->updatePersonInfobymobile($params);
    }

    /**
     * 修改个人号码验证
     * @param $staff_info_id
     * @param $mobile
     * @return true
     * @throws BusinessException
     */
    public function checkUpdatePersonMobile($staff_info_id,$mobile): bool
    {
        $mobile = get_country_full_mobile($mobile);
        //个人号码不能和在职停职待离职主账号编制实习生员工的个人号码一致
        $check1 = HrStaffInfoModel::findFirst([
            'conditions' => 'mobile = :mobile: and formal in ({formal:array}) and is_sub_staff = 0 and state in ({state:array})',
            'bind'       => [
                'mobile' => trim($mobile),
                'formal' => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                'state'  => [HrStaffInfoModel::STATE_1, HrStaffInfoModel::STATE_3],
            ],
        ]);

        if (!empty($check1) && $check1->staff_info_id == $staff_info_id) {
            throw new BusinessException($this->getTranslation()->_('mobile_no_change'));
        }

        if (!empty($check1)) {
            //个人号码与其他在职员工重复，请重新输入
            throw new BusinessException($this->getTranslation()->_('person_phone_check_1'));
        }

        if (isCountry('TH')) {
            $check2 = HrStaffInfoModel::count([
                'conditions' => 'mobile_company = :mobile: and formal in ({formal:array}) and is_sub_staff = 0 and state in ({state:array})',
                'bind'       => [
                    'mobile' => trim($mobile),
                    'formal' => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                    'state'  => [HrStaffInfoModel::STATE_1, HrStaffInfoModel::STATE_3],
                ],
            ]);
            if (!empty($check2)) {
                //个人号码与企业号码重复，请重新输入
                throw new BusinessException($this->getTranslation()->_('person_phone_check_2'));
            }
        }
        return true;

    }


    /**
     * 修改个人邮箱
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function updatePersonEmail($params): array
    {
        //人脸验证
        (new FaceCompareServer($this->lang, $this->timeZone))->checkTicket($params['ticket'], $params['staff_id']);

        //验证码验证
        $this->checkMailCode($params['staff_id'],$params['personal_email'],$params['verify_code']);
        return $this->updatePersonInfobymobile($params);
    }

    /**
     * 修改银行卡信息
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function updateBankInfo($params): array
    {
        if (!empty($params['bank_no']) && !empty($params['bank_type'])){
            (new SysServer($this->lang, $this->timezone))->bankNoVerify($params['bank_type'],$params['bank_no']);
        }
        //人脸认证
        (new FaceCompareServer($this->lang, $this->timeZone))->checkTicket($params['ticket'], $params['staff_id']);
        //差异化
        $this->modifyHrStaffIdentityAnnex($params);

        return $this->updatePersonInfobymobile($params);
    }


    /**
     * staffMobileAddS
     * @Access  public
     * @Param   request
     * @Return  info
     */
    public function staffMobileAddS($params)
    {
        $paramIn['staff_id']     = $params['staff_id'];
        $paramIn['staff_mobile'] = $params['staff_mobile'];
        $paramIn['create_id']    = $params['staff_info_id'];
        $result                  = $this->getDI()->get('db')->insertAsDict(
            'staff_mobile', $paramIn
        );
        return $result;
    }

    /**
     * staffMobileInfoS
     * @Access  public
     * @Param   request
     * @Return  info
     */
    public function staffMobileInfoS($params)
    {
        $sql     = "select * from staff_mobile where create_id = " . $params['staff_info_id'];
        $dataObj = $this->getDI()->get('db')->query($sql);
        $result  = $dataObj->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $result;
    }


    //获取该员工 工资条 最早和最晚 月份 以及 个人邮箱和企业邮箱
    public function salary_need($staff_info_id)
    {
        $sql = "select max(a.excel_month) as max_date ,min(a.excel_month) as min_date, s.email,s.personal_email
                from hr_staff_info s
                left join salary_gongzi a on s.staff_info_id = a.staff_info_id
                where s.staff_info_id = {$staff_info_id}
                and status = 3
                group by s.staff_info_id
                ";

        return $this->db_rby->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    //获取员工基本信息 和薪资构成信息 在职证明 预览接口哟过
    public function on_job_need($param)
    {
        //个人信息
        $staff_model = new StaffRepository($this->lang);
        $info        = $staff_model->getStaffPosition($param['staff_id']);
        if (empty($info)) {
            return false;
        }

        $PostData['fbid'] = $param['staff_id'];
        $apiClient        = new ApiClient('hr_rpc', '', 'staffs-salary', $this->lang);
        $apiClient->setParams($PostData);
        $result = $apiClient->execute();
        if ($result['code'] == 1) {
            $salary_data = $result['result'];
        }
        $return = [];
        if (isset($salary_data) && $salary_data['code'] === 0 && !empty($salary_data['body'])) {
            $return['base_salary'] = $salary_data['body']['base_salary'];
        }

        $return['name']    = $info['name'];
        $return['name_en'] = $info['name_en'];

        $return['hire_date']      = $info['hire_date'];
        $return['job_name']       = $info['job_name'];
        $return['email']          = $info['email'];
        $return['personal_email'] = $info['personal_email'];
        $return['depart_name']    = $info['depart_name'];

        //入职日期
        $return['hire_year_th']  = date('Y', strtotime($info['hire_date'])) + 543;
        $month_key               = intval(date('m', strtotime($info['hire_date'])));
        $return['hire_month_th'] = StaffRepository::$month_th[$month_key];

        //日期格式改为日+月（文字）+佛历年份 ，如 4  มิถุนายน พ.ศ. 2563；
        $month               = intval(date('m', time()));
        $return['send_date'] = intval(date('d', time())) . ' ' . StaffRepository::$month_th[$month] . ' ' . (date('Y',
                    time()) + 543);

        return $return;
    }

    /**
     * 更换邮箱
     * 发送邮箱验证码
     * @param $param -- staff_id  e_mail 手动输入的邮箱 针对修改邮箱且没有个人邮箱时候用
     * @throws ValidationException
     */
    public function send_mail($param)
    {
        $t        = $this->getTranslation();
        $staff_id = $param['staff_id'];
        $mail     = $param['e_mail'];
        $code     = verificationCode();//生成验证码
        //存redis
        if (!$this->setEmailCode($staff_id, $mail, $code)) {
            throw new ValidationException($t->_('server_error'));
        }

        //发送
        $body        = $this->mail_code_format($code);
        $mail_server = new MailServer();
        if ((new StaffServer())->isLntStaff($staff_id)) {
            $res = $mail_server->send_mail_lnt($mail, $body['title'], $body['content']);
        } else {
            $res = $mail_server->send_mail($mail, $body['title'], $body['content']);
        }

        if (!$res) {
            throw new ValidationException($t->_('server_error'));
        }
        $this->logger->write_log(['param' => $param, 'res' => $res, 'code' => $code], 'info');
        $data = '';
        if (get_runtime() == 'dev') {
            $data = $code;
        }
        return $this->checkReturn([
            'data' => $data,
            'code' => ErrCode::SUCCESS,
            'msg'  => $t->_('verify_code_new_email', ['email' => $mail]),
        ]);
    }

    /**
     * 更换邮箱
     * 邮箱验证
     * @param $staff_id
     * @param $email
     * @return string
     */
    protected function getEmailCheckKey($staff_id,$email): string
    {
        return 'MAIL_CODE_' . $staff_id.'_'.$email;
    }

    /**
     * 更换邮箱
     * 保存临时code进缓存
     * @param $staff_id
     * @param $email
     * @param $code
     * @return void
     */
    protected function setEmailCode($staff_id, $email, $code)
    {
        $cache = $this->getDI()->get('redisLib');
        $cache1 = $this->getDI()->get('redis');
        $cache1->save('MAIL_CODE_' . $staff_id,$code,600);
        return $cache->set($this->getEmailCheckKey($staff_id, $email), $code, 320);
    }

    /**
     * 更换邮箱
     * 验证邮箱验证码
     * @param $staff_id
     * @param $email
     * @param $code
     * @return true
     * @throws ValidationException
     */
    protected function checkMailCode($staff_id, $email, $code): bool
    {
        $cache      = $this->getDI()->get('redisLib');
        $key        = $this->getemailCheckKey($staff_id, $email);
        $cache_code = $cache->get($key);
        if (empty($cache_code) || $cache_code != $code) {
            throw new ValidationException($this->getTranslation()->_('auth_code_error'));
        }
        return true;
    }


    //邮箱验证码 格式
    public function mail_code_format($code)
    {
        $return['title']   = $this->getTranslation()->_('mail_code_title');
        $return['content'] = $this->getTranslation()->_('mail_code_content');
        $return['content'] = str_replace('{code}', $code, $return['content']);
        return $return;
    }


    //保存 用户设备信息
    public function save_device($param)
    {
        $param['current_time'] = date('Y-m-d H:i:s', time());//当前国家时间

        if (empty($param['staff_info_id']) || empty($param['equipment_type']) || empty($param['client_id'])) {
            return 'param missed';
        }

        $info = StaffDeviceInfo::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and equipment_type = :equipment_type: and client_id = :client_id:",
            'bind'       => [
                'staff_info_id'  => $param['staff_info_id'],
                'equipment_type' => $param['equipment_type'],
                'client_id'      => $param['client_id'],
            ],
        ]);
        if (!empty($info)) {
            $info->current_time = $param['current_time'];
            $info->device_model = $param['device_model'];
//            $info->equipment_type = $param['equipment_type'];
            $info->current_ip   = $param['current_ip'];
            $info->network_type = $param['network_type'];
            $info->lat          = $param['lat'];
            $info->lng          = $param['lng'];
            $info->version      = $param['version'];
            $info->os           = $param['os'];
//            $info->client_id = $param['client_id'];
            $info->device_id = $param['device_id'];
            return $info->update();
        }
        $model = new StaffDeviceInfo();
        return $model->create($param);
    }


    /**
     * 版本号信息 和 对应的下载地址
     * kit 不迁移 所以这里面只有 by
     * @param $equipment_type
     * @param $os
     * @param $client_version
     * @param $user_info
     * @return array
     */
    public function get_version($equipment_type, $os, $client_version, $user_info)
    {
        //灰度开关打开 -》 验证灰度名单 在灰度 比对最新版本号  不在灰度 比对全量版本号
        //灰度开关关闭 -》 验证全量版本号 更新全量版本
        $os = strtolower($os);
        if (strstr($os, 'android')) {
            $os = 'android';
        }
        if (strstr($os, 'ios')) {
            $os = 'ios';
        }
        $this->logger->write_log("get_version {$user_info['id']} {$os}_{$equipment_type} {$client_version}", 'info');
        $return = ['version' => '', 'download_url' => '', 'is_update' => false, 'release_note' => ''];


        //获取 更新提示语
        $configList = (new SettingEnvServer())->getMultiEnvByCode(['by_ui_version', 'release_note', 'version_switch']);
        //前段ui版本
        $return['by_ui_version'] = $configList['by_ui_version'];

        $notice = $configList['release_note'] ?? '';
        if (!empty($notice)) {
            $return['release_note'] = $notice;
        }

        // code  ios_3 ios 只有 by android_1 kit（kit 不迁移） android_3 by 所以
        $code = strtoupper($os) . '_' . $equipment_type;

        //有个人特殊 永远不提示升级 IOS
        $special_man = SettingEnvModel::findFirst("code = 'UNUP_{$code}'");
        if (!empty($special_man) && !empty($special_man->set_val) && ($special_man->set_val == $user_info['id'])) {
            return $return;
        }

        $setting = SettingEnvModel::findFirst("code = '{$code}'");

        if (empty($setting)) {
            return $return;
        }
        $lasted_version = $setting->set_val;//最新版本号
        $switch         = $configList['version_switch'] ?? 0;

        if (!empty($switch)) {//灰度的控制
            //获取灰度名单  && strtoupper($os) == 'ANDROID'
            $name_list_code = 'LIST_' . $code;
            $name_list      = SettingEnvModel::findFirst("code = '{$name_list_code}'");
            if (!empty($name_list) && !empty($name_list->set_val)) {
                $name_list = explode(',', $name_list->set_val);
                if (in_array($user_info['id'], $name_list)) {//在灰度名单里面 比对客户端版本号和灰度版本号
                    //获取灰度版本号 GREY_ANDROID_3
                    $grey_version = SettingEnvModel::findFirst("code = 'GREY_{$code}'");
                    $grey_version = $grey_version->set_val;//灰度版本号
                    if (!empty($grey_version) && check_version($client_version, $grey_version)) {
                        //返回灰度链接 ios 返回的是空
                        $return['is_update'] = true;
                        $return['version']   = $grey_version;
                        $grey_url            = SettingEnvModel::findFirst("code = 'GREY_URL_{$code}'");
                        if (!empty($grey_url) && !empty($grey_url->set_val))//下载地址和 版本更新
                        {
                            $return['download_url'] = $grey_url->set_val;
                        }

                        $this->logger->write_log("grey_version {$user_info['id']} {$client_version}_{$grey_version} " . json_encode($return),
                            'info');
                        return $return;
                    }
                }
            }
        }

        //灰度开关关闭 比对用户版本和 全量版本号
        if (!empty($lasted_version) && check_version($client_version, $lasted_version)) {
            $return['is_update'] = true;
            $return['version']   = $lasted_version;
            $all_url             = SettingEnvModel::findFirst("code = 'ALL_URL_{$code}'");
            if (!empty($all_url) && !empty($all_url->set_val)) {
                $return['download_url'] = $all_url->set_val;
            }

            $this->logger->write_log("all_version {$user_info['id']} {$client_version}_{$lasted_version} " . json_encode($return),
                'info');
        }

        return $return;
    }

    /**
     * 调用接口识别身份证
     * @param $id_card_url
     * @return array
     */
    public function ai_id_card_ocr_post($id_card_url)
    {
        try {
            $this->getDI()->get('logger')->write_log('ai_id_card_ocr_post  参数：' . $id_card_url, 'info');
            $setting_env_info = SettingEnvModel::find([
                'conditions' => 'code in ({code:array})',
                'bind'       => [
                    'code' => [
                        'ai_id_card_ocr_domain',
                        'ai_id_card_ocr_url',
                        'ai_id_card_ocr_secret_id',
                        'ai_id_card_ocr_secret_key',
                    ],
                ],
            ])->toArray();

            if (empty($setting_env_info)) {
                $this->getDI()->get('logger')->write_log('ai_id_card_ocr_post---未配置setting env');
                return $this->checkReturn(-3, 'error');
            }

            $setting_env_info = array_column($setting_env_info, 'set_val', 'code');

            $url        = $setting_env_info['ai_id_card_ocr_domain'] . $setting_env_info['ai_id_card_ocr_url'];
            $secret_id  = $setting_env_info['ai_id_card_ocr_secret_id'];
            $secret_key = $setting_env_info['ai_id_card_ocr_secret_key'];

            $tmp       = intval(microtime(true) * 1000);
            $str_token = "POST" . "\n" . $setting_env_info['ai_id_card_ocr_url'] . "\n" . $tmp . "\n" . $secret_id;
            $t         = hash_hmac("sha1", $str_token, $secret_key, true);
            $token     = "{$tmp}_{$secret_id}_" . base64_encode($t);

            $headers = [
                'X-FLE-Token: ' . $token,
                'X-FC-TIMESTAMP: ' . time(),
                'Content-type: application/x-www-form-urlencoded',
            ];

            $post_date = 'url=' . $id_card_url . '&return_idcard_type=true';
            $ai_result = httpPostFun($url, $post_date, $headers);
            $this->getDI()->get('logger')->write_log('ai_id_card_ocr_post url : ' . $id_card_url . ' 结果:' . json_encode($ai_result),
                'info');
            $returnData['data'] = json_decode($ai_result, true);

            $log = [
                'url'    => $url,
                'param'  => $post_date,
                'env'    => $setting_env_info,
                'result' => $returnData['data'],
            ];
            $this->getDI()->get('logger')->write_log('ai_id_card_ocr_post 结果:' . json_encode($log), 'info');
            return $this->checkReturn($returnData);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('ai_id_card_ocr_post---' . $e->getMessage() . '-----' . $e->getLine() . '-----' . $e->getFile());
            return $this->checkReturn(-3, 'error');
        }
    }

    /**
     * ai 审核身份证
     * 情况说明：1.当前系统是泰国且用户是泰国国籍，走ai审核系统；
     *         2.当前系统是泰国且用户不是泰国国籍，走人工审核系统；
     *         3.当前系统不是泰国国籍，走人工审核系统
     * @param $paramIn
     * @return array
     */
    public function ai_id_card_audit($paramIn)
    {
        try {
            $this->getDI()->get('logger')->write_log('ai_id_card_audit params:' . json_encode($paramIn), 'info');
            $staff_id    = $paramIn['staff_id'];
            $id_card_url = $paramIn['file_url'] ?? '';

            //国籍是泰国的走ai识别 非泰国籍的直接走人工审核
            /*$hr_staff_item = HrStaffItemsModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and item = 'NATIONALITY'",
                'bind' => [
                    'staff_info_id' => $staff_id,
                ]
            ]);
            $hr_staff_item = empty($hr_staff_item) ? [] : $hr_staff_item->toArray();
            */
            //泰国原逻辑不变，新增逻辑：除泰国外其他国家走人工审核逻辑
            $country_code = env('country_code', 'TH');
            $nationality  = 0;
            if ($country_code == 'TH') {  //国家是泰国
                $hr_staff_item = HrStaffItemsModel::findFirst([
                    'conditions' => "staff_info_id = :staff_info_id: and item = 'NATIONALITY'",
                    'bind'       => [
                        'staff_info_id' => $staff_id,
                    ],
                ]);
                $hr_staff_item = empty($hr_staff_item) ? [] : $hr_staff_item->toArray();
                if ($hr_staff_item['value'] == 1) {//当前系统是泰国且员工是泰国国籍
                    $nationality = enums::IS_TH_NATIONALITY;
                }
            }

            //if($hr_staff_item['value'] == 1) {
            if ($nationality == 1) {
                $redis     = $this->getDI()->get('redisLib');
                $redis_key = 'STAFF_AI_ID_CARD_UPLOAD_COUNT_' . $staff_id;
                $redis->expire($redis_key, 30 * 60);
                $upload_count = $redis->incr($redis_key);

                $post_result = $this->ai_id_card_ocr_post($id_card_url);
                $ai_result   = $post_result['data'];

                $staff_model = new StaffRepository($this->lang);
                $info        = $staff_model->getStaffPosition($staff_id);
                //获取生日
                $staffItems       = (new StaffRepository())->getSpecStaffItemsInfo($staff_id, ['BIRTHDAY']);
                $info['birthday'] = !empty($staffItems['BIRTHDAY']) ? $staffItems['BIRTHDAY'] : '';

                $compare_id_card = (new AiServer())->ai_compare([
                    'ai_data'    => $ai_result['result'] ?? [],
                    'staff_info' => $info,
                    'method'     => 'aiCompareIdCard',
                ]);

                //将 ai 识别到的数据保存下来
                $updateData['staff_info_id']       = $staff_id;
                $updateData['ai_recognition_data'] = $compare_id_card ? json_encode($compare_id_card,
                    JSON_UNESCAPED_UNICODE) : '';
                $updateData['type']                = StaffInfoAuditCheckModel::TYPE_IDENTITY;
                $conditions                        = 'staff_info_id = :staff_info_id: and type = :type: ';
                $bind                              = [
                    'staff_info_id' => $staff_id,
                    'type'          => StaffInfoAuditCheckModel::TYPE_IDENTITY,
                ];

                $db        = $this->getDI()->get("db");
                $auditInfo = StaffInfoAuditCheckRepository::getStaffAuditCheckInfo('*', $conditions, $bind);
                if ($auditInfo) {
                    $db->updateAsDict("staff_info_audit_check", $updateData,
                        ["conditions" => "id='{$auditInfo['id']}'"]);
                } else {
                    $db->insertAsDict("staff_info_audit_check", $updateData);
                }

                // 这里之前顺序错了，调整下顺序
                if ($upload_count >= 4) {
                    //未通过审核
                    $msg = $this->getTranslation()->_('id_card_ai_audit_error_5');

                    $returnData['data'] = [
                        'code'   => 1,
                        'msg'    => '',
                        'result' => [
                            'status'       => 2,
                            'upload_count' => $upload_count,
                            'file_url'     => $id_card_url,
                            'msg'          => $msg,
                        ],
                    ];
                    return $this->checkReturn($returnData);
                }

                if (empty($ai_result)) {
                    return $this->checkReturn(-3, 'Review failed, please try again later');
                }
                if (strtoupper($ai_result['status']) == 'ERROR') {
                    $error_msg = '';
                    switch ($ai_result['error']['code']) {
                        case 'IMAGE_SIZE_EXCEED':
                            $error_msg = $this->getTranslation()->_('id_card_ai_audit_error_1');
                            break;
                        case 'LOW_QUALITY':
                            $error_msg = $this->getTranslation()->_('id_card_ai_audit_error_2');//'未检测到身份证，请保证证件无遮挡，并重新上传';
                            break;
                        default:
                            $error_msg = $this->getTranslation()->_('id_card_ai_audit_error_3');//'未检测到身份证，请重新上传';
                    }
                    $returnData['data'] = [
                        'code'   => 0,
                        'msg'    => $error_msg,
                        'result' => [],
                    ];
                    return $this->checkReturn($returnData);
                }

                if (!empty($compare_id_card['is_pass'])) {
                    $redis->delete($redis_key);
                    //通过审核
                    $returnData['data'] = [
                        'code'   => 1,
                        'msg'    => '',
                        'result' => [
                            'status'       => 1,
                            'upload_count' => 1,
                            'file_url'     => $id_card_url,
                            'msg'          => $this->getTranslation()->_('id_card_ai_audit_success'),
                        ],
                    ];
                } else {
                    //未通过审核
                    $msg = $this->getTranslation()->_('id_card_ai_audit_error_5');
                    if ($upload_count < 4) {
                        $msg = $this->getTranslation()->_('id_card_ai_audit_error_4');
                    }
                    $returnData['data'] = [
                        'code'   => 1,
                        'msg'    => '',
                        'result' => [
                            'status'       => 2,
                            'upload_count' => $upload_count,
                            'file_url'     => $id_card_url,
                            'msg'          => $msg,
                        ],
                    ];
                }

                $redis_id_card_key = 'STAFF_AI_ID_CARD_AUDIT_' . $staff_id;
                $redis->set($redis_id_card_key, json_encode($returnData['data']), 600);
                $this->getDI()->get('logger')->write_log('ai_id_card_audit 结果:' . json_encode($returnData), 'info');
            } else {
                $returnData['data'] = [
                    'code'   => 1,
                    'msg'    => '',
                    'result' => [
                        'status'       => 2,
                        'upload_count' => 4,
                        'file_url'     => $id_card_url,
                        'msg'          => $this->getTranslation()->_('id_card_ai_audit_error_6'),
                    ],
                ];
                $this->getDI()->get('logger')->write_log('非泰国籍 ai_id_card_audit 结果:' . json_encode($returnData),
                    'info');
            }
            return $this->checkReturn($returnData);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('ai_id_card_audit---' . $e->getMessage() . '-----' . $e->getLine() . $e->getFile());
            return $this->checkReturn(-3, 'error');
        }
    }

    /**
     * 比较两个字符串
     * @param $aiString
     * @param $staffString
     * @return bool
     */
    public function checkString($aiString, $staffString)
    {
        $aiString    = AiServer::stringSpecialCharsReplace($aiString);
        $staffString = AiServer::stringSpecialCharsReplace($staffString);

        if (empty($aiString) || empty($staffString) || !is_string($aiString) || !is_string($staffString)) {
            return false;
        }
        if (strcasecmp($aiString, $staffString) === 0) {
            return true;
        }
        return false;
    }

    /**
     * 提交ai 审核身份证结果
     * @param $paramIn
     * @return array
     */
    public function ai_id_card_submit($paramIn)
    {
        $db = $this->getDI()->get("db");
        try {
            $db->begin();
            $this->getDI()->get('logger')->write_log('ai_id_card_submit params:' . json_encode($paramIn), 'info');
            $status              = $paramIn['status'] ?? 2;
            $staff_id            = $paramIn['staff_id'];
            $file_url            = $paramIn['file_url'];
            $card_number         = isset($paramIn['card_number']) ? trim($paramIn['card_number']) : '';
            $identity_annex_info = HrStaffAnnexInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: AND type = :type:',
                'bind'       => [
                    'staff_info_id' => $staff_id,
                    // 附件类型：身份证
                    'type'          => HrStaffAnnexInfoModel::TYPE_ID_CARD,
                ],
            ]);

            //记录审核前状态
            $audit_before_state = $identity_annex_info->audit_state ?? 0;

            if (!empty($identity_annex_info)) {
                if (in_array($identity_annex_info->audit_state, [1])) {
                    return $this->checkReturn(-3, '通过审核的不需要再进行上传');
                }
            } else {
//                $identity_annex_info = new HrStaffIdentityAnnexModel();
                $identity_annex_info                = new HrStaffAnnexInfoModel();
                $identity_annex_info->staff_info_id = $staff_id;
                $identity_annex_info->type          = HrStaffAnnexInfoModel::TYPE_ID_CARD;
            }

            $redis              = $this->getDI()->get('redisLib');
            $redis_id_card_key  = 'STAFF_AI_ID_CARD_AUDIT_' . $staff_id;
            $redis_id_card_info = $redis->get($redis_id_card_key);
            if (!empty($redis_id_card_info)) {
                $redis_id_card_info = json_decode($redis_id_card_info, true);
                $status             = $redis_id_card_info['result']['status'];
                $file_url           = $redis_id_card_info['result']['file_url'];
                $redis->delete($redis_id_card_key);
            }

            $identity_annex_info->annex_path_front    = $file_url;
            $identity_annex_info->ai_audit_state      = $status;
            $identity_annex_info->ai_audit_state_date = date('Y-m-d H:i:s');
            // 之前没清除数据
            $identity_annex_info->reject_reason = '';
            if (empty($card_number)) {
                $staffObj = (new HrStaffInfoServer($this->lang, $this->timeZone))->getUserInfoByStaffInfoId($staff_id,
                    'identity,social_security_num,fund_num,medical_insurance_num');
                // 身份证
                $card_number = $staffObj->identity ? trim($staffObj->identity) : "";
            }
            if ($card_number) {
                $identity_annex_info->card_number = $card_number;
            }
            if ($status == 1) {
                $identity_annex_info->audit_state         = 1;
                $identity_annex_info->audit_state_date    = date('Y-m-d H:i:s');
                $identity_annex_info->audit_staff_info_id = 10000;
            } else {
                $identity_annex_info->audit_state         = 0;
                $identity_annex_info->audit_state_date    = null;
                $identity_annex_info->audit_staff_info_id = null;
            }

            if ($identity_annex_info->save() !== true) {
                return $this->checkReturn(-3, 'error');
            }

            $conditions = 'staff_info_id = :staff_info_id: and type = :type: ';
            $bind       = [
                'staff_info_id' => $staff_id,
                'type'          => StaffInfoAuditCheckModel::TYPE_IDENTITY,
            ];

            //ai识别后，提交 将最后一次 ai识别到的数据，更新到 ai_recognition_end_data 字段，给 人工审核时做参考
            $auditInfo = StaffInfoAuditCheckRepository::getStaffAuditCheckInfo('*', $conditions, $bind);
            if (!empty($auditInfo) && !empty($auditInfo['ai_recognition_data']) && isCountry('TH')) {
                $updateData['ai_recognition_end_data'] = $auditInfo['ai_recognition_data'];
                $db->updateAsDict("staff_info_audit_check", $updateData,
                    ["conditions" => "id='{$auditInfo['id']}'"]);
            }

            $this->insertAiAuditLog($staff_id, $audit_before_state, $status);
            $db->commit();
            return $this->checkReturn([]);
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('ai_id_card_submit ---' . $e->getMessage() . '-----' . $e->getLine());
            return $this->checkReturn(-3, 'error');
        }
    }

    /**
     * 记录审核日志--只有泰国有ai审核日志
     * @param $staff_id
     * @param $audit_before_state
     * @param $status
     */
    public function insertAiAuditLog($staff_id, $audit_before_state, $status)
    {
        $country_code = env('country_code', 'TH');
        if ($country_code == 'TH') {
            $audit_log        = [
                'staff_info_id'      => $staff_id,
                'audit_id'           => 10000,
                'audit_name'         => $this->getTranslation()->_('ai_audit'),
                'audit_before_state' => $audit_before_state,
                'audit_after_state'  => $status,
            ];
            $audit_log_result = $this->getDI()->get('db')->insertAsDict('staff_identity_annex_audit_log', $audit_log);
            if (!$audit_log_result) {
                $msg = "identity annex audit log insert fail:" . var_export($audit_log, true) . PHP_EOL;
                $this->getDI()->get("logger")->write_log($msg, 'info');
            }
        }
    }

    /**
     * 获取员工身份证附件信息
     * @param $paramIn
     * @return mixed
     */
    public function get_staff_identity_annex($paramIn)
    {
        //身份证审核认证信息
        $person_data['id_card_upload_status']      = 0;//是否上传
        $person_data['id_card_upload_status_text'] = $this->getTranslation()->_('id_card_upload_status_0');
        $person_data['id_card_audit_status']       = '';//是否审核通过
        $person_data['id_card_audit_status_text']  = '';
        $person_data['id_card_file_url']           = '';//身份证图片地址
        $person_data['reject_reason']              = '';//驳回原因

        try {
            $staff_id  = $paramIn['staff_id'];
            $annexRet  = HrStaffAnnexInfoModel::find([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => ['staff_info_id' => $staff_id],
            ])->toArray();
            $annexList = array_column($annexRet, null, 'type');

            if (!empty($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]) && !is_null($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['audit_state'])) {
                //身份证审核认证信息
                $person_data['id_card_upload_status']      = 1;//是否上传 0未上传 1已上传
                $person_data['id_card_upload_status_text'] = $this->getTranslation()->_('id_card_upload_status_1');
                $audit_state                               = $annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['audit_state'];//是否审核通过 0 未审核 1通过 2 未通过
                $person_data['id_card_audit_status']       = $audit_state;
                $person_data['id_card_audit_status_text']  = $this->getTranslation()->_('id_card_audit_status_' . $audit_state);
                $person_data['id_card_file_url']           = $annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['annex_path_front'];   //身份证图片地址
                $person_data['reject_reason']              = $annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['reject_reason'] ?: "";//驳回原因
            }

            if (isCountry(['TH', 'MY'])) {
                $person_data['bank_card_photo']         = !empty($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['annex_path_front']) ? $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['annex_path_front'] : '';   // 银行卡oss地址
                $person_data['bank_card_reject_reason'] = '';

                $hr_staff_info          = HrStaffInfoModel::findFirst([
                    'conditions' => "staff_info_id = :staff_info_id:",
                    'bind'       => [
                        'staff_info_id' => $staff_id,
                    ],
                    'columns'    => 'bank_no',
                ]);
                $_bank_card_audit_state = isset($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]) && !is_null($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state'])
                    ? $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state']
                    : null;
                // 有号码 有图片 附件状态不为null 附件状态是啥就是啥
                if (is_numeric($_bank_card_audit_state)) {
                    $person_data['bank_card_audit_state']      = (int)$_bank_card_audit_state;
                    $person_data['bank_card_audit_state_text'] = $this->getTranslation()->_(HrStaffAnnexInfoModel::$audit_state_key[$_bank_card_audit_state]);
                } else {
                    // 默认 待上传
                    $person_data['bank_card_audit_state']      = HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
                    $person_data['bank_card_audit_state_text'] = $this->getTranslation()->_(HrStaffAnnexInfoModel::$audit_state_key[HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD]);
                }
                // 拒绝时展示拒绝原因
                if ($person_data['bank_card_audit_state'] == 2 && $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['reject_reason']) {
                    $person_data['bank_card_reject_reason'] = $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['reject_reason'];
                }
            }

            //户口簿审核认证信息
            //户口簿 第一页
            $person_data['residence_booklet_file_url_first'] = !empty($annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['annex_path_front']) ? $annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['annex_path_front'] : '';
            //户口簿 个人信息页
            $person_data['residence_booklet_file_url_second'] = !empty($annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['annex_path_rear']) ? $annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['annex_path_rear'] : '';

            $person_data['residence_booklet_reject_reason'] = '';//户口簿驳回原因

            // 默认 待上传
            $person_data['residence_booklet_audit_status']      = HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
            $person_data['residence_booklet_audit_status_text'] = $this->getTranslation()->_(HrStaffAnnexInfoModel::$audit_state_key[HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD]);

            $residence_booklet_audit_state = isset($annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]) && !is_null($annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['audit_state']) && !empty($annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['annex_path_front']) && !empty($annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['annex_path_rear'])
                ? $annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['audit_state']
                : null;
            // 有号码 有图片 附件状态不为null 附件状态是啥就是啥
            if (is_numeric($residence_booklet_audit_state)) {
                $person_data['residence_booklet_audit_status']      = (int)$residence_booklet_audit_state;
                $person_data['residence_booklet_audit_status_text'] = $this->getTranslation()->_(HrStaffAnnexInfoModel::$audit_state_key[$residence_booklet_audit_state]);
            }

            // 拒绝时展示拒绝原因
            if ($person_data['residence_booklet_audit_status'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT) {
                $person_data['residence_booklet_reject_reason'] = $annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['reject_reason'] ?? '';
            }

            return $person_data;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('get_staff_identity_annex ---' . $e->getMessage() . '-----' . $e->getLine());
            return $person_data;
        }
    }

    /**
     * 获取用户信息
     * @param $staffInfoId
     * @return mixed
     * @throws ValidationException
     */
    public function getBdoAuthInfo($staffInfoId)
    {
        $staffInfo = (new StaffRepository())->getStaffEmailInfo($staffInfoId);
        if (empty($staffInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        $staffInfo['email']          = !empty($staffInfo['email']) ? $staffInfo['email'] : '';
        $staffInfo['personal_email'] = !empty($staffInfo['personal_email']) ? $staffInfo['personal_email'] : '';
        $staffInfo['date']           = date('m/d/Y');

        return $staffInfo;
    }

    /**
     * BDO 发送
     * @param $params
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function sendBdoEmailInfo($params)
    {
        $staffInfo = (new StaffRepository())->getStaffEmailInfo($params['staff_info_id']);
        if (empty($staffInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        //判断所选邮箱，是否 是当前登录人的邮箱
        if (!in_array($params['email'], [$staffInfo['email'], $staffInfo['personal_email']])) {
            throw new BusinessException($this->getTranslation()->_('4109'));
        }
        $tempFile = BASE_PATH . '/public/pdf_template/bdo.ftl';

        $pdfTempUrl = (new ToolServer($this->lang, $this->timezone))->getPdfTemp($tempFile);

        $data['staff_info_id'] = $staffInfo['staff_info_id'];
        $data['name']          = $staffInfo['name'];
        $data['date']          = date('m/d/Y');

        $pdfHeaderFooterSetting['displayHeaderFooter'] = true;
        $pdf_file_data                                 = (new formPdfServer())->generatePdf($pdfTempUrl, $data, [], '',
            $pdfHeaderFooterSetting);

        //生产pdf失败
        if (empty($pdf_file_data['object_url'])) {
            $this->logger->write_log("sendBdoEmail 生成pdf失败params：" . json_encode($staffInfo) . 'result:' . json_encode($pdf_file_data),
                'notice');
            throw new BusinessException($this->getTranslation()->_('server_error'));
        }
        $data['email']   = $params['email'];
        $data['pdf_url'] = $pdf_file_data['object_url'] ?? '';
        $res             = $this->sendBdoEmail($data);
        if (!$res) {
            throw new BusinessException($this->getTranslation()->_('send_email_fail'));
        }

        return $this->checkReturn([]);
    }

    /**
     * 发送BDOpdf到邮箱
     * @param $data
     * @return bool
     */
    public function sendBdoEmail($data)
    {
        $title     = 'BDO Endorsement Letter';
        $content   = "Hello,</br>The BDO Endorsement Letter  you applied for  has been successfully applied, <a href='{$data['pdf_url']}'>please click here to download</a>.</br>Thanks.";
        $emails    = explode(',', $data['email']);
        $sendEmail = \FlashExpress\bi\App\library\Mail::send($emails, $title, $content);
        if (!$sendEmail) {
            $this->logger->write_log("sendBdoEmail 发送失败-email：" . json_encode($data), 'info');
            return false;
        }
        return true;
    }

    /**
     * 获取  PDF
     * @param $pdfTempFile
     * @return mixed
     * @throws \Exception
     */
    public function getBdoPdfTemp($pdfTempFile)
    {
        $redis_key = md5_file($pdfTempFile);

        $cache       = $this->getDI()->get('redisLib');
        $redis_value = $cache->get($redis_key);

        if (!empty($redis_value)) {
            $pdfTempUrl = $redis_value;
        } else {
            //上传oss 更新表
            $result     = $this->uploadFileOss($pdfTempFile, self::OSS_DIR_MAPS[self::CERTIFICATE_PDF]);
            $pdfTempUrl = $result['object_url'];//oss地址
            //30天
            $cache->set($redis_key, $result['object_url'], 86400 * 30);
        }

        return $pdfTempUrl;
    }

    /**
     * 调用hris接口更新员工社保号、医保卡、公积金、和税卡号
     * @param array $paramIn
     * @return void
     */
    public function updatePersonInfo(array $paramIn = [])
    {
        $attributes = [];
        if (!isset($paramIn['staff_id'])) {
            return;
            $attributes;
        }

        // 公积金
        if (isset($paramIn['fund_num']) && !empty($paramIn['fund_num'])) {
            $attributes['fund_num'] = trim($paramIn['fund_num']);
        }

        // 社保卡号码
        if (isset($paramIn['social_security_num']) && !empty($paramIn['social_security_num'])) {
            $attributes['social_security_num'] = trim($paramIn['social_security_num']);
        }

        // 医疗保险卡号
        if (isset($paramIn['medical_insurance_num']) && !empty($paramIn['medical_insurance_num'])) {
            $attributes['medical_insurance_num'] = trim($paramIn['medical_insurance_num']);
        }

        // 税卡号
        if (isset($paramIn['tax_card']) && !empty($paramIn['tax_card'])) {
            $attributes['tax_card'] = trim($paramIn['tax_card']);
        }

        // 身份证号码
        if (isset($paramIn['identity']) && !empty($paramIn['identity'])) {
            $attributes['identity'] = trim($paramIn['identity']);
        }

        // 如果都为空直接返回
        if (empty($attributes)) {
            return;
            $attributes;
        }
        $attributes['staff_info_id'] = $paramIn['staff_id'];
        $attributes['operater']      = $paramIn['staff_id'];
        $hr_rpc                      = (new ApiClient('hr_rpc', '', 'update_staff_info', $this->lang));
        $hr_rpc->setParams($attributes);
        $result = $hr_rpc->execute();
        $this->logger->write_log("updatePersonInfo Params:" . json_encode($attributes,
                JSON_UNESCAPED_UNICODE) . "=== Result:" . json_encode($result, JSON_UNESCAPED_UNICODE), 'info');
        if ($result['code'] == 1) {
            return $this->checkReturn(['msg' => $this->getTranslation()->_('5002')]);
        } else {
            return $this->checkReturn(-3, $result['msg']);
        }
    }


    public function formatOtData($staffId, $cycle)
    {
        //获取 周期开始结束时间
        $payrollServer = new PayrollServer($this->lang);
        $payrollServer = Tools::reBuildCountryInstance($payrollServer, [$this->lang]);
        [$start, $end] = $payrollServer->formatSalaryDate($cycle);
        $overtimeRe             = new OvertimeRepository($this->timezone);
        $extend['salary_state'] = [
            HrOvertimeModel::SALARY_STATE_UN_COUNT,
            HrOvertimeModel::SALARY_STATE_EFFECTIVE,
            HrOvertimeModel::SALARY_STATE_INVALID_TIME,
            HrOvertimeModel::SALARY_STATE_COVER,
            HrOvertimeModel::SALARY_STATE_MISS_CARD,
            HrOvertimeModel::SALARY_STATE_AUDIT_FAIL,
        ];
        $extend['states']       = [
            enums::APPROVAL_STATUS_APPROVAL,
            enums::APPROVAL_STATUS_REJECTED,
            enums::APPROVAL_STATUS_TIMEOUT,
        ];
        $data                   = $overtimeRe->getOtList($staffId, $start, $end, $extend);
        if (empty($data)) {
            return [0, 0];
        }
        $effectNum = $invalidNum = 0;
        foreach ($data as $da) {
            if ($da['salary_state'] == HrOvertimeModel::SALARY_STATE_EFFECTIVE) {
                $effectNum++;
            } else {
                $invalidNum++;
            }
        }

        return [$effectNum, $invalidNum];
    }

    /**
     * 从fbi 获取 invoice pdf 地址
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getInvoiceInfoFromFbi($params)
    {
        $data['staff_info_id'] = $params['staff_id'];
        //如果指定周期 则返回，指定周期的 pdf,若无指定，则返回最新的 pdf
        if (!empty($params['start_date'])) {
            $data['start_date'] = $params['start_date'];
        }
        if (!empty($params['period_id'])) {
            $data['period_id'] = $params['period_id'];
        }
        $ret = new ApiClient('ard_api', '', 'proxyInvoice.get_invoice_pdf', $this->lang);
        $ret->setParams($data);
        $res = $ret->execute();
        if (!isset($res['result'])) {
            $this->getDI()->get('logger')->write_log("getInvoiceInfoFromFbi 【1】 参数:" . json_encode($data) . ";结果:" . json_encode($res),
                'notice');
            throw new ValidationException($res['error']);
        }
        if ($res['result']['code'] == 1) {
            $result['url']           = $res['result']['data']['pdf_url'] ?? '';
            $result['flash_box_url'] = isCountry() ? '' : env('h5_endpoint') . CeoMailEnums::FLASH_BOX_CATEGORY_INVOICE;
            return $result;
        }
        $msg = isset($res['result']['msg']) ? $res['result']['msg'] : $res['error'];
        $this->getDI()->get('logger')->write_log("getInvoiceInfoFromFbi 【2】 参数:" . json_encode($data) . ";结果:" . json_encode($res),
            'notice');
        throw new ValidationException($msg);
    }

    /**
     * 从fbi 获取 invoice pdf 地址
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getVoucherFromFbi($params)
    {
        $data['staff_info_id'] = $params['staff_id'];
        //如果指定周期 则返回，指定周期的 pdf,若无指定，则返回最新的 pdf
        if (!empty($params['start_date'])) {
            $data['start_date'] = $params['start_date'];
        }

        $ret = new ApiClient('ard_api', '', 'proxyInvoice.get_invoice_pdf', $this->lang);
        $ret->setParams($data);
        $res = $ret->execute();
        if (!isset($res['result'])) {
            $this->getDI()->get('logger')->write_log("getInvoiceInfoFromFbi 【1】 参数:" . json_encode($data) . ";结果:" . json_encode($res),
                'notice');
            throw new ValidationException($res['error']);
        }
        if ($res['result']['code'] == 1) {
            $url                     = $params['type'] == VoucherServer::TYPE_PAYMENT ? ($res['result']['data']['payment_pdf_url'] ?? '') : ($res['result']['data']['tax_pdf_url'] ?? '');
            $result['url']           = $url;
            $result['flash_box_url'] = env('h5_endpoint') . CeoMailEnums::FLASH_BOX_CATEGORY_VOUCHER;
            return $result;
        }
        $msg = isset($res['result']['msg']) ? $res['result']['msg'] : $res['error'];
        $this->getDI()->get('logger')->write_log("getInvoiceInfoFromFbi 【2】 参数:" . json_encode($data) . ";结果:" . json_encode($res),
            'notice');
        throw new ValidationException($msg);
    }

    /**
     * 获取发送周期枚举
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getPeriodFromFbi($params)
    {
        [$data, $res] = $this->getPeriod($params['staff_id']);
        if ($res['result']['code'] == 1) {
            $allData = [];
            foreach ($res['result']['data'] as $one) {
                $_data['label'] = $one['period_name'];
                $_data['value'] = $one['start_date'];
                $allData[]      = $_data;
            }
            $result['date_list'] = $allData;

            return $result;
        }
        $msg = isset($res['result']['msg']) ? $res['result']['msg'] : $res['error'];
        $this->getDI()->get('logger')->write_log("getPeriodFromFbi 【2】 参数:" . json_encode($data) . ";结果:" . json_encode($res),
            'notice');
        throw new ValidationException($msg);
    }

    /**
     * 我的 个人信息 基本信息--上传户口簿
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function residenceBookletSave($params)
    {
        $conditions            = 'staff_info_id = :staff_info_id: and type = :type:';
        $bind['staff_info_id'] = $params['staff_info_id'];
        $bind['type']          = HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD;
        $annexInfo             = HrStaffAnnexInfoRepository::getStaffAnnexInfoInfo('*', $conditions, $bind);

        $db = $this->getDI()->get("db");

        $updateData['annex_path_front'] = $params['residence_booklet_file_url_first'];
        $updateData['annex_path_rear']  = $params['residence_booklet_file_url_second'];
        $updateData['audit_state']      = HrStaffAnnexInfoModel::AUDIT_STATE_NOT_REVIEWED;
        if (!is_null($annexInfo['audit_state']) && in_array($annexInfo['audit_state'],
                [HrStaffAnnexInfoModel::AUDIT_STATE_NOT_REVIEWED, HrStaffAnnexInfoModel::AUDIT_STATE_PASSED])) {
            throw new ValidationException($this->getTranslation()->_('residence_booklet_audit_waiting'));
        }
        if (!empty($annexInfo)) {
            $db->updateAsDict("hr_staff_annex_info", $updateData,
                ["conditions" => "id='{$annexInfo['id']}'"]);
            return true;
        }
        $updateData['staff_info_id'] = $params['staff_info_id'];
        $updateData['type']          = HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD;

        $db->insertAsDict("hr_staff_annex_info", $updateData);

        return true;
    }

    /**
     * 考勤统计
     * @throws \ReflectionException
     */
    public function salaryAttendanceStat($staffInfoId, $month, $type): array
    {
        $payrollServer = Tools::reBuildCountryInstance(new PayrollServer($this->lang), [$this->lang]);
        [$startTime, $endTime] = $payrollServer->formatSalaryDate($month);
        //薪酬
        if (isCountry('MY')) {
            $salaryFind = StaffPayrollModel::findFirst([
                'columns'    => 'date(hire_date) as hire_date,date(last_work_day) as last_working_day',
                'conditions' => 'staff_info_id=:staff_id: and month=:month: and run_type=1 and is_deleted=0',
                'bind'       => ['staff_id' => $staffInfoId, 'month' => $month],
            ]);
        } else {
            $salaryFind = SalaryGongZiModel::findFirst([
                'columns'    => 'start as hire_date,last_working_day,sys_store_id',
                'conditions' => 'staff_info_id=:staff_id: and excel_month=:excel_month:',
                'bind'       => ['staff_id' => $staffInfoId, 'excel_month' => $month],
            ]);
        }

        $salary = empty($salaryFind) ? [] : $salaryFind->toArray();
        //发薪不打卡白名单
        $typePaidLocally = (new WhiteListServer())->attendanceList([
            'start_date' => $startTime,
            'end_date'   => $endTime,
        ]);
        $typePaidLocally = empty($typePaidLocally['result']['data']) ? [] : $typePaidLocally['result']['data'];
        //历史班次数据
        if (!isCountry('MY')) {
            $shiftFind = HrStaffShiftHistoryModel::find([
                'columns'    => 'shift_day,start as shift_start,[end] as shift_end,shift_id',
                'conditions' => 'staff_info_id=:staff_id: and shift_day>=:start: and shift_day<=:end:',
                'bind'       => ['staff_id' => $staffInfoId, 'start' => $startTime, 'end' => $endTime],
            ])->toArray();
            $shiftMap  = array_column($shiftFind, null, 'shift_day');
        } else {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['hssv' => HrStaffShiftV2Model::class]);
            $builder->Join(HrShiftV2ExtendModel::class, 'hssv.shift_extend_id = hsve.id', 'hsve');
            $builder->columns('
            hssv.shift_date,
            hsve.first_start as shift_start,
            hsve.first_end as shift_end,
            hsve.second_start as second_shift_start,
            hsve.second_end as second_shift_end,
            hsve.is_middle_rest,
            hsve.middle_rest_start,
            hsve.middle_rest_end
            ');
            $builder->where('hssv.staff_info_id=:staff_id: and hssv.shift_date>=:start: and hssv.shift_date<=:end:',
                ['staff_id' => $staffInfoId, 'start' => $startTime, 'end' => $endTime]);
            $shiftFind = $builder->getQuery()->execute()->toArray();
            $shiftMap  = array_column($shiftFind, null, 'shift_date');
        }
        $haveLateEarly = in_array(get_country_code(), ['PH', 'LA']);
        //考勤数据 -- leave_time_type 1:上午半天假 2:下午半天假，3:全天请假
        $columns = 'stat_date,AB,attendance_started_at,attendance_end_at,shift_start,shift_end,leave_time_type,job_title';
        if ($haveLateEarly) {
            $columns .= ',late_times,leave_early_times';
        }
        // ph 不需要累加 (LW + C19SL + ISL) as LW 在 v2逻辑中已经处理
        if (isCountry()) {
            $columns .= ',(LW + USL + other_UL) as LW';
        } else {
            $columns .= ',LW';
        }
        if (isCountry('MY')) {
            $columns .= ',second_attendance_started_at,second_attendance_end_at,second_shift_start,second_shift_end';
        }
        $attendanceSolid = AttendanceDataV2RecalSolidModel::find([
            'columns'    => $columns,
            'conditions' => 'staff_info_id=:staff_id: and stat_date >=:start: and stat_date<=:end:',
            'bind'       => ['staff_id' => $staffInfoId, 'start' => $startTime, 'end' => $endTime],
            'orderBy'    => 'stat_date',
        ])->toArray();
        //请假数据
        $auditRepository = new AuditRepository($this->lang);
        $leaveData       = $auditRepository->getLeaveDataForSalary($staffInfoId, $startTime, $endTime);
        //请假类型翻译
        $leaveTypeNameMap = array_column($auditRepository->getTypeBook_allFromCache(), 'msg', 'code');
        //主播职位
        $liveJobIds              = (new SettingEnvServer())->getSetVal('free_shift_position', ',');
        $returnData['ab_and_lw'] = [];
        $returnData['ab_total']  = 0;
        $returnData['lw_total']  = 0;
        if ($haveLateEarly) {
            $returnData['late_early']       = [];
            $returnData['late_early_total'] = 0;
        }
        //LA总部不展示迟到早退
        $noLateEarly = isCountry('LA') && $salary['sys_store_id'] == '-1';
        foreach ($attendanceSolid as $item) {
            if ($item['stat_date'] < $salary['hire_date']
                || $salary['last_working_day'] && $item['stat_date'] > $salary['last_working_day']) {
                continue;
            }
            if (!empty($typePaidLocally[$item['stat_date']]['type_paid_locally']) && in_array($staffInfoId,
                    $typePaidLocally[$item['stat_date']]['type_paid_locally'])) {
                $item['AB'] = 0;
            }
            $haveCheckIn = isCountry('MY') ? ($item['attendance_started_at'] || $item['attendance_end_at'] || $item['second_attendance_started_at'] || $item['second_attendance_end_at'])
                : ($item['attendance_started_at'] || $item['attendance_end_at']);
            //入职第一天
            if (!isCOuntry('PH') && $item['stat_date'] == $salary['hire_date']
                && $item['AB'] == 10
                && $haveCheckIn
                && (empty($salary['last_working_day']) || $salary['last_working_day'] > $salary['hire_date'])) {
                $item['AB'] = 0;
            }
            //v2 solid 无班次 取历史班次
            if (empty($item['shift_start']) && isset($shiftMap[$item['stat_date']])) {
                $item['shift_start'] = $shiftMap[$item['stat_date']]['shift_start'];
            }
            if (empty($item['shift_end']) && isset($shiftMap[$item['stat_date']])) {
                $item['shift_end'] = $shiftMap[$item['stat_date']]['shift_end'];
            }
            if (isCountry('MY')) {
                if (empty($item['second_shift_start']) && !empty($shiftMap[$item['stat_date']]['second_shift_start'])) {
                    $item['second_shift_start'] = $shiftMap[$item['stat_date']]['second_shift_start'];
                }
                if (empty($item['second_shift_end']) && !empty($shiftMap[$item['stat_date']]['second_shift_end'])) {
                    $item['second_shift_end'] = $shiftMap[$item['stat_date']]['second_shift_end'];
                }
            }
            //LA 总部不展示迟到早退
            if ($noLateEarly) {
                $item['late_times'] = $item['leave_early_times'] = 0;
            }
            //缺勤
            if ($item['AB'] > 0) {
                if ($type != 'only_total') {
                    $baseData              = [];
                    $baseData['stat_date'] = $item['stat_date'];
                    $baseData['type']      = 'ab';
                    $baseData['day']       = $item['AB'] / 10;
                    $goWorkShow            = $this->showLastOrNextDay($item['attendance_started_at'],
                        $item['stat_date']);
                    $workOffShow           = $this->showLastOrNextDay($item['attendance_end_at'], $item['stat_date']);
                    $shiftShow             = $this->handleShiftShow($item['shift_start'], $item['shift_end']);
                    //班次
                    $baseData['shift'] = '';
                    //原始班次开始时间
                    $originalShiftStart = $item['shift_start'];
                    //是否存在两个班次
                    $haveSecondShift = isCountry('MY') && !empty($item['second_shift_start']) && !empty($item['second_shift_end']);
                    //主播班次
                    if (in_array($item['job_title'], $liveJobIds)) {
                        if (empty($item['attendance_started_at'])) {
                            $baseData['shift'] = $this->getTranslation()->_('live_no_fixed_shift');
                        } else {
                            $liveStartedAt     = date('H', strtotime($item['attendance_started_at']));
                            $baseData['shift'] = $liveStartedAt >= 4 && $liveStartedAt < 16 ? $this->getTranslation()->_('day_shift') : $this->getTranslation()->_('night_shift');
                        }
                    } elseif (($item['shift_start'] && $item['shift_end'])) {
                        $baseData['shift'] = $item['shift_start'] . '-' . $shiftShow . $item['shift_end'];
                        //员工请假前半天
                        if ($item['leave_time_type'] == enums::LEAVE_TIME_TYPE_MORNING) {
                            $shift_start = $item['shift_start'];
                            $shift_end   = $item['shift_end'];
                            if (!isCountry('MY')) {
                                $shift_start = date('H:i',
                                    strtotime($item['stat_date'] . ' ' . $item['shift_end'] . ' -4 Hours'));
                            }
                            //马来一个班次
                            if (isCountry('MY') && !$haveSecondShift) {
                                //中间有休息
                                $haveMiddleRest = !empty($shiftMap[$item['stat_date']]['is_middle_rest']) && $shiftMap[$item['stat_date']]['is_middle_rest'] == 1;
                                if ($haveMiddleRest) {
                                    $shift_start = $shiftMap[$item['stat_date']]['middle_rest_end'];
                                } else {
                                    if ($item['shift_start'] > $item['shift_end']) {
                                        $m = strtotime($item['stat_date'] . ' ' . $item['shift_end'] . ' +1 day') - strtotime($item['stat_date'] . ' ' . $item['shift_start']);
                                    } else {
                                        $m = strtotime($item['stat_date'] . ' ' . $item['shift_end']) - strtotime($item['stat_date'] . ' ' . $item['shift_start']);
                                    }
                                    $s           = $m / 2;
                                    $shift_start = date('H:i',
                                        strtotime($item['stat_date'] . ' ' . $item['shift_start']) + $s);
                                }
                            }
                            //马来两个班次
                            if (isCountry('MY') && $haveSecondShift) {
                                $shift_start = $item['second_shift_start'];
                                $shift_end   = $item['second_shift_end'];
                            }
                            $baseData['shift'] = $this->handleShiftShow($originalShiftStart,
                                    $shift_start) . $shift_start . '-' . $this->handleShiftShow($originalShiftStart,
                                    $shift_end) . $shift_end;
                        }
                        //员工请假后半天
                        if ($item['leave_time_type'] == enums::LEAVE_TIME_TYPE_AFTERNOON) {
                            $shift_start = $item['shift_start'];
                            $shift_end   = $item['shift_end'];
                            if (!isCountry('MY')) {
                                $shift_end = date('H:i',
                                    strtotime($item['stat_date'] . ' ' . $item['shift_start'] . ' +4 Hours'));
                            }
                            //马来一个班次
                            if (isCountry('MY') && !$haveSecondShift) {
                                //中间有休息
                                $haveMiddleRest = !empty($shiftMap[$item['stat_date']]['is_middle_rest']) && $shiftMap[$item['stat_date']]['is_middle_rest'] == 1;
                                if ($haveMiddleRest) {
                                    $shift_end = $shiftMap[$item['stat_date']]['middle_rest_start'];
                                } else {
                                    if ($item['shift_start'] > $item['shift_end']) {
                                        $m = strtotime($item['stat_date'] . ' ' . $item['shift_end'] . ' +1 day') - strtotime($item['stat_date'] . ' ' . $item['shift_start']);
                                    } else {
                                        $m = strtotime($item['stat_date'] . ' ' . $item['shift_end']) - strtotime($item['stat_date'] . ' ' . $item['shift_start']);
                                    }
                                    $s         = $m / 2;
                                    $shift_end = date('H:i',
                                        strtotime($item['stat_date'] . ' ' . $item['shift_start']) + $s);
                                }
                            }
                            $shiftShow         = $this->handleShiftShow($shift_start, $shift_end);
                            $baseData['shift'] = $shift_start . '-' . $shiftShow . $shift_end;
                        }
                    }


                    $firstStart             = empty($item['attendance_started_at']) ? $this->getTranslation()->_('attendance_miss') : $goWorkShow . date('H:i',
                            strtotime($item['attendance_started_at']));
                    $firstEnd               = empty($item['attendance_end_at']) ? $this->getTranslation()->_('attendance_miss') : $workOffShow . date('H:i',
                            strtotime($item['attendance_end_at']));
                    $baseData['attendance'] = $firstStart . '-' . $firstEnd;
                    $baseData['ab_reason']  = $this->getAbReason($item['attendance_started_at'],
                        $item['attendance_end_at']);
                    if (isCountry('MY')) {
                        $haveSecondShift = $item['second_shift_start'] && $item['second_shift_end'];
                        $secondShift     = $haveSecondShift ? $this->handleShiftShow($originalShiftStart,
                                $item['second_shift_start']) . $item['second_shift_start'] . '-' . $this->handleShiftShow($originalShiftStart,
                                $item['second_shift_end']) . $item['second_shift_end'] : '';
                        if ($item['leave_time_type'] == enums::LEAVE_TIME_TYPE_MORNING && $secondShift) {
                            $baseData['shift'] = $secondShift;
                        }
                        if (empty($item['leave_time_type']) && $secondShift) {
                            $baseData['shift'] .= ';' . $secondShift;
                        }
                        if ($haveSecondShift) {
                            $goWorkShow  = $this->showLastOrNextDay($item['second_attendance_started_at'],
                                $item['stat_date']);
                            $workOffShow = $this->showLastOrNextDay($item['second_attendance_end_at'],
                                $item['stat_date']);
                            $secondStart = empty($item['second_attendance_started_at']) ? $this->getTranslation()->_('attendance_miss') : $goWorkShow . date('H:i',
                                    strtotime($item['second_attendance_started_at']));
                            $secondEnd   = empty($item['second_attendance_end_at']) ? $this->getTranslation()->_('attendance_miss') : $workOffShow . date('H:i',
                                    strtotime($item['second_attendance_end_at']));
                            if ($item['leave_time_type'] == enums::LEAVE_TIME_TYPE_MORNING) {
                                $baseData['attendance'] = $secondStart . '-' . $secondEnd;
                            }
                            if (empty($item['leave_time_type'])) {
                                $baseData['attendance'] .= ';' . $secondStart . '-' . $secondEnd;
                            }
                        }
                        if ($haveSecondShift) {
                            if ($item['leave_time_type'] == enums::LEAVE_TIME_TYPE_MORNING) {
                                $baseData['ab_reason'] = $this->getAbReason($item['second_attendance_started_at'],
                                    $item['second_attendance_end_at']);
                            }
                            if (empty($item['leave_time_type'])) {
                                $baseData['ab_reason'] = $this->getAbReasonMY($item['attendance_started_at'],
                                    $item['attendance_end_at'], $item['second_attendance_started_at'],
                                    $item['second_attendance_end_at']);
                            }
                        }
                    }
                    $baseData['shift'] = trim( $baseData['shift'],';');
                    $returnData['ab_and_lw'][] = $baseData;
                }
                $returnData['ab_total'] += ($item['AB'] / 10);
            }
            $leaveDateData = $leaveData[$item['stat_date']] ?? [];
            //有不带薪假
            $haveLeaveLW = $item['LW'] > 0 && $leaveDateData;
            //上午不带薪假
            $haveMorningLW = $item['leave_time_type'] == enums::LEAVE_TIME_TYPE_MORNING && isset($leaveDateData[enums::LEAVE_TIME_TYPE_MORNING]);
            //下午不带薪假
            $haveAfterLW = $item['leave_time_type'] == enums::LEAVE_TIME_TYPE_AFTERNOON && isset($leaveDateData[enums::LEAVE_TIME_TYPE_AFTERNOON]);
            //不带薪假
            if ($haveLeaveLW) {
                //上午
                if ($haveMorningLW) {
                    if ($type != 'only_total') {
                        $baseData                    = [];
                        $baseData['stat_date']       = $item['stat_date'];
                        $baseData['type']            = 'lw';
                        $baseData['day']             = 0.5;
                        $baseData['start_time']      = $leaveDateData[1]['leave_start_time'] . $this->morningOrAfter($leaveDateData[1]['leave_start_type']);
                        $baseData['end_time']        = $leaveDateData[1]['leave_end_time'] . $this->morningOrAfter($leaveDateData[1]['leave_end_type']);
                        $baseData['leave_type']      = $leaveDateData[1]['leave_type'];
                        $baseData['leave_type_name'] = $leaveTypeNameMap[$leaveDateData[1]['leave_type']] ?? '';
                        $returnData['ab_and_lw'][]   = $baseData;
                    }
                    $returnData['lw_total'] += 0.5;
                }
                //下午
                if ($haveAfterLW) {
                    if ($type != 'only_total') {
                        $baseData               = [];
                        $baseData['stat_date']  = $item['stat_date'];
                        $baseData['type']       = 'lw';
                        $baseData['day']        = 0.5;
                        $baseData['start_time'] = $leaveDateData[2]['leave_start_time'] . $this->morningOrAfter($leaveDateData[2]['leave_start_type']);
                        $baseData['end_time']   = $leaveDateData[2]['leave_end_time'] . $this->morningOrAfter($leaveDateData[2]['leave_end_type']);;
                        $baseData['leave_type']      = $leaveDateData[2]['leave_type'];
                        $baseData['leave_type_name'] = $leaveTypeNameMap[$leaveDateData[2]['leave_type']] ?? '';
                        $returnData['ab_and_lw'][]   = $baseData;
                    }
                    $returnData['lw_total'] += 0.5;
                }
                //全天
                $count = count($leaveDateData);
                if ($count && $item['leave_time_type'] == 3) {
                    foreach ($leaveDateData as $leaveItem) {
                        if ($type != 'only_total') {
                            $baseData                    = [];
                            $baseData['stat_date']       = $item['stat_date'];
                            $baseData['type']            = 'lw';
                            $baseData['day']             = $count == 1 ? $item['LW'] / 10 : 0.5;
                            $baseData['start_time']      = $leaveItem['leave_start_time'] . $this->morningOrAfter($leaveItem['leave_start_type']);
                            $baseData['end_time']        = $leaveItem['leave_end_time'] . $this->morningOrAfter($leaveItem['leave_end_type']);
                            $baseData['leave_type']      = $leaveItem['leave_type'];
                            $baseData['leave_type_name'] = $leaveTypeNameMap[$leaveItem['leave_type']] ?? '';
                            $returnData['ab_and_lw'][]   = $baseData;
                        }
                        $returnData['lw_total'] += ($count == 1 ? $item['LW'] / 10 : 0.5);
                    }
                }
            }
            //迟到 早退
            if ($haveLateEarly) {
                //迟到
                if ($item['late_times'] > 0) {
                    if ($type != 'only_total') {
                        $baseData                     = [];
                        $baseData['stat_date']        = $item['stat_date'];
                        $baseData['type']             = 'late';
                        $baseData['time']             = $item['late_times'] / 10;
                        $baseData['attendance_start'] = empty($item['attendance_started_at']) ? $this->getTranslation()->_('attendance_miss') : date('H:i',
                            strtotime($item['attendance_started_at']));
                        $baseData['shift_start']      = $item['leave_time_type'] == enums::LEAVE_TIME_TYPE_MORNING
                            ? date('H:i', strtotime($item['stat_date'] . ' ' . $item['shift_end'] . ' -4 Hours'))
                            : $item['shift_start'];
                        $returnData['late_early'][]   = $baseData;
                    }

                    $returnData['late_early_total'] += ($item['late_times'] / 10);
                }
                //早退
                if ($item['leave_early_times'] > 0) {
                    if ($type != 'only_total') {
                        $baseData                   = [];
                        $baseData['stat_date']      = $item['stat_date'];
                        $baseData['type']           = 'early';
                        $baseData['time']           = $item['leave_early_times'] / 10;
                        $baseData['attendance_end'] = empty($item['attendance_end_at']) ? $this->getTranslation()->_('attendance_miss') : date('H:i',
                            strtotime($item['attendance_end_at']));
                        $baseData['shift_end']      = $item['leave_time_type'] == enums::LEAVE_TIME_TYPE_AFTERNOON
                            ? date('H:i', strtotime($item['stat_date'] . ' ' . $item['shift_start'] . ' +4 Hours'))
                            : $item['shift_end'];
                        $returnData['late_early'][] = $baseData;
                    }
                    $returnData['late_early_total'] += ($item['leave_early_times'] / 10);
                }
            }
        }
        return $returnData;
    }

    /**
     * 判断是否次日
     * @param $start
     * @param $end
     * @return string
     */
    protected function handleShiftShow($start, $end): string
    {
        return date('Y-m-d' . $start . ':00') > date('Y-m-d' . $end . ':00') ? $this->getTranslation()->_('next_day') : '';
    }
    /**
     * 昨日次日
     * @param $attendanceTime
     * @param $statDate
     * @return string
     */
    public  function showLastOrNextDay($attendanceTime, $statDate): string
    {
        if (empty($attendanceTime)) {
            return '';
        }
        if (date('Y-m-d',
                strtotime($attendanceTime)) < $statDate) {
            return $this->getTranslation()->_('yesterday');
        }
        if (date('Y-m-d',
                strtotime($attendanceTime)) > $statDate) {
            return $this->getTranslation()->_('next_day');
        }
        return '';
    }
    /**
     * 获取缺勤原因MY
     * @param $attendanceStart
     * @param $attendanceEnd
     * @param $secondStart
     * @param $secondEnd
     * @return string
     */
    protected function getAbReasonMY($attendanceStart, $attendanceEnd, $secondStart, $secondEnd): string
    {
        $t = $this->getTranslation();
        if ((empty($attendanceStart) || empty($secondStart)) && (empty($attendanceEnd) || empty($secondEnd))) {
            return $t->_('ab_reason_2');
        }
        if (empty($attendanceStart) || empty($secondStart)) {
            return $t->_('ab_reason_3');
        }
        if (empty($attendanceEnd) || empty($secondEnd)) {
            return $t->_('ab_reason_4');
        }
        return $t->_('ab_reason_1');
    }

    /**
     * 获取缺勤原因
     * @param $attendanceStart
     * @param $attendanceEnd
     * @return string
     */
    protected function getAbReason($attendanceStart, $attendanceEnd): string
    {
        $t = $this->getTranslation();
        if (empty($attendanceStart) && empty($attendanceEnd)) {
            return $t->_('ab_reason_2');
        }
        if (empty($attendanceStart) && $attendanceEnd) {
            return $t->_('ab_reason_3');
        }
        if ($attendanceStart && empty($attendanceEnd)) {
            return $t->_('ab_reason_4');
        }
        if ($attendanceStart && $attendanceEnd) {
            return $t->_('ab_reason_1');
        }
        return '';
    }

    protected function morningOrAfter($leaveType): string
    {
        $t = $this->getTranslation();
        return ' ' . ($leaveType == 1 ? $t->_('morning') : $t->_('afternoon'));
    }

    /**
     * 获取工资表明细
     * @param $paramIn
     * @return array|mixed
     * @throws \ReflectionException
     */
    public function getSalaryInfoFromHCM($paramIn)
    {
        $month   = $paramIn['month'];
        $staffId = $paramIn['staff_id'];
        //传一个当前月份 如果没有记录 会返回 有薪资记录的最新的一个月
        $param['month']    = $month;
        $param['staff_id'] = $staffId;
        $ac                = new ApiClient('hcm_rpc', '', 'get_salary_data', $this->lang);
        $ac->setParams($param);
        $ac_result = $ac->execute();
        $this->logger->write_log("staff {$staffId} getSalaryInfoFromHCM request:" . json_encode($param) . " res:" . json_encode($ac_result,
                JSON_UNESCAPED_UNICODE), 'info');

        if (!empty($ac_result) && !empty($ac_result['result']['data'])) {
            $data = $ac_result['result']['data'];
            //考勤数量统计
            $salaryAttendanceStat = $this->salaryAttendanceStat($staffId, $data['salary_cycle'], 'only_total');
            $data                 = array_merge($data, $salaryAttendanceStat);
            //工资条新增 有效无效ot 数量
            [$data['effect_num'], $data['invalid_num']] = $this->formatOtData($staffId, $data['salary_cycle']);
            $data['flash_box_url'] = env('h5_endpoint') . CeoMailEnums::FLASH_BOX_CATEGORY;
            return $data;
        }
        return [];
    }

    /**
     * @param $staff_id
     * @return array
     * @throws ValidationException
     */
    protected function getPeriod($staff_id): array
    {
        $data['staff_info_id'] = $staff_id;
        $ret                   = new ApiClient('ard_api', '', 'proxyInvoice.get_period', $this->lang);
        $ret->setParams($data);
        $res = $ret->execute();
        if (!isset($res['result'])) {
            $this->getDI()->get('logger')->write_log("getPeriodFromFbi 【1】 参数:" . json_encode($data) . ";结果:" . json_encode($res),
                'notice');
            throw new ValidationException($res['error']);
        }
        return [$data, $res];
    }
}
