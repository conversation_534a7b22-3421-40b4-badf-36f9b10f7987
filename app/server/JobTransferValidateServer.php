<?php

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;

class JobTransferValidateServer extends BaseServer
{
    protected $submitter_info_manage_departments; //申请人管辖部门
    protected $submitter_info_manage_stores;      //申请人管辖网点
    protected $submitter_info_manage_staff_id;    //申请人直接管理员工
    protected $job_transfer_staff_info;           //被转岗人信息
    protected $submitter_id;                      //申请人id
    protected $submitter_info;                    //申请人信息
    protected $job_transfer_info;                 //转岗信息
    protected $job_transfer_staff_probation;      //被转岗人转正信息
    protected $job_handover_staff_info;           //工作交接人
    protected $after_date;                        //转岗日期
    protected $after_store_id;                    //转岗后网点
    protected $after_position_id;                 //转岗后职位
    protected $rules = [];                        //校验规则

    /**
     * @var StaffServer
     */
    protected $staffServer;

    //配置key
    protected $configKeyList = [
        'dept_network_management_id',
        'dept_shop_management_id',
        'dept_hub_management_id',
        'dept_flash_freight_hub_dep_id',
        'job_transfer_front_line_position',
        'individual_contractor_job_title',
        'job_transfer_front_line_number',
        'job_transfer_position_permission',
    ];
    protected $config = [];

    protected $staff_info_columns = 'h.staff_info_id,
        h.name as staff_name,
        h.state,
        h.formal,
        h.is_sub_staff,
        h.sys_store_id as store_id,
        s.name as store_name,
        h.job_title, 
        j.job_name,
        d.name as department_name,
        h.sys_department_id, 
        h.node_department_id,
        h.wait_leave_state,
        h.job_title_grade_v2,
        h.hire_type,
        h.hire_times,
        h.hire_date,
        h.nationality,
        h.manger as manager_id';

    const DEFAULT_RULES = [
        'checkTransferStaffId',             //被转岗工号是否存在
        'checkTransferStaffOnJobState',     //被转岗人是否在职
        'checkTransferStaffAgent',          //被转岗人是否个人代理
        'checkTransferStaff',               //不能为自己转岗
        'checkInProcessTransfer',           //是否存在进行中的转岗
        'checkProbation',                   //试用期不能转岗
        'checkPermission',                  //转岗权限
    ];

    const CREATE_RULES = [
        'checkTransferStaffId',             //被转岗工号是否存在
        'checkTransferStaffOnJobState',     //被转岗人是否在职
        'checkTransferStaffAgent',          //被转岗人是否个人代理
        'checkTransferStaff',               //不能为自己转岗
        'checkInProcessTransfer',           //是否存在进行中的转岗
        'checkProbation',                   //试用期不能转岗
        'checkPermission',                  //转岗权限
        'checkHandoverStaffId',             //工作交接人工号是否存在
        'checkHandoverStaffOnJobState',     //工作交接人是否在职
        'checkHandoverStaffValid',          //工作交接人是否有效
        'checkAfterDate',                   //校验转岗日期
    ];

    const CHECK_RULES = [
        'checkTransferStaffId',             //被转岗工号是否存在
        'checkTransferStaffOnJobState',     //被转岗人是否在职
        'checkTransferStaffAgent',          //被转岗人是否个人代理
        'checkTransferStaff',               //不能为自己转岗
        'checkInProcessTransfer',           //是否存在进行中的转岗
        //'checkProbation',                   //试用期不能转岗
        'checkPermission',                  //转岗权限
        'checkFrontLine',                   //是否一线职位
    ];

    //特殊转岗
    const SPECIAL_CHECK_RULES = [
        'checkTransferStaffId',             //被转岗工号是否存在
        'checkTransferStaffOnJobState',     //被转岗人是否在职
        'checkTransferStaffAgent',          //被转岗人是否个人代理
        'checkInProcessTransfer',           //是否存在进行中的转岗
        'checkSpecialPermission',           //转岗权限
        'checkFrontLine',                   //是否一线职位
        'checkAfterDate',                   //校验转岗日期
    ];

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * @description 初始化申请人相关数据，包含管辖范围、权限、申请信息等
     * @param $params
     * @return JobTransferValidateServer
     * @throws ValidationException
     */
    public function init($params): JobTransferValidateServer
    {
        $submitterId         = $params['submitter_id'];
        $transferStaffInfoId = $params['transfer_staff_id'];
        if (empty($submitterId)) {
            throw new ValidationException('miss args');
        }
        $this->staffServer = new StaffServer();
        $this->setSubmitterId($submitterId);
        $this->setSubmitterInfo($this->getSpecifiedStaffInfo($submitterId));

        //获取管辖部门、管辖网点
        [$manageDepartment, $manageStore] = $this->staffServer->getManageOrganizationByStaffInfoId($submitterId);
        $this->setSubmitterInfoManageDepartments($manageDepartment);
        $this->setSubmitterInfoManageStores($manageStore);

        $this->setSubmitterInfoManageStaffId($this->staffServer->getStaffInfoId($submitterId));
        $this->setJobTransferStaffDetail($transferStaffInfoId);

        //工作交接人信息
        if (isset($params['job_handover_staff_id'])) {
            $this->setJobHandoverStaffInfo($this->getSpecifiedStaffInfo($params['job_handover_staff_id']));
        }

        if (isset($params['after_date'])) {
            $this->setAfterDate($params['after_date']);
        }
        if (isset($params['after_store_id'])) {
            $this->setAfterStoreId($params['after_store_id']);
        }
        if (isset($params['after_position_id'])) {
            $this->setAfterPositionId($params['after_position_id']);
        }
        $this->setConfig($this->configKeyList);
        return $this;
    }

    /**
     * @description 获取员工信息
     * @param $staff_info_id
     * @return array
     */
    protected function getSpecifiedStaffInfo($staff_info_id): array
    {
        $staffInfo = $this->staffServer->getStaffInfoSpecColumns($staff_info_id, $this->staff_info_columns);

        if (!empty($staffInfo) && $staffInfo['store_id'] == '-1') {
            $staffInfo['store_name'] = enums::HEAD_OFFICE;
        }
        return $staffInfo;
    }

    /**
     * @description 设置被转岗人
     * @param $transfer_staff_id
     * @return void
     */
    protected function setJobTransferStaffDetail($transfer_staff_id)
    {
        //获取转岗人信息
        $this->setJobTransferStaffInfo($this->getSpecifiedStaffInfo($transfer_staff_id));

        //转岗信息
        $this->setJobTransferInfo((new JobTransferV2Server($this->lang, $this->timeZone))->getJobTransferData($transfer_staff_id));
        //转正状态
        $this->setJobTransferStaffProbation($this->getProbationByStaffInfoId($transfer_staff_id));
    }

    /**
     * @description 根据被转岗人ID查询转正状态
     * @param $transfer_staff_id
     * @return array
     */
    protected function getProbationByStaffInfoId($transfer_staff_id): array
    {
        $probationInfo = HrProbationModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => [
                'staff_info_id' => $transfer_staff_id,
            ],
            'columns' => 'status',
        ]);
        return $probationInfo ? $probationInfo->toArray(): [];
    }

    /**
     * @description 指定校验规则
     * @param array $rule
     * @return JobTransferValidateServer
     */
    public function loadRules(array $rule = self::DEFAULT_RULES): JobTransferValidateServer
    {
        $this->setRules($rule);
        return $this;
    }

    /**
     * @description 校验指定工号
     * @return int
     * @throws ValidationException
     */
    public function check(): int
    {
        $checkRules = $this->getRules();
        if (empty($checkRules)) {
            return ErrCode::ERROR;
        }
        foreach ($checkRules as $rule) {
            if (method_exists($this, $rule)) {
                $errCode = $this->$rule();
                if ($errCode != ErrCode::SUCCESS) {
                    $msg = $this->getTranslation()->_($this->getErrorMessageKey($errCode));
                    $this->logger->write_log("job transfer error code: {$errCode} " . $msg, 'info');
                    throw new ValidationException($msg);
                }
            }
        }
        return ErrCode::SUCCESS;
    }

    /**
     * @description 校验被转岗人工号是否有效
     * @return int
     * @checkRule
     */
    protected function checkTransferStaffId(): int
    {
        return $this->checkStaffExist($this->getJobTransferStaffInfo());
    }

    /**
     * @description 校验工作交接人工号是否有效
     * @return int
     * @checkRule
     */
    protected function checkHandoverStaffId(): int
    {
        return $this->checkStaffExist($this->getJobHandoverStaffInfo(), ErrCode::JOB_TRANSFER_HANDOVER_STAFF_NOT_EXIST_ERROR);
    }

    /**
     * @description 工号是否有效
     * @param $staff_info
     * @param int $errCode
     * @return int
     */
    protected function checkStaffExist($staff_info, int $errCode = ErrCode::JOB_TRANSFER_STAFF_NOT_EXIST_ERROR): int
    {
        if (empty($staff_info)) {
            return $errCode; //工号不存在
        }
        return ErrCode::SUCCESS;
    }

    /**
     * @description 校验被转岗人在职状态
     * @return int
     * @checkRule
     */
    protected function checkTransferStaffOnJobState(): int
    {
        //仅在职
        return $this->checkStaffOnJobState($this->getJobTransferStaffInfo(), [HrStaffInfoModel::STATE_ON_JOB]);
    }

    /**
     * @description 校验被转岗人在职状态
     * @return int
     * @checkRule
     */
    protected function checkTransferStaffAgent(): int
    {
        $staffInfo = $this->getJobTransferStaffInfo();
        if ($staffInfo['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return ErrCode::SUCCESS;
        }
        return ErrCode::JOB_TRANSFER_NOT_SUPPORT_AGENT;
    }

    /**
     * @description 校验工作交接人在职状态
     * @return int
     * @checkRule
     */
    protected function checkHandoverStaffOnJobState(): int
    {
        //仅在职
        return $this->checkStaffOnJobState($this->getJobHandoverStaffInfo(), [HrStaffInfoModel::STATE_ON_JOB], ErrCode::JOB_TRANSFER_HANDOVER_STAFF_NOT_ON_JOB_ERROR);
    }

    /**
     * @description 校验工作交接人是否有效
     *
     * 有效的工作交接人
     *  转岗前是网点员工
     *      - 被转岗员工原网点员工
     *      - 被转岗人装岗前属于Network Management部门下，可填写被转岗员工原网点的片区负责人、大区负责人
     *      - 被转岗人装岗前属于Retail Management部门下，可填写被转岗员工原网点的大区负责人
     *      - 被转岗人装岗前属于Hub Management以及Flash Freight Hub部门下，可填写被转岗员工所属部门负责人
     *  转岗前是总部员工
     *      - 被转岗员工原所属部门内员工和原所属部门负责人
     *
     * @return int
     * @checkRule
     */
    protected function checkHandoverStaffValid(): int
    {
        $validHandoverStaff = [];
        //被转岗人信息
        $transferStaffInfo = $this->getJobTransferStaffInfo();
        $handoverStaffInfo = $this->getJobHandoverStaffInfo();

        if ($handoverStaffInfo['staff_info_id'] == $transferStaffInfo['staff_info_id']) {
            return ErrCode::JOB_TRANSFER_HANDOVER_STAFF_IS_TRANSFER_STAFF_DATE_ERROR;
        }

        //被转岗人所属部门信息
        $departmentInfo = (new SysDepartmentServer())->getDepartmentDetail($transferStaffInfo['node_department_id']);

        if ($transferStaffInfo['store_id'] == enums::HEAD_OFFICE_ID) {

            //同部门下的员工
            $isSameDepartment     = ApprovalFinderServer::getInstance()
                ->isSpecifiedStaffBelongDepartment($handoverStaffInfo['staff_info_id'], $transferStaffInfo['node_department_id']);
            $validHandoverStaff[] = $departmentInfo['manager_id'] ?? '';
            if (!$isSameDepartment && !in_array($handoverStaffInfo['staff_info_id'], $validHandoverStaff)) {
                return ErrCode::JOB_TRANSFER_HANDOVER_STAFF_NOT_VALID_ERROR;
            }
        } else {

            //同网点下员工
            $validHandoverStaff = ApprovalFinderServer::getInstance()->findSpecStoreStaff($transferStaffInfo['store_id']);
            if ($transferStaffInfo['sys_department_id'] == $this->getConfig('dept_network_management_id')) {

                //片区负责人、大区负责人
                $validHandoverStaff[] = ApprovalFinderServer::getInstance()->getPieceManagerByStoreId($transferStaffInfo['store_id']);
                $validHandoverStaff[] = ApprovalFinderServer::getInstance()->getRegionManagerByStoreId($transferStaffInfo['store_id']);
            }

            if ($transferStaffInfo['sys_department_id'] == $this->getConfig('dept_shop_management_id')) {

                //大区负责人
                $validHandoverStaff[] = ApprovalFinderServer::getInstance()->getRegionManagerByStoreId($transferStaffInfo['store_id']);
            }

            if (in_array($transferStaffInfo['sys_department_id'], [
                $this->getConfig('dept_hub_management_id'),
                $this->getConfig('dept_flash_freight_hub_dep_id'),
            ])) {
                //所属部门负责人
                $validHandoverStaff[] = $departmentInfo['manager_id'] ?? '';
            }

            if (!in_array($handoverStaffInfo['staff_info_id'], $validHandoverStaff)) {
                return ErrCode::JOB_TRANSFER_HANDOVER_STAFF_NOT_VALID_ERROR;
            }
        }

        return ErrCode::SUCCESS;
    }

    /**
     * @description 校验转岗后日期最早为申请日 +1 day
     * @return int
     * @checkRule
     */
    protected function checkAfterDate(): int
    {
        if (empty($this->getAfterDate())) {
            return ErrCode::JOB_TRANSFER_AFTER_DATE_ERROR;
        }
        $afterDate = date('Y-m-d', strtotime($this->getAfterDate()));
        $todayDate = date('Y-m-d', strtotime('+1 day'));
        return strtotime($afterDate) < strtotime($todayDate) ? ErrCode::JOB_TRANSFER_AFTER_DATE_ERROR: ErrCode::SUCCESS;
    }

    /**
     * @description 校验员工在职状态
     * @param $staff_info
     * @param int $err_code
     * @param array $staff_state
     * @return int
     */
    protected function checkStaffOnJobState($staff_info,
        array $staff_state = [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPENSION],
        int $err_code = ErrCode::JOB_TRANSFER_STAFF_NOT_ON_JOB_ERROR
    ): int
    {
        if (in_array($staff_info['state'], $staff_state) &&
            $staff_info['formal'] == HrStaffInfoModel::FORMAL_1 &&
            $staff_info['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF_0
        ) {
            return ErrCode::SUCCESS;
        }
        return $err_code; //工号非在职不可转岗
    }

    /**
     * @description 校验被转岗人不能是申请人
     * @return int
     * @checkRule
     */
    protected function checkTransferStaff(): int
    {
        $jobTransferStaffInfo = $this->getJobTransferStaffInfo();
        $submitterInfo =  $this->getSubmitterInfo();
        if ($jobTransferStaffInfo['staff_info_id'] == $submitterInfo['staff_info_id']) {
            return ErrCode::JOB_TRANSFER_STAFF_BE_SAME_ERROR; //不能为自己申请转岗
        }
        return ErrCode::SUCCESS;
    }

    /**
     * @description 校验是否存在进行中的转岗记录
     * @return int
     * @checkRule
     */
    protected function checkInProcessTransfer(): int
    {
        $jobTransferInfo = $this->getJobTransferInfo();
        if (!empty($jobTransferInfo)) {
            return ErrCode::JOB_TRANSFER_EXIST_TRANSFER_ERROR; //已经存在转岗申请记录
        }
        return ErrCode::SUCCESS;
    }

    /**
     * @description 校验是否非一线职位 & 没有转正
     * @return int
     * @checkRule
     */
    protected function checkProbation(): int
    {
        $probationStatus = $this->getJobTransferStaffProbation();
        $jobTransferStaffInfo = $this->getJobTransferStaffInfo();

        //非一线职位 & 没有转正
        if (strtotime($jobTransferStaffInfo['hire_date']) >= strtotime('2020-06-13 00:00:00') &&
            (
                !$this->checkStaffFrontPosition() &&
                (empty($probationStatus) || $probationStatus['status'] != HrProbationModel::STATUS_FORMAL)
            )
        ) {
            return ErrCode::JOB_TRANSFER_IN_PROBATION_ERROR; //非一线职位需要在BY进行转岗申请
        }
        return ErrCode::SUCCESS;
    }

    /**
     * @description 校验权限
     * @return int
     * @checkRule
     */
    protected function checkPermission(): int
    {
        $submitterInfo        = $this->getSubmitterInfo();
        $manageStores         = $this->getSubmitterInfoManageStores();
        $manageDepartment     = $this->getSubmitterInfoManageDepartments();
        $manageStaffs         = $this->getSubmitterInfoManageStaffId();
        $jobTransferStaffInfo = $this->getJobTransferStaffInfo();

        if (
            in_array($jobTransferStaffInfo['store_id'], $manageStores) ||
            in_array($jobTransferStaffInfo['node_department_id'], $manageDepartment) ||
            in_array($jobTransferStaffInfo['staff_info_id'], $manageStaffs) ||
            in_array($submitterInfo['job_title'], $this->getConfig('job_transfer_position_permission', CommonEnums::SEPARATOR_DEFAULT)) &&
                $jobTransferStaffInfo['sys_department_id'] == $submitterInfo['sys_department_id']
        ) {
            return ErrCode::SUCCESS;
        }
        return ErrCode::JOB_TRANSFER_NO_PERMISSION_ERROR; //无权限为此工号申请转岗
    }

    /**
     * @description 校验权限
     * @return int
     * @checkRule
     */
    protected function checkSpecialPermission(): int
    {
        //可申请范围规则：只能给申请人所在一级部门下以及子部门下的员工导入转岗
        $jobTransferStaffInfo = $this->getJobTransferStaffInfo();
        $submitterStaffInfo   = $this->getSubmitterInfo();

        if ($jobTransferStaffInfo['sys_department_id'] != $submitterStaffInfo['sys_department_id']) {
            return ErrCode::JOB_TRANSFER_NO_PERMISSION_ERROR; //无权限为此工号申请转岗
        }
        return ErrCode::SUCCESS;
    }

    /**
     * @description 校验是否一线职位
     * @return int
     * @checkRule
     */
    protected function checkFrontLine(): int
    {
        //非一线职位
        if (!$this->checkStaffFrontPosition()) {
            return ErrCode::JOB_TRANSFER_NOT_FRONT_LINE_ERROR; //非一线职位需要在BY进行转岗申请
        }
        return ErrCode::SUCCESS;
    }

    /**
     * @description 校验一线职位
     * @return bool
     */
    protected function checkStaffFrontPosition(): bool
    {
        $positionConfig       = $this->getConfig('job_transfer_front_line_position', CommonEnums::SEPARATOR_DEFAULT);
        $jobTransferStaffInfo = $this->getJobTransferStaffInfo();
        if (empty($positionConfig)) {
            return false;
        }

        //一线职位
        if (in_array($jobTransferStaffInfo['job_title'], $positionConfig)) {
            return true;
        }
        return false;
    }

    /**
     * @description 根据错误码返回对应的错误
     * @param $err_code
     * @return string
     */
    public function getErrorMessageKey($err_code): string
    {
        if (empty($err_code)) {
            return '';
        }

        $errCodeMap = [
            ErrCode::JOB_TRANSFER_STAFF_NOT_EXIST_ERROR                       => 'job_transfer.err_msg.1',
            ErrCode::JOB_TRANSFER_STAFF_NOT_ON_JOB_ERROR                      => 'job_transfer.err_msg.2',
            ErrCode::JOB_TRANSFER_STAFF_BE_SAME_ERROR                         => 'job_transfer.err_msg.3',
            ErrCode::JOB_TRANSFER_EXIST_TRANSFER_ERROR                        => 'job_transfer.err_msg.4',
            ErrCode::JOB_TRANSFER_IN_PROBATION_ERROR                          => 'job_transfer.err_msg.5',
            ErrCode::JOB_TRANSFER_NO_PERMISSION_ERROR                         => 'job_transfer.err_msg.6',
            ErrCode::JOB_TRANSFER_HANDOVER_STAFF_NOT_EXIST_ERROR              => 'job_transfer.err_msg.7',
            ErrCode::JOB_TRANSFER_HANDOVER_STAFF_NOT_ON_JOB_ERROR             => 'job_transfer.err_msg.8',
            ErrCode::JOB_TRANSFER_AFTER_DATE_ERROR                            => 'job_transfer.err_msg.9',
            ErrCode::JOB_TRANSFER_HANDOVER_STAFF_NOT_VALID_ERROR              => 'job_transfer.err_msg.10',
            ErrCode::JOB_TRANSFER_HANDOVER_STAFF_IS_TRANSFER_STAFF_DATE_ERROR => 'job_transfer.err_msg.11',
            ErrCode::JOB_TRANSFER_NOT_FRONT_LINE_ERROR                        => 'job_transfer.err_msg.13',
            ErrCode::JOB_TRANSFER_NOT_SUPPORT_AGENT                           => 'job_transfer.err_msg.14',
            ErrCode::JOB_TRANSFER_NATIONALITY_NOT_MY                          => 'job_transfer.err_msg.15',
            ErrCode::JOB_TRANSFER_STORE_ID_ERROR                              => 'job_transfer.err_msg.16',
            ErrCode::JOB_TRANSFER_AGENT_NOT_SUPPORT_JOB_TITLE                 => 'job_transfer.err_msg.17',
            ErrCode::JOB_TRANSFER_AGENT_MISS_ARGS_CAR_TYPE                    => 'miss_args',
            ErrCode::JOB_TRANSFER_AGENT_INVALID_CAR_TYPE                      => 'invalid_car_type',
        ];

        return $errCodeMap[$err_code] ?? '';
    }

    /**
     * @return mixed
     */
    protected function getSubmitterInfoManageDepartments()
    {
        return $this->submitter_info_manage_departments;
    }

    /**
     * @param mixed $submitter_info_manage_departments
     */
    protected function setSubmitterInfoManageDepartments($submitter_info_manage_departments): void
    {
        $this->submitter_info_manage_departments = $submitter_info_manage_departments;
    }

    /**
     * @return mixed
     */
    protected function getSubmitterInfoManageStores()
    {
        return $this->submitter_info_manage_stores;
    }

    /**
     * @param mixed $submitter_info_manage_stores
     */
    protected function setSubmitterInfoManageStores($submitter_info_manage_stores): void
    {
        $this->submitter_info_manage_stores = $submitter_info_manage_stores;
    }

    /**
     * @return mixed
     */
    protected function getSubmitterInfoManageStaffId()
    {
        return $this->submitter_info_manage_staff_id;
    }

    /**
     * @param mixed $submitter_info_manage_staff_id
     */
    protected function setSubmitterInfoManageStaffId($submitter_info_manage_staff_id): void
    {
        $this->submitter_info_manage_staff_id = $submitter_info_manage_staff_id;
    }

    /**
     * @return mixed
     */
    public function getJobTransferStaffInfo()
    {
        return $this->job_transfer_staff_info;
    }

    /**
     * @param mixed $job_transfer_staff_info
     */
    protected function setJobTransferStaffInfo($job_transfer_staff_info): void
    {
        $this->job_transfer_staff_info = $job_transfer_staff_info;
    }

    /**
     * @return mixed
     */
    protected function getSubmitterId()
    {
        return $this->submitter_id;
    }

    /**
     * @param mixed $submitter_id
     */
    protected function setSubmitterId($submitter_id): void
    {
        $this->submitter_id = $submitter_id;
    }

    /**
     * @return mixed
     */
    protected function getJobTransferInfo()
    {
        return $this->job_transfer_info;
    }

    /**
     * @param mixed $job_transfer_info
     */
    protected function setJobTransferInfo($job_transfer_info): void
    {
        $this->job_transfer_info = $job_transfer_info;
    }

    /**
     * @return mixed
     */
    protected function getRules()
    {
        return $this->rules;
    }

    /**
     * @param mixed $rules
     */
    protected function setRules($rules): JobTransferValidateServer
    {
        $this->rules = $rules;
        return $this;
    }

    /**
     * @return mixed
     */
    protected function getJobTransferStaffProbation()
    {
        return $this->job_transfer_staff_probation;
    }

    /**
     * @param mixed $job_transfer_staff_probation
     */
    protected function setJobTransferStaffProbation($job_transfer_staff_probation): void
    {
        $this->job_transfer_staff_probation = $job_transfer_staff_probation;
    }

    /**
     * @return mixed
     */
    protected function getAfterDate()
    {
        return $this->after_date;
    }

    /**
     * @return mixed
     */
    protected function getAfterStoreId()
    {
        return $this->after_store_id;
    }

    /**
     * @return mixed
     */
    public function getAfterPositionId()
    {
        return $this->after_position_id;
    }

    /**
     * @param mixed $after_position_id
     */
    public function setAfterPositionId($after_position_id): void
    {
        $this->after_position_id = $after_position_id;
    }


    /**
     * @param mixed $after_date
     */
    protected function setAfterDate($after_date): void
    {
        $this->after_date = $after_date;
    }

    /**
     * @param mixed $after_date
     */
    protected function setAfterStoreId($after_store_id): void
    {
        $this->after_store_id = $after_store_id;
    }

    /**
     * @return mixed
     */
    protected function getJobHandoverStaffInfo()
    {
        return $this->job_handover_staff_info;
    }

    /**
     * @param mixed $job_handover_staff_info
     */
    protected function setJobHandoverStaffInfo($job_handover_staff_info): void
    {
        $this->job_handover_staff_info = $job_handover_staff_info;
    }

    /**
     * 获取配置
     * @param string $setting_env_code
     * @param null $separator
     * @return mixed
     */
    public function getConfig(string $setting_env_code = '', $separator = null)
    {
        if (empty($setting_env_code)) {
            return '';
        }
        if ($separator !== null) {
            return isset($this->config[$setting_env_code]) && !empty($this->config[$setting_env_code])
                ? explode($separator, $this->config[$setting_env_code])
                : [];
        }
        return isset($this->config[$setting_env_code]) && !empty($this->config[$setting_env_code])
            ? $this->config[$setting_env_code]
            : '';
    }

    public function setConfig(array $config): void
    {
        if (empty($config)) {
            $this->config = [];
            return;
        }
        $configList = (new SettingEnvServer())->listByCode($config);
        $this->config = array_column($configList, 'set_val', 'code');
    }

    /**
     * @return mixed
     */
    public function getSubmitterInfo()
    {
        return $this->submitter_info;
    }

    /**
     * @param mixed $submitter_info
     */
    public function setSubmitterInfo($submitter_info): void
    {
        $this->submitter_info = $submitter_info;
    }

    /**
     * 校验个人代理职位
     * @return int
     */
    protected function checkTransferStaffAgentJobTitle(): int
    {

        $individualContractorJobTitleIds = (new SysServer())->getAgentJAllowJobTitleConfig($this->getAfterStoreId());
        $individualContractorJobTitleIds = explode(',', $individualContractorJobTitleIds);
        if (!empty($individualContractorJobTitleIds) && !empty($this->getAfterPositionId()) && in_array($this->getAfterPositionId(), $individualContractorJobTitleIds)) {
            return ErrCode::SUCCESS;
        }
        return ErrCode::JOB_TRANSFER_AGENT_NOT_SUPPORT_JOB_TITLE;
    }
}