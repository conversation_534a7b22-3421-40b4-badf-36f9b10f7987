<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\FuelApproveModel;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;

class FuelService extends AuditBaseServer
{


    public function __construct($lang = 'zh-CN', $timezone = 'Asia/Bangkok')
    {
        parent::__construct($lang, $timezone);

        $this->auditlistRep = new AuditlistRepository($lang, $timezone);
    }

    const SCENES_START = 'start';
    const SCENES_END = 'end';


    /**
     * 油费补贴 添加
     *
     *
     * @param $staffId
     * @param $params
     *
     * @return bool
     * @throws \Exception
     *
     */
    public function fuelReport($staffId, $params)
    {
        $db = FuelApproveModel::beginTransaction($this);
        try {
            $fuelReport = $this->getLastFuelReport($staffId,true);
            if ($fuelReport && $fuelReport->status == 0) {
                // 存在值并且在用车中
                if ($params['report_scenes'] == self::SCENES_END) {
                    // 结束用车
                    $fuelReport->end_drive_lat = $params['end_drive_lat'];
                    $fuelReport->end_drive_lng = $params['end_drive_lng'];
                    $fuelReport->end_drive_place = $params['end_drive_place'];
                    $fuelReport->end_drive_mileage = $params['end_drive_mileage'];
                    $fuelReport->end_drive_mileage_img = is_array($params['end_drive_mileage_img']) ? $params['end_drive_mileage_img'][0] : (string) $params['end_drive_mileage_img'];
                    $fuelReport->end_drive_place_img = is_array($params['end_drive_place_img']) ? $params['end_drive_place_img'][0] : (string) $params['end_drive_place_img'];
                    $fuelReport->end_drive_time = gmdate('Y-m-d H:i:s');
                    $fuelReport->status = enums::$audit_status['panding'];
                    $fuelReport->updated_at = gmdate('Y-m-d H:i:s');
                    //生成审批流
                    $auditId = $fuelReport->id;
                    $fuelReport->save();
                    if (!$auditId) {
                        throw new \Exception('结束用车失败');
                    }

                    $requestId = (new ApprovalServer($this->lang, $this->timeZone))->create($auditId, AuditListEnums::APPROVAL_TYPE_FUEL, $staffId);
                    if (!$requestId) {

                        throw new \Exception('创建审批流失败');
                    }
                } else {
                    throw new \Exception('存在用车中状态，请结束用车');
                }
            } else {
                // 新增 开始用车
                if ($params['report_scenes'] == self::SCENES_START) {
                    $fuelReport = new FuelApproveModel();
                    $fuelReport->staff_id = $staffId;
                    $fuelReport->serial_no = 'fuel' . $this->getID();
                    $fuelReport->start_drive_time = gmdate('Y-m-d H:i:s');
                    $fuelReport->start_drive_lat = $params['start_drive_lat'];
                    $fuelReport->start_drive_lng = $params['start_drive_lng'];
                    $fuelReport->start_drive_place = $params['start_drive_place'];
                    $fuelReport->start_drive_mileage = $params['start_drive_mileage'];
                    $fuelReport->start_drive_mileage_img = is_array($params['start_drive_mileage_img']) ? $params['start_drive_mileage_img'][0] : (string) $params['start_drive_mileage_img'];
                    $fuelReport->start_drive_place_img = is_array($params['start_drive_place_img']) ? $params['start_drive_place_img'][0] : (string) $params['start_drive_place_img'];
                    $fuelReport->drive_reason = $params['drive_reason'];
                    $fuelReport->status = 0;
                    $fuelReport->created_at = gmdate('Y-m-d H:i:s');
                    $fuelReport->updated_at = gmdate('Y-m-d H:i:s');
                    $fuelReport->save();
                } else {

                    throw new \Exception('不存在用车中状态，请开始用车');
                }
            }

            $db->commit();
        } catch (\Exception $e) {

            $db->rollBack();
            throw $e;

        }
        return true;
    }


    /**
     * 更新油费审批审批状态
     *
     *
     * @param $auditId
     * @param $staffId
     * @param $status
     * @param $cancelreason
     *
     *
     * @return bool
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     *
     */
    public function updateFuelApprove($auditId, $staffId, $status, $cancelreason)
    {
        $db = FuelApproveModel::beginTransaction($this);
        try {
            $fuelApprove = FuelApproveModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $auditId
                ],
                'for_update' => true
            ]);
            $fuelApprove = $fuelApprove ? $fuelApprove->toArray() : [];
            if ($fuelApprove) {
                $approveService = new ApprovalServer($this->lang, $this->timeZone);
                if ($status == enums::$audit_status['approved']) {

                    $approveService->approval($auditId, enums::$audit_type['FUEL'], $staffId);
                } else if ($status == enums::$audit_status['dismissed']) {

                    $approveService->reject($auditId, enums::$audit_type['FUEL'], $cancelreason, $staffId);
                } else if ($status == enums::$audit_status['revoked']) {

                    $approveService->cancel($auditId, enums::$audit_type['FUEL'], $cancelreason, $staffId);
                }

            } else {
                throw new \Exception( $auditId . " auditid not found..." . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log("fuel_approve "
                . "file " . $e->getFile()
                . " line " . $e->getLine()
                . " message " . $e->getMessage()
                . " trace " . $e->getTraceAsString(), "error");
            throw $e;
        }
        return true;
    }


    /**
     * 获取用户最新的 油费补贴数据
     *
     * @param $staffId
     *
     * @return \FlashExpress\bi\App\Models\backyard\FuelApproveModel|\Phalcon\Mvc\Model\ResultInterface
     */
    public function getLastFuelReport($staffId ,$for_update = false)
    {

        return FuelApproveModel::findFirst([
            'conditions' => ' staff_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staffId
            ],
            'order' => ' created_at DESC',
            'for_update' => $for_update
        ]);

    }

    /**
     *
     * 按钮开关
     *
     * @param $staffId
     */
    public function buttonSwitch($staffId)
    {

       
        $fuelApprove = $this->getLastFuelReport($staffId);
        if ($fuelApprove && $fuelApprove->status == 0) {
            // 用车中
            return [
                'start' => 0,
                'end' => 1,
            ];

        } else {
            return [
                'start' => 1,
                'end' => 0,
            ];
        }

    }








    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $fuelApprove = FuelApproveModel::findFirst([
            'columns' => "
                            id,
                            staff_id,
                            serial_no,
                            convert_tz(start_drive_time, '+00:00', '+07:00') as start_drive_time,
                            start_drive_mileage,
                            start_drive_place,
                            start_drive_mileage_img,
                            start_drive_place_img,
                            drive_reason,
                            convert_tz(end_drive_time, '+00:00', '+07:00') as end_drive_time,
                            end_drive_mileage,
                            end_drive_mileage_img,
                            end_drive_place,
                            end_drive_place_img,
                            convert_tz(created_at, '+00:00', '+07:00') as created_at,
                            convert_tz(updated_at, '+00:00', '+07:00') as updated_at,
                            status
                            ",
            'conditions' => ' id = :id: ',
            'bind' => [
                'id' => $auditId
            ]
        ])->toArray();

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($fuelApprove['staff_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }
        $returnData['data']['detail'] = [
            ['key' => $this->getTranslation()->_('apply_parson'), 'value' => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? '')],
            ['key' => $this->getTranslation()->_('apply_department'),'value' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? '')],
            ['key' => $this->getTranslation()->_('start_drive_time'), 'value' => $fuelApprove['start_drive_time']],
            ['key' => $this->getTranslation()->_('start_drive_place'), 'value' => $fuelApprove['start_drive_place']],
            ['key' => $this->getTranslation()->_('mileage_num'), 'value' => $fuelApprove['start_drive_mileage']],
            ['key' => $this->getTranslation()->_('start_drive_mileage_img'), 'value' => [$fuelApprove['start_drive_mileage_img']]],
            ['key' => $this->getTranslation()->_('start_drive_place_img'), 'value' => [$fuelApprove['start_drive_place_img']]],
            ['key' => $this->getTranslation()->_('end_drive_time'), 'value' => $fuelApprove['end_drive_time']],
            ['key' => $this->getTranslation()->_('end_drive_place'), 'value' => $fuelApprove['end_drive_place']],
            ['key' => $this->getTranslation()->_('mileage_num'), 'value' => $fuelApprove['end_drive_mileage']],
            ['key' => $this->getTranslation()->_('mileage_img'), 'value' => [$fuelApprove['end_drive_mileage_img']]],
            ['key' => $this->getTranslation()->_('end_drive_place_img'), 'value' => [$fuelApprove['end_drive_place_img']]],
            ['key' => $this->getTranslation()->_('drive_reason'), 'value' => $fuelApprove['drive_reason']],
        ];

        $data = [
            'title'       => $this->auditlistRep->getAudityType(enums::$audit_type['FUEL']),
            'origin_id'   => $fuelApprove['id'],
            'id'          => $fuelApprove['id'],
            'staff_id'    => $fuelApprove['staff_id'],
            'type'        => enums::$audit_type['FUEL'],
            'created_at'  => $fuelApprove['created_at'],
            'updated_at'  => $fuelApprove['updated_at'],
            'status'      => $fuelApprove['status'],
            'status_text' => $this->auditlistRep->getAuditStatus('10' . $fuelApprove['status']),
            'serial_no'   => $fuelApprove['serial_no'] ?? '',
        ];

        $returnData['data']['head']   = $data;
        return $returnData;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false,
            false,
            false,
            false,
            false,
            false);
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $fuelApproveModel = FuelApproveModel::findFirst($auditId);
        $fuelApprove = $fuelApproveModel ? $fuelApproveModel->toArray() : [];
        $summary = [];
        if ($fuelApprove) {
            $summary = [[
                'key' => 'start_drive_time',
                'value' => show_time_zone($fuelApprove['start_drive_time']),
            ], [
                'key' => 'end_drive_time',
                'value' => show_time_zone($fuelApprove['end_drive_time']),
            ]];
        }
        return $summary;
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $fuelApprove = FuelApproveModel::findFirst($auditId);
            $fuelApprove->status = $state;
            $fuelApprove->save();
        }

        return true;
    }

    /**
     * 样例
     * from_node_id | to_node_id | valuate_formula | valuate_code
     * -------------+------------+-----------------+-------------
     *      4       |     5      |    $p1 == 4     | getSubmitterDepartment
     *
     * 表示当提交人的部门为4时，审批节点4的下一个节点是5
     * 需要在 getWorkflowParams 中返回申请人所在的部门字段
     *
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param $state
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {

        return [];
    }
}