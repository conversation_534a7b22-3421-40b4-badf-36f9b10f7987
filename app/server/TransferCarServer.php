<?php

namespace FlashExpress\bi\App\Server;


use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\TransferCarFeeApplyModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;

class TransferCarServer extends AuditBaseServer
{
    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
    }

    const FEE_AUDIT_STATE_DEFAULT = 1;//未提交
    const FEE_AUDIT_STATE_PENDING = 2;//审批中
    const FEE_AUDIT_STATE_REJECT = 3;//已驳回
    const FEE_AUDIT_STATE_PASS = 4;//已通过


    /**
     *
     * @param $action
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function getRequestData($action, $params): array
    {
        $method = RestClient::METHOD_POST;
        switch ($action) {
            case 'add':
                $url = '/svc/fleet/audit/tandem/scheduling/add';
                break;
            case 'list':
                $url = '/svc/fleet/audit/tandem/scheduling/list';
                break;
            case 'detail':
                $method = RestClient::METHOD_GET;
                $url    = '/svc/fleet/audit/tandem/scheduling/detail';
                break;
            case 'getFleet':
                $method = RestClient::METHOD_GET;
                $url    = '/svc/fleet/audit/tandem/scheduling/get/fleet';
                break;
            case 'getPrice':
                $method = RestClient::METHOD_GET;
                $url    = '/svc/fleet/audit/tandem/scheduling/get/price';
                break;
            case 'getPendingCount':
                $method = RestClient::METHOD_GET;
                $url    = '/svc/fleet/audit/tandem/scheduling/get/pending_count';
                break;
            case 'getDriver':
                $method = RestClient::METHOD_GET;
                $url    = '/svc/fleet/audit/tandem/scheduling/get/driver';
                break;
            case 'fleetDetail':
                $method = RestClient::METHOD_GET;
                $url    = '/svc/fleet/audit/tandem/scheduling/get/fleet/detail';
                break;
            case 'isBigSaleDate':
                $method = RestClient::METHOD_GET;
                $url    = '/svc/fleet/audit/tandem/scheduling/is_big_sale_date';
                break;
            case 'feeAudit':
                $url    = '/svc/fleet/audit/tandem/scheduling/fee/audit';
                break;
            default:
                throw new BusinessException('action error');
        }
        return [$method, $url, $params];
    }

    /**
     * @param $action
     * @param $params
     * @return mixed
     * @throws BusinessException
     * @throws Exception
     */
    public function sendRequest($action, $params)
    {
        $api = new RestClient('nws');
        [$method, $url, $params] = $this->getRequestData($action, $params);
        $res = $api->execute($method, $url, $params, ['Accept-Language' => $this->lang]);
        if (!isset($res['code'])) {
            throw new Exception($this->getTranslation()->_('server_error'));
        }
        if ($res['code'] != 1) {
            $msg = $res['message'];
            throw new BusinessException($msg);
        }
        if (isset($res['error'])) {
            throw new BusinessException($res['error']);
        }
        return $res;
    }


    /**
     * @param $paramIn
     * @return array
     * @throws BusinessException
     */
    protected function beforeApplyFeeValidate($paramIn): array
    {
        $model = TransferCarFeeApplyModel::findFirst([
            'conditions' => "biz_id = :biz_id:",
            'bind'       => [
                'biz_id' => $paramIn['id'],
            ],
            'order'      => 'id desc',
            'for_update' => true,
        ]);
        if (!empty($model)) {
            if ($model->status == enums::APPROVAL_STATUS_PENDING) {
                //审批中，请耐心等待
                throw new BusinessException($this->getTranslation()->_('22687_error_msg_1'));
            }
            if ($model->status == enums::APPROVAL_STATUS_APPROVAL) {
                //审批完成，请问重复提交
                throw new BusinessException($this->getTranslation()->_('22687_error_msg_2'));
            }
        }

        $routes_detail = $this->sendRequest('detail', ['id' => $paramIn['id']]);//车线任务Vehicle route
        if ($routes_detail['data']['status'] != 3) {
            //车线任务未完成，如有疑问，请联系汽运工作人员
            throw new BusinessException($this->getTranslation()->_('22687_error_msg_3'));
        }
        return $routes_detail['data'];
    }


    /**
     * @param $paramIn
     * @return bool
     * @throws BusinessException
     * @throws Exception
     */
    public function applyFee($paramIn): bool
    {
        $db = TransferCarFeeApplyModel::beginTransaction($this);
        try {
            $routes_detail                   = $this->beforeApplyFeeValidate($paramIn);
            $model                           = new TransferCarFeeApplyModel();
            $serial_no                       = 'TC' . $this->getRandomId();
            $model->biz_id                   = $paramIn['id'];
            $model->serial_no                = $serial_no;
            $model->staff_info_id            = $paramIn['staff_id'];
            $model->status                   = enums::APPROVAL_STATUS_PENDING;
            $model->payment_certificate      = $paramIn['payment_certificate'];
            $model->proof_of_van_dispatching = $paramIn['proof_of_van_dispatching'];
            $model->driver_id_card           = $paramIn['driver_id_card'];
            $model->content                  = json_encode($routes_detail, JSON_UNESCAPED_UNICODE);
            if (!$model->save()) {
                throw new Exception('TransferCarFeeApplyModel 保存申请失败');
            }
            $approvalServer = new ApprovalServer($this->lang, $this->timeZone);
            $requestId      = $approvalServer->create($model->id, AuditListEnums::APPROVAL_TYPE_TRANSFER_CAR,
                $paramIn['staff_id']);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }

           $this->sendRequest('feeAudit', ['id' => intval($paramIn['id']),'state'=>self::FEE_AUDIT_STATE_PENDING]);

            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false, false, false, false, false, false);
    }

    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $model             = TransferCarFeeApplyModel::findFirstById($auditId);
            $model->status     = $state;
            $model->updated_at = gmdate('Y-m-d H:i:s');
            $fee_audit_state = self::FEE_AUDIT_STATE_PASS;
            if ($state == enums::APPROVAL_STATUS_REJECTED) {
                $model->reject_reason = $extend['remark'] ?? '';// 原因
                $fee_audit_state      = self::FEE_AUDIT_STATE_REJECT;
            }
            $params = [
                'id'    => $model->biz_id,
                'state' => $fee_audit_state, //2 审核通过 3 驳回
            ];
            AuditCallbackServer::createData(AuditListEnums::APPROVAL_TYPE_TRANSFER_CAR, $params);

            $model->save();
        }
        return true;
    }

    /**
     * 审批结果回调
     * @param $data
     * @return mixed
     * @throws BusinessException
     */
    public function delayCallBack($data)
    {
        return  $this->sendRequest('feeAudit', $data);//车线任务Vehicle route

    }

    public function genSummary(int $auditId, $user)
    {
        $model = TransferCarFeeApplyModel::findFirstById($auditId);

        if (!empty($model)) {
            $content = json_decode($model->content, true);
            //线路名称
            $param = [
                [
                    'key'   => "22687_route_name",
                    'value' => $content['line_name'],
                ],
                [
                    'key'   => "22687_driver",
                    'value' => $content['driver'],
                ],
            ];
        }
        return $param ?? [];
    }

    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $t = $this->getTranslation();
        //[1]获取加班详情数据
        $info = TransferCarFeeApplyModel::findFirst($auditId);
        if (empty($info)) {
            throw new BusinessException($t->_('data_error'));
        }
        $content = json_decode($info->content, true);
        $detailLists['22687_temporary_vehicle_application_number'] = $content['serial_no'];//加班车申请编号
        $detailLists['22687_route_name']                           = $content['line_name'];//线路名称
        $detailLists['22687_proof_of_van_dispatching_number']      = $content['proof_id'];//出车凭证
        $detailLists['22687_driver']                               = $content['driver'].'('.$content['driver_phone'].')';//司机
        $detailLists['22687_driver_id_number']                     = $content['card_num'];//司机身份证号
        $detailLists['22687_driver_id_card_img']                   = $info->driver_id_card;//司机身份证照片
        $detailLists['22687_payment_certificate_img']              = $info->payment_certificate;//支付凭证照片
        $detailLists['22687_proof_of_van_dispatching_img']         = $info->proof_of_van_dispatching;//出车凭证照片
        $detailLists['22687_amount_before_tax']                    = $content['price'];// 金额（税前)
        $detailLists['22687_wht_tax']                              = $content['tax'];//WHT税额
        $detailLists['22687_amount_after_tax']                     = $content['price_after_tax'];//金额

        $auditListRe = new AuditlistRepository($this->lang, $this->timeZone);

        $returnData['data']['detail'] = $this->format($detailLists);
        $data                         = [
            'title'       => $auditListRe->getAudityType(AuditListEnums::APPROVAL_TYPE_TRANSFER_CAR),
            'id'          => $info->id,
            'staff_id'    => $info->staff_info_id,
            'type'        => AuditListEnums::APPROVAL_TYPE_TRANSFER_CAR,
            'created_at'  => show_time_zone($info->created_at),
            'updated_at'  => show_time_zone($info->updated_at),
            'status'      => $info->status,
            'status_text' => $auditListRe->getAuditStatus('10' . $info->status),
            'serial_no'   => $info->serial_no ?? '',
        ];
        $returnData['data']['head']   = $data;
        return $returnData;
    }

    public function getWorkflowParams($auditId, $user, $state = null): array
    {
        return [];
    }

    /**
     * @param $list
     * @return array
     */
    public function format($list): array
    {
        $return = [];
        foreach ($list as $key => $v) {
            $item             = [];
            $item['key']      = $this->getTranslation()->_($key) ?? '';
            $item['key_tips'] = is_array($v) && isset($v['key_tips']) ? $v['key_tips'] : null;
            $item['key_icon'] = is_array($v) && isset($v['key_icon']) ? $v['key_icon'] : null;
            $item['value']    = is_array($v) && isset($v['value']) ? $v['value'] : $v;
            $item['tips']     = is_array($v) && isset($v['tips']) ? $v['tips'] : null;
            $item['color']    = is_array($v) && isset($v['color']) ? $v['color'] : null;
            if (in_array($key,
                ['22687_driver_id_card_img', '22687_payment_certificate_img', '22687_proof_of_van_dispatching_img'])) {
                $item['type'] = enums::APPROVAL_DETAIL_TYPE_PIC_PDF;
                $item['url']  = $item['value'];
            }
            $return[]         = $item;
        }
        return $return;
    }

    /**
     * 获取调车申请权限
     * @param $staffInfo
     * @return bool
     */
    public function getTransferCarPermission($staffInfo): bool
    {
        // 角色
        $intersectResult = array_intersect([
            enums::$roles['DISTRIBUTION_MANAGER'],    // 分拨经理
            enums::$roles['AREA_MANAGER'],            // 大区记录
        ], $staffInfo['positions']);
        if (!empty($intersectResult)) {
            return true;
        }
        $config = (new SettingEnvServer())->getSetVal('transfer_car_staff_list', ',');
        if (in_array($staffInfo['staff_info_id'], $config)) {
            return true;
        }
        return false;
    }

}
