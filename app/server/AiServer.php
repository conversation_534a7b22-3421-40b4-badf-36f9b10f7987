<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\HttpCurl;
use FlashExpress\bi\App\Repository\BySettingRepository;
use Matrix\Exception;

class AiServer extends BaseServer
{
    /**
     * @var AiServer
     */
    private static $instance;

    /**
     * @var bool|mixed|string
     */
    private $host;

    private $secret_id;

    private $secret_key;

    private $method = '/v1/thai-driving-licence-ocr';

    public function __construct()
    {
        parent::__construct($this->lang, $this->timeZone);
    }

    /**
     * 获取实例
     * @param int $type
     * @return AiServer
     */
    public static function getInstance(int $type = enums::IDENTIFY_DRIVING_LICENCE): AiServer
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self($type);
        }
        return self::$instance;
    }

    /**
     * 获取配置信息
     * @param int $type
     * @return $this
     */
    public function setConfig($type = enums::IDENTIFY_DRIVING_LICENCE)
    {
        $settingEnv = new SettingEnvServer();
        switch ($type) {
            case enums::IDENTIFY_DRIVING_LICENCE :
                $this->host = $settingEnv->getSetVal('ai_host');
                $this->secret_id = $settingEnv->getSetVal('ai_secret_id');
                $this->secret_key = $settingEnv->getSetVal('ai_secret_key');
                break;
            case enums::IDENTIFY_REGISTRATION_CERTIFICATE :
                $config =  $settingEnv->getSetVal('ai_r_c_config');
                $config = json_decode($config ,true);
                [$this->host ,$this->method,$this->secret_id, $this->secret_key] = $config;
                break;
            case enums::IDENTIFY_VEHICLE_TAX_CERTIFICATE :
                $config =  $settingEnv->getSetVal('ai_v_t_c_config');
                $config = json_decode($config ,true);
                [$this->host ,$this->method,$this->secret_id, $this->secret_key] = $config;
                break;
            case enums::IDENTIFY_ANALYZE_FACE_QUALITY:
                $config =  $settingEnv->getSetVal('ai_a_f_q_config');
                $config = json_decode($config ,true);
                [$this->host, $this->method, $this->secret_id, $this->secret_key] = $config;
                break;
            case enums::IDENTIFY_ANALYZE_SEARCH_FACE:
                $config =  $settingEnv->getSetVal('ai_a_s_f_config');
                $config = json_decode($config ,true);
                [$this->host, $this->method, $this->secret_id, $this->secret_key] = $config;
                break;
            case enums::IDENTIFY_FACE_CHECK: // 人脸查重
                $config = $settingEnv->getSetVal('ai_face_check_config');
                $config = json_decode($config, true);
                [$this->host, $this->method, $this->secret_id, $this->secret_key] = $config;
                break;
            case enums::IDENTIFY_FACE_DATA_SYNCHRONIZATION:  // 人脸数据同步
                $config = $settingEnv->getSetVal('ai_face_data_sync');
                $config = json_decode($config, true);
                [$this->host, $this->method, $this->secret_id, $this->secret_key] = $config;
                break;
            case enums::IDENTIFY_BANK_CARD :
                $config = $settingEnv->getSetVal('ai_bank_card_config');
                $config = json_decode($config, true);
                [$this->host, $this->method, $this->secret_id, $this->secret_key] = $config;
                break;
            case enums::IDENTIFY_LIVE_CHECK:  // 人脸活体检测
                $config = $settingEnv->getSetVal('ai_live_check_config');
                $config = json_decode($config, true);
                [$this->host, $this->method, $this->secret_id, $this->secret_key] = $config;
                break;
            case enums::IDENTIFY_LIVE_COMPARE_FACE:  // 人脸比对
                $config = $settingEnv->getSetVal('ai_compare_face_config');
                $config = json_decode($config, true);
                [$this->host, $this->method, $this->secret_id, $this->secret_key] = $config;
                break;
            case enums::IDENTIFY_MASK_CHECK:  // 口罩检测
                $config = $settingEnv->getSetVal('ai_mask_check_config');
                $config = json_decode($config, true);
                [$this->host, $this->method, $this->secret_id, $this->secret_key] = $config;
                break;
            case enums::IDENTIFY_FACE_BLACKLIST:  // 人脸黑名单检测
                $config = $settingEnv->getSetVal('ai_face_black_config');
                $config = json_decode($config, true);
                [$this->host, $this->method, $this->secret_id, $this->secret_key] = $config;
                break;
        }

        return $this;
    }

    /**
     * 生成Token
     * @param $method
     * @param string $staff_id
     * @return string
     */
    private function genToken($method, string $staff_id = ""): string
    {
        //获取当前时间、密钥、员工工号(非必须)
        $currentTimestamp = intval(microtime(true) * 1000);
        $secretKey = $this->secret_key;
        $staff_id = $staff_id ?? rand(10000, 99999);

        //组织生成token参数
        $postParamters = [
            "POST",
            $method,
            $currentTimestamp,
            $staff_id,
        ];
        $authStr = implode("\n", $postParamters);

        //生成Token字符串
        $digest = hash_hmac("sha1", $authStr, $secretKey,true);
        return sprintf("%s_%s_%s", $currentTimestamp, $staff_id,  base64_encode($digest));
    }

    /**
     * ai识别图片
     * @param $image
     * @param string|null $staff_info_id
     * @return array
     * @throws ValidationException
     */
    public function aiImageIdenfication($image, string $staff_info_id = ""): array
    {
        if (empty($image)) {
            throw new ValidationException('image empty exception');
        }
        $this->validateConfig();

        $method = $this->method;

        //获取token
        $token = $this->genToken($method, $staff_info_id);

        //组织请求识别的参数
        $header[] = "X-FLE-Token: " . $token;
        $params   = "url={$image}";
        $timeout  = RUNTIME !== 'pro' ? 60: 40; //测试环境识别速度比较慢，如果超时时间太短会无法识别

        //发送请求
        $result = HttpCurl::httpPost($this->host . $method, $params, $header, null, $timeout) ;

        $this->logger->write_log("aiImageIdentification parameters: ". $params . ', result:' . $result, 'info');

        return json_decode($result,true) ?? [];
    }

    /**
     * @description 发送请求
     * @param $params
     * @param int $staff_info_id 员工ID
     * @return mixed
     * @throws ValidationException
     */
    public function send($params, $staff_info_id)
    {
        if (empty($params)) {
            return false;
        }
        $this->validateConfig();

        //获取token
        $token = $this->genToken($this->method, $staff_info_id);

        //组织请求识别的参数
        $header[] = "X-FLE-Token: " . $token;
        $header[] = "Content-Type: application/x-www-form-urlencoded";
        $timeout  = RUNTIME !== 'pro' ? 30: 10;

        //发送请求
        $result = HttpCurl::httpPost($this->host . $this->method, $params, $header, null, $timeout) ;
        $this->logger->write_log([
            'staff_id' => $staff_info_id,
            'url'      => $this->host . $this->method,
            'params'   => $params,
            'result'   => $result,
        ], 'info');

        return json_decode($result,true) ?? [];
    }

    /**
     * @description 发送Post请求
     * @param array $params POST请求参数
     * @param string $staff_info_id 员工ID
     * @return array|bool|mixed
     * @throws ValidationException
     */
    public function sendPostRequest(array $params, string $staff_info_id)
    {
        if (empty($params)) {
            return false;
        }
        $this->validateConfig();

        //获取token
        $token = $this->genTokenByRequestMethod($this->method, $staff_info_id, HttpCurl::REQUEST_METHOD_POST);

        //组织请求识别的参数
        $header[] = "X-FLE-Token: ".$token;
        $header[] = "Content-Type: application/json";

        //发送请求
        $result = HttpCurl::callInterface($this->host.$this->method, json_encode($params, JSON_UNESCAPED_UNICODE),
            HttpCurl::REQUEST_METHOD_POST, $header);
        $this->logger->write_log([
            'staff_id' => $staff_info_id,
            'url'      => $this->host . $this->method,
            'params'   => $params,
            'result'   => $result,
        ], 'info');
        return json_decode($result, true) ?? [];
    }

    /**
     * 发送 patch请求
     * @param array $params
     * @param string $staff_info_id
     * @return array|false|mixed
     * @throws ValidationException
     */
    public function sendPatchRequest(array $params, string $staff_info_id)
    {
        if (empty($params)) {
            return false;
        }
        $this->validateConfig();

        //获取token
        $token = $this->genTokenByRequestMethod($this->method, $staff_info_id, HttpCurl::REQUEST_METHOD_PATCH);

        //组织请求识别的参数
        $header[] = "X-FLE-Token: ".$token;
        $header[] = "Content-Type: application/json";

        //发送请求
        $result = HttpCurl::callInterface($this->host.$this->method, json_encode($params, JSON_UNESCAPED_UNICODE),
            HttpCurl::REQUEST_METHOD_PATCH, $header);
        $this->logger->write_log(sprintf("sendPatchRequest staff id: %d, %s parameters: %s, result: %s", $staff_info_id,
            $this->host.$this->method, json_encode($params, JSON_UNESCAPED_UNICODE), $result), 'info');
        return json_decode($result, true) ?? [];
    }

    /**
     * 发送delete 请求
     * @param array $params
     * @param string $staff_info_id
     * @return array|false|mixed
     * @throws ValidationException
     */
    public function sendDeleteRequest(array $params, string $staff_info_id)
    {
        if (empty($params)) {
            return false;
        }
        $this->validateConfig();

        //获取token
        $token = $this->genTokenByRequestMethod($this->method, $staff_info_id, HttpCurl::REQUEST_METHOD_DELETE);

        //组织请求识别的参数
        $header[] = "X-FLE-Token: ".$token;
        $header[] = "Content-Type: application/json";

        //发送请求
        $result = HttpCurl::callInterface($this->host.$this->method.'/'.$staff_info_id,
            json_encode($params, JSON_UNESCAPED_UNICODE), HttpCurl::REQUEST_METHOD_DELETE, $header);
        $this->logger->write_log(sprintf("sendDeleteRequest staff id: %d, %s parameters: %s, result: %s",
            $staff_info_id,
            $this->host.$this->method, json_encode($params, JSON_UNESCAPED_UNICODE), $result), 'info');
        return json_decode($result, true) ?? [];
    }

    /**
     * 生成Token
     * @param $method
     * @param string $staff_id
     * @param string $request_method
     * @return string
     */
    private function genTokenByRequestMethod($method, string $staff_id = "", string $request_method = ''): string
    {
        //获取当前时间、密钥、员工工号(非必须)
        $currentTimestamp = intval(microtime(true) * 1000);
        $secretKey        = $this->secret_key;
        $staff_id         = $staff_id ?? rand(10000, 99999);

        //组织生成token参数
        $postParams = [
            "POST",
            $method,
            $currentTimestamp,
            $staff_id,
        ];

        if (!empty($request_method) && in_array($request_method,
                [HttpCurl::REQUEST_METHOD_PATCH, HttpCurl::REQUEST_METHOD_PUT, HttpCurl::REQUEST_METHOD_DELETE])) {
            $postParams[0] = trim($request_method);

            if ($request_method == HttpCurl::REQUEST_METHOD_DELETE) {
                $postParams[1] .= '/'.$staff_id;
            }
        }

        $authStr = implode("\n", $postParams);

        //生成Token字符串
        $digest = hash_hmac("sha1", $authStr, $secretKey, true);
        return sprintf("%s_%s_%s", $currentTimestamp, $staff_id, base64_encode($digest));
    }


    /**
     * @description 发送请求
     * @param array $params POST请求参数
     * @param int $staff_info_id 员工ID
     * @return array|bool|mixed
     * @throws ValidationException
     */
    public function sendEx($params, $staff_info_id)
    {
        if (empty($params)) {
            return false;
        }
        $this->validateConfig();
        
        //获取token
        $token = $this->genToken($this->method, $staff_info_id);

        if (is_array($params)) {
            $data = json_encode($params, JSON_UNESCAPED_UNICODE);
        } else {
            $data = $params;
        }

        //组织请求识别的参数
        $header[] = "X-FLE-Token: " . $token;
        $header[] = "Content-Type: application/json";
        $timeout  = RUNTIME !== 'pro' ? 30: 10;

        //发送请求
        $result = HttpCurl::httpPost($this->host . $this->method, $data, $header, null, $timeout) ;
        $this->logger->write_log(sprintf("staff id: %d, %s parameters: %s, result: %s", $staff_info_id,
            $this->host.$this->method, $data, $result), 'info');

        return json_decode($result,true) ?? [];
    }

    /**
     * 校验请求 ai 服务 相关配置参数
     * @throws ValidationException
     */
    public function validateConfig()
    {
        if(empty($this->host) || empty($this->method) || empty($this->secret_id) || empty($this->secret_key)) {
            throw new ValidationException('ai server config is can not empty!');
        }
    }

    /**
     *
     * 过滤字符串中特殊字符
     *
     * 该方法过滤的特殊字符都是会导致php报错的特殊字符，严格慎用过滤其他字符！！！
     *
       替换的字符要和要过滤 字符 数组的索引值对应
     *
     * 用于AI 识别出的字符串，进行过滤
     */
    public static function stringSpecialCharsReplace($name)
    {
        if(empty($name)){
            return  $name;
        }
        //去除“-”，“—”，“/”，“·”，“.”逗号，空格，换行
        $search_arr = ['-', '—', '/', '·', '.', ',', '，', ' ', '\n', '\r', '\r\n'];
        $replace_arr = [''];//特殊字符对应的替代字符
        return str_replace($search_arr,$replace_arr,$name);
    }


    /**
     * @description 用提交过来的人脸图片，与该员工的人脸底片进行比对
     * @param $params
     * @param $staff_info_id
     * @return array
     * @throws ValidationException
     */
    public function sendRequestWithHttpCode($params, $staff_info_id): array
    {
        if (empty($params)) {
            return [];
        }
        $this->validateConfig();

        //获取token
        $token = $this->genToken($this->method, $staff_info_id);

        //组织请求识别的参数
        $header[] = "X-FLE-Token: " . $token;
        $header[] = "Content-Type: application/x-www-form-urlencoded";
        $timeout  = RUNTIME !== 'pro' ? 30: 10;

        //发送请求
        $result = HttpCurl::httpPostWithHttpCode($this->host . $this->method, $params, $header, null, $timeout) ;
        $this->logger->write_log(sprintf("staff id: %d, %s parameters: %s, result: %s", $staff_info_id,
            $this->host.$this->method, json_encode($params, JSON_UNESCAPED_UNICODE), json_encode($result)), 'info');

        //$http_code = $result['http_code'];
        //$this->logger->write_log("sendRequestWithHttpCode {$http_code} ".$params.'  ' . $result['response'], 'info');
        $result['response'] = json_decode($result['response'],true);

        return $result;
    }

    /**
     * AI-比较逻辑-身份证
     * @param $params
     * @return false|mixed
     */
    public function ai_compare($params)
    {
        // method：
        // aiCompareIdCard = 身份证，aiCompareSocialSecuritySystemCard = 社保卡 ，aiCompareTaxIdCard = 税卡
        // aiComparePhilHealthCard = 医保卡,  aiComparePagIbigCard = 公积金
        $method =  $params['method'] ?? '';
        if (empty($method)){
            return [];
        }
        $rpc = (new ApiClient("winhr_rpc", '', $method, $this->lang));
        $rpc->setParams([
            'ai_data'    => $params['ai_data'] ?? [],
            'staff_info' => $params['staff_info'] ?? [],
        ]);
        $result = $rpc->execute();
        $this->logger->write_log("ai_compare params: " . json_encode($params,
                JSON_UNESCAPED_UNICODE) . ', result:' . json_encode($result, JSON_UNESCAPED_UNICODE), 'info');
        if (!empty($result) && !empty($result['result']['data']) && $result['result']['code'] == 1 ) {
            return $result['result']['data'];
        } else {
            return [];
        }
    }
}