<?php
namespace FlashExpress\bi\App\Server;
use FlashExpress\bi\App\Models\xxljob\XxljobTaskModel;
use FlashExpress\bi\App\Models\xxljob\XxljobUserModel;

class XxljobTaskServer extends BaseServer
{
    
    public function getList($param)
    {
        if(empty($param) && $param['data']){
            return [];
        }
        $ids = explode(',',$param['data']);
        $data = XxljobTaskModel::find([
            //'columns' => 'staff_info_id',
            'conditions' => 'id in ({id:array}) ',
            'bind' => ['id' => $ids]
        ])->toArray();
        return $data ?? [];
    }

    public function getUserList($params)
    {
        if(empty($param) && $params['data']){
            return [];
        }
        $ids = explode(',',$params['data']);
        return XxljobUserModel::find([
            //'columns' => 'staff_info_id',
            'conditions' => 'id in ({id:array}) ',
            'bind' => ['id' => $ids]
        ])->toArray();
    }

    public function JobSave($param)
    {
        if(empty($param)){
            return false;
        }

        foreach ($param as $key => $val) {
            $m = XxljobTaskModel::findFirst([
                //'columns' => 'staff_info_id',
                'conditions' => 'regtask = :regtask: ',
                'bind' => ['regtask' => $val['regtask']]
            ]);

            if(!empty($m)){
                continue;
            }
            $task_path = $val['task_path'];
            if(strpos($val['task_path'],'fbi.flash')){
                $cc = strtolower(env('country_code', 'Th'));
                switch ($cc) {
                    case 'th':
                        $task_path = "cd /mnt/www/fbi.flashexpress.com/";
                        break;
                    default:
                        $task_path = sprintf("cd /mnt/www/fbi.flashexpress.%s",$cc);
                        break;
                }
            }
            $m = new XxljobTaskModel();
            $m->regtask = $val['regtask'];
            $m->drive = $val['drive'];
            $m->task_path = $task_path;
            $m->state = $val['state'];
            $m->remark = $val['remark'];
            $m->save();
        }
        return true;
    }

}