<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AttendanceWhiteListModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetBusinessModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetDetailModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetMessageModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Repository\ProbationTargetRepository;
use Exception;

class ProbationTargetServer extends BaseServer
{
    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->add_hour = $this->getDI()['config']['application']['add_hour'];
    }

    /**
     * 获取消息详情
     * @param $param
     * @return array
     * @throws ValidationException
     * @throws BusinessException
     * @throws Exception
     */
    public function getProbationTargetMsg($param): array
    {
        $this->logger->write_log([
            'title'  => '获取消息详情',
            'func'   => 'getProbationTargetMsg',
            'opt'    => '开始',
            'params' => $param,
        ], 'info');

        $businessId = $param['business_id'] ?? 0;
        $type       = $param['type'] ?? 0;
        $userId     = $param['user_id'] ?? 0;
        $msgId      = $param['msg_id'] ?? '';

        $probationTargetRepository = (new ProbationTargetRepository());

        //获取业务数据
        $businessInfo = $probationTargetRepository->getTargetBusinessInfo([
            'id' => $businessId,
        ], '
            id,target_id,target_detail_id,stage,staff_info_id,staff_state,staff_sign_time
            ,staff_sign_url,manager_id,manager_state,manager_sign_time,manager_sign_url,
            pdf_path_th,pdf_path_zh,pdf_path_en,state'
        );

        if (!$businessInfo) {
            throw new ValidationException($this->t->_('target_business_data_empty'));
        }

        //获取pdf文件数据
        $langKey = 'pdf_path_'.$this->getLangKey($this->lang);

        $businessInfo['pdf_path'] = $businessInfo[$langKey] ?? '';

        if (empty($businessInfo['pdf_path'])) {
            $businessInfo['pdf_path'] = $this->getHcmPdfPath($businessId, $userId, $this->lang);

            if (empty($businessInfo['pdf_path'])) {
                throw new Exception('Pdf Path Probation Get Error !');
            }
        }

        //验证最新消息是否有效
        $newMessageInfo = $probationTargetRepository->getTargetMessageInfo([
            'target_business_id' => $businessId,
            'staff_info_id'      => $userId,
            'type'               => $type,
        ]);
        //处理消息已读状态
        $this->dealMessageState($type, $msgId, $businessInfo);

        $data = $this->formatBusinessInfo($type, $businessInfo, $newMessageInfo, $msgId);

        $this->logger->write_log([
            'title' => '试用期目标签字-未获取到最新消息',
            'func'  => 'getProbationTargetMsg',
            'opt'   => '返回数据',
            'res'   => $data,
        ], 'info');

        return $data;
    }

    /**
     * 处理消息已读状态
     * @param $type
     * @param $msgId
     * @param $probationTargetBusiness
     * @return true
     */
    protected function dealMessageState($type, $msgId, $probationTargetBusiness): bool
    {
        if (!in_array($type,
            [HrProbationTargetMessageModel::TYPE_STAFF, HrProbationTargetMessageModel::TYPE_MANAGER])) {
            return false;
        }
        //目标已撤销 消息直接改已读
        if ($probationTargetBusiness['state'] == HrProbationTargetBusinessModel::STATE_CANCEL) {
            return (new BackyardServer($this->lang, $this->timeZone))->has_read_operation($msgId);
        }
        if ($type == HrProbationTargetMessageModel::TYPE_STAFF && $probationTargetBusiness['staff_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS) {
            return (new BackyardServer($this->lang, $this->timeZone))->has_read_operation($msgId);
        }
        if ($type == HrProbationTargetMessageModel::TYPE_MANAGER &&  ($probationTargetBusiness['staff_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_REJECT
                || $probationTargetBusiness['manager_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS)) {
            return (new BackyardServer($this->lang, $this->timeZone))->has_read_operation($msgId);
        }
        return false;
    }

    /**
     * 验证消息是否需要延期拦截
     * @param $staff_id
     * @param $msgInfo
     * @return false
     */
    public function checkMessageDelay($staff_id,$msgInfo)
    {
        $content = json_decode($msgInfo['message'], true);
        if (empty($content)) {
            return false;
        }
        $businessId = $content['business_id'];
        $type       = $content['type'];
        if (empty($businessId) || empty($type)) {
            return false;
        }
        // 考勤打卡白名单
        $isInfWhite = (new WhiteListServer())->isInfWhiteList($staff_id, date('Y-m-d'),
            [AttendanceWhiteListModel::TYPE_PAID_LOCALLY, AttendanceWhiteListModel::TYPE_NOT_PAID_LOCALLY]);
        if ($isInfWhite) {
            return $msgInfo['id'];
        }

        $probationTargetRepository = new ProbationTargetRepository();

        //获取业务数据
        $businessInfo = $probationTargetRepository->getTargetBusinessInfo([
            'id' => $businessId,
        ], 'send_time,staff_sign_time'
        );
        if ($type == HrProbationTargetMessageModel::TYPE_STAFF) {
            //签字时间
            $sendDate = show_time_zone($businessInfo['send_time'], 'Y-m-d');
            if (time() > strtotime("{$sendDate} + 2 days")) {
                return false;
            }
            return $msgInfo['id'];
        }

        if ($type == HrProbationTargetMessageModel::TYPE_MANAGER) {
            //签字时间两天限制打卡
            $signTime = show_time_zone($businessInfo['staff_sign_time'], 'Y-m-d');
            if (time() > strtotime("{$signTime} + 2 days")) {
                return false;
            }
            return $msgInfo['id'];
        }
        return false;
    }


    /**
     * 格式化业务信息，组合员工和经理的签名数据及业务状态等
     *
     * @param mixed $type 业务类型标识
     * @param array $businessInfo 包含业务详细信息的数组，需包含员工和经理的签名URL、时间及状态等字段
     * @param array $messageInfo 包含消息相关信息的数组，可能包含msg_id字段
     * @param string $msgId 可选的消息ID，默认为空字符串
     * @return array 返回格式化后的业务信息数组，包含：
     *               - business_id 业务ID
     *               - business_state 业务状态（默认取HrProbationTargetBusinessModel::STATE_NORMAL）
     *               - pdf_path PDF文件路径
     *               - type 原始传入的业务类型
     *               - new_msg_id 消息ID（优先取自$messageInfo）
     *               - msg_id 传入或生成的消息ID
     *               - staff_msg 员工签名数据（图片/时间/状态）
     *               - manger_msg 经理签名数据（图片/时间/状态）
     */
    public function formatBusinessInfo($type, $businessInfo, $messageInfo, $msgId = ''): array
    {
        // 构建员工签名信息，包括签名图片、时间（转换时区后）及状态
        $staffMsg = [
            'sign_img'  => $businessInfo['staff_sign_url'] ?? '',
            'sign_time' => show_time_zone($businessInfo['staff_sign_time']),
            'state'     => $businessInfo['staff_state'],
        ];

        // 构建经理签名信息，结构同员工信息
        $managerMsg = [
            'sign_img'  => $businessInfo['manager_sign_url'] ?? '',
            'sign_time' => show_time_zone($businessInfo['manager_sign_time']),
            'state'     => $businessInfo['manager_state'],
        ];

        // 组合最终返回的业务信息结构
        return [
            'business_id'    => $businessInfo['id'],
            'business_state' => $businessInfo['state'] ?? (string)HrProbationTargetBusinessModel::STATE_NORMAL,
            'pdf_path'       => $businessInfo['pdf_path'],
            'type'           => $type,
            'new_msg_id'     => $messageInfo['msg_id'] ?? '',
            'msg_id'         => $msgId,
            'staff_msg'      => $staffMsg,
            'manager_msg'    => $managerMsg,
        ];
    }


    /**
     * 提交试用期目标结果并进行消息签字
     * 处理试用期员工的目标完成情况提交，并进行相关消息的签字确认
     * @param $params
     * @return bool 返回操作结果，成功返回true，失败返回false
     * @throws ValidationException 当提交参数验证失败时抛出异常
     */
    public function probationTargetResultSubmit($params): bool
    {
        //写日志
        $this->logger->write_log([
            'title'  => '消息签字',
            'func'   => 'probationTargetResultSubmit',
            'opt'    => '开始',
            'params' => $params,
        ], 'info');

        $businessId = $params['business_id'] ?? 0;
        $type       = $params['type'] ?? 0;
        $userId     = $params['user_id'] ?? 0;
        $signUrl    = $params['sign_url'] ?? '';
        $date       = gmdate('Y-m-d H:i:s');

        $probationTargetRepository = (new ProbationTargetRepository());

        $db        = $this->getDI()->get("db");
        $messageDb = $this->getDI()->get('db_coupon');
        $db->begin();
        $messageDb->begin();

        try {
            //获取业务数据
            $businessInfo = $probationTargetRepository->getTargetBusinessInfo([
                'id' => $businessId,
            ], '
                id,target_id,target_detail_id,stage,staff_info_id,staff_state,staff_sign_time
                ,staff_sign_url,manager_id,manager_state,manager_sign_time,manager_sign_url,
                pdf_path_th,pdf_path_zh,pdf_path_en,state,staff_data'
            );

            if (!$businessInfo) {
                throw new ValidationException($this->t->_('target_business_data_empty'));
            }

            //获取target
            $targetInfo = $probationTargetRepository->getTargetInfoByTargetId($businessInfo['target_id']);

            //已加入白名单不需要操作
            if (empty($targetInfo)) {
                throw new ValidationException($this->t->_('target_data_empty_check_info'));
            }

            //已撤回
            if ($businessInfo['state'] != HrProbationTargetBusinessModel::STATE_NORMAL) {
                throw new ValidationException($this->t->_('target_business_data_empty'));
            }

            //写日志
            $this->logger->write_log([
                'title'  => '消息签字',
                'func'   => 'probationTargetResultSubmit',
                'opt'    => '验证完成',
                'params' => $businessInfo,
            ], 'info');

            $staffData   = !empty($businessInfo['staff_data']) ? json_decode($businessInfo['staff_data'], true) : [];
            $managerId   = $staffData['manger'] ?? 0;
            $staffInfoId = $staffData['staff_info_id'] ?? 0;

            //查看签字状态
            if ($type == HrProbationTargetMessageModel::TYPE_STAFF) {
                //验证签字
                if ($userId != $businessInfo['staff_info_id']) {
                    throw new ValidationException($this->t->_('probation_target_sign_staff_check_error'));
                }

                //员工签字
                if ($businessInfo['staff_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS) {
                    throw new ValidationException($this->t->_('target_business_sign_success_not_operate'));
                }

                //数据更新 更新业务表状态
                $busRes = $probationTargetRepository->saveTargetBusiness([
                    'id'              => $businessId,
                    'staff_state'     => HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS,
                    'manager_id'      => $managerId,
                    'staff_sign_time' => $date,
                    'staff_sign_url'  => $signUrl,
                ]);
            } else {
                //验证签字,后续产品会优化直线上级变更，重新生成PDF操作等！
                if ($userId != $managerId) {
                    throw new ValidationException($this->t->_('probation_target_sign_manager_oline_manager_check_error'));
                }

                //上级签字
                if ($businessInfo['manager_state'] == HrProbationTargetBusinessModel::MANAGER_SIGN_STATE_SUCCESS) {
                    throw new ValidationException($this->t->_('target_business_sign_success_not_operate'));
                }

                $busRes = $probationTargetRepository->saveTargetBusiness([
                    'id'                => $businessId,
                    'manager_state'     => HrProbationTargetBusinessModel::MANAGER_SIGN_STATE_SUCCESS,
                    'manager_sign_time' => $date,
                    'manager_sign_url'  => $signUrl,
                ]);
            }

            if (!$busRes) {
                throw new Exception('save Target Business Error! ');
            }

            //插入签字日志
            $messageRes = $probationTargetRepository->saveTargetMessage([
                'target_id'          => $businessInfo['target_id'],
                'target_business_id' => $businessId,
                'type'               => $type,
                'staff_info_id'      => $userId,
                'sign_state'         => HrProbationTargetMessageModel::SIGN_STATE_SUCCESS,
                'sign_time'          => $date,
                'sign_url'           => $signUrl,
                'source_type'        => HrProbationTargetMessageModel::SOURCE_TYPE_SIGN,
                'operate_id'         => $userId,
            ]);

            if (!$messageRes) {
                throw new Exception('save Target Message Error! ');
            }

            //给上级发送消息
            if ($type == HrProbationTargetMessageModel::TYPE_STAFF) {
                if ($managerId) {
                    $res = $this->sendManagerMesssage($managerId, $businessId, $staffInfoId);
                    $this->logger->write_log([
                        'title' => '消息签字',
                        'func'  => 'probationTargetResultSubmit',
                        'opt'   => '上级消息发送完成',
                        'res'   => $res,
                    ], 'info');

                    if (!isset($res[1]) || $res[1] != 1) {
                        throw new Exception('Target Message Manager Send Error');
                    }

                    $msgId = $res[2][0] ?? '';

                    // 添加空值校验
                    if (empty($msgId)) {
                        throw new Exception('Message ID generation failed');
                    }

                    $messageManagerRes = $probationTargetRepository->saveTargetMessage([
                        'target_id'          => $businessInfo['target_id'],
                        'target_business_id' => $businessId,
                        'msg_id'             => $msgId,
                        'type'               => HrProbationTargetMessageModel::TYPE_MANAGER,
                        'source_type'        => HrProbationTargetMessageModel::SOURCE_TYPE_SIGN,
                        'staff_info_id'      => $managerId,
                        'sign_state'         => HrProbationTargetMessageModel::SIGN_STATE_WAIT,
                        'operate_id'         => $userId,
                    ]);

                    if (!$messageManagerRes) {
                        throw new Exception('save Target Message Manager Error! ');
                    }
                }
            }

            //再次查询业务状态
            $businessInfoV2 = $probationTargetRepository->getTargetBusinessInfo([
                'id' => $businessId,
            ], '
                id,target_id,target_detail_id,stage,staff_state,manager_state'
            );

            //签字状态
            $signDetailState = HrProbationTargetDetailModel::SIGN_STATE_WAIT;
            $signState       = HrProbationTargetModel::SIGN_STATE_WAIT;

            if ($businessInfoV2['stage'] == HrProbationTargetDetailModel::STAGE_SECOND) {
                $signState = HrProbationTargetModel::SIGN_STATE_ADJUST_WAIT;
            }

            if (
                $businessInfoV2['staff_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS
                && $businessInfoV2['manager_state'] == HrProbationTargetBusinessModel::MANAGER_SIGN_STATE_SUCCESS
            ) {
                //给BP发抄送消息
                $signDetailState = HrProbationTargetModel::SIGN_STATE_FINISH;
                $signState = HrProbationTargetModel::SIGN_STATE_FINISH;
                if ($businessInfoV2['stage'] == HrProbationTargetDetailModel::STAGE_SECOND) {
                    $signState = HrProbationTargetModel::SIGN_STATE_ADJUST_FINISH;
                }
            }

            //更新detail 签字状态
            $detailData = [
                'id'         => $businessInfoV2['target_detail_id'],
                'sign_state' => $signDetailState,
            ];

            $detailRes = $probationTargetRepository->saveTargetDetail($detailData);

            $this->logger->write_log([
                'title'  => '消息签字',
                'func'   => 'probationTargetResultSubmit',
                'opt'    => '更新详情表数据',
                'params' => $detailData,
                'res'    => $detailRes,
            ], 'info');

            if (!$detailRes) {
                throw new Exception('save Target Detail Error! ');
            }

            //更新target 签字状态
            $targetData = [
                'id'         => $businessInfoV2['target_id'],
                'sign_state' => $signState,
            ];

            $targetRes = $probationTargetRepository->saveTarget($targetData);

            $this->logger->write_log([
                'title'  => '消息签字',
                'func'   => 'probationTargetResultSubmit',
                'opt'    => '更新主表数据',
                'params' => $targetData,
                'res'    => $targetRes,
            ], 'info');

            if (!$targetRes) {
                throw new Exception('save Target Error! ');
            }

            if (!$db->commit() || !$messageDb->commit()) {
                throw new Exception('save Target Commit Error! ');
            }

            //给BP抄送
            if ($signDetailState == HrProbationTargetModel::SIGN_STATE_FINISH) {
                //抄送数据
                $rmq = new RocketMQ('probation-target-msg');
                $rmq->setShardingKey($businessId);

                $sendData = [
                    'type'               => RocketMQ::TAG_PROBATION_TARGET_MSG,
                    'staff_info_id'      => $businessInfo['staff_info_id'],
                    'target_business_id' => $businessId,
                    'source_type'        => HrProbationTargetMessageModel::SOURCE_TYPE_SIGN,
                    'operate_id'         => $userId,
                ];

                $rid = $rmq->sendOrderlyMsg($sendData);

                $this->logger->write_log([
                    'title'  => '消息签字',
                    'func'   => 'probationTargetResultSubmit',
                    'opt'    => 'BP抄送',
                    'params' => $sendData,
                ], $rid ? 'info' : 'error');
            }
        } catch (ValidationException $exception) {
            $db->rollback();
            $messageDb->rollback();
            //写日志
            $this->logger->write_log([
                'title'  => '消息签字',
                'func'   => 'probationTargetResultSubmit',
                'opt'    => '告警',
                'params' => $exception->getMessage(),
            ], 'info');

            throw $exception;
        } catch (Exception $exception) {
            $db->rollback();
            $messageDb->rollback();
            throw $exception;
        }

        return true;
    }

    /**
     * 发送上级消息
     * @param $managerId
     * @param $businessId
     * @param $staffInfoId
     * @return array
     */
    public function sendManagerMesssage($managerId, $businessId, $staffInfoId): array
    {
        $id = time().$managerId.rand(1000000, 9999999);

        //获取员工信息
        $staffInfo            = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId);
        $param['staff_users'] = [$managerId];

        //获取上级语言环境
        $lang = (new StaffServer())->getLanguage($managerId);

        $param['message_title'] = $this->getTranslationByLang($lang)->_('probation_target_message_manager_sign', [
            'staff_info_id' => $staffInfoId,
            'staff_name'    => $staffInfo['name'] ?? '',
        ]);

        $param['message_content'] = json_encode([
            'type'        => HrProbationTargetMessageModel::TYPE_MANAGER,
            'business_id' => $businessId,
        ]);

        $param['id']                 = $id;
        $param['staff_info_ids_str'] = $managerId;
        $param['category']           = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_TARGET');;

        return (new MessageCourierServer())->add_kit_message($param);
    }

    /**
     * 驳回签字
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function probationTargetResultReject($params): bool
    {
        $this->logger->write_log([
            'title'  => '驳回签字',
            'func'   => 'probationTargetResultReject',
            'opt'    => '开始',
            'params' => $params,
        ], 'info');

        $businessId   = $params['business_id'] ?? 0;
        $type         = $params['type'] ?? 0;
        $userId       = $params['user_id'] ?? 0;
        $businessType = $params['business_type'] ?? '';
        $date         = gmdate('Y-m-d H:i:s');

        $probationTargetRepository = (new ProbationTargetRepository());

        $db        = $this->getDI()->get("db");
        $messageDb = $this->getDI()->get('db_coupon');
        $db->begin();
        $messageDb->begin();

        try {
            //获取业务数据
            $businessInfo = $probationTargetRepository->getTargetBusinessInfo([
                'id' => $businessId,
            ], '
                id,target_id,target_detail_id,stage,staff_info_id,staff_state,staff_sign_time
                ,staff_sign_url,manager_id,manager_state,manager_sign_time,manager_sign_url,
                pdf_path_th,pdf_path_zh,pdf_path_en,state'
            );

            if (!$businessInfo) {
                throw new ValidationException($this->t->_('target_business_data_empty'));
            }

            //已撤回
            if ($businessInfo['state'] != HrProbationTargetBusinessModel::STATE_NORMAL) {
                throw new ValidationException($this->t->_('target_business_data_empty'));
            }

            //获取target
            $targetInfo = $probationTargetRepository->getTargetInfoByTargetId($businessInfo['target_id']);

            //已加入白名单不需要操作
            if (empty($targetInfo)) {
                throw new ValidationException($this->t->_('target_data_empty_check_info'));
            }

            //验证员工是否已经转正
            $probationInfo = HrProbationModel::findFirst(
                [
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind'       => [
                        'staff_info_id' => $businessInfo['staff_info_id'],
                    ],
                ]
            );

            if ($probationInfo) {
                $probationInfo = $probationInfo->toArray();

                //用户已经转正
                if ($probationInfo['status'] == HrProbationModel::STATUS_FORMAL) {
                    throw new ValidationException($this->t->_('staff_probation_state_corrected_not_operate'));
                }

                //用户第二阶段发起不可以驳回签字
                if ($probationInfo['second_audit_status'] != HrProbationModel::SECOND_AUDIT_STATUS_WAIT) {
                    throw new ValidationException($this->t->_('staff_second_audit_status_wait_not_operate'));
                }

                //用户第一阶段已经发起不可以驳回签字
                if (
                    ($probationInfo['first_audit_status'] != HrProbationModel::FIRST_AUDIT_STATUS_WAIT)
                    && ($businessInfo['stage'] == HrProbationTargetDetailModel::STAGE_FIRST)
                ) {
                    throw new ValidationException($this->t->_('staff_first_audit_status_wait_not_operate'));
                }
            }

            //验证上级
            if (
                ($type == HrProbationTargetMessageModel::TYPE_MANAGER)
                &&
                $userId != $businessInfo['manager_id']) {
                throw new ValidationException($this->t->_('probation_target_sign_manager_oline_manager_check_error'));
            }

            //验证上级是否在线
            if (
                $type == HrProbationTargetMessageModel::TYPE_MANAGER
                &&
                $businessType == HrProbationTargetBusinessModel::BUSINESS_TYPE_MANAGER
            ) {
                throw new ValidationException($this->t->_('probation_target_sign_manager_business_type_error'));
            }

            //验证当前消息是否存在
            $messageInfo = $probationTargetRepository->getTargetMessageInfo([
                'target_business_id' => $businessId,
                'staff_info_id'      => $userId,
                'type'               => $type,
            ]);

            if (empty($messageInfo)) {
                throw new ValidationException($this->t->_('target_business_message_empty'));
            }

            $this->logger->write_log([
                'title'  => '驳回签字',
                'func'   => 'probationTargetResultReject',
                'opt'    => '验证完成',
                'params' => $messageInfo,
            ], 'info');

            //查看签字状态
            if ($businessType == HrProbationTargetBusinessModel::BUSINESS_TYPE_STAFF) {
                if ($businessInfo['staff_state'] != HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS) {
                    throw new ValidationException($this->t->_('target_business_sign_not_success_not_operate'));
                }

                //数据更新 更新业务表状态
                $busRes = $probationTargetRepository->saveTargetBusiness([
                    'id'                => $businessId,
                    'staff_state'       => HrProbationTargetBusinessModel::STAFF_SIGN_STATE_REJECT,
                    'staff_reject_time' => $date,
                ]);

                $signStaffInfoId = $businessInfo['staff_info_id'];
                $messageType     = HrProbationTargetMessageModel::TYPE_STAFF;
                $messageTitle    = 'probation_target_message_staff_reject';
            } else {
                if ($businessInfo['manager_state'] != HrProbationTargetBusinessModel::MANAGER_SIGN_STATE_SUCCESS) {
                    throw new ValidationException($this->t->_('target_business_sign_not_success_not_operate'));
                }

                $busRes = $probationTargetRepository->saveTargetBusiness([
                    'id'                  => $businessId,
                    'manager_state'       => HrProbationTargetBusinessModel::MANAGER_SIGN_STATE_REJECT,
                    'manager_reject_time' => $date,
                ]);

                $signStaffInfoId = $businessInfo['manager_id'];
                $messageType     = HrProbationTargetMessageModel::TYPE_MANAGER;
                $messageTitle    = 'probation_target_message_manager_reject';
            }

            if (!$busRes) {
                throw new Exception('save Target Business Error! ');
            }

            //插入驳回日志
            $messageSignInfoData = [
                'target_id'          => $businessInfo['target_id'],
                'target_business_id' => $businessId,
                'type'               => $messageType,
                'staff_info_id'      => $signStaffInfoId,
                'sign_state'         => HrProbationTargetMessageModel::SIGN_STATE_REJECT,
                'reject_time'        => $date,
                'source_type'        => HrProbationTargetMessageModel::SOURCE_TYPE_SIGN,
                'operate_id'         => $userId,
            ];

            $messageSignInfoRes = $probationTargetRepository->saveTargetMessage($messageSignInfoData);

            $this->logger->write_log([
                'title'  => '驳回签字',
                'func'   => 'probationTargetResultReject',
                'opt'    => '消息更新完成',
                'params' => $messageSignInfoData,
                'res'    => $messageSignInfoRes,
            ], 'info');

            if (!$messageSignInfoRes) {
                throw new Exception('save Target Message Sign Error! ');
            }

            $staffInfo = (new HrStaffInfoModel())->getOneByStaffId($businessInfo['staff_info_id']);

            $msgId = time().$signStaffInfoId.rand(1000000, 9999999);

            //获取签字人系统语言
            $lang                           = (new StaffServer())->getLanguage($signStaffInfoId);
            $messageSendData['staff_users'] = [$signStaffInfoId];

            $messageSendData['message_title'] = $this->getTranslationByLang($lang)->_($messageTitle, [
                'staff_info_id' => $staffInfo['staff_info_id'] ?? '',
                'staff_name'    => $staffInfo['name'] ?? '',
            ]);

            $messageSendData['message_content'] = json_encode([
                'type'        => $messageType,
                'business_id' => $businessId,
            ]);

            $messageSendData['staff_info_ids_str'] = $signStaffInfoId;
            $messageSendData['id']                 = $msgId;
            $messageSendData['category']           = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_TARGET');;

            $res = (new MessageCourierServer())->add_kit_message($messageSendData);

            $this->logger->write_log([
                'title'  => '驳回签字',
                'func'   => 'probationTargetResultReject',
                'opt'    => '消息更新完成',
                'params' => $messageSendData,
                'res'    => $res,
            ], 'info');

            if (!isset($res[1]) || $res[1] != 1) {
                throw new Exception('Target Message Manager Send Error');
            }

            //更新消息业务数据
            $messageManagerRes = $probationTargetRepository->saveTargetMessage([
                'target_id'          => $businessInfo['target_id'],
                'target_business_id' => $businessId,
                'msg_id'             => $msgId,
                'type'               => $messageType,
                'source_type'        => HrProbationTargetMessageModel::SOURCE_TYPE_SIGN,
                'staff_info_id'      => $signStaffInfoId,
                'sign_state'         => HrProbationTargetMessageModel::SIGN_STATE_WAIT,
                'operate_id'         => $userId,
            ]);

            if (!$messageManagerRes) {
                throw new Exception('save Target Message Manager Error! ');
            }

            //再次查询业务状态
            $businessInfoV2 = $probationTargetRepository->getTargetBusinessInfo([
                'id' => $businessId,
            ], '
                id,target_id,target_detail_id,stage,staff_state,manager_state'
            );

            $signDetailState = HrProbationTargetDetailModel::SIGN_STATE_NOT_START;
            $signState = HrProbationTargetModel::SIGN_STATE_NOT_START;

            if ($businessInfoV2['stage'] == HrProbationTargetDetailModel::STAGE_SECOND) {
                $signState = HrProbationTargetModel::SIGN_STATE_ADJUST_NOT_START;
            }

            //更新签字状态
            if ($businessInfoV2['staff_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS && $businessInfoV2['manager_state'] == HrProbationTargetBusinessModel::MANAGER_SIGN_STATE_SUCCESS) {
                $signDetailState = HrProbationTargetModel::SIGN_STATE_FINISH;

                $signState = HrProbationTargetModel::SIGN_STATE_FINISH;
                if ($businessInfoV2['stage'] == HrProbationTargetDetailModel::STAGE_SECOND) {
                    $signState = HrProbationTargetModel::SIGN_STATE_ADJUST_FINISH;
                }
            } elseif ($businessInfoV2['staff_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS || $businessInfoV2['manager_state'] == HrProbationTargetBusinessModel::MANAGER_SIGN_STATE_SUCCESS) {
                $signDetailState = HrProbationTargetModel::SIGN_STATE_WAIT;

                $signState = HrProbationTargetModel::SIGN_STATE_WAIT;
                if ($businessInfoV2['stage'] == HrProbationTargetDetailModel::STAGE_SECOND) {
                    $signState = HrProbationTargetModel::SIGN_STATE_ADJUST_WAIT;
                }
            }

            //更新detail 签字状态
            $detailData = [
                'id'         => $businessInfoV2['target_detail_id'],
                'sign_state' => $signDetailState,
            ];

            $detailRes = $probationTargetRepository->saveTargetDetail($detailData);

            $this->logger->write_log([
                'title'  => '驳回签字',
                'func'   => 'probationTargetResultReject',
                'opt'    => '更新详情表数据',
                'params' => $detailData,
                'res'    => $detailRes,
            ], 'info');

            if (!$detailRes) {
                throw new Exception('save Target Detail Error! ');
            }

            //更新target 签字状态
            $targetData = [
                'id'         => $businessInfoV2['target_id'],
                'sign_state' => $signState,
            ];

            $targetRes  = $probationTargetRepository->saveTarget($targetData);

            $this->logger->write_log([
                'title'  => '驳回签字',
                'func'   => 'probationTargetResultReject',
                'opt'    => '更新详情表数据',
                'params' => $targetData,
                'res'    => $targetRes,
            ], 'info');

            if (!$targetRes) {
                throw new Exception('save Target Error! ');
            }

            if (!$db->commit() || !$messageDb->commit()) {
                throw new Exception('save Target Commit Error! ');
            }
        } catch (ValidationException $exception) {
            $db->rollback();
            $messageDb->rollback();
            //写日志
            $this->logger->write_log([
                'title'  => '驳回签字',
                'func'   => 'probationTargetResultSubmit',
                'opt'    => '告警',
                'params' => $exception->getMessage(),
            ], 'info');

            throw $exception;
        } catch (Exception $exception) {
            $db->rollback();
            $messageDb->rollback();
            throw $exception;
        }

        return true;
    }

    /**
     * 获取lang的短字符
     * @param string $lang
     * @return string
     */
    public function getLangKey($lang = 'th'): string
    {
        $lang = strtolower(substr($lang, 0, 2));
        if (!in_array($lang, ['en', 'zh', 'th'])) {
            if (isCountry()) {
                $lang = 'th';
            } else {
                $lang = 'en';
            }
        }

        return $lang;
    }

    /**
     * 获取pdf文件路径
     * @param $businessId
     * @param $userId
     * @param $lang
     * @return mixed|string
     */
    public function getHcmPdfPath($businessId, $userId, $lang)
    {
        $param['business_id'] = $businessId;
        $param['user_id']     = $userId;
        $method               = 'get_probation_target_business_pdf_path';

        $client = (new ApiClient("hcm_rpc", '', $method, $lang));
        $client->setParams($param);
        $data = $client->execute();

        $pdfPath = '';
        if (isset($data['result']['code']) && $data['result']['code'] == 1) {
            $pdfPath = $data['result']['data']['pdf_path'] ?? '';
        }

        return $pdfPath;
    }

    /**
     * 检查员工目标签字状态
     * 该函数依次验证指定员工及其上级是否存在未完成的学习签到。根据验证结果返回对应的业务类型和详情。
     * 当员工或其上级存在未完成签到时，返回的业务类型将标记为'un_finished_study'并包含提示信息。
     * @param int $staffInfoId 员工信息记录ID，用于标识要检查的员工
     * @return array 返回包含业务状态的结构化数组：
     * - business_type: 业务类型标识
     * - training_detail: 培训详情，包含提示消息和预留URL字段
     */
    public function checkStaffManagerSign($staffInfoId): array
    {
        $returnData = [];

        $switch = (new SettingEnvServer())->getSetVal('is_check_probation_staff_manager_sign');

        if (!$switch) {
            return $returnData;
        }

        // 考勤打卡白名单
        $isInfWhite = (new WhiteListServer())->isInfWhiteList($staffInfoId, date('Y-m-d'),
            [AttendanceWhiteListModel::TYPE_PAID_LOCALLY, AttendanceWhiteListModel::TYPE_NOT_PAID_LOCALLY]);

        if ($isInfWhite) {
            return $returnData;
        }

        //员工验证
        $staffRes = $this->checkStaffNotSign($staffInfoId);

        if ($staffRes) {
            $returnData['business_type']     = 'un_remittance';
            $returnData['remittance_detail'] = [
                'dialog_msg'         => $this->getTranslation()->_('staff_manager_sign_not_out_title'),
                'dialog_status'      => 1, //弹窗
                'dialog_must_status' => 1, //是否跳过 1不能跳过
                'is_ces_tra'         => 0, //是否ces培训 0否
            ];

            return $returnData;
        }

        //上级验证
        $managerRes = $this->checkManagerNotSign($staffInfoId);

        if ($managerRes) {
            $returnData['business_type']     = 'un_remittance';
            $returnData['remittance_detail'] = [
                'dialog_msg'         => $this->getTranslation()->_('staff_manager_sign_not_out_title'),
                'dialog_status'      => 1, //弹窗
                'dialog_must_status' => 1, //是否跳过 1不能跳过
                'is_ces_tra'         => 0, //是否ces培训 0否
            ];

            return $returnData;
        }

        return $returnData;
    }

    /**
     * 查询目标是否签字
     * @param $staffInfoId
     * @return bool
     */
    public function checkStaffNotSign($staffInfoId): bool
    {
        if (!$staffInfoId) {
            return false;
        }

        //获取评估主记录
        $probationData = HrProbationModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staffInfoId,
            ],
        ]);
        $probationData = !empty($probationData) ? $probationData->toArray() : [];

        if (!empty($probationData) && $probationData['status'] == HrProbationModel::STATUS_FORMAL) {
            return false;
        }

        $probationTargetRepository = (new ProbationTargetRepository());

        //查询员工最后一次签字
        $lastSign = $probationTargetRepository->getTargetBusinessInfo([
            'staff_info_id' => $staffInfoId,
            'state'         => HrProbationTargetBusinessModel::STATE_NORMAL,
        ], 'staff_state,send_time,staff_info_id,target_id');

        if (!$lastSign) {
            return false;
        }

        if ($lastSign['staff_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS) {
            return false;
        }

        //验证目标是否存在
        $targetInfo = (new ProbationTargetRepository())->getTargetInfoByTargetId($lastSign['target_id']);

        if (empty($targetInfo)) {
            return false;
        }

        //签字时间
        $sendDate = show_time_zone($lastSign['send_time'], 'Y-m-d');

        if (time() > strtotime("{$sendDate} + 2 days")) {
            $this->logger->write_log([
                'title' => '查询目标是否签字',
                'func'  => 'checkStaffNotSign',
                'res'   => $lastSign,
            ], 'info');
            return true;
        }

        return false;
    }

    /**
     * 查询目标下级是否签字
     * @param $staffInfoId
     * @return bool
     */
    public function checkManagerNotSign($staffInfoId): bool
    {
        //查找上级
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('business.id as business_id, business.manager_id as manager_id, business.manager_state as manager_state, business.staff_sign_time as staff_sign_time,business.staff_info_id as staff_info_id');
        $builder->from(['business' => HrProbationTargetBusinessModel::class]);
        $builder->leftJoin(HrProbationModel::class, 'business.staff_info_id = probation.staff_info_id ', 'probation');
        $builder->leftJoin(HrProbationTargetModel::class, 'business.target_id = target.id', 'target');

        $builder->andWhere("business.manager_id = :manager_id:", ['manager_id' => $staffInfoId]);
        $builder->andWhere("business.manager_state IN ({manager_state:array})", [
            'manager_state' => [
                HrProbationTargetBusinessModel::MANAGER_SIGN_STATE_WAIT,
                HrProbationTargetBusinessModel::MANAGER_SIGN_STATE_REJECT,
            ],
        ]);
        $builder->andWhere("business.staff_state = :staff_state:",
            ['staff_state' => HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS]);
        $builder->andWhere("business.state = :state:", ['state' => HrProbationTargetBusinessModel::STATE_NORMAL]);
        $builder->andWhere("probation.status != :probation_status:",
            ['probation_status' => HrProbationModel::STATUS_FORMAL]);
        $builder->andWhere("target.is_deleted = :is_deleted:", ['is_deleted' => enums::IS_DELETED_NO]);
        $builder->orderby('business.staff_sign_time ASC');
        $list = $builder->getQuery()->execute()->toArray();

        if (empty($list)) {
            return false;
        }

        $businessStaffInfos = array_column($list, null, 'staff_info_id');

        //查询员工信息
        $staffList = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({staffs:array}) and state = :state:',
            'bind'       => [
                'staffs' => array_keys($businessStaffInfos),
                'state'  => HrStaffInfoModel::STATE_RESIGN,
            ],
        ])->toArray();

        $newStaffIds = array_column($staffList, 'staff_info_id');

        //去除离职员工和白名单员工
        foreach ($businessStaffInfos as $k => $v) {
            //去除离职员工
            if (in_array($v['staff_info_id'], $newStaffIds)) {
                unset($businessStaffInfos[$k]);
            }
        }

        //如果没有需要验证的，则返回false
        if (empty($businessStaffInfos)) {
            return false;
        }

        //取出第一个需要验证的
        $info = array_shift($businessStaffInfos);

        //签字时间两天限制打卡
        $signTime = show_time_zone($info['staff_sign_time'], 'Y-m-d');

        if (time() > strtotime("{$signTime} + 2 days")) {
            $this->logger->write_log([
                'title' => '查询目标下级是否签字',
                'func'  => 'checkManagerNotSign',
                'res'   => $info,
            ], 'info');
            return true;
        }

        return false;
    }

    /**
     * 获取试用期列表数据
     * @param $params
     * @return null
     */
    public function list($params)
    {
        $this->logger->write_log([
            'title'  => '试用期列表',
            'func'   => 'list',
            'opt'    => '开始',
            'params' => $params,
        ], 'info');

        $params['tab_setting_state'] = $this->processingDefault($params, 'tab_setting_state', 2, 2);
        $params['page_num']          = $this->processingDefault($params, 'page_num', 2, 1);
        $params['page_size']         = $this->processingDefault($params, 'page_size', 2, 20);

        if (empty($params['user_id']) || empty($params['tab_setting_state'])) {
            return [];
        }

        $ac = new ApiClient('hcm_rpc', '', 'probation_target_list', $this->lang);
        $ac->setParams($params);
        $acRes = $ac->execute();

        if ($acRes["result"]['code'] != 1) {
            return [];
        }

        return $acRes['result']['data'] ?? [];
    }

    /**
     * 获取试用期列表数据
     * @param $params
     * @return null
     * @throws ValidationException
     * @throws Exception
     */
    public function detail($params)
    {
        $this->logger->write_log([
            'title'  => '试用期详情',
            'func'   => 'detail',
            'opt'    => '开始',
            'params' => $params,
        ], 'info');

        if (empty($params['staff_info_id']) || empty($params['user_id'])) {
            return (object)[];
        }

        $ac = new ApiClient('hcm_rpc', '', 'probation_target_detail', $this->lang);
        $ac->setParams($params);
        $acRes = $ac->execute();

        //可抛出异常数据
        if (isset($acRes["result"]['code']) && $acRes["result"]['code'] == ErrCode::SUCCESS) {
            return $acRes['result']['data'] ?? (object)[];
        } elseif (isset($acRes["result"]['code']) && $acRes["result"]['code'] == ErrCode::VALIDATE_ERROR) {
            throw new ValidationException($acRes["result"]['error']);
        } else {
            $this->logger->write_log([
                'title'  => '试用期详情',
                'func'   => 'detail',
                'opt'    => 'error',
                'params' => $acRes,
            ], 'error');

            throw new Exception($this->t->_('no_server'));
        }
    }

    /**
     * 获取试用期保存
     * @param $params
     * @return null
     * @throws ValidationException
     * @throws Exception
     */
    public function save($params)
    {
        $this->logger->write_log([
            'title'  => '试用期保存',
            'func'   => 'save',
            'opt'    => '开始',
            'params' => $params,
        ], 'info');

        if (empty($params['user_id'])) {
            throw new ValidationException($this->t->_('login_timeout'));
        }

        $staffInfoId = $params['staff_info_id'] ?? 0;
        $staffInfo   = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId);

        if (!$staffInfoId || empty($staffInfo)) {
            throw new ValidationException($this->t->_('probation_target_empty'));
        }

        $ac = new ApiClient('hcm_rpc', '', 'probation_target_save', $this->lang);
        $ac->setParams($params);
        $acRes = $ac->execute();

        //可抛出异常数据
        if (isset($acRes["result"]['code']) && $acRes["result"]['code'] == ErrCode::SUCCESS) {
            return true;
        } elseif (isset($acRes["result"]['code']) && $acRes["result"]['code'] == ErrCode::VALIDATE_ERROR) {
            throw new ValidationException($acRes["result"]['error']);
        } else {
            $this->logger->write_log([
                'title'  => '试用期保存',
                'func'   => 'save',
                'opt'    => 'error',
                'params' => $acRes,
            ], 'error');

            throw new Exception($this->t->_('no_server'));
        }
    }

    /**
     * 获取试用期保存并发送
     * @param $params
     * @return null
     * @throws ValidationException
     * @throws Exception
     */
    public function saveSend($params)
    {
        $this->logger->write_log([
            'title'  => '试用期保存发送',
            'func'   => 'save',
            'opt'    => '开始',
            'params' => $params,
        ], 'info');

        if (empty($params['user_id'])) {
            throw new ValidationException($this->t->_('login_timeout'));
        }

        $staffInfoId = $params['staff_info_id'] ?? 0;
        $staffInfo   = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId);

        if (!$staffInfoId || empty($staffInfo)) {
            throw new ValidationException($this->t->_('probation_target_empty'));
        }

        $ac = new ApiClient('hcm_rpc', '', 'probation_target_save_send', $this->lang);
        $ac->setParams($params);
        $acRes = $ac->execute();

        //可抛出异常数据
        if (isset($acRes["result"]['code']) && $acRes["result"]['code'] == ErrCode::SUCCESS) {
            return true;
        } elseif (isset($acRes["result"]['code']) && $acRes["result"]['code'] == ErrCode::VALIDATE_ERROR) {
            throw new ValidationException($acRes["result"]['error']);
        } else {
            $this->logger->write_log([
                'title'  => '试用期保存发送',
                'func'   => 'save',
                'opt'    => 'error',
                'params' => $acRes,
            ], 'error');

            throw new Exception($this->t->_('no_server'));
        }
    }

    /**
     * 获取试用期发送
     * @param $params
     * @return null
     * @throws ValidationException
     * @throws Exception
     */
    public function send($params)
    {
        $this->logger->write_log([
            'title'  => '试用期发送',
            'func'   => 'send',
            'opt'    => '开始',
            'params' => $params,
        ], 'info');

        $staffInfoId = $params['staff_info_id'] ?? 0;
        $userId      = $params['user_id'] ?? 0;

        if (empty($userId)) {
            throw new ValidationException($this->t->_('login_timeout'));
        }

        $staffInfo = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId);
        if (!$staffInfoId || empty($staffInfo)) {
            throw new ValidationException($this->t->_('probation_target_empty'));
        }

        $ac = new ApiClient('hcm_rpc', '', 'probation_target_send', $this->lang);
        $ac->setParams([
            'staff_info_id' => $staffInfoId,
            'user_id'       => $userId,
        ]);
        $acRes = $ac->execute();

        //可抛出异常数据
        if (isset($acRes["result"]['code']) && $acRes["result"]['code'] == ErrCode::SUCCESS) {
            return true;
        } elseif (isset($acRes["result"]['code']) && $acRes["result"]['code'] == ErrCode::VALIDATE_ERROR) {
            throw new ValidationException($acRes["result"]['error']);
        } else {
            $this->logger->write_log([
                'title'  => '试用期发送',
                'func'   => 'send',
                'opt'    => 'error',
                'params' => $acRes,
            ], 'error');

            throw new Exception($this->t->_('no_server'));
        }
    }

    /**
     * 获取试用期取消
     * @param $params
     * @return null
     * @throws ValidationException
     * @throws Exception
     */
    public function cancel($params)
    {
        $this->logger->write_log([
            'title'  => '试用期取消',
            'func'   => 'cancel',
            'opt'    => '开始',
            'params' => $params,
        ], 'info');

        $staffInfoId = $params['staff_info_id'] ?? 0;
        $userId      = $params['user_id'] ?? 0;

        if (empty($userId)) {
            throw new ValidationException($this->t->_('login_timeout'));
        }

        $staffInfo = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId);
        if (!$staffInfoId || empty($staffInfo)) {
            throw new ValidationException($this->t->_('probation_target_empty'));
        }

        $ac = new ApiClient('hcm_rpc', '', 'probation_target_cancel', $this->lang);
        $ac->setParams([
            'staff_info_id' => $staffInfoId,
            'user_id'       => $userId,
        ]);

        $acRes = $ac->execute();

        //可抛出异常数据
        if (isset($acRes["result"]['code']) && $acRes["result"]['code'] == ErrCode::SUCCESS) {
            return true;
        } elseif (isset($acRes["result"]['code']) && $acRes["result"]['code'] == ErrCode::VALIDATE_ERROR) {
            throw new ValidationException($acRes["result"]['error']);
        } else {
            $this->logger->write_log([
                'title'  => '试用期取消',
                'func'   => 'send',
                'opt'    => 'cancel',
                'params' => $acRes,
            ], 'error');

            throw new Exception($this->t->_('no_server'));
        }
    }

    /**
     * 获取试用期目标组合后的地址
     * @param $params
     * @return string[]
     * @throws ValidationException
     */
    public function viewSignUrl($params): array
    {
        $staffInfoId = $params['staff_info_id'] ?? 0;
        $staffInfo   = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId);
        if (!$staffInfoId || empty($staffInfo)) {
            throw new ValidationException($this->t->_('probation_target_empty'));
        }

        //查询最新的签字信息
        $businessInfo = (new ProbationTargetRepository())->getTargetBusinessInfo([
            'staff_info_id' => $staffInfoId,
            'state'         => HrProbationTargetBusinessModel::STATE_NORMAL,
        ], 'id,target_id,stage,staff_info_id');

        if (empty($businessInfo)) {
            throw new ValidationException($this->t->_('probation_business_empty_not_view'));
        }

        $url = env('h5_endpoint').sprintf("regular-assess-message?type=%s&business_id=%s&msg_id=",
                HrProbationTargetMessageModel::TYPE_MANAGER, $businessInfo['id']);
        return ['sign_url' => $url];
    }

    /**
     * 查询试用期目标菜单权限
     * @param $params
     * @return bool
     */
    public function getMenuPermission($params): bool
    {
        if (empty($params['staff_id'])) {
            return false;
        }

        //只要员工的下级有权限就展示true
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as count');
        $builder->from(['staff' => HrStaffInfoModel::class]);
        $builder->join(HrProbationTargetModel::class, 'staff.staff_info_id = target.staff_info_id ', 'target');
        $builder->andWhere("staff.manger = :manager_id:", ['manager_id' => $params['staff_id']]);
        $builder->andWhere("target.is_deleted = :is_deleted:", ['is_deleted' => enums::IS_DELETED_NO]);

        $info = $builder->getQuery()->getSingleResult();

        $count = $info ? $info->count : 0;

        $this->logger->write_log([
            'title'  => '试用期目标权限',
            'func'   => 'getMenuPermission',
            'opt'    => '返回',
            'params' => $params,
            'res'    => $count,
        ], 'info');

        return $count > 0;
    }

    /**
     * 试用期目标红点接口
     * @param $params
     * @return array
     */
    public function redCount($params): array
    {
        $returnData = [
            'setting_start_count' => 0,
        ];

        $managerId = $params['user_id'] ?? 0;

        if (empty($managerId)) {
            return $returnData;
        }

        $returnData['setting_start_count'] = $this->getSettingStartCount($managerId);

        return $returnData;
    }

    /**
     * 获取待指定目标的红点数量
     * @param $managerId
     * @return int
     */
    public function getSettingStartCount($managerId): int
    {
        //只要员工的下级有权限就展示true
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as count');
        $builder->from(['staff' => HrStaffInfoModel::class]);
        $builder->join(HrProbationTargetModel::class, 'staff.staff_info_id = target.staff_info_id ', 'target');
        $builder->andWhere("staff.manger = :manager_id:", ['manager_id' => $managerId]);
        $builder->andWhere("staff.state != :staff_state:", ['staff_state' => HrStaffInfoModel::STATE_RESIGN]);
        $builder->andWhere("target.setting_state = :setting_state:",
            ['setting_state' => HrProbationTargetModel::SETTING_STATE_NOT_START]);
        $builder->andWhere("target.is_deleted = :is_deleted:", ['is_deleted' => enums::IS_DELETED_NO]);
        $builder->andWhere("target.is_hidden = :is_hidden:", ['is_hidden' => 0]);

        $info = $builder->getQuery()->getSingleResult();

        return $info ? intval($info->count) : 0;
    }
}