<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/5/8
 * Time: 下午3:25
 */

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Enums\AttendanceCalendarEnums;
use FlashExpress\bi\App\Enums\AttendanceEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftHistoryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftModel;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BusinesstripRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\HrShiftServer;

/**
 * 考勤日历专属 勿写其他业务逻辑
 */
class AttendanceCalendarServer extends BaseServer
{

    protected $shiftInfo = ''; //员工班次信息
    protected $staffId = 0;
    protected $staffInfo = [];
    protected $staffAData = []; //员工考勤记录
    protected $monthDay = []; //当月日历
    protected $tripDate = []; //出差外出记录
    protected $holidays = [];//假日记录
    protected $dateLeave = [];//请假记录
    protected $staffOffDate = [];//休息日数据  六天班员工按照 hr_staff_work_days ，五天班员工按照每天固化的N天班员工以及 当天是 周五周六 周六周日 来进行是否是休息日判断
    protected $transferWWD = []; //固化每天员工的 weeK_working_day
    protected $markData = []; //标记 缺卡 迟到 早退
    protected $lateData = []; //标记 缺卡 迟到 早退
    protected $makeUpCard = []; //补卡记录
    protected $staffAuditReissueForBusiness = []; //出差打卡&外出申请

    protected $settingEnv = []; //配置项
    protected $attendanceServer = null;

    protected $current_job_title = 0;//员工职位
    protected $workHomeDate = [];//居家办公日期

    //主播职位id
    protected $liveJobId;
    //支援信息 一个月的
    protected $supportData;
    //主账号信息
    protected $masterInfo;

    /**
     * 初始化数据
     * @param $staff_id
     * @param $yearMonth
     * @return $this
     */
    public function init($staff_id, $yearMonth): AttendanceCalendarServer
    {
        $this->staffId = $staff_id;
        $this->staffInfo = (new StaffRepository())->getStaffPosition($staff_id);
        $this->masterInfo = $this->getMasterStaffInfo();
        $this->monthDay = get_month_days($yearMonth);
        $this->workHomeDate = $this->getWhDate();
        $start_date = current($this->monthDay);
        $end_date = end($this->monthDay);
        $this->transferWWD = $this->getStaffTransferData($staff_id,$start_date,date('Y-m-d', time() - 24 * 3600));
        $shiftServer = new HrShiftServer();
        $shiftServer->transferData = $this->transferWWD;
        $this->shiftInfo = $shiftServer->getShiftInfos($staff_id,$this->monthDay);
        $this->staffAData = $this->getAttendanceMess($staff_id, $start_date, $end_date);
        $this->tripDate = (new BusinesstripRepository($this->lang, $this->timeZone))->trip_dates($staff_id, $start_date, $end_date); //出差&外出信息
        $this->makeUpCard = $this->getMakeUpCardData($staff_id, $start_date, $end_date);

        $this->holidays = $this->getHolidayData($start_date, $end_date);
        //如果当前账号是子账号 要去查询子账号的支援信息   主账号的请假和休息日数据
        $this->supportData = $this->getSupportData($start_date, $end_date);
        $this->dealSupportData();//把支援信息 放到每一天
        $this->staffOffDate = $this->dealWithOffDay($start_date, $end_date);
        $this->staffAuditReissueForBusiness = $this->staff_audit_reissue_for_business($staff_id, $start_date, $end_date);
        $this->dateLeave = $this->getLeaveDate($this->masterInfo['staff_info_id'], $start_date, $end_date);
        //考勤server
        $this->attendanceServer = new AttendanceServer($this->lang,$this->timeZone);
        $this->attendanceServer->initFreeShiftConfig();
        $this->liveJobId = (new SettingEnvServer())->getSetVal('free_shift_position',',');
        return $this;
    }

    /**
     *  考勤日历
     * @return array
     */
    public function attendanceCalendar($params = []): array
    {

        $returnData = [];
        $today = date('Y-m-d');
        foreach ($this->monthDay as $k => $date) {
            $returnData[$k]['date'] = $date;
            $returnData[$k]['type'] = AttendanceCalendarEnums::TYPE_NORMAL;
            $returnData[$k]['started_at'] = ''; //上班打卡时间
            $returnData[$k]['end_at'] = ''; //下班打卡时间
            $returnData[$k]['start_make_up_card_state'] = AttendanceCalendarEnums::MAKE_UP_CARD_NO;//补卡状态
            $returnData[$k]['end_make_up_card_state'] = AttendanceCalendarEnums::MAKE_UP_CARD_NO;
            $returnData[$k]['now_date'] = $date == date('Y-m-d') ? 1 : 0; //是否是当天
            $returnData[$k]['ob_trip_start'] = ""; //出差打卡申请未审批提示
            $returnData[$k]['ob_trip_end'] = "";
            $returnData[$k]['business_trip_type'] = 0; //出差类型
            $returnData[$k]['mark'] = (object)[]; //标记  缺卡 迟到 早退
            $returnData[$k]['vacation_type'] = $this->dateLeave[$date]['type'] ?? AttendanceCalendarEnums::LEAVE_NO_TYPE; //假期类型
            $returnData[$k]['holiday'] = '';//是否ph
            $returnData[$k]['support_info'] = $this->supportData[$date] ?? null;//这天是否支援

            if (!empty($this->transferWWD[$date])) {
                $this->current_job_title = $this->transferWWD[$date]['job_title'];
            } else {
                $this->current_job_title = $this->staffInfo['job_title'];
            }

            if (isset($this->staffInfo['hire_date']) &&  (strtotime($date) < strtotime(date('Y-m-d', strtotime($this->staffInfo['hire_date']))))) {
                $returnData[$k]['type'] = AttendanceCalendarEnums::TYPE_BLANK;
                continue;
            }
            //[1] 未来的日期
            if ($date > $today) {
                $returnData[$k]['type'] = $this->getFutureACType($date);
            }
            //[2] 当天
            if ($date == $today) {
                $returnData[$k]['type'] = $this->getCurrentACType($date);
            }
            //[3] 过去的日期
            if ($date < $today) {
                $returnData[$k]['type'] = $this->getPastACType($date);
            }
            //[4]设置日历下方的展示信息
            $returnData[$k]['display'] = $this->setUpDisplay($date, $returnData[$k]['type']);
            //[5] 设置出差&外出打卡审批展示
            [$returnData[$k]['business_trip_type'], $returnData[$k]['ob_trip_start'], $returnData[$k]['ob_trip_end']] = $this->businessTripAndGoOutDisplay($date);
            //[6] 设置基础考勤数据
            [$returnData[$k]['started_at'], $returnData[$k]['end_at']] = $this->attendanceDate($date);
            //[7] 获取标记的 缺卡迟到 早退
            $returnData[$k]['mark'] = $this->markData[$date] ?? (object)[];
            $returnData[$k]['late'] = $this->lateData[$date] ?? "";

            //新需求 节假日不放到类型 从类型中排除 放到mark 里
            if (isset($this->holidays[$date])) {
                $returnData[$k]['holiday'] = $this->dealWithHoliday($this->holidays[$date],$date);
                [$returnData[$k]['type'], $returnData[$k]['mark']] = $this->dealPhColor($returnData[$k]['type'],$returnData[$k]['mark'],$date);
            }
            if(in_array($date,$this->workHomeDate)){
                $returnData[$k]['holiday'] = 'WH';
            }

            //[8] 设置补卡状态
            [$returnData[$k]['start_make_up_card_state'], $returnData[$k]['end_make_up_card_state']] = $this->judgeDateMakeUpCard($date, $returnData[$k]['type']);
            //新需求 班次 取每天打卡的数据固化的 如果 当天没打卡 取 hr_staff_shift_history
            $returnData[$k]['shift'] = $this->makeShiftInfo($this->staffId,$this->current_job_title, $date, $this->staffAData[$date]['start_data']??null, $this->shiftInfo[$date]??[],$params['shift_prefix_filter'] ?? false);

        }

        $returnALLData['organization_type'] = $this->getOrganizationType();

        $returnALLData['staff_id'] = $this->staffId;
        $returnALLData['hire_type'] = $this->staffInfo['hire_type'];
        $returnALLData['is_live_master'] =  in_array($this->staffInfo['job_title'], $this->liveJobId);
        $returnALLData['dataList'] = $returnData;
        $returnALLData['penalty_permission'] = $this->getPenaltyPermission();
        return $returnALLData;
    }

    public function getOrganizationType()
    {
        $info = $this->getDI()->get('db_fle')->query("select organization_type from  staff_info where  id= :staff_info_id", ['staff_info_id' => $this->staffId])->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info['organization_type'] ?? '0';
    }

    /**
     * 获取基础考勤数据
     * @param $date
     * @return string[]
     */
    public function attendanceDate($date): array
    {
        $started_at = empty($this->staffAData[$date]['started_at']) ? '' : date('H:i', strtotime($this->staffAData[$date]['started_at']));
        $end_at = empty($this->staffAData[$date]['end_at']) ? '' : date('H:i', strtotime($this->staffAData[$date]['end_at']));
        return [$started_at, $end_at];
    }

    /**
     * 出差&外出打卡审批展示
     * @param $date
     * @return array
     */
    public function businessTripAndGoOutDisplay($date): array
    {
        $staff_audit_reissue_map = $this->staffAuditReissueForBusiness;
        if (isset($staff_audit_reissue_map, $staff_audit_reissue_map[$date])) {
            $trip_start_time = empty($staff_audit_reissue_map[$date]['started_at']) ? 'N/A' : date('H:i:s', strtotime($staff_audit_reissue_map[$date]['started_at']));
            $trip_end_time = empty($staff_audit_reissue_map[$date]['end_at']) ? 'N/A' : date('H:i:s', strtotime($staff_audit_reissue_map[$date]['end_at']));

            if (isset($staff_audit_reissue_map[$date]['business_trip_type'])) {
                return [$staff_audit_reissue_map[$date]['business_trip_type'], $trip_start_time, $trip_end_time];
            }
        }
        return [0, '', ''];
    }

    /**
     * 设置展示
     * 需要展示的类型有  休息日 法定假日 请假  出差 外出
     * @param $date
     * @param $type
     * @return string
     */
    public function setUpDisplay($date, $type): string
    {

        if(!empty($this->staffInfo['hire_type']) && $this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID){
            if (in_array($type, [
                AttendanceCalendarEnums::TYPE_HOLIDAY,
                AttendanceCalendarEnums::TYPE_OFF_DAY,
            ])) {
                return '';
            }
        }
        if (in_array($type, [
            AttendanceCalendarEnums::TYPE_HOLIDAY,
            AttendanceCalendarEnums::TYPE_REGULAR_HOLIDAY,
            AttendanceCalendarEnums::TYPE_SPECIAL_HOLIDAY,
            AttendanceCalendarEnums::TYPE_OFF_DAY,
            AttendanceCalendarEnums::TYPE_REST_DAY
        ])) {
            return $this->getTranslation()->_('attendance_calendar_type_' . $type) . ' ' . $this->getTranslation()->_('attendance_calendar_ot');
        }
        if (in_array($type, [
            AttendanceCalendarEnums::TYPE_BUSINESS_TRIP,
            AttendanceCalendarEnums::TYPE_GO_OUT,
        ])) {
            return $this->getTranslation()->_('attendance_calendar_type_' . $type);
        }
        if ($type == AttendanceCalendarEnums::TYPE_ASK_FOR_LEAVE && isset($this->dateLeave[$date])) {
            $leave_type = '';
            foreach (array_unique((array)$this->dateLeave[$date]['leave_type']) as $item) {
                if (isset(AuditRepository::$leave_type[$item])) {
                    $leave_type .= $this->getTranslation()->_(AuditRepository::$leave_type[$item]) . '/';
                }
            }

            $typeKey = 'attendance_calendar_type_' . $type;
            if($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID){
                $typeKey = 'attendance_calendar_type_unpaid_leave';
            }
            $leave_type = trim($leave_type, '/');
            return sprintf('%s %s-%s',
                $this->getTranslation()->_($typeKey),
                $this->dateLeave[$date]['type'] == AttendanceCalendarEnums::LEAVE_WHOLE_TYPE ? $this->getTranslation()->_('attendance_calendar_day_whole') : $this->getTranslation()->_('attendance_calendar_day_half'),
                $leave_type
            );
        }
        return '';
    }

    /**
     * 获取考勤信息
     * @param $staffId
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function getAttendanceMess($staffId, $start_date, $end_date): array
    {
        $selectSql = "select 
                            started_state,
                            end_state,
                            staff_info_id,
                            attendance_date,
                            shift_start,
                            shift_end,
                            shift_id,
                            started_at AS start_data,
                            CONVERT_TZ( started_at, '+00:00', :time_zone ) AS started_at,
                            CONVERT_TZ( end_at, '+00:00', :time_zone ) AS end_at  
                            ,TIMESTAMPDIFF(hour ,started_at,end_at) sub_time 
                       from staff_work_attendance where staff_info_id= :staff_info_id
                       AND attendance_date >= :start_time
                       AND attendance_date <= :end_time";
        $time_zone =  $this->config->application->timeZone;
        $data = $this->getDI()->get('db_rby')->query($selectSql, ['time_zone' => $time_zone, 'staff_info_id' => $staffId, 'start_time' => $start_date, 'end_time' => $end_date]);
        $staffAData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return array_column($staffAData, NULL, 'attendance_date');
    }

    /**
     * 员工班次轮休（休息日）所有员工都进轮休表
     * @param $staffId
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function getStaffOffDate($staffId, $start_date, $end_date): array
    {
        $work_days_sql = "select date_at,type,staff_info_id from hr_staff_work_days where date_at >= :start_time and date_at <= :end_time and staff_info_id = :staff_info_id";
        $work_days_data = $this->getDI()->get('db_rby')->query($work_days_sql, ['staff_info_id' => $staffId, 'start_time' => $start_date, 'end_time' => $end_date]);
        $data = $work_days_data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return array_column($data, 'type','date_at');
    }

    /**
     * 获取员工假期 各个国家需重写此方法
     * @return array
     */
    public function getHolidayData($startDate='',$endDate=''): array
    {
        $holiday_data = [];
        $staffInfo = $this->staffInfo;
        if(!empty($staffInfo['hire_type']) && $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID){
            return $holiday_data;
        }
        $leaveServer =  (new LeaveServer($this->lang,$this->timeZone));
        $week_working_days = $this->transferWWD;
        $dateRange = DateHelper::DateRange(strtotime($startDate), strtotime($endDate));
        foreach ($dateRange as $day) {
            if(isset($week_working_days[$day])){
                $staffInfo['staff_info_id'] = $week_working_days[$day]['staff_info_id'];
                $staffInfo['week_working_day'] = $week_working_days[$day]['week_working_day'];
                $staffInfo['job_id'] = $week_working_days[$day]['job_title'];
                $staffInfo['node_department_id'] = $week_working_days[$day]['node_department_id'];
            }else{
                $staffInfo = $this->staffInfo;
            }
            $holiday_data[$day] = in_array ($day ,array_column($leaveServer->ph_days($staffInfo),'day')) ? true : null;//FromCache
        }
        return $holiday_data;
    }

    /**
     * 获取 时间区间的请假和类型
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @return mixed
     */
    public function getLeaveDate($staff_id, $start_date, $end_date)
    {
        $sql = "select `type`, s.date_at,a.leave_type
                from staff_audit_leave_split s
                join staff_audit a on s.staff_info_id = a.staff_info_id and  a.audit_id = s.audit_id        
                where a.staff_info_id = :staff_info_id
                and a.audit_type = 2 
                and a.status = 2 
                and a.parent_id = 0
                and s.date_at between :start_date and :end_date";
        $data = $this->getDI()->get('db_rby')->query($sql, ['staff_info_id' => $staff_id, 'start_date' => $start_date, 'end_date' => $end_date])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $returnData = [];
        foreach ($data as $item) {
            if (enums::LEAVE_TYPE_15 == $item['leave_type']) {
                continue;
            }
            if(!isset($returnData[$item['date_at']]['type'])){
                $returnData[$item['date_at']]['type'] = ($item['type'] == 0 ? AttendanceCalendarEnums::LEAVE_WHOLE_TYPE : $item['type']);
            }else{
                $returnData[$item['date_at']]['type'] += ($item['type'] == 0 ? AttendanceCalendarEnums::LEAVE_WHOLE_TYPE : $item['type']);
            }

            $returnData[$item['date_at']]['leave_type'][] = $item['leave_type'];
        }
        return $returnData;
    }

    /**
     * 出差打卡信息
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @param int $status 1 待审核 2 审核通过 3 驳回 4撤销 5 超时关闭
     * @return mixed
     */
    public function staff_audit_reissue_for_business($staff_id, $start_date, $end_date, array $status = [1])
    {
        $data = StaffAuditReissueForBusinessModel::find(
            [
                'columns' => "attendance_date,start_time AS started_at,end_time AS end_at,business_trip_type,start_time_zone,end_time_zone",
                'conditions' => 'staff_info_id = :staff_info_id: AND attendance_date >= :start_date: AND  attendance_date <= :end_date:  AND status in ({status:array}) ',
                'bind' => ['staff_info_id' => $staff_id, 'start_date' => $start_date, 'end_date' => $end_date, 'status' =>$status]
            ]
        )->toArray();
        $AttendanceBusinessServer = new AttendanceBusinessServer($this->lang, $this->timeZone);
        $add_hour = $this->config->application->add_hour;

        foreach ($data as &$v) {
            //计算出差打卡需要的时区偏移量  秒
            [$start_time_zone_s, $end_time_zone_s] = $AttendanceBusinessServer->getCalculateTimeZoneSecond($v);
            $v['started_at'] = empty($v['started_at']) ? $v['started_at'] : date('Y-m-d H:i:s',
                strtotime($v['started_at']) + $start_time_zone_s + ($add_hour * 3600));
            $v['end_at']     = empty($v['end_at']) ? $v['end_at'] : date('Y-m-d H:i:s',
                strtotime($v['end_at']) + $end_time_zone_s + ($add_hour * 3600));
        }
        return empty($data) ? [] : array_column($data, null, 'attendance_date');
    }

    /**
     * 获取未来日期的考勤类型 假日>休息日>请假>出差
     * @param $date
     * @return int
     */
    public function getFutureACType($date): int
    {

        if (isset($this->staffOffDate[$date])) {
            return $this->staffOffDate[$date];
        }
        if (isset($this->dateLeave[$date])) {
            return AttendanceCalendarEnums::TYPE_ASK_FOR_LEAVE;
        }
        if (isset($this->tripDate[$date])) {
            return $this->tripDate[$date]['business_trip_type'] == BusinessTripModel::BTY_GO_OUT ? AttendanceCalendarEnums::TYPE_GO_OUT : AttendanceCalendarEnums::TYPE_BUSINESS_TRIP;
        }
        return AttendanceCalendarEnums::TYPE_BLANK;
    }

    /**
     * 获取当天的考勤类型  休息日>请假>打卡
     * @param $date
     * @return int
     */
    public function getCurrentACType($date): int
    {
        if (isset($this->staffOffDate[$date])) {
            return $this->staffOffDate[$date];
        }
        if (isset($this->dateLeave[$date])) {
            //半天请假的时候才计算 迟到早退
            if ($this->dateLeave[$date]['type'] != AttendanceCalendarEnums::LEAVE_WHOLE_TYPE) {
                $this->dealWithLateAndLeaveEarly($date ,5 * 3600 ,$this->dateLeave[$date]['type']);
            }
            return AttendanceCalendarEnums::TYPE_ASK_FOR_LEAVE;
        }
        $this->dealWithLateAndLeaveEarly($date);
        return AttendanceCalendarEnums::TYPE_NORMAL;
    }

    /**
     * 获取过去的考勤类型 假日>休息日>请假>出差>正常打卡
     * @param $date
     * @return int
     */
    public function getPastACType($date): int
    {

        if (isset($this->staffOffDate[$date])) {
            return $this->staffOffDate[$date];
        }
        if (isset($this->dateLeave[$date])) {
            return $this->getPastAskForLeaveType($date);
        }
        if (isset($this->tripDate[$date])) {
            return $this->getPastBusinessTripType($date);
        }
        $this->dealWithMissingCard($date);
        $this->dealWithLateAndLeaveEarly($date);
        return AttendanceCalendarEnums::TYPE_NORMAL;
    }

    /**
     * 获取休息日
     */
    public function dealWithOffDay($startDate, $endDate): array
    {
        $offDate = $this->getStaffOffDate($this->masterInfo['staff_info_id'], $startDate, $endDate);
        $offDay = [];
        $dateRange = DateHelper::DateRange(strtotime($startDate), strtotime($endDate));
        foreach ($dateRange as $date) {
            if (array_key_exists($date,$offDate)) {
                $offDay[$date] = $offDate[$date] == HrStaffWorkDayModel::TYPE_REST ? AttendanceCalendarEnums::TYPE_REST_DAY : AttendanceCalendarEnums::TYPE_OFF_DAY;
            }
        }
        return $offDay;
    }

    /**
     * ph 底色去掉 改成文字展示
     * 获取法定节假日类型
     * @param $holiday
     * @param $date
     * @return null
     */
    public function dealWithHoliday($holiday, $date)
    {
        return 'PH';
    }

    //前端是根据 type  和mark 处理颜色的
    public function dealPhColor($type, $mark, $date){
        //如果是 off 或者 出差 不变
        if (in_array($type, [
            AttendanceCalendarEnums::TYPE_OFF_DAY,
            AttendanceCalendarEnums::TYPE_REST_DAY,
        ])) {
            return [$type, $mark];
        }

        if (in_array($type, [
            AttendanceCalendarEnums::TYPE_ASK_FOR_LEAVE,
            AttendanceCalendarEnums::TYPE_BUSINESS_TRIP,
            AttendanceCalendarEnums::TYPE_GO_OUT,
        ])) {
            //有异常 显示白色
            if($mark != (object)[]){
                return [$type,''];
            }
            return [$type, $mark];
        }

        //如果 ph  有迟到早退（不算） 显示白色
        if($type == AttendanceCalendarEnums::TYPE_NORMAL && $mark != (object)[]){
            return [AttendanceCalendarEnums::TYPE_NORMAL,''];
        }
        return [$type,$mark];

    }

    /**
     * 标记 缺卡 迟到 早退
     * 四种组合 (上班下班) （缺卡 迟到 早退）
     * @param $date
     * @param $dayType
     * @param $type
     */
    public function markMissingCardAndLateAndLeaveEarly($date, $dayType, $type)
    {
        if (in_array($dayType, [AttendanceCalendarEnums::GO_WORK, AttendanceCalendarEnums::GET_OFF_WORK])
            && in_array($type, [AttendanceCalendarEnums::MISSING_CARD, AttendanceCalendarEnums::BE_LATE, AttendanceCalendarEnums::LEAVE_EARLY])) {
            $this->markData[$date][$dayType . '_' . $type] = true;
        }
    }

    /**
     * 判断 外出&出差
     * @param $date
     * @return int
     */
    public function getPastBusinessTripType($date): int
    {
        $this->dealWithMissingCard($date);
        $this->dealWithLateAndLeaveEarly($date);
        return $this->tripDate[$date]['business_trip_type'] == BusinessTripModel::BTY_GO_OUT ? AttendanceCalendarEnums::TYPE_GO_OUT : AttendanceCalendarEnums::TYPE_BUSINESS_TRIP;
    }

    /**
     * 判断 请假整天 请假半天时的迟到早退缺卡
     * 过去的时间才使用这个方法！
     * @param $date
     * @return int
     */
    public function getPastAskForLeaveType($date): int
    {
        if (in_array($this->dateLeave[$date]['type'], [AttendanceCalendarEnums::LEAVE_UP_TYPE, AttendanceCalendarEnums::LEAVE_DOWN_TYPE])) {
            $this->dealWithMissingCard($date);
            $this->dealWithLateAndLeaveEarly($date, 5 * 3600 ,$this->dateLeave[$date]['type']);
        }
        return AttendanceCalendarEnums::TYPE_ASK_FOR_LEAVE;
    }

    /**
     * 标记缺卡
     * @param $date
     */
    public function dealWithMissingCard($date)
    {
        if (empty($this->staffAData[$date]['started_at'])) {
            $this->markMissingCardAndLateAndLeaveEarly($date, AttendanceCalendarEnums::GO_WORK, AttendanceCalendarEnums::MISSING_CARD);
        }
        if (empty($this->staffAData[$date]['end_at'])) {
            $this->markMissingCardAndLateAndLeaveEarly($date, AttendanceCalendarEnums::GET_OFF_WORK, AttendanceCalendarEnums::MISSING_CARD);
        }
    }

    /**
     * 标记迟到早退
     * @param $date
     * @param $seconds
     * @param int|null $leaveType 存在半天请假的情况下,开始&结束班次需要加减相应时长在判断迟到早退
     * @return bool
     */
    public function dealWithLateAndLeaveEarly($date, $seconds = 0, $leaveType = null)
    {
        //主播 因 实际工作时长 小于 应工作时长 标记 早退
        if (in_array($this->current_job_title, $this->liveJobId)) {
            $this->setLiveLeaveEarly($date, $leaveType);
            return true;
        }

        if (!empty($this->staffAData[$date]['started_at']) && !empty($this->staffAData[$date]['shift_start'])) {
            //不考虑宽限分钟数，精确到分钟
            $start_at = date('Y-m-d H:i', strtotime($this->staffAData[$date]['started_at']));

            if(strtotime($start_at) > strtotime($date . ' ' . $this->staffAData[$date]['shift_start']) + ($leaveType == AttendanceCalendarEnums::LEAVE_UP_TYPE ? $seconds : 0)){
                $this->markMissingCardAndLateAndLeaveEarly($date, AttendanceCalendarEnums::GO_WORK, AttendanceCalendarEnums::BE_LATE);
                if (isset($this->holidays[$date])) {
                    $this->lateData[$date] = 0;
                }else{
                    $late = (int)((abs(strtotime($start_at) - strtotime($date . ' ' . $this->staffAData[$date]['shift_start'])-($leaveType == AttendanceCalendarEnums::LEAVE_UP_TYPE ? $seconds : 0))) / 60);
                    $this->lateData[$date] = $late;
                }
            }

        }
        if (!empty($this->staffAData[$date]['end_at']) && !empty($this->staffAData[$date]['shift_end']) && !empty($this->staffAData[$date]['shift_start'])) {
            $shift_end = strtotime($date . ' ' . $this->staffAData[$date]['shift_end']);
            //处理跨天班次
            if (strtotime($date . ' ' . $this->staffAData[$date]['shift_start']) > strtotime($date . ' ' . $this->staffAData[$date]['shift_end'])) {
                $shift_end = strtotime($date . ' ' . $this->staffAData[$date]['shift_end']) + 24 * 3600;
            }

            //不考虑宽限分钟数，精确到分钟
            $end_at = date('Y-m-d H:i', strtotime($this->staffAData[$date]['end_at']));

            if (strtotime($end_at) < ($shift_end - ($leaveType == AttendanceCalendarEnums::LEAVE_DOWN_TYPE ? $seconds : 0))) {
                $this->markMissingCardAndLateAndLeaveEarly($date, AttendanceCalendarEnums::GET_OFF_WORK, AttendanceCalendarEnums::LEAVE_EARLY);
            }
        }
    }

    /**
     * 标记 主播因 实际工作时长 小于 应工作时长 标记 早退
     *
     * 仅主播职位 使用
     *
     * @param $date
     * @param $leaveType
     * @return bool
     */
    public function setLiveLeaveEarly($date, $leaveType)
    {
        if(empty($this->staffAData[$date]['started_at']) || empty($this->staffAData[$date]['end_at'])) {
            return false;
        }
        $hours = $this->attendanceServer->getLiveHours($date, $this->staffAData[$date]['started_at'], $leaveType);

        //不考虑宽限分钟数，精确到分钟
        $start_at = date('Y-m-d H:i', strtotime($this->staffAData[$date]['started_at']));
        $end_at = date('Y-m-d H:i', strtotime($this->staffAData[$date]['end_at']));

        //实际工作时长 大于 等于 应工作时长 则 不标记
        $min = floor((strtotime($end_at) - strtotime($start_at)) / 60);
        if($min >= $hours * 60) {//分钟
            return false;
        }

        $this->markMissingCardAndLateAndLeaveEarly($date, AttendanceCalendarEnums::GET_OFF_WORK, AttendanceCalendarEnums::LEAVE_EARLY);
        return true;
    }


    /**
     * 展示班次信息
     * @param $staff_id
     * @param $job_title
     * @param $date_at
     * @param $attendance_started_at
     * @param $shiftInfo
     * @return string
     */
    protected function makeShiftInfo($staff_id, $job_title, $date_at, $attendance_started_at, $shiftInfo,$shift_prefix_filter = false): string
    {
        [$resultShiftStr,$leaveTips] = $this->attendanceServer->makeLeaveTipsAndShiftInfo($this->masterInfo['staff_info_id'], $job_title,$this->staffInfo['hire_type'], $date_at,
            $attendance_started_at, $shiftInfo['start'] ?? '', $shiftInfo['end'] ?? '');

        $shift = '';
        if ($date_at == date('Y-m-d') &&  !in_array($this->staffInfo['job_title'], $this->liveJobId)) {
            switch ($shiftInfo['shift_type']) {
                case "EARLY":
                    $shift = $this->getTranslation()->_('shift_early');
                    break;
                case "MIDDLE":
                    $shift = $this->getTranslation()->_('shift_middle');
                    break;
                case "NIGHT":
                    $shift = $this->getTranslation()->_('shift_night');
                    break;
            }
            $shift .= ' ';
        }
        if ($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID || $shift_prefix_filter) {
            $shift = '';
        }
        return $shift . $resultShiftStr;
    }

    /**
     * 获取指定日期操作补卡信息
     * @param $date
     * @param $type
     * @return array
     */
    public function judgeDateMakeUpCard($date, $type): array
    {
        $start_attendance = AttendanceCalendarEnums::MAKE_UP_CARD_NO;
        $end_attendance = AttendanceCalendarEnums::MAKE_UP_CARD_NO;

        if (strtotime($date) > (time() - 3600 * 72) && strtotime($date) < strtotime(gmdate('Y-m-d'))
            && !in_array($type, [
                AttendanceCalendarEnums::TYPE_HOLIDAY,
                AttendanceCalendarEnums::TYPE_OFF_DAY,
                AttendanceCalendarEnums::TYPE_REST_DAY,
                AttendanceCalendarEnums::TYPE_REST_DAY,
                AttendanceCalendarEnums::TYPE_REGULAR_HOLIDAY,
                AttendanceCalendarEnums::TYPE_SPECIAL_HOLIDAY
            ])) {
            if (empty($this->staffAData[$date]['started_at'])) {
                $start_attendance = AttendanceCalendarEnums::MAKE_UP_CARD_ALLOW;
            }
            if (empty($this->staffAData[$date]['end_at'])) {
                $end_attendance = AttendanceCalendarEnums::MAKE_UP_CARD_ALLOW;
            }
        }
        //存在补卡记录
        if (isset($this->makeUpCard[$date . '-' . AttendanceCalendarEnums::GO_WORK_MAKE_UP_CARD])) {
            $start_attendance = AttendanceCalendarEnums::MAKE_UP_CARD_EXISTED;
        }
        if (isset($this->makeUpCard[$date . '-' . AttendanceCalendarEnums::GET_OFF_WORK_MAKE_UP_CARD])) {
            $end_attendance = AttendanceCalendarEnums::MAKE_UP_CARD_EXISTED;
        }
        //当前打卡数据是补卡进来的
        if (!empty($this->staffAData[$date]['started_at']) && $this->staffAData[$date]['started_state'] == StaffWorkAttendanceModel::STATE_MAKE_UP_CARD) {
            $start_attendance = AttendanceCalendarEnums::MAKE_UP_CARD_EXISTED_CURRENT;
        }
        if (!empty($this->staffAData[$date]['end_at']) && $this->staffAData[$date]['end_state'] == StaffWorkAttendanceModel::STATE_MAKE_UP_CARD) {
            $end_attendance = AttendanceCalendarEnums::MAKE_UP_CARD_EXISTED_CURRENT;
        }
        //当前卡是系统补卡
        if (!empty($this->staffAData[$date]['started_at']) && $this->staffAData[$date]['started_state'] == StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY) {
            $start_attendance = AttendanceCalendarEnums::MAKE_UP_CARD_SYSTEM_ENTRY;
        }
        if (!empty($this->staffAData[$date]['end_at']) && $this->staffAData[$date]['end_state'] == StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY) {
            $end_attendance = AttendanceCalendarEnums::MAKE_UP_CARD_SYSTEM_ENTRY;
        }
        return [$start_attendance, $end_attendance];
    }

    /**
     * 获取补卡记录
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function getMakeUpCardData($staff_id, $start_date, $end_date): array
    {
        $auditSql = "SELECT CONCAT(attendance_date,'-',attendance_type) as date_type,reissue_card_date  FROM `staff_audit`
                      WHERE `staff_info_id` = :staff_info_id
                      AND status=1 And audit_type=1 
                      AND attendance_type in (1,2) AND attendance_date between :start_date and :end_date";
        $bind = ['staff_info_id' => $staff_id, 'start_date' => $start_date, 'end_date' => $end_date];
        $data = $this->getDI()->get('db_rby')->query($auditSql, $bind);
        $auditData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if ($auditData) {
            return array_column($auditData, NULL, 'date_type');
        }
        return [];
    }




    /**
     * 获取员工固化每天week_working_day
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function getStaffTransferData($staff_id, $start_date, $end_date): array
    {
        $sql = "select week_working_day,stat_date,staff_info_id,job_title,node_department_id,store_id from  hr_staff_transfer  WHERE `staff_info_id` = :staff_info_id and  stat_date between :start_date and :end_date";
        $bind = ['staff_info_id' => $staff_id, 'start_date' => $start_date, 'end_date' => $end_date];
        $data = $this->getDI()->get('db_rby')->query($sql, $bind);
        $result = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if ($result) {
            return array_column($result, null, 'stat_date');
        }
        return [];
    }

    /**
     * @description 获取处罚列表显示权限
     * @return bool
     */
    protected function getPenaltyPermission(): bool
    {
        return false;
    }

    //主账号信息
    public function getMasterStaffInfo(){
        if($this->staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF_0){
            return $this->staffInfo;
        }
        //当前是子账号 找主账号
        $supportInfo = HrStaffApplySupportStoreModel::findFirst([
            'conditions' => 'sub_staff_info_id = :staff_id:',
            'bind' => ['staff_id' => $this->staffId],
            'order'=> "id desc",
        ]);

        if(empty($supportInfo)){
            //子账号进来的 还没有支援信息
            $this->logger->write_log("getMasterStaffInfo support info empty {$this->staffId}");
            return $this->staffInfo;
        }
        return  (new StaffRepository())->getStaffPosition($supportInfo->staff_info_id);
    }

    public function getSupportData($start_date, $end_date){
        $supportServer = new StaffSupportStoreServer($this->lang, $this->timeZone);
        if($this->staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF_0){
            $data = $supportServer->getSupportDataBetween($this->staffId,$start_date, $end_date);
            return $data;
        }

        //子账号
        $data = $supportServer->getSupportDataBetween($this->staffId,$start_date, $end_date,$this->staffId);
        return $data;
    }
    //把支援日期 拆分
    public function dealSupportData(){
        if(empty($this->supportData)){
            return [];
        }
        $data = [];
        foreach ($this->supportData as $da){
            foreach ($this->monthDay as $date){
                if($date >= $da['employment_begin_date'] && $date <= $da['employment_end_date']){
                    $data[$date] = $da;
                }
            }
        }
        $this->supportData = $data;

    }

    //获取对应月份 居家办公日期
    public function getWhDate(){
        $server = new WorkHomeServer($this->lang, $this->timeZone);
        $whDate = $server->getWhDayTH($this->monthDay, $this->staffId);
        return $whDate;
    }


}