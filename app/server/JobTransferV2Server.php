<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use app\enums\SysAttachmentEnums;
use FlashExpress\bi\App\Enums\AuditDetailOperationsEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\JobTransferConfirmEnums;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\FrontlineSpecialNonCarriageModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\JobTransferExtendModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\JobTransferOperateLogModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysAttachmentModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\HrJobDepartmentRelationRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\JobTitleRepository;
use FlashExpress\bi\App\Repository\JobtransferRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysListRepository;
use Phalcon\Mvc\Model\Resultset;
use Phalcon\Mvc\Phalcon\Mvc\Model;
use WebGeeker\Validation\Validation;

class JobTransferV2Server extends AuditBaseServer
{

    const PROJECT_NUM_PREFIX_EV = 'project_num_';
    const PROJECT_NUM_PREFIX_VAN_PROJECT = 'van_courier_project_num_';

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang,$timezone);
    }

    /**
     * @description 提交转岗申请
     * @throws ValidationException
     * @throws BusinessException
     */
    public function addJobTransfer($paramIn = [])
    {
        //[1]获取参数
        $transferStaffId    = $this->processingDefault($paramIn, 'staff_id', 2);
        $submitterId        = $this->processingDefault($paramIn, 'submitter_id');
        $departmentId       = $this->processingDefault($paramIn, 'after_department_id', 2);
        $storeId            = $this->processingDefault($paramIn, 'after_store_id');
        $jobTitle           = $this->processingDefault($paramIn, 'after_position_id', 2);
        $afterDate          = $this->processingDefault($paramIn, 'after_date');
        $afterHireType      = $this->processingDefault($paramIn, 'after_hire_type');
        $jobHandoverStaffId = $this->processingDefault($paramIn, 'job_handover_staff_id', 2);
        $transferReason     = $this->processingDefault($paramIn, 'reason_type');
        $reason             = $this->processingDefault($paramIn, 'reason');
        $reason             = addcslashes(stripslashes($reason), "'");
        $uploadFiles        = $this->processingDefault($paramIn, 'upload');


	    //[2]参数校验
	    //[2-1]校验员工信息
        $params = [
            'submitter_id'          => $submitterId,
            'transfer_staff_id'     => $transferStaffId,
            'job_handover_staff_id' => $jobHandoverStaffId,
            'after_date'            => $afterDate,
            'after_store_id'        => $storeId,
            'after_position_id'     => $jobTitle,
        ];
        $transferValidateServer = $this->class_factory('JobTransferValidateServer', $this->lang, $this->timeZone);
        $transferValidateServer->init($params)->loadRules(get_class($transferValidateServer)::CREATE_RULES)->check();

        //[3]获取转岗前数据
        $transferStaffInfo = $transferValidateServer->getJobTransferStaffInfo();

        //根据网点信息获取大区、片区信息
        $repository       = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
        $currentStoreData = $repository->getOrganizationRegionPiece($transferStaffInfo['store_id']);
        $afterStoreData   = $repository->getOrganizationRegionPiece($storeId);

        $transferType = $this->getTransferType($transferStaffInfo['node_department_id'], $transferStaffInfo['job_title']);

        //转岗前的工作天数与轮休规则
        $jobTransferRepository = new JobTransferRepository($this->timeZone);
        $baseStaffInfo = $jobTransferRepository->getBaseStaffInfo($transferStaffId);
        $beforeWorkingDayRestType = $baseStaffInfo['week_working_day'] . $baseStaffInfo['rest_type'];

        //获取转岗前角色
        $staffInfo = (new HrStaffInfoServer())->getStaffInfo($transferStaffId);

        //获取转岗前车辆信息
        $vehicleInfo = $this->getVehicleDetailByStaffId($transferStaffId);

        //转岗前公司ID
        $currentDepartmentInfo = (new DepartmentRepository())->getSpecDepartmentInfo($transferStaffInfo['node_department_id'], 'id,company_id');

        //转岗后职级
        $hcId       = 0;
        $afterGrade = 0;
        $hcServer   = new HcServer($this->lang, $this->timeZone);
        if ($transferType == JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE) {

            //转岗后HC
            $hcId = $hcServer->getValidTransferHc($staffInfo['hire_type'], $departmentId, $storeId, $jobTitle, $afterDate);
            if (empty($hcId)) {
                $hcServer->checkBudget($departmentId, $storeId, $jobTitle);
            }

            //获取转岗后职级
            $afterGrade = $this->calcJobTitleGrade($transferType, $departmentId, $jobTitle, $transferStaffInfo['job_title_grade_v2']);

            //转岗后公司ID
            $afterDepartmentInfo = (new DepartmentRepository())->getSpecDepartmentInfo($departmentId, 'id,company_id');

            $getDefaultParams = [
                'staff_id'      => $transferStaffId,
                'department_id' => $departmentId,
                'position_id'   => $jobTitle,
                'store_id'      => $storeId,
            ];
            $validateHireType = $this->getDefaultHireType($getDefaultParams);
            if (isset($afterHireType, $validateHireType['hire_type']) && !in_array($afterHireType, $validateHireType['hire_type'])) {
                throw new ValidationException($this->getTranslation()->_('invalidate_hire_type'));
            }

            //获取转岗
            $afterHireTimes = $this->getDefaultHireTimes($afterHireType);
        }

        //添加转岗信息
        $param = [
            'staff_id'                      => $transferStaffId,
            'submitter_id'                  => $submitterId,
            'serial_no'                     => $this->getRandomId(),
            'current_company_id'            => $currentDepartmentInfo['company_id'] ? : 0,
            'current_department_id'         => $transferStaffInfo['node_department_id'],
            'current_store_id'              => $transferStaffInfo['store_id'],
            'current_position_id'           => $transferStaffInfo['job_title'],
            'current_manager_id'            => $transferStaffInfo['manager_id'] ?? '',
            'current_role_id'               => strlen($staffInfo['position_category']) > 0 ? $staffInfo['position_category'] : '',
            'current_region_id'             => !empty($currentStoreData) ? $currentStoreData->region_id: 0,
            'current_job_title_grade'       => $transferStaffInfo['job_title_grade_v2'] ?? 0,
            'current_piece_id'              => !empty($currentStoreData) ? $currentStoreData->piece_id: 0,
            'after_company_id'              => $afterDepartmentInfo['company_id'] ?? 0,
            'after_department_id'           => $departmentId,
            'after_store_id'                => $storeId,
            'after_region_id'               => !empty($afterStoreData) ? $afterStoreData->region_id: 0,
            'after_piece_id'                => !empty($afterStoreData) ? $afterStoreData->piece_id: 0,
            'after_position_id'             => $jobTitle,
            'after_job_title_grade'         => $afterGrade,
            'after_date'                    => $afterDate,
            'transfer_reason'               => $transferReason,
            'reason'                        => $reason ? : '',
            'job_handover_staff_id'         => $jobHandoverStaffId,
            'state'                         => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED,   //待转岗
            'approval_state'                => enums::APPROVAL_STATUS_PENDING,                         //待审批
            'approval_state_stage_one'      => enums::APPROVAL_STATUS_PENDING,                         //一阶段待审批
            'approval_state_stage_two'      => enums::APPROVAL_STATUS_PENDING,                         //二阶段待审批
            'confirm_state'                 => JobTransferConfirmEnums::CONFIRM_STATE_INVALID,        //待确认
            'vehicle_source'                => $vehicleInfo['vehicle_source'] ?? 0,                    //转岗前车辆来源
            'current_rental_car_created_at' => $vehicleInfo['vehicle_start_date'] ?? null,             //转岗前用车开始日期
            'before_working_day_rest_type'  => $beforeWorkingDayRestType,
            'type'                          => $transferType,
            'workflow_role_name'            => JobTransferEnums::JOB_TRANSFER_VERSION_V3,
            'current_project_num'           => in_array($transferStaffInfo['job_title'], [VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID, VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID])
                ? $vehicleInfo['project_num']
                : null,
            'current_hire_type'             => $staffInfo['hire_type'] ?? 0,
            'current_hire_times'            => $staffInfo['hire_times'] ?? 0,
        ];

        //一线非个人代理
        if ($transferType == JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE &&
            !in_array($afterHireType, HrStaffInfoModel::$agentTypeTogether) && !empty($afterHireType)) {
            $param['after_hire_type']  = $afterHireType;
            $param['after_hire_times'] = $afterHireTimes ?? null;
        }
        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            //扣减HC
            if (!empty($hcId)) {
                $param['hc_id'] = $hcId;
                $hcServer->deductHc($hcId);
            }

            $jobTransferId = $jobTransferRepository->addJobTransfer($param);
            if (empty($jobTransferId)) {
                throw new BusinessException($this->getTranslation()->_('jobtransfer_0004'));
            }
            if (!empty($uploadFiles)) {
                $insertImgData = [];
                foreach ($uploadFiles as $image) {
                    $insertImgData[] = [
                        'id'            => $jobTransferId,
                        'image_path'    => $image,
                    ];
                }
                (new PublicRepository())->batchInsertImgs($insertImgData, SysAttachmentEnums::OSS_TYPE_JOB_TRANSFER_CONFIRM_IMAGES);
            }
            $extend = $this->getParseNodeParams($jobTransferId, $submitterId);

            //提交转岗转出申请
            $ret = (new ApprovalServer($this->lang, $this->timeZone))->create($jobTransferId,
                AuditListEnums::APPROVAL_TYPE_JT,
                $submitterId,
                null,
                $extend
            );
            if ($ret === false) {
                throw new BusinessException($this->getTranslation()->_('contract_create_workflow_error'));
            } else {
                $db->commit();
            }
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->write_log('add Job Transfer:' . $e->getMessage() . $e->getTraceAsString() , 'notice');
            return $this->checkReturn(-3, $e->getMessage());
        }
        return $this->checkReturn(['data' => ['id' => $jobTransferId]]);
    }

    /**
     * @description 计算转岗后职级
     * @param $transfer_type
     * @param $after_department_id
     * @param $after_position_id
     * @param $before_job_grade
     * @return int|mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function calcJobTitleGrade($transfer_type, $after_department_id, $after_position_id, $before_job_grade)
    {
        //职级校验
        //1. 校验转岗前后的职位职级范围是否有重合,有重合则转岗成功
        //2. 如没有重合,则再次校验[转岗后部门+职位]是否是[可跨职级转岗的部门+职位]
        //如是，则可提交;如不是, 提示：“员工转岗后部门职位对应的职级范围与员工转岗前不匹配，同职级才能转岗”
        //$beforeJobGrade        = HrJobDepartmentRelationRepository::getJobGrade($before_department_id, $before_position_id);
        $afterJobGrade         = HrJobDepartmentRelationRepository::getJobGrade($after_department_id, $after_position_id);
        $config                = $this->getCrossGradeConfig();

        if ($transfer_type == JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE) {
            if (in_array($after_position_id, $config)) { //可跨职级

                $afterGrade = min($afterJobGrade);
            } else { //不可跨职级

                if (in_array($before_job_grade, $afterJobGrade)) {
                    $afterGrade = $before_job_grade;
                } else {
                    throw new ValidationException($this->getTranslation()->_('job_transfer.job_grade_unmatch'));
                }
            }
        } else {

            if (in_array($before_job_grade, $afterJobGrade)) {
                $afterGrade = $before_job_grade;
            } else {
                throw new ValidationException($this->getTranslation()->_('job_transfer.job_grade_unmatch'));
            }
        }
        return $afterGrade;
    }

    /**
     * @description 转岗类型 1=一线转岗 2=非一线
     * @param $department_id
     * @param $job_title_id
     * @return int
     */
    public function getTransferType($department_id , $job_title_id): int
    {
        $positionConfig = $this->getFrontLineConfig();

        //一线职位
        if (!empty($positionConfig) && in_array($job_title_id, $positionConfig)
        ) {
            return JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE;
        }
        return JobTransferEnums::JOB_TRANSFER_TYPE_NOT_FRONT_LINE;
    }

    /**
     * @description 转岗审批
     * 审批人可以
     *  - 同意
     *  - 驳回
     *
     * 申请人可以
     *  - 撤销
     * @throws \Exception
     */
    public function approvalTransfer($paramIn = [])
    {
        $auditId      = $this->processingDefault($paramIn, 'audit_id');
        $status       = $this->processingDefault($paramIn, 'status');
        $staffInfoId  = $this->processingDefault($paramIn, 'staff_id');
        $rejectReason = $this->processingDefault($paramIn, 'reject_reason');
        $rejectReason = addcslashes(stripslashes($rejectReason), "'");

        //获取详情
        $detailInfo = JobTransferModel::findFirst($auditId);
        if (empty($detailInfo)) {
            throw new ValidationException('no valid data');
        }

        if ($detailInfo->approval_state != enums::APPROVAL_STATUS_PENDING) {
            throw new ValidationException($this->getTranslation()->_("4012"));
        }

        $request = (new ApplyRepository())->getApplyObject(AuditListEnums::APPROVAL_TYPE_JT, $auditId);
        $isSecondStageAudit = (new JobTransferConfirmServer($this->lang, $this->timeZone))->isSecondStageAudit($request);

        // 待确认 & 已经创建二阶段审批
        if ($detailInfo->confirm_state == JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM && !$isSecondStageAudit &&
            in_array($status, [enums::APPROVAL_STATUS_CANCEL, enums::APPROVAL_STATUS_TIMEOUT])
        ) {
            $this->processStateToFinal($detailInfo, $status, $staffInfoId);
        } else {

            if ($detailInfo->approval_state_stage_one != enums::APPROVAL_STATUS_PENDING && $detailInfo->approval_state_stage_two != enums::APPROVAL_STATUS_PENDING ||
                $detailInfo->approval_state_stage_one == enums::APPROVAL_STATUS_APPROVAL && !$isSecondStageAudit
            ) {
                throw new ValidationException($this->getTranslation()->_("err_msg_data_updated_please_refresh"));
            }

            //获取申请的详情
            $server = new ApprovalServer($this->lang, $this->timeZone);
            if (enums::APPROVAL_STATUS_APPROVAL == $status) {
                $server->approval($auditId, AuditListEnums::APPROVAL_TYPE_JT, $staffInfoId);
            } elseif (enums::APPROVAL_STATUS_REJECTED == $status) {
                $server->reject($auditId, AuditListEnums::APPROVAL_TYPE_JT, $rejectReason, $staffInfoId);
            } else {
                $server->cancel($auditId, AuditListEnums::APPROVAL_TYPE_JT, '', $staffInfoId);
            }
        }
        return $this->checkReturn(['data' => $auditId]);
    }

    /**
     * 一阶段审批同意后，转岗待确认，二阶段未创建审批
     * @param $detailInfo
     * @param $status
     * @param $staffInfoId
     * @return bool
     * @throws ValidationException
     */
    public function processStateToFinal($detailInfo, $status, $staffInfoId): bool
    {
        $this->getDI()->get('db')->begin();

        //更新确认、转岗状态
        $detailInfo->state = JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED;
        $detailInfo->approval_state = $status;
        $detailInfo->confirm_state = $status;
        $detailInfo->save();

        //更新申请表状态
        $requestObj = (new ApplyRepository())->getApplyObject(AuditListEnums::APPROVAL_TYPE_JT, $detailInfo->id);
        $requestObj->setState($status);
        $requestObj->setFinalApprovalTime(date('Y-m-d H:i:s'));
        $requestObj->setFinalApprover($staffInfoId);
        $requestObj->save();

        //更新确认状态，并插入撤销｜超时日志
        $workflowService = new WorkflowServer($this->lang, $this->timeZone);
        if ($status == enums::APPROVAL_STATUS_CANCEL) {
            $action = enums::WF_ACTION_CONFIRM_CANCEL;
        } else {
            $action = enums::WF_ACTION_CONFIRM_OVERTIME;
        }
        $workflowService->updateAuditLog($requestObj, $detailInfo->staff_id, $action, null);
        if (in_array($status, [enums::APPROVAL_STATUS_CANCEL, enums::APPROVAL_STATUS_TIMEOUT])) {
            $workflowService->saveAuditLog($requestObj, $staffInfoId, $status, null);
        }

        //返还HC
        if (!empty($detailInfo->hc_id)) {
            (new HcServer($this->lang, $this->timeZone))->remittanceHc($detailInfo->hc_id);
        }

        if ($status == enums::APPROVAL_STATUS_TIMEOUT) {
            //发送超时消息
            //给申请人发送消息
            $submitterLang              = (new StaffServer())->getLanguage($detailInfo->submitter_id);
            $messageData                = $this->getJobtransferInfo(['id' => $detailInfo->id]);
            $messageData['audit_state'] = $this->getTranslation($submitterLang)->_('audit_status.' . $detailInfo->approval_state);
            JobTransferMessageServer::getInstance()->noticeAuditFinish($detailInfo->submitter_id,
                $submitterLang,
                $messageData,
                JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE
            );
        }

        //固化确认数据
        if (isCountry($this->getPersistentCountry())) {
            $server = $this->class_factory("JobTransferConfirmServer",$this->lang, $this->timeZone);
            $insertConfirmContent = $server->getPersistConfirmBaseInfo(['state' => $status], $detailInfo);
            $detailInfo->sign_url        = '';
            $detailInfo->confirm_date    = date('Y-m-d');
            $detailInfo->confirm_content = json_encode($insertConfirmContent, JSON_UNESCAPED_UNICODE);
            $detailInfo->save();
            $this->logger->write_log('processStateToFinal save confirm_content ' . json_encode($insertConfirmContent, JSON_UNESCAPED_UNICODE), 'info');
        }

        $this->getDI()->get('db')->commit();
        return true;
    }

    /**
     * 激活转岗到转岗确认
     */
    public function activate($job_transfer_id, $operator_id): bool
    {
        try {
            $this->getDI()->get('db')->begin();

            $detail_info = JobTransferModel::findFirst([
                'conditions' => 'id = :job_transfer_id:',
                'bind' => [
                    'job_transfer_id' => $job_transfer_id,
                ],
                'for_update' => true,
            ]);
            if (empty($detail_info)) {
                throw new ValidationException('job transfer id not exist:' . $job_transfer_id);
            }

            //更新申请表状态
            $requestObj = (new ApplyRepository())->getApplyObject(AuditListEnums::APPROVAL_TYPE_JT, $detail_info->id);
            $requestObj->setState(enums::APPROVAL_STATUS_PENDING);
            $requestObj->setFinalApprovalTime(null);
            $requestObj->setFinalApprover(null);
            $requestObj->save();

            $isSecondStageAudit = (new JobTransferConfirmServer($this->lang, $this->timeZone))->isSecondStageAudit($requestObj);
            if (!$isSecondStageAudit) {
                //插入待确认审批日志
                $workflowService = new WorkflowServer($this->lang, $this->timeZone);
                $workflowService->removeAuditLog($requestObj, enums::WF_ACTION_CONFIRM_REJECT);
                $workflowService->saveAuditLog($requestObj, $detail_info->staff_id, enums::WF_ACTION_CONFIRM_PENDING, null);

                //扣减HC
                //获取转岗人信息
                $server                    = new HcServer($this->lang, $this->timeZone);
                $staffService              = new StaffServer();
                $staffInfo                 = $staffService->getStaffInfoSpecColumns($detail_info->staff_id,
                    "h.staff_info_id, h.hire_type");
                $transferInfo              = $detail_info->toArray();
                $transferInfo['hire_type'] = $staffInfo['hire_type'];

                [$hc_id, $err] = $server->occupyHc($transferInfo);
                if (!is_null($err) && $err->getCode() != ErrCode::SUCCESS) {
                    throw new ValidationException($err->getMessage());
                }

                //更新确认、转岗状态
                $detail_info->hc_id          = $hc_id ? : null;
                $detail_info->state          = JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED;
                $detail_info->approval_state = enums::APPROVAL_STATUS_PENDING;
                $detail_info->confirm_state  = JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM;
                $detail_info->save();
            } else {
                //更新确认、转岗状态
                $detail_info->confirm_state  = JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM;
                $detail_info->save();
            }

            $this->getDI()->get('db')->commit();
        } catch (ValidationException $ve) {
            $this->getDI()->get('db')->rollback();
            throw $ve;
        }

        return true;
    }

    /**
     * @description 获取BP审批字段校验
     * @param array $params
     * @return array
     */
    public function getBpValidate(array $params = []): array
    {
        //过滤字段
        $validations = [];

        //获取转岗信息
        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);
        $detailInfo     = (new JobtransferRepository($this->timeZone))->getJobtransferInfo(['id' => $params['audit_id']]);

        if ($detailInfo['after_position_id'] == VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID) {

            if (empty($detailInfo['rental_car_cteated_at'])) {
                $validations['after_rental_car_created_at'] = 'Required|Date';
            }

        } else if ($detailInfo['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE) {

            if (isset($params['after_vehicle_source']) && $params['after_vehicle_source'] == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
                $validations['after_rental_car_created_at'] = 'IfIntEq:after_vehicle_source,2|Date';
            }
            $detailInfo['after_vehicle_source'] = $params['after_vehicle_source'];
        }
        if (isCountry()) {
            $validations['hire_type'] = 'IntIn:1,2,3';
        } else {
            $validations['hire_type'] = 'IntIn:1,2';
        }
        $validations['hire_times'] = 'IfIntEq:hire_type,2|Required|IntGeLe:1,12';
        $filterColumns             = $this->getValidateFilterColumns($detailInfo);

        //对于存在可以编辑字段的情况，取出可编辑字段的类型
        return $approvalServer->getCanEditFieldValidation($params['audit_id'], AuditListEnums::APPROVAL_TYPE_JT, $filterColumns, $validations);
    }

    /**
     * 组织合同数据
     * @param $date
     * @return array
     */
    public function generateSendContractFailData($date): array
    {
        if (empty($date)) {
            return [];
        }
        $jobTransferInfo = JobTransferModel::find([
            'conditions' => 'actual_after_date = :after_date: and after_hire_type != :after_hire_type:',
            'bind' => [
                'after_date'      => $date,
                'after_hire_type' => HrStaffInfoModel::HIRE_TYPE_UN_PAID,
            ],
            'columns' => 'staff_id,current_company_id,current_department_id,current_region_id,current_piece_id,current_store_id,current_position_id,
                after_company_id,after_department_id,after_region_id,after_piece_id,after_store_id,after_position_id,actual_after_date,current_hire_type,
                after_hire_type',
        ])->toArray();
        $jobTransferStaffInfo = array_column($jobTransferInfo, null,'staff_id');
        if (empty($jobTransferStaffInfo)) {
            return [];
        }

        //获取姓名
        $jobTransferStaffInfoDetail = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id IN ({staff_info_id:array})',
            'bind' => [
                'staff_info_id' => array_keys($jobTransferStaffInfo),
            ],
            'columns' => 'staff_info_id,name'
        ])->toArray();
        $jobTransferStaffInfoDetail = array_column($jobTransferStaffInfoDetail, 'name','staff_info_id');

        $staffContractInfo = HrStaffContractModel::find([
            'conditions' => 'staff_id in({staff_ids:array}) and contract_is_deleted = :is_deleted: and contract_is_need = 1 
                and contract_status in({contract_status:array}) and contract_type in({contract_type:array}) and is_transfer = 0',
            'bind'       => [
                'staff_ids' => array_keys($jobTransferStaffInfo),
                'contract_status' => [
                    enums::CONTRACT_STATUS_ADD,
                    enums::CONTRACT_STATUS_SEND,
                    enums::CONTRACT_STATUS_SIGNATURE,
                    enums::CONTRACT_STATUS_FEEDBACK,
                    enums::CONTRACT_STATUS_UNSIGNED,
                    enums::CONTRACT_STATUS_AUDIT,
                ],
                'is_deleted' => HrStaffContractModel::CONTRACT_DELETED_NO,
                'contract_type' => [
                    HrStaffContractModel::CONTRACT_TYPE_LABOR_CONTRACT,
                    HrStaffContractModel::CONTRACT_TYPE_ASSET_CONTRACT,
                ],
            ]
        ])->toArray();
        if (empty($staffContractInfo)) {
            return [];
        }
        $sysObj = new SysListRepository();

        //查询部门名称
        $departmentIdsArr = array_merge(array_column($jobTransferInfo, 'current_department_id'),
            array_column($jobTransferInfo, 'after_department_id'),
            array_column($jobTransferInfo, 'current_company_id'),
            array_column($jobTransferInfo, 'after_company_id')
        );
        $departmentData   = $sysObj->getDepartmentList([
            "ids" => $departmentIdsArr,
        ]);
        $departmentData   = array_column($departmentData, 'name', 'id');

        //查询职位名称
        $positionIdsArr = array_merge(array_column($jobTransferInfo, 'current_position_id'),
            array_column($jobTransferInfo, 'after_position_id'));
        $positionData   = $sysObj->getPositionList([
            "ids" => $positionIdsArr,
        ]);
        $positionData   = array_column($positionData, 'name', 'id');

        //查询网点名称
        $storeIdsArr = array_merge(array_column($jobTransferInfo, 'current_store_id'),
            array_column($jobTransferInfo, 'after_store_id'));
        $storeData   = $sysObj->getStoreList([
            "ids" => $storeIdsArr,
        ]);
        $storeData   = array_column($storeData, 'name', 'id');

        //查询大区
        $regionIdsArr = array_merge(array_column($jobTransferInfo, 'current_region_id'),
            array_column($jobTransferInfo, 'after_region_id'));
        $regionData   = $sysObj->getRegionList([
            "ids" => $regionIdsArr,
        ]);
        $regionData   = array_column($regionData, 'name', 'id');

        //查询片区
        $pieceIdsArr = array_merge(array_column($jobTransferInfo, 'current_piece_id'),
            array_column($jobTransferInfo, 'after_piece_id'));
        $pieceData   = $sysObj->getPieceList([
            "ids" => $pieceIdsArr,
        ]);
        $pieceData   = array_column($pieceData, 'name', 'id');

        $t = $this->getTranslation('en');

        $jobTransferInfo = array_column($jobTransferInfo, null,'staff_id');
        $resultList = [];
        foreach ($staffContractInfo as $item) {
            $data = $jobTransferInfo[$item['staff_id']] ?? [];
            if (empty($data)) {
                continue;
            }
            $resultList[] = [
                //ID
                $item['staff_id'],
                //Name
                $jobTransferStaffInfoDetail[$item['staff_id']] ?? '',
                //转岗前公司名
                $departmentData[$data["current_company_id"]] ?? '',
                //转岗前部门名
                $departmentData[$data["current_department_id"]] ?? '',
                //转岗前大区
                $regionData[$data["current_region_id"]] ?? '',
                //转岗前片区
                $pieceData[$data["current_piece_id"]] ?? '',
                //转岗前网点
                $storeData[$data["current_store_id"]] ?? '',
                //转岗前职位
                $positionData[$data["current_position_id"]] ?? '',
                //转岗前雇佣类型
                $t->_('hire_type_' . $data['current_hire_type']),
                //转岗后公司名
                $departmentData[$data["after_company_id"]] ?? '',
                //转岗后部门名
                $departmentData[$data["after_department_id"]] ?? '',
                //转岗后大区
                $regionData[$data["after_region_id"]] ?? '',
                //转岗后片区
                $pieceData[$data["after_piece_id"]] ?? '',
                //转岗后网点
                $storeData[$data["after_store_id"]] ?? '',
                //转岗后职位
                $positionData[$data["after_position_id"]] ?? '',
                //转岗后雇佣类型
                $t->_('hire_type_' . $data['after_hire_type']),
                //实际转岗日期
                $jobTransferInfo[$item['staff_id']]['actual_after_date'] ?? '',
                //合同类型
                $t->_('contract_type_' . $item['contract_type']),
            ];
        }
        return $resultList;
    }

    /**
     * @description BP审批同意
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function bpApproval($params): array
    {
        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",
        ];
        Validation::validate($params, $validations);

        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);
        if ($params['status'] == enums::APPROVAL_STATUS_APPROVAL &&
            $approvalServer->isExistCanEditField($params['audit_id'], AuditListEnums::APPROVAL_TYPE_JT)) {

            //开始校验提交字段
            $server = new JobTransferV2Server($this->lang, $this->timeZone);
            $server = Tools::reBuildCountryInstance($server, [$this->lang, $this->timeZone]);
            $bpValidations = $server->getBpValidate($params);
            Validation::validate($params, array_merge($validations, $bpValidations));

            //提交审核
            $returnArr = $this->auditTransfer($params);
        } else {
            throw new ValidationException($this->getTranslation()->_('please try again'));
        }
        return $returnArr ?? [];
    }

    /**
     * @description 转岗审核
     * 审批人一般为BP，存在可编辑的字段
     * @throws ValidationException
     */
    public function auditTransfer($paramIn = [])
    {
        //[1]获取参数
        $auditId                 = $this->processingDefault($paramIn, 'audit_id');
        $afterManagerId          = $this->processingDefault($paramIn, 'after_manager_id');
        $roles                   = $this->processingDefault($paramIn, 'after_role_ids');
        $afterWorkingDayRestType = $this->processingDefault($paramIn, 'after_working_day_rest_type');
        $vehicleSource           = $this->processingDefault($paramIn, 'after_vehicle_source');
        $rentalCarCreatedAt      = $this->processingDefault($paramIn, 'after_rental_car_created_at');
        $carType                 = $this->processingDefault($paramIn, 'car_type');
        $hcId                    = $this->processingDefault($paramIn, 'hc_id');
        $uploadFiles             = $this->processingDefault($paramIn, 'upload_files');
        $salaryType              = $this->processingDefault($paramIn, 'salary_type');
        $staffInfoId             = $this->processingDefault($paramIn, 'staff_id');
        $status                  = $this->processingDefault($paramIn, 'status');
        $projectNum              = $this->processingDefault($paramIn, 'project_num');
        $hireType                = $this->processingDefault($paramIn, 'hire_type');
        $hireTimes               = $this->processingDefault($paramIn, 'hire_times');

        //获取详情
        $transferDetail = JobTransferModel::findFirst($auditId);
        if (empty($transferDetail)) {
            throw new ValidationException('no valid data');
        }
        $staffServer = new StaffServer();
        $staffInfo   = $staffServer->getStaffById($transferDetail->staff_id);

        $updateParams = [];
        //获取转岗后直线上级
        if (!empty($afterManagerId)) {

            //转岗后直线上级不能为员工本人
            if ($afterManagerId == $transferDetail->staff_id) {
                throw new ValidationException($this->getTranslation()->_('job_transfer.can_not_be_himself'));
            }

            $updateParams['after_manager_id'] = $afterManagerId;
        }
        if (!empty($afterWorkingDayRestType)) {
            $updateParams['after_working_day_rest_type'] = $afterWorkingDayRestType;
        }
        if (!empty($vehicleSource)) {
            $updateParams['car_owner'] = $vehicleSource;
            //租用公司车辆才有开始时间
            if ($vehicleSource == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {

                if (strtotime($rentalCarCreatedAt) < strtotime(date('Y-m-d', strtotime($staffInfo['hire_date'])))) {
                    throw new ValidationException($this->getTranslation()->_('job_transfer.can_not_earlier_than_hire_date'));
                }

                $updateParams['rental_car_cteated_at'] = $rentalCarCreatedAt;
            }
        }
        //EV courier、Van project
        if (in_array($transferDetail->after_position_id, [VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID, VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID])) {
            if (empty($transferDetail->rental_car_cteated_at) && !empty($rentalCarCreatedAt)) {

                if (strtotime($rentalCarCreatedAt) < strtotime(date('Y-m-d', strtotime($staffInfo['hire_date'])))) {
                    throw new ValidationException($this->getTranslation()->_('job_transfer.can_not_earlier_than_hire_date'));
                }

                $updateParams['rental_car_cteated_at'] = $rentalCarCreatedAt;
                $updateParams['car_owner']             = VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE;
            }
        }
        if (!empty($projectNum)) {
            $updateParams['after_project_num'] = $projectNum;
        }
        if (!empty($carType)) {
            $updateParams['after_car_type'] = $carType;
        }
        if (!empty($hireType)) {
            $updateParams['after_hire_type'] = $hireType;
            if (in_array($hireType, [HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3])) { //月薪制
                if (!is_numeric($hireTimes)) {
                    throw new ValidationException($this->getTranslation()->_('job_transfer.miss_hire_times'));
                }
                if ($hireType == HrStaffInfoModel::HIRE_TYPE_2 && $hireTimes != HrStaffInfoModel::DEFAULT_MONTHLY_HIRE_TIME ||
                    $hireType == HrStaffInfoModel::HIRE_TYPE_3 && $hireTimes != HrStaffInfoModel::DEFAULT_DAILY_HIRE_TIME) {
                    throw new ValidationException($this->getTranslation()->_('job_transfer.invalid_hire_times'));
                }
                $updateParams['after_hire_times'] = $hireTimes;
            }
            if ($hireType == HrStaffInfoModel::HIRE_TYPE_1) {
                $updateParams['after_hire_times'] = 0;
            }
        }
        if (!empty($hcId)) {
            $updateParams['hc_id'] = $hcId;
            $hcRepo = new HcRepository($this->timeZone);
            $hcInfo = $hcRepo->getHcInfo($hcId);
            if (empty($hcInfo)) {
                throw new ValidationException($this->getTranslation()->_('4412'));
            }
            //获取网点信息
            if ($hcInfo['worknode_id'] != enums::HEAD_OFFICE_ID) {
                $repository                      = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
                $afterStoreData                  = $repository->getOrganizationRegionPiece($hcInfo['worknode_id']);
                $updateParams['after_piece_id']  = !empty($afterStoreData) ? $afterStoreData->piece_id : 0;
                $updateParams['after_region_id'] = !empty($afterStoreData) ? $afterStoreData->region_id : 0;
            }
            //转岗后公司ID
            $afterDepartmentInfo = (new DepartmentRepository())->getSpecDepartmentInfo($hcInfo['department_id'], 'id,company_id');

            $updateParams['after_department_id']   = $hcInfo['department_id'];
            $updateParams['after_store_id']        = $hcInfo['worknode_id'];
            $updateParams['after_position_id']     = $hcInfo['job_title'];
            $updateParams['after_job_title_grade'] = $this->calcJobTitleGrade(JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
                $hcInfo['department_id'],
                $hcInfo['job_title'], $transferDetail->current_job_title_grade);
            $updateParams['after_company_id']      = $afterDepartmentInfo['company_id'] ?? 0;
        }

        if (!empty($roles) && is_array($roles)) {
            if (!empty($hcId)) {
                $afterStoreId = $updateParams['after_store_id'];
            } else {
                $afterStoreId = $transferDetail->after_store_id;
            }
            if (in_array(enums::$roles['BRANCH_CASHIER'], $roles)) {
                $this->checkStoreCashier($afterStoreId);
            }
            $updateParams['after_role_ids'] = implode(',', $roles);
        }
        if (!empty($salaryType)) {
            $updateParams['salary_type'] = $salaryType;
        }

        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            $db->updateAsDict("job_transfer",
            $updateParams, [
                'conditions' => "id = ?",
                'bind' => [
                    $auditId,
                ],
            ]);
            if (!empty($hcId)) {
                (new HcServer($this->lang, $this->timeZone))->deductHc($hcId);
            }

            //如果是非一线转岗，需要在更新HC id的时候，重新查找转岗后的HRBP。
            //因为在申请的时候，这个人由于确实转岗后部门+网点，这个人是找不到的
            if ($status == enums::APPROVAL_STATUS_APPROVAL && !empty($hcId)) {
                $request = (new ApplyRepository())->getApplyObject(AuditListEnums::APPROVAL_TYPE_JT, $auditId);
                WorkflowPersistentServer::getInstance($this->lang, $this->timeZone)
                    ->refreshHasPersistentNode($request, $staffInfoId, $this->getParseNodeParams($auditId, $staffInfoId, $status));
            }

            //插入图片
            if(!empty($uploadFiles)) {
                $insertImgData  = [];
                //软删除已经添加的图片
                $images = SysAttachmentModel::find([
                    'conditions' => "oss_bucket_type = 'JOB_TRANSFER' and oss_bucket_key = :oss_bucket_key: ",
                    'bind' => [
                        'oss_bucket_key'  => $auditId,
                    ],
                ])->toArray();
                $images = array_column($images, 'object_key');
                foreach($uploadFiles as $image) {
                    $urlInfo = parse_url(stripslashes($image));
                    if (in_array(trim($urlInfo['path'], '/'), $images)) {
                        continue;
                    }

                    if (is_array($image)) { //需要保存原文件名
                        $insertImgData[] = [
                            'id'            => $auditId,
                            'image_path'    => $image['url'],
                            'original_name' => $image['file_name'],
                        ];
                    } else {
                        $insertImgData[] = [
                            'id'         => $auditId,
                            'image_path' => $image,
                        ];
                    }
                }
                (new PublicRepository())->batchInsertImgs($insertImgData, "JOB_TRANSFER");
            }

            $this->approvalTransfer($paramIn);

            $db->commit();

            return $this->checkReturn(['data' => $auditId]);
        } catch (ValidationException|\Exception $e) {
            $db->rollback();
            $this->logger->write_log('[auditTransfer]' . $e->getMessage() . $e->getTraceAsString(), 'notice');
        }

        return $this->checkReturn(['data' => null]);
    }

    /**
     * 校验出纳人数
     * @param $store_id
     * @return void
     * @throws ValidationException
     */
    protected function checkStoreCashier($store_id)
    {
        if ($store_id == enums::HEAD_OFFICE_ID) { //总部不校验
            return;
        }
        //获取在职的出纳人数
        $server = (new AdjustRoleServer($this->lang, $this->timeZone));
        $storeCashierInfo = $server->getStoreCashierNo($store_id);

        //获取网点类型
        $storeInfo = SysStoreModel::findFirstById($store_id);
        if (empty($storeInfo)) {
            throw new ValidationException('store id not exist:' . $store_id);
        }
        $storeCashierLimitCnt  = $server->getStoreCashierLimitNo($storeInfo->category);
        if (count($storeCashierInfo) >= $storeCashierLimitCnt) {
            throw new ValidationException($this->getTranslation()->_('reselect_staff_roles', ['count' => $storeCashierLimitCnt]));
        }
    }

    /**
     * 设置回调
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return void
     * @throws InnerException
     * @throws ValidationException
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        //获取详情
        if ($isFinal) {
            $transferDetail = JobTransferModel::findFirst($auditId);
            if (empty($transferDetail)) {
                throw new InnerException(sprintf('data id %d not exist', $auditId));
            }
            if ($transferDetail->approval_state_stage_one == enums::APPROVAL_STATUS_PENDING) {

                //审批同意-> 转岗确认
                $this->processStageOne($transferDetail, $state, $extend, $isFinal);

            } else {
                $this->processStageTwo($transferDetail, $state, $extend, $isFinal);
            }

            if ($state != enums::APPROVAL_STATUS_APPROVAL && !empty($transferDetail->hc_id)) {
                (new HcServer($this->lang, $this->timeZone))->remittanceHc($transferDetail->hc_id);
            }

            $jobTransferInfo = $this->getJobTransferInfo(['id' => $auditId]);
            if (empty($jobTransferInfo)) {
                throw new \Exception('data err');
            }

            if (isCountry('TH')) {
                // TH 暂时没有重新确认的问题
                $needSendAuditFinishMessageFlag = $transferDetail->approval_state_stage_one == enums::APPROVAL_STATUS_APPROVAL &&
                    $transferDetail->approval_state_stage_two == enums::APPROVAL_STATUS_PENDING &&
                    $transferDetail->confirm_state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS &&
                    $state == enums::APPROVAL_STATUS_APPROVAL || in_array($state, [
                        enums::APPROVAL_STATUS_REJECTED,
                        enums::APPROVAL_STATUS_TIMEOUT]);
            } else {
                // PH、MY 可以重发确认单
                $needSendAuditFinishMessageFlag = $transferDetail->approval_state_stage_one == enums::APPROVAL_STATUS_APPROVAL &&
                    $transferDetail->approval_state_stage_two == enums::APPROVAL_STATUS_PENDING &&
                    $state == enums::APPROVAL_STATUS_APPROVAL || in_array($state, [
                        enums::APPROVAL_STATUS_REJECTED,
                        enums::APPROVAL_STATUS_TIMEOUT]);
            }

            if ($needSendAuditFinishMessageFlag) {
                //推送文本内容
                $submitterLang              = (new StaffServer())->getLanguage($jobTransferInfo['submitter_id']);
                $messageData                = $this->getJobtransferInfo(['id' => $auditId]);
                $messageData['audit_state'] = $this->getTranslation($submitterLang)->_('audit_status.' . $state);
                JobTransferMessageServer::getInstance()->noticeAuditFinish($jobTransferInfo['submitter_id'],
                    $submitterLang,
                    $messageData,
                    JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE
                );
                $this->logger->write_log("pushAndSendMessageToSubmitter:转岗最终审批-" . $jobTransferInfo['submitter_id'],"info");
            }

            //固化转岗确认数据
            //待确认时才固化
            if (isCountry($this->getPersistentCountry()) && in_array($transferDetail->type, [JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE, JobTransferEnums::JOB_TRANSFER_TYPE_NOT_FRONT_LINE]) &&
                $transferDetail->approval_state_stage_one == enums::APPROVAL_STATUS_APPROVAL &&
                $transferDetail->approval_state_stage_two == enums::APPROVAL_STATUS_PENDING &&
                $transferDetail->confirm_state == JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM &&
                in_array($state, [enums::APPROVAL_STATUS_REJECTED, enums::APPROVAL_STATUS_TIMEOUT, enums::APPROVAL_STATUS_CANCEL])) {

                $server = $this->class_factory("JobTransferConfirmServer",$this->lang, $this->timeZone);
                $insertConfirmContent = $server->getPersistConfirmBaseInfo(['state' => $state], $transferDetail);

                $transferDetail->sign_url        = '';
                $transferDetail->confirm_date    = date('Y-m-d');
                $transferDetail->confirm_content = json_encode($insertConfirmContent, JSON_UNESCAPED_UNICODE);
                $transferDetail->save();
                $this->logger->write_log('setProperty save confirm_content ' . json_encode($insertConfirmContent, JSON_UNESCAPED_UNICODE), 'info');
            }
        }
    }

    /**
     * @description 处理一阶段审批
     * @param $transferDetail
     * @param $state
     * @param $extend
     * @param $isFinal
     */
    protected function processStageOne($transferDetail, $state, $extend, $isFinal)
    {
        //处理审批状态
        if (enums::APPROVAL_STATUS_APPROVAL == $state) {

            //1. 更新一阶段转岗状态、转岗确认状态
            $updateData = [
                'approval_state_stage_one' => enums::APPROVAL_STATUS_APPROVAL,
                'confirm_state'            => JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM,
            ];

            //2. 添加确认log
            $async_params = [
                'staff_info_id' => $transferDetail->staff_id,
                'audit_id'      => $transferDetail->id,
            ];
            $this->logger->write_log([
                'message'   => '异步写入待确认log',
                'redis_key' => RedisEnums::REDIS_ASYNC_JOB_TRANSFER,
                'params'    => $async_params,
            ],'info');
            $redis = $this->getDI()->get('redisLib');
            $redis->lpush(RedisEnums::REDIS_ASYNC_JOB_TRANSFER, json_encode($async_params));

            //转岗前是个人代理没有bp节点，需补充转岗后上级、转岗后角色、转岗后工作天数与轮休规则、转岗后雇佣类型、转岗后雇佣期间
            if (in_array($transferDetail->current_hire_type, HrStaffInfoModel::$agentTypeTogether)) {
                $updateData['after_manager_id']            = $this->getafterManagerIdInfo($transferDetail->after_store_id,
                    $transferDetail->after_department_id,
                    $transferDetail->after_position_id,
                    $transferDetail->after_date,
                    $transferDetail->staff_id
                );
                $updateData['after_working_day_rest_type'] = HrStaffInfoModel::WEEK_WORKING_DAY_6 . HrStaffInfoModel::REST_TYPE_1;
                $updateData['after_role_ids']              = $transferDetail->current_role_id;
                $updateData['after_hire_type']             = HrStaffInfoModel::HIRE_TYPE_UN_PAID;
                $updateData['after_hire_times']            = HrStaffInfoModel::DEFAULT_MONTHLY_HIRE_TIME;
            }

        } else if (enums::APPROVAL_STATUS_REJECTED == $state) {
            $updateData = [
                'approval_state'           => enums::APPROVAL_STATUS_REJECTED,
                'approval_state_stage_one' => enums::APPROVAL_STATUS_REJECTED,
                'state'                    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
                //'confirm_state'            => JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_REJECT,
            ];
        } else if (enums::APPROVAL_STATUS_CANCEL == $state) {
            $updateData = [
                'approval_state'           => enums::APPROVAL_STATUS_CANCEL,
                'approval_state_stage_one' => enums::APPROVAL_STATUS_CANCEL,
                'state'                    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
                //'confirm_state'            => JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_CANCEL,
            ];
        } else {
            $updateData = [
                'approval_state'           => enums::APPROVAL_STATUS_TIMEOUT,
                'approval_state_stage_one' => enums::APPROVAL_STATUS_TIMEOUT,
                'state'                    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
                //'confirm_state'            => JobTransferConfirmEnums::CONFIRM_STATE_OVER_TIME,
            ];
        }
        $this->getDI()->get('db')->updateAsDict(
            'job_transfer',
            $updateData,
            'id = ' . $transferDetail->id
        );
    }

    /**
     * @description 处理二阶段审批
     * @param $transferDetail
     * @param $state
     * @param $extend
     * @param $isFinal
     */
    protected function processStageTwo($transferDetail, $state, $extend, $isFinal)
    {
        if (enums::APPROVAL_STATUS_APPROVAL == $state) {
            $updateData = [
                "approval_state"           => enums::APPROVAL_STATUS_APPROVAL,
                'approval_state_stage_two' => enums::APPROVAL_STATUS_APPROVAL,
            ];
        } else if (enums::APPROVAL_STATUS_REJECTED == $state) {
            $updateData = [
                "approval_state"           => enums::APPROVAL_STATUS_REJECTED,
                'approval_state_stage_two' => enums::APPROVAL_STATUS_REJECTED,
                "state"                    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
            ];
        } else if (enums::APPROVAL_STATUS_CANCEL == $state) {
            $updateData = [
                "approval_state"           => enums::APPROVAL_STATUS_CANCEL,
                'approval_state_stage_two' => enums::APPROVAL_STATUS_CANCEL,
                "state"                    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
            ];
        } else {
            $updateData = [
                "approval_state"           => enums::APPROVAL_STATUS_TIMEOUT,
                'approval_state_stage_two' => enums::APPROVAL_STATUS_TIMEOUT,
                "state"                    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
            ];
        }
        $this->getDI()->get('db')->updateAsDict(
            'job_transfer',
            $updateData,
            'id = ' . $transferDetail->id
        );
    }

    /**
     * 立即转岗
     * @param array $params
     * @return array
     */
    public function doJobTransfer($params = []): array
    {
        $data       = $params['data'] ?? [];
        $manual     = $params['manual_operate'] ?? [];
        $operatorId = $params['operator_id'] ?? 10000;

        $staffObj = new StaffServer();
        $hcServer = new HcServer($this->lang, $this->timeZone);
        $vanContainerServer = new VanContainerServer($this->lang, $this->timeZone);
        $acknowledgeServer = $this->class_factory('JobTransferAcknowledgementServer', $this->lang, $this->timeZone);
        $jobTransferRepo = new JobtransferRepository($this->timeZone);

        //消息发送黑名单
        $blackList = $this->getBlackList();

        //获取配置
        $configList = (new SettingEnvServer())->listByCode(['job_title_vehicle_type', 'agent_job_transfer_message_staff_ids']);
        $configList = array_column($configList, 'set_val', 'code');

        //与车辆有关的职位
        $vehicleJobTitle = isset($configList['job_title_vehicle_type'])
            ? explode(CommonEnums::SEPARATOR_DEFAULT, $configList['job_title_vehicle_type'])
            : JobTransferEnums::TRANSFER_DEFAULT_JOB_TITLE;

        foreach ($data as $v) {
            if ($v['state'] == JobTransferModel::JOBTRANSFER_STATE_TRANSFERED) {
                continue;
            }
            if (in_array($v['after_hire_type'], HrStaffInfoModel::$agentTypeTogether)) { //个人代理给执行工号发送消息
                $hrBpList = isset($configList['agent_job_transfer_message_staff_ids'])
                    ? explode(CommonEnums::SEPARATOR_DEFAULT, $configList['agent_job_transfer_message_staff_ids'])
                    : [];
            } else {
                //获取hrBp
                $beforeHrBpList = ApprovalFinderServer::getInstance()->findHrBp($v['current_department_id'], $v['current_store_id']);
                $afterHrBpList  = ApprovalFinderServer::getInstance()->findHrBp($v['after_department_id'], $v['after_store_id']);
                $this->logger->write_log(sprintf('转岗前HrBP:%s,转岗后HrBP:%s', json_encode($beforeHrBpList), json_encode($afterHrBpList)), 'info');
                $hrBpList = array_merge($beforeHrBpList, $afterHrBpList);
                $hrBpList = array_values(array_filter(array_unique($hrBpList)));
            }

            //获取要发送消息的人的语言
            $submitterLanguage = $staffObj->getLanguage($v['submitter_id']);
            $transferLanguage  = $staffObj->getLanguage($v['staff_id']);
            $managerLanguage   = $staffObj->getLanguage($v['after_manager_id']);

            //如果转岗后上级离职，则重新查上级
            //https://flashexpress.feishu.cn/docx/G7KYdMULpoDprDxzF7GcS2wunag
            $afterManagerInfo = $staffObj->getStaffInfo(['staff_info_id' => $v['after_manager_id']]);

            //如没有编辑过上级，则重新获取上级
            if (!empty($afterManagerInfo) && $afterManagerInfo['state'] == HrStaffInfoModel::STATE_RESIGN) {
                $manager_id = $this->getafterManagerIdInfo($v['after_store_id'], $v['after_department_id'],
                    $v['after_position_id'], $v['after_date'], $v['staff_id']);
                if ($manager_id != $v["after_manager_id"]) {
                    $this->logger->write_log([
                        'do_transfer_manager' => [
                            'before' => $v["after_manager_id"],
                            'after'  => $manager_id,
                        ],
                    ], "info");
                    $v["after_manager_id"] = $manager_id;
                }
            }

            if (in_array($v['after_hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
                $sendMessageToTransferType = JobTransferMessageServer::SEND_MESSAGE_TO_AGENT_TRANSFER;
            } else {
                $sendMessageToTransferType = JobTransferMessageServer::SEND_MESSAGE_TO_TRANSFER;
            }

            //[1.1](仅立即转岗进来才校验)校验是否预算充足｜存在可用HC
            if ($manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER && $v['type'] != JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                $this->getDI()->get('db')->begin();
                [$currentHcId, $err] = $hcServer->occupyHc($v);
                $this->getDI()->get('db')->commit();
                if (!is_null($err) && $err->getCode() != ErrCode::SUCCESS) {
                    if ($err->getCode() == ErrCode::HC_BUDGET_EXHAUSTED) { //预算不足
                        $errMessageKey = 'job_transfer_budget_err';
                    } else if ($err->getCode() == ErrCode::ERR_NO_AVAILABLE_HC) {
                        $errMessageKey = 'job_transfer_not_valid_hc_err';
                    } else {
                        $errMessageKey = '4008';
                    }

                    //更新转岗状态为未转岗
                    $this->transferFailed($v['id'], $currentHcId, false);
                    $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-没有预算!(%s),不能转岗', $v['staff_id']), 'info');
                    $failure_reason = [
                        'zh-CN' => $this->getTranslationByLang('zh-CN')->t($errMessageKey),
                        'en' => $this->getTranslationByLang('en')->t($errMessageKey),
                        'th' => $this->getTranslationByLang('th')->t($errMessageKey),
                    ];
                    //记录操作日志
                    $this->saveOperateLog([
                        'id'             => $v['id'],
                        'staff_info_id'  => $operatorId,
                        'operate_id'     => JobTransferEnums::MANUAL_DO_JOB_TRANSFER,
                        'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                        'failure_reason' => json_encode($failure_reason),
                    ]);

                    //给申请人发送消息
                    JobTransferMessageServer::getInstance()->noticeTransferFail($v['submitter_id'],
                        $submitterLanguage,
                        $v,
                        $errMessageKey,
                        JobTransferMessageServer::SEND_MESSAGE_TO_SUBMITTER
                    );

                    //给被转岗人发送消息
                    JobTransferMessageServer::getInstance()->noticeTransferFail($v['staff_id'],
                        $transferLanguage,
                        $v,
                        $errMessageKey,
                        $sendMessageToTransferType
                    );

                    //给HrBP发送消息
                    foreach ($hrBpList as $item) {
                        if (in_array($item, $blackList)) {
                            continue;
                        }
                        JobTransferMessageServer::getInstance()->noticeTransferFail($item,
                            $staffObj->getLanguage($item),
                            $v,
                            $errMessageKey,
                            JobTransferMessageServer::SEND_MESSAGE_TO_HRBP
                        );
                    }
                    $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-有未回款项!(%s)转岗失败消息提醒发送完毕。', $v['staff_id']), 'info');
                    continue;
                }

                //更新hc
                $v['hc_id'] = $currentHcId;
            }

            //[1.2]校验是否有回款项；如果手里有未回款，不能转岗；
            $_re = $this->checkReceivableDetail([
                "staff_id" => $v['staff_id'],
                "store_id" => $v["current_store_id"],
            ]);
            if (!$_re) {
                //更新转岗状态为未转岗
                $this->transferFailed($v['id'], $v['hc_id']);
                $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-有未回款项!(%s)手里有未回款,不能转岗', $v['staff_id']), 'info');
                $failure_reason = [
                    'zh-CN' => $this->getTranslationByLang('zh-CN')->t('job_transfer_cod_err'),
                    'en' => $this->getTranslationByLang('en')->t('job_transfer_cod_err'),
                    'th' => $this->getTranslationByLang('th')->t('job_transfer_cod_err'),
                ];
                //记录操作日志
                $this->saveOperateLog([
                    'id'             => $v['id'],
                    'staff_info_id'  => $operatorId,
                    'operate_id'     => $manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        ? JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        : JobTransferEnums::SYSTEM_AUTO_DO_JOB_TRANSFER,
                    'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                    'failure_reason' => json_encode($failure_reason),
                ]);

                if ($v['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                    continue;
                }

                //给申请人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v['submitter_id'],
                    $submitterLanguage,
                    $v,
                    'job_transfer_cod_err',
                    JobTransferMessageServer::SEND_MESSAGE_TO_SUBMITTER
                );

                //给被转岗人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v['staff_id'],
                    $transferLanguage,
                    $v,
                    'job_transfer_cod_err',
                    $sendMessageToTransferType
                );

                //给HrBP发送消息
                foreach ($hrBpList as $item) {
                    if (in_array($item, $blackList)) {
                        continue;
                    }
                    JobTransferMessageServer::getInstance()->noticeTransferFail($item,
                        $staffObj->getLanguage($item),
                        $v,
                        'job_transfer_cod_err',
                        JobTransferMessageServer::SEND_MESSAGE_TO_HRBP
                    );
                }
                $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-有未回款项!(%s)转岗失败消息提醒发送完毕。', $v['staff_id']), 'info');
                continue;
            }
            //[1.3]校验工作是否完成；未完成任务（交接扫描但未妥投），不能转岗。
            $_re = $this->checkTicketDelivery([
                "staff_id" => $v["staff_id"],
            ]);
            if (!$_re) {
                //更新转岗状态为未转岗
                $this->transferFailed($v['id'], $v['hc_id']);
                $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-未完成任务!(%s)有未完成任务（交接扫描但未妥投）,不能转岗', $v['staff_id']), 'info');
                $failure_reason = [
                    'zh-CN' => $this->getTranslationByLang('zh-CN')->t('job_transfer_unfinish_task_err'),
                    'en' => $this->getTranslationByLang('en')->t('job_transfer_unfinish_task_err'),
                    'th' => $this->getTranslationByLang('th')->t('job_transfer_unfinish_task_err'),
                ];
                //记录操作日志
                $this->saveOperateLog([
                    'id'             => $v['id'],
                    'staff_info_id'  => $operatorId,
                    'operate_id'     => $manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        ? JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        : JobTransferEnums::SYSTEM_AUTO_DO_JOB_TRANSFER,
                    'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                    'failure_reason' => json_encode($failure_reason),
                ]);

                if ($v['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                    continue;
                }

                //给申请人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v["submitter_id"],
                    $submitterLanguage,
                    $v,
                    'job_transfer_unfinish_task_err',
                    JobTransferMessageServer::SEND_MESSAGE_TO_SUBMITTER
                );

                //给被转岗人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v["staff_id"],
                    $transferLanguage,
                    $v,
                    'job_transfer_unfinish_task_err',
                    $sendMessageToTransferType
                );

                //给HrBP发送消息
                foreach ($hrBpList as $item) {
                    if (in_array($item, $blackList)) {
                        continue;
                    }
                    JobTransferMessageServer::getInstance()->noticeTransferFail($item,
                        $staffObj->getLanguage($item),
                        $v,
                        'job_transfer_unfinish_task_err',
                        JobTransferMessageServer::SEND_MESSAGE_TO_HRBP
                    );
                }
                $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-未完成任务!(%s)转岗失败消息提醒发送完毕。', $v['staff_id']), 'info');
                continue;
            }
            //[1.4]校验网点是否营业中
            $_re = $this->checkStoreUseState([
                "store_id" => $v['after_store_id'],
            ]);
            if (!$_re) {
                //更新转岗状态为未转岗
                $this->transferFailed($v['id'], $v['hc_id']);
                $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-网点不存在或网点没营业!(%s),不能转岗', $v['staff_id']), 'info');
                $failure_reason = [
                    'zh-CN' => $this->getTranslationByLang('zh-CN')->t('job_transfer_store_use_state_task_err'),
                    'en' => $this->getTranslationByLang('en')->t('job_transfer_store_use_state_task_err'),
                    'th' => $this->getTranslationByLang('th')->t('job_transfer_store_use_state_task_err'),
                ];
                //记录操作日志
                $this->saveOperateLog([
                    'id'             => $v['id'],
                    'staff_info_id'  => $operatorId,
                    'operate_id'     => $manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        ? JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        : JobTransferEnums::SYSTEM_AUTO_DO_JOB_TRANSFER,
                    'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                    'failure_reason' => json_encode($failure_reason),
                ]);

                if ($v['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                    continue;
                }

                //给申请人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v["submitter_id"],
                    $submitterLanguage,
                    $v,
                    'job_transfer_store_use_state_task_err',
                    JobTransferMessageServer::SEND_MESSAGE_TO_SUBMITTER
                );

                //给被转岗人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v["staff_id"],
                    $transferLanguage,
                    $v,
                    'job_transfer_store_use_state_task_err',
                    $sendMessageToTransferType
                );

                //给HrBP发送消息
                foreach ($hrBpList as $item) {
                    if (in_array($item, $blackList)) {
                        continue;
                    }
                    JobTransferMessageServer::getInstance()->noticeTransferFail($item,
                        $staffObj->getLanguage($item),
                        $v,
                        'job_transfer_store_use_state_task_err',
                        JobTransferMessageServer::SEND_MESSAGE_TO_HRBP
                    );
                }
                $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-网点不存在或网点没营业!(%s)转岗失败消息提醒发送完毕。', $v['staff_id']), 'info');
                continue;
            }
            
            //[2]向FBI-HRIS同步转岗信息
            $positionCategory = [];
            $carType          = '';
            if (!empty($v['after_car_type'])) {
                $carType = JobTransferEnums::$car_type_map[$v['after_car_type']] ?? '';
            } else {
                if ($v["after_position_id"] == VehicleInfoEnums::JOB_BIKE_TITLE_ID) {
                    $carType = 'Bike';
                }
                if(in_array($v["after_position_id"], [
                    VehicleInfoEnums::JOB_VAN_TITLE_ID,
                    VehicleInfoEnums::JOB_VAN_FEEDER_TITLE_ID,
                    VehicleInfoEnums::JOB_COURIER_AND_INSTALLATION_STAFF_TITLE_ID,
                    VehicleInfoEnums::JOB_PICKUP_DRIVER,
                    VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID,
                    VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID,
                ])) {
                    $carType = 'Van';
                }
            }
            //转岗后车辆来源默认值规则
            //- 转岗后职位为【1930】、【1015】，默认车辆来源为租用公司车辆
            //- 转岗前职位为【1930】、【1015】且转岗后非前2个职位，车辆来源清空
            //- 其他情况下，默认车辆来源转岗前后不变
            if (!empty($v['car_owner'])) {
                $afterVehicleSource = $v['car_owner'];
                if (!empty($v['rental_car_cteated_at'])) {
                    $afterRentalCarCreatedAt = date('Y-m-d', strtotime($v['rental_car_cteated_at']));
                }
            } else {

                if (in_array($v['after_position_id'], [VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID, VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID])) {
                    $afterVehicleSource = VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE;
                } else if (
                    in_array($v['current_position_id'], [VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID, VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID]) &&
                    !in_array($v['after_position_id'], [VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID, VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID]) &&
                    in_array($v['after_position_id'], $vehicleJobTitle)
                ) {
                    $afterVehicleSource = 0;
                } else {
                    $afterVehicleSource = $v['vehicle_source'];
                }

                if ($afterVehicleSource == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
                    if (!empty($v['current_rental_car_created_at'])) { //默认用车开始时间，与转岗前一致
                        $afterRentalCarCreatedAt = date('Y-m-d', strtotime($v['current_rental_car_created_at']));
                    } else { //如转岗前没有用车开始时间，那么默认是实际转岗日期
                        $afterRentalCarCreatedAt = date('Y-m-d');
                    }
                }
            }

            //角色: 0-快递分配员 也是有效值
            if (isset($v['after_role_ids'])) {
                $positionCategory = is_string($v['after_role_ids']) && strlen($v['after_role_ids']) > 0
                    ? explode(',', $v['after_role_ids'])
                    : [];
            }
            $params = [
                "staff_info_id"         => $v["staff_id"],
                "department_id"         => $v["after_department_id"],
                "job_title"             => $v["after_position_id"],
                "sys_store_id"          => $v["after_store_id"],
                "operater"              => $v['submitter_id'],
                "direct_manager"        => $v["after_manager_id"],
                "position_category"     => $positionCategory,
                "car_type"              => $carType,
                "vehicle_source"        => $afterVehicleSource,
                "vehicle_use_date"      => $afterRentalCarCreatedAt ?? '',
                "working_day_rest_type" => $v['after_working_day_rest_type'] ?? 0,
                'type'                  => $v['type'],
                'job_title_grade'       => $v['after_job_title_grade'],
                'project_num'           => $v['after_project_num'],
                'hire_type'             => $v['after_hire_type'],
                'hire_times'            => $v['after_hire_times'],
            ];
            $this->logger->write_log("转岗参数,参数:" . json_encode($params),'info');
            $_re = $staffObj->syncHrStaff($params);
            if ($_re["result"]['code'] == 1) { //转岗成功

                //[3]更新HC剩余人数、HC招聘状态、转岗状态
                //更新转岗状态
                //更新转岗关联hc的剩余人数
                $this->getDI()->get('db')->begin();
                try {
                    if (empty($v['hc_id'])) {
                        $v['hc_id'] = $hcServer->systemAutoCreateHc($v);
                        $hcServer->deductHc($v['hc_id']);
                    }

                    //生成转岗确认单
                    if ($v['type'] != JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL && $v['confirm_state'] == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS) {
                        //暂时关闭TH生成确认单
                        //$confirmationSign = $acknowledgeServer
                        //    ->generateJobTransferPdf(['id' => $v['id'], 'staff_info_id' => $v['staff_id']]);
                    }

                    $updateParams = [
                        "actual_after_date"=> date('Y-m-d'),
                        "after_manager_id" => $v['after_manager_id'],
                        "state"            => JobTransferModel::JOBTRANSFER_STATE_TRANSFERED,
                        'hc_id'            => $v['hc_id'],
                        'confirmation_url' => $confirmationSign ?? '',
                    ];
                    if ($v['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                        $updateParams['confirm_state'] = JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM;
                    }
                    if (!empty($afterVehicleSource)) {
                        $updateParams['car_owner'] = $afterVehicleSource;
                    }
                    if (!empty($afterRentalCarCreatedAt)) {
                        $updateParams['rental_car_cteated_at'] = $afterRentalCarCreatedAt;
                    }

                    //更新转岗状态
                    $_re = $jobTransferRepo->updateJobtransfer([
                        "id"         => $v["id"],
                        "updateData" => $updateParams,
                    ]);
                    $this->getDI()->get('db')->commit();
                    $this->logger->write_log("转岗成功,修改转岗表:" . $_re,"info");
                } catch (\Exception $e) {
                    $this->logger->write_log("转岗失败,问题:" . $e->getMessage());
                    $this->getDI()->get('db')->rollback();
                    continue;
                }
                //实际转岗日期
                $v['actual_after_date'] = date('Y-m-d');

                $this->logger->write_log("转岗成功，工号:" . $v["staff_id"], "info");
                //记录操作日志
                $this->saveOperateLog([
                    'id'            => $v['id'],
                    'staff_info_id' => $operatorId,
                    'operate_id'     => $manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        ? JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        : JobTransferEnums::SYSTEM_AUTO_DO_JOB_TRANSFER,
                    'state'         => JobTransferModel::JOBTRANSFER_STATE_TRANSFERED,
                ]);

                //电子合同入队列
                if ($v['after_hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                    $data = ['type' => 'job_transfer', 'staff_info_id' => $v['staff_id'],'job_transfer_id' => $v['id']];
                    $rmq  = new RocketMQ('hr-contract-add');
                    $rid  = $rmq->sendToMsg($data);
                    $this->logger->write_log('hr-contract-transfer rid:' . $rid . 'data:' . json_encode($data), $rid ? 'info' : 'error');
                }

                // 转岗为Bike Courier或Van Courier或Van courier (Project)职位，给该员工发送车辆信息更新提醒
                if (in_array($v['after_position_id'], $vehicleJobTitle)) {
                    $staffObj->sendCourierTransferVehicleMsg($v['staff_id']);
                }

                //涉及到其他职位转岗到Van Courier[110]职位转岗成功后，提醒员工更新车辆信息
                //如果员工已在「特殊无车厢配置」且状态为“生效中”，则无需要发送此消息
                if ($v['after_hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID &&
                    $v['current_position_id'] != VehicleInfoEnums::JOB_VAN_TITLE_ID &&
                    $v['after_position_id'] == VehicleInfoEnums::JOB_VAN_TITLE_ID) {

                    $effectiveStatus = $vanContainerServer->getEffectiveStatusByStaffInfoId($v['staff_id']);
                    if (empty($effectiveStatus) || $effectiveStatus != FrontlineSpecialNonCarriageModel::EFFECTIVE_STATUS_EFFECT) {
                        JobTransferMessageServer::getInstance()->sendUpdateVehicleInfoMessage($v['staff_id']);
                    }
                }

                //[4]发送消息通知
                //给被转岗人发送消息(特殊转岗只给被转岗人发成功消息)
                JobTransferMessageServer::getInstance()->noticeTransferSuccess($v["staff_id"], $v, $transferLanguage,
                    $sendMessageToTransferType
                );

                if ($v['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                    continue;
                }
                //给申请人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferSuccess($v["submitter_id"], $v, $submitterLanguage);

                //给上级发送消息
                JobTransferMessageServer::getInstance()->noticeTransferSuccess($v["after_manager_id"],
                    $v, $managerLanguage,
                    JobTransferMessageServer::SEND_MESSAGE_TO_MANAGER
                );

            } else { //转岗失败
                //[3]更新转岗状态为未转岗
                $this->transferFailed($v['id'], $v['hc_id']);

                //[4]发送消息通知
                $this->logger->write_log("转岗失败，syncHrStaff返回:" . json_encode($_re["result"]),"info");
                $failure_reason = [
                    'zh-CN' => $_re["result"]['msg'] ?? '',
                    'en' => $_re["result"]['msg_en'] ?? '',
                    'th' => $_re["result"]['msg_th'] ?? '',
                ];
                //记录操作日志
                $this->saveOperateLog([
                    'id'             => $v['id'],
                    'staff_info_id'  => $operatorId,
                    'operate_id'     => $manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        ? JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        : JobTransferEnums::SYSTEM_AUTO_DO_JOB_TRANSFER,
                    'failure_reason' => json_encode($failure_reason),
                    'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                ]);

                if ($v['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                    continue;
                }

                //给申请人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v['submitter_id'],
                    $submitterLanguage,
                    $v,
                    $failure_reason[$submitterLanguage] ?? $failure_reason['en'],
                    JobTransferMessageServer::SEND_MESSAGE_TO_SUBMITTER
                );
                //给被转岗人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v['staff_id'],
                    $transferLanguage,
                    $v,
                    $failure_reason[$transferLanguage] ?? $failure_reason['en'],
                    $sendMessageToTransferType
                );

                //给HrBP发送消息
                foreach ($hrBpList as $item) {
                    if (in_array($item, $blackList)) {
                        continue;
                    }
                    $staffLanguage = $staffObj->getLanguage($item);
                    JobTransferMessageServer::getInstance()->noticeTransferFail($item,
                        $staffLanguage,
                        $v,
                        $failure_reason[$staffLanguage] ?? $failure_reason['en'],
                        JobTransferMessageServer::SEND_MESSAGE_TO_HRBP
                    );
                }
                $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-%s!(%s)转岗失败消息提醒发送完毕。',
                    $failure_reason['zh-CN'],
                    $v['staff_id']), 'info');
            }
        }
        return $this->checkReturn([]);
    }

    /**
     * 转岗失败
     *
     * 转岗HC加减规则
     *
     * 一、添加：
     *  1. 新增转岗(BY)
     *  2. 新增转岗(OA)
     *  3. 操作立即转岗HC人数不足，重新指定HC时
     * 二、扣减
     *  1. 审批驳回或撤销
     *  2. 转岗、立即转岗
     *
     *
     * @param $job_transfer_id
     * @param $hc_id
     * @param bool $is_remittance 是否返还
     * @return void
     */
    public function transferFailed($job_transfer_id, $hc_id, $is_remittance = true)
    {
        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            //获取一个可用的Hc

            //更新转岗状态为未转岗
            $jobTransferRepo = new JobtransferRepository($this->timeZone);
            $jobTransferRepo->updateJobtransfer([
                "id"         => $job_transfer_id,
                "updateData" => [
                    "state" => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR, //转岗失败
                ],
            ]);

            //立即转岗时，一线、非一线当前HC不可用，且无其他可用HC，无需返还当前绑定的、不可用的HC
            //如果存在HC则返还
            if (!empty($hc_id) && $is_remittance) {
                (new HcServer($this->lang, $this->timeZone))->remittanceHc($hc_id);
            }

            $db->commit();
            $this->logger->write_log("转岗失败,修改转岗表:" . $job_transfer_id, "info");
        } catch (\Exception $e) {
            $this->logger->write_log("转岗失败,问题:" . $e->getMessage() . $e->getTraceAsString(), "info");
            $db->rollback();
        }
    }

    /**
     * 校验转岗申请
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function checkJobTransferApply($paramIn = []): array
    {
        //[1]获取参数
        $submitterId     = $this->processingDefault($paramIn, 'submitter_id', 2);
        $data            = $this->processingDefault($paramIn, 'data', 3);
        $failArr         = [];
        $jobTransferRepo = new JobtransferRepository($this->timeZone);
        try {
            //[1]校验申请人权限
            $storeManagerInfo = SysStoreModel::find([
                'conditions' => 'manager_id = :manager_id:',
                'bind'       => ['manager_id' => $submitterId],
                'column'     => 'id,manager_id',
            ])->toArray();
            $regionMangerInfo = SysManageRegionModel::find([
                'conditions' => 'manager_id = :manager_id:',
                'bind'       => ['manager_id' => $submitterId],
                'column'     => 'id,manager_id',
            ])->toArray();
            $pieceMangerInfo = SysManagePieceModel::find([
                'conditions' => 'manager_id = :manager_id:',
                'bind'       => ['manager_id' => $submitterId],
                'column'     => 'id,manager_id',
            ])->toArray();
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => ['staff_info_id' => $submitterId],
                'column'     => 'id,node_department_id',
            ]);
            $submitterInfo = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :id:',
                'bind' => ['id' => $submitterId],
                'column' => 'sys_department_id',
            ]);
            $envModel = new SettingEnvServer();
            $hubManagementId = $envModel->getSetVal('dept_hub_management_id');
            $shopManagementId = $envModel->getSetVal('dept_shop_management_id');
            $networkManagementId = $envModel->getSetVal('dept_network_management_id');
            $networkPlanId = $envModel->getSetVal('dept_network_planning_id');
            $networkBulkyPlanId = $envModel->getSetVal('dept_network_bulky_planning_id');

            //获取 $networkPlanId  部门负责人
            $networkPlanId_manager_id = '';
            if (!empty($networkPlanId)) {
                $networkPlanId_manager = SysDepartmentModel::findFirst(
                    [
                        "conditions" => " id= :node_department_id:",
                        "bind"       => [
                            "node_department_id" => $networkPlanId,
                        ],
                        "columns"    => "id,ancestry_v3,manager_id",
                    ]
                );
                $networkPlanId_manager_id = $networkPlanId_manager ? $networkPlanId_manager->manager_id : $networkPlanId_manager_id;
            }
            //获取$BulkyPlanId  部门负责人
            $BulkyPlanId_manager_id = '';
            if(!empty($BulkyPlanId)){
                $BulkyPlanId_manager = SysDepartmentModel::findFirst(
                    [
                        "conditions" => " id= :node_department_id:",
                        "bind"       => [
                            "node_department_id" => $BulkyPlanId,
                        ],
                        "columns"    => "id,ancestry_v3,manager_id",
                    ]
                );
                $BulkyPlanId_manager_id = $BulkyPlanId_manager ? $BulkyPlanId_manager->manager_id : $BulkyPlanId_manager_id;
            }

            //检查申请人权限
            //shop只有AM可以申请
            //hub只有网点负责人可以申请
            //如果既不是AM、DM、网点负责人、不在Network Planning部门，也不可以申请
            if (isset($submitterInfo->sys_department_id) && $submitterInfo->sys_department_id == $shopManagementId && !(isset($regionMangerInfo) && $regionMangerInfo) ||
                isset($submitterInfo->sys_department_id) && $submitterInfo->sys_department_id == $networkManagementId  && !(isset($storeManagerInfo) && $storeManagerInfo) && !(isset($regionMangerInfo) && $regionMangerInfo) &&
                !(isset($pieceMangerInfo) && $pieceMangerInfo) && isset($staffInfo->node_department_id) &&
                !in_array($staffInfo->node_department_id, [$networkPlanId, $networkBulkyPlanId]) &&
                !in_array($submitterId, [$networkPlanId_manager_id, $BulkyPlanId_manager_id])

            ) {
                $failArr[] = [
                    'staff_id' => $submitterId,
                    'column' => "",
                    'reason' => $this->getTranslation()->_('jobtransfer_0004'),
                ];
            }
            //[2]校验申请数据
            if (!empty($data) && is_array($data)) {
                $t = $this->getTranslation();
                foreach ($data as $key => $value) {
                    //[2-1]校验员工信息
                    $staffInfo = $this->getStaffInfo([
                        "staff_id" => $value['staff_id'],
                    ]);
                    if (!$staffInfo) {
                        $failArr[] = [
                            'staff_id' => $value['staff_id'],
                            'column' => "staff_id",
                            'reason' => $this->getTranslation()->_('1001'),
                        ];
                        continue;
                    }
                    //[2-2]校验交接员工信息
                    //交接人需要在职、在编、非子账号
                    //交接人不能是转岗员工自己、需要在职
                    $jobHandoverStaffInfo = $this->getStaffInfo([
                        "staff_id" => $value['job_handover_staff_id'],
                    ]);
                    //如果不是本网点的员工
                    $jobHandoverStaffIds = [];
                    if(!isset($jobHandoverStaffInfo["store_id"]) || $jobHandoverStaffInfo["store_id"] != $staffInfo["store_id"]){
                        $jobHandoverStaffIds = $this->getJobHandoverStaffId($staffInfo);
                    }
                    if (!$jobHandoverStaffInfo || $jobHandoverStaffInfo["state"] != enums::$service_status['incumbency'] ||
                        $jobHandoverStaffInfo["formal"] != 1 || $jobHandoverStaffInfo["is_sub_staff"] != 0 ||
                        ($jobHandoverStaffInfo["store_id"] != $staffInfo["store_id"] && !in_array($value['job_handover_staff_id'],$jobHandoverStaffIds)  && $staffInfo['hr_staff_sys_store_id'] != '-1' ) ||
                        $value['job_handover_staff_id'] == $value['staff_id'])
                    {
                        $failArr[] = [
                            'staff_id' => $value['staff_id'],
                            'column' => "job_handover_staff_id",
                            'reason' => $this->getTranslation()->_('jobtransfer_0020'),
                        ];
                        continue;
                    }

	                //如果是总部网点员工    不是同一个部门   并且交接人 不是部门负责人
	                if($staffInfo['hr_staff_sys_store_id'] == '-1' && $jobHandoverStaffInfo['hr_staff_node_department_id'] != $staffInfo['hr_staff_node_department_id']){
		                //查询是否为部门负责人
		                $deptinfo = SysDepartmentModel::findFirst([
			                                                          'conditions' => "manager_id = :staff_info_id: and id = :current_department_id:",
			                                                          'bind' => [
				                                                          'staff_info_id'  =>  $value['job_handover_staff_id'],
				                                                          'current_department_id'       => $staffInfo['hr_staff_node_department_id'],
			                                                          ],
		                                                          ]);
		                if(empty($deptinfo)){
			                //工作交接人错误，请填写您部门内的员工
			                $failArr[] = [
				                'staff_id' => $value['staff_id'],
				                'column' => "job_handover_staff_id",
				                'reason' => $this->getTranslation()->_('jobtransfer_0050'),
			                ];
                            continue;
		                }

	                }

                    //[2-3]校验是否重复添加
                    $_re = $jobTransferRepo->getJobtransferInfo([
                        "staff_id" => $value['staff_id'],
                        "state"    => 1,
                    ]);
                    if ($_re) {
                        $failArr[] = [
                            'staff_id' => $value['staff_id'],
                            'column' => "staff_id",
                            'reason' => $this->getTranslation()->_('5202'),
                        ];
                        continue;
                    }
                    $_re = $jobTransferRepo->getJobtransferInfo([
                        "staff_id" => $value['staff_id'],
                    ]);
                    if (!empty($_re) && $_re['approval_state'] == enums::$audit_status['approved'] && $_re['state'] == enums::$job_transfer_state['to_be_transfered']) {
                        $failArr[] = [
                            'staff_id' => $value['staff_id'],
                            'column' => "staff_id",
                            'reason' => $this->getTranslation()->_('jobtransfer_0018'),
                        ];
                        continue;
                    }
                    //查询当前关联的HC
                    $hcInfo = HrHcModel::findFirst([
                        'conditions' => 'hc_id = :hc_id:',
                        'columns' => 'surplusnumber,demandnumber,expirationdate,hc_id',
                        'bind' => ['hc_id' => $value['hc_id']],
                    ]);
                    if (empty($hcInfo)) {
                        $failArr[] = [
                            'staff_id' => $value['staff_id'],
                            'column' => "HcId",
                            'reason' => $this->getTranslation()->_('jobtransfer_0024'),
                        ];
                        continue;
                    }
                }
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("checkJobTransferApply:" . $e->getMessage() . $e->getTraceAsString(), 'notice');
        }
        return $failArr ?? [];
    }

    /**
     * 校验转岗申请
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function checkApply($paramIn = []): array
    {
        $submitterId        = $paramIn['submitter_id'];
        $transferStaffInfo  = $paramIn['staff_id'];

        if (empty($transferStaffInfo)) {
            return [];
        }
        try {
            $params = [
                'submitter_id'      => $submitterId,
                'transfer_staff_id' => $transferStaffInfo,
            ];
            $transferValidateServer = $this->class_factory('JobTransferValidateServer', $this->lang, $this->timeZone);
            $transferValidateServer->init($params)->loadRules(get_class($transferValidateServer)::CHECK_RULES)->check();
        } catch (ValidationException $e) {
            $failArr = [
                'staff_id' => $transferStaffInfo,
                'column'   => "staff_id",
                'reason'   => $e->getMessage(),
            ];
        }
        return $failArr ?? [];
    }

    /**
     * 校验转岗申请
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function checkJobTransferApplyV2($paramIn = []): array
    {
        $submitterId        = $paramIn['submitter_id'];
        $transferStaffInfos = $paramIn['data'];
        $type               = $paramIn['type'];
        $budgetCheckList    = [];

        if (empty($transferStaffInfos)) {
            return [];
        }
        $hcServer               = new HcServer($this->lang, $this->timeZone);
        $transferValidateServer = $this->class_factory('JobTransferValidateServer', $this->lang, $this->timeZone);
        $className              = get_class($transferValidateServer);
        if ($type == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
            $checkRules = $className::SPECIAL_CHECK_RULES;
        } else {
            $checkRules = $className::CREATE_RULES;
        }

        $settingEnvPositionsList = [];
        $settingEnvPositions     = $this->getCannotApplyPositionIds();
        if (!empty($settingEnvPositions)) {
            $settingEnvPositionsList = JobTitleRepository::getJobTitleByIds($settingEnvPositions);
            $settingEnvPositionsList = array_column($settingEnvPositionsList, 'job_name', 'id');
        }

        foreach ($transferStaffInfos as $staffInfo) {
            try {

                $params                 = [
                    'submitter_id'          => $submitterId,
                    'transfer_staff_id'     => $staffInfo['staff_id'],
                    'after_date'            => $staffInfo['after_date'],
                    'after_store_id'        => $staffInfo['after_store_id'],
                    'job_handover_staff_id' => $staffInfo['job_handover_staff_id'] ?? 0,
                    'after_position_id'     => $staffInfo['after_position_id'],
                    'after_car_type'        => $staffInfo['after_car_type'] ?? 0,
                ];
                $transferValidateServer->init($params)->loadRules($checkRules)->check();

                //[3]获取转岗前数据
                $transferStaffInfo = $transferValidateServer->getJobTransferStaffInfo();

                if (isCountry('MY')) {
                    //转岗前后的职位在配置里，则无法提交申请，需走线下流程
                    $this->checkApplyPositions([$transferStaffInfo['job_title'], $staffInfo['after_position_id']], $settingEnvPositions, $settingEnvPositionsList);
                }

                //一线校验职级、预算
                if ($type != JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                    //校验职级
                    $this->calcJobTitleGrade($type,
                        $staffInfo['after_department_id'],
                        $staffInfo['after_position_id'],
                        $transferStaffInfo['job_title_grade_v2']);

                    //转岗后HC
                    $hcId = $hcServer->getValidTransferHc($transferStaffInfo['hire_type'],
                        $staffInfo['after_department_id'],
                        $staffInfo['after_store_id'],
                        $staffInfo['after_position_id'],
                        $staffInfo['after_date']);
                    if (empty($hcId)) {
                        $uniqueKey = $staffInfo['after_department_id'] . $staffInfo['after_position_id'];
                        $params    = [
                            'department_id' => $staffInfo['after_department_id'],
                            'store_id'      => $staffInfo['after_store_id'],
                            'job_title_id'  => $staffInfo['after_position_id'],
                        ];
                        $left = $hcServer->getHcReferences($params);
                        if (($left['data']['left'] - ($budgetCheckList[$uniqueKey] ?? 0)) <= 0) { // 该部门职位HC预算不足，请先申请HC预算
                            throw new ValidationException($this->getTranslation()->_('err_msg_hc_budget_exhausted'), ErrCode::HC_BUDGET_EXHAUSTED);
                        }
                        $budgetCheckList[$uniqueKey] = isset($budgetCheckList[$uniqueKey]) ? $budgetCheckList[$uniqueKey] + 1: 1;
                    }
                }
            } catch (ValidationException $e) {
                $failArr[] = [
                    'staff_id' => $staffInfo['staff_id'],
                    'column'   => "staff_id",
                    'reason'   => $e->getMessage(),
                ];
            }
        }
        return $failArr ?? [];
    }

    /**
     * 获取子审批流
     * @param array $paramIn
     * @return int
     */
    private function getWorkflowCode($paramIn = [])
    {
        $category = $paramIn['category'];
        $source = $paramIn['source'];
        $hr_staff_sys_department_id = $paramIn['hr_staff_sys_department_id'] ?? -1;// 部门
        $shopCategoryArr    = explode(",", $this->shopCategory);
        $hubCategoryArr     = explode(",", $this->hubCategory);
        $networkCategoryArr = explode(",", $this->networkCategory);
        $bhubCategoryArr = explode(",", $this->bhubCategory);

	    //获取部门
	    $firstDepartmentId = (new SettingEnvServer())->getSetVal('dept_hub_management_id');
	    //如果是 hub 部门
        if($hr_staff_sys_department_id == $firstDepartmentId){
	        $flowCode = $source == 2 ? 8 : 8;
        } else if (in_array($category, $hubCategoryArr)) {
            $flowCode = $source == 2 ? 6 : 3;
        } elseif (in_array($category, $networkCategoryArr)) {
            $flowCode = $source == 2 ? 4 : 1;
        } elseif (in_array($category, $shopCategoryArr)) {
            $flowCode = $source == 2 ? 5 : 2;
        } elseif(in_array($category, $bhubCategoryArr)){
            $flowCode = $source == 2 ? 7 : 4;
        }
        return $flowCode;
    }

    /**
     * @param $data
     * @param $state
     * @return bool
     */
    public function sendMail($data,$state=0)
    {
        $vehicleInfo=VehicleInfoModel::findFirst(
            [
                'conditions' => 'uid = :uid:',
                'bind' => ['uid' => $data['staff_id']],
            ]
        );
        if($vehicleInfo){
            $vehicleInfo=$vehicleInfo->toArray();
        }
        $this->getDI()->get('logger')->write_log("发送邮件前查询车辆信息 ".json_encode($vehicleInfo), 'info');
        //flag 0不发邮件；1新参与；2退出
        $flag=0;
        //1. 新参与
        //转岗前不是：职位Van Courier且车辆来源“租用公司车辆”——FBI-运营报表-员工车辆信息管理
        //转岗后是：职位Van Courier且车辆来源“租用公司车辆”——OA/BY-转岗审批信息
        //2. 退出
        //转岗前是：职位Van Courier且车辆来源“租用公司车辆”——FBI-运营报表-员工车辆信息管理
        //转岗后不是：职位Van Courier且车辆来源“租用公司车辆”——OA/BY-转岗审批信息
        if(trim($data['current_position_name'])!=='Van Courier'&&$data['vehicle_source']!==2&&trim($data['after_position_name'])=='Van Courier'&&isset($data['car_owner'])&&$data['car_owner']==2){
            $flag=1;
        }
        if(trim($data['current_position_name'])=='Van Courier'&&$data['vehicle_source']==2&&trim($data['after_position_name'])!=='Van Courier'&&isset($data['car_owner'])&&$data['car_owner']!==2){
            $flag=2;
        }
        $this->getDI()->get('logger')->write_log("flag 值".$flag, 'info');
        if(!$flag){
            return false;
        }
        $content = $this->getTemplate($data,$state,$flag);
        $mailaddress = SettingEnvModel::findFirst("code = 'notice_van_project_mail'");
        if(!empty($mailaddress)){
            $mailaddress = $mailaddress->set_val;
        }else{
            $mailaddress = "<EMAIL>";
        }
        $toUsers=$mailaddress;
        if ($toUsers && is_string($toUsers)) {
            $toUsers = explode(',', $toUsers);
        }
        $this->getDI()->get('logger')->write_log("发送邮件开始", 'info');
        $title = $this->getTranslation()->_('job_transfer_notice_mail_title');
        $ret   =  (new MailServer())->send_mail( $toUsers, $title, $content);
        $this->getDI()->get('logger')->write_log("发送邮件结束", 'info');
        $this->getDI()->get('logger')->write_log("send cc mail to:" . implode(',', $toUsers) .', content is:' .  $content.',result is:'. $ret, 'info');
        return true;
    }

    /**
     * @param $params
     * @param $state
     * @return array|string|string[]
     */
    private function getTemplate($params,$state,$flag)
    {
        $this->lang='th';
        //$state转岗状态 $flag 参加 退出
        if($state==1){
            $transfer_state=$this->getTranslation()->_('job_transfer_succ');
        }elseif ($state==2){
            $transfer_state=$this->getTranslation()->_('job_transfer_failulre');
        }else{
            $transfer_state='';
        }
        //审批通过参加
        if($state==0&&$flag==1){
            $content = str_replace([
                '{staff_id}',
                '{staff_name}',
                '{current_department_name}',
                '{current_store_name}',
                '{current_position_name}',
                '{after_department_name}',
                '{after_store_name}',
                '{after_position_name}',
                '{transfer_date}',
            ], [
                $params['staff_id'],
                $params['staff_name'],
                $params['current_department_name'],
                $params['current_store_name'],
                $params['current_position_name'],
                $params['after_department_name'],
                $params['after_store_name'],
                $params['after_position_name'],
                $params['after_date'],
            ], $this->getTranslation()->_('job_transfer_notice_mail_content1'));
        }
        //审批通过退出
        if($state==0&&$flag==2){
            $content = str_replace([
                '{staff_id}',
                '{staff_name}',
                '{current_department_name}',
                '{current_store_name}',
                '{current_position_name}',
                '{after_department_name}',
                '{after_store_name}',
                '{after_position_name}',
                '{transfer_date}',
            ], [
                $params['staff_id'],
                $params['staff_name'],
                $params['current_department_name'],
                $params['current_store_name'],
                $params['current_position_name'],
                $params['after_department_name'],
                $params['after_store_name'],
                $params['after_position_name'],
                $params['after_date'],
            ], $this->getTranslation()->_('job_transfer_notice_mail_content3'));
        }
        //参加
        if(($state==1||$state==2)&&$flag==1){
            $content = str_replace([
                '{staff_id}',
                '{staff_name}',
                '{current_department_name}',
                '{current_store_name}',
                '{current_position_name}',
                '{after_department_name}',
                '{after_store_name}',
                '{after_position_name}',
                '{transfer_date}',
                '{state}',
            ], [
                $params['staff_id'],
                $params['staff_name'],
                $params['current_department_name'],
                $params['current_store_name'],
                $params['current_position_name'],
                $params['after_department_name'],
                $params['after_store_name'],
                $params['after_position_name'],
                $params['after_date'],
                $transfer_state,
            ], $this->getTranslation()->_('job_transfer_notice_mail_content2'));
        }
        //退出
        if(($state==1||$state==2)&&$flag==2){
            $content = str_replace([
                '{staff_id}',
                '{staff_name}',
                '{current_department_name}',
                '{current_store_name}',
                '{current_position_name}',
                '{after_department_name}',
                '{after_store_name}',
                '{after_position_name}',
                '{transfer_date}',
                '{state}',
            ], [
                $params['staff_id'],
                $params['staff_name'],
                $params['current_department_name'],
                $params['current_store_name'],
                $params['current_position_name'],
                $params['after_department_name'],
                $params['after_store_name'],
                $params['after_position_name'],
                $params['after_date'],
                $transfer_state,
            ], $this->getTranslation()->_('job_transfer_notice_mail_content4'));
        }
        return $content;
    }

    /**
     * @description 获取转岗详细数据
     */
    public function getJobTransferInfo($paramIn = [])
    {
        $id   = $this->processingDefault($paramIn, 'id', 2);
        $data = JobTransferModel::findFirst($id);
        if (!$data) {
            return [];
        }
        $data = $data->toArray();

        $extendData = JobTransferExtendModel::findFirstByTransferId($id);
        if (!empty($extendData)) {
            $data['extend_id']           = $extendData['id'];
            $data['f_sr_id']             = $extendData['f_sr_id'];
            $data['salary_range_amount'] = $extendData['salary_range_amount'];
        }

        // 拓展数据
        $extendData = JobTransferExtendModel::findFirstByTransferId($data['id']);

        $sysObj = new SysListRepository();

        //获取提交人用户信息
        $staffServer = new StaffServer();
        $staffInfo = $staffServer->getStaffById($data['staff_id']);

        //查询部门名称
        $departmentIdsArr                = [$data["current_department_id"], $data["after_department_id"]];
        $departmentData                  = $sysObj->getDepartmentList([
            "ids" => $departmentIdsArr,
        ]);
        $departmentData                  = array_column($departmentData, 'name', 'id');
        $data["current_department_name"] = $departmentData[$data["current_department_id"]] ?? "";
        $data["after_department_name"]   = $departmentData[$data["after_department_id"]] ?? "";

        //查询职位名称
        $positionIdsArr                = [$data["current_position_id"], $data["after_position_id"]];
        $positionData                  = $sysObj->getPositionList([
            "ids" => $positionIdsArr,
        ]);

        $positionData                  = array_column($positionData, 'name', 'id');
        $data["current_position_name"] = $positionData[$data["current_position_id"]] ?? "";
        $data["after_position_name"]   = $positionData[$data["after_position_id"]] ?? "";

        //查询网点名称
        $storeIdsArr                = [$data["current_store_id"], $data["after_store_id"]];
        $storeData                  = $sysObj->getStoreList([
            "ids" => $storeIdsArr,
        ]);
        $storeData                  = array_column($storeData, 'name', 'id');
        $data["current_store_name"] = $storeData[$data["current_store_id"]] ?? '';
        $data["after_store_name"]   = $storeData[$data["after_store_id"]] ?? '';

        //转岗前大区、片区
        $storeInfo = (new SysStoreServer())->getStorePieceAndRegionInfo($data['current_store_id']);
        $data['current_piece_name'] = $storeInfo['piece_name'] ?? '';
        $data['current_region_name'] = $storeInfo['region_name'] ?? '';

        //查询员工名称
        $staffIds      = array_values(array_filter(array_unique([
            $data['staff_id'],
            $data['current_manager_id'],
            $data['current_indirect_manger_id'],
            $data['after_manager_id'],
            $data['job_handover_staff_id'],
        ])));
        $staffDataList = (new HrStaffInfoServer())->getUserInfoByStaffInfoIds($staffIds, 'staff_info_id,name');
        $staffData     = array_column($staffDataList, 'name', 'staff_info_id');

        //车辆类型
        $carType     = $this->getCarOwnerList();
        $carTypeName = array_column($carType, 'key', 'value');

        if (!empty($data['after_department_id'])) {
            $afterFirstDepartmentId = (new DepartmentRepository())->getSpecLevelDepartmentInChain($data['after_department_id']);
        }

        //转岗原因
        $transferReason                      = $this->getTotalTransferReason();
        $transferReason                      = array_column($transferReason, 'value', 'key');
        $data['transfer_reason_name']        = $transferReason[$data['transfer_reason']];
        $data['current_vehicle_source_name'] = $carTypeName[$data['vehicle_source']] ?? '';
        $data["staff_name"]                  = $staffData[$data["staff_id"]] ?? "";
        $data["current_manager_name"]        = $staffData[$data["current_manager_id"]] ?? "";
        $data["after_manager_name"]          = $staffData[$data["after_manager_id"]] ?? "";
        $data["job_handover_staff_name"]     = $staffData[$data["job_handover_staff_id"]] ?? "";
        $data['after_vehicle_source']        = $data['car_owner'];
        $data['after_vehicle_source_name']   = $carTypeName[$data['car_owner']] ?? '';
        $data['rental_car_created_at']       = $data['rental_car_cteated_at'] ?? '';
        $data['current_hire_type_name']      = $data['current_hire_type'] ? $this->getTranslation()->_('hire_type_' . $data['current_hire_type']) : '';
        $data['after_hire_type_name']        = $data['after_hire_type'] ? $this->getTranslation()->_('hire_type_' . $data['after_hire_type']) : '';
        $data['hire_date']                   = $staffInfo['hire_date'] ? date('Y-m-d',
            strtotime($staffInfo['hire_date'])) : '';
        $data['sys_department_id']           = $afterFirstDepartmentId ?? "";
        $data['contract_company_id']         = $staffInfo['contract_company_id'];
        $data['after_base_salary']           = $extendData['salary_range_amount'] ?? "";

        return $data;
    }

    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom): array
    {
        //获取转岗详情
        $result    = $this->getJobTransferInfo(['id' => $auditId]);
        $afterType = $this->getTransferType($result['after_department_id'], $result['after_position_id']);
        $auditRepo = new AuditlistRepository($this->lang, $this->timeZone);

        //获取配置日薪职位、月薪职位
        $employeesJobsConfigs = (new SettingEnvServer())->listByCode(['monthly_special_contract_employees_job', 'daily_special_contract_employees_job']);
        $employeesJobsConfigs = array_column($employeesJobsConfigs, 'set_val', 'code');
        $monthlyEmployeesJobsConfig = !empty($employeesJobsConfigs['monthly_special_contract_employees_job'])
            ? explode(',', $employeesJobsConfigs['monthly_special_contract_employees_job'])
            : [];

        $dailyEmployeesJobsConfig = !empty($employeesJobsConfigs['daily_special_contract_employees_job'])
            ? explode(',', $employeesJobsConfigs['daily_special_contract_employees_job'])
            : [];

        $headData = [
            'title'                         => $auditRepo->getAudityType(AuditListEnums::APPROVAL_TYPE_JT),
            'id'                            => $result['id'],
            'staff_id'                      => $result['submitter_id'],
            'transfer_staff_id'             => $result['staff_id'],
            'type'                          => AuditListEnums::APPROVAL_TYPE_JT,
            'created_at'                    => DateHelper::utcToLocal($result['created_at']),
            'updated_at'                    => DateHelper::utcToLocal($result['updated_at']),
            'status'                        => $result['state'],
            'status_text'                   => $auditRepo->getAuditState($result['state']),
            'serial_no'                     => $result['serial_no'] ?? '',
            'after_department_id'           => $result['after_department_id'],
            'after_job_title_id'            => $result['after_position_id'],
            'after_store_id'                => $result['after_store_id'],
            'hire_date'                     => $result['hire_date'],
            'after_type'                    => $afterType,
            'current_job_title_id'          => $result['current_position_id'] ?? 0,
            'current_vehicle_id'            => $result['vehicle_source'] ?? 0,
            'current_hire_type'             => $result['current_hire_type'],
            'after_hire_type'               => $result['after_hire_type'],
            'after_vehicle_id'              => $result['car_owner'] ?? 0,
            'monthly_special_contract_jobs' => $monthlyEmployeesJobsConfig,
            'daily_special_contract_jobs'   => $dailyEmployeesJobsConfig,
            'after_sys_department_id'       => $result['sys_department_id'],
        ];

        //如果存在可编辑字段，需返回默认参数
        $auditShowType  = $this->getAuditDetailRequest()->getAuditShowType();
        $auditStateType = $this->getAuditDetailRequest()->getAuditStateType();
        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);

        //审批状态为待审批 && 并且查看我的待审批 && 存在可编辑字段时，
        //返回当前节点的可编辑字段
        if ($result['approval_state'] == enums::APPROVAL_STATUS_PENDING &&
            $auditShowType . $auditStateType == '21' &&
            $approvalServer->isExistCanEditField($auditId, AuditListEnums::APPROVAL_TYPE_JT)
        ) {

            //默认参数
            $headData = array_merge($headData, $this->getDefaultParameter($result));

            //获取可编辑字段
            $canEditField = $approvalServer->getCanEditFieldColumns($auditId, AuditListEnums::APPROVAL_TYPE_JT,
                AuditDetailOperationsEnums::RESPONSE_STRUCTURE_COLUMN_NAME);

            //过滤可编辑字段
            $filterColumns = $this->getValidateFilterColumns($result);

            $canEditField = array_values(array_filter($canEditField, function ($v) use ($filterColumns) {
                return !in_array($v, $filterColumns);
            }));
        }

        //组织详情数据
        $returnData['data'] = $this->genTransferDetail($result);
        $returnData['data']['head'] = $headData;
        $returnData['data']['can_edit_field'] = $canEditField ?? [];

        return $returnData;
    }

    /**
     * 获取校验过滤字段
     * @param $transfer_info
     * @return array
     */
    public function getValidateFilterColumns($transfer_info): array
    {
        $filterColumns[] = 'car_type';

        //如果转岗后职位未非显示车辆来源的职位，则不显示车辆来源
        $vehicleJobTitle = explode(',',(new SettingEnvServer())->getSetVal('job_title_vehicle_type')) ? : JobTransferEnums::TRANSFER_DEFAULT_JOB_TITLE;

        if (in_array($transfer_info['after_position_id'], [VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID, VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID])) {
            $filterColumns[] = 'after_vehicle_source';

            if (!empty($transfer_info['rental_car_created_at'])) {
                $filterColumns[] = 'after_rental_car_created_at';
            }

        } else if (!in_array($transfer_info['after_position_id'], $vehicleJobTitle)) {
            $filterColumns[] = 'after_vehicle_source';

            if (!isset($transfer_info['after_vehicle_source']) || $transfer_info['after_vehicle_source'] != VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
                $filterColumns[] = 'after_rental_car_created_at';
            }
        }

        if ($this->getTransferType($transfer_info['after_department_id'], $transfer_info['after_position_id']) == JobTransferEnums::JOB_TRANSFER_TYPE_NOT_FRONT_LINE) {
            $filterColumns[] = 'salary_type';
        }

        if (!in_array($transfer_info['after_position_id'], [VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID, VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID])) {
            $filterColumns[] = 'project_num';
        }
        return $filterColumns;
    }

    /**
     * @description 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId): AuditOptionRule
    {
        return new AuditOptionRule(true,
            false,
            false,
            false,
            false,
            false);
    }

    /**
     * @description 自定义审批按钮
     * @param $auditId
     * @return int[]
     */
    public function customiseOptions($auditId): array
    {
        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);

        //获取是否存在可编辑字段
        //如存在，则返回可编辑字段的按钮ID
        //如不存在，则返回空数组，走默认逻辑（即：同意、驳回按钮）
        if ($approvalServer->isExistCanEditField($auditId, AuditListEnums::APPROVAL_TYPE_JT)) {
            $canEditField = $approvalServer->getCanEditFieldColumns($auditId, AuditListEnums::APPROVAL_TYPE_JT);

            return array_merge(AuditDetailOperationsEnums::BUTTON_COMMON_APPROVAL, $canEditField);
        } else {
            return [];
        }
    }

    /**
     * @description 组织转岗详情
     * @param $transfer_info
     * @return array
     */
    protected function genTransferDetail($transfer_info): array
    {
        //获取提交人用户信息
        $staffServer = new StaffServer();
        $staffInfo = $staffServer->get_staff($transfer_info['submitter_id']);
        if($staffInfo['data']){
            $staffInfo = $staffInfo['data'];
        }

        $transferInfo = $staffServer->get_staff($transfer_info['staff_id']);
        if($transferInfo['data']){
            $transferInfo = $transferInfo['data'];
        }

        //组织详情数据
        //[1]通用详情
        $detailLists = [
            'apply_parson'     => sprintf('%s ( %s )',$staffInfo['name'] ?? '' , $staffInfo['id'] ?? ''),
            'apply_department' => sprintf('%s - %s',$staffInfo['depart_name'] ?? '' , $staffInfo['job_name'] ?? ''),
            'transfer_parson'  => sprintf('%s ( %s )',$transferInfo['name'] ?? '' , $transferInfo['id'] ?? ''),
        ];
        //【正式员工】涉及车辆信息职位ID
        $vehicleJobTitle = explode(',',(new SettingEnvServer())->getSetVal('job_title_vehicle_type')) ? : JobTransferEnums::TRANSFER_DEFAULT_JOB_TITLE;

        //[2]转岗前/后详情
        if ($transfer_info['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE) {
            $beforeDetailList = [
                'job_transfer.before_department' => $transfer_info['current_department_name'],
                'job_transfer.before_store'      => $transfer_info['current_store_name'],
                'job_transfer.before_region'     => $transfer_info['current_region_name'],
                'job_transfer.before_piece'      => $transfer_info['current_piece_name'],
                'job_transfer.before_position'   => $transfer_info['current_position_name'],
                'job_transfer.before_hire_type'  => $transfer_info['current_hire_type_name'],
            ];
            if (in_array($transfer_info['current_hire_type'], [HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3, HrStaffInfoModel::HIRE_TYPE_4])) {
                $beforeDetailList['job_transfer.before_hire_times'] = $transfer_info['current_hire_times'] . $this->formatHireTimesUnit($transfer_info['current_hire_type']);
            }

            // 仅小黄车、小电车才展示用车开始时间
            if (!empty($transfer_info['current_rental_car_created_at']) && in_array($transfer_info['current_position_id'],
                    [VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID, VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID])) {
                $beforeDetailList['job_transfer.before_rental_car_created_at'] = date('Y-m-d', strtotime($transfer_info['current_rental_car_created_at']));
            }

            if (!empty($transfer_info['current_project_num'])) {
                $beforeDetailList['job_transfer.current_project_num'] = $this->generateProjectNum(['job_title' => $transfer_info['current_position_id']], ['project_num' => $transfer_info['current_project_num']]);
            }

            $afterDetailList = [
                'job_transfer.after_department' => $transfer_info['after_department_name'],
                'job_transfer.after_position'   => $transfer_info['after_position_name'],
                'job_transfer.after_store'      => $transfer_info['after_store_name'],
                'job_transfer.expect_date'      => date('Y-m-d', strtotime($transfer_info['after_date'])),
                'work_handover'                 => sprintf('%d %s', $transfer_info['job_handover_staff_id'],
                    $transfer_info['job_handover_staff_name']),
                'jobtransfer_0017'              => sprintf('%s %s', $transfer_info['transfer_reason_name'],
                    $transfer_info['reason']),
            ];

            //一线转岗，在申请时提交雇佣类型
            if ($this->isSHowHireTypeAtAfterInfo($transfer_info)) {
                $afterDetailList['job_transfer.after_hire_type'] = $transfer_info['after_hire_type_name'];
                if (in_array($transfer_info['after_hire_type'], [HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3])) {
                    $afterDetailList['job_transfer.after_hire_times'] = $transfer_info['after_hire_times'] . $this->formatHireTimesUnit($transfer_info['after_hire_type']);
                }
            }

            //指定工号填写项目期数
            if (!empty($transfer_info['after_project_num'])) {
                $afterDetailList['job_transfer.after_project_num'] = $this->generateProjectNum(['job_title' => $transfer_info['after_position_id']], ['project_num' => $transfer_info['after_project_num']]);
                if (!empty($transfer_info['rental_car_created_at'])) {
                    $afterDetailList['job_transfer.vehicle_start_date'] = date('Y-m-d', strtotime($transfer_info['rental_car_created_at']));
                }
            }
            if ($transfer_info['after_hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID) { //个人代理隐藏确认单
                $afterDetailList['job_transfer.confirmation'] = $this->getUploadFiles($transfer_info['id'], SysAttachmentEnums::OSS_TYPE_JOB_TRANSFER_CONFIRM_IMAGES);
            }

            //HRBP填写转岗后相关信息
            if (!empty($transfer_info['after_manager_id'])) {
                if (is_string($transfer_info['after_role_ids']) && strlen($transfer_info['after_role_ids']) > 0) {
                    [$rolesIds, $rolesNames] = $this->getStaffRoles($transfer_info['after_role_ids']);
                }
                $additionDetail = [
                    'job_transfer.manager_info'           => sprintf('%d(%s)', $transfer_info['after_manager_id'],
                        $transfer_info['after_manager_name']),
                    'job_transfer.role'                   => $rolesNames ?? '',
                    'job_transfer.working_days_rest_type' => $this->getTranslation()->_('working_day_rest_type_' . $transfer_info['after_working_day_rest_type']) ?? '',
                ];

                if (!$this->isSHowHireTypeAtAfterInfo($transfer_info)) {
                    $additionDetail['job_transfer.after_hire_type'] = $transfer_info['after_hire_type_name'];
                    if (in_array($transfer_info['after_hire_type'], [HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3])) {
                        $additionDetail['job_transfer.after_hire_times'] = $transfer_info['after_hire_times'] . $this->formatHireTimesUnit($transfer_info['after_hire_type']);
                    }
                }

                if ($transfer_info['after_hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID) { //个人代理隐藏上传文件
                    $additionDetail['job_transfer.upload_files'] = $this->getUploadFiles($transfer_info['id']);
                }
            }
        } else if ($transfer_info['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_NOT_FRONT_LINE) {
            $beforeDetailList = [
                'job_transfer.before_department' => $transfer_info['current_department_name'],
                'job_transfer.before_position'   => $transfer_info['current_position_name'],
                'job_transfer.before_store'      => $transfer_info['current_store_name'],
                'job_transfer.before_hire_type'  => $transfer_info['current_hire_type_name'],
            ];
            if (in_array($transfer_info['current_hire_type'], [HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3, HrStaffInfoModel::HIRE_TYPE_4])) {
                $beforeDetailList['job_transfer.before_hire_times'] = $transfer_info['current_hire_times'] . $this->formatHireTimesUnit($transfer_info['current_hire_type']);
            }
            if (!empty($transfer_info['current_project_num'])) {
                $beforeDetailList['job_transfer.current_project_num'] = $this->generateProjectNum(['job_title' => $transfer_info['current_position_id']], ['project_num' => $transfer_info['current_project_num']]);
            }

            if (!empty($transfer_info['hc_id'])) {
                $afterDetailList = [
                    'job_transfer.after_department' => $transfer_info['after_department_name'],
                    'job_transfer.after_position'   => $transfer_info['after_position_name'],
                    'job_transfer.after_store'      => $transfer_info['after_store_name'],
                    'job_transfer.expect_date'      => date('Y-m-d', strtotime($transfer_info['after_date'])),
                    'work_handover'                 => sprintf('%d %s', $transfer_info['job_handover_staff_id'],
                        $transfer_info['job_handover_staff_name']),
                    'jobtransfer_0017'              => sprintf('%s %s', $transfer_info['transfer_reason_name'],
                        $transfer_info['reason']),
                ];
            } else {
                $afterDetailList = [
                    'job_transfer.expect_date'      => date('Y-m-d', strtotime($transfer_info['after_date'])),
                    'work_handover'                 => sprintf('%d %s', $transfer_info['job_handover_staff_id'],
                        $transfer_info['job_handover_staff_name']),
                    'jobtransfer_0017'              => sprintf('%s %s', $transfer_info['transfer_reason_name'],
                        $transfer_info['reason']),
                ];
            }

            if (!empty($transfer_info['after_manager_id'])) {
                [$rolesIds, $rolesNames] = $this->getStaffRoles($transfer_info['after_role_ids']);
                $additionDetail = [
                    'job_transfer.manager_info'           => sprintf('%d(%s)', $transfer_info['after_manager_id'],
                        $transfer_info['after_manager_name']),
                    'job_transfer.role'                   => $rolesNames ?? '',
                    'job_transfer.working_days_rest_type' => $this->getTranslation()->_('working_day_rest_type_' . $transfer_info['after_working_day_rest_type']) ?? '',
                    'job_transfer.upload_files'           => $this->getUploadFiles($transfer_info['id']),
                    'job_transfer.after_hire_type'        => $transfer_info['after_hire_type_name'],
                ];
                if (in_array($transfer_info['after_hire_type'], [HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3])) {
                    $additionDetail['job_transfer.after_hire_times'] = $transfer_info['after_hire_times'] . $this->formatHireTimesUnit($transfer_info['after_hire_type']);
                }
                if (!empty($transfer_info['after_project_num'])) {
                    $additionDetail['job_transfer.after_project_num'] = $this->generateProjectNum(['job_title' => $transfer_info['after_position_id']], ['project_num' => $transfer_info['after_project_num']]);
                }
            }
        } else {
            $beforeDetailList = [];
            $afterDetailList  = [];
            $additionDetail   = [];
        }

        return [
            'detail'          => $this->format($detailLists),
            'before_detail'   => $this->format($beforeDetailList),
            'after_detail'    => $this->format($afterDetailList),
            'addition_detail' => !empty($additionDetail) ? $this->format($additionDetail) : [],
        ];
    }

    /**
     * @description:获取转岗后的直线上级
     * @param $after_store_id
     * @param int $after_demp_id
     * @param int $after_job_title
     * @param string $afterDate
     * @param int $transfer_staff_info_id
     * @return int|string
     * <AUTHOR> L.J
     * @time       : 2021/7/8 14:45
     */
	public function getafterManagerIdInfo($after_store_id,$after_demp_id=0, $after_job_title = 0, $afterDate = '', $transfer_staff_info_id = 0)
	{
        $afterManagerId = 0;
		try {
			// 2. 转岗后是总部员工
			//    1. 当转岗后部门有部门负责人时，转岗员工转至新部门后直线上级默认取新部门负责人
			//    2. 当转岗后部门没有负责人时，转岗员工转至新网点后直线上级取上级部门负责人
			if($after_store_id == '-1'){
                //查找部门负责人
                $departmentInfo = SysDepartmentModel::findFirst($after_demp_id);
                if ($departmentInfo) {
                    if (!empty($departmentInfo->manager_id) && $this->checkManager($departmentInfo->manager_id, $afterDate)) {
                        $afterManagerId = $departmentInfo->manager_id;
                    }
                    if (empty($afterManagerId)) {
                        //找上级部门
                        $departmentInfo = SysDepartmentModel::findFirst($departmentInfo->ancestry);
                        $afterManagerId = !empty($departmentInfo->manager_id) && $this->checkManager($departmentInfo->manager_id,
                            $afterDate)
                            ? $departmentInfo->manager_id
                            : $afterManagerId;
                    }
                }
			}else {
                $afterJobTitleConfig = (new SettingEnvServer())->getSetVal('after_transfer_job_title_config',',');

                //如果 CDC Supervisor【1290】 、Branch Supervisor【16】、Shop Supervisor 【101】 职位
                if(in_array($after_job_title, $afterJobTitleConfig)) {
                    $afterManagerId = $this->getOrgManager($after_demp_id, $after_store_id, $afterDate);
                } else {
                    //获取转岗后直线上级
                    $afterManagerId = $this->getJobtransferStoreManager([
                        "store_id" => $after_store_id,
                    ]);

                    if($this->checkManager($afterManagerId, $afterDate) && $afterManagerId != $transfer_staff_info_id) {
                        return $afterManagerId;
                    }
                    $afterOrgManagerId = $this->getOrgManager($after_demp_id, $after_store_id, $afterDate);
                    $afterManagerId = empty($afterOrgManagerId) ? $afterManagerId : $afterOrgManagerId;

                }
			}
		} catch (\Exception $e) {
			if ($e->getCode() == enums::$ERROR_CODE['1000']) {
				$this->getDI()->get('logger')->write_log('getafterManagerIdInfo:' . $e->getMessage() . $e->getTraceAsString(), 'error');
			}else{
				$this->getDI()->get('logger')->write_log('getafterManagerIdInfo:' . $e->getMessage() . $e->getTraceAsString());
			}
            return $afterManagerId;

        }
		
		return $afterManagerId;
	}

    /**
     * 检查 上级 是否是 待离职状态，如果是 离职日期 要晚于 员工转岗日期
     * @param $staffId
     * @param $after_date
     * @return int
     */
    public function checkManager($staffId, $after_date)
    {
        $managerInfo = (new JobtransferRepository($this->timeZone))->getBaseStaffInfo($staffId);
        if (empty($managerInfo)) {
            return 0;
        }

        if (in_array($managerInfo['state'], [HrStaffInfoModel::STATE_2, HrStaffInfoModel::STATE_SUSPENSION])) {
            return 0;
        }

        if ($managerInfo['state'] == HrStaffInfoModel::STATE_ON_JOB && $managerInfo['wait_leave_state'] != HrStaffInfoModel::WAITING_LEAVE) {
            return $staffId;
        }
        $leave_date = date('Y-m-d', strtotime($managerInfo['leave_date']));
        if (strtotime($leave_date) > strtotime($after_date)) {
            return $staffId;
        }
        //待离职状态，离职日期 小于 转岗日期
        $this->logger->write_log([
            'job_transfer_check_manager_id' => $staffId,
            'leave_date'                    => $leave_date,
            'after_date'                    => $after_date,
        ], 'info');
        return 0;
    }

    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return array
     */
    public function genSummary(int $auditId, $user): array
    {
        //获取转岗详情
        $osServer = new JobtransferRepository($this->timeZone);
        $info     = $osServer->getJobtransferInfo(['id' => $auditId]);

        //获取被转岗人姓名
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $info['staff_id'],
            ],
        ]);

        $sysServer = new SysListRepository();

        //查询网点名称
        $storeList = $sysServer->getStoreUnorderList(['ids' => $info['current_store_id']]);
        $storeList = array_column($storeList, 'name', 'id');

        //查询部门名称
        $departmentData = $sysServer->getDepartmentList([
            'ids' => $info["current_department_id"],
        ]);
        $departmentData = array_column($departmentData, 'name', 'id');

        //查询职位名称
        $positionData = $sysServer->getPositionList([
            "ids" => $info['current_position_id'],
        ]);
        $positionData = array_column($positionData, 'name', 'id');

        if (empty($info)) {
            return [];
        }
        return [
            [
                'key'   => "jobtransfer_0031", //转岗人
                'value' => sprintf("%s (%s)", $info['staff_id'], $staffInfo->name ?? ''),
            ],
            [
                'key'   => "jobtransfer_0010", //当前部门
                'value' => $departmentData[$info['current_department_id']] ?? '',
            ],

            [
                'key'   => "jobtransfer_0012", //当前职位
                'value' => $positionData[$info['current_position_id']] ?? '',
            ],
            [
                'key'   => "jobtransfer_0007", //当前网点
                'value' => $storeList[$info['current_store_id']] ?? '',
            ],
        ];
    }

    /**
     * 获取转岗后上级
     * @param $afterDepartId
     * @param $afterStoreId
     * @param $afterDate
     * @return int|string
     */
    public function getOrgManager($afterDepartId, $afterStoreId, $afterDate)
    {
        $model          = new FleSysDepartmentModel();
        $networkDeptIds = $model->getNetworkDepartmentIds();
        $shopDeptIds    = $model->getShopDepartmentIds();
        $hubDeptIds     = $model->getHubDepartmentIds();
        $bHubDeptIds    = $model->getBHubDepartmentIds();

        //1. NW一级部门及其子部门下：对应转岗后组织架构上网点对应的DM，没有DM或者DM离职，取AM，没有AM取所属部门负责人
        //一直没有取到一级部门负责人
        if (in_array($afterDepartId, $networkDeptIds)) {
            $manager = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getOrganizationRegionPieceManagerId($afterStoreId);
            //DM
            if (!empty($manager['piece_manager_id']) && $this->checkManager($manager['piece_manager_id'], $afterDate)) {
                return $manager['piece_manager_id'];
            }
            //AM
            if (!empty($manager['region_manager_id']) && $this->checkManager($manager['region_manager_id'], $afterDate)) {
                return $manager['region_manager_id'];
            }
            //查找 转岗后的 所属部门负责人
            $deptMangerId = $this->getDepartmentManger($afterDepartId);
            if ($this->checkManager($deptMangerId, $afterDate)) {
                return $deptMangerId;
            }

            //一级部门
            $firstDepartmentId = (new SysDepartmentServer())->searchSysDeptId($afterDepartId);
            // 找 所属部门 -》 所属部门的一级部门 区间内的 部门负责人
            $deptMangerId = $this->getGraduallyDeptManager($afterDepartId, $firstDepartmentId, $afterDate);
            if($deptMangerId) {
                return $deptMangerId;
            }

             //一级部门负责人不用验证 是否 待离职
            return $this->getDepartmentManger($firstDepartmentId);
        }

        //Retail(以前的shop)
        //对应转岗后组织架构上网点对应的AM，没有AM取所属部门负责人
        //一直没有取到一级部门负责人
        if (in_array($afterDepartId, $shopDeptIds)) {
            $manager = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getOrganizationRegionPieceManagerId($afterStoreId);
            //AM
            if (!empty($manager['region_manager_id']) && $this->checkManager($manager['region_manager_id'], $afterDate)) {
                return $manager['region_manager_id'];
            }
            //查找 转岗后的 所属部门负责人
            $deptMangerId = $this->getDepartmentManger($afterDepartId);
            if ($this->checkManager($deptMangerId, $afterDate)) {
                return $deptMangerId;
            }
            //一级部门
            $firstDepartmentId = (new SysDepartmentServer())->searchSysDeptId($afterDepartId);
            // 找 所属部门 -》 所属部门的一级部门 区间内的 部门负责人
            $deptMangerId = $this->getGraduallyDeptManager($afterDepartId, $firstDepartmentId, $afterDate);
            if($deptMangerId) {
                return $deptMangerId;
            }

            //一级部门负责人不用验证 是否 待离职
            $deptMangerId = $this->getDepartmentManger($firstDepartmentId);
            return $deptMangerId;
        }
        //HUB & Flash Freight Hub
        $hubDeptIds = array_merge($hubDeptIds, $bHubDeptIds);
        if (in_array($afterDepartId, $hubDeptIds)) {
            //查找 转岗后的 所属部门负责人
            $deptMangerId = $this->getDepartmentManger($afterDepartId);
            if ($this->checkManager($deptMangerId, $afterDate)) {
                return $deptMangerId;
            }
            //一级部门
            $firstDepartmentId = (new SysDepartmentServer())->searchSysDeptId($afterDepartId);
            // 找 所属部门 -》 所属部门的一级部门 区间内的 部门负责人
            $deptMangerId = $this->getGraduallyDeptManager($afterDepartId, $firstDepartmentId, $afterDate);
            if($deptMangerId) {
                return $deptMangerId;
            }

            //一级部门负责人不用验证 是否 待离职
            $deptMangerId = $this->getDepartmentManger($firstDepartmentId);
            return $deptMangerId;
        }

        return 0;
    }

    //查找部门负责人
    public function getDepartmentManger($departmentId)
    {
        if (empty($departmentId)) {
            return 0;
        }
        $departmentInfo = SysDepartmentModel::findFirst($departmentId);
        return empty($departmentInfo->manager_id) ? 0 : $departmentInfo->manager_id;
    }

    /**
     * 查找 部门 链上区间 部门负责人
     * @param $departmentId
     * @param $firstDepartment
     * @param $after_date
     * @return int
     */
    public function getGraduallyDeptManager($departmentId, $firstDepartment, $after_date)
    {
        $deptIds = [$departmentId, $firstDepartment];
        $list    = (new SysDepartmentServer())->getDepartmentByIds($deptIds);

        $firstDepartmentLink = explode('/', $list[$firstDepartment]['ancestry_v3']);
        $departmentLink      = explode('/', $list[$departmentId]['ancestry_v3']);

        $firstDepartmentLink[] = $departmentId;
        //部门链 倒序 排序
        $partDeptIds           = array_reverse(array_values(array_diff($departmentLink, $firstDepartmentLink)));
        if (empty($partDeptIds)) {
            return 0;
        }

        $partDeptIdsList = (new SysDepartmentServer())->getDepartmentByIds($partDeptIds);
        foreach ($partDeptIds as $oneDeptId) {
            if (empty($partDeptIdsList[$oneDeptId]['manager_id'])) {
                continue;
            }

            if ($this->checkManager($partDeptIdsList[$oneDeptId]['manager_id'], $after_date)) {
                return $partDeptIdsList[$oneDeptId]['manager_id'];
            }
        }

        return 0;
    }

    /**
     * 转岗获取网点下拉列表》》》》组织架构上部门关联的网点
     * @param $departmentId
     * @return array
     */
    public function getStoreListByDepartmentId($departmentId)
    {
        $returnData['data'] = [];
        if (empty($departmentId)) {
            return $returnData;
        }
        $departmentIds = (new SysDepartmentModel())->getSpecifiedDeptAndSubDept($departmentId);

        $storeIds = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getDepartmentStoreRelationInfo($departmentIds);

        $model          = new FleSysDepartmentModel();
        $hubDeptIds     = $model->getHubDepartmentIds();
        $bHubDeptIds    = $model->getBHubDepartmentIds();

        $hubDeptIds = array_merge($hubDeptIds, $bHubDeptIds);

        if (!empty($storeIds)) {
            //获取网点信息
            $data       = SysStoreModel::find([
                'conditions' => "id in ({store_ids:array})",
                'bind'       => [
                    'store_ids' => $storeIds,
                ],
                'columns'    => 'id, name',
            ])->toArray();

            $returnData['data'] = $data;
        }

        if (in_array($departmentId, $hubDeptIds)) {
            $returnData['data'][] = [
               'id' => enums::HEAD_OFFICE_ID,
               'name' => enums::HEAD_OFFICE,
            ];
        }

        return $returnData;
    }

    /**
     * @description 校验被转岗人是否符合转岗条件，如果符合，返回被转岗人相关信息
     * @param array $paramIn
     * @return array
     * @throws ValidationException
     */
    public function checkTransfer($paramIn = []): array
    {
        $transferStaffId = $paramIn['staff_id'];
        $submitterId     = $paramIn['staff_info_id'];

        //校验
        $params = [
            'submitter_id'      => $submitterId,
            'transfer_staff_id' => $transferStaffId,
        ];
        $transferValidateServer = $this->class_factory('JobTransferValidateServer', $this->lang, $this->timeZone);
        $transferValidateServer->init($params)->loadRules(get_class($transferValidateServer)::DEFAULT_RULES)->check();

        return $this->getJobTransferStaffInfo($transferValidateServer->getJobTransferStaffInfo(), $submitterId);
    }

    /**
     * @description 获取被转岗人信息
     * @param $transferStaffInfo
     * @param $submitterId
     * @return array
     */
    public function getJobTransferStaffInfo($transferStaffInfo, $submitterId): array
    {
        $storeId = $transferStaffInfo['store_id'];
        if (!empty($storeId)) { //获取被转岗人所在网点对应的大区、片区名
            $storeInfo = (new SysStoreServer())->getStorePieceAndRegionInfo($storeId);
        }

        //如果被转岗人是Van Courier、Bike Courier、Van Feeder
        //则获取 车辆类型、租用时间
        //与车辆有关的职位
        $vehicleJobTitle = explode(',',(new SettingEnvServer())->getSetVal('job_title_vehicle_type')) ? : JobTransferEnums::TRANSFER_DEFAULT_JOB_TITLE;
        if (in_array($transferStaffInfo['job_title'], $vehicleJobTitle)) {
            $vehicleInfo = $this->getVehicleDetailByStaffId($transferStaffInfo['staff_info_id']);
            if (!empty($vehicleInfo['vehicle_source'])) {
                $vehicleSourceKey = VehicleInfoEnums::VEHICLE_SOURCE_ITEM[(int)$vehicleInfo['vehicle_source']] ?? '';
                $vehicleInfo['vehicle_source_title'] = $this->getTranslation()->_($vehicleSourceKey);

                if ($vehicleInfo['vehicle_source'] != VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
                    unset($vehicleInfo['vehicle_start_date']);
                }
            }
        }
        $transferStaffInfo['hire_type_name'] = $this->getTranslation()->_('hire_type_' . $transferStaffInfo['hire_type']);
        $transferStaffInfo['project_num_title'] = $this->generateProjectNum($transferStaffInfo, $vehicleInfo ?? []);

        $result = [
            'staff_info' => array_merge($transferStaffInfo, $storeInfo ?? [], $vehicleInfo ?? []),
        ];

        //判断被转岗人是否为一线职位
        //被转岗人所属部门、职位在一线职位配置中
        $result['front_line'] = $this->getTransferType($transferStaffInfo['node_department_id'], $transferStaffInfo['job_title']);
        $result['submitter_upper_department_id'] = $this->getSubmitterDepartmentId($submitterId);

        return $result;
    }

    /**
     * @description 获取员工的上级部门ID
     * @param $staff_info_id
     * @return int
     */
    protected function getSubmitterDepartmentId($staff_info_id): int
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => [
                'staff_info_id' => $staff_info_id,
            ],
            'columns' => 'node_department_id',
        ]);
        if (empty($staffInfo)) {
            return 0;
        }
        $departmentInfo = SysDepartmentModel::findFirst($staffInfo->node_department_id);
        return $departmentInfo ?  (!empty($departmentInfo->ancestry) ? $departmentInfo->ancestry: 999) : 999;
    }

    /**
     * @description 转岗获取网点下拉列表
     * @param array $paramIn
     * @return mixed
     */
    public function getStoreList($paramIn = [])
    {
        $departmentId = $paramIn['department_id'] ?? '';
        $storeName    = $paramIn['store_name'] ?? '';

        $storeServer    = new SysStoreServer();
        $manageStoreIds = $storeServer->getManageStoresByDepartmentId($departmentId);
        if (empty($manageStoreIds) && isCountry(['TH'])) {
            $department_service = new SysDepartmentServer();
            $departmentHubInfo = $department_service->getChildrenListByDepartmentIdV2(enums::$department['Hub Management'], true);
            $departmentHubInfo[] = enums::$department['Hub Management'];
            if (in_array($departmentId, $departmentHubInfo)) {
                $manageStoreIds = $department_service->getRelevanceStoreByDepartmentId($departmentHubInfo);
            }
            $departmentFFMInfo = $department_service->getChildrenListByDepartmentIdV2(20001, true);
            $departmentFFMInfo[] = 20001;
            if (in_array($departmentId, $departmentFFMInfo)) {
                $manageStoreIds = $department_service->getRelevanceStoreByDepartmentId($departmentFFMInfo);
            }
        }

        //获取搜索的网点
        $storeList   = $storeServer->searchStoreListByName($storeName);
        $storeList   = array_column($storeList, null, 'id');
        $storeIdList = array_column($storeList, 'id');

        //取交集
        $intersection = array_values(array_intersect($manageStoreIds, $storeIdList));
        if (is_numeric(stripos(enums::HEAD_OFFICE, $storeName))) {
            $intersection[] = enums::HEAD_OFFICE_ID;
        }
        $intersection = array_values(array_slice($intersection, 0, CommonEnums::DEFAULT_LIST_SIZE));

        $result = array_map(function ($v) use ($storeList) {

            if ($v == enums::HEAD_OFFICE_ID) {
                return [
                    'key'   => enums::HEAD_OFFICE,
                    'value' => $v,
                ];
            } else {
                return [
                    'key'   => $storeList[$v]['name'],
                    'value' => $v,
                ];
            }
        }, array_values($intersection));

        $result = array_values($result);
        return array_sort($result, 'key', SORT_ASC);
    }

    /**
     * @description 获取一线职位配置
     * @return array
     */
    public function getFrontLineConfig(): array
    {
        $positionConfig = (new SettingEnvServer())->getSetValFromCache('job_transfer_front_line_position');
        return $this->parseConfig($positionConfig);
    }

    /**
     * @description 获取一线职位配置
     * @return array
     */
    public function getCrossGradeConfig(): array
    {
        $positionConfig = (new SettingEnvServer())->getSetValFromCache('job_transfer_position_across_grade');
        return $this->parseConfig($positionConfig);
    }

    /**
     * @description 获取不能申请转岗的职位
     * @return array
     */
    public function getCannotApplyPositionIds(): array
    {
        return (new SettingEnvServer())->getSetValFromCache('job_transfer_can_not_apply_position_ids', ',');
    }

    /**
     * 校验转岗前后职位是否能够提交申请
     * @param $positions
     * @param $settingEnvPositions
     * @param $settingEnvPositionsMap
     * @return void
     * @throws ValidationException
     */
    public function checkApplyPositions($positions, $settingEnvPositions, $settingEnvPositionsMap)
    {
        if (empty($settingEnvPositions)) {
            return;
        }
        $intersection = array_values(array_unique(array_intersect($positions, $settingEnvPositions)));
        if (!empty($intersection)) {
            $positionNames = array_map(function ($position) use ($settingEnvPositionsMap) {
                return $settingEnvPositionsMap[$position] ?? '';
            }, $intersection);
            $positionNames = join(', ', $positionNames);
            throw new ValidationException($this->getTranslation()->_('please_apply_transfer_offline', ['positions' => $positionNames]));
        }
    }

    /**
     * @description 解析配置
     * @param $config
     * @return array
     */
    private function parseConfig($config): array
    {
        //$list           = explode(',', $config);
        //foreach ($list as $item) {
        //    $v             = explode('|', $item);
        //    $result[$v[0]][] = $v[1];
        //}
        //return $result ?? [];
        return explode(',', $config);
    }

    /**
     * 设置审批条件参数
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null): array
    {
        //获取信息
        $transferInfo = JobTransferModel::findFirst($auditId);

        //被转岗人所属部门级别
        $departmentInfo = (new SysListRepository())->getDepartmentList(['ids' => [$transferInfo->current_department_id, $transferInfo->after_department_id], 'columns' => 'id,level']);
        $departmentInfo = array_column($departmentInfo, 'level', 'id');

        //是否为一线职位
        $beforeTransferType = $this->getTransferType($transferInfo->current_department_id, $transferInfo->current_position_id);
        $afterTransferType  = $this->getTransferType($transferInfo->after_department_id, $transferInfo->after_position_id);

        $parameters = [
            'before_department_id'          => $transferInfo->current_department_id,
            'before_position_is_front_line' => $beforeTransferType,
            'before_department_level'       => $departmentInfo[(int)$transferInfo->current_department_id],
            'after_department_id'           => $transferInfo->after_department_id,
            'after_position_is_front_line'  => $afterTransferType,
            'after_department_level'        => !empty($transferInfo->after_department_id)
                ? $departmentInfo[(int)$transferInfo->after_department_id] : 0,
            'is_same_region'                => $transferInfo->current_region_id == $transferInfo->after_region_id,
            'is_same_piece'                 => $transferInfo->current_piece_id == $transferInfo->after_piece_id,
            'is_same_department'            => $transferInfo->current_department_id == $transferInfo->after_department_id,
            'before_job_title_grade'        => $transferInfo->current_job_title_grade ?: 0,
            'after_job_title_grade'         => $transferInfo->after_job_title_grade ?: 0,
            'before_position_id'            => $transferInfo->current_position_id ?: 0,
            'after_position_id'             => $transferInfo->after_position_id ?: 0,
            'hire_type_from_submit'         => $transferInfo->current_hire_type,
        ];

        $this->logger->write_log("[job transfer][getWorkflowParams]=======:".json_encode($parameters), "info");

        return $parameters;
    }

    /**
     * @description
     * @param $auditId
     * @param $user
     * @param null $state
     * @return array
     */
    final public function getParseNodeParams($auditId, $user, $state = null): array
    {
        $transferInfo = JobTransferModel::findFirst($auditId);

        return [
            'from_submit' => [
                'department_id' => [$transferInfo->current_department_id, $transferInfo->after_department_id],
                'sys_store_id'  => [$transferInfo->current_store_id, $transferInfo->after_store_id],
            ],
        ];
    }

    /**
     * @description BP审核时，返回默认数值
     */
    protected function getDefaultParameter($paramIn = []): array
    {
        //转岗后上级
        $managerId = $this->getafterManagerIdInfo($paramIn['after_store_id'], $paramIn['after_department_id'],
            $paramIn['after_position_id'], $paramIn['after_date'], $paramIn['staff_id']);

        //获取转岗后的默认角色
        if ($paramIn['current_position_id'] == $paramIn['after_position_id'] &&
            is_string($paramIn['current_role_id']) &&
            strlen($paramIn['current_role_id']) > 0) {

            //转岗前部门、职位关联的角色
            $relateRoleIds    = (new HrStaffInfoServer())->rolesListRelation([
                'department_id' => $paramIn['current_department_id'],
                'position_id'   => $paramIn['current_position_id'],
            ]);
            $roles            = explode(',', $paramIn['current_role_id']);
            $intersectRoleIds = array_values(array_intersect($roles, $relateRoleIds));

            //当前角色
            [$roleIds, $roleName] = $this->getStaffRoles($intersectRoleIds);
        }

        //工作天数与轮休
        $workingDayRestType = $this->getWorkingDayRestType($paramIn['after_department_id'], $paramIn['after_position_id']);
        if (count($workingDayRestType) == 1) {
            $workingDayRestType = current($workingDayRestType);
            $afterWorkingDayRestType = $workingDayRestType['key'];
            $afterWorkingDayRestTypeName = $workingDayRestType['value'];
        }
        //上级姓名
        $staffDataList = (new HrStaffInfoServer())->getUserInfoByStaffInfoIds([$managerId], 'staff_info_id,name');
        $staffData     = array_column($staffDataList, 'name', 'staff_info_id');

        return [
            'after_manager_id'                 => $managerId,
            'after_manager_name'               => sprintf('%d (%s)', $managerId, $staffData[$managerId] ?? ''),
            'after_role_ids'                   => $roleIds ?? [],
            'after_role_name'                  => $roleName ?? '',
            'after_working_day_rest_type'      => $afterWorkingDayRestType ?? '',
            'after_working_day_rest_type_name' => $afterWorkingDayRestTypeName ?? '',
            'default_salary_type'              => JobTransferEnums::SALARY_TYPE_SALARY_STRUCTURE,
        ];
    }

    /**
     * @description 转岗获取职位下拉列表
     * @param $department_id
     * @param $job_title_id
     * @return array
     */
    public function getWorkingDayRestType($department_id, $job_title_id): array
    {
        if (empty($department_id) || empty($job_title_id)) {
            return [];
        }

        //获取部门职位关联的的JD
        $data = HrJobDepartmentRelationModel::findFirst([
            'conditions' => 'department_id = :department_id: and job_id = :job_id:',
            'bind'       => [
                'department_id' => $department_id,
                'job_id'        => $job_title_id,
            ],
        ]);
        if (empty($data)) {
            return [];
        }

        $returnData         = [];
        $workingDayRestType = explode(',', $data->working_day_rest_type);
        foreach ($workingDayRestType as $key => $value) {
            // 过滤自由轮休
            if ($value == HrStaffInfoModel::WEEK_WORKING_DAY_FREE . HrStaffInfoModel::REST_TYPE_1) {
                continue;
            }
            $returnData[] = [
                'key'   => $value,
                'value' => $this->getTranslation()->_('working_day_rest_type_' . $value),
            ];
        }
        return $returnData;
    }

    /**
     * @description 获取角色信息
     * @param $roles
     * @return array
     */
    public function getStaffRoles($roles): array
    {
        if (is_string($roles)) {
            $roleIds = explode(',', $roles);
        } else {
            $roleIds = $roles;
        }
        if (empty($roleIds)) {
            return [[], ''];
        }
        $roleTranslation = $this->getRolesTranslationFromCache();
        $roleInfo        = RolesModel::find([
            'columns'    => 'id,name,name_th,name_en',
            'conditions' => 'id in ({role_id:array}) and status = 1',
            'bind'       => [
                'role_id' => $roleIds,
            ],
        ])->toArray();
        $rolesList       = [];
        foreach ($roleInfo as $role) {
            $roleName    = $this->lang == 'zh-CN' ? ($roleTranslation[$role['id']]['role_name_zh'] ?? "")
                : ($this->lang == 'en' ? $roleTranslation[$role['id']]['role_name_en'] ?? "" : $roleTranslation[$role['id']]['role_name_th'] ?? '');
            $rolesList[] = $roleName;
        }
        $roleIds  = array_column($roleInfo, 'id');
        $roleName = is_array($rolesList) && !empty($rolesList) ? implode(',', $rolesList) : '';
        return [$roleIds, $roleName];
    }

    /**
     * @description 获取编辑下拉列表
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function getEditList($paramIn)
    {
        $auditId = $this->processingDefault($paramIn, 'audit_id');
        $transferDetail = JobTransferModel::findFirst($auditId);
        if (empty($transferDetail)) {
            throw new ValidationException('no valid data');
        }

        //指定部门、职位关联的角色
        $result['role_list'] = $this->getRelateRoles($paramIn['department_id'], $paramIn['job_title_id'], $transferDetail->after_store_id);

        //工作天数与轮休规则
        $result['working_day_rest_type'] = $this->getWorkingDayRestType($paramIn['department_id'], $paramIn['job_title_id']);

        //车辆来源
        $result['vehicle_source'] = $this->getCarOwnerList();

        //车类型
        //$result['car_type'] = $this->getVehicleType($auditId);

        //雇佣类型
        $result['hire_type'] = $this->getHireType();

        //薪资情况
        $result['salary_type'] = $this->getSalaryType();

        //转岗后小电车项目期数
        $result['project_num'] = $this->getProjectNumByJobTitle($paramIn['job_title_id']);

        return $this->checkReturn(['data' => $result]);
    }

    /**
     * @description 获取指定部门、职位关联的总部/网点角色
     * @param $department_id
     * @param $job_title_id
     * @param $store_id
     * @return array|mixed
     */
    public function getRelateRoles($department_id, $job_title_id, $store_id): array
    {
        //指定部门、职位关联的角色
        $ac = new ApiClient('hr_rpc', '', 'department_job_title_role_by_store', $this->lang);
        $ac->setParams(
            [
                'department_id' => $department_id,
                'job_title_id'  => $job_title_id,
                'sys_store_id'  => $store_id,
            ]
        );
        $return = $ac->execute();
        return $return["result"]['data'] ?? [];
    }

    /**
     * @description 转岗获取hc下拉列表
     */
    public function getHcListV2($paramIn = []): array
    {
        //获取参数
        $searchCondition = $this->processingDefault($paramIn, 'condition');

        $condition = 'state_code = 2 and reason_type = 2 and expirationdate >= :expiration_date: and surplusnumber > 0 and deleted = 1';
        $bind      = [
            'expiration_date' => date("Y-m-d", time()),
        ];
        if (!empty($searchCondition)) {
            $condition                .= ' and hc_id like :search_condition:';
            $bind['search_condition'] = $searchCondition . '%';
        }

        //获取指定网点和部门的、转岗类型的HC申请
        $data = HrHcModel::find([
            'conditions' => $condition,
            'bind'       => $bind,
            'columns'    => 'hc_id,department_id,job_title as job_title_id,worknode_id as store_id',
            'limit'      => 10,
            'order'      => 'hc_id asc',
        ])->toArray();

        //职位
        $sys = new SysListRepository();
        $jobTitleId = array_values(array_unique(array_column($data, 'job_title_id')));
        $jobTitleList = $sys->getPositionList(['ids' => $jobTitleId]);
        $jobTitleList = array_column($jobTitleList, 'name', 'id');

        //部门
        $departmentId = array_values(array_unique(array_column($data, 'department_id')));
        $departmentList = $sys->getDepartmentList(['ids' => $departmentId]);
        $departmentList = array_column($departmentList, 'name', 'id');

        foreach ($data as &$v) {
            $v['label'] = sprintf('%d - %s - %s', $v['hc_id'], $jobTitleList[$v['job_title_id']] ?? '', $departmentList[$v['department_id']] ?? '');
        }

        return ['data' => $data];
    }

    /**
     * 转岗获取hc下拉列表
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function getHcList($paramIn = [])
    {
        //[1]获取参数
        $departmentId = $paramIn["department_id"] ?? "";
        $storeId      = $paramIn["store_id"] ?? "";
        $jobTitle     = $paramIn["job_title_id"] ?? "";

        if (empty($paramIn["department_id"]) || empty($paramIn["store_id"]) || empty($paramIn['job_title_id'])) {
            return [];
        }

        //获取指定网点和部门的、转岗类型的HC申请
        $returnData = [];
        $add_hour   = $this->getDI()['config']['application']['add_hour'];
        $data       = HrHcModel::find([
            'conditions' => 'worknode_id = :store_id: and department_id = :department_id: and job_title = :job_title: and state_code = 2
                             and reason_type = 2 and expirationdate >= :expiration_date: and surplusnumber > 0  and deleted = 1',
            'bind'       => [
                'store_id'        => $storeId,
                'department_id'   => $departmentId,
                'job_title'       => $jobTitle,
                'expiration_date' => gmdate("Y-m-d", time() + $add_hour * 3600),
            ],
        ])->toArray();

        if ($data) {
            $returnData = [
                "data" => $data,
            ];
        }
        return $returnData;
    }

    /**
     * @description 搜索员工
     * @param $condition
     * @return mixed
     */
    public function searchStaff($condition)
    {
        return HrStaffInfoModel::find([
            'conditions' => '(staff_info_id like :name: or name like :name:) and state = 1 and formal = 1 and is_sub_staff = 0',
            'columns'    => 'staff_info_id,name',
            'bind'       => ['name' => $condition['search_condition'] . '%'],
            'limit'      => 10,
            'order'      => 'staff_info_id asc',
        ])->toArray();
    }

    /**
     * @description 获取转岗原因
     * @param $paramIn
     * @return array
     */
    public function getTransferReason($paramIn): array
    {
        $staffId = $paramIn['staff_id'];

        //获取是否一线职位
        $staffInfo = (new StaffServer())->getStaffInfoV2(['staff_info_id' => $staffId]);
        $transferType = $this->getTransferType($staffInfo['node_department_id'], $staffInfo['job_title']);
        $t = $this->getTranslation();

        if (JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE == $transferType) {
            $transferReason = [
                JobTransferModel::JOB_TRANSFER_REASON_STORE_INTEGRATION   => $t->_('job_transfer_reason.1'),
                JobTransferModel::JOB_TRANSFER_REASON_STORE_NEWLY_OPENED  => $t->_('job_transfer_reason.2'),
                JobTransferModel::JOB_TRANSFER_REASON_ADJUST_SERVICE_AREA => $t->_('job_transfer_reason.3'),
                JobTransferModel::JOB_TRANSFER_REASON_STORE_UPGRADE       => $t->_('job_transfer_reason.4'),
                JobTransferModel::JOB_TRANSFER_REASON_INSUFFICIENT_STAFF  => $t->_('job_transfer_reason.5'),
                JobTransferModel::JOB_TRANSFER_REASON_OTHERS              => $t->_('job_transfer_reason.99'),
            ];
        } else {
            $transferReason = [
                JobTransferModel::JOB_TRANSFER_REASON_CAREER_PLANNING          => $t->_('job_transfer_reason.6'),
                JobTransferModel::JOB_TRANSFER_REASON_OPERATIONAL_REQUIREMENTS => $t->_('job_transfer_reason.7'),
                JobTransferModel::JOB_TRANSFER_REASON_ORGANIZATION_RESTRUCTURE => $t->_('job_transfer_reason.8'),
                JobTransferModel::JOB_TRANSFER_REASON_OTHERS                   => $t->_('job_transfer_reason.99'),
            ];
        }

        foreach ($transferReason as $key => $value) {
            $data[] = [
                'key'   => $key,
                'value' => $value,
            ];
        }
        return $data;
    }

    /**
     * @description 获取部门树
     * @param $paramIn
     * @return array
     */
    public function getMyDepartmentTree($paramIn): array
    {
        $staffId = $this->processingDefault($paramIn, 'staff_id');
        $name    = $this->processingDefault($paramIn, 'name');

        //查询申请人部门
        $departments = SysDepartmentModel::find([
            'conditions' => 'deleted = 0',
            'columns'    => "id as value,name as label, ancestry",
        ])->toArray();
        if (empty($departments)) {
            return [];
        }
        $tree = $this->generateTree($departments);
        $res  = $this->recursionSetAncestry($tree);

        $result = [];
        if (is_string($name) && strlen($name) > 0) {
            $hcServer      = new HcServer($this->lang, $this->timeZone);
            $list          = $hcServer->convertRecursionToList($res);
            $departmentIds = array_column($list, 'value');
            $departments   = (new DepartmentRepository())->findDepartmentsByIdsAndName($name, $departmentIds);

            if ($departments) {
                $departmentIds = array_column($departments, 'id');
                $departmentMap = array_column($list, null, 'value');
                foreach ($departmentIds as $id) {
                    if ($departmentMap[$id]) {
                        $result[] = [
                            "value"         => $departmentMap[$id]['value'],
                            "label"         => $departmentMap[$id]['label'],
                            "ancestry"      => $departmentMap[$id]['ancestry'],
                            "ancestry_text" => $departmentMap[$id]['ancestry_text'],
                        ];
                    }
                }
            }
            return $result;
        }
        return $res;
    }

    /**
     * 生成树
     * @param $array
     * @return array
     */
    function generateTree($array): array
    {
        $items = [];
        foreach ($array as $value) {
            $items[$value['value']] = $value;
        }
        $tree = [];
        foreach ($items as $key => $value) {
            if (isset($items[$value['ancestry']])) {
                $items[$value['ancestry']]['children'][] = &$items[$key];
            } else {
                $tree[] = &$items[$key];
            }
        }
        return $tree;
    }

    public function recursionSetAncestry($tree, $higher = "")
    {
        foreach ($tree as $key => $val) {
            $val['ancestry_text'] = $higher ? "{$higher}-{$val['label']}" : $val['label'];
            if (!empty($val['children'])) {
                $val['children'] = $this->recursionSetAncestry($val['children'], $val['ancestry_text']);
            }
            $tree[$key] = $val;
        }
        return $tree;
    }

    /**
     * @description 获取员工车辆信息
     * @param $staff_info_id
     * @return array
     */
    public function getVehicleDetailByStaffId($staff_info_id): array
    {
        $vehicleInfo = VehicleInfoModel::findFirst(
            [
                'conditions' => 'uid = :uid:',
                'bind'       => ['uid' => $staff_info_id],
            ]
        );
        if ($vehicleInfo) {
            $vehicleInfo = $vehicleInfo->toArray();
        }
        return $vehicleInfo ? : [];
    }

    /**
     * @description 返回上传图片
     */
    public function getUploadFiles($transfer_id, $oss_bucket_type = SysAttachmentEnums::OSS_TYPE_JOB_TRANSFER_UPLOAD_FILES): array
    {
        $uploadImages = SysAttachmentModel::find([
            'conditions' => "oss_bucket_key = :oss_bucket_key: and oss_bucket_type = :oss_bucket_type: and deleted = 0",
            'bind' => [
                'oss_bucket_key'  => $transfer_id,
                'oss_bucket_type' => $oss_bucket_type,
            ],
            'columns' => 'bucket_name, object_key',
        ])->toArray();

        $result = [];
        foreach ($uploadImages as $image) {
            $result[] = convertImgUrl($image['bucket_name'], $image['object_key']);
        }
        return $result;
    }

    /**
     * @param $request
     * @return false
     * @throws ValidationException
     */
    public function isShowEndNode($request): bool
    {
        $info = JobTransferModel::findFirst($request->getBizValue());
        if (empty($info)) {
            return true;
        }

        $workflow = WorkflowModel::getWorkflowInfo($request->getWorkflowId());
        if (empty($workflow)) {
            return true;
        }

        //一阶段待审批不显示最终节点
        //待确认状态也不显示
        if ($info->approval_state == enums::APPROVAL_STATUS_PENDING && $info->approval_state_stage_one == enums::APPROVAL_STATUS_PENDING ||
            $info->confirm_state == JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM && $workflow->next_stage_flow_id != WorkflowModel::WORKFLOW_NEXT_STAGE_FLOW_ID
        ) {
            return false;
        }

        return true;
    }

    /**
     * 创建
     * @param array $paramIn
     * @param $submitter_id
     * @param $timezone
     * @param $uuid
     * @return array
     * @throws \Exception
     */
    public function create($paramIn = [], $submitter_id, $timezone, $uuid): array
    {
        $paramIn['submitter_id'] = $submitter_id;
        $paramIn['reason_type']  = $paramIn['transfer_reason'];
        return $this->addJobTransfer($paramIn);
    }

    /**
     * 获取车辆类型
     * @return array[]
     */
    public function getVehicleType($audit_id = 0): array
    {
        $vehicleType = [
            [
                'key'   => 'van',
                'value' => JobTransferEnums::CAR_TYPE_VAN,
            ],
            [
                'key'   => 'bike',
                'value' => JobTransferEnums::CAR_TYPE_BIKE,
            ],
        ];
        if (empty($audit_id)) {
            return $vehicleType;
        }

        //是Courier and Installation Staff、Pickup Driver(新增)时，
        //展示车辆类型字段，默认为van
        $transferInfo = JobTransferModel::findFirst($audit_id);
        if (in_array($transferInfo->after_position_id, [
            VehicleInfoEnums::JOB_COURIER_AND_INSTALLATION_STAFF_TITLE_ID,
            VehicleInfoEnums::JOB_PICKUP_DRIVER,
        ])) {
            return [
                [
                    'key'   => 'van',
                    'value' => JobTransferEnums::CAR_TYPE_VAN,
                ],
            ];
        }
        return $vehicleType;
    }

    /**
     * 消费创建转岗申请队列
     * @param $data
     * @param $submitter_id
     * @param $audit_type
     * @param $uuid
     * @param $apply_time
     * @return void
     */
    public function consumerProcessing($data, $submitter_id, $audit_type, $uuid, $apply_time)
    {
        if (empty($data)) {
            return;
        }

        //锁ID
        $redis    = $this->getDI()->get('redis');
        $redisKey = "batch_add_job_transfer_key" . $uuid;

        //错误消息
        $failMessage = [];
        foreach ($data as $k => $v) {
            //存在待转岗不能申请
            $info = JobTransferModel::findFirst([
                'conditions' => 'staff_id = :staff_id: and state = 1',
                'bind'       => [
                    'staff_id' => $v['staff_id'],
                ],
            ]);
            if (!empty($info)) {
                $this->logger->write_log("Batch Create Job Transfer failure 【存在待转岗】:" . json_encode($v),
                    "info");
               continue;
            }

            //创建转岗申请
            try {
                $this->logger->write_log("Batch Create Job Transfer key {$k}, value:" . json_encode($v),
                    "info");

                $ret = $this->create($v, $submitter_id, $apply_time, $uuid);
                $this->logger->write_log('ret result:' . json_encode($ret), 'info');

                //获取cache
                $cache = $redis->get($redisKey);
                $cache = json_decode($cache, true);

                if ($ret['code'] == ErrCode::SUCCESS) {
                    $cache['success'] = array_merge($cache['success'] ?? [], [$v['staff_id']]);
                    $this->logger->write_log('ret code == success, cache:' . json_encode($cache), 'info');

                } else {
                    $failMessage[] = [
                        'staff_id' => $v['staff_id'],
                        'fail_msg' => $ret['msg'],
                    ];
                    $cache['fail'] = array_merge($cache['fail'] ?? [], [$v['staff_id']]);
                    $cache['fail_msg'] = $failMessage;

                    $this->logger->write_log('ret code == fail, cache:' . json_encode($cache), 'info');
                }
                $redis->save($redisKey, json_encode($cache), 1800);

                $this->logger->write_log('create success.', 'info');

            } catch (\Exception $e) {
                $failMessage[] = [
                    'staff_id' => $v['staff_id'],
                    'fail_msg' => $e->getMessage(),
                ];
                $this->logger->write_log("Batch Create Job Transfer failure:" . json_encode($failMessage), "info");

                $cache['fail'] = array_merge($cache['fail'] ?? [], [$v['staff_id']]);
                $cache['fail_msg'] = $failMessage;
                $redis->save($redisKey, json_encode($cache), 1800);

                $this->logger->write_log('create fail.', 'info');
            }
        }
    }

    /**
     * @description 获取指定工号待审批或待转岗的数据
     * @param $staff_info_id
     * @return array
     */
    public function getJobTransferData($staff_info_id): array
    {
        //job_transfer表中拿到数据相关员工的转岗状态
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_id,id,after_department_id,after_store_id,after_position_id,approval_state,state');
        $builder->from(['r' => JobTransferModel::class]);
        $builder->where('deleted = 0');
        $builder->andWhere('staff_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        $builder->inWhere('approval_state', [enums::APPROVAL_STATUS_PENDING, enums::APPROVAL_STATUS_APPROVAL]);
        $builder->andWhere('state = :state:', ['state' => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED]);
        $info = $builder->getQuery()->getSingleResult();
        if (empty($info)) {
            return [];
        }
        return $info->toArray();
    }

    /**
     * 获取原因下拉列表
     * @Return array
     */
    public function getTotalTransferReason(): array
    {
        $t = $this->getTranslation();
        $list = [
            JobTransferModel::JOB_TRANSFER_REASON_STORE_INTEGRATION        => $t->_('job_transfer_reason.1'),
            JobTransferModel::JOB_TRANSFER_REASON_STORE_NEWLY_OPENED       => $t->_('job_transfer_reason.2'),
            JobTransferModel::JOB_TRANSFER_REASON_ADJUST_SERVICE_AREA      => $t->_('job_transfer_reason.3'),
            JobTransferModel::JOB_TRANSFER_REASON_STORE_UPGRADE            => $t->_('job_transfer_reason.4'),
            JobTransferModel::JOB_TRANSFER_REASON_INSUFFICIENT_STAFF       => $t->_('job_transfer_reason.5'),
            JobTransferModel::JOB_TRANSFER_REASON_CAREER_PLANNING          => $t->_('job_transfer_reason.6'),
            JobTransferModel::JOB_TRANSFER_REASON_OPERATIONAL_REQUIREMENTS => $t->_('job_transfer_reason.7'),
            JobTransferModel::JOB_TRANSFER_REASON_ORGANIZATION_RESTRUCTURE => $t->_('job_transfer_reason.8'),
            JobTransferModel::JOB_TRANSFER_REASON_OTHERS                   => $t->_('job_transfer_reason.99'),
        ];

        foreach ($list as $key => $value) {
            $data[] = [
                'key'   => $key,
                'value' => $value,
            ];
        }
        return $data;
    }

    /**
     * 获取消息黑名单列表
     * @return array
     */
    public function getBlackList()
    {
        $blackList = (new SettingEnvServer())->getSetVal('notice_black_list');

        return $blackList ? explode(',', $blackList) : [];
    }

    public function getHireType(): array
    {
        $hireTypeList = [
            [
                'label' => $this->getTranslation()->_('hire_type_1'),
                'value' => 1,
            ],
            [
                'label' => $this->getTranslation()->_('hire_type_2'),
                'value' => 2,
            ],
        ];
        if (isCountry()) {
            $hireTypeList[] = [
                'label' => $this->getTranslation()->_('hire_type_3'),
                'value' => 3,
            ];
        }
        return $hireTypeList;
    }

    /**
     * @return array[]
     */
    public function getSalaryType(): array
    {
        return [
            [
                'key'   => $this->getTranslation()->_('job_transfer.salary_type_1'),
                'value' => JobTransferEnums::SALARY_TYPE_NOT_CHANGE,
            ],
            [
                'key'   => $this->getTranslation()->_('job_transfer.salary_type_2'),
                'value' => JobTransferEnums::SALARY_TYPE_SALARY_STRUCTURE,
            ],
        ];
    }

    /**
     * 转岗-列表
     * @Access  public
     * @Param   array
     * @Return  bool
     */
    public function getJobtransferList($paramIn = [])
    {
        $data = (new JobtransferRepository($this->timeZone))->getJobtransferList($paramIn);
        if ($data) {
            $sysObj = new SysListRepository();
            //查询职位名称 ids
            $positionIdsArr = array_values(array_unique(array_merge(array_column($data, "current_position_id"),
                array_column($data, "after_position_id"))));
            $positionData   = $sysObj->getPositionList([
                "ids" => $positionIdsArr,
            ]);
            $positionData   = array_column($positionData, 'name', 'id');

            //查询网点名称 ids
            $storeIdsArr = array_values(array_unique(array_merge(array_column($data, "current_store_id"),
                array_column($data, "after_store_id"))));
            $storeData   = $sysObj->getStoreList([
                "ids" => $storeIdsArr,
            ]);
            $storeData   = array_column($storeData, 'name', 'id');

            //查询部门名称 ids
            $departmentIdsArr = array_values(array_unique(array_merge(array_column($data, "current_department_id"),
                array_column($data, "after_department_id"))));
            $departmentData   = $sysObj->getDepartmentList([
                "ids" => $departmentIdsArr,
            ]);
            $departmentData   = array_column($departmentData, 'name', 'id');

            //查询员工名称 ids
            $staffIdsArr = array_values(array_unique(array_merge(array_column($data, "staff_id"),
                array_column($data, "current_manager_id"), array_column($data, "current_indirect_manger_id"),
                array_column($data, "after_manager_id"))));
            $staffData = (new StaffServer())->getStaffInfoList($staffIdsArr);

            foreach ($data as $k => $v) {
                $data[$k]['staff_name']              = !empty($staffData[$v['staff_id']]) ? $staffData[$v["staff_id"]]['staff_name'] : "";
                $data[$k]['hire_type']               = !empty($staffData[$v['staff_id']]) ? $staffData[$v["staff_id"]]['hire_type'] : "";
                $data[$k]['current_department_name'] = $departmentData[$v["current_department_id"]] ?? "";
                $data[$k]["after_department_name"]   = $departmentData[$v["after_department_id"]] ?? "";
                $data[$k]["current_store_name"]      = $storeData[$v["current_store_id"]] ?? "";
                $data[$k]["after_store_name"]        = $storeData[$v["after_store_id"]] ?? "";
                $data[$k]["current_position_name"]   = $positionData[$v["current_position_id"]] ?? "";
                $data[$k]["after_position_name"]     = $positionData[$v["after_position_id"]] ?? "";
            }
        }
        return $data;
    }

    /**
     * 转岗校验回款
     * @Access  public
     * @Param   array
     * @Return  bool
     */
    public function checkReceivableDetail($paramIn = [])
    {
        $param["staff_id"] = $paramIn["staff_id"] ?? "";
        $param["store_id"] = $paramIn["store_id"] ?? "";
        $param["state"]    = "0";

        $setting_code  = 1;
        $setting_model = SettingEnvModel::findFirst("code = 'hris_validate_store_receivable_bill_detail'");
        if (!empty($setting_model)) {
            $setting_code = $setting_model->set_val;
        }
        if ($setting_code == 1) {
            $data = (new JobtransferRepository($this->timeZone))->getReceivableDetail($param);
            if ($data) {
                return false;
            }
        }

        return true;
    }

    /**
     * 转岗校验工作是否完成
     * @Access  public
     * @Param   array
     * @Return  bool
     */
    public function checkTicketDelivery($paramIn = [])
    {
        $param["staff_id"] = $paramIn["staff_id"] ?? "";
        $param["state"]    = 0;

        $setting_code  = 1;
        $setting_model = SettingEnvModel::findFirst("code = 'hris_validate_ticket_delivery'");
        if (!empty($setting_model)) {
            $setting_code = $setting_model->set_val;
        }
        if ($setting_code == 1) {
            $data = (new JobtransferRepository($this->timeZone))->getTicketDelivery($param);
            if ($data) {
                return false;
            }
        }

        return true;
    }
    
    /**
     * 校验转岗网点营业中状态
     * @param $paramIn
     * @return bool
     */
    public function checkStoreUseState($paramIn = []): bool
    {
        if (
            !isCountry('TH') ||
            (!empty( $paramIn["store_id"]) &&  $paramIn["store_id"] == enums::HEAD_OFFICE_ID)
        ){
            return true;
        }
        if (empty($paramIn["store_id"])){
            return true;
        }
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => "id = :store_id:",
            'bind'       => [
                'store_id' => $paramIn["store_id"],
            ],
        ]);
        if (empty($storeInfo) || $storeInfo->use_state != SysStoreModel::USE_STATE_YES){
            return false;
        }
        return true;
    }

    /**
     * 保存操作记录
     * @param array $paramIn
     * operate_id: 1-立即转岗 2-系统自动转岗 3-修改日期 4-修改hc
     * @return bool
     */
    public function saveOperateLog($paramIn = []): bool
    {
        $jobTransferId = $paramIn['id'];
        $staffId       = $paramIn['staff_info_id'];
        $operateId     = $paramIn['operate_id'];
        $state         = $paramIn['state'];
        $failureReason = $paramIn['failure_reason'] ?? '';
        $content       = $paramIn['content'] ?? '';

        //获取操作人信息
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $staffId],
        ]);

        if (isset($staffInfo->node_department_id) && $staffInfo->node_department_id) {
            $deptInfo = SysDepartmentModel::findFirst([
                'conditions' => "id = :department_id:",
                'bind'       => [
                    'department_id' => $staffInfo->node_department_id,
                ],
                'columns'    => "id,name",
            ]);
        }

        if (isset($staffInfo->job_title) && $staffInfo->job_title) {
            $jobInfo = HrJobTitleModel::findFirst($staffInfo->job_title);
        }

        $log = new JobTransferOperateLogModel();
        $log->setPid($jobTransferId);
        $log->setStafInfoId($staffId);
        $log->setStaffName($staffInfo->name ?? '');
        $log->setDepartmentId($staffInfo->node_department_id ?? null);
        $log->setDepartmentName($deptInfo->name ?? '');
        $log->setOperateId($operateId);
        $log->setPostionId($staffInfo->job_title ?? null);
        $log->setPositionName($jobInfo->job_name ?? '');
        $log->setState($state);
        $log->setFailureReason($failureReason);
        $log->setOperateContent($content);
        $log->save();

        return true;
    }

    /**
     * 获取批量添加结果
     * @param $params
     * @return array
     */
    public function getBatchAddProgress($params): array
    {
        $batchNumber = $params['batch_number'] ?? '';
        $redis_obj   = $this->getDI()->get('redis');
        $cacheKey    = 'batch_add_job_transfer_key'.$batchNumber;

        //获取批量添加进程
        $cache = $redis_obj->get($cacheKey);
        if (empty($cache)) {
            $this->getDI()->get('logger')->write_log("getBatchAddProgress batch num is :".$batchNumber, 'info');
            return [
                'code'    => -1,
                'message' => 'invalid batch number', //无效的批次号
            ];
        }

        $res = isset($cache) && $cache ? json_decode($cache, true) : [];
        if (empty($res) || isset($res['total']) && empty($res['total'])) {
            return [
                'success'      => '',
                'fail'         => '',
                'fail_message' => [
                ],
            ];
        }

        return [
            'success'      => $res['success'] ?? [],
            'fail'         => $res['fail'] ?? [],
            'fail_message' => $res['fail_msg'] ?? [],
        ];
    }

    /**
     * 获取转岗流程
     * @param array $paramIn
     * @return array
     */
    public function getJobTransferOperateLog($paramIn = []): array
    {
        $jobTransferId = $paramIn['id'];
        if (empty($jobTransferId)) {
            return [];
        }

        $logData = JobTransferOperateLogModel::find([
            'conditions' => 'pid = :job_transfer_id:',
            'bind'       => [
                'job_transfer_id' => $jobTransferId,
            ],
            'order'      => "id desc",
        ])->toArray();

        $t = $this->getTranslation();
        foreach ($logData as &$v) {
            $failure_reason       = json_decode($v['failure_reason'], true);
            $v['state_title']     = isset($v['state']) && $v['state'] ? $t->_("job_transfer_state." . $v['state']) : "";
            $v['operate_title']   = $t->_("job_transfer_operate." . $v['operate_id']);
            $v['failure_reason']  = $failure_reason ? $failure_reason[$this->lang] : $v['failure_reason'];
            $v['operate_content'] = $this->getOperateContent($v);
            $v['failure_reason']  = $v['operate_id'] == JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_ROLE ? '' : $v['failure_reason'];
            $v['operate_content'] = $v['operate_title'] . " " . $v['operate_content'];
        }

        return $logData;
    }

    /**
     * @param $data
     * @return void
     */
    private function getOperateContent($data)
    {
        $operateId      = $data['operate_id'];
        $failureReason  = $data['failure_reason'];
        $operateContent = $data['operate_content'];
        switch ($operateId) {
            case JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_ROLE:
                $content = $failureReason;
                break;
            case JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_TRANSFER_INFO:
                $content = $this->reverseContent($operateContent);
                break;
            default:
                $content = $operateContent;
                break;
        }
        return $content;
    }

    private function reverseContent($content)
    {
        $contentArr = json_decode($content, true);
        if (empty($contentArr)) {
            return '';
        }
        $roleInfo = [];
        $columns  = array_column($contentArr, 'column');
        if (in_array('after_role_ids', $columns)) {
            $roleInfo = RolesModel::find(['columns' => "id,name as name_zh,name_th,name_en"])->toArray();
            $roleInfo = array_column($roleInfo, null, 'id');
        }
        $responseContent = [];
        foreach ($contentArr as $value) {
            $translationKey = JobTransferEnums::$change_column_translation_key_map[$value['column']] ?? '';
            $columnName     = $this->getTranslation()->_($translationKey);

            if ($value['column'] == 'after_role_ids') {
                $before = $this->genRoleName($value['before'], $roleInfo);
                $after  = $this->genRoleName($value['after'], $roleInfo);
            } else {
                if ($value['column'] == 'after_working_day_rest_type') {
                    $before = $this->getTranslation()->_('working_day_rest_type_' . $value['before']);
                    $after  = $this->getTranslation()->_('working_day_rest_type_' . $value['after']);
                } else {
                    $before = $value['before'];
                    $after  = $value['after'];
                }
            }
            $responseContent[] = sprintf('%s:%s ==> %s', $columnName, $before, $after);
        }
        return join('; ', $responseContent);
    }

    private function genRoleName($data, $roleInfo)
    {
        $result = is_string($data) && strlen($data) > 0
            ? array_map(function ($v) use ($roleInfo) {
                if ($this->lang == 'zh-CN') {
                    return $roleInfo[$v]['name_zh'];
                } else {
                    if ($this->lang == 'th') {
                        return $roleInfo[$v]['name_th'];
                    } else {
                        return $roleInfo[$v]['name_en'];
                    }
                }
            }, explode(',', $data))
            : '';
        return !empty($result) ? join(',', $result): '';
    }

    /**
     * 编辑转岗信息
     * @param $paramIn
     * @return array
     * @throws \Exception
     */
    public function editJobTransferInfo($paramIn = []): array
    {
        if (!in_array($paramIn['type'], [
            JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_TRANSFER_DATE,
            JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_HC_ID,
            JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_ROLE,
            JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_TRANSFER_INFO,
        ])) {
            throw new ValidationException('invalid type');
        }

        switch ($paramIn['type']) {
            case JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_TRANSFER_DATE: //更改转岗日期
                $data = $this->updateJobTransferDate($paramIn);
                break;
            case JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_HC_ID: //更改HcID
                $data = $this->updateJobTransferHcId($paramIn);
                break;
            case JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_ROLE: //更改角色和上级
                $data = $this->updateJobTransferStore($paramIn);
                break;
            case JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_TRANSFER_INFO: //更改员工信息
                $data = $this->editJobTransferData($paramIn);
                break;
            default:
                $data = [];
                break;
        }

        return $data;
    }

    /**
     * 更新转岗日期
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function updateJobTransferDate($paramIn = []): array
    {
        $jobTransferId = $paramIn['id'] ?? '';
        $staffId       = $paramIn['staff_info_id'] ?? '';
        $afterDate     = $paramIn['after_date'] ?? '';

        if (empty($jobTransferId)) {
            throw new \Exception('no valid job transfer request');
        }

        //获取转岗详情
        $jobTransferInfo = JobTransferModel::findFirst($jobTransferId);

        $this->db = $this->getDI()->get("db");
        $this->db->begin();
        $this->db->updateAsDict("job_transfer",
            [
                'after_date' => $afterDate,
            ],
            [
                'conditions' => "id = ?",
                'bind'       => [
                    $jobTransferId,
                ],
            ]
        );

        $this->saveOperateLog([
            'id'            => $jobTransferId,
            'staff_info_id' => $staffId,
            'operate_id'    => 3, // 1-立即转岗 2-系统自动转岗 3-修改日期 4-修改hc
            'state'         => $jobTransferInfo->state,
            'content'       => $jobTransferInfo->after_date.' ==> '.$afterDate,
        ]);
        $this->db->commit();
        return $this->checkReturn([]);
    }

    /**
     * 更新转岗HCId
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function updateJobTransferHcId($paramIn = []): array
    {
        $jobTransferId = $paramIn['id'] ?? '';
        $staffId       = $paramIn['staff_info_id'] ?? '';
        $hcId          = $paramIn['hc_id'] ?? '';

        if (empty($jobTransferId)) {
            throw new \Exception('no valid job transfer');
        }

        //获取转岗详情
        $jobTransferInfo = JobTransferModel::findFirst($jobTransferId);

        $this->db = $this->getDI()->get("db");
        $this->db->begin();

        //校验hc
        //如果所选hc已经等于0了就不能再减少了
        $hcInfo = HrHcModel::findFirst([
            'conditions' => 'hc_id = :hc_id: and surplusnumber > 0',
            'columns'    => 'surplusnumber,demandnumber,expirationdate',
            'bind'       => ['hc_id' => $hcId],
        ]);
        if (!isset($hcInfo->surplusnumber) || isset($hcInfo->surplusnumber) && $hcInfo->surplusnumber == 0) {
            $this->db->rollback();
            throw new \Exception($this->getTranslation()->_('job_transfer_err_2'));
        }

        //更新转岗hc、以及过期时间
        $this->db->updateAsDict("job_transfer",
            [
                'hc_id'              => $hcId,
                'hc_expiration_date' => $hcInfo->expirationdate,
            ],
            [
                'conditions' => "id = ?",
                'bind'       => [
                    $jobTransferId,
                ],
            ]
        );

        //张帆需求: 更改hc将不再扣减，改为在立即转岗的时候扣减
        //减hc
        /*(new HcServer($this->lang,$this->timezone))->updateHc([
            'id'            => $hcId,
            'surplusnumber' => $hcInfo->surplusnumber ?? 0,
            'demandnumber'  => $hcInfo->demandnumber ?? 0,
        ]);*/

        if (isset($paramIn['after_date'])) {
            $type    = 3;
            $content = $jobTransferInfo->after_date." ==> ".$paramIn['after_date'];
        } elseif (isset($paramIn['hc_id'])) {
            $type    = 4;
            $content = $jobTransferInfo->hc_id." ==> ".$paramIn['hc_id'];
        } else {
            $this->db->rollback();
            return [];
        }

        $this->saveOperateLog([
            'id'            => $jobTransferId,
            'staff_info_id' => $staffId,
            'operate_id'    => $type, // 1-立即转岗 2-系统自动转岗 3-修改日期 4-修改hc
            'state'         => $jobTransferInfo->state,
            'content'       => $content,
        ]);
        $this->db->commit();
        return $this->checkReturn([]);
    }

    /**
     * @description:修改上级和角色
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/8 14:39
     */
    public function updateJobTransferStore($paramIn = [])
    {
        try {
            $roles            = $this->processingDefault($paramIn, 'role_ids', 3);
            $after_manager_id = $this->processingDefault($paramIn, 'after_manager_id', 2);
            $id               = $this->processingDefault($paramIn, 'id', 2);
            $staffId          = $paramIn['staff_info_id'] ?? '';
            $transferInfo     = JobTransferModel::findFirst([
                'conditions' => 'id = :id: ',
                'bind'       => ['id' => $id],
            ]);
            if (empty($after_manager_id)) {
                throw new \Exception('after_manager_id error');
            }
            if (empty($roles)) {
                throw new \Exception('role_ids error');
            }
            if (empty($transferInfo)) {
                throw new \Exception('no valid data');
            }
            //转岗状态为“待转岗”或“转岗失败”，且审核状态为“审批通过”的情况下
            if (!in_array($transferInfo->state, [
                    JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED,
                    JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                ]) || $transferInfo->approval_state != enums::APPROVAL_STATUS_APPROVAL) {
                throw new \Exception('state error');
            }
            //查询上级是否在职
            $jobHandoverStaffInfo = $this->getStaffInfo([
                "staff_id" => $after_manager_id,
            ]);
            if (!$jobHandoverStaffInfo || $jobHandoverStaffInfo["state"] != enums::$service_status['incumbency'] ||
                $jobHandoverStaffInfo["formal"] != 1 || $after_manager_id == $transferInfo->staff_id) {
                throw new \Exception('after_manager_id error ', enums::$ERROR_CODE['1000']);
            }

            $roles = $roles ? implode(',', $roles) : "";  //转岗后角色

            $this->db = $this->getDI()->get("db");
            $this->db->begin();

            $updateData = [
                'after_role_ids'   => $roles,
                'after_manager_id' => $after_manager_id,
            ];
            // 是否编辑上级，如果是，则标记。 转岗时，不重新获取。
            if($transferInfo->after_manager_id != $after_manager_id) {
                $updateData['is_edit_manager_id'] = JobTransferModel::IS_EDIT_MANAGER_ID_YES;
            }
            $success = $this->db->updateAsDict("job_transfer", $updateData,
                [
                    'conditions' => "id = ?",
                    'bind'       => [
                        $id,
                    ],
                ]
            );
            if (!$success) {
                $this->db->rollback();
                throw new \Exception('update error');
            }
            $before_role_name = '';
            $roles_name       = '';
            $roleTranslation  = $this->getRolesTranslation();
            //获取以前的角色
            if (isset($transferInfo->after_role_ids) && $transferInfo->after_role_ids) {
                $roles_ids = [];
                $roleInfo  = RolesModel::find([
                    'columns'    => "id,name,name_th,name_en",
                    'conditions' => "id in ({role_id:array}) and status = 1",
                    'bind'       => [
                        'role_id' => explode(',', $transferInfo->after_role_ids),
                    ],
                ])->toArray();
                foreach ($roleInfo as $role) {
                    $roleName    = $this->lang == 'zh-CN' ? ($roleTranslation[$role['id']]['role_name_zh'] ?? "")
                        : ($this->lang == 'en' ? $roleTranslation[$role['id']]['role_name_en'] ?? "" : $roleTranslation[$role['id']]['role_name_th'] ?? '');
                    $roles_ids[] = $roleName;
                }
                $before_role_name = $roles_ids ? implode(',', $roles_ids) : "";
            }
            //获取现在的角色
            if (isset($roles) && $roles) {
                $roles_ids = [];
                $roleInfo  = RolesModel::find([
                    'columns'    => "id,name,name_th,name_en",
                    'conditions' => "id in ({role_id:array}) and status = 1",
                    'bind'       => [
                        'role_id' => explode(',', $roles),
                    ],
                ])->toArray();
                foreach ($roleInfo as $role) {
                    $roleName    = $this->lang == 'zh-CN' ? ($roleTranslation[$role['id']]['role_name_zh'] ?? "")
                        : ($this->lang == 'en' ? $roleTranslation[$role['id']]['role_name_en'] ?? "" : $roleTranslation[$role['id']]['role_name_th'] ?? '');
                    $roles_ids[] = $roleName;
                }
                $roles_name = $roles_ids ? implode(',', $roles_ids) : "";
            }
            $failure_reason = [
                'zh-CN' => '',
                'en'    => '',
                'th'    => '',
            ];
            if ($transferInfo->after_manager_id != $after_manager_id) {
                $failure_reason['zh-CN'] .= '转岗后直线上级:'.$transferInfo->after_manager_id.' ==> '.$after_manager_id." , ";
                $failure_reason['en']    .= 'Straight line superior after transfer:'.$transferInfo->after_manager_id.' ==> '.$after_manager_id." , ";
                $failure_reason['th']    .= 'ผู้บังคับบัญชาหลังโอนย้าย:'.$transferInfo->after_manager_id.' ==> '.$after_manager_id." , ";
            }

            if ($transferInfo->after_role_ids != $roles) {
                $failure_reason['zh-CN'] .= " 转岗后角色:".$before_role_name.' ==> '.$roles_name;
                $failure_reason['en']    .= " Role after transfer:".$before_role_name.' ==> '.$roles_name;
                $failure_reason['th']    .= " บทบาทหลังโอนย้าย:".$before_role_name.' ==> '.$roles_name;
            }
//            $failure_reason = [
//                'zh-CN' => '转岗后直线上级:'. $transferInfo->after_manager_id . ' ==> ' . $after_manager_id . " , 转岗后角色:". $before_role_name . ' ==> ' . $roles_name,
//                'en' => 'Straight line superior after transfer:'. $transferInfo->after_manager_id . ' ==> ' . $after_manager_id . " , Role after transfer:". $before_role_name . ' ==> ' . $roles_name,
//                'th' => 'ผู้บังคับบัญชาหลังโอนย้าย:'. $transferInfo->after_manager_id . ' ==> ' . $after_manager_id . " , บทบาทหลังโอนย้าย:". $before_role_name . ' ==> ' . $roles_name,
//            ];

            $success = $this->saveOperateLog([
                'id'             => $id,
                'staff_info_id'  => $staffId,
                'operate_id'     => 5, // 1-立即转岗 2-系统自动转岗 3-修改日期 4-修改hc  5 修改上级和角色
                'state'          => $transferInfo->state,
                'failure_reason' => json_encode($failure_reason),
                'content'        => $transferInfo->after_manager_id.' ==> '.$after_manager_id." ,  ".$transferInfo->after_role_ids.' ==> '.$roles,
            ]);
            if (!$success) {
                $this->db->rollback();
                throw new \Exception('add error');
            }
            $this->db->commit();
            return $this->checkReturn([]);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('updateJobTransferStore:'.$e->getMessage(), 'notice');
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * 编辑转岗信息
     * @param $paramIn
     * @return array
     */
    public function editJobTransferData($paramIn = [])
    {
        $jobTransferId = $paramIn['id'];
        $operatorId    = $paramIn['operator_id'];
        $result        = true;

        $params = [
            'after_date'                  => $paramIn['after_date'],
            'after_manager_id'            => $paramIn['after_manager_id'],
            'after_role_ids'              => $paramIn['after_role_ids'],
            'after_working_day_rest_type' => $paramIn['after_working_day_rest_type'],
        ];

        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            //获取转岗详情
            $jobTransferInfo = JobTransferModel::findFirst($jobTransferId);
            if (empty($jobTransferInfo)) {
                throw new BusinessException(sprintf('id %d not exist', $jobTransferId));
            }
            $transferChange = $this->getTransferColumnsChangeInfo($jobTransferInfo->toArray(), $params);
            if (!isset($transferChange['value'])) {
                throw new ValidationException('data not change');
            }
            //如上级变更，转岗时不再重新获取
            if ($jobTransferInfo->after_manager_id != $paramIn['after_manager_id']) {
                $transferChange['value']['is_edit_manager_id'] = JobTransferModel::IS_EDIT_MANAGER_ID_YES;
            }

            $this->db->updateAsDict("job_transfer",
                $transferChange['value'], [
                    'conditions' => "id = ?",
                    'bind'       => [
                        $jobTransferId,
                    ],
                ]
            );
            if (!empty($transferChange['change_info'])) {
                $content = json_encode($transferChange['change_info']);
            } else {
                $content = '';
            }

            $this->saveOperateLog([
                'id'            => $jobTransferId,
                'staff_info_id' => $operatorId,
                'operate_id'    => JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_TRANSFER_INFO,
                'state'         => $jobTransferInfo->state,
                'content'       => $content,
            ]);

            $db->commit();
        } catch(BusinessException $be) {
            $db->rollback();
            $this->logger->write_log('editJobTransferData err :' . $be->getMessage());
            $result = false;
        } catch (ValidationException $ve) {
            $db->rollback();
            $this->logger->write_log('editJobTransferData validate err :' . $ve->getMessage(), 'info');
            $result = false;
        }
        return $this->checkReturn(['data' => $result]);
    }

    /**
     * 获取字段变更
     * @param $before
     * @param $after
     * @return array
     */
    private function getTransferColumnsChangeInfo($before, $after)
    {
        $result  = [];
        $columns = ['after_date', 'after_manager_id', 'after_role_ids', 'after_working_day_rest_type'];
        foreach ($columns as $column) {
            if (isset($before[$column]) && isset($after[$column]) && $before[$column] != $after[$column]) {
                $result['value'][$column]       = $after[$column];
                $result['change_info'][] = [
                    'column' => $column,
                    'after'  => $after[$column],
                    'before' => $before[$column],
                ];
            }
        }
        return $result;
    }

    /**
     * 获取员工信息
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function getStaffInfo($paramIn = [])
    {
        $returnData = [];
        $staffId    = $paramIn["staff_id"];
        $staffInfo  = (new StaffRepository())->getStaffInfo($staffId);
        if ($staffInfo) {
            //员工状态
            if ($staffInfo["state"] == 1) {
                $staffInfo["state_name"] = $this->getTranslation()->_('jobtransfer_0021');
            } elseif ($staffInfo["state"] == 2) {
                $staffInfo["state_name"] = $this->getTranslation()->_('jobtransfer_0022');
            } elseif ($staffInfo["state"] == 3) {
                $staffInfo["state_name"] = $this->getTranslation()->_('jobtransfer_0023');
            } else {
                $staffInfo["state_name"] = "";
            }
            //所属区域
            $staffInfo["store_name"] = $staffInfo["store_name"] ?: ($staffInfo["hr_staff_sys_store_id"] == '-1' ? 'Head Office' : '');
            //公司名称
            $staffInfo["company_name"] = "Flash Express";

            $returnData = $staffInfo;
        }

        return $returnData;
    }

    /**
     * 获取全部角色翻译
     */
    public function getRolesTranslation(): array
    {
        try {
            $winhr_rpc = (new ApiClient('hr_rpc', '', 'role_list'));
            $winhr_rpc->setParams(['']);
            $ret = $winhr_rpc->execute();
            $this->getDI()->get('logger')->write_log('Translation: '.json_encode($ret).'====>'.json_encode($ret),
                'info');
            return array_column($ret['result'], null, 'role_id');
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("Translation-".$e->getMessage().$e->getTraceAsString(), 'info');
            return [];
        }
    }

    /**
     * 获取立即转岗处理进度
     * @param $params
     * @return bool
     */
    public function getJobtransferProcess($params)
    {
        $transferId = $this->processingDefault($params, 'transfer_id');
        $operatorId = $this->processingDefault($params, 'operator_id');

        $cacheKey = "do_job_transfer_".$transferId.'_'.$operatorId;

        $redis = $this->getDI()->get('redisLib');
        return (bool)$redis->exists($cacheKey);
    }

    /**
     * 可视化超时，按照表单去超时
     * @param $audit_id
     * @return string
     */
    public function getAuditFormOvertimeDate($audit_id): string
    {
        $resignInfo = JobTransferModel::findFirst($audit_id);
        if (empty($resignInfo)) {
            return '';
        }
        return $resignInfo->after_date;
    }

    /**
     * @description:获取可以交接的员工id
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/13 11:53
     */
    public function getJobHandoverStaffId($staffInfo)
    {
        $jobHandoverStaffIds = [];
        //这里是shop 部门的可选网点类型
        $shopCategory       = (new SettingEnvServer())->getSetVal('jobtransfer_shop_category');
        $shopCategory = empty($shopCategory) ? (UC("jobtransfer")["shop_category"] ?? "") : $shopCategory;

        //这里是network 部门的可选网点类型
        $networkCategory       = (new SettingEnvServer())->getSetVal('jobtransfer_network_category');
        $networkCategory = empty($networkCategory) ? (UC("jobtransfer")["network_category"] ?? "") : $networkCategory;

        //这里是hub 部门的可选网点类型
        $hubCategory       = (new SettingEnvServer())->getSetVal('jobtransfer_hub_category');
        $hubCategory = empty($hubCategory) ? (UC("jobtransfer")["hub_category"] ?? "") : $hubCategory;

        $shopCategoryArr     = explode(",", $shopCategory);
        $hubCategoryArr      = explode(",", $hubCategory);
        $networkCategoryArr  = explode(",", $networkCategory);

        //NW and  bulky：可填写负责转岗员工原网点的DM、AM
        if (in_array($staffInfo["category"], $networkCategoryArr)) {
            $pieceMangerInfo = SysManagePieceModel::findFirst([
                'conditions' => 'id = :manage_piece:',
                'bind'       => ['manage_piece' => $staffInfo['manage_piece']],
                'column'     => 'id,manager_id',
            ]);
            if ($pieceMangerInfo->manager_id) {
                $jobHandoverStaffIds[] = $pieceMangerInfo->manager_id;
            }

            $regionMangerInfo = SysManageRegionModel::findFirst([
                'conditions' => 'id = :manage_region:',
                'bind'       => ['manage_region' => $staffInfo['manage_region']],
                'column'     => 'id,manager_id',
            ]);
            if ($regionMangerInfo->manager_id) {
                $jobHandoverStaffIds[] = $regionMangerInfo->manager_id;
            }
            //SHOP：可填写负责转岗员工原网点的AM
        } else {
            if (in_array($staffInfo["category"], $shopCategoryArr)) {
                $regionMangerInfo = SysManageRegionModel::findFirst([
                    'conditions' => 'id = :manage_region:',
                    'bind'       => ['manage_region' => $staffInfo['manage_region']],
                    'column'     => 'id,manager_id',
                ]);
                if ($regionMangerInfo->manager_id) {
                    $jobHandoverStaffIds[] = $regionMangerInfo->manager_id;
                }
                //HUB：可填写转岗员工所属部门负责人
            } else {
                if (in_array($staffInfo["category"], $hubCategoryArr)) {
                    //所属部门的负责人
                    $departmentInfo = SysDepartmentModel::findFirst($staffInfo['department_id']);
                    if ($departmentInfo->manager_id) {
                        $jobHandoverStaffIds[] = $departmentInfo->manager_id;
                    }
                }
            }
        }
        return $jobHandoverStaffIds;
    }

    /**
     * 获取转岗网点负责人
     */
    public function getJobtransferStoreManager($paramIn = [])
    {
        //参数
        $storeId = $paramIn["store_id"] ?? "";

        $storeData  = (new JobtransferRepository($this->timeZone))->getStoreManager([
            "store_id" => $storeId,
        ]);
        return $storeData[0]["manager_id"] ?? "";
    }

    /**
     * 加转岗锁
     * @param string $key1 转岗id
     * @param string $key2 转岗id + 操作人员工号
     * @param int $expire 超时时间
     * @return mixed
     */
    public function setTransferLock(string $key1, string $key2, int $value, int $expire)
    {
        $redis     = $this->getDI()->get('redisLib');
        $LUASCRIPT = <<<LUA
local key1 = KEYS[1]
local key2 = KEYS[2]
local value = ARGV[1]
local ttl = ARGV[2]

if (redis.call('SISMEMBER', key1, value) == 1) then
    return 0
else
    redis.call('SADD', key1, value)
    redis.call('EXPIRE', key, ttl)
    redis.call('SET', key2)
    redis.call('EXPIRE', key, ttl)
end
return 1
LUA;
        return $redis->eval($LUASCRIPT, [$key1, $key2, $value, $expire], 2);
    }

    /**
     * 移除转岗锁
     * @param string $key 转岗id
     * @param string $key2 转岗id + 操作人员工号
     * @param int $value 超时时间
     * @return mixed
     */
    public function remTransferLock(string $key1, string $key2, int $value)
    {
        $redis     = $this->getDI()->get('redisLib');
        $LUASCRIPT = <<<LUA
local key1 = KEYS[1]
local key2 = KEYS[2]
local value = ARGV[1]

if (redis.call('SISMEMBER', key1, value) == 1) then
    redis.call('SREM', key1, value)
    redis.call('DEL', key2)
else
    return 0
end
return 1
LUA;
        return $redis->eval($LUASCRIPT, [$key1, $key2, $value], 2);
    }

    /**
     * @return array[]
     */
    private function getEvCourierProjectNumber(): array
    {
        return [
            [
                'key'   => $this->getTranslation()->_('project_num_1'),
                'value' => 1,
            ],
            [
                'key'   => $this->getTranslation()->_('project_num_2'),
                'value' => 2,
            ],
        ];
    }

    /**
     * 组织项目期数
     * @param $staff_info
     * @param array $vehicleInfo
     * @return string
     */
    private function generateProjectNum($staff_info, array $vehicleInfo): string
    {
        if (empty($vehicleInfo)) {
            return '';
        }

        $projectNumPrefixKey = '';
        switch ($staff_info['job_title']) {
            case VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID:
                $projectNumPrefixKey = self::PROJECT_NUM_PREFIX_EV;
                break;
            case VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID:
                $projectNumPrefixKey = self::PROJECT_NUM_PREFIX_VAN_PROJECT;
                break;
        }
        return !empty($vehicleInfo['project_num'])
            ? $this->getTranslation()->_($projectNumPrefixKey . $vehicleInfo['project_num'])
            : '';
    }

    /**
     * 根据职位获取项目期数下拉项
     * @param $job_title_id
     * @return array|array[]
     */
    private function getProjectNumByJobTitle($job_title_id): array
    {
        $projectNumList = [];
        switch ($job_title_id) {
            case VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID:
                $projectNumList = $this->getEvCourierProjectNumber();
                break;
            case VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID:
                $projectNumList = $this->getVanProjectProjectNumber();
                break;
        }
        return $projectNumList;
    }

    private function getVanProjectProjectNumber(): array
    {
        return [
            [
                'key'   => $this->getTranslation()->_('van_courier_project_num_1'),
                'value' => 1,
            ],
            [
                'key'   => $this->getTranslation()->_('van_courier_project_num_2'),
                'value' => 2,
            ],
            [
                'key'   => $this->getTranslation()->_('van_courier_project_num_3'),
                'value' => 3,
            ],
            [
                'key'   => $this->getTranslation()->_('van_courier_project_num_4'),
                'value' => 4,
            ],
        ];
    }

    /**
     * 设置批次信息
     * @param $params
     * @return bool
     */
    public function setBatchAddNumber($params)
    {
        $batchNumber = $params['batch_number'] ?? '';
        $staffIds    = $params['ids'] ?? '';

        //获取需要转岗的staff_id
        $staffIdsStr = json_encode(['total' => $staffIds, 'success' => [], 'fail' => [], 'fail_msg' => []]);
        $this->getDI()->get('logger')->write_log("setBatchAddNumber staff ids :".$staffIdsStr, 'info');

        //有效时长10分钟
        $this->getDI()->get('redis')->save('batch_add_job_transfer_key'.$batchNumber, $staffIdsStr, 1800);

        return true;
    }

    /**
     * 根据不同的雇佣类型返回雇佣期间的单位
     * @param $current_hire_type
     * @return string
     */
    protected function formatHireTimesUnit($current_hire_type): string
    {
        if (!in_array($current_hire_type, [HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3, HrStaffInfoModel::HIRE_TYPE_4])) {
            return '';
        }
        $result = '';

        if ($current_hire_type == HrStaffInfoModel::HIRE_TYPE_2) {
            $result = $this->getTranslation()->_('monthlies');
        }
        if (in_array($current_hire_type, [HrStaffInfoModel::HIRE_TYPE_3, HrStaffInfoModel::HIRE_TYPE_4])) {
            $result = $this->getTranslation()->_('daily');
        }
        return $result;
    }

    /**
     * 获取默认的雇佣类型
     * @param $paramIn
     * @return array
     */
    public function getDefaultHireType($paramIn): array
    {
        $staffId      = $paramIn['staff_id'];
        $departmentId = $paramIn['department_id'];
        $positionId   = $paramIn['position_id'];
        $storeId      = $paramIn['store_id'];

        //获取员工信息
        $staffInfo = (new StaffServer())->getStaffInfoSpecColumns($staffId, 'hire_type');
        if (empty($staffInfo)) { //无工号信息
            return ['hire' => []];
        }

        $currentHireType = $staffInfo['hire_type'];
        if ($currentHireType == HrStaffInfoModel::HIRE_TYPE_UN_PAID) { //个人代理
            return ['hire' => [HrStaffInfoModel::HIRE_TYPE_UN_PAID]];
        }
        $result = [
            'hire_type' => [HrStaffInfoModel::HIRE_TYPE_1] // 默认正式员工
        ];

        // 获取职位配置信息
        $settingEnvSrv       = new SettingEnvServer();
        $networkManagementId = $settingEnvSrv->getSetVal('dept_network_management_id');

        // 获取一级部门
        $departmentInfo = (new DepartmentRepository())->getSpecLevelDepartmentInChain($departmentId);
        $isNetworkManagement = !empty($departmentInfo) && $departmentInfo == $networkManagementId;

        $hcHireTypeConfigSrv = new HcHireTypeConfigServer();
        $hireTypeBranch = $hcHireTypeConfigSrv->getBranchHireTypeList($positionId,$storeId);
        $hireTypePosition = $hcHireTypeConfigSrv->getPositionHireTypeList($positionId);
        // 日薪制合同工转岗规则
        if ($currentHireType == HrStaffInfoModel::HIRE_TYPE_3) {
            if ($isNetworkManagement) {
                if ($positionId == enums::$job_title['branch_supervisor']) { // 网点主管
                    $result['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_1];
                } else {
                    $result['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_3];
                }
            } else {
                if (in_array(HrStaffInfoModel::HIRE_TYPE_3,$hireTypeBranch) || in_array(HrStaffInfoModel::HIRE_TYPE_3,$hireTypePosition)) {
                    $result['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_3];
                } else if (in_array(HrStaffInfoModel::HIRE_TYPE_2,$hireTypeBranch) || in_array(HrStaffInfoModel::HIRE_TYPE_2,$hireTypePosition)) {
                    $result['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_2];
                } else {
                    $result['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_1];
                }
            }
            return $result;
        }

        // 月薪制合同工转岗规则
        if ($currentHireType == HrStaffInfoModel::HIRE_TYPE_2) {
            if (in_array(HrStaffInfoModel::HIRE_TYPE_3,$hireTypeBranch) || in_array(HrStaffInfoModel::HIRE_TYPE_3,$hireTypePosition)) {
                if (in_array(HrStaffInfoModel::HIRE_TYPE_2,$hireTypeBranch) || in_array(HrStaffInfoModel::HIRE_TYPE_2,$hireTypePosition)) {
                    $result['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3];
                } else {
                    $result['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_3];
                }
            } else {
                $result['hire_type'] = [in_array(HrStaffInfoModel::HIRE_TYPE_2,$hireTypeBranch) || in_array(HrStaffInfoModel::HIRE_TYPE_2,$hireTypePosition) ? HrStaffInfoModel::HIRE_TYPE_2 : HrStaffInfoModel::HIRE_TYPE_1];
            }
            return $result;
        }

        // 正式员工转岗规则
        if ($currentHireType == HrStaffInfoModel::HIRE_TYPE_1 && (in_array(HrStaffInfoModel::HIRE_TYPE_3,$hireTypeBranch) || in_array(HrStaffInfoModel::HIRE_TYPE_3,$hireTypePosition))) {
            $result['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_3];
        }

        return $result;
    }

    /**
     * 获取默认的雇佣期间
     * @param $hire_type
     * @return int|null
     */
    private function getDefaultHireTimes($hire_type): ?int
    {
        $validateHireTimes = null;
        switch ($hire_type) {
            case HrStaffInfoModel::HIRE_TYPE_2:
                $validateHireTimes = 12;
                break;
            case HrStaffInfoModel::HIRE_TYPE_3:
                $validateHireTimes = 365;
                break;
            default:
                break;
        }
        return $validateHireTimes;
    }

    /**
     * 获取车辆来源下来列表
     */
    public function getCarOwnerList($paramIn=[])
    {
        $t = $this->getTranslation();
        if (isCountry() && !empty($paramIn) && $paramIn['audit_type'] == enums::$audit_type["TF"] && !empty($paramIn['audit_id'])) {
            $transferInfo = JobTransferModel::findFirst($paramIn['audit_id']);
            if ($transferInfo->after_position_id == enums::$job_title['th_pickup_driver']) {
                return [
                    [
                        'key'   => $t->_('jobtransfer_0030'),
                        'value' => 2,
                    ],
                ];
            }
        }
        return [
            [
                'key'   => $t->_('jobtransfer_0028'),
                'value' => 1,
            ],
            [
                'key'   => $t->_('jobtransfer_0030'),
                'value' => 2,
            ],
            [
                'key'   => $t->_('jobtransfer_0029'),
                'value' => 3,
            ],

        ];
    }

    /**
     * 是否需要展示雇佣类型在转岗后信息中
     * @param $transfer_info
     * @return bool
     */
    public function isSHowHireTypeAtAfterInfo($transfer_info): bool
    {
        return $transfer_info['workflow_role_name'] == JobTransferEnums::JOB_TRANSFER_VERSION_V3 &&
            !in_array($transfer_info['current_hire_type'], HrStaffInfoModel::$agentTypeTogether);
    }

    /**
     * 重新发送指定日期
     * @param array $staffIds
     * @return void
     * @throws \Exception
     */
    public function resendTransferConfirmation(array $staffIds = [])
    {
        // 个人代理不需要发送确认单
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['j' => JobTransferModel::class]);
        $builder->where("j.state = :state:", ['state' => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED]);
        $builder->andWhere('j.confirm_state = :confirm_state:',
            ['confirm_state' => JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS]);
        $builder->andWhere('j.salary_type = :salary_type:', ['salary_type' => JobTransferEnums::SALARY_TYPE_SALARY_STRUCTURE]);
        $builder->notInWhere('j.after_hire_type', [HrStaffInfoModel::HIRE_TYPE_UN_PAID, HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT]);
        $builder->notInWhere('j.current_hire_type', [HrStaffInfoModel::HIRE_TYPE_UN_PAID, HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT]);
        if (!empty($staffIds)) {
            $builder->inWhere('j.staff_id', $staffIds);
        }
        $builder->columns(['j.id', 'j.serial_no']);
        $list = $builder->getQuery()->execute()->toArray();

        if (empty($list)) {
            return;
        }
        foreach ($list as $item) {
            $jobTransferObj = JobTransferModel::findFirst($item['id']);
            if ($this->isJobTransferConfirmationChanged($jobTransferObj) == JobTransferEnums::SALARY_CHANGE_STATE_STRUCTURE_CHANGED) {
                echo sprintf('重新发送确认单 : %s', $item['serial_no']), PHP_EOL;
                $this->logger->write_log(sprintf('重新发送确认单 : %s', json_encode($item)), 'info');
                $this->resendConfirmation($jobTransferObj);
            }
        }
    }

    /**
     * 是否转岗确认单信息变更
     * @param $job_transfer_obj
     * @return int
     */
    public function isJobTransferConfirmationChanged($job_transfer_obj): int
    {
        $staffService = new StaffServer();
        $staffInfo = $staffService->getStaffInfoSpecColumns($job_transfer_obj->staff_id,
            "h.staff_info_id, 
                h.name as staff_name,
                h.identity,
                h.formal,
                date_format(h.hire_date,'%Y-%m-%d') hire_date");

        //获取确认详情
        //将转岗确认的数据进行排序
        $jobTransferConfirmServer = $this->class_factory('JobTransferConfirmServer', $this->lang, $this->timeZone);
        $transferDetail = $jobTransferConfirmServer->getTransferPendingConfirmInfo($job_transfer_obj->toArray(), $staffInfo);
        $latestTransferDetail = array_values($transferDetail['salary']);

        //已确认数据
        $currentData = json_decode($job_transfer_obj->confirm_content, true);
        $currentTransferDetail = array_values($currentData['transfer_info']['salary']);

        //追加日志
        $this->logger->write_log(sprintf('transfer id %d,staff id (%d) confirmation changed, latest data = %s, older data = %s',
            $job_transfer_obj->id,
            $job_transfer_obj->staff_id, json_encode($latestTransferDetail, JSON_UNESCAPED_UNICODE),
            json_encode($currentTransferDetail, JSON_UNESCAPED_UNICODE)
        ), 'info');

        if ($jobTransferConfirmServer->arraysHaveChanged($latestTransferDetail, $currentTransferDetail)) {
            return JobTransferEnums::SALARY_CHANGE_STATE_STRUCTURE_CHANGED;
        }
        return JobTransferEnums::SALARY_CHANGE_STATE_NON;
    }

    protected function resendConfirmation($job_transfer_obj)
    {
        $job_transfer_obj->confirmation_gen_date = date('Y-m-d');
        $job_transfer_obj->confirm_state = JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM;
        $job_transfer_obj->sign_url = '';
        $job_transfer_obj->confirm_content = '';

        //确认日期要保留，作为已经确认过的记号
        //$job_transfer_obj->confirm_date = null;
        $job_transfer_obj->save();
    }

    /**
     * 返回被转岗人，用于判断是否为 lnt
     * @param $auditId
     * @return int|Resultset|Model
     */
    public function checkIfLntStaff($auditId)
    {
        if (empty($auditId)) {
            return 0;
        }
        $transferInfo = JobTransferModel::findFirst($auditId);
        if (!$transferInfo) {
            return 0;
        }
        return (new StaffServer())->isLntStaff($transferInfo->staff_id);
    }

    /**
     * @return array
     */
    private function getPersistentCountry(): array
    {
        return ['MY', 'PH'];
    }
}