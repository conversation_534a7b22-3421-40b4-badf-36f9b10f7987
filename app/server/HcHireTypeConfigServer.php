<?php

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Models\backyard\HcHireTypeBranchModel;
use FlashExpress\bi\App\Models\backyard\HcHireTypePositionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

class HcHireTypeConfigServer extends BaseServer
{

    /**
     * 获取个人代理配置的职位
     * @param string $store_id
     * @return array
     */
    public function getAgentConfigJobTitle($store_id): array
    {
        if (!empty($store_id)) {
            $hireTypeBranch = HcHireTypeBranchModel::find([
                'conditions' => 'store_id = :store_id: and status = :status: and hire_type = :hire_type:',
                'bind'       => [
                    'store_id'  => $store_id,
                    'hire_type' => HrStaffInfoModel::HIRE_TYPE_UN_PAID,
                    'status'    => HcHireTypeBranchModel::STATUS_ACTIVE,
                ],
                'columns'    => 'job_title',
            ])->toArray();
            if (!empty($hireTypeBranch)) {
                return array_values(array_unique(array_column($hireTypeBranch, 'job_title')));
            }
        }

        $hireTypePosition = HcHireTypePositionModel::find([
            'conditions' => 'status = :status: and hire_type = :hire_type:',
            'bind'       => [
                'hire_type' => HrStaffInfoModel::HIRE_TYPE_UN_PAID,
                'status'    => HcHireTypeBranchModel::STATUS_ACTIVE,
            ],
            'columns'    => 'job_title',
        ])->toArray();
        if (!empty($hireTypePosition)) {
            return array_values(array_unique(array_column($hireTypePosition, 'job_title')));
        }
        return [];
    }


    /**
     * 获取网点可选的雇佣类型
     * @param $job_title
     * @param $store_id
     * @return array
     */
    public function getBranchHireTypeList($job_title, $store_id): array
    {
        if (empty($store_id) || empty($job_title)) {
            return [];
        }
        $hireTypeBranch = HcHireTypeBranchModel::find([
            'conditions' => 'job_title = :job_title: and store_id = :store_id: and status = :status:',
            'bind'       => [
                'job_title' => $job_title,
                'store_id'  => $store_id,
                'status'    => HcHireTypeBranchModel::STATUS_ACTIVE,
            ],
            'columns'    => 'hire_type',
        ])->toArray();
        if (!empty($hireTypeBranch)) {
            return array_values(array_unique(array_column($hireTypeBranch, 'hire_type')));
        }
        return [];
    }

    /**
     * 获取职位可选的雇佣类型
     * @param $job_title
     * @return array
     */
    public function getPositionHireTypeList($job_title): array
    {
        if (empty($job_title)) {
            return [];
        }
        $hireTypePosition = HcHireTypePositionModel::find([
            'conditions' => 'job_title = :job_title: and status = :status:',
            'bind'       => [
                'job_title' => $job_title,
                'status'    => HcHireTypePositionModel::STATUS_ACTIVE,
            ],
            'columns'    => 'hire_type',
        ])->toArray();
        if (!empty($hireTypePosition)) {
            return array_values(array_unique(array_column($hireTypePosition, 'hire_type')));
        }

        return [];
    }

    /**
     * 检查职位是否支持雇佣类型
     * @param $job_title
     * @param $hire_type
     * @return bool
     */
    public function checkPositionHireType($job_title, $hire_type): bool
    {
        if (empty($hire_type) || empty($job_title)) {
            return false;
        }
        $hireTypePosition = HcHireTypePositionModel::findFirst([
            'conditions' => 'job_title = :job_title: and status = :status: and hire_type = :hire_type:',
            'bind'       => [
                'job_title' => $job_title,
                'status'    => HcHireTypePositionModel::STATUS_ACTIVE,
                'hire_type' => $hire_type,
            ],
        ]);
        return !empty($hireTypePosition);
    }

}