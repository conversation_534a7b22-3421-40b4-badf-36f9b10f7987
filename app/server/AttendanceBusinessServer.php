<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/10/22
 * Time: 10:17 AM
 */


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\HttpCurl;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;

class AttendanceBusinessServer extends AuditBaseServer
{

    public $timezone;
    //审批流走向条件
    protected $condition_department_1 = array(4,13,25);// Network Management、Shop Management、Hub Management部门下的员工出差打卡审批流
    protected $condition_department_2 = array();
    protected $condition_job_level = array(1,2);//职等为Staff和Supervisor的人：
    public function __construct($lang = 'zh-CN',$timezone)
    {
        $this->timezone = $timezone;
        parent::__construct($lang);

    }


    /**
     * 出差&外出期间打卡 申请 写入staff_audit_reissue_for_business  等任务跑了再正式提交审批
     * 目前会保存 出差打卡以及外出期间打卡 ，外出其实是出差的一种
     * @param $data
     * @param $info
     * @return mixed
     */
    public function apply_by_business($data,$info){

        $audit_type =  $this->getAuditTypeByBusinessTripType($data['business_trip_type'] ?? BusinessTripModel::BTY_NORMAL);
         //判断 是否存在 决定 插入还是更新
        if(empty($info)){//insert
            $data['serial_no'] = $audit_type . $this->getID();
            $flag = $this->getDI()->get('db')->insertAsDict('staff_audit_reissue_for_business', $data);
        }else{//update
            unset($data['task_time']);
            $id = $info['id'];
            $flag = $this->getDI()->get('db')->updateAsDict('staff_audit_reissue_for_business', $data, 'id = ' . $id);
        }
        return $flag;
    }
    //跑任务时候 时候 写入大列表
    public function insert_audit($id,$staffId , $business_trip_type){
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $audit_type = $this->getAuditTypeByBusinessTripType($business_trip_type);
            //创建
            $server = new ApprovalServer($this->lang,$this->timezone);
            $flag = $server->create($id, $audit_type, $staffId);
            if (!$flag) {
                throw new \Exception('创建审批流失败');
            }
            $db->commit();
            return $flag;
        } catch (\Exception $e){
            $db->rollback();
            $this->getDI()->get("logger")->write_log(['attendance_business_error' => $e->getMessage()]);
            return false;
        }
    }



    //获取指定日期 申请记录

    /**
     * CREATE TABLE `staff_audit_reissue_for_business` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '审批ID',
    `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
    `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
    `attendance_date` date DEFAULT NULL COMMENT '考勤日期',
    `start_shift` varchar(32) NOT NULL DEFAULT '' COMMENT '班次开始时间',
    `end_shift` varchar(32) NOT NULL DEFAULT '' COMMENT '班次结束时间',
    `working_day` tinyint(3) unsigned DEFAULT NULL COMMENT '是否是工作日 0:否；1:是',
    `start_time` datetime DEFAULT NULL COMMENT '补卡时间',
    `start_lat` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置的纬度',
    `start_lng` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置经度',
    `started_path` varchar(100) DEFAULT NULL COMMENT '上班考勤照片',
    `started_bucket` varchar(63) DEFAULT NULL COMMENT '上班考勤照片所属bucket',
    `start_reason` varchar(512) NOT NULL DEFAULT '' COMMENT '上班申请原因',
    `end_time` datetime DEFAULT NULL COMMENT '补卡时间',
    `end_lat` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置的纬度',
    `end_lng` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置经度',
    `end_path` varchar(100) DEFAULT NULL COMMENT '下班考勤照片',
    `end_bucket` varchar(63) DEFAULT NULL COMMENT '下班考勤照片所属bucket',
    `status` tinyint(1) DEFAULT '1' COMMENT '补卡状态 1 申请中 2 审核通过 3 驳回 4 撤销',
     *
     *
     * @param $staff_id
     * @param $date
     * @return array
     */
    public function find_by_date($staff_id,$date){
        $columns = "id,serial_no,staff_info_id,attendance_date,attendance_date as date_at,start_time,end_time,status,
                    CONVERT_TZ(start_time, '+00:00', '{$this->timezone}') start_thai,
                    CONVERT_TZ(end_time, '+00:00', '{$this->timezone}') end_thai,
                    start_shift as shift_start,end_shift as shift_end
                    ";
        $info = StaffAuditReissueForBusinessModel::findFirst([
            'conditions' => "staff_info_id = :staff_id: and attendance_date = :date:",
            'bind'       => array('staff_id' => $staff_id,'date' => $date),
            'columns' => $columns
        ]);
        return empty($info) ? array() : $info->toArray();

    }
    public function find_by_id($id){
        $info = StaffAuditReissueForBusinessModel::findFirst($id);
        return empty($info) ? array() : $info->toArray();
    }

    public function genSummary(int $id, $user)
    {
        //获取详情
        $info = $this->find_by_id($id);
        if (empty($info)) {
            return '';
        }
        $add_hour = $this->config->application->add_hour;
        //计算出差打卡需要的时区偏移量  秒
        [$start_time_zone_s,$end_time_zone_s] = $this->getCalculateTimeZoneSecond($info);

        $start_time = empty($info['start_time']) ? '' : date('Y-m-d H:i:s',strtotime($info['start_time']) + $start_time_zone_s + $add_hour * 3600);
        $end_time = empty($info['end_time']) ? '' : date('Y-m-d H:i:s',strtotime($info['end_time']) + $end_time_zone_s + $add_hour * 3600);
        $param = [
            [
                //$this->getTranslation()->_('date')
                'key'   => 'atb_date',
                'value' => $info['attendance_date']
            ],
            [
                'key'   => 'atb_start',
                'value' => $start_time
            ],
            [
                'key'   => 'atb_end',
                'value' => $end_time
            ],

        ];
        return $param ?? "";
    }

    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //[1]获取详情数据
        $result = $this->find_by_id($auditId);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        $auditType = $this->getAuditTypeByBusinessTripType($result['business_trip_type']);
        $add_hour = $this->config->application->add_hour;
        $result['created_at'] = date('Y-m-d H:i:s',strtotime("{$result['created_at']}") + $add_hour * 3600);
        $result['updated_at'] = date('Y-m-d H:i:s',strtotime("{$result['updated_at']}") + $add_hour * 3600);

        $atbPlaceStart = [
            'value'=>empty($result['start_lat']) ? '' : "https://www.google.com/maps/place/{$result['start_lat']},{$result['start_lng']}/",
            'tips'=>$this->getAddressName($result['start_lat'],$result['start_lng']),
        ];
        $atbPlaceEnd =[
            'value'=>empty($result['end_lat']) ? '' : "https://www.google.com/maps/place/{$result['end_lat']},{$result['end_lng']}/",
            'tips'=>$this->getAddressName($result['end_lat'],$result['end_lng']),
        ];
        //计算出差打卡需要的时区偏移量  秒
        [$start_time_zone_s,$end_time_zone_s] = $this->getCalculateTimeZoneSecond($result);

        //[2]组织详情数据
        $detailLists = [
            'work_shift' => "{$result['start_shift']}-{$result['end_shift']}",
            //上班打卡时间
            'atb_start' => empty($result['start_time']) ? '' : date(
                'Y-m-d H:i:s',
                strtotime($result['start_time']) + $start_time_zone_s + $add_hour * 3600
            ),
            'atb_place_start' => $atbPlaceStart,
            //下班打卡时间
            'atb_end' => empty($result['end_time']) ? '' : date(
                'Y-m-d H:i:s',
                strtotime($result['end_time']) + $end_time_zone_s + $add_hour * 3600
            ),
            'atb_place_end' => $atbPlaceEnd,
        ];

        $returnData['data']['detail'] = $this->format($detailLists);
        $audit_re = new AuditlistRepository($this->lang,$this->timezone);
        $data = [
            'title'       => $audit_re->getAudityType($auditType),
            'id'          => $result['id'],
            'staff_id'    => $result['staff_info_id'],
            'type'        => $auditType,
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['status'],
            'status_text' => $audit_re->getAuditStatus('10' . $result['status']),
            'notice'      => '',
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        $returnData['data']['head']   = $data;
        return $returnData;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(
            false,
            false,
            false,
            false,
            false,
            false);
    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $info = StaffAuditReissueForBusinessModel::findFirst($auditId);
        $apply_id = $info->staff_info_id;
        $staff_model = new StaffRepository($this->lang);
        $data['staff_info'] = $staff_model->getStaffPosition($apply_id);
        $data['condition_department_1'] = $this->condition_department_1;//Network Management、Shop Management、Hub Management一级部门以及一级部门以下的所有级别部门内的所有员工出差打卡审批流：
        $data['condition_department'] = $this->condition_department_2;//security&safety部门（部门ID=93）下的员工出差打卡审批流:
        $data['condition_job_level'] = $this->condition_job_level;
        $this->logger->write_log('atb work param '. $auditId .json_encode($data),'info');
        return $data;
    }
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            //同步更新审批状态
            $this->getDI()->get('db')->updateAsDict(
                'staff_audit_reissue_for_business',
                ['status' => $state],
                'id = '.$auditId
            );
            //审批通过 回写主表
            $info = $this->find_by_id($auditId);
            if(empty($info)){
                throw new \Exception('audit info error');
            }

            $updateData = ['status' => $state];
            //审批人信息等 hcm 导出用到 驳回原因等数据
            if(!empty($extend['staff_id'])){
                $approvalInfo                = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id:',
                    'bind' => ['staff_id' => $extend['staff_id']]
                ]);
                $updateData['approver_id']   = $extend['staff_id'];
                $updateData['approver_name'] = empty($approvalInfo) ? '' : $approvalInfo->name;
                $updateData['reject_reason'] = $extend['remark'] ?? '';
            }

            if($state == enums::APPROVAL_STATUS_APPROVAL){
                //处理境外出差时区  增加时区偏移量
                [$start_time_zone_s, $end_time_zone_s] = $this->getCalculateTimeZoneSecond($info);
                $info['start_time'] = empty($info['start_time']) ? $info['start_time'] : date('Y-m-d H:i:s',
                    strtotime($info['start_time']) + $start_time_zone_s);
                $info['end_time']   = empty($info['end_time']) ? $info['end_time'] : date('Y-m-d H:i:s',
                    strtotime($info['end_time']) + $end_time_zone_s);


                $staff_re = new StaffRepository($this->lang);
                $user_info = $staff_re->getStaffpositionV2($info['staff_info_id']);
                if(empty($user_info)){
                    $this->logger->write_log('atb 回写 错误 找不到用户信息' .$info['staff_info_id'] ,'info');
                    return true;
                }
                //获取信息 插入 打卡表
                $re = new AttendanceRepository($this->lang,$this->timezone);
                $act_att_info = $re->getDateInfo($info['staff_info_id'], $info['attendance_date'],$info['shift_type']);
                if(!empty($act_att_info)){//根据对应 缺失的 打卡数据 用出差打卡 补全
                    $att_server = new AttendanceServer($this->lang,$this->timezone);
                    $att_server->save_attendance_business($act_att_info,$info);
                    $this->logger->write_log('atb 回写 更新原数据' .$auditId ,'info');
                }else{
                    $insert['staff_info_id'] = $info['staff_info_id'];
                    $insert['attendance_date'] = $info['attendance_date'];
                    $insert['organization_id'] = $user_info['organization_id'];
                    $insert['organization_type'] = $user_info['organization_type'];
                    $insert['shift_start'] = $info['start_shift'];
                    $insert['shift_end'] = $info['end_shift'];
                    $insert['working_day'] = $info['working_day'];
                    //新增班次 字段
                    $insert['shift_id'] = $info['shift_id'];
                    $insert['shift_ext_id'] = $info['shift_ext_id'];
                    $insert['shift_type'] = $info['shift_type'];

                    if(!empty($info['start_time'])){
                        $insert['started_at'] = $info['start_time'];
                        $insert['started_state'] = self::getStaffWorkAttendanceStateByBusinessTripType($info['business_trip_type'] ?? BusinessTripModel::BTY_NORMAL);
                        $insert['started_staff_lat'] = $info['start_lat'];
                        $insert['started_staff_lng'] = $info['start_lng'];
                        $insert['started_path'] = $info['started_path'];
                        $insert['started_bucket'] = $info['started_bucket'];
                        $insert['started_remark'] = $info['start_reason'];
                        $insert['started_os'] = $info['started_os'];
                        $insert['started_clientid'] = $info['started_clientid'];
                    }
                    if(!empty($info['end_time'])){
                        $insert['end_at'] = $info['end_time'];
                        $insert['end_state'] = self::getStaffWorkAttendanceStateByBusinessTripType($info['business_trip_type'] ?? BusinessTripModel::BTY_NORMAL);
                        $insert['end_staff_lat'] = $info['end_lat'];
                        $insert['end_staff_lng'] = $info['end_lng'];
                        $insert['end_path'] = $info['end_path'];
                        $insert['end_bucket'] = $info['end_bucket'];
                        $insert['end_remark'] = $info['end_reason'];
                        $insert['end_os'] = $info['end_os'];
                        $insert['end_clientid'] = $info['end_clientid'];
                    }
                    $this->getDI()->get('db')->insertAsDict("staff_work_attendance",$insert);
                }

            }
            //更新审批表状态
            $this->getDI()->get('db')->updateAsDict(
                'staff_audit_reissue_for_business',
                $updateData,
                'id = ' . $auditId
            );
        }

    }
    //审批出差&外出打卡操作
    public function update_status($paramIn, $user_info)
    {
        try {
            if (empty($paramIn['audit_id']) || empty($paramIn['status'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
            }
            $info = $this->find_by_id(intval($paramIn['audit_id']));
            if (empty($info)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
            }

            $status     = intval($paramIn['status']);
            $auditType  = $this->getAuditTypeByBusinessTripType($info['business_trip_type']);
            $app_server = new ApprovalServer($this->lang, $this->timezone);

            if ($status == enums::$audit_status['approved']) {//审核通过
                $res = $app_server->approval($info['id'], $auditType, $user_info['id']);
            } elseif ($status == enums::$audit_status['dismissed']) {//驳回
                $res = $app_server->reject($info['id'], $auditType, $paramIn['reject_reason'], $user_info['id']);
            } elseif ($status == enums::$audit_status['revoked']) {//撤销
                $res = $app_server->cancel($info['id'], $auditType, $paramIn['reject_reason'], $user_info['id']);
            } else {
                return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
            }
            if (!isset($res) || empty($res)) {
                $this->logger->write_log('atb_update_status error ' . json_encode($paramIn) . json_encode($res),
                    'info');
                return $this->checkReturn(-3, 'server error');
            }
            return $this->checkReturn(['data' => ['audit_id' => $info['id']]]);
        } catch (\Exception $e) {
            $this->logger->write_log(['atb_update_status_error' => $paramIn, 'msg' => $e->getMessage()]);
            return $this->checkReturn(-3, 'server error');
        }
    }

    /**
     * @param $lat
     * @param $lng
     * @return string
     */
    public function getAddressName($lat,$lng)
    {
        if (empty($lat) || empty($lng)){
            return 'Unknown';
        }
        if (isCountry(['th', 'ph', 'la', 'my'])) {
            $api = new RestClient('oms');
            $res = $api->execute(RestClient::METHOD_POST, '/svc/maps/geocoding',
                ['lat' => (float)$lat, 'lng' => (float)$lng,'source'=>'backyard_bt'], ['Accept-Language' => $this->lang]);
            if (isset($res['code']) && $res['code'] == 1) {
                return $res['data'][0]['detailAddress'] ?? '';
            }
        } elseif (env('break_away_from_ms')) {
            return $this->getGeocoding($lat, $lng);
        } else {
            $api = new ApiClient('fle', 'com.flashexpress.fle.svc.api.GoogleMapsSvc', 'geoCoding', $this->lang);
            $api->setParams(['lat' => (float)$lat, 'lng' => (float)$lng]);
            $res = $api->execute();
            if (isset($res['result'])) {
                $result = $res['result'];
                return $result['detail_address'] ?? $result['district_name'] . $result['city_name'] . $result['province_name'] . $result['country_name'];
            }
        }
        return '';
    }

    public function getGeocoding($lat, $lng)
    {
        //https://developers.google.com/maps/documentation/geocoding/requests-reverse-geocoding?hl=zh-cn
        $location = 'https://maps.googleapis.com/maps/api/geocode/json';
        // 这里的秘钥不要记录了
        $google_maps_key = env('google_maps_api_key', '');
        $url             = $location . "?latlng={$lat},{$lng}&key=";
        $res             = HttpCurl::httpGet($url . $google_maps_key);
        $this->logger->write_log("getGeocoding  curl : {$url} , res : {$res}", 'info');
        $res     = json_decode($res, true);
        $address = 'Unknown';
        if (!empty($res['results'])) {
            $address = $res['results'][0] ? $res['results'][0]['formatted_address'] : $address;
        }
        return $address;
    }


    /**
     * 查询出差打卡表
     */
    public function find_by_date_staffs($staffs,$date){
        $columns = "id,serial_no,staff_info_id,attendance_date,start_time,end_time,status,
                    CONVERT_TZ(start_time, '+00:00', '{$this->timezone}') start_thai,
                    CONVERT_TZ(end_time, '+00:00', '{$this->timezone}') end_thai";
        $info = StaffAuditReissueForBusinessModel::find([
            'conditions' => "staff_info_id in ({staffs:array}) and attendance_date = :date: and status in (1,2)",
            'bind'       => array('staffs' => $staffs,'date' => $date),
            'columns' => $columns
        ]);
        return empty($info) ? array() : $info->toArray();
    }


    public function getAuditTypeByBusinessTripType($business_trip_type): int
    {
        if ($business_trip_type == BusinessTripModel::BTY_GO_OUT) {
            return  enums::$audit_type['GOC'];
        }
        if($business_trip_type == BusinessTripModel::BTY_WORK_FROM_HOME){
            return enums::$audit_type['WH'];
        }
        return  enums::$audit_type['ATB'];
    }

    public static function getStaffWorkAttendanceStateByBusinessTripType($business_trip_type): int
    {
        if ($business_trip_type == BusinessTripModel::BTY_GO_OUT) {
            return  StaffWorkAttendanceModel::STATE_GO_OUT;
        }
        if ($business_trip_type == BusinessTripModel::BTY_WORK_FROM_HOME) {
            return  StaffWorkAttendanceModel::STATE_WORK_HOME;
        }
        return  StaffWorkAttendanceModel::STATE_BUSINESS_TRIP;
    }

    //计算出差打卡需要增加的偏移量
    public function getCalculateTimeZoneSecond($business){
        $add_hour = $this->config->application->add_hour;
        //计算上班打卡需要的偏移量  秒
        $start_time_zone_s = is_null($business['start_time_zone']) ? 0 : (($business['start_time_zone'] - $add_hour ) * 3600);
        //计算下班打卡需要的偏移量 秒
        $end_time_zone_s = is_null($business['end_time_zone']) ? 0 : (($business['end_time_zone'] - $add_hour ) * 3600);
        return [$start_time_zone_s,$end_time_zone_s];
    }
}