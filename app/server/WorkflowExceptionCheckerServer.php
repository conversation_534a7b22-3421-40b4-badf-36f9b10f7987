<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\FeiShuRobotEnums;
use FlashExpress\bi\App\Enums\WorkflowEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeBaseModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeCcBaseModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNoticeModel;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

class WorkflowExceptionCheckerServer extends BaseServer
{
    /**
     * 检测审批流中的指定工号是否为空或离职
     * @return void
     */
    public function checkSpecifyStaffIds()
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("================= 检查审批流固定工号，begin=================", 'info');

        $workflowNotice = WorkflowNoticeModel::find();
        $workflowNotice->delete();

        $workflow = WorkflowModel::find([
            'columns' => 'id,relate_type,version',
        ])->toArray();
        if (empty($workflow)) {
            $logger->write_log("================= workflow 表中无数据，请检查数据表数据 =================", 'info');
            return;
        }

        $model               = new WorkflowNodeBaseModel();
        $ccModel             = new WorkflowNodeCcBaseModel();
        foreach ($workflow as $wfInfo) {
            //查找出指定工号
            $baseNodeInfo = $model->find([
                'conditions' => 'flow_id = :flow: and (auditor_type = 2 or approval_policy = 2) and deleted = 0 and version = :version:',
                'bind'       => [
                    'flow'    => $wfInfo['id'],
                    'version' => isset($wfInfo['version']) && $wfInfo['version'] ? $wfInfo['version'] : '',
                ],
                'columns'    => 'auditor_type,auditor_id,approval_policy,specify_approver',
            ])->toArray();

            $ccBaseNodeInfo = $ccModel->find([
                'conditions' => 'flow_id = :flow: and auditor_type = 2 and deleted = 0 and version = :version:',
                'bind'       => [
                    'flow'    => $wfInfo['id'],
                    'version' => isset($wfInfo['version']) && $wfInfo['version'] ? $wfInfo['version'] : '',
                ],
                'columns'    => 'auditor_type,auditor_id',
            ])->toArray();

            if (!empty($baseNodeInfo)) {
                //保存指定审批人类型
                $this->checkStaffValidate($baseNodeInfo, 'auditor_type', 'auditor_id', $wfInfo['relate_type']);

                //审批人为空指定工号
                $this->checkStaffValidate($baseNodeInfo, 'approval_policy', 'specify_approver', $wfInfo['relate_type'],
                    WorkflowEnums::WORKFLOW_EXCEPTION_TYPE_HANDOVER);
            }

            if (!empty($ccBaseNodeInfo)) {
                //保存指定抄送人类型
                $this->checkStaffValidate($ccBaseNodeInfo, 'auditor_type', 'auditor_id', $wfInfo['relate_type'],
                    WorkflowEnums::WORKFLOW_EXCEPTION_TYPE_CC);
            }
        }

        $logger->write_log("================= 检查审批流固定工号，end=================", 'info');
    }

    /**
     * @param $nodeInfo
     * @param string $specifiedColumnName 审批类型字段名称
     * @param string $valueColumnName 审批人字段名称
     * @param int $relateType 审批类型
     * @param int $exceptionType
     * @return void
     */
    public function checkStaffValidate(
        $nodeInfo,
        $specifiedColumnName,
        $valueColumnName,
        $relateType,
        int $exceptionType = WorkflowEnums::WORKFLOW_EXCEPTION_TYPE_APPROVAL
    )
    {
        $staffInfoIds = [];
        $staffRe      = new StaffRepository();

        foreach ($nodeInfo as $node) {
            if ($node[$specifiedColumnName] == enums::WF_NODE_DESIGNATE_OTHER && isset($node[$valueColumnName]) && $node[$valueColumnName]) {
                $staffIdArr   = explode(',', $node[$valueColumnName]);
                $staffInfoIds = array_unique(array_merge($staffInfoIds, $staffIdArr));
            }
        }
        if (empty($staffInfoIds)) {
            return;
        }
        $insert       = [];
        $staffInfoIds = array_values($staffInfoIds);

        foreach ($staffInfoIds as $staffId) {
            if (!$staffRe->isStaffStateOnJob($staffId)) {
                $insert[] = [
                    'relate_type'     => $relateType,
                    'exception_type'  => $exceptionType,
                    'exception_time'  => gmdate('Y-m-d H:i:s', time()),
                    'resign_staff_id' => $staffId,
                ];
            }
        }

        $count  = count($insert) ?? 0;
        $logger = $this->getDI()->get('logger');
        $logger->write_log(sprintf("审批类型%s, 异常类型 %d,共有 %d 条数据待插入", $relateType, $exceptionType,
            $count), 'info');
        if ($count <= 0) { //无数据
            return;
        }
        (new BaseRepository())->batch_insert('workflow_notice', $insert);
        $logger->write_log("[checkSpecifyStaffIds]插入:" . json_encode($insert), 'info');

        if (RUNTIME != 'pro' && env('approval_develop_switch', 1)) { //非生产环境不发送飞书消息
            return;
        }

        $relateType         = current($insert)['relate_type'];
        $typeKey            = enums::$at[$relateType];
        $this->languagePack = $this->getDI()->get('languagePack');
        $this->languagePack->setLanguage('zh-CN');
        $typeText = $this->languagePack->getTranslation()->_($typeKey);
        $content  = sprintf("国家: %s", $this->config->application->country_code) . PHP_EOL;
        $content  .= sprintf("环境: %s", env('runtime')) . PHP_EOL;
        $content  .= sprintf("审批类型: %s", $typeText) . PHP_EOL;
        $content  .= sprintf("离职工号: %s", join(',', array_column($insert, 'resign_staff_id'))) . PHP_EOL;
        $this->sendFeiShuWarnMessage($content, FeiShuRobotEnums::ROBOT_APPROVAL_EMPTY_OR_RESIGN_NOTICE);
    }

    /**
     * 发送飞书提醒
     * @return void
     */
    public function sendFeiShuWarnMessage($content, $robot_uuid)
    {
        if (empty($content) || empty($robot_uuid)) {
            return;
        }

        //文本消息
        $message['msg_type']        = 'text';
        $message['content']['text'] = $content;

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL            => $this->getUrl($robot_uuid),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING       => '',
            CURLOPT_MAXREDIRS      => 10,
            CURLOPT_TIMEOUT        => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST  => 'POST',
            CURLOPT_POSTFIELDS     => json_encode($message),
            CURLOPT_HTTPHEADER     => [
                'Accept-Language: zh-CN',
                'Content-Type: application/json',
            ],
        ]);

        $response = curl_exec($curl);
        curl_close($curl);
        $this->logger->write_log("[sendFeiShuWarnMessage]:" . json_encode($response), 'info');
    }

    private function getUrl($robot_uuid)
    {
        return sprintf('%s%s', 'https://open.feishu.cn/open-apis/bot/v2/hook/', $robot_uuid);
    }
}