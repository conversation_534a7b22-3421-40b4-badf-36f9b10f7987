<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrBlackGreyListModel;
use FlashExpress\bi\App\Models\backyard\ReemploymentRequestModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\ReemploymentRepository;
use FlashExpress\bi\App\Repository\ResignRepository;
use FlashExpress\bi\App\Repository\ResumeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use WebGeeker\Validation\Validation;

class ReemploymentServer extends AuditBaseServer
{
    const SERIAL_PRE = "HMD";

    public function addRequest($params)
    {
        //检查数据
        $this->checkParams($params);

        try {
            $this->db->begin();
            //
            $resume = (new ResumeRepository($this->timeZone))->getResumeInfoById($params['resume_id']);
            $hc     = (new HcRepository($this->timeZone))->getHcInfo($resume['hc_id']);

            $request                  = new ReemploymentRequestModel();
            $request->serial_no       = static::SERIAL_PRE.$this->getRandomId();
            $request->resume_id       = $params['resume_id'];
            $request->hc_id           = $resume['hc_id'];
            $request->identity        = $resume['credentials_num'];
            $request->name            = $resume['name'];
            $request->phone_area_code = $resume['phone_area_code'];
            $request->phone           = $resume['phone'];
            $request->sex             = $resume['sex'];
            $request->age             = $resume['date_birth'] ? DateHelper::howOld($resume['date_birth']) : ' ';
            $request->job_title       = $hc['job_title'];
            $request->department_id   = $hc['department_id'];
            $request->worknode_id     = $hc['worknode_id'];
            $request->job_title_grade = $params['job_title_grade'];
            $request->submitter       = $params['staff_info_id'];
            $request->state           = enums::APPROVAL_STATUS_PENDING;
            [$list, $count] = (new DelinquencyServer($this->lang,$this->timeZone))->getDelinquencyList($resume['credentials_num'],
                [HrBlackGreyListModel::BEHAVIOR_TYPE_GREY]);
            $request->delinquency_count = $count;
            $request->delinquency_list  = json_encode($list);
            $request->save();

            $extend['from_submit'] = [
                'sys_store_id'  => $request->worknode_id, //用人网点
                'department_id' => $request->department_id, //用人部门
            ];
            //创建审批流
            (new ApprovalServer($this->lang, $this->timeZone))->create($request->id,
                AuditListEnums::APPROVAL_TYPE_REEMPLOYMNET, $params['staff_info_id'], null, $extend);
            $this->db->commit();
            return $request->serial_no;
        } catch (\Exception $e) {
            $this->db->rollback();
            $this->logger->write_log("重新雇佣申请提交失败：".$e->getMessage());
            return false;
        }
    }

    /**
     * @param $params
     * @return void
     * @throws ValidationException
     */
    private function checkParams($params)
    {
        $validations = [
            'resume_id'       => "Required|Int",
            'job_title_grade' => "Required|IntGeLe:0,100",
            'staff_info_id'   => "Required|Int",
        ];
        Validation::validate($params, $validations);
        //
        $resume = (new ResumeRepository($this->timeZone))->getResumeInfoById($params['resume_id']);
        if (!$resume) {
            throw new ValidationException("resume not exists");
        }
        $hc = (new HcRepository($this->timeZone))->getHcInfo($resume['hc_id']);
        if (!$hc) {
            throw new ValidationException("hc not exists");
        }
        $staff = (new StaffRepository($this->timeZone))->getStaffInfo($params['staff_info_id']);
        if (!$staff) {
            throw new ValidationException("staff not exists");
        }
        $requestCount = ReemploymentRequestModel::count(
            [
                'conditions' => "resume_id = :resume_id:",
                'bind'       => ['resume_id' => $params['resume_id']],
            ]
        );
        if ($requestCount > 0) {
            throw new ValidationException($this->getTranslation()->_('reemployment_error_1'));
        }
    }

    /**
     * @param $params
     * @param $staffId
     * @return array
     * @throws \Exception
     */
    public function audit($params, $staffId)
    {
        $auditType   = AuditListEnums::APPROVAL_TYPE_REEMPLOYMNET;
        $approval_id = $staffId;//操作人工号
        $audit_id    = $params['audit_id'];
        $status      = intval($params['status']);

        $server = new ApprovalServer($this->lang, $this->timeZone);

        if ($status == enums::$audit_status['approved']) {//审核通过
            $res = $server->approval($audit_id, $auditType, $approval_id);
        } elseif ($status == enums::$audit_status['dismissed']) {//驳回
            $res = $server->reject($audit_id, $auditType, $params['reject_reason'], $approval_id);
        } elseif ($status == enums::$audit_status['revoked']) {//撤销
            $res = $server->cancel($audit_id, $auditType, $params['reason'], $approval_id);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }
        if (empty($res)) {
            $this->logger->write_log('audit_update_status error '.json_encode($params).json_encode($res), 'info');
            return $this->checkReturn(-3, 'server error');
        } else {
            return $this->checkReturn(1);
        }
    }

    /**
     * @inheritDoc
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $request = (new ReemploymentRepository($this->timeZone))->getRequestById($auditId);
        if (!$request) {
            return [];
        }

        $hc = (new HcRepository($this->timeZone))->getHcInfo($request['hc_id']);

        $deList = json_decode($request->delinquency_list, true);

        $head   = [
            'title'      => (new AuditlistRepository($this->lang,
                $this->timeZone))->getAudityType(AuditListEnums::APPROVAL_TYPE_REEMPLOYMNET),
            'id'         => $request->id,
            'staff_id'   => $request->submitter,
            'type'       => AuditListEnums::APPROVAL_TYPE_REEMPLOYMNET,
            'created_at' => DateHelper::utcToLocal($request->created_at),
            'updated_at' => DateHelper::utcToLocal($request->updated_at),
            'status'     => $request->state,
            'serial_no'  => $request->serial_no ?? '',
        ];
        $detail = [
            'reemployment_staff_name'       => $request->name,//姓名
            'reemployment_sex'              => $this->displaySex($request->sex),//性别
            'reemployment_age'              => $request->age,//年龄
            'reemployment_apply_job_title'  => $hc['job_name'],//应聘职位
            'reemployment_apply_department' => $hc['department_name'],//应聘部门
            'reemployment_apply_job_grade'  => $request['job_title_grade'],//职级
        ];

        $staffRepo = (new StaffRepository());
        $lastStaff = $staffRepo->getLastStaffInfoByIdentity($request['identity']);
        if ($lastStaff) {
            $leave_reason_map = (new ResignRepository($this->lang, $this->timeZone))->getLeaveReasonMap();
            $staff     = $staffRepo->getStaffInfoV4($lastStaff['staff_info_id']);
            $managerId = $staffRepo->getHightManager($lastStaff['staff_info_id']);
            if ($managerId) {
                $manager = $staffRepo->getStaffInfoV4($managerId);
            }
            $detail = array_merge($detail, [
                'last_job_name'        => $staff['job_name'] ?? '',
                'last_department_name' => $staff['department_name'] ?? '',
                'last_manager_info'    => !empty($manager) ? $manager['staff_name']."({$manager['staff_info_id']})" : '',
                'last_leave_type'      => $staff ? $this->t->t("leave_type_".$staff['leave_type']) : '',
                'last_leave_reason'    => $staff ? ($leave_reason_map[$staff['leave_reason']]??''):'',
            ]);
        }

        $detail = array_merge($detail, [
            'CVID' => $request->resume_id,
            'HCID' => $request->hc_id,
        ]);
        $detail           = $this->format($detail);
        $deList           = (new DelinquencyServer($this->lang,$this->timeZone))->displayList($deList);
        return [
            'data' => [
                'head'             => $head,
                'detail'           => $detail,
                'list'             => $deList,
            ],
        ];
    }

    /**
     * @inheritDoc
     */
    public function genSummary(int $auditId, $user)
    {
        $request = (new ReemploymentRepository($this->timeZone))->getRequestById($auditId);
        $hc      = (new HcRepository($this->timeZone))->getHcInfo($request['hc_id']);
        return [
            [
                'key'   => 'staff_name',
                'value' => $request->name ?? '',
            ],
            [
                'key'   => 'staff_job_title',
                'value' => $hc['job_name'] ?? '',
            ],
        ];
    }

    /**
     * @inheritDoc
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $request = (new ReemploymentRepository($this->timeZone))->getRequestById($auditId);
            if ($request) {
                $request->state = $state;
                $request->save();
            }
        }
    }

    /**
     * @inheritDoc
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $request = (new ReemploymentRepository($this->timeZone))->getRequestById($auditId);
        return [
            're_department_id'   => $request['department_id'] ?? '',
            're_job_title'       => $request['job_title'] ?? '',
            're_job_title_grade' => $request['job_title_grade'] ?? '',
        ];
    }

    private function displaySex($sex)
    {
        switch ($sex) {
            case '1':
                $txt = $this->t->t('4900');
                break;
            case '2':
                $txt = $this->t->t('4901');
                break;
            default:
                $txt = $this->t->t('4902');
                break;
        }
        return $txt;
    }

    /**
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false, false, false, false, false, false);
    }
}