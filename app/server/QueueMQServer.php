<?php
namespace FlashExpress\bi\App\Server;
use FlashExpress\bi\App\library\RocketMQ;

class QueueMQServer extends BaseServer
{

    protected $key = 'message-push';
    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang,$timezone);
    }

    /**
     * rmq: 向指定队列 - 写入消息数据
     * @param array $data 消息数据结构  示例： ['data'=>'xxxx']
     * @param $timeInSeconds
     * @param $key
     * @return mixed
     */
    public function sendToMsg(array $data, $timeInSeconds = 0, $key = null)
    {
        try {
            if (empty($key)) {
                $key = $this->key;
            }
            $rmq = new RocketMQ($key);
            $rid = $rmq->sendToMsg($data, $timeInSeconds);
            $this->logger->write_log('backyard rmq exception: ' . $rid, 'info');
        } catch (\Exception $e) {
            $this->logger->write_log('backyard rmq exception: ' . $e->getMessage(), 'error');
            return false;
        }
    }

}
