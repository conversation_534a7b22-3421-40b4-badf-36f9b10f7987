<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Models\backyard\HrBlackListModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Modules\My\enums\CompanyEnums as MyCompanyEnums;
use FlashExpress\bi\App\Modules\My\library\Enums\VehicleInfoEnums AS MyVehicleInfoEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HireTypeChangeFailedReasonModel;
use FlashExpress\bi\App\Models\backyard\HireTypeChangeInfoModel;
use FlashExpress\bi\App\Models\backyard\HireTypeImportListModel;
use FlashExpress\bi\App\Models\backyard\HrBlackGreyListModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\StaffDefaultRestDayModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\ShiftRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use Phalcon\Db;

class HireTypeChangeServer extends AuditBaseServer
{
    public $paramBean;
    public $staffInfo;
    public $storeInfo;
    public $regionPieceInfo;

    /**
     * @param array $paramIn
     * @return array
     * @throws ValidationException
     */
    public function addHireChange($paramIn = [])
    {
        $this->paramBean = $paramIn;
        $serialNo        = $this->getRandomId();
        //逻辑验证
        $this->checkApply();
        $insert['serial_no']          = empty($serialNo) ? null : "HTC{$serialNo}";
        $insert['staff_info_id']      = $this->paramBean['user_info']['id'];
        $insert['job_title']          = $this->staffInfo['job_title'];
        $insert['sys_department_id']  = $this->staffInfo['sys_department_id'];
        $insert['sys_store_id']       = $this->staffInfo['sys_store_id'];
        $insert['node_department_id'] = $this->staffInfo['node_department_id'];
        $insert['manage_region']      = $this->regionPieceInfo['region_id'];
        $insert['manage_piece']       = $this->regionPieceInfo['piece_id'];
        $insert['change_date']        = $this->paramBean['change_date'];
        $insert['remark']             = $this->paramBean['remark'] ?? '';
        $insert['file_url']           = $this->paramBean['file_url'];


        $db = $this->getDI()->get("db");
        try {
            $db->begin();
            $model = new HireTypeChangeInfoModel();
            $model->create($insert);

            $lastInsertId = $model->id;
            if (!$lastInsertId) {
                $db->rollBack();
                throw new \Exception('apply failed');
            }

            $flag = (new ApprovalServer($this->lang, $this->timeZone))->create($lastInsertId,
                AuditListEnums::APPROVAL_HIRE_TYPE_CHANGE, $this->paramBean['user_info']['id']);
            if (!$flag) {
                throw new \Exception('创建审批流失败');
            }

            $db->commit();
            return $this->checkReturn(['data' => $lastInsertId]);
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log('addHireChange error '.$e->getMessage());
            return $this->checkReturn(-3, $this->getTranslation()->_('2109'));
        }
    }

    //验证
    public function checkApply()
    {
        //权限
        $flag = $this->applyPermission($this->paramBean['user_info']['id']);
        if (!$flag) {
            throw new ValidationException($this->getTranslation()->_('2107'));
        }

        //日期
        $limitDate = date('Y-m-d', strtotime('+2 day'));
        if (empty($this->paramBean['change_date']) || $this->paramBean['change_date'] < $limitDate) {
            throw new ValidationException($this->getTranslation()->_('wrong_date_selected'));
        }

        //已经存在申请记录
        $data = HireTypeChangeInfoModel::find([
            'conditions' => 'staff_info_id = :staff_id: and status in ({status:array}) and change_status in ({change_status:array})',
            'bind'       => [
                'staff_id'      => $this->paramBean['user_info']['id'],
                'status'        => [enums::APPROVAL_STATUS_PENDING, enums::APPROVAL_STATUS_APPROVAL],
                'change_status' => [
                    HireTypeChangeInfoModel::CHANGE_STATUS_ING,
                    HireTypeChangeInfoModel::CHANGE_STATUS_SUCCESS,
                ],
            ],
        ])->toArray();

        if (!empty($data)) {
            throw new ValidationException($this->getTranslation()->_('hire_change_exist'));
        }

        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $this->staffInfo['sys_store_id']],
        ]);
        if (empty($storeInfo)) {
            throw new ValidationException('store info error');
        }
        $regionPieceRe = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
        $this->regionPieceInfo = $regionPieceRe->getOrganizationRegionPieceManagerId($storeInfo->id);

        $this->storeInfo = $storeInfo->toArray();
    }

    /**
     * @param $paramIn
     * @param $userinfo
     * @return bool
     * @throws ValidationException
     */
    public function updateStatus($paramIn, $userinfo = [])
    {
        $info = HireTypeChangeInfoModel::findFirst($paramIn['audit_id']);
        if (empty($info)) {
            throw new ValidationException("can not find info");
        }
        $info       = $info->toArray();
        $audit_type = AuditListEnums::APPROVAL_HIRE_TYPE_CHANGE;

        if ($info['status'] != enums::APPROVAL_STATUS_PENDING && empty($paramIn['is_tool'])) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        //撤销完了不能再撤销
        if ($info['status'] == enums::APPROVAL_STATUS_CANCEL) {
            throw new ValidationException($this->getTranslation()->_('4012'));
        }

        $res    = false;
        $server = new ApprovalServer($this->lang, $this->timeZone);
        if ($paramIn['status'] == enums::APPROVAL_STATUS_APPROVAL) {
            $res = $server->approval($paramIn['audit_id'], $audit_type, $userinfo['id']);
        } elseif ($paramIn['status'] == enums::APPROVAL_STATUS_REJECTED) {
            $res = $server->reject($paramIn['audit_id'], $audit_type, $paramIn['reject_reason'], $userinfo['id']);
        } elseif ($paramIn['status'] == enums::APPROVAL_STATUS_CANCEL){
            //工具或者任务撤销的情况
            if(!empty($paramIn['is_tool'])){
                //如果有审批流
                $server->cancel($paramIn['audit_id'], $audit_type, 'system tool cancel',$info['staff_info_id'],['super' => 1]);
            }else{
                //撤销审批流相关

            }
        }
        if ($res === false) {
            return $this->checkReturn(-3, 'server error');
        }

        return $this->checkReturn([]);
    }

    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $info = HireTypeChangeInfoModel::findFirst($auditId);
        if (empty($info)) {
            throw new ValidationException('[hire type change] info error');
        }
        $info = $info->toArray();
        //获取提交人用户信息
        $staff_info = (new StaffRepository())->getStaffPosition($info['staff_info_id']);
        $storeInfo  = SysStoreModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $info['sys_store_id']],
        ]);
        $regionPieceInfo = [];
        if(!empty($storeInfo)){
            $regionPieceRe = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
            $regionPieceInfo = $regionPieceRe->getOrganizationRegionPieceManagerId($storeInfo->id);
        }

        //职位
        $jobInfo = HrJobTitleModel::findFirst($info['job_title']);

        $detailLists['apply_parson']       = $staff_info['name']. "({$staff_info['staff_info_id']})";
        $detailLists['department']         = $staff_info['depart_name'];
        $detailLists['dot']                = $storeInfo->name ?? '';
        $detailLists['manage_region_name'] = $regionPieceInfo['region_name'] ?? '';
        $detailLists['manage_piece_name']  = $regionPieceInfo['piece_name'] ?? '';
        $detailLists['staff_job_title']    = empty($jobInfo) ? '' : $jobInfo->job_name;
        $detailLists['change_date']        = $info['change_date'];


        $addHour       = $this->config->application->add_hour;
        $audit_list_re = new AuditlistRepository($this->lang, $this->timeZone);
        $data          = [
            'title'       => $audit_list_re->getAudityType(AuditListEnums::APPROVAL_HIRE_TYPE_CHANGE),
            'id'          => $info['id'],
            'staff_id'    => $info['staff_info_id'],
            'type'        => AuditListEnums::APPROVAL_HIRE_TYPE_CHANGE,
            'created_at'  => date('Y-m-d H:i:s', strtotime($info['created_at']) + $addHour * 3600),
            'updated_at'  => date('Y-m-d H:i:s', strtotime($info['updated_at']) + $addHour * 3600),
            'status'      => $info['status'],
            'status_text' => $audit_list_re->getAuditStatus('10'.$info['status']),
            'serial_no'   => $info['serial_no'] ?? '',
        ];

        $returnData['data']['detail'] = $this->format($detailLists);
        //pdf  特殊处理
        $item['url']   = $info['file_url'];
        $item['key']   = 'confirm_file';//冒号前面翻译key
        $item['value'] = $this->getTranslation()->_('6112');//冒号后面链接名称
        $fileData[]    = $item;
        $pdfItem       = $this->formatDetailPdf($fileData);

        $returnData['data']['detail'] = array_merge($returnData['data']['detail'], $pdfItem);

        $returnData['data']['head'] = $data;
        return $returnData;
    }

    /**
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        $rule = new AuditOptionRule(false, false, false, false, false, false);
        return $rule;
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        //获取加班详情
        $info = HireTypeChangeInfoModel::findFirst($auditId);
        if (empty($info)) {
            return '';
        }

        $info = $info->toArray();
        //职位
        $jobInfo       = HrJobTitleModel::findFirst($info['job_title']);
        $item['key']   = 'staff_job_title';
        $item['value'] = empty($jobInfo) ? '' : $jobInfo->job_name;
        $return[]      = $item;

        if (empty($info['sys_store_id']) || $info['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            return $return;
        }

        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $info['sys_store_id']],
        ]);
        if (empty($storeInfo)) {
            return $return;
        }
        $regionPieceRe = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
        $regionPieceInfo = $regionPieceRe->getOrganizationRegionPieceManagerId($storeInfo->id);

        //大区名称
        if (!empty($regionPieceInfo['region_id'])) {
            $item['key']   = 'manage_region_name';
            $item['value'] = $regionPieceInfo['region_name'];
            $return[]      = $item;
        }
        //片区
        if (!empty($regionPieceInfo['piece_id'])) {
            $item['key']   = 'manage_piece_name';
            $item['value'] = $regionPieceInfo['piece_name'];
            $return[]      = $item;
        }

        //网点
        $item['key']   = 'dot';
        $item['value'] = $storeInfo->name;
        $return[]      = $item;

        return $return;
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $info = HireTypeChangeInfoModel::findFirst($auditId);
            $update['status'] = $state;
            //如果是驳回 修改转型状态 为失败
            if($state == enums::APPROVAL_STATUS_REJECTED){
                $update['change_status'] = HireTypeChangeInfoModel::CHANGE_STATUS_FAILED;
            }
            $info->update($update);

        }

        return true;
    }

    /**
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        return [];
    }


    //申请权限判断
    public function applyPermission($staffId = 0)
    {
        //获取配置 职位
        $settingEnvServer = new SettingEnvServer();
        $jobIds           = $settingEnvServer->getSetVal('hire_change_job_titles');
        $jobIds           = empty($jobIds) ? [] : explode(',', $jobIds);
        $this->logger->write_log("hire_type_change setting ".json_encode($jobIds), 'info');
        if (empty($jobIds)) {
            return false;
        }

        //只针对Formal=1并且雇佣类型不等于个人代理的员工生效，外协、子账号均不展示此入口
        if (!empty($staffId)) {
            $staffInfo       = HrStaffInfoModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind'       => ['staff_info_id' => $staffId],
            ]);
            $this->staffInfo = empty($staffInfo) ? [] : $staffInfo->toArray();
        }

        if (empty($this->staffInfo)) {
            return false;
        }

        if(!in_array($this->staffInfo['job_title'],$jobIds)){
            return false;
        }

        if ($this->staffInfo['formal'] != HrStaffInfoModel::FORMAL_1) {
            return false;
        }

        if (in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
            return false;
        }

        if ($this->staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            return false;
        }

        if ($this->staffInfo['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            return false;
        }

        return true;
    }

    //生成pdf
    public function getPdf($param)
    {
        $path = APP_PATH."/views/hirechange/hire_change.ftl";
        //上传oss 更新表
        $result        = $this->uploadFileOss($path, self::OSS_DIR_MAPS[self::HRIS_STAFF_PROFILE]);
        $url           = $result['object_url'];//oss地址
        $formPdfServer = new formPdfServer();
        $imgData       = [];
        //图片参数
        if (!empty($param['sign_url'])) {
            $imgData[] = ['name' => 'sign_url', 'url' => $param['sign_url']];
        }

        //参数
        $data['staff_id']    = (string)$param['user_info']['id'];
        $data['staff_name']  = $param['user_info']['name'];
        $data['change_date'] = date('Y-m-d');

        $headerTemplate            = file_get_contents(APP_PATH."/views/hirechange/hire_header.html");
        $footerTemplate            = file_get_contents(APP_PATH."/views/hirechange/hire_footer.html");
        $pdf_header_footer_setting = [
            'displayHeaderFooter' => true,
            'headerTemplate'      => $headerTemplate,
            'footerTemplate'      => $footerTemplate,
        ];

        $pdfName = $data['staff_id'] . '_' . date('Y-m-d');
        $res                = $formPdfServer->generatePdf($url, $data, $imgData, $pdfName, $pdf_header_footer_setting, 'attchment');
        $return['file_url'] = empty($res['object_url']) ? '' : $res['object_url'];
        return $return;
    }


    //超时关闭
    public function timeout_invalid($data)
    {
        //所有记录都 改成 超时关闭
        $ids = array_column($data, 'id');
        HireTypeChangeInfoModel::find([
            'conditions' => 'id in ({ids:array})',
            'bind'       => ['ids' => $ids],
        ])->update(['status' => enums::APPROVAL_STATUS_TIMEOUT, 'change_status' => HireTypeChangeInfoModel::CHANGE_STATUS_FAILED]);

        //apply
        $approvalData = AuditApplyModel::find([
            'conditions' => 'biz_type = :biz_type: and biz_value in ({ids:array})',
            'bind'       => ['biz_type' => AuditListEnums::APPROVAL_HIRE_TYPE_CHANGE, 'ids' => $ids],
        ])->toArray();

        if (empty($approvalData)) {
            $this->logger->write_log("hire_change_timeout_invalid 没有 apply 记录", 'info');
            return true;
        }
        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);
        $reasonModel    = new HireTypeChangeFailedReasonModel();
        foreach ($approvalData as $da) {
            $result = $approvalServer->timeOut($da['biz_value'], $da['biz_type']);
            if ($result === false) {
                $this->logger->write_log('hire_change_timeout_invalid 失败'.$da['biz_value'].$da['biz_type']);
            }

            //失败原因
            $row['change_id']   = $da['biz_value'];
            $row['reason_type'] = HireTypeChangeFailedReasonModel::CHANGE_STATUS_TIMEOUT;
            $row['reason_key']  = HireTypeChangeFailedReasonModel::$reasonArr[HireTypeChangeFailedReasonModel::CHANGE_STATUS_TIMEOUT];
            $clone              = clone $reasonModel;
            $clone->create($row);
        }
        return true;
    }

    /**
     * @description 入职列表
     * @param $params
     * @param $staff_id
     * @return array
     */
    public function getAgentEntryList($params, $staff_id): array
    {
        $params['page_num']  = empty($params['page_num']) ? CommonEnums::DEFAULT_PAGE_NUM: $params['page_num'];
        $params['page_size'] = empty($params['page_size']) ? CommonEnums::DEFAULT_PAGE_SIZE : $params['page_size'];
        $params['page_size'] = min(CommonEnums::MAX_PAGE_SIZE, $params['page_size']);

        //获取Builder
        $builder = $this->getQueryBuilder($params, $staff_id);

        //获取count
        $total = $builder->columns('count(1) as cou')->getQuery()->getSingleResult()->cou;

        //获取List
        $builder->limit($params['page_size'], $params['page_size'] * ($params['page_num'] - 1));
        $builder->columns($this->getColumns());
        $builder->orderBy('h.id desc');
        $list = $builder->getQuery()->execute()->toArray();
        if ($list) {
            $list = $this->formatListData($list);
        }

        $total = !empty($total) ? intval($total) : 0;
        $data['pagination'] = [
            'count'     => $total,
            'pageCount' => ceil($total / $params['page_size']),
            'pageNum'   => intval($params['page_num']),
            'pageSize'  => $params['page_size'],
        ];
        $data['dataList']  = $list;

        return $data;
    }

    /**
     * 获取个人代理builder
     * @param $params
     * @param $staff_id
     * @return mixed
     */
    private function getQueryBuilder($params, $staff_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['h' => HireTypeImportListModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = h.old_staff_id', 'hsi');
        $builder->andWhere('h.entry_state = :entry_state:', ['entry_state' => $params['status']]);
        $builder->andWhere('deleted = 0');

        $staffInfo = (new StaffServer())->getStaffById($staff_id);
        [$deptIds, $storeIds] = $this->getStaffPermission($staff_id);
        if ($staffInfo['sys_store_id'] != enums::HEAD_OFFICE_ID) {//不是总部网点，则查自己所在网点
            $storeIds[] = $staffInfo['sys_store_id'];
        }

        //权限
        $conditions           = '';
        $bindParams['manger'] = $staff_id;
        if(!empty($storeIds)) {
            $conditions .= ' or hsi.sys_store_id in ({store_ids:array})';
            $bindParams['store_ids'] = $storeIds;
        }
        if(!empty($deptIds)) {
            $conditions .= ' or hsi.node_department_id in ({dept_ids:array})';
            $bindParams['dept_ids'] = $deptIds;
        }
        $builder->andWhere('hsi.manger = :manger:' . $conditions, $bindParams);

        return $builder;
    }

    /**
     * @description 获取管辖权限
     * @param $staff_info_id
     * @return array
     */
    public function getStaffPermission($staff_info_id): array
    {
        $deptIds  = (new DepartmentRepository())->getStaffOrganizationInfo($staff_info_id);
        $storeIds = (new SysStoreServer())->getStoreByManager($staff_info_id);
        return [$deptIds, $storeIds];
    }

    /**
     * @description 获取字段
     * @return string[]
     */
    private function getColumns(): array
    {
        return [
            'hsi.name as staff_name',
            'h.id',
            'h.new_staff_job_title as job_title',
            'h.expected_entry_date',
            'h.actual_entry_date',
            'h.hc_id',
            'h.entry_state',
        ];
    }

    /**
     * @description 组织list
     * @param $list
     * @return array
     */
    private function formatListData($list): array
    {
        if (empty($list)) {
            return [];
        }

        $hcIds       = array_values(array_unique(array_column($list, 'hc_id')));
        $jobTitleIds = array_values(array_unique(array_column($list, 'job_title')));

        //获取职位
        if (!empty($jobTitleIds)) {
            $jobTitleInfo = HrJobTitleModel::find([
                'conditions' => 'id in({ids:array})',
                'bind'       => ['ids' => $jobTitleIds],
                'columns'    => 'id,job_name',
            ])->toArray();
            $jobTitleInfoList = array_column($jobTitleInfo, 'job_name', 'id');
        }

        //获取HC信息
        if (!empty($hcIds)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(['h.hc_id', 'h.worknode_id','s.name as store_name', 'j.job_name']);
            $builder->from(['h' => HrhcModel::class]);
            $builder->leftJoin(SysStoreModel::class, 'h.worknode_id = s.id', 's');
            $builder->leftJoin(HrJobTitleModel::class, 'h.job_title = j.id', 'j');
            $builder->inWhere('h.hc_id', $hcIds);
            $hcInfo = $builder->getQuery()->execute()->toArray();
            $hcInfoList = array_column($hcInfo, null, 'hc_id');
        }

        $data = [];
        foreach ($list as $item) {
            $info = $hcInfoList[$item['hc_id']] ?? [];

            if ($item['entry_state'] == HrEntryModel::STATUS_EMPLOYED) { //已入职
                $date = $item['actual_entry_date'];
            } else {
                $date = $item['expected_entry_date'];
            }

            $tmp['id']            = $item['id'];
            $tmp['position_id']   = $item['job_title'];
            $tmp['work_time']     = $tmp['entry_date'] = $date;
            $tmp['name']          = $item['staff_name'];
            $tmp['worknode_id']   = $info['worknode_id'] ?? '';
            $tmp['position_name'] = $jobTitleInfoList[$item['job_title']] ?? '';

            $data[] = $tmp;
        }
        return $data;
    }

    /**
     * @description 获取到岗详情
     * @param $id
     * @return array
     * @throws ValidationException
     */
    public function getAgentEntryDetail($id): array
    {
        //获取详情
        $detail = HireTypeImportListModel::findFirst($id);
        if (empty($detail)) {
            return [];
        }
        if ($detail->deleted == enums::DATA_DELETED) {
            throw new ValidationException($this->getTranslation()->_('err_msg_go_back_and_refresh'));
        }

        //获取员工信息
        $staffServer = new StaffServer();
        $columns = 'staff_info_id,name,sex,identity,node_department_id as department_id,job_title as position_id,
            sys_store_id,hire_date,week_working_day,rest_type,sys_store_id';
        $staffInfo = $staffServer->getStaffInfo(['staff_info_id' => $detail->old_staff_id], $columns);

        //获取生日
        $staffItems = (new StaffRepository())->getSpecStaffItemsInfo($detail->old_staff_id, ['BIRTHDAY']);
        if (isset($staffItems['BIRTHDAY']) && !empty($staffItems['BIRTHDAY'])) {
            $staffInfo['age'] = DateHelper::howOld($staffItems['BIRTHDAY']);
        } else {
            $staffInfo['age'] = '';
        }

        //获取头像
        $staffInfo['avator'] = (new AuditlistRepository($this->lang,$this->timeZone))->getPersonAvator($detail->old_staff_id);

        $hcInfo = HrHcModel::findFirst([
            'conditions' => 'hc_id = :hc_id:',
            'bind'       => ['hc_id' => $detail->hc_id],
        ]);
        if (empty($hcInfo)) {
            throw new ValidationException('hc is not exists:'. $detail->hc_id);
        }

        $staffInfo['mobile'] = $detail->receive_offer_mobile ?: '';
        //职位名
        $jobTitleInfo = (new HrJobTitleModel())->getOneById($hcInfo->job_title);
        $staffInfo['position_name'] = $jobTitleInfo['job_name'] ?? '';

        //部门名
        $departmentInfoName = (new DepartmentRepository())->getDepartmentNameById($hcInfo->department_id);
        $staffInfo['department_name'] = $departmentInfoName;

        //性别
        $staffInfo['sex_title'] = $staffInfo['sex'] == enums::$sex_type['male']
            ? $this->getTranslation()->_('4900')
            : $this->getTranslation()->_('4901');

        $staffInfo['hire_date'] = date('Y-m-d', strtotime($staffInfo['hire_date']));

        //网点名
        $storeInfo = (new SysStoreServer())->getStoreInfoByid($hcInfo->worknode_id);
        $staffInfo['store_name'] = $storeInfo['name'] ?? '';

        //获取班次信息
        $configShiftList = $this->getShitList($hcInfo->department_id, $hcInfo->job_title, $hcInfo->worknode_id);
        $staffInfo['config_shift_list']  = $configShiftList;
        $staffInfo['work_time']         = $detail->entry_state == HrEntryModel::STATUS_EMPLOYED ? $detail->actual_entry_date: $detail->expected_entry_date;
        $staffInfo['status']            = $detail->entry_state;
        $staffInfo['store_manager_id']  = $storeInfo['manager_id'] ?? '';
        if (!empty($staffInfo['store_manager_id'])) {
            $storeManagerInfo = $staffServer->getStaffInfo(['staff_info_id' => $staffInfo['store_manager_id']], $columns);
            $staffInfo['store_manager_name']  = $storeManagerInfo['name'] ?? '';
        } else {
            $staffInfo['store_manager_name']  = '';
        }
        $staffInfo['entry_operator_id']   = $detail->entry_confirm_operator_id ?? 0;
        if (!empty($staffInfo['entry_operator_id'])) {
            $entryOperatorInfo = $staffServer->getStaffInfo(['staff_info_id' => $staffInfo['entry_operator_id']], $columns);
            $staffInfo['entry_operator_name']  = $entryOperatorInfo['name'] ?? '';
        } else {
            $staffInfo['entry_operator_name'] = '';
        }

        if ($detail->new_staff_job_title != $staffInfo['position_id']) {
            $staffInfo['week_working_day']      = HrStaffInfoModel::WEEK_WORKING_DAY_6;
            $staffInfo['working_day_rest_type'] = HrStaffInfoModel::WEEK_WORKING_DAY_6 . HrStaffInfoModel::REST_TYPE_1;
        } else {
            $staffInfo['working_day_rest_type'] = $staffInfo['week_working_day'] . $staffInfo['rest_type'];
        }
        $staffInfo['working_day_rest_name'] = '';
        if ($staffInfo['working_day_rest_type']) {
            $staffInfo['working_day_rest_name'] = $this->getTranslation()->_('working_day_rest_type_'.$staffInfo['working_day_rest_type']);
        }
        $staffInfo['default_rest_day_date'] = $detail->default_rest_day_date ? explode(',', $detail->default_rest_day_date): [];
        $staffInfo['default_rest_day_list'] = $this->getDefaultRestDay();

        //获取派件码
        $staffServer                     = Tools::reBuildCountryInstance(new StaffServer($this->lang, $this->timeZone),
            [$this->lang, $this->timeZone]);
        $staffInfo['delivery_code_info'] = $staffServer->getDeliveryCode($staffInfo['sys_store_id']);
        $staffInfo['delivery_code']      = $detail->delivery_code ? : '';
        if (isCountry('PH') && in_array($detail->new_staff_job_title, $staffServer->getShowDeliveryCodeJobTitle())) {
            $staffInfo['is_show_delivery_code'] = 1; //显示
        } else {
            $staffInfo['is_show_delivery_code'] = 0; //不显示
        }

        if (isCountry('MY')){
            // 产品要求my 区号写死60
            $staffInfo['phone_area_code']        = '60';
        }

        if ($detail->entry_state == HrEntryModel::STATUS_EMPLOYED) {
            if (isCountry('MY')) {
                $repo = new ShiftRepository($this->timeZone);
                $shiftInfo = $repo->getV2ShiftInfo($detail->shift_id);
                $staffInfo['shift_type_text'] = $shiftInfo['shift_name'] ?? '';
                $staffInfo['shift_work_time'] = $shiftInfo['shift_work_time'] ?? '';
                return $staffInfo;
            }

            $shiftInfo = HrShiftModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => [
                    'id' => $detail->shift_id,
                ],
            ]);
            if (empty($shiftInfo)) {
                $staffInfo['shift_id']        = $detail->shift_id;
                $staffInfo['shift_type']      = '';
                $staffInfo['shift_type_text'] = '';
                $staffInfo['shift_text']      = '';
                return $staffInfo;
            }
            if (!empty($shiftInfo->type)) {
                $key       = 'shift_' . strtolower($shiftInfo->type);
                $shiftName = $this->getTranslation()->_($key);
            }
            $staffInfo['shift_id']        = $shiftInfo->id;
            $staffInfo['shift_type']      = $shiftInfo->type;
            $staffInfo['shift_type_text'] = $shiftName ?? '';
            $staffInfo['shift_text']      = $shiftInfo->start . '-' . $shiftInfo->end;
        }
        //获取 自由轮休配置天数
        if (isCountry('PH') && $staffInfo['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_FREE) {
            $workdayServer                     = new WorkdaySettingServer($this->lang, $this->timeZone);
            $setting                           = $workdayServer->getFreeLeaveConfig($staffInfo['sys_store_id'],
                $detail->new_staff_job_title);
            $staffInfo['free_workday_num']     = $setting ? $setting->days_num : null;
            $staffInfo['max_free_workday_num'] = $setting ? $setting->max_days_num : null;
        }

        return $staffInfo;
    }

    /**
     * @description 个人代理到岗确认
     * @param $params
     * @param $staff_info_id
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function agentEntryConfirm($params, $staff_info_id): bool
    {
        $detail = HireTypeImportListModel::findFirst($params['id']);
        if (empty($detail)) {
            return false;
        }

        if ($detail->deleted == enums::DATA_DELETED) {
            throw new ValidationException($this->getTranslation()->_('err_msg_go_back_and_refresh'));
        }

        if ($params['status'] == 1) { //到岗
            //获取员工信息
            $staffInfo = (new HrStaffInfoModel())->getOneByStaffId($detail->old_staff_id);
            if (empty($staffInfo)) {
                return false;
            }
            if ($staffInfo['state'] != HrStaffInfoModel::STATE_RESIGN) {
                throw new ValidationException($this->getTranslation()->_('err_msg_staff_not_leave'));
            }
            $defaultRestDayDate = null;
            if (!empty($params['default_rest_day_date'])) {
                if (!is_array($params['default_rest_day_date'])) {
                    throw new ValidationException($this->getTranslation()->_('miles_C100100'));
                }
                $defaultRestDayDate = $params['default_rest_day_date'];
            }
            //跟 detail 逻辑 一致 getAgentEntryDetail
            if ($detail->new_staff_job_title != $staffInfo['job_title']) {
                $staffInfo['week_working_day'] = HrStaffInfoModel::WEEK_WORKING_DAY_6;
            }
            if(isCountry('PH') && $staffInfo['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_FREE){
                $workdayServer = new WorkdaySettingServer($this->lang, $this->timeZone);
                $setting = $workdayServer->getFreeLeaveConfig($staffInfo['sys_store_id'], $detail->new_staff_job_title);

                if ($setting && $setting->days_num && $setting->days_num > count($params['default_rest_day_date'])) {
                    throw new ValidationException($this->getTranslation()->_('default_less_setting_days',
                        ['days_num' => $setting->days_num]));
                }
                if ($setting && $setting->max_days_num && $setting->max_days_num < count($params['default_rest_day_date'])) {
                    throw new ValidationException($this->getTranslation()->_('default_more_then_setting_days',
                        ['days_num' => $setting->max_days_num]));
                }
            }
            $deliveryCode = null;
            $storeId      = null;
            if (!empty($params['delivery_code'])) {
                if (!is_array($params['delivery_code'])) {
                    throw new ValidationException($this->getTranslation()->_('miles_C100100'));
                }
                $deliveryCode = $params['delivery_code'];
                $storeId      = $params['store_id'];
            }

            $checkHcInfo = HrHcModel::findFirst([
                'conditions' => 'hc_id = :hc_id:',
                'bind'       => ['hc_id' => $detail->hc_id],
            ]);

            // 校验黑名单
            if (isCountry('TH')){
                $black_list = $this->getBlackGreyList([$detail->old_staff_id]);
            }else{
                $black_list = $this->getBlackList([$detail->old_staff_id]);
            }
            if (isCountry(['TH','MY','PH']) && isset($staffInfo['identity']) && in_array($staffInfo['identity'], $black_list)) {
                throw new ValidationException($this->getTranslation()->_('err_msg.1009'));
            }
            if (!empty($staffInfo['identity'])) {
                $outsourcingBlackList = (new OutsourcingBlackListServer())->check($staffInfo['identity'], 'winhr', false,$this->lang);
                if ($outsourcingBlackList && $outsourcingBlackList['is_black']){
                    throw new BusinessException($outsourcingBlackList['tip']);
                }
            }

            if (empty($checkHcInfo)) {
                throw new ValidationException('hc is not exists:'. $detail->hc_id);
            }

            $entryParams = [
                'staff_info_id'         => $detail->old_staff_id,
                'hire_date'             => date('Y-m-d'),
                'job_title_id'          => $detail->new_staff_job_title,
                'shift_id'              => $params['shift_id'],
                'mobile'                => $detail->receive_offer_mobile,
                'is_job_title_changed'  => $detail->new_staff_job_title != $staffInfo['job_title'],
                'hire_times'            => $checkHcInfo->hire_times,
                'default_rest_day_date' => $defaultRestDayDate,
                'delivery_code'         => $deliveryCode,
                'store_id'              => $storeId,
                'data_type'             => $detail->data_type,
            ];
            return $this->doEntry($entryParams, $staff_info_id);
        }

        $db = $this->getDI()->get("db");

        try {
            $db->begin();

            //返还HC
            $hcInfo = HrHcModel::findFirst([
                'conditions' => 'hc_id = :hc_id:',
                'bind'       => ['hc_id' => $detail->hc_id],
                'for_update' => true,
            ]);
            if (empty($hcInfo)) {
                throw new ValidationException('hc is not exists:'. $detail->hc_id);
            }
            if ($hcInfo->submitter_id == enums::SYSTEM_STAFF_ID) {
                $hcInfo->surplusnumber       = new \Phalcon\Db\RawValue('surplusnumber + 1');
                $hcInfo->state_code = HrHcModel::STATE_VOIDED;
                $updateHcData['hc_last_operator'] = enums::SYSTEM_STAFF_ID;
            } else {
                $hcInfo->surplusnumber       = new \Phalcon\Db\RawValue('surplusnumber + 1');
                $updateHcData['surplusnumber'] = $hcInfo->surplusnumber;
                if ($hcInfo->state_code == HrHcModel::STATE_FULL_RECRUITMENT) {
                    $hcInfo->state_code = HrHcModel::STATE_RECRUITING;
                }
                $updateHcData['hc_last_operator'] = $staff_info_id;
            }
            $updateHcData['state_code']       = $hcInfo->state_code;
            //操作记录
            (new HcServer($this->lang, $this->timeZone))->addHrhcSaveLog($detail->hc_id, $updateHcData['hc_last_operator'],$updateHcData);

            $hcInfo->save();
            if ($hcInfo->getMessages()) {
                throw new \Exception(implode(' ', $hcInfo->getMessages()));
            }

            //更改状态
            $detail->entry_state = HrEntryModel::STATUS_NOT_EMPLOYED;
            $detail->entry_confirm_operator_id = $staff_info_id;
            $detail->save();
            if ($detail->getMessages()) {
                throw new \Exception(implode(' ', $detail->getMessages()));
            }

            $db->commit();
        } catch (ValidationException $ve) {
            $db->rollback();
            $this->logger->write_log('agentEntryConfirm err' . $ve->getMessage(). $ve->getTraceAsString());
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->write_log('agentEntryConfirm err' . $e->getMessage(). $e->getTraceAsString());
        }

        return true;
    }

    /**
     * 根据工号获取黑名单
     * @param $staff_ids
     * @return array
     */
    public function getBlackList($staff_ids = [])
    {
        if (empty($staff_ids)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("black.identity");
        $builder->from(['black' => HrBlackListModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'black.identity = staff.identity', 'staff');
        $builder->Where(
            'black.status = :status: and staff.staff_info_id in ({staff_info_ids:array})',
            [
                'status'         => HrBlacklistModel::STATUS_EFFICIENT,
                'staff_info_ids' => $staff_ids,
            ]
        );
        $data = $builder->getQuery()->execute()->toArray();
        return array_column($data, 'identity','identity');
    }

    /**
     * 根据工号获取黑名单
     * @param $staff_ids
     * @return array
     */
    public function getBlackGreyList($staff_ids = [])
    {
        if (empty($staff_ids)) {
            return [];
        }
        // 获取个人代理过滤黑名单小类code 配置
        $agent_black_list_category = (new SettingEnvServer())->getSetVal('resume_hire_type_agent_black_list_category');
        $agent_black_list_category_arr = $agent_black_list_category ? explode(',',$agent_black_list_category) : [];
        if (!$agent_black_list_category_arr){
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("black_grey.identity");
        $builder->from(['black_grey' => HrBlackGreyListModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'black_grey.identity = staff.identity', 'staff');
        $builder->Where(
            'black_grey.identity != \'\' and black_grey.status = :status: and staff.staff_info_id in ({staff_info_ids:array}) and black_grey.category in ({agent_black_list_category_arr:array})',
            [
                'status'         => HrBlackGreyListModel::STATUS_ON,
                'staff_info_ids' => $staff_ids,
                'agent_black_list_category_arr' => $agent_black_list_category_arr,
            ]
        );
        $data = $builder->getQuery()->execute()->toArray();
        return array_column($data, 'identity','identity');
    }

    /**
     * @description 到岗确认
     * @param $params
     * @param $operator_id
     * @return bool
     * @throws BusinessException
     */
    private function doEntry($params, $operator_id): bool
    {
        $staff_info_id = $params['staff_info_id'];

        //获取原车辆类型
        $vehicleObj = VehicleInfoModel::findFirst([
            'conditions' => 'uid = :staff_info_id:',
            'bind'       => ['staff_info_id' => $staff_info_id],
        ]);

        $staffInfoObj = HrStaffInfoModel::findFirstByStaffInfoId($staff_info_id);
        if (empty($staffInfoObj)) {
            throw new BusinessException('工号不存在', 10000);
        }
        $staffInfo     = $staffInfoObj->toArray();
        $staffItemsObj = HrStaffItemsModel::findByStaffInfoId($staff_info_id);


        if ($staffItemsObj) {
            $staffItemsInfo = $staffItemsObj->toArray();
            $vehicleType    = $this->getVehicleType($params['job_title_id']);
            foreach ($staffItemsInfo as $item) {
                //银行卡号以hr_staff_info表为准
                if (strtolower($item['item']) == 'bank_no' && !empty($staffInfo['bank_no'])) {
                    continue;
                }
                if (strtolower($item['item']) == 'car_type' && !is_null($vehicleType)) {
                    $staffInfo['staff_car_type'] = $this->getCarType($vehicleType);
                }
                if (isCountry('MY')) {
                    if (strtolower($item['item']) == 'vehicle_type_category') {
                        $vehicleTypeKey = array_search($item['value'], enums::$vehicleTypeCategoryMY);
                        $staffInfo['vehicle_type_category'] = $vehicleTypeKey ? :0;
                    } else {
                        $staffInfo[strtolower($item['item'])] = $item['value'];
                    }
                } else {
                    $staffInfo[strtolower($item['item'])] = $item['value'];
                }
            }
        }
        unset($staffInfo['created_at']);
        unset($staffInfo['updated_at']);
        unset($staffInfo['staff_info_id']);
        unset($staffInfo['leave_source']);
        unset($staffInfo['leave_type']);
        unset($staffInfo['leave_reason_remark']);
        unset($staffInfo['stop_payment_type']);
        unset($staffInfo['staff_type']);
        unset($staffInfo['stop_duties_count']);
        unset($staffInfo['is_auto_system_change']);
        unset($staffInfo['leave_reason']);
        unset($staffInfo['leave_scenario']);
        unset($staffInfo['payment_markup']);
        unset($staffInfo['payment_state']);
        unset($staffInfo['hire_date_origin']);
        unset($staffInfo['hire_times']);
        unset($staffInfo['social_security_leave_date']);
        $staffInfo['job_title']             = $params['job_title_id'];
        $staffInfo['mobile']                = $params['mobile'];
        $staffInfo['hire_date']             = $params['hire_date'];
        $staffInfo['fbid']                  = $operator_id;
        $staffInfo['state']                 = HrStaffInfoModel::STATE_ON_JOB;
        $staffInfo['is_history_leave']      = 0;
        $staffInfo['wait_leave_state']      = HrStaffInfoModel::WAITING_LEAVE_NO;
        $staffInfo['manager']               = $staffInfo['manger'];//上级
        $staffInfo['formal']                = HrStaffInfoModel::FORMAL_1;
        $staffInfo['email']                 = '';
        $staffInfo['payment_markup']        = '';
        $staffInfo['mobile_company']        = '';//企业号码
        $staffInfo['uuid']                  = null;
        $staffInfo['entry_id']              = 0;
        $staffInfo['stop_duties_count']     = 0;
        $staffInfo['average_tenure']        = 0;
        $staffInfo['hire_type']             = $params['data_type'] == HireTypeImportListModel::DATA_TYPE_IC ? HrStaffInfoModel::HIRE_TYPE_UN_PAID : HrStaffInfoModel::HIRE_TYPE_2;
        $staffInfo['working_day_rest_type'] = $staffInfo['week_working_day'] . $staffInfo['rest_type'];
        $staffInfo['hire_times']            = $params['hire_times'] ?? 12;
        $staffInfo['contract_expiry_date']  = date('Y-m-d',
            strtotime("+ ". $staffInfo['hire_times'] ." month -1 day", strtotime($params['hire_date']))); //合同到期日
        $staffInfo['shift_id']              = $params['shift_id'];
        $staffInfo['vehicle_source']        = $this->getVehicleSource($vehicleObj,$params['job_title_id']);//车辆来源
        if ($params['is_job_title_changed']) { // 职位如果变更，默认角色为快递员 6天班轮休
            $staffInfo['position_category']     = [RolesModel::ROLE_COURIER];
            $staffInfo['working_day_rest_type'] = HrStaffInfoModel::WEEK_WORKING_DAY_6 . HrStaffInfoModel::REST_TYPE_1;
        } else {
            $staffPositionObj               = HrStaffInfoPositionModel::findByStaffInfoId($staff_info_id);
            $staffInfo['position_category'] = array_column($staffPositionObj->toArray(), 'position_category');
        }

        if (isCountry('MY')) {
            $staffInfo['contract_company_id'] = $params['data_type'] == HireTypeImportListModel::DATA_TYPE_LNT ? MyCompanyEnums::LNT_ID : 0;
        }

        $ac                             = new ApiClient('hr_rpc', '', 'staffs-create', $this->lang);
        $ac->setParams($staffInfo);
        $data = $ac->execute();
        if ($data['result']['code'] != 0 || empty($new_staff_info_id = $data['result']['body']['staff_info_id'])) {
            throw new BusinessException($data['result']['msg']);
        }
        $this->logger->write_log(['create_new_staff_success' => $new_staff_info_id], 'info');

        $type = $params['data_type'] == HireTypeImportListModel::DATA_TYPE_LNT ? 'lnt_export' : 'agent_export';
        //插入电子合同
        $data = ['type' => $type, 'staff_info_id' => $new_staff_info_id];
        $rmq  = new RocketMQ('hr-contract-add');
        $rid  = $rmq->sendToMsg($data);
        $this->logger->write_log('hr-contract-transfer rid:' . $rid . 'data:' . json_encode($data),
            $rid ? 'info' : 'error');

        //同步派件码
        try {
            if (isCountry('PH') &&
                isset($params['store_id']) && isset($params['delivery_code']) &&
                !empty($params['store_id']) && !empty($params['delivery_code'])) {
                $paramsArray = [
                    'staff_id'       => $new_staff_info_id,
                    'operator_id'    => $operator_id,
                    'store_id'       => $params['store_id'],
                    'delivery_codes' => $params['delivery_code'],
                ];
                (new DeliveryCodeServer())->bindDeliveryCode($paramsArray);
            }
        } catch (\Exception $e) {
            $this->logger->write_log('bindDeliveryCodee err' . $e->getMessage());
        }

        $tran = BackyardBaseModel::beginTransaction($this);
        try {
            try {
                $staffIdentityNewObj = HrStaffAnnexInfoModel::find([
                    'conditions' => ' staff_info_id = :staff_info_id:',
                    'bind'       => ['staff_info_id' => $new_staff_info_id],
                ]);
                $staffIdentityNewObj->delete();

                $staffIdentityV2Obj = HrStaffAnnexInfoModel::find([
                    'conditions' => ' staff_info_id = :staff_info_id:',
                    'bind'       => ['staff_info_id' => $staff_info_id],
                ]);
                if ($staffIdentityV2Obj) {
                    $staffIdentityV2 = $staffIdentityV2Obj->toArray();
                    $this->logger->write_log(['HrStaffAnnexInfoModel' => ['old_staff_info_id'=>$staff_info_id,'new_staff_info_id'=>$new_staff_info_id],'data'=>$staffIdentityV2], 'info');
                    $model = new HrStaffAnnexInfoModel();
                    foreach ($staffIdentityV2 as &$value2) {
                        $value2['id']            = 0;
                        $value2['staff_info_id'] = $new_staff_info_id;
                        $value2['created_at']    = gmdate('Y-m-d H:i:s');
                        $value2['updated_at']    = gmdate('Y-m-d H:i:s');

                        $insertModel = clone $model;
                        $insertModel->create($value2);
                    }
                }
            } catch (\Exception $e) {
                $this->logger->write_log('HrStaffAnnexInfoModel delete and save err:' . $e->getMessage());
            }

            //保存车辆类型
            $this->saveVehicleType($vehicleObj, $new_staff_info_id, $params['job_title_id']);

            //保存默认休息日
            $this->saveDefaultRestDay($staff_info_id, $new_staff_info_id, $params['default_rest_day_date']);

            $detail = HireTypeImportListModel::findFirst([
                'conditions' => 'old_staff_id = :staff_info_id: and entry_state = :entry_state: and deleted = 0',
                'bind' => [
                    'staff_info_id' => $staff_info_id,
                    'entry_state'   => HrEntryModel::STATUS_TO_BE_EMPLOYED,
                ],
            ]);
            if (empty($detail)) {
                throw new \Exception('invalid data:' . $staff_info_id);
            }
            //更改状态
            $detail->entry_state              = HrEntryModel::STATUS_EMPLOYED;
            $detail->new_staff_id             = $new_staff_info_id;
            $detail->entry_confirm_operator_id = $operator_id;
            $detail->actual_entry_date        = $params['hire_date'];
            $detail->shift_id                 = $params['shift_id'];
            if (!empty($params['default_rest_day_date'])) { //默认休息日
                $detail->default_rest_day_date = implode(',', $params['default_rest_day_date']);
            }
            if (!empty($params['delivery_code'])) { //派件码
                $detail->delivery_code = implode(',', $params['delivery_code']);
            }
            $detail->save();
            if ($detail->getMessages()) {
                throw new \Exception(implode(' ', $detail->getMessages()));
            }
            //返聘标识
            $update_leave_manager = "update leave_manager set is_rehire = 1,updated_at = updated_at where staff_info_id = ? ";
            $this->getDI()->get('db')->execute($update_leave_manager, [$staff_info_id]);

            //到岗同步OA
            $this->pushSyncOA(['old_staff_id' => $staff_info_id, 'new_staff_id' => $new_staff_info_id]);

            $tran->commit();
            //lnt 薪资结构
            if ($params['data_type'] == HireTypeImportListModel::DATA_TYPE_LNT) {
                $this->updateSalaryStructure($new_staff_info_id);
            }
        } catch (\Exception $exception) {
            $tran->rollback();
            $this->logger->write_log('doEntry err,' . $exception->getMessage() .$exception->getTraceAsString());
            throw $exception;
        }
        return true;
    }

    /**
     * 马来 快速返聘为lnt员工薪资结构
     * @param $staff_info_id
     * @return void
     */
    private function updateSalaryStructure($staff_info_id): void
    {
        if (!isCountry('MY')) {
            return;
        }
        $this->getDI()->get('redisLib')->lpush(RedisEnums::LIST_REHIRE_LNT,$staff_info_id);
    }

    /**
     * 马来 快速返聘为lnt员工薪资结构
     * @return array|bool|mixed|null
     */
    public function popLntSalaryStructure($staff_info_id)
    {

        if (empty($staff_info_id)) {
            $staff_info_id = $this->getDI()->get('redisLib')->rpop(RedisEnums::LIST_REHIRE_LNT);
        }
        if (empty($staff_info_id)) {
            return true;
        }
        $apiCli = (new ApiClient('hcm_rpc', '', 'rehire_lnt_salary', $this->lang));
        $apiCli->setParams(['staff_info_id' => $staff_info_id]);
        return $apiCli->execute();
    }


    //到岗同步OA
    public function pushSyncOA($params)
    {
        if (!isCountry('TH')) {
            return true;
        }
        $this->getDI()->get('redisLib')->lpush(RedisEnums::LIST_CHANGE_TO_IC_NOTICE_OA, json_encode($params));
    }

    public function popSyncOA()
    {
        $data = $this->getDI()->get('redisLib')->rpop(RedisEnums::LIST_CHANGE_TO_IC_NOTICE_OA);
        if (empty($data)) {
            return true;
        }
        $data = json_decode($data,true);
        $apiCli = (new ApiClient('oa_rpc', '', 'transfer_leave_assets_to_personal_agent', $this->lang));
        $apiCli->setParams($data);
        return $apiCli->execute();
    }



    private function getVehicleType($job_title_id)
    {
        if (isCountry('MY')) {
            if ($job_title_id == MyVehicleInfoEnums::JOB_BIKE_TITLE_ID) {
                $vehicleType = MyVehicleInfoEnums::VEHICLE_TYPE_BIKE_CODE;
            } else if ($job_title_id == MyVehicleInfoEnums::JOB_CAR_TITLE_ID) {
                $vehicleType = MyVehicleInfoEnums::VEHICLE_TYPE_CAR_CODE;
            } else if (in_array($job_title_id, [MyVehicleInfoEnums::JOB_VAN_TITLE_ID, MyVehicleInfoEnums::BDC_DRIVER_JOB_TITLE_ID])) {
                $vehicleType = MyVehicleInfoEnums::VEHICLE_TYPE_VAN_CODE;
            } else {
                $vehicleType = null;
            }
        } elseif (isCountry('PH')) {
            if ($job_title_id == VehicleInfoEnums::JOB_BIKE_TITLE_ID) {
                $vehicleType = VehicleInfoEnums::VEHICLE_TYPE_BIKE_CODE;
            } else if ($job_title_id == VehicleInfoEnums::JOB_VAN_TITLE_ID) {
                $vehicleType = VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE;
            } else if ($job_title_id == VehicleInfoEnums::JOB_TRICYCLE_TITLE_ID) {
                $vehicleType = VehicleInfoEnums::VEHICLE_TYPE_TRICYCLE_CODE;
            } else if ($job_title_id == VehicleInfoEnums::JOB_TRUCK_TITLE_ID) {
                $vehicleType = VehicleInfoEnums::VEHICLE_TYPE_TRUCK_CODE;
            } else {
                $vehicleType = null;
            }
        } else {
            if ($job_title_id == VehicleInfoEnums::JOB_BIKE_TITLE_ID) {
                $vehicleType = VehicleInfoEnums::VEHICLE_TYPE_BIKE_CODE;
            } else if (in_array($job_title_id, [
                    VehicleInfoEnums::JOB_VAN_TITLE_ID,
                    VehicleInfoEnums::JOB_VAN_FEEDER_TITLE_ID,
                    VehicleInfoEnums::JOB_COURIER_AND_INSTALLATION_STAFF_TITLE_ID,
                    VehicleInfoEnums::JOB_PICKUP_DRIVER,
                ])) {
                $vehicleType = VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE;
            } else {
                $vehicleType = null;
            }
        }
        return $vehicleType;
    }

    private function getShitList($department_id, $job_title_id, $store_id)
    {
        $param = ['department_id' => $department_id, 'job_title_id' => $job_title_id, 'store_id' => $store_id];
        $this->logger->write_log("getAllShift params:".json_encode($param), 'info');
        $ac = new ApiClient('hr_rpc', '', 'get_staff_config_shift', $this->lang);
        $ac->setParams($param);
        $data = $ac->execute();
        $this->getDI()->get('logger')->write_log("getAllShift response:".json_encode($data), 'info');
        $returnArr['data'] = $data['result'];

        if (isCountry('MY')) {
            foreach ($returnArr['data']['shift_list'] as &$item) {
                $item['shift_work_time'] = $item['attendance_time'];
            }
            return ['data' => $returnArr['data']['shift_list']];
        } else {
            foreach ($returnArr['data'] as $key => $value) {
                foreach ($value as $k => $v) {
                    $returnArr['data'][$key][$k]['markup'] = $v['start'].'-'.$v['end'];
                }
            }
            return $returnArr;
        }
    }

    /**
     * 员工轮休时给定的默认的休息列表
     */
    public function getDefaultRestDay(): array
    {
        $data = [];
        foreach (range(1, 7) as $item) {
            $data[] = ['name' => $this->getTranslation()->_('default_rest_day_' . $item), 'value' => (string)$item];
        }
        return $data;
    }

    /**
     * 保存默认休息日
     * @param $old_staff_id
     * @param $new_staff_id
     * @param $default_rest_day_date
     * @return void
     * @throws \Exception
     */
    private function saveDefaultRestDay($old_staff_id, $new_staff_id, $default_rest_day_date)
    {
        if (isCountry(['PH', 'MY'])) {
            if (empty($default_rest_day_date)) {
                return;
            }
            $model                = new StaffDefaultRestDayModel();
            $model->staff_info_id = $new_staff_id;
            $model->rest_day      = implode('', $default_rest_day_date);
            $model->save();
            $this->logger->write_log([
                'StaffDefaultRestDayModel' => [
                    'staff_info_id' => $new_staff_id,
                    'rest_day'      => $model->rest_day,
                ],
            ], 'info');
        } else {
            $defaultRestDayObj = StaffDefaultRestDayModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and is_deleted = 0',
                'bind'       => ['staff_info_id' => $old_staff_id],
            ]);
            if ($defaultRestDayObj) {
                $defaultRestDayInfo = $defaultRestDayObj->toArray();
                $this->logger->write_log([
                    'StaffDefaultRestDayModel' => [
                        'old_staff_info_id' => $old_staff_id,
                        'new_staff_info_id' => $new_staff_id,
                    ],
                    'data' => $defaultRestDayInfo,
                ], 'info');
                $newRestDayInfo = new StaffDefaultRestDayModel();
                foreach ($defaultRestDayInfo as $key4 => $value4) {
                    if ($key4 == 'id' || $key4 == 'created_at' || $key4 == 'updated_at') {
                        continue;
                    }
                    $newRestDayInfo->$key4 = $value4;
                }
                $newRestDayInfo->staff_info_id = $new_staff_id;
                $newRestDayInfo->save();
                if ($newRestDayInfo->getMessages()) {
                    throw new \Exception(implode(' ', $newRestDayInfo->getMessages()));
                }
            }
        }
    }

    /**
     * 获取车辆来源编码
     *
     * 根据职位ID获取车辆类型，并结合车辆对象判断车辆来源。若车辆类型不存在返回0，
     * 车辆对象为空或车辆类型不匹配时返回默认个人车辆来源编码。
     *
     * @param object|null $vehicleObj 车辆信息对象，包含vehicle_type和vehicle_source属性
     * @param int $job_title_id 职位ID，用于关联车辆类型
     *
     * @return int 车辆来源编码(来自VehicleInfoEnums)，若车辆类型不存在返回0
     */
    protected function getVehicleSource($vehicleObj, $job_title_id)
    {
        // 核心逻辑：通过三级递进判断确定车辆来源
        // 1. 基础校验 - 获取车辆类型
        $vehicleType = $this->getVehicleType($job_title_id);
        if (is_null($vehicleType)) {
            return 0;
        }

        // 2. 空对象处理 - 直接返回默认个人来源
        if (empty($vehicleObj)) {
            return VehicleInfoEnums::VEHICLE_SOURCE_PERSONAL_CODE;
        }

        // 3. 类型匹配判断 - 类型一致时采用对象原有来源
        if ($vehicleType == $vehicleObj->vehicle_type) {
            return $vehicleObj->vehicle_source;
        }

        // 最终兜底策略 - 所有不匹配情况返回个人来源
        return VehicleInfoEnums::VEHICLE_SOURCE_PERSONAL_CODE;
    }

    /**
     * @param $vehicleObj
     * @param $new_staff_id
     * @param $job_title_id
     * @return void
     * @throws \Exception
     */
    private function saveVehicleType($vehicleObj, $new_staff_id, $job_title_id)
    {
        //获取新车辆类型
        $vehicleType    = $this->getVehicleType($job_title_id);
        if (is_null($vehicleType)) {
            return;
        }

        if (empty($vehicleObj)) {
            $this->newVehicleType($new_staff_id, $vehicleType);
            return;
        }

        if ($vehicleType == $vehicleObj->vehicle_type) {
            $vehicleInfo    = $vehicleObj->toArray();
            $newVehicleInfo = new VehicleInfoModel();
            foreach ($vehicleInfo as $key3 => $value3) {
                if (in_array($key3, ['id', 'created_at', 'updated_at', 'uid', 'oil_number', 'oil_img',
                    'oil_effective_date', 'oil_type', 'oil_company', 'open_date'])) {
                    continue;
                }
                if ($key3 == 'formal_data') {
                    $newVehicleInfo->formal_data = $value3 ?: '';
                }
                $newVehicleInfo->$key3 = $value3;
            }
            $newVehicleInfo->uid = $new_staff_id;
            $newVehicleInfo->is_open = 0;
            $newVehicleInfo->balance = 0;
            $newVehicleInfo->money = 0;
            $newVehicleInfo->unit_price = 0;
            $newVehicleInfo->oil_subsidy_type = 0;
            $newVehicleInfo->vehicle_type = $vehicleType;
            $newVehicleInfo->save();
            if ($newVehicleInfo->getMessages()) {
                throw new \Exception(implode(' ', $newVehicleInfo->getMessages()));
            }
        } else {
            $this->newVehicleType($new_staff_id, $vehicleType);
        }
    }

    private function newVehicleType($staff_info_id, $vehicle_type)
    {
        $this->logger->write_log([
            'VehicleInfoModel' => [
                'new_staff_info_id' => $staff_info_id,
            ],
            'data'             => $vehicle_type,
        ], 'info');

        $info = [
            'vehicle_type' => $vehicle_type,
            'vehicle_source' => VehicleInfoEnums::VEHICLE_SOURCE_PERSONAL_CODE,
        ];
        $newVehicleInfo = new VehicleInfoModel();
        $newVehicleInfo->uid = $staff_info_id;
        $newVehicleInfo->vehicle_type = $vehicle_type;
        $newVehicleInfo->vehicle_source = VehicleInfoEnums::VEHICLE_SOURCE_PERSONAL_CODE; //默认个人车辆
        $newVehicleInfo->formal_data = json_encode($info);                                //字段必传
        $newVehicleInfo->save();
        if ($newVehicleInfo->getMessages()) {
            throw new \Exception(implode(' ', $newVehicleInfo->getMessages()));
        }
    }

    /**
     * 获取车辆类型
     * @param int $vehicleType
     * @return string
     */
    private function getCarType(int $vehicleType): string
    {
        if (isCountry('MY')) {
            return MyVehicleInfoEnums::VEHICLE_TYPE_ITEM[$vehicleType] ?? '';
        } else {
            return VehicleInfoEnums::VEHICLE_TYPE_ITEM[$vehicleType] ?? '';
        }
    }
}
