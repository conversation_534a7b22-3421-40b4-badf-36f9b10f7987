<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrBlackGreyListModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewFileModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewPassTypeRecordModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewSubscribeModel;
use FlashExpress\bi\App\Models\backyard\HrResumeFilterModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\MessageCenterRepository;
use FlashExpress\bi\App\Repository\ResumeRepository;
use FlashExpress\bi\App\Models\backyard\ResumeOutLogModel;
use FlashExpress\bi\App\Repository\StaffRepository;

class ResumeServer extends BaseServer
{

    /**
     * 简历淘汰
     * @param type $params
     * @return type
     */
    public function out($params = [])
    {
        try {
            //开启事物
            $db = $this->getDI()->get('db');
            $db->begin();
            $resume_data = (new ResumeRepository($this->timeZone))->out($params);
            $resume_id  = $resume_data["resume_id"];
            //检查是否有面试中数据
            $interviewData = HrInterviewModel::findFirst([
                        'conditions' => 'resume_id = :resume_id: and hc_id=:hc_id:',
                        'bind' => ['resume_id' => $resume_id,'hc_id' => $resume_data["hc_id"]],
            ]);

            //修改面试信息
            if ($interviewData) {
                $interviewData->state = 30;
                $interviewData->save();

                $resumeOutLogModel = new ResumeOutLogModel();
                $resumeOutLogModel->interview_id = $interviewData->interview_id;
                $resumeOutLogModel->resume_id = $resume_id;
                $resumeOutLogModel->hc_id = $interviewData->hc_id;
                //$resumeOutLogModel->out_type = $params['reason']; //不通过原因写入type_record表
                $resumeOutLogModel->hr_log_id = $params['hr_log_id'] ?? 0;
                $resumeOutLogModel->save();

                //不通过原因写入type_record表
                $out_log_id = $db->lastInsertId();
                if(!empty($params['out_type_list'])){
                    $batch_data =  [];
                    // 插入前先把之前的数据软删
                    $this->getDI()->get('db') ->updateAsDict('hr_interview_pass_type_record', ["deleted"=>1], [
                        'conditions' => "resume_id = $resume_id",
                    ]);
                    foreach ($params['out_type_list'] as $key => $val) {
                        $pass_model = new HrInterviewPassTypeRecordModel();
                        $pass_model->business_type = enums::RESUME_LOG_BUSINESS_TYPE;
                        $pass_model->business_id = $out_log_id;
                        $pass_model->resume_id = $resume_id;
                        $pass_model->out_type = enums::INTERVIEW_OUT_TYPE_1;
                        $pass_model->pass_type =  $val['pass_type'];
                        $pass_model->deleted =  0;
                        $pass_model->operater =  $params['staff_id'];
                        $pass_model->pass_reason = $val['out_type'];
                        $pass_model->pass_remark = !empty($val['pass_remark']) ? $val['pass_remark'] : '';
                        $pass_model->save();
                    }

                }

                $interviewSubscribeData = HrInterviewSubscribeModel::findFirst([
                            'conditions' => 'interview_id = :interview_id:',
                            'bind' => ['interview_id' => $interviewData->interview_id],
                ]);

                if ($interviewSubscribeData) {
                    $interviewSubscribeData->status = 2;
                    $interviewSubscribeData->save();
                }
            }

            $db->commit();

            // 数据返回
            return $resume_data["resume_id"];
        } catch (\Exception $e) {
            //异常回滚
            $db->rollback();
            $this->getDI()->get('logger')->write_log("淘汰简历:（interview_id-{$params['interview_id']}）更新失败：" . $e->getMessage() . $e->getTraceAsString(), 'error');

            return false;
        }
    }



    /**
     * 需求出处：https://l8bx01gcjr.feishu.cn/docs/doccnglfR8QUAiPj1RJBjtfypaf
     * 获取指定筛选官的筛选的简历列表
     * @param $params
     * @return array
     */
    public function getResumeFilterList($params): array
    {

        //type
        if (empty($params['staff_info_id'])) {
            throw new BusinessException('staff id empty');
        }

        $result = (new ResumeRepository($this->timeZone))->getResumeFilterList($params);

        if (!$result['total']) {
            return $result;
        }
        //获取职位与名称 增加缓存
        $job_title = array_column((new SysServer($this->lang, $this->timeZone))->getJobTitleList(), 'job_name', 'id');

        $recommender_staff_ids = array_column($result['list'], 'recommender_staff_id');
        $recommenderStaffInfo = array_column((new HrStaffInfoServer())->getUserInfoByStaffInfoIds($recommender_staff_ids, 'staff_info_id,name,sex'), null, 'staff_info_id');
        $now =  time();
        $list = [];

        foreach ($result['list'] as $value) {
            $item['id'] = $value['id'];
            $item['filter_state'] = $value['filter_state'];
            $item['hc_id'] = $value['hc_id'];
            $item['title'] = str_replace('%name%', $value['name'], $this->getTranslation()->_('resume_filter_title'));
            $sub_title = $this->getTranslation()->_('resume_filter_sub_title');
            $sub_title = str_replace('%staff_name%', $recommenderStaffInfo[$value['recommender_staff_id']]['name'], $sub_title);
            $sub_title = str_replace('%recommend_time%', $value['recommend_time'], $sub_title);
            $item['sub_title'] = $sub_title;
            $wait_time = $this->getWaitTime($value['recommend_time'], $now);
            $item['wait_time'] = $value['filter_state'] == HrResumeFilterModel::FILTER_STATE_STAY && $wait_time ? str_replace('%wait_time%',$wait_time , $this->getTranslation()->_('resume_filter_wait_time')):'';
            $item['job_name']  = $job_title[$value['job_title']]??'';
            $list[] = $item;
        }
        $result['list'] = $list;
        return $result;
    }

    public function getWaitTime($diff_time, $current_time): string
    {
        $_diff_time = strtotime($diff_time);
        $is_writing = $current_time - $_diff_time;

        $str = '';
        if ($is_writing > 0) {
            $timeInfo = getTimeDifference($current_time, $_diff_time);
            $str = '';
            if ($timeInfo['day']>0) {
                $str.=sprintf($this->getTranslation()->_('wait_time_feedback_interview_day'), $timeInfo['day']);
            }
            if ($timeInfo['hour']>0) {
                $str.=sprintf($this->getTranslation()->_('wait_time_feedback_interview_hour'), $timeInfo['hour']);
            }
            if ($timeInfo['min']>0) {
                $str.=sprintf($this->getTranslation()->_('wait_time_feedback_interview_minutes'), $timeInfo['min']);
            }
        }
        return $str;
    }

    /**
     * 需求出处：https://l8bx01gcjr.feishu.cn/docs/doccnglfR8QUAiPj1RJBjtfypaf
     * 获取指定筛选官的筛选简历详情
     * @param $params
     * @return array
     */
    public function getResumeFilterDetail($params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log('getResumeFilterDetail '.json_encode($params,JSON_UNESCAPED_UNICODE), 'info');

        //获取网点信息
        $sysServer = new SysServer();
        $storeInfo = array_column($sysServer->getSysStoreList(), 'name', 'id');
      
        //获取城市信息
        $provinceInfo = array_column($sysServer->getSysProvinceList(), 'name', 'code');
        $resumeRepository = new ResumeRepository($this->timeZone);

        $resumeFilter = $resumeRepository->getResumeFilterInfo($params);

        if (empty($resumeFilter)) {
            throw new BusinessException('aaaaa');
        }
        $config = $this->getDI()->getConfig();
        $interViewService = new InterviewServer($this->lang, $this->timeZone);

        $detail['id'] = $resumeFilter->id;
        $detail['filter_state'] = $resumeFilter->filter_state;
        $detail['recommender_staff_id'] = $resumeFilter->recommender_staff_id;

        $hcInfo = (new HcRepository($this->timeZone))->getHcInfo($resumeFilter->hc_id);
        if (empty($hcInfo)) {
            throw new BusinessException('bbbbb');
        }

        $detail['recommend_info']['department_name'] = $hcInfo['department_name'];     //部门
        $detail['recommend_info']['job_name'] = $hcInfo['job_name'];      //职位
        $detail['recommend_info']['work_place'] = $hcInfo['worknode_id'] == '-1'? "Head Office" : ($storeInfo[$hcInfo['worknode_id']]??''); //工作地点  工作网点
        $detail['recommend_info']['hc_id'] = $resumeFilter->hc_id;
        $detail['recommend_info']['reason'] = $resumeFilter->recommend_reason?:'';
        $detail['filter_info']['filter_state'] = $resumeFilter->filter_state;
        $detail['filter_info']['feedback_reason'] = $resumeFilter->feedback_reason?:'';

        //简历信息有 $provinceInfo.code
        $resumeInfo = $resumeRepository->getResumeInfoById($resumeFilter->resume_id);
        if (empty($resumeInfo)) {
            throw new BusinessException($this->getTranslation()->_('resume_does_not_exist'));
        }

        if (isCountry("TH")){
            $deServer = new DelinquencyServer($this->lang,$this->timeZone);
            [$list, $count] = $deServer->getDelinquencyList($resumeInfo->credentials_num,[HrBlackGreyListModel::BEHAVIOR_TYPE_GREY]);
            $detail['delinquency_list'] = $deServer->displayList($list);

        }

        if (!empty($resumeFilter->resume_data)) {
            $resume_data = json_decode($resumeFilter->resume_data, true);

            //姓名
            $detail['recommend_info']['staff_name'] =$resume_data['candidate_name'];
            //工作城市
            $detail['recommend_info']['work_city'] = $resume_data['work_city_name'];
            //期望薪资
            $detail['recommend_info']['entry_salary'] = $resume_data['expected_salary'];
            //当前薪资
            $detail['recommend_info']['current_salary'] = $resume_data['current_salary'];

            $detail['resume_info']['sex'] = $interViewService->getResumeSex($resume_data['candidate_sex']) == 1 ? $this->getTranslation()->_('4900') : $this->getTranslation()->_('4901');
            $detail['resume_info']['age'] = $resume_data['candidate_age'];
            $detail['resume_info']['cv_id'] = $resumeFilter->resume_id;
            $detail['resume_info']['annex']['file_name'] = $resume_data['resume_annex']['file_name'];
            $detail['resume_info']['annex']['file_path'] = $resume_data['resume_annex']['file_path'];
            $logger->write_log('getResumeFilterDetail detail '.json_encode($detail,JSON_UNESCAPED_UNICODE), 'info');

            return $detail;
        }

        $resumeAnnexInfo = $resumeRepository->getAnnexList($resumeFilter->resume_id);
        if (empty($resumeAnnexInfo)) {
           // throw new BusinessException('ddddd');
        }

        $detail['recommend_info']['staff_name'] =$resumeInfo->name;
        $detail['recommend_info']['work_city'] = $provinceInfo[$resumeInfo->address_id]??''; //工作城市
        $detail['recommend_info']['entry_salary'] = round($resumeInfo->entry_salary/100, 2);
        $detail['recommend_info']['current_salary'] = round($resumeInfo->current_salary/100, 2);

        $detail['resume_info']['sex'] = $interViewService->getResumeSex($resumeInfo->sex, $resumeInfo->call_name) == 1 ? $this->getTranslation()->_('4900') : $this->getTranslation()->_('4901');
        $detail['resume_info']['age'] = $resumeInfo->date_birth ? DateHelper::howOld($resumeInfo->date_birth) : ' ';
        $detail['resume_info']['annex']['file_name'] = $resumeAnnexInfo['original_name']??'';
        $detail['resume_info']['annex']['file_path'] = isset($resumeAnnexInfo['object_key']) ? $config->application['img_prefix'].$resumeAnnexInfo['object_key']:'';
        $detail['resume_info']['cv_id'] = $resumeFilter->resume_id;
        $logger->write_log('getResumeFilterDetail detail '.json_encode($detail,JSON_UNESCAPED_UNICODE), 'info');

        return $detail;
    }

    /**
     * 需求出处：https://l8bx01gcjr.feishu.cn/docs/doccnglfR8QUAiPj1RJBjtfypaf
     * 保存筛选官的筛选简历结果
     * @param $params
     * @return bool
     */
    public function saveResumeFilterResult($params): bool
    {
        $logger = $this->getDI()->get('logger');

        $logger->write_log('saveResumeFilterResult '.json_encode($params,JSON_UNESCAPED_UNICODE), 'info');

        try {

            $detail = $this->getResumeFilterDetail($params);

            $data=['resume_id'=>$detail['resume_info']['cv_id'],'filter_state'=>$params['filter_state'],'feedback_reason'=>$params['remark'],'filter_staff_id'=>$params['staff_info_id']];
            $rpcClient = new ApiClient('winhr_rpc', '', 'resumeFilterFeedback', $this->lang);
            $rpcClient->setParams($data);
            $result  = $rpcClient->execute();
            $logger->write_log('winhr_rpc :'.json_encode($result,JSON_UNESCAPED_UNICODE), 'info');

            if (isset($result['error'])) {
                throw new BusinessException($result['error']);
            }

            if (isset($result['result']['code']) && $result['result']['code'] != 1) {
                throw new BusinessException($result['result']['msg']);
            }

            $this->lang = (new StaffServer)->getLanguage($detail['recommender_staff_id']);
            $t = $this->getTranslation();
            //筛选结果
            if( $params['filter_state'] == HrResumeFilterModel::FILTER_STATE_PASS){
                $title = $t->_('resume_filter_result_title_pass');
                $content = $t->_('resume_filter_result_content_pass');
            }else{
                $title = $t->_('resume_filter_result_title_not_pass');
                $content = $t->_('resume_filter_result_content_not_pass');
            }
            $content= str_replace('%cv_id%', $detail['resume_info']['cv_id'], $content);
            $content= str_replace('%staff_name%', $detail['recommend_info']['staff_name'], $content);
            $content= str_replace('%job_name%', $detail['recommend_info']['job_name'], $content);
            $content= str_replace('%filter_staff_name%', $params['name']. "(".$params['staff_info_id'].")", $content);
            //发送消息
            $add_message_param = [
                'staff_info_ids_str' => $detail['recommender_staff_id'],
                'staff_users'        => [['id' => $detail['recommender_staff_id']]],
                'message_title'      => $title,
                'message_content'    => "<div style='font-size: 40px'>" . $content . "</div>",
            ];
            $rpcClient = new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang);
            $rpcClient->setParams($add_message_param);
            $result  = $rpcClient->execute();
            $logger->write_log('bi :'.json_encode($result,JSON_UNESCAPED_UNICODE), 'info');

            if (isset($result['error'])) {
                throw new BusinessException($result['error']);
            }
            //发消息
        } catch (Exception $e) {
            throw $e;
        }
        return true;
    }

    /**
     * 获取待处理的数据
     * @param $staff_info_id
     * @return int
     */
    public function getStaffResumeWaitFilterNum($staff_info_id): int
    {

        $wait_num = 0;
        if(!is_cli()){
            //演示APP 调用返回 0
            $headerData       = $this->request->getHeaders();
            if((isset($headerData['X-Demonstration-App']) || $this->request->get('x-demonstration-app'))){
                return $wait_num;
            }
        }

        $wait_filter_resume = (new ResumeRepository($this->timeZone))->getStaffResumeFilterNum($staff_info_id,[HrResumeFilterModel::FILTER_STATE_STAY]);
        if(!empty($wait_filter_resume)){
            $wait_num = array_column($wait_filter_resume,'total','filter_state')[HrResumeFilterModel::FILTER_STATE_STAY];
        }
        return intval($wait_num);
    }

    /**
     * 给菜单用的 展示入口和待处理数量
     * @param $staff_info_id
     * @return int[]
     */
    public function getStaffResumeFilterNum($staff_info_id): array
    {

        $result = ['is_show'=>0,'num'=>0];

        if(!is_cli()){
            //演示APP 调用返回 0
            $headerData       = $this->request->getHeaders();
            if((isset($headerData['X-Demonstration-App']) || $this->request->get('x-demonstration-app'))){
                return $result;
            }
        }

        $wait_filter_resume = (new ResumeRepository($this->timeZone))->getStaffResumeFilterNum($staff_info_id);
        if(!empty($wait_filter_resume)){
            $result['is_show'] = 1;
            $map = array_column($wait_filter_resume,'total','filter_state');
            $result['num'] = intval($map[HrResumeFilterModel::FILTER_STATE_STAY]??0);
        }
        return $result;
    }

    /**
     * 发送简历完善通知 消息
     * @param $staff_info_id
     * @param $sub_staff_info_id
     * @param $begin_date-支援日期
     * @return bool
     */
    public function sendResumeMessage($staff_info_id, $sub_staff_info_id, $begin_date)
    {
        if(empty($staff_info_id) || empty($sub_staff_info_id)) {
            return false;
        }
        $masterStaffInfo = (new StaffRepository())->getStaffInfoOne($staff_info_id);
        $hireData = date('Y-m-d 00:00:00', strtotime($masterStaffInfo['hire_date']));

        //入职日期+30天， 五天之后不再看是否 未读。
        $limitData = date('Y-m-d 00:00:00', strtotime("{$hireData}+15 day"));

        $begin_date = date('Y-m-d 00:00:00', strtotime($begin_date));

        if(strtotime($limitData) < strtotime($begin_date)) {
            return false;
        }

        $category = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_COMPLETE_RESUME');
        //主账号肯定发过，已读不发
        $messageParams = ['staff_info_id' => $staff_info_id, 'category' => $category,'read_state' => MessageCourierModel::READ_STATE_UNREAD];
        $messageInfo = MessageCenterRepository::getMessageCourierInfo($messageParams);
        if(empty($messageInfo)) {
            return false;
        }

        //消息标题（泰国泰文，其他国家英文）
        $resume_msg_title = 'Completion of Personal Information Notice';
        if (isCountry()) {

            //泰国用泰文
            $resume_msg_title = 'แจ้งกรอกข้อมูลพนักงาน';

            if (!empty($masterStaffInfo['hire_type']) && $masterStaffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                $resume_msg_title = 'แจ้งการกรอกข้อมูลของผู้รับจ้างขนส่งพัสดุรายชิ้น';
            }
        }
        //签字消息
        $message_param = [
            'id'                 => time() . $sub_staff_info_id . rand(1000000, 9999999),
            'staff_info_ids_str' => $sub_staff_info_id,
            'staff_users'        => [$sub_staff_info_id],
            'message_title'      => $resume_msg_title,
            'message_content'    => $messageInfo['id'],
            'category'           => $category,
        ];

        $job_title_list = (new SettingEnvServer())->getSetVal('perfect_person_message_job_title_list', ',');

        if (
            isCountry('TH')
            &&
            in_array($masterStaffInfo['job_title'],$job_title_list)
        ) {
            //完善简历消息 最晚 完善时间:入职日期+2天，如果在这期间未完善，则发送给子账号。
            $delayDate = date("Y-m-d 00:00:00", strtotime("{$hireData}+2 day"));
            if(strtotime($delayDate) >= strtotime($begin_date)) {
                $message_param['related_id'] = strtotime($delayDate);
            }
        }

        $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', 'th'));
        $bi_rpc->setParams($message_param);
        $res = $bi_rpc->execute();
        $this->getDI()->get('logger')->write_log([
            'function' => 'StaffSupportStoreServer-setProperty-sendResumeMessage',
            'message' => '消息发送成功',
            'params' => $message_param,
            'result' => $res
        ], 'info');

        return true;
    }

    /**
     * 获取简历信息
     * @param $staff_info_id
     * @return array
     */
    public function getResumeInfoByStaffId($staff_info_id)
    {
        if(empty($staff_info_id)) {
            return [];
        }

        $entryInfo = HrEntryModel::findFirst([
            'conditions' => 'staff_id = :staff_info_id: and status = :status: and deleted = :deleted:',
            'bind' => ['staff_info_id' => $staff_info_id, "status" => HrEntryModel::STATUS_EMPLOYED, "deleted" => HrEntryModel::DELETE_NO],
        ]);
        if (empty($entryInfo)) {
            return [];
        }

        $resumeInfo = (new ResumeRepository($this->timeZone))->getResumeInfoById($entryInfo['resume_id']);

        if(empty($resumeInfo)) {
            return [];
        }

        $resumeExtendInfo = ResumeRepository::getExtentOne($entryInfo['resume_id']);

        if(empty($resumeExtendInfo)) {
            return [];
        }

        $data['identity_validate_status'] = $resumeInfo['identity_validate_status'];
        $data['identity_code_url']        = $resumeExtendInfo['identity_code_url'];
        $data['hand_identity_url']        = $resumeExtendInfo['hand_identity_url'];

        return $data;
    }
}
