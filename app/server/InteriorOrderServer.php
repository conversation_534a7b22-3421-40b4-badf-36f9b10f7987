<?php
/**
 *SyncWarehoseServer.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/8/12 0012 14:41
 */

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\InteriorOrderStatusEnums;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersGoodsSkuModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersModel;
use FlashExpress\bi\App\Models\oa\SysDepartmentPcCode;


class InteriorOrderServer extends BaseServer
{


    public function __construct($lang = 'zh-CN', $timezone = 'Asia/Bangkok')
    {
        $this->timezone = $timezone;
        parent::__construct($lang, $timezone);
    }
    /**
     * 下单没成功的，重新判断下
     */
    public function recommit_interior_order($params){

        $orders = InteriorOrdersModel::find(
            [
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $params['id'],
                ]
            ]
        )->toArray();

        if (empty($orders)) {
            $this->getDI()->get('logger')->write_log("InteriorOrderTask = recommit_order ==no_data", 'info');
            return;
        }

        $db               = InteriorOrdersModel::beginTransaction($this);

        $syncServer = new SyncWarehoseServer();
        $orderCode = '';
        foreach ($orders as $order) {
            try {
                $item = InteriorOrdersModel::findFirst(
                    [
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $order['id']],
                        'for_update' => true
                    ]
                );
                if(empty($item)){
                    throw new Exception('not found item');
                }

                $orderCode = $item->order_code;

                $postData = [
                    'orderSn' => $item->order_code,
                    'lang'    => $this->lang,
                    'mchId'   => $item->mach_code,
                ];

                //就已取消不需要重新下
                if ($item->order_status == InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE) {
                    $item->out_status = 1;
                } else {
                    $res = $syncServer->getOrderDetail($postData, 1);

                    //如果不存在，需要重新下单。
                    //不存在，也会返回1，但是data是个空数组
                    if (empty($res) || $res['code'] != 1 || empty($res['data'])) {
                        $item->receive_province_name = $params['receive_province_name']??$item->receive_province_name;
                        $item->receive_city_name = $params['receive_city_name']??$item->receive_city_name;
                        $item->receive_district_name = $params['receive_district_name']??$item->receive_district_name;
                        $item->receive_postal_code = $params['receive_postal_code']??$item->receive_postal_code;
                        $this->getDI()->get('logger')->write_log("InteriorOrderSvc = recommit_interior_order ==order".$item->order_code.'node_sn'.$item->node_sn, 'info');
                        $res = self::syncAddOrderToWmsReturnWarehouseAdd($item);
                        if (empty($res) || $res['code'] != 1) {
                            $item->fail_num++;
                            $item->fail_reason = $res['msg'];
                        } else {
                            $item->out_sn = $res['data'];
                            $item->out_status = 1;
                        }
                    } else {
                        $item->out_sn = $res['data']['outSn'];
                        $item->receive_province_name = $params['receive_province_name']??$item->receive_province_name;
                        $item->receive_city_name = $params['receive_city_name']??$item->receive_city_name;
                        $item->receive_district_name = $params['receive_district_name']??$item->receive_district_name;
                        $item->receive_postal_code = $params['receive_postal_code']??$item->receive_postal_code;
                        $item->out_status = 1;
                    }
                }
                $item->updated_at = date('Y-m-d H:i:s',time());
                $item->save();
                $db->commit();

                $return['code'] = 1;
                $return['msg']  = 'success';
                return $return;
            } catch (Exception $e) {
                $db->rollback();
                $this->logger->write_log("InteriorOrderTask orderCode===" .$orderCode.'---'.$e->getMessage());
            }
        }
    }

    private static function syncAddOrderToWmsReturnWarehouseAdd($_preOrder)
    {
        $conditions      = "order_code = :order_code:";
        $bind            = ["order_code" => $_preOrder->order_code];
        $preOrderSkusObj = InteriorOrdersGoodsSkuModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'order'      => 'id desc'
        ]);
        $preOrderSkusArr = $preOrderSkusObj->toArray();
        $syncWmsPostData = $goods = [];
        foreach ($preOrderSkusArr as $k => $_preOrderSkus) {
            $buyNum  = $_preOrderSkus['buy_num'];
            $price   = $_preOrderSkus['buy_price'];
            $amount  = $_preOrderSkus['pay_amount'];
            $goods[] = [
                'i'             => $k,
                "barCode"       => $_preOrderSkus['goods_sku_code'],
                "goodsName"     => $_preOrderSkus['goods_name_th'],
                "specification" => $_preOrderSkus['attr_1'],
                "num"           => $buyNum,
                //出库商品json，其中price的单位为萨当，即1泰铢=100萨当
                "price"         => ($price * 100),
                "remark"        => $amount
            ];
        }

        if(empty($_preOrder->node_sn)){
            $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($_preOrder->staff_id);
            if(empty($userInfo)){
                return ['code' => 0, 'data' => '没有找到该工号' . $_preOrder->staff_id];
            }
            $userInfo = $userInfo->toArray();
            $nodeSn='';
            if(isCountry()||isCountry('PH')||isCountry('MY')) {
                if ($userInfo['sys_store_id'] != -1) {
                    // 网点员工传入网点编号
                    $nodeSn = $userInfo['sys_store_id'];
                } else {
                    // 总部员工传入成本中心,获取费用所属中心 先用子部门查 没有的话 拿顶级部门
                    $pcCode = SysDepartmentPcCode::findFirst("department_id = {$userInfo['node_department_id']}");
                    if (empty($pcCode)) {
                        $pcCode = SysDepartmentPcCode::findFirst("department_id = {$userInfo['sys_department_id']}");
                    }
                    $nodeSn = empty($pcCode) ? '' : $pcCode->pc_code;
                }
            }
        }else{
            $nodeSn = $_preOrder->node_sn;
        }

        if(isCountry()||isCountry('PH')||isCountry('MY')){
            if (empty($nodeSn)) {
                return ['code' => 0, 'data' => '没有找到该工号对应pc_code' . $_preOrder->staff_id];
            }
        }else{
            $nodeSn='';
        }

        $syncWmsPostData['nodeSn']           = $nodeSn;
        $syncWmsPostData['consigneeName']    = $_preOrder->staff_name;
        $syncWmsPostData['consigneePhone']   = $_preOrder->staff_mobile;
        $syncWmsPostData['province']         = $_preOrder->receive_province_name;
        $syncWmsPostData['city']             = $_preOrder->receive_city_name;
        $syncWmsPostData['district']         = $_preOrder->receive_district_name;
        $syncWmsPostData['postalCode']       = $_preOrder->receive_postal_code;
        $syncWmsPostData['consigneeAddress'] = $_preOrder->receive_address;
        $syncWmsPostData['orderSn']          = $_preOrder->order_code;
        $syncWmsPostData['node_department_id']= $_preOrder->node_department_id;
        $syncWmsPostData['deliveryWay']      = 'express';
        $syncWmsPostData['goods']            = json_encode($goods, JSON_UNESCAPED_UNICODE);
        $syncWmsPostData['remark']           = "GF_order_server_crontab：【staff_id：{ $_preOrder->staff_id;}】";
        $syncWmsPostData['lang']             = 'zh-CN';

        $interior_goods_server = new InteriorGoodsServer();
        $warehouseId = $interior_goods_server->getGoodsTypeStockId($_preOrder->goods_type);
        if (!empty($warehouseId)) {
            $syncWmsPostData['warehouseId'] = $warehouseId;
        }

        //V22367 针对泰国区分FFM需要获取分仓配置货主、仓库、配送方式自提
        if (isCountry('TH') && $_preOrder->mach_code == $interior_goods_server->getFfmMachCode()) {
            $warehouseConfig = $interior_goods_server->getWarehouseConfigByStoreId($_preOrder->receive_store_id);
            $syncWmsPostData['mchId']       = $_preOrder->mach_code;
            $syncWmsPostData['warehouseId'] = $warehouseConfig['stock_id'];
            $syncWmsPostData['deliveryWay'] = 'self';
        }

        $syncWarehoseServer                  = new SyncWarehoseServer();
        $res                                 = $syncWarehoseServer->syncAddOrderToWmsReturnWarehouseAdd($syncWmsPostData, true);
        return $res;
    }

}
