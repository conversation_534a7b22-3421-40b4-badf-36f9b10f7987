<?php
namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\MobileHelper;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HrEconomyAbilityModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HRStaffingModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysFleetVanTypeModel;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoImageIdentificationModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\backyard\VehicleMileageModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StaffVehicleStatusRecordRepository;
use FlashExpress\bi\App\Repository\VehicleRepository;

class VehicleServer extends AuditBaseServer
{

    const OTHER = 100;
    const NETWORK_TAG = 1;//NETWORK部门
    const SHOP_TAG = 2;// SHOP 部门
    /**
     * @var PublicRepository
     */
    private $public;
    /**
     * @var AuditListServer
     */
    private $auditlist;
    /**
     * @var OvertimeRepository
     */
    private $ov;
    /**
     * @var VehicleRepository
     */
    private $vehicle;

    public function __construct($lang, $timezone='+08:00')
    {
        parent::__construct($lang,$timezone);
        $this->vehicle = new VehicleRepository($lang, $timezone);
        $this->public = new PublicRepository();
        $this->auditlist = new AuditListServer($lang, $timezone);
        $this->ov = new OvertimeRepository($timezone);
    }


    /**
     * 验证油卡号是否被他人占用
     * @param string $oil_number
     * @param int $user_id
     * @return mixed
     */
    protected function checkOilNoIsExist(string $oil_number, int $user_id)
    {
        if (empty($oil_number) || empty($user_id)) {
            return 0;
        }

        $oid_number_staff_item = VehicleInfoModel::find([
                                                            'conditions' => 'oil_number = :oil_number:',
                                                            'bind' => ['oil_number' => $oil_number],
                                                            'columns' => ['uid']
                                                        ])->toArray();
        $oid_number_staff_item = $oid_number_staff_item ? array_column($oid_number_staff_item,  'uid', 'uid') : [];
        if (!empty($oid_number_staff_item[$user_id])) {
            unset($oid_number_staff_item[$user_id]);
        }

        return $oid_number_staff_item ? array_shift($oid_number_staff_item) : '';
    }

    /**
     * 检查是否已充值
     * @param $oil_number
     * @param $staff_id
     * @return array
     */
    public function getCheckOilNumberByIsIntoMoney($oil_number,$staff_id)
    {
        if(empty($oil_number) || empty($staff_id)){
            return [];
        }
        $is_exist_oil_number = 'select prepaid_no,staff_id,oil_number from staff_mileage_record_prepaid_info where oil_number = :oil_number and state = 0 and staff_id = :staff_id';
        $bind['oil_number'] = $oil_number;
        $bind['staff_id'] = $staff_id;
        $result = $this->getDI()->get('db')->fetchOne($is_exist_oil_number ,\Phalcon\Db::FETCH_ASSOC,$bind);
        return $result ?? [];
    }

    /**
     * 获取车辆型号
     */
    public static function getVehicleSize($is_all = true): array
    {
        if ($is_all) {
            $query = ['columns' => 'id, van_value'];
        } else {
            //MY废弃旧的车型
            if (isCountry('MY')) {
                $query = [
                    'columns' => 'id, van_value',
                    'conditions' => "deleted = 0 and code between :start_id: and :end_id:",
                    'bind' => [
                        'start_id' => 227,
                        'end_id'   => 233,
                    ],
                    'order' => 'sort_number ASC'
                ];
            } else {
                $query = ['columns' => 'id, van_value', 'conditions' => 'deleted = 0', 'order' => 'sort_number ASC'];
            }
        }
        $res = SysFleetVanTypeModel::find($query);
        return empty($res) ? []: array_column($res->toArray(),'van_value','id');
    }

    /**
     * 枚举类型(转换为前端口需要的方式)
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function enumVehicleS($paramIn = [])
    {
        $returnData = UC('vehicleInfo');
        //只有油的类型需要翻译
        $ot = $this->vehicle->getOilType();
        $oilType = [];
        $oilType[] = ['value' => 1, 'label' => $ot[1]];
        $oilType[] = ['value' => 2, 'label' => $ot[2]];
        $returnData['oil_type'] = $oilType;
        foreach($returnData['vehicle_brand'] as $key => $val){
            if($val['value'] == 8) {
                $returnData['vehicle_brand'][$key]['label'] = $this->getTranslation()->_('2016');
            }
            foreach ($val['data'] as $key1 => $val1) {
                if($val1['value'] == VehicleServer::OTHER){
                    $val1['label'] = $this->getTranslation()->_('2016');
                    $returnData['vehicle_brand'][$key]['data'][$key1] = $val1;
                }
            }
        }

        // 车辆来源
        foreach (VehicleInfoEnums::VEHICLE_SOURCE_ITEM as $vs_k => $vs_v) {
            $returnData['vehicle_source_item'][] = [
                'value' => $vs_k,
                'label' => $this->getTranslation()->_($vs_v),
            ];
        }

        // 驾照类型
        $driver_license_item = [];
        foreach (VehicleInfoEnums::DRIVER_LICENSE_TYPE_ITEM as $l_k => $l_v) {
            if (is_array($l_v) && !empty($l_v)) {
                $driver_license_item[$l_k] = [
                    'value' => $l_k,
                    'label' => $this->getTranslation()->_(VehicleInfoEnums::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.$l_k),
                ];

                foreach ($l_v as $sub_k => $sub_v) {
                    $driver_license_item[$l_k]['data'][] = [
                        'value' => $sub_k,
                        'label' => $this->getTranslation()->_($sub_v),
                    ];
                }
            } else {
                $driver_license_item[$l_k] = [
                    'value' => $l_k,
                    'label' => $this->getTranslation()->_($l_v),
                ];
            }
        }
        $returnData['vehicle_size'] = [];
        // 车型
        foreach (self::getVehicleSize(false)  as $k => $v) {
            $returnData['vehicle_size'][] = [
                'value' => strval($k),
                'label' => $v
            ];
        }

        $returnData['driver_license_item'] = array_values($driver_license_item);

        $resData['vehicle_enum'] = $returnData;
        return $resData;
    }

    /**
     * 获取全部的车辆来源（用于OA审批流可视化）
     * 请勿追加限定逻辑
     * @return array
     */
    public function getAllVehicleSource(): array
    {
        $returnData = [];

        // 车辆来源
        foreach (VehicleInfoEnums::VEHICLE_SOURCE_ITEM as $vs_k => $vs_v) {
            $returnData['vehicle_source_item'][] = [
                'value' => $vs_k,
                'label' => $this->getTranslation()->_($vs_v),
            ];
        }
        return $returnData;
    }


    /**
     * 获取里程上报的职位
     * @return array|mixed
     */
    protected function getMileageJobTitle()
    {
        if (!isCountry('TH')) {
            return [];
        }
        $param = [];
        $ac    = new ApiClient('ard_api', '', 'mileage.get_position_list', $this->lang);
        $ac->setParams($param);
        $res = $ac->execute();
        if (!empty($res['result']['data'])) {
            return $res['result']['data'];
        }
        return [
            VehicleInfoEnums::JOB_VAN_TITLE_ID,
            VehicleInfoEnums::JOB_COURIER_AND_INSTALLATION_STAFF_TITLE_ID,
            VehicleInfoEnums::JOB_PICKUP_DRIVER,
            VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID,
        ];
    }

    public function h5NewPageMobileVersion()
    {
        $set_val        = (new SettingEnvServer())->getSetVal('vehicle_h5_new_page_config');
        if (empty($set_val)) {
            return true;
        }
        $equipment_info = json_decode($set_val, true);
        return MobileHelper::compareVersion($equipment_info);
    }

    /**
     * 车辆信息
     * @Access  public
     * @param $paramIn
     * @return array
     */
    public function getVehicleInfoS($paramIn = [])
    {
        $res                        = $this->vehicle->getVehicleInfoR($paramIn['id']);
        $returnData['vehicle_info'] = [];

        //如果存在车辆信息显示车辆型号品牌名称
        if ($res) {
            //TODO 判断本月是否能换车
            $res['change_car']          = 1;
            $returnData['vehicle_info'] = $res;
        }
        $staffInfo  =  (new StaffServer())->getStaffInfo(['staff_info_id'=>$paramIn['id']]);

        $returnData['vehicle_info']          = $this->handleVehicleInfo($returnData['vehicle_info'], $paramIn);
        $returnData['is_first_submit']       = $this->checkStaffSelfSubmit($paramIn['id']);
        $returnData['is_show_vehicle_check'] = in_array($paramIn['job_title'], $this->getMileageJobTitleFromCache());
        $returnData['is_show_container']     = (new VanContainerServer($this->lang, $this->timeZone))->isShowVanContainer($staffInfo);
        return $returnData;
    }

    /**
     * 判断员工本人是否填写过车辆信息
     * @param $staff_info_id
     * @return bool
     */
    public function checkStaffSelfSubmit($staff_info_id): bool
    {
        $sql    = "select staff_id  from vehicle_info_log where staff_id= :staff_id and !(text like '%_url%' or text like '%fbid%')";
        $result = $this->getDI()->get("db_rby")->query($sql, ["staff_id" => $staff_info_id])->fetchAll();
        return empty($result);
    }


    /**
     * 里程信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getMileageS($paramIn = [], $userinfo = [])
    {
        $returnData = $resImgArr = [];
        //获取里程信息
        $res = $this->vehicle->dayMileageCountR($paramIn['mileage_date'], $userinfo['id']);
        if ($res) {
            //获取里程对应的图片信息SMI-上班 // EMI-下班
            $resImg = $this->vehicle->getMileageImgR($res['id']);
            if ($resImg) {
                foreach ($resImg as $k => $v) {
                    if (strstr($v['object_key'], 'SMI')) {
                        $resImgArr['started_path'] = $v['object_key'];
                        $resImgArr['started_bucket'] = $v['bucket_name'];
                        $resImgArr['started_img'] = convertImgUrl($v['bucket_name'], $v['object_key']);
                    } else if (strstr($v['object_key'], 'EMI')) {
                        $resImgArr['end_path'] = $v['object_key'];
                        $resImgArr['end_bucket'] = $v['bucket_name'];
                        $resImgArr['end_img'] = convertImgUrl($v['bucket_name'], $v['object_key']);
                    }
                }
            }
            $res = $res + $resImgArr;
        } else {
            $res = (object)[];
        }
        //获取本月补里程次数
        $count = $this->vehicle->getMileageCountR($userinfo['id'], $paramIn['mileage_date']);
        //月次数计算和剩余提示
        $count_str = str_replace("{}", (3 - $count['record_count']), $this->getTranslation()->_('7137'));
        //提示已经用完
        $alert = $count['record_count'] >= 3 ? $this->getTranslation()->_('7138') : 3 - $count['record_count'];
        //网点补里程大小控制
        $branchMileageArr = UC('vehicleInfo')['branch_mileage'];
        $lang = $this->getTranslation()->_('7140');
        $branch_mileage_msg = key_exists($userinfo['organization_id'] ,$branchMileageArr) ? str_replace( '50',bcdiv($branchMileageArr[$userinfo['organization_id']],1000,0) ,$lang) : $lang;
        $branch_mileage = key_exists($userinfo['organization_id'] ,$branchMileageArr) ? $branchMileageArr[$userinfo['organization_id']] : 50000;

        //当日里程信息和历史申请次数
        $returnData['data'] = $count;
        $returnData['data']['list'] = $res;
        $returnData['data']['count'] = $count_str;
        $returnData['data']['alert'] = $alert;
        $returnData['data']['branch_mileage'] = bcdiv($branch_mileage,1000,0);
        $returnData['data']['branch_mileage_msg'] = $branch_mileage_msg;
        return $this->checkReturn($returnData);
    }

    /**
     * @description 获取里程信息
     * @param array $paramIn
     * @param array $userinfo
     * @return array
     * @throws ValidationException
     */
    public function getMileageSV2($paramIn = [], $userinfo = []): array
    {
        $data['staff_info_id'] = $userinfo['id'];
        $data['mileage_date']  = $paramIn['mileage_date'];
        $ac                    = new ApiClient($this->getSvcHost(), '', 'mileage.get_repair_info', $this->lang);
        $ac->setParams($data);
        $result = $ac->execute();

        if ($result['result']['code'] != ErrCode::SUCCESS) {
            throw new ValidationException($result['result']['msg']);
        }
        return $this->checkReturn($result['result']['data'] ?? []);
    }

    /**
     * 创建车辆信息 $userinfo 里只有 id 和 job_title 如果要用别的 记得在外层新增参数
     * @Access  public
     * @Param   request
     * @Return  array
     * @throws ValidationException
     */
    public function addVehicleInfoS($paramIn , $userinfo,$operate_staff_id = '')
    {
        $returnData['data'] = [];

        // 整合入表字段
        $vehicleData = $this->filterVehicleData($paramIn, $userinfo);

        //查询验证是否有数据
        $vehicleInfo = $this->vehicle->getVehicleInfoR($vehicleData['uid']);
        if (!empty($vehicleInfo) && ($vehicleInfo['approval_status'] == VehicleInfoEnums::APPROVAL_PENDING_CODE)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0006'));
        }

        // 验证车牌号 是否 与 其他在职人的车牌号重复
        if ($this->checkPlateNumberIsExist($vehicleData['plate_number'], $userinfo['id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0001'));
        }

        // 验证发动机号 是否 与 其他在职人的发动机号重复
        if ($this->checkEngineNoIsExist($vehicleData['engine_number'], $userinfo['id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0003'));
        }

        // 验证开始用车日期, 不得早于入职日期
        $hire_date = $this->getStaffHireDate($userinfo['id']);
        if ($vehicleData['vehicle_source'] == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE && !empty($vehicleData['vehicle_start_date']) && $vehicleData['vehicle_start_date'] < $hire_date) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0007'));
        }

        if (empty($vehicleInfo)) {
            $vehicleData['creator_id'] = $userinfo['id'];
            $vehicleData['create_channel'] = VehicleInfoEnums::VEHICLE_ADD_CHANNEL_KIT;
            $vehicleData['formal_data'] = '';
            $res = $this->vehicle->addVehicleInfoR($vehicleData);
        } else {
            $res = $this->vehicle->updateVehicleInfo($vehicleData);
        }

        if ($res) {
            // 车辆信息日志
            $vehicleLogData = [];
            $vehicleLogData['staff_id'] = $vehicleData['uid'];
            $vehicleLogData['operate_staff_id'] = $operate_staff_id ?? $vehicleData['uid'];
            $vehicleLogData['text'] = json_encode($vehicleData, JSON_UNESCAPED_UNICODE);
            $this->vehicle->addVehicleInfoLog($vehicleLogData);

            if (isCountry()) {
                $this->createDrivingLicenseIdentifyRequest($vehicleLogData);
            }

            $returnData['data'] = $vehicleData['uid'];
            return $this->checkReturn($returnData);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }
    }

    /**
     * @descriptio 创建审批
     * @param array $paramIn
     * @param $userinfo
     * @return array
     */
    public function addVehicleS($paramIn = [], $userinfo): array
    {
        $paramIn['staff_info_id'] = $userinfo['id'];

        $ac     = new ApiClient($this->getSvcHost(), '', 'mileage.repair_vehicle', $this->lang);
        $ac->setParams($paramIn);
        $result = $ac->execute();

        $this->logger->write_log("addVehicleS result" . json_encode($result), 'info');

        if (empty($result)) {
            throw new ValidationException($this->getTranslation()->_('please try again'));
        }
        if ($result['result']['code'] != ErrCode::SUCCESS) {
            throw new ValidationException($result['result']['msg']);
        }

        return $this->checkReturn([]);
    }

    /**
     * @description 获取svc请求Host
     * @return string
     */
    protected function getSvcHost(): string
    {
        if (isCountry(['TH', 'MY'])) {
            $host = 'ard_api';
        } else {
            $host = 'bi_rpcv2';
        }
        return $host;
    }

    /**
     * 修改记录状态记录日志
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function updateVehicleStatus($paramIn, $userinfo)
    {
        //获取请求参数
        $auditId      = $this->processingDefault($paramIn, 'id');
        $status       = $this->processingDefault($paramIn, 'status');
        $staffId      = $this->processingDefault($userinfo, 'id');
        $rejectReason = $this->processingDefault($paramIn, 'reject_reason');

        //获取记录信息
        $result = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('please try again'));
        }
        $result = $result->toArray();

        //当前状态如果已经审批，申请人不可撤销
        if ($result['state'] != enums::APPROVAL_STATUS_PENDING && $status == enums::APPROVAL_STATUS_CANCEL) {
            return $this->checkReturn(-3, $this->getTranslation()->_('2206'));
        }
        //申请人撤销后,审批人不能审批
        if ($result['state'] == enums::APPROVAL_STATUS_CANCEL) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1016'));
        }

        $this->getDI()->get('db')->begin();
        try {
            //同意或者驳回等分开处理
            $server = new ApprovalServer($this->lang, $this->timeZone);

            if ($status == enums::APPROVAL_STATUS_APPROVAL) {
                //同意
                $server->approval($auditId, AuditListEnums::APPROVAL_TYPE_VEHICLE, $staffId, null);
            } elseif ($status == enums::APPROVAL_STATUS_REJECTED) {
                //驳回
                $server->reject($auditId, AuditListEnums::APPROVAL_TYPE_VEHICLE, $rejectReason, $staffId);
            } else {
                //撤销
                $server->cancel($auditId, AuditListEnums::APPROVAL_TYPE_VEHICLE, $rejectReason, $staffId);
            }

        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('update fleet failure:' . $e->getMessage(), 'error');
            return $this->checkReturn([]);
        }
        $this->getDI()->get('db')->commit();

        return $this->checkReturn([]);
    }

    /**
     * 调用push
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function pushVehicleMsg($paramIn, $userinfo)
    {
        try {
            //获取上级ID
            $newUserInfo['staff_info_id'] = $userinfo['id'];
            $higherUId = $this->ov->getHigherStaffId($newUserInfo);
            $lang = (new StaffServer())->getLanguage($higherUId['value']);
            $t = $this->getTranslation($lang);
            //调用push 发消息
            $message_title = $t->_('6007');
            $audit_type = $t->_('6010');

            $pushParam = [
                'staff_info_id' => $higherUId['value'],    //接收push信息人id
                'message_title' => $message_title,    //push标题
                'userinfo' => $userinfo,    //当前登陆用户信息
                'lastInsert_id' => $userinfo['id'],    //操作id
                'audit_type' => 'vehicle',    //模块名称
                'is_audit' => 1,    //xx状态
                'lang' => $lang    //xx状态
            ];
            $success = $this->public->pushMessage($pushParam);
            if ($success) {
                /* 执行修改操作 */
                $sql = "update vehicle_mileage set is_push = 1 where id = {$paramIn['id']} ";
                $this->getDI()->get('db')->query($sql);
            }
        } catch (\Exception $e) {
            $this->wLog('pushError', $e->getMessage(), 'vehicle',  'push');
        }
        return true;
    }

    /**
     * 车辆里程补卡申请记录次数
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getVehicleMileageS($paramIn, $userinfo)
    {
        $sql = " SELECT count(*) as record_count FROM vehicle_mileage where status = 1 AND apply_user = {$userinfo['id']}  AND mileage_date =  '{$paramIn['mileage_date']}' ;";
        $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }

    /**
     * 获取油卡充值记录
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffRecord($mouth, $staff_id)
    {
        $beign = date('Y-m-d', mktime(0, 0, 0, $mouth, 1, date('Y')));
        $end = date('Y-m-d', mktime(23, 59, 59, $mouth + 1, 0, date('Y')));
        $sql = "SELECT	s_mr.staff_info_id,
                mileage_date,
                start_kilometres,
                end_kilometres,
                prepaid_slip_no,
                s_mr.money,
                LEFT ( s_mr.updated_at, 10 ) AS updated_at,
                s_mr_info.recharge_at ,
                s_mr_info.state ,
                v_info.unit_price
            FROM
                staff_mileage_record as s_mr
            left join staff_mileage_record_prepaid_info as s_mr_info on s_mr.prepaid_slip_no = s_mr_info.prepaid_no and s_mr_info.`staff_id` = s_mr.`staff_info_id`
            left join vehicle_info as v_info on s_mr.staff_info_id = v_info.uid
            WHERE
                s_mr.staff_info_id = {$staff_id} 
                AND mileage_date >= '{$beign}' 
                AND mileage_date <= '{$end}';
        ";
        
        $info_data = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (empty($info_data)) {
            return [];
        }
        $list = [];
        foreach ($info_data as $key => $value) {
            $list[$key]['created_at'] = $value['mileage_date'];
            $list[$key]['kilometres'] =
                ($value['end_kilometres'] > 0 && $value['start_kilometres'] > 0) && ($value['end_kilometres'] > $value['start_kilometres']) ?
                bcdiv(bcsub($value['end_kilometres'], $value['start_kilometres']), 1000) : "NA";
            $list[$key]['fuel_prepaid'] = 0;
            $list[$key]['fuel_subsidies'] = 0;
            $list[$key]['buy_data'] = "NA";
            $list[$key]['fuel_prepaid'] = bcdiv($value['money'],100,2); //预计补贴金额
            if($info_data[$key]['state']){
                $list[$key]['fuel_subsidies'] = bcdiv($value['money'],100,2); //补贴金额;
                $list[$key]['buy_data'] = $value['updated_at'];
            }
                

            $list[$key]['recharge_at'] = $value['recharge_at'] ?? "NA";
        }
        return $list;
    }
    /**
     * 获取人员车辆信息
     */
    public function getVehicleInfoByStaffID($staff_id)
    {
        $sql = "SELECT
                `id`,
                `vehicle_brand`,
                `vehicle_model`,
                `vehicle_size`,
                `plate_number`,
                `buy_date`,
                `oil_number`,
                `oil_type`,
                `oil_company`,
                `vehicle_img`,
                `driving_licence_img`,
                `uid` as staff_id,
                `deleted`,
                `money`,
                `is_open`,
                `open_date`,
                `updated_at`,
                `created_at`,
                `is_cut_money`,
                balance
            FROM
                `vehicle_info` 
            WHERE
                uid = $staff_id
        ";
        $info_data = $this->getDI()->get('db')->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC);
        return $info_data ?? [];
    }

    /**
     * 获取车辆信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffVehicle($staff_id)
    {
        //非正式员工没有车辆信息
        $staffInfo = (new StaffRepository())->checkoutStaffBi($staff_id);
        if ($staffInfo['is_sub_staff'] == 1){
            // 子账号需切到主账号 获取信息
            $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timeZone))->getSupportInfoBySubStaff($staff_id);
            if (empty($supportStaffInfo)){
                return 2;
            }
            $staff_id = $supportStaffInfo['staff_info_id'];
            $staffInfo = (new StaffRepository())->checkoutStaffBi($staff_id);
        }
        if(empty($staffInfo) || $staffInfo['formal'] != 1){
            // 不是编制内，需要隐藏车辆信息栏的填写。
            return 2;
        }

        $curCountry = strtoupper(env('country_code'));
        if (in_array($curCountry,['TH','PH','MY','LA'])) {
            // 当前登入者职位id
            $curLoginUserJobTitleId = trim($staffInfo['job_title']);
            // 快递员职位
            $courierJobStr          = (new SettingEnvServer())->getSetVal('job_title_vehicle_type');
            $courierJobIds          = explode(',', $courierJobStr);
            if (!in_array($curLoginUserJobTitleId, $courierJobIds)) {
                // 如果当前登入者职位不是快递员，则隐藏车辆信息编辑栏
                return 2;
            }
        }

        $info_data = VehicleInfoModel::findFirst([
            'conditions' => 'uid = :uid: ',
            'bind' => ['uid' => $staff_id],
            'columns' => ['id', 'approval_status']
        ]);

        if (!empty($info_data) && in_array($info_data->approval_status, [1, 2,VehicleInfoEnums::APPROVAL_WAIT_NW_CODE])) {
            return 1;
        }

        return 0;
    }


    /**
     * 新里程数必须大于等于上次里程数
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function LastMileage($kilometres,$mileage_date,$userinfo)
    {
        $sql = " SELECT * FROM staff_mileage_record WHERE mileage_date <= '{$mileage_date}'  AND staff_info_id = '{$userinfo['id']}' ORDER BY mileage_date DESC LIMIT 1;";
        $info_data = $this->getDI()->get('db_rby')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        $kilometresNext = ( isset( $info_data['end_kilometres'] ) && !empty($info_data['end_kilometres']) )  ? $info_data['end_kilometres'] : $info_data['start_kilometres'];//里程缺失判断

        if (empty($info_data) || $info_data['change_car'] == 1) {
            return 0;
        }

        if($mileage_date == $info_data['mileage_date']){
            return 0;
        }

        if($kilometresNext <= $kilometres){
            return 0;
        }else{
            return 1;
        }
    }


    /**
     * 在当前选择的时间之后，已有新的里程数。当前里程数不能大于新的里程数
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function NextMileage($kilometres,$mileage_date,$userinfo)
    {
        $sql = " SELECT * FROM staff_mileage_record WHERE mileage_date > '{$mileage_date}'  AND staff_info_id = '{$userinfo['id']}' ORDER BY mileage_date ASC LIMIT 1;";
        $info_data = $this->getDI()->get('db_rby')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);

        if(!empty($info_data) && $info_data['change_car'] == 1){
            return 0;
        }
        $kilometresNext = ( isset( $info_data['start_kilometres'] ) && !empty($info_data['start_kilometres']) )  ? $info_data['start_kilometres'] : $info_data['end_kilometres'];//里程缺失判断
        if (empty($info_data)) {
            return 0;
        }else if($kilometresNext >= $kilometres){
            return 0;
        }else{
            return 1;
        }
    }


    /**
     * 获取里程信息[bi使用]
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffMileageS($paramIn = [])
    {
        return $res = $this->vehicle->dayMileageCountR($paramIn['mileage_date'], $paramIn['staff_info_id']);
    }

    /**
     * 修改里程信息[bi使用]
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function updateStaffMileageS($paramIn = [])
    {
        $res = $this->vehicle->updateStaffMileageR($paramIn);
        return $res;
    }

    /**
     * 获取人员车辆信息
     */
    public function getVehicleInfoByWhere($where = [])
    {
        if(empty($where)){
            return [];
        }

        $sql = "SELECT
                `id`,
                `vehicle_brand`,
                `vehicle_model`,
                `vehicle_size`,
                `plate_number`,
                `buy_date`,
                `oil_number`,
                `oil_type`,
                `oil_company`,
                `vehicle_img`,
                `driving_licence_img`,
                `uid` as staff_id,
                `deleted`,
                `money`,
                `is_open`,
                `open_date`,
                `updated_at`,
                `created_at`,
                `is_cut_money`,
                balance
            FROM
                `vehicle_info` 
            WHERE
                1 = 1

        ";
        $str_where = '';
        if (isset($where['oil_company'])){
            $str_where .= ' and oil_company = 3 ';//PT
        }
        $info_data = $this->getDI()->get('db')->fetchAll($sql.$str_where,\Phalcon\Db::FETCH_ASSOC);
        return $info_data ?? [];
    }

    /**
     * 判断本月能修改几次
     */
    public function getStaffRecordMileage($mouth, $staff_id)
    {
        try{
            $beign = date('Y-m-d', mktime(0, 0, 0, $mouth, 1, date('Y')));
            $end = date('Y-m-d', mktime(23, 59, 59, $mouth + 1, 0, date('Y')));
            $sql = "SELECT	s_mr.staff_info_id,
                    mileage_date,
                    prepaid_slip_no
                FROM
                    staff_mileage_record as s_mr
                WHERE
                    s_mr.staff_info_id = {$staff_id} 
                    AND mileage_date >= '{$beign}' 
                    AND mileage_date <= '{$end}'
                    AND change_car = 1;
            ";
            
            $info_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            return count($info_data ?? []);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage(),'error');
        }
        return 0;
    }

    /**
     * 里程信息【Van快递员新增“修改里程表数”使用】
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getMileageModifyS($paramIn = [], $userinfo = [])
    {
        $returnData = $resImgArr = [];
        //获取里程信息
        $res = $this->vehicle->dayMileageCountR($paramIn['mileage_date'], $userinfo['id']);
        if ($res) {
            //获取里程对应的图片信息SMI-上班 // EMI-下班
            $resImg = $this->vehicle->getMileageImgR($res['id']);
            if ($resImg) {
                foreach ($resImg as $k => $v) {
                    if (strstr($v['object_key'], 'SMI')) {
                        $resImgArr['started_path'] = $v['object_key'];
                        $resImgArr['started_bucket'] = $v['bucket_name'];
                        $resImgArr['started_img'] = convertImgUrl($v['bucket_name'], $v['object_key']);
                    } else if (strstr($v['object_key'], 'EMI')) {
                        $resImgArr['end_path'] = $v['object_key'];
                        $resImgArr['end_bucket'] = $v['bucket_name'];
                        $resImgArr['end_img'] = convertImgUrl($v['bucket_name'], $v['object_key']);
                    }
                }
            }
            $res = $res + $resImgArr;
            //当日已经修改的里程信息
            $NewInfo = $this->vehicle->getMileageModifyDetailR($userinfo['id'], $paramIn['mileage_date']);
            $res['new_start_kilometres'] = $NewInfo['start_kilometres'];
            $res['new_end_kilometres'] = $NewInfo['end_kilometres'];
            //by不补里程记录 不允许再次修改
            $res['permission'] = (isset($res['create_channel']) && $res['create_channel'] == 1 ) ? 0 : 1 ;
            $res['msg'] = (isset($res['create_channel']) && $res['create_channel'] == 1 ) ?$this->getTranslation()->_('mileage_modify_msg1') : '' ;
        } else {
            $res = (object)[];
        }
        //获取本月补里程次数
        $count = $this->vehicle->getMileageModifyCountR($userinfo['id'], $paramIn['mileage_date']);
        //月次数计算和剩余提示
        $count_str = str_replace("{}", (2 - $count['record_count']), $this->getTranslation()->_('7137'));
        //提示已经用完
        $alert = $count['record_count'] >= 2 ? $this->getTranslation()->_('7138') : 2 - $count['record_count'];



        $returnData['data'] = $count;
        $returnData['data']['list'] = $res;
        //$returnData['data']['count'] = $count_str;
        //$returnData['data']['alert'] = $alert;
        return $this->checkReturn($returnData);
    }
    /**
     * 车辆里程补卡申请记录次数【Van快递员新增“修改里程表数”使用】
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function dayMileageModifyS($paramIn, $userinfo)
    {
        $sql = " SELECT count(*) as record_count FROM approve_modify_mileage where staff_info_id = {$userinfo['id']}  AND mileage_date =  '{$paramIn['mileage_date']}' ;";
        $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }

    /**
     * 创建【Van快递员新增“修改里程表数”使用】
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addMileageModifyS($paramIn = [], $userinfo)
    {
        //格式化订单数据
        $serialNo = $this->getID();
        //当日已经修改的里程信息
        $returnData['data'] = [];
        $updateData['mileage_date'] = isset($paramIn['mileage_date']) ? $paramIn['mileage_date'] : '';//里程日期
        $updateData['start_kilometres'] = isset($paramIn['start_kilometres']) ? $paramIn['start_kilometres'] : '';//上班公里数(m)

        $updateData['end_kilometres'] = isset($paramIn['end_kilometres']) ? $paramIn['end_kilometres'] : '';//下班公里数(m)

        //$updateData['status'] = isset($paramIn['status']) ? $paramIn['status'] : 1;
        $updateData['staff_info_id'] = isset($userinfo['id']) ? $userinfo['id'] : '';//申请人
        $updateData['serial_no']   = !empty($serialNo) ?'MI'.$serialNo : NULL;

        $res = $this->vehicle->dayMileageCountR($updateData['mileage_date'], $userinfo['id']);
        $updateData['origin_id'] = isset($res['id']) ? $res['id'] : 0;//origin_id
        $updateData['origin_start_kilometres'] = isset($res['start_kilometres']) ? $res['start_kilometres'] : 0;//start_kilometres
        $updateData['origin_end_kilometres'] = isset($res['end_kilometres']) ? $res['end_kilometres'] : 0;//end_kilometres

        //插入数据
        $returnData['data'] = $this->vehicle->addMileageModifyR($updateData);
        if ($returnData['data']){
            return $this->checkReturn($returnData);
        }else{
            return $this->checkReturn(-3, $this->getTranslation()->_('2109'));
        }

    }

    /**
     * 获取检查清单项目
     * @param $userinfo
     * @return array[]
     */
    public function getCheckListItems($userinfo)
    {
        $t = $this->getTranslation();
        $checkList =
        [
            'staff'=>['name'=>$userinfo['name']],
            'checklist'=>[
            [
                'key'=>'car_lights_list',
                'name'=>$t['car_lights_list'],
                'items' => [
                    [
                        'name'=>$t['front_lights'],
                        'key' => 'front_lights',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['rear_lights'],
                        'key' => 'rear_lights',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['double_flash'],
                        'key' => 'double_flash',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['roof_lamp'],
                        'key' => 'roof_lamp',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['left_turn_light'],
                        'key' => 'left_turn_light',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['right_turn_light'],
                        'key' => 'right_turn_light',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ]
                ]
            ],
            [
                'key'=>'container_list',
                'name'=>$t['container_list'],
                'items' => [
                    [
                        'name'=>$t['left_container_wall'],
                        'key' => 'left_container_wall',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['right_container_wall'],
                        'key' => 'right_container_wall',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['wall_container_top'],
                        'key' => 'wall_container_top',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['container_door'],
                        'key' => 'container_door',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['door_lock'],
                        'key' => 'door_lock',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ]
                ]
            ],
            [
                'key'=>'safety_equipment_list',
                'name'=>$t['safety_equipment_list'],
                'items' => [
                    [
                        'name'=>$t['fire_extinguisher'],
                        'key' => 'fire_extinguisher',
                        'options'=>[
                            ['label'=>$t['yes'],'value'=>'yes'],
                            ['label'=>$t['no'],'value'=>'no']
                        ],
                        'selectValue' => 'yes'
                    ],
                    [
                        'name'=>$t['flashlight'],
                        'key' => 'flashlight',
                        'options'=>[
                            ['label'=>$t['yes'],'value'=>'yes'],
                            ['label'=>$t['no'],'value'=>'no']
                        ],
                        'selectValue' => 'yes'
                    ],
                    [
                        'name'=>$t['cone_or_triangle_reflective_sign'],
                        'key' => 'cone_or_triangle_reflective_sign',
                        'options'=>[
                            ['label'=>$t['yes'],'value'=>'yes'],
                            ['label'=>$t['no'],'value'=>'no']
                        ],
                        'selectValue' => 'yes'
                    ],
                    [
                        'name'=>$t['wheel_baffle_plate'],
                        'key' => 'wheel_baffle_plate',
                        'options'=>[
                            ['label'=>$t['yes'],'value'=>'yes'],
                            ['label'=>$t['no'],'value'=>'no']
                        ],
                        'selectValue' => 'yes'
                    ],
                    [
                        'name'=>$t['reflective_coat'],
                        'key' => 'reflective_coat',
                        'options'=>[
                            ['label'=>$t['yes'],'value'=>'yes'],
                            ['label'=>$t['no'],'value'=>'no']
                        ],
                        'selectValue' => 'yes'
                    ]
                ]
            ],
            [
                'key'=>'other_list',
                'name'=>$t['other_list'],
                'items' => [
                    [
                        'name'=>$t['tire_condition'],
                        'key' => 'tire_condition',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['wiper'],
                        'key' => 'wiper',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['brake'],
                        'key' => 'brake',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                ]
            ]
        ]
        ];

        return $checkList;
    }

    /**
     * @param $reportDate
     * @param $staffId
     * @param $duty
     * @param $checkList
     * @return bool
     */
    public function addStatusRecord($reportDate,$staffId,$duty,$checkList)
    {
        $column1 = $duty .'_duty_report_time';
        $column2 = $duty .'_duty_check_list';
        $reportTime = date('H:i:s',time());
        $data = [
            'report_date' => $reportDate,
            'report_time' => $reportTime,
            'staff_info_id' => $staffId,
            $column1 => $reportTime,
            $column2 => json_encode($checkList),
        ];

        $repo = new StaffVehicleStatusRecordRepository();
        $records = $repo->findAllByCondition(['staff_info_id'=>$staffId,'report_date'=>$reportDate]);
        $record = $records->getFirst();
        if ($record){
            return $repo->update($record,$data);
        }else{
            return $repo->insert($data);
        }
    }

    /**
     * 格式化车辆信息详情
     * @param array $vehicle_info
     * @param array $paramIn
     * @return mixed
     */
    protected function handleVehicleInfo(array $vehicle_info, array $paramIn)
    {
        if (empty($paramIn)) {
            return [];
        }

        // 为空, 补充默认字段 及 默认值
        if (empty($vehicle_info)) {
            // 车辆品牌及车辆型号、购买日期、油卡公司 van
            $vehicle_info['vehicle_brand'] = '';
            $vehicle_info['vehicle_brand_text'] = '';
            $vehicle_info['vehicle_model'] = '';
            $vehicle_info['vehicle_model_text'] = '';
            $vehicle_info['buy_date'] = null;
            $vehicle_info['oil_number'] = '';
            $vehicle_info['oil_company'] = '';

            // 车辆照片/机动车登记证 &&
            $vehicle_info['vehicle_img'] = '';
            $vehicle_info['registration_certificate_img'] = '';

            // 车辆保险 &&
            $vehicle_info['insurance_policy_number'] = '';
            $vehicle_info['insurance_start_date'] = null;
            $vehicle_info['insurance_end_date'] = null;

            // 车辆税 &&
            $vehicle_info['vehicle_tax_expiration_date'] = null;
            $vehicle_info['vehicle_tax_certificate_img'] = '';

            // 驾照信息 &&
            $vehicle_info['driver_license_type'] = '';
            $vehicle_info['driver_license_type_other_text'] = '';
            $vehicle_info['driver_license_start_date'] = null;
            $vehicle_info['driver_license_end_date'] = null;
            $vehicle_info['driving_licence_img'] = '';
            $vehicle_info['vehicle_check_video'] = '';
            $vehicle_info['vehicle_check_img_2'] = '';
            $vehicle_info['vehicle_check_img_1'] = '';
        } else {
            // 删除无需字段
            unset($vehicle_info['id']);
            unset($vehicle_info['deleted']);
            unset($vehicle_info['money']);
            unset($vehicle_info['is_open']);
            unset($vehicle_info['open_date']);
            unset($vehicle_info['updated_at']);
            unset($vehicle_info['created_at']);
            unset($vehicle_info['is_cut_money']);
            unset($vehicle_info['balance']);
            unset($vehicle_info['unit_price']);
            unset($vehicle_info['approval_staff_id']);
            unset($vehicle_info['approval_time']);
            unset($vehicle_info['creator_id']);
            unset($vehicle_info['editor_id']);
            unset($vehicle_info['create_channel']);
        }

        // 车辆类型, 职位优先
        $vehicle_info['vehicle_type'] = VehicleInfoEnums::JOB_VEHICLE_TYPE_REL_CODE[$paramIn['job_title']];
        $vehicle_info['vehicle_type_label'] = VehicleInfoEnums::VEHICLE_TYPE_ITEM[$vehicle_info['vehicle_type']];

        // 车辆来源: 同步fbi-hr_is数据
        $hr_staff_info = HrStaffInfoServer::getUserInfoByStaffInfoId($paramIn['id'], 'vehicle_source, vehicle_use_date,job_title,hire_type');
        $hr_staff_info = $hr_staff_info ? $hr_staff_info->toArray() : [];

        $vehicle_info['job_title'] = $hr_staff_info['job_title'] ?? 0 ;
        $vehicle_info['hire_type'] = intval($hr_staff_info['hire_type']);

        if (empty($vehicle_info['vehicle_source'])) {

            $vehicle_info['vehicle_source'] = $hr_staff_info['vehicle_source'] ?? VehicleInfoEnums::VEHICLE_SOURCE_PERSONAL_CODE;
            $vehicle_info['vehicle_start_date'] = $hr_staff_info['vehicle_use_date'] ?? null;
        }

        $vehicle_info['vehicle_source_label'] = $vehicle_info['vehicle_source'] ? $this->getTranslation()->_(VehicleInfoEnums::VEHICLE_SOURCE_ITEM[$vehicle_info['vehicle_source']]) : '';

        // 车牌号 hr-is取默认值, 如没有，则再从whr_is取默认值
        if (empty($vehicle_info['plate_number'])) {
            $vehicle_info['plate_number'] = (new StaffRepository())->getAvatar($paramIn['id'], 'CAR_NO');
        }

        // 上牌地点/发动机号码/驾照号码 whr-is取默认值
        if (
            empty($vehicle_info['plate_number'])
        ||
            empty($vehicle_info['license_location'])
        ||
            empty($vehicle_info['engine_number'])
        ||
            empty($vehicle_info['driver_license_number'])
        ) {

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['entry' => HrEntryModel::class]);
            $builder->innerJoin(HrEconomyAbilityModel::class,'entry.resume_id = hr.resume_id', 'hr');
            $builder->where('entry.staff_id = :staff_id:', ['staff_id' => $paramIn['id']]);
            $builder->columns([
                'hr.car_number',// 车牌号
                'hr.driver_number',//驾照号
                'hr.place_cards',//上牌地点
                'hr.car_engine_number',//发动机号
            ]);
            $win_staff_info = $builder->getQuery()->getSingleResult();
            if (!empty($win_staff_info)) {
                $vehicle_info['plate_number'] = !empty($vehicle_info['plate_number']) ? $vehicle_info['plate_number'] : $win_staff_info->car_number ?? '';
                $vehicle_info['license_location'] = !empty($vehicle_info['license_location']) ? $vehicle_info['license_location'] : $win_staff_info->place_cards ?? '';
                $vehicle_info['engine_number'] = !empty($vehicle_info['engine_number']) ? $vehicle_info['engine_number'] : $win_staff_info->car_engine_number ?? '';
                $vehicle_info['driver_license_number'] = !empty($vehicle_info['driver_license_number']) ? $vehicle_info['driver_license_number'] : $win_staff_info->driver_number ?? '';
            }
        }

        $vehicle_setting = UC('vehicleInfo');

        // 油卡企业
        $vehicle_info['oil_company_label'] = '';
        if (!empty($vehicle_info['oil_company'])) {
            $oil_company_conf = array_column($vehicle_setting['oil_company'], 'label', 'value');
            $vehicle_info['oil_company_label'] = $oil_company_conf[$vehicle_info['oil_company']] ?? '';
        }

        // 车辆品牌/车辆型号
        $vehicle_info['vehicle_brand_label'] = '';
        $vehicle_info['vehicle_model_label'] = '';
        if ($vehicle_info['vehicle_type'] == VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE && !empty($vehicle_info['vehicle_brand'])) {
            $vehicle_brand_conf = array_column($vehicle_setting['vehicle_brand'], null, 'value');
            $vehicle_model_conf = array_column($vehicle_brand_conf[$vehicle_info['vehicle_brand']]['data'], 'label', 'value');

            $vehicle_info['vehicle_brand_label'] = $vehicle_brand_conf[$vehicle_info['vehicle_brand']]['label'] ?? '';
            $vehicle_info['vehicle_model_label'] = !empty($vehicle_info['vehicle_model']) ? $vehicle_model_conf[$vehicle_info['vehicle_model']] : '';
        }

        // 车型
        $vehicle_info['vehicle_size'] = $vehicle_info['vehicle_size'] ?? '';
        $vehicle_info['vehicle_size_label'] = self::getVehicleSize(true)[$vehicle_info['vehicle_size']]?? '';

        // 油类型
        $vehicle_info['oil_type'] = $vehicle_info['oil_type'] ?? '';
        $vehicle_info['oil_type_label'] = $vehicle_info['oil_type'] ? $vehicle_setting['oil_type'][$vehicle_info['oil_type']] : '';

        // 驾照图片(两张) &&
        $vehicle_info['driving_licence_img_item'] = [];
        if (!empty($vehicle_info['driving_licence_img'])) {
            $vehicle_info['driving_licence_img_item'] = explode("\n", $vehicle_info['driving_licence_img']);
        }

        // 驾照类型 &&
        $vehicle_info['driver_license_type_label'] = '';
        if (!empty($vehicle_info['driver_license_type'])) {
            foreach (VehicleInfoEnums::DRIVER_LICENSE_TYPE_ITEM as $license_k => $license_v) {
                if ($license_k == $vehicle_info['driver_license_type']) {
                    $driver_license_type_label = is_array($license_v) ? VehicleInfoEnums::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.$license_k : $license_v;
                    $vehicle_info['driver_license_type_label'] = $this->getTranslation()->_($driver_license_type_label);

                    break;
                }

                if (is_array($license_v)) {
                    foreach ($license_v as $sub_k => $sub_v) {
                        if ($sub_k == $vehicle_info['driver_license_type']) {
                            $vehicle_info['driver_license_type_label'] = $this->getTranslation()->_($sub_v);
                            break;
                        }
                    }
                }
            }
        }

        // 审核状态
        $vehicle_info['approval_status'] = $vehicle_info['approval_status'] ?? VehicleInfoEnums::APPROVAL_UN_SUBMITTED_CODE;

        // 员工入职日期
        $vehicle_info['staff_hire_date'] = $this->getStaffHireDate($paramIn['id']);
        return $vehicle_info;
    }

    /**
     * 提取不同职位需入库的字段
     * @param array $vehicle_data
     * @param array $user_info
     * @return array $data
     */
    protected function filterVehicleData(array $vehicle_data, array $user_info)
    {
        // 公共字段
        $data = [
            'uid' => $user_info['id'],
            'vehicle_source' => $vehicle_data['vehicle_source'],
            'plate_number' => $vehicle_data['plate_number'],
            'license_location' => $vehicle_data['license_location'],
            'registration_certificate_img' => $vehicle_data['registration_certificate_img'],
            'vehicle_img' => $vehicle_data['vehicle_img'],
            'insurance_policy_number' => $vehicle_data['insurance_policy_number'],
            'insurance_start_date' => $vehicle_data['insurance_start_date'],
            'insurance_end_date' => $vehicle_data['insurance_end_date'],
            'vehicle_tax_expiration_date' => $vehicle_data['vehicle_tax_expiration_date'],
            'vehicle_tax_certificate_img' => $vehicle_data['vehicle_tax_certificate_img'],
            'driver_license_type' => $vehicle_data['driver_license_type'],
            'driver_license_type_other_text' => $vehicle_data['driver_license_type'] != 100 ? '' : $vehicle_data['driver_license_type_other_text'] ?? '',
            'driver_license_number' => $vehicle_data['driver_license_number'],
            'driver_license_start_date' => $vehicle_data['driver_license_start_date'],
            'driver_license_end_date' => $vehicle_data['driver_license_end_date'],
            'driving_licence_img' => implode("\n", $vehicle_data['driving_licence_img_item']),
            'vehicle_type' => VehicleInfoEnums::VEHICLE_TYPE_BIKE_CODE,
            'engine_number' => $vehicle_data['engine_number'],

            // 重置审核信息
            'approval_status' => VehicleInfoEnums::APPROVAL_PENDING_CODE,
            'approval_staff_id' => '',
            'approval_time' => null,
            'approval_remark' => '',

            'editor_id' => $user_info['id'],
        ];

        // 用车开始日期
        if ($vehicle_data['vehicle_source'] == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
            $data['vehicle_start_date'] = $vehicle_data['vehicle_start_date'];
        }

        // van 特有字段
        if (in_array($user_info['job_title'], VehicleInfoEnums::VAN_JOB_GROUP_ITEM)) {
            $data['vehicle_brand'] = $vehicle_data['vehicle_brand'];
            $data['vehicle_brand_text'] = $vehicle_data['vehicle_brand'] != 8 ? '' : $vehicle_data['vehicle_brand_text'] ?? '';
            $data['vehicle_model'] = $vehicle_data['vehicle_model'];
            $data['vehicle_model_text'] = $vehicle_data['vehicle_model'] != 100 ? '' : $vehicle_data['vehicle_model_text'] ?? '';
            $data['vehicle_size'] = $vehicle_data['vehicle_size'];
            $data['buy_date'] = $vehicle_data['buy_date'];
            $data['oil_type'] = $vehicle_data['oil_type'];

            $data['vehicle_type'] = VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE;
        }

        return $data;
    }

    /**
     * 验证车牌号是否与在职人重复
     * @param string $plate_number
     * @param int $user_id
     * @param string $license_location
     * @return mixed
     */
    protected function checkPlateNumberIsExist(string $plate_number, int $user_id, string $license_location = '')
    {
        if (empty($plate_number) || empty($user_id)) {
            return true;
        }

        $plate_number = nameSpecialCharsReplace($plate_number);
        $conditions = 'plate_number = :plate_number: ';
        $bind['plate_number'] = $plate_number;
        if(!empty($license_location)) {
            $conditions .= ' and license_location = :license_location: ';
            $bind['license_location'] = $license_location;
        }

        $plate_number_staff_item = VehicleInfoModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => ['uid']
        ])->toArray();
        $plate_number_staff_item = $plate_number_staff_item ? array_column($plate_number_staff_item,  'uid', 'uid') : [];
        if (!empty($plate_number_staff_item[$user_id])) {
            unset($plate_number_staff_item[$user_id]);
        }
        $hr_is_plate_number_item = [];
        $staff_count = 0;
        $other_staff_item = array_merge($plate_number_staff_item, $hr_is_plate_number_item);
        if (!empty($other_staff_item)) {
            // 是否在职
            $staff_count = \FlashExpress\bi\App\Models\backyard\HrStaffInfoModel::count([
                'conditions' => ' staff_info_id IN ({ids:array}) AND state = :state:',
                'bind' => ['ids' => array_values($other_staff_item), 'state' => \FlashExpress\bi\App\Models\backyard\HrStaffInfoModel::STATE_1]
            ]);
        }

        return (bool)$staff_count;
    }

    /**
     * 验证发动机号是否与在职人重复
     * @param string $engine_number
     * @param int $user_id
     * @return mixed
     */
    protected function checkEngineNoIsExist(string $engine_number, int $user_id)
    {
        if (empty($engine_number) || empty($user_id)) {
            return true;
        }

        $engine_number_staff_item = VehicleInfoModel::find([
            'conditions' => 'engine_number = :engine_number:',
            'bind' => ['engine_number' => $engine_number],
            'columns' => ['uid']
        ])->toArray();
        $engine_number_staff_item = $engine_number_staff_item ? array_column($engine_number_staff_item,  'uid', 'uid') : [];
        if (!empty($engine_number_staff_item[$user_id])) {
            unset($engine_number_staff_item[$user_id]);
        }

        $staff_count = 0;
        if (!empty($engine_number_staff_item)) {
            // 是否在职
            $staff_count = StaffInfoModel::count([
                'conditions' => 'id IN ({ids:array}) AND state = :state:',
                'bind' => ['ids' => array_values($engine_number_staff_item), 'state' => 1]
            ]);
        }

        return $staff_count ? true : false;
    }

    /**
     * 获取员工入职日期
     * @param $staff_info_id
     * @return mixed
     */
    protected function getStaffHireDate($staff_info_id)
    {
        if (empty($staff_info_id)) {
            return '';
        }

        // 员工入职日期
        $hr_staff_info = HrStaffInfoServer::getUserInfoByStaffInfoId($staff_info_id, 'hire_date');
        return !empty($hr_staff_info->hire_date) ? substr($hr_staff_info->hire_date, 0, 10) : '';
    }

    /**
     * 创建图片识别请求
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function createDrivingLicenseIdentifyRequest($params): bool
    {
        $uid = $this->processingDefault($params, 'staff_id', 2);

        if (!isCountry()) { //只有泰国有驾驶证识别
            return false;
        }

        //获取驾驶证图片 机动车登记证 车辆税证明
        $vehicleInfo = VehicleInfoModel::findFirst([
            "conditions" => "uid = :uid:",
            "bind"       => [
                'uid' => $uid
            ],
        ]);
        if (!($vehicleInfo instanceof VehicleInfoModel)) {
            throw new ValidationException('Invalid paramters');
        }
        //驾驶证 上传正反面 获取正面识别
        $drivingLicenseImageArr = isset($vehicleInfo->driving_licence_img) && $vehicleInfo->driving_licence_img
            ? explode(PHP_EOL, $vehicleInfo->driving_licence_img)
            : [];
        $drivingLicenseImage = !empty($drivingLicenseImageArr) ? current($drivingLicenseImageArr): "";
        //提交申请
        $data = [];
        $base_data = [
            'vehicle_info_id' => $vehicleInfo->id,
            'state' => enums::IMAGE_IDENTIFY_STATE_NOT_SUBMIT,
            'uid' => $uid
        ];
        if ($drivingLicenseImage) {
            $data[] = array_merge($base_data, [
                'image_type' => VehicleInfoImageIdentificationModel::DRIVING_LICENCE_TYPE,
                'image_url'  => $drivingLicenseImage
            ]);
        }
        //车辆登记证书图片
        if ($vehicleInfo->registration_certificate_img) {
            $data[] = array_merge($base_data, [
                'image_type' => VehicleInfoImageIdentificationModel::REG_CER_TYPE,
                'image_url'  => $vehicleInfo->registration_certificate_img
            ]);
        }
        //车辆税证明图片
        if ($vehicleInfo->vehicle_tax_certificate_img) {
            $data[] = array_merge($base_data, [
                'image_type' => VehicleInfoImageIdentificationModel::VEHICLE_CER_TYPE,
                'image_url'  => $vehicleInfo->vehicle_tax_certificate_img
            ]);
        }
        //TODO 添加未做图片唯一校验，后台修改会调用新增数据，产品约：后台添加操作未关联创建图片识别数据
        if ($data) {
            (new VehicleInfoImageIdentificationModel)->batch_insert($data, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
        }
        return true;
    }

    /**
     * 获取比对信息
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getDrivingLicenseComparisonInfo($params): array
    {
        $vehicleInfoId = $this->processingDefault($params, 'vehicle_info_id');

        $vehicleInfo = VehicleInfoModel::findFirst($vehicleInfoId);
        if (!($vehicleInfo instanceof VehicleInfoModel)) {
            throw new ValidationException('Invalid paramters');
        }
        $vehicleInfo = $vehicleInfo->toArray();
        $returnData =   [
            "driver_license_type" => 0, //驾照类型
            "driver_license_number" => 0, //驾照号码
            "driver_license_start_date" => 0, //驾照开始有效时间
            "driver_license_end_date" => 0, //驾照过期时间
            "plate_number" => 0, //车牌号
            "license_location" => 0,//上牌地点
            "engine_number" => 0,// 发动机号码
            "vehicle_tax_expiration_date" => 0,//车辆税失效时间
        ];
        //获取图片识别信息
        //求出三类识别最新的数据
        $ids = VehicleInfoImageIdentificationModel::find([
            "conditions" => "vehicle_info_id = :vehicle_info_id:  and state = :state:",
            "bind"       => [
                'vehicle_info_id' => $vehicleInfoId,
                'state' => enums::IMAGE_IDENTIFY_STATE_SUC,
            ],

            "group" => "image_type",
            "columns" => ['max(id) as id']
        ]);
        $ids = $ids->toArray();
        if (!$ids) {
            return $returnData;
        }
        $exec = VehicleInfoImageIdentificationModel::find([
            "conditions" => " id in ({id:array})",
            "bind"       => [
                'id' => array_column($ids,'id'),
            ]
        ]);
        $list = $exec->toArray();
        if ($list) {
            $recognition_result = array_column($list,null,'image_type');
            $recognition_result = array_map(function ($item) {
                if ($item['result']) {
                    $identificationResult = json_decode($item['result'] ,true);
                    $item['result'] = $identificationResult['result'] ?? [];
                }
                return $item;
            },$recognition_result);
            //驾照相关

            if (isset($recognition_result[VehicleInfoImageIdentificationModel::DRIVING_LICENCE_TYPE])) {
                $driverLicenseResult = $recognition_result[VehicleInfoImageIdentificationModel::DRIVING_LICENCE_TYPE];
                $result = [
                    "driver_license_type" => $this->getDrivingLicenseType($driverLicenseResult['result']['type_en'] ?? ""),
                    "driver_license_number_en" => stripcslashes($driverLicenseResult['result']['no_en'] ?? ""),
                    "driver_license_number_th" => stripcslashes($driverLicenseResult['result']['no_thai'] ?? ""),
                    "driver_license_start_date" => date("Y-m-d", strtotime($driverLicenseResult['result']['issue_date_en']?? "")),
                    "driver_license_end_date" => date("Y-m-d", strtotime($driverLicenseResult['result']['expire_date_en'] ?? "")),
                ];
                $returnData['driver_license_type'] = $vehicleInfo['driver_license_type'] == $result['driver_license_type'] ? 1 : 2; //驾照类型
                $returnData['driver_license_number'] = in_array($vehicleInfo['driver_license_number'], [$result['driver_license_number_en'], $result['driver_license_number_th']]) ? 1 : 2; //驾照号码
                $returnData['driver_license_start_date'] = $vehicleInfo['driver_license_start_date'] == $result['driver_license_start_date']  ? 1 : 2;//驾照开始有效时间
                $returnData['driver_license_end_date'] = $vehicleInfo['driver_license_end_date'] == $result['driver_license_end_date'] ? 1 : 2; //驾照过期时间
            }
            //车辆登记证书图片
            if (isset($recognition_result[VehicleInfoImageIdentificationModel::REG_CER_TYPE])) {
                $regCerResult = $recognition_result[VehicleInfoImageIdentificationModel::REG_CER_TYPE];
                //只识别发动机号码
                $returnData['engine_number']  =  ($regCerResult['result']['engine_number'] ?? "") == $vehicleInfo['engine_number'] ? 1 : 2;
            }
            if (isset($recognition_result[VehicleInfoImageIdentificationModel::VEHICLE_CER_TYPE])) {
                $vehicleCerResult = $recognition_result[VehicleInfoImageIdentificationModel::VEHICLE_CER_TYPE];
                $returnData['plate_number'] = ($vehicleCerResult['result']['plate_number'] ?? "") == $vehicleInfo['plate_number'] ? 1 : 2; //车牌号
                $returnData['license_location'] = ($vehicleCerResult['result']['province_of_registration'] ?? "") == $vehicleInfo['license_location'] ? 1 : 2;//上牌地点
                $returnData['vehicle_tax_expiration_date'] = ($vehicleCerResult['result']['date'] ?? "") == $vehicleInfo['vehicle_tax_expiration_date'] ? 1 : 2;//车辆税失效时间
            }
        }
        return $returnData;
    }

    /**
     * 获取驾驶证类型
     * @param $type_string
     * @return int
     */
    public function getDrivingLicenseType($type_string): int
    {
        switch (strtolower($type_string)) {
            case 'private car driving licence (temporary)':
                $type = 1;
                break;
            case 'private motorcycle driving licence (temporary)':
                $type = 2;
                break;
            case 'private car driving licence':
                $type = 3;
                break;
            case 'private vehicle driving licence class i':
                $type = 4;
                break;
            case 'private vehicle driving licence class ii':
                $type = 5;
                break;
            case 'private vehicle driving licence class iii':
                $type = 6;
                break;
            case 'private vehicle driving licence class iv':
                $type = 7;
                break;
            case "public vehicle driving licence":
                $type = 8;
                break;
            case 'public vehicle driving licence class i':
                $type = 9;
                break;
            case 'public vehicle driving licence class ii':
                $type = 10;
                break;
            case 'public vehicle driving licence class iii':
                $type = 11;
                break;
            case 'public vehicle driving licence class iv':
                $type = 12;
                break;
            case "private motorcycle driving licence":
                $type = 13;
                break;
            case "public motorcycle driving licence":
                $type = 14;
                break;
            default:break;
        }
        return $type ?? 0;
    }

    /**
     * 网点统计HC
     * 规则：
     *    NETWORK：HC总数 = 在职 + 待入职（已发offer） + 招聘中（审批通过）+ 审批中HC; 使用人数：在职人数 + 待入职人数(已发offer);
     *    SHOP:    HC总数 = HC预算总数;                                           使用人数：在职人数 + 待入职人数(已发offer);
     * @param $params
     * @return array
     */
    public function getStoreJobTitleNumberInfo($params)
    {
        if(empty($params['type']) || empty($params['store_ids'])) {
            return [];
        }
        if($params['type'] == self::NETWORK_TAG) {//NW
            $job_ids = [
                enums::$job_title['bike_courier'],
                enums::$job_title['branch_supervisor'],
                enums::$job_title['dc_officer'],
                enums::$job_title['van_courier'],
                enums::$job_title['boat_courier']
            ];
            if(isCountry('MY') == 'MY'){
                array_push($job_ids, enums::$job_title['car_courier']);
            }
        } else {// SHOP
            $job_ids = [
                enums::$job_title['shop_cashier'],
                enums::$job_title['shop_officer'],
                enums::$job_title['shop_supervisor']
            ];
        }

        $workNodeIds = explode(",", $params['store_ids']);
        if(empty($workNodeIds)) {
            return [];
        }

        $where['store_ids'] = $workNodeIds;
        $where['job_title_ids'] = $job_ids;
        if($params['type'] == self::NETWORK_TAG){
            //NETWORK
            //获取招聘中人数
            $surplusNumber     = $this->formatResult($this->getSurplusNumber($where));
            //获取已提交的HC总人数（待审批）
            $budgetAdoptNumber = $this->formatResult($this->getBudgetAdoptNumber($where));
        } else {
            //SHOP
            //总职位数(HC预算总数)
            $total = $this->formatResult($this->getHcTotalNumber($where));
        }
        //在职人数
        $onTheJobNumber = $this->formatResult($this->getOnTheJobNumber($where));
        //待入职人数
        $waitingToEmployeeNumber = $this->formatResult($this->getWaitingToEmployeeNumber($where));

        $newWorkNodeId = [];
        foreach ($workNodeIds as $oneWorkNodeId) {
            $newWorkNodeId[$oneWorkNodeId]['courier']['number'] = 0;
            $newWorkNodeId[$oneWorkNodeId]['courier']['total'] = 0;
            foreach ($job_ids as $oneJob) {
                //在职人数
                $storeJobOnTheJobNumber          = $onTheJobNumber[$oneWorkNodeId  . '-' . $oneJob] ?? 0;
                //待入职人数
                $storeJobWaitingToEmployeeNumber = $waitingToEmployeeNumber[$oneWorkNodeId  . '-' . $oneJob] ?? 0;

                if($params['type'] == self::NETWORK_TAG) {//tab:NW
                    //招聘中人数
                    $storeJobSurplusNumber = $surplusNumber[$oneWorkNodeId  . '-' . $oneJob] ?? 0;
                    //已提交的HC总人数（待审批）
                    $storeJobBudgetAdoptNumber = $budgetAdoptNumber[$oneWorkNodeId  . '-' . $oneJob] ?? 0;
                    $countNumber = $storeJobOnTheJobNumber + $storeJobWaitingToEmployeeNumber;
                    //HC总数量 = 在职人数 + 待入职人数 + 招聘中人数 + 已提交的HC总人数（待审批）
                    $storeJobTotalNumber             = $countNumber + $storeJobSurplusNumber + $storeJobBudgetAdoptNumber;
                    $endResult = $countNumber . '/' . $storeJobTotalNumber;
                    if($oneJob == enums::$job_title['branch_supervisor']) {
                        $newWorkNodeId[$oneWorkNodeId]['branch_supervisor']['number'] = $endResult;
                    }
                    if($oneJob == enums::$job_title['dc_officer']) {
                        $newWorkNodeId[$oneWorkNodeId]['dc_officer']['number'] = $endResult;
                    }
                    if($oneJob == enums::$job_title['bike_courier']) {
                        $newWorkNodeId[$oneWorkNodeId]['courier']['number'] += $countNumber;
                        $newWorkNodeId[$oneWorkNodeId]['courier']['total'] += $storeJobTotalNumber;
                        $newWorkNodeId[$oneWorkNodeId]['courier']['subs']['bike_courier'] = $endResult;
                    }
                    if($oneJob == enums::$job_title['van_courier']) {
                        $newWorkNodeId[$oneWorkNodeId]['courier']['number'] += $countNumber;
                        $newWorkNodeId[$oneWorkNodeId]['courier']['total'] += $storeJobTotalNumber;
                        $newWorkNodeId[$oneWorkNodeId]['courier']['subs']['van_courier'] = $endResult;
                    }
                    if($oneJob == enums::$job_title['boat_courier']) {
                        $newWorkNodeId[$oneWorkNodeId]['courier']['number'] += $countNumber;
                        $newWorkNodeId[$oneWorkNodeId]['courier']['total'] += $storeJobTotalNumber;
                        $newWorkNodeId[$oneWorkNodeId]['courier']['subs']['boat_courier'] = $endResult;
                    }
                    //目前只有马来有这个职位--car_courier
                    if(isCountry('MY') == 'MY'){
                        if($oneJob == enums::$job_title['car_courier']) {
                            $newWorkNodeId[$oneWorkNodeId]['courier']['number'] += $countNumber;
                            $newWorkNodeId[$oneWorkNodeId]['courier']['total'] += $storeJobTotalNumber;
                            $newWorkNodeId[$oneWorkNodeId]['courier']['subs']['car_courier'] = $endResult;
                        }
                    }
                } else {//tab:SHOP
                    //HC总数量
                    $storeJobTotalNumber = $total[$oneWorkNodeId  . '-' . $oneJob] ?? 0;
                    $countNumber         = $storeJobOnTheJobNumber + $storeJobWaitingToEmployeeNumber;
                    $endResult           = $countNumber . '/' . $storeJobTotalNumber;
                    if($oneJob == enums::$job_title['shop_cashier']) {
                        $newWorkNodeId[$oneWorkNodeId]['shop_cashier']['number'] =  $endResult;
                    }
                    if($oneJob == enums::$job_title['shop_officer']) {
                        $newWorkNodeId[$oneWorkNodeId]['shop_officer']['number'] =  $endResult;
                    }
                    if($oneJob == enums::$job_title['shop_supervisor']) {
                        $newWorkNodeId[$oneWorkNodeId]['shop_supervisor']['number'] =  $endResult;
                    }
                }
            }
            $newWorkNodeId[$oneWorkNodeId]['courier']['number'] =  $newWorkNodeId[$oneWorkNodeId]['courier']['number'] . '/' . $newWorkNodeId[$oneWorkNodeId]['courier']['total'];
            unset($newWorkNodeId[$oneWorkNodeId]['courier']['total']);
            if($params['type'] != self::NETWORK_TAG){
                unset($newWorkNodeId[$oneWorkNodeId]['courier']);
            }
        }
        return $newWorkNodeId;
    }

    /**
     * 格式化数据
     * @param $data
     * @return array
     */
    public function formatResult($data)
    {
        $result = [];
        if(empty($data)) {
            return $result;
        }
        foreach ($data as $one) {
            $result[$one['sys_store_id'] . '-' . $one['job_title_id']] = $one['number'];
        }

        return $result;
    }

    /**
     * 计算网点HC职位总数
     * @param $where
     * @return mixed
     */
    public function getHcTotalNumber($where)
    {
        return HRStaffingModel::find([
            'conditions' => "job_title_id in ({job_title_ids:array}) and store_id in ({store_ids:array})",
            'bind'       => [
                'job_title_ids' => $where['job_title_ids'],
                'store_ids' => $where['store_ids']
            ],
            'columns' => "sum(count) as number, job_title_id, store_id as sys_store_id",
            'group' => 'store_id, job_title_id'
        ])->toArray();
    }

    /**
     * 获取在职人数
     * @param $where
     * @return mixed
     */
    public function getOnTheJobNumber($where)
    {
        return HrStaffInfoModel::find([
            'conditions' => "job_title in ({job_title_ids:array}) and sys_store_id in ({store_ids:array}) and formal in (1,4) and state = 1 and is_sub_staff = 0 and wait_leave_state = 0",
            'bind'       => [
                'job_title_ids' => $where['job_title_ids'],
                'store_ids' => $where['store_ids']
            ],
            'columns' => "count(*) as number, sys_store_id, job_title as job_title_id",
            'group' => 'sys_store_id , job_title'
        ])->toArray();
    }

    /**
     * 获取网点职位待入职人数
     * @param $where
     * @return array
     */
    public function getWaitingToEmployeeNumber($where)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("count(*) as number, hh.worknode_id as sys_store_id, hh.job_title as job_title_id");
        $builder->from(['hh' => HrHcModel::class]);
        $builder->leftJoin(HrInterviewModel::class, 'hh.hc_id = hi.hc_id', 'hi');
        $builder->where('hi.state = 25');
        $builder->inWhere('hh.job_title', $where['job_title_ids']);
        $builder->inWhere('hh.worknode_id', $where['store_ids']);
        $builder->groupBy('hh.job_title, hh.worknode_id');
        $result = $builder->getQuery()->execute();
        return $result ? $result->toArray() : [];
    }

    /**
     * 获取招聘中人数。
     * @param $where
     * @return mixed
     */
    public function getSurplusNumber($where)
    {
        return HrHcModel::find([
            'conditions' => "state_code = 2 and job_title in ({job_title_ids:array}) and worknode_id in ({store_ids:array})  and deleted = 1",
            'bind'       => [
                'job_title_ids' => $where['job_title_ids'],
                'store_ids' => $where['store_ids']
            ],
            'columns' => "sum(surplusnumber) as number, job_title as job_title_id, worknode_id as sys_store_id",
            'group' => 'worknode_id, job_title'
        ])->toArray();
    }

    /**
     * 获取已提交的HC总人数（待审批）
     * @param $where
     * @return mixed
     */
    public function getBudgetAdoptNumber($where)
    {
        return HrHcModel::find([
            'conditions' => "state_code = 1 and approval_state_code = 7  and job_title in ({job_title_ids:array}) and worknode_id in ({store_ids:array})  and deleted = 1",
            'bind'       => [
                'job_title_ids' => $where['job_title_ids'],
                'store_ids' => $where['store_ids']
            ],
            'columns' => "sum(demandnumber) as number, job_title as job_title_id, worknode_id as sys_store_id",
            'group' => 'worknode_id, job_title'
        ])->toArray();
    }

    /**
     * @description 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info = SystemExternalApprovalModel::findFirst($auditId);
        if (!empty($info)) {
            $summary = json_decode($info->summary, true);
        } else {
            $summary = json_decode([], true);
        }
        return $summary;
    }

    /**
     * @description 回调接口
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return mixed|void
     * @throws \Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('setProperty  没有找到数据  '.$auditId);
            }
            $SystemExternalApprovalModel->state      = $state;
            $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());
            $SystemExternalApprovalModel->save();

            //向bi同步数据
            $data = [
                'serial_no'              => $SystemExternalApprovalModel->serial_no,
                'status'                 => $state,
                'approval_staff_info_id' => $extend['staff_id'],
            ];
            $ac   = new ApiClient($this->getSvcHost(), '', 'approval.approval_repair_mileage', $this->lang);
            $ac->setParams($data);
            $result = $ac->execute();

            $this->logger->write_log("setProperty result" . json_encode($result), 'info');
        }
        return true;
    }

    /**
     * @description  获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $auditInfo = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return [];
        }
        $parameters = json_decode($auditInfo->approval_parameters, true);
        return $parameters ?? [];
    }

    /**
     * @description 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     * @throws BusinessException
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //获取详情数据
        $result = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($result)) {
            throw new BusinessException('invalid data');
        }
        $result     = $result->toArray();
        $detailInfo = $this->detail($result['serial_no']);

        //组织详情数据
        $detailLists = [
            'apply_parson'       => $detailInfo['apply_parson'],
            'apply_department'   => $detailInfo['apply_department'],
            'mileage_date'       => $detailInfo['mileage_date'],
            'start_kilometres'   => $detailInfo['start_kilometres'],
            'started_img'        => $detailInfo['started_img'],
            'end_kilometres'     => $detailInfo['end_kilometres'],
            'end_img'            => $detailInfo['end_img'],
        ];
        if (isset($detailInfo['sub_mileage'])) {
            $detailLists['total_mileage'] = $detailInfo['sub_mileage'];
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        $auditListRepo = new AuditlistRepository($this->lang, $this->timeZone);
        $add_hour  = $this->config->application->add_hour;
        $data      = [
            'title'       => $auditListRepo->getAudityType(AuditListEnums::APPROVAL_TYPE_VEHICLE),
            'id'          => $result['id'],
            'staff_id'    => $result['submitter_id'],
            'type'        => AuditListEnums::APPROVAL_TYPE_VEHICLE,
            'created_at'  => date('Y-m-d H:i:s', (strtotime($result['created_at']) + $add_hour * 3600)),
            'updated_at'  => date('Y-m-d H:i:s', (strtotime($result['updated_at']) + $add_hour * 3600)),
            'status'      => $result['state'],
            'status_text' => $auditListRepo->getAuditState($result['state']),
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        //当前用户审批操作按钮
        $returnData['data']['head'] = $data;

        return $returnData;
    }

    /**
     * @description 获取详情
     * @param $serial_no
     * @return array
     */
    public function detail($serial_no): array
    {
        $data['serial_no'] = $serial_no;
        $ac                       = new ApiClient($this->getSvcHost(), '', 'approval.get_repair_info', $this->lang);
        $ac->setParams($data);
        $result = $ac->execute();

        $this->logger->write_log("vehicle detail result" . json_encode($result), 'info');

        return $result['result']['data'] ?? [];
    }

    /**
     * @descriptio 创建审批
     * @param array $paramIn
     * @param $userinfo
     * @return array
     */
    public function addVehicleV2($paramIn = [], $userinfo): array
    {
        $paramIn['staff_info_id'] = $userinfo['id'];
        $ac                       = new ApiClient($this->getSvcHost(), '', 'mileage.repair_vehicle', $this->lang);
        $ac->setParams($paramIn);
        $result = $ac->execute();

        $this->logger->write_log("addVehicleS result" . json_encode($result), 'info');

        if (empty($result)) {
            throw new ValidationException($this->getTranslation()->_('please try again'));
        }
        if ($result['result']['code'] != ErrCode::SUCCESS) {
            throw new ValidationException($result['result']['msg']);
        }

        return $this->checkReturn($result['result']['data'] ?? []);
    }

    /**
     * 创建【Van快递员新增“修改里程表数”使用】
     * @param array $paramIn
     * @param $userinfo
     * @return array
     */
    public function addMileageModifySV2($paramIn = [], $userinfo)
    {
        //向bi同步数据
        $data = [
            'staff_info_id'    => $userinfo['id'],
            'mileage_date'     => $paramIn['mileage_date'],
            'start_kilometres' => $paramIn['start_kilometres'],
            'end_kilometres'   => $paramIn['end_kilometres'],
        ];
        $ac   = new ApiClient($this->getSvcHost(), '', 'mileage.modify_vehicle', $this->lang);
        $ac->setParams($data);
        $ac->execute();

        return $this->checkReturn([]);
    }

    /**
     * 里程信息【Van快递员新增“修改里程表数”使用】
     * @param array $paramIn
     * @param array $userinfo
     * @return array
     * @throws ValidationException
     */
    public function getMileageModifySV2($paramIn = [], $userinfo = [])
    {
        $data = [
            'staff_info_id' => $userinfo['id'],
            'mileage_date'  => $paramIn['mileage_date'],
        ];
        $ac   = new ApiClient($this->getSvcHost(), '', 'mileage.get_modify_info', $this->lang);
        $ac->setParams($data);
        $result = $ac->execute();

        $this->logger->write_log("getMileageModifySV2 result" . json_encode($result), 'info');

        if (empty($result)) {
            throw new ValidationException($this->getTranslation()->_('please try again'));
        }
        if ($result['result']['code'] != ErrCode::SUCCESS) {
            throw new ValidationException($result['result']['msg']);
        }

        return $this->checkReturn($result['result']['data'] ?? []);
    }

    /**
     * 修改记录状态记录日志
     * @Return  array
     */
    public function updateVehicleStatusV2($paramIn, $userinfo): array
    {
        //获取请求参数
        $auditId = $this->processingDefault($paramIn, 'id');
        $status = $this->processingDefault($paramIn, 'status');
        $staffId = $this->processingDefault($userinfo, 'id');
        $rejectReason = $this->processingDefault($paramIn, 'reject_reason');

        //获取记录信息
        $result = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('please try again'));
        }
        $result = $result->toArray();

        //当前状态如果已经审批，申请人不可撤销
        if ($result['state'] != enums::APPROVAL_STATUS_PENDING && $status == enums::APPROVAL_STATUS_CANCEL) {
            return $this->checkReturn(-3, $this->getTranslation()->_('2206'));
        }
        //申请人撤销后,审批人不能审批
        if ($result['state'] == enums::APPROVAL_STATUS_CANCEL) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1016'));
        }

        $this->getDI()->get('db')->begin();
        try {
            //同意或者驳回等分开处理
            if ($status == enums::APPROVAL_STATUS_APPROVAL) {

                //同意
                $server = new ApprovalServer($this->lang, $this->timeZone);
                $server->approval($auditId, AuditListEnums::APPROVAL_TYPE_VEHICLE, $staffId, null);
            } else if ($status == enums::APPROVAL_STATUS_REJECTED) {

                //驳回
                $server = new ApprovalServer($this->lang, $this->timeZone);
                $server->reject($auditId, AuditListEnums::APPROVAL_TYPE_VEHICLE, $rejectReason, $staffId);
            } else {
                //撤销
                $server = new ApprovalServer($this->lang, $this->timeZone);
                $server->cancel($auditId, AuditListEnums::APPROVAL_TYPE_VEHICLE, $rejectReason, $staffId);
            }

        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('update fleet failure:' . $e->getMessage(), 'error');
            return $this->checkReturn([]);
        }
        $this->getDI()->get('db')->commit();

        return $this->checkReturn([]);
    }

    /**
     * 同步车辆信息[From 车厢信息]
     *
     * @param $staff_info
     * @param $plate_number
     * @param $license_location
     *
     * @return bool
     * @throws ValidationException
     * @throws BusinessException
     */
    public function saveVehicleInfo($staff_info, $plate_number, $license_location)
    {
        // 员工车辆信息
        $vehicle_model = VehicleInfoModel::findFirst([
            'conditions' => 'uid = :uid: AND deleted = 0',
            'bind'       => ['uid' => $staff_info['staff_info_id']],
        ]);

        $save_vehicle_data = [];
        if (!empty($vehicle_model)) {
            $exist_plate_number = nameSpecialCharsReplace($vehicle_model->plate_number);

            // 待审批 或 已通过, 不可以编辑车牌号 和 上牌地点
            if (in_array($vehicle_model->approval_status,
                [VehicleInfoEnums::APPROVAL_PENDING_CODE, VehicleInfoEnums::APPROVAL_PASSED_CODE])) {
                if ($plate_number != $exist_plate_number) {
                    throw new ValidationException($this->getTranslation()->_('van_container_save_error_004'),
                        ErrCode::VALIDATE_ERROR);
                }

                if ($license_location != $vehicle_model->license_location) {
                    throw new ValidationException($this->getTranslation()->_('van_container_save_error_005'),
                        ErrCode::VALIDATE_ERROR);
                }
            } else {
                if ($plate_number != $exist_plate_number) {
                    $save_vehicle_data['plate_number'] = $plate_number;
                }

                if ($license_location != $vehicle_model->license_location) {
                    $save_vehicle_data['license_location'] = $license_location;
                }
            }
        } else {
            $vehicle_model = new VehicleInfoModel();

            $save_vehicle_data['uid']              = $staff_info['staff_info_id'];
            $save_vehicle_data['approval_status']  = VehicleInfoEnums::APPROVAL_UN_SUBMITTED_CODE;
            $save_vehicle_data['plate_number']     = $plate_number;
            $save_vehicle_data['license_location'] = $license_location;
            $save_vehicle_data['creator_id']       = $staff_info['staff_info_id'];
            $save_vehicle_data['editor_id']        = $staff_info['staff_info_id'];
            $save_vehicle_data['formal_data']      = '';

            if (in_array($staff_info['job_title'], VehicleInfoEnums::VAN_JOB_GROUP_ITEM)) {
                $save_vehicle_data['vehicle_type'] = VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE;
            }
        }

        $before_vehicle_info = [
            'plate_number'     => $vehicle_model->plate_number ?? '',
            'license_location' => $vehicle_model->license_location ?? '',
        ];

        $this->logger->write_log([
            'van_container_sync_vehicle_info' => $save_vehicle_data,
            'before_vehicle_info'             => $before_vehicle_info,
        ], 'info');

        if (!empty($save_vehicle_data)) {
            if ($this->checkPlateNumberIsExist($plate_number, $staff_info['staff_info_id'], $license_location)) {
                throw new ValidationException($this->getTranslation()->_('vehicle_info_0001'), ErrCode::VALIDATE_ERROR);
            }

            if ($vehicle_model->save($save_vehicle_data) === false) {
                throw new BusinessException("车厢信息提交->车辆信息同步失败,uid={$staff_info['staff_info_id']}",
                    ErrCode::SYSTEM_ERROR);
            }
        }

        return true;
    }

    public function getLicensePlateLocation()
    {
        //上牌地点可选项
        $ac = new ApiClient('hcm_rpc', '', 'license_plate_location', $this->lang);
        $ac->setParams([]);
        $ac_result = $ac->execute();
        $returnData['license_plate_location'] = [];
        if (!empty($ac_result) && !empty($ac_result['result']['data'])) {
            return $ac_result['result']['data'];
        }
        return [];
    }
}
