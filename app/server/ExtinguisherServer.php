<?php
/**
 * Author: Bruce
 * Date  : 2021-11-17 23:35
 * Description:
 */

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\ExtinguisherOperationLogModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\ExtinguisherRepository;

class ExtinguisherServer extends BaseServer
{
    protected $re;
    public $timezone;
    public $add_hour;
    public $checkLimit;
    public $currentTime;
    protected $extinguisherRepository;
    private $db;

    const TASK_CHECKED_STATUS = 1;//灭火器任务,未检查
    const TASK_NOT_CHECKED_STATUS = 2;//灭火器任务,已检查
    const TASK_CHECKED_QUALIFIED = 1;//灭火器任务,检查合格
    const TASK_CHECKED_NOT_QUALIFIED = 2;//灭火器任务,检查不合格
    const EXTINGUISHER_MIN_WEIGHT = 12;//灭火器重量下限
    const EXTINGUISHER_MAX_WEIGHT = 23;//灭火器重量上限

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone  = $timezone;
        $this->extinguisherRepository = new ExtinguisherRepository($timezone);
        $this->add_hour = $this->config->application->add_hour;
        $this->checkLimit = gmdate("Y-m-07 23:59:59", time() + $this->add_hour * 3600);
        $this->currentTime = gmdate("Y-m-d H:i:s", time() + $this->add_hour * 3600);
        $this->db = $this->getDI()->get("db");
    }

    /**
     * 计算红点--未检查任务数量
     * @param $params
     * @return array
     */
    public function getPubAssetsList($params)
    {
            $where['staff_id'] = $params['userinfo']['staff_id'];
            $where['check_month'] = gmdate("Y-m", time() + $this->add_hour * 3600);
            $notCheckTaskList = $this->extinguisherRepository->getNotCheckTaskInfo($where);
            $exist = $this->extinguisherRepository->getExtinguisherTaskStaffRelationExist($where);
            return ['extinguisher_check' => $exist, 'number' => count($notCheckTaskList)];

    }

    /**
     * 获取检查任务列表
     * @param $params
     * @return array
     */
    public function checkTaskListInfo($params)
    {
        $isSearch = true;
        //判断是否是网点工作人员
        if($params['userinfo']['organization_type'] == 1) {//网点id
            //$where['store_id'] = $params['userinfo']['organization_id'];
            $isSearch = false;//网点工作人员不显示灭火器编码搜索框
        }

        $where['staff_id'] = $params['userinfo']['staff_id'];
        //按灭火器编码查询
        if(!empty($params['extinguisher_code'])){
            $where['extinguisher_code'] = trim($params['extinguisher_code']);
        }
        $where['check_month'] = gmdate("Y-m", time() + $this->add_hour * 3600);
        $checkTaskList = $this->extinguisherRepository->getCheckTaskListInfo($where);
        if(!$checkTaskList) {
            return ['is_search' => $isSearch, 'list' => []];
        }
        foreach ($checkTaskList as $key => $oneTask){
            //当前时间小于每月7日23:59:59,未检查的任务。
            if($this->currentTime <= $this->checkLimit) {
                $checkTaskList[$key]['is_ash_setting'] = false;
                //当前时间且任务创建时间皆大于每月7日23:59:59,未检查任务的不过期。
            } elseif($this->currentTime > $this->checkLimit) {
                $checkTaskList[$key]['is_ash_setting'] = true;
                if($oneTask['created_at'] > $this->checkLimit){
                    $checkTaskList[$key]['is_ash_setting'] = false;
                }
            }
        }
        return ['is_search' => $isSearch, 'list' => $checkTaskList];
    }

    /**
     * 查看灭火器详情
     * @param $params
     * @return array
     */
    public function getExtinguisherInfoDetail($params)
    {
        $where['check_month'] = gmdate("Y-m", time() + $this->add_hour * 3600);
        $where['extinguisher_info_id'] = intval($params['extinguisher_info_id']);
        $extinguisherInfo = $this->extinguisherRepository->getExtinguisherInfo($where);
        if(!$extinguisherInfo){
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        //获取网点信息
        if ($extinguisherInfo['store_id'] != Enums::HEAD_OFFICE_ID) {
            $builder = $this->modelsManager->createBuilder();
            //查询系统网点信息
            $storeInfo = $builder->columns('name')
                ->from(SysStoreModel::class)
                ->where('id = :store_id:', ['store_id' => $extinguisherInfo['store_id']])
                ->getQuery()->getSingleResult();
        }else{
            $storeInfo['name'] = Enums::HEAD_OFFICE;
        }
        $extinguisherInfo['extinguisher_type_name'] = $this->getTranslation()->_(enums::$extinguisher_type[$extinguisherInfo['type']]);
        $extinguisherInfo['store_name'] = $storeInfo['name'];
        $extinguisherInfo['gps_position'] = $extinguisherInfo['longitude'] . ';' . $extinguisherInfo['latitude'];

        return $this->checkReturn(['data'=> $extinguisherInfo]);
    }

    /**
     * 任务详情
     * @param $params
     * @return mixed
     */
    public function getInspectionItemsInfo($params)
    {
        $where['task_id'] = intval($params['task_id']);
        $taskInfo = $this->extinguisherRepository->getTaskInfoById($where);
        $taskFileInfo = $this->extinguisherRepository->getTaskFileInfo($where);
        $taskCheckListInfo = $this->extinguisherRepository->getTaskCheckList($where);
        $baseInfo['extinguisher_id'] = $taskInfo['extinguisher_info_id'];
        $extinguisherBaseInfo = $this->extinguisherRepository->getExtinguisherInfoById($baseInfo);

        $img_prefix = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
        foreach ($taskFileInfo as $key => $oneFile) {
            $oneFile['pic_url']  = $img_prefix . $oneFile['object_key'];
            $taskFileInfo[$key] = $oneFile;
            if($oneFile['type']  == 2) {
                $taskInfo['sign_info']  = $oneFile;
                unset($taskFileInfo[$key]);
            }
        }
        $taskInfo['photo_url_list']  = $taskFileInfo;

        if(!$taskCheckListInfo) {
            foreach (enums::$extinguisher_check_list as $key => $item) {
                $oneData['problem_name'] = $this->getTranslation()->_($item);
                $oneData['problem_key'] = $item;
                $oneData['type'] = '1';
                if($key == 7){//第八个问题，为输入型。
                    $oneData['type'] = '2';
                }
                $oneData['content'] = '';
                $taskCheckListInfo[] = $oneData;
            }
        } else {
            foreach ($taskCheckListInfo as $oneKey => $oneProblem) {
                $taskCheckListInfo[$oneKey]['problem_name'] = $this->getTranslation()->_($oneProblem['problem_key']);
            }
        }
        $taskInfo['item_list']  = $taskCheckListInfo;
        $taskInfo['extinguisher_type']  = $extinguisherBaseInfo['type'];
        return $taskInfo;
    }

    /**
     * 提交检查任务
     * @param $params
     * @return array
     */
    public function storeCheckResult($params)
    {
        //检查结果
        $taskInfo['check_result'] = self::TASK_CHECKED_QUALIFIED;
        $insertItems = [];
        foreach ($params['items'] as $oneKey => $oneItems) {
            if($oneItems['content'] == self::TASK_CHECKED_NOT_QUALIFIED){
                $taskInfo['check_result'] = self::TASK_CHECKED_NOT_QUALIFIED;
            }
            //第8个问题，值[12,23];
            if($oneKey == 7 && $params['extinguisher_type'] == 1){//灭火器类型：二氧化碳
                if(empty($oneItems['content'])){
                    return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
                }
                if(!($oneItems['content'] >= self::EXTINGUISHER_MIN_WEIGHT && $oneItems['content'] <= self::EXTINGUISHER_MAX_WEIGHT)){
                    $taskInfo['check_result'] = self::TASK_CHECKED_NOT_QUALIFIED;
                }
            }
            $insertOneItems['problem_key'] = $oneItems['problem_key'];
            $insertOneItems['content'] = $oneItems['content'];
            $insertOneItems['type'] = $oneItems['type'];
            $insertOneItems['extinguisher_task_id'] = $params['task_id'];
            $insertItems[] = $insertOneItems;
        }
        //检查照片
        $insertPhotoInfo = [];
        foreach ($params['photo_url_list'] as $photoKey => $onePhoto) {
            $oneInsertPhoto['bucket_name'] = $onePhoto['bucket_name'];
            $oneInsertPhoto['object_key'] = $onePhoto['object_key'];
            $oneInsertPhoto['file_name'] = $onePhoto['file_name'];
            $oneInsertPhoto['type'] = 1;
            $oneInsertPhoto['extinguisher_task_id'] = $params['task_id'];
            $insertPhotoInfo [] = $oneInsertPhoto;
        }
        //签名信息
        $signInfo['bucket_name'] = $params['sign_info']['bucket_name'];
        $signInfo['object_key'] = $params['sign_info']['object_key'];
        $signInfo['file_name'] = $params['sign_info']['file_name'];
        $signInfo['type'] = 2;
        $signInfo['extinguisher_task_id'] = $params['task_id'];
        $insertPhotoInfo[] = $signInfo;
        try {
            $this->db->begin();
            $where['task_id'] = $params['task_id'];
            $taskInfoOne = $this->extinguisherRepository->getTaskInfoById($where);

            //查询文件信息是否存在，如果是则删除
            $taskFileInfo = $this->extinguisherRepository->getTaskFileInfoByTaskId($where);
            if($taskFileInfo) {
                $this->db->updateAsDict("extinguisher_task_file", ['deleted' => 1], ['conditions' => "extinguisher_task_id=?", 'bind' => [$where['task_id']]]);
            }

            //查询检查问题是否存在，如果是则删除
            $taskCheckList = $this->extinguisherRepository->getTaskCheckList($where);
            if($taskCheckList) {
                $this->db->updateAsDict("extinguisher_check_list", ['deleted' => 1], ['conditions' => "extinguisher_task_id=?", 'bind' => [$where['task_id']]]);
            }

            //查询管理人员是否存在，如果是，则删除
            $taskStaffRelation = $this->extinguisherRepository->getExtinguisherTaskStaffRelation($where);
            if($taskStaffRelation) {
                $this->db->updateAsDict("extinguisher_task_staff_relation", ['deleted' => 1], ['conditions' => "extinguisher_task_id=?", 'bind' => [$where['task_id']]]);
            }

            //插入检查单信息
            $this->extinguisherRepository->batch_insert('extinguisher_check_list', $insertItems);
            //插图片文件，签名文件信息
            $this->extinguisherRepository->batch_insert('extinguisher_task_file', $insertPhotoInfo);
            //插入管理人员与任务关联信息
            $staffWhere['extinguisher_id'] = $taskInfoOne['extinguisher_info_id'];
            $staffRelation = $this->extinguisherRepository->getExtinguisherStaffRelation($staffWhere);
            if($staffRelation) {
                foreach ($staffRelation as $relationKey => $oneStaff){
                    $staffRelation[$relationKey]['extinguisher_task_id'] = $params['task_id'];
                }
                $this->extinguisherRepository->batch_insert('extinguisher_task_staff_relation', $staffRelation);
            }

            //获取最新灭火器信息，同步到灭火器任务中存档
            $extinguisherInfo = $this->extinguisherRepository->getExtinguisherInfoById($staffWhere);
            $extinguisherBaseJson['asset_code'] = $extinguisherInfo['asset_code'];
            $extinguisherBaseJson['longitude']  = $extinguisherInfo['longitude'];
            $extinguisherBaseJson['latitude']   = $extinguisherInfo['latitude'];
            $extinguisherBaseJson['coordinate'] = $extinguisherInfo['coordinate'];
            $extinguisherBaseJson['weight']     = $extinguisherInfo['weight'];
            $extinguisherBaseJson['photo_url']  = $extinguisherInfo['photo_url'];
            $extinguisherBaseJson['operator_id']= $extinguisherInfo['operator_id'];
            $extinguisherBaseJson['deleted']    = $extinguisherInfo['deleted'];

            $taskInfo['extinguisher_info_id']  = $extinguisherInfo['id'];
            $taskInfo['store_id']              = $extinguisherInfo['store_id'];
            $taskInfo['extinguisher_code']     = $extinguisherInfo['extinguisher_code'];
            $taskInfo['type']                  = $extinguisherInfo['type'];
            $taskInfo['extinguisher_base_json']= json_encode($extinguisherBaseJson, JSON_UNESCAPED_UNICODE);
            $taskInfo['check_time']            = gmdate("Y-m-d H:i:s", time() + $this->add_hour * 3600);
            $taskInfo['status'] = self::TASK_NOT_CHECKED_STATUS;
            $taskInfo['remark'] = trim($params['remark']);
            $taskInfo['operator_id'] = $params['staff_id'];
            //更新任务信息
            $this->getDI()->get('db')->updateAsDict(
                'extinguisher_task',
                $taskInfo,
                'id = '. $where['task_id']
            );

            //记录操作内容
            $content['task_id'] = $params['task_id'];
            $content['task_info'] = $taskInfo;
            $content['items'] = $insertItems;
            $content['photo_url_list'] = $insertPhotoInfo;
            $content['staff_relation'] = $staffRelation;
            //插入操作日志信息
            $operationLogModel = new ExtinguisherOperationLogModel();
            $operationLogModel->setExtinguisherId($params['task_id']);
            $operationLogModel->setContent('');
            $operationLogModel->setType(2);
            $operationLogModel->setOperatorId($params['staff_id']);
            $operationLogModel->save();
            $this->db->commit();
            $this->getDI()->get("logger")->write_log("storeCheckInfo_operator_save_data: " . json_encode($content, JSON_UNESCAPED_UNICODE), "info");
            return $this->checkReturn(['data'=> []]);
        }catch (\Exception $e) {
            $this->db->rollback();
            $this->getDI()->get('logger')->write_log('Extinguisher:storeCheckResult'. $e->getMessage() . $e->getTraceAsString());
            return $this->checkReturn(-3, $e->getMessage());
        }
    }
}
