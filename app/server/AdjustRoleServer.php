<?php

namespace FlashExpress\bi\App\Server;



use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\SysStoreCateEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\StaffAdjustRoleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\SysStoreTypeModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

class AdjustRoleServer extends AuditBaseServer
{
    public $timezone;
    public $lang;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
        $this->lang     = $lang;
    }

    /**
     * 提出申请
     * @param array $paramIn
     * @return array
     * @throws ValidationException
     */
    public function create(array $paramIn = []): array
    {
        //1 获取参数
        $submitterId = $this->processingDefault($paramIn, 'staff_id');
        $AdjustStaffId = $this->processingDefault($paramIn, 'staff_info_id');
        $roleIds = $this->processingDefault($paramIn, 'role_ids');
        $reason = $this->processingDefault($paramIn, 'reason');
        $reason = addcslashes(stripslashes($reason),"'");

        //2 获取调整角色人详情
        $staffInfo = (new StaffRepository())->getStaffInfo($AdjustStaffId);

        //校验 如果存在待审批的申请则不能申请
        $auditInfo = StaffAdjustRoleModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and state = 1",
            'bind' => [
                'staff_info_id'  => $AdjustStaffId,
            ]
        ]);
        if ($auditInfo instanceof StaffAdjustRoleModel) {
            throw new ValidationException($this->getTranslation()->_('adjust_role_err_msg_request_exist'));
        }

        //校验前后角色是否一致
        $rolesArr = isset($staffInfo['position_category']) && $staffInfo['position_category']
            ? explode(',', $staffInfo['position_category'])
            : [];
        $intersect = array_intersect($rolesArr, $roleIds);

        if (count($intersect) > 0 && (empty(array_diff($rolesArr, $roleIds) || array_diff($roleIds, $rolesArr)))) {
            throw new ValidationException($this->getTranslation()->_('adjust_role_err_msg_no_change'));
        }

        //校验是否存在出纳角色
        if (in_array(enums::$roles['BRANCH_CASHIER'], $roleIds)) {
            $this->checkBranchCashier($staffInfo['store_id']);
        }

        //[3]组织数据插入业务主数据
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $insertData = [
                'serial_no'         => $this->getRandomId(),
                'submitter_id'      => $submitterId,
                'staff_info_Id'     => $AdjustStaffId,
                'department_id'     => $staffInfo['department_id'],
                'job_title_id'      => $staffInfo['job_title'],
                'store_id'          => $staffInfo['store_id'],
                'current_role_ids'  => $staffInfo['position_category'],
                'adjust_role_ids'   => isset($roleIds) && $roleIds ? implode(',', $roleIds) : "",
                'reason'            => $reason,
                'state'             => enums::APPROVAL_STATUS_PENDING,
            ];
            $db->insertAsDict(
                'staff_adjust_role', $insertData
            );
            $auditId = $db->lastInsertId();
            if (empty($auditId)) {
                throw new \Exception($this->getTranslation()->_('4008'));
            }

            //创建
            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($auditId, AuditListEnums::APPROVAL_TYPE_ADJUST_ROLE, $submitterId,null, [
                'Array_department_id' => [$staffInfo['department_id']],
                'DM_store_ids' => [$staffInfo['store_id']],
                'AM_store_ids' => [$staffInfo['store_id']],
            ]);
            if (!$requestId) {
                throw new \Exception('创建审批流失败');
            }
            $db->commit();
        } catch (\Exception $e){
            $db->rollback();
            $this->logger->write_log('添加角色申请失败:' . $e->getMessage() . $e->getTraceAsString(), 'info');
            return $this->checkReturn(-3, $e->getMessage());
        }

        return $this->checkReturn([]);
    }

    /**
     * 审批
     * @param array $paramIn
     * @return array
     * @throws ValidationException
     */
    public function audit(array $paramIn = []): array
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $auditId = $this->processingDefault($paramIn, 'audit_id', 2);
        $status  = $this->processingDefault($paramIn, 'status', 2);
        $reason  = $this->processingDefault($paramIn, 'reject_reason', 1);
        $reason  = addcslashes(stripslashes($reason),"'");

        //[2]获取详情
        $auditInfo = StaffAdjustRoleModel::findFirst($auditId);
        if ($auditInfo instanceof StaffAdjustRoleModel) {
            $auditInfo = $auditInfo->toArray();
        } else {
            throw new ValidationException($this->getTranslation()->_('4008'));
        }

        //[3]验证
        if ($auditInfo['state'] != enums::$audit_status['panding']) { //已经是最终状态,不能修改
            throw new ValidationException($this->getTranslation()->_('4008'));
        }

        $this->getDI()->get('db')->begin();

        try {
            //同意或者驳回等分开处理
            if ($status == enums::$audit_status['approved']) {

                //同意
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->approval($auditId, AuditListEnums::APPROVAL_TYPE_ADJUST_ROLE, $staffId, null, null);
            } else if ($status == enums::$audit_status['dismissed']) {

                //驳回
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->reject($auditId, AuditListEnums::APPROVAL_TYPE_ADJUST_ROLE, $reason, $staffId);
            } else {

                //撤销
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->cancel($auditId, AuditListEnums::APPROVAL_TYPE_ADJUST_ROLE, $reason, $staffId);
            }

        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('update adjust role failure:' . $e->getMessage(), 'error');
            return $this->checkReturn(-3, $e->getMessage());
        }
        $this->getDI()->get('db')->commit();

        return $this->checkReturn([]);
    }

    /**
     * 获取员工信息 & 获取角色下拉列表
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function getStaffInfoAndRolesList($paramIn): array
    {
        //1. 获取员工ID
        $staffInfoId = $this->processingDefault($paramIn, 'staff_info_id');
        $submitterId = $this->processingDefault($paramIn, 'staff_id');

        //2.1 校验调整角色员工是否在职
        $staffInfo = (new StaffRepository())->getStaffInfo($staffInfoId);
        if ($staffInfo['state'] != enums::$service_status['incumbency'] || $staffInfo['store_id'] == -1) {
            throw new ValidationException($this->getTranslation()->_('adjust_role_err_msg_check_state'));
        }

        //2.2 校验调整角色员工是否为申请人管理大区下的下辖的员工
        $management = (new StaffServer())->getManagement($staffInfoId, $submitterId);
        if (empty($management)) {
            throw new ValidationException($this->getTranslation()->_('adjust_role_err_msg_no_permission'));
        }

        //2.3 校验调整角色员工是否存在待申请
        $auditInfo = StaffAdjustRoleModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and state = 1",
            'bind'       => [
                'staff_info_id' => $staffInfoId,
            ],
        ]);
        if ($auditInfo instanceof StaffAdjustRoleModel) {
            throw new ValidationException($this->getTranslation()->_('adjust_role_err_msg_request_exist'));
        }

        //3 获取调整角色员工当前角色
        $rolesList = $this->getAllRoleList();
        $staffRoles = isset($staffInfo['position_category']) && $staffInfo['position_category']
            ? explode(',', $staffInfo['position_category'])
            : [];

        $staffRolesArr = array_map(function ($role) use($rolesList) {
            return [
                'code' => $role,
                'role_name' => $rolesList[$role] ?? ''
            ];
        }, $staffRoles);

        $staffRoleNamesArr = array_map(function ($role) use($rolesList) {
            return $rolesList[$role] ?? "";
        }, $staffRoles);
        $staffInfo['position_category_name'] = isset($staffRoleNamesArr) && $staffRoleNamesArr ? implode(',', $staffRoleNamesArr): "";

        //4 获取可选角色列表
        $relateRoles = $this->getRelateRole($staffInfo['department_id'], $staffInfo['job_title']);
        if (!empty($relateRoles)) {
            $relateRolesArr = array_column($relateRoles, 'role_id');
            $intersect = array_intersect($relateRolesArr, [
                enums::$roles['DISPATCHER'],
                enums::$roles['COURIER'],
                enums::$roles['STORE_KEEPER'],
                enums::$roles['BRANCH_CASHIER'],
                enums::$roles['FLEXIBLE_COURIER'],
            ]);
            $intersect = array_unique(array_merge($intersect, $staffRoles));

            $intersectArr = array_map(function ($role) use($rolesList) {
                return [
                    'code' => $role,
                    'role_name' => $rolesList[$role] ?? ''
                ];
            }, $intersect);
        }

        return [
            'staff_info' => $staffInfo,
            'current_roles' => array_values($staffRolesArr),
            'valid_roles' => isset($intersectArr) && $intersectArr ? array_values($intersectArr) : [],
        ];
    }

    /**
     * 获取全部角色列表
     * @return array
     */
    public function getAllRoleList(): array
    {
        $ret = new ApiClient('hr_rpc', '', 'role_list', $this->lang);
        $ret->setParams([]);
        $return = $ret->execute();

        $lang = substr($this->lang , 0 , 2);
        if ($roleList = array_column($return['result'], 'role_name_' . $lang, 'role_id')) {
            return $roleList;
        } else {
            return array_column($return['result'], 'role_name_en', 'role_id');
        }
    }

    /**
     * 获取关联角色
     * @param $department_id
     * @param $job_title_id
     * @return bool|mixed|null
     */
    public function getRelateRole($department_id, $job_title_id)
    {
        $ac = new ApiClient('hr_rpc', '', 'department_job_title_role', $this->lang);
        $ac->setParams(
            [
                "department_id" => $department_id,
                "job_title_id"  => $job_title_id,
            ]
        );
        $return = $ac->execute();
        return $return['result'] ?? [];
    }

    /**
     * 更改申请人角色
     * @param $staff_info_id
     * @param $submitter_id
     * @param $roles
     * @return array
     */
    public function setStaffRoles($staff_info_id, $submitter_id, $roles): array
    {
        //提交角色
        $ac = new ApiClient('hr_rpc', '', 'staff_change_roles', $this->lang);
        $ac->setParams(
            [
                "staff_info_id" => $staff_info_id,
                "roles"         => explode(',', $roles),
                "operater_id"   => $submitter_id,
            ]
        );
        $return = $ac->execute();
        $this->logger->write_log('staff_change_roles result:' . json_encode($return), 'info');

        return $return['result'] ?? [];
    }
    /**
     * 详情页面
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //获取详情
        $result  = StaffAdjustRoleModel::findFirst($auditId);
        if (empty($result)) {
            return false;
        }
        $result = $result instanceof StaffAdjustRoleModel ? $result->toArray() : [];

        //申请人信息
        $submitterInfo = (new StaffServer())->get_staff($result['submitter_id']);
        if($submitterInfo['data']){
            $submitterInfo = $submitterInfo['data'];
        }

        $staffInfo = (new StaffServer())->get_staff($result['staff_info_id']);
        if($staffInfo['data']){
            $staffInfo = $staffInfo['data'];
        }

        //获取调整角色员工角色
        $rolesList = $this->getAllRoleList();
        $currentRolesArr = isset($result['current_role_ids']) && $result['current_role_ids']
            ? explode(',', $result['current_role_ids'])
            : [];

        $currentRoles = array_map(function ($role) use($rolesList) {
            return $rolesList[$role] ?? '';
        }, $currentRolesArr);

        $adjustRolesArr = isset($result['adjust_role_ids']) && $result['adjust_role_ids']
            ? explode(',', $result['adjust_role_ids'])
            : [];

        $adjustRoles = array_map(function ($role) use($rolesList) {
            return $rolesList[$role] ?? '';
        }, $adjustRolesArr);

        //组织详情数据
        $detailLists = [
            'apply_parson'              => sprintf('%s ( %s )',$submitterInfo['name'] ?? '' , $submitterInfo['id'] ?? ''),
            'apply_department'          => sprintf('%s - %s',$submitterInfo['depart_name'] ?? '' , $submitterInfo['job_name'] ?? ''),
            'adjust_role_staff'         => sprintf('%s ( %s )',$staffInfo['name'] ?? '' , $staffInfo['id'] ?? ''),
            'adjust_role_staff_dept'    => $staffInfo['depart_name'] ?? '',
            'adjust_role_staff_job'     => $staffInfo['position_name'] ?? '',
            'adjust_role_staff_store'   => $staffInfo['store_name'] ?? '',
            'adjust_role_now_roles'     => implode(',', $currentRoles) ?? '',
            'adjust_role_after_roles'   => implode(',', $adjustRoles) ?? '',
            'reason_app'                => $result['reason'] ?? '',
        ];

        if ($result['state'] == enums::$audit_status['dismissed']) { //已经驳回，需要显示驳回原因
            $detailLists = array_merge($detailLists, [
                'reject_reason1' => $result['reason'] ?? '',
            ]);
        }
        $returnData['data']['detail'] = $this->format($detailLists);
        $auditRepo = new AuditlistRepository($this->lang, $this->timezone);

        $data = [
            'title'       => $auditRepo->getAudityType(AuditListEnums::APPROVAL_TYPE_ADJUST_ROLE),
            'id'          => $result['id'],
            'staff_id'    => $result['submitter_id'],
            'type'        => AuditListEnums::APPROVAL_TYPE_ADJUST_ROLE,
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['state'],
            'status_text' => $auditRepo->getAuditStatus('10' . $result['state']),
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        $returnData['data']['head'] = $data;
        $returnData['data']['extend'] = $result['extend'] ?? []; //时刻表

        return $returnData;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(true,
            false,
            false,
            false,
            false,
            false);
    }

    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        //获取角色调整人信息
        $request = StaffAdjustRoleModel::findFirst($auditId);
        if (empty($request)) {
            return [];
        }
        $staffInfo = (new StaffRepository())->getStaffPositionv3($request->staff_info_id);

        return [
            [
                'key'   => 'adjust_role_staff',
                'value' => sprintf("%s (%s)", $staffInfo['id'], $staffInfo['name'])
            ],[
                'key'   => 'adjust_role_store',
                'value' => $staffInfo['store_name'] ?? ''
            ]
        ];

    }

    /**
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     * @throws \Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $this->getDI()->get('db')->updateAsDict(
                'staff_adjust_role',
                ['state' => $state],
                'id = '. $auditId
            );

            $auditInfo = StaffAdjustRoleModel::findFirst($auditId);
            if (empty($auditInfo)) {
                $this->logger->write_log('adjust role error: data not exist ,audit id:' . $auditId);
                throw new \Exception('4008');
            }
            $auditInfoArr = $auditInfo->toArray();

            //提交角色
            $return = $this->setStaffRoles($auditInfoArr['staff_info_id'], $auditInfoArr['submitter_id'], $auditInfoArr['adjust_role_ids']);
            if ($return['code'] != ErrCode::SUCCESS) {
                //获取收信人语言环境
                $lang = (new StaffServer)->getLanguage($auditInfoArr['submitter_id']);

                //获取语言
                $t = $this->getTranslation($lang);

                //获取信息
                $staffInfo = (new StaffServer())->get_staff($auditInfoArr['staff_info_id']);
                if($staffInfo['data']){
                    $staffInfo = $staffInfo['data'];
                }

                //文案模板
                //您好，您为{工号}-{姓名}-{部门}-{职位}-{网点}申请角色调整失败，失败原因为：{失败原因}
                $content = $t->_('adjust_role_falure_message');
                $content = str_replace([
                    "{staff_info}",
                    "{staff_name}",
                    "{department}",
                    "{job_title}",
                    "{store}",
                    "{failire_reason}",
                ], [
                    $auditInfoArr['staff_info_id'],
                    $staffInfo['name'],
                    $staffInfo['depart_name'],
                    $staffInfo['job_name'],
                    $staffInfo['store_name'],
                    $return['msg'] ?? ''
                ], $content);

                $param = [
                    'staff_info_id' => $auditInfoArr['submitter_id'],
                    'message_title' => $t->_('adjust_role_falure_title'),
                    'message_content'=> $content,
                    'type'          => 11
                ];
                (new PublicRepository())->pushAndSendMessageToSubmitter($param);
            }
        }
    }

    /**
     * @param $auditId
     * @param $user
     * @param null $state
     * @return array
     */
    public function getWorkflowParams($auditId, $user, $state = null): array
    {
        $auditInfo = StaffAdjustRoleModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return [];
        }
        $auditInfoArr = $auditInfo instanceof StaffAdjustRoleModel ? $auditInfo->toArray() : [];

        //获取角色申请人信息
        $staffInfo = (new StaffRepository())->getStaffInfo($auditInfoArr['staff_info_id']);

        //获取管辖
        $managerment = (new StaffServer())->getManagement($auditInfoArr['staff_info_id'], $auditInfoArr['submitter_id']);

        return [
            'category' => $staffInfo['category'],
            'is_am' => isset($managerment['r_manager_id']) && $managerment['r_manager_id'] == $auditInfoArr['submitter_id'],
            'is_dm' => isset($managerment['p_manager_id']) && $managerment['p_manager_id'] == $auditInfoArr['submitter_id'],
            'is_store_manager' => isset($managerment['s_manager_id']) && $managerment['s_manager_id'] == $auditInfoArr['submitter_id'],
        ];
    }

    /**
     * 检验指定网点在职的出纳数量
     * @param string $store_id
     * @return bool
     * @throws ValidationException
     */
    public function checkBranchCashier(string $store_id): bool
    {
        //获取指定网点的在职的仓管角色
        $onJobInfo = $this->getStoreCashierNo($store_id);

        //获取待审批的申请
        $requestInfo = StaffAdjustRoleModel::find([
            'conditions' => "store_id = :store_id: and FIND_IN_SET(:roles:,adjust_role_ids) and state = 1",
            'bind'       => [
                'store_id' => $store_id,
                'roles' => '4',
            ],
            'columns' => 'staff_info_id'
        ])->toArray();
        $requestInfo = array_column($requestInfo, 'staff_info_id');

        //在职+申请中的出纳的总数
        $total = array_merge($onJobInfo, $requestInfo);

        //计算
        //a.SHOP 网点，判断在职“网点出纳”最多：2名
        //b.DC/SP/BDC/PDC网点，判断在职“网点出纳”最多：4名
        //c.其他网点，判断不变，在职“网点出纳”最多：1名
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => "id = :store_id:",
            'bind'       => [
                'store_id' => $store_id,
            ],
        ]);
        if ($storeInfo instanceof SysStoreModel) {
            $storeInfo = $storeInfo->toArray();
        }
        $specifiedRoleLimitCnt = $this->getStoreCashierLimitNo($storeInfo['category']);

        $this->logger->write_log(sprintf("checkBranchCashier::on job count: %s, branch cashier count: %s, total cashier count: %s", count($onJobInfo), count($requestInfo), $specifiedRoleLimitCnt), "info");
        if (($specifiedRoleLimitCnt - count($total)) <= 0) {
            $errMsg = str_replace('{count}', $specifiedRoleLimitCnt, $this->getTranslation()->_('adjust_role_err_msg_over'));
            throw new ValidationException($errMsg);
        }
        return true;
    }

    /**
     * 获取出纳角色限制
     * @param $category
     * @return int
     */
    public function getStoreCashierLimitNo($category): int
    {
        switch ($category) {
            case enums::$stores_category['sp']:
            case enums::$stores_category['dc']:
            case enums::$stores_category['bdc']:
            case enums::$stores_category['cdc']:
            case enums::$stores_category['pdc']:
                $count = 4;
                break;
            case enums::$stores_category['shop_pickup_only']:
            case enums::$stores_category['shop_pickup_delivery']:
            case enums::$stores_category['shop_ushop']:
                $count = 2;
                break;
            default:
                $count = 1;
                break;
        }
        return $count;
    }

    /**
     * 获取指定网点的在职的仓管角色
     * @param string $store_id
     * @return array
     */
    public function getStoreCashierNo(string $store_id): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->join(SysStoreModel::class, 'hsi.sys_store_id = ss.id', 'ss');
        $builder->join(HrStaffInfoPositionModel::class, 'hsip.staff_info_id = hsi.staff_info_id', 'hsip');
        $builder->andWhere('hsi.sys_store_id = :sys_store_id:', ['sys_store_id' => $store_id]);
        $builder->andWhere('hsip.position_category = :position_id:',
            ['position_id' => enums::$roles['BRANCH_CASHIER']]);
        $builder->andWhere('hsi.state = 1 and hsi.formal = 1');
        $onJobInfo = $builder->getQuery()->execute()->toArray();
        return array_column($onJobInfo, 'staff_info_id');
    }
}