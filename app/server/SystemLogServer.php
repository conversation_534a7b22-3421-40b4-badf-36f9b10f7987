<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrSystemScreenCaptureLogModel;
use Exception;
use FlashExpress\bi\App\Models\backyard\OperationLogModel;

class SystemLogServer extends BaseServer
{
    /**
     * 屏幕捕捉信息保存
     * @param $parmas
     */
    public function screenCaptureLogSave($parmas, $userInfo)
    {
        $screenCapture = new HrSystemScreenCaptureLogModel();

        $screenCapture->staff_info_id   = $userInfo['staff_id'];
        $screenCapture->equipment       = $parmas['equipment'] ?? '';
        $screenCapture->operation_type  = $parmas['operation_type'] ?? 0;
        $screenCapture->operation_time  = !empty($parmas['operation_time']) ? gmdate("Y-m-d H:i:s", substr($parmas['operation_time'], 0, 10)) : null;
        $screenCapture->page_type       = $parmas['page_type'] ?? 0;
        $screenCapture->page_path       = $parmas['page_path'] ?? '';
        $screenCapture->page_image_path = $parmas['page_image_path'] ?? '';
        $screenCapture->remark          = $parmas['remark'] ?? '';

        //保存订单数据
        if (!$screenCapture->save()) {
            throw new Exception('screenCaptureLogSave Error!');
        }

        return $this->checkReturn(1);
    }

    /**
     * by-记录操作日志-埋点
     * @param $parmas
     * @return bool
     * @throws Exception
     */
    public function operationLogAdd($paramIn): bool
    {
        try {
            $log                = new OperationLogModel();
            $log->operator_id   = $paramIn['userinfo']['staff_id'];
            $log->business_type = $paramIn['business_type'] ?? 0;

            if ($paramIn['business_type'] == OperationLogModel::BUSINESS_TYPE && !empty($paramIn['business_data']['entry_id'])) {
                $entryInfo                         = HrEntryModel::findFirst([
                    'conditions' => 'entry_id = :entry_id:',
                    'bind'       => ['entry_id' => $paramIn['business_data']['entry_id']],
                ]);
                $paramIn['business_data']['hc_id'] = !empty($entryInfo) ? $entryInfo->hc_id : '';
                if (!empty($paramIn['business_data']['hc_id'])) {
                    $hc                                   = HrHcModel::findFirst([
                        'conditions' => 'hc_id = :hc_id:',
                        'bind'       => ['hc_id' => $paramIn['business_data']['hc_id']],
                    ]);
                    $paramIn['business_data']['store_id'] = !empty($hc) ? $hc->worknode_id : '';
                }
            }
            // 将business_data对象转换为JSON字符串存储
            $log->business_data = !empty($paramIn['business_data']) ? json_encode($paramIn['business_data'],
                JSON_UNESCAPED_UNICODE) : '';
            //保存订单数据
            if (!$log->save()) {
                throw new Exception('add_operation_log error');
            }
            return true;
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log([
                'function'   => 'operationLogAdd',
                'params'     => $paramIn,
                "error_msg"  => $e->getMessage(),
                "error_line" => $e->getLine(),
                "error_file" => $e->getFile(),
            ], 'error');
            return true;
        }
    }
}
