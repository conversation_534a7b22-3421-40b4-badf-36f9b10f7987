<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\BanklistModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewPassTypeModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffPayrollCompanyInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Models\backyard\SysDistrictModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Repository\SysCityRepository;
use FlashExpress\bi\App\Repository\SysDistrictRepository;
use FlashExpress\bi\App\Repository\SysProvinceRepository;


class SysServer extends BaseServer
{
    public function __construct($lang = 'zh-CN', $timezone='+08:00')
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    public function getJobTitleList($from_db = false)
    {

        if($from_db){
            return HrJobTitleModel::find(['columns'=>'id,job_name'])->toArray();
        }
        $redis = $this->getDI()->get('redisLib');
        //优先缓存
        $key = RedisEnums::TABLE_HR_JOB_TITLE;
        $info = $redis->get($key);
        if($info){
            return json_decode($info,true);
        }
        $info = HrJobTitleModel::find(['columns'=>'id,job_name'])->toArray();
        $redis->set($key,json_encode($info),3600);
        return $info;
    }

    public function getSysProvinceList($from_db = false){
        if($from_db){
            return SysProvinceModel::find(['columns'=>'code,name'])->toArray();
        }
        $redis = $this->getDI()->get('redisLib');
        //优先缓存
        $key = RedisEnums::TABLE_SYS_PROVINCE;
        $info = $redis->get($key);
        if($info){
            return json_decode($info,true);
        }
        $info = SysProvinceModel::find(['columns'=>'code,name'])->toArray();
        $redis->set($key,json_encode($info),3600);
        return $info;
    }

    //查询市
    public function getCitiesArrByCodeArr( array $citiesCode, $field = '*'){
        return SysCityModel::getCitiesArrByCodeArr($citiesCode, $field);
    }
    //查查询区
    public function getDistrictsArrByCodeArr(array $citiesCode, $field = '*'){
        return SysDistrictModel::getDistrictsArrByCodeArr($citiesCode, $field);
    }

    /**
     * 根据 省 code 查市信息
     * @param $provinceCode
     * @return bool
     */
    public function getCityInfoByProvinceCode($provinceCode)
    {
        return SysCityModel::find([
            'columns' => 'id,code,name',
            'conditions' => 'deleted=0 and  province_code = :province_code:',
            'bind' => [
                'province_code' => $provinceCode,
            ],
            'order'=> 'name asc',
        ])->toArray();
    }

    /**
     * 根据 市 code 查区
     * @param $cityCode
     * @return mixed
     */
    public function getDistrictInfoByCityCode($cityCode)
    {
        return SysDistrictModel::find([
            'columns' => 'id,code,name,postal_code',
            'conditions' => 'deleted=0 and  city_code = :city_code:',
            'bind' => [
                'city_code' => $cityCode,
            ],
            'order'=> 'name asc',
        ])->toArray();
    }
    //根据 city code 获取 信息和 省信息
    public function getProvinceCityInfo($cityCode){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('c.code c_code,c.name c_name, c.en_name c_en_name,p.code p_code, p.name p_name, p.en_name p_en_name');
        $builder->from(['c' => SysCityModel::class]);
        $builder->leftjoin(SysProvinceModel::class, 'c.province_code = p.code', 'p');
        $builder->where('c.code = :city_code:', ['city_code' => $cityCode]);
        $info = $builder->getQuery()->execute()->getFirst();

        if(empty($info)){
            return [];
        }
        return $info->toArray();
    }


    //省市 联动 key  value  children 用缓存调用!
    public function getProvinceCitySelect()
    {
        $provinceData = SysProvinceModel::find([
            'columns'    => 'code,name,en_name',
            'conditions' => 'deleted = 0',
        ])->toArray();

        if (empty($provinceData)) {
            return [];
        }
        $changeLang = false;//是否切英文 但是 英文是空 还切name
        if($this->lang != 'th'){
            $changeLang = true;
        }

        $res = [];
        foreach ($provinceData as $p) {
            $row            = [];
            $row['value']     = $p['code'];
            $row['label']   = $changeLang ? (empty($p['en_name']) ? $p['name'] : $p['en_name']) : $p['name'];
            $cityData       = SysCityModel::find([
                'columns'    => 'code,name,en_name',
                'conditions' => 'deleted = 0 and province_code = :province_code:',
                'bind'       => ['province_code' => $p['code']],
            ])->toArray();
            foreach ($cityData as $c) {
                $item['value']       = $c['code'];
                $item['label']     = $changeLang ? (empty($c['en_name']) ? $c['name'] : $c['en_name']) : $c['name'];
                $row['children'][] = $item;
            }
            $res[] = $row;
        }
        return $res;
    }


    public function getSysStoreList($from_db = false){
        if($from_db){
            return SysStoreModel::find(['columns'=>'code,name'])->toArray();
        }
        $redis = $this->getDI()->get('redisLib');
        //优先缓存
        $key = RedisEnums::TABLE_SYS_STORE;
        $info = $redis->get($key);
        if($info){
            return json_decode($info,true);
        }
        $info = SysStoreModel::find(['columns'=>'id,name'])->toArray();
        $redis->set($key,json_encode($info),3600);
        return $info;
    }

    public function getSysDepartmentList($from_db = false){
        if($from_db){
            return SysDepartmentModel::find(['columns'=>'id,name'])->toArray();
        }
        $redis = $this->getDI()->get('redisLib');
        //优先缓存
        $key = RedisEnums::TABLE_SYS_DEPARTMENT;
        $info = $redis->get($key);
        if($info){
            return json_decode($info,true);
        }
        $info = SysDepartmentModel::find(['columns'=>'id,name'])->toArray();
        $redis->set($key,json_encode($info),3600);
        return $info;
    }

    /**
     * 获取面试不通过类型-原因
     * @return array
     */
    function getInterviewPassType(){
        $data = HrInterviewPassTypeModel::find([
            'columns'=>'pass_type, pass_reason',
            'conditions' => " deleted=".enums::DELETED_NO,
        ])->toArray();
        $return_data = [];
        foreach ($data as $key=>$val){
            $return_data[$val['pass_type']]['id'] = $val['pass_type'].'.0';
            $return_data[$val['pass_type']]['key'] = (int)$val['pass_type'];
            $return_data[$val['pass_type']]['name'] = $this->getTranslation()->_(enums::INTERVIEW_PASS_TYPE_PREFIX . $val['pass_type']);
            if($val['pass_reason']){
                $return_data[$val['pass_type']]['child'][] = [
                    'id' => $val['pass_type'] .'.'. $val['pass_reason'],
                    'key' => (int)$val['pass_reason'],
                    'name' => $this->getTranslation()->_(enums::INTERVIEW_PASS_TYPE_REASON_PREFIX . $val['pass_type'] . '_' . $val['pass_reason']),
                ];
            }
        }
        return array_values($return_data);
    }

    /**
     * @description 获取雇佣类型
     * @return array[]
     */
    public function getHireTypesList($job_title = 0, $store_id = '',$jd_id = 0): array
    {
        $agentJobTitle = (new SettingEnvServer)->getSetVal('individual_contractor_job_title', ',');
        $hireTypeEnum   = (new SettingEnvServer)->getSetVal('hire_type_enum');
        $hireTypeEnum   = explode(',', ($hireTypeEnum ?: "1,2,3,4,5"));
        //不在个人代理可选职位的，不能展示个人代理类型
        if (isCountry() && !in_array($job_title, $agentJobTitle)) {
            $hireTypeEnum = array_diff($hireTypeEnum, [HrStaffInfoModel::HIRE_TYPE_UN_PAID]);
        }
        $returnData = [];
        foreach ($hireTypeEnum as $k => $v) {
            $returnData[$k]['key']   = intval($v);
            $returnData[$k]['value'] = $this->getTranslation()->_('hire_type_' . $v);
        }
        return array_values($returnData);
    }
    /**
     * @description 获取雇佣类型
     * @return array[]
     */
    public function getReserveTypeList(): array
    {
        return [[
            'key' => HrResumeModel::RESERVE_TYPE_FORMAL,
            'value' => $this->getTranslation()->_('reserve_type_1'),
        ], [
            'key' => HrResumeModel::RESERVE_TYPE_AGENT,
            'value' => $this->getTranslation()->_('reserve_type_2'),
        ]];
    }

    /**
     * 获取员工户口地址
     * @param $data
     * @return string
     */
    public function getStaffRegisterAddress($data): string
    {
        $address = '';
        if (empty($data)) {
            return $address;
        }

        $addressBox = [];

        //户口地址所在门牌号
        if (!empty($data['REGISTER_HOUSE_NUM'])) {
            $addressBox[] = $data['REGISTER_HOUSE_NUM'];
        }

        //户口地址所在村号
        if (!empty($data['REGISTER_VILLAGE_NUM'])) {
            $addressBox[] = $data['REGISTER_VILLAGE_NUM'];
        }
        //户口地址所在村
        if (!empty($data['REGISTER_VILLAGE'])) {
            $addressBox[] = $data['REGISTER_VILLAGE'];
        }

        //户口地址所在巷
        if (!empty($data['REGISTER_ALLEY'])) {
            $addressBox[] = $data['REGISTER_ALLEY'];
        }

        //户口地址所在街道
        if (!empty($data['REGISTER_STREET'])) {
            $addressBox[] = $data['REGISTER_STREET'];
        }

        //户口地址所在乡
        if (!empty($data['REGISTER_DISTRICT'])) {
            $addressBox[] = SysDistrictRepository::findFirstByCode($data['REGISTER_DISTRICT'])['name'] ?? $data['REGISTER_DISTRICT'];
        }

        //户口地址所在市
        if (!empty($data['REGISTER_CITY'])) {
            $addressBox[] = SysCityRepository::findFirstByCode($data['REGISTER_CITY'])['name'] ?? $data['REGISTER_CITY'];
        }

        //户口地址所在省
        if (!empty($data['REGISTER_PROVINCE'])) {
            $addressBox[] = SysProvinceRepository::findFirstByCode($data['REGISTER_PROVINCE'])['name'] ?? $data['REGISTER_PROVINCE'];
        }

        //户口地址所在邮编
        if (!empty($data['REGISTER_POSTCODES'])) {
            $addressBox[] = $data['REGISTER_POSTCODES'];
        }

        return implode(' ', $addressBox);
    }
    
    public function getAddressList($address_type, $code, $query_name = '')
    {
        $columns         = ['code', 'name'];
        $conditions      = 'deleted = :deleted:';
        $bind['deleted'] = 0;
        if ($address_type == 2) {
            $model        = new SysCityModel();
            $conditions   .= ' and province_code = :province_code:';
            $bind['province_code'] = $code;
        } elseif ($address_type == 3) {
            $model        = new SysDistrictModel();
            $conditions   .= ' and city_code = :city_code:';
            $bind['city_code'] = $code;
            $columns[]    = 'postal_code';
        } else {
            $model = new SysProvinceModel();
        }
        if ($query_name) {
            $conditions   .= ' and name like :name:';
            $bind['name'] = '%' . $query_name . '%';
        }
        $q = [
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
            'order'      => 'name asc',
        ];
        $list = $model::find($q)->toArray();

        if ($address_type == 3) {
            foreach ($list as &$item) {
                $p = explode(',', $item['postal_code']);
                foreach ($p as $pc) {
                    $item['postal_code_list'][] = ['text' => $pc, 'value' => $pc];
                }
            }
        }
        return $list;
    }

    /**
     * 获取国家列表
     * @throws BusinessException
     */
    public function getTotalDictionaryRegionByDictCode($dict_code,$lang = ''): array
    {
        $apiClient = new ApiClient('hcm_rpc', '', 'getTotalDictionaryItemsByDictCode', !empty($lang) ? $lang : $this->lang);
        $apiClient->setParams(['dict_code' => $dict_code]);
        $result = $apiClient->execute();
        $this->getDI()->get("logger")->write_log("getTotalDictionaryItemsByDictCode result: " . json_encode($result,
                JSON_UNESCAPED_UNICODE), "info");
        if ($result['result'] && $result['result']['code'] != 1) {
            throw new BusinessException($result['result']['msg']);
        }
        if ($dict_code != 'address_country_region'){
            return $result['result']['data'] ?? [];
        }
        $data          = $result['result']['data'];
        $distance_list = array_column($data, 'value');
        array_multisort($distance_list, SORT_ASC, $data);
        $current_nationality = (new SettingEnvServer())->getSetVal('s_f_d_nationality');
        $tmp                 = [];
        foreach ($data as $key => $item) {
            if ($item['value'] == $current_nationality) {
                $tmp = $item;
                unset($data[$key]);
            }
        }
        return array_values(array_merge([$tmp], $data));
    }

    /**
     * 获取目标枚举
     */
    public function probationEnumsList(): array
    {
        $returnData = [];

        $tab_setting_state_list = HrProbationTargetModel::$tab_setting_state_list;
        $setting_state_list     = HrProbationTargetModel::$setting_state_list;
        $send_state_list        = HrProbationTargetModel::$send_state_list;
        $sign_state_list        = HrProbationTargetModel::$sign_state_list;

        foreach ($tab_setting_state_list as $key => $value) {
            $returnData['probationTargetTabSettingState'][] = [
                'label' => $this->t->_($value),
                'value' => (string)$key,
            ];
        }

        unset($setting_state_list[HrProbationTargetModel::SETTING_STATE_NOT_START]);
        foreach ($setting_state_list as $key => $value) {
            $returnData['probationTargetSettingState'][] = [
                'label' => $this->t->_($value),
                'value' => (string)$key,
            ];
        }

        foreach ($send_state_list as $key => $value) {
            $returnData['probationTargetSendState'][] = [
                'label' => $this->t->_($value),
                'value' => (string)$key,
            ];
        }

        foreach ($sign_state_list as $key => $value) {
            $returnData['probationTargetSignState'][] = [
                'label' => $this->t->_($value),
                'value' => (string)$key,
            ];
        }

        return $returnData;
    }

    /**
     * @param string $content
     * @return string
     */
    public function getEmailSignature(string $content,$company_type = StaffPayrollCompanyInfoModel::FLASH_EXPRESS): string
    {
        if (isCountry('MY')) {
            $companyConfigInfo       = StaffPayrollCompanyInfoModel::findFirst([
                'columns'    => 'company_name, company_registration_no,business_registration_number',
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $company_type],
            ]);
            $companyConfigInfo       = empty($companyConfigInfo) ? [] : $companyConfigInfo->toArray();
            $company_name            = $companyConfigInfo['company_name'] ?? '';
            $business_registration_number = !empty($companyConfigInfo['business_registration_number']) ? '('.$companyConfigInfo['business_registration_number'].')' : '';
            $company_registration_no = !empty($companyConfigInfo['company_registration_no']) ? 'Company Registration No. : '.$companyConfigInfo['company_registration_no'] . $business_registration_number: '';
            $email_signature         = '<br/><br/><br/>' . $company_name . '<br/>' . $company_registration_no;
            $content                 .= $email_signature;
        }
        return $content;
    }
    
    /**
     * @param $bank_id
     * @param $bank_no
     * @return true
     * @throws ValidationException
     */
    public function bankNoVerify($bank_id,$bank_no): bool
    {
        $bank_id = trim($bank_id);
        $bank_no = trim($bank_no);
        $bank_list = BanklistModel::findFirst([
            'conditions' => "bank_id = :bank_id:",
            'bind'       => [
                'bank_id' => $bank_id,
            ],
        ]);
        if (empty($bank_list) || empty($bank_no) || !preg_match("/^\d{1,30}$/", $bank_no)) {
            throw new ValidationException($this->getTranslation()->_('bank_no_err_1'));
        } else {
            $bank_no_len = strlen($bank_no);
            if (($bank_no_len < $bank_list->min_length || $bank_no_len > $bank_list->max_length) && $bank_list->min_length != $bank_list->max_length) {
                throw new ValidationException($this->getTranslation()->_('bank_no_err_3',
                    ['min_length' => $bank_list->min_length, 'max_length' => $bank_list->max_length]));
            } elseif (($bank_no_len < $bank_list->min_length || $bank_no_len > $bank_list->max_length) && $bank_list->min_length == $bank_list->max_length) {
                throw new ValidationException($this->getTranslation()->_('bank_no_err_2', ['min_length' => $bank_list->min_length]));
            }
        }
        return true;
    }

    public function getAgentJAllowJobTitleConfig($store_id)
    {
        if (isCountry('TH')) {
            $agentJobTitle = (new HcHireTypeConfigServer())->getAgentConfigJobTitle($store_id);
            return $agentJobTitle ? implode(',', $agentJobTitle) : '';
        } else {
            return (new SettingEnvServer)->getSetVal('individual_contractor_job_title');
        }
    }
}