<?php
namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\MobileHelper;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\backyard\VehicleInspectionModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\VanContainerRepository;

class VehicleInspectionServer extends BaseServer
{

    /**
     * 版本控制
     * @return bool|mixed
     */
    public function enableMobileVersion()
    {
        $set_val        = (new SettingEnvServer())->getSetVal('vehicle_inspection_version_config');
        if (empty($set_val)) {
            return true;
        }
        $equipment_info = json_decode($set_val, true);
        return MobileHelper::compareVersion($equipment_info);
    }

    public function isShowVehicleInspection($staff_info_id): bool
    {
        //切主账号
        if ($master_staff_id = StaffRepository::getMasterStaffIdBySubStaff($staff_info_id)) {
            $staff_info_id = $master_staff_id;
        }

        $exist = VehicleInspectionModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and status = :status:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'status' => VehicleInspectionModel::STATUS_WAIT],
        ]);
        return !empty($exist);
    }

    public function isShowVehicleInspectionById($id): bool
    {
        $exist = VehicleInspectionModel::findFirst([
            'conditions' => 'id = :id: and status = :status:',
            'bind'       => ['id' => $id, 'status' => VehicleInspectionModel::STATUS_WAIT],
        ]);
        return !empty($exist);
    }

    /**
     * 提交状态
     * @param $staff_info_id
     * @param $msg_id
     * @return string
     */
    public function getStatus($staff_info_id,$msg_id = ''): string
    {
        $exist = $this->isShowVehicleInspection($staff_info_id);
        if ($exist) {
            return strval(VehicleInspectionModel::STATUS_WAIT);
        }
        if (!empty($msg_id)) {
            (new BackyardServer($this->lang, $this->timeZone))->has_read_operation($msg_id);
        }

        return strval(VehicleInspectionModel::STATUS_SUBMITTED);
    }

    /**
     * 获取提交状态-按业务id 查询
     * @param $params
     * @return string
     */
    public function getStatusNew($params): string
    {
        $exist = $this->isShowVehicleInspectionById($params['id']);
        if ($exist) {
            return strval(VehicleInspectionModel::STATUS_WAIT);
        }
        if (!empty($params['msg_id'])) {
            (new BackyardServer($this->lang, $this->timeZone))->has_read_operation($params['msg_id']);
        }

        return strval(VehicleInspectionModel::STATUS_SUBMITTED);
    }


    /**
     * 提交接口
     * @throws ValidationException
     */
    public function submit($staff_info_id, $params): bool
    {
        $t = $this->getTranslation();
        //切主账号
        if ($master_staff_id = StaffRepository::getMasterStaffIdBySubStaff($staff_info_id)) {
            $staff_info_id = $master_staff_id;
        }

        $vehicle_inspection_model = VehicleInspectionModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and status = :status:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'status' => VehicleInspectionModel::STATUS_WAIT],
        ]);
        if (empty($vehicle_inspection_model)) {
            throw new ValidationException($t->_('ticket_repeat_msg'));
        }

        $recentlyInfo = $this->getStaffRecentlyInfoFromBI($staff_info_id);

        $vanContainer = VanContainerRepository::getOne(['staff_info_id' => $staff_info_id]);

        $vehicleInfoModel =  VehicleInfoModel::findFirst([
            'conditions' => 'uid = :uid: ',
            'bind'       => ['uid' => $staff_info_id],
        ]);
        $vehicle_inspection_model->mileage_num =$params['mileage_num'];
        $vehicle_inspection_model->plate_number = empty($vehicleInfoModel) ? '' : $vehicleInfoModel->plate_number;
        $vehicle_inspection_model->license_location = empty($vehicleInfoModel) ? '' : $vehicleInfoModel->license_location;
        $vehicle_inspection_model->vehicle_video = $this->getDI()->getConfig()->application['img_prefix'] . $params['vehicle_video'];
        $vehicle_inspection_model->submit_time = date('Y-m-d H:i:s');
        $vehicle_inspection_model->status = VehicleInspectionModel::STATUS_SUBMITTED;
        //里程汇报
        $vehicle_inspection_model->report_mileage_num = !empty($recentlyInfo['kilometres']) ? $recentlyInfo['kilometres'] : '';
        $vehicle_inspection_model->mileage_img = !empty($recentlyInfo['img']) ? $recentlyInfo['img'] : NULL;
        $vehicle_inspection_model->report_mileage_time = !empty($recentlyInfo['time_date']) ? $recentlyInfo['time_date'] : NULL;
        //车厢记录id
        $vehicle_inspection_model->van_container_id = !empty($vanContainer['id']) ? $vanContainer['id'] : 0;

        $vehicle_inspection_model->video_status = VehicleInspectionModel::VIDEO_STATUS_PENDING;

        return $vehicle_inspection_model->save();
    }

    /**
     * 从 BI 获取 车辆里程汇报记录
     * @param $staffId
     * @return array|mixed
     */
    public function getStaffRecentlyInfoFromBI($staffId)
    {
        $ac = new ApiClient('ard_api', '', 'Mileage.getStaffRecentlySubmitInfo', $this->lang);
        $ac->setParams([
            'staff_info_id' => $staffId,
        ]);
        $ac_result = $ac->execute();
        $this->logger->write_log(['getStaffRecentlyInfoFromBI' => [$staffId, $ac_result]], 'info');
        if (!empty($ac_result['result']['code']) && $ac_result['result']['code'] == 1) {
            return empty($ac_result['result']['data']) ? [] : $ac_result['result']['data'];
        }
        return [];
    }



}
