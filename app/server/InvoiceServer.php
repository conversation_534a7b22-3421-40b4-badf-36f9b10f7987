<?php
/**
 * Author: Bruce
 * Date  : 2024-01-24 22:20
 * Description:
 */

namespace FlashExpress\bi\App\Server;


use AliyunMNS\Exception\ReceiptHandleErrorException;
use Exception;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\InvoiceResendEmailModel;
use FlashExpress\bi\App\Modules\My\library\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Repository\BankListRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use WebGeeker\Validation\Validation;

class InvoiceServer extends BaseServer
{
    private static $single = null;
    public         $timezone;


    public static function getInstance($lang, $timezone)
    {
        if (!self::$single) {
            self::$single = new self($lang, $timezone);
        }

        return self::$single;
    }

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 处理 invoice
     * @param $params
     * @return bool
     * @throws Exception
     */
    public function handle($params)
    {
        $params = $this->formatData($params);

        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoOne($params['staff_info_id']);
        if (empty($staffInfo)) {
            return false;
        }

        //获取地址
        $itemInfo = (new StaffRepository($this->lang))->getStaffItemsInfo([$params['staff_info_id']], [
            'RESIDENCE_DETAIL_ADDRESS',
            'RESIDENCE_POSTCODES',
            'RESIDENCE_PROVINCE',
            'RESIDENCE_CITY',
            'BANK_NO_NAME',
        ]);

        $address       = $itemInfo[$params['staff_info_id']]['RESIDENCE_DETAIL_ADDRESS'] ?? '';
        $province      = $itemInfo[$params['staff_info_id']]['RESIDENCE_PROVINCE'] ?? '';
        $city          = $itemInfo[$params['staff_info_id']]['RESIDENCE_CITY'] ?? '';
        $postCode      = $itemInfo[$params['staff_info_id']]['RESIDENCE_POSTCODES'] ?? '';
        $bankStaffName = $itemInfo[$params['staff_info_id']]['BANK_NO_NAME'] ?? '';

        $sysServer    = new SysServer();
        $provinceCode = array_column($sysServer->getSysProvinceList(), 'name', 'code');
        $cityArr      = array_column($sysServer->getCitiesArrByCodeArrFromCache([$city], 'code,name'),
            'name', 'code');
        $city         = $cityArr[$city] ?? $city;
        $province     = $provinceCode[$province] ?? $province;

        $bankInfo = BankListRepository::getBankInfo($staffInfo['bank_type']);

        $params['address']         = $address . ' ' . $postCode . ' ' . $city . ' ' . $province;
        $params['identity']        = $staffInfo['identity'];
        $params['mobile']          = $staffInfo['mobile'];
        $params['send_date']       = date('d/m/Y');
        $params['bank_staff_name'] = $bankStaffName;
        $params['bank_type_text']  = $bankInfo['bank_name'] ?? '';
        $params['bank_no']         = $staffInfo['bank_no'] ?? '';


        $start_date           = $params['start_date'];

        //  1. 如果员工存在「转型日期」在周期内，则开始日期为「转型日期」
        //  2. 如果员工「入职日期」在周期内，则开始日期为「入职日期」
        $date = !empty($staffInfo['signing_date']) ? $staffInfo['signing_date'] : $staffInfo['hire_date'];
        if (!empty($date) && strtotime($params['start_date']) <= strtotime($date)) {
            $params['start_date'] = date('Y-m-d', strtotime($date));
        }


        //快递员最后工作日处于时间范围之内，则结束时间显示为员工最后工作日
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_2 && !empty($staffInfo['leave_date'])) {
            if (strtotime($params['end_date']) >= strtotime($staffInfo['leave_date'])) {
                $params['end_date'] = date('Y-m-d', strtotime("{$staffInfo['leave_date']} -1 day"));
            }
        }
        $params['start_date'] = date('d/m/Y', strtotime($params['start_date']));
        $params['end_date']   = date('d/m/Y', strtotime($params['end_date']));

        $pdfUrl = $this->invoiceCreatPdf($params);
        if ($pdfUrl === false) {
            return false;
        }

        $callBackData['staff_info_id'] = $staffInfo['staff_info_id'];
        $callBackData['start_date']    = $start_date;
        $callBackData['pdf_url']       = $pdfUrl;
        $callBackData['job_title']     = $params['job_title'];
        $callBackResult                = $this->callBackToFbi($callBackData);
        if (!$callBackResult) {
            return false;
        }

        $message['staff_info_id'] = $staffInfo['staff_info_id'];
        $message['start_date']    = $params['start_date'];
        $message['end_date']      = $params['end_date'];
        $message['period_id']     = $params['period_id'];

        $sendMessageRes = $this->sendMessage(json_encode($message, JSON_UNESCAPED_UNICODE));
        if (!$sendMessageRes) {
            return false;
        }

        $emailData['staff_info_id'] = $staffInfo['staff_info_id'];
        $emailData['name']          = $staffInfo['name'];
        $emailData['email']         = $staffInfo['personal_email'];
        $emailData['start_date']    = $params['start_date'];
        $emailData['end_date']      = $params['end_date'];
        $emailData['link']          = env('sign_url') . '/#/LinkLogin?source=invoice&start_date=' . $start_date . '&staff_info_id=' . $staffInfo['staff_info_id']
            . '&period_id=' . $params['period_id'];

        $this->sendEmail(json_encode($emailData, JSON_UNESCAPED_UNICODE));

        return true;
    }

    /**
     * 格式化数据
     * @param $params
     * @return array
     */
    public function formatData($params)
    {
        $payment_terms_key = $this->getPaymentTermsKey($params);
        return [
            "data_id"                      => $params['data_id'] ?? '',
            "staff_info_id"                => $params['staff_info_id'] ?? '',
            "staff_name"                   => $params['staff_name'] ?? '',
            "store_id"                     => $params['store_id'] ?? '',
            "store_name"                   => $params['store_name'] ?? '',
            "job_title"                    => $params['job_title'] ?? '',
            "period_id"                    => $params['period_id'] ?? '',
            "vehicle_category"             => $params['vehicle_category'] ?? '',
            "start_date"                   => $params['start_date'] ?? '', //结算周期开始日期
            "end_date"                     => $params['end_date'] ?? '', //结算周期结束日期

            "delivery_amount"              => $params['delivery_amount'] ?? '', //派件提成总金额 -- 展示快递员上月的总派件提成
            "delivery_amount_key"          => $this->t->_('delivery_amount_key'), //派件提成总金额 -- 展示快递员上月的总派件提成--Total Delivery Payment

            "pickup_amount"                => $params['pickup_amount'] ?? '', //揽件提成总金额 -- 展示快递员上月的总揽件提成
            "pickup_amount_key"            => $this->t->_('pickup_amount_key'), //揽件提成总金额 -- 展示快递员上月的总揽件提成--Total Pick-up Payment

            "productivity_bonus"           => $params['productivity_bonus'] ?? '', //全勤奖 -- 全勤奖
            "productivity_bonus_key"       => $this->t->_('productivity_bonus_key'), //全勤奖 -- 全勤奖--Productivity Bonus

            "ph_delivery_bonus"            => $params['ph_delivery_bonus'] ?? '', //节假日津贴    -- 节假日津贴
            "ph_delivery_bonus_key"        => $this->t->_('ph_delivery_bonus_key'), //节假日津贴    -- 节假日津贴--PH delivery Bonus

            "other_addition"               => $params['other_addition'] ?? '', //其他补贴（5合一）-- Other Deduction 其他补贴
            "other_addition_key"           => $this->t->_('other_addition_key'), //其他补贴（5合一）-- Other Deduction 其他补贴

            "asset_deduction_money"        => $params['asset_deduction_money'] ?? '', //资产扣款金额 -- Asset related Compensation Deduction资产扣款金额
            "asset_deduction_money_key"    => $this->t->_('asset_deduction_money_key'), //资产扣款金额 -- Asset related Compensation Deduction资产扣款金额

            "penalty"                      => $params['penalty'] ?? '', //Penalty (Flash Kit)
            "penalty_key"                  => $this->t->_('penalty_key'), //Penalty (Flash Kit)

            "early_termination_day_punish"     => $params['early_termination_day_punish'] ?? '', //提前解约单日违约金 -- Single-day penalty for early termination
            "early_termination_day_punish_key" => $this->t->_('early_termination_day_punish_key'), //提前解约单日违约金 -- Single-day penalty for early termination

            "other_deduction"              => $params['other_deduction'] ?? '', //未回款金额+其他扣款 -- Other Deduction
            "other_deduction_key"          => $this->t->_('other_deduction_key'), //未回款金额+其他扣款 -- Other Deduction

            "held_money"                   => $params['held_money'] ?? '',//Held金额    -- Held金额
            "held_money_key"               => $this->t->_('held_money_key'),//Held金额    -- Held金额 --Payment Held

            "income_total_amount"          => $params['income_total_amount'] ?? '', //收入提成总金额
//            "personal_punish"              => $params['personal_punish'] ?? '', //普通罚款      -- Personal Fine 普通罚款
//            "parcels_damage_punish"        => $params['parcels_damage_punish'] ?? '', //破损赔偿金额  -- Parcels Damage破损赔偿金额
//            "parcels_loss_punish"          => $params['parcels_loss_punish'] ?? '', //丢失赔偿金额  -- Parcels Loss丢失赔偿金额

            //提前解约罚款
            "early_termination_punish"     => $params['early_termination_punish'] ?? '',
            "early_termination_punish_key" => $this->t->_('early_termination_punish_key'), //提前解约罚款-Penalty for breach of contract

            "total_amount"                 => $params['total_amount'] ?? '',//提成总金额   -- Total

            "payment_terms_key"            => $this->t->_($payment_terms_key),//payment terms支付说明文案

            //21921【MY | FBI】个人代理 & Van 快递员提成方案修改
            //https://flashexpress.feishu.cn/docx/TbPYdaEx0oF3cZxm2BKc4vqBn1d
            //支援补贴
            "help_amount"                => $params['help_amount'] ?? '',
            "help_amount_key"            => $this->t->_('help_amount_key'),

            //保底补贴
            "floor_subsidy"                => $params['floor_subsidy'] ?? '',
            "floor_subsidy_key"            => $this->t->_('floor_subsidy_key'),

            //普通PVD提成总金额
            "normal_pvd_amount"                => $params['normal_pvd_amount'] ?? '',
            "normal_pvd_amount_key"            => $this->t->_('normal_pvd_amount_key'),

            //加班PVD提成总金额
            "add_pvd_amount"                => $params['add_pvd_amount'] ?? '',
            "add_pvd_amount_key"            => $this->t->_('add_pvd_amount_key'),

            //车补津贴
            "car_subsidy"                => $params['car_subsidy'] ?? '',
            "car_subsidy_key"            => $this->t->_('car_subsidy_key'),

            //GDL津贴
            "gdl_subsidy"                => $params['gdl_subsidy'] ?? '',
            "gdl_subsidy_key"            => $this->t->_('gdl_subsidy_key'),

            //内推津贴
            "referral_subsidy"                => $params['referral_subsidy'] ?? '',
            "referral_subsidy_key"            => $this->t->_('referral_subsidy_key'),

            //是否显示新追加字段, 1=显示 2=不显示
            "is_show"                    => strtotime($params['start_date']) >= strtotime($this->getInvoiceEffectDate())
                ? "1" : "2",
        ];
    }

    /**
     * 创建pdf
     * @param $params
     * @return bool|string
     * @throws Exception
     */
    public function invoiceCreatPdf($params)
    {
        $tempFile = BASE_PATH . '/public/pdf_template/invoice.ftl';

        $pdfTempUrl = (new ToolServer($this->lang, $this->timezone))->getPdfTemp($tempFile);

        try {
            $pdf_file_data = (new formPdfServer())->generatePdf($pdfTempUrl, $params,
                [], '', [], 'attchment');

            //生产pdf失败
            if (empty($pdf_file_data['object_url'])) {
                $this->logger->write_log([
                    "invoiceCreatPdf_create_fail" => $params,
                    'result'                      => json_encode($pdf_file_data),
                ],
                    'notice');
                return false;
            }
            return $pdf_file_data['object_url'] ?? '';
        } catch (\Exception $e) {
            $this->logger->write_log("invoiceCreatPdf fail ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString());
            echo 'invoiceCreatPdf fail' . PHP_EOL;
            return false;
        }
    }

    /**
     * 发送消息
     * @param $data
     * @return bool
     */
    public function sendMessage($data)
    {
        $messageData = json_decode($data, true);
        $title       = $this->t->_('send_invoice_message_title');
        $content     = 'invoice_start_date=' . $messageData['start_date'] . '&invoice_end_date=' . $messageData['end_date'] . '&period_id=' . $messageData['period_id'];

        $kit_param['staff_info_ids_str'] = $messageData['staff_info_id'];
        $kit_param['staff_users']        = [['id' => $messageData['staff_info_id']]];
        $kit_param['message_title']      = $title;
        $kit_param['message_content']    = $content;
        $kit_param['category']           = MessageEnums::MESSAGE_CATEGORY_CODE_INVOICE;

        $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($kit_param);
        $res = $bi_rpc->execute();
        if (!isset($res['result']['code']) || $res['result']['code'] != ErrCode::SUCCESS) {
            $this->getDI()->get('logger')->write_log([
                'function' => 'invoice_send_message',
                'message'  => '消息发送失败',
                'params'   => $data,
                'result'   => $res,
            ]);
            return false;
        }
        return true;
    }

    /**
     * 发送邮件
     * @param $data
     * @return bool
     */
    public function sendEmail($data)
    {
        $this->getDi()->get('logger')->write_log('invoice_send_email_data:' . $data, 'info');

        $emailData = json_decode($data, true);
        try {
            $email_num = $emailData['email'];
            $title     = $this->t->_('send_invoice_email_titile');
            $content   = $this->t->_('send_invoice_email_contetn', [
                'name'          => $emailData['name'],
                'staff_info_id' => $emailData['staff_info_id'],
                'start_date'    => $emailData['start_date'],
                'end_date'      => $emailData['end_date'],
                'link'          => $emailData['link'],
            ]);

            if (!empty($emailData['email'])) {
                $sendEmail = \FlashExpress\bi\App\library\Mail::send(
                    $email_num,
                    $title,
                    $content);

                if ($sendEmail) {
                    return true;
                } else {
                    $this->logger->write_log(['invoice_send_mail_fail' => $data, 'msg' => '发送异常'], 'notice');
                    return false;
                }
            } else {
                $this->logger->write_log(['invoice_send_mail_fail' => $data, 'msg' => '个人邮箱为空'], 'notice');
                return false;
            }
        } catch (Exception $e) {
            $this->getDi()->get('logger')->write_log('出现异常！invoice_send_email:邮件发送失败：data:' . $data . 'message:' . $e->getMessage(),
                'notice');
            return false;
        }
    }

    /**
     * 将pdf 回调同步 给fbi
     * @param $params
     * @return bool
     */
    public function callBackToFbi($params)
    {
        $data['staff_info_id'] = $params['staff_info_id'];
        $data['start_date']    = $params['start_date'];
        $data['pdf_url']       = $params['pdf_url'];
        $data['job_title']     = $params['job_title'];
        //todo 更换地址
        $ret = new ApiClient('ard_api', '', 'proxyInvoice.receive_invoice_pdf', $this->lang);
        $ret->setParams($data);
        $res = $ret->execute();
        $this->getDI()->get('logger')->write_log("invoiceCallBackFbi 参数:" . json_encode($data) . ";结果:" . json_encode($res),
            'info');
        if (!isset($res['result'])) {
            return false;
        }
        if ($res['result']['code'] == 1) {
            return true;
        }
        $msg = isset($res['result']['msg']) ? $res['result']['msg'] : $res['error'];
        $this->getDI()->get('logger')->write_log("invoiceCallBackFbi 参数:" . json_encode($data) . ";结果:" . $msg);

        return false;
    }

    /**
     * invoice 重新发送邮件
     * @return bool
     */
    public function resendEmail()
    {
        $db = $this->getDI()->get('db');

        $staffRepository = new StaffRepository();
        $num = 0;
        while($data = $this->getInvoiceResendData())
        {
            foreach ($data as $oneData) {
                //找不到员工信息
                $staffInfo = $staffRepository->getStaffInfoOne($oneData['staff_info_id']);
                if(empty($staffInfo)) {
                    $db->updateAsDict('invoice_resend_email', ['status' => 2], 'id = ' . $oneData['id']);
                    continue;
                }

                $pos = strpos($staffInfo['personal_email'], '@gmail');
                //不是 gmail 的邮箱
                if($pos === false) {
                    $db->updateAsDict('invoice_resend_email', ['status' => 3], 'id = ' . $oneData['id']);
                    continue;
                }
                $start_date           = $oneData['start_date'];

                //  1. 如果员工存在「转型日期」在周期内，则开始日期为「转型日期」
                //  2. 如果员工「入职日期」在周期内，则开始日期为「入职日期」
                $date = !empty($staffInfo['signing_date']) ? $staffInfo['signing_date'] : $staffInfo['hire_date'];
                if (!empty($date) && strtotime($oneData['start_date']) <= strtotime($date)) {
                    $oneData['start_date'] = date('Y-m-d', strtotime($date));
                }

                //快递员最后工作日处于时间范围之内，则结束时间显示为员工最后工作日
                if ($staffInfo['state'] == HrStaffInfoModel::STATE_2 && !empty($staffInfo['leave_date'])) {
                    if (strtotime($oneData['end_date']) >= strtotime($staffInfo['leave_date'])) {
                        $oneData['end_date'] = date('Y-m-d', strtotime("{$staffInfo['leave_date']} -1 day"));
                    }
                }
                $oneData['start_date'] = date('d/m/Y', strtotime($oneData['start_date']));
                $oneData['end_date']   = date('d/m/Y', strtotime($oneData['end_date']));

                $emailData['staff_info_id'] = $staffInfo['staff_info_id'];
                $emailData['name']          = $staffInfo['name'];
                $emailData['email']         = $staffInfo['personal_email'];
                $emailData['start_date']    = $oneData['start_date'];
                $emailData['end_date']      = $oneData['end_date'];
                $emailData['link']          = env('sign_url') . '/#/LinkLogin?source=invoice&start_date=' . $start_date . '&staff_info_id=' . $staffInfo['staff_info_id'];

                $res = $this->sendEmail(json_encode($emailData, JSON_UNESCAPED_UNICODE));
                if(!$res) {
                    //邮箱异常。发送失败
                    $db->updateAsDict('invoice_resend_email', ['status' => 4], 'id = ' . $oneData['id']);
                }
                //重新发送成功。
                $db->updateAsDict('invoice_resend_email', ['status' => 1], 'id = ' . $oneData['id']);
            }
            $num = $num + count($data);

            echo "已处理数量：" . $num . PHP_EOL;
        }
        return true;
    }

    /**
     * 获取 需要重新发送的 invoice 信息。
     * @return mixed
     */
    public function getInvoiceResendData()
    {
        return InvoiceResendEmailModel::find([
            'conditions' => 'status = :status:',
            'bind'       => [
                'status' => 0,
            ],
            'limit'      => 1000
        ])->toArray();
    }

    /**
     * @param $params
     * @return string
     */
    private function getPaymentTermsKey($params): string
    {
        if (strtotime($params['start_date']) < strtotime($this->getInvoiceEffectDate())) {
            return 'payment_terms_key';
        }

        if (VehicleInfoEnums::BDC_DRIVER_JOB_TITLE_ID == $params['job_title']) {
            return 'payment_terms_key_1';
        } else if (VehicleInfoEnums::JOB_VAN_TITLE_ID == $params['job_title']) {
            return 'payment_terms_key_2';
        } else {
            return 'payment_terms_key';
        }
    }

    private function getInvoiceEffectDate()
    {
        return '2025-06-01';
    }
}
