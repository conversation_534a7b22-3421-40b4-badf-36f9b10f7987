<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Interfaces\AuditInterface;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeOrderModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyDeviceTokenModel;
use FlashExpress\bi\App\Models\backyard\StaffHikvisionModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\HubOutsourcingOvertimeDetailRepository;
use FlashExpress\bi\App\Repository\HubOutsourcingOvertimeRepository;
use FlashExpress\bi\App\Repository\OsOrderRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Server\Osm\OutsourcingOrderServer;
use Phalcon\Db;
use FlashExpress\bi\App\Repository\StaffWorkAttendanceRepository;
use FlashExpress\bi\App\Models\backyard\HrOutsourcingOrderModel;
use FlashExpress\bi\App\Models\backyard\HrOutsourcingOrderDetailModel;


class OutsourcingOTServer extends AuditBaseServer
{

    /**
     * 用于获取审批详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        // TODO: Implement getDetail() method.
        // 查询外协加班业务表
        $outsourcing_ot_info = (new HubOutsourcingOvertimeRepository($this->timeZone))->getOutsourcingOTById($auditId);
        // 查询网点
        $store_info = (new SysStoreServer($this->lang,
            $this->timeZone))->getStoreInfoByid($outsourcing_ot_info['store_id']);
        // 查询申请人
        $apply_staff_info = HrStaffInfoServer::getUserInfoByStaffInfoId($outsourcing_ot_info['apply_staff_id'],
            "id,staff_info_id,name")->toArray();

        // 格式化时间段
        $b_time = date('H:i', strtotime($outsourcing_ot_info['start_time']));
        $e_time = date('H:i', strtotime($outsourcing_ot_info['end_time']));

        // 详情页面的详细信息
        $detailLists = [
            // 申请人信息
            'os_ot_sponsor'    => $apply_staff_info['name'].'('.$apply_staff_info['staff_info_id'].')',
            // 加班网点
            'os_ot_store'      => !empty($store_info['name']) ? trim($store_info['name']) : '',
            // 加班日期
            'os_ot_date'       => $outsourcing_ot_info['ot_date'],
            // 加班时间
            'os_ot_time'       => $b_time.' - '.$e_time,
            // 加班人数
            'os_ot_demand_num' => $outsourcing_ot_info['demand_num'],
            // 加班证明
            'os_ot_img'        => json_decode($outsourcing_ot_info['img']),
        ];

        $request = AuditApplyModel::findFirst([
            'conditions' => "biz_value = :value: and biz_type = :type:",
            'bind'       => [
                'type'  => enums::$audit_type['OS_OT'],
                'value' => $auditId,
            ],
        ]);
        // 如果为驳回状态 则追加驳回原因
        if ($outsourcing_ot_info['apply_state'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason1' => $request->getRejectReason() ?? '',]);
        }

        // 拼组数据 + 翻译
        $detailLists                  = $this->format($detailLists);
        $returnData['data']['detail'] = $detailLists;

        $add_hour = $this->config->application->add_hour;
        // 组织header信息
        $data = [
            'title'      => (new AuditlistRepository($this->lang,
                $this->timeZone))->getAudityType(enums::$audit_type['OS_OT']),
            'id'         => $outsourcing_ot_info['id'],
            'staff_id'   => $outsourcing_ot_info['apply_staff_id'],
            'type'       => enums::$audit_type['OS_OT'],
            'created_at' => date('Y-m-d H:i:s',strtotime("{$request->created_at}") +( $add_hour * 3600)),//$outsourcing_ot_info['created_at'],
            'updated_at' => date('Y-m-d H:i:s',strtotime("{$request->updated_at}") +( $add_hour * 3600)), //$outsourcing_ot_info['updated_at'],
            'status'     => $outsourcing_ot_info['apply_state'],
            'serial_no'  => $outsourcing_ot_info['serial_no'],
        ];

        $returnData['data']['head'] = $data;

        return $returnData;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        //申请人不可撤销
        return new AuditOptionRule(false,
            false,
            false,
            false,
            false,
            false);
    }

    /**
     * 生成概要信息用作列表页展示
     * @param int $auditId
     * @param $user
     * @return array|mixed|string
     */
    public function genSummary(int $auditId, $user)
    {
        // TODO: Implement genSummary() method.
        $info       = (new HubOutsourcingOvertimeRepository($this->timeZone))->getOutsourcingOTById($auditId);
        $store_info = (new SysStoreServer($this->lang, $this->timeZone))->getStoreInfoByid($info['store_id']);

        // 格式化时间段
        $b_time = date('H:i', strtotime($info['start_time']));
        $e_time = date('H:i', strtotime($info['end_time']));
        $param  = [
            [
                // 加班网点
                'key'   => 'os_ot_store',
                'value' => !empty($store_info['name']) ? trim($store_info['name']) : '',
            ],
            [
                // 加班日期
                'key'   => 'os_ot_date',
                'value' => $info['ot_date'],
            ],
            [
                // 申请的加班时间
                'key'   => 'os_ot_time',
                'value' => $b_time.' - '.$e_time,
            ],
            [
                // 申请的加班人数
                'key'   => 'os_ot_demand_num',
                'value' => $info['demand_num'],
            ],
        ];
        return $param ?? "";
    }

    /**
     * 审批完成后回调的方法，用于修改业务表的一些中态和信息
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        // TODO: Implement setProperty() method.
        //如果为最终审批状态，则同步更新审批状态
        if (!$isFinal) {
            return true;
        }

        if ($state == Enums::APPROVAL_STATUS_REJECTED) {
            $data['reject_reason'] = $extend['remark'] ?? '';
        }
        $data['apply_state'] = $state;
        $data['osm_state']   = HubOutsourcingOvertimeModel::OSM_STATE_PROCESSED;

        $this->getDI()->get('db')->updateAsDict(
            'hub_outsourcing_overtime',
            $data,
            'id = '.$auditId
        );
    }

    /**
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        // TODO: Implement getWorkflowParams() method.
        return [];
    }

    // ****************************************************

    /**
     * 根据当前登入者信息判断是否未分拨经理
     * pdc 相关网点的负责人包括 AM DM 网点负责人
     * @param array $userInfo
     * @return bool
     */
    public function isHubFenBoManger(array $userInfo): bool
    {
        // 拿到分拨经理的角色id
        $hubOsRoles     = UC('outsourcingStaff')['hubOsRoles'];
        $loginPositions = $userInfo['positions'];

        if (array_intersect($hubOsRoles, $loginPositions)) {
            return true;
        }

        //PDC Operations
        $pdcManagerList = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getManagerByDepartmentIdFromCache([OsStaffServer::DEPARTMENT_ID_PDC_OPERATIONS]);
        if(in_array($userInfo['staff_id'], $pdcManagerList)) {
            return true;
        }

        $ffmOs = (new OsStaffServer($this->lang, $this->timeZone))->ffmPermission($userInfo);
        if ($ffmOs) {
            return true;
        }

        return false;
    }

    /**
     * 根据登入这id获取网点
     * @param array $userInfo
     * @return array|\Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getStoreListByLoginId(array $userInfo)
    {
        // 根据当前登入用户的id，查询他管辖的网点
        $store_list = (new StaffWorkAttendanceRepository())->getManagerStore($userInfo['staff_id'],'id AS store_id,name AS store_name');

        if (!empty($store_list)) {
            return $store_list;
        }

        // 产品要求：如果没有查询到数据，返回所属网点
        if (1 == $userInfo['organization_type']) {
            $store_list[0]['store_id']   = $userInfo['organization_id'];
            $store_list[0]['store_name'] = $userInfo['organization_name'];
            return $store_list;
        }
        return $store_list;
    }

    /**
     * 加班时间
     * @return array[]
     */
    public function getDuration(): array
    {
        $outsourcing_ot_duration = (new SettingEnvServer())->getSetVal('outsourcing_ot_duration');
        return json_decode($outsourcing_ot_duration, true);
    }

    /**
     * 根据加班时长获取对应的分钟数
     * @param $index
     * @return int
     */
    public function getMinuteByDuration($index): int
    {
        $minute = 0;
        if (empty($index)) {
            return $minute;
        }
        $mapping = [
            '0.5' => 30,
            '1'   => 60,
            '1.5' => 90,
            '2'   => 120,
            '2.5' => 150,
            '3'   => 180,
            '3.5' => 210,
            '4'   => 240,
            '4.5' => 270,
            '5'   => 300,
            '5.5' => 330,
            '6'   => 360,
            '6.5' => 390,
            '7'   => 420,
            '7.5' => 450,
            '8'   => 480,
        ];
        if (!empty($mapping[$index])) {
            return $mapping[$index];
        }
        return $minute;
    }

    /**
     * by申请页基本数据
     * @param array $userInfo
     * @return array
     */
    public function getOutsourcingOTPre(array $userInfo): array
    {
        $data = [];
        // 登入者所属网点
        $data['login_store_info'] = $this->affiliatedStore($userInfo);
        // 网点信息
        $data['store_list'] = $this->getStoreListByLoginId($userInfo);
        // 加班时长
        $data['duration'] = $this->getDuration();
        // 外协公司信息
        $data['os_company_list'] = (new OsStaffServer($this->lang, $this->timeZone))->getOutCompanyInfo();
        // 外协加班数据
        $data['order_shift_list'] = $this->getOrderShiftList(array_column($data['store_list'],'store_id'));
        return $data;
    }


    public function affiliatedStore(array $userInfo): array
    {
        $result = [
            'store_id'   => '',
            'store_name' => '',
        ];
        if (1 == $userInfo['organization_type']) {
            $result['store_id']   = $userInfo['organization_id'];
            $result['store_name'] = $userInfo['organization_name'];
            return $result;
        }

        $staff_info_obj = HrStaffInfoServer::getUserInfoByStaffInfoId($userInfo['staff_id'],
            'staff_info_id,sys_store_id');
        // 员工所属网点
        if ($staff_info_obj->sys_store_id == enums::HEAD_OFFICE_ID) {
            $result['store_id']   = enums::HEAD_OFFICE_ID;
            $result['store_name'] = enums::HEAD_OFFICE;
            return $result;
        }

        $store_info           = (new SysStoreServer())->getStoreByid($staff_info_obj->sys_store_id);
        $result['store_id']   = $store_info['id'];
        $result['store_name'] = $store_info['name'];
        return $result;
    }

    /**
     * 根据参数获取order中的数据
     * @param array $params
     * @param bool $isGroup
     * @return array
     */
    public function getOutsourcingOrder(array $params, $isGroup = false): array
    {
        if (empty($params)) {
            return [];
        }

        $columns = [
            "hood.staff_info_id,
                hood.company_name_ef,
                hood.staff_name,
                hoo.id,
                hoo.serial_no as order_serial_no, 
                hoo.shift_id,
                hoo.shift_begin_time,
                hoo.shift_end_time,
                hoo.employment_date,
                hoo.store_id,
                hoo.out_company_id, 
                hoo.shift_begin_time as shift_begin_time_str, 
                hoo.shift_end_time as shift_end_time_str,
                hoo.final_audit_num
                ",
        ];

        if($isGroup) {
            $columns = [
                "count(1) as staff_numbers,
                hoo.id,
                hoo.serial_no as order_serial_no, 
                hoo.shift_id,
                hoo.shift_begin_time,
                hoo.shift_end_time,
                hoo.employment_date,
                hoo.store_id,
                hoo.out_company_id, 
                hoo.shift_begin_time as shift_begin_time_str, 
                hoo.shift_end_time as shift_end_time_str,
                hoo.final_audit_num
                ",
            ];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['hoo' => HrOutsourcingOrderModel::class]);
        $builder->leftJoin(HrOutsourcingOrderDetailModel::class, 'hood.serial_no = hoo.serial_no', 'hood');
        $builder->where('hoo.status != :status:', ['status' => HrOutsourcingOrderModel::STATUS_CANCELED]);
        $builder->andWhere('hood.staff_info_id IS NOT NULL');
        if (!empty($params['b_time']) && !empty($params['e_time'])) {
            $builder->andWhere('hoo.effective_date > :effective_start: AND hoo.effective_date <= :effective_end: AND hoo.out_company_id > 0',
                [
                    'effective_start' => $params['b_time'],//最早12点班次，11点30开始
                    'effective_end'   => $params['e_time'],//最晚次日11点半班次，11点开始
                ]);
        }

        if (!empty($params['order_serial_no']) && is_array($params['order_serial_no'])) {
            $builder->inWhere('hoo.serial_no', $params['order_serial_no']);
        }

        if (!empty($params['store_ids']) && is_array($params['store_ids'])) {
            $builder->inWhere('hoo.store_id', $params['store_ids']);
        }

        if (!empty($params['job_id'])) {
            $builder->inWhere('hoo.job_id', is_array($params['job_id']) ? $params['job_id'] : [$params['job_id']]);
        }
        if($isGroup) {
            $builder->groupby('hoo.serial_no');
        }

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 根据规则查询订单数据
     * @param array $manage_store_ids
     * @return array
     */
    public function getOrderShiftList(array $manage_store_ids,$params=[]): array
    {

        /**
         * 1. 上一考勤周期的考勤计算时间<进入页面时间，所属班次取值范围为：上个考勤周期开始时间<订单开始时间<=进入页面时间+24h
         * 如图所示：9号3:00pm进入页面，属于9号~10号的考勤周期内，且9号3:00pm<10号3:00am，
         * 因此所取订单对应的班次开始时间应大于上周期的考勤开始时间，
         * 即8号 12:00pm，同时小于进入页面时间后24小时，即10号 3:00pm。
         * 最终生效范围为：8号 12:00pm<x<=10号 3:00pm。
         *
         * 2. 上一考勤周期的考勤计算时间>=进入页面时间，所属班次取值范围为：当前考勤周期开始时间<订单开始时间<=进入页面时间+24h
         * 如图所示：10号8:00am进入页面，属于9号~10号的考勤周期内，且10号8:00am>10号3:00am，
         * 因此所取订单对应的班次开始时间应大于本周期的考勤开始时间，即9号 12:00pm，
         * 同时小于进入页面时间后24小时，即11号 8:00am。
         * 最终生效范围为：9号 12:00pm<x<=11号 8:00am。
         */

        // 当前日期和时间
        $cur_date_time = time();//date("Y-m-d H:i:s",time());
        // 固定考勤时间
        $attendance_time = date('Y-m-d 12:00:00', $cur_date_time);

        // 进入页面的时间 如果小于中午12点，当前考勤周期属于昨天，>= 12点 考勤周期属于当日
        if ($cur_date_time < strtotime($attendance_time)) {
            $attendance_time = date('Y-m-d 12:00:00', strtotime('-1 day', $cur_date_time));
        }

        // 获取订单的左侧时间边界为永远为上一个考勤的中午12点开始，这里永远减去1天
        $b_time = strtotime('-1 day', strtotime($attendance_time));
        // 获取订单的右侧时间边界为永远为当前时间+24小时
        $e_time = strtotime('+1 day', $cur_date_time);

        // 生效时间提前半小时
        $params['b_time'] = date('Y-m-d H:i:s', strtotime('-1800 seconds', $b_time));
        $params['e_time'] = date('Y-m-d H:i:s', strtotime('-1800 seconds', $e_time));
        $params['store_ids'] = $manage_store_ids;
        $list = $this->getOutsourcingOrder($params, true);
        if (empty($list)) {
            return [];
        }

        /**
         * 雇佣日期相同、班次相同、网点，做合并
         * 雇佣日期不同，班次相同，不做合并
         */
        $order_list = [];
        foreach ($list as $key => $val) {
            $_idx = $val['employment_date'].'_'.$val['shift_id'].'_'.$val['store_id'];
            if (!isset($order_list[$_idx]['order_serial_no'])) {
                $order_list[$_idx]['order_serial_no'][] = $val['order_serial_no'];
            }

            if (isset($order_list[$_idx]['order_serial_no']) && !in_array($val['order_serial_no'],
                    $order_list[$_idx]['order_serial_no'])) {
                $order_list[$_idx]['order_serial_no'][] = $val['order_serial_no'];
            }

            $order_list[$_idx]['employment_date'] = $val['employment_date'];
            $order_list[$_idx]['shift_begin_time']= $val['shift_begin_time'];
            $order_list[$_idx]['shift_end_time']  = $val['shift_end_time'];
            $order_list[$_idx]['shift_info']      = $val['shift_begin_time'].'~'.$val['shift_end_time'];
            $order_list[$_idx]['store_id']        = $val['store_id'];

            $staffNumber = $val['staff_numbers'] > $val['final_audit_num'] ? $val['final_audit_num'] : $val['staff_numbers'];

            if (!isset($order_list[$_idx]['staff_numbers'])) {
                $order_list[$_idx]['staff_numbers'] = $staffNumber;
            } else {
                $order_list[$_idx]['staff_numbers'] = $order_list[$_idx]['staff_numbers'] + $staffNumber;
            }
            $order_list[$_idx]['shift_id']  = $val['shift_id'];
            $order_list[$_idx]['unique_key']  = $key;
        }

        return array_values($order_list);
    }

    //****************************************************************************************

    /**
     * BY 提交外协员工加班申请单 此时不创建审批流，向业务表插入数据，想osm发送push
     * @return void
     */
    public function add(array $params, array $userInfo)
    {
        // 网点id
        $store_id = $this->processingDefault($params, 'store_id');
        // 加班开始时间 前端提交的格式：2023-03-07 05:00 变为 2023-03-07 05:00:00
        $ot_start_time = date("Y-m-d H:i:s",strtotime($this->processingDefault($params, 'ot_start_time')));
        // 加班时长
        $duration = $this->processingDefault($params, 'duration');
        // 加班人数
        $demand_num = $this->processingDefault($params, 'demand_num', 2);
        // 外协公司id
        $outsourcing_company_id = $this->processingDefault($params, 'outsourcing_company_id', 2);
        // 加班原因
        $reason = $this->processingDefault($params, 'reason');
        // 加班证明图片
        $img = $this->processingDefault($params, 'img', 3);

        // 1、验证前端传递的 网点和 外协公司id是否合法
        $store_info = (new SysStoreServer())->getStoreInfoByid($store_id);
        if (empty($store_info)) {
            // 不存在此网点
            $this->getDI()->get('logger')->write_log(__CLASS__." add-getStoreInfoByid".json_encode(['store_id' => $store_id],
                    JSON_UNESCAPED_UNICODE), 'info');
            return $this->checkReturn(-3, $this->getTranslation()->_('4010'));
        }


        $out_company_info = (new OsStaffServer($this->lang,
            $this->timeZone))->getOutCompanyInfoOne($outsourcing_company_id);
        if (empty($out_company_info)) {
            // 不存在此外协公司
            $this->getDI()->get('logger')->write_log(__CLASS__." add-getOutCompanyInfoOne:".json_encode(['company_id' => $outsourcing_company_id],
                    JSON_UNESCAPED_UNICODE), 'info');
            return $this->checkReturn(-3, $this->getTranslation()->_('outsourcing_company_empty'));
        }

        $device_list = OutsourcingCompanyDeviceTokenModel::find([
            'conditions' => ' company_id = :company_id:',
            'bind'       => ['company_id' => $outsourcing_company_id],
            'columns'    => 'accept_language,device_token,device_type,os',
        ])->toArray();

        // todo 计算考勤日期由脚本生成
        //$attendance_date = $this->getAttendanceDateByOtStartTime($ot_start_time);
        // 加班日期
        $ot_date = date('Y-m-d', strtotime($ot_start_time));
        // 加班结束日期
        $minute   = $this->getMinuteByDuration($duration);
        $end_time = date('Y-m-d H:i:s', strtotime("+".$minute." minute", strtotime($ot_start_time)));

        // 2、组织插入数据
        $cur_date    = date('Y-m-d');
        $insert_data = [
            // 网点id
            'store_id'               => trim($store_id),
            // 考勤日期,需要根据选择的加班时间进行计算
            //'attendance_date'        => $attendance_date,
            // 加班日期
            'ot_date'                => $ot_date,
            // 加班开始时间
            'start_time'             => $ot_start_time,
            // 加班结束时间
            'end_time'               => $end_time,
            // 加班时长
            'duration'               => trim($duration),
            // 外协公司id
            'outsourcing_company_id' => $outsourcing_company_id,
            // 加班原因
            'reason'                 => trim($reason),
            // 加班证明url地址
            'img'                    => json_encode($img, JSON_UNESCAPED_UNICODE),
            // 申请加班人数
            'demand_num'             => $demand_num,
            // 申请人工号
            'apply_staff_id'         => $userInfo['staff_id'],
            // 是否推送,1=已推送，0=未推送
            'is_push'                => 1,
            // 申请日期
            'date_at'                => $cur_date,
        ];
        $last_id     = (new HubOutsourcingOvertimeRepository($this->timeZone))->addOutsourcingOT($insert_data);
        // 向osm发送push

        if (!empty($device_list)) {
            $all_send_data = [];
            foreach ($device_list as $oneDevice) {
                $send_data['message_title']   = $this->getTranslation($oneDevice['accept_language'])->_('push_osm_outsourcing_ot_title');
                $send_data['message_content'] = $this->getTranslation($oneDevice['accept_language'])->_('push_osm_outsourcing_ot_content');
                $send_data['device_token']    = $oneDevice['device_token'];
                $send_data['device_type']     = $oneDevice['device_type'];
                $send_data['os']              = $oneDevice['os'];
                $send_data['src']             = 'osm';
                $send_data['message_scheme']  = 'osm://fe/page?path=otList';
                $all_send_data[]              = $send_data;
            }
            $post_data   = [
                'list' => $all_send_data,
            ];
            (new PublicRepository($this->timeZone))->sendPushToOsmCompany($post_data);
        }

        return $this->checkReturn(['data' => ['outsourcing_overtime_id' => $last_id]]);
    }


    /**
     * osm 配置外协加班人员 提交加班审批单
     * @param array $params
     * @return array
     * @throws ValidationException
     */
    public function submitOutsourcingOT(array $params): array
    {
        // 雇佣日期
        $ot_date = $employment_date = $this->processingDefault($params, 'employment_date');
        // 班次id
        $shift_id = $this->processingDefault($params, 'shift_id');

        $shift_info  = HrShiftModel::findFirst(intval($shift_id));
        $shift_start = empty($shift_info) ? '' : $shift_info->start;
        $shift_end   = empty($shift_info) ? '' : $shift_info->end;
        $start_time      = $employment_date.' '.$shift_start.':00';
        $end_time        = $employment_date.' '.$shift_end.':00';
        if (strtotime($end_time) < strtotime($start_time)) {
            // 存在跨天 雇佣日期 + 1天
            $employment_date = date('Y-m-d', strtotime("{$employment_date} +1 day"));
        }
        // 班次结束时间
        $shift_end_time = $this->processingDefault($params, 'shift_end_time');
        // 加班开始时间 前端提交的格式：2023-03-07 05:00 变为 2023-03-07 05:00:00
        $shift_end_time = $employment_date.' '.$shift_end_time.':00';

        //加班开始时间
        $ot_start_time = $this->processingDefault($params, 'ot_start_time');
        // 加班开始时间 前端提交的格式：2023-03-07 05:00 变为 2023-03-07 05:00:00
        $ot_start_time = $ot_start_time.':00';
        //班次结束时间 距离 加班开始时间 相差不能 大于 5小时。
        $time = strtotime($ot_start_time) - strtotime($shift_end_time);
        if($time > 60 * 60 * 5) {
            throw new ValidationException($this->getTranslation()->_('ot_start_time_limit'));
        }

        if($time < 0) {
            throw new ValidationException($this->getTranslation()->_('ot_start_limit'));
        }

        // 网点id
        $store_id = $this->processingDefault($params, 'store_id');
        // 加班时长
        $duration = $this->processingDefault($params, 'duration');
        // 加班原因
        $reason = $this->processingDefault($params, 'reason');
        // 加班证明图片
        $img = $this->processingDefault($params, 'img', 3);

        $demand_num = $this->processingDefault($params, 'demand_num', 2);

        //外协订单 编号
        $order_serial_nos = $this->processingDefault($params, 'order_serial_no', 3);

        // 根据用户选择的时长 计算出加班结束时间
        $minute      = $this->getMinuteByDuration($duration);
        $ot_end_time = date('Y-m-d H:i:s', strtotime("+".$minute." minute", strtotime($ot_start_time)));
        $userInfo    = $params['user_info'];

        // 登入者id
        $apply_staff_id = $userInfo['staff_id'];

        $where['store_id'] = $store_id;
        $where['ot_date']  = $ot_date;
        $where['shift_id'] = $shift_id;
        $otList = HubOutsourcingOvertimeRepository::getOutsourcingOTList($where);
        if(!empty($otList)) {
            throw new ValidationException($this->getTranslation()->_('isset_order_shift'));
        }

        //加班人数，不能超过所选择的班次订单人数
        $orderStaffInfo = $this->getOutsourcingOrder(['order_serial_no' => $order_serial_nos], true);
        // 修改后的人数，比订单配置的人数少的话，取修改后的订单人数。
        $finalAuditNum = 0;
        foreach ($orderStaffInfo as $oneOrder) {
            if($oneOrder['staff_numbers'] >= $oneOrder['final_audit_num']) {
                $finalAuditNum = $finalAuditNum + $oneOrder['final_audit_num'];
            } else {
                $finalAuditNum = $finalAuditNum + $oneOrder['staff_numbers'];
            }
        }

        if($demand_num > $finalAuditNum) {
            throw new ValidationException($this->getTranslation()->_('demand_num_limit'));
        }
        $duration = trim($duration);
        if(!isCountry()) {
            //【17885】【TH | BY HCM】HUB外协审批流调整与临时订单  (去掉 休息时间 半小时)
            // my 需要减
            $duration = $duration - 0.5;
        }

        $serialNo = $this->getID();
        // db 操作
        $db = $this->getDI()->get('db');
        try {
            $db->begin();

            // 1、组织插入数据
            $cur_date    = date('Y-m-d');
            $insert_data = [
                // 网点id
                'store_id'       => trim($store_id),
                // 加班日期
                'ot_date'        => $ot_date, //$ot_date,
                // 加班开始时间
                'start_time'     => $ot_start_time,
                // 加班结束时间
                'end_time'       => $ot_end_time,
                // 加班时长
                'duration'       => $duration,
                // 加班原因
                'reason'         => trim($reason),
                // 加班证明url地址
                'img'            => json_encode($img, JSON_UNESCAPED_UNICODE),
                // 申请加班人数
                'demand_num'     => $demand_num,
                // 申请人工号
                'apply_staff_id' => $userInfo['staff_id'],
                // 是否推送,1=已推送，0=未推送
                'is_push'        => 1,
                // 申请日期
                'date_at'        => $cur_date,
                // 待审批
                'apply_state'    => enums::APPROVAL_STATUS_PENDING,
                'serial_no'      => !empty($serialNo) ? trim($serialNo) : '',
                'shift_id'       => trim($shift_id),
                // osm 去掉待处理页签，审批流创建时的状态为审核中
                'osm_state'      => HubOutsourcingOvertimeModel::OSM_STATE_AUDIT
            ];
            $ot_id       = (new HubOutsourcingOvertimeRepository($this->timeZone))->addOutsourcingOT($insert_data);

            //生成 hub 加班 与 外协订单关联关系
            $allOverTimeOrder = [];
            foreach ($order_serial_nos as $oneOrder) {
                $overTimeOrder['hub_outsourcing_overtime_id'] = $ot_id;
                $overTimeOrder['serial_no'] = !empty($serialNo) ? trim($serialNo) : '';
                $overTimeOrder['outsourcing_order_serial_no'] = $oneOrder;
                $allOverTimeOrder[] = $overTimeOrder;
            }

            $model = new HubOutsourcingOvertimeOrderModel;
            $model->batch_insert($allOverTimeOrder, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);

            // 3.创建审批流
            $extend = [
                'store_id'     => $store_id,
                'AM_store_ids' => [$store_id],
                'from_submit'  => [
                    'sys_store_id'  => $store_id,
                ],
            ];
            //创建
            $server    = new ApprovalServer($this->lang, $this->timeZone);
            $requestId = $server->create($ot_id, AuditListEnums::APPROVAL_TYPE_OUTSOURCING_OT, $apply_staff_id, null, $extend);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $this->logger->write_log("add_outsourcingOvertime 审批流创建异常 {$apply_staff_id}".$e->getMessage());
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }
        return $this->checkReturn(['data' => ['outsourcing_overtime_id' => $ot_id]]);
    }

    /**
     * 外协员工加班审批【同意|拒绝】
     * @param array $params
     * @return array
     * @throws Exception
     */
    public function updateOutsourcingOT(array $params): array
    {
        $staff_id      = $this->processingDefault($params, 'staff_id', 2);
        $ot_id         = $this->processingDefault($params, 'audit_id', 2);
        $reject_reason = $this->processingDefault($params, 'reject_reason');
        $state         = $this->processingDefault($params, 'status', 2);

        $outsourcing_ot_info = (new HubOutsourcingOvertimeRepository($this->timeZone))->getOutsourcingOTById($ot_id);
        if (empty($outsourcing_ot_info)) {
            // 未找到有效外协加班申请
            return $this->checkReturn(-3, $this->getTranslation()->_('os_ot_data_empty'));
        }

        $server = new ApprovalServer($this->lang, $this->timeZone);
        //同意
        if ($state == enums::$audit_status['approved']) {
            $server->approval($ot_id, AuditListEnums::APPROVAL_TYPE_OUTSOURCING_OT, $staff_id);
        }

        //驳回
        if ($state == enums::$audit_status['dismissed']) {
            $server->reject($ot_id, AuditListEnums::APPROVAL_TYPE_OUTSOURCING_OT, $reject_reason, $staff_id);
        }
        return $this->checkReturn(['data' => ['audit_id' => $ot_id]]);
    }

    /**
     * 外协加班申请 撤销操作
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function cancelOutsourcingOT($params)
    {
        $staff_id      = $this->processingDefault($params, 'staff_id', 2);
        $ot_id         = $this->processingDefault($params, 'audit_id', 2);
        $cancel_reason = $this->processingDefault($params, 'cancel_reason');

        // 逻辑验证：
        $outsourcing_ot_info = (new HubOutsourcingOvertimeRepository($this->timeZone))->getOutsourcingOTById($ot_id);
        if (empty($outsourcing_ot_info)) {
            // 未找到有效外协加班申请
            return $this->checkReturn(-3, $this->getTranslation()->_('os_ot_data_empty'));
        }

        // 如果已经是驳回状态
        if ($outsourcing_ot_info['apply_state'] == enums::APPROVAL_STATUS_CANCEL) {
            return $this->checkReturn(['data' => ['audit_id' => $ot_id]]);
        }
        $server = new ApprovalServer($this->lang, $this->timeZone);
        // 当前工单属于当前登入人时
        if ($outsourcing_ot_info['apply_staff_id'] == $staff_id) {
            $server->cancel($ot_id, AuditListEnums::APPROVAL_TYPE_OUTSOURCING_OT, $cancel_reason, $staff_id);
        } else {
            $server->approvalCancel($ot_id, AuditListEnums::APPROVAL_TYPE_OUTSOURCING_OT, $cancel_reason, $staff_id);
        }
        return $this->checkReturn(['data' => ['audit_id' => $ot_id]]);
    }

    /**
     * 获取外协员工信息
     * @param array $params
     * @return array
     */
    public function getOutsourcingOTStaffInfo(array $params): array
    {
        $ot_id = $this->processingDefault($params, 'audit_id', 2);

        // 返回数据格式
        $data                = [
            'data' => [
                'list' => [],
            ],
        ];
        $outsourcing_ot_info = (new HubOutsourcingOvertimeRepository($this->timeZone))->getOutsourcingOTById($ot_id);
        if (empty($outsourcing_ot_info)) {
            // 未找到有效外协加班申请
            return $this->checkReturn($data);
        }

        $outsourcing_ot_detail = (new HubOutsourcingOvertimeDetailRepository($this->timeZone))->getOutsourcingOTDetailByOtId($ot_id);
        $ot_staff_ids          = array_values(array_column($outsourcing_ot_detail, 'staff_id'));

        $ot_staff_list = HrStaffInfoServer::getUserInfoByStaffInfoIds($ot_staff_ids, 'staff_info_id,name');
        if (empty($ot_staff_list)) {
            return $this->checkReturn($data);
        }

        foreach ($ot_staff_list as $key => $val) {
            $data['data']['list'][] = [
                'staff_id' => (int)$val['staff_info_id'],
                'name'     => !empty($val['name']) ? trim($val['name']) : "",
            ];
        }
        return $this->checkReturn($data);
    }

}