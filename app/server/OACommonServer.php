<?php
/**
 * OA相关核心业务
 */
namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\PushEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

class OACommonServer extends BaseServer
{
    public $timezone;

    /**
     * OACommonAuditServer constructor.
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang, $timezone);
    }



    /**
     * 获取红点数量
     * @return int
     */
    public function getOaFolderNum($parmas)
    {
        $staffInfoId = $parmas['staff_info_id'] ?? 0;
        $jobTitle    = $parmas['job_title'] ?? 0;
        $formal      = $parmas['formal'] ?? 0;
        $src         = $parmas['src'] ?? PushEnums::OA_REDDOT_PUSH_SRC_APP;

        //检索是否开启oa folder 的数量权限
        if(!$staffInfoId || !(new SettingEnvServer)->getSetVal('oa_folder_num_show_switch')) {
            return 0;
        }

        if ($formal != HrStaffInfoModel::FORMAL_1) {
            return 0;
        }

        //检索用户是否有菜单权限
        if (!(new AuditServer($this->lang, $this->timezone))->OAFolderPermissionForJobTitleConfig($jobTitle)) {
            return 0;
        }

        try {
            $oaRpc = (new ApiClient('oa_rpc', '', 'get_user_oa_reddot_count', $this->lang));
            $oaRpc->setParams([
                'staff_id' => $staffInfoId,
                'src'      => $src,
            ]);
            $res = $oaRpc->execute();

            if (!empty($res['result']) && $res['result']['code'] == ErrCode::SUCCESS) {
                return (int) $res['result']['data']['total_count'] ?? 0;
            }

        } catch (Exception $exception) {
            $this->logger->write_log('getOaFolderNum error ' . $exception->getMessage() .'-'. $exception->getTraceAsString() . $exception->getLine(), 'error');
        }

        return 0;
    }

    /**
     * 获取报销申请红点数量
     * @return int
     */
    public function getReimbursementApplyNum($parmas)
    {
        $staffInfoId = $parmas['staff_info_id'] ?? 0;
        $formal      = $parmas['formal'] ?? 0;
        $hire_type   = $parmas['hire_type'] ?? 0;
        $src         = $parmas['src'] ?? PushEnums::OA_REDDOT_PUSH_SRC_APP;

        if (!isCountry('TH')) {
            return 0;
        }

        if (empty($staffInfoId)) {
            return 0;
        }

        if ($formal != HrStaffInfoModel::FORMAL_1 || $hire_type == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return 0;
        }

        try {
            $oaRpc = (new ApiClient('oa_rpc', '', 'get_reimbursement_apply_pending', $this->lang));
            $oaRpc->setParams([
                'staff_id' => $staffInfoId,
                'src'      => $src,
            ]);
            $res = $oaRpc->execute();

            if (!empty($res['result']) && $res['result']['code'] == ErrCode::SUCCESS) {
                return (int)$res['result']['data']['total_count'] ?? 0;
            }
        } catch (\Exception $exception) {
            $this->logger->write_log([
                'getReimbursementApplyNumError' => $exception->getMessage() . '-' . $exception->getTraceAsString() . $exception->getLine(),
                'staff_params'                  => func_get_args(),
            ], 'notice');
        }

        return 0;
    }
}
