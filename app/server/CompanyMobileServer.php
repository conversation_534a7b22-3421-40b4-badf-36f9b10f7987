<?php

namespace FlashExpress\bi\App\Server;


use Exception;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\CompanyMobileFeedbackModel;
use FlashExpress\bi\App\Models\backyard\CompanyMobileManageLogModel;
use FlashExpress\bi\App\Models\backyard\CompanyMobileManageModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SimCardLogModel;
use FlashExpress\bi\App\Models\backyard\SimCardModel;


class CompanyMobileServer extends BaseServer
{


    /**
     * 获取员工是短信套餐的企业号码
     * @param $staff_info_id
     * @return array
     */
    public function staffSMSCompanyMobile($staff_info_id): array
    {
        $result            = [
            'list' => [],
        ];
        $companyMobileInfo = CompanyMobileManageModel::find(
            [
                'conditions' => 'staff_info_id = :staff_info_id: and status = :status: and update_sim_status = :update_sim_status: and sim_company = :sim_company:',
                'bind'       => [
                    'staff_info_id'     => $staff_info_id,
                    'status'            => CompanyMobileManageModel::STATUS_ACTIVE,
                    'update_sim_status' => CompanyMobileManageModel::UPDATE_SIM_STATUS_UPDATED,
                    'sim_company'       => SimCardModel::SIM_COMPANY_TRUE,
                ],
            ]
        )->toArray();
        if (empty($companyMobileInfo)) {
            return $result;
        }
        $result['list'] = array_column($companyMobileInfo, 'phone_number');
        return $result;
    }



    /**
     * 获取员工企业号码信息
     * @param $staff_info_id
     * @return array
     */
    public function mobileAndSimCardInfo($staff_info_id): array
    {
        $result            = [
            'is_true_operator' => false,
        ];
        $companyMobileInfo = CompanyMobileManageModel::findFirst(
            [
                'conditions' => 'staff_info_id = :staff_info_id: and status = :status: and update_sim_status = :update_sim_status: and sim_company = :sim_company:',
                'bind'       => [
                    'staff_info_id'     => $staff_info_id,
                    'status'            => CompanyMobileManageModel::STATUS_ACTIVE,
                    'update_sim_status' => CompanyMobileManageModel::UPDATE_SIM_STATUS_UPDATED,
                    'sim_company'       => SimCardModel::SIM_COMPANY_TRUE,
                ],
            ]
        );
        if (empty($companyMobileInfo)) {
            return $result;
        }
        $result['is_true_operator'] = true;
        return $result;
    }


    /**
     * 获取员工 企业号码情况
     * @param $staff_info_id
     * @return array
     * @throws ValidationException
     */
    public function mobileInfo($staff_info_id): array
    {
        $result = (new StaffServer())->getStaffById($staff_info_id,
            'mobile_company as phone_number,is_sub_staff,hire_type,formal,node_department_id,name,staff_info_id,sys_store_id');
        //是否是公司号码
        $result['is_company']  = false;
        $result['sim_card_id'] = 0;
        [$result['phone_number'], $is_edit_company_mobile] = PersoninfoServer::isEditCompanyMobile($result['formal'],
            $result['is_sub_staff'], $result['hire_type'], $result['phone_number']);
        if (!$is_edit_company_mobile) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        unset($result['is_sub_staff'], $result['hire_type'], $result['formal']);

        if ($result['phone_number']) {
            $companyMobileInfo = CompanyMobileManageModel::findFirstByPhoneNumber($result['phone_number']);
            if (!empty($companyMobileInfo)) {
                $result['is_company']  = true;
                $result['sim_card_id'] = intval($companyMobileInfo->sim_card_id);
            }
        }

        return $result;
    }

    /**
     * 企业号码异常翻译类型枚举
     * @return array[]
     */
    public function getFeedBackCategory(): array
    {
        $t = $this->getTranslation();
        return [
            [
                'label' => $t->_('feedback_category_1'),
                'value' => '1',
            ],
            [
                'label' => $t->_('feedback_category_2'),
                'value' => '2',
            ],
            [
                'label' => $t->_('feedback_category_99'),
                'value' => '99',
            ],
        ];
    }



    /**
     * 获取员工可用的套餐
     * @param $staff_info_id
     * @return array
     */
    public function getStaffEnablePackage($staff_info_id): array
    {
        $staffInfo = (new StaffServer())->getStaffById($staff_info_id, 'staff_info_id,sys_store_id');

        $configList = (new SettingEnvServer())->getMultiEnvByCode([
            'staff_phone_number_package',
            'head_office_phone_number_package',
            'store_phone_number_package',
        ]);

        if (empty($configList)) {
            return [];
        }
        //默认是支持员工自行申请的SIM卡套餐
        $config = array_filter(explode(',', $configList['staff_phone_number_package']));
        //总部的员工则优先用总部配置的名单
        if ($staffInfo['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            if (!empty($configList['head_office_phone_number_package'])) {
                $tmp_config = array_filter(explode(',', $configList['head_office_phone_number_package']));
            }
        } else {
            if (!empty($configList['store_phone_number_package']) && is_json($configList['store_phone_number_package'])) {
                $tmp = json_decode($configList['store_phone_number_package'],
                    true)[$staffInfo['sys_store_id']] ?? '';
                if ($tmp) {
                    $tmp_config = array_filter(explode(',', $tmp));
                    //V21255 当前登录用户不是总部员工-所属网点是特殊网点，则企业号码的套餐需是“特殊网点所能选择到的手机号套餐”或“支持员工自行申请的SIM卡套餐”
                    $tmp_config = array_unique(array_merge($tmp_config, $config));
                }
            }
        }
        if (!empty($tmp_config)) {
            $config = $tmp_config;
        }
        $this->logger->write_log(['staff_id' => $staffInfo, 'configList' => $configList, 'config' => $config], 'info');
        return $config;
    }

    /**
     * 企业号码是否可编辑
     * @param array $staff_info 当前登陆者信息组
     * @return bool
     * @throws Exception
     */
    public function checkCompanyMobileCanEdit($staff_info): bool
    {
        try {
            //不可自行申请使用公司企业号码的职位ID
            $job_ids = (new SettingEnvServer())->getSetVal('staff_unable_apply_company_mobile_job', ',');
            if (in_array($staff_info['job_title'], $job_ids)) {
                throw new ValidationException($this->getTranslation()->_('21255_by_error_message_001'));
            }
            return true;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 获取企业号码池用于员工选择
     * @param string $sim_card_number SIM序号
     * @param integer $staff_info_id 员工工号
     * @return array
     */
    public function getCompanyMobileList($sim_card_number, $staff_info_id): array
    {
        //SIM序号必须是存在且可用的;如果用户输入的SIM卡不是公司序列号池的值，企业号码返回的值是空的列表
        $simCardInfo = SimCardModel::findFirst([
            'conditions' => 'number = :sim_card_number: and status = :status:',
            'bind'       => [
                'sim_card_number' => $sim_card_number,
                'status' => SimCardModel::STATUS_AVAILABLE
            ]
        ]);
        if (empty($simCardInfo)) {
            return [];
        }

        //获取员工可用的套餐
        $config = $this->getStaffEnablePackage($staff_info_id);
        if (empty($config)) {
            return [];
        }

        $columns         = 'phone_number';
        $condition       = 'package in ({package:array}) and staff_info_id is null and status = :status: and sim_company = :sim_company:';
        $bind['status']  = CompanyMobileManageModel::STATUS_NOT_ENABLED;
        $bind['package'] = array_values($config);
        $bind['sim_company'] = $simCardInfo->sim_company;

        return CompanyMobileManageModel::find([
            'columns'    => $columns,
            'conditions' => $condition,
            'bind'       => $bind,
            'order'      => 'RAND()',
            'limit'      => 50,
        ])->toArray();
    }

    /**
     * 绑定企业号和sim卡序列号
     * @param $staff_info_id
     * @param $phone_number
     * @param $sim_card_number
     * @return bool
     * @throws Exception
     */
    public function save($staff_info_id, $phone_number, $sim_card_number): bool
    {
        $t  = $this->getTranslation();
        $db = SimCardModel::beginTransaction($this);

        try {
            $simCardModelInput = SimCardModel::findFirst([
                'conditions' => 'number = :sim_card_number: ',
                'bind'       => [
                    'sim_card_number' => $sim_card_number,
                ],
                'for_update' => true,
            ]);
            //您填写的SIM序号不是公司序号，请联系资产部！
            if (empty($simCardModelInput)) {
                throw new ValidationException($t->_('21255_by_error_message_002'));
            }

            //SIM序号，必填，只能输入18位数字或者13位数字，且只能输入SIM序列池中，使用状态为“可用”的序列号
            if ($simCardModelInput->status != SimCardModel::STATUS_AVAILABLE) {
                throw new ValidationException($t->_('20211_by_error_message_001'));
            }

            $companyMobileModelInput = CompanyMobileManageModel::findFirst([
                'conditions' => 'phone_number = :phone_number:',
                'bind'       => ['phone_number' => $phone_number],
                'for_update' => true,
            ]);
            //手机号码不存在，请重新选择
            if (empty($companyMobileModelInput)) {
                throw new ValidationException($t->_('20211_by_error_message_003'));
            }

            //SIM卡已经填写过，请更换！
            if ($companyMobileModelInput->sim_card_id == $simCardModelInput->id) {
                throw new ValidationException($t->_('20211_by_error_message_005'));
            }

            //V21255校验SIM手机号的运营商和SIM序列号的运营商是否一致，如果不一致，则提示：手机号和序列号的运营商不一致，请联系资产部！
            if ($companyMobileModelInput->sim_company != $simCardModelInput->sim_company) {
                throw new ValidationException($t->_('21255_by_error_message_003'));
            }

            //提交的手机号套餐不在其可选范围
            if (!in_array($companyMobileModelInput->package, $this->getStaffEnablePackage($staff_info_id))) {
                throw new ValidationException($t->_('20211_by_error_message_006'));
            }

            $checkMobileCompany = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id != :staff_info_id: and mobile_company = :mobile_company: and formal in ({formal:array}) and state in ({state:array}) and is_sub_staff = :is_sub_staff:',
                'bind'       => [
                    'staff_info_id'  => $staff_info_id,
                    'mobile_company' => $phone_number,
                    'formal'         => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                    'state'          => [
                        HrStaffInfoModel::STATE_ON_JOB,
                        HrStaffInfoModel::STATE_SUSPENSION,
                    ],
                    'is_sub_staff'   => 0,

                ],
            ]);
            //填写的企业号码和其他人重复，请重新选择！
            if ($checkMobileCompany) {
                throw new ValidationException($t->_('20211_by_error_message_002'));
            }
            $currentMobileInfo = $this->mobileInfo($staff_info_id);

            $currentSimCardNumber = '';
            if ($currentMobileInfo['sim_card_id']) {
                $simCardModel = SimCardModel::findFirst($currentMobileInfo['sim_card_id']);
                if ($simCardModel) {
                    $currentSimCardNumber = $simCardModel->number;

                    $this->addSimCardLog($simCardModel->id, SimCardLogModel::TYPE_ABANDON, $currentMobileInfo,
                        ['status' => $simCardModel->status], ['status' => SimCardModel::STATUS_ABANDON]);

                    $simCardModel->status = SimCardModel::STATUS_ABANDON;
                    $simCardModel->save();
                }
            }
            //sim卡序列号没变更
            if ($currentSimCardNumber == $sim_card_number) {
                throw new ValidationException($t->_('20211_by_error_message_004'));
            }
            //企业号码操作日志
            $mobileCompanyLogBefore = [
                'update_sim_status' => $companyMobileModelInput->update_sim_status,
                'sim_card_number'   => $currentSimCardNumber,
            ];
            $mobileCompanyLogAfter  = [
                'update_sim_status' => CompanyMobileManageModel::UPDATE_SIM_STATUS_WAITING,
                'sim_card_number'   => $sim_card_number,
            ];

            $companyMobileModelInput->update_sim_status = CompanyMobileManageModel::UPDATE_SIM_STATUS_WAITING;
            $companyMobileModelInput->sim_card_id       = $simCardModelInput->id;

            //sim卡操作日志 已使用
            $this->addSimCardLog($simCardModelInput->id, SimCardLogModel::TYPE_USE, $currentMobileInfo,
                ['status' => $simCardModelInput->status], ['status' => SimCardModel::STATUS_USED]);

            $simCardModelInput->status = SimCardModel::STATUS_USED;
            $simCardModelInput->save();

            $type = CompanyMobileManageLogModel::TYPE_STAFF_UPDATE_SIM;

            // 更换手机号码的逻辑
            if (!$currentMobileInfo['is_company'] && $currentMobileInfo['phone_number'] != $phone_number) {
                if ($companyMobileModelInput->status != CompanyMobileManageModel::STATUS_NOT_ENABLED) {
                    throw new ValidationException($t->_('20211_by_error_message_003'));
                }
                $type = CompanyMobileManageLogModel::TYPE_STAFF_NEW_NUMBER;
                //企业号码操作日志 before
                $mobileCompanyLogBefore['phone_number_status'] = $companyMobileModelInput->status;
                //企业号码操作日志 after
                $mobileCompanyLogAfter['phone_number_status'] = CompanyMobileManageModel::STATUS_WAIT_ACTIVE;
                $mobileCompanyLogAfter['employee_id']         = $staff_info_id;
                $mobileCompanyLogAfter['staff_name']          = $currentMobileInfo['name'];

                $companyMobileModelInput->staff_info_id      = $staff_info_id;
                $companyMobileModelInput->status             = CompanyMobileManageModel::STATUS_WAIT_ACTIVE;

                //更新员工表 mobile_company 字段
                $attributes['mobile_company'] = $phone_number;
                $attributes['staff_info_id']  = $staff_info_id;
                $attributes['operater']       = $staff_info_id;
                $hr_rpc                       = (new ApiClient('hr_rpc', '', 'update_staff_info', $this->lang));
                $hr_rpc->setParams($attributes);
                $result = $hr_rpc->execute();
                if ($result['result']['code'] != 1) {
                    throw new ValidationException(implode('', $result['result']['msg']));
                }
            }
            $companyMobileModelInput->last_operator      = $staff_info_id;
            $companyMobileModelInput->last_operator_name = $currentMobileInfo['name'];
            $companyMobileModelInput->save();

            //手机号码操作日志
            $this->addCompanyMobileLog($companyMobileModelInput->id,
                $type, $currentMobileInfo,
                $mobileCompanyLogBefore, $mobileCompanyLogAfter);

            $db->commit();
            return true;
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }

    protected function addCompanyMobileLog($cmm_id, $type, $user, $before, $after)
    {
        $model                            = new CompanyMobileManageLogModel();
        $data['mobile_company_manage_id'] = $cmm_id;
        $data['operator']                 = $user['staff_info_id'];
        $data['operator_name']            = $user['name'];
        $data['operator_dept_id']         = $user['node_department_id'];
        $data['type']                     = $type;
        $data['before']                   = json_encode($before, JSON_UNESCAPED_UNICODE);
        $data['after']                    = json_encode($after, JSON_UNESCAPED_UNICODE);
        return $model->create($data);
    }

    protected function addSimCardLog($sim_card_id, $type, $user, $before, $after)
    {
        $model                    = new SimCardLogModel();
        $data['sim_card_id']      = $sim_card_id;
        $data['operator']         = $user['staff_info_id'];
        $data['operator_name']    = $user['name'];
        $data['operator_dept_id'] = $user['node_department_id'];
        $data['type']             = $type;
        $data['before']           = json_encode($before, JSON_UNESCAPED_UNICODE);
        $data['after']            = json_encode($after, JSON_UNESCAPED_UNICODE);
        return $model->create($data);
    }

    /**
     * @param $phone_number
     * @return bool
     */
    public function checkValidateCompanyMobile($phone_number): bool
    {
        if (empty($phone_number)) {
            return false;
        }

        $feedBackModel = CompanyMobileFeedbackModel::findFirst([
            'conditions' => 'phone_number = :phone_number: and state = :state:',
            'bind'       => ['phone_number' => $phone_number, 'state' => CompanyMobileFeedbackModel::STATE_PROCESSING],
        ]);
        if (!empty($feedBackModel)) {
            return false;
        }


        $mobile = CompanyMobileManageModel::findFirst([
            'conditions' => 'phone_number = :phone_number:',
            'bind'       => ['phone_number' => $phone_number],
        ]);
        if (empty($mobile)) {
            return true;
        }

        if (in_array($mobile->status,
            [CompanyMobileManageModel::STATUS_FEEDBACK_HANDLING, CompanyMobileManageModel::STATUS_WAIT_ACTIVE])) {
            return false;
        }
        return true;
    }

    /**
     * 企业号码异常反馈
     * @param $staff_info_id
     * @param $params
     * @return true
     * @throws ValidationException
     */
    public function feedBack($staff_info_id,$params): bool
    {
        $t = $this->getTranslation();
        $staffInfo = (new StaffServer())->getStaffById($staff_info_id);
        if (empty($staffInfo['mobile_company'])) {
            throw new ValidationException($t->_('message_no_exists'));
        }
        $mobile_company = $staffInfo['mobile_company'];

        $feedBackModel = CompanyMobileFeedbackModel::findFirst([
            'conditions' => 'phone_number = :phone_number: and state = :state:',
            'bind'       => [
                'phone_number' => $mobile_company,
                'state'        => CompanyMobileFeedbackModel::STATE_PROCESSING,
            ],
        ]);

        if (!empty($feedBackModel)) {
            //请勿重复提交
            throw new ValidationException($t->_('5202'));
        }

        $db = BackyardBaseModel::beginTransaction($this);

        try {
            $companyMobileModel = CompanyMobileManageModel::findFirst([
                'conditions' => 'phone_number = :phone_number:',
                'bind'       => ['phone_number' =>$mobile_company],
                'for_update' => true,
            ]);
            if (!empty($companyMobileModel)) {
                if($companyMobileModel->status == CompanyMobileManageModel::STATUS_FEEDBACK_HANDLING){
                    //请勿重复提交
                    throw new ValidationException($t->_('5202'));
                }

                //手机号码操作日志
                $this->addCompanyMobileLog($companyMobileModel->id,
                    CompanyMobileManageLogModel::TYPE_STAFF_FEEDBACK, $staffInfo,
                    ['phone_number_status' => $companyMobileModel->status],
                    ['phone_number_status' => CompanyMobileManageModel::STATUS_FEEDBACK_HANDLING]);
                $companyMobileModel->last_operator      = $staff_info_id;
                $companyMobileModel->last_operator_name = $staffInfo['name'];
                $companyMobileModel->status = CompanyMobileManageModel::STATUS_FEEDBACK_HANDLING;
                $companyMobileModel->save();
            }
            //加反馈记录
            $feedBackModel = new CompanyMobileFeedbackModel();
            $feedBackModel->staff_info_id = $staff_info_id;
            $feedBackModel->phone_number = $mobile_company;
            $feedBackModel->state = CompanyMobileFeedbackModel::STATE_PROCESSING;
            $feedBackModel->category = $params['category'];
            $feedBackModel->reason = $params['reason'];
            $feedBackModel->save();
            $db->commit();
        }catch (Exception $e){
            $db->rollback();
            throw $e;
        }

        //同步员工信息
        $sendData['jsonCondition']    = json_encode([
            'staff_info_id' => intval($staff_info_id),
            'name'          => $staffInfo['name'],
        ]);
        $sendData['handleType']       = RocketMQ::TAG_HR_STAFF_UPDATE;
        $sendData['shardingOrderKey'] = $staff_info_id;
        $rmq                          = new RocketMQ('update-staff-info');
        $rmq->setShardingKey($staff_info_id);
        $rmq->sendOrderlyMsg($sendData);//有序
        
        return true;
    }



}