<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use App\Library\RocketMQ;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\JobTransferConfirmEnums;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\JobTransferOperateLogModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysAttachmentModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\JobtransferRepository;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Enums\VehicleInfoEnums;


class JobtransferServer extends AuditBaseServer
{
    protected $re;
    public    $timezone;
    public    $lang;
    protected $shopCategory;
    protected $networkCategory;
    protected $hubCategory;
    protected $jobtransfer;
    protected $staff;
    protected $typeUnion;
    protected $other;
    protected $auditlist;
    protected $pub;
    protected $bulkyCategory;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->timezone = $timezone;
        $this->lang     = $lang;
        parent::__construct($this->lang);
        $this->staff       = new StaffRepository();
        $this->jobtransfer = Tools::reBuildCountryInstance((new JobtransferRepository($timezone)), [$timezone]);
        $this->other       = new OtherRepository();
        $this->auditlist   = new AuditlistRepository($this->lang, $this->timezone);
        $this->pub         = new PublicRepository($this->timezone);
        $this->hc          = new HcRepository($this->lang, $this->timezone);

        //这里是shop 部门的可选网点类型
        $shopCategory       = (new SettingEnvServer())->getSetVal('jobtransfer_shop_category');
        $this->shopCategory = empty($shopCategory) ? (UC("jobtransfer")["shop_category"] ?? "") : $shopCategory;

        //这里是network 部门的可选网点类型
        $networkCategory       = (new SettingEnvServer())->getSetVal('jobtransfer_network_category');
        $this->networkCategory = empty($networkCategory) ? (UC("jobtransfer")["network_category"] ?? "") : $networkCategory;

        //这里是hub 部门的可选网点类型
        $hubCategory       = (new SettingEnvServer())->getSetVal('jobtransfer_hub_category');
        $this->hubCategory = empty($hubCategory) ? (UC("jobtransfer")["hub_category"] ?? "") : $hubCategory;

        //这里是Network Bulky 的可选网点类型
        $bulkyCategory       = (new SettingEnvServer())->getSetVal('jobtransfer_bulky_category');
        $this->bulkyCategory = empty($bulkyCategory) ? (UC("jobtransfer")["bulky_category"] ?? "") : $bulkyCategory;

        $this->typeUnion = enums::$audit_type["TF"];
    }

    /**
     * 获取员工信息
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function getStaffInfo($paramIn = [])
    {
        $returnData = [];
        $staffId    = $paramIn["staff_id"];
        $staffInfo  = $this->staff->getStaffInfo($staffId);
        if ($staffInfo) {
            //员工状态
            if ($staffInfo["state"] == 1) {
                $staffInfo["state_name"] = $this->getTranslation()->_('jobtransfer_0021');
            } elseif ($staffInfo["state"] == 2) {
                $staffInfo["state_name"] = $this->getTranslation()->_('jobtransfer_0022');
            } elseif ($staffInfo["state"] == 3) {
                $staffInfo["state_name"] = $this->getTranslation()->_('jobtransfer_0023');
            } else {
                $staffInfo["state_name"] = "";
            }
            //所属区域
            $staffInfo["store_name"] = $staffInfo["store_name"] ?: ($staffInfo["hr_staff_sys_store_id"] == '-1' ? 'Head Office' : '');
            //公司名称
            $staffInfo["company_name"] = "Flash Express";

            $returnData = $staffInfo;
        }

        return $returnData;
    }

    /**
     * 获取转岗人信息
     * @param array $paramIn
     * @return array
     */
    public function getTransferInfo($paramIn = [])
    {
        $staffId   = $paramIn["staff_id"];
        $staffInfo = $this->hc->getTransferInfo($staffId);
        if (!$staffInfo) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
        }
        return $staffInfo;
    }

    /**
     * 添加转岗信息
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function addJobtransfer($paramIn = [])
    {
        //[1]获取参数
        $staffId            = $this->processingDefault($paramIn, 'staff_id', 2);
        $departmentId       = $this->processingDefault($paramIn, 'after_department_id', 2);
        $storeId            = $this->processingDefault($paramIn, 'after_store_id');
        $jobHandoverStaffId = $this->processingDefault($paramIn, 'job_handover_staff_id', 2);
        $jobTitle           = $this->processingDefault($paramIn, 'after_position_id', 2);
        $hcId               = $this->processingDefault($paramIn, 'hc_id', 2);
        $type               = $this->processingDefault($paramIn, 'type');
        $userinfo           = $this->processingDefault($paramIn, 'userinfo');
        $date               = $this->processingDefault($paramIn, 'after_date');
        $reason             = $this->processingDefault($paramIn, 'reason');
        $carOwner           = $this->processingDefault($paramIn, 'car_owner', 2);
        $rentalCarCteatedAt = $this->processingDefault($paramIn, 'rental_car_cteated_at', 2);
        $source  = $this->processingDefault($paramIn, 'source', 'by');
        $after_working_day_rest_type = $this->processingDefault($paramIn, 'after_working_day_rest_type', 2);

        //[2]参数校验
        //[2-1]校验员工信息
        $staffInfo = $this->getStaffInfo([
            "staff_id" => $staffId,
        ]);

        if (!$staffInfo) {
            throw new \Exception($this->getTranslation()->_('jobtransfer_0004'), enums::$ERROR_CODE['1000']);
        }

        //[2-2]校验交接员工信息
        //交接人需要在职、在编、非子账号
        //交接人不能是转岗员工自己、需要在职
        $jobHandoverStaffInfo = $this->getStaffInfo([
            "staff_id" => $jobHandoverStaffId,
        ]);
        //如果不是本网点的员工
        $jobHandoverStaffIds = [];
        if (!isset($jobHandoverStaffInfo["store_id"]) || $jobHandoverStaffInfo["store_id"] != $staffInfo["store_id"]) {
            $jobHandoverStaffIds = $this->getJobHandoverStaffId($staffInfo);
        }

        if (!$jobHandoverStaffInfo || $jobHandoverStaffInfo["state"] != enums::$service_status['incumbency'] ||
            $jobHandoverStaffInfo["formal"] != 1 || $jobHandoverStaffInfo["is_sub_staff"] != 0 ||
            ($jobHandoverStaffInfo["store_id"] != $staffInfo["store_id"] && !in_array($jobHandoverStaffId,
                    $jobHandoverStaffIds)) ||
            $jobHandoverStaffId == $staffId) {
            throw new \Exception($this->getTranslation()->_('jobtransfer_0020'), enums::$ERROR_CODE['1000']);
        }

        //[2-3]校验是否重复添加
        $_re = $this->jobtransfer->getJobtransferInfo([
            "staff_id" => $staffId,
            "state"    => 1,
        ]);
        if ($_re) {
            throw new \Exception($this->getTranslation()->_('5202'), enums::$ERROR_CODE['1000']);
        }

        $_re = $this->jobtransfer->getJobtransferInfo([
            "staff_id" => $staffId,
        ]);
        if (!empty($_re) && $_re['approval_state'] == enums::$audit_status['approved'] && $_re['state'] == enums::$job_transfer_state['to_be_transfered']) {
            throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
        }

        //获取网点信息
        $afterStoreData = $this->jobtransfer->getStoreInfo([
            "store_id" => $storeId,
        ]);

        //获取当前直线上级id
        $currentManagerId = $this->getJobtransferStaffMangerId($staffId);
        //获取当前虚线上级id
        $currentIndirectMangerId = $this->getJobtransferStaffIndirectMangerId($staffId);

        //获取转岗后直线上级
        $afterManagerId = $this->getafterManagerIdInfo($storeId);

        //获取被转岗人的薪资信息
        $staffInfoSalary = $this->staff->getStaffSalary($staffId);
        $this->getDI()->get('logger')->write_log('current salary:'.json_encode($staffInfoSalary), 'info');

        //查询当前关联的HC
        $hcInfo = HrHcModel::findFirst([
            'conditions' => 'hc_id = :hc_id:',
            'columns'    => 'surplusnumber,demandnumber,expirationdate,hc_id',
            'bind'       => ['hc_id' => $hcId],
        ]);
        if (empty($hcInfo)) {
            throw new \Exception($this->getTranslation()->_('4414'), enums::$ERROR_CODE['1000']);
        }

        $base_staff_info = $this->jobtransfer->getBaseStaffInfo($staffId);
        $before_working_day_rest_type = $base_staff_info['week_working_day'] . $base_staff_info['rest_type'];

        //添加转岗信息
        $param = [
            "staff_id"                   => $staffId ?? 0,
            "hc_id"                      => $hcId ?? "",
            "hc_expiration_date"         => $hcInfo->expirationdate,
            "type"                       => $type ?? 0,
            "current_department_id"      => $staffInfo["department_id"] ?? 0,
            "current_store_id"           => $staffInfo["store_id"] ?? "",
            "current_position_id"        => $staffInfo["job_title"] ?? 0,
            "current_indirect_manger_id" => intval($currentIndirectMangerId) ?? 0,
            "current_manager_id"         => intval($currentManagerId) ?? 0,
            "current_role_id"            => $staffInfo['position_category'] ?? 0,
            "after_manager_id"           => intval($afterManagerId) ?? 0,
            "after_department_id"        => $departmentId ?? 0,
            "after_store_id"             => $storeId ?? "",
            "after_position_id"          => $jobTitle ?? 0,
            "after_date"                 => $date ?? "",
            "reason"                     => $reason ?? "",
            "job_transfer_id"            => null,
            "job_handover_staff_id"      => $jobHandoverStaffId ?? 0,
            "submitter_id"               => $userinfo['staff_id'] ?? 0,
            "approval_state"             => enums::APPROVAL_STATUS_PENDING,
            "car_owner"                  => $carOwner ?? null,
            "rental_car_cteated_at"      => !empty($rentalCarCteatedAt) ? $rentalCarCteatedAt : null,
            "state"                      => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED,
            "serial_no"                  => $this->getRandomId(),
            "before_base_salary"         => $staffInfoSalary['base_salary'] ?? 0,
            "before_exp_allowance"       => $staffInfoSalary['exp_allowance'] ?? 0,
            "before_position_allowance"  => $staffInfoSalary['position_allowance'] ?? 0,
            "before_car_rental"          => $staffInfoSalary['car_rental'] ?? 0,
            "before_trip_payment"        => $staffInfoSalary['trip_payment'] ?? 0,
            "before_notebook_rental"     => $staffInfoSalary['notebook_rental'] ?? 0,
            "before_recommended"         => $staffInfoSalary['recommended'] ?? 0,
            "before_food_allowance"      => $staffInfoSalary['food_allowance'] ?? 0,
            "before_dangerous_area"      => $staffInfoSalary['dangerous_area'] ?? 0,
            "before_house_rental"        => $staffInfoSalary['house_rental'] ?? 0,
            "before_working_day_rest_type" => $before_working_day_rest_type,
            "after_working_day_rest_type" => $after_working_day_rest_type,
        ];

        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            //减hc
            (new HcServer($this->lang, $this->timezone))->updateHc([
                'id'            => $hcId,
                'surplusnumber' => $hcInfo->surplusnumber ?? 0,
                'demandnumber'  => $hcInfo->demandnumber ?? 0,
            ]);

            $this->getDI()->get('logger')->write_log("param ====:".json_encode($param), 'info');
            $jobTransferId = $this->jobtransfer->addJobtransfer($param);
            if (empty($jobTransferId)) {
                throw new \Exception($this->getTranslation()->_('jobtransfer_0004'));
            }

            $otherObj = new OtherRepository($this->timezone, $this->lang);
            $summary  = $this->genSummary($jobTransferId, $this->typeUnion);

            $insertAuditData[] = [
                'id_union'       => 'tf_'.$jobTransferId,
                'staff_id_union' => $userinfo["id"],
                'type_union'     => $this->typeUnion,
                'status_union'   => 101,
                'store_id'       => $userinfo["organization_type"] == 2 ? '' : $userinfo["organization_id"],
                'data'           => "",
                'table'          => 'job_transfer',
                'created_at'     => gmdate('Y-m-d H:i:s', time()),
                'origin_id'      => $jobTransferId,
                'summary'        => json_encode($summary, JSON_UNESCAPED_UNICODE),
                'approval_id'    => 0,
            ];
            $otherObj->insterUnion($insertAuditData);

            //指定子审批流
            $flowCode = $this->getWorkflowCode([
                'category' => $afterStoreData["category"],
                'source'   => $source,
            ]);

            $department        = SysDepartmentModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => [
                    'id' => $departmentId,
                ],
            ]);
            $firstDepartmentId = (new SettingEnvServer())->getSetVal('dept_hub_management_id');
            if ($department) {
                $department        = $department->toArray();
                $firstDepartmentId = explode('/', $department['ancestry_v3'])[3]; //一级部门
            }

            //添加转岗申请
            $ret = (new ApprovalServer($this->lang, $this->timezone))->create($jobTransferId, enums::$audit_type['TF'],
                $userinfo['staff_id'], null, [
                    "AM_store_ids"           => [$staffInfo["store_id"], $storeId],
                    "DM_store_ids"           => [$staffInfo["store_id"], $storeId],
                    "Head_store_ids"         => [$staffInfo["store_id"], $storeId],
                    "store_id"               => $staffInfo["store_id"],
                    "flow_code"              => $flowCode ?? 1,
                    "department_id"          => $departmentId,
                    "transfer_department_id" => $staffInfo["department_id"],
                    //转岗人对应的hrbp
                    "Array_department_id"    => [$staffInfo["department_id"], $departmentId, $firstDepartmentId],
                    //转岗前部门，转岗后部门，转岗后一级部门（由于只有hub用可写死25）
                ]);
            if ($ret === false) {
                $db->rollback();
            } else {
                $db->commit();
            }
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log('addJobtransfer:'.$e->getMessage().$e->getTraceAsString(),
                'notice');
            return $this->checkReturn(-3, $e->getMessage());
        }

        return $this->checkReturn(["id" => $jobTransferId]);
    }

    /**
     * 转岗获取hc下拉列表
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function getHcList($paramIn = [])
    {
        //[1]获取参数
        $departmentId = $paramIn["department_id"] ?? "";
        $storeId      = $paramIn["store_id"] ?? "";
        $jobTitle     = $paramIn["job_title_id"] ?? "";

        if (empty($paramIn["department_id"]) || empty($paramIn["store_id"]) || empty($paramIn['job_title_id'])) {
            return [];
        }

        //获取指定网点和部门的、转岗类型的HC申请
        $returnData = [];
        $add_hour   = $this->getDI()['config']['application']['add_hour'];
        $data       = HrHcModel::find([
            'conditions' => 'worknode_id = :store_id: and department_id = :department_id: and job_title = :job_title: and state_code = 2
                             and reason_type = 2 and expirationdate >= :expiration_date: and surplusnumber > 0  and deleted = 1',
            'bind'       => [
                'store_id'        => $storeId,
                'department_id'   => $departmentId,
                'job_title'       => $jobTitle,
                'expiration_date' => gmdate("Y-m-d", time() + $add_hour * 3600),
            ],
        ])->toArray();

        if ($data) {
            $returnData = [
                "data" => $data,
            ];
        }
        return $returnData;
    }

    /**
     * 获取车辆来源下来列表
     */
    public function getCarOwnerList($paramIn=[])
    {
        $t = $this->getTranslation();
        if (isCountry() && !empty($paramIn) && $paramIn['audit_type'] == enums::$audit_type["TF"] && !empty($paramIn['audit_id'])) {
            $transferInfo = JobTransferModel::findFirst($paramIn['audit_id']);
            if ($transferInfo->after_position_id == enums::$job_title['th_pickup_driver']) {
                return [
                    [
                        'key'   => $t->_('jobtransfer_0030'),
                        'value' => 2,
                    ],
                ];
            }
        }
        return [
            [
                'key'   => $t->_('jobtransfer_0028'),
                'value' => 1,
            ],
            [
                'key'   => $t->_('jobtransfer_0030'),
                'value' => 2,
            ],
            [
                'key'   => $t->_('jobtransfer_0029'),
                'value' => 3,
            ],

        ];
    }

    /**
     * @return array[]
     */
    public function getCarType($audit_id = 0): array
    {
        return [
            [
                'key'   => 'van',
                'value' => JobTransferEnums::CAR_TYPE_VAN,
            ],
            [
                'key'   => 'bike',
                'value' => JobTransferEnums::CAR_TYPE_BIKE,
            ],
        ];
    }

    /**
     * @description 转岗获取职位下拉列表
     * @param array $paramIn
     * @return array|array[]
     */
    public function getPositionList($paramIn = [])
    {
        $returnData             = [];
        $param['department_id'] = $paramIn['department_id'] ?? '';

        if (empty($param['department_id'])) {
            return [];
       }

        // 个人代理的邮件级高于LNT，如果为个人代理，职位下拉列表只显示个人代理职位
        $server = new StaffServer();
        if (isCountry('MY') && $server->isAgentStaff($paramIn['staff_id'])) {
            $individualContractorJobTitle = (new SettingEnvServer())->getSetVal('individual_contractor_job_title', ',');
            $param['job_title'] = !empty($individualContractorJobTitle) ? $individualContractorJobTitle: [];
        } else if(!empty($paramIn['staff_id']) && $server->isLntStaff($paramIn['staff_id'])) { //如果是LNT公司
            $lnt_recruitment_job_ids = (new SettingEnvServer())->getSetVal('lnt_recruitment_job_ids', ',');
            $param['job_title'] = !empty($lnt_recruitment_job_ids) ? $lnt_recruitment_job_ids : [];
        }

        $data = $this->jobtransfer->getPositionList($param);
        if ($data) {
            foreach ($data as $key => $value) {
                if (!empty($value['working_day_rest_type'])) {
                    $working_day_rest_type = explode(',', $value['working_day_rest_type']);
                    foreach ($working_day_rest_type as $w_k => $w_v) {
                        if ($w_v[0] == HrStaffInfoModel::WEEK_WORKING_DAY_FREE) {
                            continue;
                        }

                        $data[$key]['working_day_rest_type_items'][] =[
                            'key'   => $w_v,
                            'value' => $this->getTranslation()->_('working_day_rest_type_'.$w_v)
                        ];
                    }
                } else {
                    $data[$key]['working_day_rest_type'] = [];
                }
            }
            $returnData = [
                'data' => $data,
            ];
        }
        return $returnData;
    }

    /**
     * 转岗获取网点下拉列表
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function getStoreList($paramIn = [])
    {
        $returnData = [];
        $data       = $this->jobtransfer->getStoreList($paramIn);
        if ($data) {
            $returnData = [
                "data" => $data,
            ];
        }
        return $returnData;
    }

    /**
     * 转岗校验回款
     * @Access  public
     * @Param   array
     * @Return  bool
     */
    public function checkReceivableDetail($paramIn = [])
    {
        $param["staff_id"] = $paramIn["staff_id"] ?? "";
        $param["store_id"] = $paramIn["store_id"] ?? "";
        $param["state"]    = "0";

        $setting_code  = 1;
        $setting_model = SettingEnvModel::findFirst("code = 'hris_validate_store_receivable_bill_detail'");
        if (!empty($setting_model)) {
            $setting_code = $setting_model->set_val;
        }
        if ($setting_code == 1) {
            $data = $this->jobtransfer->getReceivableDetail($param);
            if ($data) {
                return false;
            }
        }

        return true;
    }

    /**
     * 转岗校验工作是否完成
     * @Access  public
     * @Param   array
     * @Return  bool
     */
    public function checkTicketDelivery($paramIn = [])
    {
        $param["staff_id"] = $paramIn["staff_id"] ?? "";
        $param["state"]    = 0;

        $setting_code  = 1;
        $setting_model = SettingEnvModel::findFirst("code = 'hris_validate_ticket_delivery'");
        if (!empty($setting_model)) {
            $setting_code = $setting_model->set_val;
        }
        if ($setting_code == 1) {
            $data = $this->jobtransfer->getTicketDelivery($param);
            if ($data) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取转岗网点负责人
     * @Access  public
     * @Param   array
     * @Return  bool
     */
    public function getJobtransferStoreManager($paramIn = [])
    {
        //返回
        $returnData = "";
        //参数
        $storeId = $paramIn["store_id"] ?? "";


        $storeData  = $this->jobtransfer->getStoreManager([
            "store_id" => $storeId,
        ]);
        $returnData = $storeData[0]["manager_id"] ?? "";

        return $returnData;
    }

    /**
     * 转岗-获取指定人直接上级
     * @Access  public
     * @Param   array
     * @Return  bool
     */
    public function getJobtransferStaffMangerId($staffId = "")
    {
        $returnData = "";
        if (!$staffId) {
            return $returnData;
        }
        $sql        = "select `value` from `hr_staff_items` where `staff_info_id`=" . $staffId . " AND `item`='MANGER'  ORDER BY id DESC";
        $data       = $this->getDI()->get('db_rby')->query($sql);
        $mangerId   = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (empty($mangerId)) {
            return $returnData;
        }

        $sql        = "select * from `hr_staff_info` where `staff_info_id`=" . $mangerId['value'];
        $data       = $this->getDI()->get('db_rby')->query($sql);
        $mangerInfo = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if ($mangerInfo['state'] != 1) {
            return $returnData;
        } else {
            $returnData = $mangerId["value"];
        }
        return $returnData;
    }

    /**
     * 转岗-获取指定人虚线上级
     * @Access  public
     * @Param   array
     * @Return  bool
     */
    public function getJobtransferStaffIndirectMangerId($staffId = "")
    {
        $returnData = "";
        if (!$staffId) {
            return $returnData;
        }
        $sql  = "--
        SELECT
            `value`  
        FROM
            hr_staff_items 
        WHERE
            item = 'INDIRECT_MANGER' 
            AND staff_info_id = {$staffId}";
        $obj  = $this->getDI()->get('db_rby')->query($sql);
        $data = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data["value"] ?? "";
    }

    /**
     * 转岗-更新
     * @Access  public
     * @Param   array
     * @return array
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     * @throws \Exception
     */
    public function updateJobtransfer($paramIn = [])
    {
        //[1]获取参数
        $staffId                  = $this->processingDefault($paramIn, 'staff_id', 2);
        $status                   = $this->processingDefault($paramIn, 'status', 2);//同意=2，不同意=3
        $reason                   = $this->processingDefault($paramIn, 'reject_reason', 1);
        $auditId                  = $this->processingDefault($paramIn, 'audit_id', 2);
        $reason                   = addcslashes(stripslashes($reason), "'");
        $extend                   = $this->processingDefault($paramIn, 'extend', 3);
        $baseSalary               = $this->processingDefault($paramIn, 'base_salary', 2);
        $expAllowance             = $this->processingDefault($paramIn, 'exp_allowance', 2);
        $positionAllowance        = $this->processingDefault($paramIn, 'position_allowance', 2);
        $carRental                = $this->processingDefault($paramIn, 'car_rental', 2);
        $tripPayment              = $this->processingDefault($paramIn, 'trip_payment', 2);
        $notebookRental           = $this->processingDefault($paramIn, 'notebook_rental', 2);
        $recommended              = $this->processingDefault($paramIn, 'recommended', 2);
        $foodAllowance            = $this->processingDefault($paramIn, 'food_allowance', 2);
        $dangerousArea            = $this->processingDefault($paramIn, 'dangerous_area', 2);
        $houseRental              = $this->processingDefault($paramIn, 'house_rental', 2);
        $gasolineAllowance        = $this->processingDefault($paramIn, 'gasoline_allowance', 2);
        $islandAllowance          = $this->processingDefault($paramIn, 'island_allowance', 2);
        $deminimisBenefits        = $this->processingDefault($paramIn, 'deminimis_benefits', 2);
        $performanceAllowance     = $this->processingDefault($paramIn, 'performance_allowance', 2);
        $otherNonTaxableAllowance = $this->processingDefault($paramIn, 'other_non_taxable_allowance', 2);
        $otherTaxableAllowance = $this->processingDefault($paramIn, 'other_taxable_allowance', 2);
        $roles = $this->processingDefault($paramIn, 'role_ids', 3);
        $positions = $this->processingDefault($paramIn, 'positions', 3);
        $hcExpirationDate = $this->processingDefault($paramIn, 'hc_expiration_date');
        $uploadFiles = $this->processingDefault($paramIn, 'upload_files', 3);
        $carOwner = $this->processingDefault($paramIn, 'car_owner');
        $afterDate = $this->processingDefault($paramIn, 'after_date');
        $rentalCarCteatedAt = $this->processingDefault($paramIn, 'rental_car_cteated_at');
        $auditType = $this->processingDefault($paramIn, 'audit_type', 2, enums::$audit_type['TF']);
        $phone_subsidy = $this->processingDefault($paramIn, 'phone_subsidy', 2);
        $after_working_day_rest_type = $this->processingDefault($paramIn, 'after_working_day_rest_type', 2);

        //获取详情
        $detailInfo = $this->jobtransfer->getJobtransferInfo(['id' => $auditId]);
        if (empty($detailInfo)) {
            throw new ValidationException('no valid data');
        }

        $params = [
            "after_base_salary"                 => ($baseSalary ?? 0) * 100,
            "after_exp_allowance"               => ($expAllowance ?? 0) * 100,
            "after_position_allowance"          => ($positionAllowance ?? 0) * 100,
            "after_car_rental"                  => ($carRental ?? 0) * 100,
            "after_trip_payment"                => ($tripPayment ?? 0) * 100,
            "after_notebook_rental"             => ($notebookRental ?? 0) * 100,
            "after_recommended"                 => ($recommended ?? 0) * 100,
            "after_food_allowance"              => ($foodAllowance ?? 0) * 100,
            "after_dangerous_area"              => ($dangerousArea ?? 0) * 100,
            "after_house_rental"                => ($houseRental ?? 0) * 100,
            "after_gasoline_allowance"          => ($gasolineAllowance ?? 0) * 100,
            "after_island_allowance"            => ($islandAllowance ?? 0) * 100,
            "after_deminimis_benefits"          => ($deminimisBenefits ?? 0) * 100,
            "after_performance_allowance"       => ($performanceAllowance ?? 0) * 100,
            "after_other_non_taxable_allowance" => ($otherNonTaxableAllowance ?? 0) * 100,
            "after_other_taxable_allowance"     => ($otherTaxableAllowance ?? 0) * 100,
            "after_phone_subsidy"               => ($phone_subsidy ?? 0) * 100,
        ];
        //比较薪资是否变化
        $salaryChange = $this->encodeRejectSalaryDetail($detailInfo, $params);
        if ($detailInfo['after_base_salary'] != 0 || in_array(68, $positions)) {
            if ($detailInfo['after_base_salary'] != 0
                && !in_array(68, $positions)
                && $status == enums::APPROVAL_STATUS_APPROVAL
                && $this->isSalaryChanged($detailInfo, $params)
            ) {
                throw new ValidationException($this->getTranslation()->_('4018'));
            }
            if (in_array(68, $positions)) { //只有hrbp可以更改角色
                //校验角色
                if (isset($roles) && $roles) {
                    $ac = new ApiClient('hr_rpc', '', 'department_job_title_role', $this->lang);
                    $ac->setParams(
                        [
                            "department_id" => $detailInfo['after_department_id'],
                            "job_title_id"  => $detailInfo['after_position_id'],
                        ]
                    );
                    $return = $ac->execute();
                    if (empty($roles) && isset($return["result"]) && !empty($return["result"])) { //可选角色时，没有选择角色
                        throw new \Exception($this->getTranslation()->_('please try again'),
                            enums::$ERROR_CODE['1000']);
                    }
                }

                $params              = array_merge($params, [
                    "after_role_ids"     => $roles ? implode(',', $roles) : "",
                    //转岗后角色
                    "hc_expiration_date" => $hcExpirationDate,
                    "after_date" => isset($extend['code21']) && $extend['code21'] ? $extend['code21'] : $afterDate, //兼容BY & OA审批
                    "after_working_day_rest_type" => $after_working_day_rest_type,
                ]);
                $showVehicleJobTitle = explode(',',
                    (new SettingEnvServer())->getSetVal('job_title_vehicle_type')) ?: JobTransferEnums::TRANSFER_DEFAULT_JOB_TITLE;
                if (in_array($detailInfo['after_position_id'], $showVehicleJobTitle)) {
                    if ($carOwner == 1 || $carOwner == 3) {//1：个人车辆、2：公司车辆、3：借用车辆
                        $params = array_merge($params, [
                            "car_owner" => $carOwner,
                        ]);
                    } else {
                        $params = array_merge($params, [
                            "car_owner"             => $carOwner,
                            "rental_car_cteated_at" => $rentalCarCteatedAt ?? null,
                        ]);
                    }
                }
            }
        }

        $this->getDI()->get('db')->begin();
        try {
            //插入图片
            if (!empty($uploadFiles)) {
                $insertImgData = [];

                //软删除已经添加的图片
                $images = SysAttachmentModel::find([
                    'conditions' => "oss_bucket_type = 'JOB_TRANSFER' and oss_bucket_key = :oss_bucket_key: ",
                    'bind'       => [
                        'oss_bucket_key' => $auditId,
                    ],
                ])->toArray();
                $images = array_column($images, 'object_key');

                foreach ($uploadFiles as $image) {
                    $urlInfo = parse_url(stripslashes($image));
                    if (in_array(trim($urlInfo['path'], '/'), $images)) {
                        continue;
                    }

                    if (is_array($image)) { //需要保存原文件名
                        $insertImgData[] = [
                            'id'            => $auditId,
                            'image_path'    => $image['url'],
                            'original_name' => $image['file_name'],
                        ];
                    } else {
                        $insertImgData[] = [
                            'id'         => $auditId,
                            'image_path' => $image,
                        ];
                    }
                }
                (new PublicRepository())->batchInsertImgs($insertImgData, "JOB_TRANSFER");
            }

            //获取申请的详情
            $server = new ApprovalServer($this->lang, $this->timezone);

            if (enums::APPROVAL_STATUS_APPROVAL == $status) {
                //更新工资、角色值
                $this->getDI()->get('db')->updateAsDict(
                    'job_transfer',
                    $params,
                    [
                        'conditions' => 'id ='.$auditId,
                    ]
                );
                $server->approval($auditId, $auditType, $staffId);
            } elseif (enums::APPROVAL_STATUS_REJECTED == $status) {
                //hrbp & hrbp之前的节点 & (hrbp之后的节点 & 没有修改工资信息)驳回时最终驳回
                //hrbp之后的节点的驳回，是驳回到hrbp
                if ($detailInfo['after_base_salary'] != 0
                    && !in_array(68, $positions)
                    && $this->isSalaryChanged($detailInfo, $params)
                ) { //已经添加过薪资 & 当前的审批人不是hrbp的驳回
                    //非最终驳回
                    $this->getDI()->get('db')->updateAsDict(
                        'job_transfer',
                        [
                            "is_pay_adjustment"    => 1,
                            "reject_salary_detail" => $salaryChange,
                        ],
                        [
                            'conditions' => 'id ='.$auditId,
                        ]
                    );
                    $server->rejectRollBack($auditId, $auditType, $reason, $staffId);
                } else {
                    //最终驳回
                    $server->reject($auditId, $auditType, $reason, $staffId);
                }
            }

            $this->getDI()->get('db')->commit();
        } catch (ValidationException $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log("updateJobtransfer:异常信息:".$e->getMessage().$e->getTraceAsString(),
                'info');
        }

        return $this->checkReturn(['data' => ['audit_id' => $auditId]]);
    }

    /**
     * @param $auditId
     * @param $reason
     * @param $staffId
     * @return array
     * @throws ValidationException
     *
     */
    public function cancelJobtransfer($auditId, $reason, $staffId)
    {
        //获取申请的详情
        $server = new ApprovalServer($this->lang, $this->timezone);
        //最终驳回
        $server->cancel($auditId, enums::$audit_type['TF'], $reason, $staffId);

        return $this->checkReturn(['data' => ['audit_id' => $auditId]]);
    }

    /**
     * 转岗-详细
     * @Access  public
     * @Param   array
     * @Return  bool
     */
    public function getJobtransferInfo($paramIn = [])
    {
        $id   = $this->processingDefault($paramIn, 'id', 2);
        $data = $this->jobtransfer->getJobtransferInfo([
            "id" => $id,
        ]);
        if (!$data) {
            return [];
        }
        $sysObj = new SysListRepository();
        //查询部门名称 ids
        $departmentIdsArr                = array_unique(array_merge([$data["current_department_id"]],
            [$data["after_department_id"]]));
        $departmentData                  = $sysObj->getDepartmentList([
            "ids" => $departmentIdsArr,
        ]);
        $departmentData                  = array_column($departmentData, 'name', 'id');
        $data["current_department_name"] = $departmentData[$data["current_department_id"]] ?? "";
        $data["after_department_name"]   = $departmentData[$data["after_department_id"]] ?? "";

        //查询hc
        if (!empty($data['hc_id'])) {
            $hcInfo = HrHcModel::findFirst([
                'conditions' => "hc_id = :hc_id:",
                'bind'       => [
                    'hc_id' => $data['hc_id'],
                ],
            ]);
            if (empty($hcInfo)) {
                throw new BusinessException('hc 不存在 ' . $data['hc_id']);
            }
            $hcInfo = $hcInfo->toArray();
        }

        $positionList = [$data["current_position_id"], $data["after_position_id"], $hcInfo['job_title'] ?? 0];
        $positionList = array_filter($positionList);

        //查询职位名称 ids
        $positionIdsArr                = array_values(array_unique($positionList));
        $positionData                  = $sysObj->getPositionList([
            "ids" => $positionIdsArr,
        ]);

        $positionData                  = array_column($positionData, 'name', 'id');
        $data["current_position_name"] = $positionData[$data["current_position_id"]] ?? "";
        $data["after_position_name"]   = $positionData[$data["after_position_id"]] ?? "";

        //查询网点名称 ids
        $storeIdsArr                = array_unique(array_merge([$data["current_store_id"]], [$data["after_store_id"]]));
        $storeIds                   = getIdsStr($storeIdsArr);
        $storeData                  = $sysObj->getStoreList([
            "ids" => $storeIds,
        ]);
        $storeData                  = array_column($storeData, 'name', 'id');
        $data["current_store_name"] = $storeData[$data["current_store_id"]] ?? "";
        $data["after_store_name"]   = $storeData[$data["after_store_id"]] ?? "";

        //查询员工名称 ids
        $staffIdsArr   = array_unique(array_merge([$data["staff_id"]], [$data["current_manager_id"]],
            [$data["current_indirect_manger_id"]], [$data["after_manager_id"]], [$data["job_handover_staff_id"]]));
        $staffIds      = getIdsStr($staffIdsArr);
        $staffDataList = $sysObj->getStaffList([
            "ids" => $staffIds,
        ]);
        $staffData     = array_column($staffDataList, 'name', 'id');

        //查询转岗后的角色
        $roles           = [];
        $roleTranslation = $this->getRolesTranslation();
        if (isset($data['after_role_ids']) && $data['after_role_ids']) {
            $roleInfo = RolesModel::find([
                'columns'    => "id,name,name_th,name_en",
                'conditions' => "id in ({role_id:array}) and status = 1",
                'bind'       => [
                    'role_id' => explode(',', $data['after_role_ids']),
                ],
            ])->toArray();
            foreach ($roleInfo as $role) {
                $roleName    = $this->lang == 'zh-CN' ? ($roleTranslation[$role['id']]['role_name_zh'] ?? "")
                    : ($this->lang == 'en' ? $roleTranslation[$role['id']]['role_name_en'] ?? "" : $roleTranslation[$role['id']]['role_name_th'] ?? '');
                $roles[]     = $roleName;
                $rolesName[] = ['label' => $roleName, 'value' => $role['id']];
            }
            $data["after_role_name"]     = $roles ? implode(',', $roles) : "";
            $data['after_role_name_arr'] = $rolesName ?? [];
        }

        //查询转岗前的角色
        if (isset($data['current_role_id']) && $data['current_role_id']) {
            $roleInfo = RolesModel::find([
                'columns'    => "id,name,name_th,name_en",
                'conditions' => "id in ({role_id:array}) and status = 1",
                'bind'       => [
                    'role_id' => explode(',', $data['current_role_id']),
                ],
            ])->toArray();
            foreach ($roleInfo as $role) {
                $roleName    = $this->lang == 'zh-CN' ? ($roleTranslation[$role['id']]['role_name_zh'] ?? "")
                    : ($this->lang == 'en' ? $roleTranslation[$role['id']]['role_name_en'] ?? "" : $roleTranslation[$role['id']]['role_name_th'] ?? '');
                $rolesName[] = ['label' => $roleName, 'value' => $role['id']];
            }
            $data['current_role_name_arr'] = $rolesName ?? [];
        }

        //车辆类型
        $carType     = $this->getCarOwnerList();
        $carTypeName = array_column($carType, 'key', 'value');

        //获取被转岗人入职日期
        $staffHireDataList = array_column($staffDataList, 'hire_date', 'id');

        $data["staff_name"]                   = $staffData[$data["staff_id"]] ?? "";
        $data["current_manager_name"]         = $staffData[$data["current_manager_id"]] ?? "";
        $data["current_indirect_manger_name"] = $staffData[$data["current_indirect_manger_id"]] ?? "";
        $data["after_manager_name"]           = $staffData[$data["after_manager_id"]] ?? "";
        $data["job_handover_staff_name"]      = $staffData[$data["job_handover_staff_id"]] ?? "";
        $data["hc_job_title"]                 = $hcInfo['job_title'] ?? "";
        $data["hc_job_title_name"]            = !empty($hcInfo['job_title'])? $positionData[$hcInfo['job_title']] : "";
        $data['car_owner_label']              = $carTypeName[$data['car_owner'] ?? 0] ?? '';
        $data['rental_car_cteated_at']        = $data['rental_car_cteated_at'] ?? '';
        $data['staff_hire_date']              = $staffHireDataList[$data['staff_id']] ?? '';
        $data['before_base_salary']           = $data['before_base_salary'] / 100;

        switch ($data['after_position_id']) {
            case enums::$job_title['van_courier']:
            case enums::$job_title['boat_courier']:
            case enums::$job_title['bike_courier']:
                //【基本工资/m】【租车津贴/d】【餐补/d】【危险区域津贴/m】
                $params = [
                    enums::$audit_detail_btns['input_base_salary']    => $data['after_base_salary'] / 100,
                    enums::$audit_detail_btns['input_car_rental']     => $data['after_car_rental'] / 100,
                    enums::$audit_detail_btns['input_food_allowance'] => $data['after_food_allowance'] / 100,
                    enums::$audit_detail_btns['input_dangerous_area'] => $data['after_dangerous_area'] / 100,
                ];
                break;
            case enums::$job_title['dc_officer']:
            case enums::$job_title['assistant_branch_supervisor']:
            case enums::$job_title['shop_officer']:
            case enums::$job_title['shop_cashier']:
            case enums::$job_title['hub_staff']:
            case enums::$job_title['warehouse_staff']:
            case enums::$job_title['warehouse_staff_sorter']:
            case enums::$job_title['mini_cs_officer']:
            case enums::$job_title['store_officer']:
                //【基本工资/m】【经验津贴/m】【餐补/d】【危险区域津贴/m】【电脑补贴/m】
                $params = [
                    enums::$audit_detail_btns['input_base_salary']     => $data['after_base_salary'] / 100,
                    enums::$audit_detail_btns['input_exp_allowance']   => $data['after_exp_allowance'] / 100,
                    enums::$audit_detail_btns['input_food_allowance']  => $data['after_food_allowance'] / 100,
                    enums::$audit_detail_btns['input_dangerous_area']  => $data['after_dangerous_area'] / 100,
                    enums::$audit_detail_btns['input_notebook_rental'] => $data['after_notebook_rental'] / 100,
                ];
                break;
            case enums::$job_title['branch_supervisor']:
            case enums::$job_title['shop_supervisor']:
            case enums::$job_title['store_supervisor']:
            case enums::$job_title['hub_supervisor']:
                //【基本工资/m】【职位津贴/m】【餐补/d】【危险区域津贴/m】【电脑补贴/m】
                $params = [
                    enums::$audit_detail_btns['input_base_salary']        => $data['after_base_salary'] / 100,
                    enums::$audit_detail_btns['input_position_allowance'] => $data['after_position_allowance'] / 100,
                    enums::$audit_detail_btns['input_food_allowance']     => $data['after_food_allowance'] / 100,
                    enums::$audit_detail_btns['input_dangerous_area']     => $data['after_dangerous_area'] / 100,
                    enums::$audit_detail_btns['input_notebook_rental']    => $data['after_notebook_rental'] / 100,
                ];
                break;
            default:
                //【基本工资/m】【经验津贴/m】 【职位津贴/m】【餐补/d】【电脑补贴/m】【租房津贴/m】【危险区域津贴/m】
                $params = [
                    enums::$audit_detail_btns['input_base_salary']        => $data['after_base_salary'] / 100,
                    enums::$audit_detail_btns['input_exp_allowance']      => $data['after_exp_allowance'] / 100,
                    enums::$audit_detail_btns['input_position_allowance'] => $data['after_position_allowance'] / 100,
                    enums::$audit_detail_btns['input_food_allowance']     => $data['after_food_allowance'] / 100,
                    enums::$audit_detail_btns['input_notebook_rental']    => $data['after_notebook_rental'] / 100,
                    enums::$audit_detail_btns['input_house_rental']       => $data['after_house_rental'] / 100,
                    enums::$audit_detail_btns['input_dangerous_area']     => $data['after_dangerous_area'] / 100,
                ];
                break;
        }
        $data["after_salary"] = $params;

        return $data;
    }

    /**
     * 转岗-列表
     * @Access  public
     * @Param   array
     * @Return  bool
     */
    public function getJobtransferList($paramIn = [])
    {
        $data = $this->jobtransfer->getJobtransferList($paramIn);
        if ($data) {
            $sysObj = new SysListRepository();
            //查询职位名称 ids
            $positionIdsArr = array_values(array_unique(array_merge(array_column($data, "current_position_id"),
                array_column($data, "after_position_id"))));
            $positionData   = $sysObj->getPositionList([
                "ids" => $positionIdsArr,
            ]);
            $positionData   = array_column($positionData, 'name', 'id');

            //查询网点名称 ids
            $storeIdsArr = array_values(array_unique(array_merge(array_column($data, "current_store_id"),
                array_column($data, "after_store_id"))));
            $storeData   = $sysObj->getStoreList([
                "ids" => $storeIdsArr,
            ]);
            $storeData   = array_column($storeData, 'name', 'id');

            //查询部门名称 ids
            $departmentIdsArr = array_values(array_unique(array_merge(array_column($data, "current_department_id"),
                array_column($data, "after_department_id"))));
            $departmentData   = $sysObj->getDepartmentList([
                "ids" => $departmentIdsArr,
            ]);
            $departmentData   = array_column($departmentData, 'name', 'id');

            //查询员工名称 ids
            $staffIdsArr = array_values(array_unique(array_merge(array_column($data, "staff_id"),
                array_column($data, "current_manager_id"), array_column($data, "current_indirect_manger_id"),
                array_column($data, "after_manager_id"))));
            $staffData = (new StaffServer())->getStaffInfoList($staffIdsArr);

            foreach ($data as $k => $v) {
                $data[$k]['staff_name']              = !empty($staffData[$v['staff_id']]) ? $staffData[$v["staff_id"]]['staff_name'] : "";
                $data[$k]['hire_type']               = !empty($staffData[$v['staff_id']]) ? $staffData[$v["staff_id"]]['hire_type'] : "";
                $data[$k]['current_department_name'] = $departmentData[$v["current_department_id"]] ?? "";
                $data[$k]["after_department_name"]   = $departmentData[$v["after_department_id"]] ?? "";
                $data[$k]["current_store_name"]      = $storeData[$v["current_store_id"]] ?? "";
                $data[$k]["after_store_name"]        = $storeData[$v["after_store_id"]] ?? "";
                $data[$k]["current_position_name"]   = $positionData[$v["current_position_id"]] ?? "";
                $data[$k]["after_position_name"]     = $positionData[$v["after_position_id"]] ?? "";
            }
        }
        return $data;
    }

    /**
     * 获取片区经理管理的片区
     * @param $staffId
     * @return array
     */
    public function getManagerPieces($staffId)
    {
        $sql  = "--
            SELECT
                id as piece_id
            FROM
                sys_manage_piece
            WHERE
                manager_id = {$staffId}";
        $data = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $data ? array_column($data, 'piece_id') : [];
    }

    /**
     * 获取大区经理管理的大区
     * @param $staffId
     * @return array
     */
    public function getManagerRegions($staffId)
    {
        $sql  = "--
            SELECT
                id as region_id
            FROM
                sys_manage_region
            WHERE
                manager_id = {$staffId}";
        $data = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $data ? array_column($data, 'region_id') : [];
    }

    /**
     * 转岗失败
     *
     * 转岗HC加减规则
     *
     * 一、添加：
     *  1. 新增转岗(BY)
     *  2. 新增转岗(OA)
     *  3. 操作立即转岗HC人数不足，重新指定HC时
     * 二、扣减
     *  1. 审批驳回或撤销
     *  2. 转岗、立即转岗
     *
     *
     * @param $job_transfer_id
     * @param $hc_id
     * @return void
     */
    public function transferFailed($job_transfer_id, $hc_id)
    {
        $sql    = "--
                SELECT * FROM hr_hc WHERE hc_id = ?";
        $obj    = $this->getDI()->get('db')->query($sql, [$hc_id]);
        $hcInfo = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (empty($hcInfo)) {
            return;
        }
        $this->getDI()->get('db')->begin();
        try {
            //更新转岗状态为未转岗
            $this->jobtransfer->updateJobtransfer([
                "id"         => $job_transfer_id,
                "updateData" => [
                    "state" => 4, //转岗失败
                ],
            ]);

            (new HcServer($this->lang, $this->timezone))->rollBackHc([
                'id'            => $hc_id,
                'surplusnumber' => $hcInfo['surplusnumber'] ?? 0,
                'demandnumber'  => $hcInfo['demandnumber'] ?? 0,
            ]);

            $this->getDI()->get('db')->commit();
            $this->getDI()->get('logger')->write_log("转岗失败,修改转岗表:".$job_transfer_id, "info");
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("转岗失败,问题:".$e->getMessage());
            $this->getDI()->get('db')->rollback();
        }
    }

    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom): array
    {
        //获取转岗详情
        $result = $this->getJobtransferInfo(['id' => $auditId]);

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
        if ($staff_info['data']) {
            $staff_info = $staff_info['data'];
        }

        //组织详情数据-通用详情
        $detailLists = [
            'apply_parson'     => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? ''),
            'apply_department' => sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? ''),
            'jobtransfer_0031' => sprintf('%s ( %s )', $result['staff_name'], $result['staff_id']),
            'jobtransfer_0010' => $result['current_department_name'],
            'jobtransfer_0012' => $result['current_position_name'],
            'jobtransfer_0007' => $result['current_store_name'],
            'before_working_day_rest_type' => !empty($result['before_working_day_rest_type']) ? $this->getTranslation()->_('working_day_rest_type_'.$result['before_working_day_rest_type']) : '',
            'jobtransfer_0014' => $result['after_department_name'],
            'jobtransfer_0015' => $result['after_position_name'],
            'jobtransfer_0008' => $result['after_store_name'],
            'jobtransfer_0033' => $result['hc_id'],
            'after_working_day_rest_type' => !empty($result['after_working_day_rest_type']) ? $this->getTranslation()->_('working_day_rest_type_'.$result['after_working_day_rest_type']) : '',
            'jobtransfer_0016' => $result['after_date'],
            'work_handover'    => sprintf('%s ( %s )', $result['job_handover_staff_name'],
                $result['job_handover_staff_id']),
            'jobtransfer_0017' => $result['reason'],
        ];
        if (!empty($result['after_role_ids'])) {
            $detailLists = array_merge($detailLists, [
                'jobtransfer_0035' => $result['after_role_name'] ?? '',
            ]);
        }

        //转岗后职位为Van Courier、Bike Courier、Tricycle Courier时显示车辆来源
        if (in_array($result['after_position_id'], [
            enums::$job_title['van_courier'],
            enums::$job_title['bike_courier'],
            enums::$job_title['tricycle_courier'],
            enums::$job_title['car_courier'],
        ])) {
            if ($result['car_owner'] == 1) {
                $detailLists = array_merge($detailLists, [
                    'jobtransfer_0036' => $result['car_owner_label'] ?? '',
                ]);
            } else {
                $detailLists = array_merge($detailLists, [
                    'jobtransfer_0036' => $result['car_owner_label'] ?? '',
                    'start_time'       => $result['rental_car_cteated_at'] ?? '',
                ]);
            }
        }
        //高阶审批人才可以看到的详情
        if (!empty($result['senior_auditor']) && in_array($user, explode(',', $result['senior_auditor']))) {
            $detailLists = array_merge($detailLists, [
                'jobtransfer_0038' => $result['before_base_salary'] ?? '',
                'jobtransfer_0037' => $this->decodeRejectSalaryDetail($result['reject_salary_detail']),
                'jobtransfer_0045' => $result['image_path'] ?? [],
            ]);
        }
        $this->getDI()->get('logger')->write_log('111111111111111111' . json_encode($detailLists));
        $returnData['data']['detail'] = $this->format($detailLists);

        //获取当前审批人的角色
        $roleInfo = HrStaffInfoPositionModel::findFirst([
            'conditions' => "staff_info_id = :staff_id: and position_category = 68",
            'bind'       => [
                'staff_id' => $user,
            ],
        ]);

        $vehicleJobTitle = explode(',',(new SettingEnvServer())->getSetVal('job_title_vehicle_type'));
        $data = [
            'title'      => $this->auditlist->getAudityType($this->typeUnion),
            'id'         => $result['id'],
            'staff_id'   => $result['submitter_id'],
            'type'       => $this->typeUnion,
            'created_at' => $result['created_at'],
            'updated_at' => $result['updated_at'],
            'status'     => $result['state'],
            'hc_begin_date' => date("Y-m-d", strtotime($result['created_at']) + 1 * 86400),
            'hc_end_date' => date("Y-m-d", strtotime($result['hc_expiration_date'])),
            'status_text'=> $this->auditlist->getAuditStatus('10' . $result['state']),
            'serial_no'  => $result['serial_no'] ?? '',
            'after_job_title' => $result['after_position_id'],
            'after_department_id' => $result['after_department_id'],
            'staff_hire_date' => $result['staff_hire_date'],
            'is_hrbp'   => $roleInfo ? 1 : 0, //0-无hrbp角色 1-有hrbp角色
            'show_vehicle_job_title' =>  $vehicleJobTitle,
            'after_working_day_rest_type' => $result['after_working_day_rest_type'],
            'after_working_day_rest_type_text' => !empty($result['after_working_day_rest_type']) ? $this->getTranslation()->_('working_day_rest_type_'.$result['after_working_day_rest_type']) : '',
        ];

        if ($comeFrom == 2) { //审批人可以审批相应的薪资信息
            //获取当前审批人的审批状态
            $appInfo = AuditApprovalModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and approval_id = :approval_id: and deleted = 0",
                'bind'       => [
                    'type'        => enums::$audit_type['TF'],
                    'value'       => $auditId,
                    'approval_id' => $user,
                ],
                'order'      => 'id desc',
            ]);
            $state   = isset($appInfo) && $appInfo ? $appInfo->getState() : 0;

            $auditApply    = AuditApplyModel::findFirst([
                'conditions' => ' biz_value = :value: and biz_type = :type: ',
                'bind'       => [
                    'value' => $auditId,
                    'type'  => enums::$audit_type['TF'],
                ],
            ]);
            $afterApproval = 0;
            if ($auditApply) {
                //$auditApply = $auditApply->toArray();
                $node = WorkflowNodeModel::findFirst([
                    'conditions' => ' flow_id = :flow_id: and auditor_type = :type: and find_in_set(:auditor_id:, auditor_id) ',
                    'bind'       => [
                        'flow_id'    => $auditApply->flow_id,
                        'type'       => enums::WF_NODE_HRBP,
                        'auditor_id' => $user,
                    ],
                ]);
                if ($node && $result['approval_state'] == enums::APPROVAL_STATUS_PENDING) {
                    $afterApproval = 3;
                }
            }

            $option = ['beforeApproval' => "1,2", 'afterApproval' => $afterApproval];

            //在审批时,审批人的操作
            $options = $this->getStaffOptions(['option' => $option, 'status' => $state]);

            //存在待审批
            //已经填写过薪资的审批人、或者是hrbp
            if (isset($state) && $state == enums::APPROVAL_STATUS_PENDING && ($result['after_base_salary'] != 0 || !empty($roleInfo))) {
                $tmpOption = [];
                foreach ($result['after_salary'] as $k => $v) {
                    $tmpOption['code'.$k] = intval($v);
                }

                if (!empty($roleInfo)) { //hrbp可以修改图片、角色、

                    //如果转岗前与转岗后职位相同，并且是第一次填写 要代入转岗前的角色
                    $roles = $result['after_position_id'] == $result['current_position_id'] ? ($result['current_role_name_arr'] ?? []) : [];

                    $optionData = array_merge((array)$options ?? [], $tmpOption ?? [], [
                        //转岗后职位
                        'code19' => isset($result['after_role_ids']) && $result['after_role_ids']
                            ? ($result['after_role_name_arr'] ?? [])
                            : $roles,
                        //图片
                        'code20' => $result['image_path'] ?? "",
                        //转岗日期
                        'code21' => isset($result['after_date']) && $result['after_date']
                            ? date('Y-m-d', strtotime($result['after_date']))
                            : '',
                    ]);

                    //如果是hrbp并且转岗后的角色是van Courier Car courier则可以修改车辆归属
                    if (isset($result['after_position_id']) && in_array($result['after_position_id'],
                            $vehicleJobTitle)) {
                        if (isset($result['car_owner']) && $result['car_owner'] == 2) { //借用公司车辆
                            $optionData = array_merge($optionData, [
                                'code22' => isset($result['car_owner']) && $result['car_owner']
                                    ? ['label' => $result['car_owner_label'], 'value' => $result['car_owner']]
                                    : '',
                                'code23' => date("Y-m-d", strtotime($result['rental_car_cteated_at'])),
                            ]);
                        } else {
                            $optionData = array_merge($optionData, [
                                'code22' => isset($result['car_owner']) && $result['car_owner']
                                    ? ['label' => $result['car_owner_label'], 'value' => $result['car_owner']]
                                    : '',
                            ]);
                        }
                    }
                } else {
                    $optionData = array_merge($tmpOption ?? [], (array)$options ?? []);
                }
            } else {
                $optionData = array_merge((array)$options ?? []);
            }
        } else { //获取申请的审批状态
            $option = ['beforeApproval' => '0', 'afterApproval' => '0'];
            $state  = 1;

            //在审批时,审批人的操作
            $optionData = $this->getStaffOptions(['option' => $option, 'status' => $state]);
        }

        $data['options']            = $optionData;
        $returnData['data']['head'] = $data;
        return $returnData;
    }

    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        //获取转岗详情
        $osServer = new JobtransferRepository($this->timezone);
        $info     = $osServer->getJobtransferInfo(['id' => $auditId]);

        //获取被转岗人姓名
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $info['staff_id'],
            ],
        ]);

        //获取网点名
        $ss              = new SysStoreServer();
        $storeList       = $ss->getStoreName([$info['current_store_id'], $info['after_store_id']]);
        $storeList['-1'] = 'Head Office';
        //获取大区片区
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("concat(r.name,'-',p.name) as name");
        $builder->from(['s' => SysStoreModel::class]);
        $builder->join(SysManageRegionModel::class, 's.manage_region = r.id', 'r');
        $builder->join(SysManagePieceModel::class, 's.manage_piece = p.id', 'p');
        $builder->where('s.id = :store_id:', ['store_id' => $info['current_store_id']]);
        $pieceAndRegionName = $builder->getQuery()->execute();

        if ($pieceAndRegionName && $pieceAndRegionName->getFirst()) {
            $pieceAndRegionName = $pieceAndRegionName->getFirst()->toArray();
        } else {
            //大区
            $builder = $this->modelsManager->createBuilder();
            $builder->columns("r.name as name");
            $builder->from(['s' => SysStoreModel::class]);
            $builder->join(SysManageRegionModel::class, 's.manage_region = r.id', 'r');
            $builder->where('s.id = :store_id:', ['store_id' => $info['current_store_id']]);
            $pieceAndRegionName = $builder->getQuery()->execute();

            $pieceAndRegionName = isset($pieceAndRegionName) && $pieceAndRegionName ? $pieceAndRegionName->getFirst() : "";
            if (!empty($pieceAndRegionName)) {
                $pieceAndRegionName = $pieceAndRegionName->toArray();
            }
        }

        if (!empty($info)) {
            $param = [
                [
                    'key'   => "jobtransfer_0031",
                    'value' => sprintf("%s (%s)", $info['staff_id'], $staffInfo->name ?? ''),
                ],
                [
                    'key'   => "jobtransfer_0007",
                    'value' => $storeList[$info['current_store_id']] ?? '',
                ],
                [
                    'key'   => "jobtransfer_0032",
                    'value' => $pieceAndRegionName['name'] ?? '-',
                ],
                [
                    'key'   => "jobtransfer_0008",
                    'value' => $storeList[$info['after_store_id']] ?? '',
                ],

            ];
        }
        return $param;
    }

    /**
     * 设置回调
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        //获取详情
        $jobtransferInfo = $this->getJobtransferInfo(['id' => $auditId]);

        if ($isFinal) {
            //推送文本内容
            //获取语言
            $lang = (new StaffServer())->getLanguage($jobtransferInfo['submitter_id']);


            //[您为{工号}{员工姓名}提交的{转岗日期}，由{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}的转岗申请{审批编号}，审批状态为{审批状态}]
            $content = str_replace([
                '{staff_id}',
                '{staff_name}',
                '{after_date}',
                '{serial_no}',
                '{current_department_name}',
                '{current_store_name}',
                '{current_position_name}',
                '{after_department_name}',
                '{after_store_name}',
                '{after_position_name}',
                '{audit_state}',
            ], [
                $jobtransferInfo['staff_id'],
                $jobtransferInfo['staff_name'],
                $jobtransferInfo['after_date'],
                $jobtransferInfo['serial_no'],
                $jobtransferInfo['current_department_name'],
                $jobtransferInfo['current_store_name'],
                $jobtransferInfo['current_position_name'],
                $jobtransferInfo['after_department_name'],
                $jobtransferInfo['after_store_name'],
                $jobtransferInfo['after_position_name'],
                $this->getTranslation($lang)->_("audit_status.".$state),
            ], $this->getTranslation($lang)->_('job_transfer_finish'));

            if ($state == 2) {
                $this->getDI()->get('logger')->write_log("jobtransfer::setProperty::最终同意:".$auditId, 'info');

                $this->jobtransfer->updateJobtransfer([
                    "id"         => $auditId,
                    "updateData" => [
                        "approval_state" => 2,
                    ],
                ]);

                $param  = [
                    'staff_info_id'   => $jobtransferInfo['submitter_id'],
                    'message_title'   => $this->getTranslation($lang)->_('job_transfer_result'),
                    'message_content' => $content,
                    'type'            => 18,
                ];
                $msgRes = (new PublicRepository())->pushAndSendMessageToSubmitter($param);
                $this->getDI()->get('logger')->write_log("pushAndSendMessageToSubmitter:转岗最终审批-".$jobtransferInfo['submitter_id']."推送结果:".$msgRes,
                    "info");
            } else {
                $this->getDI()->get('logger')->write_log("jobtransfer::setProperty::最终驳回:".$auditId, 'info');
                //业务处理
                $this->jobtransfer->updateJobtransfer([
                    "id"         => $auditId,
                    "updateData" => [
                        "approval_state" => 3,
                        "state"          => 2,
                    ],
                ]);

                $hcId = $jobtransferInfo['hc_id'];
                //查询当前关联的HC
                $hcInfo = HrHcModel::findFirst([
                    'conditions' => 'hc_id = :hc_id:',
                    'columns'    => 'surplusnumber,demandnumber',
                    'bind'       => ['hc_id' => $hcId],
                ]);
                if (empty($hcInfo)) {
                    throw new \Exception($this->getTranslation()->_('4414'), enums::$ERROR_CODE['1000']);
                }
                (new HcServer($this->lang, $this->timezone))->rollBackHc([
                    'id'            => $hcId,
                    'surplusnumber' => $hcInfo->surplusnumber ?? 0,
                    'demandnumber'  => $hcInfo->demandnumber ?? 0,
                ]);

                $param  = [
                    'staff_info_id'   => $jobtransferInfo['submitter_id'],
                    'message_title'   => $this->getTranslation($lang)->_('job_transfer_result'),
                    'message_content' => $content,
                    'type'            => 18,
                ];
                $msgRes = (new PublicRepository())->pushAndSendMessageToSubmitter($param);
                $this->getDI()->get('logger')->write_log("pushAndSendMessageToSubmitter:转岗驳回-".$jobtransferInfo['submitter_id']."推送结果:".$msgRes,
                    "info");
            }
        } else {
            //获取审批人Ids
            $approvers = $extend['approval'] ?? [];

            //审批人是否存在HRBP角色
            if (isset($approvers) && !empty($approvers)) {
                $staffsRoles = HrStaffInfoPositionModel::find([
                    'conditions' => 'staff_info_id in ({staff_ids:array}) and position_category = 68',
                    'bind'       => ['staff_ids' => $approvers],
                    'columns'     => 'staff_info_id',
                ])->toArray();
            } else {
                $staffsRoles = [];
            }

            if (!empty($staffsRoles) || !empty($jobtransferInfo['senior_auditor'])) {
                $seniorAuditor    = !empty($jobtransferInfo['senior_auditor']) ? explode(',',
                    $jobtransferInfo['senior_auditor']) : [];
                $seniorAuditorArr = array_unique(array_filter(array_merge($seniorAuditor, $approvers)));

                if (!empty($seniorAuditorArr)) {
                    //记录高级审批人
                    $this->jobtransfer->updateJobtransfer([
                        "id"         => $auditId,
                        "updateData" => [
                            "senior_auditor" => implode(',', $seniorAuditorArr),
                        ],
                    ]);
                    $this->getDI()->get('logger')->write_log("jobtransfer::setProperty::记录高阶审批人:".$auditId,
                        'info');
                }
            }
        }
    }

    /**
     * 设置审批条件参数
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null): array
    {
        //获取信息
        $transferInfo = JobTransferModel::findFirst($auditId);

        //申请人的职位
        $submitterInfo = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $transferInfo->submitter_id,
            ],
        ]);

        //申请人是转岗人的被转岗前的网点负责人
        $isStoreManager = SysStoreModel::findFirst([
            'conditions' => "manager_id = :staff_info_id: and id = :store_id:",
            'bind'       => [
                'staff_info_id' => $transferInfo->submitter_id,
                'store_id'      => $transferInfo->current_store_id,
            ],
        ]);

        //申请人是转岗人的被转岗前的大区负责人
        $isBeforeAm = false;
        $storeInfo  = SysStoreModel::findFirst([
            'conditions' => "id = :store_id:",
            'bind'       => [
                'store_id' => $transferInfo->current_store_id,
            ],
        ]);
        if (!empty($storeInfo)) {
            $isBeforeAm = SysManageRegionModel::findFirst([
                'conditions' => 'id = :id: and manager_id = :manager_id:',
                'bind' => [
                    'id' => $storeInfo->manage_region,
                    'manager_id' => $transferInfo->submitter_id,
                ],
            ]);
        }

        $is_dept_manager  = 0;//申请人是否为部门负责人
        $department_level = 0;//部门等级

        //获取申请人是否为部门负责人 如果是 获取部门等级最高的
        $department = SysDepartmentModel::findFirst([
            'conditions' => "manager_id = :staff_info_id: and deleted = 0",
            'bind'       => [
                'staff_info_id' => $transferInfo->submitter_id,
            ],
            'order'      => ' level asc',
        ]);
        if ($department) {
            $is_dept_manager  = 1;
            $department_level = $department->level; //部门等级
        }

        $parameters = [
            'exp_allowance'     => $transferInfo->after_exp_allowance ?? 0,  //转岗后经验津贴
            'audit_state'       => $state ?? 1,                              //审批状态
            'is_salary_changed' => $transferInfo->is_pay_adjustment ?? 0,    //审批时，薪资是否变化
            'job_title'         => $submitterInfo->job_title ?? 0,           //申请人职位
            'is_store_manager'  => !empty($isStoreManager),
            'department_id'     => $submitterInfo->node_department_id ?? 0,  //申请人所属部门
            'is_dept_manager'   => $is_dept_manager,
            'level'             => $department_level,
            'is_before_am'      => !empty($isBeforeAm),
            'after_position_id' => $transferInfo->after_position_id ?? 0,  //转岗后职位

        ];

        $this->logger->write_log("[job transfer][getWorkflowParams]=======:".json_encode($parameters), "info");

        return $parameters;
    }

    /**
     * 转岗-部门下拉
     */
    public function getDepartmentList($paramIn = [])
    {
        $param["staff_id"] = $paramIn["staff_id"] ?? "";
        $sysDepartmentId   = $this->jobtransfer->getSysDepartmentId($param);
        if (empty($sysDepartmentId)) {
            return false;
        }
        $getAncestry = SysDepartmentModel::findFirst([
            'conditions' => 'id = :str: and deleted = 0',
            'bind'       => [
                'str' => $sysDepartmentId['sys_department_id'],
            ],
            'columns'    => 'id,ancestry_v3',
        ]);
        if (empty($getAncestry)) {
            return false;
        }
        $getAncestry                = $getAncestry->toArray();
        $data                       = SysDepartmentModel::find([
            'conditions' => '(ancestry_v3 like :str: or id = :department_id:) and deleted = 0',
            'bind'       => [
                'str'           => "{$getAncestry['ancestry_v3']}/%",
                'department_id' => $getAncestry['id'],
            ],
            'columns'    => 'id,name',
            'order'      => 'name asc',
        ])->toArray();
        $returnData['data']['list'] = $data;
        return $returnData;
    }

    /**
     * 获取全部角色翻译
     */
    public function getRolesTranslation(): array
    {
        try {
            $winhr_rpc = (new ApiClient('hr_rpc', '', 'role_list'));
            $winhr_rpc->setParams(['']);
            $ret = $winhr_rpc->execute();
            $this->getDI()->get('logger')->write_log('Translation: '.json_encode($ret).'====>'.json_encode($ret),
                'info');
            return array_column($ret['result'], null, 'role_id');
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("Translation-".$e->getMessage().$e->getTraceAsString(), 'info');
            return [];
        }
    }

    /**
     * 立即转岗
     * @param array $params
     * @return array
     */
    public function doJobTransfer($params = []): array
    {
        $data       = $params['data'] ?? [];
        $manual     = $params['manual_operate'] ?? [];
        $operatorId = $params['operator_id'] ?? 10000;

        $objPublic   = new PublicRepository();
        $staffObj    = new StaffServer();
        $workflowObj = new WorkflowServer($this->lang, $this->timezone);


        //消息发送黑名单
        $blackList = $this->getBlackList();
        //与车辆有关的职位
        $vehicleJobTitle = explode(',', (new SettingEnvServer())->getSetVal('job_title_vehicle_type')) ?: [
            VehicleInfoEnums::JOB_VAN_TITLE_ID,
            VehicleInfoEnums::JOB_BIKE_TITLE_ID,
        ];
        //获取转岗成功后需要发送邮件的职位  只有 ph 有值 其他国家为空
        $do_job_transfer_mail_job_titles = explode(',',
            (new SettingEnvServer())->getSetVal('do_job_transfer_email_job_titles'));

        foreach ($data as $k => $v) {
            if ($v['state'] == JobTransferModel::JOBTRANSFER_STATE_TRANSFERED) {
                continue;
            }

            //[1.1]校验是否有回款项；如果手里有未回款，不能转岗；
            $_re = $this->checkReceivableDetail([
                "staff_id" => $v['staff_id'],
                "store_id" => $v["current_store_id"],
            ]);
            //转岗申请人 ผู้ยื่นขอ
            //转岗前HRBP  HRBP หลังโอนย้าย
            $pushStaffIds = [
                $v["submitter_id"],
            ];
            $hrbp         = $workflowObj->findHRBP($v['current_department_id'], ["store_id" => $v['current_store_id']]);
            $this->getDI()->get('logger')->write_log("转岗前hrbp:".json_encode($hrbp), "info");
            if ($hrbp) {
                $pushStaffIds = array_merge($pushStaffIds, explode(',', $hrbp));
            }
            //没有在oa 编辑过上级 的 重新 获取上级
            if ($v["is_edit_manager_id"] == JobTransferModel::IS_EDIT_MANAGER_ID_NO) {
                $manager_id = $this->getafterManagerIdInfo($v['after_store_id'], $v['after_department_id'],
                    $v['after_position_id'], $v['after_date']);
                if ($manager_id != $v["after_manager_id"]) {
                    $this->getDI()->get('logger')->write_log([
                        'do_transfer_manager' => [
                            'before' => $v["after_manager_id"],
                            'after'  => $manager_id,
                        ],
                    ], "info");
                    $v["after_manager_id"] = $manager_id;
                }
            }

            if (!$_re) {
                //更新转岗状态为未转岗
                $this->transferFailed($v['id'], $v['hc_id']);

                $this->getDI()->get('logger')->write_log("({$v['staff_id']})转岗同步数据出现异常，有回款项；手里有未回款，不能转岗；",
                    "info");

                $failure_reason = [
                    'zh-CN' => $this->getTranslationByLang('zh-CN')->t('job_transfer_cod_err'),
                    'en'    => $this->getTranslationByLang('en')->t('job_transfer_cod_err'),
                    'th'    => $this->getTranslationByLang('th')->t('job_transfer_cod_err'),
                ];
                //记录操作日志
                $this->saveOperateLog([
                    'id'             => $v['id'],
                    'staff_info_id'  => $operatorId,
                    'operate_id'     => $manual == 1 ? 1 : 2, // 1-立即转岗 2-系统自动转岗 3-修改日期 4-修改hc
                    'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                    'failure_reason' => json_encode($failure_reason),
                ]);

                foreach ($pushStaffIds as $val) {
                    //不给黑名单发送消息
                    if (in_array($val, $blackList)) {
                        $this->getDI()->get('logger')->write_log("blacklist-notice：转岗黑名单不发送消息".$val, "info");
                        continue;
                    }

                    //获取语言
                    $lang = $staffObj->getLanguage($val);

                    //内容：您好，您为{工号}{转岗员工姓名}提交的{转岗日期}转岗申请{审批编号}，由{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}的转岗失败，原因为{失败原因}
                    $checkErrorMsageContent = str_replace([
                        '{staff_id}',
                        '{staff_name}',
                        '{after_date}',
                        '{serial_no}',
                        '{current_department_name}',
                        '{current_store_name}',
                        '{current_position_name}',
                        '{after_department_name}',
                        '{after_store_name}',
                        '{after_position_name}',
                        '{failure_reason}',
                    ], [
                        $v['staff_id'],
                        $v['staff_name'],
                        $v['after_date'],
                        $v['serial_no'],
                        $v['current_department_name'],
                        $v['current_store_name'],
                        $v['current_position_name'],
                        $v['after_department_name'],
                        $v['after_store_name'],
                        $v['after_position_name'],
                        $this->getTranslation($lang)->_('job_transfer_cod_err'),
                    ], $this->getTranslation($lang)->_('job_transfer_fail'));

                    $checkErrorParam = [
                        'staff_info_id'   => $val,
                        'message_title'   => $this->getTranslation($lang)->_('job_transfer_failulre'),
                        'message_content' => $checkErrorMsageContent,
                        'type'            => 18,
                    ];
                    $msgRes          = $objPublic->pushAndSendMessageToSubmitter($checkErrorParam);
                    $this->getDI()->get('logger')->write_log("({$v['staff_id']})转岗同步数据出现异常，消息推送{$val}结果".$msgRes,
                        "info");
                }
                continue;
            }

            //[1.2]校验工作是否完成；未完成任务（交接扫描但未妥投），不能转岗。
            $_re = $this->checkTicketDelivery([
                "staff_id" => $v["staff_id"],
            ]);
            if (!$_re) {
                //更新转岗状态为未转岗
                $this->transferFailed($v['id'], $v['hc_id']);

                $this->getDI()->get('logger')->write_log("转岗同步数据出现异常，有未完成任务（交接扫描但未妥投），不能转岗",
                    "info");
                $failure_reason = [
                    'zh-CN' => $this->getTranslationByLang('zh-CN')->t('job_transfer_unfinish_task_err'),
                    'en'    => $this->getTranslationByLang('en')->t('job_transfer_unfinish_task_err'),
                    'th'    => $this->getTranslationByLang('th')->t('job_transfer_unfinish_task_err'),
                ];
                //记录操作日志
                $this->saveOperateLog([
                    'id'             => $v['id'],
                    'staff_info_id'  => $operatorId,
                    'operate_id'     => $manual == 1 ? 1 : 2, // 1-立即转岗 2-系统自动转岗 3-修改日期 4-修改hc
                    'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                    'failure_reason' => json_encode($failure_reason),
                ]);


                foreach ($pushStaffIds as $val) {
                    //不给黑名单发送消息
                    if (in_array($val, $blackList)) {
                        $this->getDI()->get('logger')->write_log("blacklist-notice：转岗黑名单不发送消息".$val, "info");
                        continue;
                    }

                    //获取语言
                    $lang = $staffObj->getLanguage($val);

                    //内容：您好，您为{工号}{转岗员工姓名}提交的{转岗日期}转岗申请{审批编号}，由{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}的转岗失败，原因为{失败原因}
                    $checkErrorMsageContent = str_replace([
                        '{staff_id}',
                        '{staff_name}',
                        '{after_date}',
                        '{serial_no}',
                        '{current_department_name}',
                        '{current_store_name}',
                        '{current_position_name}',
                        '{after_department_name}',
                        '{after_store_name}',
                        '{after_position_name}',
                        '{failure_reason}',
                    ], [
                        $v['staff_id'],
                        $v['staff_name'],
                        $v['after_date'],
                        $v['serial_no'],
                        $v['current_department_name'],
                        $v['current_store_name'],
                        $v['current_position_name'],
                        $v['after_department_name'],
                        $v['after_store_name'],
                        $v['after_position_name'],
                        $this->getTranslation($lang)->_('job_transfer_unfinish_task_err'),
                    ], $this->getTranslation($lang)->_('job_transfer_fail'));

                    $checkErrorParam = [
                        'staff_info_id'   => $val,
                        'message_title'   => $this->getTranslation($lang)->_('job_transfer_failulre'),
                        'message_content' => $checkErrorMsageContent,
                        'type'            => 18,
                    ];
                    $msgRes          = $objPublic->pushAndSendMessageToSubmitter($checkErrorParam);
                    $this->getDI()->get('logger')->write_log("转岗同步数据出现异常，消息推送{$val}结果".$msgRes, "info");
                }
                continue;
            }

            //[2]向FBI-HRIS同步转岗信息
            $positionCategory = [];
            $carType          = '';


            if ($v["after_position_id"] == VehicleInfoEnums::JOB_BIKE_TITLE_ID) {
                $carType = 'Bike'; //['Bike', 'Van','Boat']
            }
            if ($v["after_position_id"] == VehicleInfoEnums::JOB_VAN_TITLE_ID) {
                $carType = 'Van';
            }

            if (in_array($v['after_position_id'],
                    $vehicleJobTitle) && $v["after_position_id"] == VehicleInfoEnums::JOB_TRUCK_TITLE_ID) {
                $carType = 'Truck';
            }

            //角色: 0-快递分配员 也是有效值
            if (isset($v["after_role_ids"])) {
                $positionCategory = explode(',', $v["after_role_ids"]);
            }

            $params = [
                "staff_info_id"     => $v["staff_id"],
                "department_id"     => $v["after_department_id"],
                "job_title"         => $v["after_position_id"],
                "sys_store_id"      => $v["after_store_id"],
                "operater"          => $v['submitter_id'],
                "direct_manager"    => $v["after_manager_id"],
                "position_category" => $positionCategory,
                "car_type"          => $carType,
                "vehicle_source"    => $v['car_owner'] ?? null,
                "vehicle_use_date"  => $v['rental_car_cteated_at'] ?? null,
                "working_day_rest_type" => $v['after_working_day_rest_type'] ?? 0,
            ];
            $this->getDI()->get('logger')->write_log("转岗参数,参数:".json_encode($params), 'info');

            $_re = $staffObj->syncHrStaff($params);
            if ($_re["result"]['code'] == 1) { //转岗成功

                //[3]更新HC剩余人数、HC招聘状态、转岗状态
                //更新转岗状态
                //更新转岗关联hc的剩余人数
                $this->getDI()->get('db')->begin();
                try {
                    //昕哲需求
                    //二阶段转岗，当转岗申请提交成功后，立即在winhr中减少一条已选的转岗HC数量；
                    //若转岗流程正常完成则后续不作更改，若转岗流程未成功完成则返回一条对应的转岗HC

                    //更新转岗状态
                    $_re = $this->jobtransfer->updateJobtransfer([
                        "id"         => $v["id"],
                        "updateData" => [
                            "after_manager_id" => $v["after_manager_id"],
                            "state"            => 3,
                        ],
                    ]);
                    $this->getDI()->get('db')->commit();
                    $this->getDI()->get('logger')->write_log("转岗成功,修改转岗表:".$_re, "info");
                } catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log("转岗失败,问题:".$e->getMessage());
                    $this->getDI()->get('db')->rollback();
                    continue;
                }

                //[4]发送消息通知
                $pushStaffIds = [
                    ['staff_info_id' => $v["staff_id"], 'type' => 1],
                    ['staff_info_id' => $v["after_manager_id"], 'type' => 1],
                    ['staff_info_id' => $v["submitter_id"], 'type' => 2],
                ];
                //转岗前 hrbp
                if ($hrbp) {
                    $pushStaffIds = array_merge($pushStaffIds, explode(',', $hrbp));
                }
                //查询转岗后hrbp
                $hrbp = $workflowObj->findHRBP($v['after_department_id'], ["store_id" => $v['after_store_id']]);
                $this->getDI()->get('logger')->write_log("转岗后hrbp:".json_encode($hrbp), "info");
                if ($hrbp) {
                    $pushStaffIds = array_merge($pushStaffIds, explode(',', $hrbp));
                }

                foreach ($pushStaffIds as $val) {
                    //不给黑名单发送消息
                    if (in_array($val, $blackList)) {
                        $this->getDI()->get('logger')->write_log("blacklist-notice：转岗黑名单不发送消息".$val, "info");
                        continue;
                    }

                    //获取语言
                    $lang = $staffObj->getLanguage($val['staff_info_id'] ?? $val);

                    //转岗员工 พนักงานที่ถูกโอนย้าย
                    //转岗员工转岗后直线上级  หัวหน้าพนักงานหลังโอนย้าย
                    //内容：您好，{工号}{转岗员工姓名}的转岗申请，转岗日期为{转岗日期} ，转岗申请编号{审批编号}，员工已从{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}。
                    $pushSuccessMessageContent = str_replace([
                        '{staff_id}',
                        '{staff_name}',
                        '{after_date}',
                        '{serial_no}',
                        '{current_department_name}',
                        '{current_store_name}',
                        '{current_position_name}',
                        '{after_department_name}',
                        '{after_store_name}',
                        '{after_position_name}',
                    ], [
                        $v['staff_id'],
                        $v['staff_name'],
                        $v['after_date'],
                        $v['serial_no'],
                        $v['current_department_name'],
                        $v['current_store_name'],
                        $v['current_position_name'],
                        $v['after_department_name'],
                        $v['after_store_name'],
                        $v['after_position_name'],
                    ], $this->getTranslation($lang)->_('job_transfer_success_1'));

                    //转岗申请人 ผู้ยื่นขอ
                    //转岗前后HRBP  HRBP หลังโอนย้าย
                    //内容：您好，您为{工号}{转岗员工姓名}提交的{转岗日期}转岗申请{审批编号}，由{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}的转岗申请已通过，请联系员工进行转岗。
                    $pushSuccessMessageContent2 = str_replace([
                        '{staff_id}',
                        '{staff_name}',
                        '{after_date}',
                        '{serial_no}',
                        '{current_department_name}',
                        '{current_store_name}',
                        '{current_position_name}',
                        '{after_department_name}',
                        '{after_store_name}',
                        '{after_position_name}',
                    ], [
                        $v['staff_id'],
                        $v['staff_name'],
                        $v['after_date'],
                        $v['serial_no'],
                        $v['current_department_name'],
                        $v['current_store_name'],
                        $v['current_position_name'],
                        $v['after_department_name'],
                        $v['after_store_name'],
                        $v['after_position_name'],
                    ], $this->getTranslation($lang)->_('job_transfer_success_2'));

                    $pushSuccessParam = [
                        'staff_info_id'   => $val['staff_info_id'] ?? $val,
                        'message_title'   => $this->getTranslation($lang)->_('job_transfer_succ'),
                        'message_content' => isset($val['type']) && $val['type'] == 1 ? $pushSuccessMessageContent : $pushSuccessMessageContent2,
                        'type'            => 18,
                    ];
                    $msgRes           = $objPublic->pushAndSendMessageToSubmitter($pushSuccessParam);
                    $this->getDI()->get('logger')->write_log("转岗成功,推送给工号{$pushSuccessParam['staff_info_id']}推送状态:".$msgRes,
                        "info");
                }
                $this->getDI()->get('logger')->write_log("转岗成功，工号:".$v["staff_id"], "info");

                //记录操作日志
                $this->saveOperateLog([
                    'id'            => $v['id'],
                    'staff_info_id' => $operatorId,
                    'operate_id'    => $manual == 1 ? 1 : 2, // 1-立即转岗 2-系统自动转岗 3-修改日期 4-修改hc
                    'state'         => JobTransferModel::JOBTRANSFER_STATE_TRANSFERED,
                ]);

                // 转岗为Bike Courier或Van Courier或Van courier (Project)职位，给该员工发送车辆信息更新提醒
                echo 'staff:'.$v['staff_id'].'check is sendCourierTransferVehicleMsg :'.intval(in_array($v['after_position_id'],
                        $vehicleJobTitle));

                if (
                    in_array($v['after_position_id'], $vehicleJobTitle)
                ) {
                    echo 'staff:'.$v['staff_id'].' sendCourierTransferVehicleMsg !!';
                    $staffObj->sendCourierTransferVehicleMsg($v['staff_id']);
                }
                //转岗后要发送邮件的职位
                if (!empty($do_job_transfer_mail_job_titles) &&
                    in_array($v['after_position_id'], $do_job_transfer_mail_job_titles)
                ) {
                    $this->doJobTransferEmail($v);
                }
            } else { //转岗失败

                //[3]更新转岗状态为未转岗
                $this->transferFailed($v['id'], $v['hc_id']);

                //[4]发送消息通知
                $this->getDI()->get('logger')->write_log("转岗失败，syncHrStaff返回:".json_encode($_re["result"]),
                    "info");

                $failure_reason = [
                    'zh-CN' => $_re["result"]['msg'] ?? '',
                    'en'    => $_re["result"]['msg_en'] ?? '',
                    'th'    => $_re["result"]['msg_th'] ?? '',
                ];
                //记录操作日志
                $this->saveOperateLog([
                    'id'             => $v['id'],
                    'staff_info_id'  => $operatorId,
                    'operate_id'     => $manual == 1 ? 1 : 2, // 1-立即转岗 2-系统自动转岗 3-修改日期 4-修改hc
                    'failure_reason' => json_encode($failure_reason),
                    'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                ]);

                foreach ($pushStaffIds as $val) {
                    //不给黑名单发送消息
                    if (in_array($val, $blackList)) {
                        $this->getDI()->get('logger')->write_log("blacklist-notice：转岗黑名单不发送消息".$val, "info");
                        continue;
                    }

                    //获取语言
                    $lang = $staffObj->getLanguage($val);

                    //转岗申请人 ผู้ยื่นขอ
                    //转岗后HRBP  HRBP หลังโอนย้าย
                    //内容：您好，您为{工号}{转岗员工姓名}提交的{转岗日期}转岗申请{审批编号}，由{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}的转岗失败，原因为{失败原因}
                    $checkErrorMsageContent = str_replace([
                        '{staff_id}',
                        '{staff_name}',
                        '{after_date}',
                        '{serial_no}',
                        '{current_department_name}',
                        '{current_store_name}',
                        '{current_position_name}',
                        '{after_department_name}',
                        '{after_store_name}',
                        '{after_position_name}',
                        '{failure_reason}',
                    ], [
                        $v['staff_id'],
                        $v['staff_name'],
                        $v['after_date'],
                        $v['serial_no'],
                        $v['current_department_name'],
                        $v['current_store_name'],
                        $v['current_position_name'],
                        $v['after_department_name'],
                        $v['after_store_name'],
                        $v['after_position_name'],
                        $failure_reason[$lang] ?? $failure_reason['en'],
                    ], $this->getTranslation($lang)->_('job_transfer_fail'));

                    $checkErrorParam = [
                        'staff_info_id'   => $val,
                        'message_title'   => $this->getTranslation($lang)->_('job_transfer_failulre'),
                        'message_content' => $checkErrorMsageContent,
                        'type'            => 18,
                    ];
                    $msgRes          = $objPublic->pushAndSendMessageToSubmitter($checkErrorParam);
                    $this->getDI()->get('logger')->write_log("转岗失败,同步数据出现异常，消息推送{$val}结果".$msgRes,
                        "info");
                }
                $this->getDI()->get('logger')->write_log("转岗失败，工号:".$v["staff_id"], "info");
            }
        }
        return $this->checkReturn([]);
    }

    /**
     * 编辑转岗信息
     * @param $paramIn
     * @return array
     * @throws \Exception
     */
    public function editJobTransferInfo($paramIn = []): array
    {
        if (!in_array($paramIn['type'], [
            JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_TRANSFER_DATE,
            JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_HC_ID,
            JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_ROLE,
            JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_TRANSFER_INFO,
        ])) {
            throw new ValidationException('invalid type');
        }

        switch ($paramIn['type']) {
            case JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_TRANSFER_DATE: //更改转岗日期
                $data = $this->updateJobTransferDate($paramIn);
                break;
            case JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_HC_ID: //更改HcID
                $data = $this->updateJobTransferHcId($paramIn);
                break;
            case JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_ROLE: //更改角色和上级
                $data = $this->updateJobTransferStore($paramIn);
                break;
            case JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_TRANSFER_INFO: //更改员工信息
                $data = $this->editJobTransferData($paramIn);
                break;
            default:
                $data = [];
                break;
        }

        return $data;
    }

    /**
     * 更新转岗HCId
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function updateJobTransferHcId($paramIn = []): array
    {
        $jobTransferId = $paramIn['id'] ?? '';
        $staffId       = $paramIn['staff_info_id'] ?? '';
        $hcId          = $paramIn['hc_id'] ?? '';

        if (empty($jobTransferId)) {
            throw new \Exception('no valid job transfer');
        }

        //获取转岗详情
        $jobTransferInfo = JobTransferModel::findFirst($jobTransferId);

        $this->db = $this->getDI()->get("db");
        $this->db->begin();

        //校验hc
        //如果所选hc已经等于0了就不能再减少了
        $hcInfo = HrHcModel::findFirst([
            'conditions' => 'hc_id = :hc_id: and surplusnumber > 0',
            'columns'    => 'surplusnumber,demandnumber,expirationdate',
            'bind'       => ['hc_id' => $hcId],
        ]);
        if (!isset($hcInfo->surplusnumber) || isset($hcInfo->surplusnumber) && $hcInfo->surplusnumber == 0) {
            $this->db->rollback();
            throw new \Exception($this->getTranslation()->_('job_transfer_err_2'));
        }

        //更新转岗hc、以及过期时间
        $this->db->updateAsDict("job_transfer",
            [
                'hc_id'              => $hcId,
                'hc_expiration_date' => $hcInfo->expirationdate,
            ],
            [
                'conditions' => "id = ?",
                'bind'       => [
                    $jobTransferId,
                ],
            ]
        );

        //张帆需求: 更改hc将不再扣减，改为在立即转岗的时候扣减
        //减hc
        /*(new HcServer($this->lang,$this->timezone))->updateHc([
            'id'            => $hcId,
            'surplusnumber' => $hcInfo->surplusnumber ?? 0,
            'demandnumber'  => $hcInfo->demandnumber ?? 0,
        ]);*/

        if (isset($paramIn['after_date'])) {
            $type    = 3;
            $content = $jobTransferInfo->after_date." ==> ".$paramIn['after_date'];
        } elseif (isset($paramIn['hc_id'])) {
            $type    = 4;
            $content = $jobTransferInfo->hc_id." ==> ".$paramIn['hc_id'];
        } else {
            $this->db->rollback();
            return [];
        }

        $this->saveOperateLog([
            'id'            => $jobTransferId,
            'staff_info_id' => $staffId,
            'operate_id'    => $type, // 1-立即转岗 2-系统自动转岗 3-修改日期 4-修改hc
            'state'         => $jobTransferInfo->state,
            'content'       => $content,
        ]);
        $this->db->commit();
        return $this->checkReturn([]);
    }

    /**
     * 更新转岗日期
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function updateJobTransferDate($paramIn = []): array
    {
        $jobTransferId = $paramIn['id'] ?? '';
        $staffId       = $paramIn['staff_info_id'] ?? '';
        $afterDate     = $paramIn['after_date'] ?? '';

        if (empty($jobTransferId)) {
            throw new \Exception('no valid job transfer request');
        }

        //获取转岗详情
        $jobTransferInfo = JobTransferModel::findFirst($jobTransferId);

        $this->db = $this->getDI()->get("db");
        $this->db->begin();
        $this->db->updateAsDict("job_transfer",
            [
                'after_date' => $afterDate,
            ],
            [
                'conditions' => "id = ?",
                'bind'       => [
                    $jobTransferId,
                ],
            ]
        );

        $this->saveOperateLog([
            'id'            => $jobTransferId,
            'staff_info_id' => $staffId,
            'operate_id'    => 3, // 1-立即转岗 2-系统自动转岗 3-修改日期 4-修改hc
            'state'         => $jobTransferInfo->state,
            'content'       => $jobTransferInfo->after_date.' ==> '.$afterDate,
        ]);
        $this->db->commit();
        return $this->checkReturn([]);
    }

    /**
     * 校验转岗申请
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function checkJobTransferApply($paramIn = []): array
    {
        //[1]获取参数
        $submitterId = $this->processingDefault($paramIn, 'submitter_id', 2);
        $data        = $this->processingDefault($paramIn, 'data', 3);
        $failArr     = [];
        try {
            //[1]校验申请人权限
            $storeManagerInfo = SysStoreModel::find([
                'conditions' => 'manager_id = :manager_id:',
                'bind'       => ['manager_id' => $submitterId],
                'column'     => 'id,manager_id',
            ])->toArray();
            $regionMangerInfo = SysManageRegionModel::find([
                'conditions' => 'manager_id = :manager_id:',
                'bind'       => ['manager_id' => $submitterId],
                'column'     => 'id,manager_id',
            ])->toArray();
            $pieceMangerInfo  = SysManagePieceModel::find([
                'conditions' => 'manager_id = :manager_id:',
                'bind'       => ['manager_id' => $submitterId],
                'column'     => 'id,manager_id',
            ])->toArray();
            $staffInfo        = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => ['staff_info_id' => $submitterId],
                'column'     => 'id,node_department_id',
            ]);

            $submitterInfo = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :id:',
                'bind'       => ['id' => $submitterId],
                'column'     => 'sys_department_id',
            ]);

            $envModel            = new SettingEnvServer();
            $hubManagementId     = $envModel->getSetVal('dept_hub_management_id');
            $shopManagementId    = $envModel->getSetVal('dept_shop_management_id');
            $networkManagementId = $envModel->getSetVal('dept_network_management_id');
            $networkPlanId       = $envModel->getSetVal('dept_network_planning_id');

            //获取hub部门及其子部门
            $model      = new FleSysDepartmentModel();
            $hubDeptIds = $model->getHubDepartmentIds();

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('id,manager_id');
            $builder->from(['a' => FleSysDepartmentModel::class]);
            $builder->where('manager_id = :manager_id: and deleted = 0', ['manager_id' => $submitterId]);
            $builder->inWhere('id', $hubDeptIds);
            $hubManagerlist = $builder->getQuery()->execute()->toArray();
            $hubManagerArr  = array_column($hubManagerlist, 'manager_id');

            //检查申请人权限
            //shop只有AM可以申请
            //hub只有网点负责人可以申请
            //如果既不是AM、DM、网点负责人、不在Network Planning部门，也不可以申请
            if (isset($submitterInfo->sys_department_id) && $submitterInfo->sys_department_id == $shopManagementId && !(isset($regionMangerInfo) && $regionMangerInfo) ||
                isset($submitterInfo->sys_department_id) && $submitterInfo->sys_department_id == $hubManagementId && !(isset($storeManagerInfo) && $storeManagerInfo || in_array($submitterId,
                        $hubManagerArr)) ||
                isset($submitterInfo->sys_department_id) && $submitterInfo->sys_department_id == $networkManagementId && !(isset($storeManagerInfo) && $storeManagerInfo) && !(isset($regionMangerInfo) && $regionMangerInfo) &&
                !(isset($pieceMangerInfo) && $pieceMangerInfo) && isset($staffInfo->node_department_id) && $staffInfo->node_department_id != $networkPlanId
            ) {
                $failArr[] = [
                    'staff_id' => $submitterId,
                    'column'   => "",
                    'reason'   => $this->getTranslation()->_('jobtransfer_0004'),
                ];
            }

            //[2]校验申请数据
            if (!empty($data) && is_array($data)) {
                $t = $this->getTranslation();
                foreach ($data as $key => $value) {
                    //[2-1]校验员工信息
                    $staffInfo = $this->getStaffInfo([
                        "staff_id" => $value['staff_id'],
                    ]);

                    if (!$staffInfo) {
                        $failArr[] = [
                            'staff_id' => $value['staff_id'],
                            'column'   => "staff_id",
                            'reason'   => $this->getTranslation()->_('1001'),
                        ];
                        continue;
                    }

                    //[2-2]校验交接员工信息
                    //交接人需要在职、在编、非子账号
                    //交接人不能是转岗员工自己、需要在职
                    $jobHandoverStaffInfo = $this->getStaffInfo([
                        "staff_id" => $value['job_handover_staff_id'],
                    ]);

                    //如果不是本网点的员工
                    $jobHandoverStaffIds = [];
                    if (!isset($jobHandoverStaffInfo["store_id"]) || $jobHandoverStaffInfo["store_id"] != $staffInfo["store_id"]) {
                        $jobHandoverStaffIds = $this->getJobHandoverStaffId($staffInfo);
                    }

                    if (!$jobHandoverStaffInfo || $jobHandoverStaffInfo["state"] != enums::$service_status['incumbency'] ||
                        $jobHandoverStaffInfo["formal"] != 1 || $jobHandoverStaffInfo["is_sub_staff"] != 0 ||
                        ($jobHandoverStaffInfo["store_id"] != $staffInfo["store_id"] && !in_array($value['job_handover_staff_id'],
                                $jobHandoverStaffIds)) ||
                        $value['job_handover_staff_id'] == $value['staff_id']) {
                        $failArr[] = [
                            'staff_id' => $value['staff_id'],
                            'column'   => "job_handover_staff_id",
                            'reason'   => $this->getTranslation()->_('jobtransfer_0020'),
                        ];
                        continue;
                    }

                    //[2-3]校验是否重复添加
                    $_re = $this->jobtransfer->getJobtransferInfo([
                        "staff_id" => $value['staff_id'],
                        "state"    => 1,
                    ]);
                    if ($_re) {
                        $failArr[] = [
                            'staff_id' => $value['staff_id'],
                            'column'   => "staff_id",
                            'reason'   => $this->getTranslation()->_('5202'),
                        ];
                        continue;
                    }

                    $_re = $this->jobtransfer->getJobtransferInfo([
                        "staff_id" => $value['staff_id'],
                    ]);
                    if (!empty($_re) && $_re['approval_state'] == enums::$audit_status['approved'] && $_re['state'] == enums::$job_transfer_state['to_be_transfered']) {
                        $failArr[] = [
                            'staff_id' => $value['staff_id'],
                            'column'   => "staff_id",
                            'reason'   => $this->getTranslation()->_('jobtransfer_0018'),
                        ];
                        continue;
                    }

                    //获取网点信息
                    $afterStoreData = $this->jobtransfer->getStoreInfo([
                        "store_id" => $value['after_store_id'],
                    ]);

                    //获取转岗后直线上级
                    $afterManagerId = $this->getJobtransferStoreManager([
                        "category" => $afterStoreData["category"],
                        "store_id" => $value['after_store_id'],
                    ]);

                    $hubCategoryArr = explode(",", $this->hubCategory);
                    if (empty($afterManagerId)) {
                        //当转岗网点没有网点负责人时，转岗员工转至新网点后直线上级获取规则
                        //[[1]SP,[2]DC,[10]BDC]对应网点的片区经理district manager
                        //[[4]shop(pickup-only),[5]shop(pickup&delivery),[7]ushop]对应网点的大区经理area manager
                        if (in_array($afterStoreData["category"], $hubCategoryArr)) {
                            $failArr[] = [
                                'staff_id' => $value['staff_id'],
                                'column'   => $t->_('jobtransfer_0008'),
                                'reason'   => $this->getTranslation()->_('jobtransfer_0005'),
                            ];
                            continue;
                        }
                    }

                    //查询当前关联的HC
                    $hcInfo = HrHcModel::findFirst([
                        'conditions' => 'hc_id = :hc_id:',
                        'columns'    => 'surplusnumber,demandnumber,expirationdate,hc_id',
                        'bind'       => ['hc_id' => $value['hc_id']],
                    ]);
                    if (empty($hcInfo)) {
                        $failArr[] = [
                            'staff_id' => $value['staff_id'],
                            'column'   => "HcId",
                            'reason'   => $this->getTranslation()->_('jobtransfer_0024'),
                        ];
                        continue;
                    }
                }
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("checkJobTransferApply:".$e->getMessage().$e->getTraceAsString(),
                'notice');
        }
        return $failArr ?? [];
    }

    /**
     * 创建
     * @param array $paramIn
     * @param $submitter_id
     * @param $timezone
     * @param $uuid
     * @return array
     * @throws \Exception
     */
    public function create($paramIn = [], $submitter_id, $timezone, $uuid): array
    {
        //[1]获取参数
        $staffId            = $this->processingDefault($paramIn, 'staff_id', 2);
        $departmentId       = $this->processingDefault($paramIn, 'after_department_id', 2);
        $storeId            = $this->processingDefault($paramIn, 'after_store_id');
        $jobHandoverStaffId = $this->processingDefault($paramIn, 'job_handover_staff_id', 2);
        $jobTitle           = $this->processingDefault($paramIn, 'after_position_id', 2);
        $hcId               = $this->processingDefault($paramIn, 'hc_id', 2);
        $type               = $this->processingDefault($paramIn, 'type');
        $userinfo           = $this->processingDefault($paramIn, 'userinfo');
        $date               = $this->processingDefault($paramIn, 'after_date');
        $reason             = $this->processingDefault($paramIn, 'reason');
        $carOwner           = $this->processingDefault($paramIn, 'car_owner', 2);
        $rentalCarCteatedAt = $this->processingDefault($paramIn, 'rental_car_cteated_at', 2);
        $after_working_day_rest_type = $this->processingDefault($paramIn, 'after_working_day_rest_type', 2);

        //[2]参数校验
        //[2-1]校验员工信息
        $staffInfo = $this->getStaffInfo([
            "staff_id" => $staffId,
        ]);

        if (!$staffInfo) {
            throw new \Exception($this->getTranslation()->_('jobtransfer_0004'), enums::$ERROR_CODE['1000']);
        }

        //[2-2]校验交接员工信息
        //交接人需要在职、在编、非子账号
        //交接人不能是转岗员工自己、需要在职
        $jobHandoverStaffInfo = $this->getStaffInfo([
            "staff_id" => $jobHandoverStaffId,
        ]);
        //如果不是本网点的员工
        $jobHandoverStaffIds = [];
        if (!isset($jobHandoverStaffInfo["store_id"]) || $jobHandoverStaffInfo["store_id"] != $staffInfo["store_id"]) {
            $jobHandoverStaffIds = $this->getJobHandoverStaffId($staffInfo);
        }

        if (!$jobHandoverStaffInfo || $jobHandoverStaffInfo["state"] != enums::$service_status['incumbency'] ||
            $jobHandoverStaffInfo["formal"] != 1 || $jobHandoverStaffInfo["is_sub_staff"] != 0 ||
            ($jobHandoverStaffInfo["store_id"] != $staffInfo["store_id"] && !in_array($jobHandoverStaffId,
                    $jobHandoverStaffIds)) ||
            $jobHandoverStaffId == $staffId) {
            throw new \Exception($this->getTranslation()->_('jobtransfer_0020'), enums::$ERROR_CODE['1000']);
        }

        //[2-3]校验是否重复添加
        $_re = $this->jobtransfer->getJobtransferInfo([
            "staff_id" => $staffId,
            "state"    => 1,
        ]);
        if ($_re) {
            throw new \Exception($this->getTranslation()->_('5202'), enums::$ERROR_CODE['1000']);
        }

        $_re = $this->jobtransfer->getJobtransferInfo([
            "staff_id" => $staffId,
        ]);
        if (!empty($_re) && $_re['approval_state'] == enums::$audit_status['approved'] && $_re['state'] == enums::$job_transfer_state['to_be_transfered']) {
            throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
        }

        //获取网点信息
        $afterStoreData = $this->jobtransfer->getStoreInfo([
            "store_id" => $storeId,
        ]);

        //获取当前直线上级id
        $currentManagerId = $this->getJobtransferStaffMangerId($staffId);
        //获取当前虚线上级id
        $currentIndirectMangerId = $this->getJobtransferStaffIndirectMangerId($staffId);


        $shopCategoryArr = explode(",", $this->shopCategory);
        $hubCategoryArr  = explode(",", $this->hubCategory);

        $afterManagerId = $this->getafterManagerIdInfo($storeId);

        //获取被转岗人的薪资信息
        $staffInfoSalary = $this->staff->getStaffSalary($staffId);
        $this->getDI()->get('logger')->write_log('current salary:'.json_encode($staffInfoSalary), 'info');

        //查询当前关联的HC
        $hcInfo = HrHcModel::findFirst([
            'conditions' => 'hc_id = :hc_id:',
            'columns'    => 'surplusnumber,demandnumber,expirationdate,hc_id',
            'bind'       => ['hc_id' => $hcId],
        ]);
        if (empty($hcInfo)) {
            throw new \Exception($this->getTranslation()->_('4414'), enums::$ERROR_CODE['1000']);
        }

        //如果所选hc已经等于0了就不能再减少了
        $hcInfo = HrHcModel::findFirst([
            'conditions' => 'hc_id = :hc_id: and surplusnumber > 0',
            'columns'    => 'surplusnumber,demandnumber,expirationdate',
            'bind'       => ['hc_id' => $hcId],
        ]);
        if (!isset($hcInfo->surplusnumber) || isset($hcInfo->surplusnumber) && $hcInfo->surplusnumber == 0) {
            throw new \Exception($this->getTranslation()->_('job_transfer_err_2'), enums::$ERROR_CODE['1000']);
        }

        $base_staff_info = $this->jobtransfer->getBaseStaffInfo($staffId);
        $before_working_day_rest_type = $base_staff_info['week_working_day'] . $base_staff_info['rest_type'];

        //添加转岗信息
        $param = [
            "staff_id"                   => $staffId ?? 0,
            "hc_id"                      => $hcId ?? "",
            "hc_expiration_date"         => $hcInfo->expirationdate,
            "type"                       => $type ?? 0,
            "current_department_id"      => $staffInfo["department_id"] ?? 0,
            "current_store_id"           => $staffInfo["store_id"] ?? "",
            "current_position_id"        => $staffInfo["job_title"] ?? 0,
            "current_indirect_manger_id" => intval($currentIndirectMangerId) ?? 0,
            "current_manager_id"         => intval($currentManagerId) ?? 0,
            "current_role_id"            => $staffInfo['position_category'] ?? 0,
            "after_manager_id"           => intval($afterManagerId) ?? 0,
            "after_department_id"        => $departmentId ?? 0,
            "after_store_id"             => $storeId ?? "",
            "after_position_id"          => $jobTitle ?? 0,
            "after_date"                 => $date ?? "",
            "reason"                     => $reason ?? "",
            "job_transfer_id"            => null,
            "job_handover_staff_id"      => $jobHandoverStaffId ?? 0,
            "submitter_id"               => $userinfo['staff_id'] ?? 0,
            "approval_state"             => enums::APPROVAL_STATUS_PENDING,
            "car_owner"                  => $carOwner ?? null,
            "rental_car_cteated_at"      => !empty($rentalCarCteatedAt) ? $rentalCarCteatedAt : null,
            "state"                      => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED,
            "serial_no"                  => $this->getRandomId(),
            "before_base_salary"         => $staffInfoSalary['base_salary'] ?? 0,
            "before_exp_allowance"       => $staffInfoSalary['exp_allowance'] ?? 0,
            "before_position_allowance"  => $staffInfoSalary['position_allowance'] ?? 0,
            "before_car_rental"          => $staffInfoSalary['car_rental'] ?? 0,
            "before_notebook_rental"     => $staffInfoSalary['notebook_rental'] ?? 0,
            "before_recommended"         => $staffInfoSalary['recommended'] ?? 0,
            "before_food_allowance"      => $staffInfoSalary['food_allowance'] ?? 0,
            "before_dangerous_area"      => $staffInfoSalary['dangerous_area'] ?? 0,
            "before_house_rental"        => $staffInfoSalary['house_rental'] ?? 0,
            "before_island_allowance"    => $staffInfoSalary['island_allowance'] ?? 0,
            "before_gasoline_allowance"  => $staffInfoSalary['gasoline_allowance'] ?? 0,
            "after_base_salary"          => $staffInfoSalary['base_salary'] ?? 0,
            "after_exp_allowance"        => $staffInfoSalary['exp_allowance'] ?? 0,
            "after_position_allowance"   => $staffInfoSalary['position_allowance'] ?? 0,
            "after_car_rental"           => $staffInfoSalary['car_rental'] ?? 0,
            "after_notebook_rental"      => $staffInfoSalary['notebook_rental'] ?? 0,
            "after_recommended"          => $staffInfoSalary['recommended'] ?? 0,
            "after_food_allowance"       => $staffInfoSalary['food_allowance'] ?? 0,
            "after_dangerous_area"       => $staffInfoSalary['dangerous_area'] ?? 0,
            "after_house_rental"         => $staffInfoSalary['house_rental'] ?? 0,
            "after_island_allowance"     => $staffInfoSalary['island_allowance'] ?? 0,
            "after_gasoline_allowance"   => $staffInfoSalary['gasoline_allowance'] ?? 0,
            "data_source"                => 2,
            "after_working_day_rest_type" => $after_working_day_rest_type,
            "before_working_day_rest_type" => $before_working_day_rest_type,
        ];

        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            //减hc
            (new HcServer($this->lang, $this->timezone))->updateHc([
                'id'            => $hcId,
                'surplusnumber' => $hcInfo->surplusnumber ?? 0,
                'demandnumber'  => $hcInfo->demandnumber ?? 0,
            ]);

            $this->getDI()->get('logger')->write_log("param ====:".json_encode($param), 'info');
            $jobTransferId = $this->jobtransfer->addJobtransfer($param);
            if (empty($jobTransferId)) {
                throw new \Exception($this->getTranslation()->_('jobtransfer_0004'));
            }

            //指定子审批流
            $flowCode = $this->getWorkflowCode([
                'category' => $afterStoreData["category"],
                'source'   => 2,
            ]);


//            if (in_array($afterStoreData["category"], $hubCategoryArr)) {
//                $afterDepartmentId = (new SettingEnvServer())->getSetVal('dept_hub_management_id');
//            } elseif (in_array($afterStoreData["category"], $shopCategoryArr)) {
//                $afterDepartmentId = (new SettingEnvServer())->getSetVal('dept_shop_management_id');
//            } else {
//                $afterDepartmentId = (new SettingEnvServer())->getSetVal('dept_network_management_id');
//            }
            //获取一级部门
            $afterDepartmentId = (new SysDepartmentServer())->searchSysDeptId($departmentId);

            //添加转岗申请
            $ret = (new ApprovalServer($this->lang, $this->timezone))->create($jobTransferId,
                enums::$audit_type['TFOA'], $submitter_id, null, [
                    "Head_store_ids"         => [$staffInfo["store_id"], $storeId],
                    "store_id"               => $staffInfo["store_id"],
                    "flow_code"              => $flowCode ?? 1,
                    "department_id"          => $staffInfo["department_id"],
                    "transfer_department_id" => $staffInfo["department_id"],
                    //转岗人对应的hrbp
                    "Array_department_id"    => [$staffInfo["department_id"], $departmentId, $afterDepartmentId],
                    //转岗前部门，转岗后部门，转岗后一级部门
                ]);
            if ($ret === false) {
                $db->rollback();
            } else {
                $db->commit();
            }
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log('addJobtransfer:'.$e->getMessage().$e->getTraceAsString(),
                'notice');
            return $this->checkReturn(-3, $e->getMessage());
        }

        return $this->checkReturn(["id" => $jobTransferId]);
    }

    /**
     * 获取子审批流
     * @param array $paramIn
     * @return int
     */
    private function getWorkflowCode($paramIn = [])
    {
        $category = $paramIn['category'];
        $source   = $paramIn['source'];

        $shopCategoryArr    = explode(",", $this->shopCategory);
        $hubCategoryArr     = explode(",", $this->hubCategory);
        $networkCategoryArr = explode(",", $this->networkCategory);

        if (in_array($category, $hubCategoryArr)) {
            $flowCode = $source == 2 ? 6 : 3;
        } elseif (in_array($category, $networkCategoryArr)) {
            $flowCode = $source == 2 ? 4 : 1;
        } elseif (in_array($category, $shopCategoryArr)) {
            $flowCode = $source == 2 ? 5 : 2;
        }

        return $flowCode;
    }

    /**
     * 获取转岗流程
     * @param array $paramIn
     * @return array
     */
    public function getJobTransferOperateLog($paramIn = []): array
    {
        $jobTransferId = $paramIn['id'];
        if (empty($jobTransferId)) {
            return [];
        }

        $logData = JobTransferOperateLogModel::find([
            'conditions' => 'pid = :job_transfer_id:',
            'bind'       => [
                'job_transfer_id' => $jobTransferId,
            ],
            'order'      => "id desc",
        ])->toArray();

        $t = $this->getTranslation();
        foreach ($logData as &$v) {
            $failure_reason       = json_decode($v['failure_reason'], true);
            $v['state_title']     = isset($v['state']) && $v['state'] ? $t->_("job_transfer_state." . $v['state']) : "";
            $v['operate_title']   = $t->_("job_transfer_operate." . $v['operate_id']);
            $v['failure_reason']  = $failure_reason ? $failure_reason[$this->lang] : $v['failure_reason'];
            $v['operate_content'] = $this->getOperateContent($v);
            $v['failure_reason']  = $v['operate_id'] == JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_ROLE ? '' : $v['failure_reason'];
            $v['operate_content'] = $v['operate_title'] . " " . $v['operate_content'];
        }

        return $logData;
    }

    /**
     * @param $data
     * @return void
     */
    private function getOperateContent($data)
    {
        $operateId      = $data['operate_id'];
        $failureReason  = $data['failure_reason'];
        $operateContent = $data['operate_content'];
        switch ($operateId) {
            case JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_ROLE:
                $content = $failureReason;
                break;
            case JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_TRANSFER_INFO:
                $content = $this->reverseContent($operateContent);
                break;
            default:
                $content = $operateContent;
                break;
        }
        return $content;
    }

    private function reverseContent($content)
    {
        $contentArr = json_decode($content, true);
        if (empty($contentArr)) {
            return '';
        }
        $roleInfo = [];
        $columns  = array_column($contentArr, 'column');
        if (in_array('after_role_ids', $columns)) {
            $roleInfo = RolesModel::find(['columns' => "id,name as name_zh,name_th,name_en"])->toArray();
            $roleInfo = array_column($roleInfo, null, 'id');
        }
        $responseContent = [];
        foreach ($contentArr as $value) {
            $translationKey = JobTransferEnums::$change_column_translation_key_map[$value['column']] ?? '';
            $columnName     = $this->getTranslation()->_($translationKey);

            if ($value['column'] == 'after_role_ids') {
                $before = $this->genRoleName($value['before'], $roleInfo);
                $after  = $this->genRoleName($value['after'], $roleInfo);
            } else {
                if ($value['column'] == 'after_working_day_rest_type') {
                    $before = $this->getTranslation()->_('working_day_rest_type_' . $value['before']);
                    $after  = $this->getTranslation()->_('working_day_rest_type_' . $value['after']);
                } else {
                    $before = $value['before'];
                    $after  = $value['after'];
                }
            }
            $responseContent[] = sprintf('%s:%s ==> %s', $columnName, $before, $after);
        }
        return join('; ', $responseContent);
    }

    private function genRoleName($data, $roleInfo)
    {
        $result = is_string($data) && strlen($data) > 0
            ? array_map(function ($v) use ($roleInfo) {
                if ($this->lang == 'zh-CN') {
                    return $roleInfo[$v]['name_zh'];
                } else {
                    if ($this->lang == 'th') {
                        return $roleInfo[$v]['name_th'];
                    } else {
                        return $roleInfo[$v]['name_en'];
                    }
                }
            }, explode(',', $data))
            : '';
        return !empty($result) ? join(',', $result): '';
    }

    /**
     * 保存操作记录
     * @param array $paramIn
     * operate_id: 1-立即转岗 2-系统自动转岗 3-修改日期 4-修改hc
     * @return bool
     */
    public function saveOperateLog($paramIn = []): bool
    {
        $jobTransferId = $paramIn['id'];
        $staffId       = $paramIn['staff_info_id'];
        $operateId     = $paramIn['operate_id'];
        $state         = $paramIn['state'];
        $failureReason = $paramIn['failure_reason'] ?? '';
        $content       = $paramIn['content'] ?? '';

        //获取操作人信息
        $stffInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $staffId],
        ]);

        if (isset($stffInfo->node_department_id) && $stffInfo->node_department_id) {
            $deptInfo = SysDepartmentModel::findFirst([
                'conditions' => "id = :department_id:",
                'bind'       => [
                    'department_id' => $stffInfo->node_department_id,
                ],
                'columns'    => "id,name",
            ]);
        }

        if (isset($stffInfo->job_title) && $stffInfo->job_title) {
            $jobInfo = HrJobTitleModel::findFirst($stffInfo->job_title);
        }

        $log = new JobTransferOperateLogModel();
        $log->setPid($jobTransferId);
        $log->setStafInfoId($staffId);
        $log->setStaffName($stffInfo->name ?? '');
        $log->setDepartmentId($stffInfo->node_department_id ?? null);
        $log->setDepartmentName($deptInfo->name ?? '');
        $log->setOperateId($operateId);
        $log->setPostionId($stffInfo->job_title ?? null);
        $log->setPositionName($jobInfo->job_name ?? '');
        $log->setState($state);
        $log->setFailureReason($failureReason);
        $log->setOperateContent($content);
        $log->save();

        return true;
    }

    /**
     * 设置批次信息
     * @param $params
     * @return bool
     */
    public function setBatchAddNumber($params)
    {
        $batchNumber = $params['batch_number'] ?? '';
        $staffIds    = $params['ids'] ?? '';

        //获取需要转岗的staff_id
        $staffIdsStr = json_encode(['total' => $staffIds, 'success' => [], 'fail' => [], 'fail_msg' => []]);
        $this->getDI()->get('logger')->write_log("setBatchAddNumber staff ids :".$staffIdsStr, 'info');

        //有效时长10分钟
        $this->getDI()->get('redis')->save('batch_add_job_transfer_key'.$batchNumber, $staffIdsStr, 1800);

        return true;
    }

    /**
     * 获取批量添加结果
     * @param $params
     * @return array
     */
    public function getBatchAddProgress($params): array
    {
        $batchNumber = $params['batch_number'] ?? '';
        $redis_obj   = $this->getDI()->get('redis');
        $cacheKey    = 'batch_add_job_transfer_key'.$batchNumber;

        //获取批量添加进程
        $cache = $redis_obj->get($cacheKey);
        if (empty($cache)) {
            $this->getDI()->get('logger')->write_log("getBatchAddProgress batch num is :".$batchNumber, 'info');
            return [
                'code'    => -1,
                'message' => 'invalid batch number', //无效的批次号
            ];
        }

        $res = isset($cache) && $cache ? json_decode($cache, true) : [];
        if (empty($res) || isset($res['total']) && empty($res['total'])) {
            return [
                'success'      => '',
                'fail'         => '',
                'fail_message' => [
                ],
            ];
        }

        return [
            'success'      => $res['success'] ?? [],
            'fail'         => $res['fail'] ?? [],
            'fail_message' => $res['fail_msg'] ?? [],
        ];
    }

    /**
     * 判断薪资项是否被修改
     * @param $detailInfo
     * @param $params
     * @return bool
     */
    protected function isSalaryChanged($detailInfo, $params)
    {
        if (
            $params['after_base_salary'] != $detailInfo['after_base_salary']
            || $params['after_exp_allowance'] != $detailInfo['after_exp_allowance']
            || $params['after_position_allowance'] != $detailInfo['after_position_allowance']
            || $params['after_car_rental'] != $detailInfo['after_car_rental']
            || $params['after_trip_payment'] != $detailInfo['after_trip_payment']
            || $params['after_notebook_rental'] != $detailInfo['after_notebook_rental']
            || $params['after_recommended'] != $detailInfo['after_recommended']
            || $params['after_food_allowance'] != $detailInfo['after_food_allowance']
            || $params['after_dangerous_area'] != $detailInfo['after_dangerous_area']
            || $params['after_house_rental'] != $detailInfo['after_house_rental']
            || $params['after_gasoline_allowance'] != $detailInfo['after_gasoline_allowance']
            || $params['after_island_allowance'] != $detailInfo['after_island_allowance']
            || $params['after_phone_subsidy'] != $detailInfo['after_phone_subsidy']
        ) {
            return true;
        }
        return false;
    }

    /**
     * @param $detailInfo
     * @param $params
     * @return false|string
     */
    public function encodeRejectSalaryDetail($detailInfo, $params)
    {
        $salaryFields = [
            'base_salary',       //基本工资
            'position_allowance',//职位津贴
            'exp_allowance',     //经验津贴
            'food_allowance',    //餐补
            'dangerous_area',    //危险区域津贴
            'notebook_rental',   //电脑补贴
            'house_rental',      //租房津贴
            'car_rental',        //租车津贴
            'trip_payment',      //油补
            'gasoline_allowance',//销售油补
            'recommended',       //推荐补贴
            'island_allowance',  //海岛补贴
        ];

        $salaryFieldsChanged = [];
        foreach ($salaryFields as $v1) {
            $old = ($detailInfo['after_'.$v1] / 100);
            $new = ($params['after_'.$v1] / 100);
            if ($old != $new) {
                $salaryFieldsChanged['job_transfer.'.$v1] = $old.' => '.$new;
            }
        }
        return json_encode($salaryFieldsChanged);
    }

    /**
     * @param $detail
     * @return string
     */
    public function decodeRejectSalaryDetail($detail)
    {
        if (empty($detail)) {
            return '';
        }
        $decoded = json_decode($detail, true);
        if (is_null($decoded)) {
            return $detail;
        }
        if (empty($decoded)) {
            return '';
        }
        $s = '';
        $t = $this->getTranslation();
        foreach ($decoded as $k => $v) {
            $s .= $t[$k].' :'.$v."\n";
        }
        return $s;
    }

    /**
     * 加转岗锁
     * @param string $key1 转岗id
     * @param string $key2 转岗id + 操作人员工号
     * @param int $expire 超时时间
     * @return mixed
     */
    public function setTransferLock(string $key1, string $key2, int $value, int $expire)
    {
        $redis     = $this->getDI()->get('redisLib');
        $LUASCRIPT = <<<LUA
local key1 = KEYS[1]
local key2 = KEYS[2]
local value = ARGV[1]
local ttl = ARGV[2]

if (redis.call('SISMEMBER', key1, value) == 1) then
    return 0
else
    redis.call('SADD', key1, value)
    redis.call('EXPIRE', key, ttl)
    redis.call('SET', key2)
    redis.call('EXPIRE', key, ttl)
end
return 1
LUA;
        return $redis->eval($LUASCRIPT, [$key1, $key2, $value, $expire], 2);
    }

    /**
     * 移除转岗锁
     * @param string $key 转岗id
     * @param string $key2 转岗id + 操作人员工号
     * @param int $value 超时时间
     * @return mixed
     */
    public function remTransferLock(string $key1, string $key2, int $value)
    {
        $redis     = $this->getDI()->get('redisLib');
        $LUASCRIPT = <<<LUA
local key1 = KEYS[1]
local key2 = KEYS[2]
local value = ARGV[1]

if (redis.call('SISMEMBER', key1, value) == 1) then
    redis.call('SREM', key1, value)
    redis.call('DEL', key2)
else
    return 0
end
return 1
LUA;
        return $redis->eval($LUASCRIPT, [$key1, $key2, $value], 2);
    }

    /**
     * 获取立即转岗处理进度
     * @param $params
     * @return bool
     */
    public function getJobtransferProcess($params)
    {
        $transferId = $this->processingDefault($params, 'transfer_id');
        $operatorId = $this->processingDefault($params, 'operator_id');

        $cacheKey = "do_job_transfer_".$transferId.'_'.$operatorId;

        $redis = $this->getDI()->get('redisLib');
        return (bool)$redis->exists($cacheKey);
    }

    /**
     * @description:修改上级和角色
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/8 14:39
     */
    public function updateJobTransferStore($paramIn = [])
    {
        try {
            $roles            = $this->processingDefault($paramIn, 'role_ids', 3);
            $after_manager_id = $this->processingDefault($paramIn, 'after_manager_id', 2);
            $id               = $this->processingDefault($paramIn, 'id', 2);
            $staffId          = $paramIn['staff_info_id'] ?? '';
            $transferInfo     = JobTransferModel::findFirst([
                'conditions' => 'id = :id: ',
                'bind'       => ['id' => $id],
            ]);
            if (empty($after_manager_id)) {
                throw new \Exception('after_manager_id error');
            }
            if (empty($roles)) {
                throw new \Exception('role_ids error');
            }
            if (empty($transferInfo)) {
                throw new \Exception('no valid data');
            }
            //转岗状态为“待转岗”或“转岗失败”，且审核状态为“审批通过”的情况下
            if (!in_array($transferInfo->state, [
                    JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED,
                    JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                ]) || $transferInfo->approval_state != enums::APPROVAL_STATUS_APPROVAL) {
                throw new \Exception('state error');
            }
            //查询上级是否在职
            $jobHandoverStaffInfo = $this->getStaffInfo([
                "staff_id" => $after_manager_id,
            ]);
            if (!$jobHandoverStaffInfo || $jobHandoverStaffInfo["state"] != enums::$service_status['incumbency'] ||
                $jobHandoverStaffInfo["formal"] != 1 || $after_manager_id == $transferInfo->staff_id) {
                throw new \Exception('after_manager_id error ', enums::$ERROR_CODE['1000']);
            }

            $roles = $roles ? implode(',', $roles) : "";  //转岗后角色

            $this->db = $this->getDI()->get("db");
            $this->db->begin();

            $updateData = [
                'after_role_ids'   => $roles,
                'after_manager_id' => $after_manager_id,
            ];
            // 是否编辑上级，如果是，则标记。 转岗时，不重新获取。
            if($transferInfo->after_manager_id != $after_manager_id) {
                $updateData['is_edit_manager_id'] = JobTransferModel::IS_EDIT_MANAGER_ID_YES;
            }
            $success = $this->db->updateAsDict("job_transfer", $updateData,
                [
                    'conditions' => "id = ?",
                    'bind'       => [
                        $id,
                    ],
                ]
            );
            if (!$success) {
                $this->db->rollback();
                throw new \Exception('update error');
            }
            $before_role_name = '';
            $roles_name       = '';
            $roleTranslation  = $this->getRolesTranslation();
            //获取以前的角色
            if (isset($transferInfo->after_role_ids) && $transferInfo->after_role_ids) {
                $roles_ids = [];
                $roleInfo  = RolesModel::find([
                    'columns'    => "id,name,name_th,name_en",
                    'conditions' => "id in ({role_id:array}) and status = 1",
                    'bind'       => [
                        'role_id' => explode(',', $transferInfo->after_role_ids),
                    ],
                ])->toArray();
                foreach ($roleInfo as $role) {
                    $roleName    = $this->lang == 'zh-CN' ? ($roleTranslation[$role['id']]['role_name_zh'] ?? "")
                        : ($this->lang == 'en' ? $roleTranslation[$role['id']]['role_name_en'] ?? "" : $roleTranslation[$role['id']]['role_name_th'] ?? '');
                    $roles_ids[] = $roleName;
                }
                $before_role_name = $roles_ids ? implode(',', $roles_ids) : "";
            }
            //获取现在的角色
            if (isset($roles) && $roles) {
                $roles_ids = [];
                $roleInfo  = RolesModel::find([
                    'columns'    => "id,name,name_th,name_en",
                    'conditions' => "id in ({role_id:array}) and status = 1",
                    'bind'       => [
                        'role_id' => explode(',', $roles),
                    ],
                ])->toArray();
                foreach ($roleInfo as $role) {
                    $roleName    = $this->lang == 'zh-CN' ? ($roleTranslation[$role['id']]['role_name_zh'] ?? "")
                        : ($this->lang == 'en' ? $roleTranslation[$role['id']]['role_name_en'] ?? "" : $roleTranslation[$role['id']]['role_name_th'] ?? '');
                    $roles_ids[] = $roleName;
                }
                $roles_name = $roles_ids ? implode(',', $roles_ids) : "";
            }
            $failure_reason = [
                'zh-CN' => '',
                'en'    => '',
                'th'    => '',
            ];
            if ($transferInfo->after_manager_id != $after_manager_id) {
                $failure_reason['zh-CN'] .= '转岗后直线上级:'.$transferInfo->after_manager_id.' ==> '.$after_manager_id." , ";
                $failure_reason['en']    .= 'Straight line superior after transfer:'.$transferInfo->after_manager_id.' ==> '.$after_manager_id." , ";
                $failure_reason['th']    .= 'ผู้บังคับบัญชาหลังโอนย้าย:'.$transferInfo->after_manager_id.' ==> '.$after_manager_id." , ";
            }

            if ($transferInfo->after_role_ids != $roles) {
                $failure_reason['zh-CN'] .= " 转岗后角色:".$before_role_name.' ==> '.$roles_name;
                $failure_reason['en']    .= " Role after transfer:".$before_role_name.' ==> '.$roles_name;
                $failure_reason['th']    .= " บทบาทหลังโอนย้าย:".$before_role_name.' ==> '.$roles_name;
            }
//            $failure_reason = [
//                'zh-CN' => '转岗后直线上级:'. $transferInfo->after_manager_id . ' ==> ' . $after_manager_id . " , 转岗后角色:". $before_role_name . ' ==> ' . $roles_name,
//                'en' => 'Straight line superior after transfer:'. $transferInfo->after_manager_id . ' ==> ' . $after_manager_id . " , Role after transfer:". $before_role_name . ' ==> ' . $roles_name,
//                'th' => 'ผู้บังคับบัญชาหลังโอนย้าย:'. $transferInfo->after_manager_id . ' ==> ' . $after_manager_id . " , บทบาทหลังโอนย้าย:". $before_role_name . ' ==> ' . $roles_name,
//            ];

            $success = $this->saveOperateLog([
                'id'             => $id,
                'staff_info_id'  => $staffId,
                'operate_id'     => 5, // 1-立即转岗 2-系统自动转岗 3-修改日期 4-修改hc  5 修改上级和角色
                'state'          => $transferInfo->state,
                'failure_reason' => json_encode($failure_reason),
                'content'        => $transferInfo->after_manager_id.' ==> '.$after_manager_id." ,  ".$transferInfo->after_role_ids.' ==> '.$roles,
            ]);
            if (!$success) {
                $this->db->rollback();
                throw new \Exception('add error');
            }
            $this->db->commit();
            return $this->checkReturn([]);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('updateJobTransferStore:'.$e->getMessage(), 'notice');
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * 编辑转岗信息
     * @param $paramIn
     * @return array
     */
    public function editJobTransferData($paramIn = [])
    {
        $jobTransferId = $paramIn['id'];
        $operatorId    = $paramIn['operator_id'];
        $result        = true;

        $params = [
            'after_date'                  => $paramIn['after_date'],
            'after_manager_id'            => $paramIn['after_manager_id'],
            'after_role_ids'              => $paramIn['after_role_ids'],
            'after_working_day_rest_type' => $paramIn['after_working_day_rest_type'],
        ];

        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            //获取转岗详情
            $jobTransferInfo = JobTransferModel::findFirst($jobTransferId);
            if (empty($jobTransferInfo)) {
                throw new BusinessException(sprintf('id %d not exist', $jobTransferId));
            }
            $transferChange = $this->getTransferColumnsChangeInfo($jobTransferInfo->toArray(), $params);
            if (!isset($transferChange['value'])) {
                throw new ValidationException('data not change');
            }
            //如上级变更，转岗时不再重新获取
            if ($jobTransferInfo->after_manager_id != $paramIn['after_manager_id']) {
                $transferChange['value']['is_edit_manager_id'] = JobTransferModel::IS_EDIT_MANAGER_ID_YES;
            }

            $this->db->updateAsDict("job_transfer",
                $transferChange['value'], [
                    'conditions' => "id = ?",
                    'bind'       => [
                        $jobTransferId,
                    ],
                ]
            );
            if (!empty($transferChange['change_info'])) {
                $content = json_encode($transferChange['change_info']);
            } else {
                $content = '';
            }

            $this->saveOperateLog([
                'id'            => $jobTransferId,
                'staff_info_id' => $operatorId,
                'operate_id'    => JobTransferEnums::JOB_TRANSFER_ACTION_EDIT_TRANSFER_INFO,
                'state'         => $jobTransferInfo->state,
                'content'       => $content,
            ]);

            $db->commit();
        } catch(BusinessException $be) {
            $db->rollback();
            $this->logger->write_log('editJobTransferData err :' . $be->getMessage());
            $result = false;
        } catch (ValidationException $ve) {
            $db->rollback();
            $this->logger->write_log('editJobTransferData validate err :' . $ve->getMessage(), 'info');
            $result = false;
        }
        return $this->checkReturn(['data' => $result]);
    }

    /**
     * 获取字段变更
     * @param $before
     * @param $after
     * @return array
     */
    private function getTransferColumnsChangeInfo($before, $after)
    {
        $result  = [];
        $columns = ['after_date', 'after_manager_id', 'after_role_ids', 'after_working_day_rest_type'];
        foreach ($columns as $column) {
            if (isset($before[$column]) && isset($after[$column]) && $before[$column] != $after[$column]) {
                $result['value'][$column]       = $after[$column];
                $result['change_info'][] = [
                    'column' => $column,
                    'after'  => $after[$column],
                    'before' => $before[$column],
                ];
            }
        }
        return $result;
    }

    /**
     * @description:获取转岗后的直线上级
     * @param $after_store_id
     * @param int $after_demp_id
     * @param int $after_job_title
     * @param string $afterDate
     * @param int $transfer_staff_info_id 被转岗员工ID
     * @return int|mixed|string :
     */
    public function getafterManagerIdInfo($after_store_id,$after_demp_id=0, $after_job_title = 0, $afterDate = '', $transfer_staff_info_id = 0)
    {
        $afterManagerId = 0;
        try {
            //获取网点信息
            $afterStoreData = $this->jobtransfer->getStoreInfo([
                "store_id" => $after_store_id,
            ]);

            //获取转岗后直线上级
            $afterManagerId = $this->getJobtransferStoreManager([
                "category" => $afterStoreData["category"],
                "store_id" => $after_store_id,
            ]);

            $shopCategoryArr    = explode(",", $this->shopCategory);
            $hubCategoryArr     = explode(",", $this->hubCategory);
            $networkCategoryArr = explode(",", $this->networkCategory);
            if (empty($afterManagerId)) {

                //当转岗网点没有网点负责人时，转岗员工转至新网点后直线上级获取规则
                //[[1]SP,[2]DC,[10]BDC]对应网点的片区经理district manager
                //[[4]shop(pickup-only),[5]shop(pickup&delivery),[7]ushop]对应网点的大区经理area manager
                if (in_array($afterStoreData["category"], $hubCategoryArr)) {
                    throw new \Exception($this->getTranslation()->_('jobtransfer_0005'), enums::$ERROR_CODE['1000']);
                } else {
                    if (in_array($afterStoreData["category"], $networkCategoryArr)) {
                        //按组织架构取值
                        $pieceMangerInfo = SysManagePieceModel::findFirst([
                            'conditions' => 'id = :manage_piece:',
                            'bind'       => ['manage_piece' => $afterStoreData['manage_piece']],
                            'column'     => 'id,manager_id',
                        ]);
                        $afterManagerId  = $pieceMangerInfo->manager_id ?? $afterManagerId;
                    } else {
                        if (in_array($afterStoreData["category"], $shopCategoryArr)) {
                            //按组织架构取值
                            $regionMangerInfo = SysManageRegionModel::findFirst([
                                'conditions' => 'id = :manage_region:',
                                'bind'       => ['manage_region' => $afterStoreData['manage_region']],
                                'column'     => 'id,manager_id',
                            ]);
                            $afterManagerId   = $regionMangerInfo->manager_id ?? $afterManagerId;
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            if ($e->getCode() == enums::$ERROR_CODE['1000']) {
                $this->getDI()->get('logger')->write_log('getafterManagerIdInfo:'.$e->getMessage().$e->getTraceAsString(),
                    'error');
            } else {
                $this->getDI()->get('logger')->write_log('getafterManagerIdInfo:'.$e->getMessage().$e->getTraceAsString());
            }
            return $afterManagerId;

        }

        return $afterManagerId;
    }


    /**
     * @description:获取可以交接的员工id
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/13 11:53
     */
    public function getJobHandoverStaffId($staffInfo)
    {
        $jobHandoverStaffIds = [];
        $shopCategoryArr     = explode(",", $this->shopCategory);
        $hubCategoryArr      = explode(",", $this->hubCategory);
        $networkCategoryArr  = explode(",", $this->networkCategory);

        //NW and  bulky：可填写负责转岗员工原网点的DM、AM
        if (in_array($staffInfo["category"], $networkCategoryArr)) {
            $pieceMangerInfo = SysManagePieceModel::findFirst([
                'conditions' => 'id = :manage_piece:',
                'bind'       => ['manage_piece' => $staffInfo['manage_piece']],
                'column'     => 'id,manager_id',
            ]);
            if ($pieceMangerInfo->manager_id) {
                $jobHandoverStaffIds[] = $pieceMangerInfo->manager_id;
            }

            $regionMangerInfo = SysManageRegionModel::findFirst([
                'conditions' => 'id = :manage_region:',
                'bind'       => ['manage_region' => $staffInfo['manage_region']],
                'column'     => 'id,manager_id',
            ]);
            if ($regionMangerInfo->manager_id) {
                $jobHandoverStaffIds[] = $regionMangerInfo->manager_id;
            }
            //SHOP：可填写负责转岗员工原网点的AM
        } else {
            if (in_array($staffInfo["category"], $shopCategoryArr)) {
                $regionMangerInfo = SysManageRegionModel::findFirst([
                    'conditions' => 'id = :manage_region:',
                    'bind'       => ['manage_region' => $staffInfo['manage_region']],
                    'column'     => 'id,manager_id',
                ]);
                if ($regionMangerInfo->manager_id) {
                    $jobHandoverStaffIds[] = $regionMangerInfo->manager_id;
                }
                //HUB：可填写转岗员工所属部门负责人
            } else {
                if (in_array($staffInfo["category"], $hubCategoryArr)) {
                    //所属部门的负责人
                    $departmentInfo = SysDepartmentModel::findFirst($staffInfo['department_id']);
                    if ($departmentInfo->manager_id) {
                        $jobHandoverStaffIds[] = $departmentInfo->manager_id;
                    }
                }
            }
        }
        return $jobHandoverStaffIds;
    }


    /**
     * 获取消息黑名单列表
     * @return array
     */
    public function getBlackList()
    {
        $blackList = (new SettingEnvServer())->getSetVal('notice_black_list');

        return $blackList ? explode(',', $blackList) : [];
    }


    /**
     * @description: 创建验证
     *
     * @param $staffId //转岗人
     * @param $jobHandoverStaffId //交接人*
     * @param $staffInfo //转岗人 info
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/3/22 20:02
     * @throws BusinessException
     */

    public function checkVerification($staffId, $jobHandoverStaffId, $staffInfo)
    {
        //[2-2]校验交接员工信息
        //交接人需要在职、在编、非子账号
        //交接人不能是转岗员工自己、需要在职
        $jobHandoverStaffInfo = $this->getStaffInfo([
            "staff_id" => $jobHandoverStaffId,
        ]);
        //如果不是本网点的员工
        $jobHandoverStaffIds = [];
        if (!isset($jobHandoverStaffInfo["store_id"]) || $jobHandoverStaffInfo["store_id"] != $staffInfo["store_id"]) {
            $jobHandoverStaffIds = $this->getJobHandoverStaffId($staffInfo);
        }

        if (!$jobHandoverStaffInfo || $jobHandoverStaffInfo["state"] != enums::$service_status['incumbency'] ||
            $jobHandoverStaffInfo["formal"] != 1 || $jobHandoverStaffInfo["is_sub_staff"] != 0 ||
            ($jobHandoverStaffInfo["store_id"] != $staffInfo["store_id"] && !in_array($jobHandoverStaffId,
                    $jobHandoverStaffIds) && $staffInfo['hr_staff_sys_store_id'] != '-1') ||
            $jobHandoverStaffId == $staffId) {
            throw new BusinessException($this->getTranslation()->_('jobtransfer_0020'), enums::$ERROR_CODE['1000']);
        }


        //如果是总部网点员工    不是同一个部门   并且交接人 不是部门负责人
        if ($staffInfo['hr_staff_sys_store_id'] == '-1' && $jobHandoverStaffInfo['hr_staff_node_department_id'] != $staffInfo['hr_staff_node_department_id']) {
            //查询是否为部门负责人
            $deptinfo = SysDepartmentModel::findFirst([
                'conditions' => "manager_id = :staff_info_id: and id = :current_department_id:",
                'bind'       => [
                    'staff_info_id'         => $jobHandoverStaffId,
                    'current_department_id' => $staffInfo['hr_staff_node_department_id'],
                ],
            ]);
            if (empty($deptinfo)) {
                //工作交接人错误，请填写您部门内的员工
                throw new BusinessException($this->getTranslation()->_('jobtransfer_0050'), enums::$ERROR_CODE['1000']);
            }
        }

        //[2-3]校验是否重复添加
        $_re = $this->jobtransfer->getJobtransferInfo([
            "staff_id" => $staffId,
            "state"    => 1,
        ]);
        if ($_re) {
            throw new BusinessException($this->getTranslation()->_('5202'), enums::$ERROR_CODE['1000']);
        }
        $_re = $this->jobtransfer->getJobtransferInfo([
            "staff_id" => $staffId,
        ]);
        if (!empty($_re) && $_re['approval_state'] == enums::$audit_status['approved'] && $_re['state'] == enums::$job_transfer_state['to_be_transfered']) {
            throw new BusinessException($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
        }

        return true;
    }


    /**
     * @description:转岗成功发送邮件
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/9/15 15:00
     */
    public function doJobTransferEmail($staff_data = [])
    {
        //发送英文
        $lang = 'en';
        //获取邮箱
        $emails = (new SettingEnvServer())->getSetVal('do_job_transfer_email');
        $emails = explode(',', $emails);
        if (empty($emails)) {
            $this->getDI()->get('logger')->write_log('doJobTransferEmail 失败,没有找到邮箱!');
            return false;
        }


        //：{工号}{转岗员工姓名}，from{转岗前部门}-{转岗前网点}-{转岗前职位} transfer to {转岗后部门}-{转岗后网点}-{转岗后职位} ,
        //the approve number is{审批编号}，approve status is{审批状态}, transfer date is {转岗日期}.
        // Please pay attention to updating the person in charge of branch on OA/MS system in time.

        //:  {staff_id} {staff_name}，from {current_department_name}-{current_store_name}-{current_position_name}
        // transfer to {after_department_name}-{after_store_name}-{after_position_name} ,
        //the approve number is {serial_no}，approve status is {audit_state},
        // transfer date is {after_date}. Please pay attention to updating the person in charge of branch on OA/MS system in time.
        $content = str_replace([
            '{staff_id}',
            '{staff_name}',
            '{current_department_name}',
            '{current_store_name}',
            '{current_position_name}',
            '{after_department_name}',
            '{after_store_name}',
            '{after_position_name}',
            '{serial_no}',
            '{audit_state}',
            '{after_date}',
        ], [
            $staff_data['staff_id'],
            $staff_data['staff_name'],
            $staff_data['current_department_name'],
            $staff_data['current_store_name'],
            $staff_data['current_position_name'],
            $staff_data['after_department_name'],
            $staff_data['after_store_name'],
            $staff_data['after_position_name'],
            $staff_data['serial_no'],
            $this->getTranslation($lang)->_('audit_status.'.$staff_data['approval_state']),
            $staff_data['after_date'],
        ], $this->getTranslation($lang)->_('job_transfer_email_content'));

        $title = str_replace([
            '{staff_id}',
        ], [
            $staff_data['staff_id'],
        ], $this->getTranslation($lang)->_('job_transfer_email_title'));

        $ret = (new MailServer())->send_mail($emails, $title, $content);
        $this->getDI()->get('logger')->write_log('doJobTransferEmail to:'.implode(',',
                $emails).', content is:'.$content.',result is:'.$ret, 'info');
        return $ret;
    }

    /**
     * 检查 上级 是否是 待离职状态，如果是 离职日期 要晚于 员工转岗日期
     * @param $staffId
     * @param $after_date
     * @return int
     */
    public function checkManager($staffId, $after_date)
    {
        $managerInfo = $this->jobtransfer->getBaseStaffInfo($staffId);
        if (empty($managerInfo)) {
            return 0;
        }

        if(in_array($managerInfo['state'], [HrStaffInfoModel::STATE_2, HrStaffInfoModel::STATE_SUSPENSION])) {
            return 0;
        }

        if ($managerInfo['state'] == HrStaffInfoModel::STATE_ON_JOB && $managerInfo['wait_leave_state'] != HrStaffInfoModel::WAITING_LEAVE) {
            return $staffId;
        }
        $leave_date = date('Y-m-d', strtotime($managerInfo['leave_date']));
        if (strtotime($leave_date) > strtotime($after_date)) {
            return $staffId;
        }
        //待离职状态，离职日期 小于 转岗日期
        $this->logger->write_log([
            'job_transfer_check_manager_id' => $staffId,
            'leave_date'                    => $leave_date,
            'after_date'                    => $after_date,
        ], 'info');
        return 0;
    }

    //查找部门负责人
    public function getDepartmentManger($departmentId)
    {
        if (empty($departmentId)) {
            return 0;
        }
        $departmentInfo = SysDepartmentModel::findFirst($departmentId);
        return empty($departmentInfo->manager_id) ? 0 : $departmentInfo->manager_id;
    }

    /**
     * 查找 部门 链上区间 部门负责人
     * @param $departmentId
     * @param $firstDepartment
     * @param $after_date
     * @return int
     */
    public function getGraduallyDeptManager($departmentId, $firstDepartment, $after_date)
    {
        $deptIds = [$departmentId, $firstDepartment];
        $list    = (new SysDepartmentServer())->getDepartmentByIds($deptIds);

        $firstDepartmentLink = explode('/', $list[$firstDepartment]['ancestry_v3']);
        $departmentLink      = explode('/', $list[$departmentId]['ancestry_v3']);

        $firstDepartmentLink[] = $departmentId;
        //部门链 倒序 排序
        $partDeptIds           = array_reverse(array_values(array_diff($departmentLink, $firstDepartmentLink)));
        if (empty($partDeptIds)) {
            return 0;
        }

        $partDeptIdsList = (new SysDepartmentServer())->getDepartmentByIds($partDeptIds);
        foreach ($partDeptIds as $oneDeptId) {
            if (empty($partDeptIdsList[$oneDeptId]['manager_id'])) {
                continue;
            }

            if ($this->checkManager($partDeptIdsList[$oneDeptId]['manager_id'], $after_date)) {
                return $partDeptIdsList[$oneDeptId]['manager_id'];
            }
        }

        return 0;
    }

    /**
     * 转岗获取网点下拉列表》》》》组织架构上部门关联的网点
     * @param $departmentId
     * @return array
     */
    public function getStoreListByDepartmentId($departmentId)
    {
        $returnData['data'] = [];
        if (empty($departmentId)) {
            return $returnData;
        }
        $departmentIds = (new SysDepartmentModel())->getSpecifiedDeptAndSubDept($departmentId);

        $storeIds = (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getDepartmentStoreRelationInfo($departmentIds);


        if (!empty($storeIds)) {
            //获取网点信息
            $data       = SysStoreModel::find([
                'conditions' => "id in ({store_ids:array})",
                'bind'       => [
                    'store_ids' => $storeIds,
                ],
                'columns'    => 'id, name',
            ])->toArray();

            $returnData['data'] = $data;
        }

        return $returnData;
    }

    /**
     * 获取原因下拉列表
     * @Return array
     */
    public function getTotalTransferReason(): array
    {
        $t = $this->getTranslation();
        $list = [
            JobTransferModel::JOB_TRANSFER_REASON_STORE_INTEGRATION        => $t->_('job_transfer_reason.1'),
            JobTransferModel::JOB_TRANSFER_REASON_STORE_NEWLY_OPENED       => $t->_('job_transfer_reason.2'),
            JobTransferModel::JOB_TRANSFER_REASON_ADJUST_SERVICE_AREA      => $t->_('job_transfer_reason.3'),
            JobTransferModel::JOB_TRANSFER_REASON_STORE_UPGRADE            => $t->_('job_transfer_reason.4'),
            JobTransferModel::JOB_TRANSFER_REASON_INSUFFICIENT_STAFF       => $t->_('job_transfer_reason.5'),
            JobTransferModel::JOB_TRANSFER_REASON_CAREER_PLANNING          => $t->_('job_transfer_reason.6'),
            JobTransferModel::JOB_TRANSFER_REASON_OPERATIONAL_REQUIREMENTS => $t->_('job_transfer_reason.7'),
            JobTransferModel::JOB_TRANSFER_REASON_ORGANIZATION_RESTRUCTURE => $t->_('job_transfer_reason.8'),
            JobTransferModel::JOB_TRANSFER_REASON_OTHERS                   => $t->_('job_transfer_reason.99'),
        ];

        foreach ($list as $key => $value) {
            $data[] = [
                'key'   => $key,
                'value' => $value,
            ];
        }
        return $data;
    }

    /**
     * @description 获取指定工号待审批或待转岗的数据
     * @param $staff_info_id
     * @return array
     */
    public function getJobTransferData($staff_info_id): array
    {
        //job_transfer表中拿到数据相关员工的转岗状态
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_id,id,after_department_id,after_store_id,after_position_id,approval_state,state');
        $builder->from(['r' => JobTransferModel::class]);
        $builder->where('deleted = 0');
        $builder->andWhere('staff_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        $builder->inWhere('approval_state', [enums::APPROVAL_STATUS_PENDING, enums::APPROVAL_STATUS_APPROVAL]);
        $builder->andWhere('state = :state:', ['state' => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED]);
        $info = $builder->getQuery()->getSingleResult();
        if (empty($info)) {
            return [];
        }
        return $info->toArray();
    }

    /**
     * 激活转岗到转岗确认
     * @param $params
     * @return array
     * @throws ValidationException
     * @throws \ReflectionException
     */
    public function activateTransfer($params)
    {
        $jobTransferId = $params['id'];
        $operator_id   = $params['operator_id'];

        $jobTransferServer = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        $jobTransferServer->activate($jobTransferId, $operator_id);

        return $this->checkReturn([]);
    }

    /**
     * Activates a job transfer.
     */
    public function activate($job_transfer_id, $operator_id): bool
    {
        return true;
    }
}