<?php
/**
 * Author: Bruce
 * Date  : 2023-12-01 14:32
 * Description:
 */

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\AttendanceFaceIgnoreStaffModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\StaffHikvisionModel;
use FlashExpress\bi\App\Repository\HikvisionSyncTaskRepository;
use FlashExpress\bi\App\Repository\StaffHikvisionRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;
use FlashExpress\bi\App\Server\Osm\OutsourcingStaffServer;

class StaffHikServer extends BaseServer
{
    public $timezone;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 是否有 海康人脸照片 的入口权限
     * @param $params
     * @return bool
     */
    public function getHikInputPermission($params)
    {
        $staffIdsPermission = (new SettingEnvServer())->getSetVal('hikcentral_face_picture_staffs', ',');

        if (in_array($params['staff_id'], $staffIdsPermission)) {
            return true;
        }

        return false;
    }

    /**
     * 获取列表数据
     * @param $params
     * @param $isTotal
     * @return array
     */
    public function getStaffList($params, $isTotal = false)
    {
        $params['page_num']      = empty($params['page_num']) ? 1 : $params['page_num'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size'] = ($params['page_size'] > 100) ? 100 : $params['page_size'];

        $result = ['total' => 0, 'list' => []];
        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoOne($params['userinfo']['staff_id'],
            'staff_info_id, name, sex, nationality, identity, sys_store_id, job_title');

        //有入口权限，但是，当前登录人所在网点，不是 HUB 导入海康数据 配置的网点内。
        $hub_face_store = (new SettingEnvServer())->getSetVal('hub_face_store', ',');
        if (!in_array($staffInfo['sys_store_id'], $hub_face_store)) {
            return $result;
        }
        $params['sys_store_id'] = $staffInfo['sys_store_id'];

        //未上传 = 同步失败 + 未上传。
        //需要查出，已同步,同步处理中的，去除掉，才是 同步失败 + 未上传过的。
        $isSyncStaffIds = [];
        if ($params['is_sync'] == StaffHikvisionModel::SYNC_NO) {
            //查出 已同步,同步处理中的
            $isSyncStaffIds = $this->getIsSyncStaffIds($params);
        }
        //hub刷脸考勤管理-特殊工号不导入海康数据
        $ignoreStaffRes     = AttendanceFaceIgnoreStaffModel::find([
            'columns'    => "staff_info_id",
            'conditions' => "is_deleted = 0",
        ])->toArray();
        $ignoreStaffResToId = !empty($ignoreStaffRes) ? array_column($ignoreStaffRes, 'staff_info_id') : [];
        //不需要展示的工号：特殊工号不导入海康数据 + 已同步,同步处理中的。
        $params['not_need_display_staff_ids'] = !empty($ignoreStaffResToId) ? array_values(array_unique(array_merge($isSyncStaffIds,
            $ignoreStaffResToId))) : $ignoreStaffResToId;

        $staffHikvisionRepository = (new StaffHikvisionRepository($this->lang));
        $total                    = $staffHikvisionRepository->getListQuery($params, true);
        $result['total']          = !empty($total) ? intval($total['count']) : 0;

        if ($result['total'] == 0) {
            return $result;
        }
        //只返回总数。
        if($isTotal) {
            return $result;
        }

        $list = $staffHikvisionRepository->getListQuery($params);
        if ($list) {
            $list = $this->formatList($list, $params['is_sync']);
        }

        $result['list'] = $list;

        return $result;
    }


    /**
     * 格式化 数据
     * @param $list
     * @param int $is_sync
     * @return mixed
     */
    public function formatList($list, $is_sync = StaffHikvisionModel::SYNC_NO)
    {
        $taskResultToId = [];
        if (empty($is_sync)) {
            $staff_hikvision_ids = array_column($list, 'staff_hikvision_id');
            $taskResult          = HikvisionSyncTaskRepository::getHikvisionSyncTaskList($staff_hikvision_ids);
            $taskResultToId      = array_column($taskResult, NULL,'staff_hikvision_id');
        }
        $job_title = array_column((new SysServer($this->lang, $this->timeZone))->getJobTitleList(), 'job_name', 'id');

        foreach ($list as &$oneData) {
            $oneData['sex_text']       = $oneData['sex'] == HrStaffInfoModel::SEX_MALE ? $this->getTranslation()->_('4900') : $this->getTranslation()->_('4901');
            $oneData['job_title_name'] = $job_title[$oneData['job_title']] ?? '';
            $oneData['nationality']    = isset(HrStaffInfoModel::NATIONALITY[$oneData['nationality']]) ? $this->getTranslation()->_(HrStaffInfoModel::NATIONALITY[$oneData['nationality']]) : '';
            $oneData['fail_reason']    = isset($taskResultToId[$oneData['staff_hikvision_id']]) ? $this->getTranslation()->_('re_upload') : '';
        }
        return $list;
    }

    /**
     * 获取 同步过的工号
     * @param $params
     * @return array
     */
    public function getIsSyncStaffIds($params)
    {
        $staffHikvisionRepository = (new StaffHikvisionRepository($this->lang));
        $where['syncs']         = [StaffHikvisionModel::SYNC_YES, StaffHikvisionModel::SYNC_PENDING];
        $where['sys_store_id']    = $params['sys_store_id'];
        $where['not_limit']       = 1;
        $list                     = $staffHikvisionRepository->getListQuery($where);
        return !empty($list) ? array_column($list, 'staff_info_id') : [];
    }

    /**
     * 编辑 人脸
     * @param $params
     * @return array
     * @throws BusinessException
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function editFace($params)
    {
        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoOne($params['staff_info_id'],
            'staff_info_id, name, sex, nationality, identity, sys_store_id, job_title');

        if (empty($staffInfo)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//未找到数据

        }
        //检查图片图片质量
        (new OutsourcingStaffServer($this->lang))->checkFaceImageQuality($params['face_img'], '');

        $staffHikvisionRepository = (new StaffHikvisionRepository($this->lang));
        $staff_hikvision          = $staffHikvisionRepository->getStaffHikvisionInfo($params['staff_info_id']);

        $db = $this->getDI()->get('db');

        try {
            $db->begin();

            $staff_hikvision_data                  = $staff_hikvision;
            $staff_hikvision_data['staff_info_id'] = $params['staff_info_id'];
            $staff_hikvision_data['face_img_path'] = $params['face_img'];
            $staff_hikvision_data['deleted']       = StaffHikvisionModel::DELETED_NO;
            $staff_hikvision_data['is_sync']       = StaffHikvisionModel::SYNC_PENDING;//待处理
            $staff_hikvision_data['editor_id']     = $params['userinfo']['staff_id'];
            $staff_hikvision_data['editor_type']   = StaffHikvisionModel::EDITOR_TYPE_4;
            $staff_hikvision_data['ai_emb']        = '';
            $staff_hikvision_data['formal']        = StaffHikvisionModel::FORMAL_1;

            unset($staff_hikvision_data['updated_at'], $staff_hikvision_data['created_at']);
            //3.1写入或更新staff_hikvision表
            if (!empty($staff_hikvision)) {
                $db->updateAsDict('staff_hikvision', $staff_hikvision_data,
                    'staff_info_id=' . $params['staff_info_id']);
            } else {
                $db->insertAsDict('staff_hikvision', $staff_hikvision_data);
            }
            //3.2写入或更新staff_hikvision_log表
            if (!empty($staff_hikvision)) {
                unset($staff_hikvision_data['id']);
            }
            $db->insertAsDict('staff_hikvision_log', $staff_hikvision_data);

            $db->commit();

            $result = [
                'code' => ErrCode::SUCCESS,
                'msg'  => 'Success',
            ];
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log('edit-face-fail' . json_encode(array_merge($params)), 'error');
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg'  => 'server error',
            ];
        }

        //变更组织，走异步直接同步hik
        if ($result['code'] == ErrCode::SUCCESS) {
            $sendData['staff_info_id'] = $staffInfo['staff_info_id'];
            $sendData['sys_store_id']  = $staffInfo['sys_store_id'];
            (new OutsourcingStaffServer($this->lang))->sendStaffInfo($sendData, 'edit');
        }

        return $result;
    }

    /**
     * 获取详情
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function detail($params)
    {
        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoOne($params['staff_info_id'],
            'staff_info_id, name, sex, nationality, identity, sys_store_id, job_title');

        if(empty($staffInfo)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//未找到数据

        }

        $staffInfo['sex_text']    = $staffInfo['sex'] == HrStaffInfoModel::SEX_MALE ? $this->getTranslation()->_('4900') : $this->getTranslation()->_('4901');
        $staffInfo['nationality'] = isset(HrStaffInfoModel::NATIONALITY[$staffInfo['nationality']]) ? $this->getTranslation()->_(HrStaffInfoModel::NATIONALITY[$staffInfo['nationality']]) : '';

        $storeInfo               = (new SysStoreRepository($this->lang))->getSysStoreInfo($staffInfo['sys_store_id'],
            'name');
        $staffInfo['store_name'] = empty($storeInfo) ? '' : $storeInfo['name'];

        $job_title                   = array_column((new SysServer($this->lang, $this->timeZone))->getJobTitleList(),
            'job_name', 'id');
        $staffInfo['job_title_name'] = $job_title[$staffInfo['job_title']] ?? '';

        $staffHikvisionInfo    = (new StaffHikvisionRepository($this->lang))->getStaffHikvisionInfo($staffInfo['staff_info_id'],
            'face_img_path');
        $staffInfo['face_img'] = empty($staffHikvisionInfo) ? '' : $staffHikvisionInfo['face_img_path'];

        return $staffInfo;
    }


}