<?php

namespace FlashExpress\bi\App\Server;



use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\FleetAuditModel;
use FlashExpress\bi\App\Models\backyard\FleetAuditRecordModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\SysAttachmentModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Models\fle\SysFleetVanTypeModel;
use FlashExpress\bi\App\Modules\My\library\Enums\CommonEnums as CommonEnumsMy;
use FlashExpress\bi\App\Modules\My\library\Enums\VehicleInfoEnums as VehicleInfoEnumsMy;
use FlashExpress\bi\App\Modules\Ph\library\Enums\CommonEnums as CommonEnumsPh;
use FlashExpress\bi\App\Modules\Ph\library\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Modules\Th\library\Enums\CommonEnums as CommonEnumsTh;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\FleetRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use Phalcon\Db;


class FleetServer extends AuditBaseServer
{
    protected   $re;
    public      $timezone;
    public      $lang;
    public      $fleet;
    public      $overtime;
    public      $auditlist;
    public      $staff;
    public      $public;
    public      $other;

    const       MAX_STORE_NUM = 6;
    const       MIN_STORE_NUM = 2;
    const       REDIS_FLEET_LINE_SCHEDULING_MESSAGE_KEY = 'fleet_line_scheduling_message_list';
    const       TERMINATE_TYPE_OTHER = 99;

    const       FDC_STATE_DEFAULT = 1; //默认状态，不符合FDC前置条件
    const       FDC_STATE_FIT_FDC = 2; //符合FDC条件
    const       FDC_STATE_NOT_FIT_FDC = 3; //不符合FDC条件
    const       FDC_STATE_NOT_BY = 4; //非by申请

    const       WAREHOUSE_NORMAL = 0; //没有爆仓
    const       WAREHOUSE_BOOM = 1;  //爆仓
    const       SCHEDULE_STATUS_HIDE = 0; //隐藏
    const       SCHEDULE_STATUS_FINDING_CAR = 1; //找车中
    const       SCHEDULE_STATUS_ARRANGED = 2; //已安排
    const       JOB_TITLE_ID_TRANSPORTATION_CCD_OFFICER = 1564;
    const       JOB_TITLE_ID_CENTRAL_CONTROL_DISPATCHING_OFFICER = 389;
    const       JOB_TITLE_ID_CENTRAL_CONTROL_DISPATCHING_SUPERVISOR = 388;
    const       JOB_TITLE_ID_CENTRAL_CONTROL_DISPATCHING_SPECIALIST = 1232;
    const       JOB_TITLE_ID_CENTRAL_CONTROL_DISPATCHING_DEPUTY_MANAGER = 519;
    const       JOB_TITLE_ID_CENTRAL_CONTROL_DISPATCHING_MANAGER = 324;

    //19484【TH】新增加班车申请限制
    const       FLEET_LIMIT_0 = 0;//无限制
    const       FLEET_LIMIT_1 = 1;//该始发网点不允许申请加班车
    const       FLEET_LIMIT_2 = 2;//该线路走向不允许申请加班车

    const       FDC_DRIVER_JOB_TITLE_IDS = ['Van Courier', 'Van Feeder', 'Van Courier (Project)', 'EV Courier'];

    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->timezone = $timezone;
        $this->lang     = $lang;
        parent::__construct($this->lang);

        $this->fleet = new FleetRepository($this->timezone);
        $this->overtime = new OvertimeRepository($this->timezone);
        $this->auditlist = new AuditlistRepository($this->lang, $this->timezone);
        $this->staff = new StaffRepository();
        $this->public = new PublicRepository();
        $this->other = new OtherRepository();
    }

    public function batchAddValidation($paramIn)
    {
        $validations = [
            "car_type"  => "Required|Int",
            "store_ids" => "Required|Required|Arr",
            "capacity"  => "Required|IntGtLe:0,5000",
            "reason"    => "StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('1019'),
        ];
        $this->validateCheck($paramIn, $validations);
    }


    /**
     * FDC国家差异化逻辑
     * @throws ValidationException
     */
    protected function countryFDCLogic($t,$arriveTime,$auditType,$fdCourierId,$source,$paramIn): array
    {

        $settingEnv = new SettingEnvServer();
        $fleet_expected_arrival_time_check =  $settingEnv->getSetVal('fleet_expected_date_check');//期望到达时间限制
        $now = date('Y-m-d H:i:s');
        if ($fleet_expected_arrival_time_check) {
            [$check_min, $check_max] = json_decode($fleet_expected_arrival_time_check, true);
            if (strtotime($arriveTime) < strtotime($now . ' + ' . $check_min . ' Hours')) {
                throw new ValidationException($t->_('fleet_expected_date_check_start',['x' => $check_min]));
            }
            if (strtotime($arriveTime) > strtotime($now . ' + ' . $check_max . ' Hours')) {
                throw new ValidationException($t->_('fleet_expected_date_check_end',['x' => $check_max]));
            }
        }

        //校验输入的司机工号是否存在
        if ($auditType == enums::$fleet_audit_type['FD_courier']) {
            $fdCourierInfo = $this->staff->checkoutStaffBi($fdCourierId);
            if (empty($fdCourierInfo)) {
                throw new ValidationException($t->_('err_msg_driver_not_exist'));
            }

            $jobTitleIdsArr = $this->getDriverJotTitle();
            if (!in_array($fdCourierInfo['job_title'], $jobTitleIdsArr)) {
                throw new ValidationException($t->_('err_msg_wrong_job_title'));
            }
        }

        if ($source == 1) { //by来源
            $fdcConditions   = $this->checkFleetLine($paramIn);
            $fdcState        = $fdcConditions['data']['fdc_state'] ?? self::FDC_STATE_DEFAULT;
            $setFlag         = self::FDC_STATE_DEFAULT;
            $runningMileages = $fdcConditions['data']['running_mileages'] ?? 0;

            if ($auditType == enums::$fleet_audit_type['Normal'] && $fdcState == self::FDC_STATE_FIT_FDC) {
                $setFlag = self::FDC_STATE_FIT_FDC;
            }

            if ($auditType == enums::$fleet_audit_type['FD_courier'] && $fdcState == self::FDC_STATE_NOT_FIT_FDC) {
                $setFlag = self::FDC_STATE_NOT_FIT_FDC;
            }
        } else {
            $setFlag         = self::FDC_STATE_NOT_BY;
            $runningMileages = 0;
        }
        return [$setFlag,$runningMileages];
    }

    /**
     * 完善线路网点信息
     * @param $t
     * @param $auditType
     * @param $startStore
     * @param $endStore
     * @param $storeIds
     * @return array
     * @throws ValidationException
     */
    protected function fullApplyStoreInfo($t, $auditType, $startStore, $endStore, $storeIds): array
    {
        if ($auditType == enums::$fleet_audit_type['Normal']) { //普通加班车申请
            //检验是否存在重复值
            if (count($storeIds) != count(array_unique($storeIds))) {
                throw new ValidationException($t->_('err_msg_store_repeat'));
            }

            //校验网点个数
            if (count($storeIds) > self::MAX_STORE_NUM || count($storeIds) < self::MIN_STORE_NUM) {
                throw new ValidationException($t->_('err_msg_store_too_much'));
            }

            $startStore = array_shift($storeIds);
            $endStore   = array_pop($storeIds);

            if (!empty($storeIds)) {
                //获取全部申请网点的缩写
                $storeInfos = SysStoreModel::find([
                    'conditions' => "state = 1 and id IN ({store_ids:array})",
                    'bind'       => [
                        'store_ids' => $storeIds,
                    ],
                    'columns'    => 'id,name,short_name',
                ])->toArray();
                if (!empty($storeInfos)) {
                    $shortNameList = array_column($storeInfos, 'short_name', 'id');
                    $shortName     = array_map(function ($v) use ($shortNameList) {
                        return $shortNameList[$v] ?? "";
                    }, $storeIds);
                    $shortName     = implode('-', $shortName);
                }

                //途径网点
                $viaStoreIds = $storeIds;
            }
        }
        return [$startStore, $endStore, $viaStoreIds ?? [], $shortName ?? ''];
    }



    /**
     * 添加加班车申请
     * @throws \Exception
     */
    public function addFleet($paramIn = []): array
    {
        //[1]获取参数
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $carType        = $this->processingDefault($paramIn, 'car_type', 2);
        $capacity       = $this->processingDefault($paramIn, 'capacity', 2);
        $arriveTime     = $this->processingDefault($paramIn, 'arrive_time', 1);
        $startStore     = $this->processingDefault($paramIn, 'start_store', 1);
        $endStore       = $this->processingDefault($paramIn, 'end_store', 1);
        $reason         = $this->processingDefault($paramIn, 'reason', 1);
        $imagePathArr   = $this->processingDefault($paramIn, 'image_path');
        $reason         = addcslashes(stripslashes($reason), "'");
        $fdCourierId    = $this->processingDefault($paramIn, 'fd_courier_id', 2);
        $fdCourierName  = $this->processingDefault($paramIn, 'fd_courier_name', 1);
        $fdCourierPhone = $this->processingDefault($paramIn, 'fd_courier_phone', 1);
        $fdCourierPlate = $this->processingDefault($paramIn, 'fd_courier_plate', 1);
        $provinceCode   = $this->processingDefault($paramIn, 'province_code', 1);
        $auditType      = $this->processingDefault($paramIn, 'audit_type', 2);
        $storeIds       = $this->processingDefault($paramIn, 'store_ids', 3);
        $source         = $this->processingDefault($paramIn, 'source', 2);
        $selectBtnNum   = $this->processingDefault($paramIn, 'select_btn_num', 2);
        $reasonType     = $this->processingDefault($paramIn, 'reason_type', 2,99);

        $t = $this->getTranslation();
        //[2]参数校验
        //校验车辆类型
        $carTypeList = $this->getCarTypeList();
        if (!in_array($carType, array_column($carTypeList, 'type'))) {
            throw new ValidationException($t->_('1017'));
        }

        if (empty(trim($reason)) && empty($reasonType)) {
            throw new ValidationException($t->_('miss_args') . ' reason');
        }

        //完善线路网点信息
        [$startStoreId,$endStoreId,$viaStoreIds,$shortName] = $this->fullApplyStoreInfo($t, $auditType, $startStore, $endStore, $storeIds);

        //校验出发网点与目的网点是否相同
        if (strcmp($startStoreId, $endStoreId) == 0) {
            throw new ValidationException($t->_('fleet_depture_can_no_same'));
        }
        //申请时间要大于当前时间
        if (strtotime($arriveTime) < strtotime(date('Y-m-d H:i:s'))) {
            throw new ValidationException($t->_('fleet_time_need_ge_current'));
        }

        //加班车申请java限制
        $this->checkLimitStore([
            'origin_id'        => $startStoreId,
            'target_id'        => $endStoreId,
            'fleet_audit_type' => intval($auditType),
        ]);


        [$setFlag,$runningMileages] = $this->countryFDCLogic($t,$arriveTime,$auditType,$fdCourierId,$source,$paramIn);


        //获取网点大区、序列号
        $region   = (new SysStoreServer())->getStoreRegion($startStoreId);
        $serialNo = $this->getRandomId();

        //[3]组织数据插入业务主数据
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $insertData = [
                'audit_type'           => $auditType,
                'car_type'             => $carType,
                'capacity'             => $capacity,
                'expected_date'        => $arriveTime,
                'start_store'          => $startStoreId,
                'submitter_id'         => $staffId,
                'end_store'            => $endStoreId,
                'reason'               => $reason,
                'region'               => $region['region_name'] ?? '',
                'status'               => enums::APPROVAL_STATUS_PENDING,
                'serial_no'            => (!empty($serialNo) ? 'VA' . $serialNo : null),
                'wf_role'              => 'fleet_new_v2',
                'via_store_ids'        => implode('-',$viaStoreIds),
                'via_store_short_name' => $shortName ?? '',
                'set_flag'             => $setFlag,
                'select_btn_num'       => $selectBtnNum,
                'running_mileages'     => $runningMileages,
                'reason_type'          => $reasonType,
            ];
            //如果是FD加班车，需追加保存数据
            if ($auditType == enums::$fleet_audit_type['FD_courier']) {
                $insertData = array_merge($insertData, [
                    'fd_courier_id'     => $fdCourierId,
                    'fd_courier_name'   => $fdCourierName,
                    'fd_courier_phone'  => $fdCourierPhone,
                    'fd_courier_plate'  => $fdCourierPlate,
                    'province_code'     => $provinceCode,
                ]);
            }

            $fleetId = $this->fleet->InsertFleet($insertData);
            if (empty($fleetId)) {
                throw new \Exception($t->_('4008'));
            }

            //插入图片
            if(!empty($imagePathArr)) {
                $insertImgData  = [];

                foreach($imagePathArr as $image) {
                    $insertImgData[] = [
                        'id'         => $fleetId,
                        'image_path' => $image,
                    ];
                }
                $this->public->batchInsertImgs($insertImgData);
            }

            //创建
            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($fleetId, AuditListEnums::APPROVAL_TYPE_FLEET, $staffId,null, []);
            if (!$requestId) {
                throw new \Exception('创建审批流失败');
            }
            $db->commit();
        } catch (\Exception $e){
            $db->rollback();
            $this->logger->write_log("err fleet create" . $e->getMessage(), 'info');
            return $this->checkReturn(-3, $e->getMessage());
        }

        return $this->checkReturn([]);
    }

    /**
     * 19484【TH】新增加班车申请限制
     * https://flashexpress.feishu.cn/docx/CbDUdapF3oO79NxQmoLcNUKpn1j
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function checkLimitStore($params)
    {
        //仅th
        if(!isCountry()) {
            return true;
        }

        $api = new RestClient('nws');
        $res = $api->execute(RestClient::METHOD_POST, '/svc/fleet/audit/apply/store/limit',
            $params, ['Accept-Language' => $this->lang]);
        $this->logger->write_log(['fleet_limit_error_from_ms' => json_encode($res, JSON_UNESCAPED_UNICODE)], 'info');

        if ((isset($res['code']) && $res['code'] != 1)) {
            $this->logger->write_log(['fleet_limit_error_from_ms_1' => json_encode($res, JSON_UNESCAPED_UNICODE)]);
            throw new ValidationException($this->getTranslation()->_('server_error'));
        }

        //没有限制
        if(isset($res['data']['check_type']) && $res['data']['check_type'] === self::FLEET_LIMIT_0) {
            return true;
        }

        //该始发网点不允许申请加班车
        if (!empty($res['data']['check_type']) && $res['data']['check_type'] === self::FLEET_LIMIT_1) {
            throw new ValidationException($this->getTranslation()->_('fleet_limit_1'));
        }

        //该线路走向不允许申请加班车
        if (!empty($res['data']['check_type']) && $res['data']['check_type'] === self::FLEET_LIMIT_2) {
            throw new ValidationException($this->getTranslation()->_('fleet_limit_2'));
        }

        $this->logger->write_log(['fleet_limit_error_from_ms_2' => json_encode($res, JSON_UNESCAPED_UNICODE)]);
        throw new ValidationException($this->getTranslation()->_('server_error'));
    }

    /**
     * 更新加班车申请
     * @param array $paramIn 传入参数
     *  int $fleet_id             加班车ID
     *  int $status               审批状态
     *  string $reject_reason     拒绝原因
     * @return array
     * @throws \Exception
     */
    public function updateFleetV2($paramIn = []): array
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $fleetId = $this->processingDefault($paramIn, 'audit_id', 2);
        $status  = $this->processingDefault($paramIn, 'status', 2);
        $reason  = $this->processingDefault($paramIn, 'reject_reason', 1);
        $rejectType  = $this->processingDefault($paramIn, 'reject_type', 2);
        $super = $paramIn['super'] ?? false;
        $reason  = addcslashes(stripslashes($reason),"'");

        //[2]获取详情
        $fleetInfo = $this->getFleetDetail(['fleet_id'=>$fleetId, 'require_extend'=>false]);
        if (empty($fleetInfo)) {
            throw new ValidationException($this->getTranslation()->_('miles_C100100'), ErrCode::VALIDATE_ERROR);
        }

        //[4]验证
        //[4-1]如果已经是最终状态，啥操作都做不了
        if ($fleetInfo['status'] != enums::$audit_status['panding']) { //已经是最终状态,不能修改
            throw new ValidationException($this->getTranslation()->_('err_msg_status_updated'), ErrCode::VALIDATE_ERROR);
        }

        $paramData = [
            'reject_reason' => $reason,
        ];

        if (!empty($rejectType)) {
            $paramData['reject_type'] = $rejectType;
        }

        $this->getDI()->get('db')->begin();

        try {

            //同意或者驳回等分开处理
            if ($status == enums::$audit_status['approved']) {
                //同意
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->approval($fleetId, AuditListEnums::APPROVAL_TYPE_FLEET, $staffId, null,['super' => $super]);
            } else if ($status == enums::$audit_status['dismissed']) {
                //$this->fleet->updateFleet($fleetId, $status, $reason);
                //驳回
                $server = new ApprovalServer($this->lang, $this->timezone);
                $ret = $server->reject($fleetId, AuditListEnums::APPROVAL_TYPE_FLEET, $reason, $staffId, ['super' => $super]);

                if (!empty($ret)) {
                    //更新数据
                    $this->getDI()->get('db')->updateAsDict(
                        'fleet_audit',
                        $paramData,
                        "id = " . $fleetId
                    );
                }
            } else {
                //$this->fleet->updateFleet($fleetId, $status, $reason);
                //撤销
                $server = new ApprovalServer($this->lang, $this->timezone);
                $ret = $server->cancel($fleetId, AuditListEnums::APPROVAL_TYPE_FLEET, $reason, $staffId);
                if (!empty($ret)) {
                    //更新数据
                    $this->getDI()->get('db')->updateAsDict(
                        'fleet_audit',
                        $paramData,
                        "id = " . $fleetId
                    );
                }
            }

            $this->getDI()->get('db')->commit();
        }
        catch (BusinessException $be) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('update fleet failure:' . $be->getMessage(), 'notice');
            return $this->checkReturn(-3,$be->getMessage());
        }catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('update fleet failure:' . $e->getMessage(), 'error');
            return $this->checkReturn(-3,$e->getMessage());
        }
        return $this->checkReturn([]);
    }

    /**
     * 获取加班车类型列表
     */
    public function getCarTypeList($carType = 0): array
    {
        //从java表中获取车辆全部类型
        $carTypes = SysFleetVanTypeModel::find([
            'conditions' => " deleted = 0 ",
            'columns'    => 'van_value as type_txt, code as type',
            'order'      => 'sort_number asc',
        ])->toArray();

        if ($carType) { //获取指定的车辆类型
            $carTypes = array_column($carTypes, 'type_txt', 'type');
            $returnData = [
                'type'      => $carType,
                'type_txt'  => $carTypes[$carType],
            ];
        } else { //返回所有的
            $returnData = $carTypes;
        }

        return $returnData;
    }

    /**
     * 获取加班车申请类型
     */
    public function getFleetAuditType($paramIn = []): array
    {
        return [
            [
                'type'      => FleetAuditModel::FLEET_TYPE_NORMAL,
                'type_txt'  => $this->getTranslation()->_('ordinary_car'),
                'type_txt_key'=>'ordinary_car',
            ],
            [
                'type'      => FleetAuditModel::FLEET_TYPE_FD,
                'type_txt'  => $this->getTranslation()->_('fd_courier_car'),
                'type_txt_key'=>'fd_courier_car',
            ],
        ];
    }

    /**
     * 获取加班车网点分类--虚拟网点 or 真实网点
     */
    public function getStoreType($paramIn = []): array
    {
        return [
            [
                'type'      => SysStoreModel::STORE_VIRTUAL,//虚拟网点
                'type_txt'  => $this->getTranslation()->_('store_virtual'),
            ],
            [
                'type'      => SysStoreModel::STORE_REALITY,//真实网点
                'type_txt'  => $this->getTranslation()->_('store_reality'),
            ],
        ];
    }

    /**
     *申请原因
     * @return array
     */
    public function getReasonType(): array
    {
        $t       = $this->getTranslation();
        $country = 'th';
        $num     = range(1, 8);
        if (isCountry('TH')) {
            $country = strtolower(get_country_code());
            $num     = range(1, 11);
        } elseif (isCountry('PH')) {
            $country = strtolower(get_country_code());
            $num     = range(1, 6);
        } elseif (isCountry('MY')) {
            $country = strtolower(get_country_code());
            $num     = range(1, 7);
        } elseif (isCountry('LA')) {
            $country = strtolower(get_country_code());
            $num     = range(1, 8);
        }
        $list    = [];
        foreach ($num as $v) {
            $list[] = [
                'type'     => strval($v),
                'type_txt' => $t->_($country . '_fleet_reason_type_' . $v),
            ];
        }
        $list[] = [
            'type'     => strval(99),
            'type_txt' => $t->_('fleet_reason_type_99'),
        ];
        return $list;
    }


    /**
     * 判断当前登录人所在网点, 网点类型是否是SP BDC
     * @param array $paramIn
     * @return array
     */
    public function isSpBdcTypeAndStoreCategory(array $paramIn): array
    {
        $store_category = 0;
        if ($paramIn['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2']) {
            return [false, $store_category];
        }

        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind'       => ['store_id' => $paramIn['organization_id']],
        ]);

        if (empty($storeInfo)) {
            return [false, $store_category];
        }
        $store_category = intval($storeInfo->category);
        if (in_array($storeInfo->category, [enums::$stores_category['sp'], enums::$stores_category['bdc']])) {
            return [true, $store_category];
        }
        return [false,$store_category];
    }

    /**
     * 获取加班车详情
     * @param int $fleet_id 加班车记录id
     * @return array
     */
    public function getFleetDetail($paramIn = [])
    {
        $fleet_id         = $this->processingDefault($paramIn, 'fleet_id', 2);
        $isRequireExtend  = $this->processingDefault($paramIn, 'require_extend', 4, true);
        $returnData       = $this->fleet->getFleetInfo($fleet_id, true);

        if ($returnData) {
            $returnData['start_store']  = $returnData['start_store'] ?? '';
            $returnData['end_store']    = $returnData['end_store'] ?? '';
            $storeNames = (new SysStoreServer())->getStoreName([$returnData['start_store'], $returnData['end_store']]);
            $returnData['start_store_name'] = $storeNames[$returnData['start_store']] ?? '';
            $returnData['end_store_name']   = $storeNames[$returnData['end_store']] ?? '';

            //获取全部申请网点的缩写
            $storeInfos = SysStoreModel::find([
                'conditions' => "state = 1 and id IN ({store_ids:array})",
                'bind'       => [
                    'store_ids'=> [$returnData['start_store'], $returnData['end_store']],
                ],
                'columns' => 'id,name,short_name',
            ])->toArray();
            $shortNames = array_column($storeInfos, 'short_name', 'id');
            $returnData['start_store_short_name']  = $shortNames[$returnData['start_store']] ?? '';
            $returnData['end_store_short_name']    = $shortNames[$returnData['end_store']] ?? '';

            //加班车申请类型
            $auditType = $this->getFleetAuditType();
            $auditType = array_column($auditType, 'type_txt', 'type');
            $returnData['audit_type_title'] = $auditType[$returnData['audit_type']] ?? '';

            $reasonType = $this->getFleetRejectReason();
            $reasonType = array_column($reasonType, 'value', 'code');
            $returnData['reject_type_text'] = !empty($returnData['reject_type']) ? $reasonType[$returnData['reject_type']] : '';
        }

        if ($returnData && $isRequireExtend && $returnData['status'] == 2) {
            //抢单需求 切换接口新增参数
            $_param['serial_no'] = $returnData['serial_no'];
            $_param['line_id'] = $returnData['line_id'];
            $_param['line_back_id'] = $returnData['line_back_id'];
            $_param['plan_date'] = $returnData['plan_date'];
            $_param['plan_back_date'] = $returnData['plan_back_date'];
            $_param['single_line'] = $returnData['single_line'];

            //请求java接口获取车牌号、加班车公司、时刻表
            $completeInfo = $this->getFleetTimeTable($_param);
            if ($completeInfo['code'] == 1 && !empty($completeInfo['data'])) {
                $res = $completeInfo['data'];
                $returnData['extend'] = [];
                if (isset($res['line_timetable_dtolist'])) {
                    $data = $this->generateTable($this->getTranslation()->_('time_table'), $res['line_timetable_dtolist']);
                    $returnData['extend'][] = $data;
                }

                if (isset($res['line_back_timetable_dtolist'])) {
                    $data = $this->generateTable($this->getTranslation()->_('return_back_time_table'), $res['line_back_timetable_dtolist']);
                    $returnData['extend'][] = $data;
                }
                $returnData['fd_courier_id'] = $res['fd_courier_id'] ?? '';
                $returnData['plate_number'] = $res['plate_name'] ?? '';

                $returnData['company_name'] = $res['fleet_name'] ?? '';
                $returnData['driver'] = $res['driver'] ?? '';
                $returnData['driver_phone'] = $res['driver_phone'] ?? '';

                //ms 加班车调度状态
                //1待执行 2已同意 3驳回 4撤销 5已到仓 6运输中 7待审批 8待调度 9撮合中 10已作废 11已完成 12已终止
                if (in_array($res['status'], [8,9])) { //找车中
                    $returnData['schedule_banner']['show_schedule_status'] = self::SCHEDULE_STATUS_FINDING_CAR;
                    $returnData['schedule_banner']['schedule_status_text'] = $this->getTranslation()->_('fleet.schedule_status_find_car');
                } elseif (in_array($res['status'], [1,5,6,11])) { //已安排
                    $returnData['schedule_banner']['show_schedule_status'] = self::SCHEDULE_STATUS_ARRANGED;
                    $returnData['schedule_banner']['schedule_status_text'] = $this->getTranslation()->_('fleet.schedule_status_arranged');
                } else {
                    $returnData['schedule_banner']['show_schedule_status'] = self::SCHEDULE_STATUS_HIDE;
                    $returnData['schedule_banner']['schedule_status_text'] = '';
                }

                $cadence = [
                    'estimate_start_time' => $res['estimate_start_time_text'] ?? '',
                    'driver_name'         => $res['driver'] ?? '',
                    'driver_phone'        => $res['driver_phone'] ?? '',
                    'plate_number'        => $res['plate_name'] ?? '',
                    'car_type'            => $res['plate_type_text'],
                    'estimate_end_time'   => $res['estimate_end_time_text'] ?? '',
                ];

                if ($returnData['audit_type'] == FleetAuditModel::FLEET_TYPE_FD) {
                    $cadence['fd_driver_id'] = $res['fd_courier_id'] ?? '';
                }

                if (in_array($res['status'], [3,4,10])) { //3驳回 4撤销,不展示详情
                    $returnData['cadence_status'] = FleetAuditModel::CADENCE_STATUS_NORMAL;
                    $cadence = [];
                } elseif ($res['status'] == 12) { //终止
                    // 终止原因字段 "cadence_type":1,
                    // 终止原因文本 "cadence_type_text": "重复申请加班车"
                    //终止原因枚举code  1:重复申请加班车 2:未找到车 3:客户取消订单 4:评估包裹数量过多 5:网点常规线路装载率不达标
                    //6:网点加班车申请类型选择错误 7:拍摄的照片不合格 8:网点主管/DM审批过慢 9:网点申请不合理的车型 99:其他（选择其他时 需要填写备注原因）
                    $terminateReason = isset($res['cadence_type_text']) && $res['cadence_type_text']
                        ? $res['cadence_type_text']
                        : $this->getTranslation()->_('terminate_reason_' . $res['cadence_type']);

                    $returnData['terminate_reason'] = $terminateReason;

                    $returnData['cadence_status'] = FleetAuditModel::CADENCE_STATUS_TERMINATE;
                    $cadence = [];
                } elseif (in_array($res['status'], [7,8,9,2])) { //待调度
                    $returnData['cadence_status'] = FleetAuditModel::CADENCE_STATUS_PENDING;
                    $cadence = [];
                } else {
                    $returnData['cadence_status'] = FleetAuditModel::CADENCE_STATUS_NORMAL;
                }

                $returnData['cadence_detail'] = $this->format($cadence);

                //当调度状态为待调度、已终止、撮合中时，查询在职状态=在职且职位为Central Control Dispatching Officer的人员信息
                if (in_array($res['status'], [8,9,12])) {
                    $returnData['ccd'] = $this->getStaffCCD();
                }
            }
        }

        return $returnData;
    }

    /**
     * 组织表数据
     * @param string $title     表头
     * @param array  $tableData 表数据
     * @return array
     */
    public function generateTable($title, $tableData)
    {
        $returnData = [];
        if ($tableData) {
            $detail = [];
            foreach ($tableData as $k => $v) {
                $detail[] = [
                    ($k + 1),
                    $v['store_name'],
                    $v['estimate_end_time_text'] ?? '',
                    $v['estimate_start_time_text'] ?? '',
                ];
            }

            $returnData = [
                'title' => $title,
                'th'    => [
                    $this->getTranslation()->_('serial_number'),
                    $this->getTranslation()->_('site'),
                    $this->getTranslation()->_('planned_arrive'),
                    $this->getTranslation()->_('planned_departure'),
                ],
                'td'    => $detail,
            ];
        }
        return $returnData;
    }

    /**
     * 获取加班车时刻表
     * @param int $paramIn      加班车申请ID
     * @return array
     */
    public function getFleetTimeTable($paramIn = [])
    {
        try {
            //调用java 新项目 不用rpc  用 http  地址：/svc/fleet/audit/tandem/sync_approve_info
            $url   = $this->config->api->java_http_url;
            $url   .= "/svc/fleet/audit/tandem/query/approve_info";
            $param = $paramIn;
            $res   = $this->httpPost($url, $param);
            if (empty($res['code']) || $res['code'] != 1) {//回滚
                $this->logger->write_log("fleet get_detail ".json_encode($param).json_encode($res));
                return [];
            }
            return $res;
        } catch (\Exception $e) {
            //异常 处理返回空 不影响详情页展示
            $this->logger->write_log("fleet get_detail ".json_encode($param).json_encode($res ?? []));
            return [];
        }
    }

    /**
     * 获取相同请求的提交人
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getSimilarRequest($paramIn = [])
    {
        $arriveTime = $this->processingDefault($paramIn, 'arrive_time');
        $startStore = $this->processingDefault($paramIn, 'start_store');
        $endStore   = $this->processingDefault($paramIn, 'end_store');
        $store_ids   = $this->processingDefault($paramIn, 'store_ids');
        if (!empty($store_ids)) {
            $startStore = array_shift($store_ids);
            $endStore   = array_pop($store_ids);
        }

        $parms = [
            'start_store' => $startStore,
            'end_store'   => $endStore,
            'arrive_time' => $arriveTime,
        ];
        $returnData = (new \FlashExpress\bi\App\Repository\FleetRepository($this->timezone))->getSimilarRequest($parms);

        return $this->checkReturn(['data' => ['dataList' => $returnData]]);
    }

    /**
     * 获取驳回原因
     */
    public function getFleetRejectReason(): array
    {
        $t = $this->getTranslation();
        return [
            [
                'code'  => 1,
                'value' => $t->_('va_reject_1'),
            ],
            [
                'code'  => 2,
                'value' => $t->_('va_reject_2'),
            ],
            [
                'code'  => 3,
                'value' => $t->_('va_reject_3'),
            ],
            [
                'code'  => 4,
                'value' => $t->_('va_reject_4'),
            ],
            [
                'code'  => 5,
                'value' => $t->_('va_reject_5'),
            ],
            [
                'code'  => 6,
                'value' => $t->_('va_reject_6'),
            ],
            [
                'code'  => 999,
                'value' => $t->_('va_reject_999'),
            ],
        ];
    }

    /**
     * 获取查询sql
     * @param $params
     * @return \Phalcon\Mvc\Model\Query\BuilderInterface
     */
    public function getBuilder($params)
    {
        //获取参数
        $region = $this->processingDefault($params, 'region');
        $originStoreId = $this->processingDefault($params, 'origin_store_id');
        $targetStoreId = $this->processingDefault($params, 'target_store_id');
        $status = $this->processingDefault($params, 'state');
        $serialNo = $this->processingDefault($params, 'serial_no');
        $auditType = $this->processingDefault($params, 'audit_type');
        $storeIds = $this->processingDefault($params, 'ids');
        $startTime = $this->processingDefault($params, 'start_time', 1);
        $endTime = $this->processingDefault($params, 'end_time', 1);
        $column = $this->processingDefault($params, 'column', 1);
        $ascending = $params['ascending'];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($column);
        $builder->from(['fa' => FleetAuditModel::class]);
        $builder->leftJoin(StaffAuditApprovalModel::class, 'fa.id = saa.audit_id', 'saa');
        $builder->where('saa.type = :type: AND saa.is_shown = 2 AND fa.status IN (1,2,3,4,7,10)', ['type' => enums::$audit_type['VA']]);

        //区域
        if (!empty($region)) {
            $builder->andWhere('fa.region = :region:', ['region' => $region]);
        }

        //开始网点
        if (!empty($originStoreId)) {
            $builder->andWhere('fa.start_store = :start_store:', ['start_store' => $originStoreId]);
        }

        //目的网点
        if (!empty($targetStoreId)) {
            $builder->andWhere('fa.end_store = :end_store:', ['end_store' => $targetStoreId]);
        }

        //审批状态
        if (!empty($status)) {
            $builder->andWhere('saa.status = :status:', ['status' => $status]);
        }

        //审批序列号
        if (!empty($serialNo)) {
            $builder->andWhere('fa.serial_no LIKE :serial_no:', ['serial_no' => $serialNo . '%']);
        }

        //加班车类型
        if (!empty($auditType)) {
            $builder->andWhere('fa.audit_type = :audit_type:', ['audit_type' => $auditType]);
        }

        //开始时间
        if (!empty($startTime)) {
            $builder->andWhere('fa.expected_date >= :start_time:', ['start_time' => date("Y-m-d H:i:s", strtotime($startTime))]);
        }

        //结束时间
        if (!empty($endTime)) {
            $builder->andWhere('fa.expected_date <= :end_time:', ['end_time' => date("Y-m-d H:i:s", strtotime($endTime))]);
        }

        //起始网点包含
        if (!empty($storeIds)) {
            $builder->inWhere('fa.start_store', $storeIds);
        }

        if (isset($ascending) && $ascending == 1) {
            $builder->orderBy('fa.expected_date ASC');
        } elseif (isset($ascending) && $ascending == 0) {
            $builder->orderBy('fa.expected_date DESC');
        } else {
            $builder->orderBy('saa.createtime ASC');
        }
        return $builder;
    }

    /**
     * 获取加班车列表For Java
     * @param array $params
     * @return array
     */
    public function getFleetList(array $params = []): array
    {
        //获取参数
        $pageSize = $this->processingDefault($params, 'page_size', 2, 10);
        $pageNum = $this->processingDefault($params, 'page_num', 2, 1);
        $isExport = $params['isExport'] ?? false;

        $params['column'] = "fa.id,fa.serial_no,fa.car_type,fa.audit_type,fa.capacity,fa.expected_date,fa.plan_date,
            fa.plan_back_date,fa.start_store,fa.end_store,fa.region,fa.reason,fa.reject_reason,fa.line_id,
            fa.line_back_id,fa.single_line,fa.system_quote,fa.abnormal_cost,fa.final_cost,fa.submitter_id,
            fa.created_at,fa.updated_at,fa.final_approver,fa.final_approval_time,saa.status,fa.approve_time,
            fa.running_mileage,fa.reject_type,fa.driver_id,fa.abnormal_cost_back,fa.final_cost_back,fa.ms_first_approver,
            fa.ms_first_approval_time,fa.via_store_ids,fa.via_store_short_name";
        $builder = $this->getBuilder($params);

        //[2]获取列表数据
        $totalCnt = $this->lineCount($params);

        if ($isExport === false) { //非导出

            if ($pageNum > 1) {
                $pageOffset = ($pageNum - 1) * $pageSize;
            } else {
                $pageOffset = 0;
            }
            $builder->limit($pageSize, $pageOffset);
        }

        $list = $builder->getQuery()->execute()->toArray();

        return [
            "item" => $list,
            "pagination" => [
                "current_page" => $pageNum,
                "per_page" => $pageSize,
                "total_count" => $totalCnt,
            ],
        ];
    }

    /**
     * 分页接口
     * @param $params
     * @return int
     */
    public function lineCount($params): int
    {
        $params['column'] = "COUNT(1) AS cou";
        $builder = $this->getBuilder($params);

        $totalCnt = $builder->getQuery()->execute()->getFirst()->toArray();

        //[2]获取列表数据
        return $totalCnt['cou'] ?? 0;
    }

    /**
     * 根据线路ID获取加班车详情 ForJava
     * @param $params
     * @return array
     */
    public function getFleetDetailByLineId($params): array
    {
        $lineId = $this->processingDefault($params, 'lineId');

        if (empty($lineId)) {
            return [];
        }

        $detail = FleetAuditModel::findFirst([
            'conditions' => "line_id = :line_id: OR line_back_id = :line_id:",
            'bind' => [
                'line_id' => $lineId,
            ],
            'columns' => '*',
        ]);
        if (empty($detail)) {
            return [];
        }

        return $detail->toArray();
    }

    /**
     * 根据审批ID获取加班车详情 ForJava
     * @param $params
     * @return array
     */
    public function getFleetDetailByAuditId($params): array
    {
        $auditId = $this->processingDefault($params, 'id');

        if (empty($auditId)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('
            fa.id, fa.serial_no, fa.car_type, fa.audit_type, fa.province_code, fa.fd_courier_id, fa.fd_courier_name, 
            fa.fd_courier_phone, fa.fd_courier_plate, fa.province_code, fa.capacity, fa.expected_date, fa.plan_date, 
            fa.plan_back_date, fa.start_store, fa.end_store,fa.region, fa.reason, fa.reject_reason, fa.line_id, 
            fa.line_back_id, fa.single_line, fa.system_quote, fa.abnormal_cost, fa.final_cost, fa.submitter_id, 
            fa.created_at, fa.updated_at, fa.final_approver, fa.final_approval_time, fa.status AS audit_state, 
            saa.status,fa.approve_time, fa.running_mileage,fa.reject_type,fa.driver_id,fa.abnormal_cost_back,
            fa.final_cost_back,fa.ms_first_approver,fa.ms_first_approval_time,fa.via_store_ids,fa.via_store_short_name
                    ');
        $builder->from(['fa' => FleetAuditModel::class]);
        $builder->join(StaffAuditApprovalModel::class,'fa.id = saa.audit_id','saa');
        $builder->where('fa.id = :audit_id:', ['audit_id' => $auditId]);
        $builder->andWhere('saa.type = 12 AND saa.is_shown = 2 AND fa.status IN (1,2,3,4,7,10)');
        $detail = $builder->getQuery()->execute()->getFirst();

        if (empty($detail)) {
            return [];
        }
        return $detail->toArray();
    }

    /**
     * 审批加班车 ForJava
     * @throws \Exception
     */
    public function auditFleet($params)
    {
        //获取参数
        $auditId = $this->processingDefault($params, 'id');
        $params['audit_id'] = $auditId;
        $params['from'] = true; //从MS系统审批

        //查看使用哪个版本的审批
        $info = FleetAuditModel::findFirst($auditId);
        if (empty($info)) {
            throw new ValidationException($this->getTranslation()->_('miles_C100100'));
        }
        $return = $this->checkReturn([]);

        if ($info instanceof FleetAuditModel) {
            $return = $this->updateFleetV2($params);
        }

        return $return;
    }

    //java 那边 批量同意 加班车申请 产品说了 没有批量驳回
    public function batchAuditFleet($param){
        $ids_arr = array_map('intval',$param['batch_audit_ids']);
        $ids_arr = array_unique($ids_arr);
        try{

            //只针对批量审核通过操作
            if(empty($param['status']) || $param['status'] != enums::APPROVAL_STATUS_APPROVAL)
                return $this->checkReturn(array('code' => -1,'msg' => 'audit status error', 'data' => null));

            if(empty($ids_arr))
                return $this->checkReturn(array('code' => -1,'msg' => 'invalid param batch_audit_ids', 'data' => null));

            if(count($ids_arr) > 10)
                return $this->checkReturn(array('code' => -1,'msg' => 'too many lines,less than 10 pls', 'data' => null));

            //批量操作
            $this->getDI()->get('db')->begin();
            //[1]获取参数
            $staffId = $this->processingDefault($param, 'staff_id', 2);
            $super = true;
            $server = new ApprovalServer($this->lang, $this->timezone);
            foreach ($ids_arr as $id){
                $flag = $server->approval($id, AuditListEnums::APPROVAL_TYPE_FLEET, $staffId, null,['super' => $super]);
                if($flag === false){
                    $this->logger->write_log("fleet svc batch_audit {$id} failed ");
                    $this->getDI()->get('db')->rollback();
                    return $this->checkReturn(array('code' => -1,'msg' => "batch_audit when {$id} failed", 'data' => null));
                }
            }
            $this->getDI()->get('db')->commit();
            $this->logger->write_log("fleet svc batch_audit success ".json_encode($ids_arr),'info');
            return $this->checkReturn(1);

        }catch (\Exception $e){
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log("fleet svc batch_audit failed ".$e->getMessage().json_encode($ids_arr),'info');
            return $this->checkReturn(array('code' => -1,'msg' => "batch_audit fleet failed", 'data' => null));
        }


    }

    /**
     * 编辑加班车信息 ForJava
     * @param array $params
     * @return bool
     */
    public function editFleetInfo(array $params = []):bool
    {
        //获取参数
        $auditId = $this->processingDefault($params, 'id');
        $carType = $this->processingDefault($params, 'car_type');
        $planDate = $this->processingDefault($params, 'plan_date');
        $planBackDate = $this->processingDefault($params, 'plan_back_date');
        $lineId = $this->processingDefault($params, 'line_id');
        $lineBackId = $this->processingDefault($params, 'line_back_id');
        $singleLine = $this->processingDefault($params, 'single_line');
        $systemQuote = $this->processingDefault($params, 'system_quote');
        $abnormalCost = $this->processingDefault($params, 'abnormal_cost');
        $finalCost = $this->processingDefault($params, 'final_cost');
        $finalApprover = $this->processingDefault($params, 'final_approver');
        $rejectReason = $this->processingDefault($params, 'reject_reason');
        $finalApprovalTime = $this->processingDefault($params, 'final_approval_time');
        $runningMileage = $this->processingDefault($params, 'running_mileage');
        $rejectType = $this->processingDefault($params, 'reject_type');
        $driverId = $this->processingDefault($params, 'driver_id');
        $abnormalCostBack = $this->processingDefault($params, 'abnormal_cost_back');
        $finalCostBack = $this->processingDefault($params, 'final_cost_back');
        $msFirstApprover = $this->processingDefault($params, 'ms_first_approver');
        $msFirstApprovalTime = $this->processingDefault($params, 'ms_first_approval_time');

        if (empty($auditId)) {
            return false;
        }

        $FleetInfo = FleetAuditModel::findFirst([
            'conditions' => "id = :audit_id:",
            'bind' => [
                'audit_id' => $auditId,
            ],
        ]);
        if (empty($FleetInfo) || !($FleetInfo instanceof FleetAuditModel)) {
            return false;
        }

        if (!empty($carType)) {
            $FleetInfo->setCarType($carType);
        }

        if (!empty($planDate)) {
            $FleetInfo->setPlanDate(date("Y-m-d H:i:s", $planDate));
        }

        if (!empty($planBackDate)) {
            $FleetInfo->setPlanBackDate(date("Y-m-d H:i:s", $planBackDate));
        }

        if (!empty($lineId)) {
            $FleetInfo->setLineId($lineId);
        }

        if (!empty($lineBackId)) {
            $FleetInfo->setLineBackId($lineBackId);
        }

        if (!empty($singleLine)) {
            $FleetInfo->setSingleLine($singleLine);
        }

        if (!empty($systemQuote)) {
            $FleetInfo->setSystemQuote($systemQuote);
        }

        if (!empty($finalCost)) {
            $FleetInfo->setFinalCost($finalCost);
        }

        if (!empty($finalApprover)) {
            $FleetInfo->setFinalApprover($finalApprover);
        }

        if (!empty($rejectReason)) {
            $FleetInfo->setRejectReason($rejectReason);
        }

        if (!empty($finalApprovalTime)) {
            $FleetInfo->setFinalApprovalTime(date("Y-m-d H:i:s", $finalApprovalTime));
        }

        if (!empty($runningMileage)) {
            $FleetInfo->setRunningMileage($runningMileage);
        }

        if (!empty($rejectType)) {
            $FleetInfo->setRejectType($rejectType);
        }

        if (!empty($driverId)) {
            $FleetInfo->setDriverId($driverId);
        }

        if (!empty($abnormalCost)) {
            $FleetInfo->setAbnormalCost($abnormalCost);
        }

        if (!empty($abnormalCostBack)) {
            $FleetInfo->setAbnormalCostBack($abnormalCostBack);
        }

        if (!empty($finalCostBack)) {
            $FleetInfo->setFinalCostBack($finalCostBack);
        }

        if (!empty($msFirstApprover)) {
            $FleetInfo->setMsFirstApprover($msFirstApprover);
        }

        if (!empty($msFirstApprovalTime)) {
            $FleetInfo->setMsFirstApprovalTime(date("Y-m-d H:i:s", $msFirstApprovalTime));
        }
        $ret = $FleetInfo->update();

        return true;
    }

    /**
     * 查询加班车需求量, 供给量和未供给量
     * @param $params
     * @return array
     */
    public function getDemandNum($params): array
    {
        //获取请求参数
        $startTime = $this->processingDefault($params, 'startTime');
        $endTime = $this->processingDefault($params, 'endTime');

        $sql = "--
          SELECT 
            COUNT(fa.id) AS demand_number,
            IFNULL(SUM(CASE WHEN fa.status = 2 THEN 1 ELSE 0 END), 0) supply_number,
            IFNULL(SUM(CASE WHEN fa.status IN (1, 3, 7) THEN 1 ELSE 0 END), 0) no_supply_number
          FROM fleet_audit fa
          LEFT JOIN staff_audit_approval saa ON fa.id = saa.audit_id
          WHERE saa.type = 12 AND saa.is_shown = 2 AND fa.status IN (1,2,3,7,10)";

        $model = $this->getDI()->get('db');
        $conditions = [];
        if (!empty($startTime)) {
            $sql .= " AND fa.plan_date >= ?";
            $conditions[] = date('Y-m-d', $startTime);
        }

        if (!empty($endTime)) {
            $sql .= "AND fa.plan_date <= ?";
            $conditions[] = date('Y-m-d', $endTime);
        }

        return $model->query($sql, $conditions)->fetch(Db::FETCH_ASSOC);
    }

    /**
     * 新增日志
     * @param $params
     * @return int
     */
    public function saveFleetOptLog($params) : int
    {
        //获取参数
        $auditId = $this->processingDefault($params, 'audit_id');
        $status = $this->processingDefault($params, 'status');
        $operatorId = $this->processingDefault($params, 'operator_id');
        $operatorName = $this->processingDefault($params, 'operator_name');

        if (empty($auditId)) {
            return false;
        }

        $FleetInfo = FleetAuditModel::findFirst([
            'conditions' => "id = :audit_id:",
            'bind' => [
                'audit_id' => $auditId,
            ],
        ]);
        if (empty($FleetInfo)) {
            return false;
        }

        $model = new FleetAuditRecordModel();
        $model->setAuditId($auditId);
        $model->setStatus($status);
        $model->setOperatorId($operatorId);
        $model->setOperatorName($operatorName);
        $model->save();

        return $model->getId();
    }

    /**
     * 获取日志列表
     * @param $params
     * @return array
     */
    public function getFleetOptLogList($params): array
    {
        $auditId = $this->processingDefault($params, 'audit_id');
        $pageNum = $this->processingDefault($params, 'page_num',2, 1);
        $pageSize = $this->processingDefault($params, 'page_size',2,10);

        //获取全部的部门、职位、网点
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['far' => FleetAuditRecordModel::class]);
        $builder->leftJoin(StaffAuditApprovalModel::class,'far.audit_id = saa.audit_id','saa');
        $builder->andWhere('saa.type = 12 AND saa.is_shown = 2');

        if (!empty($auditId)) {
            $builder->andWhere('far.audit_id = :auditId:', ['auditId' => $auditId]);
        }

        $totalCount = $builder->columns('COUNT(1) AS total')->getQuery()->execute()->getFirst();
        $totalCnt = intval($totalCount->total);

        $builder->columns('far.id,far.operator_id,far.operator_name,far.created_at');
        $builder->orderBy("far.created_at DESC");
        if ($pageNum > 1) {
            $pageOffset = ($pageNum - 1) * $pageSize;
        } else {
            $pageOffset = 0;
        }
        $builder->limit($pageOffset, $pageSize);

        $list = $builder->getQuery()->execute()->toArray();

        if (!empty($list)) {
            foreach ($list as $key => $item) {
                $list[$key]['created_at'] = date("Y-m-d H:i:s", strtotime($item['created_at']));
            }
        }

        return [
            "item" => $list,
            "pagination" => [
                "current_page" => $pageNum,
                "per_page"     => $pageSize,
                "total_count"  => $totalCnt,
            ],
        ];
    }

    /**
     * 废弃线路
     * @param $params
     * @return bool
     */
    public function abandonFleetLineByLineId($params): bool
    {
//        $startStoreId = $this->processingDefault($params, 'start_store');
//        $endStoreId   = $this->processingDefault($params, 'end_store');
        $lineId       = $this->processingDefault($params, 'line_id');

        if (empty($lineId)) {
            throw new \Exception($this->getTranslation()->_('miss_args'));
        }

        //根据开始、结束网点、线路id查找主键ID
//        $auditInfo = FleetAuditModel::findFirst([
//            'conditions' => 'start_store = :start_store: and end_store = :end_store: and (line_id = :line_id: or line_back_id = :line_back_id:)',
//            'bind' => [
//                'start_store' => $startStoreId,
//                'end_store' => $endStoreId,
//                'line_id' => $lineId,
//                'line_back_id' => $lineId
//            ],
//            'columns' => 'id,status'
//        ]);
        //只有审核通过的才能作废
        $auditInfo_front = FleetAuditModel::find([
            'conditions' => "status = 2 and line_id = :line_id:",
            'bind' => ['line_id' => $lineId],
        ])->toArray();
        $auditInfo_back = FleetAuditModel::find([
            'conditions' => "status = 2 and line_back_id = :line_id:",
            'bind' => ['line_id' => $lineId],
        ])->toArray();

        $arr = array_merge($auditInfo_front,$auditInfo_back);

        if (empty($arr)) {
            throw new \Exception($this->getTranslation()->_('no valid line'));
        }

//        //非审核通过 不能操作
//        $status_arr = array_column($arr,'status');
//        $count = array_count_values($status_arr);
//        if($count[enums::APPROVAL_STATUS_APPROVAL] != count($status_arr))
//            throw new \Exception($this->getTranslation()->_('please check audit status'));


        //作废
        $this->getDI()->get('db')->begin();
        try{
            foreach ($arr as $auditInfo){
                $info = FleetAuditModel::findFirst($auditInfo['id']);
                if ($info instanceof FleetAuditModel) {
                    $info->setStatus(enums::APPROVAL_STATUS_ABANDON);
                    $info->update();
                }

                $approvalInfo = StaffAuditApprovalModel::findFirst([
                    'conditions' => 'audit_id = :audit_id: and type = :type: and is_shown = 2',
                    'bind' => [
                        'audit_id' => $auditInfo['id'],
                        'type'     => AuditListEnums::APPROVAL_TYPE_FLEET,
                    ],
                ]);
                if ($approvalInfo instanceof StaffAuditApprovalModel) {
                    $approvalInfo->status = enums::APPROVAL_STATUS_ABANDON;
                    $approvalInfo->update();
                }
            }

            $this->getDI()->get('db')->commit();
            return true;
        }catch (\Exception $e){
            $this->logger->write_log("fleet sync abandon failed".json_encode($params).$e->getMessage());
            $this->getDI()->get('db')->rollback();
            return false;
        }
    }

    public function batch_abandon($param){
        $ids_arr = array_unique($param['batch_line_ids']);

        $line_info = FleetAuditModel::find([
            'conditions' => 'status = 2 and line_id in({ids:array})',
            'bind'       => ['ids' => $ids_arr],
        ])->toArray();

        $back_line_info = FleetAuditModel::find([
            'conditions' => 'status = 2 and line_back_id in({ids:array})',
            'bind'       => ['ids' => $ids_arr],
        ])->toArray();

        $data = array_merge($line_info,$back_line_info);

        if(empty($data))
            return $this->checkReturn(array('code' => -1,'msg' => 'find nothing by line id', 'data' => null));

//        //如果存在 非审核通过的
//        $status_list = array_column($data,'status');
//        foreach ($status_list as $status){
//            if($status != enums::APPROVAL_STATUS_APPROVAL){
//                throw new \Exception($this->getTranslation()->_('please check audit status'));
//            }
//        }

        //批量 废弃
        $this->getDI()->get('db')->begin();
        try{
            foreach ($data as $item){
                //作废
                $info = FleetAuditModel::findFirst($item['id']);
                if(empty($info))
                    throw new \Exception($this->getTranslation()->_("abandon fleet_audit {$item['id']} failed"));

                if ($info instanceof FleetAuditModel) {
                    $info->setStatus(enums::APPROVAL_STATUS_ABANDON);
                    $info->update();
                }

                $approvalInfo = StaffAuditApprovalModel::findFirst([
                    'conditions' => 'audit_id = :audit_id: and type = :type: and is_shown = 2',
                    'bind' => [
                        'audit_id' => $item['id'],
                        'type'     => AuditListEnums::APPROVAL_TYPE_FLEET,
                    ],
                ]);
                if(empty($approvalInfo))
                    throw new \Exception($this->getTranslation()->_("abandon approval_info {$item['id']} failed"));

                if ($approvalInfo instanceof StaffAuditApprovalModel) {
                    $approvalInfo->status = enums::APPROVAL_STATUS_ABANDON;
                    $approvalInfo->update();
                }
            }
            $this->getDI()->get('db')->commit();
            return $this->checkReturn(1);

        }catch (\Exception $e){
            $this->logger->write_log("fleet sync batch_abandon failed ".json_encode($param).$e->getMessage());
            $this->getDI()->get('db')->rollback();
            return $this->checkReturn(array('code' => -1,'msg' => 'find nothing by line id', 'data' => null));
        }
    }


    /**
     * 详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     * @throws \Exception
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $params = [
            'fleet_id' => $auditId,
            'require_extend' => true,
        ];

        //获取车型详情
        $result  = $this->getFleetDetail($params);
        $carType = $this->getCarTypeList($result['car_type']);

        $statisticsParameter = [
            'start_store' => $result['start_store'],
            'end_store'   => $result['end_store'],
            'serial_no'   => $result['serial_no'],
        ];

        $fleetStatisticsPackage = $this->getFleetStatisticsPackage($statisticsParameter);
        $fleetStatisticsShuttleBus = $this->getFleetStatisticsShuttleBus($statisticsParameter);

        //申请人信息
        $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }

        $reason_type_list = array_column($this->getReasonType(),'type_txt','type');
        if ($result['reason_type'] == 99) {
            $show_reason = $reason_type_list[$result['reason_type']] . '-' . $result['reason'] ?? '';
        } else {
            $show_reason = $reason_type_list[$result['reason_type']];
        }

        //组织详情数据
        $detailLists = [
            'apply_parson'       => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? ''),
            'apply_department'   => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
            'car_type'           => $carType['type_txt'],
            'capacity'           => $result['capacity'] . $this->getTranslation()->_('piece') ?? '',
            'arrive_time'        => $result['expected_date'] ?? '',
            'region'             => $result['region'] ?? '',
            'reason_application' => $show_reason,
            'photo'              => $result['image_path'] ?? '',
            'app_type'           => $result['audit_type_title'] ?? '',
        ];
        if ($result['audit_type'] == enums::$fleet_audit_type['FD_courier']) { //如果是FD Courier类型的加班车申请，需要显示司机的工号

            if (!empty($result['via_store_ids'])) {
                $storeNameArr = [
                    $result['start_store_short_name'] ?? '',
                    $result['via_store_short_name'] ?? '',
                    $result['end_store_short_name'] ?? '',
                ];
                $storeNameArr = array_filter($storeNameArr);
                $detailLists  = array_merge($detailLists, [
                    'temporary_route' => implode("-", $storeNameArr),
                    'fd_driver_id'    => $result['fd_courier_id'] ?? '',
                ]);
            } else {
                $detailLists = array_merge($detailLists, [
                    'start_store'  => $result['start_store_name'] ?? '',
                    'end_store'    => $result['end_store_name'] ?? '',
                    'fd_driver_id' => $result['fd_courier_id'] ?? '',
                ]);
            }
        } else {
            $storeNameArr = [$result['start_store_short_name'] ?? '', $result['via_store_short_name'] ?? '', $result['end_store_short_name'] ?? ''];
            $storeNameArr = array_filter($storeNameArr);
            $detailLists = array_merge($detailLists, [
                'temporary_route'    => implode("-", $storeNameArr),
            ]);
        }

        if ($result['status'] == enums::$audit_status['approved']) { //已经审批通过，需要显示车牌、公司
            $detailLists = array_merge(['company' => $result['company_name'] ?? '', 'plate_number' => $result['plate_number'] ?? ''], $detailLists, ['driver_name' => $result['driver'] ?? '', 'driver_phone' => $result['driver_phone'] ?? '']);
        }
        if ($result['status'] == enums::$audit_status['dismissed']) { //已经驳回，需要显示驳回原因
            $detailLists = array_merge($detailLists, [
                'reject_reason1' => $result['reject_type_text'] ?? '',
                'remark'        => $result['reject_reason'] ?? '',
            ]);
        }

        $storeInfo = (new SysStoreServer())->getStoreByid($staff_info['sys_store_id']);
        if (!empty($storeInfo)) {
            $pieceMangerInfo = SysManagePieceModel::findFirst([
                'conditions' => 'id = :manage_piece:',
                'bind'       => ['manage_piece' => $storeInfo['manage_piece']],
                'column'     => 'id,name',
            ]);
            $pieceName  = $pieceMangerInfo->name ?? "";

            $regionMangerInfo = SysManageRegionModel::findFirst([
                'conditions' => 'id = :manage_region:',
                'bind'       => ['manage_region' => $storeInfo['manage_region']],
                'column'     => 'id,name',
            ]);
            $regionName  = $regionMangerInfo->name ?? "";
        }

        $detailLists = array_merge($detailLists, [
            'ot_detail_6'        => $storeInfo['name'] ?? "",
            'manage_region_name' => $regionName ?? "",
            'manage_piece_name'  => $pieceName ?? "",
        ]);

        $head = [
            'title'       => $this->auditlist->getAudityType(enums::$audit_type['VA']),
            'id'          => $result['id'],
            'staff_id'    => $result['submitter_id'],
            'type'        => enums::$audit_type['VA'],
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['status'],
            'status_text' => $this->auditlist->getAuditStatus('10' . $result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
            'show_schedule_status' => $result['schedule_banner']['show_schedule_status'] ?? 0,
            'schedule_status_text' => $result['schedule_banner']['schedule_status_text'] ?? '',
        ];
        //调度信息
        if (in_array($result['status'], [enums::APPROVAL_STATUS_CANCEL, enums::APPROVAL_STATUS_REJECTED])) {
            $head['schedule_state']   = FleetAuditModel::CADENCE_STATUS_NORMAL;
        } else {
            $head['schedule_state']   = $result['cadence_status'] ?? FleetAuditModel::CADENCE_STATUS_PENDING;
        }
        $head['terminate_reason'] = $result['terminate_reason'] ?? "";

        $returnData['data']['head'] = $head;
        $returnData['data']['detail'] = $this->format($detailLists);
        $returnData['data']['extend'] = $result['extend'] ?? [];         //时刻表
        $returnData['data']['cadence_detail'] = $result['cadence_detail'] ?? []; //调度信息
        $returnData['data']['statistics_package'] = $fleetStatisticsPackage['data'] ?? NULL;//当日包裹详情
        $returnData['data']['statistics_shuttle_bus'] = $fleetStatisticsShuttleBus['data'] ?? NULL;//当日班车详情
        $returnData['data']['ccd'] = $result['ccd'] ?? null;

        return $returnData;
    }

    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return array[]
     */
    public function genSummary(int $auditId, $user): array
    {
        $info = $this->fleet->getFleetInfo($auditId);
        $info['start_store']  = $info['start_store'] ?? '';
        $storeNames = (new SysStoreServer())->getStoreName([$info['start_store']]);
        $info['start_store_name'] = $storeNames[$info['start_store']] ?? '';
        if (!isset($info['car_type']) || empty($info)) {
            return [];
        }

        return [
            [
                'key'   => "car_type",
                'value' => $this->getCarTypeList($info['car_type'])['type_txt'] ?? '',
            ],
            [
                'key'   => "arrive_time",
                'value' => $info['expected_date'] ?? '',
            ],
            [
                'key'   => "start_store",
                'value' => $info['start_store_name'],
            ],
        ];
    }

    /**
     * 设置回调属性
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     * @throws \Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $detail = FleetAuditModel::findFirst($auditId);
        if (empty($detail)) {
            throw new \Exception("fleet info error");
        }
        if ($detail->wf_role == 'fleet_new_v2') {
            $this->setPropertyV2($auditId, $state, $extend, $isFinal);
        } else {
            $this->setPropertyV1($auditId, $state, $extend, $isFinal);
        }

        //驳回，撤销 同步java
        if ($isFinal && in_array($state, [enums::APPROVAL_STATUS_REJECTED, enums::APPROVAL_STATUS_CANCEL])) {
            $data['id']            = $detail->id;
            $data['serialNo']      = $detail->serial_no;
            $data['endStore']      = $detail->end_store;
            $data['startStore']    = $detail->start_store;
            $data['viaStoreIds']   = $detail->via_store_ids;
            $data['approvalState'] = $state;
            $data['expectedTime']  = date('Y-m-d H:i:s', strtotime($detail->expected_date));
            $data['carType']       = $detail->car_type;
            $data['dataSource']    = 1;//1: by数据 2:ms数据

            AuditCallbackServer::createData(AuditListEnums::APPROVAL_TYPE_FLEET, $data);
        }
    }

    /**
     * 撤销，驳回。同步 java
     * @param $data
     * @return bool
     * @throws BusinessException
     */
    public function delayCallBack($data)
    {
        $rmq = new RocketMQ('sync-fleet-audit-status');
        $rmq->setHandleType(RocketMQ::TAG_FLEET_TMP_LINE_STATISTICS_SAVE);
        $rid = $rmq->sendMsgByTag($data);
        $this->logger->write_log('backyard to hcm rmq-sync-fleet-audit-status exception: ' . $rid, 'info');
        if(!$rid) {//mq 发送失败
            $this->logger->write_log(['FleetServer-setProperty-mq-fail' => $data]);
            throw new BusinessException($this->getTranslation()->_('data_error'));
        }

        return true;
    }

    /**
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return void
     * @throws ValidationException
     * @throws \FlashExpress\bi\App\library\Exception\InnerException
     */
    public function setPropertyV1(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $detail = FleetAuditModel::findFirst($auditId);
        if (empty($detail)) {
            throw new \Exception("fleet info error");
        }
        if ($isFinal) {
            $this->getDI()->get('db')->updateAsDict(
                'fleet_audit',
                ['status' => $state],
                'id = '. $auditId
            );
            if ($state == enums::APPROVAL_STATUS_REJECTED) {
                $state = enums::$audit_status['dismissed'];

                //推送给申请人
                $staff_info_id = $detail instanceof FleetAuditModel ? $detail->getSubmitterId() : '';
                $lang = (new StaffServer())->getLanguage($staff_info_id);

                $message_title  = $this->getTranslation($lang)->_('6006');
                $audit_type     = $this->getTranslation($lang)->_('6011');

                $pushParam = [
                    'staff_info_id' => $staff_info_id,
                    'message_title' => $message_title,
                    'audit_type'    => $audit_type,
                    'lang'    => $lang,
                ];
                $this->public->pushMessageToSubmitter($pushParam);
            }

            //更新staff_audit_approval表
            $data = StaffAuditApprovalModel::findFirst([
                'conditions' => 'type = :type: and audit_id = :audit_id: and is_shown = 2 and level = 1',
                'bind' => [
                    'audit_id' => $auditId,
                    'type' => AuditListEnums::APPROVAL_TYPE_FLEET,
                ],
            ]);
            if ($data instanceof StaffAuditApprovalModel) {
                $data->status = $state;
                $data->update();
            }
            //新增逻辑 撤销操作要同步 java
            $setting = (new SettingEnvServer())->getSetVal('fleet_sync_switch');
            if($state == enums::APPROVAL_STATUS_CANCEL && (!empty($setting) && $setting == 1)){
                $url = $this->config->api->java_http_url;
                $param['serial_no'] = $detail->getSerialNo();
                $url .= "/svc/fleet/audit/tandem/change_status";
                $res = $this->httpPost($url, $param);
                if(empty($res['code']) || $res['code'] != 1){//回滚
                    $this->logger->write_log("fleet sync cancel {$auditId} ".json_encode($param).json_encode($res));
                    throw new \Exception('fleet sync cancel error');
                }
            }

            //抄送
            //加班车审批通过后，判断申请人所在网点类型为SP/DC/BDC/SHOP/PDC时，抄送给指定工号658649
            // 先注释掉不要删除
//            if (isCountry('TH') && $state == enums::APPROVAL_STATUS_APPROVAL) {
//                $submitterInfo = $this->staff->getStaffInfoById($detail->getSubmitterId());
//                if (!empty($submitterInfo) && in_array($submitterInfo['category'], [1,2,4,5,7,10,14])) {
//                    $request = (new ApplyRepository())->getApplyObject(AuditListEnums::APPROVAL_TYPE_FLEET, $auditId);
//                    if (!empty($request)) {
//                        $ccStaffInfoArr = [658649];
//                        if (RUNTIME == 'dev') {
//                            $ccStaffInfoArr[] = 27003;
//                        }
//                        (new PushServer($this->lang, $this->timezone))->sendCc($request, $ccStaffInfoArr);
//                    }
//                }
//            }

        } else {
            $approvals = $extend['approval'];
            $nextNodeId = $extend['next_node_id'];
            if ($state == enums::APPROVAL_STATUS_APPROVAL) { //审批同意
                $this->fleet->updateFleetInTime(['fleetId' => $auditId]);

                //需重新获取下
                $detail = FleetAuditModel::findFirst($auditId);

                //当前节点是否为最后一个审批节点
                $server = new WorkflowServer($this->lang, $this->timezone);
                $applyModel = AuditApplyModel::findFirst([
                    'conditions' => 'biz_value = :audit_id: and biz_type = :audit_type:',
                    'bind' => [
                        'audit_id' => $auditId,
                        'audit_type' => AuditListEnums::APPROVAL_TYPE_FLEET,
                    ],
                ]);
                $isFinal = $server->isFinalNode($applyModel, $extend['staff_id'], $nextNodeId);

                //ccd 前一个审批节点 审核通过 需要同步给 ms 一些数据
                if ($isFinal) {

                    $this->logger->write_log(sprintf("fleet id {%s} update ,result => %s", $auditId, json_encode($isFinal)), 'info');

                    $model = new StaffAuditApprovalModel();
                    $model->type = AuditListEnums::APPROVAL_TYPE_FLEET;
                    $model->audit_id = $auditId;
                    $model->level = 1;
                    $model->status = enums::$audit_status['panding_approval'];
                    $model->is_shown = 2;
                    $model->submitter_id = $detail->submitter ?? '';
                    $model->staff_ids = implode(',', $approvals);
                    $model->save();

                    //多国家开关 目前只上了 泰国
                    $setting = (new SettingEnvServer())->getSetVal('fleet_sync_switch');
                    if(!empty($setting) && $setting == 1){
                        $this->sendFleetDataToMs($detail);
                    }
                }
            }
        }
    }

    /**
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return void
     * @throws \Exception
     */
    public function setPropertyV2(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $detail = FleetAuditModel::findFirst($auditId);
        if (empty($detail)) {
            throw new \Exception("fleet info error");
        }
        if ($isFinal) {
            $this->getDI()->get('db')->updateAsDict(
                'fleet_audit',
                [
                    'status'              => $state,
                    'final_approver'      => $extend['staff_id'],
                    'final_approval_time' => gmdate('Y-m-d H:i:s'),
                ],
                'id = ' . $auditId
            );
            if ($state == enums::APPROVAL_STATUS_REJECTED) {

                //推送给申请人
                $staff_info_id = $detail instanceof FleetAuditModel ? $detail->getSubmitterId() : '';
                $lang = (new StaffServer())->getLanguage($staff_info_id);

                $message_title  = $this->getTranslation($lang)->_('6006');
                $audit_type     = $this->getTranslation($lang)->_('6011');

                $pushParam = [
                    'staff_info_id' => $staff_info_id,
                    'message_title' => $message_title,
                    'audit_type'    => $audit_type,
                    'lang'    => $lang,
                ];
                $this->public->pushMessageToSubmitter($pushParam);
            }
            //
            if ($state == enums::APPROVAL_STATUS_APPROVAL) { //审批同意
                $detail->setFinalApprover($extend['staff_id']);
                $detail->setFinalApprovalTime(gmdate('Y-m-d H:i:s'));

                //调用java 新项目 不用rpc  用 http  地址：/svc/fleet/audit/tandem/sync_approve_info
                $this->sendFleetDataToMs($detail);
            }
        }
    }

    /**
     * @param $detail
     * @return void
     * @throws \Exception
     */
    protected function sendFleetDataToMs($detail)
    {
        $url = $this->config->api->java_http_url;
        $url .= "/svc/fleet/audit/tandem/sync_approve_info";

        $detail_arr = $detail->toArray();
        $auditId    = $detail_arr['id'];

        if (!empty($detail_arr['final_approver'])) {
            $staffInfo = (new StaffRepository())->getStaffInfoOne($detail_arr['final_approver'], 'staff_info_id,name');
            if (empty($staffInfo)) {
                if ($detail_arr['final_approver'] == enums::SYSTEM_STAFF_ID) {
                    $approvalName = 'SuperAdmin';
                } else {
                    $approvalName = '';
                }
            } else {
                $approvalName = $staffInfo['name'];
            }
        }

        $param['audit_id']             = $detail_arr['id'];       //申请主键
        $param['serial_no']            = $detail_arr['serial_no'];//申请编号
        $param['start_store']          = $detail_arr['start_store'];
        $param['via_store_ids']        = $detail_arr['via_store_ids'];
        $param['end_store']            = $detail_arr['end_store'];
        $param['via_store_short_name'] = $detail_arr['via_store_short_name'];
        $param['region']               = $detail_arr['region'];
        $param['reason_type']          = $detail_arr['reason_type'];
        $param['reason']               = $detail_arr['reason'];
        $param['submitter_id']         = $detail_arr['submitter_id'];
        $param['submitter_time']       = $detail_arr['created_at'];
        $param['expected_date']        = $detail_arr['expected_date'];//申请时填写的期望到达时间  ---LocalDateTime类型
        $param['car_type']             = $detail_arr['car_type'];     //车辆类型
        $param['capacity']             = $detail_arr['capacity'];     //装载量
        $param['audit_type']           = $detail_arr['audit_type'];   //申请类型 1:普通加班车 2:FDC
        $param['province_code']        = $detail_arr['province_code'];
        $param['approve_time']         = $detail_arr['approve_time'];
        $param['fdc_type']             = $detail_arr['set_flag'];
        $param['choose_btn_num']       = $detail_arr['select_btn_num'];
        $param['final_approver']       = $detail_arr['final_approver']? intval($detail_arr['final_approver']): 0;
        $param['final_approver_name']  = empty($approvalName) ? '': $approvalName;
        $param['final_approval_time']  = $detail_arr['final_approval_time'] ?: '';

        //新增的
        if(!empty($detail_arr['fd_courier_id'])){
            $param['fd_courier_id'] = $detail_arr['fd_courier_id'];
            $param['fd_courier_name'] = $detail_arr['fd_courier_name'];
            $param['fd_courier_phone'] = $detail_arr['fd_courier_phone'];
            $param['fd_courier_plate'] = $detail_arr['fd_courier_plate'];

        }

        $res = $this->httpPost($url, $param);
        if (empty($res['code'])) {//回滚
            $this->logger->write_log("fleet sync {$auditId} " . json_encode($param) . json_encode($res));
            throw new \Exception('fleet sync error');
        }
        if ($res['code'] != 1) {
            $this->logger->write_log("fleet sync {$auditId} " . json_encode($param) . json_encode($res), 'notice');
            throw new BusinessException($res['message']);
        }
        //同步成功
        if ($res['code'] == 1) {
            if (empty($res['data'])) {
                $this->logger->write_log("fleet sync {$auditId} 重复同步 要看看 咋回事");
            } else {
                $this->logger->write_log("fleet sync {$auditId} 同步成功", 'info');
            }
        } else {//同步有问题 需要回滚
            $this->logger->write_log("fleet sync {$auditId} 未知错误 " . json_encode($res));
            throw new \Exception('sync fleet error');
        }
    }

    /**
     * @param $auditId
     * @param $user
     * @param null $state
     * @return int[]
     * @throws ValidationException
     */
    public function getWorkflowParams($auditId, $user, $state = null): array
    {
        //获取加班车详情
        $builder = $this->modelsManager->createBuilder();
        $builder->from(FleetAuditModel::class);
        $builder->where("id = :fleet_id:", ['fleet_id' => $auditId]);
        $builder->columns('id,submitter_id,audit_type,set_flag,running_mileages,start_store');
        $fleetInfo = $builder->getQuery()->getSingleResult();
        if (empty($fleetInfo)) {
            throw new ValidationException('invalid data, auditId:' . $auditId);
        }

        //获取申请人所在网点
        $submitterInfo = $this->staff->checkoutStaff($fleetInfo->submitter_id);

        //申请人是否为网点负责人
        $info = SysStoreModel::findFirst([
            'conditions' => "manager_id = :staff_info_id: and id = :store_id:",
            'bind' => [
                'staff_info_id'  => $fleetInfo->submitter_id,
                'store_id'       => $submitterInfo['organization_id'],
            ],
        ]);
        //网点类型
        $soreInfo = SysStoreModel::findFirst([
            'conditions' => "id = :store_id:",
            'bind' => [
                'store_id' => $submitterInfo['organization_id'],
            ],
        ]);

        $manger_all_level = false;
        if (!empty($soreInfo)) {
            $soreInfo = $soreInfo->toArray();
            if (isset($soreInfo['manage_piece']) && $soreInfo['manage_piece']) {
                //片区负责人
                $pieceInfo = SysManagePieceModel::findFirst([
                    "id = :manage_piece:",
                    "bind" => [
                        "manage_piece" => $soreInfo['manage_piece'],
                    ],
                    "columns" => "manager_id",
                ]);
                $pieceManager = !empty($pieceInfo) ? $pieceInfo->manager_id : "";
            }

            if (isset($soreInfo['manage_region']) && $soreInfo['manage_region']) {
                //大区负责人
                $regionInfo = SysManageRegionModel::findFirst([
                    "id = :manage_region:",
                    "bind" => [
                        "manage_region" => $soreInfo['manage_region'],
                    ],
                    "columns" => "manager_id",
                ]);
                $regionManager = !empty($regionInfo) ? $regionInfo->manager_id : "";
            }
        }

        if (!empty($submitterInfo['department_id'])) {
            //部门负责人
            //[1]获取部门的部门链
            $deptInfo = SysDepartmentModel::findFirst($submitterInfo['department_id']);
            if (!empty($deptInfo)) {
                $departmentIds = explode('/', $deptInfo->ancestry_v3);
                $builder = $this->modelsManager->createBuilder();
                $builder->from(SysDepartmentModel::class);
                $builder->inWhere("id", $departmentIds);
                $builder->andWhere("level = :level:", ["level" => 2]);
                $builder->columns('id,manager_id');
                $departmentManagerInfo = $builder->getQuery()->getSingleResult();
                if ($departmentManagerInfo) {
                    $departmentManager = $departmentManagerInfo->manager_id;
                }
            }
        }
        $mangerList = array_values(array_filter([$pieceManager ?? "", $regionManager ?? "", $departmentManager ?? ""]));
        if (empty($mangerList)) {
            $manger_all_level = true;
        } else {
            $managerListInfo = HrStaffInfoModel::find([
                "staff_info_id in({staff_info_id:array}) and state = 1 and formal = 1 and is_sub_staff = 0",
                "bind" => [
                    "staff_info_id" => $mangerList,
                ],
                "columns" => 'staff_info_id',
            ])->toArray();

            if (empty($managerListInfo)) {
                $manger_all_level = true;
            }
        }

        //始发网点类型
        //网点类型
        $startSoreInfo = SysStoreModel::findFirst([
            'conditions' => "id = :store_id:",
            'bind' => [
                'store_id' => $fleetInfo['start_store'],
            ],
        ]);
        $startStoreCategory = !empty($startSoreInfo) ? $startSoreInfo->category : 0;

        if ($fleetInfo->audit_type == enums::$fleet_audit_type['Normal'] && $fleetInfo->set_flag == self::FDC_STATE_FIT_FDC ||
            $fleetInfo->audit_type == enums::$fleet_audit_type['FD_courier'] && $fleetInfo->set_flag == self::FDC_STATE_NOT_FIT_FDC ||
            in_array($startStoreCategory, [
                enums::$stores_category['sp'],
                enums::$stores_category['bdc'],
            ]) && $fleetInfo->set_flag == self::FDC_STATE_DEFAULT && $fleetInfo->running_mileages == 0
        ) {
            $isNeedDepartmentManager = true;
        } else {
            $isNeedDepartmentManager = false;
        }

        return [
            'w_f_condition_category'     => $soreInfo['category'] ?? 0,
            'category'                   => $startStoreCategory,
            'is_store_manager'           => !empty($info) ? 1 : 0,
            'job_title'                  => $submitterInfo['job_title'],
            'is_need_department_manager' => $isNeedDepartmentManager ? 1 : 0,
            'is_manager_all_leave'       => $manger_all_level ? 1 : 0,
            'audit_type'                 => $fleetInfo->audit_type, // 加班车类型
            'set_flag'                   => in_array($fleetInfo->set_flag,
                [self::FDC_STATE_FIT_FDC, self::FDC_STATE_NOT_FIT_FDC]) ? 1 : 2,
        ];
    }

    /**
     * 查找图片
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function findSysAttachment($params): array
    {
        $type = $this->processingDefault($params, 'oss_bucket_type');
        $key = $this->processingDefault($params, 'oss_bucket_key');

        if (empty($type) && empty($key)) {
            throw new ValidationException('miss args');
        }

        return SysAttachmentModel::find([
            'conditions' => 'oss_bucket_type = :ossBucketType: AND oss_bucket_key = :ossBucketKey: AND deleted = 0',
            'bind' => [
                'ossBucketType' => $type,
                'ossBucketKey' => $key,
            ],
            'order' => 'created_at ASC',
        ])->toArray();
    }

    /**
     * 司机职位
     * @return array
     */
    public function getDriverJotTitle(): array
    {
        $job_title = (new SettingEnvServer())->getSetVal('fleet_driver_jot_title');

        if (!empty($job_title)) {
            $result = explode(',', $job_title);
        } else {
            $result = [enums::$job_title['van_courier']];
        }
        return $result;
    }

    /**
     * 模糊搜索司机工号
     * @param $staff_id
     * @return array
     */
    public function getStaffDriverById($staff_id)
    {
        //先根据职位获取到职位id
        $job_title_ids = $this->getDriverJotTitle();

        $drivers = StaffInfoModel::find([
            'conditions' => 'job_title in ({job_title:array}) AND state in (1,3) and  id like :id:',
            'bind' => [
                'job_title' => $job_title_ids,
                'id' => $staff_id.'%',
            ],
            'columns' => 'id, name',
            'limit' => 50,
        ])->toArray();

        return  array_column($drivers, 'name', 'id');
    }

    /**
     * 根据司机工号获取 【司机姓名】、【司机电话】、【车牌号】、【车型】
     * @param $staff_id
     * @return array
     */
    public function getStaffDetail($staff_id)
    {
        try{

            //获取司机的信息
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('a.mobile,a.name,b.vehicle_size,b.plate_number  ');
            $builder->from(['a' => HrStaffInfoModel::class]);
            $builder->leftJoin(VehicleInfoModel::class, 'a.staff_info_id = b.uid', 'b');
            $builder->andWhere('a.staff_info_id = :staff_info_id:', [
                'staff_info_id' => $staff_id,
            ]);
            $driverInfo  = $builder->getQuery()->execute()->toArray();
            $driverInfo = $driverInfo[0] ?? [];
            if(strtolower(env('country_code')) == 'th') {
                $vehicle_setting = UC('vehicleInfo');
            } elseif (strtolower(env('country_code')) == 'ph') { //菲律宾
                $vehicle_setting = VehicleInfoEnums::CONFIG_VEHICLE_INFO;
            } elseif (strtolower(env('country_code')) == 'my') {
                $vehicle_setting = VehicleInfoEnumsMy::CONFIG_VEHICLE_INFO;
            } else {
                $vehicle_setting = [];
            }

            if (!empty($driverInfo['vehicle_size']) && !empty($vehicle_setting)) {
                //根据vehicle_info表中对应的车型，拿到sys_fleet_van_type表中对应的车型的code
                $vehicle_size_conf = array_column($vehicle_setting['vehicle_size'], 'label', 'value');
                $van_value  =   $vehicle_size_conf[$driverInfo['vehicle_size']] ?? '';

                $vehicle_size = SysFleetVanTypeModel::findFirst([
                    'conditions' => "van_value = :van_value: and deleted = 0",
                    'bind' => [
                        'van_value' => $van_value,
                    ],
                ]);
                $driverInfo['vehicle_size'] = $vehicle_size->code ?? ''; //获取到code,没有就返回空
            }
            return  $driverInfo;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage(),'error');
            return [];
        }
    }

    //ms 展示 申请列表用
    public function apply_list($param){
        $staff_id  = intval($param['user_info']['id']);
        $page      = empty(intval($param['page'])) ? 1 : intval($param['page']);
        $page_size = empty(intval($param['pageSize'])) ? 50 : intval($param['pageSize']);
        if (empty($staff_id)) {
            throw new \Exception("staff id error");
        }


        $count = FleetAuditModel::findFirst([
            'columns' => 'count(1) num',
            'conditions' => "status in ({status_arr:array}) and submitter_id = :staff_id:",
            'order' => 'id desc',
            'bind' => ['status_arr' => array(
                enums::APPROVAL_STATUS_PENDING,
                enums::APPROVAL_STATUS_APPROVAL,
                enums::APPROVAL_STATUS_REJECTED,
                enums::APPROVAL_STATUS_CANCEL,
                enums::APPROVAL_STATUS_ABANDON),
                'staff_id' => $staff_id],
        ]);
        if(empty($count))
            return array('count' => 0,'list' => array());



        $data = FleetAuditModel::find([
            'columns' => 'id,serial_no,car_type,capacity,expected_date,start_store,end_store,submitter_id,status,created_at,updated_at',
            'conditions' => "status in ({status_arr:array}) and submitter_id = :staff_id:",
            'order' => 'id desc',
            'bind' => ['status_arr' => array(
                enums::APPROVAL_STATUS_PENDING,
                enums::APPROVAL_STATUS_APPROVAL,
                enums::APPROVAL_STATUS_REJECTED,
                enums::APPROVAL_STATUS_CANCEL,
                enums::APPROVAL_STATUS_ABANDON),
                'staff_id' => $staff_id],
            'offset' => ($page - 1) * $page_size,
            'limit' => $page_size,
        ])->toArray();

        if(empty($data))
            return array();

        //拼接 网点 名称
        $store_ids = array_column($data,'start_store');
        $store_ids = array_merge($store_ids,array_column($data,'end_store'));
        $store_ids = array_values(array_unique($store_ids));
        $store_data = SysStoreModel::find([
            'conditions' => "id in ({store_ids:array})",
            'bind' => ['store_ids' => $store_ids],
            'columns' => 'id,name',
        ])->toArray();
        $store_info = array_column($store_data,'name','id');

        //拼接状态 和 车辆类型 名称
        $type_list = $this->getCarTypeList();
        $type_list = array_column($type_list,'type_txt','type');

        //状态描述
        $status_list = enums::$approval_status;
        $status_list[enums::APPROVAL_STATUS_ABANDON] = '4004';//追加 已作废状态

        foreach ($data as &$da){
            $add_hour = $this->config->application->add_hour;
            $da['created_at'] = date('Y-m-d H:i:s',strtotime($da['created_at']) + $add_hour * 3600);
            $da['expected_date'] = date('Y-m-d H:i:s',strtotime($da['expected_date']));

            $da['start_store_name'] = empty($store_info[$da['start_store']]) ? '-' : $store_info[$da['start_store']];
            $da['end_store_name'] = empty($store_info[$da['end_store']]) ? '-' : $store_info[$da['end_store']];

            $da['car_type'] = empty($type_list[$da['car_type']]) ? 'null' : $type_list[$da['car_type']];
            $da['staff_text'] = "{$param['user_info']['name']}({$staff_id})";
            $da['status_text'] = $this->getTranslation()->_($status_list[$da['status']]);
        }

        $return = array('count' => intval($count->num), 'list' => $data);
        return $return;
    }

    /**
     * @description 获取待审批的加班车列表
     * @return array
     * @throws \Exception
     */
    public function getPendingFleet($params)
    {
        $serial_no = $params['serialNo'];

        if (empty($serial_no)) {
            throw new \Exception('miss args', ErrCode::VALIDATE_ERROR);
        }

        //获取目标数据
        $fleetDetailInfo = FleetAuditModel::findFirst([
            'serial_no = :serial_no:',
            'bind' => [
                'serial_no' => $serial_no,
            ],
            'start_store,end_store,via_store_ids',
        ]);
        if (empty($fleetDetailInfo)) {
            throw new \Exception('data not exist', ErrCode::VALIDATE_ERROR);
        }

        //获取全部目标网点
        $srcStoreIds = $this->getStoreIds([$fleetDetailInfo->start_store], $fleetDetailInfo->via_store_ids);

        $time        = date("Y-m-d H:i:s", strtotime("-1 month"));
        $list        = FleetAuditModel::find([
            'created_at > :time: and status = 1 and end_store = :end_store: and serial_no != :serial_no:',
            'bind' => [
                'time'      => $time,
                'end_store' => $fleetDetailInfo->end_store,
                'serial_no' => $serial_no,
            ],
            'columns' => 'start_store,end_store,via_store_ids,via_store_short_name,serial_no,car_type,expected_date',
        ])->toArray();

        if (empty($list)) {
            return [
                'tmp_pending_num'     => 0,
                'tmp_pending_details' => [],
            ];
        }

        //获取全部的交集网点
        foreach ($list as $key => $v) {
            $storeIds = $this->getStoreIds([$v['start_store']], $v['via_store_ids']);
            $list[$key]['statistic_store'] = array_intersect($srcStoreIds, $storeIds);
            $list[$key]['line_ids'] = $this->getStoreIds([$v['start_store'], $v['end_store']], $v['via_store_ids']);
        }

        //获取全部网点名
        $ids = array_merge(...array_column($list, 'statistic_store'));
        if (empty($ids)) {
            return [
                'tmp_pending_num'     => 0,
                'tmp_pending_details' => [],
            ];
        }
        $totalStoreIds = array_merge(...array_column($list, 'line_ids'));

        //PH01010155
        $storeInfo = SysStoreModel::find([
            'id in ({ids:array})',
            'bind' => [
                'ids' => array_merge($ids, $totalStoreIds),
            ],
            'columns' => 'id,short_name',
        ])->toArray();
        $storeInfo = array_column($storeInfo, 'short_name', 'id');

        //获取全部的Car type
        $carTypes = SysFleetVanTypeModel::find([
            'conditions' => " deleted = 0 ",
            'columns' => 'van_value as type_txt, code as type',
        ])->toArray();
        $carTypesList = array_column($carTypes, 'type_txt', 'type');

        $count          = 0;
        $pendingDetails = [];
        foreach ($list as $item) {

            if (empty($item['statistic_store'])) {
                continue;
            }

            $count += 1;
            $pending = array_map(function($store_id) use($item, $carTypesList, $storeInfo) {

                $lineName = !empty($item['via_store_short_name']) ? $storeInfo[$item['start_store']] . '-' . $item['via_store_short_name'] . '-' . $storeInfo[$item['end_store']]
                    : $storeInfo[$item['start_store']] . '-' . $storeInfo[$item['end_store']];
                return [
                    'serial_no'            => $item['serial_no'],
                    'line_name'            => $lineName,
                    'car_type'             => $item['car_type'],
                    'car_type_text'        => $carTypesList[$item['car_type']] ?? '',
                    'store_id'             => $store_id,
                    'store_name'           => $storeInfo[$store_id] ?? '',
                    "expected_arrive_date" => $item['expected_date'],
                ];

            }, $item['statistic_store']);
            $pendingDetails = array_merge($pendingDetails, $pending);
        }

        return [
            'tmp_pending_num'     => $count,
            'tmp_pending_details' => $pendingDetails,
        ];
    }

    /**
     * @description 获取被统计网点
     * @param $start_store
     * @param $via_store_ids
     * @return array
     */
    private function getStoreIds($store_ids, $via_store_ids)
    {
        $viaStoreIds = explode('-', $via_store_ids);
        return array_values(array_filter(array_merge($viaStoreIds, $store_ids)));
    }

    /**
     * @description 发送车线调度变更信息
     * @param array $params
     * @return bool
     */
    public function sendSchedulingChangeMessage($params = [])
    {
        $validations = [
            'audit_id'              => 'Int',        //主键ID
            'schedule_type'         => 'Int',        //调度进度 1=变更 2=终止调度
            'terminate_reason_type' => 'Int',        //终止原因类型：1=重复申请加班车 2=未找到车 3=客户取消订单 4=评估包裹数量过多 99=其他
            'terminate_reason'      => 'StrLenGe:0', //终止原因
        ];
        $this->validateCheck($params, $validations);

        $fleetInfo = FleetAuditModel::findFirst([
            "id = :id:",
            "bind" => [
                'id' => $params['audit_id'],
            ],
        ]);
        if (empty($fleetInfo)) {
            throw new ValidationException("no valid data");
        }

        $storeInfo = SysStoreModel::find([
            "id IN({ids:array})",
            "bind" => [
                "ids" => [$fleetInfo->start_store, $fleetInfo->end_store],
            ],
            "columns" => "id,name",
        ])->toArray();
        $storeInfoArr = array_column($storeInfo, 'name', 'id');

        //线路名称：开始网点 + 途径网点 + 结束网点
        if (!empty($fleetInfo->via_store_short_name)) {
            $lineName = $storeInfoArr[$fleetInfo->start_store].'-'.$fleetInfo->via_store_short_name.'-'.$storeInfoArr[$fleetInfo->end_store];
        } else {
            $lineName = $storeInfoArr[$fleetInfo->start_store].'-'.$storeInfoArr[$fleetInfo->end_store];
        }

        $carType = $this->getFleetAuditType();
        $carTypeTxt = array_column($carType, 'type_txt_key', 'type');

        $terminateReason    = "";
        $terminateReasonKey = "";
        if ($params['terminate_reason_type'] == self::TERMINATE_TYPE_OTHER) {
            $terminateReason = $params['terminate_reason'];
        } else {
            $terminateReasonKey = 'terminate_reason_' . $params['terminate_reason_type'];
        }

        $message = [
            "audit_id"             => $fleetInfo->id,
            "schedule_type"        => $params['schedule_type'],
            "terminate_reason_key" => $terminateReasonKey,
            "terminate_reason"     => $terminateReason,
            "expected_date"        => $fleetInfo->expected_date,
            "line_name"            => $lineName,
            "audit_type_key"       => $carTypeTxt[$fleetInfo->audit_type] ?? '',
        ];

        $redis = $this->getDI()->get('redisLib');
        $fleet_scheduling_change_key = self::REDIS_FLEET_LINE_SCHEDULING_MESSAGE_KEY;
        $content = json_encode($message, JSON_UNESCAPED_UNICODE);

        $this->logger->write_log("[sendSchedulingChangeMessage lpush]" . json_encode($content, JSON_UNESCAPED_UNICODE), 'info');
        $redis->lpush($fleet_scheduling_change_key, $content);

        return true;
    }

    /**
     * @description 发送消息
     * @param array $data
     *  audit_id                int     主键ID
     *  schedule_type           int     调度进度 1=变更 2=终止调度
     *  terminate_reason_key    string  原因翻译KEY
     *  terminate_reason        string  其他原因手动填写的
     *  expected_date           string  期望到达日期
     *  line_name               string  线路ID
     *  audit_type_key          string  加班车类型翻译KEY
     * @return void
     */
    public function SendMessage($data = [])
    {
        $fleetId = $data['audit_id'];

        //找人 申请人 + 除了ccd以外的审批人
        $fleetInfo = AuditApplyModel::findFirst([
            "biz_type = :biz_type: and biz_value = :biz_value:",
            "bind" => [
                "biz_value" => $fleetId,
                "biz_type"  => AuditListEnums::APPROVAL_TYPE_FLEET,
            ],
        ]);
        if (empty($fleetInfo)) {
            $this->logger->write_log("[SendMessage] 无有效加班车数据, id:" . $fleetId, "info");
            return;
        }

        //获取模版
        $this->doSend($data, [$fleetInfo->submitter_id]);
    }

    /**
     * @description 发送消息 + push
     * @param array $data
     *  audit_id                int     主键ID
     *  schedule_type           int     调度进度 1=变更 2=终止调度
     *  terminate_reason_key    string  原因翻译KEY
     *  terminate_reason        string  其他原因手动填写的
     *  expected_date           string  期望到达日期
     *  line_name               string  线路ID
     *  audit_type_key          string  加班车类型翻译KEY
     * @param array $staffIds 到发送消息工号
     * @return void
     */
    public function doSend($data, $staffIds)
    {
        if (empty($staffIds)) {
            return;
        }
        $publicRepo = new PublicRepository($this->timezone);

        //获取语言
        $staffLang = (new StaffServer())->getBatchStaffLanguage($staffIds);
        foreach ($staffIds as $staffId) {

            $lang = $staffLang[$staffId] ?? "en";
            $t = $this->getTranslation($lang);

            if ($data['schedule_type'] == 1) { // 加班车调度进度变更通知
                $title = $t->_('message_title_fleet_schedule_change_notice');
            } else { // 加班车调度终止通知
                $title = $t->_('message_title_terminate_fleet_notice');
            }

            $param = [
                'staff_info_id'   => $staffId,
                'message_title'   => $title,
                'message_content' => json_encode($data, JSON_UNESCAPED_UNICODE) ?? "",
                'type'            => MessageEnums::MESSAGE_CATEGORY_FLEET_SCHEDULE_NOTICE,
                'relate_id'       => $data['audit_id'],
            ];
            $publicRepo->sendMessageToSubmitter($param);

            //组织push内容
            if ($data['schedule_type'] == 2) {
                $terminateReason = isset($data['terminate_reason']) && $data['terminate_reason'] ? $data['terminate_reason'] : $t->_($data['terminate_reason_key']);

                $noticeContent = $t->_("fleet_notice_terminate", [
                    'line_name'        => $data['line_name'],
                    'expected_date'    => date("Y-m-d H:i", strtotime($data['expected_date'])),
                    'fleet_type'       => $t->_($data['audit_type_key']),
                    'terminate_reason' => $terminateReason,
                ]);
            } else {
                $noticeContent = $t->_("fleet_notice_change", [
                    'line_name'        => $data['line_name'],
                    'expected_date'    => date("Y-m-d H:i", strtotime($data['expected_date'])),
                    'fleet_type'       => $t->_($data['audit_type_key']),
                ]);
            }

            $pushSuccessParam = [
                'staff_info_id'   => $staffId,
                'message_title'   => $title,
                'message_content' => $noticeContent,
                'type'            => MessageEnums::MESSAGE_CATEGORY_FLEET_SCHEDULE_NOTICE,
                'path'            => 'approval_detail',
                'extend'          => [
                    'edition'          => 'new',
                    'auditId'          => $data['audit_id'],
                    'auditType'        => AuditListEnums::APPROVAL_TYPE_FLEET,
                    'audit_show_type'  => 1,
                    'audit_state_type' => 1,
                    'tab'              => 1,
                    'from'             => 'push',
                    'schedule_change'  => $data['schedule_type'] == 2
                        ? FleetAuditModel::CADENCE_STATUS_TERMINATE
                        : FleetAuditModel::CADENCE_STATUS_CHANGE,
                ],
            ];
            $publicRepo->sendPushToSubmitter($pushSuccessParam);
        }
    }

    /**
     * @description 校验线路是否符合FDC条件
     * @return array
     * @throws ValidationException
     */
    public function checkFleetLine($paramIn): array
    {
        //[1]获取参数
        $arriveTime = $this->processingDefault($paramIn, 'arrive_time', 1);
        $startStore = $this->processingDefault($paramIn, 'start_store', 1);
        $endStore   = $this->processingDefault($paramIn, 'end_store', 1);
        $auditType  = $this->processingDefault($paramIn, 'audit_type', 2);
        $storeIds   = $this->processingDefault($paramIn, 'store_ids', 3);

        $t = $this->getTranslation();

        [$startStoreId,$endStoreId,$viaStoreIds,$shortName] = $this->fullApplyStoreInfo($t, $auditType, $startStore, $endStore, $storeIds);

        $this->logger->write_log(['checkFleetLine'=>[$startStoreId,$endStoreId,$viaStoreIds,$shortName]], "info");

        $auditStoreIds = [$startStoreId, $endStoreId];

        if (!empty($viaStoreIds)) {
            $auditStoreIds = array_merge($auditStoreIds, $viaStoreIds);
        }

        //校验出发网点与目的网点是否相同
        if (strcmp($startStoreId, $endStoreId) == 0) {
            throw new ValidationException($this->getTranslation()->_('fleet_depture_can_no_same'));
        }

        //[2.1]始发站类型sp、bdc
        $storeServer    = new SysStoreServer();
        $startStoreInfo = $storeServer->getStoreInfoByid($startStoreId);
        if (empty($startStoreInfo)) {
            throw new ValidationException('提交网点不存在');
        }

        //[2.2]获取始发网点有没有配置客户揽件虚拟网点 & 始发->目的网点距离
        $params = [
            'audit_store_ids' => implode(',', $auditStoreIds),
        ];
        $result          = $this->doHttpPost($params, "/svc/fleet/audit/tandem/query/audit_store_info");
        $runningMileages = $result['data']['running_mileages'];

        if (!in_array($startStoreInfo['category'], [SysStoreServer::CATEGORY_SP, SysStoreServer::CATEGORY_BDC])) {
            $this->logger->write_log(sprintf("[checkFleetLine]start store %s, date %s, store category 不符合", $startStoreInfo['id'], $arriveTime), "info");
            return self::checkReturn(['data' => ['fdc_state' => self::FDC_STATE_DEFAULT, 'running_mileages' => $runningMileages]]);
        }

        //起始网点为虚拟网点
        $virtualStoreIds = $storeServer->getVirtualStoreInfo();
        if(in_array($startStoreId, $virtualStoreIds)) {
            return self::checkReturn(['data' => ['fdc_state' => self::FDC_STATE_DEFAULT, 'running_mileages' => $runningMileages]]);
        }

        //[2.2]当日用车
        if (strtotime($arriveTime) < time()) {
            throw new ValidationException($this->getTranslation()->_('fleet_time_need_ge_current'));
        }
        if (strtotime($arriveTime) > strtotime(date("Y-m-d 00:00:00", strtotime("+1 days")))) {
            $this->logger->write_log(sprintf("[checkFleetLine]start store %s, date %s, not today", $startStoreInfo['id'], $arriveTime), "info");
            return self::checkReturn(['data' => ['fdc_state' => self::FDC_STATE_DEFAULT, 'running_mileages' => $runningMileages]]);
        }

        //[2.3]始发网点有没有配置客户揽件虚拟网点 & 始发->目的网点距离是否小于50KM
        if (empty($result)) {
            $this->logger->write_log(sprintf("[checkFleetLine]start store %s, request %s, response %s",
                json_encode($startStoreInfo), json_encode($params), json_encode($result)), "info");
            return self::checkReturn(['data' => ['fdc_state' => self::FDC_STATE_DEFAULT, 'running_mileages' => $runningMileages]]);
        }

        if (is_null($runningMileages) || $runningMileages >= 50) {
            $this->logger->write_log("[checkFleetLine]running_mileages or pickup_virtual_store_enabled not fit", "info");
            return self::checkReturn(['data' => ['fdc_state' => self::FDC_STATE_DEFAULT, 'running_mileages' => $runningMileages]]);
        }

        //[2.4]始发网点在期望到达日期前一天是否爆仓
        $bi_params = [
            'store_id' => $startStoreId,
            'date'     => date("Y-m-d", strtotime($arriveTime. '-1 days')),
        ];
        $ret = new ApiClient('bi_rpcv2', '', 'dc.get_store_is_question', $this->lang);
        $ret->setParams($bi_params);
        $res = $ret->execute();
        $this->logger->write_log("dc.get_store_is_question 参数:".json_encode($bi_params).";结果:".json_encode($res),
            'info');
        if ($res['result']['data'] == self::WAREHOUSE_BOOM) {
            $this->logger->write_log(sprintf("[checkFleetLine]start store %s, request %s, %response %s",
                json_encode($startStoreInfo), json_encode($bi_params), json_encode($res)), "info");
            return self::checkReturn(['data' => ['fdc_state' => self::FDC_STATE_DEFAULT, 'running_mileages' => $runningMileages]]);
        }

        //[2.5]是否符合FDC条件
        //始发网点所属的网点 & 在职 & 职位为van courier & 用车日期=当日的
        //实到人数大于2 普通加班车 ，打标记符合
        //实到人数小于等于2 FDC加班车 ，打标记不符合
        $attendanceRepo = new AttendanceRepository($this->lang, $this->timezone);
        $vanCourierOnJobCnt = $attendanceRepo->getAttendanceCourierNum($startStoreId, date("Y-m-d", strtotime($arriveTime)));
        if ($auditType == enums::$fleet_audit_type['Normal'] && $vanCourierOnJobCnt > 2) {
            return self::checkReturn(['data' => ['fdc_state' => self::FDC_STATE_FIT_FDC, 'running_mileages' => $runningMileages]]);
        }

        if ($auditType == enums::$fleet_audit_type['FD_courier'] && $vanCourierOnJobCnt <= 2) {
            return self::checkReturn(['data' => ['fdc_state' => self::FDC_STATE_NOT_FIT_FDC, 'running_mileages' => $runningMileages]]);
        }
        return self::checkReturn(['data' => ['fdc_state' => self::FDC_STATE_DEFAULT, 'running_mileages' => $runningMileages]]);
    }

    /**
     * @description 获取信息from车线项目
     * @param array $paramIn
     * @param string $method
     * @return array
     */
    public function doHttpPost(array $paramIn = [], string $method, $is_post = true): array
    {
        try {
            $url   = $this->config->api->java_http_url;
            $url   .= $method;
            $res   = $this->httpPost($url, $paramIn, null, 10, $is_post);
            if (empty($res['code']) || $res['code'] != 1) {
                $this->logger->write_log("fleet {$method} ".json_encode($paramIn).json_encode($res),'info');
                return [];
            }
            return $res;
        } catch (\Exception $e) {
            $this->logger->write_log("fleet {$method} ".json_encode($paramIn).json_encode($res ?? []));
            return [];
        }
    }

    /**
     * @description 获取van courier应到人数+van courier实到人数
     * @param array $params
     * @return array
     */
    public function getVanCourierAttendanceByAuditId($params = []): array
    {
        $serialNo = $params['serial_no'] ?? "";
        if (empty($serialNo)) {
            return [];
        }
        $fleetInfo = FleetAuditModel::findFirst([
            "serial_no = :serial_no:",
            "bind" => [
                "serial_no" => $serialNo,
            ],
        ]);
        if (empty($fleetInfo)) {
            return [];
        }
        //获取真实网点
        $fleetInfo->start_store = (new SysStoreServer())->getRealStoreById($fleetInfo->start_store);

        $currentDate = date("Y-m-d", strtotime($fleetInfo->expected_date));

        //实到人数
        $attendanceRepo = new AttendanceRepository($this->lang, $this->timezone);
        $staffAttendanceInfoCnt = $attendanceRepo->getAttendanceCourierNum($fleetInfo->start_store, $currentDate);
        $staffShouldAttendanceInfoCnt = $this->getStaffShouldAttendanceInfo($fleetInfo);
        $shouldCnt = is_null($staffShouldAttendanceInfoCnt)? $staffShouldAttendanceInfoCnt: intval($staffShouldAttendanceInfoCnt);

        $this->logger->write_log(sprintf("getVanCourierAttendanceByAuditId %s, staffAttendanceInfoCnt: %d, staffShouldAttendanceInfoCnt: %d",
            $serialNo, $staffAttendanceInfoCnt, $shouldCnt), "info");
        //van_courier_should_attendance_num 应到人数
        //van_courier_actual_attendance_num 实到人数
        return [
            'van_courier_should_attendance_num' => $shouldCnt,
            'van_courier_actual_attendance_num' => $staffAttendanceInfoCnt,
        ];
    }

    /**
     * @description 获取van courier实到人数、应到人数
     * @param $fleetInfo
     * @return mixed
     */
    public function getStaffShouldAttendanceInfo($fleetInfo)
    {
        $currentDate                  = date("Y-m-d", strtotime($fleetInfo->expected_date));
        $staffShouldAttendanceInfoCnt = 0;

        //今天是不是ph
        $isPhDay = ThailandHolidayModel::find([
            'conditions' => 'day = :day:',
            'columns'    => 'day,holiday_type',
            'bind'       => [
                "day" => $currentDate,
            ],
        ])->toArray();
        if (!empty($isPhDay)) {
            return $staffShouldAttendanceInfoCnt;
        }

        //应到人数，今天不是rest day、并且没有请假
        $staffInfo = HrStaffInfoModel::find([
            "sys_store_id = :store_id: and job_title = :job_title: and state = 1 and formal = 1",
            "bind" => [
                "store_id"  => $fleetInfo->start_store,
                "job_title" => enums::$job_title['van_courier'],
            ],
            "columns" => "staff_info_id",
        ])->toArray();
        $staffInfoArr = array_column($staffInfo, 'staff_info_id');
        if (empty($staffInfoArr)) {
            return $staffShouldAttendanceInfoCnt;
        }

        //排掉今天休息的
        $restStaffIds = HrStaffWorkDayModel::find([
            "staff_info_id in({staff_ids:array}) and date_at = :date:",
            "bind" => [
                "staff_ids" => $staffInfoArr,
                "date"      => $currentDate,
            ],
            "columns" => "staff_info_id",
        ])->toArray();
        $restStaffIdsArr = array_column($restStaffIds, 'staff_info_id');
        $staffInfoArr = array_diff($staffInfoArr, $restStaffIdsArr);
        if (empty($staffInfoArr)) {
            return $staffShouldAttendanceInfoCnt;
        }

        //排掉今天请假的,只要请假就排掉,不管是不是半天、整天的情况
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.staff_info_id');
        $builder->from(['s' => StaffAuditLeaveSplitModel::class]);
        $builder->join(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
        $builder->inWhere("s.staff_info_id", $staffInfoArr);
        $builder->andWhere('s.date_at = :date_at:',['date_at' => $currentDate]);
        $builder->andWhere('a.status = :status:',['status' => enums::APPROVAL_STATUS_APPROVAL]);
        $leaveStaffIds = $builder->getQuery()->execute()->toArray();
        $leaveStaffIdsArr = array_column($leaveStaffIds, 'staff_info_id');

        $staffInfoArr = array_diff($staffInfoArr, $leaveStaffIdsArr);
        if (empty($staffInfoArr)) {
            return $staffShouldAttendanceInfoCnt;
        }
        return count($staffInfoArr);
    }

    /**
     * 旧的统计 作废
     * @description
     * @return array
     */
    public function getFleetStatistics($paramIn = [])
    {
        //[1]获取参数
        $arriveTime = $this->processingDefault($paramIn, 'arrive_time', 1);
        $startStore = $this->processingDefault($paramIn, 'start_store', 1);
        $endStore   = $this->processingDefault($paramIn, 'end_store', 1);
        $auditType  = $this->processingDefault($paramIn, 'audit_type', 2);
        $storeIds   = $this->processingDefault($paramIn, 'store_ids', 3);
        $status     = $this->processingDefault($paramIn, 'status', 2);

        if ($auditType == enums::$fleet_audit_type['Normal']) { //普通加班车申请
            $startStoreId  = array_shift($storeIds);
            //排掉结束节点
            array_pop($storeIds);
        } else {
            $startStoreId  = $startStore;
        }

        $params = sprintf("storeId=%s&arriveDate=%s&viaStoreIds=%s&&status=%s",
            $startStoreId,
            date("Y-m-d", strtotime($arriveTime)),
            !empty($storeIds) ? implode("-", $storeIds): "",
            !empty($status) ? $status: enums::$audit_status['panding_approval']
        );
        $requestUrl = $this->config->api->java_http_url . "/svc/fleet/audit/tandem/statistics?". $params;

        $result = $this->httpPost($requestUrl, [], null, 10, false);

        return $this->checkReturn(['data' => $result['data'] ?? []]);
    }

    /**
     * 21177【ALL】预计入仓包裹优化
     * 加班车详情
     * 当日包裹详情
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function getFleetStatisticsPackage($paramIn = [])
    {
        //[1]获取参数
        $startStore = $this->processingDefault($paramIn, 'start_store', 1);
        $endStore   = $this->processingDefault($paramIn, 'end_store', 1);
        $serialNo   = $this->processingDefault($paramIn, 'serial_no', 1);

        $params['start_store'] = $startStore;
        $params['end_store'] = $endStore;
        $params['serial_no'] = $serialNo;

        $requestUrl = $this->config->api->java_http_url . "/svc/fleet/audit/tandem/statistics/parcel";

        $result = $this->httpPost($requestUrl, $params, null, 10, true);

        return $this->checkReturn(['data' => $result['data'] ?? []]);
    }

    /**
     * 21177【ALL】预计入仓包裹优化
     * 加班车详情
     * 当日班车详情
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function getFleetStatisticsShuttleBus($paramIn = [])
    {
        //[1]获取参数
        $startStore = $this->processingDefault($paramIn, 'start_store', 1);
        $endStore   = $this->processingDefault($paramIn, 'end_store', 1);
        $serialNo   = $this->processingDefault($paramIn, 'serial_no', 1);

        $params['start_store'] = $startStore;
        $params['end_store'] = $endStore;
        $params['serial_no'] = $serialNo;

        $requestUrl = $this->config->api->java_http_url . "/svc/fleet/audit/tandem/statistics/line_task";

        $result = $this->httpPost($requestUrl, $params, null, 10, true);

        return $this->checkReturn(['data' => $result['data'] ?? []]);
    }

    /**
     * 获取指定职位的在职工号
     * @return array
     */
    public function getStaffCCD()
    {
        if (isCountry('PH')) {
            $jobTitleId = [self::JOB_TITLE_ID_TRANSPORTATION_CCD_OFFICER];
        } else if (isCountry(['TH','LA'])) {
            $jobTitleId = [
                self::JOB_TITLE_ID_CENTRAL_CONTROL_DISPATCHING_MANAGER,
                self::JOB_TITLE_ID_CENTRAL_CONTROL_DISPATCHING_SUPERVISOR,
                self::JOB_TITLE_ID_CENTRAL_CONTROL_DISPATCHING_DEPUTY_MANAGER,
            ];
        } else if (isCountry('MY')) {
            $jobTitleId = [
                self::JOB_TITLE_ID_CENTRAL_CONTROL_DISPATCHING_MANAGER,
                self::JOB_TITLE_ID_CENTRAL_CONTROL_DISPATCHING_SUPERVISOR,
                self::JOB_TITLE_ID_CENTRAL_CONTROL_DISPATCHING_DEPUTY_MANAGER,
                self::JOB_TITLE_ID_CENTRAL_CONTROL_DISPATCHING_SPECIALIST,
            ];
        } else {
            return [];
        }
        $conditions = "formal = :formal: and state = :state: and is_sub_staff = 0 and job_title in({job_title:array})";
        $bind       = ['state'     => HrStaffInfoModel::STATE_ON_JOB,
                       'formal'    => HrStaffInfoModel::FORMAL_1,
                       'job_title' => $jobTitleId,
        ];
        return HrStaffInfoModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => ['staff_info_id', 'name'],
        ])->toArray();
    }

    /**
     * 加班车审批状态枚举
     * @return array[]
     */
    public function getAuditStateEnums(): array
    {
        $t = $this->getTranslation();
        return [
            ['label' => $t->_('audit_status.1'), 'value' => enums::APPROVAL_STATUS_PENDING],
            ['label' => $t->_('audit_status.2'), 'value' => enums::APPROVAL_STATUS_APPROVAL],
            ['label' => $t->_('audit_status.3'), 'value' => enums::APPROVAL_STATUS_REJECTED],
            ['label' => $t->_('audit_status.4'), 'value' => enums::APPROVAL_STATUS_CANCEL],
            ['label' => $t->_('4004'), 'value' => enums::APPROVAL_STATUS_ABANDON],
        ];
    }

    /**
     * 加班车详情
     * @param $fleet_audit_id
     * @return array
     * @throws ValidationException
     */
    public function getFleetDetailForSvc($fleet_audit_id): array
    {
        if (empty($fleet_audit_id)) {
            throw new ValidationException('参数错误');
        }
        $fleetInfo = $this->fleet->getFleetInfo($fleet_audit_id, true);
        $staffInfo = (new StaffServer)->getStaffInfoById($fleetInfo['submitter_id']);

        $storeNames       = (new SysStoreServer())->getStoreName([$fleetInfo['start_store'], $fleetInfo['end_store']]);
        $start_store_name = $storeNames[$fleetInfo['start_store']] ?? '';
        $end_store_name   = $storeNames[$fleetInfo['end_store']] ?? '';


        //获取全部申请网点的缩写
        $storeInfos             = SysStoreModel::find([
            'conditions' => "id IN ({store_ids:array})",
            'bind'       => [
                'store_ids' => [$fleetInfo['start_store'], $fleetInfo['end_store']],
            ],
            'columns'    => 'id,short_name',
        ])->toArray();
        $shortNames             = array_column($storeInfos, 'short_name', 'id');
        $start_store_short_name = $shortNames[$fleetInfo['start_store']] ?? '';
        $end_store_short_name   = $shortNames[$fleetInfo['end_store']] ?? '';

        if ($fleetInfo['audit_type'] == enums::$fleet_audit_type['FD_courier']) {
            $temporary_route = $start_store_short_name . '-' . $end_store_short_name;
        } else {
            $storeNameArr    = [$start_store_short_name, $fleetInfo['via_store_short_name'] ?? '', $end_store_short_name];
            $storeNameArr    = array_filter($storeNameArr);
            $temporary_route = implode( '-', $storeNameArr);
        }
        $carType = $this->getCarTypeList($fleetInfo['car_type']);
        //加班车申请类型
        $auditType                      = array_column($this->getFleetAuditType(), 'type_txt', 'type');
        $approval_duration = round((strtotime($fleetInfo['final_approval_time'] ? : date('Y-m-d H:i:s')) - strtotime($fleetInfo['created_at']))/3600,1) . 'h';
        $reason_type_list = array_column($this->getReasonType(),'type_txt','type');

        if ($fleetInfo['reason_type'] == 99) {
            $show_reason = $reason_type_list[$fleetInfo['reason_type']] . '-' . $fleetInfo['reason'] ?? '';
        } else {
            $show_reason = $reason_type_list[$fleetInfo['reason_type']];
        }
        return [
            'fleet_audit_id'      => $fleet_audit_id,
            'serial_no'           => $fleetInfo['serial_no'],
            'apply_staff_info_id' => $staffInfo['staff_info_id'],
            'via_store_ids'       => implode(',',explode('-', $fleetInfo['via_store_ids'])),
            'start_store'         => $fleetInfo['start_store'],
            'end_store'           => $fleetInfo['end_store'],
            'status'              => $fleetInfo['status'],
            'status_text'         => $this->getTranslation()->_('audit_status.' . $fleetInfo['status']),
            'apply_staff_name'    => $staffInfo['staff_name'],
            'apply_store_name'    => $staffInfo['store_name'],
            'expected_time'       => $fleetInfo['expected_time'],
            'reason'              => $show_reason,
            'created_at'          => $fleetInfo['created_at'],
            'region'              => $fleetInfo['region'],
            'capacity'            => $fleetInfo['capacity'] . $this->getTranslation()->_('piece'),
            'image_path'          => $fleetInfo['image_path'],
            'end_store_name'      => $end_store_name,
            'start_store_name'    => $start_store_name,
            'temporary_route'     => $temporary_route,
            'car_type_text'       => $carType['type_txt'],
            'audit_type_text'     => $auditType[$fleetInfo['audit_type']] ?? '',
            'approval_duration'   => $approval_duration,
        ];
    }


    /**
     * 获取加班车列表For Java
     * @param array $params
     * @return array
     * @throws ValidationException
     */
    public function getFleetListForSvc(array $params = []): array
    {
        if (empty($params['page_size']) || empty($params['page_num'])) {
            throw new ValidationException('分页参数错误');
        }
        //获取参数
        $pageSize = $this->processingDefault($params, 'page_size', 2, 10);
        $pageNum  = $this->processingDefault($params, 'page_num', 2, 1);

        $result = [
            "item"       => [],
            "pagination" => [
                "current_page" => $pageNum,
                "per_page"     => $pageSize,
                "total_count"  => 0,
            ],
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['fa' => FleetAuditModel::class]);
        $builder->innerJoin(HrStaffInfoModel::class, 'fa.submitter_id = i.staff_info_id', 'i');

        //区域
        if (!empty($params['region'])) {
            $builder->andWhere('fa.region = :region:', ['region' => $params['region']]);
        }

        //开始网点类型
        if (!empty($params['store_category'])) {
            $builder->innerJoin(SysStoreModel::class, 'fa.start_store = ss.id', 'ss');
            $builder->andWhere('ss.category = :category:', ['category' => $params['store_category']]);
        }
        //开始网点
        if (!empty($params['start_store_id'])) {
            $builder->andWhere('fa.start_store = :start_store_id:', ['start_store_id' => $params['start_store_id']]);
        }

        //管辖网点
        if (!empty($params['manage_store_ids']) && is_array($params['manage_store_ids'])) {
            $builder->inWhere('fa.start_store', $params['manage_store_ids']);
        }

        //目的网点
        if (!empty($params['end_store_id'])) {
            $builder->andWhere('fa.end_store = :end_store:', ['end_store' => $params['end_store_id']]);
        }

        //审批状态
        if (!empty($params['status']) && is_array($params['status'])) {
            $builder->inWhere('fa.status', $params['status']);
        }

        //审批序列号
        if (!empty($params['serial_no'])) {
            $builder->andWhere('fa.serial_no = :serial_no:', ['serial_no' => $params['serial_no']]);
        }

        //加班车类型
        if (!empty($params['audit_type'])) {
            $builder->andWhere('fa.audit_type = :audit_type:', ['audit_type' => $params['audit_type']]);
        }

        //期望到达时间-开始
        if (!empty($params['expected_start_time'])) {
            $builder->andWhere('fa.expected_date >= :expected_start_time:',
                ['expected_start_time' => date("Y-m-d H:i:s", strtotime($params['expected_start_time']))]);
        }

        //期望到达时间-结束
        if (!empty($params['expected_end_time'])) {
            $builder->andWhere('fa.expected_date <= :expected_end_time:',
                ['expected_end_time' => date("Y-m-d H:i:s", strtotime($params['expected_end_time']))]);
        }

        //期望到达时间-开始
        if (!empty($params['apply_start_date'])) {
            $builder->andWhere('fa.created_at >= :apply_start_date:',
                ['apply_start_date' => zero_time_zone($params['apply_start_date'])]);
        }

        //期望到达时间-结束
        if (!empty($params['apply_end_date'])) {
            $builder->andWhere('fa.created_at < :apply_end_date:', [
                'apply_end_date' => zero_time_zone(date('Y-m-d H:i:s', strtotime($params['apply_end_date']) + 86400)),
            ]);
        }

        $totalCount = $builder->columns('COUNT(1) AS total')->getQuery()->execute()->getFirst();
        $totalCnt   = intval($totalCount->total);
        if (empty($totalCnt)) {
            return $result;
        }

        $result['pagination']['total_count'] = $totalCnt;
        $column                              = [
            'fa.id as fleet_audit_id',
            'fa.serial_no',
            'fa.audit_type',
            'fa.car_type',
            'fa.status',
            'fa.capacity',
            'fa.expected_date',
            'fa.start_store',
            'fa.end_store',
            'fa.via_store_ids',
            'fa.region',
            'fa.reject_reason',
            'fa.reason',
            'fa.reason_type',
            'fa.created_at',
            'fa.final_approval_time',
            'fa.via_store_short_name',
            'i.staff_info_id as apply_staff_info_id',
            'i.name as apply_staff_name',
        ];
        $builder->columns($column);
        $builder->orderBy('fa.id DESC');
        $builder->limit($pageSize, ($pageNum - 1) * $pageSize);
        $list      = $builder->getQuery()->execute()->toArray();
        $fleet_ids = [];

        foreach ($list as $item) {
            if ($item['status'] == enums::$audit_status['panding']) {
                $fleet_ids[] = $item['fleet_audit_id'];
            }
        }

        if (!empty($fleet_ids)) {
            $approvalList = AuditApprovalModel::find([
                'columns'    => 'approval_id,biz_value',
                'conditions' => 'biz_type = :biz_type: and biz_value in ({biz_value:array}) and state = :state:',
                'bind'       => [
                    'biz_type'  => AuditListEnums::APPROVAL_TYPE_FLEET,
                    'biz_value' => $fleet_ids,
                    'state'     => enums::$audit_status['panding'],
                ],
            ])->toArray();
            if (!empty($approvalList)) {
                $allApprovalIds   = array_column($approvalList, 'approval_id');
                $staffList        = (new StaffServer())->getStaffDepartmentInfoList($allApprovalIds);
                $current_approver = [];
                foreach ($approvalList as $item) {
                    if (!empty($staffList[$item['approval_id']])) {
                        $current_approver[$item['biz_value']][] = $staffList[$item['approval_id']];
                    }
                }
            }
        }

        $t           = $this->getTranslation();
        $result_list = [];
        //枚举信息
        $carTypes  = array_column($this->getCarTypeList(), 'type_txt', 'type');
        $auditType = array_column($this->getFleetAuditType(), 'type_txt', 'type');
        $allStore  = (new SysStoreServer())->getAllStore();
        //申请原因类型
        $reason_type_list = array_column($this->getReasonType(),'type_txt','type');
        foreach ($list as $item) {
            if ($item['reason_type'] == 99) {
                $show_reason = $reason_type_list[$item['reason_type']] . '-' . $item['reason'] ?? '';
            } else {
                $show_reason = $reason_type_list[$item['reason_type']];
            }

            $tmp['status']              = intval($item['status']);
            $tmp['via_store_ids']       = $item['via_store_ids']?explode('-', $item['via_store_ids']):[];
            $tmp['end_store']           = $item['end_store'];
            $tmp['start_store']         = $item['start_store'];
            $tmp['fleet_audit_id']      = $item['fleet_audit_id'];
            $tmp['apply_staff_info_id'] = $item['apply_staff_info_id'];
            $tmp['apply_staff_name']    = $item['apply_staff_name'];
            $tmp['region']              = $item['region'];
            $tmp['reject_reason']       = $item['reject_reason'] ?: '';
            $tmp['reason']              = $show_reason;
            $tmp['serial_no']           = $item['serial_no'];
            $tmp['car_type_text']       = $carTypes[$item['car_type']] ?? '';
            $tmp['audit_type_text']     = $auditType[$item['audit_type']] ?? '';
            $tmp['status_text']         = $t->_('audit_status.' . $item['status']);
            $tmp['capacity']            = $item['capacity'] . $t->_('piece');
            $tmp['expected_time']       = date('Y-m-d H:i:s', strtotime($item['expected_date']));
            $tmp['start_store_name']    = $allStore[$item['start_store']]['name'];
            $tmp['end_store_name']      = $allStore[$item['end_store']]['name'];
            $tmp['created_at']          = show_time_zone($item['created_at']);
            $approval_duration          = round((strtotime($item['final_approval_time'] ? show_time_zone($item['final_approval_time']) : date('Y-m-d H:i:s')) - strtotime(show_time_zone($item['created_at']))) / 3600,
                    1) . 'h';
            $tmp['approval_duration']   = $approval_duration;
            if ($item['audit_type'] == enums::$fleet_audit_type['FD_courier']) {
                $temporary_route = implode('-', [
                    $allStore[$item['start_store']]['short_name'] ?: $tmp['start_store_name'],
                    $allStore[$item['end_store']]['short_name'] ?: $tmp['end_store_name'],
                ]);
            } else {
                $storeNameArr    = [
                    $allStore[$item['start_store']]['short_name'],
                    $item['via_store_short_name'] ?? '',
                    $allStore[$item['end_store']]['short_name'],
                ];
                $storeNameArr    = array_filter($storeNameArr);
                $temporary_route = implode('-', $storeNameArr);
            }

            $tmp['temporary_route']       = $temporary_route;
            $tmp['current_approver_list'] = $current_approver[$item['fleet_audit_id']] ?? [];
            $result_list[] = $tmp;
        }
        $result['item'] = $result_list;
        return $result;
    }

    /**
     * 获取 加班车数据
     * @param $params
     * @return array
     */
    public function getFleetPendingList($params)
    {
        if (empty($params['start_store']) || empty($params['end_store'])) {
            return [];
        }
        //获取参数
        $start_store = $this->processingDefault($params, 'start_store', 1);
        $end_store   = $this->processingDefault($params, 'end_store', 1);

        $currentTime = date('Y-m-d H:i:s');

        $where['start_store']         = $start_store;
        $where['end_store']           = $end_store;
        $where['status']              = enums::APPROVAL_STATUS_PENDING;
        $where['start_expected_date'] = date('Y-m-d H:i:s', strtotime("{$currentTime} -12 hours"));
        $where['end_expected_date']   = date('Y-m-d H:i:s', strtotime("{$currentTime} +24 hours"));

        $data = FleetRepository::getList($where, ['id', 'serial_no', 'car_type', 'status', 'expected_date']);
        foreach ($data as $key => $value) {
            $data[$key]['expected_date'] = date('Y-m-d H:i:s', strtotime($value['expected_date']));
        }

        return $data;
    }




}
