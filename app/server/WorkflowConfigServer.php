<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\oa\WorkflowBasicConfigModel;

class WorkflowConfigServer extends BaseServer
{
    private static $instance;

    /**
     * 获取实例
     * @param $lang
     * @param $timezone
     * @return self
     */
    public static function getInstance($lang , $timezone): WorkflowConfigServer
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self($lang , $timezone);
        }
        return self::$instance;
    }

    /**
     * 获取审批流配置
     * @param $version
     * @param $workflow_relate_type_id
     * @return array
     */
    public function getAuditConfigByVersion($version, $workflow_relate_type_id): array
    {
        //查询审批流配置
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("configure_type,configure_value");
        $builder->from(['o' => WorkflowBasicConfigModel::class]);
        $builder->andwhere("workflow_relate_type_id = :workflow_relate_type_id: and version = :version:",
            ['workflow_relate_type_id' => $workflow_relate_type_id, 'version' => $version]);
        $builder->andwhere("deleted = 0");
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取审批流配置-指定设置项
     * @param $version
     * @param $workflow_relate_type_id
     * @param int $configure_type
     * @return mixed
     */
    public function getSpecConfigType($version, $workflow_relate_type_id, int $configure_type = enums::WF_OA_APPROVER_VALUE)
    {
        //查询审批流是否配置了 自动审批通过
        return WorkflowBasicConfigModel::findFirst([
            'conditions' => "workflow_relate_type_id = :workflow_relate_type_id: and configure_type = :configure_type: 
                and deleted = 0 and version = :version:",
            'bind'       => [
                'workflow_relate_type_id' => $workflow_relate_type_id,
                'configure_type'          => $configure_type,
                'version'                 => $version,
            ],
        ]);
    }
}