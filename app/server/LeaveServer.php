<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/1/5
 * Time: 2:36 PM
 */


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\StaffDaysFreezeModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReadModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;


use FlashExpress\bi\App\Modules\Th\Server\Vacation\SickServer;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Models\backyard\StaffLeaveInLieuModel;

use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use App\Country\Tools;
use FlashExpress\bi\App\Models\backyard\StaffLastYearDaysModel;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffPublicHolidayRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\AttendanceRepository;

use Exception;
use FlashExpress\bi\App\Server\Vacation\InternationalServer;
use FlashExpress\bi\App\Server\Vacation\MaternityServer;

class LeaveServer extends BaseServer{
    public $leaveObject;
    public $timezone;
    public $initType = [enums::LEAVE_TYPE_19,enums::LEAVE_TYPE_4];

    public function __construct($lang = 'zh-CN', $timezone = ''){
        parent::__construct($lang);
        if(empty($timezone))
            $this->timezone = $this->config->application->timeZone;

    }

    public function format_leave_insert($staffId, $param)
    {
        $leaveStartTime = $param['start_time'];
        $leaveEndTime   = $param['end_time'];
        $leaveType      = $param['leave_type'];
        $holidays       = $param['holidays'];
        $sub_day_off    = $param['sub_day'];
        $leaveStartType = $param['start_type'];
        $leaveEndType   = $param['end_type'];
        $creat_year     = date("Y");

        $insert = [];

        //计算对应年的天数
        $this_num = $next_num = 0;
        if ((strtotime($leaveEndTime) - strtotime($leaveStartTime)) / (24 * 3600) >= 1) {//跨天
            $key        = $start_date = date('Y-m-d', strtotime($leaveStartTime));
            $end_date   = date('Y-m-d', strtotime($leaveEndTime));
            $date_array = [];
            while ($key <= $end_date) {
                $date_array[] = $key;
                $key          = date("Y-m-d", strtotime("+1 day", strtotime($key)));
            }

            foreach ($date_array as $k => $date) {
                //whether in ones holidays or not
                if (in_array($date, $holidays) && in_array($leaveType, $sub_day_off)) {
                    continue;
                }
                $apply_year = date('Y', strtotime($date));
                if ($creat_year >= $apply_year) {
                    $this_num++;
                } else {
                    $next_num++;
                }


                //请假多天 拆开单天
                $insert[$k]['staff_info_id'] = $staffId;
                $insert[$k]['date_at']       = $date;
                $insert[$k]['type']          = 0;
                $insert[$k]['year_at']       = ($creat_year >= $apply_year) ? $creat_year : $apply_year;
                //第一天
                if ($date == $start_date) {
                    $insert[$k]['type'] = ($leaveStartType == 1) ? 0 : $leaveStartType;
                    //非整天 减去半天
                    if (!empty($insert[$k]['type'])) {
                        if ($creat_year >= $apply_year) {
                            $this_num -= 0.5;
                        } else {
                            $next_num -= 0.5;
                        }
                    }
                }
                //最后一天
                if ($date == $end_date) {
                    $insert[$k]['type'] = ($leaveEndType == 2) ? 0 : $leaveEndType;
                    if (!empty($insert[$k]['type'])) {
                        if ($creat_year >= $apply_year) {
                            $this_num -= 0.5;
                        } else {
                            $next_num -= 0.5;
                        }
                    }
                }

                if (in_array($leaveType, [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_4, enums::LEAVE_TYPE_19])) {
                    $insert[$k]['year_at'] = $creat_year;
                }
            }
        } else {//不跨天
            if (in_array($leaveStartTime, $holidays) && in_array($leaveType, $sub_day_off)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('day off for the apply date'));
            } else {
                $apply_year = date('Y', strtotime($leaveStartTime));
                if ($creat_year >= $apply_year) {
                    $this_num = 1;
                } else {
                    $next_num = 1;
                }

                $insert[0]['staff_info_id'] = $staffId;
                $insert[0]['date_at']       = $leaveStartTime;
                $insert[0]['type']          = 0;
                if ($leaveStartType == $leaveEndType)//半天
                {
                    $insert[0]['type'] = $leaveStartType;

                    if ($creat_year >= $apply_year) {
                        $this_num = 0.5;
                    } else {
                        $next_num = 0.5;
                    }
                }
                $insert[0]['year_at'] = ($creat_year >= $apply_year) ? $creat_year : $apply_year;;
            }

            if (in_array($leaveType, [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_4, enums::LEAVE_TYPE_19])) {
                $insert[0]['year_at'] = $creat_year;
            }
        }
        //如果要天数
        if (!empty($param['need_num']) && $param['need_num'] == 1) {
            return [
                'code' => 1,
                'msg'  => '',
                'data' => [
                    'this_num' => $this_num,
                    'next_num' => $next_num,
                ],
            ];
        }

        return ['code' => 1, 'msg' => '', 'data' => $insert];
    }

    //根据额度 标记 年假所属年份
    public function year_flag($staffId,$insert,$month_arr,$leave_param){
        $year_type = $leave_param['year_type'];
        $left_days = $leave_param['left_days'];
        $leaveStartTime = $leave_param['leaveStartTime'];
        // 拼接 拆分表 归属年 year_at 字段
        $start_year = date('Y',strtotime($leaveStartTime));
        $add_row = array();//出现不够减情况 额外新增一条  merge到 insert
        foreach ($insert as $k => $in) {
            $insert[$k]['year_at'] = date('Y', time());
            //优先逻辑  如果日期 在 当年 3月 31号 失效之前 才计算剩余假期 打标记 否则 一律按当年算
            if (!in_array(date('m', strtotime($in['date_at'])), $month_arr)) {
                $insert[$k]['year_at'] = date('Y',strtotime($in['date_at']));
                continue;
            } else {
                if ($year_type == 1)//下面有可能更改为1
                    $insert[$k]['year_at'] = $start_year;
                if ($year_type == -1)
                    $insert[$k]['year_at'] = date('Y', strtotime("{$leaveStartTime} -1 year"));

                if ($year_type == 2) {//需拆分
                    $duration = empty($in['type']) ? 1 : 0.5;
                    if ($left_days > 0 && ($left_days - $duration >= 0)) {//还够减
                        $insert[$k]['year_at'] = date('Y', strtotime("{$leaveStartTime} -1 year"));
                        $left_days             = $left_days - $duration;
                    } else if ($left_days > 0 && ($left_days - $duration < 0)) {//不够减了（剩余0。5 本次记录 需要1天 只能是这种情况 把本条记录更改为半天 额外新增一条半天记录）
                        $insert[$k]['type']    = 1;
                        $insert[$k]['year_at'] = date('Y', strtotime("{$leaveStartTime} -1 year"));

                        //拼接剩下半天 标记归属年 为今年 merge
                        $add_row[0]['staff_info_id'] = $staffId;
                        $add_row[0]['date_at']       = $in['date_at'];
                        $add_row[0]['type']          = 2;
                        $add_row[0]['year_at']       = $start_year;

                        $left_days = 0;//减没了
                        $year_type = 1;//剩下的 都是 当年的了
                    } else if ($left_days == 0) {//上次循环 减没了
                        $insert[$k]['year_at'] = $start_year;
                        $year_type             = 1;
                    }

                }
            }

        }

        if (!empty($add_row))
            $insert = array_merge($insert, $add_row);
        return $insert;
    }

    //新需求 计算 根据职等 对应的假期额度 have day
    public function get_have_day($staff_id,$should_days){
        if(empty($staff_id) || empty($should_days))
            return 0;

        $count_day = StaffDaysFreezeModel::findFirst($staff_id);
        if(empty($count_day))
            return 0;


        $current_tmp = strtotime(date('Y-m-d',strtotime("-1 day")));//昨天00 也就是 任务跑过的日期
        $start_tmp = strtotime(date('Y-01-01'));//周期第一天 00点
        $year = date('Y');
        $all_days = 365;
        if(year_type($year))
            $all_days = 366;

        //获取剩余天数
        $left_days = $all_days - (($current_tmp - $start_tmp) / (24 * 3600));
        $left_days = $should_days * ($left_days / $all_days);//每年剩余天数 对应职等天数的份数 例：剩余200天 职等7天  7 * (200/365)
        $have_days = $count_day->days + $left_days;
        $have_days = round($have_days,enums::ROUND_NUM_READ);
        $int_day   = floor($have_days);//2
        $have_days = $have_days > ($int_day + 0.5) ? ($int_day + 0.5) : $int_day;//2.5
        return $have_days;
    }


    /**
     * 根据请假创建年 获取当年是否申请过 目前只适用 产假
     * @param $staff_id
     * @param $leave_type
     * @return bool
     */
    public function check_create_year($staff_id,$leave_type){
        $create_year = date('Y');
        $check       = StaffAuditModel::find("staff_info_id = {$staff_id} and audit_type = 2 and leave_type = {$leave_type} and status in (1,2)")->toArray();
        $flag        = true;
        if (!empty($check)) {
            $add_hour = $this->config->application->add_hour;
            foreach ($check as $v) {
                //如果 当年存在记录
                if (date('Y', strtotime($v['created_at']) + $add_hour * 3600) == $create_year) {
                    $flag = false;
                    break;
                }
            }
        }
        return $flag;
    }


    public function read_explain($param){
        $read_info = StaffLeaveReadModel::findFirst("staff_info_id = {$param['staff_id']} and leave_type = {$param['leave_type']}");
        if(!empty($read_info)){
            $read_info->is_read = 1;
            return $read_info->update();
        }

        $insert['staff_info_id'] = $param['staff_id'];
        $insert['leave_type'] = $param['leave_type'];
        $insert['is_read'] = 1;
        $model = new StaffLeaveReadModel();
        return $model->create($insert);
    }




    //针对 已经固化的请假额度 非审批通过 需返还对应的天数
    public function re_back_leave_days($audit_info){
        //新需求 针对 补休假撤销驳回超时 返还 对应额度的补休加
        $limit_num = intval($audit_info['leave_day'] * 2);

        $data = StaffLeaveInLieuModel::find([
            'conditions' => "state = 2 and staff_info_id = {$audit_info['staff_info_id']} ",
            'order' => 'invalid_date desc',
            'limit' => $limit_num
        ]);

        if(!empty($data)){
            foreach ($data as $da){
                $da->state = 1;
                $da->update();
            }
        }

        return true;
    }

    /**
     * 泰国 根据员工 工作日 不同 返回不同 ph 返回 map list  country文件夹的不需要了 别再调用了！
     * @param $staff_info
     * @return array|\int[][]|\string[][]
     */
    public function ph_days($staff_info)
    {
        $bind['type']       = $staff_info['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_5 ? [
            ThailandHolidayModel::TYPE_DEFAULT,
            ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_5,
        ] : [ThailandHolidayModel::TYPE_DEFAULT, ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_6];
        $data               = ThailandHolidayModel::find([
            'conditions' => 'type in ({type:array})',
            'columns'    => 'day,holiday_type',
            'bind'       => $bind,
        ])->toArray();


        //紧急需求 泰国 临时针对某些人增加一天ph泰国，
        //6天班员工，所属网点为“head office”的员工，
        //除Claim、Customer Service Online、Customer Service Representative 部门或者是 Fleet Driver 、Fleet Leader Driver 职位的员工外，
        //其他员工增加2021年12月06日为法定假日.
        if(!empty($staff_info['node_dep_name']) && !empty($staff_info['job_name']) && $staff_info['week_working_day'] == 6){
            if($staff_info['sys_store_id'] == '-1'
                && !in_array($staff_info['job_name'],array('Fleet Driver','Fleet Leader Driver'))
                && !in_array($staff_info['node_dep_name'],array('Claim','Customer Service Online','Customer Service Representative'))){
                $data = array_merge($data,array(array('day' => enums::SB_PH,'holiday_type' => 1)));
            }
        }
        //查询个人维度的公共假期
        $staffPublicHoliday = (new StaffPublicHolidayRepository($this->lang,$this->timezone))->getStaffData($staff_info['staff_info_id']);
        if($staffPublicHoliday) {
            $data = array_merge($data,$staffPublicHoliday);
        }
        return $data;
    }

    /**
     * 泰国6天班固定休息日的员工，如果命中白名单增加2022的ph
     * @param $staff_info
     * @return array
     */
    public function getPhDays2022($staff_info){
        $id = $staff_info['staff_info_id'];
        $weekWorkingDay = $staff_info['week_working_day'];
        $nodeDepartmentId = $staff_info['node_department_id'];
        $jobId = $staff_info['job_id'];
        if($weekWorkingDay != 6){
            return [];
        }

        $settingEnv = (new SettingEnvServer())->listByCode(['staff_ph','department_jobtitle_ph','add_ph_days_2022']);
        $settingEnv = array_column($settingEnv,'set_val','code');
        $phDays2022 = !empty($settingEnv['add_ph_days_2022']) ? explode(',', $settingEnv['add_ph_days_2022']) : [];
        $staffIds = !empty($settingEnv['staff_ph']) ? explode(',', $settingEnv['staff_ph']) : [];
        $departmentConfig = !empty($settingEnv['department_jobtitle_ph']) ? explode(',', $settingEnv['department_jobtitle_ph']) : [];

        if(in_array($id, $staffIds)) {
            $dates = $this->formatPhDays2022($phDays2022);
            $this->getDI()->get('logger')->write_log("员工{$id}命中工号白名单增加PH:". json_encode($dates),'info');
            return $dates;
        }

        $departmentConfig = $departmentConfig ? array_flip($departmentConfig) : [];
        if(! isset($departmentConfig[$nodeDepartmentId . '-' . $jobId])){
            $this->getDI()->get('logger')->write_log("员工{$id}未命中部门职位白名单",'info');
            return [];
        }
        $dates = $this->formatPhDays2022($phDays2022);
        $this->getDI()->get('logger')->write_log("员工{$id}命中部门职位白名单增加PH". json_encode($dates),'info');
        return $dates;
    }

    /**
     * 新增2022年的ph
     * @param $phDays2022
     * @return array
     */
    public function formatPhDays2022($phDays2022){
        if(empty($phDays2022)){
            $this->getDI()->get('logger')->write_log("命中白名单日期配置为空",'error');
            return [];
        }
        $dates = [];
        foreach($phDays2022 as $v){
            if(empty($v)){
                continue;
            }
            $dates[] = ['day' => $v,'holiday_type' => 1];
        }
        return $dates;
    }

    public function leave_off($staff_info)
    {
        return $staff_info['week_working_day'] == 6 ? true : false;
    }


    public function leave_25_off($staff_id)
    {
        $settingEnvServer = new SettingEnvServer();
        $staffIds = $settingEnvServer->getSetVal('leave_25_list');
        $staffIds = explode(',', $staffIds);
        return in_array($staff_id, $staffIds);
    }

    public function leave_26_off($staff_id)
    {
        $settingEnvServer = new SettingEnvServer();
        $staffIds = $settingEnvServer->getSetVal('leave_26_list');
        $staffIds = explode(',', $staffIds);
        return in_array($staff_id, $staffIds);
    }




    public function explain_read_flag($staff_info,$data)
    {
        //获取员工 是否配置了 对应请假类型的 不再提示配置 staff_leave_read
        $read_data = StaffLeaveReadModel::find("staff_info_id = {$staff_info['id']}")->toArray();
        if(!empty($read_data))
            $read_data = array_column($read_data,'is_read','leave_type');

        foreach ($data as &$da){
            $da['is_read'] = 0;
            if(!empty($read_data[$da['code']]))
                $da['is_read'] = intval($read_data[$da['code']]);

        }

        return $data;

    }
    /**
     *
     * "F0~F14：7/15
    F15~F16：9/15
    F17~F18：12/15
    F19（含）以上above： 12/17"
     * @param $staff_info
     * @return array|int|mixed
     */
    public function get_year_leave_days($staff_info)
    {
        $grade = $staff_info['job_title_grade_v2'];
        if(empty($grade) || $grade <= 14)
            return 7;
        if($grade > 14 && $grade <= 16)
            return 9;
        if($grade >= 17)
            return 12;

        return 7;
    }

    //年假额度 最大上限
    public function get_year_leave_max_days($staff_info)
    {

        $grade = $staff_info['job_title_grade_v2'];
        if(empty($grade) || $grade < 19)
            return 15;
        else
            return 17;
    }



    /**
     * 批量获取 员工 指定年的 已经请的假期额度
     * @param $staff_ids 员工工号 数组
     * @param string $year 指定年份
     * @param $leave_type 指定 假期类型
     */
    public function get_batch_leave_days($staff_ids,$year ,$leave_type){

        if (empty($staff_ids))
            return false;

        //查询 周期内 离职的员工
//        $builder = $this->modelsManager->createBuilder();
//        $builder->columns('s.staff_info_id,sum(if(s.`type`=0,1,0.5)) as num');
//        $builder->from(['s' => StaffAuditLeaveSplitModel::class]);
//        $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
//        $builder->inWhere("s.staff_info_id",$staff_ids);
//        $builder->andWhere('a.leave_type = :leave_type:',['leave_type' => $leave_type]);
//        $builder->andWhere('s.year_at = :year:',['year' => $year]);
//        $builder->inWhere('a.status',array(1,2));
//        $builder->groupBy("s.staff_info_id");
//        var_dump($builder->getQuery()->getSql()) ;exit;
//        return $builder->getQuery()->execute()->toArray();
        //！！！！ 上面的不好使 谁看见了 改一下

        $id_str = implode(',',$staff_ids);
        $sql = " select sum(if(s.`type`=0,1,0.5)) as num
                ,group_concat(s.id) as split_id
                ,a.leave_type
                ,group_concat(distinct a.audit_id) audit_ids
                ,a.staff_info_id
                from staff_audit_leave_split s
                join staff_audit a on a.audit_id = s.audit_id
                where a.staff_info_id in ({$id_str})
                and a.audit_type = 2 
                and a.status in (1,2) 
                and a.leave_type = {$leave_type}
                and s.year_at = {$year} 
                and a.parent_id = 0
                group by a.staff_info_id
                ";

        return $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

    }

    //泰国 统一模板 没有定制的
    public function format_detail_body($type, $comment)
    {
        if(empty($comment)){
            return [];
        }
        //先改产假 陪产假等改版后在放开
        if($type == enums::LEAVE_TYPE_4){
            $server = Tools::reBuildCountryInstance($this,[$this->lang,$this->timezone]);
            $this->leaveObject = $server->getLeaveObj(intval($type));

            $key = "leave_{$type}_key";
            $comment = json_decode($comment,true);
            $subType = $comment[$key];//对应国家选择的子类型

            $showString = $this->leaveObject->formatDetailInfo($subType);
            if(empty($showString)){
                return [];
            }
            //数据库保存示例 {'leave_4_key' => 3} 如果 为3类型 该产假 需减去5天
            $return[] = array('key' => $this->getTranslation()->_($key),'value' => $showString);
            return $return;
        }

        //家人去世假 显示与去世家人关系
        if ($type == enums::LEAVE_TYPE_7) {
            $key     = "leave_{$type}_key";
            $comment = json_decode($comment, true);
            $subType = $comment[$key] ?? null;//对应选择的子类型

            if (!empty($subType)) {
                $subTypeNames = StaffLeaveRemainDaysModel::getSubTypeNames();
                $relationKey  = $subTypeNames[$subType] ?? null;
                if (!empty($relationKey)) {
                    $return[] = [
                        'key'   => $this->getTranslation()->_($key),
                        'value' => $this->getTranslation()->_("22328_relation_{$relationKey}"),
                    ];
                    return $return;
                }
            }
        }

        return [];
    }

    //格式化 详情页显示的 上下午
    public function formatLeaveTime($leaveInfo){
        $t = $this->getTranslation();
        $startText = $leaveInfo['leave_start_time'] . ' ' .($leaveInfo['leave_start_type'] == 1 ? $t->_('morning') : $t->_('afternoon'));
        $endText = $leaveInfo['leave_end_time'] . ' ' .($leaveInfo['leave_end_type'] == 1 ? $t->_('morning') : $t->_('afternoon'));

        return [$startText,$endText];
    }


    /**
     * 初始化 入职员工 额度  hris 调用 rpc 或者 task 消费 返回
     * @param $param
     * @return false|StaffLeaveRemainDaysModel
     * @throws \ReflectionException
     */
    public function init_leave($param){
        if (empty(intval($param['staff_info_id'])) || empty($param['leave_type'])) {
            return false;
        }
        //查询对应假期 发放额度
        $audit_re   = new AuditRepository($this->lang);
        $limit_days = $audit_re->get_leave_days_by_type($param['leave_type']);
        if (empty($limit_days)) {
            $this->logger->write_log("init_leave_{$param['staff_info_id']} type error" . json_encode($param));
            return false;
        }

        //是否是 一次性额度
        $audit_server = new AuditServer($this->lang, $this->timezone);
        $audit_server = Tools::reBuildCountryInstance($audit_server, [$this->lang, $this->timezone]);
        $one_send     = $audit_server->one_send;

        //查询是否 有使用过 如果是一次性的 查询职业生涯有没有 如果不是 查询当前年
        $year = '1970';
        if (!in_array($param['leave_type'], $one_send)) {
            $year = date('Y');
        }

        $applied      = $audit_re->get_used_leave_days(intval($param['staff_info_id']), $year, $param['leave_type']);
        $applied_days = 0;
        if (!empty($applied)) {
            $applied_days = floatval($applied[0]['num']);
        }

        //计算剩余额度 入库
        $insert['staff_info_id'] = intval($param['staff_info_id']);
        $insert['leave_type']    = intval($param['leave_type']);
        $insert['year']          = empty($year) ? '1970' : $year;
        $insert['freeze_days']   = $limit_days;
        $insert['days']          = $limit_days - $applied_days;
        $insert['leave_days']    = $applied_days;
        $model                   = new StaffLeaveRemainDaysModel();
        $flag                    = $model->create($insert);
        $this->logger->write_log("init_leave_{$param['staff_info_id']} {$flag}" . json_encode($param), 'info');
        return $model;
    }

    //入职满整年 计算 额外加的年假天数 满第一个365 并且 年差 小于2 加1天 之后递增
    public function over_one_year_days($staff_info){

        //旧版 还原
        $entry = strtotime($staff_info['hire_date']);
        $add_days = 0;
        //如果 满365天入职 则第366天那一年 +1天 而后每自然年加一天
        if (time() > ($entry + 365 * 24 * 3600)) {
            $first_year = date('Y', $entry + 365 * 24 * 3600);//转正当年 +1额度
            $add_days   = intval(date('Y', time())) - intval($first_year);//而后每自然年 +1天
            $add_days  = 1 + $add_days;

        }
        return $add_days;


        //下面是 新版逻辑 改版以后要用 上面的就删除了
        if(empty($staff_info['hire_date']))
            return 0;
        $work_days = (time() - strtotime($staff_info['hire_date'])) / (24 * 3600);
        //没满365 返回0
        if($work_days < 365)
            return 0;
        //满1年
        $add_days = 1;
        $year = intval(date('Y'));
        $hire_year = intval(date('Y',strtotime($staff_info['hire_date'])));
        if(($year - $hire_year) <= 2)
            return $add_days;
        return $add_days + ($year - $hire_year) - 2;//满一年以后的第二年都只给1天

    }


    //跨年区间 获取对应年的日期
    public function format_year_date($leaveType,$leaveStartTime,$leaveEndTime,$holidays){
        $audit_server = new AuditServer($this->lang,$this->timezone);
        $audit_server = Tools::reBuildCountryInstance($audit_server,[$this->lang, $this->timezone]);
        $key   = date('Y-m-d', strtotime($leaveStartTime));
        $end_date   = date('Y-m-d', strtotime($leaveEndTime));
        $date_array = array();
        while ($key <= $end_date) {
            if (in_array($key, $holidays) && in_array($leaveType, $audit_server->sub_day)) {
                $key = date("Y-m-d", strtotime("+1 day", strtotime($key)));
                continue;
            }
            $y                = date('Y', strtotime($key));
            $date_array[$y][] = $key;
            $key              = date("Y-m-d", strtotime("+1 day", strtotime($key)));
        }

        return array_values($date_array);
    }

    //获取超出 有效期的日期 和天数
    public function get_invalid_date($leave_info,$invalid_date = ''){
        $date_list = $this->format_year_date($leave_info['leave_type'],$leave_info['leave_start_time'],$leave_info['leave_end_time'],$leave_info['holidays']);
        if(empty($invalid_date))
            $invalid_date = date('Y') . '-' . enums::LEAVE_INVALID_DATE;

        $return['invalid_date'] = array();
        $return['num'] = 0;
        //只请一天的情况
        if($leave_info['leave_start_time'] == $leave_info['leave_end_time']){
            if($leave_info['leave_start_time'] > $invalid_date)
                $return['invalid_date'][] = $leave_info['leave_start_time'];
            //开始和结束类型 如果不一样 说明是 整天 相等 就是半天
            $return['num'] = ($leave_info['leave_start_type'] == $leave_info['leave_end_type']) ? 0.5 : 1;

        }else{//超过一天
            foreach ($date_list as $y){
                foreach ($y as $date){
                    if($date <= $invalid_date)
                        continue;

                    $return['num']++;
                    //如果正好是 区间两头 需要判断 是半天还是一天
                    if($date == $leave_info['leave_start_time'] && $leave_info['leave_start_type'] != 1)
                        $return['num'] -= 0.5;
                    if($date == $leave_info['leave_end_time'] && $leave_info['leave_end_type'] != 2)
                        $return['num'] -= 0.5;

                    $return['invalid_date'][] = $date;

                }
            }
        }

        return $return;
    }


    /**
     * 操作更新 扣除 和 返还 对应额度
     * @param $staff_id
     * @param $up_type  1 申请 扣除操作  2 驳回超时等 返还额度操作
     * @param $param
     * @return bool
     * @throws \Exception
     */
    public function update_year_days($staff_id,$up_type,$param){

        $freeze_info = StaffDaysFreezeModel::findFirst($staff_id);
        if($up_type == enums::YEAR_ADD){
            $this_year = $param['this_year'] ?? 0;
            $last_year = $param['last_year'] ?? 0;
            $freeze_info->used_days += $this_year;
            $freeze_info->update();
            if(!empty($last_year)){
                $last_info = StaffLastYearDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year_time = :year_time:',
                    'bind' => ['staff_id' => $staff_id,'year_time' => intval(date('Y',strtotime('-1 year')))]
                ]);

                $last_info->left_days -= $last_year;
                $last_info->update();
            }
        }else if($up_type == enums::YEAR_RE_BACK){
            //需要 audit id
            $split_info = StaffAuditLeaveSplitModel::find([
                'conditions' => "audit_id = :audit_id:",
                'bind' => ['audit_id' => intval($param['audit_id'])]
            ])->toArray();
            if(empty($split_info))
                throw new \Exception('update_year_days can not find split info ' . json_encode($param));
            $current = date('Y');
            $last = date('Y',strtotime('-1 year'));
            $this_year = $last_year = 0;
            foreach ($split_info as $split){
                if($split['year_at'] == $current){
                    $this_year = empty($split['type']) ? ($this_year + 1) : ($this_year + 0.5);
                }
                if($split['year_at'] == $last){
                    $last_year = empty($split['type']) ? ($last_year + 1) : ($last_year + 0.5);
                }
            }

            $freeze_info->used_days -= $this_year;
            $freeze_info->update();

            if(!empty($last_year)){
                $last_info = StaffLastYearDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year_time = :year_time:',
                    'bind' => ['staff_id' => $staff_id,'year_time' => intval(date('Y',strtotime('-1 year')))]
                ]);

                $last_info->left_days += $last_year;
                $last_info->update();
            }
        }
        $this->logger->write_log("年假额度操作 {$up_type} {$staff_id} ".json_encode($param),'info');
        return true;

    }

    //操作扣除和返还 其他假期类型的额度
    public function update_leave_days($staff_id,$up_type,$param){
        $leave_type = intval($param['leave_type']);
        $year = date('Y');

        $info = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
            'bind' => ['staff_id' => $staff_id,'year_at' => $year,'leave_type' => $leave_type]
        ]);
        if($up_type == enums::YEAR_ADD){//申请操作 需要扣除
            if(!empty($info) && $param['this_year'] > 0){
                $info->days -= $param['this_year'];
                $info->leave_days += $param['this_year'];
                $info->update();
            }

            //更新去年
            if(!empty($param['last_year'])){
                $last_info = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
                    'bind' => ['staff_id' => $staff_id,'year_at' => date('Y',strtotime('-1 year')),'leave_type' => $leave_type]
                ]);
                if(!empty($last_info)){
                    $last_info->days -= $param['last_year'];
                    $last_info->leave_days += $param['last_year'];
                    $last_info->update();
                }
            }

            //更新明年
            if(!empty($param['next_year'])){
                $next_info = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
                    'bind' => ['staff_id' => $staff_id,'year_at' => date('Y',strtotime('+1 year')),'leave_type' => $leave_type]
                ]);
                if(!empty($next_info)){
                    $next_info->days -= $param['next_year'];
                    $next_info->leave_days += $param['next_year'];
                    $next_info->update();
                }
            }
        }

        if($up_type == enums::YEAR_RE_BACK){//返还额度
            $audit_id = intval($param['audit_id']);
            $split_info = StaffAuditLeaveSplitModel::find([
                'conditions' => "audit_id = :audit_id:",
                'bind' => ['audit_id' => $audit_id]
            ])->toArray();

            if(empty($split_info))
                throw new \Exception('update_year_days can not find split info ' . json_encode($param));
            //只涵盖 去年到明年 3年区间
            $current = date('Y');
            $last = date('Y',strtotime('-1 year'));
            $next = date('Y',strtotime('+1 year'));
            $this_year = $last_year = $next_year = 0;
            foreach ($split_info as $split){
                if($split['year_at'] == $current){
                    $this_year = empty($split['type']) ? ($this_year + 1) : ($this_year + 0.5);
                }
                if($split['year_at'] == $last){
                    $last_year = empty($split['type']) ? ($last_year + 1) : ($last_year + 0.5);
                }

                if($split['year_at'] == $next){
                    $next_year = empty($split['type']) ? ($next_year + 1) : ($next_year + 0.5);
                }
            }

            if(!empty($info) && $this_year > 0){
                $info->days += $this_year;
                $info->leave_days = max($info->leave_days - $this_year,0);
                $info->update();
            }

            if(!empty($last_year)){
                $last_info = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
                    'bind' => ['staff_id' => $staff_id,'year_at' => $last,'leave_type' => $leave_type]
                ]);
                if(!empty($last_info)){
                    $last_info->days += $last_year;
                    $last_info->leave_days =  max($last_info->leave_days - $this_year,0);
                    $last_info->update();
                }
            }

            if(!empty($next_year)){
                $next_info = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
                    'bind' => ['staff_id' => $staff_id,'year_at' => $next,'leave_type' => $leave_type]
                ]);
                if(!empty($next_info)){
                    $next_info->days += $next_year;
                    $next_info->leave_days = max($next_info->leave_days - $next_year,0);
                    $next_info->update();
                }
            }

        }
        $this->logger->write_log("假期余额额度操作 {$up_type} {$staff_id} ".json_encode($param),'info');

    }



    //存在 需要用到去年额度的 改下标记年
    public function year_flag_new($staffId,$insert,$leave_param){
        $left_days = $leave_param['left_days'];
        $invalid_date = $leave_param['invalid_date'] ?? array();
        $need_last_year = 0;
        // 拼接 拆分表 归属年 year_at 字段
        $add_row = array();//出现不够减情况 额外新增一条  merge到 insert
        foreach ($insert as $k => $in) {
            //如果 是在有效期外的 日期 不操作标记年
            if(in_array($in['date_at'],$invalid_date))
                continue;
            $duration = empty($in['type']) ? 1 : 0.5;
            if ($left_days > 0 && ($left_days - $duration >= 0)) {//还够减
                $insert[$k]['year_at'] = date('Y', strtotime("-1 year"));
                $left_days             = $left_days - $duration;
                $need_last_year += $duration;
            } else if ($left_days > 0 && ($left_days - $duration < 0)) {//不够减了（剩余0。5 本次记录 需要1天 只能是这种情况 把本条记录更改为半天 额外新增一条半天记录）
                $insert[$k]['type']    = 1;
                $insert[$k]['year_at'] = date('Y', strtotime("-1 year"));

                //拼接剩下半天 标记归属年 为今年 merge
                $add_row[0]['staff_info_id'] = $staffId;
                $add_row[0]['date_at']       = $in['date_at'];
                $add_row[0]['type']          = 2;
                $add_row[0]['year_at']       = date('Y');

                $left_days = 0;//减没了
                $need_last_year+= 0.5;
            }
        }

        if(!empty($leave_param['need_num']) && $leave_param['need_num'] === true)
            return $need_last_year;

        if (!empty($add_row))
            $insert = array_merge($insert, $add_row);

        return $insert;
    }

    //发放跨国探亲假操作 并返回对应的额度
    public function send_country_leave_days($staff_info){
        $return['limit'] = $return['last_limit'] = $return['sub'] = $return['last_sub'] = 0;
        $send_days = ($staff_info['job_title_grade_v2'] >= 19) ? 3 : 1;
        $info = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
            'bind' => ['staff_id' => $staff_info['staff_info_id'],'year_at' => date('Y'),'leave_type' => enums::LEAVE_TYPE_19]
        ]);

        if(!empty($info)){
            $return['limit'] = half_num($info->days + $info->leave_days);
            $return['sub'] = half_num($info->days);
            return $return;
        }

        $row['staff_info_id'] = $staff_info['staff_info_id'];
        $row['leave_type'] = enums::LEAVE_TYPE_19;
        $row['year'] = date('Y');
        $row['days'] = $return['limit'] = $return['sub'] = $send_days;
        $model = new StaffLeaveRemainDaysModel();
        $model->create($row);

        return $return;

    }

    //获取 休息次数 请休息日类型假期用 从请假校验逻辑 搬过来的
    public function get_rest_times($staff_info,$leave_info){
        $staff_id = $staff_info['staff_info_id'];
        //获取请假月份 开始 结束 日期 新需求 http://193x782t53.imwork.net:29667/zentao/story-view-4065-1-project-89.html 周期从自然月更改为 30天周期
        //更改为 周期为自然周 判断请假日期 所在星期 是否存在记录 业务逻辑不变 http://193x782t53.imwork.net:29667/zentao/story-view-4281.html
        $today  = date('Y-m-d', strtotime($leave_info['leave_start_time']));
        $monday = weekStart($today) . " 00:00:00";
        $sunday = weekEnd($today) . " 23:59:59";

        //如果请休息日当天为 轮休 提示轮休日不用请假 https://shimo.im/docs/tRWKQ8wrt9JVWDWP/read
        $staff_re = new StaffRepository($this->lang);

        $off_days = $staff_re->get_work_days_between($staff_id, weekStart($today), weekEnd($today));
        $off_days = array_column($off_days, null, 'date_at');
        //当天是休息日 不用请假
        if (array_key_exists($today, $off_days)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('rest_day'));
        }

        $audit_model = new AuditRepository($this->lang);
        $month_leave = $audit_model->get_leave_days($staff_id, $monday, $sunday, enums::LEAVE_TYPE_15);
        //新需求 如果是 ph 不记入休息日 天数
        $leave_server = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        $ph_day       = $leave_server->ph_days($staff_info);
        $ph_day       = array_column($ph_day, 'day');

        //ph 不让申请休息日 https://shimo.im/docs/tWPXXJD3xhQhKy83
        if (in_array($today, $ph_day)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('ph_day'));
        }

        //每种类型的假期 都有多少天
        $days = $times = 0;
        if (empty($month_leave)) {
            return $this->checkReturn(['code' => 1, 'msg' => '', 'data' => $times]);
        }

        $leave_dates = $change_leave = [];
        foreach ($month_leave as $day) {
            $_d = date('Y-m-d', strtotime($day['leave_start_time']));
            //调休的不占用额度
            if (array_key_exists($_d,$off_days) && $off_days[$_d]['src_week']){
                $change_leave[] = $_d;
                continue;
            }
            if (in_array($_d, $ph_day)) {
                continue;
            }

            $days          += $day['leave_day'];
            $leave_dates[] = $_d;
        }

        if (empty($leave_dates)) {
            return $this->checkReturn(['code' => 1, 'msg' => '', 'data' => $times]);
        }

        //排除ph后 剩余的 已请 休息日
        $leave_dates = array_unique($leave_dates);
        sort($leave_dates);
        //总申请请假次数 //剔掉批量调休的
        $times = max(count($month_leave) - count($change_leave),0);

        //新增需求 如果超过5次 但是申请过的休息日当天出勤时间大于等于8.5小时且没有申请OT，不占用休息日请假次数。
        //周期改为一周 次数为1天
        if (empty($times)) {
            return $this->checkReturn(['code' => 1, 'msg' => '', 'data' => $times]);
        }

        //获取大于8。5小时的 考勤记录
        $att_model = new AttendanceRepository($this->lang, $this->timezone);
        $att_list  = $att_model->getSignInfo($staff_id, $leave_dates[0], end($leave_dates));
        if (empty($att_list)) {
            return $this->checkReturn(['code' => 1, 'msg' => '', 'data' => $times]);
        }

        //存在 休息日来上班的情况 看有没有申请加班
        $check_ot_date = [];
        foreach ($att_list as $att) {
            if (in_array($att['date_at'],
                    $leave_dates) && !empty($att['second_last']) && $att['second_last'] / 3600 >= 8.5) {
                $check_ot_date[] = $att['date_at'];
            }
        }

        if (empty($check_ot_date)) {
            return $this->checkReturn(['code' => 1, 'msg' => '', 'data' => $times]);
        }

        //判断是否有 ot记录 如果没有 次数减1
        $ot_model = new OvertimeRepository($this->timezone);
        foreach ($check_ot_date as $ot_date) {
            $record = $ot_model->getOtByDate($staff_id, $ot_date);
            if (empty($record)) {
                $times--;
            } else {//如果 存在ot  并且 驳回撤销状态 也不算次数
                $st_arr = array_column($record, 'state');
                if (!in_array(1, $st_arr) && !in_array(2, $st_arr)) {
                    $times--;
                }
            }
        }
        return $this->checkReturn(array('code' => 1,'msg'=>'','data' => $times));
    }




    //获取带薪事假 额度 这个只有当前年的额度 如果申请跨年 明年的 走下面的 check_personal_leave 方法
    public function get_personal_days($staff_info){
        //基础额度 就是 3 如果数据小于3  按3 显示
        $type = enums::LEAVE_TYPE_2;
        $freeze_info = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind' => [
                'staff_id' => $staff_info['staff_info_id'],
                'leave_type' => $type,
                'year_at' => date('Y'),
            ]

        ]);
        //可能是 新入职 还没跑任务 或者 没转正都是3
        if(empty($freeze_info)) {
            $da['day_limit'] = enums::PERSONAL_DAYS_UN;
            $da['day_sub'] = enums::PERSONAL_DAYS_UN;
            $da['day_sum'] = 0;
            return $da;
        }

        //转正以后 看 是不是当年转正 如果是往年转正 额度直接发7天
        if(($staff_info['status'] == 4 || $staff_info['hire_date'] < '2020-06-13 00:00:00')){

            $formal_year = date('Y',strtotime($staff_info['formal_at']));
            if(!empty($staff_info['formal_at']) && $formal_year == date('Y')){
                $da['day_sub'] = half_num($freeze_info->days);//剩余天数 每日递增
                $limit = half_num($freeze_info->days + $freeze_info->leave_days);//剩余天数 + 申请天数
                $da['day_limit'] = $limit > enums::PERSONAL_DAYS_UN ? $limit : enums::PERSONAL_DAYS_UN;//和 3比大小 取大
                //如果是提前转正 额度递增可能小于3 要判断一下
                $act_sub = half_num($da['day_limit'] - $freeze_info->leave_days);
                $da['day_sub'] = $act_sub > $da['day_sub'] ? $act_sub : $da['day_sub'];//
                $da['day_sum'] = $freeze_info->leave_days;

            }else{//往年转正 额度是满额 直接从数据库取使用额度
                $da['day_limit'] = enums::PERSONAL_DAYS;//已经转正了 就是固定7天 不从数据库取加和
                $da['day_sum'] = $freeze_info->leave_days;
                $da['day_sub'] = half_num($da['day_limit'] - $freeze_info->leave_days);//分子 剩余额度
            }

        }else{//没转正 只有3
            $da['day_limit'] = enums::PERSONAL_DAYS_UN;
            $da['day_sub'] = $da['day_limit'] - $freeze_info->leave_days;
            $da['day_sum'] = $freeze_info->leave_days;
        }
        return $da;
    }

    //返回 带薪事假 额度 如果 预申请明年额度 会初始化一条记录 变态玩意
    public function check_personal_leave($staff_info,$year){
        //如果是 申请当年的 返回当前剩余额度
        if(date('Y') == $year){
            $r = $this->get_personal_days($staff_info);
            return $r['day_sub'];
        }
        //请去年的 额度失效了 不让请
        if($year < date('Y'))
            return 0;


        //预申请明年的 需要判断一下 是否转正
        $format = true;//已经转正了
        //如果没转正 额度就是3
        if(date('Y-m-d',strtotime($staff_info['hire_date'])) >= '2020-06-13' && $staff_info['status'] != 4)
            $format = false;//没转正

        $remain_info = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind' => [
                'staff_id' => $staff_info['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_2,
                'year_at' => $year,
            ]

        ]);

        //如果 没有额度 数据 需要初始化一条
        if(empty($remain_info)){
            $remain_info = new StaffLeaveRemainDaysModel();
            //获取 对应年 已经申请的假期
            $audit_re = new AuditRepository($this->lang);
            $used_day = 0;
            $used = $audit_re->get_used_leave_days($staff_info['staff_info_id'],$year,'2');
            if(!empty($used) && !empty($used[0]['num']))
                $used_day = $used[0]['num'];

            $row['staff_info_id'] = $staff_info['staff_info_id'];
            $row['leave_type'] = enums::LEAVE_TYPE_2;
            $row['year'] = $year;
            $row['days'] = $format ? (enums::PERSONAL_DAYS - $used_day) : 0;
            $row['leave_days'] = $used_day;
            $remain_info->create($row);

        }
        //没转正 程序写死 3天
        $limit = $format ? $remain_info->days : (enums::PERSONAL_DAYS_UN - $remain_info->leave_days);

        return $limit;

    }

    /**
     * 从 audit server 迁移过来
     * 根据员工信息 计算区间内是否存在休息日 返回对应休息日日期数组
     * 用于计算 请假区间 扣除包含休息日的天数
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function staff_off_days($staff_id, $start_date, $end_date = '')
    {
        if (empty($end_date)) {
            $end_date = $start_date;
        }

        //获取员工信息
        $model      = new StaffRepository($this->lang);
        $staff_info = $model->getStaffPosition($staff_id);
        $rest       = $model->get_work_days_between($staff_id, $start_date, $end_date);
        $rest       = !empty($rest) ? array_column($rest, 'date_at') : [];

        $leave_server = new LeaveServer($this->lang, $this->timezone);
        $leave_server = Tools::reBuildCountryInstance($leave_server, [$this->lang, $this->timezone]);
        $holidays     = $leave_server->ph_days($staff_info);
        $holidays     = empty($holidays) ? [] : array_column($holidays, 'day');
        if (!empty($holidays)) {
            $rest = array_merge($rest, $holidays);
        }

        $this->logger->write_log("ignore rest_day {$staff_id} ".json_encode($rest), 'info');

        return $rest;
    }

    /**
     * 员工类型 验证
     * @param $staffInfo
     */
    public function hireTypeCheck($staffInfo){
        //正式员工、月薪制合同工、日薪制合同工、时薪制合同工
        $flag = $staffInfo['formal'] == HrStaffInfoModel::FORMAL_1 && in_array($staffInfo['hire_type'], [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
                HrStaffInfoModel::HIRE_TYPE_3,
                HrStaffInfoModel::HIRE_TYPE_4,
            ]);
        return $flag;
    }

    //是否满一年
    public function overOneYear($staffInfo){
        $today = date('Y-m-d');
        //入职日期 + 1年
        $hireDateYear = date('Y-m-d',strtotime("{$staffInfo['hire_date']} +1 year"));
        $flag = false;//默认 没满一年

        //满一年
        if($hireDateYear <= $today){
            $flag = true;
        }
        return $flag;
    }

    /**
     * @description 带薪事假请假原因
     * @return array[]
     */
    public function getPaidLeaveReasonList(): array
    {
        return [];
    }

    /** 申请假期 保存
     * @param $param
     * @throws ValidationException
     * @return array
     */
    public function saveVacation($param){
        $this->leaveObject = $this->getLeaveObj(intval($param['leave_type']));

        $db = $this->getDI()->get('db');
        $db->begin();
        try{
            $audit_id = $this->leaveObject->handleCreate($param);
            //没生成id
            if (empty($audit_id)) {
                throw new ValidationException($this->getTranslation()->_('1009'));
            }

            //二次确认结构 code ->1 msg -> xxx data -> ['is_error' -> 1]
            if(is_array($audit_id)){
                return $audit_id;
            }

            //非工具操作申请 创建审批相关
            if (empty($param['is_bi'])) {
                $auditServer = new AuditServer($this->lang, $this->timeZone);
                $param['time_out'] = $this->leaveObject->timeOut ?? null;
                $auditServer->saveApproval($audit_id, $param);
            }

            $db->commit();
            $return['data'] = ['leave_day' => $param['leave_days'] ?? 0];
            return $this->checkReturn($return);
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }

    //非审核通过 撤销操作 返还额度
    public function cancelVacation($auditInfo,$staffInfo,$state, $extend = []){
        $extend['status'] = $state;
        $this->leaveObject = $this->getLeaveObj(intval($auditInfo['leave_type']));
        $this->leaveObject->returnRemainDays($auditInfo['audit_id'],$staffInfo,$extend);
    }

    //获取对应假期的额度
    public function getVacationDays($staffId,$leaveType, $extend = []){
        $param['staff_id'] = $staffId;
        $param['leave_type'] = $leaveType;
        $param = array_merge($param,$extend);
        $leaveObject = $this->getLeaveObj($param['leave_type']);
        return $leaveObject->handleSearch($param);
    }


    /**
     * 对应本国的 所有类型 映射类
     * @param $leaveType
     * @throws ValidationException
     */
    public function getLeaveObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_19://跨国探亲
                $leaveObj =  new InternationalServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_4://产假
                $leaveObj =  new MaternityServer($this->lang,$this->timeZone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }


    public function getInstanceObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_19://跨国探亲
                $leaveObj = InternationalServer::getInstance($this->lang,$this->timezone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }

    //判断员工班次 和请假时间 返回二次确认
    public function checkShiftLeave($staffId,$leaveParam){
        //前提条件 请假日期 和 当前日期 是同一天
        $today = date('Y-m-d');
        $date = date('Y-m-d',strtotime($leaveParam['leave_start_time']));
        if($today != $date){
            return ;
        }

        //查询根据请假日期对应班次
        $shiftServer = new HrShiftServer();
        $shiftInfo = $shiftServer->getShiftInfos($staffId,[$date]);
        //没有班次
        if(empty($shiftInfo[$date])){
            return;
        }

        $startTmp = strtotime($shiftInfo[$date]['start_datetime']);
        $currentTmp = time();
        if($leaveParam['leave_start_type'] == StaffAuditModel::LEAVE_PM){
            $startTmp += 5 * 3600;
        }
        if($currentTmp >= $startTmp){
            throw new ValidationException($this->getTranslation()->_('leave_shift_notice'),10086);
        }

        if((2 * 3600 + $currentTmp) >= $startTmp){
            throw new ValidationException($this->getTranslation()->_('leave_shift_notice_close'),10086);
        }
        return;
    }

    /**
     * FromCache 使用中！！！
     * 获取请假时间类型
     * @param $staff_id
     * @param $date
     * @return int
     */
    public function getStaffLeaveInfo($staff_id, $date): int
    {
        $staff_leave = 0;
        //休息日 没有 再看看 请假没
        $p['staff_id']         = $staff_id;
        $p['leave_start_time'] = $p['leave_end_time'] = $date;
        $auditRe               = new AuditRepository($this->lang);
        $isLeave               = $auditRe->getLeaveData($p, enums::APPROVAL_STATUS_APPROVAL);
        //新增逻辑 请半天 不算 必须得请一天
        if (!empty($isLeave)) {
            $typeSum = array_sum(array_column($isLeave, 'sum_type'));
            if ($typeSum == 0 || $typeSum == 3) {//一整天 或者 2个半天 都算请一天
                $staff_leave = 3;
            } else {
                $staff_leave = intval($typeSum);
            }
        }
        return $staff_leave;
    }





    //泰国病假特殊逻辑
    public function formatSickDetail($info, $currentStaff)
    {

        $return = [];
        if (!isCountry('TH')) {
            return $return;
        }

        //病假材料状态
        $return[] = ['key' => $this->getTranslation()->_('certificate_status'), 'value' => $this->getTranslation()->_(StaffAuditModel::$subState[$info['sub_status']])];

        //当前登陆人是申请人
        if ($info['staff_info_id'] == $currentStaff) {
            return $return;
        }

        if (empty($info['template_comment'])) {
            return $return;
        }

        $data   = json_decode($info['template_comment'], true);
        if (!empty($data['leave_face_img'])) {
            $return[] = ['key' => $this->getTranslation()->_('leave_face_img'), 'value' => [$data['leave_face_img']]];
        }
        if (!empty($data['leave_lat'])) {
            $return[] = [
                'key'   => $this->getTranslation()->_('coordinate'),
                'value' => "https://www.google.com/maps/place/{$data['leave_lat']},{$data['leave_lng']}/",
                'lat'   => $data['leave_lat'],
                'lng'   => $data['leave_lng'],
                'type'  => enums::APPROVAL_DETAIL_TYPE_COORDINATE,
            ];
        }

        return $return;
    }

    //整理详情页图片
    public function formatLeaveImg($data, $leaveType){
        if(empty($data)){
            return [];
        }

        $photo = [];
        if(isCountry('TH')){
            $category = SickServer::$sickImgCategory;
        }
        $isSick = isCountry('TH') && $leaveType == enums::LEAVE_TYPE_38;
        foreach ($data as $item) {
            if($isSick){
                $key = $category[$item['business_category']] ?? $category[SickServer::SICK_OTHER];
                $photo[$key][] = $item['image_path'];
                continue;
            }
            $photo[] = $item['image_path'];
        }

        return $photo;
    }

    public function formatMilitaryDetail($data){
        if(empty($data['template_comment'])){
            return [];
        }
        $comment = json_decode($data['template_comment'],true);
        $res = [];
        if(!empty($comment['agree_reason'])){
            $res[] = ['key' => $this->getTranslation()->_('agree_reason'), 'value' => $comment['agree_reason']];
        }
        if(!empty($comment['agree_url'])){
            $res[] = ['key' => $this->getTranslation()->_('agree_url'), 'value' => [$comment['agree_url'] ?? '']];
        }
        return $res;
    }


    //是否有请假权限
    public function leavePermission($staffInfo){
        if(empty($staffInfo['working_country'])){
            return false;
        }
        $codeList = HrStaffInfoModel::$countryCode;
//        if(empty($codeList[$staffInfo['working_country']])){
//            return false;
//        }
        //工作国家和当前app 是同一个
        if(isCountry($codeList[$staffInfo['working_country']] ?? 0)){
            return true;
        }
        //获取配置项
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('leave_for_not_local_staff')
            ->setParameters(['staff_info_id' => $staffInfo['staff_info_id']])
            ->getConfig();
        $setting = $res['response_data'] ?? '';
        //如果 工号存在配置 也有权限
        $setting = empty($setting) ? [] : explode(',', $setting);
        if(in_array($staffInfo['staff_info_id'], $setting)){
            return true;
        }
        return false;
    }


}