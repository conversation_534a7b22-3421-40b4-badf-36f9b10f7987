<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Repository\LogRepository;

class LogServer extends BaseServer {

    /**
     * 日志添加
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addLog($params = []) {
        try {
            $moduleId = $params['module_id'] ? $params['module_id'] : 0;
            $moduleType = $params['module_type'] ? $params['module_type'] : 0;
            $moduleLevel = $params['module_level'] ?? 0;
            $moduleStatus = $params['module_status'] ?? 0;
            $action = $params['action'] ? $params['action'] : 0;
            $dataAfter = $params['data_after'] ? $params['data_after'] : [];
            $dataBefore = $params['data_before'] ?? [];

            $userIp = $_SERVER["REMOTE_ADDR"];
            $staffInfoId = $params['current_user_id'];

            if (!$staffInfoId) {
                return [];
            }

            //获取更新前后数据比较结果
            $dataDiff = (new LogRepository())->getDataDiff([
                'data_after' => $dataAfter,
                'action' => $action,
                'module_id' => $moduleId,
                'module_type' => $moduleType,
            ]);

            if (empty($dataBefore)) {
                $dataBefore = $dataDiff['data_before'] ??  '';
                $dataAfter = $dataDiff['data_after'] ??  '';
            } else {
                $dataBefore = json_encode($dataBefore);
                $dataAfter = json_encode($dataAfter);
            }

            $paramLog = [
                'staff_info_id' => $staffInfoId,
                'module_id' => $moduleId,
                'module_type' => $moduleType,
                'module_level' => $moduleLevel,
                'module_status' => $moduleStatus,
                'action' => $action,
                'user_ip' => $userIp,
                'data_before' => $dataBefore,
                'data_after' => $dataAfter
            ];
            return (new LogRepository())->addLog($paramLog);
        } catch (\Exception $e) {
            $this->wLog('添加日志异常', $e->getMessage(), 'Log');
        }
    }

}
