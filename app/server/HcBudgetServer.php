<?php


namespace FlashExpress\bi\App\Server;


use App\Country\Tools;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HcmStaffManageListModel;
use FlashExpress\bi\App\Models\backyard\HcmStaffManageRegionModel;
use FlashExpress\bi\App\Models\backyard\HRHCBudgetItemModel;
use FlashExpress\bi\App\Models\backyard\HRHCBudgetModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HRStaffingModel;
use FlashExpress\bi\App\Models\backyard\HrStatisticalBudgetDept;
use FlashExpress\bi\App\Models\backyard\HrStatisticalBudgetStore;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Repository\HcBudgetRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use Matrix\Exception;

class HcBudgetServer extends AuditBaseServer
{
	/**
	 * @description: 获取共享部门和职位
	 *
	 * @param null
	 * department_ids 所有的共享部门 id   position_ids 所有的共享职位 id    department_position  部门=>[共享职位,共享职位]
	 * @return     :['department_ids'=>[1,2],'position_ids'=>[3,4,5,6],'department_position'=>[1=>[3,4],2=>[5,6]]];
	 * <AUTHOR> L.J
	 * @time       : 2022/1/12 17:31
	 */
    public function getShareDepartmentPosition(): array
    {
        $result = ['department_ids' => [], 'position_ids' => [], 'department_position' => []];

        try {
            $envModel = new SettingEnvServer();
            //格式是  部门|职位,职位&部门|职位,职位
            $share = $envModel->getSetVal('hc_share_department_position');
            if (!empty($share)) {
                $share = explode('#', $share);
                foreach ($share as $k => $v) {
                    if (empty($v)) {
                        continue;
                    }
                    $department_position                           = explode('|', $v);
                    $department_id                                 = $department_position[0] ?? 0;//部门 id
                    $result['department_ids'][]                    = $department_id;              //部门 id
                    $result['department_position'][$department_id] = [];
                    if (isset($department_position[1]) && !empty($department_position[1])) {
                        $position_ids                                  = explode(',', $department_position[1]); //职位 id
                        $result['position_ids']                        = array_merge($result['position_ids'],
                            $position_ids);
                        $result['department_position'][$department_id] = $position_ids;
                    }
                }
            }
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log(
                'file '.$e->getFile().
                ' line '.$e->getLine().
                ' message '.$e->getMessage().
                ' trace '.$e->getTraceAsString()
            );
        }
        return $result;
    }
	

    /**
     * 添加申请
     * @param $data
     * @return array
     */
    public function addHcBudget($data)
    {
        try {
            $department = FleSysDepartmentModel::findFirst($data['dept_id']);
            $staff = HrStaffInfoModel::findFirstByStaffInfoId($data['applicant_id']);
            $applicant_dept_name = '';
            if($staff) {
                $staff_dept = FleSysDepartmentModel::findFirst($staff->node_department_id);
                $applicant_dept_name = $staff_dept ? $staff_dept->name : '';
            }
            $db = $this->getDI()->get('db');
            $db->begin();

            $bm = new HRHCBudgetModel();
            $bm->budget_month = $data['budget_month'];
            $bm->dept_id = $data['dept_id'];
            $bm->dept_name =$department ? $department->name:'';
            $bm->applicant_id = $data['applicant_id'];
            $bm->applicant_name = $staff ? $staff->name:'';
            $bm->applicant_dept_id = $staff ? $staff->node_department_id : -1;
            $bm->applicant_dept_name = $applicant_dept_name;
            $bm->remarks = $data['remarks'];
            $bm->annex = $data['annex'];
            $bm->summary = $data['summary'];
            $bm->item_file = $data['item_file'];
            $bm->is_batch = $data['is_batch'];
            $bm->status = enums::APPROVAL_STATUS_PENDING;
            $bm->created_at = date('Y-m-d H:i:s');
            $bm->save();
            $item = $data['items'];

            if (empty($bm->id)) {
                throw new \Exception('HR_HC_BUDGET SAVE ERROR');
            }

            $hc_budget_r = new HcBudgetRepository();
            $job_title_list = $hc_budget_r->getAllJobTitleList();
            $store_list = $hc_budget_r->getAllStoreList();
            $dept_list = $hc_budget_r->getAllDeptList();

           
	        //获取需要使用的值  过滤不用的值
	        $items=[];
            foreach ($item as $key => $value) {
	            $items[$key]['adopt_count'] = $value['adopt_count'] ?? 0;//已提交的hc人数（待审批）
	            $items[$key]['budget_count'] = $value['budget_count'] ?? 0;//预算人数
	            $items[$key]['dept_id'] = $value['dept_id'] ?? 0;//部门 id
	            $items[$key]['hiring_count'] = $value['hiring_count']; //招聘中人数
	            $items[$key]['job_title_id'] = $value['job_title_id']; //职位
	            $items[$key]['on_job_count'] = $value['on_job_count']; //在职人数
	            $items[$key]['store_id'] = $value['store_id']; //网点
	            $items[$key]['to_be_hired_count'] = $value['to_be_hired_count']; //带入职人数
	            
                if(array_key_exists('store_id', $value) && !empty($value['store_id'])){
                    $store_name = $store_list[$value['store_id']]['name'] ?? '';
                } else {
                    $store_name = '';
                }
                $items[$key]['budget_id'] = $bm->id;
                $items[$key]['dept_name'] = $dept_list[$value['dept_id']]['name'] ?? '';
                $items[$key]['job_title_name'] = $job_title_list[$value['job_title_id']]['job_name'] ?? '';
                $items[$key]['store_name'] = $store_name;
                $items[$key]['created_at'] = date('Y-m-d H:i:s');
            }
            $items_result = $hc_budget_r->batch_insert('hr_hc_budget_item',$items);

            $server = new ApprovalServer($this->lang, $this->timeZoneOfThailand);
            $reqId = $server->create($bm->id, enums::$audit_type['HCB'], $data['applicant_id'],null,[
                'department_id' => $data['dept_id'],
                'Array_department_id' => [$data['dept_id']],
            ]);
            if (!$reqId) {
                throw new \Exception('创建审批流失败');
            }

            $db->commit();

        }catch (\Exception $e){
            $db->rollback();
            $this->di->get('logger')->write_log('addHcBudgetError---'.$e->getMessage().'-----'.$e->getLine());
            return ['code'=>0,'msg'=>$e->getMessage(),'data'=>null];
        }

        return ['code'=>1,'msg'=>'','data'=>['hcb_id'=>$bm->id]];
    }

    /**
     * 预算详情
     * @param $data
     * @return array
     * @throws \FlashExpress\bi\App\library\Exception\InnerException
     */
    public function getHcBudgetDetail($data)
    {
        try{
            $server = new ApprovalServer($this->lang, $this->timeZoneOfThailand);
            $detail = $server->getAuditDetail($data['hcb_id'], enums::$audit_type['HCB'], $data['staff_id'], 1, null,
                '', 2);
            $this->getDI()->get('logger')->write_log('getHcBudgetDetail Param' .json_encode($data).'----'.json_encode($detail), 'info');
            $detail['data']['stream'] = array_reverse($detail['data']['stream']);
            return ['code'=>1,'msg'=>'','data'=>$detail['data']];
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('getHcBudgetDetail Error' .$e->getMessage().'----'.$e->getLine(), 'info');
            return ['code'=>1,'msg'=>'','data'=>[]];
        }
    }

    /**
     * 审批列表
     * @param $params
     * @return array
     */
    public function getHcBudgetList($params)
    {
        $params['limit'] = $params['page_size'] ?? 100;
        $params['offset'] = (($params['page_num'] ?? 1)-1)*$params['limit'];
        $flag = $params['tab'] ?? 0;
        $is_all = $params['is_all'] ?? 0;

        $columns = [
            'b.id',
            'b.budget_month',
            'b.dept_id',
            'b.dept_name',
            'b.applicant_id',
            'b.applicant_name',
            'b.applicant_dept_id',
            'b.applicant_dept_name',
            'b.status',
            'b.remarks',
            'b.annex',
            'b.summary',
            'b.item_file',
            'b.is_batch',
            'b.created_at',
        ];

        $builder = $this->modelsManager->createBuilder()->from(['b'=>HRHCBudgetModel::class])->where('1=1');

        if (isset($params['budget_month']) && !empty($params['budget_month'])){
            $builder->andWhere('b.budget_month=:month:',['month'=>$params['budget_month']]);
        }
        if (isset($params['status']) && !empty($params['status'])){
            $builder->andWhere('b.status = :status:',['status'=>$params['status']]);
        }
        if (isset($params['dept_id']) && !empty($params['dept_id'])){
            $builder->andWhere('b.dept_id = :dept_id:',['dept_id'=>$params['dept_id']]);
        }
        if ($params['type'] ==1 && $is_all != 1){
            $builder->andWhere('b.applicant_id=:staff_id:',['staff_id'=>$params['staff_id']]);
        }elseif ($params['type'] == 2){
            $columns[] = 'aa.audit_time';

            switch ($flag){
                case 1:
                    //待处理
                    $builder->leftJoin(AuditApprovalModel::class,'b.id=aa.biz_value and aa.biz_type='.enums::$audit_type['HCB'],'aa');
                    $builder->andWhere('aa.state=1 and aa.approval_id=:staff_id: and aa.deleted = 0',['staff_id'=>$params['staff_id']]);
                    break;
                case 2:
                    //已处理
                    $builder->leftJoin(AuditApprovalModel::class,'b.id=aa.biz_value and aa.biz_type='.enums::$audit_type['HCB'],'aa');
                    //$builder->leftJoin(AuditLogModel::class,'aa.id=al.request_id and al.audit_action in (2,3)','al');
                    $builder->andWhere('aa.state in (2,3) and aa.approval_id=:staff_id: and aa.deleted = 0',['staff_id'=>$params['staff_id']]);
                    break;
                default:
                    break;
            }
        }

        $count = (int) $builder->columns('COUNT(DISTINCT b.id) AS total')->getQuery()->getSingleResult()->total;

        $list = [];
        if ($count) {
            $builder->columns($columns);
            $builder->groupBy('b.id');

            $list = $builder->orderBy('b.id desc')
                ->limit($params['limit'],$params['offset'])
                ->getQuery()->execute()
                ->toArray();
            $list = $this->handleList($list);
        }

        $result = ['code' => 1,'msg' => 'ok','data' => ['count' => $count, 'items' => $list]];

        $this->getDI()->get("logger")->write_log("getHcBudgetList，参数: " . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 结果: ' . json_encode($result, JSON_UNESCAPED_UNICODE), 'info');

        return $result;
    }

    /**
     * @param $param 同部门 同职位 同网点是否有重复申请
     * @return array
     */
    public function getHcBudgetDetailRepeat($param) {
        $budget_month = $param['budget_month'];
        $dept_id = $param['dept_id'] ?? '';
        $job_title_id = $param['job_title_id'] ?? '';
        $store_id = $param['store_id'] ?? '';
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hr_hc_budget'=>HRHCBudgetModel::class]);
        $builder->innerJoin(HRHCBudgetItemModel::class,'hr_hc_budget.id=hr_hc_budget_item.budget_id','hr_hc_budget_item');
        $builder->where('hr_hc_budget.status = 1');
        $builder->andWhere('hr_hc_budget.budget_month = :budget_month:', ['budget_month' => $budget_month]);
        if(!empty($dept_id) && !empty($job_title_id)) {
            $builder->andWhere('hr_hc_budget_item.dept_id = :dept_id:',['dept_id' => $dept_id]);
            $builder->andWhere('hr_hc_budget_item.job_title_id = :job_title_id:',['job_title_id' => $job_title_id]);
        }
        if( !empty($store_id)) {
            $builder->andWhere('hr_hc_budget_item.store_id = :store_id:',['store_id' => $store_id]);
        }
        $totalCount = $builder->columns('COUNT(1) AS total')->getQuery()->execute()->getFirst();

        //$list = $builder->getQuery()->execute()->toArray();
        return ['code' => 1,'msg' => 'ok','data' => intval($totalCount->total)];
    }

    /**
     * @param $data
     * @return mixed
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function audit($data)
    {
        $result = [
            'code' => 1,
            'msg' => 'success',
            'data'=>[],
        ];

        try {
            $this->getDI()->get('logger')->write_log('hcb_audit_request data :' . json_encode($data), 'info');

            $hcbId = $data['hcb_id'];
            $action = $data['action'];
            $staffId = $data['staff_id'];

            $server = new ApprovalServer($this->lang, $this->timeZoneOfThailand);

            $audit_result = false;
            switch ($action){
                case 1:
                    //同意
                    $audit_result = $server->approval($hcbId, enums::$audit_type['HCB'], $staffId, $data['note']);
                    break;
                case 2:
                    $audit_result = $server->reject($hcbId, enums::$audit_type['HCB'],$data['note'], $staffId);
                    break;
                case 3:
                    $audit_result = $server->cancel($hcbId, enums::$audit_type['HCB'],$data['note'], $staffId);
                    break;
                default:
                    break;
            }

            if ($audit_result === false) {
                $result['code'] = 0;
                $result['msg'] = 'audit abnormal';
            }

            $this->getDI()->get('logger')->write_log('hcb_audit_request audit_result : ' . $result['msg'], 'info');

        } catch (\Exception $e) {
            $result['code'] = 0;
            $result['msg'] = 'audit abnormal';

            $this->getDI()->get('logger')->write_log('hcb_audit_request exception info:' . $e->getMessage() . "; exception line: " . $e->getLine(), 'error');
        }

        $this->getDI()->get('logger')->write_log('hcb_audit_request res :' . json_encode($result), 'info');

        return $result;
    }

    /**
     * @param $data
     * @return array
     */
    public function getStaffing($data)
    {
        $result = [];
        try {
            $hcs = Tools::reBuildCountryInstance(new HcServer($this->lang,$this->timeZone), [$this->lang, $this->timeZone]);
            foreach ($data as $k=>$v){
                $count = $hcs->getHcBudgetCount($v['month'],$v['dept_id'],$v['store_id'],$v['job_title_id']);
                $result[$k] = [
                    'dept_id'        => $v['dept_id'],
                    'store_id'       => $v['store_id'],
                    'job_title_id'   => $v['job_title_id'],
                    'on_the_job'     => $count['on_job'],
                    'to_be_employed' => $count['pending_entry'],
                    'hiring'         => $count['surplus'],
                    'pendingCon'     => $count['pendingCon'],
                ];
            }
            return $result;
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("getStaffing，参数:".json_encode($data).'----结果：'.$e->getMessage().'----'.$e->getLine(), "info");
            return $result;
        }
    }

    /**
     * @param $auditId
     * @param $user
     * @return array
     */
    public function genSummary($auditId, $user)
    {
        return [];
    }

    /**
     * @param $auditId
     * @param $user
     * @return array
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $hcr = new HcRepository($this->timeZoneOfThailand);
        $hcb = HRHCBudgetModel::findFirst($auditId);
        $hcbi = HRHCBudgetItemModel::find([
            'conditions' => 'budget_id = ?1',
            'bind' => [
                1 => $auditId,
            ],
        ])->toArray();

        //申请的job title列表
        $jobTitles = array_unique(array_column($hcbi, 'job_title_id'));

        //获取job title职级
        $jobTitleLevelArr = [];
        if (is_array($hcbi)) {
            foreach ($hcbi as $v) {
                //获取直属部门关联的职位的职级
                $jobTitleLevel = HrJobDepartmentRelationModel::findFirst([
                    'conditions' => 'department_id = :department_id: and job_id = :job_ids:',
                    'bind' => [
                        'department_id' => $v['dept_id'],
                        'job_ids' => $v['job_title_id'],
                    ],
                    'columns' => 'job_level',
                ]);
                if (empty($jobTitleLevel)) {
                    throw new Exception('4008');
                }
                $jobTitleLevel = $jobTitleLevel->toArray();
                $jobTitleLevelArr[] = max(explode(',', $jobTitleLevel['job_level']));
            }
        }

        //获取特殊职位配置
        $envModel = new SettingEnvServer();
        $specialJobId = $envModel->getSetVal('hc_budget_special_job');
        $specialJobIdArr = !empty($specialJobId) ? explode(',', $specialJobId): [];

        //根据组织架构去判断申请人是否为CPO
        $ser = new WorkflowServer($this->lang, $this->timeZone);
        $cpoInfo = $ser->findCLevelManager(enums::WF_NODE_CPO);
        if ($hcb->applicant_id == $cpoInfo) {
            $isCPO = true;
        }

        //用人部门ID查找到公司
        $dInfo = FleSysDepartmentModel::findFirst([
            'conditions' => 'id = :department_id:',
            'bind' => [
                'department_id' => $hcb->dept_id,
            ],
        ]);

        if (empty($dInfo)) {
            throw new \Exception("Request department is not exist.");
        }

        //获取申请人在用人部门部门链上的位置
        $deptIds = explode('/', $dInfo->ancestry_v3);
        $dChain = FleSysDepartmentModel::find([
            'conditions' => 'id in ({ids:array}) and manager_id = :manager_id:',
            'bind' => [
                'ids' => $deptIds,
                'manager_id' => $hcb->applicant_id,
            ],
            'columns' => 'id,level,type',
        ])->toArray();

        //是否为BU Clevel
        $type = array_column($dChain, 'type');
        if (array_intersect($type, [1,4])) {
            $isBuClevel = true;
        }

        $level = array_column($dChain, 'level');
        if (in_array(1, $level)) {
            $isFirstDeptManager = true;
        }
        if (in_array(2, $level)) {
            $isSecendDeptManager = true;
        }

        //分析申请用人部门的级别
        if ($dInfo->type == 1) {
            $deptIsBU = true;
        } else if (in_array($dInfo->type,[4,5])) {
            $deptIsClevel = true;
        } else {
            switch ($dInfo->level) {
                case 1:
                    $deptIs1st = true;
                    break;
                case 2:
                    $deptIs2nd = true;
                    break;
                case 3:
                    $deptIs3rd = true;
                    break;
                case 4:
                    $deptIs4th = true;
                    break;
                default:
                    break;
            }
        }
        $dpChain = FleSysDepartmentModel::find([
            'conditions' => 'id in ({ids:array}) and deleted = 0',
            'bind' => [
                'ids' => $deptIds,
            ],
            'columns' => 'id,level,type',
        ])->toArray();
        $dpLevel = array_column($dpChain, 'level');

        if (in_array(1, $dpLevel)) {
            $reqDeptChainHas1st = true;
        }
        if (in_array(2, $dpLevel)) {
            $reqDeptChainHas2nd = true;
        }


        //获取申请人是否为Bp Head
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('a.staff_info_id');
        $builder->from(['a' => HcmStaffManageListModel::class]);
        $builder->leftJoin(HcmStaffManageRegionModel::class, 'a.id = b.pid', 'b');
        $builder->where('b.staff_info_id = :staff_id: and a.manage_type = 1 and b.category = 1 and b.deleted = 0 and b.value = :dept_id:',
            ['staff_id' => $hcb->applicant_id, 'dept_id' => $hcb->dept_id]);
        $manageRegion = $builder->getQuery()->execute()->toArray();
        if (!empty($manageRegion)) {
            $isBpHead = true;
        }

        //是否为people head
        if (!isCountry('TH')) {
            $envModel = new SettingEnvServer();
            $peopleHeadDeptId = $envModel->getSetVal('budget_people_head_dept_id');

            $phInfo = FleSysDepartmentModel::findFirst([
                'conditions' => 'id = :department_id: and manager_id = :manager_id:',
                'bind' => [
                    'department_id' => $peopleHeadDeptId,
                    'manager_id' => $hcb->applicant_id,
                ],
            ]);
            if (!empty($phInfo)) {
                $isPeopleHead = true;
            }
        }

        $return = [
            'is_cpo' => $isCPO ?? false,
            'is_bu_clevel' => $isBuClevel ?? false,
            'is_bp_head' => $isBpHead ?? false,
            'is_first_dept_manager' => $isFirstDeptManager ?? false,
            'is_secend_dept_manager' => $isSecendDeptManager ?? false,
            'req_dept_chain_has_1st' => $reqDeptChainHas1st ?? false,
            'req_dept_chain_has_2nd' => $reqDeptChainHas2nd ?? false,
            'is_req_dept_cl' => $deptIsClevel ?? false,
            'is_req_dept_bu' => $deptIsBU ?? false,
            'is_req_dept_1st' => $deptIs1st ?? false,
            'is_req_dept_2nd' => $deptIs2nd ?? false,
            'is_req_dept_3rd' => $deptIs3rd ?? false,
            'is_req_dept_4th' => $deptIs4th ?? false,
            'is_people_head' => $isPeopleHead ?? false,
            'request_job_titles_max_level' => max($jobTitleLevelArr),
            'request_job_titles_inclue_special' => (bool)array_intersect($jobTitles, $specialJobIdArr),
        ];
        $this->logger->write_log(sprintf("add budget staff id: %s, audit id: %s, data: %s", $hcb->applicant_id, $auditId, json_encode($return)), 'info');

        return $return;
    }

    /**
     * 是否是cpo部门且不是 Philippines People 部门
     *
     * @param $department_id
     *
     */
    public function isFlashHrOrCpoAndNotPhilippinesPeople($department_id)
    {
        $sysDepartment = FleSysDepartmentModel::findFirst([
            'conditions' => " ancestry_v3 like '999/222/70001%' and  id = :department_id: ",
            'bind' => [
                "department_id" => $department_id,
            ],
        ]);
        if ($sysDepartment) {
            return 1;
        }

        $sysDepartment = FleSysDepartmentModel::findFirst([
            "conditions" => " ancestry_v3 like '999/444/%' and ancestry_v3 not LIKE '999/444/210%' and id = :department_id: ",
            "bind" => [
                "department_id" => $department_id,
            ],
        ]);

        return $sysDepartment ? 1 : 0;
    }

    /**
     * 是否是cpo部门且不是 Philippines People 部门 - 泰国
     *
     * @param $department_id
     *
     */
    public function isFlashHrOrCpoAndNotPhilippinesPeopleTH($department_id)
    {
        $sysDepartment = FleSysDepartmentModel::findFirst([
            'conditions' => " ancestry_v3 like '999/222/70001%' and  id = :department_id: ",
            'bind' => [
                "department_id" => $department_id,
            ],
        ]);
        if ($sysDepartment) {
            return 1;
        }

        $sysDepartment = FleSysDepartmentModel::findFirst([
            "conditions" => " (ancestry_v3 like '999/444/%' or id = 444) and ancestry_v3 not LIKE '999/444/210/%' and ancestry_v3 NOT LIKE '999/444/303/%' and ancestry_v3 NOT LIKE '999/444/235/287/%' and ancestry_v3 NOT LIKE '999/444/235/289/%' and ancestry_v3 NOT LIKE '999/444/235/290/%' and id NOT IN (210, 303, 287, 289, 290) and id = :department_id: ",
            "bind" => [
                "department_id" => $department_id,
            ],
        ]);

        return $sysDepartment ? 1 : 0;
    }

    /**
     * @param $auditId
     * @param $state
     * @param $extend
     * @param $isFinial
     */
    public function setProperty($auditId, $state, $extend=null, $isFinial=true)
    {
        switch ($state){
            case enums::APPROVAL_STATUS_APPROVAL:
                $budget = HRHCBudgetModel::findFirst($auditId);
                $items = $budget->getRelated('Items');
                //发邮件
                $staffInfo = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = ?1',
                    'bind' => [
                        1 => $extend['staff_id'],
                    ],
                ]);

                if ($staffInfo && $staffInfo->job_title == 443) { //HRBP Director 发送邮件 backyard抄送
                    //发邮件
                    $this->sendMail($auditId,$budget->dept_name);

                }

                if ($isFinial){
                    $budget->update(['status' => $state]);
                    foreach ($items as $item){
                        $this->setStaffing($budget->budget_month,$item->dept_id,$item->store_id,$item->job_title_id,$item->budget_count);
                    }
                }
                break;
            case enums::APPROVAL_STATUS_REJECTED:
            case enums::APPROVAL_STATUS_CANCEL:
                $budget = HRHCBudgetModel::findFirst($auditId);
                $budget->update(['status' => $state]);
                break;
        }

    }

    /**
     * @param $month
     * @param $depId
     * @param $storeId
     * @param $jobTitleId
     * @param $count
     */
    public function setStaffing($month,$depId,$storeId,$jobTitleId,$count)
    {
       
        $row = HRStaffingModel::findFirst(
            [
                'conditions'=>'dept_id=:dept_id: and store_id=:store_id: and job_title_id=:job_title_id:',
                'bind'=>[
                    'dept_id'=>$depId,
                    'store_id'=>$storeId,
                    'job_title_id'=>$jobTitleId,
                ],
            ]);
        if ($row){
            $row->update(['count' => $count]);
        }else{
            $row = new HRStaffingModel();
            $row->save([
                'dept_id'=>$depId,
                'store_id' =>$storeId,
                'job_title_id'=>$jobTitleId,
                'month'=>$month,
                'count' =>$count,
            ]);
        }
        $share_department_position = $this->getShareDepartmentPosition();
	    $share_department_position = $share_department_position['department_position'];
        //是否为共享部门
        if(isset($share_department_position[$depId])) {
	        $share_position_arr = $share_department_position[$depId] ?? [];
	        //是否为共享职位
            if(!empty($share_position_arr) && in_array($jobTitleId, $share_position_arr)) {
                $jobTitleId = array_values(array_diff($share_position_arr, [$jobTitleId]));
                //如果还存在其他的共享职位 则进行清零 因为共享部门-职位不存在网点维度 所有不查网点
                if(!empty($jobTitleId)){
	                foreach($jobTitleId as  $v){
		                $this->getDI()->get('db')->updateAsDict(
			                'hr_staffing',
			                ['count' => 0],
			                [
				                'conditions' => "dept_id = ? and job_title_id = ?",
				                'bind'       => [$depId,$v],
			                ]
		                );
	                }
	                
                }
            }
        }
        return true;
    }

    /**
     * @param $auditId
     * @param $user
     * @param $comefrom
     * @return array
     */
    public function getDetail($auditId, $user, $comefrom)
    {
        $return = [];
        $this->getDI()->get('logger')->write_log('getDetail'.$auditId, 'info');
        $budget = HRHCBudgetModel::findFirst($auditId);
        if ($budget->is_batch){
            $items = json_decode($budget->summary);
        }else{
            $items = $budget->getRelated('Items');
            $items->toArray();
        }
        $budget = $budget->toArray();
        $budget['items'] = $items;

        $return['data']['detail'] = $budget;

        return $return;
    }

    /**
     * @param $list
     * @return array
     */
    private function handleList($list)
    {
        $add_hour = $this->config->application->add_hour;
        foreach ($list as $k => $v) {
            $list[$k]['status_text'] = $this->getTranslation()->t('audit_status.'.$v['status']);

            $list[$k]['audit_time'] = !empty($v['audit_time']) ? date('Y-m-d H:i:s',strtotime($v['audit_time']) + ( $add_hour * 3600)) : '';

        }

        return $list;
    }

    /*
     * 发送邮件
     */
    public function sendMail($auditId, $directly_department) {

        if(RUNTIME == 'dev') {
            $toUsers = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ];
        } else if (RUNTIME == 'tra') {
            $toUsers = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ];
        } else if (RUNTIME == 'pro') {
            $toUsers = [
               // '<EMAIL>' //  侯曼琳 邮件需求
            ];
        } else {
            $toUsers = [];
        }

        if (empty($toUsers)) {
            return false;
        }

        $title = "HC budget request list";
        $content = $this->getMailTemplate($auditId, $directly_department);
        $ret   =  (new MailServer())->send_mail( $toUsers, $title, $content);

        $this->getDI()->get('logger')->write_log("send HCB mail to:" . implode(',', $toUsers) .', content is:' .  $content.',result is:'. $ret, 'info');
        return true;
    }

    /*
     * 邮件模版
     */
    public function getMailTemplate($auditId, $directly_department) {

        $hc_budget_items = HRHCBudgetItemModel::find([
            'conditions' => 'budget_id = ?1',
            'bind' => [
                1 => $auditId,
            ],
        ])->toArray();

        $content = "
                <tr>
                    <th>position</th>
                    <th>workplace</th>
                    <th>directly department</th>
                    <th>Employing department</th>
                    <th>Budget number</th>
                    <th>Number of employees</th>
                    <th>Number of be employed</th>
                    <th>Total Demand</th>
                </tr>";

        foreach ($hc_budget_items as $key => $value) {
            $content .= "
                <tr>
                    <th>{$value['job_title_name']}</th>
                    <th>{$value['store_name']}</th>
                    <th>{$directly_department}</th>
                    <th>{$value['dept_name']}</th>
                    <th>{$value['budget_count']}</th>
                    <th>{$value['on_job_count']}</th>
                    <th>{$value['to_be_hired_count']}</th>
                    <th>{$value['hiring_count']}</th>
                </tr>";
        }


        $table = "
            The following is the new HC budget request
            </br>     
            <table cellpadding='3' border='1' cellspacing='0' width='100%' style='font-size: 20px'>
            {$content}            
            </table>
            </br>
            The system automatically sends emails. Please do not reply.
            ";
        return $table;
    }

    /**
     * 获取HC预算汇总-部门维度
     * @param array $paramIn
     */
    public function getHcBudgetListByDep($paramIn = [])
    {
        //[1]获取传入参数
        $pageNum  = $this->processingDefault($paramIn, 'page_num',2);
        $pageSize = $this->processingDefault($paramIn, 'page_size',2);
        $jobTitle = $this->processingDefault($paramIn, 'job_title',2);
        $departmentId = $this->processingDefault($paramIn, 'department_id',2);
        $isExport = $this->processingDefault($paramIn, 'is_export',2,0);
        $month    = $this->processingDefault($paramIn, 'budget_month',1);

        $limit  = $pageSize ?? 100;
        $offset = (($pageNum ?? 1) - 1) * $limit;

        //[2]拼接条件
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('
            b.job_title_id,
            b.department_id,
            b.budget as cur_month_budget,
            b.hc_plan as cur_month_planed,
            b.employee_in_service,
            b.hc_pending,
            b.hc_demand,
            b.hc_request,
            b.budget_month,
            b.hc_new_budget as budget,
            hc_p1,
            hc_p2,
            hc_p3,
            hc_p4')
            ->from(['b'=>HrStatisticalBudgetDept::class])
            ->where('1=1');

        if (isset($jobTitle) && !empty($jobTitle)){
            $builder->andWhere('b.job_title_id = :job_title:',['job_title' => $jobTitle]);
        }

        if (isset($departmentId) && !empty($departmentId)){
            $builder->andWhere('b.department_id IN ({department_id:array})',['department_id' => $departmentId]);
        }

        if (isset($month) && !empty($month)) {
            $builder->andWhere('b.budget_month = :month:',['month' => $month]);
        }
        $builder->groupBy("b.department_id,b.job_title_id");
        $count = $builder->getQuery()->execute()->count();
        $builder->orderBy('b.updated_at desc');
        if ($isExport == 0) {
            $builder->limit($limit, $offset);
        }
        $list = $builder->getQuery()->execute()->toArray();

        //[3]记录日志并返回结果
        $this->getDI()->get("logger")->write_log("getHcBudgetListByDep, 参数:".json_encode($paramIn).'----结果：'.json_encode($list), "info");
        return $this->checkReturn(['data' => [
            'count' => $count,
            'items' => $list,
        ]]);
    }

    /**
     * 获取HC预算汇总-网点维度
     * @param array $paramIn
     */
    public function getHcBudgetListByStore($paramIn = [])
    {
        //[1]获取传入参数
        $pageNum  = $this->processingDefault($paramIn, 'page_num',2);
        $pageSize = $this->processingDefault($paramIn, 'page_size',2);
        $jobTitle = $this->processingDefault($paramIn, 'job_title',2);
        $departmentId = $this->processingDefault($paramIn, 'department_id',2);
        $storeId  = $this->processingDefault($paramIn, 'store_id',2);
        $regionId = $this->processingDefault($paramIn, 'manage_region',2);
        $pieceId  = $this->processingDefault($paramIn, 'manage_piece',2);
        $isExport = $this->processingDefault($paramIn, 'is_export',2,0);
        $month    = $this->processingDefault($paramIn, 'budget_month',1);

        $limit  = $pageSize ?? 100;
        $offset = (($pageNum ?? 1) - 1) * $limit;

        //[2]拼接条件
        $builder = $this->modelsManager->createBuilder();

        $builder->from(['b'=>HrStatisticalBudgetStore::class])->where('1=1');

        if (isset($jobTitle) && !empty($jobTitle)){
            $builder->andWhere('b.job_title_id = :job_title:',['job_title' => $jobTitle]);
        }
        if (isset($departmentId) && !empty($departmentId)){
            $builder->andWhere('b.department_id = :department_id:',['department_id' => $departmentId]);
        }
        if (isset($regionId) && !empty($regionId)){
            $builder->andWhere('b.manage_region = :manage_region:',['manage_region' => $regionId]);
        }
        if (isset($pieceId) && !empty($pieceId)){
            $builder->andWhere('b.manage_piece = :manage_piece:',['manage_piece' => $pieceId]);
        }
        if (isset($storeId) && !empty($storeId)){
            $builder->andWhere('b.store_id = :store_id:',['store_id' => $storeId]);
        }
        if (isset($month) && !empty($month)) {
            $builder->andWhere('b.budget_month = :month:',['month' => $month]);
        }

        $totalCount = $builder->columns('COUNT(1) AS total')->getQuery()->execute()->getFirst();

        $count = intval($totalCount->total);

        $builder->columns('b.manage_region,
            b.manage_piece,
            b.job_title_id,
            b.store_id,
            b.department_id,
            b.budget as cur_month_budget,
            b.employee_in_service,
            b.hc_pending,
            b.hc_demand,
            b.budget_month,
            b.hc_plan as cur_month_planed,
            b.hc_request,
            b.hc_new_budget as budget,
            hc_p1,
            hc_p2,
            hc_p3,
            hc_p4');

        $builder->orderBy('b.updated_at desc');
        if ($isExport == 0) {
            $builder->limit($limit, $offset);
        }
        $list = $builder->getQuery()->execute()->toArray();

        //[3]记录日志并返回结果
        $this->getDI()->get("logger")->write_log("getHcBudgetListByStore, 参数:".json_encode($paramIn).'----结果：'.json_encode($list), "info");
        return $this->checkReturn(['data' => [
            'count' => $count,
            'items' => $list,
        ]]);
    }

    public function getDeptIds($dept_ids) {
        $dept_list = FleSysDepartmentModel::find([
            'columns' => 'id,name,ancestry,ancestry_v3',
            'conditions' => 'id IN ({dept_ids:array}) and deleted = 0',
            'bind'       => [
                'dept_ids' => $dept_ids,
            ],
        ])->toArray();
        $dept_ids = [];
        foreach ($dept_list as $key => $value) {
            $dept = FleSysDepartmentModel::find([
                'columns' => 'id',
                'conditions' => ' ancestry_v3 like :ancestry: or id = :id: ',
                'bind' => [
                    'ancestry' => $value['ancestry_v3'].'/%',
                    'id' => $value['id'],
                ],
            ])->toArray();
            $dept_ids = array_merge($dept_ids, array_column($dept, 'id'));
        }
        return $dept_ids;
    }
}
