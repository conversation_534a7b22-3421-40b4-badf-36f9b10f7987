<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Enums\AuditDetailOperationsEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AttendanceHikStoreSettingModel;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrOutsourcingOrderModel;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffOutsourcingModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyDeviceTokenModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AttendanceDataV2Repository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\HrStaffTransferRepository;
use FlashExpress\bi\App\Repository\OsOrderRepository;
use FlashExpress\bi\App\Repository\OsStaffRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffAuditToolLog;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;

//外协员工server
class OsStaffServer extends AuditBaseServer
{
    protected   $os;
    protected   $staff;
    protected   $pub;
    protected   $auditlist;
    protected   $auditlog;
    public      $timezone;
    public      $lang;
    public      $other;
    public      $wf;
    public $sourceCategory = 0;//订单来源分类 0 默认 无分类 1 ffm 部门申请
    public $manageStores = [];//ffm 类型来源 管辖的网点

    // boat couriers 专属班次ID
    const BOAT_COURIER_SHIFT_ID = 50;

    const IS_RED = 2;//红色
    const IS_NOT_RED = 1;//非红色

    //PDC Operations
    const DEPARTMENT_ID_PDC_OPERATIONS = 1205;


    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->timezone = $timezone;
        $this->lang     = $lang;

        $this->os       = new OsStaffRepository($timezone);
        $this->staff    = new StaffRepository();
        $this->pub      = new PublicRepository();
        $this->auditlist= new AuditlistRepository($this->lang, $this->timezone);
        $this->auditlog = new StaffAuditToolLog();
        $this->wf = new WorkflowServer($this->lang, $this->timezone);
        $this->other = new OtherRepository($timezone,$lang);


        parent::__construct($this->lang);
    }

    protected $disableShiftIds = [35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49];

    public function setDisableShift(array $shiftIds): OsStaffServer
    {
        $this->disableShiftIds = $shiftIds;
        return $this;
    }

    public function getDisableShift()
    {
        return $this->disableShiftIds;
    }



    /**
     * 添加外协员工申请
     * @param array $paramIn    传入参数
     * @return mixed
     * @throws Exception
     */
    public function addOsStaff($paramIn = [])
    {
        //[1]参数定义
        $staffId         = $this->processingDefault($paramIn, 'staff_id');
        $jobId           = $this->processingDefault($paramIn, 'job_id');
        $employDate      = $this->processingDefault($paramIn, 'employment_date');
        $employDays      = $this->processingDefault($paramIn, 'employment_days');
        $shiftId         = $this->processingDefault($paramIn, 'shift_id');
        $demendNum       = $this->processingDefault($paramIn, 'demend_num');
        $reason          = $this->processingDefault($paramIn, 'reason');
        $reason          = addcslashes(stripslashes($reason), "'");
        $submit_store_id = $this->processingDefault($paramIn, 'store_id');
        $reasonType      = $this->processingDefault($paramIn, 'reason_type', 2);
        $imagePathArr    = $this->processingDefault($paramIn, 'image_path');
        $osType          = $this->processingDefault($paramIn, 'os_type', 2);
        $store_id        = $this->processingDefault($paramIn, 'organization_id');
        $out_company_id  = $this->processingDefault($paramIn, 'company_id', 2);

        $staffInfo = $this->staff->checkoutStaff($staffId);

        //由于operation部门在ms中存的是子部门,
        //并且hr中job_title与部门的关联关系是job_title与一级部门的关联而不是子部门关联
        //该位置必须查询申请人在bi系统中的部门
        $staff_info = $this->staff->getStaffInfoById($staffId);
        if ($staffInfo) {
            $department_id = $osType == enums::$os_staff_type['motorcade'] ? 32 : $staffInfo['department_id'];
        } else {
            $department_id = null;
        }

        if (empty($department_id)) {
            throw new ValidationException($this->getTranslation()->_('err_msg_do_not_have_department'));
        }

        $shift_info = $this->getShiftDetail($shiftId);
        $shift_start = '';
        $shift_end = '';
        if(!empty($shift_info)) {
            $shift_start = $shift_info['start'];
            $shift_end = $shift_info['end'];
        }

        //hub 外协 申请订单。
        $hubOsRoles = UC('outsourcingStaff')['hubOsRoles'];
        if (array_intersect($hubOsRoles, $paramIn['param']['position_category'])) {

            $employDateShiftStart = strtotime($employDate.' '.$shift_start);
            // 不可选择距离当前时间小于1小时开始的班次
            if (time() > ($employDateShiftStart - 60 * 60)) {
                throw new ValidationException($this->getTranslation()->_('shift_error'));
            }
        } else {
            $res = $this->addOsStaffValidation($staffInfo['department_id'], $staff_info['category'], $department_id, $osType,
                $jobId);
            if($res) {
                throw new ValidationException($this->getTranslation()->_('err_msg_job_title_department_dismatch'));
            }
        }

        //获取转岗审批流Code
        $extend['flow_code'] = $this->getOsStaffWorkflowRole([
            'job_id'        => $jobId,
            'os_type'       => $osType,
            'staff_id'      => $staffId,
            'department_id' => $staffInfo['department_id'],
        ]);

        //如果是车队外协需调整申请的网点
        $store_id = $osType == enums::$os_staff_type['motorcade'] ? $submit_store_id : $store_id;


        //[3]组织数据插入业务主数据

        $insertData = [
            'serial_no'       => 'OS'.$this->getRandomId(),
            'os_type'         => $osType,
            'staff_id'        => $staffId,
            'job_id'          => intval($jobId),
            'department_id'   => $department_id,
            'store_id'        => $store_id,
            'employment_date' => $employDate,
            'employment_days' => $employDays,
            'shift_id'        => $shiftId,
            'status'          => enums::$audit_status['panding'],
            'demend_num'      => $demendNum,
            'final_audit_num' => $demendNum,
            'reason_type'     => $reasonType,
            'reason'          => $reason,
            'wf_role'         => 'os_new',
            'out_company_id'  => !empty($out_company_id) ? $out_company_id : 0,
            'shift_begin_time'=> $shift_start,
            'shift_end_time'  => $shift_end,
        ];
        $osStaffId  = $this->os->insertOsStaff($insertData);
        if (empty($osStaffId)) {
            throw new Exception($this->getTranslation()->_('4008'));
        }
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            //插入业务关联图片
            if (!empty($imagePathArr)) {
                $insertImgData = [];

                foreach ($imagePathArr as $image) {
                    $insertImgData[] = [
                        'id'         => $osStaffId,
                        'image_path' => $image,
                    ];
                }
                $this->pub->batchInsertImgs($insertImgData, 'OUTSOURCING_STAFF');
            }

            //HRBP根据申请的网点查找
            $extend['store_id'] = $store_id;

            //这里是 from 表单的内容,用于查找审批人
            $extend['from_submit'] = [
                'sys_store_id' => $store_id,
                'audit_type'   => AuditListEnums::APPROVAL_TYPE_OS,
            ];

            //创建
            $server    = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($osStaffId, AuditListEnums::APPROVAL_TYPE_OS, $staffId, null, $extend);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
            $db->commit();
        } catch (ValidationException $vException) {
            $db->rollback();
            $this->deleteOs($osStaffId);
            $this->getDI()->get('logger')->write_log(['checkPersistWorkflow'=>$vException->getMessage()],'info');
            return $this->checkReturn(-3, $vException->getMessage());
        } catch (Exception $e) {
            $db->rollback();
            $this->deleteOs($osStaffId);
            $this->getDI()->get('logger')->write_log(['Exception'=>$e->getMessage()]);
            return $this->checkReturn(-3, $e->getMessage());
        }

        return $this->checkReturn([]);
    }

    /**
     * 创建审批流 失败回滚数据，需要删除
     * @param $osStaffId
     * @return mixed
     */
    public function deleteOs($osStaffId)
    {
        $db = $this->getDI()->get('db');
        $sql = " delete from `hr_staff_outsourcing` where `id`= :os_id ";
        $sql_param = [
            'os_id' => $osStaffId,
        ];
        return $db->execute($sql,$sql_param);

    }

    /**
     * 非 分拨经理 检验规则
     * @param $staff_fle_department_id
     * @param $store_category
     * @param $department_id
     * @param $osType
     * @param $jobId
     */
    public function addOsStaffValidation($staff_fle_department_id, $store_category, $department_id, $osType, $jobId)
    {
        //申请的外协类型是短期或长期外协
        //只有OS类型的网点才可以申请Onsite Officer
        //只有USHOP、SHOP(pickup-only)类型的网点才可以申请Shop Officer
        if (in_array($osType, [enums::$os_staff_type['normal'], enums::$os_staff_type['long_term']])
            &&
            (
                $jobId == enums::$job_title['onsite_officer'] && $store_category != enums::$stores_category['os']
                ||
                $jobId == enums::$job_title['shop_officer'] && !in_array($store_category, [
                    enums::$stores_category['shop_pickup_only'],
                    enums::$stores_category['shop_ushop']])
                ||
                $jobId == enums::$job_title['warehouse_staff_sorter'] && in_array($store_category, [
                    enums::$stores_category['shop_pickup_only'],
                    enums::$stores_category['shop_ushop'],
                    enums::$stores_category['os']])
            )
        ) {
            return true;
        }

        //非车队外协需要验证
        //部门下是否存在该职位
        if ($osType != enums::$os_staff_type['motorcade']) {
            $relate = $this->os->getRelation($department_id, $jobId);
            if (empty($relate)) {
                return true;
            }
        }

        //Line haul & Transportation部门的人，不可以申请普通外协、长期外协申请。
        //只可以申请[车队外协申请条件]
        //[车队外协申请条件][Line haul & Transportation部门(26)，角色为线路中控人员(31)/线路规划管理员的员工申请(32)]
        if ($staff_fle_department_id == 26 && in_array($osType, [enums::$os_staff_type['normal'], enums::$os_staff_type['long_term']])) {
            return true;
        }
        return false;
    }

    protected function getUpdateValidationJobTitle()
    {
        return [];
    }


    /**
     * 更新外协员工申请
     * @param array $paramIn
     * @return array|void
     * @throws Exception
     */
    public function updateOsStaff($paramIn = [])
    {
        //[1]获取参数
        $this->wLog('更新外协员工申请', $paramIn);
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $status  = $this->processingDefault($paramIn, 'status', 2);
        $reason  = $this->processingDefault($paramIn, 'reject_reason', 1);
        $osStaffId = $this->processingDefault($paramIn, 'audit_id', 2);
        $employmentDays = $this->processingDefault($paramIn, 'employment_days', 2);
        $approvalData = $this->processingDefault($paramIn, 'demend_num', 2);
        $reason   = addcslashes(stripslashes($reason),"'");

        //[2]校验
        //[2.1]校验审批ID是否存在
        $osStaffInfo = $this->getOsStaffDetail(['id'=>$osStaffId]);
        if (empty($osStaffInfo)) {
            throw new ValidationException($this->getTranslation()->_('4008'), enums::$ERROR_CODE['1000']);
        }

        //[2.2]校验是否已经是最终状态
        //如果是，则不能撤销
        if ($osStaffInfo['status'] != enums::$audit_status['panding'] && $status == enums::$audit_status['revoked']) {
            throw new ValidationException($this->getTranslation()->_('please try again'), enums::$ERROR_CODE['1000']);
        }

        //[2.3]校验审批人数
        if(isCountry('TH')) {
            $jobTitles = $this->getUpdateValidationJobTitle();
        } elseif(isCountry('MY')) {
            $jobTitles = [enums::$my_job_title['outsource'], enums::$my_job_title['security_outsource']];
        } else {
            $jobTitles = [];
        }
        //TH MY：outsource
        if (isset($approvalData) && $approvalData && !in_array($osStaffInfo['job_id'], $jobTitles)) {
            if ($approvalData < 1 || $approvalData > 200) {
                throw new ValidationException("'approval_data' invalid input", enums::$ERROR_CODE['1000']);
            }
        }

        //[2.4]校验天数
        $this->checkEmploymentDays($employmentDays, $osStaffInfo, $paramIn['param']['position_category']);

        //notice: 前端可能不传 demend_num
        $paramData = [
            'id'                => $osStaffInfo['id'],
            'reject_reason'     => $reason,
            //'demend_num'        => $approvalData ?? 0,
            'employment_days'   => $employmentDays,
            'final_audit_num'   => $status == enums::$audit_status['approved'] ? (isset($approvalData) && $approvalData ? $approvalData : $osStaffInfo['demend_num']) : 0,
        ];

        $sendMessageToApply = false;
        if(!empty($paramIn['out_company_list']) && $status == enums::$audit_status['approved']) {
            $outCompanyInfo = $this->updateOutCompanyData($paramIn['out_company_list'], $osStaffInfo);
            if(!empty($outCompanyInfo['out_company_data'])) {
                $paramData['out_company_data'] = $outCompanyInfo['out_company_data'];
            }

            if(!empty($outCompanyInfo['final_audit_num'])) {
                $paramData['final_audit_num'] = $outCompanyInfo['final_audit_num'];

                if($paramData['final_audit_num'] > $osStaffInfo['demend_num']) {
                    throw new ValidationException($this->getTranslation()->_('approved_number_limit'));
                }

                if($paramData['final_audit_num']!= $osStaffInfo['demend_num']) {
                    $sendMessageToApply = true;
                }
            }
        }

        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            //更新数据
            $this->getDI()->get('db')->updateAsDict(
                'hr_staff_outsourcing',
                $paramData,
               "id = " . $osStaffId
            );

            //同意或者驳回等分开处理
            if ($status == enums::$audit_status['approved']) {
                //同意
                $server = new ApprovalServer($this->lang, $this->timezone);
                $approval_res = $server->approval($osStaffId, AuditListEnums::APPROVAL_TYPE_OS, $staffId);

                //只有O
                if(!empty($approval_res) && !empty($outCompanyInfo['out_company_data']) && !empty($outCompanyInfo['final_audit_num'])) {
                    $replace_list[] = ['before_column' => 'demend_num', 'after_columns' => 'current_number'];
                    $insertParams = [
                        'audit_type' => AuditListEnums::APPROVAL_TYPE_OS,
                        'audit_value'=> $osStaffId,
                        'update_list' => [
                            'current_number' => $outCompanyInfo['final_audit_num'],
                            'replace_list' => $replace_list,
                        ],
                    ];
                    $mq = new RocketMQ('audit-list-update');
                    $mq->setType(RocketMQ::TAG_AUDIT_LIST_SUMMARY_UPDATE);
                    $rid = $mq->sendMsgByTag($insertParams,5);
                    $this->logger->write_log('updateOsStaff audit-list-update rid:' . $rid . 'data:' . json_encode($insertParams),
                        $rid ? 'info' : 'error');
                }elseif (!empty($approval_res) && !empty($paramData['final_audit_num'])){
                    $insertParams = [
                        'audit_type' => AuditListEnums::APPROVAL_TYPE_OS,
                        'audit_value'=> $osStaffId,
                        'update_list' => [
                            'demend_num' => $paramData['final_audit_num'],
                            'employment_days' => empty($employmentDays) ? 0 : $employmentDays,
                        ],
                    ];
                    $mq = new RocketMQ('audit-list-update');
                    $mq->setType(RocketMQ::TAG_AUDIT_LIST_SUMMARY_UPDATE);
                    $rid = $mq->sendMsgByTag($insertParams,5);
                    $this->logger->write_log('updateOsStaff audit-list-update 2 rid:' . $rid . 'data:' . json_encode($insertParams),
                        $rid ? 'info' : 'error');
                }

            } else if ($status == enums::$audit_status['dismissed']) {
                //驳回
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->reject($osStaffId, AuditListEnums::APPROVAL_TYPE_OS, $reason, $staffId);
            } else {
                //撤销
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->cancel($osStaffId, AuditListEnums::APPROVAL_TYPE_OS, $reason, $staffId);
            }
            $db->commit();

            if($sendMessageToApply) {
                $messageData['serial_no']  = $osStaffInfo['serial_no'];
                $this->sendMessageToApply($osStaffInfo['staff_id'], $messageData);
            }

        } catch (ValidationException|BusinessException $bv) {
            $db->rollback();
            return $this->checkReturn(-3, $bv->getMessage());
        } catch (Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log("updateOsStaff failure:" . $e->getMessage() . $e->getTraceAsString(),
                "notice");
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        return $this->checkReturn([]);
    }

    /**
     * 可以修改需求人数的职位
     * @return array
     */
    protected function getOutsourceCanEditNnumJobTitle():array
    {
        return [enums::$job_title['outsource']];
    }


    /**
     * 更新 外协申请，外协公司人数信息
     * @param $out_company_list
     * @param $osStaffInfo
     * @return array
     */
    public function updateOutCompanyData($out_company_list, $osStaffInfo)
    {
        if(empty($out_company_list) || empty($osStaffInfo['out_company_data'])) {
            return [];
        }
        if (!in_array($osStaffInfo['job_id'], $this->getOutsourceCanEditNnumJobTitle())) {
            return [];
        }

        $out_company_data_arr = json_decode($osStaffInfo['out_company_data'], true);
        $out_company_listId = array_column($out_company_list, 'final_audit_num', 'out_company_id');
        $final_audit_num = 0;
        foreach ($out_company_data_arr as $key => $oneData) {
            if (!isset($out_company_listId[$oneData['out_company_id']])) {
                continue;
            }
            if($oneData['demend_num'] < $out_company_listId[$oneData['out_company_id']]) {
                $outsourcing_company_model = new OutsourcingCompanyModel();
                $outsourcing_company       = $outsourcing_company_model->getOneById($oneData['out_company_id']);
                throw new ValidationException($this->getTranslation()->_('approved_number_cannot_exceed_apply', ['company_name' => $outsourcing_company['company_name']]));
            }

            if($out_company_listId[$oneData['out_company_id']] <= 0) {
                throw new ValidationException($this->getTranslation()->_('must_be_greater'));
            }

            $out_company_data_arr[$key]['final_audit_num'] = $out_company_listId[$oneData['out_company_id']];
            $final_audit_num = $final_audit_num + $out_company_listId[$oneData['out_company_id']];
        }

        $paramData['out_company_data'] = json_encode($out_company_data_arr, JSON_UNESCAPED_UNICODE);
        //核定人数
        $paramData['final_audit_num'] = $final_audit_num;

        return $paramData;
    }

    /**
     * [2.4]校验天数
     * @param $employmentDays
     * @param $osStaffInfo
     * @param array $position_category
     * @return bool
     * @throws Exception
     */
    public function checkEmploymentDays($employmentDays, $osStaffInfo)
    {
        //[2.4]校验天数
        if (isset($employmentDays) && $employmentDays) {
            if (in_array($osStaffInfo['os_type'], [enums::$os_staff_type['motorcade'], enums::$os_staff_type['normal']])) { //车队外协 、短期外协都是1~7天
                if ($employmentDays < 1 || $employmentDays > 7) {
                    throw new Exception("'employment_days' invalid input", enums::$ERROR_CODE['1000']);
                }
            } else { //长期外协是90到365天
                if ($employmentDays < 90 || $employmentDays > 365) {
                    throw new Exception("'employment_days' invalid input", enums::$ERROR_CODE['1000']);
                }
            }
        }
        return true;
    }

    /**
     * 获取外协员工详情
     * @param array $paramIn    传入参数
     * @return mixed
     */
    public function getOsStaffDetail($paramIn = [])
    {
        //[1]参数定义
        $osStaff_id       = $this->processingDefault($paramIn, 'id', 2);

        //[2]请求详情
        $returnData       = $this->os->getOsStaffInfo($osStaff_id);
        if ($returnData) {
            //查找网点
            $storeNames = (new SysStoreServer)->getStoreName([$returnData['store_id']]);
            $returnData['store_name'] = $storeNames[$returnData['store_id']] ?? '';
            $reasons   = array_column($this->getOsStaffReqReason($returnData['store_id']), 'title', 'code');
            $returnData['reason'] = $reasons[$returnData['reason']] ?? "";
            //查找班次
            $shiftInfo  = $this->os->getWorkShift($returnData['shift_id']);
            if (!empty($shiftInfo)) {
                if ($shiftInfo['type'] == 'EARLY') {
                    $shift = $this->getTranslation()->_('shift_early');
                } else if ($shiftInfo['type'] == 'MIDDLE') {
                    $shift = $this->getTranslation()->_('shift_middle');
                } else if ($shiftInfo['type'] == 'NIGHT') {
                    $shift = $this->getTranslation()->_('shift_night');
                } else {
                    $shift = $this->getTranslation()->_('shift_early');
                }
                $returnData['work_shift'] = $shift . ' ' . $shiftInfo['start'] . ' - ' . $shiftInfo['end'];
                $returnData['shift_name'] = $shiftInfo['start'] . ' - ' . $shiftInfo['end'];
            }
            if($returnData['out_company_id'] > 0 || !empty($returnData['out_company_data'])) {
                $returnData['part_time'] = $this->getTranslation()->_('part_time_2');
                if(in_array($shiftInfo['shift_group'], [HrShiftModel::SHIFT_GROUP_HUB_OUTSOURCE_SHIFT_4, HrShiftModel::SHIFT_GROUP_HUB_OUTSOURCE_SHIFT_5])) {
                    $returnData['part_time'] = $this->getTranslation()->_('part_time_1');
                }

                $returnData['os_shift_time'] = $returnData['employment_date'] . ' ' . $shiftInfo['start'] . ' - ' . $shiftInfo['end'];

            }

            $server = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);

            //非车队外协需要查看网点的参考数据
            if ($returnData['source_category'] != HrStaffOutsourcingModel::SOURCE_CATEGORY_FFM //非ffm 申请
                && $returnData['status'] == 1
                && $returnData['os_type'] != enums::$os_staff_type['motorcade']
                && empty($returnData['out_company_id'])
                && empty($returnData['out_company_data'])) {
                    $returnData['extend']  = $server->get_apply_form($returnData,$this->timezone);
            }
        }
        return $returnData;
    }

    /**
     * 获取外协员工信息
     * @param $data
     * @param $osDetail
     * @return mixed
     */
    public function getOsStaffDetailInfo($data, $osDetail)
    {

        //解析班次
        if ($data['staff_shift']['type'] == 'EARLY') {
            $shifit = $this->getTranslation()->_('shift_early');
        } else if ($data['staff_shift']['type'] == 'MIDDLE') {
            $shifit = $this->getTranslation()->_('shift_middle');
        } else if ($data['staff_shift']['type'] == 'NIGHT') {
            $shifit = $this->getTranslation()->_('shift_night');
        } else {
            $shifit = $this->getTranslation()->_('shift_early');
        }

        $staffList = [];
        if (isset($data['detail']) && $data['detail']) {
            foreach ($data['detail'] as $staffInfo) {
                $staffList[] = [
                    'name'  => $staffInfo['staff_name'] ?? '',
                    'phone' => $staffInfo['mobile'] ?? '',
                ];
            }
        }

        //[4]组织数据
        $returnData['config'] = [
            [
                'key'   => $this->getTranslation()->_('status'),
                'value' => $data['status_text'],
            ],
            [
                'key'   => $this->getTranslation()->_('employment_date'),
                'value' => $data['employment_date'],
            ],
            [
                'key'   => $this->getTranslation()->_('employment_days'),
                'value' => $data['employment_days'],
            ],
            [
                'key'   => $this->getTranslation()->_('work_shift'),
                'value' => $shifit . ' ' . $data['staff_shift']['start'] . ' - ' . $data['staff_shift']['end'],
            ],
            [
                'key'   => $this->getTranslation()->_('staff_assigned'),
                'value' => isset($data['detail']) ? count($data['detail']) : 0,
            ],
            [
                'key'   => $this->getTranslation()->_('staff_list'),
                'value' => $staffList,
            ],
        ];

        $returnData['audit_num'] = [
            [
                'key'   => $this->getTranslation()->_('final_num'),
                'value' => $osDetail['final_audit_num'],
            ],
        ];

        $returnData['audit_info'] = [
            [
                'key'   => $this->getTranslation()->_('audit_type'),
                'value' => $this->getTranslation()->_('7011'),
            ],
            [
                'key'   => $this->getTranslation()->_('audit_no'),
                'value' => $osDetail['serial_no'],
            ],
            [
                'key'   => $this->getTranslation()->_('submit_time'),
                'value' => date("Y-m-d H:i", strtotime($osDetail['created_at'])),
            ],
            [
                'key'   => $this->getTranslation()->_('os_staff_job_title'),
                'value' => $osDetail['job_title'],
            ],
            [
                'key'   => $this->getTranslation()->_('department'),
                'value' => $osDetail['department'],
            ],
            [
                'key'   => $this->getTranslation()->_('store'),
                'value' => $osDetail['store_name'],
            ],
            [
                'key'   => $this->getTranslation()->_('employment_date'),
                'value' => $osDetail['employment_date'],
            ],
            [
                'key'   => $this->getTranslation()->_('employment_days'),
                'value' => $osDetail['employment_days'],
            ],
            [
                'key'   => $this->getTranslation()->_('work_shift'),
                'value' => $osDetail['work_shift'],
            ],
            [
                'key'   => $this->getTranslation()->_('demend_num'),
                'value' => $osDetail['demend_num'],
            ],
            [
                'key'   => $this->getTranslation()->_('reason_app'),
                'value' => $osDetail['reason'],
            ],
            [
                'key'   => $this->getTranslation()->_('live_photo'),
                'value' => $osDetail['image_path'] ?? '',
            ],
        ];
        return $returnData;
    }

    /**
     * @param int $jobId
     * @param int $shift_group
     * @param string $employment_date
     * @return array
     */
    public function getShiftList($jobId = 0, $shift_group = HrShiftModel::SHIFT_GROUP_FULL_DAY_SHIFT, $employment_date = '')
    {
        [$condition, $bind] = $this->getCondition($jobId, $shift_group);

        $disableShiftIds = $this->getDisableShift();
        if(!empty($disableShiftIds)){
            $condition .= " AND id NOT IN ({ids:array})";
            $bind['ids'] = $disableShiftIds;
        }

        $shiftList =  HrShiftModel::find([
            'conditions' => $condition,
            'bind'       => $bind,
            'order'=>"type,start asc",
        ])->toArray();

        $current_day  = date('Y-m-d', time());
        $current_time = date('Y-m-d H:i:s', time());
        $employment_time          = date('Y-m-d H:i:s', strtotime($current_time));
        foreach ($shiftList as $k => &$item) {
            if ($item["id"] == self::BOAT_COURIER_SHIFT_ID && $jobId != enums::$job_title['boat_courier']) {
                unset($shiftList[$k]);
                continue;
            }
            
            if (isCountry('PH') && !empty($employment_date) && $current_day == $employment_date) {
                $shift_time = $current_day . ' ' . $item['start'];
                if (strtotime($employment_time) > strtotime($shift_time)) {
                    unset($shiftList[$k]);
                    continue;
                }
            }
            switch ($item['type']) {
                case 'EARLY':
                    $item['type_text'] = $this->getTranslation()->_('shift_early');
                    break;
                case 'MIDDLE':
                    $item['type_text'] = $this->getTranslation()->_('shift_middle');
                    break;
                case 'NIGHT':
                    $item['type_text'] = $this->getTranslation()->_('shift_night');
                    break;
                default:
                    break;
            }
        }
        return array_values($shiftList);
    }

    /**
     * 获取所有班次
     */
    public function getShiftListAll()
    {
        $shiftList =  HrShiftModel::find([
            'order'=>"type,start asc",
        ])->toArray();

        foreach ($shiftList as $k => &$item) {
            switch ($item['type']) {
                case 'EARLY':
                    $item['type_text'] = $this->getTranslation()->_('shift_early');
                    break;
                case 'MIDDLE':
                    $item['type_text'] = $this->getTranslation()->_('shift_middle');
                    break;
                case 'NIGHT':
                    $item['type_text'] = $this->getTranslation()->_('shift_night');
                    break;
                default:
                    break;
            }
        }
        return array_values($shiftList);
    }

    /**
     * 获取班次条件
     * @param int $jobId
     * @param $shift_group
     * @return array
     */
    public function getCondition($jobId = 0, $shift_group)
    {
        $condition = "shift_attendance_type = :shift_attendance_type: and shift_group = :shift_group:";
        $bind['shift_attendance_type'] =  HrShiftModel::SHIFT_ATTENDANCE_TYPE_FIXED;
        $bind['shift_group']           = $shift_group;

        return [$condition, $bind];
    }

    /**
     * 获取申请原因
     * @param string $storeId 网点ID
     * @return array
     */
    public function getOsStaffReqReason($storeId)
    {
        $retReason = [
            ["code" => 2, "title" => $this->getTranslation()->_('os_request_reason_resign')],
            ["code" => 3, "title" => $this->getTranslation()->_('os_request_reason_nocar')],
            ["code" => 4, "title" => $this->getTranslation()->_('os_request_reason_stopout')],
            ["code" => 5, "title" => $this->getTranslation()->_('os_request_reason_highavg')],
            ["code" => 6, "title" => $this->getTranslation()->_('os_request_reason_daily')],
            ["code" => 7, "title" => $this->getTranslation()->_('os_request_reason_big_promotion')],
            ["code" => 8, "title" => $this->getTranslation()->_('os_request_reason_special_customer')],
            ["code" => 0, "title" => $this->getTranslation()->_('os_request_reason_other')],
        ];
        if ($this->os->isStoreForaway($storeId)) {
            $res = [[
                "code"  => 1,
                "title" => $this->getTranslation()->_('os_request_reason_faraway'),
            ]];
            $retReason = array_merge($res, $retReason);
        }
        return $retReason;
    }

    /**
     * 组织表数据
     * @param string $title     表头
     * @param array  $tableData 表数据
     * @return array
     */
    public function generateTable($title, $tableData)
    {
        $returnData = [];
        if ($tableData) {
            $detail = [];
            foreach ($tableData as $k => $v) {
                $detail[] = [
                    $v['stat_date'],
                    $v['delivery_undertake'] ?? '',
                    $v['delivery_cnt'] ?? '',
                    $v['parcel_per'] ?? '',
                    $v['delivery_avg'] ?? '',
                ];
            }

            $returnData = [
                'title' => $title,
                'th'    => [
                    $this->getTranslation()->_('date'),
                    $this->getTranslation()->_('undertake_count'),
                    $this->getTranslation()->_('parcel_count'),
                    $this->getTranslation()->_('parcel_per'),
                    $this->getTranslation()->_('parcel_avg'),
                ],
                'td'    => $detail,
            ];
        }
        return $returnData;
    }



    /**
     * 外协部门
     * @return string
     */
    public function getSubcontractDepartment($storeId)
    {
        if (!$storeId){
            return false;
        }

        $store_department = $this->getStoreDepartment();

        $storeData = (new SysStoreServer())->getStoreByid($storeId);
        if ($storeData){
            $department = isset($store_department[$storeData["category"]]) ? $store_department[$storeData["category"]] :'';
        }else{
            $department = '';
        }
        return $department;
    }

    /**
     * 获取对应类型网点的部门
     */
    public function getStoreDepartment()
    {
        return $store_department = [
            "1" => "Network Management",
            "2" => "Network Management",
            "4" => "Shop Operations",
            "5" => "Shop Operations",
            "6" => "Flash home",
            "7" => "University Project",
            "8" => "Hub",
            "9" => "Shop Operations",
            "10"=> "Network Bulky",
            "11"=> "Fulfillment",
            "13"=> "Network Bulky",
        ];
    }

    /**
     * 获取请求列表
     */
    public function getRequestList($paramIn = [])
    {
        //[1]参数定义
        $staffInfo = $this->processingDefault($paramIn, 'userinfo');
        $jobId     = $this->processingDefault($paramIn, 'job_id', 2);
        $storeId   = $staffInfo['organization_id'] ?? '';
        $storeName = $staffInfo['organization_name'] ?? '';
        $type      = $staffInfo['organization_type'] ?? '';
        $jobTitle  = $staffInfo['job_title'] ?? '';
        $positions = $staffInfo['positions'] ?? [];

        //[2]获取网点详情
        $storeInfo = (new SysStoreServer())->getStoreByid($storeId);

        //hub可以申请长期外协的角色
        $hubRequestRole = UC('outsourcingStaff')['osLongPeriodRequestRole'];

        //可以申请车队外协申请的角色
        $motorcadeRequestRole = UC('outsourcingStaff')['osMotorcadeRequestRole'];

        //[3]长期外协申请条件
        // 1-HUB部门的网点经理、区域经理
        // 2-申请人是特殊网点（OS_CLD-Chilindo、OS_LAS-KA）的正主管
        // 3-OS、UShop、Shop(pickup only)
        $stores   = env('os_staff_long_term_stores', "'TH02020402','TH02030208'");
        $storeIds = explode(',', $stores);

        if (in_array($storeId, $storeIds) && $jobTitle == enums::$job_title['branch_supervisor'] ||
            $storeInfo['category'] == enums::$stores_category['hub'] && !empty(array_intersect($positions,
                array_keys($hubRequestRole))) ||
            in_array($storeInfo['category'], [
                enums::$stores_category['shop_pickup_only'],
                enums::$stores_category['shop_ushop'],
                enums::$stores_category['os'],
            ])
        ) {
            $ret['os_mode'] = enums::$os_staff_type['long_term'];
            $ret['os_type'] = [
                ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
                ["code" => 2, "title" => $this->getTranslation()->_('os_request_mode_2')],
            ];
        } else {
            if ($type == 2 && $storeId == enums::$department['Transportation'] && array_intersect($positions,
                    array_keys($motorcadeRequestRole))) { //车队外协
                $ret['os_mode'] = enums::$os_staff_type['motorcade'];
                $ret['os_type'] = [
                    ["code" => 3, "title" => $this->getTranslation()->_('os_request_mode_3')],
                ];
            } else { //短期外协
                $ret['os_mode'] = enums::$os_staff_type['normal'];
                $ret['os_type'] = [
                    ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
                ];
            }
        }

        $hubOsRoles = UC('outsourcingStaff')['hubOsRoles'];
        //hub 外协工单，分拨经理 有入口权限
        $is_out_company   = false;
        $out_company_list = [];
        if (array_intersect($hubOsRoles, $positions)) {
            //短期外协
            $ret['os_mode']   = enums::$os_staff_type['normal'];
            $ret['os_type']   = [
                ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
            ];
            $is_out_company   = true;
            $out_company_list = $this->getOutCompanyInfo();
            $this->setDisableShift([]);//分拨经理查看全部的班次
        }
        $ret['store_list'] = [];
        $department = (new DepartmentRepository())->getDepartmentNameById($staffInfo['department_id'] ?? '');

        $ret['store_name']       = $ret['os_mode'] == enums::$os_staff_type['motorcade'] ? '' : $storeName;
        $ret['department']       = $department;
        $ret['shift_info']       = $this->getShiftList($jobId);
        $ret['reason']           = $this->getOsStaffReqReason($storeId);
        $ret['is_out_company']   = $is_out_company;
        $ret['out_company_list'] = $out_company_list;

        return $ret;
    }

    /**
     * 获取外协公司信息
     * @return mixed
     */
    public function getOutCompanyInfo()
    {
        return OutsourcingCompanyModel::find([
            'conditions' => 'deleted = :deleted:',
            'bind'       => ['deleted' => OutsourcingCompanyModel::DELETED_NO],
            'columns' => 'id as company_id, company_name',
        ])->toArray();
    }

    /**
     * 获取职位列表
     * @param array $paramIn
     * @return array
     */
    public function getOsJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $osType     = $this->processingDefault($paramIn, 'type');
        $staffInfo  = $this->processingDefault($paramIn, 'userinfo');

        //[2]组织列表
        //1)短期外协职位下拉列表[全部职位]
        //2)长期外协职位下拉列表[Hub Staff|Onsite Officer|Shop Officer]
        //3)车队外协下拉列表[Van Courier]
        switch ($osType) {
            case enums::$os_staff_type['normal']: //短期外协
                $returnArr = $this->getShortTermJobTitleList(['staff_id' => $staffInfo['id']]);
                break;
            case enums::$os_staff_type['long_term']: //长期外协
                $returnArr = $this->getLongTermJobTitleList(['staff_id' => $staffInfo['id']]);
                break;
            case enums::$os_staff_type['motorcade']: //车队外协
                $returnArr = [
                    [
                        'job_id'        => enums::$job_title['van_courier'],
                        'job_title'     => 'Van Courier',
                    ],
                ];
                break;
            default:
                $returnArr = [];
                break;
        }
        return $returnArr;
    }

    /**
     * 获取今天短期外协请求列表
     */
    public function shortTermApplyS($organization_id)
    {

        $res     = $this->os->shortTermApplyR($organization_id);
        if (empty($res)){
            $ret['exist'] = 0;
            $ret['staff_ids'] = [];
        }else{
            $ret['exist'] = 1;
            $ret['staff_ids'] = array_values(array_unique(array_column($res,'staff_id')));
        }
        return $ret;
    }

    /**
     * 获取审批流角色
     * 详细见 doc/OS_workflow.md
     * @param $paramIn
     * @return string
     */
    public function getOsStaffWorkflowRole($paramIn)
    {
        //[1]获取参数
        $staffId    = $this->processingDefault($paramIn,'staff_id');
        $jobId      = $this->processingDefault($paramIn,'job_id');
        $osType     = $this->processingDefault($paramIn,'os_type', 2);
        $departmentId = $this->processingDefault($paramIn,'department_id', 2);

        //[2]获取员工详情
        $staffInfo = $this->staff->getStaffPositionv3($staffId);

        //[3]找到合适的审批流
        if ($osType == enums::$os_staff_type['normal']) { //短期外协
            if($jobId == enums::$job_title['hub_staff']) { //HUB STAFF职位
               //申请人 -> 上级 -> 30286
               $flowCode = 1;
            } else if (in_array($jobId, [enums::$job_title['bike_courier'],
                    enums::$job_title['van_courier'],
                    enums::$job_title['boat_courier'],
                    enums::$job_title['tricycle_courier'],
                    enums::$job_title['warehouse_staff_sorter']] //bike courier、van courier、boat courier、warehouse staff(sorter)职位
            )) {
                if ($departmentId == enums::$department['Fulfillment'] && $jobId == enums::$job_title['warehouse_staff_sorter']) {
                    //申请人→网点负责人→fulfillment部门负责人
                    $flowCode = 6;
                } else {
                    //申请人 -> 上级 -> 申请人所在网点对应的大区经理
                    $flowCode = 2;
                }
            } else {
                $flowCode = 2;
            }
        } else if ($osType == enums::$os_staff_type['long_term']) { //长期外协
            //申请人-> 上级 -> 所属区域的HRBP终审
            $flowCode = 7;
        } else { //车队外协申请
            //申请人-> 51290
            $flowCode = 5;
        }

        //onsite_officer、shop_officer的长期、短期外协申请审批流是一样的
        if ($jobId == enums::$job_title['onsite_officer'] && $staffInfo['category'] == enums::$stores_category['os']) {
            //申请人 -> 申请人所在网点负责人 -> 21715
            $flowCode = 4;
        } else if ($jobId == enums::$job_title['shop_officer'] && in_array($staffInfo['category'], [enums::$stores_category['shop_pickup_only'],enums::$stores_category['shop_ushop']])) {
            //申请人 -> 网点负责人 -> 20467 (Shop Operation Manager) ->17574
            $flowCode = 3;
        }

        return $flowCode;
    }

    /**
     * 获取外协员工职位列表
     * @param array $paramIn
     * @return array
     */
    public function getShortTermJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_id');

        //[2]获取申请人所在部门
        $staffInfo = $this->staff->getStaffInfoById($staffId);

        //昕哲需求 ： https://shimo.im/docs/93vWtkjkXWc6tWxG/read
        //短期外协职位列表
        //1)Network Management、Network Operations部门,职位列表：[Van Courier、Bike Courier、Warehouse Staff (Sorter)]；
        //如果有岛屿还需增加[Boat Courier]
        //2)Shop Management 职位列表
        //2.1) shop_pickup_only、shop_ushop类型网点 [Shop Officer、Warehouse Staff (Sorter)]
        //2.2) OS类型网点 [Onsite Officer、Warehouse Staff (Sorter)]
        //2.3) 其他网点 [Warehouse Staff (Sorter)]
        //3)Fulfillment  [Warehouse Staff (Sorter)]
        //3)Hub Management  [Warehouse Staff (Sorter)、Hub Staff]
        switch ($staffInfo['sys_department_id']) {
            case enums::$department['Network Management']:
            case enums::$department['Network Operations']:
            case enums::$department['Network Bulky']:
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['van_courier'],
                        'job_title'     => 'Van Courier',
                    ],
                    [
                        'job_id'        => enums::$job_title['bike_courier'],
                        'job_title'     => 'Bike Courier',
                    ],
                    [
                        'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                        'job_title'     => 'Warehouse Staff (Sorter)',
                    ],
                ];
                if ($this->os->isStoreIsland($staffInfo['sys_store_id'])) {
                    $returnArr = array_merge($returnArr, [
                        [
                            'job_id'        => enums::$job_title['boat_courier'],
                            'job_title'     => 'Boat Courier',
                        ],
                    ]);
                }
                break;
            case enums::$department['Shop Management']:
                if (in_array($staffInfo['category'], [enums::$stores_category['shop_pickup_only'], enums::$stores_category['shop_ushop']])) {
                    $returnArr     = [
                        [
                            'job_id'        => enums::$job_title['shop_officer'],
                            'job_title'     => 'Shop Officer',
                        ],
                        [
                            'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                            'job_title'     => 'Warehouse Staff (Sorter)',
                        ],
                    ];

                } else if (in_array($staffInfo['category'], [enums::$stores_category['os']])) {
                    $returnArr     = [
                        [
                            'job_id'        => enums::$job_title['onsite_officer'],
                            'job_title'     => 'Onsite Officer',
                        ],
                        [
                            'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                            'job_title'     => 'Warehouse Staff (Sorter)',
                        ],
                    ];
                } else {
                    $returnArr     = [
                        [
                            'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                            'job_title'     => 'Warehouse Staff (Sorter)',
                        ],
                    ];
                }
                break;
            case enums::$department['Fulfillment']:
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                        'job_title'     => 'Warehouse Staff (Sorter)',
                    ],
                ];
                break;
            case enums::$department['Hub Management']:
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                        'job_title'     => 'Warehouse Staff (Sorter)',
                    ],
                    [
                        'job_id'        => enums::$job_title['hub_staff'],
                        'job_title'     => 'Hub Staff',
                    ],
                ];
                break;
            default:
                $returnArr = [];
                break;
        }
        return $returnArr;
    }


    /**
     * 获取有效的外协员工列表
     * @param array $paramIn
     * @return array
     */
    public function getLongTermJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_id');

        //[2]获取申请人所在部门
        $staff_info = $this->staff->getStaffInfoById($staffId);

        //申请列表
        //1)Shop Pickup Only & Shop Ushop类型网点可以申请Shop Officer
        //2)OS类型网点可以申请Onsite Officer
        //3)HUB类型可以申请Hub Staff
        if (in_array($staff_info['category'], [enums::$stores_category['shop_pickup_only'], enums::$stores_category['shop_ushop']])) {
            $returnArr     = [
                [
                    'job_id'        => enums::$job_title['shop_officer'],
                    'job_title'     => 'Shop Officer',
                ],
            ];

        } else if (in_array($staff_info['category'], [enums::$stores_category['os']])) {
            $returnArr     = [
                [
                    'job_id'        => enums::$job_title['onsite_officer'],
                    'job_title'     => 'Onsite Officer',
                ],
            ];
        } else {
            $returnArr     = [
                [
                    'job_id'        => enums::$job_title['hub_staff'],
                    'job_title'     => 'Hub Staff',
                ],
            ];
        }
        return $returnArr;
    }

    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result = $this->getOsStaffDetail(['id' => $auditId]);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        if ($result['store_id']) {
            $storeData = SysStoreModel::findFirst([
                'conditions' => "id = :store_id:",
                'bind' => [
                    'store_id'  => $result['store_id'],
                ],
            ]);

            if ($storeData) {
                $store_department = $this->getStoreDepartment();
                $department       = isset($storeData->category) && $storeData->category
                    ? ($store_department[$storeData->category] ?? '')
                    : "";
            }
        }
        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }
        $demendNum = !empty($result['final_audit_num']) ? $result['final_audit_num']: ($result['demend_num'] ?? '');
        $hireOsType = $result['hire_os_type'];
        //组织详情数据
        $detailLists['apply_parson'] = sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? '');
        $detailLists['apply_department'] = sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? '');
        $detailLists['os_mode'] = $this->getTranslation()->_('os_request_mode_' . $result['os_type']);
        $detailLists['os_staff_job_title'] = $result['job_title'] ?? '';
        $detailLists['os_outsourcing_hire_type'] = $this->getTranslation()->_("os_outsourcing_hire_type_{$hireOsType}");//雇佣类型
        $detailLists['department'] = $department ?? '';
        $detailLists['store'] = $result['store_name'] ?? '';
        $detailLists['employment_date'] = $result['employment_date'] ?? '';
        $detailLists['employment_days'] = $result['employment_days'] ?? '';
        $detailLists['work_shift'] = $result['work_shift'] ?? '';

        $out_company_arr = [];
        if (isCountry('MY')) {
            $envService               = new SettingEnvServer();
            $workPartitionStoreIds    = $envService->getSetValFromCache('hub_store_work_partition_store_ids',
                ',');
            $workPartitionJobTitleIds = $envService->getSetValFromCache('hub_store_work_partition_job_title_ids',
                ',');
            //校验hub工作分区
            if (in_array($result['store_id'] ?? '', $workPartitionStoreIds) &&
                in_array($result['job_id'] ?? '', $workPartitionJobTitleIds)) {
                $detailLists['hub_work_partition'] = empty($result['work_partition']) ? '' : $this->getTranslation()->_('hub_work_partition_' . strtolower($result['work_partition']));
            }

            if (!empty($result['out_company_data'])){
                $out_company_data_arr = json_decode($result['out_company_data'], true);
                $out_company_ids_arr = array_column($out_company_data_arr, 'out_company_id');
                $company_data = OutsourcingCompanyModel::find([
                    'conditions' => 'id in ({company_id:array}) ',
                    'bind'       => ['company_id' => $out_company_ids_arr],
                    'columns'    => 'id,company_name',
                ]);
                $company_data = $company_data ? $company_data->toArray() : [];
                if($company_data) {
                    $company_data = array_column($company_data, 'company_name', 'id');
                }
                foreach ($out_company_data_arr as $k=>$v){
                    if (!isset($company_data[$v['out_company_id']])){
                        continue;
                    }

                    $out_company_arr[$k]['serial_num'] = $k+1;
                    $out_company_arr[$k]['out_company_id'] = $v['out_company_id'];
                    $out_company_arr[$k]['company_name'] = $company_data[$v['out_company_id']] ?? '';
                    //申请人数
                    $out_company_arr[$k]['demend_num'] = $v['demend_num'];
                    //核定人数（最终核定人数）
                    if(!empty($v['final_audit_num'])) {
                        $out_company_arr[$k]['final_audit_num'] = $v['final_audit_num'];
                    }

                    if(!empty($v['apply_update_num'])) {
                        $out_company_arr[$k]['apply_update_num'] = $v['apply_update_num'];
                    }
                }
            }
        }

        if(empty($out_company_arr)) {
            $detailLists['demend_num'] = $demendNum;
        }

        $detailLists = array_merge($detailLists,['reason_app'         => $result['reason'] ?? '',
                                                 'remark'             => $result['remark'] ?? '',
                                                 'live_photo'         => $result['image_path'] ?? '']);

        //驳回状态，需要显示驳回原因
        if ($result['status'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        $data = [
            'title'      => $this->auditlist->getAudityType(enums::$audit_type['OS']),
            'id'         => $result['id'],
            'staff_id'   => $result['staff_id'],
            'type'       => enums::$audit_type['OS'],
            'created_at' => $result['created_at'],
            'updated_at' => $result['updated_at'],
            'status'     => $result['status'],
            'is_stint'     => isCountry('TH') && $result['os_type'] ==  1 && in_array($result['job_id'],
                    [
                        enums::$job_title['van_courier'],
                        enums::$job_title['bike_courier'],
                        enums::$job_title['boat_courier'],
                        enums::$job_title['tricycle_courier'],
                    ]) ? 1 : 0,
            'status_text'=> $this->auditlist->getAuditStatus('10' . $result['status']),
            'serial_no'  => $result['serial_no'] ?? '',
            'demend_num' => $demendNum,
            'final_audit_num'   => $result['final_audit_num'],
            'employment_days'   => $result['employment_days'],
            'out_company_list'   => $out_company_arr,
        ];

        //获取待审批的审批人
        $approvalList = AuditApprovalModel::find([
            'columns' => 'approval_id',
            'conditions' => "biz_value = :value: and biz_type = :type: and state = 1 and deleted = 0",
            'bind' => [
                'type'  => enums::$audit_type['OS'],
                'value' => $auditId,
            ],
        ])->toArray();
        $approvalListArr = array_column($approvalList, 'approval_id');

        //待审批的审批人需要显示参考表
        if (in_array($user, $approvalListArr) && $result['status'] == 1) {
            $returnData['data']['os_extend'] = $result['extend'] ?? [];
        }

        if ($result['status'] == 2) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('final_num'), 'value' => $result['final_audit_num']],
            ];
        } else if ($result['status'] == 3) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('reject_reason'), 'value' => $result['reject_reason']],
            ];
        }

        //如果存在可编辑字段，需返回默认参数
        $auditShowType  = $this->getAuditDetailRequest()->getAuditShowType();
        $auditStateType = $this->getAuditDetailRequest()->getAuditStateType();
        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);

        //审批状态为待审批 && 并且查看我的待审批 && 存在可编辑字段时，
        //返回当前节点的可编辑字段
        if ($result['status'] == enums::APPROVAL_STATUS_PENDING &&
            $auditShowType . $auditStateType == '21' &&
            $approvalServer->isExistCanEditField($auditId, AuditListEnums::APPROVAL_TYPE_OS) && $result['job_id'] == enums::$my_job_title['outsource']
        ) {
            //获取可编辑字段
            $canEditField = $approvalServer->getCanEditFieldColumns($auditId, AuditListEnums::APPROVAL_TYPE_OS,
                AuditDetailOperationsEnums::RESPONSE_STRUCTURE_COLUMN_NAME);
        }


        $is_update_demend_num = false;
        if($result['status'] == enums::APPROVAL_STATUS_APPROVAL && $result['job_id'] == enums::$my_job_title['outsource'] && $result['staff_id'] == $user) {
            $number = (new SettingEnvServer())->getSetVal('os_hub_support_reducing_order_num');
            $number = empty($number) ? 2 : $number;
            $number = $number * 60;
            $currentTime = date('Y-m-d H:i:s');
            $shiftStartTime = $result['employment_date'] . ' ' . $result['shift_begin_time'];
            $shiftStartTime = date('Y-m-d H:i:00', strtotime("{$shiftStartTime} -{$number} minutes"));

            $is_update_demend_num = strtotime($currentTime) < strtotime($shiftStartTime);
        }

        // outsource 职位 $canEditField 中 存在 ：final_audit_num 字段 则 审批人展示：修改按钮
        $returnData['data']['can_edit_field'] = $canEditField ?? [];
        $returnData['data']['is_update_demend_num'] = $is_update_demend_num;

        $returnData['data']['head']   = $data;
        return $returnData;
    }

    /**
     * @description 自定义审批人审批按钮
     * @param $auditId
     * @return array
     */
    public function customiseOptions($auditId): array
    {
        return array_merge(AuditDetailOperationsEnums::BUTTON_COMMON_APPROVAL, [6,18]);
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(true,
            false,
            false,
            false,
            false,
            false);
    }

    /**
     * 获取外协公司信息
     * @param $company_id
     * @return array
     */
    public function getOutCompanyInfoOne($company_id)
    {
        $res = OutsourcingCompanyModel::findFirst([
            'conditions' => 'id = :company_id: ',
            'bind'       => ['company_id' => $company_id],
            'columns'    => 'company_name',
        ]);

        return !empty($res) ? $res->toArray() : [];
    }

    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info     = $this->getOsStaffDetail(['id' => $auditId]);
        if (!empty($info)) {
            $param = [
                [
                    'key'   => "os_staff_job_title",
                    'value' => $info['job_title'],
                ],
                [
                    'key'   => "store",
                    'value' => $info['store_name'],
                ],
                [
                    'key'   => "demend_num",
                    'value' => !empty($info['final_audit_num']) ? $info['final_audit_num'] : $info['demend_num'],
                ],
                [
                    'key'   => "employment_date",
                    'value' => $info['employment_date'] ?? '',
                ],
                [
                    'key'   => "employment_days",
                    'value' => $info['employment_days'] ?? '',
                ],
                [
                    'key'   => "os_shift_time",
                    'value' => $info['shift_name'] ?? '',
                ],
            ];
        }
        return $param ?? [];
    }

    /**
     * 设置回调属性
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $this->getDI()->get('db')->updateAsDict(
                'hr_staff_outsourcing',
                ['status' => $state],
                'id = '.$auditId
            );
            $osStaffInfo = $this->getOsStaffDetail(['id' => $auditId]);

            //获取收信人语言环境
            $lang = (new StaffServer)->getLanguage($osStaffInfo['staff_id']);
            //获取语言
            $t = $this->getTranslation($lang);

            /** 文案模板
             * 标题：[外协员工申请] 审批结果通知
             * 内容：
             * 审批编号: OS1234567890123
             * 审批结果: {审批状态}
             * 申请人数: {申请人数}
             * 批准人数: {批准人数}
             *
             * 请到审批>我的申请>已完成中查看详细信息
             */
            $state_content = $state == 2 ? $t->_('5106') : $t->_('5105');
            $content       = $t->_('os_staff_push_message_content');
            $content       = str_replace("{serial_no}", $osStaffInfo['serial_no'], $content);
            $content       = str_replace("{state}", $state_content, $content);
            $content       = str_replace("{demend_num}", $osStaffInfo['demend_num'], $content);
            $content       = str_replace("{final_audit_num}", $osStaffInfo['final_audit_num'], $content);

            $param = [
                'staff_info_id'   => $osStaffInfo['staff_id'],
                'message_title'   => $t->_('os_staff_push_message_title'),
                'message_content' => $content,
                'type'            => 11,
            ];
            $this->pub->pushAndSendMessageToSubmitter($param);

            try {
                $this->logger->write_log('>>>hr_staff_outsourcing serial no:'.$osStaffInfo['serial_no'], 'info');

                //长期、短期外协都同步hris
                if ($state == 2) {
                    $sync_params = [
                        'serial_no'       => $osStaffInfo['serial_no'],
                        'employment_days' => $osStaffInfo['employment_days'] ?? 0,
                        'final_audit_num' => $osStaffInfo['final_audit_num'] ?? 0,
                    ];
                    $this->logger->write_log([
                        'message'   => '异步同步已审批通过外协工单',
                        'redis_key' => RedisEnums::REDIS_SYNC_OS_ORDER,
                        'params'    => $sync_params,
                    ],'info');
                    $redis = $this->getDI()->get('redisLib');
                    $redis->lpush(RedisEnums::REDIS_SYNC_OS_ORDER, json_encode($sync_params));
                }
            } catch (\Exception $e) {
                $this->logger->write_log('>>>hr_staff_outsourcing'.$e->getMessage().$e->getTraceAsString());
            }
        }
    }
    /**
     * 获取审批流参数
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $auditInfo = HrStaffOutsourcingModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return false;
        }

        //获取申请人的角色
        $roles = HrStaffInfoPositionModel::find([
            'columns' => "staff_info_id, position_category",
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind' => [
                'staff_info_id'  => $auditInfo->staff_id,
            ],
        ])->toArray();
        $roles = array_column($roles, 'position_category');

        //申请人是否为申请网点的负责人
        $submitterInfo = SysStoreModel::findFirst([
            'columns' => "id, name, manager_id",
            'conditions' => "id = :store_id: and manager_id = :staff_info_id:",
            'bind' => [
                'store_id'  => $auditInfo->store_id,
                'staff_info_id' => $auditInfo->staff_id,
            ],
        ]);

        return [
            'os_type' => $auditInfo->os_type,
            'os_job_title' => $auditInfo->job_id,
            'roles' => $roles ?? [],
            'is_manager' => isset($submitterInfo) && $submitterInfo,
        ];
    }

    /**
     * 校验已经固化的审批流是否有效
     * 如果饭
     * @param $flowId
     * @param $auditId
     * @return bool
     */
    public function checkPersistWorkflow($flowId, $auditId): bool
    {
        $request = AuditApplyModel::findFirst([
            'conditions' => "biz_value = :value: and biz_type = :type:",
            'bind' => [
                'type'  => AuditListEnums::APPROVAL_TYPE_OS,
                'value' => $auditId,
            ],
        ]);
        if (empty($request)) {
            throw new \Exception('no valid data created!');
        }

        $workflowServer = new WorkflowServer($this->lang, $this->timezone);
        $appServer = new ApprovalServer($this->lang, $this->timezone);

        $flowNodes = $workflowServer->getFlowNodes($flowId);
        $currentNode = $workflowServer->getStartNode($flowId); // 开始节点

        do {
            $currentNode = $workflowServer->findNextNode($flowId, $currentNode->id, $appServer->getWorkflowParams($auditId, AuditListEnums::APPROVAL_TYPE_OS, $request->getSubmitterId()));
            $currentNode = $workflowServer->pickupNode($flowNodes, $currentNode);
            $approvals = $workflowServer->getNodeStaffIds($currentNode);
            if ($currentNode->type == enums::NODE_APPROVER && empty($approvals)) { // 审批节点 并且审批人为空

                $this->getDI()->get('logger')->write_log('checkPersistWorkflow currentNode :' . json_encode($currentNode->toArray(), JSON_UNESCAPED_UNICODE), 'info');
                throw new ValidationException($this->getTranslation()->_('wk_flow_error'));
            }
        } while ($currentNode->type != enums::NODE_FINAL);

        return true;
    }


    /**
     * @description:获取网点本月累计外协快递员使用情况
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/21 16:06
     */
    public function getThisMonthOsHappening($paramIn = [])
    {
        //[1]参数定义
        $staffInfo       = $this->processingDefault($paramIn, 'userinfo');
        $jobId           = $this->processingDefault($paramIn, 'job_id', 2);
        $submit_store_id = $this->processingDefault($paramIn, 'store_id');          //网点 id
        $osType          = $this->processingDefault($paramIn, 'os_type', 2);
        $store_id        = $this->processingDefault($paramIn, 'organization_id');
        $server          = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);

        return $server->get_this_month_os_happening($jobId, $store_id, $submit_store_id, $osType, $this->timezone,
            $staffInfo['positions']);
    }


    /**
     * @description:提供3 天的外协使用风险提示
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/21 16:06
     */
    public function getOsRiskPrompt($paramIn=[]){
        //[1]参数定义
//        $staffInfo  = $this->processingDefault($paramIn, 'userinfo');
        $jobId = $this->processingDefault($paramIn, 'job_id', 2);
        $submit_store_id = $this->processingDefault($paramIn, 'store_id');          //网点 id
        $osType     = $this->processingDefault($paramIn,'os_type', 2);
        $store_id   = $this->processingDefault($paramIn,'organization_id');
        $server = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);

        return  $server->get_os_risk_prompt($jobId,$store_id,$submit_store_id,$osType,$this->timezone);

    }


    /**
     * @description:获取外协雇佣类型
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/14 17:15
     */
    public function getHireOsTypeList(){
        //获取可以选择外协雇佣类型的job_ids
        $envModel                = new SettingEnvServer();
        $hire_list               = $envModel->getSetValFromCache('os_outsourcing_hire_job_ids');
        $hire_list               = explode(',', $hire_list);
        $HIRE_OS_TYPE_1          = HrStaffOutsourcingModel::HIRE_OS_TYPE_1; //普通外协
        $HIRE_OS_TYPE_2          = HrStaffOutsourcingModel::HIRE_OS_TYPE_2; //众包外协
        $result= [
            'is_optional_job_list' => $hire_list,
            'hire_type_list'       => [
                ['value' => $HIRE_OS_TYPE_1, 'label' => $this->getTranslation()->_("os_outsourcing_hire_type_{$HIRE_OS_TYPE_1}")],
                ['value' => $HIRE_OS_TYPE_2, 'label' => $this->getTranslation()->_("os_outsourcing_hire_type_{$HIRE_OS_TYPE_2}")],
            ],
            'hire_type_default' => $HIRE_OS_TYPE_1,
        ];
        return $this->checkReturn(['data'=>$result]);

    }

    /**
     * 获取网点员工维度的数据
     * @param $store_id
     * @param $dateList
     * @param $formal
     * @return mixed
     * @throws BusinessException
     */
    public function getStaffStatisticsDataFromFbi($store_id,$dateList,$formal)
    {
        
        $fle_rpc = (new ApiClient("ard_api",'','deliverycount.get_delivery_count_staff', $this->lang));
        $param['store_id'] = $store_id;
        $param['date_at'] = $dateList;
        $param['formal'] = $formal;
        $fle_rpc->setParams($param);
        $res = $fle_rpc->execute();
        if(isset($res['error'])){
            throw new BusinessException($res['error']);
        }
        return $res['result']['data'];
    }

    /**
     * 获取网点维度的揽派统计数据
     * @param $store_id
     * @param $dateList
     * @return mixed
     * @throws BusinessException
     */
    public function getStoreStatisticsDataFromFbi($store_id, $dateList)
    {

        $fle_rpc = new ApiClient("ard_api",'','deliverycount.dc_increment', $this->lang);
        $param['store_id'] = $store_id;
        $param['date_at'] = $dateList;
        $fle_rpc->setParams($param);
        $res = $fle_rpc->execute();
        if(isset($res['error'])){
            throw new BusinessException($res['error']);
        }
        return $res['result']['data'];
    }


    /**
     *
     * @param $day_array
     * @param $store_id
     * @param $category
     * @param int $type
     * @return array
     * @throws BusinessException
     */
    public function calculationHumanEffect($day_array, $store_id, $category , int $type = 1): array
    {
        //网点类型的达标线
        $storeCategory = [
            enums::$stores_category['sp']  => [
                ['condition' => '$x<=1', 'line' => 100], ['condition' => '$x > 1 && $x<=1.5', 'line' => 90], ['condition' => '$x > 1.5', 'line' => 80],
            ],
            enums::$stores_category['bdc'] => [
                ['condition' => '$x<=1', 'line' => 80], ['condition' => '$x > 1 && $x<=1.5', 'line' => 70], ['condition' => '$x > 1.5', 'line' => 60],
            ],
        ];

        //获取周期内网点每日 正式快递员妥投量
        $dataFormalPVList = $this->getStaffStatisticsDataFromFbi($store_id,$day_array,1);
        $dataFormalPVList = array_column($dataFormalPVList, 'delivery_count', 'date_at');


        //获取周期内网点每日出勤的 正式快递员人数
        $dataFormalAtList = AttendanceDataV2Repository::getStoreStaffSumAttendanceTime($store_id,[enums::$job_title['van_courier'], enums::$job_title['bike_courier']],$day_array);
        $dataFormalAtList = array_column($dataFormalAtList, 'total', 'stat_date');

        $storeStatisticsData = $this->getStoreStatisticsDataFromFbi($store_id, $day_array);

        //获取网点周期内的每日 揽件量
        $storeDataPQtList = array_column($storeStatisticsData, 'pickup_operation_count', 'date_at');
        //获取网点周期内的每日 派件量  => 当日应妥投件量
        $storeDataDAtList = array_column($storeStatisticsData, 'count_day_signed_num', 'date_at');
        //获取网点周期内的 绝对妥投率
        $storeDataASRateList = array_column($storeStatisticsData, 'actual_signed_rate', 'date_at');

        if($type == 1){
            //周期内每日外协快递员人数
            $dataOStList = $this->getStaffStatisticsDataFromFbi($store_id,$day_array,0);
            $dataOStList = array_column($dataOStList, 'staff_num', 'date_at');
        }

        $returnData  = [];
        registerStream();
        foreach ($day_array as $k => $v) {
            $returnData[$v]['store_delivery_count'] = $dataFormalPVList[$v]??0;
            //每日出勤的正式快递员人数
            $returnData[$v]['formal_at'] = $dataFormalAtList[$v] ?? 0;
            //绝对妥投率
            $returnData[$v]['actual_signed_rate'] = $storeDataASRateList[$v]?? 0;
            //当日应妥投件量
            $returnData[$v]['count_day_signed_num'] = $storeDataDAtList[$v]??0;
            //当日数据
            //  2. 网点正式快递员派件人效：网点当日正式快递员妥投量/当日出勤的正式快递员人数
            $returnData[$v]['formal_human_effect'] = isset($dataFormalAtList[$v] ) &&  $dataFormalAtList[$v] > 0 ?floatval( bcdiv($dataFormalPVList[$v]??0, $dataFormalAtList[$v], 2)) :0;
            //揽件量/派件量
            $x = $returnData[$v]['package_dispatch'] =  empty($storeDataPQtList[$v]) || empty($storeDataDAtList[$v])  ? 0 : floatval(bcdiv($storeDataPQtList[$v], $storeDataDAtList[$v], 2));
            $returnData[$v]['compliance_line'] = 0;
            if (isset($storeCategory[$category])) {
                foreach ($storeCategory[$category] as $key => $val) {
                    $result = include 'var://<?php return ' . $val['condition'] . ';';
                    if ($result === true) {
                        //网点正式快递员派件人效达标线
                        $returnData[$v]['compliance_line'] = $val['line'];
                        break;
                    }
                }
            }

            if($type == 1) {
                //1. 低效外协人次：网点当日正式快递员派件人效<网点当日正式快递员派件人效达标线时的 外协快递员人数
                $returnData[$v]['inefficient_os_num'] = $returnData[$v]['formal_human_effect'] < $returnData[$v]['compliance_line'] && isset($dataOStList[$v]) ? $dataOStList[$v] : 0;
                //外协人次
                $returnData[$v]['os_num'] = $dataOStList[$v] ?? "/";
            }
            //正式快递员妥投量 -- 没用到
            $returnData[$v]['formal_pv'] = $dataFormalPVList[$v] ?? "/";
            //揽件量
            $returnData[$v]['formal_pq'] = $storeDataPQtList[$v] ?? "/";
            //派件量
            $returnData[$v]['formal_da'] = $storeDataDAtList[$v] ?? "/";
            //日期
            $returnData[$v]['date_val'] = $v;
        }
        $this->getDI()->get('logger')->write_log(['calculationHumanEffect'=>$returnData], "info");
        return $returnData;
    }


    /**
     * @description:获取获取外协员工审批参考信息
     * @param $store_id  int //网点 id
     * @param $day_array array //日期数组
     * @return    array // 绝对妥投率/应派件量/查询出勤快递员/周期内在职的快递员
     * <AUTHOR> L.J
     * @time       : 2021/7/22 21:06
     */
    public function getOsStaffReferInfoNew($store_id, array $day_array): array
    {
        if(empty($store_id) || empty($day_array)){
            return [];
        }
        $returnData=[];
        //正式快递员出勤率
        //查询出勤快递员包含 van  bike  boat
        $dataFormalAtList = AttendanceDataV2Repository::getStoreStaffCountAttendanceTime($store_id,[ enums::$job_title['van_courier'] , enums::$job_title['bike_courier'],enums::$job_title['boat_courier']],$day_array);
        $returnData['formalAtList'] = array_column($dataFormalAtList, 'total', 'stat_date');
        //查询周期内在职的快递员
        $dataStaffTransferList = HrStaffTransferRepository::getStoreStaffOnJobNum($store_id,[ enums::$job_title['van_courier'] , enums::$job_title['bike_courier'],enums::$job_title['boat_courier']],$day_array);
        $returnData['staffTransferList'] = array_column($dataStaffTransferList, 'total', 'stat_date');
        return $returnData;
    }

    //----------------- country 迁移出来的 泰国 的方法 放在外层
    //https://flashexpress.feishu.cn/docx/BmGcdICGaoJRlixLtKWcxiyYnzh
    //17417
    /**
     * 获取审批详情的 tbl 列表
     * @param $returnData
     * @param $timezone
     * @return array
     * @throws BusinessException
     */
    public function get_apply_form($returnData, $timezone)
    {
        $returnData['extend'] = [];
        if ($returnData['store_id'] != -1 && !empty($returnData['store_id'])) {
            //todo shop officer 得 请求fbi开关
            $isOpen = (new SettingEnvServer())->getSetVal('shop_human_efficiency_data');
            if($returnData['job_id'] == enums::$job_title['shop_officer'] && !empty($isOpen)) {
                $efficiencyData = $this->getShopHumanEfficiencyData($returnData);
            } else {
                $efficiencyData = $this->getHumanEfficiencyData($returnData);
            }
        }
        $retData                                    = $efficiencyData['list_data'] ?? [];
        $retData['van_cnt']                         = $efficiencyData['van_cnt'] ?? 0;
        $retData['bike_cnt']                        = $efficiencyData['bike_cnt'] ?? 0;
        $retData['dc_officer_cnt']                  = $efficiencyData['dc_officer_cnt'] ?? 0;
        $retData['assistant_branch_supervisor_cnt'] = $efficiencyData['assistant_branch_supervisor_cnt'] ?? 0;
        $retData['soles_cnt']                       = $efficiencyData['soles_cnt'] ?? 0;

        array_push($returnData['extend'], $retData);
        return $returnData['extend'];
    }


    /**
     * 获取网点工作情况
     * @param $returnData
     * @return mixed
     * @throws BusinessException
     */
    public function getHumanEfficiencyData($returnData)
    {
        $os                   = new OsStaffRepository($this->timeZone);
        $data = [];
        //新的近 7 天工作情况
        $start_time = strtotime(date("Y-m-d", strtotime("-7 day")));
        $end_time   = strtotime(date("Y-m-d", strtotime("-1 day")));
        $onJobStaff = $os->getStoreOnJobStaffNum($returnData['store_id'], [
            enums::$job_title['dc_officer'],
            enums::$job_title['van_courier'],
            enums::$job_title['bike_courier'],
            enums::$job_title['assistant_branch_supervisor'],
        ]);

        //在职van bike人数
        $van_cnt                         = $onJobStaff[enums::$job_title['van_courier']] ?? 0;
        $bike_cnt                        = $onJobStaff[enums::$job_title['bike_courier']] ?? 0;
        $dc_officer_cnt                  = $onJobStaff[enums::$job_title['dc_officer']] ?? 0;
        $assistant_branch_supervisor_cnt = $onJobStaff[enums::$job_title['assistant_branch_supervisor']] ?? 0;
        //在职仓管人数
        $soles_cnt = $os->getCourierSoles($returnData['store_id'], 2);

        //获取网点类型
        $storeInfo = (new SysStoreServer())->getStoreByid($returnData['store_id']);
        $dates     = DateHelper::DateRange($start_time, $end_time);
        //查询正式快递员人效 和 揽件量
        $calculationHumanEffectList = $this->calculationHumanEffect($dates, $returnData['store_id'], $storeInfo['category'], 2);
        //查询绝对妥投  应派件 正式快递员
        $getOsStaffReferInfoNewList = $this->getOsStaffReferInfoNew($returnData['store_id'], $dates);

        foreach ($dates as $date) {
            $data[] = [
                //日期
                'stat_date'              => [
                    'num'    => date("m/d", strtotime($date)),
                    'is_red' => 1,
                ],
                //正式快递员人效 和是否显示红色 1 不显示 2 显示
                'formal_human_effect'    => [
                    'num'    => $calculationHumanEffectList[$date]['formal_human_effect'] ?? 0,
                    'is_red' => $calculationHumanEffectList[$date]['compliance_line'] > $calculationHumanEffectList[$date]['formal_human_effect'] ? 2 : 1,
                ],
                //绝对投妥率
                'absolute_delivery_rate' => [
                    'num'    => $calculationHumanEffectList[$date]['actual_signed_rate'] ?? '/',
                    'is_red' => 1,
                ],
                //揽件量
                'formal_pq'              => [
                    'num'    => $calculationHumanEffectList[$date]['formal_pq'] ?? '/',
                    'is_red' => 1,
                ],
                //应派件量
                'amount_to_dispatched'   => [
                    'num'    => $calculationHumanEffectList[$date]['count_day_signed_num'] ?? '/',
                    'is_red' => 1,
                ],
                //快递员出勤率
                'attendance_formal'      => [
                    'num'    => isset($getOsStaffReferInfoNewList['formalAtList'][$date]) && isset($getOsStaffReferInfoNewList['staffTransferList'][$date]) ? bcmul(bcdiv($getOsStaffReferInfoNewList['formalAtList'][$date],
                        $getOsStaffReferInfoNewList['staffTransferList'][$date], 3), 100,2) : '/',
                    'is_red' => 1,
                ],
            ];
        }
        $retData['list_data'] = $this->generateTableNew($this->getTranslation()->_('last_7_days_parcel'),
            $this->getTranslation()->_('note_formal_courier_dispatch'), $data);

        $retData['van_cnt']                         = $van_cnt ?? 0;
        $retData['bike_cnt']                        = $bike_cnt ?? 0;
        $retData['dc_officer_cnt']                  = $dc_officer_cnt ?? 0;
        $retData['assistant_branch_supervisor_cnt'] = $assistant_branch_supervisor_cnt ?? 0;
        $retData['soles_cnt']                       = $soles_cnt ?? 0;

        return $retData;
    }

    /**
     * 获取 shop_officer 职位 网点的 工作情况
     * @param $returnData
     * @return mixed
     * @throws BusinessException
     */
    public function getShopHumanEfficiencyData($returnData)
    {
        $data = [];
        //新的近 14 天工作情况
        $startDate = date("Y-m-d", strtotime("-14 day"));
        $endDate = date("Y-m-d", strtotime("-1 day"));
        $start_time = strtotime($startDate);
        $end_time   = strtotime($endDate);
        $dates     = DateHelper::DateRange($start_time, $end_time);

        //获取网点指定日期 全部在职+停职+待离职的网点编制员工人数
        $staffNum = HrStaffTransferRepository::getStoreStaffNum($returnData['store_id'], $dates);
        $staffNumToDate = !empty($staffNum) ? array_column($staffNum, 'total', 'stat_date') : [];

        //从 fbi 获取 揽件量 和 出勤人数
        $storeStatisticData = ArdApiServer::getShopStoreStatisticsData($returnData['store_id'], $startDate, $endDate);
        $storeStatisticDataToDate = !empty($storeStatisticData) ? array_column($storeStatisticData, NULL, 'stat_date') : [];

        foreach ($dates as $date) {
            //揽件量
            $quantity_of_packages = isset($storeStatisticDataToDate[$date]['total']) ? $storeStatisticDataToDate[$date]['total'] : 0;
            //出勤人数
            $attendance_count = isset($storeStatisticDataToDate[$date]['attendance_count']) ? $storeStatisticDataToDate[$date]['attendance_count'] : 0;

            //网点人数=网点全部在职+停职+待离职的网点编制员工人数
            $store_num = $staffNumToDate[$date] ?? 0;

            //网点人效 = 揽件量/FBI-SHOP&U_project Dashboard监控【出勤人数】
            $store_efficiency = (intval($attendance_count) > 0) ? round(intval($quantity_of_packages) / intval($attendance_count), 2) : 0;

            //出勤率%=FBI-SHOP&U_project Dashboard监控【出勤人数】 / 网点人数
            $attendance_late =  (intval($store_num) > 0) ? round(intval($attendance_count) / intval($store_num) * 100, 2) : 0;

            $data[] = [
                //日期
                'stat_date'              => [
                    'num'    => date("m/d", strtotime($date)),
                    'is_red' => 1,
                ],
                //正式快递员人效 和是否显示红色 1 不显示 2 显示
                // 揽件量=FBI-SHOP&U_project Dashboard监控【当日揽件量】
                'quantity_of_packages'    => [
                    'num'    => $quantity_of_packages,
                    'is_red' => self::IS_NOT_RED,
                ],
                //网点人数
                'store_num' => [
                    'num'    => $store_num,
                    'is_red' => self::IS_NOT_RED,
                ],
                //网点人效  网点人效<350：此数值标红
                'store_efficiency'              => [
                    'num'    => $store_efficiency,
                    'is_red' => $store_efficiency < 350 ? self::IS_RED : self::IS_NOT_RED,
                ],
                //出勤率  出勤率<90%：此数值标红
                'attendance_rate'   => [
                    'num'    => $attendance_late,
                    'is_red' => $attendance_late < 90  ? self::IS_RED : self::IS_NOT_RED,
                ],
            ];
        }

        $retData['list_data'] = $this->generateShopTableNew($this->getTranslation()->_('last_14_days_parcel'),
            $this->getTranslation()->_('note_formal_store_dispatch'), $data);

        return $retData;
    }

    /**
     * 组织shop表数据
     * @param string $title     表头
     * @param $title_two
     * @param array  $tableData 表数据
     * @return array
     */
    public function generateShopTableNew($title, $title_two, $tableData)
    {
        $returnData = [];
        if ($tableData) {
            $detail = [];
            foreach ($tableData as $k => $v) {
                $detail[] = [
                    $v['stat_date'],
                    $v['quantity_of_packages'],
                    $v['store_num'],
                    $v['store_efficiency'],
                    $v['attendance_rate'],
                ];
            }

            $returnData = [
                'title'     => $title,
                'title_two' => $title_two,
                'th'        => [
                    $this->getTranslation()->_('date'),//日期
                    $this->getTranslation()->_('quantity_of_packages'),//揽件量
                    $this->getTranslation()->_('store_num'),//网点人数
                    $this->getTranslation()->_('store_efficiency'),//网点人效
                    $this->getTranslation()->_('store_attendance_rate'),//出勤率%
                ],
                'td'        => $detail,
            ];
        }
        return $returnData;
    }


    /**
     * 组织表数据
     * @param string $title     表头
     * @param $title_two
     * @param array  $tableData 表数据
     * @return array
     */
    public function generateTableNew($title, $title_two, $tableData)
    {
        $returnData = [];
        if ($tableData) {
            $detail = [];
            foreach ($tableData as $k => $v) {
                $detail[] = [
                    $v['stat_date'],
                    $v['formal_human_effect'],
                    $v['absolute_delivery_rate'],
                    $v['formal_pq'],
                    $v['amount_to_dispatched'],
                    $v['attendance_formal'],
                ];
            }

            $returnData = [
                'title'     => $title,
                'title_two' => $title_two,
                'th'        => [
                    $this->getTranslation()->_('date'),//日期
                    $this->getTranslation()->_('formal_courier_dispatch'),//正式快递员派件人效
                    $this->getTranslation()->_('absolute_delivery_rate'),//绝对妥投率
                    $this->getTranslation()->_('undertake_count'),//揽件量
                    $this->getTranslation()->_('parcel_count'),//应派件量
                    $this->getTranslation()->_('formal_employee_attendance'),//正式员工出勤率
                ],
                'td'        => $detail,
            ];
        }
        return $returnData;
    }


    public function get_this_month_os_happening($jobId, $store_id, $submit_store_id, $osType, $timezone, $roles = [])
    {
        $this->os = new OsStaffRepository($timezone);
        //如果是车队外协需调整申请的网点
        $store_id = $osType == enums::$os_staff_type['motorcade'] ? $submit_store_id : $store_id;

        $returnData = [
            'is_remind' => 1,  //1 不需要提醒 2 需要提醒
            'info'      => [
                'store_name'         => '',
                'os_num'             => ['num' => "0", 'is_remind' => 1], //累计外协人次
                'inefficient_os_num' => ['num' => "0", 'is_remind' => 1], //累计低效外协
                'inefficient_os_pe'  => ['num' => "0%", 'is_remind' => 1],//低效外协占比
            ],
        ];

        //外协，分拨经理，角色 不提醒。
        $hubOsRoles = UC('outsourcingStaff')['hubOsRoles'];
        //hub 外协工单，分拨经理 有入口权限
        if (array_intersect($hubOsRoles, $roles)) {
            return $returnData;
        }

        //需要校验的职位  van_courier
        $jobList = [enums::$job_title['van_courier'], enums::$job_title['bike_courier']];
        //需要校验的 网点类型
        $storesCategory = [enums::$stores_category['bdc'], enums::$stores_category['sp']];
        //[2]获取网点详情
        $storeInfo = (new SysStoreServer())->getStoreByid($store_id);
        if (empty($storeInfo) || !in_array($storeInfo['category'], $storesCategory) || !in_array($jobId, $jobList)) {
            return $returnData;
        }
        $returnData['is_remind'] = 2;
        //网点名称
        $returnData['info']['store_name'] = $storeInfo['name'];
        $infoData                         = $this->thisMonthOsHappening($store_id, $storeInfo['category']);
        //累计外协人次
        $returnData['info']['os_num'] = $infoData['os_num'];
        //累计低效外协
        $returnData['info']['inefficient_os_num'] = $infoData['inefficient_os_num'];
        //低效外协占比
        $returnData['info']['inefficient_os_pe'] = $infoData['inefficient_os_pe'];
        return $returnData;
    }

    /**
     * @description:获取网点本月累计外协快递员使用情况
     * @param $store_id 网点 id
     * @param $category 网点类型
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/22 10:42
     */
    public function thisMonthOsHappening($store_id, $category)
    {

        //获取本月的日期
        $returnData = [
            'os_num'             => ['num' => "0", 'is_remind' => 1],//累计外协人次
            'inefficient_os_num' => ['num' => "0", 'is_remind' => 1],//累计低效外协
            'inefficient_os_pe'  => ['num' => "0%", 'is_remind' => 1],//低效外协占比
        ];
        $end_time          =strtotime(date("Y-m-d",strtotime("-1 day")));   //获取截止时间
        $start_time = strtotime(date('Y-m-01'));  //获取本月第一天时间戳
        //如果是 1 号的话
        //不包含申请当天的数据
        if ($end_time < $start_time) {
            return $returnData;
        }
        $day_array = DateHelper::DateRange($start_time, $end_time);
        //获取数据
        $indoData = $this->calculationHumanEffect($day_array, $store_id, $category);
        //查询累计外协人次
        $returnData['os_num']['num'] = array_sum(array_column($indoData, 'os_num', 'date_val'));
        //获取  2. 累计低效外协：当月累计低效外协人次
        $returnData['inefficient_os_num']['num'] = array_sum(array_column($indoData, 'inefficient_os_num', 'date_val'));

        //当累计低效外协数字大于8，增加警示icon；
        $returnData['inefficient_os_num']['is_remind'] = $returnData['inefficient_os_num']['num'] > 8 ? 2 : 1;
        //低效外协占比
        $returnData['inefficient_os_pe']['num'] = empty($returnData['os_num']['num']) || empty($returnData['inefficient_os_num']['num']) ? '0' : bcmul(bcdiv($returnData['inefficient_os_num']['num'], $returnData['os_num']['num'], 3), 100);
        //当低效外协占比数字大于24%，增加警示icon；
        $returnData['inefficient_os_pe']['is_remind'] = $returnData['inefficient_os_pe']['num'] > 24 ? 2 : 1;
        $returnData['inefficient_os_pe']['num']       = $returnData['inefficient_os_pe']['num'] . " %";
        return $returnData;
    }

    public function get_os_risk_prompt($jobId, $store_id, $submit_store_id, $osType, $timezone)
    {
        
        //如果是车队外协需调整申请的网点
        $store_id = $osType == enums::$os_staff_type['motorcade'] ? $submit_store_id : $store_id;

        $returnData = [
            'is_remind' => 1,//1 不需要提醒 2 需要提醒
            'info_list' => [//返回 ['date_val'='6/09','compliance_line'=>100,'formal_human_effect'=>10];  //达标线   正式员工派件人效
            ],
            'x'         => 0,//没有达标的天数
        ];
        //需要校验的职位  van_courier
        $jobList = [enums::$job_title['van_courier'], enums::$job_title['bike_courier']];
        //需要校验的 网点类型
        $storesCategory = [enums::$stores_category['bdc'], enums::$stores_category['sp']];
        //[2]获取网点详情
        $storeInfo = (new SysStoreServer())->getStoreByid($store_id);
        if (empty($storeInfo) || !in_array($storeInfo['category'], $storesCategory) || !in_array($jobId, $jobList)) {
            return $returnData;
        }

        //获取前3 天的日期
        $end_time   = strtotime(date("Y-m-d", strtotime("-1 day")));   //获取截止时间
        $start_time = strtotime(date("Y-m-d", strtotime("-3 day")));   //获取开始时间
        $day_array  = DateHelper::DateRange($start_time, $end_time);
        $x          = 0;//未达标次数
        //获取数据
        $indoData = $this->calculationHumanEffect($day_array, $store_id, $storeInfo['category'], 2);
        foreach ($indoData as $k => $v) {
            $returnData['info_list'][] = [
                'date_val'            => date("m/d", strtotime($v['date_val'])),
                'compliance_line'     => $v['compliance_line'],
                'formal_human_effect' => $v['formal_human_effect'],
            ];
            $x                         = $v['compliance_line'] > $v['formal_human_effect'] ? $x + 1 : $x;
        }
        $returnData['x']         = $x;
        $returnData['is_remind'] = $returnData['x'] > 0 ? 2 : 1;
        return $returnData;

    }


    // -----------------country 迁移 结束-------------


    /**
     * 外协员工申请班次时长
     * @return array[]
     */
    public function getShiftDuration(): array
    {
        return [
            ['shift_duration' => '5', 'shift_duration_text' => '5h'],
            ['shift_duration' => '6', 'shift_duration_text' => '6h'],
            ['shift_duration' => '7', 'shift_duration_text' => '7h'],
            ['shift_duration' => '8', 'shift_duration_text' => '8h'],
            ['shift_duration' => '9', 'shift_duration_text' => '9h'],
        ];
    }

    /**
     * 班次详情
     * @param $shift_id
     * @return array
     */
    public function getShiftDetail($shift_id): array
    {
        $detail = HrShiftModel::findFirst([
            'conditions' => "id = :id:",
            'bind' => ['id' => $shift_id],
        ]);
        return !empty($detail) ? $detail->toArray() : [];
    }


    public function getFFMStoreList()
    {
        return SysStoreModel::find([
            'columns'    => 'id as store_id, name as store_name',
            'conditions' => 'category = :category: and use_state = :use_state:',
            'bind'       => [
                'use_state' => SysStoreModel::USE_STATE_YES,
                'category' => enums::$stores_category['ffm'],
            ],
        ])->toArray();
    }


    //ffm部门 申请权限 https://flashexpress.feishu.cn/docx/CAlBdCA7poB0ynxA5mIckZH9nKh
    public function ffmPermission($param)
    {
        $staffId = $param['staff_id'];
        //网点经理
        $roleInfo = HrStaffInfoPositionModel::findFirst([
            'conditions' => "staff_info_id = :staff_id: and position_category = :position_category:",
            'bind'       => [
                'staff_id'          => $staffId,
                'position_category' => RolesModel::ROLE_OUTLET_MANAGER,
            ],
        ]);
        if (empty($roleInfo)) {
            return false;
        }
        return $this->isFFMStaff($staffId);
    }

    protected function isFFMStaff($staffId): bool
    {
        //Thailand Fulfillment[243]及下级
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $staffId],
        ]);
        if (empty($staffInfo)) {
            return false;
        }
        $staffInfo = $staffInfo->toArray();
        return $staffInfo['sys_department_id'] == enums::FFM_DEPARTMENT;

    }

    /**
     * 获取工号管辖网点
     * @param $staff_info_id
     * @return array|\Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getStaffManageStore($staff_info_id) {
        $staff_stores = HrStaffManageStoreModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = 1',
            'bind'       => ['staff_info_id' => $staff_info_id],
            'columns'    => 'staff_info_id,store_id',
        ])->toArray();
        $store_list = [];
        if(!empty($staff_stores)) {
            $store_ids = array_column($staff_stores, 'store_id');
            $store_list = SysStoreModel::find([
                'columns' =>'id as store_id, name as store_name',
                'conditions' => "id in ({store_ids:array}) and state = 1",
                'bind' => [
                    'store_ids' => $store_ids,
                ],
            ])->toArray();
        }
        return $store_list;

    }

    /**
     * 获取工号管辖网点--根据oa 组织架构找
     * @param $staff_info_id
     * @return array|\Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getStaffManagePdcStore($staff_info_id)
    {
        $store_ids = (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getManageStoreByOaOrg($staff_info_id);

        $store_list = [];
        if(!empty($store_ids)) {


            $hikPdcStore = $this->getPdcHikStore();
            if(empty($hikPdcStore)) {
                return [];
            }
            $hikPdcStoreIds = array_column($hikPdcStore, 'id');

            $store_list = SysStoreModel::find([
                'columns' =>'id as store_id, name as store_name',
                'conditions' => "id in ({store_ids:array}) and id in ({hik_store_id:array}) and state = 1",
                'bind' => [
                    'store_ids' => $store_ids,
                    'hik_store_id' => $hikPdcStoreIds,
                ],
            ])->toArray();
        }
        return $store_list;
    }

    /**
     * 获取hik配置中的PDC网点
     * @return mixed
     */
    public function getPdcHikStore()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('ss.id');
        $builder->from(['ahss' => AttendanceHikStoreSettingModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'ss.id = ahss.flash_store_id AND ss.state = 1', 'ss');
        $builder->where('ss.category = :category:', ['category' => enums::$stores_category['pdc']]);

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取距离班次的时长
     *【外协员工申请】页面，允许展示距离班次开始时间 6小时>=x>2小时的班次
     */
    public function getDurationByShiftId(array $params): array
    {
        $return          = [
            'is_pressing' => false, // false 为非紧急工单，true 为紧急工单
            'duration'    => 0,     // 时长
        ];
        $shift_id        = $params['shift_id'];
        $employment_date = $params['employment_date'];

        // 根据班次id获取
        $shift_info_obj       = HrShiftModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => [
                'id' => $shift_id,
            ],
        ]);

        if (!$shift_info_obj) {
            return $return;
        }

        $shift_info = $shift_info_obj->toArray();
        $cur_time         = date('Y-m-d H:i:s');
        $shift_start_time = $employment_date . ' ' . $shift_info['start'] . ':00';
        $hour             = (strtotime($shift_start_time) - strtotime($cur_time)) / 3600;

        // 工单时长
        if ($hour <= 6 && $hour > 0) {
            $return['duration'] = (int)ceil($hour);
        }

        // 工单是否紧急
        if (isCountry('MY')){
            $hour_half = 0.5;
            $hour_six = 2;
        }else{
            $hour_half = 1;
            $hour_six = 6;
        }
        if ($hour <= $hour_six && $hour > $hour_half) {
            $return['is_pressing'] = true;
            return $return;
        }

        return $return;
    }

    public function osType($param)
    {
        $hireNormal = HrStaffOutsourcingModel::HIRE_OS_TYPE_1; //普通外协
        $hireEmploy = HrStaffOutsourcingModel::HIRE_OS_TYPE_3; //兼职外协
        $default    = [
            ['value' => $hireNormal, 'label' => $this->getTranslation()->_("os_outsourcing_hire_type_{$hireNormal}")],
        ];
        if (empty($param['job_id']) || empty($param['os_type'])) {
            return $default;
        }
        //泰国 限制的职位id
        $limitJob = 0;
        if(isCountry('TH')){
            $limitJob = enums::$job_title['warehouse_staff_sorter'];
        }
        if(isCountry('MY')){
            $limitJob = enums::$job_title['dc_officer'];
        }
        //普通外协 并且 选的是对应职位 增加 兼职外协选项
        if (!empty($limitJob) && $param['job_id'] == $limitJob && $param['os_type'] == enums::$os_staff_type['normal']) {
            $default[] = [
                'value' => $hireEmploy,
                'label' => $this->getTranslation()->_("os_outsourcing_hire_type_{$hireEmploy}"),
            ];
        }

        return $default;
    }


    //获取对应网点日期的 出勤人数
    public function getWorkNum($param)
    {
        $storeId   = $param['storeId'];
        $startDate = $param['startDate'];
        $endDate   = $param['endDate'];
        $dateList  = DateHelper::DateRange(strtotime($startDate), strtotime($endDate));
        $jobTitles = $param['job_titles'] ?? [];
        if (empty($jobTitles)) {
            $jobTitles = [enums::$job_title['mobile_dc']];
        }

        $re            = new OsStaffRepository($this->timezone);
        $attendanceNum = $re->getAttendanceNum($storeId, $dateList, $jobTitles);

        return $attendanceNum;
    }

    /**
     * 修改人数
     * 修改需求人数
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function updatePeopleNum($param)
    {
        if(empty($param['out_company_list'])) {
            throw new ValidationException('out_company_list params is not empty!!');

        }

        $osStaffInfo = (new OsStaffRepository($this->timezone))->getOsStaffInfo($param['audit_id']);
        if (empty($osStaffInfo)) {
            throw new ValidationException($this->getTranslation()->_('4008'), enums::$ERROR_CODE['1000']);
        }

        //[2.2]校验是否已经是通过状态
        if ($osStaffInfo['status'] != enums::$audit_status['approved']) {
            throw new ValidationException($this->getTranslation()->_('os_audit_status_not_update'));
        }

        $job_title = $this->getOutsourceCanEditNnumJobTitle();

        if($osStaffInfo['status'] == enums::APPROVAL_STATUS_APPROVAL && $osStaffInfo['job_id'] == $job_title) {
            $number = (new SettingEnvServer())->getSetVal('os_hub_support_reducing_order_num');
            $number = empty($number) ? 2 : $number;
            $number = $number * 60;
            $currentTime = date('Y-m-d H:i:s');
            $shiftStartTime = $osStaffInfo['employment_date'] . ' ' . $osStaffInfo['shift_begin_time'];
            $shiftStartTime = date('Y-m-d H:i:00', strtotime("{$shiftStartTime} -{$number} minutes"));

            if(strtotime($currentTime) >= strtotime($shiftStartTime)) {
                throw new ValidationException($this->getTranslation()->_('os_order_update_time_error', ['limit_time' => $number]));
            }
        }

        $orderList = OsOrderRepository::getOrderList(['init_serial_no' => $osStaffInfo['serial_no']]);
        $orderListToCompanyId = array_column($orderList, NULL, 'out_company_id');

        //检验修改人数
        $updateOrder = [];
        $apply_update_num = 0;
        $sendPushToOsm = [];
        $sendMessage = false;
        foreach ($param['out_company_list'] as $oneCompanyInfo) {
            if(!isset($orderListToCompanyId[$oneCompanyInfo['out_company_id']])) {
                throw new ValidationException('out_company_list params ' . $this->getTranslation()->_('data_error'));
            }

            if($oneCompanyInfo['apply_update_num'] > $orderListToCompanyId[$oneCompanyInfo['out_company_id']]['final_audit_num']) {
                throw new ValidationException($this->getTranslation()->_('update_num_less_order'));
            }

            if($oneCompanyInfo['apply_update_num'] <= 0) {
                throw new ValidationException($this->getTranslation()->_('must_be_greater'));
            }

            $apply_update_num = $apply_update_num + $oneCompanyInfo['apply_update_num'];

            if($oneCompanyInfo['apply_update_num'] != $orderListToCompanyId[$oneCompanyInfo['out_company_id']]['final_audit_num']) {
                $sendPushToOsm[] = $oneCompanyInfo['out_company_id'];
                $sendMessage = true;
                //不同需要改人数,肯定是减少的
                $order['id'] = $orderListToCompanyId[$oneCompanyInfo['out_company_id']]['id'];
                $order['final_audit_num'] = $oneCompanyInfo['apply_update_num'];
                $order['serial_no'] = $orderListToCompanyId[$oneCompanyInfo['out_company_id']]['serial_no'];
                $updateOrder[] = $order;
            }
        }

        $out_company_listId = array_column($param['out_company_list'], 'apply_update_num', 'out_company_id');

        $out_company_data_arr = json_decode($osStaffInfo['out_company_data'], true);
        foreach ($out_company_data_arr as $key => $oneData) {
            if(!isset($out_company_listId[$oneData['out_company_id']])) {
                continue;
            }
            $out_company_data_arr[$key]['apply_update_num'] = $out_company_listId[$oneData['out_company_id']];
        }

        $applyData['out_company_data'] = json_encode($out_company_data_arr, JSON_UNESCAPED_UNICODE);

        $db = $this->getDI()->get('db');
        try {
            $db->begin();

            //更新数据
            $db->updateAsDict(
                'hr_staff_outsourcing',
                $applyData,
                "id = " . $param['audit_id']
            );
            $osOrderRepository = new OsOrderRepository($this->timezone);
            foreach ($updateOrder as $oneData) {
                //查询订单详情
                $detail = $osOrderRepository->getHrOutsourcingOrderDetail($oneData['serial_no']);
                $update['final_audit_num'] = $oneData['final_audit_num'];
                //订单详情配置的人数是否大于 修改的人数, 则将置顶 osm 订单
                if(count($detail) > $oneData['final_audit_num']) {
                    $update['is_exceeds'] = HrOutsourcingOrderModel::IS_EXCEEDS_YES;
                }

                $db->updateAsDict(
                    'hr_outsourcing_order',
                    $update,
                    "id = " . $oneData['id']
                );
            }

            if(!empty($apply_update_num)) {
                $replace_list[] = ['before_column' => 'demend_num', 'after_columns' => 'current_number'];
                $insertParams = [
                    'audit_type' => AuditListEnums::APPROVAL_TYPE_OS,
                    'audit_value'=> $param['audit_id'],
                    'update_list' => [
                        'current_number' => $apply_update_num,
                        'replace_list' => $replace_list,
                    ],
                ];
                $mq = new RocketMQ('audit-list-update');
                $mq->setType(RocketMQ::TAG_AUDIT_LIST_SUMMARY_UPDATE);
                $rid = $mq->sendMsgByTag($insertParams,5);
                $this->logger->write_log('updatePeopleNum audit-list-update rid:' . $rid . 'data:' . json_encode($insertParams),
                    $rid ? 'info' : 'error');
            }

            //人数减少发送 osm push
            $pushData['store_name'] = SysStoreRepository::getStoreName($osStaffInfo['store_id']);
            $pushData['shift_name'] = $osStaffInfo['employment_date'] . ' ' . $osStaffInfo['shift_begin_time'];
            $pushData['serial_no']  = $osStaffInfo['serial_no'];
            if(!empty($sendPushToOsm)) {
                $this->sendOsmPush($sendPushToOsm, $pushData);
            }

            //人数减少发送消息 申请人+抄送人
            if($sendMessage) {
                $this->sendMessageToApproval($param['audit_id'], AuditListEnums::APPROVAL_TYPE_OS, $pushData);
            }

            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log("updatePeopleNum "
                ." E_file:".$e->getFile()
                ." E_line:".$e->getLine()
                ." E_code:".$e->getCode()
                ." E_Msg:".$e->getMessage(),
                "error");
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        return $this->checkReturn(['data' => []]);
    }

    /**
     * 申请人 修改人数 发送 osm push
     * @param $companyIds
     * @param $data
     * @return bool
     */
    public function sendOsmPush($companyIds, $data)
    {
        if(empty($companyIds)) {
            return false;
        }

        $device_list = OutsourcingCompanyDeviceTokenModel::find([
            'conditions' => ' company_id in ({company_ids:array}) ',
            'bind'       => ['company_ids' => $companyIds],
            'columns'    => 'accept_language,device_token,device_type,os',
        ])->toArray();

        $all_send_data = [];
        foreach ($device_list as $oneDevice) {
            $send_data['message_title']   = $this->getTranslation($oneDevice['accept_language'])->_('osm_push_applicant_reduces_title');
            $send_data['message_content'] = $this->getTranslation($oneDevice['accept_language'])->_('osm_push_applicant_reduces_content', [
                'store_name' => $data['store_name'],
                'shift_name' => $data['shift_name'],
                'serial_no'  => $data['serial_no'],
            ]);
            $send_data['device_token']    = $oneDevice['device_token'];
            $send_data['device_type']     = $oneDevice['device_type'];
            $send_data['os']              = $oneDevice['os'];
            $send_data['src']             = 'osm';
            $send_data['message_scheme']  = 'osm://fe/page?path=orderList&index=1';
            $all_send_data[]              = $send_data;
        }
        $post_data   = [
            'list' => $all_send_data,
        ];
        return (new PublicRepository($this->timeZone))->sendPushToOsmCompany($post_data);
    }

    /**
     * 申请人 修改人数 给审批人发送消息
     * @param $biz_value
     * @param $biz_type
     * @param $data
     * @return bool
     */
    public function sendMessageToApproval($biz_value, $biz_type, $data)
    {
        $staffIds = (new ApprovalFinderServer())->getApprovalList(['biz_value' => $biz_value, 'biz_type' => $biz_type]);

        if(empty($staffIds)) {
            return false;
        }
        //获取员工和上级的语言包
        $staffServer   = new StaffServer();
        $staffLangInfo = $staffServer->getBatchStaffLanguage($staffIds);

        foreach ($staffIds as $oneStaffId) {
            if(!isset($staffLangInfo[$oneStaffId])) {
                continue;
            }
            $t             = $this->getTranslation($staffLangInfo[$oneStaffId]);
            $title                               = $t->_('apply_reduces_num_title');
            $content                             = $t->_('apply_reduces_num_content', [
                'store_name' => $data['store_name'],
                'shift_name' => $data['shift_name'],
                'serial_no'  => $data['serial_no'],
            ]);
            $message_param['staff_users']        = [$oneStaffId];
            $message_param['message_title']      = $title;
            $message_param['message_content']    = '<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no,viewport-fit=cover"><p style="font-size: 16px;">' . $content . "</p>";
            $message_param['staff_info_ids_str'] = $oneStaffId;
            $message_param['id']                 = time() . $oneStaffId . rand(1000000, 9999999);
            $message_param['category'] = -1;
            $bi_rpc                    = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
            $bi_rpc->setParams($message_param);
            $bi_rpc->execute();
        }

        return true;
    }

    /**
     * 审批人修改人数 发送给申请人
     * @param $staffId
     * @param $data
     * @return bool
     */
    public function sendMessageToApply($staffId, $data)
    {
        //获取收信人语言环境
        $lang = (new StaffServer)->getLanguage($staffId);
        //获取语言
        $t = $this->getTranslation($lang);

        $title                               = $t->_('os_partial_approval_title');
        $content                             = $t->_('os_partial_approval_content', ['serial_no'  => $data['serial_no']]);
        $message_param['staff_users']        = [$staffId];
        $message_param['message_title']      = $title;
        $message_param['message_content']    = '<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no,viewport-fit=cover"><p style="font-size: 16px;">' . $content . "</p>";
        $message_param['staff_info_ids_str'] = $staffId;
        $message_param['id']                 = time() . $staffId . rand(1000000, 9999999);
        $message_param['category'] = -1;
        $bi_rpc                    = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
        $bi_rpc->setParams($message_param);
        $bi_rpc->execute();

        return true;
    }

    /**
     * my 审批 超时关闭
     * @param $timeAt
     * @return bool
     */
    public function getPendingAudit($timeAt)
    {
        $data['status'] = enums::APPROVAL_STATUS_PENDING;
        $data['time'] = $timeAt;
        $data['job_id'] = enums::$my_job_title['outsource'];
        $pendingData = (new OsStaffRepository($this->timezone))->getPendingOutsourcing($data);

        $approvalServer = new ApprovalServer($this->lang, $this->timezone);
        foreach ($pendingData as $oneData) {
            //待离职日期 还是 待审批 置为 审批超时
            $result = $approvalServer->timeOut($oneData['id'], AuditListEnums::APPROVAL_TYPE_OS);
            if (!$result){
                $this->logger->write_log("os-getPendingAudit 超时关闭续约审批任务失败 id:".$oneData['id']);
                continue;
            }
            $this->logger->write_log(['os-getPendingAudit-timeOut-success ' => $oneData['id']], 'info');
        }

        $this->logger->write_log('os-getPendingAudit-timeOut-end', 'info');
        echo 'success';
        return true;

    }



}
