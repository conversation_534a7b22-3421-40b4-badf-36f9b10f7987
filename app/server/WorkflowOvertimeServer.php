<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\WorkflowConfigEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeOvertimeConfigModel;

class WorkflowOvertimeServer extends BaseServer
{
    private static $instance;

    /**
     * @var ApprovalServer
     */
    public $approvalServer = null;

    /**
     * 获取实例
     * @param $lang
     * @param $timezone
     * @return self
     */
    public static function getInstance($lang , $timezone): WorkflowOvertimeServer
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self($lang , $timezone);
            self::$instance->init();
        }
        return self::$instance;
    }

    private function init()
    {
        $this->approvalServer = new ApprovalServer($this->lang, $this->timeZone);
    }

    /**
     * 根据当前审批流的最新超时配置去计算超时时间
     * @return mixed
     * @throws ValidationException
     */
    public function getLatestAuditOvertimeConfig($audit_id, $audit_type, $workflow_id = '')
    {
        if (empty($audit_type)) {
            return null;
        }
        $this->logger->write_log(sprintf('getLatestAuditOvertimeConfig params %d,%d,%s', $audit_id, $audit_type, $workflow_id), 'info');
        if (!empty($workflow_id)) {
            $workflowInfo = WorkflowModel::findFirstById($workflow_id);
        } else {
            $workflowInfo = WorkflowModel::findFirst([
                'conditions' => 'relate_type = :relate_type: and is_view = 1',
                'bind' => [
                    'relate_type' => $audit_type
                ]
            ]);
        }
        if (empty($workflowInfo)) {
            return null;
        }
        $flowBase = $workflowInfo->toArray();

        if (empty($flowBase['version'])) {
            return null;
        }
        $configures = WorkflowConfigServer::getInstance($this->lang, $this->timeZone)
            ->getAuditConfigByVersion($flowBase['version'], $flowBase['id']);
        $configuresList = array_column($configures, 'configure_value', 'configure_type');

        //无超时配置
        if (!isset($configuresList[enums::WF_OA_AUTO_CNF_OT_TYPE]) || $configuresList[enums::WF_OA_AUTO_CNF_OT_TYPE] == WorkflowConfigEnums::WF_OVERTIME_TYPE_NONE) {
            return null;
        }

        //超时类型
        //1=无超时 2=设置整体超时时间 3=设置单一节点超时时间 4=单独设置每个节点超时时间 5=设置表单中字段为超时时间
        $overtimeType = $configuresList[enums::WF_OA_AUTO_CNF_OT_TYPE];

        $overtimeDate = '';
        switch ($overtimeType) {
            case WorkflowConfigEnums::WF_OVERTIME_TYPE_OVERALL:
            case WorkflowConfigEnums::WF_OVERTIME_TYPE_EACH_NODE:
                $overtimeDays = $configuresList[enums::WF_OA_AUTO_CNF_OT_TYPE_DAYS];
                $overtimeDate = $this->getOvertimeDays($overtimeDays);
                break;
            case WorkflowConfigEnums::WF_OVERTIME_TYPE_FORM:
                $overtimeDate = $this->approvalServer->getAuditFormOvertimeDate($audit_id, $audit_type);
                break;
            default:
                break;
        }
        return empty($overtimeDate) ? null : $overtimeDate;
    }

    /**
     * 获取当前审批申请超时设置
     * @param $request
     * @param $current_node
     * @param $next_node
     * @param $action
     * @return mixed
     * @throws ValidationException
     */
    public function getCurrentApplyAuditOvertimeDate($request, $current_node, $next_node, $action)
    {
        $configuresList = $this->getOvertimeConfig($request);
        if (is_null($configuresList)) {
            return null;
        }

        //超时类型
        //1=无超时 2=设置整体超时时间 3=设置单一节点超时时间 4=单独设置每个节点超时时间 5=设置表单中字段为超时时间
        $overtimeType = $configuresList[enums::WF_OA_AUTO_CNF_OT_TYPE];

        $overtimeDate = '';
        switch ($overtimeType) {
            //当超时设置2=设置整体超时时间 5=设置表单中字段为超时时间， 且审批同意时
            //直接返回 audit_apply表 time_out超时日期字段
            case WorkflowConfigEnums::WF_OVERTIME_TYPE_OVERALL:
            case WorkflowConfigEnums::WF_OVERTIME_TYPE_FORM:
                $overtimeDate = $request->getTimeOut();
                break;
            //当超时设置3=设置单一节点超时时间， 且审批同意时
            //time_out = 当前时间+超时天数 ，其他时time_out = audit_apply表time_out字段
            case WorkflowConfigEnums::WF_OVERTIME_TYPE_EACH_NODE:
                $overtimeDays = $configuresList[enums::WF_OA_AUTO_CNF_OT_TYPE_DAYS];
                if ($this->isActionApproval($current_node, $next_node, $action)) {
                    $overtimeDate = $this->getOvertimeDays($overtimeDays);
                } else {
                    $overtimeDate = $request->getTimeOut();
                }
                break;
            //当超时设置4=单独设置每个节点超时时间， 且审批同意时
            //time_out = 当前时间+超时天数 ，其他时time_out = audit_apply表time_out字段
            case WorkflowConfigEnums::WF_OVERTIME_TYPE_INDIVIDUAL_NODE:
                if ($this->isActionApproval($current_node, $next_node, $action)) {
                    $overtimeDate = $this->getIndividualNodeOvertimeDate($next_node, $request);
                } else {
                    $overtimeDate = $request->getTimeOut();
                }
                break;
            default:
                break;
        }
        return empty($overtimeDate) ? null : $overtimeDate;
    }

    /**
     * 重算转交时的超时时间
     * @param $request
     * @param $node_info
     * @param $overtime_type
     * @param $configuresList
     * @return mixed|string|null
     * @throws ValidationException
     */
    public function recalculateHandOverOvertimeDate($request, $node_info, $configuresList)
    {
        $overtime_type = $configuresList[enums::WF_OA_AUTO_CNF_OT_TYPE];
        if (empty($overtime_type)) {
            return null;
        }
        $overtimeDate = '';
        switch ($overtime_type) {
            //当超时设置3=设置单一节点超时时间， 且审批同意时
            //time_out = 当前时间+超时天数 ，其他时time_out = audit_apply表time_out字段
            case WorkflowConfigEnums::WF_OVERTIME_TYPE_EACH_NODE:
                $overtimeDays = $configuresList[enums::WF_OA_AUTO_CNF_OT_TYPE_DAYS];
                $overtimeDate = $this->getOvertimeDays($overtimeDays);
                break;
            //当超时设置4=单独设置每个节点超时时间， 且审批同意时
            //time_out = 当前时间+超时天数 ，其他时time_out = audit_apply表time_out字段
            case WorkflowConfigEnums::WF_OVERTIME_TYPE_INDIVIDUAL_NODE:
                $overtimeDate = $this->getIndividualNodeOvertimeDate($node_info, $request);
                break;
            default:
                break;
        }
        return $overtimeDate ?: '';
    }

    /**
     * 是否需要获取下一个节点
     * true - 是，false - 否
     * @param $request
     * @param $current_node
     * @param $action
     * @return bool
     */
    public function isNeedProcessNextNode($request, $current_node, $action): bool
    {
        $processPolicy = $this->getCurrentApplyAuditOvertimePolicy($request, $current_node);
        return $processPolicy == WorkflowConfigEnums::WF_HANDLING_AFTER_OT_AUTO_PASS;
    }

    /**
     * 获取当前节点超时处理方式
     * @return void
     */
    public function getCurrentApplyAuditOvertimePolicy($request, $current_node)
    {
        $configuresList = $this->getOvertimeConfig($request);
        if (is_null($configuresList)) {
            return null;
        }

        //超时后处理方式，1=自动通过 4=自动驳回 5=超时关闭 6=自动转交
        $overtimeType = $configuresList[enums::WF_OA_AUTO_CNF_OT_TYPE];
        if ($overtimeType == WorkflowConfigEnums::WF_OVERTIME_TYPE_INDIVIDUAL_NODE) { //单独设置每个节点超时时间
            return $this->getIndividualNodeOvertimeAutoProcessPolicy($request, $current_node);
        } else {
            return $configuresList[enums::WF_OA_AUTO_CNF_OT_TYPE_POLICY];
        }
    }

    /**
     * 获取超时配置
     * @param $request
     * @return array|null
     */
    public function getOvertimeConfig($request): ?array
    {
        if (empty($request)) {
            return null;
        }
        if (!empty($request->getWorkflowId())) {
            $workflowInfo = WorkflowModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $request->getWorkflowId(),
                ],
            ]);
        } else {
            $workflowInfo = WorkflowModel::findFirst([
                'conditions' => 'relate_type = :relate_type:',
                'bind' => [
                    'relate_type' => $request->getBizType(),
                ],
            ]);
        }
        if (empty($workflowInfo)) {
            return [];
        }
        $flowBase = $workflowInfo->toArray();
        if (empty($request->getWorkflowBaseVersionId())) {
            return null;
        }

        //获取超时配置
        $configures = WorkflowConfigServer::getInstance($this->lang, $this->timeZone)
            ->getAuditConfigByVersion($request->getWorkflowBaseVersionId(), $flowBase['id']);
        if (empty($configures)) {
            return null;
        }
        $configuresList = array_column($configures, 'configure_value', 'configure_type');

        //无超时配置
        if (!isset($configuresList[enums::WF_OA_AUTO_CNF_OT_TYPE]) || $configuresList[enums::WF_OA_AUTO_CNF_OT_TYPE] == WorkflowConfigEnums::WF_OVERTIME_TYPE_NONE) {
            return null;
        }
        return $configuresList;
    }

    /**
     * 是否为审批同意
     * 会签（最后一个审批人同意时）
     * 或签
     *
     * @param $current_node
     * @param $next_node
     * @param $action
     * @return bool
     */
    private function isActionApproval($current_node, $next_node, $action): bool
    {
        if (!in_array($action, [
            enums::WF_ACTION_CREATE,
            enums::WF_ACTION_CREATE_CANCEL,
            enums::WF_ACTION_CREATE_WITHOUT_LOG,
            enums::WF_ACTION_APPROVAL_EMPTY,
            enums::WF_ACTION_APPROVE,
            enums::WF_ACTION_AUDIT_APPROVE,
            enums::WF_ACTION_APPROVE_COUNTERSIGN,
            enums::WF_ACTION_FORCE_APPROVAL_PASS,
            enums::WF_ACTION_OVERTIME_AUTO_APPROVAL,
            enums::WF_ACTION_OVERTIME_AUTO_APPROVAL_COUNTERSIGN,
            enums::WF_ACTION_EDIT_RECREATE,
            enums::WF_ACTION_SYS_APPROVAL_PASS,
        ])) {
            return false;
        }

        //会签没有审批完
        if ($action == enums::WF_ACTION_APPROVE_COUNTERSIGN && $current_node->getId() == $next_node->getId()) {
            return false;
        }

        return true;
    }

    /**
     * 获取超时时间
     * @param $request
     * @param $current_node
     * @param $next_node
     * @param $action
     * @return null
     * @throws ValidationException
     */
    public function getOvertimeDate($request, $current_node, $next_node, $action)
    {
        //没有固化 Base 表 version 字段 或 下一个节点为最终节点时，不重新计算超时时间
        if (empty($request->getWorkflowBaseVersionId()) || $next_node->getType() == Enums::NODE_FINAL) {
            return null;
        }
        return $this->getCurrentApplyAuditOvertimeDate($request, $current_node, $next_node, $action);
    }

    /**
     * 获取今天后 N 天
     * @param $days
     * @return string
     */
    public function getOvertimeDays($days): string
    {
        return date('Y-m-d', strtotime(sprintf('+%d days', $days)));
    }

    /**
     * 当设置为“单独设置每个节点超时时间”时，获取超时日期
     * @param $node_info
     * @param $request
     * @return mixed
     * @throws ValidationException
     */
    private function getIndividualNodeOvertimeDate($node_info, $request)
    {
        $config = $this->getIndividualNodeOvertimeConfig($node_info);
        if (is_null($config)) {
            return null;
        }
        switch ($config->getOvertimeSubType()) {
            case WorkflowConfigEnums::WF_OVERTIME_SUB_TYPE_FORM_COLUMN:
                //设置表单字段为超时时间
                $overtimeDate = $this->approvalServer->getAuditFormOvertimeDate($request->getBizValue(), $request->getBizType());
                break;
            case WorkflowConfigEnums::WF_OVERTIME_SUB_TYPE_AUDIT_DAYS:
                //最大审批自然日（天数）
                $overtimeDate = $this->getOvertimeDays($config->getOvertimeDays());
                break;
            default:
                break;
        }
        return $overtimeDate ?? null;
    }

    /**
     * 获取节点超时后自动处理策略
     * @param $node_info
     * @return mixed
     */
    protected function getIndividualNodeOvertimeAutoProcessPolicy($request, $node_info)
    {
        $config = $this->getIndividualNodeOvertimeConfig($node_info);
        if (is_null($config)) {
            return null;
        }

        //超时转交 & 开启了审批人跳过
        if ($config->getOvertimePolicy() == WorkflowConfigEnums::WF_HANDLING_AFTER_OT_AUTO_HANDLE &&
            $config->getIsHandoverTermination() == CommonEnums::SWITCH_OPENED) {

            //获取转交人(只能是一个人)
            $handoverStaffId = $this->getHandOverStaffIds($request, $config);

            //如跳过的人是转交人，则直接执行超时策略
            $handoverTerminationStaffIds = $config->getHandoverTerminationStaffIds() ? explode(',', $config->getHandoverTerminationStaffIds()) : [];
            if (array_intersect($handoverStaffId, $handoverTerminationStaffIds)) {
                return $config->getHandoverOvertimePolicy();
            }
        }

        //已经转交过，则使用转交超时策略
        if ($config->getIsHandover() == WorkflowConfigEnums::WF_HAS_HANDOVER_YES) {
            return $config->getHandoverOvertimePolicy();
        }
        return $config->getOvertimePolicy();
    }

    /**
     * 获取超时转交人
     * @param $request
     * @param $config
     * @return array
     */
    public function getHandOverStaffIds($request, $config): array
    {
        //获取转交人
        $handoverStaffIds = [];
        switch ($config->getHandoverPolicy()) {
            case WorkflowConfigEnums::WF_HANDLE_POLICY_SPEC_STAFF:
                $handoverStaffIds = $config->getHandoverConfig() ? explode(',', $config->getHandoverConfig()) : [];
                break;
            case WorkflowConfigEnums::WF_HANDLE_POLICY_UPPER_MANAGER:
                $handoverStaffIds = ApprovalFinderServer::getInstance()->findSuperior($request->getSubmitterId(), 2);
                $handoverStaffIds = $handoverStaffIds ? explode(',', $handoverStaffIds) : [];
                break;
        }
        if (empty($handoverStaffIds)) {
            return [];
        }
        return $handoverStaffIds;
    }

    /**
     * 获取独立节点超时配置
     * @param $node_info
     * @return mixed
     */
    public function getIndividualNodeOvertimeConfig($node_info)
    {
        if (empty($node_info)) {
            return null;
        }
        $config = WorkflowNodeOvertimeConfigModel::findFirstByNodeId($node_info->getId());
        if (empty($config)) {
            return null;
        }
        if ($config->getIsSwitchOpen() == WorkflowConfigEnums::WF_OVERTIME_SWITCH_CLOSED) {
            return null;
        }
        return $config;
    }

    /**
     * 处理转交
     * @param $request
     * @param $current_node
     * @return array
     */
    public function processOvertimeHandOver($request, $current_node): array
    {
        $config = $this->getIndividualNodeOvertimeConfig($current_node);
        if (empty($config)) {
            return [];
        }

        //获取转交人
        $handoverStaffIds = $this->getHandoverStaffIds($request, $config);
        if ($config->getIsHandoverTermination() == CommonEnums::SWITCH_OPENED) {
            //要跳过的审批人
            $handoverTerminationStaffIds = $config->getHandoverTerminationStaffIds() ? explode(',', $config->getHandoverTerminationStaffIds()) : [];

            //排掉要跳过的人
            $diffHandOverStaffIds = array_diff($handoverStaffIds, $handoverTerminationStaffIds);
            $this->logger->write_log(sprintf('[request = %s]getIsHandoverTermination() == 1 & 转交人%s',
                json_encode($request->toArray()), implode(',', $diffHandOverStaffIds)), 'info');

            if (empty($diffHandOverStaffIds)) { //按照转交超时逻辑处理
                [$action, $auditLog] = $this->transferHandoverOvertimePolicyToAction($config->getHandoverOvertimePolicy());
                return [$action, $auditLog, []];
            }
            $handoverStaffIds = $diffHandOverStaffIds;
        }

        //重新计算超时时间
        //audit_apply表
        $timeOutDate = $this->getOvertimeDays($config->getHandoverAuditDays());
        $request->setTimeOut($timeOutDate);

        //audit_approval表超时
        $approval = AuditApprovalModel::find([
            'conditions' => 'biz_type = :audit_type: and biz_value = :audit_value: and state = 1',
            'bind' => [
                'audit_type' => $request->getBizType(),
                'audit_value' => $request->getBizValue(),
            ],
        ]);
        $approval->update(['state' => enums::APPROVAL_STATUS_TIMEOUT, 'deleted' => CommonEnums::IS_DELETED_YES]);

        //标记已经转交过
        $overtimeConfig = WorkflowNodeOvertimeConfigModel::findFirstByNodeId($request->getCurrentFlowNodeId());
        if (empty($overtimeConfig)) {
            return [];
        }
        $overtimeConfig->setIsHandover(CommonEnums::SWITCH_OPENED);
        $overtimeConfig->save();
        //更新workflow_node
        $workflowNodeModel = WorkflowNodeModel::findFirstById($request->getCurrentFlowNodeId());
        $workflowNodeModel->setAuditorId(implode(',', $handoverStaffIds));
        $workflowNodeModel->save();

        return [Enums::WF_ACTION_OVERTIME_AUTO_HANDOVER, 'err_msg_wf_process_handover', $handoverStaffIds];
    }

    public function transferHandoverOvertimePolicyToAction($policy)
    {
        if ($policy == WorkflowConfigEnums::WF_HANDLING_AFTER_OT_AUTO_CLOSED) {
            return [enums::WF_ACTION_TIMEOUT, 'err_msg_wf_process_timeout'];
        } else if ($policy == WorkflowConfigEnums::WF_HANDLING_AFTER_OT_AUTO_REJECT) {
            return [enums::WF_ACTION_OVERTIME_AUTO_REJECT, 'err_msg_wf_process_reject'];
        } else if ($policy == WorkflowConfigEnums::WF_HANDLING_AFTER_OT_AUTO_PASS) {
            return [enums::WF_ACTION_OVERTIME_AUTO_APPROVAL, 'err_msg_wf_process_approval'];
        }
    }
}