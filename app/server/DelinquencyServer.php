<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\Models\backyard\DelinquencyCategoryModel;
use FlashExpress\bi\App\Models\backyard\HrBlackGreyListModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;

class DelinquencyServer extends BaseServer
{
    public const T_PRE = 'deli_';

    /**
     * @param $identity
     * @param $type
     * @return array
     */
    public function getDelinquencyList($identity,$type=[])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['l' => HrBlackGreyListModel::class])
            ->where("identity = :identity: and identity != '' and  l.status = :status:",
                ['identity'=> $identity,'status'=>HrBlackGreyListModel::STATUS_ON]);
        if (!empty($type)){
            $builder->andWhere("behavior_type in ({type:array}) ",['type'=>$type]);
        }

        $builder->orderBy('l.id ASC');
        return $this->paginate($builder,1, 9999);
    }

    /**
     * 格式化输出列表
     * @param $list
     * @return array
     */
    public function displayList($list)
    {
        $return = [];
        foreach ($list as $value){
            $row = [
                'staff_job_title' => $this->getJobTitleName($value['job_title']),//职位
                'deli_created_at' => DateHelper::utcToLocal($value['created_at']),
                'source_cate' => $this->t->t(self::T_PRE.$value['source_code']),
                'p_category' => $this->getCategoryTxt($value['p_category']),
                's_category' => $this->getCategoryTxt($value['category']),
                'deli_remark' => $value['remark'],
            ];
            if (!empty($value['leave_num']) && $value['category'] == 'D0101'){
                $row['deli_leave_num'] = $value['leave_num'];
            }
            $return[] = $this->format($row);
        }

        return $return;
    }

    private function getJobTitleName($jobTitle)
    {
        $jobTitles = HrJobTitleModel::find()->toArray();
        $jobTitles = array_column($jobTitles,'job_name','id');
        return $jobTitles[$jobTitle] ?? '';
    }


    /**
     * @param $code
     * @return string
     */
    private function getCategoryTxt($code)
    {
        $cate = DelinquencyCategoryModel::find()->toArray();
        $cate = array_column($cate,null,'inter_code');
        if (isset($cate[$code])){
            return $this->t->t($cate[$code]['t_key']);
        }
        return '';
    }

}