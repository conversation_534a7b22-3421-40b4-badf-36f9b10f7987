<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\LeaveQuestionnaireModel;
use FlashExpress\bi\App\Models\backyard\LeaveQuestionnaireOptionModel;
use FlashExpress\bi\App\Models\backyard\MsgAssetModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveQuestionnaireModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReasonModel;
use FlashExpress\bi\App\Models\backyard\StaffResignModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\fle\FleKaProfileModel;
use FlashExpress\bi\App\Models\oa\LoanModel;
use FlashExpress\bi\App\Models\oa\ReimbursementModel;
use FlashExpress\bi\App\Models\oa\ReimbursementRelLoanModel;
use FlashExpress\bi\App\Models\oa\ReserveFundApplyModel;
use FlashExpress\bi\App\Models\oa\ReserveFundReturnModel;
use FlashExpress\bi\App\Repository\AuditApprovalRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\JobTitleRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\ResignRepository;
use FlashExpress\bi\App\Repository\StaffAuditToolLog;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Server\MaterialAssetServer;


class ResignServer extends AuditBaseServer
{
    public $timezone;
    protected $re;
    protected $resign;
    protected $staff;
    protected $approval;
    protected $overtime;
    protected $auditlist;
    protected $hc;
    protected $auditlog;
    protected $public;
    protected $other;
    public static $current_state;


    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
        $this->resign   = new ResignRepository($lang, $this->timezone);
        $this->staff    = new StaffRepository();
        $this->approval = new AuditApprovalRepository();
        $this->overtime = new OvertimeRepository($this->timezone);
        $this->auditlist= new AuditlistRepository($lang, $this->timezone);
        $this->public   = new PublicRepository();
        $this->auditlog = new StaffAuditToolLog();
        $this->other    = new OtherRepository();
        $this->hc = new HcRepository($this->timezone);
    }

    /**
     *
     *
     * 添加离职申请
     *
     * @param array $paramIn
     * @return array
     * @throws \Exception
     *
     */
    public function addResign($paramIn = [])
    {
        //[1]获取传入参数
        $staffId            = $this->processingDefault($paramIn, 'staff_id');
        $workHandover       = $this->processingDefault($paramIn, 'work_handover');
        $lastWorkDate       = $this->processingDefault($paramIn, 'last_work_date');
        $leaveDate          = $this->processingDefault($paramIn, 'leave_date');
        $reason             = $this->processingDefault($paramIn, 'reason');
        $remark             = $this->processingDefault($paramIn, 'remark');
        $organization_id    = $this->processingDefault($paramIn, 'organization_id');
        $organization_type  = $this->processingDefault($paramIn, 'organization_type');
        $imagePathArr       = $this->processingDefault($paramIn, 'sign_images');
        $assets_sign_images = $this->processingDefault($paramIn, 'assets_sign_images', 3);

        $vehicleImagePathArr     = $this->processingDefault($paramIn, 'vehicle_images');      //车辆照片
        $vehicleTestImagePathArr = $this->processingDefault($paramIn, 'vehicle_test_images'); //车联检验照片
        $claimImagePathArr       = $this->processingDefault($paramIn, 'vehicle_claim_images');//汽车索赔件
        $agent_job_title       = $this->processingDefault($paramIn, 'agent_job_title',2);//转个人代理职位
        $agent_mobile       = $this->processingDefault($paramIn, 'agent_mobile');//转个人代理职位
        $ticket       = $this->processingDefault($paramIn, 'ticket');//人脸比对临时凭证

        $reason    = addcslashes(stripslashes($reason), "'");
        $serial_no = $this->getID();
        $add_hour  = $this->getDI()['config']['application']['add_hour'];
        //[2]逻辑验证
        //根据工号获取详情
        $staffInfo              = $this->staff->checkoutStaffById($staffId);
        $staffInfo['hire_date'] = date('Y-m-d', strtotime($staffInfo['hire_date']));
        if (empty($staffInfo)) {
            throw new \Exception($this->getTranslation()->_('1001'), enums::$ERROR_CODE['1000']);
        }

        //子账号不能申请
        if ($staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            throw new \Exception($this->getTranslation()->_('no_have_permission'), enums::$ERROR_CODE['1000']);
        }
        //需要进行人脸比对的，进行凭证验证
        $isUseFaceCompare = $this->isUseFaceCompare($staffInfo['formal'],$staffInfo['hire_type']);
        if ($isUseFaceCompare) {
            $faceCompareSever = new FaceCompareServer($this->lang);
            $faceCompareSever->checkTicket($ticket, $staffId);
            $face_image_url = $faceCompareSever->getFaceImageUrl($ticket, $staffId);
        }
        //已离职员工不能申请
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_RESIGN) {
            throw new \Exception($this->getTranslation()->_('err_msg_has_leave'), enums::$ERROR_CODE['1000']);
        }

        //待离职员工不能申请
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_ON_JOB &&  $staffInfo['wait_leave_state'] == 1) {
            throw new \Exception($this->getTranslation()->_('err_msg_wait_leave'), enums::$ERROR_CODE['1000']);
        }

        //校验离职日期小于入职日期
        if ($leaveDate < $staffInfo['hire_date']) {
            throw new \Exception($this->getTranslation()->_('err_msg_unvalid_leave_time'), enums::$ERROR_CODE['1000']);
        }

        //校验是否存在离职申请
        $result = $this->resign->getResignCnt(['staff_info_id' => $staffId]);
        if ($result != 0) {
            throw new \Exception($this->getTranslation()->_('5202'), enums::$ERROR_CODE['1000']);
        }

        //校验交接人是否是自己
        if ($workHandover == $staffId) {
            throw new \Exception($this->getTranslation()->_('err_msg_no_self'), enums::$ERROR_CODE['1000']);
        }

        $staffIdHandoverToInfo = $this->staff->getStaffpositionV2($workHandover);
        if (empty($staffIdHandoverToInfo) || $staffIdHandoverToInfo['state'] != 1) {
            throw new \Exception($this->getTranslation()->_('err_msg_should_on_hire'), enums::$ERROR_CODE['1000']);
        }
        //查询申请用户是否需要上传车辆照片
        $type = $this->getResignTypes($staffId);
        if ($type['type'] == 2) {
            //判断照片车辆照片  1. 必填，最少6个，最多10个，页面提示“请上传车的四边、车内、皮卡车厢照片，至少6个”
            if (empty($vehicleImagePathArr) || count($vehicleImagePathArr) < 6) {
                throw new \Exception($this->getTranslation()->_('err_msg_resign_vehicle_images'),
                    enums::$ERROR_CODE['1000']);
            }
            //2. 车辆检验文件   1. 必填，最少1个，最多2个
            if (empty($vehicleTestImagePathArr) || count($vehicleTestImagePathArr) > 2) {
                throw new \Exception($this->getTranslation()->_('err_msg_resign_vehicle_test_images'),
                    enums::$ERROR_CODE['1000']);
            }

            if (!empty($claimImagePathArr) && count($claimImagePathArr) > 3) {
                throw new \Exception($this->getTranslation()->_('err_msg_resign_vehicle_claim_images'),
                    enums::$ERROR_CODE['1000']);
            }
        }
        $now = gmdate('Y-m-d H:i:s');

        $db = $this->getDI()->get("db");
        //开启事务
        $db->begin();
        try {
            if (isCountry('PH') && $reason == StaffLeaveReasonModel::LEAVE_REASON_96) {
                $hcInfo = HrhcModel::findFirst([
                    'conditions' => 'department_id = :department_id: and job_title = :job_title: and  worknode_id = :store_id: and state_code = 2 and hire_type = :hire_type: and reason_type in(1,3) and deleted = 1',
                    'bind'       => [
                        'department_id' => $staffInfo['node_department_id'],
                        'job_title'     => $agent_job_title,
                        'store_id'      => $staffInfo['sys_store_id'],
                        'hire_type'     => HrStaffInfoModel::HIRE_TYPE_UN_PAID,
                    ],
                    'for_update' => true,
                ]);
                if (empty($hcInfo)) {
                    throw new BusinessException($this->getTranslation()->_('hc_not_exist'));
                }
            }
            //[3]保存申请数据
            $data = [
                'submitter_id'        => $staffId,
                'hire_date'           => $staffInfo['hire_date'],
                'last_work_date'      => $lastWorkDate,
                'leave_date'          => $leaveDate,
                'work_handover'       => $workHandover,
                'reason'              => $reason,
                'remark'              => $remark,
                'status'              => enums::$audit_status['panding'],
                'serial_no'           => 'RN' . $serial_no,
                'source'              => 0,
                'created_at'          => $now,
                'job_title'           => $agent_job_title,
                'mobile'              => $agent_mobile,
                're_employment_hc_id' => !empty($hcInfo) ? $hcInfo->hc_id : 0,
                'face_image_url'      => $face_image_url??'',
            ];

            if (isCountry('My')) {
                // 马来 离职通知期
                $personInfoServer  =
                    new PersoninfoServer($this->lang, $this->timezone);
                $resignationNotice = $personInfoServer->resignationNotice($this->staff->getStaffPosition($staffId),
                    $staffId);
                if ($resignationNotice) {
                    $data = array_merge($data, [
                        'resignation_notice'    => $resignationNotice['resignation_notice'],
                        'resignation_work_day'  => $resignationNotice['work_day'],
                        'resignation_leave_day' => $resignationNotice['leave_day'],
                    ]);
                }
            }

            $auditId = $this->resign->inserResign($data);
            if (empty($auditId)) {
                throw new \Exception($this->getTranslation()->_('4008'));
            }

            //插入签名图片
            if (!empty($imagePathArr)) {
                $insertImgData = [];

                foreach ($imagePathArr as $image) {
                    $insertImgData[] = [
                        'id'         => $auditId,
                        'image_path' => $image,
                    ];
                }
                $this->public->batchInsertImgs($insertImgData, 'RESIGN_AUDIT');
            }

            //插入资产扣款须知签字
            if (!empty($assets_sign_images)) {
                $insert_sign_image_data = [];
                foreach ($assets_sign_images as $image) {
                    $insert_sign_image_data[] = [
                        'id'         => $auditId,
                        'image_path' => $image,
                    ];
                }
                $this->public->batchInsertImgs($insert_sign_image_data, 'RESIGN_ASSETS_SIGN_AUDIT');
            }

            //插入车辆照片
            if (!empty($vehicleImagePathArr)) {
                $insertImgData = [];

                foreach ($vehicleImagePathArr as $image) {
                    $insertImgData[] = [
                        'id'         => $auditId,
                        'image_path' => $image,
                    ];
                }
                $this->public->batchInsertImgs($insertImgData, 'RESIGN_VEHICLE_AUDIT');
            }

            //车辆检验文件
            if (!empty($vehicleTestImagePathArr)) {
                $insertImgData = [];

                foreach ($vehicleTestImagePathArr as $image) {
                    $insertImgData[] = [
                        'id'         => $auditId,
                        'image_path' => $image,
                    ];
                }
                $this->public->batchInsertImgs($insertImgData, 'RESIGN_VEHICLE_TEST_AUDIT');
            }

            //汽车索赔件
            if (!empty($claimImagePathArr)) {
                $insertImgData = [];

                foreach ($claimImagePathArr as $image) {
                    $insertImgData[] = [
                        'id'         => $auditId,
                        'image_path' => $image,
                    ];
                }
                $this->public->batchInsertImgs($insertImgData, 'RESIGN_VEHICLE_CLAIM_AUDIT');
            }

            $approvalServer = new ApprovalServer($this->lang, $this->timezone);
            $requestId      = $approvalServer->create($auditId, AuditListEnums::APPROVAL_TYPE_RN, $staffId);
            if (!$requestId) {
                throw new \Exception('创建审批流失败');
            }
            if(!empty($hcInfo)){
                //扣减HC
                $updateHc = [
                    'id'            => $hcInfo->hc_id,
                    'surplusnumber' => $hcInfo->surplusnumber,
                    'demandnumber'  => $hcInfo->demandnumber,
                ];
                (new HcServer($this->lang, $this->timezone))->updateHc($updateHc);
            }
            //菲律宾 要去做个人代理  锁HC
        } catch (BusinessException  $be) {
            $db->rollBack(); //回滚
            throw $be;
        } catch (\Exception $e) {
            $db->rollBack(); //回滚
            $this->logger->write_log("addResign error E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString());
            throw new \Exception($e->getMessage());
        }
        //提交事务
        $db->commit();

        if ($isUseFaceCompare) {
            //发送离职问卷消息
            $this->sendLeaveQuestionnaireMessage($staffInfo,$auditId);
        }

        $hold_params = [
            'staff_info_id' => $staffId,//员工id
            'type' =>  '',//hold类型(1.工资hold,2.提成hold,同时就传’1,2’)
            'hold_reason' => 'incomplete_resignation_procedures',//hold原因(BY申请离职)
            'hold_remark' => $reason,//hold备注
            'hold_time' => gmdate('Y-m-d H:i:s', time() + $add_hour * 3600),//hold时间操作时间
            'hold_source' => 5,//来源->离职管理
        ];

        // 如果离职日期在下个月5号之前  同步工资hold
        // 如果离职日期在下个月15号之前 同步提成hold
        $nextMonthType1 = date("Y-m-05", strtotime("+1 month"));
        $nextMonthType2 = date("Y-m-15", strtotime("+1 month"));
        if ($leaveDate <= $nextMonthType1) {
            $hold_params['type'] = "1,2";
        }else if ($leaveDate <= $nextMonthType2) {
            $hold_params['type'] = "2";
        }

        if (!empty($hold_params['type']) && $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            $hold_params['type'] = "2";
        }

        $this->getDI()->get('logger')->write_log('同步离职申请数据到BI:request:'.json_encode($hold_params), 'info');
        if (!empty($hold_params['type'])) {
            $hcm_rpc = (new ApiClient('hcm_rpc', '', 'synchronize_hold'));
            $hcm_rpc->setParams($hold_params);
            $hcm_return = $hcm_rpc->execute();
            $this->getDI()->get('logger')->write_log('同步离职申请数据到BI:request:' . json_encode($hold_params) . ';response:' . json_encode($hcm_return),
                'info');
        }

        //同步状态
        $this->syncApproval([
            'approval_status'   => self::$current_state ?: enums::$audit_status['panding'],
            'submitter_id'      => $staffId,
            'leave_date'        => $leaveDate,
            'apply_resign_time' => $now,
        ]);

        return $this->checkReturn(['data'=>['resign_id' => $auditId]]);
    }

    /**
     * 发送离职问卷
     * @param $staff_info_id
     * @param $auditId
     * @return bool
     */
    public function sendLeaveQuestionnaireMessage($staffInfo, $auditId): bool
    {
        $staff_info_id = $staffInfo['staff_info_id'];
        $lang                        = (new StaffServer())->getLanguage($staff_info_id);
        $id                          = time() . $staff_info_id . rand(1000000, 9999999);
        $param['staff_users']        = [$staff_info_id];//数组 多个员工id
        $param['message_title']      = $this->getTranslation($lang)->_('leave_questionnaire_msg_title');
        $param['message_content']    = '';
        $param['staff_info_ids_str'] = $staff_info_id;
        $param['id']                 = $id;
        $param['category']           = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_LEAVE_QUESTIONNAIRE');
        $messageRes                  = (new MessageCourierServer())->add_kit_message($param);
        if ($messageRes[1] == 1) {

            $allDept                    = SysDepartmentModel::find([
                'columns' => 'id,name,ancestry_v3,type,ancestry',
            ])->toArray();
            $allDept                    = array_column($allDept, null, 'id');
            $departmentLevelInfo                  = (new SysDepartmentServer())->getDepartmentLevelNamesV2($staffInfo['node_department_id'],
                $allDept);
            $solidData['group_ceo'] = 'GroupCEO';
            $solidData['c_level'] = $departmentLevelInfo[0] ?? '-';
            $solidData['bu'] = $departmentLevelInfo[1] ?? '-';
            $solidData['dept_level_1'] = $departmentLevelInfo[2] ?? '-';
            $solidData['dept_level_2'] = $departmentLevelInfo[3] ?? '-';
            $solidData['dept_level_3'] = $departmentLevelInfo[4] ?? '-';
            $solidData['dept_level_4'] = $departmentLevelInfo[5] ?? '-';
            $solidData['store_name'] = '';
            $solidData['region_name'] = '';
            $solidData['piece_name'] = '';
            $solidData['position_type'] = '';
            $solidData['manager_name'] = '';
            $solidData['manager_id'] = $staffInfo['manger'];
            if ($staffInfo['manger']) {
                $managerInfo               = (new StaffRepository())->getStaffInfoOne($staffInfo['manger'], 'name');
                $solidData['manager_name'] = $managerInfo['name'] ?? '';
            }
            $solidData['job_title_grade_v2'] = $staffInfo['job_title_grade_v2'];
            $solidData['week_working_day']   = $staffInfo['week_working_day'];
            $solidData['rest_type']          = $staffInfo['rest_type'];
            $solidData['formal']             = $staffInfo['formal'];
            $solidData['hire_type']          = $staffInfo['hire_type'];
            $jobTitle                        = JobTitleRepository::getJobTitleInfo($staffInfo['job_title']);
            $solidData['job_title_name']     = $jobTitle ? $jobTitle->job_name : '';
            if ($staffInfo['sys_store_id'] != '-1') {
                $storeInfo                = (new SysStoreServer())->getStoreRegionPieceInfo($staffInfo['sys_store_id']);
                $solidData['store_name']  = $storeInfo['store_name'] ?? '';
                $solidData['region_name'] = $storeInfo['region_name'] ?? '';
                $solidData['piece_name']  = $storeInfo['piece_name'] ?? '';
            } else {
                $solidData['store_name'] = enums::HEAD_OFFICE;
            }
            $positionType = HrJobDepartmentRelationModel::findFirst([
                'columns'    => 'position_type',
                'conditions' => 'department_id = :department_id: and job_id = :job_id:',
                'bind'       => [
                    'department_id' => $staffInfo['node_department_id'],
                    'job_id'        => $staffInfo['job_title'],
                ],
            ]);
            if ($positionType) {
                $solidData['position_type'] = $positionType->position_type;
            }

            $questionnaireModel                = new StaffLeaveQuestionnaireModel();
            $questionnaireModel->staff_info_id = $staff_info_id;
            $questionnaireModel->resign_id     = $auditId;
            $questionnaireModel->msg_id        = $id;
            $questionnaireModel->solid_data    = json_encode($solidData, JSON_UNESCAPED_UNICODE);
            $questionnaireModel->save();
        }
        return true;
    }


    /**
     * 获取问卷详情
     * @param $params
     * @return array
     */
    public function getStaffLeaveQuestionnaireDetail($params): array
    {
        if (empty($params['msg_id'])) {
            return [];
        }
        $questionnaireModel = StaffLeaveQuestionnaireModel::findFirst([
            'conditions' => 'msg_id = :msg_id:',
            'bind'       => ['msg_id' => $params['msg_id']],
        ]);
        if ($questionnaireModel) {
            return $questionnaireModel->toArray();
        }
        return [];
    }


    /**
     * 离职问卷详情
     * @return array
     * @throws Exception
     */
    public function questionnaireInfo(): array
    {
        //获取题干
        $questionnaireData = LeaveQuestionnaireModel::find([
            'conditions' => 'deleted = 0 and pid = 0',
            'columns'    => 'id,t_key,type,max_length',
            'order'      => 'sort asc,id asc',
        ])->toArray();
        if (empty($questionnaireData)) {
            throw new Exception('问卷信息不存在');
        }
        $questionnaireOptionData = [];
        //获取选项
        $questionnaireOptionDataTmp = LeaveQuestionnaireOptionModel::find([
            'conditions' => 'deleted = 0',
            'columns'    => 'id,leave_questionnaire_id,t_key,score',
            'order'      => 'leave_questionnaire_id asc,sort asc',
        ])->toArray();
        if (empty($questionnaireOptionDataTmp)) {
            throw new Exception('问卷选项不存在');
        }
        foreach ($questionnaireOptionDataTmp as $value) {
            $value['description'] = $value['score'];
            $questionnaireOptionData[$value['leave_questionnaire_id']][] = $value;
        }

        //获取子问题
        $subQuestionnaireDataTmp = LeaveQuestionnaireModel::find([
            'conditions' => 'deleted = 0 and pid > 0',
            'columns'    => 'id,t_key,type,max_length,pid',
            'order'      => 'sort asc,id asc',
        ])->toArray();

        $t = $this->getTranslation();

        $subQuestionnaireData = [];
        foreach ($subQuestionnaireDataTmp as &$subQuestionnaireDatum) {
            $subQuestionnaireDatum['title']                        = $t->_($subQuestionnaireDatum['t_key']);
            $subQuestionnaireDatum['value']                        = null;
            $subQuestionnaireDatum['options']                      = $questionnaireOptionData[$subQuestionnaireDatum['id']] ?? [];
            $subQuestionnaireDatum['child']                        = [];
            $subQuestionnaireData[$subQuestionnaireDatum['pid']][] = $subQuestionnaireDatum;
        }
        foreach ($questionnaireData as &$item) {
            $item['title']   = $t->_($item['t_key']);
            $item['options'] = $questionnaireOptionData[$item['id']] ?? [];
            $item['value']   = null;
            $item['child']   = $subQuestionnaireData[$item['id']] ?? [];
        }
        return $questionnaireData;
    }


    /**
     * 过滤问卷数据
     * @param $questionnaire_data
     * @return array
     */
    protected function filterQuestionnaireData($questionnaire_data): array
    {
        $questionnaireOptionDataTmp = LeaveQuestionnaireOptionModel::find([
            'conditions' => 'deleted = 0',
            'columns'    => 'id,leave_questionnaire_id,t_key,score',
            'order'      => 'leave_questionnaire_id asc,sort asc',
        ])->toArray();
        $questionnaireOptionDataTmp = array_column($questionnaireOptionDataTmp, 'score', 'id');

        $filter_data = [];
        foreach ($questionnaire_data as $v) {

            if ($v['type'] == LeaveQuestionnaireModel::TYPE_SELECT) {
                $filter_data[] = ['leave_questionnaire_id' => $v['id'], 'value' => $questionnaireOptionDataTmp[$v['value']]];
            } elseif ($v['type'] == LeaveQuestionnaireModel::TYPE_INPUT) {
                $filter_data[] = ['leave_questionnaire_id' => $v['id'], 'value' => $v['value']];
            } elseif ($v['type'] == LeaveQuestionnaireModel::TYPE_MULTIPLE_INPUT) {
                foreach ($v['child'] as $item) {
                    $filter_data[] = ['leave_questionnaire_id' => $item['id'], 'value' => $item['value']];
                }
            }
        }
        return $filter_data;
    }
    /**
     * 保存问卷
     * @param $staffId
     * @param $msg_id
     * @param $questionnaire_data
     * @return bool
     * @throws BusinessException
     */
    public function questionnaireSave($staffId, $msg_id, $questionnaire_data): bool
    {
        $filter_data = $this->filterQuestionnaireData($questionnaire_data);
        if (empty($filter_data)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//未找到数据
        }

        $db = StaffLeaveQuestionnaireModel::beginTransaction($this);
        try {
            $filter_data        = json_encode($filter_data, JSON_FORCE_OBJECT);
            $questionnaireModel = StaffLeaveQuestionnaireModel::findFirst([
                'conditions' => 'msg_id = :msg_id: and staff_info_id = :staff_info_id:',
                'bind'       => ['msg_id' => $msg_id, 'staff_info_id' => $staffId],
            ]);
            if ($questionnaireModel) {
                $update_sql = "update staff_leave_questionnaire set is_new = 0,updated_at = updated_at where staff_info_id = ? and msg_id != ?";
                $this->getDI()->get('db')->execute($update_sql, [$staffId,$msg_id]);
                $questionnaireModel->detail    = $filter_data;
                $questionnaireModel->state     = StaffLeaveQuestionnaireModel::STATE_SUBMIT;
                $questionnaireModel->is_submit = 1;
                $questionnaireModel->is_new    = 1;
                $questionnaireModel->save();
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }

        (new BackyardServer($this->lang, $this->timezone))->has_read_operation($msg_id, true);
        return true;
    }


    /**
     * @param $audit_id
     * by 撤销驳回  更新离职消息已读
     */
    public function readLeaveMsg($audit_id){
        $detail = $this->getResignDetail(['resign_id' => $audit_id]);
        $staff_id=$detail['submitter_id'];
        // 主管离职资产确认  更新消息
        $msgAssetInfo=MsgAssetModel::find(
            [
                'conditions' => 'staff_id = :staff_id:  and type = :type:',
                'bind'       => ['staff_id' => $staff_id,'type' => '1'],
            ]
        )->toArray();
        if($msgAssetInfo){
            foreach ($msgAssetInfo as $info){
                $server=new BackyardServer($this->lang,$this->timezone);
                $rs = $server->has_read_operation($info['msg_id'],true);
            }

        }
    }
    /**
     *
     * 获取审批详情详细信息
     *
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     *
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $server = new \FlashExpress\bi\App\Server\ResignServer($this->lang, $this->timezone);
        $result = $server->getResignDetail(['resign_id' => $auditId]);
        $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }

        $detailLists = [
            'apply_parson'     => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? ''),
            'apply_department' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
        ];

        // 展示大区片区网点和手机信息逻辑
        $is_show_region_piece = false; //默认不展示
        if (
            isCountry('TH') &&
            $staff_info['sys_store_id'] != '-1' &&
            !empty($staff_info['ancestry_v3']) &&
            (
                in_array(SysDepartmentModel::NETWORK_MANAGEMENT_TH,explode('/', $staff_info['ancestry_v3'])) ||
                in_array(SysDepartmentModel::RETAIL_MANAGEMENT_TH,explode('/', $staff_info['ancestry_v3']))
            )
        ){
            $is_show_region_piece = true;
        }
        if (
            isCountry('MY') &&
            $staff_info['sys_store_id'] != '-1' &&
            !empty($staff_info['ancestry_v3']) &&
            (
                in_array(SysDepartmentModel::NETWORK_MANAGEMENT_MY,explode('/', $staff_info['ancestry_v3'])) ||
                in_array(SysDepartmentModel::RETAIL_MANAGEMENT_MY,explode('/', $staff_info['ancestry_v3']))
            )
        ){
            $is_show_region_piece = true;
        }
        if (
            isCountry('PH') &&
            $staff_info['sys_store_id'] != '-1' &&
            !empty($staff_info['ancestry_v3']) &&
            in_array(SysDepartmentModel::NETWORK_MANAGEMENT_PH,explode('/', $staff_info['ancestry_v3']))
        ){
            $is_show_region_piece = true;
        }
        if ($is_show_region_piece){
            $organizationRegionPiece = [];
            if (!empty($staff_info['store_id'])){
                $organizationRegionPiece = (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getOrganizationRegionPieceManagerId($staff_info['store_id']);
            }
            // 申请人所属区域
            $detailLists['apply_store_area'] = !empty($staff_info['store_area_name']) ? $staff_info['store_area_name'] : '-';
            // 申请人所属大区
            $detailLists['apply_region'] = !empty($organizationRegionPiece['region_name']) ? $organizationRegionPiece['region_name'] : '-';
            // 申请人所属片区
            $detailLists['apply_piece'] = !empty($organizationRegionPiece['piece_name']) ? $organizationRegionPiece['piece_name'] : '-';
            // 申请人所属网点
            $detailLists['apply_store'] = !empty($staff_info['store_name']) ? $staff_info['store_name'] : '-';
            // 申请人个人号码
            $detailLists['apply_mobile'] = !empty($staff_info['mobile']) ? $staff_info['mobile'] : '-';
        }
        $detailLists['hire_date'] = $result['hire_date'] ?? '';

        if (isCountry('My')) {
            if (!empty($result['resignation_notice'])) {
                $tmp = explode(" ", $result['resignation_notice']);
                $detailLists['resignation_notice_day'] = $tmp[0]. $this->getTranslation()->_($tmp[1]);
            }
        }

        $detailLists['last_work_date'] = $result['last_work_date'];
        $detailLists['leave_date'] = $result['leave_date'];
        if (isCountry('My')) {
            $detailLists['leave_date'] = [];
            $detailLists['leave_date']['value'] = $result['leave_date'] ;
            if (!empty($result['resignation_leave_day']) && $result['resignation_leave_day'] > $result['leave_date']) {
                $diffDays =
                round((strtotime($result['resignation_leave_day']) - strtotime($result['leave_date'])) / (86400));

                $detailLists['leave_date']['remark'] =
                    sprintf($this->getTranslation()->_('resignation_notice'), $result['resignation_work_day'], $result['resignation_leave_day'], $diffDays, $result['leave_date']);
            }
        }

        $detailLists['work_handover'] = $result['work_handover_text'];
        $detailLists['leave_reason'] = $result['reason_text'];
        $detailLists['leave_remark'] = $result['remark'];

        if(in_array($result['reason'],[StaffLeaveReasonModel::CODE_INDIVIDUAL_CONTRACTOR,StaffLeaveReasonModel::LEAVE_REASON_96])){
            unset( $detailLists['leave_remark']);
            if (!empty($result['agent_job_title'])) {
                $jobTitleInfo                   = JobTitleRepository::getJobTitleInfo($result['agent_job_title']);
                $detailLists['individual_contractor_job_title_name'] = $jobTitleInfo ? $jobTitleInfo->job_name : '';
            }
            if (!empty($result['agent_mobile'])) {
                $detailLists['individual_contractor_person_mobile'] = $result['agent_mobile'];
            }
        }

        $detailLists['staff_sign'] =  $result['image_path'] ?? '';



        empty($result['vehicle_images_path']['vehicle_images']) ? '' : $detailLists['resign_vehicle_images'] = $result['vehicle_images_path']['vehicle_images'];
        empty($result['vehicle_images_path']['vehicle_test_images']) ? '' : $detailLists['resign_vehicle_test_images'] = $result['vehicle_images_path']['vehicle_test_images'];
        empty($result['vehicle_images_path']['vehicle_claim_images']) ? '' : $detailLists['resign_vehicle_claim_images'] = $result['vehicle_images_path']['vehicle_claim_images'];

        //驳回状态，需要显示驳回原因
        if ($result['status'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
        }
        //撤销状态，需要显示撤销原因
        if ($result['status'] == enums::$audit_status['revoked']) {
            $detailLists = array_merge($detailLists, ["cancel_reason" => $result['cancel_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        $approvals = AuditApprovalModel::find([
            'conditions' => "biz_value = :value: and biz_type = :type: and deleted = 0",
            'bind' => [
                'type'  => enums::$audit_type['RN'],
                'value' => $auditId,
            ],
            'order' => 'id asc',
        ]);
        $approvals = $approvals ? $approvals->toArray() : [];

        foreach($approvals as $v){
            $approvalsVal[$v['state']][] = $v;
        }

        $data = [
            'title'      => $this->getTranslation()->_("7010"),
            'id'         => $result['resign_id'],
            'staff_id'   => $staff_info['id'],
            'type'       => (string) enums::$audit_type['RN'],
            'created_at' => $result['created_at'],
            'updated_at' => $result['updated_at'],
            'approvalLevel'=> 0,
            'status_text'=> (new AuditlistRepository($this->lang, $this->timezone))->getAuditStatus('10' . $result['status']),
            'serial_no'  => $result['serial_no'] ?? '',

        ];
        $returnData['data']['head'] = $data;
        $staffLeaveInfo                = $server->getStaffLeaveInfo(['staff_info_id' => $result['submitter_id']]);
        //v15994离职资产:判断离职申请是新的就走新逻辑
        if ($result['asset_tag'] == enums::RESIGN_ASSET_TAG_NEW) {
            $new_assets_server = new MaterialAssetServer($this->lang, $this->timezone);
            if(in_array($result['status'],[enums::$audit_status['approved'],enums::$audit_status['timedout']])){
                $new_assets = $new_assets_server->getAuditLeaveAssets(['resign_id' => $result['resign_id']]);
                $staffLeaveInfo['assets_process_state'] = $new_assets['assets_process_state'];
                $staffLeaveInfo['assets'] = $new_assets['assets'];
            }else{
                $result = $new_assets_server->getAssetsDetailByStaffId(['staff_id'=>$result['submitter_id'],'page_ize'=>1000,'page_size'=>1000,'page_num'=>1]);
                $staffLeaveInfo['assets']= $result['data']['items'];
            }
        }
        $returnData['data']['extend']  = $staffLeaveInfo ?? [];
        $returnData['data']['confirm'] = $result['confirm'] ?? [];

        return $returnData;
    }
    /**
     * 拼组数据
     * @param $list
     * @return array
     */
    public function format($list): array
    {
        $return = [];
        foreach ($list as $key => $v) {
            $return[] = [
                'key'   => $this->getTranslation()->_($key) ?? '',
                'value' => is_array($v) && isset($v['value']) ? $v['value']:$v,
                'tips'=> is_array($v) && isset($v['tips']) ? $v['tips']:null,
                'remark'=> is_array($v) && isset($v['remark']) ? $v['remark']:null,
            ];
        }
        return $return;
    }

    /**
     * @param $staff_id
     */
    public function getResignByStaffId($staff_id){
        return $this->resign->getResignBySubmitterId(array("submitter_id"=>$staff_id),true);
    }

    protected function afterAuditFullOtherData($resignInfo): string
    {
        return '';
    }

    protected function afterFinalExecute($audit_id){
        return true;
    }


    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            self::$current_state = $state;
            $resignInfo = $this->getResignDetail(['resign_id'=>$auditId]);

            $update_field = [
                'source'     => 0,
                'status'     => $state,
                'updated_at' => gmdate('Y-m-d H:i:s'),
            ];

            // 原因
            if ($state == enums::$audit_status['dismissed']) {
                $update_field['reject_reason'] = $extend['remark'];
            } else if ($state == enums::$audit_status['revoked']) {
                $update_field['cancel_reason'] = $extend['remark'];
            }
            $staffServer = (new StaffServer())->getStaffById($resignInfo['submitter_id'],'state');
            $is_resign = $staffServer && $staffServer['state'] == HrStaffInfoModel::STATE_RESIGN;
            //提交申请就同步数据到FBI
            if ($state == enums::$audit_status['approved'] || $state == enums::$audit_status['timedout']) {

                //离职不做后续处理
                if ($is_resign) {
                    $this->getDI()->get('db')->updateAsDict('staff_resign',
                        $update_field,
                        [
                            'conditions' => "resign_id = ?",
                            'bind'       => [$resignInfo['resign_id']],
                        ]);
                    return true;
                }
                $params                    = [
                    'staff_info_id'       => $resignInfo['submitter_id'],
                    'wait_leave_date'     => $resignInfo['leave_date'],
                    'leave_reason'        => $resignInfo['reason'],
                    'leave_scenario'      => 0,
                    'leave_source'        => 5, //backyard提交申请
                    'leave_type'          => 1,
                    'leave_reason_remark' => $resignInfo['remark'],
                    'project_source'      => 'by',
                ];
                $params['staff_resign_id'] = $auditId;
                $params['work_handover']   = $resignInfo['work_handover'];
                //这里会调用OA 资产
                $rpcClient                 = new ApiClient('hr_rpc', '', 'hr_staff_wait_leave');
                $rpcClient->setParams($params);
                $resultData = $rpcClient->execute();
                if ($resultData['result'] !== true && !is_array($resultData['result'])) {
                    $this->getDI()->get("logger")->write_log([
                        'staff_id' => $resignInfo['submitter_id'],
                        'result'   => $resultData['result'],
                    ]);
                }
                $server = Tools::reBuildCountryInstance($this,[$this->lang, $this->timezone]);
                //审批通过或者超时 需要执行的事
                $server->afterFinalExecute($auditId);

            }

            //同步状态
            !$is_resign && $this->syncApproval([
                'approval_status'   => $state,
                'submitter_id'      => $resignInfo['submitter_id'],
                'leave_date'        => $resignInfo['leave_date'],
            ]);

            //要去做个人代理的，撤销\驳回 返还HC
            if (isCountry('PH') && in_array($state,[enums::$audit_status['dismissed'],enums::$audit_status['revoked']]) &&  $resignInfo['re_employment_hc_id'] > 0) {
                //查询当前关联的HC
                $hcInfo = HrHcModel::findFirst([
                    'conditions' => 'hc_id = :hc_id:',
                    'columns'    => 'surplusnumber,demandnumber,hc_id',
                    'bind'       => ['hc_id' => $resignInfo['re_employment_hc_id']],
                ]);
                if ($hcInfo) {
                    (new HcServer($this->lang, $this->timezone))->rollBackHc([
                        'id'            => $resignInfo['re_employment_hc_id'],
                        'surplusnumber' => $hcInfo->surplusnumber,
                        'demandnumber'  => $hcInfo->demandnumber,
                    ]);
                }
            }

            //审批通过  发送离职通知  MY 重写
            if ($state == enums::$audit_status['approved']) {
                $update_field['leave_acceptance_book'] = $this->afterAuditfullOtherData($resignInfo);
            }

            $this->getDI()->get('db')->updateAsDict('staff_resign',
                $update_field,
                [
                    'conditions' => "resign_id = ?",
                    'bind'       => [$resignInfo['resign_id']],
                ]);
            //驳回和撤销 要撤销问卷
            if (in_array($state, [enums::APPROVAL_STATUS_REJECTED, enums::APPROVAL_STATUS_CANCEL])) {
                $this->cancelLeaveQuestionnaire($auditId);
            }
        }

        return true;
    }

    /**
     * 撤销离职问卷
     * @param $auditId
     * @return void
     */
    protected function cancelLeaveQuestionnaire($auditId)
    {
        $questionnaireModel = StaffLeaveQuestionnaireModel::findFirst([
            'conditions' => 'resign_id = :resign_id:',
            'bind'       => ['resign_id' => $auditId],
        ]);
        if ($questionnaireModel) {
            $questionnaireModel->state = StaffLeaveQuestionnaireModel::STATE_CANCEL;
            $questionnaireModel->save();
            (new BackyardServer($this->lang, $this->timezone))->has_read_operation($questionnaireModel->msg_id, true);
        }
    }


    public function genSummary(int $auditId, $user)
    {
        // TODO: Implement genSummary() method.
        $info = StaffResignModel::findFirst([
            "resign_id = :resign_id:",
            "bind" => [
                "resign_id" => $auditId,
            ],
        ]);
        if (!empty($info)) {
            $info = $info->toArray();
            $param = [
                [
                    'key'   => "leave_date",
                    'value' => $info['leave_date'],
                ],
                [
                    'key'   => "work_handover",
                    'value' => $info['work_handover'],
                ],
            ];
        }
        return $param ?? [];
    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        // TODO: Implement getWorkflowParams() method.
        //获取员工是否为特殊类型的申请
        //查询
        $resignInfo = StaffResignModel::findFirst([
                                                      'conditions' => ' resign_id = :resign_id: ',
                                                      'bind' => [
                                                          'resign_id' => $auditId,
                                                      ],
                                                  ]);
	    $resignInfo = $resignInfo ?  $resignInfo->toArray() : [];
        $data = $this->getResignTypes($resignInfo['submitter_id'] ?? 0);
	    $data_v2= $this->getVehicleSource($resignInfo['submitter_id'] ?? 0);

        return [
        	'type'=>$data['type'],
	        'vehicle_source'=>$data_v2['vehicle_source'] ?? 0,// 车辆类型  可视化审批流用
        ];
    }

    /**
     * 更新[包含操作：同意|驳回]离职申请
     * @param array $paramIn 传入参数
     * @return array
     * @throws {Exception}
     */
    public function updateResign($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id');
        $staffName = $this->processingDefault($paramIn, 'staff_name');
        $auditId = $this->processingDefault($paramIn, 'audit_id');
        $status  = $this->processingDefault($paramIn, 'status');
        $reason  = $this->processingDefault($paramIn, 'reject_reason');
        $reason  = addcslashes(stripslashes($reason),"'");  //单引号过滤
        $imagePathArr = $this->processingDefault($paramIn, 'sign_images', 3);

        //[2]验证数据
        $resignInfo = $this->getResignDetail(['resign_id'=>$auditId]);
        if (empty($resignInfo)) {
            throw new \Exception($this->getTranslation()->_('1015'));
        }

        //已经是最终状态,不能修改
        if ($resignInfo['status'] != enums::$audit_status['panding']) {
            throw new \Exception($this->getTranslation()->_('1016'), enums::$ERROR_CODE['1000']);
        }

        //申请人无操作权限
        if ($resignInfo['submitter_id'] == $staffId) {
            throw new \Exception($this->getTranslation()->_('miss_args'), enums::$ERROR_CODE['1000']);
        }

        // 新老数据
        $auditApply = AuditApplyModel::findFirst([
            'conditions' => ' biz_value = :value: and biz_type = :type:',
            'bind' => [
                'value' => $auditId,
                'type' => enums::$audit_type['RN'],
            ],
        ]);
        //签名图片类型
        $osstype = 'RESIGN_AUDIT';


        //[3]处理数据
        if ($auditApply) {
            //验证审批节点
            //查询当前审批节点 的 auditor_level ==11  3. NW Support审批时不需要签字，不需要显示在离职申请书中
            $workFlowNode = WorkflowNodeModel::findFirst([
                                                             'conditions' => ' id = :id: ',
                                                             'bind' => ['id' => $auditApply->getCurrentFlowNodeId()],
                                                         ])->toArray();
            //
            if ($workFlowNode['audit_level'] == 11 && $workFlowNode['auditor_type'] == 2) {
                $osstype = 'RESIGN_AUDIT_NW';
            }

            $approvalServer = new ApprovalServer($this->lang, $this->timezone);
            if ($status == enums::$audit_status['dismissed']) {

                $approvalServer->reject($auditId, AuditListEnums::APPROVAL_TYPE_RN, $reason, $staffId);
            } else if ($status == enums::$audit_status['approved']) {

                $approvalServer->approval($auditId, AuditListEnums::APPROVAL_TYPE_RN, $staffId);
            }

            //插入签名图片
            if(!empty($imagePathArr)) {
                $insertImgData  = [];

                foreach($imagePathArr as $image) {
                    $insertImgData[] = [
                        'id'         => $auditId,
                        'image_path' => $image,
                    ];
                }
                $this->public->batchInsertImgs($insertImgData, $osstype);
            }

        } else{
            throw new \Exception('离职审批出现错误!-- 历史数据!', enums::$ERROR_CODE['1005']);
        }

        return $this->checkReturn([]);
    }

    /**
     * 驳回操作
     * @param int $auditId
     * @return bool
     */
    public function adminReject($auditId)
    {
        $resignInfo = $this->getResignDetail(['resign_id'=>$auditId]);
        if (empty($resignInfo)) {
            throw new \Exception($this->getTranslation()->_('1015'));
        }

        //更新驳回原因、离职申请审批状态
        $paramData = [
            'resign_id'     => $auditId,
            'status'        => enums::$audit_status['dismissed'],
            'reject_reason' => 'ผู้บังคับบัญชาของพนักงานมีการเปลี่ยนแปลง คำขอนี้จึงไม่เป็นผล', //员工直线上级变更，该离职审批失效。
            'staff_id'      => 10000,
            'staff_name'    => 'SystemAuto',
            'audit_id'      => $auditId,//id
        ];

        $result  = $this->updateResign($paramData);

        if (!$result) {
            //撤销sql执行失败已经保存log，此处不需要保存log了
            throw new \Exception($this->getTranslation()->_('4008'));
        }

        //同步状态
        $this->syncApproval([
            'approval_status'   => enums::$audit_status['dismissed'],
            'submitter_id'      => $resignInfo['submitter_id'],
            'leave_date'        => $resignInfo['leave_date'],
        ]);

        //调用push 发消息
        $pushParam = [
            'staff_info_id' => $resignInfo['submitter_id'],
            'message_title' => $this->getTranslation()->_('request_dismissed'),
            'message_content'=> 'เนื่องจากมีการเปลี่ยนแปลงผู้บังคับบัญชาของท่าน คำขอลาออกจึงถูกปฏิเสธโดยอัตโนมัติ กรุณายื่นใหม่อีกครั้ง', //由于您的直线上级变更，原离职审批自动驳回，请提交新的离职申请
        ];
        $this->public->pushMessage($pushParam);

        return true;
    }

    /**
     * 取消离职申请
     * @param array $paramIn 传入参数
     * @return array
     * @throws ValidationException
     * @throws Exception
     */
    public function cancelResign($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id');
        $staffName = $this->processingDefault($paramIn, 'staff_name');
        $auditId = $this->processingDefault($paramIn, 'audit_id');
        $status  = $this->processingDefault($paramIn, 'status');
        $reason  = $this->processingDefault($paramIn, 'cancel_reason');
        $reason  = addcslashes(stripslashes($reason),"'");

        //[2]验证数据
        $resignInfo = $this->getResignDetail(['resign_id'=>$auditId]);

        //验证是不是撤销操作
        if ($status != enums::$audit_status['revoked']) {
            throw new Exception($this->getTranslation()->_('4018'), enums::$ERROR_CODE['1000']);
        }

        //验证审批状态
        //只有待审批的、审批通过的可以撤销
        if (!in_array($resignInfo['status'], [
            enums::$audit_status['panding'],
            enums::$audit_status['approved'],
            enums::$audit_status['timedout'],
        ])) {
            throw new Exception($this->getTranslation()->_('4018'), enums::$ERROR_CODE['1000']);
        }

        // 新老数据
        $auditApply = AuditApplyModel::findFirst([
            'conditions' => ' biz_value = :value: and biz_type = :type:',
            'bind' => [
                'value' => $auditId,
                'type' => enums::$audit_type['RN'],
            ],
        ]);
        if (empty($auditApply)) {
            throw new Exception($this->getTranslation()->_('4018'), enums::$ERROR_CODE['1000']);
        }
        $db = $this->getDI()->get("db");
        //开启事务
        $db->begin();

        try {
            $approvalServer = new ApprovalServer($this->lang, $this->timezone);
            if ($auditApply->submitter_id == $staffId) {
                $approvalServer->cancel($auditId, enums::$audit_type['RN'], $reason, $staffId);
            } else {
                $approvalServer->approvalCancel($auditId, enums::$audit_type['RN'], $reason, $staffId);
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }

        return $this->checkReturn([]);
    }

    /**
     * 超时离职
     * @param array $paramIn 传入参数
     * @return bool
     */
    public function overtimeResign($paramIn = [])
    {
        $resignIds = $this->processingDefault($paramIn, 'resign_ids', 3);

        if (empty($resignIds)) {
            return false;
        }

        foreach ($resignIds as $resignId) {
            $resignInfo = $this->getResignDetail(['resign_id'=>$resignId]);
            //[3]处理更新
            //主状态变更为撤销
            //追加日志
            $logInsertData = [
                'staff_id'      => $resignInfo['submitter_id'],
                'type'          => enums::$audit_type['RN'],
                'original_type' => $resignInfo['status'] ?? 0,
                'to_status_type'=> enums::$audit_status['timedout'],
                'original_id'   => $resignInfo['resign_id'],
                'operator'      => 10000,
                'operator_name' => 'SYSTEM',
            ];

            //更新撤销原因、离职申请审批状态
            $paramData = [
                'resign_id'     => $resignInfo['resign_id'],
                'status'        => enums::$audit_status['timedout'],
            ];
            $result = $this->other->cancelApproval($logInsertData, $paramData, 'staff_resign', 'resign_id');
            if (!$result) {
                continue;
            }

            //同步状态
            $this->syncApproval([
                'approval_status'   => enums::$audit_status['timedout'],
                'submitter_id'      => $resignInfo['submitter_id'],
                'leave_date'        => $resignInfo['leave_date'],
            ]);

            //提交申请就同步数据到FBI
            $postData = [
                'staff_info_id'       => $resignInfo['submitter_id'],
                'wait_leave_date'     => $resignInfo['leave_date'],
                'leave_reason'        => $resignInfo['reason'],
                'leave_scenario'      => 0,
                'leave_type'          => 1,
                'leave_source'        => 5, //backyard提交申请
                'project_source'      => 'by',
                'leave_reason_remark' => $resignInfo['remark'],
            ];


            $fle_rpc = (new ApiClient('hr_rpc','','hr_staff_wait_leave', $this->lang));
            $fle_rpc->setParams($postData);
            $resultData = $fle_rpc->execute();


            $this->getDI()->get('logger')->write_log('同步离职申请数据到BI:request:'.json_encode($postData).';response:'. json_encode($resultData), 'info');
        }
        return true;
    }

    /**
     * 同步审批状态
     * @param array $paramIn
     * @return bool
     */
    public function syncApproval($paramIn = [])
    {
        //同步审批状态
        $data = [
            'approval_status'   => $paramIn['approval_status'],
            'staff_id'          => $paramIn['staff_info_id'] ?? $paramIn['submitter_id'],
            'leave_date'        => $paramIn['leave_date'],
            'leave_source'      => $paramIn['leave_source'] ?? 5,
            'src'               => $paramIn['src'] ?? 1,
            'apply_resign_time' => !empty($paramIn['apply_resign_time']) ? $paramIn['apply_resign_time'] : gmdate('Y-m-d H:i:s'),
        ];
        $this->getDI()->get('logger')->write_log("svc syncApprovalStatus 参数列表:" . json_encode($data), 'info');

        $fle_rpc = (new ApiClient('hcm_rpc','','syncApprovalStatus', $this->lang));
        $fle_rpc->setParams($data);
        $ret =  $fle_rpc->execute();
        return  isset($ret['result']['code']) && $ret['result']['code'] == 1;
    }

    /**
     * 获取离职详情
     */
    public function getResignDetail($paramIn = [])
    {
        //[1]参数定义
        $resignId   = $this->processingDefault($paramIn, 'resign_id', 2);

        //[2]获取详情
        $returnData = $this->resign->getResignInfo(['id'=>$resignId]);
        if ($returnData) {
            //[2.1]解析物品
            //已经去掉
            //需求：员工离职管理优化-update05-22 https://shimo.im/docs/hDtyYxgy93gQqQRY

            //[2.2]获取确认内容
            $returnData['confirm'] = [];
            $info = $this->resign->getConfirmInfo(['resign_id'=>$resignId]);
            if (!empty($info)) {
                if (isset($info[2])) {
                    $returnData['confirm'][] = [
                        'key'   => $this->getTranslation()->_('not_returned_stock'),
                        'value' => $info[2],
                    ];
                }

                if (isset($info[3])) {
                    $returnData['confirm'][] = ['key' => $this->getTranslation()->_('arrears'), 'value' => $info[3]];
                }

                if (isset($info[4])) {
                    $returnData['confirm'][] = [
                        'key'   => $this->getTranslation()->_('not_returned_hro'),
                        'value' => $info[4],
                    ];
                }
            }
            //[2.3]解析原因
            $leaveReasonMap = $this->resign->getLeaveReasonMap();
            $returnData['reason_text'] = $leaveReasonMap[$returnData['reason']];

            //[2.4]解析交接人
            $workHandOverStaffInfo = $this->staff->getStaffpositionV2($returnData['work_handover']);
            if ($workHandOverStaffInfo) {
                $returnData['work_handover_text'] = "{$workHandOverStaffInfo['name']}.({$returnData['work_handover']}) {$workHandOverStaffInfo['job_name']}-{$workHandOverStaffInfo['department_name']}";
            }
        }
        return $returnData;
    }

    /**
     * 离职页面 静态枚举
     * @return array
     */
    public function getStaticEnums($staff_info_id): array
    {
        $staffServer = new StaffServer();
        $staffInfo = $staffServer->getStaffInfo(['staff_info_id'=>$staff_info_id]);
        $enableTransferAgentJobTitle = (new SettingEnvServer)->getSetVal('enable_transition_individual_contractor_job_title',',');
        $notInCode = [];
        //已经是个人代理、不在能转个人代理职位名单的，的不展示转个人代理
        if(isCountry(['TH','PH','MY']) && ($staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID || empty($enableTransferAgentJobTitle) || !in_array($staffInfo['job_title'],$enableTransferAgentJobTitle) )){
            $notInCode = [StaffLeaveReasonModel::LEAVE_REASON_96];
        }
        if ($staffServer->isLntStaff($staff_info_id)) {
            $notInCode = [StaffLeaveReasonModel::LEAVE_REASON_96, StaffLeaveReasonModel::LEAVE_REASON_77];
        }

        $data['individual_contractor_job_title'] = $this->getAgentJobTitle($staffInfo);
        $data['reason']                          = $this->resign->getResignReason($notInCode);
        $data['agent_resign_reason']             = $this->resign->getAgentResignReason();
        $data['goodsPrice']                      = $this->resign->getGoodsPrice();
        return $data;
    }

    /**
     * 个人代理可选职位
     * @return array
     */
    protected function getAgentJobTitle($staffInfo)
    {
        $agentJobTitle = (new SysServer())->getAgentJAllowJobTitleConfig($staffInfo['sys_store_id']);
        if (empty($agentJobTitle)) {
            return [];
        }
        $jobTitleList = (new SysListRepository())->getPositionList(['ids' => $agentJobTitle]);
        $list         = [];
        foreach ($jobTitleList as $item) {
            $list[] = [
                'id'   => strval($item['id']),
                'name' => $item['name'],
            ];
        }
        return $list;
    }


    /**
     * @deprecated
     * 获取物品价格表和离职原因
     */
    public function getGoodsPriceAndResignReason($staff_info_id)
    {
        $staffServer = new StaffServer();
        $staffInfo = $staffServer->getStaffInfo(['staff_info_id'=>$staff_info_id]);
        $data['individual_contractor_job_title'] = $this->getAgentJobTitle($staffInfo);
        $data['reason']     = $this->resign->getResignReason();
        $data['goodsPrice'] = $this->resign->getGoodsPrice();
        return $data;
    }

    /**
     * backyard 未审批的离职申请
     * <AUTHOR> <<EMAIL>>
     * @param $staffId integer 员工ID
     *
     */
    public function getNotApprovals($staffId)
    {

        $data = $this->auditlist->getNotApprovals($staffId);
        $results['count'] = count($data);
        if ($results['count']) {
            $staffNames = $this->staff->getStaffNameByIds(array_column($data, 'submitter_id'));
            $staffNames = array_column($staffNames, null, 'staff_info_id');
            foreach ($data as $staffId => $datum) {
                $results['items'][] = $datum['submitter_id'] . " " . $staffNames[$datum['submitter_id']]['name'] . " " . $datum['leave_date'];
            }
        }

        return $results;
    }

    public function getOutstandingAmount($staffInfoId): array
    {
        $rpcParam['staff_info_id'] = $staffInfoId;
        $ret                       = new ApiClient('oa_rpc', '', 'get_user_outstanding_amount', $this->lang);
        $ret->setParams($rpcParam);
        $rpc_result = $ret->execute();
        $this->logger->write_log("get_user_outstanding_amount: post_data: ".json_encode($rpcParam,
                JSON_UNESCAPED_UNICODE)." result:".json_encode($rpc_result, JSON_UNESCAPED_UNICODE), 'info');

        if (empty($rpc_result['result'])) {
            throw new \Exception("request oa fail");
        }

        $result = $rpc_result['result'];
        if (empty($result['data'])) {
            throw new \Exception("request oa fail");
        }

        return $result['data'];
    }

    /**
     * 获取员工离职信息
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getStaffLeaveInfo($paramIn = [])
    {
        $staffInfoId = $this->processingDefault($paramIn, 'staff_info_id');
        $params = [
            'staff_info_id' => $staffInfoId,
            'locale' => in_array($this->lang, ['th','en','zh-CN']) ? $this->lang : 'en',
        ];
        $fle_rpc = (new ApiClient('hcm_rpc','','get_staff_money_asset_info', $this->lang));
        $fle_rpc->setParams($params);
        $retData = $fle_rpc->execute();
        if (isset($retData['result']['code']) && $retData['result']['code'] == 1) {
            $retData = $retData['result']['data'];
            $result = [
                'is_has_superior'       => $retData['is_has_superior'],
                'assets_remand_state'   => $retData['assets_remand_state'],
                'assets_process_state'  => $retData['assets_process_state'],
                'assets'                => $retData['assets'],
            ];
            $moneyInfo = $this->buildMoneyInfo($retData);
            $result = array_merge($result,$moneyInfo);
        } else {
            $result = [];
        }
        return $result;
    }

    /**
     * 处理钱款信息
     * @param $retData
     * @return array
     */
    protected function buildMoneyInfo($retData): array
    {
        $t            = $this->getTranslation();
        $countryCode  = ucfirst(strtolower(env('country_code', 'Th')));
        $currency_iso = '(' . enums::$country_conf[$countryCode]['currency_iso'] . ')';
        if (isCountry('PH')) {
            $result = [
                'money_process_state_ar' => $retData['money_info_ar']['money_process_state'],
                'money_process_info_ar'  => [
                    [
                        'key'   => $t->_('resign_arrears') . $currency_iso,
                        'value' => $this->formatMoney($retData['money_info_ar']['courier_money']??0),
                    ],
                    [
                        'key'   => $t->_('resign_cashier') . $currency_iso,
                        'value' => $this->formatMoney($retData['money_info_ar']['cashier_money']??0),
                    ],
                    [
                        'key'   => $t->_('22271_delivered_not_return_cod') . $currency_iso,
                        'value' => $this->formatMoney($retData['money_info_ar']['delivered_not_update_total_amount']??0),
                    ],
                    [
                        'key'   => $t->_('resign_other') . $currency_iso,
                        'value' => $this->formatMoney($retData['money_info_ar']['unpaid_money'] ?? 0),
                    ],
                    [
                        'key'   => $t->_('resign_total') . $currency_iso,
                        'value' => $this->formatMoney($retData['money_info_ar']['all_price'] ?? 0),
                    ],
                ],
                'money_process_state_ap' => $retData['money_info_ap']['money_process_state'],
                'money_process_info_ap'  => [
                    [
                        'key'   => $t->_('loan_not_return') . $currency_iso,
                        'value' => $this->formatMoney($retData['money_info_ap']['loan_not_return']??0),
                    ],
                    [
                        'key'   => $t->_('reserve_fund') . $currency_iso,
                        'value' => $this->formatMoney($retData['money_info_ap']['reserve_fund']??0),
                    ],
                    [
                        'key'   => $t->_('resign_other') . $currency_iso,
                        'value' => $this->formatMoney($retData['money_info_ap']['unpaid_money'] ?? 0),
                    ],
                    [
                        'key'   => $t->_('resign_total') . $currency_iso,
                        'value' => $this->formatMoney($retData['money_info_ap']['all_price'] ?? 0),
                    ],
                ],
            ];
        } elseif (isCountry('TH')) {
            $result = [
                'money_process_state' => $retData['money_process_state'],
                'money_process_info'  => [
                    [
                        'key'   => $t->_('resign_arrears') . $currency_iso,
                        'value' => $this->formatMoney($retData['courier_money']),
                    ],
                    [
                        'key'   => $t->_('resign_cashier') . $currency_iso,
                        'value' => $this->formatMoney($retData['cashier_money']),
                    ],
                    [
                        'key'   => $t->_('22271_delivered_not_return_cod') . $currency_iso,
                        'value' => $this->formatMoney($retData['delivered_not_update_total_amount']??0),
                    ],
                    [
                        'key'   => $t->_('loan_not_return') . $currency_iso,
                        'value' => $this->formatMoney($retData['loan_not_return']),
                    ],
                    [
                        'key'   => $t->_('reserve_fund') . $currency_iso,
                        'value' => $this->formatMoney($retData['reserve_fund']),
                    ],

                    [
                        'key'   => $t->_('resign_other') . $currency_iso,
                        'value' => $this->formatMoney($retData['unpaid_money'] ?? 0),
                    ],
                    [
                        'key'   => $t->_('resign_total') . $currency_iso,
                        'value' => $this->formatMoney($retData['all_price'] ?? 0.00),
                    ],
                ],
            ];
        }else{
            $result = [
                'money_process_state' => $retData['money_process_state'],
                'money_process_info'  => [
                    [
                        'key'   => $t->_('resign_arrears') . $currency_iso,
                        'value' => $this->formatMoney($retData['courier_money']),
                    ],
                    [
                        'key'   => $t->_('resign_cashier') . $currency_iso,
                        'value' => $this->formatMoney($retData['cashier_money']),
                    ],
                    [
                        'key'   => $t->_('loan_not_return') . $currency_iso,
                        'value' => $this->formatMoney($retData['loan_not_return']),
                    ],
                    [
                        'key'   => $t->_('reserve_fund') . $currency_iso,
                        'value' => $this->formatMoney($retData['reserve_fund']),
                    ],

                    [
                        'key'   => $t->_('resign_other') . $currency_iso,
                        'value' => $this->formatMoney($retData['unpaid_money'] ?? 0),
                    ],
                    [
                        'key'   => $t->_('resign_total') . $currency_iso,
                        'value' => $this->formatMoney($retData['all_price'] ?? 0.00),
                    ],
                ],
            ];
        }
        return $result;

    }

    protected function formatMoney($money)
    {
        return $money ? bcdiv($money, 100, 2) : 0;
    }

    /**
     * 获取员工的所属部门和职位 车辆来源判断员工离职单展示类型
     *   - 调整：不限部门，职位:Van Courier，车辆来源:【租用公司车辆】的员工
     * @param array $staff_info_id 传入参数 用户 id
     * @return array  type =1 不是特殊类型 2 是特殊类型
     */
    public function getResignTypes($staff_info_id)
    {
        $data = ['type' => 1];
        try {
            $staffInfo    = $this->staff->getStaffpositionV2($staff_info_id);
            if (empty($staffInfo)) {
                throw new \Exception($this->getTranslation()->_('1001'), enums::$ERROR_CODE['1000']);
            }

            if (isCountry('TH')) {
                $data = $this->getResignTypesV2($staffInfo);
            } else {
                $jobTitleId   = VehicleInfoEnums::JOB_VAN_TITLE_ID; //职位 Van Courier
                
                if ($staffInfo['job_title'] == $jobTitleId) {
                    //查询用户的 车联来源
                    $vehicle_model = VehicleInfoModel::findFirst([
                        'conditions' => 'uid = :uid: AND deleted = 0',
                        'bind' => ['uid' => $staff_info_id],
                        'columns' => "id,uid,vehicle_source",
                    ]);
                    $vehicle_model = !empty($vehicle_model) ? $vehicle_model->toArray() : [];
                    //如果租用公司车辆
                    if (isset($vehicle_model['vehicle_source']) && $vehicle_model['vehicle_source'] == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
                        $data['type'] = 2;
                    }
                }
            }

            return $data;

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("ResignSer:getResignTypes-" . $e->getMessage(), 'error');
            return $data;
        }
    }

    public function getResignTypesV2($staffInfo)
    {
        $type = $staffInfo['job_title'] == VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID ? 2 : 1;

        return [
            'type' => $type,
        ];
    }



	/**
	 * @description: 获取员工车辆来源
	 *
	 * @param $staff_info_id   用户 id
	 *
	 * @return     : [vehicle_source=>'车辆来源']
	 * <AUTHOR> L.J
	 * @time       : 2022/3/16 10:34
	 */
	
	public function getVehicleSource($staff_info_id)
	{
		$data = ['vehicle_source' => 0];
		try {
			//查询用户的 车联来源
			$vehicle_model = VehicleInfoModel::findFirst([
				                                             'conditions' => 'uid = :uid: AND deleted = 0',
				                                             'bind'       => ['uid' => $staff_info_id],
				                                             'columns'    => "id,uid,vehicle_source",
			                                             ]);

			if($vehicle_model){
				$data['vehicle_source'] = $vehicle_model->vehicle_source;
			}

		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log("ResignSer:getVehicleSource-" . $e->getMessage(), 'error');
		}
		return $data;


	}

    /**
     * 获取提交人信息
     * @param $staffId
     * @return array
     */
    public function getUserData($staffId): array
    {
        $data['sales_representative']     = 0;
        $data['resignation_advance_days'] = $this->getResignationAdvanceDays($staffId);
        if ($staffId && isCountry(['ph', 'my'])) {
            $kaInfo                       = FleKaProfileModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id: ',
                'bind'       => ['staff_id' => $staffId,],
            ]);
            $data['sales_representative'] = intval(!empty($kaInfo));
        }

        $staffInfo     = (new StaffServer())->getStaffInfo(['staff_info_id' => $staffId], 'personal_email,contract_company_id,formal,hire_type');
        $data['email'] = $staffInfo['personal_email'] ?? '';
        $data['is_lnt'] = (int)StaffServer::isLntCompany($staffId);
        $data['is_alter_tips'] = $this->isUseFaceCompare($staffInfo['formal'],$staffInfo['hire_type']);
        $data['is_use_face_compare'] = $this->isUseFaceCompare($staffInfo['formal'],$staffInfo['hire_type']);
        return $data;
    }

    /**
     * 是否使用人脸比对
     * @param $formal
     * @param $hire_type
     * @return bool
     */
    protected function isUseFaceCompare($formal, $hire_type): bool
    {
        return $formal == HrStaffInfoModel::FORMAL_1 && !in_array($hire_type, HrStaffInfoModel::$agentTypeTogether);
    }


    public function getResignationAdvanceDays($staff_id): int
    {
        $setting = 0;
        if (empty($staff_id) || !isCountry('PH')) {
            return $setting;
        }

        $config = (new SettingEnvServer())->getMultiEnvByCode([
            'resignation_advance_days',
            'resignation_advance_job_title',
        ]);

        $resignation_advance_days      = $config['resignation_advance_days'] ?? 0;
        $resignation_advance_job_title = explode(',', $config['resignation_advance_job_title']);

        $staffInfo = (new StaffServer())->getStaffInfo(['staff_info_id' => $staff_id]);
        if (in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            return $setting;
        }
        if (in_array($staffInfo['job_title'], $resignation_advance_job_title)) {
            return intval($resignation_advance_days);
        }
        return $setting;
    }



    /**
     * @param $myInfo
     * @return string
     */
    public function getSmsContent($myInfo)
    {
        $name=$myInfo['name'];
        $staff_id=$myInfo['staff_info_id'];
        $resignDetail=$this->getResignByStaffId($staff_id);
        if(!empty($resignDetail['leave_date'])){
            $resignDetail['leave_date']=date('d/m/Y',strtotime($resignDetail['leave_date']));
        }
        $leave_date=$resignDetail['leave_date']?? '';
        $assetServer= new AssetServer($this->lang,$this->timezone);
        $assetList=$assetServer->assetsByStaffId($staff_id);
        $assets=array_merge($assetList['public_goods'],$assetList['personal_goods']);
        $amount=0;
        $asset_name="";
        foreach ($assets as $k=> $item){
            $amount+=($item['price']*$item['num']);
            $num=$k+1;
            $asset_name.=$num.'.'.$item['name'].' ';

        }
//        if($asset_name){
//            $asset_name=rtrim($asset_name,',');
//        }
        $asset_name1=$assets[0]['name']??'';
        $asset_name2=$assets[1]['name']??'';
//        $content=' '.$name.' '.$staff_id.' สวัสดีค่ะ วันที่ลาออก '.$leave_date.' รบกวนตรวจสอบและคืนทรัพย์สินดังต่อไปนี้ '.$asset_name.' มูลค่ารวม'.$amount.'THB กรุณาคืนทรัพย์สินถึงสำนักงานใหญ่หลังลาออกภายใน7วัน หากมิเช่นนั้นจะส่งผลต่อการจ่ายเงินเดือน/intensiveของท่าน';

        $lang = (new StaffServer())->getLanguage($staff_id);
        $lang = substr($lang, 0, 2);
        if ($lang == 'zh') {
            // 中文
            $content = "您好" . $name . " ，请于离职前归还总价值" . $amount . "THB的资产，以免影响您的薪酬 " .  $this->getShortUrl(env("h5_url") . "zh/assets/assets_list?staff_id=" . $staff_id . "&lang=zh");
        } else if ($lang == 'th') {
            // 泰文
            $content = "สวัสดี " . $name ." กรุณาคืนค่าทรัพย์สินจำนวน " . $amount . " บาทก่อนลาออก เพื่อไม่ให้กระทบต่อเงินเดือนและค่าอินเซนทีฟของท่าน " . $this->getShortUrl(env("h5_url") . "th/assets/assets_list?staff_id=" . $staff_id . "&lang=th");
        } else {
            // 英文
            $content = "Dear " . $name . ", please return the assets with total value of THB " . $amount . "  on or before your last day to avoid your salary and bonus being affected. " . $this->getShortUrl(env("h5_url") . "en/assets/assets_list?staff_id=" . $staff_id . "&lang=en");
        }
        return $content;
    }


    /**
     * 获取离职管理信息
     *
     * @param $resignId
     *
     */
    public function getResignInfo($resignId)
    {
        $staffResignModel = StaffResignModel::findFirst([
            'conditions' => ' resign_id = :id: ',
            'bind' => ['id' => $resignId],
        ]);
        $staffResign = $staffResignModel ? $staffResignModel->toArray() : [];

        if ($staffResign && isset($staffResign['leave_acceptance_book'])) {
            return $staffResign['leave_acceptance_book'];
        }

        return '';
    }


    /**
     * 处理离职资产上级被换掉的原始上级消息
     * @param $currentDay
     */
    public function processResignAssetsMsg($currentDay)
    {
        $pageSize = 50;
        $pageNum = 1;
        $backyardServer = new BackyardServer($this->lang,$this->timezone);
        do {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns("ma.msg_id, ma.staff_id, ma.transfer_id, hsi.manger, ma.created_at");
            $builder->from(['hsi' => HrStaffInfoModel::class]);
            $builder->leftJoin(MsgAssetModel::class, 'hsi.staff_info_id = ma.staff_id and hsi.manger != ma.transfer_id', 'ma'); // 与员工目前上级不一致
            $builder->where("hsi.leave_date >= :leave_date: and ma.msg_id is not null and hsi.formal = 1 and hsi.is_sub_staff = 0", [
                "leave_date" => date("Y-m-d H:i:s", strtotime($currentDay . " - 90 days")),
            ]);
            $builder->limit($pageSize, ($pageNum - 1) * $pageSize);
            $builder->orderBy("ma.created_at DESC");
            $sql = $builder->getQuery()->getSql();
            $this->getDI()->get('logger')->write_log("processResignAssetsMsg: " . json_encode($sql, JSON_UNESCAPED_UNICODE), 'info');
            $assetsMsg = $builder->getQuery()->execute()->toArray();
            if ($assetsMsg) {
                foreach ($assetsMsg as $item) {

                    $result = $backyardServer->has_read_operation($item['msg_id'],true);
                    $this->getDI()->get('logger')->write_log("processResignAssetsMsg: " . json_encode(array_merge($item, ['result' => $result]), JSON_UNESCAPED_UNICODE), 'info');
                    echo json_encode(array_merge($item, ['result' => $result]), JSON_UNESCAPED_UNICODE) . PHP_EOL;
                }
            }
            $pageNum++;
        } while ($assetsMsg);

        return true;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false, false, false, false, false, false);
    }

    /**
     * 可视化超时，按照表单去超时
     * @param $audit_id
     * @return string
     */
    public function getAuditFormOvertimeDate($audit_id): string
    {
        $resignInfo = staffResignModel::findFirst($audit_id);
        if (empty($resignInfo)) {
            return '';
        }
        return $resignInfo->leave_date;
    }
}
