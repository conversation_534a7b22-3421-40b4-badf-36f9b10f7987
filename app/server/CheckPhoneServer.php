<?php

namespace FlashExpress\bi\App\Server;


use DateTime;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffPhoneCheckLogModel;
use FlashExpress\bi\App\Repository\StaffRepository;

class CheckPhoneServer extends BaseServer
{

    /**
     * 验证号码
     * @param $staff_info_id
     * @return array
     */
    public function mobileInfo($staff_info_id): array
    {
        $companyMobileServer         = (new CompanyMobileServer($this->lang, $this->timeZone));
        $result['feedback_category'] = $companyMobileServer->getFeedBackCategory();
        $staffInfo                   = (new StaffServer())->getStaffById($staff_info_id);
        $is_check_company            = $companyMobileServer->checkValidateCompanyMobile($staffInfo['mobile_company']);
        if ($is_check_company) {
            $result['type']   = StaffPhoneCheckLogModel::TYPE_COMPANY;
            $result['mobile'] = $staffInfo['mobile_company'];
            return $result;
        }
        $result['type']   = StaffPhoneCheckLogModel::TYPE_PERSON;
        $result['mobile'] = $staffInfo['mobile'];
        return $result;
    }


    public function checkPunchOut($staff_id): array
    {
        $t      = $this->getTranslation();
        $result = [];
        if (empty($staff_id)) {
            return $result;
        }

        $query = '';
        //子账号换主账号
        if ($master_staff_id = StaffRepository::getMasterStaffIdBySubStaff($staff_id)) {
            $staff_id = $master_staff_id;
            $query    = '?sub_to_master_token=' . $this->setSessionPrefix(self::$sub_to_master_session_prefix)->generateSessionId($master_staff_id);
        }
        $staffInfo = (new StaffServer())->getStaffById($staff_id);

        $settingList = (new SettingEnvServer)->getMultiEnvByCode([
            'phone_check_switch',
            'phone_check_position',
            'phone_check_days',
            'phone_check_online_date',
        ]);
        if (empty($settingList['phone_check_switch'])) {
            return $result;
        }
        if (empty($staffInfo) || $staffInfo['formal'] != HrStaffInfoModel::FORMAL_1) {
            return $result;
        }
        if (!in_array($staffInfo['job_title'], explode(',', $settingList['phone_check_position']))) {
            return $result;
        }

        $checkLog = StaffPhoneCheckLogModel::findFirst([
            'columns'    => 'date_at',
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => ['staff_info_id' => $staffInfo['staff_info_id']],
            'order'      => 'id desc',
        ]);
        //已经检查过了
        if (!empty($checkLog)) {
            $datetime_start = new DateTime($checkLog->date_at);
            $datetime_end   = new DateTime(date('Y-m-d'));
            //没到再次检查日期
            if (!empty($settingList['phone_check_days']) && $datetime_start->diff($datetime_end)->days < $settingList['phone_check_days']) {
                return $result;
            }
        } else {
            //老员工
            $old_data_start = new DateTime($settingList['phone_check_online_date']);
            $old_data_end   = new DateTime(date('Y-m-d'));
            //新入职的 3天内不验证
            $newer_start = new DateTime(substr($staffInfo['hire_date'], 0, 10));
            $newer_end   = new DateTime(date('Y-m-d'));
            if ($newer_start->diff($newer_end)->days < 2 || $staffInfo['staff_info_id'] % 30 > $old_data_start->diff($old_data_end)->days) {
                return $result;
            }
        }

        $jump_url         = 'enterprise-number';
        $is_check_company = (new CompanyMobileServer())->checkValidateCompanyMobile($staffInfo['mobile_company']);
        if (!$is_check_company) {
            $jump_url = 'personal-mobile-phone';
        }
        $base_url                 = env('h5_endpoint');
        $result['business_type']  = 'jump_h5';
        $result['jump_h5_detail'] = [
            'dialog_status'      => 1,
            'dialog_must_status' => 1,
            'dialog_msg'         => $t->_('check_phone_punch_out_message'),
            'dialog_btn_msg'     => $t->_('check_phone_punch_out_btn'),
            'dialog_jump_url'    => $base_url . $jump_url . $query,
        ];
        return $result;
    }

    /**
     * 保存验证号码记录
     * @param $staff_id
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function saveCheckMobile($staff_id, $params): bool
    {
        //没抛异常就是验证通过了
        (new SmsServer($this->lang, $this->timeZone))->checkSmsCode(SmsServer::SMS_BIZ_TYPE_CHECK_MOBILE,
            $params['mobile'],
            $params['verify_code']);

        $model                = new StaffPhoneCheckLogModel();
        $model->staff_info_id = $staff_id;
        $model->date_at       = date('Y-m-d');
        $model->phone_number  = $params['mobile'];
        $model->type          = $params['type'];
        return $model->save();
    }


}