<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;

class EmailServer extends BaseServer
{

    const SMS_BIZ_TYPE_PASSWORD = 4;//修改密码

    public $timezone;

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 获取验证码超时分钟
     * @param $biz_type
     * @return int
     */
    protected function getExpirationDuration($biz_type): int
    {
        switch ($biz_type) {
            case self::SMS_BIZ_TYPE_PASSWORD:
            default:
                $duration = 5;
                break;
        }
        return $duration;
    }


    /**
     * 发送邮箱
     * @param $biz_type
     * @param $email
     * @return string
     */

    protected function getEmailCacheKey($biz_type, $email): string
    {
        return 'tool_mail_' . $biz_type . '_' . md5($email);
    }

    protected function setEmailData($biz_type, $email, $content)
    {
        $cache      = $this->getDI()->get('redisLib');
        $cacheKey   = $this->getEmailCacheKey($biz_type, $email);
        $smsTimeout = $this->getExpirationDuration($biz_type);
        switch ($biz_type) {
            case self::SMS_BIZ_TYPE_PASSWORD:
                $cache->set($cacheKey, $content, $smsTimeout * 60);
                break;
            default:
                break;
        }
    }

    protected function getEmailTitle($staff_id,$biz_type)
    {
        return $this->getTranslation()->_('v2_email_title_tpl_' . $biz_type,['staff_id' => $staff_id]);
    }

    protected function getEmailContent($staff_id,$biz_type, $code, $num)
    {
        return $this->getTranslation()->_('v2_email_content_tpl_' . $biz_type, ['staff_id' => $staff_id,'code' => $code, 'num' => $num]);
    }

    /**
     * 发送内容
     * @param $biz_type
     * @return array
     */
    protected function makeSendEmailContent($staff_id,$biz_type): array
    {
        $code = $title = $content = '';
        switch ($biz_type) {
            case self::SMS_BIZ_TYPE_PASSWORD:
                $code    = verificationCode();
                $title   = $this->getEmailTitle($staff_id,$biz_type);
                $content = $this->getEmailContent($staff_id,$biz_type, $code, $this->getExpirationDuration($biz_type));
                break;
            default:
                break;
        }
        return [$code, $title, $content];
    }

    /**
     *
     * @param $staffId
     * @param $email
     * @param int $biz_type
     * @return mixed|true
     * @throws BusinessException
     * @throws ValidationException
     */
    public function send($staffId,$email, int $biz_type = self::SMS_BIZ_TYPE_PASSWORD)
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new ValidationException($this->getTranslation()->_('please_input_personal_email'));
        }
        [$code, $title, $content] = $this->makeSendEmailContent($staffId,$biz_type);
        $mail_server = new MailServer();
        if ((new StaffServer())->isLntStaff($staffId)) {
            $res = $mail_server->send_mail_lnt($email, $title, $content);
        } else {
            $res = $mail_server->send_mail($email, $title, $content);
        }

        if ($res) {
            $this->setEmailData($biz_type, $email, $code);
            if (RUNTIME != 'pro') {
                return $code;
            }
            return true;
        }
        throw new BusinessException($this->getTranslation()->_('send_email_fail'));
    }

    /**
     * 验证验证码是否正确
     * @throws ValidationException
     */
    public function checkCode($biz_type, $email, $input_code): bool
    {
        $cache    = $this->getDI()->get('redisLib');
        $cacheKey = $this->getEmailCacheKey($biz_type, $email);
        $sendCode = $cache->get($cacheKey);
        if (!$sendCode) {
            throw new ValidationException($this->getTranslation()->_('sms_verify_code_error_3'));//验证码已过期，请重新发送
        }
        if ($sendCode != $input_code) {
            throw new ValidationException($this->getTranslation()->_('sms_verify_code_error_4'));//验证码错误，请重新输入
        }
        return true;
    }
}