<?php
/**
 * Created by <PERSON>p<PERSON>tor<PERSON>.
 * User: nick
 * Date: 2021/8/16
 * Time: 9:59 AM
 */

namespace FlashExpress\bi\App\Server;


use App\Country\Tools;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AttendanceFaceIgnoreStaffModel;
use FlashExpress\bi\App\Models\backyard\AttendanceFaceStoreModel;
use FlashExpress\bi\App\Models\backyard\AttendanceFaceStoreSplitModel;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftHistoryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftModel;
use FlashExpress\bi\App\Models\backyard\HubAttendanceConfigLogModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditRepository;

use FlashExpress\bi\App\Models\backyard\AttendanceHikStoreSettingModel;
use FlashExpress\bi\App\Repository\StaffRepository;

class AttendanceHubServer extends BaseServer{

    const DOOR_IN = 1;//进门
    const DOOR_OUT = 2;//出门

    protected $column = array(
        'id',
        'staff_info_id' ,
        'organization_id' ,
        'organization_type',
        'attendance_date' ,
        'shift_start' ,
        'shift_end' ,
        'working_day',
        'started_at' ,
        'started_state',
        'started_staff_lat',
        'started_staff_lng',
        'started_store_id' ,
        'started_store_lng',
        'started_store_lat',
        'started_clientid' ,
        'started_clientid_num',
        'started_equipment_type',
        'started_os' ,
        'started_path',
        'started_bucket',
        'started_remark',
        'end_at',
        'end_state',
        'end_staff_lat',
        'end_staff_lng',
        'end_store_id',
        'end_store_lng',
        'end_store_lat',
        'end_clientid',
        'end_clientid_num',
        'end_equipment_type',
        'end_os',
        'end_path',
        'end_bucket',
        'end_remark',
        'created_at',
    );

    /**
     * //保存海康刷脸数据
     * @param $data HIK 原始数据
     * @param $attendance_date 跑数据的日期 当天
     * @param $matchStores
     * @return bool
     */
    public function save_hik_data($data, $attendance_date, $matchStores)
    {
        try {
            //存到数据库 是当天 或者 传入日期的 前一天
            $real_date = date('Y-m-d', strtotime("{$attendance_date} -1 day"));
            //数据太多 一次性插入比较好
            if (empty($data)) {
                return false;
            }

            $result = [];
            foreach ($data as $v) {
                $insert = [];
                if (empty($v['jobNo'])) {
                    continue;
                }
                $insert['staff_info_id'] = $v['jobNo'];
                $insert['date_at']       = $real_date;
                $insert['dev_name']      = $v['devName'];
                $insert['door_name']     = $v['doorName'];
                //2021-06-27T02:03:30+07:00 时间需要格式化
                $time_arr             = empty($v['eventTime']) ? [] : explode('T', $v['eventTime']);
                $insert['event_time'] = null;
                if (!empty($time_arr)) {
                    $date                 = $time_arr[0];
                    $time                 = explode('+', $time_arr[1])[0];
                    $insert['event_time'] = date('Y-m-d H:i:s', strtotime("{$date} {$time}"));
                }
                $name                       = empty($v['devName']) ? '' : explode('_', str_replace('-','_',$v['devName']));//海康机器保存的网点名称
                $hikName                    = is_array($name) ? str_replace('ฺ','',strtoupper($name[0])) : '';//取第一个下划线前面的值 发现特殊字符 需要替换下
                $insert['hik_store_name']   = $hikName;
                $insert['flash_store_name'] = $matchStores[$hikName] ?? '';//如果没匹配上 先把hik 的存进去
                $direction                  = 0;//默认未知
                if (strstr(strtoupper($v['doorName']), 'IN')) {
                    $direction = self::DOOR_IN;
                }
                if (strstr(strtoupper($v['doorName']), 'OUT')) {
                    $direction = self::DOOR_OUT;
                }
                $insert['direction'] = $direction;//海康机器对应的 出门还是进门 用in out 区分

                $result[] = $insert;
            }

            $model = new AttendanceRepository($this->lang, $this->timeZone);
            return $model->batch_insert('attendance_hik_data', $result);
        } catch (\Exception $e) {
            $this->logger->write_log("save_hik_data failed ".$e->getMessage());
            return false;
        }
    }

    /**
     * hub 网点的考勤数据 需要 通过 门口刷脸硬件设备 的后台页面抓取 然后导入 考勤数据
     * @param $data_res
     * @param $post_date
     * @return array|bool|string
     * @throws ValidationException
     */
    public function hub_attendance($data_res, $post_date)
    {
        if (empty($data_res)) {
            throw new ValidationException('考勤导入 没有数据'.$post_date);
        }
        $staff_ids = array_column($data_res, 'staff_info_id');//工号

        $this->getDI()->get('logger')->write_log("recover_attendance 员工班次 ".json_encode($staff_ids), 'info');
        $staff_ids = array_map('intval', $staff_ids);
        $staff_ids = array_unique($staff_ids);
        $staff_ids = array_values($staff_ids);
        // 查员工班次
        $server = Tools::reBuildCountryInstance($this,[$this->lang,$this->timeZone]);
        $staffShiftMap = $server->getStaffShiftMap($staff_ids, $post_date);
        if (empty($staffShiftMap)) {
            throw new ValidationException('考勤导入 员工班次没有数据'.$post_date);
        }

        $source_data = [];
        foreach ($data_res as $da) {
            if (!empty($da['staff_info_id'])) {
                $source_data[$da['staff_info_id']]['event_time'][] = $da['event_time'];
                $source_data[$da['staff_info_id']]['store'] = $da['flash_store_name'];//新增 flash 网点id 字段 大写
            }
        }

        if(empty($source_data)){
            throw new ValidationException('考勤导入 员工班次没有数据'.$post_date);
        }

        //数据太大 清空
        $data_res = null;
        //准备导出的数据
        $log_data = $export = [];
        foreach ($source_data as $staff_id => $datum) {
            //如果查询不到班次 干掉
            if (empty($staffShiftMap[$staff_id])) {
                unset($source_data[$staff_id]);
                continue;
            }
            $source = $datum['event_time'];

            sort($source);
            $log['source_data'] = implode(',', $source);

            // 班次拼接对应日期
            $shift_begin = $staffShiftMap[$staff_id]['start'];
            $shift_end   = $staffShiftMap[$staff_id]['end'];
            $start_shift = $post_date.' '.$shift_begin;
            $end_shift   = $post_date.' '.$shift_end;
            // 如果班次跨天
            if (intval(explode(':', $shift_begin)[0]) > intval(explode(':', $shift_end)[0])) {
                $end_key   = date('Y-m-d', strtotime("{$post_date} +1 day"));
                $end_shift = $end_key.' '.$shift_end;
            }

            // 抓取上班时间
            $start_act = $this->get_card_time($source, $start_shift, $end_shift, 1);
            // 抓取下班时间
            $end_act = $this->get_card_time($source, $start_shift, $end_shift, 2);
            // 如果一天只有一个打卡时间,根据班次判定是上班卡还是下班卡
            if ($start_act && $end_act && ($start_act == $end_act)) {
                [$start_act, $end_act] = $this->setStartOrEndByAttendanceTime($staff_id, $post_date, $start_act,
                    $start_shift, $end_shift);
            }
            // 如果上班和下班相差不到一分钟,舍弃上班卡,根据班次判定是上班卡还是下班卡
            if ($start_act && $end_act && (strtotime($end_act) - strtotime($start_act) <= 60)) {
                [$start_act, $end_act] = $this->setStartOrEndByAttendanceTime($staff_id, $post_date, $end_act,
                    $start_shift, $end_shift);
            }

            //整理导入数据
            $r['staff_id']    = $staff_id;
            $r['date']        = $post_date;
            $r['start_time']  = empty($start_act) ? '-' : $start_act;
            $r['end_time']    = empty($end_act) ? '-' : $end_act;
            $r['shift_start'] = $shift_begin;
            $r['shift_end']   = $shift_end;
            $r['start_store'] = $datum['store'];
            $r['end_store'] = $datum['store'];
            $r['shift_id'] = $staffShiftMap[$staff_id]['shift_id'];
            $r['shift_ext_id'] = $staffShiftMap[$staff_id]['shift_ext_id'] ?? 0;
            $export[]         = $r;

            //原始数据和格式化后导入的数据 日志表
            $log['staff_id']    = $staff_id;
            $log['start_time']  = empty($start_act) ? null : $start_act;
            $log['end_time']    = empty($end_act) ? null : $end_act;
            $log['date_at']     = $post_date;
            $log['shift_start'] = $shift_begin;
            $log['shift_end']   = $shift_end;
            $log_data[]         = $log;
        }
        //备份数据源
        if (!empty($log_data)) {
            $model = new AttendanceRepository($this->lang, $this->timeZone);
            $model->batch_insert('staff_attendance_source_data', $log_data);
        }

        $source_data = null;
        $flag        = $this->read_att($export, $post_date);

        // 保存海康考勤配置
        $this->saveHubAttendanceConfigLog($post_date);
        return $flag;
    }

    /**
     * 获取员工班次
     * @param $staff_ids
     * @param $post_date
     * @return array
     */
    public function getStaffShiftMap($staff_ids, $post_date){
        if (empty($staff_ids)) {
            $this->getDI()->get('logger')->write_log("recover_attendance 员工为空 ".json_encode($staff_ids));
            return [];
        }

        // 班次表
        $shift_data = HrStaffShiftModel::find([
            'conditions' => "staff_info_id in ({staff_ids:array})",
            'bind'       => ['staff_ids' => $staff_ids],
        ])->toArray();
        $shift_data = empty($shift_data) ? [] : array_column($shift_data, null, 'staff_info_id');

        // 历史班次表
        $history_shift = HrStaffShiftHistoryModel::find([
            'conditions' => "staff_info_id in ({staff_ids:array}) and shift_day = :shift_day:",
            'bind'       => [
                'staff_ids' => $staff_ids,
                'shift_day' => $post_date,
            ],
        ])->toArray();
        $history_shift = empty($history_shift) ? [] : array_column($history_shift, null, 'staff_info_id');

        // 优先获取考勤表班次
        $att_shift = StaffWorkAttendance::find([
            'conditions' => "staff_info_id in ({staff_ids:array}) and attendance_date = :attendance_date:",
            'bind'       => [
                'staff_ids' => $staff_ids,
                'attendance_date' => $post_date,
            ],
        ])->toArray();
        $att_shift = empty($att_shift) ? [] : array_column($att_shift, null, 'staff_info_id');

        $staffShiftMap = [];
        foreach ($staff_ids as $staff_id) {
            $shift_begin = $shift_end = "";
            $shift_id    = 0;
            // 1.取班次表
            if (!empty($shift_data[$staff_id])) {
                $shift_begin = $shift_data[$staff_id]['start'];
                $shift_end   = $shift_data[$staff_id]['end'];
                $shift_id    = $shift_data[$staff_id]['shift_id'];
                //如果 日期 在班次生效日期之前 取旧班次
                if (!empty($shift_data[$staff_id]['effective_date']) && $shift_data[$staff_id]['effective_date'] > $post_date) {
                    $shift_begin = $shift_data[$staff_id]['last_start'];
                    $shift_end   = $shift_data[$staff_id]['last_end'];
                }
            }
            // 2.取历史班次
            if (!empty($history_shift[$staff_id])) {
                $shift_begin = $history_shift[$staff_id]['start'];
                $shift_end   = $history_shift[$staff_id]['end'];
                $shift_id    = $history_shift[$staff_id]['shift_id'];
            }
            // 3.取考勤班次
            if (!empty($att_shift[$staff_id])) {
                if (!empty($att_shift[$staff_id]['shift_start']) && !empty($att_shift[$staff_id]['shift_end'])) {
                    $shift_begin = $att_shift[$staff_id]['shift_start'];
                    $shift_end   = $att_shift[$staff_id]['shift_end'];
                    $shift_id    = $att_shift[$staff_id]['shift_id'];
                }
            }
            if ($shift_begin && $shift_end) {
                $staffShiftMap[$staff_id] = ['start' => $shift_begin, 'end' => $shift_end, 'shift_id' => $shift_id];
            }
        }
        return $staffShiftMap;
    }

    /**
     * 根据打卡记录判定是上班还是下班
     * @param $staffInfoId
     * @param $attendanceDate //考勤日期
     * @param $attendanceTime //打卡记录 Y-m-d H:i:s
     * @param $startShiftDate //上班班次 Y-m-d H:i:s
     * @param $endShiftDate //下班班次 Y-m-d H:i:s
     * @return string[]
     */
    private function setStartOrEndByAttendanceTime($staffInfoId, $attendanceDate, $attendanceTime, $startShiftDate, $endShiftDate){
        // 查请假记录
        $auditRepository = new AuditRepository($this->lang);
        $leaveInfo = $auditRepository->getStaffLeaveInfoByDate($staffInfoId, $attendanceDate);

        // 有请假时
        if(! empty($leaveInfo)){
            $start = $end = '';
            foreach($leaveInfo as $val){
                // 如果当天有审批通过的上午半天请假
                if($val['type'] == 1){
                    // 打卡时间在班次内作为上班卡，不在班次内作为下班卡
                    if(strtotime($attendanceTime) < strtotime($endShiftDate)) {
                        $start = $attendanceTime;
                    } else {
                        $end = $attendanceTime;
                    }
                }
                // 如果当天有审批通过的下午半天请假
                if($val['type'] == 2){
                    // 打卡时间在班次内作为下班卡，不在班次内作为上班卡
                    if(strtotime($attendanceTime) <= strtotime($startShiftDate)) {
                        $start = $attendanceTime;
                    } else {
                        $end = $attendanceTime;
                    }
                }
            }
            if($start || $end){
                return [$start, $end];
            }
        }
        // 没有请假时
        $res = $this->selectNearestTimeForAttendanceTime($attendanceTime, $startShiftDate, $endShiftDate);
        return $res;
    }

    /**
     * 打卡记录与上下班班次做比较，与上班班次时间较近则作为上班打卡时间，与下班班次较近则作为下班打卡记录。
     * @param $attendanceTime 打卡记录
     * @param $startShiftDate 上班班次
     * @param $endShiftDate 下班班次
     * @return string[]
     */
    private function selectNearestTimeForAttendanceTime($attendanceTime, $startShiftDate, $endShiftDate){
        $start = $end = '';
        $pivot = (strtotime($startShiftDate) + strtotime($endShiftDate))/2;
        if(strtotime($attendanceTime) < $pivot){
            $start = $attendanceTime;
        } else {
            $end = $attendanceTime;
        }
        return [$start, $end];
    }

    /**
     * 同步逻辑：https://l8bx01gcjr.feishu.cn/docs/doccnBLIIcQUj50ED1XUPmfWKre
     * 根据上下班类型，从海康打卡列表里找上班时间或下班时间
     * @param $time_array //海康打卡列表
     * @param $startShiftDate //班次开始时间
     * @param $endShiftDate //班次结束时间
     * @param $type //上下班类型：type 1- 上班  2- 下班
     * @return string
     */
    protected function get_card_time($time_array, $startShiftDate, $endShiftDate, $type){
        $time = '';
        if(empty($time_array)) {
            return $time;
        }
        // 取上班班次/下班班次
        $shift = ($type == 1) ? $startShiftDate : $endShiftDate;
        //班次 前2小时区间 后 4小时
        $shift_start = strtotime($shift) - (3600 * 2);
        $shift_end = strtotime($shift) + (3600 * 2);
        if($type == 2)
            $shift_end = strtotime($shift) + (3600 * 5.5); //又改了 4小时改成5小时 又改了 5。5小时
        if($type == 1)//上班 开始时间 改为前4个小时 由于爆仓 上班太早 抓不到
            $shift_start = strtotime($shift) - (3600 * 4);

        $return = array();
        foreach ($time_array as $time){
            $time_tmp = strtotime($time);
            if($time_tmp >= $shift_start && $time_tmp <= $shift_end)
                $return[] = $time_tmp;
        }

        if(empty($return)){
            //如果上班时间没有 取当天班次开始时间 到 24点 最早一个
            if($type == 1){
                $shift_end = strtotime(date('Y-m-d 23:59:59',strtotime($shift)));
                foreach ($time_array as $time){
                    $time_tmp = strtotime($time);
                    if($time_tmp >= $shift_start && $time_tmp <= $shift_end)
                        $return[] = $time_tmp;
                }
            }

            //如果 下班时间没有 取当天 零点到 下班班次时间 最晚的一个时间点
            if($type == 2){
                $shift_start = strtotime(date('Y-m-d 00:00:00',strtotime($shift)));
                foreach ($time_array as $time){
                    $time_tmp = strtotime($time);
                    if($time_tmp >= $shift_start && $time_tmp <= $shift_end)
                        $return[] = $time_tmp;
                }
            }
        }

        // 如果没找到，在上班班次和下班班次之间找海康数据
        if(empty($return)) {
            $st = strtotime($startShiftDate);
            $et = strtotime($endShiftDate);
            foreach ($time_array as $time){
                $time_tmp = strtotime($time);
                if($time_tmp >= $st && $time_tmp <= $et)
                    $return[] = $time_tmp;
            }
        }

        if(empty($return))
            return '';

        sort($return);
        if($type == 1){
            $time = date('Y-m-d H:i:s', $return[0]);
        }
        if($type == 2){
            $c = end($return);
            $time = date('Y-m-d H:i:s', $c);
        }
        return $time;

    }


    /**
     * @throws ValidationException
     * @throws \Exception
     */
    private function read_att($data_res,$date){
        //需要刷脸数据覆盖的网点配置
        $env_store = SettingEnvModel::findFirst("code = 'hub_face_store'");
        if(empty($env_store) || empty(trim($env_store->set_val)))
            throw new ValidationException( '没有env hub_face_store 数据');
        $limit_store = explode(',', $env_store->set_val);

        //排除 有效期内需要排除的hub网点
        $invalid_store = AttendanceFaceStoreSplitModel::find("date_at = '{$date}'")->toArray();
        if(!empty($invalid_store)){
            $invalid_store = array_column($invalid_store,'store_id');
            $limit_store = array_diff($limit_store,$invalid_store);
            if(empty($limit_store))
                throw new ValidationException('没有需要操作覆盖的网点配置 '.$date);

            $limit_store = array_values($limit_store);
        }


        $add_hour = $this->config->application->add_hour;
        $di = $this->getDI();
        $logger      = $di->get('logger');
        if (empty($data_res)) {
            throw new ValidationException ('没有数据 不用操作');
        }

        //获取所有员工id 去重
        $staff_ids = array_column($data_res, 'staff_id');
        $staff_ids = array_unique($staff_ids);
        $staff_ids = array_map('intval', $staff_ids);

        //获取考勤信息
        $data = StaffWorkAttendance::find(
            [
                "conditions" => "staff_info_id in ({ids:array}) and attendance_date = :date:",
                "columns" => "id,staff_info_id,attendance_date,started_at,end_at 
                ,shift_id,started_store_id,end_store_id
                ,organization_id,organization_type,started_state,end_state,started_remark,end_remark",
                "bind"=>["ids"=>$staff_ids,"date" => $date],
            ]
        )->toArray();
        $att_data = array();
        foreach ($data as $da) {
            $key = $da['staff_info_id'] . '_' . $da['attendance_date'];
            $att_data[$key] = $da;
        }
        $data = null;
        //获取员工所属网点 筛选入职时间
        $store_data = StaffInfoModel::find(
            [
                "conditions" => "id in ({ids:array}) and hire_date < :date:",
                "columns" => "id,organization_id,organization_type",
                "bind"=>["ids" => $staff_ids,"date" => $date],
            ]
        )->toArray();

        $staff_store = array_column($store_data, null, 'id');

        //新增规则 如果 excel没有数据 但是 数据表有数据  清掉这个数据 获取 指定hub  员工
        $hub_staff = StaffInfoModel::find(
            [
                "conditions" => "organization_id in ({store_ids:array}) and organization_type = 1",
                "columns" => "id",
                "bind"=>["store_ids" => $limit_store],
            ]
        )->toArray();

        $hub_staff = array_column($hub_staff, 'id');

        //新增规则 加入白名单 不用刷脸数据覆盖
        $ignoreStaffRes = AttendanceFaceIgnoreStaffModel::find(['columns'=>"staff_info_id", 'conditions' => "is_deleted=0"])->toArray();
        $ignore_list = ! empty($ignoreStaffRes) ? array_column($ignoreStaffRes, 'staff_info_id') : [];
        if(!empty($ignore_list) && is_array($ignore_list))
            $hub_staff = array_diff($hub_staff,$ignore_list);

        //所有hub 网点的 需要用刷脸数据的员工
        $hub_staff = array_values($hub_staff);
        //新增需求 出差打卡期间 不操作删除
        $trip_list  = BusinessTripModel::find([
            'conditions' => "apply_user in ({staff_ids:array}) and start_time <= :date: and end_time >= :date:
                            and status = 2",
            'bind' => [
                'staff_ids'  => $hub_staff,
                'date' => $date
            ],
            'columns' => 'apply_user'
        ])->toArray();
        if(!empty($trip_list)){
            $trip_list = array_column($trip_list,'apply_user');
            $hub_staff = array_diff($hub_staff,$trip_list);
        }
        $staffRe = new StaffRepository($this->lang);

        $del = $update = $diff = $un_in_hub = $back_up = $insert_data = array();
        foreach ($data_res as $v) {
            $excel_key = $v['staff_id'] . '_' . $v['date'];
            //总部员工 记录下 然后跳过 修改规则  以当时考勤 记录的 组织机构为准
            if(!empty($att_data[$excel_key]) && !empty($att_data[$excel_key]['organization_type'])){
                if($att_data[$excel_key]['organization_type'] == 2){//总部员工
                    $diff[] = $v['staff_id'];
                    continue;
                }
                //判断当时 是否在hub 如果不在hub  跳过 后补数据 没有记录网点 按最新网点算
                if(!empty($att_data[$excel_key]['organization_id']) && !in_array($att_data[$excel_key]['organization_id'],$limit_store)){
                    $diff[] = $v['staff_id'];
                    continue;
                }

            }

            //筛选 不属于hub网点的员工 不做操作
            if(!in_array($v['staff_id'],$hub_staff))
                continue;

            //减去 excel存在的员工
            $hub_staff = array_diff($hub_staff,array($v['staff_id']));

            //验证是否白名单用户
            if(!empty($ignore_list) && is_array($ignore_list)){
                if(in_array($v['staff_id'], $ignore_list))
                    continue;
            }

            //查询 当天是否是该员工 working day
            $bi_staff_info = $staffRe->getStaffPosition($v['staff_id']);
            $working_day = empty($bi_staff_info) ? StaffWorkAttendanceModel::IS_NOT_WORKING_DAY : $staffRe->get_is_working_day($bi_staff_info,$date);

            //如果 考勤无记录 跳过  2019-10-08 朝晖新规则 全面以刷脸考勤为准 没有则新增刷脸考勤 数据入库
            if (empty($att_data[$excel_key])){
                if ($v['start_time'] != '-' || $v['end_time'] != '-') {
                    //记录 有可能需要 插入的数据
                    $insert['staff_info_id'] = $v['staff_id'];
                    $insert['attendance_date'] = $date;
                    $insert['working_day'] = $working_day;
                    $insert['started_at'] = $v['start_time'] == '-' ? null : date('Y-m-d H:i:s', strtotime($v['start_time']) - ($add_hour * 3600));
                    $insert['end_at'] = $v['end_time'] == '-' ? null : date('Y-m-d H:i:s', strtotime($v['end_time']) - ($add_hour * 3600));
                    $insert['started_remark'] = 'excel_add';
                    $insert['end_remark'] = 'excel_add';
                    $insert['started_state'] = StaffWorkAttendanceModel::STATE_HUB_ATTENDANCE;
                    $insert['end_state'] = StaffWorkAttendanceModel::STATE_HUB_ATTENDANCE;
                    $insert['organization_type'] = 1;
                    $insert['organization_id'] = empty($staff_store[$v['staff_id']]) ? '' : $staff_store[$v['staff_id']]['organization_id'];
                    $insert['shift_start'] = $v['shift_start'];
                    $insert['shift_end'] = $v['shift_end'];

                    //新增字段
                    $insert['shift_id'] = $v['shift_id'];
                    $insert['shift_ext_id'] = $v['shift_ext_id'];
                    $insert['started_store_id'] = $v['start_store'];
                    $insert['end_store_id'] = $v['end_store'];

                    $insert_data[] = $insert;
                }
                continue;
            }

            //如果存在补卡 补卡相关不动 操作覆盖另外一个
            if(in_array($att_data[$excel_key]['started_state'], [StaffWorkAttendanceModel::STATE_MAKE_UP_CARD, StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY])
                || in_array($att_data[$excel_key]['end_state'], [StaffWorkAttendanceModel::STATE_MAKE_UP_CARD, StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY])){
                //如果都是补卡 不操作
                if(in_array($att_data[$excel_key]['started_state'], [StaffWorkAttendanceModel::STATE_MAKE_UP_CARD, StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY])
                    && in_array($att_data[$excel_key]['end_state'], [StaffWorkAttendanceModel::STATE_MAKE_UP_CARD, StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY]))
                    continue;

                $st = $v['start_time'] == '-' ? null : date('Y-m-d H:i:s', strtotime($v['start_time']) - ($add_hour * 3600));
                $en = $v['end_time'] == '-' ? null : date('Y-m-d H:i:s', strtotime($v['end_time']) - ($add_hour * 3600));
                $st_re = $en_re = 'excel_update';//备注
                $st_s = $en_s = StaffWorkAttendanceModel::STATE_HUB_ATTENDANCE;//状态
                //新增覆盖字段 https://flashexpress.feishu.cn/docx/Q5tidXfpyoTBlFxRzURcG82Tnqb
                $up_data['started_store_id'] = $up_data['end_store_id'] = $v['start_store'];//刷脸 上下班网点是一样的 取一个上班就可以

                //上班补卡了
                if(in_array($att_data[$excel_key]['started_state'], [StaffWorkAttendanceModel::STATE_MAKE_UP_CARD, StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY])){
                    $st = $att_data[$excel_key]['started_at'];
                    $st_re = $att_data[$excel_key]['started_remark'];
                    $st_s = $att_data[$excel_key]['started_state'];
                    unset($up_data['started_store_id']);//有补卡数据了 不更新这字段
                }
                if(in_array($att_data[$excel_key]['end_state'], [StaffWorkAttendanceModel::STATE_MAKE_UP_CARD, StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY])){
                    $en = $att_data[$excel_key]['end_at'];
                    $en_re = $att_data[$excel_key]['end_remark'];
                    $en_s = $att_data[$excel_key]['end_state'];
                    unset($up_data['end_store_id']);
                }
                $back_up[] = $up_data['id'] = $att_data[$excel_key]['id'];
                $up_data['started_at'] =  $st;
                $up_data['end_at'] = $en;
                $up_data['started_remark'] = $st_re;
                $up_data['end_remark'] = $en_re;
                $up_data['started_state'] = $st_s;
                $up_data['end_state'] = $en_s;
                $up_data['shift_id'] = $v['shift_id'];
                $update[] = $up_data;
                continue;
            }

            //如果excel 没记录 清除考勤记录
            if ($v['start_time'] == '-' && $v['end_time'] == '-') {
                //如果 不属于hub 网点 不做清除操作
                if (!in_array($staff_store[$v['staff_id']]['organization_id'], $limit_store)) {
                    $un_in_hub[] = $excel_key;
                } else {
                    $back_up[] = $del[] = $att_data[$excel_key]['id'];
                }
                continue;
            }else{//以excel 刷脸导入数据为准
                $back_up[] = $up_data['id'] = $att_data[$excel_key]['id'];
                $up_data['started_at'] = ($v['start_time'] == '-') ? null : date('Y-m-d H:i:s', strtotime($v['start_time']) - ($add_hour * 3600));
                $up_data['end_at'] = ($v['end_time'] == '-') ? null : date('Y-m-d H:i:s', strtotime($v['end_time']) - ($add_hour * 3600));
                $up_data['started_remark'] = 'excel_single';
                $up_data['end_remark'] = 'excel_single';
                $up_data['started_state'] = StaffWorkAttendanceModel::STATE_HUB_ATTENDANCE;
                $up_data['end_state'] = StaffWorkAttendanceModel::STATE_HUB_ATTENDANCE;

                $up_data['shift_id'] = $v['shift_id'];
                $up_data['started_store_id'] = $v['start_store'];
                $up_data['end_store_id'] = $v['end_store'];

                $update[] = $up_data;
                continue;
            }
        }

        //记录日志 $del = $update = $diff = $un_in_hub = $back_up = $insert_data = array();
        $logger->write_log('考勤导入 需要去掉的员工 ' . json_encode($diff), 'info');
        $logger->write_log('考勤导入刷脸 但是不在hub的员工 ' . json_encode($un_in_hub), 'info');
        $logger->write_log('考勤导入刷脸 需要更新的数据 ' . json_encode($update), 'info');

        $hub_staff = array_diff($hub_staff,$diff);
        //获取 excel 没有打卡的人 但是在by有数据的 打卡记录 人员基数 以excel为准 这些人的by数据要删除
        if(!empty($hub_staff)){
            $logger->write_log('考勤导入 没有刷脸如果有考勤准备删除的员工 ' . json_encode($hub_staff), 'info');
            $hub_staff = array_values($hub_staff);
            $un_sign = StaffWorkAttendance::find(
                [
                    "conditions" => "staff_info_id in ({ids:array}) and attendance_date = :date:",
                    "columns" => "id, started_state, end_state",
                    "bind"=>["ids"=>$hub_staff,"date" => $date],
                ]
            )->toArray();

            //$need_back 需要备份的
            //$need_del 需要删除的
            //$need_update 需要更新的
            $need_back = $need_del = $need_update = [];
            if(!empty($un_sign)){
                $need_back = $need_del = array_column($un_sign,'id', 'id');
                foreach ($un_sign as $oneData) {
                    //上下班卡，都是 补卡。则 不需要 备份，不需要删除
                    if(in_array($oneData['started_state'], [StaffWorkAttendanceModel::STATE_MAKE_UP_CARD, StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY]) &&
                    in_array($oneData['end_state'], [StaffWorkAttendanceModel::STATE_MAKE_UP_CARD, StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY])) {
                        unset($need_back[$oneData['id']]);
                        unset($need_del[$oneData['id']]);
                        continue;
                    }

                    //如果，上班卡是 补卡， 则把 下班卡置为空。需要备份。不需要删除。
                    if(in_array($oneData['started_state'], [StaffWorkAttendanceModel::STATE_MAKE_UP_CARD, StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY])) {
                        $oneUpdate['end_at'] = NULL;
                        $oneUpdate['end_state'] = NULL;
                        $need_update[$oneData['id']] = $oneUpdate;//需要备份，需要更新

                        unset($need_del[$oneData['id']]);//需要备份，不需要删除
                        continue;
                    }

                    //如果，下班卡是 补卡 则把 上班卡置为空。需要备份。不需要删除。
                    if(in_array($oneData['end_state'], [StaffWorkAttendanceModel::STATE_MAKE_UP_CARD, StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY])) {
                        $oneUpdate['started_at'] = NULL;
                        $oneUpdate['started_state'] = NULL;
                        $need_update[$oneData['id']] = $oneUpdate;//需要备份，需要更新

                        unset($need_del[$oneData['id']]);//需要备份，不需要删除
                        continue;
                    }
                }

                $need_back = array_values($need_back);
                $need_del = array_values($need_del);
            }

            $back_up = array_merge($back_up, $need_back);
            $del = array_merge($del,$need_del);
        }

        $di->get('db')->begin();
        try{
            //备份
            if(!empty($back_up)){
                $backup_flag = $this->backup_att($back_up);
                if($backup_flag !== true){//说明 已经操作过覆盖数据 需要还原之后再跑任务
                    //$di->get('db')->rollback();
                    throw new \Exception('考勤导入 需要操作恢复数据后 重新跑!'.$date);
                }
            }

            //存在一个补卡，需要将 另外一个卡的 打卡时间和 打卡状态 更新的 为 null 的
            if(!empty($need_update)) {
                $logger->write_log(['isset_one_card_replacement' => $need_update], 'info');
                foreach ($need_update as $keyId => $oneData) {
                    $di->get('db')->updateAsDict('staff_work_attendance', $oneData,
                        [
                            'conditions' =>"id = '{$keyId}'",
                        ]
                    );
                }
            }

            //删除 by打卡 但是没刷脸的
            if (!empty($del)) {
                $del_str = implode(',', $del);
                $del_sql = "delete from staff_work_attendance where id in ({$del_str})";
                $di->get('db')->execute($del_sql);
                //日志
                $logger->write_log('考勤导入 删除 ' . json_encode($del), 'info');
            }

            //需要用刷脸数据更新 整事考勤的
            if (!empty($update)) {
                $flag = array();
                foreach ($update as $k => $u) {
                    $key = $u['id'];
                    $flag[] = $u['id'];
                    unset($u['id']);
                    $di->get('db')->updateAsDict(
                        'staff_work_attendance',
                        $u,
                        [
                            'conditions' =>"id = '{$key}'",
                        ]
                    );
                }
            }

            //by没有打卡记录 但是有刷脸
            if(!empty($insert_data)){
                $model = new AttendanceRepository($this->lang,$this->timeZone);
                $model->batch_insert('staff_work_attendance',$insert_data);
            }
            $di->get('db')->commit();
            return true;
        }catch (\Exception $e){
            $di->get('db')->rollback();
            $logger->write_log('考勤导入操作异常 ' . $e->getMessage());
            throw $e;
        }
    }

    public function saveHubAttendanceConfigLog($date){
        $ignoreStaffRes = AttendanceFaceIgnoreStaffModel::find(['columns'=>"staff_info_id", 'conditions' => "is_deleted=0"])->toArray();
        $staffList = ! empty($ignoreStaffRes) ? array_column($ignoreStaffRes, 'staff_info_id') : [];
        $staffsStr = ! empty($staffList) ? implode(',', $staffList) : "";

        $storeList = SettingEnvModel::findFirst("code = 'hub_face_store'");
        $configLog = HubAttendanceConfigLogModel::findFirst(["conditions" => " date_at = :date_at: ", "bind" => ["date_at" => $date]]);
        if(empty($configLog)){
            $configLog = new HubAttendanceConfigLogModel();
        }
        $configLog->date_at = $date;
        $configLog->staff_data = $staffsStr;
        $configLog->store_data = ! empty($storeList) && $storeList->set_val ? $storeList->set_val : "";
        $res = $configLog->save();
        return $res;
    }

    //插入备份表  staff_work_attendance_for_import  备份之前 要看看是不是已经操作过
    private function backup_att($rows){
        if(empty($rows))
            return false;

        $id_str = implode(',',$rows);
        $check_sql = "select id from staff_work_attendance_for_import where id in ($id_str)";
        $check = $this->getDI()->get('db')->query($check_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if(!empty($check)){
            $this->logger->write_log('考勤导入 要恢复数据' , 'info');
            return false;
        }

        try{
            $column = $this->column;
            $column_str = "`" . implode("`,`", $column)."`";

            $sql = "insert into staff_work_attendance_for_import  ({$column_str})
                    select {$column_str} from staff_work_attendance  where id in ({$id_str}) ";
            $flag = $this->getDI()->get('db')->execute($sql);
            if($flag)
                $this->logger->write_log('考勤导入备份成功 ' . json_encode($rows), 'info');
            else
                $this->logger->write_log('考勤导入备份失败 ' . json_encode($rows), 'info');

            return $flag;
        }catch (\Exception $e){
            $this->logger->write_log('考勤导入备份失败 ' .$e->getMessage());
            throw $e;
        }

    }


    //海康网点和 flash 网点映射
    public function formatMatchStores()
    {
        $data = AttendanceHikStoreSettingModel::find("is_deleted = 0")->toArray();

        if (empty($data)) {
            return [];
        }

        $return = [];
        foreach ($data as $da) {
            $hikName          = strtoupper($da['hik_store_name']);
            $flashName        = strtoupper($da['flash_store_id']);
            $return[$hikName] = $flashName;
        }
        return $return;
    }


}