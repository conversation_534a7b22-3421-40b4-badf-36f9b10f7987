<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceAttachmentModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkFaceVerifyRecordModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

/**
 * 员工人脸识别记录服务类
 * Class StaffWorkFaceVerifyRecordServer
 * @package FlashExpress\bi\App\Server
 */
class StaffWorkFaceVerifyRecordServer extends BaseServer
{
    /**
     * 创建查询构建器
     * @param array $params
     * @return \Phalcon\Mvc\Model\Query\Builder
     */
    protected function createBuilder($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['f' => StaffWorkFaceVerifyRecordModel::class]);

        // 关联员工信息表
        $builder->leftJoin(HrStaffInfoModel::class, 'f.staff_info_id = s.staff_info_id', 's');
        $builder->leftJoin(StaffWorkAttendanceModel::class, 'sw.staff_info_id = f.staff_info_id and sw.attendance_date = f.attendance_date', 'sw');
        $builder->leftJoin(StaffWorkAttendanceAttachmentModel::class, 'swa.staff_info_id = s.staff_info_id and swa.deleted = 0', 'swa');
        $builder->where('f.image_path=sw.started_path or f.image_path=sw.end_path');
        // 员工工号筛选
        if (!empty($params['staff_info_id'])) {
            $builder->andWhere("f.staff_info_id = :staff_info_id:", ['staff_info_id' => $params['staff_info_id']]);
        }

        // 组织机构ID筛选
        if (!empty($params['organization_id'])) {
            $builder->andWhere("f.organization_id = :organization_id:",
                ['organization_id' => $params['organization_id']]);
        }

        // 组织类型筛选
        if (!empty($params['organization_type'])) {
            $builder->andWhere("f.organization_type = :organization_type:",
                ['organization_type' => $params['organization_type']]);
        }

        // 验证渠道筛选
        if (isset($params['verify_channel']) && $params['verify_channel'] !== '') {
            $builder->andWhere("f.verify_channel = :verify_channel:", ['verify_channel' => $params['verify_channel']]);
        }

        // 验证结果筛选
        if (isset($params['success_enabled']) && $params['success_enabled'] !== '') {
            $builder->andWhere("f.success_enabled = :success_enabled:",
                ['success_enabled' => $params['success_enabled']]);
        }

        // 考勤日期筛选
        if (!empty($params['attendance_date'])) {
            $builder->andWhere("f.attendance_date = :attendance_date:",
                ['attendance_date' => $params['attendance_date']]);
        }

        // 考勤日期范围筛选
        if (!empty($params['start_date'])) {
            $builder->andWhere("f.attendance_date >= :start_date:", ['start_date' => $params['start_date']]);
        }
        if (!empty($params['end_date'])) {
            $builder->andWhere("f.attendance_date <= :end_date:", ['end_date' => $params['end_date']]);
        }

        return $builder;
    }

    /**
     * 获取记录总数
     * @param array $params
     * @return int
     */
    protected function countData($params)
    {
        $builder = $this->createBuilder($params);
        $count   = $builder->columns('count(1) as count')->getQuery()->getSingleResult();
        return intval($count->count);
    }

    /**
     * 获取列表数据
     * @param array $params
     * @return array
     */
    protected function listData($params)
    {
        $t = $this->getTranslation('zh');
        $size   = $params['size'] ?? 20;
        $page   = $params['page'] ?? 1;
        $offset = $size * ($page - 1);

        $builder = $this->createBuilder($params);

        // 查询字段
        $builder->columns([
            'f.id',
            'f.staff_info_id',
            'f.organization_id',
            'f.organization_type',
            'f.attendance_date',
            'f.verify_channel',
            'f.success_enabled',
            'f.image_path',
            'f.image_bucket',
            'f.device_type',
            'f.created_at',
            'f.updated_at',
            's.name as staff_name',
            's.job_title',
            's.sys_store_id',
            's.node_department_id',
            's.formal',
            's.hire_type',
            'sw.started_path',
            'sw.end_path',
            'swa.work_attendance_path',
        ]);

        $builder->limit($size, $offset);
        $builder->orderBy('f.id desc');

        $result = $builder->getQuery()->execute()->toArray();

        // 格式化数据
        $data = [];//人脸底片
        foreach ($result as $item) {
            $attendance_type = $item['image_path'] == $item['end_path'] ? 2 : 1;
            $data[] = [
                'staff_info_id'     => $item['staff_info_id'],
                'attendance_type'   => $attendance_type,
                'name'              => $item['staff_name'] ?? '',
                'job_name'          => $this->showJobTitleName($item['job_title']),
                'store_name'        => $this->showStoreName($item['sys_store_id']),
                'area_name'         => $this->showRegionNameByStoreId($item['sys_store_id']),
                'distinct_name'     => $this->showPieceNameByStoreId($item['sys_store_id']),
                'category'          => $this->showStoreCategoryNameByStoreId($item['sys_store_id']),
                'organization_type' => $item['organization_type'] = 1 ? 'store' : 'department',
                'organization_id'   => $item['organization_id'],
                'formal'            => $t->_('formal_' . $item['formal']),
                'hire_type'         => $item['hire_type'] ? $t->_('hire_type_' . $item['hire_type']) : '',
                'department_name'   => $this->showDepartmentName($item['node_department_id']),
                'attendance_date'   => $item['attendance_date'],
                'verify_channel'    => $item['verify_channel'],
                'success_enabled'   => $item['success_enabled'],
                'device_type'       => $item['device_type'],
                'created_at'        => show_time_zone($item['created_at']),
                'image_path'        => !empty($item['image_path']) ? env('img_prefix') . $item['image_path'] : '',
                'negative_path'     => !empty($item['work_attendance_path']) ? env('img_prefix') . $item['work_attendance_path'] : '',
            ];
        }

        return $data;
    }

    /**
     * 获取人脸识别记录数据
     * @param array $params
     * @return array
     */
    public function getFaceVerifyRecordData(array $params): array
    {
        $result['list']  = $this->listData($params);
        $result['total'] = $this->countData($params);
        return $result;
    }

}
