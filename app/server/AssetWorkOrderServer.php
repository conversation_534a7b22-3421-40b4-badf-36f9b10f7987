<?php
namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\AssetWorkOrderEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AssetWorkOrderModel;
use FlashExpress\bi\App\Models\backyard\AssetWorkOrderReplyRecordModel;
use FlashExpress\bi\App\Models\backyard\AssetWorkOrderTypeModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use Phalcon\Db;

/**
 * 资产工单-服务层
 * Class AssetWorkOrderServer
 * @package FlashExpress\bi\App\Server
 */
class AssetWorkOrderServer extends BaseServer
{
    public function __construct($lang = 'zh-CN')
    {
        parent::__construct($lang);
    }

    /**
     * 根据code获取配置信息
     * @param string $code code
     * @return mixed
     */
    private function getSettingEnvInfo($code)
    {
        return SettingEnvModel::findFirst([
            'conditions' => 'code = :code:',
            'bind'       => ['code' => $code],
            'columns'    => ['set_val'],
        ]);
    }

    /**
     * 资产工单-申请权限-入口判断
     * @param array $user 当前登陆者信息组
     * @return bool
     */
    public function getAssetWorkOrderPermission($user)
    {
        //第一步：检测资产工单-是否开放0未开放，1开放
        $isOpen = $this->getSettingEnvInfo(enums::ASSET_WORK_ORDER_IS_OPEN);
        if (empty($isOpen) || $isOpen->set_val == 0) {
            return false;
        }

        //第二步：检测资产工单-是否开放全员0否，1是
        $isOpenAllStaff = $this->getSettingEnvInfo(enums::ASSET_WORK_ORDER_IS_OPEN_ALL_STAFF);
        //不存在直接返回无权限
        if (empty($isOpenAllStaff)) {
            return false;
        }
        //已开放全员
        if ($isOpenAllStaff->set_val == 1) {
            return true;
        }

        //第三步：检测资产工单-未开放全员-则看是否满足配置的权限范围
        $openStaffRule = $this->getSettingEnvInfo(enums::ASSET_WORK_ORDER_OPEN_RULE);
        //不存在直接返回无权限
        if (empty($openStaffRule)) {
            return false;
        }
        $rule = json_decode($openStaffRule->set_val, true);
        if (empty($rule['job_ids']) || !in_array($user['job_title'], explode(',', $rule['job_ids']))) {
            return false;
        }
        return true;
    }

    /**
     * 资产工单-审批权限-入口判断
     * @param array $user 当前登陆者信息组
     * @return bool
     */
    public function getAuditAssetWorkOrderPermission($user)
    {
        //检测资产工单-是否设置了资产审批规则
        $auditRule = $this->getSettingEnvInfo(enums::ASSET_WORK_ORDER_AUDIT);
        //没设置或者值为空
        if (empty($auditRule) || empty($auditRule->set_val)) {
            return false;
        }
        $rule = json_decode($auditRule->set_val, true);
        //设置了按照工号并且是工号组里的工号，则有审批权限（后期扩展职位或者部门后面直接||）
        if (!empty($rule['staffs']) && in_array($user['staff_id'], explode(',', $rule['staffs']))) {
            return true;
        }
        return false;
    }

    /**
     * 资产工单-获取提交人或审批人小红点数量
     * @param array $user 当前登陆者信息组
     * @param integer $type 0默认全部，1提交人，2审批人
     * @return array
     */
    public function getAssetWorkOrderData($user, $type = AssetWorkOrderEnums::TYPE_ALL)
    {
        $data = [
            //提交人-已回复数
            'asset_work_order_reply_num'  => 0,
            //资产部审核人-待回复数
            'asset_work_order_submit_num' => 0,
            //是否展示资产工单入口
            "is_asset_work_order"         => 0
        ];

        // 提交人-已回复数
        if (in_array($type, [AssetWorkOrderEnums::TYPE_ALL, AssetWorkOrderEnums::TYPE_SUBMIT]) && true === $this->getAssetWorkOrderPermission($user)) {
            $data['asset_work_order_reply_num'] = $this->getCount(['created_staff_id' => $user['id'], 'status' => enums::TICKET_STATUS_REPLY]);
        }

        //资产部-待回复数
        if (in_array($type, [AssetWorkOrderEnums::TYPE_ALL, AssetWorkOrderEnums::TYPE_AUDIT]) && true === $this->getAuditAssetWorkOrderPermission($user)) {
            $data['is_asset_work_order'] = 1;
            $data['asset_work_order_submit_num'] = $this->getCount(['status' => enums::TICKET_STATUS_WAIT_REPLY]);
        }
        return $data;
    }

    /**
     * 获取特定条件下的工单数量
     * @param array $params 查询条件
     * @return int
     */
    public function getCount($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(id) as total');
        $builder->from(AssetWorkOrderModel::class);
        $builder = $this->getCondition($builder, $params);
        return (int)$builder->getQuery()->getSingleResult()->total;
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器对象
     * @param array $params 查询条件
     * @return mixed
     */
    private function getCondition($builder, $params)
    {
        //按照工单ID筛选
        if (!empty($params['id'])) {
            $builder->andWhere('id = :id:', ['id' => $params['id']]);
        }
        //按照提交人筛选
        if (!empty($params['created_staff_id'])) {
            $builder->andWhere('created_staff_id = :created_staff_id:', ['created_staff_id' => $params['created_staff_id']]);
        }
        //按照状态筛选
        if (!empty($params['status'])) {
            if (is_array($params['status'])) {
                $builder->inWhere('status', $params['status']);
            } else {
                $builder->andWhere('status = :status:', ['status' => $params['status']]);
            }
        }
        //按照问题类型筛选
        if (!empty($params['type_id'])) {
            if (is_array($params['type_id'])) {
                $builder->inWhere('type_id', $params['type_id']);
            } else {
                $builder->andWhere('type_id = :type_id:', ['type_id' => $params['type_id']]);
            }
        }
        //按照问题所在仓筛选
        if (!empty($params['store_id'])) {
            if (is_array($params['store_id'])) {
                $builder->inWhere('store_id', $params['store_id']);
            } else {
                $builder->andWhere('store_id = :store_id:', ['store_id' => $params['store_id']]);
            }
        }
        //按照工单编号筛选
        if (!empty($params['order_code'])) {
            $builder->andWhere('order_code like :order_code:', ['order_code' => '%' . $params['order_code'] . '%']);
        }
        //提交时间筛选
        if (!empty($params['created_at_start']) && !empty($params['created_at_end'])) {
            $params['created_at_start'] .= ' 00:00:00';
            $params['created_at_end']   .= ' 23:59:59';
            $builder->betweenWhere('created_at', $params['created_at_start'], $params['created_at_end']);
        }
        //最后更新时间筛选
        if (!empty($params['updated_at_start']) && !empty($params['updated_at_end'])) {
            $params['updated_at_start'] .= ' 00:00:00';
            $params['updated_at_end']   .= ' 23:59:59';
            $builder->betweenWhere('updated_at', $params['updated_at_start'], $params['updated_at_end']);
        }

        //按照提交人工号或者姓名筛选
        if (!empty($params['staff_id_or_name'])) {
            $builder->andWhere('created_staff_id like :created_staff_id: or created_staff_name like :created_staff_id:', ['created_staff_id' => '%' . $params['staff_id_or_name'] . '%']);
        }
        return $builder;
    }

    /**
     * 资产工单-初始化信息
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getDefault($user)
    {
        $hrStaffServer = new HrStaffInfoServer();
        $userInfo = $hrStaffServer->getUserInfoByStaffInfoId($user['id'], 'sys_store_id, mobile_company as mobile');
        if (empty($userInfo)) {
            //用户信息不存在
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "staff data empty"]);
        }
        $data = $userInfo->toArray();
        if ($data['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            //总部
            $data['sys_store_name'] = enums::HEAD_OFFICE;
        } else {
            $storeInfo = $hrStaffServer->getStaffStoreBySysStoreId($data['sys_store_id'], 'id, name');
            $data['sys_store_name'] = !empty($storeInfo) ? $storeInfo->name : '';
        }
        return $this->checkReturn(["data" => $data]);
    }

    /**
     * 资产工单-问题类型
     * @return array
     */
    public function getQuestionItem()
    {
        $questionType = AssetWorkOrderTypeModel::find([
            'columns' => 'id, t_key',
            'conditions' => 'is_deleted = :is_deleted:',
            'bind' => ['is_deleted' => AssetWorkOrderEnums::IS_DELETED_NO]
        ])->toArray();
        $questionTypeItem = [];
        if (!empty($questionType)) {
            foreach ($questionType as $key => $val) {
                $questionTypeItem[] = [
                    "id"   => $val["id"],
                    "name" => $this->getTranslation()->_($val['t_key']),
                ];
            }
        }
        return $this->checkReturn(["data" => $questionTypeItem]);
    }

    /**
     * 资产工单-问题所在仓
     * @param array $paramIn 请求参数
     * @return array
     */
    public function getStoreList($paramIn)
    {
        $storeList = (new SysStoreServer())->getStoreListByCategory((int)$paramIn['category_id'], ['id AS store_id', 'name', 'category'], 0);
        return $this->checkReturn(["data" => $storeList ?? []]);
    }

    /**
     * 获取一条问题类型信息
     * @param integer $typeId 问题类型ID
     * @return mixed
     */
    private function getWorkTypeInfo($typeId)
    {
        return AssetWorkOrderTypeModel::findFirst([
            'columns' => 'id,t_key',
            'conditions' => 'id = :id: and is_deleted = :is_deleted:',
            'bind' => ['id' => $typeId, 'is_deleted' => AssetWorkOrderEnums::IS_DELETED_NO]
        ]);
    }

    /**
     * 资产工单-提交
     * @param array $paramIn 请求参数
     * @param array $user 申请人信息
     * @return array
     * @throws ValidationException
     */
    public function add($paramIn, $user)
    {
        //验证是否有提交权限
        if (false === $this->getAssetWorkOrderPermission($user)) {
            throw new ValidationException($this->getTranslation()->_('asset_work_order_cannot_submit'));
        }
        //验证问题类型是否真实存在
        $questionType = $this->getWorkTypeInfo($paramIn['type_id']);
        if (empty($questionType)) {
            throw new ValidationException($this->getTranslation()->_('asset_work_order_type_not_found'));
        }
        //验证网点是否存在
        $storeInfo = (new SysStoreServer())->getStoreInfoByid($paramIn['store_id']);
        if (empty($storeInfo) || !is_array($storeInfo)) {
            throw new ValidationException($this->getTranslation()->_('4010'));
        }
        //是否是FULFILLMENT网点类型
        if ($storeInfo['category'] != AssetWorkOrderEnums::STORE_CATEGORY_TYPE) {
            throw new ValidationException($this->getTranslation()->_('asset_work_order_store_category_not_ffm'));
        }
        //泰国时间
        $addHour = $this->getDI()['config']['application']['add_hour'];
        //生成资产工单编号规则：国家码+年月日+4位流水（按照当天申请日期从工单表里去总数➕1）
        $nowTime = time() + $addHour * 3600;
        $day = gmdate("Y-m-d", $nowTime);
        $nums = $this->getNumByDay($day);
        $nums++;
        $countryCode = substr(strtoupper(env('country_code', 'Th')), 0, 2);
        $orderCode = $countryCode . gmdate("Ymd", $nowTime) . str_pad($nums, 4,0, STR_PAD_LEFT);

        $createTime = gmdate("Y-m-d H:i:s", $nowTime);
        //组装资产工单信息
        $orderInfo = [
            'order_code' => $orderCode,//资产工单编号
            'type_id' => $paramIn['type_id'],//问题类型id
            'store_id' => $paramIn['store_id'],//问题所属仓-网点ID
            'line_id' => $paramIn['line_id'],//WeChatID
            'mobile' => $paramIn['mobile'],//手机号码
            'info' => $paramIn['info'],//问题详情文本内容
            'pics' => !empty($paramIn['pics']) ? json_encode($paramIn['pics'], JSON_UNESCAPED_UNICODE) : "",//问题详情图片地址
            'created_staff_id' => $user['id'],//申请人工号
            'created_staff_name' => $user['name'],//申请人姓名
            'created_department_id' => $user['department_id'],//申请人部门id
            'created_job_title_id' => $user['job_title'],//申请人职位id
            'created_store_id' => $user['organization_id'],//申请人网点id
            'status' => enums::TICKET_STATUS_WAIT_REPLY,//待回复
            'created_at' => $createTime,//添加时间
            'updated_at' => $createTime//更新时间
        ];
        //获取提交人所属部门名称
        if (!empty($orderInfo['created_department_id'])) {
            $orderInfo['created_department_name'] = (new DepartmentRepository())->getDepartmentNameById($orderInfo['created_department_id']);
        }
        //获取提交人所属职位、网点名称信息
        if (!empty($orderInfo['created_job_title_id'])) {
            $userInfo = (new StaffRepository())->getStaffInfoById($user['id']);
            if (!empty($userInfo)) {
                $orderInfo['created_store_name'] = $userInfo['name'] ?? "";
                $orderInfo['created_job_title_name'] = $userInfo['job_name'] ?? "";
            }
        }
        //开始插入资产工单
        $db = $this->getDI()->get('db');
        $db->begin();
        $assetWorkOrder = new AssetWorkOrderModel();
        $bool = $assetWorkOrder->create($orderInfo);
        if (!$bool) {
            //插入工单失败，事务回滚
            $db->rollback();
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "insert order error"]);
        }

        //开始插入回复记录信息
        $orderId = $db->lastInsertId();
        $replyRecord = new AssetWorkOrderReplyRecordModel();
        $recordBool = $replyRecord->create([
            'order_id' => $orderId,//工单ID
            'created_staff_id' => $orderInfo['created_staff_id'],//提交人id
            'created_staff_name' => $orderInfo['created_staff_name'],//提交人姓名
            'created_type' => AssetWorkOrderEnums::REPLY_TYPE_SUBMIT,//提交
            'mark' => $orderInfo['info'],//内容
            'pics' => $orderInfo['pics'],//图片地址
            'source_type' => AssetWorkOrderEnums::REPLY_SOURCE_TYPE_BY,//by端
            'created_at' => $createTime,//添加时间
            'updated_at' => $createTime//更新时间
        ]);
        if (!$recordBool) {
            //插入回复记录失败，事务回滚
            $db->rollback();
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "insert reply record error"]);
        }
        //均插入成功，事务提交
        $db->commit();
        return $this->checkReturn(["code" => ErrCode::SUCCESS, "msg" => "success"]);
    }

    /**
     * 验证pic 数组的格式 必须是 pic_nam  pic_path
     * @param array $pics
     * @return array|bool
     */
    public function checkPics(array $pics)
    {
        if (empty($pics)) {
            return $pics;
        }
        foreach ($pics as $key => $val) {
            if (count($val) != 2) {
                return false;
            }
            if (!isset($val['pic_name']) || !isset($val['pic_path'])) {
                return false;
            }
        }
        return true;
    }


    /**
     * 关闭工单
     * @param array $paramIn 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws ValidationException
     */
    public function close($paramIn, $user)
    {
        //检测用户权限
        $this->checkUserPermission($paramIn['from'], $user);

        //查询工单是否存在
        $params['id'] = (int)$paramIn['id'];
        //from=1 表示请求来自提交入口 需要拼接提交人的员工号
        if ($paramIn['from'] == AssetWorkOrderEnums::TYPE_SUBMIT) {
            $params['created_staff_id'] = (int)$user['id'];
        }
        $orderInfo = $this->getOrderDetail($params);

        //如果是关闭则不允许回复
        if ($orderInfo->status == enums::TICKET_STATUS_CLOSED) {
            throw new ValidationException($this->getTranslation()->_('asset_work_order_close'));
        }
        //泰国时间
        $addHour = $this->getDI()['config']['application']['add_hour'];
        $curTime = gmdate("Y-m-d H:i:s", time() + $addHour * 3600);
        $orderInfo->close_staff_id = $user['id'];//关闭人ID
        $orderInfo->close_reason = $paramIn['close_reason'];//关闭原因
        $orderInfo->close_time = $curTime;//关闭时间
        $orderInfo->status = enums::TICKET_STATUS_CLOSED; //已关闭
        $orderInfo->updated_at = $curTime;//更新时间
        $bool = $orderInfo->update();
        if (!$bool) {
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "update order error"]);
        }
        return $this->checkReturn(["code" => ErrCode::SUCCESS, "msg" => "success"]);
    }

    /**
     * 回复工单
     * @param array $paramIn 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws ValidationException
     */
    public function reply($paramIn, $user)
    {
        //检测用户权限
        $this->checkUserPermission($paramIn['from'], $user);

        //查询工单是否存在
        $params['id'] = (int)$paramIn['id'];
        //from=1 表示请求来自提交入口 需要拼接提交人的员工号
        if ($paramIn['from'] == AssetWorkOrderEnums::TYPE_SUBMIT) {
            $params['created_staff_id'] = (int)$user['id'];
        }
        $orderInfo = $this->getOrderDetail($params);

        //如果是关闭则不允许回复
        if ($orderInfo->status == enums::TICKET_STATUS_CLOSED) {
            throw new ValidationException($this->getTranslation()->_('asset_work_order_reply'));
        }

        //泰国时间
        $addHour = $this->getDI()['config']['application']['add_hour'];
        $curTime  = gmdate("Y-m-d H:i:s", time() + $addHour * 3600);
        $db = $this->getDI()->get('db');
        $db->begin();
        $replyData = [
            'order_id' => (int)$paramIn["id"],//工单id
            'created_staff_id' => $user['id'],//消息回复人id
            'created_staff_name' => $user['name'],//消息回复人姓名
            'mark' => $paramIn['mark'] ?? '',//消息内容
            'pics' => !empty($paramIn['pics']) ? json_encode($paramIn['pics'], JSON_UNESCAPED_UNICODE) : '',//图片
            'created_type' => ($paramIn['from'] == AssetWorkOrderEnums::TYPE_SUBMIT) ? AssetWorkOrderEnums::REPLY_TYPE_SUBMIT : AssetWorkOrderEnums::REPLY_TYPE_REPLY,
            'created_at' => $curTime,
            'updated_at' => $curTime
        ];
        //记录资产工单回复记录
        $replyRecord = new AssetWorkOrderReplyRecordModel();
        $recordBool = $replyRecord->create($replyData);
        if (!$recordBool) {
            //插入回复记录失败，事务回滚
            $db->rollback();
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "insert reply record error"]);
        }

        //更新资产工单相关信息
        $orderInfo->updated_at = $curTime;
        $orderInfo->status = enums::TICKET_STATUS_WAIT_REPLY;//默认是待回复
        //审批者回复
        if ($paramIn['from'] == AssetWorkOrderEnums::TYPE_AUDIT) {
            $orderInfo->status = enums::TICKET_STATUS_REPLY;//已回复
            if (empty($orderInfo->first_deal_staff_id) && 0 === (int)$orderInfo->first_deal_staff_id) {
                //首次回复为空，则需要记录首次回复信息
                $orderInfo->first_deal_staff_id = $user['id'];
                $orderInfo->first_deal_staff_name = $user['name'];
                $orderInfo->first_deal_staff_time = $curTime;
            } else {
                //首次回复非空，则需要记录最后回复信息
                $orderInfo->last_deal_staff_id = $user['id'];
                $orderInfo->last_deal_staff_name = $user['name'];
                $orderInfo->last_deal_staff_time = $curTime;
            }
        }
        $bool = $orderInfo->update();
        if (!$bool) {
            //更新资产工单信息失败，事务回滚
            $db->rollback();
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "update order error"]);
        }
        $db->commit();
        return $this->checkReturn(["code" => ErrCode::SUCCESS, "msg" => "success"]);
    }

    /**
     * 获取指定日期下总工单数
     * @param string $day 日期
     * @return int
     */
    public function getNumByDay($day)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(id) as total');
        $builder->from(AssetWorkOrderModel::class);
        $builder->where('created_at >= :begin_time:', ['begin_time' => $day." 00:00:00"]);
        $builder->andWhere('created_at <= :end_time:', ['end_time' => $day." 23:59:59"]);
        return (int)$builder->getQuery()->getSingleResult()->total;
    }

    /**
     * 检测用户权限
     * @param integer $from 1提交者，2审批者
     * @param array $user 当前登陆者信息组
     * @return bool
     * @throws ValidationException
     */
    private function checkUserPermission($from, $user)
    {
        //提交人查看列表-但是没有权限
        if ($from == AssetWorkOrderEnums::TYPE_SUBMIT && false === $this->getAssetWorkOrderPermission($user)) {
            throw new ValidationException($this->getTranslation()->_('asset_work_order_cannot_submit'));
        }
        //审批人查看列表-但是没有权限
        if ($from == AssetWorkOrderEnums::TYPE_AUDIT && false === $this->getAuditAssetWorkOrderPermission($user)) {
            throw new ValidationException($this->getTranslation()->_('asset_work_order_cannot_audit'));
        }
        return true;
    }

    /**
     * 获取待回复、已回复、已关闭工单列表
     * @param array $paramIn 查询条件
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws ValidationException
     */
    public function list($paramIn, $user)
    {
        //检测用户权限
        $this->checkUserPermission($paramIn['from'], $user);
        //翻页处理
        $pageNum  = intval($paramIn['page_num']);
        $pageSize = intval($paramIn['page_size']);
        $data = [
            'items'      => [],
            'pagination' => [
                'page_num' => $pageNum,
                'per_page'     => $pageSize,
                'total_count'  => 0,
            ],
        ];

        //处理状态：1待回复，2已回复，3已关闭
        $paramIn['status'] = (int)$paramIn['status'];
        //问题类型
        if (!empty($paramIn['type_id'])) {
            $paramIn['type_id'] = (int)$paramIn['type_id'];
        }
        // from=1 表示请求来自提交入口 需要拼接提交人的员工号
        if ($paramIn['from'] == AssetWorkOrderEnums::TYPE_SUBMIT) {
            $paramIn['created_staff_id'] = (int)$user['id'];
        }

        [$items, $count] = $this->getList($paramIn, [
            'id',// 主键id
            'order_code',//工单编号
            'created_at',//提交时间
            'updated_at',//更新时间
            'created_staff_id', //提交人信息
            'created_staff_name',//提交姓名
            'created_store_name',//所属网点
            'store_id',//问题所在仓
            'type_id', //问题类型
            "status"// 状态
        ]);
        $data['items'] = $items ?? [];
        $data['pagination']['total_count'] = $count;
        return $this->checkReturn(['data' => $data]);
    }

    /**
     * 获取列表
     * @param array $condition 筛选条件组
     * @param array $columns 字段组
     * @return array
     */
    private function getList($condition, $columns)
    {
        $pageNum  = intval($condition['page_num']);
        $pageSize = intval($condition['page_size']);
        $items = [];
        //获得数量
        $count = $this->getCount($condition);
        if ($count > 0) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns($columns);
            $builder->from(AssetWorkOrderModel::class);
            $builder = $this->getCondition($builder, $condition);
            $offset    = $pageSize * ($pageNum - 1);
            $builder->limit($pageSize, $offset);
            $builder->orderBy('created_at DESC');
            $items_obj = $builder->getQuery()->execute();
            $items = $items_obj ? $items_obj->toArray() : [];
            $items = $this->formatList($items);
        }
        return [$items, $count];
    }

    /**
     * 格式化工单列表
     * @param array $list 工单列表组
     * @return array
     */
    private function formatList($list)
    {
        if (empty($list)) {
            return [];
        }
        //网点ID组
        $storeIds = array_values(array_unique(array_column($list, 'store_id')));

        //获取网点
        $storeList = [];
        $storeRet = SysStoreModel::find([
            'conditions' => "id IN ({store_ids:array})",
            'bind' => [
                'store_ids' => $storeIds,
            ],
            "columns" => 'id,name',
        ])->toArray();
        if (!empty($storeRet)) {
            $storeList = array_column($storeRet, null, 'id');
        }

        //获取问题类型
        $questionTypeList = [];
        $questionTypeIds = array_values(array_unique(array_column($list, 'type_id')));
        $questionTypeRet = AssetWorkOrderTypeModel::find([
            'conditions' => "id IN ({type_ids:array}) and is_deleted = :is_deleted:",
            'bind' => [
                'type_ids' => $questionTypeIds,
                'is_deleted' => AssetWorkOrderEnums::IS_DELETED_NO
            ],
            "columns" => 'id,t_key',
        ])->toArray();
        if (!empty($questionTypeRet)) {
            $questionTypeList = array_column($questionTypeRet, null, 'id');
        }
        //状态枚举
        $status = enums::$ticket_status;
        foreach ($list as &$item) {
            $item['store_name'] = !empty($storeList[$item['store_id']]) ? $storeList[$item['store_id']]['name'] : '';
            $item['type_text'] = !empty($questionTypeList[$item['type_id']]) ? $this->getTranslation()->_($questionTypeList[$item['type_id']]['t_key']) : '';
            $item['status_text'] = !empty($status[$item['status']]) ?  $this->getTranslation()->_($status[$item['status']]) : '';
        }
        return $list;
    }

    /**
     * 根据条件获取一条工单信息
     * @param array $param 请求参数
     * @param array $columns 查询字段值
     * @return mixed
     * @throws ValidationException
     */
    public function getOrderDetail($param, $columns = ['*'])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(AssetWorkOrderModel::class);
        $builder->columns($columns);
        $builder = $this->getCondition($builder, $param);
        $orderInfo = $builder->getQuery()->getSingleResult();
        if (empty($orderInfo)) {
            throw new ValidationException($this->getTranslation()->_('asset_work_order_not_found'));
        }
        return $orderInfo;
    }

    /**
     * 获取工单详情
     * @param array $paramIn 查询条件
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws ValidationException
     */
    public function detail($paramIn, $user)
    {
        //检测用户权限
        $this->checkUserPermission($paramIn['from'], $user);

        $params['id'] = (int)$paramIn['id'];
        //from=1 表示请求来自提交入口 需要拼接提交人的员工号
        if ($paramIn['from'] == AssetWorkOrderEnums::TYPE_SUBMIT) {
            $params['created_staff_id'] = (int)$user['id'];
        }
        $columns = [
            'id',
            'order_code',
            'type_id',
            'store_id',
            'line_id',
            'mobile',
            'info',
            'status',
            'pics',
            'created_at',
            'updated_at'
        ];
        $orderInfo = $this->getOrderDetail($params, $columns);
        $orderInfo = $this->formatDetail($orderInfo->toArray());
        return $this->checkReturn(['data' => $orderInfo]);
    }

    /**
     * 格式化工单信息
     * @param array $info 工单信息组
     * @return array
     */
    private function formatDetail($info)
    {
        if (empty($info)) {
            return [];
        }
        //问题类型
        $questionTypeRet = $this->getWorkTypeInfo($info['type_id']);
        $info['type_text'] = !empty($questionTypeRet) ? $this->getTranslation()->_($questionTypeRet->t_key) : '';
        //问题所属仓
        $storeRet = (new SysStoreServer())->getStoreInfoByid($info['store_id']);
        $info['store_name'] = !empty($storeRet) ? $storeRet['name'] : '';
        //图片
        $info['pics'] = !empty($info['pics']) ? json_decode($info['pics'], true) : "";
        return $info;
    }

    /**
     * 资产工单-沟通记录
     * @param array $paramIn 查询条件
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws ValidationException
     */
    public function getLogList($paramIn, $user)
    {
        //检测用户权限
        $this->checkUserPermission($paramIn['from'], $user);
        //检测资产工单信息是否存在
        $detailParam = ['id' => $paramIn['id']];
        if ($paramIn['from'] == AssetWorkOrderEnums::TYPE_SUBMIT) {
            //提交者只可看自己的
            $detailParam['created_staff_id'] = $user['id'];
        }
        $orderInfo = $this->getOrderDetail($detailParam, ['id']);
        //翻页处理
        $pageNum  = intval($paramIn['page_num']);
        $pageSize = intval($paramIn['page_size']);
        $data = [
            'items' => [],
            'pagination' => [
                'page_num' => $pageNum,
                'per_page' => $pageSize,
                'total_count' => 0,
            ],
        ];
        $builder = $this->modelsManager->createBuilder();
        $builder->from(AssetWorkOrderReplyRecordModel::class);
        $builder->where('order_id = :order_id:', ['order_id' => $orderInfo->id]);
        $builder->columns('count(id) as total');
        $count = (int)$builder->getQuery()->getSingleResult()->total;
        if ($count > 0) {
            $builder->columns([
                'id',                 // 主键id
                'order_id',           // 工单id
                'created_staff_id',   // 创建人工号
                'created_staff_name', // 创建人姓名
                'created_type',       // 1提交，2回复
                'mark',               // 消息内容
                'pics',               // 图片
                'created_at',         // 创建时间
            ]);

            $offset = $pageSize * ($pageNum - 1);
            $builder->limit($pageSize, $offset);
            $items_obj = $builder->getQuery()->execute();
            $items = $items_obj ? $items_obj->toArray() : [];
            if (!empty($items)) {
                foreach ($items as &$val) {
                    $val['pics'] = json_decode($val['pics'], true);
                    //消息左侧展示0,消息右侧展示1
                    $val['is_right'] = ($user['id'] == $val['created_staff_id']) ? 1 : 0;
                }
            }
        }
        $data['items'] = $items ?? [];
        $data['pagination']['total_count'] = $count;
        return $this->checkReturn(['data' => $data]);
    }

    /**
     * 资产工单-问题类型\问题所属仓\处理状态-FOR-OA
     * @param array $params 请求参数
     * @return array
     */
    public function getEnumsForOa($params)
    {
        //获取问题类型
        $questionItem = $this->getQuestionItem();
        $storeList = $this->getStoreList(['category_id' => AssetWorkOrderEnums::STORE_CATEGORY_TYPE]);
        $status = enums::$ticket_status;
        $statusItems = [];
        foreach ($status as $key => $value) {
            $statusItems[] = [
                'value' => $this->getTranslation()->_($value),
                'key' => $key
            ];
        }
        return [
            'question_item' => $questionItem['data'] ?? [],
            'store_list' => $storeList['data'] ?? [],
            'status' => $statusItems
        ];
    }

    /**
     * 资产工单-列表-FOR-OA
     * @param array $params 请求参数
     * @return array
     */
    public function getListForOa($params)
    {
        $params['page_num'] = !empty($params['page_num']) ? $params['page_num'] : AssetWorkOrderEnums::PAGE_NUM;
        $params['page_size'] = !empty($params['page_size']) ? $params['page_size'] : AssetWorkOrderEnums::PAGE_SIZE;
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $params['page_num'],
                'per_page'     => $params['page_size'],
                'total_count'  => 0,
            ],
        ];
        [$items, $count] = $this->getList($params, [
            'id',
            'order_code',
            'created_at',
            'created_staff_id',
            'created_staff_name',
            'type_id',
            'store_id',
            'first_deal_staff_name',
            'first_deal_staff_time',
            'first_deal_staff_id',
            'last_deal_staff_id',
            'last_deal_staff_name',
            'last_deal_staff_time',
            'updated_at',
            'status'
        ]);
        $data['items'] = $items ?? [];
        $data['pagination']['total_count'] = $count;
        return $data;
    }

    /**
     * 资产工单-详情-FOR-OA
     * @param array $params 请求参数组
     * @return mixed
     * @throws ValidationException
     */
    public function getDetailForOa($params)
    {
        if (empty($params['id'])) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $columns = [
            'id',
            'order_code',
            'type_id',
            'store_id',
            'line_id',
            'created_staff_id',
            'created_staff_name',
            'created_department_name',
            'created_store_name',
            'mobile',
            'info',
            'status',
            'created_job_title_name',
            'close_staff_id',
            'close_reason',
            'close_time'
        ];
        $orderInfo = $this->getOrderDetail($params, $columns)->toArray();
        $orderInfo = $this->formatDetail($orderInfo);

        //获取回复记录
        $builder = $this->modelsManager->createBuilder();
        $builder->from(AssetWorkOrderReplyRecordModel::class);
        $builder->where('order_id = :order_id:', ['order_id' => $orderInfo['id']]);
        $builder->columns([
            'id',                 // 主键id
            'order_id',           // 工单id
            'created_staff_id',   // 创建人工号
            'created_staff_name', // 创建人姓名
            'created_type',       // 1提交，2回复
            'mark',               // 消息内容
            'pics',               // 图片
            'created_at',         // 创建时间
        ]);
        $items_obj = $builder->getQuery()->execute();
        $replyList = $items_obj ? $items_obj->toArray() : [];
        if (!empty($replyList)) {
            foreach ($replyList as &$val) {
                $val['pics'] = json_decode($val['pics'], true);
            }
        }

        return [
            'order_info' => $orderInfo,
            'reply_list' => $replyList
        ];
    }

    /**
     * 资产工单-回复-FOR-OA
     * @param array $paramIn 请求参数组
     * @return array
     * @throws ValidationException
     */
    public function replyForOa($paramIn)
    {
        $params['id'] = (int)$paramIn['order_id'] ?? 0;
        if (empty($params['id'])) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        //查询工单是否存在
        $orderInfo = $this->getOrderDetail($params);

        //如果是关闭则不允许回复
        if ($orderInfo->status == enums::TICKET_STATUS_CLOSED) {
            throw new ValidationException($this->getTranslation()->_('asset_work_order_reply'));
        }

        //泰国时间
        $addHour = $this->getDI()['config']['application']['add_hour'];
        $curTime  = gmdate("Y-m-d H:i:s", time() + $addHour * 3600);
        $db = $this->getDI()->get('db');
        $db->begin();
        $replyData = [
            'order_id' => (int)$paramIn["order_id"],//工单id
            'created_staff_id' => $paramIn['created_staff_id'],//消息回复人id
            'created_staff_name' => $paramIn['created_staff_name'],//消息回复人姓名
            'mark' => $paramIn['mark'] ?? '',//消息内容
            'pics' => $paramIn['pics'],//图片
            'created_type' => AssetWorkOrderEnums::REPLY_TYPE_REPLY,
            'source_type' => AssetWorkOrderEnums::REPLY_SOURCE_TYPE_OA,
            'created_at' => $curTime,
            'updated_at' => $curTime
        ];
        //记录资产工单回复记录
        $replyRecord = new AssetWorkOrderReplyRecordModel();
        $recordBool = $replyRecord->create($replyData);
        if (!$recordBool) {
            //插入回复记录失败，事务回滚
            $db->rollback();
            throw new \Exception($this->getTranslation()->_('asset_work_order_operate_failed'));
        }

        //更新资产工单相关信息
        $orderInfo->updated_at = $curTime;
        $orderInfo->status = enums::TICKET_STATUS_REPLY;//已回复
        if (empty($orderInfo->first_deal_staff_id) && 0 === (int)$orderInfo->first_deal_staff_id) {
            //首次回复为空，则需要记录首次回复信息
            $orderInfo->first_deal_staff_id = $paramIn['created_staff_id'];
            $orderInfo->first_deal_staff_name = $paramIn['created_staff_name'];
            $orderInfo->first_deal_staff_time = $curTime;
        } else {
            //首次回复非空，则需要记录最后回复信息
            $orderInfo->last_deal_staff_id = $paramIn['created_staff_id'];
            $orderInfo->last_deal_staff_name = $paramIn['created_staff_name'];
            $orderInfo->last_deal_staff_time = $curTime;
        }
        $bool = $orderInfo->update();
        if (!$bool) {
            //更新资产工单信息失败，事务回滚
            $db->rollback();
            throw new \Exception($this->getTranslation()->_('asset_work_order_operate_failed'));
        }
        $db->commit();
        return $this->checkReturn(["code" => ErrCode::SUCCESS, "msg" => "success"]);
    }

    /**
     * 资产工单-关闭-FOR-OA
     * @param array $paramIn  请求参数组
     * @return array
     * @throws ValidationException
     */
    public function closeForOa($paramIn)
    {
        //检测参数合法性
        if (empty($paramIn['order_id']) || !is_array($paramIn['order_id'])) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }

        //检测提交过来的是否存在已关闭工单
        $ids = array_values($paramIn['order_id']);
        $hasCloseOrder = AssetWorkOrderModel::find([
            'columns' => 'id, status',
            'conditions' => 'id in ({ids:array})',
            'bind' => ['ids' => $ids]
        ])->toArray();
        if (!empty($hasCloseOrder) && (in_array(enums::TICKET_STATUS_CLOSED, array_column($hasCloseOrder, 'status')) || array_diff($ids, array_column($hasCloseOrder, 'id')))) {
            //存在,且有关闭的或者不存在工单ID，则给予提示。
            throw new ValidationException($this->getTranslation()->_('asset_work_order_close'));
        }

        //泰国时间
        $addHour = $this->getDI()['config']['application']['add_hour'];
        $curTime = gmdate("Y-m-d H:i:s", time() + $addHour * 3600);
        $bool = $this->getDI()->get('db')->updateAsDict(
            'asset_work_order',
            [
                'status' => enums::TICKET_STATUS_CLOSED,//已关闭
                'close_staff_id' => $paramIn['close_staff_id'],//关闭人ID
                'close_reason' => $paramIn['close_reason'],//关闭原因
                'close_time' => $curTime,//工单关闭时间
                'updated_at' => $curTime,//更新时间
            ],
            [
                'conditions' => "id in (" . implode(',', $ids) . ") ",
            ]
        );
        if (!$bool) {
            throw new \Exception($this->getTranslation()->_('asset_work_order_operate_failed'));
        }
        return $this->checkReturn(["code" => ErrCode::SUCCESS, "msg" => "success"]);
    }
}