<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\HrStaffIdentityAnnexEnums;
use FlashExpress\bi\App\Enums\AssetsHandoverEnums;
use FlashExpress\bi\App\Enums\HrStaffContractEnums;
use FlashExpress\bi\App\Models\backyard\AssetsHandoverModel;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;

class AssetsHandoverServer extends BaseServer
{

    /**
     * 脚本同步数据使用
     * @Date: 12/25/22 12:15 PM
     * @return  array
     **/
    public function synchronizationAssetsHandover()
    {
        echo "开始执行脚本 -时间为" . date('Y-m-d H:i:s') . PHP_EOL;
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsia.staff_info_id,hsc.staff_id,hsia.annex_path_front,hsc.contract_signature_img');
        $builder->from(['hsia' => HrStaffAnnexInfoModel::class]);
        $builder->leftjoin(HrStaffContractModel::class, 'hsia.staff_info_id = hsc.staff_id', 'hsc');
        $builder->where('hsia.audit_state = :audit_state: and hsia.annex_path_front is not null and hsia.type = :type:', ['audit_state' => HrStaffIdentityAnnexEnums::AUDIT_STATE_PASSED,'type'=>HrStaffAnnexInfoModel::TYPE_ID_CARD]);
        $builder->andWhere(' hsc.contract_is_deleted = :contract_is_deleted: and ISNULL(hsc.contract_signature_img)=0 and LENGTH(trim(hsc.contract_signature_img))>0', [
            'contract_is_deleted' => HrStaffContractEnums::IS_NOT_DELETE
        ]);
        $contract_status = [
            HrStaffContractEnums::CONTRACT_STATUS_AUDIT,
            HrStaffContractEnums::CONTRACT_STATUS_ARCHIVE,
            HrStaffContractEnums::CONTRACT_STATUS_ARCHIVED,
            HrStaffContractEnums::CONTRACT_STATUS_RESCIND,
            HrStaffContractEnums::CONTRACT_STATUS_RDNEWAL,
            HrStaffContractEnums::CONTRACT_STATUS_RDNEWED
        ];
        $builder->inWhere('hsc.contract_status', $contract_status);
        $contract_child_type = [
            HrStaffContractEnums::CONTRACT_CHILD_TYPE_51,
            HrStaffContractEnums::CONTRACT_CHILD_TYPE_52
        ];
        $builder->inWhere('hsc.contract_child_type', $contract_child_type);

        $staff_arr = $builder->getQuery()->execute()->toArray();
        if (!empty($staff_arr)) {
            $assets_staff_ids = [];
            $staff_ids        = array_values(array_filter(array_unique(array_column($staff_arr, 'staff_id'))));

            $contract_signature_img_arr = array_filter(array_unique(array_column($staff_arr, 'contract_signature_img', 'staff_id')));
            $identity_file_a_arr        = array_filter(array_unique(array_column($staff_arr, 'annex_path_front', 'staff_id')));

            echo '执行时间是 ' . date('Y-m-d H:i:s') . "符合条件的数据" . json_encode($staff_ids) . PHP_EOL;
            $assets_handover_ids = AssetsHandoverModel::find([
                'conditions' => 'staff_id in ({staff_id:array})',
                'bind'       => [
                    'staff_id' => $staff_ids
                ]
            ])->toArray();

            if (!empty($assets_handover_ids)) {
                $assets_staff_ids = array_values(array_column($assets_handover_ids, 'staff_id'));
            }
            echo '查询AssetsHandover执行时间是 ' . date('Y-m-d H:i:s') . "符合条件的数据" . json_encode($assets_staff_ids) . PHP_EOL;

            //获取差集
            $assets_handover_arr = array_diff($staff_ids, $assets_staff_ids);

            echo '获取差集符合条件的数据' . json_encode($assets_handover_arr) . PHP_EOL;

            if (empty($assets_handover_arr)) {
                echo '同步完成 没有检测到符合条件的数据' . date('Y-m-d H:i:s') . PHP_EOL;
                exit;
            }
            try {
                //开启事物
                $db = $this->getDI()->get('db');
                $db->begin();
                $current_time = gmdate('Y-m-d H:i:s');
                foreach ($assets_handover_arr as $assets_handover_item) {
                    $assets_handover_model                    = new AssetsHandoverModel();
                    $assets_handover_model->staff_id          = $assets_handover_item;
                    $assets_handover_model->state             = AssetsHandoverEnums::ASSETS_HANDOVER_PASSED_STATUS;
                    $assets_handover_model->approver_staff_id = null;
                    $assets_handover_model->reason            = '';
                    $assets_handover_model->createtime        = $current_time;
                    $bool                                     = $assets_handover_model->create();
                    if ($bool === false) {
                        echo date('Y-m-d H:i:s') . "数据添加失败,失败id" . $assets_handover_item . PHP_EOL;
                        $this->getDI()->get('logger')->write_log(json_encode(['assets_handover_item' => $assets_handover_item, 'createtime' => $current_time]), 'info');
                        throw new \Exception("数据添加失败,失败id:{$assets_handover_item}");
                    }

                    $contract_signature_img = $contract_signature_img_arr[$assets_handover_item];
                    if (!empty($contract_signature_img)) {
                        $contract_signature_img_arr      = explode('backyardUpload', $contract_signature_img);
                        $hr_annex_model                  = new HrAnnexModel();
                        $hr_annex_model->oss_bucket_key  = $assets_handover_item;
                        $hr_annex_model->oss_bucket_type = 'ASSET_ANNEX';
                        $hr_annex_model->bucket_name     = explode('.', explode('/', $contract_signature_img_arr[0])[2])[0];
                        $hr_annex_model->object_key      = 'backyardUpload' . $contract_signature_img_arr[1];
                        $hr_annex_model->original_name   = $contract_signature_img_arr[1];
                        $hr_annex_model->deleted         = 0;
                        $hr_annex_model->created_at      = $current_time;
                        $hr_annex_model->rotate_angle    = 0;
                        $hr_annex_model->file_size       = '';
                        $hr_annex_model->file_type       = AssetsHandoverEnums::ANNEX_FILE_TYPE_CONTRACT;
                        $hr_annex_model->type            = AssetsHandoverEnums::ANNEX_TYPE;
                        $fool_hr_annex                   = $hr_annex_model->save();
                        if ($fool_hr_annex === false) {
                            throw new \Exception("添加资产审核签名附件表(hr_annex)失败:{$contract_signature_img}");
                        }
                    }
                    $identity_file_a_img = $identity_file_a_arr[$assets_handover_item];
                    if (!empty($identity_file_a_img)) {
                        $identity_file_arr                  = explode('backyardUpload', $identity_file_a_img);
                        $hr_annex_identity                  = new HrAnnexModel();
                        $hr_annex_identity->oss_bucket_key  = $assets_handover_item;
                        $hr_annex_identity->oss_bucket_type = 'ASSET_ANNEX';
                        $hr_annex_identity->bucket_name     = explode('.', explode('/', $identity_file_arr[0])[2])[0];
                        $hr_annex_identity->object_key      = 'backyardUpload' . $identity_file_arr[1];
                        $hr_annex_identity->original_name   = $identity_file_arr[1];
                        $hr_annex_identity->deleted         = 0;
                        $hr_annex_identity->created_at      = $current_time;
                        $hr_annex_identity->rotate_angle    = 0;
                        $hr_annex_identity->file_size       = '';
                        $hr_annex_identity->file_type       = AssetsHandoverEnums::ANNEX_FILE_TYPE_IDENTITY;
                        $hr_annex_identity->type            = AssetsHandoverEnums::ANNEX_TYPE;
                        $fool_hr_annex_identity             = $hr_annex_identity->save();
                        if ($fool_hr_annex_identity === false) {
                            throw new \Exception("添加资产审核身份证照片表(hr_annex)失败:{$identity_file_a_img}");
                        }
                    }
                    echo '数据添加成功时间为' . date('Y-m-d H:i:s') . ' ,成功id' . $assets_handover_item . PHP_EOL;
                }

                $db->commit();
            } catch (\Exception $e) {
                //异常回滚
                $db->rollback();
                $this->getDI()->get('logger')->write_log('数据同步异常 ' . $e->getMessage() . $e->getTraceAsString(), 'error');
            }

        } else {
            echo '同步完成 没有检测到符合条件的数据' . date('Y-m-d H:i:s') . PHP_EOL;
            $this->getDI()->get('logger')->write_log(date('Y-m-d H:i:s') . '同步完成 没有检测到符合条件的数据', 'error');
        }
    }



}
