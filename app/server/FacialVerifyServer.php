<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Models\backyard\AttendanceWhiteListModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;

class FacialVerifyServer extends BaseServer
{
    
    public function __construct($lang)
    {
        parent::__construct($lang);
    }

    public function getConfig($staff_info_id): array
    {
        $result = [
            'staff_info_id'         => intval($staff_info_id),
            'is_face_check'         => true,
            'is_used_device_check'  => false,
            'is_new_wifi_check'     => false,
            'is_captcha'            => false,
            'token_expiration_date' => null,
            'face_negatives_url'    => '',
            'is_allow_login'        => true,
            'company_type'          => CommonEnums::MS_COMPANY_ENUM_FLASH,
            'staff_formal'          => 1,
            'staff_hire_type'       => 1,
            'facelogin_alive'       => 0,
            'facelogin_alive_score' => 70,
            'facelogin_maxtimes'    => 3,
            'facelogin_error_email' => '',
        ];
        /**
         * 'facial_verify_face_check'                      => '开启数据安全人脸验证，0代表关闭，1代表开启，默认0',
         * 'facial_verify_used_device'                     => '开启常用设备人脸验证，0代表关闭，1代表开启，默认0',
         * 'facial_verify_new_wifi'                        => '开启新增WiFi人脸验证，0代表关闭，1代表开启，默认0',
         * 'facial_verify_token_expiration_date'           => '30天过期扫脸，首次登录扫脸，30天后扫脸',
         * 'facial_verify_captcha'                         => '开启BY登录腾讯云验证码，0代表关闭，1代表开启，默认0',
         * 'facial_verify_salary_whitelist_noverifiy_face' => '发薪不打卡白名单无需验证人脸开关，0关闭，1开启，默认1',
         * 'facial_verify_whitelist_noverifiy_facee'       => '无需人脸校验工号,默认空',
         */
        $codeList    = [
            'facelogin_alive',
            'facelogin_alive_score',
            'facelogin_maxtimes',
            'facelogin_error_email',
            'facial_verify_face_check',
            'facial_verify_used_device',
            'facial_verify_new_wifi',
            'facial_verify_token_expiration_date',
            'facial_verify_captcha',
            'facial_verify_salary_whitelist_noverifiy_face',
            'facial_verify_whitelist_noverifiy_facee',
            'NotAllowedLoginCompanyID',
            'WhitelistOfLogin',
        ];
        $settingList = (new SettingEnvServer)->getMultiEnvByCode($codeList);
        if (!empty($settingList['facial_verify_captcha'])) {
            $result['is_captcha'] = boolval($settingList['facial_verify_captcha']);
        }

        $staffIno = (new StaffServer())->getStaffInfo(['staff_info_id' => $staff_info_id],'contract_company_id,formal,hire_type');
        if (!empty($settingList['NotAllowedLoginCompanyID'])) {
            if (in_array($staffIno['contract_company_id'], explode(',', $settingList['NotAllowedLoginCompanyID']))) {
                $result['is_allow_login'] = false;
                $result['company_type'] = CommonEnums::MS_COMPANY_ENUM_LNT;
            }
            if (!empty($settingList['WhitelistOfLogin']) &&  in_array($staff_info_id, explode(',', $settingList['WhitelistOfLogin']))) {
                $result['is_allow_login'] = true;
                $result['company_type'] = CommonEnums::MS_COMPANY_ENUM_FLASH;
            }
        }
        if ($staffIno) {
            $result['staff_formal']    = intval($staffIno['formal']);
            $result['staff_hire_type'] = intval($staffIno['hire_type']);
        }

        //东昊发版
        if (in_array($staff_info_id, explode(',', env('donghao_staff_ids', '82530,87251,57627')))) {
            $result['is_captcha'] = false;
        }

        if (!empty($settingList['facial_verify_token_expiration_date']) &&  $settingList['facial_verify_token_expiration_date'] > 0) {
            $result['token_expiration_date'] = intval($settingList['facial_verify_token_expiration_date']);
        }

        if (isset($settingList['facelogin_alive'])) {
            $result['facelogin_alive'] = intval($settingList['facelogin_alive']);
        }
        if (isset($settingList['facelogin_alive_score'])) {
            $result['facelogin_alive_score'] = intval($settingList['facelogin_alive_score']);
        }
        if (isset($settingList['facelogin_maxtimes'])) {
            $result['facelogin_maxtimes'] = intval($settingList['facelogin_maxtimes']);
        }
        if (isset($settingList['facelogin_error_email'])) {
            $result['facelogin_error_email'] = $settingList['facelogin_error_email'];
        }
        //总开关关闭了，就不验证人脸了
        if (empty($settingList['facial_verify_face_check'])) {
            $result['is_face_check'] = false;
            return $result;
        }

        if (!empty($staff_info_id)) {
            $re         = new AttendanceRepository($this->lang,$this->timeZone);
            $load_photo = $re->get_attendance_photo($staff_info_id);
            $result['face_negatives_url'] = $load_photo ? $load_photo['work_attendance_path']:'';
            //在 配置白名单 跳过人脸验证
            if (!empty($settingList['facial_verify_whitelist_noverifiy_facee']) && in_array($staff_info_id,
                    explode(',', $settingList['facial_verify_whitelist_noverifiy_facee']))) {
                $result['is_face_check'] = false;
                return $result;
            }
            //在 考勤白名单 跳过人脸验证
            if (!empty($settingList['facial_verify_salary_whitelist_noverifiy_face']) && (new WhiteListServer())->isInfWhiteList($staff_info_id,
                    date('Y-m-d'),
                    [AttendanceWhiteListModel::TYPE_PAID_LOCALLY])) {
                $result['is_face_check'] = false;
                return $result;
            }
        }
        $result['is_used_device_check'] = !empty($settingList['facial_verify_used_device']);
        $result['is_new_wifi_check']    = !empty($settingList['facial_verify_new_wifi']);

        return $result;
    }

    public function getKitConfig($staff_info_id): array
    {
        $result = [
            'staff_info_id'         => intval($staff_info_id),
            'is_face_check'         => true,
            'is_used_device_check'  => false,
            'is_new_wifi_check'     => false,
            'is_captcha'            => false,
            'is_opt'                => false,
            'token_expiration_date' => null,
            'face_negatives_url'    => '',
            'verify_job_title'      => [],
            'is_check_cheating'     => false,
            'staff_formal'          => null,
            'staff_hire_type'       => null,
            'company_type'          => CommonEnums::MS_COMPANY_ENUM_FLASH,
            'facelogin_alive'       => 0,
            'facelogin_alive_score' => 70,
            'facelogin_maxtimes'    => 3,
            'facelogin_error_email' => '',
        ];
        /**
         * 'kit_facial_verify_face_check'            => 'kit开启数据安全人脸验证，0代表关闭，1代表开启，默认0',
         * 'kit_facial_verify_used_device'           => 'kit开启常用设备人脸验证，0代表关闭，1代表开启，默认0',
         * 'kit_facial_verify_new_wifi'              => 'kit开启新增WiFi人脸验证，0代表关闭，1代表开启，默认0',
         * 'kit_facial_verify_token_expiration_date' => 'kit登录X天后自动过期，需重新登录刷脸。默认值30',
         * 'kit_facial_verify_captcha'               => 'kit开启登录腾讯云验证码，0代表关闭，1代表开启，默认0',
         * 'kit_facial_verify_otp'                   => 'kit当设备原因（无摄像头）无法人脸验证时是否进行OTP验证，0代表关闭验证，1代表开启验证，默认0',
         * 'kit_facial_verify_position'              => 'kit需要验证人脸的职位ID，用逗号隔开。仅Kit读取职位列表',
         * 'kit_anti_cheating_store'                 => '开启防作弊登录的网点ID'
         * 'kit_anti_cheating_staff_id'              => '防作弊登录白名单'
         */
        $codeList    = [
            'facelogin_alive',
            'facelogin_alive_score',
            'facelogin_maxtimes',
            'facelogin_error_email',
            'kit_facial_verify_face_check',
            'kit_facial_verify_used_device',
            'kit_facial_verify_new_wifi',
            'kit_facial_verify_token_expiration_date',
            'kit_facial_verify_captcha',
            'kit_facial_verify_otp',
            'kit_facial_verify_position',
            'kit_facial_verify_whitelist_noverifiy_face',
            'kit_anti_cheating_store',
            'kit_anti_cheating_staff_id',
            'kit_anti_cheating_branchtype',
            'NotAllowedLoginCompanyID',
            'WhitelistOfLogin',
        ];

        $settingList = (new SettingEnvServer)->getMultiEnvByCode($codeList);

        if (!empty($staff_info_id)) {
            $staffInfo = (new StaffServer())->getStaffInfo(['staff_info_id' => $staff_info_id],
                'formal,hire_type,sys_store_id,contract_company_id');

            if (!empty($settingList['NotAllowedLoginCompanyID']) && in_array($staffInfo['contract_company_id'],
                    explode(',',
                        $settingList['NotAllowedLoginCompanyID'])) && (empty($settingList['WhitelistOfLogin']) || !in_array($staff_info_id,
                        explode(',', $settingList['WhitelistOfLogin'])))) {
                $result['company_type'] = CommonEnums::MS_COMPANY_ENUM_LNT;
            }

            if ($settingList['kit_anti_cheating_store'] === '1') {
                $result['is_check_cheating'] = true;
            } elseif ($settingList['kit_anti_cheating_store'] === '0') {
                $result['is_check_cheating'] = false;
            } elseif (!empty($staffInfo['sys_store_id']) && in_array($staffInfo['sys_store_id'],
                    explode(',', $settingList['kit_anti_cheating_store']))) {
                $result['is_check_cheating'] = true;
            }

            if ($staffInfo) {
                $result['staff_formal']    = intval($staffInfo['formal']);
                $result['staff_hire_type'] = intval($staffInfo['hire_type']);
                $storeInfo                 = (new SysStoreServer())->getStoreInfoByid($staffInfo['sys_store_id']);
                if ($storeInfo && !in_array($storeInfo['category'], explode(',', $settingList['kit_anti_cheating_branchtype']))) {
                    $result['is_check_cheating'] = true;
                }
            }

            //在白名单了，就不用验证作弊了
            if (in_array($staff_info_id, explode(',', $settingList['kit_anti_cheating_staff_id'] ?? ''))) {
                $result['is_check_cheating'] = false;
            }
        }

        if (!empty($settingList['kit_facial_verify_captcha'])) {
            $result['is_captcha'] = boolval($settingList['kit_facial_verify_captcha']);
        }
        if (!empty($settingList['kit_facial_verify_token_expiration_date']) && $settingList['kit_facial_verify_token_expiration_date'] > 0) {
            $result['token_expiration_date'] = intval($settingList['kit_facial_verify_token_expiration_date']);
        }
        if (!empty($settingList['kit_facial_verify_position'])) {
            $result['verify_job_title'] = explode(',',
                $settingList['kit_facial_verify_position']);
        }
        if (isset($settingList['facelogin_alive'])) {
            $result['facelogin_alive'] = intval($settingList['facelogin_alive']);
        }
        if (isset($settingList['facelogin_alive_score'])) {
            $result['facelogin_alive_score'] = intval($settingList['facelogin_alive_score']);
        }
        if (isset($settingList['facelogin_maxtimes'])) {
            $result['facelogin_maxtimes'] = intval($settingList['facelogin_maxtimes']);
        }
        if (isset($settingList['facelogin_error_email'])) {
            $result['facelogin_error_email'] = $settingList['facelogin_error_email'];
        }

        //总开关关闭了，就不验证人脸了
        if (empty($settingList['kit_facial_verify_face_check'])) {
            $result['is_face_check'] = false;
            return $result;
        }
        $result['is_opt'] = boolval($settingList['kit_facial_verify_otp']);

        if (!empty($staff_info_id)) {
            $re                           = new AttendanceRepository($this->lang, $this->timeZone);
            $load_photo                   = $re->get_attendance_photo($staff_info_id);
            $result['face_negatives_url'] = $load_photo ? $load_photo['work_attendance_path'] : '';
            //在 配置白名单 跳过人脸验证
            if (!empty($settingList['kit_facial_verify_whitelist_noverifiy_face']) && in_array($staff_info_id,
                    explode(',', $settingList['kit_facial_verify_whitelist_noverifiy_face']))) {
                $result['is_face_check'] = false;
                return $result;
            }
        }
        $result['is_used_device_check'] = !empty($settingList['kit_facial_verify_used_device']);
        $result['is_new_wifi_check']    = !empty($settingList['kit_facial_verify_new_wifi']);
        $this->logger->write_log(['FacialVerifyServer_KitConfig' => $result], 'info');

        return $result;
    }


}