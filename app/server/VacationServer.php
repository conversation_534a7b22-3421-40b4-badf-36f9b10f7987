<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/17/22
 * Time: 11:42 AM
 */

namespace FlashExpress\bi\App\Server;

//假期 公共方法
use App\Country\Tools;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditImageModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveProperty;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\LeaveServer;

class VacationServer extends BaseServer
{

    public $leave_day;
    public $staffInfo;//员工信息 audit re 里面 getStaffPosition
    public $paramModel;//入参 数据模型
    public $holidays = [];//员工对应 休息日轮休 list
    public $limitDays;//员工当前额度
    public $leaveProperty;//staff_leave_property 数据模型
    public $leaveSplitInfo;//没有额度参与的 标记日期对应所在年
    public $thisNum;//申请区间 当前年所需天数 扣减额度用
    public $nextNum;//下一年或者周期要用天数
    public $leaveServer;

    //需要入库的 模型
    public $auditId;//主表 id
    public $staffAuditModel;
    public $staffAuditLeaveSplitModel;
    public $staffAuditImagesModel;
    public $staffLeaveRemainingDaysModel;

    public $timeOut;//超时关闭时间点

    public $leaveType;


    public $canleavetoday = 0;//19310: 是否可以今天请假，1代表可以申请今天，0代表不能申请今天
    public $is_leave_policy_department = false;//19310 执行新请假规则的部门ID（含子部门）


    //公共申请 入口
    protected function initData($param)
    {
        //属性 附值
        $staff_re            = new StaffRepository($this->lang);
        $this->staffInfo     = $staff_re->getStaffPosition($param['staff_id']);
        $this->paramModel    = $param;
        $this->leaveProperty = StaffLeaveProperty::findFirst([
            'conditions' => "leave_type = :leave_type: and is_delete = 0",
            'bind'       => ['leave_type' => $this->paramModel['leave_type']],
        ]);

        $leaveServer       = new LeaveServer($this->lang, $this->timeZone);
        $this->leaveServer = Tools::reBuildCountryInstance($leaveServer, [$this->lang, $this->timeZone]);
        //是否跳过休息日
        if ($this->leaveProperty->is_ignore) {
            $this->holidays    = $this->leaveServer->staff_off_days($this->staffInfo['staff_info_id'],
                $this->paramModel['leave_start_time'], $this->paramModel['leave_end_time']);
        }
        $this->formatSplitTag();

        if(isCountry('MY')) {
            //19310: 是否可以今天请假，1代表可以申请今天，0代表不能申请今天
            $this->canleavetoday = (new SettingEnvServer())->getSetVal('canleavetoday');
            //只能申请今天及之后的日期（年假）
            //19310 执行新请假规则的部门ID（含子部门），用逗号隔开
            $leave_policy_department = (new SettingEnvServer())->getSetVal('Leave_Policy_Department', ',');
            $departmentIds = (new DepartmentRepository($this->lang))->getDepartmentChildInfo($leave_policy_department);
            $this->is_leave_policy_department = in_array($this->staffInfo['node_department_id'], $departmentIds);
        }
    }

    //公共查询额度入口
    protected function initSearch($param)
    {
        //属性 附值
        $staff_re            = new StaffRepository($this->lang);
        $this->paramModel    = $param;
        $this->staffInfo     = $staff_re->getStaffPosition($param['staff_id']);
        $this->leaveProperty = StaffLeaveProperty::findFirst([
            'conditions' => "leave_type = :leave_type: and is_delete = 0",
            'bind' => ['leave_type' => $this->paramModel['leave_type']]
        ]);
        $this->leaveServer = new LeaveServer($this->lang, $this->timeZone);
    }



    /**
     * 公共验证
     * @throws ValidationException
     */
    protected function publicValidate()
    {
        //验证用户信息
        if (empty($this->staffInfo)) {
            throw new ValidationException($this->getTranslation()->_('invalid staff id'));
        }

        if (empty($this->staffInfo['hire_date'])) {
            throw new ValidationException($this->getTranslation()->_('invalid hire date'));
        }

        if (in_array($this->staffInfo['formal'], [HrStaffInfoModel::FORMAL_0, HrStaffInfoModel::FORMAL_FRANCHISEE_OTHER, HrStaffInfoModel::FORMAL_FRANCHISEE])) {
            throw new ValidationException($this->getTranslation()->_('os_or_franchisee_staff_disable'));
        }

        //新增权限判断 https://flashexpress.feishu.cn/docx/USK1dOhAiod8STxnC0ccJqacn4d
        if(!$this->leaveServer->leavePermission($this->staffInfo)){
            throw new ValidationException('working country not match');
        }

        //验证属性模型
        if (empty($this->leaveProperty)) {
            throw new ValidationException($this->getTranslation()->_('invalid leave type'));
        }

        //性别 默认不验证 属性表是0
        if(!empty($this->leaveProperty->sex) && $this->staffInfo['sex'] != $this->leaveProperty->sex){
            throw new ValidationException($this->getTranslation()->_('invalid sex type'));
        }

        //开始时间 结束时间 验证
        if ($this->paramModel['leave_end_time'] < $this->paramModel['leave_start_time']) {
            throw new ValidationException($this->getTranslation()->_('1010'));
        }

        //同一天 上下午 反了
        if ($this->paramModel['leave_end_time'] == $this->paramModel['leave_start_time']
            && $this->paramModel['leave_start_type'] > $this->paramModel['leave_end_type']) {
            throw new ValidationException($this->getTranslation()->_('1010'));
        }

        $auditServer = new AuditServer($this->lang,$this->timeZone);
        //[3]查询请假记录
        $levelData = $auditServer->checkExistLeave($this->paramModel);
        if (!empty($levelData)) {
            throw new ValidationException($this->getTranslation()->_('1012'));
        }

        //图片必填的类型
        if (!empty($this->leaveProperty->is_need_img) && empty($this->paramModel['image_path'])) {
            throw new ValidationException($this->getTranslation()->_('22328_leave_application'));
        }
        //图片最多限制5张 泰国病假结构不同
        if (!empty($this->paramModel['image_path']) && count($this->paramModel['image_path']) > 5 && $this->paramModel['leave_type'] != enums::LEAVE_TYPE_38) {
            throw new ValidationException('at most 5 photos');
        }

        //申请的日期 是休息日
        if (empty($this->leaveSplitInfo)) {
            throw new ValidationException($this->getTranslation()->_('day off for the apply date'));
        }

        if (empty($this->paramModel['is_bi']) && empty($this->paramModel['is_submit'])) {
            $this->leaveServer->checkShiftLeave($this->staffInfo['staff_info_id'], $this->paramModel);
        }

        return true;
    }


    //是否转正
    protected function hireCheck()
    {
        //定制逻辑 所有国家 入职在这个时间点之前 都视为转正
        if ($this->staffInfo['hire_date'] < '2020-06-13 00:00:00') {
            return true;
        }

        //转正状态
        if ($this->staffInfo['status'] == ProbationServer::STATUS_FORMAL) {
            return true;
        }

        return false;
    }


    /**
     * 申请时间 $start - $end
     * 表示 当天为基准  申请的【开始时间】要在指定范围
     * -100 或者 +100 代表负无穷 正无穷 不限制
     * 0 代表限制到当天
     * 工具进来的 不走这个方法
     * @param int $start
     * @param int $end
     * @param string $language_key 对应的提示语
     * @throws ValidationException
     * @return boolean
     */
    protected function timeValidate($start = -100, $end = +100, $language_key = '')
    {
        $start_date = $this->formatBetween($start);
        $end_date   = $this->formatBetween($end);


        if (empty($language_key)) {
            $language_key = 'wrong date select';
        }

        //没有限制
        if (empty($start_date) && empty($end_date)) {
            return true;
        }

        //有开始限制 没结束限制
        if (empty($end_date) && $this->paramModel['leave_start_time'] < $start_date) {
            throw new ValidationException($this->getTranslation()->_($language_key));
        }


        if (empty($start_date) && $this->paramModel['leave_start_time'] > $end_date) {
            throw new ValidationException($this->getTranslation()->_($language_key));
        }

        //开始 结束 都有限制
        if (!empty($start_date) && !empty($end_date)) {
            if ($this->paramModel['leave_start_time'] < $start_date || $this->paramModel['leave_start_time'] > $end_date) {
                throw new ValidationException($this->getTranslation()->_($language_key));
            }
        }
        return true;
    }

    //整理时间 上面 方法用timeValidate
    protected function formatBetween($num)
    {
        switch ($num) {
            case -100:
                return '';
                break;
            case 100:
                return '';
                break;
            case 0:
                $date = date('Y-m-d');
                break;
            default:
                $date = date('Y-m-d', strtotime("{$num} day"));
                break;
        }
        return $date;
    }


    //按自然年 拆分申请区间 到天 并标记到对应的所在周期内
    protected function formatSplitTag()
    {
        //拆分天
        $key        = $this->paramModel['leave_start_time'];
        $end_date   = $this->paramModel['leave_end_time'];
        $date_array = [];
        while ($key <= $end_date) {
            $date_array[] = $key;
            $key          = date("Y-m-d", strtotime("+1 day", strtotime($key)));
        }
        $this_num   = $next_num = 0;
        $creat_year = date("Y");
        $insert     = [];
        foreach ($date_array as $k => $date) {
            //whether in ones holidays or not
            if ($this->leaveProperty->is_ignore && in_array($date, $this->holidays)) {
                continue;
            }

            $apply_year = date('Y', strtotime($date));
            if (in_array($this->paramModel['leave_type'],
                [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_4, enums::LEAVE_TYPE_19])) {
                $insert[$k]['year_at'] = $apply_year;
            }
            //请假多天 拆开单天
            $insert[$k]['staff_info_id'] = $this->staffInfo['staff_info_id'];
            $insert[$k]['date_at']       = $date;
            $insert[$k]['type']          = StaffAuditLeaveSplitModel::SPLIT_TYPE_0;
            $insert[$k]['year_at']       = $apply_year;
            //只申请了一天
            if ($this->paramModel['leave_start_time'] == $this->paramModel['leave_end_time']) {
                $insert[$k]['type'] = ($this->paramModel['leave_start_type'] == $this->paramModel['leave_end_type']) ? $this->paramModel['leave_start_type'] : StaffAuditLeaveSplitModel::SPLIT_TYPE_0;
                $used_days          = empty($insert[$k]['type']) ? 1 : 0.5;

                if ($creat_year == $apply_year) {
                    $this_num = $used_days;
                } else {
                    $next_num = $used_days;
                }
                break;
            }

            //计算 对应年申请的天数
            if ($creat_year == $apply_year) {
                $this_num++;
            } else {
                $next_num++;
            }
            //超过一天以上请假 标记 开始日期 和结束日期  0 全天  1 上午  2 下午
            //第一天
            if ($date == $this->paramModel['leave_start_time']) {
                //开始时间 选下午 拆分表 标记为 2 否则 认定全天 标记0
                $insert[$k]['type'] = ($this->paramModel['leave_start_type'] == StaffAuditLeaveSplitModel::SPLIT_TYPE_1) ? StaffAuditLeaveSplitModel::SPLIT_TYPE_0 : StaffAuditLeaveSplitModel::SPLIT_TYPE_2;
                //非整天 减去半天
                if (!empty($insert[$k]['type'])) {
                    if ($creat_year == $apply_year) {
                        $this_num -= 0.5;
                    } else {
                        $next_num -= 0.5;
                    }
                }
            }
            //最后一天
            if ($date == $this->paramModel['leave_end_time']) {
                //结束时间 选上午 拆分表 标记为 1 否则 认定全天 标记0
                $insert[$k]['type'] = ($this->paramModel['leave_end_type'] == StaffAuditLeaveSplitModel::SPLIT_TYPE_2) ? StaffAuditLeaveSplitModel::SPLIT_TYPE_0 : StaffAuditLeaveSplitModel::SPLIT_TYPE_1;
                if (!empty($insert[$k]['type'])) {
                    if ($creat_year == $apply_year) {
                        $this_num -= 0.5;
                    } else {
                        $next_num -= 0.5;
                    }
                }
            }
        }
        $this->thisNum        = $this_num;
        $this->nextNum        = $next_num;
        $this->leaveSplitInfo = $insert;
    }


    /**
     * 时间拼装
     * @param   $date 时间|$dateType 1 开始时间 2 结束时间 | $dateUType 1 AM 2 PM
     * @param $dateType
     * @param $dateUType
     * @return string
     */
    public function assemblyData($date, $dateType = 1, $dateUType = 1)
    {
        $resourceStartData = [
            1 => '09:00:00',
            2 => '13:00:00',
        ];
        $resourceEndData   = [
            1 => '12:00:00',
            2 => '18:00:00',
        ];
        if ($dateType == 1) {
            $dateTime = $date.' '.$resourceStartData[$dateUType];
        } else {
            $dateTime = $date.' '.$resourceEndData[$dateUType];
        }
        return $dateTime;
    }

    //-------- 保存相关
    protected function saveAudit()
    {
        $serialNo = $this->getRandomId();
        $reason   = addcslashes(stripslashes($this->paramModel['audit_reason']), "'");
        $status   = enums::$audit_status['panding'];
        if (!empty($this->paramModel['is_bi'])) {
            $status = enums::$audit_status['approved'];
            $reason .= "|system_tool_add";
        }
        $timeOut = date('Y-m-d 00:00:00',strtotime('+3 day'));
        $insetData = [
            'staff_info_id'    => $this->staffInfo['staff_info_id'],
            'leave_type'       => $this->paramModel['leave_type'],
            'leave_start_time' => $this->assemblyData($this->paramModel['leave_start_time'], 1,
                $this->paramModel['leave_start_type']),
            'leave_start_type' => $this->paramModel['leave_start_type'],
            'leave_end_time'   => $this->assemblyData($this->paramModel['leave_end_time'], 2,
                $this->paramModel['leave_end_type']),
            'leave_end_type'   => $this->paramModel['leave_end_type'],
            'audit_reason'     => $reason,
            'status'           => $status,
            'audit_type'       => enums::$audit_type['LE'],
            'leave_day'        => $this->thisNum + $this->nextNum,
            'serial_no'        => (!empty($serialNo) ? 'LE'.$serialNo : null),
            'time_out'         => $timeOut,
        ];
        //申请来源 目前只有个人代理和正式
        $insetData['source_type'] = (in_array($this->staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) ? 1 : 0;
        $this->leave_day = $this->thisNum + $this->nextNum;

        //不同国家 存在 子类型 产假等
        if (!empty($this->paramModel['sub_type'])) {
            $insetData['template_comment'] = json_encode(["leave_{$this->paramModel['leave_type']}_key" => intval($this->paramModel['sub_type'])]);
        }
        $auditModel = new StaffAuditModel();
        $auditModel->create($insetData);
        $this->auditId = $auditModel->audit_id;
        $this->timeOut = $timeOut;
    }


    //图片表
    protected function saveImgData()
    {
        if (empty($this->paramModel['image_path'])) {
            return;
        }

        $imgModel = new StaffAuditImageModel();
        foreach ($this->paramModel['image_path'] as $k => $v) {
            $insertImgData = [
                'audit_id'   => $this->auditId,
                'image_path' => $v,
            ];
            $clone         = clone $imgModel;
            $clone->create($insertImgData);
        }
    }


    //split 表 保存
    protected function saveSplitData()
    {
        $splitModel = new StaffAuditLeaveSplitModel();
        foreach ($this->leaveSplitInfo as $v) {
            $clone = clone $splitModel;
            $clone->create($v);
        }
    }

    /**
     * 年假定制逻辑 需要查询的数据
     * @param $staffId
     * @param $cycle 周期 或者 自然年
     * @param string $date 开始
     * @param int $unContainId 要排除掉的 audit id （马来 三坑逻辑 用）
     * @return bool
     */
    public function leaveDaysByDate($staffId, $cycle, $date = '', $unContainId = 0)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('sum(if(s.type=0,1,0.5)) as num');
        $builder->from(['s' => StaffAuditLeaveSplitModel::class]);
        $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
        $builder->andWhere("s.staff_info_id = :staff_id:", ['staff_id' => $staffId]);
        $builder->andWhere('a.leave_type = :leave_type:', ['leave_type' => enums::LEAVE_TYPE_1]);
        $builder->andWhere('s.year_at = :year:', ['year' => $cycle]);
        $builder->andWhere('a.audit_type = 2');
        $builder->inWhere('a.status', [enums::APPROVAL_STATUS_PENDING, enums::APPROVAL_STATUS_APPROVAL]);
        if (!empty($date)) {
            $builder->andWhere('s.date_at >= :date:', ['date' => $date]);
        }

        if (!empty($unContainId)) {
            $builder->andWhere('a.audit_id != :out_id:', ['out_id' => $unContainId]);
        }
        $info = $builder->getQuery()->execute()->getFirst();

        return empty($info) ? 0 : $info->num;
    }

    /**
     * 根据 固定时间区间 查询区间表 对应假期类型 申请的天数
     * @param $staffId -- 工号
     * @param string $startDate -- 区间开始时间
     * @param string $endDate 区间结束时间 默认当天
     * @param $leaveType -- 请假类型
     * @return int 天数
     */
    public function leaveDaysBetween($staffId,$startDate,$endDate, $leaveType){

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('sum(if(s.type=0,1,0.5)) as num');
        $builder->from(['s' => StaffAuditLeaveSplitModel::class]);
        $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
        $builder->andWhere("s.staff_info_id = :staff_id:",['staff_id' => $staffId]);
        $builder->andWhere('a.leave_type = :leave_type:',['leave_type' => $leaveType]);
        $builder->andWhere('a.audit_type = 2');
        $builder->inWhere('a.status',array(1,2));
        $builder->betweenWhere('s.date_at', $startDate, $endDate);
        //如果 类型是 不带薪病假 只算 新需求之后的额度
        if($leaveType == enums::LEAVE_TYPE_18){
            $builder->andWhere('a.parent_id != 0');
        }

        $info = $builder->getQuery()->execute()->getFirst();

        return empty($info) ? 0 : $info->num;
    }


    //获取 对应申请占用的额度 不能用group
    public function getUsedDays($auditId){
        //split 对应的 占用的 对应周期的 额度
        $splitInfo = StaffAuditLeaveSplitModel::find([
            'columns'    => 'staff_info_id,if(type=0,1,0.5) as num,year_at',
            'conditions' => "audit_id = :audit_id:",
            'bind'       => [
                'audit_id' => $auditId,
            ],
        ])->toArray();

        if(empty($splitInfo)){
            return [];
        }
        $format = [];
        foreach ($splitInfo as $item) {
            if(empty($format[$item['year_at']])){
                $format[$item['year_at']] = $item['num'];
            }else{
                $format[$item['year_at']] += $item['num'];
            }
        }
        return $format;
    }

    /**
     * @param $year
     * @param $days 如果是申请操作 为正数  撤销返还操作 为负数
     *
     */
    protected function updateRemain($year, $days)
    {
        $remain = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => $this->leaveProperty->leave_type,
                'year_at'    => $year,
            ],
        ]);
        $remain->days       = $remain->days - $days;
        $remain->leave_days = $remain->leave_days + $days;
        $remain->update();
    }

    //撤销驳回 返还额度
    public function returnRemainDays($auditId, $staffInfo, $extend = [])
    {
        $this->staffInfo = $staffInfo;
        //split 对应的 占用的 对应周期的 额度
        $splitInfo = $this->getUsedDays($auditId);

        //没有拆分表额度信息
        if (empty($splitInfo)) {
            $this->logger->write_log("拆分表信息异常 {$this->staffInfo['staff_info_id']} {$auditId}");
            return true;
        }

        $remainData = StaffLeaveRemainDaysModel::find([
            'column'     => 'staff_info_id,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycle:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => $this->leaveType,
                'cycle'      => array_keys($splitInfo),
            ],
        ]);


        if (empty($remainData->toArray())) {
            $this->logger->write_log("额度表信息异常 {$this->staffInfo['staff_info_id']} {$auditId} ".json_encode($splitInfo));
            return true;
        }

        foreach ($remainData as $remain) {
            $needBackDays = $splitInfo[$remain->year] ?? 0;//需要返还的 额度
            //使用额度 减少
            $remain->days       += $needBackDays;
            $remain->leave_days -= $needBackDays;
            $remain->update();
        }
    }



    //by 或者 hcm 显示额度 整理
    protected function formatLimitDays()
    {
        $dayLimit = half_num($this->limitDays['limit']);
        $daySub   = half_num($this->limitDays['sub']);
        $return   = [];

        $return['day_limit'] = "{$dayLimit}";//总额度
        $return['day_sub']   = "{$daySub}";  //剩余额度 有可能是负数
        $this->getDI()->get('logger')->write_log(['formatLimitDays' => $return], 'info');
        return $return;
    }

    //整理 扣减额度 标记 对应周期
    protected function formatSplitData()
    {
        // 拼接 拆分表 归属年 year_at 字段
        foreach ($this->leaveSplitInfo as $k => $in) {
            $this->leaveSplitInfo[$k]['audit_id'] = $this->auditId;
        }
    }




    /**
     * 格式化 年假额度 保留1位小数
     * @param $num
     */
    public  function format_leave_days($num)
    {
        //带有去年额度的 不管
        if(strstr($num,'+')){
            return $num;
        }

        if(empty($num)){
            return '0.0';
        }
        return number_format($num,1);
    }


    //实现接口防止报错
    public function getLimitDays()
    {
        // TODO: Implement getLimitDays() method.
    }

    public function businessCheck()
    {
        // TODO: Implement businessCheck() method.
    }

    public function dataSave()
    {
        // TODO: Implement dataSave() method.
    }


}