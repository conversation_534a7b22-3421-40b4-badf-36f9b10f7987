<?php
namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\InteriorOrderFundStatusEnums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersModel;
use FlashExpress\bi\App\Enums\InteriorGoodsPayMethodEnums;
use FlashExpress\bi\App\Enums\InteriorOrderStatusEnums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersRefundRecordModel;
use FlashExpress\bi\App\Repository\BaseRepository;

class FlashPayServer extends BaseServer
{
    /**
     * FlashPayServer constructor.
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone='+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * 组装FlashPay公共请求参数
     * @return array
     */
    public function getPublicRequestParams(array $flash_pay_config = [])
    {
        return [
            'appKey' => $flash_pay_config['app_key'] ?? env('flash_pay_app_key'),//FlashPay颁发的APP_KEY
            'charset' => 'UTF-8',//请求使用的编码格式，默认且只支持UTF-8
            'sign' => '',//签名，不参与签名
            'signType' => 'RSA2',//签名类型，默认且只支持RSA2(SHA256withRSA)
            'time' => date('Y-m-d H:i:s'),//时间字符串，格式：yyyy-MM-dd HH:mm:ss
            'version' => '1.0',//调用的接口版本，目前固定为：1.0
            'data' => null//具体每个接口的实际参数结构体
        ];
    }

    /**
     * 组装FlashPay公共响应参数
     * @return array
     */
    public function getPublicResponseParams()
    {
        return [
            'code' => 0,//网关码(0：成功，只表达请求成功，非业务成功)
            'message' => '成功',//网关返回码描述
            'sign' => '',//签名，不参与签名
            'data' => ''//具体每个接口的实际参数结构体
        ];
    }

    /**
     * 创建FlashPay订单支付接口
     * @地址 https://l8bx01gcjr.feishu.cn/docs/doccnZZd6tddvcKeBq2doMG71Sf#FNiYpv
     *
     * @param array $flash_pay_create_order 收银台订单参数组
     * @param array $flash_pay_config flashPay 配置
     * @param string $create_payment_api 创建支付订单接口
     * @return mixed
     * @throws Exception
     */
    public function createOrder($flash_pay_create_order, $flash_pay_config, $create_payment_api)
    {
        try {
            $this->wLog('FlashPay createOrder', "flash_pay_merchant_no: {$flash_pay_config['merchant_no']}", 'FlashPay');

            //公共的请求参数
            $params = $this->getPublicRequestParams($flash_pay_config);

            //实际的请求参数组
            $params['data'] = $flash_pay_create_order;
            //调取FlashPay首先要生成签名
            $flash_pay_support_server = new FlashPaySupportServer();
            $params['sign'] = $flash_pay_support_server->generateSign($params, $flash_pay_config);
            $json_params = json_encode($params,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $this->wLog('FlashPay createOrder', "request_params is:【".$json_params."】",'FlashPay');
            $header[] = "Content-type: application/json;charset=utf-8";
            $header[] = "Accept: application/json";
            $header[] = "Accept-Language: " . $this->lang;
            $res_data = httpPostFun(env('flash_pay') . $create_payment_api, $json_params, $header);
            $this->wLog('FlashPay createOrder', "response_result is:【".$res_data."】",'FlashPay');
            $res_data_arr = json_decode($res_data, true);
            if (!isset($res_data_arr['code'])) {
                //调取创建订单接口异常
                throw new Exception('flash pay create order request failed');
            }

            if (isset($res_data_arr['code']) && $res_data_arr['code'] != 0 && empty($res_data_arr['data'])) {
                throw new ValidationException($res_data_arr['message'], $res_data_arr['code']);
            }

            //解析接口返回首先要进行验签
            $verify = $flash_pay_support_server->verifySign($res_data_arr, false, null, $flash_pay_config);
            if ($verify) {
                return $res_data_arr;
            } else {
                //验签未通过
                throw new Exception('flash pay sign is invalid');
            }
        } catch (ValidationException $e) {
            throw $e;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 查询交易结果
     * @地址 https://l8bx01gcjr.feishu.cn/docs/doccnZZd6tddvcKeBq2doMG71Sf#FNiYpv
     *
     * @param array $get_payment_result_params 查询交易结果参数组
     * @param array $flash_pay_config
     * @return bool|mixed|string
     * @throws Exception
     */
    public function getPaymentResult($get_payment_result_params, $flash_pay_config = [])
    {
        try {
            //公共的请求参数
            $params = $this->getPublicRequestParams($flash_pay_config);
            //实际的请求参数组
            $params['data'] = $get_payment_result_params;
            //调取FlashPay首先要生成签名
            $flash_pay_support_server = new FlashPaySupportServer();
            $params['sign'] = $flash_pay_support_server->generateSign($params, $flash_pay_config);
            $json_params = json_encode($params,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $header[] = "Content-type: application/json;charset=utf-8";
            $header[] = "Accept: application/json";
            $header[] = "Accept-Language: " . $this->lang;
            $this->wLog('FlashPay getPaymentResult', "request_params is:【".$json_params."】",'FlashPay');
            $res_data = httpPostFun(env('flash_pay').'/upay/get-payment-result', $json_params, $header);
            $this->wLog('FlashPay getPaymentResult', "response_result is:【".$res_data."】",'FlashPay');
            $res_data_arr = json_decode($res_data, true);
            if (!isset($res_data_arr['code'])) {
                //调取查询交易结果接口异常
                throw new Exception('flash pay payment result request failed');
            }
            //解析接口返回首先要进行验签
            $verify = $flash_pay_support_server->verifySign($res_data_arr, false, null, $flash_pay_config);
            if ($verify) {
                return $res_data_arr;
            } else {
                //验签未通过
                throw new Exception('flash pay sign is invalid');
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 获取订单信息
     * @param string $order_code 订单号
     * @return mixed
     */
    public function getInteriorOrdersInfo($order_code)
    {
        return InteriorOrdersModel::findFirst([
            'conditions' => 'order_code = :order_code: and pay_method in ({pay_method:array})',
            'bind'       => [
                'order_code' => $order_code,
                'pay_method' => [InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE, InteriorGoodsPayMethodEnums::PAY_METHOD_OFFLINE_PAY]
            ],
        ]);
    }

    /**
     * FlashPay在线支付同步回调
     *
     * @param array $params ['outTradeNo'=>'商户订单号', 'tradeNo'=>'FlashPay交易号']
     * @return int|mixed
     */
    public function payTradeSync($params)
    {
        $code = ErrCode::OTHER_ERROR;
        try {
            $interior_goods_server = new InteriorGoodsServer($this->lang);
            $order_model = $interior_goods_server->getOrderByOrderCode($params['outTradeNo']);
            $flash_pay_config = $interior_goods_server->getFlashConfig($order_model->goods_type ?? 0, $order_model->mach_code ?? '');

            $this->wLog('FlashPay payTradeSync', 'request params:【' . json_encode($params) . "】");
            $payment_result = $this->getPaymentResult(['outTradeNo' => $params['outTradeNo']], $flash_pay_config);
            $code = $payment_result['data']['tradeStatus'];
        } catch (BusinessException $e) {
            $this->logger->write_log(['payTradeSync'=>['message'=>$e->getMessage(),'code'=>$e->getCode()],'params'=>$params],'notice');
        } catch (Exception $e) {
            $this->wLog('payTradeSync', $e->getFile()
                . " code " . $e->getCode()
                . " line " . $e->getLine()
                . " message " . $e->getMessage()
                . " trace " . $e->getTraceAsString());
        }
        return $code;
    }

    /**
     * FlashPay在线支付异步回调
     * @param array $params['outTradeNo'=>'商户订单号', 'tradeNo'=>'FlashPay交易号','tradeStatus'=>'交易状态：3、交易成功；','tradeTime'=>'交易时间，时间字符串，格式：yyyy-MM-dd HH:mm:ss',
     * 'paymentAmount'=>'商户订单金额','cur'=>'币种，泰国：THB ，马来西亚：MYR', 'completeTime'=>'交易完成时间，交易终态时返回，时间字符串，格式：yyyy-MM-dd HH:mm:ss']
     * @return array
     * @throws Exception
     */
    public function payTradeNoSync($params)
    {
        $this->wLog('FlashPay payTradeNoSync', 'request params:【'.json_encode($params)."】");

        $outTradeNo = (isset($params['data']) && $params['data']['outTradeNo'] ?? '') ? $params['data']['outTradeNo'] : '';

        $interior_goods_server = new InteriorGoodsServer($this->lang);
        $order_model = $interior_goods_server->getOrderByOrderCode($outTradeNo);
        $flash_pay_config = $interior_goods_server->getFlashConfig($order_model->goods_type ?? 0, $order_model->mach_code);

        $response_to_pay = $this->getPublicResponseParams();
        $flash_pay_support_server = new FlashPaySupportServer();
        try {
            //针对FlashPay的请求要先进行验签
            $verify = $flash_pay_support_server->verifySign($params, false, null, $flash_pay_config);
            if ($verify) {
                $this->wLog('FlashPay payTradeNoSync','verify pass');
                //验签通过, 获取该订单号对应的的订单信息
                if ($outTradeNo) {
                    $orderObj = $this->getInteriorOrdersInfo($outTradeNo);
                    if ($orderObj) {
                        //待付款支付中的订单，才做交易状态与订单状态的同步
                        $this->wLog('FlashPay payTradeNoSync','sync tradeStatus to orderStatus');
                        $this->syncFlashPayTradeStatus($orderObj);
                    } else {
                        $this->wLog('FlashPay payTradeNoSync','not found order');
                        //未找到满足条件的订单
                        $response_to_pay['code'] = ErrCode::OUT_TRADE_NO_ERROR;
                        $response_to_pay['message'] = '非法的商户订单号请求或未找到符合条件的订单';
                    }
                } else {
                    //缺少必要参数传递
                    $response_to_pay['code'] = ErrCode::OUT_TRADE_NO_ERROR;
                    $response_to_pay['message'] = '商户交易号缺失';
                }
            } else {
                $this->wLog('FlashPay payTradeNoSync','verify invalid');
                //验签未通过
                $response_to_pay['code'] = ErrCode::VERIFY_FAILED;
                $response_to_pay['message'] = '验签失败';
            }
        } catch (Exception $e) {
            $this->wLog('FlashPay payTradeNoSync', $e->getFile()
                . " code " .$e->getCode()
                . " line " . $e->getLine()
                . " message " . $e->getMessage()
                . " trace " . $e->getTraceAsString());
            $response_to_pay['code'] = ErrCode::SYNC_FAILED;
            $response_to_pay['message'] = $e->getMessage();
        }
        $response_to_pay['sign'] = $flash_pay_support_server->generateSign($response_to_pay, $flash_pay_config);
        return $response_to_pay;
    }

    /**
     * 同步FlashPay的交易状态到订单表
     * @param object $orderObj 订单基本信息
     * @throws Exception
     */
    public function syncFlashPayTradeStatus($orderObj)
    {
        if ($orderObj) {
            $db = $this->getDI()->get('db');
            $db->begin();

            $orderObj = InteriorOrdersModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $orderObj->id],
                'for_update' => true
            ]);

            try {
                $interior_goods_server = new InteriorGoodsServer();
                $flash_pay_config = $interior_goods_server->getFlashConfig($orderObj->goods_type, $orderObj->mach_code);

                //获得FlashPay的交易状态
                $payment_result = $this->getPaymentResult(['outTradeNo'=>$orderObj->order_code], $flash_pay_config);
                if ($payment_result['code'] != 0) {
                    //如果查询交易状态接口返回错误code
                    throw new Exception($payment_result['message'], $payment_result['code']);
                }
                $payment_data = $payment_result['data'];
                switch ($payment_data['tradeStatus']) {
                    case InteriorOrderStatusEnums::FLASH_PAY_TRADE_STATUS_PAYING:
                        //交易处理中,将该订单状态由待付款变更为支付中
                        if ($orderObj->order_status == InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE) {
                            $orderObj->order_status = InteriorOrderStatusEnums::ORDER_STATUS_PAYING_CODE;
                        }
                        break;
                    case InteriorOrderStatusEnums::FLASH_PAY_TRADE_STATUS_SUCCESS:
                        if ($orderObj->order_status == InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE) {
                            //若订单已取消，款项状态调整为待退款
                            $orderObj->fund_status = InteriorOrderFundStatusEnums::FUND_STATUS_REFUNDING;
                            $orderObj->save();
                        } else if (in_array($orderObj->order_status, [InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE, InteriorOrderStatusEnums::ORDER_STATUS_PAYING_CODE])) {
                            //交易成功，将该订单状态由待付款（支付中）变更为待发货,记录pay交易号、支付时间
                            $orderObj->order_status = InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE;
                            $orderObj->flash_pay_code = $payment_data['tradeNo'];
                            $orderObj->flash_pay_at = $payment_data['completeTime'] ?? null;
                            $remark = "flash_pay_trade_success";
                            $date = date('Y-m-d H:i:s');
                            $orderObj->remark = $orderObj->remark . "【{$remark}:{$date}】";
                            //先保存交易信息
                            $orderObj->save();
                            if ($orderObj->out_status != 1) {
                                //出库单状态不是成功的，在交易成功市需要同步到scm出库
                                $goods = [];
                                $orderSkusObj = $orderObj->getOrdersGoodsSku();
                                if ($orderSkusObj) {
                                    $orderSkusArr = $orderSkusObj->toArray();
                                    foreach ($orderSkusArr as $k => $_preOrderSkus) {
                                        $buyNum  = $_preOrderSkus['buy_num'];
                                        $price   = $_preOrderSkus['buy_price'];
                                        $amount  = $_preOrderSkus['pay_amount'];
                                        $goods[] = [
                                            'i'             => $k,
                                            "barCode"       => $_preOrderSkus['goods_sku_code'],
                                            "goodsName"     => $_preOrderSkus['goods_name_th'],
                                            "specification" => $_preOrderSkus['attr_1'],
                                            "num"           => $buyNum,
                                            //出库商品json，其中price的单位为萨当，即1泰铢=100萨当
                                            "price"         => ($price * 100),
                                            "remark"        => $amount
                                        ];
                                    }
                                }
                                $syncWmsPostData = [
                                    'nodeSn' => $orderObj->node_sn,
                                    'consigneeName' => $orderObj->staff_name,
                                    'consigneePhone' => $orderObj->staff_mobile,
                                    'province' => $orderObj->receive_province_name,
                                    'city' => $orderObj->receive_city_name,
                                    'district' => $orderObj->receive_district_name,
                                    'postalCode' => $orderObj->receive_postal_code,
                                    'consigneeAddress' => $orderObj->receive_address,
                                    'orderSn' => $orderObj->order_code,
                                    'node_department_id' => $orderObj->node_department_id,
                                    'deliveryWay' => 'express',
                                    'goods' => json_encode($goods, JSON_UNESCAPED_UNICODE),
                                    'remark' => $orderObj->remark . "【{$remark}:{$date}】",
                                    'lang' => $this->lang
                                ];

                                $warehouseId = $interior_goods_server->getGoodsTypeStockId($orderObj->goods_type);
                                if (!empty($warehouseId)) {
                                    $syncWmsPostData['warehouseId'] = $warehouseId;
                                }

                                //V22367 针对泰国区分FFM需要获取分仓配置货主、仓库、配送方式自提
                                if (isCountry('TH') && $orderObj->mach_code == $interior_goods_server->getFfmMachCode()) {
                                    $warehouseConfig = $interior_goods_server->getWarehouseConfigByStoreId($orderObj->receive_store_id);
                                    $syncWmsPostData['mchId']       = $orderObj->mach_code;
                                    $syncWmsPostData['warehouseId'] = $warehouseConfig['stock_id'];
                                    $syncWmsPostData['deliveryWay'] = 'self';
                                }

                                $this->wLog('FlashPay syncFlashPayTradeStatus', "sync_to_wms request_params is:【".json_encode($syncWmsPostData)."】",'FlashPay');
                                $syncWarehoseServer = new SyncWarehoseServer();
                                $res                = $syncWarehoseServer->syncAddOrderToWmsReturnWarehouseAdd($syncWmsPostData);
                                if (empty($res) || $res['code'] != 1 || !$res['data']) {
                                    //出库单失败，但有可能是超时失败，后续重试
                                    $orderObj->fail_num = 1;
                                    $orderObj->out_status = 2;
                                    $orderObj->fail_reason = $res['msg'] ?? '';
                                } else {
                                    $orderObj->out_status = 1;
                                    $orderObj->out_sn = $res['data'];
                                    $orderObj->remark = $orderObj->remark . "【wms_out_sn：{$res['data']}】";
                                }
                                $this->wLog('FlashPay syncFlashPayTradeStatus', "sync_to_wms response is:【".json_encode($res)."】",'FlashPay');
                            }
                        }
                        break;
                    case InteriorOrderStatusEnums::FLASH_PAY_TRADE_STATUS_FAILED:
                    case InteriorOrderStatusEnums::FLASH_PAY_TRADE_STATUS_CLOSED:
                        $date = date('Y-m-d H:i:s');
                        //交易失败，交易关闭将该订单状态由待付款（支付中）变更为已取消
                        $orderObj->order_status = InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE;
                        $orderObj->cancel_reason = InteriorOrderStatusEnums::ORDER_CANCEL_FLASH_PAY;
                        $orderObj->canceled_at = $date;
                        $orderObj->updated_at = $date;
                        $orderObj->remark = $orderObj->remark . "【flash_pay_trade_cancel:{$date}】";
                        break;
                    default:
                        //交易待支付,不做任何处理
                        break;
                }
                $orderObj->flash_pay_code = $payment_data['tradeNo'] ?? '';
                $orderObj->save();

                $db->commit();
            } catch (Exception $e) {
                $db->rollback();
                throw $e;
            }
        }
    }

    /**
     * 退款
     * @param array $params 退款参数
     * @return bool
     * @throws ValidationException
     */
    public function refund($params)
    {
        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            $order_code = $params['order_code'];//订单编号
            $orderObj = $this->getInteriorOrdersInfo($order_code);
            if ($orderObj) {
                //用户自主取消的flashpay支付方式的订单在款项状态为退款中的才可操作退款
                if ($orderObj->order_status == InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE && $orderObj->fund_status == InteriorOrderFundStatusEnums::FUND_STATUS_REFUNDING) {
                    $orderObj->fund_status = $params['fund_status'];
                    $params['fund_at'] ? $orderObj->fund_at = $params['fund_at'] : '';
                    $orderObj->fund_remark = $params['fund_remark'];
                    $orderObj->fund_code = $params['fund_code'];
                    $orderObj->fund_amount = $params['fund_amount'];
                    $orderObj->payment_voucher_status = $params['payment_voucher_status'] ?? $orderObj->payment_voucher_status;
                    $orderObj->save();

                    //保存附件信息
                    $attachment_arr = $params['attachment_arr'] ?? [];
                    if($attachment_arr) {
                        $attachments = [];
                        foreach ($attachment_arr as $item) {
                            $attachments[] = [
                                'id' => md5(time().random_int(1000,9999)),
                                'oss_bucket_type' => InteriorOrderStatusEnums::INTERIOR_ORDER_REFUND_OSS_TYPE,
                                'oss_bucket_key' => $orderObj->id,
                                'bucket_name' => $item['bucket_name'],
                                'object_key' => $item['object_key'],
                            ];
                        }
                        $flag = (new BaseRepository())->batch_insert('sys_attachment', $attachments);
                        if(!$flag) {
                            throw new Exception('batch insert sys_attachment 表失败!');
                        }
                    }

                    //记录退款日志
                    $add_hour = $this->getDI()['config']['application']['add_hour'];
                    $nowDate    = gmdate('Y-m-d H:i:s', time() + $add_hour * 3600);
                    $record = new InteriorOrdersRefundRecordModel();
                    $record->order_code = $order_code;
                    $record->staff_id = $params['staff_id'];
                    $record->staff_name = $params['staff_name'];
                    $record->type = $params['type'];
                    $record->file_path = $params['file_path'] ?? '';
                    $record->submit_at = $nowDate;
                    $refund_status_enums = InteriorOrderFundStatusEnums::getCodeTxtMap($this->lang);
                    $from_refund_status = $refund_status_enums[InteriorOrderFundStatusEnums::FUND_STATUS_REFUNDING];
                    $to_refund_status = $refund_status_enums[$params['fund_status']];
                    $refund_amount = sprintf("%.2f", $params['fund_amount']);
                    $record->remark = $this->getTranslation()->_('interior_order_refund_record_remark',['from_refund_status'=>$from_refund_status,'to_refund_status'=>$to_refund_status, 'refund_amount'=>$refund_amount]);
                    $record->save();
                    $db->commit();
                } else {
                    throw new ValidationException($this->getTranslation()->_('order_can_not_refund'));
                }
            } else {
                throw new ValidationException($this->getTranslation()->_('order_info_not_found'));
            }
        } catch (ValidationException $e) {
            throw $e;
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }
}
