<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Controllers\ControllerBase;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\ClaimerApproveModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditUnionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\BaseRepository;
use Phalcon\DiInterface;
use WebGeeker\Validation\Validation;
use <PERSON>sonRPC\Client as JsonRPC_Client;


class ClaimerServer extends AuditBaseServer
{

    private static $single = null;
    private static $singleApproval = null;

    public static function getInstance($lang)
    {
        if (!self::$single) {
            self::$single = new self($lang);
        }

        return self::$single;
    }

    private function __construct($lang = 'zh-CN', DiInterface $di = null)
    {
        parent::__construct($lang, $di);
    }

    private function getSingleApproval()
    {
        if (!self::$singleApproval) {
            self::$singleApproval = new ApprovalServer($this->lang, $this->timeZone);
        }

        return self::$singleApproval;
    }

    /**
     * 添加网点理赔 申请
     *
     * @param $id
     * @param $organizationId
     * @param $operatorId
     * @param $clientId
     * @param $pno
     * @param $createdAt
     *
     */
    public function addApproval($id, $organizationId, $operatorId, $clientId, $pno, $createdAt)
    {
        $db = ClaimerApproveModel::beginTransaction($this);
        try {
            Validation::validate([
                "id" => $id,
                "organization_id" => $organizationId,
                "operator_id" => $operatorId,
                "client_id" => $clientId,
                "pno" => $pno,
                "created_at" => $createdAt
            ], [
                "id" => "Required|StrLenGe:1",
                "organization_id" => "Required|StrLenGe:2",
                "operator_id" => "Required|IntGe:0",
                "client_id" => "Required",
                "pno" => "Required|StrLenGe:2",
                "created_at" => "Required|Int",
            ]);

            $lastInfo = $this->getLastInfo($pno, true);
            if ($lastInfo && in_array($lastInfo['state'], [enums::$audit_status['panding']])) {
                $this->logger->write_log("pno " . $pno . " 状态审批完成或待审批中，不可重复申请", 'notice');
                return true;
            }

            $claimerApproveModel = new ClaimerApproveModel();
            $claimerApproveModel->pickup_claims_ticket_id = $id;
            $claimerApproveModel->serial_no = "CR" . $this->getID();
            $claimerApproveModel->organization_id = $organizationId;
            $claimerApproveModel->operator_id = $operatorId;
            $claimerApproveModel->client_id = $clientId;
            $claimerApproveModel->pno = $pno;
            $claimerApproveModel->state= enums::$audit_status['panding'];
            $claimerApproveModel->approval_at = gmdate("Y-m-d H:i:s", $createdAt);
            $claimerApproveModel->save();
            $auditId = $claimerApproveModel->id;

            $requestId = $this->getSingleApproval()->create($auditId, AuditListEnums::APPROVAL_TYPE_CLAIMER, $operatorId);
            if (!$requestId) {
                throw new \Exception('创建审批流失败');
            }
            $db->commit();
            return $requestId;
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log("addapproval_error 消费数据错误 file:" . $e->getFile() . " message: " . $e->getMessage() . " line: " . $e->getLine() . " ". $e->getTraceAsString() . " " .  json_encode([
                    "organization_id" => $organizationId,
                    "operator_id" => $operatorId,
                    "client_id" => $clientId,
                    "pno" => $pno,
                    "created_at" => $createdAt
                ]), "error");
        }
        unset($db, $lastInfo, $claimerApproveModel, $auditId, $organizationId, $operatorId, $clientId, $pno, $createdAt);
        return false;
    }

    /**
     * 更新理赔申请
     *
     * @param $operateId
     * @param $auditId
     * @param $status
     * @param $cancelReason
     *
     */
    public function updateClaimerApproval($operateId, $auditId, $status, $cancelReason)
    {
        $db = ClaimerApproveModel::beginTransaction($this);
        try {
            $auditInfo = ClaimerApproveModel::findFirst([
                'conditions' => ' id = :id:',
                'bind' => ['id' => $auditId],
                'for_update' => true
            ]);
            $auditInfo = $auditInfo ? $auditInfo->toArray() : [];
            if ($auditInfo) {
                if ($auditInfo && isset($auditInfo['state']) && in_array($auditInfo['state'], [enums::$audit_status['panding']])) {
                    if ($status == enums::$audit_status['approved']) {
                        $result = (new ApprovalServer($this->lang, "{$this->timeZone}"))->approval($auditId, AuditListEnums::APPROVAL_TYPE_CLAIMER, $operateId);
                    } else {
                        $result = (new ApprovalServer($this->lang, "{$this->timeZone}"))->reject($auditId, AuditListEnums::APPROVAL_TYPE_CLAIMER, $cancelReason, $operateId);
                    }
                    if (!$result) {
                        throw new \Exception( $auditInfo . " update failing " . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
                    }
                    $db->commit();
                } else {
                    throw new InnerException($this->getTranslation()->_('5202'));
                }
            } else {
                throw new \Exception( $auditId . " auditid not found..." . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
            }
        } catch (InnerException $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log("claimer file "
                . $e->getFile()
                . " line " . $e->getLine()
                . " message " . $e->getMessage()
                . " trace " . $e->getTraceAsString(), "info");

            $this->jsonReturn($this->checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log("claimer file "
                . $e->getFile()
                . " line " . $e->getLine()
                . " message " . $e->getMessage()
                . " trace " . $e->getTraceAsString(), "error");
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }

        return true;
    }

    /**
     * 获取最新一条审批数据
     * @param $pno
     * @param $forUpdate
     */
    private function getLastInfo($pno, $forUpdate)
    {
        $info = ClaimerApproveModel::findFirst([
            "conditions" => "pno = :pno:",
            "bind" => ["pno" => $pno],
            "order" => " id desc",
            "for_update" => $forUpdate
        ]);

        return $info ? $info->toArray() : [];
    }
    /**
     * java验证数据登陆
     * @param $method
     * @param $param
     * @param $url
     * @return array|mixed
     */
    public function GetApiDatApiToken($method, $param, $url)
    {
        // 验证数据
        if(empty(trim($method)) || empty($url) || empty($param))
        {
            return [];
        }
        // 获取当前语言
        $locale = $this->lang;
        if(!$locale)
        {
            $locale = 'zh-CN';
        }

        // 实例化jsonRPC接口
        $client = new JsonRPC_Client($url);

        $data_params = [
            ['locale' => "$locale"],
            //$param
        ];
        //TODO 此参数没有使用key value的形式，数个数
        $data_params = array_merge($data_params, $param);
        $result = [];
        try
        {
            // 获取Api回调数据
            $result['result'] = $client->execute($method, $data_params);
            if(isset($result['error'])){
                $result['info'] = $result['error'];
                unset($result['error']);
            }
            $res = [
                'request'  => $data_params,
                'response' => $result
            ]; //echo
            //region 记录日志
            $logger = $this->getDI()->get('logger');
            $logger->write_log(json_encode($res), 'info');
            //endregion
        } catch(Exception $e)
        {
            //region 记录日志
            $log    = [
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ];
            $res    = [
                'request'   => $data_params,
                'response'  => '',
                'exception' => $log
            ];
            $logger = $this->getDI()->get('logger');
            $logger->write_log(json_encode($res, JSON_UNESCAPED_UNICODE), 'info');
            //endregion

            $result['error']['message'] = $e->getMessage();
            $result['error']['code'] = $e->getCode();

        }
        // 返回数据
        return $result;
    }


    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $auditInfo = ClaimerApproveModel::findFirst($auditId);
        $auditInfo = $auditInfo ? $auditInfo->toArray() : [];
        $data = ["data" => ["head" => [], "detail" => []]];

        if ($auditInfo) {
            $store = SysStoreModel::findFirst([
                    'conditions' => 'id = :storeId:',
                    'bind'       => ['storeId' => $auditInfo['organization_id']]
            ]);
            $store = $store ? $store->toArray() : [];

            $data['data']['head'] = [
                "title" => $this->getTranslation()->_("7024"),
                "origin_id" => (string) $auditId,
                "id" => $auditInfo['id'],
                "staff_id" => $auditInfo['operator_id'],
                "type" => enums::$audit_type['CR'],
                "created_at" => show_time_zone($auditInfo['approval_at']),
                "updated_at" => show_time_zone($auditInfo['updated_at']),
                "status" => (string) $auditInfo['state'],
                "status_txt" => $this->getTranslation()->_("audit_status.".$auditInfo['state']),
                "serial_no" => $auditInfo['serial_no'],
                "store_id" => $auditInfo['operator_id'],
                "store_name" =>  $store ? $store['name'] : "",
            ];
            $result = $this->GetApiDatApiToken('getPickupClaimsTicket', [
                [
                    'pickup_claims_ticket_id' => $auditInfo['pickup_claims_ticket_id'],
                ],
//                ['pno' => (string)1]
            ],  $this->config->api->get_claims_ticket);
            $this->getDI()->get("logger")->write_log("claims_ticket pno:" . $auditInfo['pickup_claims_ticket_id']  . " result:" . json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            if ($result && isset($result['result'])) {
                $staffs = HrStaffInfoModel::find([
                    'conditions' => 'staff_info_id in ({staff_ids:array})',
                    'bind'       => ['staff_ids' => array_filter([$auditInfo['client_id'], $auditInfo['operator_id']])],
                ]);
                $staffs = $staffs ? array_column($staffs ->toArray(), null, 'staff_info_id') : [];

                $attachMents = array_column($result['result']['id_card_image_list'], 'object_url');
                $attachMents = array_merge($attachMents, array_column($result['result']['bankbook_image_list'], 'object_url'));
                $attachMents = array_merge($attachMents, array_column($result['result']['agreement_image_list'], 'object_url'));
                $attachMents = array_merge($attachMents, array_column($result['result']['goods_price_image_list'], 'object_url'));
                $attachMents = array_merge($attachMents, array_column($result['result']['instructions_image_list'], 'object_url'));
                $attachMents = array_merge($attachMents, array_column($result['result']['others_image_list'], 'object_url'));
                $data['data']['detail'] = [
                    ["key" => $this->getTranslation()->_('claims_amount'), "value" => bcdiv($result['result']['claims_amount'], 100, 2)."THB"],
                    ["key" => $this->getTranslation()->_('organization_id'), "value" => $store ? $store['name'] . " " . $store['id'] : ""],
                    ["key" => $this->getTranslation()->_('operator_id'), "value" => $staffs && isset($staffs[$auditInfo['operator_id']]) ? $staffs[$auditInfo['operator_id']]['name'] . " " . $staffs[$auditInfo['operator_id']]['staff_info_id'] : ""],
                    ["key" => $this->getTranslation()->_('approval_at'), "value" => show_time_zone($auditInfo['approval_at'])],
                    ["key" => $this->getTranslation()->_('costomer_id'), "value" => $auditInfo['client_id']],
                    ["key" => $this->getTranslation()->_('client_mobile'), "value" => $result['result']['client_mobile']],
                    ["key" => $this->getTranslation()->_('pno_no'), "value" => $auditInfo['pno']],
                    ["key" => $this->getTranslation()->_('customer_type_category'), "value" => $this->getTranslation()->_("customer_type_category_" . $result['result']['customer_type_category'])] ,
                    ["key" => $this->getTranslation()->_('diff_marker_category'), "value" => $this->getTranslation()->_("diff_marker_category_" . $result['result']['diff_marker_category'])],
                    ["key" => $this->getTranslation()->_('instructions_image_list'), "value" => $attachMents],
                ];
            }
        }

        return $data;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(
            false,
            false,
            false,
            false,
            false,
            false);
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $info = ClaimerApproveModel::findFirst($auditId);
        $info = $info ? $info->toArray() : [];
        $summary = [];
        if ($info) {
            $summary = [
                ['key' => 'organization_id', 'value' => $info['organization_id']],
                ['key' => 'operator_id', 'value' => $info['operator_id']],
                ['key' => 'approval_at', 'value' => $info['approval_at']],
            ];
        }
        return $summary;
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId      审批ID
     * @param int $state        审批状态
     * @param null $extend      扩展字段
     * @param bool $isFinal
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            // 更新
            $claimer = ClaimerApproveModel::findFirst($auditId);
            $claimer->state = $state;
            $claimer->save();
            // 投递队列
            //  "pno":"TH7601V254",
            //  "auditResult":1
            //  "auditAt":1604038506,
            //  "remark":"xxx"
            $remark = '';
            if (in_array($state, [enums::$audit_status['revoked'], enums::$audit_status['dismissed']])) {
                $remark = '';
            }
            $data = [
                "id" => $claimer->pickup_claims_ticket_id,
                "pno" => $claimer->pno,
                "auditResult" => (int)$state,
                "auditAt" => time(),
                "remark" => $remark,
            ];

            $rmq = new RocketMQ('store_claimer_audit_producer');
            $rmq->setType(RocketMQ::TAG_NAME_PICKUP_CLAIMS_RESULT);
            $rid = $rmq->sendMsgByTag($data);
            $this->logger->write_log('backyard to MS rmq-store_claimer_audit_producer exception: ' . $rid, 'info');
        }

        return true;
    }

    /**
     * 样例
     * from_node_id | to_node_id | valuate_formula | valuate_code
     * -------------+------------+-----------------+-------------
     *      4       |     5      |    $p1 == 4     | getSubmitterDepartment
     *
     * 表示当提交人的部门为4时，审批节点4的下一个节点是5
     * 需要在 getWorkflowParams 中返回申请人所在的部门字段
     *
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $store = [];
        $info = ClaimerApproveModel::findFirst($auditId);
        $info = $info ? $info->toArray() : [];
        if ($info && isset($info['organization_id'])) {
            $store = SysStoreModel::findFirst([
                'conditions' => 'id = :storeId:',
                'bind'       => ['storeId' => $info['organization_id']]
            ]);
            $store = $store ? $store->toArray() : [];
        }
        return $store;
    }





}
