<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 10/20/21
 * Time: 4:28 PM
 */

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\OsSpecialPriceModel;
use FlashExpress\bi\App\Models\backyard\OsSpecialPriceStoreModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

class OsPriceServer extends AuditBaseServer
{

    public static $store_category = array(
        array('id' => SysStoreServer::CATEGORY_SP,'name' => 'SP'),
        array('id' => SysStoreServer::CATEGORY_BDC,'name' => 'BDC'),
    );
    public static $job_permission = array(
        'transportation settlement supervisor','transportation settlement manager'
    );

    public static $need_job_name = array('transportation settlement supervisor');

    public function __construct($lang = 'zh-CN', $timezone = '+08:00')
    {
        parent::__construct($lang, $timezone);

    }


    public function getDetail(int $auditId, $user, $comeFrom)
    {

        //[1]获取详情数据
        $result = OsSpecialPriceModel::findFirst($auditId);
        if (empty($result) || empty($result->id)){
            $this->logger->write_log('os price detail ' .$auditId ,'info');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        $result = $result->toArray();
        $add_hour = $this->config->application->add_hour;
        $result['created_at'] = date('Y-m-d H:i:s',strtotime("{$result['created_at']}") + $add_hour * 3600);
        $result['updated_at'] = date('Y-m-d H:i:s',strtotime("{$result['updated_at']}") + $add_hour * 3600);

        //获取关联网点
        $store_info = OsSpecialPriceStoreModel::find("origin_id = {$result['id']}")->toArray();
        $store_ids = $store_name = '';
        if(!empty($store_info)){
            $store_ids = array_column($store_info,'store_id');
            //要改为网点名称
            $store_names = SysStoreModel::find(
                [
                    'conditions' => 'id in({ids:array})',
                    'bind'       => ['ids' => $store_ids],
                    'columns'    => ['id', 'name']
                ]
            )->toArray();

            $store_name = implode(',',array_column($store_names,'name'));

        }


        $category = self::$store_category;
        $category = array_column($category,'name','id');
        $job_name = HrJobTitleModel::findFirst($result['job_title']);
        //[2]组织详情数据
        $detailLists = [
            //网点类型
            'store_category' => $category[$result['store_category']],
            //外协员工类型
            'os_job_title' => $job_name->job_name,

            //生效网点
            'effect_store' => $store_name,

            //生效日期
            'effect_start_date' => $result['start_date'],

            //结束日期
            'effect_end_date' => $result['end_date'],

            //特殊价格表
            'special_price_list' => $result['price_name'],

            //备注
            'remark' => $result['remark'],

        ];

        $returnData['data']['detail'] = $this->format($detailLists);
        $audit_re = new AuditlistRepository($this->lang,$this->timeZone);
        $data = [
            'title'       => $audit_re->getAudityType(enums::$audit_type['OPR']),
            'id'          => $result['id'],
            'staff_id'    => $result['staff_info_id'],
            'type'        => enums::$audit_type['OPR'],
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['status'],
            'status_text' => $audit_re->getAuditStatus('10' . $result['status']),
            'notice'      => '',
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        $returnData['data']['head']   = $data;
        return $returnData;
    }


    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $info = OsSpecialPriceModel::findFirst($auditId);
            if (empty($info) || empty($info->id)){
                $this->logger->write_log('os price 回写 错误 找不到用户信息 ' .$auditId ,'info');
                return false;
            }

            //调用java 同步 审核数据
            if($state == enums::WF_ACTION_APPROVE){
                $store_info = OsSpecialPriceStoreModel::find("origin_id = {$auditId}")->toArray();
                $store_ids = '';
                if(!empty($store_info))
                    $store_ids = implode(',',array_column($store_info,'store_id'));

                $param['id'] = $info->price_id;
                $param['state'] = intval($state);
                $param['store_id'] = $store_ids;
                $param['start_date'] = $info->start_date;
                $param['end_date'] = $info->end_date;
                $param['remark'] = $info->remark;
                $param['operator_id'] = $extend['staff_id'];
                $url = $this->config->api->java_http_url . "/svc/outsourcing/delivery/priceStateUpdate";
                $fle_return = $this->httpPost($url, $param);
                $this->logger->write_log('os price 同步 fle  ' .json_encode($fle_return) ,'info');

                if(empty($fle_return) || $fle_return['code'] != ErrCode::SUCCESS)
                    throw new \Exception("fle sync error ".json_encode($fle_return));
            }


            $info->status = $state;
            $flag = $info->update();
            return $flag;
        }
    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $info = OsSpecialPriceModel::findFirst($auditId);
        $apply_id = $info->staff_info_id;
        $staff_model = new StaffRepository($this->lang);
        $staff_info = $staff_model->getStaffPosition($apply_id);
        $data['job_title'] = $staff_info['job_title'];
        $data['job_name'] = strtolower($staff_info['job_name']);
        $data['need_job_name'] = self::$need_job_name;
        $this->logger->write_log('os price work param '. "{$auditId} {$staff_info['staff_info_id']} {$staff_info['job_title']} {$staff_info['node_department_id']}",'info');
        return $data;
    }

    public function genSummary(int $auditId, $user)
    {
        //获取详情
        $info = OsSpecialPriceModel::findFirst($auditId);
        if (empty($info) || empty($info->id))
            return '';
        $staff_info = HrStaffInfoModel::findFirst("staff_info_id = {$user}");

        $param = [
            [
                'key'   => 'apply_parson',
                'value' => $staff_info->name
            ],
            [
                'key'   => 'report_id',
                'value' => $user
            ],

        ];
        return $param ?? "";
    }



    public function add($param){
        if(empty($param['store_category']))
            throw new ValidationException($this->getTranslation()->_('miss_args').' store_category');
        if(empty($param['job_title']))
            throw new ValidationException($this->getTranslation()->_('miss_args').' job_title');
        if(empty($param['start_date']))
            throw new ValidationException($this->getTranslation()->_('miss_args').' start_date');
        if(empty($param['end_date']))
            throw new ValidationException($this->getTranslation()->_('miss_args').' end_date');
        if(empty($param['price_id']))
            throw new ValidationException($this->getTranslation()->_('miss_args').' price_id');
        if(empty($param['store_ids']))
            throw new ValidationException($this->getTranslation()->_('miss_args').' store_ids');

        if(count($param['store_ids']) > 50)
            throw new ValidationException($this->getTranslation()->_('opr_50_notice'));

        if(!empty($param['remark']) && strlen($param['remark']) > 1000)
            throw new ValidationException($this->getTranslation()->_('opr_1000_notice'));

        //生效日期 只能 当天到 7天 之间
        $current = date('Y-m-d');
        if($param['start_date'] < $current || $param['start_date'] > date('Y-m-d',strtotime("+7 day")))
            throw new ValidationException($this->getTranslation()->_('opr_7_days_notice'));

        //生效区间不能超过31天
        if(date('Y-m-d',strtotime("{$param['start_date']} +31 day")) <= $param['end_date'])
            throw new ValidationException($this->getTranslation()->_('opr_31_days_notice'));

        $staff_id = $param['user_info']['id'];
        $db = $this->getDI()->get('db');
        $db->begin();
        $main_model = new OsSpecialPriceModel();
        $store_model = new OsSpecialPriceStoreModel();

        $insert['serial_no'] = 'osp'.$this->getID();
        $insert['staff_info_id'] = $param['user_info']['id'];
        $insert['store_category'] = $param['store_category'];
        $insert['job_title'] = $param['job_title'];
        $insert['start_date'] = date('Y-m-d', strtotime($param['start_date']));
        $insert['end_date'] = date('Y-m-d', strtotime($param['end_date']));
        $insert['price_id'] = $param['price_id'];
        $insert['price_name'] = $param['price_name'];
        $insert['remark'] = empty($param['remark']) ? '' : $param['remark'];

        $flag = $main_model->create($insert);
        if(!$flag){
            $db->rollback();
            throw new \Exception('main_model create error');
        }

        foreach ($param['store_ids'] as $store_id){
            $row = array();
            $clone = clone $store_model;
            $row['origin_id'] = $main_model->id;
            $row['store_id'] = $store_id;

            $flag = $clone->create($row);
            if(!$flag){
                $db->rollback();
                throw new \Exception('store create error');
            }
        }

        //审批流相关业务
        $params = $this->genSummary($main_model->id, $staff_id);
        $insertAuditData = [
            [
                'id_union'      => 'opr_' . $main_model->id,
                'staff_id_union'=> $staff_id,
                'type_union'    => enums::$audit_type['OPR'],
                'status_union'  => enums::$audit_list_status['panding'],
                'store_id'      => "",
                'data'          => "",
                'table'         => 'os_special_price',
                'origin_id'     => $main_model->id,
                'approval_id'   => 0,
                'created_at'    => gmdate('Y-m-d H:i:s', time()),
                'summary'       => json_encode($params, JSON_UNESCAPED_UNICODE),
            ]
        ];
        $this->getDI()->get('db')->insertAsDict('staff_audit_union', $insertAuditData[0]);
        //创建
        $server = new ApprovalServer($this->lang,$this->timeZone);
        $flag = $server->create($main_model->id, enums::$audit_type['OPR'], $staff_id);
        if (!$flag) {
            $db->rollback();
            throw new \Exception('os_price approval failed');
        }
        $db->commit();
        return 1;

    }

    /**
     * 获取外协派件价格表
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function get_base_price($param)
    {
        if(empty($param['store_category']))
            throw new ValidationException($this->getTranslation()->_('miss_args').' store category');
        if(empty($param['job_title']))
            throw new ValidationException($this->getTranslation()->_('miss_args').' job_title');

        $fle_param['store_type'] = intval($param['store_category']);
        $fle_param['outsourcing_employee_type'] = intval($param['job_title']);
        $url = $this->config->api->java_http_url . "/svc/outsourcing/delivery/getPriceList";
        $fle_return = $this->httpPost($url, $fle_param);

        $this->logger->write_log('os price rpc ' . json_encode($fle_return),'info');
        $data = $fle_return['data'] ?? null;
        return array('code' => ErrCode::SUCCESS,'message' => '', 'data' => $data);
    }

    //审核 特殊价格
    public function update_status($paramIn,$user_info){

        if(empty($paramIn['audit_id']) || empty($paramIn['status']))
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        $info = OsSpecialPriceModel::findFirst($paramIn['audit_id']);
        if(empty($info))
            return $this->checkReturn(-3, $this->getTranslation()->_('can not find info'));

        $info = $info->toArray();

        $status = intval($paramIn['status']);
        $app_server = new ApprovalServer($this->lang,$this->timeZone);

        if ($status == enums::$audit_status['approved']) {//审核通过
            $res = $app_server->approval($info['id'], enums::$audit_type['OPR'], $user_info['id']);
        } elseif ($status == enums::$audit_status['dismissed']) {//驳回
            $res = $app_server->reject($info['id'], enums::$audit_type['OPR'], $paramIn['reject_reason'], $user_info['id']);
        } elseif ($status == enums::$audit_status['revoked']) {//撤销
            $res = $app_server->cancel($info['id'], enums::$audit_type['OPR'], $paramIn['reject_reason'], $user_info['id']);
        }else{
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }
        if (!isset($res) || empty($res)) {
            $this->logger->write_log('os price update_status error ' . json_encode($paramIn).json_encode($res),'info');
            return $this->checkReturn(-3, 'server error');
        }
        return $this->checkReturn(['data' => ['id' => $info['id']]]);
    }

    public function permission($param){
        //transportation 部门（是否包含子部门 待确认）的 Settlement Manager和 Settlement supervisor 职位
        $dep_info = SysDepartmentModel::findFirst("(name = 'Transportation Settlement' or name = 'Transportation Settlement (Malaysia)') and deleted = 0 ");
        if(empty($dep_info) || empty($dep_info->id))
            return false;

        $job_info = HrJobTitleModel::findFirst($param['job_title']);
        if(empty($job_info) || empty($job_info->job_name))
            return false;

        $department_id = $param['department_id'];
        if($department_id == $dep_info->id && in_array(strtolower($job_info->job_name),self::$job_permission))
            return true;

//        //子部门
//        $sub_dep_list = SysDepartmentModel::find([
//            'conditions' => "ancestry_v3 like '{$dep_info->ancestry_v3}/%'",
//            'columns' => 'id'
//        ])->toArray();
//
//        $sub_ids = empty($sub_dep_list) ? array() : array_column($sub_dep_list,'id');
//        $this->logger->write_log("os price permission {$department_id} " . json_encode($sub_ids));
//        if(in_array($department_id,$sub_ids))
//            return true;
        return false;

    }

    /**
     * 外协员工类型列表
     * @return mixed
     */
    public function get_enum()
    {
      return  HrJobTitleModel::find([
            'conditions' => "id in ({ids:array}) ",
            'columns' => 'id,job_name',
            'bind' => ['ids' =>  [enums::$job_title['car_courier'],enums::$job_title['van_courier'] , enums::$job_title['bike_courier']]]
        ])->toArray();
    }

}


