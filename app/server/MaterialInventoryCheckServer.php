<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\InventoryCheckEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\oa\MaterialInventoryCheckModel;
use FlashExpress\bi\App\Models\oa\MaterialInventoryCheckStaffModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StoreRepository;

/**
 * 新资产-资产盘点操作服务层
 * Class MaterialInventoryCheckServer
 * @package FlashExpress\bi\App\Server
 */
class MaterialInventoryCheckServer extends BaseServer
{
    public $timezone;
    const OA_VALIDATE_CODE = 2;

    /**
     * MaterialInventoryCheckServer constructor.
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * rpc请求OA端
     * @param array $params 请求参数组
     * @param string $method 请求方法
     * @return array
     */
    private function rpcOa($params, $method)
    {
        $api_client = new ApiClient('oa_rpc', '', $method, $this->lang);
        $api_client->setParams($params);
        $res = $api_client->execute();
        if (!isset($res['result'])) {
            return [
                'code' => self::OA_VALIDATE_CODE,
                'msg'  => $this->getTranslation()->_('please try again'),
                'data' => [],
            ];
        }
        $res['result']['msg'] = $res['result']['message'] ?? '';
        unset($res['result']['message']);
        return $res['result'];
    }

    /**
     * 资产盘点-资产盘点通知消息详情
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getMsgDetail($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'get_msg_detail');
    }

    /**
     * 资产盘点-开始盘点/去盘点
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function taskStart($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_start');
    }

    /**
     * 资产盘点-任务清单-待处理、已处理总数
     * @param array $params 请求参数组
     * @return array
     */
    public function getTaskCount($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(task.id) as total');
        $builder->from(['task' => MaterialInventoryCheckStaffModel::class]);
        $builder->leftjoin(MaterialInventoryCheckModel::class, 'task.inventory_check_id = main.id', 'main');
        $builder = $this->getTaskCondition($builder, $params);
        return ['data' => intval($builder->getQuery()->getSingleResult()->total)];
    }

    /**
     * 组装盘点任务查询条件
     * @param object $builder 查询器对象
     * @param array $condition['staff_id'=>'员工工号','type'=>'1待处理，2已处理'] 筛选条件组
     * @return mixed
     */
    private function getTaskCondition(object $builder, array $condition)
    {
        $builder->where('task.staff_id = :staff_id: and task.is_deleted = :is_deleted: and main.is_deleted = :is_deleted:', ['staff_id' => $condition['staff_id'], 'is_deleted' => 0]);
        $condition['type'] = $condition['type'] ?? InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PENDING;
        if ($condition['type'] == InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PENDING) {
            //待处理(待盘点/盘点中/未到盘点结束时间)
            $builder->andWhere('task.status < :status: and main.end_at > :now:', ['status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE, 'now' => date('Y-m-d H:i:s')]);
        } else if ($condition['type'] == InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PROCESSED) {
            //已完成(已完成/已终止)
            $builder->andWhere('task.status in ({status:array})', ['status' => [InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE, InventoryCheckEnums::INVENTORY_CHECK_STATUS_TERMINAL]]);
        }
        return $builder;
    }

    /**
     * 资产盘点-任务清单-待处理,已结束列表
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getTaskList($params, $user)
    {
        $params['staff_name'] = $user['name'];
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'get_task_list');
    }

    /**
     * 资产盘点-任务详情
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getTaskInfo($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'get_task_info');
    }

    /**
     * 资产盘点-资产盘点清单-待盘点、已盘点总数
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getTaskAssetCount($params, $user)
    {
        $params['staff_id'] = $user['id'];
        $result = $this->rpcOa($params, 'get_task_asset_count');
        return $result['data'] ?? 0;
    }

    /**
     * 资产盘点-资产盘点清单-待盘点、已盘点列表
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getTaskAssetList($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'get_task_asset_list');
    }

    /**
     * 资产盘点-资产盘点清单-未盘到
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function lose($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_asset_lose');
    }

    /**
     * 资产盘点-资产盘点清单-批量未盘到
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function batchLose($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_asset_batch_lose');
    }

    /**
     * 资产盘点-资产盘点清单-盘到
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function match($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_asset_match');
    }

    /**
     * 资产盘点-资产盘点清单-批量盘到
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function batchMatch($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_asset_batch_match');
    }

    /**
     * 资产盘点-资产盘点清单-修改使用人
     * @param array $params 请求参数组
     * @return array
     */
    public function staffSearch($params)
    {
        return ['data' => (new StaffRepository())->searchStaff($params)];
    }

    /**
     * 资产盘点-资产盘点清单-修改使用网点
     * @param array $params 请求参数组
     * @return array
     */
    public function storeSearch($params)
    {
        return ['data' => (new StoreRepository())->searchStore($params)];
    }

    /**
     * 资产盘点-资产盘点清单-信息有误
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function notMatch($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_asset_not_match');
    }

    /**
     * 资产盘点-资产盘点清单-查看信息有误
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function viewNotMatch($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_asset_view_not_match');
    }

    /**
     * 资产盘点-资产盘点清单-修改数量
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function updateNum($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_asset_update_num');
    }

    /**
     * 资产盘点-资产盘点清单-搜索资产
     * @param array $params 请求参数组
     * @return array
     */
    public function barcodeSearch($params)
    {
        return $this->rpcOa($params, 'task_asset_barcode_search');
    }

    /**
     * 资产盘点-资产盘点清单-添加资产
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function add($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_asset_add');
    }

    /**
     * 资产盘点-资产盘点清单-删除资产
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function del($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_asset_del');
    }

    /**
     * 资产盘点-资产盘点清单-扫码盘点
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function isOwner($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_asset_scan_code');
    }

    /**
     * 资产盘点-资产盘点清单-确认无资产
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function done($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_asset_done');
    }

    /**
     * 资产盘点-资产盘点清单-更新盘点
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function update($params, $user)
    {
        $params['staff_id'] = $user['id'];
        return $this->rpcOa($params, 'task_asset_update');
    }

    /**
     * 资产盘点-资产盘点清单-限制打卡
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function verifyHasUndoneInventoryTask($user)
    {
        $params['staff_id'] = $user['id'];
        $result = $this->rpcOa($params, 'task_check_punch_out');
        $data = $result['data'] ?? [];
        if ($data) {
            $t = $this->getTranslation($this->lang);
            $data['asset_inventory_detail']['dialog_msg'] = $t->_('inventory_staff_punch_out.' . strtolower(get_country_code()));
            $data['asset_inventory_detail']['dialog_btn_msg'] = $t->_('inventory_staff_punch_out_btn');
            $data['asset_inventory_detail']['dialog_jump_url'] = env('sign_url') . '/#/inventoryTask';
        }
        return $data;
    }
}
