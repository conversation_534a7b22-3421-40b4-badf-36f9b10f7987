<?php
/**
 * Author: Bruce
 * Date  : 2023-07-31 15:33
 * Description:
 */

namespace FlashExpress\bi\App\Server\Osm;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\BaseServer;

class PunishServer extends BaseServer
{
    public function __construct($lang = '')
    {
        parent::__construct($lang);
    }

    /**
     * 处罚列表
     * @param $params
     * @param $user_info
     * @return array
     * @throws BusinessException
     */
    public function getListInfo($params, $user_info)
    {
        $params['page']       = empty($params['page']) ? 1 : $params['page'];
        $params['page_size']  = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size']  = ($params['page_size'] > 100) ? 100 : $params['page_size'];
        $params['month']      = empty($params['month']) ? date('Y-m') : $params['month'];
        $params['company_id'] = $user_info['id'];

        $ret                = new ApiClient('bi_rpcv2', '', 'abnormal.get_vs_temp_staff_list', $this->lang);
        $ret->setParams($params);
        $res = $ret->execute();
        $this->getDI()->get('logger')->write_log("punish osm 参数:".json_encode($params).";结果:".json_encode($res),
            'info');
        if (!isset($res['result'])) {
            throw new BusinessException($res['error']);
        }
        if ($res['result']['code'] == ErrCode::SUCCESS) {
            if(empty($res['result']['data'])) {
                throw new BusinessException($this->getTranslation()->_('data_error'));
            }
            $punishInfo = $res['result']['data'];

            $data['penalty_amount'] = $punishInfo['penalty_amount'] ?? '0';
            $data['total']          = intval($punishInfo['total']) ?? 0;
            $data['list']           = $punishInfo['list'] ?? [];
            return $data;
        }
        $msg = isset($res['result']['msg']) ? $res['result']['msg'] : $res['error'];
        throw new BusinessException($this->getTranslation()->_($msg));
    }

    /**
     * 处罚详情
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function detail($params)
    {
        $ret = new ApiClient('bi_rpcv2', '', 'abnormal.get_vs_temp_staff_info', $this->lang);
        $ret->setParams($params);
        $res = $ret->execute();
        $this->getDI()->get('logger')->write_log("punish osm 参数:" . json_encode($params) . ";结果:" . json_encode($res),
            'info');
        if (!isset($res['result'])) {
            throw new BusinessException($res['error']);
        }
        if ($res['result']['code'] == ErrCode::SUCCESS) {
            if(empty($res['result']['data'])) {
                throw new BusinessException($this->getTranslation()->_('data_error'));
            }
            $punishInfo = $res['result']['data'];
            $data          = [
                'equipment_number'     => $punishInfo['equipment_number'],
                'violators'            => $punishInfo['violators'],
                'company_name'         => $punishInfo['company_name'],
                'penalty_amount'       => $punishInfo['penalty_amount'],
                'reporting_time'       => $punishInfo['reporting_time'],
                'recording_start_time' => $punishInfo['recording_start_time'],
                'recording_end_time'   => $punishInfo['recording_end_time'],
                'video_url'            => $punishInfo['video_url'],
            ];

            return $data;
        }
        $msg = isset($res['result']['msg']) ? $res['result']['msg'] : $res['error'];
        throw new BusinessException($msg);
    }

    /**
     * 获取处罚消息详情
     * @param $params
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getPunishInfo($params)
    {
        $server          = new BackyardServer($this->lang, $this->timeZone);
        // 验证该员工是否收到处罚消息
        $msgInfo = $server->getMessageCourierDetail($params);
        if (empty($msgInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        $data['id'] = $msgInfo['related_id'];
        $ret                = new ApiClient('bi_rpcv2', '', 'abnormal.get_id_vs_info', $this->lang);
        $ret->setParams($data);
        $res = $ret->execute();
        $this->getDI()->get('logger')->write_log("getPunishInfo 参数:".json_encode($data).";结果:".json_encode($res),
            'info');
        if (!isset($res['result'])) {
            throw new BusinessException($res['error']);
        }

        if ($res['result']['code'] == ErrCode::SUCCESS) {
            $punishInfo = $res['result']['data'];
            $data = [
                'store_name'    => $punishInfo['store_name'] ?? '',//网点名称
                'staff_info_id' => $punishInfo['staff_info_id'] ?? '',//员工id
                'staff_name'    => $punishInfo['staff_name'] ?? '',//员工名称
                'report_time'   => $punishInfo['report_time'] ?? '',//上报时间
                'release_date'  => $punishInfo['release_date'] ?? '',//公布时间
                'record_time'   => $punishInfo['record_time'] ?? '',//违规时间段
                'punish_money'  => $punishInfo['punish_money'] ?? '',//处罚金额。
            ];

            return $data;
        }
        $msg = isset($res['result']['msg']) ? $res['result']['msg'] : $res['error'];
        throw new BusinessException($msg);
    }

}