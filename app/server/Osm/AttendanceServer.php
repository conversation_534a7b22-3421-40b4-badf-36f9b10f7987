<?php


namespace FlashExpress\bi\App\Server\Osm;


use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingHubStaffAttendanceCorrectionModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingHubStaffAttendanceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditBaseServer;
use FlashExpress\bi\App\Server\AuditOptionRule;

class AttendanceServer extends AuditBaseServer
{

    /**
     * @param $companyId
     * @param $status
     * @param $page
     * @param $pageSize
     * @return array
     */
    public function getCorrectionList($companyId,$params,$page,$pageSize)
    {
        $params['start_date'] = empty($params['start_date']) ? date('Y-m-d',strtotime('-30 days')) : $params['start_date'];
        $params['end_date']   = empty($params['end_date']) ? date('Y-m-d') : $params['end_date'];

        $query = $this->modelsManager->createBuilder()
            ->columns('t1.id,t1.staff_info_id,t2.name as staff_name,t3.name as store_name,t1.attendance_date,
            t1.shift,t1.attendance_type,t1.status,t1.correction_datetime,t1.reason,t1.extra')
            ->from(['t1'=>OutsourcingHubStaffAttendanceCorrectionModel::class])
            ->leftJoin(HrStaffInfoModel::class,'t1.staff_info_id=t2.staff_info_id','t2')
            ->leftJoin(SysStoreModel::class,'t1.store_id=t3.id','t3')
            ->where("t1.company_id=:c_id: ",['c_id'=>$companyId])
            ->orderBy('t1.id desc');

        //已处理 = 已处理 + 超时关闭
        if($params['status'] == 3) {
            $query->andWhere('t1.status in ({status:array})', ['status' => [OutsourcingHubStaffAttendanceCorrectionModel::STATUS_3, OutsourcingHubStaffAttendanceCorrectionModel::STATUS_4]]);
        } else {
            $query->andWhere('t1.status = :status:', ['status'=>$params['status']]);
        }

        //员工id or name
        if(!empty($params['staff_keyword'])) {
            $query->andWhere('(t2.staff_info_id LIKE :staff_keyword: OR t2.name LIKE :staff_keyword:)', ['staff_keyword' => '%' . $params['staff_keyword'] . '%']);
        }

        //员工id or name
        if(!empty($params['start_date']) && !empty($params['end_date'])) {
            $startDate = gmdate('Y-m-d H:i:s', strtotime($params['start_date'] . ' 00:00:00'));
            $endDate   = gmdate('Y-m-d H:i:s', strtotime($params['end_date'] . ' 23:59:59'));
            $query->andWhere('t1.created_at >= :start_created: and t1.created_at <= :end_created:', ['start_created' => $startDate, 'end_created' => $endDate]);
        }

        //员工id or name
        if(!empty($params['sys_store_ids'])) {
            $sys_store_ids = explode(',', $params['sys_store_ids']);
            $query->andWhere('t1.store_id in ({sys_store_ids:array})', ['sys_store_ids' => $sys_store_ids]);
        }


        [$list ,$count] = $this->paginate($query,$page,$pageSize);
        foreach ($list as $k=>$v){
            $list[$k]['attendance_type'] = $this->t->t('hub_os_attendance_type_'.$v['attendance_type']);
            $list[$k]['correction_datetime'] = !empty($v['correction_datetime']) ? DateHelper::utcToLocal($v['correction_datetime']) : null;
            $list[$k]['extra'] = json_decode($v['extra'], true);
        }
        return [$list,$count];
    }

    /**
     * @param $id
     * @param $correctionDatetime
     * @param $reason
     * @param $extra
     * @return void
     * @throws BusinessException
     * @throws Exception
     */
    public function fillCorrection($id,$correctionDatetime,$reason,$extra)
    {
        $correction = OutsourcingHubStaffAttendanceCorrectionModel::findFirst($id);
        if (!$correction) {
            throw new Exception($this->getTranslation()->_('message_no_exists'));
        }
        if (in_array($correction->status, [
            OutsourcingHubStaffAttendanceCorrectionModel::STATUS_2,
            OutsourcingHubStaffAttendanceCorrectionModel::STATUS_3,
        ])) {
            throw new BusinessException($this->getTranslation()->_('5202'));
        }

        $this->getDI()->get('db')->begin();
        try {
            $correction->serial_no = 'OSAT'.$this->getRandomId();
            $correction->correction_datetime = DateHelper::localToUtc($correctionDatetime);
            $correction->reason = $reason;
            $correction->extra = json_encode($extra);
            $correction->status = OutsourcingHubStaffAttendanceCorrectionModel::STATUS_2;
            $correction->updated_at = DateHelper::localToUtc();
            $correction->save();
            //创建审批流
            $approvalServer = new ApprovalServer($this->lang,$this->timeZone);

            $extend = [
                'from_submit' => [
                    'sys_store_id' => $correction->store_id
                ]
            ];
            $req = $approvalServer->create($id, AuditListEnums::APPROVAL_TYPE_HUB_OS_AT, $correction->staff_info_id, null, $extend);
            $this->getDI()->get('db')->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log('fillCorrection err:' . $e->getMessage() . $e->getTraceAsString());
        }
    }

    /**
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $correction = OutsourcingHubStaffAttendanceCorrectionModel::findFirst($auditId);

        $head = [
            'title'       => (new AuditlistRepository($this->lang, $this->timeZone))->getAudityType(enums::$audit_type['AT']),
            'id'          => $correction->id,
            'staff_id'    => $correction->staff_info_id,
            'type'        => enums::$audit_type['OSAT'],
            'created_at'  => DateHelper::utcToLocal($correction->created_at),
            'updated_at'  => DateHelper::utcToLocal($correction->updated_at),
            //'status'      => $auditDetail['status'],
            //'status_text' => (new AuditlistRepository($this->lang, $this->timezone))->getAuditStatus('10' . $auditDetail['status']),
            'serial_no'   => $correction->serial_no ?? '',
        ];
        $staff_info = (new HrStaffInfoModel())->getOneByStaffId($correction->staff_info_id);
        $company = (new OutsourcingCompanyModel())->getOneById($correction->company_id);
        $store = (new SysStoreModel())->getOneStoreById($correction->store_id);
        $detail  = [
            'apply_parson'=> sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['staff_info_id'] ?? ''),
            'apply_company' => $company['company_full_name'] ?? '',
            'attendance_store' => $store['name'] ?? '',
            'attendance_date' => $correction->attendance_date,
            'reissue_card_time'=> DateHelper::utcToLocal($correction->correction_datetime),
            'attendance_type' => (new AuditRepository($this->lang))->typeName(1)[$correction->attendance_type],
            'audit_reason' => $correction->reason,
            'photo' => json_decode($correction->extra,true),
        ];

        $returnData['data']['head'] = $head;
        $returnData['data']['detail'] = $this->format($detail);

        return $returnData;
    }

    /**
     * @param int $auditId
     * @param $user
     * @return array[]
     */
    public function genSummary(int $auditId, $user)
    {
        $correction = OutsourcingHubStaffAttendanceCorrectionModel::findFirst($auditId);
        $data = [
            [
                'key' => 'attendance_date',
                'value' => $correction->attendance_date,
            ],
            [
                'key' => 'attendance_type',
                'value' => $correction->attendance_type,
            ],
        ];
        return $data;
    }

    /**
     * @param $auditId
     * @param $user
     * @param $state
     * @return array
     */
    public function getWorkflowParams($auditId, $user, $state = null): array
    {
        $correction    = OutsourcingHubStaffAttendanceCorrectionModel::findFirst($auditId);
        $workStoreInfo = SysStoreRepository::getSysStoreInfo($correction->store_id, 'id,category');
        return [
            'work_store_category' => $workStoreInfo ? $workStoreInfo['category'] : 0,
        ];
    }

    /**
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return true
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal){
            $correction = OutsourcingHubStaffAttendanceCorrectionModel::findFirst($auditId);
            if ($state == enums::$audit_status['approved']){
                $correction->status = OutsourcingHubStaffAttendanceCorrectionModel::STATUS_3;
                $correction->updated_at = DateHelper::localToUtc();
                $correction->save();
                // 更新考勤统计数据
                $this->updateAttendanceStats($correction->staff_info_id,$correction->attendance_date,$correction->attendance_type, $correction->correction_datetime,$correction->extra);
            }
            if ($state == enums::$audit_status['dismissed']){
                $correction->status = OutsourcingHubStaffAttendanceCorrectionModel::STATUS_3;
                $correction->updated_at = DateHelper::localToUtc();
                $correction->save();
            }
        }

        return true;
    }

    /**
     * @param $staffId
     * @param $date
     * @param $attType
     * @param $correctionDatetime
     * @param $extra
     * @return void
     */
    private function updateAttendanceStats($staffId,$date,$attType,$correctionDatetime,$extra)
    {
        $attendance = OutsourcingHubStaffAttendanceModel::findFirst(
            [
                'conditions'=>"staff_info_id=:staff_id: and hub_attendance_date = :date:",
                'bind' => ['staff_id'=> $staffId,'date'=> $date]
            ]
        );

        $clocking_time = $correctionDatetime;
        //如果补的是下班卡，则看上班卡有没有值，如果有，这个值取上班卡的值。
        if($attType == 2 && !empty($attendance->started_at)) {
            $clocking_time = $attendance->started_at;
        }

        if ($attendance){
            switch ($attType){
                case 1:
                    $attendance->clocking_time = $clocking_time;
                    $attendance->started_at = $correctionDatetime;
                    $attendance->started_extra = $extra;
                    $attendance->is_late = 0;
                    $attendance->late_times = 0;
                    $attendance->working_hours = $this->calcWorkingHours($attendance->started_at,$attendance->end_at);
                    $attendance->settlement_coefficient = $this->getSettlementCoefficient($attendance->working_hours,$attendance->is_holiday_vacations)*100;
                    break;
                case 2:
                    $attendance->clocking_time = $clocking_time;
                    $attendance->end_at = $correctionDatetime;
                    $attendance->end_extra = $extra;
                    $attendance->is_leave_early = 0;
                    $attendance->leave_early_times = 0;
                    $attendance->working_hours = $this->calcWorkingHours($attendance->started_at,$attendance->end_at);
                    $attendance->settlement_coefficient = $this->getSettlementCoefficient($attendance->working_hours,$attendance->is_holiday_vacations)*100;
                    break;
            }
            $this->getDI()->get("logger")->write_log(['hub_correction_data' => ['staff_id' => $staffId, 'att_type' => $attType, 'date' => $date,'clocking_time' => $clocking_time, 'started_at' => $correctionDatetime, 'started_extra' => $extra, 'attendance' => $attendance->toArray()]], "info");

            $attendance->save();
        }
    }

    /**
     * @param $start
     * @param $end
     * @return false|float
     */
    private function calcWorkingHours($start,$end)
    {
        if(empty($start) || empty($end)) {
            return 0;
        }
        $resTime = strtotime($end) - strtotime($start);
        if($resTime < 0) {
            return 0;
        }
        return floor(($resTime / 3600) * 10);
    }

    /**
     * 获取结算系数
     * @param $working_hours
     * @param $isHoliday
     * @return float|int
     */
    public function getSettlementCoefficient($working_hours, $isHoliday)
    {
        $working_hours = $working_hours / 10;//$working_hours 工时 是乘 10后的，所以要除以10。
        $settlement_coefficient = 0;
        if ($working_hours < 4) {
            $settlement_coefficient = 0;
        } elseif ($working_hours >= 4 && $working_hours < 6) {
            $settlement_coefficient = 0.5;
        } elseif ($working_hours >= 6 && $working_hours < 9) {
            $settlement_coefficient = 0.75;
        } elseif ($working_hours >= 9) {
            $settlement_coefficient = 1;
        }
        if ($isHoliday) {
            $settlement_coefficient = $settlement_coefficient * 2;
        }

        return $settlement_coefficient;
    }

    /**
     * @param $companyId
     * @return mixed
     */
    public function getPendingCount($companyId)
    {
        return OutsourcingHubStaffAttendanceCorrectionModel::count(
            [
                'conditions' => 'company_id = :company_id: and status = :status:',
                'bind'       => ['company_id' => $companyId, 'status' => OutsourcingHubStaffAttendanceCorrectionModel::STATUS_1],
            ]
        );

    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false, false, false, false, false, false);
    }
}