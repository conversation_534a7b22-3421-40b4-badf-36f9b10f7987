<?php


namespace FlashExpress\bi\App\Server\Osm;


use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\PasswordHash;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyDeviceTokenModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyModel;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class LoginServer extends BaseServer
{
    const PASSWORD_WRONG_KEY_PREFIX = 'osm_login_password_wrong_';  //密码错误超限key前缀

    public function __construct($lang = 'zh-CN')
    {
        parent::__construct($lang);
    }

    /**
     * 外协公司登录接口
     * @param $data
     * @return array
     */
    public function login($data)
    {
        try {
            //获取外协公司信息
            $outsourcing_company_model = new OutsourcingCompanyModel();
            $outsourcing_company       = $outsourcing_company_model->getOneByPhone($data['login']);
            if (empty($outsourcing_company) || $outsourcing_company['deleted'] != 0) {
                $this->getDI()->get('logger')->write_log('outsoucing-company-login-no-exists'.json_encode($data),
                    'info');
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_no_exists'),
                    ErrCode::VALIDATE_ERROR);
            }

            $cache     = $this->getDI()->get('redisLib');
            $cache_key = self::PASSWORD_WRONG_KEY_PREFIX.$data['login'];

            //密码超过5次，则当天不再登录
            $password_wrong_num = $cache->get($cache_key);
            if (RUNTIME == 'pro' && $password_wrong_num >= 5) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_password_wrong_astrict'),
                    ErrCode::VALIDATE_ERROR);
            }

            //验证密码
            $hash = new PasswordHash(10, false);
            $bool = $hash->checkPassword($data['password'], $outsourcing_company['encrypted_password']);
            if (!$bool) {
                $cache_time = strtotime(date('Y-m-d').' 23:59:59') - time();  //时区问题有待验证
                if (empty($password_wrong_num)) {
                    $cache->set($cache_key, 1, $cache_time);
                } elseif ($password_wrong_num > 0) {
                    $cache->incr($cache_key);
                }
                $this->getDI()->get('logger')->write_log('outsoucing-company-login-password-wrong'.json_encode($data),
                    'info');
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_wrong_password'),
                    ErrCode::VALIDATE_ERROR);
            }

            //生成token
            $result = $this->create_token($outsourcing_company, $data);
            //登录成功，删除密码错误次数
            if ($password_wrong_num) {
                $this->unfreezePassword($data['login']);
            }
        } catch (ValidationException $e) {
            $result = [
                'code' => $e->getCode(),
                'msg'  => $e->getMessage(),
            ];
        }

        return $result;
    }

    /**
     * 生成toker、伪sessionid
     * @param $outsourcing_company
     * @param $params
     * @return array
     */
    public function create_token($outsourcing_company, $params)
    {
        try {
            //生成token
            $di            = $this->getDI();
            $str           = $di['config']['application']['authenticate'];
            $time          = time();
            $company_phone = $outsourcing_company['company_phone'];
            $auth          = sha1(md5($time.$str.$company_phone));
            $session_id    = "{$time}_{$auth}_{$company_phone}";//返回伪session

            //要更新的字段数据
            $update_data = [
                'last_login_time' => gmdate('Y-m-d H:i:s'),
                'device_model'    => $params['device_model'],
                'ip_address'      => $params['ip_address'],
                'lat'             => $params['lat'],
                'lng'             => $params['lng'],
                'version'         => $params['version'],
                'os'              => $params['os'],
                'os_version'      => $params['os_version'],
                'accept_language' => $params['lang'],
                'clientsd'        => $params['clientsd'],
                'clientid'        => $params['clientid'],
                'sessionid'       => $session_id,
            ];

            //执行更新操作
            $db            = $this->getDI()->get('db');
            $update_result = $db->updateAsDict('outsourcing_company', $update_data, 'id='.$outsourcing_company['id']);

            //存储session
            if ($update_result) {
                $cache      = $this->getDI()->get('redisLib');
                $cache_key  = md5($auth);//缓存用户信息 key
                $user       = [
                    'sessionid'     => $session_id,
                    'id'            => $outsourcing_company['id'],
                    'company_name'  => $outsourcing_company['company_name'],
                    'company_phone' => $outsourcing_company['company_phone'],
                    'is_agreement'  => $outsourcing_company['is_agreement'] == OutsourcingCompanyModel::IS_AGREEMENT_1 ? true : false,
                ];
                $cache_time = 24 * 3600 * 30;//30天
                $cache->set($cache_key, json_encode($user), $cache_time);
            }
            $this->getDI()->get('logger')->write_log('outsoucing-company-login-success'.json_encode($params), 'info');
            $result = [
                'code' => ErrCode::SUCCESS,
                'msg'  => 'Success',
                'data' => $user,
            ];
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('outsoucing-company-login-fail'.json_encode($params).$e->getMessage(),
                'error');
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg'  => 'Server Error',
            ];
        }
        return $result;
    }

    /**
     * 登录server接口
     * @param $params
     * @return array
     */
    function logout($params)
    {
        try {
            $di       = $this->getDI();
            $str      = $di['config']['application']['authenticate'];
            $arr      = explode('_', $params['x_osm_session_id']);
            $time     = $arr[0];
            $staff_id = $arr[2];
            $auth     = sha1(md5($time.$str.$staff_id));

            if ($auth == $arr[1]) {
                $cache_key = md5($auth);
                $cache     = $di->get('redisLib');
                $cache->del($cache_key);
            }

            $result = [
                'code' => ErrCode::SUCCESS,
                'msg'  => 'Success',
            ];
            $this->getDI()->get('logger')->write_log('outsoucing-company-logout-success'.json_encode($params), 'info');
        } catch (\Exception $e) {
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg'  => 'Server Error',
            ];
            $this->getDI()->get('logger')->write_log('outsoucing-company-logout-fail'.json_encode($params).$e->getMessage(),
                'error');
        }
        return $result;
    }

    /**
     * 校验密码
     * @param $params
     * @return array
     */
    public function verifyPassword($params, $user_info)
    {
        try {
            //获取外协公司信息
            $outsourcing_company_model = new OutsourcingCompanyModel();
            $outsourcing_company       = $outsourcing_company_model->getOneByPhone($user_info['company_phone']);
            if (empty($outsourcing_company) || $outsourcing_company['deleted'] != 0) {
                $this->getDI()->get('logger')->write_log('outsoucing-company-verify-login-no-exists'.json_encode($params),
                    'info');
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_no_exists'),
                    ErrCode::VALIDATE_ERROR);
            }

            //验证密码
            $hash = new PasswordHash(10, false);
            $bool = $hash->checkPassword($params['password'], $outsourcing_company['encrypted_password']);
            if (!$bool) {
                $this->getDI()->get('logger')->write_log('outsoucing-company-verify-password-wrong'.json_encode($params),
                    'info');
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_wrong_password'),
                    ErrCode::VALIDATE_ERROR);
            }
            $result = [
                'code' => ErrCode::SUCCESS,
                'msg'  => 'Success',
            ];
        } catch (ValidationException $e) {
            $result = [
                'code' => $e->getCode(),
                'msg'  => $e->getMessage(),
            ];
        } catch (\Exception $e) {
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg'  => 'Server Error',
            ];
        }
        return $result;
    }

    /**
     * 重置密码
     * @param $params
     * @return array
     */
    public function resetPassword($params, $user_info)
    {
        try {
            if (!preg_match("/[0-9]{6}/", $params['new_password'])) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_wrong_password2'),
                    ErrCode::VALIDATE_ERROR);
            }
            //校验旧密码和新密码是否一致
            $outsourcing_company = (new OutsourcingCompanyModel())->getOneByPhone($user_info['company_phone']);
            $hash                = new PasswordHash(10, false);
            $bool                = $hash->checkPassword($params['new_password'],
                $outsourcing_company['encrypted_password']);
            if ($bool) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_password_repeat'),
                    ErrCode::VALIDATE_ERROR);
            }

            $db                = $this->getDI()->get('db');
            $new_hash_password = $hash->HashPassword($params['new_password']);
            $update_result     = $db->updateAsDict('outsourcing_company', ['encrypted_password' => $new_hash_password],
                'company_phone='.$user_info['company_phone']);
            if ($update_result) {
                $di       = $this->getDI();
                $str      = $di['config']['application']['authenticate'];
                $arr      = explode('_', $params['x_osm_session_id']);
                $time     = $arr[0];
                $staff_id = $arr[2];
                $auth     = sha1(md5($time.$str.$staff_id));

                if ($auth == $arr[1]) {
                    $cache_key = md5($auth);
                    $cache     = $di->get('redisLib');
                    $cache->del($cache_key);
                }
                $result = [
                    'code' => ErrCode::SUCCESS,
                    'msg'  => 'Success',
                ];
                $this->getDI()->get('logger')->write_log('outsoucing-company-reset-password-success'.json_encode($params),
                    'info');
            } else {
                $result = [
                    'code' => ErrCode::FAIL,
                    'msg'  => 'Fail',
                ];
                $this->getDI()->get('logger')->write_log('outsoucing-company-reset-password-error'.json_encode($params),
                    'error');
            }
        } catch (ValidationException $e) {
            $result = [
                'code' => $e->getCode(),
                'msg'  => $e->getMessage(),
            ];
        } catch (\Exception $e) {
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg'  => 'Server Error',
            ];
            $this->getDI()->get('logger')->write_log('outsoucing-company-reset-password-error'.json_encode($params),
                'error');
        }
        return $result;
    }

    /**
     * 解除密码超限
     * @param $params
     * @return array
     */
    public function unfreezePassword($company_phone)
    {
        $cache     = $this->getDI()->get('redisLib');
        $cache_key = self::PASSWORD_WRONG_KEY_PREFIX.$company_phone;
        if ($cache->del($cache_key)) {
            return [
                'code' => ErrCode::SUCCESS,
                'msg'  => 'Success',
            ];
        }
        return [
            'code' => ErrCode::FAIL,
            'msg'  => 'Fail',
        ];
    }

    /**
     * 校验app版本是否一致，是否强制更新
     * @param $params
     * @return array
     */
    public function verifyAppVersion($params)
    {
        try {
            $result               = [
                'code' => ErrCode::SUCCESS,
                'msg'  => 'Success',
                'data' => [
                    'is_upload'     => 0,
                    'updating_type' => 0,
                ],
            ];
            $osm_app_last_version = (new SettingEnvServer())->getSetVal('osm_app_last_version');
            $osm_app_last_version = !empty($osm_app_last_version) ? json_decode($osm_app_last_version, true) : [];
            if ($params['os'] == 'ios') {
                if ($osm_app_last_version['ios']['version'] !== $params['version']) {
                    $result['data']['is_upload']     = 1;
                    $result['data']['updating_type'] = $osm_app_last_version['ios']['forced_updating'];
                    $result['data']['download_url']  = $osm_app_last_version['ios']['download_url'];
                }
            } else {
                if ($osm_app_last_version['android']['version'] !== $params['version']) {
                    $result['data']['is_upload']     = 1;
                    $result['data']['updating_type'] = $osm_app_last_version['android']['forced_updating'];
                    $result['data']['download_url']  = $osm_app_last_version['android']['download_url'];
                }
            }
        } catch (\Exception $e) {
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg'  => 'Server Error',
                'data' => [],
            ];
            $this->getDI()->get('logger')->write_log('verifyAppVersion-error'.json_encode($params), 'error');
        }
        return $result;
    }

    /**
     * 注销
     * @param $params
     * @param $user_info
     * @return array
     */
    public function cancellation($params, $user_info)
    {
        try {
            //注销
            $db            = $this->getDI()->get('db');
            $update_result = $db->updateAsDict('outsourcing_company', ['deleted' => 1],
                'company_phone='.$user_info['company_phone']);
            if ($update_result) {
                $di       = $this->getDI();
                $str      = $di['config']['application']['authenticate'];
                $arr      = explode('_', $params['x_osm_session_id']);
                $time     = $arr[0];
                $staff_id = $arr[2];
                $auth     = sha1(md5($time.$str.$staff_id));

                if ($auth == $arr[1]) {
                    $cache_key = md5($auth);
                    $cache     = $di->get('redisLib');
                    $cache->del($cache_key);
                }
                $result = [
                    'code' => ErrCode::SUCCESS,
                    'msg'  => 'Success',
                ];
                $this->getDI()->get('logger')->write_log('outsoucing-company-cancellation-success'.json_encode($params),
                    'info');
            } else {
                $result = [
                    'code' => ErrCode::FAIL,
                    'msg'  => 'Fail',
                ];
                $this->getDI()->get('logger')->write_log('outsoucing-company-cancellation-error'.json_encode($params),
                    'error');
            }
        } catch (\Exception $e) {
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg'  => 'Server Error',
            ];
            $this->getDI()->get('logger')->write_log('outsoucing-company-cancellation-error'.json_encode($params),
                'error');
        }
        return $result;
    }

    /**
     * 添加device token
     * @param $params
     * @param $user_info
     * @return array
     * @throws ValidationException
     */
    public function addDeviceToken($params, $user_info)
    {
        $deviceWhere['device_token']     = $params['device_token'];
        $deviceWhere['company_id']       = $user_info['id'];
        $deviceInfo                      = $this->getCompanyDeviceToken($deviceWhere);
        $deviceType = $params['device_type'] ?? 0;
        if(!empty($deviceType) && $params['os'] == 'android' && !in_array($deviceType, [OutsourcingCompanyDeviceTokenModel::DEVICE_TYPE_GOOGLE, OutsourcingCompanyDeviceTokenModel::DEVICE_TYPE_HUAWEI])) {
            throw new ValidationException('device_type error');
        }

        $db = $this->getDI()->get('db');
        try {
            $data['accept_language'] = $this->lang;
            if (!empty($deviceInfo)) {
                $db->updateAsDict('outsourcing_company_device_token', $data, 'id = '.$deviceInfo['id']);
            } else {
                $data['company_id']       = $user_info['id'];
                $data['os']               = $params['os'];
                $data['device_token']     = $params['device_token'];
                $data['device_type']      = $deviceType;
                $db->insertAsDict('outsourcing_company_device_token', $data);
            }
            $result = [
                'code' => ErrCode::SUCCESS,
                'msg'  => 'Success',
            ];
        } catch (\Exception $e) {
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg'  => 'Server Error',
            ];
            $this->getDI()->get('logger')->write_log('addOsmDeviceToken-error'.json_encode($params), 'error');
        }
        return $result;
    }

    /**
     * 获取 deviceToken
     * @param $params
     * @return array
     */
    public function getCompanyDeviceToken($params)
    {
        $deviceToken = OutsourcingCompanyDeviceTokenModel::findFirst([
            'conditions' => 'company_id = :company_id: and device_token = :device_token:',
            'bind'       => ['device_token' => $params['device_token'], 'company_id' => $params['company_id']],
            'columns'    => "id",
        ]);

        return !empty($deviceToken) ? $deviceToken->toArray() : [];
    }

}