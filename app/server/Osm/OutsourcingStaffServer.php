<?php


namespace FlashExpress\bi\App\Server\Osm;


use App\Country\Tools;
use FlashExpress\bi\App\Enums\OsmEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AttendanceHikStoreSettingModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrOutsourcingOrderModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyModel;
use FlashExpress\bi\App\Models\backyard\StaffHikvisionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Server\AiServer;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\OsStaffServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class OutsourcingStaffServer extends BaseServer
{
    public function __construct($lang = '')
    {
        parent::__construct($lang);
    }
    
    /**
     * 获取枚举数据
     * @param $user_info
     * @return array
     */
    public function getSysInfo($user_info){
            //性别
            foreach (OsmEnums::$sex as $key=>$val){
                $data['sex'][] = [
                  'code' => $key,
                  'name' => $this->getTranslation()->_($val),
                ];
            }
            //国籍
            $server = Tools::reBuildCountryInstance($this,[$this->lang]);
            foreach ($server->getNationalityEnums() as $key => $val) {
                $data['nationality_list'][] = [
                    'code' => $key,
                    'name' => $this->getTranslation()->_($val),
                ];
            }
            $data['job_list'] = $this->getJobTitleList();
            //网点
            $storeSetting = AttendanceHikStoreSettingModel::find("is_deleted = 0")->toArray();
            $storeIds = array_column($storeSetting, 'flash_store_id');
            $store_list = (new SysStoreModel())->getStoreListById($storeIds);
            foreach ($store_list as $key => $val) {
                $data['store_list'][] = [
                    'code' => $val['id'],
                    'name' => $val['name'],
                ];
            }
            $result = [
                'code' => ErrCode::SUCCESS,
                'msg' => 'Success',
                'data' => $data
            ];
        return $result;
    }

    public function getJobTitleList()
    {
        //职位
        $job_list = [];
        foreach (OsmEnums::$job_title as $key => $val) {
            $job_list[] = [
                'code' => $key,
                'name' => $this->getTranslation()->_($val),
            ];
        }
        return $job_list;
    }
    /**
     * 添加外协员工
     * @param $params
     * @param $user_info
     * @return array
     */
    public function addOutsourcingStaff($params, $user_info){
        $db = $this->getDI()->get('db');
        $staff_info_id = 0;
        try {
            //1.1根据身份证号码 + formal=0 + staff_type=4 获取外协公司创建的外协员工
            $hr_staff_model = new HrStaffInfoModel();
            $staff_info = $hr_staff_model->getOneStaffByOutsourcing($params, HrStaffInfoModel::STAFF_TYPE_4);
            //1.2如果获取外协公司创建的外协员工为空，则根据 根据身份证号码 + formal=0 获取最新创建的外协员工
            if (!$staff_info) {
                $staff_info = $hr_staff_model->getLatestStaffByOutsourcing($params);
            }

            //根据网点找部门
            $store_department_relation = (new SettingEnvServer())->getSetVal('hub_store_department_id_relation');
            if (!empty($store_department_relation)) {
                $store_department_relation = json_decode($store_department_relation, true);
            }
            $node_department_id = $store_department_relation[$params['sys_store_id']] ?? '';
            if (empty($store_department_relation) || empty($node_department_id)) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_no_store_department'));
            }

            if(isCountry('TH')) {
                $hikPdcStore = (new OsStaffServer($this->lang, $this->timeZone))->getPdcHikStore();
                $hikPdcStoreIds = array_column($hikPdcStore, 'id');
                if(in_array($params['sys_store_id'], $hikPdcStoreIds) && $params['job_id'] == enums::$job_title['security_outsource']) {
                    throw new ValidationException($this->getTranslation()->_('pdc_store_job_tips'));
                }
            }

            //截取图片地址
            $identity_file_a_url = parse_url($params['identity_file_a']);
            $params['identity_file_a'] = ltrim($identity_file_a_url['path'], '/');
            //检查图片
            $this->checkFaceImageQuality($params['face_img_path'], '');

            if (isCountry(['TH', 'MY'])){
                // 人脸查重
                $this->checkFaceImageExists($params['face_img_path'], '');
            }

            $staff_info_id = $staff_info ? $staff_info['staff_info_id'] : 0;
            $rpc_data = [];
            //2.1如果员工信息不存在，则调用接口创建员工
            if (!$staff_info) {
                //2.1.1调用hris-rpc创建员工信息
                $rpc_data = [
                    'name'               => $params['name'],
                    'sex'                => $params['sex'],
                    'identity'           => $params['identity'],
                    'mobile'             => $user_info['company_phone'],
                    'job_title'          => $params['job_id'],
                    'sys_store_id'       => $params['sys_store_id'],
                    'node_department_id' => $node_department_id,
                    'company_name_ef'    => $user_info['company_name'],
                    'state'              => HrStaffInfoModel::STATE_1,
                    'staff_type'         => HrStaffInfoModel::STAFF_TYPE_4,
                    'identity_front_key' => $params['identity_file_a'],
                    'nationality'        => $params['nationality'],
                    'position_category'  => [],
                    'hire_date'          => date('Y-m-d') . ' 00:00:00',
                ];

                //hub外协公司id
                if(isCountry('TH')) {
                    $rpc_data['company_item_id'] = $user_info['id'];
                }

                $hr_rpc = new ApiClient('hr_rpc', '', 'create_hub_os_staff', $this->lang);
                $hr_rpc->setParams($rpc_data);
                $ret = $hr_rpc->execute();

                if($ret['result']['code']==1 && $ret['code']==1){
                    $staff_info_id = $ret['result']['data']['staff_info_id'];
                }
            }
    
            //获取员工海康附属信息
            $staff_hikvision_Model = new StaffHikvisionModel();
            $staff_hikvision = $staff_hikvision_Model->getOneByStaff($staff_info_id);
            
            //2.2如果员工信息存在，则调用rpc接口更新员工信息，并调海康接口获取信息，再调海康更新信息
            if ($staff_info) {
                //2.2.1如果该员工在其他外协公司存在且尚未离职，则不能添加
                if(!empty($staff_hikvision) && $staff_hikvision['outsourcing_company_id']!=$user_info['id'] && $staff_hikvision['deleted']==StaffHikvisionModel::DELETED_NO ){
                    throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_leave'));
                }
                //2.2.2如果该员工在本公司已添加且未删除
                if(!empty($staff_hikvision) && $staff_hikvision['outsourcing_company_id']==$user_info['id'] && $staff_hikvision['deleted']==StaffHikvisionModel::DELETED_NO ){
                    throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_exists'));
                }
                
                //2.2.3调用hris-rpc更新员工信息
                $rpc_data = [
                    'staff_info_id' => $staff_info['staff_info_id'],
                    'name' => $params['name'],
                    'sex' => $params['sex'],
                    'identity' => $params['identity'],
                    'mobile' => $user_info['company_phone'],
                    'job_title' => $params['job_id'],
                    'sys_store_id' => $params['sys_store_id'],
                    'node_department_id' => isset($store_department_relation[$params['sys_store_id']]) ? $store_department_relation[$params['sys_store_id']] : '',
                    'company_name_ef' => $user_info['company_name'],
                    'state' => HrStaffInfoModel::STATE_1,
                    'staff_type' => HrStaffInfoModel::STAFF_TYPE_4,
                    'identity_front_key' => $params['identity_file_a'],
                    'nationality' => $params['nationality'],
                    'position_category' => [],
                    'hire_date' => date('Y-m-d') . ' 00:00:00',
                ];

                //hub外协公司id
                if(isCountry('TH')) {
                    $rpc_data['company_item_id'] = $user_info['id'];
                }

                $hr_rpc = new ApiClient('hr_rpc', '', 'create_hub_os_staff', $this->lang);
                $hr_rpc->setParams($rpc_data);
                $ret = $hr_rpc->execute();
            }
    
            if($ret['result']['code']==1 && $ret['code']==1) {
                $result = [
                    'code' => ErrCode::SUCCESS,
                    'msg' => 'Success',
                ];
                $db->begin();

                $ai_emb = '';
                // 向ai 同步人脸照片的数据
                if (isCountry(['TH', 'MY'])){
                    $add_face_img_ret = $this->faceImageUpdate($params['face_img_path'], (int) $staff_info_id);
                    if (is_bool($add_face_img_ret) && !$add_face_img_ret) {
                        throw new \Exception('please try again');
                    }
                    $ai_emb = $add_face_img_ret;
                }

                $update_staff_hikvision = $staff_hikvision;
                $update_staff_hikvision['staff_info_id'] = $staff_info_id;
                $update_staff_hikvision['outsourcing_company_id'] = $user_info['id'];
                $update_staff_hikvision['face_img_path'] = $params['face_img_path'];
                $update_staff_hikvision['deleted'] = StaffHikvisionModel::DELETED_NO;
                $update_staff_hikvision['is_sync'] = StaffHikvisionModel::SYNC_NO;
                $update_staff_hikvision['editor_id'] = $user_info['id'];
                $update_staff_hikvision['editor_type'] = StaffHikvisionModel::EDITOR_TYPE_1;
                $update_staff_hikvision['ai_emb']   = $ai_emb;
                unset($update_staff_hikvision['updated_at'], $update_staff_hikvision['created_at']);
                //3.1写入或更新staff_hikvision表
                if(!empty($staff_hikvision)){
                    $db->updateAsDict('staff_hikvision', $update_staff_hikvision, 'staff_info_id='.$staff_info_id);
                }else{
                    $db->insertAsDict('staff_hikvision', $update_staff_hikvision);
                }
                //3.2写入或更新staff_hikvision_log表
                if(!empty($staff_hikvision)){
                    unset($update_staff_hikvision['id']);
                }
                $db->insertAsDict('staff_hikvision_log', $update_staff_hikvision);

                $db->commit();
            }elseif($ret['result']['code']!=1 && $ret['code']==1) {
                $result = [
                    'code' => ErrCode::VALIDATE_ERROR,
                    'msg' => $ret['result']['msg'],
                ];
            } else {
                $result = [
                    'code' => ErrCode::FAIL,
                    'msg' => 'Fail',
                ];
            }
        } catch (ValidationException $e){
            $this->getDI()->get('logger')->write_log('add-outsourcing-staff-Validation-fail' . json_encode(array_merge($params,$user_info)), 'info');
            $result = [
                'code' => ErrCode::VALIDATE_ERROR,
                'msg' => $e->getMessage(),
            ];
        } catch (\Exception $e){
            $db->rollback();
            $this->getDI()->get('logger')->write_log(['add-outsourcing-staff-fail'=> array_merge($params,$user_info),'error'=>$e->getMessage()], 'error');
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg' => 'server error',
            ];
        }
        //同步人员hik 组织信息， hcm 异步处理
        if($result['code'] == ErrCode::SUCCESS) {
            $sendData['staff_info_id'] = $staff_info_id;
            $sendData['sys_store_id'] = $params['sys_store_id'];
            $this->sendStaffInfo($sendData);
        }
        return $result;
    }
    
    /**
     * 编辑员工信息
     * @param $params
     * @param $user_info
     * @return array
     */
    public function updateOutsourcingStaff($params, $user_info){

        $db = $this->getDI()->get('db');
        $is_edit_store = false;
        try {
            $hr_staff_model = new HrStaffInfoModel();
            $staff_info = $hr_staff_model->getOneByStaffId($params['staff_info_id']);
            //员工不存在
            if(empty($staff_info)){
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_exists'));
            }
            //员工已不是外协
            if ($staff_info['formal'] != HrStaffInfoModel::FORMAL_0) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_outsourcing'));
            }
            //员工已离职
            if($staff_info['state']!=HrStaffInfoModel::STATE_1){
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_leave'));
            }
            if($staff_info['sys_store_id'] != $params['sys_store_id']) {
                $is_edit_store = true;
            }
    
            $staff_hikvision_Model = new StaffHikvisionModel();
            $staff_hikvision = $staff_hikvision_Model->getOneByStaff($params['staff_info_id']);
            //海康附属信息不存在或员工不属于当前公司
            if( empty($staff_hikvision['outsourcing_company_id'])  || $staff_hikvision['outsourcing_company_id']!=$user_info['id'] ){
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_company'));
            }
            if($staff_hikvision['deleted']==StaffHikvisionModel::DELETED_YES){
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_leave'));
            }
            
            //根据网点找部门
            $store_department_relation = (new SettingEnvServer())->getSetVal('hub_store_department_id_relation');
            if (!empty($store_department_relation)) {
                $store_department_relation = json_decode($store_department_relation, true);
            }
            if(empty($store_department_relation) || empty($store_department_relation[$params['sys_store_id']])){
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_no_store_department'));
            }

            if(isCountry('TH')) {
                $hikPdcStore = (new OsStaffServer($this->lang, $this->timeZone))->getPdcHikStore();
                $hikPdcStoreIds = array_column($hikPdcStore, 'id');
                if(in_array($params['sys_store_id'], $hikPdcStoreIds) && $params['job_id'] == enums::$job_title['security_outsource']) {
                    throw new ValidationException($this->getTranslation()->_('pdc_store_job_tips'));
                }
            }

            // 当修改了人脸照片时 再触发 质量检测、和人脸查重的接口
            $is_change_face_img = false;
            if (isCountry(['TH', 'MY'])){
                $is_change_face_img = true;
                //检查图片 （老照片 不符合 新的 质量规则， 变更照片再检测）
                if($staff_hikvision['face_img_path'] != $params['face_img_path']) {
                    $this->checkFaceImageQuality($params['face_img_path'], $params['staff_info_id']);
                }

                // 人脸查重
                $this->checkFaceImageExists($params['face_img_path'], (string) $params['staff_info_id']);
            }
            //截取图片地址
            $identity_file_a_url = parse_url($params['identity_file_a']);
            $params['identity_file_a'] = ltrim($identity_file_a_url['path'], '/');
            
            //调用rpc接口更新员工信息
            //2.2.3调用hris-rpc更新员工信息
            $rpc_data = [
                'staff_info_id' => $staff_info['staff_info_id'],
                'name' => $params['name'],
                'sex' => $params['sex'],
                'identity' => $params['identity'],
                'mobile' => $user_info['company_phone'],
                'job_title' => $params['job_id'],
                'sys_store_id' => $params['sys_store_id'],
                'node_department_id' => isset($store_department_relation[$params['sys_store_id']]) ? $store_department_relation[$params['sys_store_id']] : '',
                'company_name_ef' => $user_info['company_name'],
                'state' => HrStaffInfoModel::STATE_1,
                'staff_type' => HrStaffInfoModel::STAFF_TYPE_4,
                'identity_front_key' => $params['identity_file_a'],
                'nationality' => $params['nationality'],
                'position_category' => [],
                'hire_date' => date('Y-m-d') . ' 00:00:00',
            ];

            //hub外协公司id
            if(isCountry('TH')) {
                $rpc_data['company_item_id'] = $user_info['id'];
            }

            $hr_rpc = new ApiClient('hr_rpc', '', 'create_hub_os_staff', $this->lang);
            $hr_rpc->setParams($rpc_data);
            $ret = $hr_rpc->execute();
            
            if($ret['result']['code']==1 && $ret['code']==1){
                $db->begin();
                $ai_emb = '';
                if ($is_change_face_img) {
                    $face_image_update_ret = $this->faceImageUpdate($params['face_img_path'],
                        (int) $params['staff_info_id']);
                    if (is_bool($face_image_update_ret) && !$face_image_update_ret) {
                        throw new \Exception('please try again');
                    }
                    $ai_emb = $face_image_update_ret;
                }

                //更新员工海康附属信息
                $update_staff_hikvision['face_img_path'] = $params['face_img_path'];
                $update_staff_hikvision['is_sync'] = StaffHikvisionModel::SYNC_NO;
                $update_staff_hikvision['editor_id'] = $user_info['id'];
                $update_staff_hikvision['editor_type'] = StaffHikvisionModel::EDITOR_TYPE_1;
                $update_staff_hikvision['ai_emb'] = $ai_emb;
                if($db->updateAsDict('staff_hikvision', $update_staff_hikvision, 'staff_info_id='.$params['staff_info_id'])) {
                    //写入员工海康附属log表
                    $staff_hik_log = $staff_hikvision;
                    unset($staff_hik_log['id'], $staff_hik_log['created_at'], $staff_hik_log['updated_at']);
                    $staff_hik_log['face_img_path'] = $params['face_img_path'];
                    $staff_hik_log['editor_id'] = $user_info['id'];
                    $staff_hik_log['editor_type'] = StaffHikvisionModel::EDITOR_TYPE_1;
                    $staff_hik_log['is_sync'] = StaffHikvisionModel::SYNC_NO;
                    $staff_hik_log['ai_emb']  = $ai_emb;
                    $db->insertAsDict('staff_hikvision_log', $staff_hik_log);

                    $result = [
                        'code' => ErrCode::SUCCESS,
                        'msg' => 'Success',
                    ];
                }else{
                    $db->rollback();
                    $result = [
                        'code' => ErrCode::FAIL,
                        'msg' => 'Fail',
                    ];
                }
                $db->commit();
            } elseif($ret['result']['code']!=1 && $ret['code']==1) {
                $result = [
                    'code' => ErrCode::VALIDATE_ERROR,
                    'msg' => $ret['result']['msg'],
                ];
            }else {
                $result = [
                    'code' => ErrCode::FAIL,
                    'msg' => 'Fail',
                ];
            }
        } catch (ValidationException $e){
            $this->getDI()->get('logger')->write_log('update-outsourcing-staff-Validation-fail' . json_encode(array_merge($params,$user_info)), 'info');
            $result = [
                'code' => ErrCode::VALIDATE_ERROR,
                'msg' => $e->getMessage(),
            ];
        } catch (\Exception $e){
            $db->rollback();
            $this->getDI()->get('logger')->write_log('update-outsourcing-staff-fail' . json_encode(array_merge($params,$user_info)), 'error');
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg' => 'server error',
            ];
        }

        //变更组织，走异步直接同步hik
        if($result['code'] == ErrCode::SUCCESS && $is_edit_store) {
            $sendData['staff_info_id'] = $staff_info['staff_info_id'];
            $sendData['sys_store_id'] = $params['sys_store_id'];
            $this->sendStaffInfo($sendData, 'edit');
        }
        return $result;
    }


    /**
     * 同步hik人员信息， hcm 异步处理
     * @param $params
     * @param string $operation
     * @return bool
     */
    public function sendStaffInfo($params, $operation = 'add')
    {
        //同步hik人员信息， hcm 异步处理
        $ac = new ApiClient('hcm_rpc', '', 'hub_staff_sync_to_hik' ,$this->lang);
        $data = [
            "staff_info_id" => $params['staff_info_id'],
            "sys_store_id"  => $params['sys_store_id'],
            "operation"     => $operation,
        ];
        $ac->setParams($data);
        $ac_result = $ac->execute();
        if($ac_result["result"]['code'] != 1) {
            $this->logger->write_log('hub_staff_sync_to_hik RPC fail :' . json_encode($data, JSON_UNESCAPED_UNICODE), 'error');
            return false;
        }

        return true;
    }
    
    /**
     * 删除员工
     * @param $params
     * @param $user_info
     * @return array
     */
    public function delOutsourcingStaff($params, $user_info){
        $db = $this->getDI()->get('db');
        try {
            $hr_staff_model = new HrStaffInfoModel();
            $staff_info = $hr_staff_model->getOneByStaffId($params['staff_info_id']);
            //1.1员工不存在
            if (empty($staff_info)) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_exists'));
            }
            //1.2员工已不是外协
            if ($staff_info['formal'] != HrStaffInfoModel::FORMAL_0) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_outsourcing'));
            }
            //1.3员工已离职
            if ($staff_info['state'] != HrStaffInfoModel::STATE_1) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_leave'));
            }
    
            $staff_hikvision_Model = new StaffHikvisionModel();
            $staff_hikvision = $staff_hikvision_Model->getOneByStaff($params['staff_info_id']);
            //1.4海康附属信息不存在或员工不属于当前公司
            if (empty($staff_hikvision) || empty($staff_hikvision['outsourcing_company_id'])) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_company'));
            }
            //1.5员工已离职
            if ($staff_hikvision['deleted'] == StaffHikvisionModel::DELETED_YES) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_leave'));
            }
    
            //调用rpc接口更新员工信息
            $rpc_data = [
                'staff_info_id' => $params['staff_info_id'],
                'state' => HrStaffInfoModel::STATE_2,
                'leave_date' => date('Y-m-d'),
            ];
            $hr_rpc = new ApiClient('hr_rpc', '', 'update_hub_os_staff_state', $this->lang);
            $hr_rpc->setParams($rpc_data);
            $ret = $hr_rpc->execute();
            if($ret['result']['code']==1 && $ret['code']==1) {
                $db->begin();

                if (isCountry(['TH', 'MY'])) {
                    $ret = $this->faceImageDelete((string) $params['staff_info_id']);
                    if (!$ret) {
                        throw new \Exception('please try again');
                    }
                }

                $update_staff_hikvision['staff_info_id'] = $params['staff_info_id'];
                $update_staff_hikvision['outsourcing_company_id'] = $user_info['id'];
                $update_staff_hikvision['hikvision_pic_uri'] = '';
                $update_staff_hikvision['face_img_path'] = $staff_hikvision['face_img_path'];
                $update_staff_hikvision['hikvision_org_index_code'] = '';
                $update_staff_hikvision['hikvision_org_name'] = '';
                $update_staff_hikvision['is_sync'] = StaffHikvisionModel::SYNC_NO;
                $update_staff_hikvision['deleted'] = StaffHikvisionModel::DELETED_YES;
                $update_staff_hikvision['editor_id'] = $user_info['id'];
                $update_staff_hikvision['editor_type'] = StaffHikvisionModel::EDITOR_TYPE_1;
                $update_staff_hikvision['ai_emb']  = '';
                //更新员工海康附属信息
                if($db->updateAsDict('staff_hikvision', $update_staff_hikvision, 'staff_info_id='.$params['staff_info_id'])){
                    //写入员工海康附属日志表
                    $db->insertAsDict('staff_hikvision_log', $update_staff_hikvision);
                    $result = [
                        'code' => ErrCode::SUCCESS,
                        'msg' => 'Success',
                    ];
                }else{
                    $db->rollback();
                    $result = [
                        'code' => ErrCode::FAIL,
                        'msg' => 'Fail',
                    ];
                }
                $db->commit();
            }elseif($ret['result']['code']!=1 && $ret['code']==1){
                $result = [
                    'code' => ErrCode::VALIDATE_ERROR,
                    'msg' => $ret['result']['msg'],
                ];
            } else{
                $result = [
                    'code' => ErrCode::FAIL,
                    'msg' => 'Fail',
                ];
            }
        } catch (ValidationException $e){
            $this->getDI()->get('logger')->write_log('update-outsourcing-staff-Validation-fail' . json_encode(array_merge($params,$user_info)), 'info');
            $result = [
                'code' => ErrCode::VALIDATE_ERROR,
                'msg' => $e->getMessage(),
            ];
        } catch (\Exception $e){
            $db->rollback();
            $this->getDI()->get('logger')->write_log('update-outsourcing-staff-fail' . json_encode(array_merge($params,$user_info)), 'error');
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg' => 'server error',
            ];
        }
        return $result;
    }

    public function getNationalityEnums()
    {
        return OsmEnums::$nationality_list;
    }
    
    /**
     * 获取单个外协员工信息
     * @param $params
     * @param $user_info
     * @return array
     */
    public function getOneOutsourcingStaff($params, $user_info){
        try {
            $hr_staff_model = new HrStaffInfoModel();
            $staff_info = $hr_staff_model->getOneByStaffId($params['staff_info_id']);
            //1.1员工不存在
            if (empty($staff_info)) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_exists'));
            }
            //1.2员工已不是外协
            if ($staff_info['formal'] != HrStaffInfoModel::FORMAL_0) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_outsourcing'));
            }
            //1.3员工已离职
            if ($staff_info['state'] != HrStaffInfoModel::STATE_1) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_leave'));
            }
    
            $staff_hikvision_Model = new StaffHikvisionModel();
            $staff_hikvision = $staff_hikvision_Model->getOneByStaff($params['staff_info_id']);
            //1.4海康附属信息不存在或员工不属于当前公司
            if (empty($staff_hikvision) || empty($staff_hikvision['outsourcing_company_id'])) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_company'));
            }
            //1.5员工已离职
            if ($staff_hikvision['deleted'] == StaffHikvisionModel::DELETED_YES) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_leave'));
            }
            
            //国籍
            $item_model = new HrStaffItemsModel();
            $nationality_info = $item_model->getOneByStaffId($staff_info['staff_info_id'], HrStaffItemsModel::ITEM_NATIONALITY);
            
            //网点信息
            $store_model = new SysStoreModel();
            $store = $store_model->getOneStoreById($staff_info['sys_store_id']);
            
            //职位
            $job_model = new HrJobTitleModel();
            $job = $job_model->getOneById($staff_info['job_title']);
            
            //身份证照片
            $identity_front = $item_model->getOneByStaffId($staff_info['staff_info_id'], HrStaffItemsModel::ITEM_IDENTITY_FRONT_KEY);
            $server = Tools::reBuildCountryInstance($this,[$this->lang]);
            
            $data = [
                'staff_info_id' => $staff_info['staff_info_id'],
                'name' => $staff_info['name'],
                'sex' => (int)$staff_info['sex'],
                'sex_name' => !empty($staff_info['sex']) ? $this->getTranslation()->_(OsmEnums::$sex[$staff_info['sex']]) : '',
                'nationality' => !empty($nationality_info) ? (int)$nationality_info['value'] : 0,
                'nationality_name' => !empty($nationality_info) ? $this->getTranslation()->_($server->getNationalityEnums()[$nationality_info['value']]): '',
                'identity' => $staff_info['identity'],
                'sys_store_id' => $staff_info['sys_store_id'],
                'sys_store_name' => !empty($store) ? $store['name'] : '',
                'job_id' => (int)$staff_info['job_title'],
                'job_name' => !empty($job) ? $job['job_name'] : '',
                'identity_file_a' => !empty($identity_front) ? env('img_prefix', '') . $identity_front['value'] : '',
                'face_img_path' => $staff_hikvision['face_img_path'],
            ];
            $result = [
                'code' => ErrCode::SUCCESS,
                'msg' => 'Success',
                'data' => $data,
            ];
        } catch (ValidationException $e){
            $this->getDI()->get('logger')->write_log('update-outsourcing-staff-Validation-fail' . json_encode(array_merge($params,$user_info)), 'info');
            $result = [
                'code' => ErrCode::VALIDATE_ERROR,
                'msg' => $e->getMessage(),
                'data' => [],
            ];
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('update-outsourcing-staff-fail' . json_encode(array_merge($params,$user_info)), 'error');
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg' => 'server error',
                'data' => [],
            ];
        }
        return $result;
    }
    
    /**
     * 获取列表
     * @param $params
     * @param $user_info
     * @return array
     */
    public function getOutsourcingStaffList($params, $user_info){
        $data['count'] = 0;
        $data['list'] = [];
        
        //获取总数
        $count = $this->getOutsourcingStaffListCount($params, $user_info);
        if($count<=0){
            return [
                'code' => ErrCode::SUCCESS,
                'msg' => 'Success',
                'data' => $data,
            ];
        }
        
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['hsi.staff_info_id, hsi.name, hsi.sex, hsi.job_title']);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(StaffHikvisionModel::class, 'hsi.staff_info_id = ocsh.staff_info_id', 'ocsh');
        $builder = $this->createListWhere($builder, $params, $user_info);
        $builder->orderBy('ocsh.created_at DESC');
        $list = $builder->getQuery()->execute()->toArray();
        //echo $this->di->get('profiler')->getLastProfile()->getSQLStatement();
        if(!empty($list)){
            //职位
            $job_ids = array_values(array_unique(array_column($list,'job_title')));
            $job_model = new HrJobTitleModel();
            $job_list = $job_model->getListByIds($job_ids);
            $job_list = array_column($job_list, NULL, 'id');

            //国籍
            $staff_ids = array_values(array_unique(array_column($list,'staff_info_id')));
            $item_model = new HrStaffItemsModel();
            $nationality_list = $item_model->getListByStaffId($staff_ids, HrStaffItemsModel::ITEM_NATIONALITY);
            $nationality_list = array_column($nationality_list, NULL, 'staff_info_id');
            //组装数据
            foreach ($list as $key=>$value){
                $list[$key]['name'] = $value['name'] . '(' . $value['staff_info_id'] . ')';
                $list[$key]['sex'] = intval($value['sex']);
                $list[$key]['sex_name'] = !empty($value['sex']) ? $this->getTranslation()->_(OsmEnums::$sex[$value['sex']]) : '';
                $list[$key]['nationality'] = isset($nationality_list[$value['staff_info_id']]) ? (int)$nationality_list[$value['staff_info_id']]['value'] : 0;
                $list[$key]['nationality_name'] = isset($nationality_list[$value['staff_info_id']]) ? $this->getTranslation()->_('nationality_'.$nationality_list[$value['staff_info_id']]['value']) : '';
                $list[$key]['job_id'] = intval($value['job_title']);
                $list[$key]['job_name'] = isset($job_list[$value['job_title']]) ? $job_list[$value['job_title']]['job_name'] : '';
                unset($list[$key]['job_title']);
            }
        }
        $data['count'] = $count;
        $data['list'] = $list;
        $result = [
            'code' => ErrCode::SUCCESS,
            'msg' => 'Success',
            'data' => $data,
        ];
        
        return $result;
    }
    
    /**
     * 获取列表总数
     * @param $params
     * @param $user_info
     * @return int
     */
    public function getOutsourcingStaffListCount($params, $user_info){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['COUNT(1) as total_count']);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(StaffHikvisionModel::class, 'hsi.staff_info_id = ocsh.staff_info_id', 'ocsh');
        $builder = $this->createListWhere($builder, $params, $user_info);
        $count = $builder->getQuery()->getSingleResult()->total_count;
        return $count ? intval($count) : 0;
    }
    //生成where条件
    private function createListWhere($builder, $params, $user_info){
        $builder->Where('hsi.formal=:formal: AND hsi.state=:state: AND staff_type=:staff_type:', ['formal'=>HrStaffInfoModel::FORMAL_0, 'state'=>HrStaffInfoModel::STATE_1, 'staff_type'=>HrStaffInfoModel::STAFF_TYPE_4]);
        $builder->andWhere('ocsh.deleted=0 AND ocsh.outsourcing_company_id='.$user_info['id']);
        $builder->andWhere('ocsh.formal = :hik_formal:', ['hik_formal' => StaffHikvisionModel::FORMAL_0]);
        if(!empty($params['name'])){
            $builder->andWhere('( hsi.name LIKE :name: OR hsi.staff_info_id LIKE :name: )', ['name' => '%' . $params['name'].'%']);
        }
        return $builder;
    }

    /**
     * 获取登录人 所属外协公司信息
     * @param $user_info
     * @return array
     */
    public function getLoginCompanyInfo($user_info)
    {
        try {
            $companyInfo = OutsourcingCompanyModel::getOneById($user_info['id']);
            if(empty($companyInfo)) {
                throw new ValidationException('no data');

            }
            $data['company_full_name'] = $companyInfo['company_full_name'] ?? '';
            $data['company_tel'] = $companyInfo['company_phone'] ?? '';
            $data['company_email'] = $companyInfo['company_mail'] ?? '';
            $result = [
                'code' => ErrCode::SUCCESS,
                'msg' => 'Success',
                'data' => $data,
            ];
        } catch (ValidationException $e){
            $this->getDI()->get('logger')->write_log('update-outsourcing-staff-Validation-fail' . json_encode($user_info), 'info');
            $result = [
                'code' => ErrCode::VALIDATE_ERROR,
                'msg' => $e->getMessage(),
                'data' => [],
            ];
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('update-outsourcing-staff-fail' . json_encode($user_info), 'error');
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg' => 'server error',
                'data' => [],
            ];
        }
        return $result;
    }

    /**
     * 提交是否同意隐私协议
     * @param $params
     * @param $user_info
     * @return array
     */
    public function submitAgreement($params, $user_info)
    {
        $db = $this->getDI()->get('db');
        $update_result = $db->updateAsDict('outsourcing_company', ['is_agreement' => $params['is_agreement']], 'id=' . $user_info['id']);
        if($update_result) {
            return [
                'code' => ErrCode::SUCCESS,
                'msg' => 'Success',
                'data' => '',
            ];
        }
        return [
            'code' => ErrCode::SYSTEM_ERROR,
            'msg' => 'Fail',
            'data' => '',
        ];
    }

    /**
     * 获取未读数量
     * @param $user_info
     * @return array
     * 目前没有地方调用
     */
    public function geUnReadCount($user_info)
    {
        $total_nums   = [
            'un_read_number'         => 0,
            'un_read_outsourcing_ot' => 0,
        ];
        $pendingCount = $this->getOutSourcingOrderCount($user_info['id']);

        $count_columns          = ['COUNT(*) AS count'];
        $params                 = [
            'osm_state' => HubOutsourcingOvertimeModel::OSM_STATE_PENDING,
            'company_id' => $user_info['id']
        ];

        $result_count           = (new OutsourcingOTServer($this->lang,$this->timeZone))->getOutsourcingOTQuery($params, $count_columns,
            true);
        $un_read_outsourcing_ot = (int)$result_count['count'];

        $total_nums['un_read_number']         = $pendingCount;
        $total_nums['un_read_outsourcing_ot'] = $un_read_outsourcing_ot;
        return $total_nums;
    }

    /**
     * 获取外协加班未读数量
     * @param $userId
     * @return int
     */
    public function getUnReadOutsourcingOt($userId)
    {
        $count_columns = ['COUNT(*) AS count'];
        $params        = [
            'osm_state'  => HubOutsourcingOvertimeModel::OSM_STATE_PENDING,
            'company_id' => $userId,
        ];

        $result_count = (new OutsourcingOTServer($this->lang, $this->timeZone))->getOutsourcingOTQuery($params,
            $count_columns,
            true);
        return (int)$result_count['count'];
    }

    /**
     * 获取待生效的订单数
     * @param $companyId
     * @return mixed
     */
    public function getOutSourcingOrderCount($companyId)
    {
        return HrOutsourcingOrderModel::count([
            'conditions' => 'out_company_id = :company_id: and status = :status:',
            'bind'       => ['company_id' => $companyId, 'status' => HrOutsourcingOrderModel::STATUS_PENDING],
        ]);

    }

    /**
     * @param $img
     * @param $staffId
     * @param $appVersion
     * @return void
     * @throws ValidationException
     */
    public function checkFaceImageQuality($img,$staffId)
    {
        $t = $this->getTranslation();
        $imgArrayUrl = explode('?', $img);//地址有参数
        $imgArray = explode('.', $imgArrayUrl[0]);
        $file_type = end($imgArray);
        if(!in_array(strtolower($file_type), ['jpg', 'jpeg', 'png'])) {
            throw new ValidationException($t->_('os_staff_face_check_file_type_error'));
        }

        $params = ['url'=> $img,'face_attributes'=> 'quality,mask','max_face_num'=>2, 'min_pupillary_distance' => OsmEnums::FACE_IMG_PUPILLARY_DISTANCE];
        $params = http_build_query($params);
        $result = AiServer::getInstance()->setConfig(enums::IDENTIFY_ANALYZE_FACE_QUALITY)->send($params, $staffId);
        $this->logger->write_log(['checkFaceImageQualityResult' => $result], 'info');
        //无返回结果
        if (empty($result)){
            $this->logger->write_log("quality check error:" . json_encode($result), "info");
            throw new ValidationException($t->_('os_staff_face_check_msg_1'));//接口调用失败
        }
        if(isset($result['error'])) {
            $this->logger->write_log("quality check error:" . $result['error']['message'], "info");
            throw new ValidationException($t->_('os_staff_face_check_msg_2'));//识别失败，或者没有检测到人脸
        }
        if (isset($result['result'])){
            if ($result['result']['face_num'] > 1){
                throw new ValidationException($t->_('os_staff_face_check_msg_3'));//检测到多个人脸
            }

            if ($result['result']['face_list'][0]['code'] == OsmEnums::FACE_WITH_MASK_DETECTED){
                throw new ValidationException($t->_('os_staff_face_check_msg_mask'));//请勿佩戴口罩
            }

            if ($result['result']['face_list'][0]['code'] == OsmEnums::BRIGHTNESS_NOT_ACCEPTABLE){
                throw new ValidationException($t->_('os_staff_face_check_msg_brightness'));//亮度不合格，图片太亮或太暗
            }

            if (in_array($result['result']['face_list'][0]['code'], [OsmEnums::SHARPNESS_NOT_ACCEPTABLE, OsmEnums::LOW_IMAGE_QUALITY])){
                throw new ValidationException($t->_('os_staff_face_check_msg_low'));//图像质量评分太低
            }

            if ($result['result']['face_list'][0]['code'] == OsmEnums::FACE_INCOMPLETE_DETECTED){
                throw new ValidationException($t->_('os_staff_face_check_msg_incomplete'));//图片太模糊
            }

            //瞳间距
            if ($result['result']['face_list'][0]['code'] == OsmEnums::FACE_PUPILLARY_DETECTED){
                throw new ValidationException($t->_('os_staff_face_check_msg_pupillary_distance'));//瞳距不符合要求
            }

            if ($result['result']['face_list'][0]['code'] > OsmEnums::HIGH_QUALITY && $result['result']['face_list'][0]['code'] != OsmEnums::SHARPNESS_NOT_ACCEPTABLE){
                throw new ValidationException($t->_('os_staff_face_check_msg_4'));//人脸质量不合格
            }

            //图片高度。 640 - 1920
            if ($result['result']['image_height'] < OsmEnums::FACE_IMG_FILE_HEIGHT_MIN || $result['result']['image_height'] > OsmEnums::FACE_IMG_FILE_HEIGHT_MAX){
                throw new ValidationException($t->_('os_staff_face_check_msg_img_height_limit'));
            }

            //图片宽度。480 - 1080
            if ($result['result']['image_width'] < OsmEnums::FACE_IMG_FILE_WIDTH_MIN || $result['result']['image_width'] > OsmEnums::FACE_IMG_FILE_WIDTH_MAX){
                throw new ValidationException($t->_('os_staff_face_check_msg_img_width_limit'));
            }
        }

        if (isset($result['debug'])){
            //图片大小。最小 60kb
            if ($result['debug']['image_size'] < OsmEnums::FACE_IMG_FILE_SIZE_MIN){
                throw new ValidationException($t->_('os_staff_face_check_msg_img_size_min'));
            }

            //图片大小。最大 200kb
            if ($result['debug']['image_size'] > OsmEnums::FACE_IMG_FILE_SIZE_MAX){
                throw new ValidationException($t->_('os_staff_face_check_msg_img_size_max'));
            }
        }
    }

    /**
     * 人脸查重接口
     * @param string $img
     * @param string $staff_info_id
     * @return void
     * @throws ValidationException
     */
    public function checkFaceImageExists(string $img, string $staff_info_id): void
    {
        $params = ['url' => $img];
        $result = AiServer::getInstance()->setConfig(enums::IDENTIFY_FACE_CHECK)->sendPostRequest($params, $staff_info_id);
        $t = $this->getTranslation();
        //无返回结果
        if (empty($result)) {
            $this->logger->write_log("checkFaceImageExists:".json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            throw new ValidationException($t->_('os_staff_face_check_msg_1'));//接口调用失败
        }
        if ($result['status'] != 'OK') {
            $this->logger->write_log("checkFaceImageExists:".json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            if (isset($result['error']['message'])) {
                throw new ValidationException($result['error']['message']);//接口调用失败
            }
            throw new ValidationException($t->_('os_staff_face_check_msg_1'));//接口调用失败
        }

        // 命中了重复的数据
        if ($result['result']['flash_hr_exist'] === true && !empty($result['result']['person_id'])) {
            $this->logger->write_log("checkFaceImageExists:".json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            // ai 查重是全网查重，没有排除自己 这里如果提示的工号与当前工号一致，说明找到了自己，则不抛异常
            if (empty($staff_info_id) || ($staff_info_id != $result['result']['person_id'])) {
                //操作失败，人脸信息与 %staff_info_id% 重复
                throw new ValidationException($t->_('os_staff_face_check_msg_5',
                    ['staff_info_id' => $result['result']['person_id']]));
            }
        }
    }

    /**
     * 更新信息时 向ai同步
     * @param string $img
     * @param int $staff_info_id
     * @return false|string
     * @throws ValidationException
     */
    public function faceImageUpdate(string $img, int $staff_info_id)
    {
        $params = ['url' => $img, 'person_id' => $staff_info_id];
        $result = AiServer::getInstance()->setConfig(enums::IDENTIFY_FACE_DATA_SYNCHRONIZATION)->sendPostRequest($params,
            $staff_info_id);

        if ($result['status'] == 'ERROR') {
            $this->logger->write_log("faceImageUpdate - error:".json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            return false;
        }

        if ($result['status'] == 'OK') {
            return json_encode(['emb' => $result['result']['emb'], 'version' => $result['result']['version']],
                JSON_UNESCAPED_UNICODE);
        }
        return false;
    }

    /**
     * 删除信息时 向ai同步
     * @param string $staff_info_id
     * @return bool|false
     * @throws ValidationException
     */
    public function faceImageDelete(string $staff_info_id): bool
    {
        $params = ['person_id' => $staff_info_id];
        $result = AiServer::getInstance()->setConfig(enums::IDENTIFY_FACE_DATA_SYNCHRONIZATION)->sendDeleteRequest($params,
            $staff_info_id);

        if ($result['status'] != 'OK') {
            $this->logger->write_log("faceImageDelete - error:".json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            return false;
        }
        return true;
    }
}