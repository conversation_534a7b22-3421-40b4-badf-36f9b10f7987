<?php
/**
 * Author: Bruce
 * Date  : 2022-12-01 20:58
 * Description:
 */

namespace FlashExpress\bi\App\Server\Osm;


use Exception;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrOutsourcingOrderDetailModel;
use FlashExpress\bi\App\Models\backyard\HrOutsourcingOrderModel;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingHubStaffAttendanceModel;
use FlashExpress\bi\App\Modules\Th\library\Enums\HrJobTitleEnums;
use FlashExpress\bi\App\Repository\OsOrderRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\OsStaffServer;
use FlashExpress\bi\App\Server\SysServer;
use FlashExpress\bi\App\Server\SysStoreServer;

class OutsourcingOrderServer extends BaseServer
{

    public function __construct($lang = '')
    {
        parent::__construct($lang);
    }

    /**
     * 订单列表
     * @param $params
     * @param $user_info
     * @return array
     */
    public function getOrderList($params, $user_info)
    {
        $params['page']       = empty($params['page']) ? 1 : $params['page'];
        $params['page_size']  = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size']  = ($params['page_size'] > 100) ? 100 : $params['page_size'];

        $params['company_id'] = $user_info['id'];

        $osOrderRepository = new OsOrderRepository($this->timeZone);

        $count = $osOrderRepository->getOutsourcingOrderQuery($params, true);

        $total = !empty($count) ? intval($count['count']) : 0;
        if (empty($total)) {
            return ['total' => 0, 'list' => []];
        }

        $list     = $osOrderRepository->getOutsourcingOrderQuery($params);

        //待提交才 展示提示。
        $isDisplayRedTips = HrOutsourcingOrderModel::STATUS_PENDING == $params['status'] ? true : false;
        $dataList = $this->formatList($list, $isDisplayRedTips);

        return ['total' => $total, 'list' => $dataList];
    }

    /**
     * 数据格式化
     * @param $data
     * @return array
     */
    public function formatList($data, $isDisplayRedTips = false)
    {
        $serial_nos = array_column($data, 'serial_no');

        //获取网点信息
        $storeIds        = array_column($data, 'store_id');
        $storeInfoToId   = (new SysStoreServer())->getStoreName($storeIds);
        $storeInfoToId['-1'] = enums::HEAD_OFFICE;

        //获取班次信息
        $shift_list    = (new OsStaffServer($this->lang, $this->timeZone))->getShiftListAll();
        $shiftListToId = array_column($shift_list, null, 'id');

        //获取职位信息
        $job_title = array_column((new SysServer($this->lang, $this->timeZone))->getJobTitleList(), 'job_name', 'id');

        $orderStaffNumber     = (new OsOrderRepository($this->timeZone))->getOrderDetailStaffNumber($serial_nos);
        $orderStaffNumberToNo = !empty($orderStaffNumber) ? array_column($orderStaffNumber, 'count', 'serial_no') : [];
        //出勤数据
        $attendanceNum = $this->getOrderAttendanceNum($serial_nos);
        $list = [];
        foreach ($data as $key => $oneData) {
            $list[$key]['serial_no']                 = $oneData['serial_no'];
            $list[$key]['order_id']                  = $oneData['order_id'];
            $list[$key]['title']                     = ($storeInfoToId[$oneData['store_id']] ?? '') . ' ' . $this->getTranslation()->_('out_os_order');
            $list[$key]['status_name']               = $this->getTranslation()->_(HrOutsourcingOrderModel::STATUS_TRANS[$oneData['status']] ?? '');
            $list[$key]['status']                    = $oneData['status'];
            $list[$key]['employment_date']           = $oneData['employment_date'];
            $list[$key]['shift_name']                = isset($shiftListToId[$oneData['shift_id']])
                ? $shiftListToId[$oneData['shift_id']]['start'] . '-' . $shiftListToId[$oneData['shift_id']]['end']
                : $oneData['shift_begin_time'] . '-' . $oneData['shift_end_time'];
            $list[$key]['demend_num']                = intval($oneData['final_audit_num']);//可能被修改了
            $list[$key]['job_title_name']            = $job_title[$oneData['job_id']] ?? '';
            $list[$key]['selected_num']              = !empty($orderStaffNumberToNo[$oneData['serial_no']]) ? intval($orderStaffNumberToNo[$oneData['serial_no']]) : 0;
            $list[$key]['need_remark']               = $oneData['need_remark'];
            $list[$key]['order_type']                = $oneData['order_type'];
            $list[$key]['order_type_txt']            = $oneData['order_type'] == 2 ? $this->getTranslation()->_('os_order_type_2') : '';
            $list[$key]['is_show_attendance_export'] = !empty($attendanceNum[$oneData['serial_no']]);
            $list[$key]['is_exceeds']                = $oneData['is_exceeds'] == HrOutsourcingOrderModel::IS_EXCEEDS_YES && $isDisplayRedTips ? true : false;
        }

        return $list;
    }


    /**
     * 获取订单出勤人数
     * @param $serial_nos
     * @return array
     */
    public function getOrderAttendanceNum($serial_nos): array
    {
        if (empty($serial_nos) || !isCountry('MY')) {
            return [];
        }

        $data = OutsourcingHubStaffAttendanceModel::find([
            'columns'    => 'count(1) as num,order_serial_no',
            'conditions' => 'order_serial_no in ({order_serial_no:array})',
            'bind'       => ['order_serial_no' => $serial_nos],
            'group'      => 'order_serial_no',
        ])->toArray();
        return empty($data) ? [] : array_column($data, 'num', 'order_serial_no');
    }


    /**
     *
     * @param $serial_no
     * @return mixed
     * @throws BusinessException
     */
    public function getAttendanceExcel($serial_no)
    {
        $params = [
            'order_serial_no'  => $serial_no,
        ];
        $fle_rpc = (new ApiClient('hcm_rpc', '', 'hub_os_attendance_export', $this->lang));
        $fle_rpc->setParams($params);
        $res = $fle_rpc->execute();
        if (isset($res['error'])) {
            throw new BusinessException($res['error']);
        }
        if (!empty($res['result'])) {
            return $res['result'];
        }
        throw new Exception('调用HCM下载hub外协考勤excel');
    }



    /**
     * 获取外协订单已配置员工列表
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getOrderStaffList($params)
    {
        $params['page']      = empty($params['page']) ? 1 : $params['page'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size'] = ($params['page_size'] > 100) ? 100 : $params['page_size'];
        $params['order_id']  = $this->processingDefault($params, 'order_id', 2);


        $osOrderRepository = new OsOrderRepository($this->timeZone);
        $orderInfo = $osOrderRepository->getOsOrderInfo($params['order_id']);

        if (empty($orderInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        $params['serial_no'] = $orderInfo['serial_no'];
        $count = $osOrderRepository->getOrderDetailStaffQuery($params, true);

        $total = !empty($count) ? intval($count['count']) : 0;
        if (empty($total)) {
            return ['total' => 0, 'list' => []];
        }

        $list = $osOrderRepository->getOrderDetailStaffQuery($params);

        $serial_number = $params['page'];
        foreach ($list as &$one) {
            $one['serial_number'] = $serial_number;
            $serial_number++;
        }

        return ['total' => $total, 'list' => $list];
    }

    /**
     * 获取 可选人员 列表信息
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getOptionalStaffList($params)
    {
        $params['page']      = empty($params['page']) ? 1 : $params['page'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size'] = ($params['page_size'] > 100) ? 100 : $params['page_size'];
        $params['order_id']  = $this->processingDefault($params, 'order_id', 2);

        $osOrderRepository = new OsOrderRepository($this->timeZone);
        $orderInfo = $osOrderRepository->getOsOrderInfo($params['order_id']);

        if (empty($orderInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        //增加网点的筛选
        $params['store_id'] = $orderInfo['store_id'];
        $params['job_title'] = $orderInfo['job_id'];
        if (isCountry('TH') && in_array($orderInfo['job_id'], [
                HrJobTitleEnums::JOB_TITLE_OUTSOURCE_PARCEL_UNLOADER,
                HrJobTitleEnums::JOB_TITLE_OUTSOURCE_WAREHOUSE_SORTER,
            ])) {
            $params['job_title'] = HrJobTitleEnums::JOB_TITLE_OUTSOURCE;
        }

        $whereCurrent['serial_no'] = $orderInfo['serial_no'];

        $currentOrderCount = $osOrderRepository->getOrderDetailStaffQuery($whereCurrent, true);
        //当前订单 已配置的人数
        $currentOrderTotal = !empty($currentOrderCount) ? intval($currentOrderCount['count']) : 0;
        //当前订单 已配置的工号
        $currentOrderStaffIds = [];
        if (!empty($currentOrderTotal)) {
            $whereCurrent['is_all'] = 1;
            $currentOrderList     = $osOrderRepository->getOrderDetailStaffQuery($whereCurrent);
            $currentOrderStaffIds = !empty($currentOrderList) ? array_column($currentOrderList, 'staff_info_id') : [];
        }

        //与当前订单  相同雇佣日期 相同班次 员工使用中（没被释放）的 订单
        if (isCountry(['TH', 'MY'])){
            $params['use_staff_info_ids'] = $this->getUsingStaffInfoV3($orderInfo);
        }else{
            $params['use_staff_info_ids'] = $this->getUsingStaffInfo($orderInfo);
        }

        // 查询外协员工加班没有释放的数据
        $use_os_otstaff_ids           = (new OutsourcingOTServer($this->lang, $this->timeZone))->getOutsourcingOTIntersectionV2($orderInfo);
        $params['use_staff_info_ids'] = array_values(array_unique(array_merge($params['use_staff_info_ids'], $use_os_otstaff_ids)));
        //可选员工数量
        if (isCountry(['TH', 'MY'])){
            $count = $osOrderRepository->getOptionalStaffQueryV2($params, true);
        }else{
            $count = $osOrderRepository->getOptionalStaffQuery($params, true);
        }

        $total = !empty($count) ? intval($count['count']) : 0;
        if (empty($total)) {
            return ['total' => 0, 'list' => []];
        }
        //可选员工列表
        if (isCountry(['TH', 'MY'])) {
            $list = $osOrderRepository->getOptionalStaffQueryV2($params);
        } else {
            $list = $osOrderRepository->getOptionalStaffQuery($params);
        }
        $result = [
            'total'                => $total,
            'list'                 => $list,
            'selected_staff_count' => $currentOrderTotal,
            'selected_staff_ids'   => $currentOrderStaffIds,
        ];

        return $result;
    }

    /**
     * 与当前订单  相同雇佣日期 相同班次 员工使用中（没被释放）的 订单
     * @param $orderInfo
     * @return array
     */
    public function getUsingStaffInfo($orderInfo)
    {
        $osOrderRepository = new OsOrderRepository($this->timeZone);
        $otherOrderInfo = $osOrderRepository->getOsOrderList($orderInfo);

        //判断 当前班次所在日期
        $otherOrderNo = [];
        if (!empty($otherOrderInfo)) {
            $otherOrderShiftId = array_column($otherOrderInfo, 'shift_id');
            $otherOrderShiftId[] = $orderInfo['shift_id'];
            $shiftInfo         = $this->getShiftInfo($otherOrderShiftId);

            $employmentDate = $orderInfo['employment_date'];
            $orderStart     = $employmentDate.' '.$shiftInfo[$orderInfo['shift_id']]['start'] ?? '';
            $orderEnd       = $employmentDate.' '.$shiftInfo[$orderInfo['shift_id']]['end'] ?? '';

            if ($orderStart > $orderEnd) {
                $orderEnd = date('Y-m-d H:i', strtotime("+1 day", strtotime($orderEnd)));
            }

            foreach ($otherOrderInfo as $otherOne) {
                $otherEmploymentDate = $otherOne['employment_date'];
                $otherOneStart       = $otherEmploymentDate.' '.$shiftInfo[$otherOne['shift_id']]['start'] ?? '';
                $otherOneEnd         = $otherEmploymentDate.' '.$shiftInfo[$otherOne['shift_id']]['end'] ?? '';

                if ($otherOneStart > $otherOneEnd) {
                    $otherOneEnd = date('Y-m-d H:i', strtotime("+1 day", strtotime($otherOneEnd)));
                }

                if (($orderStart <= $otherOneStart && $otherOneStart <= $orderEnd) || ($orderStart <= $otherOneEnd && $otherOneEnd <= $orderEnd)) {
                    $otherOrderNo[] = $otherOne['serial_no'];
                }
            }
        }

        $useStaffId = [];
        if (!empty($otherOrderNo)) {
            $where['serial_no'] = $otherOrderNo;
            $where['is_all']    = 1;
            $useStaffInfo       = $osOrderRepository->getOrderDetailStaffQuery($where);
            $useStaffId         = !empty($useStaffInfo) ? array_column($useStaffInfo, 'staff_info_id') : [];
        }

        return $useStaffId;
    }

    /**
     * 查询与外协员工加班时间有冲突的数据
     * @param array $params
     * @return array
     */
    public function getUsingStaffInfoV2(array $params = []): array
    {
        $osOrderRepository = new OsOrderRepository($this->timeZone);
        $otherOrderInfo    = $osOrderRepository->getOsOrderList($params);

        //判断 当前班次所在日期
        $otherOrderNo = [];
        if (!empty($otherOrderInfo)) {
            $otherOrderShiftId = array_column($otherOrderInfo, 'shift_id');
            $shiftInfo         = $this->getShiftInfo($otherOrderShiftId);

            // 加班开始时间和结束时间
            $orderStart = strtotime($params['start_time']);
            $orderEnd   = strtotime($params['end_time']);

            foreach ($otherOrderInfo as &$otherOne) {
                $otherEmploymentDate = $otherOne['employment_date'];
                $otherOneStart       = strtotime($otherEmploymentDate.' '.$shiftInfo[$otherOne['shift_id']]['start']) ?? '';
                $otherOneEnd         = strtotime($otherEmploymentDate.' '.$shiftInfo[$otherOne['shift_id']]['end']) ?? '';
                // 跨天
                if ($otherOneStart > $otherOneEnd) {
                    $otherOneEnd = strtotime("+1 day", $otherOneEnd);
                }

                // 外协订单班次的开始时间或者结束时间落在 外协加班区间内
                if (($orderStart <= $otherOneStart && $otherOneStart < $orderEnd) || ($orderStart < $otherOneEnd && $otherOneEnd <= $orderEnd)) {
                    $otherOrderNo[] = $otherOne['serial_no'];
                }

                // 加班的开始时间或者结束时间落在外协订单的班次时间内
                if (($otherOneStart <= $orderStart && $orderStart < $otherOneEnd) || ($otherOneStart < $orderEnd && $orderEnd <= $otherOneEnd)) {
                    $otherOrderNo[] = $otherOne['serial_no'];
                }

                // 班次时间吧加班时间包住了
                if ($otherOneStart <= $orderStart && $otherOneEnd >= $orderEnd) {
                    $otherOrderNo[] = $otherOne['serial_no'];
                }
            }
        }

        $useStaffId = [];
        if (!empty($otherOrderNo)) {
            $where['serial_no'] = $otherOrderNo;
            $where['is_all']    = 1;
            $useStaffInfo       = $osOrderRepository->getOrderDetailStaffQuery($where);
            $useStaffId         = !empty($useStaffInfo) ? array_column($useStaffInfo, 'staff_info_id') : [];
        }
        return $useStaffId;
    }


    /**
     * 获取指定班次信息
     * @param $shift_ids
     * @return array
     */
    public function getShiftInfo($shift_ids)
    {
        $shift_list = HrShiftModel::find([
            'columns' => 'id, type, start, [end]',
            'conditions' => "id IN ({shift_ids:array})",
            'order' => 'type,start asc',
            'bind' => [
                'shift_ids' => $shift_ids,
            ],
        ])->toArray();
        $shift_list = array_column($shift_list, null, 'id');
        return $shift_list;
    }


    /**
     * 提交 配置员工
     * @param $params
     * @return array|bool
     * @throws ValidationException
     */
    public function submitStaff($params)
    {
        $params['order_id']  = $this->processingDefault($params, 'order_id', 2);
        $params['staff_ids'] = $this->processingDefault($params, 'staff_ids');

        $osOrderRepository = new OsOrderRepository($this->timeZone);

        $orderInfo = $osOrderRepository->getOsOrderInfo($params['order_id']);

        if (empty($orderInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        //待提交状态才可以提交
        if ($orderInfo['status'] != HrOutsourcingOrderModel::STATUS_PENDING) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        //订单生效时间 小于 当前时间。无法配置新员工。
        if(strtotime($orderInfo['effective_date']) < strtotime(date('Y-m-d H:i:s'))){
            throw new ValidationException($this->getTranslation()->_('hub_os_time_out'));
        }

        if (isCountry(['TH', 'MY'])) {
            $usingStaffIds = $this->getUsingStaffInfoV3($orderInfo);
        } else {
            $usingStaffIds = $this->getUsingStaffInfo($orderInfo);
        }
        // 查询外协员工加班没有释放的数据
        $use_os_ot_staff_ids = (new OutsourcingOTServer($this->lang, $this->timeZone))->getOutsourcingOTIntersectionV2($orderInfo);
        $usingStaffIds       = array_values(array_unique(array_merge($usingStaffIds, $use_os_ot_staff_ids)));

        $staffIds = !empty($params['staff_ids']) ? explode(',', $params['staff_ids']) : [];

        //如果提交的 工号与未被释放的工号，有交集
        if (array_intersect($staffIds, $usingStaffIds)) {
            throw new ValidationException($this->getTranslation()->_('isset_using_staff'));
        }
        $hubStaffInfoToStaffId = [];
        if(!empty($staffIds)) {
            $hubStaffInfo = $osOrderRepository->getStaffInfo($staffIds);

            if (empty($hubStaffInfo)) {
                $this->getDI()->get('logger')->write_log('os-submitStaff : staff info not found,params:'.json_encode($staffIds),
                    'notice');
                throw new ValidationException('staff info not found:'.$this->getTranslation()->_('data_error'));
            }

            $staffItemsInfo = (new StaffRepository())->getStaffItemsInfo($staffIds, ["DRIVER_LICENSE", "BANK_NO_NAME", "BANK_NO", "CAR_NO"]);

            $hubStaffInfoToStaffId = array_column($hubStaffInfo, null, 'staff_info_id');
        }

        $allStaffData = [];
        foreach ($staffIds as $oneStaffId) {
            if (!isset($hubStaffInfoToStaffId[$oneStaffId])) {
                $this->getDI()->get('logger')->write_log('os-submitStaff : staff info not found,staff_id:'.$oneStaffId,
                    'notice');
                continue;
            }
            $oneStaffData['serial_no']            = $orderInfo['serial_no'];
            $oneStaffData['staff_info_id']        = $oneStaffId;
            $oneStaffData['identity']             = $hubStaffInfoToStaffId[$oneStaffId]['identity'] ?? '';
            $oneStaffData['outsourcing_type']     = 'company';
            $oneStaffData['company_name_ef']      = $hubStaffInfoToStaffId[$oneStaffId]['company_name_ef'] ?? '';
            $oneStaffData['staff_name']           = $hubStaffInfoToStaffId[$oneStaffId]['name'] ?? '';
            $oneStaffData['driver_license']       = !empty($staffItemsInfo[$oneStaffId]['DRIVER_LICENSE']) ? $staffItemsInfo[$oneStaffId]['DRIVER_LICENSE'] : '';
            $oneStaffData['mobile']               = $hubStaffInfoToStaffId[$oneStaffId]['mobile'] ?? '';
            $oneStaffData['sex']                  = $hubStaffInfoToStaffId[$oneStaffId]['sex'] ?? 0;
            $oneStaffData['personal_email']       = $hubStaffInfoToStaffId[$oneStaffId]['personal_email'] ?? '';
            $oneStaffData['bank_id']              = $hubStaffInfoToStaffId[$oneStaffId]['bank_type'] ?? '';
            $oneStaffData['bank_no_name']         = !empty($staffItemsInfo[$oneStaffId]['BANK_NO_NAME']) ? $staffItemsInfo[$oneStaffId]['BANK_NO_NAME'] : '';
            $oneStaffData['bank_no']              = !empty($staffItemsInfo[$oneStaffId]['BANK_NO']) ? $staffItemsInfo[$oneStaffId]['BANK_NO'] : '';
            $oneStaffData['car_no']               = !empty($staffItemsInfo[$oneStaffId]['CAR_NO']) ? $staffItemsInfo[$oneStaffId]['CAR_NO'] : '';
            $oneStaffData['create_staff_info_id'] = 0;
            $oneStaffData['pay_type']             = 'BY_DAY';
            $oneStaffData['effective_date']       = $orderInfo['effective_date'];
            $oneStaffData['invalid_date']         = $orderInfo['invalid_date'];
            $oneStaffData['outsourcing_company_id'] = $hubStaffInfoToStaffId[$oneStaffId]['outsourcing_company_id'];
            $allStaffData[] = $oneStaffData;
        }

        if(count($allStaffData) > intval($orderInfo['final_audit_num'])) {
            throw new ValidationException($this->getTranslation()->_('select_nub_need_less_need'));
        }

        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $orderDetail = $osOrderRepository->getHrOutsourcingOrderDetail($orderInfo['serial_no']);
            if(!empty($orderDetail)) {
                $sql = " delete from `hr_outsourcing_order_detail` where `serial_no`= :serial_no ";
                $sql_param = [
                    'serial_no' => $orderInfo['serial_no'],
                ];
                $db->execute($sql,$sql_param);
            }
            if(!empty($allStaffData)){
                $osOrderRepository->batch_insert('hr_outsourcing_order_detail', array_remove_key($allStaffData,'outsourcing_company_id'));
            }
            $this->createSubOrderAndDetail($orderInfo, $allStaffData);

            $db->updateAsDict("hr_outsourcing_order", ['is_exceeds' => HrOutsourcingOrderModel::IS_EXCEEDS_NO],
                ["conditions" => "id='{$orderInfo['id']}'"]);

            $db->commit();
            $this->getDI()->get("logger")->write_log("submitStaff-order detail: " . json_encode($allStaffData, JSON_UNESCAPED_UNICODE), "info");
            return $this->checkReturn(['data'=> []]);
        }catch (\Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log('submitStaff-order detail'. $e->getMessage() . $e->getTraceAsString());
            return $this->checkReturn(-3, $e->getMessage());
        }
    }


    /**
     * 获取待生效的订单数
     * @param $companyId
     * @return mixed
     */
    public function getOutSourcingOrderCount($companyId)
    {
        return HrOutsourcingOrderModel::count(
            [
                'conditions' => 'out_company_id = :company_id: and status = :status:',
                'bind'       => ['company_id' => $companyId, 'status' => HrOutsourcingOrderModel::STATUS_PENDING],
            ]
        );
    }

    /**
     * 获取其他订单（待生效、已生效）中已使用的员工ID
     * @param $params
     * @return array
     */
    public function getUsingStaffInfoV3($params)
    {
        $osOrderRepository = new OsOrderRepository($this->timeZone);
        $otherOrderInfo = $osOrderRepository->getOtherOrderList($params);
        $otherOrderNo = array_column($otherOrderInfo,'serial_no');

        $useStaffId = [];
        if (!empty($otherOrderNo)) {
            $where['serial_no'] = $otherOrderNo;
            $where['is_all']    = 1;
            $useStaffInfo       = $osOrderRepository->getOrderDetailStaffQuery($where);
            $useStaffId         = !empty($useStaffInfo) ? array_column($useStaffInfo, 'staff_info_id') : [];
        }

        return $useStaffId;
    }

    /**
     * @param $order
     * @param $detail
     * @return void
     */
    public function createSubOrderAndDetail($order, $detail)
    {
        $detail = array_group_by($detail,'outsourcing_company_id');

        $osOrderRepository = new OsOrderRepository($this->timeZone);

        $oldOrders = HrOutsourcingOrderModel::find([
            'conditions' => "inherited_serial_no = :serial_no:",
            'bind'       => [
                'serial_no' => $order['serial_no'],
            ],
        ]);
        if (!empty($oldOrders)){
            foreach ($oldOrders as $oldOrder){
                $oldOrderDetails = HrOutsourcingOrderDetailModel::find(
                    [
                        'conditions' => "serial_no = :serial_no:",
                        'bind'       => [
                            'serial_no' => $oldOrder->serial_no,
                        ],
                    ]
                );
                $oldOrderDetails->delete();
            }
            $oldOrders->delete();
        }

        foreach ($detail as $key=>$item){
            if ($key == $order['out_company_id']){
                continue;
            }
            $subOrder = $order;
            unset($subOrder['id']);
            $subOrder['serial_no'] = $order['serial_no'].'_'.$key;
            $subOrder['order_type'] = 2;
            $subOrder['inherited_serial_no'] = $order['serial_no'];
            $subOrder['out_company_id'] = $key;
            $subOrder['status'] = HrOutsourcingOrderModel::STATUS_EFFECTIVE;
            $osOrderRepository->batch_insert('hr_outsourcing_order',[$subOrder]);

            $subDetails = [];
            foreach ($item as $key1=>$item1){
                $subDetail = $item1;
                $subDetail['serial_no'] = $subOrder['serial_no'];
                unset($subDetail['outsourcing_company_id']);
                $subDetails[] = $subDetail;
            }
            $osOrderRepository->batch_insert('hr_outsourcing_order_detail',$subDetails);
        }
    }

}