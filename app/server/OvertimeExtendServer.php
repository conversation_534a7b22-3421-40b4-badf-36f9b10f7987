<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/2/3
 * Time: 10:31 AM
 */

namespace FlashExpress\bi\App\Server;


use App\Country\Tools;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditPermissionModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftMiddleDateModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Modules\My\Server\AttendanceCalendarV2Server;
use FlashExpress\bi\App\Repository\StaffRepository;

class OvertimeExtendServer extends OvertimeServer{

    public $staffInfo;//hrs 员工信息
    public $shiftInfo;//班次信息
    public $typeBook;//加班类型枚举
    public $timeTypeBook;//加班倍数枚举
    public $holidayTypes;
    public $second = 59;//加班开始时间限制容差 59秒


    //判断是否走 ot 子审批流 https://l8bx01gcjr.feishu.cn/docs/doccnM3g2ev63nhQDTFk15I13Vg 只有泰国用
    public function check_ot_flow($staff_info){
        //所属部门  8,17,38,55 3,12,18,20,26,41,22
        /**
         *   3. Hub Management及其子部门 25  4. Transportation部门及其子部门   5. QAQC部门及其子部门   6. Express Thai Product部门及其子部门
         *   7. Customer Service部门及其子部门  8. Asset Resource Management部门及其子部门  9. Procurement部门及其子部门
         *   10. Finance and Accounting部门及其子部门  11. Project Management部门及其子部门  12. Flash Home部门及其子部门  14. Group CEO Office部门及其子部门
         *   15. EHS部门及其子部门  16. Marketing部门及其子部门
         * 93 部门后来去掉了 EHS
         */
        if(in_array($staff_info['sys_department_id'],array(8,17,38,55,3,12,18,20,22,26,41)))
            return true;
        //所属公司 20001,40001   17. Flash Fullfillment公司   18. Flash Laos公司
        if(in_array($staff_info['company_id'],array(20001,40001)))
            return true;

        //职位 或者 组织机构
	    //如果是 Network Management  或者是 Network bulky 部门 并且  职位是 branch_supervisor   或者是 部门是  Hub Management
	    $envModel = new SettingEnvServer();
        $setting_code = array('dept_network_management_id','dept_network_bulky_id','ot_s1_job_ids', 'flash_freight_hub_management_id', 'dept_hub_management_id');
        $setting_val = $envModel->listByCode($setting_code);
        if(!empty($setting_val))
            $setting_val = array_column($setting_val,'set_val','code');

        $networkManagementId = $setting_val['dept_network_management_id'] ?? '';
        $networkBulkyId = $setting_val['dept_network_bulky_id'] ?? '';
        $flashFreightHubId = $setting_val['flash_freight_hub_management_id'] ?? '';
        $hubManagementId = $setting_val['dept_hub_management_id'] ?? '';

        //新需求 针对 指定2个部门下的所有职位 都走新审批流 增加了一层上上级
        //https://flashexpress.feishu.cn/docx/doxcn635y5rMuLlchwXtVHISmwE

        if( in_array($staff_info['sys_department_id'],[$networkManagementId,$networkBulkyId])
            || in_array($staff_info['sys_department_id'], [$hubManagementId, $flashFreightHubId])
        ) {
            return true;
        }

        return false;

    }


    /**
     * 校验权限
     * @param $staffId
     * @param $data
     * 申请加班 针对每种类型的  权限展示 https://l8bx01gcjr.feishu.cn/docs/doccnM3g2ev63nhQDTFk15I13Vg
     */
    public function check_permission($staff_id, $type_list = array())
    {

        $default_v = 'x';//默认皮配值 前提条件是 其他匹配值没有x
        if(empty($staff_id))
            return array();
        //获取员工信息
        $staff_re = new StaffRepository($this->lang);
        $staff_info = $staff_re->getStaffPosition($staff_id);
        if(empty($staff_info))
            return array();
        if(empty($staff_info['node_department_id']))
            return array();

        //紧急需求 部分网点 主管职位 放开申请权限
        $current = date('Y-m-d');
        if($staff_info['job_title'] == 16 && $staff_info['sys_store_id'] != '-1' && $current < '2023-03-11'){
            $setting_model = SettingEnvModel::findFirst("code = 'open_permission_store'");
            if(!empty($setting_model)){
                $store_env = explode(',',$setting_model->set_val);
                if(in_array($staff_info['sys_store_id'], $store_env))
                    return empty($type_list) ? true : $type_list;

            }
        }

        //获取所属公司id
        $dep_info = SysDepartmentModel::findFirst("id = {$staff_info['node_department_id']} and deleted = 0");
        if(empty($dep_info))
            return array();
        //调试
//        $dep_info->ancestry_v3 = '123/222/1/4/32';
//        $dep_info->ancestry_v3 = '999/2';
        $ancestry = explode('/',$dep_info->ancestry_v3);
        if($ancestry[0] != '999')//错误组织架构数据
            return array();

        //补全5个层级
        $ancestry[0] = empty($ancestry[0]) ? $default_v : $ancestry[0];
        $ancestry[1] = empty($ancestry[1]) ? $default_v : $ancestry[1];
        $ancestry[2] = empty($ancestry[2]) ? $default_v : $ancestry[2];
        $ancestry[3] = empty($ancestry[3]) ? $default_v : $ancestry[3];
        $ancestry[4] = empty($ancestry[4]) ? $default_v : $ancestry[4];


        //所有配置权限记录
        $module_id = AuditPermissionModel::MODULE_P_1;
        $version = 'ot_2';
        $permission_list = AuditPermissionModel::get_permission($module_id,$version);
        if(empty($permission_list))//没有配置对应公司限制条件 不在限制公司之内 都可申请
        {
            if(empty($type_list))
                return true;

            return $type_list;
        }

        //获取对应 限制条件的最终值
        $format = array();
        foreach ($permission_list as $v){//组装 key => value
            $l1 = empty($v['ancestry_1']) ? "{$default_v}" : "{$v['ancestry_1']}";
            $l2 = empty($v['ancestry_2']) ? "_{$default_v}" : "_{$v['ancestry_2']}";
            $l3 = empty($v['ancestry_3']) ? "_{$default_v}" : "_{$v['ancestry_3']}";
            $l4 = empty($v['ancestry_4']) ? "_{$default_v}" : "_{$v['ancestry_4']}";
            $l5 = empty($v['ancestry_5']) ? "_{$default_v}" : "_{$v['ancestry_5']}";
            $job_id = empty($v['job_title_id']) ? "_{$default_v}" : "_{$v['job_title_id']}";
            $grade = empty($v['job_title_grade']) ? "_{$default_v}" : "_{$v['job_title_grade']}";
            $k = $l1.$l2.$l3.$l4.$l5.$job_id.$grade;
            $format[$k] = json_decode($v['permission_value']);
        }


        //排列组合 当前员工 的key
        //def 为 默认key
        $combination_arr = array(
            array($ancestry[0],"{$default_v}"),
            array('_'.$ancestry[1],"_{$default_v}"),
            array('_'.$ancestry[2],"_{$default_v}"),
            array('_'.$ancestry[3],"_{$default_v}"),
            array('_'.$ancestry[4],"_{$default_v}"),
            array('_'.$staff_info['job_title'],"_{$default_v}"),//职位
            array('_'.$staff_info['job_title_grade_v2'],"_{$default_v}"),//职级

        );
        $staff_key_arr = combination($combination_arr);
        $staff_key_arr = array_unique($staff_key_arr[0]);
        //获取 匹配上的 并且 默认值数量最少的
        $correct_k = array();
        $num = count($combination_arr) + 1;//默认 x 数量 用x 拆分 num 为数量+1
        $final_value = array();//最终返回的 权限值
        if(!empty($staff_key_arr)){
            //获取所有匹配上的key
            foreach ($format as $key => $val){
                if(in_array($key,$staff_key_arr))
                    $correct_k[] = $key;
            }
            $cks = array();
            $k = '';
            foreach ($correct_k as $ck => $item){
                $n = explode($default_v,$item);
                if($num > count($n))
                {
                    $final_value = $item;
                    $num = count($n);
                    $k = $ck;
                }else if($num == count($n)){
                    $cks[] = $ck;
                    $cks[] = $k;
                }
            }
            //看数量相同的 是不是只有一个
            if(!empty($cks)){
                $check_correct = array();
                foreach ($cks as $ck){
                    $check_correct[] = $correct_k[$ck];
                }
                $this->logger->write_log("ot_check_permission {$staff_id} ".json_encode($correct_k),'info');
                $same_value = get_final_key($check_correct,$default_v);
                //相同数的 和最终key 比对 default的 数量  取默认值最少的
                $final_value = count(explode($default_v,$final_value)) > count(explode($default_v,$same_value)) ? $final_value : $same_value;
            }

        }
        $this->logger->write_log("ot_check_permission {$staff_id} final_value {$final_value} end",'info');
        if(empty($final_value))//一个都没匹配上 返回空
            return array();
        $final_value = $format[$final_value];

        if(empty($type_list)){
            //入口按钮权限判断 返回bool
            return empty($final_value) ? false : true;
        }
        $return = array();
        $data = array_column($type_list,null,'code');
        foreach ($final_value as $type){
            $return[] = $data[$type];
        }
        return $return;
    }


    /**
     * @param $store_id 网点id
     * @return string  返回 对应的 大区-片区-网点名称
     */
    public function getStoreDetailName($store_id){
        if(empty($store_id) || $store_id == '-1')
            return '';
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind' => ['store_id' => $store_id]
        ]);

        if(empty($storeInfo))
            return '';

        //拼接网点名称 大区-片区-网点名称
        $str = '';
        //大区
        if (!empty($storeInfo->manage_region)) {
            $region = SysManageRegionModel::findFirst([
                'conditions' => 'id = :region_id:',
                'bind'       => ['region_id' =>  $storeInfo->manage_region],
            ]);

            if(!empty($region))
                $str .= "{$region->name}-";
        }
        //片区
        if (!empty($storeInfo->manage_piece)) {
            $piece = SysManagePieceModel::findFirst([
                'conditions' => "id = :piece_id:",
                'bind' => ['piece_id'  => $storeInfo->manage_piece,]
            ]);
            if(!empty($piece))
                $str .= "{$piece->name}-";
        }
        $str .= "{$storeInfo->name}";
        return $str;
    }

    //泰国 ot 详情页 用到的 rpc 数据 只有 dc officer 和 assi branch supervisor 用到
    public function ot_dc_detail($staff_info, $apply_info, $param = [])
    {
        $references                 = json_decode($apply_info['references'], true);
        $detailLists['ot_detail_6'] = $references['store_name'] ?? '';//员工网点名称 带 大区片区
//        $detailLists['ot_detail_7'] = isset($references['job_num']) ? (string)$references['job_num']: '0';//在职仓管人数
        //新需求 要夹在这块 https://flashexpress.feishu.cn/docx/doxcne2vUHNJAf8WSoeqOkWW5bt

        $detailLists['should_delivery_today'] = 0;//当天应派件 实时调用接口 审批通过以后 固化
        $detailLists['should_pickup_today']   = 0;//当天揽件包裹量 实时调用接口 审批通过以后 固化

        if ($apply_info['state'] != enums::APPROVAL_STATUS_PENDING) {//最终状态 直接取数据 没有就没有了
            $detailLists['should_delivery_today'] = $references['should_delivery_today'] ?? 0;
            $detailLists['should_pickup_today']   = $references['should_pickup_today'] ?? 0;
            $detailLists['attendance_rate']       = $references['attendance_rate'] ?? '';//出勤率
            $detailLists['dc_today_effect']       = $references['dc_today_effect'] ?? 0 .' '.$this->getTranslation()->_('rate_unit');//人效
        } else {//带审批状态 调rpc
            $param['store_id'] = $staff_info['sys_store_id'];
            $param['date']     = $apply_info['date_at'];
            $ac                = new ApiClient('bi_rpcv2', '', 'dc.get_dc_should_delivery_count', $this->lang);
            $ac->setParams($param);
            $ac_result = $ac->execute();
            $this->logger->write_log("delivery today num {$param['store_id']}_{$param['date']}  res ".json_encode($ac_result,
                    JSON_UNESCAPED_UNICODE)." ", 'info');

            if (!empty($ac_result) && isset($ac_result['result']['data'])) {
                $detailLists['should_delivery_today'] = intval($ac_result['result']['data']);
            }

            //揽件 接口
            $ac = new ApiClient('bi_rpcv2', '', 'dc.get_dc_operation_count', $this->lang);
            $ac->setParams($param);
            $ac_result = $ac->execute();
            $this->logger->write_log("pick today num {$param['store_id']}_{$param['date']}  res ".json_encode($ac_result,
                    JSON_UNESCAPED_UNICODE)." ", 'info');

            if (!empty($ac_result) && isset($ac_result['result']['data'])) {
                $detailLists['should_pickup_today'] = intval($ac_result['result']['data']);
            }

            //当天 仓管 出勤率
            $rateJobs                 = [
                enums::$job_title['dc_officer'],
                enums::$job_title['assistant_branch_supervisor'],
            ];
            $rate_data                = $this->attendance_rate($apply_info['date_at'], $rateJobs,
                $staff_info['sys_store_id']);
            $store_job_attendance_num = 0;//对应日期、所属网点、指定职位的[出勤人数]
            $attendance_rate          = "0/0";//出勤率
            if (!empty($rate_data)) {
                $att_num         = $store_job_attendance_num = count($rate_data['all_num']); //对应网点 职位 出勤人数
                $staff_num       = count($rate_data['in_staff']) + count($rate_data['out_staff']);
                $rate            = empty($staff_num) ? 0 : round($att_num / $staff_num, 2) * 100;
                $attendance_rate = sprintf("%d/%d(%d%%)", $att_num, $staff_num, $rate);
            }
            $detailLists['attendance_rate'] = $attendance_rate;//出勤率

            //当天 仓管 人效 实时调用接口 取前面三个的数据计算 审批通过以后 固化  所在网点当天应派包裹量+所在网点当天揽件包裹量）/实际DC Officer出勤人数
            $detailLists['dc_today_effect'] = '0 '.$this->getTranslation()->_('rate_unit');
            if ($store_job_attendance_num > 0) {
                $detailLists['dc_today_effect'] = ($detailLists['should_delivery_today'] + $detailLists['should_pickup_today']) / $store_job_attendance_num;
                $detailLists['dc_today_effect'] = round($detailLists['dc_today_effect'],
                        1).' '.$this->getTranslation()->_('rate_unit');
            }
        }

        //以前就有的 数据
        $detailLists['ot_detail_1'] = ($references['all_effective_num'] ?? 0).' '.$this->getTranslation()->_('ot_detail_4');//上周所有网点平均工作效率
        $detailLists['ot_detail_2'] = ($references['store_effective_num'] ?? 0).' '.$this->getTranslation()->_('ot_detail_4');//上周所在网点平均工作效率

        //当月 累计ot 时长
        $type_key    = HrOvertimeModel::$th_ot_key;
        $ot_type_key = $type_key[$apply_info['type']] ?? 'invalid_ot_type';
        //累计时长
        $detailLists[$ot_type_key] = ($references['duration'] ?? 0)."h";

        if ($apply_info['type'] == 1) { //1。5倍工资 才显示
            //只有 dc officer 展示 新加的 ass branch supervisor 不展示
            $showJobTitles = array(enums::$job_title['dc_officer']);
            //1。5倍 新增 显示字段 所在网点 所有dc ot 预算剩余
            if (isset($references['dc_left_hours']) && $references['sys_department_id'] == $param['networkManagementId']
            && in_array($staff_info['job_title'],$showJobTitles)
            )//只有泰国 才有这个key 新增需求 只有 nw 部门才展示 把bulky 去掉了 所以要判断下部门 防止历史数据 展示出来
            {
                $detailLists['dc_left_hours'] = ($references['dc_left_hours'] ?? 0)."h";
            }
        }
        if ($apply_info['type'] == 4) { //1倍工资 才显示
            //1倍 新增显示 是否爆仓和 剩余件数
            if (isset($references['is_boom'])) {//是否爆仓字段
                $detailLists['dc_situation'] = $this->getTranslation()->_('is_not_boom');//没爆
                if ($references['is_boom'] == 1) {
                    //pick_left 剩余件
                    $replace_text                = ['pick_left' => $references['pick_left'] ?? 0];
                    $detailLists['dc_situation'] = $this->getTranslation()->_('is_boom', $replace_text);
                }
            }
        }
        return $detailLists;
    }


    /**
     * @description 获取指定网点、指定日期、指定职位的人的出勤数据
     * @param string $date 统计日期
     * @param array $job_titles 统计指定职位
     * @param string $store_id 统计网点
     * @return array  all_num 所有打卡员工 in_staff 所属网点内职位员工总数 out_staff 外来打卡人数
     */
    public function attendance_rate($date, $job_titles, $store_id)
    {
        if (empty($job_titles) || empty($store_id)) {
            return [];
        }
        //总部员工统计不了
        if($store_id == enums::HEAD_OFFICE_ID){
            return [];
        }
        //所属网点的 快递员职位的 人数 分母1
        $store_courier_data = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => "sys_store_id = :store_id: and state = 1 and formal = 1 and is_sub_staff = 0 and job_title in ({courier_job:array})",
            'bind'       => [
                'store_id'    => $store_id,
                'courier_job' => $job_titles,
            ],
        ])->toArray();
        //所属网点的 所有在职 快递员 包括外协
        $store_courier_staff = empty($store_courier_data) ? [] : array_column($store_courier_data, 'staff_info_id');


        //出勤率 : 所有出勤人数 / 快递员总人数（所属人+外来人） 不包含 子账号(把支援的重复数据 排除掉)
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->join(StaffWorkAttendanceModel::class, 's.staff_info_id = a.staff_info_id', 'a');
        $builder->columns('a.staff_info_id');
        $builder->andWhere('a.attendance_date = :date:', ['date' => $date]);
        $builder->andWhere('started_store_id = :store_id: or end_store_id = :store_id:', ['store_id' => $store_id]);
        $builder->inWhere('a.job_title', $job_titles);
        $builder->andWhere('s.is_sub_staff = 0');
        $all_attendance_data = $builder->getQuery()->execute()->toArray();

        $out_staff = [];
        if (!empty($all_attendance_data)) {
            //分子
            $all_attendance_data = array_column($all_attendance_data, 'staff_info_id');
            //外来打卡人员 分母2
            $out_staff = array_values(array_diff($all_attendance_data, $store_courier_staff));
        }

        $return = ['all_num' => $all_attendance_data, 'in_staff' => $store_courier_staff, 'out_staff' => $out_staff];
        $this->logger->write_log("attendance_rate {$date} {$store_id} ".json_encode($return), 'info');
        return $return;
    }

    //网点有几个 dc  记录 工号 改造 为定制职位
    public function get_dc_num($store_id,$jobs = array()){
        $job_id = enums::$job_title['dc_officer'];
        if (empty($jobs)) {
            $jobs = [$job_id];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.staff_info_id');
        $builder->from(['t' => SysStoreModel::class]);
        $builder->join(HrStaffInfoModel::class, "s.sys_store_id = t.id", 's');
        $builder->andWhere('t.id = :store_id:', ['store_id' => $store_id]);
        $builder->andWhere('s.job_title in ({ids:array})', ['ids' => $jobs]);
        $builder->andWhere('s.formal = 1 and s.state = 1 and is_sub_staff = 0');
        $data = $builder->getQuery()->execute()->toArray();
        $this->logger->write_log("dc_officer_num {$store_id} ".json_encode($data), 'info');

        if (!empty($data)) {
            return array_column($data, 'staff_info_id');
        }

        return [];
    }

    //外层的 弄一个 以防万一
    public function getReference($staffId, $param){
        return array([],[]);
    }


    /**
     * @description 发送json-rpc请求
     * @param array $requestParameter 请求参数
     * @param string $method 方法名
     * @param string 项目地址
     * @return mixed'
     */
    public function sendRequest(array $requestParameter, string $method, $client = 'bi_rpcv2')
    {
        $ac = new ApiClient($client, '', $method, $this->lang);
        $ac->setParams($requestParameter);
        $ac_result = $ac->execute();
        $this->logger->write_log(sprintf("request parameters: %s, result: %s",
            json_encode($requestParameter, JSON_UNESCAPED_UNICODE), json_encode($ac_result, JSON_UNESCAPED_UNICODE)),
            'info');

        if (!empty($ac_result) && isset($ac_result['result']['data'])) {
            return $ac_result['result']['data'];
        }
        return "";
    }

    //fle http 调用
    public function sendFleRequest($uri, $param)
    {
        $url = $this->config->api->java_pms_url;
        $url .= $uri;
        $res = $this->httpPost($url, $param);
        $this->logger->write_log("sendFleRequest {$url}{$uri} ".json_encode($param).json_encode($res), 'info');
        if (!empty($res['code']) && $res['code'] == 1) {
            return $res['data'];
        }
        return 0;
    }

    //工资条 查看 有效无效ot 的列表用
    public function calculateForSalary($param)
    {
        $month         = $param['salary_cycle'];
        $payrollServer = new PayrollServer($this->lang);
        $payrollServer = Tools::reBuildCountryInstance($payrollServer);

        //获取发薪周期 每个国家不一样
        [$startDate, $endDate] = $payrollServer->formatSalaryDate($month);

        $otData = $this->getOtBetween($param['user_info']['id'], $startDate, $endDate, $param['salary_state'] ?? 0);

        if (empty($otData)) {
            return [];
        }
        $otServer = new OvertimeServer($this->lang,$this->timezone);
        $ot_server = Tools::reBuildCountryInstance($otServer, [$this->lang, $this->timezone]);
        $this->holidayTypes = $ot_server->getHourOverType();
        $data      = $ot_server->getAllOtType();
        //加班类型翻译
        $this->typeBook = array_column($data, 'msg', 'code');
        //加班倍数翻译
        $this->timeTypeBook = array_column($data, 'sub_msg', 'code');

        $return = [];
        foreach ($otData as $ot) {
            $row['date_at']           = $ot['date_at'];
            $row['salary_state_text'] = $ot['salary_state'] == HrOvertimeModel::SALARY_STATE_EFFECTIVE ? $this->getTranslation()->_('effective') : $this->getTranslation()->_('invalid') ;//有效 无效
            $row['salary_state'] = $ot['salary_state'] == HrOvertimeModel::SALARY_STATE_EFFECTIVE ? $ot['salary_state'] : HrOvertimeModel::SALARY_STATE_INVALID;
            $row['item']              = $this->formatSalaryOt($ot);
            $return[]                 = $row;
        }

        return $return;
    }


    //获取时间区间内 单个员工 加班时间 班次时间 打卡时间  马来不一样(产品说只有一次班 不管2次班情况)
    public function getOtBetween($staffId, $startDate, $endDate, $salaryState = 0)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['o' => HrOvertimeModel::class]);
        $builder->leftJoin(StaffWorkAttendanceModel::class,
            'o.staff_id = a.staff_info_id and o.date_at = a.attendance_date', 'a');
        $builder->columns('o.staff_id,o.date_at,o.type,o.start_time,o.end_time,o.state,o.salary_state,o.duration, a.shift_start,a.shift_end,a.started_at,a.end_at');
        $builder->betweenWhere('o.date_at', $startDate, $endDate);
        $builder->andWhere('o.staff_id = :staff_id:', ['staff_id' => $staffId]);
        $builder->inWhere('o.state ',
            [enums::$audit_status['approved'], enums::$audit_status['dismissed'], enums::$audit_status['timedout']]);

        if (!empty($salaryState)) {
            //有效ot
            if ($salaryState == HrOvertimeModel::SALARY_STATE_EFFECTIVE) {
                $builder->andWhere('o.salary_state = :salary_state:',
                    ['salary_state' => HrOvertimeModel::SALARY_STATE_EFFECTIVE]);
            }
            //无效的
            if ($salaryState == HrOvertimeModel::SALARY_STATE_INVALID) {
                $builder->andWhere('o.salary_state != :salary_state:',
                    ['salary_state' => HrOvertimeModel::SALARY_STATE_EFFECTIVE]);
            }
        }
        //加班日期
        $builder->orderby('o.date_at');

        $data = $builder->getQuery()->execute()->toArray();
        return $data;
    }

    //key =》 value 格式
    public function formatSalaryOt($info)
    {
        $t         = $this->getTranslation();
        $nextDay   = $t->_('next_day');
        $yesterday = $t->_('yesterday');
        $addHour   = $this->config->application->add_hour;

        //--------------加班类型
        $item['label'] = $t->_('OT_type');//翻译
        $item['value'] = $this->typeBook[$info['type']];
        if (!empty($this->timeTypeBook[$info['type']])) {
            $item['value'] .= "({$this->timeTypeBook[$info['type']]})";
        }
        $data[] = $item;

        //----------------班次
        $item['label'] = $t->_('shift_time');//翻译
        //如果没打卡 需要取对应班次 middle
        if (empty($info['started_at']) && empty($info['end_at'])) {
            if (isCountry('MY')) {
                $server              = new AttendanceCalendarV2Server($this->lang, $this->timezone);
                $shiftInfo           = $server->getDailyShiftInfo($info['staff_id'], $info['date_at'], $info['date_at']);
                $info['shift_start'] = empty($shiftInfo) ? '' : $shiftInfo[$info['date_at']]['first_start'];
                $info['shift_end']   = empty($shiftInfo) ? '' : $shiftInfo[$info['date_at']]['first_end'];
            } else {
                $shiftInfo           = HrStaffShiftMiddleDateModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and shift_date = :date_at:',
                    'bind'       => ['staff_id' => $info['staff_id'], 'date_at' => $info['date_at']],
                ]);
                $info['shift_start'] = empty($shiftInfo) ? '' : $shiftInfo->shift_start;
                $info['shift_end']   = empty($shiftInfo) ? '' : $shiftInfo->shift_end;
            }
        }

        $item['value'] = "{$info['shift_start']}-{$info['shift_end']}";
        //跨天
        if ($info['shift_start'] > $info['shift_end']) {
            $item['value'] = "{$info['shift_start']}-{$nextDay}{$info['shift_end']}";
        }
        $data[] = $item;

        //------------------ot 区间
        $item['label'] = $t->_('ot_time_between');//申请ot 时间
        $otStart       = date('H:i', strtotime($info['start_time']));
        $otEnd         = date('H:i', strtotime($info['end_time']));
        $duration      = $info['duration'];
        if ($info['duration'] >= 8 && in_array($info['type'],$this->holidayTypes)) {
            $duration += 1;
            $otEnd    = date('H:i', strtotime("{$info['end_time']} +1 hour"));
        }

        $item['value'] = "{$otStart}-{$otEnd}";
        if ($otStart > $otEnd) {
            $item['value'] = "{$otStart}-{$nextDay}{$otEnd}";
        }
        $hour          = floatval($info['duration']);
        $item['value'] .= "({$hour}h)";
        $data[]        = $item;

        //---------------打卡时间
        $item['label'] = $t->_('attendance_time');
        $startCard     = empty($info['started_at']) ? '' : date('H:i', strtotime($info['started_at']) + ($addHour * 3600));
        $endCard       = empty($info['end_at']) ? '' : date('H:i', strtotime($info['end_at']) + ($addHour * 3600));
        if (empty($startCard)) {
            $startCard = $t->_('attendance_miss');
        }
        if (empty($endCard)) {
            $endCard = $t->_('attendance_miss');
        }

        $item['value'] = "{$startCard}-{$endCard}";
        if (!empty($info['started_at']) && !empty($info['end_at']) && $startCard > $endCard) {
            $item['value'] = "{$startCard}-{$nextDay}{$endCard}";
        }
        //判断昨天 就没有次日了
        if(!empty($info['started_at'])){
            $realStart = date('Y-m-d', strtotime($info['started_at']) + ($addHour * 3600));
            if($info['date_at'] > $realStart){
                $item['value'] = "{$yesterday}{$startCard}-{$endCard}";
            }
        }

        $data[] = $item;

        //有效ot  没有原因
        if ($info['salary_state'] == HrOvertimeModel::SALARY_STATE_EFFECTIVE) {
            return $data;
        }

        //---------------------无效原因
        $item['label'] = $t->_('audit_leave_reason');
        $invalidReason = HrOvertimeModel::$salary_ot_reason;
        $reasonKey     = $invalidReason[$info['salary_state']] ?? '';
        $item['value'] = $t->_($reasonKey);
        //有变量的翻译
        if ($info['salary_state'] == HrOvertimeModel::SALARY_STATE_INVALID_TIME) {
            $item['value'] = $t->_($reasonKey, ['duration' => $duration]);
        }
        if ($info['salary_state'] == HrOvertimeModel::SALARY_STATE_MISS_CARD) {
            if (empty($info['started_at']) && empty($info['end_at'])) {
                $item['value'] = $t->_($reasonKey, ['card' => $t->_('att_start_end')]);
            } elseif (empty($info['started_at'])) {
                $item['value'] = $t->_($reasonKey, ['card' => $t->_('att_start')]);
            } else {
                $item['value'] = $t->_($reasonKey, ['card' => $t->_('att_end')]);
            }
        }

        $data[] = $item;

        return $data;
    }



}