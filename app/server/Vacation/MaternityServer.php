<?php
/**
 * 跨国探亲假
 * Created by PhpStorm.
 * User: nick
 * Date: 4/10/23
 * Time: 8:34 PM
 */


namespace FlashExpress\bi\App\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveProperty;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Server\VacationServer;

class MaternityServer extends VacationServer implements LeaveInterface
{

    private static $instance = null;

    public $today;

    public $thisYear;


    //任务 每年初始化调用
    public static function getInstance($lang, $timezone)
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self($lang, $timezone);
        }
        return self::$instance;
    }

    //申请 保存入口
    public function handleCreate($param)
    {
        //初始化数据
        $this->initData($param);

        //逻辑验证
        $this->businessCheck();

        //format 数据
        $this->dataFormat();

        //保存
        $this->dataSave();

        return $this->auditId;
    }


    //额度查询 入口
    public function handleSearch($param)
    {
        //初始化数据
        $this->initSearch($param);

        $this->getLimitDays();

        //整理成 day_sub  day_limit
        return $this->formatLimitDays();
    }

    //查询额度 初始化
    public function initSearch($param)
    {
        parent::initSearch($param);
        $this->today    = date('Y-m-d');
        $this->thisYear = date('Y');
        if (!empty($param['today'])) {
            $this->today    = $param['today'];
            $this->thisYear = date('Y', strtotime($this->today));
        }
    }


    //构造数据
    protected function initData($param)
    {
        parent::initData($param);
        $this->today    = date('Y-m-d');
        $this->thisYear = date('Y');

        if (!empty($param['today'])) {
            $this->today    = $param['today'];
            $this->thisYear = date('Y', strtotime($this->today));
        }

        //获取额度
        $this->getLimitDays();
    }


    //逻辑验证 通过后 保存相关数据表
    public function businessCheck()
    {
        //公共验证逻辑
        $this->publicValidate();
        //没权限 返回异常
        if (!$this->applyPermission()) {
            throw new ValidationException($this->getTranslation()->_('2107'));
        }

        //验证时间区间 分流各个国家
        $this->timeCheck();

        //验证自类型 分流 只有菲律宾老挝越南有
        $this->subTypeCheck();


        //没转正 不让申请 验证
        if (!empty($this->leaveProperty->is_forbidden)) {
            if (!$this->hireCheck()) {
                $message = str_replace('leave_type', $this->getTranslation()->_($this->leaveProperty->language_key),
                    $this->getTranslation()->_('probation_limit'));
                throw new ValidationException($message);
            }
            if (!empty($this->staffInfo['formal_at']) && $this->paramModel['leave_start_time'] < $this->staffInfo['formal_at']) {
                $message = $this->getTranslation()->_('probation_before_limit');
                throw new ValidationException($message);
            }
        }

        //没有额度记录 异常数据验证
        if (empty($this->limitDays) || !isset($this->limitDays['limit'])) {
            $this->logger->write_log("额度异常 {$this->staffInfo['staff_info_id']} ".json_encode($this->limitDays),
                'info');
            throw new ValidationException($this->getTranslation()->_('leave_limit'));
        }

        $applyDays = $this->thisNum + $this->nextNum;//申请总天数
        //说明跳过休息日之后 用0天
        if (empty($applyDays)) {
            throw new ValidationException('day off for the apply date');
        }

        $this->applyCheck();

        //申请天数 是否够用
        if ($this->limitDays['sub'] < ($this->thisNum + $this->nextNum)) {
            throw new ValidationException($this->getTranslation()->_('leave_notice_at_most',
                ['days' => $this->limitDays['sub']]));
        }

        return true;
    }

    //验证申请日期 各国不一样 印尼 泰国 菲律宾 限制开始时间当天之后 越南 -3  老挝 -2 马来 没限制
    public function timeCheck()
    {
        //请假日期必须大于等于当前日期
        if (empty($this->paramModel['is_bi'])) {
            $this->timeValidate(0, 100, '1018');
        }
    }

    //部分国家产假 要选择子类型 必选项
    public function subTypeCheck()
    {
        return true;
    }

    //一年申请一次 只有印尼不限制
    public function applyCheck()
    {
        $create_year = date('Y');
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('sum(if(s.type=0,1,0.5)) as num');
        $builder->from(['s' => StaffAuditLeaveSplitModel::class]);
        $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
        $builder->andWhere("s.staff_info_id = :staff_id:",['staff_id' => $this->staffInfo['staff_info_id']]);
        $builder->andWhere('a.leave_type = :leave_type:',['leave_type' => enums::LEAVE_TYPE_4]);
        $builder->andWhere('a.audit_type = 2');
        $builder->inWhere('a.status',array(1,2));
        $builder->andWhere('s.year_at = :year_at:', ['year_at' => $create_year]);
        $info = $builder->getQuery()->execute()->getFirst();
        $applyDays = empty($info) ? 0 : $info->num;
        if (!empty($applyDays)) {
            throw new ValidationException($this->getTranslation()->_('leave_limit'));
        }
        return true;
    }

    //整理数据 数据结构 audit, split, img
    protected function dataFormat()
    {
        //先保存 主表 拿audit id
        $this->saveAudit();

        //拆分表
        $this->formatSplitData();
    }


    //by 或者 hcm 显示额度 整理
    protected function formatLimitDays()
    {
        $dayLimit            = half_num($this->limitDays['limit']);
        $daySub              = half_num($this->limitDays['sub']);
        $return              = [];
        $return['day_limit'] = "{$dayLimit}";//总额度
        $return['day_sub']   = "{$daySub}";//剩余额度 有可能是负数
        $this->getDI()->get('logger')->write_log(['formatLimitDays' => $return], 'info');
        return $return;
    }


    //整理 扣减额度 标记 对应周期
    protected function formatSplitData()
    {
        // 拼接 拆分表 归属年 year_at 字段
        foreach ($this->leaveSplitInfo as $k => $in) {
            $this->leaveSplitInfo[$k]['year_at']  = $this->thisYear;//初始化当年
            $this->leaveSplitInfo[$k]['audit_id'] = $this->auditId;
        }
    }

    //男性不能申请
    public function applyPermission()
    {
        //申请权限  正式员工、月/日/时薪制合同工
        if ($this->staffInfo['formal'] != HrStaffInfoModel::FORMAL_1 || !in_array($this->staffInfo['hire_type'], [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
                HrStaffInfoModel::HIRE_TYPE_3,
                HrStaffInfoModel::HIRE_TYPE_4,
            ])) {
            return false;
        }

        //女性
        if ($this->staffInfo['sex'] != HrStaffInfoModel::SEX_FEMALE) {
            return false;
        }
        return true;
    }


    //获取 额度 印尼 老挝 越南 试用期不能申请 其他可以
    public function getLimitDays()
    {
        //获取额度
        $return['limit'] = $return['sub'] = 0.0;

        //没有权限
        if (!$this->applyPermission()) {
            return;
        }
        //没转正
        if (!empty($this->leaveProperty->is_forbidden) && !$this->hireCheck()) {
            return;
        }

        //获取额度
        $this_year = date('Y', strtotime($this->today));
        $info      = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year in ({year_at:array}) and leave_type = :leave_type:',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'year_at'    => [$this_year],
                'leave_type' => enums::LEAVE_TYPE_4,
            ],
        ]);
        //初始化一条记录
        if (empty($info)) {
            [$return['limit'], $return['sub']] = $this->initLeaveDays();
            //然后再获取对象 子类型操作用
            $info = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and year in ({year_at:array}) and leave_type = :leave_type:',
                'bind'       => [
                    'staff_id'   => $this->staffInfo['staff_info_id'],
                    'year_at'    => [$this_year],
                    'leave_type' => enums::LEAVE_TYPE_4,
                ],
            ]);
        } else {
            $return['limit'] = $info->freeze_days;
            $return['sub']   = $info->days;
        }

        //额外 根据子类型 动态增减额度 定制的有老挝 越南 菲律宾
        $addDays = 0;
        if (!empty($this->paramModel['sub_type'])) {
            $addDays           = $this->getAddDays();
            $info->freeze_days = $info->freeze_days + $addDays;
            $info->days        = $info->days + $addDays;
            $info->update();
        }
        $return['sub']   += $addDays;
        $return['limit'] += $addDays;
        $this->limitDays = $return;
    }

    //初始化一条记录
    protected function initLeaveDays()
    {
        $year                    = $this->thisYear ?? date('Y');
        $days                    = $this->leaveProperty->days;
        $insert['staff_info_id'] = $this->staffInfo['staff_info_id'];
        $insert['leave_type']    = enums::LEAVE_TYPE_4;
        $insert['year']          = $year;
        $insert['task_date']     = date('Y-m-d');
        $insert['freeze_days']   = $days;
        $insert['days']          = $days;
        $insert['leave_days']    = 0;

        $model = new StaffLeaveRemainDaysModel();
        $model->create($insert);
        $return['limit'] = $return['sub'] = half_num($days);
        return [$return['limit'], $return['sub']];
    }


    //获取额外天数
    public function getAddDays()
    {
        return 0;
    }

    //保存
    public function dataSave()
    {
        //保存 申请 上传图片
        $this->saveImgData();
        //保存 拆分表记录
        $this->saveSplitData();
        //额度更新 当前周期 和上个周期
        $this->saveRemainData();
    }


    //申请操作 额度表 扣除带薪病假的
    protected function saveRemainData()
    {
        $Remain = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_4,
                'year_at'    => $this->thisYear,
            ],
        ]);

        $Remain->days       = $Remain->days - ($this->thisNum + $this->nextNum);
        $Remain->leave_days = $Remain->leave_days + ($this->thisNum + $this->nextNum);
        $Remain->update();
    }


    //撤销返还
    public function returnRemainDays($auditId, $staffInfo, $extend = [])
    {
        $this->staffInfo = $staffInfo;

        //获取应有额度
        $this->leaveProperty = StaffLeaveProperty::findFirst([
            'conditions' => "leave_type = :leave_type: and is_delete = 0",
            'bind'       => ['leave_type' => enums::LEAVE_TYPE_4],
        ]);

        $splitInfo = $this->getUsedDays($auditId);

        //没有拆分表额度信息
        if (empty($splitInfo)) {
            $this->logger->write_log("拆分表信息异常 {$this->staffInfo['staff_info_id']} {$auditId}", 'info');
            return true;
        }

        $remainData = StaffLeaveRemainDaysModel::find([
            'column'     => 'staff_info_id,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycle:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_4,
                'cycle'      => array_keys($splitInfo),
            ],
        ]);


        if (empty($remainData->toArray())) {
            $this->logger->write_log("额度表信息异常 {$this->staffInfo['staff_info_id']} {$auditId} ".json_encode($splitInfo),
                'info');
            return true;
        }

        foreach ($remainData as $remain) {
            //使用额度 减少
            $remain->freeze_days = $this->leaveProperty->days;
            $remain->days        = $this->leaveProperty->days;
            $remain->leave_days  = 0;
            $remain->update();
        }
        $this->logger->write_log("额度返还成功 {$this->staffInfo['staff_info_id']} {$auditId} ".json_encode($splitInfo),
            'info');
    }


    //每年1号 初始化任务用
    public function conditionsStr()
    {
        $conditions = 'formal = :formal: and hire_type in ({hire_type:array}) and is_sub_staff = :is_sub_staff: and sex = :sex:';
        $bind       = [
            'formal'       => HrStaffInfoModel::FORMAL_1,
            'hire_type'    => [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_1,
            ],
            'is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0,
            'sex'          => HrStaffInfoModel::SEX_FEMALE,
        ];

        return [$conditions, $bind];
    }
    //每年初始化额度任务调用
    public function taskInitialize($staffInfo)
    {
        $this->staffInfo = $staffInfo;

        //跑数据
        $this->initLeaveDays();
    }

    //上线跑数据到 remain表 只跑一次
    public function initOne($staffInfo)
    {
        $this->staffInfo = $staffInfo;
        //权限
        if (!$this->applyPermission()) {
            return;
        }

        //获取应有额度
        $this->leaveProperty = StaffLeaveProperty::findFirst([
            'conditions' => "leave_type = :leave_type: and is_delete = 0",
            'bind'       => ['leave_type' => enums::LEAVE_TYPE_4],
        ]);

        $year = date('Y');
        //查一下有没有数据
        $info = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
            'bind'       => [
                'staff_id'   => $staffInfo['staff_info_id'],
                'year_at'    => $year,
                'leave_type' => enums::LEAVE_TYPE_4,
            ],
        ]);
        if (!empty($info)) {
            return;
        }

        //获取已经申请的额度
        $start = date('Y-01-01 00:00:00');
        //转 零时区 产假是以创建时间 作为扣减额度
        $addHour = $this->config->application->add_hour;
        $start   = date('Y-m-d H:i:s', strtotime($start) - ($addHour * 3600));
        //除了印尼 其他国家 应该只有一条 一年只能申请一次
        $apply_info = StaffAuditModel::find([
            'columns'    => 'audit_id,leave_day,template_comment',
            'conditions' => 'staff_info_id = :staff_id: and audit_type = :audit_type: 
                            and leave_type = :leave_type: and status in (1,2) and created_at >= :date_time:',
            'bind'       => [
                'staff_id'   => $staffInfo['staff_info_id'],
                'audit_type' => StaffAuditModel::AUDIT_TYPE_LEAVE,
                'leave_type' => enums::LEAVE_TYPE_4,
                'date_time'  => $start,
            ],
        ])->toArray();

        if (empty($apply_info)) {
            //没申请过 初始化数据就行
            $this->initLeaveDays();
            return true;
        }

        $addDays = 0;//定制国家 需要额外增加的天数 菲律宾 老挝 越南
        $applied = 0;//申请过的天数
        $subType = 0;//自类型
        foreach ($apply_info as $apply) {
            $applied += $apply['leave_day'];
            if (!empty($apply['template_comment'])) {
                $comment = json_decode($apply['template_comment'], true);
                $subType = $comment['leave_4_key'];
            }
        }

        if (!empty($subType)) {
            $this->paramModel['sub_type'] = $subType;
            $addDays                      = $this->getAddDays();
        }

        //申请过 要计算额度
        $days                    = $this->leaveProperty->days;
        $insert['staff_info_id'] = $this->staffInfo['staff_info_id'];
        $insert['leave_type']    = enums::LEAVE_TYPE_4;
        $insert['year']          = $year;
        $insert['task_date']     = date('Y-m-d');
        $insert['freeze_days']   = $days + $addDays;
        $insert['days']          = $days + $addDays - $applied;
        $insert['leave_days']    = $applied;

        $model = new StaffLeaveRemainDaysModel();
        $flag  = $model->create($insert);
        return $flag;
    }

    //菲律宾 老挝 越南 产假 有特殊详情页展示字段
    public function formatDetailInfo($subType){
        return [];
    }


}