<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 3/6/23
 * Time: 10:46 AM
 */


namespace FlashExpress\bi\App\Server\Vacation;

use FlashExpress\bi\App\Server\VacationServer;

class TrainingServer extends VacationServer
{
    //培训假 验证逻辑 不验证 false； 验证 true； https://flashexpress.feishu.cn/docx/XxJqdb3feoZeffxDHQAcbnLhnIf
    public function checkTrainingLeave()
    {
        //工具不验证
        if (!empty($this->paramModel['is_bi'])) {
            return false;
        }
        $leaveDate = date('Y-m-d', strtotime($this->paramModel['leave_start_time']));
        $hireDate  = date('Y-m-d', strtotime($this->staffInfo['hire_date']));
        if ($leaveDate != $hireDate) {
            return true;
        }
        $today     = date('Y-m-d');//01-01
        $limitDate = date('Y-m-d', strtotime("{$hireDate} +3 day"));//01-04 不包含
        //不在入职3天内 不能申请
        if (strtotime($today) >= strtotime($hireDate) && strtotime($today) < strtotime($limitDate)) {
            return false;
        }

        return true;
    }
}