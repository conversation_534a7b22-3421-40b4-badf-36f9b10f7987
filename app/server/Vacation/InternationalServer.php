<?php
/**
 * 跨国探亲假
 * Created by PhpStorm.
 * User: nick
 * Date: 4/10/23
 * Time: 8:34 PM
 */


namespace FlashExpress\bi\App\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\VacationServer;

class InternationalServer extends VacationServer implements LeaveInterface
{

    private static $instance = null;

    public $today;

    public $thisYear;
    public $lastYear;

    //操作扣减额度用
    public $currentApplyDays = 0;      //当前周期 申请天数过期日

    //任务 每年初始化调用
    public static function getInstance($lang, $timezone)
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self($lang, $timezone);
        }
        return self::$instance;
    }

    /**
     * 申请 保存入口
     * @param $param
     * @return mixed
     * @throws ValidationException
     */
    public function handleCreate($param)
    {
        //初始化数据
        $this->initData($param);

        //逻辑验证
        $this->businessCheck();

        //format 数据
        $this->dataFormat();

        //保存
        $this->dataSave();

        return $this->auditId;
    }


    //额度查询 入口
    public function handleSearch($param)
    {
        //初始化数据
        $this->initSearch($param);

        $this->getLimitDays();

        //整理成 day_sub  day_limit
        return $this->formatLimitDays();
    }

    //查询额度 初始化
    public function initSearch($param)
    {
        parent::initSearch($param);
        $this->today    = date('Y-m-d');
        $this->thisYear = date('Y');
        $this->lastYear = date('Y', strtotime('-1 year'));

        if (!empty($param['today'])) {
            $this->today    = $param['today'];
            $this->thisYear = date('Y', strtotime($this->today));
            $this->lastYear = date('Y', strtotime("{$this->today} -1 year"));
        }
    }


    //构造数据
    protected function initData($param)
    {
        parent::initData($param);
        $this->today    = date('Y-m-d');
        $this->thisYear = date('Y');
        $this->lastYear = date('Y', strtotime('-1 year'));

        if (!empty($param['today'])) {
            $this->today    = $param['today'];
            $this->thisYear = date('Y', strtotime($this->today));
            $this->lastYear = date('Y', strtotime("{$this->today} -1 year"));
        }
        //获取额度
        $this->getLimitDays();
    }


    //逻辑验证 通过后 保存相关数据表

    /**
     * @throws ValidationException
     */
    public function businessCheck()
    {
        //公共验证逻辑
        $this->publicValidate();
        //没权限 返回异常
        if (!$this->applyPermission()) {
            throw new ValidationException($this->getTranslation()->_('2107'));
        }

        $this->timeCheck();

        //没转正 不让申请 验证
        if (!empty($this->leaveProperty->is_forbidden)) {
            if (!$this->hireCheck()) {
                $message = str_replace('leave_type', $this->getTranslation()->_($this->leaveProperty->language_key),
                    $this->getTranslation()->_('probation_limit'));
                throw new ValidationException($message);
            }
            if (!empty($this->staffInfo['formal_at']) && $this->paramModel['leave_start_time'] < $this->staffInfo['formal_at']) {
                $message = $this->getTranslation()->_('probation_before_limit');
                throw new ValidationException($message);
            }
        }

        //没有额度记录 异常数据验证
        if (empty($this->limitDays) || !isset($this->limitDays['limit'])) {
            $this->logger->write_log("额度异常 {$this->staffInfo['staff_info_id']} " . json_encode($this->limitDays),
                'info');
            throw new ValidationException($this->getTranslation()->_('leave_limit'));
        }
        // 新需求 请明年非有效期假期不能请
        $nextYear = date('Y', strtotime($this->paramModel['leave_end_time']));
        if (date('Y') < $nextYear) {//失效日期
            throw new ValidationException($this->getTranslation()->_('expiration_date_notice_1231'));
        }
        //请假日期必须小于等于本年12月31日
        $applyDays = $this->thisNum + $this->nextNum;//申请总天数
        //说明跳过休息日之后 用0天
        if (empty($applyDays)) {
            throw new ValidationException('day off for the apply date');
        }
        if ($applyDays > $this->limitDays['sub']) {
            throw new ValidationException($this->getTranslation()->_('leave_limit'));
        }
        return true;
    }

    //除了越南和印尼 其他国家是当天之后
    public function timeCheck()
    {
        //只能申请当前时间之后
        if (empty($this->paramModel['is_bi'])) {
            $this->timeValidate(0, 100, '1018');
        }
    }

    //整理数据 数据结构 audit, split, img
    protected function dataFormat()
    {
        //先保存 主表 拿audit id
        $this->saveAudit();

        //拆分表
        $this->formatSplitData();
    }


    //by 或者 hcm 显示额度 整理
    protected function formatLimitDays()
    {
        $dayLimit = half_num($this->limitDays['limit']);
        $daySub   = half_num($this->limitDays['sub']);
        $return   = [];

        $return['day_limit'] = "{$dayLimit}";//总额度
        $return['day_sub']   = "{$daySub}";  //剩余额度 有可能是负数
        $this->getDI()->get('logger')->write_log(['formatLimitDays' => $return], 'info');
        return $return;
    }


    //整理 扣减额度 标记 对应周期
    protected function formatSplitData()
    {
        // 拼接 拆分表 归属年 year_at 字段
        foreach ($this->leaveSplitInfo as $k => $in) {
            $duration                             = ($in['type'] == StaffAuditLeaveSplitModel::SPLIT_TYPE_0) ? 1 : 0.5;//这天用额 1天 或者 半天
            $this->leaveSplitInfo[$k]['year_at']  = $this->thisYear;                                                   //初始化当年
            $this->leaveSplitInfo[$k]['audit_id'] = $this->auditId;

            //用的都是当年额度
            $this->currentApplyDays += $duration;
        }
    }

    //申请权限 工作国家和国籍不一样的 返回 true  没有权限 返回 false
    public function applyPermission($staff_info = [])
    {
        if (empty($this->staffInfo)) {
            $this->staffInfo = $staff_info;
        }
        //申请权限  正式员工、月/日/时薪制合同工
        if ($this->staffInfo['formal'] != HrStaffInfoModel::FORMAL_1 || !in_array($this->staffInfo['hire_type'], [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
                HrStaffInfoModel::HIRE_TYPE_3,
                HrStaffInfoModel::HIRE_TYPE_4,
            ])) {
            return false;
        }

        //工作所在地判断
        if (empty($this->staffInfo['working_country'])) {
            return false;
        }

        if (empty($this->staffInfo['nationality'])) {
            return false;
        }

        if ($this->staffInfo['working_country'] == $this->staffInfo['nationality']) {
            return false;
        }

        return true;
    }


    //获取 额度
    public function getLimitDays()
    {
        //获取额度
        $this->limitDays['limit'] = $this->limitDays['sub'] = 0.0;

        //没有权限
        if (!$this->applyPermission()) {
            return;
        }
        //没转正不可以申请
        if (!empty($this->leaveProperty->is_forbidden) && !$this->hireCheck()) {
            return;
        }

        //获取额度
        $this_year = date('Y', strtotime($this->today));

        $info = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'year_at'    => $this_year,
                'leave_type' => enums::LEAVE_TYPE_19,
            ],
            //'for_update' => true,
        ]);
        if (empty($info)) {
            [$this->limitDays['limit'], $this->limitDays['sub']] = $this->initInternational($this->staffInfo,
                $this_year);
            return;
        }
        //过期了
        if (!empty($info->expire_date) && $this->today > $info->expire_date) {
            return;
        }
        $this->limitDays['limit'] = $info->freeze_days;
        $this->limitDays['sub']   = $info->days;
    }

    //初始化额度 触发发放规则 或者 任务 1月1号 调用
    public function initInternational($staffInfo, $year)
    {
        $days                    = $staffInfo['job_title_grade_v2'] >= 19 ? 3 : 1;
        $insert['staff_info_id'] = $staffInfo['staff_info_id'];
        $insert['leave_type']    = enums::LEAVE_TYPE_19;
        $insert['year']          = $year;
        $insert['task_date']     = date('Y-m-d');
        $insert['freeze_days']   = $days;
        $insert['days']          = $days;
        $insert['leave_days']    = 0;
        $insert['expire_date']   = $year . '-12-31';
        $model                   = new StaffLeaveRemainDaysModel();
        $model->create($insert);
        $return['limit'] = $return['sub'] = half_num($days);
        return [$return['limit'], $return['sub']];
    }

    //保存
    public function dataSave()
    {
        //保存 申请 上传图片
        $this->saveImgData();
        //保存 拆分表记录
        $this->saveSplitData();
        //额度更新 当前周期 和上个周期
        $this->saveRemainData();
    }


    //申请操作 额度表 扣除带薪病假的
    protected function saveRemainData()
    {
        if (!empty($this->currentApplyDays)) {
            $this->updateRemain($this->thisYear, $this->currentApplyDays);
        }
    }


    /**
     * @param $year
     * @param $days 如果是申请操作 为正数  撤销返还操作 为负数
     *
     */
    protected function updateRemain($year, $days)
    {
        $Remain = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_19,
                'year_at'    => $year,
            ],
        ]);

        $Remain->days       = $Remain->days - $days;
        $Remain->leave_days = $Remain->leave_days + $days;
        $Remain->update();
    }

    //撤销返还
    public function returnRemainDays($auditId, $staffInfo, $extend = [])
    {
        $this->staffInfo = $staffInfo;
        $splitInfo       = $this->getUsedDays($auditId);

        //没有拆分表额度信息
        if (empty($splitInfo)) {
            $this->logger->write_log("拆分表信息异常 {$this->staffInfo['staff_info_id']} {$auditId}", 'info');
            return true;
        }

        $remainData = StaffLeaveRemainDaysModel::find([
            'column'     => 'staff_info_id,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycle:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_19,
                'cycle'      => array_keys($splitInfo),
            ],
        ]);


        if (empty($remainData->toArray())) {
            $this->logger->write_log("额度表信息异常 {$this->staffInfo['staff_info_id']} {$auditId} " . json_encode($splitInfo),
                'info');
            return true;
        }

        foreach ($remainData as $remain) {
            $needBackDays = $splitInfo[$remain->year] ?? 0;//需要返还的 额度
            //使用额度 减少
            $remain->days       += $needBackDays;
            $remain->leave_days -= $needBackDays;
            $remain->update();
        }
        $this->logger->write_log("额度返还成功 {$this->staffInfo['staff_info_id']} {$auditId} " . json_encode($splitInfo),
            'info');
    }

    //每年1号 初始化任务用
    public function conditionsStr(){
        $conditions = 'formal = :formal: and hire_type in ({hire_type:array}) and is_sub_staff = :is_sub_staff:
                        and working_country > 0 and nationality > 0 and working_country != nationality';
        $bind = ['formal'       => HrStaffInfoModel::FORMAL_1,
                 'hire_type'    => [
                     HrStaffInfoModel::HIRE_TYPE_1,
                     HrStaffInfoModel::HIRE_TYPE_1,
                     HrStaffInfoModel::HIRE_TYPE_1,
                     HrStaffInfoModel::HIRE_TYPE_1,
                 ],
                 'is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0,
        ];

        return [$conditions, $bind];
    }

    //每年初始化额度任务调用
    public function taskInitialize($staffInfo)
    {
        $this->staffInfo = $staffInfo;
        $year = date('Y');
        //跑数据
        $this->initInternational($staffInfo, $year);
    }


}