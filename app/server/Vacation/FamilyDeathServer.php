<?php
/**
 * 家人去世假
 * @description: 家人去世假业务逻辑处理
 * @author: AI
 * @date: <?php echo date('Y-m-d H:i:s'); ?>
 */

namespace FlashExpress\bi\App\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\VacationServer;

class FamilyDeathServer extends VacationServer implements LeaveInterface
{
    private static $instance = null;

    public $today;
    public $thisYear;
    public $lastYear;

    // 操作扣减额度用
    public $currentApplyDays = 0; // 当前周期申请天数

    /**
     * 单例模式获取实例
     * @param string $lang 语言
     * @param string $timezone 时区
     * @return FamilyDeathServer
     */
    public static function getInstance($lang, $timezone)
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self($lang, $timezone);
        }
        return self::$instance;
    }

    /**
     * 申请保存入口
     * @param array $param 申请参数
     * @return mixed 审核ID
     * @throws ValidationException
     */
    public function handleCreate($param)
    {
        // 初始化数据
        $this->initData($param);

        // 逻辑验证
        $this->businessCheck();

        // format 数据
        $this->dataFormat();

        // 保存
        $this->dataSave();

        return $this->auditId;
    }

    /**
     * 额度查询入口
     * @param array $param 查询参数
     * @return array 额度信息
     */
    public function handleSearch($param)
    {
        // 初始化数据
        $this->initSearch($param);

        $this->getLimitDays();

        // 整理成 day_sub day_limit
        return $this->formatLimitDays();
    }

    /**
     * 查询额度初始化
     * @param array $param 参数
     */
    public function initSearch($param)
    {
        parent::initSearch($param);
        $this->today    = date('Y-m-d');
        $this->thisYear = date('Y');
        $this->lastYear = date('Y', strtotime('-1 year'));

        if (!empty($param['today'])) {
            $this->today    = $param['today'];
            $this->thisYear = date('Y', strtotime($this->today));
            $this->lastYear = date('Y', strtotime("{$this->today} -1 year"));
        }
    }

    /**
     * 构造数据
     * @param array $param 参数
     */
    protected function initData($param)
    {
        // 将sub_leave_type映射为sub_type，以便父类处理
        if (!empty($param['sub_leave_type'])) {
            $param['sub_type'] = $param['sub_leave_type'];
        }

        parent::initData($param);
        $this->today    = date('Y-m-d');
        $this->thisYear = date('Y');
        $this->lastYear = date('Y', strtotime('-1 year'));

        if (!empty($param['today'])) {
            $this->today    = $param['today'];
            $this->thisYear = date('Y', strtotime($this->today));
            $this->lastYear = date('Y', strtotime("{$this->today} -1 year"));
        }
        // 获取额度 - 家人去世假不需要年度额度管理
        $this->getLimitDays();
    }

    /**
     * 逻辑验证 通过后保存相关数据表
     * @throws ValidationException
     */
    public function businessCheck()
    {
        // 公共验证逻辑
        $this->publicValidate();

        // 没权限返回异常
        if (!$this->applyPermission()) {
            throw new ValidationException($this->getTranslation()->_('2107'));
        }

        $this->timeCheck();

        // 验证子类型
        $this->validateSubType();

        // 没转正不让申请验证
        if (!empty($this->leaveProperty->is_forbidden)) {
            if (!$this->hireCheck()) {
                $message = str_replace('leave_type', $this->getTranslation()->_($this->leaveProperty->language_key),
                    $this->getTranslation()->_('probation_limit'));
                throw new ValidationException($message);
            }
            if (!empty($this->staffInfo['formal_at']) && $this->paramModel['leave_start_time'] < $this->staffInfo['formal_at']) {
                $message = $this->getTranslation()->_('probation_before_limit');
                throw new ValidationException($message);
            }
        }

        // 申请日期验证
        $applyDays = $this->thisNum + $this->nextNum; // 申请总天数

        // 说明跳过休息日之后用0天
        if (empty($applyDays)) {
            throw new ValidationException('day off for the apply date');
        }

        // 最多申请3天
        if ($applyDays > 3) {
            throw new ValidationException($this->getTranslation()->_('22328_max_days'));
        }

        return true;
    }

    /**
     * 时间检查
     */
    public function timeCheck()
    {
        // 只能申请 -7天
        if (empty($this->paramModel['is_bi'])) {
            $this->timeValidate(-7, 100, '1018');
        }
    }

    /**
     * 验证子类型和申请次数限制
     * @throws ValidationException
     */
    protected function validateSubType()
    {
        // 验证子类型是否存在
        if (empty($this->paramModel['sub_type'])) {
            throw new ValidationException($this->getTranslation()->_('22328_sub_type_required'));
        }

        $subType       = $this->paramModel['sub_type'];
        $validSubTypes = array_keys(StaffLeaveRemainDaysModel::getSubTypeNames());

        if (!in_array($subType, $validSubTypes)) {
            throw new ValidationException($this->getTranslation()->_('22328_invalid_sub_type'));
        }

        // 检查是否为只能申请一次的类型
        $onceOnlyTypes = StaffLeaveRemainDaysModel::getOnceOnlySubTypes();
        if (in_array($subType, $onceOnlyTypes)) {
            $this->checkOnceOnlySubType($subType);
        }
    }

    /**
     * 检查只能申请一次的子类型
     * @param int $subType 子类型
     * @throws ValidationException
     */
    protected function checkOnceOnlySubType($subType)
    {
        // 查询是否已经申请过该子类型的假期（审批状态为已通过或待审批）
        $existingRecord = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and sub_leave_type = :sub_type:',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_7,
                'sub_type'   => $subType,
            ],
        ]);

        if (!empty($existingRecord)) {
            $subTypeNames = StaffLeaveRemainDaysModel::getSubTypeNames();
            $relationName = $subTypeNames[$subType] ?? 'unknown';
            $message      = $this->getTranslation()->_('22328_already_applied',
                ['sub_type' => $this->getTranslation()->_("22328_relation_{$relationName}")]);
            throw new ValidationException($message);
        }
    }


    /**
     * 整理数据 数据结构 audit, split, img
     */
    protected function dataFormat()
    {
        // 先保存主表拿audit id
        $this->saveAudit();

        // 拆分表
        $this->formatSplitData();
    }

    /**
     * by 或者 hcm 显示额度整理
     * @return array 格式化后的额度信息
     */
    protected function formatLimitDays()
    {
        $dayLimit = half_num($this->limitDays['limit']);
        $daySub   = half_num($this->limitDays['sub']);
        $return   = [];

        $return['day_limit'] = "{$dayLimit}"; // 总额度
        $return['day_sub']   = "{$daySub}";     // 剩余额度 有可能是负数
        $return['text']      = 'no_limit';
        $this->getDI()->get('logger')->write_log(['formatLimitDays' => $return], 'info');
        return $return;
    }

    /**
     * 整理扣减额度标记对应周期
     */
    protected function formatSplitData()
    {
        // 拼接拆分表归属年 year_at 字段
        foreach ($this->leaveSplitInfo as $k => $in) {
            $duration                             = ($in['type'] == StaffAuditLeaveSplitModel::SPLIT_TYPE_0) ? 1 : 0.5; // 这天用额 1天 或者 半天
            $this->leaveSplitInfo[$k]['audit_id'] = $this->auditId;

            // 累计申请天数
            $this->currentApplyDays += $duration;
        }
    }

    /**
     * 申请权限验证
     * @param array $staff_info 员工信息
     * @return bool 是否有权限
     */
    public function applyPermission($staff_info = [])
    {
        if (empty($this->staffInfo)) {
            $this->staffInfo = $staff_info;
        }

        // 申请权限：正式员工、月/日/时薪制合同工
        if ($this->staffInfo['formal'] != HrStaffInfoModel::FORMAL_1 || !in_array($this->staffInfo['hire_type'], [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
                HrStaffInfoModel::HIRE_TYPE_3,
                HrStaffInfoModel::HIRE_TYPE_4,
            ])) {
            return false;
        }

        // 员工状态必须是在职状态
        if (empty($this->paramModel['is_bi']) && empty($this->paramModel['is_svc']) && $this->staffInfo['state'] != HrStaffInfoModel::STATE_ON_JOB) {
            return false;
        }

        // 非子员工
        if ($this->staffInfo['is_sub_staff'] != HrStaffInfoModel::IS_SUB_STAFF_0) {
            return false;
        }

        return true;
    }

    /**
     * 获取额度
     */
    public function getLimitDays()
    {
        // 家人去世假每次申请最多3天，不需要年度额度管理 hcm 也都展示 3/3
        $this->limitDays['limit'] = $this->limitDays['sub'] = 3.0;

        // 没有权限
        if (!$this->applyPermission()) {
            $this->limitDays['limit'] = $this->limitDays['sub'] = 0.0;
            return;
        }

        // 没转正不可以申请
        if (!empty($this->leaveProperty->is_forbidden) && !$this->hireCheck()) {
            $this->limitDays['limit'] = $this->limitDays['sub'] = 0.0;
            return;
        }
    }

    /**
     * 保存数据
     */
    public function dataSave()
    {
        // 保存申请上传图片
        $this->saveImgData();
        // 保存拆分表记录
        $this->saveSplitData();
        // 创建家人去世假记录（标记已申请的子类型）
        $this->saveRemainData();
    }

    /**
     * 保存家人去世假记录
     */
    protected function saveRemainData()
    {
        // 使用父类计算的申请天数
        $applyDays = $this->thisNum + $this->nextNum;
        if (!empty($applyDays) && !empty($this->paramModel['sub_type'])) {
            // 只有只能申请一次的类型才需要创建记录
            $onceOnlyTypes = StaffLeaveRemainDaysModel::getOnceOnlySubTypes();
            if (!in_array($this->paramModel['sub_type'], $onceOnlyTypes)) {
                return true;
            }

            $insert['staff_info_id']  = $this->staffInfo['staff_info_id'];
            $insert['leave_type']     = enums::LEAVE_TYPE_7;
            $insert['sub_leave_type'] = $this->paramModel['sub_type'];
            $insert['year']           = date('Y');
            $insert['task_date']      = date('Y-m-d');
            $insert['freeze_days']    = $applyDays;
            $insert['days']           = 0; // 已使用完
            $insert['leave_days']     = $applyDays;
            $insert['expire_date']    = null; // 永久有效，标记已申请过

            $model = new StaffLeaveRemainDaysModel();
            return $model->create($insert);
        }
        return true;
    }


    /**
     * 撤销返还额度（删除家人去世假记录）
     * @param int $auditId 审核ID
     * @param array $staffInfo 员工信息
     * @param array $extend 扩展参数
     * @return bool
     */
    public function returnRemainDays($auditId, $staffInfo, $extend = [])
    {
        $this->staffInfo = $staffInfo;

        // 从audit记录中获取子类型信息
        $auditInfo = StaffAuditModel::findFirst([
            'conditions' => 'audit_id = :audit_id:',
            'bind'       => ['audit_id' => $auditId],
            'columns'    => 'template_comment',
        ]);

        $subType = null;
        if (!empty($auditInfo) && !empty($auditInfo->template_comment)) {
            $templateData = json_decode($auditInfo->template_comment, true);
            $subType      = $templateData['leave_7_key'] ?? null;
        }

        // 如果从extend参数中获取
        if (empty($subType) && !empty($extend['sub_leave_type'])) {
            $subType = $extend['sub_leave_type'];
        }

        // 如果从extend参数中获取sub_type
        if (empty($subType) && !empty($extend['sub_type'])) {
            $subType = $extend['sub_type'];
        }

        if (empty($subType)) {
            $this->logger->write_log("撤销家人去世假缺少子类型信息，有可能是历史数据 {$this->staffInfo['staff_info_id']} {$auditId}",
                'info');
            return true;
        }

        // 删除家人去世假记录，允许重新申请
        $this->deleteFamilyDeathRecord($staffInfo['staff_info_id'], $subType);

        $this->logger->write_log("家人去世假记录删除成功 {$this->staffInfo['staff_info_id']} {$auditId} 子类型:{$subType}",
            'info');
        return true;
    }

    /**
     * 删除家人去世假记录
     * @param int $staffInfoId 员工ID
     * @param int $subType 子类型
     * @return bool
     */
    protected function deleteFamilyDeathRecord($staffInfoId, $subType)
    {
        // 只删除只能申请一次的类型记录
        $onceOnlyTypes = StaffLeaveRemainDaysModel::getOnceOnlySubTypes();
        if (!in_array($subType, $onceOnlyTypes)) {
            return true;
        }

        $record = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and sub_leave_type = :sub_type:',
            'bind'       => [
                'staff_id'   => $staffInfoId,
                'leave_type' => enums::LEAVE_TYPE_7,
                'sub_type'   => $subType,
            ],
        ]);

        if (!empty($record)) {
            return $record->delete();
        }

        return true;
    }

    public function getEnum()
    {
        $data   = StaffLeaveRemainDaysModel::getSubTypeNames();
        $return = [];
        foreach ($data as $k => $da) {
            $key          = '22328_relation_' . $da;
            $row['label'] = $this->getTranslation()->_($key);
            $row['value'] = $k;
            $return[]     = $row;
        }
        return $return;
    }

}
