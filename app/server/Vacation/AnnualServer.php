<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 11/2/22
 * Time: 9:25 PM
 */

namespace FlashExpress\bi\App\Server\Vacation;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveExtendModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\VacationServer;

class AnnualServer extends VacationServer{

    public $isPermission = true;//是否有年假权限

    //额度 占用 一半一半的情况
    protected function splitHalfItem($in, $cycle)
    {
        $item['year_at']       = $cycle;
        $item['staff_info_id'] = $this->staffInfo['staff_info_id'];
        $item['date_at']       = $in['date_at'];
        $item['type']          = StaffAuditLeaveSplitModel::SPLIT_TYPE_2;
        $item['audit_id']      = $this->auditId;
        return $item;
    }


    //申请权限
    protected function applyPermission()
    {
        //新增权限判断
        $flag = $this->leaveServer->leavePermission($this->staffInfo);
        if(!$flag){
            return false;
        }
        //  年假规则适用于：“正式员工”、“月薪制特殊合同工” 其他 类型都不能申请
        if ($this->staffInfo['formal'] != 1 //正式
            || !in_array($this->staffInfo['hire_type'],
                [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3])) {
            return false;
        }
        return true;
    }



    //获取每个员工的周期 和 失效日期 和 结算日期 90天失效
    public function get_cycle($staffInfo = [], $date_at = '')
    {
        //任务 直接调用
        if (!empty($staffInfo)) {
            $this->staffInfo = $staffInfo;
        }

        if (empty($this->staffInfo['hire_date'])) {
            return [];
        }
        $return = [];
        //入职日期加13月 的1号为结算日 以后每个结算日 加一年 改了 按天算
        $hire_date   = date('Y-m-d', strtotime($this->staffInfo['hire_date']));
        $first_count = date('Y-m-d', strtotime("{$hire_date} +1 year"));
        $today       = date('Y-m-d');
        if (!empty($date_at)) {
            $today = $date_at;
        }

        $return['cycle'] = 1;//默认周期 为1 每满一个周期 +1
        if ($today < $first_count) {//还没 满第一个周期
            $return['count_day']   = $first_count;
            $return['invalid_day'] = date('Y-m-d', strtotime("{$hire_date} +90 day"));
            return $return;
        }
        $count_day = $first_count;
        while ($today >= $count_day) {
            $count_day = date('Y-m-d', strtotime("{$count_day} +1 year"));
            $return['cycle']++;
        }

        //结算日 加 6个月
        $invalid_day = date('Y-m-d', strtotime("{$count_day} -1 year +90 day"));

        $return['count_day']   = $count_day;//结算日
        $return['invalid_day'] = $invalid_day;//结余 失效日

        return $return;
    }

    /**
     * 新入职员工 或者是新周期员工 初始化 年假相关发放调用
     * @param $staff_info
     * @param $cycle
     * @param array $extend 数组成员 days 22年额度 挪过来的情况才有 是负数 task_date任务日期
     * @return StaffLeaveRemainDaysModel
     */
    public function initAnnual($staff_info, $cycle, $extend = array())
    {
        $task_date  = $extend['task_date'] ?? date('Y-m-d');
        $days       = $extend['days'] ?? 0;
        $freezeDays = $extend['freeze_days'] ?? 0;
        $leaveDays  = $extend['leave_days'] ?? 0;
        $remain     = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year = :cycle: and leave_type = :leave_type:',
            'bind'       => [
                'staff_id'   => $staff_info['staff_info_id'],
                'cycle'      => $cycle,
                'leave_type' => enums::LEAVE_TYPE_1,
            ],
        ]);
        if (empty($remain)) {
            $remain = new StaffLeaveRemainDaysModel();
            //新增一条 remain
            $row['staff_info_id'] = $staff_info['staff_info_id'];
            $row['task_date']     = $task_date;
            $row['leave_type']    = enums::LEAVE_TYPE_1;
            $row['year']          = $cycle;
            $row['freeze_days']   = $freezeDays;
            $row['days']          = $days;
            $row['leave_days']    = $leaveDays;
            $remain->create($row);
        }

        $ext_info = StaffLeaveExtendModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year_at = :cycle:',
            'bind'       => [
                'staff_id'   => $staff_info['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_1,
                'cycle'      => $cycle,
            ],
        ]);
        //新增一条 ext
        if (empty($ext_info)) {
            $extModel               = new StaffLeaveExtendModel();
            $ext['staff_info_id']   = $staff_info['staff_info_id'];
            $ext['leave_type']      = enums::LEAVE_TYPE_1;
            $ext['year_at']         = $cycle;
            $ext['left_all_days']   = $days;
            $ext['job_title_level'] = $staff_info['job_title_level'];
            $ext['job_title_grade'] = $staff_info['job_title_grade_v2'];

            $extModel->create($ext);
        }

        return StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year = :cycle: and leave_type = :leave_type:',
            'bind'       => [
                'staff_id'   => $staff_info['staff_info_id'],
                'cycle'      => $cycle,
                'leave_type' => enums::LEAVE_TYPE_1,
            ],
        ]);
    }


    /**
     * 获取员工 应有的总额度 分母
     * @param $staff_info
     * @return array|int|mixed
     */
    public function getGradeDaysNormal($staff_info)
    {
        if (empty($staff_info)) {
            return 0;
        }

        $shouldDays   = $this->getShouldDays($staff_info);//职等额度
        $yearAdd      = $this->overOneYear($staff_info);//满周年额度

        $maxDay = $this->getMaxDays($staff_info);//最高额度 不能超过多少天
        return ($shouldDays + $yearAdd) > $maxDay ? $maxDay : ($shouldDays + $yearAdd);
    }

    //根据周期 初始化当前额度和上周期额度 hris 和 hcm 写
    public function addAnnualDataByMq($staffId){
        $staffRe = new StaffRepository($this->lang);
        $this->staffInfo = $staffRe->getStaffPosition($staffId);

        if(empty($this->staffInfo)){
            return false;
        }

        if(!in_array($this->staffInfo['formal'],[1,4])){
            return true;
        }
        if($this->staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF){
            return true;
        }

        if(!in_array($this->staffInfo['state'],[1,3])){
            return true;
        }

        $cycleInfo = $this->get_cycle();
        //没有信息情况
        if(empty($cycleInfo)){
            return false;
        }

        $this->initAnnual($this->staffInfo, $cycleInfo['cycle']);

        if($cycleInfo['cycle'] > 1){
            $this->initAnnual($this->staffInfo, $cycleInfo['cycle'] -1 );
        }

        return true;
    }




}