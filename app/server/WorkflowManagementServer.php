<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeBaseModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeRelateBaseModel;
use FlashExpress\bi\App\Server\BaseServer;

class WorkflowManagementServer extends BaseServer
{

	
	public function __construct($lang = 'zh-CN', $timezone='+08:00')
	{
		parent::__construct($lang,$timezone='+08:00');
		
	}
	
    /**
     * 获取列表
     */
    public function getValidWorkflowList(): array
    {
        $list = WorkflowModel::find([
            'columns' => 'id, name',
            'conditions' => " IFNULL(version,'') = '' ",//存在版本号的不可进行编辑
        ])->toArray();

        $list = array_map(function ($v) {
            return [
                'key' => $v['id'],
                'value' => $v['name'],
            ];
        }, $list);

        return $list ?? [];
    }

    /**
     * 获取列表
     * @param array $params
     * @return false
     */
    public function editSpecifyStaffInfoId(array $params = []): bool
    {
        $flowId = $this->processingDefault($params, 'flow_id');
        $id    = $this->processingDefault($params, 'id');
        $staffInfoIds = $this->processingDefault($params, 'staff_ids');
        $operatorId = $this->processingDefault($params, 'operator_id');

        //查找指定审批流中的固定工号节点
        $info = WorkflowNodeBaseModel::findFirst([
            'conditions' => 'id = :id: and flow_id = :flow_id: and auditor_type = 2 and deleted = 0',
            'bind' => [
                'flow_id' => $flowId,
                'id' => $id,
            ]
        ]);
        if ($info instanceof WorkflowNodeBaseModel) {

            //记录日志
            $this->logger->write_log(sprintf('staff %s has modify %s to %s', $operatorId, $info->auditor_id, $staffInfoIds),'info');

            //更新审批人
            $info->auditor_id = $staffInfoIds;
            $info->update();

            return true;
        }
        return false;
    }

    /**
     * 获取审批流
     */
    public function getWorkflowForHcm($params)
    {
        $flowId = $params['flow_id'] ?? 0;

        //先判断是否存在子审批流
        $nodeList = WorkflowNodeBaseModel::find([
            'conditions' => 'flow_id = :flow_id: and deleted = 0',
            'bind' => [
                'flow_id' => $flowId,
            ],
            'columns' => 'DISTINCT(code) as flow_code'
        ])->toArray();
        $flowCode = array_column($nodeList, 'flow_code');

        if (empty($flowCode)) {
            return [];
        }

        $result = [];
        foreach ($flowCode as $code) {

            //获取子审批流
            $result[] = $this->generateSubWorkflow([
                'flow_id' => $flowId,
                'flow_code' => $code
            ]);
        }
        return $result;
    }

    /**
     * 组织子审批流
     * @param $params
     * @return array
     */
    public function generateSubWorkflow($params): array
    {
        $flowId = $params['flow_id'];
        $flowCode = $params['flow_code'];


        if ($flowCode) {
            $conditions = "flow_id = :flow_id: and code = :code: and deleted = 0";
            $bind = [
                'flow_id' => $flowId,
                'code'   => $flowCode,
            ];
        } else {
            $conditions = "flow_id = :flow_id: and deleted = 0";
            $bind = [
                'flow_id' => $flowId,
            ];
        }

        //获取全部的relate
        $nodeList = WorkflowNodeBaseModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
        ])->toArray();

        //获取全部的relate
        $relateList = WorkflowNodeRelateBaseModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'order' => 'sort desc'
        ])->toArray();

        //获取开始节点
        $startNode = WorkflowNodeBaseModel::findFirst([
            'conditions' => $conditions . " and type = 0",
            'bind' => $bind,
        ]);
        if (empty($startNode)) { //没有开始节点
            return  [];
        }
        $startNode = $startNode->toArray();

        $workflowTree = $this->tree($startNode['id'], $nodeList, $relateList);

        return $workflowTree ?? [];
    }

    /**
     * 获取审批流树
     * @param $curNodeId
     * @param $nodeList
     * @param $relateList
     * @return array
     */
    public function tree($curNodeId, $nodeList, $relateList): array
    {
        $nodeKey = array_search($curNodeId, array_column($nodeList, 'id'));
        if (!isset($nodeKey)) {
            return [];
        }
        $nodeMap = array_column($nodeList, 'name', 'id');

        //节点名字
        if (in_array($nodeList[$nodeKey]['type'], [0, 99])) { //开始结束节点
            $nodeLabel = $nodeMap[$curNodeId] ?? "";
        } else { //非开始结束节点
            $nodeLabel = $this->getNodeNameByNodeAuditorType($nodeList[$nodeKey]['auditor_type']);
        }

        //获取当前
        $curNode = [
            "id"        => $curNodeId,
            "label"     => $nodeLabel,
            "child"     => [],
            "isEdit"    => isset($nodeList[$nodeKey]['auditor_type']) && $nodeList[$nodeKey]['auditor_type'] == 2 ? 1 : 0, //如果是auditor_type == 指定工号，则可以编辑
            "auditor_id"=> isset($nodeList[$nodeKey]['auditor_type']) && $nodeList[$nodeKey]['auditor_type'] == 2 ? $nodeList[$nodeKey]['auditor_id'] : "", //如果是auditor_type == 指定工号，则可以编辑
        ];

        //是否存在子节点
        $childNodekey = array_search($curNodeId, array_column($relateList, 'from_node_id'));

        if (isset($childNodekey)) {

            //找出全部的子节点
            $childList = array_map(function ($v) use($curNodeId) {
                return $v['from_node_id'] == $curNodeId ? $v : null;
            }, $relateList);
            $childList = array_values(array_filter($childList));

            $children = [];
            foreach ($childList as $child) {
                $children[] = $this->tree($child['to_node_id'], $nodeList, $relateList);
            }

            $curNode['child'] = $children;
        }
        return $curNode;
    }

    /**
     * 获取节点title By 审批人节点类型
     * @param $auditor_type
     * @return string
     */
    public function getNodeNameByNodeAuditorType($auditor_type): string
    {
        switch ($auditor_type) {
            case enums::WF_NODE_DESIGNATE_OTHER:
                $typeLabel = "指定的员工工号";
                break;
            case enums::WF_NODE_MANAGER:
                $typeLabel = "上级";
                break;
            case enums::WF_NODE_DEPARTMENT_MANAGER:
                $typeLabel = "部门负责人";
                break;
            case enums::WF_NODE_SUPERVISOR:
                $typeLabel = "网点正主管";
                break;
            case enums::WF_NODE_DM:
                $typeLabel = "DM";
                break;
            case enums::WF_NODE_RM:
                $typeLabel = "RM";
                break;
            case enums::WF_NODE_AM:
                $typeLabel = "AM";
                break;
            case enums::WF_NODE_GROUP_BOSS:
                $typeLabel = "Group Boss";
                break;
            case enums::WF_NODE_CEO:
                $typeLabel = "CEO";
                break;
            case enums::WF_NODE_COO:
                $typeLabel = "COO";
                break;
            case enums::WF_NODE_CFO:
                $typeLabel = "CFO";
                break;
            case enums::WF_NODE_CPO:
                $typeLabel = "CPO";
                break;
            case enums::WF_NODE_SPEC_DEPARTMENT_MANAGER:
                $typeLabel = "指定部门负责人";
                break;
            case enums::WF_NODE_HRBP:
                $typeLabel = "HRBP";
                break;
            case enums::WF_NODE_STORE_MANAGER:
                $typeLabel = "网点负责人";
                break;
            case enums::WF_NODE_MULTI_DEPARTMENT_MANAGER:
                $typeLabel = "部门负责人(多级)";
                break;
            case enums::WF_NODE_DM_BY_ORG:
                $typeLabel = "DM(根据组织架构)";
                break;
            case enums::WF_NODE_AM_BY_ORG:
                $typeLabel = "AM(根据组织架构)";
                break;
            case enums::WF_NODE_SS:
                $typeLabel = "shop supervisor";
                break;
            case enums::WF_NODE_SOM:
                $typeLabel = "shop operations manager";
                break;
            case enums::WF_NODE_SPEC_JOB_TITLE:
                $typeLabel = "申请人上级或上上级，有Sales Manager职位";
                break;
            case enums::WF_NODE_DI:
                $typeLabel = "审批人动态传入";
                break;
            case enums::WF_NODE_SUPERVISOR_MANAGER:
                $typeLabel = "网点正主管(如果不存在则找上级)";
                break;
            case enums::WF_NODE_ASSET_HUB_MANAGER:
                $typeLabel = "根据申请人所在网点 找对应网点的 xx职位的人";
                break;
            default:
                break;
        }

        return $typeLabel ?? '';
    }
}