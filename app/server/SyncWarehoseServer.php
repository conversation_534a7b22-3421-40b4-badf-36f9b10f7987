<?php
/**
 *SyncWarehoseServer.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/8/12 0012 14:41
 */

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class SyncWarehoseServer extends BaseServer
{

    public function getWmsCnf($isTask)
    {
        if ($isTask) {
            sleep(2);//wms,1s25次，一个账号，一个接口,每小时09:00,09:10都会调用这个接口，有交叉。
        }

        if (strtolower(env('runtime')) === 'pro') {
            $mchId       = env('wms_mchId');
            $pwd         = env('wms_pwd');
            $warehouseId = env('wms_warehouseId','1,18');
        } else {
//            $mchId       = 'Supply';
//            $pwd         = 'd04b7e7746e0c2e4e04cb9a59cdea98841e80ba6f9ce4ab09339bc8a93ac4424';
//            $warehouseId = '1,8';
            $mchId       = env('wms_mchId','Supply');
            $pwd         = env('wms_pwd','d04b7e7746e0c2e4e04cb9a59cdea98841e80ba6f9ce4ab09339bc8a93ac4424');
            $warehouseId = env('wms_warehouseId','1,8');

        }

        return $account = [
            'wms_mchId'   => $mchId,
            'wms_pwd'     => $pwd,
            'warehouseId' => $warehouseId,
            'api_wms_url' => env('api_wms_url')
        ];
    }

    /**
     * 获取出库单号
     * 物流信息
     *
     * @param $orderSn
     * @throws ValidationException
     */
    public function outBoundTracking($orderSn, $mchId = '')
    {
        $cnf = $this->getWmsConfigByMchId($mchId);

        $postData['mchId']       = $cnf['wms_mchId'];
        $postData['orderSn']     = $orderSn;
        $pwd                     = $cnf['wms_pwd'];
        $func                    = $cnf['api_wms_url'] . '/open/getOutboundTrackingInfo';
        $resPost = $this->httpPostFun($func, $postData, null, $pwd);
        $this->getDI()->get('logger')->write_log(
            "function =>api_wms_out_sn" .
            ' request=>' . var_export($postData, true) .
            ' response=>' . var_export($resPost, true),
            'info'
        );
        if (isset($resPost['code']) && $resPost['code'] == 1 && isset($resPost['data']) && $resPost['data']) {
            return $resPost['data'];
        }

        return [];
    }

    /**
     * 向 wms 出库单添加出货单
     * Created by: Lqz.
     * @param $postData
     * CreateTime: 2020/8/12 0012 15:31
     * @throws ValidationException
     */
    public function syncAddOrderToWmsReturnWarehouseAdd(array $postData, $isTask = false)
    {
        // 获取传入的mchId，用于识别货主
        $mchId = $postData['mchId'] ?? null;

        // 根据mchId获取对应的WMS配置
        $cnf = $this->getWmsConfigByMchId($mchId, $isTask);

        //19024传递了仓库id按照传递为主，未传递按照默认配置
        $postData['warehouseId'] = isset($postData['warehouseId']) ? $postData['warehouseId'] : $cnf['warehouseId'];
        $postData['mchId']       = $cnf['wms_mchId'];
        $pwd                     = $cnf['wms_pwd'];

        $func = $cnf['api_wms_url'] . '/open/returnWarehouseAdd';
        $resPost = $this->httpPostFun($func, $postData, null, $pwd);
        $this->getDI()->get('logger')->write_log(
            "function =>api_wms_order" .
            ' request=>' . var_export($postData, true) .
            ' response=>' . var_export($resPost, true),
            'info'
        );

        return $resPost;
    }

    /**
     * 向 wms 出货单审核结果
     * Created by: Lqz.
     * CreateTime: 2020/8/12 0012 15:31
     *
     * @param object $orderObj
     * @param bool $isTask
     * @return array|bool|mixed|string
     * @throws ValidationException
     */
    public function syncAuditFromWmsAuditOutbound(object $orderObj, bool $isTask = false)
    {
        $postData = [
            'orderSn' => $orderObj->out_sn
        ];

        // 根据mchId获取对应的WMS配置
        $cnf = $this->getWmsConfigByMchId($orderObj->mach_code, $isTask);

        $postData['mchId']       = $cnf['wms_mchId'];
        $pwd                     = $cnf['wms_pwd'];
        $func                    = $cnf['api_wms_url'] . '/Audit/outbound';
        $resPost                 = $this->httpPostFun($func, $postData, null, $pwd);
        $this->getDI()->get('logger')->write_log(
            "function =>api_wms_outBoundGoods" .
            ' request=>' . var_export($postData, true) .
            ' response=>' . var_export($resPost, true),
            'info'
        );
        return $resPost;
    }

    /**
     * 向 wms 取消订单
     * Created by: Lqz.
     * @param $postData
     * CreateTime: 2020/8/12 0012 15:38
     * @throws ValidationException
     */
    public function syncCancelOrderToWmsCancelOutbound(array $postData, $isTask = false)
    {
        // 获取传入的mchId，用于识别货主
        $mchId = $postData['mchId'] ?? null;

        // 根据mchId获取对应的WMS配置
        $cnf = $this->getWmsConfigByMchId($mchId, $isTask);

        // 接口无需此参数, 暂去
        // $postData['warehouseId'] = $cnf['warehouseId'];
        $postData['mchId']       = $cnf['wms_mchId'];
        $pwd                     = $cnf['wms_pwd'];
        $func                    = $cnf['api_wms_url'] . '/open/cancelOutbound';
        $resPost                 = $this->httpPostFun($func, $postData, null, $pwd);
        $this->getDI()->get('logger')->write_log(
            "function =>api_wms_cancelOutbound" .
            ' request=>' . var_export($postData, true) .
            ' response=>' . var_export($resPost, true),
            'info'
        );
        return $resPost;
    }

    /**
     * 向 wms 获取商品库存
     * Created by: Lqz.
     * @param $postData
     * CreateTime: 2020/8/12 0012 15:38
     * @throws ValidationException
     */
    public function syncGoodsInventoryFromWmsGoodsStock(array $postData, $isTask = false)
    {
        // 获取传入的mchId，用于识别货主
        $mchId = $postData['mchId'] ?? null;

        // 根据mchId获取对应的WMS配置
        $cnf = $this->getWmsConfigByMchId($mchId, $isTask);

        //19024传递了仓库id按照传递为主，未传递按照默认配置
        $postData['warehouseId'] = !empty($postData['warehouseId']) ? $postData['warehouseId'] : $cnf['warehouseId'];
        $postData['mchId']       = $cnf['wms_mchId'];
        $pwd                     = $cnf['wms_pwd'];
        $func                    = $cnf['api_wms_url'] . '/open/goodsStock';

        $resPost = $this->httpPostFun($func, $postData, null, $pwd);
        $this->getDI()->get('logger')->write_log(
            "function =>api_wms_goodsStock" .
            ' request=>' . var_export($postData, true) .
            ' response=>' . var_export($resPost, true),
            'info'
        );
        return $resPost;
    }

    /**
     * 获取 订单状态
     * Created by: Lqz.
     * @param array $postData
     * @return bool|mixed|string
     * CreateTime: 2020/8/24 0024 20:22
     * @throws ValidationException
     */
    public function syncOrderStatusFromWmsGetOutboundOrderStatus(array $postData, $isTask = false)
    {
        // 获取传入的mchId，用于识别货主
        $mchId = $postData['mchId'] ?? null;

        // 根据mchId获取对应的WMS配置
        $cnf = $this->getWmsConfigByMchId($mchId, $isTask);

        // 接口无需此参数, 暂去
        // $postData['warehouseId'] = $cnf['warehouseId'];
        $postData['mchId']       = $cnf['wms_mchId'];
        $pwd                     = $cnf['wms_pwd'];
        $func                    = $cnf['api_wms_url'] . '/open/getOutboundOrderStatus';
        $resPost = $this->httpPostFun($func, $postData, null, $pwd);
        $this->getDI()->get('logger')->write_log(
            "function =>api_wms_OrderStatus" .
            ' request=>' . var_export($postData, true) .
            ' response=>' . var_export($resPost, true),
            'info'
        );
        return $resPost;
    }

    /**
     * 获取 订单详情，因为相同接口有频次限制，用不一样的吧
     * Created by: Lqz.
     * @param array $postData
     * @return bool|mixed|string
     * CreateTime: 2020/8/24 0024 20:22
     * @throws ValidationException
     */
    public function getOrderDetail(array $postData, $isTask = false)
    {
        // 获取传入的mchId，用于识别货主
        $mchId = $postData['mchId'] ?? null;

        // 根据mchId获取对应的WMS配置
        $cnf = $this->getWmsConfigByMchId($mchId, $isTask);

        //19024传递了仓库id按照传递为主，未传递按照默认配置

        // 接口用不到该参数, 暂注释
        // $postData['warehouseId'] = isset($postData['warehouseId']) ? $postData['warehouseId'] : $cnf['warehouseId'];
        $postData['mchId']       = $cnf['wms_mchId'];
        $pwd                     = $cnf['wms_pwd'];
        $func                    = $cnf['api_wms_url'] . '/open/outBoundOrderDetail';

        $resPost = $this->httpPostFun($func, $postData, null, $pwd);

        $this->getDI()->get('logger')->write_log(
            "function =>api_wms_outBoundOrderDetail" .
            ' request=>' . var_export($postData, true) .
            ' response=>' . var_export($resPost, true),
            'info'
        );
        return $resPost;
    }

    function httpPostFun($url, $param, $header = null, $pwd = null)
    {
        $curl = curl_init();
        if ($pwd != null) {
            $data = buildRequestParamFun($param, $pwd);
        } else {
            $data = $param;
        }
        if (!isset($header)) {
            $header = [];
            $header[] = "Content-type: application/x-www-form-urlencoded";
            $header[] = "Accept: application/json";
            if(isset($param['lang'])){
                $header[] = "Accept-Language: " . $param['lang'];
            }
            $escape = true;
        }
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, true); // post
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data); // post data
        curl_setopt($curl, CURLOPT_TIMEOUT, 10);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);

        $responseText = curl_exec($curl);
        $errno = curl_errno($curl);
        $error = curl_error($curl);
        curl_close($curl);
        if ($errno) {
            $responseText .= "errno:$errno,error: " .$error;
            $this->wLog('工服调用scm','curl错误'.$responseText,'','notice');
            return [];
        }

        if (isset($escape)) {
            $responseText = json_decode($responseText, true);
        }
        return $responseText;
    }

    /**
     * 查询商品库存（支持多货主配置）返回正确的bracode库存数量
     * @description: 根据传入的mchId参数动态读取对应货主的WMS配置
     * @param array $postData 查询参数，包含mchId用于识别货主以及需要查询的barcode数据
     * @param bool $isTask 是否为任务调用
     * @return array 库存查询结果
     * @throws ValidationException
     * @author: AI
     * @date: 2025-08-05 20:30:00
     */
    public function syncGoodsStockWithErrBarcode(array $postData, bool $isTask = false)
    {
        // 获取传入的mchId，用于识别货主
        $mchId = $postData['mchId'] ?? null;

        // 根据mchId获取对应的WMS配置
        $cnf = $this->getWmsConfigByMchId($mchId, $isTask);

        //19024传递了仓库id按照传递为主，未传递按照默认配置
        $postData['warehouseId'] = isset($postData['warehouseId']) ? $postData['warehouseId'] : $cnf['warehouseId'];
        $postData['mchId']       = $cnf['wms_mchId'];
        $pwd                     = $cnf['wms_pwd'];
        $func                    = $cnf['api_wms_url'] . '/open/goodsStockWithErrBarcode';

        $resPost = $this->httpPostFun($func, $postData, null, $pwd);
        $this->getDI()->get('logger')->write_log(
            'function =>api_wms_goodsStock' .
            ' mchId=>' . $mchId .
            ' request=>' . json_encode($postData, JSON_UNESCAPED_UNICODE) .
            ' response=>' . json_encode($resPost, JSON_UNESCAPED_UNICODE),
            'info'
        );
        return $resPost;
    }

    /**
     * 根据mchId获取WMS配置
     * @description: 从setting_env表的interior_goods_scm_set配置中获取指定货主的WMS配置
     * @param string|null $mchId 货主ID
     * @param bool $isTask 是否为任务调用
     * @return array WMS配置信息
     * @throws ValidationException
     * @author: AI
     * @date: 2025-08-05 20:30:00
     */
    private function getWmsConfigByMchId(?string $mchId, bool $isTask = false): array
    {
        if ($isTask) {
            sleep(2); // wms,1s25次，一个账号，一个接口,每小时09:00,09:10都会调用这个接口，有交叉。
        }

        // 如果没有传入mchId，使用原有的getWmsCnf方法（向后兼容）
        if (empty($mchId)) {
            return $this->getWmsCnf($isTask);
        }

        try {
            $settingEnvServer = new SettingEnvServer();
            $scmConfigJson = $settingEnvServer->getSetVal('interior_goods_scm_set');

            if (empty($scmConfigJson)) {
                // 如果没有配置，回退到原有方法
                return $this->getWmsCnf($isTask);
            }

            $scmConfig = json_decode($scmConfigJson, true);
            if (json_last_error() !== JSON_ERROR_NONE || !is_array($scmConfig)) {
                // JSON解析失败，回退到原有方法
                throw new ValidationException('SCM配置JSON解析失败: ' . json_last_error_msg());
            }

            // 查找指定mchId的配置
            if (!isset($scmConfig[$mchId])) {
                // 如果找不到指定mchId的配置，回退到原有方法
                throw new ValidationException("未找到mchId [{$mchId}] 的SCM配置");
            }

            $mchConfig = $scmConfig[$mchId];

            // 验证配置完整性
            if (empty($mchConfig['mchId']) || empty($mchConfig['pwd'])) {
                throw new ValidationException("mchId [{$mchId}] 的SCM配置不完");
            }

            return [
                'wms_mchId'   => $mchConfig['mchId'],
                'wms_pwd'     => $mchConfig['pwd'],
                'warehouseId' => $mchConfig['warehouseId'] ?? '',
                'api_wms_url' => env('api_wms_url')
            ];

        } catch (\Exception $e) {
            // 异常情况下回退到原有方法
            $this->logger->write_log('获取SCM配置异常: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

}
