<?php


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStoreApplySupportModel;
use FlashExpress\bi\App\Repository\OsStaffRepository;
use FlashExpress\bi\App\Repository\ShiftRepository;

class StoreSupportServer extends BaseServer
{
    public $timezone;

    public static $not_in_shift = [12,13,14,15,16,17,18,19,20,21,22,23,26,28,29,30,40,41,42,43,44,45,46,47,48,49,35];

    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * 支援班次信息
     * @param $shift_id
     * @param $shift_extend_id
     * @return string
     */
    public function getWorkShift($shift_id, $shift_extend_id)
    {
        $work_shift = '';
        if (!empty($shift_extend_id)) {
            //指定班次
           $shift_info = (new ShiftRepository($this->timezone))->getV2ShiftInfo($shift_id,
                $shift_extend_id);
            return $shift_info ? $shift_info['shift_work_time'] : '';
        }

        $shift_info = (new OsStaffRepository($this->timezone))->getWorkShift($shift_id);
        if (!empty($shift_info)) {
            switch ($shift_info['type']) {
                case 'EARLY':
                    $shift = $this->getTranslation()->_('shift_early');
                    break;
                case 'MIDDLE':
                    $shift = $this->getTranslation()->_('shift_middle');
                    break;
                case 'NIGHT':
                    $shift = $this->getTranslation()->_('shift_night');
                    break;
                default:
                    $shift = $this->getTranslation()->_('shift_early');
            }
            $work_shift = $shift.' '.$shift_info['start'].' - '.$shift_info['end'];
        }
        return $work_shift;
    }

    /**
     * 根据产品需求在审批流状态发生变化时需要按照规则跟新状态
     * @param int $id
     * @param int $state
     * @return int
     */
    public function getSupportStatusById(int $id, int $state): int
    {
        /**
         * 支援状态 1->待生效 2->已生效 3->已失效 4->已取消 5->招募中
         * 支援开始日期晚于今天：招募中
         * 支援开始日期等于或早于今天，且支援结束日期等于或晚于今天：已生效
         * 支援结束日期早于今天：已失效
         */

        $support_status = 1;
        // 审批驳回 or 撤销
        if (in_array($state, [enums::APPROVAL_STATUS_REJECTED, enums::APPROVAL_STATUS_CANCEL])) {
            return 4;
        }

        if ($state == enums::APPROVAL_STATUS_APPROVAL) {
            $store_apply_support = HrStoreApplySupportModel::findFirst([
                'conditions' => "id = :id:",
                'bind'       => [
                    'id' => $id,
                ],
                'columns'    => 'employment_begin_date, employment_end_date',
            ]);

            $cur_date = date('Y-m-d');
            if ($store_apply_support->employment_begin_date > $cur_date) {
                $support_status = 5;
            }

            if ($store_apply_support->employment_end_date < $cur_date) {
                $support_status = 3;
            }

            if (($store_apply_support->employment_begin_date == $cur_date || $store_apply_support->employment_begin_date < $cur_date) &&
                ($store_apply_support->employment_end_date == $cur_date || $store_apply_support->employment_end_date > $cur_date)
            ) {
                $support_status = 2;
            }
            return $support_status;
        }

        return $support_status;
    }

}