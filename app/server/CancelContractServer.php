<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\MsgAssetModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReasonModel;
use FlashExpress\bi\App\Models\backyard\StaffResignModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\fle\FleKaProfileModel;
use FlashExpress\bi\App\Models\oa\LoanModel;
use FlashExpress\bi\App\Models\oa\ReimbursementModel;
use FlashExpress\bi\App\Models\oa\ReimbursementRelLoanModel;
use FlashExpress\bi\App\Models\oa\ReserveFundApplyModel;
use FlashExpress\bi\App\Models\oa\ReserveFundReturnModel;
use FlashExpress\bi\App\Repository\AuditApprovalRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\ResignRepository;
use FlashExpress\bi\App\Repository\StaffAuditToolLog;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\MaterialAssetServer;


class CancelContractServer extends ResignServer
{
    //解约申请编号
    const CANCEL_CONTRACT_NUMBER_ENV = 'cancel_contract_number_%s';
    const STAFF_CANCEL_CONTRACT_NUMBER = 'C-%s/%s/%s/%s';

    public static $current_state;


    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
        $this->timezone = $timezone;
    }

    public function previewPdf($paramsIn)
    {
    }


    //获取编号
    protected function getNumber($staff_info_id): string
    {
        $db   = SettingEnvModel::beginTransaction($this);
        $code = sprintf(self::CANCEL_CONTRACT_NUMBER_ENV, date('Y'));
        $env  = SettingEnvModel::findFirst([
            'conditions' => 'code = :code:',
            'bind'       => ['code' => $code],
            'for_update' => true,
        ]);
        if (empty($env)) {
            $env          = new SettingEnvModel();
            $env->code    = $code;
            $env->set_val = 0;
            $env->remark  = date('Y') . '解约申请编号';
        }
        $env->set_val += 1;
        $env->save();

        $db->commit();
        return sprintf(self::STAFF_CANCEL_CONTRACT_NUMBER, date('Y'), date('m'),
            str_pad($env->set_val, 4, "0", STR_PAD_LEFT), $staff_info_id);
    }


    /**
     */
    protected function validation($staffInfo, $paramIn): bool
    {
        return true;
    }


    protected function fullInsertData(&$data, $paramIn)
    {
        return $data;
    }

    protected function fullOtherInsertData($auditId, $paramIn): bool
    {
        return true;
    }

    /**
     * 添加解约申请
     * @param array $paramIn
     * @return array
     * @throws Exception
     */
    public function add(array $paramIn): array
    {
        //[1]获取传入参数
        $staffId      = $this->processingDefault($paramIn, 'staff_id');
        $lastWorkDate = $this->processingDefault($paramIn, 'last_work_date');
        $leaveDate    = $this->processingDefault($paramIn, 'leave_date');
        $reason       = $this->processingDefault($paramIn, 'reason');
        $remark       = $this->processingDefault($paramIn, 'remark');


        $serial_no = $this->getID();
        //[2]逻辑验证
        //根据工号获取详情
        $staffInfo = (new StaffRepository())->checkoutStaffById($staffId);

        if (empty($staffInfo)) {
            throw new BusinessException($this->getTranslation()->_('1001'));
        }

        $this->validation($staffInfo, $paramIn);

        //非个人代理的不能提交
        if (!in_array($staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
            throw new BusinessException($this->getTranslation()->_('18535_err_msg_003'));
        }
        
        if ($staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID && empty($reason)){
            throw new BusinessException($this->getTranslation()->_('resign_agent_reason'));
        }
        if ($staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID && empty($remark)){
            throw new BusinessException($this->getTranslation()->_('resign_agent_remark'));
        }

        if (strtotime($leaveDate) < time()) {
            throw new BusinessException($this->getTranslation()->_('leave_err_msg_leave_date'));
        }
        
        //目前已经处于待解约状态，无需自己申请
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_ON_JOB && $staffInfo['wait_leave_state'] == 1) {
            throw new BusinessException($this->getTranslation()->_('18974_error_msg_004'));
        }

        //校验是否存在离职申请
        $result = (new ResignRepository($this->lang, $this->timezone))->getResignCnt(['staff_info_id' => $staffId]);
        if ($result != 0) {
            throw new BusinessException($this->getTranslation()->_('5202'), enums::$ERROR_CODE['1000']);
        }

        $db = $this->getDI()->get("db");
        //开启事务
        $db->begin();
        try {
            //[3]保存申请数据
            $data = [
                'submitter_id'   => $staffId,
                'hire_date'      => substr($staffInfo['hire_date'],0,10),
                'last_work_date' => $lastWorkDate,
                'leave_date'     => $leaveDate,
                'reason'         => empty($reason) ? StaffLeaveReasonModel::LEAVE_REASON_76 : $reason,
                'remark'         => $remark ?? '',
                'status'         => enums::$audit_status['panding'],
                'serial_no'      => 'RN' . $serial_no,
                'source'         => 0,
                'hire_type'      => $staffInfo['hire_type'],
            ];

            $this->fullInsertData($data, $paramIn);

            $auditId = (new ResignRepository($this->lang, $this->timezone))->inserResign($data);
            if (empty($auditId)) {
                throw new \Exception($this->getTranslation()->_('4008'));
            }
            //其他数据
            $this->fullOtherInsertData($auditId, $paramIn);

            $approvalServer = new ApprovalServer($this->lang, $this->timezone);
            $requestId      = $approvalServer->create($auditId, AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT,
                $staffId);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
            //处理hold
            $this->dealHold($staffId, $leaveDate);
        } catch (Exception $e) {
            $db->rollBack(); //回滚
            throw $e;
        }
        //提交事务
        $db->commit();

        return $this->checkReturn(['data' => ['resign_id' => $auditId]]);
    }

    /**
     * @throws BusinessException
     */
    protected function dealHold($staffId, $leaveDate,$leave_source = 5,$src= 1): bool
    {
        $add_hour = $this->getDI()['config']['application']['add_hour'];

        $hold_reason = 'incomplete_resignation_procedures';
        $hold_params = [
            'staff_info_id' => $staffId,
            //员工id
            'type'          => '',
            //hold类型(1.工资hold,2.提成hold,同时就传’1,2’)
            'hold_reason'   =>  $hold_reason,
            //hold原因(BY申请离职)
            'hold_remark'   => 1,
            //hold备注
            'hold_time'     => gmdate('Y-m-d H:i:s', time() + $add_hour * 3600),
            //hold时间操作时间
            'hold_source'   => $leave_source,
        ];
        // 如果离职日期在下个月5号之前  同步工资hold
        // 如果离职日期在下个月15号之前 同步提成hold
        $nextMonthType1 = date("Y-m-05", strtotime("+1 month"));
        $nextMonthType2 = date("Y-m-15", strtotime("+1 month"));
        if ($leaveDate <= $nextMonthType1) {
            $hold_params['type'] = "2";
        } elseif ($leaveDate <= $nextMonthType2) {
            $hold_params['type'] = "2";
        }
        $this->getDI()->get('logger')->write_log(['synchronize_hold' => $hold_params], 'info');
        if (!empty($hold_params['type'])) {
            $hcm_rpc = (new ApiClient('hcm_rpc', '', 'synchronize_hold'));
            $hcm_rpc->setParams($hold_params);
            $return = $hcm_rpc->execute();
            if (!isset($return['result']['code']) || $return['result']['code'] != 1) {
                throw new BusinessException($this->getTranslation()->_('server_error'));
            }
        }

        //同步状态
        $res = $this->syncApproval([
            'approval_status' => self::$current_state ?: enums::$audit_status['panding'],
            'staff_info_id'   => $staffId,
            'leave_date'      => $leaveDate,
            'leave_source'    => $leave_source,
            'src'             => $src,
        ]);
        if (!$res) {
            throw new BusinessException($this->getTranslation()->_('server_error'));
        }
        return true;
    }


    /**
     *
     * 获取审批详情详细信息
     *
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     *
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result     = $this->getResignDetail(['resign_id' => $auditId]);
        $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
        if ($staff_info['data']) {
            $staff_info = $staff_info['data'];
        }

        $detailLists = [
            'apply_parson'     => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? ''),
            'apply_department' => sprintf('%s - %s', $staff_info['depart_name'] ?? '',
                $staff_info['job_name'] ?? ''),
            'signing_date'     =>  $result['signing_date'],
        ];

        if (!empty($result['resignation_notice'])) {
            $tmp                                    = explode(" ", $result['resignation_notice']);
            $detailLists['contract_end_notice_day'] = $tmp[0] . $this->getTranslation()->_($tmp[1]);
        }

        $detailLists['last_work_date_independent'] = $result['last_work_date'];
        if (isCountry('MY') && $staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID){
            $detailLists['agent_reason_my'] = !empty($result['reason']) ? $this->getTranslation()->_('resign_reason_'.$result['reason']) : '';
            $detailLists['agent_remark_my'] = $result['remark'] ?? '';
        }

        $detailLists['contract_end_day']['value'] = $result['leave_date'];

        if (!empty($result['resignation_leave_day']) && $result['resignation_leave_day'] > $result['leave_date']) {
            $diffDays                                  = round((strtotime($result['resignation_leave_day']) - strtotime($result['leave_date'])) / (86400));
            $detailLists['contract_end_day']['remark'] = $this->getTranslation()->_('contract_end_notice', [
                'last_work_day'         => $result['resignation_work_day'],
                'need_contract_end_day' => $result['resignation_leave_day'],
                'num'                   => $diffDays,
                'contract_end_day'      => $result['leave_date'],
            ]);
        }
        if (!empty($result['leave_acceptance_book'])) {
            $detailLists['employee_notice'] = $result['leave_acceptance_book'];
        }

        //驳回状态，需要显示驳回原因
        if ($result['status'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
        }
        //撤销状态，需要显示撤销原因
        if ($result['status'] == enums::$audit_status['revoked']) {
            $detailLists = array_merge($detailLists, ["cancel_reason" => $result['cancel_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        //当前登陆人 信息 个人代理要换文案
        $staffRe = new StaffRepository($this->lang);
        $userInfo = $staffRe->getStaffPosition($user);

        $data                       = [
            'title'         => $this->getTranslation()->_("workflow_type_63"),
            'id'            => $result['resign_id'],
            'staff_id'      => $staff_info['id'],
            'type'          => (string)AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT,
            'created_at'    => $result['created_at'],
            'updated_at'    => $result['updated_at'],
            'approvalLevel' => 0,
            'status_text'   => (new AuditlistRepository($this->lang,
                $this->timezone))->getAuditStatus('10' . $result['status']),
            'serial_no'     => $result['serial_no'] ?? '',
            'user_hire_type'=> $userInfo['hire_type'] ?? 0,
        ];
        $returnData['data']['head'] = $data;
        $staffLeaveInfo             = $this->getStaffLeaveInfo(['staff_info_id' => $result['submitter_id']]);
        if ($result['asset_tag'] == enums::RESIGN_ASSET_TAG_NEW) {
            $new_assets_server = new MaterialAssetServer($this->lang, $this->timezone);

            if (in_array($result['status'], [enums::$audit_status['approved'], enums::$audit_status['timedout']])) {
                $new_assets                             = $new_assets_server->getAuditLeaveAssets(['resign_id' => $result['resign_id']]);
                $staffLeaveInfo['assets_process_state'] = $new_assets['assets_process_state'];
                $staffLeaveInfo['assets']               = $new_assets['assets'];
            } else {
                $result                   = $new_assets_server->getAssetsDetailByStaffId([
                    'staff_id'  => $result['submitter_id'],
                    'page_ize'  => 1000,
                    'page_size' => 1000,
                    'page_num'  => 1,
                ]);
                $staffLeaveInfo['assets'] = $result['data']['items'];
            }
        }
        $returnData['data']['extend']  = $staffLeaveInfo ?? [];
        $returnData['data']['confirm'] = [];
        return $returnData;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false, false, false, false, false, false);
    }

    /**
     * 拼组数据
     * @param $list
     * @return array
     */
    public function format($list): array
    {
        $return = [];
        foreach ($list as $key => $v) {
            $item = [];
            if ($key === 'employee_notice') {
                $item['key']        = $this->getTranslation()->_('employee_notice');
                $item['type']       = 1;
                $item['simple_val'] = $v;
                $item['value']      = $this->getTranslation()->_('view_employee_notice');
                $return[]           = $item;
                continue;
            }

            $item['key']    = $this->getTranslation()->_($key) ?? '';
            $item['value']  = is_array($v) && isset($v['value']) ? $v['value'] : $v;
            $item['tips']   = is_array($v) && isset($v['tips']) ? $v['tips'] : null;
            $item['remark'] = is_array($v) && isset($v['remark']) ? $v['remark'] : null;
            $return[]       = $item;
        }
        return $return;
    }

    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            self::$current_state = $state;
            $resignInfo = $this->getResignDetail(['resign_id' => $auditId]);


            $update_field = [
                'source'     => 0,
                'status'     => $state,
                'updated_at' => gmdate('Y-m-d H:i:s'),
            ];
            // 原因
            if ($state == enums::$audit_status['dismissed']) {
                $update_field['reject_reason'] = $extend['remark'];
            } elseif ($state == enums::$audit_status['revoked']) {
                $update_field['cancel_reason'] = $extend['remark'];
            }

            $staffServer = (new StaffServer())->getStaffById($resignInfo['submitter_id'],'state');
            $is_resign =  $staffServer && $staffServer['state'] == HrStaffInfoModel::STATE_RESIGN;
            //提交申请就同步数据到FBI
            if ($state == enums::$audit_status['approved'] || $state == enums::$audit_status['timedout']) {

                //离职不做后续处理
                if ($is_resign) {
                    $this->getDI()->get('db')->updateAsDict('staff_resign',
                        $update_field,
                        [
                            'conditions' => "resign_id = ?",
                            'bind'       => [$resignInfo['resign_id']],
                        ]);
                    return true;
                }

                $params = [
                    'staff_info_id'       => $resignInfo['submitter_id'],
                    'wait_leave_date'     => $resignInfo['leave_date'],
                    'leave_reason'        => $resignInfo['reason'],
                    'leave_source'        => 5, //backyard提交申请
                    'leave_type'          => 1,
                    'leave_reason_remark' => $resignInfo['remark'],
                    'project_source'      => 'by',
                ];

                $params['staff_resign_id'] = $auditId;
                $params['work_handover']   = $resignInfo['work_handover'];
                //这里会调用OA 资产
                $rpcClient = new ApiClient('hr_rpc', '', 'hr_staff_wait_leave');
                $rpcClient->setParams($params);
                $resultData = $rpcClient->execute();
                if ($resultData['result'] !== true) {
                    $this->getDI()->get("logger")->write_log([
                        'staff_id' => $resignInfo['submitter_id'],
                        'result'   => $resultData['result'],
                    ], "alert");
                }
            }
            if (isCountry('MY') && $state == enums::$audit_status['approved']){
                // 发送邮件
                $sendEmail = $this->sendEmail($resignInfo);
            }
            //同步状态
            !$is_resign && $this->syncApproval([
                'approval_status' => $state,
                'submitter_id'    => $resignInfo['submitter_id'],
                'leave_date'      => $resignInfo['leave_date'],
            ]);

            $this->getDI()->get('db')->updateAsDict('staff_resign',
                $update_field,
                [
                    'conditions' => "resign_id = ?",
                    'bind'       => [$resignInfo['resign_id']],
                ]);
        }
        return true;
    }
    
    public function sendEmail($resignInfo): bool
    {
        return true;
    }


    public function genSummary(int $auditId, $user)
    {
        $info = StaffResignModel::findFirst([
            "resign_id = :resign_id:",
            "bind" => [
                "resign_id" => $auditId,
            ],
        ]);
        if (!empty($info)) {
            $info  = $info->toArray();
            $param = [
                [
                    'key'   => "contract_end_day",
                    'value' => $info['leave_date'],
                ],
            ];
        }
        return $param ?? [];
    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        return [];
    }

    /**
     * 更新[包含操作：同意|驳回]离职申请
     * @param array $paramIn 传入参数
     * @return array
     * @throws Exception
     */
    public function update($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id');
        $auditId = $this->processingDefault($paramIn, 'audit_id');
        $status  = $this->processingDefault($paramIn, 'status');
        $reason  = $this->processingDefault($paramIn, 'reject_reason');
        $reason  = addcslashes(stripslashes($reason), "'");  //单引号过滤

        //[2]验证数据
        $resignInfo = $this->getResignDetail(['resign_id' => $auditId]);
        if (empty($resignInfo)) {
            throw new BusinessException($this->getTranslation()->_('1015'));
        }

        //已经是最终状态,不能修改
        if ($resignInfo['status'] != enums::$audit_status['panding']) {
            throw new BusinessException($this->getTranslation()->_('1016'));
        }

        //申请人无操作权限
        if ($resignInfo['submitter_id'] == $staffId) {
            throw new BusinessException($this->getTranslation()->_('miss_args'));
        }

        $approvalServer = new ApprovalServer($this->lang, $this->timezone);
        if ($status == enums::$audit_status['dismissed']) {
            $approvalServer->reject($auditId, AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT, $reason, $staffId);
        } elseif ($status == enums::$audit_status['approved']) {
            $approvalServer->approval($auditId, AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT, $staffId);
        }
        return $this->checkReturn([]);
    }

    /**
     * 取消离职申请
     * @param array $paramIn 传入参数
     * @return array
     * @throws BusinessException|ValidationException
     */
    public function cancel($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id');
        $auditId = $this->processingDefault($paramIn, 'audit_id');
        $status  = $this->processingDefault($paramIn, 'status');
        $reason  = $this->processingDefault($paramIn, 'cancel_reason');
        $reason  = addcslashes(stripslashes($reason), "'");

        //[2]验证数据
        $resignInfo = $this->getResignDetail(['resign_id' => $auditId]);

        //验证是不是撤销操作
        if ($status != enums::$audit_status['revoked']) {
            throw new BusinessException($this->getTranslation()->_('4018'));
        }

        //验证审批状态
        //只有待审批的、审批通过的可以撤销
        if (!in_array($resignInfo['status'], [
            enums::$audit_status['panding'],
            enums::$audit_status['approved'],
            enums::$audit_status['timedout'],
        ])) {
            throw new BusinessException($this->getTranslation()->_('4018'));
        }

        // 新老数据
        $auditApply = AuditApplyModel::findFirst([
            'conditions' => ' biz_value = :value: and biz_type = :type:',
            'bind'       => [
                'value' => $auditId,
                'type'  => AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT,
            ],
        ]);
        if (empty($auditApply)) {
            throw new \Exception($this->getTranslation()->_('4018'));
        }

        $approvalServer = new ApprovalServer($this->lang, $this->timezone);
        if ($auditApply->submitter_id == $staffId) {
            $approvalServer->cancel($auditId, AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT, $reason, $staffId);
        } else {
            $approvalServer->approvalCancel($auditId, AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT, $reason, $staffId);
        }

        return $this->checkReturn([]);
    }

    /**
     * 超时离职
     * @param array $paramIn 传入参数
     * @return bool
     */
    public function overtime($paramIn)
    {
        $resignIds = $this->processingDefault($paramIn, 'resign_ids', 3);

        if (empty($resignIds)) {
            return false;
        }

        foreach ($resignIds as $resignId) {
            $resignInfo = $this->getResignDetail(['resign_id' => $resignId]);
            //[3]处理更新
            //主状态变更为撤销
            //staff_audit_approval 当前107 106的变更为 104
            //staff_audit_union 107 106 变更为104
            //追加日志
            $logInsertData = [
                'staff_id'       => $resignInfo['submitter_id'],
                'type'           => AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT,
                'original_type'  => $resignInfo['status'] ?? 0,
                'to_status_type' => enums::$audit_status['timedout'],
                'original_id'    => $resignInfo['resign_id'],
                'operator'       => 10000,
                'operator_name'  => 'SYSTEM',
            ];

            //更新撤销原因、离职申请审批状态
            $paramData = [
                'resign_id' => $resignInfo['resign_id'],
                'status'    => enums::$audit_status['timedout'],
            ];
            $result    = (new OtherRepository())->cancelApproval($logInsertData, $paramData, 'staff_resign',
                'resign_id');
            if (!$result) {
                continue;
            }

            //同步状态
            $this->syncApproval([
                'approval_status' => enums::$audit_status['timedout'],
                'submitter_id'    => $resignInfo['submitter_id'],
                'leave_date'      => $resignInfo['leave_date'],
            ]);

            //提交申请就同步数据
            $postData = [
                'staff_info_id'       => $resignInfo['submitter_id'],
                'wait_leave_date'     => $resignInfo['leave_date'],
                'leave_reason'        => $resignInfo['reason'],
                'leave_type'          => 1,
                'leave_source'        => 5, //backyard提交申请
                'leave_reason_remark' => $resignInfo['remark'],
            ];

            $fle_rpc = (new ApiClient('hr_rpc', '', 'hr_staff_wait_leave', $this->lang));
            $fle_rpc->setParams($postData);
            $resultData = $fle_rpc->execute();
            $this->getDI()->get('logger')->write_log('同步离职申请数据到BI:request:' . json_encode($postData) . ';response:' . json_encode($resultData),
                'info');
        }
        return true;
    }


    /**
     * 获取离职详情
     */
    public function getResignDetail($paramIn = [])
    {
        //[1]参数定义
        $resignId = $this->processingDefault($paramIn, 'resign_id', 2);
        //[2]获取详情
        $returnData                 = (new ResignRepository($this->lang,
            $this->timezone))->getResignInfo(['id' => $resignId]);
        $returnData['signing_date'] =  $returnData['hire_date'];
        return $returnData;
    }

    /**
     * 可视化超时，按照表单去超时
     * @param $audit_id
     * @return string
     */
    public function getAuditFormOvertimeDate($audit_id): string
    {
        return parent::getAuditFormOvertimeDate($audit_id);
    }
}
