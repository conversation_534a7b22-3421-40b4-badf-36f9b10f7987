<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\Enums\HrStaffPositionEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\Enums\SettingEnvEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftOperateLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditImageModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveAddLogModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveProperty;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\ResumeRecommendRepository;
use FlashExpress\bi\App\Repository\StaffAuditToolLog;
use FlashExpress\bi\App\Repository\StaffOffDayRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StaffWorkAttendanceRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;
use FlashExpress\bi\App\Server\Penalty\PunchOutPenaltyServer;
use FlashExpress\bi\App\Server\Vacation\MilitaryServer;
use FlashExpress\bi\App\Server\Vacation\TrainingServer;
use FlashExpress\bi\App\Traits\AuditStateTrait;
use Phalcon\Db\Column;
use FlashExpress\bi\App\Server\OsStaffServer;


class AuditServer extends AuditBaseServer
{
    use AuditStateTrait;
    protected $re;
    protected $hc;
    public $timezone;
    public $country_server;
    protected $month_arr = enums::LEAVE_INVALID_MONTH;
    protected $last_day = enums::LEAVE_INVALID_DATE;
    //申请区间内 存在休息日 自动扣除的请假类型  适用假期：1 年假,事假,10 婚嫁,11 出家假,9 个人培训假（不带薪）,3 病假,8 绝育手术假,16 公司培训假,7 家人去世假
    //新增 无薪病假（不带薪）18、陪产假 5、不带薪事假 12  19 跨年探亲假 20 单亲育儿假 22家庭暴力假 23紧急假
    public $sub_day = array(
        1,
        2,
        3,
        7,
        8,
        //9 个人培训假剔除跳过休息日行列,
        10,
        11,
        16,
        //12 不带薪事假剔除跳过休息日行列,
        5,
        //18, 不带薪病假剔除跳过休息日行列,
        19
    ,20,22,23//菲律宾项目新增
    ,25,26,
    );

    protected $forbidden = array(1, 5, 9, 10, 11, 19);//试用期不能申请的类型
    protected $time_limit_start = array(3, 7, 15, 16, 18,20,21,22,25,26);//限制开始时间 只能当天以后
    public $one_time = array(
        enums::LEAVE_TYPE_5,enums::LEAVE_TYPE_11,enums::LEAVE_TYPE_10,enums::LEAVE_TYPE_20,
    );//入职以来 只能申请一次
    public $one_send = array(enums::LEAVE_TYPE_17,enums::LEAVE_TYPE_16,enums::LEAVE_TYPE_6);//入职以来 限制总额不限制次数
    public static $c_days = 20;//c 级别 20天

    public $staffId;
    public $staffInfo;
    public $remainData;//额度信息数据
    public $sum_days;//已经申请的天数
    public $limit_array;//有额度的类型数据
    public $limit_types;//有额度的类型list
    public $oneTypes;//只发放一次的类型
    public $yearTypes = [];//已经固化到remain的 按年发放类型
    public $unpaidInfo;//不带薪病假 数据 泰国定制逻辑

    public $reissueSpecialDepartment;//泰国特殊部门需要1天超时关闭 并且补卡时间只有当天 请假不能请过去时间 https://flashexpress.feishu.cn/docx/Ccrad5zjPoFVyzxRqhUctPdcnWh
    public $timeOut;//超时关闭时间

    const CAN_LEAVE_TODAY = 1;


    public static $paramIn;
    /**
     * @var OtherRepository
     */
    private $other;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
        $this->other = new OtherRepository($timezone, $lang);
        $this->hc = new HcRepository($timezone);
        $this->re       = [
            'staff'      => new StaffRepository(),
            'audit'      => new AuditRepository($this->lang),
            'overtime'   => new OvertimeRepository($timezone),
            'department' => new DepartmentRepository(),
            'public'     => new PublicRepository(),
        ];

    }

    public function setStaffInfo($staffInfo)
    {
        $this->staffInfo = $staffInfo;
    }


    /**
     * 补卡添加
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function reissueCardAdd($paramIn = [], $userinfo)
    {
        //[1]参数定义
        $staffId         = $this->processingDefault($paramIn, 'staff_id', 2);
        $reissueCardDate = $this->processingDefault($paramIn, 'reissue_card_date');
        $attendanceType  = $this->processingDefault($paramIn, 'attendance_type', 2);
        $auditReason     = $this->processingDefault($paramIn, 'audit_reason');
        $auditReason = strip_tags(addcslashes(stripslashes($auditReason), "'"));
        $dayType     = intval($paramIn['day_type']);
        $date_at     = date('Y-m-d', strtotime($paramIn['date_at']));
        $imagePathArr   = $this->processingDefault($paramIn, 'image_path');

        //检验工号 是否存在 hrs
        $staff_re = new StaffRepository($this->lang);
        $staff_info = $staff_re->getStaffPosition($staffId);
        if (empty($staff_info)) {
            throw new ValidationException('invalid staff id');
        }

        if ($staff_info['is_sub_staff'] == 1) {
            throw new ValidationException($this->getTranslation()->_('sub_staff_disable'));
        }
        if ($this->checkStaffFormal($staff_info)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('os_or_franchisee_staff_disable'));
        }

        //[2]用户校验
        $staffData          = $staff_re->checkoutStaff($staffId);//fle
        if (empty($staffData)) {
            throw new ValidationException($this->getTranslation()->_('1001'));
        }

        if (empty($paramIn['date_at']) || empty($dayType)) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $_organization_type = isset($staffData['organization_type']) ? $staffData['organization_type'] : 0;
        $_organization_id   = isset($staffData['organization_id']) ? $staffData['organization_id'] : '';
        // 新增需求逻辑 网点 TH01470301 和 TH02030204 不允许补卡 20190926 朝晖确定改为所有hub网点
        //新增需求 HUB 8/OS 9/B-HUB 12网点类型为必填，其他网点类型为非必填。 https://l8bx01gcjr.feishu.cn/docs/doccnlc6EBnUsMntDVjAZQSOYAh#
        $store_server = new SysStoreServer($this->lang);
        $store_where = '8,9,12';
        $hub_store    = $store_server->getHubStore($store_where);
        $extend = array();
        if (($_organization_type == 1 && $_organization_id) && in_array($_organization_id, $hub_store)) {
            $extend['store_id'] = $_organization_id;
            //新需求 hub相关网点可以走补卡申请 必须上传图片
            if (empty($imagePathArr)) {
                throw new ValidationException($this->getTranslation()->_('2106'));
            }
            if (count($imagePathArr) > 3) {
                throw new ValidationException('at most 3 photos');
            }
        }

        $reissue_tmp     = strtotime($reissueCardDate);
        $reissueCardDate = date('Y-m-d H:i:s', $reissue_tmp);
        if ($dayType == 2) {//选的次日 打卡 需加24小时
            $reissueCardDate = date('Y-m-d H:i:s', $reissue_tmp + 24 * 3600);
        }

        //[3]校验选择时间内是否有考勤记录
	    $this->checkDataByDate($paramIn,$staff_info);

        $extend['time_out'] = date('Y-m-d 00:00:00',strtotime('+3 day'));

        $serialNo  = $this->getID();
        $insetData = [
            'staff_info_id'     => $staffId,
            'reissue_card_date' => $reissueCardDate,
            'attendance_type'   => $attendanceType,
            'audit_reason'      => $auditReason,
            'attendance_date'   => $date_at,
            'status'            => enums::$audit_status['panding'],
            'audit_type'        => enums::$audit_type['AT'],
            'serial_no'         => (!empty($serialNo) ? 'AT' . $serialNo : NULL),
            'time_out'          => $extend['time_out'],
        ];
        //个人代理
        if(in_array($staff_info['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            $insetData['source_type'] = StaffAuditModel::SOURCE_TYPE_UNPAID;
        }

        $db = StaffAuditModel::beginTransaction($this);
        try {
            $db->insertAsDict("staff_audit", $insetData);
            $audit_id = $db->lastInsertId();
            //插入图片
            if (!empty($imagePathArr)) {
                $img_model = new StaffAuditImageModel();
                foreach ($imagePathArr as $k => $v) {
                    $img_clone = clone $img_model;
                    $row = [
                        'audit_id'   => $audit_id,
                        'image_path' => $v,
                    ];
                    $img_clone->create($row);
                }
            }
            $rst = (new ApprovalServer($this->lang, $this->timezone))->create($audit_id, enums::$audit_type['AT'], $staffId,null,$extend);
            if (!$rst ) {
                $this->logger->write_log("reissueCardAdd log ".json_encode($rst,JSON_UNESCAPED_UNICODE),'info');
                $db->rollBack();
                throw new \Exception('audit staff insert reissueCardAdd  workflow fail '. $rst );
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log('reissue_card msg '. $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString(), "error");
            return $this->checkReturn(-3, $this->getTranslation()->_('1006'));
        }

        return $this->checkReturn([]);
    }


    /**
     * 校验选择时间内是否有考勤记录
     * @Access  public
     * @param  $paramIn
     * @param $staffInfo
     * @return true
     * @throws ValidationException
     */
    public function checkDataByDate($paramIn,$staffInfo)
    {
        $t = $this->getTranslation();
        //个人代理 不能申请 除了马来 泰国（重写）
        $t_key_suffix = '';
        if(in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            if(!isCountry('PH')){
                throw new ValidationException($t->_('reissue_wrong_hire_type'));
            }
            //菲律宾的 翻译后缀
            $t_key_suffix = '_unpaid_ph';
        }

        $current_date = date('Y-m-d', time());
        //[1]参数定义
        $staffId         = $this->processingDefault($paramIn, 'staff_id', 2);
        $reissueCardDate = $this->processingDefault($paramIn, 'reissue_card_date');
        $attendanceType  = $this->processingDefault($paramIn, 'attendance_type', 2);
        $day             = $paramIn['date_at'] = date('Y-m-d', strtotime($paramIn['date_at']));
        $dayType         = intval($paramIn['day_type']);// 1-当天 2-次日
        $resource        = [
            1 => 'started_at',
            2 => 'end_at',
        ];
        $reissue_tmp     = strtotime($reissueCardDate);//前端 参数有可能没有前导0 格式化一下
        $reissueCardDate = date('Y-m-d H:i:s', $reissue_tmp);
        if ($dayType == 2) {//选的次日 打卡 需加24小时 前端做比较麻烦
            $reissue_tmp     = $reissue_tmp + 24 * 3600;
            $reissueCardDate = $paramIn['reissue_card_date'] = date('Y-m-d H:i:s', $reissue_tmp);
        }
        //实际申请的 时间 日期
        $act_date = date('Y-m-d', $reissue_tmp);
        $param    = [
            'staff_id'        => $staffId,
            'attendance_type' => $attendanceType,
            'date_at'         => $day,
        ];
        $auditRe = new AuditRepository($this->lang);
        //[2]查询时间内是否有补卡记录
        $existAuditInfo = $auditRe->getAttendanceCardData($param);
        if (!empty($existAuditInfo)) {
            throw new ValidationException($t->_(($attendanceType == 1 ? '1004' : '1005').$t_key_suffix));
        }

        //检测日期[1是否大于当前时间 2是否三天内]
        if (strtotime($reissueCardDate) > time()) {
            throw new ValidationException($t->_('1008'.$t_key_suffix));
        }
        if (strtotime($day) < (time() - 3600 * 72)) {
            throw new ValidationException($t->_('1007'));
        }

        //[3]查询本月打卡次数|超过三次补卡失败 查询时间内打卡记录  4月10日产品去掉每月补卡限制-> 20190729 加上限制（只针对上班打卡） 2019-10-19 新增逻辑 上班班 加一起不能超过3次
        $punchMonthCardNum = $auditRe->getAttendanceCarMonthData($paramIn);
        if ($punchMonthCardNum >= 3) {
            throw new ValidationException($t->_('1025'.$t_key_suffix));
        }

        //如果存在补卡类型记录记录 不能补卡
        //新需求 即使存在记录 也可以补卡 需要判断补卡时间 是不是 异常 https://l8bx01gcjr.feishu.cn/docs/doccn2Yqjmknumh6s70wKNspZ8b
        //获取班次
        $shiftServer                 = new HrShiftServer();
        $shift_info                  = $shiftServer->getShiftInfos($staffId, [$day]);
        $shift_info                  = $shift_info[$day] ?? [];
        $att_re                      = new StaffWorkAttendanceRepository($this->lang);
        $punchCardData               = $att_re->getPunchCardData($param);
        $punchCardData['started_at'] = empty($punchCardData['started_at']) ? '' : date('Y-m-d H:i:s',
            strtotime($punchCardData['started_at']));
        $punchCardData['end_at']     = empty($punchCardData['end_at']) ? '' : date('Y-m-d H:i:s',
            strtotime($punchCardData['end_at']));
        $add_hour                    = $this->getDI()['config']['application']['add_hour'];
        if (!empty($punchCardData['started_at']) || !empty($punchCardData['end_at'])) {
            //上班 班次时间判断
            if (!empty($shift_info['start']) && $attendanceType == 1) {
                $shift_start = date('Y-m-d H:i:s', strtotime($shift_info['start_datetime']) - $add_hour * 3600);
                //判断 没有迟到 返回异常提示 确定没迟到是否要补卡
                if (!empty($punchCardData[$resource[$attendanceType]]) && $punchCardData[$resource[$attendanceType]] <= $shift_start && empty($paramIn['is_submit'])) {
                    //有打卡时间了 还补卡的二次确认
                    throw new ValidationException($t->_('no_need_reissue_notice'.$t_key_suffix,['time'=>show_time_zone($punchCardData[$resource[$attendanceType]])]), 10086);
                }
            }
            //下班 班次时间判断
            if (!empty($shift_info['end']) && $attendanceType == 2) {
                $shift_end = date('Y-m-d H:i:s', strtotime($shift_info['end_datetime']) - $add_hour * 3600);
                //判断 没早退
                if (!empty($punchCardData[$resource[$attendanceType]]) && $punchCardData[$resource[$attendanceType]] >= $shift_end && empty($paramIn['is_submit'])) {
                    throw new ValidationException($t->_('no_need_reissue_notice'.$t_key_suffix,['time'=>show_time_zone($punchCardData[$resource[$attendanceType]])]), 10086);
                }
            }
        }

        //新增 出差期间 打卡审批 逻辑判断 https://l8bx01gcjr.feishu.cn/docs/doccnyA3C6x0DsUA3ZGlztvhDLf#
        $att_bus_server = new AttendanceBusinessServer($this->lang, $this->timezone);
        $att_bus_info   = $att_bus_server->find_by_date($staffId, $day);
        if (!empty($att_bus_info) && !in_array($att_bus_info['status'], [1, 2]))//如果非有效状态 视为无记录
        {
            $att_bus_info = [];
        }
        if (!empty($att_bus_info)) {
            if ($attendanceType == 1 && !empty($att_bus_info['start_time']) && !empty($shift_info['start'])) {
                //判断同上
                $shift_start = $shift_info['start_datetime'];
                //判断 没有迟到 返回异常提示 确定没迟到是否要补卡
                if (!empty($att_bus_info['start_thai']) && $att_bus_info['start_thai'] <= $shift_start && empty($paramIn['is_submit'])) {
                    throw new ValidationException($t->_('no_need_reissue_notice'.$t_key_suffix,['time'=>$att_bus_info['start_thai']]), 10086);
                }
            }
            if ($attendanceType == 2 && !empty($att_bus_info['end_time']) && !empty($shift_info['start'])) {
                //判断同上
                $shift_end = $shift_info['end_datetime'];
                //判断 没早退
                if (!empty($att_bus_info['end_thai']) && $att_bus_info['end_thai'] >= $shift_end  && empty($paramIn['is_submit'])) {
                    throw new ValidationException($t->_('no_need_reissue_notice'.$t_key_suffix,['time'=>$att_bus_info['end_thai']]), 10086);
                }
            }
            $punchCardData['started_at'] = $att_bus_info['start_time'];
            $punchCardData['end_at']     = $att_bus_info['end_time'];
        }

        /**
         * 补卡时间和出勤日期必须是同一天
         * 如果出勤日期有下班打卡记录，补卡时间必须早于下班打卡时间
         */
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        //[4]校验是否可以打卡[本天不可以补上班下班卡]
        if ($attendanceType == 1) {//上班补卡
            if ($dayType == 2) {
                //上班打卡时间和出勤日期必须是同一天
                throw new ValidationException($t->_('repair_same_day'.$t_key_suffix));
            }

            //没有考勤记录 获取是否有申请补卡记录
            //上班打卡时间必须早于当天下班打卡时间
            if (!empty($punchCardData) && !empty($punchCardData['end_at'])) {
                if ($reissue_tmp >= (strtotime($punchCardData['end_at']) + $add_hour * 3600)) {
                    throw new ValidationException($t->_('repair_early'.$t_key_suffix));
                }

                //如果[跨天]判断 相差 要小于16小时 不跨天 不判断16小时 改为22小时
                $act_down_date = date('Y-m-d', strtotime($punchCardData['end_at']) + $add_hour * 3600);
                if ($day != $act_date || $day != $act_down_date) {
                    if ((strtotime($punchCardData['end_at']) + ($add_hour * 3600) - $reissue_tmp) > (22 * 3600)) {
                        throw new ValidationException($t->_('repair_limit_16'.$t_key_suffix));
                    }
                }
            }
            //当天下班补卡申请判断
            $up_param = [
                'staff_id'        => $staffId,
                'attendance_type' => 2,
                'date_at'         => $day,
            ];
            $up_info  = $auditRe->getAttendanceCardData($up_param);
            if (!empty($up_info)) {//上班打卡时间必须早于下班打卡时间
                if (strtotime($up_info['reissue_card_date']) <= $reissue_tmp) {
                    throw new ValidationException($t->_('repair_early'.$t_key_suffix));
                }

                //跨天判断 相差 要小于16小时
                $apply_down_date = substr($up_info['reissue_card_date'], 0, 10);
                if ($day != $act_date || $day != $apply_down_date) {
                    if ((strtotime($up_info['reissue_card_date']) - $reissue_tmp) > (22 * 3600)) {
                        throw new ValidationException($t->_('repair_limit_16'.$t_key_suffix));
                    }
                }
            }

            //获取前一天的打卡记录
            $yesterdayParam         = [
                'staff_id' => $staffId,
                'date_at'  => date('Y-m-d', strtotime($day) - 24 * 3600),
            ];
            $punchCardYesterdayData = $att_re->getPunchCardData($yesterdayParam);
            //没有前一天打卡记录 校验前一天的 补卡申请
            if (!empty($punchCardYesterdayData['end_at'])) {
                //上班打卡时间不得早于前一天的下班打卡时间
                if ($reissue_tmp <= (strtotime($punchCardYesterdayData['end_at']) + $add_hour * 3600)) {
                    throw new ValidationException($t->_('repair_yesterday'.$t_key_suffix));
                }
            }
            //前一天 下班补卡申请判断
            $yes_param = [
                'staff_id'        => $staffId,
                'attendance_type' => 2,
                'date_at'         => date('Y-m-d', strtotime($day) - 24 * 3600),
            ];
            $yes_info  = $auditRe->getAttendanceCardData($yes_param);
            if (!empty($yes_info)) {//上班打卡时间必须早于下班打卡时间
                if (strtotime($yes_info['reissue_card_date']) >= $reissue_tmp) {
                    throw new ValidationException($t->_('repair_yesterday'.$t_key_suffix));
                }
            }

            //看用不用补卡 上班打过卡 并且是当天 才能补卡
            if (empty($punchCardData['started_at']) && empty($punchCardData['end_at']) && $day == $current_date && empty($paramIn['is_confirm'])) {
                throw new ValidationException($t->_('reissue_up_notice'.$t_key_suffix), 10087);
            }

            //下班卡不为空 判断补卡时长
            if (!empty($punchCardData['end_at']) && empty($paramIn['confirm_10088'])) {
                $isLiveJob = (new StaffServer())->checkIsLiveJob($staffInfo,$day);
                $seconds = strtotime(show_time_zone($punchCardData['end_at'])) - strtotime($reissueCardDate);
                if (!$isLiveJob && $seconds < CheckPunchOutServer::ALL_DAY_SHIFT_DURATION * 3600) {
                    $time_str = $t->_('early_off_hour', ['x' => CheckPunchOutServer::ALL_DAY_SHIFT_DURATION]);
                    throw new ValidationException($t->_('attendance_duration_less' . $t_key_suffix,
                        ['time' => $time_str]), 10088);
                }
            }


        } elseif ($attendanceType == 2) { //下班补卡
            //没有当天考勤 检验当天 上班补卡申请  和 后一天上班申请
            if (!empty($punchCardData) && !empty($punchCardData['started_at'])) {
                //下班打卡时间必须晚于上班打卡时间
                if ($reissue_tmp <= (strtotime($punchCardData['started_at']) + $add_hour * 3600)) {
                    throw new ValidationException($t->_('repair_late'.$t_key_suffix));
                }
                //下班补卡时间距离上班打卡时间不得超过16小时 跨天
                $act_up_date = date('Y-m-d', strtotime($punchCardData['started_at']) + $add_hour * 3600);
                if ($day != $act_date || $day != $act_up_date) {
                    if (($reissue_tmp - (strtotime($punchCardData['started_at']) + $add_hour * 3600)) >= (22 * 3600)) {
                        throw new ValidationException($t->_('repair_limit_16'.$t_key_suffix));
                    }
                }
            }
            //是否申请过 上班打卡 用申请时间做比较
            $down_param = [
                'staff_id'        => $staffId,
                'attendance_type' => 1,
                'date_at'         => $day,
            ];
            $down_info  = $auditRe->getAttendanceCardData($down_param);
            if (!empty($down_info)) {//上班打卡时间必须早于下班打卡时间
                if (strtotime($down_info['reissue_card_date']) >= $reissue_tmp) {
                    throw new ValidationException($t->_('repair_late'.$t_key_suffix));
                }

                $app_up_date = substr($down_info['reissue_card_date'], 0, 10);
                if ($day != $act_date || $day != $app_up_date) {
                    //判断 相差 要小于16小时 跨天
                    if (($reissue_tmp - strtotime($down_info['reissue_card_date'])) > (22 * 3600)) {
                        throw new ValidationException($t->_('repair_limit_16'.$t_key_suffix));
                    }
                }
            }
            //补卡时间为出勤日期的次日，补卡时间必须早于同日期的上班打卡时间
            $tomorrowParam = [
                'staff_id' => $staffId,
                'date_at'  => date('Y-m-d', strtotime($day) + 24 * 3600),
            ];
            //获取申请日期第二天的打卡记录
            $tomorrow_info = $att_re->getPunchCardData($tomorrowParam);
            if (!empty($tomorrow_info) && !empty($tomorrow_info['started_at'])) {
                //下班打卡时间必须晚于上班打卡时间
                if ($reissue_tmp >= (strtotime($tomorrow_info['started_at']) + $add_hour * 3600)) {
                    throw new ValidationException($t->_('repair_tomorrow'.$t_key_suffix));
                }
            }
            //是否申请过 后一天上班打卡 用申请时间做比较
            $to_param = [
                'staff_id'        => $staffId,
                'attendance_type' => 1,
                'date_at'         => date('Y-m-d', strtotime($day) + 24 * 3600),
            ];
            $to_info  = $auditRe->getAttendanceCardData($to_param);
            if (!empty($to_info)) {//第二天上班打卡时间必须晚于下班打卡时间
                if (strtotime($to_info['reissue_card_date']) <= $reissue_tmp) {
                    throw new ValidationException($t->_('repair_tomorrow'.$t_key_suffix));
                }
            }
            //看是否还可以打卡 无需补卡
            if (!empty($punchCardData['started_at']) &&  (min(strtotime($punchCardData['started_at']) + $add_hour * 3600,strtotime($shift_info['start_datetime'])) + 22 * 3600) > time() && empty($punchCardData['end_at']) && empty($paramIn['is_confirm'])) {
                throw new ValidationException($t->_('reissue_low_notice'.$t_key_suffix), 10087);
            }
            //上班卡不为空 判断补卡时长
            if (!empty($punchCardData['started_at']) && empty($paramIn['confirm_10088'])) {
                //获取班次时长
                $shiftDuration = $this->getLiveJobShiftDuration($staffInfo, $day,
                    show_time_zone($punchCardData['started_at'])) ?: CheckPunchOutServer::ALL_DAY_SHIFT_DURATION * 3600;
                $seconds       = strtotime($reissueCardDate) - strtotime(show_time_zone($punchCardData['started_at']));
                if ($seconds < $shiftDuration) {
                    $time_str = $t->_('early_off_hour', ['x' => round($shiftDuration / 3600, 2)]);
                    throw new ValidationException($t->_('attendance_duration_less' . $t_key_suffix,
                        ['time' => $time_str]), 10088);
                }
            }
        }


        return true;
    }

    /**
     * @param $staffInfo
     * @param $date_at
     * @param $attendance_started_at
     * @return false|int
     * @throws Exception
     */
    public function getLiveJobShiftDuration($staffInfo,$date_at,$attendance_started_at)
    {
        $isLiveJob = (new StaffServer())->checkIsLiveJob($staffInfo,$date_at);
        if($isLiveJob && $attendance_started_at){
            $settingConfig                         = (new SettingEnvServer())->getMultiEnvByCode([
                'day_shift_duration',
                'night_shift_duration',
            ]);
            //本地时区
            $hour      = date( 'H',strtotime($attendance_started_at));
            $dayHour   = $settingConfig['day_shift_duration'];
            $nightHour = $settingConfig['night_shift_duration'];
            //4-16点 白班
            if ($hour >= 4 && $hour < 16) {
                $shiftDuration = $dayHour;
            } else {
                $shiftDuration = $nightHour;
            }
            return $shiftDuration * 3600;
        }
        return false;
    }


    /**
     * 请假添加 泰国请假没有对接可视化 已经迁移至国家目录
     * @Access  public
     * @Param   request
     * @Return  array
     * 废弃 不用了 用各个国家的
     * ！！！ 涉及表 staff_audit 主表 staff_audit_leave_split 拆分表
     */
    public function leaveAdd($paramIn = [])
    {
        return ;
    }

    /**
     * 校验员工formal 如果是外协、加盟商无法提交申请
     * @param $staff_info
     * @return bool
     */
    public function checkStaffFormal($staff_info): bool
    {
        $result = false;
        if (in_array($staff_info['formal'], [HrStaffInfoModel::FORMAL_0, HrStaffInfoModel::FORMAL_FRANCHISEE_OTHER, HrStaffInfoModel::FORMAL_FRANCHISEE])) {
            $result = true;
        }
        return $result;
    }

    /**
     *
     * 验证请假规则
     * 泰国在用
     * @param array $paramIn
     * @return array
     */
    public function checkLeaveDataByDate($paramIn = [])
    {
        //[1]参数定义 leave_start_time 和 leave_end_time 是 ymd 类型
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $imagePathArr   = $this->processingDefault($paramIn, 'image_path');
        $leave_days     = $paramIn['leave_day'];


        //开始时间 结束时间 验证
        if ($leaveEndTime < $leaveStartTime) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
        }
        if ($leaveEndTime == $leaveStartTime) {
            if ($leaveStartType > $leaveEndType) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
            }
        }
        //图片是否必填
        $leaveProperty = StaffLeaveProperty::findFirst([
            'conditions' => "leave_type = :leave_type:",
            'bind'       => ['leave_type' => $leaveType],
        ]);
        if (!empty($leaveProperty) && $leaveProperty->is_need_img == 1 && empty($imagePathArr)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('22328_leave_application'));
        }

        //图片最多限制5张
        if (!empty($imagePathArr) && count($imagePathArr) > 5) {
            return $this->checkReturn(-3, $this->getTranslation()->_('at most 5 photos'));
        }

        //[3]查询请假记录
        $audit_model = new AuditRepository($this->lang);
        $param       = [
            'staff_id'         => $staffId,
            'leave_start_time' => date('Y-m-d', strtotime($leaveStartTime)),
            'leave_start_type' => $leaveStartType,
            'leave_end_time'   => date('Y-m-d', strtotime($leaveEndTime)),
            'leave_end_type'   => $leaveEndType,
        ];
        $levelData   = $this->checkExistLeave($param);
        if (!empty($levelData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1012'));
        }


        //新增规则 二期需求 https://shimo.im/sheets/94eQG9bXB8oMZlnT/MODOC
        //试用期：从入职第1天开始算，第1-90天
        //不能申请的假期类型： 1年假，2带薪事假，5陪产假，9个人受训假，10婚假，11出家假 --  新版需求把 6 8 去掉了 后来又把7 去掉了
        //新增需求 试用期判断 不用固定120天 https://l8bx01gcjr.feishu.cn/docs/doccne9Sx3V9c0YesAU97rFTkBb hr_probation 状态为4的字段
        $forbidden = $this->forbidden;
        $leaveType = intval($leaveType);

        $leave_lang = $audit_model::$leave_type;
        //获取job title name 入职时间
        $staff_model = new StaffRepository($this->lang);
        $staff_info  = $staff_model->getStaffPosition($staffId);
        $hire_date   = $staff_info['hire_date'];
        $entry       = strtotime($staff_info['hire_date']);
        if (empty($staff_info) || empty($hire_date)) {
            return $this->checkReturn(-3, 'no permission to apply');
        }

        //类型验证
//        $checkParam['staff_id']            = $staffId;
//        $checkParam['user_info']           = $staff_info;
//        $checkParam['is_check_permission'] = true;
        $typeData                          = $this->staffLeaveType($staff_info);
        if (empty($typeData)) {
            throw new ValidationException('wrong leave type');
        }
        if (!in_array($leaveType, array_keys($typeData)) && $leaveType != enums::LEAVE_TYPE_15) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004').$this->getTranslation()->_($leave_lang[$leaveType]));
        }


        $leave_server = new LeaveServer($this->lang,$this->timezone);
        //新增 请假白名单 不限制入职天数 修改为 查看是否入职
        if (in_array($leaveType, $forbidden) && $hire_date >= '2020-06-13 00:00:00') {
            //试用期状态，1试用期，2已通过，3未通过，4已转正 适用于 xxx 之后 之前的数据默认都转正 因为没管旧数据
            if($staff_info['status'] != 4 ){
                $message = str_replace('leave_type', $this->getTranslation()->_($leave_lang[$leaveType]), $this->getTranslation()->_('probation_limit'));
                return $this->checkReturn(-3, $message);
            }
            if (!empty($staff_info['formal_at']) && $leaveStartTime < $staff_info['formal_at']) {
                $message = $this->getTranslation()->_('probation_before_limit');
                return $this->checkReturn(-3, $message);
            }

        }

        //除了 3，15，16 类型 其他类型必须 大于当前时间  bi工具 不需要时间验证 新增可以候补家人去世假类型 http://193x782t53.imwork.net:29667/zentao/story-view-3638.html 新增无薪病假
        // $time_limit_start 表示 除了这些假期类型 其余的 必须大于当前时间
        if (!in_array($leaveType, $this->time_limit_start) && empty($paramIn['is_bi'])) {
            //请假日期必须大于等于当前日期
            if ($leaveStartTime < date('Y-m-d', time())) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1018'));
            }
        }

        //19310 是否可以今天请假，1代表可以申请今天，0代表不能申请今天
        $canleavetoday = (new SettingEnvServer())->getSetVal('canleavetoday');
        if ($leaveType == enums::LEAVE_TYPE_40 && empty($paramIn['is_bi'])) {
            //请假日期必须大于等于当前日期
            $start_day_leave = !empty($canleavetoday) && $canleavetoday == self::CAN_LEAVE_TODAY ? date('Y-m-d') : date('Y-m-d', strtotime("+1 day"));

            if (strtotime($leaveStartTime) < strtotime($start_day_leave) && $start_day_leave == date('Y-m-d')) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1018'));
            }

            if (strtotime($leaveStartTime) < strtotime($start_day_leave) && $start_day_leave == date('Y-m-d', strtotime("+1 day"))) {
                return $this->checkReturn(-3, $this->getTranslation()->_('can_leave_today_outweigh'));
            }
        }


        // 需要先检验 次数 和总额度 是否非法 然后再拆分  一次限制的年假(4,5,6,8,10,11) 去掉 产检一次性限制
        if (in_array($leaveType, $this->one_time)) {//http://193x782t53.imwork.net:29667/zentao/story-view-3112.html

            //验证 员工入职以后 是否请过
            $remain_info = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                'bind' => ['staff_info_id' => $staffId,'leave_type' => $leaveType],
            ]);

            if (!empty($remain_info))
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

            //是否超出 限定 额度
            $limit_days = $audit_model->get_leave_days_by_type($leaveType);
            if (!empty($limit_days) && $leave_days > $limit_days)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
        }
        //不限次数 的 总和 限制 假期 新增假期类型 16 员工培训假 不限次数 限制总和
        if (in_array($leaveType, $this->one_send)) {
            //验证 员工入职以后 是否请过
            $remain_info = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                'bind' => ['staff_info_id' => $staffId,'leave_type' => $leaveType],
            ]);

            if (empty($remain_info)) {
                $remain_info = $leave_server->init_leave(['staff_info_id' => $staffId, 'leave_type' => $leaveType]);
            }

            if (empty($remain_info)) {
                return $this->checkReturn(-3, $this->getTranslation()->_("leave_limit"));
            }

            if ($remain_info->days <= 0 || ($remain_info->days - $leave_days) < 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }

        }

        //其他类型 特殊定制 逻辑限制

        //新冠相关假期 一次申请 最大 30天限制  可申请多次
        if(in_array($leaveType,array(25,26)) && $leave_days > 30){
            return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit_30'));
        }

        //无薪病假什么需求 新加了一个 逻辑 不能申请跨年 没有测试 产品 刘佳雪 https://l8bx01gcjr.feishu.cn/docs/doccnva7Hm60yxw5D3hot00nGsb
        //date('Y',strtotime($leaveStartTime)) != date('Y',strtotime($leaveEndTime))
        // 15259 不能请无薪病假了
        if ($leaveType == enums::LEAVE_TYPE_18) {
            return $this->checkReturn(-3, $this->getTranslation()->_('leave_18_over_year'));
        }

        //实习生也没权限申请带薪事假
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_INTERN && $leaveType == enums::LEAVE_TYPE_2){
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004').$this->getTranslation()->_($leave_lang[$leaveType]));
        }

        //带薪事假 一次一天
        if ($leaveType == enums::LEAVE_TYPE_2 && $leave_days > 1) {
            return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit_num',['num'=>1]));
        }


        //拼接 验证逻辑 参数 check_leave_type 额度验证
        $leave_info['leave_type'] = $leaveType;
        $leave_info['img']        = $imagePathArr;
        $leave_info['is_bi']      = empty($paramIn['is_bi']) ? '' : $paramIn['is_bi'];
        $leave_info['sub_type'] = empty($paramIn['sub_type']) ? 0 : $paramIn['sub_type'];
        $leave_info['leave_start_type'] = $leaveStartType;
        $leave_info['leave_end_type'] = $leaveEndType;
        $leave_info['holidays'] = $paramIn['holidays'];
        $leave_info['unpaid_confirm'] = $paramIn['unpaid_confirm'] ?? false;
        //本次请假  持续天数 如果跨年 //如果跨年 并且 不是年假 要拆开 每年分别是几天 和 每年的 开始结束时间 因为部分假期类型是按年度区分 今年可以请n天明年仍然可以请n天 还可以一次性跨年请2n天
        //！！新需求  如果部分请假类型其中包含法定假日 等休息日  需要扣除掉 不占用 该请假类型的额度
        //新需求 如果是产假 请跨年 不占用2年额度 按创建时间 每年一次
        $audit_server = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        if (date('Y', strtotime($leaveStartTime)) != date('Y', strtotime($leaveEndTime))
            && !in_array($leaveType,array(enums::LEAVE_TYPE_1,enums::LEAVE_TYPE_4,enums::LEAVE_TYPE_19))
            && !in_array($leaveType,$audit_server->one_time)
            && !in_array($leaveType,$audit_server->one_send)
        ) {
            //新需求 拆分时候 如果当天是休息日 剔除掉
            $date_array = $leave_server->format_year_date($leaveType,$leaveStartTime,$leaveEndTime,$paramIn['holidays']);
            $flag       = false;
            foreach ($date_array as $y) {//array('2019' => array('2019-12-30','2019-10-31') )) 有几年就有几个 正常应该就两年 没人能请一整年假期 还干不干了
                $need_days = 0;
                sort($y);
                $leave_info['leave_start_time'] = $y[0];//该年开始 时间
                $leave_info['leave_end_time']   = end($y);//该年 结束时间

                //拆分计算 本次请假 需要天数
                foreach ($y as $date) {
                    if ($date == $leaveStartTime) {
                        $need_days += ($leaveStartType == 1) ? 1 : 0.5;
                    } else if ($date == $leaveEndTime) {//最后一天
                        $need_days += ($leaveEndType == 2) ? 1 : 0.5;
                    } else {//区间内 肯定是一整天
                        $need_days += 1;
                    }
                }
                //验证每年的额度 是否 够用
                $flag = $this->check_leave_type($leave_info, $staff_info, $need_days);
                if ($flag !== true)
                    break;
            }

            if ($flag !== true)
                return $flag;
        } else {
            $need_days                      = $paramIn['leave_day'];
            $leave_info['leave_start_time'] = $leaveStartTime;
            $leave_info['leave_end_time']   = $leaveEndTime;
            $flag                           = $this->check_leave_type($leave_info, $staff_info, $need_days);
            if ($flag !== true)
                return $flag;
        }
        return $this->checkReturn(1);
    }

    /**
     *请假逻辑
     * 年假 额度 按创建时间计算  按当前时间 获取 今年年假 计算额度
     * 其他假期 如果跨年 先取出 开始年 和额度
     * 然后结束年 和 额度 两次计算 是否合法请假
     * 计算剩余额度 需要 获取 已经请的假期记录 如果存在跨年 需要再次拆分 留下 当前判定年的天数
     * !!泰国在用
     * @param $leave_info 截取后的 请假开始时间和结束时间
     * @param $staff_info
     * @param $need_days 截断后的 请假天数 比如 19年 1天 20年2天 总数是3天 need_days 两次传参 1， 2
     * @return bool
     */
    protected function check_leave_type($leave_info, $staff_info, $need_days)
    {
        $leaveType        = $leave_info['leave_type'];
        $leaveStartTime   = $leave_info['leave_start_time'];
        $leaveEndTime     = $leave_info['leave_end_time'];
        $entry            = strtotime($staff_info['hire_date']);
        $staffId          = $staff_info['staff_info_id'];
        $audit_model      = new AuditRepository($this->lang);
        $imagePathArr     = $leave_info['img'];
        $paramIn['is_bi'] = $leave_info['is_bi'];
        //额度计算
        //新需求 如果请假天数 跨年 需做新规则处理 如果是年假 按当前 年 计算
        $year  = date('Y', strtotime($leaveStartTime));
        $apply_month = date('m',strtotime($leaveStartTime));
        $apply_end_month = date('m',strtotime($leaveEndTime));
        $current_year = date('Y',time());
        //新算法 用拆分表
        if($leaveType == 1 && in_array($apply_month,$this->month_arr))//20年 请 21年 4月之后的年假 不需要取当年的剩余额度
            $applied_days = $audit_model->get_used_leave_days($staffId, $current_year, $leaveType);
        else
            $applied_days = $audit_model->get_used_leave_days($staffId, $year, $leaveType);

        //除了年假  之外  每种类型的额度
        $limit_days = $audit_model->get_all_leave_days();

        $sum_days             = $count_days = array();
        $sum_days[$leaveType] = $count_days[$leaveType] = 0;
        if (!empty($applied_days)) {
            $info                   = $applied_days[0];
            $sum_days[$leaveType]   = $info['num'];
            $count_days[$leaveType] = empty($info['audit_ids']) ? 0 : count(explode(',', $info['audit_ids']));
        }

        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $leave_server = Tools::reBuildCountryInstance($leave_server, [$this->lang, $this->timezone]);
        if ($leaveType == enums::LEAVE_TYPE_2) {//带薪事假

            //新版 获取 已经请的额度
            $apply_year = date('Y',strtotime($leaveStartTime));
            $left_days = $leave_server->check_personal_leave($staff_info,$apply_year);

            if ($need_days > $left_days) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }

        } else if ($leaveType == 3) {//[2]检测日期[结束时间必须大于开始时间] 病假可以选择近三天 bi工具排除

            if ($leaveStartTime < date('Y-m-d', time() - 48 * 3600) && empty($paramIn['is_bi'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1026'));
            }

            $used_days = $sum_days[$leaveType];
            //获取 已经用过的 25 26 类型的 假期天数 当年
            //新需求 隔离假不占用病假额度了
            $applied_days = $audit_model->get_used_leave_days($staffId, $year, '26');
            if(!empty($applied_days)){
                $covid_used = array_sum(array_column($applied_days,'num'));
                $used_days += $covid_used;
            }
            if ($used_days >= $limit_days[$leaveType] || ($need_days + $used_days) > $limit_days[$leaveType]) {
                $notice_msg = $this->getTranslation()->_('leave_limit');
                return $this->checkReturn(-3, $notice_msg);
            }

            //新需求 带薪病假 超过3天 要上传图片
            if($need_days >= 3 && empty($imagePathArr)){
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_3_img'));
            }


        } else if ($leaveType == 4) {//产假 一次 且不能超过98天 2021-04-07 修改为 按创建时间算 每年一次 每次90天
            $check_4 = $leave_server->check_create_year($staffId,$leaveType);
            if(!$check_4)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));


        } else if ($leaveType == 5) {//陪产假 一次  7天

        } else if ($leaveType == 6) {//国家军训假 一次 60天

        } else if ($leaveType == 7) {//家人去世假 5天
            //去世假 开始时间+7天 必须在当前时间的之前
            if (empty($paramIn['is_bi']) && $leaveStartTime < date('Y-m-d', strtotime('-7 day'))) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_7'));
            }
            //去世假 图片必填
            if (empty($imagePathArr)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_7_img'));
            }

        } else if ($leaveType == 8) {//绝育手术假 一次 不限制天数 也不限制 次数了 产品说的
        } else if ($leaveType == 9) {//个人培训假 不超过 30 天，或一年不超过3 次
            if ($count_days[$leaveType] > 2)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
        } else if ($leaveType == 10) {//婚假 一次 不超过3天
            //新增需求 婚嫁 入职需大雨6个月才能申请 改成1年了
            $hire_1_year = strtotime("{$staff_info['hire_date']} +1 year");
            if (time() < $hire_1_year) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_11_notice'));
            }

        } else if ($leaveType == 11) {//出家假 一次 7天
            //新需求 出家假 入职大于1年才能申请
            $hire_1_year = strtotime("{$staff_info['hire_date']} +1 year");
            if (time() < $hire_1_year) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_11_notice'));
            }

        } //不带薪事假 无限制
        else if ($leaveType == 12) {

        }
        else if ($leaveType == 16) { //公司培训假 只能选择今天、明天和后天，或者过去8天的日期 且不能超过8天 改了
            $trainingServer             = new TrainingServer($this->lang, $this->timezone);
            $trainingServer->staffInfo  = $staff_info;
            $trainingServer->paramModel = $leave_info;
            if ($trainingServer->checkTrainingLeave()) {
                return $this->checkReturn(-3, $this->getTranslation()->_('training_leave_notice'));
            }
        } else if ($leaveType == 17) { //产检在本公司只有8天  不超过一次
//            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
//                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

        } else if ($leaveType == 18) {//新增类型 不带新病假 最多申请天数：小于等于60天
            $date_7 = date('Y-m-d', strtotime("-7 day"));//7天前 和当天 区间
            if (empty($paramIn['is_bi'])) {
                if ($leaveStartTime < $date_7)
                    return $this->checkReturn(-3, $this->getTranslation()->_('leave_18_notice'));
            }
            //新需求 一次申请不能超过90天 加一起不能超过 2倍
            if($need_days > $limit_days[$leaveType] )
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            if (($need_days + $sum_days[$leaveType]) > ($limit_days[$leaveType] * 2))
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));


//            //辉哥说 去掉这个限制
//            //新增逻辑  在申请“不带薪病假”的时候校验“带薪病假”余额是否为0。如果不为0，提示：“请先使用完带薪病假后再申请不带薪病假”，不允许申请成功。 https://l8bx01gcjr.feishu.cn/docs/doccn7vmWUYEs8kE9QaGsW6Zohg
//            $limit_3 = $limit_days[enums::LEAVE_TYPE_3];
//            //带薪病假 已经用的额度（包括新冠相关假期的占用） https://l8bx01gcjr.feishu.cn/docs/doccn7vmWUYEs8kE9QaGsW6Zohg
//            $applied_days = $audit_model->get_used_leave_days($staffId, $year, "3,26");
//            $all_applied = array_sum(array_column($applied_days,'num'));
//            if($limit_3 > $all_applied)
//                return $this->checkReturn(-3, str_replace('{X}', $limit_3 - $all_applied, $this->getTranslation()->_('leave_use_3_first')));


        } else if ($leaveType == 15) {//休息日 可以申请前天、昨天、今天、明天、后天。

            if (empty($paramIn['is_bi'])) {
                $beforeTime = strtotime(date("Y-m-d", strtotime("-2 day")));
                $behindTime = strtotime(date("Y-m-d", strtotime("+2 day")));
                if (strtotime($leaveStartTime) < $beforeTime || strtotime($leaveStartTime) > $behindTime) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('overtime_five_days'));
                }
                if (strtotime($leaveEndTime) < $beforeTime || strtotime($leaveEndTime) > $behindTime) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('overtime_five_days'));
                }
            }

            //如果是网点员工 或者总部 客服 和ka  一个月不能超过5次  每次请假 不能超过一天 4 和 25 不知道 谁是单休 不能加到 这里
            //新需求 按工作天数 区分 6天为单休
            if ($staff_info['week_working_day'] != 6) {
                return $this->checkReturn(-3, $this->getTranslation()->_('jobtransfer_0004'));
            }


            $check_rest = $leave_server->get_rest_times($staff_info, $leave_info);
            if ($check_rest['code'] != 1) {
                return $this->checkReturn(-3, $check_rest['msg']);
            }

            $times = $check_rest['data'];

            if ($times >= 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_for_five'));
            }

            //新需求 休息日 只能是1天 大于小于都不行 https://shimo.im/docs/kv6rgP3GXDT69wdc/read
            if ($need_days < 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_for_half'));
            }

            if ($need_days > 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }


        } else if ($leaveType == 20) {//单亲育儿假 7天 可以申请3天之内和未来的日期 一次  入职时间大于1年的员工有资格享受7天带薪育儿假
            if (date('Y-m-d') < date('Y-m-d',strtotime("{$staff_info['hire_date']} +1 year") ))
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_20_notice'));

            $date_3 = date('Y-m-d', strtotime("-3 day"));//可以申请3天之内和未来的日期 包含当天
            if (empty($paramIn['is_bi'])) {
                if ($leaveStartTime <= $date_3)
                    return $this->checkReturn(-3, $this->getTranslation()->_('leave_3_notice'));
            }

//            if ($count_days[$leaveType] > 0)
//                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
//
//            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
//                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

        } else if ($leaveType == 21) {//女性特殊假 适用所有因妇科疾病而接受手术的人 入职时间开始+6个月的员工有资格申请此类假期 无限制次数，请完为止
            if (date('Y-m-d') < date('Y-m-d',strtotime("{$staff_info['hire_date']} + 6 month")))
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_21_notice'));

            $date_3 = date('Y-m-d', strtotime("-3 day"));//可以申请3天之内和未来的日期
            if (empty($paramIn['is_bi'])) {
                if ($leaveStartTime <= $date_3)
                    return $this->checkReturn(-3, $this->getTranslation()->_('leave_3_notice'));
            }

            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

        } else if ($leaveType == 22) {//家庭暴力假 无限制
            $date_3 = date('Y-m-d', strtotime("-3 day"));//可以申请3天之内和未来的日期
            if (empty($paramIn['is_bi'])) {
                if ($leaveStartTime <= $date_3)
                    return $this->checkReturn(-3, $this->getTranslation()->_('leave_3_notice'));
            }

            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

        }  else if ($leaveType == 23) {//紧急假 无限制

            if ($need_days > 5) { //佳雪需求 菲律宾 紧急假 不限制次数，一次最多申请5天
                $message = str_replace('limit_days', 5, $this->getTranslation()->_('leave_limit_x_days'));
                return $this->checkReturn(-3, $message);
            }
        } else if($leaveType == 24){//老挝新增 无薪休假


        }  else if ($leaveType == 25) {//隔离假
            $beforeTime = strtotime(date("Y-m-d", strtotime("-6 day"))); //7天前
            if (strtotime($leaveStartTime) < $beforeTime && empty($paramIn['is_bi']))
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit_start'));


            if(empty($imagePathArr) || count($imagePathArr) > 5)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_img_most'));


        }  else if ($leaveType == 26) {//新冠治疗
            $beforeTime = strtotime(date("Y-m-d", strtotime("-6 day"))); //7天前
            if (strtotime($leaveStartTime) < $beforeTime && empty($paramIn['is_bi']))
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit_start'));


            if(empty($imagePathArr) || count($imagePathArr) > 5)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_img_most'));


        } else if ($leaveType == 40) {//新增 类型 个人代理
            if($staff_info['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID){
                return $this->checkReturn(-3, $this->getTranslation()->_('jobtransfer_0004'));
            }
            //只能申请 3天及以后日期 >= +3day
            $limitDate = date('Y-m-d', strtotime('+3 day'));
            if($leaveStartTime < $limitDate && empty($paramIn['is_bi']) && empty($leave_info['unpaid_confirm'])){
                throw new ValidationException($this->getTranslation()->_('unpaid_confirm_notice'),10010);
            }

        } else {//非法请求
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }


        return true;
    }


    //验证是否存在 请假
    public function checkExistLeave($param)
    {
        $auditRe   = new AuditRepository($this->lang);
        $leaveData = $auditRe->getLeaveData($param);

        if (empty($leaveData)) {
            return false;
        }

        $flag = false;
        foreach ($leaveData as $da) {
            //有记录 并且是 一整天
            if ($da['sum_type'] == 0 || $da['sum_type'] == 3) {
                $flag = true;
                break;
            }

            //开始时间 相同 验证 上下午 有没有重叠 sum_type 1 上午 2 下午 3和0 是全天
            if ($da['date_at'] == $param['leave_start_time'] && $param['leave_start_type'] == $da['sum_type']) {
                $flag = true;
                break;
            }

            //结束时间 相同 验证 上下午 有没有重叠
            if ($da['date_at'] == $param['leave_end_time'] && $param['leave_end_type'] == $da['sum_type']) {
                $flag = true;
                break;
            }
        }
        return $flag;
    }


    /**
     * 时间拼装
     * @Access  public
     * @Param   $date 时间|$dateType 1 开始时间 2 结束时间 | $dateUType 1 AM 2 PM
     * @Return  array
     */
    public function assemblyData($date, $dateType = 1, $dateUType = 1)
    {
        $resourceStartData = [
            1 => '09:00:00',
            2 => '13:00:00',
        ];
        $resourceEndData   = [
            1 => '12:00:00',
            2 => '18:00:00',
        ];
        if ($dateType == 1) {
            $dateTime = $date . ' ' . $resourceStartData[$dateUType];
        } else {
            $dateTime = $date . ' ' . $resourceEndData[$dateUType];
        }
        return $dateTime;
    }

    /**
     * 修改审核状态
     * @Access  public
     * @Param  $paramIn
     * @Param request
     * @Return  array
     */
    public function auditEditStatus($paramIn = [])
    {
        self::$paramIn = $paramIn;
        $database = $this->getDI()->get('db');
        try{
            $audit_id = intval($paramIn['audit_id']);
            $info = StaffAuditModel::findFirst($audit_id);
            if (empty($info)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
            }

            $auditType = intval($info->audit_type);//1
            //泰国个人代理 补卡
            if (isCountry('TH') && $info->audit_type == StaffAuditModel::AUDIT_TYPE_REISSUE && $info->source_type == StaffAuditModel::SOURCE_TYPE_UNPAID) {
                $auditType = enums::$audit_type['ICAT'];//74
            }

            $approval_id = $paramIn['staff_id'];//操作人工号
            $status = intval($paramIn['status']);

            $reason = '';
            if ($status != enums::$audit_status['approved']) {
                $reason = empty(self::$paramIn['reject_reason']) ? ($paramIn['reject_reason'] ?? '') : self::$paramIn['reject_reason'];
            }

            $app_server = new ApprovalServer($this->lang,$this->timezone);
            //工具或者任务撤销的情况
            if((!empty($paramIn['is_bi']) || !empty($paramIn['is_task']))){
                $paramExt['super'] = 1;
                //工具过来的要加操作人记录
                if (!empty($paramIn['is_bi'])) {
                    $paramExt['operate_id'] = $paramIn['operate_id'] ?? 0;
                }
                $request = $app_server->getApply($audit_id, $auditType);
                if (!$request){
                    $db = BackyardBaseModel::beginTransaction($this);
                    try {
                        //如果没有审批流
                        $this->setProperty($audit_id, $status, $paramExt);
                        $db->commit();
                    } catch (Exception $e) {
                        $db->rollback();
                        throw $e;
                    }
                }else{
                    //如果有审批流
                    $app_server->cancel($audit_id, $auditType, 'system tool cancel',$approval_id,$paramExt);
                }
                return $this->checkReturn(['data' => ['audit_id' => $info->audit_id]]);
            }

            if ($status == enums::$audit_status['approved']) {//审核通过
                //马来特殊逻辑 国民假 第一个审批节点 审批通过 要弹窗
                $militaryFlag = isCountry('MY') && $auditType == StaffAuditModel::AUDIT_TYPE_LEAVE && $info->leave_type == enums::LEAVE_TYPE_42 && $info->sub_status == StaffAuditModel::MILITARY_SUB_STATUS_HAS_NOT;
                if ($militaryFlag === true) {
                    $m_server = new MilitaryServer($this->lang, $this->timezone);
                    $m_server = Tools::reBuildCountryInstance($m_server, [$this->lang, $this->timezone]);
                    $m_server->checkFirstNode($info->toArray(), $paramIn);
                }
                $res = $app_server->approval($audit_id, $auditType, $approval_id);
            } elseif ($status == enums::$audit_status['dismissed']) {//驳回
                $res = $app_server->reject($audit_id, $auditType, $reason, $approval_id);
            } elseif ($status == enums::$audit_status['revoked']) {//撤销
                $type = $this->determineCancelType($audit_id,$auditType);
                if ($type == 1){
                    //审批中撤销
                    if (!($this->getOptionsRule($audit_id)->getIsApplierAllowedCancelInApprovalProcess() ||$this->getOptionsRule($audit_id)->getIsApplierAllowedCancelInRevokeProcess())){
                        return $this->checkReturn(-3, $this->getTranslation()->_('action_not_allowed'));
                    }
                    $res = $app_server->cancel($audit_id, $auditType, $reason, $approval_id);
                }elseif ($type == 2){
                    //审批通过后撤销
                    if (!($this->getOptionsRule($audit_id)->getIsApplierAllowedCancelAfterApprovalComplete() ||$this->getOptionsRule($audit_id)->getIsApplierAllowedCancelAfterRevokeComplete())){
                        return $this->checkReturn(-3, $this->getTranslation()->_('action_not_allowed'));
                    }
                    $database->begin();
                    //撤销审批流 超时重新计算 只有请假
                    $timeOut = date('Y-m-d 00:00:00',strtotime('+3 day'));
                    //更改业务表状态
                    $info->created_at = DateHelper::localToUtc();
                    $info->status = 1;
                    $info->time_out = $timeOut;
                    $info->update();
                    //子记录处理
                    $children = StaffAuditModel::find([
                        'conditions' => "parent_id = :audit_id:",
                        'bind'       => ['audit_id' => $audit_id],
                    ]);
                    if ($children){
                        $children->update(['status'=> 1,'created_at'=> DateHelper::localToUtc(), 'time_out' => $timeOut]);
                    }

                    //创建撤销审批流
                    $staff_model = new StaffRepository();
                    $staffData   = $staff_model->getStaffPosition($info->staff_info_id);
                    $extend      = $this->getWorkflowExtend($info->toArray(), $staffData);
                    $workflowId  = $this->getWorkflowId($staffData);
                    $this->logger->write_log("撤销发起审批参数：".json_encode(['extend'=>$extend,'workflow_id'=>$workflowId]),'info');
                    $res = $app_server->cancel_create($audit_id, $auditType, $reason, $approval_id, '', $extend,
                        $workflowId);
                    $database->commit();
                }
            } else {
                return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
            }
            if (empty($res)) {
                $this->logger->write_log('audit_update_status error ' . json_encode($paramIn),'info');
                return $this->checkReturn(-3, 'server error');
            }
            //马来特殊逻辑 国民假 第一个审批节点 审批通过 要弹窗
            if (!empty($militaryFlag)) {
                $info->sub_status            = StaffAuditModel::MILITARY_SUB_STATUS_APPROVED;
                $commentJson['agree_reason'] = $paramIn['agree_reason'] ?? '';
                $commentJson['agree_url']    = $paramIn['agree_url'] ?? '';
                $info->template_comment      = json_encode($commentJson);
                $info->update();
            }
            return $this->checkReturn(['data' => ['audit_id' => $info->audit_id]]);
        } catch (ValidationException $e) {
            if ($database->isUnderTransaction()) {
                $database->rollback();
            }
            return $this->checkReturn(-3, $e->getMessage());
        } catch (BusinessException $be) {
            if ($database->isUnderTransaction()) {
                $database->rollback();
            }
            return $this->checkReturn(-3, $be->getMessage());
        } catch (\Exception $e) {
            if ($database->isUnderTransaction()) {
                $database->rollback();
            }
            $this->logger->write_log("audit_update_status error " . json_encode($paramIn) . $e->getMessage());
            return $this->checkReturn(-3, 'server error');
        }
    }

    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $auditRe = new AuditRepository($this->lang);
        $auditDetail = $auditRe->auditDetail(['audit_id' => $auditId]);
        if (empty($auditDetail)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        //补卡和泰国代理补卡
        if (in_array($auditDetail['audit_type'], [enums::$audit_type['AT'], enums::$audit_type['ICAT']])) {
            return $this->getATDetail($auditId, $user, $comeFrom);
        } elseif ($auditDetail['audit_type'] == enums::$audit_type['LE']) {
            return $this->getLEDetail($auditId,$user,$comeFrom);
        }
    }

    /**
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     */
    protected function getATDetail(int $auditId, $user, $comeFrom)
    {
        $auditDetail = $this->re['audit']->auditDetail(['audit_id' => $auditId]);
        if (empty($auditDetail)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        $staff_info = (new StaffServer())->get_staff($auditDetail['staff_info_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }
        $img = StaffAuditImageModel::Find("audit_id = {$auditId}")->toArray();
        $photo = [];
        if (!empty($img)) {
            foreach ($img as $item) {
                $photo[] = $item['image_path'];
            }
        }
        //个人代理类型的详情 要改翻译 先只改 马来
        if(isCountry('MY') && in_array($staff_info['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
            return $this->getUnpaidAtDetail($auditDetail, $staff_info, $photo);
        }

        //个人代理类型的详情 泰国
        if(isCountry('TH') && in_array($staff_info['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
            return $this->getTHUnpaidAtDetail($auditDetail, $staff_info, $photo);
        }

        //菲律宾 个人代理
        if(isCountry('PH') && $auditDetail['source_type'] == StaffAuditModel::SOURCE_TYPE_UNPAID){
            return $this->getPHUnpaidAtDetail($auditDetail, $staff_info, $photo);
        }

        $returnData['data']['head'] = [
            'title'       => (new AuditlistRepository($this->lang, $this->timezone))->getAudityType(enums::$audit_type['AT']),
            'id'          => $auditDetail['audit_id'],
            'staff_id'    => $auditDetail['staff_info_id'],
            'type'        => enums::$audit_type['AT'],
            'created_at'  => $auditDetail['created_at'],
            'updated_at'  => $auditDetail['updated_at'],
            'status'      => $auditDetail['status'],
            'status_text' => (new AuditlistRepository($this->lang, $this->timezone))->getAuditStatus('10' . $auditDetail['status']),
            'serial_no'   => $auditDetail['serial_no'] ?? '',
        ];

        $returnData['data']['detail'] = [
            ['key' => $this->getTranslation()->_('apply_parson'), 'value' => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? '')],
            ['key' => $this->getTranslation()->_('apply_department'), 'value' => sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? '')],
            ['key' => $this->getTranslation()->_('attendance_date'), 'value' => $auditDetail['attendance_date']],
            ['key' => $this->getTranslation()->_('reissue_card_time'), 'value' => $auditDetail['reissue_card_date']],
            ['key' => $this->getTranslation()->_('attendance_type'), 'value' => $this->re['audit']->typeName($auditDetail['audit_type'])[$auditDetail['attendance_type']]],
            ['key' => $this->getTranslation()->_('audit_reason'), 'value' => $auditDetail['audit_reason']],
            ['key' => $this->getTranslation()->_('photo'), 'value' => $photo],
        ];

        return $returnData;
    }

    /**
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     * @throws \ReflectionException
     */
    protected function getLEDetail(int $auditId, $user, $comeFrom)
    {
        $auditRe = new AuditRepository($this->lang);
        $result = $auditRe->auditDetail(['audit_id' => $auditId], 1);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        $staff_info = (new StaffServer())->get_staff($result['staff_info_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }
        //个人代理类型的详情 要改翻译
        if(in_array($staff_info['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
            return $this->getUnpaidDetail($result, $staff_info);
        }
        $leave_server = Tools::reBuildCountryInstance(new LeaveServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);

        //针对 请假类型 需要额外增加 对应的请假说明
        $returnData['data']['leave_type']   =  $result['leave_type'];
        $photo = $leave_server->formatLeaveImg($result['img_path'], $result['leave_type']);
        $audit_model = new AuditRepository($this->lang);
        $leave_book  = $audit_model->getTypeBook_all();
        $is_or       = array_column($leave_book, 'type', 'code');
        $describe    = '';
        if (isset($is_or[$result['leave_type']]) && $is_or[$result['leave_type']] == 1) {
            $describe = '(' . $this->getTranslation()->_('paid') . ')';
        }
        if (isset($is_or[$result['leave_type']]) && $is_or[$result['leave_type']] == 2) {
            $describe = '(' .$this->getTranslation()->_('un_paid') . ')';
        }

        $typeName = $this->re['audit']->typeName($result['audit_type'])[$result['leave_type']] ?? $result['leave_type'];
        if($result['leave_type'] == enums::LEAVE_TYPE_3 && strtolower(env('country_code', 'Th')) == 'th'){
            $typeName = $this->re['audit']->typeName($result['audit_type'])[enums::LEAVE_TYPE_38] ?? $result['leave_type'];
        }

        //如果是病假 需要根据当前登陆人展示额外字段
        if($result['leave_type'] == enums::LEAVE_TYPE_38){
            $sickData = $leave_server->formatSickDetail($result, $user);
        }
        if($result['leave_type'] == enums::LEAVE_TYPE_42){
            $militaryData = $leave_server->formatMilitaryDetail($result);
        }

        [$leave_start_time,$leave_end_time] = $leave_server->formatLeaveTime($result);

        $returnData['data']['head'] = [
            'title'       => (new AuditlistRepository($this->lang, $this->timezone))->getAudityType(enums::$audit_type['LE']),
            'id'          => $result['audit_id'],
            'staff_id'    => $result['staff_info_id'],
            'type'        => enums::$audit_type['LE'],
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['status'],
            'status_text' => (new AuditlistRepository($this->lang, $this->timezone))->getAuditStatus('10' . $result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
            'hire_type'   => $staff_info['hire_type'] ?? '',
        ];
        //马来国民假新增 字段 判断是否审批弹窗
        if(isCountry('MY') && $result['leave_type'] == enums::LEAVE_TYPE_42){
            $returnData['data']['head']['alert_42'] = $result['sub_status'];//对应 枚举值 1 审批过 不弹窗 2 没审批 弹窗
        }

        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('apply_parson'), 'value' => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? '')];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('apply_department'),'value' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? '')];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('cs_department'),'value' => $result['cs_department'] ?? '--'];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('leave_type'), 'value' => $typeName . $describe];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('start_time'), 'value' => $leave_start_time];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('end_time'), 'value' => $leave_end_time];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('days'), 'value' => $result['leave_day']];
        //针对不同请假类型 显示 额外的模板字段
        $template_comment = $leave_server->format_detail_body($result['leave_type'],$result['template_comment']);
        $returnData['data']['detail'] = array_merge($returnData['data']['detail'],$template_comment);

        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('audit_leave_reason'), 'value' => $result['audit_reason']];
        //病假额外字段
        if(!empty($sickData)){
            $returnData['data']['detail'] = array_merge($returnData['data']['detail'], $sickData);
        }
        //病假图片结构不一样
        //如果是病假 需要根据当前登陆人展示额外字段
        if($result['leave_type'] == enums::LEAVE_TYPE_38){
            foreach ($photo as $category => $path){
                if($category == 'other'){
                    $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_($category), 'value' => $path, 'type' => enums::APPROVAL_DETAIL_TYPE_OTHER_CONTENT,'other_content' => $result['other_content']];
                }else{
                    $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_($category), 'value' => $path];
                }
            }
        }else{
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('photo'), 'value' => $photo];
        }
        //马来 国民假 额外字段
        if(!empty($militaryData)){
            $returnData['data']['detail'] = array_merge($returnData['data']['detail'], $militaryData);
        }

        if (!empty($result['paid_leave_reason'])) {
            $paidLeaveReason = $leave_server->getPaidLeaveReason($result['paid_leave_reason']);
            $returnData['data']['detail'] = array_merge($returnData['data']['detail'], [
                ['key' => $this->getTranslation()->_('paid_leave_reason'), 'value' => $paidLeaveReason],
            ]);
        }

        return $returnData;
    }

    //个人代理请假详情
    public function getUnpaidDetail($result, $staffInfo){

        //针对 请假类型 需要额外增加 对应的请假说明
        $returnData['data']['leave_type']   =  $result['leave_type'];
        $photo = [];
        if (!empty($result['img_path'])) {
            foreach ($result['img_path'] as $item) {
                $photo[] = $item['image_path'];
            }
        }
        $auditRe = new AuditRepository($this->lang);
        $typeName = $auditRe->typeName($result['audit_type'])[$result['leave_type']] ?? $result['leave_type'];
        $leave_server = Tools::reBuildCountryInstance(new LeaveServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
        [$leave_start_time,$leave_end_time] = $leave_server->formatLeaveTime($result);
        $titleKey = '';
        if(isCountry('My')){
            $titleKey = 'leave_detail_title_my';
        }
        if(isCountry('TH')){
            $titleKey = 'leave_40';
        }
        if(isCountry('PH')){
            $titleKey = 'leave_41';
        }
        if($result['leave_type'] == enums::LEAVE_TYPE_42){
            $typeName = $this->getTranslation()->_('leave_42_unpaid');
        }

        $returnData['data']['head'] = [
            'title'       => $this->getTranslation()->_($titleKey),
            'id'          => $result['audit_id'],
            'staff_id'    => $result['staff_info_id'],
            'type'        => enums::$audit_type['LE'],
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['status'],
            'status_text' => (new AuditlistRepository($this->lang, $this->timezone))->getAuditStatus('10' . $result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
            'hire_type'   => $staffInfo['hire_type'],
        ];

        //马来国民假新增 字段 判断是否审批弹窗
        if(isCountry('MY') && $result['leave_type'] == enums::LEAVE_TYPE_42){
            $returnData['data']['head']['alert_42'] = $result['sub_status'];
            $militaryData = $leave_server->formatMilitaryDetail($result);
        }

        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('agent_name'), 'value' => sprintf('%s ( %s )',$staffInfo['name'] ?? '' , $staffInfo['id'] ?? '')];
        if(!isCountry('My')){//马来个人代理不显示部门
            $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('agent_department'),'value' => sprintf('%s - %s',$staffInfo['depart_name'] ?? '' , $staffInfo['job_name'] ?? '')];
        }
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('agent_store'),'value' => $result['cs_department'] ?? '--'];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('agent_type'), 'value' => $typeName ];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('start_time'), 'value' => $leave_start_time];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('end_time'), 'value' => $leave_end_time];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('days'), 'value' => $result['leave_day']];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('audit_leave_reason'), 'value' => $result['audit_reason']];
        $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('photo'), 'value' => $photo];
        //马来 国民假 额外字段
        if(!empty($militaryData)){
            $returnData['data']['detail'] = array_merge($returnData['data']['detail'], $militaryData);
        }

        return $returnData;
    }

    //个人代理 补卡详情翻译
    public function getUnpaidAtDetail($auditDetail, $staff_info, $photo){
        $returnData['data']['head'] = [
            'title'       => $this->getTranslation()->_('home_make_up_unpaid'),
            'id'          => $auditDetail['audit_id'],
            'staff_id'    => $auditDetail['staff_info_id'],
            'type'        => enums::$audit_type['AT'],
            'created_at'  => $auditDetail['created_at'],
            'updated_at'  => $auditDetail['updated_at'],
            'status'      => $auditDetail['status'],
            'status_text' => (new AuditlistRepository($this->lang, $this->timezone))->getAuditStatus('10' . $auditDetail['status']),
            'serial_no'   => $auditDetail['serial_no'] ?? '',
        ];
        $returnData['data']['detail'] = [
            ['key' => $this->getTranslation()->_('agent_name'), 'value' => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? '')],
            ['key' => $this->getTranslation()->_('service_date'), 'value' => $auditDetail['attendance_date']],
            ['key' => $this->getTranslation()->_('rectify_time'), 'value' => $auditDetail['reissue_card_date']],
            ['key' => $this->getTranslation()->_('rectify_type'), 'value' => $this->re['audit']->reissueTypeUnpaid()[$auditDetail['attendance_type']]],
            ['key' => $this->getTranslation()->_('reason'), 'value' => $auditDetail['audit_reason']],
            ['key' => $this->getTranslation()->_('photo'), 'value' => $photo],
        ];

        return $returnData;
    }

    //个人代理 补卡详情翻译 人称 补结算服务费申请
    public function getTHUnpaidAtDetail($auditDetail, $staff_info, $photo)
    {
        $returnData['data']['head']   = [
            'title'       => $this->getTranslation()->_('ic_reissue'),
            'id'          => $auditDetail['audit_id'],
            'staff_id'    => $auditDetail['staff_info_id'],
            'type'        => enums::$audit_type['ICAT'],
            'created_at'  => $auditDetail['created_at'],
            'updated_at'  => $auditDetail['updated_at'],
            'status'      => $auditDetail['status'],
            'status_text' => (new AuditlistRepository($this->lang, $this->timezone))->getAuditStatus('10' . $auditDetail['status']),
            'serial_no'   => $auditDetail['serial_no'] ?? '',
        ];
        $returnData['data']['detail'] = [
            ['key'   => $this->getTranslation()->_('agent_name'),
             'value' => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? ''),
            ],
            ['key' => $this->getTranslation()->_('apply_department'), 'value' => sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? '')],
            ['key' => $this->getTranslation()->_('ic_service_date'), 'value' => $auditDetail['attendance_date']],
            ['key' => $this->getTranslation()->_('ic_rectify_time'), 'value' => $auditDetail['reissue_card_date']],
            ['key' => $this->getTranslation()->_('ic_reason'), 'value' => $auditDetail['audit_reason']],
            ['key' => $this->getTranslation()->_('photo'), 'value' => $photo],
        ];

        return $returnData;
    }
    public function getPhUnpaidAtDetail($auditDetail, $staff_info, $photo){
        $returnData['data']['head'] = [
            'title'       => $this->getTranslation()->_('home_make_up'),//make up
            'id'          => $auditDetail['audit_id'],
            'staff_id'    => $auditDetail['staff_info_id'],
            'type'        => enums::$audit_type['AT'],
            'created_at'  => $auditDetail['created_at'],
            'updated_at'  => $auditDetail['updated_at'],
            'status'      => $auditDetail['status'],
            'status_text' => (new AuditlistRepository($this->lang, $this->timezone))->getAuditStatus('10' . $auditDetail['status']),
            'serial_no'   => $auditDetail['serial_no'] ?? '',
            'hire_type'   => HrStaffInfoModel::HIRE_TYPE_UN_PAID,
        ];
        $returnData['data']['detail'] = [
            ['key' => $this->getTranslation()->_('agent_name'), 'value' => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? '')],
            ['key' => $this->getTranslation()->_('agent_department'), 'value' => sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? '')],
            ['key' => $this->getTranslation()->_('service_date'), 'value' => $auditDetail['attendance_date']],
            ['key' => $this->getTranslation()->_('reissue_card_time'), 'value' => $auditDetail['reissue_card_date']],
            ['key' => $this->getTranslation()->_('22495_agent_reissue_type'), 'value' => $this->re['audit']->reissueTypeUnpaid()[$auditDetail['attendance_type']]],
            ['key' => $this->getTranslation()->_('22495_agent_reason'), 'value' => $auditDetail['audit_reason']],
            ['key' => $this->getTranslation()->_('photo'), 'value' => $photo],
        ];

        return $returnData;
    }


    /**
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        $auditDetail = $this->re['audit']->auditDetail(['audit_id' => $auditId]);
        if ($auditDetail['audit_type'] == enums::$audit_type['LE']){
            //获取请假拆分数据
            $splitData = $this->re['audit']->getLeaveSplitData($auditId);
            //个人代理请假 什么时候都能撤销
            if($auditDetail['leave_type'] == enums::LEAVE_TYPE_40){
                return new AuditOptionRule(true, true, false, false, false, false);
            }

            //当前薪资周期
            [$start,$end] = $this->getPayCycle();
            //请假开始时间早于当前薪资周期开始时间的，不可撤销
            if ($splitData[0]['date_at'] < $start){
                return new AuditOptionRule(true, false, false, false, false, false);
            }else{
                if (isCountry('TH')){
                    return new AuditOptionRule(true, true, false, false, false, false);
                }else{
                    return new AuditOptionRule(true, true, false, false, false, true);
                }
            }
        } else {
            return parent::getOptionsRule($auditId);
        }
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $auditDetail = StaffAuditModel::findFirst([
            'conditions' => ' audit_id = :audit_id: ',
            'bind' => ['audit_id' => $auditId],
        ]);
        if(empty($auditDetail)){
            return [];
        }
        $auditDetail = $auditDetail->toArray();
        $auditType = $auditDetail['audit_type'];
        //泰国 个人代理
        if(isCountry('TH') && $auditDetail['audit_type'] == StaffAuditModel::AUDIT_TYPE_REISSUE && $auditDetail['source_type'] == StaffAuditModel::SOURCE_TYPE_UNPAID){
            $auditType = enums::$audit_type['ICAT'];
        }

        //菲律宾个人代理
        if(isCountry('PH') && $auditDetail['audit_type'] == StaffAuditModel::AUDIT_TYPE_REISSUE && $auditDetail['source_type'] == StaffAuditModel::SOURCE_TYPE_UNPAID){
            $param = [
                [
                    'key'   => "service_date",
                    'value' => $auditDetail['attendance_date']
                ],
                [
                    'key'   => "22495_agent_reissue_type",
                    'value' => $auditDetail['attendance_type']
                ]
                ,[
                    'key'   => "created_at",
                    'value' => show_time_zone($auditDetail['created_at'])
                ]
            ];
            return $param;
        }

        return (new AuditListServer($this->lang,$this->timezone))->generateSummary($auditId, $auditType);
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     * @throws \ReflectionException
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $auditDetail = StaffAuditModel::findFirst([
            'conditions' => ' audit_id = :audit_id: ',
            'bind' => ['audit_id' => $auditId],
        ]);
        if ($auditDetail){
            $auditDetail = $auditDetail->toArray();
        }
        if ($auditDetail['audit_type'] == AuditListEnums::APPROVAL_TYPE_AT){
            return $this->setATProperty($auditId,$state,$extend,$isFinal);
        }elseif($auditDetail['audit_type'] == AuditListEnums::APPROVAL_TYPE_LE){
            return $this->setLEProperty($auditId,$state,$extend,$isFinal);
        }


        return true;
    }

    /**
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return array|true
     */
    protected function setATProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            // 是最终审批
            $auditDetail = StaffAuditModel::findFirst([
                'conditions' => ' audit_id = :audit_id: ',
                'bind'       => ['audit_id' => $auditId],
            ]);
            if(empty($auditDetail)){
                throw new \Exception($this->getTranslation()->_('1014'));
            }
            $auditDetail = $auditDetail->toArray();
            if (isset(self::$paramIn['staff_id'])) {
                $staff = HrStaffInfoModel::findFirst([
                    'conditions' => ' staff_info_id = :staff_id: ',
                    'bind'       => ['staff_id' => self::$paramIn['staff_id']],
                ]);
                if ($staff) {
                    $staff = $staff->toArray();
                }
            }
            $updateData = [
                'status'        => $state,
                'audit_id'      => $auditId,
                'reject_reason' => self::$paramIn['reject_reason'] ?? '',
                'approver_id'   => self::$paramIn['staff_id'] ?? 0,
                'approver_name' => isset($staff) && $staff ? $staff['name'] : '',
            ];
            $auditRe = new AuditRepository($this->lang);
            $auditEditFlag = $auditRe->auditEditStatus($updateData);
            if (!$auditEditFlag) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1014'));
            }
            if ($state == enums::$audit_status['approved']) {
                //回调修改打卡时间
                //新需求 回写 班次到打卡表 徐华伟
                $attServer   = new HrShiftServer();
                $shift_data  = $attServer->getShiftInfos($auditDetail['staff_info_id'], [$auditDetail['attendance_date']]);
                $shift_start = $shift_data[$auditDetail['attendance_date']]['start'] ?? '';
                $shift_end   = $shift_data[$auditDetail['attendance_date']]['end'] ?? '';
                $shift_id    = $shift_data[$auditDetail['attendance_date']]['shift_id'] ?? 0;

                $updateData = [
                    'attendance_type'   => $auditDetail['attendance_type'],
                    'attendance_date'   => $auditDetail['attendance_date'],
                    'reissue_card_date' => $auditDetail['reissue_card_date'],
                    'staff_info_id'     => $auditDetail['staff_info_id'],
                    'shift_start'       => $shift_start,
                    'shift_end'         => $shift_end,
                    'shift_id'          => $shift_id,
                ];
                $auditRe->editWorkAttendance($updateData);
            }
        }
        return true;
    }

    /**
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return mixed
     * @throws \ReflectionException
     */
    protected function setLEProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            // 是最终审批
            $auditDetail = StaffAuditModel::findFirst([
                'conditions' => ' audit_id = :audit_id: ',
                'bind'       => ['audit_id' => $auditId],
            ]);
            if(empty($auditDetail)){
                throw new \Exception($this->getTranslation()->_('1014'));
            }
            $auditDetail = $auditDetail->toArray();
            if (isset(self::$paramIn['staff_id'])) {
                $staff = HrStaffInfoModel::findFirst([
                    'conditions' => ' staff_info_id = :staff_id: ',
                    'bind'       => ['staff_id' => self::$paramIn['staff_id']],
                ]);
                if ($staff) {
                    $staff = $staff->toArray();
                }
            }
            if (!$extend['super']){
                $state = $this->transformState($state,$extend['is_cancel'] ?? 0);
            }
            $updateData = [
                'status'        => $state,
                'audit_id'      => $auditId,
                'reject_reason' => self::$paramIn['reject_reason'] ?? '',
                'approver_id'   => self::$paramIn['staff_id'] ?? 0,
                'approver_name' => isset($staff) && $staff ? $staff['name'] : '',
            ];
            $auditEditFlag = $this->re['audit']->auditEditStatus($updateData);
            if (!$auditEditFlag) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1014'));
            }
            //驳回请假 年假类型 需要判断 补回去年额度
            if ($state != enums::$audit_status['approved'] && in_array($auditDetail['leave_type'],[enums::LEAVE_TYPE_1,enums::LEAVE_TYPE_19,enums::LEAVE_TYPE_4])) {
                //新版 年假额度返还逻辑
                $staff_model = new StaffRepository($this->lang);
                $staff_info = $staff_model->getStaffPosition($auditDetail['staff_info_id']);
                $leave_server = new LeaveServer($this->lang,$this->timezone);
                $leave_server = Tools::reBuildCountryInstance($leave_server,[$this->lang,$this->timezone]);
                $leave_server->cancelVacation($auditDetail,$staff_info,$state);
            }
            //审核通过 休息日
            if($state == enums::$audit_status['approved'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_15){
                $check_param['staff_id'] = $auditDetail['staff_info_id'];
                $check_param['date'] = date('Y-m-d',strtotime($auditDetail['leave_start_time']));
                $this->leave_for_workday($check_param);
            }
            //撤销休息日
            if($state == enums::$audit_status['revoked'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_15){
                $ext_server = new AuditExtendServer($this->lang,$this->timezone);
                $del_param['staff_info_id'] = $auditDetail['staff_info_id'];
                $del_param['date_at'] = date('Y-m-d',strtotime($auditDetail['leave_start_time']));
                $del_param['operate_id'] = $extend['operate_id'] ?? 0;
                $ext_server->cancel_for_leave($del_param);
            }
            //马来 补休假 如果 非审核通过的最终审批 需返还对应额度  印尼 也启用 补休假
            if($state != enums::$audit_status['approved']  && $auditDetail['leave_type'] == enums::LEAVE_TYPE_13){
                $leave_server = new LeaveServer($this->lang,$this->timezone);
                $leave_server->re_back_leave_days($auditDetail);
            }
            //跨国探亲假 和其他假期 非审核通过 操作返还 目前其他类型 只有 带薪事假 陆续兼容
            if ($auditDetail['leave_type'] == enums::LEAVE_TYPE_2 && $state != enums::$audit_status['approved']) {
                $leave_server = new LeaveServer($this->lang, $this->timezone);
                $p['leave_type'] = $auditDetail['leave_type'];
                $p['audit_id'] = $auditId;
                $leave_server->update_leave_days($auditDetail['staff_info_id'],enums::YEAR_RE_BACK,$p);
            }
            //一次性的假期 one time  one send 需要操作staff_leave_remaining_days 表
            $audit_server = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
            if($state != enums::$audit_status['approved'] && $auditDetail['audit_type'] == 2){
                if(in_array($auditDetail['leave_type'],$audit_server->one_time)){
                    //delete remain
                    StaffLeaveRemainDaysModel::find([
                        'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                        'bind' => ['staff_info_id' => $auditDetail['staff_info_id'], 'leave_type' => $auditDetail['leave_type']],
                    ])->delete();
                }
                if(in_array($auditDetail['leave_type'],$audit_server->one_send)){
                    //update remain
                    $remain_info = StaffLeaveRemainDaysModel::findFirst([
                        'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                        'bind' => ['staff_info_id' => $auditDetail['staff_info_id'], 'leave_type' => $auditDetail['leave_type']],
                    ]);
                    if(!empty($remain_info)){
                        $leave_days = $auditDetail['leave_day'];
                        $remain_info->days += $leave_days;
                        $remain_info->leave_days -= $leave_days;
                        $remain_info->updated_at = gmdate('Y-m-d');
                        $remain_info->update();
                    }
                }

                //驳回 和拒绝 都发消息 除了撤销
                $this->sendMessage($auditDetail, $state);

            }
        }
        return true;
    }
    /**
     * 样例
     * from_node_id | to_node_id | valuate_formula | valuate_code
     * -------------+------------+-----------------+-------------
     *      4       |     5      |    $p1 == 4     | getSubmitterDepartment
     *
     * 表示当提交人的部门为4时，审批节点4的下一个节点是5
     * 需要在 getWorkflowParams 中返回申请人所在的部门字段
     *
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $auditDetail = StaffAuditModel::findFirst([
            'conditions' => ' audit_id = :audit_id: ',
            'bind' => ['audit_id' => $auditId],
        ])->toArray();
        $staffId = $auditDetail['staff_info_id'];
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: ',
            'bind' => ['staff_id' => $staffId],
        ]);
        $staffInfo = $staffInfo ? $staffInfo->toArray() : [];

        $departmentId   = 0;
        $store_category = -1;
        $store_region   = null;
        if ($staffInfo) {
            if (isset($staffInfo['node_department_id']) && $staffInfo['node_department_id']) {
                $departmentId = $staffInfo['node_department_id'];
            } else {
                $departmentId = $staffInfo['sys_department_id'];
            }

            //查询 网点员工 所属网点的类型 补卡用 总部员工默认写 -1
            if ($staffInfo['sys_store_id'] != '-1') {
                $store_info = SysStoreModel::findFirst("id = '{$staffInfo['sys_store_id']}'");
                if (!empty($store_info)) {
                    $store_category = $store_info->category;
                    $store_region = $store_info->manage_region;
                }
            }
        }

        $is_end = 0;
        $is_past = 0;
        if ($auditDetail['audit_type'] == enums::$audit_type['LE']) {
            $request = AuditApplyModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type:",
                'bind' => [
                    'type'  => enums::$audit_type['LE'],
                    'value' => $auditId,
                ],
            ]);
            if ($request) {
                // 获得下个审批节点
                $workFlowNode = WorkflowNodeModel::findFirst([
                    'conditions' => ' id = :id: ',
                    'bind' => [
                        'id' => $request->getCurrentFlowNodeId(),
                    ],
                ]);
                if(!empty($workFlowNode)) {
                    $workFlowNode = $workFlowNode->toArray();
                }

                if ($workFlowNode && $workFlowNode['auditor_type'] == enums::WF_NODE_MANAGER) {
                    // 上级审批节点
                    if (
                        // 当申请人是一级部门负责人 head of organization 审批后停止
                        $this->isFirstDepartmentManager($auditDetail['staff_info_id'])
                        && $this->isHeadOfOrganization($workFlowNode['auditor_id'])
                        // 当申请人是head of bu  head of organization 审批后停止
                        ||
                        $this->isHeadOfBu($auditDetail['staff_info_id'])
                        && $this->isHeadOfOrganization($workFlowNode['auditor_id'])
                        // 当申请人是 group ceo的负责人或者 coo负责人
                        ||
                        $this->isHeadOfOrganization($auditDetail['staff_info_id'])
                        && $this->isCeo($workFlowNode['auditor_id'])
                        ||
                        // 其他人申请 遇到 head of bu 或者 head of organization
                        (
                            !(
                                $this->isFirstDepartmentManager($auditDetail['staff_info_id'])
                                ||
                                $this->isHeadOfBu($auditDetail['staff_info_id'])
                                ||
                                $this->isHeadOfOrganization($auditDetail['staff_info_id'])
                            )
                            &&
                            ($this->isHeadOfOrganization($workFlowNode['auditor_id']) || $this->isHeadOfBu($workFlowNode['auditor_id']))
                        )
                        ||
                        (
                            // 当申请人是Express Thai Product一级部门一下
                            // 审批人是 Express Thai Product一级部门负责人
                            $this->isExpressThaiProductDepartment($auditDetail['staff_info_id'])
                            &&
                            $this->isExpressThaiProductDepartmentManager($workFlowNode['auditor_id'])
                        )
                    ) {

                        $is_end = 1;
                    }
                } else if ($workFlowNode && $workFlowNode['type'] == 0) {
                    if ($this->isCeo($auditDetail['staff_info_id']) || $this->isCoo($auditDetail['staff_info_id'])) {
                        // 当申请人是head of organization  group ceo
                        $is_end = 1;
                    }
                }
            }

            //年假 并且 申请开始时间是 创建时间之前 走别的审批哦流
            $add_hour = $this->config->application->add_hour;
            $create_date = date('Y-m-d',strtotime($auditDetail['created_at']) + $add_hour * 3600);
            $apply_date = date('Y-m-d',strtotime($auditDetail['leave_start_time']));
            if($apply_date < $create_date)
                $is_past = 1;
        }

        //Fulfillment 或者Flash Money部门
        $isFlashMoney = $this->isFullDepartment($auditDetail['staff_info_id'], "999/222/30001");
        $submitterDept = $this->isFullDepartment($auditDetail['staff_info_id'], "999/222/20001") || $isFlashMoney
            ? 1: 0;

        //https://flashexpress.feishu.cn/docx/ArQadkQhgoThjhxZNKyco70Cn1c
        $is_special_region = $this->check_special_region($store_region);

        return [
            'category' => $store_category,
            'department_id' => $departmentId,
            'k' => $auditDetail['leave_type'],
            'k1' => $auditDetail['leave_day']
            ,'current_staff_id' => $user//当前登陆人
            ,'is_end' => $is_end // 是否应当是最终
            ,'is_network' => $this->isNetworkDepartment($auditDetail['staff_info_id'])
            ,'is_network_operation' => $this->isBelongByAncestry($auditDetail['staff_info_id'],enums::NETWORK_AREA)
            ,'money_flag' => $isFlashMoney
            ,'is_full' => $submitterDept
            ,'is_past' => $is_past,
            'job_title' => $staffInfo['job_title'],
            'is_special_region' => (int)$is_special_region,
        ];

    }

    //判断是否属于 指定大区 新增审批流判断 bool
    public function check_special_region($regionId){
        $setting_server = new SettingEnvServer();
        $limitAreaIds = $setting_server->getSetVal('bulky_ignore_region');
        if(empty($limitAreaIds) || empty($regionId)){
            return false;
        }
        $limitAreaIds = explode(',',$limitAreaIds);
        return in_array($regionId,$limitAreaIds);

    }

    /// 判断是否是ExpressThaiProduct部门负责人
    protected function isExpressThaiProductDepartmentManager($uid)
    {
        return SysDepartmentModel::findFirst([
            'conditions' => ' manager_id = :manager_id: and id = 20 ',
            'bind' => [
                'manager_id' => $uid,
            ],
        ]);
    }

    /// 判断是否是ExpressThaiProduct部门
    protected function isExpressThaiProductDepartment($uid)
    {
        $ids = SysDepartmentModel::find([
            'columns' => 'id',
            'conditions' => ' ancestry_v3 like :ancestry: or id = :id: ',
            'bind' => [
                'ancestry' => '999/222/1/20/%',
                'id' =>  20,
            ],
        ])->toArray();

        $ids = array_column($ids, 'id');
        if (empty($ids)) {
            return 0;
        }
        $staff = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: and ( sys_department_id in ({department_ids:array}) or node_department_id in ({department_ids:array}) )',
            'bind' => [
                'staff_id' => $uid,
                'department_ids' => $ids,
            ],
        ]);

        return $staff ?  1 : 0;
    }

    // 判断是否是network部门
    public function isNetworkDepartment($uid)
    {
        $ids = SysDepartmentModel::find([
            'columns' => 'id',
            'conditions' => ' ancestry_v3 like :ancestry: or id = :id: ',
            'bind' => [
                'ancestry' => '999/222/1/4/%',
                'id' => 4,
            ],
        ])->toArray();

        $ids = array_column($ids, 'id');
        if (empty($ids)) {
            return 0;
        }

        $staff = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: and ( sys_department_id in ({department_ids:array}) or node_department_id in ({department_ids:array}) )',
            'bind' => [
                'staff_id' => $uid,
                'department_ids' => $ids,
            ],
        ]);

        return $staff ?  1 : 0;
    }

    // 判断是否是某个子部门 或下级部门的 员工 https://flashexpress.feishu.cn/docx/Lx7pd6oO3o9TMix6WVLcRxt6nde
    public function isBelongByAncestry($uid,$ancestry)
    {
        if(empty($ancestry)){
            return 0;
        }

        $fleDepartmentModel = new FleSysDepartmentModel();
        $ids = $fleDepartmentModel->getSpecifiedDeptAndSubDept($ancestry);
        if(empty($ids)){
            return 0;
        }

        $staff = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: and ( sys_department_id in ({department_ids:array}) or node_department_id in ({department_ids:array}) )',
            'bind' => [
                'staff_id' => $uid,
                'department_ids' => $ids,
            ],
        ]);

        return $staff ?  1 : 0;
    }

    /**
     * 判断指定工号是否属于对应部门及其子部门
     * @param int $uid 员工工号
     * @param string $ancestry 部门链
     * @return int
     */
    public function isFullDepartment($uid, $ancestry = '')
    {
        //默认为 ffm
        if(empty($ancestry)){
            $ancestry = '999/222/20001';
        }
        $arr = explode('/',$ancestry);
        $last_id = end($arr);
        $ids = SysDepartmentModel::find([
            'columns' => 'id',
            'conditions' => ' ancestry_v3 like :ancestry: or id = :id: ',
            'bind' => [
                'ancestry' => $ancestry . '/%',
                'id' => $last_id,
            ],
        ])->toArray();

        if (empty($ids)) {
            return 0;
        }
        $ids = array_column($ids, 'id');

        $staff = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: and ( sys_department_id in ({department_ids:array}) or node_department_id in ({department_ids:array}) )',
            'bind' => [
                'staff_id' => $uid,
                'department_ids' => $ids,
            ],
        ]);

        return $staff ?  1 : 0;
    }

    // 判断是否group coo
    protected function isCoo($uid)
    {
        return SysDepartmentModel::findFirst([
            'conditions' => ' manager_id = :manager_id: and id = 222 ',
            'bind' => [
                'manager_id' => $uid,
            ],
        ]);
    }

    // 判断是group ceo
    protected function isCeo($uid)
    {
        return SysDepartmentModel::findFirst([
            'conditions' => ' manager_id = :manager_id: and type = 5  ',
            'bind' => [
                'manager_id' => $uid,
            ],
        ]);
    }

    // 判断是否是一级部门负责人
    protected function isFirstDepartmentManager($uid)
    {
        return SysDepartmentModel::findFirst([
            'conditions' => ' manager_id = :manager_id: and type in (2, 3) and level = 1',
            'bind' => [
                'manager_id' => $uid,
            ],
        ]);
    }

    // 判断是否是 head of origanizaiton
    protected function isHeadOfOrganization($uid)
    {
        return SysDepartmentModel::findFirst([
            'conditions' => ' type = 4 and manager_id = :manager_id: ',
            'bind' => [
                'manager_id' => $uid,
            ],
        ]);
    }


    // 判断是否是 head of  bu
    protected function isHeadOfBu($uid)
    {
        return SysDepartmentModel::findFirst([
            'conditions' => ' type = 1 and manager_id = :manager_id: ',
            'bind' => [
                'manager_id' => $uid,
            ],
        ]);
    }

    //如果 审批通过 休息日类型的假期 判断该员工 是 工作6天的 需要 覆盖该日期所在周的轮休记录 如果该申请 审核通过以后 被撤销 需还原 之前的轮休日期
    protected function leave_for_workday($param){
        try{
            //获取员工信息
            $re = new StaffRepository($this->lang);
            $staff_info = $re->getStaffPosition($param['staff_id']);
            if (empty($staff_info) || $staff_info['week_working_day'] == 5) {
                return;
            }
            $operateId = $param['operator'] ?? $param['staff_id'];

            //获取轮休信息 get_work_days_between
            $start = weekStart($param['date']);
            $end = weekEnd($param['date']);
            $work_days = $re->get_work_days_between($param['staff_id'],$start,$end);
            if(!empty($work_days)){
                //获取 ph
                $leave_server = Tools::reBuildCountryInstance(new LeaveServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
                $holiday = $leave_server->ph_days($staff_info);
                $holiday = empty($holiday) ? array() : array_column($holiday,'day');
                //只能存在一个轮休日 如果有多个 排除ph
                $row = array();
                foreach ($work_days as $k => $d){
                    if(in_array($d['date_at'], $holiday) || !empty($d['src_week'])){
                        unset($work_days[$k]);
                        continue;
                    }
                    $row = $d;
                    break;
                }
            }

            //如果 轮休 日期 有人后来配置了 就跳过
            if(!empty($row) ){
                if ($row['date_at'] == $param['date']) {
                    $this->getDI()->get('logger')->write_log('请休息日 是同一天'.json_encode($row), 'info');
                    return;
                }
                //保存 备份 轮休数据 并删除
                $re->del_workday($row['staff_info_id'], $row['date_at']);
                $this->getDI()->get('db')->insertAsDict('work_day_replace', $row);
                $this->getDI()->get('logger')->write_log('请休息日 备份轮休'.json_encode($row), 'info');
                //轮休日志操作记录表 删除
                $logServer          = new WorkdayServer($this->lang, $this->timezone);
                $row['operate_id'] = $operateId;
                $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_CANCEL_OFF, $row);
            }

            //操作覆盖
            $insert['staff_info_id'] = $param['staff_id'];
            $insert['month'] = date('Y-m',strtotime($param['date']));
            $insert['date_at'] = $param['date'];
            $insert['operator'] = $operateId;
            $insert['remark'] = 'leave replace';

            $model = new HrStaffWorkDayModel();
            $model->create($insert);
            //轮休日志操作记录表 来新增
            $logServer = new WorkdayServer($this->lang, $this->timezone);
            $insert['operate_id'] = $operateId;
            $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_ADD_OFF, $insert);
            //log
            $this->getDI()->get('logger')->write_log('请休息日 覆盖轮休'.json_encode($insert),'info');
            return ;
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('请休息日 覆盖轮休 异常' . $e->getMessage());
            return;
        }

    }

//    /**
//     * 获取 补卡/请假类型
//     * @Access  public
//     * @Param   request
//     * type 1 补卡 枚举
//     * 2 请假类型 枚举 (请假的客户端不调用 只有内部left holiday 用)
//     * @Return  array
//     *
//     * 1:年假 2:带薪事假 3:病假 4:产假 5:陪产假 6:国家军训假
//     * 7:家人去世假 8:Leave for sterilization 9:个人受训假 10:婚假
//     * 11:出家假 12:不带薪事假 13:调休 14:其他 15:休息日 16：公司培训假 17：陪产假
//     */
//    public function getTypeBook($paramIn = [])
//    {
//        $type         = $this->processingDefault($paramIn, 'type', 2, 1);
//        $audit_server = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
//        switch ($type) {
//            case 1;
//                $data     = [
//                    [
//                        'code' => '1',
//                        'msg'  => $this->getTranslation()->_('2001'),
//                    ],
//                    [
//                        'code' => '2',
//                        'msg'  => $this->getTranslation()->_('2002'),
//                    ],
//                ];
//                $show_img = $audit_server->type_book_show_msg($paramIn);
//
//                break;
//            case 2;//新增是否带薪属性  1-带薪 2-不带薪
//                $data = $audit_server->type_book();
//                break;
//            default:
//                $data = [
//
//                ];
//                break;
//        }
//
//        $returnData['data']['dataList'] = $data;
//        if ($type == 1) {
//            $returnData['data']['need_img'] = empty($show_img) ? false : $show_img;
//        }
//
//        //新增个人代理字段
//        $info = HrStaffInfoModel::findFirst("staff_info_id = {$paramIn['user_info']['id']}");
//        $returnData['data']['hire_type'] = empty($info) ? 0 :$info->hire_type;
//
//        return $this->checkReturn($returnData);
//    }

    //获取补卡类型 上班下班 马来重写
    public function getReissueType($param = [])
    {
//        $date     = $param['reissue_date'];
        $userInfo = $param['user_info'];
        $start_key = '2001';
        $end_key = '2002';
        if(!empty($userInfo) && in_array($userInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            $start_key = 'up_unpaid';
            $end_key = 'down_unpaid';
            if(isCountry('PH')){
                $start_key = 'up_unpaid_ph';
                $end_key = 'down_unpaid_ph';
            }
        }

        $data = [
            [
                'code' => '1',
                'msg'  => $this->getTranslation()->_($start_key),
            ],
            [
                'code' => '2',
                'msg'  => $this->getTranslation()->_($end_key),
            ],
        ];
        return $data;
    }


    //获取 上午下午 马来是 前半天 后半天
    public function startEndType($lang = ''){
        $data = [
            [
                'code' => (string)StaffAuditModel::LEAVE_AM,
                'msg'  => $this->getTranslation($lang)->_('half_am'),
            ],
            [
                'code' => (string)StaffAuditModel::LEAVE_PM,
                'msg'  => $this->getTranslation($lang)->_('half_pm'),
            ],
        ];
        return $data;
    }

    /**
     * 换算时间
     * 新需求 需根据员工属性 扣除休息日 和ph  6天-找轮休  5天 找周六日
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function conversionTime($paramIn = [])
    {
        //[1]参数定义
        //leave type 1 上午 2 下午
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $type           = $this->processingDefault($paramIn, 'type', 2, 1);//类型 1外部调用 2内部调用
        $staff_id       = $paramIn['staff_id'];


        //[2]换算时间
        if ($leaveEndTime < $leaveStartTime || ($leaveEndTime == $leaveStartTime) && $leaveEndType < $leaveStartType) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
        }
        $leave_day = 0;
        $key       = $start_date = date('Y-m-d', strtotime($leaveStartTime));
        $end_date  = date('Y-m-d', strtotime($leaveEndTime));
        //获取该员工 请假区间的 休息日 和 ph
        $leaveServer = new LeaveServer($this->lang, $this->timezone);
        $leaveServer = Tools::reBuildCountryInstance($leaveServer, [$this->lang, $this->timezone]);
        $holidays    = $leaveServer->staff_off_days($staff_id, $leaveStartTime, $leaveEndTime);
        while ($key <= $end_date) {
            if (in_array($key, $holidays) && in_array($leaveType, $this->sub_day)) {
                $key = date("Y-m-d", strtotime("+1 day", strtotime($key)));
                continue;
            }
            $add = 1;
            if ($key == $start_date && $leaveStartType != 1) {
                $add = 0.5;
            }
            if ($key == $end_date && $leaveEndType != 2) {
                $add = 0.5;
            }

            $leave_day += $add;
            $key       = date("Y-m-d", strtotime("+1 day", strtotime($key)));
        }

        if ($type == 1) {
            $returnData['data']['day'] = $leave_day;
            return $this->checkReturn($returnData);
        }
        return $leave_day;
    }

    /**
     * 获取待审核数量
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function waitAuditNum($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);

        //[2]用户校验
        $staffData = $this->re['staff']->checkoutStaff($staffId);
        if (empty($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
        }

        //[3]获取待审批的数量
        //$countData = $this->getWaitAuditNum($staffId);
        $countData = $this->getWaitAuditNumV2($staffId);

        //[4]获取离职资产待确认数
        $rs_count = $this->getNotResignAssetNum($staffId) ?? 0;

        //[5]cc数
        $cc_count = (new AuditListServer($this->lang, $this->timezone))->getCCCount($staffId);

        $returnData = ['data' => ['num' => $countData, 'rs_num' => $rs_count ?? 0, 'cc_num' => $cc_count ?? 0]];
        $this->getDI()->get('logger')->write_log('waitAuditNum ' .$staffId .' ' . json_encode($returnData), 'info');
        return $this->checkReturn($returnData);
    }

    /**
     * 获取待审批数
     * @param $staffId
     * @return int
     */
    public function getWaitAuditNum($staffId): int
    {
        //指定工号不存在，则返回0
        if (empty($staffId) || intval($staffId) == 0) {
            return 0;
        }

        //获取指定工号的待审批
        $auditTypeList = AuditListEnums::getAllAuditTypes();

        //获取全部的待审批数据
        $conditions = [
            'state' => [enums::APPROVAL_STATUS_PENDING],
            'audit_type' => $auditTypeList,
            'is_count' => true,
        ];
        $countData = (new AuditlistRepository($this->lang, $this->timezone))->getAuditApprovalList($staffId, $conditions, '');

        return $countData ?? 0;
    }

    /**
     * 获取待审批数
     * @param $staffId
     * @return int
     */
    public function getWaitAuditNumV2($staffId): int
    {
        //指定工号不存在，则返回0
        if (empty($staffId) || intval($staffId) == 0) {
            return 0;
        }
        $countData = (new AuditlistRepository($this->lang, $this->timezone))->getAuditApprovalPendingList($staffId);

        return $countData ?? 0;
    }

    /**
     *
     * 获取主管名下离职申请未审核资产数量
     *
     * @param $staffId
     */
    public function getNotApprovalNum($staffId)
    {
        $staffInfos = [];
        $staffInfo  = $this->DMOrAM($staffId);
        if ($staffInfo) {

            if ($staffInfo['job_title'] == enums::$job_title['district_manager']) {
                $sql = "
                --
                select
                    hsis.staff_info_id
                from
                    hr_staff_items hsis
                    left join hr_staff_info hsi on hsi.staff_info_id = hsis.staff_info_id
                    left join sys_store ss on hsi.sys_store_id = ss.id
                    left join hr_job_title hjt on hsi.job_title = hjt.id
                where 
                    hsis.item = 'MANGER' and hsis.value = '" . $staffId . "' and not (ss.category in (1,2,10) and hjt.id = " . enums::$job_title['branch_supervisor'] . ")
                ";
            } else if ($staffInfo['job_title'] == enums::$job_title['regional_manager']) {
                $sql = "
                --
                select
                    hsis.staff_info_id
                from
                    hr_staff_items hsis
                    left join hr_staff_info hsi on hsi.staff_info_id = hsis.staff_info_id
                    left join sys_store ss on hsi.sys_store_id = ss.id
                    left join hr_job_title hjt on hsi.job_title = hjt.id
                where 
                    hsis.item = 'MANGER' and hsis.value = '" . $staffId . "' and not (ss.category in (4,5,7,9) and hjt.id = " . enums::$job_title['shop_supervisor'] . ")
                ";
            } else {
                $sql = "
                --
                select
                    hsis.staff_info_id
                from
                    hr_staff_items hsis
                    left join hr_staff_info hsi on hsi.staff_info_id = hsis.staff_info_id
                    left join sys_store ss on hsi.sys_store_id = ss.id
                    left join hr_job_title hjt on hsi.job_title = hjt.id
                where 
                    hsis.item = 'MANGER' and hsis.value = '" . $staffId . "' and not (ss.category in (1,2,10) and hjt.id = " . enums::$job_title['branch_supervisor'] . " or ss.category in (4,5,7,9) and hjt.id = " . enums::$job_title['shop_supervisor'] . ")
                ";
            }
            $dmSubordinate = $this->getDI()->get("db_rby")->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
            if ($dmSubordinate) {

                if ($staffInfo['job_title'] == enums::$job_title['district_manager']) {

                    $sql = "
                       --
                       select
                            hsis.staff_info_id
                        from
                            hr_staff_info hsi 
                                left join hr_staff_items hsis on hsi.staff_info_id = hsis.staff_info_id
                        where 
                            hsis.item = 'MANGER' and hsis.value in ('" . implode("','", array_column($dmSubordinate, "staff_info_id")) . "') and hsi.job_title in (" . implode(", ", [
                            enums::$job_title['van_courier'],
                            enums::$job_title['bike_courier'],
                            enums::$job_title['dc_officer'],
                        ]) . ") ";
                } else if ($staffInfo['job_title'] == enums::$job_title['regional_manager']) {
                    $sql = "
                       --
                       select
                            hsis.staff_info_id
                        from
                            hr_staff_info hsi 
                                left join hr_staff_items hsis on hsi.staff_info_id = hsis.staff_info_id
                        where 
                            hsis.item = 'MANGER' and hsis.value in ('" . implode("','", array_column($dmSubordinate, "staff_info_id")) . "') and hsi.job_title in (" . implode(", ", [
                            enums::$job_title['shop_officer'],
                            enums::$job_title['dc_officer'],
                        ]) . ") ";

                } else {
                    $sql = "
                       --
                       select
                            hsis.staff_info_id
                        from
                            hr_staff_info hsi 
                                left join hr_staff_items hsis on hsi.staff_info_id = hsis.staff_info_id
                        where 
                            hsis.item = 'MANGER' and hsis.value in ('" . implode("','", array_column($dmSubordinate, "staff_info_id")) . "') and hsi.job_title in (" . implode(", ", [
                            enums::$job_title['van_courier'],
                            enums::$job_title['bike_courier'],
                            enums::$job_title['shop_officer'],
                            enums::$job_title['dc_officer'],
                        ]) . ") ";

                }
                $staffInfos = $this->getDI()->get("db_rby")->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
            }

            // 添加上主管用户ID
            $sql        = "
                --
                select
                    hsis.staff_info_id
                from
                    hr_staff_items hsis
                    left join hr_staff_info hsi on hsi.staff_info_id = hsis.staff_info_id
                    left join sys_store ss on hsi.sys_store_id = ss.id
                    left join hr_job_title hjt on hsi.job_title = hjt.id
                where 
                    hsis.item = 'MANGER' and hsis.value = :staff_id and (ss.category in (1,2,10) and hjt.id = :job_title1 or ss.category in (4,5,7,9) and hjt.id = :job_title2 )";
            $staffInfos = array_merge($staffInfos, $this->getDI()->get("db_rby")->fetchAll($sql, \PDO::FETCH_ASSOC, [
                "staff_id"   => $staffId,
                "job_title1" => enums::$job_title['branch_supervisor'],
                "job_title2" => enums::$job_title['shop_supervisor'],
            ], [
                "staff_id" => Column::BIND_PARAM_STR,
                "job_title1" => Column::BIND_PARAM_INT,
                "job_title2" => Column::BIND_PARAM_INT,
            ]));
        } else {
            $sql = "
                --
                select
                    hsis.staff_info_id
                from
                    hr_staff_info hsi 
                        left join hr_staff_items hsis on hsi.staff_info_id = hsis.staff_info_id
                where 
                    hsis.item = 'MANGER' and hsis.value = '" . $staffId . "' and hsi.job_title in (" . implode(", ", [
                    enums::$job_title['van_courier'],
                    enums::$job_title['bike_courier'],
                    enums::$job_title['shop_officer'],
                    enums::$job_title['dc_officer'],
                ]) . ") ";

            $staffInfos = $this->getDI()->get("db_rby")->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        }

        $staffIds = array_column($staffInfos, "staff_info_id");
        if ($staffIds) {
            $sql        = "
                    --
                    select 
                        submitter_id 
                    from 
                        staff_resign 
                    where 
                        resign_id in (
            
                                select 
                                        max(resign_id) as resign_id
                                from 
                                    staff_resign
                                where 
                                submitter_id in (" . implode(",", $staffIds) . ") group by submitter_id
                        ) and status not in (3, 4) and last_work_date <= '" . date("Y-m-d") . "'";
            $submitters = $this->getDI()->get("db")->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
            $submitters = array_column($submitters, "submitter_id");
            if ($submitters) {
                $sql   = "
                --
                select count(*) as count from leave_manager where staff_info_id in (" . implode(", ", $submitters) . ") and is_has_superior = 0 ";
                $count = $this->getDI()->get("db_rby")->query($sql)->fetch(\PDO::FETCH_ASSOC);
                return $count['count'];
            }
        }


        return 0;

    }


    public function getNotApprovalNumNew($staffId)
    {

       $bi_params = [
        'staffId'       => $staffId,
        'is_process'    => 0, //0-待处理 1-已处理
        'page'          => 1,
        'pagesize'      => 10,
    ];
	    try {
		    //优先缓存
		    $redis = $this->getDI()->get('redisLib');
		    //优先缓存
		    $key        = 'leave_staff_list_num_' . $staffId;
		    $cache_data = $redis->get($key);
		    if ($cache_data === false) {
			    $bi_rpc = (new ApiClient('bi_rpc', '', 'leaveStaffListNum', $this->lang));
			    $bi_rpc->setParams($bi_params);
			    $bi_return = $bi_rpc->execute();

			    if (isset($bi_return['result']['data']['data']['num']) && $bi_return['result']['data']['data']['num']) {
				    $cache_data = (int)$bi_return['result']['data']['data']['num'];
			    } else {
				    $cache_data = 0;
			    }
			    $redis->set($key, $cache_data, 30 * 60); //30 分钟缓存

		    }
	    } catch (\Exception $e) {
		    $this->getDI()->get("logger")->write_log('getNotApprovalNumNew 出现异常 ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString(), "error");
		    return 0;
	    }
	    return $cache_data;

    }

    /**
     *  获取离职未处理的资产的数量
     * @param $staffId
     * @return int
     */
    public function getNotResignAssetNum($staffId){
        $params = [
            'staffId'       => $staffId,
            'is_process'    => 0, //0-待处理 1-已处理
            'page'          => 1,
            'pagesize'      => 10,
        ];

        try {
            //优先缓存
            $redis = $this->getDI()->get('redisLib');
            //优先缓存
            $key        = RedisEnums::LEAVE_ASSETS_NOT_APPROVE_NUM . $staffId;
            $cache_data = $redis->get($key);
            if ($cache_data === false) {
                $result= $this->leaveStaffListNotDealNum($params['staffId'],$params['is_process'],$params['page'],$params['pagesize']);
                $this->getDI()->get("logger")->write_log('getNotResignAssetNum ' .json_encode($result).json_encode($params), "info");
                if (isset($result['data']['num']) && $result['data']['num']) {
                    $cache_data = (int)$result['data']['num'];
                } else {
                    $cache_data = 0;
                }
                //v15994新离职资产待处理数量
                $new_assets_count = (new MaterialAssetServer($this->lang,$this->timezone))->getLeaveAssetsManagerCount($params['staffId']);
                $this->getDI()->get("logger")->write_log('getLeaveAssetsManagerCount ' . $new_assets_count . ';params:' . json_encode($params), "info");
                $cache_data = $cache_data + $new_assets_count;
                $redis->set($key, $cache_data, 30 * 60); //30 分钟缓存

            }
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log('getNotResignAssetNum 出现异常 ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString(), "error");
            return 0;
        }
        return $cache_data;
    }

    /**
     * 获取离职资产数量
     * @param $staffId
     * @param $isProcess
     * @param $page
     * @param $pagesize
     * @return array
     */
    public  function leaveStaffListNotDealNum($staffId, $isProcess, $page, $pagesize){
        //获取下级员工
        $staffIds = $this->getSubordinateByManagerNotIsProcessNew($staffId);
        $result = ['data'=>[], 'is_over' => 1];
        if ($staffIds) {
            //排除by 撤销 驳回的员工
            $notByStaffs=$this->getByLeaveStaffIds($staffIds,false);
            if($notByStaffs){
                $notByStaffIds=array_column($notByStaffs,'submitter_id');
                $staffIds=array_diff($staffIds,$notByStaffIds);
            }
            //如果下级为空的话 返回 空
            if(empty($staffIds)){
                $result['data']= array(
                    'num'=>0,
                );
                return $result;
            }
            $num=$this->staffListForManagerNum(array('page'=>$page,'pagesize'=>$pagesize,'member_ids'=>$staffIds,'isProcess'=>$isProcess,'manager_staff_id'=>$staffId));
            $result['data']= array(
                'num'=>$num,
            );
        }
        return $result;
    }


    //获取自己的下级 并且职位  在Bike Courier、Van Courier、shop officer、DC Officer
    private function getSubordinateByManagerNotIsProcessNew($staffId)
    {
        if ($staffId) {
            $sql = "
                --
                select
                    hsi.staff_info_id
                from
                    hr_staff_info hsi
                where
                   hsi.manger =   '" . $staffId . "' and hsi.job_title in (" . implode(", ", [
                    enums::$job_title['van_courier'],
                    enums::$job_title['bike_courier'],
                    enums::$job_title['shop_officer'],
                    enums::$job_title['dc_officer'],
                    enums::$job_title['branch_supervisor'], // 即job_title=Branch Supervisor/Shop Supervisor，给直接上级发送资产处理消息与离职资产确认待办
                    enums::$job_title['shop_supervisor'],
                ]) . ") and hsi.is_sub_staff = 0 ";

            $subordinate = $this->getDI()->get("db_rby")->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
            if (!empty($subordinate)) {
                $staffIds = array_column($subordinate, "staff_info_id");
                return $staffIds;
            }
        }

        return [];
    }

    /**
     * by离职员工（排除撤销驳回）
     * @param $staffIds
     * @param bool $flag
     * @return mixed
     */
    private function getByLeaveStaffIds($staffIds ,$flag=true)
    {
        $leavesql="
        select 
                    max(resign_id) as resign_id
                from 
                    staff_resign
                where 
                    submitter_id in (" . implode(",", $staffIds) . ") group by submitter_id
        ";

        $leavestaff = $this->getDI()->get("db")->query($leavesql)->fetchAll(\PDO::FETCH_ASSOC);
      if($leavestaff){
          $leavestaff=array_column($leavestaff,'resign_id');

          $sql = "
        --
        select 
            * 
        from 
            staff_resign 
        where 
            resign_id in (" . implode(",", $leavestaff) . ") ";
          //v15994新资产需求,这里不再读取新的离职数据,15994上线时会把老数据全部改成asset_tag=0
          if($flag==true){
              $sql.='and status not in (3, 4) and asset_tag = 0  order by created_at desc';
          }else{
              $sql.='and ((status  in (3, 4) and asset_tag = 0) or (asset_tag = 1)) order by created_at desc';
          }
          $this->getDi()->get('logger')->write_log("获取离职员工id".$sql, 'info');
          $submiters = $this->getDI()->get("db")->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
      }else{
          $submiters=array();
      }
        return $submiters;

    }

    /**
     * 获取by 非by 离职的员工
     * @param $params
     * @return array
     */
    public function staffListForManagerNum($params)
    {
        $sql="select
    hsi.staff_info_id,
    hsi.name,
    hsi.name_en,
    hsi.mobile,
    hsi.job_title,
    hsi.sys_department_id,
    hsi.node_department_id,
    hsi.sys_store_id,
    hsi.hire_date,
    hsi.state,
    hsi.wait_leave_state,
    hsi.leave_date,
    hsi.leave_type,
    hsi.leave_reason,
    hsi.leave_reason_remark,
    lm.staff_info_id as lm_staff_info_id,
    lm.assets_operate_version as lm_assets_operate_version,
    lm.is_has_superior as lm_is_has_superior,
    lm.superior_operate_time as lm_superior_operate_time,
    lm.superior_operate_version as lm_superior_operate_version,
    lm.leave_date as lm_leave_date,
    lm.approval_status as lm_approval_status,
    (case when ss.category in (3, 6, 11) then 3 when lm.assets_remand_state is  null then 1 when        lm.assets_remand_state = 0 then 1 else lm.assets_remand_state end ) lm_assets_remand_state,
    (case when lm.money_remand_state is null then 1 when lm.money_remand_state = 0 then 1 else          lm.money_remand_state end ) lm_money_remand_state,
    (case when lm.leave_date is null then hsi.leave_date  when lm.leave_date = '0000-00-00 00:00:00'    then hsi.leave_date else lm.leave_date end) order_leave_date,
    ss.category as ss_category,
    hsi.leave_source
    from
    hr_staff_info hsi
    left join
    leave_manager lm
    on
    hsi.staff_info_id = lm.staff_info_id
    left join
    sys_store ss
    on
    hsi.sys_store_id = ss.id
    where
    (hsi.state = 1 and hsi.wait_leave_state = 1 or hsi.state = 2 and hsi.leave_date > '2020-04-17       00:00:00'  or lm.approval_status is not null)  and hsi.formal = 1 ";
        if(isset($params['member_ids'])&&$params['member_ids']){

            if($params['isProcess']){
                $sql.="and (hsi.staff_info_id in ( " . implode(", ", $params['member_ids']) . ") and (lm.is_has_superior = 1 and lm.superior_id = " . $params['manager_staff_id'].'))';
            }else{
                $sql.="and (hsi.staff_info_id in ( " . implode(", ", $params['member_ids']) . ") and (lm.is_has_superior = 0  or lm.is_has_superior is null))" ;
            }
        }
        //如果返回空sql，证明找不到数据，直接返回
        $this->getDI()->get('logger')->write_log('staffListForManagerNum -- staffList params : ' . json_encode($params, JSON_UNESCAPED_UNICODE). " sql " .  $sql, 'info');
        if (empty($sql)) {
            return ['page_count' => 0, 'rows' => []];
        }
        $sqlCount = explode("from", $sql);
        $result = $this->getDI()->get('db_rby')->prepare("select count(*) as count from " . $sqlCount[1]);
        $result->execute();
        $pageCount = $result->fetchColumn();
        return $pageCount;

    }
    /**
     *
     * 判断是否是DM area manager
     *
     *
     * @param $staffId
     *
     */
    private function DMOrAM($staffId)
    {
        return $this->getDI()->get("db_rbi")->query("
        --
        select 
            *
        from
            hr_staff_info
        where staff_info_id = " . $staffId . " and job_title in (" . implode(", ", [enums::$job_title['district_manager'], enums::$job_title['regional_manager']] ) . ")") -> fetch(\PDO::FETCH_ASSOC);
    }


    /**
     * 打卡日历 年月
     * @Access  public
     * @Param   request
     * @Return  array
     * 废弃
     */
    public function attendanceCalendar($paramIn = [])
    {
        //[1]参数定义
//        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
//        $years   = $this->processingDefault($paramIn, 'years');
//
//        //[2]查询当月日期
//        $date = $this->getMonthDay($years);
//
//        //[3]获取打卡信息
//        $param       = [
//            'staff_id' => $staffId,
//            'date'     => $date
//        ];
//        $audit_model = new AuditRepository($this->lang);
//
//        //新需求 ph日期 根据员工工作日 不同 返回的日期也不同 https://shimo.im/docs/dYHQWXv9VQtpKwTw/read
//        $staff_model = new StaffRepository();
//        $staff_info  = $staff_model->getStaffPosition($staffId);
//        //试用菲律宾项目 ph 数据返回结构需要变更为map
//        $holiday_data = $this->country_server->ph_days($staff_info);
//
//        $param['holidays'] = empty($holiday_data) ? [] : array_column($holiday_data,'day');
//        $resultDay             = $audit_model->attendanceCalendar($param);
////        $resultDay['holidays'] = $this->country_server->format_ph_days($holiday_data);
//        $resultDay['holidays'] = $holiday_data;
//
//        $returnData = ['data' => $resultDay];
//        return $this->checkReturn($returnData);

    }



    /**
     * 临时申请LH费
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function lhMoneyAddApply($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
//        $auditType     = $this->processingDefault($paramIn, 'audit_type', 2, 3);
        $lhDate        = $this->processingDefault($paramIn, 'lh_date');
        $lhTime        = $this->processingDefault($paramIn, 'lh_time');
        $lhPlateNumber = $this->processingDefault($paramIn, 'lh_plate_number');
        $auditReason   = strip_tags($this->processingDefault($paramIn, 'audit_reason'));
        $imagePathArr  = $this->processingDefault($paramIn, 'image_path');
        $version       = $this->processingDefault($paramIn, 'version', 2);
        $day_type      = intval($paramIn['act_date']);

        //[2]用户校验
        $staffData = $this->re['staff']->checkoutStaff($staffId);
        if (empty($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
        }


        $lh_datetime = $lhDate . ' ' . $lhTime;
        $date        = date('Y-m-d', strtotime($lh_datetime));
        $lh_datetime = date('Y-m-d H:i:s', strtotime($lh_datetime));


        //新增当日次日判断 限制 暂时去掉
        if ($day_type == 1) {//当日  必须不能在 00点到 06点之间

//            if($lh_datetime >= "{$date} 00:00:00" && $lh_datetime <= "{$date} 06:00:00")
//                return $this->checkReturn(-3, $this->getTranslation()->_('5206'));

        } else if ($day_type == 2) {//次日  必须不能在 21点到 24点之间
            $lh_datetime = date('Y-m-d H:i:s', strtotime($lh_datetime) + 24 * 3600);

//            if($lh_datetime >= "{$date} 21:00:00" && $lh_datetime <= "{$date} 23:59:59")
//                return $this->checkReturn(-3, $this->getTranslation()->_('5206'));
        }

        $model = new ApplyRepository();
        //验证该LH申请时间内 是否有 加班申请 LH和晚班 互斥
        $checkNight = $model->checkNightWork($staffId, $lhDate);
        if ($checkNight > 0)
            return $this->checkReturn(-3, $this->getTranslation()->_('5201'));

        //[5]申请插入
        $insetData = [
            'staff_info_id'   => $staffId,
            'apply_category'  => intval($paramIn['category']),
            'apply_type'      => 0,
            'start_time'      => $lh_datetime,
            'end_time'        => $lh_datetime,
            'lh_plate_number' => $lhPlateNumber,
            'reason'          => $auditReason,
            'version'         => intval($version),
            'date_at'         => $date,
        ];

        //图片url拼接
        $imgString = '';
        if (!empty($imagePathArr)) {
            $imgString = implode(',', $imagePathArr);
        }
        $insetData['image_path'] = $imgString;


        //验证时间是否合法期间内  需求定为 2019 3月16号  -  4月20号 三期日期 替换成 version--3 5.16-6.15
        if ($version == 1) {
            $start_date = "2019-03-16 00:00:00";
            $end_date   = "2019-04-20 23:59:59";
            $tra        = 5200;
        } else if ($version == 2) {
            $start_date = "2019-04-16 00:00:00";
            $end_date   = "2019-05-10 23:59:59";
            $tra        = 5204;
        } else if ($version == 3) {
            $start_date = "2019-05-16 00:00:00";
            $end_date   = "2019-06-15 23:59:59";
            $tra        = 5205;
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        if ($insetData['start_time'] < $start_date || $insetData['start_time'] > $end_date) {
            return $this->checkReturn(-3, $this->getTranslation()->_($tra));
        }

        //验证 是否有请假记录
        $exist = $model->checkLhRow($staffId, intval($paramIn['category']), $date);
        if ($exist > 0)
            return $this->checkReturn(-3, $this->getTranslation()->_('5202'));

        //验证当天是否存在 真实ot
        $ot_model = new OvertimeRepository($this->timezone);
        $check_ot = $ot_model->getOtByDate($staffId, $date);
        if (!empty($check_ot)) {
            $types = array_column($check_ot, 'type');//type  1 2 不让申请lh
            if (in_array(1, $types) || in_array(2, $types))
                return $this->checkReturn(-3, $this->getTranslation()->_('5201'));

        }
//        //验证 申请时间必须在21:00-次日6:00之间
//        if($lh_datetime > "{$date} 06:00:00" && $lh_datetime < "{$date} 21:00:00")
//            return $this->checkReturn(-3, $this->getTranslation()->_('5206'));

        $model = new ApplyRepository();
        $flag  = $model->applyInsert($insetData);
        if (!$flag) {
            return $this->checkReturn(-3, $this->getTranslation()->_('2109'));
        }

        return $this->checkReturn([]);
    }


    /**
     * 申请LH费用
     * @param array $paramIn
     * @return array
     */
    public function lhMoneyAdd($paramIn = [])
    {
        //[1]参数定义
        $staffId       = $this->processingDefault($paramIn, 'staff_id', 2);
        $auditType     = $this->processingDefault($paramIn, 'audit_type', 2, 3);
        $lhDate        = $this->processingDefault($paramIn, 'lh_date');
        $lhTime        = $this->processingDefault($paramIn, 'lh_time');
        $lhPlateNumber = $this->processingDefault($paramIn, 'lh_plate_number');
        $auditReason   = strip_tags($this->processingDefault($paramIn, 'audit_reason'));
        $imagePathArr  = $this->processingDefault($paramIn, 'image_path');

        //[2]用户校验
        $staffData = $this->re['staff']->checkoutStaff($staffId);
        if (empty($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
        }

        //[3]校验是否有添加LC的权限
        $checkDataFlag = $this->checkLcDataPosition($paramIn);
        if (!$checkDataFlag) {
            return $this->checkReturn(-3, $this->getTranslation()->_('2107'));
        }

        //[4]校验选择时间内是否有LH申请 todo 方便测试暂时隐藏
        $checkData = $this->checkLhDataByDate($paramIn);
        if (isset($checkData['code']) && $checkData['code'] == 0) {
            return $this->checkReturn(-3, $checkData['msg']);
        }

        //新增校验  在申请OT的时间段中不能再申请LH。否则提示：当前时段已申请OT，不能申请LH！
        //如果是待审和同意，不能申请；如果是没申请或者驳回，可以申请。

        $act_lh_datetime = date('Y-m-d H:i:s', strtotime($lhDate . ' ' . $lhTime));
        $exist           = $this->re['overtime']->getOvertimeInfo($staffId, $act_lh_datetime);
        if ($exist > 0) {//存在ot 不可申请
            return $this->checkReturn(-3, $this->getTranslation()->_('5201'));
        }

        //[5]申请插入
        $serialNo  = $this->getID();
        $insetData = [
            'staff_info_id'   => $staffId,
            'lh_date'         => $lhDate,
            'lh_time'         => $lhTime,
            'lh_plate_number' => $lhPlateNumber,
            'audit_reason'    => $auditReason,
            'status'          => 1,
            'audit_type'      => $auditType,
            'serial_no'       => (!empty($serialNo) ? 'LH' . $serialNo : NULL),
        ];
        if (!$auditId = $this->re['audit']->auditInsert($insetData, $staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('2109'));
        }

        //[6]插入图片
        if (!empty($imagePathArr)) {
            foreach ($imagePathArr as $k => $v) {
                $insertImgData = [
                    'audit_id'   => $auditId,
                    'image_path' => $v,
                ];
                $this->re['audit']->auditImgInsert($insertImgData);
            }
        }

        //加操作日志
        //获取员工名称
        $public_model          = new PublicRepository();
        $staff_name            = $public_model->getStaffName($staffId);
        $log['staff_id']       = $staffId;
        $log['type']           = 3;//分类 1补卡 2请假 3申请LH费 加班类型 5=工作日加班，6=节假日加班，7=晚班 8-节假日正常上班
        $log['original_type']  = 1;//原始状态 1 待审核 2 审核通过 3 驳回
        $log['original_id']    = $auditId;
        $log['operator']       = $staffId;
        $log['operator_name']  = $staff_name;
        $log['to_status_type'] = 1;
        $log_model             = new StaffAuditToolLog();
        $log_model->add_log($log, 'audit:lhMoneyAdd');
        //(new \FlashExpress\bi\App\Server\AuditListServer($this->lang, $this->timezone))->auditUpdateByTablesV2('staff_audit', $auditId);

        //调用push 发消息
        $message_title   = $this->getTranslation()->_('6006');
        $audit_type      = $this->getTranslation()->_('6005');
        $staffData       = $this->re['staff']->checkoutStaff($staffId);
        $higherStaffId   = $this->re['overtime']->getHigherStaffId(['staff_info_id' => $staffId]);
        $higher_staff_id = $higherStaffId ? $higherStaffId['value'] : '';

        $pushParam = [
            'staff_info_id' => $higher_staff_id,
            'message_title' => $message_title,
            'userinfo'      => $staffData,
            'lastInsert_id' => $auditId,
            'audit_type'    => $audit_type,
        ];
        $this->re['public']->pushMessage($pushParam);
        return $this->checkReturn([]);
    }

    /**
     * LH添加验证
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkLcDataPosition($paramIn = [])
    {
        #7828【BY】取消申请LH费https://l8bx01gcjr.feishu.cn/docs/doccnz1T6Dv9CAVvdgQdvkO0noP
        return false;
        //[1]校验添加权限
        $positionData = UC('lhPosition')['lhData'];
//        $departmentData = UC('lhPosition')['departmentData'];
        //[2]获取当前员工职位
        $staffId   = $this->processingDefault($paramIn, 'staff_id', 2);
        $staffData = $this->re['staff']->getStaffPosition($staffId);
        if (!empty($positionData) && !empty($staffData)) {
            $jobTitleId = $this->processingDefault($staffData, 'job_title', 2);
//            $sysDepartmentId = $this->processingDefault($staffData, 'sys_department_id', 2);
            $positionIdArr = array_keys($positionData);
//            $departmentIdArr = array_keys($departmentData);
//            if(in_array($jobTitleId, $positionIdArr) && in_array($sysDepartmentId, $departmentIdArr))
            if (in_array($jobTitleId, $positionIdArr)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验申请时间内是审批记录
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkLhDataByDate($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $lhDate  = $this->processingDefault($paramIn, 'lh_date');

        $param = [
            'staff_id' => $staffId,
            'lh_date'  => $lhDate,
        ];

        //[3]查询LH申请记录
        $lhData = $this->re['audit']->getLhData($param);
        if (!empty($lhData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('2108'));
        }
        return $this->checkReturn(1);
    }

    /**
     * 审批详情
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function auditDetail($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $auditId = $this->processingDefault($paramIn, 'audit_id');

        $param = [
            'staff_id' => $staffId,
            'audit_id' => $auditId,
        ];

        //[3]查询LH申请记录
        $auditDetail = $this->re['audit']->auditDetail($param, 1);
        return $this->checkReturn(['data' => $auditDetail]);
    }

    /**
     * 补卡次数查询
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function reissueCardCount($paramIn = [])
    {
        $staffId           = $this->processingDefault($paramIn, 'staff_id', 2);
        $param             = [
            'staff_id'          => $staffId,
            'reissue_card_date' => date('Y-m-d', time()),
        ];
        $punchMonthCardNum = $this->re['audit']->getAttendanceCarMonthData($param);
        $limitNum = 3;//正常员工 都是3次最多
        //泰国 个人代理 次数是取配置
        if(isCountry('TH') && in_array($paramIn['user_info']['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            //取配置
            $envModel = new SettingEnvServer();
            $setting_code = [
                'ic_reissue_days',
                'ic_reissue_times',
            ];
            $setting_val = $envModel->listByCode($setting_code);
            $setting_val = empty($setting_val) ? [] : array_column($setting_val, 'set_val', 'code');

            //个人代理 还有个 申请时间的限制
            $data['days'] = $setting_val['ic_reissue_days'] ?? 0;
            $limitNum = $setting_val['ic_reissue_times'] ?? 0;
        }

        $data['total'] = $limitNum;
        $data['used'] = $punchMonthCardNum;

        return $this->checkReturn(['data' => $data]);

    }

    /**
     * 查询是否有LC权限
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function isLhPermission($paramIn = [])
    {
        $checkDataFlag = $this->checkLcDataPosition($paramIn);
        if (!$checkDataFlag) {
            return $this->checkReturn([
                'msg'  => $this->getTranslation()->_('2107'),
                'data' => ['flag' => 2],
            ]);
        }

        $this->wLog('lh权限返回值:' . $paramIn['staff_id'], $checkDataFlag, 'audit/isLhPermission');
        return $this->checkReturn(['data' => ['flag' => 1]]);
    }

    /**
     * 请假回写打卡数据
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function leaveInPunchCard($paramIn = [])
    {
        //[1]初始化参数
        $dateFlag  = date('Y-m-d', time() - 48 * 3600);
        $staffId   = 0;
        $startDate = $this->processingDefault($paramIn, 'start_date', 1, $dateFlag);
        $endDate   = $this->processingDefault($paramIn, 'end_date', 1, $dateFlag);

        //[2]控制开始日期截止日期为两天前
        if ($startDate > $endDate) {
            return $this->checkReturn(-3, '开始时间必须小于结束时间');
        }
        if ($startDate > $dateFlag) {
            $startDate = $dateFlag;
        }
        if ($endDate > $dateFlag) {
            $endDate = $dateFlag;
        }
        $this->wLog('记录入参', $paramIn, 'AUDIT：leaveInPunchCard');
        $this->wLog('员工号', $staffId, 'AUDIT：leaveInPunchCard');

        //[3]获取日历数据
        $allDate = getDateFromRange($startDate, $endDate);
        //[4]处理主业务
        if (!empty($allDate)) {
            foreach ($allDate as $key => $value) {
                //[4-1]查询请假记录【请假已审批的数据】
                $levelData = $this->re['audit']->selectLevelByDate($value, $staffId);
                $this->wLog('请假员工数据', $levelData, 'AUDIT');

                if (!empty($levelData)) {
                    //[4-2]查询员工打卡记录
                    $levelStaffId  = array_unique(array_column($levelData, 'staff_info_id'));
                    $punchCardData = $this->re['audit']->selectPunchCardDataByLevelStaffId($value, $levelStaffId);
                    $this->wLog('打卡员工数据', $punchCardData, 'AUDIT');
                    $punchCardData = [];
                    if (!empty($punchCardData)) {
                        $punchCardData = array_column($punchCardData, NULL, 'staff_info_id');
                    }

                    //[4-3]处理员工打卡 请假主逻辑
                    foreach ($levelData as $k => $v) {
                        $staffId = $v['staff_info_id'];
                        if (in_array($v['staff_info_id'], array_keys($punchCardData))) {
                            $staffPersonPunchCardData = $punchCardData[$v['staff_info_id']];
                            //[4-3-1]验证上班时间是否满足8小时
                            if (!empty($staffPersonPunchCardData['started_at']) && !empty($staffPersonPunchCardData['end_at'])) {
                                $hourLength = intval((strtotime($staffPersonPunchCardData['end_at']) - strtotime($staffPersonPunchCardData['started_at']) / (3600 * 8)));

                                //[4-3-1]处理半天情况
                                if ($hourLength >= 4 && $hourLength < 8) {
                                    $this->askForLeaveProcessing($value, $v['level_type_day'], $staffId, 1, $staffPersonPunchCardData['started_at']);
                                    continue;
                                }

                                //[4-3-2]处理全天情况
                                if ($hourLength < 4) {
                                    $this->askForLeaveProcessing($value, $v['level_type_day'], $staffId, 3);
                                    continue;
                                }
                            } else {
                                $this->askForLeaveProcessing($value, $v['level_type_day'], $staffId);
                                continue;
                            }
                        } else {
                            $this->askForLeaveProcessing($value, $v['level_type_day'], $staffId);
                            continue;
                        }
                    }
                }
            }
        }
        return $this->checkReturn(1);
    }

    /**
     * 处理请假
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function askForLeaveProcessing($levelDay = '', $levelTypeDay = '', $staffId = '', $type = 2, $startDate = '')
    {
        $returnFlag = false;
        if (!empty($levelDay) && !empty($levelTypeDay) && !empty($staffId)) {
            //[1]处理半天 与 全天 情况
            if ($type == 1) {
                //全天请假
                if ($levelTypeDay == 3) {
                    if (date('H', strtotime($startDate)) < '12:00') {
                        $param = [
                            'levelDay'       => 2,
                            'level_type_day' => $levelTypeDay,
                            'staff_info_id'  => $staffId,
                        ];
                        $this->re['audit']->editWorkByLevel($param);

                    } else {
                        $param = [
                            'levelDay'       => 1,
                            'level_type_day' => $levelTypeDay,
                            'staff_info_id'  => $staffId,
                        ];
                        $this->re['audit']->editWorkByLevel($param);

                    }
                }
                //上午请假
                if ($levelTypeDay == 1) {
                    if (date('H', strtotime($startDate)) >= '12:00') {
                        $param = [
                            'levelDay'       => 1,
                            'level_type_day' => $levelTypeDay,
                            'staff_info_id'  => $staffId,
                        ];
                        $this->re['audit']->editWorkByLevel($param);
                    }
                }
                //下午请假
                if ($levelTypeDay == 1) {
                    if (date('H', strtotime($startDate)) < '12:00') {
                        $param = [
                            'levelDay'       => 2,
                            'level_type_day' => $levelTypeDay,
                            'staff_info_id'  => $staffId,
                        ];
                        $this->re['audit']->editWorkByLevel($param);

                    }
                }
            } else {
                $param = [
                    'levelDay'       => $levelDay,
                    'level_type_day' => $levelTypeDay,
                    'staff_info_id'  => $staffId,
                    'type'           => $type,
                ];
                $this->re['audit']->editWorkByLevel($param);
            }
            $returnFlag = true;
        }
        return $returnFlag;
    }


    /**
     *
     * !!!!!!!!!!!!!!废弃
     * 撤销申请
     * @param array $paramIn
     * @return array
     */
    public function cancel($paramIn = [],$ismy = 0)
    {
        return;
    }

    public function checkFeeder($paramIn){

        $staffId = $paramIn["staff_id"];
        if (empty($staffId)) {
            return false;
        }
        //获取员工的职级
        $staffInfo = HrStaffInfoModel::findFirst([
                                                     'conditions' => "staff_info_id = :staff_info_id:",
                                                     'bind' => [
                                                         'staff_info_id' => $staffId,
                                                     ],
                                                     'columns' => 'job_title_grade,job_title_grade_v2,job_title',
                                                 ]);
        //fbi不存在员工工号、没有职级不能申请
        if (empty($staffInfo)) {
            return false;
        }
        //是Van 并且角色是feeder A 不能申请加班
        //https://l8bx01gcjr.feishu.cn/docs/doccn0H9wsOtlNfCAIjAQ3AoAGd
        if($staffInfo->job_title == HrJobTitleModel::JOB_TITLE_VAN){
            $role = HrStaffInfoPositionModel::find([
                                                       'conditions' => "staff_info_id = :staff_info_id:",
                                                       'bind' => [
                                                           'staff_info_id' => $staffId,
                                                       ],
                                                       'columns' => 'position_category',
                                                   ])->toArray();
            if($role && in_array(RolesModel::ROLE_FEEDER_A,array_column($role,'position_category'))){
                return  true;
            }
        }
        return  false;
    }

    /**
     * 离职权限
     * @return bool
     */
    protected function isResignPermission(): bool
    {
        if (empty($this->staffInfo)) {
            return false;
        }

        if ($this->staffInfo['formal'] != HrStaffInfoModel::FORMAL_1) {
            return false;
        }

        return !in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether);
    }


    protected function checkIsShowCancelContract(): bool
    {
        return in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether);
    }

    /**
     * 请假权限
     * @return bool
     */
    protected function isLeavePermission(): bool
    {
        if (empty($this->staffInfo)) {
            return false;
        }
        //新增权限判断 https://flashexpress.feishu.cn/docx/USK1dOhAiod8STxnC0ccJqacn4d
        $leaveServer = new LeaveServer($this->lang, $this->timezone);
        if(!$leaveServer->leavePermission($this->staffInfo)){
            return false;
        }

        return !in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether);
    }
    //个人代理权限
    protected function unPaidLeavePermission(){
        if (empty($this->staffInfo)) {
            return false;
        }
        return in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether);
    }

    /**
     * 补卡权限
     * @return bool
     */
    protected function isMakeUpPermission(): bool
    {
        if (empty($this->staffInfo)) {
            return false;
        }

        return true;
    }
    //泰国个人代理补卡权限
    protected function isUnpaidMakeUpPermission(): bool
    {
        if (empty($this->staffInfo)) {
            return false;
        }

        return in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether);
    }

    /**
     * 解约个人代理权限
     * @return bool
     */
    public function isCompanyTerminationContractPermission(): bool
    {
        return (new CompanyTerminationContractServer($this->lang,
            $this->timezone))->checkPermission($this->staffInfo['staff_info_id']);
    }

    /**
     * HCM入口
     * @return bool
     */
    protected function isShowHCMPermission(): bool
    {
        $setVal          = (new SettingEnvServer())->getMultiEnvByCode(['by_hcm_role', 'by_hcm_staff_id']);
        $by_hcm_role     = !empty($setVal['by_hcm_role']) ? explode(',', $setVal['by_hcm_role']) : [];
        $by_hcm_staff_id = !empty($setVal['by_hcm_staff_id']) ? explode(',', $setVal['by_hcm_staff_id']) : [];
        if (in_array($this->staffInfo['staff_info_id'], $by_hcm_staff_id)) {
            return true;
        }
        if (array_intersect($this->staffInfo['positions'], $by_hcm_role)) {
            return true;
        }
        return false;
    }

    // 报销申请入口
    protected function isShowReimbursementApplyPermission(): bool
    {
        if (empty($this->staffInfo)) {
            return false;
        }

        return $this->staffInfo['formal'] == HrStaffInfoModel::FORMAL_1 && $this->staffInfo['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID;
    }


    /**
     * 获取权限
     * @param array paramIn
     * @return array
     */
    public function getListPermission($paramIn = [])
    {
        //LH移除
        // $result['LH'] = $this->checkLcDataPosition($paramIn) == true ? 1 : 2;
        //补卡
        $result['AT'] = 1; //默认存在
        //请假
        $result['LE'] = 1;//默认存在
        //新耗材申请上线老的耗材入口移除
        //$result['Wms'] = $this->getWmsPermission($paramIn) == true ? 1 : 2;
        //修改里程权限
        $result['Mile'] = $this->getMilePermission($paramIn) == true ? 1 : 2;
        //加班-所有人都可以申请OT
        $result['OT'] = $this->getOTPermission($paramIn) == true ? 1 : 2;
        //出差
        $result['Trip'] = $this->getTripPermission($paramIn) == true ? 1 : 2;
        //车辆里程
        $result['Vehicle'] = $this->getVehiclePermission($paramIn) == true ? 1 : 2;
        //HC
        $result['HC'] = $this->checkHcDataRole($paramIn) == true ? 1 : 2;
        //补申请
        $result['apply'] = $this->getApplyPermission($paramIn) == true ? 1 : 2;
        //加班车
        $result['fleet'] = $this->getFleetPermission($paramIn) == true ? 1 : 2;
        //离职-所有人都可以申请离职
        $result['resign'] = $this->isResignPermission() ? 1 : 2;
        //外协员工
        $result['OS'] = $this->getOSPermission($paramIn) == true ? 1 : 2;
        //资产申请
        $result['Asset'] = $this->getASPermission($paramIn) == true ? 1 : 2;
        //举报
        $result['Report'] = $this->getReportPermission($paramIn) == true ? 1 : 2;
        //到岗确认
        $result['Confirm'] = $this->getEntryConfirmPermission($paramIn) == true ? 1 : 2;
        //工资条pdf
        $result['salary'] = $this->salary_pdf_permission($paramIn) == true ? 1 : 2;
        //油费补贴
        $result['fuel_subsidy'] = $this->getFuelSubsidyPermission($paramIn) == true ? 1 : 2;
        //在职证明
        $result['on_job'] = $this->on_job_pdf_permission($paramIn) == true ? 1: 2;
        //工资证明
        $result['payroll'] = $this->on_job_pdf_permission($paramIn) == true ? 1: 2;
        //离职资产确认
        $result['resign_as'] = $this->getResignAssetPermision($paramIn) == true ? 1: 2;
        //转岗
        $result['TF'] = $this->getJobtransferPermission($paramIn) == true ? 1: 2;
        //运费申请
        //$result['FD_nw'] = $this->getFreightDiscNetworkPermission($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_shop'] = $this->getFreightDiscShopPermission($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_other'] = $this->getFreightDiscOtherPermission($paramIn) == true ? 1 : 2;
        //抄送列表
        $result['CC'] = 1;
        //黄牌项目出差
        $result['yc_Trip'] = $this->getYCTripPermission($paramIn) == true ? 1 : 2;
        // 销售CRM
        $result['Sales_CRM'] = $this->getSalesCRMPermission($paramIn) == true ? 1 : 2;
        //工服购买排除外协员工
        $result['InteriorOrder'] = $this->getInteriorOrdersPermission($paramIn) == true ? 1 : 2;
        //为员工增减角色审批
        $result['AR'] = $this->getAdjustRolePermission($paramIn) == true ? 1 : 2;
        // 后勤-报销申请
        $result['fuel_budget']  = 2;
        //我的面试
        $result['my_interview'] = (new InterviewRepository($this->timezone))->myInterview($paramIn) == true ? 1 : 2;
        //我的简历
        $resumeRecommendNum   = (new ResumeRecommendRepository())->getShowTotal($paramIn);
        $result['rr_is_show'] = !empty($resumeRecommendNum['is_show']) ? 1 : 2;
        //紧急上报
        $result['accident'] = env('switch_accident', 1) == 1 ? 1 : 2;
        //hcm入口
        $result['HCM'] = $this->isShowHCMPermission() ? 1 : 2;
        //过滤外协员工权限
        $paramIn['permission_list'] = $result;
        $result = $this->outsourcingPermissionFilter($paramIn);
        //耗材调拨入口
        $result['package_allot'] = $this->isShowPackageAllotPermission($this->staffInfo) ? 1 : 2;
        return $this->checkReturn(['data' => $result]);
    }

    /**
     * 预支油费
     * @param $paramIn
     * @return bool
     */
    public  function getAdvanceFuelPermission($paramIn): bool
    {
        if (!isCountry('MY')) {
            return false;
        }
        if (isset($paramIn['staffInfo']['hire_type']) && !in_array($paramIn['staffInfo']['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
            return false;
        }
        $setVal = (new SettingEnvServer())->getSetVal(SettingEnvEnums::ADVANCE_FUEL_JOB_TITLE_IDS);
        $jobIds = empty($setVal) ? [] : explode(',', $setVal);
        if($jobIds && in_array($paramIn['staffInfo']['job_title'], $jobIds)){
            return true;
        }
        return false;
    }

    /**
     * 快递员推荐入口权限
     * @param $paramIn
     * @return bool
     */
    public function getCourierRecommendPermission($paramIn): bool
    {
        $node_department_id = $paramIn['staffInfo']['node_department_id'] ?? '';
        $department_id      = $paramIn['department_id'] ?? '';
        if (!isCountry('MY')) {
            return false;
        }
        $department_id_arr = (new SettingEnvServer())->getSetVal('recommended_dep', ',');
        if (empty($department_id_arr)) {
            return false;
        }
        $fleDepartmentModel = new FleSysDepartmentModel();

        $department_all_ids = [];
        foreach ($department_id_arr as $id) {
            $ids                = $fleDepartmentModel->getSpecifiedDeptAndSubDept($id);
            $department_all_ids = array_merge($department_all_ids, $ids);
        }
        if (
            !empty($department_all_ids) &&
            (in_array($node_department_id, $department_all_ids) || in_array($department_id, $department_all_ids))
        ) {
            return true;
        }
        return false;
    }

    /**
     * 暂停个人代理接单权限
     * @param $paramIn
     * @return bool
     */
    public  function getAgentSuspendWorkPermission($paramIn): bool
    {
        if (!isCountry('TH')) {
            return false;
        }
        $departmentIds              = (new SysDepartmentModel())->getSpecifiedDeptAndSubDept(enums::$department['Network Management']);
        $manageInfo                 = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getDepartmentRelateRegionPieceStore($departmentIds);
        return (new StaffServer())->checkStaffManageInfo($paramIn['staff_id'], $manageInfo['region_id'], $manageInfo['piece_id'],$manageInfo['store_id']);
    }

    public  function getFuelBudget($paramIn): bool
    {
        if (!isCountry('TH')) {
            return false;
        }
        return $paramIn['organization_type'] == 2;
    }


    // 用车申请
    public function getFuelSubsidyPermission($paramIn)
    {
        if (!isCountry('TH')) {
            return false;
        }
        //新需求 用车申请 给所有总部员工
        return $paramIn['organization_type'] == 2;
    }


    /**
     * Notes: 获取举报权限
     * @param $paramIn
     * @return bool
     */
    public function getReportPermission($paramIn)
    {
        $country_code = $this->config->application->country_code;
        if (!in_array(strtolower($country_code), ['th', 'my', 'ph', 'id'])) {
            return false;
        }
        //检查配置,配置中的工号则有 入口权限
        $config = (new ReportServer($this->lang, $this->timezone))->checkReportJurisdictionConfig($paramIn['staff_id']);
        if($config['is_in_config']) {
            return true;
        }

        if($this->validateReportPermissionByRoles($paramIn['staff_id'])) {
            return true;
        }

        $sql       = " SELECT id FROM hr_staff_items WHERE `item` = 'MANGER' AND `value` = '{$paramIn['staff_id']}' ; ";
        $data      = $this->getDI()->get('db_rby')->query($sql);
        $countData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if ($countData) {
            return true;
        }

        //是否为部门负责人
        $departmentManager = SysDepartmentModel::findFirst([
            'conditions' => "manager_id = :staff_id: and deleted = :deleted:",
            'bind' => [
                'staff_id'  => $paramIn['staff_id'],
                'deleted'   => enums::IS_DELETED_NO,
            ],
        ]);
        if(!empty($departmentManager)) {
            return true;
        }

        //是否为大区负责人
        $regionManager = SysManageRegionModel::findFirst([
            'conditions' => "manager_id = :staff_id: and deleted = :deleted:",
            'bind' => [
                'staff_id'  => $paramIn['staff_id'],
                'deleted'   => enums::IS_DELETED_NO,
            ],
        ]);
        if(!empty($regionManager)) {
            return true;
        }

        //是否为片区负责人
        $pieceManager = SysManagePieceModel::findFirst([
            'conditions' => "manager_id = :staff_id: and deleted = :deleted:",
            'bind' => [
                'staff_id'  => $paramIn['staff_id'],
                'deleted'   => enums::IS_DELETED_NO,
            ],
        ]);
        if(!empty($pieceManager)) {
            return true;
        }

        //是否为网点负责人
        $storeManager = SysStoreModel::findFirst([
            'conditions' => "manager_id = :staff_id: and state = :state:",
            'bind' => [
                'staff_id'  => $paramIn['staff_id'],
                'state'     => SysStoreModel::STATE_1,
            ],
        ]);
        if(!empty($storeManager)) {
            return true;
        }

        return false;
    }
    //QAQC
    public function isQAQC($staff_id){
        /*$staffSql = "SELECT sys_department_id FROM hr_staff_info WHERE `staff_info_id` =  '{$staff_id}';";
        $staffData      = $this->getDI()->get('db_rbi')->query($staffSql);
        $staffInfo = $staffData->fetch(\Phalcon\Db::FETCH_ASSOC);
        // 如果是 QAQC 部门直接返回 true
        if($staffInfo && $staffInfo['sys_department_id'] == 12){
            return true;
        }*/
        $staffInfo = HrStaffInfoPositionModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: AND position_category=:position_category:",
            'bind' => ['staff_info_id' => $staff_id, 'position_category'=>enums::QAQC_ROLES_ID],
        ]);
        if($staffInfo){
            return true;
        }
        return false;
    }

    /**
     * 根据当前登录人角色判断是否可以进行举报
     * @param $staff_id
     * @return bool
     */
    public function validateReportPermissionByRoles($staff_id)
    {
        $isQAQC = $this->isQAQC($staff_id);
        if ($isQAQC) {
            return true;
        }

        if (isCountry('PH') && HrStaffInfoPositionModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id: and position_category in ({positions:array}) ',
                'bind'       => [
                    'staff_id'  => $staff_id,
                    'positions' => [enums::NETWORK_QC_ROLES_ID, enums::HUB_QAQC_ROLES_ID],
                ],
            ])) {
            return true;
        }

        $isSpecialPosition = HrStaffInfoPositionModel::find([
            'conditions' => ' staff_info_id = :staff_id: and position_category in ({positions:array}) ',
            'bind'       => [
                'staff_id'  => $staff_id,
                'positions' => [HrStaffPositionEnums::ROLE_HRBP, HrStaffPositionEnums::ROLE_SAFETY_SUPERVISOR],
            ],
        ])->toArray();
        if ($isSpecialPosition) {
            return true;
        }

        return false;
    }


    /**
     * 获取资产申请权限 和 我的tab  公共资产显示
     * @param array paramIn
     * @param boolean paramIn 是否只判断白名单
     * @return boolean
     */
    public function getASPermission($paramIn = [], $isOnlyWhiteList = false)
    {
        $staffId = $paramIn['staff_id'];
        //[2]获取当前员工职位
        $staff_re = new StaffRepository($this->lang);
        $info = $staff_re->getStaffPositionv3($staffId);

        //申请权限
        //白名单 ：工号为[19685,24455,40450,25921,29053] 或者 职位是Regional Manager(79)、Assistant Regional Manager(269)
        //DC、SP网点: 网点正主管(职位是16)
        //SHOP网点: 网点正主管
        //HUB网点: 指定工号
        $staffArr = explode(',', env('asset_request_white_list', "24455,40450,38983,19685,47094,19060,21426,21328"));
        if (in_array($staffId, $staffArr) || in_array($info['job_title'], [
                enums::$job_title['regional_manager'],
                enums::$job_title['district_manager'],
                enums::$job_title['area_manager'],
            ])) {
            return true;
        }

        if ($isOnlyWhiteList === true) {
            // 只验证工号职位白名单
            return false;
        }

        return $this->check_asset_permission($info);
    }



    //资产权限 从country 搬出来的 只针对泰国
    public function check_asset_permission($info)
    {
        // 泰国弃用
        $staffId = $info['id'];

        if ($info['organization_type'] == 2) {
            // 总部用户
            $staff_re = new StaffRepository($this->lang);
            $staffInfo = $staff_re->checkoutStaffBi($staffId);
            if ($staffInfo && isset($staffInfo['job_title_level']) && in_array($staffInfo['job_title_level'], [2, 3, 4]) || in_array($staffId, [19982, 31856])) {
                return true;
            }
        }
        //1(DC)，2(SP)，457(shop)，8(HUB)
        if (empty($info) || !in_array($info['category'], [1, 2, 4, 5, 7, 8, 9, 10, 12])) {
            return false;
        }

        switch ($info['category']) {
            // DC/SP
            case 1:
            case 2:
                // BDC
            case 10:
                if (in_array($info['job_title'], [enums::$job_title['branch_supervisor']])) {
                    return true;
                }
                break;
            // shop
            case 4:
            case 5:
            case 7:
                //OS
            case 9:
                if (in_array($info['job_title'], [enums::$job_title['shop_supervisor'], enums::$job_title['onsite_supervisor']])) {
                    return true;
                }
                break;
            //hub
            case 8:
                if(in_array($info['job_title'], [
                    enums::$job_title['hub_supervisor'],
                    enums::$job_title['hub_manager'],
                    enums::$job_title['hub_admin_officer'],
                    enums::$job_title['freight_hub_manager'],
                    enums::$job_title['freight_hub_outbound_supervisor'],
                    enums::$job_title['freight_hub_QAQC_supervisor'],
                    enums::$job_title['freight_hub_inbound_supervisor']])
                ) {
                    return true;
                }
                break;
            case 12:
                if (in_array($info['job_title'], [
                    enums::$job_title['hub_manager'],
                    enums::$job_title['hub_supervisor'],
                    enums::$job_title['hub_admin_officer'],
                    enums::$job_title['freight_hub_manager'],
                    enums::$job_title['freight_hub_inbound_supervisor'],
                    enums::$job_title['freight_hub_QAQC_supervisor'],
                    enums::$job_title['freight_hub_outbound_supervisor'],
                ])) {
                    return true;
                }
        }

        return false;
    }

    /**
     * 获取物料申请权限
     * @param array paramIn
     * @return boolean
     */
    public function getWmsPermission($paramIn = [])
    {
        //[1]校验添加权限
        $admin_group = UC('wmsRole')['admin_group'];
        $staffId     = $paramIn['staff_id'];

        //admin小組有提交物料权限
        if (isset($admin_group[$staffId])) {
            return true;
        }

        return $this->check_wrs_permission($staffId);
    }

    public function check_wrs_permission($staffId)
    {
        // hub hub supervisor
        // dc/sp branch supervisor
        // shop shop supervisor
        $staff_re = new StaffRepository($this->lang);
        $info = $staff_re->getStaffPositionv3($staffId);
        //1(DC)，2(SP)，457(shop)，8(HUB)
        if(empty($info) || !in_array($info['category'],[1,2,4,5,7,8,9,10,12])){
            return false;
        }

        switch ($info['category']) {
            // DC/SP
            case 1:
            case 2:
                // BDC
            case 10:
                if (in_array($info['job_title'], [enums::$job_title['branch_supervisor']])) {
                    return true;
                }
                break;
            // shop
            case 4:
            case 5:
            case 7:
                // OS
            case 9:
                if (in_array($info['job_title'], [enums::$job_title['shop_supervisor'], enums::$job_title['onsite_supervisor']])) {
                    return true;
                }
                break;
            //hub
            case 8:
                if (in_array($info['job_title'], [
                    enums::$job_title['freight_hub_QAQC_supervisor'],
                    enums::$job_title['hub_supervisor'],
                    enums::$job_title['hub_manager'],
                    enums::$job_title['hub_admin_officer'],
                    enums::$job_title['freight_hub_outbound_supervisor'],
                    enums::$job_title['freight_hub_inbound_supervisor'],
                ])) {
                    return true;
                }
                break;
            case 12:
                if (in_array($info['job_title'], [
                    enums::$job_title['hub_manager'],
                    enums::$job_title['hub_supervisor'],
                    enums::$job_title['freight_hub_QAQC_supervisor'],
                    enums::$job_title['freight_hub_inbound_supervisor'],
                    enums::$job_title['freight_hub_outbound_supervisor'],
                ])) {
                    return true;
                }
        }

        return false;
    }

    /**
     * 获取出差申请权限
     * @param array paramIn
     * @return boolean
     */
    public function getTripPermission($paramIn = [])
    {
        if (empty($this->staffInfo)) {
            return false;
        }
        return in_array($this->staffInfo['formal'], [
                HrStaffInfoModel::FORMAL_1,
                HrStaffInfoModel::FORMAL_INTERN,
            ]) && !in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether);
    }

    /**
     * 获取黄牌项目出差权限
     * @param array $paramIn
     * @return boolean
     */
    public function getYCTripPermission($paramIn = []) {

        if (!isCountry()) {
            return false;
        }

        if (in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
            return false;
        }
        
        //Network部门的van courier(110)、branch supervisor(16)可以申请
        $staff_info_id = $paramIn['staff_id'];
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_id:",
            'bind'       => [
                'staff_id' => $staff_info_id,
            ],
        ]);
        if(!empty($staffInfo) && $staffInfo->sys_department_id == 4 && in_array($staffInfo->job_title,[16, 110])) {
            return true;
        }

        return false;
    }

    /**
     * 获取是非外协员工权限
     * @param array $paramIn
     * @return boolean
     */
    public function getInteriorOrdersPermission($paramIn = [])
    {
        if (!isCountry() && !isCountry('MY') && !isCountry('PH') && !isCountry('LA')) {
            return false;
        }
        if (empty($this->staffInfo)) {
            return false;
        }
        //外协的屏蔽这个 https://l8bx01gcjr.feishu.cn/docs/doccn5r1mVUUC3BpBcSBIS4RjNb

        if($this->staffInfo['formal'] ) {
            //泰国 || 菲律宾 && 个人代理入口关闭
            return ((isCountry() || isCountry('PH')) && $this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) ? false : true;
        }

        return false;
    }

    /**
     * 获取剩余假期天数
     * @param $param
     */
    public function get_left_holidays($param)
    {
        return $this->checkReturn(['data' => []]);
    }

    //hcm工具 获取额度
    public function rpcHolidaysNum($param)
    {
        $staff_id        = $this->staffId = intval($param['staff_id']);
        $this->staffInfo = $param['user_info'];
        $year            = date('Y', time());
        $audit_model     = new AuditRepository($this->lang);

        //所有假期类型
        $data = $this->type_book();

        //根据员工属性 获取对应权限类型
        $data           = array_column($data, null, 'code');
        $permissionData = $this->staffLeaveType($param['user_info'], $data);

        //一次性假期 额度查询 逻辑不同
        $this->oneTypes = array_merge($this->one_time, $this->one_send);
        $str = '';
        $bind = [
            'staff_info_id' => $staff_id,
            'one_types'     => $this->oneTypes,
            'this_year'     => $year];
        if(!empty($this->yearTypes)){
            $str = " or (year = :this_year: and leave_type in ({year_types:array}))";
            $bind['year_types'] = $this->yearTypes;
        }

        $conditions = "staff_info_id = :staff_info_id: and (leave_type in ({one_types:array})  or year = :this_year: {$str})";
        //获取固化的 额度 包括按年发放的 和一次性的
        $this->remainData = StaffLeaveRemainDaysModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
        if (!empty($this->remainData)) {
            $this->remainData = array_column($this->remainData, null, 'leave_type');
        }
        //需要计算的类型 去掉无权限之后的
        $leftTypes  = array_column($permissionData, 'code');
        $countTypes = array_diff($leftTypes, $this->oneTypes, $this->yearTypes);
        if (!empty($countTypes)) {
            $str            = implode(',', $countTypes);
            $applied_days   = $audit_model->get_used_leave_days($staff_id, $year, $str);
            $this->sum_days = array_column($applied_days, null, 'leave_type');
        }
        //获取配置表的额度总额
        $this->limit_array = $audit_model->get_all_leave_days();
        $this->limit_types = array_keys($this->limit_array);
        $leave_server      = new LeaveServer($this->lang, $this->timezone);
        $leave_server      = Tools::reBuildCountryInstance($leave_server, [$this->lang, $this->timezone]);
        //对应国家内容
        $permissionData = $this->rpcHolidayBody($permissionData, $leave_server);

        //泰国 病假 拆分 特殊情况
        if(!empty($this->unpaidInfo)){
            $permissionData[] = $this->unpaidInfo;
        }
        $vacationServer = new VacationServer($this->lang, $this->timezone);

        //转 string 给前端
        foreach ($permissionData as &$item) {
            //改类型 前端用
            $item['day_limit'] = $vacationServer->format_leave_days($item['day_limit']);
            $item['day_sub']   = $vacationServer->format_leave_days($item['day_sub']);
        }
        return $permissionData;
    }

    //找对应国家的 额度方法
    public function rpcHolidayBody($permissionData, $leave_server){
        return [];
    }



    /**
     * 获取车辆里程权限
     * @param array paramIn
     * @return boolean
     */
    public function getVehiclePermission($paramIn = [])
    {
        if (empty($this->staffInfo)) {
            return false;
        }

        $setting_model = new BySettingRepository();
        $miles_job = $setting_model->get_setting('miles_job_title');
        if(empty($miles_job))
            $miles_job = '110';
        $job_titles = explode(',',$miles_job);
        if (in_array($this->staffInfo['job_title'],
                $job_titles) && $this->staffInfo['formal'] == HrStaffInfoModel::FORMAL_1) {
            return true;
        }
        return false;
    }


    /**
     * 获取补申请 三期 lh 权限 判断菜单
     * 只有Van courier和Bike courier
     * @param array $paramIn
     */
    public function getApplyPermission($paramIn = [])
    {
        if (empty($this->staffInfo)) {
            return false;
        }
        $permission  = UC('applyJobTitle');
        if (!empty($this->staffInfo['job_name'])) {//存在记录
            if (in_array($this->staffInfo['job_name'], $permission))
                return true;
            else
                return false;
        }
        return false;

    }

    /**
     * 获取加班车申请权限
     * 全部网点人员 & Sales
     * @param array $paramIn
     * @return boolean
     */
    public function getFleetPermission($paramIn = [])
    {
        if (empty($this->staffInfo)) {
            return false;
        }
        $staffId          = $this->processingDefault($paramIn, 'staff_id', 2);
        $organizationType = $this->processingDefault($paramIn, 'organization_type', 2);
        $position         = $this->processingDefault($paramIn, 'positions', 3);
        $positionData     = (new SettingEnvServer())->getSetVal('fleet_apply_roles',',');

        if (!empty($organizationType) && !empty($position)) {
            //网点在编人员并且不是快递员、大区经理才可以申请
            if ($organizationType == 1 && array_intersect($position, $positionData)) {
                if ($this->staffInfo['formal'] == 1) { //是否在编
                    return true;
                } else {
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * 获取外协员工申请权限
     * @param array $paramIn
     * @return boolean
     */
    public function getOSPermission($paramIn = [])
    {
        if (!isCountry('TH')) {
            return false;
        }
        //新增需求 申请人是 ffm网点类型的负责人 并且 Thailand Fulfillment[243]及下级
        $server = new OsStaffServer($this->lang,$this->timezone);
        $ffmFlag = $server->ffmPermission($paramIn);
        if($ffmFlag){
            return true;
        }

        $jobTitlesData        = UC('outsourcingStaff')['jobTitlesNot'];
        $hubRequestRole       = UC('outsourcingStaff')['osLongPeriodRequestRole'];
        $motorcadeRequestRole = UC('outsourcingStaff')['osMotorcadeRequestRole'];
        $hubOsRoles           = UC('outsourcingStaff')['hubOsRoles'];

        $roles                = $this->processingDefault($paramIn, 'positions', 3);
        $job_title            = $this->processingDefault($paramIn, 'job_title', 2);
        $organizationId       = $this->processingDefault($paramIn, 'organization_id', 2);
        $type                 = $this->processingDefault($paramIn, 'organization_type', 2);
        $departmentId         = $this->processingDefault($paramIn, 'department_id', 2);

        //hub 外协工单，分拨经理 有入口权限
        if(array_intersect($hubOsRoles, $roles)) {
            return true;
        }

        //PDC Operations
        $pdcManagerList = (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getManagerByDepartmentIdFromCache([OsStaffServer::DEPARTMENT_ID_PDC_OPERATIONS]);
        if(in_array($paramIn['staff_id'], $pdcManagerList)) {
            return true;
        }

        if ((!empty($jobTitlesData) || !empty($hubRequestRole)) || !empty($motorcadeRequestRole)) {

            //1-[短期外协申请条件][网点在编人员并且不jobTitlesData崗位才可以申请]
            //2-[长期外协申请条件][hub网点网点经理、区域经理可以申请]['TH02020402','TH02030208'网点正主管][shop project部门所有员工]
            //3-[车队外协申请条件][Line haul & Transportation部门(26)，角色为线路中控人员(31)/线路规划管理员的员工申请(32)]

            $store = (new SysStoreServer())->getStoreByid($organizationId);
            if ($departmentId == enums::$department['Transportation'] && array_intersect($roles, array_keys($motorcadeRequestRole))) { //部门26
                return true;
            }

            if ($type == 2) { //除了车队外协可以是总部员工申请，其余都是网点员工才能申请
                return false;
            }

            $stores = env('os_staff_long_term_stores', "'TH02020402','TH02030208'");
            $storeIds = explode(',', $stores);
            if (!isset($jobTitlesData[$job_title]) ||
                isset($store) && (
                    !empty(array_intersect(array_keys($hubRequestRole), $roles)) && $store['category'] == 8 ||
                    in_array($organizationId, $storeIds) && $job_title == 16 ||
                    $departmentId == 13
                )
            ) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取修改里程权限[在编job_name` = 'Van courier';]
     * @param array paramIn
     * @return boolean
     */
    public function getMilePermission($paramIn = [])
    {
        if (empty($this->staffInfo)) {
            return false;
        }
        $setting_model = new BySettingRepository();
        $miles_job = $setting_model->get_setting('miles_job_title');
        if (empty($miles_job)) {
            $miles_job = '110';
        }
        $job_titles = explode(',', $miles_job);

        if (in_array($this->staffInfo['job_title'],
                $job_titles) && $this->staffInfo['formal'] == HrStaffInfoModel::FORMAL_1) {
            return true;
        }
        return false;
    }

    /**
     * 获取到岗确认权限 default [角色 = 18 网点主管]
     * @param array paramIn
     * @return boolean
     */
    public function getEntryConfirmPermission($paramIn = [])
    {
        $entry_confirm_roles = (new SettingEnvServer())->getSetVal('entry_confirm_roles') ? : enums::$roles['DOT_ADMINISTRATOR'];
        $entry_confirm_job_title = (new SettingEnvServer())->getSetVal('entry_confirm_job_title') ? : '';
        $entry_confirm_job_title = !empty($entry_confirm_job_title) ? explode(',', $entry_confirm_job_title) : [];
        $jobTitle = $this->processingDefault($paramIn, 'job_title', 2);
        $position = $this->processingDefault($paramIn, 'positions', 3, []);
        if (array_intersect(explode(',',$entry_confirm_roles), $position) || in_array($jobTitle, $entry_confirm_job_title)) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 1. Flash express 的非flash logistic 28、flash money 16、fulfillment 15 部门的在职编制员工
     * 2. 合作商员工中，合作商类型为Flash Home 18 的员工
     * @param array $param
     */
    public function salary_pdf_permission($param)
    {
        if (!isCountry('TH')) {
            return false;
        }

        if (empty($this->staffInfo)) {
            return false;
        }

        if ($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return false;
        }

        if ($this->staffInfo['formal'] == 1) {
            $setting_model = new BySettingRepository();
            $salary_pdf_sys_department_ids = $setting_model->get_setting('salary_pdf_sys_department_ids');
            if($salary_pdf_sys_department_ids){
                $salary_pdf_sys_department_ids=explode(',',$salary_pdf_sys_department_ids);
            }else{
                $salary_pdf_sys_department_ids=array(15, 16, 28);
            }
            if (in_array($this->staffInfo['sys_department_id'],$salary_pdf_sys_department_ids ))
                return false;
        } else if ($this->staffInfo['formal'] == 2) {
            //那就是 合作商的 只有 flash home 的展示工资条 合作商其他部门的 都不展示 是吧
            //员工属性（下拉列表：1 编制、0 非编制、2:加盟商（合作商）3: 其他（合作商））4:实习生  产品确定后 只有2  2就是 flash home 类型
//            if($staff_info['sys_department_id'] != 18)
//                return false;
        } else {
            return false;
        }

        return true;
    }

    /**
     * Flash express 的非flash logistic、flash money、fulfillment、Flash Home 18 部门的在职编制员工
     * @param array $param
     */
    public function on_job_pdf_permission($param)
    {
        if (!isCountry()) {
            return false;
        }

        if (empty($this->staffInfo)) {
            return false;
        }

        if ($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return false;
        }

        // 18160 新规则 ： "工作所在国家"为泰国的正式员工
        //非编制 不展示
        if ($this->staffInfo['formal'] != 1) {
            return false;
        }
        //工作所在国家非泰国 不展示
        if ($this->staffInfo['working_country'] != HrStaffInfoModel::WORKING_COUNTRY_TH) {
            return false;
        }

        return true;
    }

    /**
     * 获取离职后资产确认权限
     * @param $param
     * @return bool
     */
    public function getResignAssetPermision($param)
    {
        if (empty($this->staffInfo)) {
            return false;
        }
        //马来 lnt 员工隐藏入口  22411 申请资产(新)入口 -显示
        /*if(isCountry('MY')){
            $isLnt = StaffServer::isLntCompanyByInfo($this->staffInfo);
            if($isLnt){
                return false;
            }
        }*/
        /**
         * @description: https://l8bx01gcjr.feishu.cn/docs/doccn0mfIdeEhBkihek3KSD1YFg#  所有人BY均有离职资产确认菜单  芦佳
         * <AUTHOR> L.J
         * @time       : 2021/8/11 11:43
         */
        //雇佣类型=13个人代理，入口关闭
        return ($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) ? false : true;
    }



    /**
     * 检查提交用人申请人角色
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkHcDataRole($paramIn = [])
    {
        if (empty($this->staffInfo)) {
            return false;
        }
        $hc_appaly_id_staffids_arr = SettingEnvServer::getSetValToArray('hc_appaly_id',',');
        if ($this->staffInfo['job_title_grade_v2'] >= 15 || in_array($this->staffInfo['staff_info_id'],$hc_appaly_id_staffids_arr)) { //用人部门职级在15及以上 或者有配置
            return true;
        }

        return false;
    }

    /**
     * 获取运费折扣权限--network部门
     * @param array $paramIn
     * @return bool
     */
    public function getFreightDiscNetworkPermission($paramIn = []): bool
    {
        $roles     = $this->processingDefault($paramIn, 'positions', 3);
        $organizationId = $this->processingDefault($paramIn, 'organization_id', 2);
        $store     = (new SysStoreServer())->getStoreByid($organizationId);

        //角色：网点主管，网点类型：SP、BDC、DC
        if (array_intersect($roles, [18]) && in_array($store['category'], [1,2,10])) {
            return true;
        }

        return false;
    }

    /**
     * 获取运费折扣权限--Shop部门
     * @param array $paramIn
     * @return bool
     */
    public function getFreightDiscShopPermission($paramIn = []): bool
    {
        if (!isCountry()) {
            return false;
        }
        $roles     = $this->processingDefault($paramIn, 'positions', 3);
        $organizationId = $this->processingDefault($paramIn, 'organization_id', 2);
        $store     = (new SysStoreServer())->getStoreByid($organizationId);

        //shop类型网点主管 角色=网点主管
        if (array_intersect($roles, [18]) && in_array($store['category'], [4,5,7])) {
            return true;
        }
        return false;
    }


    /**
     *
     * 判断部门是否属于该公司
     *
     * @param $departmentId
     * @param $companyId
     *
     */
    public function isBelongCompany($departmentId, $companyId)
    {
        return SysDepartmentModel::findFirst([
            'conditions' => " id = :department_id: and company_id = :company_id: ",
            'bind' => [
                'department_id' => $departmentId,
                'company_id' =>  $companyId,
            ],
        ]);
    }

    /**
     * 获取运费折扣权限
     * @param array $paramIn
     * @return bool
     */
    public function getFreightDiscOtherPermission($paramIn = []): bool
    {
        if (!isCountry()) {
            return false;
        }
        $staffId  = $this->processingDefault($paramIn, 'staff_id', 2);
        $position = $this->processingDefault($paramIn, 'positions', 3, []);

        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => [
                'staff_id' => $staffId,
            ],
        ]);
        if (empty($staffInfo)) {
            return false;
        }
        $staffInfo = $staffInfo->toArray();

        //1. 申请人所在部门是sales部门 & 角色是业务员、销售经理或sales Manager职位
        //2. 申请人所在部门是Project Management部门 & 角色是ka销售
        if ($staffInfo['sys_department_id'] == enums::$department['Sales'] && (array_intersect($position, [12, 13]) || $staffInfo['job_title'] == enums::$job_title['sales_manager']) ||
            $staffInfo['sys_department_id'] == enums::$department['Project Management'] && array_intersect($position, [69])
        ) {
            return true;
        }

        return false;
    }

    /**
     * 转岗-申请权限
     * @param array $paramIn
     * @return bool
     */
    public function getJobtransferPermission(array $paramIn = []): bool
    {
        $staffId = $paramIn["staff_id"];
        $sysStoreInfo = SysStoreModel::find([
            'conditions' => 'manager_id is not null',
            'bind'       => ['state ' => 1],
            'columns' => "manager_id",
        ])->toArray();
        $sysStoreInfo = array_column($sysStoreInfo,'manager_id');
        if (in_array($staffId,$sysStoreInfo)){
            return true;
        };

        //获取全部网点经理的职位id
        $staffInfo = $this->hc->getHcStaffInfo($staffId);
        if (!$staffInfo) {
            return false;
        }
        return true;
    }

    /**
     * 获取转岗权限
     * @param array $paramIn
     * @return bool
     */
    public function getJobTransferPermissionV2(array $paramIn = []): bool
    {
        $staffId = $paramIn["staff_id"];

        //指定职位
        if ($this->isSpecJobTitles($paramIn['job_title'])) {
            return true;
        }

        //部门负责人
        $staffServer = new StaffServer($this->lang, $this->timezone);
        $manageDepartmentIds = $staffServer->getManageDepartmentList($staffId);
        if (!empty($manageDepartmentIds)) {
            return true;
        }

        //大区负责人
        $manageRegionIds = $staffServer->getManageRegionsList($staffId);
        if (!empty($manageRegionIds)) {
            return true;
        }

        //片区负责人
        $managePieceIds = $staffServer->getManagePiecesList($staffId);
        if (!empty($managePieceIds)) {
            return true;
        }

        //网点负责人
        $manageStoresIds = $staffServer->getManageStoresList($staffId);
        if (!empty($manageStoresIds)) {
            return true;
        }
        $underManageStaff = (new HrStaffItemsModel())->getOneByValue($staffId, 'MANGER');
        if (!empty($underManageStaff)) {
            return true;
        }

        return false;
    }

    /**
     * 获取员工最近一次请假信息
     * @param $staff_id int
     * @return $data array
     */
    public function getLatestLeaveData($staff_id)
    {
        if (empty($staff_id)) {
            return [];
        }

        return $this->re['audit']->getLatestLeaveDataByStaffId($staff_id);
    }

    /**
     * 获取抄送权限
     * @param array $paramIn
     * @return bool
     */
    public function getCCPermission(array $paramIn)
    {
        if (!isCountry()) {
            return false;
        }
        $staffId = $paramIn["staff_id"];
        $staffInfo = (new StaffRepository())->getStaffInfo($staffId);
        if (!$staffInfo) {
            return false;
        }

        return $staffInfo['department_id'] == 49;
    }


    /**
     * 获取申请权限
     * @description 申请权限： 全部员工
     * 需求链接： https://l8bx01gcjr.feishu.cn/docs/doccnHz9MpzeBic02OY8ehxNoFb
     * 需求作者： 刘佳雪
     * @param mixed $paramIn
     */
    public function getOTPermission($paramIn): bool
    {
        $staffId = $paramIn["staff_id"];
        if (empty($staffId)) {
            return false;
        }
        //规则没有子账号 程序控制
        if($this->staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF){
            return false;
        }

        //调rpc 配置
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('OT_apply_rules')
            ->setParameters(['staff_info_id' => $staffId])
            ->getConfig();

        $setting = [];
        if(!empty($res['response_type']) && $res['response_type'] == ConditionsRulesEnums::RESPONSE_TYPE_VALUE){
            $setting = $res['response_data'];
        }
        return (bool)$setting;
    }

    /**
     * 获取销售CRM权限
     * @param mixed $paramIn
     * @doc https://l8bx01gcjr.feishu.cn/docs/doccnPxTa5v78XVc6Q2Hq6C1xOe
     */
    public function getSalesCRMPermission($paramIn): bool
    {
        $staffId = $paramIn["staff_id"];
        $department_id = $paramIn["department_id"];

        //CRM 白名单 crm_white_list = 57776
        $senv = (new SettingEnvServer())->getSetVal('crm_white_list');
        if(!empty($senv) && in_array($staffId,explode(',',$senv))){
            return true;
        }

        if (empty($staffId) || empty($department_id)) {
            return false;
        }

        // 有权限的部门IDS
        $permission_department_ids = [];

        // 获取所有部门
        $all_department_ids = SysDepartmentModel::find([
            'conditions' => 'type = :type:',
            'bind' => ['type' => 2],
            'columns' => ['id', 'ancestry_v2'],
        ])->toArray();

        // 提取 Project Management[22], Sales[40] 部门 及其子部门IDS
        //新增shop部门（id：13），和network部门（id：4)
        foreach ($all_department_ids as $item) {
            $ancestry_v2 = explode('/', $item['ancestry_v2']);
            if (in_array(Enums::SALES_CRM_ACCESS_PROJECT_MANAGEMENT_DEPARTMENT_ID, $ancestry_v2)) {
                $permission_department_ids[] = $item['id'];
                continue;
            }

            if (in_array(Enums::SALES_CRM_ACCESS_SALES_DEPARTMENT_ID, $ancestry_v2)) {
                $permission_department_ids[] = $item['id'];
                continue;
            }

            if (in_array(Enums::SALES_CRM_ACCESS_SHOP_DEPARTMENT_ID, $ancestry_v2)) {
                $permission_department_ids[] = $item['id'];
                continue;
            }
            if (in_array(Enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID, $ancestry_v2)) {
                $permission_department_ids[] = $item['id'];
                continue;
            }
            if (in_array(Enums::SALES_CRM_ACCESS_FH_DEPARTMENT_ID, $ancestry_v2)) {
                $permission_department_ids[] = $item['id'];//18FH增加crm入口权限
                continue;
            }
        }


        return in_array($department_id, $permission_department_ids) ? true : false;
    }



    //获取对应假期的 模板 各个国家 模板不同 现在有 菲律宾 老挝 详情 看枚举
    public function get_template($type){
        $return = array();
        if($type == enums::LEAVE_TEMPLATE_TYPE_1){
            $type_arr = enums::LEAVE_TEMPLATE_1;
            $return = array();
            if(!empty($type_arr)){
                foreach ($type_arr as $k => $v){
                    $row['code'] =  $k;
                    $row['value'] = $this->getTranslation()->_($v);
                    $return[] = $row;
                }

            }
        }
        if($type == enums::LEAVE_TEMPLATE_TYPE_2){
            $type_arr = enums::LEAVE_TEMPLATE_2;
            $return = array();
            if(!empty($type_arr)){
                foreach ($type_arr as $k => $v){
                    $row['code'] =  $k;
                    $row['value'] = $this->getTranslation()->_($v);
                    $return[] = $row;
                }

            }
        }
        return $return;
    }

    /**
     * 调整角色申请权限
     * @param array $paramIn
     * @return bool
     */
    public function getAdjustRolePermission(array $paramIn = []): bool
    {
        $staffInfoId = $paramIn['staff_id'];

        //1-network、shop网点负责人
        //网点员工所属网点的网点类型：SP[1]、DC[2]、BDC[10] CDC [13]
        //网点类型SHOP(pickuponly)[4]、SHOP(pickup&delivery)[5]、USHOP[7]）
        $manageStore = SysStoreModel::find([
            'conditions' => 'manager_id = :staff_id: and state = 1 and category IN (1,2,4,5,7,10,13)',
            'bind'       => ['staff_id' => $staffInfoId],
        ])->toArray();

        //2-片区负责人
        $managePiece = SysManagePieceModel::find([
            'conditions' => 'manager_id = :staff_id: and deleted = 0',
            'bind'       => ['staff_id' => $staffInfoId],
        ])->toArray();

        //3-大区负责人
        $manageRegion = SysManageRegionModel::find([
            'conditions' => 'manager_id = :staff_id: and deleted = 0',
            'bind'       => ['staff_id' => $staffInfoId],
        ])->toArray();

        return !empty($manageStore) || !empty($managePiece) || !empty($manageRegion);
    }

    /**
     * 当前实时 有效的假期类型
     * code 类型枚举
     * type 1 带薪 2 不带薪 3 不拼接
     * color 1 黄色 2 灰色
     * @return array
     */
    public function type_book($locale = '')
    {
        $data = [
            [
                'code'  => '15',
                'type'  => 1,
                'color' => 1,
                'msg'   => $this->getTranslation($locale)->_('2017'),
            ],
            [
                'code'  => '16',
                'type'  => 1,
                'color' => 1,
                'msg'   => $this->getTranslation($locale)->_('2018'),
            ],
            [
                'code'  => '1',
                'type'  => 1,
                'color' => 1,
                'msg'   => $this->getTranslation($locale)->_('2003'),
            ],
            [
                'code'     => '2',
                'type'     => 1,
                'color'    => 1,
                'msg'      => $this->getTranslation($locale)->_('2004'),
                'need_img' => 1,
            ],
            [
                'code'  => '12',
                'type'  => 2,//展示出来 不带薪
                'color' => 2,
                'msg'   => $this->getTranslation($locale)->_('2014'),
            ],
//                    [
//                        'code' => '13',
//                        'type' => 1,
//                        'msg'  => $this->getTranslation($locale)->_('2015')
//                    ],
            [
                'code'  => '38',//新病假类型 把 带薪病假(3) 和不带薪病假(18) 合并为这一个
                'type'  => 0,
                'color' => 1,
                'msg'   => $this->getTranslation($locale)->_('2005'),
            ],
            [
                'code'     => '4',
                'type'     => 1,
                'color'    => 1,
                'msg'      => $this->getTranslation($locale)->_('2006'),
                'need_img' => 1,
            ],
            [
                'code'  => '5',
                'type'  => 1,
                'color' => 1,
                'msg'   => $this->getTranslation($locale)->_('2007'), //'陪产假',
            ],
            [
                'code'     => '17',
                'type'     => 1,
                'color'    => 1,
                'msg'      => $this->getTranslation($locale)->_('2020'), //'产检',
                'need_img' => 1,
            ],
            [
                'code'  => '6',
                'type'  => 1,
                'color' => 1,
                'msg'   => $this->getTranslation($locale)->_('2008'),
            ],
            [
                'code'     => '7',
                'type'     => 1,
                'color'    => 1,
                'msg'      => $this->getTranslation($locale)->_('2009'),
                'need_img' => 1,
            ],
            [
                'code'     => '8',
                'type'     => 1,
                'color'    => 1,
                'msg'      => $this->getTranslation($locale)->_('2010'),
                'need_img' => 1,
            ],
            [
                'code'  => '9',
                'type'  => 2,
                'color' => 2,
                'msg'   => $this->getTranslation($locale)->_('2011'),
            ],
            [
                'code'     => '10',
                'type'     => 1,
                'color'    => 1,
                'msg'      => $this->getTranslation($locale)->_('2012'),
                'need_img' => 1,
            ],
            [
                'code'  => '11',
                'type'  => 1,
                'color' => 1,
                'msg'   => $this->getTranslation($locale)->_('2013'),
            ],
//            [
//                'code' => '14',
//                'msg'  => $this->getTranslation($locale)->_('2016')
//            ],
//            [
//                'code' => '18',
//                'type' => 2,
//                'msg'  => $this->getTranslation($locale)->_('2021')
//            ],
            [
                'code'  => '19',
                'type'  => 1,
                'color' => 1,
                'msg'   => $this->getTranslation($locale)->_('2022'),
            ],

            [
                'code'     => '25',
                'type'     => 1,
                'color'    => 1,
                'msg'      => $this->getTranslation($locale)->_('leave_25'),
                'need_img' => 1,
            ],
            [
                'code'     => '26',
                'type'     => 1,
                'color'    => 1,
                'msg'      => $this->getTranslation($locale)->_('leave_26'),
                'need_img' => 1,
            ],

        ];

        $data[] = [
            'code' => '40',
            'type' => 0,
            'msg'  => $this->getTranslation($locale)->_('leave_40'),
        ];
        return $data;
    }

    /**
     * code 类型枚举
     * type 1 带薪 2 不带薪 3 不拼接
     * color 1 黄色 2 灰色
     * @return array
     */
    public function os_type_book()
    {
        $data = [
            [
                'code' => '12',
                'type' => 2,//展示出来 不带薪
                'color' => 2,
                'msg'  => $this->getTranslation()->_('2014'),
            ],
        ];
        return $data;
    }


    /**
     * 无底薪员工假期类型 个人代理 兼职个人代理
     * code 类型枚举
     * type 1 带薪 2 不带薪 3 不拼接
     * color 1 黄色 2 灰色
     * @param $code 类型 泰国和马来不一样
     * @return array
     */
    public function hire_un_paid_book($code = '39')
    {
        $type = 2;
        if($code == enums::LEAVE_TYPE_40 || $code == enums::LEAVE_TYPE_41){
            $type = 0;
        }
        $data = [
            [
                'code' => (string)$code,
                'type' => $type,//展示出来 不带薪
                'color' => 2,
                'msg'  => $this->getTranslation()->_('leave_' . $code),
            ],
        ];
        return $data;
    }


    /**实习生员工 定制类型 公司培训 和 无薪假
     * code 类型枚举
     * type 1 带薪 2 不带薪 3 不拼接
     * color 1 黄色 2 灰色
     * @return array
     */
    public function practise_type_book(){
        $data = [
            [
                'code' => '16',
                'type' => 1,
                'color' => 1,
                'msg'  => $this->getTranslation()->_('2018'),
            ],
            [
                'code' => '12',
                'type' => 2,//展示出来 不带薪
                'color' => 2,
                'msg'  => $this->getTranslation()->_('2014'),
            ],
        ];
        return $data;
    }


    //补卡申请 是否显示 图片必填 给前端
    public function type_book_show_msg($paramIn)
    {
        $show_img = false;
        $_organization_type = $paramIn['user_info']['organization_type'];
        $_organization_id = $paramIn['user_info']['organization_id'];
        $store_server = new SysStoreServer($this->lang);
        $store_where = '8,9,12';
        $hub_store    = $store_server->getHubStore($store_where);
        if (($_organization_type == 1 && $_organization_id) && in_array($_organization_id, $hub_store))
            $show_img = true;
        return $show_img;
    }


    /**
     * @description: 判断是不是外协,如果是外协或者没有工号,去除相应权限
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/8/10 14:30
     */
    public function outsourcingPermissionFilter($paramIn){
        $permission = $paramIn['permission_list'] ??[];
        //获取用户信息

        //外协不需要的权限
        $outsourcing_not_permission = [
            'TF'          => 2, //转岗
            'AT'          => 2, //补卡
            'LE'          => 2, //请假
            'OT'          => 2, //加班
            'Trip'        => 2, //出差
            'go_trip'     => 2, //外出申请
            'resign'      => 2, //离职
            'OS'          => 2, //外协员工申请
            'AR'          => 2, //角色调整
            'yc_Trip'     => 2, //黄牌出差
            'HC'          => 2, //用人申请
            'Report'      => 2, //举报申请
            'fuel_budget' => 2, //报销申请
            'Wms'         => 2, //申请设备和物料
            'Asset'       => 2, //物料资产领用
            'SASS'        => 2, //申请支援网点
            'SAS'         => 2, //网点申请支援权限
        ];
        if(empty($this->staffInfo) || $this->staffInfo['formal'] == HrStaffInfoModel::FORMAL_0 ) {
            $permission = array_merge($permission,$outsourcing_not_permission);//后面的会替换前面的值
        }
        return $permission;
    }

    /**
     * 新资产申请权限
     * @param $paramIn
     * @return bool
     */
    protected function getNewAssetPermission($paramIn): bool
    {
        if (empty($this->staffInfo)) {
            return false;
        }
        //菲律宾&&个人代理，关闭
        if (isCountry('PH') && $this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return false;
        }
        $key_code = enums::MATERIAL_ASSET_OPEN_INTERN;
        $intern_open_val =  (new StaffRepository())->getOaSettingEnvAuthority($key_code);
        $formal = [HrStaffInfoModel::FORMAL_1];//在编
        //资产开启了实习生
        if ($intern_open_val == 1) {
            $formal = [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN];
        }
        //马来 lnt 员工隐藏入口 22411 申请资产(新)入口 -显示
        /*if(isCountry('MY')){
            $isLnt = StaffServer::isLntCompanyByInfo($this->staffInfo);
            if($isLnt){
               return false;
            }
        }*/
        return in_array($this->staffInfo['formal'],
                $formal) && $this->staffInfo['state'] == HrStaffInfoModel::STATE_1 && $this->staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF_0;

    }


    //审批流相关写入
    public function saveApproval($auditId,$paramIn){

        $staffRe = new StaffRepository($this->lang);
        $staffInfo = $staffRe->getStaffPosition($paramIn['staff_id']);

        $extend = $this->getWorkflowExtend($paramIn,$staffInfo);
        $extend['time_out'] = $paramIn['time_out'] ?? null;
        $workflow_id = $this->getWorkflowId($staffInfo);

        $res = (new ApprovalServer($this->lang, $this->timezone))->create($auditId, enums::$audit_type['LE'], $paramIn['staff_id'],null,$extend,'',$workflow_id);
        if (!$res) {
            throw new \Exception('audit staff insert leaveAdd workflow fail' . $res);
        }
    }

    public function getPayCycle($now = null)
    {
        return [];
    }

    public function getWorkflowExtend($auditData,$staffData)
    {
        return [];
    }
    public function getWorkflowId($data)
    {
        if(isCountry('TH')){
            return 49;
        }
        return '';
    }

    //审批超时 拒绝 发消息
    public function sendMessage($detail , $state, $isUnpaid = false){
        if(!in_array($state,[enums::APPROVAL_STATUS_REJECTED,enums::APPROVAL_STATUS_TIMEOUT])){
            return true;
        }
        $title = $content = $suffix = '';
        //个人代理翻译后缀
        if($isUnpaid){
            $suffix = '_unpaid';
        }
        if($state == enums::APPROVAL_STATUS_REJECTED){
            $title = 'leave_reject_title' . $suffix;
            $content = 'leave_reject_content' . $suffix;
        }
        if($state == enums::APPROVAL_STATUS_TIMEOUT){
            $title = 'leave_timeout_title' . $suffix;
            $content = 'leave_timeout_content' . $suffix;
        }

        $locale = getCountryDefaultLang();
        $hc_re        = new HcRepository($this->timeZone);
        $staffAccount = $hc_re->getStaffAcceptLang([$detail['staff_info_id']]);
        foreach ($staffAccount as $account) {
            $locale = strtolower(substr($account['accept_language'], 0, 2)) ?: $locale;
        }
        //整理变量
        $dayType = $this->startEndType($locale);
        $dayType = array_column($dayType, 'msg', 'code');
        $bind['start_time'] = date('Y-m-d', strtotime($detail['leave_start_time'])) . "[{$dayType[$detail['leave_start_type']]}]";
        $bind['end_time'] = date('Y-m-d', strtotime($detail['leave_end_time'])) . "[{$dayType[$detail['leave_end_type']]}]";
        $server = Tools::reBuildCountryInstance($this,[$locale,$this->timezone]);
        $leaveType = $server->type_book($locale);
        $leaveType = array_column($leaveType,'msg', 'code');
        $bind['leave_type'] = $leaveType[$detail['leave_type']];

        $title          = $this->getTranslation($locale)->_($title);
        $content        = $this->getTranslation($locale)->_($content, $bind);

        if (RUNTIME == 'dev') {//测试用 看push 发的对不对
            $title = $detail['staff_info_id'] . $title;
        }
        $send_message = [
            'staff_users'        => [$detail['staff_info_id']],
            'message_title'      => $title,
            'message_content'    => "<p style='font-size: 32px;'>" . $content . "</p>",
            'staff_info_ids_str' => $detail['staff_info_id'],
            'category'           => -1,
            'push_state'         => 1,
            'id'                 => time() . $detail['staff_info_id'] . rand(1000000, 9999999),
        ];
        $hcm_rpc      = new ApiClient('hcm_rpc', '', 'add_kit_message', $locale);
        $hcm_rpc->setParams($send_message);
        $res = $hcm_rpc->execute();
        if (!isset($res['result']['code']) || $res['result']['code'] != ErrCode::SUCCESS) {
            $this->getDI()->get('logger')->write_log([
                'function' => 'leave sendMessage',
                'message'  => '消息发送失败',
                'params'   => $send_message,
                'result'   => $res,
            ]);
            return true;
        }

        //发push
        $scheme = "flashbackyard://fe/page?path=message&messageid=" . $send_message['id'];
        PushServer::getInstance($locale, $this->timezone)->sendPush($detail['staff_info_id'],$title, $content, $scheme,'backyard');
        PushServer::getInstance($locale, $this->timezone)->sendPush($detail['staff_info_id'],$title, $content, $scheme,'kit');
    }

    /**
     * Quick Offer
     * @return bool
     */
    protected function quickOfferPermission(): bool
    {
        $quick_offer_position = (new SettingEnvServer())->getSetVal('quick_offer_position', ',');
        return in_array($this->staffInfo['job_title'], $quick_offer_position);
    }


    /**
     * OA文件夹权限
     * @return bool
     */
    protected function OAFolderPermission(): bool
    {
        if ($this->staffInfo['formal'] != HrStaffInfoModel::FORMAL_1) {
            return false;
        }

        if ($this->OAFolderPermissionForJobTitleConfig($this->staffInfo['job_title'])) {
            return true;
        }

        return false;
    }

    /**
     * oa职位设置获取
     * @param $jobTitle
     * @return bool
     */
    public function OAFolderPermissionForJobTitleConfig($jobTitle): bool
    {
        $config = (new SettingEnvServer())->getSetValToArray('show_oa_file_folder_job_title');

        if (!$jobTitle || in_array($jobTitle, $config)) {
            return false;
        }

        return true;
    }


    /**
     * 不接单/不服务通知 发送消息
     * @param $data
     * @return bool
     */
    public function sendMessageToManagerHandle($data)
    {
        //获取收信人语言环境
        $lang = (new StaffServer)->getLanguage($data['receiver_id']);
        //获取语言
        $t       = $this->getTranslation($lang);

        //th请假类型 40  ph 请假类型 41
        $leave_type_text = $t->_('leave_' . $data['leave_type']);

        $start_half_day_key = $data['leave_start_type'] == 1 ? 'half_am' : 'half_pm';
        $end_half_day_key   = $data['leave_end_type'] == 1 ? 'half_am' : 'half_pm';

        $start_half_day = $t->_($start_half_day_key);
        $end_half_day   = $t->_($end_half_day_key);


        $title = $t->_('not_working_title', ['leave_type_text' => $leave_type_text]);
        //%store_name%的%name%（%staff_info_id%）于%created_at%提交了 %leave_type_text%。告知您其在%leave_start_time% %start_half_day% - %leave_end_time% %end_half_day% 不参与相关任务，请合理安排其它人员完成服务。
        $content = $t->_('not_working_content', [
            'leave_type_text'  => $leave_type_text,
            'staff_info_id'    => $data['staff_info_id'],
            'name'             => $data['name'],
            'store_name'       => $data['store_name'],
            'created_at'       => $data['created_at'],
            'leave_start_time' => $data['leave_start_time'],
            'start_half_day'   => $start_half_day,
            'leave_end_time'   => $data['leave_end_time'],
            'end_half_day'     => $end_half_day,
        ]);

        $kit_param['staff_info_ids_str'] = $data['receiver_id'];
        $kit_param['staff_users']        = [['id' => $data['receiver_id']]];
        $kit_param['message_title']      = $title;
        $kit_param['message_content']    = $content;
        $kit_param['category']           = MessageEnums::MESSAGE_CATEGORY_GET_CONFIRM;

        $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($kit_param);
        $res = $bi_rpc->execute();
        if (!isset($res['result']['code']) || $res['result']['code'] != ErrCode::SUCCESS) {
            $this->getDI()->get('logger')->write_log([
                'function' => 'sendMessageToManager-请假',
                'message'  => '消息发送失败',
                'params'   => $data,
                'result'   => $res,
            ]);
            return false;
        }
    }

    /**
     * 不接单/不服务通知 发送消息 给上级 上上级发送消息
     * @param $auditDetail
     * @return bool
     */
    public function sendMessageToManager($auditDetail)
    {
        if(empty($auditDetail)) {
            return false;
        }

        $staffRepository = new StaffRepository($this->lang);
        $staffInfo = $staffRepository->getStaffInfoOne($auditDetail['staff_info_id']);
        if(empty($staffInfo)) {
            return false;
        }

        $storeInfo = SysStoreRepository::getSysStoreInfo($staffInfo['sys_store_id']);

        $storeName = !empty($storeInfo) ? $storeInfo['name'] : '';

        $messageData['staff_info_id']    = $staffInfo['staff_info_id'];
        $messageData['name']             = $staffInfo['name'];
        $messageData['store_name']       = $storeName;
        $messageData['leave_start_time'] = !empty($auditDetail['leave_start_time']) ? date('Y-m-d', strtotime($auditDetail['leave_start_time'])) : '';
        $messageData['leave_start_type'] = $auditDetail['leave_start_type'];
        $messageData['leave_end_time']   = !empty($auditDetail['leave_end_time']) ? date('Y-m-d', strtotime($auditDetail['leave_end_time'])) : '';
        $messageData['leave_end_type']   = $auditDetail['leave_end_type'];
        $messageData['leave_type']     = $auditDetail['leave_type'];//请假类型

        $add_hour = $this->config->application->add_hour;
        $messageData['created_at']     = date('Y-m-d H:i:s',strtotime($auditDetail['created_at']) + $add_hour * 3600);


        if(empty($staffInfo['manger'])) {
            return false;
        }

        $messageData['receiver_id']     = $staffInfo['manger'];//接收人上级
        $this->sendMessageToManagerHandle($messageData);

        $managerStaffInfo = $staffRepository->getStaffInfoOne($staffInfo['manger']);
        if(empty($managerStaffInfo['manger'])) {
            return false;
        }
        $messageData['receiver_id']     = $managerStaffInfo['manger'];//接收人上上级
        $this->sendMessageToManagerHandle($messageData);
        return true;
    }

    /**
     * 补卡获取考勤信息接口
     * @param $staff_info
     * @param $param
     * @return array
     */
    public function attendanceShiftInfo($staff_info, $param): array
    {
        $attendance_date = $param['attendance_date'] ?? '';//补卡日期
        $attendance_type = $param['attendance_type'] ?? 0;//补卡类型
        $result          = [
            'hire_type'            => strval($staff_info['hire_type']),
            'default_reissue_info' => ['day_type' => '0', 'shift_time' => ''],
            'shift_info'           => '',
            'attendance_info'      => [],
            'attendance_type_list' => [],
        ];
        $t               = $this->getTranslation();
        $audit_server    = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        //是否需要传图片
        $result['need_img'] = $audit_server->type_book_show_msg(['user_info' => $staff_info]);
        //什么都没传
        if (empty($attendance_date) && empty($attendance_type)) {
            return $result;
        }
        //有类型没日期 不存在这种情况 必须选日期之后才有类型
        if (!empty($attendance_type) && empty($attendance_date)) {
            return $result;
        }
        $today     = date('Y-m-d');
        //获取日期对应员工固化信息
        if ($attendance_date < $today) {
            $transfer = HrStaffTransferModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and stat_date = :date_at:',
                'bind'       => [
                    'staff_id' => $staff_info['staff_id'],
                    'date_at'  => $attendance_date,
                ],
            ]);
            if ($transfer) {
                if(!empty($transfer->hire_type)){
                    $staff_info['hire_type'] = $result['hire_type'] = $transfer->hire_type;
                }
                $staff_info['job_title'] = $transfer->job_title;
                //organization_type  organization_id
                $staff_info['organization_type'] = $transfer->store_id == enums::HEAD_OFFICE_ID ? HrStaffInfoModel::ORGANIZATION_DEPARTMENT : HrStaffInfoModel::ORGANIZATION_STORE;
                $staff_info['organization_id']   = $transfer->store_id;
                $result['need_img']              = $audit_server->type_book_show_msg(['user_info' => $staff_info]);
            }
        }
        //如果有日期 查询 请假或者off  存在记录 不自动补全 显示横线
        $leaveInfo = (new LeaveServer($this->lang, $this->timezone))->getStaffLeaveInfoFromCache($staff_info['staff_id'], $attendance_date);
        $isOffRest = (new StaffOffDayRepository($this->lang, $this->timezone))->checkStaffIsOffRest($staff_info['staff_id'], $attendance_date);

        //打卡信息
        $attendanceServer = new AttendanceServer($this->lang, $this->timezone);
        $attendanceInfo   = $attendanceServer->get_att_by_date($staff_info['staff_id'], $attendance_date);
        $started_at       = empty($attendanceInfo['started_at']) ? '' : date('H:i', strtotime($attendanceInfo['started_at']));
        $end_at           = empty($attendanceInfo['end_at']) ? '' : date('H:i', strtotime($attendanceInfo['end_at']));
        $isDefault        = true;//是否需要默认选中值 (上班补卡 下班补卡 当日 次日)
        $suffix           = '';//个人代理翻译
        if (in_array($staff_info['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            $suffix = '_unpaid';
            if (isCountry('TH')) {
                $suffix = '_unpaid_th';
                $isDefault = false;
            }
        }
        $attendance_start_time_key = 'attendance_start_time' . $suffix;
        $attendance_end_time_key   = 'attendance_end_time' . $suffix;
        $attendance_miss           = 'attendance_miss' . $suffix;

        //显示空
        if($attendance_date == $today){
            $attendance_miss = '';
        }
        if ($isOffRest || $leaveInfo == 3) {
            //不用默认选中 直接返回
            $attendance_miss = 'leave_off_attendance_hidden';
            $isDefault       = false;
        }

        $result['attendance_info'][] = [
            'label' => $t->_($attendance_start_time_key),
            'value' => $started_at ?: $t->_($attendance_miss),
        ];
        $result['attendance_info'][] = [
            'label' => $t->_($attendance_end_time_key),
            'value' => $end_at ?: $t->_($attendance_miss),
        ];

        //有日期 有类型 取班次
        $shiftServer = new HrShiftServer();
        $shiftInfo   = $shiftServer->getShiftInfos($staff_info['staff_id'], [$attendance_date]);
        $shiftInfo   = $shiftInfo[$attendance_date] ?? [];
        if (empty($shiftInfo)) {
            $isDefault = false;
        }
        $result['shift_info'] = empty($shiftInfo) ? '' : $shiftInfo['start'] . '-' . $shiftInfo['end'];

        $reissueParam['reissue_date'] = $attendance_date;
        $reissueParam['user_info'] = $staff_info;
        $result['attendance_type_list'] = $this->getReissueType($reissueParam);
        //有日期 没有类型
        if (!empty($attendance_date) && empty($attendance_type)) {
            //如果是当天 也不给默认类型和时间
            if($attendance_date == $today){
                return $result;
            }
            //非当天日期 双卡都缺 或者都不缺 没默认值
            if ((empty($started_at) && empty($end_at)) || (!empty($started_at) && !empty($end_at))) {
                return $result;
            }
            //过去日期 如果只有单卡缺卡 有默认值
            $default_attendance_type = 0;
            if (empty($started_at)) {
                $default_attendance_type = 1;
            }
            if (empty($default_attendance_type)) {
                $default_attendance_type = 2;
            }

            //如果泰国个人代理补卡 只能补下班
            if (isCountry('Th') && in_array($staff_info['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
                $default_attendance_type = 2;
            }
            //默认选中补卡类型
            $result['attendance_type_list'] = $this->makeAttendanceTypeDefault($result['attendance_type_list'], $default_attendance_type);
            //请假或者off 没有 默认选中逻辑
            if ($isDefault === false) {
                return $result;
            }
        }
        //是否只主播职位 不需要 默认选中的情况
        if (in_array($staff_info['job_title'], (new SettingEnvServer())->getSetVal('free_shift_position', ','))) {
            return $result;
        }
        //有日期 有类型
        if(!empty($attendance_type)){
            $default_attendance_type        = $attendance_type;
        }
        //默认 补卡时间
        if (!empty($default_attendance_type)) {
            $result['default_reissue_info']['day_type']   = '1';
            $result['default_reissue_info']['shift_time'] = $default_attendance_type == 1 ? $shiftInfo['start'] : $shiftInfo['end'];
            if ($default_attendance_type == 2 && $shiftInfo['start'] > $shiftInfo['end']) {
                $result['default_reissue_info']['day_type'] = '2';
            }
        }
        return $result;
    }

    protected function makeAttendanceTypeDefault($attendance_type_list, $type)
    {
        foreach ($attendance_type_list as &$item) {
            $item['is_default'] = $item['code'] == $type;
        }
        return $attendance_type_list;
    }
    

    /**
     * 耗材调拨入口
     * @param array $staff_info 员工信息组
     * @return bool
     */
    public function isShowPackageAllotPermission($staff_info): bool
    {
        $wms_allot_permission_job_ids = (new SettingEnvServer())->getSetVal('package_allot_permission_job_ids', ',');
        return in_array($staff_info['job_title'], $wms_allot_permission_job_ids);
    }

    /**
     * 网点运营 - 车辆维修申请 - 入口
     * @return bool
     */
    public function isShowVehicleRepairRequestPermission()
    {
        return (new VehicleCompanyInfoServer($this->lang, $this->timezone))->isShowVehicleRepairRequestPermission($this->staffInfo);
    }

    /**
     * 网点运营 - 交车&验车 - 入口
     * @return bool
     */
    public function isShowVehicleDeliveryAndInspectionPermission()
    {
        return (new VehicleCompanyInfoServer($this->lang, $this->timezone))->isShowVehicleDeliveryAndInspectionPermission($this->staffInfo);
    }

    /**
     *
     * @param $job_title_id
     * @return bool
     */
    public function isSpecJobTitles($job_title_id): bool
    {
        $configJobTitles = (new SettingEnvServer())->getSetVal('job_transfer_position_permission', CommonEnums::SEPARATOR_DEFAULT);
        if (empty($configJobTitles)) {
            return false;
        }
        if (in_array($job_title_id, $configJobTitles)) {
            return true;
        }
        return false;
    }

}
