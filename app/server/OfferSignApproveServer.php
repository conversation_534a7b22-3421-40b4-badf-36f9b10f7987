<?php

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\SalaryApproveModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferSignApproveModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\OfferRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\ResumeRepository;
use FlashExpress\bi\App\Repository\StaffAuditToolLog;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Repository\WmsRepository;
use Phalcon\Db;
use Phalcon\Db\Column;
use FlashExpress\bi\App\Repository\PublicRepository;


class OfferSignApproveServer extends AuditBaseServer
{
/**
     * workflow role
     */
    const WORKFLOW_ROLE_1 = '17758';
    const WORKFLOW_ROLE_2 = '56780';
    const PDF_TYPE_1 = [
        HrInterviewOfferSignApproveModel::PDF_TYPE_THB_NOT_TJ,
        HrInterviewOfferSignApproveModel::PDF_TYPE_THB_IN_TJ,
        HrInterviewOfferSignApproveModel::PDF_TYPE_SGD_NOT_BT_TJ,
        HrInterviewOfferSignApproveModel::PDF_TYPE_SGD_NOT_WBT_TJ,
        HrInterviewOfferSignApproveModel::PDF_TYPE_SGD_IN_BT_TJ,
        HrInterviewOfferSignApproveModel::PDF_TYPE_SGD_IN_WBT_TJ,
    ];

    const PDF_TYPE_2 = [
        HrInterviewOfferSignApproveModel::PDF_TYPE_THB_NOT_TD,
        HrInterviewOfferSignApproveModel::PDF_TYPE_THB_IN_TD,
        HrInterviewOfferSignApproveModel::PDF_TYPE_SGD_NOT_BT_TD,
        HrInterviewOfferSignApproveModel::PDF_TYPE_SGD_NOT_WBT_TD,
        HrInterviewOfferSignApproveModel::PDF_TYPE_SGD_IN_BT_TD,
        HrInterviewOfferSignApproveModel::PDF_TYPE_SGD_IN_WBT_TD
    ];

    const AUDIT_STATUS_0 = 0;//已撤销||薪资审批联动撤销
    const AUDIT_STATUS_1 = 1;//待审批
    const AUDIT_STATUS_2 = 2;//已同意
    const AUDIT_STATUS_3 = 3;//已驳回
    const AUDIT_STATUS_4 = 4;//已撤销
    const STATUS_PANDING_APPROVAL = 7;//同意
        /**
     * 面试待发offer
     */
    const STATE_INTERVIEW_WAIT_SEND_OFFER = 20;//驳回
    const STATE_INTERVIEW_SEND_OFFER = 25;//撤销
    const STATE_INTERVIEW_REVOKED_OFFER = 30; // 待审批
    const STATE_INTERVIEW_ENTRY = 40;

    //  17 申请  18 撤销 19 驳回 关联win——hr 项目
	const STATUS_APPROVAED_17 = 17;
	const STATUS_APPROVAED_18 = 18;
	const STATUS_APPROVAED_19 = 19;

    const STATUS_APPROVAED_20 = 20;
    const STATUS_APPROVAED_21 = 21;
    const STATUS_APPROVAED_22 = 22;
    const STATUS_APPROVAED_23 = 23; // 已同意

    public static $params = []; // 已同意 已离职自动通过
    public $timezone; // 已同意 已停职自动通过

    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->lang = $lang;
        $this->timezone = $timezone;
        parent::__construct($lang, null);
        $this->wf = new WorkflowServer($this->lang, $this->timezone);
        $this->other = new OtherRepository($timezone, $lang);
        $this->pub = new PublicRepository($timezone);
        $this->wms = new WmsRepository($timezone);
        $this->auditlist = new AuditlistRepository($lang, $timezone);
        $this->auditToolLog = new StaffAuditToolLog();
    } // 已同意 已停职自动通过

    public static $addParams = []; // 添加接口需要 其他地方不要赋值！！!!

    /**
     * 新增申请
     * @param $param
     * @return array
     */
    public function addApprove($param)
    {

        $db = $this->getDI()->get("db");
        $db->begin();
        try {

            self::$addParams = $param;

            $this->validateCheck($param, [
                'interview_id' => 'Required|int',//面试ID
                'resume_id' => 'Required|int',//简历ID
                'submitter_id' => 'Required|int',//提交人
                'approve_state' => 'Required|int',//审批状态
                'pdf_type' => 'Required|int',//pdf模板类型
                'pdf_path' => 'Required|Str',//pdf路径
            ]);

            $lastResume = $this->getLastApprove($param['resume_id']);
            if ($lastResume && in_array($lastResume['approve_state'], [self::AUDIT_STATUS_1, self::AUDIT_STATUS_2])) {
                // 正在审批 或 一审批通过
                return $this->checkReturn([
                    "code" => -1,
                    "msg" => "该offer签字正在审批中或已审批通过"
                ]);
            }

            $staff_info = (new StaffServer())->get_staff($param['submitter_id']);
            if (!isset($staff_info['data'])) {
                return $this->checkReturn([
                    "code" => -1,
                    "msg" => "未找到申请人！"
                ]);
            }

            $staff_info = $staff_info['data'];

            //审批流角色
            if (in_array($param['pdf_type'], self::PDF_TYPE_1)) {
                $settingEnvServer = new SettingEnvServer();
                $role_id = $settingEnvServer->getSetVal('offer_sign_approval_role_type_1');

                $workflowRole = $role_id;
            } else {
                $settingEnvServer = new SettingEnvServer();
                $role_id = $settingEnvServer->getSetVal('offer_sign_approval_role_type_2');

                $workflowRole = $role_id;
            }

            //生成一条记录 添加审批
            $HrInterviewOfferSignApproveModel = new HrInterviewOfferSignApproveModel();
            $result = $HrInterviewOfferSignApproveModel->save(
                [
                    'interview_id' => $param['interview_id'],
                    'resume_id' => $param['resume_id'],
                    'submitter_id' => $param['submitter_id'],
                    'approve_state' => 1,
                    'pdf_type' => $param['pdf_type'],
                    'pdf_path' => $param['pdf_path'],
                    "serial_no" => "ST" . $this->getID(),
                    "workflow_role" => $workflowRole,
                ]
            );

            //写入失败记录日志
            if (!$result) {
                $this->getDI()->get("logger")->write_log(
                    "addApprove: insert db: hr_interview_offer_sign_approve fail"
                    . json_encode($param, JSON_UNESCAPED_UNICODE)
                    , 'notice');
                throw new \Exception('OfferSignApproveServer :insert db HrInterviewOfferSignApproveModel fail');
            }
            //业务ID
            $offer_sign_id = $HrInterviewOfferSignApproveModel->id;
            //日志
            $this->insertLog($param['resume_id'], $param['submitter_id'], 17);

            //固化审批流流程
            $create_flow = (new ApprovalServer($this->lang, $this->timezone))->create(
                $offer_sign_id,
                enums::$audit_type['ST'],
                $param['submitter_id'],
                null
            );

            if (!$create_flow) {
                throw new \Exception("OfferSignApproveServer: 固化审批流流程失败 业务ID: {$offer_sign_id}");
            }

            $db->commit();
            $this->getDI()->get("logger")->write_log("OfferSignApproveServer : addApprove success: hr_interview_offer_sign_approve id:{$offer_sign_id}", "info");
            return $this->checkReturn([
                "code" => 1,
                "msg" => 'success'
            ]);
        } catch (\Exception $e) {
            $db->rollBack();

            $this->getDI()->get("logger")->write_log("OfferSignApproveServer : addApprove fail:" . $e->getMessage().' '.$e->getTraceAsString(), "notice");

            return $this->checkReturn([
                "code" => -1,
                "msg" => $e->getMessage()
            ]);
        }
    }

    //修改审批状态

    /**
     * 简历最新审批
     * @param $resumeId
     * @return mixed
     */
    public function getLastApprove($resumeId)
    {
        $sql = "--
            select 
                 id,
                 interview_id,
                 resume_id,
                 submitter_id,
                 approve_state,
                 pdf_type,
                 pdf_path,
                 workflow_role,
                 serial_no,
                 cancel_reason,
                 DATE_FORMAT( CONVERT_TZ(created_at, '+00:00', '{$this->timezone}' ), '%Y-%m-%d %H:%i:%s' ) as created_at,
                 DATE_FORMAT( CONVERT_TZ(updated_at, '+00:00', '{$this->timezone}' ), '%Y-%m-%d %H:%i:%s' ) as updated_at
            from hr_interview_offer_sign_approve 
            where resume_id = :resume_id order by id desc";

        return $this->getDI()->get("db")->fetchOne($sql, Db::FETCH_ASSOC, [
            'resume_id' => $resumeId
        ], [
            'resume_id' => Column::BIND_PARAM_INT
        ]);
    }

    /**
     * 列表展示字段
     * @param $param
     * @return array
     */
    public function showInfo($param)
    {
        $summary = [];
        $resume_info = (new ResumeRepository($this->timezone))->getResumeInfoById($param['resume_id']);
        if ($resume_info) {
            $container['key'] = 'hr_probation_name';//姓名
            $container['value'] = $resume_info->name;
            $summary[] = $container;
        }


        //获取部门 职位
        $interview_info = HrInterviewModel::findFirst([
            'conditions' => 'resume_id =:resume_id: and hc_id=:hc_id:',
            'bind' => ['resume_id' => $param['resume_id'],'hc_id'=>$resume_info->hc_id],
            'order' => 'interview_id desc'
            ]);
        if ($interview_info) {
            $hc_info = (new HcRepository($this->timezone))->getHcInfo($interview_info->hc_id);

            if ($hc_info) {
                $container['key'] = 'report_job_name';//职位
                $container['value'] = $hc_info['job_name'];
                $summary[] = $container;

                $container['key'] = 'hr_probation_department';//部门
                $container['value'] = $hc_info['department_name'];
                $summary[] = $container;
            }

            $container['key'] = 'estimated_time_entry';//预计入职时间
            if(isset($param['work_time'])){
                $time = explode(' ',$param['work_time']);
            }
            $container['value'] = isset($time) ? $time[0] : '';

            $summary[] = $container;
        }

        return $summary;
    }

    /**
     *写入hr_log
     * @param $resumeId
     * @param $operatorId
     * @param int $action 17 申请  18 撤销 19 驳回 关联win——hr 项目
     */
    public function insertLog($resumeId, $operatorId, $action = 17)
    {
        $interview_info = HrInterviewModel::findFirst([
            'conditions' => 'resume_id = :resume_id:',
            'bind' => ['resume_id' => $resumeId],
        ]);

        if (!$interview_info) {
            $this->getDI()->get("logger")->write_log("insertLog resume_id: {$resumeId}", "notice");
            return '';
        }

        $moduleType = 15; // 关联win-hr 项目的enums下的 module type
        $this->getDI()->get("db")->insertAsDict("hr_log", [
            "staff_info_id" => $operatorId,
            "module_id" => $interview_info->interview_id,
            "module_type" => $moduleType,
            "action" => $action,
        ]);
    }

    /**
     * 审批操作
     * @param $uid
     * @param $id
     * @param $status
     * @param $remark
     * @return array|void
     */
    public function updateApprove($uid, $id, $status, $remark)
    {
	    //开启事务
	    $db = $this->getDI()->get("db");
	    $db->begin();
        //申请业务记录
        $info = $this->getRecord($id);
        try {
            if (!$info) {
                $this->getDI()->get("logger")->write_log("offer_sign_approval_error: 非审批状态"
                    . json_encode($id, JSON_UNESCAPED_UNICODE)
                    . " id:" . $id);
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_("1015")));
            }

	        $app_server = new ApprovalServer($this->lang, $this->timeZone);
	        $res = [];
	        if ($status == enums::$audit_status['approved']) {//审核通过
		        $res = $app_server->approval($info['id'], enums::$audit_type['ST'], $uid['id'],$remark);
	        } elseif ($status == enums::$audit_status['dismissed']) {//驳回

		        $res = $app_server->reject($info['id'], enums::$audit_type['ST'],$remark, $uid['id']);

	        } elseif ($status == enums::$audit_status['revoked'] or $status == 0 ) {//撤销
		        $res = $app_server->cancel($id, enums::$audit_type['ST'], $remark, $uid['id']);
	        }

	        if (!isset($res) || empty($res)) {
		        throw new \Exception('offer 签字审批 失败  ' . json_encode($status).' uid => '.json_encode($uid). ' id=> '.json_encode($id) .json_encode($res));
	        }

	        $this->getDI()->get("logger")->write_log("OfferSignApproveServer 审批成功 " . $info['id'], "info");

	        $db->commit();
        } catch (\Exception $e) {

            if ($e->getCode() == ErrCode::WORKFLOW_DATA_ERROR && $status == enums::$audit_status['revoked']) {
                $this->setProperty($id, $status, null, true);
                $db->commit();
                return $this->checkReturn(["code" => 1, "msg" => 'sucess']);
            }

            $db->rollback();
            $this->getDI()->get("logger")->write_log("offer_sign_approval_error: 事务未提交"
                . " id: " . $id
                . " uid: " . $uid['id']
                . " message: " . $e->getMessage()
                . " line: " . $e->getLine()
                . " file: " . $e->getFile());
            return $this->checkReturn(["code" => -1, "msg" => $this->getTranslation()->_("4008")]);
        }

	    return $this->checkReturn(["code" => 1, "msg" => 'sucess']);

    }


    /**
     * 撤销操作
     * @param $id 申请记录id
     * @param $uid 当前登陆用户
     * @param $info 申请记录
     * @param $approveList 聚合审批人
     * @param $status  状态
     * @param $resume_id 简历id
     * @param $remark 备注
     * @return array|void
     */
    public function undo($id, $uid, $info, $approveList, $status, $resume_id, $remark){
        try {

            $db = $this->getDI()->get("db");
            $db->begin();

            //判断当前操作
            if(!in_array($status,[self::AUDIT_STATUS_4,0])){
                return $this->checkReturn(["code" => -1, "msg" => $this->getTranslation()->_("4008")]);
            }

            if (isset($approveList[enums::$audit_list_status['panding_approval']])) {
                // 存在待审批 将待审批状态抹除
                $unionStatus = $db->updateAsDict("staff_audit_union", [
                    "status_union" => 104
                ], [
                    "conditions" => " id_union = ? and status_union = ? and staff_id_union = ? and type_union = ? ",
                    "bind" => [
                        "st_" . $id,
                        enums::$audit_list_status['panding_approval'],
                        $info['submitter_id'],
                        enums::$audit_type['ST'],
                    ]
                ]);

                $approvalStatus = $db->updateAsDict("staff_audit_approval", [
                    "status" => 0
                ], [
                    "conditions" => " type = ? and audit_id = ? and status = ? and submitter_id = ? ",
                    "bind" => [
                        enums::$audit_type['ST'],
                        $id,
                        self::STATUS_PANDING_APPROVAL,
                        $info['submitter_id']
                    ]
                ]);
                if (!$unionStatus || !$approvalStatus) {
                    throw new \Exception("OfferSignApproveServer undo 撤销1失败 union: " . $unionStatus . " approval: " . $approvalStatus);
                }
            }

            $userInfo = (new StaffRepository())->getStaffInfoById($uid['id']);
            $logStatus = $this->other->insertLog([
                "staff_id" => $info['submitter_id'],
                "type" => enums::$audit_type['ST'],
                "original_type" => self::AUDIT_STATUS_1,
                "to_status_type" => $status,
                "original_id" => $info['id'],
                "operator" => $uid['id'],
                "operator_name" => $userInfo['nick_name'],
            ]);

            //修改业务表状态为已取消
            $salaryStatus = $db->updateAsDict("hr_interview_offer_sign_approve", [
                "approve_state" => $status,
                "cancel_reason" => $remark,
            ], "id=" . $info['id']);

            $unionStatus = $db->updateAsDict("staff_audit_union", [
                "status_union" => enums::$audit_list_status['revoked'],
            ], [
                "conditions" => " id_union = ? and status_union = ? and staff_id_union = ? and type_union = ? ",
                "bind" => [
                    "st_" . $id,
                    enums::$audit_list_status['panding'],
                    $info['submitter_id'],
                    enums::$audit_type['ST']
                ]
            ]);

            if (!$logStatus || !$salaryStatus || !$unionStatus) {
                throw new \Exception("OfferSignApproveServer undo table:hr_interview_offer_sign_approve、staff_audit_tool_log: 撤销失败 id：{$id} log: " . $logStatus);
            }

            $this->insertLog($info['resume_id'], $uid['id'], 18);

            //修改offer状态
            $offerInfo = HrInterviewOfferModel::findFirst([
                'conditions' => 'resume_id = :resume_id:',
                'bind' => ['resume_id' => $resume_id],
                'order'=> 'id desc'
            ]);
            if($offerInfo){
                $offerInfo->status = enums::$hr_interview_offer_state['offer_sign_cancel'];//撤销offer签字
                if(!$offerInfo->save()){
                    throw new \Exception("OfferSignApproveServer  undo:offer 签字撤销 修改offer表状态失败 resume_id：{$resume_id} ");
                }
            }

            $db->commit();
            return $this->checkReturn(["code" => 1, "msg" => 'sucess']);
        }catch (\Exception $e){
            $db->rollback();
            $this->getDI()->get("logger")->write_log("offer_sign_approval undo: 事务未提交"
                . " id: " . $id
                . " uid: " . $uid['id']
                . " resume_id: " . $resume_id
                . " message: " . $e->getMessage()
                . " line: " . $e->getLine()
                . " file: " . $e->getFile());
            return $this->checkReturn(["code" => -1, "msg" => $this->getTranslation()->_("4008")]);
        }
    }

	/**
	 * 撤销操作
	 * @param $id 申请记录id
	 * @param $uid 当前登陆用户
	 * @param $info 申请记录
	 * @param $approveList 聚合审批人
	 * @param $status  状态
	 * @param $resume_id 简历id
	 * @param $remark 备注
	 * @return array|void
	 */
	public function undoV2($id, $uid, $info, $resume_id){
		try {
			$this->insertLog($info['resume_id'], $uid, self::STATUS_APPROVAED_18);
			//修改offer状态
			$offerInfo = HrInterviewOfferModel::findFirst([
				                                              'conditions' => 'resume_id = :resume_id:',
				                                              'bind' => ['resume_id' => $resume_id],
				                                              'order'=> 'id desc'
			                                              ]);
			if($offerInfo){
				$offerInfo->status = enums::$hr_interview_offer_state['offer_sign_cancel'];//撤销offer签字
				if(!$offerInfo->save()){
					throw new \Exception("OfferSignApproveServer  undo:offer 签字撤销 修改offer表状态失败 resume_id：{$resume_id} ");
				}
			}


		}catch (\Exception $e){
			$this->getDI()->get("logger")->write_log("offer_sign_approval undo: 事务未提交"
			                                         . " id: " . $id
			                                         . " uid: " . $uid['id']
			                                         . " resume_id: " . $resume_id
			                                         . " message: " . $e->getMessage()
			                                         . " line: " . $e->getLine()
			                                         . " file: " . $e->getFile());
			throw new \Exception($e->getMessage());

		}
		return true;
	}


    /**
     * 通过主键获取offer表信息
     * @param $params
     * @return mixed
     */
    public function getRecord($id)
    {
        $result = HrInterviewOfferSignApproveModel::findFirst([
            'columns' =>
                "
                id, 
                interview_id, 
                resume_id, 
                submitter_id, 
                approve_state, 
                serial_no,
                pdf_type, 
                pdf_path,
                cancel_reason,
                DATE_FORMAT( CONVERT_TZ(created_at, '+00:00', '{$this->timezone}' ), '%Y-%m-%d %H:%i:%s' ) as created_at,
                DATE_FORMAT( CONVERT_TZ(updated_at, '+00:00', '{$this->timezone}' ), '%Y-%m-%d %H:%i:%s' ) as updated_at
            ",
            'conditions' => 'id=:id:',
            'bind' => ['id' => $id]
        ]);
        if ($result) {
            return $result->toArray();
        }
        return [];
    }

    /**
     *聚合审批人
     * @param $idUnion
     *
     */
    private function getApproveGrpByStatus($idUnion)
    {
        $sql = "--
        select
            *,
            group_concat(approval_id) as approval_ids
        from
            staff_audit_union
        where id_union = :id_union and type_union = :type_union group by status_union for update;
        ";
        $db = $this->getDI()->get('db');
        $list = $db->fetchAll($sql, Db::FETCH_ASSOC, [
            "id_union" => $idUnion,
            "type_union" => enums::$audit_type['ST'],
        ]);

        return array_column($list, null, 'status_union');
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $offer_sign = HrInterviewOfferSignApproveModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind' => ['id' => $auditId]
        ]);

        if ($offer_sign) {
            //最新的实时数据
            $param = $offer_sign->toArray();
            $param['work_time'] = isset(self::$addParams['work_time']) ? self::$addParams['work_time'] : '';
            return $this->showInfo($param);
        }
        return [];
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
	    $offer_sign = HrInterviewOfferSignApproveModel::findFirst([
		                                                              'conditions' => 'id = :id: ',
		                                                              'bind' => [
			                                                              'id' => $auditId
		                                                              ]
	                                                              ]);
	    $info = $offer_sign->toArray();
	    $userInfo = (new StaffRepository())->getStaffInfoById($extend['staff_id']);
	    //日志
	    $this->other->insertLog([
		                                         "staff_id" => $info['submitter_id'],
		                                         "type" => enums::$audit_type['ST'],
		                                         "original_type" => enums::APPROVAL_STATUS_PENDING,
		                                         "to_status_type" => $state,
		                                         "original_id" => $info['id'],
		                                         "operator" => $extend['staff_id'],
		                                         "operator_name" => $userInfo['name'],
	                                         ]);



        if ($isFinal) { //终审

            if ($state == enums::APPROVAL_STATUS_REJECTED) {
	            // 驳回

	            $offerInfo = HrInterviewOfferModel::findFirst([
		                                                          'conditions' => 'resume_id = :resume_id:',
		                                                          'bind' => ['resume_id' => $info['resume_id']],
		                                                          'order'=> 'id desc'
	                                                          ]);
	            if($offerInfo){
		            $offerInfo->status = enums::$hr_interview_offer_state['offer_sign_revoke'];//驳回offer
		            $offerInfo->save();
	            }

	            $this->insertLog($info['resume_id'], $extend['staff_id'], self::STATUS_APPROVAED_19);

            } elseif ($state == enums::APPROVAL_STATUS_CANCEL) {
            	//撤销
	            //执行撤销操作
	             $this->undoV2($auditId, $extend['staff_id'], $info, $info['resume_id']);
            } else if ($state == enums::APPROVAL_STATUS_APPROVAL) {
		            //给 winhr 发从相关信息
		            $params = [
			            'resume_id'   => $info['resume_id']
		            ];
		            $rpcClient = (new ApiClient('winhr_rpc','','addOfferSignDate', $this->lang));
		            $rpcClient->setParams($params);
		            $return = $rpcClient->execute();

		            $this->getDI()->get('logger')->write_log("winhr_rpc addOfferSignDate:" . json_encode($return,JSON_UNESCAPED_UNICODE), 'info');
		            if (isset($return['result']['code']) && $return['result']['code'] == 1) {
			            //更新hr_interview_offer_sign_approve pdf地址
			            $new_pdf_path = $return['result']['data']['new_pdf_path'];
		            }

		            //todo 给offer签字发起人发送消息
		            $this->sendOfferSignApprovePassedMsg($info['resume_id'],$info['submitter_id']);

            }


	        $offer_sign->approve_state = $state;
	        $offer_sign->cancel_reason = $extend['remark'] ?? '';
	        if(isset($new_pdf_path)){
		        $offer_sign->pdf_path = $new_pdf_path;
	        }
	        $offer_sign->save();

        }
        //以前没有返回值 现在返回一个
        return true;
    }

    /**
     * @param $auditId
     * @param $user
     * @param $state
     * @return array
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $offerSignApprove = HrInterviewOfferSignApproveModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind' => ['id' => $auditId]
        ]);

        if (empty($offerSignApprove)) {
            return [];

        }

        if (in_array($offerSignApprove->pdf_type, self::PDF_TYPE_1)) {
            $k1 = 'type1';
        } else {
            $k1 = 'type2';
        }

        return [
            'country_code' => env('country_code', 'th'),
            'k1' => $k1
        ];
    }

    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {

        return $this->approvalDetail([
            'st_id' => $auditId,
            'userinfo' => ['id' => $user, 'staff_id' => $user]
        ], $comeFrom);
    }

    /**
     * 个人基本信息
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array|array[]
     */
    public function approvalDetail($postData, $comFrom)
    {

        $offer_sign_info = $this->getRecord($postData['st_id']);
        if(!$offer_sign_info){
            return ["data" => [
                "head" => '',
                "detail" => '',
                "stream" => '',
            ]];
        }

        $staff_info = (new StaffServer($this->lang, $this->timezone))->get_staff($offer_sign_info['submitter_id']);
        //获取薪资表里的职级 "CONVERT_TZ(work_time, '+00:00', '" . $this->timezone . "' ) AS work_times,".
        $offerInfo = HrInterviewOfferModel::findFirst([
            'columns' => 'job_title_grade,id,work_time,position_id,currency,subsidy_type,internship_salary,hire_type,work_days,company',
            'conditions' => 'resume_id = :resume_id:',
            'bind' => ['resume_id' => $offer_sign_info['resume_id']],
            'order'=> 'id desc'
        ]);

        if (isset($staff_info['data']) && !empty($staff_info['data'])) {
            //获取简历附件  1 是半身照
            $hr_annex = (new SalaryServer($this->lang, $this->timezone))->getHrAnnex(1, $offer_sign_info['resume_id']);
    
            $data = [
                "title" => $this->auditlist->getAudityType(enums::$audit_type['ST']),
                "id" => $postData['st_id'],
                "staff_id" => $offer_sign_info['submitter_id'],
                "job_id" => 1,//$salaryInfo['job_id'],
                "job_name" => $staff_info['data']['job_name'] ?? "",
                "type" => enums::$audit_type['ST'],
                "created_at" => $offer_sign_info['created_at'],
                "updated_at" => $offer_sign_info['updated_at'],
                "status_text" => $this->auditlist->getAuditStatus('10' . $offer_sign_info['approve_state']),
                "status" => $this->transferStatus($offer_sign_info['approve_state']),
                "serial_no" => $offer_sign_info['serial_no'],
                //'salary_view_permission' => $postData['userinfo']['id'] == $offer_sign['submitter_id'] || in_array($postData['userinfo']['id'], self::SALARY_VIEW_PERMISSION) ? 1 : 0,
                //候选人照片  半身照
                'candidate_avator' => $hr_annex['url'] ?? 'https://' . env('hris_profile_uri') . env('default_avator'),
                'job_title_grade' => isset($offerInfo->job_title_grade) ? "F" . $offerInfo->job_title_grade : '',
                'currency' => $offerInfo->currency ?? SalaryApproveModel::CURRENCY_THB,
                'subsidy_type' => $offerInfo->subsidy_type ?? SalaryApproveModel::SUBSIDY_TYPE_NOT_SELECT,
            ];

            $staff_info['data']['resume_id'] = $offer_sign_info['resume_id'];
            $staff_info['data']['submitter_id'] = $offer_sign_info['submitter_id'];
            $staff_info['data']['currency'] = $offerInfo->currency ?? SalaryApproveModel::CURRENCY_THB;
            
            $work_time = date('Y-m-d',strtotime($offerInfo->work_time));

            $staff_info['data']['work_time'] = $work_time;
            $position_name = '';
            if (!empty($offerInfo->position_id)) {
                $positionData                = (new SysListRepository())->getPositionList(['ids' => $offerInfo->position_id]);
                $position_name = !empty( $positionData[0] ) ? $positionData[0]['name']:'';
            }
            $staff_info['data']['position_name'] = $position_name;

            $staff_info['data']['job_title_grade'] = $offerInfo->job_title_grade;
            $staff_info['data']['internship_salary'] = $offerInfo->internship_salary;
            $staff_info['data']['hire_type'] =  $offerInfo->hire_type;
            $staff_info['data']['company'] =  $offerInfo->company;
            $staff_info['data']['work_days'] =  $offerInfo->work_days;

            $detail = $this->getDetail_1($staff_info['data']);
            $streams = $this->getStreams($postData, $comFrom);

            $salaryItems = [];
            if (isCountry('Id')) {
                $SalaryServerObj = new SalaryServer($this->lang, $this->timezone);
                $salaryInfo = $SalaryServerObj->getLastApprove($staff_info['data']['resume_id']);
                $salaryItems = $SalaryServerObj->salaryApproveItems($salaryInfo);
            }

            $salary = (new SalaryServer($this->lang, $this->timezone))->getLastApprove($offer_sign_info['resume_id']);
            if ($salary) {
                if ($salary['work_days'] != HrStaffInfoModel::WEEK_WORKING_DAY_FREE) {
                    $work_days['key']        = $this->getTranslation()->_("work_days");//"工作天数";
                    $work_days['value']      = $salary['work_days'];
                    $work_days['simple_val'] = '';
                    $work_days['type']       = '';
                    $detail[]                = $work_days;
                }

                $company['key']        = $this->getTranslation()->_("company_name");//公司名称
                $company['value']      = $salary['company'];
                $company['simple_val'] = '';
                $company['type']       = '';
                $detail[]              = $company;
            }

            return ["data" => [
                "head" => $data,
                "detail" => $detail,
                "salary_items" => $salaryItems,
                "stream" => $streams,
            ]];
        }
    }

    /**
     *
     * 转 状态
     * @param $status
     */
    private function transferStatus($status)
    {
        $statusMaps = [
            1 => 7
        ];
        return isset($statusMaps[$status]) ? $statusMaps[$status] : $status;
    }

    /**
     * 获取options 控制按钮
     * @param $comFrom
     * @param $idUnion
     * @param $userinfo
     *
     */
    public function getOptions($comFrom, $idUnion, $userinfo)
    {

        $list = $this->getApproveGrpByStatus($idUnion);

        $options = [];
        if ($comFrom == 2) {// comfrom 我审批的
            if (isset($list[enums::$audit_list_status['panding_approval']])) {
                $approvalIds = explode(",", $list[enums::$audit_list_status['panding_approval']]['approval_ids']);
                if (in_array($userinfo['staff_id'], $approvalIds)) {
                    $options = [1, 2];
                }
            }
        } else { // 我申请的
            if (isset($list[enums::$audit_list_status['panding']])) {
                $options = [3];
            }
        }
        $result = [];
        foreach ($options as $i) {
            $result['code' . $i] = $i;
        }
        return $result;

    }

    /**
     * 获取详情 detail
     * @param $salaryInfo
     */
    public function getDetail_1($salaryInfo)
    {

        $resumeId = $salaryInfo['resume_id'];
        $info = $this->isCanAddApprove($resumeId, [self::STATE_INTERVIEW_WAIT_SEND_OFFER, self::STATE_INTERVIEW_SEND_OFFER, self::STATE_INTERVIEW_REVOKED_OFFER, self::STATE_INTERVIEW_ENTRY]);
        $this->getDI()->get("logger")->write_log("offer_sign" . json_encode($info, JSON_UNESCAPED_UNICODE), "info");
        $detail = [];
        $jobs = $this->wms->getJobByids([$salaryInfo['job_id'], $info['hc_job_id']]);
        $jobName = '';
        if ($jobs) {
            $jobs = array_column($jobs, null, 'id');
            $jobName = $jobs[$salaryInfo['job_id']]['name'];
        }

        $jd = $this->getDI()->get("db")->fetchOne("--
        select
            hr_jd.job_id ,
            hr_jd.job_name,
            hr_hc.province_code,
            hr_resume.address_id
            from 
                hr_resume
            left join hr_interview on hr_interview.resume_id = hr_resume.id and hr_interview.hc_id = hr_resume.hc_id 
            left join hr_hc on hr_interview.hc_id = hr_hc.hc_id
            left join hr_jd on hr_hc.job_id = hr_jd.job_id
            where hr_resume.id = " . $salaryInfo['resume_id'], Db::FETCH_ASSOC);

        $innerJobName = $province = '';
        if ($jd) {
            $innerJobName = $jd['job_name'];
            $province = (new StaffRepository($this->lang))->getProvince($jd['address_id']);
        }

        $departMents = (new SysListRepository())->getDepartmentList(["ids" => $info['hc_department_id']]);
        $department = '';
        if ($departMents) {
            $departMents = array_column($departMents, null, 'id');
            $department = $departMents[$info['hc_department_id']]['name'];
        }
        //是否是一线职位
        $is_front_line_job = (new OfferSignApproveServer($this->lang,$this->timezone))->isFrontLineJob($salaryInfo['job_id']);

        $stores = (new SysListRepository())->getStoreList(["ids" => getIdsStr([$info['worknode_id']])]);
        $store = '';
        if ($stores) {
            $stores = array_column($stores, null, 'id');
            if ($info['worknode_id'] && isset($stores[$info['worknode_id']])) {
                $store = $stores[$info['worknode_id']]['name'];
            }
        }
        //当前用户信息
        $staff_info = (new StaffServer($this->lang, $this->timezone))->get_staff($salaryInfo['submitter_id']);
        if ($staff_info['data']) {
            $staff_info = $staff_info['data'];
        }

        //获取简历附件 16 是简历附件
        $hr_annex = (new SalaryServer($this->lang, $this->timezone))->getHrAnnex(16, $salaryInfo['resume_id']);
        $work_time = $salaryInfo['work_time'];
        //入职职位改为取offer表 position_id
        $position_name = $salaryInfo['position_name'];
        if ($info) {
            $detail[] = ["k" => "apply_parson", "key" => $this->getTranslation()->_("apply_parson"), "value" => sprintf('%s ( %s )', $staff_info['name'], $salaryInfo['submitter_id']), 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "apply_department", "key" => $this->getTranslation()->_("apply_department"), "value" => sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? ''), 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "hr_probation_name", "key" => $this->getTranslation()->_("hr_probation_name"), "value" => $info['name'], 'simple_val' => '', 'type' => 0];

            $detail[] = ["k" => "resume_attachment", "key" => $this->getTranslation()->_("resume_attachment"), "value" => $hr_annex['original_name'] ?? '', 'simple_val' => $hr_annex["url"] ?? '', 'type' => 1];//简历附件  type 1是附件

            $detail[] = ["k" => "cv_id", "key" => $this->getTranslation()->_("cv_id"), "value" => $info['id'], 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "hc_id", "key" => $this->getTranslation()->_("hc_id"), "value" => $info['hc_id'], 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "work_time", "key" => $this->getTranslation()->_("estimated_time_entry"), "value" => $work_time, 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "job_title_grade", "key" => $this->getTranslation()->_("hr_probation_field_grade"), "value" => !isset($salaryInfo['job_title_grade']) || is_null($salaryInfo['job_title_grade']) ? '' : "F" . $salaryInfo['job_title_grade'], 'simple_val' => !isset($salaryInfo['job_title_grade']) || is_null($salaryInfo['job_title_grade']) ? '' : $salaryInfo['job_title_grade'], 'type' => 0]; // 职级

            $detail[] = ["k" => "department", "key" => $this->getTranslation()->_("salary_department"), "value" => $department, 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "city", "key" => $this->getTranslation()->_("salary_city"), "value" => $province ? $province['name'] : '', 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "store", "key" => $this->getTranslation()->_("salary_store"), "value" => $store, 'simple_val' => '', 'type' => 0];


            $detail[] = ["k" => "job", "key" => $this->getTranslation()->_("salary_job"), "value" => $position_name, 'simple_val' => '', 'type' => 0];
            if (strtolower(env('country_code', 'Th')) == 'th') {
                $company = SalaryApproveModel::CURRENCY_THB_COMPANY;
                if (isset($salaryInfo['currency']) && $salaryInfo['currency'] == SalaryApproveModel::CURRENCY_SGD) {
                    $company = SalaryApproveModel::CURRENCY_SGD_COMPANY;
                }
                
                $salaryInfo = (new SalaryServer($this->lang, $this->timezone))->getLastApprove($resumeId);
                $detail[] = ["k" => "current_salary", "key" => $this->getTranslation()->_("current_salary"), "value" => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : (bcdiv((int)$salaryInfo['current_salary'], 100, 0) . " {$company}"), 'simple_val' => (!isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary']), 'type' => 0];//当前薪资

                $detail[] = ["k" => "money", "key" => $this->getTranslation()->_("salary_money"), "value" => $salaryInfo['basic_salary'] . " {$company}", 'simple_val' => $salaryInfo['basic_salary'], 'type' => 0];
                $detail[] = ["k" => "trial_salary", "key" => $this->getTranslation()->_("trial_salary"), "value" => $salaryInfo['trial_salary'] . " {$company}", 'simple_val' => $salaryInfo['trial_salary'], 'type' => 0];
                $detail[] = ["k" => "renting", "key" => $this->getTranslation()->_("renting"), "value" => $salaryInfo['renting'] . " {$company}", 'simple_val' => $salaryInfo['renting'], 'type' => 0];

                if($is_front_line_job == 0) { //非一线职位显示工作天数
                    // 非一线 泰国 添加 星火激励进贴 岗位进贴字段

                    $detail[] = ["k" => "position_allowance", "key" => $this->getTranslation()->_("job_transfer.position_allowance"), "value" => (int)$salaryInfo['position_allowance'] . " {$company}", 'simple_val' => (int)$salaryInfo['position_allowance'], 'type' => 0];
                    $detail[] = ["k" => "xinghuo_allowance", "key" => $this->getTranslation()->_("xinghuo_allowance"), "value" => (int)$salaryInfo['xinghuo_allowance'] . " {$company}", 'simple_val' => (int)$salaryInfo['xinghuo_allowance'], 'type' => 0];
                }
                
                $detail[] = ["k" => "exp", "key" => $this->getTranslation()->_("exp"), "value" => $salaryInfo['exp'] . " {$company}", 'simple_val' => $salaryInfo['exp'], 'type' => 0];


            } else if (strtolower(env('country_code', 'Th')) == 'ph') {
                $salaryInfo = (new SalaryServer($this->lang, $this->timezone))->getLastApprove($resumeId);
                $detail[] = ["k" => "current_salary", "key" => $this->getTranslation()->_("current_salary"), "value" => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : (bcdiv((int)$salaryInfo['current_salary'], 100, 0) . " PHP"), 'simple_val' => (!isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary']), 'type' => 0];//当前薪资

                $detail[] = ["k" => "money", "key" => $this->getTranslation()->_("salary_money"), "value" => $salaryInfo['basic_salary'] . " PHP", 'simple_val' => $salaryInfo['basic_salary'], 'type' => 0];
                $detail[] = ["k" => "trial_salary", "key" => $this->getTranslation()->_("trial_salary"), "value" => $salaryInfo['trial_salary'] . " PHP", 'simple_val' => $salaryInfo['trial_salary'], 'type' => 0];
                $detail[] = ["k" => "renting", "key" => $this->getTranslation()->_("renting"), "value" => $salaryInfo['renting'] . " PHP", 'simple_val' => $salaryInfo['renting'], 'type' => 0];

                if (!empty($salaryInfo['xinghuo_allowance'])) {
                    $detail[] = ["k" => "xinghuo_allowance", "key" => $this->getTranslation()->_("xinghuo_allowance"), "value" => (int)$salaryInfo['xinghuo_allowance'] . " PHP", 'simple_val' => (int)$salaryInfo['xinghuo_allowance'], 'type' => 0];
                }
                if (!empty($salaryInfo['deminimis_benefits'])) {
                    $detail[] = ["k" => "deminimis_benefits", "key" => $this->getTranslation()->_("deminimis_benefits"), "value" => (int)$salaryInfo['deminimis_benefits'] . " PHP", 'simple_val' => (int)$salaryInfo['deminimis_benefits'], 'type' => 0];
                }
                if (!empty($salaryInfo['other_non_taxable_allowance'])) {
                    $detail[] = ["k" => "other_non_taxable_allowance", "key" => $this->getTranslation()->_("other_non_taxable_allowance"), "value" => (int)$salaryInfo['other_non_taxable_allowance'] . " PHP", 'simple_val' => (int)$salaryInfo['other_non_taxable_allowance'], 'type' => 0];
                }

            } else if (strtolower(env('country_code', 'Th')) == 'la') {
                $salaryInfo = (new SalaryServer($this->lang, $this->timezone))->getLastApprove($resumeId);
                $detail[] = ["k" => "current_salary", "key" => $this->getTranslation()->_("current_salary"), "value" => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : (bcdiv((int)$salaryInfo['current_salary'], 100, 0) . " LAK"), 'simple_val' => (!isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary']), 'type' => 0];//当前薪资

                $detail[] = ["k" => "money", "key" => $this->getTranslation()->_("salary_money"), "value" => $salaryInfo['basic_salary'] . " LAK", 'simple_val' => $salaryInfo['basic_salary'], 'type' => 0];
                $detail[] = ["k" => "trial_salary", "key" => $this->getTranslation()->_("trial_salary"), "value" => $salaryInfo['trial_salary'] . " LAK", 'simple_val' => $salaryInfo['trial_salary'], 'type' => 0];
                $detail[] = ["k" => "renting", "key" => $this->getTranslation()->_("renting"), "value" => $salaryInfo['renting'] . " LAK", 'simple_val' => $salaryInfo['renting'], 'type' => 0];

                if (!empty($salaryInfo['xinghuo_allowance'])) {
                    $detail[] = ["k" => "xinghuo_allowance", "key" => $this->getTranslation()->_("xinghuo_allowance"), "value" => (int)$salaryInfo['xinghuo_allowance'] . " LAK", 'simple_val' => (int)$salaryInfo['xinghuo_allowance'], 'type' => 0];
                }
            } else if (strtolower(env('country_code', 'th')) == 'vn') {
                $salaryInfo = (new SalaryServer($this->lang, $this->timezone))->getLastApprove($resumeId);
                $detail[] = ["k" => "current_salary", "key" => $this->getTranslation()->_("current_salary"), "value" => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : (bcdiv((int)$salaryInfo['current_salary'], 100, 0) . " VND"), 'simple_val' => (!isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary']), 'type' => 0];//当前薪资

                $detail[] = ["k" => "money", "key" => $this->getTranslation()->_("salary_money"), "value" => $salaryInfo['basic_salary'] . " VND", 'simple_val' => $salaryInfo['basic_salary'], 'type' => 0];
                $detail[] = ["k" => "trial_salary", "key" => $this->getTranslation()->_("trial_salary"), "value" => $salaryInfo['trial_salary'] . " VND", 'simple_val' => $salaryInfo['trial_salary'], 'type' => 0];
                $detail[] = ["k" => "renting", "key" => $this->getTranslation()->_("renting"), "value" => $salaryInfo['renting'] . " VND", 'simple_val' => $salaryInfo['renting'], 'type' => 0];

                if (!empty($salaryInfo['xinghuo_allowance'])) {
                    $detail[] = ["k" => "xinghuo_allowance", "key" => $this->getTranslation()->_("xinghuo_allowance"), "value" => (int)$salaryInfo['xinghuo_allowance'] . " VND", 'simple_val' => (int)$salaryInfo['xinghuo_allowance'], 'type' => 0];
                }

            }else if (strtolower(env('country_code', 'my')) == 'my') {
                if ($salaryInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_5) {
                    $detail[] = [
                        'k'          => 'internship_salary',
                        'key'        => $this->getTranslation()->_('internship_salary'),
                        'value'      => !isset($salaryInfo['internship_salary']) || is_null($salaryInfo['internship_salary']) ? '' : (bcdiv((int)$salaryInfo['internship_salary'],
                                100, 0).' MYR'),
                        'simple_val' => (!isset($salaryInfo['internship_salary']) || is_null($salaryInfo['internship_salary']) ? '' : $salaryInfo['internship_salary']),
                        'type'       => 0,
                    ];//实习期工资

                    //"工作天数";
                    if ($salaryInfo['work_days'] != HrStaffInfoModel::WEEK_WORKING_DAY_FREE) {
                        $detail[] = [
                            'key'        => $this->getTranslation()->_('work_days'),
                            'value'      => $salaryInfo['work_days'],
                            'simple_val' => '',
                            'type'       => '',
                        ];
                    }
                    //公司名称
                    $detail[] = [
                        'key'        => $this->getTranslation()->_('company_name'),
                        'value'      => $salaryInfo['company'],
                        'simple_val' => '',
                        'type'       => '',
                    ];

                } else {
                    $salaryInfo = (new SalaryServer($this->lang, $this->timezone))->getLastApprove($resumeId);
                    $detail[]   = [
                        "k"          => "current_salary",
                        "key"        => $this->getTranslation()->_("current_salary"),
                        "value"      => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : (bcdiv((int)$salaryInfo['current_salary'],
                                100, 0)." MYR"),
                        'simple_val' => (!isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary']),
                        'type'       => 0,
                    ];//当前薪资

                    $detail[] = ["k"          => "money",
                                 "key"        => $this->getTranslation()->_("salary_money"),
                                 "value"      => $salaryInfo['basic_salary']." MYR",
                                 'simple_val' => $salaryInfo['basic_salary'],
                                 'type'       => 0
                    ];
                    $detail[] = ["k"          => "trial_salary",
                                 "key"        => $this->getTranslation()->_("trial_salary"),
                                 "value"      => $salaryInfo['trial_salary']." MYR",
                                 'simple_val' => $salaryInfo['trial_salary'],
                                 'type'       => 0
                    ];
                    $detail[] = ["k"          => "renting",
                                 "key"        => $this->getTranslation()->_("renting"),
                                 "value"      => $salaryInfo['renting']." MYR",
                                 'simple_val' => $salaryInfo['renting'],
                                 'type'       => 0
                    ];

                    if (!empty($salaryInfo['xinghuo_allowance'])) {
                        $detail[] = ["k"          => "xinghuo_allowance",
                                     "key"        => $this->getTranslation()->_("xinghuo_allowance"),
                                     "value"      => (int)$salaryInfo['xinghuo_allowance']." MYR",
                                     'simple_val' => (int)$salaryInfo['xinghuo_allowance'],
                                     'type'       => 0
                        ];
                    }
                }

            }else if (strtolower(env('country_code', 'id')) == 'id') {

                $salaryInfo = (new SalaryServer($this->lang, $this->timezone))->getLastApprove($resumeId);
                $detail[] = ["k" => "current_salary", "key" => $this->getTranslation()->_("current_salary"), "value" => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : (bcdiv((int)$salaryInfo['current_salary'], 100, 0) . " IDR"), 'simple_val' => (!isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary']), 'type' => 0];//当前薪资

            }
        }

        return $detail;

    }

    /**
     * 是否可以添加 薪资审批
     * @param $resumeId
     * @param int[] $state
     *
     */
    public function isCanAddApprove($resumeId, $state = [self::STATE_INTERVIEW_WAIT_SEND_OFFER])
    {
        $state = implode(",", $state);
        $sql = "-- 待发offer
        select 
            hr_resume.id,
            hr_resume.name,
            hr_resume.phone,
            hr_resume.email,
            hr_resume.address_id,
            hr_hc.department_id as hc_department_id,
            hr_hc.job_id as hc_job_id,
            hr_hc.hc_id,
            hr_hc.worknode_id,
            hr_hc.city_code
        from 
            hr_resume 
        left join hr_interview on hr_interview.resume_id = hr_resume.id and hr_interview.hc_id = hr_resume.hc_id
        left join hr_hc on hr_interview.hc_id = hr_hc.hc_id
        where 
            hr_resume.id = :resume_id and hr_interview.state in (" . $state . ") 
        ";
        $db = $this->getDI()->get("db");
        return $db->fetchOne($sql, Db::FETCH_ASSOC, [
            "resume_id" => $resumeId,
        ]);
    }

    /**
     * 审批流程
     * @param $postData
     * @param $comFrom
     */
    public function getStreams($postData, $comFrom)
    {
        $auditLogs = $this->auditToolLog->getAuditRecords(['id' => $postData['st_id'], 'type' => enums::$audit_type['ST']]);

        $list = $this->getApproveGrpByStatus("st_" . $postData['st_id']);
        $offerSignApproval = $this->getRecord($postData['st_id']);

        $streams = [];
        foreach ($auditLogs as $k => $auditLog) {
            $staff = (new StaffRepository())->getStaffpositionV2($auditLog['operator']);
            $staff_name = $staff['name'];
            $staff_id = $auditLog['operator'];
            if(isCountry('ID')) {
                $staff_name = $auditLog['operator'] == '56780' ? '' : $staff['name'];
                $staff_id = $auditLog['operator'] == '56780' ? '' : $auditLog['operator'];
            }

            if ($k == 0) {
                $streams[] = [
                    "staff_id" => $staff_id,
                    "name" => $staff_name ?? '',
                    "position" => $staff['job_name'] ?? '',
                    "department" => $staff['department_name'] ?? '',
                    "store_id" => $staff['organization_id'] ?? '',
                    "status" => $this->getTranslation()->_('send_request'),
                    "status_code" => 8,
                    "wait_time" => "",
                    "time" => $auditLog['created_at'],
                    "is_OK" => 0
                ];
            } else {
                if (in_array($auditLog['to_status_type'], [self::STATUS_APPROVAED_20, self::STATUS_APPROVAED_21, self::STATUS_APPROVAED_22, self::STATUS_APPROVAED_23])) {
                    $streams[] = [
                        "staff_id" => $staff_id,
                        "name" => $staff_name ?? '',
                        "position" => $staff['job_name'] ?? '',
                        "department" => $staff['department_name'] ?? '',
                        "store_id" => $staff['organization_id'] ?? '',
                        "status" => $this->getAutoApprovedStatus($auditLog['to_status_type']),
                        "status_code" => (int)$auditLog['to_status_type'],
                        "wait_time" => "",
                        "remark" => '',
                        "time" => $auditLog['created_at'],
                        "is_OK" => 0
                    ];
                } else {
                    $streams[] = [
                        "staff_id" => $staff_id,
                        "name" => $staff_name ?? '',
                        "position" => $staff['job_name'] ?? '',
                        "department" => $staff['department_name'] ?? '',
                        "store_id" => $staff['organization_id'] ?? '',
                        "status" => $this->auditlist->getAuditStatus((string)$this->approvalStatusMaps($auditLog['to_status_type'])),
                        "status_code" => (int)$auditLog['to_status_type'],
                        "wait_time" => "",
                        "remark" => in_array($auditLog['to_status_type'], [self::AUDIT_STATUS_4, self::AUDIT_STATUS_3]) ? $offerSignApproval['cancel_reason'] : '',
                        "time" => $auditLog['created_at'],
                        "is_OK" => 0
                    ];
                }
            }
        }
        if (!empty($list)) {
            if (isset($list[enums::$audit_list_status['panding_approval']]['approval_ids'])) {
                $approvalIds = explode(",", $list[enums::$audit_list_status['panding_approval']]['approval_ids']);

                $staff = (new StaffRepository())->getStaffpositionV2($approvalIds[0]);
                $time = strtotime(date('Y-m-d H:i:s')) - strtotime(show_time_zone($list[enums::$audit_list_status['panding_approval']]['created_at']));

                $staff_name = $staff['name'];
                $staff_id = $staff['id'];
                if(isCountry('ID')) {
                    $staff_name = $staff['id'] == '56780' ? '' : $staff['name'];
                    $staff_id = $staff['id'] == '56780' ? '' : $staff['id'];
                }

                $streams[] = [
                    "staff_id" => $staff_id,
                    "name" => $staff_name ?? '',
                    "position" => $staff['job_name'] ?? '',
                    "department" => $staff['department_name'] ?? '',
                    "store_id" => $staff['organization_id'] ?? '',
                    "status" => $this->auditlist->getAuditStatus(107),
                    "status_code" => 7,
                    "wait_time" => sprintf($this->getTranslation()->_("wait_time"), gmdate("H", $time), gmdate("i", $time)),
                    "time" => "",
                    "is_OK" => 0
                ];
            }
        }
        return $streams;

    }

    /**
     * 审批状态转换
     * @param $status
     * @return mixed
     */
    private function approvalStatusMaps($status)
    {
        $maps = [
            self::AUDIT_STATUS_0 => enums::$audit_list_status['revoked'],//offer签字撤销状态会有0这个特殊值
            self::AUDIT_STATUS_1 => enums::$audit_list_status['panding'],
            self::AUDIT_STATUS_2 => enums::$audit_list_status['approved_approval'],
            self::AUDIT_STATUS_3 => enums::$audit_list_status['dismissed'],
            self::AUDIT_STATUS_4 => enums::$audit_list_status['revoked'],
        ];

        return $maps[$status];
    }

    private function getAutoApprovedStatus($status)
    {
        $maps = [
            self::STATUS_APPROVAED_20 => $this->getTranslation()->_('auto_pandding_20'),
            self::STATUS_APPROVAED_21=> $this->getTranslation()->_('auto_pandding_21'),
            self::STATUS_APPROVAED_22=> $this->getTranslation()->_('auto_pandding_22'),
            self::STATUS_APPROVAED_23=> $this->getTranslation()->_('auto_pandding_22'),
        ];

        return isset($maps[$status]) ? $maps[$status] : '';
    }

    /**
     * 是否是一线员工职位
     * @param $job_id
     * @return int
     */
    public function isFrontLineJob($job_id)
    {
        $settingEnv = new SettingEnvServer();
        $front_line_jobids = $settingEnv->getSetVal('front_line_employee_jobids');
        if($front_line_jobids && in_array($job_id, explode(',',$front_line_jobids))){
            return 1;
        }

        return 0;
    }

    /**
     * 给offer签字发起人发送审批通过消息
     *
     * @param int $resume_id 简历ID
     * @param int $submitter_staff_id offer签字发起人ID
     *
     * @return bool
     */
    public function sendOfferSignApprovePassedMsg($resume_id,$submitter_staff_id)
    {
        try{
            if(empty($submitter_staff_id)){
                $this->getDI()->get('logger')->write_log("offer_sing_approve_msg发送消息失败:cvid-{$resume_id}的offer签字申请的发起人ID为空",'info');
                return ;
            }
            $resume_info = (new ResumeRepository($this->timezone))->getResumeInfoById($resume_id);
            if ($resume_info) {
                $resume_name = $resume_info->first_name .' '.$resume_info->last_name;
            }


            $current_lang = $this->lang;
            //获取offer签字审批发起人by上设置的语言
            $lang = (new StaffServer($this->lang,$this->timezone))->getLanguage($submitter_staff_id);

            $msg_title = $this->getTranslation($lang)->_('offersign_approve_passed_msg_title');
            $msg_content = $this->getTranslation($lang)->_('offersign_approve_passed_msg_content',['resume_id'=>$resume_id,'resume_name'=>$resume_name ?? '']);
            //组装站内信消息参数
            $msg_data = [
                "staff_users"       => [['id'=>$submitter_staff_id]],  //审批人ID
                "message_title"     => $msg_title, //标题
                "message_content"   => $msg_content,//内容
                "category"          => MessageEnums::MESSAGE_CATEGORY_OFFER_SIGN_APPROVAL,
            ];
            //拼接bi站内信消息接口
            $bi_rpc = (new ApiClient('bi_rpc','','add_kit_message', $this->lang));
            $bi_rpc->setParams($msg_data);
            $res = $bi_rpc->execute();

            $this->lang = $current_lang;// 将语言项设还原操作用户语言环境

            if ($res && $res['result']['code'] == 1) {
                $msg_id = $res['result']['data'][0];
                $this->getDI()->get('logger')->write_log("offer_sing_approve_msg发送消息成功:msg_id:{$msg_id},cvid:{$resume_id},msg_data:" . json_encode($msg_data),'info');
            }else{
                $this->getDI()->get('logger')->write_log("offer_sing_approve_msg发送消息失败:msg_data:" . json_encode($msg_data).',消息返回：'.json_encode($res),'info');
            }
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("offer_sing_approve_msg发送消息异常:".$e->getMessage(),'error');
        }
        return true;
    }
}
