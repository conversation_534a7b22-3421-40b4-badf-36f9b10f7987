<?php

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use Exception;

class VehicleCompanyInfoServer extends BaseServer
{
    public function __construct($lang, $timezone)
    {
        parent::__construct($lang);
    }

    public function getArdApiModule()
    {
        return 'vehiclecompanyinfo';
    }

    public function checkSpecialStore($staff_info_id)
    {
        $config = (new SettingEnvServer())->getSetVal('rent_car_store_staff_config');
        if (empty($config)) {
            return false;
        }
        $config = json_decode($config, true);

        foreach ($config as $store_id => $staffs) {
            if (in_array($staff_info_id, $staffs)) {
                return $store_id;
            }
        }
        return false;
    }


    /**
     * 获取入口权限和红点数
     * @param $staffInfo
     * @return array
     * @throws BusinessException
     */
    public function getWaitDealNum($staffInfo): array
    {
        $specialStoreId = $this->checkSpecialStore($staffInfo['staff_id']);

        $showInfo = $specialStoreId || array_intersect($staffInfo['positions'],
                [RolesModel::ROLE_HUB_SUPERVISOR, RolesModel::ROLE_DOT_ADMIN, RolesModel::ROLE_DC_OFFICE]);
        if ($showInfo) {
            $result = $this->sendRequest($this->getArdApiModule() . '.' . 'summary',
                ['store_id' => $specialStoreId ?: $staffInfo['organization_id']]);

            if (!empty($result['data'])) {
                $num = $result['data']['toSubmit'] + $result['data']['rejected'];
            }
        }
        return [
            'is_show' => $showInfo ? 1 : 0,
            'num'     => $num ?? 0,
        ];
    }


    /**
     * @param $action
     * @param $params
     * @return mixed
     * @throws BusinessException
     */
    public function sendRequest($action, $params)
    {
        if (!empty($params['staff_info_id'])) {
            $specialStoreId = $this->checkSpecialStore($params['staff_info_id']);
            if ($specialStoreId) {
                $params['store_id'] = $specialStoreId;
            }
            $params['is_special_store'] = !empty($specialStoreId);
        }
        
        $client = new ApiClient("ard_api", '', $action, $this->lang);
        $client->setParams($params);
        $res = $client->execute();

        if (isset($res['error'])) {
            throw new BusinessException($res['error']);
        }
        if (isset($res['result']['code']) && $res['result']['code'] != 1) {
            throw new BusinessException($res['result']['msg']);
        }
        return $res['result'];
    }

    /**
     * 网点运营 - 车辆维修申请 - 入口
     * @param array $staffInfo 当前登陆者信息组
     * @return bool
     */
    public function isShowVehicleRepairRequestPermission(array $staffInfo): bool
    {
        $settingEnvServer = new SettingEnvServer();
        //网点员工：角色为网点主管、网点仓管、接件员
        $positionData = $settingEnvServer->getSetVal('vehicle_repair_request_store_roles',',');
        if ($staffInfo['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_1'] && $staffInfo['positions'] && array_intersect($staffInfo['positions'], $positionData)) {
            return true;
        }
        return false;
    }

    /**
     * 网点运营 - 交车&验车 - 入口
     * @param array $staffInfo 当前登陆者信息组
     * @return bool
     */
    public function isShowVehicleDeliveryAndInspectionPermission(array $staffInfo): bool
    {
        $settingEnvServer = new SettingEnvServer();
        //网点员工：角色为接件员
        $positionData = $settingEnvServer->getSetVal('vehicle_delivery_and_inspection_store_roles', ',');
        if ($staffInfo['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_1'] && $staffInfo['positions'] && array_intersect($staffInfo['positions'], $positionData)) {
            return true;
        }
        return false;
    }

    /**
     *  网点运营 - 交车&验车 - 小红点数 = 展派指派给该员工的维修状态为【待交车】和【待验车车】的维修单和保养单数量
     * @param array $staffInfo 当前登陆者信息组
     * @return int|mixed
     */
    public function getDeliveryAndInspectionWaitDealNum(array $staffInfo)
    {
        $num = 0;
        $showInfo = $this->isShowVehicleDeliveryAndInspectionPermission($staffInfo);
        if (!$showInfo) {
            return $num;
        }
        try {
            $result = $this->sendRequest($this->getArdApiModule() . '.' . 'maintenanceApplyCount', ['staff_info_id' => $staffInfo['id'], 'store_id' => $staffInfo['organization_id']]);

            if (!empty($result['data'])) {
                $num = ($result['data']['send_apply_count'] ?? 0) + ($result['data']['maintenance_apply_count'] ?? 0) + ($result['data']['check_maintenance_apply_count'] ?? 0) + ($result['data']['check_apply_count'] ?? 0);
            }
        } catch (Exception $exception) {
            $this->logger->write_log('VehicleCompanyInfoServer getDeliveryAndInspectionWaitDealNum error ' . $exception->getMessage() .'-'. $exception->getTraceAsString() . $exception->getLine(), 'error');
        }
        return $num;
    }

    /**
     * 网点运营 - 车辆维修申请 - 枚举
     * @return int|mixed
     * @return array|mixed
     */
    public function getStaticData()
    {
        $data = [];
        if (!isCountry(['MY','PH'])){
            return $data;
        }
        try {
            $result = $this->sendRequest($this->getArdApiModule() . '.' . 'get_static_data', []);
            if (!empty($result['data'])) {
                $data = $result['data'];
            }
        } catch (Exception $exception) {
            $this->logger->write_log('VehicleCompanyInfoServer getStaticData error ' . $exception->getMessage() .'-'. $exception->getTraceAsString() . $exception->getLine(), 'error');
        }
        return $data;
    }
}
