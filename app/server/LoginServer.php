<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/7/3
 * Time: 下午4:39
 */

namespace FlashExpress\bi\App\Server;

use app\enums\LangEnums;
use Exception;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\MobileHelper;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\fle\DeviceKickedRecordModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Models\fle\StaffAccountRecordModel;
use FlashExpress\bi\App\Models\fle\StaffCancellationInfoModel;
use FlashExpress\bi\App\Repository\StaffDeviceInfoRepository;
use FlashExpress\bi\App\library\PasswordHash;
use FlashExpress\bi\App\Repository\StaffRepository;

class LoginServer extends BaseServer
{

    public $timezone;
    public $lang;
    public $sessionId;
    public $tid;
    public $staffInfo;
    public $lockPrefix   = 'lock_';
    public $lockErrorNum = 'lock_error_';
    public $sessionKey;
    protected $secretKey = 'rxhpQEiFdN6MrjZte9PbHsm0QsVBBzrZ';

    protected static $device_source = [
        'backyard' => 3,
    ];


    public function __construct($lang = '', $timezone = '+08:00')
    {
        $lang = $lang ?: getCountryDefaultLang();
        parent::__construct($lang);
    }


    /**
     * 根据token获取用户信息
     * @throws BusinessException|ValidationException
     */
    public function getStaffInfoBYToken($x_fle_session_id,$device_id)
    {
        //验证token
        $cache = $this->getDI()->get('redisLib');
        [$time, $paramAuth, $staff_id] = explode('_', $x_fle_session_id);
        $str  = $this->config->application->authenticate;
        $auth = sha1(md5($time . $str . $staff_id));
        if ($auth == $paramAuth && !empty($cache->get($auth))) {
            //新增 互踢验证
            $this->loginKick($staff_id, $device_id);
            return $this->staffCheck($staff_id);
        }
        http_response_code(422);
        throw new BusinessException('SessionID expired (re_login)', 100112);
    }


    //写入 员工设备信息表 insert 或 更新
    public function device_info_save($param)
    {
        //替换 设备源 为 对应id
        $key                     = strtolower($param['equipment_type']);
        $param['equipment_type'] = empty(self::$device_source[$key]) ? 1 : self::$device_source[$key];

        $re   = new StaffDeviceInfoRepository($this->lang);
        $info = $re->get_info($param['staff_info_id']);
        if (empty($info)) {
            $param['last_time'] = $param['current_time'] = gmdate('Y-m-d H:i:s', time());
            $param['last_ip']   = $param['current_ip'];
            $param['login_num'] = $info['login_num'] + 1;
            $flag               = $re->insert($param);
        } else {
            $param['id']           = $info['id'];
            $param['login_num']    = $info['login_num'] + 1;
            $param['current_time'] = gmdate('Y-m-d H:i:s', time());
            $param['last_time']    = $info['current_time'];
            $param['last_ip']      = $info['current_ip'];
            $flag                  = $re->update($param);
        }

        return $flag;
    }

    public function device_token_up($staff_id, $token, $channel)
    {
        if (empty($token)) {
            return false;
        }

        $re   = new StaffDeviceInfoRepository($this->lang);
        $info = $re->get_info($staff_id);
        $flag = false;
        if (!empty($info)) {
            $param['id']               = $info['id'];
            $param['device_token']     = $token;
            $param['download_channel'] = intval($channel);
            $flag                      = $re->update($param);
        }
        return $flag;
    }


    /**
     * @param $post
     * @return array
     * @throws ValidationException
     */
    public function verifyLogin($post): array
    {
        if (empty($post['staff_id'])) {
            throw new ValidationException('no such account');
        }
        $uid  = $post['staff_id'];
        //获取员工信息
        $info = $this->staffCheck($uid);
        if (empty($post['equipment_type']) || $post['equipment_type'] != 'backyard') {
            throw new ValidationException('EQUIPMENT-TYPE error');
        }

        //是否有锁
        $cache    = $this->getDI()->get('redisLib');
        $lock     = $cache->get($this->lockPrefix . $uid);
        $errorNum = $cache->get($this->lockErrorNum . $uid);
        //登录有锁 或者 超过4次 登录错误 锁1小时
        if (!empty($lock) || $errorNum > 4) {
            $seconds = $cache->ttl($this->lockPrefix . $uid);
            throw new ValidationException('Exceeded maximum login attempts，backyard is locked，' . $seconds . ' seconds left');
        }

        $hash = new PasswordHash(10, false);
        //验证密码
        $bool = $hash->checkPassword($post['password'], $info['encrypted_password']);
        if (!$bool) {
            //密码错误逻辑 线上环境 如果 超过4次 锁50分钟
            $cache->set($this->lockErrorNum . $uid, $errorNum + 1, 3600);
            $errorNum = $cache->get($this->lockErrorNum . $uid);
            if ($errorNum > 4) {
                $cache->set($this->lockPrefix . $uid, 1, 3600);
            }
            throw new ValidationException('wrong password', -108);
        }

        $cache->del($this->lockPrefix.$uid);
        $cache->del($this->lockErrorNum.$uid);
        $post['staff_info_name'] = $info['name'];
        $data_info = $this->crypt_token($post);
        $data_info['hire_type'] = (int)$info['hire_type'];
        //写 staff_account
        $this->saveAccount($post, time());
        return ['code' => 1, 'message' => 'success', 'data' => $data_info];
    }

    public function unlock($uid)
    {
        $cache = $this->getDI()->get('redisLib');
        $cache->del($this->lockPrefix . $uid);
        $cache->del($this->lockErrorNum . $uid);
        return true;
    }




    //账号验证

    /**
     * @throws ValidationException
     */
    public function staffCheck($staffId)
    {
        if (empty($staffId)) {
            throw new ValidationException('no such account');
        }

        //先查 fle
        $staff_model       = new StaffRepository($this->lang);
        $info              = $staff_model->login_info($staffId);
        $info['positions'] = explode(',', $info['position_category']);
        //找到不该用户
        if (empty($info)) {
            throw new ValidationException('no such account');
        }

        //员工已离职
        if ($info['state'] == HrStaffInfoModel::STATE_2) {
            http_response_code(422);
            throw new ValidationException('This account has been suspended',100112);
        } elseif ($info['state'] == HrStaffInfoModel::STATE_3) {
            http_response_code(422);
            throw new ValidationException('Non-working staff cannot log in the system', 100112);
        }

        //众包账号
        if ($info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_12) {
            throw new ValidationException('wrong hire type', 100382);
        }

        //已注销
        $cancel = StaffCancellationInfoModel::findFirst([
            'conditions' => 'staff_id = :staff_id:',
            'bind'       => ['staff_id' => $staffId],
        ]);
        if (!empty($cancel) && $cancel->state == StaffCancellationInfoModel::STATE_CANCEL) {
            throw new ValidationException('cancel staff can not login', 100203);
        }
        $staffServer          = new StaffServer();
        $staffInfo            = $staffServer->getStaffById($staffId, 'formal,is_sub_staff');
        $info['formal']       = intval($staffInfo['formal'] ?? 0);
        $info['is_sub_staff'] = intval($staffInfo['is_sub_staff'] ?? 0);
        //子账号
        if ($info['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            throw new ValidationException('sub staff can not login', 100329);
        }
        return $info;
    }

    /**
     * 登陆互踢验证
     * 您的账户已于{最新的update 时间} 在 设备名称 登录，
     * 您已经被强制下线，如果不是本人请重新登录并修改密码，以免信息泄露
     * @param $staffId
     * @param $clientId
     * @return bool
     * @throws ValidationException
     */
    public function loginKick($staffId, $clientId){
        if (empty($staffId)) {
            return true;
        }
        $t          = $this->getTranslation();
        $agentInfo  = MobileHelper::getUserAgent();
        $sourceType = $agentInfo['app_name'];

        $this->getDI()->get('logger')->write_log("loginKick {$staffId} {$clientId} {$sourceType}", 'info');
        $source = LangEnums::$equipment_type[$sourceType] ?? 0;
        //非by kit来源
        if (empty($source)) {
            return true;
        }
        if (!in_array($source, [LangEnums::$equipment_type['kit'], LangEnums::$equipment_type['backyard']])) {
            return true;
        }
        $accountInfo = StaffAccountModel::findFirst([
            'conditions' => "staff_info_id = :staff_id: and equipment_type = :equipment_type:",
            'bind'       => [
                'staff_id'       => $staffId,
                'equipment_type' => $source,
            ],
        ]);

        if (empty($accountInfo)) {
            http_response_code(422);
            throw new ValidationException($t->_('login_timeout'), 100112);
        }
        //版本开关 如果是旧版本 不验证
        $versionFlag = $this->kickVersionCheck();
        if ($versionFlag === false) {
            return true;
        }
        //踢了
        if ($accountInfo->clientid != $clientId) {
            //设备互踢 code  返回 非200  http
            http_response_code(422);
            $deviceName = empty($accountInfo->device_model) ? $t->_('other_device') : $accountInfo->device_model;
            $time       = show_time_zone($accountInfo->currenttime);
            throw new ValidationException($t->_('login_kick_notice', ['kick_time' => $time, 'device_name' => $deviceName]), 402026);
        }
        return true;
    }

    /**
     * 对比用户版本是否是新版本。
     * 如果是新版本 返回true  如果是旧版本 返回 false
     * @return bool
     */
    public function kickVersionCheck()
    {
        $equipment_info = [];
        $set_model = SettingEnvModel::findFirst("code = 'kick_version_check' ");
        if (!empty($set_model) && $set_model->set_val) {
            $equipment_info = json_decode($set_model->set_val, true);
        }
        if(!$equipment_info) {
            return false;
        }

        return MobileHelper::compareVersion($equipment_info);
    }


    /**
     * 认证后 用户信息 并写 员工设备表
     * @param $info
     * @return array
     */
    public function crypt_token($info)
    {
        $cache_time = 7 * 24 * 3600;//7天
        $refresh_time = 4 * 24 * 3600;//4天刷新session
        //生成token
        $str              = $this->config->application->authenticate;
        $time             = time();
        $this->sessionKey = enums::$sessionPrefix;
        $staff_id         = $info['staff_id'];
        $auth             = sha1(md5($time . $str . $staff_id));
        $this->sessionId = "{$time}_{$auth}_{$staff_id}";//返回伪session
        //随便生成一个 目前没用
        $this->tid = $this->crypt_tid($str, $info['clientid']);

        $cache = $this->getDI()->get('redisLib');
        $user  = [
            'sessionid'             => $this->sessionId,
            'tid'                   => $this->tid,
            'current_time'          => $time,
            'refresh_session_time'  => $time + $refresh_time,
            'field_punch'           => false,
            'staff_info_avatar_url' => null,
            'staff_info_id'         => intval($staff_id),
            'staff_info_name'       => $info['staff_info_name'],
            'store_lat'             => floatval($info['store_lat']??0),
            'store_lng'             => floatval($info['store_lng']??0),
            'wage_display'          => true,
        ];

        $cache->set($auth, json_encode($user), $cache_time);
        return $user;
    }


    /**
     * @throws ValidationException
     */
    public function reLogin($param)
    {
        //获取设备
        $this->sessionId = $param['sessionid'];
        $this->tid       = $param['tid'];
        $clientId        = $param['clientid'];
        //验证 session
        $cache = $this->getDI()->get('redisLib');

        $this->sessionKey = enums::$sessionPrefix;

        if (empty($this->sessionId) || empty($this->tid)) {
            throw new ValidationException('fail', -1);
        }
        $str = $this->config->application->authenticate;
        //验证单点 不同设备登陆 只有一个有效
        $check_tid = $this->crypt_tid($str, $clientId);
        if ($this->tid != $check_tid) {
            throw new ValidationException('device changed', 100111);
        }

        [$time, $paramAuth, $staff_id] = explode('_', $this->sessionId);
        //登陆设备踢出
        $this->loginKick($staff_id, $clientId);
        //验证账号信息
        $info = $this->staffCheck($staff_id);
        $auth = sha1(md5($time . $str . $staff_id));
        if ($auth != $paramAuth) {
            http_response_code(422);
            throw new ValidationException('SessionID expired (re_login)', 100112);
        }
        $this->sessionKey = enums::$sessionPrefix;
        $res              = $cache->get($auth);
        //验证后获取缓存信息
        if (empty($res)) {
            http_response_code(422);
            throw new ValidationException('SessionID expired (re_login)', 100112);
        }
        $param['staff_id'] = $staff_id;

        //避免频繁刷新session
        $currentTime              = time();
        $sessionInfo              = json_decode($res, true);
        $param['staff_info_name'] = $sessionInfo['staff_info_name'];
        if ($currentTime > $sessionInfo['refresh_session_time']) {
            $return = $this->crypt_token($param);
        } else {
            $return = $sessionInfo;
        }
        $return['hire_type'] = (int) $info['hire_type'];
        header('uid:' . $param['staff_id']);
        //保存设备信息
        $this->saveAccount($param, $currentTime);
        $data_list['code']    = 1;
        $data_list['message'] = 'success';
        $data_list['data']    = $return;
        //更新当前时间戳
        $data_list['data']['current_time'] = $currentTime;
        $this->getDI()->get('logger')->write_log($data_list, 'info');
        return $data_list;
    }


    /**
     * @param $key --私钥密码
     * @param $client_id --设备码 089A386A-E54F-4899-B1F5-1C0C19854EE7
     * @param string $equipment_type 设备源
     * @return string
     */
    protected function crypt_tid($key, $client_id, string $equipment_type = 'backyard'): string
    {
        $str = $client_id . $key . $equipment_type;
        return hash('sha256', $str);
    }

    public function saveAccount($info, $time)
    {
        $dateTime = gmdate('Y-m-d H:i:s', $time);
        $staff_id = $info['staff_id'];

        $accountInfo = StaffAccountModel::findFirst([
            'conditions' => "staff_info_id = :staff_id: and equipment_type = :type:",
            'bind'       => [
                'staff_id' => $staff_id,
                'type'     => StaffAccountModel::$equipment[$info['equipment_type']] ?? 0,
            ],
        ]);
        if (empty($accountInfo)) {
            $accountModel              = new StaffAccountModel();
            $insert['staff_info_id']   = $staff_id;
            $insert['serversd']        = $time;
            $insert['clientsd']        = $info['clientsd'] ?? '';
            $insert['clientid']        = $info['clientid'] ?? '';
            $insert['equipment_type']  = StaffAccountModel::$equipment[$info['equipment_type']] ?? 0;
            $insert['tid']             = $this->tid;
            $insert['session_id']      = $this->sessionId;
            $insert['lasttime']        = $dateTime;
            $insert['currenttime']     = $dateTime;
            $insert['current_ip']      = $this->request->getServer('REMOTE_ADDR');
            $insert['os']              = $info['os'];
            $insert['version']         = $info['version'];
            $insert['accept_language'] = $this->lang;
            $insert['device_model']    = $info['device_model'] ?? '';
            $accountModel->create($insert);
        } else {
            $accountInfo->serversd        = $time;
            $accountInfo->clientsd        = $info['clientsd'] ?? '';
            $accountInfo->clientid        = $info['clientid'] ?? '';
            $accountInfo->equipment_type  = StaffAccountModel::$equipment[$info['equipment_type']] ?? 0;
            $accountInfo->tid             = $this->tid;
            $accountInfo->sessionid       = $this->sessionId;
            $accountInfo->lasttime        = $accountInfo->currenttime;
            $accountInfo->currenttime     = $dateTime;
            $accountInfo->lastip          = $accountInfo->currentip;
            $accountInfo->currentip       = $this->request->getServer('REMOTE_ADDR');
            $accountInfo->os              = $info['os'];
            $accountInfo->version         = $info['version'];
            $accountInfo->accept_language = $this->lang;
            $accountInfo->device_model    = $info['device_model'] ?? '';
            $accountInfo->update();
        }
        //写staff_account_record 没什么用 不写了
        $recordInfo = StaffAccountRecordModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and clientid = :clientid: and equipment_type = :equipment_type:',
            'bind'       => [
                'staff_id'       => $staff_id,
                'clientid'       => $info['clientid'],
                'equipment_type' => StaffAccountModel::FB_BACKYARD_CODE,
            ],
        ]);

        if (empty($recordInfo)) {
            $recordModel                  = new StaffAccountRecordModel();
            $re_insert['staff_info_id']   = $staff_id;
            $re_insert['serversd']        = $time;
            $re_insert['clientsd']        = $info['clientsd'] ?? '';
            $re_insert['clientid']        = $info['clientid'] ?? '';
            $re_insert['equipment_type']  = StaffAccountModel::$equipment[$info['equipment_type']] ?? 0;
            $re_insert['tid']             = $this->tid;
            $re_insert['sessionid']       = $this->sessionId;
            $re_insert['currenttime']     = $dateTime;
            $re_insert['currentip']       = $this->request->getServer('REMOTE_ADDR');
            $re_insert['os']              = $info['os'];
            $re_insert['version']         = $info['version'];
            $re_insert['device_model']    = $info['device_model'] ?? '';
            $re_insert['device_sn_code']  = $info['device_sn_code'] ?? '';
            $re_insert['device_imei']     = $info['device_imei'] ?? '';
            $re_insert['device_sn']       = $info['device_sn'] ?? '';
            $re_insert['accept_language'] = $this->lang;
            $recordModel->create($re_insert);
        } else {
            $recordInfo->serversd        = $time;
            $recordInfo->clientsd        = $info['clientsd'] ?? '';
            $recordInfo->clientid        = $info['clientid'] ?? '';
            $recordInfo->tid             = $this->tid;
            $recordInfo->sessionid       = $this->sessionId;
            $recordInfo->currenttime     = $dateTime;
            $recordInfo->currentip       = $this->request->getServer('REMOTE_ADDR');
            $recordInfo->os              = $info['os'];
            $recordInfo->version         = $info['version'];
            $recordInfo->device_model    = $info['device_model'] ?? '';
            $recordInfo->device_sn_code  = $info['device_sn_code'] ?? '';
            $recordInfo->device_imei     = $info['device_imei'] ?? '';
            $recordInfo->device_sn       = $info['device_sn'] ?? '';
            $recordInfo->accept_language = $this->lang;
            $recordInfo->update();
        }
    }

    public function sign_out($param)
    {
        $staffId = $param['staff_id'];
        StaffAccountModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and equipment_type = :equipment_type:',
            'bind'       => [
                'staff_id'       => $staffId,
                'equipment_type' => StaffAccountModel::FB_BACKYARD_CODE,
            ],
        ])->update(['device_token' => null]);
        return true;
    }

    /**
     * 上传设备信息
     * @param $param
     */
    public function tokenUploadFle($param)
    {
        $this->tid = $param['tid'];
        $str       = $this->config->application->authenticate;
        //验证单点 不同设备登陆 只有一个有效
        $check_tid = $this->crypt_tid($str, $param['clientid']);
        if ($this->tid != $check_tid) {
            throw new ValidationException('device changed', 100111);
        }
        $accountInfo = StaffAccountModel::findFirst([
            'conditions' => "staff_info_id = :staff_id: and equipment_type = :type:",
            'bind'       => [
                'staff_id' => $param['staff_id'],
                'type'     => StaffAccountModel::FB_BACKYARD_CODE,
            ],
        ]);

        if (empty($accountInfo)) {
            throw new ValidationException('staff info error', 100111);
        }

        $accountInfo->os               = $param['os'];
        $accountInfo->device_token     = $param['device_token'];
        $accountInfo->download_channel = intval($param['download_channel']) ?? 0;
        $accountInfo->update();
        return ['code' => 1, 'message' => 'success', 'data' => null];
    }

    //注销
    public function cancelAccount($param)
    {
        $info = StaffCancellationInfoModel::findFirst([
            'conditions' => 'staff_id = :staff_id:',
            'bind'       => ['staff_id' => $param['staff_id']],
        ]);

        if (empty($info)) {
            $model              = new StaffCancellationInfoModel();
            $insert['staff_id'] = $param['staff_id'];
            $insert['state']    = StaffCancellationInfoModel::STATE_CANCEL;
            $model->create($insert);
        } else {
            $info->state = StaffCancellationInfoModel::STATE_CANCEL;
            $info->update();
        }

        //清除 session
        [$time, $paramAuth, $staff_id] = explode('_', $param['session_id']);
//        $this->sessionKey = enums::$sessionPrefix;
//        $cache_key = md5($this->sessionKey.$param['staff_id']);//缓存用户信息 键
        $cache_key = md5($paramAuth);
        $cache     = $this->getDI()->get('redisLib');
        $res       = $cache->get($cache_key);
        $cache->delete($cache_key);
        return ['code' => 1, 'message' => 'success', 'data' => null];
    }

    /**
     * @throws BusinessException
     * @throws Exception
     */
    public function getOALoginInfo($staff_id)
    {
        $key = (new SettingEnvServer())->getSetVal('oa_login_private_key');
        $params = [
            'staff_id'  => strval($staff_id),
            'platform'  => self::$flePlatform,
            'timestamp' => strval(time()),
        ];
        $params['sign'] = buildRequestParamFun($params, $key, true);
        $fle_rpc = (new ApiClient('oa_rpc', '', 'access_authentication', $this->lang));
        $fle_rpc->setParams($params);
        $res = $fle_rpc->execute();
        if (!empty($res['result'])) {
            if ($res['result']['code'] == 1) {
                return $res['result']['data'];
            }
            throw new BusinessException($res['result']['message']);
        }
        if (isset($res['error'])) {
            throw new BusinessException($res['error']);
        }
        throw new Exception('调用OA登录接口异常');
    }

    /**
     * 登录hcm系统临时URL
     * @param $staff_id
     * @return mixed
     * @throws BusinessException|Exception
     */
    public function getHCMLoginURL($staff_id)
    {
        $params = [
            'staff_info_id'  => strval($staff_id),
        ];
        $fle_rpc = (new ApiClient('hcm_rpc', '', 'ticket_login_url', $this->lang));
        $fle_rpc->setParams($params);
        $res = $fle_rpc->execute();
        if (isset($res['error'])) {
            throw new BusinessException($res['error']);
        }
        if (!empty($res['result'])) {
            return $res['result'];
        }
        throw new Exception('调用HCM登录接口异常');
    }

    /**
     * 停职token
     * @throws ValidationException
     */
    public function checkSuspendTokenToFau($x_fle_session_id): array
    {
        //停职用户登录后
        $fau    = new RestClient('fau');
        $result = $fau->execute(RestClient::METHOD_GET, '/svc/staff/get_staff_info_by_token',
            ['token' => $x_fle_session_id]);
        if (empty($result)) {
            http_response_code(422);
            throw new ValidationException('fau sys error', 100112);
        }
        if ($result['code'] == 0 || empty($result['data'])) {
            http_response_code(422);
            throw new ValidationException($result['message'], 100112);
        }
        return [
            'result' => ['id' => $result['data']['staffInfoId'], 'name' => $result['data']['name']],
        ];
    }

    /**
     * 取java验证token的有效性
     * @throws ValidationException
     */
    public function checkStaffTokenToJava($x_fle_session_id,$device_id, $equipment_type)
    {
        $redis  = $this->getDI()->get('redisLib');

        [$time, $auth, $staff_id] = explode('_', $x_fle_session_id);

        if (empty($staff_id)) {
            http_response_code(422);
            throw new ValidationException('session error', 100112);
        }
        //测试环境跳过java验证
        if (RUNTIME == 'dev' && env('break_away_from_ms', 0)) {
            $res['result'] = $this->staffCheck($staff_id);
            return $res;
        }

        $cacheKey  = sprintf(RedisEnums::SYS_STAFF_SESSION_KEY, $staff_id);
        $staffInfo  = $redis->get($cacheKey);

        if(!empty($staffInfo) && RUNTIME == 'pro'){
            return json_decode($staffInfo,true);
        }

        $params = ['token' => $x_fle_session_id, 'device_id' => $device_id, 'equipment_type' => $equipment_type];
        $api    = new RestClient('nws');
        $res    = $api->execute(RestClient::METHOD_POST, '/svc/staff/get_staff_by_token',
            $params, ['Accept-Language' => $this->lang]);
        if (!isset($res['code'])) {
            http_response_code(422);
            throw new ValidationException($this->getTranslation()->_('login_timeout'), 100112);
        }
        if ($res['code'] != 1) {
            //如果是旧版本 统一返回 100112
            $versionFlag = $this->kickVersionCheck();
            if($versionFlag === false){
                http_response_code(422);
                throw new ValidationException($this->getTranslation()->_('login_timeout'), 100112);
            }
            //设备互踢 code  返回 非200  http
            if($res['code'] == 402026 || $res['code'] == 100112){
                http_response_code(422);
            }
            throw new ValidationException($res['message'], intval($res['code']));
        }
        $data_list['result']                 = $res['data'];
        $staffServer                         = new StaffServer();
        $staffInfo                           = $staffServer->getStaffById($staff_id, 'formal,is_sub_staff');
        $data_list['result']['formal']       = intval($staffInfo['formal'] ?? 0);
        $data_list['result']['is_sub_staff'] = intval($staffInfo['is_sub_staff'] ?? 0);
        $redis->set($cacheKey, json_encode($data_list, JSON_UNESCAPED_UNICODE), 300);
        return $data_list;
    }

    /**
     * 清除session缓存
     * @param $staff_id
     * @return mixed
     */
    public function deleteStaffSessionCache($staff_id)
    {
        $redis    = $this->getDI()->get('redisLib');
        $cacheKey = sprintf(RedisEnums::SYS_STAFF_SESSION_KEY, $staff_id);
        return $redis->del($cacheKey);
    }

    //其他设备登陆踢出 验证签名
    public function checkSign($param)
    {
        $sign = $param['sign'];
        if (empty($sign)) {
            throw new ValidationException('sign error');
        }
        if (empty($param['login'])) {
            throw new ValidationException('staff id error');
        }

        $explode      = explode('_', $param['sign']);
        $paramToken = end($explode);
        $stamp      = $explode[0];

        //验证签名
        $str_token = "POST" . "_" . "/auth/record" . "_" . $stamp . "_" . $param['login'] . $param['device_id'] . $param['type'];
        $secretKey = $this->secretKey;

        $t     = hash_hmac("sha256", $str_token, $secretKey, true);
        $token = base64_encode($t);
        //验证签名
        if ($paramToken != $token) {
            throw new ValidationException('sign is not match');
        }

        //查询踢掉本设备的 那个设备信息 和名称 staff account
        $equipment_type = LangEnums::$equipment_type[$param['equipment_type'] ?? 0] ?? '';
        if (empty($equipment_type)) {
            throw new ValidationException('equipment type error');
        }
        $accountInfo = StaffAccountModel::findFirst([
            'conditions' => "staff_info_id = :staff_id: and equipment_type = :equipment_type:",
            'bind'       => [
                'staff_id'       => $param['login'],
                'equipment_type' => $equipment_type,
            ],
        ]);

        if (empty($accountInfo)) {
            $this->getDI()->get('logger')->write_log("checkSign {$param['login']} 设备信息 无记录", 'info');
        }

        //保存 fle  表 device_kicked_record
        $model                     = new DeviceKickedRecordModel();
        $insert['staff_info_id']   = $param['login'];
        $insert['type']            = $param['type'];
        $insert['equipment_type']  = $equipment_type;
        $insert['device_id']       = $accountInfo->clientid ?? '';
        $insert['device_name']     = $accountInfo->device_model ?? '';
        $insert['pre_device_id']   = $param['device_id'];
        $insert['pre_device_name'] = $param['device_name'];
        $model->create($insert);
        return self::checkReturn(['data' => null]);
    }



}