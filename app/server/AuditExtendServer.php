<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/7/28
 * Time: 下午9:00
 *
 * audit 扩展 server
 */


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftOperateLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffPublicHolidayModel;
use FlashExpress\bi\App\Repository\StaffRepository;

class AuditExtendServer extends BaseServer
{

    public $timezone;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }



    //主管撤销 休息日请假 同时还原 原来的轮休配置

    /**
     * @throws BusinessException
     */
    public function cancel_for_leave($param)
    {
        if (isCountry('MY')) {
            //需要被撤销的休息日假期 是发生调休中的被调休息日 不能操作撤销
            $srcDateFind = HrStaffWorkDayModel::findFirst([
                'conditions' => 'staff_info_id= :staff_info_id: and src_date = :src_date:',
                'bind'       => [
                    'src_date'      => $param['date_at'],
                    'staff_info_id' => $param['staff_info_id'],
                ],
            ]);
            if ($srcDateFind) {
                throw new BusinessException($this->getTranslation()->_('cancel_off_day_error_2',
                    ['date' => $srcDateFind->date_at]));
            }
        }
        //先查询 取消那天 有没有被设置轮休
        $offDayInfo = HrStaffWorkDayModel::findFirst([
            'conditions' => 'staff_info_id= :staff_info_id: and date_at = :date_at: ',
            'bind'       => [
                'date_at'       => $param['date_at'],
                'staff_info_id' => $param['staff_info_id'],
            ],
        ]);

        if (empty($offDayInfo)) {
            return;
        }
        //取备份周的 轮休
        $start  = weekStart($param['date_at']);
        $end    = weekEnd($param['date_at']);
        $backup = $this->get_backup_workday($param['staff_info_id'], $start, $end);

        //先删撤销当天的轮休 然后 还原备份的轮休
        $return_info = [];
        if (!empty($backup)) {
            $return_info = $backup[0];
        }

        if ($return_info) {
            //批量导入的调休 撤销的话不做恢复replace表的动作
            if ($offDayInfo->src_week) {
                $return_info = [];
            } else {
                $isset = HrStaffWorkDayModel::findFirst([
                    'conditions' => 'staff_info_id= :staff_info_id: and date_at = :date_at: and date_at != :not_date_at:',
                    'bind'       => [
                        'date_at'       => $return_info['date_at'],
                        'not_date_at'   => $param['date_at'],
                        'staff_info_id' => $return_info['staff_info_id'],
                    ],
                ]);
                if ($isset) {
                    throw new BusinessException($this->getTranslation()->_('cancel_off_day_error_1',
                        ['current_date' => $param['date_at'], 'source_date' => $return_info['date_at']]));
                }
            }
        }
        $flag = $this->return_workday($param['staff_info_id'], $param['date_at'], $return_info,$offDayInfo);

        //新增日志（del）
        $logServer = new WorkdayServer($this->lang, $this->timezone);
        if(empty($param['operate_id'])){
            $param['operate_id'] = $param['staff_info_id'];
        }
        $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_CANCEL_OFF, $param);
        //新增日志（add）
        if(!empty($return_info)){//上面有可能给空
            $logServer        = new WorkdayServer($this->lang, $this->timezone);
            $param['date_at'] = $return_info['date_at'];
            $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_ADD_OFF, $param);
        }

        $this->getDI()->get('logger')->write_log("cancel_leave_15 还原轮休 {$param['staff_info_id']} {$param['date_at']} " . json_encode($return_info),
            'info');

        return $flag;
    }

    //获取 轮休备份表区间数据 撤销请假时候用
    public function get_backup_workday($staff_id, $start, $end){

        $sql = " select * from work_day_replace where staff_info_id = {$staff_id} and date_at >= '{$start}' and date_at <= '{$end}'";
        $data = $this->getDI()->get('db_rby')->query($sql);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }


    //撤销请假操作 删除对应日期的轮休 并还原 当周的原配置 并删除备份记录
    public function return_workday($staff_id, $del_date, $return_info, $offDayInfo)
    {
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            //删除 撤销日期的轮休
            $del_sql = " delete from hr_staff_work_days where staff_info_id = {$staff_id} and date_at = '{$del_date}'";
            $db->execute($del_sql);

            if (isCountry('VN') || isCountry('LA')) {
                //删除 撤销日期的轮休
                $del_sql = " delete from hr_staff_work_days where staff_info_id = {$staff_id} and src_date = '{$del_date}'";
                $db->execute($del_sql);
            }
            if (isCountry('TH') || isCountry('MY') || isCountry('ID')) {
                //删除关联的补的PH
                $del_ph_sql = " update staff_public_holiday set is_deleted = 1,remark ='cancel off day leave'  where staff_info_id = {$staff_id} and src_date = '{$del_date}'";
                $db->execute($del_ph_sql);
            }

            if (!empty($return_info)) {
                //删除 备份轮休 并把该记录还原 轮休表
                $del_back_sql = " delete from work_day_replace where staff_info_id = {$staff_id} and date_at = '{$return_info['date_at']}'";
                $db->execute($del_back_sql);

                unset($return_info['updated_at']);
                $db->insertAsDict('hr_staff_work_days', $return_info);
            }
            if (isCountry('MY') && $offDayInfo->src_date && ($return_info['date_at'] ?? '') != $offDayInfo->src_date) {
                $insertData = [
                    'staff_info_id' => $staff_id,
                    'month'         => date('Y-m', strtotime($offDayInfo->src_date)),
                    'date_at'       => $offDayInfo->src_date,
                    'operator'      => 10000,
                    'type'          => HrStaffWorkDayModel::TYPE_REST,
                    'remark'        => 'compensatory off cancel',
                ];
                $db->insertAsDict('hr_staff_work_days', $insertData);
                $staffAuditModel = (new StaffAuditModel())->getSource();
                $db->execute("update {$staffAuditModel}  set status=:s,audit_reason=replace(audit_reason,'|compensatory off','')  where staff_info_id=:staff_info_id and status=4 and leave_type=15 and  date(leave_start_time)=:start and date(leave_end_time)=:end and audit_reason like '%compensatory off%'",
                    ['s' => enums::APPROVAL_STATUS_APPROVAL,'staff_info_id' => $staff_id,'start' =>$offDayInfo->src_date,'end'=>$offDayInfo->src_date]);

                //恢复对应的补的ph 和 新增ph对应 off
                $staffHoliday = StaffPublicHolidayModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and src_date = :src_date:',
                    'bind' => ['staff_id'=>$staff_id,'src_date'=>$offDayInfo->src_date]
                ]);
                if($staffHoliday){
                    $staffHoliday->update([
                        'is_deleted' => 0,
                        'remark' => 'compensatory off cancel'
                    ]);
                    //给补的ph 加off
                    $this->addOffForPh($staff_id, $staffHoliday);
                }
//                $db->execute("update staff_public_holiday set is_deleted=0,remark='compensatory off cancel' where staff_info_id=:staff_id and src_date=:src_date",['staff_id'=>$staff_id,'src_date'=>$offDayInfo->src_date]);
                $this->getDI()->get('logger')->write_log($insertData, 'info');
            }
            if(isCountry('MY') && $offDayInfo->src_week){
                $staffInfo      = (new StaffRepository())->getStaffPosition($staff_id);
                $lang = (new StaffServer)->getLanguage($staff_id);
                $t                                   = $this->getTranslation($lang);

                if (!empty($offDayInfo->src_date)) {
                    $title   = $t->_('work_day_title_adjustment');//您的休息日已调整!
                    $content = $t->_('work_day_content_adjustment', [
                        'staff_name'      => $staffInfo['name'],
                        'staff_info_id'   => $staff_id,
                        'staff_job_title' => $staffInfo['job_name'],
                        'before_date'     => $del_date,
                        'after_date'      => $offDayInfo->src_date,
                    ]);
                } else {
                    $title   = $t->_('work_day_title_cancel');//您的休息日已取消!
                    $content = $t->_('work_day_content_cancel', [
                        'staff_name'      => $staffInfo['name'],
                        'staff_info_id'   => $staff_id,
                        'staff_job_title' => $staffInfo['job_name'],
                        'staff_date'      => $del_date,
                    ]);
                }
                $content    = addslashes("<div style='font-size: 30px'>".$content.'</div>');

                $message_param['staff_users']        = [$staff_id];
                $message_param['message_title']      = $title;
                $message_param['message_content']    = $content;
                $message_param['staff_info_ids_str'] = $staff_id;
                $message_param['id']                 = time() . $staff_id . rand(1000000, 9999999);;
                $message_param['category'] = -1;
                $bi_rpc                    = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
                $bi_rpc->setParams($message_param);
                $bi_rpc->execute();
            }

            $db->commit();
            return true;
        } catch (\Exception $e) {
            $db->rollback();
            return false;
        }
    }


    public function addOffForPh($staff_id, StaffPublicHolidayModel $holidayInfo){
        //先检查有没有
        $info = HrStaffWorkDayModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and date_at = :date_at:',
            'bind' => ['staff_id' => $staff_id,'date_at' => $holidayInfo->date_at]
        ]);
        if($info){
            return true;
        }

        //没有 off  新增
        $workRow['staff_info_id'] = $staff_id;
        $workRow['month'] = date('Y-m',strtotime($holidayInfo->date_at));
        $workRow['date_at'] = $holidayInfo->date_at;
        $workRow['operator'] = 10000;
        $workRow['type'] = HrStaffWorkDayModel::TYPE_REST;
        $workRow['staff_ph_id'] = $holidayInfo->id;
        $workRow['remark'] = HrStaffWorkDayModel::REMARK_FOR_DEFAULT_HOLIDAY;
        $model = new HrStaffWorkDayModel();
        return $model->create($workRow);
    }


}