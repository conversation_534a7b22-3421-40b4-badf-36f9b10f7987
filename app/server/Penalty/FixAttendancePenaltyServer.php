<?php


namespace FlashExpress\bi\App\Server\Penalty;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrPenaltyAppealModel;
use FlashExpress\bi\App\Models\backyard\HrPenaltyDetailModel;
use FlashExpress\bi\App\Repository\StaffMessageRepository;
use FlashExpress\bi\App\Server\ApprovalServer;

/**
 * 考勤修正后
 * 处罚需要做的动作
 */
class FixAttendancePenaltyServer extends BasePenaltyServer
{

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
    }

    public $initDataObj;

    /**
     *
     */
    public function initData($initData): FixAttendancePenaltyServer
    {
        $this->initDataObj = $initData;
        return $this;
    }

    /**
     * 考勤修正
     * @return bool
     * @throws BusinessException
     */
    public function handle(): bool
    {
        //不需要出勤(是休息日 、 ph 、请假整天)
        $not_need_online = ($this->initDataObj->is_off_or_ph || (!is_null($this->initDataObj->leave_type) && $this->initDataObj->leave_type == 0));
        $penalty_num     = $this->initDataObj->penalty_detail->count();
        //不需要出勤 && 没有处罚数据 直接退出
        if ($not_need_online && empty($penalty_num)) {
            return true;
        }

        //不需要出勤  但是有处罚数据  需要将处罚数据清除
        if ($not_need_online && !empty($penalty_num)) {
            $this->cancelPenalty();
            return true;
        }

        //需要出勤  撤销已有处罚，重新生成新的处罚
        if (!$not_need_online) {
            try {
                (new PunchInPenaltyServer($this->lang, $this->timeZone))->initData($this->initDataObj)->handle();
            } catch (BusinessException $e) {
                echo $e->getMessage();
            }
            try {
                (new PunchOutPenaltyServer($this->lang, $this->timeZone))->initData($this->initDataObj)->handle();
            } catch (BusinessException $e) {
                echo $e->getMessage();
            }
            try {
                (new AbsentPenaltyServer($this->lang, $this->timeZone))->initData($this->initDataObj)->handle();
            } catch (BusinessException $e) {
                echo $e->getMessage();
            }
            return true;
        }
        return true;
    }


}