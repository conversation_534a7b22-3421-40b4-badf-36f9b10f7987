<?php


namespace FlashExpress\bi\App\Server\Penalty;

use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrPenaltyAppealModel;
use FlashExpress\bi\App\Models\backyard\HrPenaltyDetailModel;
use FlashExpress\bi\App\Repository\StaffMessageRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\BaseServer;


class BasePenaltyServer extends BaseServer
{

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
    }

    public $initDataObj = null;

    const SRC_PUNCH_IN = 1;      //上班
    const SRC_PUNCH_OUT = 2;     //下班
    const SRC_ABSENT = 3;        //旷工
    const SRC_MAKE_UP = 4;       //补卡
    const SRC_ADD_LEAVE = 5;     //请假
    const SRC_EDIT_SHIFT = 6;     //修改班次

    public function initData($initData)
    {
        $this->initDataObj = $initData;
        return $this;
    }

    public function handle()
    {
    }

    public function checkIsNeed(): bool
    {
        if (in_array($this->initDataObj->staff_state, [2, 3])) {
            return false;
        }
        $not_online = ($this->initDataObj->is_off_or_ph || (!is_null($this->initDataObj->leave_type) && $this->initDataObj->leave_type == 0));
        if ($not_online) {
            return false;
        }

        return true;
    }


    /**
     * 生成处罚数据
     * @throws BusinessException
     * @throws Exception
     */
    public function createPenalty($bind, $data)
    {
        $penaltyModel = HrPenaltyDetailModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and attendance_date = :attendance_date: and penalty_reason = :penalty_reason:",
            'bind'       => $bind,
            'order'      => 'id desc',
        ]);

        if ($penaltyModel && ($penaltyModel->state == HrPenaltyDetailModel::PENALTY_STATE_TAKE_EFFECT || $penaltyModel->expiry_reason == HrPenaltyDetailModel::EXPIRY_REASON_HAS_APPEAL)) {
            throw new BusinessException(' 已存在 '.json_encode([$bind, $data], JSON_UNESCAPED_UNICODE));
        }
        $model = new HrPenaltyDetailModel();
        foreach ($data as $field => $value) {
            $model->$field = $value;
        }
        if ($model->save()) {
            return $model->id;
        }
        throw new Exception('save error '.json_encode([$bind, $data], JSON_UNESCAPED_UNICODE));
    }


    /**
     * 发送消息
     * @param $data
     * @return bool
     */
    public function sendMessage($data): bool
    {
        $fle_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang));
        $fle_rpc->setParams($data);
        $fle_return = $fle_rpc->execute();//响应
        return !isset($fle_return['error']);
    }

    public function dealPenaltyInfo($params): array
    {
        $time_max = in_array($this->initDataObj->leave_type, [1, 2]) ? 4 : 8;
        $money_max = in_array($this->initDataObj->leave_type, [1, 2]) ? 250 : 500;

        $penaltyInfo = [
            'early_late_ab_hour' => 0,
            'penalty_money'      => 0,
        ];
        $minutes     = $params / 60;
        echo '考勤异常分钟:'.$minutes.PHP_EOL;
        if ($minutes < 1) {
            return $penaltyInfo;
        } elseif ($minutes < 31) {
            $penaltyInfo['early_late_ab_hour'] = 0.5;
        } elseif ($minutes < 61) {
            $penaltyInfo['early_late_ab_hour'] = 1;
        } elseif ($minutes < 91) {
            $penaltyInfo['early_late_ab_hour'] = 1.5;
        } elseif ($minutes < 121) {
            $penaltyInfo['early_late_ab_hour'] = 2;
        } elseif ($minutes < 241) {
            $penaltyInfo['early_late_ab_hour'] = 4;
        } else {
            $penaltyInfo['early_late_ab_hour'] = $time_max;
        }

        //处理罚款
        if ($minutes <= 5) {
            $penaltyInfo['penalty_money'] = 0;
        } elseif ($minutes <= 30) {
            $penaltyInfo['penalty_money'] = 200;
        } elseif ($minutes <= 240) {
            $penaltyInfo['penalty_money'] = 250;
        } else {
            $penaltyInfo['penalty_money'] = $money_max;
        }
        return $penaltyInfo;
    }

    public static function showPenalTime($params): array
    {
        $timeInfo = ['day'=>0,'h'=>0];

        if ($params <= 2) {
            $timeInfo['h'] = floatval($params);
        }
        if ($params >= 4) {
            $timeInfo['day'] = $params == 4 ? 0.5 : 1;
        }
        return $timeInfo;
    }
    /**
     * 撤销处罚
     * @return void
     */
    protected function cancelPenalty()
    {
        foreach ($this->initDataObj->penalty_detail as $penaltyModel) {
            $this->clearPenalty($penaltyModel) && $this->doReadMessage($penaltyModel) && $this->sendCancelMessage($penaltyModel) && $this->cancelAudit($penaltyModel);
        }
    }


    /**
     * 撤销审批
     */
    protected function cancelAudit(HrPenaltyDetailModel $penaltyModel)
    {
        $auditInfo = HrPenaltyAppealModel::findFirst([
            'conditions' => ' penalty_detail_id = :penalty_detail_id: and staff_info_id = :staff_info_id: and status = :status:',
            'bind'       => [
                'penalty_detail_id' => $penaltyModel->id,
                'staff_info_id'     => $this->initDataObj->staff_info_id,
                'status'            => 1,
            ],
        ]);
        if (empty($auditInfo)) {
            return true;
        }
        //系统自动驳回，super 字段免于校验是否具备审批权限
        $server = new ApprovalServer($this->lang, $this->timeZone);
        return $server->reject($auditInfo->id, AuditListEnums::APPROVAL_TYPE_HR_PENALTY_APPEAL, "The penalty has been revoked and the system has automatically rejected it.",
            10000, ['super' => 1]);
    }

    /**
     * 发送撤销的消息
     * @param HrPenaltyDetailModel $penaltyModel
     * @return bool
     */
    public function sendCancelMessage(HrPenaltyDetailModel $penaltyModel)
    {
        $t = $this->getTranslation($this->initDataObj->staff_lang);
        $reasonMap = HrPenaltyDetailModel::getPenaltyReasonMap();
        $data = [
            "staff_users"        => [$penaltyModel->staff_info_id], //提交人ID
            "message_title"      =>  $t->_('penalty_cancellation_notice'),
            "message_content"    =>  $t->_('penalty_cancellation_notice_content',
                ['attendance_date' => $penaltyModel->attendance_date,'penalty_reason'=>$t->_($reasonMap[$penaltyModel->penalty_reason])]),
            "category"           => -1,                                   //类别
            "staff_info_ids_str" => $penaltyModel->staff_info_id,    //内容
            "id"                 => time().$penaltyModel->staff_info_id.rand(1000000, 9999999),

        ];
        return $this->sendMessage($data);
    }


    /**
     * 清理消息
     * @param HrPenaltyDetailModel $penaltyModel
     * @return bool
     */
    public function doReadMessage(HrPenaltyDetailModel $penaltyModel)
    {
        $staffMessageRepository = new StaffMessageRepository();
        $message_info           = $staffMessageRepository->getListByParams([
            'staff_info_id' => $penaltyModel->staff_info_id,
            'category'      => 33,
            'category_code' => [6, 7, 8],
            'related_id'    => $penaltyModel->id,
            'read_state'    => 0,
        ], ['mr.id']);
        if (empty($message_info)) {
            return true;
        }
        $message_ids = array_column($message_info, 'id');
        return $this->getDI()->get('db_coupon')->updateAsDict("message_courier", ["read_state" => 1],
            ['conditions' => "id in (".getIdsStr($message_ids).")"]);
    }


    /**
     * 清理处罚
     * @param HrPenaltyDetailModel $penaltyModel
     * @return bool
     */
    protected function clearPenalty(HrPenaltyDetailModel $penaltyModel)
    {
        $penaltyModel->state         = HrPenaltyDetailModel::PENALTY_STATE_INVALID;
        $penaltyModel->expiry_date   = date('Y-m-d');
        $penaltyModel->expiry_reason = $this->initDataObj->expiry_reason;
        return $penaltyModel->save();
    }


}