<?php


namespace FlashExpress\bi\App\Server\Penalty;

use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrPenaltyDetailModel;

/**
 * 旷工
 */
class AbsentPenaltyServer extends BasePenaltyServer
{

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
    }

    public $initDataObj;


    /**
     * 旷工
     */
    public function initData($initData): AbsentPenaltyServer
    {
        $this->initDataObj = $initData;
        return $this;
    }


    /**
     * @throws BusinessException
     */
    public function handle(): bool
    {
        if (!$this->checkIsNeed()) {
            return true;
        }

        $ab_long = $this->dealTime();
        $this->cancel($ab_long);
        $ab_long > 0 && $this->makeData($ab_long);
        return true;
    }

    public function cancel($ab_long): bool
    {
        $info = $this->dealPenaltyInfo($ab_long);
        //有变换再撤销
        if(!empty($this->initDataObj->absent_penalty) && $info['early_late_ab_hour'] != $this->initDataObj->absent_penalty->early_late_ab_hour){
            $this->clearPenalty($this->initDataObj->absent_penalty) && $this->doReadMessage($this->initDataObj->absent_penalty) && $this->sendCancelMessage($this->initDataObj->absent_penalty) && $this->cancelAudit($this->initDataObj->absent_penalty);
        }
        return true;
    }


    public function checkIsNeed(): bool
    {
        if (!parent::checkIsNeed()) {
            return false;
        }
        $need_check_time = date('Y-m-d H:i:s',
            strtotime($this->initDataObj->attendance_date.' '.$this->initDataObj->shift_start) + 22 * 3600);
        if (!empty($this->initDataObj->attendance_started_at)) {
            $need_check_time = min(date('Y-m-d H:i:s',
                strtotime($this->initDataObj->attendance_started_at) + 22 * 3600), $need_check_time);
        }
        //当前考勤日当天不判断旷工
        if (date('Y-m-d H:i:s') < $need_check_time) {
            return false;
        }
        return true;
    }


    /**
     *旷工天数
     */
    public function dealTime()
    {
        //请半天假 最多旷工0.5day
        $long = in_array($this->initDataObj->leave_type, [1, 2]) ? 0.5 : 1;

        if (empty($this->initDataObj->attendance_started_at) || empty($this->initDataObj->attendance_end_at)) {
            return $long;
        }
        return 0;
    }


    /**
     */
    public function dealPenaltyInfo($params): array
    {
        $penaltyInfo = [
            'early_late_ab_hour' => 0,
            'penalty_money'      => 0,
        ];
        if ($params == 0.5) {
            $penaltyInfo['early_late_ab_hour'] = 4;
            $penaltyInfo['penalty_money']      = 250;
        } elseif ($params == 1) {
            $penaltyInfo['early_late_ab_hour'] = 8;
            $penaltyInfo['penalty_money']      = 500;
        }

        return $penaltyInfo;
    }

    public static function showPenalTime($params): array
    {
        return ['day' => $params, 'h' => 0];
    }

    /**
     * @throws BusinessException
     */
    public function makeData($ab_long)
    {
        //处罚
        $penaltyInfo                    = $this->dealPenaltyInfo($ab_long);


        $penaltyInfo['staff_info_id']   = $this->initDataObj->staff_info_id;
        $penaltyInfo['store_id']        = $this->initDataObj->staff_store_id;
        $penaltyInfo['job_title']       = $this->initDataObj->job_title;
        $penaltyInfo['attendance_date'] = $this->initDataObj->attendance_date;
        $penaltyInfo['state']           = HrPenaltyDetailModel::PENALTY_STATE_TAKE_EFFECT;
        $penaltyInfo['penalty_reason']  = HrPenaltyDetailModel::PENALTY_REASON_AB;
        $penaltyInfo['penalty_date']    = date('Y-m-d');

        if (!empty($this->initDataObj->related_id)) {
            $penaltyInfo['id']           = $this->initDataObj->related_id;
            $penaltyInfo['created_at']   = $this->initDataObj->created_at;
            $penaltyInfo['updated_at']   = $this->initDataObj->created_at;
            $penaltyInfo['penalty_date'] = show_time_zone($this->initDataObj->created_at, 'Y-m-d');
            $penaltyInfo['remark']       = '事务问题修正';
        }
        $penaltyDetailId = $this->createPenalty([
            'staff_info_id'   => $this->initDataObj->staff_info_id,
            'attendance_date' => $this->initDataObj->attendance_date,
            'penalty_reason'  => HrPenaltyDetailModel::PENALTY_REASON_AB,
        ], $penaltyInfo);
        if (!empty($this->initDataObj->related_id)) {
            return true;
        }
        $t               = $this->getTranslation($this->initDataObj->staff_lang);
        $penaltyTime     = '';
        $penaltyTimeInfo = self::showPenalTime($ab_long);
        if ($penaltyTimeInfo['day'] > 0) {
            $penaltyTime .= $t->_('penalty_day', ['x' => $penaltyTimeInfo['day']]);
            $data = [
                "staff_users"        => [$this->initDataObj->staff_info_id],
                //提交人ID
                "message_title"      => $t->_('unauthorized_absent_reminder'),
                "message_content"    => $t->_('unauthorized_absent_reminder_content',
                    [
                        'attendance_date' => $this->initDataObj->attendance_date,
                        'penalty_time'    => $penaltyTime,
                        'money'           => $penaltyInfo['penalty_money'],
                    ]),
                "category"           => MessageEnums::CATEGORY_SIGN,
                //类别
                "category_code"      => MessageEnums::CATEGORY_SIGN_CODE_PENALTY_ABSENT,
                //子类别
                "push_state"         => 1,
                "audit_status"       => 2,
                //类别
                "staff_info_ids_str" => $this->initDataObj->staff_info_id,
                //内容
                "related_id"         => $penaltyDetailId,
                //内容
                "id"                 => time().$this->initDataObj->staff_info_id.rand(1000000, 9999999),

            ];
            $this->sendMessage($data);
        }

    }


}