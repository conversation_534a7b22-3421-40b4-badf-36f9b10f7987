<?php


namespace FlashExpress\bi\App\Server\Penalty;

use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrPenaltyDetailModel;
use FlashExpress\bi\App\Server\StaffServer;

/**
 * 早退
 */
class PunchOutPenaltyServer extends BasePenaltyServer
{

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
    }

    public $initDataObj = null;

    /**
     * 下班打卡
     */
    public function initData($initData): PunchOutPenaltyServer
    {
        $this->initDataObj = $initData;
        return $this;
    }


    /**
     * @throws BusinessException
     */
    public function handle(): bool
    {
        if (!$this->checkIsNeed()) {
            return true;
        }
        $seconds = $this->dealTime();
        $this->cancel($seconds);
        $seconds >= 60 && $this->makeData($seconds);
        return true;
    }

    public function cancel($seconds): bool
    {
        //有变换再撤销
        if(!empty($this->initDataObj->leave_early_penalty) && floor($seconds) != $this->initDataObj->leave_early_penalty->early_late_seconds){
            $this->clearPenalty($this->initDataObj->leave_early_penalty) && $this->doReadMessage($this->initDataObj->leave_early_penalty) && $this->sendCancelMessage($this->initDataObj->leave_early_penalty) && $this->cancelAudit($this->initDataObj->leave_early_penalty);
        }
        return true;
    }


    public function checkIsNeed(): bool
    {
        if (!parent::checkIsNeed()) {
            return false;
        }

        if (empty($this->initDataObj->attendance_end_at) || empty($this->initDataObj->need_shift_end)) {
            return false;
        }
        return true;
    }


    /**
     * 早退秒数
     * @return false|mixed
     */
    public function dealTime()
    {
        //1判断是否迟到;
        $punch_out_time      = strtotime(date('Y-m-d H:i:s', strtotime($this->initDataObj->attendance_end_at)));
        $need_punch_out_time = strtotime($this->initDataObj->need_shift_end);
        return floor(max($need_punch_out_time - $punch_out_time, 0));
    }


    /**
     * @throws BusinessException
     */
    public function makeData($seconds)
    {
        //生成处罚项目
        $penaltyInfo = $this->dealPenaltyInfo($seconds);

        //发送消息
        $t = $this->getTranslation($this->initDataObj->staff_lang);

        $timeInfo = DateHelper::dealSecondsToHoursAndMinutes($seconds);
        $timeStr  = '';
        if ($timeInfo['hours'] > 0) {
            $timeStr .= $t->_('penalty_hour', ['x' => $timeInfo['hours']]);
        }
        if ($timeInfo['minutes'] > 0) {
            $timeStr .= $t->_('penalty_minutes', ['x' => $timeInfo['minutes']]);
        }
        $penaltyTime     = '';
        $penaltyTimeInfo = self::showPenalTime($penaltyInfo['early_late_ab_hour']);
        if ($penaltyTimeInfo['h'] > 0) {
            $penaltyTime .= $t->_('penalty_hour', ['x' => $penaltyTimeInfo['h']]);
        }
        if ($penaltyTimeInfo['day'] > 0) {
            $penaltyTime .= $t->_('penalty_day', ['x' => $penaltyTimeInfo['day']]);
        }

        $category        = -1;
        $category_code   = 0;
        $penaltyDetailId = 0;

        $title   = $t->_('leave_early_reminder');
        $content = $t->_('leave_early_reminder_content', ['time' => $timeStr]);

        if ($penaltyInfo['penalty_money'] > 0) {
            $penaltyInfo['early_late_seconds'] = floor($seconds);
            $penaltyInfo['staff_info_id']      = $this->initDataObj->staff_info_id;
            $penaltyInfo['store_id']           = $this->initDataObj->staff_store_id;
            $penaltyInfo['job_title']          = $this->initDataObj->job_title;
            $penaltyInfo['attendance_date']    = $this->initDataObj->attendance_date;
            $penaltyInfo['state']              = HrPenaltyDetailModel::PENALTY_STATE_TAKE_EFFECT;
            $penaltyInfo['penalty_reason']     = HrPenaltyDetailModel::PENALTY_REASON_LEAVE_EARLY;
            $penaltyInfo['penalty_date']       = date('Y-m-d');

            if (!empty($this->initDataObj->related_id)) {
                $penaltyInfo['id']           = $this->initDataObj->related_id;
                $penaltyInfo['created_at']   = $this->initDataObj->created_at;
                $penaltyInfo['updated_at']   = $this->initDataObj->created_at;
                $penaltyInfo['penalty_date'] = show_time_zone($this->initDataObj->created_at, 'Y-m-d');
                $penaltyInfo['remark']       = '事务问题修正';
            }

            $penaltyDetailId                   = $this->createPenalty([
                'staff_info_id'   => $this->initDataObj->staff_info_id,
                'attendance_date' => $this->initDataObj->attendance_date,
                'penalty_reason'  => HrPenaltyDetailModel::PENALTY_REASON_LEAVE_EARLY,
            ], $penaltyInfo);

            if(!empty($this->initDataObj->related_id)){
                return true;
            }

            $title                             = $t->_('undertime_reminder');
            $content                           = $t->_('undertime_reminder_content',
                [
                    'attendance_date' => $this->initDataObj->attendance_date,
                    'time'            => $timeStr,
                    'penalty_time'    => $penaltyTime,
                    'money'           => $penaltyInfo['penalty_money'],
                ]);
            $category                          = MessageEnums::CATEGORY_SIGN;
            $category_code                     = MessageEnums::CATEGORY_SIGN_CODE_PENALTY_LEAVE_EARLY;
        }

        $data = [
            "staff_users"        => [$this->initDataObj->staff_info_id],
            //提交人ID
            "message_title"      => $title,
            "message_content"    => $content,
            //类别
            "category"           => $category,
            //子类别
            "category_code"      => $category_code,
            //类别
            "push_state"         => 1,
            //类别
            "staff_info_ids_str" => $this->initDataObj->staff_info_id,
            //内容
            "related_id"         => $penaltyDetailId,
            "audit_status"       => 2,
            //内容
            "id"                 => time().$this->initDataObj->staff_info_id.rand(1000000, 9999999),

        ];

        $this->sendMessage($data);
    }


}