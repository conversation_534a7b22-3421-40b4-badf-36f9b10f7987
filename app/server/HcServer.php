<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\Enums\WorkflowEnums;
use FlashExpress\bi\App\Enums\WorkingCountryEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\AuditCCModel;
use FlashExpress\bi\App\Models\backyard\HcmHcManagerDetailModel;
use FlashExpress\bi\App\Models\backyard\HrApproval;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HRHCBudgetItemModel;
use FlashExpress\bi\App\Models\backyard\HRHCBudgetModel;
use FlashExpress\bi\App\Models\backyard\HrHcLogModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrMyApproval;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HRStaffingModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Models\oa\HcBudgetPerMonthModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\OsStaffRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysListRepository;

class HcServer extends AuditBaseServer
{
    protected $re;
    protected $auditlist;
    public $timezone;
    protected $hc;
    protected $staff;
    protected $public;
    protected $department;
	protected $share_department_id;
    
	protected $share_department_position;
    protected $taskFlag = false;
    public function setTaskFlag($flag)
    {
        $this->taskFlag = $flag;
    }
    public function getTaskFlag()
    {
        return $this->taskFlag;
    }

    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->auditlist  = new AuditlistRepository($lang, $timezone);
        $this->hc         = new HcRepository($timezone);
        $this->hcBudget   = new HcBudgetServer($timezone);
        $this->staff      = new StaffRepository();
        $this->timezone   = $timezone;
        $this->public     = new PublicRepository();
        $this->department = new DepartmentRepository();
	    $share_department_position = $this->hcBudget->getShareDepartmentPosition();
		$this->share_department_position = $share_department_position['department_position']; //部门=>[共享职位,共享职位]
	    $this->share_department_id = !empty($share_department_position['department_ids']) ? $share_department_position['department_ids'] : [];
    }

    /**
     * 枚举项
     * @param $params
     * @return array
     */
    public function getEnums($params): array
    {
        $data['language_ability_list'] = $this->languageAbilityList();
        $data['non_head_office_position'] = $this->getNonHeadOfficePosition();
        return $data;
    }

    /**
     * 获取不可以创建的职位key
     * @return array
     */
    public function getNonHeadOfficePosition(): array
    {
        $nonHeadOfficePosition = (new SettingEnvServer())->getSetValToArray('non_head_office_position');
        return array_values(array_unique(array_filter($nonHeadOfficePosition)));
    }

    /**
     * 语言能力 枚举  产品不要翻译
     * @return string[]
     */
    protected function languageAbilityList(): array
    {
        $result = [];
        $list   = enums::$languageAbility;
        foreach ($list as $key => $item) {
            $result[] = ['value' => strval($key), 'label' => $item];
        }
        return $result;
    }
	

    /**
     * 获取工作流
     * @param array $paramIn
     * @return array
     */
    public function getApproval($paramIn = [])
    {
        //[1]获取请求url
        $url  = $this->processingDefault($paramIn, 'url', 1);

        //[2]组合数据
        $method          = 'hc_approval_list';
        $param           = [
            'user_info' => $paramIn['userinfo'],
        ];

        //[3]请求接口
        $fle_rpc = (new ApiClient($url,'',$method, $this->lang));
        $fle_rpc->setParams($param);
        $resultData = $fle_rpc->execute();
        return $resultData['result'];
    }

    /**
     * 获取hc提交人信息
     * @param array $paramIn
     * @return array
     */
    public function getHcSubmitter($paramIn = [])
    {
        $hc_id = $this->processingDefault($paramIn, 'hc_id', 1);

        $hcSql = "select submitter_id,state_code from hr_hc where hc_id= '{$hc_id}'";
        $data = $this->getDI()->get('db')->query($hcSql);
        $hcData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (!isset($hcData['submitter_id'])) {
            return [];
        }

        $staffData = $this->getPersonInfo($hcData['submitter_id']);
        return array_merge($hcData, $staffData);
    }

    /**
     * 获取用户详情
     * @param int $submitter_id
     * @return array
     */
    public function getPersonInfo($submitter_id)
    {
        $query_sql = "
            --
            select 
            si.id
            ,si.id as staff_id
            ,organization_type
            ,GROUP_CONCAT(sip.position_category) position_category
            ,(case organization_type when 1 then organization_id when 2 then -1 end) as store_id
            ,department_id as department_id
            ,(case organization_type when 1 then ss.name when 2 then 'Head Office' end) as store_name
            ,ss.category as store_category
            ,ss.use_state as use_state
            ,sd.name as department_name
            from staff_info si
            left join staff_info_position sip on si.id = sip.staff_info_id
            left join sys_store ss on ss.id = organization_id
            join sys_department sd on sd.id = si.department_id
            where si.id = {$submitter_id}
        ";
        $data = $this->getDI()->get('db_fle')->query($query_sql);
        $staffData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $staffData;
    }

    /**
     * 获取优先级
     */
    public function getPriorityList($priorityId = null)
    {
        $data = [
            [
                'code'  => 1,
                'msg'   => 'P1 (' . $this->getTranslation()->_('highest') . ')',
            ],
            [
                'code'  => 2,
                'msg'   => 'P2 (' . $this->getTranslation()->_('high') . ')',
            ],
            [
                'code'  => 3,
                'msg'   => 'P3 (' . $this->getTranslation()->_('medium') . ')',
            ],
            [
                'code'  => 4,
                'msg'   => 'P4 (' . $this->getTranslation()->_('low') . ')',
            ],
        ];

        if (empty($priorityId)) {
            return $data;
        } else {
            $data = array_column($data, 'msg', 'code');
            return $data[$priorityId] ?? '';
        }
    }

    /**
     * 获取详情
     */
    public function getRecruitDetail($paramIn = [])
    {
        //[1]参数定义
        $hcId       = $this->processingDefault($paramIn, 'hc_id');
        $returnData = [];

        //[2]获取Hc详情
        $data = $this->getHcInfo(['hc_id'=>$hcId]);

        if (isset($data['priority_id']) && $data['priority_id']) {
            $priority = $this->getPriorityList($data['priority_id']);
        }else {
            $priority = '';
        }

        //[3]组织数据
        if ($data) {
            $returnData[] = [
                [
                    'key'   => 'HC ID',
                    'value' => $data['hc_id'],
                ],
                [
                    'key'   => $this->getTranslation()->_('recruit_status'),
                    'value' => $this->getRecruitStatusText($data['state_code']),
                ],
                [
                    'key'   => $this->getTranslation()->_('has_recruited'),
                    'value' => ($data['demandnumber'] - $data['surplusnumber']),
                ],
                [
                    'key'   => $this->getTranslation()->_('recruit_list'),
                    'value' => $data['issuedOfferResume'],
                ],
            ];

            $returnData[] = [
                [
                    'key'   => $this->getTranslation()->_('audit_type'),
                    'value' => $this->auditlist->getAudityType(6),
                ],
                [
                    'key'   => $this->getTranslation()->_('submit_time'),
                    'value' => $data['createtime'],
                ],
                [
                    'key'   => $this->getTranslation()->_('selectJD'),
                    'value' => $data['job_name'],
                ],[
                    'key'   => $this->getTranslation()->_('department'),
                    'value' => $data['department_name'],
                ],[
                    'key'   => $this->getTranslation()->_('store'),
                    'value' => $data['worknode_name'],
                ],[
                    'key'   => $this->getTranslation()->_('expirationdate'),
                    'value' => $data['expirationdate'],
                ],
                [
                    'key'   => $this->getTranslation()->_('demandnumber'),
                    'value' => $data['demandnumber'],
                ],
                [
                    'key'   => $this->getTranslation()->_('recruit_priority'),
                    'value' => $priority,
                ],
                [
                    'key'   => $this->getTranslation()->_('other_requirement'),
                    'value' => $data['remarks'],
                ],
                [
                    'key'   => $this->getTranslation()->_('hcreason'),
                    'value' => $data['reason_type_text'],
                ],
            ];
        }
        return $returnData;
    }

    /**
     * 获取招聘状态
     * @param $status
     * @return string
     */
    public function getRecruitStatusText($status)
    {
        $statusArr = [
            1   => $this->getTranslation()->_('4031'),
            2   => $this->getTranslation()->_('4032'),
            3   => $this->getTranslation()->_('4033'),
            4   => $this->getTranslation()->_('4034'),
            9   => $this->getTranslation()->_('4035'),
        ];
        return $statusArr[$status] ?? '';
    }

    /**
     * 发送推送消息
     */
    public function sendHcPushMessage($paramIn = [])
    {
        $approvalId = $this->processingDefault($paramIn, 'approval_id');
        $staffData  = $this->processingDefault($paramIn, 'user_info');
        $hcId       = $this->processingDefault($paramIn, 'hc_id');

        $lang = (new StaffServer())->getLanguage($approvalId);

        $message_title = $this->getTranslation($lang)->_('6006');
        $auditType     = $this->getTranslation($lang)->_('7007');

        $pushParam = [
            'staff_info_id' => $approvalId,
            'message_title' => $message_title,
            'userinfo'      => $staffData,
            'lastInsert_id' => $hcId,
            'audit_type'    => $auditType,
            'lang'          => $lang,
        ];
        (new \FlashExpress\bi\App\Repository\PublicRepository())->pushMessage($pushParam);
    }

    /**
     * 更新Hc surplusnumber数
     * @param array $paramIn
     * @return void
     */
    public function updateHc($paramIn = [])
    {
        $hcId       = $paramIn['id'] ?? '';
        $surplusnumber = $paramIn['surplusnumber'] ?? 0;
        $demandnumber = $paramIn['demandnumber'] ?? 0;

        if ($surplusnumber == 1) {
            $now = date('Y-m-d H:i:s');
            // 修改hc表状态 改为已招满
            $hcSql = "update hr_hc set surplusnumber = surplusnumber - 1, state_code = 3,full_time ='{$now}' where hc_id = ?";

        } elseif ($surplusnumber > $demandnumber) {
            $hcSql = "update hr_hc set surplusnumber = demandnumber - 1 where hc_id = ?";
        } else {
            $hcSql = "update hr_hc set surplusnumber = surplusnumber - 1 where hc_id = ? and surplusnumber > 0";
        }
        $this->getDI()->get('logger')->write_log("reduce hc surplusnumber sql:" . $hcSql, 'info');
        $this->getDI()->get('logger')->write_log(['updateHc' => $hcSql, 'hd_id' => $hcId,'params'=>$paramIn], 'info');
        $this->getDI()->get('db')->execute($hcSql, [$hcId]);
    }

    /**
     * 更新Hc surplusnumber数
     * @param array $paramIn
     * @return void
     */
    public function rollBackHc($paramIn = [])
    {
        $hcId       = $paramIn['id'] ?? '';
        $surplusnumber = $paramIn['surplusnumber'] ?? 0;
        $demandnumber = $paramIn['demandnumber'] ?? 0;

        if ($surplusnumber == 0) {
            // 修改hc表状态 改为已招满
            $hcSql = "update hr_hc set surplusnumber=surplusnumber+1,state_code=2 where hc_id = ?";

        } elseif ($surplusnumber > $demandnumber) {
            $hcSql = "update hr_hc set surplusnumber=demandnumber+1 where hc_id = ?";
        } else {
            $hcSql = "update hr_hc set surplusnumber=surplusnumber+1 where hc_id = ?";
        }
        $this->getDI()->get('logger')->write_log(['rollBackHc' => $hcSql, 'hd_id' => $hcId,'params'=>$paramIn], 'info');

        $this->getDI()->get('db')->execute($hcSql, [$hcId]);
    }

    /**
     * 获取HC详情
     * @param array $paramIn
     * @return array
     */
    public function getHcInfo($paramIn = [])
    {
        //[1]校验数据类型
        $hcId  = $this->processingDefault($paramIn, 'hc_id', '2');
        if (empty($hcId)) {
            return [];
        }

        //[3]发送RPC请求
        $rpcClient = new ApiClient('winhr_rpc', '', 'hc_info', $this->lang);
        $rpcClient->setParams([
            'hc_id' => $hcId,
        ]);
        $return    = $rpcClient->execute();
        $this->getDI()->get('logger')->write_log("winhr_rpc getHcInfo:" . json_encode($return,JSON_UNESCAPED_UNICODE), 'info');
        if (isset($return['result']['code']) && $return['result']['code'] == 1) {
            //用人原因类型
            $return['result']['data']['reason_type_text'] = str_replace(["招聘", "Recruitment"], ["新增", "Add"], $this->getTranslation()->_("hc_reason_type_".$return['result']['data']['reason_type']));
            return $return['result']['data'] ?? [];
        } else {
            return [];
        }
    }

    /**
     * 添加HC申请
     * @param int    department_id     部门ID
     * @param int    worknode_id       网点ID
     * @param string expirationdate    截止日期
     * @param int    demandnumber      需求人数
     * @param string remarks           其他要求
     * @param string reason            用人原因
     * @param string interviewer       面试官(目前没有数据传NULL)
     * @param int    job_id            JD ID
     * @param int    priority_id       优先级：1-P1 2-P2 3-P3 4-P4
     * @param int    type              类型：1=快递员工，2=总部员工
     * @return mixed
     */
    public function addHc($paramIn = [])
    {
        //[1]传入参数
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $departmentId   = $this->processingDefault($paramIn, 'department_id', 2);
        $worknodeId     = $this->processingDefault($paramIn, 'worknode_id', 1);
        $expirationDate = $this->processingDefault($paramIn, 'expirationdate', 1);
        $demandnumber   = $this->processingDefault($paramIn, 'demandnumber', 2);
        $remarks        = $this->processingDefault($paramIn, 'remarks', 1);
        $remarks        = addcslashes(stripslashes($remarks),"'");
        $reason         = $this->processingDefault($paramIn, 'reason', 1);
        $reason         = addcslashes(stripslashes($reason),"'");
        $jobId          = $this->processingDefault($paramIn, 'job_id', 2);
        $priorityId     = $this->processingDefault($paramIn, 'priority_id', 2);
        $type           = $this->processingDefault($paramIn, 'type', 2);
        $reason_type    = $this->processingDefault($paramIn, 'reason_type', 2,1);
        $hire_type = $this->processingDefault($paramIn, 'hire_type', 2,1);
        $hire_times = $this->processingDefault($paramIn, 'hire_times', 2,1);
        $job_title      = $this->processingDefault($paramIn, 'job_title_id', 2);
        $leave_staffs = $this->processingDefault($paramIn, 'leave_staffs', 3);
        $working_day_rest_type = $this->processingDefault($paramIn, 'working_day_rest_type', 1);
        $language_ability = $this->processingDefault($paramIn, 'language_ability');

        //[2]校验
        //[2.1]校验JD
        $jdData = $this->hc->infoJD(['job_id' => $jobId]);
        if (empty($jdData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('3007'));
        }

        //[2.1]校验网点
        $worknodeData = (new SysStoreServer())->getStoreByid($worknodeId);
        if (empty($worknodeData) && $worknodeId != -1) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4010'));
        }

        //[2.2]校验部门
        $checkData = $this->department->getDepartmentNameById($departmentId);
        if (empty($checkData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('3009'));
        }
        //[2]获取预算
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        if($reason_type == 3) {
            if(empty($leave_staffs)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4022'));
            }
            if(count($leave_staffs) != $demandnumber) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4023'));
            }

            $leave_staff_ids = array_column($leave_staffs,'staff_info_id');
            $r = $this->hc->getHrHcLeaveStaffsByStaffInfoIds($leave_staff_ids);
            if(count($r) > 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4021'));
            }
        }

        //[3]获取
        //固化信息
        $requestInfo = $this->getHcReferences([
            'department_id' => $departmentId,
            'store_id'      => $worknodeId,
            'job_title_id'  => $job_title,
        ]);
        $leftTotal   = $requestInfo['data']['left'] ?? 0;

        //刘光辉需求
        //https://l8bx01gcjr.feishu.cn/docs/doccnnUugShqAiYyaXTY7RUrkZe
        $submitterInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staffId,
            ],
            'columns' => "node_department_id",
        ]);

        //network operations
        $envModel = new SettingEnvServer();
        $departId = $envModel->getSetVal('dept_network_operations_id');
        if (!empty($submitterInfo) && isset($submitterInfo->node_department_id) && $submitterInfo->node_department_id == $departId) {
            $errMessageBudgetNoCount = 'err_msg_network_operations_hc_budget_no_count';
        } else {
            $errMessageBudgetNoCount = 'hc_budget_no_count';
        }

        $this->logger->write_log("addHc  departmentId:".$departmentId." leftTotle:".$leftTotal .' demandnumber:'.$demandnumber, 'info');


        //当前编制不足，或者编制剩余人数小于申请的额度
        if ($leftTotal < $demandnumber) {
            throw new BusinessException($this->getTranslation()->_($errMessageBudgetNoCount),ErrCode::VALIDATE_ERROR);
        }

        //部门职位关联关系
        $relate = (new OsStaffRepository($this->timezone))->getRelation($departmentId, $job_title);
        if (empty($relate)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('err_msg_job_title_department_dismatch'));
        }

        //[3]拼装HC请求数据
        $serialNo = $this->getRandomId();

        //获取城市地址
        $country_code  = '';
        $province_code = '';
        $city_code     = '';
        $district_code = '';

        if (!empty($worknodeId) || $worknodeId != '-1') { //网点
            if ($worknodeData) {
                $country_code  = $worknodeData['country_code'];
                $province_code = $worknodeData['province_code'];
                $city_code     = $worknodeData['city_code'];
                $district_code = $worknodeData['district_code'];
            }
        }
        try {
            $this->getDI()->get('db')->begin();

            //Hc负责人工号
            $hcManagerStaffId = $this->getHcManagerByDeptId($departmentId,$worknodeId);

            //添加hc申请
            $insetHcData = [
                'job_id'              => $jobId,
                'submitter_id'        => $staffId,
                'worknode_id'         => $worknodeId,
                'department_id'       => $departmentId,
                'expirationdate'      => $expirationDate,
                'demandnumber'        => $demandnumber,
                'surplusnumber'       => $demandnumber,
                'remarks'             => $remarks,
                'reason'              => $reason,
                'country_code'        => $country_code,
                'province_code'       => $province_code,
                'city_code'           => $city_code,
                'district_code'       => $district_code,
                'type'                => $type,
                'priority_id'         => $priorityId,
                'job_title'           => $job_title,
                'approval_stage'      => 0,
                'approval_state_code' => 7,
                'serial_no'           => $serialNo ? ("HC" . $serialNo) : '',
                'state_code'          => 1,
                'workflow_role'       => 'hc_v2',
                'reason_type'         => $reason_type,
                'hire_type'           => $hire_type,
                'hire_times'          => $hire_times,
                'manager_staff_id'    => $hcManagerStaffId,
                'request_info'        => ($requestInfo['data']['left'] ?? 0) . '/' . ($requestInfo['data']['total'] ?? 0),
                'working_day_rest_type' => $working_day_rest_type,
                'language_ability'    => $language_ability,
                'create_src'          => HrHcModel::CREATE_SRC_BY_APPLY,
                'hc_last_operator'    => $staffId,
            ];
            if (isCountry('MY')) {
                $insetHcData['manager_count'] = !empty($hcManagerStaffId) ? 1 : 0;
            }
            $hcId = $this->hc->insertHc($insetHcData);
            $this->saveHcManager($hcId, $hcManagerStaffId, $staffId);

            $insertLeaveStaffIds = [];
            foreach ($leave_staffs as $key => $value) {
                $insertLeaveStaffIds[] = ['hc_id' => $hcId, 'staff_info_id' => $value['staff_info_id'], 'snapshot' => json_encode($leave_staffs[$key])];
            }
            if(!empty($insertLeaveStaffIds)) {
                $this->hc->batch_insert('hr_hc_leave_staff', $insertLeaveStaffIds);
            }

            //这里是 from 表单的内容
            $extend['from_submit'] = [
                'department_id'        => $departmentId,
                'request_department_id'=> $departmentId,
                'submitter_max_Level'  => $this->getSubmitterMaxLevel($staffId),
                'sys_store_id'         => $worknodeId,
            ];
            $extend['department_id'] = $departmentId;

            $this->getDI()->get('logger')->write_log("hc_workflow_data - create_wf: hc_id - {$hcId}, staff_id - {$staffId}, extend - ". json_encode($extend, JSON_UNESCAPED_UNICODE), 'info');

            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($hcId, enums::$audit_type['HC'], $staffId, null, $extend);

            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
            $this->getDI()->get('db')->commit();

        }catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('hc:addHc'. $e->getMessage() . $e->getTraceAsString());
            return $this->checkReturn(-3, $e->getMessage());
        }
        return $this->checkReturn(['data' => ['hc_id' => $hcId]]);
    }
	
	/**
	 * @description:获取自审批流
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2022/3/11 18:17
	 */
	
	public function getFlowCode($extend,$departmentId,$worknodeId,$departId,$bulky_departId){
		return $extend;
	}
	
	/**
     * 获取Hc招聘负责人
     * hc负责人
     * @param $dept_id
     */
    private function getHcManagerByDeptId($dept_id,$worknode_id){

        $hc_manager_staff_id = 0;
        $worknode_id = $worknode_id == '-1' ? '-1' : '-2';
        $dept_info = SysDepartmentModel::findFirst($dept_id);
        if($dept_info && $dept_info->ancestry_v3){
            $dept_ancestry_v3 = $dept_info->ancestry_v3;
            //判断是不是网点类型部门（指部门下面没有部门信息，下面是网点大区等类型的部门）
            $store_dept_ids = (new SettingEnvServer())->getSetVal('hc_manager_store_type_dept_ids');
            $log_info = ['dept_id'=>$dept_id,'worknode_id'=>$worknode_id,'dept_ancestry_v3'=>$dept_ancestry_v3,'store_dept_ids'=>$store_dept_ids];

            if($store_dept_ids && in_array($dept_id,explode(',',$store_dept_ids))){
                $this->getDI()->get('logger')->write_log("getHcManagerByDeptId-1 : ". json_encode($log_info, JSON_UNESCAPED_UNICODE), 'info');
                //网点类型部门获取负责人逻辑
                $hc_manager_info = HcmHcManagerDetailModel::findFirst([
                    'conditions' => "manage_dept_id = :dept_id: AND manage_worknode_id = :worknode_id:",
                    'bind' => ['dept_id' => $dept_id,'worknode_id'=>$worknode_id],
                ]);

                if($hc_manager_info){
                    $hc_manager_staff_id =  $hc_manager_info->staff_id;
                }else{
                    //如果未获取到负责人，则根据hc所属部门条件再查找，随机读取一条作为负责人，读不到就不处理
                    $hc_manager_info = HcmHcManagerDetailModel::findFirst([
                        'conditions' => "manage_dept_id = :dept_id: AND manage_worknode_id NOT IN ('-1','-2')",
                        'bind' => ['dept_id' => $dept_id],
                    ]);
                    if($hc_manager_info){
                        $hc_manager_staff_id =  $hc_manager_info->staff_id;
                    }
                }
            }else{
                //普通类型部门获取负责人逻辑，，按照从后到前的顺序依次判断该部门下是否有负责人（先判断末级部门，找不到找上级部门依次类推）
                $this->getDI()->get('logger')->write_log("getHcManagerByDeptId-2 : ". json_encode($log_info, JSON_UNESCAPED_UNICODE), 'info');
                $dept_ancestry_v3_arr = array_reverse(explode('/',$dept_ancestry_v3));//根据部门链拆成数组分并反转
                foreach ($dept_ancestry_v3_arr as $i=>$current_dept_id){
                    if($i > 1){
                        //只寻找两级
                        break;
                    }
                    $hc_manager_info = HcmHcManagerDetailModel::findFirst([
                        'conditions' => "manage_dept_id = :dept_id:",
                        'bind' => ['dept_id' => $current_dept_id],
                    ]);
                    if($hc_manager_info){ //读取到管辖负责人则停止继续读取

                        $hc_manager_staff_id =  $hc_manager_info->staff_id;
                        $this->getDI()->get('logger')->write_log("getHcManagerByDeptId-2-ok :current_dept_id-{$current_dept_id},staff_id:{$hc_manager_staff_id} ", 'info');
                        break;
                    }


                }

            }
        }

        return $hc_manager_staff_id;
    }

    /**
     * 审批hc
     * @param array $paramIn
     * @throws \Exception
     */
    public function updateHcV2($paramIn = [])
    {
        //[1]传入参数
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $hcId = $this->processingDefault($paramIn, 'audit_id', 2);
        $reason = $this->processingDefault($paramIn, 'reject_reason', 1);
        $state_code = $this->processingDefault($paramIn, 'status', 2);
        $priority = $this->processingDefault($paramIn, 'priority_id', 2);
        $reason = addcslashes(stripslashes($reason), "'");
        $new_demand_number = $this->processingDefault($paramIn, 'new_demand_number', 0);

        //[2]获取详情
        $info = $this->getHcInfo(['hc_id' => $hcId]);
        if (empty($info)) {
            throw new \Exception($this->getTranslation()->_('4008'));
        }

        $server = new ApprovalServer($this->lang, $this->timezone);
        if ($state_code == enums::$audit_status['approved']) {
            //同意

            //更新优先级
            $hc_update = [];
            if (!empty($priority) && $priority != $info['priority_id']) {
                $hc_update = array_merge($hc_update,['priority_id' => $priority]);
            }
	
	        //network operations
	        $envModel = new SettingEnvServer();
//	        $departId = $envModel->getSetVal('dept_network_operations_id');
//	        //Network Bulky Operations
//	        $bulky_departId = $envModel->getSetVal('dept_network_bulky_operations_id');
//	        $bulky_departId = !empty($bulky_departId) ? $bulky_departId : 0;
//
//	        // 区分FE事业部的子审批流: 获取Flash Express事业部下Network Operations部门及其子部门列表
//	        $network_operations_department_ids = $this->department->getDepartmentListById($departId, true);
//	        //获取Network Bulky Operations部门及其子部门列表
//	        $network_bulky_operations_department_ids = $this->department->getDepartmentListById($bulky_departId, true);
//	        $dept_ids = array_merge($network_operations_department_ids,$network_bulky_operations_department_ids);
            $editHcDemandNumDepartmentIds = $envModel->getSetVal('edit_hc_demand_num_dept_ids', ',');
            $edit_demand_num_staffids_arr = SettingEnvServer::getSetValToArray('hrlist_edit_demand_num_staffids',',');
	        
            //更新申请人数
            if(!$this->getTaskFlag() && in_array($info['department_id'], $editHcDemandNumDepartmentIds) && !empty($new_demand_number) && $new_demand_number <= $info['demandnumber']) {
                $hc_update = array_merge($hc_update, ['demandnumber' => $new_demand_number, 'surplusnumber' => $new_demand_number]);
            }elseif (!empty($new_demand_number) && $new_demand_number != $info['demandnumber']){
                if (!in_array($staffId,$edit_demand_num_staffids_arr)){
                    throw new BusinessException($this->getTranslation()->_('update_hc_err_3'));
                }
                $supplement_num = intval($new_demand_number) - intval($info["demandnumber"]);
                if ($supplement_num > 0){
                    $requestInfo = $this->getHcReferences([
                        'department_id' => $info['department_id'],
                        'store_id'      => $info['worknode_id'],
                        'job_title_id'  => $info['job_title_id'],
                    ]);
                    $leftTotal   = $requestInfo['data']['left'] ?? 0;
                    if ($leftTotal  >= $supplement_num) {
                        $hc_update = array_merge($hc_update, ['demandnumber' => $new_demand_number, 'surplusnumber' => $new_demand_number]);
                    }else{
                        throw new BusinessException($this->getTranslation()->_('job_transfer_budget_err'));
                    }
                }elseif ($supplement_num < 0){
                    $hc_update = array_merge($hc_update, ['demandnumber' => $new_demand_number, 'surplusnumber' => $new_demand_number]);
                }
            }
            $approval_res = $server->approval($hcId, AuditListEnums::APPROVAL_TYPE_HC, $staffId);
            if(!empty($approval_res) && !empty($hc_update)) {
                //记录一下日志
                $log_result =  $this->addHrhcSaveLog($hcId,$staffId,$hc_update);

                if (isset($hc_update['demandnumber']) && $hc_update['demandnumber'] != $info['demandnumber']) {
                    $hc_update['hc_last_operator'] = $staffId;
                }
                if (isset($hc_update['priority_id']) && $hc_update['priority_id'] != $info['priority_id']) {
                    $hc_update['hc_last_operator'] = $staffId;
                }

                $update_result = $this->getDI()->get('db')->updateAsDict(
                    'hr_hc',
                    $hc_update,
                    'hc_id = ' . $hcId
                );
                if ($new_demand_number != $info['demandnumber'] && !empty($new_demand_number)){
                    $insertParams = [
                        'audit_type' => AuditListEnums::APPROVAL_TYPE_HC,
                        'audit_value'=> $hcId,
                        'update_list' => [
                            'demandnumber' => $new_demand_number,
                        ],
                    ];
                    $mq = new RocketMQ('audit-list-update');
                    $mq->setType(RocketMQ::TAG_AUDIT_LIST_SUMMARY_UPDATE);
                    $rid = $mq->sendMsgByTag($insertParams,5);
                    $this->logger->write_log('updateHcV2 audit-list-update rid:' . $rid . 'data:' . json_encode($insertParams),
                        $rid ? 'info' : 'error');

                	//这里是埋点统计  如果 修改的值 不等于以前的值
	                $this->getDI()->get('logger')->write_log("updateHcV2_buried_point ｜{$staffId}｜{$hcId}｜{$info['demandnumber']}｜{$new_demand_number}", 'info');
                }
                $this->getDI()->get('logger')->write_log("updateHcV2 hc_id - " . $hcId .":". json_encode($hc_update) . "结果：". $update_result.'--'.$log_result, 'info');
            }


        } else if ($state_code == enums::$audit_status['revoked']) {
            //撤销
            $approval_res =  $server->cancel($hcId, AuditListEnums::APPROVAL_TYPE_HC, $reason, $staffId);
        } else {
            //驳回
            $approval_res =  $server->reject($hcId, AuditListEnums::APPROVAL_TYPE_HC, $reason, $staffId);
        }

        return $this->checkReturn(['data'=>['hc_id' => $hcId,'approval_res'=>$approval_res ?? 'false']]);
    }

    /**
     * 详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //获取Hc详情
        $result = $this->hc->infoHc(['hc_id' => $auditId]);

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }

        if (isset($result['priority_id']) && $result['priority_id']) {
            $priority = $this->getPriorityList($result['priority_id']);
        } else {
            $priority = '';
        }
        //解析部门、网点、职位
        $jobTitle = HrJobTitleModel::findFirst($result['job_title']);
        $departmentName = $this->department->getDepartmentNameById($result['department_id']);
        $storeIds       = getIdsStr([$result['worknode_id']]);
        $workNodeName   = (new SysListRepository())->getStoreList(['ids' => $storeIds]);
        $workNodeName   = array_column($workNodeName, 'name', 'id');

        $working_day_rest_type_str = '';
        if(!empty($result['working_day_rest_type'])) {
            $working_day_rest_type = explode(',', $result['working_day_rest_type']);
            array_walk($working_day_rest_type, function (&$val) {
                $val = $this->getTranslation()->_('working_day_rest_type_' . $val);
            });
            $working_day_rest_type_str = implode(',', $working_day_rest_type);
        }

        $detailLists = [
            'apply_parson'      => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? ''),
            'apply_department'  => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
            'selectJD'          => ($departmentName ?? '') . "-" . ($result['job_name'] ?? ''),
            'report_job_name'   => !empty($jobTitle) ? $jobTitle->job_name : '',
            'department'        => $departmentName ?? '',
            'working_day_rest_type' => $working_day_rest_type_str,
        ];
        if (in_array($result['hire_type'], [
            HrHcModel::HIRE_TYPE_PERMANENT_EMPLOYEE,
            HrHcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS,
            HrHcModel::HIRE_TYPE_SPECIAL_CONTRACT_WEEKLY_WORKERS,
            HrHcModel::HIRE_TYPE_SPECIAL_CONTRACT_HOURLY_WORKER,
            HrHcModel::HIRE_TYPE_INTERNS,
            HrHcModel::HIRE_TYPE_CONTRACT_LABOUR,
            HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT
        ])) {
            $detailLists['hire_type'] = $this->getTranslation()->_('hire_type_' . $result['hire_type']); // 雇佣类型
            if (in_array($result['hire_type'], [3, 4])) {

                $detailLists['hire_time_days'] = $result['hire_times'] . $this->getTranslation()->_('daily');
            } else if (in_array($result['hire_type'], [HrHcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS, HrHcModel::HIRE_TYPE_CONTRACT_LABOUR,HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT])) {

                $detailLists['hire_time_month'] = $result['hire_times'] . $this->getTranslation()->_('monthly');
            }
        }
        $detailLists['store'] = $workNodeName[$result['worknode_id']] ?? '';//工作网点
        $detailLists['HC ID'] = $result['hc_id'] ?? '';
        $detailLists['expirationdate'] = $result['expirationdate'] ?? '';
        $detailLists['demandnumber'] = $result['demandnumber'] ?? '';
        $detailLists['recruit_priority'] = $priority;
        $detailLists['other_requirement'] = $result['remarks'] ?? '';
        $detailLists['hcreason'] = str_replace(["招聘", "recruitment"], ["新增", "Add"], $this->getTranslation()->_('hc_reason_type_' . $result['reason_type']));
        $detailLists[''] = $result['reason'] ?? '';

        if ($result['state_code'] == enums::$audit_status['dismissed']) {
            $request = AuditApplyModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type:",
                'bind' => [
                    'type'  => enums::$audit_type['HC'],
                    'value' => $auditId,
                ],
            ]);
            $detailLists = array_merge($detailLists, [
                'reject_reason1' => $request ? $request->getRejectReason() : '',
            ]);
        }

        $this->getCountryDetail($detailLists, $result);

        //以前是写死的 32 部门 is_demandnumber_show  =1  现在 判断是否有 env  没有的话 默认 32
	    $envModel = new SettingEnvServer();
//	    $bulky_departId = $envModel->getSetVal('dept_network_bulky_operations_id');
//	    $bulky_departId = !empty($bulky_departId) ? $bulky_departId : 0;
	    $departId = $envModel->getSetVal('dept_network_operations_id');
//	    $is_demanders_list[]= empty($departId) ? 32 : $departId;
//	    $is_demanders_list[]= empty($bulky_departId) ? '' : $bulky_departId;
        $is_demanders_id              = $result['department_id'] ?? 0;
        $envModel                     = new SettingEnvServer();
        $editHcDemandNumDepartmentIds = $envModel->getSetVal('edit_hc_demand_num_dept_ids', ',');
	
	    $head = [
            'title'       => $this->auditlist->getAudityType(enums::$audit_type['HC']),
            'id'          => $result['hc_id'],
            'staff_id'    => $result['submitter_id'],
            'type'        => enums::$audit_type['HC'],
            'created_at'  => $result['createtime'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['state_code'],
            'priority_id' => $priority,
            'serial_no'   => $result['serial_no'] ?? '',
            'demandnumber' => $result['demandnumber'] ?? '',
            'is_demandnumber_show' => in_array($is_demanders_id, array_filter($editHcDemandNumDepartmentIds)) ? 1 : 0,
        ];

        //当前用户是否已经审批
        if ($comeFrom == 2) { //获取审批人的审批状态
            $approverInfo = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $user,
                ],
            ]);
            
	        //获取network operations ID
	        if (!(!empty($approverInfo) && isset($approverInfo->node_department_id) && $approverInfo->node_department_id == $departId)) {
		        $detailLists['hc_budget'] = $result['request_info'] ?? '';
	        }

            $infos = AuditApprovalModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and approval_id = :approval_id: and deleted = 0",
                'bind' => [
                    'type'  => enums::$audit_type['HC'],
                    'value' => $auditId,
                    'approval_id' => $user,
                ],
                'order' => 'id desc',
            ]);

            $state = $infos instanceof AuditApprovalModel ? $infos->getState() : 0;
            $option = ['beforeApproval' => "1,2,5", 'afterApproval' => '0'];
        } else { //获取申请的审批状态
        	
	        //刘光辉需求
	        //https://l8bx01gcjr.feishu.cn/docs/doccnnUugShqAiYyaXTY7RUrkZe
	        if (!($staff_info['node_department_id'] == $departId && $user == $result['submitter_id'])) {
		        $detailLists['hc_budget'] = $result['request_info'] ?? '';
	        }

            $state = $result['approval_state_code'];
            $option = ['beforeApproval' => '3', 'afterApproval' => '0'];
        }

        $head['options'] = $this->getStaffOptions(['option' => $option, 'status' => $state]);
        $returnData['data']['head']   = $head;
        $returnData['data']['detail'] = $this->format($detailLists);
        $returnData['data']['priorityList'] = $this->getPriorityList();
        $returnData['data']['leave_staff_list'] = $this->getHrHcLeaveStaffListByHcId($auditId);
        $returnData['data']['store_data'] = $this->getDisplayStoreData($result);
        return $returnData;
    }

    public function getCountryDetail(&$detailLists, $result)
    {
    }

    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info = $this->hc->infoHc(['hc_id' => $auditId]);
        if (empty($info)) {
            return '';
        }

        //获取JD、网点
        $storeInfo = (new SysStoreServer())->getStoreName([$info['worknode_id']]);
        $jdInfo    = $this->hc->infoJD(['job_id' => $info['job_id']]);
        $param     = [
            [
                'key'   => "store",
                'value' => $storeInfo[$info['worknode_id']] ?? 'Head Office',
            ],
            [
                'key'   => "selectJD",
                'value' => $jdInfo['job_name'],
            ],
            [
                'key'   => "demandnumber",
                'value' => $info['demandnumber'],
            ],
        ];
        return $param ?? "";
    }

    /**
     * 审批完成回调方法
     * @param int $auditId
     * @param int $state
     * @param array $extend
     * @param bool $isFinal
     * @return mixed|void`
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $info = $this->hc->infoHc(['hc_id' => $auditId]);
        if ($isFinal) {
            //更新hr_myApproval、hr_approval
            $this->processHcFinalApproval($auditId, $state, $extend);
            $data = [
                'approval_completion_time' => gmdate('Y-m-d H:i:s', time()),
                'approval_stage'           => $info['approval_stage'] + 1,
            ];
            $data = array_merge($data, $this->getCountryPropertyData($info));
            if ($state == 2) {
                //[1]更新状态
                $data = array_merge($data,[
                    'state_code'               => 2,
                    'approval_state_code'      => 6,
                ]);

                $this->getDI()->get('db')->updateAsDict(
                    'hr_hc',
                    $data,
                    "hc_id = {$auditId}"
                );
                //[2]Email
                $this->sendMail($auditId);

            } elseif ($state == 3) {
                //hc主表最终状态
                $data = array_merge($data,[
                    'state_code'               => 1,
                    'approval_state_code'      => 5,
                ]);

                $this->getDI()->get('db')->updateAsDict(
                    'hr_hc',
                    $data,
                    "hc_id = {$auditId}"
                );
                //处理hc列表
                $this->processHcApproval($auditId, $state, $extend);
            } else {
                //hc主表最终状态
                $data = array_merge($data,[
                    'state_code'               => 1,
                    'approval_state_code'      => 4,
                ]);

                $this->getDI()->get('db')->updateAsDict(
                    'hr_hc',
                    $data,
                    "hc_id = {$auditId}"
                );
            }
        } else {
            //非最终状态
            $this->getDI()->get('db')->updateAsDict(
                'hr_hc',
                [
                    'approval_stage'        => $info['approval_stage'] + 1,
                    'approval_state_code'   => 7,
                    'approval_completion_time' => gmdate('Y-m-d H:i:s', time()),
                ],
                "hc_id = {$auditId}"
            );

            //处理hc列表
            $this->processHcApproval($auditId, $state, $extend);
        }
    }

    /**
     * 固化统计数据
     * @param $info
     * @return array
     */
    public function getCountryPropertyData($info)
    {
        return [];
    }
	
	
	public function getCcList($dep_env = '',$cc_staff_env = '',$cc_list=[],$department_v3=[]){
		try {
			if (empty($dep_env) || empty($cc_staff_env) || empty($department_v3)) {
				return $cc_list;
			}
			$settingEnvServer = new SettingEnvServer();
			//查询部门id
			$dep_env_ids = $settingEnvServer->getSetVal($dep_env);
			//查询需要抄送的人
			$cc_env_ids = $settingEnvServer->getSetVal($cc_staff_env);
			if (!empty($cc_env_ids) && !empty($dep_env_ids) && !empty($department_v3)) {
				$hc_cc_department = explode(',', $dep_env_ids);// 获取所有的部门 拆分成数组
				//这里获取交集如果存在 就代表 需要抄送给这个人
				if (array_intersect($department_v3, $hc_cc_department)) {
					$cc_env  = explode(',', $cc_env_ids);
					$cc_list = array_merge($cc_list, $cc_env);
				}
			}
		}catch (\Exception $e) {
			$this->getDI()->get("logger")->write_log("getCcList "
			                                         . $e->getFile()
			                                         . " line " . $e->getLine()
			                                         . " message " . $e->getMessage()
			                                         . " trace " . $e->getTraceAsString(), "error");
		}
		return $cc_list;
	}
	
	/**
     * 发送抄送eMail
     * @param $hc_id
     * @return bool
     */
    public function sendMail($hc_id)
    {
        $params = $this->hc->infoHc(['hc_id' => $hc_id]);

        //获取邮件抄送列表
        $envModel = new SettingEnvServer();
        $toUsers = $envModel->getSetVal('hc_cc_email_list');

        $content = $this->getTemplate($params);
        if ($toUsers && is_string($toUsers)) {
            $toUsers = explode(',', $toUsers);
        }

        if (empty($toUsers)) {
            return false;
        }

        $title = "Employment application list（HC details list）";
        $ret   =  (new MailServer())->send_mail($toUsers, $title, $content);

        $this->getDI()->get('logger')->write_log("send cc mail to:" . implode(',', $toUsers) .', content is:' .  $content.',result is:'. $ret, 'info');
        return true;
    }

    /**
     * 获取邮件模板
     * @param $params
     * @return string
     */
    public function getTemplate($params)
    {
        $date = date('Y-m-d', time());
        //解析部门
        $department = $this->department->getDepartmentNameById($params['department_id']);
        //解析网点
        $storeName = $params['worknode_id'] == -1 ? ['name' => 'Head Office']: $this->staff->getStaffStoreInfo($params['worknode_id']);
        //优先级转义
        $priority = $this->getPriorityList($params['priority_id']);
        //原因类型
        $reason = $this->getTranslation()->_('hc_reasontype_' . $params['reason_type']);

        return "The following are the newly added employment applications on {$date}
            </br>     
            <table cellpadding='3' border='1' cellspacing='0' width='100%' style='font-size: 20px'> 
                <tr>
                    <th>HC ID</th>
                    <th>Priority</th>
                    <th>JD  Name</th>
                    <th>Department</th>
                    <th>Branch</th>
                    <th>Deadline</th>
                    <th>creator</th>
                    <th>Total Demand</th>
                    <th>Employment reason</th>
                </tr>
                <tr>
                    <td>{$params['hc_id']}</td>
                    <td>{$priority}</td>
                    <td>{$params['job_name']}</td>
                    <td>{$department}</td>
                    <td>{$storeName['name']}</td>
                    <td>{$params['expirationdate']}</td>
                    <td>{$params['submitter_id']}</td>
                    <td>{$params['demandnumber']}</td>
                    <td>{$reason}</td>
                </tr>
            </table>
            </br>  
            The system automatically sends emails. Please do not reply.
        ";
    }

    /**
     * @description 获取审批流参数
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null): array
    {
        //[1]获取HC申请详情
        $hcInfo = HrHcModel::findFirst([
            "hc_id = :hc_id:",
            "bind" => [
                "hc_id" => $auditId,
            ],
        ]);
        if (empty($hcInfo)) {
            throw new \Exception($this->getTranslation()->_("4008"));
        }


        return [
            'request_department_id' => $hcInfo->department_id,
            'submitter_max_Level'   => $this->getSubmitterMaxLevel($hcInfo->submitter_id),
            'sys_store_id'          => $hcInfo->worknode_id,
            'department_id'         => $hcInfo->department_id,
            'submitter_position'    => $hcInfo->job_title,
            'reason_type'           => $hcInfo->reason_type,
        ];
    }

    /**
     * @description 申请人最大级别
     * @param $staff_info_id
     * @return mixed
     */
    public function getSubmitterMaxLevel($staff_info_id)
    {
        //Clevel级别所辖部门
        $bossDeptList = SysDepartmentModel::find([
            'conditions' => 'type in (4,5) and manager_id = :manager_id: and deleted = 0',
            'bind' => [
                'manager_id'=> $staff_info_id,
            ],
            'columns' => "id",
        ])->toArray();

        //GM所辖部门
        $groupMasterList = SysDepartmentModel::find([
            'conditions' => 'type = :type: and manager_id = :manager_id: and deleted = 0',
            'bind' => [
                'type'      => 1,
                'manager_id'=> $staff_info_id,
            ],
            'columns' => "id",
        ])->toArray();

        //获取最大部门级别
        $departmentList = SysDepartmentModel::findFirst([
            'conditions' => 'level between 1 and 4 and manager_id = :manager_id: and deleted = 0',
            'bind' => [
                'manager_id'=> $staff_info_id,
            ],
            'columns' => "min(level) as department_level",
        ]);

        if (!empty($bossDeptList)) {
            return WorkflowEnums::DEPARTMENT_MANAGER_C_LEVEL;
        } else if (!empty($groupMasterList)) {
            return WorkflowEnums::DEPARTMENT_MANAGER_BU_LEVEL;
        } else {

            if (empty($departmentList)) {
                return false;
            }

            switch ($departmentList->department_level) {
                case 1:
                    $departmentLevel = WorkflowEnums::DEPARTMENT_MANAGER_DEPARTMENT_LEVEL_1;
                    break;
                case 2:
                    $departmentLevel = WorkflowEnums::DEPARTMENT_MANAGER_DEPARTMENT_LEVEL_2;
                    break;
                case 3:
                    $departmentLevel = WorkflowEnums::DEPARTMENT_MANAGER_DEPARTMENT_LEVEL_3;
                    break;
                case 4:
                    $departmentLevel = WorkflowEnums::DEPARTMENT_MANAGER_DEPARTMENT_LEVEL_4;
                    break;
                default:
                    $departmentLevel = false;
                    break;
            }
            return $departmentLevel;
        }
    }

    /**
     * 处理hc列表
     * @param $auditId
     * @param $state
     * @param $extend
     */
    public function processHcFinalApproval($auditId, $state, $extend)
    {
        $staffId = $extend['staff_id'];

        //系统间状态转义
        if ($state == 2) {
            $thisState = 6;
        } else if ($state == 3) {
            $thisState = 5;
        } else {
            $thisState = 4;
        }

        if ($staffId != enums::SYSTEM_STAFF_ID) {
            $app = new HrApproval();
            $app->hc_id = $auditId;
            $app->submitter_id = $staffId;
            $app->state_code = $thisState;
            $app->save();
        }

        //my approval => 6 or 5 or 4
        $myApp = HrMyApproval::findFirst([
            'conditions' => "hc_id = :hc_id: and state_code = 7",
            'bind' => [
                'hc_id'  => $auditId,
            ],
        ]);
        if (!empty($myApp)) {
            $myApp->update(['state_code' => $thisState]);
        }
    }

    /**
     * 处理hc列表
     * @param $auditId
     * @param $state
     * @param $extend
     */
    public function processHcApproval($auditId, $state, $extend)
    {
        $staffId      = $extend['staff_id'];
        $isStartNode  = $extend['is_start_node'];
        $approvals    = $extend['approval'];

        //审批时 & 非起始节点时 approval insert 6
        if ($staffId != enums::SYSTEM_STAFF_ID) {
            $app = new HrApproval();
            $app->hc_id = $auditId;
            $app->submitter_id = $staffId;
            $app->state_code = $isStartNode ? 8 : 6;
            $app->staff_ids = isset($approvals) && $approvals ? implode(',',$approvals) : '';
            $app->save();
        }

        //my approval update 6
        if ($state == 2) {
            $thisState = 6;
        } else if ($state == 3) {
            $thisState = 5;
        } else {
            $thisState = 4;
        }
        $myApp = HrMyApproval::find([
            'conditions' => "hc_id = :hc_id: and state_code = 7",
            'bind' => [
                'hc_id'  => $auditId,
            ],
        ]);
        $this->getDI()->get('logger')->write_log("process_hc_approval: hc_id - {$auditId}, staff_id - {$staffId}, state - {$state}, extend - " . json_encode($extend, JSON_UNESCAPED_UNICODE), 'info');

        $res = 'myApp data is null';
        if (!empty($myApp)) {
            $res = $myApp->update(['state_code' => $thisState]);
        }

        $this->getDI()->get('logger')->write_log("process_hc_approval: state_code - {$thisState}, HrMyApproval - update_result: ".$res , 'info');

        //my approval insert 7
        if ($state == 2 && isset($extend['approval']) && !empty($extend['approval']) && is_array($extend['approval'])) {
            foreach ($extend['approval'] as $v) {
                if ($v == enums::SYSTEM_STAFF_ID) {
                    continue;
                }

                $app = new HrMyApproval();
                $app->hc_id = $auditId;
                $app->submitter_id = $v;
                $app->state_code = 7;
                $app->save();
            }
        }
    }

    /**
     * 查询在职、待入职、hc剩余人数
     * @param $month
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return array
     */
    public function getHcBudgetCount($month, $departmentId, $storeId, $jobTitle): array
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return [];
        }
        $share_department_position = $this->share_department_position;
        //如果是共享部门
        if(isset($share_department_position[$departmentId])) {
            $share_position_arr = $share_department_position[$departmentId] ?? [];
            //如果是共享职位
            if(!empty($share_position_arr) && in_array($jobTitle, $share_position_arr) ) {
                $jobTitle = $share_position_arr;
            } else {
                $jobTitle = [$jobTitle];
            }
        } else {
            $jobTitle = [$jobTitle];
        }
        //获取在职人数
        $onJobCnt = $this->getOnJobStaffCount($departmentId, $storeId, $jobTitle);

        //获取待入职人数
        $pendingEntryCnt = $this->getPendingEntryCount($departmentId, $storeId, $jobTitle);

        //获取剩余人数  相当于招聘中的人数
        $surplusCnt = $this->getSurplusCount($departmentId, $storeId, $jobTitle);

        //4.获取已提交的HC总人数（待审批）
        $pendingCon = $this->getBudgetAdoptCount($departmentId, $storeId, $jobTitle);

        //当月预算人数
        //$curMonthBudgetCnt = $this->getCurrentMonthBudgetCount($month, $departmentId, $storeId, $jobTitle);

        //当月计划人数
        //当月计划人数 = 新增招聘人数+在职人数+待入职人数+招聘中人数
        //$budget = $this->getHrStaffing($month, $departmentId, $storeId, $jobTitle);

        //当月已提交的新增招聘人数
        //$adoptCon = $this->getCurrentMothBudgetAdoptCount($month, $departmentId, $storeId, $jobTitle);

        return [
            'on_job'        => $onJobCnt,//在职人数
            'pending_entry' => $pendingEntryCnt,//待入职人数
            'surplus'       => $surplusCnt,//招聘中的人数
            'pendingCon'   => $pendingCon, //已提交待审批
//            'cur_month_budget' => $curMonthBudgetCnt,
//            'cur_month_planed' => $adoptCon + $onJobCnt + $pendingEntryCnt + $surplusCnt,
//            'adopt_count' => $adoptCon
        ];
    }

    /**
     * @description 获取在职人数
     * 指定部门、职位、工作所在国家, 在职、在编、不含子账号、不含实习生、不含待离职
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getOnJobStaffCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        $conditions = 'node_department_id = :node_department_id: and job_title in ({job_title_id:array}) and
                       formal in (1,4) and state = 1 and is_sub_staff = 0 and wait_leave_state = 0 and 
                       working_country = :working_country:';
        $bind =  [
            'node_department_id' => $departmentId,
            'job_title_id' => $jobTitle,
            'working_country' => WorkingCountryEnums::getWorkingCountry(),
        ];

        $now_store_id = '';
        $share_department_position = $this->share_department_position;
        if(!empty($storeId) && !isset($share_department_position[$departmentId])) {
            $conditions .= ' and sys_store_id = :store_id:';
            $bind['store_id'] = $storeId;
            $now_store_id = $storeId;
        }
        //获取指定部门、网点、职位的，在职、在编、非子账号的人数
        //v8919+不要待离职的+实习生
        $onJobCou = HrStaffInfoModel::find([
            'conditions' => $conditions,
            'bind' =>$bind,
            'columns' => "staff_info_id",
        ])->count();


        $transfer_num = $this->getJobTransferCount($departmentId, $now_store_id, $jobTitle);
        //在职 - 待转岗人数
        $res = $onJobCou - $transfer_num;
        if ($res < 0) {
            $res = 0;
        }
        return $res;
    }

    /**
     * 获得待转岗人数
     * @param $department_id
     * @param $jobTitleIds
     * @return int
     */
    public function getJobTransferCount($department_id,$store_id,$jobTitleIds){
        //审核通过，待转岗
        $conditions = 'approval_state =2 and state=1 and current_department_id = :department_id: and current_position_id and current_position_id in ({job_title_id:array}) ';
        $bind = [];
        $bind['department_id'] = $department_id;
        $bind['job_title_id'] = $jobTitleIds;

        if(!empty($store_id)){
            $conditions .= ' and current_store_id = :store_id:';
            $bind['store_id'] = $store_id;
        }

        return JobTransferModel::count(
            [
                'conditions' => $conditions,
                'bind'=>$bind,
            ]
        );
    }

    /**
     * @description 获取待入职人数 , 即：已发offer人数
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getPendingEntryCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        $share_department_position = $this->share_department_position;

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("count(1) as cou");
        $builder->from(['h' => HrHcModel::class]);
        $builder->leftJoin(HrEntryModel::class,'e.hc_id = h.hc_id','e');
        $builder->andWhere('e.deleted = :deleted:', ['deleted' => Enums::IS_DELETED_NO]);
        $builder->andWhere('e.status = :status:', ['status' => HrEntryModel::STATUS_TO_BE_EMPLOYED]);
        $builder->andWhere('h.department_id = :dept_id:', ['dept_id' => $departmentId]);
        $builder->inWhere('h.job_title', $jobTitle);
        if(!empty($storeId) &&  !isset($share_department_position[$departmentId])) {
            $builder->andWhere('h.worknode_id = :store_id:', ['store_id' => $storeId]);
        }
        $totalCount = $builder->getQuery()->execute()->getFirst();

        return intval($totalCount->cou);
    }

    /**
     * 获取招聘中人数
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getSurplusCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }
        $conditions = 'state_code = 2 and department_id = :department_id: and
                            job_title in ({job_title_id:array})  and deleted = 1';
        $bind = [
            'department_id' => $departmentId,
            'job_title_id' => $jobTitle,
        ];
        $share_department_position = $this->share_department_position;
        if(!empty($storeId) &&  !isset($share_department_position[$departmentId])) {
            $conditions .= ' and worknode_id = :store_id:';
            $bind['store_id'] =  $storeId;
        }
        $count = HrHcModel::findFirst([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => "sum(surplusnumber) as cou",
        ])->toArray();

        return $count['cou'] ?? 0;
    }

    /**
     * 获取职位列表
     * @param array $paramIn
     * @return array
     */
    public function getJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $departmentId = $this->processingDefault($paramIn, 'department_id',2);

        //[2]获取部门职位关联数据
        $builder = $this->modelsManager->createBuilder()
            ->columns('hr_job_department_relation.job_id as code, hr_job_title.job_name as value,hr_job_department_relation.working_day_rest_type,hr_job_title.id as job_title_id')
            ->addFrom(HrJobDepartmentRelationModel::class, 'hr_job_department_relation')
            ->leftJoin(HrJobTitleModel::class, 'hr_job_department_relation.job_id = hr_job_title.id', 'hr_job_title')
            ->where('hr_job_title.status = 1')
            ->andWhere('hr_job_department_relation.department_id = :sys_department_id:', ['sys_department_id' => $departmentId]);
        $departmentData = $builder->getQuery()->execute()->toArray();
        $server = (new SalaryServer($this->lang, $this->timezone));

        foreach ($departmentData as $key => &$value) {
            $value['is_show_language_ability'] = false;
            //泰国 非一线 语言能力
            if (isCountry('TH') && !$server->isFirstLineJob($departmentId, $value['job_title_id'])) {
                $value['is_show_language_ability'] = true;
            }

            if(!empty($value['working_day_rest_type'])) {
                $working_day_rest_type = explode(',', $value['working_day_rest_type']);
                foreach ($working_day_rest_type as $w_k => $w_v) {
                    $departmentData[$key]['working_day_rest_type_arr'][] = [
                        'key' => $w_v,
                        'value' => $this->getTranslation()->_('working_day_rest_type_' . $w_v),
                    ];
                }
            } else {
                $departmentData[$key]['working_day_rest_type_arr'] = [];
            }
        }
        return $this->checkReturn(['data'=> $departmentData]);
    }

    /**
     * 获取部门列表
     * @param array $paramIn
     * @return array
     */
    public function getDepartmentList($paramIn = []): array
    {
        $staff_id = $this->processingDefault($paramIn, 'staff_id');

        //[1]查询申请人部门
        $ac = new ApiClient('winhr_rpc', '', 'getHcDepartmentList', $this->lang);
        $ac->setParams([
            'staff_id' => $staff_id,
        ]);
        $departmentSet = $ac->execute();

        $returnArr = [];
        //c-level or GM可直接查出全部的部门
        if (isset($departmentSet['result']) && $departmentSet['result'] && is_array($departmentSet['result'])) {
            $returnArr = SysDepartmentModel::find([
                'conditions' => 'id in({department_id:array}) and deleted = 0',
                'bind' => [
                    'department_id'    => $departmentSet['result'],
                ],
                'columns' => "id,name",
                'order' => 'name asc',
            ])->toArray();
        }
        return $returnArr;
    }

    /**
     * 查询用户权限范围内的部门树
     * @param array $paramIn
     * @return array
     */
    public function getMyDepartmentTree($paramIn = [])
    {
        $staffId = $this->processingDefault($paramIn, 'staff_id');
        $name = $this->processingDefault($paramIn, 'name');

        //查询申请人部门
        $ac = new ApiClient('winhr_rpc', '', 'getHcDepartmentList', $this->lang);
        $ac->setParams(['staff_id' => $staffId]);
        $departmentSet = $ac->execute();
        $res = $departmentSet['result'] ?? [];
        if(empty($res) || empty($name)) {
            return $res;
        }

        $list = $this->convertRecursionToList($res);
        $dpeartmentIds = array_column($list, 'value');
        $dpeartments = $this->department->findDepartmentsByIdsAndName($name, $dpeartmentIds);
        $res = [];
        if($dpeartments){
            $dpeartmentIds = array_column($dpeartments, 'id');
            $dpeartmentMap = array_column($list, null, 'value');
            foreach($dpeartmentIds as $id) {
                if($dpeartmentMap[$id]) {
                    $res[] = [
                        "value" => $dpeartmentMap[$id]['value'],
                        "label" => $dpeartmentMap[$id]['label'],
                        "ancestry" => $dpeartmentMap[$id]['ancestry'],
                        "ancestry_text" => $dpeartmentMap[$id]['ancestry_text'],
                    ];
                }
            }
        }
        return $res;
    }

    public function convertRecursionToList($data){
        if(empty($data)){
            return [];
        }
        $res = [];
        foreach($data as $val){
            $res[] = $val;
            if(!empty($val['children'])) {
                $childrenRes = $this->convertRecursionToList($val['children']);
                $res = array_merge($childrenRes, $res);
            }
        }
        return $res;
    }

    /**
     * 获取参考数据
     * @param array $paramIn
     * @return array
     */
    public function getHcReferences($paramIn = []): array
    {
        //[1]获取参数
        $departmentId = $this->processingDefault($paramIn, 'department_id');
        $storeId      = $this->processingDefault($paramIn, 'store_id');
        $jobTitleId   = $this->processingDefault($paramIn, 'job_title_id');

        $config = (new SettingEnvServer)->getSetVal('hc_budget_configured');
        if (!empty($config)) { //执行 hc 数据汇总计算task
            //[2]获取预算
            $total = HcStatisticServer::getInstance()->getHrStaffing($departmentId, $storeId, $jobTitleId);

            //[3]获取剩余人数
            $leftTotal = HcStatisticServer::getInstance()->getSurplusHcRequestCount($total, $departmentId, $storeId, $jobTitleId);
        } else {
            //[2]获取预算
            $total = $this->getHrStaffing($departmentId, $storeId, $jobTitleId);

            //[3]获取剩余人数
            $leftTotal = $this->getSurplusHcRequestCount($total, $departmentId, $storeId, $jobTitleId);
        }

        $ret = [
            'total' => $total,
            'left'  => $leftTotal ?? 0,
        ];
        return $this->checkReturn(['data' => $ret]);
    }


    /**
     * 获取预算人数
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return int
     */
    public function getHrStaffing($departmentId, $storeId, $jobTitle) : int
    {

        $this->getDI()->get('logger')->write_log('getHrStaffing: params - departmentId:' .$departmentId.'-storeId:'. $storeId.'-jobTitle:'. $jobTitle , 'info');

        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        //获取全部的部门、职位、网点
        $builder = $this->modelsManager->createBuilder();
//        $builder->columns("sum(d.budget_count) as cou");
//        $builder->from(['s' => HRHCBudgetModel::class]);
//        $builder->leftJoin(HRHCBudgetItemModel::class,'s.id = d.budget_id','d');
//        $builder->andWhere('s.status = :status:', ['status' => 2]);
//        $builder->andWhere('s.budget_month = :budget_month:', ['budget_month' => $budgetMonth]);
//        $builder->andWhere('d.dept_id = :dept_id:', ['dept_id' => $departmentId]);
//        $builder->andWhere('d.job_title_id = :job_title_id:', ['job_title_id' => $jobTitle]);
//        $builder->andWhere('d.store_id = :store_id:', ['store_id' => $storeId]);
//        $builder->groupBy("d.dept_id,d.job_title_id,d.store_id");

        $builder->columns("count");
        $builder->from(HRStaffingModel::class);
        $builder->andWhere('dept_id = :dept_id:', ['dept_id' => $departmentId]);
        $share_department_position = $this->share_department_position;
        if(isset($share_department_position[$departmentId])) {

            $share_position_arr = $share_department_position[$departmentId] ?? [];
            if( !empty($share_position_arr) && in_array($jobTitle, $share_position_arr)){
                $builder->inWhere('job_title_id', $share_position_arr);
            } else {
                $builder->andWhere('job_title_id = :job_title_id:', ['job_title_id' => $jobTitle]);
            }
        } else {
            if(!empty($storeId) && !isCountry('TH')) {
                $builder->andWhere('store_id = :store_id:', ['store_id' => $storeId]);
            }
            $builder->andWhere('job_title_id = :job_title_id:',  ['job_title_id' => $jobTitle]);
        }

        //$builder->groupBy("dept_id,job_title_id,store_id");
        $builder->andWhere('count > 0');
        $builder->orderBy('id desc');
        $builder->limit(1);
        $list = $builder->getQuery()->execute()->getFirst();
        $count = 0;
        if (!empty($list)) {
            $listArr = $list->toArray();
            $count  = $listArr['count'] ?? 0;
        }

        $this->getDI()->get('logger')->write_log('getHrStaffing: params - departmentId:' .$departmentId.' share_department_position:'.json_encode($this->share_department_position ?? [], JSON_UNESCAPED_UNICODE).' storeId:'. $storeId.'-jobTitle:'. $jobTitle .' count:'.$count, 'info');

        return $count;
    }

    /**
     * 当月已提交的新增招聘人数
     * @param $month
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return int
     */
    public function getCurrentMothBudgetAdoptCount($month, $departmentId, $storeId, $jobTitle) {
        if (empty($departmentId) || empty($storeId) || empty($jobTitle) || empty($month)) {
            return 0;
        }

        $result = $this->modelsManager->createBuilder()
            ->columns('
                    hr_hc_budget.budget_month,
                    hr_hc_budget_item.dept_id as item_dept_id,
                    hr_hc_budget_item.job_title_id as item_job_title_id,
                    hr_hc_budget_item.store_id as item_store_id,
                    sum(hr_hc_budget_item.budget_count) as item_budget_count
                    ')
            ->from(['hr_hc_budget' => HRHCBudgetModel::class])
            ->innerJoin(HRHCBudgetItemModel::class,'hr_hc_budget.id=hr_hc_budget_item.budget_id','hr_hc_budget_item')
            ->where('hr_hc_budget.status in (1,2)')
            ->andWhere('hr_hc_budget.budget_month = :budget_month:', ['budget_month' => $month])
            ->andWhere('hr_hc_budget_item.dept_id = :dept_id:',['dept_id' => $departmentId])
            ->andWhere('hr_hc_budget_item.job_title_id = :job_title_id:',['job_title_id' => $jobTitle])
            ->andWhere('hr_hc_budget_item.store_id = :store_id:',['store_id' => $storeId])
            ->getQuery()->getSingleResult()->toArray();

        return $result['item_budget_count'] ?? 0;
    }

    /**
     * 获取本月HC申请数
     * @param $budgetMonth
     * @param $departmentId
     * @param $storeId
     * @param $jobTitleId
     * @return int
     */
    public function getCurrentMonthHcRequestCount($budgetMonth, $departmentId, $storeId, $jobTitleId): int
    {
        if (empty($departmentId) || empty($storeId) || empty($jobTitleId) || empty($budgetMonth)) {
            return 0;
        }

        $startTimeTmp = strtotime($budgetMonth . "-01 00:00:00"); //当前月1号东八区
        $startTimeTH = date("Y-m-d H:i:s", $startTimeTmp - 8 * 3600); //当前月1号0时区
        $endTime = strtotime(date("Y-m-d H:i:s", $startTimeTmp) . " +1 month"); //下月1号东八区
        $endTimeTH = date("Y-m-d H:i:s", $endTime - 8 * 3600);//下月1号0时区

        //最终审批通过但是作废的审批，申请人数要算到已申请的预算里
        //待审批 + 招聘中 + 已招满 + 作废的
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("sum(demandnumber) as cou");
        $builder->from(['s' => HrHcModel::class]);
        $builder->andWhere('s.state_code = 2 or s.state_code = 3 or s.state_code = 1 and s.approval_state_code = 7 or s.state_code = 4 and approval_completion_time is not null');
        $builder->andWhere('s.department_id = :dept_id:', ['dept_id' => $departmentId]);
        $builder->andWhere('s.job_title = :job_title_id:', ['job_title_id' => $jobTitleId]);
        $builder->andWhere('s.worknode_id = :store_id:', ['store_id' => $storeId]);
        $builder->andWhere('s.createtime >= :start_time:', ['start_time' => $startTimeTH]);
        $builder->andWhere('s.createtime < :end_time:', ['end_time' => $endTimeTH]);
        $count = $builder->getQuery()->execute()->getFirst()->toArray();

        return $count['cou'] ?? 0;
    }


    /**
     * 同步给hr,只是招聘中
     * @param $hc_id
     * @return bool|mixed|null
     */
    public function syncToBuddy($hc_id,$state_code=0)
    {
        //合作到期，停用次功能
        return;

        $rpcClient = new ApiClient('winhr_rpc', '', 'hc_sync_to_buddy', $this->lang);
        $rpcClient->setParams(['hc_id' => $hc_id, 'state_code' => $state_code]);
        return $rpcClient->execute();
    }

    /**
     * @description 获取HC预算剩余数量
     * @param $total
     * @param $departmentId
     * @param $jobTitleId
     * @return mixed
     */
    public function getSurplusHcRequestCount($total, $departmentId, $storeId, $jobTitleId)
    {
        if($total<=0 || empty($departmentId) || empty($jobTitleId)) {
            return 0;
        }
        $share_department_position = $this->share_department_position;
        if(isset($share_department_position[$departmentId])) {
            $share_position_arr = $share_department_position[$departmentId];
            if(!empty($share_position_arr) && in_array($jobTitleId, $share_position_arr)) {
                $jobTitle = $share_position_arr;
            } else {
                $jobTitle = [$jobTitleId];
            }
        } else {
            $jobTitle = [$jobTitleId];
        }
        //HC预算剩余数量:预算人数 - 在职人数 - 待入职人数 - 招聘中人数 - 已提交HC总人数(待审批)
        //1.获取在职人数
        $onJobCnt = $this->getOnJobStaffCount($departmentId, $storeId, $jobTitle);
        //2.获取待入职人数
        $pendingEntryCnt = $this->getPendingEntryCount($departmentId, $storeId, $jobTitle);
        //3.获取招聘中人数
        $surplusCnt = $this->getSurplusCount($departmentId, $storeId, $jobTitle);
        //4.获取已提交的HC总人数（待审批）
        $adoptCon = $this->getBudgetAdoptCount($departmentId, $storeId, $jobTitle);

        //HC预算剩余数量 100000-37-11-173
        $leftCount = $total-$onJobCnt-$pendingEntryCnt-$surplusCnt-$adoptCon;

        $this->logger->write_log(sprintf("getSurplusHcRequestCount %s %s %s: onJobCnt: %s,pendingEntryCnt: %s,surplusCnt: %s,adoptCon:%s,leftCount:%s.",
            $departmentId, $storeId, $jobTitleId, $onJobCnt, $pendingEntryCnt, $surplusCnt, $adoptCon,$leftCount), 'info');
        return $leftCount;
    }

    /**
     * @desc 获取已提交的HC总人数（待审批）
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return int
     */
    public function getBudgetAdoptCount($departmentId, $storeId, $jobTitle)
    {
        //approval_state_code = 7  state_code = 1
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }
        $conditions = 'state_code = 1 and approval_state_code = 7 and department_id = :department_id: and
                            job_title in ({job_title_id:array})';
        $bind = [
            'department_id' => $departmentId,
            'job_title_id' => $jobTitle,
        ];

        $share_department_position = $this->share_department_position;

        if(!empty($storeId) && !isset($share_department_position[$departmentId])) {
            $conditions .= ' and worknode_id = :store_id:';
            $bind['store_id'] =  $storeId;
        }
        $count = HrHcModel::findFirst([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => "sum(demandnumber) as cou",
        ])->toArray();

        return $count['cou'] ?? 0;
    }


    /**
     * 获取离职停职待离职员工列表
     * @param array $param
     * @return array
     */
    public function getHcStaffLeaveList($param = []) {
        try {

            $param['level_date'] = date('Y-m-d', strtotime('-31 day', time()));
            $list = $this->hc->getStaffLeaveList($param);

            $hr_hc_staff_info_ids = $this->hc->getHrHcLeaveStaffIds();

            $optional_list = [];
            $not_optional_list = [];

            foreach ($list as $key => $value) {
                $list[$key]['leave_date'] = date('Y-m-d',strtotime($value['leave_date']));
                $leave_date = !empty($value['leave_date']) ? date('Y-m-d',strtotime($value['leave_date'])) : $value['leave_date'];
                $stop_duties_date = !empty($value['stop_duties_date']) ? date('Y-m-d',strtotime($value['stop_duties_date'])) : $value['stop_duties_date'];
                $list[$key]['leave_date'] = $leave_date;
                $list[$key]['stop_duties_date'] = $stop_duties_date;
                $list[$key]['remark'] = '';
                if($value['state'] == 2) {
                    $list[$key]['remark'] = str_replace('{xxxx-xx-xx}', $leave_date, $this->getTranslation()->_('4027'));
                }
                if($value['state'] == 3) {
                    $list[$key]['remark'] = str_replace('{xxxx-xx-xx}', $stop_duties_date, $this->getTranslation()->_('4025'));
                }
                if($value['state'] == 1 && $value['wait_leave_state'] == 1) {
                    $list[$key]['remark'] = str_replace('{xxxx-xx-xx}', $leave_date, $this->getTranslation()->_('4026'));
                }

                if(!empty($hr_hc_staff_info_ids) && in_array($value['staff_info_id'], $hr_hc_staff_info_ids)) {
                    $not_optional_list[] = $list[$key];
                } else {
                    $optional_list[] = $list[$key];
                }
            }

            $data = ['optional_list' => $optional_list, 'not_optional_list' => $not_optional_list];
            return $data;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('HcServer:getHcStaffLeaveList:'. $e->getMessage().'-----'.$e->getLine());
            $data = ['optional_list' => [], 'not_optional_list' => []];
            return $data;
        }
    }

    /**
     * 获取hc申请离职人员list
     * @param $hc_id
     * @return array
     */
    public function getHrHcLeaveStaffListByHcId($hc_id) {
        try {
            $leave_staff_list = $this->hc->getHrHcLeaveStaffListByHcId($hc_id);
            foreach ($leave_staff_list as $key => $value) {
                $snapshot = json_decode($value['snapshot'], true);
                $leave_staff_list[$key]['staff_info_id'] = $snapshot['staff_info_id'];
                $leave_staff_list[$key]['name'] = $snapshot['name'];
                $leave_staff_list[$key]['job_name'] = $snapshot['job_name'];
                $leave_staff_list[$key]['remark'] = '';
                if($snapshot['state'] == 2) {
                    $leave_staff_list[$key]['remark'] = str_replace('{xxxx-xx-xx}', $snapshot['leave_date'], $this->getTranslation()->_('4027'));
                }
                if($snapshot['state'] == 3) {
                    $leave_staff_list[$key]['remark'] = str_replace('{xxxx-xx-xx}', $snapshot['stop_duties_date'], $this->getTranslation()->_('4025'));
                }
                if($snapshot['state'] == 1 && $snapshot['wait_leave_state'] == 1) {
                    $leave_staff_list[$key]['remark'] = str_replace('{xxxx-xx-xx}', $snapshot['leave_date'], $this->getTranslation()->_('4026'));
                }
            }
            return $leave_staff_list;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log('HcServer:getHrHcLeaveStaffListByHcId:'. $e->getMessage().'-----'.$e->getLine());
            return [];
        }
    }
	
	
	/**
	 * @description:获取部门链 判断申请人部门在 //Network Bulky Operations //Network Bulky Operations部门
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2022/1/14 20:18
	 */
	
	public function isShowHc($deptId = 0)
	{
		$is_show_hc = 1;  //显示
        //获取 Network Bulky Operations
        $envModel = new SettingEnvServer();
        $bulky_departId = $envModel->getSetVal('dept_network_bulky_operations_id');
        $share_department_ids[] = !empty($bulky_departId) ? $bulky_departId : 0;
        //获取 Network  Operations
        $share_department_ids[] = $envModel->getSetVal('dept_network_operations_id');

        if (!empty($deptId) && !empty($share_department_ids)) {
            //查询部门链
            $deptInfo = FleSysDepartmentModel::findFirst([
                'conditions' => "id = :id:",
                'bind'       => [
                    'id' => $deptId,
                ],
                'columns' => ['ancestry_v3'],
            ]);
            $ancestry_v3 = !empty($deptInfo) ? $deptInfo->ancestry_v3 : '';
            $deptIds = !empty($ancestry_v3) ?  explode('/', $ancestry_v3) : [];
            //判断是否存在交集  如果存在交集 不显示
            if (array_intersect($deptIds, $share_department_ids)) {
                $is_show_hc = 0;
            }
        }
		return $is_show_hc;
	}


    /**
     * @description: 记录修改 hc 的记录  主要是 优先级和需求人数, 注意这个方法要在修改之前调用
     * @param $hc_id  int  hc_id
     * @param $staff_info_id  int  修改人
     * @param $after_data  []  修改的数据
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/4/20 18:45
     */
    public function addHrhcSaveLog($hc_id = '', $staff_info_id = '', $after_data = []): bool
    {
        $result = false;
        try {
            //先查询一下 hc
            $hr_hc_data = HrhcModel::findFirst([
                'conditions' => "hc_id = :hc_id:",
                'bind'       => ['hc_id' => $hc_id],
            ]);
            if (empty($hr_hc_data) || empty($staff_info_id) || empty($after_data)) {
                throw new \Exception('没有找到 hc ==>hr_hc_id ==> '.$hc_id.' staff_info_id =>'.$staff_info_id);
            }
            $hr_hc_data                   = $hr_hc_data->toArray();
            $after_data['demandnumber']   = $after_data['demandnumber'] ?? $hr_hc_data['demandnumber'];   //如果没有传就代表没有修改
            $after_data['priority_id']    = $after_data['priority_id'] ?? $hr_hc_data['priority_id'];     //如果没有传就代表没有修改
            $after_data['state_code']     = $after_data['state_code'] ?? $hr_hc_data['state_code'];        //如果没有传就代表没有修改
            $after_data['expirationdate'] = $after_data['expirationdate'] ?? $hr_hc_data['expirationdate'];//如果没有传就代表没有修改


            $before_data = [];
            foreach ($after_data as $key => $val) {
                $before_data[$key] = $hr_hc_data[$key] ?? '';
            }

            //判断一下是否修改了优先级和需求人数
            $is_save_number_priority = HrHcLogModel::$is_save_number_priority_1;
            if ($before_data['demandnumber'] != $after_data['demandnumber'] || $before_data['priority_id'] != $after_data['priority_id']) {
                $is_save_number_priority = HrHcLogModel::$is_save_number_priority_2;
            }

            $insert = [
                'hc_id'                   => $hr_hc_data['hc_id'],
                'before_demandnumber'     => $before_data['demandnumber'],
                'after_demandnumber'      => $after_data['demandnumber'],
                'before_priority_id'      => $before_data['priority_id'],
                'after_priority_id'       => $after_data['priority_id'],
                'staff_info_id'           => $staff_info_id,
                'created_at'              => gmdate('Y-m-d H:i:s'),
                'before_data'             => json_encode($before_data, JSON_UNESCAPED_UNICODE),
                'after_data'              => json_encode($after_data, JSON_UNESCAPED_UNICODE),
                'is_save_number_priority' => $is_save_number_priority,
                'type'                    => HrHcLogModel::TYPE_DEFAULT,
            ];

            //1 修改优先级
            if ($before_data['priority_id'] != $after_data['priority_id']) {
                $insert['type'] = HrHcLogModel::TYPE_PRIORITY_ID;
                $insert_data[]  = $insert;
            }
            //2 激活 hc
            if ($before_data['state_code'] != $after_data['state_code'] && $after_data['state_code'] == HrhcModel::STATE_RECRUITING) {
                $insert['type'] = HrHcLogModel::TYPE_STATE_CODE_ACTIVATION;
                $insert_data[]  = $insert;
            }
            //3 作废 hc
            if ($before_data['state_code'] != $after_data['state_code'] && $after_data['state_code'] == HrhcModel::STATE_VOIDED) {
                $insert['type'] = HrHcLogModel::TYPE_STATE_CODE_VOID;
                $insert_data[]  = $insert;
            }
            //4. 修改截止时间
            if (date('Y-m-d', strtotime($before_data['expirationdate'])) != date('Y-m-d',
                    strtotime($after_data['expirationdate']))) {
                $insert['type'] = HrHcLogModel::TYPE_EXPIRATIONDATE;
                $insert_data[]  = $insert;
            }
            // 5. 修改需求人数
            if ($before_data['demandnumber'] != $after_data['demandnumber']) {
                $insert['type'] = HrHcLogModel::TYPE_DEMANDNUMBER;
                $insert_data[]  = $insert;
            }
            if (empty($insert_data)) {
                $insert_data[] = $insert;
            }

            $result = $this->hc->batch_insert('hr_hc_log', $insert_data);
        } catch (\Exception $e) {
            $this->logger->write_log('addHrhcSaveLog:'.$e->getMessage().'-----'.$e->getLine().'---- before_data =>'.json_encode($before_data ?? [],
                    JSON_UNESCAPED_UNICODE).' after_data-- '.json_encode($after_data, JSON_UNESCAPED_UNICODE));
        }

        return $result;
    }

    /**
     * 批量创建HC
     *
     * @param $fileName
     * @param $staffInfoId
     * @return bool
     */
    public function batchInsertHc($file, $staffInfoId)
    {

        $svr = new StaffServer();
        $fileName = $file[0];

        $fileName = $fileName->getTempName();

        $config = ['path' => dirname($fileName)];
        $file_real_name = basename($fileName);
        $excel = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($file_real_name)
            ->openSheet();

        //设置时间列
        $excel->setType(array(6 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP));

        //跳过字段第一行
        $excel->setSkipRows(0);

        $svr = new BaseServer();
        $hc = new HcServer($this->lang, $this->timezone);
        $insert = [];

        while ($data = $excel->nextRow()) {

            try {
                if(empty($data[0])){
                    break;
                }

                $params['serial_no'] = $svr->getRandomId();
                $params['department_id'] = $data[0];  //部门 id
                $params['job_title'] = $data[1];  //职位 id
                $params['job_id'] = $data[2];   //JOb id
                $params['hire_type'] = $data[3];  //雇佣类型
                $params['worknode_id'] = $data[4]; //工作地点
                if (!empty($data[5])) {
                    $params['hire_times'] = $data[5]; // 雇佣期间（月）
                }
                $params['expirationdate'] = date("Y-m-d H:i:s", $data[6]); //截止有效时间
                $params['priority_id'] = $data[7]; // 招聘优先级
                $params['reason_type'] = $data[8]; //用人原因
                $params['demandnumber'] = $data[9]; //需求人数
                $params['submitter_id'] = 84135;
                $params['surplusnumber'] = $params['demandnumber'];
                $params['state_code'] = 2; //招聘中
                $params['approval_stage'] = 0;
                $params['approval_state_code'] = 6; //审批已同意
                $params['workflow_role'] = 'hc_v2';

                $storeInfo = SysStoreModel::findFirst([
                    'conditions' => 'name = :name:',
                    'bind' => [
                        'name' => $data[4],
                    ],
                ]);
                if ($storeInfo) {
                    $storeInfo = $storeInfo->toArray();
                    $params['country_code'] = $storeInfo['country_code'];
                    $params['province_code'] = $storeInfo['province_code'];
                    $params['city_code'] = $storeInfo['city_code'];
                    $params['district_code'] = $storeInfo['district_code'];
                    $params['worknode_id'] = $storeInfo['id'];
                    $params['type'] = 1; //类型：1=快递员工，2=总部员工
                } else {
                    throw new \Exception('not exist store');
                }

                //申请原因
                if ($params['reason_type'] == '新增') {
                    $params['reason_type'] = 1;
                } else if ($params['reason_type'] == '转岗') {
                    $params['reason_type'] = 2;
                } else if ($params['reason_type'] == '离职') {
                    $params['reason_type'] = 3;
                }

                //部门
                $deptInfo = SysDepartmentModel::findFirst([
                    'conditions' => 'name = :name:',
                    'bind' => [
                        'name' => $data[0],
                    ],
                ]);
                if ($deptInfo) {
                    $params['department_id'] = $deptInfo->id ?? 0;
                } else {
                    throw new \Exception('not exist department');
                }

                //职位
                $jobTitleInfo = HrJobTitleModel::findFirst([
                    'conditions' => 'job_name = :name:',
                    'bind' => [
                        'name' => $data[1],
                    ],
                ]);
                if ($jobTitleInfo) {
                    $params['job_title'] = $jobTitleInfo->id ?? 0;
                } else {
                    throw new \Exception('not exist job title');
                }

                //job id
                $jdInfo = HrJdModel::findFirst([
                    'conditions' => 'job_name = :name:',
                    'bind' => [
                        'name' => $data[2],
                    ],
                ]);
                if ($jdInfo) {
                    $params['job_id'] = $jdInfo->job_id ?? 0;
                } else {
                    throw new \Exception('not exist jd');
                }

                //雇佣类型
                //1 正式员工 2 月薪制特殊合同工  3 日薪制特殊合同工 4 时薪制特殊合同工  5  实习生员工
                if ($params['hire_type'] == '正式员工') {
                    $params['hire_type'] = 1;
                } else if ($params['hire_type'] == '月薪制特殊合同工') {
                    $params['hire_type'] = 2;
                } else if ($params['hire_type'] == '日薪制特殊合同工') {
                    $params['hire_type'] = 3;
                } else if ($params['hire_type'] == '时薪制特殊合同工') {
                    $params['hire_type'] = 4;
                } else if ($params['hire_type'] == '实习生员工') {
                    $params['hire_type'] = 5;
                } else {
                    $params['hire_type'] = 0;
                }
                $params['manager_staff_id'] = $hc->getHcManagerByDeptId($params['department_id'], $params['worknode_id']);

                //优先级
                $params['priority_id'] = substr($params['priority_id'], 1);

                $this->logger->write_log("batchInsertHc data:" . json_encode($params), 'info');

                $insert[] = $params;
            } catch (\Exception $e){
                $this->logger->write_log("batchInsertHc :" . $e->getMessage() . $e->getTraceAsString(), 'error');
            }
        }

        $result = (new BaseRepository())->batch_insert("hr_hc", $insert);
        if (!$result) {
            throw new \Exception("hr_hc 失败" . json_encode($params, JSON_UNESCAPED_UNICODE));
        }
        return true;
    }


    /**
     * 获取部门职位 关联的 jd
     * @param $params
     * @return array
     */
    public function getJdByRelationInfo($params)
    {
        //获取部门职位关联的的JD
        $data = HrJobDepartmentRelationModel::findFirst([
            'conditions' => 'department_id = :department_id: and job_id = :job_id:',
            'bind'       => [
                'department_id' => $params['department_id'],
                'job_id'        => $params['job_title_id'],
            ],
            'columns'    => 'jd_id',
        ]);
        if (empty($data)) {
            return [];
        }

        $jdInfo = HrJdModel::findFirst([
            'conditions' => 'job_id = :job_id: and state = :state:',
            'bind'       => [
                'job_id' => $data->jd_id,
                'state'  => HrJdModel::STATE_1,
            ],
            'columns'    => 'job_id, job_name',
        ]);

        return empty($jdInfo) ? [] : $jdInfo->toArray();
    }

    /**
     * @param $result
     * @return array|mixed|\stdClass
     */
    public function getDisplayStoreData($result)
    {
    }

    /**
     * network部门
     * 网点数据
     * @param $store_id
     * @return array
     */
    public function getStoreData($store_id, $job_title=null): array
    {
        return [];
    }


    /**
     * - 网点当月异动人数
     * @param $store_id
     * @param array $job_titles
     * @return array
     */
    protected function getAboutStaffData($store_id, array $job_titles = []): array
    {
        //网点在职人数
        $store_staff['store_on_job_count'] = $this->getStoreOnJobCount([
            'store_id'   => $store_id,
            'job_titles' => $job_titles,
        ]);
        //网点当月离职人数
        $store_staff['store_current_month_resign_count'] = $this->getStoreMonthResignCount([
            'store_id'   => $store_id,
            'begin_date' => date('Y-m-01 00:00:00'),
            'end_date'   => date('Y-m-t 00:00:00'),
        ]);
        //网点停职人数
        $store_staff['store_suspension_count'] = $this->getStoreSuspensionCount(['store_id' => $store_id]);
        //网点当月待离职人数
        $store_staff['store_waiting_leave_count'] = $this->getStoreWaitingLeaveCount(['store_id' => $store_id]);
        //网点当月待入职人数
        $store_staff['store_waiting_entry_count'] = $this->getStoreWaitingEntryCount(['store_id' => $store_id]);
        return $store_staff;
    }


    /**
     * - 大区当月异动人数
     * @param $store_ids
     * @param array $job_titles
     * @return array
     */
    protected function getRegionAboutStaffData($store_ids, array $job_titles = []): array
    {
        //网点当月离职人数
        $store_staff['region_current_month_resign_count'] = $this->getStoreMonthResignCount([
            'store_id'   => $store_ids,
            'begin_date' => date('Y-m-01 00:00:00'),
            'end_date'   => date('Y-m-t 00:00:00'),
        ]);
        //网点停职人数
        $store_staff['region_suspension_count'] = $this->getStoreSuspensionCount(['store_id' => $store_ids]);
        //网点当月待离职人数
        $store_staff['region_waiting_leave_count'] = $this->getStoreWaitingLeaveCount(['store_id' => $store_ids]);
        //网点当月待入职人数
        $store_staff['region_waiting_entry_count'] = $this->getStoreWaitingEntryCount(['store_id' => $store_ids]);
        return $store_staff;
    }

    /**
     * 网点在职人数
     * @param $params
     * @return mixed
     */
    public function getStoreOnJobCount($params)
    {
        $store_id   = $params['store_id'];
        $conditions = 'formal = :formal: and wait_leave_state = :wait_leave_state: and state = :state: and is_sub_staff = :is_sub_staff: and sys_store_id = :sys_store_id:';
        $bind = [
            'formal'           => HrStaffInfoModel::FORMAL_1,
            'state'            => HrStaffInfoModel::STATE_ON_JOB,
            'wait_leave_state' => HrStaffInfoModel::WAITING_LEAVE_NO,
            'is_sub_staff'     => HrStaffInfoModel::IS_SUB_STAFF_0,
            'sys_store_id'     => $store_id,
        ];
        if (!empty($params['job_titles'])){
            $conditions .= ' and job_title IN({job_title:array})';
            $bind['job_title'] = $params['job_titles'];
        }
        return HrStaffInfoModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * 网点当月离职人数
     * @param $params
     * @return mixed
     */
    public function getStoreMonthResignCount($params)
    {
        $store_ids   = is_string($params['store_id']) ? [$params['store_id']] : $params['store_id'];
        if (empty($store_ids)) {
            return 0;
        }
        $begin_date = $params['begin_date'];
        $end_date   = $params['end_date'];
        $conditions = 'formal = :formal: and is_sub_staff = :is_sub_staff: and state = :state: and sys_store_id in ({sys_store_id:array}) and leave_date >= :begin_leave_date: and leave_date <= :end_leave_date:';
        $bind = [
            'formal'           => HrStaffInfoModel::FORMAL_1,
            'state'            => HrStaffInfoModel::STATE_RESIGN,
            'is_sub_staff'     => HrStaffInfoModel::IS_SUB_STAFF_0,
            'sys_store_id'     => $store_ids,
            'begin_leave_date' => $begin_date,
            'end_leave_date'   => $end_date,
        ];

        if (!empty($params['job_titles'])){
            $conditions .= ' and job_title IN({job_title:array})';
            $bind['job_title'] = $params['job_titles'];
        }

        return HrStaffInfoModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * 网点停职人数
     * @param $params
     * @return mixed
     */
    public function getStoreSuspensionCount($params)
    {
        $store_id   = is_string($params['store_id']) ? [$params['store_id']] : $params['store_id'];
        if (empty($store_id)) {
            return 0;
        }
        $conditions =  'formal = :formal: and is_sub_staff = :is_sub_staff: and state = :state: and sys_store_id in ({sys_store_id:array})';
        $bind = [
            'formal'           => HrStaffInfoModel::FORMAL_1,
            'state'            => HrStaffInfoModel::STATE_SUSPENSION,
            'is_sub_staff'     => HrStaffInfoModel::IS_SUB_STAFF_0,
            'sys_store_id'     => $store_id,
        ];
        if (!empty($params['job_titles'])){
            $conditions .= ' and job_title IN({job_title:array})';
            $bind['job_title'] = $params['job_titles'];
        }

        return HrStaffInfoModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * 网点待离职人数
     * @param $params
     * @return mixed
     */
    public function getStoreWaitingLeaveCount($params)
    {
        $store_id   = is_string($params['store_id']) ? [$params['store_id']] :$params['store_id'];
        if (empty($store_id)) {
            return 0;
        }
        $conditions = 'formal = :formal: and wait_leave_state = :wait_leave_state: and state = :state: and is_sub_staff = :is_sub_staff: and sys_store_id in ({sys_store_id:array})';
        $bind = [
            'formal'           => HrStaffInfoModel::FORMAL_1,
            'state'            => HrStaffInfoModel::STATE_ON_JOB,
            'wait_leave_state' => HrStaffInfoModel::WAITING_LEAVE,
            'is_sub_staff'     => HrStaffInfoModel::IS_SUB_STAFF_0,
            'sys_store_id'     => $store_id,
        ];
        if (!empty($params['job_titles'])){
            $conditions .= ' and job_title IN({job_title:array})';
            $bind['job_title'] = $params['job_titles'];
        }
        return HrStaffInfoModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * 网点待入职人数
     * @param $params
     * @return mixed
     */
    public function getStoreWaitingEntryCount($params)
    {
        $store_id = is_string($params['store_id']) ? [$params['store_id']] : $params['store_id'];
        if (empty($store_id)) {
            return 0;
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(*) as count');
        $builder->from(['hr_entry' => HrEntryModel::class]);
        $builder->leftJoin(HrHcModel::class, 'hr_entry.hc_id = hr_hc.hc_id', 'hr_hc');
        $builder->where('hr_entry.status = 2');
        $builder->andWhere('hr_hc.worknode_id in ({store_id:array})', ['store_id' => $store_id]);
        if (!empty($params['job_titles'])){
            $builder->andWhere('hr_hc.job_title in ({job_title:array})',['job_title'=> $params['job_titles']]);
        }

        $result = $builder->getQuery()->getSingleResult();
        return (int)$result->count;
    }

    protected function getRegionStoreIdsByStoreId($store_id): array
    {
        $store_server = new SysStoreServer();
        $storeInfo    = $store_server->getStoreRegionPiece($store_id);
        if (!empty($storeInfo['manage_region'])) {
            $region_store_ids = $store_server->getStoreListByRegionNew([$storeInfo['manage_region']]);
        }
        return $region_store_ids??[];
    }


    /**
     * 大区待招聘hc数量
     * @param $params
     * @return int|mixed
     */
    public function getRegionWaitingRecruitHcCount($params)
    {
        $store_ids   = $params['store_ids'];
        $job_titles = $params['job_titles'];
        return $this->getStoreWaitingRecruitHcCount(['store_id' => $store_ids, 'job_titles' => $job_titles]);
    }

    /**
     * 片区待招聘hc数量
     * @param $params
     * @return int|mixed
     */
    public function getPieceWaitingRecruitHcCount($params)
    {
        $store_id = $params['store_id'];
        $job_titles = $params['job_titles'];

        $piece_store_ids[] = $store_id;
        $store_server = new SysStoreServer();
        $region_piece = $store_server->getStoreRegionPiece($store_id);
        $store_manage_piece = $region_piece['manage_piece'] ?? '';
        if (!empty($store_manage_piece)) {
            $piece_ids[] = $region_piece['manage_piece'];
            $piece_store_ids = $store_server->getStoreListByPieceNew($piece_ids);
        }

        return $this->getStoreWaitingRecruitHcCount(['store_id' => $piece_store_ids, 'job_titles' => $job_titles]);
    }

    /**
     * 网点待招聘hc数量
     * 拆分维度
     * @param $params
     * @return int|mixed
     */
    public function getStoreWaitingRecruitHcCount($params)
    {
        $store_ids   = is_string($params['store_id']) ? [$params['store_id']] : $params['store_id'];
        if (empty($store_ids)) {
            return sprintf('%s/%s/%s', 0, 0, 0);//  新增/离职/转岗
        }
        $job_titles = $params['job_titles'];
        $data       = HrHcModel::find([
            'conditions' => 'state_code = 2 and worknode_id in ({store_ids:array}) and job_title IN({job_title:array})  and deleted = 1',
            'bind'       => [
                'store_ids' => $store_ids,
                'job_title' => $job_titles,
            ],
            'columns'    => 'sum(surplusnumber) as total,reason_type',
            'group'      => 'reason_type',
        ])->toArray();
        $data       = array_column($data, 'total', 'reason_type');
        $add        = $data[HrHcModel::REASON_TYPE_RECRUIT] ?? 0;
        $leave      = $data[HrHcModel::REASON_TYPE_QUIT] ?? 0;
        $transfer   = $data[HrHcModel::REASON_TYPE_TRANSFER_POST] ?? 0;
        return sprintf('%s/%s/%s', $add, $leave, $transfer);//  新增/离职/转岗
    }


    /**
     * 占用HC
     * @param array $params
     * @return array
     */
    public function occupyHc($params = []): array
    {
        $hcId = $params['hc_id'];
        try {
            if ($params['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE) {
                $hcId = $this->occupyFrontLineHc($params);
            } else {
                $hcId = $this->occupyNonFrontLineHc($params);
            }
            //扣减HC
            if (!empty($hcId)) {
                $this->deductHc($hcId);
            }
        } catch (ValidationException $ve) {
            $this->logger->write_log(sprintf('occupyHc err occur:%s, errCode=%d', $ve->getMessage(), $ve->getCode()),
                'info');
        }
        return [$hcId, $ve ?? null];
    }

    /**
     * 占用一线的HC
     * @param $params
     * @return int
     * @throws ValidationException
     */
    private function occupyFrontLineHc($params)
    {
        $hcId = $params['hc_id'];
        // TH雇佣类型取当前员工雇佣类型
        // MY雇佣类型取BP审批时配置的雇佣类型
        if (isCountry('MY')) {
            $hireType = $params['after_hire_type'];
        } else {
            $hireType = $params['hire_type'];
        }
        if (empty($hcId)) {
            $hcId = $this->getValidTransferHc($hireType,
                $params['after_department_id'],
                $params['after_store_id'],
                $params['after_position_id'],
                $params['after_date']);
            //校验预算
            if (empty($hcId)) {
                $this->checkBudget($params['after_department_id'],
                    $params['after_store_id'],
                    $params['after_position_id']);

                //预算充足则转岗成功后生成HC
                $hcId = '';
            }
        } else {
            $hcInfo = HrHcModel::findFirstByHcId($hcId);

            //如果传入的Hc不是招聘中或者没有剩余人数,
            //则匹配新的Hc，如匹配不到则校验预算，
            if ($hcInfo->state_code != HrHcModel::STATE_RECRUITING || $hcInfo->surplusnumber < 1) {
                $hcId = $this->getValidTransferHc($hireType,
                    $params['after_department_id'],
                    $params['after_store_id'],
                    $params['after_position_id'],
                    $params['after_date']);
                if (empty($hcId)) {
                    $this->checkBudget($params['after_department_id'],
                        $params['after_store_id'],
                        $params['after_position_id']);
                }
            }
        }

        return $hcId;
    }

    /**
     * 占用非一线的HC
     * @param $params
     * @return int
     * @throws ValidationException
     */
    private function occupyNonFrontLineHc($params)
    {
        $hcId = $params['hc_id'];
        if (isCountry('MY')) {
            $hireType = $params['after_hire_type'];
        } else {
            $hireType = $params['hire_type'];
        }
        $hcInfo = HrHcModel::findFirstByHcId($hcId);
        if (empty($hcInfo) || $hcInfo->deleted == 2) { //不存在或已删除
            throw new ValidationException('hc not exists');
        }

        //如果传入的Hc不是招聘中或者没有剩余人数,
        //则匹配新的Hc
        if ($hcInfo->state_code != HrHcModel::STATE_RECRUITING || $hcInfo->surplusnumber < 1) {
            $hcId = $this->getValidTransferHc($hireType,
                $params['after_department_id'],
                $params['after_store_id'],
                $params['after_position_id'],
                $params['after_date']);
            if (empty($hcId)) {
                throw new ValidationException($this->getTranslation()->_('err_msg_no_available_hc'), ErrCode::ERR_NO_AVAILABLE_HC);
            }
        }

        return $hcId;
    }

    /**
     * @description 获取有效的转岗HC
     * 获取一个转岗后部门、转岗后职位、转岗后网点、招聘中的、
     *
     */
    public function getValidTransferHc($hire_type, $department_id, $store_id, $job_title_id, $after_date)
    {
        //招聘中、转岗类型、雇佣类型与申请人的类型一致的
        //未删除的、指定部门、职位、工作地点的,截止日期在转岗日期之后的HC
        //截止日期当天是有效的
        $hcInfo = HrHcModel::findFirst([
            'conditions' => 'state_code = :state_code: and reason_type = :reason_type: and hire_type = :hire_type:
                and deleted = 1 and department_id = :department_id: and job_title = :job_title: and expirationdate >= :date: 
                and worknode_id = :worknode_id:',
            'bind'       => [
                'state_code'    => HrHcModel::STATE_RECRUITING,
                'reason_type'   => HrHcModel::REASON_TYPE_TRANSFER_POST,
                'hire_type'     => $hire_type,
                'department_id' => $department_id,
                'job_title'     => $job_title_id,
                'worknode_id'   => $store_id,
                'date'          => $after_date,
            ],
            'columns'    => 'hc_id',
            'order'      => 'hc_id asc',
        ]);
        if (empty($hcInfo)) {
            return '';
        }
        return $hcInfo->hc_id;
    }

    /**
     * @description 校验预算
     * @param $department_id
     * @param $store_id
     * @param $job_title_id
     * @throws ValidationException
     */
    public function checkBudget($department_id, $store_id, $job_title_id)
    {
        $params = [
            'department_id' => $department_id,
            'store_id'      => $store_id,
            'job_title_id'  => $job_title_id,
        ];
        $server = $this->class_factory("HcServer",$this->lang, $this->timezone);
        $data = $server->getHcReferences($params);
        if ($data['data']['left'] <= 0) { // 该部门职位HC预算不足，请先申请HC预算
            throw new ValidationException($this->getTranslation()->_('err_msg_hc_budget_exhausted'), ErrCode::HC_BUDGET_EXHAUSTED);
        }
    }

    /**
     * @description 扣减HC
     * @throws ValidationException
     */
    public function deductHc($hc_id)
    {
        if (empty($hc_id)) {
            throw new ValidationException('method deductHc err: miss args');
        }
        $hcInfo = HrHcModel::findFirst([
            'conditions' => "hc_id = :hc_id:",
            'bind'       => ['hc_id' => $hc_id],
            'for_update' => true,
        ]);
        if ($hcInfo->state_code != HrHcModel::STATE_RECRUITING) { //非招聘中状态，不可扣减
            throw new ValidationException('please check hc status');
        }
        if ($hcInfo->surplusnumber == 1) {
            $hcInfo->state_code = HrHcModel::STATE_FULL_RECRUITMENT;
            $hcInfo->full_time  = date('Y-m-d H:i:s');
        }
        $hcInfo->surplusnumber = new \Phalcon\Db\RawValue('surplusnumber - 1');
        $hcInfo->save();
    }

    /**
     * @description 返还HC
     * @throws ValidationException
     */
    public function remittanceHc($hc_id)
    {
        if (empty($hc_id)) {
            throw new ValidationException('method remittanceHc err: miss args');
        }
        $hcInfo = HrHcModel::findFirst([
            'conditions' => "hc_id = :hc_id:",
            'bind'       => ['hc_id' => $hc_id],
            'for_update' => true,
        ]);
        if (empty($hcInfo) || $hcInfo->deleted == 2) {
            throw new ValidationException(sprintf('hc id(%d) not exist', $hc_id));
        }
        if ($hcInfo->surplusnumber > $hcInfo->demandnumber) {
            $this->logger->write_log(sprintf('hc id %d , surplusnumber > demandnumber', $hc_id), 'notice');
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        if ($hcInfo->surplusnumber == 0) {
            $hcInfo->state_code = HrHcModel::STATE_RECRUITING;
        }
        $hcInfo->surplusnumber = new \Phalcon\Db\RawValue('surplusnumber + 1');
        $hcInfo->save();
    }

    /**
     * 系统自动创建转岗类型HC
     * @param $params
     * @return int
     */
    public function systemAutoCreateHc($params): int
    {
        //追加雇佣类型
        $staffServer = new StaffServer();
        $staffInfo = $staffServer->getStaffInfoSpecColumns($params['staff_id'], 'staff_info_id,hire_type,hire_times');

        $hcModel                      = new HrHcModel();
        $hcModel->department_id       = $params['after_department_id'];
        $hcModel->job_title           = $params['after_position_id'];
        $hcModel->hire_type           = $staffInfo['hire_type'];
        $hcModel->hire_times          = $staffInfo['hire_times'];
        $hcModel->worknode_id         = $params['after_store_id'];
        $hcModel->expirationdate      = date('Y-m-d', strtotime('+1 month'));
        $hcModel->priority_id         = 1; //P1优先级
        $hcModel->reason_type         = HrHcModel::REASON_TYPE_TRANSFER_POST;
        $hcModel->demandnumber        = 1;
        $hcModel->surplusnumber       = 1;
        $hcModel->state_code          = HrhcModel::STATE_RECRUITING;
        $hcModel->approval_state_code = 6;
        $hcModel->submitter_id        = enums::SYSTEM_STAFF_ID;
        if ($hcModel->save() === false) {
            $this->logger->write_log('systemAutoCreateHc save err', 'notice');
        }
        return $hcModel->hc_id;
    }

    /**
     * 保存HC负责人
     * @param int $hcManagerStaffId
     * @return void
     */
    protected function saveHcManager($hcId, $hcManagerStaffId, $operatorId)
    {
    }
}
