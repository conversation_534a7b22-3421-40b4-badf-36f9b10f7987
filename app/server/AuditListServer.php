<?php

namespace FlashExpress\bi\App\Server;

use app\enums\StaffAuditTypeEnums;
use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\AssetsGoodsModel;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\AuditCCModel;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\FuelApproveModel;
use FlashExpress\bi\App\Models\backyard\HrPenaltyDetailModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\oa\SmsPendingWhitelistModel;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Repository\AssetRepository;
use FlashExpress\bi\App\Repository\AuditApplyRepository;
use FlashExpress\bi\App\Repository\AuditApprovalRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\BusinesstripRepository;
use FlashExpress\bi\App\Repository\FleetRepository;
use FlashExpress\bi\App\Repository\JobtransferRepository;
use FlashExpress\bi\App\Repository\OsStaffRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\ReportRepository;
use FlashExpress\bi\App\Repository\StaffAuditToolLog;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StaffWorkAttendanceRepository;
use FlashExpress\bi\App\Repository\VehicleRepository;
use FlashExpress\bi\App\Repository\WmsRepository;
use FlashExpress\bi\App\Traits\AuditStateTrait;
use Phalcon\Db;

class AuditListServer extends BaseServer
{
    use AuditStateTrait;
    protected $re;
    public $timezone;
    public $auditlist;
    public $invalid;

    protected $is_individual_contractor = false;

    //可以发送 push 提醒的审批类型
    public static $sendPushAuditList = [
        AuditListEnums::APPROVAL_TYPE_REINSTATEMENT,
    ];
    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone  = $timezone;
        $this->re        = [
            'staff'     => new StaffRepository(),
            'audit'     => new AuditRepository($this->lang),
            'overtime'  => new OvertimeRepository($timezone),
            'staffWork' => new StaffWorkAttendanceRepository(),
            'auditLog'  => new StaffAuditToolLog(),
            'auditlist' => new AuditlistRepository($this->lang, $timezone),
            'wms'       => new WmsRepository($timezone),
            'trip'      => new BusinesstripRepository($this->lang, $timezone),
            'vehicle'   => new VehicleRepository($this->lang, $timezone),
            'fleet'     => new FleetRepository($timezone),
            'osstaff'   => new OsStaffRepository($this->timezone),
            'report'    => new ReportRepository($this->lang, $timezone),
        ];
        $this->auditlist = new AuditlistRepository($this->lang, $timezone);

        //需要显示超时关闭倒计时的审批类型
        $this->setInvalid(AuditListEnums::getAuditTypeOfLimitApprovalTime());
        //$this->invalid = array(enums::$audit_type['AT'], enums::$audit_type['LE'], enums::$audit_type['LH'],
        //    enums::$audit_type['OT'],enums::$audit_type['BT'],enums::$audit_type['GO']);
    }

    /**
     * 获取列表
     * @param array $paramIn
     * @return array
     */
    public function auditListV2(array $paramIn = []): array
    {
        //[1.1]获取传入参数
        $auditShowType  = $this->processingDefault($paramIn, 'audit_show_type', 2, 1); //显示类型  1-我的申请 2- 我的审批 ！！！！！
        $auditStateType = $this->processingDefault($paramIn, 'audit_state_type', 2, 1); //1-待审批/进行中 2-已审批/已完成
        $pageNum        = $this->processingDefault($paramIn, 'page_num', 2, 1);
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $search         = $this->processingDefault($paramIn, 'search_conditions');
        $auditTypes     = $this->processingDefault($paramIn, 'filter_types');
        $order          = $this->processingDefault($paramIn, 'sort_order');

        //[1.2]初始化
        $returnData     = [];
        $conditions     = [];

        //[1.3]增加审批类别和员工名字搜索
        if ($auditTypes) {
            $conditions['audit_type'] = $auditTypes;
        }
        if ($search) {
            $staffsArr = self::getLikeStaffs($search, true);
            if ($staffsArr) {
                $staffIds = array_column($staffsArr, 'staff_info_id');
                $conditions['submitter_id'] = $staffIds;
            }
        }

        //[2]获取列表数据
        $pageSize = 10;
        $columns = "*";
        $repo = new AuditlistRepository($this->lang, $this->timezone);
        switch ($auditShowType . $auditStateType) {
            case '11': //我的申请-待审批

                $conditions['state'] = [
                    enums::APPROVAL_STATUS_PENDING
                ];
                $list = $repo->getAuditApplyList($staffId, $conditions, $columns, $order, $pageNum, $pageSize);
                break;
            case '12': //我的申请-已审批

                $conditions['state'] = [
                    enums::APPROVAL_STATUS_APPROVAL,
                    enums::APPROVAL_STATUS_REJECTED,
                    enums::APPROVAL_STATUS_CANCEL,
                    enums::APPROVAL_STATUS_TIMEOUT,
                ];
                $list = $repo->getAuditApplyList($staffId, $conditions, $columns, $order, $pageNum, $pageSize);
                break;
            case '21': //我的审批-待审批

                $conditions['state'] = [
                    enums::APPROVAL_STATUS_PENDING
                ];
                $list = $repo->getAuditApprovalList($staffId, $conditions, $columns, $order, $pageNum, $pageSize);
                break;
            case '22': //我的审批-已完成

                $conditions['state'] = [
                    enums::APPROVAL_STATUS_APPROVAL,
                    enums::APPROVAL_STATUS_REJECTED,
                    enums::APPROVAL_STATUS_CANCEL,
                    enums::APPROVAL_STATUS_TIMEOUT,
                ];
                $list = $repo->getAuditApprovalList($staffId, $conditions, $columns, $order, $pageNum, $pageSize);
                break;
        }
        if(isCountry(['TH','PH'])){
            $staffInfo = (new StaffRepository())->checkoutStaffById($staffId);
            $this->is_individual_contractor = $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID;
        }
        if (isset($list) && count($list) !== 0) {
            $returnData = $this->generateList($list, $auditShowType, $auditStateType);
        }

        return $this->checkReturn(['data' => ['dataList' => $returnData]]);
    }


    /**
     * 获取列表详情
     * @param $staff_id int 类型
     * @param $type int 类型
     * @return array
     * @throws \FlashExpress\bi\App\library\Exception\InnerException
     */
    public function getDetail($paramIn = [])
    {
        $this->logger->write_log(['func' => 'getDetail', 'paramIn' => $paramIn], 'notice');

        //[1]参数定义
        $returnData    = ['data' => ['head' => [], 'detail' => [], 'stream' => []]];
        $id            = $this->processingDefault($paramIn, 'id', 1);
        $type          = $this->processingDefault($paramIn, 'type', 1);
        $staff_id      = $this->processingDefault($paramIn, 'staff_id', 2);
        $comeFrom      = $this->processingDefault($paramIn, 'isCommit', 2, 1); //1-我申请的   2-我审批的
        $url           = $this->processingDefault($paramIn, 'url', 1);
        //[2]ID转换 eg:audit_101 => [audit_id => 101]
        [$idPrefix, $primaryId] = explode('_', $id);
        $postData = [
            $idPrefix . '_id' => $primaryId,
            'url'             => $url,
            'userinfo'        => $paramIn['userinfo']??[]
        ];
        $info = $this->re['auditlist']->getStaffAuditUnion(['id_union'=>$id,'type_union'=>$type]);

        if (empty($info)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
        }
        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($info['staff_id_union']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        //[3]数据拼接
        switch ($type) {
            case enums::$audit_type['AT']: //补卡申请
                $result = $this->re['audit']->auditDetail($postData);
                if (empty($result)) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
                }
                $returnData['data']['detail'] = [
                    ['key' => $this->getTranslation()->_('apply_parson'), 'value' => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? '')],
                    ['key' => $this->getTranslation()->_('apply_department'),'value' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? '')],
                    ['key' => $this->getTranslation()->_('attendance_date'), 'value' => $result['attendance_date']],
                    ['key' => $this->getTranslation()->_('reissue_card_time'), 'value' => $result['reissue_card_date']],
                    ['key' => $this->getTranslation()->_('attendance_type'), 'value' => $this->re['audit']->typeName($result['audit_type'])[$result['attendance_type']]],
                    ['key' => $this->getTranslation()->_('audit_reason'), 'value' => $result['audit_reason']],
                ];
                $data                         = [
                    'title'       => $this->re['auditlist']->getAudityType($type),
                    'id'          => $result['audit_id'],
                    'staff_id'    => $info['staff_id_union'],
                    'type'        => $type,
                    'created_at'  => $result['created_at'],
                    'updated_at'  => $result['updated_at'],
                    'status'      => $result['status'],
                    'status_text' => $this->re['auditlist']->getAuditStatus('10' . $result['status']),
                    'serial_no'   => $result['serial_no'] ?? '',
                ];
                if (isset($result['workflow_role']) && $result['workflow_role']) {
                    $data['options']              = (new WorkflowServer($this->lang, $this->timezone))->getOptions($comeFrom, "audit_" .  $postData['audit_id'], enums::$audit_type['AT'], ['staff_id' => $staff_id]);
                    $returnData['data']['stream'] = (new WorkflowServer($this->lang, $this->timezone))->generateStream('audit_'.$result['audit_id'], $result['audit_id'], enums::$audit_type['AT']);
                } else {
                    $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($postData['audit_id'], $type, $staff_id, $comeFrom);
                    break;
                }
                $returnData['data']['head']   = $data;
                break;
            case enums::$audit_type['LE']: //请假申请
                $result = $this->re['audit']->auditDetail($postData, 1);
                if (empty($result)) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
                }
                //针对 请假类型 需要额外增加 对应的请假说明
                $returnData['data']['leave_type']   =  $result['leave_type'];
                $photo = [];
                if (!empty($result['img_path'])) {
                    foreach ($result['img_path'] as $item) {
                        $photo[] = $item['image_path'];
                    }
                }
                //获取该类型是否带薪
                $audit_model = new AuditRepository($this->lang);
                $leave_book  = $audit_model->getTypeBook_all();
                $is_or       = array_column($leave_book, 'type', 'code');
                $describe    = $this->getTranslation()->_('un_paid');
                if (isset($is_or[$result['leave_type']]) && $is_or[$result['leave_type']] == 1) {
                    $describe = $this->getTranslation()->_('paid');
                }
                $leave_start_time = $result['leave_start_time'] . ' ' .($result['leave_start_type'] == 1 ? $this->getTranslation()->_('morning') : $this->getTranslation()->_('afternoon'));
                $leave_end_time = $result['leave_end_time'] . ' ' .($result['leave_end_type'] == 1 ? $this->getTranslation()->_('morning') : $this->getTranslation()->_('afternoon'));
                $returnData['data']['detail'] = [
                    ['key' => $this->getTranslation()->_('apply_parson'), 'value' => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? '')],
                    ['key' => $this->getTranslation()->_('apply_department'),'value' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? '')],
                    ['key' => $this->getTranslation()->_('cs_department'),'value' => $result['cs_department'] ?? '--'],
                    ['key' => $this->getTranslation()->_('leave_type'), 'value' => ($this->re['audit']->typeName($result['audit_type'])[$result['leave_type']] ?? $result['leave_type']) . "({$describe})"],
                    ['key' => $this->getTranslation()->_('start_time'), 'value' => $leave_start_time],
                    ['key' => $this->getTranslation()->_('end_time'), 'value' => $leave_end_time],
                    ['key' => $this->getTranslation()->_('days'), 'value' => $result['leave_day']],
                    ['key' => $this->getTranslation()->_('audit_leave_reason'), 'value' => $result['audit_reason']],
                    ['key' => $this->getTranslation()->_('photo'), 'value' => $photo],
                ];
                $data                         = [
                    'title'       => $this->re['auditlist']->getAudityType($type),
                    'id'          => $result['audit_id'],
                    'staff_id'    => $info['staff_id_union'],
                    'type'        => $type,
                    'created_at'  => $result['created_at'],
                    'updated_at'  => $result['updated_at'],
                    'status'      => $result['status'],
                    'status_text' => $this->re['auditlist']->getAuditStatus('10' . $result['status']),
                    'serial_no'   => $result['serial_no'] ?? '',
                ];
                if (isset($result['workflow_role']) && $result['workflow_role']) {
                    $data['options']              = (new WorkflowServer($this->lang, $this->timezone))->getOptions($comeFrom, "audit_" .  $postData['audit_id'], enums::$audit_type['LE'], ['staff_id' => $staff_id]);
                    $returnData['data']['stream'] = (new WorkflowServer($this->lang, $this->timezone))->generateStream('audit_'.$result['audit_id'], $result['audit_id'], enums::$audit_type['LE']);
                } else {
                    $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($postData['audit_id'], $type, $staff_id, $comeFrom);
                    break;
                }
                $this->wLog('auditListServer', json_encode($data), '[detail][auditDetail][3]');
                $returnData['data']['head']   = $data;

                break;
            case enums::$audit_type['OT']: //OT申请
                $result = (new OvertimeRepository($this->timezone))->infoOvertime(['overtime_id' => $postData['overtime_id']]);
                if (empty($result)) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
                }

                $server = new OvertimeServer($this->lang, $this->timezone);
                if ($result['wf_role'] == 'ot_new') {
                    $apServer = new ApprovalServer($this->lang, $this->timezone);
                    $returnData = $apServer->getAuditDetail($postData['overtime_id'], enums::$audit_type['OT'],$staff_id, $comeFrom);
                    break;
                }
                break;
            case enums::$audit_type['HC']:
                //获取Hc详情
                $result = (new HcServer($this->lang, $this->timezone))->getHcInfo($postData);

                //新数据走新的详情
                //旧数据走原有的详情
                if ($result && $result['workflow_role'] == 'hc_v2') {
                    $apServer = new ApprovalServer($this->lang, $this->timezone);
                    $returnData = $apServer->getAuditDetail($result['hc_id'], enums::$audit_type['HC'],$staff_id, $comeFrom);
                    break;
                }
                break;
            case enums::$audit_type['MA']: //物料申请
                $result = $this->re['wms']->getWmsOrderMongoR($postData['id_id']);
                if (empty($result)) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
                }
                if (!$result['wf_role']) {
                    $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($info['origin_id'], $type, $staff_id, $comeFrom);
                    break;
                }
                break;
            case enums::$audit_type['BT']: //出差申请
            case enums::$audit_type['YCBT']: //黄牌项目出差
                $result    = $this->re['trip']->getTripR($postData['id_id']);//通过id获取详情
                $resultImg = $this->re['trip']->getTripImgR($postData['id_id']);//附件
                $resultImg = array_column($resultImg, 'img_path');
                //多语言切换
                $tran_type   = $this->re['trip']->getTransportationType();
                $single_type = $this->re['trip']->getSingleroundtripType();
                if ($result['traffic_tools'] == 4) {//如果是其他类型交通工具显示录入的具体名称
                    $traffic_tools = $tran_type[$result['traffic_tools']] . "[" . $result['other_traffic_name'] . "]";
                } else {
                    $traffic_tools = $tran_type[$result['traffic_tools']];
                }
                $returnData['data']['detail'] = [
                    ['key' => $this->getTranslation()->_('apply_parson'), 'value' => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? '')],
                    ['key' => $this->getTranslation()->_('apply_department'),'value' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? '')]
                ];
                if ($type == enums::$audit_type['BT'] && in_array($result['business_trip_type'],[BusinessTripModel::BTY_DOMESTIC,BusinessTripModel::BTY_FOREIGN])) {
                    $returnData['data']['detail'] = array_merge($returnData['data']['detail'],[
                        ['key' => $this->getTranslation()->_('business_trip_type'), 'value' => $result['business_trip_type'] == BusinessTripModel::BTY_DOMESTIC ? $this->getTranslation()->_('business_trip_domestic') : $this->getTranslation()->_('business_trip_foreign')],
                    ]);
                }
                $returnData['data']['detail'] = array_merge($returnData['data']['detail'],[
                    ['key' => $this->getTranslation()->_('reason_application'), 'value' => $result['reason_application']],
                    ['key' => $this->getTranslation()->_('traffic_tools'), 'value' => $traffic_tools],
                    ['key' => $this->getTranslation()->_('oneway_or_roundtrip'), 'value' => $single_type[$result['oneway_or_roundtrip']]],
                    ['key' => $this->getTranslation()->_('departure_city'), 'value' => $result['departure_city']],
                    ['key' => $this->getTranslation()->_('destination_city'), 'value' => $result['destination_city']],
                    ['key' => $this->getTranslation()->_('start_time'), 'value' => $result['start_time']],
                    ['key' => $this->getTranslation()->_('end_time'), 'value' => $result['end_time']],
                    ['key' => $this->getTranslation()->_('days_num'), 'value' => $result['days_num']],
                    ['key' => $this->getTranslation()->_('remark'), 'value' => $result['remark']],
                    ['key' => $this->getTranslation()->_('photo'), 'value' => $resultImg]
                ]);
                //字段解释 ：申请理由，交通工具，单程1往返2 ,出发城市,到达城市,开始时间,结束时间,出差天数,备注
                $data                         = [
                    'title'       => $this->re['auditlist']->getAudityType($type),
                    'origin_id'   => $result['id'],
                    'id'          => $result['id'],
                    'staff_id'    => $info['staff_id_union'],
                    'type'        => 10,
                    'created_at'  => $result['created_at'],
                    'updated_at'  => $result['updated_at'],
                    'status'      => $result['status'],
                    'status_text' => $this->re['auditlist']->getAuditStatus('10' . $result['status']),
                    'serial_no'   => $result['serial_no'] ?? '',
                ];
                $auditApply = AuditApplyModel::findFirst([
                    'conditions' => ' biz_value = :value: and biz_type = :type: ',
                    'bind' => ['value' => $postData['id_id'], 'type' => $type]
                ]);
                if ($auditApply) {
                    $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($postData['id_id'], $type, $staff_id, $comeFrom);
                    break;
                }
                $data['options']              = (new WorkflowServer($this->lang, $this->timezone))->getOptions($comeFrom, "id_" .  $postData['id_id'], enums::$audit_type['BT'], ['staff_id' => $staff_id]);
                $returnData['data']['stream'] = (new WorkflowServer($this->lang, $this->timezone))->generateStream('id_'.$result['id'], $result['id'], enums::$audit_type['BT']);
                $returnData['data']['head']   = $data;

                break;
            case enums::$audit_type['GO']: //外出申请
                $result    = $this->re['trip']->getTripR($postData['id_id']);//通过id获取详情
                $resultImg = $this->re['trip']->getTripImgR($postData['id_id']);//附件
                $resultImg = array_column($resultImg, 'img_path');

                $returnData['data']['detail'] = [
                    ['key' => $this->getTranslation()->_('apply_parson'), 'value' => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? '')],
                    ['key' => $this->getTranslation()->_('apply_department'),'value' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? '')],
                    ['key' => $this->getTranslation()->_('start_time'), 'value' => $result['start_time']],
                    ['key' => $this->getTranslation()->_('end_time'), 'value' => $result['end_time']],
                    ['key' => $this->getTranslation()->_('days_num'), 'value' => $result['days_num']],
                    ['key' => $this->getTranslation()->_('go_out_reason_application'), 'value' => $result['reason_application']],
                    ['key' => $this->getTranslation()->_('go_out_location'), 'value' => $result['destination_city']],
                    ['key' => $this->getTranslation()->_('photo'), 'value' => $resultImg],
                ];
                $data                         = [
                    'title'       => $this->re['auditlist']->getAudityType($type),
                    'origin_id'   => $result['id'],
                    'id'          => $result['id'],
                    'staff_id'    => $info['staff_id_union'],
                    'type'        => $type,
                    'created_at'  => $result['created_at'],
                    'updated_at'  => $result['updated_at'],
                    'status'      => $result['status'],
                    'status_text' => $this->re['auditlist']->getAuditStatus('10' . $result['status']),
                    'serial_no'   => $result['serial_no'] ?? '',
                ];
                $auditApply = AuditApplyModel::findFirst([
                    'conditions' => ' biz_value = :value: and biz_type = :type: ',
                    'bind' => ['value' => $postData['id_id'], 'type' => $type]
                ]);
                if ($auditApply) {
                    $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($postData['id_id'], $type, $staff_id, $comeFrom);
                    break;
                }
                $data['options']              = (new WorkflowServer($this->lang, $this->timezone))->getOptions($comeFrom, "id_" .  $postData['id_id'], AuditListEnums::APPROVAL_TYPE_GO, ['staff_id' => $staff_id]);
                $returnData['data']['stream'] = (new WorkflowServer($this->lang, $this->timezone))->generateStream('id_'.$result['id'], $result['id'], AuditListEnums::APPROVAL_TYPE_GO);
                $returnData['data']['head']   = $data;

                break;
            case enums::$audit_type['VA']: //加班车
                $postData['require_extend'] = true;

                //获取车型详情
                $server  = new FleetServer($this->lang, $this->timezone);
                $result  = $server->getFleetDetail($postData);
                if ($result['wf_role'] == 'fleet_new') {
                    $apServer = new ApprovalServer($this->lang, $this->timezone);
                    $returnData = $apServer->getAuditDetail($postData['fleet_id'], enums::$audit_type['VA'],$staff_id, $comeFrom);
                    break;
                }
                break;
            case enums::$audit_type['RN']: //离职
                $auditApply = AuditApplyModel::findFirst([
                    'conditions' => ' biz_value = :biz_value: and biz_type = :biz_type: ',
                    'bind' => ['biz_value' => $info['origin_id'], 'biz_type' => $type]
                ]);
                if ($auditApply) {
                    $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($info['origin_id'], $type, $staff_id, $comeFrom);
                    break;
                }
                break;
            case enums::$audit_type['OS']: //外协员工
                $server = new OsStaffServer($this->lang, $this->timezone);
                $result = $server->getOsStaffDetail(['id' => $primaryId]);
                if ($result['wf_role'] == 'os_new') {
                    $apServer = new ApprovalServer($this->lang, $this->timezone);
                    $returnData = $apServer->getAuditDetail($postData['os_id'], enums::$audit_type['OS'],$staff_id, $comeFrom);
                    break;
                }
                break;
            case enums::$audit_type['AS']: //个人资产申请
            case enums::$audit_type['ASP']: //公共资产申请
                $result      = $this->getAssetOrderDetail($info['origin_id'], strtolower(substr($this->lang, 0, 2)));
                $statusUnion = $info['status_union'] - 100;
                if (empty($result)) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
                }
                $auditApply = AuditApplyModel::findFirst([
                    'conditions' => ' biz_value = :biz_value: and biz_type = :biz_type: ',
                    'bind' => ['biz_value' => $info['origin_id'], 'biz_type' => $type]
                ]);
                if ($auditApply) {
                    $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($info['origin_id'], $type, $staff_id, $comeFrom);
                    break;
                }

                foreach ($result as $k => $v) {
                    $returnData['data']['detail'][]     = [
                        'key'   => $v['goods_name'],
                        'value' => $statusUnion == 1 ? $v['recomment_num'] : $v['approval_num'],
                    ];

                    $returnData['data']['asset_edit'][] = ['id' => $v['item_id'], 'key' => $v['goods_name'], 'value' => $v['recomment_num']];
                }

                //申请人 合并 显示一条
                $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('apply_parson'), 'value' => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? '')];
                $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('apply_department'),'value' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? '')];


                $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('reason_application'), 'value' => $result[0]['reason']];
                //驳回
                if ($statusUnion == 3) {
                    $returnData['data']['detail'][] = ['key' => $this->getTranslation()->_('reject_reason'), 'value' => $result[0]['reject_reason']];
                }

                //获取网点名
                $storeInfo = (new \FlashExpress\bi\App\Server\SysStoreServer())->getStoreName([$result[0]['organization_id']]);

                //物品名称 ，物品数量 ，使用者，申请理由，审批人，状态
                $data = [
                    'title'       => $type == enums::$audit_type['AS'] ? $this->getTranslation()->_('abort_list_myasset') : $this->getTranslation()->_('abort_list_pubasset'),
                    'origin_id'   => $result[0]['id'],
                    'id'          => $info['id_union'],
                    'staff_id'    => $info['staff_id_union'],
                    'type'        => $type,
                    'created_at'  => $result[0]['created_at'],
                    'updated_at'  => $result[0]['updated_at'],
                    'status'      => $statusUnion,
                    'status_text' => $this->re['auditlist']->getAuditStatus($info['status_union']),
                    'serial_no'   => $result[0]['order_union_id'],
                    'store_id'    => $result[0]['organization_id'],
                    'store_name'  => $storeInfo[$result[0]['organization_id']] ?? '',
                ];

                $staffData = $this->re['staff']->getStaffpositionV2($info['staff_id_union']);
                $stream[]  = [
                    "staff_id"    => $info['staff_id_union'],    //申请人ID
                    "name"        => $staffData['name'],   //申请人职位
                    "position"    => "position",    //申请人职位
                    "department"  => 'Department/Branch',
                    "status"      => $this->re['auditlist']->getAuditStatus('108'),   //申请状态
                    "status_code" => 8,   //发起申请
                    "time"        => date('Y-m-d H:i:s', strtotime($info['created_at']) + $add_hour * 3600),
                    "is_OK"       => 0,      //已处理
                ];
                //1-我申请的   2-我审批的
                $AssetsServer = new AssetServer($this->lang, $this->timezone);
                if (
                    ($comeFrom == 1 && $info['staff_id_union'] != $staff_id)
                    or
                    ($comeFrom == 2 && $type == enums::$audit_type['AS'] && !in_array($staff_id, $AssetsServer->getSetEnv('asset_personal_approver'))) // 我审批的 且是个人资产权限
                    or
                    ($comeFrom == 2 && $type == enums::$audit_type['ASP'] && !in_array($staff_id, $AssetsServer->getSetEnv('asset_public_approver'))) // 我审批的 且是公共资产权限
                ) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
                }

                $data['options'] = (object)null;
                $workflow        = $this->re['auditlist']->getAuditUnionList('', ["id_union = '{$info['id_union']}'"], 1, 1, 'id asc');

                $names    = $staff_ids = '';
                $staffIds = [];
                //按钮权限
                foreach ($workflow['data'] as $key => $value) {
                    $data['options'] = $comeFrom == 1 && $statusUnion == 1 ? $this->re['auditlist']->generateoptions([3]) : $data['options'];
                    $data['options'] = $comeFrom == 2 && $statusUnion == 1 ? $this->re['auditlist']->generateoptions([1, 2]) : $data['options'];
                    $data['options'] = $statusUnion == 4 ? [] : $data['options'];
                }
                if ($type == enums::$audit_type['AS']) {
                    $staffIds = $AssetsServer->getSetEnv('asset_personal_approver');
                } else if ($type == enums::$audit_type['ASP']) {
                    $staffIds = $AssetsServer->getSetEnv('asset_public_approver');
                }
                $staffInfo = $this->getApprovalInfo($staffIds);

                if (!empty($staffInfo)) {
                    foreach ($staffInfo as $key => $value) {
                        $names     .= $value['name'] . ' (' . $value['id'] . '),';
                        $staff_ids .= $value['id'] . ',';
                    }
                }

                $stream[] = [
                    "staff_id"    => trim($staff_ids, ','),
                    "name"        => "",
                    "position"    => $this->getTranslation()->_('purchase'),
                    "department"  => $this->getTranslation()->_('purchase_department'),
                    "status"      => $this->re['auditlist']->getAuditStatus($info['status_union']),   //申请状态
                    "status_code" => $statusUnion,
                    "time"        => $result[0]['updated_at'],
                    "is_OK"       => !in_array($statusUnion, [2, 3]) ? 1 : 0,      //已处理
                ];

                //已撤销，重置按钮
                if ($info['status_union'] == 104) {
                    $stream[1]                = $stream[0];
                    $stream[1]['status_code'] = $statusUnion;
                    $stream[1]['status']      = $this->re['auditlist']->getAuditStatus($info['status_union']);
                    $stream[1]['time']        = $result[0]['updated_at'];
                }
                $returnData['data']['head']   = $data;
                $returnData['data']['stream'] = $stream;

                $params     = [];
                $params['orderSn'] = $result[0]['order_union_id'];
                $params['lang'] = $this->lang;
                $result = (new BaseRepository())->getDataFromWms(env("api_wms_url") . "/open/getOutboundTrackingInfo", $params);
                $tracking = '';
                $this->getDI()->get("logger")->write_log("getOutboundTrackingInfo " . json_encode($result, JSON_UNESCAPED_UNICODE).json_encode($params, JSON_UNESCAPED_UNICODE), "info");
                if ($result && $result['code'] == 1 && isset($result['data']) && $result['data'] && isset($result['data']['status'])) {
                    if ($result['data']['status'] == 'Wait for approval') {
                        $tracking = $this->getTranslation()->_("shipment_be_delivered");
                    } else if ($result['data']['status'] == 'Outbounded') {
                        $tracking = $this->getTranslation()->_("shipment_be_received") . ' (' . $this->getTranslation()->_("shipment_number") . '：' . $result['data']['expressSn'] . ")";
                    }
                }
                $returnData['data']['detail'][] = ["key" => $this->getTranslation()->_("shipment_status"), "value" => $tracking];
                break;
            case enums::$audit_type['RE']: // 举报申请
                // 获取举报申请信息
                $auditApply = AuditApplyModel::findFirst([
                    'conditions' => ' biz_value = :biz_value: and biz_type = :biz_type: ',
                    'bind' => ['biz_value' => $info['origin_id'], 'biz_type' => $type]
                ]);
                if ($auditApply) {
                    $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($info['origin_id'], $type, $staff_id, $comeFrom);
                    break;
                }
                break;
            case AuditListEnums::APPROVAL_TYPE_JT: //转岗
                $apServer   = new ApprovalServer($this->lang, $this->timezone);
                $returnData = $apServer->getAuditDetail($postData['tf_id'], AuditListEnums::APPROVAL_TYPE_JT, $staff_id,
                    $comeFrom);
                break;
            case AuditListEnums::APPROVAL_TYPE_JT_OA: //转岗oa渠道
                //获取转岗详情
                $apServer   = new ApprovalServer($this->lang, $this->timezone);
                $returnData = $apServer->getAuditDetail($postData['tf_id'], AuditListEnums::APPROVAL_TYPE_JT_OA,
                    $staff_id, $comeFrom);
                break;
            case enums::$audit_type['FD']://运费折扣
                $server = new DiscountServer($this->lang, $this->timezone);
                $result = $server->getDetail(['id' => $primaryId]);
                if (empty($result)) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
                }

                //已经申请的折扣产品
                $validReuqest = $server->getHasRequestDisc($result['costomer_id']);

                //组织详情数据
                $detailLists = [
                    'apply_parson'     => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? ''),
                    'apply_department' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
                    'costomer_id'   => $result['costomer_id'] ?? '',
                    'costomer_name' => $result['costomer_name'] ?? '',
                    'phone_no'      => $result['costomer_mobile'] ?? '',
                    'price_type'    => $result['price_type_text'] ?? '',
                    'current_disc'  => $result['current_disc'] . '%' ?? '',
                    'discount_detail_1' => $result['request_text'] ?? '',
                    'discount_detail_2' => $result['costomer_parcel_count'],
                    'discount_detail_3' => $result['proportion'] ?? '',
                    'discount_detail_4' => $result['costomer_created_at'],
                    'discount_detail_5' => $result['costomer_estimate_parcel_count'],
                    'reason_app'        => $result['apply_reason_type_text'] ?? '',
                    'discount_detail_6' => $validReuqest ?? '',
                    'store_name'               => $result['store_name'] ?? '',
                    'manage_region_name'       => $result['manage_region_name'] ?? '',
                    'manage_piece_name'        => $result['manage_piece_name'] ?? '',
                    'photo'              => $result['image_path'] ?? '',
                ];

                //驳回状态，需要显示驳回原因
                if ($result['state'] == enums::$audit_status['dismissed']) {
                    $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
                }
                $returnData['data']['detail'] = $this->format($detailLists);

                $data      = [
                    'title'       => $this->re['auditlist']->getAudityType($type),
                    'id'          => $result['id'],
                    'staff_id'    => $info['staff_id_union'],//申请人
                    'type'        => $type,
                    'created_at'  => $result['created_at'],
                    'updated_at'  => $result['updated_at'],
                    'status'      => $result['state'],
                    'status_text' => $this->re['auditlist']->getAuditStatus('10' . $result['state']),
                    'serial_no'   => $result['serial_no'] ?? '',
                ];
                $approvals = $this->re['auditlist']->getApprovalIds(['id' => $id, 'type' => enums::$audit_type['FD']]);

                $currentAuditStatus = $this->getApprovalAuditStatus([
                    'id'          => 'fd_' . $result['id'],
                    'approval_id' => $staff_id,
                    'type'        => $type
                ]);

                //1-我申请的   2-我审批的
                $status   = $currentAuditStatus && $comeFrom == 2 ? $currentAuditStatus - 100 : $result['state'];
                $server   = new WorkflowServer($this->lang, $this->timezone);
                //$workflow = $server->doWfRequest($result['submitter_id'], $staff_id, $result['wf_role'], enums::$audit_type['FD']);
                $data['options']              = isset($workflow['current']) ? $this->getOptions(['option' => $workflow['current'], 'status' => $status]) : (object)null;
                $returnData['data']['head']   = $data;
                $returnData['data']['stream'] = $this->generateStream($data);

                $stream                       = [
                    'id'        => $result['id'],
                    'status'    => $result['state'],
                    'type'      => $type,
                    'approvals' => $approvals,
                ];
                $returnData['data']['stream'] = $this->getMutiApprovalStream($stream);
                break;
            case enums::$audit_type['SA'] : // 薪资审批
                if (strtolower(env('country_code', 'Th')) == 'th') {
                    $auditApply = AuditApplyModel::findFirst([
                        'conditions' => ' biz_value = :biz_value: and biz_type = :biz_type: ',
                        'bind' => ['biz_value' => $info['origin_id'], 'biz_type' => $type]
                    ]);
                    if ($auditApply) {

                        $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($info['origin_id'], $type, $staff_id, $comeFrom);
                    } else {

                        $returnData = (new SalaryServer($this->lang, $this->timezone))->approvalDetail($postData, $comeFrom);
                    }
                } else {

                    $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($info['origin_id'], $type, $staff_id, $comeFrom,null,'',1,$paramIn['request_src']??'');
                }
                break;
            case enums::$audit_type['CR'] : // 理赔金额
                $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($postData['claimer_id'], enums::$audit_type['CR'], $staff_id, $comeFrom);
                break;
            case enums::$audit_type['FDS']: //运营产品-shop
            case enums::$audit_type['FDP']: //运营产品-sales/Project Managerment
                $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($postData['fd_id'], $type, $staff_id, $comeFrom);
                break;
            case enums::$audit_type['ATB'] : // 出差打卡
                $apServer = new ApprovalServer($this->lang, $this->timezone);
                $returnData = $apServer->getAuditDetail($primaryId, enums::$audit_type['ATB'],$staff_id, $comeFrom);
                break;
            case enums::$audit_type['GOC'] : // 外出打卡
                $apServer = new ApprovalServer($this->lang, $this->timezone);
                $returnData = $apServer->getAuditDetail($primaryId, enums::$audit_type['GOC'],$staff_id, $comeFrom);
                break;
            case enums::$audit_type['MSG'] :
                // 消息发送申请
                $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($primaryId, enums::$audit_type['MSG'], $staff_id, $comeFrom);
                break;
            case enums::$audit_type['FUEL']: // 补贴油费
                $apServer = new ApprovalServer($this->lang, $this->timezone);
                $returnData = $apServer->getAuditDetail($primaryId, enums::$audit_type['FUEL'], $staff_id, $comeFrom);
                //新需求 针对 用车申请油费 需要新增 wrs审核日志 拼接到 审批流里面
                $f_info = FuelApproveModel::findFirst($primaryId);
                if(!empty($f_info) && $f_info->status == 2){
                    //Mileage auditor
                    $row['staff_id'] = $f_info->input_by;
                    $row['name'] = 'Mileage auditor';
                    $row['position'] = '';
                    $row['department'] = '';
                    $row['status_code'] = $f_info->input_state;
                    $row['status'] = $f_info->input_state > 2 ? $this->getTranslation()->_('audit_status.3') : $this->getTranslation()->_('audit_status.2');//0、未审核，1、审核中，2、通过，3、模糊，4，虚假',
                    $row['status'] = $f_info->input_state < 2 ? $this->getTranslation()->_('audit_status.1') : $row['status'];//0、未审核，1、审核中，2、通过，3、模糊，4，虚假',
                    $row['time'] = empty($f_info->input_at) ? '' : date('Y-m-d H:i:s',strtotime($f_info->input_at) + $add_hour * 3600);
                    $row['remark'] = '';
                    if($f_info->input_state == 3)
                        $row['remark'] = $this->getTranslation()->_('fuel_blur');
                    if($f_info->input_state == 4)
                        $row['remark'] = $this->getTranslation()->_('fuel_false');
                    $returnData['data']['stream'][] = $row;
                }

                break;
            case enums::$audit_type['PA'] : //处罚申诉
                $apServer = new ApprovalServer($this->lang, $this->timezone);
                $returnData = $apServer->getAuditDetail($primaryId, enums::$audit_type['PA'], $staff_id, $comeFrom);
                break;
            case enums::$audit_type['AR'] : //处罚申诉
                $apServer = new ApprovalServer($this->lang, $this->timezone);
                $returnData = $apServer->getAuditDetail($primaryId, enums::$audit_type['AR'], $staff_id, $comeFrom);
                break;
            case enums::$audit_type['ST'] : //offer签字
                $apServer = new ApprovalServer($this->lang, $this->timezone);
                $returnData = $apServer->getAuditDetail($primaryId, enums::$audit_type['ST'], $staff_id, $comeFrom);
                break;
            case enums::$audit_type['OPR'] : //处罚申诉
                $apServer = new ApprovalServer($this->lang, $this->timezone);
                $returnData = $apServer->getAuditDetail($primaryId, enums::$audit_type['OPR'], $staff_id, $comeFrom);
                break;
            case enums::$audit_type['SAS']:
                $apServer = new ApprovalServer($this->lang, $this->timezone);
                $returnData = $apServer->getAuditDetail($primaryId, enums::$audit_type['SAS'], $staff_id, $comeFrom);
                break;
            case enums::$audit_type['SASS']:
                $apServer = new ApprovalServer($this->lang, $this->timezone);
                $returnData = $apServer->getAuditDetail($primaryId, enums::$audit_type['SASS'], $staff_id, $comeFrom);
                break;
        }

        if (empty($returnData['data']['stream'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('approver_is_not_exist'));
        }
        //提交人头像
        $returnData['data']['head']['avator'] = $this->re['auditlist']->getPersonAvator($info['staff_id_union']);
        return $this->checkReturn($returnData);
    }

    /**
     * 获取列表详情
     * @param $staff_id int 类型
     * @param $type int 类型
     * @return array
     * @throws \FlashExpress\bi\App\library\Exception\InnerException
     */
    public function getDetailV2($paramIn = []): array
    {
        //[1]参数定义
        $auditId       = $this->processingDefault($paramIn, 'audit_id', 2);
        $auditType     = $this->processingDefault($paramIn, 'audit_type', 1);
        $staffId       = $this->processingDefault($paramIn, 'staff_id', 2);
        $auditShowType = $this->processingDefault($paramIn, 'audit_show_type', 2, 1);
        $auditStateType= $this->processingDefault($paramIn, 'audit_state_type', 2, 1);
        $date_created  = $this->processingDefault($paramIn, 'date_created', 1); //数据的创建时间查分表用
        $returnData    = ['data' => ['head' => [], 'detail' => [], 'stream' => []]];

        //[2]判断详情是否存在
        //if (empty($info)) {
        //    return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
        //}

        //[3]获取审批详情
        $apServer = new ApprovalServer($this->lang, $this->timezone);
        $returnData = $apServer->getAuditDetailV2($auditId, $auditType, $staffId, $auditShowType, $auditStateType, $date_created);
        return $this->checkReturn($returnData);
    }

    /**
     * 获取列表详情
     * @param $staff_id int 类型
     * @param $type int 类型
     * @return array
     * @throws \FlashExpress\bi\App\library\Exception\InnerException
     */
    public function getAuditLogs($paramIn = []): array
    {
        //[1]参数定义
        $auditId       = $this->processingDefault($paramIn, 'audit_id', 1);
        $auditType     = $this->processingDefault($paramIn, 'audit_type', 1);
        $staffId       = $this->processingDefault($paramIn, 'staff_id', 2);
        $date_created  = $this->processingDefault($paramIn, 'date_created', 1); //数据的创建时间查分表用

        //[3]获取审批详情
        $apServer = new ApprovalServer($this->lang, $this->timezone);
        $returnData = $apServer->getAuditLogs($auditId, $auditType, $staffId, $date_created, 4);

        return $this->checkReturn(['data' => $returnData]);
    }

    /**
     * 处理工作流
     * @param array $paramIn
     * @return array
     */
    public function generateStream($paramIn = [])
    {
        $type       = $paramIn['type'];
        $staffId    = $paramIn['staff_id'];
        $created_at = $paramIn['created_at'];
        $updated_at = $paramIn['updated_at'];
        $status     = $paramIn['status'];
        $id         = $paramIn['id'];
        $origin_id  = $paramIn['origin_id'] ?? 0;

        $result = [];
        if ($type == 4) { //OT
            $type_format  = array(
                1 => 5,
                2 => 6,
                3 => 7,
                4 => 8,
            );
            $overtimeInfo = $this->re['overtime']->infoOvertime(['overtime_id' => $id]);
            $sub_type     = $type_format[$overtimeInfo['type']];
        } else if ($type == 9) { // 物料
            $id       = $origin_id;
            $sub_type = $type;
        } else { //补卡、请假、LH
            $sub_type = $type;
        }
        $records = $this->re['auditLog']->getAuditRecords(['id' => $id, 'type' => $sub_type]);

        if (empty($records)) { //没有审批记录或只有一条记录
            $staffServer       = new StaffServer();
            $staff_higher_info = $staffServer->getStaffMangerId($staffId);
            if (empty($staff_higher_info)) {
                return [];
            }
            $staff_higher_info = array_column($staff_higher_info, 'value');
            $staffData         = $this->re['staff']->getStaffpositionV2($staffId);
            $staff_higher_data = $this->re['staff']->getStaffpositionV2($staff_higher_info[0]);
            $result[]          = [
                'staff_id'    => $staffId,
                'name'        => $staffData['name'],
                'position'    => $staffData['job_name'],
                'department'  => $staffData['department_name'],
                'store_id'    => $staffData['organization_id'],
                'status'      => $this->getTranslation()->_('send_request'),
                'status_code' => 8,
                'time'        => $created_at,
                'is_OK'       => 0
            ];
            $result[]          = [
                'staff_id'    => $staff_higher_info[0],
                'name'        => $staff_higher_data['name'] ?? '',
                'position'    => $staff_higher_data['job_name'] ?? '',
                'department'  => $staff_higher_data['department_name'] ?? '',
                'store_id'    => $staff_higher_data['organization_id'] ?? '',
                'status'      => $this->re['auditlist']->getAuditStatus('10' . $status),
                'status_code' => intval($status),
                'time'        => $updated_at,
                'is_OK'       => $status == 1 ? 1 : 0
            ];
        } else { //多条审批记录

            foreach ($records as $k => $record) {
                if ($record['to_status_type'] == 0) { //无效数据
                    continue;
                }
                $staffData   = $this->re['staff']->getStaffpositionV2($record['operator']);
                $status_txt  = '';
                $status_code = 0;
                if ($k !== 0) { //不是第一个待审批

                    if (intval($record['to_status_type']) < intval($record['original_type'])
                        && $type != 12) { // 领导驳回，需补充一条驳回数据&&当前审批状态不是同意
                        // 已驳回
                        $result[] = [
                            'staff_id'    => $record['operator'],
                            'name'        => $staffData['name'],
                            'position'    => $staffData['job_name'],
                            'department'  => $staffData['department_name'],
                            'store_id'    => $staffData['organization_id'],
                            'status'      => $this->getTranslation()->_('cancel'),
                            'status_code' => 4,
                            'time'        => $record['created_at'],
                            'is_OK'       => 0
                        ];

                        if (($k + 1) == count($records)) {
                            // 待审批
                            $result[] = [
                                'staff_id'    => $record['operator'],
                                'name'        => $staffData['name'] ?? '',
                                'position'    => $staffData['job_name'] ?? '',
                                'department'  => $staffData['department_name'] ?? '',
                                'store_id'    => $staffData['organization_id'] ?? '',
                                'status'      => $this->re['auditlist']->getAuditStatus('10' . $record['to_status_type']),
                                'status_code' => 1,
                                'time'        => $record['created_at'],
                                'is_OK'       => 1
                            ];
                        }
                        continue;
                    } else { // 已同意、个人撤销
                        $status_code = $record['to_status_type'] == 6 ? 2 : $record['to_status_type'];
                        $status_txt  = $this->re['auditlist']->getAuditStatus('10' . $record['to_status_type']);
                    }
                } else {
                    $status_code = 8;
                    $status_txt  = $this->getTranslation()->_('send_request');
                }

                $result[] = [
                    'staff_id'    => $record['operator'],
                    'name'        => $staffData['name'] ?? '',
                    'position'    => $staffData['job_name'] ?? '',
                    'department'  => $staffData['department_name'] ?? '',
                    'store_id'    => $staffData['organization_id'] ?? '',
                    'status'      => $status_txt,
                    'status_code' => intval($status_code),
                    'time'        => $record['created_at'],
                    'is_OK'       => $status_code == 1 ? 1 : 0
                ];
            }


            if (count($records) == 1) { //如果只有待审批log，则补充一条审批人信息
                if ($type == 9) { //物料采购
                    $buyerArr      = UC('wmsRole')['buyer_id'];
                    $approver_id   = implode(',', $buyerArr);
                    $approver_name = '';

                    $result[] = [
                        'staff_id'    => $approver_id,
                        'name'        => $approver_name ?? '',
                        'position'    => $staff_higher_data['job_name'] ?? '',
                        'department'  => $staff_higher_data['department_name'] ?? '',
                        'store_id'    => $staff_higher_data['organization_id'] ?? '',
                        'status'      => $this->re['auditlist']->getAuditStatus('10' . $status),
                        'status_code' => intval($status),
                        'time'        => $updated_at,
                        'is_OK'       => $status == 1 ? 1 : 0 //只有一条待审核
                    ];
                } else if ($type == 14) { //外协员工
                    $approvalIds = UC('outsourcingStaff')['approvalId'];

                    $staffInfo    = $this->re['staff']->checkoutStaffBatch(array_unique($approvalIds));
                    $staffInfo    = array_column($staffInfo, 'name', 'staff_info_id');
                    $approvalData = [];
                    if ($approvalIds) {
                        foreach (array_values($approvalIds) as $k => $approvalId) {
                            if (!empty($approvalId)) {
                                if ($k == 0) {
                                    $approvalData['staff_id'] = $approvalId;
                                    $approvalData['name']     = $staffInfo[$approvalId] ?? '';
                                } else {
                                    $approvalData['staff_id_' . $k] = "({$approvalId})";;
                                    $approvalData['name_' . $k] = $staffInfo[$approvalId] ?? '';
                                }
                            }
                        }
                    }
                    $common   = [
                        'position'    => '',
                        'department'  => '',
                        'store_id'    => '',
                        'status'      => $this->re['auditlist']->getAuditStatus('10' . $status),
                        'status_code' => intval($status),
                        'time'        => $updated_at,
                        'is_OK'       => $status == 1 ? 1 : 0 //只有一条待审核
                    ];
                    $result[] = array_merge($approvalData, $common);
                } else {
                    $staffServer       = new StaffServer();
                    $staff_higher_info = $staffServer->getHigherStaffId($staffId);
                    if (empty($staff_higher_info)) {
                        return [];
                    }
                    $staff_higher_info = array_values($staff_higher_info);
                    $staff_higher_data = $this->re['staff']->getStaffpositionV2($staff_higher_info[0]);
                    $approver_id       = $staff_higher_info[0];
                    $approver_name     = $staff_higher_data['name'] ?? '';

                    $result[] = [
                        'staff_id'    => $approver_id,
                        'name'        => $approver_name ?? '',
                        'position'    => $staff_higher_data['job_name'] ?? '',
                        'department'  => $staff_higher_data['department_name'] ?? '',
                        'store_id'    => $staff_higher_data['organization_id'] ?? '',
                        'status'      => $this->re['auditlist']->getAuditStatus('10' . $status),
                        'status_code' => intval($status),
                        'time'        => $updated_at,
                        'is_OK'       => $status == 1 ? 1 : 0 //只有一条待审核
                    ];
                }
            }

            if ($type == 12 && $status == 1 && count($records) > 1) {
                $result[] = [
                    'staff_id'    => 0,
                    'name'        => $this->getTranslation()->_('vehicle_department'),
                    'position'    => '',
                    'department'  => '',
                    'store_id'    => '',
                    'status'      => $this->re['auditlist']->getAuditStatus('10' . $status),
                    'status_code' => 1,
                    'time'        => '',
                    'is_OK'       => 1
                ];
            }
        }
        //追加昵称字段
        $staffInfoArrs = array_column($result, 'staff_id');
        $staffArr = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in({staffs:array})',
            'bind'       => ['staffs' => $staffInfoArrs],
            'columns'    => ['staff_info_id', 'nick_name']
        ])->toArray();
        $staffArr = array_column($staffArr, 'nick_name', 'staff_info_id');
        foreach ($result as $k => $v) {
            $result[$k]['name'] =  isset($staffArr[$v['staff_id']]) && $staffArr[$v['staff_id']]
                ? $v['name'] . "({$staffArr[$v['staff_id']]})"
                : $v['name'];
        }
        return $result;
    }

    /**
     * 更新approval状态[类型：Fleet]
     * @param int id        主键ID
     * @param int status    审批状态
     * @param int type      审批类型
     * @return boolean
     */
    public function updateCommonApproval($paramIn = [])
    {
        $id     = $this->processingDefault($paramIn, 'id', 1);
        $status = $this->processingDefault($paramIn, 'status', 2);
        $type   = $this->processingDefault($paramIn, 'type', 2);

        $where = " id_union = '{$id}' and type_union = {$type} and approval_id != 0 and status_union = 107";

        $res = $this->re['auditlist']->updateApproval($where, $status);
        return $res;
    }

    /**
     * 获取多级审批流
     * @param array $paramIn
     *        array auditLog        审批记录
     *        int   status          审批状态
     *        array approvals       下一级待审批人ids
     * @return array
     */
    public function getMutiApprovalStream($paramIn = []): array
    {
        //[1]参数定义
        $id        = $this->processingDefault($paramIn, 'id');
        $type      = $this->processingDefault($paramIn, 'type');
        $status    = $this->processingDefault($paramIn, 'status');
        $approvals = $this->processingDefault($paramIn, 'approvals');

        $auditLogs  = $this->re['auditLog']->getAuditRecords(['id' => $id, 'type' => $type]);
        $returnData = [];
        foreach ($auditLogs as $k => $log) {
            $staffData = $this->re['staff']->getStaffpositionV2($log['operator']);

            $currentStatus = $log['to_status_type'];
            if ($k == 0) {
                $currentStatus = 8;
            }

            $status_text  = $this->re['auditlist']->getAuditStatus('10' . $currentStatus);
            $returnData[] = [
                'staff_id'    => $log['operator'],
                'name'        => $staffData['name'],
                'position'    => $staffData['job_name'],
                'department'  => $staffData['department_name'],
                'status'      => $status_text,
                'status_code' => intval($currentStatus),
                'time'        => $log['created_at'],
                'is_OK'       => $currentStatus == 1 ? 1 : 0, //如果是提交申请或是最终状态就不是高亮
            ];
        }
        if (!in_array($status, [
            enums::$audit_status['approved'],
            enums::$audit_status['dismissed'],
            enums::$audit_status['revoked'],
            enums::$audit_status['timedout']])) {
            if (count($approvals) > 1) {

                if ($type == enums::$audit_type['VA']) {
                    $data = [
                        'staff_id'    => 0,
                        'name'        => $this->getTranslation()->_('vehicle_department'),
                        'position'    => '',
                        'department'  => '',
                        'store_id'    => '',
                        'status'      => $this->re['auditlist']->getAuditStatus('10' . $status),
                        'status_code' => 1,
                        'time'        => '',
                        'is_OK'       => 1
                    ];
                } else {
                    $staffData = $this->re['staff']->getStaffpositionV2(current($approvals));
                    $data      = [
                        'staff_id'    => implode(',', $approvals),
                        'name'        => $staffData['name'] ?? '',
                        'position'    => $staffData['job_name'] ?? '',
                        'department'  => $staffData['department_name'] ?? '',
                        'status'      => $this->re['auditlist']->getAuditStatus('101'),
                        'status_code' => 1,
                        'time'        => '',
                        'is_OK'       => 1
                    ];
                }
            } else {
                $staffData = $this->re['staff']->getStaffpositionV2(current($approvals));
                $data      = [
                    'staff_id'    => implode(',', $approvals),
                    'name'        => $staffData['name'] ?? '',
                    'position'    => $staffData['job_name'] ?? '',
                    'department'  => $staffData['department_name'] ?? '',
                    'status'      => $this->re['auditlist']->getAuditStatus('101'),
                    'status_code' => 1,
                    'time'        => '',
                    'is_OK'       => 1
                ];

            }
            array_push($returnData, $data);
        }
        $staffInfoArrs = array_column($returnData, 'staff_id');

        //追加昵称字段
        $staffArr = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in({staffs:array})',
            'bind'       => ['staffs' => $staffInfoArrs],
            'columns'    => ['staff_info_id', 'nick_name']
        ])->toArray();
        $staffArr = array_column($staffArr, 'nick_name', 'staff_info_id');
        foreach ($returnData as $k => $v) {
            $returnData[$k]['name'] =  isset($staffArr[$v['staff_id']]) && $staffArr[$v['staff_id']]
                ? $v['name'] . "({$staffArr[$v['staff_id']]})"
                : $v['name'];
        }

        return $returnData;
    }


    /**
     * 获取资产申请订单
     * @param in id
     * @return int
     */
    public function getAssetOrderDetail($id, $lang)
    {
        //没有语言 默认英文
        $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);
        $sql        = "--
                select
                    a.id,
                    a.order_union_id,
                    a.staff_info_id,
                    a.organization_id,
                    a.reason,
                    a.wf_role,
                    b.goods_id,
                    b.id as item_id,
                    b.recomment_num,
                    b.approval_num,
                    a.status,
                    a.reject_reason,
                    a.use_people_id,
                    a.use_store_id,
                    c.bar_code,
                    c.is_public,
                    CONVERT_TZ(a.updated_at, '+00:00', '{$this->timeZone}') as updated_at,
                    CONVERT_TZ(a.created_at, '+00:00', '{$this->timeZone}') as created_at,
                    c.goods_name_{$lang} as goods_name
                from
                    assets_order as a
                left join
                    assets_order_detail as b
                on
                    a.id = b.order_id
                left join
                    assets_goods as c
                on
                    b.goods_id = c.id
                where
                    a.id={$id} order by b.created_at asc";
        $data       = $this->getDI()->get('db')->query($sql);
        $resultData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $resultData;
    }


    public function getApprovalInfo($ids)
    {

        $ids        = implode(',', $ids);
        $sql        = "--
                SELECT
                    id,name
                FROM
                    staff_info
                WHERE
                    id in ({$ids}) group by id
                    ;";
        $data       = $this->getDI()->get('db_fle')->query($sql);
        $resultData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $resultData;
    }

    /**
     * 获取审批人
     * @param $paramIn
     * @return mixed
     */
    public function getApprovalList($paramIn)
    {
        $type    = $this->processingDefault($paramIn, 'type');
        $auditId = $this->processingDefault($paramIn, 'audit_id');

        $sql        = "--
                SELECT
                    *
                FROM
                    staff_audit_approval
                WHERE
                    type = {$type} and audit_id = {$auditId} and status = 6
                ";
        $data       = $this->getDI()->get('db')->query($sql);
        $resultData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $resultData;
    }

    /**
     * 获取审批记录审批状态
     */
    public function getApprovalAuditStatus($paramIn = [])
    {
        $result     = $this->re['auditlist']->getApprovalAuditStatus($paramIn);
        $returnData = $result ? $result['status_union'] : 0;
        return $returnData;
    }

    /**
     * HC status转换为前端status
     * 前端状态 1-待审批 2-已同意 3-驳回 4-撤销 6-同意[非最终]
     */
    public function transferHcStatus($status)
    {
        $transfer = [
            1 => 1,
            2 => 6,
            3 => 2,
            4 => 4,
            5 => 3,
            6 => 2,
            7 => 1,
            8 => 8,
        ];

        return $transfer[$status] ?? 0;
    }

    public function transferAuditStatus($status)
    {
        $transfer = [
            1 => 1,
            2 => 2,
            3 => 3,
            4 => 4,
            5 => 5,
            6 => 2,
            7 => 1,
            8 => 8,
        ];

        return $transfer[$status] ?? 0;
    }

    /**
     * 获取审批行为
     * @param array $paramIn
     * @return mixed
     */
    public function getOptions($paramIn = [])
    {
        $status  = $this->processingDefault($paramIn, 'status');
        $options = $this->processingDefault($paramIn, 'option');

        if (empty($status) || empty($options)) {
            return (object)null;
        }

        if (in_array($status, [2, 3, 4, 6])) {
            $option = $options['options']['afterApproval'];
        } else {
            $option = $options['options']['beforeApproval'];
        }
        if ($option != 0) {
            foreach (explode(',', $option) as $v) {
                $returnData['code' . $v] = intval($v);
            }
        }
        if (empty($returnData)) {
            $returnData = (object)null;
        }
        return $returnData;
    }

    /**
     * 获取概要信息
     * @param string $summary 概要信息josn串
     */
    public function getSummary($summary, $isUnpaid = false): array
    {
        //[1]解析数据
        $summaryInfo = json_decode($summary, true);
        $returnData  = [];
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $otServer = new OvertimeServer($this->lang, $this->timezone);
        $overtime_type = $otServer->getDetailOtType();
        $osOtServer = new OsOvertimeServer($this->lang, $this->timezone);
        $osOtType = $osOtServer->getDetailOtType();
        $t =  $this->getTranslation();
        if (!empty($summaryInfo)) {
            foreach ($summaryInfo as $k => $v) {
                $returnData[$k]['key'] = $t->_($v['key']);
                switch ($v['key']) {
                    case 'attendance_type':
                        $returnData[$k]['value'] = $this->re['audit']->typeName(1)[$v['value']];
                        //马来 要改补卡类型的翻译
                        if(isCountry('MY') && $isUnpaid){
                            $returnData[$k]['value'] = $this->re['audit']->reissueTypeUnpaid()[$v['value']];
                        }
                        break;
                    case '22495_agent_reissue_type'://菲律宾 个人代理补卡
                        $returnData[$k]['value'] = $this->re['audit']->reissueTypeUnpaid()[$v['value']];
                        break;
                    case 'leave_type':
                    case 'agent_type':
                        $text = isset($v['value']) && $v['value'] ? ($this->re['audit']->typeName(2)[$v['value']] ?? $v['value']) : '';
                        if($v['value'] == enums::LEAVE_TYPE_3 && strtolower(env('country_code', 'Th')) == 'th'){
                            $text = $this->re['audit']->typeName(2)[enums::LEAVE_TYPE_38] ?? '';
                        }
                        //个人代理 马来的国民假
                        if($v['value'] == enums::LEAVE_TYPE_42 && $isUnpaid){
                            $text = $this->getTranslation()->_('leave_42_unpaid');
                        }
                        $returnData[$k]['value'] = $text;
                        break;
                    case 'OT_type':
                    case 'bonus_type':
                        $returnData[$k]['value'] = isset($v['value']) && $v['value'] ? ($overtime_type[$v['value']] ?? $v['value']) : '';
                        break;
                    case 'os_overtime_type':
                        $returnData[$k]['value'] = isset($v['value']) && $v['value'] ? ($osOtType[$v['value']] ?? $v['value']) : '';
                        break;
                    case 'report_reason':
                        $returnData[$k]['value'] =  $t->_('t_warning_'.$v['value']);
                        break;
                    case 'organization_id':
                        $store = SysStoreModel::findFirst([
                            'conditions' => 'id = :storeId:',
                            'bind'       => ['storeId' => $v['value']]
                        ]);
                        $store = $store ? $store->toArray() : [];
                        $returnData[$k]['value'] = $store && isset($store['name']) ? $store['name'] . " " . $v['value'] : "";
                        break;
                    case 'operator_id':
                        $staff = HrStaffInfoModel::findFirst([
                            'conditions' => 'staff_info_id = :staff_id:',
                            'bind'       => ['staff_id' => $v['value']]
                        ]);
                        $staff = $staff ? $staff->toArray() : [];
                        $returnData[$k]['value'] = $staff && isset($staff['name']) ? $staff['name'] . " " . $v['value'] : "";
                        break;
                    case 'approval_at':
                        $returnData[$k]['value'] = date("Y-m-d H:i:s", strtotime($v['value']) + $add_hour * 3600);
                        break;
                    case 'penalty_reason':
                        $returnData[$k]['value'] = $this->getTranslation()->_($v['value']);
                        break;
                    case 'salary_package':
                    case 'basic_salary':
                        if (strtolower(env('country_code', 'Th')) == 'th') {
                            $returnData[$k]['value'] = str_replace('THB', '', $v['value']) . ' THB';
                        } else if (strtolower(env('country_code', 'Ph')) == 'ph') {
                            $returnData[$k]['value'] = str_replace('THB', '', $v['value']) . ' PHP';
                        } else if (strtolower(env('country_code', 'My')) == 'my') {
                            $returnData[$k]['value'] = str_replace('THB', '', $v['value']) . ' MYR';
                        } else if (strtolower(env('country_code', 'La')) == 'la') {
                            $returnData[$k]['value'] = str_replace('THB', '', $v['value']) . ' LAK';
                        } else if (strtolower(env('country_code', 'Id')) == 'id') {
                            $returnData[$k]['value'] = str_replace('THB', '', $v['value']) . ' IDR';
                        }else if (strtolower(env('country_code', 'Id')) == 'vn') {
                            $returnData[$k]['value'] = str_replace('THB', '', $v['value']) . ' VND';
                        }
                        break;
                    case 'hr_penalty_reason':
                        $reasonMap = HrPenaltyDetailModel::getPenaltyReasonMap();
                        $returnData[$k]['value'] = $this->getTranslation()->_($reasonMap[$v['value']] ?? "");
                        break;
                    case 'stop_duty_reason':
                        $class = $this->class_factory('ReinstatementServer',$this->lang, $this->timezone);
                        $reasons = $class->getSuspensionReason();
                        $returnData[$k]['value'] = $reasons[$v['value']] ?? '';
                        break;
                    case 'destination_city'://出差目的地城市
                        if(!strstr($v['value'], 'code')){
                            $returnData[$k]['value'] = $v['value'];
                            break;
                        }
                        $code = explode(' ', $v['value']);
                        $code = $code[1];
                        $class = new BusinesstripServer($this->lang, $this->timezone);
                        $cityName = $class->formatProvinceCity($code);
                        $returnData[$k]['value'] = $cityName;
                        break;
                    case 'fault_classification'://故障分类
                        $vehicleRepairRequestEnums = (new VehicleCompanyInfoServer($this->lang, $this->timezone))->getStaticData();
                        $faultType = !empty($vehicleRepairRequestEnums['fault_type']) ? array_column($vehicleRepairRequestEnums['fault_type'], 'label', 'value') : [];
                        $returnData[$k]['value'] = $faultType ? $faultType[$v['value']] ?? '' : '';
                        break;
                    case 'fault_location'://故障部位
                        $faultPartText = [];
                        $vehicleRepairRequestEnums = (new VehicleCompanyInfoServer($this->lang, $this->timezone))->getStaticData();
                        if (!empty($vehicleRepairRequestEnums['fault_part'])) {
                            $faultPartsEnums = (array_column($vehicleRepairRequestEnums['fault_part'], 'label', 'value'));
                            foreach ($v['value'] as $faultPart) {
                                $faultPartText[] = $faultPartsEnums[$faultPart];
                            }
                        }
                        $returnData[$k]['value'] = implode('/', $faultPartText);
                        break;
                    case 'work_home_place'://居家办公 地点城市
                        $code = explode(' ', $v['value']);
                        $code = $code[1];
                        $class = new BusinesstripServer($this->lang, $this->timezone);
                        $cityName = $class->formatProvinceCity($code);
                        $returnData[$k]['value'] = $cityName;
                        break;
                    default:
                        $returnData[$k]['value'] = $v['value'];
                        break;
                }
            }
        } else {
            $returnData = [];
        }


        return $returnData;
    }

    /**
     * 组织概要信息
     * @param int $id staff_audit_union主键ID
     * @param int $type 审批类型
     * @return array
     */
    public function generateSummary($id, $type)
    {
        $storeServer = new \FlashExpress\bi\App\Server\SysStoreServer();
        $param       = '';
        switch ($type) {
            case 1://补卡
                $query_sql_str = "select 
                                attendance_date,
                                attendance_type,
                                DATE_FORMAT( CONVERT_TZ( created_at, '+00:00', '{$this->timezone}' ), '%Y-%m-%d %H:%i:%s' ) AS created_at
                                from staff_audit where audit_id = {$id}";
                $info          = $this->getDI()->get('db')->query($query_sql_str)->fetch(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "attendance_date",
                            'value' => $info['attendance_date']
                        ],
                        [
                            'key'   => "attendance_type",
                            'value' => $info['attendance_type']
                        ]
                        ,[
                            'key'   => "created_at",
                            'value' => $info['created_at']
                        ]
                    ];
                }
                break;
            case 75://泰国个人代理补卡
                $query_sql_str = "select 
                                attendance_date,
                                attendance_type,
                                created_at
                                from staff_audit where audit_id = {$id}";
                $info          = $this->getDI()->get('db')->query($query_sql_str)->fetch(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "ic_service_date",
                            'value' => $info['attendance_date']
                        ],
                        [
                            'key'   => "created_at",
                            'value' => $info['created_at']
                        ]
                    ];
                }
                break;
            case 2://请假
                $query_sql_str = "select * from staff_audit where audit_id = {$id}";
                $info          = $this->getDI()->get('db')->query($query_sql_str)->fetch(\Phalcon\Db::FETCH_ASSOC);
                $typeKey = 'leave_type';
                if(in_array($info['leave_type'],[enums::LEAVE_TYPE_39,enums::LEAVE_TYPE_40,enums::LEAVE_TYPE_41])){
                    $typeKey = 'agent_type';
                }
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => $typeKey,
                            'value' => $info['leave_type']
                        ],
                        [
                            'key'   => "start_time",
                            'value' => date('Y-m-d', strtotime($info['leave_start_time']))
                        ],
                        [
                            'key'   => "end_time",
                            'value' => date('Y-m-d', strtotime($info['leave_end_time']))
                        ]
                        ,[
                            'key'   => "created_at",
                            'value' => $info['created_at']
                        ]
                    ];
                    //各国个人代理 请假展示类型名称key
                    if($typeKey == 'agent_type'){
                        $param[] = ['key' => 'audit_type_title_key', 'value' => 'th_workflow_type_2'];
                    }
                }
                break;
            case 3://lh
                $query_sql_str = "select * from staff_audit where audit_id = {$id}";
                $info          = $this->getDI()->get('db')->query($query_sql_str)->fetch(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "date",
                            'value' => $info['lh_date']
                        ],
                        [
                            'key'   => "start_time",
                            'value' => $info['lh_time']
                        ],
                        [
                            'key'   => "plate_number",
                            'value' => $info['lh_plate_number']
                        ]
                        ,[
                            'key'   => "created_at",
                            'value' => $info['created_at']
                        ]
                    ];
                }
                break;
            case 4://加班
                $query_sql_str = "select * from hr_overtime where overtime_id = {$id}";
                $info          = $this->getDI()->get('db')->query($query_sql_str)->fetch(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "OT_date",
                            'value' => $info['date_at']
                        ],
                        [
                            'key'   => "OT_type",
                            'value' => $info['type']
                        ],
                        [
                            'key'   => "duration",
                            'value' => $info['duration'] . 'h'
                        ]
                        ,[
                            'key'   => "created_at",
                            'value' => $info['created_at']
                        ]
                    ];
                }
                break;
            case 6:
                $query_sql_str = "select * from hr_hc where hc_id = {$id}";
                $info          = $this->getDI()->get('db')->query($query_sql_str)->fetch(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($info)) {
                    $storeInfo = $storeServer->getStoreName([$info['worknode_id']]);
                    $jd_sql    = "select job_name from hr_jd where job_id = {$info['job_id']}";
                    $jdInfo    = $this->getDI()->get('db')->query($jd_sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
                    $param     = [
                        [
                            'key'   => "store",
                            'value' => $storeInfo[$info['worknode_id']] ?? 'Head Office'
                        ],
                        [
                            'key'   => "selectJD",
                            'value' => $jdInfo['job_name']
                        ],
                        [
                            'key'   => "demandnumber",
                            'value' => $info['demandnumber']
                        ]
                    ];
                }
                break;
            case 9:
                $sql       = "SELECT * FROM wms_order where id = {$id} ";
                $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
                $sqlDetail = "SELECT * FROM wms_order_detail where order_id = '{$info_data['order_id']}' ";
                $info      = $this->getDI()->get('db')->query($sqlDetail)->fetch(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "goods_name",
                            'value' => $info['goods_name']
                        ],
                        [
                            'key'   => "num",
                            'value' => $info['num']
                        ]
                    ];
                }
                break;
            case 10://出差
                $query_sql_str = "select * from business_trip where id = {$id}";
                $info          = $this->getDI()->get('db')->query($query_sql_str)->fetch(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "destination_city",
                            'value' => $info['destination_city']
                        ],
                        [
                            'key'   => "start_time",
                            'value' => $info['start_time']
                        ],
                        [
                            'key'   => "end_time",
                            'value' => $info['end_time']
                        ]
                        ,[
                            'key'   => "created_at",
                            'value' => $info['created_at']
                        ]
                    ];
                }
                break;
            case 32://黄牌项目出差
                $query_sql_str = "select * from business_trip where id = {$id}";
                $info          = $this->getDI()->get('db')->query($query_sql_str)->fetch(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "destination_city",
                            'value' => $info['destination_city']
                        ],
                        [
                            'key'   => "start_time",
                            'value' => $info['start_time']
                        ],
                        [
                            'key'   => "end_time",
                            'value' => $info['end_time']
                        ]
                    ];
                }
                break;
            case 37://外出申请（出差的一种）
                $query_sql_str = "select * from business_trip where id = {$id}";
                $info          = $this->getDI()->get('db')->query($query_sql_str)->fetch(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "go_out_location",//外出地点
                            'value' => $info['destination_city']
                        ],
                        [
                            'key'   => "start_time",
                            'value' => $info['start_time']
                        ],
                        [
                            'key'   => "end_time",
                            'value' => $info['end_time']
                        ]
                    ];
                }
                break;
            case 11:
                $query_sql_str = "select * from vehicle_mileage where id = {$id}";
                $info          = $this->getDI()->get('db')->query($query_sql_str)->fetch(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "date",
                            'value' => $info['mileage_date']
                        ],
                        [
                            'key'   => "start_kilometres",
                            'value' => bcdiv($info['start_kilometres'], 1000, 2),
                        ],
                        [
                            'key'   => "end_kilometres",
                            'value' => bcdiv($info['end_kilometres'], 1000, 2),
                        ]
                    ];
                }
                break;
            case 12:
                $fleetServer = new \FlashExpress\bi\App\Server\FleetServer($this->lang, $this->timezone);
                $info        = $fleetServer->getFleetDetail(['fleet_id' => $id, 'require_extend' => false]);
                if (!isset($info['car_type'])) {
                    break;
                }
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "car_type",
                            'value' => $fleetServer->getCarTypeList($info['car_type'])['type_txt'] ?? ''
                        ],
                        [
                            'key'   => "arrive_time",
                            'value' => $info['expected_date'] ?? ''
                        ],
                        [
                            'key'   => "start_store",
                            'value' => $info['start_store_name']
                        ]
                    ];
                }
                break;
            case 13:
                $query_sql_str = "select * from `staff_resign` where `resign_id` = {$id}";
                $info          = $this->getDI()->get('db')->query($query_sql_str)->fetch(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "leave_date",
                            'value' => $info['leave_date'],
                        ],
                        [
                            'key'   => "work_handover",
                            'value' => $info['work_handover'],
                        ]
                    ];
                }
                break;
            case 14:
                $osServer = new OsStaffServer($this->lang, $this->timezone);
                $info     = $osServer->getOsStaffDetail(['id' => $id]);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "os_staff_job_title",
                            'value' => $info['job_title']
                        ],
                        [
                            'key'   => "store",
                            'value' => $info['store_name']
                        ],
                        [
                            'key'   => "demend_num",
                            'value' => $info['demend_num']
                        ]
                    ];
                }
                break;
            case 16:
                $param         = [];
                $lang          = strtolower(substr($this->lang, 0, 2));
                //没有语言 默认英文
                $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);
                $query_sql_str = "--
                select
                        b.goods_id,b.recomment_num,c.goods_name_{$lang} as goods_name
                from
                    assets_order as a
                left join
                    assets_order_detail as b
                on
                    a.id=b.order_id
                left join
                    assets_goods as c
                on
                    b.goods_id = c.id
                where
                    a.id = {$id}";
                $info          = $this->getDI()->get('db')->query($query_sql_str)->fetchALL(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($info)) {
                    foreach ($info as $key => $value) {
                        $param[] = ['key' => $value['goods_name'], 'value' => $value['recomment_num']];
                    }
                }
                break;
            case enums::$audit_type["TF"]:
                $osServer = new JobtransferRepository($this->timezone);
                $info = $osServer->getJobtransferInfo(['id' => $id]);
                $ss = new SysStoreServer();
                $storeList = $ss->getStoreName([$info['current_store_id'], $info['after_store_id']]);
                $storeRegionPieceId = $ss->getStoreRegionPiece($info['current_store_id']);
                if (empty($storeRegionPieceId['manage_region'])) {
                    $storeRegionPiece['manage_region'] = '';
                } else {
                    $storeRegionName = $ss->getStoreRegionName($storeRegionPieceId['manage_region']);
                    $storeRegionPiece['manage_region'] = $storeRegionName['manage_region'];
                }
                if (empty($storeRegionPieceId['manage_piece'])) {
                    $storeRegionPiece['manage_piece'] = '';
                } else {
                    $storePieceName = $ss->getStorePieceName($storeRegionPieceId['manage_piece']);
                    $storeRegionPiece['manage_piece'] = $storePieceName['manage_piece'];
                }
                if (!empty($info)) {
                    $param = [
                        [
                            'key' => "jobtransfer_0006",
                            'value' => $info['staff_id']
                        ],
                        [
                            'key' => "jobtransfer_0007",
                            'value' => $storeRegionPiece['manage_region'] ?? ''
                        ],
                        [
                            'key' => "jobtransfer_0008",
                            'value' => $storeRegionPiece['manage_piece'] ?? ''
                        ],
                        [
                            'key' => "jobtransfer_0009",
                            'value' => $storeList[$info['after_store_id']] ?? ''
                        ],
                        [
                            'key' => "jobtransfer_0010",
                            'value' => $storeList[$info['current_store_id']] ?? ''
                        ],
                    ];
                }
                break;
            case enums::$audit_type['FD']:
                $osServer = new DiscountServer($this->lang, $this->timezone);
                $info     = $osServer->getDetail(['id' => $id]);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "phone_no",
                            'value' => $info['costomer_mobile']
                        ],
                        [
                            'key'   => "costomer_id",
                            'value' => $info['costomer_id']
                        ]
                    ];
                }
                break;
            case enums::$audit_type['MSG']:
                $osServer = new MessageCenterServer($this->lang, $this->timezone);
                $info     = $osServer->getDetail(['id' => $id]);
                if (!empty($info)) {
                    $param = [
                        [
                            'key'   => "phone_no",
                            'value' => $info['costomer_mobile']
                        ],
                        [
                            'key'   => "costomer_id",
                            'value' => $info['costomer_id']
                        ]
                    ];
                }
                break;
            default:
                break;
        }
        return $param;
    }

    /**
     * 根据申请类型查询对应类型的数据
     * @param int $paramIn 传入参数
     * @return array
     */
    public function getAuditListByTypeV2($paramIn = []): array
    {
        //[1]获取参数
        $type       = $this->processingDefault($paramIn, 'type');
        $pageNum    = $this->processingDefault($paramIn, 'page_num');
        $pageSize   = $this->processingDefault($paramIn, 'page_size');
        $source   = $this->processingDefault($paramIn, 'source');
        $staffId =  $this->processingDefault($paramIn, 'staff_id',2,0);

        //[2]获取指定类型条件下的的ids
        switch ($type) {
            case enums::$audit_type['MA']: //物料
                $param       = [
                    'start_date',
                    'end_date',
                    'serial_no',
                    'wms_id',
                    'store_id',
                    'type',
                    'staff_approval_id',
                    'status',
                    'page_size',
                    'page_num',
                ];
                $searchWhere = checkParam($param, $paramIn);
                [$returnArr, $count]  = (new WmsRepository($this->timezone))->getWmsIdsV2([
                    'searchWhere' => $searchWhere,
                    'page_size'   => $pageSize,
                    'page_num'    => $pageNum,
                ]);
                break;
            case enums::$audit_type['AS'] . "_" . enums::$audit_type['ASP']: //资产
            case enums::$audit_type['ASP']: //公共资产
            case enums::$audit_type['AS']: //个人资产
                $param       = [
                    'start_date',
                    'end_date',
                    'serial_no',
                    'goods_id',
                    'store_id',
                    'type',
                    'staff_approval_id',
                    'status',
                    'page_size',
                    'page_num',
                ];
                $searchWhere = checkParam($param, $paramIn);
                [$returnArr, $count]  = (new AssetRepository())->getAssetIdsV2([
                    'searchWhere' => $searchWhere,
                    'page_size'   => $pageSize,
                    'page_num'    => $pageNum,
                    'source'      => $source?$source:0
                ]);
                break;
            case enums::$audit_type['HC']: //HC
                $repo = new AuditlistRepository($this->lang, $this->timezone);
                $conditions['state'] = [
                    enums::APPROVAL_STATUS_PENDING
                ];
                $conditions['audit_type'] = [enums::$audit_type['HC']];
                $returnArr = $repo->getAuditApprovalList($staffId, $conditions, '*', 1, $pageNum, $pageSize);
                $count = 0;
                break;
            default:
                return [];
        }

        if(!empty($source)){
            return [
                'dataList'   => [],
                'pagination' => [
                    'count'     => $count ?? 0,
                    'pageCount' => 0,
                    'pageNum'   => $pageNum,
                    'pageSize'  => $pageSize
                ]
            ];
        }

        if (empty($returnArr)) {
            return [
                'dataList'   => [],
                'pagination' => [
                    'count'     => 0,
                    'pageCount' => 0,
                    'pageNum'   => $pageNum,
                    'pageSize'  => $pageSize
                ]
            ];
        }

        //HC 不需要查询
        if(enums::$audit_type['HC'] != $type) {
            //获取网点名
            $storeList = (new SysStoreServer())->getStoreName(array_column($returnArr, 'store_id'));

        //获取工号、姓名
        $staffInfo = (new StaffRepository())->checkoutStaffBatch(array_column($returnArr, 'staff_id'));
        $staffInfo = array_column($staffInfo, 'name', 'staff_info_id');

        foreach ($returnArr as $k => $v) {
            //申请人姓名
            $returnArr[$k]['staff_name'] = $staffInfo[$v['staff_id']] ?? '';
            //申请网点名
            $returnArr[$k]['store_name'] = $storeList[$v['store_id']] ?? '';
        }

        }
        //[4]返回数据
        $returnData = [
            'dataList'   => $returnArr,
            'pagination' => [
                'count'     => $count ?? 0,
                'pageCount' => count($returnArr),
                'pageNum'   => $pageNum,
                'pageSize'  => $pageSize
            ]
        ];
        return $returnData;
    }

    /**
     * 根据申请类型查询对应类型的待审批数
     * @param int $paramIn 传入参数
     * @return array
     */
    public function getAuditListPendingCount($paramIn = []): array
    {
        //[1]获取参数
        $staffApprovalId = $this->processingDefault($paramIn, 'staff_approval_id');
        $type            = $this->processingDefault($paramIn, 'type');
        $assetCou        = $wmsCou = $assetV2Cou = $wmsV2Count = 0;
        if ($type == AuditListEnums::APPROVAL_TYPE_WMS_V2) {
            $wmsV2Count = $this->auditlist->getAuditListPendingCount([AuditListEnums::APPROVAL_TYPE_WMS_V2], $staffApprovalId);
        } else {
            //[2]获取资产、物料待审批数
            //2024-01-10下掉老版资产、物料的查询
            //$assetCou   = $this->auditlist->getAuditListPendingCount([AuditListEnums::APPROVAL_TYPE_ASSETS, AuditListEnums::APPROVAL_TYPE_PUBLIC_ASSETS], $staffApprovalId);
            //$wmsCou     = $this->auditlist->getAuditListPendingCount([AuditListEnums::APPROVAL_TYPE_WMS], $staffApprovalId);
            $assetV2Cou = $this->auditlist->getAuditListPendingCount([AuditListEnums::APPROVAL_TYPE_ASSET_V2], $staffApprovalId);
        }

        return [
            'asset_count'    => $assetCou,
            'wms_count'      => $wmsCou,
            'asset_v2_count' => $assetV2Cou,
            'wms_v2_count'   => $wmsV2Count,
        ];
    }

    /**
     * 根据申请类型查询对应类型的待审批数
     * @param int $paramIn 传入参数
     * @return array
     */
    public function getAuditListPendingCountV2($paramIn = []): array
    {
        //[1]获取参数
        $approvalId = $this->processingDefault($paramIn, 'approval_id');
        $auditType = $this->processingDefault($paramIn, 'biz_type');
        $result = [];

        //[2]获取资产、物料待审批数
        foreach ($auditType as $item) {
            $result[] = [
                'count' => $this->auditlist->getAuditListPendingCount([$item], $approvalId),
                'audit_type' => $item
            ];
        }

        return ['pending_count' => $result];
    }

    /**
     * 根据申请类型查询对应类型的数据
     * @param int $paramIn 传入参数
     *  id       string staff_audit_union id字段
     *  type     int    申请类型
     *  staff_id int    当前登录人的工号
     * @return array
     */
    public function getAuditDetailByType($paramIn = [])
    {
        //[1]获取传入参数
        $id           = $this->processingDefault($paramIn, 'id', 1);
        $type         = $this->processingDefault($paramIn, 'type', 1);
        $staff_id     = $this->processingDefault($paramIn, 'staff_id', 2);
        $date_created = $this->processingDefault($paramIn, 'date_created', 1); //数据的创建时间查分表用
        $version      = $this->processingDefault($paramIn, 'version', 2, 1);   //版本

        //[2]ID转换 eg:audit_101 => [audit_id => 101]
        [$idPrefix, $primaryId] = explode('_', $id);


        //[2]获取传入参数
        $staffInfo = $this->re['staff']->checkoutStaff($staff_id);
        $isCommit = $paramIn['isCommit'] ?? 2;

        //[3]获取详情
        $params    = [
            'audit_id'        => $primaryId,
            'audit_type'      => $type,
            'staff_id'        => $staff_id,
            'audit_show_type' => $isCommit,
            'userinfo'        => "",
            'staff_name'      => $staffInfo['name'] ?? '',
            'date_created'    => $date_created,
        ];

        $returnArr = $this->getDetailV2($params);

        //审批流日志
        $server = new ApprovalServer($this->lang, $this->timezone);
        $returnArr['data']['stream'] = $server->getAuditLogs($primaryId, $type, $staff_id, $date_created, $version);

        return $returnArr;
    }

    /**
     * 替换上级
     * @param array $paramIn
     * @return bool
     */
    public function replaceSuperior($paramIn = [])
    {
        //不需要了  现在已经替换至新的新审批流
        return true;
        //已经使用新的审批流 和新的更新方法
        //[1]获取参数
        $staffInfoId   = $this->processingDefault($paramIn, 'staff_info_id');
        $beforeManager = $this->processingDefault($paramIn, 'before_manager');
        $afterManager  = $this->processingDefault($paramIn, 'after_manager');

        //验证非法的数据
        if (empty($staffInfoId) || $beforeManager == $afterManager || $staffInfoId == $beforeManager) {
            return false;
        }

        //[2]替换上级
        $processList = [
            'ResignAutoDismiss',
            'fixResignApprover',
        ];

        foreach ($processList as $method) {
            $state = method_exists($this, $method);
            if ($state) {
                $data                       = $this->$method($paramIn);
                $returnArr['data'][$method] = $data;
            } else {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取审批类型和待审批数量
     * @param array $paramIn
     * @return array
     */
    public function getAuditTypesWithWaitCountV2(array $paramIn = []): array
    {
        //获取请求参数
        $isMyApply = $this->processingDefault($paramIn, 'is_my_apply');
        $staffId   = $this->processingDefault($paramIn, 'staff_id');

        //初始化参数
        $repo      = new AuditlistRepository($this->lang, $this->timezone);
        $auditTypeList = [];
        $countData = [];
        $dataList  = [];
        $totalCount= 0;
        $t         = $this->getTranslation();

        //分组
        $groups = AuditListEnums::getAuditFilterGroup();
        $auditTypeList = AuditListEnums::getAllAuditTypes();

        //获取全部的待审批数据
        $conditions = [
            'state' => [enums::APPROVAL_STATUS_PENDING],
            'audit_type' => $auditTypeList,
        ];
        $pageNum = 1;
        $pageSize = 1000;
        $repo = new AuditlistRepository($this->lang, $this->timezone);

        while ($pendingList = $repo->getAuditApprovalList($staffId, $conditions, 'biz_value,biz_type',
            AuditListEnums::LIST_SORT_ASC, $pageNum, $pageSize)) {
            if (empty($pendingList)) {
                break;
            }
            foreach ($pendingList as $item) {
                $bizType  = $item['biz_type'];
                $bizValue = $item['biz_value'];

                // 如果还没有该 biz_type 的统计项，则初始化一个空数组
                if (!isset($countData[$bizType])) {
                    $countData[$bizType] = [];
                }

                // 使用数组的键来保证 biz_value 的唯一性
                $countData[$bizType][$bizValue] = true;
            }
            if (count($pendingList) < $pageSize) {
                break;
            }
            $pageNum += 1;
        }
        $hiddenType = [];
        $staffInfo = (new StaffRepository())->checkoutStaffById($staffId);
        //隐藏部分类型
        $attendance_group_key = 'audit_group_attendance';//考勤组翻译key
        if (in_array($staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
            if(isCountry('MY')){
                $hiddenType = [
                    AuditListEnums::APPROVAL_TYPE_OVERTIME,
                    AuditListEnums::APPROVAL_TYPE_RN,
                ];
                $attendance_group_key = 'agent_audit_group_attendance_my';
            }
            if (isCountry('TH')) {
                $hiddenType           = [
                    AuditListEnums::APPROVAL_TYPE_OVERTIME,
                    AuditListEnums::APPROVAL_TYPE_AT,
                    AuditListEnums::APPROVAL_TYPE_OVERTIME_UNPAID,
                    AuditListEnums::APPROVAL_TYPE_BT,
                    AuditListEnums::APPROVAL_TYPE_ATT_BUS,
                    AuditListEnums::APPROVAL_TYPE_GO,
                    AuditListEnums::APPROVAL_TYPE_GO_CI,
                    AuditListEnums::APPROVAL_TYPE_RN,
                    AuditListEnums::APPROVAL_TYPE_YCBT,
                    AuditListEnums::APPROVAL_TYPE_OUTSOURCING_OT,
                ];
                $attendance_group_key = 'agent_audit_group_attendance';
            }
            if (isCountry('PH')) {
                $hiddenType           = [
                    AuditListEnums::APPROVAL_TYPE_OVERTIME,
                    // AuditListEnums::APPROVAL_TYPE_AT, // 菲律宾展示补卡审批类型
                    AuditListEnums::APPROVAL_TYPE_OVERTIME_UNPAID,
                    AuditListEnums::APPROVAL_TYPE_BT,
                    AuditListEnums::APPROVAL_TYPE_ATT_BUS,
                    AuditListEnums::APPROVAL_TYPE_GO,
                    AuditListEnums::APPROVAL_TYPE_GO_CI,
                    AuditListEnums::APPROVAL_TYPE_RN,
                    AuditListEnums::APPROVAL_TYPE_YCBT,
                    AuditListEnums::APPROVAL_TYPE_OUTSOURCING_OT,
                ];
                $attendance_group_key = 'agent_audit_group_attendance';
            }
        }else{
            isCountry(['TH','PH']) && $isMyApply == 1 && $hiddenType = [
                AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT,
            ];
        }
        $enums = AuditlistRepository::auditTypeForFilter();
        foreach ($groups as $groupKey => $groupArr) {
            $children = [];
            foreach ($groupArr as $item) {
                if (in_array($item, $hiddenType)) {
                    continue;
                }
                $row = [
                    'aduit_num'        => $isMyApply != 1 ? count($countData[$item] ?? []): 0,
                    'audit_type'       => $item,
                    'audit_type_title' => $t->_($enums[$item]) ?? '',
                ];

                //泰国个人代理 请假翻译替换
                if (isCountry('TH') && $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID && $item == AuditListEnums::APPROVAL_TYPE_LE){
                    $row['audit_type_title'] = $t->_('leave_40');
                }
                if (isCountry('PH') && $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID && $item == AuditListEnums::APPROVAL_TYPE_LE){
                    $row['audit_type_title'] = $t->_('leave_41');
                }
                if (isCountry('MY') && in_array($staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
                    if($item == AuditListEnums::APPROVAL_TYPE_LE){
                        $row['audit_type_title'] = $t->_('leave_unpaid_title');
                    }
                    if($item == AuditListEnums::APPROVAL_TYPE_AT){
                        $row['audit_type_title'] = $t->_('home_make_up_unpaid');
                    }
                }
                $children[] = $row;
                $totalCount += $isMyApply != 1 ? count($countData[$item] ?? []) : 0;
            }

            if ($groupKey == 'audit_group_attendance') {
                $groupKey = $attendance_group_key;
            }

            $dataList[] = [
                'group_title' => $t->_($groupKey) ?? '',
                'children'    => $children,
            ];
        }
        return self::checkReturn(['data' => ['dataList' => $dataList, 'total' => $totalCount]]);
    }

    /**
     * 模糊查找用户返回用户数据
     * Created by: Lqz.
     * @param $searchStr
     * @param bool $islimit
     * @return mixed
     * CreateTime: 2020/8/17 0017 11:50
     */
    public static function getLikeStaffs($searchStr, bool $islimit = true)
    {
        $limit = '';
        if ($islimit) {
            $limit = 3;
        }
        if (is_numeric($searchStr)) {
            $conditions = 'staff_info_id like :searchStr:';
            $bind       = ['searchStr' => "{$searchStr}%"];
        } else {
            $conditions = 'name like :searchStr:';
            $bind       = ['searchStr' => "%{$searchStr}%"];
        }
        $staffsObj = HrStaffInfoModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => "name,staff_info_id,sys_store_id",
            'limit'      => $limit
        ]);
        $staffs    = $staffsObj->toArray();

        $storeServer = new SysStoreServer();
        foreach ($staffs as $k => $v) {
            $storeName = current($storeServer->getStoreName([$v['sys_store_id']]));
            $staffs[$k]['store_name'] = $v['sys_store_id'] != '-1' ? ($storeName ?: '') : '';
        }
        return $staffs;
    }

    /**
     * 更换审批人导致审批无法进行下去
     * 自动驳回
     * 需求： 鲁娇 https://l8bx01gcjr.feishu.cn/docs/doccndvJjlVJyvbzOtWMRbi4xje
     * @param array $paramIn
     * @return false
     * @throws Exception
     */
    public function ResignAutoDismiss($paramIn = [])
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_info_id');
        //$staffId 的上级由 $beforeManager 变成了 $afterManager
        //是否存在 $staffId 申请的审批中的离职申请
        $approvalInfo = $this->auditlist->getResignDetailByStaffId($staffId);
        if (empty($approvalInfo)) {
            return false;
        }

        //如果一级审批人已经审批同意了
        if (isset($approvalInfo['status']) && $approvalInfo['status'] == enums::$audit_status['approved_approval']) {

            //auto dismiss
            $this->getDI()->get('logger')->write_log('resign:ResignAutoDismiss:' . json_encode($approvalInfo), 'info');
            (new ResignServer($this->lang, $this->timezone))->adminReject($approvalInfo['audit_id']);
        }
    }

    /**
     * 获取历史某天列表数据
     * @param $date
     * @return array
     */
    public function get_staff_audit_union_list($date)
    {
        try{
            $sql = "
                    SELECT *
                    FROM staff_audit_union 
                    WHERE created_at >= '{$date} 00:00:00'
                        and created_at <= '{$date} 23:59:59'
                    ";
            $res = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("by数据归档任务，日期：{$date}，staff_audit_union error:" . $e->getMessage(), "error");
            return [];
        }
        return $res;
    }

    /**
     * 获取parcle_detail/sub/time归档历史表名
     *
     * @param $stat_date
     * @param $table_name
     */
    public function get_table_name($stat_date, $table_name)
    {
        $table_date = date("Y_m", strtotime($stat_date));
        $new_table  = "{$table_name}_{$table_date}";
        $db         = $this->getDI()->get('db');
        $logger     = $this->getDI()->get('logger');

        try {
            $insert  = "show tables like '{$new_table}'";
            $info = $db->query($insert)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (empty($info)) {
                $create_table = "create table {$new_table} like {$table_name};";
                $db->execute($create_table);
                echo "by库数据归档，日期：" . $stat_date . "，{$new_table} 建表成功".PHP_EOL;
                $logger->write_log("by库数据归档，日期：" . $stat_date . "，{$new_table} 建表成功", 'info');
            }else{
                echo "by库数据归档，日期：" . $stat_date . "，{$new_table} 表已存在".PHP_EOL;
                $logger->write_log("by库数据归档，日期：" . $stat_date . "，{$new_table} 表已存在", 'info');
            }
        } catch (\Exception $e) {
            $logger->write_log("by库数据归档，日期：" . $stat_date . "，{$new_table} 建表失败:" . $e->getMessage());
            return '0';
        }
        return $new_table;
    }

    /**
     * 写入归档表历史数据，验证批量插入数据是否已经归档，数据修复时使用
     *
     * @param $stat_date
     * @param $data_list
     * @param $data_num
     * @param $table
     * @param $type
     * @return bool
     * @throws \Exception
     */
    public function insert_table_valid($stat_date, $data_list, $data_num, $table, $type)
    {
        $logger = $this->getDI()->get('logger');
        try{
            $data_cnt = count($data_list);
            $log_msg  = "by库 归档表 {$table}，插入数据，";
            if (!empty($data_list) && $data_cnt > 0) {
                if($type == 1){
                    $model = new AuditlistRepository($this->lang, $this->timeZone);

                    //修复归档数据，判断部分数据是否已经归档，剔除已经归档过的数据
                    $data_list_new = [];
                    foreach ($data_list as $k => $v){
                        $sql = "select id from ".$table." where id= {$v['id']}";
                        $info = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
                        //根据pno有无，判断是否已经归档，如果归档了就不再批量写入目标库
                        if (empty($info)) {
                            $data_list_new[] = $v;
                        }
                    }
                    $data_list = $data_list_new;
                } else{
                    return false;
                }

                //数据为空，直接返回
                if(count($data_list) == 0){
                    $logger->write_log($log_msg."已经归档过，本次需要导入0条记录！", 'info');
                    return true;
                }

                $result_insert = $model->batch_insert($table, $data_list);//执行批量导入
                if ($result_insert) {
                    $logger->write_log($log_msg.'第'.$data_num."次成功导入" . $data_cnt . '条记录', 'info');
                    return true;
                } else {
                    $logger->write_log($log_msg.'第'.$data_num."次需要导入" . $data_cnt . '条记录，导入失败！', 'error');
                }
            } else {
                $logger->write_log($log_msg."共导入0条记录！", 'info');
                return true;
            }
        } catch (Exception $e) {
            $logger->write_log("by数据归档任务，日期：{$stat_date}，insert_table type: {$type} error:" . $e->getMessage(), "error");
        }
        return false;
    }

    /**
     * 根据运单号删除parcel_sub数据
     *
     * @param $stat_date
     * @param $data_list
     */
    public function del_audit_detail($stat_date, $data_list)
    {
        $logger = $this->getDI()->get('logger');
        try{
            $TargetTable = "staff_audit_union";
            $id_arr = array_column($data_list,'id');
            $id_str = "'" . implode("','", $id_arr) . "'";

            $del_sql = "delete from {$TargetTable} where id in ({$id_str}) ";
            $flag = $this->db->execute($del_sql);
            $rows = $this->db->affectedRows();
            if ($flag) {
                $logger->write_log("by库数据归档任务，日期：{$stat_date}，清理表 {$TargetTable}，清理{$rows}条记录成功", 'info');
            } else {
                $logger->write_log("by库数据归档任务，日期：{$stat_date}，清理表 {$TargetTable}，清除数据无记录", 'info');
            }
        } catch (Exception $e) {
            $logger->write_log("by库数据归档任务，日期：{$stat_date}，del_audit_detail error:" . $e->getMessage(), "error");
        }
    }

    /**
     * 抄送列表
     * @return array
     */
    public function getCCList($paramIn = [])
    {
        //[1]输入参数验证
        $auditReadType  = $this->processingDefault($paramIn, 'audit_read_type', 2, 1); //显示类型  1-未读 2-已读
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $pageNum        = $this->processingDefault($paramIn, 'page_num', 2, 1);
        $pageSize       = $this->processingDefault($paramIn, 'page_size', 2, enums::DEFAULT_PAGE_SIZE);
        $search         = $this->processingDefault($paramIn, 'search_conditions');
        $auditTypes     = $this->processingDefault($paramIn, 'filter_types');
        $order          = $this->processingDefault($paramIn, 'sort_order');

        //[1.2]初始化
        $returnData     = ['data' => ['dataList' => []]];
        $conditions     = [];

        //[1.3]增加审批类别和员工名字搜索
        if ($auditTypes) {
            $conditions['audit_type'] = $auditTypes;
        }
        if ($search) {
            $staffsArr = self::getLikeStaffs($search, true);
            if ($staffsArr) {
                $staffIds = array_column($staffsArr, 'staff_info_id');
                $conditions['submitter_id'] = $staffIds;
            }
        }
        if ($auditReadType) {
            $conditions['audit_read_type'] = $auditReadType;
        }
        //查询抄送列表
        $repo = new AuditlistRepository($this->lang, $this->timezone);
        $colums = 'audit_cc.id,audit_cc.biz_type,audit_cc.biz_value,audit_cc.flow_node_id,audit_cc.submitter_id,audit_cc.cc_staff_id,audit_cc.summary,audit_cc.is_read,audit_cc.deleted,audit_cc.created_at,audit_cc.update_at,audit_apply.state,audit_apply.is_cancel';
        $list = $repo->getCCList($staffId, $conditions, $colums, $order, $pageNum, $pageSize);
        if(isCountry(['TH','PH'])){
            $staffInfo = (new StaffRepository())->checkoutStaffById($staffId);
            $this->is_individual_contractor = $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID;
        }
        if (isset($list) && count($list) !== 0) {
            $returnData = $this->generateList($list, 1, 2);
        }

        return $this->checkReturn(['data' => ['dataList' => $returnData]]);
    }

    /**
     * 设置已读
     * @param $audit_id
     * @param $audit_type
     * @param $staff_id
     */
    public function setCCRead($audit_id, $audit_type, $staff_id)
    {
        //更新为已读
        $item = AuditCCModel::findFirst([
            'conditions' => "biz_value = :value: and biz_type = :type: and cc_staff_id = :staff_id: and is_read = 1",
            'bind' => [
                'type'      => $audit_type,
                'value'     => $audit_id,
                'staff_id'  => $staff_id
            ]
        ]);
        if($item) {
	        $item->update(['is_read' => 2]);
        }

        return $this->checkReturn([]);
    }

    /**
     * 抄送列表
     * @param array $paramIn
     * @return array
     * @throws \FlashExpress\bi\App\library\Exception\InnerException
     */
    public function getCCDetail($paramIn = [])
    {
        //[1]输入参数验证
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $auditId        = $this->processingDefault($paramIn, 'audit_id', 2);
        $auditType      = $this->processingDefault($paramIn, 'audit_type', 2);

        $server = new ApprovalServer($this->lang, $this->timezone);
        $detail = $server->getAuditDetail($auditId, $auditType, $staffId, 2, 1, null, 2);
        if (isset($detail['data']['head']['options'])) {
            unset($detail['data']['head']['options']);
        }

        //提交人头像
        $detail['data']['head']['avator'] = $this->re['auditlist']->getPersonAvator($detail['data']['head']['staff_id']);

        return $this->checkReturn($detail);
    }

    /**
     * 抄送数
     * @param $staffId
     * @return int
     */
    public function getCCCount($staffId)
    {
        //[2]获取列表数据
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['s' => AuditCCModel::class]);
        $builder->andWhere('s.cc_staff_id = :staff_id:', ['staff_id' => $staffId]);
        $builder->andWhere('s.is_read = :is_read:', ['is_read' => 1]);
        $totalCount = $builder->columns('COUNT(1) AS total')->getQuery()->execute()->getFirst();
        return intval($totalCount->total);
    }

    /**
     * 获取待审批人信息
     * 当前时间90天内的，超过30分钟的待审批
     */
    public function getPendingInfo($auditType = [], $staffIds = [])
    {
        if (empty($auditType)) {
            $auditType = AuditListEnums::getPendingMessageTypeList();
        }

        $groupedResults = [];
        foreach ($auditType as $typeItem) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('approval_id');
            $builder->from(['f' => AuditApprovalModel::class]);
            if (!empty($staffIds)) {
                $builder->inWhere('approval_id', $staffIds);
            }
            $builder->andWhere('biz_type = :biz_type:', ['biz_type' => $typeItem]);
            $builder->andWhere('state = :state:', ['state' => enums::APPROVAL_STATUS_PENDING]);
            $builder->andWhere('created_at > :start_time:', ['start_time' => gmdate("Y-m-d H:i:s", time() - 86400 * 90)]);
            $builder->andWhere('created_at <= :time:', ['time' => date("Y-m-d H:i:s", strtotime(gmdate("Y-m-d H:i:s", time())) - 1800)]);
            $data = $builder->getQuery()->execute()->toArray();
            if (empty($data)) {
                continue;
            }

            foreach ($data as $row) {
                $approvalId = $row['approval_id'];

                // 检查是否已经为这个 approval_id 创建了一个分组
                if (!isset($groupedResults[$approvalId])) {
                    // 如果没有，初始化该分组
                    $groupedResults[$approvalId] = 0;
                }

                // 增加当前 approval_id 的计数
                $groupedResults[$approvalId]++;
            }
        }
        $result = [];
        foreach ($groupedResults as $approval_id => $count) {
            $result[] = [
                'approval_id' => $approval_id,
                'cou'         => $count,
            ];
        }
        return $result;
    }

    /**
     * 获取存在待审批的并且审批时间跟当前时间不在同一天的审批人
     * @param array $auditType 指定的审批类型
     * @return array
     */
    public function getPendingApprovalsOfDateNotEqCreateDate(array $auditType, array $staffIds = []): array
    {
        $staffArr = [];

        //获取待审批数据
        $pendingData = (new AuditlistRepository($this->lang, $this->timezone))->getSpecTypesPendingData($auditType, $staffIds);
        if (empty($pendingData)) {
            return [];
        }

        foreach ($pendingData as $item) {

            //获取审批创建日期 与 当前时间日期
            $createDate   = date("Y-m-d", strtotime($item['origin_time']));
            $approvalDate = gmdate("Y-m-d", time());

            //审批创建时间 与 当前时间比较
            if ($createDate != $approvalDate) {
                $staffArr[] = $item['approval_id'];
            }
        }

        return !empty($staffArr) ? array_values(array_unique($staffArr)) : [];
    }

    /**
     * 获取历史审批列表
     * @param array $params
     * @return array
     */
    public function getStaffAuditList($params = []): array
    {
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $list = StaffAuditModel::find([
            'columns' => 'leave_day,leave_start_time,leave_start_type,leave_type',
            'conditions' => 'staff_info_id = :id: and audit_type = :type: and status = 2 and created_at >= :time: and parent_id = 0',
            'bind' => ['id' => $params['staff_info_id'],
                'type' => $params['audit_type'],
                'time' => isset($params['time']) ? date('Y-m-d H:i:s', strtotime($params['time']) - $add_hour * 3600) : date('Y-m-d H:i:s', strtotime('-2 month'))
            ]
        ])->toArray();

        $list = array_map(function ($item) use ($add_hour) {

            $item['leave_type_txt'] = '';
            if (isset(AuditRepository::$leave_type[$item['leave_type']])) {
                $item['leave_type_txt'] = $this->getTranslation()->_(AuditRepository::$leave_type[$item['leave_type']]);
            }
            $item['leave_start_time'] = date('Y-m-d', strtotime($item['leave_start_time']));
            if ($item['leave_start_type'] == 2) {
                $item['leave_start_time'] .= ' ' . $this->getTranslation()->_('afternoon');
            } else {
                $item['leave_start_time'] .= ' ' . $this->getTranslation()->_('morning');
            }
            unset($item['leave_start_type']);
            return $item;
        }, $list);
        $returnData['data']['dataList'] = $list;
        return $this->checkReturn($returnData);
    }

    /**
     * 加工列表
     * @param $list
     * @param $auditShowType
     * @param $auditStateType
     * @return array
     */
    public function generateList($list, $auditShowType, $auditStateType): array
    {
        //初始化
        $auditRepo = new AuditlistRepository($this->lang, $this->timezone);

        if (empty($list)) { //审批人即没有下级又没有HC审批权限
            return [];
        }
        $auditRepo->setIsIndividualContractor($this->is_individual_contractor);
        //获取员工ID
        $staffIdsArr = array_column($list, 'submitter_id');
        $info        = (new StaffRepository())->checkoutStaffBatch(array_values(array_unique($staffIdsArr)));
        $typeData    = array_column($info, 'hire_type', 'staff_info_id');
        $info        = array_column($info, 'name', 'staff_info_id');
        foreach ($list as $k => $v) {
            $newList[$k]['name'] = $info[$v['submitter_id']] ?? '';
        }

        //存在超时的审批
        $canOvertimeAuditTypeList = SettingEnvServer::getSetValToArray('workflow_overtime_types', ',');
        $lntCompany = (new SettingEnvServer())->getSetVal('lnt_company_ids', ',');
        $approvalServer = new ApprovalServer($this->lang, $this->timezone);

	    $add_hour = $this->config->application->add_hour;
        foreach ($list as $k => $v) {
            if ($v['biz_type'] == 0) { //无效状态直接过滤掉
                continue;
            }
            //是否个人代理
            $isUnpaid = !empty($typeData[$v['submitter_id']]) && in_array($typeData[$v['submitter_id']],HrStaffInfoModel::$agentTypeTogether);
            //针对审批申请状态做转换 不用转了 apply 状态不变
//            $state = $auditShowType == 1 ? $this->transformState(intval($v['state']),$v['is_cancel']):intval($v['state']);
            $state = intval($v['state']);
            //转义数据
            $newList[$k]['audit_id']             = intval($v['biz_value']);
            $newList[$k]['audit_type']           = intval($v['biz_type']);
            $newList[$k]['audit_type_title']     = $auditRepo->getAudityType($v['biz_type']);
            $newList[$k]['audit_type_sub_title'] = $this->getSubTitle($auditShowType, $v);
            $newList[$k]['standard']             = $this->getStandardByType($v['biz_type']);

            //马来 补卡 标题要换
            if(isCountry('MY') && $isUnpaid && $v['biz_type'] == StaffAuditTypeEnums::AUDIT_TYPE_AT){
                $newList[$k]['audit_type_title'] = $this->getTranslation()->_('home_make_up_unpaid');
            }
            $newList[$k]['state']            = $state;
            $newList[$k]['state_title']      = $auditRepo->getAuditState($state);
            if($isUnpaid){
                $newList[$k]['state_title']  = $auditRepo->getAuditStateUnpaid($state);
            }
            if ($this->getIsLnt($v, $lntCompany, $approvalServer)) {
                $newList[$k]['state_title']  = $this->getAuditStateLnt($state);
            }
            $newList[$k]['submitter_id']     = $v['submitter_id'];
            $newList[$k]['created_at']       = date('Y-m-d H:i:s',strtotime("{$v['created_at']}") +( $add_hour * 3600));
            $newList[$k]['audit_time']       = !empty($v['audit_time']) ? date('Y-m-d H:i:s',strtotime("{$v['audit_time']}") +( $add_hour * 3600)) : '';
            $newList[$k]['is_red']           = '';
            $newList[$k]['is_cancel']        = $this->showIsCancel($auditShowType, $v);
            //获取概要信息
            $summary = $v['summary'] ?? '';
            $summary = json_decode(addcslashes(str_replace('\\"',"'",$summary), '\\'),true);
            $new_summary = empty($summary) ? '' : array_column($summary,'value','key');

            //计算剩余时间
            if ($auditStateType == 1) {//我的审批列表 需要标红为审批 并且2天后失效的记录 和我的待审批列表
                //1-补卡 2-请假 3-LH 4-OT 展示 失效是5天 当前时间 - 创建时间 小于3天 剩2天过期 后更改为2天 并且 创建后就显示 倒计时 到失效当天的23。59
                //请不要改动超时天数strtotime("{$real_create} + 2 day") 如有调整 th-恢复在职申请同步改动 genSummary中的 $time_out
                $valid = $this->getInvalid();
                if (in_array($v['biz_type'], array_keys($valid))) {
                    if ($valid[$v['biz_type']] == 'all' || $valid[$v['biz_type']] == $v['is_cancel']){
                        $real_create = $v['created_at'];//已经转成泰国时间
                        if(!empty($new_summary) && !empty($new_summary['created_at'])){
                            $real_create = $new_summary['created_at'];

                            $invalid_time = strtotime(date('Y-m-d 23:59:59',strtotime("{$real_create} + 2 day") + $add_hour * 3600));//超时那天的 0点
                        }else{
                            $invalid_time = strtotime(date('Y-m-d 23:59:59',strtotime("{$real_create} + 2 day")));//超时那天的 0点
                        }
                        //马来 补卡超时时间有特殊逻辑 https://flashexpress.feishu.cn/wiki/D8e4wBZGli5OoKkxtBrcdo46n0d?create_from=copy_within_wiki&renamingWikiNode=true
                        if(!empty($v['time_out'])){
                            $invalid_time = strtotime($v['time_out']);
                        }

                        $left_time = $invalid_time - time();
                        if ($left_time < 0) {
                            $hour = 0;
                        } else {
                            $hour = round($left_time / 3600, 1);
                        }
                        $show_msg               = $this->getTranslation()->_('invalid_for_apply');
                        $show_msg               = str_replace('{hour}', $hour, $show_msg);
                        if (in_array($v['biz_type'], $canOvertimeAuditTypeList) && empty($v['time_out'])) {
                            $newList[$k]['is_red'] = '';
                        } else {
                            $newList[$k]['is_red'] = $show_msg;
                        }
                    }
                }
            }

            //列表页展示简要 排除掉 计算倒计时的 created_at
            if(in_array($v['biz_type'],  array_keys($this->getInvalid())) && !empty($summary)){
                foreach ($summary as $m => $n) {
                    if ($n['key'] == 'created_at') {
                        unset($summary[$m]);
                    }
                    //个人代理请假审批 要改名字
                    if ($n['key'] == 'audit_type_title_key'){
                        $newList[$k]['audit_type_title'] = $this->getTranslation()->_($n['value']);
                        unset($summary[$m]);
                    }
                }
            }
            if (!empty($summary)) {
                $summary = json_encode($summary);
                $summary = $this->getSummary($summary, $isUnpaid);
            }
            $newList[$k]['summary'] = $summary;
        }
        return $newList;
    }

    private function showIsCancel($auditShowType,$v)
    {
        if ($auditShowType == 1){
            $isCancel = ($v['state'] == 1 && $v['is_cancel'] > 0) ? '1':'0';
        }elseif ($auditShowType == 2){
            $isCancel =  $v['is_cancel'] > 0 ? '1':'0';
        }else{
            $isCancel = '0';
        }
        return $isCancel;
    }

    /**
     * //针对 撤销审批 和修改审批 列表新增 tip 文案
     * @param $auditShowType 1-我的申请 2- 我的审批
     * @param $v
     * @return mixed
     */
    private function getSubTitle($auditShowType, $v){
        $enum = AuditListEnums::$subTitleTips;
        $key = '';
        //我的申请 审批完成 不展示
        if($auditShowType == 1 && $v['state'] != enums::$audit_status['panding']){
            return '';
        }
        if($v['is_cancel'] > 0){
            $key = $enum[enums::WF_ACTION_CREATE_CANCEL];
        }
        if($v['is_edit'] == 1){
            $key = $enum[enums::WF_ACTION_EDIT_RECREATE];
        }
        return $this->getTranslation()->_($key);
    }

    /**
     * @return array
     */
    protected function getInvalid(): array
    {
        if(!isCountry()) {//只有th 举报，展示 倒计时。
            unset($this->invalid[AuditListEnums::APPROVAL_TYPE_REPORT]);
        }
        return $this->invalid;
    }

    /**
     * @param array $invalid
     */
    protected function setInvalid(array $invalid): void
    {
        $this->invalid = $invalid;
    }

    /**
     * by汇总审批待发短信
     * @param array $params
     * @return array
     */
    public function gatherApprovalSmsToBeSent($params = []): array
    {
        $everyCountryPendingCount = [];
        if (strtolower(env('country_code', 'Th')) == 'th') {
            // 获取白名单审批待发数据
            $everyCountryPendingCount = self::send_audit_message_in_white_data();
        }

        // BY定时短信提醒功能【非白名单人员】
        $messageParamer = self::send_audit_message_data();

        $return         = array_merge($everyCountryPendingCount, $messageParamer);
        $this->logger->write_log("gatherApprovalSmsToBeSent return :".json_encode($return, JSON_UNESCAPED_UNICODE), 'info');
        return $return;
    }

    /**
     * 获取【非白名单】员工在系统的审批数据
     * @return array
     */
    public function send_audit_message_data()
    {
        $return = [];
        $server = new AuditListServer($this->lang, $this->timezone);
        //[1]获取需要特殊判断的待审批数据
        //如果不存在补卡、出差、出差打卡、加班、请假类型之外的待审批,
        //需判断当前日期是否和申请日期在同一天，若是则不触发待审批短息提醒，若否则按照原规则(整点)触发短信审批提醒
        $allType     = AuditListEnums::getPendingMessageTypeList();
        $specialType = [
            AuditListEnums:: APPROVAL_TYPE_AT,
            AuditListEnums:: APPROVAL_TYPE_LE,
            AuditListEnums:: APPROVAL_TYPE_OVERTIME,
            AuditListEnums:: APPROVAL_TYPE_BT,
            AuditListEnums:: APPROVAL_TYPE_ATT_BUS,
        ];
        //用人申请(WFM)、补卡、出差、出差打卡、加班、请假之外的审批类型
        $partType      = array_values(array_diff($allType, $specialType));
        $partData      = $server->getPendingInfo($partType, []);
        $allowStaffIds = array_column($partData, 'approval_id');
        //补卡、出差、出差打卡、加班、请假
        $specTypesStaffIds = $server->getPendingApprovalsOfDateNotEqCreateDate($specialType, []);
        //[2]获取全部待审批数据
        $data = $server->getPendingInfo('', []);
        if (empty($data)) {
            return $return;
        }
        $data = array_column($data, 'cou', 'approval_id');
        //获取HC WMF 指定工号
        $server         = new SettingEnvServer();
        $wmfStaffIds    = $server->getSetVal('wmf_staff_ids');
        $wmfStaffIdsArr = !empty($wmfStaffIds) ? explode(',', $wmfStaffIds) : [];
        //先获取白名单数据
        $whiteList    = SmsPendingWhitelistModel::find([
            'conditions' => 'is_del = 0',
        ])->toArray();
        $whiteListArr = !empty($whiteList) ? array_column($whiteList, 'staff_id') : [];
        //排掉排名单的数据
        $dataStaffInfoArr = array_keys($data);
        $dataDiff         = array_values(array_diff($dataStaffInfoArr, $whiteListArr));
        if (empty($dataDiff)) {
            return $return;
        }
        //获取手机号
        $staffInfo = (new HrStaffInfoModel())->getStaffInfo($dataDiff);
        $staffInfoArr = array_column($staffInfo, 'mobile', 'staff_info_id');
        //获取国家Code == 99不管
        foreach ($dataDiff as $staffId) {
            //获取手机号
            $mobile = isset($staffInfoArr[$staffId]) && $staffInfoArr[$staffId] ? $staffInfoArr[$staffId] : "";
            if (empty($mobile) || $mobile == '000000000') {
                continue;
            }

            //发送待审批提醒短信条件
            //条件一: 存在 用人申请(WFM)、补卡、出差、出差打卡、加班、请假 以外的待审批
            //条件二: 如果不满足条件一，并且满足
            //  1. 存在补卡、出差、出差打卡、加班、请假类型的待审批
            //  2. 存在审批申请日期与当前日期 (都是0时区) 不在同一天的待审批数据
            //条件三: WMF指定工号不发送短息提醒
            //
            //条件一、二，满足其一可以发送短信
            if (isset($data[$staffId]) && !empty($data[$staffId]) && !in_array($staffId, $wmfStaffIdsArr)) {
                if (in_array($staffId, $allowStaffIds)) { //条件一
                    $pendingCount = $data[$staffId];
                } else {
                    if (in_array($staffId, $specTypesStaffIds)) { //条件二
                        $pendingCount = $data[$staffId];
                    } else {
                        continue;
                    }
                }
            } else {
                continue;
            }

            //待审批数量
            $pending_data = sprintf("%s:%s", strtoupper(env("country_code", "TH")), $pendingCount);
            $return[] = [
                "mobile" => $mobile,
                "staff_info_id" => $staffId,
                "msg"    => $pending_data,
                "nation" => strtoupper(env('country_code')),
            ];
        }
        $this->logger->write_log("send_audit_message_data return :".json_encode($return, JSON_UNESCAPED_UNICODE), 'info');
        return $return;
    }

    /**
     * 获取【白名单】员工在系统的审批数据
     * @return array
     */
    public function send_audit_message_in_white_data()
    {
        //国家code
        $countryCode = ['TH', 'PH', 'MY', 'LA', 'VN', 'ID'];

        $everyCountryPendingCount = [];

        //先获取白名单数据

        $whiteList = SmsPendingWhitelistModel::find([
            'conditions' => 'is_del = 0',
        ])->toArray();
        if (empty($whiteList)) {
            $this->logger->write_log("send_audit_message_in_white_data whiteList is null ", 'error');
            return [];
        }
        $whiteListStaffInfoArr = array_column($whiteList, 'staff_id');
        //获取各个国家的数据

        foreach ($countryCode as $country) {
            //获取各个国家pending count
            $count = $this->getCountryPendingCount($country);
            if (empty($count)) {
                continue;
            }
            $arr = array_map(function ($v) use ($whiteListStaffInfoArr) {
                return in_array($v['approval_id'], $whiteListStaffInfoArr) ? $v : [];
            }, $count);

            $everyCountryPendingCount[$country] = array_filter($arr);
        }
        //转换为已人为维度
        $approvalPendingCount = [];
        foreach ($everyCountryPendingCount as $country => $v) {
            foreach ($v as $item) { //拆分单个国家的数据
                //组织数据
                $approvalId   = $item['approval_id'] ?? 0;
                $pendingCount = $item['cou'] ?? 0;
                if ($pendingCount == 0) {
                    continue;
                }
                if (isset($approvalPendingCount[$approvalId])) {
                    $approvalPendingCount[$approvalId] = sprintf("%s %s:%s", $approvalPendingCount[$approvalId],
                        $country, $pendingCount);
                } else {
                    $approvalPendingCount[$approvalId] = sprintf("%s:%s", $country, $pendingCount);
                }
            }
        }

        $staff_info = (new HrStaffInfoModel())->getStaffInfo(array_keys($approvalPendingCount));
        $staff_info = array_column($staff_info, 'name', 'staff_info_id');

        $mobileList = array_column($whiteList, 'mobile', 'staff_id');
        $nationList = array_column($whiteList, 'sms_nation', 'staff_id');
        $return     = [];
        foreach ($approvalPendingCount as $approval_id => $pending_data) {
            //获取手机号
            $mobile = isset($mobileList[$approval_id]) && $mobileList[$approval_id] ? $mobileList[$approval_id] : "";
            if (empty($mobile)) {
                continue;
            }

            //是否在职
            if (!isset($staff_info[$approval_id])) { //员工不在职
                continue;
            }

            if ($nationList[$approval_id] == 'CN') {
                $return[] = [
                    "mobile"        => $mobile,
                    "staff_info_id" => $approval_id,
                    "msg"           => $pending_data,
                    "nation"        => "CN",
                ];
            } else {
                $return[] = [
                    "mobile"        => $mobile,
                    "staff_info_id" => $approval_id,
                    "msg"           => $pending_data,
                    "nation"        => $nationList[$approval_id] ?? "TH",
                ];
            }
        }
        $this->logger->write_log("send_audit_message_in_white_data return :".json_encode($return, JSON_UNESCAPED_UNICODE), 'info');
        return $return;
    }

    /**
     * 获取各个国家获取待审批数
     * @param $country_code
     * @return array
     */
    public function getCountryPendingCount($country_code): array
    {
        if (RUNTIME == 'pro') {
            $host = [
                'TH' => 'https://backyard-api.flashexpress.com/api/_/',
                'PH' => 'https://backyard-api.flashexpress.ph/api/_/',
                'MY' => 'https://backyard-api.flashexpress.my/api/_/',
                'LA' => 'https://backyard-api.flashexpress.la/api/_/',
                'VN' => 'https://hby-api.flashexpress.vn/',
                'ID' => 'https://hby-api.flashexpress.id/',
            ];
        } else {
            if (RUNTIME == 'tra') {
                $host = [
                    'TH' => 'https://backyard-api-tra.flashexpress.com/api/_/',
                    'PH' => 'https://backyard-api-tra.flashexpress.ph/api/_/',
                    'MY' => 'https://backyard-api-tra.flashexpress.my/api/_/',
                    'LA' => 'https://backyard-api-tra.flashexpress.la/api/_/',
                    'VN' => 'https://hby-api-tar.flashexpress.vn/',
                    'ID' => 'https://hby-api-tra.flashexpress.id/',
                ];
            } else {
                $host = [
                    'TH' => 'https://dev01-th-byapi.fex.pub/',
                    'PH' => 'https://dev01-ph-byapi.fex.pub/',
                    'MY' => 'https://dev01-my-byapi.fex.pub/',
                    'LA' => 'https://dev01-la-byapi.fex.pub/',
                    'VN' => 'https://dev01-vn-byapi.fex.pub/',
                    'ID' => 'https://dev01-id-byapi.fex.pub/',
                ];
            }
        }

        if (isset($host[$country_code]) && $host[$country_code]) {
            //获取token
            $token = $this->getToken($host[$country_code]);
            if (empty($token)){
                $this->logger->write_log("getCountryPendingCount token error", 'error');
            }
            $path    = 'api/message/getPendingCount';
            $headers = ['Authorization' => 'Bearer '.$token];

            $client = new RestClient($host[$country_code]);
            $res    = $client->execute(RestClient::METHOD_POST, $path, [], $headers);
            $this->logger->write_log(sprintf("getCountryPendingCount[%s] return : %s", $country_code,
                json_encode($res)), 'info');
        }

        return $res['data'] ?? [];
    }

    /**
     * 获得backyard token
     * @return mixed|string
     */
    public function getToken($path = '')
    {
        $key   = 'by_auth_token_pending_count_'.strtolower(env('country_code'));
        $params = [
            'client_id'     => 'xSRmr33a7X7',
            'client_secret' => '37ugTtQUtAQJT3zUi',
            'grant_type'    => 'client_credentials',
        ];
        $client = new RestClient($path);
        $res    = $client->execute(RestClient::METHOD_POST, 'api/auth/token', $params);
        if ($res) {
            $token  = $res['access_token'];
            $expire = $res['expires_in'];
        }
        return $token ?? '';
    }

    /**
     * 更新审批概要
     * @param $audit_type
     * @param $audit_value
     * @param $update_summary_list
     * @return void
     */
    public function updateAuditListSummary($audit_type, $audit_value, $update_summary_list = [])
    {
        if (empty($audit_type) || empty($audit_value) || empty($update_summary_list)) {
            return;
        }
        $applyRepository = new ApplyRepository();
        $this->db->begin();
        $auditApply = $applyRepository->getApplyObject($audit_type, $audit_value, true);
        $summaryInfo = !empty($auditApply) ? $auditApply->getSummary() : null;
        if (empty($summaryInfo)) {
            return;
        }
        $beforeSummaryArray = $summaryArray = json_decode($summaryInfo, true);
        //替换字段名，然后下边按照最新的字段值取更新
        if(!empty($update_summary_list['replace_list'])) {
            foreach ($update_summary_list['replace_list'] as $rule) {
                $after_columns = $rule['after_columns'];
                $before_column = $rule['before_column'];

                // 查找需要替换的字段在原始数据中的位置
                foreach ($summaryArray as $key => $item) {
                    if ($item['key'] == $before_column) {
                        // 进行替换
                        $summaryArray[$key]['key'] = $after_columns;
                    }
                }
            }
            $this->logger->write_log(['updateAuditListSummary' => ['update_summary_list' => $update_summary_list, 'beforeSummaryArray' => $beforeSummaryArray, 'afterSummaryArray' => $summaryArray]] , 'info');

            unset($update_summary_list['replace_list']);
        }

        //更新字段值。
        foreach ($update_summary_list as $k => $v) {
            foreach ($summaryArray as $key => $item) {
                if ($item['key'] == $k) {
                    $summaryArray[$key]['value'] = $v;
                }
            }
        }
        $this->logger->write_log('updateAuditListSummary' . json_encode($summaryArray), 'info');
        $newSummaryInfo = json_encode($summaryArray, JSON_UNESCAPED_UNICODE);
        $auditApply->setSummary($newSummaryInfo);
        $auditApply->save();

        $auditApproval = $applyRepository->getApprovalObject($audit_type, $audit_value, true);
        $auditApproval->update(['summary' => $newSummaryInfo]);

        $this->db->commit();
        $this->logger->write_log(sprintf('updateAuditListSummary done, %d, %d, %s', $audit_type, $audit_value, json_encode($update_summary_list)) , 'info');

        return;
    }

    /**
     * 给待审批 审批人发送 push
     * @param $auditType
     * @return bool
     */
    public function sendPushToApprove($auditType)
    {
        if (!in_array($auditType, self::$sendPushAuditList)) {
            echo '请输入有效审批类型：' . implode(',', self::$sendPushAuditList);
            return false;
        }

        $applyWhere['biz_type'] = $auditType;
        $applyWhere['state']    = enums::$audit_status['panding'];
        $applyListInfo          = AuditApplyRepository::getAuditApplyList($applyWhere, ['*'], true);

        if (empty($applyListInfo)) {
            echo '暂无待审批数据:no apply';
            return false;
        }

        $bizValues                 = array_values(array_unique(array_column($applyListInfo, 'biz_value')));
        $approveWhere['biz_type']  = $auditType;
        $approveWhere['biz_value'] = $bizValues;
        $approveWhere['state']     = enums::$audit_status['panding'];
        $approveListInfo           = AuditApprovalRepository::getAuditApprovalList($approveWhere, ['*'], true);

        if (empty($approveListInfo)) {
            echo '暂无待审批数据:no approve';
            return false;
        }
        $approveIds = array_values(array_unique(array_column($approveListInfo, 'approval_id')));

        $pushTitleContent = $this->getPushTitleContent($auditType);
        $staffServer      = new StaffServer();
        foreach ($approveIds as $oneStaffId) {
            $t         = $this->getTranslation($staffServer->getLanguage($oneStaffId));
            $staffInfo = $staffServer->getStaffInfoById($oneStaffId);
            if ($staffInfo['state'] != enums::$service_status['incumbency']) {
                continue;
            }

            $pushData['staff_info_id']  = $oneStaffId;
            $pushData['title']          = $t->t($pushTitleContent['title']);
            $pushData['content']        = $t->t($pushTitleContent['content']);
            $pushData['message_scheme'] = $pushTitleContent['message_scheme'];

            $this->sendPush($pushData);
        }

        return true;
    }

    /**
     * 获取推送 标题 + 内容
     * @param $auditType
     * @return mixed
     */
    public function getPushTitleContent($auditType)
    {
        switch ($auditType) {
            case AuditListEnums::APPROVAL_TYPE_REINSTATEMENT:
                $approval_html_url = env('approval_html_url', 'flashbackyard://fe/html?url=');
                $approval_pending_url = env('approval_detail_url', 'http://192.168.0.222:90/#/Approval');
                $message_scheme = $approval_html_url . urlencode($approval_pending_url);

                $result['title']          = 'push_title_reinstatement';
                $result['content']        = 'push_content_reinstatement';
                $result['message_scheme'] = $message_scheme;
                break;
            default:
                $result['title']          = '';
                $result['content']        = '';
                $result['message_scheme'] = '';
                break;
        }

        return $result;
    }

    /**
     * 推送
     * @param $pushData
     */
    public function sendPush($pushData)
    {
        $data['staff_info_id']   = $pushData['staff_info_id'];
        $data['src']             = 'backyard';
        $data['message_title']   = $pushData['title'];
        $data['message_content'] = $pushData['content'];
        $data['message_scheme']  = $pushData['message_scheme'];

        $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
        $ret->setParams($data);
        $result = $ret->execute();
        $this->logger->write_log(['function' => 'auditListSendPush', 'params' => $data, 'result' => $result], 'info');
    }

    /**
     * 是否为 Lnt
     * @param $data
     * @param $lntCompany
     * @param $approvalServer
     * @return bool
     */
    protected function getIsLnt($data, $lntCompany, $approvalServer): bool
    {
        return false;
    }

    /**
     * 获取审批状态 text
     * @param int $state
     * @return string
     */
    protected function getAuditStateLnt(int $state): string
    {
        return '';
    }

    /**
     * @description 根据审批类型判断是否使用标准模板
     * @param $biz_type
     * @return int
     */
    private function getStandardByType($biz_type): int
    {
        if (in_array($biz_type, AuditListEnums::getAuditTypeOfStandard())) {
            return AuditListEnums::TEMP_STANDARD;
        }
        return AuditListEnums::TEMP_UNNORMALIZED;
    }
}
