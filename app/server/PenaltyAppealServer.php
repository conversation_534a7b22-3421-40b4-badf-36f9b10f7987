<?php


namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Models\backyard\PenaltyAppealModel;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\PublicRepository;

class PenaltyAppealServer extends AuditBaseServer
{
    public $timezone;

    public function __construct($lang = 'zh-CN', $timezone = 'Asia/Bangkok')
    {
        $this->timezone = $timezone;
        parent::__construct($lang, $timezone);
        $this->auditlistRep = new AuditlistRepository($lang, $timezone);
    }

    /**
     * 创建处罚申诉审批流
     * @param $params
     * @return array
     */
    public function create($params)
    {
        $db = PenaltyAppealModel::beginTransaction($this);
        try {
            $penaltyId = $this->processingDefault($params, 'penalty_id', 2);
            $staffId = $this->processingDefault($params, 'staff_id', 2);
            $storeId = $this->processingDefault($params, 'store_id', 1);
            $penaltyReason = $this->processingDefault($params, 'penalty_reason', 1);
            $happenDate = $this->processingDefault($params, 'happen_date', 1);
            $relatedInfo = $this->processingDefault($params, 'related_info', 1);
            $penaltyMoney = $params['penalty_money'] ?? 0.00;
            $applyRemark = addcslashes(stripslashes($params['apply_remark']), "'");
            $image = $this->processingDefault($params, 'image', 1);
            $extra_info = $this->processingDefault($params, 'extra_info', 1);
            $model = new PenaltyAppealModel();
            $model->staff_id = $staffId;
            $model->serial_no = 'PA' . $this->getID();
            $model->store_id = $storeId;
            $model->penalty_reason = $penaltyReason;
            $model->happen_date = $happenDate;
            $model->related_info = $relatedInfo;
            $model->penalty_money = $penaltyMoney;
            $model->apply_remark = $applyRemark;
            $model->image = $image;
            $model->penalty_id = $penaltyId;
            $model->status = enums::$audit_status['panding'];
            $model->created_at = gmdate('Y-m-d H:i:s');
            $model->updated_at = gmdate('Y-m-d H:i:s');
            $model->extra_info = $extra_info;
            if (!$model->save()) {
                $messages = $model->getMessages();
                foreach ($messages as $message) {
                    $message = '_' . $message;
                }
                throw new Exception($message);
            }
            $auditId = $model->id;
            $staffId = $model->staff_id;

            $requestId = (new ApprovalServer($this->lang, '+07:00'))->create($auditId, AuditListEnums::APPROVAL_TYPE_PA, $staffId,null,['store_id' =>$storeId]);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
            $db->commit();
            return $this->checkReturn(['data' => ['penalty_appeal_id' => $auditId]]);
        } catch (Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('penalty_appeal- create' . $e->getMessage());
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * QAQC 更新最后的处理结果
     * @param $params
     * @return array
     */
    public function update($params)
    {
        $db = PenaltyAppealModel::beginTransaction($this);
        try {
            $penaltyAppealId = $this->processingDefault($params, 'penalty_appeal_id', 2);
            $processResult = $this->processingDefault($params, 'process_result', 1);
            $processRemark = $this->processingDefault($params, 'process_remark', 1);
            $penaltyAfterMoney = $params['penalty_after_money'] ?? 0.00;
            $model = PenaltyAppealModel::findFirst('id=' . $penaltyAppealId);
            if (!$model) {
                throw new Exception('非法的penalty_appeal_id');
            }
            $model->process_result = $processResult;
            $model->process_remark = $processRemark;
            $model->penalty_after_money = $penaltyAfterMoney;
            if (!$model->save()) {
                $messages = $model->getMessages();
                foreach ($messages as $message) {
                    $message = '_' . $message;
                }
                throw new Exception($message);
            }
            $db->commit();
            return $this->checkReturn(['data' => ['penalty_appeal_id' => $penaltyAppealId]]);
        } catch (Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('penalty_appeal- update' . $e->getMessage());
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * bi/kit 设置审批流超时
     * @param $params
     * @return array
     */
    public function close($params)
    {
        if (!is_array($params['penalty_appeal_ids'])) {
            return $this->checkReturn(-3, 'penalty_appeal_ids 必须是数组');
        }
        $failIds = [];
        $approvalServer = new \FlashExpress\bi\App\Server\ApprovalServer($this->lang, $this->timezone);
        foreach ($params['penalty_appeal_ids'] as $v) {
            $result = $approvalServer->timeOut($v, enums::$audit_type['PA']);
            if (!$result) $failIds[] = $v;
        }
        return $this->checkReturn(['data' => ['fail_penalty_appeal_id' => $failIds]]);
    }

    /**
     * @inheritDoc
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $t = $this->getTranslation();
        // TODO: Implement getDetail() method.
        $model = PenaltyAppealModel::findFirst([
            'columns' => "
                            id,
                            staff_id,
                            serial_no,
                            store_id,
                            penalty_reason,
                            happen_date,
                            related_info,
                            penalty_money,
                            apply_remark,
                            image,
                            status,
                            reject_reason,
                            process_result,
                            process_remark,
                            penalty_after_money,
                            approval_notice,
                            convert_tz(created_at, '+00:00', '+07:00') as created_at,
                            convert_tz(updated_at, '+00:00', '+07:00') as updated_at,
                            extra_info
                            ",
            'conditions' => ' id = :id: ',
            'bind' => [
                'id' => $auditId
            ]
        ])->toArray();
        $storeInfo = (new SysStoreServer())->getStoreName([$model['store_id']]);
        $storeName = $storeInfo[$model['store_id']] ?? '';
        $staff_info = (new StaffServer())->get_staff($model['staff_id']);
        if ($staff_info['data']) {
            $staff_info = $staff_info['data'];
        }

        $image = $model['image'] ? json_decode($model['image'], true) : [];
        //处理额外字段,额外字段需要取定位置
        $extra = $model['extra_info'] ? json_decode($model['extra_info'], true) : [];

        $detail_level_1 = [
            ['key' => $t->_('apply_parson'), 'value' => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? ''),],
            ['key' => $t->_('re_field_apply_store_name'), 'value' => $storeName],
            ['key' => $t->_('penalty_reason'), 'value' => $t->_($model['penalty_reason']),],
        ];
        $detail_level_2 = [
            ['key' => $t->_('happen_date'), 'value' => $model['happen_date']],
            ['key' => $t->_('related_info'), 'value' => $model['related_info']],
        ];
        $detail_level_3 = [
            ['key' => $t->_('penalty_money'), 'value' => $model['penalty_money']],
        ];
        //特殊处理多级
        $center = [];


        //虚假标记
        if (!empty($extra['false_marking'])) {
            $value = $extra['false_marking'];
            //处罚证据
            if (!empty($value['img'])) {
                $detail_level_2 = [
                    ['key' => $t->_('happen_date'), 'value' => $model['happen_date']],
                    ['key' => $t->_('related_info'), 'value' => $model['related_info']],
                    ['key' => $t->_('evidence_of_punishment'), 'value' => $value['img'], 'type' => enums::APPROVAL_DETAIL_TYPE_PICTURE_AND_VIDEO],
                ];
            }
        }

        //照片不合格
        if (!empty($extra['img_unqualified'])) {
            $value = $extra['img_unqualified'];
            //具体原因
            $detail_level_1 = [
                ['key' => $t->_('apply_parson'), 'value' => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? ''),],
                ['key' => $t->_('re_field_apply_store_name'), 'value' => $storeName],
                ['key' => $t->_('penalty_reason'), 'value' => $t->_($model['penalty_reason']),],
                ['key' => $t->_('specific_reasons'), 'value' => $t->_('penalty_sub_category_' . $value['sub_category']),],
            ];
            //处罚证据
            if (!empty($value['img'])) {
                $detail_level_2 = [
                    ['key' => $t->_('happen_date'), 'value' => $model['happen_date']],
                    ['key' => $t->_('related_info'), 'value' => $model['related_info']],
                    ['key' => $t->_('evidence_of_punishment'), 'value' => $value['img'], 'type' => enums::APPROVAL_DETAIL_TYPE_PICTURE_AND_VIDEO],
                ];
            }
        }

        //EPOD拍照不合格
        if (!empty($extra['epod'])) {
            $value = $extra['epod'];
            //具体原因
            $detail_level_1 = [
                ['key'   => $t->_('apply_parson'), 'value' => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? ''),],
                ['key' => $t->_('re_field_apply_store_name'), 'value' => $storeName],
                ['key' => $t->_('penalty_reason'), 'value' => $t->_($model['penalty_reason']),],
                ['key' => $t->_('specific_reasons'), 'value' => $t->_('penalty_sub_category_' . $value['sub_category'])],
            ];
            //处罚证据
            if (!empty($value['img'])) {
                $detail_level_2 = [
                    ['key' => $t->_('happen_date'), 'value' => $model['happen_date']],
                    ['key' => $t->_('related_info'), 'value' => $model['related_info']],
                    ['type' => enums::APPROVAL_DETAIL_TYPE_PICTURE_AND_VIDEO, 'key' => $t->_('evidence_of_punishment'), 'value' => $value['img']],
                ];
            }
        }

        //个人代理-虚假打卡
        if ($model['penalty_reason'] == 'punish_category_msg93') {
            //处罚证据
            if (!empty($extra['web_show_img'])) {
                $detail_level_2 = [
                    ['key' => $t->_('happen_date'), 'value' => $model['happen_date']],
                    ['key' => $t->_('related_info'), 'value' => $model['related_info']],
                    [
                        'type'  => enums::APPROVAL_DETAIL_TYPE_PICTURE_AND_VIDEO,
                        'key'   => $t->_('evidence_of_punishment'),
                        'value' => $extra['web_show_img'],
                    ],
                ];
            }
        }

        //里程
        if (!empty($extra['mileage_detail'])) {
            $value  = $extra['mileage_detail'];
            //此时是多层额外处理
            $center[] = ['key' => $t->_('mileage_detail'), 'value' => ' '];
            if (isset($value['mileage_to_work'])) {
                $center[] = [
                    'key'   => ' ',
                    'value' => $t->_('mileage_to_work') . ' : ' . ceil($value['mileage_to_work'] / 1000) . "km",
                ];
            }
            if (isset($value['mileage_to_work_img'])) {
                $center[] = ['type' => 'picture', 'key' => ' ', 'value' => ["https:" . $value['mileage_to_work_img']]];
            }

            if (isset($value['mileage_leave_work'])) {
                $center[] = [
                    'key'   => ' ',
                    'value' => $t->_('mileage_leave_work') . ' : ' . ceil($value['mileage_leave_work'] / 1000) . 'km',
                ];
            }
            if (isset($value['mileage_leave_work_img'])) {
                $center[] = [
                    'type'  => 'picture',
                    'key'   => ' ',
                    'value' => ["https:" . $value['mileage_leave_work_img']],
                ];
            }
        }
        //称量
        if (!empty($extra['abnormal_balance'])) {
            $value       = $extra['abnormal_balance'];
            $before_size = ($value['before_length'] ?? '') . "X" . ($value['before_width'] ?? '') . "X" . ($value['before_height'] ?? '');//揽收尺寸 长 X 宽 X 高
            $after_size  = ($value['after_length'] ?? '') . "X" . ($value['after_width'] ?? '') . "X" . ($value['after_height'] ?? '');   //复秤时尺寸  X 宽 X 高
            //复秤图片 单独出来
            $center[] = ['key' => $t->_('weighing_details_after_img'), 'value' => $value['after_img'] ?? ''];
            $center[] = [
                'key'   => $t->_('weighing_details'),
                'value' => [
                    ['key'   => $t->_('weighing_details_before_weight'),
                     'value' => ($value['before_weight'] ?? '') . 'kg',
                     'type'  => 'before_weight',
                    ],
                    //揽收重量
                    [
                        'key' => $t->_('weighing_details_after_weight'),
                        'value' => ($value['after_weight'] ?? '') . 'kg',
                        'type' => 'after_weight',
                    ],
                    //复秤时重量
                    ['key' => $t->_('weighing_details_before_size'), 'value' => $before_size, 'type' => 'before_size'],
                    //揽收尺寸 长 X 宽 X 高
                    ['key' => $t->_('weighing_details_after_size'), 'value' => $after_size, 'type' => 'after_size'],
                    //复秤时尺寸  X 宽 X 高
                ],
                'type'  => 'abnormal_balance',
            ];
        }
        $end = [
            ['key' => $t->_('apply_remark'), 'value' => $model['apply_remark']],
            ['type' => 'picture', 'key' => $t->_('picture'), 'value' => $image],
        ];
        $returnData['data']['detail'] =  array_merge($detail_level_1,$detail_level_2 ,$detail_level_3, $center , $end);
        if ($comeFrom == 2) {
            $returnData['data']['detail'][] = [
                'key' => $t->_('approval_notice_title'), 'value' => $t->_('approval_notice_content')
            ];
        }
        if (!empty($model['process_result'])) {
            $returnData['data']['detail'][] = [
                'key' => $t->_('process_result'), 'value' => $t->_($model['process_result'])
            ];
        }
        if (!empty($model['process_remark'])) {
            $returnData['data']['detail'][] = [
                'key' => $t->_('process_remark'), 'value' => $model['process_remark']
            ];
        }
        if (!empty($model['penalty_after_money']) && $model['penalty_after_money'] > 0.00) {
            $returnData['data']['detail'][] = [
                'key' => $t->_('penalty_after_money'), 'value' => $model['penalty_after_money'] . '฿'
            ];
        }


        $data = [
            'title'       => $this->auditlistRep->getAudityType(enums::$audit_type['PA']),
            'origin_id'   => $model['id'],
            'id'          => $model['id'],
            'staff_id'    => $model['staff_id'],
            'type'        => enums::$audit_type['PA'],
            'created_at'  => $model['created_at'],
            'updated_at'  => $model['updated_at'],
            'status'      => $model['status'],
            'status_text' => $this->auditlistRep->getAuditStatus('10' . $model['status']),
            'serial_no'   => $model['serial_no'] ?? '',
        ];

        $returnData['data']['head'] = $data;
        return $returnData;

    }

    /**
     * 更新审批状态
     * @param $auditId
     * @param $staffId
     * @param $status
     * @param $cancelreason
     * @return bool
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function updateApprove($auditId, $staffId, $status, $cancelreason)
    {
        $db = PenaltyAppealModel::beginTransaction($this);
        try {
            $model = PenaltyAppealModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $auditId
                ],
                'for_update' => true
            ]);
            $model = $model ? $model->toArray() : [];

            if ($model) {
                if($model['status'] == $status) {
                    return true;
                    throw new Exception($auditId . " status done..." . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
                }
                $approveService = new ApprovalServer($this->lang, '+07:00');
                if ($status == enums::$audit_status['approved']) {
                    $approveService->approval($auditId, enums::$audit_type['PA'], $staffId,$cancelreason);
                } else if ($status == enums::$audit_status['dismissed']) {
                    $approveService->reject($auditId, enums::$audit_type['PA'], $cancelreason, $staffId);
                }else if ($status == enums::$audit_status['revoked']) {
                    $approveService->cancel($auditId, enums::$audit_type['PA'], $cancelreason, $staffId);
                }

            } else {
                throw new Exception($auditId . " auditid not found..." . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
            }

            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log("penalty_approve "
                . "file " . $e->getFile()
                . " line " . $e->getLine()
                . " message " . $e->getMessage()
                . " trace " . $e->getTraceAsString(), "error");
            throw $e;
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function genSummary(int $auditId, $user)
    {
        // TODO: Implement genSummary() method.
        $summary = [];
        $model = PenaltyAppealModel::findFirst($auditId);
        $info = $model ? $model->toArray() : [];
        $staffName = (new PublicRepository())->getStaffName($user);
        if ($info) {
            $summary = [
                [
                    'key' => 'penalty_reason',
                    'value' => $info['penalty_reason']
                ],
                [
                    'key' => 'apply_parson',
                    'value' => $staffName . '(' . $user . ')'
                ],

            ];
        }
        return $summary;

    }

    /** * @inheritDoc
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        // TODO: Implement setProperty() method.
        if ($isFinal) {
            $model = PenaltyAppealModel::findFirst($auditId);
            $model->status = $state;
            if($state == enums::APPROVAL_STATUS_APPROVAL){
                $model->process_result = 'qaqc_process_result_1';
            }
            $model->save();
            if($state == enums::APPROVAL_STATUS_APPROVAL || $state == enums::APPROVAL_STATUS_REJECTED ||$state == enums::APPROVAL_STATUS_CANCEL || $state == enums::APPROVAL_STATUS_TIMEOUT){
                $params = [
                    'penalty_id'   => $model->penalty_id,
                    'result'       => $state, //2 审核通过 3 驳回
                    'appeal_staff' => $extend['staff_id'] ?? null,
                    'audit_id'     => $auditId,
                ];
                AuditCallbackServer::createData(AuditListEnums::APPROVAL_TYPE_PA,$params);
            }
            return true;
        }

    }

    /**
     * @inheritDoc
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        // TODO: Implement getWorkflowParams() method.
        $model = PenaltyAppealModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => [
                'id' => $auditId
            ]
        ]);
        if(!$model){
            return [
                'is_late_or_early' => false,
                'store_id' => '',
                'penalty_type' => 0,
                'is_unpaid_staff_NAN' => false,//个人代理-不接单未提前通知
            ];
        }
        $type = str_replace('punish_category_msg', '', $model->penalty_reason);
        $staffInfo = (new StaffServer())->getStaffById($model->staff_id,'formal');
        return [
            'is_late_or_early'    => $model->penalty_reason == 'punish_category_msg12' || $model->penalty_reason == 'punish_category_msg26' || $model->penalty_reason == 'punish_category_msg57',
            'store_id'            => $model->store_id,
            'penalty_type'        => (int)$type,
            'is_unpaid_staff_NAN' => $model->penalty_reason == 'punish_category_msg76',
            'is_partner'          => isCountry('TH') && in_array($staffInfo['formal'],
                [HrStaffInfoModel::FORMAL_FRANCHISEE, HrStaffInfoModel::FORMAL_FRANCHISEE_OTHER]),
        ];
    }


    /**
     * @param $params
     * @return true
     * @throws Exception
     */
    public function delayCallBack($params): bool
    {
        $rmq  = new RocketMQ('penalty-appeal-result');
        $rmq->setHandleType(RocketMQ::TAG_BY_APPEAL_RESULT);
        $rid  = $rmq->sendToMsg($params,30);
        $this->getDI()->get("logger")->write_log('penalty-appeal-result rid:' . $rid . 'data:' . json_encode($params, JSON_UNESCAPED_UNICODE), $rid ? 'info' : 'error');
        return true;
    }


    /**
     * 写入redis队列
     * @param $data
     * @return mixed
     */
    public function pushRedis($data)
    {
        $redis = $this->getDI()->get('redisLib');
        $res   = $redis->lpush(RedisEnums::LIST_BI_PENALTY_DAILY, json_encode($data));
        $this->getDI()->get("logger")->write_log(['data' => $data, 'func' => 'pushRedis', 'res' => $res], "info");
        return $res;
    }

}