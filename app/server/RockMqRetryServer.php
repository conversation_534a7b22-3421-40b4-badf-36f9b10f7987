<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\RockMqRetryRecordModel;


class RockMqRetryServer extends BaseServer
{

    public function __construct($lang)
    {
        parent::__construct($lang);
    }

    /**
     * @return false|null
     */
    public function getWaitData()
    {
        $record = RockMqRetryRecordModel::find([
            'conditions' => 'state = :state: and sys = :sys:',
            'bind'       => [
                'state' => RockMqRetryRecordModel::STATE_WAITING,
                'sys'   => RockMqRetryRecordModel::SYS_BACKYARD,
            ],
            'order'      => 'retry_num asc',
        ]);
        if (empty($record->toArray())) {
            return false;
        }
        return $record;
    }


    /**
     * @return true
     * @throws Exception
     */
    public function fire(): bool
    {
        $wait_record = $this->getWaitData();
        if (empty($wait_record)) {
            return true;
        }

        foreach ($wait_record as $item) {
            $rmq = new RocketMQ($item->tg);
            if (method_exists($rmq, $item->send_method)) {
                $method = $item->send_method;
                if (!empty($item->type)) {
                    $rmq->setType($item->type);
                }
                if (!empty($item->sharding_key)) {
                    $rmq->setShardingKey($item->sharding_key);
                }
                $rmq->setIsRetry(true);
                try {
                    $rid         = $rmq->$method(json_decode($item->body, true));
                    if(!empty($rid)){
                        $item->state = RockMqRetryRecordModel::STATE_COMPLETED;
                    }
                    $item->retry_num += 1;
                } catch (Exception $e) {
                    $item->retry_num += 1;
                }

                $item->save();
            } else {
                throw new Exception('mq method ' . $item->send_method . ' is not exist !!!!');
            }
        }
        return true;
    }

    /**
     * 保存失败记录
     * @param $tg
     * @param $type
     * @param $send_method
     * @param $body
     * @param $start_deliver_time
     * @return bool
     */
    public static function insertRecord($tg, $type, $send_method, $body, $start_deliver_time,$sharding_key)
    {
        $model                     = new RockMqRetryRecordModel();
        $model->sys                = RockMqRetryRecordModel::SYS_BACKYARD;
        $model->tg                 = $tg;
        $model->type               = $type;
        $model->send_method        = $send_method;
        $model->start_deliver_time = $start_deliver_time;
        $model->sharding_key       = $sharding_key;
        $model->body               = json_encode($body, JSON_UNESCAPED_UNICODE);
        return $model->save();
    }


}