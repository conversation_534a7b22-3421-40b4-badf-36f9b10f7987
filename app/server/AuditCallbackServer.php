<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditCallbackModel;

class AuditCallbackServer extends BaseServer
{

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
    }

    //支持的审批业务类型
    const ALLOW_BIZ_TYPE = [
        AuditListEnums::APPROVAL_TYPE_PA,
        AuditListEnums::APPROVAL_TYPE_MILEAGE,
        AuditListEnums::APPROVAL_TYPE_SYSTEM_CS,
        AuditListEnums::APPROVAL_TYPE_STOP,
        AuditListEnums::APPROVAL_TYPE_SYSTEM_DRIVER_BLACKLIST,
        AuditListEnums::APPROVAL_TYPE_VEHICLE_APPROVAL,
        AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE,
        AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE_FREIGHT,
        AuditListEnums::APPROVAL_TYPE_FLEET_GENERATE_RECORDS,
        AuditListEnums::APPROVAL_TYPE_MESSAGE,
        AuditListEnums::APPROVAL_TYPE_OA_AGENCY_PAYMENT,
        AuditListEnums::APPROVAL_TYPE_IC_RENEWAL,
        AuditListEnums::APPROVAL_TYPE_FRANCHISEES,
        AuditListEnums::APPROVAL_TYPE_WMS_V2,
        AuditListEnums::APPROVAL_TYPE_TRANSFER_CAR,
        AuditListEnums::APPROVAL_TYPE_SCHOOL_PLAN_SEND,
        AuditListEnums::APPROVAL_TYPE_FLEET,
        AuditListEnums::APPROVAL_TYPE_CS_PAYMENT,
        AuditListEnums::APPROVAL_TYPE_SUSPENSION,
        AuditListEnums::APPROVAL_TYPE_OA_BUDGET_WITHHOLDING,
        AuditListEnums::APPROVAL_TYPE_TRANSPORTATION_CONTRACT,
        AuditListEnums::APPROVAL_TYPE_PRICE_LIST,
        AuditListEnums::APPROVAL_TYPE_SINGLE_FARE_ADJUSTMENT,
        AuditListEnums::APPROVAL_TYPE_VEHICLE_REPAIR_REQUEST,//车辆维修申请
        AuditListEnums::APPROVAL_TYPE_WAREHOUSE_CHANGE_STORE,
        AuditListEnums::APPROVAL_TYPE_WAREHOUSE_CHANGE_STATUS,
        AuditListEnums::APPROVAL_TYPE_WAREHOUSE_THREAD_PRICE,
    ];

    public static function createData($biz_type, $params): bool
    {
        $model           = new AuditCallbackModel();
        $model->biz_type = $biz_type;
        $model->params   = json_encode($params);
        return $model->save();
    }


    /**
     * @return void
     * @throws ValidationException
     */
    public function fire($biz_type)
    {
        $data = $this->getWaitCallBackData($biz_type);

        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);

        foreach ($data as $datum) {
            //获取实例
            $server = $approvalServer->getInstance($datum->biz_type);
            if (!method_exists($server, 'delayCallBack')) {
                throw new ValidationException('Class need defined method delayCallBack(xxx)');
            }

            try {
                if (empty($datum->params)) {
                    $datum->is_handle = AuditCallbackModel::IS_HANDLE_YES;
                    $datum->save();
                    continue;
                }
                $params = json_decode($datum->params, true);
                $server->delayCallBack($params);
                $datum->is_handle = AuditCallbackModel::IS_HANDLE_YES;
                $datum->save();
            } catch (Exception $exception) {
                $datum->try_num = $datum->try_num + 1;
                if ($datum->try_num > 5) {
                    $datum->is_handle = 2;
                }
                $datum->save();

                $log_data = [
                    'code'    => $exception->getCode(),
                    'message' => $exception->getMessage(),
                    'file'    => $exception->getFile(),
                    'line'    => $exception->getLine(),
                    'trace'   => $exception->getTraceAsString(),
                    'params'  => $datum->toArray(),
                ];
                $this->getDI()->get('logger')->write_log($log_data, 'error');
            }
        }
    }


    /**
     * @return array
     */
    protected function getFilterBizType(): array
    {
        if (isCountry('TH')) {
            return [
                AuditListEnums::APPROVAL_TYPE_MILEAGE,
            ];
        }
        return [];
    }


    /**
     * 获取待异步回调的数据
     */
    public function getWaitCallBackData($biz_tpe = null)
    {
        $date_time    = gmdate('Y-m-d H:i:s', strtotime('-1 days'));
        $current_time = gmdate('Y-m-d H:i:s', time() - 5);
        $bind         = [
            'created_at'   => $date_time,
            'biz_type'     => self::ALLOW_BIZ_TYPE,
            'is_handle'    => AuditCallbackModel::IS_HANDLE_NO,
            'current_time' => $current_time,
        ];
        $condition = 'created_at >= :created_at: and created_at <= :current_time: AND is_handle = :is_handle: AND biz_type in ({biz_type:array}) ';

        if ($biz_tpe === null) {
            $filter_biz_type = $this->getFilterBizType();
            if (!empty($filter_biz_type)) {
                $condition               .= 'AND biz_type not in ({filter_biz_type:array})';
                $bind['filter_biz_type'] = $filter_biz_type;
            }
        } else {
            $condition        .= 'AND biz_type = :need_biz_type:';
            $bind['need_biz_type'] = $biz_tpe;
        }

        return AuditCallbackModel::find([
            "conditions" => $condition,
            'bind'       => $bind,
        ]);
    }
}
