<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Enums\MaterialAssetsEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Models\oa\MaterialAssetsModel;
use FlashExpress\bi\App\Models\oa\MaterialAssetTransferLogModel;
use FlashExpress\bi\App\Models\oa\MaterialLeaveAssetsModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

/**
 * 新资产-资产相关操作服务层
 * Class MaterialAssetServer
 * @package FlashExpress\bi\App\Server
 */
class MaterialAssetServer extends AuditBaseServer
{
    public $timezone;
    const OA_VALIDATE_CODE = 2;

    /**
     * MaterialAssetServer constructor.
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * rpc请求OA端
     * @param array $params 请求参数组
     * @param string $method 请求方法
     * @return array
     */
    private function rpcOa($params, $method)
    {
        $api_client = new ApiClient("oa_rpc", '', $method, $this->lang);
        $api_client->setParams($params);
        $res = $api_client->execute();
        if (!isset($res['result'])) {
            return [
                'code' => self::OA_VALIDATE_CODE,
                'msg'  => $this->getTranslation()->_('please try again'),
                'data' => [],
            ];
        }
        $res['result']['msg'] = $res['result']['message'] ?? '';
        unset($res['result']['message']);
        return $res['result'];
    }

    /**
     * 资产申请-选择资产（可申请资产列表）
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAssetList($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'get_asset_list');
    }

    /**
     * 资产申请-输入基本信息回显
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAddDefault($user)
    {
        return $this->rpcOa(['id' => $user['id'], 'name' => $user['name']], 'get_add_default');
    }

    /**
     * 资产申请-选择资产使用地点
     * @param array $params 请求参数组
     * @return array
     */
    public function getAddressList($params)
    {
        return $this->rpcOa($params, 'get_address_list');
    }

    /**
     * 资产申请-获取收货人
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getConsigneeList($params, $user)
    {
        $params['user_id']   = $user['id'];
        $params['user_name'] = $user['name'];
        return $this->rpcOa($params, 'get_consignee_list');
    }

    /**
     * 资产申请-提交
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function apply($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_apply');
    }

    /**
     * 资产申请-撤回
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function cancel($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_apply_cancel');
    }

    /**
     * 资产申请-审核通过
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function pass($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_apply_pass');
    }

    /**
     * 资产申请-审核拒绝
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function reject($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_apply_reject');
    }

    /**
     * 资产申请-详情
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function detail($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_apply_detail');
    }

    /**
     * 资产申请-查看出库信息
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws ValidationException
     */
    public function getAssetOutStorageList($params, $user)
    {
        $auditId = $params['audit_id'] ?? '';

        //申请信息
        $info = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($info)) {
            throw new ValidationException('invalid data');
        }

        $params['workflow_no'] = $info->serial_no ?? '';
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'get_asset_out_storage_list');
    }

    /**
     * 资产申请-查看路由
     * @param array $params 请求参数组
     * @return array
     */
    public function getOutboundTrackingInfo($params)
    {
        return $this->rpcOa($params, 'get_outbound_tracking_info');
    }

    /**
     * 资产申请-获取审批通过或驳回站内信消息详情
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAuditMsg(array $params, array $user)
    {
        $returnData['data']['apply_no'] = '';
        //获取详情数据
        $result = SystemExternalApprovalModel::findFirst($params['audit_id']);
        if (empty($result)) {
            return $returnData;
        }
        $result = $result->toArray();
        $detailInfo = $this->detail(['workflow_no' => $result['serial_no'], 'type' => 1], $user);
        $detailInfo = $detailInfo['data'] ?? [];
        $returnData['data']['apply_no'] = $detailInfo['apply_no'] ?? '';
        return $returnData;
    }

    /**
     * @description 获取详情接口
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     * @throws BusinessException
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //获取详情数据
        $result = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($result)) {
            throw new BusinessException('invalid data');
        }
        $result = $result->toArray();
        $detailInfo = $this->detail(['workflow_no' => $result['serial_no'], 'type' => $comeFrom], ['id' => $user]);
        $detailInfo = $detailInfo['data'];

        //申请人信息
        $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
        if ($staff_info['data']) {
            $staff_info = $staff_info['data'];
        }

        //组织详情数据
        //申请人为"个人代理" & 当前登陆人为申请人
        if ($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID && $user == $result['submitter_id']) {
            $detailLists = [
                'apply_parson'       => sprintf('%s ( %s )', $staff_info['name'] ?? '',
                    $staff_info['id'] ?? ''),
                'using_department'   => $detailInfo['use_land_name'],
                'asset_use'          => $detailInfo['products'][0]['use_text'] ?? '',
                'reason_application' => $detailInfo['reason'],
                'asset_serial_no'    => $detailInfo['apply_no'] ?? '',
                'consignee'          => sprintf('%s ( %s )', $detailInfo['consignee_name'] ?? '',
                    $detailInfo['consignee_id'] ?? ''),
                'delivery_way_name'  => $detailInfo['delivery_way_text'] ?? '',
            ];
        } else {
            $detailLists = [
                'apply_parson'                      => sprintf('%s ( %s )', $staff_info['name'] ?? '',
                    $staff_info['id'] ?? ''),
                'hire_type'                         => $this->getTranslation()->_("hire_type_{$staff_info['hire_type']}"),
                'apply_department'                  => sprintf('%s - %s', $staff_info['depart_name'] ?? '',
                    $staff_info['job_name'] ?? ''),
                'belong_company'                    => $detailInfo['company_name'],
                'department'                        => $detailInfo['node_department_name'],
                'hr_probation_field_sys_store_name' => $detailInfo['store_name'],
                'consignee'                         => sprintf('%s ( %s )', $detailInfo['consignee_name'] ?? '',
                    $detailInfo['consignee_id'] ?? ''),
                'delivery_way_name'                 => $detailInfo['delivery_way_text'] ?? '',
                'using_department'                  => $detailInfo['use_land_name'],
                'asset_use'                         => $detailInfo['products'][0]['use_text'] ?? '',
                'reason_application'                => $detailInfo['reason'],
                'asset_serial_no'                   => $detailInfo['apply_no'] ?? '',
            ];
            //非泰国、菲律宾、马来的审核人 || 申请人 看不到雇佣类型
            if (isCountry('LA') || isCountry('VN') || isCountry('ID') || $user == $result['submitter_id']) {
                unset($detailLists['hire_type']);
            }

            //V22411 lnt 隐藏申请人部门、所属公司、所属部门、所属网点
            if (isCountry('MY') && (new StaffServer())->isLntStaff($user)) {
                unset($detailLists['apply_department'], $detailLists['belong_company'], $detailLists['department'], $detailLists['hr_probation_field_sys_store_name']);
            }
        }

        if ($result['status'] == enums::$audit_status['dismissed']) { //已经驳回，需要显示驳回原因
            $detailLists = array_merge($detailLists, ['reject_reason' => $detailInfo['reject_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        $auditlist = new AuditlistRepository($this->lang, $this->timezone);
        $add_hour = $this->config->application->add_hour;
        $data      = [
            'title'       => $auditlist->getAudityType(enums::$audit_type['NAS']),
            'id'          => $result['id'],
            'staff_id'    => $result['submitter_id'],
            'type'        => enums::$audit_type['NAS'],
            'created_at'  => date('Y-m-d H:i:s', (strtotime($result['created_at']) + $add_hour * 3600)),
            'updated_at'  => date('Y-m-d H:i:s', (strtotime($result['updated_at']) + $add_hour * 3600)),
            'status'      => $result['status'],
            'status_text' => $auditlist->getAuditStatus('10'.$result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        $returnData['data']['head']   = $data;
        $returnData['data']['products'] = $detailInfo['products'] ?? []; //产品明细
        $returnData['data']['attachments'] = $detailInfo['attachments'] ?? []; //产品明细

        return $returnData;
    }

    /**
     * @description 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info = SystemExternalApprovalModel::findFirst($auditId);
        if (!empty($info)) {
            $summary = json_decode($info->summary, true);
        } else {
            $summary = json_decode([], true);
        }
        return $summary;
    }

    /**
     * @description 回调接口
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return mixed|void
     * @throws \Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('setProperty  没有找到数据  '.$auditId);
            }
            $SystemExternalApprovalModel->state      = $state;
            $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());
            $SystemExternalApprovalModel->save();

            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                //审批同意
                $server = new MessageServer($this->lang, $this->timeZone);
                $title = isCountry('TH') ? 'แจ้งเตือนการอนุมัติการยื่นขอทรัพย์สิน 资产申请审批通过提醒' : 'Asset application approval reminder 资产申请审批通过提醒';
                $server->sendMessage($SystemExternalApprovalModel->submitter_id, $title, $auditId, ['category' => EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_ASSET_APPLY_AUDIT_PASS')]);
            } elseif ($state == enums::APPROVAL_STATUS_REJECTED) {
                //审批驳回
                $server = new MessageServer($this->lang, $this->timeZone);
                $title = isCountry('TH') ? 'แจ้งเตือนการปฏิเสธการยื่นขอทรัพย์สิน 资产申请审批驳回提醒' : 'Asset application approval rejection reminder  资产申请审批驳回提醒';
                $server->sendMessage($SystemExternalApprovalModel->submitter_id, $title, $auditId, ['category' => EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_ASSET_APPLY_AUDIT_REJECT')]);
            }
        }
        return true;
    }

    /**
     * @description  获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $auditInfo = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return [];
        }
        $parameters = json_decode($auditInfo->approval_parameters, true);
        return $parameters ?? [];
    }

    /**
     * 离职资产-离职须知barcode列表
     * @param $params
     * @return array
     */
    public function assetsByStaffId($params)
    {
        $params['user_id'] = $params['staff_id'];
        return $this->rpcOa($params, 'get_assets_by_staff_id');
    }

    /**
     * 离职资产-离职须知资产列表
     * @param $params
     * @return array
     */
    public function getAssetsDetailByStaffId($params)
    {
        $params['user_id'] = $params['staff_id'];
        return $this->rpcOa($params, 'get_assets_detail_by_staff_id');
    }

    /**
     * 离职资产-上级处理-枚举
     * @param $params
     * @param $user
     * @return array
     */
    public function getAssetsManagerDefault($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'get_assets_manager_default');
    }

    /**
     * 离职资产-上级处理-列表
     * @param $params
     * @param $user
     * @return array
     */
    public function getAssetsManagerList($params, $user)
    {
        $params['user_id'] = $user['id'];
        $oa_return = $this->rpcOa($params, 'get_assets_manager_list');
        //处理返回结构
        $return_data = [
            'data' => [], //返回列表数据
            'is_over' => 1, //老数据的是否加载结束标识,新数据修复并继续兼容
            'oa_offset' => 0 //新数据使用的偏移量
        ];
        if (isset($oa_return['code']) && $oa_return['code'] == ErrCode::SUCCESS) {
            $return_data['data'] = $oa_return['data']['items'] ?? [];
            $return_data['is_over'] = $oa_return['data']['is_over'] ?? 1;
            //没返回偏移量那是出了问题了,结束,不继续查了
            if (!isset($oa_return['data']['oa_offset'])) {
                $return_data['is_over'] = 1;
            }
            $return_data['oa_offset'] = $oa_return['data']['oa_offset'] ?? 0;
        }
        return $return_data;
    }

    /**
     * 离职资产-上级处理-详情
     * @param $params
     * @param $user
     * @return array
     */
    public function getAssetsManagerDetail($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'get_assets_manager_detail');
    }

    /**
     * 离职资产-上级处理-barcode搜索
     * @param $params
     * @param $user
     * @return array
     */
    public function searchAssetsManagerBarcode($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'search_assets_manager_barcode');
    }

    /**
     * 离职资产-上级处理-添加
     * @param $params
     * @param $user
     * @return array
     */
    public function addAssetsManagerInfo($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'add_assets_manager_info');
    }

    /**
     * 离职资产-上级处理-保存
     * @param $params
     * @param $user
     * @return array
     */
    public function editSaveBy($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'edit_assets_manager');
    }

    /**
     * 离职资产-上级处理-删除
     * @param $params
     * @param $user
     * @return array
     */
    public function deleteLeaveAssets($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'delete_leave_assets_manager');
    }

    /**
     * 离职资产-上级处理-待处理数量(红点)
     * @param $user_id
     * @return int
     */
    public function getLeaveAssetsManagerCount($user_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(MaterialLeaveAssetsModel::class);
        $builder->columns('count(id) as count');
        //固定条件
        $builder->andWhere('manager_staff_id = :manager_staff_id:', ['manager_staff_id' => $user_id]);
        $builder->andWhere('manager_status = :manager_status:', ['manager_status' => MaterialAssetsEnums::MANAGER_STATUS_TODO]);
        return (int)$builder->getQuery()->getSingleResult()->count;
        //$params['user_id'] = $user_id;
        //return $this->rpcOa($params, 'get_leave_assets_manager_count');
    }

    /**
     * 离职资产消息-主管的内容获取
     * @param $params
     * @param $user
     * @return array
     */
    public function getLeaveMessageContent($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'get_leave_message_content');
    }

    /**
     * 离职资产-获取审批页资产列表
     * @param $params
     * @return array
     */
    public function getAuditLeaveAssets($params)
    {
        $oa_return = $this->rpcOa($params, 'get_audit_leave_assets');
        //处理返回值,保证assets_process_state和assets两个key存在
        $return = [
            'assets_process_state' => '',
            'assets' => []
        ];
        if (isset($oa_return['code']) && $oa_return['code'] == ErrCode::SUCCESS) {
            $return['assets_process_state'] = $oa_return['data']['assets_process_state'] ?? '';
            $return['assets'] = $oa_return['data']['assets'] ?? [];
        }
        return $return;
    }

    /**
     * 资产转移-我的资产-列表
     * @param array $params 请求参数组
     * @param array $user 当前用户信息
     * @return array
     */
    public function getMyAssetList($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_get_my_asset_list');
    }

    /**
     * 资产转移-通过ids获取我的资产列表(转移时/撤销时选中使用)
     * @param $params
     * @param $user
     * @return array
     * @date 2022/11/20
     */
    public function getMyAssetById($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_get_my_asset_by_id');
    }

    /**
     * 资产转移-我的资产-详情
     * @param array $params 请求参数组
     * @param array $user 当前用户信息
     * @return array
     */
    public function getMyAssetDetail($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_get_my_asset_detail');
    }

    /**
     * 资产转移-获取枚举
     * @param array $params 请求参数组
     * @param array $user 当前用户信息
     * @return array
     */
    public function getOptionsDefault($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_get_options_default');
    }

    /**
     * 资产转移-我的资产-批量转移
     * @param array $params 请求参数组
     * @param array $user 当前用户信息
     * @return array
     */
    public function batchTransfer($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_batch_transfer');
    }

    /**
     * 资产转移-待接收数量(小红点)
     * @param array $params 请求参数组
     * @param array $user 当前用户信息
     * @return int
     */
    public function getToBeReceiver($params, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['log' => MaterialAssetTransferLogModel::class]);
        $builder->leftjoin(MaterialAssetsModel::class, 'log.asset_id = asset.id', 'asset');
        $builder->columns('count(log.id) as count');
        //固定条件
        $builder->andWhere('log.to_staff_id = :to_staff_id: and log.status = :status: and log.is_deleted = :is_deleted:',
            ['is_deleted' => MaterialAssetsEnums::IS_NO_DELETED, 'to_staff_id' => $user['id'], 'status' => MaterialAssetsEnums::TRANSFER_LOG_STATUS_UNRECEIVED]);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 资产转移-我的资产-批量撤销
     * @param $params
     * @param $user
     * @return array
     * @date 2022/11/21
     */
    public function batchCancel($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_batch_cancel');
    }

    /**
     * 资产转移-待接收-批量拒绝
     * @param $params
     * @param $user
     * @return array
     * @date 2022/11/21
     */
    public function batchReject($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_get_batch_reject');
    }

    /**
     * 资产转移-待接收-批量接收
     * @param $params
     * @param $user
     * @return array
     * @date 2022/11/21
     */
    public function batchReception($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_batch_reception');
    }

    /**
     * 资产转移-待接收-待接收列表
     * @param $params
     * @param $user
     * @return array
     * @date 2022/11/21
     */
    public function getReceiverList($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_get_receiver_list');
    }

    /**
     * 资产转移-待接收-待接收列表(批量操作时通过id获取)
     * @param $params
     * @param $user
     * @return array
     * @date 2022/11/21
     */
    public function getReceiverListById($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_get_receiver_list_by_id');
    }

    /**
     * 资产转移-待接收-资产同意书
     * @param $params
     * @param $user
     * @return array
     * @date 2022/11/21
     */
    public function getAssetsPdf($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_get_asset_pdf');
    }

    /**
     * 资产转移-待接收-详情
     * @param $params
     * @param $user
     * @return array
     * @date 2022/11/21
     */
    public function getReceiverDetail($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_get_receiver_detail');
    }

    /**
     * 资产转移-站内信-通过消息id获取资产详情
     * @param $params
     * @param $user
     * @return array
     * @date 2022/11/21
     * @throws BusinessException
     */
    public function getAssetsByMsgId($params, $user)
    {
        $return_data = [
            'content_variable' => [],
            'assets_list' => [],
            'message_type' => ''
        ];
        //通过msg_id查找资产id集合
        $server = new BackyardServer($this->lang, $this->timezone);
        $get_msg_params = [
            'msg_id' => $params['msg_id'],
            'staff_info_id' => $user['id'],
        ];
        $msg_detail = $server->getMessageCourierDetail($get_msg_params);
        if (!isset($msg_detail['content']) || empty($msg_detail['content'])) {
            $this->logger->write_log('message-get-detail-error:msg_id= ' . $params['msg_id'], 'notice');
            throw new BusinessException('message-get-detail-error');
        }
        //解析内容
        $content = json_decode($msg_detail['content'], true);
        //如果需要查询资产列表, 去oa查询
        if (isset($content['asset_ids']) && !empty($content['asset_ids'])) {
            //通过资产id去oa中查询资产数据
            $oa_params = [
                'asset_ids' => array_values(array_unique($content['asset_ids'])),
                'language' => $this->lang,
            ];
            $oa_result = $this->rpcOa($oa_params, 'asset_transfer_get_assets_by_ids');
            if (!isset($oa_result['code']) || $oa_result['code'] != ErrCode::SUCCESS) {
                $this->logger->write_log('get-oa-asset-ids-error:oa_params= ' . json_encode($oa_params) . ',oa_return=' . json_encode($oa_result), 'notice');
                throw new BusinessException('get-oa-asset-ids-error');
            }
            $return_data['assets_list'] = $oa_result['data'];
        }
        //返回信息
        if ($msg_detail['category'] == MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND1 || $msg_detail['category'] == MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND2) {
            $return_data['content_variable']['staff_name'] = $content['staff_name'] ?? '';
            $return_data['content_variable']['number'] = $content['number'] ?? 0;
        } elseif ($msg_detail['category'] == MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REJECT) {
            $return_data['content_variable']['staff_name'] = $content['staff_name'] ?? '';
            $return_data['content_variable']['reject_remark'] = $content['reject_remark'] ?? '';
        } else {
            $return_data['content_variable'] = (object)[];
        }
        $return_data['message_type'] = $msg_detail['category'];

        return $return_data;
    }

    /**
     * 获取登陆者详细信息
     * @param array $user_info 登陆者信息组
     * @return array
     */
    public function getUserInfo($user_info)
    {
        $staffRe = new StaffRepository($this->lang);
        $user_info = $staffRe->getStaffPosition($user_info['staff_id']);
        $user_info['is_lnt'] = 0;
        if(isCountry('MY')){
            $user_info['is_lnt'] = (int)StaffServer::isLntCompanyByInfo($user_info);
        }
        return $user_info;;
    }


    /**
     * 获取资产转移-站内信-资产批量变更提醒[导入转移消息详情]
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function getAssetTransferMsgInfo($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_transfer_msg_info');
    }

    /**
     * 获取资产转移-站内信-资产批量变更提醒[导入转移消息详情]
     * @param array $params 参数组
     * @return array
     */
    public function searchStaff($params)
    {
        return $this->rpcOa($params, 'search_staff');
    }

    /**
     * 资产退回-退回类型-筛选
     * @param array $params 请求参数组
     * @return array
     */
    public function searchReturnType($params)
    {
        return $this->rpcOa($params, 'asset_return_search_return_type');
    }

    /**
     * 资产退回-我的资产-退回（批量）
     * @param array $params 请求参数组
     * @param array $user 当前用户信息
     * @return array
     */
    public function batchReturn($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_return_batch_return');
    }

    /**
     * 资产退回-我的资产-撤销（批量）
     * @param array $params 请求参数组
     * @param array $user 当前用户信息
     * @return array
     */
    public function batchReturnCancel($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_return_batch_return_cancel');
    }

    /**
     * 资产管理-退回资产申请-列表
     * @param array $params 请求参数组
     * @param array $user 当前用户信息
     * @return array
     */
    public function returnList($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_return_list');
    }

    /**
     * 资产管理-退回资产申请-查看
     * @param array $params 请求参数组
     * @param array $user 当前用户信息
     * @return array
     */
    public function returnDetail($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_return_detail');
    }

    /**
     * 资产管理-退回资产申请-撤销
     * @param array $params 请求参数组
     * @param array $user 当前用户信息
     * @return array
     */
    public function returnCancel($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_return_cancel');
    }


    /**
     * 资产管理-退回资产申请-撤销
     * @param array $params 请求参数组
     * @param array $user 当前用户信息
     * @return array
     */
    public function returnAddAssets($params, $user)
    {
        $params['user_id'] = $user['id'];
        return $this->rpcOa($params, 'asset_return_add_asset_list');
    }
}
