<?php
/**
 * OA业务相关审批操作 - 通用服务层 [针对接入了BY审批系统的OA业务]
 */

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Repository\ApplyRepository;

/**
 * Class OACommonAuditServer
 * @package FlashExpress\bi\App\Server
 */
class OACommonAuditServer extends AuditBaseServer
{
    public $timezone;
    const OA_VALIDATE_CODE = 2;

    /**
     * OACommonAuditServer constructor.
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * @description 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info = SystemExternalApprovalModel::findFirst($auditId);
        if (!empty($info)) {
            $summary = json_decode($info->summary, true);
        } else {
            $summary = json_decode([], true);
        }
        return $summary;
    }

    /**
     * @description 回调接口
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return mixed|void
     * @throws \Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('setProperty  没有找到数据  '.$auditId);
            }
            $SystemExternalApprovalModel->state      = $state;
            $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());
            $SystemExternalApprovalModel->save();

            //创建异步回调
            $paramIn = [
                'workflow_no'   => $SystemExternalApprovalModel->serial_no,
                'biz_type'      => $SystemExternalApprovalModel->biz_type,
                'state'         => $state,
                'approval_id'   => !empty($extend['approval']) && is_array($extend['approval'])? current($extend['approval']): enums::SYSTEM_STAFF_ID,
                'approval_time' => !empty($extend['final_approval_time_utc']) ? $extend['final_approval_time_utc']: '',
            ];
            AuditCallbackServer::createData($SystemExternalApprovalModel->biz_type, $paramIn);
        }

        return true;
    }

    /**
     * @description  获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $auditInfo = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return [];
        }
        $parameters = json_decode($auditInfo->approval_parameters, true);
        return is_array($parameters) ? $parameters : [];
    }

    /**
     * @description 获取详情接口
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     * @throws BusinessException
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //获取详情数据
        $result = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($result)) {
            throw new BusinessException('invalid data');
        }
        return $result->toArray();
    }

    /**
     * 异步回调 （需要回调接口做幂等）
     * @description 在审批到达终态时，延迟调用各个业务server实现的delayCallBack方法
     * @return void
     * @throws BusinessException
     */
    public function delayCallBack($params): bool
    {
        $fle_rpc = (new ApiClient('oa_rpc', '', 'approval_callback', $this->lang));
        $fle_rpc->setParams($params);
        $res = $fle_rpc->execute();
        $this->logger->write_log(sprintf('approval_callback result:%s', json_encode($res, JSON_UNESCAPED_UNICODE)), 'info');

        return true;
    }
}
