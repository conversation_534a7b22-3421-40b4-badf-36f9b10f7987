<?php
/**
 * Author: Bruce
 * Date  : 2023-04-17 20:41
 * Description:
 */

namespace FlashExpress\bi\App\Server;


use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\BanklistModel;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\LeaveQuitclaimInfoModel;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\LeaveQuitclaimNoticeModel;
use FlashExpress\bi\App\Models\backyard\StaffIdentityAnnexAuditLogModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\QuitclaimRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

class QuitclaimServer extends AuditBaseServer
{
    public $timezone;

    const SECRET_KEY = 'flash_quitclaim';
    const GET_LIST_PROVINCE = 1;
    const GET_LIST_CITY = 2;
    const GET_LIST_DISTRICT = 3;
    const REDIS_FIELD_PENDING_QUITCLAIM_LIST = 'redis_field_pending_quitclaim_list';//生成quitclaim pdf + img
    const REDIS_FIELD_QUITCLAIM_REJECT_LIST = 'redis_field_quitclaim_reject_list';//驳回通知

    public function __construct($lang = 'en', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 生成签名
     * @param $paramIn
     * @return string
     */
    public function createAuth($paramIn)
    {
        //[1]获取参数
        $staff_info_id = $this->processingDefault($paramIn, 'staff_info_id', 2);
        $quitclaim_id  = $this->processingDefault($paramIn, 'quitclaim_id', 2);
        $time          = time();
        $auth          = md5(self::SECRET_KEY . $staff_info_id . $quitclaim_id . $time);

        $params = '?staff_info_id=' . $staff_info_id . '&quitclaim_id=' . $quitclaim_id . '&time=' . $time . '&auth=' . $auth;

        return $params;
    }

    /**
     * 验签
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function checkAuth($paramIn)
    {
        //[1]获取参数
        $staff_info_id = $this->processingDefault($paramIn, 'staff_info_id', 2);
        $quitclaim_id  = $this->processingDefault($paramIn, 'quitclaim_id', 2);
        $time          = $this->processingDefault($paramIn, 'time', 2);
        $auth          = $this->processingDefault($paramIn, 'auth', 1);

        $checkAuth = md5(self::SECRET_KEY . $staff_info_id . $quitclaim_id . $time);

        if ($auth != $checkAuth) {
            throw new ValidationException($this->getTranslation()->_('identity_info_error'));
        }

        //3个月有效期
        $currentDate    = date('Y-m-d');
        $date           = date('Y-m-d', $time);
        $expirationDate = date('Y-m-d', strtotime("{$date}+3 Months"));

        if ($currentDate > $expirationDate) {
            throw new ValidationException($this->getTranslation()->_('identity_failure'));
        }
        return self::checkReturn(1);
    }

    /**
     * 获取基础信息
     * @param $paramIn
     * @return mixed
     */
    public function getBaseInfo($paramIn)
    {
        //国籍
        $nationality = [];
        foreach (HrStaffInfoModel::NATIONALITY as $key => $value) {
            $oneNationality['code'] = $key;
            $oneNationality['name'] = $this->getTranslation()->_($value);

            $nationality[] = $oneNationality;
        }

        //单身，已婚
        $married = [];
        foreach (LeaveQuitclaimInfoModel::IS_MARRIED as $key => $value) {
            $oneMarried['code'] = $key;
            $oneMarried['name'] = $this->getTranslation()->_($value);

            $married[] = $oneMarried;
        }

        $quitclaimRepository = new QuitclaimRepository($this->timezone);
        $staffInfo           = $quitclaimRepository->getStaffInfo($paramIn['staff_info_id'], 'state,leave_date,bank_no,hire_type');

        $isDisplay  = $this->isDisplayUb($staffInfo);
        $bankNoName = '';
        if ($isDisplay) {
            // 获取上级
            $bankNoInfoObj = HrStaffItemsModel::findFirst([
                'conditions' => "staff_info_id = :staff_id: and item = 'BANK_NO_NAME'",
                'bind'       => ['staff_id' => $paramIn['staff_info_id']],
            ]);
            $bankNoName    = !empty($bankNoInfoObj) ? $bankNoInfoObj->value : '';
        }
        //支付方式
        $paymentMethod = [];
        //个人代理
        if(in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            //        2. 可修改，下拉选项读取「HCM-设置中心-系统设置-by_staff_bank_id_list_independent」，按配置银行id的顺序展示下拉选项
            $bank_list = $this->getUnpaidBankList();
            if ($bank_list) {
                foreach ($bank_list as $bank_id => $bank) {
                    $row['code']                = (int)$bank_id;
                    $row['name']                = $bank['bank_name'];
                    $row['max_length']           = $bank['max_length'];
                    $row['min_length']           = $bank['min_length'];
                    $row['is_update']           = true;
                    $row['bank_account_name']   = $bankNoName;
                    $row['bank_account_number'] = $staffInfo['bank_no'];
                    $paymentMethod[]            = $row;
                }
            }
        }else{//正式员工
            foreach (LeaveQuitclaimInfoModel::PAYMENT_METHOD as $key => $value) {//*************
                if ($key == LeaveQuitclaimInfoModel::PAYMENT_METHOD_UB && !$isDisplay) {
                    continue;
                }
                $onePaymentMethod['code']                = $key;
                $onePaymentMethod['name']                = $this->getTranslation()->_($value);
                $onePaymentMethod['max_length']          = '';
                $onePaymentMethod['min_length']          = '';
                $onePaymentMethod['is_update']           = $key == LeaveQuitclaimInfoModel::PAYMENT_METHOD_UB ? false : true;
                $onePaymentMethod['bank_account_name']   = $key == LeaveQuitclaimInfoModel::PAYMENT_METHOD_UB ? $bankNoName : '';
                $onePaymentMethod['bank_account_number'] = $key == LeaveQuitclaimInfoModel::PAYMENT_METHOD_UB ? $staffInfo['bank_no'] : '';
                $paymentMethod[] = $onePaymentMethod;
            }
        }

        $result['nationality_list'] = $nationality;
        $result['is_married']       = $married;
        $result['payment_method']   = $paymentMethod;
        return $result;
    }

    /**
     * 是否展示 UB 支付方式
     * @param $staffInfo
     * @return bool
     */
    public function isDisplayUb($staffInfo)
    {
        if (empty($staffInfo)) {
            return false;
        }

        //非离职 展示
        if ($staffInfo['state'] != HrStaffInfoModel::STATE_2) {
            return true;
        }

        //离职日期为 空展示
        if (empty($staffInfo['leave_date'])) {
            return true;
        }

        //离职日期距离链接打开日期是否小于6个月
        $currentDate    = date('Y-m-d');
        $leaveDate      = date('Y-m-d', strtotime($staffInfo['leave_date']));
        $expirationDate = date('Y-m-d', strtotime("{$leaveDate}+6 Months"));

        //当前日期大于6个月,不展示
        if ($currentDate > $expirationDate) {
            return false;
        }

        return true;
    }

    /**
     * 查询省市区
     * @param $paramIn
     * @return mixed
     * @throws ValidationException
     */
    public function cityList($paramIn)
    {
        $type = $this->processingDefault($paramIn, 'type', 2);
        $code = $this->processingDefault($paramIn, 'code', 1);

        $sysServer = new SysServer();

        //查省
        if ($type == self::GET_LIST_PROVINCE) {
            $result['list'] = $sysServer->getSysProvinceList();
            return $result;
        }
        if (empty($code)) {
            throw new ValidationException('code ' . $this->getTranslation()->_('miss_args'));
        }

        //查市
        if ($type == self::GET_LIST_CITY) {
            $result['list'] = $sysServer->getCityInfoByProvinceCode($code);
            return $result;
        }

        //查市
        if ($type == self::GET_LIST_DISTRICT) {
            $result['list'] = $sysServer->getDistrictInfoByCityCode($code);
            return $result;
        }
    }

    /**
     * 预览数据
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function previewInfo($params)
    {
        if (in_array($params['payment_method'],
            [LeaveQuitclaimInfoModel::PAYMENT_METHOD_OTHER, LeaveQuitclaimInfoModel::PAYMENT_METHOD_GCASH])) {
            if (empty($params['bank_account_img'])) {
                throw new ValidationException('bank_account_img ' . $this->getTranslation()->_('miss_args'));
            }
        }
        $params['sign_date'] = date('Y-m-d');
        return $this->formatData($params);
    }

    /**
     * 格式化数据
     * @param $data
     * @return mixed
     */
    public function formatData($data)
    {
        $year  = substr($data['sign_date'], 0, 4);
        $month = intval(substr($data['sign_date'], 5, 2));
        $day   = substr($data['sign_date'], 8, 4);

        $sysServer    = new SysServer();
        $provinceCode = array_column($sysServer->getSysProvinceList(), 'name', 'code');

        $cityArr     = array_column($sysServer->getCitiesArrByCodeArrFromCache([
            $data['city'],
            $data['place_of_signing_city'],
        ], 'code,name'), 'name',
            'code');
        $districtArr = array_column($sysServer->getDistrictsArrByCodeArrFromCache([$data['district']], 'code,name'),
            'name', 'code');

        $result['name']                      = $data['name'];
        $result['nationality_name']          = $this->getTranslation()->_(HrStaffInfoModel::NATIONALITY[$data['nationality']]);
        $result['is_married_name']           = $this->getTranslation()->_(LeaveQuitclaimInfoModel::IS_MARRIED[$data['is_married']]);
        $result['province_name']             = $provinceCode[$data['province']] ?? '';
        $result['city_name']                 = $cityArr[$data['city']] ?? '';
        $result['district_name']             = $districtArr[$data['district']] ?? '';
        $result['detail_address']            = $data['detail_address'];
        $result['bank_account_name']         = $data['bank_account_name'];
        $result['bank_account_number']       = $data['bank_account_number'];
        $result['sign_day']                  = $day;
        $result['sign_month']                = StaffRepository::$month_en[$month] . ' ' . $year;
        $result['sign_img']                  = $data['sign_img'];
        $result['place_of_signing_province_name'] = $provinceCode[$data['place_of_signing_province']] ?? '';
        $result['place_of_signing_city_name']     = $cityArr[$data['place_of_signing_city']] ?? '';

        return $result;
    }

    /**
     * 获取详情
     * @param $params
     * @return mixed
     * @throws ValidationException
     *
     */
    public function quitclaimDetail($params)
    {
        $quitclaimRepository = new QuitclaimRepository($this->timezone);
        $quitclaimInfo       = $quitclaimRepository->getQuitclaimInfo($params['quitclaim_id']);

        if (empty($quitclaimInfo)) {
            throw new ValidationException($this->getTranslation()->_('not_found_quitclaim_data'));
        }

        $name           = $quitclaimInfo['name'];
        $nationality    = $quitclaimInfo['nationality'];
        $province       = $quitclaimInfo['province'];
        $city           = $quitclaimInfo['city'];
        $district       = $quitclaimInfo['district'];
        $detail_address = $quitclaimInfo['detail_address'];
        $staffInfo = $quitclaimRepository->getStaffInfo($params['staff_info_id'], 'name, nationality,hire_type, bank_type, bank_no');
        // 没提交过，带出 员工管理里的 信息
        $bank_type         = intval($quitclaimInfo['payment_method']);//银行类型 枚举
        //类型名称
        $bank_name         = !empty($quitclaimInfo['payment_method']) ? $this->getTranslation()->_(LeaveQuitclaimInfoModel::PAYMENT_METHOD[$quitclaimInfo['payment_method']] ?? '') : '';
        $bank_account_name = $quitclaimInfo['bank_account_name'];//持卡人姓名
        $bank_no           = $quitclaimInfo['bank_account_number'];//银行卡号
        $bank_img          = !empty($quitclaimInfo['bank_account_img']) ? explode(',', $quitclaimInfo['bank_account_img']) : NULL;//附件
        if ($quitclaimInfo['audit_status'] == LeaveQuitclaimInfoModel::AUDIT_STATUS_0) {
            if (empty($staffInfo)) {
                throw new ValidationException($this->getTranslation()->_('data_error'));
            }

            //员工基本信息
            $hr_staff_items = HrStaffItemsModel::find([
                'conditions' => 'staff_info_id = :staff_info_id: and item in ("REGISTER_PROVINCE", "REGISTER_CITY", "REGISTER_DISTRICT", "REGISTER_HOUSE_NUM", "REGISTER_VILLAGE_NUM", "REGISTER_VILLAGE", "REGISTER_ALLEY", "REGISTER_STREET", "BANK_NO_NAME")',
                'bind'       => [
                    'staff_info_id' => $params['staff_info_id'],
                ],
            ])->toArray();

            $staffItems = [];
            foreach ($hr_staff_items as $oneStaff) {
                $staffItems[$oneStaff['item']] = $oneStaff['value'];
            }

            $name           = $staffInfo['name'];
            $nationality    = $staffInfo['nationality'];
            $province       = $staffItems['REGISTER_PROVINCE'] ?? '';
            $city           = $staffItems['REGISTER_CITY'] ?? '';
            $district       = $staffItems['REGISTER_DISTRICT'] ?? '';
            $detail_address = ($staffItems['REGISTER_HOUSE_NUM'] ?? '') . ($staffItems['REGISTER_VILLAGE_NUM'] ?? '') . ($staffItems['REGISTER_VILLAGE'] ?? '') . ($staffItems['REGISTER_ALLEY'] ?? '') . ($staffItems['REGISTER_STREET'] ?? '');

            //个人代理 银行相关字段 初始化 没提交过 的取值逻辑 https://flashexpress.feishu.cn/docx/Hya7du1BmonoaxxpUZfca6JHnde
            if (in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
                $bank_list         = $this->getUnpaidBankList();
                $bank_type         = (int)$staffInfo['bank_type'];
                $bank_name         = !empty($bank_list[$bank_type]['bank_name']) ? $bank_list[$bank_type]['bank_name'] : '';
                $bank_no           = $staffInfo['bank_no'];
                $bank_account_name = $staffItems['BANK_NO_NAME'] ?? '';
                //HCM-员工信息审核-银行信息审核」中该员工银行信息附件 里面的最新的一条 不管状态
                $fileInfo = HrStaffAnnexInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and type = :type:',
                    'bind'       => [
                        'staff_id' => $params['staff_info_id'],
                        'type'     => HrStaffAnnexInfoModel::TYPE_BANK_CARD,
                    ],
                    'order'      => 'id DESC',
                ]);
                $img = empty($fileInfo) ? '' : $fileInfo->annex_path_front;
                $bank_img = empty($img) ? NULL : [$img];
            }
        }

        $year  = !empty($quitclaimInfo['sign_date']) ? substr($quitclaimInfo['sign_date'], 0, 4) : '';
        $month = !empty($quitclaimInfo['sign_date']) ? intval(substr($quitclaimInfo['sign_date'], 5, 2)) : '';
        $day   = !empty($quitclaimInfo['sign_date']) ? substr($quitclaimInfo['sign_date'], 8, 4) : '';


        $sysServer    = new SysServer();
        $provinceCode = array_column($sysServer->getSysProvinceList(), 'name', 'code');

        $cityArr     = array_column($sysServer->getCitiesArrByCodeArrFromCache([
            $city,
            $quitclaimInfo['place_of_signing_city'],
        ], 'code,name'),
            'name', 'code');
        $districtArr = array_column($sysServer->getDistrictsArrByCodeArrFromCache([$district],
            'code,name'), 'name', 'code');

        $result['id']                             = $quitclaimInfo['id'];
        $result['name']                           = $name;
        $result['nationality_name']               = !empty($nationality) ? $this->getTranslation()->_(HrStaffInfoModel::NATIONALITY[$nationality]) : '';
        $result['nationality']                    = intval($nationality);
        $result['is_married_name']                = !empty($quitclaimInfo['is_married']) ? $this->getTranslation()->_(LeaveQuitclaimInfoModel::IS_MARRIED[$quitclaimInfo['is_married']]) : '';
        $result['is_married']                     = intval($quitclaimInfo['is_married']);
        $result['province_name']                  = $provinceCode[$province] ?? '';
        $result['province']                       = $province;
        $result['city_name']                      = $cityArr[$city] ?? '';
        $result['city']                           = $city;
        $result['district_name']                  = $districtArr[$district] ?? '';
        $result['district']                       = $district;
        $result['detail_address']                 = $detail_address;
        //个人代理 取值不同
        $result['payment_method']                 = $bank_type;
        $result['payment_method_name']            = $bank_name;
        $result['bank_account_name']              = $bank_account_name;
        $result['bank_account_number']            = $bank_no;
        $result['bank_account_img']               = $bank_img;

        $result['sign_img']                       = $quitclaimInfo['sign_img'];
        $result['place_of_signing_province']      = $quitclaimInfo['place_of_signing_province'];
        $result['place_of_signing_province_name'] = $provinceCode[$quitclaimInfo['place_of_signing_province']] ?? '';
        $result['place_of_signing_city']          = $quitclaimInfo['place_of_signing_city'];
        $result['place_of_signing_city_name']     = $cityArr[$quitclaimInfo['place_of_signing_city']] ?? '';
        $result['sign_day']                       = $day;
        $result['sign_month']                     = !empty($month) ? StaffRepository::$month_en[$month] . ' ' . $year : '';
        $result['status']                         = intval($quitclaimInfo['status']);
        $result['status_name']                    = isset(LeaveQuitclaimInfoModel::QUITCLAIM_AUDIT_STATUS[$quitclaimInfo['status']]) ? $this->getTranslation()->_(LeaveQuitclaimInfoModel::QUITCLAIM_AUDIT_STATUS[$quitclaimInfo['status']]) : '';
        $result['reject_reason']                  = $quitclaimInfo['reject_reason'];
        $result['version']                        = $quitclaimInfo['version'];
        $result['hire_type']                      = $staffInfo['hire_type'];

        return $result;
    }


    /**
     * 提交 quitclaim 信息
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function submitQuitclaimInfo($params)
    {
        //员工信息
        $quitclaimRepository = new QuitclaimRepository($this->timezone);
        $staffInfo           = $quitclaimRepository->getStaffInfo($params['staff_info_id'], 'state,leave_date,bank_no,hire_type');
        $limitBankMethod = array_keys(LeaveQuitclaimInfoModel::PAYMENT_METHOD);//正式员工
        if(in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            $bankList = $this->getUnpaidBankList();
            $limitBankMethod = empty($bankList) ? [] : array_keys($bankList);
            if (empty($params['payment_method']) || empty($params['bank_account_number'])){
                throw new ValidationException('bank_account_number ' . $this->getTranslation()->_('miss_args'));
            }
            (new SysServer($this->lang, $this->timezone))->bankNoVerify($params['payment_method'],$params['bank_account_number']);
        }
        if(!in_array($params['payment_method'], $limitBankMethod)){
            throw new ValidationException('wrong payment method!');
        }

        if(!in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether) && empty($params['sign_img'])){
            throw new ValidationException('wrong sign img!');
        }

        if (in_array($params['payment_method'],
            [LeaveQuitclaimInfoModel::PAYMENT_METHOD_OTHER, LeaveQuitclaimInfoModel::PAYMENT_METHOD_GCASH])) {
            if (empty($params['bank_account_img'])) {
                throw new ValidationException('bank_account_img ' . $this->getTranslation()->_('miss_args'));
            }
        }

        $quitclaimRepository = new QuitclaimRepository($this->timezone);
        $quitclaimInfo       = $quitclaimRepository->getQuitclaimInfo($params['quitclaim_id']);

        if (empty($quitclaimInfo)) {
            throw new ValidationException($this->getTranslation()->_('not_found_quitclaim_data'));
        }

        //审批中或同意，禁止提交
        if (in_array($quitclaimInfo['status'],
            [LeaveQuitclaimInfoModel::AUDIT_STATUS_1, LeaveQuitclaimInfoModel::AUDIT_STATUS_2])) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        //原提交状态 判断是否有没有提交过
        $origin_audit_status = $quitclaimInfo['audit_status'];

        $bank_account_img = !empty($params['bank_account_img']) ? implode(',',
            $params['bank_account_img']) : '';
        if($params['payment_method'] == LeaveQuitclaimInfoModel::PAYMENT_METHOD_UB) {
            $bank_account_img = '';
        }
        //bank_account_img 必填
        if(in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            if(empty($bank_account_img)){
                throw new ValidationException('bank account file empty!');
            }
            if(count($params['bank_account_img']) > 1){
                throw new ValidationException('bank account file at most one!');
            }
        }


        $serialNo   = $this->getRandomId();
        $updateData = [
            'name'                      => $params['name'],
            'nationality'               => $params['nationality'],
            'is_married'                => $params['is_married'],
            'province'                  => $params['province'],
            'city'                      => $params['city'],
            'district'                  => $params['district'],
            'detail_address'            => $params['detail_address'],
            'payment_method'            => $params['payment_method'],
            'bank_account_name'         => $params['bank_account_name'],
            'bank_account_number'       => $params['bank_account_number'],
            'bank_account_img'          => $bank_account_img,
            'status'                    => LeaveQuitclaimInfoModel::AUDIT_STATUS_1,
            'sign_img'                  => $params['sign_img'] ?? '',//个人代理的不用签字
            'place_of_signing_province' => $params['place_of_signing_province'],
            'place_of_signing_city'     => $params['place_of_signing_city'],
            'sign_date'                 => date('Y-m-d'),
            'serial_no'                 => (!empty($serialNo) ? 'QC' . $serialNo : null),
            'reply_status'              => LeaveQuitclaimInfoModel::REPLY_YES,//已回复
            'audit_status'              => LeaveQuitclaimInfoModel::AUDIT_STATUS_1,//待审批
        ];

        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $db->updateAsDict('leave_quitclaim_info', $updateData,
                [
                    'conditions' => 'id = ?',
                    'bind'       => [$params['quitclaim_id']],
                ]
            );

            $noticeInfo = LeaveQuitclaimNoticeModel::findFirst([
                'conditions' => "leave_quitclaim_info_id = :quitclaim_id:",
                'bind'       => [
                    'quitclaim_id' => $params['quitclaim_id'],
                ],
                'order' => 'id desc'
            ]);
            //更新当前这次通知 审批 & 回复状态
            if(!empty($noticeInfo)) {
                $updateDataNotice['audit_status'] = LeaveQuitclaimNoticeModel::AUDIT_STATUS_1;
                $updateDataNotice['reply_status'] = LeaveQuitclaimNoticeModel::REPLY_YES;
                $db->updateAsDict('leave_quitclaim_notice', $updateDataNotice,
                    [
                        'conditions' => 'id = ?',
                        'bind'       => [$noticeInfo->id],
                    ]
                );
            }

            //创建
            $server = new ApprovalServer($this->lang, $this->timezone);

            //第一次提交走 创建
            if ($origin_audit_status == LeaveQuitclaimInfoModel::AUDIT_STATUS_0) {
                $requestId = $server->create($params['quitclaim_id'], AuditListEnums::APPROVAL_TYPE_QUITCLAIM,
                    $params['staff_info_id'], null, []);
            } else {
                if ($quitclaimInfo['audit_status'] != enums::APPROVAL_STATUS_REJECTED) { //非驳回状态，无法重新申请
                    throw new ValidationException('please try again');
                }
                $createParams = [
                    'audit_id'     => $params['quitclaim_id'],
                    'audit_type'   => AuditListEnums::APPROVAL_TYPE_QUITCLAIM,
                    'submitter_id' => $params['staff_info_id'],
                ];
                $requestId    = $server->recreate($createParams);
            }

            if (!$requestId) {
                throw new \Exception('创建审批流失败');
            }
            $db->commit();
        } catch (\Exception $exception) {
            $db->rollBack();
            return self::checkReturn(-3, $exception->getMessage());
        }
        if(!in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            $flag = $this->pushRedisProcess(['quitclaim_id' => $params['quitclaim_id']]);
            if (!$flag) {
                $this->logger->write_log("Quitclaim 生产pdf 写入队列失败,请手动生成pdf quitclaim_id:" . $params['quitclaim_id']);
            }
        }
        return self::checkReturn([]);
    }

    /**
     * 审批-同意 驳回
     * @param $paramIn
     * @return array
     * @throws ValidationException
     * @throws \Exception
     */
    public function update($paramIn)
    {
        $staffId       = $this->processingDefault($paramIn, 'staff_id', 2);
        $quitclaimId   = $this->processingDefault($paramIn, 'audit_id', 2);
        $status        = $this->processingDefault($paramIn, 'status', 2);
        $reject_reason = $this->processingDefault($paramIn, 'reject_reason');

        $quitclaimRepository = new QuitclaimRepository($this->timezone);
        $quitclaimInfo       = $quitclaimRepository->getQuitclaimInfo($quitclaimId, 'status');

        if (empty($quitclaimInfo)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }

        if ($quitclaimInfo['status'] == enums::APPROVAL_STATUS_APPROVAL) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        if ($quitclaimInfo['status'] == enums::APPROVAL_STATUS_REJECTED) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        $server = new ApprovalServer($this->lang, $this->timezone);
        $error = [];
        if ($status == enums::$audit_status['approved']) {
            // 同意
            $flag = $server->approval($quitclaimId, AuditListEnums::APPROVAL_TYPE_QUITCLAIM, $staffId,null,null, $error );
        } else {
            // 驳回
            $flag = $server->reject($quitclaimId, AuditListEnums::APPROVAL_TYPE_QUITCLAIM, $reject_reason, $staffId, null, $error);
        }
        if($flag === false){
            return $error;
        }
        return $this->checkReturn(['data' => ['id' => $quitclaimId]]);
    }

    /**
     * 将生成 pdf + img 和 审批 驳回发送通知 通过 redis 消费处理
     * @param $data
     * @param $key
     * @return bool
     */
    public function pushRedisProcess($data, $key = self::REDIS_FIELD_PENDING_QUITCLAIM_LIST)
    {
        try {
            $redis                     = $this->getDI()->get('redisLib');
            $fieldPendingQuitclaimList = $key;

            $content = json_encode($data, JSON_UNESCAPED_UNICODE);
            $redis->lpush($fieldPendingQuitclaimList, $content);
        } catch (\Exception $exception) {
            return false;
        }

        return true;
    }

    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $quitclaimRepository = new QuitclaimRepository($this->timezone);
        $auditListRepository = new AuditlistRepository($this->lang, $this->timezone);
        $quitclaimInfo       = $quitclaimRepository->getQuitclaimInfo($auditId);
        $staff_info          = $quitclaimRepository->getStaffInfo($quitclaimInfo['staff_info_id'],
            'staff_info_id, name, job_title, node_department_id, sys_store_id, leave_date, hire_type');
        $jobTitle            = $quitclaimRepository->getJobTitleInfo($staff_info['job_title'], 'job_name as name');

        //获取网点信息
        $sysServer = new SysServer();
        $storeInfo = array_column($sysServer->getSysStoreList(), 'name', 'id');

        $departmentRepository = new DepartmentRepository($this->timezone);
        $departmentInfoName   = $departmentRepository->getDepartmentNameById($staff_info['node_department_id']);

        $detailLists['apply_parson']                      = sprintf('%s ( %s )', $staff_info['name'], $staff_info['staff_info_id']);
        $detailLists['apply_department']                  = $departmentInfoName;
        $detailLists['hr_probation_field_job_title']      = $jobTitle['name'] ?? '';
        $detailLists['hr_probation_field_sys_store_name'] = $staff_info['sys_store_id'] == -1 ? enums::HEAD_OFFICE : $storeInfo[$staff_info['sys_store_id']] ?? '';
        $detailLists['leave_date']                        = !empty($staff_info['leave_date']) ? date('Y-m-d', strtotime($staff_info['leave_date'])) : '';
        if(in_array($staff_info['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
            $bankList = $this->getUnpaidBankList();
            $detailLists['payment_method'] = empty($bankList) ? '' : ($bankList[$quitclaimInfo['payment_method']]['bank_name'] ?? '');
        }else{
            $detailLists['payment_method'] = !empty($quitclaimInfo['payment_method']) ? $this->getTranslation()->_(LeaveQuitclaimInfoModel::PAYMENT_METHOD[$quitclaimInfo['payment_method']]) : '';
        }
        //正式员工 才显示 个人代理 没有这个类型
        if ($quitclaimInfo['payment_method'] != LeaveQuitclaimInfoModel::PAYMENT_METHOD_UB) {
            $detailLists['bank_account_name']   = $quitclaimInfo['bank_account_name'];
            $detailLists['bank_account_number'] = $quitclaimInfo['bank_account_number'];
            $detailLists['bank_account_file']   = !empty($quitclaimInfo['bank_account_img']) ? explode(',', $quitclaimInfo['bank_account_img']) : [];
        }

        $file_img_url = json_decode($quitclaimInfo['file_img_url'], true);
        if (json_last_error() != JSON_ERROR_NONE) {
            $file_img_url = [$quitclaimInfo['file_img_url']];
        }

        $detailLists['quitclaim'] = $file_img_url;

        $returnData['data']['detail'] = $this->format($detailLists);
        $add_hour = $this->config->application->add_hour;
        $data = [
            'title'       => $auditListRepository->getAudityType(enums::$audit_type['QC']),
            'id'          => $quitclaimInfo['id'],
            'staff_id'    => $quitclaimInfo['staff_info_id'],
            'type'        => enums::$audit_type['QC'],
            'created_at'  => date('Y-m-d H:i:s',strtotime("{$quitclaimInfo['created_at']}") + $add_hour * 3600),
            'updated_at'  => $quitclaimInfo['updated_at'],
            'status'      => $quitclaimInfo['status'],
            'status_text' => $auditListRepository->getAuditStatus('10' . $quitclaimInfo['status']),
            'serial_no'   => $quitclaimInfo['serial_no'] ?? '',
        ];
        $returnData['data']['head'] = $data;

        return $returnData;
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $quitclaimRepository = new QuitclaimRepository($this->timezone);
        $quitclaimInfo       = $quitclaimRepository->getQuitclaimInfo($auditId, 'staff_info_id');
        $staffInfo           = $quitclaimRepository->getStaffInfo($quitclaimInfo['staff_info_id'],
            'node_department_id, job_title, leave_date');
        $jobTitle            = $quitclaimRepository->getJobTitleInfo($staffInfo['job_title'], 'job_name as name');

        $departmentRepository = new DepartmentRepository($this->timezone);
        $departmentName       = $departmentRepository->getDepartmentNameById($staffInfo['node_department_id']);

        return [
            [
                'key'   => 'apply_department',//申请人部门
                'value' => $departmentName,
            ],
            [
                'key'   => 'apply_job_title_name', //申请人职位
                'value' => $jobTitle['name'] ?? '',
            ],
            [
                'key'   => 'leave_date', //离职日期
                'value' => !empty($staffInfo['leave_date']) ? date('Y-m-d', strtotime($staffInfo['leave_date'])) : '',
            ],
        ];
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed|void
     * @throws ValidationException
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $quitclaimRepository = new QuitclaimRepository($this->timezone);
            $quitclaimInfo       = $quitclaimRepository->getQuitclaimInfo($auditId);

            if (empty($quitclaimInfo)) {
                throw new ValidationException($this->getTranslation()->_('1015') . '[From Quitclaim审批终态设置]');
            }

            if ($quitclaimInfo['status'] == enums::APPROVAL_STATUS_APPROVAL) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From Quitclaim审批终态设置]');
            }

            if ($quitclaimInfo['status'] == enums::APPROVAL_STATUS_REJECTED) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From Quitclaim审批终态设置]');
            }
            $submitStaff = HrStaffInfoModel::findFirst([
                'columns' => 'staff_info_id, hire_type',
                'conditions' => 'staff_info_id = :staff_id:',
                'bind' => ['staff_id' => $quitclaimInfo['staff_info_id']]
            ]);
            if(empty($submitStaff)){
                throw new ValidationException('staff info error');
            }

            $db                   = $this->getDI()->get('db');

            $updateData['status']           = $state;
            $updateData['audit_status']     = $state;

            $time = date('Y-m-d H:i:s');
            // 终态更新 驳回
            if ($state == enums::APPROVAL_STATUS_REJECTED) {

                $reject_reason = $extend['remark'] ?? '';
                //过滤 \n
                $reject_reason = trim(preg_replace("/(\s+)/", " ", $reject_reason));

                // [1] 更新 message 表审核驳回终态, bi端消息无需分发
                $updateData['send_notice_time'] = $time;
                $updateData['reject_reason']    = $reject_reason;

                $flag = $this->pushRedisProcess(['quitclaim_id' => $auditId],
                    $key = self::REDIS_FIELD_QUITCLAIM_REJECT_LIST);
                if (!$flag) {
                    $this->logger->write_log("Quitclaim 驳回 写入队列失败,请手动发送驳回信息 quitclaim_id:" . $auditId);
                }
            }
            //Quitclaim审核通过，且雇佣类型为“个人代理”
            if($state == enums::APPROVAL_STATUS_APPROVAL && in_array($submitStaff->hire_type, HrStaffInfoModel::$agentTypeTogether)){
                $this->syncHrs($quitclaimInfo, $extend['staff_id']);
            }

            $update_res = $db->updateAsDict('leave_quitclaim_info', $updateData,
                [
                    'conditions' => 'id = ?',
                    'bind'       => [$auditId],
                ]
            );

            //更新当前这次通知审批状态
            $noticeInfo = LeaveQuitclaimNoticeModel::findFirst([
                'conditions' => "leave_quitclaim_info_id = :quitclaim_id:",
                'bind'       => [
                    'quitclaim_id' => $auditId,
                ],
                'order' => 'id desc'
            ]);
            //更新当前这次通知 审批 & 回复状态
            if(!empty($noticeInfo)) {
                $updateDataNotice['audit_status'] = $state;
                $updateDataNotice['audit_time']   = $time;
                $db->updateAsDict('leave_quitclaim_notice', $updateDataNotice,
                    [
                        'conditions' => 'id = ?',
                        'bind'       => [$noticeInfo->id],
                    ]
                );
            }

            $log = $update_res ? '审批状态更新成功' : '审批状态更新失败';
            $this->logger->write_log("setProperty - updateMessageAuditStatus - quitclaim: {$auditId}, res = " . $log,
                'info');
        }
    }

    //个人代理的 审核通过 同步claim 信息 到信息审核表 并且加 hris 日志 和 信息审核日志
    public function syncHrs($quitclaimInfo, $operator_id){
        $staff_info_id = $quitclaimInfo['staff_info_id'];// 员工id
        $audit_state   = HrStaffAnnexInfoModel::AUDIT_STATE_PASSED;// 操作状态
        $type          = HrStaffAnnexInfoModel::TYPE_BANK_CARD;

        //操作人信息 日志用
        $operateInfo          = HrStaffInfoModel::findFirst([
            'columns'    => 'staff_info_id, name',
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $operator_id],
        ]);

        //同步 hris 银行信息  hcm modifyBankCardNo 方法
        $attributes['staff_info_id'] = $quitclaimInfo['staff_info_id'];
        $attributes['bank_type']     = $quitclaimInfo['payment_method'];//银行类型
        $attributes['bank_no']       = $quitclaimInfo['bank_account_number'];//银行卡号
        $attributes['bank_no_name']  = $quitclaimInfo['bank_account_name'];//持卡人姓名
        $attributes['operater']      = $operator_id;
        $hr_rpc                      = (new ApiClient('hr_rpc', '', 'update_staff_info', $this->lang));
        $hr_rpc->setParams($attributes);
        $res = $hr_rpc->execute();
        $this->logger->write_log(['qt_sync' => ['param' => $attributes, 'res' => $res]], 'info');
        if ($res['result']['code'] !== 1) {
            $this->logger->write_log(['qt_sync' => ['param' => $attributes, 'res' => $res]]);
            throw new ValidationException(implode('|', $res['result']['msg']));
        }

        try {
            //信息审核 银行卡 记录
            $identity_annex_model = HrStaffAnnexInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: AND type = :type:',
                'bind'       => [
                    'staff_info_id' => $staff_info_id,
                    'type'          => $type,
                ],
            ]);
            if (empty($identity_annex_model)) {
                $identity_annex_model          = new HrStaffAnnexInfoModel();
            }
            // 记录审核前的状态
            $audit_before_state                        = is_null($identity_annex_model->audit_state) ? HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD : $identity_annex_model->audit_state;
            //保存信息审核 银行卡记录
            $identity_annex_model->staff_info_id       = $staff_info_id;
            $identity_annex_model->annex_path_front    = $quitclaimInfo['bank_account_img'];
            $identity_annex_model->card_number         = $quitclaimInfo['bank_account_number'];
            $identity_annex_model->type                = $type;
            $identity_annex_model->audit_state         = $audit_state;
            $identity_annex_model->audit_staff_info_id = $operator_id;
            $identity_annex_model->audit_state_date    = date('Y-m-d H:i:s');
            $identity_annex_model->reject_reason       = '';
            $identity_annex_model->save();

            //记录审核日志
            $audit_log = [
                'staff_info_id'      => $staff_info_id,
                'audit_id'           => $operator_id ?: 0,
                'audit_name'         => $operateInfo->name ?? '',
                'audit_before_state' => $audit_before_state,
                'audit_after_state'  => $audit_state,
                'reject_reason'      => '',
                'type'               => $type,  // 1 表示身份证日志，2 表示银行卡日志
            ];
            $logModel  = new StaffIdentityAnnexAuditLogModel();
            $logModel->create($audit_log);
        } catch (\Exception $e) {
            $this->logger->write_log(['qt_sync' => ['ex' => $e->getMessage(), 'trace' => $e->getTraceAsString()]]);
            return false;
        }
    }


    /**
     * 样例
     * from_node_id | to_node_id | valuate_formula | valuate_code
     * -------------+------------+-----------------+-------------
     *      4       |     5      |    $p1 == 4     | getSubmitterDepartment
     *
     * 表示当提交人的部门为4时，审批节点4的下一个节点是5
     * 需要在 getWorkflowParams 中返回申请人所在的部门字段
     *
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $quitclaimRepository = new QuitclaimRepository($this->timezone);
        $quitclaimInfo       = $quitclaimRepository->getQuitclaimInfo($auditId, 'payment_method');

        $result['payment_method'] = $quitclaimInfo['payment_method'];

        return $result;
    }

    /**
     * 获取消quitclaim审批日志
     * @param array $paramIn
     * @return mixed
     */
    public function getAuditStream(array $paramIn)
    {
        try {
            // [1] 接收参数
            $quitclaim_id = $paramIn['quitclaim_id'] ?? 0;

            // [2] 参数校验
            // [2.1] 校验message
            $quitclaimRepository = new QuitclaimRepository($this->timezone);
            $quitclaimInfo       = $quitclaimRepository->getQuitclaimInfo($quitclaim_id, 'status');
            if (empty($quitclaimInfo)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('message_info_null'));
            }

            // 获取审批流日志
            $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail(
                $quitclaim_id,
                enums::$audit_type['QC'],
                $quitclaimInfo['staff_info_id'],
                1, null, '', 3);
            if (empty($returnData['data']['stream'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('approver_is_not_exist'));
            }

            return $this->checkReturn(['data' => ['stream' => $returnData['data']['stream']]]);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('message - getAuditStream' . $e->getMessage());

            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * 发送短信
     * @param $data
     * @return bool
     */
    public function sendSms($data)
    {
        if (empty($data['mobile'])) {
            $this->logger->write_log($data['msg'] . ' staff_info_id:' . $data['staff_info_id'] . " quitclaim_id:" . $data['quitclaim_id'] . ":mobile is empty",
                'info');

            return false;
        }
        $return = curlJsonRpc($this->config->api->api_send_sms, curlJsonRpcStr($data['mobile'], $data['content'],'by_quitclaim'));
        //短信发送成功
        if (isset($return['result'])) {
            $this->logger->write_log($data['msg'] . ' staff_info_id:' . $data['staff_info_id'] . " quitclaim_id:" . $data['quitclaim_id'] . ":Success" . '-result:' . $return['result'],
                'info');
            return true;
        } else {
            //短信发送失败
            $this->logger->write_log($data['msg'] . ' staff_info_id:' . $data['staff_info_id'] . " quitclaim_id:" . $data['quitclaim_id'] . ":Failed, result:" . json_encode($return, JSON_UNESCAPED_UNICODE),
                'notice');
            return false;
        }
    }

    /**
     * 发送邮箱
     * @param $data
     * @return bool
     */
    public function sendEmail($data)
    {
        try {
            $title   = $data['title'];
            $content = $data['content'];

            if (!empty($data['email'])) {
                $sendEmail = \FlashExpress\bi\App\library\Mail::send(
                    $data['email'],
                    $title,
                    $content);

                if ($sendEmail) {
                    $this->logger->write_log($data['msg'] . ' staff_info_id:' . $data['staff_info_id'] . '-' . $data['email'] . ' , 发送邮件成功 quitclaim_id:' . $data['quitclaim_id'],
                        'info');
                    return true;
                } else {
                    $this->logger->write_log($data['msg'] . ' staff_info_id:' . $data['staff_info_id'] . ' , 发送邮件失败 quitclaim_id:' . $data['quitclaim_id'],
                        'info');
                    return false;
                }
            } else {
                $this->logger->write_log($data['msg'] . ' staff_info_id:' . $data['staff_info_id'] . ' , 发送邮件失败,邮箱不能为空 quitclaim_id:' . $data['quitclaim_id'],
                    'info');
                return false;
            }
        } catch (Exception $e) {
            $this->getDi()->get('logger')->write_log('出现异常！quitclaim:邮件发送失败：email:' . $data['email'] . 'message:' . $e->getMessage(),
                'notice');
        }
    }

    //获取个人代理 银行类型
    public function getUnpaidBankList(){
        $bank_ids          = (new SettingEnvServer())->getSetVal('by_staff_bank_id_list_independent');
        $bank_list         = BanklistModel::find([
            'conditions' => 'bank_id in ({bank_ids:array})',
            'bind'       => ['bank_ids' => explode(',', $bank_ids)],
            'columns'    => "bank_id ,bank_name,max_length,min_length ",
            'order'      => 'sort_num desc,bank_id desc',
        ])->toArray();
        $bank_list         = empty($bank_list) ? [] : array_column($bank_list, null, 'bank_id');
        return $bank_list;
    }


}