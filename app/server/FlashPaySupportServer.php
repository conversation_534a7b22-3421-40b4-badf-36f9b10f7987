<?php
namespace FlashExpress\bi\App\Server;

class FlashPaySupportServer extends BaseServer
{
    /**
     * 生成签名
     *
     * @param array $params 用于生成签名的参数组
     * @param array $flash_pay_config flashPay 配置
     * @return string
     * @throws \Exception
     */
    public function generateSign(array $params, array $flash_pay_config = [])
    {
        $private_key = $flash_pay_config['merchant_private_key'] ?? env('merchant_private_key');
        if (is_null($private_key)) {
            throw new \Exception('Missing Merchant Config -- [merchant_private_key]');
        }
        $data = explode('.', $private_key);
        if (end($data) == 'pem') {
            $private_key = openssl_pkey_get_private($private_key);
        } else {
            $private_key = "-----BEGIN RSA PRIVATE KEY-----\n" .
                wordwrap($private_key, 64, "\n", true) .
                "\n-----END RSA PRIVATE KEY-----";
        }
        openssl_sign($this->getSignContent($params), $sign, $private_key, OPENSSL_ALGO_SHA256);

        $sign = base64_encode($sign);

        if (is_resource($private_key)) {
            openssl_free_key($private_key);
        }

        return $sign;
    }

    /**
     * 多维数组按照键值升序排列
     * @param array $params 排序树组
     * @return array
     */
    public function sortParamsByKeyAsc($params)
    {
        if (is_array($params)) {
            ksort($params);
            foreach ($params as $k=>$v) {
                $params[$k] = $this->sortParamsByKeyAsc($v);
            }
        }
        return $params;
    }
    /**
     * 递归过滤数组中的空元素以及不参与签名的元素
     * @param array $arr 要过滤的数组
     * @param array $values
     * @return mixed
     */
    public function filterArray(array $arr, $values = ['', null]) {
        foreach ($arr as $k => $v) {
            if ($k == 'sign' || $k == 'signType') {
                unset($arr[$k]);
            }
            if (is_array($v) && count($v)>0) {
                $arr[$k] = $this->filterArray($v, $values);
            }
            foreach ($values as $value) {
                if ($v === $value) {
                    unset($arr[$k]);
                    break;
                } else if ($v === []) {
                    //flash pay的异常信息中data 是{}的在验签的时候需要吧data值由数组换为{}去验签
                    $arr[$k] = (object)[];
                }
            }
        }
        return $arr;
    }

    /**
     * FlashPay OpenAPI收银台接口文档
     * https://l8bx01gcjr.feishu.cn/docs/doccnZZd6tddvcKeBq2doMG71Sf#
     * - 首先将【公共请求参数】或【公共响应参数】中的字段按字母升序排好
     * - 请求时剔除sign、signType以及空值字段（包括null和空字符串''）
     * - 响应时剔除sign以及空值字段（包括null和空字符串''）
     * - 对于实际入参数，如果为空（包括null和空字符串''）则不参与签名；如果不为空，则对该字段以字母序升序排列后进行json序列化
     * - 用&拼接上述字段，生成待签名字符串
     * @param array $params 用于生产签名组
     * @return string
     */
    public function getSignContent(array $params)
    {
        $filter_data = $this->filterArray($params);
        $data = $this->sortParamsByKeyAsc($filter_data);
        $stringToBeSigned = '';
        foreach ($data as $k => $v) {
            $value = is_array($v) ? json_encode($v,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) : $v;
            $stringToBeSigned .= $k . '=' . $value . '&';
        }
        return trim($stringToBeSigned, '&');
    }

    /**
     * 验签
     *
     * @param array $data 用于生成验签签名的参数组
     * @param bool $sync 用于判断是否需要生成签名，默认都需要生成验签签名
     * @param null $sign 没传递依据接口返回签名
     * @param array $flash_pay_config
     * @return bool
     * @throws \Exception
     */
    public function verifySign(array $data, $sync = false, $sign = null, array $flash_pay_config = [])
    {
        //平台公钥
        $flash_pay_public_key = $flash_pay_config['flash_pay_public_key'] ?? env('flash_pay_public_key');
        if (is_null($flash_pay_public_key)) {
            throw new \Exception('Missing FlashPay Config -- [flash_pay_public_key]');
        }

        $params = explode('.', $flash_pay_public_key);
        if (end($params) == 'pem') {
            $flash_pay_public_key = openssl_pkey_get_public($flash_pay_public_key);
        } else {
            $flash_pay_public_key = "-----BEGIN PUBLIC KEY-----\n".
                wordwrap($flash_pay_public_key, 64, "\n", true).
                "\n-----END PUBLIC KEY-----";
        }

        $sign = $sign ?? $data['sign'];

        $toVerify = $sync ? mb_convert_encoding(json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), 'gb2312', 'utf-8') :
            $this->getSignContent($data);
        $isVerify = openssl_verify($toVerify, base64_decode($sign), $flash_pay_public_key, OPENSSL_ALGO_SHA256) === 1;
        if (is_resource($flash_pay_public_key)) {
            openssl_free_key($flash_pay_public_key);
        }

        return $isVerify;
    }
}
