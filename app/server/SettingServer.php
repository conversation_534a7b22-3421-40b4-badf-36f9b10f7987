<?php

namespace FlashExpress\bi\App\Server;

/**
 * 注意！！！！
 * 这个是backyard "我的"=>"设置" 使用的
 */
class SettingServer extends BaseServer
{
    public function __construct($lang)
    {
        parent::__construct($lang);
    }
    protected $staff_info = [];

    public function setStaffInfo(array $staff_info)
    {
        $this->staff_info = $staff_info;
    }

    public function list(): array
    {
        $data[] = $this->getPasswordItem();
        return $data;
    }

    protected function getPasswordItem(): array
    {
        return [
            'id'                                => 'password',
            'icon'                              => '',
            'title'                             => $this->getTranslation()->_('login_password'),
            'type'                              => '2',
            'dst'                               => '',
            'read_count'                        => '',
            'right_text'                        => '',
            'right_state_text'                  => '',
            'right_state_text_background_color' => '',
            'right_icon'                        => '',
        ];
    }


}