<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\backyard\HrShiftModel;

use FlashExpress\bi\App\Models\backyard\HrStaffShiftHistoryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftMiddleDateModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftPresetModel;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;

/**
 * @method getFlexibleShiftListFromCache()
 */
class HrShiftServer extends BaseServer
{

    public function __construct()
    {
        parent::__construct();
    }
    //固化员工信息
    public $transferData = [];


    /**
     * @description:获取弹性班次列表
     * @return array : []
     * @author: L.J
     * @time: 2022/11/14 15:01
     */
    public function getFlexibleShiftList()
    {
        $shift_list = HrShiftModel::find([
            'conditions' => "shift_attendance_type = :shift_attendance_type:",
            'bind'       => [
                'shift_attendance_type' => HrShiftModel::SHIFT_ATTENDANCE_TYPE_FLEXIBLE,
            ],
        ])->toArray();
        return array_column($shift_list, null, 'id');
    }

    /**
     * @description:获取弹性打卡班次信息
     * @param int $shift_id
     * @return array : 秒
     * @author: L.J
     * @time: 2022/11/14 20:46
     */
    public function getFlexibleShiftData($shift_id = 0)
    {
        $shift_data = [];
        if (empty($shift_id)) {
            return $shift_data;
        }
        //获取弹性班次
        $flexible_shift_list = $this->getFlexibleShiftListFromCache();
        $shift_data          = $flexible_shift_list[$shift_id] ?? [];
        if (empty($shift_data)) {
            return $shift_data;
        }
        //弹性时间- 秒   存在弹性时间 并且 弹性是弹性打卡班次
        $shift_data['current_flexible_time'] = isset($shift_data['shift_attendance_type']) && $shift_data['shift_attendance_type'] == HrShiftModel::SHIFT_ATTENDANCE_TYPE_FLEXIBLE ? ($shift_data['flexible_minute'] * 60) : 0;
        return $shift_data;
    }

    /**
     * @description:根据上班卡和班次id 获取实际打卡偏移量 返回是秒数,按照分钟计算的
     * @return float|int|mixed :
     * @author: L.J
     * @time: 2022/11/14 21:15
     */
    public function getCurrentFlexibleShiftTime(array $params = [])
    {
        $current_flexible_time = 0;
        //上班打卡时间
        $started_at_time = $params['started_at_time'] ?? ''; // 时间戳
        //班次 id
        $shift_id = $params['shift_id'] ?? 0;
        //考勤日期
        $date = $params['date'] ?? '';
        if (empty($started_at_time) || empty($shift_id) || empty($date)) {
            return $current_flexible_time;
        }
        //获取班次弹性时间
        $shift_data = $this->getFlexibleShiftData($shift_id);
        if (empty($shift_data)) {
            return $current_flexible_time;
        }
        $shift_current_flexible_time = $shift_data['current_flexible_time'] ?? 0;


        $attendance_start_at          = strtotime($date.' '.$shift_data['start']);      //上班班次时间
        $attendance_start_at_flexible = $attendance_start_at + $shift_current_flexible_time; //弹性打卡最晚时间
        //判断是否上班卡迟到 并且在弹性打卡时间内
        if ($started_at_time >= $attendance_start_at + 60 && $started_at_time < $attendance_start_at_flexible + 60) {
            //实际弹性时间 - 秒   = 上班卡时间- 班次时间   ,计算的时候只要分钟数,不要秒
            $current_flexible_time = (floor(($started_at_time - $attendance_start_at) / 60) * 60);
        }
        return $current_flexible_time;
    }

    /**
     * //根据日期list 获取对应的班次信息 打卡 和考勤日历用 如果对应日期 没有数据 有可能是支援 或者 新入职的
     * @param $staffId
     * @param array $dateList
     * 返回结构字段 date_at start end shift_type shift_id
     * @return array
     */
    public function getShiftInfos($staffId, array $dateList = [])
    {
        if (empty($dateList)) {
            $dateList[] = date('Y-m-d');
        }
        $today = date('Y-m-d');

        sort($dateList);
        $historyDate = $futureDate = $tmpDate = [];
        foreach ($dateList as $date) {
            //小于今天的日期 history 表
            if ($date < $today) {
                $historyDate[] = $date;
            }
            //每日班次表
            if ($date >= $today) {
                $tmpDate[] = $date;
            }
        }

        //优先查 新增中间表 当天 和未来的
        if(!empty($tmpDate)){
            $tmpShiftData = HrStaffShiftMiddleDateModel::find([
                'columns' => 'shift_date as date_at,shift_start as start,shift_end as [end],shift_id,shift_type',
                'conditions' => 'staff_info_id = :staff_id: and shift_date in ({dates:array}) and deleted = 0',
                'bind' => ['staff_id' => $staffId,'dates' => $tmpDate],
            ])->toArray();
        }

        $tmpShiftData = empty($tmpShiftData) ? [] : array_column($tmpShiftData, null, 'date_at');

        //只用于当天  外协的还在用 不能删
        $shiftInfoData = HrStaffShiftModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'columns'    => 'start,[end],shift_type,shift_id',
            'bind'       => ['staff_id' => $staffId],
        ]);
        if (!empty($shiftInfoData)) {
            $shiftInfoData            = $shiftInfoData->toArray();
            $shiftInfoData['date_at'] = $today;
        }


        //历史日期班次
        if (!empty($historyDate)) {
            $historyShiftData = HrStaffShiftHistoryModel::find([
                'columns'    => 'shift_day as date_at,start,[end],shift_type,shift_id',
                'conditions' => 'staff_info_id = :staff_id: and shift_day in ({dates:array})',
                'bind'       => ['staff_id' => $staffId, 'dates' => $historyDate],
            ])->toArray();
            $historyShiftData = empty($historyShiftData) ? [] : array_column($historyShiftData, null, 'date_at');
        }

        //查询transfer 主播职位没班次
        $transferData = $this->transferData;
        if(empty($this->transferData)){
            $transferData = HrStaffTransferModel::find([
                'columns' => 'staff_info_id, stat_date, job_title',
                'conditions' => 'staff_info_id = :staff_id: and stat_date in ({dates:array})',
                'bind' => [
                    'staff_id' => $staffId,
                    'dates' => $dateList,
                ]
            ])->toArray();
            $transferData = empty($transferData) ? [] : array_column($transferData,null, 'stat_date');
        }

        $liveJobId = (new SettingEnvServer())->getSetVal('free_shift_position');
        $liveJobId = empty($liveJobId) ? [] : explode(',', $liveJobId);

        //整理班次 加上日期
        $return   = [];
        foreach ($dateList as $date) {
            //主播职位没班次 弹性工作
            if(!empty($transferData[$date]) && in_array($transferData[$date]['job_title'], $liveJobId)){
                continue;
            }
            //昨天 查历史数据 没有就没有了
            if (in_array($date, $historyDate)) {
                if (empty($historyShiftData[$date])) {
                    continue;
                }
                $return[$date] = $this->formatShiftData($historyShiftData[$date]);
            }
            //今天 先中间表 后当前shift 表
            if ($date >= $today) {
                if (!empty($tmpShiftData[$date])) {
                    $return[$date] = $this->formatShiftData($tmpShiftData[$date]);
                    continue;
                }
                //没有临时中间表数据 取当前班次信息
                $return[$date] = $this->formatShiftData($shiftInfoData);
            }
        }
        return $return;
    }

    //整理班次 加上日期
    protected function formatShiftData($da)
    {
        $date        = $da['date_at'];
        $shift_start = $da['start'];
        $shift_end   = $da['end'];
        $da['start'] = date("H:i", strtotime("{$date} {$shift_start}"));
        $da['end']   = date("H:i", strtotime("{$date} {$shift_end}"));

        $da['start_datetime'] = $date . " ".$shift_start.':00';
        $da['end_datetime']   = $date . " ".$shift_end.':00';

        if ($da['start'] > $da['end']) {//跨天
            $tomorrow           = date('Y-m-d', strtotime("{$date} +1 day"));
            $da['end_datetime'] = $tomorrow . " " . $shift_end.':00';
        }
        return $da;
    }


    public static function getShiftSeconds($date_at, $shift_start, $shift_end): int
    {
        $att_shift_start = strtotime($date_at . ' ' . $shift_start);
        $att_shift_end   = strtotime($date_at . ' ' . $shift_end);
        if ($shift_start > $shift_end) {
            $att_shift_end = strtotime($date_at . ' ' . $shift_end) + 86400;
        }
        return $att_shift_end - $att_shift_start;
    }


}