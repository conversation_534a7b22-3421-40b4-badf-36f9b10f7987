<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\ConditionsResultEnums;
use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\ConditionsResultConfigurationModel;
use FlashExpress\bi\App\Models\backyard\ConditionsRulesConfigurationModel;
use FlashExpress\bi\App\Models\backyard\ConditionsRulesModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Traits\ComplexFormulaTrait;
use FlashExpress\bi\App\Util\WorkflowFormula;

class ConditionsRulesServer extends BaseServer
{
    use ComplexFormulaTrait;
    private static $instance = null;
    protected $rule_info;
    protected $rule_key = ''; //规则key
    protected $parameters = []; //参与条件参数
    protected $is_validate = true;
    protected $conditions = [];
    protected $formula_package;
    protected $isConditionsFlag = true;//是否配置条件
    //实例化
    public static function getInstance(): ConditionsRulesServer
    {
        return new self();
        if (!self::$instance instanceof self) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 根据规则key获取条件配置的结果
     * @return mixed
     * @throws BusinessException
     */
    public function getConfig()
    {
        if(!$this->isValid()){
            return null;
        }

        //不需要条件
        if(!$this->isConditionsFlag){
            return $this->getSimpleResult();
        }

        registerStream();
        return $this->getConditionsResult();
    }

    public function getRuleKey(): string
    {
        return $this->rule_key;
    }

    public function setRuleKey(string $rule_key): ConditionsRulesServer
    {
        $ruleInfo = false;
        try {
            if (empty($rule_key)) {
                throw new ValidationException('miss args `rule_key`');
            }
            $ruleInfo = ConditionsRulesModel::findFirst([
                'conditions' => 'rule_key = :rule_key: and deleted = 0',
                'bind' => ['rule_key' => $rule_key]
            ]);
            if (empty($ruleInfo)) {
                throw new ValidationException(sprintf('rule key %s not exists', $rule_key));
            }
            if ($ruleInfo->state == ConditionsRulesEnums::RULE_STATE_DISABLE) {
                throw new ValidationException(sprintf('rule key %s is disable', $rule_key));
            }
        } catch (ValidationException $ve) {
            $this->logger->write_log(sprintf('setRuleKey validate err:{%s}, %s', $rule_key, $ve->getMessage()), 'info');
            $this->is_validate = false;
        }
        $this->rule_info = $ruleInfo;
        $this->rule_key = $rule_key;
        $this->conditions      = $this->getRuleDetail();
        $this->formula_package = new WorkflowFormula();
        $this->getIsConditionsFlag();
        return $this;
    }

    protected function getParameters(): array
    {
        return $this->parameters;
    }

    public function setParameters(array $parameters): ConditionsRulesServer
    {
        $this->parameters = (new StaffRepository())->getStaffInfoV3($parameters['staff_info_id']);
        $this->parameters['type'] = $parameters['type'] ?? 0;//加班类型
        return $this;
    }

    /**
     * 加载参数
     * @param array $parameters
     * @return $this
     */
    public function loadParameters(array $parameters): ConditionsRulesServer
    {
        $this->parameters = $parameters;
        return $this;
    }

    /**
     * @return mixed
     */
    protected function getRuleInfo()
    {
        return $this->rule_info;
    }

    /**
     * @param mixed $rule_info
     */
    protected function setRuleInfo($rule_info): void
    {
        $this->rule_info = $rule_info;
    }

    /**
     * 获取规则详情
     * @return array
     */
    protected function getRuleDetail(): array
    {
        if (empty($this->isValid())) {
            return [];
        }
        return ConditionsRulesConfigurationModel::find([
            'conditions' => 'version = :version:',
            'bind' => [
                'version' => $this->getRuleInfo()->version,
            ],
            'columns' => 'valuate_formula,valuate_code,result_id,valuate_sort',
            'order' => 'valuate_sort ASC',
        ])->toArray();
    }

    /**
     * 是否配置了条件
     * @return bool
     */
    protected function getIsConditionsFlag(){
        if(empty($this->conditions)){
            $this->isConditionsFlag = false;
        }

        if(count($this->conditions) == 1 && empty($this->conditions[0]['valuate_formula'])){
            $this->isConditionsFlag = false;
        }
    }

    protected function isValid()
    {
        return $this->is_validate;
    }

    /**
     * 没有配置条件 直接获取值
     */
    protected function getSimpleResult(){
        $resultId = $this->conditions[0]['result_id'] ?? 0;
        $result = $this->getResult($resultId);
        $this->logger->write_log(sprintf( "resultId {$resultId} ". json_encode($result)), 'info');
        return $result;
    }

    protected function getConditionsResult()
    {
        if (empty($this->conditions)) {
            return null;
        }
        $this->logger->write_log(sprintf('getConditionsResult(rule key:%s) ==> start, getParameters:%s',
            $this->getRuleKey(), json_encode($this->getParameters())), 'info');
        $resultId = 0;
        foreach ($this->conditions as $condition) {
            if (empty($condition['valuate_code']) || empty($condition['valuate_formula'])) { //无有效条件、参数
                $this->logger->write_log(sprintf('getConditionsResult(rule key:%s) has matched sort:%d, params:%s, valuate_formula or valuate_code is null',
                    $this->getRuleKey(), $condition['valuate_sort'], json_encode($this->getParameters())), 'info');
                $resultId = $condition['result_id'];
                break;
            }
            $params  = [];
            $methods = explode(',', $condition['valuate_code']);
            foreach ($methods as $k => $method) {
                $method = trim($method);
                if (!method_exists(WorkflowFormula::class, $method)) {
                    throw new BusinessException(sprintf("Handler Method %s not exists", $method));
                }
                $key          = 'p' . ($k + 1);
                $params[$key] = $this->formula_package->$method($this->getParameters());
            }
            if (!strpos($condition['valuate_formula'], '#')) { //存在待解析的条件
                $formula = $condition['valuate_formula'];
            } else {
                $formula = $this->parseComplexCondition($condition['valuate_formula']);
            }
            $result = include 'var://<?php extract($params); return ' . $formula . ';';
            $this->logger->write_log(sprintf('getConditionsResult(rule key:%s) params:%s, formula:%s', $this->getRuleKey(), json_encode($params), $formula), 'info');
            if ($result === true) {
                $this->logger->write_log(sprintf('getConditionsResult(rule key:%s) has matched sort:%d, params:%s'
                    , $this->getRuleKey(),$condition['valuate_sort'], json_encode($this->getParameters())), 'info');
                $resultId = $condition['result_id'];
                break;
            }
            $this->logger->write_log(sprintf('getConditionsResult(rule key:%s) not match sort:%d', $this->getRuleKey(), $condition['valuate_sort']), 'info');
        }
        $this->logger->write_log(sprintf('getConditionsResult(rule key:%s) ==> end, result id:%d', $this->getRuleKey(), $resultId), 'info');
        return $this->getResult($resultId);
    }

    private function getResult($resultId)
    {
        if (empty($resultId)) {
            return null;
        }
        $resultInfo = ConditionsResultConfigurationModel::findFirst($resultId);
        if (empty($resultInfo)) {
            return null;
        }

        if (!empty($resultInfo->result_value)) {
            $response = [
                'response_type' => ConditionsRulesEnums::RESPONSE_TYPE_VALUE,
                'response_data' => $this->formatResponse($resultInfo->result_value, $resultInfo->result_type),
            ];
        } else {
            $response = [
                'response_type' => ConditionsRulesEnums::RESPONSE_TYPE_MESSAGE,
                'response_data' => $resultInfo->notice_key,
            ];
        }
        return $response;
    }

    /**
     * @param $result_value
     * @param $result_type
     * @return mixed
     */
    private function formatResponse($result_value, $result_type)
    {
        return json_decode($result_value, true);
    }
}