<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\MessagePdfModel;
use GuzzleHttp\Exception\GuzzleException;

class MessagePdfServer extends BaseServer
{

    /**
     * 获取证书图片
     * @param $messageId
     * @param $signResult
     * @return array
     */
    public function getCertificateImgByMessageId($messageId): array
    {
        $signInfo = MessagePdfModel::findFirst([
            'columns'    => 'staff_info_id,pdf_url as img_url',
            'conditions' => 'msg_id = :msg_id: and module_category=:module_category:',
            'bind'       => ['msg_id' => $messageId, 'module_category' => MessagePdfModel::MODULE_CATEGORY_CERTIFICATE],
        ]);

        if (!$signInfo) {
            return [];
        }

        return $signInfo->toArray();
    }

    /**
     * 获取证书图片
     * @param $messageId
     * @param $staffInfo
     * @param $extend
     * @return mixed|string
     * @throws GuzzleException
     */
    public function getCertificateImg($messageId, $staffInfo, $extend)
    {
        $existData = $this->getCertificateImgByMessageId($messageId);
        if (!empty($existData['img_url'])) {
            return $existData['img_url'];
        }
        $params['staff_info_id']             = $staffInfo['staff_info_id'];
        $params['staff_name']                = $staffInfo['name'] ?? '';
        $params['certificate_course_name']   = $extend['certificate_course_name'] ?? '';
        $params['certificate_training_time'] = $extend['certificate_training_time'] ?? '';
        $filePath                            = APP_PATH . "/views/backyard/certificate.ftl";
        //获取 html 模板 oss
        if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
            $params['tpl_url'] = $this->getTplPath($filePath);
        } else {
            $params['tpl_url'] = $this->getTplPathFromCache($filePath);
        }
        $imgServer = new FormImgServer();
        $imgSource = $imgServer->htmlToImg([
            'file_name' => $staffInfo['staff_info_id'] . '_certificate' . time() . '.png',
            'tpl_url'   => $params['tpl_url'],
            'data'      => $params,
            'viewport'  => ['width' => 1000, 'height' => 700],
        ]);
        $imgUrl    = empty($imgSource['object_url']) ? '' : $imgSource['object_url'];
        if ($imgUrl) {
            $model                  = new MessagePdfModel();
            $model->pdf_url         = $imgUrl;
            $model->staff_info_id   = $staffInfo['staff_info_id'];
            $model->module_category = MessagePdfModel::MODULE_CATEGORY_CERTIFICATE;
            $model->msg_id          = $messageId;
            $model->save();
        }
        return $imgUrl;
    }

    /**
     * 获取培训系统证书信息
     * @param $msgId
     * @param $userInfo
     * @return array
     */
    public function getSchoolSingInfo($data, $userInfo): array
    {
        $staffLearnPlanId = $data['content'];

        //默认为0
        $returnData = [
            'state'              => '0',
            'pdf_url'            => '',
            'learning_plan_name' => '',
        ];

        $apiClient = new ApiClient('school_rpc', '', 'staff_learn_plan_cert_detail', $this->lang);
        $apiClient->setParams(['staff_learning_plan_id' => $staffLearnPlanId]);
        $result = $apiClient->execute();

        if (isset($result['result']['code']) && ($result['result']['code'] == 1)) {
            $returnData['state']              = $result['result']['data']['state'] ?? '0';
            $returnData['pdf_url']            = $result['result']['data']['pdf_url'] ?? '';
            $returnData['learning_plan_name'] = $result['result']['data']['learning_plan_name'] ?? '';

            //未签字为准
            if (!$data['sign_state']) {
                $returnData['state'] = '0';
            }
        }

        return $returnData;
    }
}