<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\JobTransferConfirmEnums;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\JobTransferSpecialModel;
use FlashExpress\bi\App\Modules\My\library\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Server\AuditBaseServer;
use FlashExpress\bi\App\Server\AuditOptionRule;
use Phalcon\Mvc\Model\Resultset;
use Phalcon\Mvc\Phalcon\Mvc\Model;

class JobTransferSpecialServer extends AuditBaseServer
{

    /**
     * 创建特殊转岗申请
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function create($params)
    {
        $db = $this->getDI()->get('db');
        try {
            $db->begin();

            $model                         = new JobTransferSpecialModel();
            $model->serial_no              = $this->getRandomId();
            $model->submitter_id           = $params['submitter_id'];
            $model->upload_staff_info_file = $params['upload_staff_info_file'];
            $model->batch_code             = $params['batch_code'];
            $model->after_date             = $params['after_date'];
            $model->status                 = enums::APPROVAL_STATUS_PENDING;
            if (!$model->save()) {
                $messages = $model->getMessages();
                foreach ($messages as $message) {
                    $message = '_' . $message;
                }
                throw new \Exception($message);
            }
            $approvalServer = new ApprovalServer($this->lang, $this->timeZone);
            $approvalServer->create($model->id, AuditListEnums::APPROVAL_TYPE_JT_SPECIAL, $params['submitter_id']);

            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            $this->logger->write_log('JobTransferSpecialServer create err:' . $e->getMessage() . $e->getTraceAsString());
            throw $e;
        }

        return $this->checkReturn(['data' => $model->id]);
    }
    
    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //获取详情数据
        $result = JobTransferSpecialModel::findFirst($auditId);
        if (empty($result)) {
            throw new BusinessException('invalid data');
        }
        $result     = $result->toArray();

        //申请人信息
        $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }

        //组织详情数据
        $detailLists = [
            'apply_parson'        => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? ''),
            'apply_department'    => sprintf('%s - %s', $staff_info['depart_name'] ?? '',
                $staff_info['job_name'] ?? ''),
            'job_transfer_source' => $this->getTranslation()->_('job_transfer_special'), //特殊批量转岗
        ];
        $returnData['data']['detail'] = $this->format($detailLists);
        $returnData['data']['detail'][] = [
            'key'        => $this->getTranslation()->_('job_transfer.transfer_staff_detail'),
            'type'       => 1,
            'simple_val' => $result['upload_staff_info_file'] ?? '',
            'value'      => $this->getTranslation()->_('click_here'),
        ];

        $auditListRepo = new AuditlistRepository($this->lang, $this->timeZone);
        $add_hour  = $this->config->application->add_hour;
        $data      = [
            'title'       => $auditListRepo->getAudityType(AuditListEnums::APPROVAL_TYPE_JT_SPECIAL),
            'id'          => $result['id'],
            'staff_id'    => $result['submitter_id'],
            'type'        => AuditListEnums::APPROVAL_TYPE_JT_SPECIAL,
            'created_at'  => date('Y-m-d H:i:s', (strtotime($result['created_at']) + $add_hour * 3600)),
            'updated_at'  => date('Y-m-d H:i:s', (strtotime($result['updated_at']) + $add_hour * 3600)),
            'status'      => $result['status'],
            'status_text' => $auditListRepo->getAuditState($result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        //当前用户审批操作按钮
        $returnData['data']['head'] = $data;

        return $returnData;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false,
            false,
            false,
            false,
            false,
            false);
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $info = JobTransferSpecialModel::findFirst($auditId);
        if (empty($info)) {
            return [];
        }
        $staff = (new StaffServer())->getStaffInfoById($info->submitter_id);
        $data = [
            [
                'key' => 're_field_apply_department_name',
                'value' => $staff->department_name ?? '',
            ],
            [
                'key' => 'apply_job_title_name',
                'value' => $staff->job_name ?? '',
            ],
        ];
        return $data;
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $model = JobTransferSpecialModel::findFirst($auditId);
            $model->status = $state;
            $model->save();

            $jobTransferInfo = JobTransferModel::find([
                'conditions' => 'batch_code = :batch_code: and type = 3',
                'bind' => [
                    'batch_code' => $model->batch_code
                ]
            ]);
            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                $jobTransferInfo->update(['approval_state' => $state]);
            }

            if (in_array($state,[enums::APPROVAL_STATUS_REJECTED, enums::APPROVAL_STATUS_TIMEOUT])) {
                $jobTransferInfo->update(['approval_state' => $state, 'state' => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED]);
            }

            if ($state == enums::APPROVAL_STATUS_TIMEOUT) { //超时不发送提醒
                return;
            }

            //推送文本内容
            //获取语言
            $language = (new StaffServer())->getLanguage($model->submitter_id);
            $add_hour = $this->getDI()['config']['application']['add_hour'];
            $params = [
                'created_at'  => date('Y-m-d H:i:s', strtotime($model->created_at) + $add_hour * 3600),
                'after_date'  => $model->after_date,
                'audit_state' => $this->getTranslation($language)->_("audit_status." . $state),
            ];
            $params['approval_state_name'] = $this->getTranslation($language)->_("audit_status." . $state);
            JobTransferMessageServer::getInstance()->noticeAuditFinish($model->submitter_id,
                $language,
                $params,
                JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL
            );
            $this->logger->write_log("pushAndSendMessageToSubmitter:转岗最终审批-" . $model->submitter_id, 'info');
        }
    }

    /**
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        return [];
    }

    /**
     * 特殊转岗申请审批
     * @param $paramIn
     * @return array
     * @throws BusinessException|ValidationException
     */
    public function updateApproval($paramIn)
    {
        $staffInfoId = $this->processingDefault($paramIn, 'staff_id', 2);
        $status      = $this->processingDefault($paramIn, 'status', 2);
        $reason      = $this->processingDefault($paramIn, 'reject_reason', 1);
        $auditId     = $this->processingDefault($paramIn, 'audit_id', 2);
        $reason      = addcslashes(stripslashes($reason), "'");

        //详情
        $detail = JobTransferSpecialModel::findFirst($auditId);
        if (empty($detail)) {
            throw new ValidationException($this->getTranslation()->_('4008'), enums::$ERROR_CODE['1000']);
        }

        if ($detail['status'] != enums::APPROVAL_STATUS_PENDING) {
            throw new ValidationException($this->getTranslation()->_('please try again'), enums::$ERROR_CODE['1000']);
        }

        try {
            //同意或者驳回等分开处理
            if ($status == enums::APPROVAL_STATUS_APPROVAL) {

                //同意
                $server = new ApprovalServer($this->lang, $this->timeZone);
                $server->approval($auditId, AuditListEnums::APPROVAL_TYPE_JT_SPECIAL, $staffInfoId);
            } else if ($status == enums::APPROVAL_STATUS_REJECTED) {
                //驳回
                $server = new ApprovalServer($this->lang, $this->timeZone);
                $server->reject($auditId, AuditListEnums::APPROVAL_TYPE_JT_SPECIAL, $reason, $staffInfoId);
            } else {
                //特殊转岗不允许撤销
                $this->logger->write_log("updateApproval: batch special transfer not allowed cancel!");
                throw new ValidationException($this->getTranslation()->_('4008'), enums::$ERROR_CODE['1000']);
            }
            return $this->checkReturn([]);
        } catch (\Exception $e) {
            $this->logger->write_log("updateApproval failure:" . $e->getMessage() . $e->getTraceAsString(), "notice");
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * @description 获取转岗后上级
     * @param $params
     * @return mixed
     * @throws \ReflectionException
     */
    public function getAfterManagerIdInfo($params)
    {
        $server = new JobTransferV2Server($this->lang, $this->timeZone);
        $server = Tools::reBuildCountryInstance($server, [$this->lang, $this->timeZone]);
        return $server->getafterManagerIdInfo($params['after_store_id'], $params['after_department_id'],
            $params['after_job_title_id'], $params['after_date'], $params['staff_id']);
    }

    /**
     * 消费批量创建特殊转岗申请队列
     * @param $data
     * @param $submitter_id
     * @param $audit_type
     * @param $uuid
     * @param $apply_time
     * @return void
     */
    public function consumerProcessing($data, $submitter_id, $audit_type, $uuid, $apply_time)
    {
        if (empty($data)) {
            return;
        }
        //错误消息
        $failMessage = [];
        $errMsg = 'success';

        //存在待转岗不能申请
        $info = JobTransferModel::findFirst([
            'conditions' => 'staff_id = :staff_id: and state = 1',
            'bind'       => [
                'staff_id' => $data['staff_id'],
            ],
        ]);
        if (!empty($info)) {
            $this->logger->write_log("Batch Create Job Transfer failure 【存在待转岗】:" . json_encode($data),
                "info");
            return;
        }

        //创建转岗申请
        try {
            $db = $this->getDI()->get('db');
            $db->begin();
            $this->logger->write_log("Create Job Transfer, params:" . json_encode($data),
                "info");

            $columns = [
                'staff_id',
                'submitter_id',
                'current_department_id',
                'current_store_id',
                'current_position_id',
                'current_piece_id',
                'current_region_id',
                'current_company_id',
                'current_role_id',
                'current_manager_id',
                'after_department_id',
                'after_position_id',
                'after_store_id',
                'after_piece_id',
                'after_region_id',
                'after_date',
                'after_manager_id',
                'type',
                'approval_state',
                'batch_code',
                'car_owner',
                'vehicle_source',
                'before_working_day_rest_type',
                'current_job_title_grade',
                'project_num',
                'confirm_state',
                'current_car_type'
            ];
            $model = new JobTransferModel();
            foreach ($columns as $column) {
                if (isset($data[$column])) {
                    $model->$column = $data[$column];
                }
            }
            $model->serial_no       = $this->getRandomId();
            $model->after_date      = $data['after_date'];
            $model->transfer_reason = $data['transfer_reason_id'];
            //转岗后角色、职级、转岗后工作天数与轮休规则，与转岗前保持一致
            $model->after_role_ids              = $data['current_role_id'];
            $model->after_job_title_grade       = $data['current_job_title_grade'];
            $model->after_working_day_rest_type = $data['before_working_day_rest_type'];
            $model->after_company_id            = $data['current_company_id'];
            if (!empty($data['project_num'])) {
                $model->current_project_num = $model->after_project_num = $data['project_num'];
            }

            //转岗后类型
            $server    = new JobTransferV2Server($this->lang, $this->timeZone);
            $server    = Tools::reBuildCountryInstance($server, [$this->lang, $this->timeZone]);
            $afterType = $server->getTransferType($data['after_department_id'], $data['after_position_id']);

            //统一逻辑：根据转岗后职位是否一线判断
            $model->salary_type = $afterType == JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE
                ? JobTransferEnums::SALARY_TYPE_SALARY_STRUCTURE
                : JobTransferEnums::SALARY_TYPE_NOT_CHANGE;
            if ($data['rental_car_cteated_at']) {
                $model->rental_car_cteated_at = $data['rental_car_cteated_at'];
            }
            if ($data['current_rental_car_created_at']) {
                $model->current_rental_car_created_at = $data['current_rental_car_created_at'];
            }

            //转岗后雇佣类型、雇佣期间
            if (isCountry('MY')) {
                if (in_array($data['after_position_id'],
                    [enums::$job_title['van_courier'], enums::$job_title['car_courier']])) {
                    $model->current_car_type = $model->after_car_type = array_search($data['current_car_type'], VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST);
                }
            }
            $model->current_hire_type = $model->after_hire_type = $data['current_hire_type'];
            $model->current_hire_times = $model->after_hire_times = $data['current_hire_times'];

            //根据特殊批量转岗主数据，调整转岗数据的审批状态、转岗状态
            $jobTransferSpecial = JobTransferSpecialModel::findFirstByBatchCode($model->batch_code);
            if (empty($jobTransferSpecial)) {
                $db->rollback();
                return;
            }
            if ($jobTransferSpecial->status != $model->approval_state) {
                $model->approval_state = $jobTransferSpecial->status;
                if ($jobTransferSpecial->status != enums::APPROVAL_STATUS_APPROVAL) {
                    $model->state = JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED;
                }
            }
            $model->save();

            $db->commit();
        } catch (\Exception $e) {
            $failMessage[] = [
                'staff_id' => $data['staff_id'],
                'fail_msg' => $e->getMessage() . $e->getTraceAsString(),
            ];
            $errMsg = "Batch Create Job Transfer failure:" . json_encode($failMessage);
            $this->logger->write_log($errMsg, "info");
        }
        echo $errMsg, PHP_EOL;
    }

    /**
     * 返回被转岗人，用于判断是否为 lnt
     * @param $audit_id
     * @return int|Resultset|Model
     */
    public function checkIfLntStaff($audit_id)
    {
        if (empty($audit_id)) {
            return 0;
        }
        $transferInfo = JobTransferSpecialModel::findFirst($audit_id);
        if (empty($transferInfo)) {
            return 0;
        }
        $transferStaffInfo = JobTransferModel::find([
            'conditions' => 'batch_code = :batch_code:',
            'bind' => [
                'batch_code' => $transferInfo->batch_code
            ],
            'columns' => 'staff_id'
        ])->toArray();
        $transferStaffInfoId = array_column($transferStaffInfo, 'staff_id');

        if (empty($transferStaffInfoId)) {
            return 0;
        }
        $staffInfo = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({staff_ids:array})',
            'bind' => [
                'staff_ids' => $transferStaffInfoId
            ],
            'columns' => 'contract_company_id'
        ])->toArray();
        $staffInfoCompany = array_values(array_unique(array_column($staffInfo, 'contract_company_id')));

        //获取 lnt company
        $lntCompany = (new SettingEnvServer())->getSetVal('lnt_company_ids', ',');

        return !empty(array_intersect($staffInfoCompany, $lntCompany));
    }
}