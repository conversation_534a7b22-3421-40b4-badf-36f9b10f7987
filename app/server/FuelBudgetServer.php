<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/3/20
 * Time: 5:07 PM
 */

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\FileOssUrlModel;
use FlashExpress\bi\App\Models\backyard\FuelApproveModel;
use FlashExpress\bi\App\Models\backyard\ReimbursementFuelConnect;
use FlashExpress\bi\App\Models\backyard\ReimbursementFuelModel;
use FlashExpress\bi\App\Models\oa\ReimbursementDetailTicketModel;
use FlashExpress\bi\App\Models\oa\ReimbursementModel;
use FlashExpress\bi\App\Models\oa\SysDepartmentPcCode;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use function GuzzleHttp2\Psr7\str;

class FuelBudgetServer extends BaseServer{


    public $timezone;
    public $lang;
    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->timezone = $timezone;
        $this->lang     = $lang;
        parent::__construct($this->lang);

    }


    public function get_default($param){

        //用车记录
        $fuel_no = $param['fuel_no'];
        $staff_id = $param['user_info']['id'];
        $fuel_info = FuelApproveModel::find(
            [
                'conditions' => 'serial_no in ({fuel_no:array}) and staff_id = :staff_id:',
                'bind'       => ['fuel_no' => $fuel_no,'staff_id' => $staff_id],
            ]
        )->toArray();
        $data = array();

        if(!empty($fuel_info)){
            $add_hour = $this->config->application->add_hour;
            $start_date = $end_date = array();
            $miles = 0;
            foreach ($fuel_info as $v){
                $miles += $v['end_drive_mileage'] - $v['start_drive_mileage'];
                $start_date[] = date('Y-m-d',strtotime($v['start_drive_time']) + $add_hour * 3600);
                $end_date[] = date('Y-m-d',strtotime($v['end_drive_time']) + $add_hour * 3600);
            }
            $data['miles'] = round($miles,2);
            $data['amount'] = round($miles * 6,2);//（⾥程数*6）泰铢
            sort($start_date);
            sort($end_date);
            //报销时间 区间 取用车记录的 最早开始时间和 最晚结束时间
            $data['start_date'] = empty($start_date[0]) ? '' : date('Y-m-d',strtotime($start_date[0]));
            $end = end($end_date);
            $data['end_date'] = empty($end) ? '' : date('Y-m-d',strtotime($end));
        }

        //获取 最近一次 申请报销的 车牌号和车主姓名
        $car_info = ReimbursementFuelModel::findFirst([
            'conditions' => "staff_info_id = :staff_id: ",
            'bind' => [
                'staff_id'  => $staff_id,
            ],
            'order' => 'id DESC', //排序

        ]);
        $data['car_no'] = $data['car_owner'] = '';
        if(!empty($car_info)){
            $data['car_no'] = $car_info->car_no;
            $data['car_owner'] = $car_info->car_owner;
        }

        return $data;

    }


    public function get_fuel_list($param){
        $staff_id = $param['staff_id'];
        $list = $this->fuel_list($staff_id);

        $data = array();
        if(!empty($list)){
            $add_hour = $this->config->application->add_hour;
            foreach ($list as $li){
                $row['serial_no'] = $li['serial_no'];
                $row['start_drive_time'] = date('Y-m-d H:i:s',strtotime($li['start_drive_time']) + $add_hour * 3600);
                $row['start_drive_place'] = $li['start_drive_place'];
                $row['end_drive_time'] = date('Y-m-d H:i:s',strtotime($li['end_drive_time']) + $add_hour * 3600);
                $row['end_drive_place'] = $li['end_drive_place'];
                $miles = round($li['end_drive_mileage'] - $li['start_drive_mileage'],2);
                if($miles < 0)
                    $miles = 0;

                $row['miles'] = $miles;
                $row['amount'] = round($miles * 6,2);
                $data[] = $row;
            }
        }
        return $this->checkReturn(array('data' => $data));

    }

    //整理成 oa 保存报销单的数据结构
    public function format_data($param, $is_submit)
    {
        if (empty($param['no'])) {
            return $this->getTranslation()->_('miss_args') . '[need budget no.]';
        }

        // 发票抬头必填
        if (empty($param['invoice_header_id'])) {
            return $this->getTranslation()->_('invoice_header_selected_error', ['id' => $param['invoice_header_id']]);
        }

        $fuel_no = $param['fuel_no'];
        $staff_id = $param['user_info']['id'];

        $template_type = $param['template_type'];//对应的模板id
        $object_id = intval($param['object_id']);//科目id
        $object_name = trim($param['object_name']);
        $product_id = intval($param['product_id']);//明细id
        $product_name = trim($param['product_name']);//明细名称
        $ledger_account_id = trim($param['budget_product_account_id']);//科目明细编号
        $car_no = $param['car_no'];//车牌号
        $car_owner = $param['car_owner'];//车主
        $receipt_no = str_replace("，",",",$param['receipt_no']);//发票编号 逗号分割
        $user_tax_amount = round($param['tax_amount'],2);
        $user_no_tax_amount = round($param['no_tax_amount'],2);

        if(count($param['receipt_img']) > 5)
            return 'at most 5 fuel receipts';
        //是否有过路费
        $traffic = $traffic_no_tax = 0;
        $traffic_receipt = array();
        $traffic_receipt_no = '';
        $receipt_no_all = $receipt_no;
        if(!empty($param['is_traffic']) && $param['is_traffic'] == 1){
            $traffic = round($param['traffic_tax_amount'],2);
            $traffic_no_tax = round($param['traffic_no_tax_amount'],2);
            $traffic_receipt = $param['traffic_receipt'];
            if(count($traffic_receipt) > 5)
                return 'at most 5 traffic receipts';

            $traffic_receipt_no = str_replace("，",",",$param['traffic_receipt_no']);
            $receipt_no_all = $receipt_no.','.$traffic_receipt_no;
        }
        $this->logger->write_log("fuel_budget format_data {$staff_id} ".json_encode($param),'info');
        //获取出差记录 差旅费科目 需要出差记录
        $business_info = array();
        if($template_type == 2){
            $business_no = $param['travel_serial_no'];
            $business_info = BusinessTripModel::findFirst("serial_no = '{$business_no}'");
            if(empty($business_info)){
                $this->logger->write_log("fuel_budget format_data {$staff_id} {$business_no} 没有出差记录",'info');
                return 'no business apply data';
            }
            $business_info = $business_info->toArray();
        }
        //整理 用车记录
        $fuel_data = $this->fuel_list($staff_id,$fuel_no);
        if(empty($fuel_data)){
            $this->logger->write_log("fuel_budget format_data {$staff_id} 没有用车记录 " .json_encode($fuel_no) ,'info');
            return 'no fuel apply data';
        }
        //转时区
        $add_hour = $this->config->application->add_hour;

        //获取用户信息
        $staff_re = new StaffRepository($this->lang);
        $staff_info = $staff_re->getStaffPosition($staff_id);
        //获取费用所属中心 先用子部门查 没有的话 拿顶级部门
        $pc_code = SysDepartmentPcCode::findFirst("department_id = {$staff_info['node_department_id']}");
        if(empty($pc_code)){
            $pc_code = SysDepartmentPcCode::findFirst("department_id = {$staff_info['sys_department_id']}");
        }
        $pc_code = empty($pc_code) ? '' : $pc_code->pc_code;
        //取出 用车记录里面的 最大时间和最小时间 以及对应的 地点
        $fuel_time_arr = array_column($fuel_data,'start_drive_time');
        $fuel_time_arr = array_merge($fuel_time_arr,array_column($fuel_data,'end_drive_time'));
        sort($fuel_time_arr);
        $first_time = date('Y-m-d',strtotime($fuel_time_arr[0]) + $add_hour * 3600);
        $last_time_check = end($fuel_time_arr);
        $last_time = date('Y-m-d',strtotime($last_time_check) + $add_hour * 3600);
        //如果是交通费 时间取用车记录的 最早最晚时间 如果是差旅费 取出差时间
        $row['start_at'] = $template_type == 2 ? date('Y-m-d',strtotime($business_info['start_time'])) : $first_time;
        $row['end_at'] =  $template_type == 2 ? date('Y-m-d',strtotime($business_info['end_time'])) : $last_time;
        $row['cost_store_n_name'] = '';//总部员工 网点名称不取
        $row['cost_store_n_id'] = '';
        $row['cost_center_code'] = $pc_code;
        $row['budget_id'] = $object_id;
        $row['ledger_account_id'] = $ledger_account_id;
        $row['product_id'] = $product_id;
        $row['product_name'] =  $product_name;
        $row['template_type'] = $template_type;//差旅费-油费 模板是2 交通费模板 是0

        $row['category_a'] = ($template_type == 2) ? 1 : 0;
        $row['category_b'] = '0';
        $row['isHasCostDetail'] = true;

        $row['cost_store_id'] = '';//空
        $row['cost_store_name'] = '';//空
        $row['travel_start_at'] = $template_type == 2 ? $business_info['start_time'] : '';//出发时间 出差表
        $row['travel_end_at']  = $template_type == 2 ? $business_info['end_time'] : '';
        $row['travel_start']  = $template_type == 2 ? $business_info['departure_city'] : '';//出发城市 出差表
        $row['travel_end']  = $template_type == 2 ? $business_info['destination_city'] : '';
        $row['invoices_ids']  = explode(',',$receipt_no);//发票 编号
        $row['invoices_ids_pdf']  = $receipt_no;//发票 编号
        $row['travel_id'] = $template_type == 2 ? $business_info['id'] : '';//出差id
        $row['travel_serial_no'] = $template_type == 2 ? $business_info['serial_no'] : '';//出差编号
        $row['rate'] = 7;//默认是7%
        $row['initFiles'] = [];//空
        //里程和
        $miles_in_all = 0;
        //报销说明 拼接 用车原因（多个 +BY提交申请单上的⻋牌号+BY提交申请单上的⻋主姓名
        $reason = '';
        $fuel_insert = array();

        foreach($fuel_data as $fuel){
            $reason .= "{$fuel['drive_reason']}-";//默认BY提交的“用车原因”+BY提交申请单上的车牌号+BY提交申请单上的车主姓名
            if($fuel['start_drive_time'] == $fuel_time_arr[0])
                $row['fuel_start'] =  $fuel['start_drive_place'];//⽤⻋申请的起点 取多个用车记录的 最早时间对应地点
            if($fuel['end_drive_time'] == $last_time_check)
                $row['fuel_end'] = $fuel['end_drive_place'];//⽤⻋申请的起点取多个用车记录的 最晚时间对应地点

            $miles = round($fuel['end_drive_mileage'] - $fuel['start_drive_mileage'],2);//里程数
            if($miles > 0){
                $miles_in_all += $miles;
            }

            //整理入库 用车记录关联表
            $f_r['staff_info_id'] = $staff_id;
            $f_r['r_no'] = $param['no'];
            $f_r['f_no'] = $fuel['serial_no'];

            $fuel_insert[] = $f_r;


        }
        $row['info'] = $reason."|{$car_no}|{$car_owner}";

        //含税⾦额-不含税⾦额 用户税额
        $user_tax = $user_tax_amount - $user_no_tax_amount;  //50

        $row['fuel_mileage'] = $miles_in_all;//100
        $count_tax = round($miles_in_all * 6 * 7 / 107,2);//税额 公里数 * 6 * 7 / 107 税额 39.25
        $count_amount = $miles_in_all * 6;//公里数 * 6 含税⾦额 600
        $count_tax_not = round($count_amount - $count_tax,2);//amount-tax 不含税⾦额  600 - 39.25


        //比较大小
        $final_amount = $count_amount;
        $final_tax_not = $count_tax_not;
        if($count_amount > $user_tax_amount){
            $final_amount = $user_tax_amount;
            $final_tax_not = $user_no_tax_amount;
        }
//        $final_amount = ($count_amount > $user_tax_amount ? $user_tax_amount :  $count_amount);//600  -- 550
//        $final_tax_not = ($count_tax_not > $user_no_tax_amount ? $user_no_tax_amount :  $count_tax_not);// 560.75 -- 500
//        $final_tax = ($count_tax > $user_tax ? $user_tax :  $count_tax);// 39.25  --- 50

        //2位小数点
        $row['amount'] = $row['payable_amount'] = round($final_amount,2);//550 + 55.55
        $row['tax_not'] = round($final_tax_not,2);// 500 + 50  550
//        $row['tax'] = round($final_tax + $traffic,2);// 39.25 + 55.55  94.80

        $row['tax'] = round($row['amount'] - $row['tax_not'],2);//新需求改动

        //税率 不知道有没有用 oa那边好像是 重新计算一次
//        if(!empty($row['tax_not']))
//            $row['rate'] = round($row['tax'] / $row['tax_not'],2);//税率，tax/tax_not

        $row['budget_template_type'] = 0;
        $row['cost_center_code'] =  $param['cost_centre']??''; //所属中心

        // 油费包含过路费
        if (!empty($param['is_traffic']) && $param['is_traffic'] == 1) {
            // 过路费
            $row_traffic['start_at'] = $row['start_at'];
            $row_traffic['end_at'] = $row['end_at'];
            $row_traffic['cost_store_n_name'] = '';//总部员工 网点名称不取
            $row_traffic['cost_store_n_id'] = '';
            $row_traffic['budget_id'] = $object_id;
            $row_traffic['ledger_account_id'] = 59; //核算科目ID
            // 差旅费
            if ($object_id == 1) {
                $product_id = 640;
            // 交通费
            } elseif ($object_id == 14) {
                $product_id = 642;
            }
            $row_traffic['product_id'] = isset($product_id) ? $product_id : 0; // 过路费ID
            $row_traffic['product_name'] =  'traffic_budget_detail_name'; // 过路费名称key值

            $row_traffic['category_a'] = ($template_type == 2) ? 1 : 0;
            $row_traffic['category_b'] = '0';
            $row_traffic['isHasCostDetail'] = true;

            $row_traffic['cost_store_id'] = '';//空
            $row_traffic['cost_store_name'] = '';//空
            $row_traffic['deductible_vat_tax'] = 0;//可抵扣VAT税率
            $row_traffic['deductible_tax_amount']  = 0; //可抵扣税额
            $row_traffic['invoices_ids']  = explode(',',$traffic_receipt_no);//发票 编号
            $row_traffic['invoices_ids_pdf']  = $traffic_receipt_no;//发票 编号
            $row_traffic['wht_type']  = 0; // WHT类型
            $row_traffic['wht_tax'] = 0;//WHT税率
            $row_traffic['wht_tax_amount'] = 0;// wht_tax_amount
            $row_traffic['rate'] = 0;//by默认传0
            $row_traffic['initFiles'] = [];//空

            $row_traffic['fuel_start'] =  '';
            $row_traffic['fuel_end'] = '';

            $row_traffic['info'] = $reason."|{$car_no}|{$car_owner}";
            $row_traffic['fuel_mileage'] = $miles_in_all;//100

            $row_traffic['amount'] = $row_traffic['payable_amount'] = round($traffic,2);
            $row_traffic['tax_not'] = round($traffic_no_tax,2);// 500 + 50  550
            $row_traffic['tax'] = round($row_traffic['amount'] - $row_traffic['tax_not'],2);//新需求改动
            $row_traffic['budget_template_type'] = 0;
            $row_traffic['cost_center_code'] =  $param['cost_centre']??''; //所属中心
        }

        //拼接主表报销表数据
        $data['no'] = $param['no'];//报销单编号
        $data['start_at'] = date('Y-m-d');//报销开始时间
        $data['end_at'] =  date('Y-m-d');//报销结束时间
        $data['apply_id'] = $staff_info['id'];//申请人工号 e
        $data['apply_name'] = $staff_info['name'];//名字 e
        $data['apply_department_id'] = $staff_info['node_department_id'];
        $data['apply_department_name'] = $staff_info['depart_name'];//e
        $data['apply_company_name'] =  "Flash Express";//"Flash Express" e
        $data['apply_center_code'] = '';//e
        $data['apply_store_id'] = '-1';
        $data['apply_mobile'] = $staff_info['mobile'];//e
        $data['apply_store_name'] = enums::HEAD_OFFICE;//e
        $data['currency'] = 1;//币种 ，1泰元，2美元，3人民币，暂时只有1泰元',
        $data['bank_account'] = $staff_info['bank_no'];//卡号
        $data['bank_type'] = in_array($staff_info['bank_type'],array(0,2)) ? 'SCB' : 'TMB';//银行
        $data['pay_status'] = 1;//1
        $data['pay_bank_id'] = '';//空
        $data['pay_bank_account'] = '';//空
        $data['loan_amount'] = 0;//冲减借款金额 空
        $data['other_amount'] = 0.00;
        $data['real_amount'] =  0;//空
        $data['cost_department'] = $staff_info['node_department_id'];//费用部门
        $data['cost_store_type'] = $staff_info['organization_type'];//1 网点 2 总部
        $data['country_code'] = '';//空
        $data['created_name'] = $staff_info['name'];//e
        $data['created_id'] = $staff_info['id'];//e
        $data['create_job_title_id'] = $staff_info['job_title'];
        $data['create_job_title_name'] = $staff_info['job_name'];
        $data['created_department_id'] = $staff_info['node_department_id'];//e
        $data['created_department_name'] = $staff_info['depart_name'];
        $data['created_company_name'] = 'Flash Express';//"Flash Express" e

        $data['bank_name'] = $staff_info['name'];//开户人姓名
        $data['sys_store_id'] = '-1';
        $data['sys_department_id'] = $staff_info['sys_department_id'];
        $data['node_department_id'] = $staff_info['node_department_id'];
        $data['cost_department_name'] = $staff_info['depart_name'];
        $data['cost_department_is_update'] = 0;
        $data['cost_store'] = '-1';
        $data['cost_store_name'] = enums::HEAD_OFFICE;
        $data['cost_center_code'] = '';
        $data['cost_store_type_text'] = '';
        $data['is_submit'] = $param['is_submit'];
        $data['source_type'] = 2; //入口来源 1 oa 2 by（油费报销）
        $data['invoice_header_id'] = $param['invoice_header_id']; // 发票抬头

        // 费用所属公司
        $data['cost_company_id'] = 1; // Flash Express

        $data['expense'][] = $row;
        // 包含过路费
        if (isset($row_traffic)) {
            $data['expense'][] = $row_traffic;
        }

        //如果 不是 保存操作 调用验证接口 不需 保存 对应文件
        if($is_submit != 1)
            return $data;


        //提交操作 记录下金额日志
        $this->logger->write_log("fuel_budget_amount {$staff_id} final_amount {$final_amount}, final_tax_not {$final_tax_not}",'info');

        //保存操作 把各种pdf 生成并且当成附件 给oa
        $db = $this->getDI()->get('db');


        try{
            $db->begin();
            $mpdf = new MpdfServer($this->lang,$this->timezone);
            //报销申请单
            $pdf_1_data = $this->format_apply_pdf($data,$template_type,$object_name);
            $temp_arr['path'] = APP_PATH . '/views';
            $temp_arr['dir'] = 'fuelbudget';
            $temp_arr['name'] = "apply_pdf";
            $temp_arr['file_name'] = 'fuel_budget_apply_'.$staff_info['id'];

            $insert['staff_info_id'] = $staff_info['id'];
            $insert['file_type'] = enums::OSS_FILE_TYPE_7;
            $insert['origin_id'] = $param['no'];
            $insert['lang'] = $this->lang;
            //file_oss_url 表 数据保存 获取详情页用 oa那边固定是2 但是 by这边 4个附件 只能显示3个 无法区分
            $pdf_1_return = $mpdf->make_pdf($temp_arr,$pdf_1_data,$insert);// 当成附件放到 attachments
            $pdf_1_return['object_key'] = urldecode($pdf_1_return['object_key']);
            $attachments[] = $pdf_1_return;
            $this->logger->write_log("fuel_budget_format ".json_encode($insert),'info');


            //报销明细表 pdf 里面包含 签名 附件 2 by不显示 不保存
            //签名图片 拼接
            $sign_info = $param['sign_img'];
            $insert['path'] = urldecode($sign_info['object_key']);
            $insert['bucket'] = $sign_info['bucket_name'];
            $insert['file_type'] = enums::OSS_FILE_TYPE_6;

            $model = new FileOssUrlModel();
            $flag = $model->create($insert);
            if(!$flag){
                $this->logger->write_log("fuel_budget_format 失败 ".json_encode($insert),'info');
                $db->rollback();
                return 'apply failed';
            }
            $this->logger->write_log("fuel_budget_format ".json_encode($insert),'info');

            //报销明细单
            $address = $staff_re->get_staff_address($staff_id);
            $body['items'] = array(
                0 => array('name' => "{$object_name}-{$product_name}",'amount' => round($final_amount,2)),
                1 => array('name' => "{$object_name}-过路费",'amount' => round($traffic,2)),
            );
            $body['in_all'] = $final_amount + $traffic;
            $pdf_2_data = $this->format_detail_pdf($data,$staff_info,$address,$sign_info,$body);
            $temp_arr['name'] = "detail_pdf";
            $temp_arr['file_name'] = 'detail_'.$staff_info['id'];
            $pdf_2_return = $mpdf->make_pdf($temp_arr,$pdf_2_data);// 当成附件放到 attachments
            $pdf_2_return['object_key'] = urldecode($pdf_2_return['object_key']);
            $attachments[] = $pdf_2_return;
            $this->logger->write_log("fuel_budget_attachments pdf_2_return ".json_encode($pdf_2_return),'info');
            $model = new FileOssUrlModel();
            //油费发票
            foreach ($param['receipt_img'] as $k => $r_item){
                //发票附件 图片  附件3 转多个 list
                $receipt_img = array(
                    'file_name' => "receipt_{$staff_id}_{$k}.png",
                    'bucket_name' => $r_item['bucket_name'],
                    'object_key' => $r_item['object_key'],
                );
                $attachments[] = $receipt_img;


                $insert_item['path'] = urldecode($r_item['object_key']);
                $insert_item['bucket'] = $r_item['bucket_name'];
                $insert_item['file_type'] = enums::OSS_FILE_TYPE_5;
                $insert_item['staff_info_id'] = $staff_info['id'];
                $insert_item['origin_id'] = $param['no'];
                $insert_item['lang'] = $this->lang;

                $clone_oss_model = clone $model;
                $flag = $clone_oss_model->create($insert_item);
                if(!$flag){
                    $this->logger->write_log("fuel_budget_format receipt_img failed ".json_encode($insert_item),'info');
                    $db->rollback();
                    return 'apply failed';
                }
            }
            //过路费发票
            if(!empty($traffic_receipt)){
                foreach ($traffic_receipt as $k => $tra_re){
                    //发票附件 图片  附件3 转多个 list
                    $receipt_img = array(
                        'file_name' => "traffic_receipt_{$staff_id}_{$k}.png",
                        'bucket_name' => $tra_re['bucket_name'],
                        'object_key' => $tra_re['object_key'],
                    );
                    $attachments[] = $receipt_img;

                    $insert_item['path'] = urldecode($tra_re['object_key']);
                    $insert_item['bucket'] = $tra_re['bucket_name'];
                    $insert_item['file_type'] = enums::OSS_FILE_TYPE_8;
                    $insert_item['staff_info_id'] = $staff_info['id'];
                    $insert_item['origin_id'] = $param['no'];
                    $insert_item['lang'] = $this->lang;

                    $clone_oss_model = clone $model;
                    $flag = $clone_oss_model->create($insert_item);
                    if(!$flag){
                        $this->logger->write_log("fuel_budget_format traffic_receipt_img failed ".json_encode($insert_item),'info');
                        $db->rollback();
                        return 'apply failed';
                    }
                }
            }
            //里程图片 拼接成 pdf
            $fuel_pdf_data = array();
            foreach ($fuel_data as $f){
                $i['start'] = date('Y-m-d H:i:s',strtotime($f['start_drive_time']) + $add_hour * 3600);
                $i['start_img'] = $f['start_drive_mileage_img'];
                $i['end'] = date('Y-m-d H:i:s',strtotime($f['end_drive_time']) + $add_hour * 3600);
                $i['end_img'] = $f['end_drive_mileage_img'];
                $fuel_pdf_data[] = $i;
            }
            $temp_arr['name'] = "fuel_img_pdf";
            $temp_arr['file_name'] = 'fuel_img_'.$staff_info['id'];
           $pdf_img_return = $mpdf->make_pdf($temp_arr,array('image_data' => $fuel_pdf_data));// 当成附件放到 attachments
            $pdf_img_return['object_key'] = urldecode($pdf_img_return['object_key']);
            $attachments[] = $pdf_img_return;
            $this->logger->write_log("fuel_budget_attachments pdf_3_return ".json_encode($pdf_img_return),'info');
            //新pdf
            $pdf_3_data = $this->format_fuel_detail_pdf($staff_info,$fuel_data,$param,$row['fuel_start'],$row['fuel_end']);
            $temp_arr['name'] = "fuel_detail_pdf";
            $temp_arr['file_name'] = 'reimbursement_detail_'.$staff_info['id'];
            $pdf_3_return = $mpdf->make_pdf($temp_arr,$pdf_3_data);// 当成附件放到 attachments
            $pdf_3_return['object_key'] = urldecode($pdf_3_return['object_key']);
            $attachments[] = $pdf_3_return;
            $this->logger->write_log("fuel_budget_attachments pdf_3_return ".json_encode($pdf_3_return),'info');


            //保存 油费报销by 主表
            $re_insert['staff_info_id'] = $staff_id;
            $re_insert['r_no'] = $param['no'];
            $re_insert['car_no'] = $car_no;
            $re_insert['car_owner'] = $car_owner;
            $re_insert['invoices_ids'] = $receipt_no;
            $re_insert['traffic_invoices_ids'] = $traffic_receipt_no;
            $re_insert['no_tax_amount'] = $user_no_tax_amount;
            $re_insert['tax_amount'] = $user_tax_amount;
            $re_insert['traffic_no_tax_amount'] = $traffic_no_tax;
            $re_insert['traffic_tax_amount'] = $traffic;
            $fuel_model = new ReimbursementFuelModel();
            $flag = $fuel_model->create($re_insert);
            if(!$flag){
                $db->rollback();
                $this->logger->write_log("fuel_budget_insert failed ".json_encode($re_insert),'info');
                return 'apply failed';
            }
            //入库 关联用车记录
            $fuel_m = new ReimbursementFuelConnect();
            foreach ($fuel_insert as $item){
                $clone_f_m = clone $fuel_m;
                $clone_f_m->create($item);
                if(!$flag){
                    $db->rollback();
                    $this->logger->write_log("fuel_budget_format 失败 ".json_encode($item),'info');
                    return 'apply failed';
                }
            }
            $db->commit();
            foreach ($data['expense'] as &$v){
                $v['attachments'] = $attachments;
            }
            return $data;

        }catch (\Exception $e){
            $this->logger->write_log("fuel_budget_format 保存失败 ".$e->getMessage());
            $db->rollback();
            return 'server error';
        }
    }

    protected function format_apply_pdf($data,$template_type,$object_name){
        $pdf_1_data['head'] = [];
        $pdf_1_data['head'][] = 'detail_id';
        $pdf_1_data['head'][] = 'category_a';
        $pdf_1_data['head'][] = 'category_b';
        $pdf_1_data['head'][] = 'info';
        $pdf_1_data['head'][] = 'amount';
        $pdf_1_data['head'][] = 'rate';
        $pdf_1_data['head'][] = 'tax';
        $pdf_1_data['head'][] = 'tax_not';
        $pdf_1_data['head'][] = 'invoices_ids';
        $pdf_1_data['head'][] = 'fuel_start';
        $pdf_1_data['head'][] = 'fuel_end';
        $pdf_1_data['head'][] = 'fuel_mileage';
        if($template_type == 2){
            $pdf_1_data['head'][] = 'travel_start';
            $pdf_1_data['head'][] = 'travel_end';
            $pdf_1_data['head'][] = 'start_at';
        }

        $pdf_1_data['country_code_text'] = '';
        $pdf_1_data['currency_text'] = 'THB';
        $pdf_1_data['amount'] = $data['expense'][0]['amount'];
        $pdf_1_data['temp_type'] = $template_type;

        //渲染 pdf 文件 2个 上传 oss

        //报销申请单 pdf
        $column = ["title", "id", "base_info", "created_name", "created_id", "created_company_name", "created_department_name",
            "apply_name", "apply_id", "apply_company_name", "apply_department_name", "apply_store_name", "date", "apply_center_code", "bank_info",
            "bank_name", "currency_text", "bank_account", "bank_type", "detail", "detail_id", "category_a", "category_b", "start_at",
            "travel_start", "travel_end", "fuel_start", "fuel_end", "fuel_mileage", "rate", "tax", "tax_not", "invoices_ids", "stat",
            "detail_amount", "travel", "local", "amount", "loan_amount", "other_amount", "real_amount", "auth_logs", "step", "finished_at",
            "deal_id","deal_name","deal_res","deal_mark","approve","info","cost_store_name","apply_mobile","pay_info","is_pay","pay_bank_account",
            "pay_bank_name","sign_name","pay_at","remark",'amount_in_all'];

        //5 油费报销（发票）6油费报销（签名）7油费报销（申请单pdf）8 详情单 pdf 对应 oa sys_attachment的 sub_type 字段
        //报销申请单pdf 附件 1 by显示


        //翻译
        $f = array();
        foreach ($column as $k){
            $key = "re_field_{$k}";
            $f[$k] = $this->getTranslation()->_($key);
        }
        $f['country_code_project'] = $this->getTranslation()->_('country_code_project');

        $pdf_1_data['field'] = $f;

        //pdf模板用
        $exp['budget_text'] = $object_name;//科目名称
        $exp['pre_fix'] = '6THB';
        $pdf_exp = array_merge($data['expense'][0],$exp);
        $pdf_1_data['expense_v1'][] = $pdf_exp;
        unset($data['expense']);
        $pdf_1_data = array_merge($pdf_1_data,$data);

        return $pdf_1_data;
    }

    protected function format_detail_pdf($data,$staff_info,$address,$sign_info,$body){
        $pdf_2_data['create_time'] = date('d/m/Y');//当前时间
        $pdf_2_data['identity'] = $staff_info['identity'];//身份证号
        $pdf_2_data['address'] = $address;//居住地址 拼接的
        $pdf_2_data['flash'] = 'Flash express';//收款方 Flash express
        $pdf_2_data['amount_text'] = num2Word($data['expense'][0]['amount']);//总额 大写 num2Word
        $pdf_2_data['amount'] = $data['expense'][0]['amount'];//金额综合

        //pdf模板用
        unset($data['expense']);
        $pdf_2_data = array_merge($pdf_2_data,$data);

        $pdf_2_data['body'] = $body['items'];
        $pdf_2_data['in_all'] = $body['in_all'];

        $env_bll = new BySettingRepository();
        $point = $env_bll->get_setting('server_point');
        $pdf_2_data['sign_url'] = "https://" . $sign_info['bucket_name'] . $point. urldecode($sign_info['object_key']);
//        $pdf_2_data['sign_url'] = "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1604996162-5016648049c443f1a4794153486ec634.png";

        return $pdf_2_data;
    }


    //格式化 新增pdf 变量
    public function format_fuel_detail_pdf($staff_info,$fuel_list,$param,$start_place,$end_place){

        $user_tax_amount = $param['tax_amount'];

        //是否有过路费
        $traffic = 0;
        if(!empty($param['is_traffic']) && $param['is_traffic'] == 1){
            $traffic = round($param['traffic_tax_amount'],2);
        }

        $date_at = date('Y-m-d');

        $pdf_data['staff_id'] = $staff_info['staff_info_id'];
        $pdf_data['name'] = $staff_info['name'];
        $pdf_data['fuel_list'] = array();
        $in_all = 0;
        $reason = '';
        foreach ($fuel_list as $f){
            $row['date_at'] = $date_at;
            $row['start_place'] = $f['start_drive_place'];
            $row['end_place'] = $f['end_drive_place'];
            $row['drive_reason'] = $f['drive_reason'];
            $row['start_drive_mileage'] = $f['start_drive_mileage'];
            $row['end_drive_mileage'] = $f['end_drive_mileage'];
            $row['sub_miles'] = $row['end_drive_mileage'] - $row['start_drive_mileage'];
            $row['rate'] = 6;
            $row['amount'] = round($row['sub_miles'] * 6,2);
            $row['user_amount'] = '';


            $in_all += $row['amount'];
            $pdf_data['fuel_list'][] = $row;
            $reason .= $f['drive_reason'].'-';
        }

        $pdf_data['fuel_list'][0]['user_amount'] = $user_tax_amount;//只有第一行展示用户填写的 含税金额


        //第二个table 数据
        $pdf_data['apply_date'] = $date_at;
        $pdf_data['start_place'] = $start_place;
        $pdf_data['end_place'] = $end_place;
        $pdf_data['reason'] = rtrim($reason,'-');
        $pdf_data['traffic'] = $traffic;
        $pdf_data['in_all'] = $in_all;
        $pdf_data['user_amount'] = $user_tax_amount;

        return $pdf_data;

    }




    protected function fuel_list($staff_id,$fuel_list = ''){
        //获取所有用车记录
        $used_list = ReimbursementFuelConnect::find("staff_info_id = {$staff_id} and is_delete = 0")->toArray();
        if(!empty($used_list))
            $used_list = array_column($used_list,'f_no');
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('f.*');
        $builder->from(['f' => FuelApproveModel::class]);
//        $builder->leftJoin(ReimbursementFuelModel::class, 'f.serial_no = r.f_no and f.staff_id = r.staff_info_id', 'r');
        $builder->andWhere('f.staff_id = :staff_id:', ['staff_id' => $staff_id]);
        $builder->andWhere('f.status = 2 and f.input_state = 2');
        if(!empty($used_list))
            $builder->andWhere('f.serial_no not in ({used_list:array})',['used_list' => $used_list]);
//        $builder->orWhere('r.f_no is null');
//        $builder->andWhere('r.is_delete = 1 or r.is_delete is null');
        if(!empty($fuel_list))
            $builder->andWhere('f.serial_no in ({fuel_no:array})',['fuel_no' => $fuel_list]);
        $list = $builder->getQuery()->execute()->toArray();
        return $list;
    }


    public function format_info($data){
        if(empty($data['expense_v1']))
            return [];
        $info['id'] = $data['id'];
        $info['no'] = $data['no'];
        $info['start_date'] = $data['start_at'];
        $info['end_date'] = $data['end_at'];
        $info['amount'] = $data['amount'];

        $exp = $data['expense_v1'][0];//每条都一样 取第一个

        $info['miles'] = $exp['fuel_mileage'];//详情里面 sum

        //获取对应附件
        $attachment = FileOssUrlModel::find("staff_info_id = {$data['created_id']} and file_type in (5,6,7,8) and origin_id = '{$data['no']}'")->toArray();
        $env_bll = new BySettingRepository();
        $point = $env_bll->get_setting('server_point');

        //对应附件
        $info['sign_img'] = $info['re_pdf'] = '';
        $info['receipt_img'] = $info['traffic_receipt_img'] = array();
        if(!empty($attachment)){
            foreach($attachment as $att){
                if($att['file_type'] == enums::OSS_FILE_TYPE_5)//油费发票
                    $info['receipt_img'][] = "https://" . $att['bucket'] . $point. urldecode($att['path']);
                if($att['file_type'] == enums::OSS_FILE_TYPE_6)
                    $info['sign_img'] = "https://" . $att['bucket'] . $point. urldecode($att['path']);
                if($att['file_type'] == enums::OSS_FILE_TYPE_7)
                    $info['re_pdf'] = "https://" . $att['bucket'] . $point. urldecode($att['path']);
                if($att['file_type'] == enums::OSS_FILE_TYPE_8)//过路费发票
                    $info['traffic_receipt_img'][] = "https://" . $att['bucket'] . $point. urldecode($att['path']);

//                if($att['file_type'] == enums::OSS_FILE_TYPE_9)//油费报销 详情 新增的pdf
//                    $info['fuel_detail_pdf'] = "https://" . $att['bucket'] . $point. urldecode($att['path']);

//                if($att['file_type'] == enums::OSS_FILE_TYPE_10)//油费报销 用车记录 图片拼接的pdf
//                    $info['re_pdf'] = "https://" . $att['bucket'] . $point. urldecode($att['path']);
            }
        }

        //获取输入的 金额等信息
        $re_info = ReimbursementFuelModel::findFirst("r_no = '{$data['no']}'");
        if(empty($re_info))
            return [];
        $re_info = $re_info->toArray();
        $info['car_no'] = $re_info['car_no'];
        $info['car_owner'] = $re_info['car_owner'];
        $info['receipt_no'] = $re_info['invoices_ids'];//发票编号
        $info['traffic_receipt_no'] = $re_info['traffic_invoices_ids'];//过路费发票编号
        $info['no_tax_amount'] = $re_info['no_tax_amount'];
        $info['tax_amount'] = $re_info['tax_amount'];
        //是否 有过路费
        $info['is_traffic'] = 0;
        if(!empty(floatval($re_info['traffic_no_tax_amount'])))
            $info['is_traffic'] = 1;
        $info['traffic_no_tax_amount'] = $re_info['traffic_no_tax_amount'];
        $info['traffic_tax_amount'] = $re_info['traffic_tax_amount'];


        //整理 科目是对应名称
        $info['object_code'] = $exp['level_code'];
        $info['object_name'] = $this->getTranslation()->_("fuel_object_".$exp['level_code']);
        $info['product_name'] = $this->getTranslation()->_("fuel_object_product");

        //新增 是否可下载字段 已驳回，已撤销，未付款不能下载
        $info['can_download'] = 1;
        if($data['status'] == 4 || $data['status'] == 2 || $data['pay_status'] == 3)
            $info['can_download'] = 0;

        return $info;
    }

    /**
     * @param $staff_id 工号
     * @param $re_no 报销单号
     */
    public function reject_fuel($staff_id,$re_no){
        try{
            if(empty($staff_id) || empty($re_no))
                return false;

            $this->logger->write_log("reject_fuel {$staff_id} {$re_no} ",'info');
            $re_info = ReimbursementFuelModel::findFirst("staff_info_id = {$staff_id} and r_no = '{$re_no}' and is_delete = 0");

            if(empty($re_info))
                return true;


            $this->getDI()->get('db')->begin();
            //软删主表
            $re_info->is_delete = 1;
            $re_info->update();
            //软删 关联用车表
            $list = ReimbursementFuelConnect::find("staff_info_id = {$staff_id} and r_no = '{$re_no}'");
            foreach ($list as $li){
                $li->is_delete = 1;
                $li->update();
            }
            $this->getDI()->get('db')->commit();

            return true;
        }catch (\Exception $e){
            $this->logger->write_log("reject_fuel failed {$staff_id} {$re_no} ".$e->getMessage());
            $this->getDI()->get('db')->rollback();
            return true;
        }

    }

    //获取费用所属中心
    public function get_cost_centre($user)
    {
        // 成本中心
        $pcCode = '';

        // 费用所属公司
        $costCompanyId = '';

        $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($user['staff_id']);
        if (empty($userInfo->id)) {
            return ['cost_centre' => $pcCode, 'cost_company_id' => $costCompanyId];
        }

        $userInfo = $userInfo->toArray();
        if ($userInfo['sys_store_id'] != -1) {
            // 网点员工传入网点编号
            $pcCode = $userInfo['sys_store_id'];
            $departmentId = !empty($userInfo['node_department_id']) ? $userInfo['node_department_id'] : $userInfo['sys_department_id'];
        } else {
            // 总部员工传入成本中心,获取费用所属中心 先用子部门查 没有的话 拿顶级部门
            $pcCodeModel = SysDepartmentPcCode::findFirst("department_id = {$userInfo['node_department_id']}");
            if (empty($pcCodeModel)) {
                $pcCodeModel = SysDepartmentPcCode::findFirst("department_id = {$userInfo['sys_department_id']}");
            }

            if (!empty($pcCodeModel)) {
                $departmentId = $pcCodeModel->department_id;
                $pcCode = $pcCodeModel->pc_code;
            }
        }

        if (!empty($departmentId)) {
            $sysDepartmentModel = SysDepartmentModel::findFirst($departmentId);
            $costCompanyId = !empty($sysDepartmentModel) ? $sysDepartmentModel->company_id : '';
        }

        return ['cost_centre' => $pcCode, 'cost_company_id' => $costCompanyId];
    }

    //发票编号格式和重复性校验
    public function checkTicketsNo($data)
    {
        // 校验油票的发票编号格式
        $receiptNoList = explode(',', $data['receipt_no']);
        if(!empty($data['is_traffic']) && $data['is_traffic'] == 1) {
            // 校验发票编号格式
            $trafficReceiptNoList = explode(',', $data['traffic_receipt_no']);
            $receiptNoList = array_merge($receiptNoList,$trafficReceiptNoList);
        }
        $ticketsNo = [];
        foreach ($receiptNoList as $receiptNo) {
            if (!preg_match('/^[0-9a-zA-Z\'\-\.\(\)\/]+$/', $receiptNo)) {
                return $this->getTranslation()->_('invoice_ids_format_error');
            }
            $ticketsNo[] = $receiptNo;
        }
        // 校验油票的发票编号是否重复
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['rd' => ReimbursementDetailTicketModel::class]);
        $builder->leftjoin(ReimbursementModel::class, 'r.id=rd.re_id', 'r');
        $filterCondition = [
            'status_pending' => 1,
            'status_approval' => 3,
            'pay_status' => [1,2],
            'invoice_no' => $ticketsNo,// 当前选择的出差单id
        ];

        $filterSql = "(r.status = :status_pending: OR (r.status = :status_approval: AND r.pay_status IN ({pay_status:array}))) AND rd.invoice_no IN ({invoice_no:array})";
        $builder->andwhere($filterSql, $filterCondition);
        $builder->columns('r.no,rd.invoice_no');
        $ticketList = $builder->getQuery()->execute()->toArray();
        if (empty($ticketList)) {
            return true;
        }

        // 验证大小写严格匹配
        $ticketInvoiceNoList = array_values(array_column($ticketList,'invoice_no'));
        $ticketNoList = array_column($ticketList,null,'invoice_no');
        foreach ($ticketsNo as $no) {
            if (in_array($no,$ticketInvoiceNoList)) {
                return str_replace('{no}',$ticketNoList[$no]['no'],$this->getTranslation()->_('invoice_ids_is_used'));
            }
        }

        return true;
    }
}
