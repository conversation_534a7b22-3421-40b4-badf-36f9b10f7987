<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\MessageModel;
use FlashExpress\bi\App\Models\backyard\MessageOssFileModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\JobTitleRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\MessageCenterRepository;
use FlashExpress\bi\App\Repository\StaffPayrollCompanyInfoRepository;
use FlashExpress\bi\App\Repository\SysStoreTypeRepository;

class MessageCenterServer extends AuditBaseServer
{
    protected $auditlist;
    public $timezone;
    protected $public;
    protected $message_center_repository;

    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->auditlist  = new AuditlistRepository($lang, $timezone);
        $this->timezone   = $timezone;
        $this->public     = new PublicRepository();
        $this->message_center_repository = new MessageCenterRepository($timezone);
    }

    /**
     * 创建Message审批流
     * @param array  $paramIn
     * @return mixed
     */
    public function createWorkFlow(array $paramIn = [])
    {
        try {
            // [1] 接收参数
            $message_id = $paramIn['message_id'] ?? 0;
            $extend = $paramIn['extend'] ?? [];

            // [2] 参数校验
            // [2.1] 校验message
            $message_data = $this->message_center_repository->getMessage(['id' => $message_id], 'master_db');
            if (empty($message_data)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('message_info_null'));
            }

            if ($message_data['audit_status'] != enums::APPROVAL_STATUS_PENDING) {
                return $this->checkReturn(-3, $this->getTranslation()->_('message audit status error') . " (msg_audit_status: {$message_data['audit_status']})");
            }

            // [2.2] 校验extend
            if (empty($extend) || !in_array($extend['flow_code'], [1, 2, 3])|| !is_array($extend['node_auditor'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('extend_param_error'));
            }

            // [3] 检测审批流是否已创建
            $msg_audit_model = AuditApplyModel::findFirst([
                'conditions' => 'biz_value = :biz_value: AND biz_type = :biz_type:',
                'bind' => ['biz_value' => $message_id, 'biz_type' => AuditListEnums::APPROVAL_TYPE_MESSAGE],
            ]);
            if (!empty($msg_audit_model)) {
                return $this->checkReturn(-3, '该业务审批已创建 [勿重复操作]');
            }

            $this->getDI()->get('db')->begin();

            // 添加消息申请审批: 各节点审批人动态传入
            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($message_id, enums::$audit_type['MSG'], $message_data['add_userid'], null, $extend);
            if (!$requestId) {
                throw new \Exception('创建审批流失败');
            }
            $this->getDI()->get('db')->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('message - createWorkFlow'. $e->getMessage());

            return $this->checkReturn(-3, $e->getMessage());
        }

        return $this->checkReturn(['data' => ['message_id' => $message_id]]);
    }

    protected function getHireTypeList($hire_type_list)
    {
        $t = $this->getTranslation();
        $text = [];
        if (empty($hire_type_list)) {
            return '';
        }
        $hire_type_list = json_decode($hire_type_list, true);
        foreach ($hire_type_list as $item) {
            $text[] = $t->_('hire_type_'.$item);

        }
        return implode(',', $text);
    }

    //发送职位
    protected function getJobTitleListText($job_title_list): string
    {

        $job_title_list = is_json($job_title_list) ? json_decode($job_title_list, true) : $job_title_list;
        if (empty($job_title_list)) {
            return '';
        }

        $jobTitle = JobTitleRepository::getJobTitleByIds(array_values($job_title_list));
        return implode(',', array_column($jobTitle, 'job_name'));
    }
    //网点类型
    protected function getStoreCategoryListText($store_category_list): string
    {
        $store_category_list = is_json($store_category_list) ? json_decode($store_category_list,
            true) : $store_category_list;

        if (empty($store_category_list)) {
            return '';
        }
        $list = [];
        foreach ($this->getStoreCategory() as $store_category) {
            if (in_array($store_category['value'], $store_category_list)) {
                $list[] = $store_category['label'];
            }
        }
        return implode(',', $list);
    }
    //入职日期
    protected function getHireDateListText($hire_date_list): string
    {
        $hire_date_list = is_json($hire_date_list) ? json_decode($hire_date_list,
            true) : $hire_date_list;
        if (empty($hire_date_list)) {
            return '';
        }
        return $hire_date_list[0] . ' ~ ' . $hire_date_list[1];
    }


    public function getStoreCategory(): array
    {
        $headOffice = ['value' => enums::HEAD_OFFICE_ID, 'label' => enums::HEAD_OFFICE];
        $list       = SysStoreTypeRepository::getList();
        return array_merge([$headOffice], $list);
    }

    /**
     * 获取合同公司名称
     * @param $contract_company_ids
     * @return string
     */
    public function getContractCompanyIdsText($contract_company_ids)
    {
        $contract_company_ids = is_json($contract_company_ids) ? json_decode($contract_company_ids, true) : $contract_company_ids;
        if (empty($contract_company_ids)) {
            return '';
        }

        $companyInfo = StaffPayrollCompanyInfoRepository::allData([], 'company_id,company_short_name');
        $companyInfoToId = array_column($companyInfo, 'company_short_name', 'company_id');
        $companyNames = [];
        foreach ($contract_company_ids as $oneId) {
            if(!isset($companyInfoToId[$oneId])) {
                continue;
            }
            $companyNames[] = $companyInfoToId[$oneId] ?? '';
        }
        return implode(',', $companyNames);
    }

    /**
     * 获取职位性质
     * @param $position_types
     * @return string
     */
    public function getPositionTypesText($position_types)
    {
        $position_types = is_json($position_types) ? json_decode($position_types, true) : $position_types;
        if (empty($position_types)) {
            return '';
        }

        $position_types_text = [];
        foreach ($position_types as $value) {
            if(!isset(HrJobDepartmentRelationModel::$position_type_list[$value])) {
                continue;
            }
            $position_types_text[] = $this->getTranslation()->_(HrJobDepartmentRelationModel::$position_type_list[$value]);
        }
        return implode(',', $position_types_text);
    }


    /**
     * 详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        // 获取消息详情
        $result = $this->message_center_repository->getMessage(['id' => $auditId]);
        $t = $this->getTranslation();
        $detailLists = [
            'message_category'  => $t->_('message_category_' . $result['category']),
            'message_title'     => $result['title'] ?? '',
            'message_send_type' => $t->_('click_sending_range'),
        ];

        if ($result['send_type'] == 7) {
            $detailLists['message_send_type'] = $t->_('send_type_' . $result['send_type']) . '<br/>' . $result['to_group'];
        }
        //雇佣类型
        if (!empty($result['hire_type_list'])) {
            $detailLists['hire_type'] = $this->getHireTypeList($result['hire_type_list']);
        }

        //证书消息(位置就在message_send_type 不要乱动)
        if ($result['category'] == 144) {
            $extend                                   = (array)json_decode($result['extend'], true);
            $detailLists['certificate_tpl_url']       = ['value' => empty($extend['certificate_url']) ? [] : [$extend['certificate_url']]];
            $detailLists['certificate_course_name']   = $extend['certificate_course_name'] ?? '';
            $detailLists['certificate_training_time'] = $extend['certificate_training_time'] ?? '';
        }
        $detailLists['message_content']     = $t->_('message_content_hint');
        $detailLists['message_top_setting'] = $t->_('message_top_setting_' . $result['top_status']);
        $detailLists['message_set_time']    = $result['set_time'] < '2021-01-25 00:00:00' ? '' : mb_substr($result['set_time'],
            0, 16);

        if (empty($detailLists['message_set_time'])) {
            unset($detailLists['message_set_time']);
        }
        // 普通消息
        if ($result['category'] == 0) {
            $detailLists['message_by_show_type'] = $t->_('message_by_show_type_'.$result['by_show_type']);
        }

        // 问卷消息
        if ($result['category'] == 6) {
            $detailLists['message_feedback_setting'] = $t->_('message_feedback_setting_'.$result['feedback_setting']);
            $detailLists['message_end_time']         = $result['end_time'] < '2021-01-25 00:00:00' ? '' : mb_substr($result['end_time'],
                0, 16);
        }
        // 普通消息、签字消息 按照模板发送
        // 仅发送人、审批人可见
        if ($result['visible'] == MessageModel::VISIBLE_SEND_APPROVAL &&
            (($result['category'] == MessageEnums::CATEGORY_GENERAL && $result['category_code'] == MessageEnums::CATEGORY_GENERAL_CODE_TEMPLATE) || ($result['category'] == MessageEnums::CATEGORY_SIGN
                    && $result['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE))) {
            $detailLists['view_send_approval_title'] = $t->_('view_send_approval');
        }
        //至少阅读时长
        if(!empty($result['read_duration'])) {
            $detailLists['read_duration'] = $result['read_duration'] . 's';
        }

        if ($result['audit_status'] == enums::$audit_status['dismissed']) {
            $request = AuditApplyModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type:",
                'bind'       => [
                    'type'  => enums::$audit_type['MSG'],
                    'value' => $auditId,
                ],
            ]);

            $detailLists = array_merge(
                $detailLists,
                [
                    'reject_reason1' =>$request ? $request->getRejectReason() : '',
                ]
            );
        }

        $detailLists = $this->format($detailLists);

        // 增加 url key，标识是否跳转到指定页
        foreach ($detailLists as $k => &$v) {
            $v['url']  = '';
            $v['type'] = 0;
            if ($v['key'] == $t->_('message_content')) {
                $v['url'] = '/message-detail?msg_id=' . $auditId;
            }
            //发送范围
            if ($v['key'] == $t->_('message_send_type')) {
                [$v['type'],  $v['data']] = $this->getSendTypeData($result);
            }
        }
        $returnData['data']['detail'] = $detailLists;
        $data = [
            'title'      => $this->auditlist->getAudityType(enums::$audit_type['MSG']),
            'id'         => $result['id'],
            'staff_id'   => $result['add_userid'],
            'type'       => enums::$audit_type['MSG'],
            'created_at' => show_time_zone($result['created_at']),
            'updated_at' => show_time_zone($result['updated_at']),
            'status'     => $result['audit_status'],
            'serial_no'  => 'MSG'.$result['remote_message_id'],
        ];
        $returnData['data']['head'] = $data;

        return $returnData;
    }

    protected function getSendTypeData($result)
    {
        $data = [];
        $returnResult = [0,[]];
        $t = $this->getTranslation();
       
        //发送给指定人
        if ($result['send_type'] == 4) {
            $data[] = ['type'  => '',
                       'key'   => $t->_('send_type_' . $result['send_type']),
                       'value' => $result['staff_info_ids'],
            ];
            return [enums::APPROVAL_DETAIL_TYPE_LIST, $data];
        }

        //按模板发送
        if ($result['send_type'] == 11) {
            //获取模版链接地址
            $messageOssFile = MessageOssFileModel::findFirst([
                'conditions' => 'message_id = :message_id: AND is_del = 0',
                'bind'       => ['message_id' => $result['id']],
            ]);
            if ($messageOssFile) {
                $hcm_rpc = new ApiClient('hcm_rpc', '', 'oss_sign_url','en');
                $hcm_rpc->setParams(
                    [
                        "oss_path" => $messageOssFile->oss_path,
                        "timeout"  => 86400,
                    ]
                );
                $return = $hcm_rpc->execute();
                if (isset($return['error'])) {
                    throw new BusinessException($return['error']);
                }
                $url = $return['result'] ?? null;
            }
            $data[] = ['type' => 'excel', 'key' => $t->_('send_type_' . $result['send_type']), 'value' => $url ?? ''];
            return [enums::APPROVAL_DETAIL_TYPE_LIST, $data];
        }
        //发送给指定组织或员工
        if($result['send_type'] == 9){
            $data[] = ['type'  => '',
                       'key'   => $t->_('send_type_' . $result['send_type']),
                       'value' => $result['to_group'],
            ];
            if (!empty($result['job_title_list'])) {
                $data[] = [
                    'type'  => '',
                    'key'   => $t->_('staff_job_title'),
                    'value' => $this->getJobTitleListText($result['job_title_list']),
                ];
            }
            if (!empty($result['store_category_list'])) {
                $data[] = [
                    'type'  => '',
                    'key'   => $t->_('store_category'),
                    'value' => $this->getStoreCategoryListText($result['store_category_list']),
                ];
            }
            if (!empty($result['hire_date_list'])) {
                $data[] = [
                    'type'  => '',
                    'key'   => $t->_('hire_date'),
                    'value' => $this->getHireDateListText($result['hire_date_list']),
                ];
            }

            if (!empty($result['contract_company_ids'])) {
                $data[] = [
                    'type'  => '',
                    'key'   => $t->_('contract_company'),
                    'value' => $this->getContractCompanyIdsText($result['contract_company_ids']),
                ];
            }

            if (!empty($result['position_types'])) {
                $data[] = [
                    'type'  => '',
                    'key'   => $t->_('position_type'),
                    'value' => $this->getPositionTypesText($result['position_types']),
                ];
            }

            return [enums::APPROVAL_DETAIL_TYPE_LIST, $data];
        }

        return $returnResult;
    }


    public function format($list): array
    {
        $return = [];
        $t      = $this->getTranslation();
        foreach ($list as $key => $v) {
            $return[] = [
                'key'      => $t->_($key) ?? '',
                'key_tips' => is_array($v) && isset($v['key_tips']) ? $v['key_tips'] : null,
                'key_icon' => is_array($v) && isset($v['key_icon']) ? $v['key_icon'] : null,
                'value'    => is_array($v) && isset($v['value']) ? $v['value'] : $v,
                'tips'     => is_array($v) && isset($v['tips']) ? $v['tips'] : null,
                'color'    => is_array($v) && isset($v['color']) ? $v['color'] : null,
            ];
        }
        return $return;
    }
    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(
            false,
            false,
            false,
            false,
            false,
            false);
    }
    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info = $this->message_center_repository->getMessage(['id' => $auditId], 'master_db');
        if (empty($info)) {
            return '';
        }

        $param     = [
            [
                'key'   => 'message_title',
                'value' => $info['title'],
            ],
            [
                'key'   => "message_set_time",
                'value' => $info['set_time'] < '2021-01-25 00:00:00' ? '' : mb_substr($info['set_time'], 0, 16),
            ],
        ];
        return $param ?? "";
    }

    /**
     * 审批完成回调方法
     * @param int $auditId
     * @param int $state
     * @param array $extend
     * @param bool $isFinal
     * @throws \Exception
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        try {
            if ($isFinal) {
                $info = $this->message_center_repository->getMessage(['id' => $auditId]);

                if (empty($info)) {
                    throw new \Exception($this->getTranslation()->_('1015') . '[From 消息审批终态设置]');
                }

                if ($info['audit_status'] == enums::APPROVAL_STATUS_APPROVAL) {
                    throw new \Exception($this->getTranslation()->_('1016') . '[From 消息审批终态设置]');
                }

                if ($info['audit_status'] == enums::APPROVAL_STATUS_REJECTED) {
                    throw new \Exception($this->getTranslation()->_('1016') . '[From 消息审批终态设置]');
                }

                // 终态更新 Message
                if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                    // [1]更新状态 异步处理
                    $params = [
                        'message_id'   => $info['id'],
                        'audit_status' => $state, //2 审核通过 3 驳回
                    ];
                    AuditCallbackServer::createData(AuditListEnums::APPROVAL_TYPE_MESSAGE,$params);

                } else if ($state == enums::APPROVAL_STATUS_REJECTED) {
                    // [1] 更新 message 表审核驳回终态, bi端消息无需分发
                    $update_res = $this->message_center_repository->updateMessageAuditStatus($info['id'], $state);

                    $log = $update_res ? '驳回状态更新成功' : '驳回状态更新失败';
                    $this->getDI()->get("logger")->write_log("setProperty - updateMessageAuditStatus - message_id: {$info['id']}, res = " . $log, 'info');
                }
            }

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('msg - setProperty: '. $e->getMessage());
        }
    }

    /**
     * 审批通过终态rpc同步
     * To bi
     * @param int $message_id
     * @param int $audit_status
     * @param int $exec_count
     * @return mixed
     */
    protected function syncMsgAuditStatus(int $message_id, int $audit_status, int $exec_count = 1)
    {
        $return  = [
            'code' => -1,
            'msg' => '',
        ];

        try {
            $post_param = [
                'message_id' => $message_id,
                'audit_status' => $audit_status,
            ];

            $this->getDI()->get("logger")->write_log("bi_rpc: setProperty - sync_message_audit_status[exec_count = {$exec_count}] - start: post_data: " . json_encode($post_param, JSON_UNESCAPED_UNICODE), 'info');

            $bi_rpc = (new ApiClient('hcm_rpc','','sync_message_audit_status', $this->lang));
            $bi_rpc->setParams($post_param);
            $bi_return = $bi_rpc->execute();

            $this->getDI()->get("logger")->write_log("bi_rpc: setProperty - sync_message_audit_status[exec_count = {$exec_count}] - end: rpc_res: " . json_encode($bi_return, JSON_UNESCAPED_UNICODE), 'info');

            if (isset($bi_return['result']) && $bi_return['result']) {
                $this->getDI()->get('logger')->write_log("by_rpc: sync_message_audit_status[exec_count = {$exec_count}] - end [success]: message_id - " . $message_id, 'info');

                $return['code'] = 1;
                $return['msg'] = 'success';
            } else {
                $this->getDI()->get('logger')->write_log("by_rpc: sync_message_audit_status[exec_count = {$exec_count}] - end [fail]: message_id - " . $message_id);

                $return['code'] = 0;
                $return['msg'] = "sync message audit status failed[{$exec_count}]";

                // 2s后, 重试一次
                if ($exec_count == 1) {
                    sleep(2);
                    $this->syncMsgAuditStatus($message_id, $audit_status, 2);
                }
            }

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('msg - syncMsgAuditStatus: '. $e->getMessage());
        }

        return $return;
    }


    /**
     * 获取审批流所需参数
     * @param $auditId
     * @param $user
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        return [];
    }

    /**
     * 获取消息内容
     * @param int $message_id
     * @return mixed
     */
    public function getMessageContent(int $message_id)
    {
        try {
            // [1] 获取消息详情
            $data = $this->message_center_repository->getMessage(['id' => $message_id]);

            if (empty($data)) {
                return [];
            }

            // [2] 验证消息类型, 问卷/答题消息 - 需获取题库内容
            $lib_data = $this->message_center_repository->getQuestionnaireDetail(['id' => $data['questionnaire_lib_id']]);
            if(!empty($lib_data['qn_lib_content']) && $lib_data['qn_lib_type'] == 1) {//问卷消息,展示问卷库描述
                $lib_data['qn_lib_content'] = json_decode($lib_data['qn_lib_content'],true);
                $lib_data['qn_lib_content']['qn_lib_desc'] = $lib_data['qn_lib_desc'];
                $lib_data['qn_lib_content'] = json_encode($lib_data['qn_lib_content'], JSON_UNESCAPED_UNICODE);
            }

            //消息内容增加模板
            $message_content = preg_replace('/^<div style=\'font-size: 40px\'>(.*)<\/div>$/s','$1',$data['content']);
            $message_content = preg_replace('/^<div style="font-size: 40px">(.*)<\/div>$/s','$1',$message_content);//兼容编辑消息
            if($message_content){
                $message_content = "<div style='font-size: 14px;margin: 0px 10px;word-break: break-all;'>".$message_content."</div>";
            }

            $data['content'] = str_replace('message_content',$message_content,MessageEnums::COMMON_MESSAGE_TEMPLATE);



            return [
                'category' => $data['category'],
                'title' => $data['title'],
                'description' => $data['content'],
                'questionnaire' => $lib_data['qn_lib_content'] ?? '',
                'feedback_setting' => $data['feedback_setting'],
                'end_time' => $data['end_time'] ? mb_substr($data['end_time'], 0, 16) : '',
            ];

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('msg - getMessageContent: '. $e->getMessage());
        }

        return [];
    }

    /**
     * 审批消息
     * @param array $paramIn
     * @return array
     * @throws ValidationException
     * @throws \Exception
     */
    public function update($paramIn = [])
    {
        // [1]传入参数
        $staffId    = $this->processingDefault($paramIn, 'staff_id', 2);
        $message_id = $this->processingDefault($paramIn, 'audit_id', 2);
        $reason     = $this->processingDefault($paramIn, 'reject_reason', 1);
        $state_code = $this->processingDefault($paramIn, 'status', 2);
        $reason     = addcslashes(stripslashes($reason), "'");

        // [2]获取详情
        $info = $this->message_center_repository->getMessage(['id' => $message_id]);
        if (empty($info)) {
            throw new ValidationException($this->getTranslation()->_('1015'));
        }

        if ($info['audit_status'] == enums::APPROVAL_STATUS_APPROVAL) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        if ($info['audit_status'] == enums::APPROVAL_STATUS_REJECTED) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        $server = new ApprovalServer($this->lang, $this->timezone);
        if ($state_code == enums::$audit_status['approved']) {
            // 同意
            $server->approval($message_id, AuditListEnums::APPROVAL_TYPE_MESSAGE, $staffId);
        } else {
            // 驳回
            $server->reject($message_id, AuditListEnums::APPROVAL_TYPE_MESSAGE, $reason, $staffId);
        }

        return $this->checkReturn(['data' => ['id' => $message_id]]);
    }

    /**
     * 获取消息审批日志
     * @param array $paramIn
     * @return mixed
     */
    public function getAuditStream(array $paramIn)
    {
        try {
            // [1] 接收参数
            $message_id = $paramIn['message_id'] ?? 0;

            // [2] 参数校验
            // [2.1] 校验message
            $message_data = $this->message_center_repository->getMessage(['id' => $message_id]);
            if (empty($message_data)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('message_info_null'));
            }

            // 获取审批流日志
            $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail(
                $message_id,
                enums::$audit_type['MSG'],
                $message_data['add_userid'],
                1, null, '', 3);
            if (empty($returnData['data']['stream'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('approver_is_not_exist'));
            }

            return $this->checkReturn(['data' => ['stream' => $returnData['data']['stream']]]);

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('message - getAuditStream'. $e->getMessage());

            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * 消息撤销审批
     * @param array $paramIn
     * @return array
     */
    public function cancel(array $paramIn)
    {
        try {
            // [1] 接收参数
            $message_id = $paramIn['message_id'] ?? 0;

            // [2] 参数校验 只单纯的校验消息是否存在，不校验审批流是否已经创建
            // [2.1] 校验message
            $message_data = $this->message_center_repository->getMessage(['id' => $message_id],'master_db');
            if (empty($message_data)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('message_info_null'));
            }

            $app_server = new ApprovalServer($this->lang, $this->timezone);
            $res = $app_server->cancel($message_id, enums::$audit_type['MSG'], $paramIn['cancel_reason'],  $paramIn['add_userid']);
            return $this->checkReturn(['data' => ['result'=>$res]]);

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('message - getAuditStream'. $e->getMessage());

            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * 异步回调审批结果
     * 发送消息
     * @param $params
     * @return bool
     */
    public function delayCallBack($params): bool
    {
        $client = new ApiClient('hcm_rpc','','sync_message_audit_status', $this->lang);
        $client->setParams($params);
        $result = $client->execute();
        if (isset($result['result']) && $result['result']) {
            return true;
        }
        return false;
    }

}
