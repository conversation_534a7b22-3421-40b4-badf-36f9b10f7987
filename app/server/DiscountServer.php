<?php


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\FreightDiscountModel;
use FlashExpress\bi\App\Models\backyard\SysAttachmentModel;
use FlashExpress\bi\App\Repository\DiscountRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\StaffAuditToolLog;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\library\ApiClient;
use mysql_xdevapi\Exception;

class DiscountServer extends BaseServer
{
    public    $timezone;
    public    $lang;
    protected $public;
    protected $other;
    protected $staff;
    protected $disc;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->timezone = $timezone;
        $this->lang     = $lang;
        parent::__construct($this->lang);

        $this->public = new PublicRepository();
        $this->other  = new OtherRepository();
        $this->staff  = new StaffRepository();
        $this->disc   = new DiscountRepository($this->timezone);
    }

    /**
     * 新增运费折扣申请
     * @param array $paramIn
     * @return mixed
     * @throws \Exception
     */
    public function addFreightDisc($paramIn = [])
    {
        //[1]获取参数
        $submitterId = $this->processingDefault($paramIn, 'staff_id');
        $costomerId = $this->processingDefault($paramIn, 'costomer_id');
        $costomerName = $this->processingDefault($paramIn, 'costomer_name');
        $costomerMobile = $this->processingDefault($paramIn, 'costomer_mobile');
        $costomerType = $this->processingDefault($paramIn, 'costomer_type');
        $costomerCreatedAt = $this->processingDefault($paramIn, 'costomer_created_at');
        $costomerParcelCount = $this->processingDefault($paramIn, 'costomer_parcel_count', 2);
        $costomerEstimateParcelCount = $this->processingDefault($paramIn, 'costomer_estimate_parcel_count');
        $applyRreasonType = $this->processingDefault($paramIn, 'apply_reason_type',3);
        $currentDisc = $this->processingDefault($paramIn, 'current_disc', 2);
        $priceRuleCategory = $this->processingDefault($paramIn, 'price_rule_category', 2);
        $requestDisc = $this->processingDefault($paramIn, 'request_disc', 2);
        $validDays = $this->processingDefault($paramIn, 'valid_days', 2, 0);
        $storeId   = $this->processingDefault($paramIn, 'store_id');
        $priceType = $this->processingDefault($paramIn, 'price_type');
        $remark    = $this->processingDefault($paramIn, 'remark');
        $coupon    = $this->processingDefault($paramIn, 'coupon');
        $filePathArr= $this->processingDefault($paramIn, 'file_path',3);
        $messageQueue = [];

        //[2]校验
        //获取申请营销产品
        $this->RequestCheck($paramIn);

        //获取审批流
        $store = (new SysStoreServer())->getStoreByid($storeId);
        if (isset($store['category']) && in_array($store['category'], [4,5,7])) {
            $workflowRole = 'fd_shop';
        } else {
            $workflowRole = 'fd_dc_sp';
        }
        $auditlistServer = new AuditListServer($this->lang, $this->timezone);
        $workflowServer = new WorkflowServer($this->lang, $this->timezone);
        //$workflow = $workflowServer->doWfRequest($submitterId, $submitterId, $workflowRole, enums::$audit_type['FD']);
        if (empty($workflow)) { //无法获取审批流
            throw new \Exception($this->getTranslation()->_('4008'));
        }

        //计算占比
        $t = $this->getTranslation();
        $proportion = $this->disc->calcProportion($costomerId);
        if (!empty($proportion)) {
            $proportionArr[] = $t->_('discount_detail_7') . $proportion['insured'] * 100 . '%';
            $proportionArr[] = $t->_('discount_detail_8') . $proportion['express'] * 100 . '%';
            $proportionArr[] = $t->_('discount_detail_9') . $proportion['freight'] * 100 . '%';

            $proportionText = implode("<br />", $proportionArr);
        }

        //[3]保存数据
        $insertData = [
            'submitter_id'    => $submitterId,
            'serial_no'       => 'FD' . $this->getRandomId(),
            'costomer_id'     => $costomerId,
            'costomer_name'   => $costomerName,
            'costomer_mobile' => $costomerMobile,
            'costomer_type'   => $costomerType,
            'costomer_created_at' => $costomerCreatedAt,
            'costomer_parcel_count' => $costomerParcelCount,
            'costomer_estimate_parcel_count' => $costomerEstimateParcelCount,
            'price_type'      => $priceType,
            'current_disc'    => $currentDisc,
            'apply_reason_type' => implode(',', $applyRreasonType),
            'proportion'      => $proportionText ?? '',
            'state'           => enums::$audit_status['panding'],
            'remark'          => $remark ?? '',
            'wf_role'         => $workflowRole,
            "channel"         => 0,
        ];
        if (isset($priceRuleCategory)) {
            $insertData = array_merge($insertData, [
                'price_rule_category' => $priceRuleCategory,
                'request_disc'    => $requestDisc,
                'valid_days'      => $validDays,
            ]);
        }

        //[3]组织审批数据
        $this->getDI()->get('db')->begin();
        try {
            //保存申请数据
            $FDId = $this->other->InsertAudit('freight_discount', $insertData);
            if (empty($FDId)) {
                throw new \Exception($this->getTranslation()->_('4008'));
            }

            //保存优惠券
            if (isset($coupon) && $coupon && is_array($coupon)) {
                $insertCoupon = [];
                foreach ($coupon as $v) {
                    $insertCoupon[] = [
                        'pid'               => $FDId,
                        'coupon_type'       => $v['type'],
                        'coupon_days_type'  => $v['days_type'],
                        'coupon_valid_days' => $v['valid_days'],
                        'coupon_name'       => $v['name'],
                        'coupon_num'        => $v['num'],
                        'created_at'        => gmdate("Y-m-d H:i:s", time()),
                    ];
                }
                $this->other->batch_insert('freight_discount_coupon', $insertCoupon);
            }

            //保存上传附件
            if(!empty($filePathArr)) {
                $insertFileData  = [];

                foreach($filePathArr as $file) {
                    $insertFileData[] = [
                        'id'         => $FDId,
                        'image_path' => $file
                    ];
                }
                $this->public->batchInsertImgs($insertFileData, "FREIGHT_DISCOUNT");
            }

            $higherStaff    = $workflow['next']['staff_approval_id'] ?? '';
            if (empty($higherStaff)) {
                throw new \Exception($this->getTranslation()->_('4008'));
            }
            $higherStaffStr = implode(',', $higherStaff);
            $params         = $auditlistServer->generateSummary($FDId, enums::$audit_type['FD']);
            $info           = $this->getDetail(['id' => $FDId]);

            //插入审批人
            $insertApprovalData = [
                'audit_id'      => $FDId,
                'type'          => enums::$audit_type['FD'],
                'level'         => 1,
                'status'        => enums::$audit_status['panding_approval'],  //待审批
                'submitter_id'  => $submitterId,
                'staff_ids'     => $higherStaffStr
            ];
            $this->other->insertApproval($insertApprovalData);

//            $insertApprovalData = [
//                [
//                    'id_union'      => 'fd_' . $FDId,
//                    'staff_id_union'=> $submitterId,
//                    'type_union'    => enums::$audit_type['FD'],
//                    'status_union'  => enums::$audit_list_status['panding'],
//                    'store_id'      => $storeId,
//                    'data'          => json_encode($info),
//                    'table'         => 'freight_discount',
//                    'origin_id'     => $FDId,
//                    'approval_id'   => 0,
//                    'created_at'    => gmdate('Y-m-d H:i:s', time()),
//                    'summary'       => json_encode($params, JSON_UNESCAPED_UNICODE),
//                ],
//                [
//                    'id_union'      => 'fd_' . $FDId,
//                    'staff_id_union'=> $submitterId,
//                    'type_union'    => enums::$audit_type['FD'],
//                    'status_union'  => enums::$audit_list_status['panding_approval'],
//                    'store_id'      => $storeId,
//                    'data'          => json_encode($info),
//                    'table'         => 'freight_discount',
//                    'origin_id'     => $FDId,
//                    'approval_id'   => current($higherStaff),
//                    'created_at'    => gmdate('Y-m-d H:i:s', time()),
//                    'summary'       => json_encode($params, JSON_UNESCAPED_UNICODE),
//                ]
//            ];
//            $this->other->batch_insert('staff_audit_union', $insertApprovalData);

            //追加审批流
            $staffName = $this->public->getStaffName($submitterId);
            $log['staff_id']      = $submitterId;
            $log['type']          = enums::$audit_type['FD'];
            $log['original_type'] = 1;
            $log['original_id']   = $FDId;
            $log['operator']      = $submitterId;
            $log['operator_name'] = $staffName;
            $log['to_status_type']= 1;

            $log_model = new StaffAuditToolLog();
            $log_model->add_log($log,'Discount:addFreight');

            //异步push消息=>审批人通知
            $lang = (new StaffServer())->getLanguage(current($higherStaff));

            $message_title  = $this->getTranslation($lang)->_('6006');
            $audit_type     = $this->getTranslation($lang)->_('6011');
            $staffData      = $this->staff->checkoutStaff($submitterId);

            $pushParam = [
                'staff_info_id' => current($higherStaff),
                'message_title' => $message_title,
                'userinfo'      => $staffData,
                'lastInsert_id' => $FDId,
                'audit_type'    => $audit_type,
                'lang'    => $lang,
            ];
            array_push($messageQueue, $pushParam);

        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log("add discount failure:" . $e->getMessage(), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        $this->getDI()->get('db')->commit();

        //[4]发送push
        if (!empty($messageQueue)) {
            foreach ($messageQueue as $message) {
                $this->public->pushMessage($message);
            }
        }
        return $this->checkReturn([]);
    }

    /**
     * 审批申请运费折扣
     * @param array $paramIn
     * @return mixed
     * @throws \Exception
     */
    public function updateFreightDisc($paramIn = [])
    {
        //[1]获取参数
        $staffId   = $this->processingDefault($paramIn, 'staff_id', 2);
        $staffName = $this->processingDefault($paramIn, 'staff_name', 1);
        $status    = $this->processingDefault($paramIn, 'status', 2);
        $reason    = $this->processingDefault($paramIn, 'reject_reason', 1);
        $auditId   = $this->processingDefault($paramIn, 'audit_id', 2);
        $reason    = addcslashes(stripslashes($reason),"'");
        $server    = new AuditListServer($this->lang, $this->timezone);

        //获取详情
        $fdInfo    = $this->getDetail(['id'=>$auditId]);
        if (empty($fdInfo)) {
            throw new \Exception($this->getTranslation()->_('4008'));
        }

        //已经是最终状态,不能撤销
        if ($fdInfo['state'] != enums::$audit_status['panding'] && $status == 4) {
            throw new \Exception($this->getTranslation()->_('4008'));
        }

        //验证是否已经审批
        $approvalStatus = $server->getApprovalAuditStatus([
            'id'            => 'fd_'. $auditId,
            'approval_id'   => $staffId,
            'type'          => enums::$audit_type['FD']
        ]);

        //非撤销状态要验证审批状态
        if ($approvalStatus != enums::$audit_list_status['panding_approval'] && $status != enums::$audit_status['revoked']) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1016')));
        }

        $paramData = [
            'id'            => $fdInfo['id'],
            'state'         => $status,
            'reject_reason' => $reason,
        ];

        //获取审批流
        $workflowServer = new WorkflowServer($this->lang, $this->timezone);
        //$workflow = $workflowServer->doWfRequest($fdInfo['submitter_id'], $staffId, $fdInfo['wf_role'], enums::$audit_type['FD']);
        if (empty($workflow)) { //无法获取审批流
            throw new \Exception($this->getTranslation()->_('4008'));
        }
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        //同意或者驳回等分开处理
        if ($status == enums::$audit_status['approved']) { //审批同意

            //判断是否是最终审批人
            if ($workflow['current']['level'] == ($workflow['total_level'] - 1)) { //是审批人且最终审批

                //staff_audit_tool_log
                $log['staff_id'] = $fdInfo['submitter_id'];
                $log['type'] = enums::$audit_type['FD'];
                $log['original_type'] = $fdInfo['state'] ?? 0;
                $log['to_status_type'] = $status ?? 0;
                $log['original_id'] = $auditId;
                $log['operator'] = $staffId;
                $log['operator_name'] = $staffName;

                $nowTime = time();
                $nowTimeTH = gmdate('Y-m-d 01:00:00', $nowTime + $add_hour * 3600);
                $endTimeTH = gmdate('Y-m-d 01:00:00', $nowTime + $add_hour * 3600 + 86400 * $fdInfo['valid_days']);

                $paramData = array_merge($paramData, [
                    'disc_start_date' => $nowTimeTH,
                    'disc_end_date'   => $endTimeTH,
                ]);

                //操作记录需要的数据
                $this->other->lastApproval($log, $paramData ,'freight_discount');

                if (isset($fdInfo['coupon']) && $fdInfo['coupon'] && is_array($fdInfo['coupon'])) {
                    foreach ($fdInfo['coupon'] as $v) {
                        $this->getDI()->get('db')->updateAsDict('freight_discount_coupon', [
                            'coupon_start_date' => gmdate('Y-m-d', $nowTime),
                            'coupon_end_date'   => gmdate('Y-m-d', $nowTime + 86400 * $v['coupon_valid_days']),
                        ], "pid = {$fdInfo['id']} and coupon_type = {$v['coupon_type']}");
                    }
                }

                try {
                    //跟MS同步
                    $logger = $this->getDI()->get('logger');
                    if ($fdInfo['price_rule_category'] != 0) { //存在折扣申请
                        $params['discount_apply_list'][] = [
                            "related_id"        => (int) $fdInfo['id'],
                            "client_id"         => $fdInfo['costomer_id'],
                            "customer_type_category" => (int) $fdInfo['costomer_type'],
                            "customer_name"     => $fdInfo['costomer_name'],
                            "customer_mobile"   => $fdInfo['costomer_mobile'],
                            "price_type"        => (int) $fdInfo['price_type'],
                            "current_disc"      => in_array($fdInfo['current_disc'], [0 , -1, ""]) ? null : (float) $fdInfo['current_disc'],
                            "request_disc"      => in_array($fdInfo['request_disc'], [0 , -1, ""]) ? null : (float) $fdInfo['request_disc'],
                            "current_disc_str"      => (string) (in_array($fdInfo['current_disc'], [0, -1, ""]) ? 0 : $fdInfo['current_disc']),
                            "request_disc_str"      => (string) (in_array($fdInfo['request_disc'], [0, -1, ""]) ? 0 : $fdInfo['request_disc']),
                            "price_rule_category" => (int) $fdInfo['price_rule_category'],
                            "disc_start_date"   => strtotime($nowTimeTH),
                            "disc_end_date"     => strtotime($endTimeTH),
                            "valid_dates"       => (int) $fdInfo['valid_days'],
                            "channel"           => (int) $fdInfo['channel'],
                        ];
                        $fle_rpc = (new ApiClient('fle','com.flashexpress.fle.svc.api.DiscountApplySvc','saveDiscountApplyV2', $this->lang));
                        $fle_rpc->setParams($params);
                        $fle_return = $fle_rpc->execute();
                        if (isset($fle_return) && $fle_return['result'] == 1) {
                            //设置为已同步
                            $this->getDI()->get('db')->updateAsDict('freight_discount', [
                                'sync_state' => 2,
                            ], "id = {$fdInfo['id']}");
                            $logger->write_log('request:' . json_encode($params) . ":response:" . json_encode($fle_return), 'info');
                        } else {
                            $logger->write_log('request:' . json_encode($params) . ":response:" . json_encode($fle_return), 'notice');
                        }
                    }

                    if (isset($fdInfo['coupon']) && $fdInfo['coupon'] && is_array($fdInfo['coupon'])) {
                        $paramCoupon = [];
                        foreach ($fdInfo['coupon'] as $coupon) {
                            $coupon['coupon_validity_time'] = $coupon['coupon_days_type'];
                            $coupon['coupon_category'] = $coupon['coupon_type'];
                            $coupon['code_number'] = $coupon['coupon_num'];

                            $paramCoupon[] = $coupon;
                        }

                        $syncParams = [
                            'customer_id' => $fdInfo['costomer_id'],
                            'customer_type' => $fdInfo['costomer_type'],
                            'apply_key' => $fdInfo['serial_no'],
                            'coupon_apply'  => $paramCoupon
                        ];

                        //跟Coupon同步
                        $lang = $this->lang == 'zh-CN' ? 'zh' : $this->lang;
                        $rpc = (new ApiClient('coupon_rpc','','backyardApplyCoupon', $lang));
                        $rpc->setParams($syncParams);
                        $data = $rpc->execute();

                        if (!isset($data['result']['code']) || $data['result']['code'] != 1) {
                            $logger->write_log("coupon rpc error request:" . json_encode($syncParams) . ", response:" . json_encode($data) , 'error');
                        } else {
                            //设置为已同步
                            $this->getDI()->get('db')->updateAsDict('freight_discount', [
                                'coupon_sync_state' => 2,
                            ], "id = {$fdInfo['id']}");
                        }
                    }


                } catch (\Exception $e) {
                    $this->getDI()->get('logger')->write_log("sync discount failure,reason :" . $e->getMessage());
                }

                return $this->checkReturn(['data'=>['audit_id'=>$auditId]]);
            } else { //是审批人但非最终审批
                $nextApproval = $workflow['next']['staff_approval_id'];
                $nextApproval = current($nextApproval);
                $nextApprovalArr = explode(',',$nextApproval);
                if (empty($nextApproval)) {
                    throw new \Exception($this->getTranslation()->_('4008'));
                }

                $union   = [];
                $summary = $server->generateSummary($auditId, enums::$audit_type['FD']);
                foreach ($nextApprovalArr as $finalApprover) {
                    $union[] = [
                        'id_union'      => 'fd_' . $auditId,
                        'staff_id_union'=> $fdInfo['submitter_id'],
                        'type_union'    => enums::$audit_type['FD'],
                        'status_union'  => enums::$audit_list_status['panding_approval'],
                        'store_id'      => '',
                        'data'          => json_encode($fdInfo),
                        'summary'       => json_encode($summary),
                        'table'         => 'freight_discount',
                        'origin_id'     => $auditId,
                        'approval_id'   => $finalApprover,
                        'created_at'    => gmdate("Y-m-d H:i:s", time()),
                    ];
                }

                //staff_audit_approval数据
                $approvalInster['type']  = enums::$audit_type['FD'];
                $approvalInster['level'] = $workflow['next']['level'];
                $approvalInster['audit_id'] = $auditId;
                $approvalInster['status'] = 7;
                $approvalInster['submitter_id'] = $fdInfo['submitter_id'];
                $approvalInster['staff_ids'] = $nextApproval;

                //日志数据
                $log['staff_id'] = $fdInfo['submitter_id'];
                $log['type'] = enums::$audit_type['FD'];
                $log['original_type'] = 1;
                $log['to_status_type'] = 2;
                $log['original_id'] = $auditId;
                $log['operator'] = $staffId;
                $log['operator_name'] = $staffName;
                $this->other->insertUnionApprovalLog($union, $approvalInster, $log);
                return $this->checkReturn(['data'=>['audit_id'=>$auditId]]);
            }
        } else { //撤销、驳回

            $log['staff_id'] = $fdInfo['submitter_id'];
            $log['type'] = enums::$audit_type['FD'];
            $log['original_type'] = $fdInfo['state'] ?? 1;
            $log['to_status_type'] = $status ?? 1;
            $log['original_id'] = $fdInfo['id'];
            $log['operator'] = $staffId;
            $log['operator_name'] = $staffName;
            $this->other->lastApproval($log, $paramData ,'freight_discount');

            return $this->checkReturn(['data'=>['audit_id'=>$auditId]]);
        }
    }

    /**
     * 获取申请运费详情
     * @param array $paramIn
     * @return array|false
     */
    public function getDetail($paramIn = [])
    {
        //[1]获取参数
        $auditId = $this->processingDefault($paramIn, 'id');

        //[2]获取详情
        $detailInfo = $this->disc->getDetail($auditId);
        if (empty($detailInfo)) {
            return false;
        }

        //申请营销产品
        $t = $this->getTranslation();
        $request = [];
        if ($detailInfo['price_rule_category'] != 0) {
            $request[] = ( $t->_('discount_type_' . $detailInfo['price_rule_category'])  ?? '') . $detailInfo['request_disc'] . '%';
        }
        if (isset($detailInfo['coupon']) && $detailInfo['coupon'] && is_array($detailInfo['coupon'])) {
            foreach ($detailInfo['coupon'] as $v) {
                $request[] = $v['coupon_name'] . "({$v['coupon_num']})";
            }
        }
        $detailInfo['request_text'] = empty($request) ? '': implode("<br />", $request);

        //申请原因转义
        if (isset($detailInfo['apply_reason_type']) && $detailInfo['apply_reason_type']) {
            $applyReasonTypeArr = explode(',', $detailInfo['apply_reason_type']);

            $reasons = [];
            foreach ($applyReasonTypeArr as $type) {
                if ($type == 6) { //其他原因
                    $reasons[] = ($this->getTranslation()->_('disc_reason_' . $type) ?? '') . $detailInfo['remark'] ?? '';
                } else {
                    $reasons[] = $this->getTranslation()->_('disc_reason_' . $type) ?? '';
                }
            }
        }
        $detailInfo['apply_reason_type_text'] = isset($reasons) && $reasons ? implode("<br/>", $reasons) : '';

        //价格类型转换
        $detailInfo['price_type_text'] = $this->getTranslation()->_('price_type_' . $detailInfo['price_type']) ?? '';

        //有效期限
        $validMonths = $detailInfo['valid_days'] / 30;
        $detailInfo['valid_month'] = $this->getTranslation()->_('month_period_' . $validMonths);

        //添加用户的相关信息
        $staff = $this->staff->getStaffInfo($detailInfo['submitter_id']);
        $detailInfo['store_name'] = $staff['store_name'];
        $detailInfo['manage_region_name'] = $staff['manage_region_name'];
        $detailInfo['manage_piece_name'] = $staff['manage_piece_name'];
        return $detailInfo;
    }

    /**
     * 已经申请营销产品
     * @param $costomer_id
     * @return string
     */
    public function getHasRequestDisc($costomer_id)
    {
        $detailInfo = $this->disc->getCustomerAllRequest($costomer_id);
        if (empty($detailInfo)) {
            return '';
        }

        $request = [];
        $t = $this->getTranslation();
        foreach ($detailInfo as $value) {
            if ($value['price_rule_category'] != 0) {
                $key = $value['price_rule_category'] . '-' . $value['request_disc'] . '-' . $value['valid_days'];
                $request[$key] = ( $t->_('discount_type_' . $value['price_rule_category'])  ?? '') . $value['request_disc'] . "%({$value['valid_days']} days)";
            }

            if ($value['request_cod_poundage_rate_str']) {
                $request[] =  $t->_('cod_rate') . $value['request_cod_poundage_rate_str'] . "%({$value['valid_days']} days)";
            }

            if ($value['request_return_discount_rate']) {
                $request[] =  $t->_('return_rate') . $value['request_return_discount_rate'] . "%({$value['valid_days']} days)";
            }

            if ($value['request_credit_term']) {
                $request[] =  $t->_('credit_period') . $value['request_credit_term'] . " days";
            }

            if (isset($value['coupon_name']) && $value['coupon_name']) {
                $request[] = $value['coupon_name'] . "({$value['coupon_num']})" ?? '';
            }
            $value['request_text'] = empty($request) ? '': implode("<br />", $request);
        }

        return !empty($request) ? implode('<br />', $request) : '';
    }

    /**
     * 校验折扣、优惠券申请
     * @param array $paramIn
     * @return bool|void
     */
    public function RequestCheck($paramIn = [])
    {
        $discount   = $paramIn['price_rule_category'] ?? [];
        $coupon     = $paramIn['coupon'] ?? [];
        $couponArr  = !empty($coupon) ? array_column($coupon, 'type') : [];

        //获取待审批 & 审批已同意的数据
        $auditInfo = $this->disc->getCustomerAllRequest($paramIn['costomer_id']);
        if (empty($auditInfo)) {
            return true;
        }

        foreach ($auditInfo as $value) {
            //[2.1]校验是否存在待审批
            //校验折扣申请
            if ($value['price_rule_category'] != 0 && $discount != 0) {

                if ($value['state'] == enums::$audit_status['panding']) {
                    $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_panding')));
                }
                //产品需求，去掉有效期的判断
                //https://l8bx01gcjr.feishu.cn/docs/doccnVtrMWMxhm8jJpZrQkjVgoc#
                /*else {

                    //[2.2]校验是否存在生效中的折扣
                    if (isset($value['disc_start_date']) && isset($value['disc_end_date']) && strtotime($value['disc_start_date']) <= $nowTime && strtotime($value['disc_end_date']) > $nowTime) {

                        return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_exist_discount')));
                    }
                }*/
            }

            if ($value['state'] == enums::$audit_status['panding'] && in_array($value['coupon_type'], $couponArr)) {
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_exist_coupon')));
            }
        }

        //校验优惠券
        $customerId = $auditInfo[0]['costomer_id'] ?? '';
        $customerType = $auditInfo[0]['costomer_type'] ?? '';
        $couponCategory = array_values(array_filter(array_unique(array_column($auditInfo, 'coupon_type'))));

        if (!empty($customerId) && !empty($customerType) && !empty($couponCategory)) {
            //校验优惠券
            $lang = $this->lang == 'zh-CN' ? 'zh' : $this->lang;
            $rpc = (new ApiClient('coupon_rpc','','userCategoryValidEnabled', $lang));
            $rpc->setParams([
                "customer_id"   => $customerId,
                "customer_type" => $customerType,
                "coupon_category"=> $couponCategory
            ]);
            $data = $rpc->execute();

            if (isset($data['result']['code']) && $data['result']['code'] == 1) {
                if (isset($data['result']['data']['valid']) && $data['result']['data']['valid'] && array_intersect($couponArr, $data['result']['data']['valid'])) {
                    $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_exist_coupon')));
                }
            }
        }
    }

    /**
     * 校验折扣、优惠券申请
     * @param array $paramIn
     * @return bool|void
     */
    public function ShopRequestCheck($paramIn = [])
    {
        $discount   = $paramIn['price_rule_category'] ?? [];
        $coupon     = $paramIn['coupon'] ?? [];
        $codRate    = $paramIn['request_cod_poundage_rate_str'] ?? null;
        $returnRate = $paramIn['request_return_discount_rate'] ?? null;
        $credit    = $paramIn['request_credit_term'] ?? null;

        $couponArr  = !empty($coupon) ? array_column($coupon, 'type') : [];

        //获取待审批 & 审批已同意的数据
        $auditInfo = $this->disc->getCustomerAllRequest($paramIn['costomer_id']);
        if (empty($auditInfo)) {
            return true;
        }
        foreach ($auditInfo as $value) {
            //[2.1]校验是否存在待审批
            //校验折扣申请
            if ($value['price_rule_category'] != 0 && $discount != 0 ||
                $value['request_cod_poundage_rate_str'] != 0 && !empty($codRate) ||
                $value['request_return_discount_rate'] != 0 && !empty($returnRate) ||
                $value['request_credit_term'] != 0 && !empty($credit)
            ) {

                if ($value['state'] == enums::$audit_status['panding']) {
                    $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_panding')));
                }
            }

            if ($value['state'] == enums::$audit_status['panding'] && in_array($value['coupon_type'], $couponArr)) {
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_exist_coupon')));
            }
        }

        //校验优惠券
        $customerId = $auditInfo[0]['costomer_id'] ?? '';
        $customerType = $auditInfo[0]['costomer_type'] ?? '';
        $couponCategory = array_values(array_filter(array_unique(array_column($auditInfo, 'coupon_type'))));

        if (!empty($customerId) && !empty($customerType) && !empty($couponCategory)) {
            //校验优惠券
            $lang = $this->lang == 'zh-CN' ? 'zh' : $this->lang;
            $rpc = (new ApiClient('coupon_rpc','','userCategoryValidEnabled', $lang));
            $rpc->setParams([
                "customer_id"   => $customerId,
                "customer_type" => $customerType,
                "coupon_category"=> $couponCategory
            ]);
            $data = $rpc->execute();

            if (isset($data['result']['code']) && $data['result']['code'] == 1) {
                if (isset($data['result']['data']['valid']) && $data['result']['data']['valid'] && array_intersect($couponArr, $data['result']['data']['valid'])) {
                    $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_exist_coupon')));
                }
            }
        }
    }

    /**
     * 获取同步失败的折扣列表
     */
    public function getSyncFailureDiscList()
    {
        return $this->disc->getSyncFailureDiscList();
    }

    /**
     * 获取优惠券配置信息
     * @param array $paramIn
     * @return mixed
     */
    public function getCouponInit($paramIn = [])
    {
        $clientId = $paramIn['client_id'] ?? '';
        $type     = $paramIn['type'] ?? 1;

        //获取优惠券配置信息
        $lang = $this->lang == 'zh-CN' ? 'zh' : $this->lang;
        $rpc = (new ApiClient('coupon_rpc','','couponInit', $lang));
        $rpc->setParams(['src' => 'backyard']);
        $data = $rpc->execute();
        if (isset($data['result']['code']) && $data['result']['code'] == 1) {
            $return['coupon'] = $data['result']['data'] ?? [];
        } else {
            $return['coupon'] = [];
        }

        //获取前30天每日的发件量
        try {
            $parcelCount = $this->disc->getPerDayParcelCount($clientId);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('err:' . $e->getMessage() . $e->getTraceAsString(), 'info');
            $parcelCount = 0;
        }
        $return['parcel_count'] = $parcelCount ?? 0;

        //获取申请理由
        $return['reason'] = $this->getRequestReason($type);

        //获取折扣配置信息
        $return['discount'] = $this->getDiscount();

        return $return;
    }

    /**
     * 获取申请理由
     * @param $type
     * @return array
     */
    public function getRequestReason($type): array
    {
        $t = $this->getTranslation();
        if ($type == 1) { //Network部门原因
            return [
                [
                    "code"  => 1,
                    "value" => $t->_('disc_reason_1')
                ],
                [
                    "code"  => 2,
                    "value" => $t->_('disc_reason_2')
                ],
                [
                    "code"  => 3,
                    "value" => $t->_('disc_reason_3')
                ],
                [
                    "code"  => 4,
                    "value" => $t->_('disc_reason_4')
                ],
                [
                    "code"  => 5,
                    "value" => $t->_('disc_reason_5')
                ],
                [
                    "code"  => 6,
                    "value" => $t->_('disc_reason_6')
                ]
            ];
        } else { //Shop部门、Sales/Project Management
            return [
                [
                    "code"  => 7,
                    "value" => $t->_('disc_reason_7')
                ],
                [
                    "code"  => 8,
                    "value" => $t->_('disc_reason_8')
                ],
                [
                    "code"  => 9,
                    "value" => $t->_('disc_reason_9')
                ],
                [
                    "code"  => 10,
                    "value" => $t->_('disc_reason_10')
                ],
                [
                    "code"  => 11,
                    "value" => $t->_('disc_reason_11')
                ],
                //[
                //    "code"  => 6,
                //    "value" => $t->_('disc_reason_6')
                //]
            ];
        }

    }

    /**
     * 获取折扣
     */
    public function getDiscount()
    {
        $t = $this->getTranslation();
        return [
            [
                "code"      => 1,
                "desc"      => $t->_('discount_type_1'),
                "discount"  => [
                    ["key"   => "5%", "value" => 5],
                    ["key"   => "10%", "value" => 10],
                    ["key"   => "15%", "value" => 15],
                    ["key"   => "20%", "value" => 20],
                    ["key"   => "25%", "value" => 25],
                    ["key"   => "30%", "value" => 30],
                ],
                "valid_days"=> [
                    ["key"   => $t->_('one_month'), "value" => 30],
                    ["key"   => $t->_('two_month'), "value" => 60],
                    ["key"   => $t->_('thr_month'), "value" => 90],
                ],
            ],
            [
                "code"      => 2,
                "desc"      => $t->_('discount_type_2'),
                "discount"  => [
                    ["key"   => "5%", "value" => 5],
                    ["key"   => "10%", "value" => 10],
                    ["key"   => "15%", "value" => 15],
                ],
                "valid_days"=> [
                    ["key"   => $t->_('one_month'), "value" => 30],
                    ["key"   => $t->_('two_month'), "value" => 60],
                    ["key"   => $t->_('thr_month'), "value" => 90],
                ],
            ],
        ];
    }

    /**
     * 查询用户列表
     * @param $staffId
     * @param $condition
     * @return array|mixed|void
     */
    public function getCustomList($staffId, $condition)
    {
        $fle_rpc = (new ApiClient('fle','com.flashexpress.fle.svc.api.DiscountApplySvc','getDiscountApplyBy', $this->lang));
        $fle_rpc->setParams($staffId);
        $fle_rpc->setParams($condition);
        $fle_return = $fle_rpc->execute();
        if (isset($fle_return['result']) && empty($fle_return['result'])) { //没有查到客户信息
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_no_costomer')));
        }

        return $fle_return['result'] ?? [];
    }

    /**
     * 获取即将过期的折扣数据
     * @param array $type   折扣类型数组
     * @return array
     */
    public function getDiscountWillExpire(array $type): array
    {
        if (empty($type)) {
            return [];
        }

        //到期日剩余7天定义：假设折扣到期日为T，开始发送消息时间为T-6（eg：折扣到期日为：2021-04-07，开始发送消息时间为：2021-04-01）
        //需求链接 https://l8bx01gcjr.feishu.cn/docs/doccn0YZWwlrHkiIBfFxHpNIrjb
        return $this->disc->getDiscountWillExpire($type);
    }

    /**
     * 获取折扣类型与折扣值
     * @param string $item 折扣类型-折扣值
     */
    public function parseDiscCategoryAndValue($item)
    {
        if (!is_string($item)) {
            return [];
        }
        $itemsArr = explode('-', $item);

        return [
            $this->getTranslation()->_('discount_type_' . $itemsArr[0]) ?? "",
            (isset($itemsArr[1]) && $itemsArr[1] ? $itemsArr[1] . '%,' : '')
        ];
    }


    /**
     * 获取附件 及 校验权限
     * @param $paramIn
     * @return array
     * @throws \Exception
     */
    public function getFileDetailForEndSale($paramIn)
    {
        $customer_id = $this->processingDefault($paramIn, 'customer_id',1);
        $staff_info_id = $this->processingDefault($paramIn, 'staff_info_id');
        $sys_store_id = $this->processingDefault($paramIn, 'organization_id');

        try {

            $params=[
                "staff_info_id" => intval($staff_info_id),
                "key_word" => $customer_id,
                "store_id"=> strval($sys_store_id),
                "source"=> 'BY',
            ];

            //请求客户列表
            $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.DiscountApplySvc','checkDiscountApplyUser', $this->lang);
            $fle_rpc->setParams($params);
            $fle_return = $fle_rpc->execute();

            $this->getDI()->get('logger')->write_log('getFileDetailForEndSale fle_return: ' . json_encode($fle_return, JSON_UNESCAPED_UNICODE), 'info');

            if(isset($fle_return['error'])){
                throw new \Exception($fle_return['error']);
            }

            //获取当前客户上传的图片
            $file_path = [];
            $fd_model = FreightDiscountModel::findFirst(
                [
                    'conditions'=>'costomer_id =:customer_id: and channel =:channel:',
                    'bind'=>['customer_id'=>$customer_id,'channel'=>FreightDiscountModel::CHANNEL_YES],
                    'order'=>'id desc',
                ]
            );
            if($fd_model){
                $sa_model =SysAttachmentModel::find([
                    'conditions'=>"oss_bucket_key =:fd_id: and oss_bucket_type = 'FREIGHT_DISCOUNT_YES' and deleted = 0",
                    'bind'=>['fd_id'=>$fd_model->id]
                  ]);
                if($sa_model){
                    $files = $sa_model->toArray();
                    foreach ($files as $image) {
                        array_push($file_path, convertImgUrl($image['bucket_name'], $image['object_key']));
                    }
                }
            }
            return $file_path;
        }catch (\Exception $e){
            throw $e;
        }
    }

    /**
     * 保存数据
     * @param $paramIn
     * @return array|bool
     * @throws \Exception
     */
    public function addFreightDiscountForEndSale($paramIn)
    {
        $staff_info_id = $this->processingDefault($paramIn, 'staff_info_id');
        $customer_id = $this->processingDefault($paramIn, 'customer_id',1);
        $organization_id = $this->processingDefault($paramIn, 'organization_id',1);
        $filePathArr = $this->processingDefault($paramIn, 'file_path',3);
        $gradient_parcel_discount_detail = $this->processingDefault($paramIn, 'gradient_parcel_discount_detail',3);
        //[1]组织数据
        $this->getDI()->get('db')->begin();
        try {
            $insertDiscount = [
                'submitter_id' => $staff_info_id,
                'costomer_id'  => $customer_id,
                'state'        => 0,
                'sync_state'   => 0,
                'coupon_sync_state'=> 0,
                'is_valid'     => 1,
                'channel'      => FreightDiscountModel::CHANNEL_YES,
            ];
            //保存申请数据
            $FDId = $this->other->InsertAudit('freight_discount', $insertDiscount);
            if (empty($FDId)) {
                throw new \Exception($this->getTranslation()->_('4008'));
            }

            //保存上传附件
            if(!empty($filePathArr)) {
                $insertFileData  = [];
                foreach($filePathArr as $file) {
                    $insertFileData[] = [
                        'id'         => $FDId,
                        'image_path' => $file
                    ];
                }
                $this->public->batchInsertImgs($insertFileData, "FREIGHT_DISCOUNT_YES");//年底促销
            }

            $params=[
                "staff_info_id" => intval($staff_info_id),
                "store_id"      => strval($organization_id),
                "client_id"     => $customer_id,
                "gradient_parcel_discount_detail" => $gradient_parcel_discount_detail,
            ];
            //保存折扣设置
            $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.DiscountApplySvc','createGradientParcelDiscount', $this->lang);
            $fle_rpc->setParams($params);
            $fle_return = $fle_rpc->execute();

           $this->getDI()->get('logger')->write_log('createConfigForEndSale fle_return: ' . json_encode($fle_return, JSON_UNESCAPED_UNICODE), 'info');

           
            if(isset($fle_return['error'])){
                throw new \Exception($fle_return['error']);
            }

            $this->getDI()->get('db')->commit();

            return  true;
        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log("add year end sale discount failure:" . $e->getMessage(), 'error');
           throw $e;
        }
    }


}