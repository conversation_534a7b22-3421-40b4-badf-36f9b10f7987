<?php
/**
 * Author: Bruce
 * Date  : 2025-03-25 23:16
 * Description:
 */

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\Exception\ValidationException;

class SettlementIcServer extends BaseServer
{
    public $timezone;

    const SECRET_KEY = 'flash_period';

    public function __construct($lang = 'en', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 生成签名
     * @param $paramIn
     * @return string
     */
    public function createAuth($paramIn)
    {
        //[1]获取参数
        $staff_info_id = $this->processingDefault($paramIn, 'staff_info_id', 2);
        $period_id     = $this->processingDefault($paramIn, 'period_id', 2);
        $time          = time();
        $auth          = md5(self::SECRET_KEY . $staff_info_id . $period_id . $time);

        $params = '?staff_info_id=' . $staff_info_id . '&quitclaim_id=' . $period_id . '&time=' . $time . '&auth=' . $auth;

        return $params;
    }

    /**
     * 验签
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function checkAuth($paramIn)
    {
        //[1]获取参数
        $staff_info_id = $this->processingDefault($paramIn, 'staff_info_id', 2);
        $period_id     = $this->processingDefault($paramIn, 'period_id', 2);
        $time          = $this->processingDefault($paramIn, 'time', 2);
        $auth          = $this->processingDefault($paramIn, 'auth', 1);

        $checkAuth = md5(self::SECRET_KEY . $staff_info_id . $period_id . $time);

        if ($auth != $checkAuth) {
            throw new ValidationException($this->getTranslation()->_('identity_info_error'));
        }

        //3个月有效期
        $currentDate    = date('Y-m-d');
        $date           = date('Y-m-d', $time);
        $expirationDate = date('Y-m-d', strtotime("{$date}+3 Months"));

        if ($currentDate > $expirationDate) {
            throw new ValidationException($this->getTranslation()->_('identity_failure'));
        }
        return self::checkReturn(1);
    }

    /**
     * 提交签名
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function submitSettlementIc($params)
    {
        $payrollFileServer = new PayrollFileServer($this->lang, $this->timezone);
        $period['staff_info_id'] = $params['staff_info_id'];
        $period['period_id']     = $params['period_id'];
        $periodInfo = $payrollFileServer->getIcPeriodInfoFromFbi($period);
        if (empty($periodInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        if (!empty($periodInfo['signature_pic'])) {
            throw new ValidationException($this->getTranslation()->_('accident_report_influence_scope_3'));
        }

        $data['id']            = $params['period_id'];
        $data['staff_info_id'] = $params['staff_info_id'];
        $data['url']           = $params['url'];
        return $payrollFileServer->callBackSignUrlToFbi($data);
    }

    /**
     * 获取结算信息
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getDetail($params)
    {
        $data['staff_info_id'] = $params['staff_info_id'];
        $data['period_id']     = $params['period_id'];
        $periodInfo            = (new PayrollFileServer($this->lang, $this->timezone))->getIcPeriodInfoFromFbi($data);
        if (empty($periodInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        return $periodInfo;
    }

}