<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditCCModel;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\PublicRepository;

class PushServer extends BaseServer
{
    /**
     * @var null
     */
    private static $instance;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * @param string $lang
     * @param string $timezone
     * @return PushServer
     */
    public static function getInstance($lang = 'zh-CN', $timezone='+08:00'): PushServer
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self($lang, $timezone);
        }
        return self::$instance;
    }

    /**
     * @description 发送抄送【在列表中追加数据，并发送Push提醒】
     * @param AuditApplyModel $request
     * @param $ccStaffIdList
     * @return bool
     */
    public function sendCc(AuditApplyModel $request, $ccStaffIdList): bool
    {
        if (empty($request) || empty($ccStaffIdList)) {
            return false;
        }

        //循环发送抄送 和 push
        foreach ($ccStaffIdList as $toStaffId) {
            $auditCcModel               = new AuditCCModel();
            $auditCcModel->biz_type     = $request->getBizType();
            $auditCcModel->biz_value    = $request->getBizValue();
            $auditCcModel->flow_node_id = $request->getCurrentFlowNodeId();
            $auditCcModel->submitter_id = $request->getSubmitterId();
            $auditCcModel->cc_staff_id  = $toStaffId;
            $auditCcModel->state        = $request->getState(); //1 是待审批 2 是通过
            $auditCcModel->summary      = $request->getSummary();
            $auditCcModel->save();
        }
        //发送 push
        $this->doSendCcPush($ccStaffIdList);

        return true;
    }

    /**
     * @description 发送pussh提醒
     * @param $ccList
     * @return bool
     */
    public function doSendCcPush($ccList): bool
    {
        if (empty($ccList)) {
            return false;
        }
        //push 您有新的用人审批抄送，请查看！
        $HcRepository     = new HcRepository($this->timeZone);
        $PublicRepository = new PublicRepository();

        //获取cc人是什么语言
        $staffAccount = $HcRepository->getStaffAcceptLang($ccList);
        $langArr      = array_column($staffAccount, 'accept_language', 'staff_info_id');

        $this->logger->write_log('send_cc_push send CC message:' . json_encode($langArr), 'info');
        foreach ($ccList as $v) {
            $lang      = $langArr[$v] ?? 'th';
            $t         = $this->getTranslation($lang);
            $pushParam = [
                'staff_info_id'   => $v,
                'message_title'   => $t->_('6006'),
                'message_content' => $t->_('workflow_cc_message'),
            ];
            $PublicRepository->pushMessageAndJumpToCC($pushParam);
        }
        return true;
    }


    //发送push
    public function sendPush($staffId, $title,$content, $scheme, $src = 'backyard'){
        //push
        $data   = [
            "staff_info_id"   => $staffId,  //推送人员工ID
            "src"             => $src,      //1:'kit'; 2:'backyard','c';
            "message_title"   => $title,  //标题
            "message_content" => strip_tags($content),//内容
            'message_scheme'  => $scheme,
        ];
        $client = new ApiClient('bi_rpc', '', 'push_to_staff');
        $client->setParams($data);
        $client->execute();
    }


    /**
     * Formats the push message for staff members.
     *
     * @param array $staff_info_ids An array containing the IDs of the staff members.
     *
     * @return array
     */
    public function formatPushMsgStaff($staff_info_ids): array
    {
        $return = [];
        if (is_array($staff_info_ids)) {

            if (count($staff_info_ids) === 1) {
                $return = $staff_info_ids;
            } else {
                foreach ($staff_info_ids as $staff_info_id) {
                    $return[] = ['id' => $staff_info_id];
                }
            }
        } else {
            $return = [$staff_info_ids];
        }
        return $return;
    }

    /**
     * 统一消息样式
     * @param $content
     * @return string
     */
    public function formatPushMsgContent($content): string
    {
        return addslashes("<div style='font-size: 30px'>" . $content . "</div>");
    }

    /**
     * 发送消息并跳转
     * @param $paramIn
     * @param $language
     * @return bool
     */
    public function pushAndSendMessageToSubmitter($paramIn, $language)
    {
        //校验必传参数
        if (empty($paramIn) || empty($paramIn['staff_info_id']) || empty($paramIn['message_title'])) {
            return false;
        }

        $staff_info_id   = $paramIn['staff_info_id'];   //接收push信息人id
        $message_title   = $paramIn['message_title'];   //push标题
        $message_content = $paramIn['message_content']; //push标题
        $path            = $paramIn['path'] ?? "";      //Push跳转页面地址

        //获取push调整地址
        $message_scheme = self::getPushToMessageScheme($path);
        try {
            //发送消息
            $params = [
                'staff_users'        => [$staff_info_id],  //提交人ID
                'message_title'      => $message_title,    //标题
                'message_content'    => $this->formatPushMsgContent($message_content),  //内容
                'category'           => -1,                //普通消息
                'staff_info_ids_str' => $staff_info_id,
                'id'                 => time() . $staff_info_id . rand(1000000, 9999999),
            ];
            $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $language));
            $bi_rpc->setParams($params);
            $bi_rpc->execute();

            //发送push
            $data = [
                "staff_info_id"   => $staff_info_id,                              //上级id
                "src"             => "backyard",                                  //1:'kit'; 2:'backyard','c';
                "message_title"   => $message_title,                              //标题
                "message_content" => str_replace("<br/>", "  ", $message_content),//内容
                "message_scheme"  => $message_scheme,                             //地址
            ];
            $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
            $ret->setParams($data);
            $ret->execute();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取push跳转到前端页面地址
     * @param $to_path
     * @return string
     */
    private static function getPushToMessageScheme($to_path): string
    {
        switch ($to_path) {
            case "message_list":
                //消息列表
                $message_scheme = "flashbackyard://fe/tab?index=message";
                break;
            default:
                //默认地址
                $approval_html_url   = env('approval_html_url', 'flashbackyard://fe/html?url=');
                $approval_detail_url = env('approval_detail_url', 'http://192.168.0.222:90/#/ApprovalDetail/');
                $message_scheme      = $approval_html_url . urlencode($approval_detail_url);
                break;
        }
        return $message_scheme;
    }
}