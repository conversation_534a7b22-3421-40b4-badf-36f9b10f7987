<?php
/**
 * Author: Bruce
 * Date  : 2024-02-23 20:54
 * Description:
 */

namespace FlashExpress\bi\App\Server;


use Exception;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\BankListRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;

class VoucherServer extends BaseServer
{
    private static $single = null;
    public         $timezone;

    const TYPE_PAYMENT = 1;
    const TYPE_TAX = 2;

    const PDF_HEADER = '<div
  style="
    width: 210mm;
    height: 38mm;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
  "
>
  <img
    style="
      width: 100%;
      margin: 0 0 0 3.5mm;
      padding: 0;
      border: 0;
      outline: none;
    "
    src="data:image/png;base64,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"
  />
</div>';

    public static function getInstance($lang, $timezone)
    {
        if (!self::$single) {
            self::$single = new self($lang, $timezone);
        }

        return self::$single;
    }

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }


    public function handle($params)
    {
        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoOne($params['staff_info_id']);
        if (empty($staffInfo)) {
            return false;
        }

        if (isset($params['start_time'])) {
            return true;
        }

        $item_arr = [
            'REGISTER_COUNTRY',    //户口所在国家
            'REGISTER_PROVINCE',   //户口所在省
            'REGISTER_CITY',       //户口所在市
            'REGISTER_DISTRICT',   //户口所在乡
            'REGISTER_POSTCODES',  //户口所在邮编
            'REGISTER_HOUSE_NUM',  //户口所在门牌号
            'REGISTER_VILLAGE_NUM',//户口所在村号
            'REGISTER_VILLAGE',    //户口所在村,
            'REGISTER_ALLEY',      //户口所在巷
            'REGISTER_STREET',     //户口所在街道
            'BANK_NO_NAME',        //持卡人姓名
        ];

        //获取地址
        $itemInfo = (new StaffRepository($this->lang))->getStaffItemsInfo([$params['staff_info_id']], $item_arr);

        $bankStaffName = $itemInfo[$params['staff_info_id']]['BANK_NO_NAME'] ?? '';
        $bankInfo      = BankListRepository::getBankInfo($staffInfo['bank_type']);

        $storeInfo = !empty($params['store_id']) ? SysStoreRepository::getSysStoreInfo($params['store_id'],
            ['name']) : [];

        $start_date = $params['start_date'];

        //快递员入职日期处于时间范围之间，则开始时间显示为员工入职日期。
        if (!empty($staffInfo['hire_date']) && strtotime($params['start_date']) <= strtotime($staffInfo['hire_date'])) {
            $params['start_date'] = date('Y-m-d', strtotime($staffInfo['hire_date']));
        }


        //快递员最后工作日处于时间范围之内，则结束时间显示为员工最后工作日
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_2 && !empty($staffInfo['leave_date'])) {
            if (strtotime($params['end_date']) >= strtotime($staffInfo['leave_date'])) {
                $params['end_date'] = date('Y-m-d', strtotime("{$staffInfo['leave_date']} -1 day"));
            }
        }
        $params['start_date']   = !empty($params['start_date']) ? date('d/m/Y', strtotime($params['start_date'])) : '';
        $params['end_date']     = !empty($params['end_date']) ? date('d/m/Y', strtotime($params['end_date'])) : '';
        $params['payment_date'] = !empty($params['payment_date']) ? date('d/m/Y', strtotime($params['payment_date'])) : '';

        $pdfData['staff_info_id']   = $staffInfo['staff_info_id'];
        $pdfData['staff_name']      = $staffInfo['name'] ?? '';
        $pdfData['identity']        = $staffInfo['identity'] ?? '';
        $pdfData['store_name']      = empty($params['store_name']) ? $storeInfo['name'] : $params['store_name'];//todo fbi 给
        $pdfData['mobile']          = $staffInfo['mobile'] ?? '';
        $pdfData['payment_date']    = $params['payment_date'] ?? '';
        $pdfData['settlement_date'] = $params['start_date'] . '-' . $params['end_date'];//结算日期

        $pdfData['withholding_tax_no'] = !empty($params['withholding_tax_no']) ? $params['withholding_tax_no'] : '';//代扣税凭证编号

        $pdfData['total_pre_tax_amount']       = !empty($params['total_pre_tax_amount']) ? $params['total_pre_tax_amount'] : '0.00';//税前总金额
        $pdfData['withholding_tax_amount']     = !empty($params['withholding_tax_amount']) ? $params['withholding_tax_amount'] : '0.00';//代扣税金额
        $pdfData['withholding_tax_amount_max'] = !empty($params['withholding_tax_amount_max']) ? $params['withholding_tax_amount_max'] : '';//代扣税金额(泰语大写)

        $pdfData['total_deduction_amount'] = !empty($params['total_deduction_amount']) ? $params['total_deduction_amount'] : '0.00';//总扣款金额
        $pdfData['total_revenue_amount']   = !empty($params['total_revenue_amount']) ? $params['total_revenue_amount'] : '0.00';//总收入金额

        $pdfData['total_pre_revenue_amount'] = !empty($params['total_pre_revenue_amount']) ? $params['total_pre_revenue_amount'] : '0.00';//总收入金额-税前
        $pdfData['actual_payment_amount']    = !empty($params['actual_payment_amount']) ? $params['actual_payment_amount'] : '0.00';//实际支付金额

        $pdfData['address']      = (new SysServer($this->lang,
            $this->timeZone))->getStaffRegisterAddress($itemInfo[$params['staff_info_id']]);
        $pdfData['payment_data'] = $this->formatData($params);

        $pdfData['bank_staff_name'] = $bankStaffName;//持卡人姓名
        $pdfData['bank_no']         = $staffInfo['bank_no'];//持卡人姓名
        $pdfData['bank_type_text']  = $bankInfo['bank_name'] ?? '';//持卡人姓名

        //19774 已缴纳押金 为空不展示
        if(!empty($params['deposit_paid'])) {
            $pdfData['deposit_paid'] = $params['deposit_paid'];//已缴纳押金
        }

        //22594【TH|FBI】支持个人代理薪资预支
        // Flash Money 服务费预支 deduction_advance_payment 为0，不展示：总应支付服务费金额（税后）、Flash Money 服务费预支
        if(!empty($params['deduction_advance_payment'])) {
            $pdfData['total_after_tax_amount'] = $params['total_after_tax_amount'];//总应支付服务费金额（税后）
            $pdfData['deduction_advance_payment'] = $params['deduction_advance_payment'];//Flash Money 服务费预支
        }


        $timeSting       = date('YmdHis');
        $paymentFileName = 'payment_' . $staffInfo['staff_info_id'] . '_' . $timeSting;
        $paymentPdfUrl   = $this->voucherCreatPdf($pdfData, $paymentFileName);

        //获取 代扣税凭证pdf
        $taxFileName = 'tax_' . $staffInfo['staff_info_id'] . '_' . $timeSting;
        $taxPdfUrl   = $this->voucherCreatPdf($pdfData, $taxFileName, self::TYPE_TAX);

        if ($paymentPdfUrl === false || $taxPdfUrl === false) {
            return false;
        }

        $callBackData['staff_info_id']   = $staffInfo['staff_info_id'];
        $callBackData['start_date']      = $start_date;
        $callBackData['payment_pdf_url'] = $paymentPdfUrl;
        $callBackData['tax_pdf_url']     = $taxPdfUrl;
        $callBackResult                  = $this->callBackToFbi($callBackData);
        if (!$callBackResult) {
            return false;
        }

        //重新生成pdf 回调 给 fbi，不发消息，不发邮件,且消费消息成功
        if(!empty($params['fix_data'])) {
            return true;
        }

        $message['staff_info_id'] = $staffInfo['staff_info_id'];
        $message['start_date']    = $params['start_date'];
        $message['end_date']      = $params['end_date'];

        $message['type']       = self::TYPE_PAYMENT;
        $sendPaymentMessageRes = $this->sendMessage(json_encode($message, JSON_UNESCAPED_UNICODE));
        if (!$sendPaymentMessageRes) {
            return false;
        }

        $message['type']   = self::TYPE_TAX;
        $sendTaxMessageRes = $this->sendMessage(json_encode($message, JSON_UNESCAPED_UNICODE));
        if (!$sendTaxMessageRes) {
            return false;
        }

        $emailData['staff_info_id'] = $staffInfo['staff_info_id'];
        $emailData['name']          = $staffInfo['name'];
        $emailData['email']         = $staffInfo['personal_email'];
        $emailData['start_date']    = $params['start_date'];
        $emailData['end_date']      = $params['end_date'];

        $emailData['link'] = env('sign_url') . '/#/LinkLogin?source=payment&start_date=' . $start_date . '&staff_info_id=' . $staffInfo['staff_info_id'];
        $emailData['type'] = self::TYPE_PAYMENT;
        $this->sendEmail(json_encode($emailData, JSON_UNESCAPED_UNICODE));

        $emailData['link'] = env('sign_url') . '/#/LinkLogin?source=withholding_tax&start_date=' . $start_date . '&staff_info_id=' . $staffInfo['staff_info_id'];
        $emailData['type'] = self::TYPE_TAX;
        $this->sendEmail(json_encode($emailData, JSON_UNESCAPED_UNICODE));

        return true;
    }

    /**
     * 格式化数据
     * @param $params
     * @return array
     */
    public function formatData($params)
    {
        $params = array_filter($params);
        //收入
        $revenue_item['collection_service_fee']          = $params['collection_service_fee'] ?? '0.00';//揽件服务费
        $revenue_item['delivery_service_fee']            = $params['delivery_service_fee'] ?? '0.00';//派件服务费
        $revenue_item['support_branch_delivery_subsidy'] = $params['support_branch_delivery_subsidy'] ?? '0.00';//支援网点派件补贴
        $revenue_item['regional_fuel_subsidy']           = $params['regional_fuel_subsidy'] ?? '';//区域油费补贴 为0 则不展示
        $revenue_item['penalty_reissue']                 = $params['penalty_reissue'] ?? '';//处罚补发 为0 则不展示
        $revenue_item['refund_deposit_required']         = $params['refund_deposit_required'] ?? '';//需退还押金 为0 则不展示
        $revenue_item['other_incentives']                = $params['other_incentives'] ?? '0.00';//其他激励
        $revenue_item['other_income']                    = $params['other_income'] ?? '0.00';//其他收入

        //处罚
        $deductions_item['current_deposit_required']               = $params['current_deposit_required'] ?? '';//当期需缴押金 为0 则不展示
        $deductions_item['penalty_deduction']                      = $params['penalty_deduction'] ?? '';//处罚补扣
        $deductions_item['lost_package']                           = $params['lost_package'] ?? '';//包裹丢失
        $deductions_item['package_damaged']                        = $params['package_damaged'] ?? '';//包裹破损
//        $deductions_item['missing_collecting']                     = $params['missing_collecting'] ?? '';//漏揽收 不要了
//        $deductions_item['false_cancel_package']                   = $params['false_cancel_package'] ?? '';//虚假撤销包裹 不要了
        $deductions_item['false_reporting_violations']             = $params['false_reporting_violations'] ?? '';//虚假上报
        $deductions_item['customer_complaints']                    = $params['customer_complaints'] ?? '';//客户投诉
        $deductions_item['weighing_packages_inaccurate']           = $params['weighing_packages_inaccurate'] ?? '';//揽收时称重包裹不准确
        $deductions_item['false_misclassification']                = $params['false_misclassification'] ?? '';//虚假错分
        $deductions_item['collect_prohibited_packages']            = $params['collect_prohibited_packages'] ?? '';//揽收禁运包裹
        $deductions_item['improper_pasting_surface_labels']        = $params['improper_pasting_surface_labels'] ?? '';//面单粘贴不规范
        $deductions_item['multiple_sided_single']                  = $params['multiple_sided_single'] ?? '';//多面单
        $deductions_item['not_answering_company_phone']            = $params['not_answering_company_phone'] ?? '';//故意不接公司电话
        $deductions_item['false_delivery']                         = $params['false_delivery'] ?? '';//虚假妥投
        $deductions_item['false_problem_inventory']                = $params['false_problem_inventory'] ?? '';//虚假问题件/虚假留仓件
        $deductions_item['time_exceeds_three_days']                = $params['time_exceeds_three_days'] ?? '';//包裹配送时间超三天
        $deductions_item['not_delivered_before_rescheduling_time'] = $params['not_delivered_before_rescheduling_time'] ?? '';//未在客户要求的改约时间之前派送包裹
        $deductions_item['false_reply_to_work_order']              = $params['false_reply_to_work_order'] ?? '';//虚假回复工单
        $deductions_item['package_not_delivered_properly']         = $params['package_not_delivered_properly'] ?? '';//PRI或者speed包裹未妥投
        $deductions_item['violent_sorting']                        = $params['violent_sorting'] ?? '';//暴力分拣
        $deductions_item['report_rejection_evidence_unqualified']  = $params['report_rejection_evidence_unqualified'] ?? '';//上报拒收证据不合格
        $deductions_item['false_marking']                          = $params['false_marking'] ?? '';//虚假标记
        $deductions_item['collection_task_timeout']                = $params['collection_task_timeout'] ?? '';//揽收任务超时
        $deductions_item['outer_package_unqualified']              = $params['outer_package_unqualified'] ?? '';//包裹外包装不合格
        $deductions_item['upload_fake_photos']                     = $params['upload_fake_photos'] ?? '';//上传虚假照片
        $deductions_item['false_cancellation_tasks']               = $params['false_cancellation_tasks'] ?? '';//虚假取消揽件任务
        $deductions_item['failure_close_pickup_task']              = $params['failure_close_pickup_task'] ?? '';//未及时关闭揽件任务
        $deductions_item['false_marking_rejection']                = $params['false_marking_rejection'] ?? '';//虚假标记拒收
        $deductions_item['false_upload_facial_scans']              = $params['false_upload_facial_scans'] ?? '';//虚假上传人连脸扫描
        $deductions_item['not_accepting_orders_without_notice']    = $params['not_accepting_orders_without_notice'] ?? '';//不接单未提前通知
        $deductions_item['termination_contract_without_notice']    = $params['termination_contract_without_notice'] ?? '';//终止合同未提前通知
        $deductions_item['deduction_amount_asset_leasing']         = $params['deduction_amount_asset_leasing'] ?? '';//资产租用扣款金额
        $deductions_item['asset_deduction_amount']                 = $params['asset_deduction_amount'] ?? '';//资产扣费金额
        $deductions_item['other_deductions']                       = $params['other_deductions'] ?? '';//其他扣款
        $deductions_item['public_funds_not_turned_timeout']        = $params['public_funds_not_turned_timeout'] ?? '';//公款超时未上缴
        $deductions_item['complaints_false_issue_warehouse']       = $params['complaints_false_issue_warehouse'] ?? '';//客户投诉-虚假问题件/虚假留仓件
        $deductions_item['other_penalties']                        = $params['other_penalties'] ?? '';//其他类处罚

        //收入数据，过滤值为空的字段。
        $revenue_item      = array_filter($revenue_item);
        $revenue_item_keys = array_keys($revenue_item);

        //处罚数据，过滤值为空的字段。
        $deductions_item      = array_filter($deductions_item);
        $deductions_item_keys = array_keys($deductions_item);


        $revenue_item_all = [];
        foreach ($revenue_item_keys as $oneRevenue) {
            $revenueData['label'] = $oneRevenue;
            $revenueData['value'] = $revenue_item[$oneRevenue] ?? '';
            $revenue_item_all[]   = $revenueData;
        }

        $deductions_item_all = [];
        foreach ($deductions_item_keys as $oneDeductions) {
            $deductionsData['label'] = $oneDeductions;
            $deductionsData['value'] = $deductions_item[$oneDeductions] ?? '';
            $deductions_item_all[]   = $deductionsData;
        }
        //收入
        $revenue_item_num = count($revenue_item_all);
        //处罚
        $deductions_item_num = count($deductions_item_keys);

        $num = $revenue_item_num > $deductions_item_num ? $revenue_item_num : $deductions_item_num;

        //整理数据
        $result = [];
        for ($i = 0; $i < $num; $i++) {
            //收入
            $left_label         = isset($revenue_item_all[$i]) ? $revenue_item_all[$i]['label'] : '';
            $left_value         = isset($revenue_item_all[$i]) ? $revenue_item_all[$i]['value'] : '';
            $data['left_label'] = !empty($left_label) ? $this->getTranslation()->_($left_label) : '';
            $data['left_value'] = $left_value;

            //处罚
            $right_label = isset($deductions_item_all[$i]) ? $deductions_item_all[$i]['label'] : '';
            $right_value = isset($deductions_item_all[$i]) ? $deductions_item_all[$i]['value'] : '';

            $data['right_label'] = !empty($right_label) ? $this->getTranslation()->_($right_label) : '';
            $data['right_value'] = $right_value;
            $result[]            = $data;
        }

        return $result;
    }

    /**
     * 创建pdf
     * @param $params
     * @param $type
     * @param $filePdfName
     * @return bool|string
     * @throws Exception
     */
    public function voucherCreatPdf($params, $filePdfName, $type = self::TYPE_PAYMENT)
    {
        $fileName = $type == self::TYPE_PAYMENT ? 'payment_voucher.ftl' : 'tax_voucher.ftl';

        $tempFile = BASE_PATH . '/public/pdf_template/' . $fileName;


        $pdfTempUrl = (new ToolServer($this->lang, $this->timezone))->getPdfTemp($tempFile);

        $pdf_header_setting = [
            'displayHeaderFooter' => true,
            'headerTemplate'      => self::PDF_HEADER,
            'footerTemplate'      => '<div></div>',
        ];

        $pdf_header_footer_setting = $type == self::TYPE_PAYMENT ? $pdf_header_setting : [];

        try {
            $pdf_file_data = (new formPdfServer())->generatePdf($pdfTempUrl, $params,
                [], $filePdfName, $pdf_header_footer_setting, 'attchment');

            //生产pdf失败
            if (empty($pdf_file_data['object_url'])) {
                $this->logger->write_log([
                    "voucherCreatPdf_create_fail" => $params,
                    'result'                      => json_encode($pdf_file_data),
                ],
                    'notice');
                return false;
            }
            return $pdf_file_data['object_url'] ?? '';
        } catch (\Exception $e) {
            $this->logger->write_log("voucherCreatPdf fail ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString());
            echo 'invoiceCreatPdf fail' . PHP_EOL;
            return false;
        }
    }

    /**
     * 发送消息
     * @param $data
     * @return bool
     */
    public function sendMessage($data)
    {
        $messageData = json_decode($data, true);
        $title_key   = $messageData['type'] == self::TYPE_PAYMENT ? 'voucher_payment_message_title' : 'voucher_tax_message_title';
        $category    = $messageData['type'] == self::TYPE_PAYMENT ? MessageEnums::MESSAGE_CATEGORY_CODE_PAYMENT_VOUCHER : MessageEnums::MESSAGE_CATEGORY_CODE_TAX_WITHHOLDING_VOUCHER;

        //获取收信人语言环境
        $lang = (new StaffServer)->getLanguage($messageData['staff_info_id']);
        //获取语言
        $t       = $this->getTranslation($lang);
        $title   = $t->_($title_key);
        $content = 'invoice_start_date=' . $messageData['start_date'] . '&invoice_end_date=' . $messageData['end_date'];

        $kit_param['staff_info_ids_str'] = $messageData['staff_info_id'];
        $kit_param['staff_users']        = [['id' => $messageData['staff_info_id']]];
        $kit_param['message_title']      = $title;
        $kit_param['message_content']    = $content;
        $kit_param['category']           = $category;

        $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($kit_param);
        $res = $bi_rpc->execute();
        if (!isset($res['result']['code']) || $res['result']['code'] != ErrCode::SUCCESS) {
            $this->getDI()->get('logger')->write_log([
                'function' => 'voucher_send_message',
                'message'  => '消息发送失败',
                'params'   => $data,
                'result'   => $res,
            ]);
            return false;
        }
        return true;
    }

    /**
     * 发送邮件
     * @param $data
     * @return bool
     */
    public function sendEmail($data)
    {
        $emailData = json_decode($data, true);
        try {
            $email_num = $emailData['email'];

            $title_key = $emailData['type'] == self::TYPE_PAYMENT ? 'voucher_payment_email_title' : 'voucher_tax_email_title';
            //todo 邮件内容 加翻译
            $content_key = $emailData['type'] == self::TYPE_PAYMENT ? 'voucher_payment_email_content' : 'voucher_tax_email_content';

            $title   = $this->t->_($title_key);
            $content = $this->t->_($content_key, [
                'name'          => $emailData['name'],
                'staff_info_id' => $emailData['staff_info_id'],
                'start_date'    => $emailData['start_date'],
                'end_date'      => $emailData['end_date'],
                'link'          => $emailData['link'],
            ]);
            $this->getDI()->get('logger')->write_log(['voucher_send_mail_data' => $emailData,'voucher_send_mail' => $content], 'info');

            if (!empty($emailData['email'])) {
                $sendEmail = (new MailServer())->send_mail_from_home($email_num, $title, $content);

                if ($sendEmail) {
                    return true;
                } else {
                    $this->logger->write_log(['voucher_send_mail_fail' => $data, 'msg' => '发送异常'], 'info');
                    return false;
                }
            } else {
                $this->logger->write_log(['voucher_send_mail_fail' => $data, 'msg' => '个人邮箱为空'], 'info');
                return false;
            }
        } catch (Exception $e) {
            $this->getDi()->get('logger')->write_log('出现异常！voucher_send_email:邮件发送失败：data:' . $data . 'message:' . $e->getMessage(),
                'notice');
        }
    }

    /**
     * 将pdf 回调同步 给fbi
     * @param $params
     * @return bool
     */
    public function callBackToFbi($params)
    {
        $data['staff_info_id']   = $params['staff_info_id'];
        $data['start_date']      = $params['start_date'];
        $data['payment_pdf_url'] = $params['payment_pdf_url'];
        $data['tax_pdf_url']     = $params['tax_pdf_url'];
        //todo 更换地址
        $ret = new ApiClient('ard_api', '', 'proxyInvoice.receive_invoice_pdf', $this->lang);
        $ret->setParams($data);
        $res = $ret->execute();
        $this->getDI()->get('logger')->write_log("voucherCallBackFbi 参数:" . json_encode($data) . ";结果:" . json_encode($res),
            'info');
        if (!isset($res['result'])) {
            return false;
        }
        if ($res['result']['code'] == 1) {
            return true;
        }
        $msg = isset($res['result']['msg']) ? $res['result']['msg'] : $res['error'];
        $this->getDI()->get('logger')->write_log("voucherCallBackFbi 参数:" . json_encode($data) . ";结果:" . $msg);

        return false;
    }
}