<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Interfaces\AuditDetailRequest;
use FlashExpress\bi\App\Interfaces\AuditInterface;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;

abstract class AuditBaseServer extends BaseServer implements AuditInterface
{
    private $auditDetailRequest;


    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    abstract public function getDetail(int $auditId, $user, $comeFrom);


    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    abstract public function genSummary(int $auditId, $user);

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    abstract public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true);

    /**
     * 样例
     * from_node_id | to_node_id | valuate_formula | valuate_code
     * -------------+------------+-----------------+-------------
     *      4       |     5      |    $p1 == 4     | getSubmitterDepartment
     *
     * 表示当提交人的部门为4时，审批节点4的下一个节点是5
     * 需要在 getWorkflowParams 中返回申请人所在的部门字段
     *
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed
     */
    abstract public function getWorkflowParams($auditId, $user, $state = null);

    /**
     * @description 在固化节点时，提供Form参数以解析节点上的审批人
     */
    public function getParseNodeParams($auditId, $user, $state = null): array
    {
        return [];
    }

    /**
     * @description 设置auditDetailRequest对象
     * @param AuditDetailRequestServer $auditDetailRequestObj
     * @return void
     */
    public function setAuditDetailRequest(AuditDetailRequest $auditDetailRequestObj)
    {
        $this->auditDetailRequest = $auditDetailRequestObj;
    }

    /**
     * @description 获取auditDetailRequest对象
     * @return AuditDetailRequestServer
     */
    public function getAuditDetailRequest(): AuditDetailRequestServer
    {
        return $this->auditDetailRequest;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(true, false, false, false, false, false);
    }

    /**
     * @description 自定义审批按钮
     * @param $auditId
     * @return array
     */
    public function customiseOptions($auditId): array
    {
        return [];
    }

    /**
     * @description 申请人自定义按钮
     * @param $auditId
     * @return bool
     */
    public function applicantCustomiseOptions($auditId): bool
    {
        return true;
    }

    /**
     * @description 申请人自定义按钮
     * @param $auditId
     * @return bool
     */
    public function applicantCustomiseOptionsInApprovalProcess($auditId): bool
    {
        return true;
    }

    /**
     * 判断撤销类型，1为审批中撤销，2为审批结束后撤销，需要走审批流程，0未知
     * @param $auditId
     * @param $auditType
     * @return int
     * @throws ValidationException
     */
    public function determineCancelType($auditId, $auditType)
    {
        $apply = AuditApplyModel::findFirst([
            'conditions' => "biz_value = :value: and biz_type = :type:",
            'bind' => [
                'type'  => $auditType,
                'value' => $auditId,
            ],
        ]);
        if (empty($apply)) {
            throw new ValidationException('no valid data', ErrCode::WORKFLOW_DATA_ERROR);
        }
        if ($apply->getState() == enums::APPROVAL_STATUS_PENDING){
            return 1;
        }
        if (($apply->getIsCancel() == 0 && $apply->getState() == enums::APPROVAL_STATUS_APPROVAL)
            || ($apply->getIsCancel() > 0 && $apply->getState() == enums::APPROVAL_STATUS_APPROVAL)
        ){
            return 2;
        }
        return 0;
    }
}