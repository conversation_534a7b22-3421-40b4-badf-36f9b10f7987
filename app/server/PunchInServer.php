<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffPunchInLogModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;

/**
 * 点击上班卡打卡按钮的最早操作记录到log表，便于补卡
 */
class PunchInServer extends BaseServer
{

    const PUNCH_INFO_SYNC_TABLE_KEY = 'staff_punch_in_sync_table';

    public function pushData($staffInfo)
    {
        $params['attendance_date'] = date('Y-m-d');
        $params['started_at']      = gmdate('Y-m-d H:i:s');
        $redis                     = $this->getDI()->get('redisLib');
        $params                    = array_merge($params, $staffInfo);
        $this->logger->write_log($params, 'info');
        $content = json_encode($params, JSON_UNESCAPED_UNICODE);
        return $redis->lpush(self::PUNCH_INFO_SYNC_TABLE_KEY, $content);
    }

    public function popData()
    {
        $redis = $this->getDI()->get('redisLib');
        while ($data = $redis->rpop(self::PUNCH_INFO_SYNC_TABLE_KEY)) {
            $data = json_decode($data, true);
            $exist = StaffPunchInLogModel::findFirst([
                'columns'=>'id',
                'conditions' => "attendance_date = :attendance_date: and staff_info_id = :staff_info_id:",
                'bind'       => [
                    'attendance_date' => $data['attendance_date'],
                    'staff_info_id'   => $data['staff_id'],
                ],
            ]);
            if (empty($exist)) {
                try {
                    $model = new StaffPunchInLogModel();
                    $model->create(['staff_info_id'   => $data['staff_id'],
                                    'attendance_date' => $data['attendance_date'],
                                    'started_at'      => $data['started_at'],
                    ]);
                } catch (Exception $e) {
                    //异常不处理 唯一索引冲突
                    if($e->getCode() != 23000){
                        throw  $e;
                    }
                }
            }
            //支援
            $supportInfo = (new AttendanceRepository($this->lang,
                $this->timeZone))->getSupportInfoBySubStaff($data['staff_id'],$data['attendance_date']);
            if (!empty($supportInfo)) {
                $exist = StaffPunchInLogModel::findFirst([
                    'columns'=>'id',
                    'conditions' => "attendance_date = :attendance_date: and staff_info_id = :staff_info_id:",
                    'bind'       => [
                        'attendance_date' => $data['attendance_date'],
                        'staff_info_id'   => $supportInfo['staff_info_id'],
                    ],
                ]);
                if (empty($exist)) {
                    try {
                        $model = new StaffPunchInLogModel();
                        $model->create(['staff_info_id'   => $supportInfo['staff_info_id'],
                                        'attendance_date' => $data['attendance_date'],
                                        'started_at'      => $data['started_at'],
                        ]);
                    } catch (Exception $e) {
                        //异常不处理 唯一索引冲突
                        if($e->getCode() != 23000){
                            throw  $e;
                        }
                    }
                }
            }
        }
    }

    public function deleteLog($date)
    {
        $del = " delete from staff_punch_in_log where attendance_date = ? ";
        return $this->getDI()->get('db')->execute($del,[$date]);
    }

    //在系统设置-HRIS-punch_rule范围内的职位，当天为Off 且 当日没有申请加班（含待审批和已同意），不允许上/下班打卡，提示“休息日不能打卡”
    public function checkOff($param, $userInfo, $source)
    {
        //获取 punch_rule 配置
        $settingEnv = new SettingEnvServer();
        $punchRule  = $settingEnv->getSetVal('punch_rule', ',');
        //获取考勤日期
        $param['user_info'] = $userInfo;
        //看职位是否在 配置职位内 如果不在就返回
        if (!in_array($userInfo['job_title'], $punchRule)) {
            return true;
        }
        $attendanceServer = new AttendanceServer($this->lang, $this->timeZone);
        $attendanceInfo   = $attendanceServer->attendance_info($param);
        $date             = $attendanceInfo['attendance_date'] ?? date('Y-m-d');

        //是否 off 查询当前工号在 hr_staff_work_days 表 是否存在记录
        $workDayInfo = HrStaffWorkDayModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and date_at = :date:',
            'bind'       => ['staff_id' => $userInfo['id'], 'date' => $date],
        ]);
        //如果不是off 不拦截
        if (!$workDayInfo) {
            return true;
        }
        //查询当前工号是否存在加班记录
        $overtimeInfo = HrOvertimeModel::findFirst([
            'conditions' => 'staff_id = :staff_id: and date_at = :date: and state in (1,2)',
            'bind'       => ['staff_id' => $userInfo['id'], 'date' => $date],
        ]);
        if (!empty($overtimeInfo)) {
            return true;
        }
        $return['data'] = [];
        //上下班结构不一样
        if ($source == 'punch_in') {
            $returnArr['dialog_status']      = 1;
            $returnArr['dialog_msg']         = $this->getTranslation()->_('off_punch_notice');
            $returnArr['dialog_must_status'] = 1;
            $returnArr['is_ces_tra']         = 0;
            $return['data']                  = $returnArr;
        }
        if ($source == 'punch_out') {
            $returnArr['business_type']        = 'un_remittance';
            $returnArr['remittance_detail'] = [
                'dialog_status'      => 1,
                'dialog_msg'         => $this->getTranslation()->_('off_punch_notice'),
                'dialog_must_status' => 1,
                'is_ces_tra'         => 0,
            ];
            $return['data']                = $returnArr;
        }
        return $this->checkReturn($return);
    }


}