<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\Modules\My\Server\Message\ProbationMessage as MyProbationMessage;
use FlashExpress\bi\App\Server\BaseServer;

class MessageFactoryServer extends BaseServer
{
    /**
     * @description 获取消息详情
     * @param MessageDataServer $messageData
     * @return array
     */
    public function getMessageDetail(MessageDataServer $messageData): array
    {
        $message = $messageData->getMessageResponse();
        switch ($message->getCategory()) {
            case MessageEnums::MESSAGE_CATEGORY_QUESTIONNAIRE: //问卷消息
                $messageObj = new QuestionnaireMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_WARN: //警告书
            case MessageEnums::MESSAGE_CATEGORY_76: //不需要签字警告书
                $messageObj = new WarnMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_CORRECTION_LETTER: //个人行为整改书
                $messageObj = new CriminalMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::CATEGORY_SIGN: //签字消息
                $messageObj = new SignMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_ANSWER_QUESTION: //答题消息
                $messageObj = new AnswerMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_CONTRACT: //电子合同消息
                $messageObj = new ContractMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_ORDINARY: //普通消息
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_AGENT_CONTRACT_ATTACHMENT')://MY IC和兼职IC签署合同,合同附件
            $messageObj = new NormalMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_COMPLETE_RESUME: //简历信息完善消息
                $messageObj = new CompleteResumeMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_INTERVIEWS: //面试详情消息
                $messageObj = new InterviewMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_TRANSFER_EVALUATION: //转正评估提醒消息
                $messageObj = new ProbationMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_TRANSFER_EVALUATION_MY: //马来转正评估
            case MessageEnums::MESSAGE_CATEGORY_TRANSFER_EVALUATION_MY_V2:
                $messageObj = new MyProbationMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_INVENTORY_CHECK:
            case MessageEnums::MESSAGE_CATEGORY_INVENTORY_CHECK_REMIND:
                $messageObj = new InventoryMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_GET_CONFIRM: //知悉确认消息
                $messageObj = new GetConfirmMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_CERTIFICATE_UPLOAD_REMINDER: //上传提醒
            case MessageEnums::MESSAGE_CATEGORY_BANK_CARD_REFUSE: //银行卡照片审核拒绝
            case MessageEnums::MESSAGE_CATEGORY_BANK_CARD_REFUSE_PH: //银行卡照片审核拒绝
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_CODE_RESIDENCE_BOOKLET'): //户口簿审核拒绝
                $messageObj = new UploadReminderMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_75: //普通 带h5按钮的 消息类型
                $messageObj = new NormalH5Message($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_LEAVE_ASSET:
                $messageObj = new LeaveAssetMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_BIRTHDAY:
            case MessageEnums::MESSAGE_CATEGORY_CODE_ANNIVERSARY:
            case MessageEnums::MESSAGE_CATEGORY_CODE_FASTING:
            case MessageEnums::MESSAGE_CATEGORY_CODE_SONGGAN:
                $messageObj = new ActivityMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_MATERIAL_RECEIVING_REMINDER:
                $messageObj = new MaterialWmsMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_RENEW_CONTRACT_PROPOSE:
                $messageObj = new RenewContractMessage($this->lang, $this->timeZone);
                break;
            //合同审核业务
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_BUSINESS_CONTRACT_APPLY'):
                $messageObj = new ContractBusinessMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_SICK_CERTIFICATE:
                //普通 已读消息 没有其他逻辑
                $messageObj = new SickCertificateMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_CERTIFICATE: //证书消息
                $messageObj = new CertificateMessage($this->lang, $this->timeZone);
                break;
            case EnumSingleton::getInstance()->getEnums('UN_RECEIVE_PARCEL'): //已签收未收到包裹消息
                $messageObj = new UnReceiveParcelMessage($this->lang, $this->timeZone);
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_VAN_CONTAINER_REJECT'): //车厢类型申请驳回业务
                $messageObj = new VanContainerMessage($this->lang, $this->timeZone);
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_COURIER_VEHICLE_INSPECTION'): //快递员车辆稽查消息
                $messageObj = new VehicleInspectionMessage($this->lang, $this->timeZone);
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_LEAVE_QUESTIONNAIRE'): //离职问卷
                $messageObj = new LeaveQuestionnaireMessage($this->lang, $this->timeZone);
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_COMPLETE_VAN_CONTAINER'): //转岗发送车厢信息
                $messageObj = new VanContainerCompleteMessage($this->lang, $this->timeZone);
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PAYROLL_FILE_2316'): //2316文件，签字消息
                $messageObj = new PayrollFileMessage($this->lang, $this->timeZone);
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_IC_SETTLEMENT_MSG'): //PH个人代理结算
                $messageObj = new PayrollFileMessage($this->lang, $this->timeZone);
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PENALTY_WARNING'): //虚假类处罚站内信
                $messageObj = new PenaltyWarningMessage($this->lang, $this->timeZone);
                break;
            case MessageEnums::MESSAGE_CATEGORY_71: 
                $messageObj = new ProbationAckMessage($this->lang, $this->timeZone);
                break;
            default:
                $messageObj = new CommonMessage($this->lang, $this->timeZone);
                break;
        }

        //离职、转移资产直接返回，无需任何处理
        if (in_array($message->getCategory(), [MessageEnums::MESSAGE_CATEGORY_STAFF_RESIGN_ASSET,
            MessageEnums::MESSAGE_CATEGORY_TRANSFER_ASSET,
            MessageEnums::MESSAGE_CATEGORY_70,
//            MessageEnums::MESSAGE_CATEGORY_71,
            ])) {
            return $message->toArray();
        } else {
            return $messageObj->init($messageData)->getMessageDetail();
        }
    }
}