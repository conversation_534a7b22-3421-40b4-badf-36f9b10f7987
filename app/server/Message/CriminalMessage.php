<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Server\CriminalServer;

class CriminalMessage extends BaseMessageServer
{
    protected $message_template = "<a href='%s' style='display: inline-block; cursor: pointer; margin-left: 10px;font-size: 15px;'>%s</a>";

    /**
     * @description 返回消息详情，返回MessageResponse对象的数组结构
     * @return array
     */
    public function getMessageDetail(): array
    {
        $criminalServer = new CriminalServer($this->lang);
        $response       = $this->getMessageResponseData();
        $isNeedSign     = $criminalServer->isNeedSign($response->getStaffInfoId(), $response->getMsgId());
        if ($isNeedSign) {
            $this->getNormalMessage();
        }
        return $this->getMessageResponseData()->toArray();
    }

    /**
     * @description 生成普通类型消息，消息内容iframe嵌入url
     * @param $message_url
     * @return string
     */
    protected function generateMessageContent($message_url): string
    {
        return $this->getMessageResponseData()->getContent() . sprintf($this->message_template, $message_url, $this->getTranslation()->_('warning_notice'));
    }


    protected function isNeedSetDetailUrl(): bool
    {
        return false;
    }
}