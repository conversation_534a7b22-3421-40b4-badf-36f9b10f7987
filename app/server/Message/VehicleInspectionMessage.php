<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Models\backyard\VehicleInspectionModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\VehicleInspectionServer;

class VehicleInspectionMessage extends BaseMessageServer
{
    /**
     * @description 生成url，用于嵌入iframe
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        return sprintf("%s%s?msg_id=%s&id=%s", env('h5_endpoint'), $this->getNormalMessageUrl(),
            $this->getMessageRequestData()->getMsgId(),
            $this->getMessageResponseData()->getContent()
        );
    }


    /**
     * @description 是否设置消息读取状态为已读，如果设置会改变消息的已读状态
     * true=需要更新已读状态，false=不更新消息已读状态
     *
     * @param $isSubmit
     * @return bool
     */
    protected function isSetReadStateToHasReadForStatus($isSubmit): bool
    {
        if (empty($isSubmit)) {
            return false;
        }
        if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD) {
            return true;
        }
        return false;
    }

    /**
     * @description 实现该方法，可自定义逻辑
     * @return void
     */
    public function afterExecute()
    {
        $isSubmit   = $this->vehicleInspectionIsSubmit();
        //设置已读
        if ($this->isSetReadStateToHasReadForStatus($isSubmit)) {
            $this->setMessageReadStatus($this->isNeedChangeTopState());
        }
        //是否展示提交按钮
        $this->getMessageResponseData()->setIsBtnShow(!$isSubmit);
    }

    /**
     * 是否已经提交
     * @return bool
     */
    private function vehicleInspectionIsSubmit(): bool
    {
        return (new VehicleInspectionServer())->getStatus($this->getMessageRequestData()->getStaffId()) == VehicleInspectionModel::STATUS_SUBMITTED;
    }

}