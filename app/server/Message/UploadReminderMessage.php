<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class UploadReminderMessage extends BaseMessageServer
{

    /**
     * @description 实现该方法，可自定义逻辑
     * @return void
     */
    protected function beforeExecute()
    {
        $response = $this->getMessageResponseData();
        $response->setCreateTime(date("Y-m-d H:i:s", $response->getPushTime()));
        $response->setRejectReason($response->getContent());
    }

    /**
     * @description 是否设置消息读取状态为已读，如果设置会改变消息的已读状态
     * true=需要更新已读状态，false=不更新消息已读状态
     *
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        //未读时设置已读
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD;
    }
}