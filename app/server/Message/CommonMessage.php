<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\BackyardServer;

/**
 * 用于处理简单的消息结构，如
 *
 * ---
 * $msg_detail_url = env('sign_url')."/#/xxx?msg_id={$msg_id}";
 * $data['content'] = "<...><iframe src='{$msg_detail_url}' width='100%' height='100%' scrolling='no' frameborder='0'></iframe><...>";
 * $server->has_read_operation($msg_id);
 * ---
 *
 * 存在嵌入iframe，设置消息已读
 *
 */
class CommonMessage extends BaseMessageServer
{
    /**
     * @description 获取普通消息
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        switch ($this->getMessageResponseData()->getCategory()) {
            case MessageEnums::MESSAGE_CATEGORY_TRANSFER_EVALUATION_SIGN_UP:
                $messageUrl = sprintf("%s/#/%s?msg_id=%s&probation_audit_id=%s&read_state=%d", env('sign_url'), $this->getNormalMessageUrl(),
                    $this->getMessageResponseData()->getMsgId(),
                    $this->getMessageResponseData()->getContent(),
                    $this->getMessageResponseData()->getReadState()
                );
                break;
            case MessageEnums::MESSAGE_CATEGORY_TP3:
            case MessageEnums::MESSAGE_CATEGORY_OFFER_SIGN_APPROVAL:
            case MessageEnums::MESSAGE_CATEGORY_SALARY_APPROVAL:
            case MessageEnums::MESSAGE_CATEGORY_TP3_REFILL:
                $messageUrl = sprintf("%s/#/%s?msg_id=%s&msg_category=%d", env('sign_url'), $this->getNormalMessageUrl(),
                    $this->getMessageResponseData()->getMsgId(),
                    $this->getMessageResponseData()->getCategory()
                );
                break;
            case MessageEnums::MESSAGE_CATEGORY_KPI_STAFF_CONFIRM:
                $messageUrl = sprintf("%s/#/%s?activity_id=%s", env('sign_url'), $this->getNormalMessageUrl(),
                    $this->getMessageResponseData()->getContent()
                );
                break;
            case MessageEnums::MESSAGE_CATEGORY_COURIER_COMMITMENT_LETTER:
            case MessageEnums::MESSAGE_CATEGORY_FEEDER:
            case MessageEnums::MESSAGE_CATEGORY_ARCHIVED:
            case MessageEnums::MESSAGE_CATEGORY_72:
            case MessageEnums::MESSAGE_CATEGORY_73:
            case MessageEnums::MESSAGE_CATEGORY_74:
            case MessageEnums::MESSAGE_CATEGORY_FLEET_SCHEDULE_NOTICE:
            case MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND1:
            case MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND2:
            case MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REJECT:
            case MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_AUTO_RECEPTION:
            case MessageEnums::MESSAGE_CATEGORY_TRAINING:
                $messageUrl = sprintf("%s/#/%s?msg_id=%s", env('sign_url'), $this->getNormalMessageUrl(),
                    $this->getMessageResponseData()->getMsgId()
                );
                break;
            case MessageEnums::MESSAGE_CATEGORY_RESUME_RECOMMEND:
            case MessageEnums::MESSAGE_CATEGORY_RESUME_RECOMMEND_CANCELED:
                $messageUrl = sprintf("%s/#/%s?msg_id=%s&related_id=%s", env('sign_url'), $this->getNormalMessageUrl(),
                    $this->getMessageResponseData()->getMsgId(),
                    $this->getMessageResponseData()->getRelatedId()
                );
                break;
            case MessageEnums::MESSAGE_CATEGORY_PLAN_STOCK_CHECK:
                $messageUrl = sprintf("%s/#/%s?plan_id=%s", env('sign_url'), $this->getNormalMessageUrl(),
                    $this->getMessageResponseData()->getContent()
                );
                break;
            case MessageEnums::MESSAGE_CATEGORY_FINE:
            case MessageEnums::MESSAGE_CATEGORY_LEAVE_ASSET:
                $messageUrl = $this->getMessageResponseData()->getContent();
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_INTERIOR_ORDER:
                $messageUrl = sprintf("%s/#%s", env('sign_url'), $this->getNormalMessageUrl());
                break;
            case MessageEnums::MESSAGE_CATEGORY_BANK_ACCOUNT:
                $messageUrl = sprintf("%s/#%s?type=%s", env('sign_url'), $this->getNormalMessageUrl(),$this->getMessageResponseData()->getContent());
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_FILL_BANK_CARD_ANNEX'):
                $messageUrl = sprintf("%s/#/%s?type=%s", env('sign_url'), $this->getNormalMessageUrl(),$this->getMessageResponseData()->getContent());
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_EPF_NO_NOTICE:
                $messageUrl = sprintf("%s/#%s?msg_id=%s", env('sign_url'), $this->getNormalMessageUrl(),$this->getMessageResponseData()->getMsgId());
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_EA_FORM_NOTICE:
                $messageUrl = sprintf("%s%s?msg_id=%s&year=%s",env('h5_endpoint'), $this->getNormalMessageUrl(),$this->getMessageResponseData()->getMsgId(),$this->getMessageResponseData()->getContent());
                break;
            case MessageEnums::MESSAGE_MY_PAYSLIP:
                $messageUrl = sprintf("%s%s?msg_id=%s&payslip_param=%s",env('h5_endpoint'), $this->getNormalMessageUrl(),$this->getMessageResponseData()->getMsgId(),$this->getMessageResponseData()->getContent());
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_SALARY_TAX:
            case MessageEnums::MESSAGE_CATEGORY_INSURANCE_BENEFICIARY:
            case EnumSingleton::getInstance()->getEnums('RECEIVED_NOT_PLACED_WAREHOUSE')://已揽收未入仓
            $messageUrl = env('h5_endpoint') . $this->getNormalMessageUrl(); // h5_endpoint=https://by-v3.flashexpress.my/
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_INQUIRING_ABOUT_TASKS:
                $messageUrl = env('h5_endpoint') . sprintf("%s?msg_id=%s&related_id=%s", $this->getNormalMessageUrl(),
                        $this->getMessageResponseData()->getMsgId(),
                        $this->getMessageResponseData()->getRelatedId()
                    );
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_ACCIDENT_REPORTING://18545 MY EHS事故上报v1.1
                $messageUrl = $this->getMessageResponseData()->getContent();//整个前端地址 包括 域名+参数
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_INVOICE://18809 个人代理invoice
                $messageUrl = env('h5_endpoint') . sprintf("%s?msg_id=%s&%s", $this->getNormalMessageUrl(),
                        $this->getMessageResponseData()->getMsgId(),
                        $this->getMessageResponseData()->getContent()
                    );
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_PAYMENT_VOUCHER://18907 付款凭证
                $messageUrl = env('h5_endpoint') . sprintf("%s?msg_id=%s&%s", $this->getNormalMessageUrl(),
                        $this->getMessageResponseData()->getMsgId(),
                        $this->getMessageResponseData()->getContent()
                    );
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_TAX_WITHHOLDING_VOUCHER://18907 代扣税凭证
                $messageUrl = env('h5_endpoint') . sprintf("%s?msg_id=%s&%s", $this->getNormalMessageUrl(),
                        $this->getMessageResponseData()->getMsgId(),
                        $this->getMessageResponseData()->getContent()
                    );
                break;
            case MessageEnums::MESSAGE_CATEGORY_AUTO_TRANSFER_TO_PERSONAL_AGENT:
            case MessageEnums::MESSAGE_CATEGORY_ATTENDANCE_MESSAGE_REMIND_TABLE:
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_COMPLETE_VAN_CONTAINER'):
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_STAGE_DONE'):
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_GOAL_REMIND'):
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_OCW_AGREEMENT'):
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_SEND_RESUME_RECOMMEND_SUBMIT_TA'):
            case MessageEnums::MESSAGE_CATEGORY_PROBATION_SEND_PDF:
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_CONFIRMATION_LETTER_BP'):
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_FORMAL'):
                $messageUrl = env('h5_endpoint') . sprintf('%s?msg_id=%s', $this->getNormalMessageUrl(), $this->getMessageResponseData()->getMsgId());
                break;
            case MessageEnums::MESSAGE_CATEGORY_TRANSFER_NOTICE:
            case MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_TRANSFER_REMINDER:
                $messageUrl = env('h5_endpoint') . sprintf('%s?batch_id=%s&category=%s', $this->getNormalMessageUrl(), $this->getMessageResponseData()->getContent(), $this->getMessageResponseData()->getCategory());
                break;
            case MessageEnums::MESSAGE_CATEGORY_ASSET_RETURN_FINISHED:
            case MessageEnums::MESSAGE_CATEGORY_ASSET_RETURN_REJECT:
            case MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_PENDING_RECEIPT_REMINDER:
                $messageUrl = env('h5_endpoint') . sprintf('%s?category=%s', $this->getNormalMessageUrl(), $this->getMessageResponseData()->getCategory());
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_BUSINESS_CONTRACT_NOTICE'):
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_BUSINESS_CONTRACT_LETTER_REJECT'):
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_FACE_BLACKLIST'):
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_NEW_GOODS_NOTICE'):
                $messageUrl = env('h5_endpoint') . sprintf("%s?msg_id=%s", $this->getNormalMessageUrl(), $this->getMessageResponseData()->getMsgId());
                break;
            //合同审核业务V2
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_BUSINESS_CONTRACT_APPLY_V2'):
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_BUSINESS_CONTRACT_APPLY_V3'):
                $messageUrl = env('h5_endpoint') . sprintf("%s?apply_id=%s", $this->getNormalMessageUrl(), $this->getMessageResponseData()->getContent());
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_TARGET')://试用期目标
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_TARGET_BP'): //试用期目标-BP
                $content    = json_decode($this->getMessageResponseData()->getContent(), true);
                $type       = $content['type'] ?? 1;
                $businessId = $content['business_id'] ?? 0;
                $messageUrl = env('h5_endpoint') . sprintf("%s?type=%s&business_id=%s&msg_id=%s", $this->getNormalMessageUrl(), $type, $businessId,$this->getMessageResponseData()->getMsgId());
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_WMS_APPLY_AUDIT_PASS')://耗材申请-审批通过
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_WMS_APPLY_AUDIT_REJECT')://耗材申请-审批驳回
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_ASSET_APPLY_AUDIT_PASS')://资产申请-审批通过
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_ASSET_APPLY_AUDIT_REJECT')://资产申请-审批驳回
                $messageUrl = env('h5_endpoint') . sprintf('%s?audit_id=%s&category=%s', $this->getNormalMessageUrl(), $this->getMessageResponseData()->getContent(), $this->getMessageResponseData()->getCategory());
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_SX_CARD')://TH神仙卡限制方案
                $content         = json_decode($this->getMessageResponseData()->getContent(), true);
                $content['biz_type'] = $this->getMessageResponseData()->getCategoryCode();
                $messageUrl             = env('h5_endpoint').sprintf("%s?msg_id=%s&",  $this->getNormalMessageUrl(),
                    $this->getMessageResponseData()->getMsgId());
                $messageUrl             .= http_build_query($content);
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_HUB_FULL_ATTENDANCE'):
                $messageUrl = env('h5_endpoint') . sprintf('%s?msg_id=%s&month=%s', $this->getNormalMessageUrl(), $this->getMessageResponseData()->getMsgId(), $this->getMessageResponseData()->getContent());
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PACKAGE_ALLOT_OUT_NOTICE')://耗材调拨单-耗材调出提醒
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PACKAGE_ALLOT_IN_NOTICE')://耗材调拨单-耗材调入提醒
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PACKAGE_ALLOT_CANCEL_NOTICE')://耗材调拨单-耗材调拨取消提醒
                $messageUrl = env('h5_endpoint') . sprintf('%s?id=%s&category=%s', $this->getNormalMessageUrl(), $this->getMessageResponseData()->getContent(), $this->getMessageResponseData()->getCategory());
                break;
            default:
                $messageUrl = "";
                break;

        }
        return $messageUrl;
    }

    /**
     * @description 是否设置消息读取状态为已读，如果设置会改变消息的已读状态
     * true=需要更新已读状态，false=不更新消息已读状态
     *
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        //排掉不需要设置已读的
        if (in_array($this->getMessageResponseData()->getCategory(), [
            MessageEnums::MESSAGE_CATEGORY_TRANSFER_EVALUATION_SIGN_UP,
            MessageEnums::MESSAGE_CATEGORY_TP3,
            MessageEnums::MESSAGE_CATEGORY_TP3_REFILL,
            MessageEnums::MESSAGE_CATEGORY_CODE_ACCIDENT_REPORTING,
            EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_TARGET'),
            EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_STAGE_DONE'),
            EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_OCW_AGREEMENT'),
            EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_SEND_RESUME_RECOMMEND_SUBMIT_TA'),
            EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_CONFIRMATION_LETTER_BP'),
            EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_FORMAL'),
            EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_GOAL_REMIND'),
            MessageEnums::MESSAGE_CATEGORY_PROBATION_SEND_PDF
        ])) {
            return false;
        }

        if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD) {
            return true;
        }
        return false;
    }

    /**
     * @description 是否需要更改置顶状态为不置顶，，可继承自定义逻辑
     * @return bool
     */
    protected function isNeedChangeTopState(): bool
    {
        if (in_array($this->getMessageResponseData()->getCategory(), [
                MessageEnums::MESSAGE_CATEGORY_CODE_INTERIOR_ORDER,
                MessageEnums::MESSAGE_CATEGORY_PLAN_STOCK_CHECK,
                MessageEnums::MESSAGE_CATEGORY_CODE_INQUIRING_ABOUT_TASKS,
            ])) {
            return true;
        }
        return false;
    }


    protected function isNeedSetDetailUrl(): bool
    {
        if (in_array($this->getMessageResponseData()->getCategory(), [
            MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND1,
            MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND2,
            MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REJECT,
            MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_AUTO_RECEPTION,
            MessageEnums::MESSAGE_CATEGORY_BANK_CARD_REFUSE_PH,
            MessageEnums::MESSAGE_CATEGORY_CODE_INTERIOR_ORDER,
        ])) {
            return false;
        }
        return true;
    }
}