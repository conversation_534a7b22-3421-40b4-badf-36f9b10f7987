<?php
namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class MaterialWmsMessage extends BaseMessageServer
{
    /**
     * @description 生成url，用于嵌入iframe
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        $content = explode(',', $this->getMessageResponseData()->getContent());
        return sprintf("%s/#/%s?apply_id=%s&audit_id=%s", env('sign_url'), $this->getNormalMessageUrl(),
            $content[0], ($content[1] ?? 0)
        );
    }

    /**
     * @description 是否设置消息读取状态为已读
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        //未读
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD;
    }
}