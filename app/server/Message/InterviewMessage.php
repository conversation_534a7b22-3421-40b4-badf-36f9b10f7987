<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\BackyardServer;

class InterviewMessage extends BaseMessageServer
{
    /**
     * @description 是否设置消息读取状态为已读
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        //未读时设置已读
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD;
    }

    /**
     * @description 是否更改页眉
     * @return bool
     */
    protected function isChangeTopContent(): bool
    {
        return true;
    }

    /**
     * @description 是否更改页眉
     * @return string
     */
    protected function changeTopContent(): string
    {
        return "";
    }

    /**
     * @description 是否更改页脚
     * @return bool
     */
    protected function isChangeFootContent(): bool
    {
        return true;
    }

    /**
     * @description 更改页脚内容，可自定义逻辑
     * @return string
     */
    public function changeFootContent(): string
    {
        return '';
    }
}