<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\BackyardServer;

/**
 * 问卷消息
 */
class QuestionnaireMessage extends BaseMessageServer
{
    /**
     * @description 获取消息详情
     * @return array
     */
    public function getMessageDetail(): array
    {
        $messageData = $this->getMessageResponseData();
        switch ($messageData->getCategoryCode()) {
            case 1:
                $this->registerMessage();
                break;
            case 3:
                $this->questionMessage();
                break;
        }
        return $this->getMessageResponseData()->toArray();
    }

    /**
     * @description 注册消息
     * @return void
     */
    private function registerMessage()
    {
        $messageData = $this->getMessageResponseData();
        
        //判断是否为用户注册消息
        $client      = new \Hprose\Http\Client(env('go_rpc_server', 'http://127.0.0.1:8051/svc/call'),
            false);
        $return_data = json_decode($client->GetStaffMobileInfo($messageData->getStaffInfoId()), true);
        $client->close();

        //是否绑定手机
        if (!empty($return_data["data"]["staff_mobile"])) {
            $this->setMessageReadStatus();
            $this->getMessageResponseData()->setReadState(MessageCourierModel::READ_STATE_HAS_READ);
        } elseif ($messageData->getReadState() == MessageCourierModel::READ_STATE_UNREAD) {
            //如果未提交，并且未读，提示必须提交
            $messageData->setMustSubmit(self::SUBMIT_STATE_MUST_SUBMIT);

            $messageData->setTopText($this->getTranslation()->_('You_have_a_new_form_not_submitted_please_submit_it_first'));
            $messageData->setFootText($this->getTranslation()->_('You_can,t_close_the_message_until_you_submit_the_form'));
        }
    }

    /**
     * @description 生成url，用于嵌入iframe
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        $messageData = $this->getMessageResponseData();
        if ($messageData->getCategoryCode() == 3) {
            return sprintf("%s/#/%s?msg_id=%s", env('sign_url'), $this->getNormalMessageUrl(),
                $messageData->getMessageContentId());
        } else {
            return "";
        }
    }

    /**
     * @description 问卷消息
     * @return void
     */
    private function questionMessage()
    {
        $messageData = $this->getNormalMessage();

        //获取问卷详情
        $server     = new BackyardServer($this->lang, $this->timeZone);
        $msgDetail = $server->get_questionnaire_detail(['remote_msg_id' => $messageData->getMessageContentId()]);

        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $curr_time = gmdate('Y-m-d H:i:s', time() + $add_hour * 3600);
        $curr_date = substr($curr_time, 0, 10);
        $end_date  = !empty($msgDetail['end_time']) ? substr($msgDetail['end_time'], 0, 10) : '';

        // 是否必填, 如果是必填，且当前日期 为 截止日期，则该问卷必填
        $feedbackSetting = $msgDetail['feedback_setting'] ?? 0;
        if ($feedbackSetting && ($curr_date >= $end_date) && $end_date) {
            $messageData->setMustSubmit(self::SUBMIT_STATE_MUST_SUBMIT);
            $messageData->setFootText($this->getTranslation()->_('msg_qn_072'));
        }
        //自愿填写，且当前时间已过问卷截止时间（无设置过期时间，默认过期），且消息状态为 未读
        if ($feedbackSetting == 0 && ($curr_time >= $msgDetail['end_time']) && $messageData->getReadState() == MessageCourierModel::READ_STATE_UNREAD) {
            $this->setMessageReadStatus();
            $this->getMessageResponseData()->setReadState(MessageCourierModel::READ_STATE_HAS_READ);
        }
    }
}