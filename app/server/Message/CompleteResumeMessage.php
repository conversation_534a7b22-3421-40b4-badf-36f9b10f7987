<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\MessageCenterRepository;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;

class CompleteResumeMessage extends BaseMessageServer
{

    //校验条件
    private function check(): bool
    {
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD && $this->delayMsgIsMustSubmit();
    }

    //设置必填
    protected function isSetFormStateToMustSubmit(): bool
    {
        return $this->check();
    }

    /**
     * @description 生成url，用于嵌入iframe
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        $is_lnt = 0;
        if (isCountry('MY') && (new StaffServer())->isLntStaff($this->getMessageResponseData()->getStaffInfoId())) {
            $is_lnt = 1;
        }
        return sprintf("%s/#/%s?msg_id=%s&staff_id=%d&is_lnt=%d",env('sign_url') , $this->getNormalMessageUrl(),
            $this->getMessageResponseData()->getMsgId(), $this->getMessageResponseData()->getStaffInfoId(),$is_lnt);
    }

    //设置页脚
    protected function isChangeFootContent(): bool
    {
        return $this->check();
    }

    //设置页脚文案
    protected function changeFootContent(): string
    {
        return $this->getTranslation()->_('resume_next');
    }

    protected function isSetReadStateToHasRead(): bool
    {
        //给完善信息消息置已读。
        //如果是支援账号，则找到其 主账号
        $response = $this->getMessageResponseData();
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timeZone))->getSupportInfoBySubStaff($response->getStaffInfoId());
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            $staff_id = $supportStaffInfo['staff_info_id'];
            //主账号肯定发过(子账号发送消息，将主账号的消息id 存入 content )
            $messageParams = ['id' => $response->getContentId(), 'staff_info_id' => $staff_id];
            $messageInfo = MessageCenterRepository::getMessageCourierInfo($messageParams);
            if(!empty($messageInfo) && $messageInfo['read_state'] == MessageCourierModel::READ_STATE_HAS_READ) {
                return true;
            }
        }

        return false;
    }

    /**
     * @description 实现该方法，可自定义逻辑
     * @return void
     */
    protected function beforeExecute()
    {
        //给完善信息消息 前端展示为已读（不是真实的已读状态）
        $response = $this->getMessageResponseData();
        //如果是支援账号，则找到其主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timeZone))->getSupportInfoBySubStaff($response->getStaffInfoId());
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            $staff_id = $supportStaffInfo['staff_info_id'];
            //主账号肯定发过(子账号发送消息，将主账号的消息id 存入 content )
            $messageParams = ['id' => $response->getContent(), 'staff_info_id' => $staff_id];
            $messageInfo = MessageCenterRepository::getMessageCourierInfo($messageParams);
            if(!empty($messageInfo) && $messageInfo['read_state'] == MessageCourierModel::READ_STATE_HAS_READ) {
                //如果主账号已读，直接给前端展示为已读。真正已读，还需要 在 isSetReadStateToHasRead 实现一下。
                $response->setReadState(MessageCourierModel::READ_STATE_HAS_READ);
            }
        }
    }

}