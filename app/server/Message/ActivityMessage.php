<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Enums\ActivityEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\ActivityServer;

/**
 * 用于处理简单的消息结构，如
 *
 * ---
 * $msg_detail_url = env('sign_url')."/#/xxx?msg_id={$msg_id}";
 * $data['content'] = "<...><iframe src='{$msg_detail_url}' width='100%' height='100%' scrolling='no' frameborder='0'></iframe><...>";
 * $server->has_read_operation($msg_id);
 * ---
 *
 * 存在嵌入iframe，设置消息已读
 *
 */
class ActivityMessage extends BaseMessageServer
{
    /**
     * @description 获取普通消息
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        switch ($this->getMessageResponseData()->getCategory()) {

            case MessageEnums::MESSAGE_CATEGORY_CODE_BIRTHDAY:
            case MessageEnums::MESSAGE_CATEGORY_CODE_ANNIVERSARY:
            case MessageEnums::MESSAGE_CATEGORY_CODE_FASTING:
            case MessageEnums::MESSAGE_CATEGORY_CODE_SONGGAN:
                $messageUrl = env('h5_endpoint') . $this->getNormalMessageUrl(); // h5_endpoint=https://by-v3.flashexpress.my/
                break;
            default:
                $messageUrl = "";
                break;

        }
        return $messageUrl;
    }

    /**
     * @description 是否设置消息读取状态为已读，如果设置会改变消息的已读状态
     * true=需要更新已读状态，false=不更新消息已读状态
     *
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD) {
            return true;
        }
        return false;
    }



    /**
     * @description 实现该方法，可自定义逻辑
     * @return void
     */
    public function afterExecute()
    {
        // 消息阅读成功后 计数
        if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD) {
            //对应业务统计已读数
            $birthdayServer = new ActivityServer($this->lang,$this->timeZone);
            $category = $this->getMessageResponseData()->getCategory();
            $typeCode = ActivityEnums::$categoryToCode[$category];
            $birthdayServer->readIncr($typeCode);
        }
        parent::afterExecute();
    }
}