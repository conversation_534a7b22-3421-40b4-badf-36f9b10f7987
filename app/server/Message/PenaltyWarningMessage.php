<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\MessageWarningModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\BackyardServer;

/**
 * PenaltyWarningMessage Class
 *
 * 处理虚假类处罚站内信的消息服务类。
 * 该类负责处理与处罚警告相关的消息，包括消息的读取状态、内容获取和数据处理。
 * 支持按区域、片区、门店和员工等多个维度进行数据统计和展示。
 *
 * @package FlashExpress\bi\App\Server\Message
 */
class PenaltyWarningMessage extends BaseMessageServer
{
    /**
     * 业务类型常量定义
     * 用于标识不同级别的业务实体
     */
    const BIZ_TYPE_REGION = '1';  // 区域级别
    const BIZ_TYPE_PIECE = '2';   // 片区级别
    const BIZ_TYPE_STORE = '3';   // 门店级别
    const BIZ_TYPE_STAFF = '4';   // 员工级别

    /**
     * 生成消息URL
     *
     * @return string 返回完整的消息URL地址
     */
    protected function generateMessageUrl(): string
    {
        return env('h5_endpoint') . sprintf(
            '%s?msg_id=%s',
            'penalty-warning',
            $this->getMessageResponseData()->getMsgId()
        );
    }

    /**
     * 检查消息状态
     *
     * @return bool 返回消息是否为未读状态
     */
    private function check(): bool
    {
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD;
    }

    /**
     * 判断是否需要设置消息为已读状态
     *
     * @return bool 返回是否需要设置为已读
     */
    protected function isSetReadStateToHasRead(): bool
    {
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD;
    }

    /**
     * 判断是否需要设置表单为必填
     *
     * @return bool 返回是否设置为必填
     */
    protected function isSetFormStateToMustSubmit(): bool
    {
        return $this->check();
    }

    /**
     * 判断是否需要更改顶部内容
     *
     * @return bool 返回是否更改顶部内容
     */
    protected function isChangeTopContent(): bool
    {
        return $this->check();
    }

    /**
     * 判断是否需要更改底部内容
     *
     * @return bool 返回是否更改底部内容
     */
    protected function isChangeFootContent(): bool
    {
        return $this->check();
    }

    /**
     * 获取消息内容
     *
     * @param array $paramIn 输入参数，包含消息ID等信息
     * @return array 返回解析后的消息内容
     */
    protected function getMessageContent($paramIn): array
    {
        $server      = new BackyardServer($this->lang, $this->timeZone);
        $messageInfo = $server->getMessageCourierDetailV2($paramIn);
        if (empty($messageInfo)) {
            return [];
        }
        return json_decode($messageInfo['content'], true);
    }

    /**
     * 从消息内容中获取数据
     *
     * @param array $paramIn 输入参数
     * @return array 返回处理后的数据，包含日期、处罚数据和警告数据
     */
    public function getDataFromContent($paramIn): array
    {
        $bindData    = $this->getMessageContent($paramIn);
        $penaltyData = $this->getPenaltyData($bindData);
        $warningData = $this->getWarningData($bindData);
        return [
            'date_at'      => $bindData['date_at'],
            'penalty_data' => $penaltyData,
            'warning_data' => $warningData,
        ];
    }

    /**
     * 根据查询参数获取详细信息
     *
     * @param array $paramIn 查询参数
     * @return array 返回处罚详细信息
     */
    public function getDetailFromQuery($paramIn): array
    {
        $bindData           = $this->getMessageContent($paramIn);
        $paramIn['date_at'] = $bindData['date_at'];
        return $this->getPenaltyDetail($paramIn);
    }

    /**
     * 获取处罚数据
     *
     * @param array $params 查询参数
     * @return array 返回处罚列表数据
     */
    protected function getPenaltyData($params)
    {
        $ac = new ApiClient('ard_api', '', 'abnormal.getMailGroupFakePunishList', $this->lang);
        $ac->setParams($params);
        $ac_result = $ac->execute();
        if ($ac_result['result']['code'] != 1) {
            throw new ValidationException($ac_result['result']['msg']);
        }
        return $ac_result['result']['data'] ?? [];
    }

    /**
     * 获取处罚详情
     *
     * @param array $params 查询参数
     * @return array 返回按类别分组的处罚列表
     * @throws ValidationException
     */
    protected function getPenaltyDetail($params)
    {
        $ac = new ApiClient('ard_api', '', 'abnormal.getMailGroupPunishListByCategory', $this->lang);
        $ac->setParams($params);
        $ac_result = $ac->execute();
        if ($ac_result['result']['code'] != 1) {
            throw new ValidationException($ac_result['result']['msg']);
        }
        return $ac_result['result']['data'] ?? [];
    }

    /**
     * 获取需要发送警告的门店类别
     *
     * @return array 返回门店类别ID数组
     */
    protected function getSendWarningWarningStoreCategory(): array
    {
        return ['1', '2', '10', '14'];
    }

    /**
     * 获取警告数据
     *
     * 该方法根据不同的业务类型（区域、片区、门店、快递员）
     * 统计并返回警告信息。支持按时间范围筛选，并对警告类型进行分组。
     *
     * @param array $params 包含查询条件的参数数组
     * @return array 返回格式化后的警告数据
     */
    public function getWarningData($params): array
    {
        $params['start_time']     = zero_time_zone($params['date_at']);
        $params['end_time']       = gmdate('Y-m-d H:i:s', strtotime($params['date_at']) + 86400);
        $case_warning_type_change = 'CASE warning_type WHEN 3 THEN 2 ELSE warning_type  END AS warning_type_grouped';
        $builder                  = $this->modelsManager->createBuilder();
        $builder->from(['wm' => MessageWarningModel::class]);
        $builder->innerJoin(SysStoreModel::class, 'store.id = wm.store_id', 'store');
        $builder->where(
            'wm.warning_type in ({warning_type:array}) and wm.is_delete = 0 and wm.type_code = :type_code:',
            ['warning_type' => [MessageWarningModel::WARNING_TYPE_1, MessageWarningModel::WARNING_TYPE_2, MessageWarningModel::WARNING_TYPE_3], 'type_code' => 't_warning_6']
        );
        $builder->andWhere(
            'wm.created_at >= :start_time: and wm.created_at < :end_time:',
            ['start_time' => $params['start_time'], 'end_time' => $params['end_time']]
        );
        $builder->andWhere(
            'store.category IN ({category:array})',
            ['category' => $this->getSendWarningWarningStoreCategory()]
        );
        if (!empty($params['is_courier'])) {
            $builder->columns([
                'wm.staff_info_id as name',
                $case_warning_type_change,
                'count(1) as total',
                'wm.staff_info_id as id',
            ]);
            $builder->andWhere('store.manage_piece = :piece_id:', ['piece_id' => $params['biz_value']]);
            $builder->groupBy('wm.staff_info_id,warning_type_grouped');
            $params['biz_type'] = self::BIZ_TYPE_STAFF;
        } else {
            switch ($params['biz_type']) {
                case self::BIZ_TYPE_REGION:
                    $builder->columns([
                        'region.name  as name',
                        $case_warning_type_change,
                        'count(1) as total',
                        'store.manage_region as id',
                    ]);
                    $builder->innerJoin(SysManageRegionModel::class, 'store.manage_region = region.id', 'region');
                    $builder->groupBy('store.manage_region,warning_type_grouped');
                    break;
                case self::BIZ_TYPE_PIECE:
                    $builder->columns([
                        'piece.name  as name',
                        $case_warning_type_change,
                        'count(1) as total',
                        'store.manage_piece as id',
                    ]);
                    $builder->innerJoin(SysManagePieceModel::class, 'store.manage_piece = piece.id', 'piece');
                    $builder->andWhere('store.manage_region = :region_id:', ['region_id' => $params['biz_value']]);
                    $builder->groupBy('store.manage_piece,warning_type_grouped');
                    break;
                case self::BIZ_TYPE_STORE:
                    $builder->columns([
                        'store.name  as name',
                        $case_warning_type_change,
                        'count(1) as total',
                        'store.id',
                    ]);
                    $builder->andWhere('store.manage_piece = :piece_id:', ['piece_id' => $params['biz_value']]);
                    $builder->groupBy('store.id,warning_type_grouped');
                    break;
            }
        }
        $builder->orderBy('total desc, warning_type_grouped asc');
        $data               = $builder->getQuery()->execute()->toArray();
        $result['list']     = $this->makeResult($data, !empty($params['is_courier']));
        $result['biz_type'] = $params['biz_type'];
        return $result;
    }

    /**
     * 格式化结果数据
     *
     * @param array $data 原始数据
     * @param bool $is_courier 是否为快递员数据
     * @return array 返回格式化后的结果
     */
    protected function makeResult($data, $is_courier): array
    {
        $result = [];
        if (empty($data)) {
            return $result;
        }
        $t = $this->getTranslation();
        foreach ($data as $item) {
            if ($is_courier) {
                $length       = strlen($item['name']);
                $start        = floor(($length - 3) / 2);
                $item['name'] = substr_replace($item['name'], '***', $start, 3);
            }

            $name = $item['name'];
            if (!isset($result[$item['id']])) {
                $result[$item['id']] = [
                    'name' => $name,
                    'list' => [],
                ];
            }
            $result[$item['id']]['list'][] = [
                'total_num'         => $item['total'],
                'warning_type_text' => $t->_('warning_type_' . $item['warning_type_grouped']),
            ];
        }
        return array_values($result);
    }
}
