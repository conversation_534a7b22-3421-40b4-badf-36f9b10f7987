<?php

namespace FlashExpress\bi\App\Server\Message;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\MessageServer;

/**
 * 警告书消息
 */
class WarnMessage extends BaseMessageServer
{
    /**
     * @description 获取消息详情
     * @return array
     */
    public function getMessageDetail(): array
    {
        $messageData = $this->getMessageResponseData();

        //获取相关签名
        $message_model = Tools::reBuildCountryInstance(new MessageServer($this->lang, $this->timeZone), [$this->lang, $this->timeZone]);
        $warning_info  = $message_model->getWarningMessage($messageData->getStaffInfoId(), $messageData->getMsgId());
        $link          = $this->getTranslation()->_('warning_notice');

        //给链接加一个签名
        $preg = '/warning_id=\d+/';
        preg_match($preg, $messageData->getContent(), $res);
        if ($res) {
            $warning_id      = explode("=", current($res))[1];
            $content         = explode('?', $messageData->getContent());
            $s               = "?s=".md5('flash-'.$warning_id)."&";
            $messageData->setContent($content[0].$s.$content[1]);
        }

        if ($this->getMessageResponseData()->getCategory() == MessageEnums::MESSAGE_CATEGORY_76) {
            $warning_info['is_need_sign'] = false;//发给er的警告书，不需要签字
            
            $this->afterExecute();
        }

        $backyard_model = Tools::reBuildCountryInstance(new BackyardServer($this->lang, $this->timeZone), [$this->lang, $this->timeZone]);
        return $backyard_model->warningMessageDetail($messageData->toArray(), $warning_info, $messageData->getMsgId(), $link);
    }

    protected function isSetReadStateToHasRead(): bool
    {
        if ($this->getMessageResponseData()->getCategory() == MessageEnums::MESSAGE_CATEGORY_76 && $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD) {
            return true;
        }
        return false;
    }

    /**
     * @description 是否需要更改置顶状态为不置顶，，可继承自定义逻辑
     * true=需要更新置顶状态，false=不更新置顶状态
     *
     * @return bool
     */
    protected function isNeedChangeTopState(): bool
    {
        return true;
    }

}