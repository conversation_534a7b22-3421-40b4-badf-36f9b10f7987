<?php

namespace FlashExpress\bi\App\Server\Message;


use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\BackyardServer;

/**
 * 证书消息
 */
class CertificateMessage extends BaseMessageServer
{
    public function generateMessageUrl(): string
    {
        return env('h5_endpoint') . sprintf('training-certificate-down-message?msg_id=%s',
                $this->getMessageResponseData()->getMsgId());
    }

    public function afterExecute()
    {
        // 消息阅读成功后，写入队列
        if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD) {
            $this->logger->write_log(['addHaveReadMsgToMns' => $this->getMessageResponseData()->getMsgId()], 'info');
            (new BackyardServer($this->lang,
                $this->timeZone))->addHaveReadMsgToMns($this->getMessageResponseData()->getMsgId());
        }
        parent::afterExecute();
    }

    protected function isSetReadStateToHasRead(): bool
    {
        if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD) {
            return true;
        }
        return false;
    }
}