<?php

namespace FlashExpress\bi\App\Server\Message;


use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Models\coupon\MessageContentModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\StaffServer;

/**
 * 已签收未收到包裹消息
 */
class UnReceiveParcelMessage extends BaseMessageServer
{

    /**
     * @description 组织URL
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        return env('h5_endpoint') . sprintf('%s', 'un-receive-parcel');
    }

    //校验条件
    private function check(): bool
    {
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD;
    }

    //设置必填
    protected function isSetFormStateToMustSubmit(): bool
    {
        return $this->check();
    }

    //设置页脚
    protected function isChangeTopContent(): bool
    {
        return $this->check();
    }

    //设置页脚
    protected function isChangeFootContent(): bool
    {
        return $this->check();
    }

    /**
     * @description 更改页眉内容，可自定义逻辑
     * @return mixed
     */
//    protected function changeTopContent(): string
//    {
//        return '';
//    }
//
//    //设置页脚文案
//    protected function changeFootContent(): string
//    {
//        return '';
//    }


    /**
     * 批量创建消息
     * @param $list
     * @return bool
     */
    public function batchCreatMessage($list): bool
    {
        if (empty($list)) {
            return false;
        }
        $category     = EnumSingleton::getInstance()->getEnums('UN_RECEIVE_PARCEL');

        foreach ($list as $message) {
            $this->createMessage($message['staff_info_id'], $message['num'],$category);
        }
        return true;
    }


    public function makeReadTopState($staff_info_id,$num)
    {
        $id = $this->makeMsgId($staff_info_id);
        $courierModel = MessageCourierModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id],
        ]);
        if (empty($courierModel)) {
            return false;
        }
        if ($num > 0) {
            $courierModel->top_state  = 1;
            $courierModel->read_state = 0;
        }else{
            $courierModel->top_state  = 0;
            $courierModel->read_state = 1;
        }
        $courierModel->title      = $this->makeMessageTitle($staff_info_id,$num);
        $courierModel->created_at = gmdate('Y-m-d H:i:s');
        return $courierModel->save();
    }


    public function makeMessageTitle($staff_info_id, $num)
    {
        $staffLang = (new StaffServer())->getLanguage($staff_info_id);
        $t         = $this->getTranslation($staffLang);
        return $t->_('un_receive_parcel_title', ['num' => $num]);
    }


    protected function makeMsgId($staff_info_id)
    {
        return '2082000000' . $staff_info_id;//该类型消息固定前缀

    }



    public function createMessage($staff_info_id, $num,$category)
    {
       $id = $this->makeMsgId($staff_info_id);
        $courierModel = MessageCourierModel::findFirst([
            'conditions' => 'id = :id: and category = :category:',
            'bind'       => ['id' => $id, 'category' => $category],
        ]);


        $messageTitle = $this->makeMessageTitle($staff_info_id, $num);
        if (empty($courierModel)) {
            $courierModel                     = new MessageCourierModel();
            $courierModel->id                 = $id;
            $courierModel->staff_info_id      = $staff_info_id;
            $courierModel->category           = $category;
            $courierModel->message_content_id = $id;
        }

        $contentModel = MessageContentModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id],
        ]);
        if (empty($contentModel)) {
            $contentModel                     = new MessageContentModel();
            $contentModel->id                 = $id;
            $contentModel->message_courier_id = $id;
        }
        $contentModel->created_at = gmdate('Y-m-d H:i:s');
        $contentModel->save();


        if ($num > 0) {
            $courierModel->top_state  = 1;
            $courierModel->read_state = 0;
        } else {
            $courierModel->top_state  = 0;
            $courierModel->read_state = 1;
        }
        $courierModel->created_at = gmdate('Y-m-d H:i:s');
        $courierModel->title      = $messageTitle;
        return $courierModel->save();
    }


}