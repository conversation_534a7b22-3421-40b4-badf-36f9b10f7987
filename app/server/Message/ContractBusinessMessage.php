<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class ContractBusinessMessage extends BaseMessageServer
{
    //校验条件
    private function check(): bool
    {
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD && $this->delayMsgIsMustSubmit();
    }

    //设置必填
    protected function isSetFormStateToMustSubmit(): bool
    {
        return $this->check();
    }

    //设置页脚
    protected function isChangeTopContent(): bool
    {
        return $this->check();
    }

    //设置页脚
    protected function isChangeFootContent(): bool
    {
        return $this->check();
    }

    /**
     * @description 更改页眉内容，可自定义逻辑
     * @return mixed
     */
    protected function changeTopContent(): string
    {
        return $this->getTranslation()->_('electronic_contract_business_clock_top_hint');
    }

    //设置页脚文案
    protected function changeFootContent(): string
    {
        return $this->getTranslation()->_('electronic_contract_business_clock_foot_hint');
    }

    /**
     * @description 生成url，用于嵌入iframe
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        //sprintf("%sasset-contract-agent?msg_id=%s", env('h5_endpoint'), $data['msg_id']);
        $content = explode(',', $this->getMessageResponseData()->getContent());
        return sprintf("%s%s?apply_id=%s", env('h5_endpoint'), $this->getNormalMessageUrl(),
            $content[0]
        );
    }
}