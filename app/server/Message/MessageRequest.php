<?php

namespace FlashExpress\bi\App\Server\Message;

class MessageRequest
{
    private $msg_id;

    private $staff_id;

    private $is_know;

    /**
     * @description
     * @param array $paramIn
     * @return $this
     */
    public function init(array $paramIn =[]): MessageRequest
    {
        $this->setMsgId($paramIn['msg_id']);
        $this->setStaffId($paramIn['staff_id']);
        $this->setIsKnow($paramIn['is_know']);
        return $this;
    }

    /**
     * @return mixed
     */
    public function getMsgId()
    {
        return $this->msg_id;
    }

    /**
     * @param mixed $msg_id
     */
    private function setMsgId($msg_id): void
    {
        $this->msg_id = $msg_id;
    }

    /**
     * @return mixed
     */
    public function getStaffId()
    {
        return $this->staff_id;
    }

    /**
     * @param mixed $staff_id
     */
    private function setStaffId($staff_id): void
    {
        $this->staff_id = $staff_id;
    }

    /**
     * @return mixed
     */
    public function getIsKnow()
    {
        return $this->is_know;
    }

    /**
     * @param mixed $is_know
     */
    private function setIsKnow($is_know): void
    {
        $this->is_know = $is_know;
    }
}