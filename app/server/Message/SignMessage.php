<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\BackyardServer;

/**
 * 签字消息
 */
class SignMessage extends BaseMessageServer
{
    /**
     * @description 获取消息详情
     * @return array
     */
    public function getMessageDetail(): array
    {
        //嵌入iframe
        $messageData = $this->getNormalMessage();

        // 获取签名信息
        $server         = new BackyardServer($this->lang, $this->timeZone);
        $sign_msg_param = [
            'remote_message_id' => $messageData->getMessageContentId(),
            'staff_info_id'     => $messageData->getStaffInfoId(),
        ];
        $signResult     = $server->get_sign_msg_result($sign_msg_param);
        if (empty($signResult['sign_url'])) {
            if ($this->check()){
                $messageData->setMustSubmit(self::SUBMIT_STATE_MUST_SUBMIT);
                $messageData->setTopText($this->getTranslation()->_('sign_msg_028'));
            }
        } else {
            $messageData->getReadState() == MessageCourierModel::READ_STATE_UNREAD && $server->has_read_operation($messageData->getMsgId());
        }

        // 我已知悉并同意
        if ($this->check()){
            $messageData->setFootText($this->getTranslation()->_('sign_msg_027'));
        }

        return $this->getMessageResponseData()->toArray();
    }

    /**
     * @description 组织URL
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        return $this->getSignMessageUrl($this->getMessageResponseData()->toArray());
    }

    /**
     * @description 获取
     * @param $data
     * @return string
     */
    public function getSignMessageUrl($data): string
    {
        $categoryCode = $data['category_code'] ?? 0;
        switch ($categoryCode) {
            case MessageEnums::MESSAGE_CATEGORY_CODE_LATE:
            case MessageEnums::MESSAGE_CATEGORY_CODE_EARLY:
            case MessageEnums::MESSAGE_CATEGORY_CODE_ABSENT:
                $url = sprintf("%s/#/punish?msg_id=%s", env('sign_url'), $data['msg_id']);
                break;
            case MessageEnums::CATEGORY_SIGN_CODE_PUNISH:
                $url = sprintf("%s/#/staff-penalty-notice?msg_id=%s", env('sign_url'), $data['msg_id']);
                break;
           case MessageEnums::CATEGORY_SIGN_CODE_AGREEMENT:
                $url = sprintf("%s/#/foreignSignature?msg_id=%s", env('sign_url'), $data['msg_id']);
                break;
           case MessageEnums::CATEGORY_SIGN_CODE_CONTRACT_EXPIRE:
                $url = sprintf("%sresignation-upon-expiration-message?msg_id=%s", env('h5_endpoint'), $data['msg_id']);
                break;
            case MessageEnums::CATEGORY_SIGN_CODE_ASSET_INDEPENDENT:
                $url = sprintf("%sasset-contract-message?msg_id=%s", env('h5_endpoint'), $data['msg_id']);
                break;
            case MessageEnums::CATEGORY_SIGN_CODE_ASSET_CONTRACT_AGENT:
                $url = sprintf("%sasset-contract-agent?msg_id=%s", env('h5_endpoint'), $data['msg_id']);
                break;
           case MessageEnums::CATEGORY_SIGN_CODE_BANK_ACCOUNT_PH:
                $url = sprintf("%s/#/bankTipNews?msg_id=%s", env('sign_url'), $data['msg_id']);
                break;
            case MessageEnums::CATEGORY_SIGN_CODE_MOBILE_DC_DSHEET:
                $url = sprintf("%sdc-sheet?msg_id=%s", env('h5_endpoint'), $data['msg_id']);
                break;
            case EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_SIGN_CONFIRM'): //合同签署生效通知
                $url = sprintf("%sperson-contract-expires-sign-msg?msg_id=%s", env('h5_endpoint'), $data['msg_id']);
                break;
            case EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_SCHOOL_CERTIFICATE_SEND'): //培训系统学习计划完成后的证书签字消息
                $url = sprintf("%straining-certificate-message?msg_id=%s", env('h5_endpoint'), $data['msg_id']);
                break;
            case EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_PDPA'): //合同pdpa消息
            case EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_BEBAS'): //合同bebas消息
                $url = sprintf("%spdpa-message?msg_id=%s", env('h5_endpoint'), $data['msg_id']);
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_SEND_OS_STAFF_CONTRACT')://21322【TH|BY|消息】 外协仓管自动发送合同
                $url = sprintf("%sflash-out-contract-massage?msg_id=%s", env('h5_endpoint'), $data['msg_id']);
                break;
            case EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_WORKDAY_SETTING_CHANGE')://22217轮休建议 全勤操作 发送给员工 签字消息
                $url = sprintf("%s/#/signNewsInfo?msg_id=%s", env('sign_url'), $data['msg_id']);
                break;
            default:
                $url = sprintf("%s/#/signNewsInfo?msg_id=%s", env('sign_url'), $data['msg_id']);
                break;
        }
        return $url;
    }

    /**
     * 检查是否走延时消息
     * @return bool
     */
    private function check(): bool
    {
        if (
            isCountry('MY')
            && ($this->getMessageResponseData()->getCategoryCode() == MessageEnums::CATEGORY_SIGN_CODE_ASSET_CONTRACT_AGENT))
        {
            return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD && $this->delayMsgIsMustSubmit();
        }

        return true;
    }
}