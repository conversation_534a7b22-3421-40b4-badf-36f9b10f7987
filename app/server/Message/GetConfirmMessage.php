<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class GetConfirmMessage extends BaseMessageServer
{
    /**
     * @description 是否更改页脚
     * @return bool
     */
    protected function isChangeFootContent(): bool
    {
        //未读 && 点击我知道了 || 已读
        if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD &&
            $this->getMessageRequestData()->getIsKnow() == MessageEnums::MESSAGE_HAS_NOT_CLICK_KNOWN ||
            $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_HAS_READ
        ) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @description 更改页脚内容，可自定义逻辑
     * @return string
     */
    public function changeFootContent(): string
    {
        //未读 && 点击我知道了 || 已读
        if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD &&
            $this->getMessageRequestData()->getIsKnow() == MessageEnums::MESSAGE_HAS_NOT_CLICK_KNOWN
        ) {
            return $this->getTranslation()->_('staff_shift_message_1');
        } else if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_HAS_READ) {
            return $this->getTranslation()->_('staff_shift_message_2');
        } else {
            return $this->getMessageResponseData()->getFootText();
        }
    }

    /**
     * @description 是否设置消息读取状态为已读
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        //未读 && 已经点击“我知道了”
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD &&
            $this->getMessageRequestData()->getIsKnow() == MessageEnums::MESSAGE_HAS_CLICK_KNOWN;
    }

    /**
     * @description 是否设置消息提交状态为必须提交
     * @return bool
     */
    protected function isSetFormStateToMustSubmit(): bool
    {
        //未读 && 没有点击“我知道了”
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD &&
            $this->getMessageRequestData()->getIsKnow() == MessageEnums::MESSAGE_HAS_NOT_CLICK_KNOWN;
    }
}