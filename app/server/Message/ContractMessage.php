<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class ContractMessage extends BaseMessageServer
{

    //校验条件
    private function check(): bool
    {
        // 如果电子合同未全部签字完成，则为必须提交状态
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD && $this->delayMsgIsMustSubmit();
    }

    //设置必填
    protected function isSetFormStateToMustSubmit(): bool
    {
        return $this->check();
    }

    //设置页脚
    protected function isChangeTopContent(): bool
    {
        return $this->check();
    }

    //设置页脚
    protected function isChangeFootContent(): bool
    {
        return $this->check();
    }

    /**
     * @description 更改页眉内容，可自定义逻辑
     * @return mixed
     */
    protected function changeTopContent(): string
    {
        return $this->getTranslation()->_('electronic_contract_clock_top_hint');
    }


    //设置页脚文案
    protected function changeFootContent(): string
    {
        return $this->getTranslation()->_('electronic_contract_clock_foot_hint');
    }
}