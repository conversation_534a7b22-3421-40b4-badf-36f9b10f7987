<?php

namespace FlashExpress\bi\App\Server\Message;

class ProbationAckMessage extends BaseMessageServer
{
    /**
     * @description 获取消息详情
     * @return array
     */
    public function getMessageDetail(): array
    {
        $response = $this->getMessageResponseData();
        $content = $response->getContent();
        if (preg_match('/<iframe\s+.*?src\s*=\s*[\'"](https?:\/\/[^\'"]+)[\'"]/i', $content, $matches)) {
            $response->setMsgDetailUrl($matches[1]);
            return $this->getMessageResponseData()->toArray();
        } else {
            return $this->getMessageResponseData()->toArray();
        }
    }
}