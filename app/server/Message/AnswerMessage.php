<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class AnswerMessage extends BaseMessageServer
{
    /**
     * @description 生成url，用于嵌入iframe
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        return sprintf("%s/#/%s?msg_id=%s", env('sign_url'), $this->getNormalMessageUrl(),
            $this->getMessageResponseData()->getMessageContentId());
    }

    //设置页脚
    protected function isChangeFootContent(): bool
    {
        return true;
    }

    //设置页脚文案
    protected function changeFootContent(): string
    {
        return $this->getTranslation()->_('answer_msg_100');
    }

    //设置必填
    protected function isSetFormStateToMustSubmit(): bool
    {
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD;
    }
}