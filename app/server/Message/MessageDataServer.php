<?php

namespace FlashExpress\bi\App\Server\Message;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Models\backyard\MessageModel;
use FlashExpress\bi\App\Models\coupon\MessageContentModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\BaseServer;

class MessageDataServer extends BaseServer
{
    private $message_response; //消息response详情
    private $message_request; //消息request详情

    public function __construct($lang = 'zh-CN')
    {
        parent::__construct($lang);
    }

    /**
     * @description 初始化
     * @param array $params
     * @return $this
     */
    public function init(array $params = []): MessageDataServer
    {
        $this->initMessageRequest($params);
        $this->initMessageResponse();
        return $this;
    }

    private function initMessageRequest($params)
    {
        $this->setMessageRequest((new MessageRequest())->init($params));
    }

    /**
     * @description 初始化消息详情
     * @return void
     */
    private function initMessageResponse()
    {
        //获取消息详情
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('mc.id as msg_id
                ,mc.title
                ,UNIX_TIMESTAMP(m.updated_at) as push_time
                ,mc.read_state
                ,m.message content
                ,m.related_id
                ,mc.staff_info_id
                ,mc.category
                ,mc.category_code
                ,mc.message_content_id
                ,mc.by_show_type
                ,mc.update_state');
        $builder->from(['mc' => MessageCourierModel::class]);
        $builder->innerJoin(MessageContentModel::class, 'mc.message_content_id = m.id', 'm');
        $builder->andWhere("mc.id = :message_id:", ['message_id' => $this->getMessageRequest()->getMsgId()]);
        $builder->andWhere("mc.is_del = 0");
        $messageInfo = $builder->getQuery()->getSingleResult();
        if (empty($messageInfo)) {
            $this->setMessageResponse(new MessageResponse());
            return;
        }
        $messageInfo = $messageInfo->toArray();

        $server = Tools::reBuildCountryInstance(new BackyardServer($this->lang, $this->timeZone), [$this->lang, $this->timeZone]);
        $all_undone_msg = $server->get_new_msg(['staff_id' => $this->getMessageRequest()->getStaffId(),'request_channel'=>'need_delay_message']);
        if (!empty($all_undone_msg[array_search($this->getMessageRequest()->getMsgId(), $all_undone_msg) + 1])) {
            $next_msg_id = $all_undone_msg[array_search($this->getMessageRequest()->getMsgId(), $all_undone_msg) + 1];
        } else {
            $next_msg_id = '';
        }
        $messageInfo['read_duration'] = 0;
        if (in_array($messageInfo['category'], [
            MessageEnums::MESSAGE_CATEGORY_ORDINARY,
            MessageEnums::MESSAGE_CATEGORY_QUESTIONNAIRE,
            MessageEnums::MESSAGE_CATEGORY_SIGN,
            MessageEnums::MESSAGE_CATEGORY_ANSWER_QUESTION,
        ]) && !empty($messageInfo['message_content_id'])) {
            $messageModel = MessageModel::findFirst([
                'columns'    => 'by_show_type,read_duration',
                'conditions' => 'remote_message_id = :remote_message_id:',
                'bind'       => ['remote_message_id' => $messageInfo['message_content_id']],
            ]);
            if (!empty($messageModel)) {
                $messageInfo['by_show_type'] = $messageModel->by_show_type;
                $messageInfo['read_duration'] = intval($messageModel->read_duration);
            }
        }


        $messageInfo['next_msg_id'] = $next_msg_id;
        $messageInfo['must_submit'] = 0;
        $messageInfo['top_text'] = $this->getTranslation()->_('You_have_a_new_message_that_is_not_read_please_read_the_clock_again');
//        $messageInfo['foot_text'] = $this->getTranslation()->_('closed_after_reading');
        $messageInfo['foot_text'] = empty($next_msg_id) ? $this->getTranslation()->_('message_close') : $this->getTranslation()->_('message_next');

        // content 去掉转义字符
        $messageInfo['title'] = stripslashes($messageInfo['title']);
        $messageInfo['content'] = stripslashes($messageInfo['content']);

        $this->setMessageResponse((new MessageResponse())->init($messageInfo));
    }

    /**
     * @return mixed
     */
    public function getMessageResponse(): MessageResponse
    {
        return $this->message_response;
    }

    /**
     * @param mixed $message_response
     */
    public function setMessageResponse($message_response): void
    {
        $this->message_response = $message_response;
    }

    /**
     * @return mixed
     */
    public function getMessageRequest(): MessageRequest
    {
        return $this->message_request;
    }

    /**
     * @param mixed $message_request
     */
    public function setMessageRequest($message_request): void
    {
        $this->message_request = $message_request;
    }
}