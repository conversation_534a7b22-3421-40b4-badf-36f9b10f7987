<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Server\BaseServer;

class MessageResponse
{
    private $msg_id;
    private $title;
    private $push_time;
    private $read_state;
    private $content;
    private $content_id;
    private $related_id;
    private $staff_info_id;
    private $category;
    private $category_code;
    private $message_content_id;
    private $by_show_type;
    private $update_state;
    private $next_msg_id;
    private $must_submit;
    private $top_text;
    private $foot_text;
    private $msg_detail_url;

    private $create_time;
    private $reject_reason;
    private $is_btn_show;
    private $read_duration;

    /**
     * @description 初始化MessageResponse对象
     * @param array $paramIn
     * @return $this
     */
    public function init(array $paramIn =[]): MessageResponse
    {
        $this->setMsgId($paramIn['msg_id']);
        $this->setTitle($paramIn['title']);
        $this->setPushTime($paramIn['push_time']);
        $this->setReadState($paramIn['read_state']);

        //content用于返回content字段
        $this->setContent($paramIn['content']);
        //content_id用于保存message_content 表 message 字段
        $this->setContentId($paramIn['content']);
        $this->setRelatedId($paramIn['related_id']);
        $this->setStaffInfoId($paramIn['staff_info_id']);
        $this->setCategory($paramIn['category']);
        $this->setCategoryCode($paramIn['category_code']);
        $this->setMessageContentId($paramIn['message_content_id']);
        $this->setByShowType($paramIn['by_show_type']);
        $this->setUpdateState($paramIn['update_state']);
        $this->setNextMsgId($paramIn['next_msg_id']);
        $this->setMustSubmit($paramIn['must_submit']);
        $this->setTopText($paramIn['top_text']);
        $this->setFootText($paramIn['foot_text']);
        $this->setMsgDetailUrl($paramIn['message_detail_url'] ?? "");
        $this->setCreateTime("");
        $this->setRejectReason("");
        $this->setIsBtnShow($paramIn['is_btn_show'] ?? false);
        $this->setReadDuration($paramIn['read_duration'] ?? 0);
        return $this;
    }

    /**
     * @description 转化为数组返回
     * @return array
     */
    public function toArray(): array
    {
        return get_object_vars($this);
    }

    /**
     * @return mixed
     */
    public function getMsgId()
    {
        return $this->msg_id;
    }

    /**
     * @param mixed $msg_id
     */
    public function setMsgId($msg_id): MessageResponse
    {
        $this->msg_id = $msg_id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @param mixed $title
     */
    public function setTitle($title): MessageResponse
    {
        $this->title = $title;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getPushTime()
    {
        return $this->push_time;
    }

    /**
     * @param mixed $push_time
     */
    public function setPushTime($push_time): MessageResponse
    {
        $this->push_time = $push_time;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getReadState()
    {
        return $this->read_state;
    }

    /**
     * @param mixed $read_state
     */
    public function setReadState($read_state): MessageResponse
    {
        $this->read_state = $read_state;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * @param mixed $content
     */
    public function setContent($content): MessageResponse
    {
        $this->content = $content;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getRelatedId()
    {
        return $this->related_id;
    }

    /**
     * @param mixed $related_id
     */
    public function setRelatedId($related_id): MessageResponse
    {
        $this->related_id = $related_id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getStaffInfoId()
    {
        return $this->staff_info_id;
    }

    /**
     * @param mixed $staff_info_id
     */
    public function setStaffInfoId($staff_info_id): MessageResponse
    {
        $this->staff_info_id = $staff_info_id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getCategory()
    {
        return $this->category;
    }

    /**
     * @param mixed $category
     */
    public function setCategory($category): MessageResponse
    {
        $this->category = $category;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getCategoryCode()
    {
        return $this->category_code;
    }

    /**
     * @param mixed $category_code
     */
    public function setCategoryCode($category_code): MessageResponse
    {
        $this->category_code = $category_code;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getMessageContentId()
    {
        return $this->message_content_id;
    }

    /**
     * @param mixed $message_content_id
     */
    public function setMessageContentId($message_content_id): MessageResponse
    {
        $this->message_content_id = $message_content_id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getByShowType()
    {
        return $this->by_show_type;
    }

    /**
     * @param mixed $by_show_type
     */
    public function setByShowType($by_show_type): MessageResponse
    {
        $this->by_show_type = $by_show_type;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getUpdateState()
    {
        return $this->update_state;
    }

    /**
     * @param mixed $update_state
     */
    public function setUpdateState($update_state): MessageResponse
    {
        $this->update_state = $update_state;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getNextMsgId()
    {
        return $this->next_msg_id;
    }

    /**
     * @param mixed $next_msg_id
     */
    public function setNextMsgId($next_msg_id): MessageResponse
    {
        $this->next_msg_id = $next_msg_id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getMustSubmit()
    {
        return $this->must_submit;
    }

    /**
     * @param mixed $must_submit
     */
    public function setMustSubmit($must_submit): MessageResponse
    {
        $this->must_submit = $must_submit;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getTopText()
    {
        return $this->top_text;
    }

    /**
     * @param string $top_text
     * @return MessageResponse
     */
    public function setTopText(string $top_text): MessageResponse
    {
        $this->top_text = $top_text;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getFootText()
    {
        return $this->foot_text;
    }

    /**
     * @param string $foot_text
     * @return MessageResponse
     */
    public function setFootText(string $foot_text): MessageResponse
    {
        $this->foot_text = $foot_text;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getMsgDetailUrl()
    {
        return $this->msg_detail_url;
    }

    /**
     * @param mixed $msg_detail_url
     */
    public function setMsgDetailUrl($msg_detail_url): MessageResponse
    {
        $this->msg_detail_url = $msg_detail_url;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getCreateTime()
    {
        return $this->create_time;
    }

    /**
     * @param mixed $create_time
     */
    public function setCreateTime($create_time): MessageResponse
    {
        $this->create_time = $create_time;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getRejectReason()
    {
        return $this->reject_reason;
    }

    /**
     * @param mixed $reject_reason
     */
    public function setRejectReason($reject_reason): MessageResponse
    {
        $this->reject_reason = $reject_reason;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getIsBtnShow()
    {
        return $this->is_btn_show;
    }

    /**
     * @param mixed $is_btn_show
     */
    public function setIsBtnShow($is_btn_show): MessageResponse
    {
        $this->is_btn_show = $is_btn_show;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getContentId()
    {
        return $this->content_id;
    }

    /**
     * @param mixed $content_id
     */
    public function setContentId($content_id): MessageResponse
    {
        $this->content_id = $content_id;
        return $this;
    }

    /**
     * @description 读取时长
     * @return mixed
     */
    public function getReadDuration()
    {
        return $this->read_duration;
    }

    /**
     * @description 设置读取时长
     * @param mixed $this->read_duration
     */
    public function setReadDuration($read_duration): MessageResponse
    {
        $this->read_duration = $read_duration;
        return $this;
    }
}