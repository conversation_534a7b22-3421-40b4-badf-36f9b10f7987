<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class NormalH5Message extends BaseMessageServer
{
    /**
     * @description 是否更改页脚
     * true=需要更新页脚，false=不更新消息页脚
     *
     * @return bool
     */
    protected function isChangeFootContent(): bool
    {
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD;
    }

    /**
     * @description 更改页脚内容，可自定义返回内容
     * @return string
     */
    protected function changeFootContent(): string
    {
        return $this->getTranslation()->_('staff_shift_message_1');
    }

    /**
     * @description 是否设置消息提交状态为必须提交
     * true=需要更新必须提交状态，false=不更新消息必须提交状态
     *
     * @return bool
     */
    protected function isSetFormStateToMustSubmit(): bool
    {
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD;
    }
}