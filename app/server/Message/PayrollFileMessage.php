<?php
/**
 * Author: Bruce
 * Date  : 2025-01-17 16:43
 * Description:
 */

namespace FlashExpress\bi\App\Server\Message;


use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class PayrollFileMessage extends BaseMessageServer
{
    /**
     * @description 生成url，用于嵌入iframe 【!!! 如果返回空，则返回content显示内容为数据库中message_content表中message字段内容】
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        switch ($this->getMessageResponseData()->getCategory()) {
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PAYROLL_FILE_2316')://2316文件，签字消息
                $messageUrl = env('h5_endpoint') . sprintf("%s?msg_id=%s&id=%s", $this->getNormalMessageUrl(),
                        $this->getMessageResponseData()->getMsgId(),
                        $this->getMessageResponseData()->getContent()
                    );
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_IC_SETTLEMENT_MSG')://PH个人代理结算，签字消息
                $messageUrl = env('h5_endpoint') . sprintf("%s?msg_id=%s&id=%s", $this->getNormalMessageUrl(),
                        $this->getMessageResponseData()->getMsgId(),
                        $this->getMessageResponseData()->getContent()
                    );
                break;
            default:
                $messageUrl = '';
                break;
        }

        return $messageUrl;
    }

    //设置必填
    protected function isSetFormStateToMustSubmit(): bool
    {
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD;
    }

    //设置页脚
    protected function isChangeTopContent(): bool
    {
        return true;
    }

    //设置页脚
    protected function isChangeFootContent(): bool
    {
        return true;
    }

    /**
     * @description 更改页眉内容，可自定义逻辑
     * @return mixed
     */
    protected function changeTopContent(): string
    {
        return $this->getTranslation()->_('sign_msg_028');
    }

    //设置页脚文案
    protected function changeFootContent(): string
    {
        return $this->getTranslation()->_('sign_msg_027');
    }

}