<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class LeaveAssetMessage extends BaseMessageServer
{

    /**
     * @description 生成url，用于嵌入iframe
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        return '';
    }

    /**
     * @description 是否设置消息读取状态为已读，如果设置会改变消息的已读状态
     * true=需要更新已读状态，false=不更新消息已读状态
     *
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD;
    }
}