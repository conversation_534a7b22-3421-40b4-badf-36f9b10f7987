<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class VanContainerMessage extends BaseMessageServer
{
    /**
     * @description 获取普通消息
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        $messageUrl = env('h5_endpoint') . $this->getMessageResponseData()->getContent(); // h5_endpoint=https://by-v3.flashexpress.my/
        return $messageUrl;

    }

    /**
     * @description 是否设置消息读取状态为已读，如果设置会改变消息的已读状态
     * true=需要更新已读状态，false=不更新消息已读状态
     *
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD) {
            return true;
        }
        return false;
    }

}