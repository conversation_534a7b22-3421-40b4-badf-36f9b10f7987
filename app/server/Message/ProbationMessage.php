<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\ProbationServer;

class ProbationMessage extends BaseMessageServer
{
    private $probation_count;

    /**
     * @description 各个继承子类定义加载项
     * @return void
     */
    protected function loadIndividualConfig()
    {
        $probationCount = (new ProbationServer($this->lang,
            $this->timeZone))->getCountAuditStatus($this->getMessageResponseData()->getStaffInfoId());
        $this->setProbationCount($probationCount);
    }

    /**
     * @description 是否更改页脚
     * @return bool
     */
    protected function isChangeFootContent(): bool
    {
        //未读 && 没有未处理的转正评估
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD && !empty($this->getProbationCount());
    }

    /**
     * @description 更改页脚内容，可自定义逻辑
     * @return string
     */
    public function changeFootContent(): string
    {
        return $this->getTranslation()->_('hr_probation_reminder_button');
    }

    /**
     * @description 是否设置消息读取状态为已读
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        //未读 && 没有未处理的转正评估
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD && empty($this->getProbationCount());
    }

    /**
     * @description 是否设置消息提交状态为必须提交
     * @return bool
     */
    protected function isSetFormStateToMustSubmit(): bool
    {
        //未读 && 没有未处理的转正评估
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD && !empty($this->getProbationCount());
    }

    /**
     * @return mixed
     */
    public function getProbationCount()
    {
        return $this->probation_count;
    }

    /**
     * @param mixed $probation_count
     */
    public function setProbationCount($probation_count): void
    {
        $this->probation_count = $probation_count;
    }
}