<?php

namespace FlashExpress\bi\App\Server\Message;


use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\library\MobileHelper;
use FlashExpress\bi\App\Models\backyard\OsStaffContractSignRecordModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class NormalMessage extends BaseMessageServer
{

    /**
     * @description 是否设置消息读取状态为已读，如果设置会改变消息的已读状态
     * true=需要更新已读状态，false=不更新消息已读状态
     *
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        return true;
    }

    /**
     * @description 生成url，用于嵌入iframe 【!!! 如果返回空，则返回content显示内容为数据库中message_content表中message字段内容】
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        switch ($this->getMessageResponseData()->getCategory()) {
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_AGENT_CONTRACT_ATTACHMENT')://MY IC和兼职IC签署合同,合同附件
                $messageUrl = env('h5_endpoint') . sprintf("%s?msg_id=%s", $this->getNormalMessageUrl(), $this->getMessageResponseData()->getMsgId());
                break;
            default:
                $messageUrl = sprintf("%s/#/%s?msg_id=%s", env('sign_url'), $this->getNormalMessageUrl(), $this->getMessageResponseData()->getMsgId());;
                break;
        }

         return $messageUrl;
    }

    /**
     * @description 是否更改页脚
     * @return bool
     */
    protected function isChangeFootContent(): bool
    {
        return true;
    }

    /**
     * @description 更改页脚内容，可自定义逻辑
     * @return string
     */
    public function changeFootContent(): string
    {
        switch ($this->getMessageResponseData()->getCategory()) {
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_AGENT_CONTRACT_ATTACHMENT')://MY IC和兼职IC签署合同,合同附件
                $hintTextKey = $this->getMessageResponseData()->getByShowType() ? 'sign_msg_026' : 'sign_msg_025';
                $hintTextKey = $this->getTranslation()->_($hintTextKey);
                break;
            default:
                $hintTextKey = $this->getMessageResponseData()->getFootText();
                break;
        }
        return $hintTextKey;
    }

    /**
     * @description 实现该方法，可自定义逻辑
     * @return void
     */
    public function afterExecute()
    {
        // 消息阅读成功后，写入队列
        if($this->enableMobileVersion()) {
            //保留--其余上线完毕之后，app 都更信息完毕了，就可以删除了，只保留以下部分
            if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD && $this->getMessageResponseData()->getReadDuration() == 0 && $this->getMessageResponseData()->getByShowType() == 0) {
                $this->getDI()->get('logger')->write_log(['addHaveReadMsgToMns'=>$this->getMessageResponseData()->getMsgId()], 'info');
                (new BackyardServer($this->lang, $this->timeZone))->addHaveReadMsgToMns($this->getMessageResponseData()->getMsgId());
            }

            if($this->getMessageResponseData()->getReadDuration() == 0 && $this->getMessageResponseData()->getByShowType() == 0) {
                parent::afterExecute();
            }
            //保留
        } else {
            // 消息阅读成功后，写入队列
            if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD) {
                $this->getDI()->get('logger')->write_log(['addHaveReadMsgToMns'=>$this->getMessageResponseData()->getMsgId()], 'info');
                (new BackyardServer($this->lang, $this->timeZone))->addHaveReadMsgToMns($this->getMessageResponseData()->getMsgId());
            }
            parent::afterExecute();
        }
    }

    /**
     * 版本控制
     * @return bool|mixed
     */
    public function enableMobileVersion()
    {
        $set_val        = (new SettingEnvServer())->getSetVal('message_version_config');
        if (empty($set_val)) {
            return true;
        }
        $equipment_info = json_decode($set_val, true);
        return MobileHelper::compareVersion($equipment_info);
    }
}