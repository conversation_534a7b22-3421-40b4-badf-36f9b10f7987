<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Models\backyard\FrontlineSpecialNonCarriageModel;
use FlashExpress\bi\App\Models\backyard\VanContainerModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\VanContainerServer;

class VanContainerCompleteMessage extends BaseMessageServer
{
    /**
     * @description 生成url，用于嵌入iframe
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        return sprintf("%s%s?msg_id=%s&%s", env('h5_endpoint'), $this->getNormalMessageUrl(), $this->getMessageRequestData()->getMsgId(),$this->getMessageResponseData()->getContent());
    }


    /**
     * @description 是否设置消息读取状态为已读，如果设置会改变消息的已读状态
     * true=需要更新已读状态，false=不更新消息已读状态
     *
     * @param $is_van_container
     * @return bool
     */
    private function isSetReadStateToHasReadForVanContainer($is_van_container): bool
    {
        if (empty($is_van_container)) {
            return false;
        }
        if ($this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD) {
            return true;
        }
        return false;
    }

    /**
     * @description 实现该方法，可自定义逻辑
     * @return void
     */
    public function afterExecute()
    {
        $isVanContainerInfo = $this->getVanContainerInfo();
        //设置已读
        if ($this->isSetReadStateToHasReadForVanContainer($isVanContainerInfo)) {
            $this->setMessageReadStatus($this->isNeedChangeTopState());
        }

        //是否展示提交按钮
        $this->getMessageResponseData()->setIsBtnShow(!$isVanContainerInfo);
    }

    /**
     * 是否存在申请，不存在=false，存在=true
     * @return bool
     */
    private function getVanContainerInfo(): bool
    {
        $staffInfo = (new StaffServer())->getStaffInfoSpecColumns($this->getMessageRequestData()->getStaffId(), 'staff_info_id,is_sub_staff');
        if (empty($staffInfo)) {
            return true;
        }

        // 员工已在「特殊无车厢配置」且状态为“生效中” , 则无需填写
        $effectiveStatus = (new VanContainerServer($this->lang, $this->timeZone))->getEffectiveStatusByStaffInfoId($staffInfo['staff_info_id']);
        if (!empty($effectiveStatus) && $effectiveStatus == FrontlineSpecialNonCarriageModel::EFFECTIVE_STATUS_EFFECT) {
            return true;
        }

        return VanContainerModel::hasSubmitVanContainer($staffInfo['staff_info_id'], $staffInfo['is_sub_staff']);
    }

}