<?php


namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\PublicRepository;

class AccidentServer extends BaseServer{

    public function __construct($lang = 'zh-CN')
    {
        parent::__construct($lang);
    }

    /**
     * Notes: 获取紧急呼叫电话
     * User: TB
     * Date: 2021/6/23
     * Time: 20:54
     * @return false|mixed
     */
    public function getEmergencyCall(){
        try {
            $key = "EmergencyCall-".date('YmdH');
            $redis = $this->getDI()->get('redis');
            if (RUNTIME == 'pro' && $redis->get($key)){
                return json_decode($redis->get($key),true);
            }

            $setting_model = new BySettingRepository();
            $data = json_decode($setting_model->get_setting('emergency_call_config'),true);
            if(empty($data)){
                return  [];
            }
            $t = $this->getTranslation();
            foreach ($data as &$v){
                $v['name'] = $t[$v['name']];
                $v['label'] = $t[$v['label']];
            }

            $redis->save($key, json_encode($data),3610);
            return $data;
        } catch (\Exception $e){
            $this->getDI()->get("logger")->write_log($e->getMessage(), "error");
            return false;
        }
    }

    /**
     * Notes: 获取初始化数据
     * User: TB
     * Date: 2021/6/23
     * Time: 20:54
     * @return false
     */
    public function getInitializeData($u){
        try {
            $key = "accident_report_config-".date('YmdH').$this->lang;
            $redis = $this->getDI()->get('redis');
            if (RUNTIME == 'pro' && $redis->get($key)){
                return json_decode($redis->get($key),true);
            }

            $setting_model = new BySettingRepository();
            $data = json_decode($setting_model->get_setting('accident_report_config'),true);
            if (empty($data) || !is_array($data)) {
                return false;
            }

            $t = $this->getTranslation();
            foreach ($data as $k=>&$v){
                if (in_array($k,['showOption'])){continue;}
                foreach ($v as &$vv){
                    $vv['label'] = $t[$vv['label']];
                    if ($k=='influence_scope'){
                        foreach ($vv['loss_type'] as &$vvv){
                            $vvv['label'] = $t[$vvv['label']];
                        }
                    }
                }
            }

            $data['store'] = $u['organization_type'] == 1 ? ['id'=>$u['organization_id'],'name'=>$u['organization_name']] : [];

            $redis->save($key, json_encode($data),3610);
            return $data;
        } catch (\Exception $e){
            $this->getDI()->get("logger")->write_log($e->getMessage(), "error");
            return false;
        }
    }

    /**
     * Notes: 获取子任务配置
     * User: TB
     * Date: 2021/6/28
     * Time: 15:05
     * @param $lossType
     * @return false|mixed
     */
    public function getSubTaskConfig($lossType){
        try {
            $key = "accident_report_subtask_config-".date('YmdH');
            $redis = $this->getDI()->get('redis');
            if (RUNTIME == 'pro' && $redis->get($key)){
                return json_decode($redis->get($key),true)[$lossType];
            }

            $setting_model = new BySettingRepository();
            $data = json_decode($setting_model->get_setting('accident_report_subtask_config'),true);

            $redis->save($key, json_encode($data),3610);

            return $data[$lossType];
        } catch (\Exception $e){
            $this->getDI()->get("logger")->write_log($e->getMessage(), "error");
            return false;
        }
    }

    /**
     * Notes: 发送BY KIT push和站内信
     * User: TB
     * Date: 2021/9/23
     * Time: 16:40
     * @param $acceptor
     * @param $ccto
     * @param $content
     * @return bool
     */
    public function sendPush($acceptor,$ccto,$content, $lossTypeString = ''){
        try {
            //发送push
            //获取处理人工号
            $acceptorConf = json_decode(SettingEnvModel::findFirst([
                'conditions' => 'code = :code:',
                'bind' => ['code' => "accident_report_acceptor_config"],
            ])->toArray()['set_val'],true);
            $acceptorStaffs = [];
            foreach ($acceptor as $k=>$v){
                if (isset($acceptorConf[$v])){
                    foreach ($acceptorConf[$v]['departments'] as $kk=>$vv){
                        $acceptorStaffs = array_merge($acceptorStaffs,$this->getStaffId($kk,$vv));
                    }
                    if ($acceptorConf[$v]['roles']){
                        $acceptorStaffs = array_merge($acceptorStaffs,$this->getStaffId(NULL,$acceptorConf[$v]['roles']));
                    }
                    if ($acceptorConf[$v]['staff_id']){
                        $acceptorStaffs = array_merge($acceptorStaffs,$acceptorConf[$v]['staff_id']);
                    }
                }
            }
            //获取抄送人工号
            $cctoConf = json_decode(SettingEnvModel::findFirst([
                'conditions' => 'code = :code:',
                'bind' => ['code' => "accident_report_ccto_config"],
            ])->toArray()['set_val'],true);
            $cctoStaffs = [];
            foreach ($ccto as $k=>$v){
                if (isset($cctoConf[$v])){
                    foreach ($cctoConf[$v]['departments'] as $kk=>$vv){
                        $cctoStaffs = array_merge($cctoStaffs,$this->getStaffId($kk,$vv));
                    }
                    if ($cctoConf[$v]['roles']){
                        $cctoStaffs = array_merge($cctoStaffs,$this->getStaffId(NULL,$cctoConf[$v]['roles']));
                    }
                    if ($cctoConf[$v]['staff_id']){
                        $cctoStaffs = array_merge($cctoStaffs,$cctoConf[$v]['staff_id']);
                    }
                }
            }
            $acceptorStaffs = array_unique($acceptorStaffs);
            $cctoStaffs = array_unique($cctoStaffs);
            $push = new PublicRepository();
            $staffServer = new StaffServer();
            foreach ($acceptorStaffs as $v){
                $lang = $staffServer->getLanguage($v);
                $t = $staffServer->getTranslation($lang);
                $loss_type = '';
                foreach (explode(',', $lossTypeString) as $type) {
                    $loss_type = trim($loss_type, ',') . "," . $t->_("accident_report_loss_type_{$type}");
                }

                $pushContent = str_replace('{loss_type}', $loss_type, $content) . $t->_("accident_report_push_bottom");

                $push->pushAndSendMessageToSubmitter([
                    'staff_info_id'     =>  $v,
                    'message_title'     =>  $t->_('accident_report_push_tital_1'),
                    'message_content'   =>  $pushContent,
                    'type'              =>  18,
                    'path'              =>  'message_list',
                ]);
                $push->pushAndSendKitMessageToSubmitter([
                    'staff_info_id'     =>  $v,
                    'message_title'     =>  $t->_('accident_report_push_tital_1'),
                    'message_content'   =>  $pushContent,
                    'type'              =>  18,
                    'path'              =>  'message_list',
                ]);
            }
            foreach ($cctoStaffs as $v){
                $lang = $staffServer->getLanguage($v);
                $t = $staffServer->getTranslation($lang);
                $loss_type = '';
                foreach (explode(',', $lossTypeString) as $type) {
                    $loss_type = trim($loss_type, ',') . "," . $t->_("accident_report_loss_type_{$type}");
                }

                $pushContent = str_replace('{loss_type}', $loss_type, $content) . $t->_("accident_report_push_bottom");

                $push->pushAndSendMessageToSubmitter([
                    'staff_info_id'     =>  $v,
                    'message_title'     =>  $t->_('accident_report_push_tital_2'),
                    'message_content'   =>  $pushContent,
                    'type'              =>  18,
                    'path'              =>  'message_list',
                ]);
                $push->pushAndSendKitMessageToSubmitter([
                    'staff_info_id'     =>  $v,
                    'message_title'     =>  $t->_('accident_report_push_tital_2'),
                    'message_content'   =>  $pushContent,
                    'type'              =>  18,
                    'path'              =>  'message_list',
                ]);
            }
        } catch (\Exception $e){
            $this->getDI()->get("logger")->write_log($e->getMessage(), "error");
        }

        return true;
    }

    /**
     * Notes: 根据部门角色获取员工ID
     * User: TB
     * Date: 2021/9/23
     * Time: 11:42
     * @param null $department
     * @param null $roles
     * @return array
     */
    public function getStaffId($department=NULL,$roles=NULL){
        try {
            $builder = $this->modelsManager->createBuilder()->columns(
                'a.staff_info_id'
            )
            ->from(['a' => HrStaffInfoModel::class])
            ->leftJoin(HrStaffInfoPositionModel::class, 'a.staff_info_id = b.staff_info_id', 'b');

            if ($department){
                $builder->andWhere('(a.sys_department_id = :sys_department_id: OR node_department_id = :sys_department_id:)',['sys_department_id'=>$department]);
            }

            if ($roles){
                $builder->andWhere('b.position_category IN({position_category:array})',['position_category'=>$roles]);
                $builder->groupBy('a.staff_info_id');
            }

            $items = @array_column($builder->getQuery()->execute()->toArray(),'staff_info_id');

        } catch (\Exception $e){
            $this->getDI()->get("logger")->write_log($e->getMessage(), "error");
        }

        return $items??[];
    }

    public function getNweData($lastId){
        try {
            $sql = "--
                SELECT * FROM accident_report WHERE id > $lastId ORDER BY id ASC";

            return $this->getDI()->get('db')->fetchAll($sql);
        } catch (\Exception $e){
            $this->getDI()->get("logger")->write_log($e->getMessage(), "error");
            return [];
        }
    }

    public function getNweSubData($pid){
        try {
            $sql = "--
                SELECT * FROM accident_report_sub WHERE pid = $pid";

            return $this->getDI()->get('db')->fetchAll($sql);
        } catch (\Exception $e){
            $this->getDI()->get("logger")->write_log($e->getMessage(), "error");
            return [];
        }
    }
}