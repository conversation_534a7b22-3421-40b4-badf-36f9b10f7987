<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\FrontlineSpecialNonCarriageModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\VanContainerModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

class VanContainerServer extends BaseServer
{

    /**
     * 是否展示车厢入口
     * @param $staffInfo
     * @return bool
     */
    public function isShowVanContainer($staffInfo,$isCheckFrontlineSpecialNonCarriage = true): bool
    {
        if (!isCountry('TH')) {
            return false;
        }

        if (empty($staffInfo) || $staffInfo['formal'] != HrStaffInfoModel::FORMAL_1) {
            // 不是编制内，需要隐藏车辆信息栏的填写。
            return false;
        }

        //非个人代理
        if(in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            return false;
        }
        //van
        if($staffInfo['job_title'] != enums::$job_title['van_courier']){
            return false;
        }

        if ($isCheckFrontlineSpecialNonCarriage) {
            $isset = FrontlineSpecialNonCarriageModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and effective_status = :effective_status:',
                'bind'       => [
                    'staff_info_id'    => $staffInfo['staff_info_id'],
                    'effective_status' => FrontlineSpecialNonCarriageModel::EFFECTIVE_STATUS_EFFECT,
                ],
            ]);
            if (!empty($isset)) {
                return false;
            }
        }

        return true;
    }

    public $staffInfo;
    //获取信息 如果是子账号切主账号
    public function getStaffInfo($staffId){
        $staffRe = new StaffRepository($this->lang);
        $id = StaffRepository::getMasterStaffIdBySubStaff($staffId);
        if(!empty($id)){
            $staffId = $id;
        }
        $this->staffInfo = $staffRe->getStaffPosition($staffId);
    }

    //获取最新一条信息
    public function getLastInfo($param){
        $this->getStaffInfo($param['user_info']['id']);
        if(empty($this->staffInfo)){
            throw new ValidationException('staff id error');
        }

        $data = VanContainerModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind' => ['staff_id' => $this->staffInfo['staff_info_id']],
            'order' => 'id desc',
        ]);

        if(empty($data)){
            return [];
        }
        $data = $data->toArray();
        $data['type_text'] = VanContainerModel::$typeList[$data['type']];
        $data['state_text'] = (new AuditlistRepository($this->lang, $this->timeZone))->getAuditState($data['state']);
        $data['created_at'] = show_time_zone($data['created_at']);
        $data['updated_at'] = show_time_zone($data['updated_at']);
        return $data;
    }

    public function getContainerList($param){
        $this->getStaffInfo($param['user_info']['id']);
        if(empty($this->staffInfo)){
            throw new ValidationException('staff id error');
        }

        $page = $param['page'] ?? 1;
        $size = $param['size'] ?? 20;

        $data = VanContainerModel::find([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $this->staffInfo['staff_info_id']],
            'offset'     => ($page - 1) * $size,
            'limit'      => $size,
            'order'      => 'id desc',
        ])->toArray();

        if(empty($data)){
            return [];
        }
        $auditRe = new AuditlistRepository($this->lang, $this->timeZone);
        $typeData = VanContainerModel::$typeList;
        foreach ($data as &$da){
            $da['type_text'] = $this->getTranslation()->_($typeData[$da['type']]);
            $da['state_text'] = $auditRe->getAuditState($da['state']);
            $da['created_at'] = show_time_zone($da['created_at']);
            $da['updated_at'] = show_time_zone($da['updated_at']);
            $da['data_source'] = empty($da['source_type']) ? '' : $this->getTranslation()->_(VanContainerModel::$sourceTypeList[$da['source_type']]);
        }
        return $data;
    }


    /**
     * @param $param
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addContainer($param)
    {
        $licensePlateLocation = (new VehicleServer($this->lang, $this->timeZone))->getLicensePlateLocationFromCache();

        //需要验证 上牌地点 可选项
        if(!empty($param['license_location']) && !in_array($param['license_location'], $licensePlateLocation)) {
            throw new ValidationException($this->getTranslation()->_('vehicle_info_license_location_select'));
        }

        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            //切账号
            $this->getStaffInfo($param['user_info']['id']);
            if(empty($this->staffInfo)){
                throw new ValidationException('staff id error');
            }

            //验证
            $exist = VanContainerModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and state = :state:',
                'bind'       => ['staff_id' => $this->staffInfo['staff_info_id'], 'state' => enums::$audit_status['panding']],
            ]);
            if(!empty($exist)){
                throw new ValidationException($this->getTranslation()->_('var_container_exist'));
            }

            // 车牌号特殊字符过滤
            if (!empty($param['plate_number'])) {
                $param['plate_number'] = nameSpecialCharsReplace($param['plate_number']);
            }

            //如果 是有车厢必须有视频
            if($param['type'] == VanContainerModel::HAVE_ONE){
                //HCM无车厢名单
                $effectiveStatus = $this->getEffectiveStatusByStaffInfoId($this->staffInfo['staff_info_id']);
                if (!empty($effectiveStatus) && $effectiveStatus == FrontlineSpecialNonCarriageModel::EFFECTIVE_STATUS_EFFECT) {
                    throw new ValidationException($this->getTranslation()->_('non_carriage_can_not_submit'));
                }

                if (empty($param['video_url'])) {
                    throw new ValidationException($this->getTranslation()->_('van_container_save_error_001'), ErrCode::VALIDATE_ERROR);
                }

                // 员工车辆信息同步
                (new VehicleServer($this->lang, $this->timeZone))->saveVehicleInfo($this->staffInfo,
                    $param['plate_number'], $param['license_location']);
            }

            //写表
            $insert['staff_info_id'] = $this->staffInfo['staff_info_id'];
            $insert['operator']      = $this->staffInfo['staff_info_id'];
            $insert['date_at']       = date('Y-m-d');
            $insert['source_type']   = VanContainerModel::SOURCE_TYPE_BY;
            $insert['type']          = $param['type'];
            $insert['video_url']     = ($insert['type'] == VanContainerModel::HAVE_ONE) ? $param['video_url'] : '';
            $insert['plate_number'] = $insert['type'] == VanContainerModel::HAVE_ONE ? $param['plate_number'] : '';
            $insert['license_location'] = $insert['type'] == VanContainerModel::HAVE_ONE ? $param['license_location'] : '';

            $this->logger->write_log(['van_container_insert_data' => $insert], 'info');

            $model = new VanContainerModel();
            $flag = $model->create($insert);
            if ($flag === false) {
                throw new BusinessException('车厢信息写入失败, data=' . json_encode($insert, JSON_UNESCAPED_UNICODE), ErrCode::SYSTEM_ERROR);
            }

            $db->commit();

            return $flag;

        } catch (ValidationException $e) {
            $db->rollback();
            throw $e;

        } catch (BusinessException $e) {
            $db->rollback();
            throw $e;
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }


    //获取驳回消息详情页变量
    public function getMsgData($param): array
    {
        $id   = $param['id'];
        $info = VanContainerModel::findFirst($id);
        return ['remark' => $info->remark];
    }

    /**
     * 获取员工车辆信息
     *
     * @param $staff_info_id
     *
     * @return array
     * @throws ValidationException
     */
    public function getStaffVehicleInfo($staff_info_id)
    {
        // 子账号 找 主账号
        $this->getStaffInfo($staff_info_id);
        if (empty($this->staffInfo)) {
            throw new ValidationException($this->getTranslation()->_('1001'), enums::$ERROR_CODE['1000']);
        }

        $return = [];

        $vehicle_model = VehicleInfoModel::findFirst([
            'conditions' => 'uid = :uid: AND deleted = 0',
            'bind'       => ['uid' => $this->staffInfo['staff_info_id']],
            'columns'    => "id, uid, plate_number, license_location, approval_status",
        ]);

        $return['uid']              = $vehicle_model->uid ?? $this->staffInfo['staff_info_id'];
        $return['plate_number']     = $vehicle_model->plate_number ?? '';
        $return['license_location'] = $vehicle_model->license_location ?? '';
        $return['approval_status']  = $vehicle_model->approval_status ?? '0';

        return $return;
    }

    public function getEffectiveStatusByStaffInfoId($staffId)
    {
        $info = FrontlineSpecialNonCarriageModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and is_show = :is_show:',
            'bind'       => [
                'staff_info_id' => $staffId,
                'is_show'      => FrontlineSpecialNonCarriageModel::SHOW_STATE_YES,
            ]
        ]);
        return $info ? $info->effective_status : 0;
    }

    /**
     * 获取可选项
     * @return mixed
     */
    public function getSysStatic()
    {
        //上牌地点可选项
        $license_plate_location = (new VehicleServer($this->lang, $this->timeZone))->getLicensePlateLocationFromCache();
        $returnData['license_plate_location'] = [];
        //车厢类型 van 特有
        foreach ($license_plate_location as $val) {
            $returnData['license_plate_location'][] = [
                'value' => $val,
                'label' => $val,
            ];
        }

        return $returnData;
    }


}