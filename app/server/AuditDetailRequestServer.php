<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Interfaces\AuditDetailRequest;

class AuditDetailRequestServer implements AuditDetailRequest
{
    private $auditShowType;

    private $auditStateType;

    private $currentUser;
    private $auditId;
    private $auditType;
    private $requestSrc;


    public function init($params = [])
    {
        $auditShowType    = $params['audit_show_type'];
        $etAuditStateType = $params['audit_state_type'];
        $user             = $params['user'];
        $auditId          = $params['audit_id'];
        $auditType        = $params['audit_type'];
        $requestSrc       = $params['request_src'] ?? null;

        $this->setAuditId($auditId);
        $this->setAuditType($auditType);
        $this->setAuditShowType($auditShowType);
        $this->setAuditStateType($etAuditStateType);
        $this->setCurrentUser($user);
        $this->setRequestSrc($requestSrc);
    }

    /**
     * @return mixed
     */
    public function getAuditShowType()
    {
        return $this->auditShowType;
    }

    /**
     * @param mixed $auditShowType
     */
    public function setAuditShowType($auditShowType): void
    {
        $this->auditShowType = $auditShowType;
    }

    /**
     * @return mixed
     */
    public function getAuditStateType()
    {
        return $this->auditStateType;
    }

    /**
     * @param mixed $auditStateType
     */
    public function setAuditStateType($auditStateType): void
    {
        $this->auditStateType = $auditStateType;
    }

    /**
     * @return mixed
     */
    public function getCurrentUser()
    {
        return $this->currentUser;
    }

    /**
     * @param mixed $currentUser
     */
    public function setCurrentUser($currentUser): void
    {
        $this->currentUser = $currentUser;
    }

    /**
     * @return mixed
     */
    public function getAuditId()
    {
        return $this->auditId;
    }

    /**
     * @param mixed $auditId
     */
    public function setAuditId($auditId): void
    {
        $this->auditId = $auditId;
    }

    /**
     * @return mixed
     */
    public function getAuditType()
    {
        return $this->auditType;
    }

    /**
     * @param mixed $auditType
     */
    public function setAuditType($auditType): void
    {
        $this->auditType = $auditType;
    }

    /**
     * @return mixed
     */
    public function getRequestSrc()
    {
        return $this->requestSrc ;
    }

    /**
     * @param mixed $requestSrc
     */
    public function setRequestSrc($requestSrc): void
    {
        $this->requestSrc = $requestSrc;
    }


}