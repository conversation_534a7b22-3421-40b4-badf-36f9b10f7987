<?php
/**
 * Author: Bruce
 * Date  : 2025-01-09 21:10
 * Description:
 */

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\PayrollSendFile2316Model;
use FlashExpress\bi\App\Repository\PayrollSendFileRepository;

class PayrollFileServer extends BaseServer
{
    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->timeZone =  $timezone;
    }

    /**
     * 服务费消息列表
     * @param $params
     * @return array
     */
    public function icSettlementMessList($params): array
    {
        if (empty($params['staff_id'])) {
            return ['list' => []];
        }
        $page  = $params['page'];
        $size  = $params['page_size'];
        $start = ($page - 1) * $size;

        $sql = "select mc.id as msg_id,mc.title,mc.created_at,mc.read_state,mc.top_state, mc.category,
                mc.update_state
                from message_courier   as mc
                where mc.staff_info_id = {$params['staff_id']} and mc.is_del = 0   and mc.category=157
                order by mc.top_state desc,
                         mc.read_state asc,
                         mc.created_at desc
                limit  {$start}, {$size}
                ";


        $data = $this->getDI()->get('db_coupon_r')->query($sql);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        foreach ($data as &$datum) {
            $datum['push_time'] = show_time_zone($datum['created_at']);
            unset($datum['created_at']);
        }

        $return['list'] = $data;
        return $return;
    }
    /**
     * 获取pdf详情
     * @param $params
     * @return mixed
     * @throws BusinessException
     */
    public function getFileInfo($params)
    {
        $where['id'] = $params['id'];
        $fileData    = PayrollSendFileRepository::getOne($where);

        if (empty($fileData)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));
        }

        if ($fileData['msg_id'] != $params['msg_id']) {
            throw new BusinessException($this->getTranslation()->_('data_error'));
        }
        if (empty($fileData['pdf_url'])) {
            $ac              = new ApiClient('hcm_rpc', '', 'payroll_file_2316', $this->lang);
            $setParams['id'] = $params['id'];
            $ac->setParams($setParams);
            $ac_result = $ac->execute();
            if ($ac_result["result"]['code'] == 1 || !empty($ac_result["result"]['data'])) {
                $fileData['pdf_url']     = $ac_result["result"]['data']['pdf_url'];
                $fileData['sign_status'] = $ac_result["result"]['data']['sign_status'];
            } else {
                $this->logger->write_log([
                    'PayrollFileServer-getFileInfo' => [
                        'rpc'     => 'payroll_file_2316',
                        'rpc_res' => $ac_result,
                        'params'  => $setParams,
                    ],
                ]);
                throw new BusinessException('PDF timeout' . $this->getTranslation()->_('please_retry'));
            }
        }

        $data['file_url']    = $fileData['pdf_url'];
        $data['sign_status'] = $fileData['sign_status'];
        return $data;
    }

    /**
     * 提交签名
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function submit($params)
    {
        $where['id'] = $params['id'];
        $fileData    = PayrollSendFileRepository::getOne($where);

        if (empty($fileData)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));
        }

        $updateData['sign_url']    = $params['sign_url'];
        $updateData['sign_status'] = PayrollSendFile2316Model::SIGN_STATUS_2;
        $updateData['sign_time']   = date('Y-m-d H:i:s');

        $db = $this->getDI()->get('db');
        $db->updateAsDict("payroll_send_file_2316", $updateData, "id=" . $params['id']);

        //置已读
        (new BackyardServer($this->lang,$this->timeZone))->has_read_operation($fileData['msg_id']);

        //签名，再次生成一下pdf
        $ac              = new ApiClient('hcm_rpc', '', 'sync_payroll_file_2316', $this->lang);
        $setParams['id'] = $params['id'];
        $ac->setParams($setParams);
        $ac_result = $ac->execute();
        if (empty($ac_result["result"]) || $ac_result["result"]['code'] != 1) {
            $this->logger->write_log([
                'PayrollFileServer-submit' => [
                    'rpc'     => 'sync_payroll_file_2316',
                    'rpc_res' => $ac_result,
                    'params'  => $setParams,
                ],
            ]);
        }

        return true;
    }

    /**
     * 获取详情数据
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getIcSettlementInfo($params)
    {
        $paramsIn['staff_info_id'] = $params['staff_info_id'];
        $paramsIn['period_id']     = $params['id'];

        $periodInfo = $this->getIcPeriodInfoFromFbi($paramsIn);
        if (empty($periodInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        if (!empty($periodInfo['signature_pic'])) {
            (new BackyardServer($this->lang, $this->timeZone))->has_read_operation($params['msg_id'],true);
        }

        return $periodInfo;
    }

    /**
     * 获取数据详情--请求fbi
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getIcPeriodInfoFromFbi($params)
    {
        $data['staff_info_id'] = $params['staff_info_id'];
        $data['period_id']     = $params['period_id'];
        $ret                   = new ApiClient('ard_api', '', 'proxyInvoice.get_ic_period_info', $this->lang);
        $ret->setParams($data);
        $res = $ret->execute();
        $this->getDI()->get('logger')->write_log("getIcPeriodInfoFromFbi 参数:" . json_encode($data) . ";结果:" . json_encode($res),
            'info');
        if (!isset($res['result'])) {
            return [];
        }
        if ($res['result']['code'] == 1) {
            return $res['result']['data'];
        }
        $msg = isset($res['result']['msg']) ? $res['result']['msg'] : $res['error'];

        $this->getDI()->get('logger')->write_log("getIcPeriodInfoFromFbi 参数:" . json_encode($data) . ";结果:" . $msg);
        throw new ValidationException($msg);
    }

    /**
     * 提交签字
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function icSettlementSubmit($params)
    {
        $period['staff_info_id'] = $params['staff_info_id'];
        $period['period_id'] = $params['id'];
        $periodInfo = $this->getIcPeriodInfoFromFbi($period);
        if (empty($periodInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        if (!empty($periodInfo['signature_pic'])) {
            (new BackyardServer($this->lang, $this->timeZone))->has_read_operation($params['msg_id']);
            throw new ValidationException($this->getTranslation()->_('accident_report_influence_scope_3'));
        }

        $res = $this->callBackSignUrlToFbi($params);
        if (!$res) {
            throw new ValidationException($this->getTranslation()->_('server_error'));
        }

        (new BackyardServer($this->lang, $this->timeZone))->has_read_operation($params['msg_id'],true);

        return true;
    }

    /**
     * 将图片 回调同步 给fbi
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function callBackSignUrlToFbi($params)
    {
        $data['staff_info_id'] = $params['staff_info_id'];
        $data['period_id']     = $params['id'];
        $data['signature_url'] = $params['url'];
        $ret                   = new ApiClient('ard_api', '', 'proxyInvoice.receive_signature', $this->lang);
        $ret->setParams($data);
        $res = $ret->execute();
        $this->getDI()->get('logger')->write_log("callBackSignUrlToFbi 参数:" . json_encode($data) . ";结果:" . json_encode($res),
            'info');
        if (!isset($res['result'])) {
            return false;
        }
        if ($res['result']['code'] == 1) {
            return true;
        }
        $this->getDI()->get('logger')->write_log("callBackSignUrlToFbi 参数:" . json_encode($data) . ";结果:" . json_encode($res),'notice');
        $msg = $res['result']['msg'] ?? $res['error'];
        throw new ValidationException($msg);
    }
}