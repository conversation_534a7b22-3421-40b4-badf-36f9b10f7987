<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/3/22
 * Time: 8:46 PM
 */

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\backyard\FileOssUrlModel;
use Mpdf\Mpdf;
use FlashExpress\bi\App\Repository\BySettingRepository;
use Mpdf\Config\ConfigVariables;
use Mpdf\Config\FontVariables;


class MpdfServer extends BaseServer{

    const OSS_DIR_FUEL_BUDGET = 'WORK_ORDER';

    //oss file_type


    /**
     * @param $temp_data 模板相关数据 dir 模板目录 name 模板名称 path 模板配置根目录
     * @param $var_data 页面pdf html 渲染 变量  key => value
     * @param string $insert 是否需要插入 by 公共oss地址表 详情看  file_oss_url
     * @return string
     * @throws \Exception
     */
    public function make_pdf($temp_data,$var_data,$insert = ''){
        $view = new \Phalcon\Mvc\View();
        $view->setViewsDir($temp_data['path']);
        if(!empty($var_data)){
            foreach ($var_data as $k => $v){
                $view->setVar($k, $v);
            }
        }
        $view->start();
        $view->disableLevel(
            [
                \Phalcon\Mvc\View::LEVEL_LAYOUT      => false,
                \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
            ]
        );
        $view->render($temp_data['dir'], $temp_data['name']);
        $view->finish();
        $content = $view->getContent();
        $name = empty($temp_data['file_name']) ? '' : $temp_data['file_name'];
        $file_name = $this->m_pdf($content,$name);

        //上传oss 保存数据表
        $res = $this->uploadFileOss($file_name,self::OSS_DIR_FUEL_BUDGET);
        if(!empty($res['put_url']) && !empty($insert)){
            //保存 file_oss_url
            $insert['path'] = urldecode($res['object_key']);
            $insert['bucket'] = $res['bucket_name'];

            $model = new FileOssUrlModel();
            $flag = $model->create($insert);

            //日志
            $this->logger->write_log("oss_record_".json_encode($insert)."_{$flag}",'info');
        }
        //删除文件
        @unlink($file_name);
        $info = pathinfo($file_name);
        $return['file_name'] = $info['basename'];
        $return['bucket_name'] = $res['bucket_name'];
        $return['object_key'] = urldecode($res['object_key']);
        return $return;



        $env_bll = new BySettingRepository();
        $point = $env_bll->get_setting('server_point');
        return "https://" . $res['bucket_name'] . $point. urldecode($res['object_key']);
    }


    //mpdf 生成 文件 方法 统一放到 tmp目录下 生成之后需要上传oss 然后把文件删除
    public function m_pdf($content,$file_name = ''){
        $content = mb_convert_encoding($content, 'UTF-8', 'UTF-8');

        //实例化mpdf 扩展字体 由于泰文乱码
        $config = array(
            'format' => 'A4',
            'mode'=>'th'
        );
        $mpdf = new Mpdf($config);
        $mpdf->autoScriptToLang = true;
        $mpdf->autoLangToFont   = true;
        $mpdf->useSubstitutions = true;
        //设置字体,解决中文乱码
        $mpdf->useAdobeCJK = true;
        $mpdf->SetDisplayMode('fullwidth');
        $mpdf->WriteHTML($content);
        //指定临时目录 上传完完了删除

        if(empty($file_name))
            $file_name = time();
        $file_name = "/tmp/" . $file_name .time().".pdf";
//        $file_name = "/usr/share/nginx/backyard/public/123.pdf";
        $mpdf->Output($file_name, 'f');
//        exit;
        return $file_name;




        //实例化mpdf
        $config = array(
            'mode' => 'zh-CN',
            'in_charset' => 'UTF-8'
        );
        $mpdf = new Mpdf($config);
        $mpdf->useAdobeCJK = true;
        $mpdf->SetDisplayMode('fullpage');
        $mpdf->WriteHTML($content);
        //指定临时目录 上传完完了删除
//        $file_name = "/tmp/" . $file_name . ".pdf";
        $file_name = '/usr/share/nginx/backyard/public/123.pdf';
        $mpdf->Output($file_name, \Mpdf\Output\Destination::FILE);
        return $file_name;
    }

}
