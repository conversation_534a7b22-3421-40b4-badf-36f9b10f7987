<?php


namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\HttpCurl;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;

class AfterAttendanceServer extends BaseServer
{

    public $timezone;
    public $lang;

    public function __construct($lang, $timezone)
    {
        $this->timezone = $timezone;
        $this->lang = $lang;
        parent::__construct($lang);
    }

    /**
     * 上班卡之后的消息提醒
     * @return array
     * @throws \Exception
     */
    public function afterPunchInNotice($params)
    {
        $data = [];
        if(!isCountry('TH')){
            return  $data;
        }
        // TODO   java_pms_url env 越南、印尼没有配置
        $url = $this->config->api->java_pms_url."/svc/staff/attendance/%s";
        $url = sprintf($url, $params['id']);
        $header[] = "Content-type: application/json";
        $header[] = "Accept: application/json";
        $header[] = "Accept-Language: " . $this->lang;
        $res = json_decode(HttpCurl::httpGet($url,$header),true);

        $this->logger->write_log(['params'=>$params,'result'=>$res,'function'=>'afterPunchInNotice'],'info');

        if(empty($res['data'])){
            return $data;
        }
        if(!empty($res['data']['attendances'])){
            foreach ($res['data']['attendances'] as $item) {
                $data['message'] .= $item['message'];
            }
        }
        return $data;
    }


}