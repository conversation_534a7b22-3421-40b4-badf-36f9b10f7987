<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\HttpCurl;
use phpDocumentor\Reflection\Types\True_;

class formPdfServer extends BaseServer
{
    public static $instance;

    private static $defaultPdfOptions = [
        'contentDisposition' => 'inline',
    ];

    public function __construct()
    {

        parent::__construct($this->lang, $this->timeZone);
    }

    /**
     * 获取实例
     * @return formPdfServer
     */
    public static function getInstance(): formPdfServer
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }
        return self::$instance;
    }


    /**
     * 生成pdf服务
     * 新版 根据html页面和 form表单数据 生成pdf，
     * 参数说明，请详细阅读后使用：
     * @param string $oss_html_path 上传阿里云后的html页面的url地址
     * @param array $form_data 文本形式的表单数据，key=>value格式，key 对应 "。ftl" html模板中的文本变量名称
     * @param string $return_pdf_name ,返回的pdf文件名称 会自动拼接后缀，无需添加后缀
     * @param array $img_data 签名图片的数据，key=>value，key对应模板中的变量名称
     * @param string $contentDisposition inline 或 attchment
     * @return $pdf_path 返回一个上传阿里云后的一个资源地址，完整路径，可以直接访问
     */
    public function generatePdf(
        string $oss_html_path,
        array $form_data,
        $img_data = [],
        string $return_pdf_name = '',
        array $pdfHeaderFooterSetting = [],
        string $contentDisposition = 'inline'
    ) {
        $host = $this->getDI()->getConfig()->api->api_form_pdf;
        $url  = $host . '/api/pdf/createPdfByJvppeteer';

        //pdf格式设置，如页眉页脚展示内容等
        $pdfOptions = [];
        if (!empty($pdfHeaderFooterSetting)) {
            //是否展示页眉页脚（不能分开控制，可以通过"页眉/页脚内容"这个属性来控制是否显示对应页眉或页脚）
            if (isset($pdfHeaderFooterSetting['displayHeaderFooter']) && $pdfHeaderFooterSetting['displayHeaderFooter'] == true) {
                //pdf 页眉内容（html代码）
                $head_temp = (!isset($pdfHeaderFooterSetting['headerTemplate']) || empty($pdfHeaderFooterSetting['headerTemplate']))
                    ? "<div></div>"
                    : $pdfHeaderFooterSetting['headerTemplate'];
                //pdf页脚内容（html代码）
                $footer_temp
                    = (!isset($pdfHeaderFooterSetting['footerTemplate']) || empty($pdfHeaderFooterSetting['footerTemplate']))
                    ? "<div></div>"
                    : $pdfHeaderFooterSetting['footerTemplate'];
                $pdfOptions['displayHeaderFooter'] = true;
                $pdfOptions['footerTemplate']      = $footer_temp;//pdf页脚内容（html代码）
                $pdfOptions['headerTemplate']      = $head_temp;  //pdf页眉内容（html代码）
            }
            $pdfOptions['printBackground'] = true;//设置背景属性
        }

        $post_data = [
            'pdfName'            => $return_pdf_name ?: time(),
            'templateUrl'        => $oss_html_path,
            'data'               => $form_data,
            'downLoadData'       => $img_data ? $img_data : [],
            'contentDisposition' => $contentDisposition,
        ];
        //pdf样式设置,该参数控制的是在pdf生成时候的pdf的样式设置
        if ($pdfOptions) {
            $post_data['pdfOptions'] = $pdfOptions;
        }

        $post_data_json = json_encode($post_data, JSON_UNESCAPED_UNICODE);
        //发送请求
        $header[] = "content-type: " . "application/json;charset=UTF-8";
        $result   = HttpCurl::httpPost($url, $post_data_json, $header,null,60);
        $this->logger->write_log("generatePdf生成pdf接口返回信息，result:" . $result . ",url:{$url}, parameters: {$post_data_json}",
            'info');
        $result   = json_decode($result, true);
        $pdf_path = [];

        if (isset($result['code']) && $result['code'] == 1) {
            $pdf_path = $result['result'];
        }

        return $pdf_path;
    }

    /**
     * 合并pdf
     */
    public function mergePdf($urls = [], $options = [])
    {
        $this->logger->write_log([
            'title' => 'PDF合成',
            'func'  => 'mergePdf',
            'opt'   => '开始',
            'res'   => [
                'urls'    => $urls,
                'options' => $options,
            ],
        ],'info');

        //pdf参数不存在
        if (empty($urls)) {
            return [];
        }

        //获取调用地址
        $host = $this->getDI()->getConfig()->api->api_form_pdf;
        $url  = $host.'/api/pdf/mergePdf';
        //组装接口提交参数
        $inputData = array_merge(self::$defaultPdfOptions, $options);

        if (empty($inputData['pdfName'])) {
            $inputData['pdfName'] = $this->generateRandomFileName();
        }

        //pdf列表
        $inputData['pdfPaths'] = $urls;

        //组装接口提交参数
        $inputData = json_encode($inputData, JSON_UNESCAPED_UNICODE); //转成json

        $this->logger->write_log([
            'title' => 'PDF合成',
            'func'  => 'mergePdf',
            'opt'   => '参数组装完成',
            'res'   => $inputData,
            'time'  => microtime(true),
        ],'info');

        try {
            //发送请求
            $header[] = "content-type: "."application/json;charset=UTF-8";
            $result   = HttpCurl::httpPost($url, $inputData, $header);
            $result   = json_decode($result, true);

            $this->logger->write_log([
                'title' => 'PDF合成',
                'func'  => 'mergePdf',
                'opt'   => '请求完成',
                'res'   => $result,
                'time'  => microtime(true),
            ],'info');

            if (isset($result['code']) && $result['code'] == 1 && !empty($result['result'])) {
                return $result['result'];
            }

            $this->logger->write_log([
                'title' => 'PDF合成',
                'func'  => 'mergePdf',
                'opt'   => '合成失败',
                'res'   => $result,
            ]);
        } catch (\Exception $e) {
            $this->logger->write_log([
                'title' => 'PDF合成',
                'func'  => 'mergePdf',
                'opt'   => '合成异常',
                'error' => [
                    'message' => $e->getMessage(),
                    'file'    => $e->getFile(),
                    'line'    => $e->getLine(),
                    'trace'   => $e->getTraceAsString(),
                ],
            ]);
        }

        return [];
    }

    /**
     * 生成指定长度的随机字符串（用于文件名）
     * @return string
     */
    private function generateRandomFileName(): string
    {
        $timestamp = date('YmdHis');
        $random    = substr(uniqid(), -4);
        return $timestamp.''.$random;
    }
}
