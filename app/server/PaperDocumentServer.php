<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\oa\PaperDocumentConfirmStaffModel;
use FlashExpress\bi\App\Traits\RpcHelper;

class PaperDocumentServer extends BaseServer
{
    use RpcHelper;

    /**
     * 获取基本信息
     * @param array $params
     * @return array
     * @throws ValidationException
     */
    public function getBasicInfo($params = [])
    {
        $result = $this->rpcOa($params, 'get_paper_doc_basic_info', $this->lang);
        return $this->checkReturn($result);
    }

    /**
     * 获取详情
     * @param $paramIn
     * @return array
     */
    public function getPaperDetail($paramIn): array
    {
        $params = [
            'serial_no' => $this->formatSn($paramIn['sn']),
            'staff_id'  => $paramIn['staff_id'],
        ];
        $result = $this->rpcOa($params, 'get_paper_doc_info', $this->lang);
        return $this->checkReturn($result);
    }

    /**
     * 确认
     * @param $paramIn
     * @return array
     */
    public function confirm($paramIn)
    {
        $params = [
            'serial_no'      => $this->formatSn($paramIn['sn']),
            'staff_id'       => $paramIn['staff_id'],
            'confirm_status' => $paramIn['confirm_status'],
            'reason_type'    => $paramIn['reason_type'],
            'remark'         => $paramIn['remark'],
        ];
        return $this->rpcOa($params, 'confirm_paper_document', $this->lang);
    }

    /**
     * 批量确认
     * @param $paramIn
     * @return array
     */
    public function batchConfirm($paramIn)
    {
        $params = [
            'serial_no' => $paramIn['sn'],
            'staff_id'  => $paramIn['staff_id'],
        ];
        return $this->rpcOa($params, 'batch_confirm_paper_document', $this->lang);
    }

    /**
     * 提交
     * @param $paramIn
     * @return array
     */
    public function submit($paramIn)
    {
        $params = [
            'serial_no' => $this->formatSn($paramIn['sn']),
            'staff_id'  => $paramIn['staff_id'],
            'parcel_no' => $paramIn['parcel_no'] ?? '',
            'remark'    => $paramIn['remark'] ?? '',
        ];
        return $this->rpcOa($params, 'submit_paper_document', $this->lang);
    }

    /**
     * 获取纸质单据待补全原因
     * @return array
     */
    public function getConfirmReasonTypeList(): array
    {
        return $this->rpcOa([], 'get_confirm_reason_type_list', $this->lang);
    }

    /**
     * by 入口权限
     * @param $paramIn
     * @return bool
     */
    public function getPaperPermission($paramIn): bool
    {
        if (!isCountry(['TH', 'PH', 'MY'])) {
            return false;
        }

        $staffId = $paramIn["staff_id"];
        if (empty($staffId)) {
            return false;
        }
        $staffInfo = PaperDocumentConfirmStaffModel::findFirst([
            'conditions' => 'confirm_staff_id = :confirm_staff_id: and deleted = 0',
            'bind' => [
                'confirm_staff_id' => $staffId,
            ],
        ]);
        if (empty($staffInfo)) {
            return false;
        }
        return true;
    }

    /**
     * 格式化sn
     * @param $sn
     * @return false|string
     */
    private function formatSn($sn)
    {
        return substr($sn, 2);
    }

}