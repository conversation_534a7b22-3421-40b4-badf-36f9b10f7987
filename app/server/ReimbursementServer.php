<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\StaffRepository;

/**
 * 报销申请-相关操作服务层
 * @package FlashExpress\bi\App\Server
 */
class ReimbursementServer extends BaseServer
{
    public $timezone;
    const OA_VALIDATE_CODE = 2;

    /**
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * rpc请求OA端
     * @param array $params 请求参数组
     * @param string $method 请求方法
     * @return array
     */
    private function rpcOa(array $params, string $method)
    {
        $api_client = new ApiClient('oa_rpc', '', $method, $this->lang);
        $api_client->setParams($params);
        $res = $api_client->execute();
        if (!isset($res['result'])) {
            return [
                'code' => self::OA_VALIDATE_CODE,
                'msg'  => $this->getTranslation()->_('please try again'),
                'data' => [],
            ];
        }

        $res['result']['msg'] = $res['result']['message'] ?? '';
        unset($res['result']['message']);
        return $res['result'];
    }

    /**
     * 申请列表-筛选项枚举
     * @param array $params 请求参数组
     * @return array
     */
    public function getEnums(array $params)
    {
        return $this->rpcOa($params, 'get_reimbursement_enums');
    }

    /**
     * 申请列表-筛选项枚举
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function getApplyList(array $params, array $user)
    {
        $params['data_type'] = $params['data_type'] ?? 1;
        $params['user_id']   = $user['id'];
        return $this->rpcOa($params, 'get_reimbursement_list');
    }

    /**
     * 申请列表-签字再次提醒
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function signatureReminder(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'reimbursement_signature_reminder');
    }

    /**
     * 申请列表-确认详情
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function confirmedDetail(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'get_reimbursement_confirmed_detail');
    }

    /**
     * 申请列表-提交
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function submit(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'submit_reimbursement');
    }

    /**
     * 申请列表-撤回
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function cancel(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'cancel_reimbursement');
    }

    /**
     * 申请列表-下载
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function download(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'download_reimbursement');
    }

    /**
     * 申请列表-查看/查看详情
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function view(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'view_reimbursement');
    }

    /**
     * 申请列表-去确认
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function goConfirmInfo(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'go_confirm_reimbursement_info');
    }


    /**
     * 去确认-未共同住宿(拒绝确认)
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function rejectConfirm(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'reject_confirm_reimbursement_travel_roommate');
    }


    /**
     * 去确认-确认无误
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function agreeConfirm(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'agree_confirm_reimbursement_travel_roommate');
    }

    /**
     * 作废
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function invalid(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'invalid_reimbursement');
    }

    /**
     * 申请列表-去签字
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function goSignInfo(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'go_sign_reimbursement_info');
    }


    /**
     * 申请列表-去签字-拒绝签字
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function rejectSign(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'reject_sign_reimbursement');
    }

    /**
     * 申请列表-去签字-获取申请人身份证信息
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function getApplyIDInfo(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'get_reimbursement_apply_info');
    }

    /**
     * 申请列表-去签字-提交签字
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function submitSignInfo(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'submit_sign_info_reimbursement');
    }

    /**
     * 扫码签字/待签字消息-去签字
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function checkSignAuth(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'check_sign_auth_reimbursement');
    }

    /**
     * 待确认消息-去确认
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息
     * @return array
     */
    public function checkConfirmAuth(array $params, array $user)
    {
        $params['user_id']  = $user['id'];
        $params['platform'] = static::$flePlatform;
        return $this->rpcOa($params, 'check_confirm_auth_reimbursement');
    }

}
