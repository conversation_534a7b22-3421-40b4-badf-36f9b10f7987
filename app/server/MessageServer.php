<?php
/**
 * Created by PhpStor<PERSON>.
 * User: nick
 * Date: 2019/8/17
 * Time: 下午3:51
 */


namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\LeaveManageLogModel;
use FlashExpress\bi\App\Models\backyard\MessagePdfModel;
use FlashExpress\bi\App\Models\backyard\MessageWarningModel;
use FlashExpress\bi\App\Models\backyard\MessageWarningTransferSignModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\WarningSignConfigModel;
use FlashExpress\bi\App\Models\coupon\MessageContentModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\JobTitleRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;
use Phalcon\Db;

class MessageServer extends BaseServer
{

    protected $re;
    public    $timezone;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    const MSG_STAFF_TYPE_STAFF = 'staff';         //被警告人
    const MSG_STAFF_TYPE_SUPERIOR = 'superior';   // 上级
    const MSG_STAFF_TYPE_WITNESS_1 = 'witness_1'; // 证人1
    const MSG_STAFF_TYPE_WITNESS_2 = 'witness_2'; // 证人2
    const MSG_STAFF_TYPE_WITNESS = 'witness';     // 证人
    const MSG_STAFF_TYPE_OPERATOR = 'operator';   // 操作人--发信人 印尼用
    


    /**
     *  取ER角色 80 的员工
     * @param $all_node_staff_id
     * @return mixed
     */
    protected function getOnJobERStaffs($all_node_staff_id){
        // 取角色 80 的员工
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff.staff_info_id,staff.name');
        $builder->from(['staff' => HrStaffInfoModel::class]);
        $builder->join(HrStaffInfoPositionModel::class, 'position.staff_info_id = staff.staff_info_id', 'position');
        $builder->andWhere('position.position_category in ({position_category:array})', [
                'position_category' => [HrStaffInfoPositionModel::ER],
            ]
        );
        $builder->andWhere('hire_type != 13 and staff.state = :state: and staff.formal in ({formal:array}) and wait_leave_state = :wait_leave_state: and is_sub_staff = :is_sub_staff: and staff.staff_info_id not in ({all_node_staff_id:array})',
            [
                'state'             => HrStaffInfoModel::STATE_1,
                'wait_leave_state'  => HrStaffInfoModel::WAITING_LEAVE_NO,
                'is_sub_staff'      => HrStaffInfoModel::IS_SUB_STAFF_0,
                'formal'            => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                'all_node_staff_id' => $all_node_staff_id,
            ]
        );
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 获取警告信签字的人员
     * @param string $add_condition
     * @param array $add_bind
     * @return mixed
     */
    protected function getStaffByCondition(string $add_condition, array $add_bind = [])
    {
        $default_condition = "hire_type != :hire_type: and state = :state: and wait_leave_state = :wait_leave_state: and formal in ({formal:array}) and is_sub_staff = :is_sub_staff: ";
        $default_bind      = [
            'state'            => HrStaffInfoModel::STATE_1,
            'wait_leave_state' => HrStaffInfoModel::WAITING_LEAVE_NO,
            'is_sub_staff'     => HrStaffInfoModel::IS_SUB_STAFF_0,
            'formal'           => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
            'hire_type'        => HrStaffInfoModel::HIRE_TYPE_UN_PAID,
        ];
        $condition         = $add_condition ?  $add_condition. ' and ' . $default_condition : $default_condition;
        $bind              = array_merge($default_bind, $add_bind);

        return HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,name',
            'conditions' => $condition,
            'bind'       => $bind,
        ])->toArray();
    }



    /**
     * 警告书签字证人下拉列表逻辑
     * @param $param
     * @return array
     * @throws \Exception
     */
    public function sign_warning_witness_list($param)
    {
        $return_data   = ['default_witness_staff_list' => [], 'witness_staff_list' => []];
        $param         = filter_param($param);
        $message_model = Tools::reBuildCountryInstance(new MessageServer($this->lang, $this->timeZone),
            [$this->lang, $this->timeZone]);
        $warningInfo   = $message_model->getWarningMessage($param['staff_info_id'], $param['msg_id']);
        if (empty($warningInfo)) {
            throw new \Exception($this->t->_('miss_args'));
        }
        // 全节点的员工
        $all_node_staff_id = array_values(array_filter([
            $warningInfo['staff_info_id'] ?? '',
            $warningInfo['superior_id'] ?? '',
            $warningInfo['witness1_id'] ?? '',
            $warningInfo['witness2_id'] ?? '',
        ]));

        $staff_warning_message_id = $warningInfo['staff_warning_message_id'] ?? 0;
        $staffInfoData            = HrStaffInfoModel::findFirst([
            "staff_info_id = :staff_info_id:",
            "bind" => [
                "staff_info_id" => $warningInfo['staff_info_id'],
            ],
        ]);
        $staffInfoData            = $staffInfoData ? $staffInfoData->toArray() : [];
        // 这块逻辑公共的
        if ($staffInfoData['sys_store_id'] != '-1' && !empty($staffInfoData['sys_store_id'])) {
            // 与被警告员工同网点员工(非总部 在职、非子账号、不含外协,不含个人代理)

            $staff_2 = $this->getStaffByCondition('sys_store_id = :store_id: and staff_info_id not in ({all_node_staff_id:array})',
                ['all_node_staff_id' => $all_node_staff_id, 'store_id' => $staffInfoData['sys_store_id']]);

            $region_piece_manager = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getOrganizationRegionPieceManagerId($staffInfoData['sys_store_id']);
            // 大区 片区 负责人
            $staff_3 = $this->getStaffByCondition('staff_info_id in ({staff_info_id:array}) and staff_info_id not in ({all_node_staff_id:array})',
                [
                    'all_node_staff_id' => $all_node_staff_id,
                    'staff_info_id'     => [
                        $region_piece_manager['region_manager_id'],
                        $region_piece_manager['piece_manager_id'],
                    ],
                ]);
        }
        $settingEnvServer       = new SettingEnvServer();
        $without_witness_staffs = $settingEnvServer->getSetVal('sign_warning_not_default_witness_staff_ids', ',');

        // 20108需求 默认证人为空后的补充逻辑 
        $hrbps = (new WorkflowServer($this->lang, $this->timeZone))->findHRBP($staffInfoData['node_department_id'],['store_id' => $staffInfoData['sys_store_id']]);
        $hrbps = explode(',', $hrbps);
        $default_witness_supplement_staff_list = [];
        if ($hrbps) {
            // HRBP
            $default_witness_supplement_staff_list      = $this->getStaffByCondition('staff_info_id in ({staff_info_id:array}) and staff_info_id not in ({all_node_staff_id:array})',
                [
                    'all_node_staff_id' => $all_node_staff_id,
                    'staff_info_id'     => $hrbps,
                ]);
            if (count($default_witness_supplement_staff_list) <= 1){
                // ER
                $default_witness_supplement_staff_list = array_merge($default_witness_supplement_staff_list,$this->getOnJobERStaffs($all_node_staff_id));
            }
        }
        $return_data['default_witness_supplement_staff_list'] = array_values(array_filter(array_column($default_witness_supplement_staff_list,null,'staff_info_id')));

        // [1] 警告系统发出「HCM-警告系统-违规行为复核-发送警告书
        if ($staff_warning_message_id) {
            // 获取配置的默认证人
            $setting_env                           = new SettingEnvServer();
            $sign_warning_witness_list_default     = $setting_env->getSetVal('sign_warning_witness_list_default');
            $sign_warning_witness_list_default_arr = array_values(array_filter(explode(',',
                $sign_warning_witness_list_default)));
            if ($sign_warning_witness_list_default_arr) {
                $staff_1 = $this->getStaffByCondition('staff_info_id in ({staff_info_id:array}) and staff_info_id not in ({all_node_staff_id:array})',
                    [
                        'all_node_staff_id' => $all_node_staff_id,
                        'staff_info_id'     => $sign_warning_witness_list_default_arr,
                    ]);

                $default_witness_list = $this->getStaffByCondition('staff_info_id in ({staff_info_id:array}) and staff_info_id not in ({all_node_staff_id:array})',
                    [
                        'all_node_staff_id' => array_values(array_merge($all_node_staff_id, $without_witness_staffs)),
                        'staff_info_id'     => $sign_warning_witness_list_default_arr,
                    ]);

                if (count($default_witness_list) >= 2) {
                    foreach (array_rand($default_witness_list, 2) as $k => $v) {
                        $default_witness_staff_list[] = array_merge($default_witness_list[$v], ['type' => $k + 1]);
                    }
                } else {
                    $default_witness_staff_list = !empty($default_witness_list[0]) ? [array_merge($default_witness_list[0], ['type' => 1])] : [];
                }
                $return_data['default_witness_staff_list'] = $default_witness_staff_list ?? [];
            }
            // 取Network performance[1272]部门员工

            $staff_4 = $this->getStaffByCondition('node_department_id = :node_department_id: and staff_info_id not in ({all_node_staff_id:array})',
                [
                    'all_node_staff_id'  => $all_node_staff_id,
                    'node_department_id' => SysDepartmentModel::NETWORK_PERFORMANCE_TH,
                ]);

            // array_column 是为了根据员工去重
            $return_data['witness_staff_list'] = array_values(array_column(array_filter(array_merge($return_data['default_witness_staff_list'] ?? [],
                $staff_1 ?? [], $staff_2 ?? [],
                $staff_3 ?? [], $staff_4 ?? [])), null, 'staff_info_id'));

            // 提示语 警告系统 证人可选择同网点员工、DM、AM、Network Performance的员工
            $return_data['prompt'] = $this->t->_('warning_system_prompt');
            return $return_data;
        }


        //[2] 非警告系统发出「HCM-快捷工具-电子警告书-发送警告书
        // 获取默认证人
        $staff_witness_1 = [];
        $staff_witness_2 = [];
        //获取上上级和上上上级信息
        $staff_level = $this->getStaffMangerLevel($warningInfo['staff_info_id'], 3);
        if ($staff_level[2]['state'] == HrStaffInfoModel::STATE_1 && $staff_level[2]['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE_NO && !in_array($staff_level[2]['staff_info_id'],
                $all_node_staff_id)) {
            $staff_1[] = [
                'staff_info_id' => $staff_level[2]['staff_info_id'],
                'name'          => $staff_level[2]['name'],
            ];
        }

        if ($staff_level[3]['state'] == HrStaffInfoModel::STATE_1 && $staff_level[3]['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE_NO && !in_array($staff_level[3]['staff_info_id'],
                $all_node_staff_id)) {
            $staff_1[] = [
                'staff_info_id' => $staff_level[3]['staff_info_id'],
                'name'          => $staff_level[3]['name'],
            ];
        }

        if ($staff_level[2]['state'] == HrStaffInfoModel::STATE_1 && $staff_level[2]['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE_NO && !in_array($staff_level[2]['staff_info_id'],
                array_merge($all_node_staff_id, $without_witness_staffs))) {
            $staff_witness_1[] = [
                'staff_info_id' => $staff_level[2]['staff_info_id'],
                'name'          => $staff_level[2]['name'],
                'type'          => 1,
            ];
        } elseif ($staff_level[3]['state'] == HrStaffInfoModel::STATE_1 && $staff_level[3]['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE_NO && !in_array($staff_level[3]['staff_info_id'],
                array_merge($all_node_staff_id, $without_witness_staffs))) {
            $staff_witness_1[] = [
                'staff_info_id' => $staff_level[3]['staff_info_id'],
                'name'          => $staff_level[3]['name'],
                'type'          => 1,
            ];
        }


        $hrbps = (new WorkflowServer($this->lang, $this->timeZone))->findHRBP($staffInfoData['node_department_id'],
            ['store_id' => $staffInfoData['sys_store_id']]);
        $hrbps = explode(',', $hrbps);
        if ($hrbps) {
            $staff_hrbp      = $this->getStaffByCondition('staff_info_id in ({staff_info_id:array}) and staff_info_id not in ({all_node_staff_id:array}) and staff_info_id !=:staff_witness_1:',
                [
                    'all_node_staff_id' => array_values(array_merge($all_node_staff_id, $without_witness_staffs)),
                    'staff_info_id'     => $hrbps,
                    'staff_witness_1'   => $staff_witness_1[0]['staff_info_id'] ?? 0,
                ]);
            $staff_witness_2 = $staff_hrbp ? [array_merge($staff_hrbp[0], ['type' => 2])] : [];
        }


        $return_data['default_witness_staff_list'] = array_merge($staff_witness_1, $staff_witness_2);
        //ER[80]角色员工
        $staff_data = $this->getOnJobERStaffs($all_node_staff_id);
        $staff_4    = array_values(array_column($staff_data, null, 'staff_info_id'));
        // array_column 是为了根据员工去重
        $return_data['witness_staff_list'] = array_values(array_column(array_filter(array_merge($return_data['default_witness_staff_list'],
            $staff_1 ?? [], $staff_2 ?? [],
            $staff_3 ?? [], $staff_4 ?: [], $staff_hrbp ?? [])), null, 'staff_info_id'));
        // 提示语 非警告系统 证人可选择同网点员工、DM、AM、或是ER、BP角色的在职员工
        $return_data['prompt'] = $this->t->_('no_warning_system_prompt');
        return $return_data;
    }

    /**
     * 被警告人签署警告书主要处理逻辑
     * @param $warningInfo
     * @param $param
     * @param $staff_warning_message_id
     * @return false
     * @throws \Exception
     */
    public function sign_for_warning_staff($warningInfo, $param, $related_id)
    {
        if (empty($warningInfo) || empty($param) || !isset($param['url']) || !isset($param['staff_info_id'])) {
            throw new \Exception($this->t->_('miss_args'));
        }
        $model = new BaseRepository($this->lang);
        if ($warningInfo['img_url']) {
            throw new \Exception($this->t->_('5202'));
        }
        $updateInfo['img_url'] = $param['url']; // 更新签名;
        // 获取上级
        $mangerInfo = $this->getSuperiorInfo($param['staff_info_id']);
        if ($mangerInfo) {
            if (!$warningInfo['superior_kit_id']) {
                // 如果没有发送给上级 发送上级给消息
                $updateInfo['superior_id']   = (int)$mangerInfo['staff_id'];
                $updateInfo['superior_name'] = $mangerInfo['name'];

                $this->sendWarningMessage($mangerInfo['staff_id'], $warningInfo['id'], $warningInfo['role'],
                    $mangerInfo['level'],0,$related_id);
            }
            $flag = $model->updateInfoByTable('message_warning', 'id', $warningInfo['id'], $updateInfo, 'db');
            return $flag;
        } else {
            $this->getDI()->get("logger")->write_log($param['staff_info_id'] . " 不存在上级", "notice");
            throw new \Exception($this->t->_('not_found_superior'));
        }
    }

    /**
     * 证人1和2 签署警告书主要处理逻辑
     * @param $warningInfo
     * @param $param
     * @return false
     * @throws \Exception
     */
    public function sign_for_warning_witness($warningInfo, $param, $role)
    {
        if (empty($warningInfo) || empty($param) || !isset($param['url']) || !isset($param['staff_info_id'])) {
            throw new \Exception($this->t->_('miss_args'));
        }
        $model = new BaseRepository($this->lang);
        if ($role == self::MSG_STAFF_TYPE_WITNESS_1) {
            if ($warningInfo['witness1_img']) {
                throw new \Exception($this->t->_('5202'));
            }
            $data['witness1_img'] = $param['url'];
        } elseif ($role == self::MSG_STAFF_TYPE_WITNESS_2) {
            if ($warningInfo['witness2_img']) {
                throw new \Exception($this->t->_('5202'));
            }
            $data['witness2_img'] = $param['url'];
        } else {
            throw new \Exception($this->t->_('not_found_superior'));
        }
        $flag = $model->updateInfoByTable('message_warning', 'id', $warningInfo['id'], $data, 'db');
        // 更新最终pdf
        $this->sendWarningMessage("0", $warningInfo['id'], $warningInfo['role']);
        return $flag;
    }

    /**
     * 上级签署警告书主要处理逻辑
     * @param $warningInfo
     * @param $url
     * @param $staff_info_id
     * @return false
     * @throws \Exception
     */
    public function sign_for_warning_superior($warningInfo, $param, $related_id)
    {
        if (empty($warningInfo) || empty($param) || !isset($param['witness1_staff_id']) || !isset($param['witness2_staff_id']) || !isset($param['staff_info_id'])) {
            throw new \Exception($this->t->_('miss_args'));
        }
        $model = new BaseRepository($this->lang);
        if ($warningInfo['superior_img']) {
            throw new \Exception($this->t->_('5202'));
        }
        if ($param['witness1_staff_id'] == $param['witness2_staff_id']) {
            throw new \Exception($this->t->_('witness_not_same'));
        }
        if ($param['witness1_staff_id'] == $warningInfo['staff_info_id'] || $param['witness2_staff_id'] == $warningInfo['staff_info_id']) {
            throw new \Exception($this->t->_('witness_no_warning_staff'));
        }
        // 全节点的员工
        $all_node_staff_id = array_values(array_filter([
            $warningInfo['staff_info_id'] ?? '',
            $warningInfo['superior_id'] ?? '',
            $warningInfo['witness1_id'] ?? '',
            $warningInfo['witness2_id'] ?? '',
        ]));
        // 校验选择的证人不能和之前节点员工相同
        if (in_array($param['witness1_staff_id'],$all_node_staff_id) || in_array($param['witness2_staff_id'],$all_node_staff_id)){
            throw new \Exception($this->t->_('witness_repeat_1'));
        }
        if (in_array($param['witness2_staff_id'],$all_node_staff_id)){
            throw new \Exception($this->t->_('witness_repeat_2'));
        }
        $sign_warning_witness_list = $this->sign_warning_witness_list(['msg_id'=>$warningInfo['superior_kit_id'],'staff_info_id'=>$warningInfo['superior_id']]);
        $sign_warning_witness_list_arr = array_merge($sign_warning_witness_list['witness_staff_list'],$sign_warning_witness_list['default_witness_supplement_staff_list']);
        $sign_warning_witness_list_arr_name = array_column($sign_warning_witness_list_arr,null,'staff_info_id');
        if (!in_array($param['witness1_staff_id'],array_column($sign_warning_witness_list_arr,'staff_info_id')) || !in_array($param['witness2_staff_id'],array_column($sign_warning_witness_list_arr,'staff_info_id'))){
            throw new \Exception($this->t->_('witness_info_error_th'));
        }
        // 更新签名
        // 证人信息
        $flag = $model->updateInfoByTable('message_warning', 'id', $warningInfo['id'], [
            "superior_img"    => $param['url'],
            "witness1_id"     => $param['witness1_staff_id'],
            "witness1_name"   => $sign_warning_witness_list_arr_name[$param['witness1_staff_id']]['name'],
            "witness2_id"     => $param['witness2_staff_id'],
            "witness2_name"   => $sign_warning_witness_list_arr_name[$param['witness2_staff_id']]['name'],
            "superior_remark" => !empty($param['remark']) ? $param['remark'] : '',
        ], 'db');
        // 发送证人消息
        $this->sendWarningMessage($param['witness1_staff_id'] . "," . $param['witness2_staff_id'],
            $warningInfo['id'], $warningInfo['role'],0,0,$related_id);
        return $flag;
    }

    //电子警告书 电子签名 保存图片
    public function sign_for_warning($param)
    {
        $param = filter_param($param);
        // 更新签名图片
        //判断身份 后 发送给上级消息 或 证人消息
        $message_model = Tools::reBuildCountryInstance(new MessageServer($this->lang, $this->timeZone), [$this->lang, $this->timeZone]);
        $warningInfo = $message_model->getWarningMessage($param['staff_info_id'], $param['msg_id']);
        // $staff_warning_message_id = 0 : HCM-快捷工具-电子警告书-发送警告书
        // $staff_warning_message_id != 0 : HCM-警告系统-违规行为复核-发送警告书
        $staff_warning_message_id = $warningInfo['staff_warning_message_id'] ?? 0;
        $flag                     = false;
        $db_coupon                = $this->getDI()->get('db_coupon');
        try {
            $setting_env                           = new SettingEnvServer();
            // 获取警告书签字天数限制-非警告系统
            $no_warning_system_limitation_days     = $setting_env->getSetVal('no_warning_system_limitation_days');
            $no_warning_system_limitation_days_arr = json_decode($no_warning_system_limitation_days, true);
            // 获取警告书签字天数限制-警告系统
            $warning_system_limitation_days        = $setting_env->getSetVal('warning_system_limitation_days');
            $warning_system_limitation_days_arr    = json_decode($warning_system_limitation_days, true);
            // 获取警告书-非警告系统 各节点是否限制打卡
            $no_warning_system_is_limitation_clock        = $setting_env->getSetVal('no_warning_system_is_limitation_clock');
            $no_warning_system_is_limitation_clock_arr    = json_decode($no_warning_system_is_limitation_clock, true);
            // 获取警告书-警告系统 各节点是否限制打卡
            $warning_system_is_limitation_clock        = $setting_env->getSetVal('warning_system_is_limitation_clock');
            $warning_system_is_limitation_clock_arr    = json_decode($warning_system_is_limitation_clock, true);
            $superior_related_id = 0;
            $witness_related_id = 0;
            if (empty($staff_warning_message_id)){
                // HCM-快捷工具-电子警告书-发送警告书
                // 获取配置 是否限制打卡天数
                if ($no_warning_system_is_limitation_clock_arr['superior'] == MessageEnums::IS_LIMITATION_CLOCK_YES){
                    $superior_related_id = strtotime(date("Y-m-d 00:00:00", strtotime("+".$no_warning_system_limitation_days_arr['superior']." day")));
                }
                if ($no_warning_system_is_limitation_clock_arr['witness'] == MessageEnums::IS_LIMITATION_CLOCK_YES){
                    $witness_related_id = strtotime(date("Y-m-d 00:00:00", strtotime("+".$no_warning_system_limitation_days_arr['witness']." day")));
                }
            }else{
                // HCM-警告系统-违规行为复核-发送警告书
                // 获取配置 是否限制打卡天数
                if ($warning_system_is_limitation_clock_arr['superior'] == MessageEnums::IS_LIMITATION_CLOCK_YES){
                    $superior_related_id = strtotime(date("Y-m-d 00:00:00", strtotime("+".$warning_system_limitation_days_arr['superior']." day")));
                }
                if ($warning_system_is_limitation_clock_arr['witness'] == MessageEnums::IS_LIMITATION_CLOCK_YES){
                    $witness_related_id = strtotime(date("Y-m-d 00:00:00", strtotime("+".$warning_system_limitation_days_arr['witness']." day")));
                }
            }
            switch ($warningInfo['role']) {
                case self::MSG_STAFF_TYPE_STAFF:
                    // 被警告人
                    $flag = $this->sign_for_warning_staff($warningInfo, $param, $superior_related_id);
                    break;
                case self::MSG_STAFF_TYPE_SUPERIOR:
                    // 上级
                    $flag = $this->sign_for_warning_superior($warningInfo, $param, $witness_related_id);
                    break;
                case self::MSG_STAFF_TYPE_WITNESS_1:
                    // 证人1
                    $flag = $this->sign_for_warning_witness($warningInfo, $param,self::MSG_STAFF_TYPE_WITNESS_1);
                    break;
                case self::MSG_STAFF_TYPE_WITNESS_2:
                    // 证人2
                    $flag = $this->sign_for_warning_witness($warningInfo, $param,self::MSG_STAFF_TYPE_WITNESS_2);
                    break;
                default:
                    break;
            }
            // 消息更新为已读
            if ($flag) {
                //如果保存成功 设置为已读
                $up_sql = "update message_courier set read_state = 1,top_state = 0 where id = '{$param['msg_id']}'";
                $db_coupon->execute($up_sql);
                // 消息已读
                $server = new BackyardServer($this->lang, $this->timezone);
                $server->addHaveReadMsgToMns($param['msg_id']);

                // 签字日志
                $sign_log_data = [
                    'staff_info_id'      => $param['staff_info_id'] ?? 0,
                    'kit_id'             => $param['msg_id'] ?? '',
                    'message_warning_id' => $warningInfo['id'] ?? 0,
                    'is_auto_sign'       => 0,
                ];
                $this->getDI()->get('db')->insertAsDict('message_warning_sign_log', $sign_log_data);
            } else {
                throw new \Exception($this->t->_('4008'));
            }
        } catch (\Exception $e) {
            throw new BusinessException($e->getMessage());
        }
        return true;
    }

    /**
     * @throws \ReflectionException
     * @throws ValidationException
     */
    public function get_sign_warning_pdf_data($param)
    {
        $param = filter_param($param);
        $msg_id = $param['msg_id'] ?? '';
        $staff_info_id = $param['staff_info_id'] ?? '';
        if (empty($msg_id) || empty($staff_info_id)){
            throw new ValidationException($this->t->_('miss_args'));
        }
        // 获取警告书信息
        $message_model = Tools::reBuildCountryInstance(new MessageServer($this->lang, $this->timeZone), [$this->lang, $this->timeZone]);
        $wainingInfo = $message_model->getWarningMessage($staff_info_id, $msg_id);
        if (empty($wainingInfo)){
            throw new ValidationException($this->t->_('data_error'));
        }
        $warning_id = $wainingInfo['id'];
        $staffInfo = (new StaffRepository())->getStaffInfoAllOne($wainingInfo['staff_info_id']);

        if(!empty($wainingInfo['department_id'])) {
            $staffInfo['department_name'] = (new DepartmentRepository($this->lang))->getDepartmentNameById($wainingInfo['department_id']);
        }

        if(!empty($wainingInfo['job_title'])) {
            $jobTitle = JobTitleRepository::getJobTitleInfo($wainingInfo['job_title']);
            $staffInfo['job_title_name'] = !empty($jobTitle) ? $jobTitle->job_name : $staffInfo['job_title_name'];
        }

        if(!empty($wainingInfo['store_id'])) {
            $storeName = SysStoreRepository::getStoreName($wainingInfo['store_id']);
            $staffInfo['store_name'] = !empty($storeName) ? $storeName : $staffInfo['store_name'];
        }

        $length = 5;
        if ($warning_id > 99999) {
            $length = strlen($warning_id);
        }
        $wainingInfo['created_at'] = show_time_zone($wainingInfo['created_at']);
        $warningType = $wainingInfo['warning_type'] ?? 0;
        $wainingInfo["numbering"] = MessageWarningModel::WARNING_TYPE_NUMBERING[$warningType] ?? "";
        $wainingInfo['number'] = str_pad($wainingInfo['number'], $length, "0", STR_PAD_LEFT);
        $wainingInfo['year'] = date("Y", strtotime($wainingInfo['created_at'])) + 543;
        $wainingInfo['date'] = date("d", strtotime($wainingInfo['created_at'])) . " " . $this->getTranslationByLang('th')->t(strtolower("calendar_" . MessageWarningModel::MONTHS_MAPS[date("n", strtotime($wainingInfo['created_at']))]));
        $wainingInfo['job_title_name'] = $staffInfo['job_title_name'] ?? '';
        $wainingInfo['storeName']      = $staffInfo['store_name'];
        $wainingInfo['department']      = $staffInfo['department_name'];
        $wainingInfo['wainingType'] = $this->t->_("warning_type_" . $warningType);
        $wainingInfo['superiorRemark'] = !empty($wainingInfo['superior_remark']) ? $wainingInfo['superior_remark'] : "";
        $wainingInfo['current_param_msg_is_read'] = MessageCourierModel::READ_STATE_UNREAD;
        $message = MessageCourierModel::findFirst([
            "conditions" => " id = :id:",
            'bind' => ['id' => $msg_id],
        ]);
        if ($message && $message->read_state == MessageCourierModel::READ_STATE_HAS_READ) {
            $wainingInfo['current_param_msg_is_read'] = MessageCourierModel::READ_STATE_HAS_READ;
        }
        $setting_env                           = new SettingEnvServer();
        // 获取警告书签字天数限制-非警告系统
        $no_warning_system_limitation_days     = $setting_env->getSetVal('no_warning_system_limitation_days');
        $no_warning_system_limitation_days_arr = json_decode($no_warning_system_limitation_days, true);
        // 获取警告书签字天数限制-警告系统
        $warning_system_limitation_days        = $setting_env->getSetVal('warning_system_limitation_days');
        $warning_system_limitation_days_arr    = json_decode($warning_system_limitation_days, true);
        // 当前消息提示语逻辑
        if (empty($wainingInfo['staff_warning_message_id'])){
            // 非警告系统发出「HCM-快捷工具-电子警告书-发送警告书」
            [$staff_begin,$staff_end,] = $this->get_gmdate($no_warning_system_limitation_days_arr['staff']);
        }else{
            // 警告系统发出「HCM-警告系统-违规行为复核-发送警告书」
            [$staff_begin,$staff_end,] = $this->get_gmdate($warning_system_limitation_days_arr['staff']);
        }
        $message_warning_transfer_sign = $this->get_message_warning_transfer_sign([$warning_id]);
        $transfer_sign_message_warning_id = array_column($message_warning_transfer_sign, null, 'kit_id');
        $current_transfer_sign_message_warning = $transfer_sign_message_warning_id[$msg_id] ?? [];
        // 当前登陆人是节点2 被警告人没签字 超时提示
        if ($wainingInfo['superior_id'] == $staff_info_id && empty($wainingInfo['img_url']) && $wainingInfo['created_at'] < $staff_end && $current_transfer_sign_message_warning){
            if ($current_transfer_sign_message_warning['level'] == MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR){
                // 上级签字页面top提示
                $current_param_msg_top_prompt = $this->t->_('warning_message_prompt_1');
            }elseif ($current_transfer_sign_message_warning['level'] == MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR_SUPERIOR){
                // 上上级签字页面提示
                $current_param_msg_top_prompt = $this->t->_('warning_message_prompt_2');
            }
        }
        if ($wainingInfo['superior_id'] == $staff_info_id && !empty($wainingInfo['img_url']) && $current_transfer_sign_message_warning['level'] == MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR_SUPERIOR){
            // 当前登陆人是节点2 被警告人已签字 上上级超时提示
            $current_param_msg_top_prompt = $this->t->_('warning_message_prompt_3');
        }
        // 曾是签字人的转交人 都显示超时
        if (!empty($current_transfer_sign_message_warning) && $wainingInfo['witness1_id'] != $staff_info_id && $wainingInfo['witness2_id'] != $staff_info_id && $wainingInfo['superior_id'] != $staff_info_id && $wainingInfo['staff_info_id'] != $staff_info_id){
            $current_param_msg_bottom_prompt = $this->t->_('sign_overtime');
        }
        $wainingInfo['current_param_msg_bottom_prompt'] = $current_param_msg_bottom_prompt ?? '';
        $wainingInfo['current_param_msg_top_prompt'] = $current_param_msg_top_prompt ?? '';
        //获取公司信息
        $client =  new ApiClient('hcm_rpc','','get_warning_message_company_info');
        $client->setParams(['staff_info_id'=>$staffInfo['staff_info_id']]);
        $res = $client->execute();
        return array_merge($wainingInfo,$res['result']['data']??[]);
    }

    /**
     * 查询指定工号上级
     * @param $staffId
     * @return array
     */
    public function getMangerInfo($staffId)
    {
        if (empty($staffId)) {
            return [];
        }
        // 获取上级
        $mangerInfo = HrStaffItemsModel::findFirst([
            'conditions' => "staff_info_id = :staff_id: and item = 'MANGER'",
            'bind'       => ['staff_id' => $staffId],
        ]);

        return empty($mangerInfo) ? [] : $mangerInfo->toArray();
    }

    /**
     * 获取员工信息
     * @param $staffId
     * @return array
     */
    public function getStaffInfo($staffId)
    {
        if (empty($staffId)) {
            return [];
        }
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_id:",
            'bind'       => ['staff_id' => $staffId],
            'columns'    => 'staff_info_id, name, state, node_department_id, sys_store_id',
        ]);

        return empty($staffInfo) ? [] : $staffInfo->toArray();
    }

    /**
     * 获取被警告人上级，上上级，hr
     * @param $staffId
     * @return array
     */
    public function getSuperiorInfo($staffId)
    {
        //获取上级信息
        $superior = $this->getMangerLevelInfo($staffId, MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR);
        if (!empty($superior)) {
            return $superior;
        }

        //如果上级非在职或待离职 则找上上级
        $mangerInfo = $this->getMangerInfo($staffId);
        if (!empty($mangerInfo)) {
            $superiorSuperior = $this->getMangerLevelInfo($mangerInfo['value'],
                MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR_SUPERIOR);
            if (!empty($superiorSuperior)) {
                return $superiorSuperior;
            }
        }

        return [];
    }

    /**
     * 获取员工上级
     * @param $staffId
     * @param int $level
     * @return array
     */
    public function getMangerLevelInfo($staffId, $level = 0)
    {
        //获取上级信息
        $mangerInfo = $this->getMangerInfo($staffId);
        if (!empty($mangerInfo)) {
            $staffInfo = $this->getStaffInfo($mangerInfo['value']);
            if (!empty($staffInfo) && $staffInfo['state'] == HrStaffInfoModel::STATE_1) {
                return ['staff_id' => $staffInfo['staff_info_id'], 'name' => $staffInfo['name'], 'level' => $level];
            }
        }

        return [];
    }

    /**
     * new 获取指定员工上级 level=2则是上上级，=3则是上上上级，依次循环
     * @param $staffId
     * @param $level
     * @return array
     */
    public function getStaffMangerLevel($staffId, $level = 1)
    {
        $i          = 0;
        $staff_list = [];
        while ($i < $level) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns("staff.staff_info_id,staff.name,staff.state,staff.wait_leave_state");
            $builder->from(['item' => HrStaffItemsModel::class]);
            $builder->join(HrStaffInfoModel::class, "staff.staff_info_id = item.value and item.item = 'MANGER'",
                'staff');
            $builder->andWhere('item.staff_info_id = :staff_info_id:', ['staff_info_id' => $staffId]);
            $staffList = $builder->getQuery()->execute()->getFirst();
            if (empty($staffList)) {
                break;
            }
            $staffList = $staffList->toArray();
            $i++;
            $staffList['level'] = $i;
            $staff_list[$i]     = $staffList;
            $staffId            = $staffList['staff_info_id'];
        }
        return $staff_list;
    }

    /**
     * 获取被警告人 所在管辖的hrbp
     * @param $staffId
     * @param $type
     * @return array
     */
    public function getHrBp($staffId, $type = MessageWarningTransferSignModel::TRANSFER_TYPE_SUPERIOR)
    {
        $level = MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_BP;
        if ($type == MessageWarningTransferSignModel::TRANSFER_TYPE_WITNESS) {
            $level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_BP;
        }
        $staffInfo = $this->getStaffInfo($staffId);
        //校验转岗前网点 & 部门是否存在hrbp如果不存在，则不能申请
        $hrBp = (new WorkflowServer($this->lang, $this->timezone))->findHRBP($staffInfo['node_department_id'],
            ["store_id" => $staffInfo['sys_store_id']]);
        if (!empty($hrBp)) {
            $arrHrBp   = explode(",", $hrBp);
            $key       = array_rand($arrHrBp);
            $staffInfo = $this->getStaffInfo($arrHrBp[$key]);
            return [
                'staff_id'    => $staffInfo['staff_info_id'],
                'name'        => $staffInfo['name'],
                'level'       => $level,
                'all_hrbp_id' => $arrHrBp,
            ];
        }

        return [];
    }

    //获取bi 警告信信息 判断是否签字
    public function get_warning($staff_id, $msg_id)
    {
        if (empty($staff_id) || empty($msg_id)) {
            return false;
        }
        //获取相关签名
        $warning_sql  = "select id,img_url, is_delete from message_warning where staff_info_id = {$staff_id} and kit_id = '{$msg_id}' ";
        $warning_info = $this->getDI()->get('db_rby')->query($warning_sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $warning_info;
    }


    protected function getWarningMessageFromQueryList($staffId, $msgId)
    {
        $list = [
            'staff_info_id = :staff_id and kit_id = :msg_id',
            'superior_id = :staff_id and superior_kit_id = :msg_id',
            'operator_id = :staff_id and operator_kit_id = :msg_id',
            'witness1_id = :staff_id and witness1_kit_id = :msg_id',
            'witness2_id = :staff_id and witness2_kit_id = :msg_id',
        ];
        foreach ($list as $item) {
            $sql         = " select * from message_warning where " . $item;
            $warningInfo = $this->getDI()->get("db_rby")->fetchOne($sql, Db::FETCH_ASSOC, [
                "staff_id" => $staffId,
                "msg_id"   => $msgId,
            ], [
                "staff_id" => Db\Column::BIND_PARAM_INT,
                "msg_id"   => Db\Column::BIND_PARAM_STR,
            ]);
            if (!empty($warningInfo)) {
                return $warningInfo;
            }
        }
        return [];
    }


    /**
     * 尝试
     * 根据员工ID和消息ID
     * 获取警告信
     * 并且返回当前身份
     *
     * @param $staffId
     * @param $msgId
     *
     */
    public function getWarningMessage($staffId, $msgId)
    {
        $warningInfo = $this->getWarningMessageFromQueryList($staffId, $msgId);


        //如果根据msg_id查不到警告书内容，则根据related_id获取
        //并将msg_id回写到message_warning表
        if (!$warningInfo) {
            $warning         = false;
            $message_courier = MessageCourierModel::findFirst([
                'conditions' => "id = :id:",
                'bind'       => ['id' => $msgId],
            ]);
            if ($message_courier) {
                $message_content = MessageContentModel::findFirst([
                    'conditions' => "id = :id:",
                    'bind'       => ['id' => $message_courier->message_content_id],
                ]);
                if ($message_content && !empty($message_content->related_id)) {
                    $warning = MessageWarningModel::findFirst([
                        'conditions' => "id = :id:",
                        'bind'       => ['id' => $message_content->related_id],
                    ]);
                }
            }
            if ($warning) {
                if ($warning->staff_info_id == $staffId) {
                    $warning->kit_id = $msgId;
                } elseif ($warning->superior_id == $staffId) {
                    $warning->superior_kit_id = $msgId;
                } elseif ($warning->witness1_id == $staffId) {
                    $warning->witness1_kit_id = $msgId;
                } elseif ($warning->witness2_id == $staffId) {
                    $warning->witness2_kit_id = $msgId;
                } elseif ($warning->operator_id == $staffId) {
                    $warning->operator_kit_id = $msgId;
                } else {
                    $this->getDI()->get("logger")->write_log("warning_message 警告书获取失败，匹配不到对应的员工工号 " . json_encode([
                            $staffId,
                            $msgId,
                        ], JSON_UNESCAPED_UNICODE), "error");
                    return [];
                }
                $warning->save();
                $warningInfo = $warning->toArray();
            }
        }


        if ($warningInfo['staff_info_id'] == $staffId && $warningInfo['kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_STAFF;
        } elseif ($warningInfo['superior_id'] == $staffId && $warningInfo['superior_kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_SUPERIOR;
        } elseif ($warningInfo['witness1_id'] == $staffId && $warningInfo['witness1_kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_WITNESS_1;
        } elseif ($warningInfo['witness2_id'] == $staffId && $warningInfo['witness2_kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_WITNESS_2;
        } elseif ($warningInfo['operator_id'] == $staffId && $warningInfo['operator_kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_OPERATOR;
        }

        return $warningInfo;
    }


    /**
     * 上级验证证人
     *
     * 发送给上级的消息ID
     * @param  $msg_id
     * 证人ID
     * @param $staffId
     * @return array
     *
     */
    public function witenessInfo($msg_id, $staffId)
    {
        $message     = $this->getDI()->get("db_coupon_r")->fetchOne("select * from message_courier where id = :id",
            Db::FETCH_ASSOC, ["id" => $msg_id], ["id" => Db\Column::BIND_PARAM_STR]);
        $warningInfo = $this->getWarningMessage($message['staff_info_id'], $msg_id);

        if ($staffId == $warningInfo['staff_info_id']) {
            return $this->checkReturn(-3, $this->getTranslation()->_('witness_no_warning_staff'));
        }

        $sendStaffsInfo = MessageWarningTransferSignModel::find([
            "conditions" => " message_warning_id = :warning_id:",
            'bind'       => ['warning_id' => $warningInfo['id']],
            'columns'    => ['staff_info_id'],
        ])->toArray();
        $sendStaffIds   = [];
        if (!empty($sendStaffsInfo)) {
            $sendStaffIds = array_column($sendStaffsInfo, 'staff_info_id');
        }
        $sendStaffIds[] = $warningInfo['superior_id'];

        if (in_array($staffId, $sendStaffIds)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('signatory_not_repeat'));
        }

        //获取被警告人网点
        $staff_store  = HrStaffInfoModel::findFirst([
            "conditions" => " staff_info_id = :staff_info_id:",
            'bind'       => ['staff_info_id' => $warningInfo['staff_info_id']],
            'columns'    => ['sys_store_id'],
        ]);
        $sys_store_id = '';
        if ($staff_store) {
            $sys_store_id = $staff_store->sys_store_id;
        }

        //获取证人信息
        $staffInfo = HrStaffInfoModel::findFirst([
            "conditions" => " staff_info_id = :warning_staff_id:",
            "bind"       => ['warning_staff_id' => $staffId],
        ]);
        if (empty($staffInfo)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
        }
        $staffInfo = $staffInfo->toArray();

        //获取证人角色
        $positions    = HrStaffInfoPositionModel::find([
            "conditions" => " staff_info_id = :warning_staff_id:",
            "bind"       => ['warning_staff_id' => $staffId],
        ])->toArray();
        $positionsArr = array_column($positions, 'position_category');

        $er_job_ids = [
            enums::$job_title['er_officer'],
            enums::$job_title['employee_relation_specialist'],
            enums::$job_title['employee_relation_manager'],
        ];

        //查询证人是否是证人配置里的信息
        $signConfig = WarningSignConfigModel::findFirst([
            'columns'    => 'sign_url',
            'conditions' => 'staff_info_id = :staff_id: AND signature_node=:signature_node:',
            'bind'       => [
                'staff_id'       => $staffId,
                'signature_node' => WarningSignConfigModel::SIGNATURE_NODE_WITNESS,
            ],
        ]);

        // 证人与被警告人所在同一个网点
        // 或证人是外协员工
        // 或证人职位er officer的员工
        // 或Er，HRBP角色
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_1 && (
                $staffInfo['sys_store_id'] == $sys_store_id || $staffInfo['formal'] == HrStaffInfoModel::FORMAL_0
                || in_array($staffInfo['job_title'], $er_job_ids)
                || array_intersect([RolesModel::ROLE_ER, RolesModel::ROLE_HR_BP], $positionsArr)
                || !empty($signConfig)
            )
        ) {
            return $this->checkReturn([
                "data" => [
                    "staff_id" => $staffInfo['staff_info_id'],
                    "name"     => $staffInfo['name'],
                ],
            ]);
        }

        return $this->checkReturn(-3, $this->getTranslation()->_('witness_info_error_th'));
    }

    /**
     *
     * 发送警告书消息
     * 给被警告人上级
     * 给被警告人上级的证人
     *
     * @param $staffIds
     * @param $warningId
     * @param $role
     * @param $level -层级
     * @param $witness
     * @param $related_id
     *
     */
    public function sendWarningMessage($staffIds, $warningId, $role, $level = 0, $witness = 0 ,$related_id = 0)
    {
        $fle_rpc = (new ApiClient('hcm_rpc', '', 'sendWarningMessage', $this->lang));
        $fle_rpc->setParams([
            "staff_ids"  => $staffIds,
            "warning_id" => $warningId,
            "role"       => $role,
            "level"      => $level,
            "witness"    => $witness,
            "related_id" => $related_id,
        ]);
        $ret = $fle_rpc->execute();
        if ($ret && isset($ret['result']) && $ret['result']['code'] == 1) {
            return true;
        }
        return false;
    }

    /**
     * 获取警告书转交记录
     * @param $staffId
     * @param $msgId
     * @return array
     */
    public function getWarningTransferInfo($staffId, $msgId)
    {
        $signHistory = MessageWarningTransferSignModel::findFirst([
            'conditions' => 'staff_info_id=:staff_info_id: AND kit_id = :msg_id:',
            'bind'       => ['staff_info_id' => $staffId, 'msg_id' => $msgId],
            'columns'    => 'message_warning_id, type, level',
        ]);

        return !empty($signHistory) ? $signHistory->toArray() : [];
    }

    public function get_gmdate($day, $end_now = false)
    {
        $date  = date('Y-m-d', strtotime(" -" . $day . " days "));
        $begin = gmdate('Y-m-d H:i:s', strtotime($date . ' 00:00:00'));
        if ($end_now) {
            $end = gmdate('Y-m-d H:i:s', strtotime(date('Y-m-d', time()) . ' 23:59:59'));
        } else {
            $end = gmdate('Y-m-d H:i:s', strtotime($date . ' 23:59:59'));
        }
        return [$begin, $end];
    }

    /**
     * 检测节点1 是否符合转交給节点2
     * @param $warning_message
     * @param $message_warning_transfer_sign
     * @param $begin
     * @param $end
     * @return bool
     */
    public function check_staff_untreated($warning_message, $message_warning_transfer_sign, $begin, $end)
    {
        $transfer_sign_message_warning_id = array_column($message_warning_transfer_sign, null, 'message_warning_id');
        if (
            $warning_message['created_at'] >= $begin &&
            $warning_message['created_at'] <= $end &&
            !empty($warning_message['kit_id']) &&
            empty($warning_message['img_url']) &&
            empty($warning_message['superior_id']) &&
            empty($warning_message['superior_kit_id']) &&
            empty($warning_message['superior_img']) &&
            !isset($transfer_sign_message_warning_id[$warning_message['id']])
        ) {
            return true;
        }
        return false;
    }

    /**
     * 检测节点2 是否符合转交
     * @param $warning_message
     * @param $message_warning_transfer_sign
     * @param $begin
     * @param $end
     * @param $superior_staff_state
     * @return bool
     */
    public function check_superior_untreated(
        $warning_message,
        $message_warning_transfer_sign,
        $begin,
        $end,
        $superior_staff_state
    ) {
        $transfer_sign_message_kit_id = array_column($message_warning_transfer_sign, null, 'kit_id');
        if (
            !empty($warning_message['superior_kit_id']) &&
            !empty($warning_message['superior_id']) &&
            empty($warning_message['superior_img']) &&
            $superior_staff_state == HrStaffInfoModel::STATE_2 &&
            $transfer_sign_message_kit_id[$warning_message['superior_kit_id']]['level'] != MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR_SUPERIOR
        ) {
            return true;
        }
        return false;
    }

    /**
     * 检测节点3 证人 是否符合转交
     * @param $warning_message
     * @param $message_warning_transfer_sign
     * @param $begin
     * @param $end
     * @param $superior_staff_state
     * @return false[]
     */
    public function check_witness_untreated(
        $warning_message,
        $message_warning_transfer_sign,
        $begin,
        $end,
        $witness1_staff_state = 0,
        $witness2_staff_state = 0
    ) {
        $return_data                  = ["witness1" => false, "witness2" => false];
        $transfer_sign_message_kit_id = array_column($message_warning_transfer_sign, null, 'kit_id');
        if (empty($warning_message['staff_warning_message_id'])){
            // 非警告系统
            if (
                !empty($warning_message['witness1_kit_id']) &&
                !empty($warning_message['witness1_id']) &&
                empty($warning_message['witness1_img']) &&
                isset($transfer_sign_message_kit_id[$warning_message['witness1_kit_id']]) &&
                $transfer_sign_message_kit_id[$warning_message['witness1_kit_id']]['created_at'] >= $begin &&
                $transfer_sign_message_kit_id[$warning_message['witness1_kit_id']]['created_at'] <= $end &&
                !in_array($transfer_sign_message_kit_id[$warning_message['witness1_kit_id']]['level'], [
                    MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_BP,
                    MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER,
                ]) &&
                (($witness1_staff_state && $witness1_staff_state == HrStaffInfoModel::STATE_2) || $witness1_staff_state == 0)
            ) {
                $return_data["witness1"] = true;
            }
            if (
                !empty($warning_message['witness2_kit_id']) &&
                !empty($warning_message['witness2_id']) &&
                empty($warning_message['witness2_img']) &&
                isset($transfer_sign_message_kit_id[$warning_message['witness2_kit_id']]) &&
                $transfer_sign_message_kit_id[$warning_message['witness2_kit_id']]['created_at'] >= $begin &&
                $transfer_sign_message_kit_id[$warning_message['witness2_kit_id']]['created_at'] <= $end &&
                !in_array($transfer_sign_message_kit_id[$warning_message['witness2_kit_id']]['level'], [
                    MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_BP,
                    MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER,
                ]) &&
                (($witness2_staff_state && $witness2_staff_state == HrStaffInfoModel::STATE_2) || $witness2_staff_state == 0)
            ) {
                $return_data["witness2"] = true;
            }
        }else{
            // 警告系统
            if (
                !empty($warning_message['witness1_kit_id']) &&
                !empty($warning_message['witness1_id']) &&
                empty($warning_message['witness1_img']) &&
                $witness1_staff_state &&
                $witness1_staff_state == HrStaffInfoModel::STATE_2
            ) {
                $return_data["witness1"] = true;
            }
            if (
                !empty($warning_message['witness2_kit_id']) &&
                !empty($warning_message['witness2_id']) &&
                empty($warning_message['witness2_img']) &&
                $witness2_staff_state &&
                $witness2_staff_state == HrStaffInfoModel::STATE_2
            ) {
                $return_data["witness2"] = true;
            }
        }

        return $return_data;
    }

    /**
     * th new 每天跑超过三天没有签字的 警告书 进行转交
     * @return array
     */
    public function warning_message_transmit($params)
    {
        $return_data = [];
        $db_coupon = $this->getDI()->get('db_coupon');
        $logger = $this->getDI()->get("logger");
        $setting_env                           = new SettingEnvServer();
        // 获取警告书签字天数限制-非警告系统
        $no_warning_system_limitation_days     = $setting_env->getSetVal('no_warning_system_limitation_days');
        $no_warning_system_limitation_days_arr = json_decode($no_warning_system_limitation_days, true);
        // 获取警告书签字天数限制-警告系统
        $warning_system_limitation_days        = $setting_env->getSetVal('warning_system_limitation_days');
        $warning_system_limitation_days_arr    = json_decode($warning_system_limitation_days, true);
        // 获取警告书-非警告系统 各节点是否限制打卡
        $no_warning_system_is_limitation_clock        = $setting_env->getSetVal('no_warning_system_is_limitation_clock');
        $no_warning_system_is_limitation_clock_arr    = json_decode($no_warning_system_is_limitation_clock, true);
        // 获取警告书-警告系统 各节点是否限制打卡
        $warning_system_is_limitation_clock        = $setting_env->getSetVal('warning_system_is_limitation_clock');
        $warning_system_is_limitation_clock_arr    = json_decode($warning_system_is_limitation_clock, true);

//        $max_day = max(array_merge(array_values($no_warning_system_limitation_days_arr),array_values($warning_system_limitation_days_arr)));
        // 获取 近30天 范围内 未签字的警告书
        [$begin, $end] = $this->get_gmdate(30, true);
        $where['start'] = $begin;
        $where['end']   = $end;
        $where['warning_id']= $params[0]??0;
        $data           = $this->get_untreated_warning_message($where);
        $logger->write_log(["未签字的警告书"=>$data], "info");
        if (empty($data)) {
            return [];
        }
        // 获取警告书转接记录
        $warning_message_ids = array_column($data, 'id');
        $superior_ids        = array_values(array_filter(array_column($data, 'superior_id')));
        $staff_ids           = array_values(array_filter(array_column($data, 'staff_info_id')));
        $witness1_ids           = array_values(array_filter(array_column($data, 'witness1_id')));
        $witness2_ids           = array_values(array_filter(array_column($data, 'witness2_id')));
        $staff_list          = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({staffs:array})',
            'bind'       => [
                'staffs' => array_merge($superior_ids, $staff_ids,$witness1_ids,$witness2_ids),
            ],
        ])->toArray();
        $staff_list          = array_column($staff_list, null, 'staff_info_id');
        $message_warning_transfer_sign = $this->get_message_warning_transfer_sign($warning_message_ids);
        foreach ($data as $k => $v) {
            $superior_staff_state = $v['superior_id'] ? $staff_list[$v['superior_id']]['state'] : 0;
            $witness1_staff_state = $v['witness1_id'] ? $staff_list[$v['witness1_id']]['state'] : 0;
            $witness2_staff_state = $v['witness2_id'] ? $staff_list[$v['witness2_id']]['state'] : 0;
            // 全节点的员工
            $all_node_staff_id = array_values(array_filter([
                $v['staff_info_id'] ?? '',
                $v['superior_id'] ?? '',
                $v['witness1_id'] ?? '',
                $v['witness2_id'] ?? '',
            ]));

            if (empty($v['staff_warning_message_id'])) {
                // 非警告系统发出「HCM-快捷工具-电子警告书-发送警告书」

                [$staff_begin, $staff_end] = $this->get_gmdate($no_warning_system_limitation_days_arr['staff']);
                [
                    $superior_begin,
                    $superior_end,
                ] = $this->get_gmdate($no_warning_system_limitation_days_arr['superior']);
                [$witness_begin, $witness_end] = $this->get_gmdate($no_warning_system_limitation_days_arr['witness']);

                // 获取配置 是否限制打卡天数
                $superior_related_id = 0;
                $witness_related_id = 0;
                if ($no_warning_system_is_limitation_clock_arr['superior'] == MessageEnums::IS_LIMITATION_CLOCK_YES){
                    $superior_related_id = strtotime(date("Y-m-d 00:00:00", strtotime("+".$no_warning_system_limitation_days_arr['superior']." day")));
                }
                if ($no_warning_system_is_limitation_clock_arr['witness'] == MessageEnums::IS_LIMITATION_CLOCK_YES){
                    $witness_related_id = strtotime(date("Y-m-d 00:00:00", strtotime("+".$no_warning_system_limitation_days_arr['witness']." day")));
                }

                // 获取上级和上上级 返回结果 key 1=上级 2=上上级
                $staff_level = $this->getStaffMangerLevel($v['staff_info_id'], 2);
                // 节点1 警告人没签 转节点2找上级或者上上级
                $c1 = $this->check_staff_untreated($v, $message_warning_transfer_sign, $staff_begin, $staff_end);
                $logger->write_log(["节点1 警告人没签 转节点2找上级或者上上级"=>[$v, $message_warning_transfer_sign, $staff_begin, $staff_end],'check1'=>$c1], "info");

                if ($c1) {

                    if ($staff_level[1]['state'] == HrStaffInfoModel::STATE_1 && $staff_level[1]['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE_NO) {
                        $superior_staff_id = $staff_level[1]['staff_info_id'];
                        if (!in_array($staff_level[1]['staff_info_id'], $all_node_staff_id)) {
                            $this->sendWarningMessage($superior_staff_id, $v['id'], MessageServer::MSG_STAFF_TYPE_STAFF,
                                1,0,$superior_related_id);
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 被警告人没签转节点2 转上级",
                                "info");
                            $return_data[] = $v['id'];
                        } else {
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 的上级是自己 不能转交",
                                "info");
                        }
                    } else {
                        // 获取上上级
                        $superior_staff_id = $staff_level[2]['staff_info_id'];
                        if (!in_array($staff_level[2]['staff_info_id'], $all_node_staff_id)) {
                            # todo 设置消息限制时间
                            $this->sendWarningMessage($superior_staff_id, $v['id'], MessageServer::MSG_STAFF_TYPE_STAFF,
                                2,0,$superior_related_id);
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 被警告人没签转节点2 转上上级",
                                "info");
                            $return_data[] = $v['id'];
                        } else {
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 的上上级是自己 不能转交",
                                "info");
                        }
                    }
                }
                // 节点2 上级没签且离职 转上上级
                $c2 = $this->check_superior_untreated($v, $message_warning_transfer_sign, $superior_begin,
                    $superior_end, $superior_staff_state);
                $logger->write_log(["节点2 上级没签且离职 转上上级"=>[$v, $message_warning_transfer_sign, $superior_begin,
                    $superior_end, $superior_staff_state],'check2'=>$c2], "info");

                if ($c2) {
                    // 转交給上上级
                    $superior_staff_id = $staff_level[2]['staff_info_id'] ?? '';
                    if (!in_array($superior_staff_id, $all_node_staff_id) && $superior_staff_id) {
                        # todo 设置消息限制时间
                        $this->sendWarningMessage($superior_staff_id, $v['id'], MessageServer::MSG_STAFF_TYPE_STAFF, 2,0,$superior_related_id);
                        // 设置转交前上级的签字消息为已读
                        $db_coupon->updateAsDict('message_courier', ['read_state' => 1], [
                            'conditions' => "id = ? and category = ? and read_state = 0",
                            'bind'       => [$v['superior_kit_id'], MessageEnums::MESSAGE_CATEGORY_CODE_EARLY],
                        ]);
                        $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 上级没签且离职 转上上级",
                            "info");
                        $return_data[] = $v['id'];
                    } else {
                        $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 的上上级是自己 不能转交",
                            "info");
                    }
                }
                // 节点3 证人没签
                $witness = $this->check_witness_untreated($v, $message_warning_transfer_sign, $witness_begin, $witness_end);
                $logger->write_log(["节点3 证人没签"=>[$v, $message_warning_transfer_sign, $witness_begin, $witness_end],'check3'=>$witness], "info");

                $witness_1_level = '';
                $witness_2_level = '';
                $witness_1 = '';
                $witness_2 = '';
                if ($witness['witness1'] || $witness['witness2']) {
                    $hrbps = (new WorkflowServer($this->lang,
                        $this->timeZone))->findHRBP($staff_list[$v['staff_info_id']]['node_department_id'],
                        ['store_id' => $staff_list[$v['staff_info_id']]['sys_store_id']]);
                    $hrbps = explode(',', $hrbps);
                    if ($hrbps) {
                        $staff_hrbp = HrStaffInfoModel::find([
                            'columns'    => 'staff_info_id,name',
                            'conditions' => 'state = :state: and wait_leave_state = :wait_leave_state: and formal in ({formal:array}) and is_sub_staff = :is_sub_staff: and staff_info_id in ({staff_info_id:array}) and staff_info_id not in ({no_staff_info_id:array})',
                            'bind'       => [
                                'no_staff_info_id'    => $all_node_staff_id,
                                'state'            => HrStaffInfoModel::STATE_1,
                                'wait_leave_state' => HrStaffInfoModel::WAITING_LEAVE_NO,
                                'is_sub_staff'     => HrStaffInfoModel::IS_SUB_STAFF_0,
                                'formal'           => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                                'staff_info_id'    => $hrbps,
                            ],
                        ])->toArray();
                        if (count($staff_hrbp) <= 1){
                            // 查找ER 取角色 80 的员工 并过滤掉当前警告书节点员工
                            $builder = $this->modelsManager->createBuilder();
                            $builder->columns('staff.staff_info_id,staff.name');
                            $builder->from(['staff' => HrStaffInfoModel::class]);
                            $builder->join(HrStaffInfoPositionModel::class,
                                'position.staff_info_id = staff.staff_info_id', 'position');
                            $builder->andWhere('position.position_category in ({position_category:array})', [
                                    'position_category' => [HrStaffInfoPositionModel::ER],
                                ]
                            );
                            $builder->andWhere('staff.state = :state: and staff.formal in ({formal:array}) and staff.wait_leave_state = :wait_leave_state: and staff.is_sub_staff = :is_sub_staff: and staff.staff_info_id not in ({no_staff_info_id:array})',
                                [
                                    'no_staff_info_id'    => $all_node_staff_id,
                                    'state'            => HrStaffInfoModel::STATE_1,
                                    'wait_leave_state' => HrStaffInfoModel::WAITING_LEAVE_NO,
                                    'is_sub_staff'     => HrStaffInfoModel::IS_SUB_STAFF_0,
                                    'formal'           => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                                ]
                            );
                            $staff_er = $builder->getQuery()->execute()->toArray();
                        }
                        if ($witness['witness1'] && $witness['witness2']){
                            if (count($staff_hrbp) >= 2){
                                $witness_1       = $staff_hrbp[0]['staff_info_id'] ?? '';
                                $witness_1_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_BP;
                                $witness_2       = $staff_hrbp[1]['staff_info_id'] ?? '';
                                $witness_2_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_BP;
                            }else{
                                if (count($staff_hrbp) == 1) {
                                    $witness_1       = $staff_hrbp[0]['staff_info_id'] ?? '';
                                    $witness_1_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_BP;
                                    $witness_2       = $staff_er[0]['staff_info_id'] ?? '';
                                    $witness_2_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER;
                                } else {
                                    $witness_1       = $staff_er[0]['staff_info_id'] ?? '';
                                    $witness_1_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER;
                                    $witness_2       = $staff_er[1]['staff_info_id'] ?? '';
                                    $witness_2_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER;
                                }
                            }
                        }elseif ($witness['witness1'] && !$witness['witness2']){
                            if (count($staff_hrbp) >= 1){
                                $witness_1       = $staff_hrbp[0]['staff_info_id'] ?? '';
                                $witness_1_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_BP;
                            }else{
                                $witness_1       = $staff_er[0]['staff_info_id'] ?? '';
                                $witness_1_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER;
                            }
                        }else{
                            if (count($staff_hrbp) >= 1){
                                $witness_2       = $staff_hrbp[0]['staff_info_id'] ?? '';
                                $witness_2_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_BP;
                            }else{
                                $witness_2       = $staff_er[0]['staff_info_id'] ?? '';
                                $witness_2_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER;
                            }
                        }
                    }
                }
                if ($witness['witness1'] && $witness_1 && $witness_1_level) {
                    // 证人1 转交
                    # todo 设置转交前证人消息为已读
                    # todo 设置消息限制时间
                    $this->sendWarningMessage($witness_1, $v['id'], MessageServer::MSG_STAFF_TYPE_SUPERIOR,
                        $witness_1_level, 1,$witness_related_id);
                    // 设置转交前证人的签字消息为已读
                    $db_coupon->updateAsDict('message_courier', ['read_state' => 1], [
                        'conditions' => "id = ? and category = ? and read_state = 0",
                        'bind'       => [$v['witness1_kit_id'], MessageEnums::MESSAGE_CATEGORY_CODE_EARLY],
                    ]);
                    $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 证人1 转交",
                        "info");
                    $return_data[] = $v['id'];
                }
                if ($witness['witness2'] && $witness_2 && $witness_2_level) {
                    // 证人2 转交
                    # todo 设置转交前证人消息为已读
                    # todo 设置消息限制时间
                    $this->sendWarningMessage($witness_2, $v['id'], MessageServer::MSG_STAFF_TYPE_SUPERIOR,
                        $witness_2_level, 2,$witness_related_id);
                    // 设置转交前证人的签字消息为已读
                    $db_coupon->updateAsDict('message_courier', ['read_state' => 1], [
                        'conditions' => "id = ? and category = ? and read_state = 0",
                        'bind'       => [$v['witness2_kit_id'], MessageEnums::MESSAGE_CATEGORY_CODE_EARLY],
                    ]);
                    $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 证人2 转交",
                        "info");
                    $return_data[] = $v['id'];
                }
            } else {


                // 警告系统发出「HCM-警告系统-违规行为复核-发送警告书」
                [$staff_begin, $staff_end] = $this->get_gmdate($warning_system_limitation_days_arr['staff']);
                [$superior_begin, $superior_end] = $this->get_gmdate($warning_system_limitation_days_arr['superior']);
                [$witness_begin, $witness_end] = $this->get_gmdate($warning_system_limitation_days_arr['witness']);

                // 获取配置 是否限制打卡天数
                $superior_related_id = 0;
                $witness_related_id = 0;
                if ($warning_system_is_limitation_clock_arr['superior'] == MessageEnums::IS_LIMITATION_CLOCK_YES){
                    $superior_related_id = strtotime(date("Y-m-d 00:00:00", strtotime("+".$warning_system_limitation_days_arr['superior']." day")));
                }
                if ($warning_system_is_limitation_clock_arr['witness'] == MessageEnums::IS_LIMITATION_CLOCK_YES){
                    $witness_related_id = strtotime(date("Y-m-d 00:00:00", strtotime("+".$warning_system_limitation_days_arr['witness']." day")));
                }

                // 获取上级和上上级 返回结果 key 1=上级 2=上上级
                $staff_level = $this->getStaffMangerLevel($v['staff_info_id'], 2);
                $cn1 = $this->check_staff_untreated($v, $message_warning_transfer_sign, $staff_begin, $staff_end);
                $logger->write_log(["获取上级和上上级 返回结果 key 1=上级 2=上上级"=>[$v,$v, $message_warning_transfer_sign, $staff_begin, $staff_end],'cn1'=>$cn1], "info");

                // 节点1 警告人没签 转节点2 找上级或者上上级
                if ($cn1) {
                    if (isset($staff_level[1]) && $staff_level[1]['state'] == HrStaffInfoModel::STATE_1 && $staff_level[1]['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE_NO) {
                        $superior_staff_id = $staff_level[1]['staff_info_id'];
                        # todo 设置消息限制时间
                        if (!in_array($superior_staff_id, $all_node_staff_id)) {
                            $this->sendWarningMessage($superior_staff_id, $v['id'], MessageServer::MSG_STAFF_TYPE_STAFF,
                                1,0,$superior_related_id);
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 被警告人没签转节点2 转上级",
                                "info");
                            $return_data[] = $v['id'];
                        } else {
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 的上级是自己 不能转交",
                                "info");
                        }
                    } else {
                        // 获取上上级
                        $superior_staff_id = $staff_level[2]['staff_info_id'] ?? '';
                        if ($superior_staff_id && !in_array($superior_staff_id, $all_node_staff_id)) {
                            # todo 设置消息限制时间
                            $this->sendWarningMessage($superior_staff_id, $v['id'], MessageServer::MSG_STAFF_TYPE_STAFF,
                                2,0,$superior_related_id);
                            // 设置转交前上级的签字消息为已读
                            $db_coupon->updateAsDict('message_courier', ['read_state' => 1], [
                                'conditions' => "id = ? and category = ? and read_state = 0",
                                'bind'       => [$v['superior_kit_id'], MessageEnums::MESSAGE_CATEGORY_CODE_EARLY],
                            ]);
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 被警告人没签转节点2 转上上级",
                                "info");
                            $return_data[] = $v['id'];
                        } else {
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 的上上级是自己 不能转交",
                                "info");
                        }
                    }
                }
                // 节点2 上级没签且离职 转上上级
                $cn2 = $this->check_superior_untreated($v, $message_warning_transfer_sign, $superior_begin,
                    $superior_end, $superior_staff_state);
                $logger->write_log(["节点2 上级没签且离职 转上上级"=>[$v, $message_warning_transfer_sign, $superior_begin,
                    $superior_end, $superior_staff_state],'cn2'=>$cn2], "info");

                if ($cn2) {
                    // 转交給上上级
                    $superior_staff_id = $staff_level[2]['staff_info_id'] ?? '';
                    if (!in_array($superior_staff_id, $all_node_staff_id) && $superior_staff_id) {
                        # todo 设置消息限制时间
                        $this->sendWarningMessage($superior_staff_id, $v['id'], MessageServer::MSG_STAFF_TYPE_STAFF, 2,0,$superior_related_id);
                        // 设置转交前上级的签字消息为已读
                        $db_coupon->updateAsDict('message_courier', ['read_state' => 1], [
                            'conditions' => "id = ? and category = ? and read_state = 0",
                            'bind'       => [$v['superior_kit_id'], MessageEnums::MESSAGE_CATEGORY_CODE_EARLY],
                        ]);
                        $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 上级没签且离职 转上上级",
                            "info");
                        $return_data[] = $v['id'];
                    } else {
                        $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 的上上级是自己 不能转交",
                            "info");
                    }
                }
                // 节点3 证人没签
                $witness = $this->check_witness_untreated($v, $message_warning_transfer_sign, $witness_begin,
                    $witness_end, $witness1_staff_state, $witness2_staff_state);

                $logger->write_log(["节点3 证人没签"=>[$v, $message_warning_transfer_sign, $witness_begin,
                    $witness_end, $witness1_staff_state, $witness2_staff_state],'cn3'=>$witness], "info");
                if ($witness['witness1'] || $witness['witness2']) {
                    // 找配置的默认证人
                    $sign_warning_witness_list_default     = $setting_env->getSetVal('sign_warning_witness_list_default');
                    $sign_warning_witness_list_default_arr = array_values(array_filter(explode(',',
                        $sign_warning_witness_list_default)));
                    if ($sign_warning_witness_list_default_arr) {
                        $witness_list_default = HrStaffInfoModel::find([
                            'columns'    => 'staff_info_id,name',
                            'conditions' => 'staff_info_id in ({staffs:array}) and state = :state: and wait_leave_state = :wait_leave_state: and formal in ({formal:array}) and is_sub_staff = :is_sub_staff: and staff_info_id not in ({no_staff_info_id:array})',
                            'bind'       => [
                                'no_staff_info_id'    => $all_node_staff_id,
                                'staffs'           => $sign_warning_witness_list_default_arr,
                                'state'            => HrStaffInfoModel::STATE_1,
                                'wait_leave_state' => HrStaffInfoModel::WAITING_LEAVE_NO,
                                'is_sub_staff'     => HrStaffInfoModel::IS_SUB_STAFF_0,
                                'formal'           => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                            ],
                        ])->toArray();
                        $witness_1 = '';
                        $witness_2 = '';
                        if ($witness['witness1'] && $witness['witness2']){
                            $witness_1 = $witness_list_default[0]['staff_info_id'] ?? '';
                            $witness_1_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER;
                            $witness_2 = $witness_list_default[1]['staff_info_id'] ?? '';
                            $witness_2_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER;
                        }elseif ($witness['witness1'] && !$witness['witness2']){
                            $witness_1 = $witness_list_default[0]['staff_info_id'] ?? '';
                            $witness_1_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER;
                        }else{
                            $witness_2 = $witness_list_default[0]['staff_info_id'] ?? '';
                            $witness_2_level = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER;
                        }

                        if ($witness['witness1'] && $witness_1) {
                            // 证人1 转交
                            $this->sendWarningMessage($witness_1, $v['id'], MessageServer::MSG_STAFF_TYPE_SUPERIOR,
                                $witness_1_level, 1,$witness_related_id);

                            // 设置转交前证人的签字消息为已读
                            $db_coupon->updateAsDict('message_courier', ['read_state' => 1], [
                                'conditions' => "id = ? and category = ? and read_state = 0",
                                'bind'       => [$v['witness1_kit_id'], MessageEnums::MESSAGE_CATEGORY_CODE_EARLY],
                            ]);
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 证人1 转交",
                                "info");
                            $return_data[] = $v['id'];
                        }
                        if ($witness['witness2'] && $witness_2) {
                            // 证人2 转交
                            $this->sendWarningMessage($witness_2, $v['id'], MessageServer::MSG_STAFF_TYPE_SUPERIOR,
                                $witness_2_level, 2,$witness_related_id);
                            // 设置转交前证人的签字消息为已读
                            $db_coupon->updateAsDict('message_courier', ['read_state' => 1], [
                                'conditions' => "id = ? and category = ? and read_state = 0",
                                'bind'       => [$v['witness2_kit_id'], MessageEnums::MESSAGE_CATEGORY_CODE_EARLY],
                            ]);
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 证人1 转交",
                                "info");
                            $return_data[] = $v['id'];
                        }
                    }
                }
            }
        }
        return $return_data;
    }

    /**
     * 一次性 这是修复数据用的方法
     * @param $params
     * @return array
     */
    public function warning_message_transmit_fix_data($params)
    {
        $return_data = [];
        $db_coupon = $this->getDI()->get('db_coupon');
        $logger = $this->getDI()->get("logger");
        $setting_env                           = new SettingEnvServer();
        // 获取警告书签字天数限制-非警告系统
        $no_warning_system_limitation_days     = $setting_env->getSetVal('no_warning_system_limitation_days');
        $no_warning_system_limitation_days_arr = json_decode($no_warning_system_limitation_days, true);
        // 获取警告书签字天数限制-警告系统
        $warning_system_limitation_days        = $setting_env->getSetVal('warning_system_limitation_days');
        $warning_system_limitation_days_arr    = json_decode($warning_system_limitation_days, true);
        // 获取警告书-非警告系统 各节点是否限制打卡
        $no_warning_system_is_limitation_clock        = $setting_env->getSetVal('no_warning_system_is_limitation_clock');
        $no_warning_system_is_limitation_clock_arr    = json_decode($no_warning_system_is_limitation_clock, true);
        // 获取警告书-警告系统 各节点是否限制打卡
        $warning_system_is_limitation_clock        = $setting_env->getSetVal('warning_system_is_limitation_clock');
        $warning_system_is_limitation_clock_arr    = json_decode($warning_system_is_limitation_clock, true);

//        $max_day = max(array_merge(array_values($no_warning_system_limitation_days_arr),array_values($warning_system_limitation_days_arr)));
        // 获取 近30天 范围内 未签字的警告书 -- 2024-01-26 上线的
        [$begin, $end] = $this->get_gmdate(30, true);
        $where['start'] = '2024-01-26 00:00:00';
        $where['end']   = '2024-03-25 23:59:59';
        $where['warning_id']= $params[0]??0;
        $data           = $this->get_untreated_warning_message($where);
        $logger->write_log(["未签字的警告书"=>$data], "info");
        if (empty($data)) {
            return [];
        }
        // 获取警告书转接记录
        $warning_message_ids = array_column($data, 'id');
        $superior_ids        = array_values(array_filter(array_column($data, 'superior_id')));
        $staff_ids           = array_values(array_filter(array_column($data, 'staff_info_id')));
        $witness1_ids           = array_values(array_filter(array_column($data, 'witness1_id')));
        $witness2_ids           = array_values(array_filter(array_column($data, 'witness2_id')));
        $staff_list          = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({staffs:array})',
            'bind'       => [
                'staffs' => array_merge($superior_ids, $staff_ids,$witness1_ids,$witness2_ids),
            ],
        ])->toArray();
        $staff_list          = array_column($staff_list, null, 'staff_info_id');
        $message_warning_transfer_sign = $this->get_message_warning_transfer_sign($warning_message_ids);
        foreach ($data as $k => $v) {
            $superior_staff_state = $v['superior_id'] ? $staff_list[$v['superior_id']]['state'] : 0;
            $witness1_staff_state = $v['witness1_id'] ? $staff_list[$v['witness1_id']]['state'] : 0;
            $witness2_staff_state = $v['witness2_id'] ? $staff_list[$v['witness2_id']]['state'] : 0;
            // 全节点的员工
            $all_node_staff_id = array_values(array_filter([
                $v['staff_info_id'] ?? '',
                $v['superior_id'] ?? '',
                $v['witness1_id'] ?? '',
                $v['witness2_id'] ?? '',
            ]));

            if (empty($v['staff_warning_message_id'])) {
                // 非警告系统发出「HCM-快捷工具-电子警告书-发送警告书」

                [$staff_begin, $staff_end] = $this->get_gmdate($no_warning_system_limitation_days_arr['staff']);
                $staff_begin = '2024-01-26 00:00:00';
                $staff_end   = '2024-03-25 23:59:59';
                [
                    $superior_begin,
                    $superior_end,
                ] = $this->get_gmdate($no_warning_system_limitation_days_arr['superior']);
                [$witness_begin, $witness_end] = $this->get_gmdate($no_warning_system_limitation_days_arr['witness']);

                // 获取配置 是否限制打卡天数
                $superior_related_id = 0;
                $witness_related_id = 0;
                if ($no_warning_system_is_limitation_clock_arr['superior'] == MessageEnums::IS_LIMITATION_CLOCK_YES){
                    $superior_related_id = strtotime(date("Y-m-d 00:00:00", strtotime("+".$no_warning_system_limitation_days_arr['superior']." day")));
                }
                if ($no_warning_system_is_limitation_clock_arr['witness'] == MessageEnums::IS_LIMITATION_CLOCK_YES){
                    $witness_related_id = strtotime(date("Y-m-d 00:00:00", strtotime("+".$no_warning_system_limitation_days_arr['witness']." day")));
                }

                // 获取上级和上上级 返回结果 key 1=上级 2=上上级
                $staff_level = $this->getStaffMangerLevel($v['staff_info_id'], 2);
                // 节点1 警告人没签 转节点2找上级或者上上级
                $c1 = $this->check_staff_untreated($v, $message_warning_transfer_sign, $staff_begin, $staff_end);
                $logger->write_log(["节点1 警告人没签 转节点2找上级或者上上级"=>[$staff_level,$v, $message_warning_transfer_sign, $staff_begin, $staff_end],'check1'=>$c1], "info");

                if ($c1) {

                    if ($staff_level[1]['state'] == HrStaffInfoModel::STATE_1 && $staff_level[1]['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE_NO) {
                        $superior_staff_id = $staff_level[1]['staff_info_id'];
                        if (!in_array($staff_level[1]['staff_info_id'], $all_node_staff_id)) {
                            $this->sendWarningMessage($superior_staff_id, $v['id'], MessageServer::MSG_STAFF_TYPE_STAFF,
                                1,0,$superior_related_id);
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 被警告人没签转节点2 转上级",
                                "info");
                            $return_data[] = $v['id'];
                        } else {
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 的上级是自己 不能转交",
                                "info");
                        }
                    } else {
                        // 获取上上级
                        $superior_staff_id = $staff_level[2]['staff_info_id'];
                        if (!in_array($staff_level[2]['staff_info_id'], $all_node_staff_id)) {
                            # todo 设置消息限制时间
                            $this->sendWarningMessage($superior_staff_id, $v['id'], MessageServer::MSG_STAFF_TYPE_STAFF,
                                2,0,$superior_related_id);
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 被警告人没签转节点2 转上上级",
                                "info");
                            $return_data[] = $v['id'];
                        } else {
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 的上上级是自己 不能转交",
                                "info");
                        }
                    }
                }
            } else {


                // 警告系统发出「HCM-警告系统-违规行为复核-发送警告书」
                [$staff_begin, $staff_end] = $this->get_gmdate($warning_system_limitation_days_arr['staff']);
                $staff_begin = '2024-01-26 00:00:00';
                $staff_end   = '2024-03-25 23:59:59';
                [$superior_begin, $superior_end] = $this->get_gmdate($warning_system_limitation_days_arr['superior']);
                [$witness_begin, $witness_end] = $this->get_gmdate($warning_system_limitation_days_arr['witness']);

                // 获取配置 是否限制打卡天数
                $superior_related_id = 0;
                $witness_related_id = 0;
                if ($warning_system_is_limitation_clock_arr['superior'] == MessageEnums::IS_LIMITATION_CLOCK_YES){
                    $superior_related_id = strtotime(date("Y-m-d 00:00:00", strtotime("+".$warning_system_limitation_days_arr['superior']." day")));
                }
                if ($warning_system_is_limitation_clock_arr['witness'] == MessageEnums::IS_LIMITATION_CLOCK_YES){
                    $witness_related_id = strtotime(date("Y-m-d 00:00:00", strtotime("+".$warning_system_limitation_days_arr['witness']." day")));
                }

                // 获取上级和上上级 返回结果 key 1=上级 2=上上级
                $staff_level = $this->getStaffMangerLevel($v['staff_info_id'], 2);
                $cn1 = $this->check_staff_untreated($v, $message_warning_transfer_sign, $staff_begin, $staff_end);
                $logger->write_log(["获取上级和上上级 返回结果 key 1=上级 2=上上级"=>[$v,$v, $message_warning_transfer_sign, $staff_begin, $staff_end],'cn1'=>$cn1], "info");

                // 节点1 警告人没签 转节点2 找上级或者上上级
                if ($cn1) {
                    if (isset($staff_level[1]) && $staff_level[1]['state'] == HrStaffInfoModel::STATE_1 && $staff_level[1]['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE_NO) {
                        $superior_staff_id = $staff_level[1]['staff_info_id'];
                        # todo 设置消息限制时间
                        if (!in_array($superior_staff_id, $all_node_staff_id)) {
                            $this->sendWarningMessage($superior_staff_id, $v['id'], MessageServer::MSG_STAFF_TYPE_STAFF,
                                1,0,$superior_related_id);
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 被警告人没签转节点2 转上级",
                                "info");
                            $return_data[] = $v['id'];
                        } else {
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 的上级是自己 不能转交",
                                "info");
                        }
                    } else {
                        // 获取上上级
                        $superior_staff_id = $staff_level[2]['staff_info_id'] ?? '';
                        if ($superior_staff_id && !in_array($superior_staff_id, $all_node_staff_id)) {
                            # todo 设置消息限制时间
                            $this->sendWarningMessage($superior_staff_id, $v['id'], MessageServer::MSG_STAFF_TYPE_STAFF,
                                2,0,$superior_related_id);
                            // 设置转交前上级的签字消息为已读
                            $db_coupon->updateAsDict('message_courier', ['read_state' => 1], [
                                'conditions' => "id = ? and category = ? and read_state = 0",
                                'bind'       => [$v['superior_kit_id'], MessageEnums::MESSAGE_CATEGORY_CODE_EARLY],
                            ]);
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 被警告人没签转节点2 转上上级",
                                "info");
                            $return_data[] = $v['id'];
                        } else {
                            $logger->write_log("警告书id ：" . $v['id'] . " , 被警告人 ： " . $v['staff_info_id'] . " 的上上级是自己 不能转交",
                                "info");
                        }
                    }
                }
            }
        }
        return $return_data;
    }


    /**
     * 获取全部未签字的警告书
     * @return mixed
     */
    public function get_untreated_warning_message($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, staff_info_id, kit_id, img_url, superior_id, superior_kit_id, superior_img, witness1_id, witness1_kit_id, witness1_img, witness2_id, witness2_kit_id, witness2_img,staff_warning_message_id, created_at');
        $builder->from(MessageWarningModel::class);
        $builder->where("warning_type in ({warning_types:array})",
            ['warning_types' => array_keys(MessageWarningModel::$warning_types)]);
        $builder->andWhere('is_delete = :is_delete:', ['is_delete' => MessageWarningModel::DELETE_NO]);
        if (!empty($params['start'])) {
            $builder->andWhere("created_at >= :start: ", ['start' => $params['start']]);
        }
        if (!empty($params['end'])) {
            $builder->andWhere("created_at <= :end:", ['end' => $params['end']]);
        }
        if (!empty($params['warning_id'])) {
            $builder->andWhere("id = :warning_id:", ['warning_id' => $params['warning_id']]);
        }
        $builder->andWhere("
        (kit_id != '' and img_url = '' and superior_id = 0) or 
        (superior_kit_id != '' and superior_img = '' and witness1_id = 0) or 
        (witness1_kit_id != '' and witness1_img = '' and witness1_id != 0) or 
        (witness2_kit_id != '' and witness2_img = '' and witness2_id != 0)");
//        $builder->andWhere("id = 7833");
//        var_dump($builder->getQuery()->getSql());exit;
        $data = $builder->getQuery()->execute()->toArray();
        return $data;
    }

    /**
     * 获取警告书转交记录
     * @param $ids
     * @return mixed
     */
    public function get_message_warning_transfer_sign($ids = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(MessageWarningTransferSignModel::class);
        $builder->where("message_warning_id in ({ids:array}) and type !=3", ['ids' => $ids]);
        $builder->columns('id, message_warning_id, kit_id, staff_info_id, level, type, created_at');
        return $builder->getQuery()->execute()->toArray();
    }


    //发送消息
    public function sendMessage($staffId, $title, $content, $extend = [])
    {
        $category     = empty($extend['category']) ? -1 : $extend['category'];
        $lang         = empty($extend['lang']) ? $this->lang : $extend['lang'];
        $msgId        = time().$staffId.rand(1000000, 9999999);
        $send_message = [
            'staff_users'        => [$staffId],
            'message_title'      => $title,
            'message_content'    => $content,
            'staff_info_ids_str' => $staffId,
            'category'           => $category,
            'push_state'         => 1,
            'id'                 => $msgId,
        ];
        $hcm_rpc      = new ApiClient('hcm_rpc', '', 'add_kit_message', $lang);
        $hcm_rpc->setParams($send_message);
        $res = $hcm_rpc->execute();
        if (!isset($res['result']['code']) || $res['result']['code'] != ErrCode::SUCCESS) {
            $this->getDI()->get('logger')->write_log([
                'function' => 'leave sendMessage',
                'message'  => '消息发送失败',
                'params'   => $send_message,
                'result'   => $res,
            ]);
        }
        return $msgId;
    }

    /**
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function getMessageMsg($param): array
    {
        $userId     = $param['user_id'] ?? 0;
        $msgId      = $param['msg_id'] ?? '';
        if (empty($msgId)){
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $server = new BackyardServer($this->lang, $this->timezone);
        // 验证该员工是否收到签字消息
        $sign_msg = $server->get_staff_sign_msg([
            'msg_id'=>$msgId,
            'staff_info_id'=>$userId,
        ]);
        if (empty($sign_msg) || $sign_msg['staff_info_id'] != $userId) {
            throw new ValidationException('员工没有收到该签字消息');
        }
        $messagePdf = MessagePdfModel::findFirst([
            'conditions' => "msg_id = :msg_id:",
            'bind'       => ['msg_id' => $msgId],
        ]);
        if (empty($messagePdf)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        $messagePdf = $messagePdf->toArray();
        $return_data['msg_id'] = $msgId;
        $return_data['state'] = $messagePdf['state'] == 1 ? 1 : 0;
        $return_data['pdf_url'] = $messagePdf['pdf_url'] ?? '';
        if (!empty($messagePdf['sign_url'])){
            $return_data['pdf_url'] = $messagePdf['sign_url'];
        }
        return $return_data;
    }
    
    /**
     * @param $business_id
     * @param $msg_id
     * @param $staff_info_id
     * @param $data
     * @param $pdf_url
     * @param $module_category
     * @return bool
     */
    public function updateMessagePdf($business_id,$msg_id,$staff_info_id,$data,$pdf_url,$module_category)
    {
        $messagePdf = MessagePdfModel::findFirst(
            [
                'conditions' => 'msg_id = :message_id:',
                'bind'       => [
                    'message_id' => $msg_id,
                ],
            ]
        );
        if (empty($messagePdf)){
            $messagePdf = new MessagePdfModel();
        }
        $messagePdf->pdf_url = $pdf_url;
        $messagePdf->staff_info_id = $staff_info_id;
        $messagePdf->module_category = $module_category;
        $messagePdf->msg_id = $msg_id;
        $messagePdf->business_id = $business_id;
        $messagePdf->form_data_json = json_encode($data, JSON_UNESCAPED_UNICODE);
        return $messagePdf->save();
    }


}