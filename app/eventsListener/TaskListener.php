<?php

namespace FlashExpress\bi\App\EventsListener;

use BaseTask;
use Phalcon\CLI\Console;
use Phalcon\CLI\Dispatcher;
use Phalcon\Events\Event;


/**
 * Class TaskListener
 */
class TaskListener
{
    /**
     * Event fired before task is handling
     *
     * @param $argv
     * @return callable
     */
    public function beforeHandleTask($argv)
    {
        return function (Event $event, Console $console, Dispatcher $dispatcher) use ($argv) {
            if (class_exists($dispatcher->getHandlerClass())) {
                $class = $dispatcher->getHandlerClass();
            } else {
                $class = str_replace('FlashExpress\bi\App\Modules\\' . ucfirst(strtolower(env('country_code'))) . '\Tasks', '',
                    $dispatcher->getHandlerClass());
            }
            $annotations = $console->annotations->getMethod(
                $class,
                $dispatcher->getActiveMethod()
            );
            if ($annotations->has('Single')) {
                BaseTask::checkTaskIsFinish(date('Y-m-d'),
                    BaseTask::getTaskCode($dispatcher->getTaskName(), $dispatcher->getActionName()));
            }
        };
    }

    /**
     * Event fired after task handle
     *
     * @return callable
     */
    public function afterHandleTask($argv)
    {
        return function (Event $event, Console $console) use ($argv) {
            $dispatcher = $console->dispatcher;
            if (class_exists($dispatcher->getHandlerClass())) {
                $class = $dispatcher->getHandlerClass();
            } else {
                $class = str_replace('FlashExpress\bi\App\Modules\\' . ucfirst(strtolower(env('country_code'))) . '\Tasks', '',
                    $dispatcher->getHandlerClass());
            }

            $annotations = $console->annotations->getMethod(
                $class,
                $dispatcher->getActiveMethod()
            );
            if ($annotations->has('Single')) {
                BaseTask::createTaskLog(date('Y-m-d'),
                    BaseTask::getTaskCode($dispatcher->getTaskName(), $dispatcher->getActionName()));
            }
        };
    }
}
