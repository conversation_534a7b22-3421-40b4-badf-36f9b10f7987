<?php

namespace App\Country;


class Tools {

    /**
     *
     * 国家模块迁移
     *
     * 重新根据国家码获取实例对象
     *
     * @param $class
     * @param array $args
     * @return false|mixed
     * @throws \ReflectionException
     */
    public static function reBuildCountryInstance($class, $args = [])
    {
        if (is_object($class)) {
            $reflect = new \ReflectionClass($class);
            if (
                strpos($reflect->getName(), ucfirst(strtolower(env('country_code', 'TH')))) === false
            ) {
                $newClass =  str_replace("FlashExpress\\bi\\App\\", "FlashExpress\\bi\\App\\Modules\\" . ucfirst(strtolower(env('country_code', 'TH'))) . "\\", $reflect->getNamespaceName() ) . "\\" . $reflect->getShortName();
                if (class_exists($newClass)) {

                    $newReflection = new \ReflectionClass($newClass);
                    if ($newReflection->inNamespace() && $newReflection->isInstantiable()) {

                        return $newReflection->newInstanceArgs($args);
                    }
                }
            }

            return $class;
        } else {
            throw new \Exception('不是一个实例化对象');
        }
    }
}
