<?php

/**
 * Phalcon控制器扩展
 *
 */
namespace FlashExpress\bi\App\Core;

use FlashExpress\bi\App\Models\OperateLog;
use FlashExpress\bi\App\Traits\FactoryTrait;

class PhalBaseController extends \Phalcon\Mvc\Controller
{

    use FactoryTrait;
    public static $DI;

    protected $paramIn = null;
    public $timezone ;

    public function initialize(){

        self::$DI = $this->getDI();
    }

    /**
     * 处理跨域、入参、时区
     * @return void
     */
    protected function initLogic()
    {
        $this->cross();
        /* 解决跨域问题 */
        if ($this->request->getMethod() == 'OPTIONS') {
            $this->jsonReturn($this->checkReturn(1));
        }
        header('traceid:' .  molten_get_traceid());
        $this->getDI()->get('logger')->write_log(['requestHeader' => $this->request->getHeaders()], 'info');
        //时区配置
        $this->timezone = $this->getDI()['config']['application']['timeZone'];
        if (strpos(strtolower($this->request->getContentType()),'application/json') !== false){
            $this->paramIn = $this->request->getJsonRawBody(true);
        }else{
            $this->paramIn = $this->request->get();
        }
        $this->paramIn = filter_param($this->paramIn);
    }


    public function cross()
    {
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods: POST, GET");
        header("Access-Control-Allow-Headers: *");
        header("Access-Control-Max-Age: 20");
        header("Pragma:no-cache");
        header("Cache-Control:no-cache,must-revalidate");
    }
    /**
     * JSON返回
     * @param $paramIn array | $jsonCallback 处理跨域
     * @param bool $jsonCallback
     */
    public function jsonReturn($paramIn, $jsonCallback = false)
    {
        if (is_array($paramIn) && !isset($paramIn['tid'])) {
            $paramIn['tid'] = molten_get_traceid();
        }

        header('Content-type: application/json;charset=utf-8');
        $json_data = json_encode($paramIn, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        if ($jsonCallback) {
            echo $jsonCallback . "(" . $json_data . ")";
        } else {
            echo $json_data;
        }
        exit();
    }

    /**
     *
     * 校验返回 ToDo 这里不要改动
     * @param $paramIn
     * @param $parameter
     * @return array
     */
    public function checkReturn($paramIn, $parameter = null)
    {
        if (is_array($paramIn)) {
            //[1] 数组时返回
            $code = $paramIn['code'] ?? 1;
            $msg  = isset($paramIn['msg']) && !empty($paramIn['msg']) ? $paramIn['msg'] : 'success';
            $data = isset($paramIn['data']) && !empty($paramIn['data']) ? $paramIn['data'] : null;
        } elseif ($paramIn == -1) {
            //[2] 参数不正确时返回
            $code = 0;
            $msg  = '参数' . $parameter . '不能为空!';
        } elseif ($paramIn == -2) {
            //[2] 参数不正确时返回
            $code = 0;
            $msg  = '参数' . $parameter . '错误,json格式不正确!';
        } elseif ($paramIn == -3) {
            //[3] 参数不正确时返回
            $code = 0;
            $msg  = $parameter;
        } elseif ($paramIn == 1) {
            //[4] 请求成功时返回
            $code = 1;
            $msg  = 'success';
        }
        return [
            'code'    => $code ?? 0,
            'msg'     => $msg ?? '',
            'message' => $msg ?? '',
            'data'    => $data ?? null,
            'tid'     => molten_get_traceid(),
        ];
    }


    protected $locale;



    protected function returnJson($code, $message, $data=null,$statusCode= 200)
    {
        $result = [
            'code' => $code,
            'message' => $message,
            'data'=> $data,
        ];
        $this->response->setStatusCode($statusCode);
        $this->response->setJsonContent($result);
        return $this->response;
    }
    
    //给 原生客户端提供的 字段名师 message 并且 data 默认是null  数组客户端解析不了
    protected function ajax_fle_return($message, $code = 1, $data=array(),$statusCode= 200){

        $result = array(
            'code' => $code,
            'message' => $message,
            'data' => $data,
        );
        $this->response->setStatusCode($statusCode);
        $this->response->setJsonContent($result);
        return $this->response;
    }




}
