<?php


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Server\FullAttendanceRewardServer;
use FlashExpress\bi\App\Server\ResignServer;
use FlashExpress\bi\App\Server\WorkflowServer;

class AaTask extends BaseTask
{

    public function t222Action()
    {
        $model1 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
        $model2 = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");
//        $model1->set_val = 1;
//        $model2->set_val = 1;
        $model2->set_val = '2';
        $model2->save();
        echo  $model2->set_val .PHP_EOL;  // 第23行：现在会输出 2

        // 方案1：强制更新 - 使用writeAttribute强制标记字段为已修改
        $model1->set_val = '1';
        $model1->writeAttribute('set_val', '1'); // 强制标记字段为已修改
        $result1 = $model1->save();
        echo "方案1结果: " . ($result1 ? '成功' : '失败') . PHP_EOL;

    }

    /**
     * 演示其他解决方案
     */
    public function t223Action()
    {
        $model = SettingEnvModel::findFirst("code = 'ability_commission_front_line_job_title'");

        // 方案2：使用update方法直接更新
        $result2 = $model->update(['set_val' => '1']);
        echo "方案2结果: " . ($result2 ? '成功' : '失败') . PHP_EOL;

        // 方案3：使用原生SQL更新
        $db = $this->getDI()->get('db');
        $result3 = $db->execute(
            "UPDATE setting_env SET set_val = :set_val WHERE code = :code",
            [
                'set_val' => '1',
                'code' => 'ability_commission_front_line_job_title'
            ]
        );
        echo "方案3结果: " . ($result3 ? '成功' : '失败') . PHP_EOL;

        // 方案4：先设置为不同值，再设置为目标值
        $model->set_val = '999'; // 先设置为不同值
        $model->save();
        $model->set_val = '1';   // 再设置为目标值
        $result4 = $model->save();
        echo "方案4结果: " . ($result4 ? '成功' : '失败') . PHP_EOL;

        // 方案5：使用assign方法
        $model->assign(['set_val' => '1'], null, ['set_val']);
        $result5 = $model->save();
        echo "方案5结果: " . ($result5 ? '成功' : '失败') . PHP_EOL;

        // 方案6：使用新的工具函数（推荐）
        $result6 = forceUpdateModelField($model, 'set_val', '1');
        echo "方案6结果: " . ($result6 ? '成功' : '失败') . PHP_EOL;

        // 方案7：批量更新多个字段
        $result7 = forceUpdateModelFields($model, [
            'set_val' => '1',
            // 'other_field' => 'other_value' // 可以同时更新多个字段
        ]);
        echo "方案7结果: " . ($result7 ? '成功' : '失败') . PHP_EOL;
    }

    public function ppAction()
    {
        $s = new \FlashExpress\bi\App\Server\ProbationServer($this->lang, $this->timezone);
        [$lang, $hrbpId, $staffIds] = ['zh',122007,[56780,17245]];
        $r = $s->sendPushMessageToHrbp($lang, $hrbpId, $staffIds);
        dd($r);
    }

    public function ffAction()
    {
        $server = new ResignServer('zh-CN',$this->timezone);
        $s = $server->getStaffLeaveInfo(['staff_info_id'=>22600]);
        dd($s);
    }
    public function t11Action()
    {
        $month = '2025-05';
        $server = new FullAttendanceRewardServer($this->lang, $this->timezone);
        $data   = $server->hubFullAttendanceData(80236,$month);
        dd($data);
    }

    public function t21Action()
    {
        $code1 = ['ability_commission_front_line_job_title','2','3'];
        $code2 = ['ability_commission_front_line_job_title','3'];
        $code  = array_intersect($code1,$code2);
        var_dump($code);
        $builder = $this->modelsManager->createBuilder();
        $builder->from(\FlashExpress\bi\App\Models\backyard\SettingEnvModel::class);
        $builder->inWhere('code', $code);
        $model = $builder->getQuery()->execute()->toArray();
        dd($model);

    }

    public function t22Action()
    {
        $code1 = ['ability_commission_front_line_job_title','2','3'];
        $code2 = ['ability_commission_front_line_job_title','3'];
        $code = array_intersect($code1,$code2);
        $model = \FlashExpress\bi\App\Models\backyard\SettingEnvModel::findFirst([
            'conditions' => 'code in ({code:array})',
            'bind' => [
                'code' => $code,
            ],
        ]);

        dd($model->toArray());

    }
   public function t23Action()
    {
        $entryInfo = HrEntryModel::findFirst("resume_id =225763 ");
        dd($entryInfo);

    }
    public function t24Action()
    {
        $resume_id = 225763;
        $entryInfo = HrEntryModel::findFirstByResumeId($resume_id);
        dd(!empty($entryInfo));

    }

    public function checkPhoneAction($params)
    {
        $checkPhoneServer = new \FlashExpress\bi\App\Server\CheckPhoneServer($this->lang, $this->timezone);
        $phoneCheck       = $checkPhoneServer->checkPunchOut($params[0]);
        return !$phoneCheck ? true: false;
    }

    public function getConfirmDetailAction()
    {
        $params['id'] = 2607;
        $params['staff_info_id'] = 29263;

       $aa =  (new \FlashExpress\bi\App\Server\JobTransferConfirmServer())->getConfirmDetail($params);
       dd($aa);

    }
    public function onJobCheekMsAction($params){
        $server = new \FlashExpress\bi\App\Server\ReinstatementServer('zh-CN',$this->timezone);
        $res = $server->onJobCheekMs($params[0]);
        dd($res);
    }

    public function t1Action()
    {
        $s = new \FlashExpress\bi\App\Repository\StaffRepository();
        $r =  $s->getStaffStoreInfo('TH01010003');
        echo json_encode($r,JSON_UNESCAPED_UNICODE);
    }

    public function t2Action($args = [])
    {
        $approvalId = !empty($args[0]) ? $args[0] : 119956;
        $types = !empty($args[1]) ? explode(',', $args[1]) : [54,56];

        $auditList = AuditApprovalModel::find([
            'conditions' => 'biz_type in({audit_type:array}) and state = 1 and approval_id = :approval_id: and deleted = 0',
            'bind' => [
                'audit_type' => $types,
                'approval_id' => $approvalId,
            ],
            'columns' => 'biz_type,biz_value,submitter_id',
        ])->toArray();

        $db = $this->getDI()->get('db');
        $sv = new WorkflowServer($this->lang, $this->timezone);
        foreach ($auditList as $audit) {

            $info = AuditApprovalModel::find([
                'conditions' => 'biz_type = :biz_type: and biz_value = :biz_value: and state = 1 and deleted = 0',
                'bind' => [
                    'biz_type' => $audit['biz_type'],
                    'biz_value' => $audit['biz_value'],
                ],
            ]);
            if (empty($info->toArray())) {
                echo sprintf('数据为空 %d, %d', $audit['biz_type'], $audit['biz_value']), PHP_EOL;
                continue;
            }
            if (count($info->toArray()) > 1) {
                echo sprintf('多于1个人 %d, %d', $audit['biz_type'], $audit['biz_value']), PHP_EOL;
                continue;
            }
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $audit['submitter_id'],
                ],
            ]);
            if (empty($staffInfo)) {
                echo sprintf('工号%s不存在 %d, %d', $audit['submitter_id'], $audit['biz_type'], $audit['biz_value']), PHP_EOL;
                continue;
            }
            $auditorIds = $sv->findHRBP($staffInfo->node_department_id, ['store_id' => $staffInfo->sys_store_id]);
            if (empty($auditorIds)) {
                echo sprintf('没找到合适的bp %d, %d', $audit['biz_type'], $audit['biz_value']), PHP_EOL;
                continue;
            }
            $auditorIdsArr = explode(',', $auditorIds);

            $request = AuditApprovalModel::findFirst([
                'conditions' => 'biz_type = :biz_type: and biz_value = :biz_value: and state = 1 and deleted = 0',
                'bind' => [
                    'biz_type' => $audit['biz_type'],
                    'biz_value' => $audit['biz_value'],
                ],
            ]);
            $db->begin();
            $info->update(['deleted' => 1, 'state' => 3]);
            foreach ($auditorIdsArr as $auditorId) {
                $app = new AuditApprovalModel();
                $app->setBizType($request->getBizType());
                $app->setBizValue($request->getBizValue());
                $app->setFlowId($request->getFlowId());
                $app->setFlowNodeId($request->getFlowNodeId());
                $app->setSubmitterId($request->getSubmitterId());
                $app->setApprovalId($auditorId);
                $app->setState(Enums::APPROVAL_STATUS_PENDING);
                $app->setSummary($request->getSummary());
                $app->setIsCancel($request->getIsCancel());
                $app->setTimeOut($request->getTimeOut());
                $app->setCreatedAt($request->getCreatedAt());
                $app->setUpdateAt($request->getUpdateAt());
                $app->save();

                $this->logger->write_log('t2Action insert ' . $app->id, 'info');
            }
            $wnModel = WorkflowNodeModel::findFirst([
                'conditions' => 'id = :workflow_node_id:',
                'bind' => [
                    'workflow_node_id' => $request->getFlowNodeId(),
                ],
            ]);
            $wnModel->auditor_id = $auditorIds;
            $wnModel->save();
            $this->logger->write_log('t2Action update WorkflowNodeModel' . $auditorIds, 'info');

            $db->commit();
        }
    }
}
