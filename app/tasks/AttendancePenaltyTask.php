<?php

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AttendanceDataV2Model;
use FlashExpress\bi\App\Server\Penalty\AttendancePenaltyServer;
use FlashExpress\bi\App\Server\Penalty\BasePenaltyServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

class AttendancePenaltyTask extends RocketMqBaseTask
{


    public function whileDoneAction()
    {
        while (1){
            $this->mainAction();
        }
    }




    public function initialize()
    {
        $this->tq = "attendance-penalty";//菲律宾-考勤处罚
        parent::initialize();
    }

    /**
     * 个人代理 Invoice 消费
     * @param $msgBody
     * @return bool
     */
    protected function processOneMsg($msgBody)
    {
        $this->getDI()->get('logger')->write_log('AttendancePenaltyTask processOneMsg ' . $msgBody, 'info');
        $data = json_decode($msgBody);
        if (empty($data)) {
            return false;
        }
        $server = new AttendancePenaltyServer($this->lang, $this->timezone);
        try {
            $this->getDI()->get("logger")->write_log(['pop' => $data], "info");
            echo ' staff_info_id ' . $data->staff_info_id . ' attendance_date ' . $data->attendance_date . ' src ' . $data->src . PHP_EOL;
            return $server->fire($data->staff_info_id, $data->attendance_date, $data->src);
        } catch (BusinessException $e) {
            echo $e->getMessage();
            $this->getDI()->get("logger")->write_log([
                'exception'        => $e->getMessage(),
                'getTraceAsString' => $e->getTraceAsString(),
            ], "info");
            return true;
        } catch (Exception $e) {
            echo $e->getMessage() . $e->getTraceAsString();
            $this->getDI()->get("logger")->write_log([
                'exception'        => $e->getMessage(),
                'getTraceAsString' => $e->getTraceAsString(),
            ]);
        }
        return false;
    }


    public function fix1Action()
    {
        $service = new AttendancePenaltyServer('en', '+08:00');

        $data = [
            [210883	,'2024-08-20'],
            [193934	,'2024-08-21'],
            [137436	,'2024-08-21'],
        ];

        foreach ($data as $datum) {
            $service         = new AttendancePenaltyServer('en', '+08:00');
            $attendance_date = $datum[1];
            $staff_info_id   = $datum[0];
            try {
                $service->fire($staff_info_id, $attendance_date, BasePenaltyServer::SRC_MAKE_UP);
            } catch (Exception $e) {
                echo $e->getMessage();
            }
        }
        echo 'end';
    }

    public function fffAction($params)
    {
        $staff_info_id   = $params[0];
        $attendance_date = $params[1];
        $src             = $params[2] ?? 1;
        $service         = new AttendancePenaltyServer($this->lang, $this->timezone);
        $service->fire($staff_info_id, $attendance_date, $src);
    }





    /**
     * 旷工的处罚
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function findAbsentAction($params): bool
    {
        if (RUNTIME == 'pro' && date('Y-m-d') <= AttendancePenaltyServer::START_DATE) {
            echo('未到开始时间');
            return true;
        }

        $today      = date('Y-m-d');
        $start_date = date('Y-m-d', strtotime($today . ' -2 days'));
        $end_date   = date('Y-m-d', strtotime($today . ' -1 days'));

        if (RUNTIME == 'pro') {
            $start_date = max(date('Y-m-d', strtotime($today . ' -2 days')), AttendancePenaltyServer::START_DATE);
            $end_date   = max(date('Y-m-d', strtotime($today . ' -1 days')), AttendancePenaltyServer::START_DATE);
        }

        if (isset($params[0])) {
            $start_date = date('Y-m-d', strtotime($params[0]));
        }
        if (isset($params[1])) {
            $end_date = date('Y-m-d', strtotime($params[1]));
        }


        $settEnvServer = new SettingEnvServer();
        $list          = $settEnvServer->listByCodeFromCache([
            'dept_network_management_id',
            'attendance_penalty_job_title',
        ]);
        if (empty($list)) {
            return true;
        }
        $list              = array_column($list, 'set_val', 'code');
        $job_titles        = explode(',', $list['attendance_penalty_job_title']);
        $sys_department_id = $list['dept_network_management_id'];

        $conditions = 'job_title in ({job_title:array}) and stat_date in ({stat_date:array}) AND sys_department_id = :sys_department_id:  and AB > :AB:';
        $bind       = [
            'AB'                => 0,
            'sys_department_id' => $sys_department_id,
            'job_title'         => $job_titles,
            'stat_date'         => [$start_date, $end_date],
        ];

        if (isset($params[2])) {
            $conditions            .= ' and staff_info_id = :staff_info_id:';
            $bind['staff_info_id'] = intval($params[2]);
        }

        // 获取员工该周期的打卡情况
        $list = AttendanceDataV2Model::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => 'staff_info_id,attendance_started_at,attendance_end_at,stat_date,shift_start,shift_end,AB',
        ])->toArray();

        if (empty($list)) {
            $this->getDI()->get('logger')->write_log('attendance_data_v2 无数据 ', 'info');
            return true;
        }

        $staffIds   = array_values(array_unique(array_column($list, 'staff_info_id')));
        $staffInfos = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id, hire_date, leave_date',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffIds],
        ])->toArray();
        $staffInfos = empty($staffInfos) ? [] : array_column($staffInfos, null, 'staff_info_id');

        foreach ($list as $item) {
            $staff_info = $staffInfos[$item['staff_info_id']];
            if ($item['AB'] == 10 && strtotime($staff_info['hire_date']) == strtotime($item['stat_date'])
                && (!empty($item['attendance_started_at']) || !empty($item['attendance_end_at']))
                && (empty($staff_info['leave_date']) || (strtotime($staff_info['leave_date']) > strtotime($staff_info['hire_date']) + 86400))) {
                continue;
            }
            //昨天的跨天班次不进来
            if ($item['stat_date'] == $end_date && strtotime($item['stat_date'] . ' ' . $item['shift_start']) > strtotime($item['stat_date'] . ' ' . $item['shift_end'])) {
                continue;
            }
            // 剩下的就是 前天跨天 和 昨天非跨天
            (new AttendancePenaltyServer($this->lang, $this->timezone))->push($item['staff_info_id'],
                $item['stat_date'], BasePenaltyServer::SRC_ABSENT);
        }
        return true;
    }

    public function fixAction($params)
    {
        $staff = [];
        if (!empty($params[0])) {
            $staff = explode(',', $params[0]);
        }
        $created_at = $params[1] ?? '';
        $se         = new AttendancePenaltyServer($this->lang, $this->timezone);
        $se->hotfix0308($staff, $created_at);
    }

}