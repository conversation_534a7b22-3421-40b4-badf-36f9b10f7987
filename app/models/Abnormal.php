<?php

namespace FlashExpress\bi\App\Models;

class Abnormal extends BaseModel
{

    /**
     *
     * @var integer
     */
    public $id;

    /**
     *
     * @var integer
     */
    public $sub_id;

    /**
     *
     * @var string
     */
    public $pno;

    /**
     *
     * @var integer
     */
    public $message_type;

    /**
     *
     * @var integer
     */
    public $punish_category;

    /**
     *
     * @var integer
     */
    public $punish_money;

    /**
     *
     * @var integer
     */
    public $staff_info_id;

    /**
     *
     * @var string
     */
    public $store_id;

    /**
     *
     * @var integer
     */
    public $route_action;

    /**
     *
     * @var integer
     */
    public $route_at;

    /**
     *
     * @var integer
     */
    public $created_at;

    /**
     *
     * @var integer
     */
    public $abnormal_time;

    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        //$this->setSchema("bi");
        $this->setSource("abnormal_message");
    }

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'abnormal_message';
    }


}
