<?php

namespace FlashExpress\bi\App\Models;

class BackyardImg extends BaseModel
{

    /**
     *
     * @var integer
     */
    public $id;

    /**
     *
     * @var string
     */
    public $origin_id;

    /**
     *
     * @var integer
     */
    public $oss_bucket_type;

    /**
     *
     * @var string
     */
    public $bucket_name;

    /**
     *
     * @var string
     */
    public $object_key;

    /**
     *
     * @var integer
     */
    public $deleted;

    /**
     *
     * @var string
     */
    public $created_at;

    /**
     *
     * @var integer
     */
    public $rotate_angle;

    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        //$this->setSchema("backyard");
        $this->setSource("backyard_img");
    }

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'backyard_img';
    }

}
