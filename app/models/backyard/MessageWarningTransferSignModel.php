<?php
/**
 * Author: Bruce
 * Date  : 2023-02-16 22:23
 * Description:
 */

namespace FlashExpress\bi\App\Models\backyard;


class MessageWarningTransferSignModel extends BackyardBaseModel
{
    protected $table_name = 'message_warning_transfer_sign';

    //type
    const TRANSFER_TYPE_SUPERIOR = 1;// 上级
    const TRANSFER_TYPE_WITNESS = 2;// 证人
    const TRANSFER_TYPE_CC = 3;// 抄送


    const TYPE_SUPERIOR_LEVEL_SUPERIOR = 1;// 上级
    const TYPE_SUPERIOR_LEVEL_SUPERIOR_SUPERIOR = 2;// 上上级
    const TYPE_SUPERIOR_LEVEL_BP = 3;// BP

    const TYPE_WITNESS_LEVEL_WITNESS = 1;// 证人
    const TYPE_WITNESS_LEVEL_BP = 2;// BP
    const TYPE_WITNESS_LEVEL_ER = 3;// ER
}