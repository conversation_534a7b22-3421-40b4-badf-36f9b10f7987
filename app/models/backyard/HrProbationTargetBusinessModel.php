<?php
namespace FlashExpress\bi\App\Models\backyard;

class HrProbationTargetBusinessModel extends BackyardBaseModel
{
    protected $table_name = 'hr_probation_target_business';

    const STATE_NORMAL = 1;
    const STATE_CANCEL = 2;

    const STAFF_SIGN_STATE_SUCCESS = 1;
    const STAFF_SIGN_STATE_WAIT = 2;
    const STAFF_SIGN_STATE_REJECT = 3;


    const MANAGER_SIGN_STATE_SUCCESS = 1;
    const MANAGER_SIGN_STATE_WAIT = 2;
    const MANAGER_SIGN_STATE_REJECT = 3;

    const BUSINESS_TYPE_STAFF = 1;
    const BUSINESS_TYPE_MANAGER = 2;
}
