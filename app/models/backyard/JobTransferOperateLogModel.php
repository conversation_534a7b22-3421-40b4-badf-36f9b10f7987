<?php


namespace FlashExpress\bi\App\Models\backyard;


class JobTransferOperateLogModel extends BackyardBaseModel
{
    protected $table_name = 'job_transfer_operate_log';

    private $id;
    private $pid;
    private $staf_info_id;
    private $staff_name;
    private $department_id;
    private $department_name;
    private $postion_id;
    private $position_name;
    private $operate_id;
    private $state;
    private $failure_reason;
    private $operate_content;
    private $created_at;
    private $updated_at;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return mixed
     */
    public function getPid()
    {
        return $this->pid;
    }

    /**
     * @param mixed $pid
     */
    public function setPid($pid): void
    {
        $this->pid = $pid;
    }

    /**
     * @return mixed
     */
    public function getStafInfoId()
    {
        return $this->staf_info_id;
    }

    /**
     * @param mixed $staf_info_id
     */
    public function setStafInfoId($staf_info_id): void
    {
        $this->staf_info_id = $staf_info_id;
    }

    /**
     * @return mixed
     */
    public function getStaffName()
    {
        return $this->staff_name;
    }

    /**
     * @param mixed $staff_name
     */
    public function setStaffName($staff_name): void
    {
        $this->staff_name = $staff_name;
    }

    /**
     * @return mixed
     */
    public function getDepartmentId()
    {
        return $this->department_id;
    }

    /**
     * @param mixed $department_id
     */
    public function setDepartmentId($department_id): void
    {
        $this->department_id = $department_id;
    }

    /**
     * @return mixed
     */
    public function getDepartmentName()
    {
        return $this->department_name;
    }

    /**
     * @param mixed $department_name
     */
    public function setDepartmentName($department_name): void
    {
        $this->department_name = $department_name;
    }

    /**
     * @return mixed
     */
    public function getPostionId()
    {
        return $this->postion_id;
    }

    /**
     * @param mixed $postion_id
     */
    public function setPostionId($postion_id): void
    {
        $this->postion_id = $postion_id;
    }

    /**
     * @return mixed
     */
    public function getPositionName()
    {
        return $this->position_name;
    }

    /**
     * @param mixed $position_name
     */
    public function setPositionName($position_name): void
    {
        $this->position_name = $position_name;
    }

    /**
     * @return mixed
     */
    public function getOperateId()
    {
        return $this->operate_id;
    }

    /**
     * @param mixed $operate_id
     */
    public function setOperateId($operate_id): void
    {
        $this->operate_id = $operate_id;
    }

    /**
     * @return mixed
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * @param mixed $state
     */
    public function setState($state): void
    {
        $this->state = $state;
    }

    /**
     * @return mixed
     */
    public function getFailureReason()
    {
        return $this->failure_reason;
    }

    /**
     * @param mixed $failure_reason
     */
    public function setFailureReason($failure_reason): void
    {
        $this->failure_reason = $failure_reason;
    }

    /**
     * @return mixed
     */
    public function getOperateContent()
    {
        return $this->operate_content;
    }

    /**
     * @param mixed $operate_content
     */
    public function setOperateContent($operate_content): void
    {
        $this->operate_content = $operate_content;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param mixed $created_at
     */
    public function setCreatedAt($created_at): void
    {
        $this->created_at = $created_at;
    }

    /**
     * @return mixed
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param mixed $updated_at
     */
    public function setUpdatedAt($updated_at): void
    {
        $this->updated_at = $updated_at;
    }
}