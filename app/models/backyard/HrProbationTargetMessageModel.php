<?php
namespace FlashExpress\bi\App\Models\backyard;

class HrProbationTargetMessageModel extends BackyardBaseModel
{
    protected $table_name = 'hr_probation_target_message';

    //消息类型 1 员工 2 上级  3 BP
    const TYPE_STAFF = 1;
    const TYPE_MANAGER = 2;
    const TYPE_BP = 1;

    const SIGN_STATE_SUCCESS = 1;  //签字动作
    const SIGN_STATE_WAIT = 2;     //消息发送
    const SIGN_STATE_REJECT = 3;  //驳回动作

    const SOURCE_TYPE_SIGN = 1;
    const SOURCE_TYPE_SEND = 2;
}
