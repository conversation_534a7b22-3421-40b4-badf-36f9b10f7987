<?php
/**
 * Author: Bruce
 * Date  : 2024-03-04 18:02
 * Description:
 */

namespace FlashExpress\bi\App\Models\backyard;


class CeoMailProblemCategoryModel extends BackyardBaseModel
{
    protected $table_name = 'ceo_mail_problem_category';

    //可见类型
    //如果 父类下的 子类 存在 个人代理，则父类 use_type 要改为 公共可见
    const USE_TYPE_PUBLIC = 0;//公共可见
    const USE_TYPE_FORMAL = 1;//正式员工 可见
    const USE_TYPE_INDEPENDENT = 2;//个人代理 可见

    const MOBILE_STATE_YES = 1;//移动端展示
    const MOBILE_STATE_NO = 0;//移动端不展示
}