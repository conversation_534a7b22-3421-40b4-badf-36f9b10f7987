<?php


namespace FlashExpress\bi\App\Models\backyard;

/**
 * @property int id	主键
 * @property int staff_info_id	员工工号
 * @property string organization_id	所属组织机构id
 * @property int organization_type	组织类别 1 store 2 department
 * @property int job_title	职位ID
 * @property string attendance_date	考勤日期
 * @property string shift_start	班次开始时间
 * @property string shift_end	班次结束时间
 * @property int working_day	是否是工作日 0:否；1:是
 * @property string started_at	上班打卡时间
 * @property int started_state	上班打卡状态 1正常打卡 2外勤打卡 3补卡 4 请假回写（废弃没用）5工具或系统任务补卡 6 出差打卡回写 7 外出 8 海康覆盖
 * @property float started_staff_lat	上班打卡位置的纬度
 * @property float started_staff_lng	上班打卡位置经度
 * @property string started_store_id	上班打卡网点编号
 * @property float started_store_lng	上班打卡网点经度
 * @property float started_store_lat	上班打卡网点纬度
 * @property string started_clientid	上班打卡客户端 ID
 * @property int started_clientid_num	上班打卡设备打卡次数
 * @property string started_equipment_type	上班打卡服务端 seed
 * @property string started_os	上班打卡客户端系统
 * @property string started_path	上班考勤照片
 * @property string started_bucket	上班考勤照片所属bucket
 * @property string started_remark	上班打卡备注信息
 * @property string end_at	下班打卡时间
 * @property int end_state	下班打卡状态
 * @property float end_staff_lat	下班打卡位置的纬度
 * @property float end_staff_lng	下班打卡位置的经度
 * @property string end_store_id	下班打卡网点编号
 * @property float end_store_lng	下班打卡网点经度
 * @property float end_store_lat	下班打卡网点纬度
 * @property string end_clientid	下班打卡客户端 ID
 * @property int end_clientid_num	下班打卡设备打卡次数
 * @property string end_equipment_type	下班打卡服务端 seed
 * @property string end_os	下班打卡客户端系统
 * @property string end_path	下班考勤照片
 * @property string end_bucket	下班考勤照片所属bucket
 * @property string end_remark	下班打卡备注信息
 * @property string created_at
 * @property string updated_at
 */
class StaffWorkAttendanceModel extends  BackyardBaseModel
{
    protected $table_name = 'staff_work_attendance';

    //started_state end_state  0 缺卡   1 正常打卡 2 外勤打卡 3 补卡 4 请假回写（废弃没用）5 工具或系统任务补卡 6 出差打卡回写 7 外出

    const STATE_FIELD_PERSONNEL_CARD = 2;//外勤打卡
    const STATE_MAKE_UP_CARD = 3;
    const STATE_MAKE_UP_CARD_SYSTEM_ENTRY = 5;
    const STATE_BUSINESS_TRIP = 6;
    const STATE_GO_OUT = 7;
    const STATE_HUB_ATTENDANCE = 8; // 海康覆盖
    const STATE_WORK_HOME = 9; //居家办公
    const STATE_WORK_FACE_CHECK = 99; //人脸认证(考勤-扫脸记录里使用)
    // 是工作日
    const IS_WORKING_DAY = 1;
    // 不是工作日（公共假期或者休息日）
    const IS_NOT_WORKING_DAY = 0;

    const SHIFT_TYPE_ONLY = 0;//单班次 默认值 历史数据刷0
    const SHIFT_TYPE_FIRST = 1;//多班次 第一次上下班
    const SHIFT_TYPE_SECOND = 2;//多班次 第二次上下班



    //started_equipment_type 字段枚举
    const PLATFORM_KIT = 1;//kit 平台
    const PLATFORM_BY = 3;//backyard 平台

    //上下班
    const ATTENDANCE_ON = 1; //上班
    const ATTENDANCE_OFF = 2;//下班
    const ATTENDANCE_ON_OFF = 3;//上下班

    const WORK_DAY_PH_REST = 1; //ph 和 off
    const WORK_DAY_UN_PH_UN_REST = 2;//非ph  非off
    const WORK_DAY_PH_UN_REST = 3;//ph 非 off
    const WORK_DAY_UN_PH_REST = 4;//非ph  off

}