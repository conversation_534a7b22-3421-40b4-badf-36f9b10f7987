<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON>
 * Date: 2021/3/8
 * Time: 17:48
 */


namespace FlashExpress\bi\App\Models\backyard;

class HrStaffAnnexInfoModel extends BackyardBaseModel
{
    protected $table_name = 'hr_staff_annex_info';

    // 身份证
    const TYPE_ID_CARD = 1;
    // 银行卡
    const TYPE_BANK_CARD = 2;
    // 社保卡号
    const TYPE_SOCIAL_SECURITY = 3;
    // 公积金
    const TYPE_FUND = 4;
    // 医保
    const TYPE_MEDICAL_INSURANCE = 5;
    // 水卡号
    const TYPE_TAX_CARD = 6;
    //银行流水
    const BANK_FLOW = 7;
    //备用银行卡 菲律宾独有
    const TYPE_BACKUP_BANK_CARD = 9;

    // 户口簿第一页，本人信息页，residence booklet
    const TYPE_RESIDENCE_BOOKLET_CARD = 8;


    // 待审核
    const AUDIT_STATE_NOT_REVIEWED = 0;
    // 审核通过
    const AUDIT_STATE_PASSED = 1;
    // 审核未通过
    const AUDIT_STATE_REJECT = 2;
    // 待上传
    const AUDIT_STATE_TO_BE_UPLOAD = 999;

    // ai 审核通过
    const AI_AUDIT_STATE_PASSED = 1;
    // ai 审核拒绝
    const AI_AUDIT_STATE_REJECT = 2;

    const FACE_CHECK_STATE_HIT = 1;//命中
    const FACE_CHECK_STATE_NOT_HIT = 2;//未命中

    public static $audit_state_key = [
        self::AUDIT_STATE_NOT_REVIEWED => "audit_state_not_reviewed_0",
        self::AUDIT_STATE_PASSED => 'audit_state_passed_1',
        self::AUDIT_STATE_REJECT => 'audit_state_reject_2',
        self::AUDIT_STATE_TO_BE_UPLOAD => 'audit_state_to_be_upload_999'
    ];
}