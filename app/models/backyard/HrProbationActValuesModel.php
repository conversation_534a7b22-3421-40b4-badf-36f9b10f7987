<?php

namespace FlashExpress\bi\App\Models\backyard;

/**
 * 试用期价值观表
 */
class HrProbationActValuesModel extends BackyardBaseModel
{
    protected $table_name = 'hr_probation_act_values';

    const CONCEPT_TYPE_CUSTOMER = 1;
    const CONCEPT_TYPE_RESULT = 2;
    const CONCEPT_TYPE_TEAM = 3;
    const CONCEPT_TYPE_FAITH = 4;
    const CONCEPT_TYPE_LOYAL = 5;

    // concept_type - 概念类型
    public static $concept_type_list = [
        self::CONCEPT_TYPE_CUSTOMER => 'probation_concept_type_customer',
        self::CONCEPT_TYPE_RESULT   => 'probation_concept_type_result',
        self::CONCEPT_TYPE_TEAM     => 'probation_concept_type_team',
        self::CONCEPT_TYPE_FAITH    => 'probation_concept_type_faith',
        self::CONCEPT_TYPE_LOYAL    => 'probation_concept_type_loyal',
    ];

    const CURRENT_VERSION  = 1; //当前版本
}