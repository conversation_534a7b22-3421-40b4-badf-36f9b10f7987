<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/1/11
 * Time: 5:33 PM
 */

namespace FlashExpress\bi\App\Models\backyard;
class OutsourcingOvertimeModel  extends BackyardBaseModel{

    protected $table_name = 'outsourcing_overtime';

    const TYPE_ADD_WORK_TIME = 1;//延长工作时间

    const SALARY_STATE_EFFECTIVE = 1;//薪酬有效 ot
    const SALARY_STATE_UN_COUNT = 0;//没计算的类型
    const SALARY_STATE_INVALID = -1;//薪酬无效 ot 页面筛选用
    const SALARY_STATE_INVALID_TIME = 2;//打卡时长不足  无效ot
    const SALARY_STATE_COVER = 3;//打卡时间没覆盖ot 无效ot
    const SALARY_STATE_MISS_CARD = 4;//缺卡 无效
    const SALARY_STATE_AUDIT_FAIL = 5;//审批状态 无效

    //无效ot 原因 翻译
    public static $salary_ot_reason = [
        self::SALARY_STATE_INVALID_TIME => 'ot_invalid_reason_time',
        self::SALARY_STATE_COVER => 'ot_invalid_cover_time',
        self::SALARY_STATE_MISS_CARD => 'ot_invalid_miss',
        self::SALARY_STATE_AUDIT_FAIL => 'ot_invalid_audit_state',
    ];

    const IN_APPROVAL = 1;//在审批队列中
    const NOT_IN_APPROVAL = 0;//不在审批队列


}