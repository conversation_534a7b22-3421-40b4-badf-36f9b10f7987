<?php

namespace FlashExpress\bi\App\Models\backyard;


class SystemExternalApprovalModel extends BackyardBaseModel
{
    const is_call_third_party_1        = 1; //调用外部系统成功
    const is_call_third_party_2        = 2; //调用外部系统失败

    protected $table_name = 'system_external_approval';

    /**
     * @description 根据序列号获取详情
     * @param $serial_no
     * @return mixed
     */
    public static function getOneBySerialNo($serial_no)
    {
        return self::findFirst([
            'conditions' => 'serial_no = :serial_no:',
            'bind' => [
                'serial_no' => $serial_no
            ],
        ]);
    }
}