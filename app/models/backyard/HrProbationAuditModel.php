<?php
/**
 * Author: Bruce
 * Date  : 2022-07-08 18:24
 * Description:
 */

namespace FlashExpress\bi\App\Models\backyard;


class HrProbationAuditModel extends  BackyardBaseModel
{
    protected $table_name = 'hr_probation_audit';


    const STATUS_PASS = 2;
    const STATUS_NOT_PASS = 3;//不通过

    /**
     * 是否终止工作 1 是 2 否
     */
    const IS_TERMINATE = 1;
    const IS_NO_TERMINATE = 2;

    /**
     * 待处理
     */
    const AUDIT_STATUS_PENDING  = 1;

    /**
     * 已处理
     */
    const AUDIT_STATUS_PROCESSED = 2;
    /**
     * 超时关闭
     */
    const AUDIT_STATUS_TIMEOUT  = 3;

    /**
     *
     * 审批人等级 1 上级，2 上上级
     */
    const AUDIT_LEVEL_FIRST  =1;
    const AUDIT_LEVEL_SECOND  =2;

}