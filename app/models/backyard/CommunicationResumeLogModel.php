<?php

namespace FlashExpress\bi\App\Models\backyard;

class CommunicationResumeLogModel extends BackyardBaseModel
{
    //面试管理 1=联系不上、2=要求岗位或网点没有hc、3=不符合岗位要求、4=候选人已拒绝、5=其他
    const FAILURE_REASON_TYPE_NOT_CON   = 1;
    const FAILURE_REASON_TYPE_EMPTY_HC  = 2;
    const FAILURE_REASON_TYPE_NOT_MIS   = 3;
    const FAILURE_REASON_TYPE_NOT_REF   = 4;
    const FAILURE_REASON_TYPE_NOT_OTHER = 5;

    //沟通状态
    const STATUS_SUCCESS = 1;
    const STATUS_FAIL    = 2;
    const STATUS_EMPTY   = 3;
    const STATUS_AFRESH  = 4;
    const STATUS_IN_COMMUNICATION  = 5; // 沟通中

    protected $table_name = 'communication_resume_log';
}