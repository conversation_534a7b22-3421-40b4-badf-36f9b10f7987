<?php
/**
 * Author: Bruce
 * Date  : 2024-05-28 20:38
 * Description:
 */

namespace FlashExpress\bi\App\Models\backyard;


class WinHrAsyncImportTaskModel extends BackyardBaseModel
{
    protected $table_name = 'winhr_async_import_task';

    /**
     * 1 待执行 2 执行完成
     */
    const STATE_WAIT_EXECUTE = 1;//待执行
    const STATE_EXECUTED = 2;//执行完成

    //是否删除
    const IS_DELETED     = 1;//是
    const IS_NOT_DELETED = 0;//否

    const BATCH_ADD_HC               = 1;//HC批量申请
    const BATCH_VOID_HC              = 2;//HC批量作废
    const BATCH_EDIT_DEMAND_NUM_HC   = 3;//HC批量修改需求人数
    const BATCH_EDIT_PRIORITY_NUM_HC = 4;//HC批量修改优先级
    const BATCH_AUDIT_HC             = 5;//HC批量 审批

    public static $importTypeMap = [
        self::BATCH_ADD_HC,
        self::BATCH_VOID_HC,
        self::BATCH_EDIT_DEMAND_NUM_HC,
        self::BATCH_EDIT_PRIORITY_NUM_HC,
        self::BATCH_AUDIT_HC,
    ];

}