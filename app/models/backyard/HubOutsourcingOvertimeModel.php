<?php


namespace FlashExpress\bi\App\Models\backyard;


/**
 * 外协加班申请单表
 */
class HubOutsourcingOvertimeModel extends BackyardBaseModel
{
    const OSM_STATE_PENDING = 1;    // 待处理
    const OSM_STATE_AUDIT = 2;      // 审核中
    const OSM_STATE_PROCESSED = 3;  // 已经处理

    public static $osm_state_text = [
        self::OSM_STATE_PENDING   => 'osm_state_1',
        self::OSM_STATE_AUDIT     => 'osm_state_2',
        self::OSM_STATE_PROCESSED => 'osm_state_3',
    ];

    protected $table_name = 'hub_outsourcing_overtime';
}