<?php

namespace FlashExpress\bi\App\Models\backyard;

use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;

/**
 * 示例模型 - 演示不同的强制更新模式
 * @description: 展示如何在不同模型中自定义强制更新行为
 * @author: AI
 * @date: 2025-01-15
 */
class ExampleModel extends BackyardBaseModel
{
    protected $table_name = 'example_table';

    /**
     * 示例1: 使用全字段强制更新模式（默认）
     * 所有被设置的字段都会被强制更新
     */
    // 不需要重写任何方法，使用默认的全字段模式

    /**
     * 示例2: 关闭全字段模式，只对特定字段强制更新
     */
    /*
    protected function isForceUpdateAllFields()
    {
        return false; // 关闭全字段模式
    }

    protected function getForceUpdateFields()
    {
        return ['status', 'is_active', 'updated_at']; // 只对这些字段强制更新
    }
    */

    /**
     * 示例3: 根据条件动态决定是否使用全字段模式
     */
    /*
    protected function isForceUpdateAllFields()
    {
        // 例如：只有管理员操作时才使用全字段模式
        $user = $this->getDI()->get('session')->get('user');
        return $user && $user['role'] === 'admin';
    }
    */

    /**
     * 示例4: 根据字段内容动态决定强制更新字段
     */
    /*
    protected function getForceUpdateFields()
    {
        $fields = ['id', 'created_at']; // 基础字段
        
        // 根据业务逻辑动态添加字段
        if (isset($this->status)) {
            $fields[] = 'status';
        }
        
        if (isset($this->priority) && $this->priority > 5) {
            $fields[] = 'priority';
            $fields[] = 'updated_at';
        }
        
        return $fields;
    }
    */
}
