<?php


namespace FlashExpress\bi\App\Models\backyard;


class StaffLeaveReasonModel extends BackyardBaseModel
{
    protected $table_name = 'staff_leave_reason';
    /**
     * @Deprecated
     */
    const CODE_INDIVIDUAL_CONTRACTOR = 33;//去做个人代理(旧)
    /**
     * @Deprecated
     */
    const CODE_TRANSFER_FH = 36;//转加盟商代理

    const GROUP_TYPE_DEFAULT = 0; // 默认 暂未分组
    const GROUP_TYPE_5 = 5; // 收入低/福利问题
    const GROUP_TYPE_6 = 6; // 个人因素
    const GROUP_TYPE_7 = 7; // 培训问题
    const GROUP_TYPE_8 = 8; // 网点问题
    const GROUP_TYPE_9 = 9; // 处罚问题
    const GROUP_TYPE_10 = 10; // 系统问题
    const GROUP_TYPE_11 = 11; // 其他


    const LEAVE_REASON_18 = 18;//惩罚严重犯错的行为
    const LEAVE_REASON_25 = 25;//向公司提供虚假信息
    const LEAVE_REASON_76 = 76;//个人原因（居住变更、健康、家庭等）
    const LEAVE_REASON_77 = 77;//转去第三方
    const LEAVE_REASON_80 = 80;//腐败/滥用职权或故意对雇主实施刑事犯罪
    const LEAVE_REASON_85 = 85;//未通过试用期（提前通知）
    const LEAVE_REASON_83 = 83;//严重违反与工作相关的规定或合法且公正的雇主指令
    const LEAVE_REASON_96 = 96;//去做个人代理
    const LEAVE_REASON_97 = 97;//公司解约个人代理

    public static $agent_leave_reason = [
        self::GROUP_TYPE_5,
        self::GROUP_TYPE_6,
        self::GROUP_TYPE_7,
        self::GROUP_TYPE_8,
        self::GROUP_TYPE_9,
        self::GROUP_TYPE_10,
        self::GROUP_TYPE_11,
    ];

    //离职原因版本
    const LEAVER_REASON_VERSION_NEW = 2;//新版

}