<?php

namespace FlashExpress\bi\App\Models\backyard;

class InternalPositionSetModel extends BackyardBaseModel
{
    protected $table_name = 'internal_position_set';

    /**
     * @var int 职位ID
     */
    public $job_id;

    /**
     * @var string 优先级
     */
    public $priority;

    /**
     * @var int 操作员ID
     */
    public $operator_id;

    /**
     * @var int 是否热招
     */
    public $is_hot;

    /**
     * @var int 排序
     */
    public $sort;

    /**
     * @var string 创建时间
     */
    public $created_at;

    /**
     * @var string 更新时间
     */
    public $updated_at;

    const IS_HOT_YES = 1;//热招
    const IS_HOT_NO = 2; //非热招
}