<?php

namespace FlashExpress\bi\App\Models\backyard;


class HrStaffApplySupportStoreModel extends BackyardBaseModel
{
    protected $table_name = 'hr_staff_apply_support_store';

    //是否住宿0否1是
    const IS_STAY_NO = 0;
    const IS_STAY_YES = 1;
    //是否 枚举
    public static $whether = [
        self::IS_STAY_NO => 'whether_no',
        self::IS_STAY_YES => 'whether_yes'
    ];

    //是否揽派分离
    const IS_UN_SEPARATE = 0;
    const IS_SEPARATE = 1;
    //是否原网点打卡
    const IS_ORIGINAL_STORE = 1;
    const IS_NOT_ORIGINAL_STORE = 0;

    const SUPPORT_STATE_PENDING = 1; //待生效
    const SUPPORT_STATE_EFFECTIVE = 2; //已生效
    const SUPPORT_STATE_INVALID = 3; //已失效
    const SUPPORT_STATE_CANCEL = 4; //已取消

}