<?php

namespace FlashExpress\bi\App\Models\backyard;


class VehicleInfoAuditModel extends BackyardBaseModel
{
    /**
     * @var int|\Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     */
    public    $status;
    protected $table_name = 'vehicle_info_audit';

    /**
     * @var
     */
    public    $staff_info_id;
    /**
     * @var
     */
    public    $before_vehicle_brand;
    /**
     * @var
     */
    public    $before_vehicle_brand_text;
    /**
     * @var
     */
    public    $before_plate_number;
    /**
     * @var
     */
    public    $after_vehicle_brand;
    /**
     * @var
     */
    public    $after_vehicle_brand_text;
    /**
     * @var
     */
    public    $before_license_location;
    /**
     * @var
     */
    public    $after_plate_number;
    /**
     * @var
     */
    public    $vehicle_check_img_1;
    /**
     * @var
     */
    public    $after_license_location;
    /**
     * @var
     */
    public    $vehicle_check_img_2;
    /**
     * @var
     */
    public    $vehicle_check_video;
    /**
     * @var
     */
    public    $serial_no;
    /**
     * @var
     */
    public    $container_type;

}