<?php


namespace FlashExpress\bi\App\Models\backyard;


class HrResumeRecommendModel extends  BackyardBaseModel
{
    protected $table_name = 'hr_resume_recommend';
    //by推荐简历表状态
    const RECOMMEND_STATE_EDITING = 1;//编辑中
    const RECOMMEND_STATE_SUBMITTED = 2;//待提交
    const RECOMMEND_STATE_SUBMIT = 3;//已提交


    //by推荐简历表是否删除
    const IS_DELETE_NO = 1;//未删除
    const IS_DELETE_YES = 2;//已删除
    
    public function initialize()
    {
        $this->useDynamicUpdate(true);
        $this->setSource('hr_resume_recommend');
    }

}