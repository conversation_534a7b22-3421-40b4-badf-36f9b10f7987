<?php


namespace FlashExpress\bi\App\Models\backyard;
use Exception;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;

class WorkflowNodeAccidentBusinessModel extends BackyardBaseModel
{
    protected $table_name = 'workflow_node_accident_business';
	
	public $id;
	public $flow_id;
	public $node_id;
	public $type;
	public $auditor_type;
	public $audit_level;
	public $auditor_id;
	public $status;
	public $business;
	public $biz_type;
	public $biz_value;
	public $submitter_id;
	public $version;
	public $node_base_cc_id;
	
	public  $status_1=1;//待激活
	public  $status_2=2;//待处理
	public  $status_3=3;//已处理
	public  $business_1 = 1;//push
	public  $business_2 = 2;//cc
    public const CC_BEFORE_NODE = 1; //节点前 //同意或驳回之后
    public const CC_AFTER_NODE = 2; //节点后 //仅同意
    const STATUS_TO_BE_ACTIVATED = 1;//待激活
    const STATUS_TO_BE_PROCESSED = 2;//待处理
    const STATUS_HAS_PROCESSED = 3;//已处理

    const CC_TYPE_NORMAL = 1; //同意驳回时，抄送
    const CC_TYPE_ONLY_APPROVAL = 2; //仅同意时，抄送

    const BUSINESS_TYPE_PUSH = 1;              //push
    const BUSINESS_TYPE_CC = 2;                //cc
    const BUSINESS_TYPE_ASYNC_CALLBACK = 3;    //async callback
    const BUSINESS_TYPE_HIDE_PUSH = 4; //隐式push

	/**
	 * @return mixed
	 */
	public function getId()
	{
		return $this->id;
	}
	
	/**
	 * @return mixed
	 */
	public function getFlowId()
	{
		return $this->flow_id;
	}
	
	/**
	 * @param mixed $flow_id
	 */
	public function setFlowId($flow_id): void
	{
		$this->flow_id = $flow_id;
	}
	
	/**
	 * @return mixed
	 */
	public function getNodeId()
	{
		return $this->node_id;
	}
	
	/**
	 * @param mixed $node_id
	 */
	public function setNodeId($node_id): void
	{
		$this->node_id = $node_id;
	}
	
	/**
	 * @return mixed
	 */
	public function getType()
	{
		return $this->type;
	}
	
	/**
	 * @param mixed $type
	 */
	public function setType($type): void
	{
		$this->type = $type;
	}
	
	/**
	 * @return mixed
	 */
	public function getAuditorType()
	{
		return $this->auditor_type;
	}
	
	/**
	 * @param mixed $auditor_type
	 */
	public function setAuditorType($auditor_type): void
	{
		$this->auditor_type = $auditor_type;
	}
	
	/**
	 * @return mixed
	 */
	public function getAuditorLevel()
	{
		return $this->audit_level;
	}
	
	/**
	 * @param mixed $auditor_level
	 */
	public function setAuditorLevel($auditor_level): void
	{
		$this->audit_level = $auditor_level;
	}
	
	/**
	 * @return mixed
	 */
	public function getAuditorId()
	{
		return $this->auditor_id;
	}
	
	/**
	 * @param mixed $auditor_id
	 */
	public function setAuditorId($auditor_id): void
	{
		$this->auditor_id = $auditor_id;
	}
	
	/**
	 * @return mixed
	 */
	public function getStatus()
	{
		return $this->status;
	}
	
	/**
	 * @param mixed $status
	 */
	public function setStatus($status): void
	{
		$this->status = $status;
	}
	
	
	

	
	/**
	 * @return mixed
	 */
	public function getBusiness()
	{
		return $this->business;
	}
	
	/**
	 * @param mixed $specify_approver
	 */
	public function setBusiness($business): void
	{
		$this->business = $business;
	}
	
	/**
	 * @return mixed
	 */
	public function getBizType()
	{
		return $this->biz_type;
	}
	
	/**
	 * @param mixed $specify_approver
	 */
	public function setBizType($biz_type): void
	{
		$this->biz_type = $biz_type;
	}
	
	/**
	 * @return mixed
	 */
	public function getSubmitterId()
	{
		return $this->submitter_id;
	}
	
	/**
	 * @param mixed $specify_approver
	 */
	public function setSubmitterId($submitter_id): void
	{
		$this->submitter_id = $submitter_id;
	}
	/**
	 * @return mixed
	 */
	public function getBizValue()
	{
		return $this->biz_value;
	}
	
	/**
	 * @param mixed $specify_approver
	 */
	public function setBizValue($biz_value): void
	{
		$this->biz_value = $biz_value;
	}
	
	/**
	 * @return mixed
	 */
	public function getVersion()
	{
		return $this->version;
	}

    /**
     * @return mixed
     */
    public function getNodeBaseCcId()
    {
        return $this->node_base_cc_id;
    }

    /**
     * @param mixed $node_base_cc_id
     */
    public function setNodeBaseCcId($node_base_cc_id): void
    {
        $this->node_base_cc_id = $node_base_cc_id;
    }
	
	/**
	 * @param mixed $specify_approver
	 */
	public function setVersion($version): void
	{
		$this->version = $version;
	}

    /**
     * @description 获取待处理的任务
     * @param $field
     * @param $offset
     * @param $limit
     * @return mixed
     */
    public function getWaitPushList($field = '*', $offset = 0, $limit = 1000)
    {
        //这里读取主库
        $this->setWriteConnectionService(self::WRITE_DB_PHALCON_DI_NAME);

        return self::find([
            'conditions' => 'status = :status:',
            'bind'       => [
                'status' => self::STATUS_TO_BE_PROCESSED,
            ],
            'limit'      => $limit,
            'offset'     => $offset,
            'order'      => 'id desc',
            'columns'    => $field,
        ])->toArray();
    }

    /**
     * @description 获取待处理的任务
     * @param string $field
     * @param array $params
     * @param int $offset
     * @param int $limit
     * @return mixed
     */
    public function getWaitPushListByBusiness($field = '*', $params = [], $offset = 0, $limit = 1000,$andWhere = '')
    {
        //这里读取主库
        $this->setWriteConnectionService(self::WRITE_DB_PHALCON_DI_NAME);

        return self::find([
            'conditions' => 'status = :status: and business in ({business:array}) '.$andWhere,
            'bind'       => [
                'status'        => self::STATUS_TO_BE_PROCESSED,
                'business' => $params['business'],
            ],
            'limit'      => $limit,
            'offset'     => $offset,
            'order'      => 'id desc',
            'columns'    => $field,
        ])->toArray();
    }

    /**
     * @description:这里是触发抄送生效
     *
     * @param int $node_id 节点 id
     * @param $action
     * @return bool : true or false
     * <AUTHOR> L.J
     * @time       : 2022/2/8 20:03
     */
    public function saveCcStatus(int $node_id = 0, $action)
    {
        try {
            //直接 update 会产生死锁 查询出 主键后 根据主键进行 update
            $data = self::find([
                'conditions' => 'node_id = :node_id: and business = :business: and status = :status: ',
                'bind'       => [
                    'node_id'  => $node_id,
                    'business' => self::BUSINESS_TYPE_CC,
                    'status'   => self::STATUS_TO_BE_ACTIVATED,
                ],
                'columns'    => 'id,type,auditor_id',
            ])->toArray();
            if (empty($data)) {
                return false;
            }
            $ccStaffIds = array_column($data, 'auditor_id');
            if (empty($ccStaffIds)) {
                return false;
            }
            $ccStaffInfoIds = [];
            foreach ($ccStaffIds as $staffIds) {
                if (empty($staffIds)) {
                    continue;
                }
                $tmpIds = explode(',', $staffIds);
                $ccStaffInfoIds = array_merge($ccStaffInfoIds, $tmpIds);
            }
            if (empty($ccStaffInfoIds)) {
                return false;
            }

            //获取抄送人离职信息
            $ccStaffInfoList = HrStaffInfoModel::find([
                "staff_info_id in({staff_ids:array}) and state = 2",
                "bind" => [
                    "staff_ids" => $ccStaffInfoIds
                ],
                "columns" => "staff_info_id"
            ])->toArray();
            $ccResignStaffInfoList = array_column($ccStaffInfoList, 'staff_info_id');

            //这里循环 根据主键 更新状态
            foreach ($data as $v) {
                //[仅同意]非同意操作时，不抄送
                if ($v['type'] == self::CC_AFTER_NODE && !in_array($action, [
                        Enums::WF_ACTION_APPROVE_COUNTERSIGN,
                        Enums::WF_ACTION_OVERTIME_AUTO_APPROVAL_COUNTERSIGN,
                        Enums::WF_ACTION_CREATE,
                        Enums::WF_ACTION_CREATE_CANCEL,
                        Enums::WF_ACTION_EDIT_RECREATE,
                        Enums::WF_ACTION_APPROVE,
                        Enums::WF_ACTION_OVERTIME_AUTO_APPROVAL,
                        Enums::WF_ACTION_APPROVAL_EMPTY,
                    ])
                ) {
                    continue;
                }

                //离职状态不抄送
                $auditorIds = explode(',', $v['auditor_id']);
                $ccOnJobStaff = array_diff($auditorIds, $ccResignStaffInfoList);
                if (empty($ccOnJobStaff)) {
                    continue;
                }
                $this->getDI()->get('logger')->write_log("save to workflow_node_accident_business ," . $v['auditor_id'] . json_encode($ccOnJobStaff), "info");
                $result = $this->getDI()->get("db")->updateAsDict($this->table_name,
                    [
                        "status" => self::STATUS_TO_BE_PROCESSED,
                        "auditor_id" => implode(',', $ccOnJobStaff)
                    ], [
                        "conditions" => " id = ? ",
                        "bind"       => [$v['id']],
                    ]);
                if (!$result) {
                    throw new \Exception('更新失败 => id'.json_encode($v), ErrCode::SYSTEM_ERROR);
                }
            }

            return true;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("触发抄送失败saveCcStatus".json_encode(
                    [
                        'Err_Msg'  => $e->getMessage(),
                        'Err_Line' => $e->getLine(),
                        'Err_File' => $e->getFile(),
                        'Err_Code' => $e->getCode(),
                    ], JSON_UNESCAPED_UNICODE));
        }
        return false;
    }
	
}