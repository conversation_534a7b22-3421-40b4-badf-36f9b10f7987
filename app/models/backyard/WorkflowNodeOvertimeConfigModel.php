<?php

namespace FlashExpress\bi\App\Models\backyard;

class WorkflowNodeOvertimeConfigModel extends BackyardBaseModel
{
    protected $table_name = 'workflow_node_overtime_config';

    private $id;
    private $node_id;
    private $flow_id;
    private $is_switch_open;
    private $overtime_sub_type;
    private $overtime_column;
    private $overtime_days;
    private $overtime_policy;
    private $handover_policy;
    private $handover_config;
    private $handover_audit_days;
    private $handover_overtime_policy;
    private $is_handover_termination;
    private $handover_termination_staff_ids;
    private $is_handover;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getNodeId()
    {
        return $this->node_id;
    }

    /**
     * @param mixed $node_id
     */
    public function setNodeId($node_id): void
    {
        $this->node_id = $node_id;
    }

    /**
     * @return mixed
     */
    public function getFlowId()
    {
        return $this->flow_id;
    }

    /**
     * @param mixed $flow_id
     */
    public function setFlowId($flow_id): void
    {
        $this->flow_id = $flow_id;
    }

    /**
     * @return mixed
     */
    public function getIsSwitchOpen()
    {
        return $this->is_switch_open;
    }

    /**
     * @param mixed $is_switch_open
     */
    public function setIsSwitchOpen($is_switch_open): void
    {
        $this->is_switch_open = $is_switch_open;
    }

    /**
     * @return mixed
     */
    public function getOvertimeSubType()
    {
        return $this->overtime_sub_type;
    }

    /**
     * @param mixed $overtime_sub_type
     */
    public function setOvertimeSubType($overtime_sub_type): void
    {
        $this->overtime_sub_type = $overtime_sub_type;
    }

    /**
     * @return mixed
     */
    public function getOvertimeColumn()
    {
        return $this->overtime_column;
    }

    /**
     * @param mixed $overtime_column
     */
    public function setOvertimeColumn($overtime_column): void
    {
        $this->overtime_column = $overtime_column;
    }

    /**
     * @return mixed
     */
    public function getOvertimeDays()
    {
        return $this->overtime_days;
    }

    /**
     * @param mixed $overtime_days
     */
    public function setOvertimeDays($overtime_days): void
    {
        $this->overtime_days = $overtime_days;
    }

    /**
     * @return mixed
     */
    public function getOvertimePolicy()
    {
        return $this->overtime_policy;
    }

    /**
     * @param mixed $overtime_policy
     */
    public function setOvertimePolicy($overtime_policy): void
    {
        $this->overtime_policy = $overtime_policy;
    }

    /**
     * @return mixed
     */
    public function getHandoverPolicy()
    {
        return $this->handover_policy;
    }

    /**
     * @param mixed $handover_policy
     */
    public function setHandoverPolicy($handover_policy): void
    {
        $this->handover_policy = $handover_policy;
    }

    /**
     * @return mixed
     */
    public function getHandoverConfig()
    {
        return $this->handover_config;
    }

    /**
     * @param mixed $handover_config
     */
    public function setHandoverConfig($handover_config): void
    {
        $this->handover_config = $handover_config;
    }

    /**
     * @return mixed
     */
    public function getHandoverAuditDays()
    {
        return $this->handover_audit_days;
    }

    /**
     * @param mixed $handover_audit_days
     */
    public function setHandoverAuditDays($handover_audit_days): void
    {
        $this->handover_audit_days = $handover_audit_days;
    }

    /**
     * @return mixed
     */
    public function getHandoverOvertimePolicy()
    {
        return $this->handover_overtime_policy;
    }

    /**
     * @param mixed $handover_overtime_policy
     */
    public function setHandoverOvertimePolicy($handover_overtime_policy): void
    {
        $this->handover_overtime_policy = $handover_overtime_policy;
    }

    /**
     * @return mixed
     */
    public function getIsHandoverTermination()
    {
        return $this->is_handover_termination;
    }

    /**
     * @param mixed $is_handover_termination
     */
    public function setIsHandoverTermination($is_handover_termination): void
    {
        $this->is_handover_termination = $is_handover_termination;
    }

    /**
     * @return mixed
     */
    public function getHandoverTerminationStaffIds()
    {
        return $this->handover_termination_staff_ids;
    }

    /**
     * @param mixed $handover_termination_staff_ids
     */
    public function setHandoverTerminationStaffIds($handover_termination_staff_ids): void
    {
        $this->handover_termination_staff_ids = $handover_termination_staff_ids;
    }

    /**
     * @return mixed
     */
    public function getIsHandover()
    {
        return $this->is_handover;
    }

    /**
     * @param mixed $is_handover
     */
    public function setIsHandover($is_handover): void
    {
        $this->is_handover = $is_handover;
    }
}