<?php


namespace FlashExpress\bi\App\Models\backyard;


class HrOutsourcingOrderModel extends BackyardBaseModel
{
    protected $table_name = 'hr_outsourcing_order';

    const STATUS_PENDING   = 1;//待生效
    const STATUS_EFFECTIVE = 2;//已生效
    const STATUS_CLOSED    = 3;//已结束
    const STATUS_CANCELED  = 4;//已取消

    const STATUS_TRANS = [
        self::STATUS_PENDING   => "os_order_status_1",
        self::STATUS_EFFECTIVE => "os_order_status_2",
        self::STATUS_CLOSED    => "os_order_status_3",
        self::STATUS_CANCELED  => "os_order_status_4"
    ];

    const IS_EXCEEDS_YES = 1;//osm 置顶
    const IS_EXCEEDS_NO = 0;//osm 取消置顶
}