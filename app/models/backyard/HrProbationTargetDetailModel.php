<?php
namespace FlashExpress\bi\App\Models\backyard;

/**
 * 转正评估-目标设置详情表
 * 包含多个阶段
 */
class HrProbationTargetDetailModel extends BackyardBaseModel
{
    protected $table_name = 'hr_probation_target_detail';

    const SETTING_STATE_FINISH = 1;             //已完成
    const SETTING_STATE_NOT_START = 2;          //未开始

    //发送状态 - send_state
    const SEND_STATE_FINISH = 1;                //已完成
    const SEND_STATE_NOT_SEND = 2;              //未发送

    //签字状态 - sign_state
    const SIGN_STATE_FINISH = 1;                //已完成
    const SIGN_STATE_NOT_START = 2;             //未开始
    const SIGN_STATE_WAIT = 3;                  //进行中

    //stage
    const STAGE_FIRST = 1; //第一阶段
    const STAGE_SECOND = 2; //第二阶段

    public static $stageList = [
        self::STAGE_FIRST  => '',
        self::STAGE_SECOND => '',
    ];
}
