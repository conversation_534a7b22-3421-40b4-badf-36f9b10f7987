<?php


namespace FlashExpress\bi\App\Models\backyard;


class FleetAuditRecordModel extends BackyardBaseModel
{
    protected $table_name = 'fleet_audit_record';

    private $id;
    private $audit_id;
    private $status;
    private $operator_id;
    private $operator_name;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getAuditId()
    {
        return $this->audit_id;
    }

    /**
     * @param mixed $audit_id
     */
    public function setAuditId($audit_id): void
    {
        $this->audit_id = $audit_id;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status): void
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getOperatorId()
    {
        return $this->operator_id;
    }

    /**
     * @param mixed $operator_id
     */
    public function setOperatorId($operator_id): void
    {
        $this->operator_id = $operator_id;
    }

    /**
     * @return mixed
     */
    public function getOperatorName()
    {
        return $this->operator_name;
    }

    /**
     * @param mixed $operator_name
     */
    public function setOperatorName($operator_name): void
    {
        $this->operator_name = $operator_name;
    }


}