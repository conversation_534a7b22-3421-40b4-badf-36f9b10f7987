<?php

namespace FlashExpress\bi\App\Models\backyard;

class AuditApprovalDelayModel extends BackyardBaseModel
{
    public $table_name = 'audit_approval_delay';

    private $date;
    private $biz_type;
    private $biz_value;
    private $insert_data;
    private $is_process;
    private $created_at;
    private $updated_at;

    /**
     * @return mixed
     */
    public function getDate()
    {
        return $this->date;
    }

    /**
     * @param mixed $date
     */
    public function setDate($date): void
    {
        $this->date = $date;
    }

    /**
     * @return mixed
     */
    public function getBizType()
    {
        return $this->biz_type;
    }

    /**
     * @param mixed $biz_type
     */
    public function setBizType($biz_type): void
    {
        $this->biz_type = $biz_type;
    }

    /**
     * @return mixed
     */
    public function getBizValue()
    {
        return $this->biz_value;
    }

    /**
     * @param mixed $biz_value
     */
    public function setBizValue($biz_value): void
    {
        $this->biz_value = $biz_value;
    }

    /**
     * @return mixed
     */
    public function getInsertData()
    {
        $insertData = [];
        if (!empty($this->insert_data)) {
            $insertData = json_decode($this->insert_data, true);
        }
        return $insertData;
    }

    /**
     * @param mixed $insert_data
     */
    public function setInsertData($insert_data): void
    {
        if (is_array($insert_data)) {
            $this->insert_data = json_encode($insert_data, JSON_UNESCAPED_UNICODE);
        } else {
            $this->insert_data = $insert_data;
        }
    }

    /**
     * @return mixed
     */
    public function getIsProcess()
    {
        return $this->is_process;
    }

    /**
     * @param mixed $is_process
     */
    public function setIsProcess($is_process): void
    {
        $this->is_process = $is_process;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param mixed $created_at
     */
    public function setCreatedAt($created_at): void
    {
        $this->created_at = $created_at;
    }

    /**
     * @return mixed
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param mixed $updated_at
     */
    public function setUpdatedAt($updated_at): void
    {
        $this->updated_at = $updated_at;
    }

    /**
     * 根据指定日期获取待处理数据
     * @param $date
     * @return array
     */
    public function getDataBySpecDate($date): array
    {
        $insertRows = $this->find([
            "conditions" => "date = :date:",
            "bind" => [
                'date' => $date
            ],
        ]);
        if (empty($insertRows)) {
            return [];
        }
        $result = [];
        foreach ($insertRows as $row) {
            $rowData = $row->toArray();
            $rowData['insert_data'] = $row->getInsertData();
            $result[] = $rowData;
        }
        return $result;
    }
}