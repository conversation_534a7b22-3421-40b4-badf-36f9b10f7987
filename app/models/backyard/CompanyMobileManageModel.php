<?php
namespace FlashExpress\bi\App\Models\backyard;


class CompanyMobileManageModel extends BackyardBaseModel
{
    protected $table_name = 'company_mobile_manage';

    //手机号状态
    const STATUS_WAIT_ACTIVE = 1 ;//待激活
    const STATUS_ACTIVE = 2; //激活
    const STATUS_NOT_ENABLED = 3;//未启用
    consT STATUS_TERMINATE_CONTRACT = 4;//终止合约
    CONST STATUS_FEEDBACK_HANDLING = 5;//异常处理中

    //手机号码更新SIM序列号状态
    CONST UPDATE_SIM_STATUS_WAITING = 1;
    CONST UPDATE_SIM_STATUS_UPDATED = 2;//已更新
    CONST UPDATE_SIM_STATUS_EMPTY = 3;
    const UPDATE_SIM_STATUS_LIST = [
        self::UPDATE_SIM_STATUS_EMPTY,
        self::UPDATE_SIM_STATUS_WAITING,
        self::UPDATE_SIM_STATUS_UPDATED,
    ];

}