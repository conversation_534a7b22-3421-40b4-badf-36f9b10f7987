<?php

namespace FlashExpress\bi\App\Models\backyard;

class HrStaffInsuranceBeneficiaryModel extends BackyardBaseModel
{
    protected $table_name = 'hr_staff_insurance_beneficiary';

    const RELATION_FATHER = 1;//父亲
    const RELATION_MOTHER = 2;//母亲
    const RELATION_SPOUSE = 3;//配偶
    const RELATION_CHILD = 4;//孩子
    const RELATION_OTHER = 5;//其他亲属-泰国没有
    const RELATION_BROTHER = 6;//哥哥
    const RELATION_SISTER = 7;//姐姐
    const RELATION_YOU_BROTHER = 8;//弟弟
    const RELATION_YOU_SISTER = 9;//妹妹

    public static $relation_list = [
        self::RELATION_FATHER      => 'insurance_beneficiary_relation_th_1',
        self::RELATION_MOTHER      => 'insurance_beneficiary_relation_th_2',
        self::RELATION_SPOUSE      => 'insurance_beneficiary_relation_th_3',
        self::RELATION_CHILD       => 'insurance_beneficiary_relation_th_4',
        self::RELATION_BROTHER     => 'insurance_beneficiary_relation_th_6',
        self::RELATION_SISTER      => 'insurance_beneficiary_relation_th_7',
        self::RELATION_YOU_BROTHER => 'insurance_beneficiary_relation_th_8',
        self::RELATION_YOU_SISTER  => 'insurance_beneficiary_relation_th_9',
    ];

    //婚姻状况
    const MARITA_SINGLE = 1;  //未婚
    const MARITA_MARRIED = 2; //已婚
    const MARITA_DIVORCE = 3; //离异
    const MARITA_BEREAVE = 4; //丧偶

    public static $marita_list = [
        self::MARITA_SINGLE  => 'marita_single',
        self::MARITA_MARRIED => 'marita_married',
        self::MARITA_DIVORCE => 'marita_divorce',
        self::MARITA_BEREAVE => 'marita_bereave',
    ];
}