<?php

namespace FlashExpress\bi\App\Models\backyard;

class HrOvertimeForLeaveModel extends BackyardBaseModel
{
    protected $table_name = 'hr_overtime_for_leave';

    private $id;
    private $overtime_id;
    private $staff_info_id;
    private $date_at;
    private $duration;
    private $state;
    private $deleted;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getOvertimeId()
    {
        return $this->overtime_id;
    }

    /**
     * @param mixed $overtime_id
     */
    public function setOvertimeId($overtime_id): void
    {
        $this->overtime_id = $overtime_id;
    }

    /**
     * @return mixed
     */
    public function getStaffInfoId()
    {
        return $this->staff_info_id;
    }

    /**
     * @param mixed $staff_info_id
     */
    public function setStaffInfoId($staff_info_id): void
    {
        $this->staff_info_id = $staff_info_id;
    }

    /**
     * @return mixed
     */
    public function getDuration()
    {
        return $this->duration;
    }

    /**
     * @param mixed $duration
     */
    public function setDuration($duration): void
    {
        $this->duration = $duration;
    }

    /**
     * @return mixed
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * @param mixed $state
     */
    public function setState($state): void
    {
        $this->state = $state;
    }

    /**
     * @return mixed
     */
    public function getDeleted()
    {
        return $this->deleted;
    }

    /**
     * @param mixed $deleted
     */
    public function setDeleted($deleted): void
    {
        $this->deleted = $deleted;
    }

    /**
     * @return mixed
     */
    public function getDateAt()
    {
        return $this->date_at;
    }

    /**
     * @param mixed $date_at
     */
    public function setDateAt($date_at): void
    {
        $this->date_at = $date_at;
    }
}