<?php


namespace FlashExpress\bi\App\Models\backyard;



class HrJobDepartmentRelationModel extends BackyardBaseModel
{
    //未超过预算
    const HC_BUDGET_STATE_NORMAL = 1;
    //超过预算
    const HC_BUDGET_STATE_OVERRUN = 2;
    const POSITION_TYPE_1 = 1;//一线操作
    const POSITION_TYPE_2 = 2;//一线职能
    const POSITION_TYPE_3 = 3;//总部职能

    public static $position_type_list = [
        self::POSITION_TYPE_1 => 'position_type_1',
        self::POSITION_TYPE_2 => 'position_type_2',
        self::POSITION_TYPE_3 => 'position_type_3',
    ];

    protected $table_name = 'hr_job_department_relation';

}