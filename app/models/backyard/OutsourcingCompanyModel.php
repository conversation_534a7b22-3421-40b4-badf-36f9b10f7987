<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/1/28
 * Time: 4:03 PM
 */


namespace FlashExpress\bi\App\Models\backyard;


class OutsourcingCompanyModel extends BackyardBaseModel
{
    protected $table_name = 'outsourcing_company';

    const IS_AGREEMENT_0 = 0;//未操作
    const IS_AGREEMENT_1 = 1;//同意
    const IS_AGREEMENT_2 = 2;//不同意

    //是否删除
    const DELETED_YES = 1;
    const DELETED_NO  = 0;
    /**
     * 通过电话号码获取单条信息
     * @param $phone
     * @return array
     */
    public function getOneByPhone($phone){
        $data = self::findFirst([
            'conditions' => 'company_phone = :company_phone:',
            'bind' => ['company_phone' => $phone],
        ]);
        return $data ? $data->toArray() : [];
    }

    /**
     * 通过电话号码获取单条信息
     * @param $phone
     * @return array
     */
    public function getOneById($id){
        $data = self::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id],
        ]);
        return $data ? $data->toArray() : [];
    }
}
