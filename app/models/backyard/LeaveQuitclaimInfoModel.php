<?php
/**
 * Author: Bruce
 * Date  : 2023-04-17 11:34
 * Description:
 */

namespace FlashExpress\bi\App\Models\backyard;

class LeaveQuitclaimInfoModel extends BackyardBaseModel
{
    protected $table_name = 'leave_quitclaim_info';

    const IS_MARRIED_NO  = 1;//单身
    const IS_MARRIED_YES = 2;//已婚
    const IS_MARRIED = [
        self::IS_MARRIED_NO  => 'married_1',
        self::IS_MARRIED_YES => 'married_2',
    ];

    //支付方式
    const PAYMENT_METHOD_UB    = 1;//UB flash payroll
    const PAYMENT_METHOD_OTHER = 2;//Other bank acount
    const PAYMENT_METHOD_GCASH = 3;//Gcash

    const PAYMENT_METHOD = [
        self::PAYMENT_METHOD_UB    => 'payment_method_ub',
        self::PAYMENT_METHOD_OTHER => 'payment_method_other',
        self::PAYMENT_METHOD_GCASH => 'payment_method_gcash',
    ];

    const AUDIT_STATUS_0 = 0;//未提交-未发送
    const AUDIT_STATUS_1 = 1;//待审批
    const AUDIT_STATUS_2 = 2;//已同意
    const AUDIT_STATUS_3 = 3;//已驳回
    const AUDIT_STATUS_4 = 4;//已发送-待回复

    const QUITCLAIM_AUDIT_STATUS = [
        self::AUDIT_STATUS_1 => 'quitclaim_audit_status_1',
        self::AUDIT_STATUS_2 => 'quitclaim_audit_status_2',
        self::AUDIT_STATUS_3 => 'quitclaim_audit_status_3',
        self::AUDIT_STATUS_4 => 'quitclaim_audit_status_4',
    ];

    //回复状态
    const REPLY_PENDING_SEND = 0;//待发送
    const REPLY_PENDING      = 1;//待回复
    const REPLY_YES          = 2;//已回复
    const REPLY_TIMEOUT      = 3;//回复超时
}