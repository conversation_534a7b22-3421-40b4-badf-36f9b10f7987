<?php

namespace FlashExpress\bi\App\Models\backyard;

/**
 * 员工人脸识别记录表模型
 * Class StaffWorkFaceVerifyRecordModel
 * @package FlashExpress\bi\App\Models\backyard
 */
class StaffWorkFaceVerifyRecordModel extends BackyardBaseModel
{
    protected $table_name = 'staff_work_face_verify_record';

    // 验证渠道常量
    const VERIFY_CHANNEL_ALI = 0;             // 阿里
    const VERIFY_CHANNEL_TENCENT = 1;         // 腾讯
    const VERIFY_CHANNEL_BAIDU = 2;           // 百度
    const VERIFY_CHANNEL_AI = 3;              // AI
    const VERIFY_CHANNEL_ORIGINAL = 4;        // 原图底片
    const VERIFY_CHANNEL_SILENT_LIVENESS = 5; // 静默活体

    // 组织类别常量
    const ORGANIZATION_TYPE_STORE = 1;        // 网点
    const ORGANIZATION_TYPE_DEPARTMENT = 2;   // 部门

    /**
     * 获取验证渠道文本
     * @param int $channel
     * @return string
     */
    public static function getVerifyChannelText($channel)
    {
        $channels = [
            self::VERIFY_CHANNEL_ALI             => '阿里',
            self::VERIFY_CHANNEL_TENCENT         => '腾讯',
            self::VERIFY_CHANNEL_BAIDU           => '百度',
            self::VERIFY_CHANNEL_AI              => 'AI',
            self::VERIFY_CHANNEL_ORIGINAL        => '原图底片',
            self::VERIFY_CHANNEL_SILENT_LIVENESS => '静默活体',
        ];
        return $channels[$channel] ?? '未知';
    }

    /**
     * 获取组织类型文本
     * @param int $type
     * @return string
     */
    public static function getOrganizationTypeText($type)
    {
        $types = [
            self::ORGANIZATION_TYPE_STORE      => '网点',
            self::ORGANIZATION_TYPE_DEPARTMENT => '部门',
        ];
        return $types[$type] ?? '未知';
    }
}
