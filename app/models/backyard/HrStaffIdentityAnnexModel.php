<?php


namespace FlashExpress\bi\App\Models\backyard;


class HrStaffIdentityAnnexModel extends BackyardBaseModel
{
    protected $table_name = 'hr_staff_identity_annex';

    /**
     * 根据员工工号获取信息
     * @param $staff_info_id
     * @return array
     */
    public function getOneByStaffId($staff_info_id){
        $identity_annex = self::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: ",
            'bind' => ['staff_info_id' => $staff_info_id]
        ]);
        return $identity_annex ? $identity_annex->toArray() : [];
    }
}