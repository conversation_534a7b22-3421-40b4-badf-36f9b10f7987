<?php

namespace FlashExpress\bi\App\Models\backyard;

class HrInternalBonusDetailsModel extends BackyardBaseModel
{
    // 候选人简历推荐来源
    const RECOMMEND_SOURCE_INTERNAL_RESUME = 1; // 内推简历
    const RECOMMEND_SOURCE_SHARE_POSTER = 2;    // 分享海报
    
    // 候选人简历状态
    const RESUME_STATUS_RECOMMENDED = 1; // 已推荐
    const RESUME_STATUS_HIRED = 2;       // 已入职
    const RESUME_STATUS_NOT_HIRED = 3;   // 未入职
    const RESUME_STATUS_RESIGNED = 4;    // 已离职
    
    const CANDIDATE_RESUME_STATUS_RECOMMENDED = 1;    // 已推荐(沟通中)
    const CANDIDATE_RESUME_STATUS_EMPLOYED = 2;       // 已入职
    const CANDIDATE_RESUME_STATUS_NOT_EMPLOYED = 3;   // 未入职
    const CANDIDATE_RESUME_STATUS_LEFT = 4;           // 已离职
    
    // 奖金状态
    const BONUS_STATUS_PENDING = 0;    // 未达到发放标准
    const BONUS_STATUS_QUALIFIED = 1;  // 达到1阶段发放标准
    const BONUS_STATUS_QUALIFIED_2 = 2;  // 达到2阶段发放标准
    
    // 简历推荐来源枚举
    public static $recommend_source_list = [
        self::RECOMMEND_SOURCE_INTERNAL_RESUME => '内推简历',
        self::RECOMMEND_SOURCE_SHARE_POSTER => '分享海报'
    ];
    
    // 简历状态枚举
    public static $resume_status_list = [
        self::RESUME_STATUS_RECOMMENDED => '已推荐',
        self::RESUME_STATUS_HIRED => '已入职',
        self::RESUME_STATUS_NOT_HIRED => '未入职',
        self::RESUME_STATUS_RESIGNED => '已离职'
    ];
    
    // 奖金状态枚举
    public static $bonus_status_list = [
        self::BONUS_STATUS_PENDING => '待发放',
        self::BONUS_STATUS_QUALIFIED => '达到发放标准'
    ];
    
    protected $table_name = 'hr_internal_bonus_details';
}