<?php

namespace FlashExpress\bi\App\Models\backyard;

use FlashExpress\bi\App\library\DateTime;

/**
 * @description 外协快递员人脸检测记录表
 */
class StaffWorkDetectFaceRecordModel extends  BackyardBaseModel
{
    protected $table_name = 'staff_work_detect_face_record';

    const MATCH_STATE_NOT_MATCH = 0; //未匹配到  (未匹配到 或者 符合分数界定值的数据)
    const MATCH_STATE_HAS_MATCH = 1; //已匹配到   (上班打卡时匹配)
    const MATCH_STATE_HAS_DETECTED = 2; //已检测到   (轮询检测时匹配 不符合 分数界定值的数据)

    const DETECT_FACE_RECORD_TYPE_OS = 1; //外协
    const DETECT_FACE_RECORD_TYPE_AGENT = 2; //个人代理-打卡
    const DETECT_FACE_RECORD_TYPE_COURIER_ATTACHMENT = 3; //正式（含月薪制）/个人代理-保存底片
    const DETECT_FACE_RECORD_TYPE_AGENT_CYCLE = 4; //个人代理-2小时检测

    /**
     * 非活体分支阀值
     */
    const NOT_LIVING_SCORE = 0.7;


    /**
     * @var int
     */
    private $staff_info_id;

    /**
     * @var string
     */
    private $organization_id;

    private $attendance_date;

    /**
     * @var int
     */
    private $match_staff_info_id;

    /**
     * @var
     */
    private $live_score;

    /**
     * @var int
     */
    private $state;

    /**
     * @var string
     */
    private $os_face_image_source_path;

    /**
     * @var string
     */
    private $os_submit_face_image_path;

    /**
     * @var string
     */
    private $work_attendance_path;

    private $created_at;
    private $related_id;


    private $updated_at;
    private $type;

    /**
     * @return mixed
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param mixed $type
     */
    public function setType($type): void
    {
        $this->type = $type;
    }

    /**
     * @return int
     */
    public function getStaffInfoId(): int
    {
        return $this->staff_info_id;
    }

    /**
     * @param int $staff_info_id
     */
    public function setStaffInfoId(int $staff_info_id): void
    {
        $this->staff_info_id = $staff_info_id;
    }

    /**
     * @return string
     */
    public function getOrganizationId(): string
    {
        return $this->organization_id;
    }

    /**
     * @param string $organization_id
     */
    public function setOrganizationId(string $organization_id): void
    {
        $this->organization_id = $organization_id;
    }

    /**
     * @return mixed
     */
    public function getAttendanceDate()
    {
        return $this->attendance_date;
    }

    /**
     * @param mixed $attendance_date
     */
    public function setAttendanceDate($attendance_date): void
    {
        $this->attendance_date = $attendance_date;
    }

    /**
     * @return int
     */
    public function getMatchStaffInfoId(): int
    {
        return $this->match_staff_info_id;
    }

    /**
     * @param int $match_staff_info_id
     */
    public function setMatchStaffInfoId(int $match_staff_info_id): void
    {
        $this->match_staff_info_id = $match_staff_info_id;
    }

    /**
     * @return int
     */
    public function getState(): int
    {
        return $this->state;
    }

    /**
     * @param int $state
     */
    public function setState(int $state): void
    {
        $this->state = $state;
    }

    /**
     * @return string
     */
    public function getOsFaceImageSourcePath(): string
    {
        return $this->os_face_image_source_path;
    }

    /**
     * @param string $os_face_image_source_path
     */
    public function setOsFaceImageSourcePath(string $os_face_image_source_path): void
    {
        $this->os_face_image_source_path = $os_face_image_source_path;
    }

    /**
     * @return string
     */
    public function getOsSubmitFaceImagePath(): string
    {
        return $this->os_submit_face_image_path;
    }

    /**
     * @param string $os_submit_face_image_path
     */
    public function setOsSubmitFaceImagePath(string $os_submit_face_image_path): void
    {
        $this->os_submit_face_image_path = $os_submit_face_image_path;
    }

    /**
     * @return string
     */
    public function getWorkAttendancePath(): string
    {
        return $this->work_attendance_path;
    }

    /**
     * @param string $work_attendance_path
     */
    public function setWorkAttendancePath(string $work_attendance_path): void
    {
        $this->work_attendance_path = $work_attendance_path;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param mixed $created_at
     */
    public function setCreatedAt($created_at): void
    {
        $this->created_at = $created_at;
    }
    /**
     * @param mixed $related_id
     */
    public function setRelatedId($related_id): void
    {
        $this->related_id = $related_id;
    }

    /**
     * @return mixed
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param mixed $updated_at
     */
    public function setUpdatedAt($updated_at): void
    {
        $this->updated_at = $updated_at;
    }

    /**
     * @param mixed $live_score
     */
    public function setLiveScore($live_score): void
    {
        $this->live_score = $live_score;
    }
}