<?php

namespace FlashExpress\bi\App\Models\backyard;

use FlashExpress\bi\App\Models\BaseModel;
class BackyardBaseModel extends BaseModel
{
    protected $table_name = null;
    const WRITE_DB_PHALCON_DI_NAME = 'db';
    const READ_DB_PHALCON_DI_NAME  = 'db_rby';

    public function initialize()
    {
        parent::initialize();
        $this->useDynamicUpdate(true);
    }

    /**
     * 需要强制更新的字段列表
     * 子类可以重写此方法来指定哪些字段需要强制更新
     * @return array
     */
    protected function getForceUpdateFields()
    {
        // 只对set_val字段进行强制更新，避免影响其他字段的正常逻辑
        return ['set_val'];
    }

    /**
     * 重写save方法，自动处理需要强制更新的字段
     * @description: 在不改动业务代码的情况下，自动处理指定字段的强制更新
     * @author: AI
     * @date: 2025-01-15
     * @param array $data 可选的数据数组
     * @param array $whiteList 可选的白名单数组
     * @return bool 保存是否成功
     */
    public function save($data = null, $whiteList = null)
    {
        // 获取需要强制更新的字段列表
        $forceUpdateFields = $this->getForceUpdateFields();

        // 检查并强制标记需要更新的字段
        foreach ($forceUpdateFields as $field) {
            if (isset($this->$field)) {
                $this->writeAttribute($field, $this->$field);
            }
        }

        // 调用父类的save方法
        return parent::save($data, $whiteList);
    }

    /**
     * 重写update方法，自动处理需要强制更新的字段
     * @description: 处理update方法中指定字段的更新问题
     * @author: AI
     * @date: 2025-01-15
     * @param array $data 要更新的数据
     * @param array $whiteList 可选的白名单数组
     * @return bool 更新是否成功
     */
    public function update($data = null, $whiteList = null)
    {
        // 获取需要强制更新的字段列表
        $forceUpdateFields = $this->getForceUpdateFields();

        // 如果数据中包含需要强制更新的字段，进行特殊处理
        if (is_array($data)) {
            foreach ($forceUpdateFields as $field) {
                if (isset($data[$field])) {
                    $this->writeAttribute($field, $data[$field]);
                }
            }
        }

        // 调用父类的update方法
        return parent::update($data, $whiteList);
    }

    /**
     *
     */
    public function onConstruct()
    {
        $this->setReadConnectionService(self::READ_DB_PHALCON_DI_NAME);
        $this->setWriteConnectionService(self::WRITE_DB_PHALCON_DI_NAME);
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService(self::WRITE_DB_PHALCON_DI_NAME);
        }
        $this->relations();
    }

    protected function relations()
    {

    }

    public function refresh()
    {
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService(self::WRITE_DB_PHALCON_DI_NAME);
        }
        return parent::refresh();
    }

    // 指定表名称
    public function getSource()
    {
        $className       = substr(static::class, strrpos(static::class, '\\') + 1);
        $classNamePrefix  = str_replace('Model', '', $className);
        $tableName       = strtolower(preg_replace('/(?<=[a-z])([A-Z])/', '_$1', $classNamePrefix));
        return $this->table_name ?? $tableName;
    }

    /**
     *父子数据组合
     * Created by: Lqz.
     * @param array $parent
     * @param array $children
     * @param string $parentFK
     * @param string $childrenFK
     * @param string $returnK
     * @param string $relationShip
     * @return array
     * CreateTime: 2020/8/14 0014 19:14
     */

    public static function combineParentAndChildren($parent = [], $children = [], $parentFK = 'id', $childrenFK = 'parent_id', $returnK = 'children', $relationShip = 'hasOne')
    {
        if (!in_array($relationShip, ['hasOne', 'hasMany'])) {
            throw  new \LogicException('The relationShip error');
        }
        if ($parent && $children) {
            foreach ($parent as $_k => $_p) {
                if (!isset($_p[$parentFK])) {
                    throw  new \LogicException('The parentFK error');
                }
                foreach ($children as $_c) {
                    if (!isset($_c[$childrenFK])) {
                        throw  new \LogicException('The childrenFK error');
                    }
                    if ($_p[$parentFK] == $_c[$childrenFK]) {
                        if ($relationShip == 'hasOne') {
                            $parent[$_k][$returnK] = $_c;
                        } else {
                            $parent[$_k][$returnK][] = $_c;
                        }
                    }
                }
            }
        }
        return $parent;
    }

    /**
     * 事务
     * Created by: Lqz.
     * @param $app
     * @return mixed
     * CreateTime: 2020/8/14 0014 19:15
     */
    public static function beginTransaction($app)
    {
        $db = $app->getDI()->get(self::WRITE_DB_PHALCON_DI_NAME);
        try {
            $db->begin();
        } catch (\Exception $e) {
            $app->getDI()->get("logger")->write_log("reconnect mysql...", "info");
            $db->connect();
            $db->begin();

        }
        return $db;
    }

    public function batch_insert(array $data , $db = self::WRITE_DB_PHALCON_DI_NAME){
        if (count($data) == 0) {
            throw new \LogicException('参数错误');
        }
        $keys = array_keys(reset($data));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql = "INSERT INTO " . $this->getSource() . " ({$keys}) VALUES ";
        foreach ($data as $v) {
            $v = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                } else {
                    $value = addslashes($value); //处理特殊符号，如单引号
                    return "'{$value}'";
                }
            }, $v);
            $values = implode(',', array_values($v));
            $sql .= " ({$values}), ";
        }
        $sql = rtrim(trim($sql), ',');
        //DI中注册的数据库服务名称为"db"
        $result = $this->getDI()->get($db)->execute($sql);
        if (!$result) {
            throw new \LogicException('批量入库记录');
        }
        return $result;
    }

    /**
     * @description:获取插入 sql
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/4/14 17:46
     */
    public function batch_insert_to_sql($tbl_name, array $info)
    {
        if (!is_array($info) || count($info) == 0) {
            return false;
        }
        $keys = array_keys(reset($info));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql = "INSERT INTO ".$tbl_name." ({$keys}) VALUES ";

        foreach ($info as $v) {
            $v = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                }

                $value = addslashes($value); //处理特殊符号，如单引号
                return "'{$value}'";
            }, $v);
            $values = implode(',', array_values($v));
            $sql .= " ({$values}), ";
        }
        $sql = rtrim(trim($sql), ',');
        return $sql;
    }


    /**
     * 获取归档历史表名 并判断是否存在
     *
     * @param $stat_date  时间
     * @param $table_name  表名
     * @param $table_name_date  追加到表名的时间格式
     * return "{$table_name}_{$table_date}";
     */
    public function is_get_table_name($stat_date, $table_name, $table_name_date = "Y")
    {
        $table_date = date($table_name_date, strtotime($stat_date));
        $new_table  = "{$table_name}_{$table_date}";
        $db         = $this->getDI()->get('db');
        $logger     = $this->getDI()->get('logger');

        try {
            $insert  = "show tables like '{$new_table}'";
            $info = $db->query($insert)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if(empty($info)){
                return false; //代表表不存在
            }
        } catch (\Exception $e) {
            $logger->write_log("查询 by库数据归档，日期：" . $stat_date . "，{$new_table} 失败:" . $e->getMessage());
            return false; //代表表不存在
        }
        return $new_table;
    }

    /**
     * 创建归档历史表名
     *
     * @param $stat_date  时间
     * @param $table_name  表名
     * @param $table_name_date  追加到表名的时间格式
     * return "{$table_name}_{$table_date}";
     */
    public function create_table_name($stat_date, $table_name, $table_name_date = "Y")
    {
        $table_date = date($table_name_date, strtotime($stat_date));
        $new_table  = "{$table_name}_{$table_date}";
        $db         = $this->getDI()->get('db');
        $logger     = $this->getDI()->get('logger');

        try {
            $insert  = "show tables like '{$new_table}'";
            $info = $db->query($insert)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (empty($info)) {
                $create_table = "create table {$new_table} like {$table_name};";
                $db->execute($create_table);
                echo "by库数据归档，日期：" . $stat_date . "，{$new_table} 建表成功".PHP_EOL;
                $logger->write_log("by库数据归档，日期：" . $stat_date . "，{$new_table} 建表成功", 'info');
            }else{
                echo "by库数据归档，日期：" . $stat_date . "，{$new_table} 表已存在".PHP_EOL;
                $logger->write_log("by库数据归档，日期：" . $stat_date . "，{$new_table} 表已存在", 'info');
            }
        } catch (\Exception $e) {
            $logger->write_log("by库数据归档，日期：" . $stat_date . "，{$new_table} 建表失败:" . $e->getMessage());
            return '0';
        }
        return $new_table;
    }
}