<?php

namespace FlashExpress\bi\App\Models\backyard;

use FlashExpress\bi\App\Models\BaseModel;
class BackyardBaseModel extends BaseModel
{
    protected $table_name = null;
    const WRITE_DB_PHALCON_DI_NAME = 'db';
    const READ_DB_PHALCON_DI_NAME  = 'db_rby';

    public function initialize()
    {
        parent::initialize();
        $this->useDynamicUpdate(true);
    }

    /**
     * 是否启用全字段强制更新模式
     * 子类可以重写此方法来控制是否对所有被设置的字段进行强制更新
     * @return bool
     */
    protected function isForceUpdateAllFields()
    {
        // 默认启用全字段强制更新模式
        return true;
    }

    /**
     * 需要强制更新的特定字段列表（仅在非全字段模式下生效）
     * 子类可以重写此方法来指定哪些字段需要强制更新
     * @return array
     */
    protected function getForceUpdateFields()
    {
        // 当全字段模式关闭时，只对这些字段进行强制更新
        return ['set_val', 'status', 'state', 'is_active', 'enabled'];
    }

    /**
     * 获取模型的所有字段名
     * @return array
     */
    protected function getAllModelFields()
    {
        // 获取模型的元数据
        $metaData = $this->getModelsMetaData();
        // 获取所有字段名
        return $metaData->getAttributes($this);
    }

    /**
     * 获取当前被设置/修改的字段列表
     * @return array
     */
    protected function getChangedFields()
    {
        $changedFields = [];
        $allFields = $this->getAllModelFields();

        foreach ($allFields as $field) {
            // 检查字段是否被设置（不管值是否相同）
            if (property_exists($this, $field) && isset($this->$field)) {
                $changedFields[] = $field;
            }
        }

        return $changedFields;
    }

    /**
     * 重写save方法，自动处理需要强制更新的字段
     * @description: 在不改动业务代码的情况下，自动处理所有被设置字段的强制更新
     * @author: AI
     * @date: 2025-01-15
     * @param array $data 可选的数据数组
     * @param array $whiteList 可选的白名单数组
     * @return bool 保存是否成功
     */
    public function save($data = null, $whiteList = null)
    {
        if ($this->isForceUpdateAllFields()) {
            // 全字段强制更新模式：对所有被设置的字段进行强制更新
            $fieldsToUpdate = $this->getChangedFields();
        } else {
            // 特定字段模式：只对指定字段进行强制更新
            $fieldsToUpdate = $this->getForceUpdateFields();
        }

        // 强制标记字段为已修改
        foreach ($fieldsToUpdate as $field) {
            if (isset($this->$field)) {
                $this->writeAttribute($field, $this->$field);
            }
        }

        // 调用父类的save方法
        return parent::save($data, $whiteList);
    }

    /**
     * 重写update方法，自动处理需要强制更新的字段
     * @description: 处理update方法中字段的更新问题
     * @author: AI
     * @date: 2025-01-15
     * @param array $data 要更新的数据
     * @param array $whiteList 可选的白名单数组
     * @return bool 更新是否成功
     */
    public function update($data = null, $whiteList = null)
    {
        // 如果传入了数据数组，处理数组中的字段
        if (is_array($data)) {
            if ($this->isForceUpdateAllFields()) {
                // 全字段模式：对数据数组中的所有字段进行强制更新
                foreach ($data as $field => $value) {
                    $this->writeAttribute($field, $value);
                }
            } else {
                // 特定字段模式：只对指定字段进行强制更新
                $forceUpdateFields = $this->getForceUpdateFields();
                foreach ($forceUpdateFields as $field) {
                    if (isset($data[$field])) {
                        $this->writeAttribute($field, $data[$field]);
                    }
                }
            }
        } else {
            // 没有传入数据数组，处理模型属性
            if ($this->isForceUpdateAllFields()) {
                $fieldsToUpdate = $this->getChangedFields();
            } else {
                $fieldsToUpdate = $this->getForceUpdateFields();
            }

            foreach ($fieldsToUpdate as $field) {
                if (isset($this->$field)) {
                    $this->writeAttribute($field, $this->$field);
                }
            }
        }

        // 调用父类的update方法
        return parent::update($data, $whiteList);
    }

    /**
     *
     */
    public function onConstruct()
    {
        $this->setReadConnectionService(self::READ_DB_PHALCON_DI_NAME);
        $this->setWriteConnectionService(self::WRITE_DB_PHALCON_DI_NAME);
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService(self::WRITE_DB_PHALCON_DI_NAME);
        }
        $this->relations();
    }

    protected function relations()
    {

    }

    public function refresh()
    {
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService(self::WRITE_DB_PHALCON_DI_NAME);
        }
        return parent::refresh();
    }

    // 指定表名称
    public function getSource()
    {
        $className       = substr(static::class, strrpos(static::class, '\\') + 1);
        $classNamePrefix  = str_replace('Model', '', $className);
        $tableName       = strtolower(preg_replace('/(?<=[a-z])([A-Z])/', '_$1', $classNamePrefix));
        return $this->table_name ?? $tableName;
    }

    /**
     *父子数据组合
     * Created by: Lqz.
     * @param array $parent
     * @param array $children
     * @param string $parentFK
     * @param string $childrenFK
     * @param string $returnK
     * @param string $relationShip
     * @return array
     * CreateTime: 2020/8/14 0014 19:14
     */

    public static function combineParentAndChildren($parent = [], $children = [], $parentFK = 'id', $childrenFK = 'parent_id', $returnK = 'children', $relationShip = 'hasOne')
    {
        if (!in_array($relationShip, ['hasOne', 'hasMany'])) {
            throw  new \LogicException('The relationShip error');
        }
        if ($parent && $children) {
            foreach ($parent as $_k => $_p) {
                if (!isset($_p[$parentFK])) {
                    throw  new \LogicException('The parentFK error');
                }
                foreach ($children as $_c) {
                    if (!isset($_c[$childrenFK])) {
                        throw  new \LogicException('The childrenFK error');
                    }
                    if ($_p[$parentFK] == $_c[$childrenFK]) {
                        if ($relationShip == 'hasOne') {
                            $parent[$_k][$returnK] = $_c;
                        } else {
                            $parent[$_k][$returnK][] = $_c;
                        }
                    }
                }
            }
        }
        return $parent;
    }

    /**
     * 事务
     * Created by: Lqz.
     * @param $app
     * @return mixed
     * CreateTime: 2020/8/14 0014 19:15
     */
    public static function beginTransaction($app)
    {
        $db = $app->getDI()->get(self::WRITE_DB_PHALCON_DI_NAME);
        try {
            $db->begin();
        } catch (\Exception $e) {
            $app->getDI()->get("logger")->write_log("reconnect mysql...", "info");
            $db->connect();
            $db->begin();

        }
        return $db;
    }

    public function batch_insert(array $data , $db = self::WRITE_DB_PHALCON_DI_NAME){
        if (count($data) == 0) {
            throw new \LogicException('参数错误');
        }
        $keys = array_keys(reset($data));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql = "INSERT INTO " . $this->getSource() . " ({$keys}) VALUES ";
        foreach ($data as $v) {
            $v = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                } else {
                    $value = addslashes($value); //处理特殊符号，如单引号
                    return "'{$value}'";
                }
            }, $v);
            $values = implode(',', array_values($v));
            $sql .= " ({$values}), ";
        }
        $sql = rtrim(trim($sql), ',');
        //DI中注册的数据库服务名称为"db"
        $result = $this->getDI()->get($db)->execute($sql);
        if (!$result) {
            throw new \LogicException('批量入库记录');
        }
        return $result;
    }

    /**
     * @description:获取插入 sql
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/4/14 17:46
     */
    public function batch_insert_to_sql($tbl_name, array $info)
    {
        if (!is_array($info) || count($info) == 0) {
            return false;
        }
        $keys = array_keys(reset($info));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql = "INSERT INTO ".$tbl_name." ({$keys}) VALUES ";

        foreach ($info as $v) {
            $v = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                }

                $value = addslashes($value); //处理特殊符号，如单引号
                return "'{$value}'";
            }, $v);
            $values = implode(',', array_values($v));
            $sql .= " ({$values}), ";
        }
        $sql = rtrim(trim($sql), ',');
        return $sql;
    }


    /**
     * 获取归档历史表名 并判断是否存在
     *
     * @param $stat_date  时间
     * @param $table_name  表名
     * @param $table_name_date  追加到表名的时间格式
     * return "{$table_name}_{$table_date}";
     */
    public function is_get_table_name($stat_date, $table_name, $table_name_date = "Y")
    {
        $table_date = date($table_name_date, strtotime($stat_date));
        $new_table  = "{$table_name}_{$table_date}";
        $db         = $this->getDI()->get('db');
        $logger     = $this->getDI()->get('logger');

        try {
            $insert  = "show tables like '{$new_table}'";
            $info = $db->query($insert)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if(empty($info)){
                return false; //代表表不存在
            }
        } catch (\Exception $e) {
            $logger->write_log("查询 by库数据归档，日期：" . $stat_date . "，{$new_table} 失败:" . $e->getMessage());
            return false; //代表表不存在
        }
        return $new_table;
    }

    /**
     * 创建归档历史表名
     *
     * @param $stat_date  时间
     * @param $table_name  表名
     * @param $table_name_date  追加到表名的时间格式
     * return "{$table_name}_{$table_date}";
     */
    public function create_table_name($stat_date, $table_name, $table_name_date = "Y")
    {
        $table_date = date($table_name_date, strtotime($stat_date));
        $new_table  = "{$table_name}_{$table_date}";
        $db         = $this->getDI()->get('db');
        $logger     = $this->getDI()->get('logger');

        try {
            $insert  = "show tables like '{$new_table}'";
            $info = $db->query($insert)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (empty($info)) {
                $create_table = "create table {$new_table} like {$table_name};";
                $db->execute($create_table);
                echo "by库数据归档，日期：" . $stat_date . "，{$new_table} 建表成功".PHP_EOL;
                $logger->write_log("by库数据归档，日期：" . $stat_date . "，{$new_table} 建表成功", 'info');
            }else{
                echo "by库数据归档，日期：" . $stat_date . "，{$new_table} 表已存在".PHP_EOL;
                $logger->write_log("by库数据归档，日期：" . $stat_date . "，{$new_table} 表已存在", 'info');
            }
        } catch (\Exception $e) {
            $logger->write_log("by库数据归档，日期：" . $stat_date . "，{$new_table} 建表失败:" . $e->getMessage());
            return '0';
        }
        return $new_table;
    }
}