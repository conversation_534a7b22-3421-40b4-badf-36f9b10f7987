<?php


namespace FlashExpress\bi\App\Models\backyard;


class StaffLeaveRemainDaysModel  extends BackyardBaseModel
{
    protected $table_name = 'staff_leave_remaining_days';

    // 子类型枚举常量 - 家人去世假子类型
    const SUB_LEAVE_TYPE_FAMILY_DEATH_FATHER = 1;        // 父亲去世
    const SUB_LEAVE_TYPE_FAMILY_DEATH_MOTHER = 2;        // 母亲去世
    const SUB_LEAVE_TYPE_FAMILY_DEATH_SPOUSE = 3;        // 配偶去世
    const SUB_LEAVE_TYPE_FAMILY_DEATH_SPOUSE_FATHER = 4; // 配偶父亲去世
    const SUB_LEAVE_TYPE_FAMILY_DEATH_SPOUSE_MOTHER = 5; // 配偶母亲去世
    const SUB_LEAVE_TYPE_FAMILY_DEATH_CHILD = 6;         // 子女去世

    // 默认子类型
    const SUB_LEAVE_TYPE_DEFAULT = 0; // 默认无子类型

    /**
     * 获取可重复申请的子类型
     * @return array
     */
    public static function getRepeatableSubTypes()
    {
        return [
            self::SUB_LEAVE_TYPE_FAMILY_DEATH_CHILD, // 子女去世可以多次申请
        ];
    }

    /**
     * 获取只能申请一次的子类型
     * @return array
     */
    public static function getOnceOnlySubTypes()
    {
        return [
            self::SUB_LEAVE_TYPE_FAMILY_DEATH_FATHER,        // 父亲去世
            self::SUB_LEAVE_TYPE_FAMILY_DEATH_MOTHER,        // 母亲去世
            self::SUB_LEAVE_TYPE_FAMILY_DEATH_SPOUSE,        // 配偶去世
            self::SUB_LEAVE_TYPE_FAMILY_DEATH_SPOUSE_FATHER, // 配偶父亲去世
            self::SUB_LEAVE_TYPE_FAMILY_DEATH_SPOUSE_MOTHER, // 配偶母亲去世
        ];
    }

    /**
     * 获取子类型名称映射
     * @return array
     */
    public static function getSubTypeNames()
    {
        return [
            self::SUB_LEAVE_TYPE_FAMILY_DEATH_FATHER        => 'father',
            self::SUB_LEAVE_TYPE_FAMILY_DEATH_MOTHER        => 'mother',
            self::SUB_LEAVE_TYPE_FAMILY_DEATH_SPOUSE        => 'spouse',
            self::SUB_LEAVE_TYPE_FAMILY_DEATH_SPOUSE_FATHER => 'spouse_father',
            self::SUB_LEAVE_TYPE_FAMILY_DEATH_SPOUSE_MOTHER => 'spouse_mother',
            self::SUB_LEAVE_TYPE_FAMILY_DEATH_CHILD         => 'child',
        ];
    }
}
