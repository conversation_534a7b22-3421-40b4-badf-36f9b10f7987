<?php

namespace FlashExpress\bi\App\Models\backyard;

use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;

class SettingEnvModel  extends BackyardBaseModel
{
    protected $table_name = 'setting_env';

    /**
     * 重写save方法，解决set_val字段相同值不更新的问题
     * @description: 在不改动业务代码的情况下，自动处理set_val字段的强制更新
     * @author: AI
     * @date: 2025-01-15
     * @param array $data 可选的数据数组
     * @param array $whiteList 可选的白名单数组
     * @return bool 保存是否成功
     */
    public function save($data = null, $whiteList = null)
    {
        // 检查是否有set_val字段需要特殊处理
        if (isset($this->set_val)) {
            // 强制标记set_val字段为已修改，确保能够更新
            $this->writeAttribute('set_val', $this->set_val);
        }

        // 调用父类的save方法
        return parent::save($data, $whiteList);
    }

    /**
     * 重写update方法，确保set_val字段能够正常更新
     * @description: 处理update方法中的set_val字段更新问题
     * @author: AI
     * @date: 2025-01-15
     * @param array $data 要更新的数据
     * @param array $whiteList 可选的白名单数组
     * @return bool 更新是否成功
     */
    public function update($data = null, $whiteList = null)
    {
        // 如果数据中包含set_val字段，进行特殊处理
        if (is_array($data) && isset($data['set_val'])) {
            $this->writeAttribute('set_val', $data['set_val']);
        }

        // 调用父类的update方法
        return parent::update($data, $whiteList);
    }
}
