<?php

namespace FlashExpress\bi\App\Models\backyard;

use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;

class SettingEnvModel  extends BackyardBaseModel
{
    protected $table_name = 'setting_env';

    /**
     * 自定义强制更新模式
     * @description: SettingEnvModel使用全字段强制更新模式
     * @return bool
     */
    protected function isForceUpdateAllFields()
    {
        // 启用全字段强制更新模式
        return true;
    }

    /**
     * 自定义需要强制更新的字段（当全字段模式关闭时生效）
     * @description: 如果关闭全字段模式，只对这些字段进行强制更新
     * @return array
     */
    protected function getForceUpdateFields()
    {
        // 如果不使用全字段模式，至少要强制更新set_val字段
        return ['set_val', 'description', 'updated_at'];
    }
}
