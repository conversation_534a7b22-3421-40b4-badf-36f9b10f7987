<?php
namespace FlashExpress\bi\App\Models\backyard;

/**
 * 转正评估-目标设置主表
 */
class HrProbationTargetModel extends BackyardBaseModel
{
    protected $table_name = 'hr_probation_target';

    //设置状态 - setting_state
    const SETTING_STATE_FINISH = 1;             //已完成
    const SETTING_STATE_NOT_START = 2;          //未开始
    const SETTING_STATE_ADJUST = 3;             //已调整


    //发送状态 - send_state
    const SEND_STATE_FINISH = 1;                //已完成
    const SEND_STATE_NOT_SEND = 2;              //未发送

    const SEND_STATE_ADJUST_FINISH = 3;         //调整后已发送

    //签字状态 - sign_state
    const SIGN_STATE_FINISH = 1;                //已完成
    const SIGN_STATE_NOT_START = 2;             //未开始
    const SIGN_STATE_WAIT = 3;                  //进行中

    const SIGN_STATE_ADJUST_NOT_START = 4;      //调整后未开始
    const SIGN_STATE_ADJUST_WAIT = 5;           //调整后进行中
    const SIGN_STATE_ADJUST_FINISH = 6;         //调整后已完成


    const WIDGET_FINIS = 100;
    /**
     * 设置状态枚举
     * @var array
     */
    public static $setting_state_list = [
        self::SETTING_STATE_FINISH    => 'probation_setting_state_finish',
        self::SETTING_STATE_NOT_START => 'probation_setting_state_not_start',
        self::SETTING_STATE_ADJUST    => 'probation_setting_state_adjust',
    ];

    /**
     * 发送状态枚举
     * @var array
     */
    public static $send_state_list = [
        self::SEND_STATE_NOT_SEND      => 'probation_send_state_not_send',
        self::SEND_STATE_FINISH        => 'probation_send_state_finish',
        self::SEND_STATE_ADJUST_FINISH => 'probation_send_state_adjust_finish', // 调整后已发送
    ];

    /**
     * 签字状态枚举
     * @var array
     */
    public static $sign_state_list = [
        self::SIGN_STATE_NOT_START        => 'probation_sign_state_not_start',
        self::SIGN_STATE_WAIT             => 'probation_sign_state_wait',
        self::SIGN_STATE_FINISH           => 'probation_sign_state_finish',
        self::SIGN_STATE_ADJUST_NOT_START => 'probation_sign_state_adjust_not_start',   // 调整后未开始
        self::SIGN_STATE_ADJUST_WAIT      => 'probation_sign_state_adjust_wait',        // 调整后进行中
        self::SIGN_STATE_ADJUST_FINISH    => 'probation_sign_state_adjust_finish',      // 调整后已完成
    ];

    //TAB - 选项
    const TAB_SETTING_STATE_FINISH = 1;
    const TAB_SETTING_STATE_NOT_START = 2;

    //TAB - 选项列表
    public static $tab_setting_state_list = [
        self::TAB_SETTING_STATE_NOT_START => 'probation_tab_setting_state_not_start',
        self::TAB_SETTING_STATE_FINISH    => 'probation_tab_setting_state_finish',
    ];
}
