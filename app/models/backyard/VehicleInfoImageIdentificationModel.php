<?php

namespace FlashExpress\bi\App\Models\backyard;


class VehicleInfoImageIdentificationModel extends BackyardBaseModel
{
    /**
     * 图片类型image_type
     */
    const DRIVING_LICENCE_TYPE  = 1; //驾驶证
    const REG_CER_TYPE   = 2;  //车辆登记证书图片
    const VEHICLE_CER_TYPE  =3 ; //车辆税证明图片

    private $id;

    private $vehicle_info_id;

    private $uid;

    private $image_type;

    private $image_url;

    private $post_paramers;

    private $request_id;

    private $result;

    private $state;

    private $created_at;

    private $updated_at;

    protected $table_name = 'vehicle_info_image_identification';

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getVehicleInfoId()
    {
        return $this->vehicle_info_id;
    }

    /**
     * @param mixed $vehicle_info_id
     */
    public function setVehicleInfoId($vehicle_info_id): void
    {
        $this->vehicle_info_id = $vehicle_info_id;
    }

    /**
     * @return mixed
     */
    public function getUid()
    {
        return $this->uid;
    }

    /**
     * @param mixed $uid
     */
    public function setUid($uid): void
    {
        $this->uid = $uid;
    }

    /**
     * @return mixed
     */
    public function getImageType()
    {
        return $this->image_type;
    }

    /**
     * @param mixed $image_type
     */
    public function setImageType($image_type): void
    {
        $this->image_type = $image_type;
    }

    /**
     * @return mixed
     */
    public function getImageUrl()
    {
        return $this->image_url;
    }

    /**
     * @param mixed $image_url
     */
    public function setImageUrl($image_url): void
    {
        $this->image_url = $image_url;
    }

    /**
     * @return mixed
     */
    public function getPostParamers()
    {
        return $this->post_paramers;
    }

    /**
     * @param mixed $post_paramers
     */
    public function setPostParamers($post_paramers): void
    {
        $this->post_paramers = $post_paramers;
    }

    /**
     * @return mixed
     */
    public function getResult()
    {
        return $this->result;
    }

    /**
     * @param mixed $result
     */
    public function setResult($result): void
    {
        $this->result = $result;
    }

    /**
     * @return mixed
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * @param mixed $state
     */
    public function setState($state): void
    {
        $this->state = $state;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param mixed $created_at
     */
    public function setCreatedAt($created_at): void
    {
        $this->created_at = $created_at;
    }

    /**
     * @return mixed
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param mixed $updated_at
     */
    public function setUpdatedAt($updated_at): void
    {
        $this->updated_at = $updated_at;
    }

    /**
     * @param mixed $request_id
     */
    public function setRequestId($request_id)
    {
        $this->request_id = $request_id;
    }

    /**
     * @return mixed
     */
    public function getRequestId()
    {
        return $this->request_id;
    }
}