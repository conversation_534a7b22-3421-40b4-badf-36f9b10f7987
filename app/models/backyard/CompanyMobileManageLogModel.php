<?php
namespace FlashExpress\bi\App\Models\backyard;


class CompanyMobileManageLogModel extends BackyardBaseModel
{
    protected $table_name = 'company_mobile_manage_log';

    const TYPE_ADD = 1;             //新增
    const TYPE_IMPORT_ADD = 2;      //导入新增
    const TYPE_EDIT = 3;            //编辑
    const TYPE_IMPORT_EDIT = 4;     //导入编辑
    const TYPE_ACTIVE = 5;          //激活
    const TYPE_STOP = 6;            //停用
    const TYPE_STAFF_NEW_NUMBER = 7;//员工发起申请新号码
    const TYPE_STAFF_UPDATE_SIM = 8;//员工发起更换SIM卡
    const TYPE_STAFF_FEEDBACK = 9;//企业号码异常反馈

}