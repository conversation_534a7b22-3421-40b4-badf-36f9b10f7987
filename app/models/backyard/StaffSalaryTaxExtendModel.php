<?php

namespace FlashExpress\bi\App\Models\backyard;

/**
 * 员工薪资税扩展表
 */
class StaffSalaryTaxExtendModel extends BackyardBaseModel
{
    protected $table_name = 'staff_salary_tax_extend';

    const TAX_INFO_CATEGORY_EMPLOYEE = 1;//本人信息
    const TAX_INFO_CATEGORY_EMPLOYEE_SPOUSE = 2;//配偶信息
    const TAX_INFO_CATEGORY_CHILD_UNDER_18 = 3;//18岁以下孩子信息(不含残疾)
    const TAX_INFO_CATEGORY_CHILD_OVER_18_STUDY = 4;//18岁及以上在读孩子信息(不含残疾)
    const TAX_INFO_CATEGORY_CHILD_OVER_18_END = 5;//18岁以上全日制文凭以上(马来)或学士以上(马来外)(不含残疾)
    const TAX_INFO_CATEGORY_CHILD_UNDER_18_DISABLED = 6;//残疾儿童
    const TAX_INFO_CATEGORY_CHILD_DISABLED_AND_STUDYING = 7;//就读文凭或更高级别(马来)/学位或同等学历(马来境外)的残疾孩子
}