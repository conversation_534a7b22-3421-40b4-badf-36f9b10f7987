<?php
namespace FlashExpress\bi\App\Models\backyard;
class WorkflowNodePushModel extends BackyardBaseModel
{
	protected $table_name = 'workflow_node_push';
	public  $status_1=1;//待处理
	public  $status_2=2;//已处理
	
	
	public function getWaitPushList($offset=0,$limit=1000){
		//这里读取主库
		$this->setWriteConnectionService(self::WRITE_DB_PHALCON_DI_NAME);
		
		return self::find([
			                            'conditions' => 'status = :status:',
			                            'bind'       => [
				                            'status'=> $this->status_1,
			                            ],
			                            'limit'      => $limit,
			                            'offset'     => $offset,
			                            'order' => 'id desc',
		                            ])->toArray();
	}
}