<?php


namespace FlashExpress\bi\App\Models\backyard;


use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;

class WorkflowNodeRelateModel extends BackyardBaseModel
{
    protected $table_name = 'workflow_node_relate';
    /**
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|string
     */
    public $flow_id;
    /**
     * @var mixed|\Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|null
     */
    public $from_node_id;
    /**
     * @var mixed|\Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|null
     */
    public $to_node_id;
    /**
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     */
    public $valuate_formula;
    /**
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     */
    public $valuate_code;
    /**
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     */
    public $remark;
    /**
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     */
    public $sort;
}