<?php


namespace FlashExpress\bi\App\Models\backyard;

class HrEconomyAbilityModel extends BackyardBaseModel
{
    protected $table_name = 'hr_economy_ability';
    //驾照类型：1-公共驾驶执照，2-私人驾驶执照,3-B，4-B2，5-GDL，6-CDL
    const DRIVING_LICENSE_TYPE_PUBLIC  = 1;
    const DRIVING_LICENSE_TYPE_PRIVATE = 2;
    const DRIVING_LICENSE_TYPE_B       = 3;
    const DRIVING_LICENSE_TYPE_B2      = 4;
    const DRIVING_LICENSE_TYPE_GDL     = 5;
    const DRIVING_LICENSE_TYPE_CDL     = 6;


    public static $driving_license_type_list = [
        self::DRIVING_LICENSE_TYPE_PUBLIC  => 'public driving license',
        self::DRIVING_LICENSE_TYPE_PRIVATE => 'private driving license',
        self::DRIVING_LICENSE_TYPE_B       => 'B',
        self::DRIVING_LICENSE_TYPE_B2      => 'B2',
        self::DRIVING_LICENSE_TYPE_GDL     => 'GDL',
        self::DRIVING_LICENSE_TYPE_CDL     => 'CDL',
    ];

}
