<?php

namespace FlashExpress\bi\App\Models\backyard;

class HrPenaltyDetailModel extends BackyardBaseModel
{
    protected $table_name = 'hr_penalty_detail';
    const PENALTY_REASON_COMING_LATE = 1;//迟到
    const PENALTY_REASON_LEAVE_EARLY = 2;//早退
    const PENALTY_REASON_AB = 3;//旷工

    const EXPIRY_REASON_LEAVE = 4;//请假期
    const EXPIRY_REASON_MAKE_UP = 3;//补卡
    const EXPIRY_REASON_NEW_PENALTY = 2;
    const EXPIRY_REASON_HAS_APPEAL = 1; //已申诉

    const PENALTY_STATE_TAKE_EFFECT = 1; //生效中
    const PENALTY_STATE_INVALID = 2;     //已失效

    /**
     * @description 获取处罚原因、翻译映射
     * @return array
     */
    public static function getPenaltyReasonMap()
    {
        return [
            self::PENALTY_REASON_COMING_LATE => "hr_penalty_reason_1",
            self::PENALTY_REASON_LEAVE_EARLY => "hr_penalty_reason_2",
            self::PENALTY_REASON_AB          => "hr_penalty_reason_3",
        ];
    }

    /**
     * @description 获取处罚失效原因、翻译映射
     * @return array
     */
    public static function getPenaltyExpireReasonMap()
    {
        return [
            self::EXPIRY_REASON_HAS_APPEAL  => "hr_penalty_expire_reason_1",
            self::EXPIRY_REASON_NEW_PENALTY => "hr_penalty_expire_reason_2",
            self::EXPIRY_REASON_MAKE_UP     => "hr_penalty_expire_reason_3",
            self::EXPIRY_REASON_LEAVE       => "hr_penalty_expire_reason_4",
            ];
    }
}