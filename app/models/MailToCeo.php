<?php

namespace FlashExpress\bi\App\Models;

class MailToCeo extends BaseModel
{

    /**
     *
     * @var integer
     */
    public $id;

    /**
     *
     * @var integer
     */
    public $staff_id;

    /**
     *
     * @var string
     */
    public $staff_name;

    /**
     *
     * @var string
     */
    public $content;

    /**
     *
     * @var string
     */
    public $mobile;

    /**
     *
     * @var integer
     */
    public $type;

    /**
     *
     * @var integer
     */
    public $is_reply;

    /**
     *
     * @var string
     */
    public $create_time;

    /**
     *
     * @var integer
     */
    public $is_read;

    /**
     *
     * @var string
     */
    public $img_url;

    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        //$this->setSchema("backyard");
        $this->setSource("mail_to_ceo");
    }

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSource()
    {
        return 'mail_to_ceo';
    }


}
