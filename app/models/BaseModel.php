<?php

namespace FlashExpress\bi\App\Models;

use FlashExpress\bi\App\Core\PhalBaseModel;

/**
 * 基类model
 */
class BaseModel extends PhalBaseModel implements \ArrayAccess
{

    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 处理查询参数中的bind数组
     * @param mixed $parameters
     * @return mixed
     */
    protected static function processBindArrays($parameters)
    {
        if (!is_array($parameters)) {
            return $parameters;
        }

        if (isset($parameters['bind']) && is_array($parameters['bind'])) {
            foreach ($parameters['bind'] as $key => $value) {
                if (is_array($value)) {
                    // 检查数组索引是否连续
                    $keys         = array_keys($value);
                    $expectedKeys = range(0, count($value) - 1);
                    if ($keys !== $expectedKeys) {
                        $parameters['bind'][$key] = array_values($value);
                    }
                }
            }
        }

        return $parameters;
    }

    /**
     * 重写find方法，参数绑定数组 索引非连续 重置
     * @param mixed $parameters
     */
    public static function find($parameters = null)
    {
        // 处理bind参数中的数组
        $parameters = static::processBindArrays($parameters);
        return parent::find($parameters);
    }

    /**
     * 重写findFirst方法，参数绑定数组 索引非连续 重置
     * @param mixed $parameters
     */
    public static function findFirst($parameters = null)
    {
        // 处理bind参数中的数组
        $parameters = static::processBindArrays($parameters);
        return parent::findFirst($parameters);
    }


    /**
     * @inheritDoc
     */
    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }

    /**
     * @inheritDoc
     */
    public function offsetGet($offset)
    {
        return $this->$offset;
    }

    /**
     * @inheritDoc
     */
    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }

    /**
     * @inheritDoc
     */
    public function offsetUnset($offset)
    {
        unset($this->$offset);
    }

    /**
     * 迁移hcm方法
     * @param array $data
     * @return bool
     * @throws \Exception
     */
    public function insert_record(array $data): bool
    {
        if (!is_array($data) || count($data) == 0) {
            return false;
        }
        $result = $this->create($data);
        if (!$result) {
            throw new \Exception(implode(',', $this->getMessages()));
        }
        return $result;
    }
}
