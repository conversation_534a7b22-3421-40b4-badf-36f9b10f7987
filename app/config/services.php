<?php

use FlashExpress\bi\App\Repository\AccessTokenRepository;
use FlashExpress\bi\App\Repository\AuthCodeRepository;
use FlashExpress\bi\App\Repository\ClientRepository;
use FlashExpress\bi\App\Repository\RefreshTokenRepository;
use FlashExpress\bi\App\Repository\ScopeRepository;
use FlashExpress\bi\App\Repository\UserRepository;
use League\OAuth2\Server\CryptKey;
use Phalcon\Crypt;
use Phalcon\Flash\Direct as Flash;
use Phalcon\Http\Response\Cookies;
use Phalcon\Mvc\Model\Metadata\Memory as MetaDataAdapter;
use Phalcon\Mvc\Url as UrlResolver;
use Phalcon\Mvc\View;
use Phalcon\Mvc\View\Engine\Php as PhpEngine;
use Phalcon\Mvc\View\Engine\Volt as VoltEngine;
use Phalcon\Session\Adapter\Files as SessionAdapter;
use Phalcon\Di\FactoryDefault;
use Phalcon\Mvc\Model\MetaData\Strategy\Introspection;
use Phalcon\Mvc\Model\MetaData\Redis as RedisMetaData;
use Phalcon\Mvc\Model\MetaData\Files as FileMetaData;
use Phalcon\Mvc\Model\MetaData\Memory as MemoryMetaData;


/**
 * The FactoryDefault Dependency Injector automatically registers
 * the services that provide a full stack framework.
 */
$di = new FactoryDefault();

//注入：共享配置文件
$di->setShared('config', function () {
    return include APP_PATH . "/config/config.php";
});

//注入：URL组件
$di->setShared('url', function () {
    $config = $this->getConfig(); //使用DI里注册的config服务
    $url = new UrlResolver();
    $url->setBaseUri($config->application->baseUri);
    $url->setStaticBaseUri($config->application->staticUri);
    return $url;
});

$di->setShared('dispatcher', function (){
    $eventsManager = new \Phalcon\Events\Manager();

    
    //$eventsManager->attach('dispatch', new \FlashExpress\bi\App\Plugins\DispatchPlugin());

    $eventsManager->attach('dispatch', new \FlashExpress\bi\App\Plugins\CORSPlugin());
    /**
     * Check if the user is allowed to access certain action using the SecurityPlugin
     */
    // $eventsManager->attach('dispatch:beforeExecuteRoute', new \FlashExpress\bi\App\Plugins\SecurityPlugin());

    /**
     * Handle exceptions and not-found exceptions using ExceptionPlugin
     */
    $eventsManager->attach('dispatch', new \FlashExpress\bi\App\Plugins\ExceptionPlugin());

    $dispatcher = new \Phalcon\Mvc\Dispatcher();
    $dispatcher->setEventsManager($eventsManager);

    return $dispatcher;
});

/**
 * Setting up router
 */
$di->setShared('router', function (){
    $router = include APP_PATH . "/config/router.php";;

    return $router;
});

$di->setShared('request', function (){
    $request = new \FlashExpress\bi\App\Core\PhalBaseRequest();
    return $request;
});

$di->setShared('languagePack', function (){
    $languagePack = new \FlashExpress\bi\App\library\LanguagePack();

    return $languagePack;
});


$di->setShared('response', function (){
    $response = new \FlashExpress\bi\App\Core\PhalBaseResponse();
    return $response;
});

/**
 * Setting up the view component
 */
$di->setShared('view', function () {
    $config = $this->getConfig();

    $view = new View();
    $view->setDI($this);
    // Disable several levels
    $view->disableLevel(
        [
            View::LEVEL_LAYOUT => true,
            View::LEVEL_MAIN_LAYOUT => true,
        ]
    );
    $view->setViewsDir($config->application->viewsDir);

    $view->registerEngines([
        '.phtml' => function ($view) {
            $config = $this->getConfig();
            $volt = new VoltEngine($view, $this);
            $volt->setOptions([
                'compiledPath' => $config->application->cacheDir . 'volt/',
                'compiledSeparator' => '_',
                'compileAlways' => true, //通常情况下，出于性能方面的考虑，Volt模板引擎在重新编译模板时只会检查子模板中的内容变更。 所以建议设置Volt模板引擎的选项参数 'compileAlways' => true。这样模板会实时编译，并检查父模板中的内容变更
            ]);

            return $volt;
        },

    ]);

    return $view;
});

//region 多库
$di->set('profiler', function () {
    return new \Phalcon\Db\Profiler();
}, true);

$di->setShared('db', function () use ($di) {
    //新建一个事件管理器
    $eventsManager = new \Phalcon\Events\Manager();
    //只在非线上环境才记录sql日志
    if (in_array(RUNTIME, ['dev', 'test'])) {
        //从di中获取共享的profiler实例 分析底层sql性能，并记录日志
        $profiler = $di->getProfiler();
        $eventsManager->attach('db', function ($event, $connection) use ($profiler, $di) {
            if ($event->getType() == 'beforeQuery') {
                //在sql发送到数据库前启动分析
                $profiler->startProfile($connection->getSQLStatement());
            }
            if ($event->getType() == 'afterQuery') {
                //在sql执行完毕后停止分析
                $profiler->stopProfile();
                //获取分析结果
                $profile = $profiler->getLastProfile();
                $sql = $profile->getSQLStatement();
                $params = $connection->getSqlVariables();

                (is_array($params) && count($params)) && $params = json_encode($params);
                !$params  && $params='';
                $executeTime = $profile->getTotalElapsedSeconds();

                //记录sql日志
                $logger = $di->get('logger');
                $sql = preg_replace('/\s+|--/','  ', $sql);
                $logger->write_log("database:db_default {$sql} {$params} {$executeTime}", 'info');
            }

        });
    }

    $config = $this->getConfig();
    $params = [
        'host' => $config->database->host,
        'username' => $config->database->username,
        'password' => $config->database->password,
        'dbname' => $config->database->dbname,
        'charset' => $config->database->charset,
        'persistent' => true
    ];

    if ($config->database->adapter == 'Postgresql') {
        unset($params['charset']);
    }

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class($params);

    if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
        $connection->setEventsManager($eventsManager);
    }

    return $connection;
});

$di->setShared('db_rby', function () use ($di) {
    //新建一个事件管理器
    $eventsManager = new \Phalcon\Events\Manager();
    //只在非线上环境才记录sql日志
    if (in_array(RUNTIME, ['dev', 'test'])) {
        //从di中获取共享的profiler实例 分析底层sql性能，并记录日志
        $profiler = $di->getProfiler();
        $eventsManager->attach('db', function ($event, $connection) use ($profiler, $di) {
            if ($event->getType() == 'beforeQuery') {
                //在sql发送到数据库前启动分析
                $profiler->startProfile($connection->getSQLStatement());
            }
            if ($event->getType() == 'afterQuery') {
                //在sql执行完毕后停止分析
                $profiler->stopProfile();
                //获取分析结果
                $profile = $profiler->getLastProfile();
                $sql = $profile->getSQLStatement();
                $params = $connection->getSqlVariables();

                (is_array($params) && count($params)) && $params = json_encode($params);
                !$params  && $params='';
                $executeTime = $profile->getTotalElapsedSeconds();

                //记录sql日志
                $logger = $di->get('logger');
                $sql = preg_replace('/\s+|--/','  ', $sql);
                $logger->write_log("database:db_default {$sql} {$params} {$executeTime}", 'info');
            }

        });
    }

    $config = $this->getConfig();
    $params = [
        'host' => $config->database_rby->host,
        'username' => $config->database_rby->username,
        'password' => $config->database_rby->password,
        'dbname' => $config->database_rby->dbname,
        'charset' => $config->database_rby->charset,
        'persistent' => true
    ];

    if ($config->database->adapter == 'Postgresql') {
        unset($params['charset']);
    }

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class($params);

    if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
        $connection->setEventsManager($eventsManager);
    }

    return $connection;
});
$di->setShared('db_fle', function () use ($di) {
    //新建一个事件管理器
    $eventsManager = new \Phalcon\Events\Manager();
    //只在非线上环境才记录sql日志
    if (in_array(RUNTIME, ['dev'])) {
        $profiler = $di->getProfiler(); //从di中获取共享的profiler实例
        $eventsManager->attach('db', function ($event, $connection) use ($profiler, $di) {

            if ($event->getType() == 'beforeQuery') {
                //在sql发送到数据库前启动分析
                $profiler->startProfile($connection->getSQLStatement());
            }
            if ($event->getType() == 'afterQuery') {
                //在sql执行完毕后停止分析
                $profiler->stopProfile();

                //获取分析结果
                $profile = $profiler->getLastProfile();
                $sql = $profile->getSQLStatement();
                $params = $connection->getSqlVariables();
                (is_array($params) && count($params)) && $params = json_encode($params);
                $executeTime = $profile->getTotalElapsedSeconds();
                //记录sql日志
                $logger = $di->get('logger');
                $logger->write_log("database:db_fle {$sql} {$params} {$executeTime}", 'info');
            }

        });
    }

    $config = $this->getConfig();
    $params = [
        'host' => $config->database_fle->host,
        'username' => $config->database_fle->username,
        'password' => $config->database_fle->password,
        'dbname' => $config->database_fle->dbname,
        'charset' => $config->database_fle->charset,
        'persistent' => true,
    ];

    if ($config->database_fle->adapter == 'Postgresql') {
        unset($params['charset']);
    }

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database_fle->adapter;
    $connection = new $class($params);

    if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
        $connection->setEventsManager($eventsManager);
    }
    return $connection;
});

$di->setShared('db_bi', function () use ($di) {
    //新建一个事件管理器
    $eventsManager = new \Phalcon\Events\Manager();
    //只在非线上环境才记录sql日志
    if (in_array(RUNTIME, ['dev'])) {
        $profiler = $di->getProfiler(); //从di中获取共享的profiler实例
        $eventsManager->attach('db', function ($event, $connection) use ($profiler, $di) {

            if ($event->getType() == 'beforeQuery') {
                //在sql发送到数据库前启动分析
                $profiler->startProfile($connection->getSQLStatement());
            }
            if ($event->getType() == 'afterQuery') {
                //在sql执行完毕后停止分析
                $profiler->stopProfile();

                //获取分析结果
                $profile = $profiler->getLastProfile();
                $sql = $profile->getSQLStatement();
                $params = $connection->getSqlVariables();
                (is_array($params) && count($params)) && $params = json_encode($params);
                $executeTime = $profile->getTotalElapsedSeconds();
                //记录sql日志
                $logger = $di->get('logger');
                $logger->write_log("database:db_bi {$sql} {$params} {$executeTime}", 'info');
            }

        });
    }

    $config = $this->getConfig();
    $params = [
        'host' => $config->database_bi->host,
        'username' => $config->database_bi->username,
        'password' => $config->database_bi->password,
        'dbname' => $config->database_bi->dbname,
        'charset' => $config->database_bi->charset,
        'persistent' => true,
    ];

    if ($config->database_fle->adapter == 'Postgresql') {
        unset($params['charset']);
    }

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database_fle->adapter;
    $connection = new $class($params);

    if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
        $connection->setEventsManager($eventsManager);
    }
    return $connection;
});

$di->setShared('db_rbi', function () use ($di) {

    $config = $this->getConfig();
    $params = [
        'host' => $config->database_rbi->host,
        'username' => $config->database_rbi->username,
        'password' => $config->database_rbi->password,
        'dbname' => $config->database_rbi->dbname,
        'charset' => $config->database_rbi->charset,
        'persistent' => true,
    ];

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class($params);


    return $connection;
});

$di->setShared('db_rnl', function () use ($di) {

    $config = $this->getConfig();
    $params = [
        'host' => $config->database_rnl->host,
        'username' => $config->database_rnl->username,
        'password' => $config->database_rnl->password,
        'dbname' => $config->database_rnl->dbname,
        'charset' => $config->database_rnl->charset,
    ];

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class($params);


    return $connection;
});

$di->setShared('db_oa', function () use ($di) {

    $config = $this->getConfig();
    $params = [
        'host' => $config->database_oa->host,
        'username' => $config->database_oa->username,
        'password' => $config->database_oa->password,
        'dbname' => $config->database_oa->dbname,
        'charset' => $config->database_oa->charset,
        'persistent' => true,
    ];

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class($params);

    return $connection;
});

$di->setShared('db_msg', function () use ($di) {

    $config = $this->getConfig();
    $params = [
        'host' => $config->database_rbi->host,
        'username' => $config->database_rbi->username,
        'password' => $config->database_rbi->password,
        'dbname' => $config->database_rbi->dbname,
        'charset' => $config->database_rbi->charset,
    ];

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class($params);


    return $connection;
});

//消息表 合并到coupon
$di->setShared('db_coupon', function () use ($di) {

    $config = $this->getConfig();
    $params = [
        'host' => $config->database_coupon->host,
        'username' => $config->database_coupon->username,
        'password' => $config->database_coupon->password,
        'dbname' => $config->database_coupon->dbname,
        'charset' => $config->database_coupon->charset,
    ];

    if (env('runtime') != 'dev') {
        $params['persistent'] = true;
    }

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class($params);


    return $connection;
});


//coupon 从库
$di->setShared('db_coupon_r', function () use ($di) {

    $config = $this->getConfig();
    $params = [
        'host' => env('database_coupon_r_host'),
        'username' => env('database_coupon_r_username'),
        'password' => env('database_coupon_r_password'),
        'dbname' => env('database_coupon_r_dbname'),
        'charset' => env('database_coupon_r_charset'),
    ];

    if (env('runtime') != 'dev') {
        $params['persistent'] = true;
    }

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class($params);


    return $connection;
});

/**
 * xxljob
 */
$di->setShared('db_xxljob', function () use ($di) {

    $config = $this->getConfig();
    $params = [
        'host' => env('database_xxljob_host','*************'),
        'port' => env('database_xxljob_port','3306'),
        'username' => env('database_xxljob_username','th_flash_xxl_rw'),
        'password' => env('database_xxljob_password','th_flahsh456'),
        'dbname' => env('database_xxljob_dbname','th_dev_flashhr_xxl_job'),
        'charset' => env('database_xxljob_charset','utf8mb4'),
    ];
    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class($params);
    return $connection;
});

//ads
$di->setShared('db_adb', function () use ($di) {

    $config = $this->getConfig();

    //ADS数据库开关，环境变量配置=true，将切断ads数据库连接
    $ads_switch = $config->database_adb->ads_switch;
    if($ads_switch){
        $logger = $di->get('logger');
        $logger->write_log("database:db_adb 降级，切断BY系统 ADS数据库连接", 'error');
        return false;
    }

    //新建一个事件管理器
    $eventsManager = new \Phalcon\Events\Manager();
    if (in_array(RUNTIME, ['dev', 'test'])) {
        //从di中获取共享的profiler实例 分析底层sql性能，并记录日志
        $profiler = $di->getProfiler();
        $eventsManager->attach('db', function ($event, $connection) use ($profiler, $di) {
            if ($event->getType() == 'beforeQuery') {
                //在sql发送到数据库前启动分析
                $profiler->startProfile($connection->getSQLStatement());
            }
            if ($event->getType() == 'afterQuery') {
                //在sql执行完毕后停止分析
                $profiler->stopProfile();
                //获取分析结果
                $profile = $profiler->getLastProfile();
                $sql = $profile->getSQLStatement();
                $params = $connection->getSqlVariables();

                (is_array($params) && count($params)) && $params = json_encode($params);
                !$params  && $params='';
                $executeTime = $profile->getTotalElapsedSeconds();

                //记录sql日志
                $logger = $di->get('logger');
                $logger->write_log("database:db_default {$sql} {$params} {$executeTime}", 'info');
            }

        });
    }

    if(RUNTIME == 'pro'){
        $params = [
            'host' => $config->database_adb->host,
            'username' => $config->database_adb->username,
            'password' => $config->database_adb->password,
            'dbname' => $config->database_adb->dbname,
            'charset' => $config->database_adb->charset,
        ];
    }else{
        $params = [
            'host' => $config->database_fle->host,
            'username' => $config->database_fle->username,
            'password' => $config->database_fle->password,
            'dbname' => $config->database_fle->dbname,
            'charset' => $config->database_fle->charset,
        ];
    }

    if ($config->database->adapter == 'Postgresql') {
        unset($params['charset']);
    }

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class($params);

    if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
        $connection->setEventsManager($eventsManager);
    }

    return $connection;
});

/**
 * Start the session the first time some component request the session service
 * 实例化session并且开始 赋值给DI实例 方便在控制器中调用
 */
$di->setShared('session', function () {
    $session = new SessionAdapter();
    $session->start();
    return $session;
});

/**
 * Register the session flash service with the Twitter Bootstrap classes
 */
$di->set('flash', function () {
    return new Flash([
        'error' => 'alert alert-danger',
        'success' => 'alert alert-success',
        'notice' => 'alert alert-info',
        'warning' => 'alert alert-warning',
    ]);
});
/**
 * DI注册cookies服务
 */
$di->set('cookies', function () {
    $cookies = new \Phalcon\Http\Response\Cookies();
    $cookies->useEncryption(false);
    return $cookies;
});
//使用cookies加密的话，必须在“crypt”服务中设置一个全局的key：
$di->set(
    "crypt",
    function () {
        $crypt = new Crypt();
        $crypt->setKey('flashexpress123qwe!@#QWE');
        return $crypt;
    }
);
/**
 * DI注册日志服务
 */
$di->setShared('logger', function () use ($di) {
    $config = $this->getConfig(); //使用DI里注册的config服务
    $day = date('Ymd');
    $logPath = $config->application->runtimeDir . 'log/';
    if (!is_dir($logPath)) {
        mkdir($logPath,0755,true);
    }
    $logger = new \FlashExpress\bi\App\Core\PhalBaseLogger($config->application->runtimeDir . "log/log_{$day}.log");
    return $logger;
});
/**
 * DI注册自定义验证器
 */
$di->setShared('validator', function () use ($di) {
    $validator = new \FlashExpress\bi\App\Library\Validator($di);
    return $validator;
});
/**
 * DI注册过滤器
 */
$di->setShared('filter', function () use ($di) {
    $filter = new \FlashExpress\bi\App\Core\PhalBaseFilter($di);
    $filter->init();
    return $filter;
});

/**
 * DI注册Redis服务
 */
$di->setShared('redis', function () {
    $config = $this->getConfig();
    $frontCache = new Phalcon\Cache\Frontend\Data(["lifetime" => $config->redis->lifetime,]);
    $cache = new \FlashExpress\bi\App\Core\PhalBaseRedis(
        $frontCache,
        [
            'prefix' => $config->redis->prefix,
            'host' => $config->redis->host,
            'port' => $config->redis->port,
            'auth' => $config->redis->auth,
            //'statsKey' => true,
            'persistent' => false
        ]
    );
    return $cache;
});
$di->setShared('redisLib', function () {
    $config = $this->getConfig();
    $cache = new Redis();
    $cache->connect($config->redis->host, $config->redis->port);
    $cache->auth($config->redis->auth);
    $cache->setOption(Redis::OPT_PREFIX, $config->redis->prefix);
    return $cache;
});
/**
 * DI注册缓存服务
 */
$di->setShared('cache', function () {
    $config = $this->getConfig();
    return new \Phalcon\Cache\Backend\File(
        new Phalcon\Cache\Frontend\Data(
            [
                "lifetime" => 60,
            ]
        ),
        array(
            'cacheDir' => $config->application->cacheDir,
        )
    );
});
/**
 * 注册mongodb服务
 */
$di->setShared('mongodb', function () {
    $config = $this->getConfig();
    $cache = (new \FlashExpress\bi\App\library\Mongodb([
        'host' => $config->mongodb->host.':'.$config->mongodb->port,
        'username' => $config->mongodb->user,
        'password' => $config->mongodb->pwd,
        'db' => $config->mongodb->db,
    ]));
    return $cache;
});


/**
 *
 */
$di->setShared('oauth2Server', function () {
    $config = $this->getConfig();
    $clientRepository = new ClientRepository();
    $scopeRepository = new ScopeRepository();
    $accessTokenRepository = new AccessTokenRepository();
    $userRepository = new UserRepository();
    $refreshTokenRepository = new RefreshTokenRepository();
    $authCodeRepository = new AuthCodeRepository();

    $priKey = new CryptKey(BASE_PATH.getenv('PRIVATE_KEY_PATH'), null, false);

    // Setup the authorization server
    $server = new \League\OAuth2\Server\AuthorizationServer(
        $clientRepository,
        $accessTokenRepository,
        $scopeRepository,
        $priKey,
        env('ENCRYPTION_KEY')
//        new App\Library\OAuth2\TokenResponse()
    );

    $authCodeGrant = new \League\OAuth2\Server\Grant\AuthCodeGrant(
        $authCodeRepository,
        $refreshTokenRepository,
        new DateInterval($config->oauth->auth_code_lifespan)
    );
    $authCodeGrant->setRefreshTokenTTL(new DateInterval($config->oauth->refresh_token_lifespan));
    // Enable the authentication code grant on the server
    $server->enableGrantType($authCodeGrant, new DateInterval($config->oauth->access_token_lifespan));

    $refreshTokenGrant = new \League\OAuth2\Server\Grant\RefreshTokenGrant($refreshTokenRepository);
    $refreshTokenGrant->setRefreshTokenTTL(new DateInterval($config->oauth->refresh_token_lifespan));
    // Enable the refresh token grant on the server
    $server->enableGrantType($refreshTokenGrant, new DateInterval($config->oauth->access_token_lifespan));

    $passwordGrant = new \League\OAuth2\Server\Grant\PasswordGrant($userRepository, $refreshTokenRepository);
    $passwordGrant->setRefreshTokenTTL(new DateInterval($config->oauth->refresh_token_lifespan));
    // Enable the password grant on the server
    $server->enableGrantType($passwordGrant, new DateInterval($config->oauth->access_token_lifespan));

    $clientCredentialGrant = new \League\OAuth2\Server\Grant\ClientCredentialsGrant();
    $clientCredentialGrant->setRefreshTokenTTL(new DateInterval($config->oauth->refresh_token_lifespan));
    // Enable the client credentials grant on the server
    $server->enableGrantType($clientCredentialGrant, new DateInterval($config->oauth->access_token_lifespan));

    return $server;
});

/**
 *
 */
$di->setShared('resourceServer', function () {
    $config = $this->getConfig();
    $accessTokenRepository = new AccessTokenRepository(); // instance of AccessTokenRepositoryInterface
    $pubKey = new CryptKey(BASE_PATH.env('PUBLIC_KEY_PATH'), null, false);

    $server = new \League\OAuth2\Server\ResourceServer(
        $accessTokenRepository,
        $pubKey
//        new App\Library\OAuth2\TokenValidator($accessTokenRepository,env('ENCRYPTION_KEY'),new DateInterval($config->oauth->access_token_lifespan))
    );

    return $server;
});
$di->setShared(
    'modelsMetadata',
    function () use ($di) {
        if (env('runtime') == 'pro') {
            $config   = $this->getConfig();
            $metadata = new FileMetaData(
                [
                    'metaDataDir'=>$config->application->cacheDir,
                ]
            );
            $metadata->setStrategy(new Introspection());

        } else {
            $metadata = new MemoryMetaData();
        }
        return $metadata;
    }
);

/**
 * 注册模型管理器和强制更新插件
 * @description: 为所有模型添加强制更新功能，解决useDynamicUpdate模式下相同值不更新的问题
 * @author: AI
 * @date: 2025-01-15
 */
$di->setShared('modelsManager', function () {
    $eventsManager = new \Phalcon\Events\Manager();

    // 注册强制更新插件
    $forceUpdatePlugin = new \FlashExpress\bi\App\Plugins\ForceUpdatePlugin();
    $eventsManager->attach('model', $forceUpdatePlugin);

    $modelsManager = new \Phalcon\Mvc\Model\Manager();
    $modelsManager->setEventsManager($eventsManager);

    return $modelsManager;
});