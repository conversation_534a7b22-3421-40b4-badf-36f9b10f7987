<?php


namespace FlashExpress\bi\App\Interfaces;


interface AuditInterface
{
    /**
     * 获取申请详情
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom);

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user);

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true);

    /**
     * 样例
     * from_node_id | to_node_id | valuate_formula | valuate_code
     * -------------+------------+-----------------+-------------
     *      4       |     5      |    $p1 == 4     | getSubmitterDepartment
     *
     * 表示当提交人的部门为4时，审批节点4的下一个节点是5
     * 需要在 getWorkflowParams 中返回申请人所在的部门字段
     *
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null);

    /**
     * @description 设置auditDetailRequest对象
     * @param AuditDetailRequest $auditDetailRequestObj
     * @return mixed
     */
    public function setAuditDetailRequest(AuditDetailRequest $auditDetailRequestObj);

    /**
     * @description 获取auditDetailRequest对象
     * @return mixed
     */
    public function getAuditDetailRequest();
}