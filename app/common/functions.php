<?php


if(!function_exists('cal_days_in_month'))
{
    function cal_days_in_month($calendar, $month, $year)
    {
        return date('t', mktime(0, 0, 0, $month, 1, $year));
    }
}

if(!function_exists('checkParam'))
{

    function checkParam($param, $paramIn = [])
    {
        $arr = [];
        foreach($param as $k => $y)
        {
            if(isset($paramIn[$y]) && !empty($paramIn[$y]))
            {
                $arr[$y] = $paramIn[$y];
            }
        }
        return $arr;
    }
}

if (!function_exists('post_curl')) {

    function post_curl($url,$post_data,$headers=[],$lang = 'zh-CN',$from = 'backyard')
    {
        $url .= 'lang='.$lang.'&'.'from='.$from;
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLINFO_HEADER_OUT, TRUE);
        if ($headers){
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        }

        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
        $data = curl_exec($curl);

        curl_close($curl);
        return $data;
    }
}
if (!function_exists('feishu_push')) {
    function feishu_push($message_content,$url)
    {
        if (RUNTIME != 'pro' || empty($message_content) || empty($url)) {
            return;
        }
        $message['msg_type']        = 'text';
        $message['content']['text'] = $message_content;
        $ch                         = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_exec($ch);
        curl_close($ch);
    }
}


if (!function_exists('get_curl')) {

    function get_curl($url,$headers=[],$lang = 'zh-CN',$from = 'backyard')
    {
        $url .= 'lang='.$lang.'&'.'from='.$from;
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_HTTPGET, true);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);
        if ($headers){
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        }
        $data = curl_exec($curl);
        curl_close($curl);
        return $data;
    }
}

/**
 * 分页数据返回
 * @Access  public
 * @Param   request
 * @Return  array
 */
if(!function_exists('returnPageData'))
{
    function returnPageData()
    {

    }
}

// 方法废弃
//if (!function_exists('wLog')) {
//    function wLog($title = '', $message, $model = '', $fileName = 'inputLog')
//    {
//        // 记录日志
//        if (!empty($message)) {
//            if (is_array($message)) {
//                $message = json_encode($message);
//            }
//            $msg = '[' . date('Y-m-d H:i:s', time()) . ']'. "\n".'[' . $title . '][' . $model . '] ' . $message . "\n";
//            $wLogger = new \Phalcon\Logger\Adapter\File(APP_PATH . "/runtime/log/" . $fileName . "_" . date('Ymd') . ".log");
//            $wLogger->DEBUG($msg);
//        }
//    }
//}

/**
 * 用户配置脚手架
 * @Access  public
 * @Param   request
 * @Return  array
 */
if(!function_exists('UC'))
{
    function UC($key = null)
    {
        if(is_null($key))
        {
            return app('config');
        }
        return app($key);
    }
}

if(!function_exists('app'))
{
    function app($abstract = '')
    {
        if(empty($abstract))
        {
            return config_path($abstract, 'config');
        } else
        {
            return config_path($abstract);
        }
    }
}

if(!function_exists('config_path'))
{
    function config_path($key = '',$dir ='uconfig')
    {
        return include APP_PATH . "/".$dir."/".$key.".php";
    }
}

if(!function_exists('checkIsBetweenTime'))
{
    /**
     * 判断当前的时分是否在指定的时间段内
     * @param $start 开始时分  eg:10:30:00
     * @param $end  结束时分   eg:15:30:00
     * @author:mzc
     * @date:2018/8/9 10:46
     * @return: bool  1：在范围内，0:没在范围内
     */
    function checkIsBetweenTime($date)
    {
        $start       = '09:00';
        $end         = '18:00';
        $curTime     = strtotime($date);//当前时分
        $assignTime1 = strtotime($start);//获得指定分钟时间戳，00:00
        $assignTime2 = strtotime($end);//获得指定分钟时间戳，01:00
        $result      = 0;
        if($curTime < $assignTime1 || $curTime > $assignTime2)
        {
            $result = 1;
        }
        return $result;
    }
}

if(!function_exists('isDatetime'))
{
    /**
     * 判断时间格式是否正确
     * @param string $param 输入的时间
     * @param string $format 指定的时间格式
     * @return boolean
     */
    function isDatetime($param = '', $format = 'H:i')
    {
        return date($format, strtotime($param)) === $param;
    }
}

if(!function_exists('curlJsonRpc'))
{
    function curlJsonRpc($url = '', $data = '')
    {
        $ch = curl_init();
        $url = $url.'?tm='.time().mt_rand (100,999); //参数是为了防止缓存
        $options = array(
            CURLOPT_URL => $url,
            CURLOPT_USERAGENT => "Mozilla/5.0 (Windows NT 6.1; rv:12.0) Gecko/20100101 Firefox/12.0",
            CURLOPT_RETURNTRANSFER => 1,
        );
        curl_setopt_array($ch, $options);
        #JSON 示例数据
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        $html=curl_exec($ch);
        $info = curl_getinfo($ch);            #获取请求的详细信息 用于调试
        curl_close($ch);
        return json_decode($html,true);
    }
}

if(!function_exists('curlJsonRpcStr'))
{
    function curlJsonRpcStr($mobile, $msg , $src = 'backyard', $type= 0)
    {
        //$type = 0是普通短信 ，= 1是验证码 ， =2是提醒短信 ，=3是推广短信
        $params = ['mobile' => $mobile, 'msg' => $msg, 'code' => 'th', 'delay' => 0, 'type' => $type];
        //viber
        if (isCountry('PH')) {
            $params['nation']           = 'PH';
            $params['type']             = 0;
            $params['service_provider'] = 9;
        }
        $array = [
            'jsonrpc' => '2.0',
            'method'  => 'send',
            'params'  => [
                ['locale' => 'th', 'src' => $src],
                $params,
            ],
            'id'      => molten_get_traceid(),
        ];
        return json_encode($array, JSON_UNESCAPED_UNICODE);
    }
}

if(!function_exists('verificationCode'))
{
    /**
     * 验证码
     * @Access  public
     * @Param   request
     * @Return  array
     */
    function verificationCode()
    {
        $key = '';
        $pattern='**********';
        for( $i=0; $i<6; $i++ ) {
            $key .= $pattern[mt_rand(0, 9)];
        }
        return $key;
    }
}

if(!function_exists('getDateFromRange'))
{
    function getDateFromRange($startDate, $endDate)
    {

        $sTimeTamp = strtotime($startDate);
        $eTimeTamp = strtotime($endDate);

        // 计算日期段内有多少天
        $days = ($eTimeTamp - $sTimeTamp) / 86400 + 1;

        // 保存每天日期
        $date = [];

        for($i = 0; $i < $days; $i++)
        {
            $date[] = date('Y-m-d', $sTimeTamp + (86400 * $i));
        }

        return $date;
    }
}


if(!function_exists('is_weekend')) {
    function is_weekend($date){
        if ((date('w', strtotime($date)) == 6) || (date('w', strtotime($date)) == 0)) {
            return 1;
        } else {
            return 0;
        }
    }
}



if(!function_exists('filter_param')) {
    function filter_param($array){
        if(empty($array))
            return $array;
        if(!is_array($array))
            return strip_tags(addcslashes(stripslashes($array),"'"));//增加xss过滤 strip_tags
        else{
            foreach ($array as $key => $value) {
                if (!is_array($value)) {
                    $value = strip_tags(addcslashes(stripslashes($value),"'"));
                    $array[$key] = $value;
                } else {
                    filter_param($array[$key]);
                }
            }
        }
        return $array;
    }
}

if(!function_exists('getCurMonthFirstDay')) {
    /**
     * 月第一天
     * @param date
     * @author:马旺
     * @date:2019年6月22日
     * @return: data
     */
    function getCurMonthFirstDay($date)
    {
        return date('Y-m-01', strtotime($date));
    }
}

if(!function_exists('getCurMonthLastDay')) {
    /**
     * 月最后一天
     * @param date
     * @author:马旺
     * @date:2019年6月22日
     * @return: data
     */
    function getCurMonthLastDay($date)
    {
        return date('Y-m-d', strtotime(date('Y-m-01', strtotime($date)) . ' +1 month -1 day'));
    }
}


if(!function_exists('convertImgUrl')) {
    /**
     * 根据参数生成完整url
     * @param date
     * @author:马旺
     * @date:2019年6月25日
     * @return: data
     */
    function convertImgUrl($bucket_name = '', $object_key = '')
    {
        $server_point = \FlashExpress\bi\App\Models\backyard\SettingEnvModel::findFirst("code = 'server_point'");
        if ($server_point) {
            return 'https://' . $bucket_name . $server_point->set_val . $object_key;
        }
        return 'https://' . $bucket_name . '.oss-ap-southeast-1.aliyuncs.com/' . $object_key;
    }
}


if(!function_exists('weekStart')) {
    /**
     * 根据日期获取周的开始日期【周日是第一天】
     * @param date
     * @author:马旺
     * @date:2019年7月9日
     * @return: data
     */
    function weekStart($ymd = '')
    {
        //当前日期
        $sdefaultDate = $ymd ? $ymd : date("Y-m-d");
        //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
        $first=1;
        //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
        $w=date('w',strtotime($sdefaultDate));
        //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
        $week_start=date('Y-m-d',strtotime("$sdefaultDate -".($w ? $w - $first : 6).' days'));
        return $week_start;
    }
}

if(!function_exists('weekEnd')) {
    /**
     * 根据日期获取周的结束【周日是第一天】
     * @param date
     * @author:马旺
     * @date:2019年7月9日
     * @return: data
     */
    function weekEnd($ymd = '')
    {
        //当前日期
        $sdefaultDate = $ymd ? $ymd : date("Y-m-d");
        //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
        $first=1;
        //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
        $w=date('w',strtotime($sdefaultDate));
        //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
        $week_start=date('Y-m-d',strtotime("$sdefaultDate -".($w ? $w - $first : 6).' days'));
        //本周结束日期
        $week_end=date('Y-m-d',strtotime("$week_start +6 days"));
        return $week_end;
    }
}


// 获取时间区间内的 周六日
if(!function_exists('get_week_end')){
    function get_week_end($start, $end,$need_day=[0,6]) {//默认周六日
        if(empty($start) || empty($end))
            return array();
        $key = $start;
        $date_array = array();
        while ($key <= $end) {
            $w = date('w', strtotime($key));
            if(in_array($w,$need_day))
                $date_array[] = $key;

            $key = date("Y-m-d", strtotime("+1 day", strtotime($key)));
        }
        return $date_array;
    }
}


if(!function_exists('UUID')){
    function uuid() {
        if (function_exists ( 'com_create_guid' )) {
            return com_create_guid ();
        } else {
            mt_srand ( ( double ) microtime () * 10000 ); //optional for php 4.2.0 and up.随便数播种，4.2.0以后不需要了。
            $charid = strtoupper ( md5 ( uniqid ( rand (), true ) ) ); //根据当前时间（微秒计）生成唯一id.
            $hyphen = chr ( 45 ); // "-"
            $uuid = '' . //chr(123)// "{"
    substr ( $charid, 0, 8 ) . $hyphen . substr ( $charid, 8, 4 ) . $hyphen . substr ( $charid, 12, 4 ) . $hyphen . substr ( $charid, 16, 4 ) . $hyphen . substr ( $charid, 20, 12 );
            //.chr(125);// "}"
            return $uuid;
        }
    }
}

/**
 * http 请求
 */
if (!function_exists('httpPostFun')) {

    function httpPostFun($url, $param, $header = null, $pwd = null)
    {

        $curl = curl_init();
        if ($pwd != null) {
            $data = buildRequestParamFun($param, $pwd);
        } else {
            $data = $param;
        }
        if (!isset($header)) {
            $header[] = "Content-type: application/x-www-form-urlencoded";
            $header[] = "Accept: application/json";
            if(isset($param['lang'])){
                $header[] = "Accept-Language: " . $param['lang'];
            }
            $escape = true;
        }
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, true); // post
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data); // post data
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);

        curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);

        $responseText = curl_exec($curl);
        if (curl_errno($curl)) {
            $responseText .= "error: " . curl_error($curl);
        }

        curl_close($curl);
        if (isset($escape)) {
            $responseText = json_decode($responseText, true);
        }
        return $responseText;
    }
}
/**
 * @param $data_arr
 * @param $pwd  需要客户密码做加密认证
 * @return bool|string
 */
if (!function_exists('buildRequestParamFun')) {

    function buildRequestParamFun($data_arr, $pwd,$only_sign = false)
    {
        $sign = '';
        ksort($data_arr);
        foreach ($data_arr as $k => $v) {
            if (($v != null) && ($k != 'sign')) {
                $sign .= $k . '=' . $v . '&';
            }
        }
        $sign .= "key=" . $pwd;

        $data_arr['sign'] = strtoupper(hash("sha256", $sign));

        if ($only_sign) {
            return $data_arr['sign'];
        }
        $requestStr = '';
        foreach ($data_arr as $k => $v) {
            $requestStr .= $k . "=" . urlencode($v) . '&';
        }
        return substr($requestStr, 0, -1);
    }
}

/**
 * 数组value转换为IN条件字符串
 * @param $data
 * @return string
 */
if(!function_exists('getIdsStr'))
{
    function getIdsStr($data = [])
    {
        $str = "";
        if (empty($data)) {
            return $str;
        }
        $str = "'" . join("','", array_values(array_filter($data))) . "'";
        return $str;
    }
}

/**
 * 数组value转换为IN条件字符串
 * @param $data
 * @return string
 */
if(!function_exists('getIdsWithoutQuotes'))
{
    function getIdsWithoutQuotes($data = [])
    {
        $str = "";
        if (empty($data)) {
            return $str;
        }

        $str = join(",", array_values(array_filter($data)));
        return $str;
    }
}

/**
 * 整数转泰语
 * 参考:https://github.com/earthchie/BAHTTEXT.js/blob/master/BAHTTEXT.js
 * @param $num
 * @return string
 */
if(!function_exists('bahttext')){
    function bahttext($num,$suffix="บาทถ้วน"){

        $num = intval($num);
        if(empty($num)){
            //0元
            return "ศูนย์บาทถ้วน";
        }

        $strNum = "".$num;

        //十，百，千,万，十万，百万
        $t = ['', 'สิบ', 'ร้อย', 'พัน', 'หมื่น', 'แสน', 'ล้าน'];
        //1,2,3,4,5,6,7,8,9
        $n = ['', 'หนึ่ง', 'สอง', 'สาม', 'สี่', 'ห้า', 'หก', 'เจ็ด', 'แปด', 'เก้า'];

        $len =strlen($strNum);
        $text="";


        if ($len > 7) { // more than (or equal to) 10 millions
            $overflow = substr($strNum,0, $len - 6);
            $remains = substr($strNum,$len-6);
            return str_replace("บาทถ้วน",'ล้าน',bahttext($overflow)) . str_replace('ศูนย์','',bahttext($remains));
        } else {
            for ($i = 0; $i < $len; $i = $i + 1) {
                $digit = intval($strNum[$i], 10);

                if ($digit > 0) {
                    if ($len > 2 && $i === $len - 1 && $digit === 1 && $suffix !== 'สตางค์') {
                        $text .= 'เอ็ด' . $t[$len - 1 - $i];
                    } else {
                        $text .= $n[$digit] . $t[$len - 1 - $i];
                    }
                }
            }
            $text= str_replace('หนึ่งสิบ','สิบ',$text);
            $text= str_replace('สองสิบ','ยี่สิบ',$text);
            $text= str_replace('สิบหนึ่ง','สิบเอ็ด',$text);

            return $text .$suffix;
        }
    }
}

/**
 * 整数转英文单词
 * 参考:https://support.office.com/zh-cn/article/%E5%B0%86%E6%95%B0%E5%AD%97%E8%BD%AC%E6%8D%A2%E4%B8%BA%E5%8D%95%E8%AF%8D-a0d166fb-e1ea-4090-95c8-69442cd55d98
 * @param $num
 * @return string
 */

if(!function_exists('num2Word')){

    function num2Word($num,$suffix="Baht"){

        $place =[1=>"",2=>" Thousand ",3=>" Million ",4=>" Billion ",5=>" Trillion "];

        function getDigit($digit){
            $digitArr = ["","One","Two","Three","Four","Five","Six","Seven","Eight","Nine"];
            if(empty($digitArr[$digit])){
                return "";
            }else{
                return $digitArr[$digit];
            }
        }

        function getTens($tensText){
            $str = "".$tensText;

            $tenArr = [10=>"Ten",11=>"Eleven",12=>"Twelve",13=>"Thirteen",14=>"Fourteen",15=>"Fifteen",16=>"Sixteen",17=>"Seventeen",18=>"Eighteen",19=>"Nineteen"];
            //20-99
            if(empty($tenArr[$tensText])){
                $tyArr = ["","","Twenty","Thirty","Forty","Fifty","Sixty","Seventy","Eighty","Ninety"];
                return $tyArr[$str[0]]." ".getDigit($str[1]);
            }else{
                return $tenArr[$tensText];
            }
        }


        function getRight($str,$offset){
            $start = strlen($str)-$offset;
            if($start<0){
                $start=0;
            }
            return substr($str,$start);
        }


        function getHundreds($myNumber){
            $result = "";

            if(empty($myNumber)){
                return "";
            }

            //取后三位
            $temp = getRight($myNumber,3);
            //补0
            $temp = sprintf("%03d", intval($temp));

            if($temp[0]!=0){
                $result .= getDigit($temp[0])." Hundred ";
            }

            if($temp[1]!=0){
                $result.= getTens($temp[1].$temp[2]);
            }else{
                $result.= getDigit($temp[2]);
            }
            return $result;
        }


        $res = "";

        //没有小数
        $num = intval($num);

        $strNum = "".$num;

        $count = 1;

        while(!empty($strNum)){
            $temp =getHundreds(getRight($strNum,3));
            if(!empty($temp)){
                $res = $temp.$place[$count].$res;
            }

            if(strlen($strNum)>3){
                $strNum = substr($strNum,0,strlen($strNum)-3);
            }else{
                $strNum="";
            }

            $count++;
        }
        return $res." ".$suffix;
    }
}

/**
 * 函数只返回给定数组中指定的键／值对：
 */
if(!function_exists('array_only')){
    function array_only($array,$keys){
        return array_intersect_key($array, array_flip((array) $keys));
    }
}

/**
 * 把变量当成文件读写的协议
 */
if (!function_exists('registerStream')) {
    function registerStream()
    {
        $existed = in_array("var", stream_get_wrappers());
        if ($existed) {
            stream_wrapper_unregister("var");
        }
        stream_wrapper_register("var", "FlashExpress\bi\App\library\VarStream");
    }
}


//判断 是不是闰年 决定分母 是365 还是 366
if (!function_exists('year_type')) {
    function year_type($year)
    {
        if($year % 100 == 0){//判断世纪年
            if ($year % 400 == 0 && $year % 3200 != 0){
                return true;
            }else{
                return false;
            }
        }else{//剩下的就是普通年了
            if($year % 4 == 0 && $year % 100 != 0){
                return true;
            }else {
                return false;
            }
        }
    }
}

// 清除多维数组元素两边的空白字符
if (!function_exists('trim_array')) {
    function trim_array ($input)
    {
        if (!is_array($input)) {
            return trim($input);
        }

        return array_map('trim_array', $input);
    }
}

/**
 * 当前国家code是否为菲律宾
 * 方法修改为 非泰国
 * 是否为指定国家
 */
if (!function_exists('isCountry')) {
    function isCountry($country = 'TH'): bool
    {
        if (is_array($country)) {
            return in_array(strtolower(env('country_code')), array_map('strtolower', $country));
        }
        return strtolower(env('country_code','TH')) == strtolower($country);
    }
}

if (!function_exists('get_country_code')) {
    function get_country_code() {
        return strtoupper(env('country_code', 'TH'));
    }
}


/**
 * 是否为脚本执行
 * @return bool
 */
function is_cli(){
    return preg_match("/cli/i", php_sapi_name()) ? true : false;
}


/**
 * 获取国家默认语言
 * @return string
 */
function getCountryDefaultLang(): string
{
    switch (strtolower(env('country_code'))){
        case 'th':
            $lang = 'th';
            break;
        case 'ph':
        case 'my':
            $lang = 'en';
            break;
        case 'id':
            $lang = 'id';
            break;
        case 'la':
            $lang = 'lo';
            break;
        case 'vn':
            $lang = 'vi';
            break;
        default:
            $lang = 'en';
    }
    return $lang;
}


/**
 * 方法 套错大括号了
 * 与当前时间的时差
 * @param int $begin_time 时间戳
 * @param int $end_time 时间戳
 */
if (!function_exists('getTimeDifference')) {
    function getTimeDifference($begin_time,$end_time)
    {
        if($begin_time < $end_time){
            $starttime = $begin_time;
            $endtime = $end_time;
        }else{
            $starttime = $end_time;
            $endtime = $begin_time;
        }
        //计算天数
        $timediff = $endtime-$starttime;
        $days = intval($timediff/86400);
        //计算小时数
        $remain = $timediff%86400;
        $hours = intval($remain/3600);
        //计算分钟数
        $remain = $remain%3600;
        $mins = intval($remain/60);
        //计算秒数
        $secs = $remain%60;
        $res = array("day" => $days,"hour" => $hours,"min" => $mins,"sec" => $secs);
        return $res;
    }
}

/**
 *
 * @param $arr
 * @return mixed
 */
function combination($arr){
    if(count($arr) >= 2){
        $tmp_arr = array();
        $arr1 = array_shift($arr);
        $arr2 = array_shift($arr);
        foreach($arr1 as $k1 => $v1){
            foreach($arr2 as $k2 => $v2){
                $tmp_arr[] = $v1.$v2;
            }
        }
        array_unshift($arr, $tmp_arr);
        $arr = combination($arr);
    }else{
        return $arr;
    }
    return $arr;
}

//如果 含有默认值数量相同 取默认值 位置靠后的 如果第一个默认值相同 （不考虑 目前不存在）
function get_final_key($arr,$default){
    $p = 0;
    $final = '';
    foreach ($arr as $str){
        $pos = strpos($str,$default);//第一次出现的位置 越靠后 越优先
        if($pos > $p){
            $final = $str;
            $p = $pos;
        }
    }
    return $final;
}

/**
 * 获取国家默认银行
 * @return string
 */
function getCountryDefaultBank()
{
    switch (strtolower(env('country_code'))){
        case 'th':
            $bank_type = 2;//SCB
            break;
        case 'ph':
            $bank_type = 21;//UB
            break;
        case 'la':
            $bank_type = 53; //BCEL
            break;
        case 'my':
            $bank_type = 15;//CIMB
            break;
        case 'vn':
            $bank_type = 20; //HSBC
            break;
        case 'id':
            $bank_type = 55; //BCA
            break;
        default:
            $bank_type = 2;//SCB
    }
    return $bank_type;
}

/**
 * 零时区转env配置的时区
 */
if(!function_exists('getZeroTimeZone'))
{
    function getZeroTimeZone($date = '',$timezone = '')
    {
        $timezone = env('timeZone', '+07:00');
        if ($timezone && $date){
            $time = substr($timezone,1,2);
            return date('Y-m-d H:i:s',strtotime("-".$time."hour",strtotime($date)));
        }else{
            return $date;
        }

    }
}

/**
 * 获取展示时间，默认是零时区转换为泰国时区
 * @param $date
 * @param string $format
 * @param string $time_zone_num
 * @param string $action
 * @return false|string
 */
if(!function_exists('show_time_zone'))
{
    function show_time_zone($date, $format = 'Y-m-d H:i:s', $action = '+')
    {
        if (!$date) return '';
        $time_offset = env('add_hour', 7);
        return date($format, strtotime($date . $action . $time_offset . ' hours'));
    }
}

/**
 * 获取到零时区的时间，默认是泰国东七区转换为零时区
 * @param $date
 * @param string $format
 * @param string $time_zone_num
 * @param string $action
 * @return false|string
 */
if(!function_exists('zero_time_zone'))
{
    function zero_time_zone($date, $format = 'Y-m-d H:i:s', $action = '-')
    {
        $time_offset = env('add_hour', 7);
        return date($format, strtotime($date. $action . $time_offset . ' hours'));
    }
}

/**
 * 判断语言是否可用不可用返回本国默认语言 与getCountryDefaultLang组合使用
 * @param string $lang 语言
 */
if (!function_exists('getMessagesLang')) {
    function getMessagesLang($lang)
    {
        //语言包字符处理 language:th-TH  en-US en-GB en-US
        $lang = substr($lang,0,2);
        if(in_array($lang, ['zh', 'zh-CN']))
        {
            $lang = 'zh-CN';
        }
        //客户端手机 非限定语言环境 默认本国语言
        $path = APP_PATH . "/messages/" . $lang . ".php";
        if (!file_exists($path)) {
            //这个语言包是肯定存在的
            $lang = getCountryDefaultLang();
        }

        return  $lang;
    }
}



/**
 * 根据 0.5 取模  大于n.5 取 n.5 小于n.5 大于n 取n
 *
 */
if(!function_exists('half_num'))
{
    function half_num($num)
    {
        $have_days = round($num, 5);
        $int_day   = floor($have_days);//2

        $return = $int_day;
        if (bccomp($have_days, $int_day + 0.5, 2) >= 0) {
            $return = $int_day + 0.5;
        }

        $return = number_format($return, 1);
        return $return;

    }
}


/**
* 根据语言处理显示默认语言针对库存盘点使用
* @Date: 6/16/22 10:28 AM
**/
if (!function_exists('getLangToDefault')) {
    function getLangToDefault($lang)
    {
        $lang = empty($lang) ? 'zh' : $lang;
        if (!in_array($lang, ['zh', 'en', 'th', 'zh-CN'])) {
            if (isCountry('LA') || isCountry('TH')) {
                $lang = 'th';
            } elseif (isCountry('PH') || isCountry('ID') || isCountry('MY')) {
                $lang = 'en';
            } else {
                $lang = 'en';
            }
        }
        return  $lang;
    }
}

if (!function_exists('auth_code')) {
    /**
     * 加密/解密算法，支持过期时间
     * @param $string
     * @param string $operation
     * @param string $key
     * @param int $expiry
     * @return false|string
     */
    function auth_code($string, string $operation = 'DECODE', string $key = '', int $expiry = 0)
    {
        if ($operation == 'DECODE') {
            $string = str_replace(['[a]', '[b]', '[c]'], ['+', '&', '/'], $string);
        }
        // 动态密匙长度，相同的明文会生成不同密文就是依靠动态密匙
        $ckey_length = 4;

        // 密匙
        $key = md5($key);

        // 密匙a会参与加解密
        $keya = md5(substr($key, 0, 16));
        // 密匙b会用来做数据完整性验证
        $keyb = md5(substr($key, 16, 16));
        // 密匙c用于变化生成的密文
        $keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length) : substr(md5(microtime()), -$ckey_length)) : '';
        // 参与运算的密匙
        $cryptkey = $keya . md5($keya . $keyc);
        $key_length = strlen($cryptkey);
        // 明文，前10位用来保存时间戳，解密时验证数据有效性，10到26位用来保存$keyb(密匙b)，
        //解密时会通过这个密匙验证数据完整性
        // 如果是解码的话，会从第$ckey_length位开始，因为密文前$ckey_length位保存 动态密匙，以保证解密正确
        $string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d', $expiry ? $expiry + time() : 0) . substr(md5($string . $keyb), 0, 16) . $string;
        $string_length = strlen($string);
        $result = '';
        $box = range(0, 255);
        $rndkey = array();
        // 产生密匙簿
        for ($i = 0; $i <= 255; $i++) {
            $rndkey[$i] = ord($cryptkey[$i % $key_length]);
        }
        // 用固定的算法，打乱密匙簿，增加随机性，好像很复杂，实际上对并不会增加密文的强度
        for ($j = $i = 0; $i < 256; $i++) {
            $j = ($j + $box[$i] + $rndkey[$i]) % 256;
            $tmp = $box[$i];
            $box[$i] = $box[$j];
            $box[$j] = $tmp;
        }
        // 核心加解密部分
        for ($a = $j = $i = 0; $i < $string_length; $i++) {
            $a = ($a + 1) % 256;
            $j = ($j + $box[$a]) % 256;
            $tmp = $box[$a];
            $box[$a] = $box[$j];
            $box[$j] = $tmp;
            // 从密匙簿得出密匙进行异或，再转成字符
            $result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
        }
        if ($operation == 'DECODE') {
            // 验证数据有效性，请看未加密明文的格式
            if ((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) && substr($result, 10, 16) == substr(md5(substr($result, 26) . $keyb), 0, 16)) {
                return substr($result, 26);
            } else {
                return '';
            }
        } else {
            // 把动态密匙保存在密文里，这也是为什么同样的明文，生产不同密文后能解密的原因
            // 因为加密后的密文可能是一些特殊字符，复制过程可能会丢失，所以用base64编码
            $str = $keyc . str_replace('=', '', base64_encode($result));
            return str_replace(['+', '&', '/'], ['[a]', '[b]', '[c]'], $str);
        }
    }
}

if(!function_exists('get_month_days')){
    /**
     * 获取当月日历
     */
    function get_month_days($y_m)
    {
        $returnDays = [];
        $year       = date('Y', strtotime($y_m));
        $month      = date('m', strtotime($y_m));
        $day        = @cal_days_in_month(CAL_GREGORIAN, $month, $year);
        for ($i = 1; $i <= $day; $i++) {
            if (strlen($i) == 1) {
                $i = '0' . $i;
            }
            $returnDays[] = $y_m . '-' . $i;
        }
        return $returnDays;
    }
}

/**
 * 打印数据
 */
if (!function_exists('dd')) {
    function dd(...$args)
    {
        if (function_exists('dump')) {
            dump(...$args);
        } else {
            var_dump(...$args);
        }
        die;
    }
}
/**
* 链接数组keys
*/
if (!function_exists('implode_array_keys')) {
    function implode_array_keys($array = [])
    {
        return implode(',',array_keys($array));
    }
}

/**
 * Groups an array by a given key. Any additional keys will be used for grouping
 * the next set of sub-arrays.
 *
 * <AUTHOR> Zatecky
 *
 * @param array $arr     The array to be grouped.
 * @param mixed $key,... A set of keys to group by.
 *
 * @return array
 */
function array_group_by(array $arr, $key) : array
{
    if (!is_string($key) && !is_int($key) && !is_float($key) && !is_callable($key)) {
        trigger_error('array_group_by(): The key should be a string, an integer, a float, or a function', E_USER_ERROR);
    }

    $isFunction = !is_string($key) && is_callable($key);

    // Load the new array, splitting by the target key
    $grouped = [];
    foreach ($arr as $value) {
        $groupKey = null;

        if ($isFunction) {
            $groupKey = $key($value);
        } else if (is_object($value)) {
            $groupKey = $value->{$key};
        } else {
            $groupKey = $value[$key];
        }

        $grouped[$groupKey][] = $value;
    }

    // Recursively build a nested grouping if more parameters are supplied
    // Each grouped array value is grouped according to the next sequential key
    if (func_num_args() > 2) {
        $args = func_get_args();

        foreach ($grouped as $groupKey => $value) {
            $params = array_merge([$value], array_slice($args, 2, func_num_args()));
            $grouped[$groupKey] = call_user_func_array('array_group_by', $params);
        }
    }

    return $grouped;
}

/**
 * 数组根据指定字段分组
 */
if (!function_exists('array_group_by_column')) {
    function array_group_by_column($array, $column = '')
    {
        if (empty($column)) {
            return [];
        }
        $grouped = [];
        foreach ($array as $item) {
            $category = $item[$column] ?? null;

            if (empty($category)) {
                continue;
            }

            // 如果分类不存在，初始化空数组
            if (!isset($grouped[$category])) {
                $grouped[$category] = [];
            }
            // 将当前元素添加到对应分类
            $grouped[$category][] = $item;
        }
        return $grouped;
    }
}

/**
 * @param array $arr
 * @param $key
 * @return array
 */
function array_remove_key(array $arr, $key)
{
    $removed = [];
    foreach ($arr as $value) {
        if (isset($value[$key])){
            unset($value[$key]);
        }
        $removed[] = $value;
    }

    return $removed;
}

/**
 * need molten.so
 * molten_get_traceid
 */
if(!function_exists('molten_get_traceid')) {
    function molten_get_traceid()
    {
        static $tid;
        if (!empty($tid)) {
            return $tid;
        }
        $tid = uniqid('tid');
        return $tid;
    }
}

/**
 *
 * 要过滤的特殊字符,字符中有内容（一个红点）
 *
 */
if (!function_exists('nameSpecialCharsReplace')) {
    function nameSpecialCharsReplace($name)
    {
        $name = str_replace("\xe2\x80\x8b",'',$name);
        $name = str_replace("\xe2\x80\x8c",'',$name);
        $name = str_replace("\xe2\x80\x8d",'',$name);
        return $name;
    }
}

/**
 * 计算两个经纬度之间的距离 返回单位米
 */
if (!function_exists('calculateDistance')) {
    function calculateDistance($latitude1, $longitude1, $latitude2, $longitude2) {
        // 将经纬度转换为弧度
        $latitude1_rad = deg2rad($latitude1);
        $longitude1_rad = deg2rad($longitude1);
        $latitude2_rad = deg2rad($latitude2);
        $longitude2_rad = deg2rad($longitude2);

        // 计算地球半径（平均半径）
        $earthRadius = 6371;

        // 使用 Haversine 公式计算距离
        $distance = acos(sin($latitude1_rad) * sin($latitude2_rad) + cos($latitude1_rad) * cos($latitude2_rad) * cos($longitude2_rad - $longitude1_rad)) * $earthRadius;

        // 单位米
        return intval($distance * 1000);
    }
}

//获取文件的basename
if (!function_exists('getBaseName')) {
    function getBaseName($path = ''): string
    {
        if (!$path) {
            return '';
        }

        $info = pathinfo($path);

        return $info['basename'] ?? '';
    }
}

// 判断版本
if (!function_exists('check_version')) {
    function check_version($clientVersion, $newVersion): bool
    {
        $newVersion    = explode('.', $newVersion);
        $clientVersion = explode('.', $clientVersion);
        $num           = count($clientVersion);

        for ($i = 0; $i < $num; $i++) {
            if($clientVersion[$i] == $newVersion[$i]){
                continue;
            }
            return $clientVersion[$i] < $newVersion[$i];
        }
        return false;

    }
}

//生成随机文件名
if (!function_exists('genFileName')) {
    function genFileName($file_name_prefix = ''): string
    {
        if (!empty($file_name_prefix)) {
            return sprintf('%s_%s%s', $file_name_prefix, gmdate("YmdHis", time()), rand(100, 999));
        } else {
            return sprintf('%s%s', gmdate("YmdHis", time()), rand(100, 999));
        }
    }
}

// 多维数组按指定字段排序
if (!function_exists('array_sort')) {
    /**
     * 二维数组根据某个字段排序
     * @param array $array 要排序的数组
     * @param string $keys   要排序的键字段
     * @param mixed $sort  排序类型  SORT_ASC     SORT_DESC
     * @return array 排序后的数组
     */
    function array_sort(array $array, string $keys, $sort = SORT_DESC) {
        $keys_value = [];
        foreach ($array as $k => $v) {
            $keys_value[$k] = $v[$keys];
        }

        array_multisort($keys_value, $sort, $array);
        return $array;
    }
}


//获取环境变量 小写
if (!function_exists('get_runtime')) {
    function get_runtime() {
        return strtolower(RUNTIME);
    }
}



if(!function_exists('get_real_ip')){

    function get_real_ip(): string
    {
        if (isset($_SERVER)) {
            if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
                $real_ip = 'unknown';
                foreach ($arr as $ip) {
                    $ip = trim($ip);

                    if ($ip != 'unknown') {
                        $real_ip = $ip;
                        break;
                    }
                }
            } else {
                if (isset($_SERVER['HTTP_CLIENT_IP'])) {
                    $real_ip = $_SERVER['HTTP_CLIENT_IP'];
                } else {
                    if (isset($_SERVER['REMOTE_ADDR'])) {
                        $real_ip = $_SERVER['REMOTE_ADDR'];
                    } else {
                        $real_ip = '0.0.0.0';
                    }
                }
            }
        } else {
            if (getenv('HTTP_X_FORWARDED_FOR')) {
                $real_ip = getenv('HTTP_X_FORWARDED_FOR');
            } else {
                if (getenv('HTTP_CLIENT_IP')) {
                    $real_ip = getenv('HTTP_CLIENT_IP');
                } else {
                    $real_ip = getenv('REMOTE_ADDR');
                }
            }
        }

        preg_match('/[\\d\\.]{7,15}/', $real_ip, $online_ip);
        return (!empty($online_ip[0]) ? $online_ip[0] : '0.0.0.0');
    }
}


/**
 * 是否为json
 */
if(!function_exists('is_json')) {
    function is_json($str): bool
    {
        if (is_numeric($str)) {
            return false;
        }
        if (is_string($str)) {
            json_decode($str, true);
            return json_last_error() == JSON_ERROR_NONE;
        }
        return false;
    }
}

function is_staff_info_id($staff_info_id)
{
    return preg_match('/^\d{5,11}$/', $staff_info_id);
}



function list_to_tree($list, $pk = 'id', $pid = 'pid', $child = '_child', $root = 0)
{
    $tree = array();// 创建Tree
    if (is_array($list)) {
        // 创建基于主键的数组引用
        $refer = array();
        foreach ($list as $key => $data) {
            $refer[$data[$pk]] =& $list[$key];
        }

        foreach ($list as $key => $data) {
            // 判断是否存在parent
            $parentId = $data[$pid]??0;
            if ($root == $parentId) {
                $tree[$data[$pk]] =& $list[$key];
            } else {
                if (isset($refer[$parentId])) {
                    $parent =& $refer[$parentId];
                    $parent[$child][] =& $list[$key];
                }
            }
        }
    }
    return $tree;
}

/**
 * 国家手机号码位数处理
 * @param $mobile
 * @return mixed|string
 */
function get_country_full_mobile($mobile)
{
    if (isCountry('PH')) {
        // 菲律宾
        $mobile = str_pad($mobile, 11, "0", STR_PAD_LEFT);
    } elseif (isCountry('ID')) {
        if (strlen($mobile) != 13 && strpos($mobile, "0") !== 0) {
            $mobile = str_pad($mobile, strlen($mobile) + 1, "0", STR_PAD_LEFT);
        }
    } elseif (isCountry(['LA', 'MY'])) {
        if (strpos($mobile, '0') !== 0) {
            $pad_len = strlen($mobile) == 9 ? 10 : 11;
            $mobile  = str_pad($mobile, $pad_len, "0", STR_PAD_LEFT);
        }
    }
    return $mobile;
}


//把日期转换成 星期对应日期的数组
function formatDateWeek($dateList): array
{
    $return = [];
    if(empty($dateList)){
        return $return;
    }

    foreach ($dateList as $date){
        $key = date('w', strtotime($date));
        if($key == 0){
            $key = 7;
        }
        $return[$key][] = $date;
    }
    return $return;
}
/**
 * 是否是日期格式
 */
if (!function_exists('is_format_date')) {
    function is_format_date($date): bool
    {
        if (!preg_match("/\d{4}\-\d{2}\-\d{2}/", $date)) {
            return false;
        }
        return true;
    }
}

/**
 * 签名图转编码
 * @param string $img
 * @param bool $imgHtmlCode
 * @return string
 */
if (!function_exists('img_base64_encode')) {
    function img_base64_encode($img_url): string
    {
        //获取文件内容
        $file_content = file_get_contents($img_url);
        if ($file_content === false) {
            return '';
        }

        $imageInfo = getimagesize($img_url);
        $prefix    = 'data:'.$imageInfo['mime'].';base64,';

        return $prefix.chunk_split(base64_encode($file_content));
    }
}

/**
 * 字符串补位
 */
if (!function_exists('str_fill_in')) {
    /**
     * @param $str
     * @param string $fill 要补的字符
     * @param int $number 至多位数
     * @param string $position 补位的位置 l 左 r 右
     * @param null $positionNumber 固定位数
     * @return string
     */
    function str_fill_in($str,$fill = '',$number = 0,$position='l',$positionNumber = null): string
    {
        $fillIn = count(mb_str_split($str));
        if ($str === '') {
            $fillIn = 0;
        }
        if ($position == 'l') {
            $str = str_repeat($fill,max($number-$fillIn,0)).$str;
        } else {
            $str = $str.str_repeat($fill,max($number-$fillIn,0));
        }
        return mb_substr($str,0,$number,'UTF-8');
    }
}


