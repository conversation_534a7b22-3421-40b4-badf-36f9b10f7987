<?php
/**
 * Created by PhpStorm.
 * User: ni<PERSON><PERSON><PERSON><PERSON>
 * Date: 2018/5/26
 * Time: 17:32
 */
use FlashExpress\bi\App\EventsListener\TaskListener;
use FlashExpress\bi\App\library\Exception\ListenerException;
use Phalcon\Di\FactoryDefault\Cli as CliDI;
use Phalcon\Cli\Console as ConsoleApp;
use Phalcon\Loader;
define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', __DIR__ );
require BASE_PATH.'/vendor/autoload.php';
require BASE_PATH.'/app/common/functions.php';
require BASE_PATH.'/app/util/ErrorHandler.php';

ini_set('memory_limit', '-1');

//加载环境变量
$dotenv = new Dotenv\Dotenv(BASE_PATH);
$dotenv->load();

//设定运行环境变量
$runtime = env('runtime','dev');
define('RUNTIME', $runtime);
//设置报错级别
if ($runtime == 'pro') {
    error_reporting(E_ERROR | E_WARNING | E_PARSE);
    ini_set('display_errors','off');
    ini_set('memory_limit', '-1');
} else {
    error_reporting(E_ALL);
    ini_set('display_errors','on');
}

// 使用CLI工厂类作为默认的服务容器
$di = new CliDI();


include APP_PATH . '/tasks/services.php';
$config = $di->getConfig();
include APP_PATH . '/tasks/loader.php';

// 创建console应用
$console = new ConsoleApp();
$console->setDI($di);

$di->setShared("console", $console);

$countryCode = ucfirst(strtolower(env('country_code', 'Th')));
$console->registerModules([
                              $countryCode => [
                                  'className' => 'FlashExpress\bi\App\Modules\\'.$countryCode.'\Module',
                                  'path' => APP_PATH . '/modules/'.$countryCode.'/Module.php',
                              ]
                          ]);


//设置时区
$time_zone_GMT = $di->getConfig()->application->timeZoneGMT;
date_default_timezone_set($time_zone_GMT);

/**
 * 处理console应用参数
 */
$arguments = [];
$arguments['config'] = $di->get("config");
$arguments['module'] = $countryCode;

foreach ($argv as $k => $arg) {
    if ($k === 1) {
        $arguments["task"] = $arg;
    } elseif ($k === 2) {
        $arguments["action"] = $arg;
    } elseif ($k >= 3) {
        $arguments["params"][] = $arg;
    }
}


try {
    $taskListener = new TaskListener();
    //extracts default events manager
    $eventsManager = new \Phalcon\Events\Manager();
    //attaches new event console:beforeTaskHandle and console:afterTaskHandle
    $eventsManager->attach(
        'console:beforeHandleTask', $taskListener->beforeHandleTask($argv)
    );
    $eventsManager->attach(
        'console:afterHandleTask', $taskListener->afterHandleTask($argv)
    );
    $console->setEventsManager($eventsManager);
    // 处理参数
    $console->handle($arguments);
}
catch (\Throwable $throwable) {
    $log = array(
        'file' => $throwable->getFile(),
        'line' => $throwable->getLine(),
        'code' => $throwable->getCode(),
        'msg' => $throwable->getMessage(),
        'trace' => $throwable->getTraceAsString(),
    );
    //错误日志
    $log = [
        'code' => $throwable->getCode(),
        'msg'  => $throwable->getMessage(),
    ];
    echo json_encode($log, JSON_UNESCAPED_UNICODE) . PHP_EOL;
    $di               = $console->getDI();
    $exception_logger = $di->get('logger');
    if ($throwable instanceof ListenerException) {
        $exception_logger->write_log(json_encode($log, JSON_UNESCAPED_UNICODE), 'notice');
    } else {
        $log = array_merge($log, [
            'file'  => $throwable->getFile(),
            'line'  => $throwable->getLine(),
            'trace' => $throwable->getTraceAsString(),
        ]);
        $exception_logger->write_log(json_encode($log, JSON_UNESCAPED_UNICODE), 'error');
        exit(255);
    }
}