<?php
namespace FlashExpress\bi\App\Library;
class LangFresh {

    private $en;
    private $zh;
    private $th;
    private $JsPath;
    function __construct()
    {
        $this->en=APP_PATH.'/messages/en.php';
        $this->zh=APP_PATH.'/messages/zh-CN.php';
        $this->th=APP_PATH.'/messages/th.php';
        $this->JsPath=BASE_PATH.'/public/js/lang';
        if (!is_dir($this->JsPath)){
            mkdir($this->JsPath, 0777);
        }
        $this->GenLanguageJsFile($this->en);
        $this->GenLanguageJsFile($this->zh);
        $this->GenLanguageJsFile($this->th);
    }
    public   function GenLanguageJsFile($LanguageFilePath){
        if(!is_file($LanguageFilePath))
        {
            return false;
        }
        $messages = [];
        $path =  $LanguageFilePath;
        require $path;
        $FileName = basename($path,".php");
        $JsonData=json_encode($messages);
        file_put_contents("{$this->JsPath}/{$FileName}.js",' window.lang='.$JsonData);
    }
}