<?php

namespace FlashExpress\bi\App\library;


class VarStream
{
    private $string;
    private $position;

    public function stream_open($path, $mode, $options, &$opened_path)
    {
        $path = explode('://', $path, 2)[1];

        // 此处可对传入的参数进行自定义解析，并作进一步的操作
        $this->string = $path;
        $this->position = 0;
        return true;
    }

    public function stream_read($count)
    {
        $ret = substr($this->string, $this->position, $count);
        $this->position += strlen($ret);
        return $ret;
    }

    public function stream_eof() {}

    public function stream_stat() {}
}