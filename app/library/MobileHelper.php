<?php
/**
 * Author: Bruce
 * Date  : 2022-03-04 16:30
 * Description:
 */

namespace FlashExpress\bi\App\library;

class MobileHelper
{
    /**
     * 获取当前用户，手机设备信息
     * @return mixed
     */
    public static function getUserAgent()
    {
        //现在的例子 ios ："name:backyard_1.9.3;version:1.9.3;os:iOS_17.2;ov:17.2;device:x86_64,iPhone Simulator;deviceId:20A56F13-136E-4718-9079-514B525161C8;language:zh-Hans;user:iPhone%2015%20Pro;timeZone:+08:00";
        $userAgent = $_SERVER['HTTP_USER_AGENT'];
        //去掉前端浏览器自带的ua Mozilla/5.0 (Linux; Android 13; Infinix X6528 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.135 Mobile Safari/537.36 App/n:Backyard PH;v:1.7.8;os:Android;ov:13;device:Infinix X6528;deviceId:68afe90115adf76d;
        $start      = strpos($userAgent, " App/");
        if ($start !== false) {
            $userAgent = substr($userAgent, $start + 5);// n:Backyard PH;v:1.7.8;os:Android;ov:13;device:Infinix X6528;deviceId:68afe90115adf76d;
        }
        $userAgent = strtolower($userAgent);
        //处理userAgent，获取当前用户的app名称,os,版本号;
        $parts = explode(';', $userAgent);

        $name    = '';
        $os      = '';
        $version = '';

        //安卓例子：by:User-Agent: name:BACKYARD_2.4.4;os:Android_13;device:M2012K11AC;language:zh-CN
        foreach ($parts as $part) {
            $tmp_part    = explode(':', $part);
            $key   = $tmp_part[0];
            $value = $tmp_part[1] ?? null;
            if ($key === 'name') {
                $nameString = explode('_', $value);
                $name       = isset($nameString[0]) ? strtolower($nameString[0]) : '';
                $version    = isset($nameString[1]) ? strtolower($nameString[1]) : '';//by安卓的，版本号 在 name 中。
            }
            if ($key === 'os') {
                $os = strtolower(explode('_', $value)[0]);
            }
            if ($key === 'version') {
                $version = $value;
            }
            //h5里的ua
            if ($key === 'n') {
                if (strpos($value, 'backyard') !== false) {
                    $name = 'backyard';
                } elseif (strpos($value, 'kit') !== false) {
                    $name = 'kit';
                }
            }
            if ($key === 'v') {
                $version = $value;
            }
        }
        $app_name                  = strtolower($name);
        $userAgentInfo['app_name'] = in_array($app_name, ['backyard', 'kit']) ? $app_name : '';//用户app名称：backyard，kit
        $userAgentInfo['os']       = strtolower($os);                                          //操作系统：ios,android
        $userAgentInfo['version']  = $version;                                                 //用户当前版本 1.0.0

        return $userAgentInfo;
    }

    /**
     * 做版本比较运算
     * 判断 用户设备当前版本 大于等于 指定版本 返回 true
     * @param $equipmentInfo
     * @param string $operator
     * @return mixed
     */
    public static function compareVersion($equipmentInfo)
    {
        if (empty($equipmentInfo) || !is_array($equipmentInfo)) {
            return false;
        }
        //获取当前用户，手机设备信息
        $userAgentInfo = self::getUserAgent();
        //取出用户参照比对的版本信息
        $appointVersion = $equipmentInfo[$userAgentInfo['app_name'] . '_' . $userAgentInfo['os']] ?? '';
        if (!$appointVersion) {
            return false;
        }
        return !check_version($userAgentInfo['version'], $appointVersion);
    }


}