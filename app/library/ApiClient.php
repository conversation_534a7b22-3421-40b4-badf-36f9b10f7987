<?php
namespace FlashExpress\bi\App\library;


use Phalcon\Logger;
use Phalcon\Mvc\User\Component;
use JsonRPC\Client;

class ApiClient extends Component
{
    /**
     * @var Client
     */
    private $client;

    /**
     * @var
     */
    private $sys;

    /**
     * @var
     */
    private $module;
    /**
     * @var
     */
    private $method;

    /**
     * @var string
     */
    private $locale;

    /**
     * @var
     */
    private $params;

    /**
     * @var string
     */
    private $src;

    private $url;

    /**
     * ApiClient constructor.
     * @param $sys
     * @param string $module
     * @param string $method
     * @param string $locale
     * @param string $src
     */
    public function __construct($sys, $module = '', $method = '', $locale = 'zh-CN', $src = '')
    {
        $this->sys = $sys;
        $this->module = $module;
        $this->method = $method;
        $this->locale = $locale;
        $this->src = $src;
        $this->url = $this->getSysEndpoint($sys) . '/' . $module;
        $this->url = rtrim($this->url,'/'); //路径最右边的 / 不要了
        $this->client = new Client($this->url);



    }

    /**
     * @param $sys
     * @return string
     */
    private function getSysEndpoint($sys)
    {
        switch ($sys) {
            case 'fle':
                $endpoint = env('fle_rpc_endpoint','http://192.168.0.228:8090/fle-svc');
                break;
            case 'hr_rpc':
                $endpoint = env('hr_rpc','http://192.168.0.230:8091/hr-svc/call');
                break;
            case 'bi_rpc':
                $endpoint = env('svc_call','http://192.168.0.230:8001/svc/call');
                break;
            case 'bi_rpcv2':
                $endpoint = env('svcv2_call','http://192.168.7.69/svcv2/call');
                break;
            case 'winhr_rpc':
                $endpoint = env('hc_api_url','http://192.168.0.230:8100') . '/svc/by_api';
                break;
            case 'coupon_rpc':
                $endpoint = env('coupon_rpc','http://192.168.0.230:8080') . '/svc/call';
                break;
            case 'sms_rpc':
                $endpoint = env('api_send_sms','http://192.168.5.70:8003') . '/rpc/sms';
                break;
            case 'school_rpc':
                $endpoint = env('school_rpc','http://dev01-th-schoolapi.fex.pub/rpc/svc/call');
                break;
            case 'hcm_rpc':
                $endpoint = env('hcm_rpc','http://dev01-th-hcmapi.fex.pub/svc/call');
                break;
            case 'oa_rpc':
                //oa端rpc服务
                $endpoint = env('oa_rpc','http://dev01-th-oaapi.fex.pub/svc/call');
                break;
            case 'ard_api':
                //bi新应用rpc服务
                $endpoint = env('ard_api_rpc','http://192.168.7.66:8801/svc/call');
                break;
            default:
                $endpoint = $sys;
        }

        return $endpoint;
    }

    /**
     * @param $params
     */
    public function setParams($params)
    {
        $this->params[] = $params;
        return $this;
    }

    /**
     * @param $params
     */
    public function setParam($params)
    {
        $this->params = $params;
    }

    /**
     * @return bool|mixed|null
     */
    public function execute()
    {
        $logger = $this->getDI()->get('logger');

        $base_params = ['locale' => $this->locale];
        if (!empty($this->src)) {
            $base_params['src'] = $this->src;
        }

        $params = [$base_params];
        if(!empty($this->params)){
            $params = array_merge($params, $this->params);
        }
        $result = null;
        try {
            // 获取Api回调数据
            $result['result'] = $this->client->execute($this->method, $params,['src_sys'=>'Backyard'],molten_get_traceid());
            $result['code'] = 1;
            $info = [
                'url' => $this->url,
                'sys' => $this->sys,
                'mod' => $this->module,
                'method' => $this->method,
                'params' => $params,
                'result_is' => $result['result'],
            ];
            $logger->write_log('api_client_result '.json_encode($info,JSON_UNESCAPED_UNICODE), 'info');
        } catch (\Exception $e) {
            $info = [
                'url' => $this->url,
                'sys' => $this->sys,
                'mod' => $this->module,
                'method' => $this->method,
                'params' => $params,
                'message' => $e->getMessage(),
            ];
            $level = in_array($e->getCode(),[208002,208003,208004,208005,208006,208007,208008,100112,100100]) ? 'info' : 'error';
            $logger->write_log('api_client_result exception:'.json_encode($info,JSON_UNESCAPED_UNICODE) . ' code:'.$e->getCode(), $level);
            $result['error'] = $e->getMessage();
            $result['code'] = $e->getCode();
        }
        return $result;
    }

}
