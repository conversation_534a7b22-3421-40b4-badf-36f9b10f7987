<?php
namespace FlashExpress\bi\App\library;

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Server\SettingEnvServer;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\RequestOptions;
use Phalcon\Mvc\User\Component;

class RestClient extends Component
{
    const METHOD_GET = 'get';
    const METHOD_POST = 'post';
    const METHOD_PUT = 'put';
    const METHOD_DELETE = 'delete';

    /**
     * @var Client
     */
    private $client;

    /**
     * RestClient constructor.
     * @param $sys
     * @param int $timeout
     */
    public function __construct($sys, $timeout = 5)
    {
        $base = $this->getSysEndpoint($sys);
        $config = [
            'base_uri' => $base,
            'timeout' => $timeout,
        ];
        $this->client = new Client($config);
    }

    /**
     * @param $sys
     * @return string
     */
    private function getSysEndpoint($sys)
    {
        switch ($sys) {
            case 'by':
                $endpoint = env('by_rest_endpoint');
                break;
            case 'flashai':
                $endpoint = env('flashai_rest_endpoint');
                break;
            case 'oms':
                $endpoint = env('oms_rest_endpoint');
                break;
            case 'fau':
                $endpoint = env('fau_rest_endpoint');
                break;
            case 'nws': //地址
                $endpoint = env('java_http_url');
                break;
            case 'chat_buddy'://客服系统
                $endpoint = env('chat_buddy_endpoint');
                break;
            case 'pms': //地址
                $endpoint = env('java_pms_url');
                break;
            case 'fra':
                $endpoint = env('fra_rest_endpoint');
                break;
            case 'fms':
                $setting_env = new SettingEnvServer();
                $endpoint = $setting_env->getSetVal('fms_http_url');
                break;
            case 'tms':
                $setting_env = new SettingEnvServer();
                $endpoint = $setting_env->getSetVal('turbo_route_http_url');
                break;
            default:
                $endpoint = $sys;
                break;
        }

        return $endpoint;
    }

    /**
     * @param $method
     * @param $path
     * @param array $params
     * @param array $headers
     * @return array|mixed
     */
    public function execute($method, $path, $params = [], $headers = [])
    {
        $data = [];
        try {
            $opts = [];
            if (!empty($headers)) {
                $opts[RequestOptions::HEADERS] = $headers;
            }
            switch ($method) {
                case self::METHOD_GET:
                    $opts[RequestOptions::QUERY] = $params;
                    $response                    = $this->client->get($path, $opts);
                    break;
                case self::METHOD_POST:
                    $opts[RequestOptions::JSON] = $params;
                    $response                   = $this->client->post($path, $opts);
                    break;
                case self::METHOD_PUT:
                    $opts[RequestOptions::JSON] = $params;
                    $response                   = $this->client->put($path, $opts);
                    break;
                case self::METHOD_DELETE:
                    $opts[RequestOptions::JSON] = $params;
                    $response                   = $this->client->delete($path, $opts);
                    break;
                default:
                    throw new \RuntimeException('Method wrong');
            }
            $log = [
                'request'  => func_get_args(),
                'response' => [
                    'code'    => $response->getStatusCode(),
                    'headers' => $response->getHeaders(),
                    'body'    => (string)$response->getBody(),
                ],
            ];
            $this->logger->write_log($log, 'info');
            $data = json_decode($response->getBody(), true);

        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $data     = json_decode($response->getBody()->getContents(), true);
                $log = [
                    'request'  => func_get_args(),
                    'response' => $data,
                ];
                $this->logger->write_log($log, 'info');
            } else {
                $this->logger->write_log([
                    'base'      => (string)$this->client->getConfig('base_uri'),
                    'request'   => func_get_args(),
                    'params'    => $params,
                    'exception' => $e->getMessage(),
                    'file'      => $e->getFile(),
                    'line'      => $e->getLine(),
                ]);
            }
        } catch (GuzzleException $e) {
            
            $this->logger->write_log([
                'base'      => (string)$this->client->getConfig('base_uri'),
                'params'    => func_get_args(),
                'exception' => $e->getMessage(),
                'code'      => $e->getCode(),
            ], 'warning');
        }
        return $data;
    }


}