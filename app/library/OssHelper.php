<?php

namespace FlashExpress\bi\App\library;

use Exception;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\ToolServer;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Mvc\User\Component;

class OssHelper extends Component
{
    /**
     * @description 导出excel
     * @param $header
     * @param $rows
     * @param string $fileName
     * @return array
     * @throws Exception\BusinessException
     */
    public function exportExcel($header, $rows, $fileName = 'excel.xlsx')
    {
        if (!strstr($fileName, '.xlsx')) {
            $fileName = $fileName . '.xlsx';
        }
        $config = [
            'path' => sys_get_temp_dir(),
            //'path' => BASE_PATH . '/public/excel'
        ];
        $excel  = new \Vtiful\Kernel\Excel($config);
        // 此处会自动创建一个工作表
        $fileObject = $excel->fileName($fileName);
        $filePath   = $fileObject->header($header)->data($rows)->output();
        $file       = OssHelper::uploadFile($filePath);
        $url        = $file['object_url'];
        //$url = $this->config->application->baseUri.'/excel/'.basename($filePath);
        // 最后的最后，输出文件
        return ['code' => 1, 'data' => $url];
    }

    /**
     * @description 导出csv
     * @param $header
     * @param $rows
     * @param string $fileName
     * @return array
     * @throws ValidationException
     */
    public function exportSvc($header, $rows, $fileName = 'file.csv')
    {
        if (count($rows) > 1000000) {
            throw new ValidationException($this->getTranslation()->_('rows limit'), ErrCode::VALIDATE_ERROR);
        }

        $filePath = sys_get_temp_dir() . '/' . $fileName . '.csv';

        //打开php标准输出流
        $fp = fopen($filePath, 'a');
        //添加BOM头，以UTF8编码导出CSV文件，如果文件头未添加BOM头，打开会出现乱码。
        fwrite($fp, chr(0xEF) . chr(0xBB) . chr(0xBF));

        fputcsv($fp, $header);
        if (count($rows) > 0) {
            $page_size  = 2000;
            $row_values = array_chunk($rows, $page_size, true);
            foreach ($row_values as $item) {
                foreach ($item as $v) {
                    fputcsv($fp, $v);
                }
                if (ob_get_level() > 0) {
                    ob_flush();
                }
                flush();//必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
            }
        }
        fclose($fp);

        $file = OssHelper::uploadFile($filePath);
        $url  = $file['object_url'];

        return ['code' => 1, 'data' => $url];
    }

    /**
     * @param $path
     * @param bool $isTpl -- 如果是模板 不删除
     * @return array
     * @throws GuzzleException|Exception
     */
    public static function uploadFile($path, bool $isTpl = false): array
    {
        $filename = basename($path);

        if (isCountry(['vn', 'id'])) {
            $upload = self::uploadFileHcm('BACKYARD_UPLOAD',$filename);
        } else {
            $upload = self::uploadFileFle($filename);
        }

        $client   = new \GuzzleHttp\Client();
        $response = $client->request('PUT', $upload['put_url'], [
            'headers' => [
                'Content-Type' => $upload['content_type'],
            ],
            'body'    => file_get_contents($path),
        ]);
        if ($response->getStatusCode() == 200 && $isTpl === false) {
            unlink($path);
        }
        return [
            'file_name'   => $filename,
            'bucket_name' => $upload['bucket_name'],
            'object_key'  => $upload['object_key'],
            'object_url'  => $upload['object_url'],
        ];
    }

    /**
     * @throws Exception
     */
    public static function uploadFileHcm($biz_type,$filename)
    {
        $apiClient = new ApiClient('hcm_rpc', '', 'buildPutObjectUrl', 'en');
        $apiClient->setParams(['biz_type' => $biz_type, 'filename' => $filename]);
        $return = $apiClient->execute();
        if (isset($return['result'])) {
            return $return['result'];
        }
        throw new Exception('hcm oss buildPutObjectUrl error');
    }

    /**
     * @throws Exception
     */
    public static function uploadFileFle($filename)
    {
        //调用fle提供的OSS接口
        $ac = new ApiClient('fle', 'com.flashexpress.fle.svc.api.OssSvc', 'buildPutObjectUrl');
        $ac->setParam(
            [
                'WORK_ORDER',
                $filename,
                ' ',
            ]
        );
        $return = $ac->execute();
        if (isset($return['result'])) {
            return $return['result'];
        }
        throw new Exception('java oss buildPutObjectUrl error');
    }

}