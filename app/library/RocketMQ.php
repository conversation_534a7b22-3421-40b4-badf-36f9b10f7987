<?php

namespace FlashExpress\bi\App\library;


use FlashExpress\bi\App\Server\RockMqRetryServer;
use GuzzleHttp\Exception\GuzzleException;
use MQ\Exception\AckMessageException;
use MQ\Exception\MessageNotExistException;
use MQ\Model\TopicMessage;
use MQ\MQClient;
use MQ\MQConsumer;
use MQ\MQProducer;
use Phalcon\Mvc\User\Component;
use Exception;
class RocketMQ extends Component
{
    private $client;
    private $instanceId;
    private $topic;
    private $groupId;
    private $tag = null;
    private $shardingKey = null;
    private $sys_tg;//路由策略

    private $type = '';
    private $handleType = '';

    protected $logger;

    const TAG_NAME_PICKUP_CLAIMS = 'PICKUP_CLAIMS';                             //ms:网点理赔 消费
    const TAG_NAME_PICKUP_CLAIMS_RESULT = 'PICKUP_CLAIMS_RESULT';               //ms:网点理赔，生产
    const TAG_NAME_PUNCH_IN_AGAIN = 'PUNCH_IN_AGAIN';                           //FBI:处罚业务，监听员工补卡，进行处罚数据重算
    const TAG_NAME_STAFF_CLOCK = 'CLOCK_WORK_OR_OUT';                           //员工打卡
    const TAG_NAME_SEND_INVOICE = 'SEND_INVOICE';                               //个人代理发送invoice
    const TAG_HR_MODIFY_STAFF_SIGNING_STATUS = 'HR_MODIFY_STAFF_SIGNING_STATUS';//合同
    const TAG_SEND_D_SHEET_MOBILE_DC = 'PUNCH-OUT';                             //mobile dc 下班打卡 发送 dsheet （目前只有菲律宾）
    const TAG_SIGN_MOBILE_DC = 'SIGN';                                          //mobile dc 签字dsheet 回调fbi（目前只有菲律宾）
    const TAG_NAME_RENEW_CONTRACT = 'RENEW_CONTRACT_BUSINESS';                     //个人代理续约合同（目前只有菲律宾）
    const TAG_INCENTIVE_COURIER_PUNCH_OUT = 'INCENTIVE_COURIER_PUNCH_OUT';      //快递员打卡通知提成业务
    const TAG_DETECT_CHEATING = 'DETECT_CHEATING';                              //检验作弊
    const TAG_BATCH_APPROVAL_OVERTIME = 'BATCH_APPROVAL_OVERTIME';//oa 来源 批量审批加班
    const TAG_ANNUAL_LEAVE = 'ANNUAL_LEAVE_ADD';//给工号年假额度 初始化
    const TAG_BATCH_APPROVAL_OS_OVERTIME = 'BATCH_APPROVAL_OS_OVERTIME';//oa 来源 批量审批加班
    const TAG_ATTENDANCE_PENALTY = 'ATTENDANCE_PENALTY';//菲律宾 考勤处罚
    const TAG_CAL_SHORT_NOTICE = 'CAL_SHORT_NOTICE';//计算short notice
    const TAG_KIT_ADD_APPEAL = 'KIT_ADD_APPEAL';//Kit 申诉 创建by审批流
    const TAG_BY_APPEAL_CREATED_SUCCESS = 'BY_APPEAL_CREATED_SUCCESS';//by审批流创建成功，审批流id同步fbi
    const TAG_BY_APPEAL_RESULT = 'BY_APPEAL_RESULT';//by审批流结束，审批结果同步fbi
    const TAG_AUDIT_LIST_SUMMARY_UPDATE = 'AUDIT_LIST_SUMMARY_UPDATE'; //异步更新审批列表
    const TAG_CREATE_AUDIT_DELAY = 'CREATE_AUDIT_DELAY'; //创建延时审批
    const TAG_CHECK_STAFF_FACE_BLACKLIST = 'CHECK_STAFF_FACE_BLACKLIST'; //菲律宾人脸黑名单
    const TAG_STAFF_ADD_FACE_BLACKLIST = 'STAFF_ADD_FACE_BLACKLIST'; //人脸黑名单

    const TAG_PARCEL_N_RECEIVE = 'PARCEL_N_RECEIVE'; //已签收未收到包裹消息
    const TAG_SUPPORT_OVERTIME_SHIFT = 'SUPPORT_OVERTIME_SHIFT';//hcm 操作申请和撤销支援 修改ot 时间
    const TAG_PROBATION_TARGET_MSG = 'PROBATION_TARGET_MSG';//试用期员工发送后操作
    const TAG_HR_STAFF_UPDATE = 'HR_STAFF_UPDATE';//员工信息更新
    const TAG_FLEET_TMP_LINE_STATISTICS_SAVE = 'TMP_LINE_STATISTICS_SAVE';//加班车同步 撤销驳回 信息

    private $is_retry = false;
    public function setIsRetry($is_retry)
    {
        $this->is_retry = $is_retry;
    }


    public function getTopic()
    {
        return $this->topic;
    }

    public function getGroupId()
    {
        return $this->groupId;
    }

    public function getInstanceId()
    {
        return $this->instanceId;
    }

    public function setShardingKey($shardingKey)
    {
        $this->shardingKey = $shardingKey;
    }

    /**
     * message-push/
     *
     * RocketMQ constructor.
     * @param $tg
     */
    public function __construct($tg)
    {
        $this->logger = $this->getDI()->get('logger');
        if (!$this->getSysTopicGroup($tg)) {
           throw new Exception('topic group is not exist');
        }
        $this->client = new MQClient(
        // 设置HTTP接入域名（此处以公共云生产环境为例）
            $this->config->rocket_mq->http_endpoint,
            // AccessKey 阿里云身份验证，在阿里云服务器管理控制台创建
            $this->config->rocket_mq->access,
            // SecretKey 阿里云身份验证，在阿里云服务器管理控制台创建
            $this->config->rocket_mq->secret
        );
        // Topic所属实例ID，默认实例为空NULL
        $this->instanceId = $this->config->rocket_mq->instance_id;
    }

    /**
     * @param $tg
     * @return string
     */
    private function getSysTopicGroup($tg)
    {
        if (empty($tg)) {
            return false;
        }
        $this->sys_tg = $tg;
        switch ($tg) {
            case 'message-push':
                $this->topic   = env('rmq_topic_by_message_push', 'dev-by-message-push');
                $this->groupId = env('rmq_groupid_by_message_push', 'GID-dev-by-message-push');
                break;
            case 'create-approval':
                $this->topic   = env('rmq_topic_by_create_approval', 'dev-by-create-approval');
                $this->groupId = env('rmq_groupid_by_create_approval', 'GID-dev-by-create-approval');
                break;
            case 'cancel-pdpa':
                $this->topic   = env('rmq_topic_by_msg_cancel_pdpa', 'dev-by-msg-cancel-pdpa');
                $this->groupId = env('rmq_groupid_by_msg_cancel_pdpa', 'GID-dev-by-msg-cancel-pdpa');
                break;
            case 'staff-leave-email':
                $this->topic   = env('rmq_topic_hr_staff_leave', 'dev-hris-hr-staff-leave');
                $this->groupId = env('rmq_groupid_hris_staff_email', 'GID-dev-hris-staff-email');
                break;
            case 'do-job-transfer':
                $this->topic   = env('rmq_topic_by_job_transfer', 'dev-by-job-transfer');
                $this->groupId = env('rmq_groupid_by_job_transfer', 'GID-dev-by-job-transfer');
                break;
            case 'attendance-num'://打卡总数
                $this->topic   = env('rmq_topic_by_attendance_num', 'dev-by-attendance-num');
                $this->groupId = env('rmq_groupid_by_attendance_num', 'GID-dev-by-attendance-num');
                break;
            case 'attendance-fail'://打卡失败总数
                $this->topic   = env('rmq_topic_by_attendance_fail', 'dev-by-attendance-fail');
                $this->groupId = env('rmq_groupid_by_attendance_fail', 'GID-dev-by-attendance-fail');
                break;
            case 'hr-contract-add':
                $this->topic   = env('rmq_topic_hr-contract-add', 'dev-no-hr-contract-add');
                $this->groupId = env('rmq_groupid_hr-contract-add', 'GID-dev-no-hr-contract-add');
                break;
            case 'store_claimer_consumer'://网点理赔 消费
                $this->topic   = env('rmq_topic_by_store_claimer', 'dev-no-async-by');
                $this->groupId = env('rmq_groupid_by_store_claimer', 'GID-dev-no-async-by-claims');
                $this->tag     = self::TAG_NAME_PICKUP_CLAIMS;
                break;
            case 'incentive_courier_punch_out'://快递员下班通知提成
                $this->topic   = get_runtime() .'-no-async-by';
                $this->tag     = self::TAG_INCENTIVE_COURIER_PUNCH_OUT;
                break;
            case 'store_claimer_audit_producer'://网点理赔 审批 生产
                $this->topic   = env('rmq_topic_by_store_claimer', 'dev-no-async-by');
                $this->groupId = env('rmq_groupid_by_store_claimer_audit', 'GID-dev-no-async-by-claims-result');
                $this->tag     = self::TAG_NAME_PICKUP_CLAIMS_RESULT;
                break;
            case 'card_replacement_producer'://FBI处罚业务，监听员工补卡，进行处罚数据重算->生产者
//                $this->topic = env('rmq_topic_by_card_replacement', 'dev-no-by-clockIn');
                $this->topic = env('rmq_topic_by_card_replacement', get_runtime() . '-no-by-clockIn');
                //$this->groupId = env('rmq_groupid_by_card_replacement','GID-dev-bi-buka');
                $this->tag = self::TAG_NAME_PUNCH_IN_AGAIN;
                break;
            case 'staff_clock'://快递员上下班打卡 生产
                $this->topic = env('rmq_topic_by_store_claimer', 'dev-no-async-by');
                //$this->groupId = env('rmq_groupid_by_store_claimer_audit','GID-dev-no-async-by-claims-result');
                $this->tag = self::TAG_NAME_STAFF_CLOCK;
                break;
            case 'send_invoice'://个人代理invoice
                $this->topic   = env('rmq_topic_by_store_claimer', 'dev-no-async-by');
                $this->groupId = env('rmq_groupid_by_send_invoice', 'GID-dev-send_invoice');
                $this->tag     = self::TAG_NAME_SEND_INVOICE;
                break;
            case 'staff-contract-change-to-java':
                $this->topic = env('rmq_topic_po_async_process', 'dev-po-async-process');
                $this->tag   = self::TAG_HR_MODIFY_STAFF_SIGNING_STATUS;
                break;

            case 'click_out_send_dsheet'://菲律宾 移动dc 发送dsheet 对接 fbi
                $this->topic = env('rmq_topic_mobile_dc_dsheet', get_runtime() . '-no-mobile-dc-settlement');
                $this->tag = self::TAG_SEND_D_SHEET_MOBILE_DC;
                break;
            case 'message_sign_dsheet'://菲律宾 移动dc 签字回调fbi
                $this->topic = env('rmq_topic_mobile_dc_dsheet', get_runtime() . '-no-mobile-dc-settlement');
                $this->tag = self::TAG_SIGN_MOBILE_DC;
                break;
            case 'renew-contract-business'://通用队列 （用于 菲律宾 个人代理 续约 不续约 合同）
                $this->topic = env('rmq_topic_no_hris_common', get_runtime() . '-no-hris-common');
                $this->groupId = env('rmq_groupid_renew-contract-business', 'GID-'. get_runtime() .'-renew-contract-business');
                $this->tag = self::TAG_NAME_RENEW_CONTRACT;
                break;
            case 'cheating_detect'://通用队列 （用于检测快递作弊）
                $this->topic = env('rmq_topic_no_hris_common', get_runtime() . '-no-hris-common');
                $this->groupId = env('rmq_groupid_no_hris_common', 'GID-'. get_runtime() .'-no-hris-common');
                $this->tag = self::TAG_DETECT_CHEATING;
                break;
            case 'overtime-batch-approval'://批量审批加班
                $this->topic = env('rmq_topic_no_hris_common', get_runtime() . '-no-hris-common');//无序队列
                $this->groupId = env('rmq_groupid_overtime-batch-approval', 'GID-'. get_runtime() .'-overtime-batch-approval');
                $this->tag = self::TAG_BATCH_APPROVAL_OVERTIME;
                break;
            case 'os-ot-batch-approval'://批量审批加班(外协)
                $this->topic = env('rmq_topic_no_hris_common', get_runtime() . '-no-hris-common');//无序队列
                $this->groupId = env('rmq_groupid_os-ot-batch-approval', 'GID-'. get_runtime() .'-os-ot-batch-approval');
                $this->tag = self::TAG_BATCH_APPROVAL_OS_OVERTIME;
                break;
            case 'attendance-penalty'://菲律宾-考勤处罚
                $this->topic   = sprintf('%s-po-hris-common', get_runtime());//有序队列
                $this->groupId = sprintf('GID-%s-po-hris-att-penalty', get_runtime());
                $this->tag     = self::TAG_ATTENDANCE_PENALTY;
                break;
            case 'cal-short-notice'://马来-计算short notice
                $this->topic = get_runtime() . '-no-hris-common';
                $this->tag   = self::TAG_CAL_SHORT_NOTICE;
                break;
            case 'annual-leave-days'://给工号增加年假额度初始化 额度0
                $this->topic = env('rmq_topic_no_hris_common', get_runtime() . '-no-hris-common');
                $this->groupId = env('rmq_groupid_annual-leave-days', 'GID-'. get_runtime() .'-annual-leave-days');
                $this->tag = self::TAG_ANNUAL_LEAVE;
                break;
            case 'audit-list-update'://审批列表更新概要信息
                $this->topic = get_runtime() . '-no-hris-common';
                $this->groupId = env('rmq_groupid_audit-list-update', 'GID-'. get_runtime() .'-audit-list-update');
                $this->tag   = self::TAG_AUDIT_LIST_SUMMARY_UPDATE;
                break;
            case 'create-audit-delay':
                $this->topic   = get_runtime() . '-no-hris-common';
                $this->groupId = sprintf('GID-%s-create-audit-delay', get_runtime());
                $this->tag     = self::TAG_CREATE_AUDIT_DELAY;
                break;
            case 'penalty-appeal-add': // Kit 申诉 创建by审批流
                $this->topic   = sprintf('%s-no-bi-punish', get_runtime());//有序队列
                $this->groupId = sprintf('GID-%s-no-bi-punish-by', get_runtime());
                $this->tag = self::TAG_KIT_ADD_APPEAL;
                break;
            case 'penalty-appeal-success': // by审批流创建成功，审批流id同步fbi
                $this->topic   = sprintf('%s-no-bi-punish', get_runtime());//有序队列
                $this->groupId = sprintf('GID-%s-no-bi-punish-bi', get_runtime());
                $this->tag = self::TAG_BY_APPEAL_CREATED_SUCCESS;
                break;
            case 'penalty-appeal-result': // by审批流结束，审批结果同步fbi
                $this->topic   = sprintf('%s-no-bi-punish', get_runtime());//有序队列
                $this->groupId = sprintf('GID-%s-no-bi-punish-bi', get_runtime());
                $this->tag = self::TAG_BY_APPEAL_RESULT;
                break;
            case 'staff-face-blacklist': // 菲律宾 人脸黑名单
                $this->topic   = sprintf('%s-po-hris-common', get_runtime());
                $this->groupId = sprintf('GID-%s-po-hr-check-face-black', get_runtime());
                $this->tag     = self::TAG_CHECK_STAFF_FACE_BLACKLIST;
                break;
            case 'add-face-blacklist'://加入人脸黑名单
                $this->topic   = sprintf('%s-po-hris-common', get_runtime());
                $this->groupId = sprintf('GID-%s-po-hr-add-face-black', get_runtime());
                $this->tag     = self::TAG_STAFF_ADD_FACE_BLACKLIST;
                break;
            case 'un-receive-parcel'://已签收未收到包裹
                $this->topic   = sprintf('%s-po-hris-common', get_runtime());//有序队列
                $this->groupId = sprintf('GID-%s-po-parcel-n-receive', get_runtime());
                $this->tag     = self::TAG_PARCEL_N_RECEIVE;
                break;
            case 'probation-target-msg'://这个是发送试用期发送bp签字使用
                $this->topic   = sprintf('%s-po-hris-common', get_runtime());//有序队列
                $this->groupId = sprintf('GID-%s-hr-add-probation', get_runtime());
                $this->tag     = self::TAG_PROBATION_TARGET_MSG;
                break;
            case 'ot-support-shift'://菲律宾支援修改加班时间
                $this->topic   = sprintf('%s-po-hris-common', get_runtime());//有序队列
                $this->groupId = sprintf('GID-%s-ot-support-shift', get_runtime());
                $this->tag = self::TAG_SUPPORT_OVERTIME_SHIFT;
                break;
            case 'update-staff-info'://更改员工信息
                $this->topic   = sprintf('%s-po-async-process', get_runtime());
                $this->groupId = sprintf('GID-%s-po-hris-staff-update', get_runtime());
                $this->tag     = self::TAG_HR_STAFF_UPDATE;
                break;
            case 'sync-fleet-audit-status'://生产：同步加班车 驳回 撤销状态
                $this->topic   = sprintf('%s-no-async-process', get_runtime());
                $this->tag     = self::TAG_FLEET_TMP_LINE_STATISTICS_SAVE;
                break;
            default:
                return false;
        }

        return true;
    }

    public function getClient()
    {
        return $this->client;
    }

    /**
     * @return MQConsumer
     */
    public function getConsumer()
    {
        return $this->client->getConsumer($this->instanceId, $this->topic, $this->groupId, $this->tag);
    }

    /**
     * @return MQProducer
     */
    public function getProducer()
    {
        return $this->client->getProducer($this->instanceId, $this->topic);
    }

    /**
     * rmq: 向指定队列 - 写入消息数据
     * @param array $data 消息数据结构  示例： ['data'=>'xxxx']
     * @param $timeInMillis
     * @return mixed
     */
    public function sendToMsg(array $data, $timeInMillis = 0)
    {
        if (empty($data)) {
            return false;
        }

        //TODO 发送的消息体
        $params           = [];
        $params['locale'] = 'en';
        $params['data']   = $data;
        if (!empty($this->type)) {
            $params['type'] = $this->type;
        }
        if (!empty($this->handleType)) {
            $params['handleType'] = $this->handleType;
        }

        try {
            // 2. send message
            $messageBody = json_encode($params);
            $messageBody = base64_encode($messageBody);

            $result     = $this->publishMessage($messageBody, $timeInMillis);
            $message_id = $result->getMessageId() ?? '';;
            $this->logger->write_log(['params' => $params, 'message_id' => $message_id, 'sys' => 'sendToMsg'],
                'info');
            return $message_id;
        } catch (Exception | GuzzleException $e) {
            $this->logger->write_log(['sys'    => 'sendToMsg',
                                      'params' => $params,
                                      'error'  => $e->getTraceAsString() . $e->getMessage(),
            ], 'error');

            if (!$this->is_retry) {
                RockMqRetryServer::insertRecord($this->sys_tg, $this->type, __FUNCTION__, $data, $timeInMillis,$this->shardingKey);
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     * 生产
     * @param $messageBody
     * @param int $timeInMillis
     * @return mixed
     */
    public function publishMessage($messageBody, $timeInMillis = 0)
    {
        $producer = $this->getProducer();

        //3 topic body
        $publishMessage = new TopicMessage(
            $messageBody// 消息内容
        );
        // 设置属性
        //$publishMessage->putProperty("a", $i);
        // 设置消息KEY
        //$publishMessage->setMessageKey("MessageKey");
        // 定时消息, 定时时间为10s后
        $publishMessage->setStartDeliverTime(time() * 1000 + $timeInMillis * 1000);

        //同一个 topic 区分 tag
        if (!empty($this->tag)) {
            $publishMessage->setMessageTag($this->tag);
        }

        //设置分区key
        if ($this->shardingKey) {
            $publishMessage->setShardingKey($this->shardingKey);
        }

        return $producer->publishMessage($publishMessage);
    }

    /**
     *
     * rmq: 向指定队列 - 写入消息数据 对接MS 生产消息
     * @param array $data 消息数据结构  示例： ['data'=>'xxxx','type'=> 'xxxx']
     * @param $timeInMillis
     * @return bool|string
     */
    public function sendMsgByTag(array $data, $timeInMillis = 0)
    {
        if (empty($data)) {
            return false;
        }

        //TODO 发送的消息体
        $params         = [];
        $params['data'] = json_encode($data);
        if (!empty($this->type)) {
            $params['type'] = $this->type;
        }
        if (!empty($this->handleType)) {
            $params['handleType'] = $this->handleType;
        }

        try {
            // 2. send message
            $messageBody = json_encode($params);
            $result      = $this->publishMessage($messageBody, $timeInMillis);
            $message_id  = $result->getMessageId() ?? '';
            $this->logger->write_log(['params' => $params, 'message_id' => $message_id, 'sys' => 'sendMsgByTag'],
                'info');
            return $message_id;
        } catch (Exception | GuzzleException $e) {
            $this->logger->write_log(['sys'    => 'sendMsgByTag',
                                      'params' => $params,
                                      'error'  => $e->getTraceAsString() . $e->getMessage(),
            ],
                'error');
            if (!$this->is_retry) {
                RockMqRetryServer::insertRecord($this->sys_tg, $this->type, __FUNCTION__, $data, $timeInMillis,$this->shardingKey);
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     * 设置type 与 tag 一致
     * @param $type
     */
    public function setType($type)
    {
        $this->type = $type;
    }

    /**
     * 设置type 与 tag 一致
     * 目前只有fbi那边有 配合使用 sendToMsg
     * @param $handleType
     */
    public function setHandleType($handleType)
    {
        $this->handleType = $handleType;
    }

    /**
     * @param $num
     * @param $waitSec
     * @return \MQ\Model\Message|void
     */
    public function receiveMsg($num, $waitSec)
    {
        try {
            $consumer = $this->getConsumer();
            $messages = $consumer->consumeMessage($num, $waitSec);
        } catch (MessageNotExistException $e) {
            return;
        } catch (\Exception $e) {
            $this->logger->write_log(
                'instanceId:' . $this->instanceId . ' topic:' . $this->getTopic() . ' groupId:' . $this->getGroupId() . ' Mq Exception : ' . $e->getMessage(),
                'error'
            );
            return;
        }

        return $messages;
    }

    /**
     * @param $receiptHandles
     * @return void
     */
    public function ackMsg($receiptHandles)
    {
        try {
            $consumer = $this->getConsumer();
            $consumer->ackMessage($receiptHandles);
            $this->logger->write_log('consumer ack Succeed!' . json_encode($receiptHandles), 'info');
        } catch (AckMessageException $e) {
            $this->logger->write_log('Ack Error, RequestId:' . $e->getRequestId(), 'error');
            foreach ($e->getAckMessageErrorItems() as $errorItem) {
                $this->logger->write_log(
                    sprintf(
                        'ReceiptHandle:%s, ErrorCode:%s, ErrorMsg:%s',
                        $errorItem->getReceiptHandle(),
                        $errorItem->getErrorCode(),
                        $errorItem->getErrorCode()
                    ),
                    'error'
                );
            }
        } catch (\Exception $e) {
            $this->logger->write_log('Ack Exception : ' . $e->getMessage(), 'error');
        }
    }


    /**
     *
     * rmq: 向指定队列 - 写入消息数据 对接MS 生产消息
     * @param array $data 消息数据结构  示例： ['jsonCondition'=>'xxxx','handleType'=> 'xxxx']
     * @param int $timeInMillis
     * @return bool|string
     */
    public function sendOrderlyMsg(array $data, int $timeInMillis = 0)
    {
        if (empty($data)) {
            return false;
        }
        try {
            $producer = $this->getProducer();

            $messageBody = json_encode($data);
            //3 topic body
            $publishMessage = new TopicMessage(
                $messageBody// 消息内容
            );
            // 定时消息, 定时时间为10s后
            $publishMessage->setStartDeliverTime(time() * 1000 + $timeInMillis * 1000);
            //同一个 topic 区分 tag
            if (!empty($this->tag)) {
                $publishMessage->setMessageTag($this->tag);
            }
            //设置分区key
            if ($this->shardingKey) {
                $publishMessage->setShardingKey($this->shardingKey);
            }
            $result     = $producer->publishMessage($publishMessage);
            $message_id = $result->getMessageId() ?? '';
            $this->logger->write_log(['params' => $data, 'message_id' => $message_id, 'sys' => 'sendOrderlyMsg'],
                'info');
            return $message_id;
        } catch (Exception | GuzzleException $e) {
            $this->logger->write_log(['sys'    => 'sendOrderlyMsg',
                                      'params' => $data,
                                      'error'  => $e->getTraceAsString() . $e->getMessage(),
            ],
                'error');
            if (!$this->is_retry) {
                RockMqRetryServer::insertRecord($this->sys_tg, $this->type, __FUNCTION__, $data, $timeInMillis,$this->shardingKey);
                return true;
            } else {
                return false;
            }
        }
    }

}