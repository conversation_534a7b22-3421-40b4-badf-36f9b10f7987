<?php
namespace phpHMS;

use php<PERSON><PERSON>\Recipient\Recipient;
use phpH<PERSON>\Recipient\Topic;
use phpH<PERSON>\Recipient\Device;

/**
 * <AUTHOR>
 */
class Message implements \JsonSerializable
{
    const PRIORITY_HIGH = 'high',
        PRIORITY_NORMAL = 'normal';

    private $notification;
    private $collapseKey;

    /**
     * set priority to "high" by default. Otherwise iOS push notifications (apns) will not wake up app
     *
     * @var string
     */
    private $priority = self::PRIORITY_HIGH;
    private $data;
    /** @var Recipient[] */
    private $recipients = array();
    private $recipientType;
    private $timeToLive;
    private $delayWhileIdle;

    /** @var string */
    private $deviceType;

    public function __construct($deviceType)
    {
        if (!in_array($deviceType,[1,2]))
            throw new \UnexpectedValueException('currently phpHMS only supports 1:google or 2:HuaWei device type');

        $this->deviceType = $deviceType;
        return $this;
    }

    /**
     * where should the message go
     *
     * @param Recipient $recipient
     * @throws \UnexpectedValueException
     * @throws \InvalidArgumentException
     *
     * @return \phpHMS\Message
     */
    public function addRecipient(Recipient $recipient)
    {
        if (!$recipient instanceof Device && !$recipient instanceof Topic) {
            throw new \UnexpectedValueException('currently phpFCM only supports topic and single device messages');
        }

        if (!isset($this->recipientType)) {
            $this->recipientType = get_class($recipient);
        }

        if ($this->recipientType !== get_class($recipient)) {
            throw new \InvalidArgumentException('mixed recepient types are not supported by FCM');
        }

        $this->recipients[] = $recipient;
        return $this;
    }

    public function setNotification(Notification $notification)
    {
        $this->notification = $notification;
        return $this;
    }

    /**
     * @see https://firebase.google.com/docs/cloud-messaging/concept-options#collapsible_and_non-collapsible_messages
     *
     * @param string $collapseKey
     *
     * @return \phpHMS\Message
     */
    public function setCollapseKey($collapseKey)
    {
        $this->collapseKey = $collapseKey;
        return $this;
    }

    /**
     * normal or high, use class constants as value
     * @see https://firebase.google.com/docs/cloud-messaging/concept-options#setting-the-priority-of-a-message
     *
     * @param string $priority use the class constants
     *
     * @return \phpHMS\Message
     */
    public function setPriority($priority)
    {
        $this->priority = $priority;
        return $this;
    }

    /**
     *
     * @param integer $ttl
     *
     * @return \phpHMS\Message
     */
    public function setTimeToLive($ttl)
    {
        $this->timeToLive = $ttl;

        return $this;
    }

    /**
     *
     * @param bool $delayWhileIdle
     *
     * @return \phpHMS\Message
     */
    public function setDelayWhileIdle($delayWhileIdle)
    {
        $this->delayWhileIdle = $delayWhileIdle;
        return $this;
    }

    public function setData(array $data)
    {
        $this->data = $data;
        return $this;
    }

    public function jsonSerialize()
    {
        return $this->deviceType === 2 ? $this->jsonSerializeHuaWei() : $this->jsonSerializeGoogle();
    }

    private function jsonSerializeGoogle()
    {
        $jsonData = array();

        if (empty($this->recipients)) {
            throw new \UnexpectedValueException('message must have at least one recipient');
        }

        $this->createToGoogle($jsonData);
        if ($this->collapseKey) {
            $jsonData['collapse_key'] = $this->collapseKey;
        }
        if ($this->data) {
            $jsonData['data'] = $this->data;
        }
        if ($this->priority) {
            $jsonData['priority'] = $this->priority;
        }
        if ($this->notification) {
            $jsonData['notification'] = $this->notification;
        }
        if ($this->timeToLive) {
            $jsonData['time_to_live'] = (int)$this->timeToLive;
        }
        if ($this->delayWhileIdle) {
            $jsonData['delay_while_idle'] = (bool)$this->delayWhileIdle;
        }

        return $jsonData;
    }

    private function jsonSerializeHuaWei()
    {
        $jsonData = array();

        if (empty($this->recipients)) {
            throw new \UnexpectedValueException('message must have at least one recipient');
        }

        if ($this->collapseKey) {
            $jsonData['collapse_key'] = $this->collapseKey;
        }
        if ($this->data) {
            $jsonData['data'] = $this->data;
        }
        if ($this->priority) {
            $jsonData['priority'] = $this->priority;
        }
        if ($this->timeToLive) {
            $jsonData['time_to_live'] = (int)$this->timeToLive;
        }
        if ($this->delayWhileIdle) {
            $jsonData['delay_while_idle'] = (bool)$this->delayWhileIdle;
        }

        $jsonData['message'] = [
            'notification' => $this->notification,
            'android' =>  [
                "notification" => [
                    "click_action" => [
                        "type"=> 1,
                        "intent"=> "#Intent;compo=com.rvr/.Activity;S.W=U;end"
                    ]
                ]
            ]
        ];
        $this->createToHuaWei($jsonData['message']);


        return $jsonData;
    }

    private function createToHuaWei(array &$jsonData)
    {
        switch ($this->recipientType) {
            case Topic::class:
                if (count($this->recipients) > 1) {
                    $topics = array_map(
                        function (Topic $topic) { return sprintf("'%s' in topics", $topic->getIdentifier()); },
                        $this->recipients
                    );
                    $jsonData['condition'] = implode(' || ', $topics);
                    break;
                }
                $jsonData['token'] = sprintf('/topics/%s', current($this->recipients)->getIdentifier());
                break;
            default:
                if (count($this->recipients) === 1) {
                    $jsonData['token'] = [current($this->recipients)->getIdentifier()];
                } elseif(count($this->recipients) > 1) {
                    $jsonData['token'] = array();

                    foreach($this->recipients as $recipient) {
                        $jsonData['token'][] = $recipient->getIdentifier();
                    }
                }
        }
    }

    private function createToGoogle(array &$jsonData)
    {
        switch ($this->recipientType) {
            case Topic::class:
                if (count($this->recipients) > 1) {
                    $topics = array_map(
                        function (Topic $topic) { return sprintf("'%s' in topics", $topic->getIdentifier()); },
                        $this->recipients
                    );
                    $jsonData['condition'] = implode(' || ', $topics);
                    break;
                }
                $jsonData['to'] = sprintf('/topics/%s', current($this->recipients)->getIdentifier());
                break;
            default:
                if (count($this->recipients) === 1) {
                    $jsonData['to'] = current($this->recipients)->getIdentifier();
                } elseif(count($this->recipients) > 1) {
                    $jsonData['registration_ids'] = array();

                    foreach($this->recipients as $recipient) {
                        $jsonData['registration_ids'][] = $recipient->getIdentifier();
                    }
                }
        }
    }
}