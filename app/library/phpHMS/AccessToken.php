<?php
namespace phpHMS;

/**
 * <AUTHOR>
 */
class AccessToken
{

    private $appid;

    private $appsecret;

    private $token_expiredtime;

    private $access_token;

    const HW_TOKEN_SERVER = 'https://oauth-login.cloud.huawei.com/oauth2/v2/token';

    private $fields;


    public function __construct($appid, $appsecret,$access_token = null, $token_expiredtime = null)
    {
        $this->appid = $appid;
        $this->appsecret = $appsecret;
        $this->token_expiredtime = $token_expiredtime;
        $this->access_token = $access_token;
    }

    public function setAppId($value)
    {
        $this->appid = $value;
    }

    public function setAppSecret($value)
    {
        $this->appsecret = $value;
    }

    public function getApplicationFields()
    {
        $keys = array(
            'appid',
            'access_token',
            'token_expiredtime'
        );
        foreach ($keys as $key) {
            if (isset($this->$key)) {
                $this->fields[$key] = $this->$key;
            }
        }

        return $this->fields;
    }

    public function isTokenExpired()
    {
        if (empty($this->access_token)) {
            return true;
        }
        if (time() > $this->token_expiredtime) {
            return true;
        }
        return false;
    }

    private function refresh_token()
    {
        $result = json_decode($this->curl_https_post(self::HW_TOKEN_SERVER, http_build_query(array(
            "grant_type" => "client_credentials",
            "client_secret" => $this->appsecret,
            "client_id" => $this->appid
        )), array(
            "Content-Type: application/x-www-form-urlencoded;charset=utf-8"
        )));
        if ($result == null || ! array_key_exists("access_token", $result)) {
            return null;
        }
        $this->access_token = $result->access_token;
        $this->token_expiredtime = time() + $result->expires_in;
        return $this->access_token;
    }

    private function curl_https_post($url, $data = array(), $header = array())
    {
        $ch = curl_init($url);

        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);

        // resolve SSL: no alternative certificate subject name matches target host name
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0); // check verify
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_POST, 1); // regular post request
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data); // Post submit data

        $ret = @curl_exec($ch);
        if ($ret === false) {
            return null;
        }

        curl_close($ch);

        return $ret;
    }

    public function getAccessToken()
    {
        if ($this->isTokenExpired()) {
            $this->refresh_token();
        }

        return $this->getApplicationFields();
    }

}
