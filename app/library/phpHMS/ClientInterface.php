<?php
namespace phpHMS;

/**
 * <AUTHOR>
 */
interface ClientInterface
{

    /**
     * add your server api key here
     *
     * @param string $apiKey
     *
     * @return \phpHMS\Client
     */
    public function setApiKey($apiKey);


    /**
     * people can overwrite the api url with a proxy server url of their own
     *
     * @param string $url
     *
     * @return \phpHMS\Client
     */
    public function setProxyApiUrl($url);

    /**
     * sends your notification to the google servers and returns a guzzle repsonse object
     * containing their answer.
     *
     * @param Message $message
     *
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \GuzzleHttp\Exception\RequestException
     */
    public function send(Message $message);

}
