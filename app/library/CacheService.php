<?php

namespace FlashExpress\bi\App\library;

use Exception;
use FlashExpress\bi\App\library\Exception\ValidationException;
use Phalcon\Mvc\User\Component;

/**
 * 该方法是为了方便service层使用缓存
 * Class CacheService
 */
class CacheService extends Component
{
    public  $lang;
    public  $languagePack;
    private $_logger;
    private $_cache;

    public function __construct($lang)
    {
        $this->_logger      = $this->getDI()->get('logger');
        $this->languagePack = $this->getDI()->get('languagePack');
        $this->languagePack->setLanguage($lang);
        $this->lang = $this->languagePack->lang;
        if (RUNTIME == 'dev') {
            $this->expire1 = 1;
            $this->lockExpire = 1;
        }
        if (RUNTIME == 'tra') {
            $this->expire1 = 60;
        }
    }


    /**
     * 是否清除原子锁
     * @var bool 默认false
     */
    protected $isDelete = false;
    /**
     * 原子锁默认超时时间
     * @var int 单位s，默认1分钟
     */
    protected $lockExpire = 60;
    /**
     * 一级cache超时时间c
     * @var int 单位s，默认5分钟
     */
    protected $expire1 = 300;
    /**
     * 二级cache超时时间
     * @var int 单位s，默认一天
     */
    protected $expire2 = 86400;
    /**
     * 二级cache是否使用，默认不使用
     * @var bool 默认false
     */
    protected $secondaryFlag = false;
    /**
     * 是否使用cache，在开发环境或者调试阶段可以关闭cache
     * @var bool 默认true
     */
    protected $cacheFlag = true;
    /**
     * 随机的时间范围，对于设置的cache时间进行一定的随机，防止cache同时失效
     * @var int 单位s， 默认30，可以继承覆盖
     */
    protected $randomTime = 30;
    /**
     * 允许service设置拼接cacheKey的前缀，便于更新cache
     */
    protected $cacheKeyPrefix = '';
    /**
     * 允许用户只更新cache不读cache
     * 用于离线设置cache或者其他特殊场景
     */
    protected $onlySetFlag = false;
    /**
     * 允许用户只读cache不更新cache
     * 用于读取离线设置的cache数据，不访问后端，提高请求性能
     */
    protected $onlyGetFlag = false;
    /**
     * 允许用户刷新cache
     */
    protected $refreshFlag = false;
    /**
     * 是否开启接口清理缓存
     * @var bool
     */
    private $internalNetFlag = false;


    /**
     * 设置缓存的超时时间
     * @param $expire1 //一级cache超时时间，单位s
     * @param int $expire2 //二级cache超时时间，单位s
     */
    public function setExpire($expire1, int $expire2 = 86400): CacheService
    {
        $this->expire1 = $expire1;
        $this->expire2 = $expire2;
        return $this;
    }

    /**
     * 设置缓存前缀 可用于多语言
     * @param $cacheKeyPrefix
     * @return $this
     */
    public function setPrefix($cacheKeyPrefix): CacheService
    {
        $this->cacheKeyPrefix = $cacheKeyPrefix;
        return $this;
    }

    /**
     * 设置原子锁释放时间
     * @param int $expire 过期时间
     * @param bool $isDelete 是否清除
     * @return $this
     */
    public function setLockConf(int $expire = 60, bool $isDelete = false): CacheService
    {
        $this->lockExpire = $expire;
        $this->isDelete   = $isDelete;
        return $this;
    }

    /**
     * 设置开启二级缓存
     */
    public function useSecondaryCache()
    {
        $this->secondaryFlag = true;
    }

    /**
     * 关闭cache功能
     */
    public function closeCache()
    {
        $this->cacheFlag = false;
    }

    /**
     * 设置是否只读cache不写cache
     */
    public function setOnlyRead($flag = true)
    {
        $this->onlyGetFlag = $flag;
    }

    /**
     * 设置是否只写cache不读cache
     */
    public function setOnlyWrite($flag = true)
    {
        $this->onlySetFlag = $flag;
    }

    /**
     * 清除缓存
     */
    public function refreshCache()
    {
        $this->refreshFlag = true;
    }

    /**
     * 通用方法，从缓存中获取数据
     * 首先从一级缓存中取数据，没有从后端服务取数据
     * 如果从后端获取失败同时启用了二级缓存，会从二级缓存取数据，并更新一级缓存
     * 上述过程取数据都失败，返回null，并会打印错误日志
     *
     * @param string $cacheKey
     * @param string $method
     * @param array $params
     * @return false|mixed|void|null $data, null表示获取数据失败
     * @throws Exception
     */
    public function getDataFromCache(string $cacheKey, string $method, array $params = [])
    {
        $cacheKey1 = $this->cacheKeyPrefix.'cache1_'.$cacheKey;
        $cacheKey2 = $this->cacheKeyPrefix.'cache2_'.$cacheKey;
        //刷新缓存
        if ($this->refreshFlag) {
            $this->removeData($cacheKey1, $cacheKey2);
        }

        //从一级cache中读取数据
        if (!$this->onlySetFlag) {
            $data = $this->getDI()->get('redisLib')->get($cacheKey1); //取不到返回false
            if ($data) {
                return json_decode($data, true);
            }
        }
        $data = call_user_func_array([$this, $method], $params);
        if ($data !== null) {
            if (!$this->onlyGetFlag) {
                try {
                    $expire1 = $this->expire1 + rand(0, $this->randomTime);
                    $res     = $this->getDI()->get('redisLib')->set($cacheKey1, json_encode($data), $expire1);
                    if ($res != 1) {
                        $this->_logger->write_log("{$method} :Store {$cacheKey1} to cache1 error, code is ".$res);
                    }
                    if ($this->secondaryFlag) {
                        $expire2 = $this->expire2 + rand(0, $this->randomTime);
                        $res     = $this->getDI()->get('redisLib')->set($cacheKey2, json_encode($data), $expire2);
                        if ($res != 1) {
                            $this->_logger->write_log("{$method} :Store {$cacheKey2} to cache2 error, code is ".$res);
                        }
                    }
                } catch (Exception $e) {
                    $this->_logger->write_log("{$method} :Store to cache error: ".$e->getMessage());
                }
            }
            return $data;
        }
        //一级cache中没有，从后端获取失败，如果启用二级cache，从二级cache中取
        //$this->getDI()->get('redisLib')->set
        if ($this->secondaryFlag && !$this->onlySetFlag) {
            try {
                $data = $this->getDI()->get('redisLib')->get($cacheKey2);
                $this->_logger->write_log("{$method} :Get {$cacheKey2} from cache2!", 'info');
            } catch (Exception $e) {
                $data = null;
            }
            if ($data !== false) {
                try {
                    $expire1 = $this->expire1 + rand(0, $this->randomTime);
                    $res     = $this->getDI()->get('redisLib')->set($cacheKey1, $data, $expire1);
                    if ($res != 1) {
                        $this->_logger->write_log("{$method} :Store to cache1 from cache2 error, code is ".$res);
                    } else {
                        $this->_logger->write_log("{$method} :Store to cache1 from cache2!", 'info');
                    }
                } catch (Exception $e) {
                    $this->_logger->write_log("{$method} :Store to cache error! ".$e->getMessage());
                }
                return json_decode($data, true);
            }
        }
        //二级cache也没有取到数据，抛异常
        $this->_logger->write_log("{$method} :Get data from cache and server is error!");
        //throw new Exception("Get data from cache and server is error please check the method ".$method);//存在
    }

    /**
     * 通用方法，关闭缓存功能时，直接从后端服务获取数据
     *
     * @param string $method
     * @param array $params
     * @return false|mixed $data, null表示获取数据失败
     */
    public function getDataFromServer(string $method, array $params = [])
    {
        try {
            $data = call_user_func_array([$this, $method], $params);
        } catch (Exception $e) {
            throw new Exception("{$method} :Get data from server is error! ".$e->getMessage());
        }
        return $data;
    }


    /**
     * @throws \ReflectionException
     * @throws ValidationException
     * @throws Exception
     */
    public function __call($name, $args)
    {
        $isUseFromCache = strrpos($name, "FromCache");
        $isUseLock      = strrpos($name, "UseLock");

        if ($isUseFromCache !== false) {
            $callback = substr($name, 0, $isUseFromCache);
            $method   = $this->getClassFile(get_class($this))."::$callback";
            $key      = $this->lang.'_'.$method."_".serialize($args);
            $this->getManualParams();
            if ($this->cacheFlag) {
                $data = $this->getDataFromCache($key, $callback, $args);
            } else {
                $data = $this->getDataFromServer($callback, $args);
            }
            return $data;
        }

        if ($isUseLock !== false) {
            $callback = substr($name, 0, $isUseLock);
            $method   = $this->getClassFile(get_class($this))."::$callback";
            return $this->useLock(md5($method."_".serialize($args)), $callback, $args);
        }
        throw new Exception( sprintf('Call to undefined method %s %s::%s',$name,get_class($this),$name));
    }

    /**
     * 获取调用的service类所在的文件名，用来拼装cache的key
     * 防止多个项目中有相同的service类中定义相同的方法，造成key重复
     *
     * @param string $class 类名
     * @return string, 该类所在的文件完整路径
     */
    private function getClassFile(string $class): string
    {
        $fileName = $class.'.php';
        $files    = get_included_files();
        foreach ($files as $file) {
            if (substr_count($file, $fileName) > 0) {
                return $file;
            }
        }
        return $fileName;
    }

    /**
     * 清除缓存
     * @param $key1
     * @param $key2
     */
    private function removeData($key1, $key2)
    {
        try {
            $this->getDI()->get('redisLib')->del($key1);
        } catch (Exception $e) {
            $this->_logger->write_log("Remove {$key1} from cache exception, ".$e->getMessage());
        }
        if ($this->secondaryFlag) {
            try {
                $this->getDI()->get('redisLib')->del($key2);
            } catch (Exception $e) {
                $this->_logger->write_log("Remove {$key2} from cache exception, ".$e->getMessage());
            }
        }
    }

    /**
     * 接口清缓存
     */
    private function getManualParams()
    {
        //从url中获取设置的参数控制cache行为，只允许内网用户使用。优先级比代码中set函数设置的高
        if ($this->internalNetFlag) {
            if (isset($_GET['noCache'])) {
                $this->cacheFlag = false;
            }
            if (isset($_GET['removeCache'])) {
                $this->refreshFlag = true;
            }
            if (isset($_GET['onlySetCache'])) {
                $this->onlySetFlag = true;
            }
            if (isset($_GET['onlyGetCache'])) {
                $this->onlyGetFlag = true;
            }
        }
    }

    /**
     * @throws ValidationException
     * @throws Exception
     */
    public function useLock(string $key, string $method, array $params = [])
    {
        $redis     = $this->getDI()->get('redisLib');
        $LUASCRIPT = <<<LUA
local key = KEYS[1]
local ttl = ARGV[1]
if (redis.call('setnx', key, 1) == 1) then
    return redis.call('expire', key, ttl)
elseif (redis.call('ttl', key) == -1) then
    return redis.call('expire', key, ttl)
end
    return 0
LUA;
        $isLock    = $redis->eval($LUASCRIPT, [$key, $this->lockExpire], 1);
        if ($isLock) {
            try {
                $result = call_user_func_array([$this, $method], $params);
            } catch (Exception $e) {
                $redis->del($key);
                throw $e;
            }
            if ($this->isDelete) {
                $redis->del($key);
            }
            return $result;
        }
        throw new ValidationException($this->languagePack->getTranslation($this->lang)->_('ticket_repeat_msg'));
    }


}

