<?php

namespace FlashExpress\bi\App\library;

use Phalcon\Mvc\User\Component;

class RedisClient extends Component
{
    private static $instance;

    private $redisModel;

    private function __construct()
    {
        $this->redisModel = $this->getDI()->get('redisLib');
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || self::$instance instanceof self) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function getClient()
    {
        return $this->redisModel;
    }
}
