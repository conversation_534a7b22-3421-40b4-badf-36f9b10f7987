<?php

namespace FlashExpress\bi\App\library;


use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;

/**
 * 常用枚举数据
 * Class enums
 */
final class enums
{

    const RB_KIT = "RB_KIT";
    const FB_BACKYARD = "FB_BACKYARD";
    const CLIENT_HRIS = "CLIENT_HRIS";
    public static $sessionPrefix = 'session_';//token key


    /**
     * 语言能力 非一线
     * @var string[]
     */
    public static $languageAbility=[
        '1' => 'TH',
        '2' => 'CN',
        '3' => 'EN',
        '4' => 'CN-TH',
        '5' => 'CN-EN',
        '6' => 'EN-TH',
        '7' => 'CN-EN-TH',
    ];

    public static $country_conf = [
        'Th' => [
            'currency_symbol' => '฿',
            'currency_iso' => 'THB',
        ],
        'Ph' => [
            'currency_symbol' => '₱',
            'currency_iso' => 'PHP',
        ],
        'La' => [
            'currency_symbol' => '₭',
            'currency_iso' => 'LAK',
        ],
        'My' => [
            'currency_symbol' => 'RM',
            'currency_iso' => 'MYR',
        ],
        'Vn' => [
            'currency_symbol' => '₫',
            'currency_iso' => 'VND',
        ],
        'Id' => [
            'currency_symbol' => 'Rp',
            'currency_iso' => 'IDR',
        ],
    ];
    /**
     * 补录员的状态
     * 不再维护，请移步到\webcommon\tools\enums
     * @var array
     */

    public static $input_user_status = [
        1 => '开工接单',
        2 => '暂停接单',
    ];
    /**
     * 包裹单的处理状态
     * 不再维护，请移步到\webcommon\tools\enums
     * @var array
     */
    public static $pkg_form_status = [
        1 => '待录入',
        2 => '已锁定',
        3 => '已完成',
    ];
    /**
     * 补录单的执行状态
     * 不再维护，请移步到\webcommon\tools\enums
     * @var array
     */
    public static $pkg_form_input = [
        1 => '待录入',
        2 => '已锁定',
        3 => '已完成',
    ];

    //包裹状态 已揽收(1),运输中(2), 派送中(3),已滞留(4),已签收(5),疑难件处理中(6),已退件(7), 异常关闭(8), 已撤销(9);  20180801
    //不再维护，请移步到\webcommon\tools\enums
    public static $pkg_state = [
        1 => 'parcel_state_1',
        2 => 'parcel_state_2',
        3 => 'parcel_state_3',
        4 => 'parcel_state_4',
        5 => 'parcel_state_5',
        6 => 'parcel_state_6',
        7 => 'parcel_state_7',
        8 => 'parcel_state_8',
        9 => 'parcel_state_9',
    ];
    //包裹路由action 20180803
    //不再维护，请移步到\webcommon\tools\enums:$pkg_route_action
    public static $pkg_route_action = [
        'RECEIVED'                      => 'parcel_route_action_1',//已揽收
        'RECEIVE_WAREHOUSE_SCAN'        => 'parcel_route_action_2',//收件入仓
        'DELIVERY_TICKET_CREATION_SCAN' => 'parcel_route_action_3',//交接扫描
        'ARRIVAL_WAREHOUSE_SCAN'        => 'parcel_route_action_4',//到件入仓扫描
        'SHIPMENT_WAREHOUSE_SCAN'       => 'parcel_route_action_5',//发件出仓扫描
        'DETAIN_WAREHOUSE'              => 'parcel_route_action_6',//货件留仓
        'DELIVERY_CONFIRM'              => 'parcel_route_action_7',//确认妥投
        'DIFFICULTY_HANDOVER'           => 'parcel_route_action_8',//疑难件交接
        'CONTINUE_TRANSPORT'            => 'parcel_route_action_9',//疑难件继续配送
        'DIFFICULTY_RETURN'             => 'parcel_route_action_10',//疑难件退回寄件人
        'DIFFICULTY_RE_TRANSIT'         => 'parcel_route_action_11',//疑难件退回区域总部/重启运送
        'CLOSE_ORDER'                   => 'parcel_route_action_12',//关闭订单
        'DIFFICULTY_FINISH_INDEMNITY'   => 'parcel_route_action_13',//疑难件支付赔偿
        'CANCEL_PARCEL'                 => 'parcel_route_action_14',//撤销包裹
        'DELIVERY_MARKER'               => 'parcel_route_action_15',//派件标记
        'PHONE'                         => 'parcel_route_action_16',// 电话联系
        'MANUAL_REMARK'                 => 'parcel_route_action_17',// 包裹备注
    ];
    //不再维护，请移步到\webcommon\tools\enums
    public static $detained_marker_category = [
        1  => 'delivery_consignee_lose_contact',//联系不上收件人
        2  => 'delivery_rejection',//收件人拒收
        3  => 'delivery_express_are_misused',//快件分错网点
        4  => 'delivery_lugnut',//外包装破损
        5  => 'delivery_goods_breakage',//货物破损
        6  => 'delivery_goods_less',//货物减少
        7  => 'delivery_lost_goods',//货物丢失
        8  => 'pickup_customer_lose_contact',//电话联系不上
        9  => 'pickup_customer_change_time',//客户改约时间
        10 => 'pickup_customer_is_not_at_home',//客户不在
        11 => 'pickup_cancel_the_task',//客户取消任务
        12 => 'detained_no_sign',//无人签收
        13 => 'detained_closed_at_weekends',//周末或假期不送货
        14 => 'detained_change_about_time',//改约时间
        15 => 'detained_overburden',//当日超负荷无法配送
        16 => 'difficulty_consignee_lose_contact',//联系不上收件人
        17 => 'difficulty_rejection',//收件人拒收
        18 => 'difficulty_express_are_misused',//快件分错网点
        19 => 'difficulty_lugnut',//外包装破损
        20 => 'difficulty_goods_breakage',//货物破损
        21 => 'difficulty_goods_less',//货物减少
        22 => 'difficulty_lost_goods',//货物丢失
        23 => 'difficulty_incorrect_address',//收件人/地址不清晰或不正确
        24 => 'difficulty_closed_business',//收件地址已关闭营业(企业)
        25 => 'difficulty_misn',//收件人电话号码错误/电话号码不存在
        40 => 'detained_consignee_lose_contact',//货件留仓，联系不上收件人
        41 => 'detained_miss_trunk_transportation',//错过班车时间
        50 => 'cancel_parcel',//客户取消寄件
        51 => 'information_input_error',//信息录入错误
        70 => 'delivery_customer_change_time',//客户改约时间
        71 => 'delivery_overburden',//当日运力不足，无法派送
        72 => 'delivery_closed_at_weekends',//客户周末或假期不收货
        73 => 'delivery_incorrect_address',//收件人/地址不清晰或不正确
        74 => 'delivery_closed_business',//收件地址已关闭营业(企业)
        75 => 'delivery_misn',//收件人电话号码错误/电话号码不存在


    ];

    // 区域定义
    public static $store_areas = [
        1 => 'BKK',
        2 => 'Cen+East',
        3 => 'Cen+West',
        4 => 'North',
        5 => 'North East',
        6 => 'South',
        7 => 'Cen',
    ];

    //网点类型
    public static $store_category = [
        1 => 'SP',
        2 => 'DC',
        4 => 'shop(P only)',
        5 => 'shop(P&D)',
        6 => 'FH',
        7 => 'USHOP',
        8 => 'Hub',
        9 => 'OS',
        10 => 'BDC',
        11 => 'FFM',
        12 => 'B-Hub',
        13 => 'CDC',
        14 => 'PDC',
    ];

    //小标签复查结果枚举
    public static $pkg_info_deal_category = [
        1 => 'un_deal',//未处理
        2 => 'deal_withed',//复查通过
        3 => 'punish_supplement',// 罚补单员
        4 => 'punish_audit',//罚审核员
        5 => 'punish_keeper', //罚仓管员
    ];

    //发送chu处罚原因
    public static $punish = [
        1 => 'problem_package', //'虚假问题件/虚假留仓件',
        2 => 'package_not_update_bg_one',// '包裹超过1天没有更新',
        3 => 'undelivered_bg_five', // '5天未妥投/未中转，且超一天未更新',
        4 => 'package_resolve_not_in_time', // 'miniCS对问题件解决不及时',
        5 => 'undelivered_bg_three', //'包裹配送时间超三天',
        6 => 'undelivered_at_revision_time', // '未在客户要求的改约时间之前派送包裹',
        7 => 'package_loss', // '包裹丢失',
        8 => 'breakage', //'包裹破损',
        9 => 'other', //其他
        // 10 => 'java_rule10', //java那边抓取的数据,"5天未妥投/未中转，且超一天未更新"
    ];

    //工单类型
    public static $order_type = [
        1 => 'find_order',//查找运单
        2 => 'faster_handle',//加速处理
        3 => 'research_staff',//调查员工
        5 => 'dot_info_maintenance', //网点信息维护提醒
        6 => 'training_guide', //培训指导
        7 => 'abnormal_business_inquiry', //异常业务询问
        4 => 'other_order',//其他
    ];

    //工单受理人
    public static $order_handler_type = [
        1 => 'agents_manager', //网点经理
        2 => 'stoer_supervisor', //网点主管
        3 => 'agents_manager_and_stoer_supervisor', //网点经理和网点主管
    ];

    //工单状态
    public static $order_status = [
        1 => 'order_unread',    //未阅读
        2 => 'order_readed',    //已经阅读
        3 => 'order_reply',     //已回复
        4 => 'order_closed',    //已关闭
    ];

    //BY申请类型 biz_type 枚举
    public static $audit_type = [
        'AT'  => AuditListEnums::APPROVAL_TYPE_AT, //补卡
        'ICAT'=> AuditListEnums::APPROVAL_TYPE_ICAT, //个人代理补卡
        'LE'  => AuditListEnums::APPROVAL_TYPE_LE, //请假
        'LH'  => AuditListEnums::APPROVAL_TYPE_LH, //LH
        'OT'  => AuditListEnums::APPROVAL_TYPE_OVERTIME, //加班
        'BONUS'  => AuditListEnums::APPROVAL_TYPE_OVERTIME_UNPAID, //个人代理奖金
        'HC'  => AuditListEnums::APPROVAL_TYPE_HC, //HC
        'MA'  => AuditListEnums::APPROVAL_TYPE_WMS,//物料
        'BT'  => AuditListEnums::APPROVAL_TYPE_BT, //出差
        'MI'  => AuditListEnums::APPROVAL_TYPE_VEHICLE,//车辆里程
        'VA'  => AuditListEnums::APPROVAL_TYPE_FLEET,//加班车
        'RN'  => AuditListEnums::APPROVAL_TYPE_RN,//离职
        'OS'  => AuditListEnums::APPROVAL_TYPE_OS,//外协
        'EX'  => 15,//费用(已作废)
        'AS'  => AuditListEnums::APPROVAL_TYPE_ASSETS,//个人资产
        'RE'  => AuditListEnums::APPROVAL_TYPE_REPORT,//举报
        'TF'  => AuditListEnums::APPROVAL_TYPE_JT,//转岗
        'ASP' => AuditListEnums::APPROVAL_TYPE_PUBLIC_ASSETS,//公共资产
        'TFOA' => AuditListEnums::APPROVAL_TYPE_JT_OA,//转岗OA
        'FD'  => AuditListEnums::APPROVAL_TYPE_FD_NETWORK,//运营产品-network
        'SA'  => AuditListEnums::APPROVAL_TYPE_SALARY,//薪资审批
        'FDS' => AuditListEnums::APPROVAL_TYPE_FD_SHOP,//运营产品-shop
        'AR'  => AuditListEnums::APPROVAL_TYPE_ADJUST_ROLE, //增减角色审批
        'HCB' => AuditListEnums::APPROVAL_TYPE_HC_BUDGET,//HC预算
        'FDP' => AuditListEnums::APPROVAL_TYPE_FD_SALES_PMD,//运营产品-sales/Project Managerment
        'CR'  => AuditListEnums::APPROVAL_TYPE_CLAIMER,//网点理赔
        'ATB' => AuditListEnums::APPROVAL_TYPE_ATT_BUS,//出差外勤打卡
        'WH'  => AuditListEnums::APPROVAL_TYPE_WORK_HOME, //居家办公打卡
        'WH_APPLY'  => AuditListEnums::APPROVAL_TYPE_WORK_HOME_APPLY, //居家办公申请
        'MSG' => AuditListEnums::APPROVAL_TYPE_MESSAGE,//消息
        'YCBT' => AuditListEnums::APPROVAL_TYPE_YCBT,//黄牌项目出差
        'FUEL' => AuditListEnums::APPROVAL_TYPE_FUEL,//油费补贴
        'PA' => AuditListEnums::APPROVAL_TYPE_PA,//处罚申诉
        'ST' => AuditListEnums::APPROVAL_TYPE_OFFER_SIGNATURE,//offer签字
        'OPR' => AuditListEnums::APPROVAL_TYPE_OPR,//外协特殊价格
        'GO' => AuditListEnums::APPROVAL_TYPE_GO,//外出申请
        'GOC' => AuditListEnums::APPROVAL_TYPE_GO_CI,//外出打卡审批
        'SAS' => AuditListEnums::APPROVAL_TYPE_SAS,//网点申请支援
        'SASS' => AuditListEnums::APPROVAL_TYPE_SASS,//员工申请支援网点
        'SYSTEM_CS'=>AuditListEnums::APPROVAL_TYPE_SYSTEM_CS, // 外部审核-众包
        'NAS' => AuditListEnums::APPROVAL_TYPE_ASSET_V2, // 新版资产
        'ABE' => AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE, //异常费用类型
        'ABEF' => AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE_FREIGHT, //异常费用-单次运费调整
        'DRIVER_BLACKLIST'=>AuditListEnums::APPROVAL_TYPE_SYSTEM_DRIVER_BLACKLIST, // 外部审核- 司机黑名单
        'OS_OT' => AuditListEnums::APPROVAL_TYPE_OUTSOURCING_OT,   // 外协员工加班审批
        'OSAT' => AuditListEnums::APPROVAL_TYPE_HUB_OS_AT,//
        'WMS' => AuditListEnums::APPROVAL_TYPE_WMS_V2, //耗材申请
        'QC' => AuditListEnums::APPROVAL_TYPE_QUITCLAIM, //Quitclaim审核申请
        'MILEAGE'=>AuditListEnums::APPROVAL_TYPE_MILEAGE, // 虚假里程
        'VEHICLE_APPROVAL'=>AuditListEnums::APPROVAL_TYPE_VEHICLE_APPROVAL, // 外部审核- 司机黑名单
        'RC' => AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT, //续签合同Renew contract
        'STOP' => AuditListEnums::APPROVAL_TYPE_STOP,     // 停职审批
        'CANCEL_CONTRACT' => AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT,     //解约申请
        'COMPANY_TERMINATION_CONTRACT' => AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT,     //公司解约个人代理
        'ADVANCE_FUEL' => AuditListEnums::APPROVAL_TYPE_ADVANCE_FUEL,     //预支油费申请
        'SICK_CERTIFICATE' => AuditListEnums::APPROVAL_TYPE_SICK_CERTIFICATE,//泰国病假资料申请
        'HIRE_CHANGE' => AuditListEnums::APPROVAL_HIRE_TYPE_CHANGE,//转型个人代理审批
        'SUSPEND_WORK' => AuditListEnums::APPROVAL_TYPE_SUSPEND_WORK,//个人代理暂停接单申请
        'EC' => AuditListEnums::APPROVAL_TYPE_IC_RENEWAL,//个人代理续约合同
        'FRANCHISEES' => AuditListEnums::APPROVAL_TYPE_FRANCHISEES,//注册加盟商审批流
        'FLEET_GENERATE_RECORDS' => AuditListEnums::APPROVAL_TYPE_FLEET_GENERATE_RECORDS,//汽运-生成流水
        'RNTE' => AuditListEnums::APPROVAL_TYPE_SUSPENSION,//停职申请
        'TRANSFER_CAR' => AuditListEnums::APPROVAL_TYPE_TRANSFER_CAR,//调车-费用申请

    ];

    public static $at = [
        AuditListEnums::APPROVAL_TYPE_OVERTIME                => '6010', //加班
        AuditListEnums::APPROVAL_TYPE_OVERTIME_UNPAID         => 'apply_bonus', //申请奖金
        AuditListEnums::APPROVAL_TYPE_OVERTIME_OS             => 'workflow_type_77',//外协延长工时
        AuditListEnums::APPROVAL_TYPE_HC                      => '7007', //HC
        AuditListEnums::APPROVAL_TYPE_ASSETS                  => '7013', //个人资产
        AuditListEnums::APPROVAL_TYPE_PUBLIC_ASSETS           => '7017', //公共资产
        AuditListEnums::APPROVAL_TYPE_WMS                     => '7005', //物料
        AuditListEnums::APPROVAL_TYPE_AT                      => '7001', // 补卡
        AuditListEnums::APPROVAL_TYPE_ICAT                    => 'ic_reissue', // 补卡
        AuditListEnums::APPROVAL_TYPE_LE                      => '7002', //请假
        AuditListEnums::APPROVAL_TYPE_BT                      => '7006', //出差
        AuditListEnums::APPROVAL_TYPE_CLAIMER                 => '7024', //网点理赔
        AuditListEnums::APPROVAL_TYPE_RN                      => '7010', //离职
        AuditListEnums::APPROVAL_TYPE_FD_NETWORK              => '7021', //运营产品-network
        AuditListEnums::APPROVAL_TYPE_FD_SHOP                 => '7025', //运营产品-shop
        AuditListEnums::APPROVAL_TYPE_FD_SALES_PMD            => '7026', //运营产品-sales/Project Managerment
        AuditListEnums::APPROVAL_TYPE_ATT_BUS                 => '7023', //出差打卡
        AuditListEnums::APPROVAL_TYPE_MESSAGE                 => '7033', //消息审批
        AuditListEnums::APPROVAL_TYPE_YCBT                    => '7028', //黄牌项目出差
        AuditListEnums::APPROVAL_TYPE_FUEL                    => '7027', //油费补贴
        AuditListEnums::APPROVAL_TYPE_JT                      => '7018', //转岗
        AuditListEnums::APPROVAL_TYPE_OS                      => '7011', //外协
        AuditListEnums::APPROVAL_TYPE_PA                      => '7034', //kit 处罚申诉
        AuditListEnums::APPROVAL_TYPE_FLEET                   => '6011', //加班车
        AuditListEnums::APPROVAL_TYPE_SALARY                  => '7022', //薪资审批
        AuditListEnums::APPROVAL_TYPE_ADJUST_ROLE             => '7035', //增减角色审批
        AuditListEnums::APPROVAL_TYPE_REPORT                  => '7015', //举报
        AuditListEnums::APPROVAL_TYPE_OFFER_SIGNATURE         => '7036', //offer签字
        AuditListEnums::APPROVAL_TYPE_OPR                     => 'os_price_title', //外协特殊价格
        AuditListEnums::APPROVAL_TYPE_GO                      => 'go_out_application', //外出申请
        AuditListEnums::APPROVAL_TYPE_GO_CI                   => 'go_out_clock_in', //外出打卡审批
        AuditListEnums::APPROVAL_TYPE_SAS                     => 'store_support_support', //网点申请支援
        AuditListEnums::APPROVAL_TYPE_SASS                    => 'staff_support_support_store', //员工申请支援网点
        AuditListEnums::APPROVAL_TYPE_VEHICLE                 => '7008', //车辆里程
        AuditListEnums::APPROVAL_TYPE_SYSTEM_CS               => 'approval_type_system_cs', //众包申请
        AuditListEnums::APPROVAL_TYPE_ASSET_V2                => 'asset_apply', //新版资产
        AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE        => 'abnormal_expense', //异常费用
        AuditListEnums::APPROVAL_TYPE_WMS_V2                  => 'wms_apply', //申请耗材
        AuditListEnums::APPROVAL_TYPE_SYSTEM_DRIVER_BLACKLIST => 'workflow_type_48', //众包黑名单
        AuditListEnums::APPROVAL_TYPE_HUB_OS_AT               => 'workflow_type_52',//HUB外协补卡
        AuditListEnums::APPROVAL_TYPE_HR_PENALTY_APPEAL       => 'workflow_type_54', //HR处罚申诉
        AuditListEnums::APPROVAL_TYPE_OUTSOURCING_OT          => 'outsourcing_ot_approval',     // 外协加班申请
        AuditListEnums::APPROVAL_TYPE_MILEAGE                 => 'workflow_type_49', //众包黑名单
        AuditListEnums::APPROVAL_TYPE_QUITCLAIM               => 'workflow_type_56', //Quitclaim审核申请
        AuditListEnums::APPROVAL_TYPE_VEHICLE_APPROVAL        => 'workflow_type_55', //车辆变更审批
        AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT          => 'workflow_type_58', //续签合同
        AuditListEnums::APPROVAL_TYPE_REINSTATEMENT           => 'workflow_type_59',//复职申请
        AuditListEnums::APPROVAL_TYPE_STOP                    => 'workflow_type_60',   // 停职审批
        AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT         => 'workflow_type_63',   //解约申请
        AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT  => 'workflow_type_68',   //公司解约个人代理
        AuditListEnums::APPROVAL_TYPE_ADVANCE_FUEL            => 'workflow_type_69',   //预支油费申请
        AuditListEnums::APPROVAL_TYPE_SICK_CERTIFICATE        => 'sick_certificate',//病假材料申请
        AuditListEnums::APPROVAL_HIRE_TYPE_CHANGE             => 'workflow_type_64',//个人代理i转型审批
        AuditListEnums::APPROVAL_TYPE_REEMPLOYMNET            => "workflow_type_61",//重新雇佣申请
        AuditListEnums::APPROVAL_TYPE_JT_SPECIAL              => 'workflow_type_70', //特殊批量转岗
        AuditListEnums::APPROVAL_TYPE_SUSPEND_WORK            => 'workflow_type_72',   //暂停接单
        AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE_FREIGHT => 'workflow_type_47',
        AuditListEnums::APPROVAL_TYPE_JT_STAGE_TWO  => 'workflow_type_65',
        AuditListEnums::APPROVAL_TYPE_HC_BUDGET     => 'workflow_type_24',
        AuditListEnums::APPROVAL_TYPE_WAREHOUSE_CHANGE_STATUS => 'workflow_type_66',
        AuditListEnums::APPROVAL_TYPE_WAREHOUSE_CHANGE_STORE => 'workflow_type_67',
        AuditListEnums::APPROVAL_TYPE_WAREHOUSE_THREAD_PRICE => 'workflow_type_83',
        AuditListEnums::APPROVAL_TYPE_VEHICLE_EDIT => 'workflow_type_84', //修改车辆信息
        AuditListEnums::APPROVAL_TYPE_IC_RENEWAL    => 'workflow_type_74',//个人代理续约合同
        AuditListEnums::APPROVAL_TYPE_FRANCHISEES    => 'workflow_type_76',//注册加盟商
        AuditListEnums::APPROVAL_TYPE_SUSPENSION    => 'workflow_type_80',//停职申请
        AuditListEnums::APPROVAL_TYPE_WORK_HOME      => 'workflow_type_81',//居家办公
        AuditListEnums::APPROVAL_TYPE_CS_PAYMENT      => 'workflow_type_82',//众包结算审批
        AuditListEnums::APPROVAL_TYPE_OA_BUDGET_WITHHOLDING => 'workflow_type_89',//预算管理-预提审核
        AuditListEnums::APPROVAL_TYPE_TRANSFER_CAR => 'workflow_type_92',//调车-费用申请
        AuditListEnums::APPROVAL_TYPE_OA_AGENCY_PAYMENT => 'workflow_type_71', //代理支付
        AuditListEnums::APPROVAL_TYPE_TRANSPORTATION_CONTRACT => 'workflow_type_87',//汽运合同审批
        AuditListEnums::APPROVAL_TYPE_VEHICLE_REPAIR_REQUEST => 'workflow_type_90',//车辆维修申请
        AuditListEnums::APPROVAL_TYPE_WORK_HOME_APPLY      => 'workflow_type_88',//居家办公申请
    ];

    //BY审批状态
    public static $audit_status = [
        'panding'   => self::APPROVAL_STATUS_PENDING,  //待审批
        'approved'  => self::APPROVAL_STATUS_APPROVAL, //已同意
        'dismissed' => self::APPROVAL_STATUS_REJECTED, //已驳回
        'revoked'   => self::APPROVAL_STATUS_CANCEL,   //已撤销
        'timedout'  => self::APPROVAL_STATUS_TIMEOUT,  //已超时
        'approved_approval' => 6,//审批人同意
        'panding_approval'  => 7,//审批人待审批
    ];
    const IS_CANCEL = 1;

    const APPROVAL_DETAIL_TYPE_PDF = 1;//pdf渲染类型
    const APPROVAL_DETAIL_TYPE_URL = 2;//链接渲染类型
    const APPROVAL_DETAIL_TYPE_ARRAY = 3;//数组链接多个url
    const APPROVAL_DETAIL_TYPE_SHOW = 4;//显示查看
    const APPROVAL_DETAIL_TYPE_COORDINATE = 5;//请假详情页坐标类型
    const APPROVAL_DETAIL_TYPE_OTHER_CONTENT = 6;//病假申请 其他图片和文字描述
    const APPROVAL_DETAIL_TYPE_PICTURE_AND_VIDEO = 7;//kit 处罚申诉 图片和视频
    const APPROVAL_DETAIL_TYPE_LIST = 8;//消息发送范围 value是list
    const APPROVAL_DETAIL_TYPE_SUSPENSION = 9;//停职申请 value是list
    const APPROVAL_DETAIL_TYPE_PIC_PDF = 10;//  图片、pdf  需要定义url

    //BY大列表审批状态
    public static $audit_list_status = [
        'panding'           => 101, //待审批
        'approved'          => 102, //已同意
        'dismissed'         => 103, //已驳回
        'revoked'           => 104, //已撤销
        'timedout'          => 105, //已超时
        'approved_approval' => 106,//审批人同意
        'panding_approval'  => 107,//审批人待审批
    ];

    //职位
    //数据来源bi-hr_job_title表
    public static $job_title = [
        'area_manager' => 11,
        'bike_courier' => 13,
        'branch_supervisor' => 16,
        'dc_officer' => 37,
        'dc_supervisor' => 801,
        'hub_operator' => 807,
        'hub_manager' => 50,
        'regional_manager' => 79,
        'sales_manager' => 83,
        'shop_cashier' => 97,
        'shop_officer' => 98,
        'th_senior_shop_officer' => 1884,
        'shop_supervisor' => 101,
        'store_officer' => 106,
        'van_courier'   => 110,
        'car_courier'   => 1199,
        'warehouse_staff_sorter' => 111,
        'shop_bike' => 155,
        'mini_cs_officer' => 220,
        'district_manager' => 269,
        'hub_staff' => 271,
        'hub_supervisor' => 272,
        'store_supervisor' => 296,
        'warehouse_staff' => 300,
        'employee_relation_manager' => 441,
        'assistant_branch_supervisor' => 451,
        'boat_courier' => 452,
        'onsite_officer' => 812,
        'onsite_staff' => 473,
        'fleet_driver' => 474,
        'freight_hub_staff' => 545,
        'employee_relation_specialist' => 176,
        'er_officer' => 177,
        'shop_operations_manager' => 291,
        'freight_hub_manager' => 539,
        'freight_hub_outbound_supervisor' => 540,
        'freight_hub_inbound_supervisor' => 541,
        'freight_hub_QAQC_supervisor' => 542,
        'hub_admin_officer' => 568,
        'onsite_supervisor' => 675,
        'onsite_admin_officer' => 676,
        'tricycle_courier' => 1000,
        'hub_admin_manager' => 563,
        'hub_admin_supervisor' => 574,
        'freight_hub_admin_officer' => 544,
        'shop_support_officer' => 719,
        'shop_support_supervisor' => 718,
        'shop_support_section_manager' => 717,
        'network_support_officer' => 556,
        'network_support_supervisor' => 555,
        'network_support_manager' => 554,
        'warehouse_operations_director' => 914,
        'operations_director' => 929,
        'senior_asset_data_officer'=>624,
        'asset_data_officer'=>625,
        'cpo' => 632,
        'warehouse_supervisor'=>158,
        'warehouse_manager'=>916,
        'truck_courier'=>1194,
        'cdc_supervisor'=>1290,
        'cdc_officer'=>1289,
        'outsource' => 1474,
        'security_outsource' => 1475,
        'van_feeder' => 1497,
        'resource_officer' => 1456,
        'resource_specialist' => 1457,
        'resource_supervisor' => 1458,
        'th_courier_and_installation_staff' => 1663,
        'freight_hub_admin_supervisor' => 1238,
        'warehouse_operations_deputy_director' => 953,
        'hub_planning_supervisor' => 571,
        'translator' => 443,
        'hub_support_supervisor' => 803,
        'hub_support_officer' => 804,
        'hub_support_deputy_manager' => 1233,
        'hub_support_manager' => 802,
        'warehouse_deputy_manager' => 836,
        'network_support_deputy_manager' =>554,
        'shop_support_manager' =>782,
        'shop_area_manager' =>709,
        'ph_onsite_supervisor' =>1296,
        'warehouse_supervisor_material' =>1188,
        'customer_service_deputy_manager'  => 1299,
        'head_of_vietnam_fulfillment' =>1052,
        'customer_service_specialist' => 962,
        'quality_control_specialist' => 913,
        'head_of_f_commerce_vietnam' =>1187,
        'general_manager_of_f_commerce' => 726,
        'chinese_translator' => 25,
        'th_pickup_driver'=>1844,
        'mobile_dc' => 1652,
        'internship' => 1355,
        'truck_courier_night' => 1675,
    ];

    public static $my_job_title = [
        'outsource' => 1719,
        'security_outsource' => 1718,
    ];

    //角色
    //数据来源bi-roles表
    public static $roles = [
        'DISPATCHER'                          => 0,
        'COURIER'                             => 1,
        'STORE_KEEPER'                        => 2,
        'BRANCH_CASHIER'                      => 4,
        'DOT_ADMINISTRATOR'                   => 18,
        'CENTRAL_CONTROL_DISPATCHING_OFFICER' => 26,
        'CENTRAL_CONTROL_DISPATCHING_MANAGER' => 27,
        'SECURITY_MANAGER'                    => 30,
        'AREA_MANAGER'                        => 56,//大区经理
        'DISTRICT_MANAGER'                    => 57,
        'FLEXIBLE_COURIER'                    => 109,
        'DISTRIBUTION_MANAGER'                => 60,//分拨经理
    ];

    //性别
    public static $sex_type = [
        'male'  => 1,
        'female'=> 2,
    ];

    //加班车类型
    public static $fleet_audit_type = [
        'Normal'        => 1, //普通加班车
        'FD_courier'    => 2, //FD加班车
    ];

    //资产-附件状态
    public static $assets_handover_state = [
        'not_uploaded'  => 1,//未上传
        'audited'       => 2,//待审核
        'not_pass'      => 3,//不通过
        'adopt'         => 4,//已通过
    ];

    //网点类型
    public static $stores_category = [
        'sp' => 1,
        'dc' => 2,
        'branch_agent' => 3,
        'shop_pickup_only' => 4,
        'shop_pickup_delivery' => 5,
        'fh' => 6,
        'shop_ushop' => 7,
        'hub' => 8,
        'os' => 9,
        'bdc'=> 10,
        'ffm'=> 11,
        'bhub'=> 12,
        'cdc'=> 13,
        'pdc' => 14,
    ];
    //网点分类对应的租车津贴默认值 关系数组
    public static $store_car_rental_map = [
        'sp'  => [
            1 => 220,
            2 => 270,
            3 => 370,
            4 => 220,
            5 => 270,
            6 => 370,
        ],
        'bdc' => [
            1 => 270,
            2 => 320,
            3 => 370,
            4 => 270,
            5 => 320,
            6 => 370,
        ],
    ];

    public static $os_staff_type = [
        'normal'    => 1, //普通外协
        'long_term' => 2, //长期外协
        'motorcade' => 3, //车队外协
    ];


    //nw 部门 网点 的dc officer 数量对应的 加班小时预算
    public static $dc_hours = [
//        1 => 14,
//        2 => 28,
//        3 => 20,
//        4 => 0,
    //改了一版本
        1 => 17,
        2 => 34,
        3 => 24,
        4 => 10,
        5 => 0,
    ];


    /** 马来 nw op部门 也跟泰国一样 https://flashexpress.feishu.cn/docx/doxcnpycIQOw9VRxDaY62kbznOh
     * @var array
     */
    public static $my_hour = [
        1 => 28,
        2 => 42,
        3 => 42,
        4 => 28,
        5 => 18,
        6 => 0,
    ];

    //工单状态
    const TICKET_STATUS_WAIT_REPLY = 1; //待回复
    const TICKET_STATUS_REPLY = 2;  //已回复
    const TICKET_STATUS_CLOSED =3;  //已关闭

    public static $ticket_status = [
        self::TICKET_STATUS_WAIT_REPLY => 'ticket_status_1',
        self::TICKET_STATUS_REPLY     => 'ticket_status_2',
        self::TICKET_STATUS_CLOSED     => 'ticket_status_3',
    ];

    //工单设备类型
    const TICKET_ITEM_TYPE_MOBILE = 1; //手机
    const TICKET_ITEM_TYPE_NOTE = 2;  //笔记本电脑
    const TICKET_ITEM_TYPE_COMPUTER = 3;  //台式电脑
    const TICKET_ITEM_TYPE_OTHER =4;  //其他

    public static $ticket_item_type = [
        self::TICKET_ITEM_TYPE_MOBILE => 'ticket_item_type_1',
        self::TICKET_ITEM_TYPE_NOTE     => 'ticket_item_type_2',
        self::TICKET_ITEM_TYPE_COMPUTER     => 'ticket_item_type_3',
        self::TICKET_ITEM_TYPE_OTHER     => 'ticket_item_type_4',
    ];

    //审批详情页-审批按钮、修改数据框等
    public static $audit_detail_btns = [
        'btn_agree' => 1, //同意按钮
        'btn_reject' => 2, //驳回按钮
        'btn_cancel' => 3, //撤销按钮
        'btn_confirm' => 4, //确认按钮
        'input_hc'  => 5, //HC优先级下拉选项
        'input_os'  => 6, //OS批准人数
        'input_wms'  => 7, //最终审批数量
        'input_base_salary' => 8, //基本工资
        'input_exp_allowance' => 9, //经验津贴
        'input_position_allowance' => 10, //职位津贴
        'input_car_rental' => 11, //租车津贴
        'input_trip_payment' => 12, //油费补贴
        'input_notebook_rental' => 13, //电脑补贴
        'input_recommended' => 14, //推荐
        'input_food_allowance' => 15, //餐补
        'input_dangerous_area' => 16, //危险区域津贴
        'input_house_rental' => 17, //租房补贴
        'input_os_date' => 18, //OS雇佣天数
        'input_role' => 19, //输入角色
        'upload_file' => 20, //上传文件
        'input_hc_time' => 21, //hc过期时间
        'car_owner' => 22, //车辆所属
        'rental_car_cteated_at' => 23, //开始时间
        'input_performance_allowance' => 24, //绩效补贴
        'input_island_allowance' => 25, //海岛补贴
        'input_deminimis_benefits' => 26, //无税金补贴
        'input_other_non_taxable_allowance' => 27, //其他无税金补贴
        'input_other_taxable_allowance' => 28, //其他纳税津贴
        'input_phone_subsidy' => 29, //电话补贴

        'input_mobile_allowance' => 30, // 手机津贴
        'input_attendance_allowance' => 31, // 出勤津贴
        'input_fuel_allowance' => 32, // 油补津贴
        'input_car_allowance' => 33, // 私家车车补津贴
        'input_vehicle_allowance' => 34, // 货车车补津贴
        'input_gdl_allowance' => 35, // GDL补贴
        'input_site_allowance' => 36, //区域津贴
    ];

    /**
     * 错误码
     */
    public static $ERROR_CODE = [
        '1000' => '1000', //数据验证错误
        '1001' => '1001', //数据重复
        '1002' => '1002',
        '1003' => '1004',
        '1005' => '1005', //数据notice
    ];

    //网点大区对应关系
    public static $areasArr = [
        1 => 'bkk',
        2 => 'ce',
        3 => 'cw',
        4 => 'no',
        5 => 'ne',
        6 => 'so',
    ];

    //员工在职状态
    public static $service_status = [
        'incumbency' => 1, //在职
        'dimission'  => 2, //离职
        'suspension' => 3, //停职
    ];
	//员工编制状态
	public static $staff_formal = [
        'os'           => 0, //非编制(外协员工)
        'organization' => 1, //编制
        'internship'   => 4, //实习
	];

	//员工是否子账号
	public static $staff_is_sub= [
		'yes' => 1, //是
		'no'  => 0, //否
	];

	//员工是否子账号
	public static $staff_wait_leave_state= [
		'yes' => 1, //是
		'no'  => 0, //否
	];


    //转岗状态
    public static $job_transfer_state = [
        'to_be_transfered'  => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED, //待转岗
        'not_transfered'    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED, //未转岗
        'transfered'        => JobTransferModel::JOBTRANSFER_STATE_TRANSFERED, //已转岗
    ];

    const NETWORK_OPERATIONS_ID    = 1110; //Network Management对应的部门id,14766需求修改为Network Area部门及其子部门下;15033需求修改为Network Operations部门及其子部门下
    const NETWORK_BULKY_OPERATIONS_ID    = 1104; //Network Bulky对应的部门id,14766需求修改为Network Bulky Area部门及其子部门下;15033需求修改为Network Bulky Operations部门及其子部门下的

    //组织架构调整  x 替换 1110  y 替换 1104
    const NETWORK_AREA = 32;//原nw op 部门 999/222/1/4/1110/32
    const NETWORK_QC = 12;//原QAQC 999/222/1/4/1110/12
    const NETWORK_BULKY_AREA = 483;//原Network Bulky Operations 999/222/1/4/1104/483
    const NETWORK_BULKY_QC = 481;//原 bulky qaqc 999/222/1/4/1104/481

    const NETWORK_PICKUP_CONTROL_CENTER = 1105;//pcc 部门

    const FFM_DEPARTMENT = 243;//ffm 顶级部门

    //部门
    public static $department = [
        'Customer Service'    => 3,
        'Network Management'    => 4,
        'Network Planning'    => 30,
        'Network Training'    => 31,
        'Shop Management'       => 13,
        'Fulfillment'           => 15,
        'University Project'    => 14,
        'Hub Management'        => 25,
        'Transportation'        => 26,
        'Network Operations'    => 32,
        'Network Support'    => 33,
        'Network Bulky'    => 34,
        'Network Bulky Operations'    => 483,
        'hr_business_partner'    => 49,
        'talent_acquisition'    => 50,
        'flash_freight_hub'    => 65,
        'fleet_management'    => 64,
        'onsite_managemnet'    => 95,
        'Sales'                 => 40,
        'Project Management'    => 22,
        'Warehouse Operations'    => 20002,
        'data_support'    => 138,
        'hub_headquarter'=>107,
        'shop_support'=>105,
        'freight_hub_northeast_area'=> 502,
        'freight_hub_north_area'=> 501,
    ];
    //属于Shop Support小组的Shop Support Officer

    //审批状态
    const APPROVAL_STATUS_PENDING = 1;         //待审批
    const APPROVAL_STATUS_APPROVAL = 2;        //审批同意
    const APPROVAL_STATUS_REJECTED = 3;        //审批驳回
    const APPROVAL_STATUS_CANCEL = 4;          //审批撤销
    const APPROVAL_STATUS_TIMEOUT = 5;         //审批超时
    const APPROVAL_STATUS_CANCEL_ROLL_BACK = 6; //撤销发起审批
    const APPROVAL_STATUS_APPLY = 8;    //申请
    const APPROVAL_STATUS_ABANDON = 10;   //审批作废
    const APPROVAL_STATUS_APPROVAL_SUPERIOR = 21; // 上级离职自动转交
    const APPROVAL_STATUS_APPROVAL_EMPTY = 22; // 审批/核人为空 审批人重复自动审批通过
    const APPROVAL_STATUS_APPROVAL_COUNTERSIGN = 24; // 会签审批通过
    const APPROVAL_STATUS_REJECTED_ROLL_BACK = 23; //驳回到某一节点，非最终驳回
    const APPROVAL_STATUS_APPROVAL_EMPTY_AUTO_REJECT = 25; //审批人为空 审批人重复自动审批驳回

    //审批状态对应的多语言
    public static $approval_status = [
        self::APPROVAL_STATUS_PENDING   => 'audit_status.1',
        self::APPROVAL_STATUS_APPROVAL  => 'audit_status.2',
        self::APPROVAL_STATUS_REJECTED  => 'audit_status.3',
        self::APPROVAL_STATUS_CANCEL    => 'audit_status.4',
        self::APPROVAL_STATUS_TIMEOUT   => 'audit_status.5',
        self::APPROVAL_STATUS_APPLY     => 'flow_audit_action.0',
        self::APPROVAL_STATUS_APPROVAL_SUPERIOR   => 'audit_status.21',
        self::APPROVAL_STATUS_APPROVAL_EMPTY   => 'audit_status.22',
        self::APPROVAL_STATUS_APPROVAL_EMPTY_AUTO_REJECT   => 'audit_status.25',
        self::APPROVAL_STATUS_APPROVAL_COUNTERSIGN   => 'audit_status.24',
        self::APPROVAL_STATUS_REJECTED_ROLL_BACK  => 'audit_status.3',
        self::APPROVAL_STATUS_CANCEL_ROLL_BACK  => 'audit_status.6',
    ];

    public static $wf_action_status = [
        self::WF_ACTION_PENDING                    => 'audit_status.1',
        self::WF_ACTION_APPROVE                    => 'audit_status.2',
        self::WF_ACTION_REJECT                     => 'audit_status.3',
        self::WF_ACTION_CANCEL                     => 'audit_status.4',
        self::WF_ACTION_TIMEOUT                    => 'audit_status.5',
        self::WF_ACTION_CC                         => 'audit_status.7',
        self::WF_ACTION_CREATE                     => 'flow_audit_action.0',
        self::WF_ACTION_APPROVAL_SUPERIOR          => 'audit_status.21',
        self::WF_ACTION_APPROVAL_EMPTY             => 'audit_status.22',
        self::WF_ACTION_REJECTED_ROLL_BACK         => 'audit_status.3',
        self::WF_ACTION_APPROVE_COUNTERSIGN        => 'audit_status.24',
        self::WF_ACTION_APPROVAL_EMPTY_AUTO_REJECT => 'audit_status.25',
        self::WF_ACTION_AUDIT_PENDING              => 'audit_status.26',
        self::WF_ACTION_AUDIT_APPROVE              => 'audit_status.27',
        self::WF_ACTION_AUDIT_REJECT               => 'audit_status.28',
        self::APPROVAL_STATUS_CANCEL_ROLL_BACK     => 'audit_status.6',
        self::WF_ACTION_CONFIRM_PASS               => 'audit_status.30',
        self::WF_ACTION_CONFIRM_REJECT             => 'audit_status.31',
        self::WF_ACTION_CONFIRM_PENDING            => 'audit_status.98',
        self::WF_ACTION_CONFIRM_CANCEL             => 'audit_status.33',
        self::WF_ACTION_CONFIRM_OVERTIME           => 'audit_status.34',
        self::WF_ACTION_OVERTIME_AUTO_APPROVAL     => 'audit_status.2',
        self::WF_ACTION_OVERTIME_AUTO_REJECT       => 'audit_status.3',
        self::WF_ACTION_OVERTIME_AUTO_HANDOVER     => 'audit_status.21',
        self::WF_ACTION_OVERTIME_AUTO_APPROVAL_COUNTERSIGN     => 'audit_status.2',
        self::WF_ACTION_EDIT_RECREATE              => 'audit_status.39',
    ];
    //个人代理用到的审批状态翻译
    public static $wf_action_status_unpaid = [
        self::WF_ACTION_PENDING                    => 'audit_status_unpaid_1',
        self::WF_ACTION_APPROVE                    => 'audit_status_unpaid_2',
        self::WF_ACTION_REJECT                     => 'audit_status_unpaid_3',
        self::WF_ACTION_CANCEL                     => 'audit_status_unpaid_4',
        self::WF_ACTION_TIMEOUT                    => 'audit_status_unpaid_5',
        self::WF_ACTION_CC                         => 'audit_status.7',
        self::WF_ACTION_CREATE                     => 'audit_status_unpaid_0',
        self::WF_ACTION_APPROVAL_SUPERIOR          => 'audit_status_unpaid_21',
        self::WF_ACTION_APPROVAL_EMPTY             => 'audit_status_unpaid_22',
        self::WF_ACTION_REJECTED_ROLL_BACK         => 'audit_status_unpaid_3',
        self::WF_ACTION_APPROVE_COUNTERSIGN        => 'audit_status_unpaid_24',
        self::WF_ACTION_APPROVAL_EMPTY_AUTO_REJECT => 'audit_status_unpaid_25',
        self::WF_ACTION_AUDIT_PENDING              => 'audit_status_unpaid_26',
        self::WF_ACTION_AUDIT_APPROVE              => 'audit_status_unpaid_27',
        self::WF_ACTION_AUDIT_REJECT               => 'audit_status_unpaid_28',
        self::APPROVAL_STATUS_CANCEL_ROLL_BACK     => 'audit_status_unpaid_6',
    ];

    //lnt用到的审批状态翻译
    public static $wf_action_status_lnt = [
        self::WF_ACTION_PENDING                    => 'audit_status_lnt_1',
        self::WF_ACTION_APPROVE                    => 'audit_status_lnt_2',
        self::WF_ACTION_REJECT                     => 'audit_status_lnt_3',
        self::WF_ACTION_CANCEL                     => 'audit_status_lnt_4',
        self::WF_ACTION_TIMEOUT                    => 'audit_status_lnt_5',
        self::WF_ACTION_CC                         => 'audit_status.7',
        self::WF_ACTION_CREATE                     => 'audit_status_lnt_0',
        self::WF_ACTION_APPROVAL_SUPERIOR          => 'audit_status_lnt_21',
        self::WF_ACTION_APPROVAL_EMPTY             => 'audit_status_lnt_22',
        self::WF_ACTION_REJECTED_ROLL_BACK         => 'audit_status_lnt_3',
        self::WF_ACTION_APPROVE_COUNTERSIGN        => 'audit_status_lnt_24',
        self::WF_ACTION_APPROVAL_EMPTY_AUTO_REJECT => 'audit_status_lnt_25',
        self::WF_ACTION_AUDIT_PENDING              => 'audit_status_lnt_26',
        self::WF_ACTION_AUDIT_APPROVE              => 'audit_status_lnt_27',
        self::WF_ACTION_AUDIT_REJECT               => 'audit_status_lnt_28',
        self::APPROVAL_STATUS_CANCEL_ROLL_BACK     => 'audit_status_lnt_6',
        self::WF_ACTION_CONFIRM_PENDING            => 'audit_status_lnt_98',
    ];


    //在audit_log表中，需要解析的action
    public static $wf_action_need_parse = [
        self::WF_ACTION_APPROVAL_EMPTY_AUTO_REJECT,
        self::WF_ACTION_APPROVAL_EMPTY,
        self::WF_ACTION_APPROVAL_SUPERIOR,
        self::WF_ACTION_APPROVE,
        self::WF_ACTION_AUDIT_APPROVE,
        self::WF_ACTION_APPROVE_COUNTERSIGN,
        self::WF_ACTION_TIMEOUT,
        self::WF_ACTION_AUDIT_REJECT,
        self::WF_ACTION_REJECT,
        self::WF_ACTION_FORCE_APPROVAL_PASS,
        self::WF_ACTION_OVERTIME_AUTO_APPROVAL,
        self::WF_ACTION_OVERTIME_AUTO_REJECT,
        self::WF_ACTION_OVERTIME_AUTO_HANDOVER,
        self::WF_ACTION_OVERTIME_AUTO_APPROVAL_COUNTERSIGN,
        self::WF_ACTION_SYS_APPROVAL_PASS,
    ];

    //审批操作
    const WF_ACTION_PENDING = 1; //待审批
    const WF_ACTION_APPROVE = 2; //同意
    const WF_ACTION_REJECT  = 3; //驳回
    const WF_ACTION_CANCEL  = 4; //撤销
    const WF_ACTION_TIMEOUT  = 5; //超时关闭
    const WF_ACTION_CREATE_CANCEL  = 6; //创建撤消申请
    const WF_ACTION_CC  = 7; //抄送
    const WF_ACTION_CREATE  = 8; //创建申请
    const WF_ACTION_APPROVAL_CANCEL  = 9; //申请人再次发起撤销
    const WF_ACTION_REJECT_ROLL_BACK  = 10; //驳回到某个节点
    const WF_ACTION_APPROVAL_SUPERIOR = 21; //上级离职自动转交
    const WF_ACTION_APPROVAL_EMPTY  = 22; // 审批人为空 审批人重复自动审批通过
    const WF_ACTION_REJECTED_ROLL_BACK = 23; //驳回到某一节点，非最终驳回
    const WF_ACTION_APPROVE_COUNTERSIGN  = 24; // 会签同意
    const WF_ACTION_APPROVAL_EMPTY_AUTO_REJECT  = 25; // 审批人为空 自动审批驳回
    const WF_ACTION_AUDIT_PENDING = 26; //待审核
    const WF_ACTION_AUDIT_APPROVE = 27; //审核通过
    const WF_ACTION_AUDIT_REJECT = 28; //审核不通过
    const WF_ACTION_FORCE_APPROVAL_PASS  = 29; // 系统强制审批通过，与审批人为空、离职自动通过性质不同
    const WF_ACTION_CONFIRM_PASS = 30; // 转岗确认同意
    const WF_ACTION_CONFIRM_REJECT = 31; // 转岗确认拒绝
    const WF_ACTION_CREATE_WITHOUT_LOG = 32; // 转岗确认创建
    const WF_ACTION_CONFIRM_CANCEL = 33; // 转岗确认撤销
    const WF_ACTION_CONFIRM_OVERTIME = 34; // 转岗确认超时
    const WF_ACTION_OVERTIME_AUTO_APPROVAL = 35; //审批人超过审批时间，自动同意
    const WF_ACTION_OVERTIME_AUTO_REJECT = 36; //审批人超过审批时间，自动驳回
    const WF_ACTION_OVERTIME_AUTO_HANDOVER = 37; //审批人超过审批时间，超时转交
    const WF_ACTION_OVERTIME_AUTO_APPROVAL_COUNTERSIGN = 38; //审批人超过审批时间，自动同意(会签)
    //泰国出差 修改申请 审批流创建
    const WF_ACTION_EDIT_RECREATE = 39;
    const WF_ACTION_SYS_APPROVAL_PASS = 40;//申请时，系统强制自动通过


    const WF_ACTION_CONFIRM_PENDING = 98; //转岗待确认（仅用于转岗）
    const WF_ACTION_FINAL_COMPLETED = 99; //审批最终结束

    //审批同意
    public static $wf_actions_approval = [
        self::WF_ACTION_APPROVE,
        self::WF_ACTION_APPROVE_COUNTERSIGN,
        self::WF_ACTION_APPROVAL_EMPTY,
        self::WF_ACTION_AUDIT_APPROVE,
        self::WF_ACTION_FORCE_APPROVAL_PASS,
        self::WF_ACTION_CONFIRM_PASS,
        self::WF_ACTION_OVERTIME_AUTO_APPROVAL,
        self::WF_ACTION_OVERTIME_AUTO_APPROVAL_COUNTERSIGN,
    ];
    //审批驳回
    public static $wf_actions_reject = [
        self::WF_ACTION_REJECT,
        self::WF_ACTION_APPROVAL_EMPTY_AUTO_REJECT,
        self::WF_ACTION_AUDIT_REJECT,
        self::WF_ACTION_CONFIRM_REJECT,
        self::WF_ACTION_OVERTIME_AUTO_REJECT,
    ];

    //审批流分组的名称翻译
    public static $logTitle = [
        self::WF_ACTION_PENDING => 'normal_log',//原来流程
        self::WF_ACTION_CREATE_CANCEL => 'cancel_log',//撤销流程
        self::WF_ACTION_EDIT_RECREATE => 'edit_log',//修改流程
    ];


	//关于 OA 库 workflow_config 设置
	const WF_OA_APPROVER_VALUE  = 15; //审批人等于申请人自动通过
    const WF_OA_AUTO_CNF_PASS   = 16; //1-审批人重复自动通过 2-审批人连续重复自动通过 3-审批人手动处理
    const WF_OA_AUTO_CNF_OT_TYPE = 17; //审批超时类型 1=无超时 2=设置整体超时时间 3=设置单一节点超时时间 4=单独设置每个节点超时时间 5=设置表单中字段为超时时间
    const WF_OA_AUTO_CNF_OT_TYPE_DAYS = 18; //超时天数
    const WF_OA_AUTO_CNF_OT_TYPE_POLICY = 19; //超时后处理方式，1=自动通过 4=自动驳回 5=超时关闭
    const WF_OA_OT_FORM = 20; //超时字段

    //审批节点对应审批人

    //编号1 ~ 100为通用的逻辑
    const WF_NODE_DESIGNATE_OTHER = 2; //指定的员工工号
    const WF_NODE_MANAGER         = 3; //上级
    const WF_NODE_DEPARTMENT_MANAGER = 4; //部门负责人
    const WF_NODE_SUPERVISOR      = 5; //supervisor
    const WF_NODE_DM              = 6; //DM
    const WF_NODE_RM              = 7; //RM
    const WF_NODE_AM              = 8; //AM
    const WF_NODE_GROUP_BOSS      = 9; //Group Boss
    const WF_NODE_CEO             = 10; //CEO
    const WF_NODE_COO             = 11; //COO
    const WF_NODE_CFO             = 12; //CFO
    const WF_NODE_CPO             = 13; //CPO
    const WF_NODE_SPEC_DEPARTMENT_MANAGER = 14; //指定部门负责人
    const WF_NODE_HRBP            = 15; //部门对应的HRBP
    const WF_NODE_STORE_MANAGER   = 16; //网点负责人
    const WF_NODE_MULTI_DEPARTMENT_MANAGER   = 17; //连续多个部门负责人
    const WF_NODE_DM_BY_ORG       = 18; //根据组织架构去找DM
    const WF_NODE_AM_BY_ORG       = 19; //根据组织架构去找AM
    const WF_NODE_SS              = 20; // shop supervisor
    const WF_NODE_SOM             = 21; //shop operations manager
    const WF_FIRST_LEVEL_DEPARTMENT_MANAGER = 23;//部门是BU级别，找当前BU负责人；如果是 C-Level级别 找当前c-level负责人；如果是普通部门则直接找一级部门负责人
    const WF_NODE_JOB_IDS         = 22; // 根据网点类型获取职位下的人
	const WF_APPLICANT            = 24; // 审核人等于申请人
    const WF_NODE_FIRST_BU_DEPARTMENT_MANAGER = 25; // 申请人部门属于BU找 BU负责人 ； 如果部门属于cLevel找 clevel负责人； 否则找GroupCEO
    const WF_NODE_CLEVEL_MANAGER  = 26; // 找部门clevel负责人
    const WF_NODE_FIRST_BU_DEPARTMENT_MANAGER_BY_FORM = 27; // FORM部门属于BU找 BU负责人 ； 如果部门属于cLevel找 clevel负责人； 否则找GroupCEO
    const WF_NODE_BP_HEAD         = 28; //找BP Head
    const WF_NODE_JOB_TITLE_SAME_DEPT = 29; //指定职位-与申请人所在部门相同
    const WF_NODE_JOB_TITLE_SAME_FIRST_DEPT = 30; //指定职位-与申请人所在一级部门相同
    const WF_NODE_ROLE      = 31; //根据指定角色查找审批人
    const WF_NODE_HR_SERVICE = 32; //对应管辖范围的HR Service角色

    const WF_NODE_AUDITOR_TYPE_COMPLEX = 99; //存在多个节点逻辑复合
    // 编号100以上为不通用的逻辑
    const WF_NODE_SPEC_JOB_TITLE = 101; //申请人上级或上上级，有Sales Manager职位
    const WF_NODE_DI = 102; // 审批人动态传入
    const WF_NODE_SUPERVISOR_MANAGER = 103; //找网点正主管如果找不到则找申请人的上2级上级
    const WF_NODE_ASSET_HUB_MANAGER = 105; //根据申请人所在网点 找对应网点的 xx职位的人
	const WF_NODE_HUB_HEADQUARTER_JOB_TITLE = 106; //属于Hub Headquarter的 xx职位的人
	const WF_NODE_DATA_SUPPORT_JOB_TITLE = 107; //属于属于Data Support小组的 xx职位的人
	const WF_NODE_JOB_TITLE = 108; //属获取职位下的人
    const WF_NODE_DEPARTMENT_MANAGER_V3 = 110; //根据申请人所在部门查找申请人组织负责人 [1 ~ 4级部门负责人 or BU or Clevel]
    const WF_NODE_DEPARTMENT_JOB_TITLE_FORM = 112; //获取指定部门指定职位的人
    const WF_NODE_HUB_STANDARDIZATION = 121; //分拨总部标准化部门下全部员工(For: 分拨外协加班、分拨外协补卡)
    const WF_NODE_HUB_STORE_MANAGER = 122; //分拨经理(For: 分拨外协补卡)
    const WF_NODE_HUB_SUB_DEPARTMENT_BY_NAME = 123; //分拨标准化or行政

    const WF_NODE_HUB_OT_CC_BY_STORE = 131; //hub 加班申请 抄送节点 根据表单网点查找指定抄送人


    /***************下面是对接可视化的类型手动的谨慎使用*****************/
    const WF_NODE_DEPARTMENT_MANAGER_V3_BY_FORM = 111; //根据FORM提交的部门查找申请人组织负责人 [1 ~ 4级部门负责人 or BU or Clevel]
    const WF_NODE_MANAGER_FROM         = 113; //根据表单找上级
    const WF_NODE_STORE_MANAGER_FROM   = 114; //根据表单找网点负责人
    const WF_NODE_DM_BY_ORG_FROM       = 115; //根据表单组织架构去找DM
    const WF_NODE_AM_BY_ORG_FROM       = 116; //根据表单组织架构去找AM
    const WF_NODE_HRBP_FROM            = 117; //根据表单找部门对应的HRBP
    const WF_NODE_HASHTABLE_FROM       = 118; //(废弃)根据表单找分拨经理
    const WF_NODE_HUB_AREA_MANAGER     = 119; //根据表单-工作网点按照组织架构去找分拨经理
    const WF_NODE_STORE_MANAGER_MUL_FROM= 124; //根据表单找网点负责人(多个网点版本)
    const WF_NODE_DM_BY_ORG_MUL_FROM    = 125; //根据表单组织架构去找DM(多个网点版本)
    const WF_NODE_AM_BY_ORG_MUL_FROM    = 126; //根据表单组织架构去找AM(多个网点版本)
    const WF_NODE_DEPARTMENT_MANAGER_MUL_BY_FORM = 127; //根据FORM提交的部门查找申请人组织负责人(多部门) [1 ~ 4级部门负责人 or BU or Clevel]
    const WF_NODE_HRBP_MUL_FROM         = 128; //根据表单找部门对应的HRBP(多个部门、网点版本)
    const WF_NODE_BU_C_LEVEL_FROM       = 129; //FORM部门属于BU找 BU负责人 ； 如果部门属于cLevel找 clevel负责人； 否则找GroupCEO(多个部门)
    const WF_NODE_HR_SERVICE_FROM       = 130; //根据表单找部门对应的HR SERVICE
    const WF_NODE_STAFF_SELF_FROM       = 132; //根据表单-员工本人
    //ToDo 看看新增的数值有没有再加！！！ 比如 131

    //节点类型为部门负责人的类型
    public static $nodeDeptType = [
        self::WF_NODE_DEPARTMENT_MANAGER,
        self::WF_NODE_SPEC_DEPARTMENT_MANAGER,
        self::WF_NODE_MULTI_DEPARTMENT_MANAGER,
        self::WF_NODE_FIRST_BU_DEPARTMENT_MANAGER,
        self::WF_NODE_CLEVEL_MANAGER,
        self::WF_NODE_FIRST_BU_DEPARTMENT_MANAGER_BY_FORM,
        self::WF_NODE_DEPARTMENT_MANAGER_V3,
        self::WF_NODE_DEPARTMENT_MANAGER_V3_BY_FORM,
    ];

    //当审批人节点为空时，处理策略
    const WF_EMPTY_POLICY_AUTO_PASS = 1; //自动审批通过
    const WF_EMPTY_POLICY_DESIGNATE_OTHER = 2; //让指定的人审批
    const WF_EMPTY_POLICY_DESIGNATE_MANAGER = 3; //让上级审批， 上级离职一直找到在职的上级
    const WF_EMPTY_POLICY_AUTO_REJECT = 4; //自动审批驳回

    //节点类型
    const NODE_SUBMITTER    = 0;  //申请人节点
    const NODE_APPROVER     = 1;  //审批人或签节点
    const NODE_CC           = 2;  //抄送节点
    const NODE_COUNTERSIGN  = 3;  //审批人会签节点
    const NODE_FINAL        = 99; //结束节点

	//获取审批人类型
	const NODE_WORKFLOW_APPROVER    = 1;  //获取审批人
	const NODE_WORKFLOW_CC    = 2;  //获取申请人

    //自动审批类型
    const WF_AUTO_PROCESS_TYPE_1 = 1; //自动审批同意
    const WF_AUTO_PROCESS_TYPE_2 = 2; //审批人连续重复
	const WF_AUTO_PROCESS_TYPE_3 = 3; //审批人等于申请人自动通过
    const WF_AUTO_PROCESS_TYPE_4 = 4; //审批人重复(连续 + 不连续)
    const WF_AUTO_PROCESS_TYPE_5 = 5; //自动审批驳回
    const WF_AUTO_PROCESS_TYPE_6 = 6;   // 强制自动通过，与审批人为空或者离职自动通过性质不同
    const WF_AUTO_PROCESS_TYPE_7 = 7;   // 申请时，强制自动通过

    public static $autoType = [
        self::WF_AUTO_PROCESS_TYPE_1 => 'auto_audit_1',
        self::WF_AUTO_PROCESS_TYPE_2 => 'auto_audit_2',
    ];

    //系统ID
    const SYSTEM_STAFF_ID  = 10000;

    //数据是否有效
    const DATA_VALID        = 0; //数据有效
    const DATA_DELETED      = 1; //数据已删除

    //加班权限控制最高职级
    const OT_GRADE_MAX = 17;
    //泰国加班 定制部门指定最高职级
    const OT_HUB_GRADE_MAX = 16;


    //加班类型
    const OT_TYPE_1         = 1; //工作日加班
    const OT_TYPE_2         = 2; //休息日和假期超时加班
    const OT_TYPE_4         = 4; //休息日和假期加班
    public static $overtime_type = [
        self::OT_TYPE_1 => '5107',
        self::OT_TYPE_2 => '5108',
        self::OT_TYPE_4 => '5111',
    ];

    //加子班类型
    const OT_SUB_TYPE_0         = 0; //1倍日薪
    const OT_SUB_TYPE_1         = 1; //1倍日薪
    const OT_SUB_TYPE_2         = 2; //1.5倍日薪
    const OT_SUB_TYPE_3         = 3; //3倍日薪
    const OT_SUB_TYPE_4         = 4; //可调休
    public static $overtime_sub_type = [
        self::OT_SUB_TYPE_0  => '',
        self::OT_SUB_TYPE_1  => '1_times_salary',
        self::OT_SUB_TYPE_2  => '1.5_times_salary',
        self::OT_SUB_TYPE_3  => '3_times_salary',
        self::OT_SUB_TYPE_4  => 'could_change',
    ];
    //针对 加班 指定部门 需要获取 额外信息
    const OT_STATISTIC_TYPE_1 = 1;//NW 的 dc officer
    const OT_STATISTIC_TYPE_2 = 2;//shop
    const OT_STATISTIC_TYPE_3 = 3;//NB 的 dc officer

    //加班申请 提示文案规则
    const OT_TEMPLATE_1 = 1;//定制1 我也不知道是啥
    const OT_TEMPLATE_2 = 2;//定制2 同上
    const OT_TEMPLATE_3 = 3;//定制3 ffm子公司 迁回来用by  泰国用的定制模板
    const OT_TEMPLATE_4 = 4;//定制4 日薪制员工的 弹窗文案描述
    const OT_TEMPLATE_5 = 5;//ffm 特殊职位 文案


    const AUDIT_MY_REQUEST_LIST  = 1; //我的申请列表
    const AUDIT_MY_APPROVAL_LIST = 2; //我的审批列表

    const DEFAULT_PAGE_SIZE = 20;

    /** 销售CRM相关配置 Start ---- **/
    const SALES_CRM_ACCESS_PROJECT_MANAGEMENT_DEPARTMENT_ID = 22;
    const SALES_CRM_ACCESS_SALES_DEPARTMENT_ID = 40;
    const SALES_CRM_ACCESS_SHOP_DEPARTMENT_ID = 13;
    const SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID = 4;
    const SALES_CRM_ACCESS_FH_DEPARTMENT_ID = 18;
    const SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH = 125;//菲律宾 Flash Philippines Network
	const SALES_CRM_ACCESS_NETWORK_BULKY_ID = 34;//Network Bulky部门
    const SALES_CRM_ACCESS_BULKY_BUSINESS_DEVELOPMENT_ID = 545;//Bulky Business Development部门  th
    const SALES_CRM_ACCESS_GROUP_PROJECT_MANAGEMENT_ID = 388;//Group Project Management部门  th



    /** 销售CRM相关配置 End ---- **/

    const FLOW_CODE_OT_SUB = 'season_1';//加班 子审批流 code
    const FLOW_CODE_OT = 'ot_old';//加班 原审批流 code

    //cs工单
    const CS_TICKET_STORE_3 = 3;
    const CS_TICKET_STORE_22 = 22;
    public static $cs_ticker_store = [
        self::CS_TICKET_STORE_3  => 'CS',
        self::CS_TICKET_STORE_22  => 'KAM',
    ];

    public static $ticket_job_title = [
        'area_manager' => 79, //Area Manager
        'network_operations_manager' => 491,//Network Operations Manager
        'shop_operations_manager' => 291,//Shop Operations Manager
        'claim_manager' => 215,//Claim Manager
        'project_manager' => 73, //Project Manager
        'customer_service_director' => 216,//Customer Service Director
        'project_director' => 946, //Project Director
        'hub_area_manager' => 724, //Hub Area Manage
        'hub_director' => 557, //Hub Director
        'claim_supervisor' => 939, //Claim Supervisor
        'key_account_supervisor' => self::JOB_TITLE_KEY_ACCOUNT_SUPERVISOR,

    ];

    const JOB_TITLE_KEY_ACCOUNT_SUPERVISOR = 259;


    //工单状态
    const CS_TICKET_STATUS_WAIT_REPLY = 1; //待回复
    const CS_TICKET_STATUS_READ = 2;  //已读
    const CS_TICKET_STATUS_REPLY = 3;  //已回复
    const CS_TICKET_STATUS_CLOSED =4;//已关闭

    //是否超时
    const CS_TICKET_OVERTIME_YES = 1;//已超时
    const CS_TICKET_OVERTIME_NO = 2;//未超时

    public static $is_overtime = [
        self::CS_TICKET_OVERTIME_YES => 'cs_ticket_overtime_1',
        self::CS_TICKET_OVERTIME_NO => 'cs_ticket_overtime_2',
    ];

    public static $cs_ticket_status = [
        self::CS_TICKET_STATUS_WAIT_REPLY => 'cs_ticket_status_1',
        self::CS_TICKET_STATUS_READ=> 'cs_ticket_status_2',
        self::CS_TICKET_STATUS_REPLY     => 'cs_ticket_status_3',
        self::CS_TICKET_STATUS_CLOSED     => 'cs_ticket_status_4',
    ];
    //工单紧急程度
    const TICKET_SPEED_URGENT = 1;
    const TICKET_SPEED_general = 2;
    public static $ticket_speed = [
        self::TICKET_SPEED_URGENT         => 'ticket_speed_1',
        self::TICKET_SPEED_general        => 'ticket_speed_2',
    ];

    //工单来源
    const DEPARTMENT_CS = 3;
    const DEPARTMENT_KAM = 22;
    public static $cs_department = [
        self::DEPARTMENT_CS => 'department_3',
        self::DEPARTMENT_KAM => 'department_22',
    ];
    //工单类型
    const CS_TICKET_TYPE_URGE = 11;
    const CS_TICKET_TYPE_COMPLAIN = 18;
    public static $cs_ticket_type = [
        self::CS_TICKET_TYPE_URGE => 'cs_ticket_type_11',
        self::CS_TICKET_TYPE_COMPLAIN => 'cs_ticket_type_18',
    ];
    //cs工单，虚假工单类型
    const CS_TICKET_FALSE_REASON_1 = 1;//只回复工单，但是线下没有实行
    const CS_TICKET_FALSE_REASON_2 = 2;//回复不审题
    const CS_TICKET_FALSE_REASON_3 = 3;//没有上传附件
    const CS_TICKET_FALSE_REASON_4 = 4;//没有联系客户
    const CS_TICKET_FALSE_REASON_5 = 5;//虚假上传附件
    const CS_TICKET_FALSE_REASON_6 = 6;//其他


    //不同请假类型 需要展示不同的 表单内容
    const LEAVE_TEMPLATE_TYPE_1 = 1;//菲律宾版 产假 多出来个下拉菜单
    const LEAVE_TEMPLATE_1 = array(
        4 => 'leave_4_4',
        1 => 'leave_4_1',
        2 => 'leave_4_2',
        3 => 'leave_4_3',
    );

    const LEAVE_TEMPLATE_TYPE_2 = 2;//老挝 产假 多出来个下拉菜单
    const LEAVE_TEMPLATE_2 = array(
        4 => 'leave_4_4',
        5 => 'leave_4_5',
    );

    //不同请假类型 需要展示不同的 表单内容
    const LEAVE_TEMPLATE_TYPE_3 = 3;//越南版 产假 多出来个下拉菜单
    const LEAVE_TEMPLATE_3 = array(
        1 => 'leave_4_1_vn',
        2 => 'leave_4_2_vn',
        3 => 'leave_4_3_vn',
        4 => 'leave_4_4_vn',
    );
    //不同请假类型 需要展示不同的 表单内容
    const LEAVE_TEMPLATE_TYPE_4 = 4;//越南版 陪产假
    const LEAVE_TEMPLATE_4 = array(
        1 => 'leave_4_1_vn',
        2 => 'leave_5_2_vn',
        3 => 'leave_4_2_vn',
        4 => 'leave_4_3_vn',
        5 => 'leave_4_4_vn',
        6 => 'leave_5_6_vn',
    );

    const LEAVE_INVALID_MONTH= array('01','02','03');
    const LEAVE_INVALID_DATE = '03-31';

    const LEAVE_INVALID_MONTH_MA= array();
    const LEAVE_INVALID_DATE_MA = '06-30';

    //紧急需求 指定日期 当成ph https://l8bx01gcjr.feishu.cn/docs/doccnu1Q45aLiYLc5aHs6sB4une
    const SB_PH = '2021-12-06';

    //电子合同-合同状态
    //合同状态
    const CONTRACT_STATUS_ADD = 10;  //待添加
    const CONTRACT_STATUS_SEND = 20; //待发送
    const CONTRACT_STATUS_SIGNATURE = 30; //待员工签署
    const CONTRACT_STATUS_SIGN_PROCESSING = 35; //员工签字处理中
    const CONTRACT_STATUS_AUDIT = 40; //待复核
    const CONTRACT_STATUS_ARCHIVE = 50; //待归档
    const CONTRACT_STATUS_ARCHIVED = 60; //已归档
    const CONTRACT_STATUS_RESCIND = 70; //已解除
    const CONTRACT_STATUS_RDNEWAL = 80; //待续约
    const CONTRACT_STATUS_RDNEWED = 81; //已续约
    const CONTRACT_STATUS_EXPIRES = 90; //已到期
    const CONTRACT_STATUS_UNSIGNED = 100; //未签字
    const CONTRACT_STATUS_FEEDBACK = 110; //反馈处理
    const CONTRACT_STATUS_REFUSE   = 120; //拒绝

    const OSS_FILE_TYPE_1 = 1;
    const OSS_FILE_TYPE_2 = 2;
    const OSS_FILE_TYPE_3 = 3;
    const OSS_FILE_TYPE_4 = 4;
    const OSS_FILE_TYPE_5 = 5;//油费报销 发票
    const OSS_FILE_TYPE_6 = 6;//油费报销 签名
    const OSS_FILE_TYPE_7 = 7;//油费报销 申请单
    const OSS_FILE_TYPE_8 = 8;//油费报销 过路费发票
    const OSS_FILE_TYPE_9 = 9;//油费报销 详情pdf
    const OSS_FILE_TYPE_10 = 10;//油费报销 用车记录 图片pdf
    const OSS_FILE_TYPE_11 = 11;//扣税证明 pdf

    // HC历史数据最大id, 区分HC审批流的新老数据
    const HC_OLD_DATA_MAX_ID = [
        'dev' => 923,
        'training' => 1454,
        'prod' => 49737,
    ];

    //登陆app 类型
    const EQM_TYPE = [
        'KIT'      => 1,
        'BY'       => 1,
        'BACKYARD' => 3,
    ];

    //员工网点类型
	public static  $organization_type = array(
		'ORGANIZATION_TYPE_1' => 1,
		'ORGANIZATION_TYPE_2' => 2,
	);

	//图片识别状态
    const IMAGE_IDENTIFY_STATE_NOT_SUBMIT = 0; //未提交
    const IMAGE_IDENTIFY_STATE_SUC = 1; //识别成功
    const IMAGE_IDENTIFY_STATE_ERR = 2; //识别失败

    //识别图片类别
    const IDENTIFY_DRIVING_LICENCE = 1; //驾驶证
    const IDENTIFY_REGISTRATION_CERTIFICATE = 2; //车辆登记证书图片
    const IDENTIFY_VEHICLE_TAX_CERTIFICATE = 3; //车辆税证明图片
    const IDENTIFY_ANALYZE_FACE_QUALITY = 4; //人脸图像质量评估
    const IDENTIFY_ANALYZE_SEARCH_FACE = 5; //人脸搜索比对
    const IDENTIFY_FACE_CHECK = 6;                //人脸查重
    const IDENTIFY_FACE_DATA_SYNCHRONIZATION = 7; //人脸数据同步AI
    const IDENTIFY_BANK_CARD = 8; //银行卡存折
    const IDENTIFY_LIVE_COMPARE_FACE = 9; //人脸比对
    const IDENTIFY_LIVE_CHECK = 10; //人脸活体检测
    const IDENTIFY_MASK_CHECK = 11; //口罩检测
    const IDENTIFY_FACE_BLACKLIST = 12;//人脸黑名单

    const LEAVE_TYPE_1 = 1;//年假
    const LEAVE_TYPE_2 = 2;//带薪事假
    const LEAVE_TYPE_3 = 3;//带薪病假
    const LEAVE_TYPE_4 = 4;//产假
    const LEAVE_TYPE_5 = 5;//陪产假
    const LEAVE_TYPE_6 = 6;//6:国家军训假
    const LEAVE_TYPE_7 = 7;//7:家人去世假
    const LEAVE_TYPE_8 = 8;//8:绝育手术假
    const LEAVE_TYPE_9 = 9;//9:个人受训假
    const LEAVE_TYPE_10 = 10;//10:婚假
    const LEAVE_TYPE_11 = 11;//11:出家假
    const LEAVE_TYPE_12 = 12;//12:不带薪事假
    const LEAVE_TYPE_13 = 13;//13:调休 （已废弃 马来启用）
    const LEAVE_TYPE_14 = 14;//14:其他（已废弃）
    const LEAVE_TYPE_15 = 15;//15:休息日
    const LEAVE_TYPE_16 = 16;//16:公司培训假
    const LEAVE_TYPE_17 = 17;//17：产检
    const LEAVE_TYPE_18 = 18;//18:无薪病假
    const LEAVE_TYPE_19 = 19;//19 跨国探亲假
    const LEAVE_TYPE_20 = 20;//20 单亲育儿假
    const LEAVE_TYPE_21 = 21;//21 女性特殊假
    const LEAVE_TYPE_22 = 22;//22 家庭暴力假
    const LEAVE_TYPE_23 = 23;//23 紧急假
    const LEAVE_TYPE_24 = 24;//24 无薪休假
    const LEAVE_TYPE_25 = 25;//25 隔离假
    const LEAVE_TYPE_26 = 26;//26 带薪病假（新冠治疗）
    const LEAVE_TYPE_27 = 27;//27 住院假（马来）
    const LEAVE_TYPE_28 = 28;//28 体恤假（马来）
    const LEAVE_TYPE_29 = 29;//29 考试假（马来）
    const LEAVE_TYPE_30 = 30;//30 孩子结婚假(越南)
    const LEAVE_TYPE_31 = 31;//31丧家 leave_31
    const LEAVE_TYPE_32 = 32;//32流产假 leave_32
    const LEAVE_TYPE_33 = 33;//33其他家人丧家 leave_33
    const LEAVE_TYPE_34 = 34;//34儿子割礼 leave_34
    const LEAVE_TYPE_35 = 35;//35儿童洗礼 leave_35
    const LEAVE_TYPE_36 = 36;//36朝觐假 leave_36
    const LEAVE_TYPE_37 = 37;//灾难假 马来先加的
    const LEAVE_TYPE_38 = 38;//病假 泰国 二合一类型
    const LEAVE_TYPE_39 = 39;//马来个人代理 类型
    const LEAVE_TYPE_40 = 40;//泰国 个人代理类型
    const LEAVE_TYPE_41 = 41;//菲律宾 个人代理类型
    const LEAVE_TYPE_42 = 42;//马来 国民假期

    const LEAVE_COLOR_YELLOW = 1;//请假类型 底色 黄色 表示带薪
    const LEAVE_COLOR_GRAY = 2;//请假类型 灰色 表示不带薪
    const LEAVE_TIME_TYPE_MORNING = 1; //请假上午
    const LEAVE_TIME_TYPE_AFTERNOON = 2; //请假下午

    //证明 用途  1 Loan Application, 2 Auto loan, 3 Housing loan, 4 Credit card Application, 5 Visa Application
    public static $use_type = array(
        1 => 'Loan Application',
        2 => 'Auto loan',
        3 => 'Housing loan',
        4 => 'Credit card Application',
        5 => 'Visa Application',
        6 => 'Other',
    );

    const IS_TH_NATIONALITY = 1;    //是否是泰国系统的泰国国籍用户
    const IS_MY_NATIONALITY = 3;    //是否是马来国籍用户

    //灭火器类型
    public static $extinguisher_type = [
        '1' => 'co2_extinguisher',
        '2' => 'halo_tron_extinguisher',
        '3' => 'dry_chemical_extinguisher',
    ];

    //检查单问题
    public static $extinguisher_check_list = [
        'extinguisher_problem_1',
        'extinguisher_problem_2',
        'extinguisher_problem_3',
        'extinguisher_problem_4',
        'extinguisher_problem_5',
        'extinguisher_problem_6',
        'extinguisher_problem_7',
        'extinguisher_problem_8',
    ];


    //对应文档 https://l8bx01gcjr.feishu.cn/docs/doccnd2cFaotR11am5ZVx0v6vy6
    //https://l8bx01gcjr.feishu.cn/docs/doccnFvgc9rQMjLaT39VKaxlYhh
    //https://l8bx01gcjr.feishu.cn/docs/doccnHABfC1fuSfiNsJAVjk6cnf
    public static $ai_error_num = [
        'INTERNAL_SERVER_ERROR' => -1,//http code 500 502 504
        'IMAGE_DOWNLOAD_ERROR'  => -2,//400
        'IMAGE_TOO_LARGE'       => -2,
        //3 -对应 未知错误和 500+ 以及超时 http code 是0
        'NO_FACE_DETECTED'      => -4,//400
        'MASK_DETECT_FAILED'    => -5,
        'SIGNATURE_FAILURE'     => -6,
        'SECRET_ID_NOT_FOUND'   => -6,
        'INVALID_PARAMETER'     => -7,
        'MISSING_PARAMETER'     => -7,
    ];

    const AI_INTERFACE_1 = 1;//人脸识别
    const AI_INTERFACE_2 = 2;//活体检测
    const AI_INTERFACE_3 = 3;//口罩识别

    public static $tp3_state = [
        'un_submit'=>1,
        'submitted'=>2,
        'refill' =>3,
    ];

    public static $tp1_state = [
        'wait_approve'=>1, //待审核
        'agree'=>2,//同意
        'reject' =>3,//拒绝
    ];

    public static $hr_interview_offer_state = [
        'sended'=>1, //已发送
        'canceled'=>2, //已取消
        'offer_sign_cancel'=>3, //offer签字撤销
        'salary_cancel'=>4, //薪资审批撤销
        'offer_sign_revoke'=>5, //offer签字驳回
    ];
    //简历扩展表 resume_extent 承诺书签字状态
    const COMMITMENT_SIGN_STATE_YES = 2;//已签署
    const COMMITMENT_SIGN_STATE_NO = 1;//待签署

    public static $commitment_sign_state = [
        self::COMMITMENT_SIGN_STATE_YES => '已签署',
        self::COMMITMENT_SIGN_STATE_NO => '待签署',
    ];

    //hr_entry status
    const STATUS_EMPLOYEED = 1;//已入职
    const STATUS_TO_BE_EMPLOYEED = 2;//待⼊职
    const STATUS_NOT_EMPLOYEED = 3;//未⼊职

    //IT工单审核权限
    const IT_TICKET_AUDIT_POWER = 'it_ticket_audit_power';
    //IT工单审核权限
    const XZ_TICKET_AUDIT_POWER = 'xz_ticket_add_power';

    //资产工单-是否开放key
    const ASSET_WORK_ORDER_IS_OPEN = 'asset_work_order_is_open';
    //资产工单-是否开放全员key
    const ASSET_WORK_ORDER_IS_OPEN_ALL_STAFF = 'asset_work_order_is_open_all_staff';
    //资产工单-非全员开放配置规则key
    const ASSET_WORK_ORDER_OPEN_RULE = 'asset_work_order_open_rule';
    //资产工单-审批权限配置
    const ASSET_WORK_ORDER_AUDIT = 'asset_work_order_audit';
    //耗材入口权限控制
    const MATERIAL_WMS_OPEN_RULE = 'material_wms_open_rule';
    //资产入口-是否开启实习生
    const MATERIAL_ASSET_OPEN_INTERN = 'material_asset_apply_formal_intern_open';
    //IT工单设备类型翻译key

    const SYSTEM_EXTERNAL_APPROVAL_WHITE_LIST = 'system_external_approval_white_list';//外协白名单
    const SYSTEM_EXTERNAL_APPROVAL_USE_WHITE_LIST_STORE_CATEGORY = 'use_os_white_list_store_category';//白名单网点类型
    public static $it_ticket_item_type = [
        1 => 'ticket_item_type_1',
        2 => 'ticket_item_type_2',
        3 => 'ticket_item_type_3',
        4 => 'ticket_item_type_4',
        5 => 'ticket_item_type_5',
        6 => 'ticket_item_type_6',
        7 => 'ticket_item_type_7',
        8 => 'ticket_item_type_8',
        9 => 'ticket_item_type_9',
        10 => 'ticket_item_type_10',
        11 => 'ticket_item_type_11',
        12 => 'ticket_item_type_12',
        13 => 'ticket_item_type_13',
        14 => 'ticket_item_type_14',
    ];

    //IT工单info翻译key
    const IT_TICKET_INFO = 'ticket_info';


    //未删除
    const IS_DELETED_NO = 0;

    //员工离职原因
    const STAFF_LEAVE_TYPE_1 = 1;//自愿离职
    const STAFF_LEAVE_TYPE_2 = 2;//辞退（不赔偿）
    const STAFF_LEAVE_TYPE_3 = 3;//辞退（赔偿）
    const STAFF_LEAVE_TYPE_4 = 4;//合同终止

    //qaqc角色id
    const QAQC_ROLES_ID = 52;
    // 'NETWORK_QC',//网络QC
    const NETWORK_QC_ROLES_ID= 91;
    // 'HUB_QAQC',//分拨QC
    const HUB_QAQC_ROLES_ID= 106;

    //面试不通过类型-翻译前缀
    const INTERVIEW_PASS_TYPE_PREFIX = 'interview_pass_type_';
    //面试不通过类型原因-翻译前缀
    const INTERVIEW_PASS_TYPE_REASON_PREFIX = 'interview_pass_type_reason_';
    //没有不通过原因的resaon
    const INTERVIEW_PASS_NO_REASON = 0;
    const INTERVIEW_PASS_TYPE_DEFAULT = 0;
    //没有面试不通过原因的type
    const INTERVIEW_OUT_TYPE_DEFAULT = 0;
    const INTERVIEW_OUT_TYPE_1 = 1;
    //面试不通过类型
    const INTERVIEW_OUT_TYPE = 0;
    //面试不通过业务类型
    const RESUME_PASS_BUSINESS_TYPE = 1;
    const INTERVIEW_PASS_BUSINESS_TYPE = 2;
    const RESUME_LOG_BUSINESS_TYPE = 3;
    //未删除
    const DELETED_NO = 0;
    //已删除
    const DELETED_YES = 1;
    //精度位数 写入的时候 取3位 读的时候取2位
    const ROUND_NUM = 5;
    const ROUND_NUM_READ = 2;


    //年假操作 扣除 或者返还
    const YEAR_ADD = 1;
    const YEAR_RE_BACK = 2;

    //c 级别 所有国家都是20天
    const C_LEVEL_DAYS = 20;
    //马来 合同工 发放的额度基数
    const UN_NORMAL_STAFF_DAYS = 8;

    //带薪事假 转正的 7天额度 没转正 固定给3天
    const PERSONAL_DAYS = 7;
    const PERSONAL_DAYS_UN = 3;

    //菲律宾 病假没转正 临时天数
    const SICK_DAYS = 3;
    //菲律宾 育儿假天数
    const SINGLE_DAYS_UN = 7;

    const HEAD_OFFICE = 'Head Office';//总部 名称
    const HEAD_OFFICE_ID = '-1';//总部 id

    const SALARY_LEAVE = 1;//带薪假期
    const UN_SALARY_LEAVE = 2;//不带薪假期

    const LEAVE_NEED_IMG = 1;//图片必填

   const ATTENDANCE_SHIFT_INDEX_1 = 1;
   const ATTENDANCE_SHIFT_INDEX_2 = 2;
   const ATTENDANCE_SHIFT_INDEX_3 = 3;
   const ATTENDANCE_SHIFT_INDEX_4 = 4;


    //考勤主页面 button 数量
    const ATTENDANCE_BUTTON_NUM_ONE = 2;
    const ATTENDANCE_BUTTON_NUM_TWO = 4;
    // 打卡按钮 状态 给前端用 1可打卡 2 已打卡 3 缺卡 4 灰色 5 灰色 （离当前时间最近的可打上班卡的按钮 样式跟别人不一样）
    public static $attendanceButtonState = array(
        'button_could' => 1,
        'button_had' => 2,
        'button_missed' => 3,
        'button_grey' => 4,
        'button_click_soon' => 5,
    );

    const ATTENDANCE_BUTTON_NORMAL = 0;//正常状态
    const ATTENDANCE_BUTTON_LATE = 1;//迟到标记状态
    const ATTENDANCE_BUTTON_EARLY = 2;//早退标记状态
    const ATTENDANCE_BUTTON_MISS = 3;//缺卡标记

    const OVERTIME_DURATION_IS_GRAY = 1;//加班时长 灰色不可选
    const OVERTIME_DURATION_IS_NOT_GRAY = 0;//加班时长 可选


    //加班 针对 ct area1 大区 特殊定制逻辑
    const CT_AREA1 = 13;
    public static $region = [
        'ct_area1' => self::CT_AREA1,
    ];

    const OVERTIME_LIMIT_DURATION = 4;//加班特殊逻辑限制时长最多4小时
    const DC_EFFECT_NUM = 300;//马来计算 dc 人效 分母固定值 改成可配置 这个废弃
    const OT_PARCEL_BASE = 1000;//马来 申请ot 包裹量基数
    const OT_PARCEL_STEP = 500;//马来 申请ot 阶梯值

    //离职申请
    //v15994新资产标记
    const RESIGN_ASSET_TAG_NEW = 1;
    const RESIGN_ASSET_TAG_OLD = 0;


    public const RESPONSE_TYPE_KEY_VALUE = 1; //数据以Key/Value形式返回
    public const RESPONSE_TYPE_DATA_DIRECT = 2; //数据直接返回

    /**
     * My hub 网点分区
     * @var array
     */
    public static $hubWorkPartitionMY =  ['A','B','C','D','EM','CB','QC'];

    public static $vehicleTypeCategoryMY = [
        1 => 'Pick-up (sedan)',
        2 => 'Van',
        3 => 'Lorry',
        4 => 'Van Project - Van',
        5 => 'Panel Van',
        6 => 'Window Van',
        7 => 'Car',
        8 => 'MPV',
        9 => 'Van Project - Lorry',
    ];

    //简历是否沟通状态
    public static $resume_state_code = [
        'uncommunicated'      => 1,   //未沟通
        'communicated'        => 2,   //已沟通
        're_hc_wait_feedback' => 3,   //重新关联hc待反馈
    ];
}
