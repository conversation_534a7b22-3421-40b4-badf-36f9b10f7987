<?php

namespace FlashExpress\bi\App\library;

/**
 * 发生http请求
 * Class HttpCurl
 * @package FlashExpress\bi\App\library
 */
final class HttpCurl
{
    const REQUEST_METHOD_GET    = 'GET';
    const REQUEST_METHOD_POST   = 'POST';
    const REQUEST_METHOD_PUT    = 'PUT';
    const REQUEST_METHOD_DELETE = 'DELETE';
    const REQUEST_METHOD_PATCH  = 'PATCH';

    /**
     * @description 发送Http Get请求
     * @param $url
     * @param $header
     * @return bool|string
     */
    public static function httpGet($url, $header = null)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 500);
        // 为保证第三方服务器与微信服务器之间数据传输的安全性，接口采用https方式调用，必须使用下面2行代码打开ssl安全校验。
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_URL, $url);

        if (!empty($header)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        }

        curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        $res = curl_exec($curl);
        curl_close($curl);

        return $res;
    }

    /**
     * @description 发送Http Post请求
     * @param $url
     * @param $data
     * @param null $header
     * @param null $pwd
     * @param int $timeout
     * @return bool|string
     */
    public static function httpPost($url, $data, $header = null, $pwd = null, int $timeout = 10)
    {
        $curl = curl_init();

        if ($pwd != null) {
            $data = self::buildRequestParam($data, $pwd);
        }

        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, true); // post
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data); // post data
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);


        //curl_setopt($curl,CURLOPT_SSL_VERIFYPEER,true); ;
        //curl_setopt($curl,CURLOPT_CAINFO,dirname(__FILE__).'/cacert.pem');//SSL认证证书
        if (!empty($header)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        }

        $responseText = curl_exec($curl);
        if (curl_errno($curl)) {
            $responseText .= "error: ". curl_error($curl);
        }
        curl_close($curl);

        return $responseText;
    }

    /**
     * http POST 请求
     * @param $url
     * @param $param
     * @param null $header 如果带头信息 返回http状态和响应json 字符串  如果传null 返回json解析后的数组(不带状态码)
     * @param null $pwd
     * @param int $timeout
     * @return bool|mixed|string
     */
    public static function httpPostWithHttpCode($url, $param, $header = null, $pwd = null, $timeout = 10)
    {
        $data     = $param;
        $curl     = curl_init();
        $log_data = is_array($param) ? json_encode($param) : $param;

        if(!isset($header)){
            $header[] = "Content-type: application/x-www-form-urlencoded";
            $header[] = "Accept: application/json";
            $escape = true;
        }

        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, true); // post
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data); // post data
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);

        $responseText = curl_exec($curl);
        if (curl_errno($curl)) {
            $responseText .= "error: ". curl_error($curl);
        }
        //http 状态码
        $return['http_code'] = curl_getinfo($curl,CURLINFO_HTTP_CODE);
        curl_close($curl);

        if(isset($escape)){
            return json_decode($responseText,true);
        }

        $return['response'] = $responseText;
        return $return;
    }

    /**
     * @description 根据参数、密码制作签名
     * @param $data_arr
     * @param $pwd  需要客户密码做加密认证
     * @return bool|string
     */
    public static function buildRequestParam($data_arr, $pwd)
    {
        $sign = '';
        ksort($data_arr);
        foreach ($data_arr as $k => $v) {
            if (($v != null) && ($k != 'sign')) {
                $sign .= $k . '=' . $v . '&';
            }
        }
        $sign .= "key=" . $pwd;

        $data_arr['sign'] = strtoupper(hash("sha256", $sign));

        $requestStr = '';
        foreach ($data_arr as $k => $v) {
            $requestStr .= $k . "=" . urlencode($v) . '&';
        }
        return substr($requestStr, 0, -1);
    }

    /**
     * 支持 PUT DELETE等请求
     * @param $url
     * @param $params
     * @param string $request_method
     * @param null $header
     * @param null $pwd
     * @param null $options
     * @return bool|string
     */
    public static function callInterface(
        $url,
        $params,
        string $request_method = 'POST',
        $header = null,
        $pwd = null,
        $options = null
    ) {
        $curl = curl_init();

        if ($pwd != null) {
            $params = self::buildRequestParam($params, $pwd);
        }

        if (!empty($header)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        }

        if (!empty($options)) {
            curl_setopt_array($curl, $options);
        }

        curl_setopt($curl, CURLOPT_TIMEOUT, 20);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        switch ($request_method) {
            case self::REQUEST_METHOD_GET :
                curl_setopt($curl, CURLOPT_HTTPGET, true);
                break;
            case self::REQUEST_METHOD_POST:
                curl_setopt($curl, CURLOPT_POST, true);
                curl_setopt($curl, CURLOPT_POSTFIELDS, $params);
                break;
            case self::REQUEST_METHOD_PUT :
                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
                curl_setopt($curl, CURLOPT_POSTFIELDS, $params);
                break;
            case self::REQUEST_METHOD_PATCH:
                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'PATCH');
                curl_setopt($curl, CURLOPT_POSTFIELDS, $params);
                break;
            case self::REQUEST_METHOD_DELETE:
                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
                curl_setopt($curl, CURLOPT_POSTFIELDS, $params);
                break;
        }

        $responseText = curl_exec($curl);
        if (curl_errno($curl)) {
            $responseText .= "error: ".curl_error($curl);
        }
        curl_close($curl);
        return $responseText;
    }

    public static function post($url, $body)
    {
        $http = curl_init();
        curl_setopt($http, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($http, CURLOPT_POSTFIELDS, $body);
        curl_setopt($http, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($http, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($http, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($http, CURLOPT_POST, true);
        curl_setopt($http, CURLOPT_TIMEOUT, 15);
        curl_setopt($http, CURLOPT_URL, $url);


        $responseText = curl_exec($http);
        if (curl_errno($http)) {
            $responseText .= "error: ". curl_error($http);
        }
        if (curl_errno($http)) {
            throw new \Exception(curl_error('curl error '.curl_getinfo($http,CURLINFO_HTTP_CODE) . $http));
        }
        curl_close($http);

        return json_decode($responseText,true);
    }

}
