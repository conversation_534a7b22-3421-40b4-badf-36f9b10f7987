<?php

namespace FlashExpress\bi\App\library;


final class ErrCode
{
    /**
     * @Message ('成功')
     */
    const SUCCESS = 1;

    const ERROR = 0;

    const FAIL = -3;//失败

    /**
     * @Message ('验证错误，可对外抛出的错误')
     */
    const VALIDATE_ERROR = 2;

    /**
     * @Message ('系统内部错误')
     */
    const SYSTEM_ERROR = 3;

    /**
     * @Message ('MySQL内部错误')
     */
    const DATABASE_ERROR = 4;

    /**
     * @Message ('缺少必要参数')
     */
    const MISS_ARGS_ERROR = 5;

    /**
     * @Message ('rpc 调用错误')
     */
    const RPC_ERROR = 6;

    //业务操作系列，可以细化定义具体业务报错
    //1000 ~ 1999 审批、审批流相关错误CODE
    /**
     * @Message ('无审批流')
     */
    const WORKFLOW_IS_NULL_ERROR = 1000;

    /**
     * @Message ('审批流数据ERROR')
     */
    const WORKFLOW_DATA_ERROR = 1001;

    /**
     * @Message ('审批流数据ERROR')
     */
    const WORKFLOW_DATA_EXIST_ERROR = 1002;

    /**
     * @Message ('审批流数据配置ERROR')
     */
    const WORKFLOW_DATA_CONFIG_ERROR = 1003;

    /**
     * @Message ('审批流申请人不存在ERROR')
     */
    const WORKFLOW_STAFF_NOT_EXIST_ERROR = 1004;

    /**
     * @Message ('当前审批人无审批权限ERROR')
     */
    const WORKFLOW_CURRENT_USER_DO_NOT_HAVE_PERMISSION = 1005;

    /**
     * @Message ('已存在撤销流程，不能重复申请撤销ERROR')
     */
    const WORKFLOW_EXIST_CANCEL_REQUEST = 1006;

    /**
     * @Message ('无法找到当前审批节点ERROR')
     */
    const WORKFLOW_CAN_NOT_FIND_CURRENT_NODE = 1007;

    /**
     * @Message ('无法找到下一个审批节点ERROR')
     */
    const WORKFLOW_CAN_NOT_FIND_NEXT_NODE = 1008;

    /**
     * @Message ('审批已经结束ERROR')
     */
    const WORKFLOW_APPROVAL_HAS_ENDED = 1009;

    /**
     * @Message ('审批条件错误，条件不存在ERROR')
     */
    const WORKFLOW_VALUATE_FORMULA_METHOD_NOT_EXIST = 1010;

    /**
     * @Message ('审批条件错误，无可用的下一个节点ERROR')
     */
    const WORKFLOW_VALUATE_FORMULA_NO_VALID_ROUTE = 1011;

    /**
     * @Message ('已完成审批')
     */
    const WORKFLOW_AUDIT_HAS_FINISHED = 1201;

    /********************* 转岗 申请: 错误码区间 9000 ～ 10000 end **********************/
    //不存在员工id
    public const JOB_TRANSFER_STAFF_NOT_EXIST_ERROR = 9000;

    //员工不在职
    public const JOB_TRANSFER_STAFF_NOT_ON_JOB_ERROR = 9001;

    //转岗人与被转岗人不能是同一个人
    public const JOB_TRANSFER_STAFF_BE_SAME_ERROR = 9002;

    //存在待转岗申请
    public const JOB_TRANSFER_EXIST_TRANSFER_ERROR = 9003;

    //部门错误
    public const JOB_TRANSFER_DEPARTMENT_ERROR = 9004;

    //不存在HRBP
    public const JOB_TRANSFER_NOT_EXIST_HRBP_ERROR = 9005;

    //申请人与调岗人不在相同的大区或片区
    public const JOB_TRANSFER_NOT_IN_PIECE_OR_REGION_ERROR = 9006;

    //申请人与调岗人不在同一个网点
    public const JOB_TRANSFER_NOT_IN_SAME_STORE_ERROR = 9007;

    //无申请权限
    public const JOB_TRANSFER_NO_PERMISSION_ERROR = 9008;

    //生成批次号失败
    public const JOB_TRANSFER_GENERATE_BATCH_NUM_ERROR = 9009;

    //非一线员工
    public const JOB_TRANSFER_NOT_FRONT_LINE_ERROR = 9010;

    //工作交接人不存在
    public const JOB_TRANSFER_HANDOVER_STAFF_NOT_EXIST_ERROR = 9011;

    //工作交接人不在职
    public const JOB_TRANSFER_HANDOVER_STAFF_NOT_ON_JOB_ERROR = 9012;

    //工作交接人错误，请填写被转岗员工的网点内、所属部门内员工或部门负责人
    public const JOB_TRANSFER_HANDOVER_STAFF_NOT_VALID_ERROR = 9013;

    //最早日期为填写当日的后一天
    public const JOB_TRANSFER_AFTER_DATE_ERROR = 9015;

    //工作交接人不能是转岗人自己
    public const JOB_TRANSFER_HANDOVER_STAFF_IS_TRANSFER_STAFF_DATE_ERROR = 9016;

    //试用期不能转岗
    const JOB_TRANSFER_IN_PROBATION_ERROR = 9017;

    //不支持为个人代理申请转岗
    const JOB_TRANSFER_NOT_SUPPORT_AGENT = 9018;

    //HC预算不足
    const HC_BUDGET_EXHAUSTED = 9019;

    //无可用的HCID
    const ERR_NO_AVAILABLE_HC = 9020;

    //国籍非马来
    const JOB_TRANSFER_NATIONALITY_NOT_MY = 9021;

    //转岗确认，确认的数据版本不是最新的
    const JOB_TRANSFER_CONFIRM_DATE_VERSION_NOT_LATEST = 9022;
    //网点ID错误
    const JOB_TRANSFER_STORE_ID_ERROR = 9023;
    //不支持个人代理员工转到指定职位
    const JOB_TRANSFER_AGENT_NOT_SUPPORT_JOB_TITLE = 9024;
    //缺少车类型字段
    const JOB_TRANSFER_AGENT_MISS_ARGS_CAR_TYPE = 9025;
    //无效的车类型
    const JOB_TRANSFER_AGENT_INVALID_CAR_TYPE = 9026;

    //员工不是停职状态
    const STAFF_NOT_STOP_STATUS = 9027;

    /********************* FlashPay在线支付: 错误码区间start **********************/
    const VERIFY_FAILED = 23000;
    const OUT_TRADE_NO_ERROR = 23001;
    const SYNC_FAILED = 23002;
    const OTHER_ERROR = 23003;
    /********************* FlashPay在线支付: 错误码区间end **********************/
    
    /**
     * @Message ('校验登录token过期')
     */
    const LOGIN_CHECK_TOKEN_EXPIRED = 2001;

    /********************* AI 相关: 错误码区间start **********************/



    /**
     * @Message ('AI图片比对不一致')
     */
    const AI_IMAGE_VERIFY_ERROR = 100323;

    /**
     * 人脸比对没有底片
     */
    const AI_IMAGE_VERIFY_FACE_NEGATIVES_EMPTY = 100324;

    /**
     * 在AI人脸黑名单中
     */
    const AI_FACE_BLACKLIST_ERROR = 100400;

    /**
     * @Message ('AI人脸比对缺少参数')
     */
    const AI_IMAGE_VERIFY_MISS_ARGS = 100327;

    //防作弊规则 再次验证前端定制code 其他别用
    const IMAGE_VERIFY_TRY_AGAIN_HTTP = 422;
    const IMAGE_VERIFY_TRY_AGAIN = 100433;

    /**
     * @Message ('图像存在口罩')
     */
    const AI_IMAGE_VERIFY_MASK_EXIST = 100666;

    /**
     * @Message ('人脸检测-存在口罩')
     */
    const ANALYZE_FACE_WITH_MASK = 30001;

    /**
     * @Message ('人脸检测-亮度不合格')
     */
    const ANALYZE_FACE_BRIGHTNESS_NOT_ACCEPT = 30002;

    /**
     * @Message ('人脸检测-图片太模糊')
     */
    const ANALYZE_FACE_SHARPNESS_NOT_ACCEPT = 30003;

    /**
     * @Message ('人脸检测-图像质量评分太低')
     */
    const ANALYZE_FACE_LOW_QUALITY = 30004;

    /**
     * @Message ('人脸检测-人脸区域有遮挡')
     */
    const ANALYZE_FACE_INCOMPLETE = 30005;

    /**
     * @Message ('人脸检测-人脸区域有遮挡')
     */
    const ANALYZE_FACE_OTHER = 30099;

    /********************* AI 相关: 错误码区间start **********************/
    /********************* 设置密码 错误码区间 start **********************/
    public const PASSWORD_FACE_COMPARE_TICKET_TIMEOUT_ERROR = 4001;

    /********************* 设置密码 错误码区间 end **********************/

    /********************* 下班打卡拦截 错误码区间 start **********************/
    public const PUNCH_OUT_EARLY_OFF_ERROR = 6001;

    /********************* 下班打卡拦截 错误码区间 end **********************/

}