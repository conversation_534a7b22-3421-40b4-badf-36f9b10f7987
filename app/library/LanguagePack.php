<?php

namespace FlashExpress\bi\App\library;

use FlashExpress\bi\App\Enums\RedisEnums;
use Phalcon\Mvc\User\Component;
use Phalcon\Translate\Adapter\NativeArray;

class LanguagePack extends Component
{
    public $t;     //当前字符集合
    public $lang = 'th';  //当前语言环境

    public static $box = [];

    public function __construct()
    {
        $this->lang = $this->getDI()['config']['application']['lang'];
    }
    
    /**
     * 设置语言环境
     * @param $lang
     */
    public function setLanguage($lang)
    {
        $this->lang = $this->formatLang($lang);
        $this->t = self::getTranslation($this->lang);
    }
    
    /**
     * 加载语言包
     * @param string $lang
     * @return NativeArray 语言包
     */
    public function getTranslation($lang = ''): NativeArray
    {
        $lang = $this->formatLang($lang ?: $this->lang);

        if(!empty(self::$box[$lang])){
            return self::$box[$lang];
        }

        if (RUNTIME != 'pro' &&  $messages = $this->getCacheWithSerialize(RedisEnums::TRANSLATE_KEY . $lang)) {
            return self::$box[$lang] = new NativeArray(["content" => $messages]);
        }

        $path = APP_PATH . "/messages/" . $lang . ".php";
        if (!file_exists($path)) {
            $default_lang = getCountryDefaultLang();
            $path = APP_PATH . "/messages/$default_lang.php";
        }
        require $path;
        
        // 返回一个翻译的对象
        return self::$box[$lang] = new NativeArray(["content" => $messages]);
    }
    
    /**
     * 格式化语言传递的语言字符
     * @param $lang
     * @return false|string
     */
    public function formatLang($lang)
    {
        $lang = substr($lang, 0, 2);
        if (in_array($lang, [
            'zh',
            'zh-CN',
        ])) {
            $lang = 'zh-CN';
        }
        //客户端手机 非限定语言环境 默认泰语
        $lang_arr = ['th', 'en', 'zh-CN', 'vi', 'lo', 'id'];
        if (!in_array($lang, $lang_arr))
            $lang = getCountryDefaultLang();
        
        return $lang;
    }
    
    /**
     * 获取反序列化后string类型缓存
     * @param $key
     * @return mixed
     */
    public function getCacheWithSerialize($key)
    {
        $data = RedisClient::getInstance()->getClient()->get($key);
        return empty($data) ? $data : unserialize($data);
    }
    
    /**
     * 设置序列化后string类型缓存
     * @param $key
     * @param $val
     * @param int $time
     * @return mixed
     */
    public static function setCacheWithSerialize($key, $val, int $time = 3600)
    {
        $val = serialize($val);
        return RedisClient::getInstance()->getClient()->set($key, $val, $time);
    }
}