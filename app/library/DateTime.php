<?php

namespace FlashExpress\bi\App\library;

class DateTime extends \DateTime
{
    public function __construct(string $time, $timeZone = null)
    {
        parent::__construct($time, $timeZone ? new \DateTimeZone($timeZone) : null);
    }

    public function getIntance($date = null, $timeZone = null)
    {
        return new self($date ? $this->format('Y-m-d H:i:s.u') : null, $timeZone ? $this->getTimezone()->getName() : null);
    }

    public function thailand($dt = 'now')
    {
        return new self($dt, '+0700');
    }

    public function add($interval, $new = false)
    {
        return $new ?
        $this->getIntance()->add($interval) :
        parent::add(new \DateInterval($interval));
    }

    public function sub($interval, $new = false)
    {
        return $new ?
        $this->getIntance()->sub($interval) :
        parent::sub(new \DateInterval($interval));
    }

    public function modify($str, $new = false)
    {
        return $new ?
        $this->getIntance()->modify($str) :
        parent::modify($str);
    }

    public function gmdate($format)
    {
        return gmdate($format, $this->getTimestamp());
    }
    /**
     * 求两个日期之间相差的天数
     * (针对1970年1月1日之后，求之前可以采用泰勒公式)
     * @param string $date1
     * @param string $date2
     * @return number
     */
    public static function diff_date($date1, $date2)
    {
        if ($date1 > $date2) {
            $startTime = strtotime($date1);
            $endTime = strtotime($date2);
        } else {
            $startTime = strtotime($date2);
            $endTime = strtotime($date1);
        }
        $diff = $startTime - $endTime;
        $day = $diff / 86400;
        return intval($day);
    }
}
