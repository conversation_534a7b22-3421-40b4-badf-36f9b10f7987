<?php
namespace FlashExpress\bi\App\library;
/**
 * 
 */
class Image {

    /**
     * 水印
     */
    public static function waterMark($staffid,$name = '')
    {
        $staffid = $staffid ?? '10000';
        $staff_filename = md5($staffid).".png";
        $waterpath = BASE_PATH.'/public/staffwater/';
        if(file_exists($waterpath.$staff_filename)){
            return true;
        }
        if(!is_dir($waterpath)){
            mkdir($waterpath,0777,true);
        }

        //1. 绘制图像资源（创建一个画布）
        $image = imagecreatetruecolor(150, 100);

        //2. 先分配一个透明背景
        $green = imagecolorallocate($image, 255, 255, 255);

        //3. 使用绿色填充画布
        imagefill($image, 0, 0, $green);

        //4. 在画布中绘制图像
        $bai = imagecolorallocate($image, 220, 220, 220);
        //使用指定的字体文件绘制文字
        //参数2：字体大小
        //参数3：字体倾斜的角度
        //参数4、5：文字的x、y坐标
        //参数6：文字的颜色
        //参数7：字体文件
        //参数8：绘制的文字
        // $name = 'รหัสผ่าน';
        if(preg_match('/[\x{4e00}-\x{9fa5}]+/u',$name,$m)){
            //中文
            imagettftext($image, 16, 30, 45, 56, $bai, BASE_PATH.'/public/fonts/msyh.ttc', $staffid .' 
'.$name);
        }else{
            //泰语
            imagettftext($image, 16, 30 , 45, 56, $bai, BASE_PATH.'/public/fonts/THSarabun.ttf', $staffid .' 
'.$name);
        }

        
    

        //5. 在浏览器直接输出图像资源
        // header("Content-Type:image/png");
        imagejpeg($image,$waterpath.$staff_filename, 95);
        //6. 销毁图像资源
        imagedestroy($image);
        return true;
    }
}