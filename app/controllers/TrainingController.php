<?php
namespace FlashExpress\bi\App\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Repository\BySettingRepository;

class TrainingController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 获取员工培训详情
     */
    public function getDetailAction()
    {
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $logger  = $this->getDI()->get('logger');
        $logger->write_log("training getDetailAction param:" . json_encode($paramIn,JSON_UNESCAPED_UNICODE) , 'info');

        try {
            //[2]验证
            $validations = [
                "msg_id" => "Required|Str",
            ];
            $this->validateCheck($paramIn, $validations);

            $staff_id            = $this->userinfo['staff_id'];
            $msg_id              = $paramIn['msg_id'];
            $training_message = new \FlashExpress\bi\App\Server\TrainingMessageServer($this->lang, $this->timezone);

            $message = $training_message->dealMessage($staff_id  ,$msg_id,false);

            if(empty($message)){
                throw  new Exception('can not find message');
            }
            
            $training_info = explode('-',$message['content']);

            //构建参数
            $param   = [
                'staff_id'      => intval($this->userinfo['staff_id']),
                'id'            => intval($message['related_id']),//task_id
                'version'       => intval($training_info[0]),
                'operate'       => intval($training_info[1]),
            ];
            //非创建和编辑的消息
            if(!in_array($training_info[1],[1,2])){
                //查看了改成已读
                $training_message->dealMessage($staff_id  ,$msg_id,true);
            }


            $setting_model = new BySettingRepository($this->lang);
            $url = $setting_model->get_setting('oa_svc');
            $method  = "training_task_detail";

            //[3]发送请求
            $logger->write_log("svc method {$method} param:" . json_encode($param), 'info');

            $fle_rpc = (new ApiClient($url,'',$method, $this->lang));
            $fle_rpc->setParams($param);
            $result = $fle_rpc->execute();

            $logger->write_log("svc method {$method}  url: {$url} response:" . json_encode( $result), 'info');

            if ( isset($result['result']) && $result['result']['code'] == 0) {
                return $this->jsonReturn($this->checkReturn([
                                                                'data' => $result['result']['data']
                                                            ]));
            }
        } catch (Exception $e) {
            $logger->write_log("svc method {$method} param:" . json_encode($param) . " error message :" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }


    /**
     * 保存员工针对培训做出的选择
     * @throws BusinessException
     */
    public function commitChooseAction()
    {
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $logger  = $this->getDI()->get('logger');

        $logger->write_log("training commitChooseAction param:" . json_encode($paramIn,JSON_UNESCAPED_UNICODE) , 'info');

        //[2]验证
        $validations = [
            "msg_id" => "Required|Str",
            "select_type" => "Required|IntIn:2,3",
        ];
        $this->validateCheck($paramIn, $validations);

        $staff_id            = $this->userinfo['staff_id'];
        $msg_id              = $paramIn['msg_id'];
        $training_message = new \FlashExpress\bi\App\Server\TrainingMessageServer($this->lang, $this->timezone);

        $message = $training_message->dealMessage($staff_id  ,$msg_id, true);

        if(empty($message)){
            throw  new Exception('can not find message');
        }

        $training_info = explode('-',$message['content']);
        //构建参数
        $param   = [
            'staff_id'      => intval($this->userinfo['staff_id']),
            'id'            => intval($message['related_id']),
            'version'       => intval($training_info[0]),
            'select_type'   => intval($paramIn['select_type']),
            'reason_rejection'   => $paramIn['reason_rejection'] ?? '',
            'rejection_type'   => intval($paramIn['rejection_type'])
        ];

        $setting_model = new BySettingRepository($this->lang);
        $url = $setting_model->get_setting('oa_svc');

        $method  = "training_task_join";

        //[3]发送请求
        $logger->write_log("svc method {$method} param:" . json_encode($param), 'info');

        $fle_rpc = (new ApiClient($url,'',$method, $this->lang));
        $fle_rpc->setParams($param);
        $result = $fle_rpc->execute();

        $logger->write_log("svc method {$method}  url: {$url} response:" . json_encode( $result), 'info');

        if (!isset($result['result']) || $result['result']['code'] != 0) {
            throw  new BusinessException($result['result']['message']);
        }
        return $this->jsonReturn($this->checkReturn(1));

    }

    /**
     * 拒绝类型下拉列表
     */
    public function rejectionTypeListAction()
    {
        $arr =  [
            [
                "key"  => 1,
                "name" => $this->getTranslation()->_('rejection_type_1'),
            ],
            [
                "key"  => 2,
                "name" => $this->getTranslation()->_('rejection_type_2'),
            ],
            [
                "key"  => 3,
                "name" => $this->getTranslation()->_('rejection_type_3'),
            ],
            [
                "key"  => 4,
                "name" => $this->getTranslation()->_('rejection_type_4'),
            ],
            [
                "key"  => 5,
                "name" => $this->getTranslation()->_('rejection_type_5'),
            ],
        ];
        $this->jsonReturn(self::checkReturn(["code"=>1,"data"=>$arr]));
    }


}
