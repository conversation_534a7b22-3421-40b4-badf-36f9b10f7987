<?php
/**
 * Author: Bruce
 * Date  : 2025-03-25 23:13
 * Description:
 */

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\SettlementIcServer;
use FlashExpress\bi\App\Server\ToolServer;


class SettlementIcController extends Controllers\ControllerBase
{
    /**
     * 初始化
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function initialize()
    {
        $this->cross();
        $method = $this->request->getMethod();
        /* 解决跨域问题 */
        if ($this->request->getMethod() == 'OPTIONS') {
            $this->jsonReturn($this->checkReturn(1));
        }

        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }

        //会带个_url参数
        unset($this->paramIn['_url']);

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }

        //时区配置
        $this->timezone = $this->getDI()['config']['application']['timeZone'];
        $this->lang     = $this->processingDefault($this->request->getHeaders(), 'Accept-Language', 1, 'en');
        if (function_exists('molten_get_traceid')) {
            $traceid = molten_get_traceid();
        }
        header('traceid:' . ($traceid ?? ''));

        //处理语言相关
        $this->languagePack = $this->di->get('languagePack');
        $this->languagePack->setLanguage($this->lang);  //设置语言环境，server  controller repository通用
        $this->t = $this->languagePack->t;              //获取全局语言包赋值到controller层

        $this->baseValidateCheck($this->paramIn);
    }

    /**
     * 基本参数校验
     * @param $paramIn
     * @return bool
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function baseValidateCheck($paramIn)
    {
        $validations = [
            "staff_info_id" => "Required|IntGe:0|>>>:staff_info_id " . $this->getTranslation()->_('miss_args'),
            "period_id"     => "Required|IntGe:0|>>>:period_id " . $this->getTranslation()->_('miss_args'),
            "time"          => "Required|IntGe:0|>>>:time " . $this->getTranslation()->_('miss_args'),
            "auth"          => "Required|StrLenGeLe:1,500|>>>:auth " . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);

        (new SettlementIcServer($this->lang, $this->timezone))->checkAuth($paramIn);
        return true;
    }


    /**
     * 上传文件
     * @throws \Exception
     */
    public function img_uploadAction()
    {
        $paramIn = $this->paramIn;
        $result  = (new ToolServer($this->lang, $this->timezone))->img_upload($paramIn);

        return $this->jsonReturn($result);
    }

    /**
     * 提交 签名
     */
    public function submitAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "url"           => "Required|StrLenGeLe:1,500|>>>:url " . $this->getTranslation()->_('miss_args'),
            "period_id"     => "Required|IntGe:0|>>>:period_id " . $this->getTranslation()->_('miss_args'),
            "staff_info_id" => "Required|IntGe:0|>>>:period_id " . $this->getTranslation()->_('miss_args'),
        ];

        $this->validateCheck($paramIn, $validations);
        $returnArr = (new SettlementIcServer($this->lang,
            $this->timezone))->setLockConf(5)->submitSettlementIcUseLock($paramIn);

        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 获取详情
     */
    public function getDetailAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "period_id"     => "Required|IntGe:0|>>>:period_id " . $this->getTranslation()->_('miss_args'),
            "staff_info_id" => "Required|IntGe:0|>>>:staff_info_id " . $this->getTranslation()->_('miss_args'),
        ];

        $this->validateCheck($paramIn, $validations);
        $result = (new SettlementIcServer($this->lang, $this->timezone))->getDetail($paramIn);

        return $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

}