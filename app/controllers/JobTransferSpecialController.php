<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Server\JobTransferSpecialServer;

class JobTransferSpecialController extends ControllerBase
{
    /**
     * 特殊转岗审批
     */
    public function updateApprovalAction()
    {
        //[1]必填入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $server    = new JobTransferSpecialServer($this->lang, $this->timezone);
        //提交审核
        $returnArr = $server->updateApprovalUseLock($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
}