<?php

namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffRenewContractApplyModel;
use FlashExpress\bi\App\Server\HrStaffRenewContractServer;

class RenewContractController extends ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 更新审批
     */
    public function updateRenewContractAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|IntIn:" . enums::$audit_status['approved'] . "," . enums::$audit_status['dismissed'] . "|>>>:status " . $this->getTranslation()->_('miss_args'),
            "reject_reason" => "IfIntEq:status," . enums::$audit_status['dismissed'] . "|Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1020'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = (new HrStaffRenewContractServer($this->lang, $this->timezone))->setLockConf(5)->updateRenewContractUseLock($paramIn);
        return $this->jsonReturn($returnArr);
    }


    /**
     * 填写续签建议 th
     * @return void
     * @throws \ReflectionException
     */
    public function addProposalAction() {
        $paramIn     = $this->paramIn;
        $validations = [
            "audit_id"        => "Required|IntGt:0|>>>:audit_id " . $this->getTranslation()->_('miss_args'),
            "proposal"        => "Required|IntIn:" . HrStaffRenewContractApplyModel::PROPOSAL_PASS . ',' . HrStaffRenewContractApplyModel::PROPOSAL_REJECTED . "|>>>:proposal " . $this->getTranslation()->_('miss_args'),
            "proposal_reason" => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1020'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $server = Tools::reBuildCountryInstance(new HrStaffRenewContractServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
        $result = $server->addProposal($paramIn);
        $this->jsonReturn($result);
    }

    /**
     * 获取续签详情 消息详情字段
     * @return void
     * @throws \ReflectionException
     */
    public function getStaffRenewMessageContentAction() {
        $paramIn     = $this->paramIn;
        $validations = [
            "audit_id"        => "Required|IntGt:0|>>>:audit_id " . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $server = Tools::reBuildCountryInstance(new HrStaffRenewContractServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
        $result = $server->getStaffRenewMessageContent($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }
}