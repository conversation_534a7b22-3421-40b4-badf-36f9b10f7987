<?php

namespace FlashExpress\bi\App\Controllers;


use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\AdjustRoleServer;

class AdjustRolesController extends ControllerBase
{
    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);

    }

    /**
     * 添加增减角色审批
     * @throws \Exception
     */
    public function createAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];

        $validations = [
            "staff_info_id" => "Required|Int|>>>:" . $this->getTranslation()->_('jobtransfer_0026'),
            "role_ids"      => "Required|Arr",
            "reason"        => "Required|StrLenGeLe:0,500",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $returnArr = (new AdjustRoleServer($this->lang , $this->timezone))->setLockConf(10,true)->createUseLock($paramIn);

            //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 审批增减角色审批
     * @throws \Exception
     */
    public function auditAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];

        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $returnArr = (new AdjustRoleServer($this->lang , $this->timezone))->setLockConf(10,true)->auditUseLock($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取调整角色人详情
     * @throws ValidationException
     */
    public function getStaffInfoAndRolesListAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];

        $validations = [
            "staff_info_id"      => "Required|Int|>>>:" . $this->getTranslation()->_('jobtransfer_0026'),
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $server    = new AdjustRoleServer($this->lang, $this->timezone);
        $returnArr = $server->getStaffInfoAndRolesList($paramIn);

        //[3]数据返回
        $this->jsonReturn(self::checkReturn(["data" => $returnArr]));
    }
}