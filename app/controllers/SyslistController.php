<?php
namespace FlashExpress\bi\App\Controllers;


use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Server\FleetServer;
use FlashExpress\bi\App\Server\PublicServer;
use Exception;
use FlashExpress\bi\App\Server\SysServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class SyslistController extends Controllers\ControllerBase
{
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty(array_values($this->paramIn)[0])) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 获取动态资源列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getSysListAction()
    {
        $paramIn    = $this->paramIn;
        $returnArr  = (new PublicServer($this->lang,$this->timezone))->sysList($paramIn);
        return $this->jsonReturn($returnArr);
    }


    /**
     * 获取全量银行卡信息
     * @return null
     */
    public function getAllBankListAction()
    {
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr  = (new PublicServer($this->lang,$this->timezone))->getAllBankList($paramIn);
        return $this->jsonReturn($this->checkReturn(['data'=>$returnArr]));
    }



    /**
     * 获取静态资源列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaticSysListAction()
    {
        $paramIn    = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $returnArr  = (new PublicServer($this->lang,$this->timezone))->staticSysList($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 获取面试不通过类型-原因
     */
    function getInterviewPassTypeAction(){
        try {
            $data['data'] = (new SysServer($this->lang, $this->timezone))->getInterviewPassType();
            return $this->jsonReturn($this->checkReturn($data));
        }catch (Exception $e){
            $this->getDI()->get('logger')->write_log("getInterviewPassTypeAction 异常信息:".$e->getMessage(),'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取网点列表
     */
    public function storeListAction()
    {
        //[1]参数定义
        $paramIn              = $this->paramIn;
        $paramIn['type']      = 2;
        $paramIn['isAddHead'] = empty($paramIn['isAddHead']) ? 2 : $paramIn['isAddHead'];

        //[2]查询部门列表
        $storeData = (new SysStoreServer())->queryList($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn($storeData));
    }

    /**
     * 获取目标列表枚举值
     */
    public function probationEnumsListAction()
    {
        $data = (new SysServer($this->lang, $this->timezone))->probationEnumsList();
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 地址库
     * @return void
     */
    public function getAddressListAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;

        $validations = [
            "address_type" => "Required|IntIn:1,2,3|>>>:address_type 1 省 2 市 3县 " . $this->getTranslation()->_('miss_args'),
            'code'         => 'IfIntIn:address_type,2,3|Required|Str|>>>: 查询市、县需要传code',
        ];

        $this->validateCheck($paramIn, $validations);

        //[2]查询部门列表
        $result = (new SysServer())->getAddressList($paramIn['address_type'], $paramIn['code'] ?? '',
            $paramIn['query_name'] ?? '');
        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 国家列表
     * @return void
     * @throws BusinessException
     */
    public function getCountryListAction()
    {
        //[2]查询部门列表
        $result = (new SysServer())->getTotalDictionaryRegionByDictCode('address_country_region');
        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }


    /**
     * 获取车型
     * @return Response|ResponseInterface
     */
    public function getCarTypeListAction()
    {
        $fleetServer = new FleetServer($this->lang , $this->timezone);
        //[2]业务处理
        $carTypeList    = $fleetServer->getCarTypeList();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $carTypeList);
    }

    /**
     * 获取省份列表
     * @return Response|ResponseInterface
     */
    public function getProvinceListAction()
    {
        $publicRepo = new PublicRepository();
        //[2]业务处理
        $provinceCodeList    = $publicRepo->getProvinceList();
        return $this->returnJson(ErrCode::SUCCESS, 'success', $provinceCodeList);
    }

}
