<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\ReportServer;
use FlashExpress\bi\App\library\HttpCurl;
use Exception;

class ReportController extends Controllers\ControllerBase
{
    protected $server;

    public function initialize()
    {
        parent::initialize();
        $this->server = ['Report' => new ReportServer($this->lang, $this->timezone)];
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 费用类型
     * @Access   public
     * @Param   request
     * @Return  array
     */
    public function dictReportAction()
    {
        $userinfo = $this->userinfo;
        $paramIn = $this->paramIn;
        $validations = [
            "staff_id"      => "Required|Int|>>>:". $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $returnArr = Tools::reBuildCountryInstance(new ReportServer($this->lang, $this->timezone),[$this->lang, $this->timezone])->dictReportS($userinfo, $paramIn);
        return $this->jsonReturn( self::checkReturn($returnArr) );
    }

    public function getStaffInfoAction()
    {
        $paramIn = $this->paramIn;
        $validations = [
            "staff_id"      => "Required|Int|>>>:". $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($this->paramIn, $validations);

        $paramIn['user_info'] = $this->userinfo;
        $returnArr = (new ReportServer($this->lang, $this->timezone))->getStaffInfo($paramIn);
        return $this->jsonReturn( self::checkReturn($returnArr) );
    }
    /**
     * 创建申请
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addReportAction()
    {
        //[1]入参和验证
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        $validations = [
            "report_id"     => "Required|Int|>>>:". $this->getTranslation()->_('miss_args'), // 被举报人
            "report_type"  => "Required|Int|>>>:". $this->getTranslation()->_('miss_args'), // 申请类型
            "reason"      => "Required|Int|>>>:". $this->getTranslation()->_('miss_args'), // 违规类型
            "event_date"  => "Required|StrLenGeLe:1,500|>>>:event_date ". $this->getTranslation()->_('miss_args'), // 违规日期
            "remark"     => "Required|StrLenGeLe:1,1000|>>>:". $this->getTranslation()->_('report_str_limit'), // 事情描述
        ];
        $this->validateCheck($this->paramIn, $validations);

        $paramIn['url'] = $this->config->hr->hc_api_url;
        if( isset($paramIn['image_path']) && sizeof($paramIn['image_path']) > 10 ) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('upload_no_more_than_10_pictures')));
        }
        //申请操作
        $returnArr = $this->atomicLock(function () use ($paramIn, $userinfo) {

            return (new ReportServer($this->lang, $this->timezone))->addReportS($paramIn, $userinfo);

        },   md5($paramIn['remark']) . '_addReport_' . $this->userinfo['id'] . $paramIn['report_id'], 20, false);

        if ($returnArr === false) { //没有获取到锁
            $this->jsonReturn($this->checkReturn(['code'=> -3,'msg'=>$this->getTranslation()->_('ticket_repeat_msg')]));
        } else {
            $this->jsonReturn($returnArr);
        }
    }

    /**
     * 审核
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkReportAction()
    {
        $reportServer =  (new ReportServer($this->lang, $this->timezone));
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        //状态 1 待审核 2 审核通过 3 驳回
        $validations = [
            "status" => "Required|Required|IntIn:1,2,3,4|>>>:" . $this->getTranslation()->_('miss_args'),
            "audit_id" => "Required|Required|Int|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        //如果驳回，验证驳回理由
        if($paramIn['status'] == 3){
            $validations['reject_reason'] = "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1020');
        }
        $this->validateCheck($paramIn, $validations);

        //如果同意，验证事情描述和图片上传 信息
        if($paramIn['status'] == 2){
            $content_desc = $paramIn['content_desc'] ?? '';
            $img_list = $paramIn['img_list'] ?? [];

            // 验证 图片上传和 事情描述 是否合法
            $reportServer = Tools::reBuildCountryInstance($reportServer, [$this->lang, $this->timezone]);
            $reportServer->validateReportSubmit($paramIn['audit_id'],$content_desc,$img_list,$userinfo['id']);

        }

        //id 和驳回理由转换
        $paramIn['id'] = $paramIn['audit_id'];
        $paramIn['url']       = $this->config->hr->hc_api_url;
        $paramIn['reject_reason'] = addcslashes(stripslashes($paramIn['reject_reason']),"'");
        $res = $this->atomicLock(function () use ($paramIn, $userinfo) {
            $reportServer =  (new ReportServer($this->lang, $this->timezone));
            return $reportServer->updateReportStatus($paramIn, $userinfo);
        }, 'checkReport' . $paramIn['audit_id'], 5, false);

        if ($res === true) {
            //记录 事情描述、图片 补充信息
            if(isset($paramIn['content_desc']) && isset($paramIn['img_list'])){
                $reportServer->insertReportAuditDesc($paramIn,$userinfo);
            }

            $returnArray['data'] = $paramIn['id'];
            //[3]成功数据返回
            $this->jsonReturn(self::checkReturn($returnArray));
        } else{
            //[3]异常数据返回
            $this->getDI()->get('logger')->write_log('ReportController:checkReportAction'. json_encode($paramIn).json_encode($userinfo).json_encode(['res' => $res]),'info');
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1014')));
        }
    }

    /**
     * 处罚运单号。
     * @throws ValidationException
     */
    public function waybillNumberAction()
    {
        $paramIn = $this->paramIn;
        $validations = [
            "report_id"      => "Required|Int|>>>:". $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);

        $paramIn['user_info'] = $this->userinfo;
        $returnArr = (new ReportServer($this->lang, $this->timezone))->waybillNumber($paramIn);
        return $this->jsonReturn(self::checkReturn($returnArr) );
    }
}
