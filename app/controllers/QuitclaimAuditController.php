<?php
/**
 * Author: Bruce
 * Date  : 2023-04-24 19:44
 * Description:
 */

namespace FlashExpress\bi\App\Controllers;

use  FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\QuitclaimServer;


class QuitclaimAuditController extends Controllers\ControllerBase
{

    public function initialize()
    {
        parent::initialize();
        $method = $this->request->getMethod();

        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }

        //会带个_url参数
        unset($this->paramIn['_url']);

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * quitclaim 审批
     */
    public function updateAction()
    {
        $paramIn = $this->paramIn;

        $validations         = [
            "audit_id"           => "Required|Int",
            "status"              => "Required|IntIn:2,3|>>>:status " . $this->getTranslation()->_('miss_args'),
            "reject_reason"      => "StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('1020'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = (new QuitclaimServer($this->lang, $this->timezone))->setLockConf(5)->updateUseLock($paramIn);
        return $this->jsonReturn($returnArr);

    }
}