<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\SalaryServer;
use Exception;

class SalaryController extends Controllers\ControllerBase
{


    public function initialize()
    {
        parent::initialize(); // TODO: Change the autogenerated stub

    }



    /**
     * 更新审批状态
     *
     */
    public function updateApprovalAction()
    {
        $params = $this->paramIn;
        $this->validateCheck([
            "status" => $params['status'],
            "id" => $params['audit_id'],
        ], [
            "status" => 'Required|IntIn:2,3,4',
            "id" => 'Required|Int',
        ]);

        $userinfo = $this->userinfo;
        $this->atomicLock(function () use ($params, $userinfo) {

            (new SalaryServer($this->lang, $this->timezone))->updateApprove($userinfo, $params['audit_id'], $params['status'], isset($params['reject_reason']) && $params['reject_reason'] ? $params['reject_reason'] : '');
        }, 'approval_salary_' . $this->userinfo['staff_id']);

        $this->jsonReturn($this->checkReturn(1));

    }
	
	
	/**
	 * @description: 获取面试记录列表
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/9/26 17:27
	 */
	public function getInterviewRecordAction(){
		$params = $this->paramIn;
		$params['page']  = $params['page'] ?? 1;
		$params['limit']  = $params['limit'] ?? 10;
		$this->validateCheck([
			                     "id" => $params['id'],
			                     'page'  => $params['page'],
			                     'limit' => $params['limit'],
		                     ], [
			                     "id" => 'Required|Int',
			                     "page"  => "Required|Int",
			                     "limit" => "Required|Int",
		                     ]);
		
		$returnData['data'] = (new SalaryServer($this->lang, $this->timezone))->getInterviewRecord($params);
		$this->jsonReturn($this->checkReturn($returnData));
	}
}