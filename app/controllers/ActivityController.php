<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/29/23
 * Time: 8:26 PM
 */


namespace FlashExpress\bi\App\Controllers;

use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use FlashExpress\bi\App\Enums\ActivityEnums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\Models\backyard\ActivityRecordModel;
use FlashExpress\bi\App\Server\ActivityServer;
use FlashExpress\bi\App\Server\AnniversaryServer;
use FlashExpress\bi\App\Server\BirthdayServer;
use FlashExpress\bi\App\Server\FormImgServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

//客户端 活动弹窗公告等
class ActivityController extends ControllerBase
{

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode($this->request->getRawBody(), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    //活动列表 客户端用 首页弹公告活动图片
    public function listAction()
    {
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $param['user_info']     = $this->userinfo;

        $server = new ActivityServer($this->lang,$this->timezone);
        $return = $server->getActivities($param);


        $this->jsonReturn(['code' => 1, 'message' => 'ok', 'data' => $return]);
    }


    //活动点击或者下载分享 统计 h5 调用
    public function clickAction()
    {
        $param  = $this->paramIn;
        $server = new ActivityServer($this->lang, $this->timezone);
        $server->clickIncr($param);
        $this->jsonReturn($this->checkReturn([]));
    }


    //前端 获取 图片url 和对应的活动code
    public function getActivityInfoAction()
    {
        $param = $this->paramIn;
        if (empty($param['msg_id'])) {
            throw new ValidationException('need msg_id');
        }
        $param['staff_id'] = $this->userinfo['staff_id'];

        $server = new ActivityServer($this->lang, $this->timezone);
        $res    = $server->getMsgInfo($param);
        $this->jsonReturn($this->checkReturn(['data' => $res]));
    }


    public function testAction(){
        $qrCode = new QrCode('http://www.baidu.com');
        $qrCode->setErrorCorrectionLevel(ErrorCorrectionLevel::HIGH());
        $dataUri = $qrCode->writeDataUri();
        echo $dataUri;

    }

}
