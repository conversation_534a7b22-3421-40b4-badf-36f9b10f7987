<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON>
 * Date: 2021/8/27
 * Time: 16:29
 */

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Server\AppDownloadServer;



class AppdownloadController extends Controllers\ControllerBase
{
    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }

    }

    public function onConstruct()
    {
        parent::onConstruct();

    }

    public function listAction()
    {
        try {
            //[1]入参和验证
            $returnArr = (new AppDownloadServer())->getAppDownloadList();

            //[3]数据返回
            $this->jsonReturn($returnArr);

        } catch (\Exception $e) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }


    }

}