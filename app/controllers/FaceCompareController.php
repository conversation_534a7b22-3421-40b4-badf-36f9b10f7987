<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 6/6/24
 * Time: 5:17 PM
 */


namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\FaceCompareServer;
use FlashExpress\bi\App\Server\PasswordServer;
use FlashExpress\bi\App\Server\StaffWorkFaceVerifyRecordServer;

class FaceCompareController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();

    }


    /**
     * 人脸上传
     * @return void
     * @throws BusinessException
     * @throws \ReflectionException
     */
    public function formatUrlFleAction()
    {
        $paramIn     = $this->paramIn;
        $paramIn['staff_info_id'] = $this->userinfo['id'];
        $sever  = new FaceCompareServer($this->lang, $this->timezone);
        $result = $sever->formatImgUrl($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $result['result']]));

    }



    /**
     * 人脸比对
     * @return void
     */
    public function facialCompareAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "face_image" => "Required|StrLenGeLe:10,500|>>>:face_image error",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_info_id'] = $this->userinfo['id'];
        $paramIn['biz_type'] = empty($this->paramIn['biz_type']) ? FaceCompareServer::BIZ_TYPE_SICK_LEAVE : $this->paramIn['biz_type'];
        $sever  = new FaceCompareServer($this->lang, $this->timezone);
        if (RUNTIME != 'pro') {
            $sever->setCheckLive(false);
            $sever->setCheckImageQuality(false);
            $sever->setIsDebug(env('close_face_compare', 0));
        }
        $timeStamp = $sever->formatCompareData($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $timeStamp]));
    }

}