<?php

namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\MaterialAssetsEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\CompanyTerminationContractModel;
use FlashExpress\bi\App\Models\backyard\MsgAssetModel;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\CancelContractServer;
use FlashExpress\bi\App\Server\CompanyTerminationContractServer;
use FlashExpress\bi\App\Server\MaterialAssetServer;
use FlashExpress\bi\App\Server\ResignServer;
use FlashExpress\bi\App\Server\AssetServer;
use FlashExpress\bi\App\library\enums;
use WebGeeker\Validation\Validation;
use Exception;

class CompanyTerminationContractController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    /**
     * 预览信息
     * @return void
     * @throws BusinessException
     * @throws \ReflectionException
     */
    public function previewInfoAction()
    {
        //[1]入参校验
        $paramIn     = $this->paramIn;
        $validations = [
            'staff_info_id' => 'Required|IntLe:9999999999',
        ];
        $this->validateCheck($this->paramIn, $validations);
        //[2]业务处理
        $server = Tools::reBuildCountryInstance(new CompanyTerminationContractServer($this->lang, $this->timezone),[$this->lang, $this->timezone]);
        $data   = $server->previewInfo($paramIn['staff_info_id'], $this->userinfo['staff_id']);
        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data'=>$data]));
    }

    /**
     * 添加解约申请
     * @throws \ReflectionException
     */
    public function addAction()
    {
        //[1]入参校验
        $paramIn = $this->paramIn;
        $validations = [
            "reason"        => "Required|Int",
            'staff_info_id' => 'Required|IntLe:9999999999',
            "remark"        => "StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('content_limit_len'),
        ];
        if (isCountry('MY') && isset($paramIn['termination_type']) && $paramIn['termination_type'] == CompanyTerminationContractModel::TERMINATION_TYPE_NOT_STAFF) {
            unset($validations['reason']);
        }
        $this->validateCheck($this->paramIn, $validations);
        $paramIn['submitter_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $server = Tools::reBuildCountryInstance(new CompanyTerminationContractServer($this->lang, $this->timezone),[$this->lang, $this->timezone]);
        $server->add($paramIn);
        //[3]数据返回
        $this->jsonReturn(self::checkReturn(1));
    }


    /**
     * 更新离职申请
     */
    public function updateAction()
    {
        //[1]参数定义
        $paramIn               = $this->paramIn;
        $paramIn['staff_id']   = $this->userinfo['staff_id'];
        $paramIn['staff_name'] = $this->userinfo['name'];
        $status                = $paramIn['status'] ?? 0;

        $server = new CompanyTerminationContractServer($this->lang, $this->timezone);

        if (in_array($status, [enums::$audit_status['approved'], enums::$audit_status['dismissed'],])) {
            $validations = [
                "audit_id" => "Required",
                "status"   => "Required|Int",
            ];
            if ($status == enums::$audit_status['dismissed']) { //驳回
                $validations['reject_reason'] = "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1020');
            }
            $this->validateCheck($paramIn, $validations);
            $returnArr = $server->update($paramIn);
            return $this->jsonReturn($returnArr);
        }

        if ($status == enums::$audit_status['revoked']) {
            $model = CompanyTerminationContractModel::findFirstById($paramIn['audit_id']);
            $validations = [
                "audit_id" => "Required",
                "status"   => "Required|Int",
            ];
            if ($model->submitter_id == $paramIn['staff_id']) {
                $validations['cancel_reason'] = "Required|StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('1030');
            } else {
                $validations['cancel_reason'] = "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1030');
            }
            $this->validateCheck($paramIn, $validations);
            $returnArr = $server->cancel($paramIn);
            return $this->jsonReturn($returnArr);
        }
    }
    
    /**
     * 获取下拉列表 
     * 目前仅my在用
     * @return null
     */
    public function dropDownAction()
    {
        $server = new CompanyTerminationContractServer($this->lang, $this->timezone);
        $returnArr['26'] = $server->getHarassmentTargetList();
        $returnArr['19'] = $server->getTheftTypeList();
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }


}
