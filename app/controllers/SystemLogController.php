<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use Exception;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrSystemScreenCaptureLogModel;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\MilesServer;
use FlashExpress\bi\App\Server\SystemLogServer;

class SystemLogController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * 屏幕捕捉日志添加
     * @throws Exception
     */
    public function screen_capture_addAction()
    {
        $paramIn    = $this->paramIn;
        $userInfo   = $this->userinfo;
        $headerData = $this->request->getHeaders();
        $equipment  = $this->processingDefault($headerData, 'X-Fle-Equipment-Type',1);

        $validations = [
            "operation_type"  => "Required|IntIn:" . HrSystemScreenCaptureLogModel::OPERATION_TYPE_CAPTURE . "," . HrSystemScreenCaptureLogModel::OPERATION_TYPE_VIDEO . "|>>>:operation_type " . $this->t->_('miss_args'),
            "operation_time"  => "Required|StrLenGeLe:0,20|>>>:operation_time " . $this->t->_('miss_args'),
            "page_type"       => "Required|IntIn:" . HrSystemScreenCaptureLogModel::PAGE_TYPE_H5 . "," . HrSystemScreenCaptureLogModel::PAGE_TYPE_APP . "|>>>:page_type " . $this->t->_('miss_args'),
            "page_path"       => "Required|StrLenGeLe:0,2000|>>>:page_path " . $this->t->_('miss_args'),
            "page_image_path" => "StrLenGeLe:0,2000|>>>:page_image_path " . $this->t->_('miss_args'),
            "remark"          => "StrLenGeLe:0,16000000|>>>:remark " . $this->t->_('miss_args'),
        ];

        $this->validateCheck($paramIn, $validations);
        $paramIn['equipment'] = $equipment;

        $res = $this->atomicLock(function () use ($paramIn, $userInfo) {
            return (new SystemLogServer($this->lang))->screenCaptureLogSave($paramIn, $userInfo);
        }, 'screen_capture_add_' . $userInfo['staff_id']);

        $this->jsonReturn($res);
    }

    /**
     * 上传客户端图片
     * @info 客户端组件必须按照以前的格式返回，返回值为message，不是msg
     * @info 返回值json里data必须用JSON_UNESCAPED_UNICODE + JSON_UNESCAPED_SLASHES + JSON_PRETTY_PRINT
     */
    public function uploadAction()
    {
        try {
            $filename = $this->request->get('fileName');

            if (empty($filename)) {
                return $this->ajax_fle_return('parameter error need fileName', -1, null);
            }

            if (env('break_away_from_ms')) {
                $fle_rpc = (new ApiClient('hcm_rpc', '', 'buildPutObjectParams', $this->lang));
                $fle_rpc->setParams(['biz_type' => 'SCREEN_CATURE_RECORD', 'filename' => $filename]);
                $return = $fle_rpc->execute();
            } else {
                $fle_rpc = (new ApiClient(env('api_img_upload'), '', 'buildPutObjectParams', $this->lang));
                $fle_rpc->setParam(['SCREEN_CATURE_RECORD', $filename, '']);
                $return = $fle_rpc->execute();
            }

            //文件上传成功,返回datajson格式必须JSON_UNESCAPED_UNICODE+JSON_UNESCAPED_SLASHES+JSON_PRETTY_PRINT
            if (isset($return['result'])) {
                $data['code']    = 1;
                $data['message'] = 'success';
                $data['data']    = $return['result'];
                die(json_encode($data, JSON_UNESCAPED_UNICODE + JSON_UNESCAPED_SLASHES + JSON_PRETTY_PRINT));
            }

            $this->logger->write_log("system_log_upload {$this->userinfo['id']} " . json_encode($return), 'error');

            //文件上传失败
            return $this->ajax_fle_return('upload failed', 0, null);
        } catch (\Exception $e) {
            $this->logger->write_log("system_log_upload {$this->userinfo['id']} " . $e->getMessage());
            return $this->ajax_fle_return('upload failed', 0, null);
        }
    }

    /**
     * 操作日志添加
     * @throws Exception
     */
    public function operationLogAddAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo ?? [];

        $validations = [
            "business_type" => "Required|Int",
        ];
        if (!empty($paramIn['business_data'])) {
            $validations['business_data'] = "Required|Obj";
        }
        $this->validateCheck($paramIn, $validations);

        $data = (new SystemLogServer($this->lang, $this->timezone))->operationLogAdd($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }
}