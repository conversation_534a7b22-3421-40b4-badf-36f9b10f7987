<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\FuelBudgetServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use Exception;

class FuelBudgetController extends Controllers\ControllerBase{


    public $object_list = array('001' => 480,'014' => 694) ;//关联oa 001 差旅费 014 交通费 对应的 480 和 694 都是 油费id
    public $object_temp = array('001' => 2, '014' => 0); //差旅费-油费 模板是2 交通费模板 是0
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        $this->url_log($this->paramIn);

    }


    //进入添加页面 获取基本信息 oa 接口
    public function defaultAction(){


            if(empty($this->paramIn['fuel_no']))
                $this->jsonReturn( $this->checkReturn(-3, 'miss parameter'));
            if(empty($this->paramIn['object_code']))
                $this->jsonReturn( $this->checkReturn(-3, 'miss parameter'));
            $this->paramIn['user_info'] = $this->userinfo;
            $server = new FuelBudgetServer($this->lang,$this->timezone);
            $data = $server->get_default($this->paramIn);

            $setting_model = new BySettingRepository($this->lang);
            $oa_svc = $setting_model->get_setting('oa_svc');

            $method = 'reimbursement_default';
            $param['staff_id'] = $this->userinfo['id'];
            $param['name'] = $this->userinfo['name'];

            $fle_rpc = (new ApiClient($oa_svc,'',$method, $this->lang));
            $fle_rpc->setParams($param);
            $res = $fle_rpc->execute();

            if(!empty($res['result']) && $res['result']['code'] == 1){
                $data['no'] = $res['result']['data']['no'];
            }
            //差旅费-油费 模板是2 交通费模板 是0
            $data['template_type'] = $this->object_temp[$this->paramIn['object_code']];
            if($data['template_type'] == 2){
                //调用 oa  出差列表接口 get_business_no
                $method = 'get_business_no';
                //只查询 by 的报销单
                $param['source_type'] = 2; //1 oa  2 by

                $fle_rpc = (new ApiClient($oa_svc,'',$method, $this->lang));
                $fle_rpc->setParams($param);
                $res_b = $fle_rpc->execute();
                if(!empty($res_b['result']))
                    $data['business_list'] = $res_b['result'];

                $this->logger->write_log("fuel_budget business {$param['staff_id']} " . json_encode($res_b),'info');
            }

            $this->logger->write_log("fuel_budget {$param['staff_id']} " . json_encode($res),'info');
            $this->jsonReturn($this->checkReturn(array('data' => $data)));


    }

    //申请的报销列表
    public function reimbursement_listAction(){

            $setting_model = new BySettingRepository($this->lang);
            $oa_svc = $setting_model->get_setting('oa_svc');
//            $oa_svc = 'http://192.168.25.46:8081/rpc/svc/call';//http://192.168.0.230:8010/rpc/svc/call

            $method = 'reimbursement_list';
            $p['staff_id'] = $this->userinfo['id'];
            $p['name'] = $this->userinfo['name'];
            $p['pageNum'] = $this->paramIn['pageNum'];
            $p['pageSize'] = $this->paramIn['pageSize'];
            //6个月之内的数据
            $p['created_at'] = gmdate('Y-m-d',strtotime("-6 month"));

            $fle_rpc = (new ApiClient($oa_svc,'',$method, $this->lang));
            $fle_rpc->setParams($p);
            $res = $fle_rpc->execute();


            $return['code'] = 1;
            $return['msg'] = '';

            $this->logger->write_log("reimbursement_list {$p['staff_id']} " . json_encode($res),'info');
            if(!empty($res['result']) && $res['result']['code'] == 1){
                $data = null;
                if(!empty($res['result']['data'])){
                    foreach ($res['result']['data']['items'] as $v){
                        //`status` tinyint(1) DEFAULT '1' COMMENT '审核状态-1待审核，2拒绝（驳回），3同意，4撤回',
                        //`pay_status` tinyint(1) DEFAULT '1' COMMENT '支付状态-1待支付，2已支付，3未支付',
                        //展示by的状态 只有 4个 1代审 2 驳回 3 已支付 4 未支付
                        if($v['status'] == 1)
                            $row['state'] = 1;
                        if($v['status'] == 2 || $v['status'] == 4)
                            $row['state'] = 2;
                        if($v['status'] == 3){
                            if($v['pay_status'] == 2)
                                $row['state'] = 3;
                            else{
                                $row['state'] = 4;
                            }
                        }
                        $row['id'] = $v['id'];
                        $row['state'] = empty($row['state']) ? 4 : $row['state'];//没有值都算没支付
                        $row['create_date'] = date('Y-m-d H:i:s',strtotime($v['created_at']));
                        $row['start_date'] = $v['start_at'];
                        $row['end_date'] = $v['end_at'];
                        $row['amount'] = $v['amount'];
                        $data[] = $row;
                    }

                }
                $return['data'] = $data;

            }else{
                $return['code'] = $res['result']['code'] ??  -1;
                $return['msg'] = $res['result']['message'] ?? 'failed';
            }


            $this->jsonReturn($return);

    }


    //查询是否有 油费科目 预算 权限
    public function objectAction(){
        $param['staff_id'] = $this->userinfo['id'];
        $param['name'] = $this->userinfo['name'];
        $param['department_id'] = $this->userinfo['department_id'];
        $param['organization_type'] = $this->userinfo['organization_type'];
        $userInfo          = HrStaffInfoServer::getUserInfoByStaffInfoId($this->userinfo['id']);
        $company_id='';
        $company_name='';
        if(!empty($userInfo->id)){
            $userInfo          = $userInfo->toArray();
            $dep_info = SysDepartmentModel::findFirst($userInfo['sys_department_id']);
            $company_id = $dep_info->company_id;
            $company_name = $dep_info->company_name;
        }


            $setting_model = new BySettingRepository($this->lang);
            $oa_svc = $setting_model->get_setting('oa_svc');
            $method = 'reimbursement_object';
            $fle_rpc = (new ApiClient($oa_svc,'',$method, $this->lang));
            $fle_rpc->setParams($param);
            $res = $fle_rpc->execute();

            $this->logger->write_log("budget_object {$param['department_id']}_{$param['organization_type']} " . json_encode($res),'info');

            //根据语言环境 匹配 对应oa 返回的数据库字段
            $lang_list = array(
                'th' => 'name_th',
                'en' => 'name_en',
                'zh-CN' => 'name_cn',
            );
            $lang_key = empty($lang_list[$this->lang]) ? 'name_cn' : $lang_list[$this->lang];
            $return = array();
            if(!empty($res['result'])){
                $code_list = array_keys($this->object_list);
                foreach ($res['result'] as $v){
                    //只返回 指定科目
                    if(in_array($v['level_code'],$code_list)){
                        $row['object_id'] = $v['id'];
                        $row['level_code'] = $v['level_code'];
                        $row['name'] = $v[$lang_key];
                        $row['company_id'] = $company_id;
                        $row['company_name'] = $company_name;
                        $return[] = $row;
                    }
                }
            }
            $this->jsonReturn($this->checkReturn(array('data' => $return)));


    }

    //根据科目 code 获取对应明细 然后 找出 固定的明细 返回前端
    public function productAction(){
        $param['object_code'] = $this->paramIn['object_code'];
        if(empty($param['object_code']))
            $this->jsonReturn( $this->checkReturn(-3, 'miss parameter'));

        $setting_model = new BySettingRepository($this->lang);
        $oa_svc = $setting_model->get_setting('oa_svc');
        $method = 'reimbursement_object_product';

        $fle_rpc = (new ApiClient($oa_svc,'',$method, $this->lang));
        $fle_rpc->setParams($param);
        $res = $fle_rpc->execute();

        //根据语言环境 匹配 对应oa 返回的数据库字段
        $lang_list = array(
            'th' => 'name_th',
            'en' => 'name_en',
            'zh-CN' => 'name_cn',
        );
        $lang_key = empty($lang_list[$this->lang]) ? 'name_cn' : $lang_list[$this->lang];
        $return = array();
        if(!empty($res['result'])){
            foreach ($res['result'] as $v){
                $product_list = array_values($this->object_list);
                //只返回 指定科目
                if(in_array($v['id'],$product_list)){
                    $row['product_id'] = $v['id'];
                    $row['name'] = $v[$lang_key];
                    $row['budget_product_account_id'] = $v['budget_product_account_id'];
                    $return[] = $row;
                }
            }
        }
        $this->jsonReturn($this->checkReturn(array('data' => $return)));
    }

    //用车申请 剔除已经关联预算的 记录列表
    public function fuel_listAction(){
        $param['staff_id'] = $this->userinfo['id'];
       try{
           $server = new FuelBudgetServer($this->lang,$this->timezone);
           $res = $server->get_fuel_list($param);
           $this->jsonReturn($res);

       }catch (\Exception $e){
           $this->logger->write_log("fuel_budget {$param['staff_id']} " . $e->getMessage());
       }
    }


    public function infoAction(){

            $setting_model = new BySettingRepository($this->lang);
            $oa_svc = $setting_model->get_setting('oa_svc');

            $method = 'reimbursement_detail';
            $param['staff_id'] = $this->userinfo['id'];
            $param['id'] = $this->paramIn['id'];
            if(empty($param['id']))
                $this->jsonReturn( $this->checkReturn(-3, 'miss parameter'));
//            $param['name'] = $this->userinfo['name'];

            $fle_rpc = (new ApiClient($oa_svc,'',$method, $this->lang));
            $fle_rpc->setParams($param);
            $res = $fle_rpc->execute();


            $this->logger->write_log("fuel_budget {$param['staff_id']} " . json_encode($res),'info');
            $info = array();
            if(!empty($res['result']) && $res['result']['code'] == 1){
                $data = $res['result']['data'];
                $server = new FuelBudgetServer($this->lang,$this->timezone);
                $info = $server->format_info($data);
                //审批流信息
                $info['stream'] = $data['auth_logs'];
            }
            $this->jsonReturn($this->checkReturn(array('data' => $info)));

    }

    public function addAction(){

            /**
             * 新增需求
            - 差旅费和交通费均在“发票编号”下新增“不含税金额”和“含税金额”填写项。要求：只能填写数字，保留2位小数
            - 在“发票”下方新增“是否产生过路费”选项框，可以选择“是”或者“否”，默认是“否”。
             */
        set_time_limit(300);

        $data = $this->paramIn;
            $data['user_info'] = $this->userinfo;

            //新增校验逻辑
            $user_tax_amount = round($data['tax_amount'],2);
            $user_no_tax_amount = round($data['no_tax_amount'],2);
            if($user_no_tax_amount > $user_tax_amount)
                return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('fuel_check')));

            if(!empty($data['is_traffic']) && $data['is_traffic'] == 1){
                $traffic = round($data['traffic_tax_amount'],2);
                $traffic_no_tax = round($data['traffic_no_tax_amount'],2);
                if($traffic_no_tax > $traffic)
                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('fuel_check')));
            }

            $userInfo          = HrStaffInfoServer::getUserInfoByStaffInfoId($data['user_info']['staff_id']);
            if(!empty($userInfo->id)){
                $userInfo          = $userInfo->toArray();
                $dep_info = SysDepartmentModel::findFirst($userInfo['sys_department_id']);

                if($dep_info->company_id==1 && empty($data['cost_centre'])){
                    $this->jsonReturn( $this->checkReturn(-3,$this->getTranslation()->_('not_is_center_please_contact_add_staff')));
                }
            }
            $server = new FuelBudgetServer($this->lang,$this->timezone);
            $return = $server->format_data($data,$data['is_submit']);
            if(empty($return))
                $this->jsonReturn( $this->checkReturn(-3, 'add failed'));

            if(is_string($return))
                $this->jsonReturn( $this->checkReturn(-3, $return));

            // 发票编号校验
            $check_result = $server->checkTicketsNo($data);
            if(is_string($check_result))
                $this->jsonReturn( $this->checkReturn(-3, $check_result));

            $p['data'] = $return;
            $p['user_info'] = $this->userinfo;
            //入参日志
            $this->logger->write_log("fuel_budget {$this->userinfo['id']} rpc_param " . json_encode($p) ,'info');

            $setting_model = new BySettingRepository($this->lang);
            $oa_svc = $setting_model->get_setting('oa_svc');
            $method = 'reimbursement_add';
//            $oa_svc = 'http://**************:8081/rpc/svc/call';

            $fle_rpc = (new ApiClient($oa_svc,'',$method, $this->lang));
            $fle_rpc->setParams($p);
            $res = $fle_rpc->execute();


            $this->logger->write_log("fuel_budget {$this->userinfo['id']} rpc_result " . json_encode($res) ,'info');
            if(empty($res))
                $this->jsonReturn(array('code' => -1,'msg' => 'server error','data' => null));

            $res['result']['message'] = strip_tags(stripslashes($res['result']['message']));
            if($data['is_submit'] != 1){
                if(!empty($res['result']) && isset($res['result']['code'])){
                    $r['code'] = $res['result']['code'];
                    $r['msg'] = $res['result']['message'];
                    $r['data'] = $res['result']['data'];
                    if(!empty($res['result']['data']['message']))
                        $r['data']['message'] = strip_tags(stripslashes($res['result']['data']['message']));
                    $this->jsonReturn($r);
                }
            }else{
                if(!empty($res['result']) && isset($res['result']['code'])){
                    $r['code'] = $res['result']['code'];
                    $r['msg'] = $res['result']['message'];

                    //如果 申请没成功 需要 删除关联
                    if($res['result']['code'] != 1){
                        $flag = $server->reject_fuel($this->userinfo['id'],$data['no']);
                        $this->logger->write_log("fuel_budget add failed {$this->userinfo['id']} {$data['no']} {$flag}",'info');
                    }
                    $this->jsonReturn($r);
                }
            }


            $this->jsonReturn($this->checkReturn(1));

    }


    public function testAction(){
        $mpdf = new \FlashExpress\bi\App\Server\MpdfServer($this->lang,$this->timezone);
        $temp_arr['path'] = APP_PATH . '/views';
        $temp_arr['dir'] = 'fuelbudget';
        $temp_arr['name'] = "pdf_1";
        $var_data['param'] = 'xxxxx';
        $a = $mpdf->make_pdf($temp_arr,$var_data);
        $this->jsonReturn($a);
    }


    protected function get_object_text(){

    }

    /**
     * 获取发票抬头列表
     */
    public function getInvoiceHeaderListAction()
    {
        try {
            $params['staff_id'] = $this->userinfo['id'];

            $this->logger->write_log("getInvoiceHeaderList svc request {$params['staff_id']}", 'info');

            $fle_rpc = (new ApiClient('oa_rpc', '', 'get_invoice_header_list', $this->lang));
            $fle_rpc->setParams($params);
            $res = $fle_rpc->execute();

            $data = [];
            if (!empty($res['result']) && $res['result']['code'] == 1) {
                $data = $res['result']['data'];
            }

            $this->jsonReturn($this->checkReturn(array('data' => $data)));
        } catch (Exception $exception) {
            $this->logger->write_log('getInvoiceHeaderList svc error ' . $exception->getMessage(), 'error');
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

}
