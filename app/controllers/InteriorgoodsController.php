<?php
namespace FlashExpress\bi\App\Controllers;

use app\enums\LangEnums;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\ReturnMsgEnums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Server\InteriorGoodsServer;
use Exception;

class InteriorgoodsController extends Controllers\ControllerBase
{
    private $server = null;

    public function initialize()
    {
        parent::initialize();
        $this->server = ['InteriorGoodsServer' => new InteriorGoodsServer($this->lang)];
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     *  所有工作日满7天的员工，第8天开始可以有购买权限。
     */
    public function getStaffBuyAuthAction()
    {
        $loginUser = $this->userinfo;
        $res       = (new InteriorGoodsServer($this->lang))->getStaffBuyAuth($loginUser);
        $data = [
            'data' => [
                'buy_enable' => $res,
            ]
        ];
        $this->jsonReturn(self::checkReturn($data));

    }

    public function getGoodsListAction()
    {
        $paramIn     = $this->paramIn;
        $reqParams   = [
            'page'  => $paramIn['page'] ?? null,
            'limit' => $paramIn['limit'] ?? 10,
        ];
        $validations = [
            "page"  => "Required|Int",
            "limit" => "Required|Int",
        ];
        $this->validateCheck($reqParams, $validations);
        $loginUser = $this->userinfo;
        $res = (new InteriorGoodsServer($this->lang))->getGoodsList($loginUser, $reqParams);

        if (isset($res['msg'])) {
            $this->jsonReturn(self::checkReturn(-3, $res['msg']));
        }
        $data = [
            'data' => $res
        ];
        $this->jsonReturn(self::checkReturn($data));

    }

    public function getGoodsDetailAction()
    {
        $paramIn     = $this->paramIn;
        $reqParams   = [
            'goods_id' => $paramIn['goods_id'] ?? null,
        ];
        $validations = [
            "goods_id" => "Required|Int",
        ];
        $this->validateCheck($reqParams, $validations);

        $loginUser = $this->userinfo;
        $res = (new InteriorGoodsServer($this->lang))->getGoodsDetail($loginUser, $reqParams);
        if (isset($res['msg'])) {
            $this->jsonReturn(self::checkReturn(-3, $res['msg']));
        }
        $data = [
            'data' => [
                'goods_info' => $res
            ]
        ];
        $this->jsonReturn(self::checkReturn($data));
    }

    public function addGoodsSkuToCartAction()
    {
        $paramIn     = $this->paramIn;
        $reqParams   = [
            'goods_id'     => $paramIn['goods_id'] ?? null,
            'goods_sku_id' => $paramIn['goods_sku_id'] ?? null,
            'buy_num'      => $paramIn['buy_num'] ?? null,
            'is_free'      => $paramIn['is_free'] ?? 0,
        ];
        $validations = [
            "goods_id"     => "Required|Int",
            "goods_sku_id" => "Required|Int",
            "buy_num"      => "Required|IntNotIn:0",
        ];
        $this->validateCheck($reqParams, $validations);

        $loginUser = $this->userinfo;

        $res = (new InteriorGoodsServer($this->lang))->addGoodsSkuToCart($loginUser, $reqParams);
        if (isset($res['msg'])) {
            $this->jsonReturn(self::checkReturn(-3, $res['msg']));
        }
        $data = [
            'data' => $res
        ];
        $this->jsonReturn(self::checkReturn($data));
    }

    /**
     * 获取购物车商品列表
     * Created by: Lqz.
     * CreateTime: 2020/8/13 0013 20:14
     */
    public function getCartGoodsListAction()
    {
        $paramIn     = $this->paramIn;
        $reqParams   = [
            'page'  => $paramIn['page'] ?? null,
            'limit' => $paramIn['limit'] ?? 10,
        ];
        $validations = [
            "page"  => "Required|Int",
            "limit" => "Required|Int",
        ];
        $this->validateCheck($reqParams, $validations);
        $loginUser = $this->userinfo;

        $res = (new InteriorGoodsServer($this->lang))->getCartGoodsList($loginUser, $reqParams);
        if (isset($res['msg'])) {
            $this->jsonReturn(self::checkReturn(-3, $res['msg']));
        }
        $data = [
            'data' => $res
        ];
        $this->jsonReturn(self::checkReturn($data));
    }

    /**
     *批量删除出购物车商品
     * Created by: Lqz.
     * CreateTime: 2020/8/7 0007 20:00
     */
    public function deleteCartGoodsAction()
    {
        $paramIn     = $this->paramIn;
        $reqParams   = [
            'cart_ids' => $paramIn['cart_ids'] ?? null,
        ];
        $validations = [
            "cart_ids" => "Required|Arr|ArrLenGe:1|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($reqParams, $validations);

        $loginUser = $this->userinfo;
        $res       = (new InteriorGoodsServer($this->lang))->deleteCartGoods($loginUser, $reqParams);
        if (isset($res['msg'])) {
            $this->jsonReturn(self::checkReturn(-3, $res['msg']));
        }
        $data = [
            'data' => $res
        ];
        $this->jsonReturn(self::checkReturn($data));

    }

    public function getNoSubmitOrderInfoAction()
    {
        $paramIn     = $this->paramIn;
        $goodsIdsArr = $paramIn['buy_goods_ids_arr'] ?? null;
        if (!is_array($goodsIdsArr)) {
            $this->jsonReturn(self::checkReturn(-3, 'Parameter error!'));
        }
        $reqParams   = ['is_pre_sale' => boolval($paramIn['is_pre_sale'] ?? false)];
        $validations = ["is_pre_sale" => "Required|BoolSmart"];
        $this->validateCheck($reqParams, $validations);

        foreach ($goodsIdsArr as $reqItems) {
            $reqParams   = [
                'goods_id'     => $reqItems['goods_id'] ?? null,
                'goods_sku_id' => $reqItems['goods_sku_id'] ?? null,
                'buy_num'      => $reqItems['buy_num'] ?? null,
                'goods_type'   => $reqItems['goods_type'] ?? '',
            ];
            $validations = [
                "goods_id"     => "Required|Int",
                "goods_sku_id" => "Required|Int",
                "buy_num"      => "Required|Int",
                'goods_type'   => 'Required|Int',
            ];
            $this->validateCheck($reqParams, $validations);
        }
        $loginUser = $this->userinfo;
        $res       = (new InteriorGoodsServer($this->lang))->getNoSubmitOrderInfo($loginUser, $paramIn);
        if (isset($res['msg'])) {
            $this->jsonReturn(self::checkReturn(-3, $res['msg']));
        }
        $data = [
            'data' => $res
        ];
        $this->jsonReturn(self::checkReturn($data));
    }

    public function submitOrderAction()
    {
        $paramIn     = $this->paramIn;
        $goodsIdsArr = $paramIn['buy_goods_ids_arr'] ?? null;
        $paramIn['is_has_free'] = $paramIn['is_has_free'] ?? null;

        $reqParams   = [
            'buy_goods_ids_arr' => $goodsIdsArr,
            'is_pre_sale'       => boolval($paramIn['is_pre_sale'] ?? false),
            'csrf_token'        => $paramIn['csrf_token'] ?? null,
            'total_pay_amount'  => $paramIn['total_pay_amount'] ?? null,
            'province_code'     => $paramIn['province_code'] ?? null,
            'city_code'         => $paramIn['city_code'] ?? null,
            'district_code'     => $paramIn['district_code'] ?? null,
            'detail_address'    => $paramIn['detail_address'] ?? null,
            'postal_code'       => $paramIn['postal_code'] ?? null,
            'store_id'          => $paramIn['staff_store_id'] ?? null,
        ];
        $validations = [
            "buy_goods_ids_arr" => "Required|Arr",
            "is_pre_sale"       => "Required|BoolSmart",
            "csrf_token"        => "Required|Str",
            "total_pay_amount"  => "Required",
            "province_code"     => "Required|Str|StrLenGe:1|>>>:" . LangEnums::getTranslation($this->lang, ReturnMsgEnums::INCOMPLETE_SHIPPING_ADDRESS),
            "city_code"         => "Required|Str|StrLenGe:1|>>>:" . LangEnums::getTranslation($this->lang, ReturnMsgEnums::INCOMPLETE_SHIPPING_ADDRESS),
            "district_code"     => "Required|Str|StrLenGe:1|>>>:" . LangEnums::getTranslation($this->lang, ReturnMsgEnums::INCOMPLETE_SHIPPING_ADDRESS),
            "detail_address"    => "Required|Str|StrLenGe:1|>>>:" . LangEnums::getTranslation($this->lang, ReturnMsgEnums::INCOMPLETE_SHIPPING_ADDRESS),
            "postal_code"       => "Required|Str|StrLenGe:1|>>>:" . LangEnums::getTranslation($this->lang, ReturnMsgEnums::INCOMPLETE_SHIPPING_ADDRESS),
            "store_id"          => "Required|Str|StrLenGe:1|>>>:" . LangEnums::getTranslation($this->lang, ReturnMsgEnums::STORE_ID_IS_EMPTY)
        ];
        $this->validateCheck($reqParams, $validations);
        if (!is_array($goodsIdsArr)) {
            $this->jsonReturn(self::checkReturn(-3, 'Parameter error!'));
        }
        foreach ($goodsIdsArr as $reqItems) {
            $reqParams   = [
                'goods_id'     => $reqItems['goods_id'] ?? null,
                'goods_sku_id' => $reqItems['goods_sku_id'] ?? null,
                'buy_num'      => $reqItems['buy_num'] ?? null
            ];
            $validations = [
                "goods_id"     => "Required|Int",
                "goods_sku_id" => "Required|Int",
                "buy_num"      => "Required|Int",
            ];
            $this->validateCheck($reqParams, $validations);
        }
        $loginUser = $this->userinfo;

        $res       = (new InteriorGoodsServer($this->lang))->submitOrder($loginUser, $paramIn);
        if (isset($res['msg'])) {
            $this->jsonReturn(self::checkReturn(-3, $res['msg']));
        }
        $data = [
            'data' => $res
        ];
        $this->jsonReturn(self::checkReturn($data));
    }

    /**
     *获取我的 订单列表
     * Created by: Lqz.
     * CreateTime: 2020/8/10 0010 15:26
     */
    public function getOrderListAction()
    {
        $paramIn     = $this->paramIn;
        $reqParams   = [
            'page'  => $paramIn['page'] ?? null,
            'limit' => $paramIn['limit'] ?? 10,
        ];
        $validations = [
            "page"  => "Required|Int",
            "limit" => "Required|Int",
        ];
        $this->validateCheck($reqParams, $validations);
        $loginUser = $this->userinfo;

        $res = (new InteriorGoodsServer($this->lang))->getOrderList($loginUser, $reqParams);
        if (isset($res['msg'])) {
            $this->jsonReturn(self::checkReturn(-3, $res['msg']));
        }
        $data = [
            'data' => $res
        ];
        $this->jsonReturn(self::checkReturn($data));
    }


    /**
     *获取订单详情
     * Created by: Lqz.
     * CreateTime: 2020/8/10 0010 15:26
     */
    public function getOrderInfoAction()
    {
        $paramIn     = $this->paramIn;
        $reqParams   = [
            'order_code' => $paramIn['order_code'] ?? null,
        ];
        $validations = [
            "order_code" => "Required|Str",
        ];
        $this->validateCheck($reqParams, $validations);
        $loginUser = $this->userinfo;
        $res       = (new InteriorGoodsServer($this->lang))->getOrderInfo($loginUser, $reqParams);
        if (isset($res['msg'])) {
            $this->jsonReturn(self::checkReturn(-3, $res['msg']));
        }
        $data = [
            'data' => $res
        ];
        $this->jsonReturn(self::checkReturn($data));
    }

    /**
     * 获取订单支付状态
     * @api https://yapi.flashexpress.pub/project/93/interface/api/87581
     */
    public function getOrderPayStatusAction()
    {
        $paramIn     = $this->paramIn;
        $reqParams   = [
            'order_code' => $paramIn['order_code'] ?? null,
        ];
        $validations = [
            "order_code" => "Required|Str",
        ];
        $this->validateCheck($reqParams, $validations);
        $loginUser = $this->userinfo;
        $res       = (new InteriorGoodsServer($this->lang))->getOrderStatus($loginUser, $reqParams);
        $data      = ['data' => $res];
        $this->jsonReturn(self::checkReturn($data));
    }

    /**
     * 取消库存
     * Created by: Lqz.
     * CreateTime: 2020/8/10 0010 16:42
     */
    public function cancelOrderAction()
    {
        $paramIn     = $this->paramIn;
        $reqParams   = [
            'order_code' => $paramIn['order_code'] ?? null,
        ];
        $validations = [
            "order_code" => "Required|Str",
        ];
        $this->validateCheck($reqParams, $validations);
        $loginUser = $this->userinfo;
        $res       = (new InteriorGoodsServer($this->lang))->cancelOrder($loginUser, $reqParams);
        if (isset($res['msg'])) {
            $this->jsonReturn(self::checkReturn(-3, $res['msg']));
        }
        $data = [
            'order_info' => $res
        ];
        $this->jsonReturn(self::checkReturn($data));
    }

    /**
     *  收货
     * Created by: Lqz.
     * CreateTime: 2020/8/13 0013 13:50
     */
    public function saveOrderReceviedAction()
    {
        $paramIn     = $this->paramIn;
        $reqParams   = [
            'order_code' => $paramIn['order_code'] ?? null,
        ];
        $validations = [
            "order_code" => "Required|Str",
        ];
        $this->validateCheck($reqParams, $validations);
        $loginUser = $this->userinfo;
        $res       = (new InteriorGoodsServer($this->lang))->setLockConf(3)->saveOrderReceviedUseLock($loginUser, $reqParams);
        if (isset($res['msg'])) {
            $this->jsonReturn(self::checkReturn(-3, $res['msg']));
        }
        $data = [
            'data' => $res
        ];
        $this->jsonReturn(self::checkReturn($data));
    }

    public function getAddressListAction()
    {
        $paramIn     = $this->paramIn;
        $searchStr = $paramIn['search_str'] ?? '';
        if(!$searchStr){
            $this->jsonReturn(self::checkReturn([]));
        }
        $loginUser = $this->userinfo;
        $res       = (new InteriorGoodsServer($this->lang))->getAddressList($loginUser, $paramIn);
        if (!$res) {
            $this->jsonReturn(self::checkReturn(-3,'No data found'));
        }
        $data = [
            'data' => $res
        ];
        $this->jsonReturn(self::checkReturn($data));
    }


    /**
    * 员工商城共用枚举
    * @Date: 7/11/23 11:38 AM
    * @return  array
    **/
    public function getShopEnumsAction()
    {
        $result = (new InteriorGoodsServer($this->lang))->getShopEnums();
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 根据订单 显示订单数据 支付银行
     * @throws ValidationException
     * @return  array
     **/
    public function getOrderByBankAction()
    {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations = [
                'order_code' => 'Required|StrLenLe:20|>>>:order_code error'
            ];
            //验证
            $this->validateCheck($paramIn, $validations);
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $result = (new InteriorGoodsServer($this->lang))->getOrderByBank($paramIn);

            $data = [
                'data' => $result
            ];
            $this->jsonReturn(self::checkReturn($data));
    }


    /**
     * 我的订单 - 查看路由
     * @return  array
     **/
    public function getOutboundTrackingAction()
    {
        $result = (new InteriorGoodsServer($this->lang))->getOutboundTrackingInfo($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 下载扫码付页面图片
     * @api https://yapi.flashexpress.pub/project/93/interface/api/87629
     * @throws Exception
     */
    public function downloadQrcodePayImageAction()
    {
        //参数定义
        $paramIn = $this->paramIn;

        //参数校验
        $validations = [
            'order_code'             => 'Required|StrLenLe:20|>>>:order_code error',
            'order_code_label'       => 'Required|Str|>>>:order_code_label error',
            'total_pay_amount_fmt'   => 'Required|Str|>>>:total_pay_amount_fmt error',
            'total_pay_amount_label' => 'Required|Str|>>>:total_pay_amount_label error',
            'qr_image'               => 'Required|Url|>>>:qr_image error',
        ];

        //验证
        $this->validateCheck($paramIn, $validations);

        $result = (new InteriorGoodsServer($this->lang))->setLockConf(2)->generateQrcodePayImageUseLock($paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

}
