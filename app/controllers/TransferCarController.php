<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Server\FleetServer;
use FlashExpress\bi\App\Server\TransferCarServer;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class TransferCarController extends Controllers\ControllerBase
{

    /**
     * @param $name
     * @param $args
     * @return Response|ResponseInterface
     * @throws BusinessException
     */
    public function __call($name, $args)
    {

        $paramIn                  = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $server                   = new TransferCarServer($this->lang, $this->timezone);
        // 去掉 Action字样
        $method = substr($name, 0, -6);
        $result = $server->sendRequest($method, $paramIn);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 调车-费用申请
     * @return Response|ResponseInterface
     */
    public function applyFeeAction()
    {
        $paramIn = $this->paramIn;

        $this->validateCheck($paramIn, [
            'driver_id_card'           => 'Required|StrLenGeLe:1,500',
            'payment_certificate'      => 'Required|StrLenGeLe:1,500',
            'proof_of_van_dispatching' => 'Required|StrLenGeLe:1,500',
            'id'                   => 'Required|int',
        ]);

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $server                   = new TransferCarServer($this->lang, $this->timezone);
        /**
         * @see TransferCarServer::applyFee()
         */
        $server->applyFeeUseLock($paramIn);
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }
}
