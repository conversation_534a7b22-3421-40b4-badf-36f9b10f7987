<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\InterviewServer;
use FlashExpress\bi\App\Server\ResumeServer;
use FlashExpress\bi\App\Server\StaffServer;
use WebGeeker\Validation\Validation;
use Exception;

class InterviewController extends Controllers\ControllerBase {

    protected $server;
    protected $paramIn;

    public function initialize() {
        parent::initialize();
        $this->paramIn = $this->request->getPost();

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);

    }

    public function onConstruct() {
        parent::onConstruct();
    }

    /**
     * 面试列表
     * @param int $type 0:待反馈列表 1：已反馈列表 2：取消面试列表
     * @param string $keyword 搜索关键字
     * @return json 列表数据
     */
    public function getInterviewListAction() {

        try {
            
            $validations = [
                "page_num" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),
                "type" => "Int|>>>:" . $this->getTranslation()->_('miss_args')
            ];

            //验证
            $this->validateCheck($this->paramIn, $validations);
            
            $params['type'] = $this->processingDefault($this->paramIn, 'type', 2,0);
            $params['keyword'] = $this->processingDefault($this->paramIn, 'keyword', 1);
            $params['page_size'] = $this->processingDefault($this->paramIn, 'page_size', 2,20);
            $params['page_num'] = $this->processingDefault($this->paramIn, 'page_num', 2,1);
            $params['is_pass'] = $this->processingDefault($this->paramIn, 'is_pass', 2,0);
            //当前员工ID
            $params['staff_id'] = $this->userinfo['staff_id'];
            $result = (new InterviewServer($this->lang, $this->timezone))->getList($params);

            return $this->jsonReturn(self::checkReturn(['data' => $result]));
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("getInterviewListAction "
                    . $e->getFile()
                    . " line " . $e->getLine()
                    . " message " . $e->getMessage()
                    . " trace " . $e->getTraceAsString(), "error");
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 面试信息详情
     * @param int $interview_id 面试ID
     * @return json 详情
     */
    public function getInterviewDetailAction() {
        try {
            $params['interview_id'] = $this->processingDefault($this->paramIn, 'interview_id', 1);
            $params['subscribe_id'] = $this->processingDefault($this->paramIn, 'subscribe_id', 1);
            $params['opt_id'] = $this->processingDefault($this->paramIn, 'opt_id', 1);
            $params['src'] = $this->processingDefault($this->paramIn, 'src', 1);

            if (empty($params['interview_id'])) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('miss_args')));
            }

            //获取详情
            $params['staff_id'] = $this->userinfo['staff_id'];
            $result = (new InterviewServer($this->lang, $this->timezone))->getDetail($params);
            return $this->jsonReturn(self::checkReturn(['data' => $result]));
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("interview getInterviewListAction "
                    . $e->getFile()
                    . " line " . $e->getLine()
                    . " message " . $e->getMessage()
                    . " trace " . $e->getTraceAsString(), "error");
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }


    /**
     * 消息通知内容
     */
    public function msgDetailNewAction(){

        try {
            //消息ID
            if (!isset($this->paramIn['msg_id']) or empty($this->paramIn['msg_id'])) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('miss_args')));
            }

            //读取消息
            $server = new BackyardServer($this->lang, $this->timezone);

            $param['msg_id'] = $this->paramIn['msg_id'];
            $param['staff_id'] = $this->userinfo['staff_id'];
            $res = $server->msg_detail($param);

            $data = [];
            if (isset($res['data'])) {
                $msg_detail = $res['data'];
                    //解析参数
                    $preg_text = preg_replace('/^<div style=\'font-size: 40px\'>(.*)<\/div>$/s', '$1', $msg_detail['content']);
                    if($preg_text){
                        $arr = explode('|||', $preg_text);
                        if(is_array($arr)){
                            $params = explode(',', $arr[1]);
                            if(is_array($params)){
                                $data['interview_id'] = $params[0];  //第一个元素为面试ID
                                $data['subscribe_id'] = $params[1];  //面试预约id
                                $data['opt_id'] = $params[2] ?? '';
                                $data['src'] = $params[3] ?? '';


                                //获取详情
                                $resume['interview_id'] = $data['interview_id'] ;
                                $resume['subscribe_id'] = $data['subscribe_id'];
                                $resume['staff_id'] = $this->userinfo['staff_id'];

                                $result = (new InterviewServer($this->lang, $this->timezone))->getDetail($resume);
                                if($result){
                                    if(isset($result['interview_status']) && !empty($result['interview_status'])){
                                        $data['is_evaluation'] = 1;
                                    }elseif(empty($result['interview_status']) and $result['sub_status'] !=1){
                                        $data['is_evaluation'] = 1;
                                    }else{
                                        $data['is_evaluation'] = 0;
                                    }
                                }
                                $data['title'] = $msg_detail['title'];
                                $data['content'] = $arr[0];

                                switch ($msg_detail['category_code']) {
                                    case 0: //安排面试 面试官
                                        $data['type'] = 1; //查看详情
                                        $data['button'] = 1;//显示按钮
                                        break;
                                    case 1:    //取消面试
                                        $data['type'] = 2; //去查看
                                        $data['button'] = 1;
                                        break;
                                    case 2:    //面试反馈提醒 面试官
                                        $data['type'] = 0; //去填写
                                        $data['button'] = 1;
                                        break;
                                    default:
                                        $data['type'] = 0; //去填写
                                        $data['button'] = 0; //不显示按钮
                                }

                                return $this->jsonReturn(array('code' => 1, 'message' => 'success', 'data' => $data));
                            }
                        }
                    }
            }
            return $this->jsonReturn(array('code' => 0, 'message' => 'success', 'data' => $data));
        }catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("msgDetailNewAction "
                . $e->getFile()
                . " line " . $e->getLine()
                . " message " . $e->getMessage()
                . " trace " . $e->getTraceAsString(), "error");
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }

    /**
     * 取消面试
     * @param int $intview_id 面试ID
     * @param int $state default:31 联系不上求职者
     * @param string $cancel_reason default:null
     * @return json Description
     */
    public function cancelAction() {

        try {
            $params['interview_id'] = $this->processingDefault($this->paramIn, 'interview_id', 1);
            $params['subscribe_id'] = $this->processingDefault($this->paramIn, 'subscribe_id', 1);

            $params['state'] = $this->processingDefault($this->paramIn, 'state', 2, 2);
            $params['cancel_reason'] = $this->processingDefault($this->paramIn, 'interview_id', 1);
            $params['cancel_type'] = $this->processingDefault($this->paramIn, 'cancel_type', 1, 1); //1：未联系上求职者
            $params['module_status'] = $this->processingDefault($this->paramIn, 'module_status', 1, 2); //2：不通过
            $params['opt_id'] = $this->processingDefault($this->paramIn, 'opt_id', 1, '');
            $params['interview_back'] = $this->processingDefault($this->paramIn, 'interview_back', 2, 0);

            if (empty($params['interview_id']) || empty($params['subscribe_id'])) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('miss_args')));
            }
            //
            $params['staff_id'] = $this->userinfo['staff_id'];
            $params['current_user_id'] = $this->userinfo['id'];
            //取消面试操作
            $result = (new InterviewServer($this->lang, $this->timezone))->cancle($params);
            if ($result) {
                return $this->jsonReturn(array('code' => 1, 'message' => 'success', 'data' => null));
            } else {
                return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
            }
        } catch (\Exception $e) {

            $this->getDI()->get("logger")->write_log("interview cancelAction "
                    . $e->getFile()
                    . " line " . $e->getLine()
                    . " message " . $e->getMessage()
                    . " trace " . $e->getTraceAsString(), "error");
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 填写面试评价
     * @param int $interview_id 面试ID
     * @param int $conclusion 面试结果
     * @param int $reason 不通过原因
     * @param string $evaluation 面试评价
     * @return json Description
     */
    public function evaluationAction()
    {
        $validations = [
            "interview_id"          => "Int|>>>:" . $this->getTranslation()->_('miss_args'),
            "conclusion"            => "Int|>>>:" . $this->getTranslation()->_('miss_args'),
            "evaluation"            => "StrLenGeLe:10,2000|>>>:" . $this->getTranslation()->_('string_len'),
            "hc_id"                 => "Int|>>>:" . $this->getTranslation()->_('miss_args'),
            "subscribe_id"          => "Int|>>>:" . $this->getTranslation()->_('miss_args'),
            "opt_id"                => "Int|>>>:" . $this->getTranslation()->_('miss_args'),
            'manager_id'            => "IfIntEq:conclusion,1|Required|Int|>>>:" . $this->getTranslation()->_('interview_manager_error'),
            'manager_name'          => "Str|>>>: Manager Name Error",
            "working_day_rest_type" => "IfIntEq:conclusion,1|Required|IntIn:51,52,61,62,91|>>>:" . $this->getTranslation()->_('interview_working_day_rest_type_error'),
        ];

        //验证
        $this->validateCheck($this->paramIn, $validations);
        //设置默认值
        $this->paramIn['reason'] = $this->processingDefault($this->paramIn, 'reason', 1);

        $this->paramIn['staff_id']        = $this->userinfo['staff_id'];
        $this->paramIn['state']           = 1;                         //默认为未沟通
        $this->paramIn['current_user_id'] = $this->paramIn['staff_id'];//默认为未沟通

        $this->paramIn['manager_id']            = $this->processingDefault($this->paramIn, 'manager_id', 2);
        $this->paramIn['manager_name']          = $this->processingDefault($this->paramIn, 'manager_name', 1);
        $this->paramIn['working_day_rest_type'] = $this->processingDefault($this->paramIn, 'working_day_rest_type', 2);


        //面试反馈
        if ($this->paramIn['conclusion'] == 1) { //面试官通过，则进入下一轮
            $this->paramIn['state'] = 2;         //通过进入下一轮
        } else {
            $this->paramIn['state'] = 3; //不通过
        }
        $result = (new InterviewServer($this->lang, $this->timezone))->addEvaluationUseLock($this->paramIn);

        //淘汰简历
        if ($this->paramIn['conclusion'] == 0) {
            $params = $this->paramIn;
            //预约面试详情
            $subscribe_detail = (new InterviewRepository($this->timezone))->getInterviewDetail($this->paramIn);
            if (!$subscribe_detail) {
                throw new \Exception('未找到相关预约面试记录');
            }

            $params['id']        = $subscribe_detail[0]['cvid'];
            $params['hr_log_id'] = $result['data']['hr_log_id'] ?? 0;
            (new ResumeServer())->out($params);
        }
        //淘汰类型
        $list = (new InterviewServer($this->lang, $this->timezone))->getOutInterviewListServer(1);

        //返回面试评价
        $data['conclusion']      = $this->paramIn['conclusion'] == 0 ? $this->getTranslation()->_('6105') : $this->getTranslation()->_('asset_00004');
        $data['reason']          = $this->paramIn['reason'] ? $list[$this->paramIn['reason']] : '';
        $data['evaluation']      = $this->paramIn['evaluation'];
        $data['conclusion_type'] = $this->paramIn['conclusion'];
        return $this->jsonReturn(['code' => 1, 'message' => 'success', 'data' => $data]);
    }


    /**淘汰类型
     * @param int $returnType
     */
    public function getOutInterviewListAction() {

        try {

            $data =  (new InterviewServer($this->lang,$this->timezone))->getOutInterviewListServer();

            return $this->jsonReturn(array('code' => 1, 'message' => 'success', 'data' => $data));

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("getOutInterviewListAction 异常信息:" . $e->getMessage(), 'error');
        }

    }

    
    public function searchStaffManagerAction() {
        //[1]参数定义
        $paramIn = $this->paramIn;
        //[2]数据验证
        $validations = [
            "search_name" => "Required|Str|>>>:Search Name Error",
        ];
        $this->validateCheck($paramIn, $validations);
        $list = (new StaffServer())->searchStaffList($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $list]));
    }

}
