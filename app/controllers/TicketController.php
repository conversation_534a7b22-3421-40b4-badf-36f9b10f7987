<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use Exception;

class TicketController extends Controllers\ControllerBase
{
    protected $paramIn;
    /**
     * @var \FlashExpress\bi\App\Server\TicketServer
     */
    protected $ticketServer;

    public function initialize()
    {
        parent::initialize();
        $this->ticketServer = new \FlashExpress\bi\App\Server\TicketServer($this->lang, $this->timezone,$this->userinfo);
        $this->paramIn     = $this->request->get();
        
        //会带个_url参数
        unset($this->paramIn['_url']);

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        //$this->paramIn = array_filter($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }



    public function getNumsAction()
    {
        try {
            $data = $this->ticketServer->getNums();
            return $this->jsonReturn($data);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getNums 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }


    public function getDefaultAction(){
        try {
            $data = $this->ticketServer->getDefault();
            return $this->jsonReturn($data);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getDefaut 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }


    public function addAction(){
        try {
            $paramIn   = $this->paramIn;

            if(empty($paramIn['mobile'])){
                unset($paramIn['mobile']);
            }

            $validations = [
                "item_type" => "Required|IntGe:0",
                "line_id"=>"Required|StrLenGeLe:1,20",
                "mobile"=>"StrLenGeLe:9,10",
                "info"=>"Required|StrLenGeLe:1,500",
                'pics'=>"StrLenGeLe:0,1000",
                //"device_store_id" => "Required|StrLen:10",    // 设备所在网点 10 位
            ];

            if (!empty($paramIn['anydesk_id'])) {
                $validations['anydesk_id'] = "StrLenGeLe:1,20";
            }

            if (!empty($paramIn['item_code'])) { // 资产编号
                $validations['item_code'] = "StrLenGeLe:1,20|>>>:".sprintf($this->getTranslation()->_('parameter_length'),$this->getTranslation()->_('item_code'),1,20);
            }

            if (!empty($paramIn['device_store_id']) && $paramIn['device_store_id'] != '-1') {
                $validations['device_store_id'] = "Required|StrLen:10";
            }

            if (!empty($paramIn['device_store_id']) && $paramIn['device_store_id'] == '-1') {
                $validations['device_store_id'] = "Required|StrLen:2";
            }

            $this->validateCheck($paramIn, $validations);
            $paramIn = array_only($paramIn,array_keys($validations));
            
            $data = $this->ticketServer->add($paramIn);
            return $this->jsonReturn($data);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("add 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    public function listAction(){
        try {
            $paramIn = $this->paramIn;
            $validations = [
                "status" => "Required|IntIn:1,2,3",
                'page_num'=>"IntGe:1",
                'page_size'=>"IntGe:1"
            ];
            $this->validateCheck($paramIn, $validations);

            $paramIn = array_only($paramIn,array_keys($validations));
            $data = $this->ticketServer->list($paramIn,false,1);
            return $this->jsonReturn($data);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("list 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    public function detailAction(){
        try {
            $paramIn   = $this->paramIn;
            $validations = [
                "id" => "Required|IntGe:1",
            ];
            $this->validateCheck($paramIn, $validations);
            $data = $this->ticketServer->detail($paramIn['id'],false,false);
            return $this->jsonReturn($data);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("detail 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    //我的工单 展示自己的
    public function auditListAction(){
        try {
            $paramIn   = $this->paramIn;
            $validations = [
                "status" => "Required|IntIn:1,2,3",
                'page_num'=>"IntGe:1",
                'page_size'=>"IntGe:1",
                'orz_type' => "IntIn:1,-1",//1 网点 -1 总部
                'store_category' => "Arr",//网点类型 只有选择网点筛选才有
                'item_type' => "Arr",//设备类型
            ];
            $this->validateCheck($paramIn, $validations);
            $paramIn = array_only($paramIn,array_keys($validations));
            $data = $this->ticketServer->list($paramIn,true,1);
            return $this->jsonReturn($data);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("auditList 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    public function auditDetailAction(){
        try {
            $paramIn   = $this->paramIn;
            $validations = [
                "id" => "Required|IntGe:1",
            ];
            $this->validateCheck($paramIn, $validations);
            $data = $this->ticketServer->detail($paramIn['id'],true,false);
            return $this->jsonReturn($data);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("auditDetail 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    public function replyAction(){
        try {
            $paramIn   = $this->paramIn;
            $validations = [
                "id" => "Required|IntGe:1",
                "mark"=>"Required|StrLenGeLe:1,500",
                "is_pic"=>"IntIn:0,1"
            ];
            $this->validateCheck($paramIn, $validations);
            $paramIn = array_only($paramIn,array_keys($validations));
            $data = $this->ticketServer->reply($paramIn,2);
            return $this->jsonReturn($data);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("reply 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    public function closeAction(){
        try {
            $paramIn   = $this->paramIn;
            $validations = [
                "id" => "Required|IntGe:1",
                "mark"=>"Required|StrLenGeLe:1,500"
            ];
            $this->validateCheck($paramIn, $validations);
            $paramIn = array_only($paramIn,array_keys($validations));
            $data = $this->ticketServer->reply($paramIn,3);
            return $this->jsonReturn($data);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("close 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    public function getLogAction(){
        try {
            $paramIn   = $this->paramIn;
            $validations = [
                "id" => "Required|IntGe:1",
                "is_audit"=>"Required|IntIn:0,1"
            ];
            $this->validateCheck($paramIn, $validations);
            $data = $this->ticketServer->getLog($paramIn,$paramIn['is_audit']);
            return $this->jsonReturn($data);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getLog 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    //获取 工单类型 改成查数据库 增加对应每个类型的数量
    public function getItemTypeAction(){
        try {
            $data = $this->ticketServer->getItemType();
            return $this->jsonReturn($data);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("getItemType 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    ################cs ticket #######################

    /**
     * 工单详情
     */
    public function getCsReplyAction()
    {
        try{
            $paramIn   = $this->paramIn;
            $validations = [
                "id" => "Required|IntGe:1",
            ];
            $this->validateCheck($paramIn, $validations);
            $data = $this->ticketServer->getCsReply($paramIn['id']);
            $this->jsonReturn($data);
        }catch(Exception $e){
            $this->getDI()->get('logger')->write_log("getCsListReply 异常信息:" . $e->getMessage(), 'info');
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取工单列表
     * @return void
     */
    public function getCsListAction()
    {
        try{
            $paramIn   = $this->paramIn;
            $paramIn['status'] = $paramIn['status'] ?? 1;
            $validations = [
                "status" => "Required|IntIn:1,3,4",
                'page_num'=>"IntGe:1",
                'page_size'=>"IntGe:1"
            ];
            $this->validateCheck($paramIn, $validations);
            if(!$this->ticketServer->isDM()) throw new Exception ('It is not DM');
            $list = $this->ticketServer->getCsList($paramIn);
            $this->jsonReturn($list);
        }catch(Exception $e){
            $this->getDI()->get('logger')->write_log("getCsLis 异常信息:" . $e->getMessage(), 'info');
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    public function replyCsAction(){
        try{
            $paramIn   = $this->paramIn;
            $validations = [
                "id" => "Required|IntGe:1",
                'content' => "Required|StrLenGeLe:0,5000",
            ];
            $this->validateCheck($paramIn, $validations);
            $data = $this->ticketServer->replyCs($paramIn);
            $this->jsonReturn($data);
        }catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("replyCs 异常信息:" . $e->getMessage(), 'info');
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }


    //工单相关 枚举 目前只有 网点类型
    public function ticket_enumAction(){
        try{
            $store_category = enums::$store_category;
            $data['store_category'] = array();
            foreach ($store_category as $k => $v){
                $row['id'] = $k;
                $row['name'] = $v;

                $data['store_category'][] = $row;
            }
            $this->jsonReturn($this->checkReturn(array('data' => $data)));
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("ticket_enum 异常信息:" . $e->getMessage(), 'info');
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }


}
