<?php

namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\MaterialAssetsEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\MsgAssetModel;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\CancelContractServer;
use FlashExpress\bi\App\Server\MaterialAssetServer;
use FlashExpress\bi\App\Server\ResignServer;
use FlashExpress\bi\App\Server\AssetServer;
use FlashExpress\bi\App\library\enums;
use WebGeeker\Validation\Validation;
use Exception;

class CancelContractController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    /**
     * 添加解约申请
     */
    public function addAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['userinfo']  = $this->userinfo;

        $validations = [
            "last_work_date" => "Required|Date",
            "leave_date"     => "Required|Date",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $server = Tools::reBuildCountryInstance(new CancelContractServer($this->lang, $this->timezone),
            [$this->lang, $this->timezone]);
        $server->addUseLock($paramIn);
        //[3]数据返回
        $this->jsonReturn(self::checkReturn(1));
    }


    /**
     * 预览生成pdf
     */
    public function previewAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "last_work_date" => "Required|Date",
            "leave_date"     => "Required|Date",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $server = Tools::reBuildCountryInstance(new CancelContractServer($this->lang, $this->timezone),
            [$this->lang, $this->timezone]);
        $data   = $server->previewPdf($paramIn);
        //[3]数据返回
        $this->jsonReturn($data);
    }

    /**
     * 更新离职申请
     */
    public function updateAction()
    {
        //[1]参数定义
        $paramIn               = $this->paramIn;
        $paramIn['staff_id']   = $this->userinfo['staff_id'];
        $paramIn['staff_name'] = $this->userinfo['name'];
        $status                = $paramIn['status'] ?? 0;

        $server = new CancelContractServer($this->lang, $this->timezone);

        if (in_array($status, [enums::$audit_status['approved'], enums::$audit_status['dismissed'],])) {
            $validations = [
                "audit_id" => "Required",
                "status"   => "Required|Int",
            ];
            if ($status == enums::$audit_status['dismissed']) { //驳回
                $validations['reject_reason'] = "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1020');
            }
            $this->validateCheck($paramIn, $validations);
            $returnArr = $server->update($paramIn);
            return $this->jsonReturn($returnArr);
        }

        if ($status == enums::$audit_status['revoked']) {
            $detail      = $server->getResignDetail(['resign_id' => $paramIn['audit_id']]);
            $validations = [
                "audit_id" => "Required",
                "status"   => "Required|Int",
            ];
            if ($detail['submitter_id'] == $paramIn['staff_id']) {
                $validations['cancel_reason'] = "Required|StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('1030');
            } else {
                $validations['cancel_reason'] = "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1030');
            }
            $this->validateCheck($paramIn, $validations);
            $returnArr = $server->cancel($paramIn);
            return $this->jsonReturn($returnArr);
        }
    }

    /**
     * 资产列表
     */
    public function assetsMoneyAction()
    {
        $params = $this->request->get();
        $params['staff_id'] = $this->userinfo['staff_id'];
        $new_assets_server = new MaterialAssetServer($this->lang, $this->timezone);
        $result = $new_assets_server->assetsByStaffId($params);
        return $this->jsonReturn($this->checkReturn($result));
    }


}
