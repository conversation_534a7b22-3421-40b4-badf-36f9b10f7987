<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\Server\AuditListServer;

class AuditlistController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 获取审批列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/1042
     * @param int audit_show_type   1-我的申请 2-我的审批
     * @param int audit_state_type  1-待审批/进行中 Tab 2-已审批/已完成 Tab
     * @param int page_num          页数
     * @return string
     */
    public function getListAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['position_category'] = $this->userinfo['position_category'] ?? 0;

        $validations         = [
            "audit_show_type"   => "Required|IntIn:1,2",
            "audit_state_type"  => "Required|IntIn:1,2",
            "page_num"          => "Required|IntGe:1",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $auditListServer = $this->class_factory('AuditListServer', $this->lang, $this->timezone);
        $returnArr       = $auditListServer->auditListV2($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取审批详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/3343
     * @throws InnerException
     */
    public function detailAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations         = [
            'audit_id'          => 'Required|IntGe:0',
            'audit_type'        => 'Required|IntGe:0',
            'audit_show_type'   => 'Required|IntIn:1,2',
            'audit_state_type'  => 'Required|IntIn:1,2',
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new AuditListServer($this->lang, $this->timezone))->getDetailV2($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取审批记录日志
     * @return void
     * @throws InnerException
     */
    public function getAuditLogsAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations         = [
            'audit_id'          => 'Required|IntGe:0',
            'audit_type'        => 'Required|IntGe:0',
            'audit_show_type'   => 'IntIn:1,2',
            'audit_state_type'  => 'IntIn:1,2',
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new AuditListServer($this->lang, $this->timezone))->getAuditLogs($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取抄送列表
     */
    public function getCCListAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['staff_name'] = $this->userinfo['name'];

        $validations         = [
            'page_num'        => 'Required|IntGe:0',
            'page_size'       => 'Required|IntGe:0',
            'audit_read_type' => 'Required|IntGe:0',
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new AuditListServer($this->lang, $this->timezone))->getCCList($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取抄送列表
     */
    public function getCCDetailAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['staff_name'] = $this->userinfo['name'];

        //[2]业务处理
        try {
            $returnArr = (new AuditListServer($this->lang, $this->timezone))->getCCDetail($paramIn);

            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            $this->logger->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString());
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 设置已读
     */
    public function setReadStateAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new AuditListServer($this->lang, $this->timezone))->setCCRead($paramIn['audit_id'], $paramIn['audit_type'], $paramIn['staff_id']);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取审批类型和待审批数量
     */
    public function getAuditTypeListAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new AuditListServer($this->lang, $this->timezone))->getAuditTypesWithWaitCountV2($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * 模糊查询下拉用户数据
     * Created by: Lqz.
     * CreateTime: 2020/8/17 0017 11:52
     */
    public function getLikeStaffsAction()
    {
        $paramIn   = $this->paramIn;
        $searchStr = $paramIn['search_str'] ?? null;
        $res = null;
        if($searchStr){
            $res = (new AuditListServer($this->lang, $this->timezone))->getLikeStaffs($searchStr, true);
            if (isset($res['msg'])) {
                $this->jsonReturn(self::checkReturn(-3, $res['msg']));
            }
        }

        $data = [
            'data' => $res
        ];
        $this->jsonReturn(self::checkReturn($data));
    }

    /**
     * 单独查询申请审批员工审批相关数据
     * 目前只有请假详情 以后可以自己扩展其他审批详情
     */
    public function staffAuditListAction()
    {
        $paramIn             = $this->paramIn;
        $validations         = [
            "staff_info_id" => "Required|Int",
            "audit_type"    => "Required|IntIn:2",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new AuditListServer($this->lang, $this->timezone))->getStaffAuditList($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
}
