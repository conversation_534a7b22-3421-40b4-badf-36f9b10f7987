<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\ReinstatementServer;
use FlashExpress\bi\App\Server\StaffServer;

class ReinstatementController extends ControllerBase
{

    /**
     * 提交复职申请
     * @return void
     */
    public function addRequestAction()
    {
        $params = $this->paramIn;
        $this->validateCheck($params, [
            'reason'             => "Required|Int",
            'reason_explanation' => "Required|StrLenGeLe:10,500",
            'expected_date'      => "Required|Date",
            'attach'             => "ArrLenGeLe:0,5",
            'attach[*]'          => "Url",
        ]);
        $params['staff_info_id'] = $this->userinfo['staff_id'];
        $server = new ReinstatementServer($this->lang,$this->timezone);
        $result = $server->addRequestUseLock($params);
        $this->jsonReturn($this->checkReturn(['data'=> $result]));
    }

    /**
     * 获取复职申请详情
     * @return void
     */
    public function getDetailAction()
    {
        $server = new ReinstatementServer($this->lang,$this->timezone);
        $suspension = $server->getLatestSuspensionLog($this->userinfo['staff_id']);
        $request = $server->getRequestByStaffId($this->userinfo['staff_id'], $suspension['id']??0);
        if (!$request){
            $this->jsonReturn($this->checkReturn([]));
        }
        $appServer = new ApprovalServer($this->lang,$this->timezone);
        $returnData = [];
        $returnData['expected_date'] = $request->expected_date ?? '';
        $returnData['effective_date'] = $request->effective_date ?? '';
        $returnData['state'] = $request->state;
        $returnData['state_txt'] = $this->getTranslation()->t('audit_status.'.$request->state);
        $returnData['audit_logs'] = $appServer->getAuditLogs($request->id, AuditListEnums::APPROVAL_TYPE_REINSTATEMENT, $this->userinfo['staff_id'],'',3);
        $this->jsonReturn($this->checkReturn(['data' => $returnData]));

    }

    /**
     * @return void
     */
    public function checkAction()
    {
        $server = new ReinstatementServer($this->lang,$this->timezone);
        $suspension = $server->getLatestSuspensionLog($this->userinfo['staff_id']);
        $request = $server->getRequestByStaffId($this->userinfo['staff_id'], $suspension['id']??0);
        $returnData =[];
        $returnData['reason'] = $suspension['stop_duty_reason'] ?? '0';
        $returnData['has_apply'] = boolval($request);
        $returnData['staff_info_id'] = $this->userinfo['staff_id'];
        $returnData['staff_name'] = $this->userinfo['name'];
        $staffServer = new StaffServer($this->lang,$this->timezone);
        $staffInfo   = $staffServer->getStaffInfo(['staff_info_id'=>$this->userinfo['staff_id']],'hire_type');
        $returnData['hire_type'] = intval($staffInfo['hire_type']);
        $this->jsonReturn($this->checkReturn(['data' => $returnData]));
    }

    /**
     * 审批操作
     * @return void
     */
    public function auditAction()
    {
        $params = $this->paramIn;

        $validations = [
            "status"   => "Required|IntIn:2,3",
            "audit_id" => "Required|Int",
        ];
        $this->validateCheck($params, $validations);
        if ($params['status'] == enums::APPROVAL_STATUS_REJECTED && (empty($params['reject_reason']))) {
            $this->jsonReturn($this->checkReturn('-1', 'reject_reason'));
        }
        if ($params['status'] == enums::APPROVAL_STATUS_REJECTED) {
            if (mb_strlen($params['reject_reason']) > 500) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1020')));
            }
        }
        $returnArr = (new ReinstatementServer($this->lang, $this->timezone))->auditUseLock($params, $this->userinfo['staff_id']);
        $this->jsonReturn($returnArr);
    }

}