<?php

namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Server\ResumeRecommendServer;
use FlashExpress\bi\App\Server\SysServer;

class ResumerecommendController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 雇佣类型 静态文件
     */
    public function ReserveTypesAction()
    {
        $server = Tools::reBuildCountryInstance(new SysServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
        $this->jsonReturn(self::checkReturn(['data' => $server->getReserveTypeList()]));
    }

    /**
     * 简历数据源配置初始化
     */
    public function getResumeInitAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        //[2]业务处理
        $resumeRecommendServer = Tools::reBuildCountryInstance(new ResumeRecommendServer($this->lang, $this->timezone),
            [$this->lang, $this->timezone]);

        $returnArr = $resumeRecommendServer->getResumeRecommendInit($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 简历提交接口
     */
    public function resumeRecommendSubmitAction()
    {
        //[1]入参
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->resumeRecommendSubmit($paramIn,
            $userinfo);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     *保存简历暂存接口
     */
    public function createResumeScratchAction()
    {
        //[1]入参
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->createResumeScratch($paramIn,
            $userinfo);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 推荐简历列表
     */
    public function getResumeRecommendListAction()
    {
        //[1]入参
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->getResumeRecommendList($paramIn,
            $userinfo);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }


    /**
     * 待提交推荐简历删除
     */
    public function deleteAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        //[2]业务处理
        if (empty($paramIn['recommend_id'])) {
            $this->jsonReturn(['code' => 1, 'data' => [], 'msg' => 'params err not empty']);
        }
        $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->delete($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取简历详情
     */
    public function getResumeInfoAction()
    {
        //[1]入参
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        if (empty($paramIn['recommend_id'])) {
            $this->jsonReturn(['code' => 1, 'data' => [], 'msg' => 'params err not empty']);
        }
        $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->getInfo($paramIn, $userinfo);
        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    public function getJdListAction()
    {
        $paramIn = $this->paramIn;
        $reserve_type = $paramIn['reserve_type'] ?? '';
        $resumeRecommendServer = Tools::reBuildCountryInstance(new ResumeRecommendServer($this->lang, $this->timezone),
            [$this->lang, $this->timezone]);

        $jdList = $resumeRecommendServer->jdList($reserve_type);
        $this->jsonReturn($this->checkReturn(['data' => $jdList]));
    }

    public function searchListAction()
    {
        $paramIn = $this->paramIn;
        $this->validateCheck($paramIn, [
            'job_name' => 'Required',
        ]);
        $resumeRecommendServer = Tools::reBuildCountryInstance(new ResumeRecommendServer($this->lang, $this->timezone),
            [$this->lang, $this->timezone]);

        $reserve_type = $paramIn['reserve_type'] ?? 0;
        $jdList = $resumeRecommendServer->SearchList($paramIn['job_name'],$reserve_type);
        $this->jsonReturn($this->checkReturn(['data' => $jdList]));
    }


    /**
     * 获取hc关联网点
     */
    public function getStoreListAction()
    {
        //[1]入参
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        $resumeRecommendServer = Tools::reBuildCountryInstance(new ResumeRecommendServer($this->lang, $this->timezone),
            [$this->lang, $this->timezone]);
        $returnArr = $resumeRecommendServer->getStoreHc($paramIn, $userinfo);
        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }
}