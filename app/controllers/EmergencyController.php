<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\OvertimeServer;
use FlashExpress\bi\App\Repository\StaffRepository;
use Exception;

class EmergencyController extends Controllers\ControllerBase{
    public function initialize(){
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn)){
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct(){
        parent::onConstruct();
    }

    //事故类型
    public function emergency_typeAction(){

        $server = new BackyardServer($this->lang,$this->timezone);
        $data = $server->get_emergency_type();

        $res = array();
        foreach ($data as $k => $da){
            $row['code'] = $k;
            $row['val'] = $da;
            $res[] = $row;
        }

        return $this->jsonReturn(self::checkReturn(array('data' => $res)));

    }


    //个人 事故损失相关类型
    public function personal_typeAction(){
        $server = new BackyardServer($this->lang,$this->timezone);
        $data = $server->get_personal_type();

        $res = array();
        foreach ($data as $k => $da){
            $row['code'] = $k;
            $row['val'] = $da;
            $res[] = $row;
        }
        return $this->jsonReturn(self::checkReturn(array('data' => $res)));
    }

    //添加紧急时间
    public function addAction(){
        if(empty(trim($this->paramIn['staff_id'])) || empty(trim($this->paramIn['phone'])) || empty(trim($this->paramIn['date_at'])) || empty(trim($this->paramIn['time_at'])) || empty(trim($this->paramIn['type'])))
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));

        $this->paramIn['user_info'] = $this->userinfo;
        $server = new BackyardServer($this->lang,$this->timezone);
        $flag = $server->add_emergency($this->paramIn);

        if($flag == true){
            $post_info = $this->userinfo;
            if(trim($this->paramIn['staff_id']) != $this->userinfo['id']){
                //获取 输入员工号的姓名
                $re = new StaffRepository($this->lang);
                $post_info = $re->getStaffpositionV2(intval($this->paramIn['staff_id']));
            }
            $manager_info = $server->get_emergency_manager($this->paramIn);
            if($manager_info ){
                //获取负责人
                $param['staff_info_id'] = $manager_info['id'];//负责人 工号
                $param['title'] = "เรียนพนักงานรหัส{$post_info['id']} ชื่อ{$post_info['name']}";
                $param['content'] = "พนักงานของคุณใน{ปี/เดือน/วัน}{เวลา/นาที}ทำการแจ้งเหตุฉุกเฉิน<br/>
                                     แจ้งให้ทราบ มีเหตุฉุกเฉินเกิดขึ้น <br/>
                                    กรุณาใส่ใจความปลอดภัยของพนักงานอย่างเร่งด่วน";
                $fle_rpc = (new ApiClient('bi_rpc','','message_to_backyard', $this->lang));
                $fle_rpc->setParams($param);
                $fle_rpc->execute();

            }

        }
        if($flag == true)
            return $this->jsonReturn(self::checkReturn(1));

        if($flag == -2008)
            return $this->jsonReturn(self::checkReturn(-3,'wrong staff id'));
        return $this->jsonReturn(self::checkReturn(-3,$this->getTranslation()->_('miss_args')));

    }


    //页面直接点击呼叫电话 不取表单信息 直接发消息给当前用户 上级
    public function send_managerAction(){
        $this->paramIn['user_info'] = $this->userinfo;
        $this->paramIn['staff_id'] = $this->userinfo['id'];
        $server = new BackyardServer($this->lang,$this->timezone);
        $manager_info = $server->get_emergency_manager($this->paramIn);

        //获取负责人
        if($manager_info){
            $param['staff_info_id'] = $manager_info['id'];//负责人 工号
            $param['title'] = "เรียนพนักงานรหัส{$this->userinfo['id']} ชื่อ{$this->userinfo['name']}";
            $param['content'] = 'พนักงานในความรับผิดชอบของคุณใน{ปี/เดือน/วัน}{เวลา/นาที}ทำการโทรแจ้งเหตุฉุกเฉิน<br/>
                                แจ้งให้ทราบ มีเหตุฉุกเฉินเกิดขึ้น <br/>
                                กรุณาใส่ใจความปลอดภัยของพนักงานอย่างเร่งด่วน<br/>';
            $fle_rpc = (new ApiClient('bi_rpc','','message_to_backyard', $this->lang));
            $fle_rpc->setParams($param);
            $fle_rpc->execute();
        }

        return $this->jsonReturn(self::checkReturn(1));

    }


}