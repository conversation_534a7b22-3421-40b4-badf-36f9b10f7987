<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\MsgAssetModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\AssetServer;
use FlashExpress\bi\App\Server\StaffInsuranceBeneficiaryServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use Exception;
use FlashExpress\bi\App\Models\backyard\HeadquartersAddressModel;

class StaffController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();

    }

    /**
     * 模糊查询
     */
    public function getStaffListAction()
    {
        $paramIn             = $this->paramIn;
        $validations = [
            "staff_id" => "Required|Str",
        ];
        $this->validateCheck($paramIn, $validations);
        try {
            $returnArr = (new StaffRepository())->getstaffList($paramIn);
        } catch (Exception $e) {
            return $this->jsonReturn($this->checkReturn(-3, $e->getMessage()));
        }
        //[3]数据返回
        return $this->jsonReturn($this->checkReturn(array('data' => $returnArr)));
    }


    //获取网点信息 资产添加页面 获取 使用地信息用
    public function get_store_listAction(){
        $list = SysStoreModel::find([
            'columns' =>'id as store_id, name'
        ])->toArray();

        $listOffice = HeadquartersAddressModel::find([
            'columns' =>'id as store_id, office_name as name'
        ]);
        if(empty($listOffice)){
            $data= $list;
        }else{
            $listOfficeArr = $listOffice->toArray();
            $data= array_merge($list,$listOfficeArr);
        }
        return $this->jsonReturn($this->checkReturn(array('data' => $data)));

    }

    public  function test2Action(){
        $staffId=23703;
        $staff_server=new StaffServer();
        $my_info=$staff_server->get_staff($staffId);
        $my_info=$my_info['data'];
        if(isset($my_info['mobile'])){
            $mobile=$my_info['mobile'];
            $mobile=17338124297;
            $content=$this->getSmsContent($my_info);
            $message=$content;
            $return  = curlJsonRpc($this->config->api->api_send_sms, curlJsonRpcStr($mobile, $message));
            //短信发送成功
            if(isset($return['result']))
            {
                $this->wLog('by 给员工自己发送资产提醒短信成功', $return, 'ResignServer');
            }
            //短信发送失败
            $this->wLog('by 给员工自己发送资产提醒短信失败,远端接口出错', $return, 'ResignServer');
        }
    }

    private function getSmsContent($myInfo)
    {
        $name=$myInfo['name'];
        $staff_id=$myInfo['staff_info_id'];
        $resignServer=new \FlashExpress\bi\App\Server\ResignServer($this->lang
            ,$this->timezone);
        $resignDetail=$resignServer->getResignByStaffId($staff_id);
        if(!empty($resignDetail['leave_date'])){
            $leave_date=date('d/m/Y',strtotime($resignDetail['leave_date']));
        }
        $leave_date=$leave_date?? '';
        $assetServer= new AssetServer($this->lang,$this->timezone);
        $assetList=$assetServer->assetsByStaffId($staff_id);
        $assets=array_merge($assetList['public_goods'],$assetList['personal_goods']);
        $amount=0;
        foreach ($assets as $item){
            $amount+=$item['price'];
        }
        $asset_name1=$assets[0]['name']??'';
        $asset_name2=$assets[1]['name']??'';
        $content='「'.$name.'」-「'.$staff_id.'」您好，离职日期：'.$leave_date.'。烦请检查并归还名下资产：1. '.$asset_name1.'，2. '.$asset_name2.'，3. ...，总金额'.$amount.'THB。请在7天之内归还资产至总部，否则将会影响您的工资及奖金。';
        return $content;
    }

    /**
     * 已入职员工DC officer, bike courier, van courier,boat courier 列表
     */
    public function getStaffCourierWorkListAction()
    {
        try {
            $paramIn = $this->paramIn;
            $paramIn['user'] = $this->userinfo;
            $list = (new StaffServer($this->lang, $this->timezone))->getStaffJobTitleInstructorList($paramIn);
            $this->jsonReturn($this->checkReturn(['data' => $list]));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("StaffController:getStaffCourierWorkListAction:" . $e->getMessage());
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 已入职员工DC officer, bike courier, van courier,boat courier 未有辅导员总数
     */
    public function getCourierInstructorCountAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['user']     = $this->userinfo;
        $paramIn['store_id'] = $this->userinfo['organization_id'];
        $staffServer         = Tools::reBuildCountryInstance(new StaffServer($this->lang, $this->timezone),
            [$this->lang, $this->timezone]);
        $count               = $staffServer->getStaffJobTitleInstructorCount($paramIn);
        $result              = ['count' => $count];
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 添加辅导员
     */
    public function addStaffInstructorAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['user'] = $this->userinfo;
        $validations = [
            "staff_info_id" => "Required|int",
            "instructor_id" => "Required|int",
        ];
        $this->validateCheck($paramIn, $validations);
        $logger = $this->getDI()->get('logger');
        try {
            $staffServer         = Tools::reBuildCountryInstance(new StaffServer($this->lang, $this->timezone),
                [$this->lang, $this->timezone]);
            $result = $staffServer->addStaffInstructor($paramIn);
            $this->jsonReturn($result);
        } catch (Exception $e) {
            $logger->write_log("StaffController:addStaffInstructorAction:request:" . json_encode($paramIn) . ":error:" . $e->getMessage());
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 员工到岗确认详情
     */
    public function getStaffEntryDetailAction() {
        $paramIn = $this->paramIn;
        $paramIn['user'] = $this->userinfo;
        $validations = [
            "staff_info_id" => "Required|int",
        ];
        $this->validateCheck($paramIn, $validations);
        $result = (new StaffServer($this->lang, $this->timezone))->getStaffEntryDetail($paramIn);
        return $this->jsonReturn($result);
    }

    /**
     * 获取员工待办
     * @return null
     */
    public function todoListAction()
    {
        $paramIn              = $this->paramIn;
        $paramIn['staffInfo'] = $this->userinfo;
        $result               = (new StaffServer($this->lang, $this->timezone))->getStaffTodoList($paramIn);
        $this->getDI()->get('logger')->write_log(['todoList'=>$result],'info');
        return $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * @return null
     * @throws ValidationException|BusinessException
     */
    public function verifyMobileAction()
    {
        $mobile_rules = 'StrLenGeLe:9,20';
        if(isCountry('PH')){
            $mobile_rules = 'StrLen:11';
        }
        $paramIn     = $this->paramIn;
        $validations = [
            "mobile"      => "Required|{$mobile_rules}|>>>:" . $this->getTranslation()->_('4117'),
            "biz_type"    => "Required|>>>:need biz_type ",
            "verify_code" => "Required|StrLen:6|>>>:need verify_code",
        ];
        $this->validateCheck($this->paramIn, $validations);
        $paramIn['staffInfo'] = $this->userinfo;
        $result               = (new StaffServer($this->lang, $this->timezone))->verifyMobile($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => (object)[],'msg'=>$this->getTranslation()->_('verify_successfully')]));
    }

    /**
     * 编辑保险受益人信息
     * @return void
     * @throws BusinessException
     */
    public function editInsuranceBeneficiaryAction()
    {
        $params = $this->paramIn;
        $params['staff_info'] = $this->userinfo;
        //关系 受益人姓名 受益人证件号 受益人手机号
        $this->validateCheck([
            'beneficiary_name'     => $params['beneficiary_name'] ?? '',
            'beneficiary_identity' => $params['beneficiary_identity'] ?? '',
            'beneficiary_mobile'   => $params['beneficiary_mobile'] ?? '',
            'relation'             => $params['relation'] ?? 0,
        ], [
            'beneficiary_name'     => 'Required|StrLenGeLe:1,50|>>>:beneficiary_name error',
            'beneficiary_identity' => 'Required|StrLenGeLe:1,20|>>>:beneficiary_identity error',
            'beneficiary_mobile'   => 'Required|Int|>>>:beneficiary_mobile error',
            'relation'             => 'Required|IntIn:1,2,3,4,5|>>>:relation error',
        ]);

        $result = (new StaffInsuranceBeneficiaryServer($this->lang, $this->timezone))->setLockConf(10)->editInsuranceBeneficiaryUseLock($params);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 保险受益人详情
     * @return void
     */
    public function insuranceBeneficiaryAction() {
        $params = $this->paramIn;

        $staffInsuranceBeneficiaryServer = Tools::reBuildCountryInstance(new StaffInsuranceBeneficiaryServer($this->lang, $this->timezone),[$this->lang, $this->timezone]);
        $detail = $staffInsuranceBeneficiaryServer->getInsuranceBeneficiaryDetail(['staff_info_id' => $this->userinfo['id']]);
        $this->jsonReturn($this->checkReturn(['data' => $detail]));
    }

    /**
     * 静态资源
     * @return void
     */
    public function insuranceBeneficiarySysInfoAction() {

        $staffInsuranceBeneficiaryServer = Tools::reBuildCountryInstance(new StaffInsuranceBeneficiaryServer($this->lang, $this->timezone),[$this->lang, $this->timezone]);
        $info = $staffInsuranceBeneficiaryServer->sysInfo();
        $this->jsonReturn($this->checkReturn(['data' => $info]));
    }

    /**
     * 获取银行卡信息
     * @return void
     */
    public function getBankInfoAction()
    {
        $service = new StaffServer();
        $info = $service->getBankInfo($this->userinfo['staff_id']);
        $this->jsonReturn($this->checkReturn(['data' => $info]));
    }

    /**
     * MY 个人代理 在作用
     * 保存银行卡信息
     * @return void
     * @throws BusinessException
     */
    public function saveBankInfoAction()
    {
        $params = $this->paramIn;
        //关系 受益人姓名 受益人证件号 受益人手机号
        $this->validateCheck([
//            'bank_no'      => $params['bank_no'] ?? '',
            'bank_no_name' => $params['bank_no_name'] ?? '',
            'bank_type'    => $params['bank_type'] ?? '',
        ], [
//            'bank_no'      => 'Required|StrLenGeLe:5,20|>>>:bank_no error',
            'bank_no_name' => 'Required|StrLenGeLe:1,100|>>>:bank_no_name error',
            'bank_type'    => 'Required|Int|>>>:bank_type error',
        ]);

        $service = new StaffServer($this->lang);
        $info    = $service->saveBankInfoUseLock($this->userinfo['staff_id'], $params);
        $this->jsonReturn($this->checkReturn(['data' => $info]));
    }

    /**
     * 员工基础信息接口
     * @return void
     */
    public function get_staffAction()
    {
        $data = (new StaffServer($this->lang))->getStaffBusinessData(['staff_info_id' => $this->userinfo['id']]);

        $this->jsonReturn([
            'code'    => 1,
            'message' => 'ok',
            'data'    => $data,
        ]);
    }
}

