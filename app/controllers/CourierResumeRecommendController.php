<?php

namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Server\CourierResumeRecommendServer;
use FlashExpress\bi\App\Server\SysServer;

class CourierResumeRecommendController extends ControllerBase
{
    /**
     * 简历提交接口
     */
    public function resumeRecommendSubmitAction()
    {
        //[1]入参
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        $returnArr = (new CourierResumeRecommendServer($this->lang, $this->timezone))->resumeRecommendSubmit($paramIn,
            $userinfo);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取hc关联网点
     */
    public function getStoreListAction()
    {
        //[1]入参
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        $returnArr = (new CourierResumeRecommendServer($this->lang, $this->timezone))->getStoreList($paramIn,$userinfo);
        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    public function internalJdListAction()
    {
        $paramIn = $this->paramIn;
        $jdList = (new CourierResumeRecommendServer($this->lang, $this->timezone))->internalJdList($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $jdList]));
    }

    /**
     * 内推进度
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function progressAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        $data = (new CourierResumeRecommendServer($this->lang, $this->timezone))->progress($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    } 
}