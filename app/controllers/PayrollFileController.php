<?php
/**
 * Author: Bruce
 * Date  : 2025-01-09 21:08
 * Description:
 */

namespace FlashExpress\bi\App\Controllers;

use  FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\PayrollFileServer;


class PayrollFileController extends Controllers\ControllerBase
{
    public function getFileInfoAction()
    {
        $paramIn = $this->paramIn;

        $validations         = [
            "id"     => "Required|Int|>>>:id " . $this->getTranslation()->_('miss_args'),
            "msg_id" => "StrLenGeLe:1,500|>>>:msg_id " . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = (new PayrollFileServer($this->lang, $this->timezone))->getFileInfo($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    public function submitAction()
    {
        $paramIn = $this->paramIn;

        $validations         = [
            "id"       => "Required|Int|>>>:id " . $this->getTranslation()->_('miss_args'),
            "sign_url" => "StrLenGeLe:1,500|>>>:sign_url " . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = (new PayrollFileServer($this->lang, $this->timezone))->submitUseLock($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * PH 个人代理结算消息详情
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function getIcSettlementInfoAction()
    {
        $paramIn = $this->paramIn;

        $validations         = [
            "id"     => "Required|Int|>>>:id " . $this->getTranslation()->_('miss_args'),
            "msg_id" => "StrLenGeLe:1,500|>>>:msg_id " . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];

        $returnArr = (new PayrollFileServer($this->lang, $this->timezone))->getIcSettlementInfo($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * PH 个人代理结算消息,提交签名图片
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function icSettlementSubmitAction()
    {
        $paramIn = $this->paramIn;

        $validations         = [
            "id"     => "Required|Int|>>>:id " . $this->getTranslation()->_('miss_args'),
            "msg_id" => "StrLenGeLe:1,500|>>>:msg_id " . $this->getTranslation()->_('miss_args'),
            "url"    => "StrLenGeLe:1,500|>>>:url " . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];

        $returnArr = (new PayrollFileServer($this->lang, $this->timezone))->icSettlementSubmitUseLock($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * PH 个人代理结算消息列表
     * @return void
     */
    public function icSettlementMessAction()
    {
        $page     = $this->request->get('p', 'trim');
        $pageSize = $this->request->get('s', 'trim');

        if (empty($page)) {
            $page = 1;
        }
        if (empty($pageSize)) {
            $pageSize = 10;
        }
        $params['staff_id']  = $this->userinfo['id'];
        $params['page']      = $page;
        $params['page_size'] = $pageSize;
        $returnArr           = (new PayrollFileServer($this->lang, $this->timezone))->icSettlementMessList($params);
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

}