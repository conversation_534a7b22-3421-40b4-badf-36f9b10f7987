<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\Message\PenaltyWarningMessage;
use FlashExpress\bi\App\Server\Message\UnReceiveParcelMessage;
use FlashExpress\bi\App\Server\MessageCenterServer;
use Exception;
use FlashExpress\bi\App\Server\MessageServer;
use FlashExpress\bi\App\Server\ResignServer;
use FlashExpress\bi\App\Server\VehicleInspectionServer;

class MessageController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();

        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 消息审批处理：驳回/同意
     * @param int   msg_id      MSG ID
     * @param int   state_code  状态Code
     * @param string reason     同意|拒绝原因
     * @param int   is_agree
     * @return  void
     * @throws Exception
     */
    public function auditUpdateAction()
    {
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] = $this->userinfo;

        $this->getDI()->get('logger')->write_log('auditUpdate - request: '.json_encode($paramIn,
                JSON_UNESCAPED_UNICODE), 'info');

        //[2]数据验证
        $validations = [
            "audit_id"      => "Required|IntGt:0|>>>:audit_id param error",
            "status"        => "Required|IntIn:2,3|>>>:status param error",
            "reject_reason" => "Required|StrLenGeLe:0,500|>>>:".$this->getTranslation()->_('4014'),
        ];

        $this->validateCheck($paramIn, $validations);

        $returnArr = (new MessageCenterServer($this->lang, $this->timezone))->setLockConf(5)->updateUseLock($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 获取消息内容
     */
    public function getContentAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;

        //[2]数据验证
        $validations = [
            "id" => "Required|IntGt:0",
        ];
        $this->validateCheck($paramIn, $validations);

        $data = (new MessageCenterServer($this->lang, $this->timezone))->getMessageContent($paramIn['id']);

        $this->getDI()->get('logger')->write_log('getContent - response: ' . json_encode($data, JSON_UNESCAPED_UNICODE),
            'info');

        return $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    public function getInquireTasksListAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log(['getInquireTasksDetail' => $paramIn], 'info');

        //[2]数据验证
        $validations = [
            "page"     => "Required|Int|>>>:page param error",
            "pageSize" => "Required|Int|>>>:pageSize param error",
            "state"    => "Int|>>>:state param error",
        ];
        $this->validateCheck($paramIn, $validations);
        $ac = new ApiClient('ard_api', '', 'CustomerComplaint.getInquiryListByStaffId', $this->lang);
        $ac->setParams([
            'staff_info_id' => $this->userinfo['staff_id'],
            'state'         => $paramIn['state'] ?? 0,
            'page'          => $paramIn['page'],
            'pageSize'      => $paramIn['pageSize'],
        ]);
        $ac_result = $ac->execute();
        if (!empty($ac_result['result']) && $paramIn['page'] == 1 && $paramIn['state'] == 0) {
            (new UnReceiveParcelMessage())->makeReadTopState($this->userinfo['staff_id'],
                $ac_result['result']['data']['total_0']);
        }
        return $this->jsonReturn($this->checkReturn($ac_result['result']??[]));
    }


    public function getInquireTasksDetailAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log(['getInquireTasksDetail'=>$paramIn],'info');

        //[2]数据验证
        $validations = [
            "related_id" => "Required|Str|>>>:related_id param error",
        ];
        $this->validateCheck($paramIn, $validations);
        $ac = new ApiClient('ard_api', '', 'CustomerComplaint.getInquiryInfoById', $this->lang);
        $ac->setParams(['id' => $paramIn['related_id'],'staff_info_id'=>$this->userinfo['staff_id']]);
        $ac_result = $ac->execute();

        if(!empty($ac_result['result']['data']) && $ac_result['result']['data']['is_time_out']){
            (new BackyardServer($this->lang,$this->timezone))->has_read_operation($paramIn['msg_id'],true);
        }

        //todo 过期 改置顶
        return $this->jsonReturn($this->checkReturn($ac_result['result']));
    }

    public function submitInquireTasksAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;
        //[2]数据验证
        $validations = [
            "related_id" => "Required|Str|>>>:related_id param error",
            'apology_evidence'=>"Required|Arr|ArrLenGe:1|>>>:apology_evidence param error",
        ];
        $this->validateCheck($paramIn, $validations);

        $this->getDI()->get('logger')->write_log(['submitInquireTasks'=>$paramIn],'info');

        $ac = new ApiClient('ard_api', '', 'CustomerComplaint.inquiryAck', $this->lang);
        $data = ['id' => $paramIn['related_id'],'staff_info_id'=>$this->userinfo['staff_id'],'apology_evidence'=>$paramIn['apology_evidence']];
        $ac->setParams($data);
        $ac_result = $ac->execute();

        if($ac_result['result']['code'] != 1){
            throw new BusinessException($ac_result['result']['msg']);
        }
        
        (new BackyardServer($this->lang,$this->timezone))->has_read_operation($paramIn['msg_id'],true);
        //todo 改置顶
        return $this->jsonReturn($this->checkReturn(1));
    }

    /**
     * 20031紧急【ALL】已揽收未入仓新增快递员提醒
     * @throws BusinessException
     */
    public function getReceivedNotPlacedWarehouseDataAction()
    {
        //[1]参数定义
        $paramIn =  $this->request->get();
        //[2]数据验证
        $validations = [
            "currentPage" => "Required|Int|>>>:currentPage param error",
            "perPage"     => "Required|Int|>>>:perPage param error",
        ];
        $this->validateCheck($paramIn, $validations);

        $params = [
            'currentPage' => intval($paramIn['currentPage']),
            'perPage'     => intval($paramIn['perPage']),
            'staffInfoId' => $this->userinfo['staff_id'],
        ];
        $api    = new RestClient('pms');
        $res    = $api->execute(RestClient::METHOD_POST, '/svc/parcel/stay/detail',
            $params, ['Accept-Language' => $this->lang]);
        if (!isset($res['code']) || $res['code'] != 1 || empty($res['data'])) {
            throw new BusinessException($this->getTranslation()->_('server_error'));
        }

        return $this->jsonReturn($this->checkReturn($res));
    }

    /**
     * 是否显示签字提醒
     * @api https://yapi.flashexpress.pub/project/93/interface/api/86279
     */
    public function isShowSignRemindAction()
    {
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //签字提醒每日显示一次
        $key = sprintf('%s_%s_%s', RedisEnums::REDIS_IS_SHOW_SIGN_REMINDER, $paramIn['staff_id'], date('Y-m-d'));

        $redis = $this->getDI()->get("redisLib");
        $res   = [
            'is_show_sign_remind' => CommonEnums::IS_SHOW_NO,
        ];
        if ($redis->get($key)) {
            $this->jsonReturn($this->checkReturn(['data' => $res]));
        }
        $redis->set($key, 1, 86400);
        $res = [
            'is_show_sign_remind' => CommonEnums::IS_SHOW_YES,
        ];
        $this->jsonReturn($this->checkReturn(['data' => $res]));
    }

    /**
     * 获取公共的展示页面
     * @return void
     */
    public function getCommonPdfDetailAction()
    {
        $paramIn['staff_id'] = $this->userinfo['id'];
        $paramIn['msg_id']   = $this->request->get('msg_id','trim');

        $server              = new BackyardServer($this->lang, $this->timezone);
        $res                 = $server->msg_detail($paramIn);

        $data =  [
            'staff_info_id' => $res['data']['staff_info_id'] ?? '',
            'pdf_url'       => $res['data']['content'] ?? '',
            'title'         => $res['data']['title'] ?? '',
        ];

        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 人脸黑名单-消息
     * /message/getBlackFaceDetail
     * @return void
     */
    public function getBlackFaceDetailAction()
    {
        $paramIn['staff_info_id'] = $this->userinfo['id'];
        $paramIn['msg_id']        = $this->paramIn['msg_id'];
        $server                   = new BackyardServer($this->lang, $this->timezone);
        $res                      = $server->getMessageCourierDetail($paramIn);
        $data                     = $res['content'] ? json_decode($res['content'], true) : [];
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 20450【TH】虚假类处罚站内信逻辑
     * /message/getPenaltyWarningList
     */
    public function getPenaltyWarningListAction()
    {
        $paramIn['staff_info_id'] = $this->userinfo['id'];
        $paramIn['msg_id']        = $this->paramIn['msg_id'];
        $validations              = [
            "msg_id" => "Required|StrLenGeLe:1,100",
        ];
        $this->validateCheck($paramIn, $validations);
        $server = new PenaltyWarningMessage($this->lang, $this->timezone);
        $data   = $server->getDataFromContent($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 20450【TH】虚假类处罚站内信逻辑
     * /message/getPenaltyWarningList
     */
    public function getPenaltyWarningDetailAction()
    {
        $paramIn['staff_info_id'] = $this->userinfo['id'];
        $paramIn['msg_id']        = $this->paramIn['msg_id'];
        $paramIn['biz_type']      = $this->paramIn['biz_type'];
        $paramIn['biz_value']     = $this->paramIn['biz_value'];
        $paramIn['false_type']    = $this->paramIn['false_type'];
        $validations              = [
            "msg_id" => "Required|StrLenGeLe:1,100",
        ];
        $this->validateCheck($paramIn, $validations);
        $server = new PenaltyWarningMessage($this->lang, $this->timezone);
        $data   = $server->getDetailFromQuery($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 车辆稽查提交状态
     * @return void
     */
    public function getVehicleInspectionInfoAction()
    {
        $paramIn['msg_id']        = $this->paramIn['msg_id'];
        $paramIn['id']            = $this->paramIn['id'];
        $validations              = [
            "msg_id" => "Required|StrLenGeLe:1,100",
            "id"     => "Required|StrLenGe:1",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['id'];
        $server                   = new VehicleInspectionServer($this->lang, $this->timezone);
        $status                   = $server->getStatusNew($paramIn);
        $data                     = ['submit_status' => $status];
        $data['is_alert_update']  = !$server->enableMobileVersion();
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 获取离职问卷状态
     * @return void
     */
    public function resignQuestionnaireStateAction()
    {
        $paramIn['msg_id'] = $this->paramIn['msg_id'];
        $validations       = [
            "msg_id" => "Required|StrLenGeLe:1,100",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['id'];
        $server              = new ResignServer($this->lang, $this->timezone);
        $questionnaireDetail = $server->getStaffLeaveQuestionnaireDetail($paramIn);
        $data['state']       = $questionnaireDetail['state'];
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

}
