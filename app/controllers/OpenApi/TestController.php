<?php

namespace FlashExpress\bi\App\Controllers\OpenApi;

use FlashExpress\bi\App\Repositories\AccessTokenRepository;
use GuzzleHttp\Psr7\ServerRequest;
use League\OAuth2\Server\AuthorizationValidators\BearerTokenValidator;
use League\OAuth2\Server\CryptKey;
use League\OAuth2\Server\Exception\OAuthServerException;

/**
 * Class AuthController
 * @package App\Controllers
 */
class TestController extends BaseController
{

    public $tokenId;
//    public function initialize(){
//        $this->paramIn = $this->request->getPost();
//    }

    public function testAction()
    {
        $this->jsonReturn($this->checkReturn(['code'=>1,'data'=>$this->paramIn]));
    }

}