<?php

namespace FlashExpress\bi\App\Controllers\OpenApi;

use FlashExpress\bi\App\Server\AuditListServer;

/**
 * 短信提醒
 */
class MessageController extends BaseController
{
    public $tokenId;
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 获取待审批数量
     */
    public function getPendingCountAction()
    {
        try {
            set_time_limit(0);

            $data = $this->paramIn;
            $server = new AuditListServer($this->lang,$this->timezone);
            $data = $server->getPendingInfo();
            $this->jsonReturn($this->checkReturn(['code' => 1,'data'=> $data]));
        } catch (\Exception $e){
            $this->logger->write_log("[api:message][method:getPendingCount] ==> failure :".$e->getMessage());
            $this->jsonReturn($this->checkReturn(-3,'获取待审批数失败'));
        }
    }

    /**
     * 测试
     */
    public function pingAction()
    {
        $this->jsonReturn($this->checkReturn(['code' => 1, 'msg' => 'success','data' => "pong"]));
    }
}