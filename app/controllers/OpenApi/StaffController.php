<?php

namespace FlashExpress\bi\App\Controllers\OpenApi;

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\StaffWorkDetectFaceRecordModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkFaceVerifyRecordModel;
use FlashExpress\bi\App\Server\AttendanceDetectCheatServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffFaceBlacklistServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\StaffWorkFaceVerifyRecordServer;
use WebGeeker\Validation\Validation;

class StaffController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        $this->dealLangPack();

    }

    /**
     * 给FlashLink返回员工列表
     * @return void
     * @throws \Exception
     */
    public function listAction()
    {
        $page_num = $this->paramIn['page_num'];
        Validation::validate(['page_num' => $page_num], ['page_num' => 'Required|Int']);
        $departmentServer = new StaffServer();
        $data             = $departmentServer->getListForFlashLink($page_num);
        $this->jsonReturn($this->checkReturn(['code' => 1, 'data' => $data]));
    }

    /**
     * 给FlashLink提供验证密码
     * @return void
     * @throws \Exception
     */
    public function valid_passwordAction()
    {
        $staff_id = $this->paramIn['login'];
        $password = $this->paramIn['password'];
        $this->validateCheck(['password' => $password, 'staff_id' => $staff_id],
            ['staff_id' => 'Required|Int', 'password' => "Required|StrLenGeLe:3,15|>>>:Password format error."]);
        $departmentServer = new StaffServer();
        $bool             = $departmentServer->validatePassword($staff_id, $password);
        $this->jsonReturn($this->checkReturn(['code' => 1, 'data' => ['is_valid' => $bool]]));
    }




    public function getMsgUrlAction()
    {
        try{
            $staffId = $this->paramIn['staff_id'];
            Validation::validate(['staff_id' => $staffId], ['staff_id' => 'Required|Int']);

            $staffServer = new StaffServer();
            $data =
            $staffServer->downLoadUrl($staffId);
            $this->jsonReturn($this->checkReturn(['data' => $data]));
        } catch (ValidationException $exception) {

            $this->getDI()->get('logger')->write_log("getMsgUrlAction  E_msg " . $exception->getMessage()
                . " E_file " . $exception->getFile()
                . " E_line " . $exception->getLine(), 'info');

            throw $exception;
        }
    }

    /**
     * @description 获取(正式员工 + 个人代理)数据csv
     * csv表头 国家Code ｜ 员工工号 ｜ 网点名称 ｜ 网点ID ｜ 大区名称 ｜ 大区ID
     * For: AI团队更新员工信息库
     */
    public function getCourierInfoAction()
    {
        $settingEnvServer = new SettingEnvServer();
        $downLoadInfo = $settingEnvServer->getSetVal('download_courier_info');

        $jsonInfo = json_decode($downLoadInfo, true);

        $this->jsonReturn($this->checkReturn(['data' => $jsonInfo]));
    }

    /**
     * @description 获取正式员工数据csv
     * csv表头 国家Code ｜ 员工工号 ｜ 网点名称 ｜ 网点ID ｜ 大区名称 ｜ 大区ID
     * For: AI团队更新员工信息库
     */
    public function getFullTimeCourierInfoAction()
    {
        $settingEnvServer = new SettingEnvServer();
        $downLoadInfo = $settingEnvServer->getSetVal('download_formal_courier_info');

        $jsonInfo = json_decode($downLoadInfo, true);

        $this->jsonReturn($this->checkReturn(['data' => $jsonInfo]));
    }

    /**
     * @description 获取员工数据打卡图片csv
     * csv表头 国家Code ｜ 员工工号 ｜ 打卡图片url
     * For: AI团队更新员工打卡图片信息库
     */
    public function getCourierAttendanceInfoAction()
    {
        $settingEnvServer = new SettingEnvServer();
        $downLoadInfo = $settingEnvServer->getSetVal('download_courier_attendance_info');

        $jsonInfo = json_decode($downLoadInfo, true);

        $this->jsonReturn($this->checkReturn(['data' => $jsonInfo]));
    }

    /**
     * @description 获取员工数据打卡图片csv
     * csv表头 国家Code ｜ 员工工号 ｜ 打卡图片url
     * For: AI团队更新员工打卡图片信息库
     */
    public function getAttachmentTransferInfoAction()
    {
        $settingEnvServer = new SettingEnvServer();
        $downLoadInfo = $settingEnvServer->getSetVal('download_courier_attachment_transfer');

        $jsonInfo = json_decode($downLoadInfo, true);

        $this->jsonReturn($this->checkReturn(['data' => $jsonInfo]));
    }

    /**
     * 人脸黑名单csv文件
     * @return void
     */
    public function getFaceBlacklistAction()
    {
        $settingEnvServer = new SettingEnvServer();
        $downLoadInfo = $settingEnvServer->getSetVal('face_blacklist_file');

        $jsonInfo = json_decode($downLoadInfo, true);

        $this->jsonReturn($this->checkReturn(['data' => $jsonInfo]));
    }

    /**
     * 人脸黑名单数据
     * @return void
     */
    public function blackFaceImageAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            'page' => 'IntGe:1|>>>:页码必须大于等于1',
            'size' => 'IntGe:1|IntLe:500|>>>:每页条数必须在1-500之间',
        ];
        $this->validateCheck($paramIn, $validations);

        $data = (new StaffFaceBlacklistServer($this->lang, $this->timezone))->getFaceNegativesData($this->paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }


    /**
     * 员工活体检测
     * @return void
     */
    public function getFaceLivingRecordListAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            'page' => 'IntGe:1|>>>:页码必须大于等于1',
            'size' => 'IntGe:1|IntLe:500|>>>:每页条数必须在1-500之间',
        ];
        $this->validateCheck($paramIn, $validations);

        $server = new StaffWorkFaceVerifyRecordServer($this->lang, $this->timezone);
        $paramIn['success_enabled'] = 5;
        $paramIn['verify_channel'] = StaffWorkFaceVerifyRecordModel::VERIFY_CHANNEL_SILENT_LIVENESS;
        $result = $server->getFaceVerifyRecordData($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 2小时定时活体检测
     * @return void
     */
    public function getCycleFaceLivingRecordAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            'page' => 'IntGe:1|>>>:页码必须大于等于1',
            'size' => 'IntGe:1|IntLe:500|>>>:每页条数必须在1-500之间',
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['type'] = [StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS,StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_AGENT_CYCLE];
        $paramIn['check_live_score'] = StaffWorkDetectFaceRecordModel::NOT_LIVING_SCORE;
        $data = (new AttendanceDetectCheatServer($this->lang,
            $this->timezone))->getDetectFaceRecordData($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 2小时人脸比对数据
     * @return void
     */
    public function getCycleFaceCheckRecordAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            'page' => 'IntGe:1|>>>:页码必须大于等于1',
            'size' => 'IntGe:1|IntLe:500|>>>:每页条数必须在1-500之间',
        ];
        $this->validateCheck($paramIn, $validations);

        $data = (new AttendanceDetectCheatServer($this->lang,
            $this->timezone))->getCycleFaceCheckRecord($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

}

