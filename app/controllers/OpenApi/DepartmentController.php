<?php

namespace FlashExpress\bi\App\Controllers\OpenApi;

use FlashExpress\bi\App\Server\SysDepartmentServer;
use WebGeeker\Validation\Validation;

class DepartmentController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 给FlashLink提供部门数据
     * @return void
     * @throws \Exception
     */
    public function listAction()
    {
        $page_num = $this->paramIn['page_num'];
        Validation::validate(['page_num' => $page_num], ['page_num' => 'Required|Int']);
        $departmentServer = new SysDepartmentServer();
        $data             = $departmentServer->getListForFlashLink($page_num);
        $this->jsonReturn($this->checkReturn(['code' => 1, 'data' => $data]));
    }
}

