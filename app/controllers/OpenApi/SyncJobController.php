<?php

namespace FlashExpress\bi\App\Controllers\OpenApi;

use FlashExpress\bi\App\Server\XxljobTaskServer;

class SyncJobController extends BaseController{

    public $tokenId;
    public function initialize(){
        parent::initialize();
        $this->paramIn = filter_param($this->paramIn);
    }
   
    public function xxljobtaskAction()
    {
        try{
            $this->logger->write_log(json_encode($this->paramIn));
            if(empty($this->paramIn['data']))
                $this->jsonReturn($this->checkReturn(-3,'少参数 data => 1,2,3'));
            
            $data = $this->paramIn;
            $data = (new XxljobTaskServer())->getList($data);
            $this->jsonReturn($this->checkReturn(['code'=>1,'data'=>$data]));
        }catch (\Exception $e){
            $this->logger->write_log("sync 同步 xxljobtas 失败 ".$e->getMessage());
            $this->jsonReturn($this->checkReturn(-3,'sync failed'));
        }

    }

    public function xxljobstaffAction()
    {
        try{
            $this->logger->write_log(json_encode($this->paramIn));
            if(empty($this->paramIn['data']))
                $this->jsonReturn($this->checkReturn(-3,'少参数 data => 1,2,3'));
            
            $data = $this->paramIn;
            $data = (new XxljobTaskServer())->getUserList($data);
            $this->jsonReturn($this->checkReturn(['code'=>1,'data'=>$data]));
        }catch (\Exception $e){
            $this->logger->write_log("sync 同步 xxljobtas 失败 ".$e->getMessage());
            $this->jsonReturn($this->checkReturn(-3,'sync failed'));
        }
    }

}