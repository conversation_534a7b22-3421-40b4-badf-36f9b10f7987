<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/1/7
 * Time: 2:31 PM
 */


namespace FlashExpress\bi\App\Controllers\OpenApi;

use FlashExpress\bi\App\Server\SyncServer;

class SyncController extends BaseController{

    public $tokenId;
    public function initialize(){
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }
    //同步 考勤数据 数据表 staff_work_attendance

    /**
    `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
    `organization_id` varchar(10) DEFAULT NULL COMMENT '所属组织机构id',
    `organization_type` tinyint(3) unsigned DEFAULT NULL COMMENT '组织类别 1 store 2 department',
    `attendance_date` date DEFAULT NULL COMMENT '考勤日期',
    `shift_start` varchar(32) DEFAULT NULL COMMENT '班次开始时间',
    `shift_end` varchar(32) DEFAULT NULL COMMENT '班次结束时间',
    `working_day` tinyint(3) unsigned DEFAULT NULL COMMENT '是否是工作日 0:否；1:是',
    `started_at` datetime(3) DEFAULT NULL COMMENT '上班打卡时间',
    `started_state` tinyint(3) unsigned DEFAULT NULL COMMENT '上班打卡状态 1正常打卡 2外勤打卡 3补卡 4 请假回写（废弃没用）5工具或系统任务补卡 6 出差打卡回写',
    `started_staff_lat` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置的纬度',
    `started_staff_lng` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置经度',
    `started_store_id` varchar(10) DEFAULT NULL COMMENT '上班打卡网点编号',
    `started_store_lng` decimal(11,8) DEFAULT NULL COMMENT '上班打卡网点经度',
    `started_store_lat` decimal(11,8) DEFAULT NULL COMMENT '上班打卡网点纬度',
    `started_clientid` varchar(60) DEFAULT NULL COMMENT '上班打卡客户端 ID',
    `started_equipment_type` varchar(60) DEFAULT NULL COMMENT '上班打卡服务端 seed',
    `started_os` varchar(10) DEFAULT NULL COMMENT '上班打卡客户端系统',
    `started_path` varchar(100) DEFAULT NULL COMMENT '上班考勤照片',
    `started_bucket` varchar(63) DEFAULT NULL COMMENT '上班考勤照片所属bucket',
    `started_remark` varchar(500) DEFAULT NULL COMMENT '上班打卡备注信息',
    `end_at` datetime(3) DEFAULT NULL COMMENT '下班打卡时间',
    `end_state` tinyint(3) unsigned DEFAULT NULL COMMENT '下班打卡状态',
    `end_staff_lat` decimal(11,8) DEFAULT NULL COMMENT '下班打卡位置的纬度',
    `end_staff_lng` decimal(11,8) DEFAULT NULL COMMENT '下班打卡位置的经度',
    `end_store_id` varchar(10) DEFAULT NULL COMMENT '下班打卡网点编号',
    `end_store_lng` decimal(11,8) DEFAULT NULL COMMENT '下班打卡网点经度',
    `end_store_lat` decimal(11,8) DEFAULT NULL COMMENT '下班打卡网点纬度',
    `end_clientid` varchar(60) DEFAULT NULL COMMENT '下班打卡客户端 ID',
    `end_equipment_type` varchar(60) DEFAULT NULL COMMENT '下班打卡服务端 seed',
    `end_os` varchar(10) DEFAULT NULL COMMENT '下班打卡客户端系统',
    `end_path` varchar(100) DEFAULT NULL COMMENT '下班考勤照片',
    `end_bucket` varchar(63) DEFAULT NULL COMMENT '下班考勤照片所属bucket',
    `end_remark` varchar(500) DEFAULT NULL COMMENT '下班打卡备注信息',

     */
    public function attendanceAction()
    {
        try{
            set_time_limit(0);
            if(empty($this->paramIn['data']))
                $this->jsonReturn($this->checkReturn(-3,'少参数 data'));
            if(empty($this->paramIn['attendance_date']))
                $this->jsonReturn($this->checkReturn(-3,'少参数 attendance_date'));
            $data = $this->paramIn;
            $server = new SyncServer($this->lang,$this->timezone);
            $flag = $server->save_data($server::ATTENDANCE,$data);
            $this->jsonReturn($this->checkReturn(['code'=>1,'data'=>$flag]));
        }catch (\Exception $e){
            $this->logger->write_log("sync 同步 att 失败 ".$e->getMessage());
            $this->jsonReturn($this->checkReturn(-3,'sync failed'));
        }

    }

    //请假数据 staff_audit  staff_audit_leave_split

    /**
    `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
    `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
    `leave_type` int(10) DEFAULT '1' COMMENT '请假类型',
    `leave_start_time` datetime DEFAULT NULL COMMENT '请假开始时间',
    `leave_start_type` tinyint(1) DEFAULT '1' COMMENT '请假开始时间类型 1上午 2下午',
    `leave_end_time` datetime DEFAULT NULL COMMENT '请假结束时间',
    `leave_end_type` tinyint(1) DEFAULT '1' COMMENT '请假结束时间类型 1上午 2下午',
    `leave_day` decimal(10,1) DEFAULT NULL COMMENT '请假天数',
    `audit_reason` text COMMENT '申请原因',
    `reject_reason` text COMMENT '驳回原因',
     */
    public function leaveAction()
    {
        try{
            set_time_limit(0);
            $data = $this->paramIn;
            $server = new SyncServer($this->lang,$this->timezone);
            $flag = $server->save_data($server::LEAVE,$data);
            $this->jsonReturn($this->checkReturn(['code'=>1,'data'=>$flag]));
        }catch (\Exception $e){
            $this->logger->write_log("sync 同步 leave 失败 ".$e->getMessage());
            $this->jsonReturn($this->checkReturn(-3,'sync failed'));
        }

    }

    //同步出差数据 数据表 涉及 business_trip business_trip_img
    public function businessAction()
    {
        try{
            set_time_limit(0);
            $data = $this->paramIn;
            $server = new SyncServer($this->lang,$this->timezone);
            $flag = $server->save_data($server::BUSINESS,$data);
            $this->jsonReturn($this->checkReturn(['code'=>1,'data'=>$flag]));
        }catch (\Exception $e){
            $this->logger->write_log("sync 同步 att business ".$e->getMessage());
            $this->jsonReturn($this->checkReturn(-3,'sync failed'));
        }


    }

    //加班数据 hr_overtime
    public function over_timeAction()
    {
        try{
            set_time_limit(0);
            $data = $this->paramIn;
            $server = new SyncServer($this->lang,$this->timezone);
            $flag = $server->save_data($server::OVERTIME,$data);
            $this->jsonReturn($this->checkReturn(['code'=>1,'data'=>$flag]));
        }catch (\Exception $e){
            $this->logger->write_log("sync 同步 overtime 失败 ".$e->getMessage());
            $this->jsonReturn($this->checkReturn(-3,'sync failed'));
        }

    }

    // 请假状态同步
    public function leave_statusAction()
    {
        try {
            $data = $this->paramIn;
            $server = new SyncServer($this->lang, $this->timezone);
            $flag = $server->sync_status($data, SyncServer::LEAVE);
            $this->jsonReturn($this->checkReturn(['code'=>1,'data'=>$flag]));
        } catch (\Exception $e) {

            $this->logger->write_log("sync 同步 leave 失败 Error_msg: ".$e->getMessage() . ' Error_line: ' . $e->getLine() , ' Error_file: ' . $e->getFile());
            $this->jsonReturn($this->checkReturn(-3,'sync failed'));
        }

    }

    // 出差状态同步
    public function business_statusAction()
    {
        try {
            $data = $this->paramIn;
            $server = new SyncServer($this->lang, $this->timezone);
            $flag = $server->sync_status($data, SyncServer::BUSINESS);
            $this->jsonReturn($this->checkReturn(['code'=>1,'data'=>$flag]));
        } catch (\Exception $e) {

            $this->logger->write_log("sync 同步 leave 失败 Error_msg: ".$e->getMessage() . ' Error_line: ' . $e->getLine() , ' Error_file: ' . $e->getFile());
            $this->jsonReturn($this->checkReturn(-3,'sync failed'));
        }

    }

    // overtime状态同步
    public function overtime_statusAction()
    {
        try {
            $data = $this->paramIn;
            $server = new SyncServer($this->lang, $this->timezone);
            $flag = $server->sync_status($data, SyncServer::OVERTIME);
            $this->jsonReturn($this->checkReturn(['code'=>1,'data'=>$flag]));
        } catch (\Exception $e) {

            $this->logger->write_log("sync 同步 leave 失败 Error_msg: ".$e->getMessage() . ' Error_line: ' . $e->getLine() , ' Error_file: ' . $e->getFile());
            $this->jsonReturn($this->checkReturn(-3,'sync failed'));
        }

    }


}