<?php

namespace FlashExpress\bi\App\Controllers\OpenApi;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Repositories\AccessTokenRepository;
use GuzzleHttp\Psr7\ServerRequest;
use League\OAuth2\Server\AuthorizationValidators\BearerTokenValidator;
use League\OAuth2\Server\CryptKey;
use League\OAuth2\Server\Exception\OAuthServerException;
use think\Exception;

/**
 * Class AuthController
 * @package App\Controllers
 */
class BaseController extends Controllers\ControllerBase
{

    public $tokenId;
    public $paramIn;
    public function initialize(){
        if(function_exists('molten_get_traceid')){
            $traceid = molten_get_traceid();
        }
        header('traceid:' . ($traceid ?? ''));
        $this->paramIn = $this->request->getPost();
         if(empty($this->paramIn)){
             $this->paramIn = json_decode($this->request->getRawBody(), true);
             $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
         }
         $this->checkToken();
         $this->dealLangPack();
    }

    private function checkToken()
    {
        try {
            $serverRequest = ServerRequest::fromGlobals();
            $request = $this->resourceServer->validateAuthenticatedRequest($serverRequest);
            //$uid = $request->getAttribute('oauth_user_id');//发放token 写入的值
            $tokenId = $request->getAttribute('oauth_access_token_id');
            //获取token相关信息，并且放入Phalcon的request中
            //$this->request->uid = $uid;
            $this->request->tokenId = $tokenId;
        }catch(OAuthServerException $e){
            $this->jsonReturn($this->checkReturn(['data'=>[],'msg'=>$e->getHint(),'code'=>0]));
        }catch(\Exception $e){
            $this->jsonReturn($this->checkReturn(['data'=>[],'msg'=>$e->getMessage(),'code'=>0]));
        }
    }



}