<?php

namespace FlashExpress\bi\App\Controllers\OpenApi;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Repositories\AccessTokenRepository;
use GuzzleHttp\Psr7\LazyOpenStream;
use GuzzleHttp\Psr7\ServerRequest;
use League\OAuth2\Server\AuthorizationValidators\BearerTokenValidator;
use League\OAuth2\Server\CryptKey;
use League\OAuth2\Server\Exception\OAuthServerException;

/**
 * Class AuthController
 * @package App\Controllers
 */
class AuthController extends Controllers\ControllerBase
{

    public function initialize(){
        $this->paramIn = $this->request->getPost();
         if(empty($this->paramIn)){
             $this->paramIn = json_decode($this->request->getRawBody(), true);
             $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
         }
        if(function_exists('molten_get_traceid')){
            $traceid = molten_get_traceid();
        }
        header('traceid:' . ($traceid ?? ''));
        $this->paramIn = filter_param($this->paramIn);

    }
    /**
     * Get authorization code
     * @return mixed
     */
    public function authorizeAction()
    {
        try {
            $request = ServerRequest::fromGlobals();
            $serverResponse = new \GuzzleHttp\Psr7\Response();
            // this is where the client gets validated
            //(e.g how facebook validates/verifies the Spotify Web client)
            $authRequest = $this->oauth2Server->validateAuthorizationRequest($request);

            // Once the user has logged in set the user on the AuthorizationRequest
            $authRequest->setUser(new \FlashExpress\bi\App\Models\backyard\UserModel()); // an instance of UserEntityInterface

            // (true = approved, false = denied)
            $authRequest->setAuthorizationApproved(true);
            // Return the HTTP redirect response
            $response =  $this->oauth2Server->completeAuthorizationRequest($authRequest, $serverResponse);

            $redirectUrl = $response->getHeaders()['Location'][0];
            //this redirect url should contain the the authorization code and the optional state parameter
            //redirect to this url and request for a token with the authorization code using the token endpoint

            return $this->response->redirect($redirectUrl);
        } catch (OAuthServerException $exception) {
            return $this->sendResponseFromException($exception);
        } catch (\Exception $exception) {
            return $this->sendResponseFromException($exception);
        }
    }

    /**
     * Get access token
     * @return mixed
     */
    public function tokenAction()
    {
        try {
            $request = ServerRequest::fromGlobals();
            $request = $request->withParsedBody([
                "client_id" => $this->paramIn['client_id'] ?? '',
                "client_secret" => $this->paramIn['client_secret'] ?? '',
                "grant_type" => $this->paramIn['grant_type'] ?? '',
            ]);
            $serverResponse = new \GuzzleHttp\Psr7\Response();
            $response = $this->oauth2Server->respondToAccessTokenRequest($request, $serverResponse);
            $this->jsonReturn(json_decode($response->getBody(),true));
        } catch (OAuthServerException $exception) {
            return $this->sendResponseFromException($exception);
        } catch (\Exception $exception) {
            return $this->sendResponseFromException($exception);
        }
    }

    /**
     * Validate access token
     * @return mixed
     */
    public function validateAction()
    {
        try {
            $accessTokenRepository = new AccessTokenRepository();
            $bearerTokenValidator = new BearerTokenValidator($accessTokenRepository);
            $bearerTokenValidator->setPublicKey(new CryptKey(getenv('PUBLIC_KEY_PATH'), null, false));
            $bearerTokenValidator->validateAuthorization(ServerRequest::fromGlobals());

            return $this->response->sendSuccess([]);
        } catch (OAuthServerException $exception) {
            return $this->sendResponseFromException($exception);
        } catch (\Exception $exception) {
            return $this->sendResponseFromException($exception);
        }
    }

    /**
     * @param \Exception $exception
     * @return mixed
     */
    private function sendResponseFromException(\Exception $exception)
    {
       if ($exception instanceof OAuthServerException) {
           $payload = $exception->getPayload();
           return $this->response->sendError($payload['error'], $exception->getHttpStatusCode(), $payload['message']);
       }

    }
}