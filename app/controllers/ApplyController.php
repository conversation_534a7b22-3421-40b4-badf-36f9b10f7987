<?php
namespace FlashExpress\bi\App\Controllers;


use  FlashExpress\bi\App\Controllers;
use  FlashExpress\bi\App\Server\AuditServer;
use  FlashExpress\bi\App\Server\OvertimeServer;
use  FlashExpress\bi\App\Repository\OvertimeRepository;
use  FlashExpress\bi\App\Repository\AuditRepository;
use  FlashExpress\bi\App\Repository;


//临时需求 从此入口进入申请的（请假、OT、LH、晚班费）
//限制时间区间 必须在 3月16号  -  4月20号
//需求链接 https://shimo.im/docs/05n2DfSP3XM60BCV/read 联系人 陈璐 玉莹
class ApplyController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->server  = [
            'audit' => new AuditServer($this->lang,$this->timezone),
            'overtime' => new OvertimeServer($this->lang,$this->timezone,$this->userinfo),

        ];
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        //$this->paramIn = filter_param($this->paramIn);

    }

    public function onConstruct()
    {
        parent::onConstruct();

    }


    //添加申请
    public function addApplyAction()
    {
        return false;
        //[1]入参校验
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $category = intval($paramIn['category']);
        $version = intval($paramIn['version']);
        $returnArr = [
            'code' => isset($code) ? $code : 0,
            'msg'  => isset($msg) ? $msg : '',
            'data' => isset($data) ? $data : []
        ];


        //category                   1- 请假  2-OT 3- LH  4- 补卡          ----------------


        //请假 1 和补卡 4
        if($version == 1){
            //请假
            if ($category == 1) {
                $validations = [
                    "staff_id" => "Required|Int",
                    "category" => "Required|Int",
                    "leave_type" => "Required|IntGtLt:0,16",
                    "leave_start_time" => "Required|Date|>>>:" . $this->getTranslation()->_('1023'),
                    'leave_start_type' => "Required|IntIn:1,2",
                    'leave_end_type' => "Required|IntIn:1,2",
                    "leave_end_time" => "Required|Date|>>>:" . $this->getTranslation()->_('1024'),
                    "audit_reason" => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1019')
                ];
                $this->validateCheck($paramIn, $validations);

                //[2]业务处理
                $returnArr = $this->server['audit']->leaveAddApply($paramIn);
            }

            //补卡
            if($category == 4){
                try{
                    $staff_id = $this->userinfo['staff_id'];
                    $date_at = $paramIn['date_at'];
                    $start_time = $paramIn['start_time'];
                    $end_time = $paramIn['end_time'];

                    if(empty($date_at) || empty($start_time) || empty($end_time))
                        return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
                    if(strtotime($start_time) >= strtotime($end_time))
                        return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('5101')));
                    //超过24小时不行
                    if((strtotime($end_time) - strtotime($start_time))/3600 >= 24)
                        return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('overtime_24')));

                    //需求时间期间为2019年4月16日到4月24日
                    $start_date =  '2019-04-16';
                    $end_date = '2019-04-24';
                    if($date_at <$start_date || $date_at > $end_date)
                        return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('5200')));


                    //格式化时间  前端传来没有秒
                    $start_time = date('Y-m-d H:i:s',strtotime($paramIn['start_time']));
                    $end_time = date('Y-m-d H:i:s',strtotime($paramIn['end_time']));

                    $att_model = new Repository\AttendanceRepository($this->lang, $this->timezone);
                    $att_info = $att_model->getDateInfo($staff_id,$date_at);


                    //如果已经有半个考勤记录 这半个不能修改 考勤表存的是 微秒
                    if(!empty($att_info['started_at']))
                        $start_time = date('Y-m-d H:i:s',strtotime($att_info['started_at']));
                    if(!empty($att_info['end_at']))
                        $end_time = date('Y-m-d H:i:s',strtotime($att_info['end_at']));


                    //补申请 已提交 补卡申请 staff_audit 如果补卡记录 存在 也不让修改已有补卡记录时间
                    $audit_model = new AuditRepository();
                    $audit_exist = $audit_model->getAttendanceAudit($staff_id,$date_at);
                    if(!empty($audit_exist)){
                        foreach($audit_exist as $v){
                            $key = $v['attendance_date'].'_'.$v['attendance_type'];
                            $audit_arr[$key] = $v['reissue_card_date'];
                        }
                        //把补卡记录的时间 写上 不让修改
                        if(empty($att_info['started_at']))
                            $start_time = empty($audit_arr[$date_at.'_1']) ? '' : $audit_arr[$date_at.'_1'];

                        if(empty($att_info['end_at']))
                            $start_time = empty($audit_arr[$date_at.'_2']) ? '' : $audit_arr[$date_at.'_2'];
                    }

                    //是否已经提交补申请 staff_apply
                    $apply_model = new Repository\ApplyRepository();
                    $sign_exist = $apply_model->checkSign($staff_id, $date_at);
                    if($sign_exist > 0)
                        return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('5202')));


                    $insert['staff_info_id'] = $staff_id;
                    $insert['apply_category'] = 4;//1- 请假  2-OT 3- LH  4- 补卡
                    $insert['apply_type'] = 0;
                    $insert['start_time'] = $start_time;
                    $insert['end_time'] = $end_time;
                    $insert['date_at'] = $date_at;

                    $flag = $apply_model->applyInsert($insert);
                    if($flag){
                        $returnArr = self::checkReturn([]);
                    }else{
                        return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('2109')));
                    }
                }catch (\Exception $e){
                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
                }
            }
        }
        /**
         * 根据version参数判断 1- 补卡和请假  2- OT LH
         */
        if($version == 2) {
            //   加班 OT  和  晚班
            if($category == 2){
                $paramIn['staff_id'] = $this->userinfo['staff_id'];
                $validations         = [
                    "staff_id"           => "Required|Int",
                    "reason" => "Required|StrLenGeLe:1,1000|>>>:" . $this->getTranslation()->_('5110'),

                ];
                $this->validateCheck($paramIn, $validations);
                $returnArr = $this->server['overtime']->addOvertimeApply_v2($paramIn);

            }

            //3- LH
            if ($category == 3) {
                //[1]入参校验
                $validations = [
                    "staff_id" => "Required|Int",
                    "lh_date" => "Required|Date|>>>:" . $this->getTranslation()->_('2101'),
                    "lh_time" => "Required|StrLenGeLe:1,100|>>>:" . $this->getTranslation()->_('2102'),
                    "lh_plate_number" => "Required|StrLenGeLe:1,100|>>>:" . $this->getTranslation()->_('2103')
                ];
                $this->validateCheck($paramIn, $validations);

                //[2]特殊校验 校验时间
                $time = isDatetime($paramIn['lh_time']);
                if (!$time) {
                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('2104')));
                }
                $time = checkIsBetweenTime($paramIn['lh_time']);
                if (!$time) {
                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('2110')));
                }

                //[4]校验图片
                $imagePathArr = $paramIn['image_path'];
                if (empty($imagePathArr)) {
                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('2106')));
                }

                //[6]业务处理
                $returnArr = $this->server['audit']->lhMoneyAddApply($paramIn);
            }
        }
        //只有lh  新版
        if($version == 3){
            //3- LH
            if ($category == 3) {
                //[1]入参校验
                $validations = [
                    "staff_id" => "Required|Int",
                    "lh_date" => "Required|Date|>>>:" . $this->getTranslation()->_('2101'),
                    "lh_time" => "Required|StrLenGeLe:1,100|>>>:" . $this->getTranslation()->_('2102'),
                    "lh_plate_number" => "Required|StrLenGeLe:1,100|>>>:" . $this->getTranslation()->_('2103')
                ];
                $this->validateCheck($paramIn, $validations);

                //[2]特殊校验 校验时间
                $time = isDatetime($paramIn['lh_time']);
                if (!$time) {
                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('2104')));
                }
//                $time = checkIsBetweenTime($paramIn['lh_time']);
//                if (!$time) {
//                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('2110')));
//                }

                //[4]校验图片
                $imagePathArr = $paramIn['image_path'];
                if (empty($imagePathArr)) {
                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('2106')));
                }

                //[6]业务处理
                $returnArr = $this->server['audit']->lhMoneyAddApply($paramIn);
            }
        }


        //[3]数据返回
        $this->jsonReturn($returnArr);

    }


    //点击页面日期 申请 四种类型 时候 接口 获取该天是否存在记录
    public function checkApplyAction(){
        return false;
        //[1]入参校验
        $paramIn = $this->paramIn;
        if(empty($paramIn['date']))
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));

        if(empty($this->userinfo['staff_id']))
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('1001')));


        $start_date = '2019-04-16';
        $end_date = '2019-04-24';
        if(!empty($paramIn['version']) && $paramIn['version'] == 2){
            $start_date = '2019-04-16';//需求规定开始时间
            $end_date = '2019-05-10';
        }

        if($paramIn['date'] < $start_date || $paramIn['date'] > $end_date)
            return $this->checkReturn(-3, $this->getTranslation()->_('5200'));
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        try{
            $model = new Repository\ApplyRepository();
            $data = $model->checkDate($this->userinfo['staff_id'],$paramIn['date']);
            return $this->jsonReturn(self::checkReturn(array('data' => $data)));
        }catch (\Exception $e){

            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }

    }

    //进入页面显示日历 每天已有申请记录 颜色标点 固定时间段2019-03-16,2019-04-20
    public function getApplyListAction(){
        return false;
        //[1]入参校验
        $paramIn = $this->paramIn;
        if(empty($this->userinfo['staff_id']))
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('1001')));
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $version = intval($paramIn['version']);
        if(empty($version))
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));

        //处理下返回数据格式
        $start_date = $key = '2019-04-16';//需求规定开始时间
        $end_date = '2019-04-24';

        if($version == 2){
            $start_date = $key = '2019-04-16';//需求规定开始时间
            $end_date = '2019-05-10';
        }

        if($version == 3){//新版补申请 只能申请lh
            $start_date = $key = '2019-05-16';
            $end_date = '2019-06-15';
        }


        //构建规定日期区间
        $date_array  = array();
        while ($key <= $end_date){
            $date_array[] = $key;
            $key = date("Y-m-d",strtotime("+1 day",strtotime($key)));
        }

        try{
            $model = new Repository\ApplyRepository();
            $data = $model->getList($this->userinfo['staff_id'],$version);

            $exist = $exist_array =  $format = array();
            if($version == 1){//补卡请假 补申请  分类 1 4
                if(!empty($data)){
                    foreach($data as $v){
                        $apply_sign[$v['date_at']]['start_time'] = $v['start_time'];
                        $apply_sign[$v['date_at']]['end_time'] = $v['end_time'];
                    }

                    //格式化补申请数据
                    foreach ($date_array as $k => $date){
                        foreach($data as $v){
                            if($v['apply_category'] == 1){//请假补申请判断区间
                                if($date >= $v['start_date'] && $date <= $v['end_date']){
                                    $format[$date]['data'][$v['apply_id']] = $v['apply_category'];
                                }
                            }else if($v['apply_category'] == 4){//补卡补申请判断日期
                                if($date == $v['date_at']){
                                    $format[$date]['data'][$v['apply_id']] = $v['apply_category'];
                                }
                            }

                        }
                    }
                }


                //获取真实请假
                $audit_model = new AuditRepository();
                $leave_data = $audit_model->getAllLeaveList($this->userinfo['staff_id'], $start_date,$end_date);
                //格式化真实请假数据
                if(!empty($leave_data)){
                    foreach ($date_array as $k => $date){
                        foreach ($leave_data as $v){
                            if($date >= $v['start_date'] && $date <= $v['end_date']){
                                $format[$date]['leave_exist'] = 1;
                            }
                        }
                    }
                }
                //真实打卡记录
                $sign_model = new Repository\AttendanceRepository($this->lang, $this->timezone);
                $sign_data = $sign_model->getSignInfo($this->userinfo['staff_id'], $start_date, $end_date);
                //真实补卡记录
                $back_up_list = $audit_model->getAllAttendanceList($this->userinfo['staff_id'],$start_date,$end_date);
                $back_arr = array();
                if(!empty($back_up_list)){
                    foreach ($back_up_list as $v){
                        $k = $v['attendance_date'].'_'.$v['attendance_type'];
                        $back_arr[$k] = $v['reissue_card_date'];
                    }
                }



                if(!empty($sign_data)){
                    //获取没打卡记录 但是已申请补卡 把补卡时间塞进去
                    foreach ($sign_data as &$v){
                        if(empty($v['started_at'])){//获取上班补卡
                            $k = $v['date_at'].'_1';
                            $v['started_at'] = empty($back_arr[$k]) ? '' : $back_arr[$k];
                        }
                        if(empty($v['end_at'])){//获取下班补卡
                            $k = $v['date_at'].'_2';
                            $v['end_at'] = empty($back_arr[$k]) ? '' : $back_arr[$k];
                        }
                    }
                    //整理 format  返回数据用
                    foreach ($sign_data as $m){
                        foreach ($date_array as $date){
                            if($m['date_at'] == $date){
                                $format[$date]['start_time'] = empty($m['started_at']) ? '' : $m['started_at'];
                                $format[$date]['end_time'] = empty($m['end_at']) ? '' : $m['end_at'];
                                if(!empty($m['started_at']) && !empty($m['end_at'])){
                                    $format[$date]['sign_exist'] = 1;
                                }
                            }
                        }
                    }
                }
                //整理 补申请记录 时间  显示到日历
                if(!empty($apply_sign)){
                    //整理 format  返回数据用
                    foreach ($apply_sign as $d => $a){
                        foreach ($date_array as $date){
                            if($d == $date){
                                $format[$date]['start_time'] = empty($a['start_time']) ? '' : $a['start_time'];
                                $format[$date]['end_time'] = empty($a['end_time']) ? '' : $a['end_time'];
                            }
                        }
                    }
                }

            }

            if($version == 2){//lh 3  ot 2
                if(!empty($data)){
                    $exist = array_column($data,'start_date');
                    foreach ($data as $v){
                        $exist_array[$v['start_date']] = $v;
                    }
                }

                //格式化补申请数据
                $format = array();
                if(!empty($data)){
                    foreach ($date_array as $k => $date){
                        if(in_array($date,$exist)){
                            $format[$date]['data'][$exist_array[$date]['apply_id']] = $exist_array[$date]['apply_category'];
                        }
                    }
                }

                //获取真实ot  lh 如果当日已有“待审核”和“已同意”状态的数据，要显示在日历上。不显示修改按钮，不可修改，也不可以再次提交
                $overtime_model = new OvertimeRepository($this->timezone);
                $overtime_data = $overtime_model->getActOvertime($this->userinfo['staff_id'],$start_date,$end_date);
                $overtime_data = array_column($overtime_data,'date_at');


                $lh_model = new AuditRepository();
                $lh_data = $lh_model->getActLh($this->userinfo['staff_id'],$start_date,$end_date);
                $lh_data = array_column($lh_data,'date_at');
                //格式化真实overtime数据
                if(!empty($overtime_data)){
                    foreach ($date_array as $k => $date){
                        if(in_array($date,$overtime_data)){
                            $format[$date]['ot_exist'] = 1;
                        }
                    }
                }
                //格式化lh数据
                if(!empty($lh_data)){
                    foreach ($date_array as $k => $date){
                        if(in_array($date,$lh_data)){
                            $format[$date]['lh_exist'] = 1;
                        }
                    }
                }
            }
            //版本三 只有 lh
            if($version == 3){
                if(!empty($data)){
                    $exist = array_column($data,'date_at');
                    foreach ($data as $v){
                        $exist_array[$v['date_at']] = $v;
                    }
                }

                //格式化补申请数据
                $format = array();
                if(!empty($data)){
                    foreach ($date_array as $k => $date){
                        if(in_array($date,$exist)){
                            $format[$date]['data'][$exist_array[$date]['apply_id']] = $exist_array[$date]['apply_category'];
                        }
                    }
                }


                $lh_model = new AuditRepository();
                $lh_data = $lh_model->getActLh($this->userinfo['staff_id'],$start_date,$end_date);
                $lh_data = array_column($lh_data,'date_at');

                //格式化lh数据
                if(!empty($lh_data)){
                    foreach ($date_array as $k => $date){
                        if(in_array($date,$lh_data)){
                            $format[$date]['lh_exist'] = 1;
                        }
                    }
                }
            }


            //构建返回前端数据
            $return = null;
            foreach($date_array as $date){
                $row['date'] = $date;
                $row['type'] = array();

                if($version == 1){
                    $row['sign_exist'] = $row['leave_exist'] = 0;
                    $row['start_time'] = $row['end_time'] = '';
                }
                if($version == 2){
                    $row['ot_exist'] = $row['lh_exist'] = 0;
                }
                if($version == 3){
                    $row['lh_exist'] = 0;
                }

                $row['id'] = $row['leave_id'] =  $row['sign_id']  = '';
                if(!empty($format[$date]) && isset($format[$date])){
                    if($version == 1){
                        //是否存在 真实 请假限制前端不可点击日历
                        if(isset($format[$date]['leave_exist']))
                            $row['leave_exist'] = 1;
                        if(isset($format[$date]['sign_exist']))
                            $row['sign_exist'] = 1;

                        //拼接打卡时间
                        if(isset($format[$date]['start_time'])){
                            $row['start_time'] = $format[$date]['start_time'];
                        }
                        if(isset($format[$date]['end_time'])){
                            $row['end_time'] = $format[$date]['end_time'];
                        }
                        if(!empty($format[$date]['data'])){
                            //请假和补卡 可以共存 不能用ot和lh的方式 返回主键
                            foreach ($format[$date]['data'] as $k => $v){
                                if($v == 1)
                                    $row['leave_id'] = intval($k);
                                if($v == 4)
                                    $row['sign_id'] = intval($k);
                            }
                        }
                    }
                    if($version == 2){
                        //是否存在 真实申请 限制前端不可点击日历
                        if(isset($format[$date]['ot_exist']))
                            $row['ot_exist'] = 1;
                        if(isset($format[$date]['lh_exist']))
                            $row['lh_exist'] = 1;
                        if(!empty($format[$date]['data']))//lh ot 互斥 只能有一个id
                            $row['id'] = array_keys($format[$date]['data'])[0];
                    }
                    if($version == 3){
                        //是否存在 真实申请 限制前端不可点击日历
                        if(isset($format[$date]['lh_exist']))
                            $row['lh_exist'] = 1;
                        if(!empty($format[$date]['data']))//lh ot 互斥 只能有一个id
                            $row['id'] = array_keys($format[$date]['data'])[0];
                    }

                    $row['type'] = empty($format[$date]['data']) ? [] :  array_unique(array_values($format[$date]['data']));
                }
                $return[] = $row;
            }
            return $this->jsonReturn(self::checkReturn(array('data' => $return)));
        }catch (\Exception $e){
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

    //获取补申请详情  只针对 第二版 补申请
    public function getApplyInfoAction(){
        return false;
        $paramIn = $this->paramIn;
        $apply_id = intval($paramIn['apply_id']);
        if(empty($this->userinfo['staff_id']))
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('1001')));
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $model = new Repository\ApplyRepository();
        $info = $model->getInfo($apply_id);

        if($info['apply_category'] == 1){//请假图片
            $info['image_path'] =empty($info['image_path']) ? [] : explode(',',$info['image_path']);
        }
        if($info['apply_category'] == 3){//lh 分类 格式化图片格式
            //如果是 00点 到 6点之间 日期加一天 显示用
            $arr = explode(' ',$info['start_time']);

            $info['lh_date'] = $arr[0];
            $info['lh_time'] = $arr[1];
            $info['act_date'] = 1;//1- 当日 2-次日
            if($info['lh_date'] != $info['date_at']){
                $info['act_date'] = 2;
            }


//            if($info['start_time'] >= "{$info['lh_date']} 00:00:00" && $info['start_time'] <= "{$info['lh_date']} 06:00:00" )
//                $info['act_date'] = 2;
            $info['image_path'] =empty($info['image_path']) ? [] : explode(',',$info['image_path']);
        }

        if($info['apply_category'] == 1){//请假分类 格式化请假类型 返回对应文字描述
            $audit_model = new AuditRepository($this->lang);
            $description = $audit_model->typeName(2);
            $info['leave_type_text'] = empty($info['apply_type'])? '' : $description[$info['apply_type']];
        }
        return $this->jsonReturn(self::checkReturn(array('data' => $info)));

    }

    //编辑补申请信息 只针对 第二版  LH或者OT
    public function editApplyAction(){
        return false;
        $paramIn = $this->paramIn;
        $apply_id = intval($paramIn['apply_id']);
        $version = intval($paramIn['version']);

        //version 1 补卡和请假 不可编辑  后期可能要做
        if($version == 1)
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));

        if(empty($apply_id))
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
        $model = new Repository\ApplyRepository();
        $info = $model->getInfo($apply_id);

        if(empty($info))
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));

        //修改人与提交人不符
        $staff_id = $this->userinfo['staff_id'];
        if($staff_id != $info['staff_info_id'])
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4018')));

        $category = intval($info['apply_category']);
        if(empty($this->userinfo['staff_id']))
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('1001')));
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //验证时间是否合法期间内  两个版本的补申请时间区间
        if($version == 1){
            $start_date = "2019-03-16 00:00:00";
            $end_date = "2019-04-20 23:59:59";
            $tra = 5200;
        }else if($version == 2){
            $start_date = "2019-04-16 00:00:00";
            $end_date = "2019-05-10 23:59:59";
            $tra = 5204;
        }else if($version == 3){
            $start_date = "2019-05-16 00:00:00";
            $end_date = "2019-06-15 23:59:59";
            $tra = 5205;
        }else{
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        try{
            if($category == 2){//ot修改
                $start_time = date('Y-m-d H:i:s',strtotime($paramIn['start_time']));
                $date_at = date('Y-m-d',strtotime($start_time));
                //验证是否在限制时间区间内
                if($date_at < $start_date || $date_at > $end_date)
                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_($tra)));

                if($info['apply_type'] == 1 || $info['apply_type'] == 2)
                    $paramIn['end_time'] = '';

                $end_time = $paramIn['end_time'];
                if(empty($paramIn['end_time'])){
                    $startTime = strtotime($start_time);
                    $endTime = $startTime + intval($paramIn['duration']) * 3600;
                    $end_time = date('Y-m-d H:i:s',$endTime);
                }
                //验证 修改操作是否符合条件
                $check = $this->server['overtime']->checkApplyOvertime($start_time,$end_time,$info['apply_type']);

                if($check['code'] == 1){
                    $act_last = $check['data']['act_last'];
                    //更新数据
                    $update['start_time'] = $start_time;
                    $update['end_time'] = $end_time;
                    $update['duration'] = $act_last;
                    $update['reason'] = trim($paramIn['reason']);
                    $flag = $model->updateInfo($apply_id, $update);
                    if($flag > 0){
                        return $this->jsonReturn(self::checkReturn([]));
                    }else{
                        return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
                    }
                }else{
                    return $this->jsonReturn($check);
                }
            }
            if($category == 3){//lh修改
                $lhDate        = $this->processingDefault($paramIn, 'lh_date');
                $lhTime        = $this->processingDefault($paramIn, 'lh_time');
                $lhPlateNumber = $this->processingDefault($paramIn, 'lh_plate_number');
                $auditReason   = $this->processingDefault($paramIn, 'audit_reason');
                $imagePathArr  = $this->processingDefault($paramIn, 'image_path');
                $lh_datetime = $lhDate.' '.$lhTime;
                $lh_datetime = date('Y-m-d H:i:s',strtotime($lh_datetime));
                $lh_date = date('Y-m-d',strtotime($lh_datetime));
                $day_type = intval($paramIn['act_date']);

                //检验时间区间
                if($lh_datetime < $start_date || $lh_datetime > $end_date)
                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_($tra)));

                //校验特殊时间区间
//                $time = checkIsBetweenTime($paramIn['lh_time']);
//                if (!$time) {
//                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('2110')));
//                }
                $model = new Repository\ApplyRepository($this->lang);

                //验证该LH申请时间内 是否有 晚班申请 LH和晚班 互斥
                $checkNight = $model->checkNightWork($this->userinfo['staff_id'],$lh_date);
                if($checkNight > 0)
                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('5201')));

                if($lh_datetime < $start_date || $lh_datetime > $end_date){
                    return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_($tra)));
                }

                //验证当天是否存在 真实ot
                $ot_model = new OvertimeRepository($this->timezone);
                $check_ot = $ot_model->getOtByDate($this->userinfo['staff_id'],$lh_date);
                if(!empty($check_ot)){
                    $types = array_column($check_ot,'type');//type  1 2 不让申请lh
                    if(in_array(1,$types) || in_array(2,$types))
                        return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('5201')));

                }

//                //验证 申请时间必须在21:00-次日6:00之间
//                if($lh_datetime > "{$lh_date} 06:00:00" && $lh_datetime < "{$lh_date} 21:00:00")
//                    return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('5206')));
//
//
                //新增当日次日判断
                if($day_type == 1){//当日  必须不能在 00点到 06点之间

//                    if($lh_datetime >= "{$lh_date} 00:00:00" && $lh_datetime <= "{$lh_date} 06:00:00")
//                        return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('5206')));

                }else if($day_type == 2){//次日  必须不能在 21点到 24点之间
                    $lh_datetime = date('Y-m-d H:i:s',strtotime($lh_datetime) + 24 * 3600);
//                    if($lh_datetime >= "{$lh_date} 21:00:00" && $lh_datetime <= "{$lh_date} 23:59:59")
//                        return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('5206')));
                }

                $update['start_time'] = $lh_datetime;
                $update['lh_plate_number'] = trim($lhPlateNumber);
                $update['reason'] = trim($auditReason);

                //图片url拼接
                $imgString = '';
                if(!empty($imagePathArr)){
                    $imgString = implode(',',$imagePathArr);
                }
                $update['image_path'] = $imgString;

                $flag = $model->updateInfo($apply_id, $update);

                if($flag > 0){
                    return $this->jsonReturn(self::checkReturn([]));
                }else{
                    return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
                }
            }
            return $this->jsonReturn(self::checkReturn(array()));
        }catch (\Exception $e){
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

    //提交人自己撤销申请  从表中清除
    public function cancelApplyAction(){
        return false;
        $paramIn = $this->paramIn;
        $apply_id = intval($paramIn['apply_id']);
        $model = new Repository\ApplyRepository();
        $info = $model->getInfo($apply_id);
        if(empty($info))
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
        //修改人与提交人不符
        $staff_id = $this->userinfo['staff_id'];
        if($staff_id != $info['staff_info_id'])
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4018')));

        $flag = $model->delInfo($apply_id);
        if($flag){
            return $this->jsonReturn(self::checkReturn([]));
        }else{
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }

    }

}