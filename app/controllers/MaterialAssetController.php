<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\MaterialAssetServer;

/**
 * 新资产-资产相关操作控制器
 * Class MaterialAssetController
 * @package FlashExpress\bi\App\Controllers
 */
class MaterialAssetController extends Controllers\ControllerBase
{
    protected $paramIn;
    protected $assetServer;

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        $this->assetServer = new MaterialAssetServer($this->lang, $this->timezone);
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * 资产申请-选择资产（可申请资产列表）
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63096
     */
    public function getAssetListAction()
    {
        $result = $this->assetServer->getAssetList($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产申请-输入基本信息回显
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63098
     */
    public function getAddDefaultAction()
    {
        $result = $this->assetServer->getAddDefault($this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产申请-选择资产使用地点
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63097
     */
    public function getAddressListAction()
    {
        $result = $this->assetServer->getAddressList($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产申请-获取收货人
     * @api https://yapi.flashexpress.pub/project/93/interface/api/88166
     */
    public function getConsigneeListAction()
    {
        $result = $this->assetServer->getConsigneeList($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产申请-提交
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63099
     */
    public function applyAction()
    {
        $result = $this->assetServer->applyUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产申请-取消
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63100
     */
    public function cancelAction()
    {
        $result = $this->assetServer->cancelUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产申请-通过
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63102
     */
    public function passAction()
    {
        $result = $this->assetServer->passUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产申请-驳回
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63101
     */
    public function rejectAction()
    {
        $result = $this->assetServer->rejectUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产申请-详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63103
     */
    public function detailAction()
    {
        $result = $this->assetServer->detail($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产申请-查看出库信息
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63116
     */
    public function getAssetOutStorageListAction()
    {
        $result = $this->assetServer->getAssetOutStorageList($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产申请-查看路由
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63117
     */
    public function getOutboundTrackingInfoAction()
    {
        $result = $this->assetServer->getOutboundTrackingInfo($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产申请-获取审批通过或驳回站内信消息详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/87968
     */
    public function getAuditMsgAction()
    {
        $result = $this->assetServer->getAuditMsg($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 离职资产-上级处理-枚举
     * @api
     */
    public function getAssetsManagerDefaultAction()
    {
        $result = $this->assetServer->getAssetsManagerDefault($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-我的资产-列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63475
     */
    public function getMyAssetListAction()
    {
        $result = $this->assetServer->getMyAssetList($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 离职资产-上级处理-详情
     * @api
     */
    public function getAssetsManagerDetailAction()
    {
        $result = $this->assetServer->getAssetsManagerDetail($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-提交转移/退回时-我的资产列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63502
     */
    public function getMyAssetByIdAction()
    {
        $result = $this->assetServer->getMyAssetById($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 离职资产-上级处理-搜索barcode
     * @api
     */
    public function searchBarcodeAction()
    {
        $result = $this->assetServer->searchAssetsManagerBarcode($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-我的资产-详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63478
     */
    public function getMyAssetDetailAction()
    {
        $result = $this->assetServer->getMyAssetDetail($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 离职资产-上级处理-添加
     * @api
     */
    public function addAssetsManagerInfoAction()
    {
        $result = $this->assetServer->addAssetsManagerInfoUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-获取枚举
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63503
     */
    public function getOptionsDefaultAction()
    {
        $result = $this->assetServer->getOptionsDefault($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 离职资产-上级处理-保存
     * @api
     */
    public function editSaveByAction()
    {
        $result = $this->assetServer->editSaveByUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-我的资产-批量转移
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63479
     */
    public function batchTransferAction()
    {
        $result = $this->assetServer->batchTransferUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 离职资产-上级处理-删除
     * @api
     */
    public function deleteLeaveAssetsAction()
    {
        $result = $this->assetServer->deleteLeaveAssetsUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-待接收资产数量(小红点)
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63481
     */
    public function getToBeReceiverAction()
    {
        $result = $this->assetServer->setExpire(300)->getToBeReceiverFromCache($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn(['data' => ['count' => $result]]));
    }

    /**
     * 离职资产-上级处理-列表页的红点
     * @api
     */
    public function getLeaveAssetsCountAction()
    {
        $lod_assets_count = (new AuditServer($this->lang, $this->timezone))->getNotResignAssetNum($this->userinfo['staff_id']);
        $return = ['num' => $lod_assets_count];
        return $this->jsonReturn(self::checkReturn(['data' => $return ?? 0]));
    }

    /**
     * 离职资产消息-主管的内容获取
     * @api
     */
    public function getLeaveMessageContentAction()
    {
        $result = $this->assetServer->getLeaveMessageContent($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-我的资产-批量撤销
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63480
     */
    public function batchCancelAction()
    {
        $result = $this->assetServer->batchCancelUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-待接收-批量拒绝
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63482
     */
    public function batchRejectAction()
    {
        $result = $this->assetServer->batchRejectUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-待接收-批量接收
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63484
     */
    public function batchReceptionAction()
    {
        $result = $this->assetServer->batchReceptionUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-待接收-列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63483
     */
    public function getReceiverListAction()
    {
        $result = $this->assetServer->getReceiverList($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-待接收-勾选接收/勾选拒绝时的列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63524
     */
    public function getReceiverListByIdAction()
    {
        $result = $this->assetServer->getReceiverListById($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-待接收-生成资产同意书
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63485
     */
    public function getAssetsPdfAction()
    {
        $result = $this->assetServer->getAssetsPdf($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-待接收-详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63594
     */
    public function getReceiverDetailAction()
    {
        $result = $this->assetServer->getReceiverDetail($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-站内信-通过消息id获取资产详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63598
     */
    public function getAssetsByMsgIdAction()
    {
        $validations = [
            "msg_id" => "Required|StrLenGe:1"
        ];
        $this->validateCheck($this->paramIn, $validations);
        $result = $this->assetServer->getAssetsByMsgId($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    /**
     * 资产管理-获取登陆者信息
     * @api https://yapi.flashexpress.pub/project/93/interface/api/82022
     */
    public function getUserInfoAction()
    {
        $result = $this->assetServer->getUserInfo($this->userinfo);
        return $this->jsonReturn(self::checkReturn(['data' =>$result]));
    }

    /**
     * 资产转移-站内信-资产批量变更提醒[导入转移消息详情]
     * @api https://yapi.flashexpress.pub/project/93/interface/api/84248
     */
    public function getAssetTransferMsgInfoAction()
    {
        $result = $this->assetServer->getAssetTransferMsgInfo($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产转移-转移-接收人工号
     * @api https://yapi.flashexpress.pub/project/93/interface/api/87437
     */
    public function searchStaffAction()
    {
        $result = $this->assetServer->searchStaff($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产退回-退回类型-筛选
     * @api https://yapi.flashexpress.pub/project/93/interface/api/85763
     */
    public function searchReturnTypeAction()
    {
        $result = $this->assetServer->searchReturnType($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产退回-我的资产-退回（批量）/ 资产管理-退回资产申请-创建-提交
     * @api https://yapi.flashexpress.pub/project/93/interface/api/85784
     */
    public function batchReturnAction()
    {
        $result = $this->assetServer->setLockConf(3)->batchReturnUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产退回-我的资产-撤销退回（批量）
     * @api https://yapi.flashexpress.pub/project/93/interface/api/85805
     */
    public function batchReturnCancelAction()
    {
        $result = $this->assetServer->setLockConf(3)->batchReturnCancelUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产管理-退回资产申请-列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/85802
     */
    public function returnListAction()
    {
        $result = $this->assetServer->returnList($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产管理-退回资产申请-查看
     * @api https://yapi.flashexpress.pub/project/93/interface/api/85814
     */
    public function returnDetailAction()
    {
        $result = $this->assetServer->returnDetail($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产管理-退回资产申请-撤销
     * @api https://yapi.flashexpress.pub/project/93/interface/api/85808
     */
    public function returnCancelAction()
    {
        $result = $this->assetServer->setLockConf(3)->returnCancelUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产管理-退回资产申请-创建-添加资产-列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/85817
     */
    public function returnAddAssetsAction()
    {
        $result = $this->assetServer->returnAddAssets($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }
}
