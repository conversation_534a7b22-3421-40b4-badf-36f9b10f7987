<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 11/3/21
 * Time: 7:58 PM
 */

namespace FlashExpress\bi\App\Controllers;
use App\Country\Tools;
use FlashExpress\bi\App\Server\OsOvertimeServer;

class OsOvertimeController extends ControllerBase{

    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode($this->request->getRawBody(),true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
        $this->url_log($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 获取加班类型
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getTypeOvertimeAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $server = new OsOvertimeServer($this->lang, $this->timezone);
        $server = Tools::reBuildCountryInstance($server, [$this->lang, $this->timezone]);
        /**
         * @see \FlashExpress\bi\App\Modules\Ph\Server\OsOvertimeServer::getTypeOsOvertime()
         */
        $returnArr = $server->getTypeOsOvertime($paramIn);

        $this->jsonReturn($returnArr);
    }


    //获取可选网点 和班次 下拉
    public function getOsSelectStoreAction(){

        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $server = new OsOvertimeServer($this->lang, $this->timezone);
        $data = $server->getViewStores($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    public function getOsSelectShiftAction(){

        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $server = new OsOvertimeServer($this->lang, $this->timezone);
        $data = $server->getViewShifts($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }



    //页面 选择网点和班次之后 选择对应的 当天和昨天 打卡的
    public function getOsSelectStaffAction(){
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $server = new OsOvertimeServer($this->lang, $this->timezone);
        $data = $server->getOsStaffs($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }


    /**
     * 新建加班
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function addOsOtAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] = $this->userinfo;
        $validations         = [
            "staff_id"  => "Required|Int",
            "reason"    => "Required|StrLenGeLe:0,1000|>>>:" . $this->getTranslation()->_('5110'),
            "staff_info_ids" => "Required|Str",
            "duration" => "Required|Float"
        ];
        $this->validateCheck($paramIn, $validations);
        $server = new OsOvertimeServer($this->lang, $this->timezone);
        $server = Tools::reBuildCountryInstance($server, [$this->lang, $this->timezone]);
        $returnArr = $server->setLockConf(60,true)->addOsOtUseLock($paramIn);
        $this->jsonReturn($returnArr);
    }

    /**
     * 审批同意 和驳回 加班
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function auditOsOtAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations         = [
            "staff_id"           => "Required|Int",
            "audit_id"           => "Required|Int",
            "reject_reason"      => "Required|StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('1020'),
        ];
        $this->validateCheck($paramIn, $validations);
        $server = new OsOvertimeServer($this->lang, $this->timezone);
        $server = Tools::reBuildCountryInstance($server, [$this->lang, $this->timezone]);
        $returnArr = $server->setLockConf(60,true)->updateOsOtUseLock($paramIn);
        $this->jsonReturn($returnArr);

    }


    /**
     * 是否第一次打开ot申请页面
     */
    public function isReadRuleAction()
    {
        $isRead = (new OsOvertimeServer($this->lang, $this->timezone, $this->userinfo))->isReadRule();
        $this->jsonReturn($this->checkReturn(['data' => ['is_read' => $isRead]]));
    }

    /**
     * 加班规则弹窗 提示
     */
    public function otRuleConfirmAction(){
        $param['user_info'] = $this->userinfo;
        $text = (new OsOvertimeServer($this->lang, $this->timezone, $this->userinfo))->getOtRuleText($param);
        $this->jsonReturn($this->checkReturn(['data' => $text]));
    }



}