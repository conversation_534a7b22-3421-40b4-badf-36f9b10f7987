<?php
/**
 * Author: Bruce
 * Date  : 2023-04-17 10:21
 * Description:
 */

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\QuitclaimServer;
use FlashExpress\bi\App\Server\ToolServer;

class QuitclaimController extends Controllers\ControllerBase
{
    /**
     * 初始化
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function initialize()
    {
        $this->cross();
        $method = $this->request->getMethod();
        /* 解决跨域问题 */
        if ($this->request->getMethod() == 'OPTIONS') {
            $this->jsonReturn($this->checkReturn(1));
        }

        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }

        //会带个_url参数
        unset($this->paramIn['_url']);

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }

        //时区配置
        $this->timezone = $this->getDI()['config']['application']['timeZone'];
        $this->lang     = $this->processingDefault($this->request->getHeaders(), 'Accept-Language', 1, 'en');
        if (function_exists('molten_get_traceid')) {
            $traceid = molten_get_traceid();
        }
        header('traceid:' . ($traceid ?? ''));

        //处理语言相关
        $this->languagePack = $this->di->get('languagePack');
        $this->languagePack->setLanguage($this->lang);  //设置语言环境，server  controller repository通用
        $this->t = $this->languagePack->t;              //获取全局语言包赋值到controller层

        $this->baseValidateCheck($this->paramIn);
    }

    /**
     * 基本参数校验
     * @param $paramIn
     * @return bool
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function baseValidateCheck($paramIn)
    {
        $validations = [
            "staff_info_id" => "Required|IntGe:0|>>>:staff_info_id " . $this->getTranslation()->_('miss_args'),
            "quitclaim_id"  => "Required|IntGe:0|>>>:quitclaim_id " . $this->getTranslation()->_('miss_args'),
            "time"          => "Required|IntGe:0|>>>:time " . $this->getTranslation()->_('miss_args'),
            "auth"          => "Required|StrLenGeLe:1,500|>>>:auth " . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);

        (new QuitclaimServer($this->lang, $this->timezone))->checkAuth($paramIn);
        return true;
    }

    /**
     * 验证身份信息
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function checkAuthAction()
    {
        $paramIn = $this->paramIn;

        $this->baseValidateCheck($paramIn);

        return $this->jsonReturn(self::checkReturn(1));
    }

    /**
     * 获取枚举信息
     */
    public function getBaseInfoAction()
    {
        $paramIn['staff_info_id'] = $this->paramIn['staff_info_id'];

        $result = (new QuitclaimServer($this->lang, $this->timezone))->getBaseInfo($paramIn);

        return $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    /**
     * 上传文件
     */
    public function img_uploadAction()
    {
        $paramIn = $this->paramIn;
        $result  = (new ToolServer($this->lang, $this->timezone))->img_upload($paramIn);

        return $this->jsonReturn($result);
    }

    /**
     * 获取 省 市 区
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function cityListAction()
    {
        $paramIn = $this->paramIn;

        $validations = [
            "type" => "Required|IntIn:1,2,3|>>>:type " . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);

        $result  = (new QuitclaimServer($this->lang, $this->timezone))->cityList($paramIn);

        return $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    /**
     * 提交 quitclaim
     */
    public function submitAction()
    {
        $paramIn = $this->paramIn;
        $this->validateCheckSubmit($paramIn);

        $returnArr = (new QuitclaimServer($this->lang, $this->timezone))->setLockConf(5)->submitQuitclaimInfoUseLock($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 校验提交参数
     * @param $paramIn
     */
    public function validateCheckSubmit($paramIn)
    {
        $validations = [
            "name"                      => "Required|StrLenGeLe:1,50|>>>:name " . $this->getTranslation()->_('miss_args'),
            "nationality"               => "Required|IntIn:" . implode(',',
                    array_keys(HrStaffInfoModel::NATIONALITY)) . "|>>>:nationality " . $this->getTranslation()->_('miss_args'),
            "is_married"                => "Required|IntIn:1,2|>>>:is_married " . $this->getTranslation()->_('miss_args'),
            "province"                  => "Required|StrLenGeLe:1,4|>>>:province " . $this->getTranslation()->_('miss_args'),
            "city"                      => "Required|StrLenGeLe:1,6|>>>:city " . $this->getTranslation()->_('miss_args'),
            "district"                  => "Required|StrLenGeLe:1,8|>>>:district " . $this->getTranslation()->_('miss_args'),
            "detail_address"            => "Required|StrLenGeLe:1,128|>>>:detail_address " . $this->getTranslation()->_('miss_args'),
            "bank_account_name"         => "Required|StrLenGeLe:1,128|>>>:bank_account_name " . $this->getTranslation()->_('miss_args'),
            "bank_account_number"       => "Required|StrLenGeLe:1,30|>>>:bank_account_number " . $this->getTranslation()->_('miss_args'),
            "place_of_signing_province" => "Required|StrLenGeLe:1,4|>>>:place_of_signing_province " . $this->getTranslation()->_('miss_args'),
            "place_of_signing_city"     => "Required|StrLenGeLe:1,6|>>>:place_of_signing_city " . $this->getTranslation()->_('miss_args'),
        ];

        $this->validateCheck($paramIn, $validations);
    }

    /**
     * 提交预览
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function previewAction()
    {
        $paramIn = $this->paramIn;
        $this->validateCheckSubmit($paramIn);

        $result = (new QuitclaimServer($this->lang, $this->timezone))->previewInfo($paramIn);

        return $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    /**
     * 详情
     */
    public function detailAction()
    {
        $paramIn = $this->paramIn;

        $result = (new QuitclaimServer($this->lang, $this->timezone))->quitclaimDetail($paramIn);

        return $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

}