<?php

/**
 * 注意！！！！
 * 这个是backyard "我的"=>"设置" 使用的
 */
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\SettingServer;

class SettingController extends ControllerBase
{

    public function listAction()
    {
        $staffInfo = (new StaffRepository($this->lang))->getStaffPosition($this->userinfo['staff_id']);
        $server    = new SettingServer($this->lang);
        $server->setStaffInfo($staffInfo);
        $result = $server->list();
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }
}
