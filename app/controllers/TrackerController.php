<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use Exception;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\ActivityServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\ToolServer;

class TrackerController extends Controllers\ControllerBase
{
    public function initialize()
    {
        if (strpos(strtolower($this->request->getContentType()),'application/json') !== false){
            $this->paramIn = $this->request->getJsonRawBody(true);
        }else{
            $this->paramIn = $this->request->get();
        }
        $this->paramIn = filter_param($this->paramIn);

        $this->cross();
    }

    /**
     * @throws ValidationException
     */
    public function img_uploadAction()
    {
        if(RUNTIME != 'dev'){
            throw new ValidationException('only dev');
        }
        $paramIn = $this->paramIn;
        $result  = (new ToolServer($this->lang, $this->timezone))->img_upload($paramIn);
        return $this->jsonReturn($result);
    }


    public function indexAction()
    {
        $this->jsonReturn($this->checkReturn(1));
    }
    /**
     * 前端UI版本
     * @throws BusinessException
     */
    public function save_by_ui_versionAction()
    {
        $paramIn       = $this->paramIn;
        $by_ui_version = $paramIn['by_ui_version'];
        $validations         = [
            "by_ui_version"      => "Required|StrLenGeLe:1,30|>>>: need by_ui_version" ,
        ];
        $this->validateCheck(['by_ui_version'=>$by_ui_version], $validations);
        $service = new SettingEnvServer();
        $service->saveSetVal('by_ui_version', $by_ui_version);
        $this->jsonReturn($this->checkReturn(['data' => []]));
    }


    //活动分享接口 根据消息id  不验证token
    public function activityShareAction(){
        $param = $this->paramIn;
        if (empty($param['msg_id'])) {
            throw new ValidationException('need msg_id');
        }
        $param['staff_id'] = $param['staff_info_id'];
        $server = new ActivityServer($this->lang, $this->timezone);
        $res    = $server->getMsgInfo($param);
        $this->jsonReturn($this->checkReturn(['data' => $res]));
    }

}