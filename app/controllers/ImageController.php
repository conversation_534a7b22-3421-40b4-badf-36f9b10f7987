<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use Exception;

class ImageController extends  \Phalcon\Mvc\Controller
//class ImageController extends Controllers\ControllerBase
{
    public function testImageAction()
    {
//        $loginUser = $this->userinfo;
        $loginUser = [];
        $path      = "http://image-demo.oss-cn-hangzhou.aliyuncs.com/example.jpg";
        $server    = new \FlashExpress\bi\App\Server\ImageServer();
        $path = " <meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no\" />
    <p>
        <span class=\"title address-t\" >ที่อยู่</span> : 
        <span class=\"content address-c\">(Jdjdj),曼谷汇权车站 Thailand ท่าเรือ พระนครศรีอยุธยา ท่าเรือ</span>
    </p>
    <p>
        <span class=\"title name-t\">ลูกค้า</span>  : 
        <span class=\"content name-c\">Udhdhdjhf</span>
    </p>
    <p>
        <img src=\"{$path}\"> : 
        <span class=\"title phone-t\">เบอร์โทร</span> : 
        <span class=\"content phone-c\">4554545555</span>
    </p>";


        $waterTxt = '1000';
        $path      = $server->addWaterTxtToOSS($path, $waterTxt);
        echo "<pre>";
        print_r($path);
        exit;
    }
}