<?php

namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\AttendanceCalendarServer;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\CheckPunchOutServer;
use FlashExpress\bi\App\Server\LeaveServer;

class AuditController extends ControllerBase
{
    protected $audit;
    public function initialize()
    {
        parent::initialize();
        $this->audit = Tools::reBuildCountryInstance(new AuditServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
    }

    /**
     * 补卡添加
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function reissueCardAddAction()
    {
        try { //[1]入参校验
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $validations         = [
                "staff_id"          => "Required|Int",
                "reissue_card_date" => "Required|DateTime",
                "attendance_type"   => "Required|IntIn:1,2",
                "audit_reason"      => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('1019'),
                'day_type'          => "Required|Int",//type 1 - 当天 2 - 次日
            ];
            $this->validateCheck($paramIn, $validations);

            //[2]业务处理
            $returnArr = $this->audit->setLockConf(60,true)->reissueCardAddUseLock($paramIn, $this->userinfo);

            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (ValidationException $e) {
            //无需补卡提醒
            if ($e->getCode() == 10086) {
                return $this->returnJson(1, $e->getMessage(), ['code' => -1,'message' => $e->getMessage(),'data' => ['param' => 'is_submit']]);//二次确认
            }
            //还能打卡提醒
            if ($e->getCode() == 10087) {
                return $this->returnJson(1, $e->getMessage(), ['code' => -1,'message' => $e->getMessage(),'data' => ['param' => 'is_confirm']]);//二次确认
            }
            //时长不足提示
            if ($e->getCode() == 10088) {
                return $this->returnJson(1, $e->getMessage(), ['code' => -1,'message' => $e->getMessage(),'data' => ['param' => 'confirm_10088']]);//二次确认
            }
            throw $e;
        }
    }

    /**
     * 请假添加
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function leaveAddAction()
    {

        try { //[1]入参校验
            //[1]入参校验
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $validations         = [
                "staff_id"         => "Required|Int",
                "leave_type"       => "Required|Int",
                "leave_start_time" => "Required|Date|>>>:".$this->getTranslation()->_('1023'),
                'leave_start_type' => "Required|IntIn:1,2",
                'leave_end_type'   => "Required|IntIn:1,2",
                "leave_end_time"   => "Required|Date|>>>:".$this->getTranslation()->_('1024'),
                "audit_reason"     => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('1019'),
            ];
            $this->validateCheck($paramIn, $validations);

            // 若无则继续原业务逻辑；若有则组织提交请假，返回提示：您有未完成的问卷消息/签字消息，请完成后再请假。
            (new BackyardServer($this->lang, $this->timezone))->leaveCheckMsg($paramIn['staff_id']);
            $returnArr =  $this->audit->setLockConf(60,true)->leaveAddUseLock($paramIn);
            $this->jsonReturn($returnArr);
        } catch (ValidationException $e) {
            //请假时间跟班次判断二次确认弹窗
            if ($e->getCode() == 10086) {
                return $this->returnJson(1, $e->getMessage(), ['code' => -1,'message' => $e->getMessage(),'data' => ['param' => 'is_submit']]);//二次确认
            }
            throw $e;
        }



    }

    /**
     * 修改审批状态
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function auditEditStatusAction()
    {
        //[1]入参 参数校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations = [
            "staff_id" => "Required|Int",
            "status"   => "Required|IntIn:2,3,4",
            "audit_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        if ($paramIn['status'] == 3 && (!isset($paramIn['reject_reason']) || empty($paramIn['reject_reason']))) {
            $this->jsonReturn($this->checkReturn('-1', 'reject_reason'));
        }
        if ($paramIn['status'] == 3) {
            if (mb_strlen($paramIn['reject_reason']) > 500) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1020')));
            }
        }

        //[2]业务处理
        $returnArr = $this->audit->auditEditStatusUseLock($paramIn, $this->userinfo);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
//
//    /**
//     * 获取 补卡/请假类型 只用在 补卡 type 1类型
//     * @Access  public
//     * @Param   request
//     * @Return  jsonData
//     */
//    public function getTypeBookAction()
//    {
//        //[1]入参 参数校验
//        $paramIn     = $this->paramIn;
//        $validations = [
//            "type" => "Required|Int",
//        ];
//        $this->validateCheck($paramIn, $validations);
//        $paramIn['user_info'] = $this->userinfo;
//
//        //[2]业务处理
//
//        $returnArr    = $this->audit->getTypeBook($paramIn);
//
//        //[3]数据返回
//        $this->jsonReturn($returnArr);
//    }

    /**
     * 换算时间
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function conversionTimeAction()
    {
        //[1]入参校验
        $paramIn     = $this->paramIn;
        $validations = [
            "leave_start_time" => "Required|Date",
            'leave_start_type' => "Required|IntIn:1,2",
            'leave_end_type'   => "Required|IntIn:1,2",
            "leave_end_time"   => "Required|Date",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['id'];

        //[2]业务处理
        $returnArr    = $this->audit->conversionTime($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 已废弃，不再维护
     * 获取待审核数量
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function waitAuditNumAction()
    {
        return ;
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new AuditServer($this->lang, $this->timezone))->waitAuditNum($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 新考勤日历
     */
    public function newAttendanceCalendarAction()
    {
        try {
            $paramIn = $this->paramIn;
            // 73570是郭吉发账号 用于检测hcm和by考勤一致性
            if ($this->userinfo['staff_id'] == 73570) {
                $paramIn['staff_id'] = isset($paramIn['staff_id']) ? $paramIn['staff_id'] : $this->userinfo['staff_id'];
            } else {
                $paramIn['staff_id'] = $this->userinfo['staff_id'];
            }
            $validations = [
                "staff_id" => "Required|Int",
                "year"     => "Required|Int",
                "month"    => "Required|Int",
            ];
            $this->validateCheck($paramIn, $validations);
            //[2]业务处理
            $paramIn['years'] = $paramIn['year'].'-'.$paramIn['month'];
            $paramIn['years'] = date('Y-m', strtotime($paramIn['years']));
            //[3]数据返回
            $attendanceCalendarServer = Tools::reBuildCountryInstance(
                new AttendanceCalendarServer($this->lang, $this->timezone),
                [$this->lang, $this->timezone]
            );

            $data = $attendanceCalendarServer->init($paramIn['staff_id'], $paramIn['years'])->attendanceCalendar();

            $data['name'] = $this->userinfo['name'];
            $this->jsonReturn($this->checkReturn(['data' => $data]));
        } catch (Exception $e) {
            $this->wLog("audit", $e->getMessage().$e->getFile().$e->getLine(), 'newAttendanceCalendarAction');
            return $this->jsonReturn(self::checkReturn(-3, "server error"));
        }
    }

    /**
     * 审批详情
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function auditDetailAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id" => "Required|Int",
            "audit_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new AuditServer($this->lang, $this->timezone))->auditDetail($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * 补卡次数查询
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function reissueCardCountAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $paramIn['user_info'] = $this->userinfo;

        //[2]业务处理
        $returnArr = (new AuditServer($this->lang, $this->timezone))->reissueCardCount($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * 撤销 请假申请  由于总有人错误提交 且审核人 不仔细查看 导致多次修改数据
     *
     * staff_id 操作人
     * type 1- 本人撤销自己待审核记录  修改为 已撤销  2- 上级审核人 撤销已审核记录 修改为待审核
     * audit_type 审核类型 1补卡 2请假 3申请LH费
     *
     */

    public function cancelAuditAction()
    {
        $paramIn = $this->paramIn;
        if (empty($paramIn['type'])) {
            $this->jsonReturn($this->checkReturn('-1', 'type'));
        }
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id" => "Required|Int",
            "type"     => "Required|Int",
            'audit_id' => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['status'] = enums::$audit_status['revoked'];
        //[2]业务处理
        $returnArr = $this->audit->auditEditStatusUseLock($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取审批列表权限
     * @param int staff_id 登录用户
     * @return Json
     */
    public function getAuditlistPermissionAction()
    {
        //[1]入参校验
        $paramIn                      = $this->paramIn;
        $paramIn['staff_id']          = $this->userinfo['staff_id'];
        $paramIn['positions']         = $this->userinfo['positions'];
        $paramIn['job_title']         = $this->userinfo['job_title'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['department_id']     = $this->userinfo['department_id'];

        $validations = [
            "staff_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $audit_server = (new AuditServer($this->lang, $this->timezone));
        $staffInfo = (new StaffRepository($this->lang))->getStaffPosition($paramIn['staff_id']);
        $staffInfo['positions'] = $this->userinfo['positions'];
        $audit_server->setStaffInfo($staffInfo);
        /**
         * @see AuditServer::getListPermission()
         */
        $returnArr = $audit_server->getListPermissionFromCache($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * 获取剩余假期天数
     */
    public function getHolidayLeftAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //新需求 临时写这 等请假上线 迁移到 server 外协员工 只显示不带薪事假
        $staff_model = new StaffRepository();
        $staff_info  = $staff_model->getStaffPosition($this->userinfo['staff_id']);
        if (empty($staff_info)) {
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('empty staff info')));
        }
        $paramIn['user_info'] = $staff_info;
        //[2]业务处理
        $audit_server = Tools::reBuildCountryInstance(new AuditServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
        $returnArr    = $audit_server->get_left_holidays($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    //请假类型 产假 对应模板 返回 对应 定制下拉菜单
    public function get_templateAction()
    {
        $paramIn = $this->paramIn;

        if (empty($paramIn['template_type'])) {
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
        }

        $server = new AuditServer($this->lang, $this->timezone);
        $return = $server->get_template(intval($paramIn['template_type']));

        $this->jsonReturn(self::checkReturn(['data' => $return]));
    }

    //对应请假类型说明选中不再提示
    public function readAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['id'];

        if (empty($paramIn['leave_type'])) {
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
        }

        $server = new LeaveServer($this->lang, $this->timezone);
        $flag   = $server->read_explain($paramIn);

        if ($flag) {
            $this->jsonReturn(self::checkReturn(1));
        }

        $this->jsonReturn(self::checkReturn(-3));
    }



    //获取年假 详情 列表
    public function getAnnualDetailAction(){
        $param['staff_id'] = $this->userinfo['staff_id'];
        $param['leave_type'] = enums::LEAVE_TYPE_1;
        $leaveServer = new LeaveServer($this->lang,$this->timezone);
        $leaveServer = Tools::reBuildCountryInstance($leaveServer,[$this->lang,$this->timezone]);
        $res = $leaveServer->getAnnualDetail($param);
        $this->jsonReturn($this->checkReturn(['data' => $res]));

    }

    //获取带薪事假原因
    public function getPaidLeaveReasonAction()
    {
        $leaveServer = new LeaveServer($this->lang,$this->timezone);
        $res = $leaveServer->getPaidLeaveReasonList();
        $this->jsonReturn($this->checkReturn(['data' => $res]));
    }

    /**
     * 补卡获取打卡和班次信息
     * @return void
     */
    public function attendanceShiftInfoAction()
    {
        $paramIn     = $this->paramIn;
        $res = $this->audit->attendanceShiftInfo($this->userinfo, $paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $res]));
    }

    //个人代理调用
    public function get_ic_parcel_infoAction(){
        if(empty($this->paramIn['attendance_date'])){
            throw new ValidationException('param attendance_date error');
        }
        $server = new CheckPunchOutServer($this->lang, $this->timezone);
        $data = $server->check_ic_from_java($this->userinfo['id'], $this->paramIn['attendance_date']);
        $msg = '';
        if(!empty($data['num']) || !empty($data['abnormalNum'])){
            $msg = $this->getTranslation()->_('attendance_punch_ic_notice_h5',['date_at' => $this->paramIn['attendance_date'],'num' => $data['num'] + $data['abnormalNum']]);
        }
        return $this->jsonReturn($this->checkReturn(['msg' => $msg,'data' => $data]));
    }

    public function test_icAction(){
        $param = $this->paramIn;
        $server = new AttendanceServer($this->lang , $this->timezone);
        $time = date('Y-m-d H:i:s');
        $server->sendIcOffCard($this->userinfo['id'], $param['attendance_date'], $time);
    }


}
