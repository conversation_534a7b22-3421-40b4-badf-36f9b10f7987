<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Server\PdpaServer;
use Exception;

class PdpaController extends Controllers\ControllerBase
{

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }

    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 员工同意PDPA消息
     */
    public function agreeAction() {
        try {
            $data['staff_id'] = $this->userinfo['staff_id'];
            $data['cancel_date'] = gmdate("Y-m-d H:i:s",time());

            $result = PdpaServer::getInstance($this->lang,$this->timezone)->staffEditIsAgreePdpa($data['staff_id'], 1);//加日志
            $this->getDI()->get('logger')->write_log("agreeAction 员工同意PDPA消息 {$data['staff_id']} 结果：{$result}",'info');
            $this->jsonReturn($this->checkReturn(1));
        } catch (ValidationException $e) {
            $this->jsonReturn($this->checkReturn(-3,$e->getMessage()));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage());
            $this->jsonReturn($this->checkReturn(-3));
        }
    }


    /**
     * 员工取消PDPA消息
     */
    public function cancelAction()
    {
        try {

            $paramIn['type'] = $this->request->get('type');//1不同意，2撤销同意
            $paramIn['type'] = empty($paramIn['type']) ? 2 : $paramIn['type'];
            //[2]数据验证
            $validations = [
                "type" => "Required|IntIn:1,2",
            ];
            $this->validateCheck($paramIn, $validations);

            $data['staff_id'] = $this->userinfo['staff_id'];
            $data['cancel_date'] = gmdate("Y-m-d H:i:s",time());

            //记录pdpa状态
            $result = PdpaServer::getInstance($this->lang,$this->timezone)->staffEditIsAgreePdpa($data['staff_id'], 0, $paramIn['type']);//加日志
            if($paramIn['type'] == 2) {
                $rmq = new RocketMQ('cancel-pdpa');
                $rid = $rmq->sendToMsg($data);
                $this->getDI()->get('logger')->write_log('backyard rmq exception: '.$rid, 'info');
                $this->getDI()->get('logger')->write_log("cancelAction 员工取消同意PDPA消息 {$data['staff_id']} 结果：{$result}",'info');
            } else {
                $this->getDI()->get('logger')->write_log("cancelAction 员工不同意PDPA消息 {$data['staff_id']} 结果：{$result}",'info');
            }

            $this->jsonReturn($this->checkReturn(1));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage());
            $this->jsonReturn($this->checkReturn(-3));
        }
    }

    public function getViewLangAction(){
        try {
            $staff_id = $this->userinfo['staff_id'];
            $lang = $this->lang;
            $ret = PdpaServer::getInstance($this->lang,$this->timezone)->getViewLang($staff_id,$lang);
            $this->jsonReturn($ret);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage());
            $this->jsonReturn($this->checkReturn(-3));
        }
    }

    /**
     * 获取pdpa个人隐私协议
     */
    public function getAgreementAction()
    {
        try {
            $ret = PdpaServer::getInstance($this->lang,$this->timezone)->getAgreementInfo();
            $this->jsonReturn(array('code' => 1, 'message' => 'success', 'data' => $ret));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage());
            $this->jsonReturn($this->checkReturn(-3));
        }
    }
}