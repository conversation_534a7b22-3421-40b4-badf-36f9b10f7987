<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Server\MilesServer;
use Exception;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\SystemExternalApprovalServer;

class FranchiseesController extends Controllers\ControllerBase
{

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 更新审批状态
     *
     */
    public function updateApprovalAction()
    {
        $params = $this->paramIn;

        $this->validateCheck([
            "status" => $params['status'],
            "id"     => $params['audit_id'],
        ], [
            "status" => 'Required|IntIn:2,3,4',
            "id"     => 'Required|Int',
        ]);

        $userinfo = $this->userinfo;

        $param['operator_id'] = $userinfo['staff_id'];                       //这是操作人
        $param['biz_type']    = enums::$audit_type['FRANCHISEES'];           //类型
        $param['reason']      = $params['reason'] ?? '';                     //备注
        $param['status']      = $params['status'];                           //类型
        $param['id']          = $params['audit_id'];                         //id

        $result = (new SystemExternalApprovalServer($this->lang,
            $this->timezone))->setLockConf(5)->updateApproveUseLock($param);

        $this->jsonReturn($result);
    }

}