<?php

namespace FlashExpress\bi\App\Controllers\School;


use FlashExpress\bi\App\Controllers\ControllerBase;
use FlashExpress\bi\App\library\ApiClient;

class CallController extends ControllerBase
{
    protected $paramIn;

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 培训系统接口调用
     *
     * @param $school_controller
     * @param $school_action
     * @return mixed
     */
    public function entranceAction($school_controller, $school_action)
    {
        try {
            if (env('close_school_server', 0)) {
                return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
            }
            $params     = $this->paramIn;
            $ac         = new ApiClient('school_rpc', '', $school_controller.'_'.$school_action, $this->lang);
            $api_params = [$params, $this->userinfo];
            $ac->setParam($api_params);
            $result = $ac->execute();
            if (isset($result['result'])) {
                $result['result']['msg'] = $result['result']['message'];
                return $this->jsonReturn($this->checkReturn($result['result']));
            } elseif (isset($result['error'])) {
                $logger = $this->getDI()->get('logger');
                $logger->write_log("school_svc_return_error:code=".$result['code'].'  message='.$result['error']);
                return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
            }
        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("backyardController:school_call-".$e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

}