<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Enums\PdfEnums;
use FlashExpress\bi\App\Server\JobTransferConfirmServer;
use FlashExpress\bi\App\Traits\FactoryTrait;

class JobTransferConfirmController extends ControllerBase
{
    use FactoryTrait;
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * @description 获取转岗确认列表
     */
    public function listAction()
    {
        //[1]参数定义
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        //[2]查询相同请求
        $server                 = $this->class_factory("JobTransferConfirmServer", $this->lang, $this->timezone);
        $param['staff_info_id'] = $server->changeStaffIdToMaster($param['staff_info_id']);
        $returnArr              = $server->getConfirmList($param);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * @description 获取转岗确认详情
     */
    public function confirmDetailAction()
    {
        //[1]参数定义
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        //校验参数
        $this->validateCheck($param, JobTransferConfirmServer::$validateDetail);

        //[2]查询相同请求
        $server                 = $this->class_factory("JobTransferConfirmServer", $this->lang, $this->timezone);
        $param['staff_info_id'] = $server->changeStaffIdToMaster($param['staff_info_id']);
        $param['template_cnf']  = PdfEnums::RESPONSE_TYPE_IMAGE;
        $returnArr              = $server->getConfirmDetail($param);

        //[3]数据返回
        $this->jsonReturn(self::checkReturn($returnArr));
    }

    /**
     * @description 人脸识别校验
     */
    public function authenticationAction()
    {
        //[1]参数定义
        $params                  = $this->paramIn;
        $params['staff_info_id'] = $this->userinfo['staff_id'];

        //校验参数
        $this->validateCheck($params, JobTransferConfirmServer::$validateFace);

        //[2]查询相同请求
        $server    = $this->class_factory("JobTransferConfirmServer", $this->lang, $this->timezone);

        //请求
        $returnArr = $server->authenticationUseLock($params, $this->userinfo);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * @description 转岗确认
     */
    public function doConfirmAction()
    {
        //[1]参数定义
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        //校验参数
        $this->validateCheck($param, JobTransferConfirmServer::$validateConfirm);

        //[2]查询相同请求
        $server                 = $this->class_factory("JobTransferConfirmServer", $this->lang, $this->timezone);
        $param['staff_info_id'] = $server->changeStaffIdToMaster($param['staff_info_id']);
        $returnArr              = $server->doConfirmUseLock($param);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
}