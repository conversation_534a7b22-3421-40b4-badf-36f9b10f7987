<?php

namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers;
use Exception;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\PersoninfoServer;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\WorkHomeServer;

class WorkHomeController extends Controllers\ControllerBase
{

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
            $this->paramIn = filter_param($this->paramIn);
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    //添加
    public function addAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "home_date"  => "Required|Date|>>>:" . $this->getTranslation()->_('date error'),
            "place_code" => "Required|Str",
        ];
        $this->validateCheck($paramIn, $validations);
        $server = new WorkHomeServer($this->lang, $this->timezone);
        /**
         * @see WorkHomeServer::addRecord
         */
        $returnArr = $server->setLockConf(60, true)->addRecordUseLock($paramIn);
        $this->jsonReturn($returnArr);
    }


    //审批
    public function approveAction()
    {
        //[1]入参 参数校验
        $paramIn             = $this->paramIn;
        $paramIn['user_info'] = $this->userinfo;
        $validations = [
            "status"   => "Required|IntIn:2,3,4",
            "audit_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        //驳回原因
        if ($paramIn['status'] == enums::$audit_status['dismissed'] && (!isset($paramIn['reject_reason']) || empty($paramIn['reject_reason']))) {
            $this->jsonReturn($this->checkReturn('-1', 'reject_reason'));
        }
        if ($paramIn['status'] == enums::$audit_status['dismissed']) {
            if (mb_strlen($paramIn['reject_reason']) > 500) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1020')));
            }
        }
        /**
         * @see WorkHomeServer::approve()
         */
        $server = new WorkHomeServer($this->lang, $this->timezone);
        $returnArr = $server->approveUseLock($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    //获取 地址 接口
    public function getEnumsListAction()
    {
        $data = (new WorkHomeServer($this->lang, $this->timezone))->getEnumsList();
        $this->jsonReturn(self::checkReturn(['data' => $data]));
    }


}