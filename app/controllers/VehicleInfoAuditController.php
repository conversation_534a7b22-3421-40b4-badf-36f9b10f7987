<?php

namespace FlashExpress\bi\App\Controllers;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\VehicleInfoAuditServer;

class VehicleInfoAuditController extends ControllerBase
{

    /**
     * 更新审批
     */
    public function updateAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|IntIn:" . enums::$audit_status['approved'] . "," . enums::$audit_status['dismissed'] . "|>>>:status " . $this->getTranslation()->_('miss_args'),
            "reject_reason" => "IfIntEq:status," . enums::$audit_status['dismissed'] . "|Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1020'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = (new VehicleInfoAuditServer($this->lang, $this->timezone))->updateUseLock($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 撤销
     * @return null
     */
    public function cancelAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "audit_id"      => "Required|Int",
            "cancel_reason" => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1020'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = (new VehicleInfoAuditServer($this->lang, $this->timezone))->cancelUseLock($paramIn);
        return $this->jsonReturn($returnArr);
    }

}