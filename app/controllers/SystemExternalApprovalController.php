<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use Exception;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SystemExternalApprovalServer;

class SystemExternalApprovalController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();

        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);

    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    //外部审批流 在by 操作审批接口
    public function auditAction()
    {
        $paramIn                = $this->paramIn;
        $paramIn['operator_id'] = $this->userinfo['staff_id']; //这是操作人

        //[2]数据验证
        $validations = [
            'id'        => "Required",
            'biz_type'  => "Required",
            'serial_no' => "Required",
            'reason'    => 'Required',
            'status'    => 'Required',
        ];
        $this->validateCheck($paramIn, $validations);
        $result = (new SystemExternalApprovalServer($this->lang, $this->timezone))->updateApprove($paramIn);
        $this->jsonReturn($result);
    }

    /**
     * 外部审批在 by 审批接口，通用接口
     * 无特殊逻辑的，前端公用页面的审批
     */
    public function commonAuditAction()
    {
        $paramIn                = $this->paramIn;

        //[2]数据验证
        $validations = [
            'audit_id'      => "Required",
            'audit_type'    => "Required",
            'reject_reason' => 'Required',
            'status'        => 'Required',
        ];
        $this->validateCheck($paramIn, $validations);

        // 参数审批
        $updateParams = [
            'id'          => $paramIn['audit_id'],
            'biz_type'    => $paramIn['audit_type'],
            'reason'      => $paramIn['reject_reason'],
            'status'      => $paramIn['status'],
            'operator_id' => $this->userinfo['staff_id'],
        ];
        /**
         * @see ApprovalServer::commonAudit()
         */
        $result = (new ApprovalServer($this->lang, $this->timezone))->commonAuditUseLock($updateParams);

        $this->jsonReturn($result);
    }

    /**
     * 获取页面初始信息 众包
     */
    public function getCrowdsourcingInformationAction()
    {
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $paramIn['param_operation_staff_id'] = $this->userinfo['staff_id']; //这是操作人
        try {
            $result              = (new SystemExternalApprovalServer($this->lang, $this->timezone))->getCrowdsourcingInformation($paramIn);
            $this->jsonReturn($result);
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('getCrowdsourcingInformationAction:'.$e->getMessage() . ":request:" . json_encode($paramIn), 'info');
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_server')));
        }

    }

    /**
     * @description: 获取管辖范围的网点
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/9/21 15:46
     */
    public function  getJurisdictionStoreListAction(){
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id']; //这是操作人
        $result              = (new SystemExternalApprovalServer($this->lang, $this->timezone))->getJurisdictionStoreList($paramIn);
        $this->jsonReturn($result);
    }



    /**
     * @description:添加申请
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/21 16:21
     */
    public function addApplyAction()
    {

        //[1]参数定义
        $paramIn             = $this->paramIn;
        $paramIn['param_operation_staff_id'] = $this->userinfo['staff_id']; //这是操作人
        try {
            $SystemExternalApprovalServer    = (new SystemExternalApprovalServer($this->lang, $this->timezone));
            $paramIn['param_operation_type'] = $SystemExternalApprovalServer::param_operation_type_1;  //这是类型
            $redisKey                        = "lock_add_apply_" . $this->userinfo['staff_id'];

            //追加网点白名单
            if (isCountry('PH')) {
                $setting_model = new SettingEnvServer();
                $whiteList     = $setting_model->getSetVal(enums::SYSTEM_EXTERNAL_APPROVAL_WHITE_LIST);

                //如果申请网点不在白名单中，就拦截
                if (!empty($whiteList) && isset($paramIn['store_id']) && !in_array($paramIn['store_id'], explode(',', $whiteList))) {
                    throw new ValidationException($this->getTranslation()->_('err_msg_store_not_support',
                        ['store_name' => $paramIn['store_name']]));
                }
            }

            //[2]业务处理
            $result = $this->atomicLock(function () use ($paramIn, $SystemExternalApprovalServer) {
                return $SystemExternalApprovalServer->forwardParamIn($paramIn);
            }, $redisKey, 10);

            if ($result === false) { //没有获取到锁
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('ticket_repeat_msg')));
            }

            $this->jsonReturn($result);
        } catch (ValidationException $ve) {
            $this->jsonReturn($this->checkReturn(-3, $ve->getMessage()));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('addApplyAction:'.$e->getMessage() . ":request:" . json_encode($paramIn), 'info');
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

    /**
     * @description: 众包编辑内容(包括审核)
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/21 16:55
     */
    public function CrowdsourcingUpdateAction()
    {
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $paramIn['param_operation_staff_id'] = $this->userinfo['staff_id']; //这是操作人
        try {
            //[2]数据验证
            $validations = [
                "serial_no"  => "Required",
                "audit_id" => "Required",
                "status" => "Required",
            ];
            $this->validateCheck($paramIn, $validations);

            $SystemExternalApprovalServer = (new SystemExternalApprovalServer($this->lang, $this->timezone));

            $paramIn['param_operation_type'] = $SystemExternalApprovalServer::param_operation_type_3;  //这是类型
            $redisKey                        = 'lock_add_update_'.$this->userinfo['staff_id'].'_'.$paramIn['audit_id'];
            //[2]业务处理
            $result = $this->atomicLock(function () use ($paramIn, $SystemExternalApprovalServer) {
                return $SystemExternalApprovalServer->CrowdsourcingUpdate($paramIn);
            }, $redisKey, 10);

            $this->jsonReturn($result);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('getCrowdsourcingInformationAction:'.$e->getMessage() . ":request:" . json_encode($paramIn), 'info');
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }


    /**
     * @description: 获取变更记录
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/23 10:36
     */

    public function  getCrowdsourcingChangeLogAction(){
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $paramIn['param_operation_staff_id'] = $this->userinfo['staff_id']; //这是操作人
        try {
            $SystemExternalApprovalServer = (new SystemExternalApprovalServer($this->lang, $this->timezone));
            $paramIn['param_operation_type'] = $SystemExternalApprovalServer::param_operation_type_5;  //这是类型
            $result              = $SystemExternalApprovalServer->getCrowdsourcingChangeLog($paramIn);
            $this->jsonReturn($result);
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('getCrowdsourcingInformationAction:'.$e->getMessage() . ":request:" . json_encode($paramIn), 'info');
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }


    /**
     * @description: 获取众包编辑详情
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/23 10:36
     */

    public function  getCrowdsourcingDetailAction(){
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $paramIn['param_operation_staff_id'] = $this->userinfo['staff_id']; //这是操作人
        try {
            $SystemExternalApprovalServer = (new SystemExternalApprovalServer($this->lang, $this->timezone));
            $paramIn['param_operation_type'] = $SystemExternalApprovalServer::param_operation_type_7;  //这是类型
            $result              = $SystemExternalApprovalServer->forwardParamIn($paramIn);
            $this->jsonReturn($result);
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('getCrowdsourcingInformationAction:'.$e->getMessage() . ":request:" . json_encode($paramIn), 'info');
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }
}
