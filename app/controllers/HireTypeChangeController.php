<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\HireTypeChangeServer;

class HireTypeChangeController extends Controllers\ControllerBase
{

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
            $this->paramIn = filter_param($this->paramIn);
        }
    }

    //添加
    public function add_hire_changeAction()
    {
        $param              = $this->paramIn;
        $param['user_info'] = $this->userinfo;
        $server             = new HireTypeChangeServer($this->lang, $this->timezone);
        /**
         * @see HireTypeChangeServer::addHireChange
         */

        $validations = [
            "file_url"    => "Required|Str",//签名后的pdf地址
            "change_date" => "Required|Date",//转型日期
        ];
        $this->validateCheck($param, $validations);

        if (RUNTIME == 'dev') {
            $res = $server->addHireChange($param);
        } else {
            $res = $server->setLockConf(60, true)->addHireChangeUseLock($param);
        }
        $this->jsonReturn($res);
    }

    //审批
    public function approval_changeAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations = [
            "staff_id"      => "Required|Int",
            "audit_id"      => "Required|Int",
            "reason"        => "Required|StrLenGeLe:0,500|>>>:".$this->getTranslation()->_('1020'),
            "reject_reason" => "Required|StrLenGeLe:0,500|>>>:".$this->getTranslation()->_('1020'),
        ];
        $this->validateCheck($paramIn, $validations);
        $server = new HireTypeChangeServer($this->lang, $this->timezone);
        /**
         * @see HireTypeChangeServer::updateStatus
         */
        if (RUNTIME == 'dev') {
            $returnArr = $server->updateStatus($paramIn, $this->userinfo);
        } else {
            $returnArr = $server->setLockConf(60, true)->updateStatusUseLock($paramIn, $this->userinfo);
        }

        $this->jsonReturn($returnArr);
    }


    //临时pdf
    public function hire_pdf_tmpAction()
    {
        $param              = $this->paramIn;
        $param['user_info'] = $this->userinfo;
        $validations        = [
            "change_date" => "Required|Date",//转型日期
        ];
        $this->validateCheck($param, $validations);
        $server = new HireTypeChangeServer($this->lang, $this->timezone);
        $data   = $server->getPdf($param);
        $this->jsonReturn(self::checkReturn(['data' => $data]));
    }

    //最终pdf
    public function hire_pdf_signAction()
    {
        $param              = $this->paramIn;
        $param['user_info'] = $this->userinfo;
        $validations        = [
            "change_date" => "Required|Date",//转型日期
            'sign_url'    => "Required|str"//签名图片地址
        ];
        $this->validateCheck($param, $validations);
        $server = new HireTypeChangeServer($this->lang, $this->timezone);
        $data   = $server->getPdf($param);
        $this->jsonReturn(self::checkReturn(['data' => $data]));
    }


}