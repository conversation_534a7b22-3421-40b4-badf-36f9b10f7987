<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\ApprovalServer;

class OsAttendanceController extends Controllers\ControllerBase
{

   public function updateCorrectionAction()
   {
       $params = $this->paramIn;
       $this->validateCheck($params, [
           "status" => 'Required|IntIn:2,3',
           "audit_id"     => 'Required|Int',
       ]);
       $userinfo = $this->userinfo;

       if ($params['status'] == enums::APPROVAL_STATUS_APPROVAL) {
           (new ApprovalServer($this->lang, $this->timezone))->approvalUseLock(
               $params['audit_id'],
               enums::$audit_type['OSAT'],
               $userinfo['id'],
               $params['reason']
           );
       }
       if ($params['status'] == enums::APPROVAL_STATUS_REJECTED) {
           (new ApprovalServer($this->lang, $this->timezone))->rejectUseLock(
               $params['audit_id'],
               enums::$audit_type['OSAT'],
               $params['reject_reason'],
               $userinfo['id']
           );
       }

       $this->jsonReturn($this->checkReturn(1));
   }

}

