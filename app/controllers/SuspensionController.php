<?php
/**
 * Author: Bruce
 * Date  : 2024-12-19 21:25
 * Description:
 */

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\SuspensionServer;

class SuspensionController extends Controllers\ControllerBase
{

    /**
     * 获取员工信息
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function getStaffInfoAction()
    {
        $paramIn = $this->paramIn;

        $validations         = [
            "staff_info_id" => "Required|StrLenGeLe:1,50|>>>:staff_info_id " . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['positions'] = $this->userinfo['positions'];

        $returnArr = (new SuspensionServer($this->lang, $this->timezone))->getStaffInfo($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 获取 枚举信息
     * hold 类型， hold 原因
     */
    public function selectInfoAction()
    {
        $returnArr = (new SuspensionServer($this->lang, $this->timezone))->selectInfo();
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 创建申请，发起审批流
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function addAuditAction()
    {
        $paramIn = $this->paramIn;

        $validations         = [
            "staff_info_id" => "Required|StrLenGeLe:1,50|>>>:staff_info_id " . $this->getTranslation()->_('miss_args'),
            "description"   => "Required|StrLenGeLe:1,3010|>>>:description " . $this->getTranslation()->_('miss_args'),
            "file_url"      => "Required|Arr|ArrLenGeLe:1,9|>>>:file_url " . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['staff_name'] = $this->userinfo['name'];
        $paramIn['positions'] = $this->userinfo['positions'];

        $returnArr = (new SuspensionServer($this->lang, $this->timezone))->addAuditSuspensionUseLock($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 审批
     */
    public function submitAction()
    {
        $paramIn = $this->paramIn;

        $validations = [
            "status"   => "Required|IntIn:2,3",
            "audit_id" => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",

        ];
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['staff_name'] = $this->userinfo['name'];

        $suspensionServer = new SuspensionServer($this->lang, $this->timezone);
        $approvalServer = new ApprovalServer($this->lang, $this->timezone);
        if ($paramIn['status'] == enums::APPROVAL_STATUS_APPROVAL &&
            $approvalServer->isExistCanEditField($paramIn['audit_id'], AuditListEnums::APPROVAL_TYPE_SUSPENSION)) {

            //开始校验提交字段
            $erValidations = $suspensionServer->getErValidate($paramIn);
            $this->validateCheck($paramIn, array_merge($validations, $erValidations));

            //提交审核
            $returnArr = $suspensionServer->auditSubmitUseLock($paramIn);
        } else {
            $this->validateCheck($paramIn, $validations);

            //提交审批
            $returnArr = $suspensionServer->approvalSubmitUseLock($paramIn);
        }

        return $this->jsonReturn($returnArr);
    }

    /**
     * er节点审批，校验
     * @throws \Exception
     */
    public function checkAction()
    {
        $paramIn = $this->paramIn;

        $validations = [
            "status"   => "Required|IntIn:2",
            "audit_id" => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",

        ];
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['staff_name'] = $this->userinfo['name'];

        $data['result'] = true;
        $data['date'] = '';
        $suspensionServer = new SuspensionServer($this->lang, $this->timezone);
        $approvalServer = new ApprovalServer($this->lang, $this->timezone);
        if ($paramIn['status'] == enums::APPROVAL_STATUS_APPROVAL &&
            $approvalServer->isExistCanEditField($paramIn['audit_id'], AuditListEnums::APPROVAL_TYPE_SUSPENSION)) {

            //开始校验提交字段
            $erValidations = $suspensionServer->getErValidate($paramIn);
            $this->validateCheck($paramIn, array_merge($validations, $erValidations));
            $data = $suspensionServer->checkInfoEr($paramIn);
        }

        return $this->jsonReturn($this->checkReturn(['data' => $data]));
    }
}
