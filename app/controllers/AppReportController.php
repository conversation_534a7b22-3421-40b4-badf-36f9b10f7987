<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\ReportServer;
use FlashExpress\bi\App\library\HttpCurl;

class AppReportController extends Controllers\ControllerBase
{
    protected $server;

    public function initialize()
    {
        parent::initialize();
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    public function mainAction()
    {
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log('AppReport :'.json_encode($paramIn,JSON_UNESCAPED_UNICODE),'info');
        $this->jsonReturn( self::checkReturn(['data'=>$paramIn]) );
    }


}
