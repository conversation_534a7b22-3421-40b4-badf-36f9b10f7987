<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\ShareCenterServer;

class SharecenterController extends Controllers\ControllerBase
{
    public $paramIn = [];
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode($this->request->getRawBody(), true);
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function listAction()
    {
        $id = $this->request->get('id')??0;
//        $search = $this->request->get('file_name','string');
        $staffId      = $this->userinfo['staff_id'];
        $share_server = new ShareCenterServer($this->lang, $this->timezone);
        $treeList     = $share_server->getShareCenterList($staffId, $id);

        $this->jsonReturn($this->checkReturn(["data" => $treeList]));
    }

    //顶级文件夹 搜索 所有文件 模糊查询
    public function search_fileAction(){

        $param['file_name'] = $this->request->get('file_name','string');
        $param['id'] = $this->request->get('id','int');
        $param['staff_info'] = $this->userinfo;

        $share_server = new ShareCenterServer($this->lang,$this->timezone);
        $data = $share_server->search_all_file($param);
        $this->jsonReturn($data);
    }
}


