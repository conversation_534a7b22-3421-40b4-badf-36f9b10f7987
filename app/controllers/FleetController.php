<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\FleetAuditModel;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\FleetServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysStoreServer;

class FleetController extends Controllers\ControllerBase
{
    protected $paramIn;
    protected $storeServer;

    public function initialize()
    {
        parent::initialize();
        $this->storeServer = new SysStoreServer();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * 添加加班车申请
     * @return void
     * @throws Exception
     */
    public function addFleetAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;

        get_runtime() == 'dev' && $this->logger->write_log(['addFleet', 'paramIn' => $paramIn],'notice');

        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['job_title'] = $this->userinfo['job_title'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['url']       = $this->config->hr->hc_api_url . '/svc/by_api';
        $paramIn['userinfo']  = $this->userinfo;
        $paramIn['reason'] = $this->paramIn['reason'] = trim($this->paramIn['reason']);

        if (empty($paramIn['audit_type'])) {
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
        }

        if ($paramIn['audit_type'] == enums::$fleet_audit_type['Normal']) {
            $validations = [
                "car_type"      => "Required|Int",
                "capacity"      => "Required|IntGtLe:0,5000",
                "store_ids"     => "Required|Arr",
                "reason"        => "StrLenGeLe:0,500|>>>:". $this->getTranslation()->_('1019'),
            ];
        } else {
            $this->paramIn['fd_courier_name']  = trim($this->paramIn['fd_courier_name']);
            $this->paramIn['fd_courier_phone'] = trim($this->paramIn['fd_courier_phone']);
            $this->paramIn['fd_courier_plate'] = trim($this->paramIn['fd_courier_plate']);
            $validations = [
                "car_type"      => "Required|Int",
                "capacity"      => "Required|IntGtLe:0,5000",
//                "start_store"   => "Required|StrLenGe:1",
//                "end_store"   => "Required|StrLenGe:1",
                "reason"        => "StrLenGeLe:0,500|>>>:". $this->getTranslation()->_('1019'),
                "fd_courier_id" => "Required|Int",
                "store_ids"     => "Arr",
                "fd_courier_name" => "Required|StrLenGeLe:1,50|>>>:" . $this->getTranslation()->_('err_msg_enter_driver_name'),
                "fd_courier_phone" => "Required|StrLenGeLe:1,20|>>>:" . $this->getTranslation()->_('err_msg_enter_driver_phone'),
                "fd_courier_plate" => "Required|StrLenGeLe:1,20|>>>:" . $this->getTranslation()->_('err_msg_enter_driver_plate'),
            ];
        }
        $this->validateCheck($this->paramIn, $validations);
        $redis_key  = 'lock_addFleet_' . $this->userinfo['staff_id'];
        $fleetServer = Tools::reBuildCountryInstance(new FleetServer($this->lang,$this->timezone),[$this->lang,$this->timezone]);

        //[2]业务处理
        $returnArr = $this->atomicLock(function () use ($fleetServer,$paramIn) {
            return $fleetServer->addFleet($paramIn);
        }, $redis_key, 15);

        if ($returnArr === false) { //没有获取到锁
            $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
        }

        //[3]数据返回
        if ($returnArr === false) {
            $this->jsonReturn(self::checkReturn(-3, 'please try again later'));
        }
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * 更新加班车
     */
    public function updateFleetAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['userinfo']  = $this->userinfo;

        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|Int",
            "reject_reason" => "StrLenGeLe:0,500",
        ];
        $this->validateCheck($this->paramIn, $validations);

        $fleetServer = new FleetServer($this->lang , $this->timezone);
        //[2]业务处理
        $reids_key  = 'lock_updateFleet_' . $paramIn['audit_id'];
        try {
            //查看使用哪个版本的审批
            $info = FleetAuditModel::findFirst($paramIn['audit_id']);
            if (empty($info)) {
                throw new ValidationException($this->getTranslation()->_('miles_C100100'));
            }

            $returnArr = $this->atomicLock(function () use ($fleetServer,$paramIn, $info) {
                return $fleetServer->updateFleetV2($paramIn);
            }, $reids_key, 10, false);

            if ($returnArr === false) { //没有获取到锁
                $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
            }

            //[3]数据返回
            if ($returnArr === false) {
                return $this->jsonReturn(self::checkReturn(-3, 'please try again later'));
            }
            $this->jsonReturn($returnArr);
        } catch (ValidationException $validationException) {
            $this->jsonReturn(self::checkReturn(-3, $validationException->getMessage()));
        } catch (\Exception $e) {
            $this->logger->write_log("[updateFleetAction]error:" . $e->getMessage() . $e->getTraceAsString());
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取车辆类型
     * 获取加班车申请类型
     * 获取虚拟网点，真实网点枚举
     */
    public function getCarTypeAction()
    {
        //[1]入参校验
        $paramIn = $this->paramIn;

        $fleetServer = new FleetServer($this->lang , $this->timezone);
        //[2]业务处理
        $carTypeList    = $fleetServer->getCarTypeList($paramIn);
        $fleetTypeList  = $fleetServer->getFleetAuditType($paramIn);
        $storeTypeList  = $fleetServer->getStoreType($paramIn);

        $reasonTypeList = $fleetServer->getReasonType();

        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        [$isSpBdcType,$staffStoreCategory]    = $fleetServer->isSpBdcTypeAndStoreCategory($paramIn);

        //[3]数据返回
        return $this->jsonReturn($this->checkReturn([
            'data' => [
                'dataList' => [
                    'car_type'             => $carTypeList,
                    'fleet_type'           => $fleetTypeList,
                    'store_type'           => $storeTypeList,
                    'is_sp_bdc_type'       => $isSpBdcType,//废弃
                    'staff_store_category' => $staffStoreCategory,
                    'reason_type'          => $reasonTypeList,
                ],
            ],
        ]));
    }

    /**
     * 根据 category 获取网点数据
     * @return null
     */
    public function getStoreListByCategoryAction()
    {
        // [1] 获取参数进行验证
        $paramIn     = $this->paramIn;
        $validations = [
            "categoryId" => "Required|IntGt:0|>>>:categoryId param error", // categoryId
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn = array_only($paramIn, array_keys($validations));
        // [2] 获取数据
        $sysList = ((new SysStoreServer()))->getStoreListByCategory((int)$paramIn['categoryId'], ['id AS store_id', 'name', 'category']);
        return $this->jsonReturn(['code' => ErrCode::SUCCESS, 'message' => 'success', 'data' => $sysList]);
    }

    /**
     * 获取网点列表
     * @throws Exception
     */
    public function storeListAction()
    {
        //[1]参数定义
        $paramIn              = $this->paramIn;
        $paramIn['type']      = 2;
        $paramIn['isAddHead'] = empty($paramIn['isAddHead']) ? 2 : $paramIn['isAddHead'];

        $storeServer = new SysStoreServer();
        //始发网点过滤黑名单网点
        if(isCountry('TH') && !empty($paramIn['isStartStore'])){
            $storeServer->makeFleetStoreBlacklist();
        }

        //[2]查询部门列表
        $storeData = $storeServer->list($paramIn);

        //[3]数据返回
        return $this->jsonReturn($this->checkReturn($storeData));
    }

    /**
     * 获取网点列表
     * @throws Exception
     */
    public function getStoreListAction()
    {
        //[1]参数定义
        $paramIn              = $this->paramIn;
        $paramIn['type']      = enums::RESPONSE_TYPE_DATA_DIRECT;
        $paramIn['isAddHead'] = empty($paramIn['isAddHead']) ? 2 : $paramIn['isAddHead'];

        $storeServer = new SysStoreServer();
        //始发网点过滤黑名单网点
        if(isCountry('TH') && !empty($paramIn['isStartStore'])){
            $storeServer->makeFleetStoreBlacklist();
        }

        //[2]查询部门列表
        $storeData = $storeServer->listV2($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn($storeData));
    }

    /**
     * 获取始发 虚拟网点，真实网点
     * @throws Exception
     */
    public function storeListByTypeAction()
    {
        //[1]参数定义
        $paramIn              = $this->paramIn;

        $validations = [
            "type" => "Required|IntIn:1,2|>>>:type param error", // type
        ];
        $this->validateCheck($paramIn, $validations);
        //[2]查询部门列表
        $storeServer = new SysStoreServer();
        //始发网点过滤黑名单网点
        if(isCountry('TH')){
            $storeServer->makeFleetStoreBlacklist();
        }
        $storeData = $storeServer->storeListByType($paramIn);

        //[3]数据返回
        return $this->jsonReturn($this->checkReturn($storeData));
    }

    /**
     * 获取始发网点、目的网点、预期到达日期相同的员工号
     */
    public function checkSimilarRequestAction()
    {
        //[1]参数定义
        $param       = $this->paramIn;

        $validations = [
            "start_store" => "StrLenGeLe:0,10|>>>:" . $this->getTranslation()->_('max_char_len', ['len' => 10]),
            "end_store"   => "StrLenGeLe:0,10|>>>:" . $this->getTranslation()->_('max_char_len', ['len' => 10]),
            "arrive_time" => "Required",
            "store_ids"   => "Arr",
        ];
        $this->validateCheck($param, $validations);

        //[2]查询相同请求
        $server = new FleetServer($this->lang, $this->timezone);
        $returnArr = $server->getSimilarRequest($param);
        //[3]数据返回
        return $this->jsonReturn($returnArr);
    }

    /**
     * 批量新增加班车申请
     */
    public function batchAddFleetAction()
    {
        //[1]入参校验
        $paramIn                    = $this->paramIn;
        $submitterInfo              = [];
        $submitterInfo['staff_id']  = $this->userinfo['staff_id'];
        $submitterInfo['positions'] = $this->userinfo['positions'];
        $submitterInfo['organization_id']   = $this->userinfo['organization_id'];
        $submitterInfo['organization_type'] = $this->userinfo['organization_type'];
        $submitterInfo['userinfo']  = $this->userinfo;
        $sucessIdArr          = [];

        if (!isset($paramIn['list']) || empty($paramIn['list']) || count($paramIn['list']) === 0) {
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
        }

        $fleetServer = Tools::reBuildCountryInstance(new FleetServer($this->lang,$this->timezone),[$this->lang,$this->timezone]);

        //验证必填字段
        foreach ($paramIn['list'] as $params) {
            $fleetServer->batchAddValidation($params);
        }

        //[2]业务处理
        $redis_key  = 'lock_batchAddFleet_' . $paramIn['batch_key'];
        try {

            $returnArr = $this->atomicLock(function () use ($fleetServer,$paramIn, $submitterInfo, &$successIdArr) {
                foreach ($paramIn['list'] as $k => $params) {

                    $params = array_merge($params, $submitterInfo);
                    $result = $fleetServer->addFleet($params);
                    if (!empty($result)) { //记录insert成功的记录的index，返回结果里要包含全部insert成功的index
                        $successIdArr[] = $params['index'];
                    }
                }
            }, $redis_key, 10);

            if ($returnArr === false) { //没有获取到锁
                $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
            }

        } catch (\Exception $e) {
            $this->jsonReturn(['code' => 2, 'message'=> $e->getMessage(), 'data'=> ['count' => count($successIdArr), 'list' => $sucessIdArr]]);
        }

        //[3]数据返回
        $this->jsonReturn(['code' => 1, 'message'=> 'success', 'data'=> ['count' => count($successIdArr)]]);
    }

    /**
     * 获取批次号
     */
    public function getBatchKeyAction()
    {
        $data = (new FleetServer($this->lang , $this->timezone))->getRandomId();
        $this->jsonReturn(self::checkReturn(['data' => ['batch_key' => $data]]));
    }

    /**
     * 搜索司机信息
     */
    public function searchDriverAction()
    {
        //接受参数
        $validations = [
            "staff_info_id" => "Required|IntGtLt:0,1000000000|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($this->paramIn, $validations);
        $staff_info_id = $this->paramIn['staff_info_id']; //得到工号，模糊搜索
        $server = (new FleetServer($this->lang, $this->timezone));
        //得到搜索结果
        $data = $server->getStaffDriverById($staff_info_id);
        $this->jsonReturn(self::checkReturn(['data' => $data]));

    }


    /**
     * 获取司机详情信息
     */
    public function getDriverDetailAction()
    {
        //接受参数
        $validations = [
            "staff_info_id" => "Required|IntGtLt:1,1000000000|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($this->paramIn, $validations);
        $staff_info_id = $this->paramIn['staff_info_id']; //得到工号，模糊搜索
        $server = (new FleetServer($this->lang, $this->timezone));
        //得到搜索结果
        $data = $server->getStaffDetail($staff_info_id);
        $this->jsonReturn(self::checkReturn(['data' => $data]));
    }

    //获取 当前登录人的 申请列表
    public function listAction()
    {
        $param = $this->paramIn;
        $this->getDI()->get('logger')->write_log("fleet_list ".json_encode($param), 'info');

        $param['user_info'] = $this->userinfo;
        $server             = new FleetServer($this->lang, $this->timezone);
        $data               = $server->apply_list($param);

        return $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * @description 加班车调度信息变更消息详情
     * @return mixed
     */
    public function fleetSchedulingChgMsgDetailAction()
    {
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        if (empty($param)) {
            $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }

        $server = new BackyardServer($this->lang, $this->timezone);
        // 验证该员工是否收到扣款消息
        $msgInfo = $server->getMessageCourierDetail($param);
        if (empty($msgInfo)) {
            $this->jsonReturn(['code' => 0, 'msg' => '员工没有收到该消息', 'data' => []]);
        }

        //获取语言
        $staffLang = (new StaffServer())->getBatchStaffLanguage([$param['staff_info_id']]);
        $lang      = $staffLang[$param['staff_info_id']] ?? "en";
        $t         = $this->getTranslation($lang);

        //给链接加一个签名
        $preg = '/\{.+\}/';
        preg_match($preg, $msgInfo['content'], $res);
        if (!isset($res[0])) {
            $this->jsonReturn(['code' => 0, 'msg' => '员工没有收到该消息', 'data' => []]);
        }
        $content = json_decode($res[0], true);

        if ($content['schedule_type'] == 2) {
            $terminateReason = isset($content['terminate_reason']) && $content['terminate_reason'] ? $content['terminate_reason'] : $t->_($content['terminate_reason_key']);

            $noticeContent = $t->_("fleet_notice_terminate", [
                'line_name'        => $content['line_name'],
                'expected_date'    => date("Y-m-d H:i", strtotime($content['expected_date'])),
                'fleet_type'       => $t->_($content['audit_type_key']),
                'terminate_reason' => $terminateReason,
            ]);
        } else {
            $noticeContent = $t->_("fleet_notice_change", [
                'line_name'        => $content['line_name'],
                'expected_date'    => date("Y-m-d H:i", strtotime($content['expected_date'])),
                'fleet_type'       => $t->_($content['audit_type_key']),
            ]);
        }

        $result = [
            'msg_content'      => $noticeContent,
            'audit_id'         => $content['audit_id'],
            'fleet_type'       => $t->_($content['audit_type_key']),
            'audit_show_type'  => 1,
            'audit_state_type' => 1,
            'tab'              => 1,
            'from'             => 'message',
            'schedule_change'  => $content['schedule_type'] == 2 ? FleetAuditModel::CADENCE_STATUS_TERMINATE: FleetAuditModel::CADENCE_STATUS_CHANGE,
        ];
        $this->logger->write_log("[fleetSchedulingChgMsgDetailAction]" . $res[0], "info");

        $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $result]);
    }

    /**
     * @description 校验线路是否符合FDC条件
     */
    public function checkFleetLineAction()
    {
        //[1]参数定义
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        $validations = [
            "audit_type"    => "Required|IntIn:1,2",
//            "start_store"   => 'IfIntEq:audit_type,2|Required|StrLenGe:1',
//            "end_store"     => 'IfIntEq:audit_type,2|Required|StrLenGe:1',
            "arrive_time"   => "Required|StrLenGe:1",
            "store_ids"     => 'IfIntEq:audit_type,1|Required|Arr|ArrLenGe:1'//泰国 fdc也要传了
        ];
        $this->validateCheck($param, $validations);

        //[2]查询相同请求
        $server = Tools::reBuildCountryInstance(new FleetServer($this->lang, $this->timezone),[$this->lang, $this->timezone]);
        $returnArr = $server->checkFleetLine($param);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 21177【ALL】预计入仓包裹优化
     * 申请 加班车的时候请求的，21177后 不需要了
     * @description
     */
    public function getFleetStatisticsAction()
    {
        //[1]参数定义
        $param                  = $this->paramIn;

        $validations = [
            "audit_type"    => "Required|IntIn:1,2",
            "start_store"   => 'IfIntEq:audit_type,2|Required|StrLenGe:1',
            "end_store"     => 'IfIntEq:audit_type,2|Required|StrLenGe:1',
            "arrive_time"   => "Required|StrLenGe:1",
            "store_ids"     => 'IfIntEq:audit_type,1|Required|Arr|ArrLenGe:1'
        ];
        $this->validateCheck($param, $validations);

        //[2]查询相同请求
        $server = new FleetServer($this->lang, $this->timezone);
        $returnArr = $server->getFleetStatistics($param);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
}
