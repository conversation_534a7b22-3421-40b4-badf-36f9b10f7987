<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers;
use Exception;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\PersoninfoServer;
use FlashExpress\bi\App\Repository\StaffRepository;

class CertificateController extends Controllers\ControllerBase{

    public function initialize(){
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn)){
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
            $this->paramIn = filter_param($this->paramIn);
        }
    }

    public function onConstruct(){
        parent::onConstruct();
    }

    //页面加载个人信息邮箱月份等接口
    public function salary_pdfAction(){
        $staff_server = new PersoninfoServer($this->lang,$this->timezone);
        $data = $staff_server->salary_need($this->userinfo['staff_id']);
        if(empty($data)){
            $data['min_date'] = $data['max_date'] = '';
            return $this->jsonReturn(self::checkReturn(array('data' => $data)));
        }
        $start = empty($data['min_date']) ? '' : $data['min_date'];
        $end = empty($data['max_date']) ? '' : $data['max_date'];

        //新需求 由于修改了进铎薪资的数据源 没到月底也会有数据 但是需要到每个月最后一天才显示
        $current_last_day = date('Y-m-d',strtotime("last day of"));//每月最后一天
        $current_month = date('Y-m',time());
        if($end == $current_month){
            //不是最后一天 或者是最后一天但是没到 13点
            if(date('Y-m-d') != $current_last_day || (date('Y-m-d') == $current_last_day && date('Y-m-d H:i:s') < $current_last_day.' 13:00:00')){
                $current_month = date('Y-m',strtotime("last day of -1 month"));
                if($start == $end)
                    $start = $end = $current_month;
                $end = $current_month;
            }
        }
        $data['min_date'] = $start;
        if($start != $end){
            $arr = array();
            $item = $end;
            $i = 12;
            while ($item >= $start && $i > 0){
                $arr[] = $item;
                $item = date('Y-m',strtotime("{$item} -1 month"));
                $i--;
            }
            $data['min_date'] = end($arr);
        }
        $data['max_date'] = $end;
        return $this->jsonReturn(self::checkReturn(array('data' => $data)));
    }


    /**
     * 工资条发送
     */
    public function salary_pdf_downloadAction(){
        try{
            $param['staff_info_id'] = $this->userinfo['staff_id'];
            $param['type'] = 1;
            $param['mail_type'] = empty($this->paramIn['mail_type']) ? 1 : intval($this->paramIn['mail_type']);
            $start = $this->paramIn['start_month'];
            $end = $this->paramIn['end_month'];

            $start = date('Y-m',strtotime($start));
            $end = date('Y-m',strtotime($end));

            if($start != $end){
                $item = $start;
                $i = 0;
                while ($item <= $end && $i < 12){
                    $param['date'][] = $item;
                    $item = date('Y-m',strtotime("{$item} +1 month"));
                    $i++;
                }
            }else{
                $param['date'] = [$start];
            }
            $client = new ApiClient('hcm_rpc','','by_download',$this->lang);
            $client->setParams($param);
            $result = $client->execute();
            if (isset($result['result']['code'],$result['result']['data']) && $result['result']['code'] == 1) {
                return $this->jsonReturn(self::checkReturn(array('data' => $result['result']['data'] ?? [])));
            }
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }catch (\Exception $e) {
            $this->logger->error("salary_pdf_downloadAction:".$e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }
    }

    //在职证明 页面加载接口 员工基本信息 薪资构成等
    public function on_jobAction(){
        $en_date = intval(strftime("%d",time())) . strftime(" %B %Y",time());
        $param['staff_id'] = $this->userinfo['staff_id'];
        $server                   = Tools::reBuildCountryInstance(new PersoninfoServer($this->lang, $this->timezone),
            [$this->lang, $this->timezone]);
        $data = $server->on_job_need($param);
        if(empty($data))
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));

        $hire_date = intval(strftime("%d",strtotime($data['hire_date']))) . strftime(" %B %Y",strtotime($data['hire_date']));
        $data['en_money'] = $data['th_money'] = '';
        if(!empty($data['base_salary'])){
            $data['base_salary'] = round($data['base_salary'] / 100,2);
            //英文 钱字符串
            $data['en_money'] = num2Word($data['base_salary']);
            //泰文 钱
            $data['th_money'] = bahttext($data['base_salary']);
            //千分位
            $data['base_salary'] = number_format($data['base_salary']);
        }



        $data['en_date'] = $en_date;
        $data['en_hire_date'] = $hire_date;
        return $this->jsonReturn(self::checkReturn(array('data' => $data)));
    }


    //在职证明 操作发送接口
    public function on_job_downloadAction(){
        try{
            $param['staff_info_id'] = $this->userinfo['staff_id'];
            $param['type'] = 2;
            $param['date'] = array(date('Y-m-d',time()));
            $param['mail_type'] = empty($this->paramIn['mail_type']) ? 1 : intval($this->paramIn['mail_type']);

            $client = new ApiClient('hcm_rpc','','by_download',$this->lang);
            $client->setParams($param);
            $result = $client->execute();
            if (isset($result['result']['code'],$result['result']['data']) && $result['result']['code'] == 1) {
                return $this->jsonReturn(self::checkReturn(array('data' => $result['result']['data'] ?? [])));
            }
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }catch (\Exception $e){
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }
    }

    //工资证明
    public function payroll_downloadAction(){
        try{
            $param['staff_info_id'] = $this->userinfo['staff_id'];
            $param['type'] = 4;
            $param['date'] = array(date('Y-m-d',time()));
            $param['mail_type'] = empty($this->paramIn['mail_type']) ? 1 : intval($this->paramIn['mail_type']);

            $client = new ApiClient('hcm_rpc','','by_download',$this->lang);
            $client->setParams($param);
            $result = $client->execute();
            if (isset($result['result']['code'],$result['result']['data']) && $result['result']['code'] == 1) {
                return $this->jsonReturn(self::checkReturn(array('data' => $result['result']['data'] ?? [])));
            }
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }catch (\Exception $e){
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }

    }


}