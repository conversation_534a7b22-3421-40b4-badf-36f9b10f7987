<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Server\VehicleCompanyInfoServer;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class VehicleCompanyInfoController extends Controllers\ControllerBase
{

    /**
     * @param $name
     * @param $args
     * @return Response|ResponseInterface
     * @throws BusinessException
     */
    public function __call($name, $args)
    {
        $paramIn                  = $this->paramIn;
        $paramIn['store_id']      = $this->userinfo['organization_id'];
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $paramIn['staff_info']    = $this->userinfo;
        $server                   = new VehicleCompanyInfoServer($this->lang, $this->timezone);
        // 去掉 Action字样
        $method = $server->getArdApiModule() . '.' . substr($name, 0, -6);
        $result = $server->sendRequest($method, $paramIn);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }
}
