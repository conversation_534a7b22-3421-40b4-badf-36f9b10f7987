<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Controllers\ControllerBase;
use FlashExpress\bi\App\Server\DiscountServer;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\FreightDiscountServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use Exception;

class DiscountController extends ControllerBase
{
    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn)){
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 添加运费折扣申请
     */
    public function addAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['store_id']  = $this->userinfo['organization_type'] == 1 ? $this->userinfo['organization_id'] : '';

        try {
            //[2]参加校验
            //[2.1]校验基础数据 & 折扣数据
            $validations = [
                "costomer_id"     => "Required|Str",
                "costomer_name"   => "Required|Str",
                "costomer_mobile" => "Required|Str",
                "costomer_type"   => "Required|Int",
                "costomer_created_at" => "Required|Date",
                "costomer_parcel_count" => "Required|Int",
                "costomer_estimate_parcel_count" => "Required|IntGeLe:1,99999",
                "price_type"      => "Required|IntIn:1,2|>>>:". $this->getTranslation()->_('err_msg_speacial_price'),
                "apply_reason_type" => "Required",
            ];
            if (isset($paramIn['apply_reason_type']) && $paramIn['apply_reason_type'] == 6) { //如果是其他原因必传备注字段
                $validations = array_merge($validations, [
                    "remark"          => "Required|StrLenGeLe:0,1000|>>>:". $this->getTranslation()->_('5110'),
                ]);
            }

            if (isset($paramIn['price_rule_category'])) {
                $validations = array_merge($validations, [
                    "price_rule_category" => "Required|IntIn:0,1,2",
                    "current_disc"    => "Required|IntGeLe:0,100",
                    "request_disc"    => "Required|IntIn:0,5,10,15,20,25,30",
                    "valid_days"      => "Required|Int",
                ]);
            }
            $this->validateCheck($this->paramIn, $validations);

            //[2.2]校验优惠券数据
            if (isset($paramIn['coupon'])) {

                foreach ($paramIn['coupon'] as $coupon) {
                    $validations = [
                        "type"       => "Required|Int",
                        "name"       => "Required|Str",
                        "days_type"  => "Required|Int",
                        "valid_days" => "Required|Int",
                        "num"        => "Required|IntGeLe:1,100",
                    ];
                    $this->validateCheck($coupon, $validations);
                }
            }

            //[2.3]校验折扣 & 优惠券至少选择了一项
            if (empty($paramIn['price_rule_category']) && empty($paramIn['coupon'])) {
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
            }

            //[3]业务处理
            $returnArr = (new DiscountServer($this->lang , $this->timezone))->addFreightDisc($paramIn);

            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('Discount:add:' . $e->getMessage() . $e->getTraceAsString());
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 添加运费折扣申请
     */
    public function add_shopAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['store_id']  = $this->userinfo['organization_type'] == 1 ? $this->userinfo['organization_id'] : '';

        try {
            //[2]参加校验
            //[2.1]校验基础数据 & 折扣数据
            $validations = [
                "costomer_id"     => "Required|Str",
                "costomer_name"   => "Required|Str",
                "costomer_mobile" => "Required|Str",
                "costomer_type"   => "Required|Int",
                "costomer_created_at" => "Required|Date",
                "costomer_parcel_count" => "Str|>>>:" . 'costomer_parcel_count',
                "costomer_estimate_parcel_count" => "Required|IntGeLe:1,99999",
                "price_type"      => "Required|IntIn:1,2|>>>:". $this->getTranslation()->_('err_msg_speacial_price'),
                "apply_reason_type" => "Required",
                "discount_effective_date" => "Required|Date|>>>:" . $this->getTranslation()->_('enter_return_discount_date'),
            ];
            if (isset($paramIn['apply_reason_type']) && $paramIn['apply_reason_type'] == 6) { //如果是其他原因必传备注字段
                $validations = array_merge($validations, [
                    "remark"          => "Required|StrLenGeLe:0,1000|>>>:". $this->getTranslation()->_('5110'),
                ]);
            }

            if (isset($paramIn['price_rule_category']) && !empty($paramIn['price_rule_category'])
            ) {
                $validations = array_merge($validations, [
                    "price_rule_category" => "Required|IntIn:0,1,2",
                    "current_disc"    => "Str|>>>:" . 'current_disc',
                    "request_disc"    => "IntGeLe:0,50|>>>:". $this->getTranslation()->_('enter_return_rate'),
                ]);
                $this->validateCheck($this->paramIn, $validations);
            }

            if (isset($paramIn['request_cod_poundage_rate_str']) && !empty($paramIn['request_cod_poundage_rate_str'])) {
                $validations = array_merge($validations, [
                    "request_cod_poundage_rate_str" => "Str|>>>:" . $this->getTranslation()->_('enter_return_cod_rate'),//'请输入Cod折扣',
                ]);
                $this->validateCheck($this->paramIn, $validations);
            }

            if (isset($paramIn['request_credit_term']) && !empty($paramIn['request_credit_term'])) {
                $validations = array_merge($validations, [
                    "request_credit_term"           => "Str|>>>:" .  $this->getTranslation()->_('enter_credit_period'),//'请输入信用期限',
                ]);
                $this->validateCheck($this->paramIn, $validations);
            }
            if (isset($paramIn['request_return_discount_rate']) && !empty($paramIn['request_return_discount_rate'])) {
                $validations = array_merge($validations, [
                    "request_return_discount_rate"  => "Str|>>>:" . $this->getTranslation()->_('enter_return_rate'),//'请输入退件折扣',
                ]);
                $this->validateCheck($this->paramIn, $validations);
            }

            if (isset($paramIn['request_cod_poundage_rate_str']) && !empty($paramIn['request_cod_poundage_rate_str']) ||
                isset($paramIn['price_rule_category']) && !empty($paramIn['price_rule_category']) ||
                isset($paramIn['request_return_discount_rate']) && !empty($paramIn['request_return_discount_rate'])
            ) {
                $validations = array_merge($validations, [
                    "valid_month"     => "Required|IntGeLe:1,12|>>>:" . $this->getTranslation()->_('enter_valid_data'),//'请输入有效日期',
                ]);
                $this->validateCheck($this->paramIn, $validations);
            }

            //[2.2]校验优惠券数据
            //if (isset($paramIn['coupon'])) {
            //
            //    foreach ($paramIn['coupon'] as $coupon) {
            //        $validations = [
            //            "type"       => "Required|Int",
            //            "name"       => "Required|Str",
            //            "days_type"  => "Required|Int",
            //            "valid_days" => "Required|Int",
            //            "num"        => "Required|IntGeLe:1,100",
            //        ];
            //        $this->validateCheck($coupon, $validations);
            //    }
            //}
            //[2.3]校验折扣 & 优惠券至少选择了一项
            if (empty($paramIn['price_rule_category']) &&
                empty($paramIn['coupon']) &&
                empty($paramIn['request_cod_poundage_rate_str']) &&
                empty($paramIn['request_return_discount_rate']) &&
                empty($paramIn['request_credit_term'])
            ) {
                return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
            }

            $this->validateCheck($this->paramIn, $validations, true);

            $paramIn['channel'] = 2;

            //[3]业务处理
            $returnArr = (new FreightDiscountServer($this->lang , $this->timezone))->addFreightDiscount($paramIn);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('Discount:add:' . $e->getMessage());
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 添加运费折扣申请
     */
    public function add_sales_pmdAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['store_id']  = $this->userinfo['organization_type'] == 1 ? $this->userinfo['organization_id'] : '';

        try {
            //[2]参加校验
            //[2.1]校验基础数据 & 折扣数据
            $validations = [
                "costomer_id"     => "Required|Str",
                "costomer_name"   => "Required|Str",
                "costomer_mobile" => "Required|Str",
                "costomer_type"   => "Required|Int",
                "costomer_created_at" => "Required|Date",
                "costomer_parcel_count" => "Str|>>>:" . 'costomer_parcel_count',
                "costomer_estimate_parcel_count" => "Required|IntGeLe:1,99999",
                "price_type"      => "Required|IntIn:1,2|>>>:". $this->getTranslation()->_('err_msg_speacial_price'),
                "apply_reason_type" => "Required",
                "discount_effective_date" => "Required|Date|>>>:" . $this->getTranslation()->_('enter_return_discount_date'),
            ];
            if (isset($paramIn['apply_reason_type']) && $paramIn['apply_reason_type'] == 6) { //如果是其他原因必传备注字段
                $validations = array_merge($validations, [
                    "remark"          => "Required|StrLenGeLe:0,1000|>>>:". $this->getTranslation()->_('5110'),
                ]);
            }

            if (isset($paramIn['price_rule_category']) && !empty($paramIn['price_rule_category'])
            ) {
                $validations = array_merge($validations, [
                    "price_rule_category" => "Required|IntIn:0,1,2",
                    "current_disc"    => "Str|>>>:" . 'current_disc',
                    "request_disc"    => "IntGeLe:0,50|>>>:". $this->getTranslation()->_('enter_return_rate'),//'请输入折扣',
                ]);
                $this->validateCheck($this->paramIn, $validations);
            }

            if (isset($paramIn['request_cod_poundage_rate_str']) && !empty($paramIn['request_cod_poundage_rate_str'])) {
                $validations = array_merge($validations, [
                    "request_cod_poundage_rate_str" => "Str|>>>:" . $this->getTranslation()->_('enter_return_cod_rate'),//'请输入Cod折扣',
                ]);
                $this->validateCheck($this->paramIn, $validations);
            }

            if (isset($paramIn['request_credit_term']) && !empty($paramIn['request_credit_term'])) {
                $validations = array_merge($validations, [
                    "request_credit_term"           => "Str|>>>:" . $this->getTranslation()->_('enter_credit_period'),//'请输入信用期限',
                ]);
                $this->validateCheck($this->paramIn, $validations);
            }
            if (isset($paramIn['request_return_discount_rate']) && !empty($paramIn['request_return_discount_rate'])) {
                $validations = array_merge($validations, [
                    "request_return_discount_rate"  => "Str|>>>:" . $this->getTranslation()->_('enter_return_rate'),//'请输入退件折扣',
                ]);
                $this->validateCheck($this->paramIn, $validations);
            }

            if (isset($paramIn['request_cod_poundage_rate_str']) && !empty($paramIn['request_cod_poundage_rate_str']) ||
                isset($paramIn['price_rule_category']) && !empty($paramIn['price_rule_category']) ||
                isset($paramIn['request_return_discount_rate']) && !empty($paramIn['request_return_discount_rate'])
            ) {
                $validations = array_merge($validations, [
                    "valid_month"     => "Required|IntGeLe:1,12|>>>:" . $this->getTranslation()->_('enter_valid_data'),//'请输入有效日期',
                ]);
                $this->validateCheck($this->paramIn, $validations);
            }

            //[2.2]校验优惠券数据
            //if (isset($paramIn['coupon'])) {
            //
            //    foreach ($paramIn['coupon'] as $coupon) {
            //        $validations = [
            //            "type"       => "Required|Int",
            //            "name"       => "Required|Str",
            //            "days_type"  => "Required|Int",
            //            "valid_days" => "Required|Int",
            //            "num"        => "Required|IntGeLe:1,100",
            //        ];
            //        $this->validateCheck($coupon, $validations);
            //    }
            //}

            //[2.3]校验折扣 & 优惠券至少选择了一项
            if (empty($paramIn['price_rule_category']) &&
                empty($paramIn['coupon']) &&
                (empty($paramIn['request_cod_poundage_rate_str']) && $paramIn['request_cod_poundage_rate_str'] !== "0") &&
                empty($paramIn['request_return_discount_rate']) &&
                empty($paramIn['request_credit_term'])
            ) {
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
            }

            $paramIn['channel'] = 1;
            //[3]业务处理
            $returnArr = (new FreightDiscountServer($this->lang , $this->timezone))->addFreightDiscount($paramIn);

            //[4]数据返回
            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('Discount:add:' . $e->getMessage());
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 审批运费申请
     */
    public function updateAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['staff_name']= $this->userinfo['name'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['userinfo']  = $this->userinfo;

        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $reids_key = 'lock_updateFreightDisc_' . $paramIn['audit_id'];
        try {
            //申请操作
            $returnArr = $this->atomicLock(function () use ($paramIn) {
                return (new DiscountServer($this->lang , $this->timezone))->updateFreightDisc($paramIn);
            }, $reids_key, 5);
            if ($returnArr === false) { //没有获取到锁
                return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
            }
            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 审批运费申请
     */
    public function update_shopAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['staff_name']= $this->userinfo['name'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['userinfo']  = $this->userinfo;

        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",
            "reason"        => "StrLenGeLe:0,500",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $reids_key = 'lock_updateFreightDiscShop_' . $paramIn['audit_id'];
        try {

            $returnArr = $this->atomicLock(function () use ($paramIn) {
                return  (new FreightDiscountServer($this->lang , $this->timezone))->updateFreightDisc($paramIn);
            }, $reids_key, 5);

            if ($returnArr === false) { //没有获取到锁
                return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
            }

            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 审批运费申请
     */
    public function update_sales_pmdAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['staff_name']= $this->userinfo['name'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['userinfo']  = $this->userinfo;

        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",
            "reason"        => "StrLenGeLe:0,500",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $reids_key = 'lock_updateFreightDiscSalesAndPMD_' . $paramIn['audit_id'];
        try {
            $returnArr = $this->atomicLock(function () use ($paramIn) {
                return  (new FreightDiscountServer($this->lang , $this->timezone))->updateFreightDisc($paramIn);
            }, $reids_key, 10);

            if ($returnArr === false) { //没有获取到锁
                return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
            }

            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 查询客户
     */
    public function findCustomerAction()
    {
        //[1]入参校验
        $paramIn = $this->paramIn;

        //[2]数据验证
        $validations = [
            "condition" => "Required|StrLenGeLe:1,80",
        ];
        $this->validateCheck($paramIn, $validations);

        try {
            //请求客户列表
            $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.DiscountApplySvc','getDiscountApplyUser', $this->lang);
            $fle_rpc->setParams($paramIn['condition']);
            $fle_return = $fle_rpc->execute();

            if (isset($fle_return['result']) && empty($fle_return['result'])) { //没有查到客户信息
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_no_costomer')));
            }

            $translations = $this->getTranslation();
            foreach ($fle_return['result'] as $K => $item) {
                $fle_return['result'][$K]['price_type_text'] = $translations->_('price_type_' . $item['price_type']) ?? '';
                $fle_return['result'][$K]['created_at'] = isset($item['created_at']) ? date("Y-m-d", $item['created_at']) : '';
            }

        } catch (\Exception $e) {
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        $this->jsonReturn(self::checkReturn(['data' => ['dataList' => $fle_return['result']]]));
    }

    /**
     * 查询客户列表[使用Shop | Sales/PMD 部门]
     */
    public function findOtherCustomerListAction()
    {
        //[1]入参校验
        $paramIn    = $this->paramIn;
        $staffId    = $this->userinfo['staff_id'];

        //[2]数据验证
        $validations = [
            "condition" => "Required|StrLenGeLe:1,80",
        ];
        $this->validateCheck($paramIn, $validations);

        try {
            //请求客户列表
            $server = new DiscountServer($this->lang,$this->timezone);
            $list   = $server->getCustomList($staffId, $paramIn['condition']);

            if (!empty($list)) {
                $list = array_filter($list, function ($v) {
                    return isset($v['customer_type_category']) && $v['customer_type_category'] == 2; //KA客户
                });
                if (empty($list)) { //没有KA客户返回提示
                    $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_no_costomer')));
                }
            }

            $translations = $this->getTranslation();
            foreach ($list as $K => $item) {
                $list[$K]['price_type_text'] = $translations->_('price_type_' . $item['price_type']) ?? '';
                $list[$K]['created_at'] = isset($item['created_at']) ? date("Y-m-d", $item['created_at']) : '';
            }

            //[3]数据返回
            $this->jsonReturn(self::checkReturn(['data' => ['dataList' => $list]]));
        } catch (\Exception $e) {
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取客户详情
     */
    public function checkCostomerAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];

        //[2]数据验证
        $validations = [
            "costomer_id" => "Required",
            "customer_type_category" => "Required",
        ];
        $this->validateCheck($paramIn, $validations);

        try {
            //获取登录人所在网点对应的城市code
            $storeInfo = (new SysStoreServer())->getStoreByid($paramIn['organization_id']);
            if (empty($storeInfo) || $paramIn['organization_type'] == 2 || isset($storeInfo['city_code']) && empty($storeInfo['city_code'])) {
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
            }

            $params = [
                'client_id'   => $paramIn['costomer_id'],
                'customer_type_category' => $paramIn['customer_type_category'],
                'store_id'      => $storeInfo['id'],
            ];

            //发送Rpc请求-获取派件个数
            $fle_rpc = (new ApiClient('fle','com.flashexpress.fle.svc.api.DiscountApplySvc','canDiscountApply', $this->lang));
            $fle_rpc->setParams($params);
            $fle_return = $fle_rpc->execute();
            if (isset($fle_return['result']) && empty($fle_return['result'])) { //不在本地发货，不能申请
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_cant_request')));
            }

        } catch (\Exception $e) {
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        $this->jsonReturn(self::checkReturn([]));
    }

    /**
     * 获取优惠券配置
     */
    public function getCouponConfigAction()
    {
        try {
            $paramIn             = $this->paramIn;

            //[2]数据验证
            $validations = [
                "staff_info_id" => "Required",
            ];
            $this->validateCheck($paramIn, $validations);

            $fdServer = new DiscountServer($this->lang , $this->timezone);
            $data = $fdServer->getCouponInit($paramIn);

            $this->jsonReturn(self::checkReturn(['data' => $data ?? []]));
        } catch (\Exception $e) {
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    public function testAction()
    {
        $paramIn = $this->paramIn;
        $repo    = new \FlashExpress\bi\App\Repository\DiscountRepository();
        $data    = $repo->getPerDayParcelCount($paramIn['client_id']);

        var_dump($data);die;
    }
}