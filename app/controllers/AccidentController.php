<?php
namespace FlashExpress\bi\App\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use  FlashExpress\bi\App\Server;
use WebGeeker\Validation\Validation;

class AccidentController extends Controllers\ControllerBase{

    public function initialize(){
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn)){
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct(){
        parent::onConstruct();
    }

    /**
     * Notes: 获取紧急呼叫电话
     * User: TB
     * Date: 2021/6/26
     * Time: 13:41
     */
    public function emergencyCallAction(){
        return $this->jsonReturn(self::checkReturn(array('data' => (new Server\AccidentServer($this->lang))->getEmergencyCall())));
    }

    /**
     * Notes: 获取初始化数据
     * User: TB
     * Date: 2021/6/26
     * Time: 13:41
     */
    public function initializeDataAction(){
        return $this->jsonReturn(self::checkReturn(array('data' => (new Server\AccidentServer($this->lang))->getInitializeData($this->userinfo))));
    }

    /**
     * Notes: 提交事故上报
     * User: TB
     * Date: 2021/6/26
     * Time: 13:43
     */
    public function accidentReportAction(){
        try {
            $t = $this->getTranslation();
            $p = $this->checkData();//校验参数
            $s = new Server\AccidentServer($this->lang);
            $mainTask = [
                'occurrence_time'   => $p['accident_time'],//事故发生时间
                'report_store_id'   => $this->userinfo['organization_id'],//上报网点id
                'report_store_name' => $this->userinfo['organization_name'],//上报网点名称
                'report_staff_id'   => $this->userinfo['staff_id'],//上报员工工号
                'report_staff_name' => $this->userinfo['name'],//上报员工姓名
                'relevant_store'    => implode(',', array_unique($p['accident_network'])),// 事故相关网点
                'influence_scope'   => implode(',', array_unique($p['accident_area'])),//事故影响范围
                'loss_type'         => implode(',', array_unique($p['accident_type'])),//事故损失类型
                'suspect_staff_id'  => implode(',', array_unique($p['suspect_id'])),//嫌疑人工号
                'report_desc'       => $p['details_description'],//详细描述
                'num'               => $p['accident_number'] ?: NULL,//受影响的包裹数量
                'assets'            => $p['amount_of_assets'] ?: NULL,//受影响的资产/公款额度
                'lazada'            => $p['isLazada'] ?: NULL,//是否涉及LAZADA
                'shopee'            => $p['isShopee'] ?: NULL,//是否涉及Shopee
                'outsource'         => $p['isOutsource'] ?: NULL,//是否涉及外协
                'lh'                => $p['isLH'] ?: NULL,//是否涉及LH
                'attachments'       => json_encode($p['imgs']),//附件
                'created_at'        => date('Y-m-d H:i:s'),//上报时间
            ];

            $db = $this->getDI()->get('db');
            $db->begin();//开启事物
            $res = $db->insertAsDict('accident_report', $mainTask);
            $pid = $db->lastInsertId();
            if (!$res) {
                $db->rollback();
                throw new Exception($t->_('accident_report_err_1'));
            }

            $subTask = [];
            foreach ($p['accident_type'] as $v) {
                $subTaskConfig = $s->getSubTaskConfig($v);
                if (empty($subTaskConfig)){
                    $db->rollback();
                    throw new Exception($t->_('accident_report_err_2'));
                }
                foreach ($subTaskConfig as $vv){
                    $subTask[] = [
                        'pid'       =>  $pid,
                        'items'     =>  json_encode($vv['items']),
                        'acceptor'  =>  $vv['acceptor'],
                        'ccto'      =>  $vv['ccto'],
                        'created_at'=>  date('Y-m-d H:i:s'),
                        'updated_at'=>  date('Y-m-d H:i:s'),
                    ];
                }
            }

            if (empty($subTask)){
                $db->rollback();
                throw new Exception($t->_('accident_report_err_3'));
            }

            foreach ($subTask as $v){
                $res = $db->insertAsDict('accident_report_sub', $v);
                if (!$res){
                    $db->rollback();
                    throw new Exception($t->_('accident_report_err_4'));
                }
            }

            $db->commit();

            return $this->jsonReturn(self::checkReturn(array('data' => '')));
        } catch(\Exception $e) {
            $this->getDI()->get("logger")->write_log($e->getMessage(), "error");
            return $this->jsonReturn(self::checkReturn(array('code'=>-1,'msg' => $t->_('accident_report_err_4'))));
        }
    }

    /**
     * Notes: 事故上报参数校验
     * User: TB
     * Date: 2021/6/26
     * Time: 13:43
     * @param $paramIn
     */
    public function checkData(){
        try {
            $t = $this->getTranslation();
            $validations = [
                'accident_time'     =>  "Required|DateTime|DateTimeTo:".date("Y-m-d H:i:00")."|>>>:".$t->_('accident_report_validation_err_1'),
                'accident_network'  =>  "Required|Arr|ArrLenLe:5|>>>:".$t->_('accident_report_validation_err_2'),
                'accident_area'     =>  "Required|Arr|ArrLenGe:1|>>>:".$t->_('accident_report_validation_err_3'),
                'accident_type'     =>  "Required|Arr|ArrLenGe:1|>>>:".$t->_('accident_report_validation_err_4'),
//                'accident_number'   =>  "Int|>>>:请选择受影响的包裹数量",
//                'amount_of_assets'  =>  "Int|>>>:请选择受影响的资产/公款额",
//                'isLazada'          =>  "Int|>>>:请选择是否涉及Lazada",
//                'isShopee'          =>  "Int|>>>:请选择是否涉及Shopee",
//                'isOutsource'       =>  "Int|>>>:请选择是否涉及外协",
//                'isLH'              =>  "Int|>>>:请选择是否涉及LH",
                'suspect_id'        =>  "Arr|ArrLenLe:5|>>>:".$t->_('accident_report_validation_err_5'),
                'details_description'=> "Required|Str|StrLenLe:1000|>>>:".$t->_('accident_report_validation_err_6'),
                'imgs'              =>  "Required|Arr|ArrLenLe:5|>>>:".$t->_('accident_report_validation_err_7'),
            ];
            Validation::validate($this->paramIn, $validations);

            $server = new Server\AccidentServer($this->lang);
            $influence_scope = array_column($server->getInitializeData($this->userinfo)['influence_scope'],null,'value');

            //校验事故影响范围
            $this->paramIn['accident_area'] = array_unique($this->paramIn['accident_area']);
            foreach ($this->paramIn['accident_area'] as $v){
                if (!isset($influence_scope[$v])){
                    throw new Exception($t->_('accident_report_validation_err_8'));
                }
                $loss_type = array_merge($loss_type ?? [], array_column($influence_scope[$v]['loss_type'],'value'));
            }

            //校验事故损失类型
            $this->paramIn['accident_type'] = array_unique($this->paramIn['accident_type']);
            foreach ($this->paramIn['accident_type'] as $v){
                if (!in_array($v,$loss_type)){
                    throw new Exception($t->_('accident_report_validation_err_9'));
                }
            }

            return $this->paramIn;

        } catch(\Exception $e) {
            return $this->jsonReturn(self::checkReturn(array('code'=>-1,'msg' => $e->getMessage())));
        }
    }

    /**
     * Notes: 事故上报调用BI-svc
     * User: TB
     * Date: 2023/6/13
     * Time: 16:52
     * @throws \Exception
     */
    public function accidentForBiAction(){
        $param = $this->paramIn;
        $param['userInfo'] = $this->userinfo;

        $t = $this->getTranslation();
        $actions = [
            'getInitData',
            'report',
        ];
        $validations = [
//            'action'    =>  "Required|StrIn:" . implode(',',$actions) . "|>>>:".$t->_('miss_args'),
        ];
        Validation::validate($param, $validations);
        $action = $param['action'];
        $ac = new ApiClient('bi_rpcv2', '', "accident.$action", $this->lang);
        $ac->setParams($param);
        $acResult = $ac->execute();
        if (isset($acResult['result'])){
            $this->jsonReturn(self::checkReturn($acResult['result']));
        }else{
            $this->getDI()->get('logger')->write_log("事故上报BI-svc调用失败 参数:".json_encode($param).";结果:".json_encode($acResult));
            $this->jsonReturn(self::checkReturn(['code'=>-1,'msg'=>$t->_('server_error')]));
        }
    }
}