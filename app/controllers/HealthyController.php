<?php
namespace FlashExpress\bi\App\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\K8sServer;

class HealthyController extends Controllers\ControllerBase
{

    public function initialize()
    {

    }

    /**
     * 应用健康检查
     */
    public function indexAction(){
       return $this->returnJson(1,'ok',null);
    }

    /**
     * 服务健康检查
     */
    public function checkServerAction()
    {
        try{
            (new K8sServer())->checkServerOnline();
            return $this->returnJson(1,'ok');
        }catch (Exception $ex){
            return $this->returnJson(0,$ex->getMessage());
        }
    }

}
