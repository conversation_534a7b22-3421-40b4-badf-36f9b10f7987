<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\bi\HrStaffItemsModel;// 这里暂时不能动，需要同步数据
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Server\OvertimeExtendServer;
use FlashExpress\bi\App\Server\OvertimeServer;
use Exception;


class OvertimeController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode($this->request->getRawBody(),true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
        $this->url_log($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 新建加班
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function addOvertimeAction()
    {

        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] = $this->userinfo;
        $validations         = [
            "staff_id"  => "Required|Int",
            "reason"    => "Required|StrLenGeLe:1,1000|>>>:" . $this->getTranslation()->_('5110'),
        ];
        $this->validateCheck($paramIn, $validations);
        $returnArr = (new OvertimeServer($this->lang, $this->timezone, $this->userinfo))->setLockConf(60,true)->addOvertimeV3UseLock($paramIn);
        $this->jsonReturn($returnArr);
    }

    /**
     * 审批同意 和驳回 加班
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function updateOvertimeAction()
    {

        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations         = [
            "staff_id"           => "Required|Int",
            "audit_id"           => "Required|Int",
            "reject_reason"      => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1020'),
        ];
        $this->validateCheck($paramIn, $validations);
        $server = new OvertimeServer($this->lang,$this->timezone,$this->userinfo);
        $server = Tools::reBuildCountryInstance($server,[$this->lang,$this->timezone]);
        $returnArr = $server->setLockConf(60,true)->updateOvertimeV3UseLock($paramIn);
        $this->jsonReturn($returnArr);

    }

    /**
     * 获取加班类型
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getTypeOvertimeAction()
    {
        try {
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $paramIn['job_title']= $this->userinfo['job_title'];//id
            $paramIn['organization_type']= $this->userinfo['organization_type'];
            $paramIn['organization_id']= $this->userinfo['organization_id'];
            $paramIn['department_id']= $this->userinfo['department_id'];//fle 部门为子部门id 对应 hr系统 node_department_id
            $returnArr = (new OvertimeServer($this->lang, $this->timezone, $this->userinfo))->getTypeOvertime($paramIn,$this->userinfo);

            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('getTypeOvertime:'. $e->getMessage(), 'notice');
            } else {
                $this->getDI()->get('logger')->write_log('getTypeOvertime:'. $e->getMessage(), 'info');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 取消加班相关申请 逻辑等同于请假申请撤销
     */
    public function cancelOvertimeAction()
    {
        try {
            $paramIn = $this->paramIn;
            if(empty($paramIn['type'])){
                $this->jsonReturn($this->checkReturn('-1', 'type'));
            }
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $validations         = [
                "staff_id"    => "Required|Int",
                "type"        => "Required|Int",
                'audit_id'    => "Required|Int",
            ];
            $this->validateCheck($paramIn, $validations);

            //[2]业务处理
            $server = new OvertimeServer($this->lang,$this->timezone,$this->userinfo);
            $returnArr = $server->setLockConf(10)->cancelV3UseLock($paramIn);

            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('cancelOvertime:'. $e->getMessage(), 'notice');
            } else {
                $this->getDI()->get('logger')->write_log('cancelOvertime:'. $e->getMessage(), 'info');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 是否第一次打开ot申请页面
     */
    public function isReadRuleAction()
    {
        $isRead = (new OvertimeServer($this->lang, $this->timezone, $this->userinfo))->isReadRule();
        $this->jsonReturn($this->checkReturn(['data' => ['is_read' => $isRead]]));
    }

    /**
     * 加班规则弹窗 提示
     */
    public function otRuleConfirmAction(){
        $param['user_info'] = $this->userinfo;
        $text = (new OvertimeServer($this->lang, $this->timezone))->getOtRuleText($param);
        $this->jsonReturn($this->checkReturn(['data' => $text]));
    }


    public function calculateAction(){
        $param = $this->paramIn;
        $param['user_info'] = $this->userinfo;
        $server = new OvertimeExtendServer($this->lang,$this->timezone);
        $data = $server->calculateForSalary($param);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }



}
