<?php

namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\MessageModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\Message\AttendanceMessageRemindTableMessage;
use FlashExpress\bi\App\Server\MessagePdfServer;
use FlashExpress\bi\App\Server\AgreementSignRecordServer;
use FlashExpress\bi\App\Server\AssetServer;
use FlashExpress\bi\App\Server\Osm\PunishServer;
use FlashExpress\bi\App\Server\ProbationServer;
use FlashExpress\bi\App\Server\ProbationTargetServer;
use FlashExpress\bi\App\Server\ResignServer;
use FlashExpress\bi\App\Server\MessageServer;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\ResumeRecommendServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Server\MessageIdentityDetailServer;
use GuzzleHttp\Exception\GuzzleException;

class BackyardController extends Controllers\ControllerBase
{
    public    $staff_id;
    public    $miss_args;
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $userinfo        = $this->userinfo;
        $this->staff_id  = $userinfo['id'];
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * CEO信箱 的列表
     * @param string $page 页码
     * @return false|string|void
     */
    public function mail_listAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        try {
            $model  = new BackyardServer($this->lang, $this->timezone);
            $return = $model->get_mail_list($paramIn);
            $this->jsonReturn($return);
        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("backyardController:mail_list-".$e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

    /**
     * CEO信箱 mail_id 的详情
     * @param string $mail_id 提问id
     * @return false|string|void
     */
    public function mail_detailAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $mail_id             = $paramIn['mail_id'];

        if (empty($mail_id)) {
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
        }
        try {
            $model = new BackyardServer($this->lang, $this->timezone);
            $data  = $model->get_mail_detail($paramIn);
            $this->jsonReturn($data);
        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("backyardController:mail_detail-".$e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

    /**
     * CEO信箱 写信
     * @param string $staff_id 用户id
     * @param string $content 内容
     * @param string $type 问题类型
     * @param string $img_json json格式
     * @return false|string|void
     */
    public function mail_insertAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['user_info'] = $this->userinfo;

        try {
            $model = new BackyardServer($this->lang, $this->timezone);
            $data  = $model->add_ceo_mail($paramIn);
            $this->jsonReturn($data);
        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("backyardController:mail_insert-".$e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

    /**
     * 问题类型:1=建议；2=工资问题；3=提成问题；4=投诉&举报；5=其他
     * @return false|string|void
     */
    public function type_listAction()
    {
        //$type_arr=[1=>'建议',2=>'工资问题',3=>'提成问题',4=>'投诉&举报',5=>'其他'];
        $type_arr = [
            1 => $this->getTranslation()->_('advice'),
            2 => $this->getTranslation()->_('salary'),
            3 => $this->getTranslation()->_('incentive'),
            4 => $this->getTranslation()->_('complaint'),
            5 => $this->getTranslation()->_('other'),
        ];
        echo json_encode(['code' => 1, 'msg' => 'ok', 'data' => $type_arr]);
        die;
    }

    /**
     * CEO信箱 \审批 未读合并
     * @return false|string|void
     */
    public function un_read_and_auditAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] = $this->userinfo;
        $validations         = [
            "staff_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        $server = new BackyardServer($this->lang, $this->timezone);
        $return = $server->getRedDotsNum($paramIn);
        $this->getDI()->get('logger')->write_log("un_read_and_audit_{$this->userinfo['staff_id']}: ".json_encode($return,
                JSON_UNESCAPED_UNICODE), 'info');
        $this->jsonReturn($this->checkReturn(['data' => $return]));
    }


    /**
     * 获取各个分类的未读消息数量
     */
    public function msg_unread_type_listAction()
    {
        $param['staff_id'] = $this->userinfo['id'];
        $server            = new BackyardServer($this->lang, $this->timezone);
        $res               = $server->getStaffUnReadMessageTypeList($param);
        //return $this->jsonReturn($res);
        echo json_encode(['code' => 1, 'message' => 'ok', 'data' => $res]);
        die;
    }

    /**
     * backyard消息 的列表
     * @return false|string|void
     */
    public function msg_listAction()
    {
        $page = $this->request->get('p', 'trim');
        $type = $this->request->get('type', 'trim');
        if (empty($type)) {
            $type = 1;
        }
        if (empty($page)) {
            $page = 1;
        }

        $param['type']     = $type;
        $param['staff_id'] = $this->userinfo['id'];
        $param['page']     = $page;
        $server            = new BackyardServer($this->lang, $this->timezone);
        $res               = $server->msg_list($param);

        echo json_encode([
            'code'    => 1,
            'message' => 'ok',
            'data'    => ['last_page' => $res['last'], 'data' => $res['data']],
        ]);
        die;
    }


    /**
     * 消息详情---新版本
     * 消息分类  '消息类型 -1:未知类型 0: 普通消息 1: 平台收件派单，2:同事收件转单，3:同事派件转单，4:平台取消订单，5:快递员奖罚通知，6:问卷类型 7:警告书 8:工资相关 9:处罚相关 10:hr系统 11: 审批消息 12: 个人行为整改书
     * 41:简历详情，42: 菲律宾转正评估消息提醒，43: 马来转正通知消息，47: 马来转正评估通知 签字消息，44:资产盘点，50:KPI绩效员工确认消息分类'
     * @param string msg_id 消息id
     * @return false|string|void
     * @throws BusinessException
     */
    public function msg_detail_newAction()
    {
        $msg_id = $this->request->get('msg_id', 'trim');
        $is_know = $this->request->get('is_know', 'trim');
        if (empty($msg_id)) {
            $this->jsonReturn(['code' => 0, 'message' => $this->getTranslation()->_('miss_args'), 'data' => null]);
        }
        $param['msg_id']     = $msg_id;
        $param['is_know']    = $is_know;
        $param['staff_id']   = $this->userinfo['id'];
        $param['staff_name'] = $this->userinfo['name'];

        $server              = new BackyardServer($this->lang, $this->timezone);
        $data   = $server->getMessageDetailV2($param);
        $this->jsonReturn(['code' => 1, 'message' => 'ok', 'data' => $data]);
    }


    //返回最新一条未读消息 id 和 是否存在 下一条 未读
    public function get_new_msgAction()
    {
        $param['staff_id']        = $this->userinfo['id'];
        $param['request_channel'] = 'off_work_call';
        $server                   = Tools::reBuildCountryInstance(new BackyardServer($this->lang, $this->timezone),
            [$this->lang, $this->timezone]);
        // 本次访问，更新缓存
        $info = $server->punch_out_msg($param);
        if (empty($info)) {
            return $this->jsonReturn(['code' => 1, 'message' => 'ok', 'data' => ['msg_id' => null, 'exist' => 0]]);
        }
        //如果 存在2条未读 $exist 参数为真
        $exist = 0;
        if (count($info) > 1) {
            $exist = 1;
        }
        $this->jsonReturn([
            'code'    => 1,
            'message' => 'ok',
            'data'    => ['msg_id' => $info[0] , 'exist' => $exist],
        ]);
    }


    //警告书签名保存接口
    public function sign_warningAction()
    {
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $validations = [
            "msg_id" => "Required|StrLenGeLe:1,100",
            "url"    => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        //[2]业务处理
        $returnArr = $this->atomicLock(function () use ($param) {
            $message_server = new MessageServer($this->lang,$this->timezone);
            $message_server->sign_for_warning($param);
        }, 'sign_warning_msg_id_' . $param['msg_id']);
        if ($returnArr === false) { //没有获取到锁
            return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
        }
        //[3]数据返回
        $this->jsonReturn($this->checkReturn(1));
    }

    /**
     * @return void
     * @throws ValidationException
     * @throws \ReflectionException
     */
    public function get_sign_warning_pdf_dataAction()
    {
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        //[2]业务处理
        $message_server = new MessageServer($this->lang, $this->timezone);
        $returnArr      = $message_server->get_sign_warning_pdf_data($param);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data'=>$returnArr]));
    }

    /**
     * 考勤消息提醒内容
     * @return void
     */
    public function get_attendance_remind_dataAction()
    {
        $param['msg_id']        = $this->request->get('msg_id', 'trim');
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $validations = [
            "msg_id" => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $message_server = new AttendanceMessageRemindTableMessage($this->lang, $this->timezone);
        $returnArr      = $message_server->getMessageDataContent($param);
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }
    /**
     * 警告书签字证人下拉列表
     * @return void
     * @throws Exception
     */
    public function sign_warning_witness_listAction()
    {
        $param                  = $this->paramIn;
        $validations         = [
            "msg_id" => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        //[2]业务处理
        $message_server = new MessageServer($this->lang, $this->timezone);
        $returnArr      = $message_server->sign_warning_witness_list($param);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data'=>$returnArr]));
    }

    /**
     *
     * 警告书 上级校验证人
     *
     *
     */
    public function warningWitnessAction()
    {
        $param          = $this->paramIn;
        $message_server = new MessageServer($this->lang, $this->timezone);
        if ($param['staff_id'] == $this->userinfo['id']) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('witness_no_self')));
        }
        $returnArr = $message_server->witenessInfo($param['msg_id'], $param['staff_id']);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    // 问卷消息 - 详情读取
    public function questionnaire_detailAction()
    {
        $msg_id = $this->request->get('msg_id', 'trim');
        if (empty($msg_id)) {
            $this->jsonReturn(['code' => 0, 'message' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }
        try {
            $param['msg_id']        = $msg_id;
            $param['staff_info_id'] = $this->userinfo['staff_id'];

            //查看支援信息
            //如果是支援账号，则找到其 主账号
            $supportStaffInfo = (new AttendanceRepository($this->lang,
                $this->timezone))->getSupportInfoBySubStaff($param['staff_info_id']);
            if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
                $param['staff_info_id'] = $supportStaffInfo['staff_info_id'];
            }

            $ac                     = new ApiClient('hcm_rpc', '', 'questionnaire_detail');
            $ac->setParams($param);
            $ac_result = $ac->execute();
            $this->wLog('backyard: questionnaire_detail rpc result - ',
                json_encode(['params' => $param, 'result' => $ac_result], JSON_UNESCAPED_UNICODE), 'backyard_server');
            if ($ac_result["result"]['code'] == 1) {
                $this->jsonReturn(['code' => 1, 'message' => 'ok', 'data' => $ac_result["result"]['data']]);
            }
            return $this->jsonReturn(['code' => 0, 'message' => $ac_result["result"]['message'], 'data' => []]);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("backyardController: questionnaire_detail - ".$e->getMessage());
            return $this->jsonReturn(['code' => -1, 'message' => 'read fail', 'data' => []]);
        }
    }

    // 问卷消息 - 结果提交
    public function questionnaire_submitAction()
    {
        $param['msg_id']        = $this->paramIn['msg_id'];
        $param['answer']        = $this->paramIn['answer'];
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        //查看支援信息
        //如果是支援账号，则找到其 主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($param['staff_info_id']);
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            $param['staff_info_id'] = $supportStaffInfo['staff_info_id'];
        }

        if (empty($param['msg_id']) || empty($param['answer'])) {
            $this->jsonReturn(['code' => 0, 'message' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }

        // 验证answer的json格式
        if (is_array($param['answer'])) {
            $param['answer'] = json_encode($param['answer'], JSON_UNESCAPED_UNICODE);
        } else {
            json_decode($param['answer']);
            if (json_last_error() != JSON_ERROR_NONE) {
                $this->jsonReturn(['code' => 0, 'message' => $this->getTranslation()->_('miss_args'), 'data' => []]);
            }
        }
        try {
            $ac = new ApiClient('hcm_rpc', '', 'questionnaire_submit');
            $ac->setParams($param);
            $ac_result = $ac->execute();
            $this->wLog('backyard: questionnaire_submit rpc result - ',
                json_encode(['params' => $param, 'result' => $ac_result], JSON_UNESCAPED_UNICODE), 'backyard_server');
            if ($ac_result["result"]['code'] == 1 && $ac_result["result"]['data']) {
                // 消息处理成功后，写入队列
                $server    = new BackyardServer($this->lang, $this->timezone);
                $mnsResult = $server->addHaveReadMsgToMns($ac_result["result"]['data']['staff_questionnaire_id']);
                $this->wLog('backyard: questionnaire_submit mns result - ', json_encode([
                    'params' => $ac_result["result"]['data']['staff_questionnaire_id'],
                    'result' => $mnsResult,
                ], JSON_UNESCAPED_UNICODE), 'backyard_server');
                return $this->jsonReturn(['code' => 1, 'message' => 'ok', 'data' => ['result' => 'success']]);
            }
            if ($ac_result["result"]['code'] == 1 && !$ac_result["result"]['data']) {
                return $this->jsonReturn(['code' => 0, 'message' => 'submit fail', 'data' => ['result' => 'fail']]);
            }
            return $this->jsonReturn([
                'code'    => 0,
                'message' => $ac_result["result"]['message'],
                'data'    => ['result' => 'fail'],
            ]);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("backyardController: questionnaire_submit - ".$e->getMessage());
            return $this->jsonReturn(['code' => -1, 'message' => 'submit fail', 'data' => []]);
        }
    }

    // 签字消息 - 签字图片提交
    public function sign_result_submitAction()
    {
        $msg_id                 = $this->paramIn['msg_id'];
        $sign_url               = $this->paramIn['sign_url'];
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        if (empty($msg_id) || empty($sign_url)) {
            $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }

        try {
            $param['msg_id'] = $msg_id;

            $server = new BackyardServer($this->lang, $this->timezone);

            // 验证该员工是否已提交签字
            $sign_msg = $server->get_staff_sign_msg($param);

            if (empty($sign_msg)) {
                return $this->jsonReturn(['code' => 0, 'msg' => '员工没有收到该签字消息', 'data' => []]);
            }

            //hcm发送的签字消息，需要看下是不是 子账号 进来的。
            if($sign_msg['category'] == MessageEnums::CATEGORY_SIGN && in_array($sign_msg['category_code'], [MessageEnums::CATEGORY_SIGN_CODE, MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE])) {
                //查看支援信息
                //如果是支援账号，则找到其 主账号
                $supportStaffInfo = (new AttendanceRepository($this->lang,
                    $this->timezone))->getSupportInfoBySubStaff($param['staff_info_id']);
                if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
                    $param['staff_info_id'] = $supportStaffInfo['staff_info_id'];
                }
            }

            if (($sign_msg['staff_info_id'] != $param['staff_info_id']) || $sign_msg['category'] != 33 || !$sign_msg['push_state']) {
                return $this->jsonReturn(['code' => 0, 'msg' => '员工没有收到该签字消息', 'data' => []]);
            }

            if ($sign_msg['read_state']) {
                //return $this->jsonReturn(['code' => 0, 'msg' => '该员工已签字', 'data' => []]);
            }

            // 签名提交
            $sign_param = [
                'staff_info_id'      => $param['staff_info_id'],
                'remote_message_id'  => $sign_msg['message_content_id'],
                'sign_url'           => $sign_url,
                'message_courier_id' => $sign_msg['id'],
                'category'           => $sign_msg['category'],
                'category_code'      => $sign_msg['category_code'],
                'msg_id'             => $msg_id,
            ];

            $result     = $server->add_sign($sign_param);
            if ($result) {
                // 消息处理成功后，写入队列
                $server->addHaveReadMsgToMns($sign_msg['id']);

                return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => ['result' => 'success']]);
            }

            return $this->jsonReturn(['code' => 0, 'msg' => 'submit fail', 'data' => ['result' => 'fail']]);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("backyardController: sign_result_submit - ".$e->getMessage());
            return $this->jsonReturn(['code' => -1, 'msg' => 'submit fail', 'data' => []]);
        }
    }

    /**
     * 离职资产消息详情
     * การยืนยันทรัพย์สินของพนักงานที่ลาออก
     * @return null
     */
    public function asset_msgAction()
    {
        $staff_id     = $this->paramIn['staff_id'];
        $useInfo      = (new StaffServer($this->lang, $this->timezone))->get_staff($staff_id);
        $useAssets    = (new AssetServer($this->lang, $this->timezone))->assetsByStaffId($staff_id);
        $resignDetail = (new ResignServer($this->lang, $this->timezone))->getResignByStaffId($staff_id);
        if (isset($resignDetail['leave_date']) && $resignDetail['leave_date']) {
            $resignDetail['leave_date'] = date('d/m/Y', strtotime($resignDetail['leave_date']));
        } else {
            if (isset($useInfo['data']["leave_date"]) && $useInfo['data']["leave_date"]) {
                $resignDetail['leave_date'] = date('d/m/Y', strtotime($useInfo['data']["leave_date"]));
            } else {
                $resignDetail['leave_date'] = '';
            }
        }
        $data = [
            "name"          => $useInfo['data']['name'],
            "staff_info_id" => $useInfo['data']["staff_info_id"],
            "job_name"      => $useInfo['data']["job_name"],
            "leavedate"     => $resignDetail['leave_date'],
            "useAssets"     => $useAssets,
        ];
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }

    // 获取签字消息内容和状态
    public function get_sign_msgAction()
    {
        $msg_id                 = $this->request->get('msg_id', 'trim');
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $add_hour               = $this->getDI()['config']['application']['add_hour'];

        if (empty($msg_id)) {
            $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }

        try {
            $param['msg_id'] = $msg_id;

            $server = new BackyardServer($this->lang, $this->timezone);

            // 验证该员工是否收到签字消息
            $sign_msg = $server->get_staff_sign_msg($param);

            if (empty($sign_msg)) {
                return $this->jsonReturn(['code' => 0, 'msg' => '员工没有收到该签字消息', 'data' => []]);
            }

            //hcm发送的签字消息，需要看下是不是 子账号 进来的。
            if($sign_msg['category'] == MessageEnums::CATEGORY_SIGN && in_array($sign_msg['category_code'], [MessageEnums::CATEGORY_SIGN_CODE, MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE])) {
                //查看支援信息
                //如果是支援账号，则找到其 主账号
                $supportStaffInfo = (new AttendanceRepository($this->lang,
                    $this->timezone))->getSupportInfoBySubStaff($param['staff_info_id']);
                if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
                    $param['staff_info_id'] = $supportStaffInfo['staff_info_id'];
                }
            }

            if (($sign_msg['staff_info_id'] != $param['staff_info_id']) || $sign_msg['category'] != 33 || !$sign_msg['push_state']) {
                return $this->jsonReturn(['code' => 0, 'msg' => '员工没有收到该签字消息', 'data' => []]);
            }

            // 获取签字消息内容 及 签字状态
            $msg_param    = [
                'remote_msg_id' => $sign_msg['message_content_id'],
            ];
            $sign_content = $server->get_msg_detail($msg_param);
            if (empty($sign_content)) {
                return $this->jsonReturn(['code' => 0, 'msg' => '消息内容不存在', 'data' => []]);
            }

            if($sign_content['send_type'] == 13) {//工具号查询员工信息
                $staff_base_info = $server->get_staff_tool_info($param['staff_info_id']);
            } else {
                // 获取员工姓名、职位
                $staff_base_info = $server->get_staff_info($param['staff_info_id']);
            }


            // 获取员工签字
            $data = [
                'title'      => $sign_content['title'],
                'content'    => $sign_content['content'],
                'staff_name' => $staff_base_info['name'] ?? '',     // 员工姓名
                'staff_id'   => $param['staff_info_id'],            // 工号
                'job_title'  => $staff_base_info['job_title'] ?? '',// 职位
                'hire_type'  => $staff_base_info['hire_type'] ?? '',// 职位
            ];

            $sign_result_param  = [
                'remote_message_id' => $sign_msg['message_content_id'],
                'staff_info_id'     => $param['staff_info_id'],
            ];
            $sign_result        = $server->get_sign_msg_result($sign_result_param);
            $data['sign_url']   = !empty($sign_result['sign_url']) ? $sign_result['sign_url'] : '';
            $data['sign_date']  = '';
            $data['sign_state'] = $sign_result ? "1" : "0";

            // 公历 转 泰国佛历年 格式: วันที่ 20 ตุลาคม พ.ศ.2563
            if (!empty($sign_result['created_at'])) {
                $sign_timestamp = strtotime($sign_result['created_at']) + $add_hour * 3600;
                if (isCountry('TH')) {
                    $th_year           = date('Y', $sign_timestamp) + 543;
                    $th_month          = StaffRepository::$month_th[date('n', $sign_timestamp)];
                    $data['sign_date'] = 'วันที่ '.date('j', $sign_timestamp).' '.$th_month.' พ.ศ.'.$th_year;
                } else {
                    $data['sign_date'] = date('Y.m.d', $sign_timestamp);
                }
            }

            // 签字消息 按照模板发送
            if ($sign_msg['category'] == MessageEnums::CATEGORY_SIGN && $sign_msg['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_TEMPLATE) {

                // 查询附件明细表数据
                $identityData = (new MessageIdentityDetailServer())->getIdentityDetailByParams([
                    "staff_info_id"     => $this->userinfo['id'],
                    "remote_message_id" => $sign_msg['message_content_id'],
                    "is_del"            => 0,
                ], ["staff_info_attribute", "id"]);
                if (empty($identityData) || !is_object($identityData)) {
                    // '查询失败！'
                    return $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('4104'), 'data' => []]);
                }

                $staffInfoAttributes = json_decode($identityData->staff_info_attribute, true);
//                $pattern             = array_keys($staffInfoAttributes);
//                array_walk($pattern, function (&$item, $key) {
//                    $item = '/{{'.trim($item).'}}/';
//                });

                preg_match_all("/\{\{\s*[A-Z]\s*\}\}/", $data['content'], $matches);
                $pattern = $matches[0];
                array_walk($pattern, function (&$item, $key) {
                    $item = '/'.trim($item).'/';
                });

                // 获取到模板的 变量 并 去掉 /{{}}/
                $replaceData = [];
                foreach ($pattern as $key => $val) {
                    $seat = trim($val, '/{{}}/');
                    // 按照模板变量的顺序 进行重序
                    $replaceData[] = $staffInfoAttributes[$seat];
                }

                // 替换后重新赋值
                $data['content'] = preg_replace($pattern, $replaceData, $data['content']);
            }

            //补充签字消息字段
            if (
                isCountry('MY')
                && $sign_msg['category'] == MessageEnums::CATEGORY_SIGN
                && $sign_msg['category_code'] == MessageEnums::CATEGORY_SIGN_CODE_AGREEMENT
            ) {
                $agreementSignRecordServer = Tools::reBuildCountryInstance(new AgreementSignRecordServer());
                $singInfo                  = $agreementSignRecordServer->getStaffSignInfoByMessageId($msg_id, $sign_result);
                $data                      = array_merge($data, $singInfo);
            }
            if (
                isCountry('MY')
                && $sign_msg['category'] == MessageEnums::CATEGORY_SIGN
                && in_array($sign_msg['category_code'],
                    [
                        MessageEnums::CATEGORY_SIGN_CODE_CONTRACT_EXPIRE,
                        MessageEnums::CATEGORY_SIGN_CODE_ASSET_INDEPENDENT,
                        MessageEnums::CATEGORY_SIGN_CODE_ASSET_CONTRACT_AGENT,
                        EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_PDPA'),
                        EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_CONTRACT_BEBAS'),
                    ]
                )
            ) {
                $messagePdfServer = Tools::reBuildCountryInstance(new MessagePdfServer());
                $singInfo                  = $messagePdfServer->getStaffSignInfoByMessageId($msg_id);
                $data                      = array_merge($data, $singInfo);
            }

            //dd($data);
            if (
                isCountry()
                && $sign_msg['category'] == MessageEnums::CATEGORY_SIGN
                && $sign_msg['category_code'] == EnumSingleton::getInstance()->getEnums('CATEGORY_SIGN_CODE_SCHOOL_CERTIFICATE_SEND')
            ) {
                $messagePdfServer = Tools::reBuildCountryInstance(new MessagePdfServer());
                $singInfo                  = $messagePdfServer->getSchoolSingInfo($data,$this->userinfo);
                $data                      = array_merge($data, $singInfo);
            }

            return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("backyardController: get_sign_msg - ".$e->getMessage());
            return $this->jsonReturn(['code' => -1, 'msg' => 'get sign msg fail', 'data' => []]);
        }
    }


    /**
     * 获取证书消息内容和状态
     * @return void
     * @throws GuzzleException
     */
    public function get_certificate_msgAction()
    {
        $msg_id                 = $this->request->get('msg_id', 'trim');
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        if (empty($msg_id)) {
            $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }
        try {
            $param['msg_id'] = $msg_id;
            $server          = new BackyardServer($this->lang, $this->timezone);
            $certificate     = $server->getMessageCourierDetail($param);
            if (empty($certificate)) {
                $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('data_error'), 'data' => []]);
            }
            $data = [
                'title'   => $certificate['title'],
                'content' => $certificate['content'],
            ];
            //按照模板发送
            if ($certificate['category'] == MessageEnums::MESSAGE_CATEGORY_CERTIFICATE
                && $certificate['category_code'] == MessageEnums::MESSAGE_CATEGORY_CERTIFICATE_CODE_TEMPLATE) {
                $data['content'] = (new MessageIdentityDetailServer())->makeTplSendMsgContent($this->userinfo['id'],
                    $certificate['message_content_id'], $data['content']);
            }
            $messagePdfServer = new MessagePdfServer();
            $staffInfo = $server->get_staff_info($param['staff_info_id']);
            $messageData = $server->get_msg_detail(['remote_msg_id' => $certificate['message_content_id']]);
            $extend = (array)json_decode($messageData['extend'],true);
            $data['certificate_img_url'] = $messagePdfServer->getCertificateImg($msg_id,$staffInfo,$extend);
            $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
        } catch (\Exception $e) {
            $this->logger->write_log("backyardController: get_sign_msg - " . $e->getMessage() . $e->getFile() . $e->getLine());
            $this->jsonReturn(['code' => -1, 'msg' => 'get certificate msg fail', 'data' => []]);
        }
    }
    /**
     *答题消息详情
     */
    public function exam_detailAction()
    {
        $msg_id                 = $this->request->get('msg_id', 'trim');
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $staff_id               = $this->userinfo['staff_id'];

        //查看支援信息
        //如果是支援账号，则找到其 主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($this->userinfo['staff_id']);
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            $param['staff_info_id'] = $supportStaffInfo['staff_info_id'];
            $staff_id = $supportStaffInfo['staff_info_id'];
        }

        if (empty($msg_id)) {
            $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }

        try {
            $redis = $this->getDI()->get('redisLib');
            $rkey  = $msg_id.$staff_id;
            $redis->delete($rkey);//释放锁

            $param['msg_id'] = $msg_id;
            $server          = new BackyardServer($this->lang, $this->timezone);

            // 验证该员工是否收到考试消息
            $sign_msg = $server->get_qa_exam_detail($param);

            if (empty($sign_msg)) {
                return $this->jsonReturn(['code' => 0, 'msg' => 'no exam msg 1', 'data' => []]);
            }

            // 获取签字消息内容 及 签字状态
            $msg_param    = [
                'remote_msg_id' => $msg_id,
                'staff_info_id' => $staff_id,
            ];
            $que_countent = $server->get_questionnaire_detail($msg_param);
            $sign_content = $server->get_question_courier_detail($msg_param);

            if (empty($sign_content)) {
                return $this->jsonReturn(['code' => 0, 'msg' => 'no msg', 'data' => []]);
            }

            //TODO题目存入reids
            $redis      = $this->getDI()->get('redisLib');
            $qa_key     = 'qalist:'.$msg_id.$staff_id;
            $sign_msg_c = $redis->get($qa_key);
            if (empty($sign_msg_c)) {
                $sign_msg_c = json_decode($sign_msg['question_content'], true);
                $redis->set($qa_key, serialize($sign_msg_c), 60 * 25);
            } else {
                $sign_msg_c = unserialize($sign_msg_c);
            }
            //去除答案
            foreach ($sign_msg_c['question'] as &$item) {
                $item['correct_answer'] = [];
                if (!$sign_content['submit_status']) {
                    //选项乱序 回答完了就不乱序了
                    // shuffle($item['content']);
                }
            }

            //查找该第几题了
            $question_list_keys = array_column($sign_msg_c['question'], 'question_uuid');
            if (!empty($sign_msg['answer_content'])) {
                $answer_content = json_decode($sign_msg['answer_content'], true);

                $answer_content_count = count($answer_content);
                $question_id          = $question_list_keys[$answer_content_count] ?? $question_list_keys[$answer_content_count - 1];
                $this->getDI()->get('logger')->write_log("backyardController:  question_list_keys - ".json_encode($question_list_keys,
                        true)." answer_content - ".$sign_msg['answer_content'].'  - question_id:'.$question_id, 'info');
            } else {
                //没有回答过就从第一个开始
                $question_id = $question_list_keys[0];
            }

            // 获取员答题
            $data = [
                'title'            => $sign_msg_c['title'],
                'content'          => $que_countent['content'] ?? '',
                'read_state'       => $sign_content['submit_status'] ?? 0,
                'question_content' => json_encode($sign_msg_c),
                'question_uuid'    => $question_id ?? '',//第几题
                'answer_content'   => $sign_msg['answer_content'],
            ];

            return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("backyardController: get_exam_msg - ".$e->getMessage());
            return $this->jsonReturn(['code' => -1, 'msg' => 'get sign msg fail'.$e->getMessage(), 'data' => []]);
        }
    }


    /**
     * 员工回答问题
     */
    public function qa_answerAction()
    {
        try {
            $validations = [
                "remote_message_id" => "Required|StrLenGeLe:0,50|>>>:remote_message_id".$this->getTranslation()->_('miss_args'),
                "question_uuid"     => "Required|StrLenGeLe:0,50|>>>:question_uuid".$this->getTranslation()->_('miss_args'),
                "answer_content"    => [
                    "ArrLenGe:1|>>>:answer_content arr".$this->getTranslation()->_('miss_args'),
                    "StrLenGe:1|>>>:answer_content str".$this->getTranslation()->_('miss_args'),
                ],
            ];
            $this->validateCheck($this->paramIn, $validations);

            $msg_id                     = $this->paramIn['remote_message_id'];
            $param['remote_message_id'] = $msg_id;
            $param['staff_id']          = $this->userinfo['staff_id'];
            $param['question_id']       = $this->paramIn['question_uuid'];
            $param['answer_content']    = $this->paramIn['answer_content'] ?? '';

            //查看支援信息
            //如果是支援账号，则找到其 主账号
            $supportStaffInfo = (new AttendanceRepository($this->lang,
                $this->timezone))->getSupportInfoBySubStaff($param['staff_id']);
            if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
                $param['staff_id'] = $supportStaffInfo['staff_info_id'];
            }

            $redis = $this->getDI()->get('redisLib');
            $rkey  = $msg_id.$param['staff_id'];

            $log = (new BackyardServer($this->lang, $this->timezone))->save_qa_answer($param);
            if ($log['is_right']) {
                $redis->delete($rkey);
                $val = 0;
            } else {
                $val = $redis->incr($rkey);
            }
            $data = ['error_count' => $val ?? 0, 'question_uuid' => $log['question_id']];
            return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage());
            return $this->jsonReturn(['code' => 0, 'msg' => 'ok']);
        }
    }

    /**
     * @description: 消息转正通知文案内容
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/9/27 12:10
     */

    public function getMessageAction()
    {
        $return_contract = ['content' => ''];
        try {
            //查询转正评估
            $probation = HrProbationModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id: ',
                'bind'       => [
                    'staff_id' => $this->userinfo['id'],
                ],
            ]);
            if ($probation) {
                //返回消息内容
                $return_contract['content'] = $this->getTranslation()->_("hr_probation_field_msg_to_staff",
                    ['name' => $this->userinfo['name'], 'formal_at' => $probation->formal_at]);
            } else {
                throw new \Exception('转正通知消息,没有找到转正评估 staff_id = '.$this->userinfo['id']);
            }
            return $this->jsonReturn($this->checkReturn(['data' => $return_contract]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage());
            return $this->jsonReturn($this->checkReturn(['data' => $return_contract]));
        }
    }

    /**
     * @description:  马来转正评估 签字消息
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/12/2 14:13
     */
    public function probationMessageAction()
    {
        try {
            $paramIn['id']       = $this->paramIn['probation_audit_id']; //这里是评估 id
            $paramIn['staff_id'] = $this->userinfo['id'];
            $paramIn['msg_id']   = $this->paramIn['msg_id'];           //这是消息 id
            $sign_url            = $this->paramIn['sign_url'] ?? '';   //这是签字 url
            $status              = $this->paramIn['status'] ?? 1;      //2 是签字
            if (empty($paramIn['msg_id']) || empty($paramIn['id']) || empty($paramIn['staff_id']) || ($status == 2 && empty($sign_url))) {
                throw new \Exception($this->getTranslation()->_('miss_args').$paramIn['msg_id']);
            }
            $server = new BackyardServer($this->lang, $this->timezone);
            //获取消息详情
            $res = $server->msg_detail($paramIn);

            if ($res['code'] != 1) {
                throw new \Exception($res['msg'].$paramIn['msg_id']);
            }
            $msg_data = $res['data'];
            if (empty($msg_data)) {
                throw new \Exception('No news message!');
            }

            //获取详情
            $paramIn['type'] = 2;
            $data            = (new ProbationServer($this->lang, $this->timezone))->getByDetail($paramIn);
            if ($data['code'] != ErrCode::SUCCESS) {
                throw new \Exception(' 转正评估签字消息获取内容失败 '.$paramIn['msg_id']);
            }
            $data['data']['read_state'] = $msg_data['read_state'] ?? 1;  //这里是消息是否已读
            if ($status == 2) { //这里是签字
                $res = (new ProbationServer($this->lang, $this->timezone))->saveSign($paramIn['id'], $sign_url);
                if ($res) {
                    //消息变为已读
                    $server->has_read_operation($paramIn['msg_id'],true);
                    $data['data']['read_state'] = 1;//这里是消息是否已读
                } else {
                    throw new \Exception(' 转正评估签字消息 签字失败! '.$paramIn['msg_id']);
                }
            }

            return $this->jsonReturn($data);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage());
            return $this->jsonReturn($this->checkReturn(['data' => [], 'msg' => $e->getMessage(), 'code' => 0]));
        }
    }


    //获取班次消息
    public function getStaffShiftMessageAction()
    {
        $return_contract = ['content' => ''];
        try {
            $msg_id = $this->request->get('msg_id', 'trim');

            if (empty($msg_id)) {
                return $this->jsonReturn($this->checkReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => null]));
            }
            $staff_id            = $this->userinfo['id'];
            $param['msg_id']     = $msg_id;
            $param['staff_id']   = $staff_id;
            $param['staff_name'] = $this->userinfo['name'];

            $server = new BackyardServer($this->lang, $this->timezone);
            $res    = $server->msg_detail($param);
            if (empty($res)) {
                $this->getDI()->get('logger')->write_log("参数:".json_encode($param).";结果:".json_encode($res));
                return $this->jsonReturn($this->checkReturn(['data' => $return_contract]));
            }
            $res['data']['top_text'] = '';
            if (empty($res['data']['read_state'])) {
                $res['data']['foot_text'] = $this->getTranslation()->_('staff_shift_message_1');
            } else {
                $res['data']['foot_text'] = $this->getTranslation()->_('staff_shift_message_2');
            }
            $return_contract = $res['data'] ?? '';
            $this->getDI()->get('logger')->write_log("参数:".json_encode($param).";结果:".json_encode($res), 'info');
            return $this->jsonReturn($this->checkReturn(['data' => $return_contract]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage());
            return $this->jsonReturn($this->checkReturn(['data' => $return_contract]));
        }
    }

    /**
     *
     * 获取消息通知消息内容
     *
     */
    public function getMsgContentAction()
    {
        try {
            $paramIn['staff_id'] = $this->userinfo['id'];
            $paramIn['msg_id']   = $this->paramIn['msg_id'];     //这是消息 id

            //查看支援信息
            //如果是支援账号，则找到其 主账号
            $supportStaffInfo = (new AttendanceRepository($this->lang,
                $this->timezone))->getSupportInfoBySubStaff($this->userinfo['id']);
            if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
                $paramIn['staff_id'] = $supportStaffInfo['staff_info_id'];
            }

            $server              = new BackyardServer($this->lang, $this->timezone);
            $res                 = $server->msg_detail($paramIn);

            if ($res['code'] != 1) {
                echo json_encode(['code' => 0, 'message' => $res['msg'], 'data' => null]);
                die;
            }

            // 普通消息、签字消息 按照模板发送
            if (($res['data']['category'] == MessageEnums::CATEGORY_GENERAL && $res['data']['category_code'] == MessageEnums::CATEGORY_GENERAL_CODE_TEMPLATE)) {
                // 查询附件明细表数据
                $identityData = (new MessageIdentityDetailServer())->getIdentityDetailByParams([
                    "staff_info_id"     => $this->userinfo['id'],
                    "remote_message_id" => $res['data']['message_content_id'],
                    "is_del"            => 0,
                ], ["staff_info_attribute", "id"]);

                if (empty($identityData) || !is_object($identityData)) {
                    // '查询失败！'
                    return $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('4104'), 'data' => []]);
                }
                $staffInfoAttributes = json_decode($identityData->staff_info_attribute, true);
//                $pattern             = array_keys($staffInfoAttributes);
//                array_walk($pattern, function (&$item, $key) {
//                    $item = '/{{'.trim($item).'}}/';
//                });

                preg_match_all("/\{\{\s*[A-Z]\s*\}\}/", $res['data']['content'], $matches);
                $pattern   = $matches[0];
                array_walk($pattern, function (&$item, $key) {
                    $item = '/'.trim($item).'/';
                });

                // 获取到模板的 变量 并 去掉 /{{}}/
                $replaceData = [];
                foreach ($pattern as $key => $val) {
                    $seat = trim($val, '/{{}}/');
                    // 按照模板变量的顺序 进行重序
                    $replaceData[] = $staffInfoAttributes[$seat];
                }

                // 替换后重新赋值
                $res['data']['content'] = preg_replace($pattern, $replaceData, $res['data']['content']);
            }

            $data['msg_content']  = $res['data']['content'];
            $data['read_state']   = $res['data']['read_state'];
            $data['by_show_type'] = '0';
            $data['read_duration'] = 0;

            $messageModel = MessageModel::findFirst([
                'columns'    => 'by_show_type,read_duration',
                'conditions' => 'remote_message_id = :remote_message_id:',
                'bind'       => ['remote_message_id' => $res['data']['message_content_id']],
            ]);
            if (!empty($messageModel)) {
                $data['by_show_type'] = $messageModel->by_show_type;
                $data['read_duration'] = intval($messageModel->read_duration);
            }

            return $this->jsonReturn($this->checkReturn(['data' => $data]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage());
            return $this->jsonReturn($this->checkReturn(['data' => [], 'msg' => $e->getMessage(), 'code' => 0]));
        }
    }

    /**
     * 批量获取env数据
     * @Token
     */
    public function getSettingEnvListAction()
    {
        $code_list = $this->paramIn['code_list'];
        if (empty($code_list) || !is_array($code_list)) {
            return $this->jsonReturn($this->checkReturn(['data' => []]));
        }
        $val_list = SettingEnvModel::find([
            'conditions' => ' code IN ({codes:array}) ',
            'bind'       => ['codes' => $code_list],
            'columns'    => 'code, set_val',
        ])->toArray();
        $val_list = array_column($val_list, null, 'code');
        return $this->jsonReturn($this->checkReturn(['data' => $val_list]));
    }

    /**
     * 员工扣款消息详情
     */
    public function salary_deducitonAction()
    {
        $msg_id                 = $this->request->get('msg_id', 'trim');
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        if (empty($msg_id)) {
            $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }
        $param['msg_id'] = $msg_id;
        $server          = new BackyardServer($this->lang, $this->timezone);
        // 验证该员工是否收到扣款消息
        $msgInfo = $server->getMessageCourierDetail($param);
        if (empty($msgInfo)) {
            return $this->jsonReturn(['code' => 0, 'msg' => '员工没有收到该扣款消息', 'data' => []]);
        }
        $content = json_decode($msgInfo['content'], true);
        $data    = [
            'staff_info_id'    => $content['staff_info_id'],
            'staff_name'       => $content['staff_name'],
            'deduction_amount' => $content['deduction_amount'],
        ];
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }

    /**
     * 外协员工道歉消息详情
     */
    public function apologize_detailAction()
    {
        $msg_id                 = $this->request->get('msg_id', 'trim');
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        if (empty($msg_id)) {
            $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }
        $param['msg_id'] = $msg_id;
        $server          = new BackyardServer($this->lang, $this->timezone);
        // 验证该员工是否收到道歉消息
        $msgInfo = $server->getMessageCourierDetail($param);
        if (empty($msgInfo)) {
            return $this->jsonReturn(['code' => 0, 'msg' => '员工没有收到该道歉消息', 'data' => []]);
        }
        $data['related_id'] = $msgInfo['related_id'];
        $data['type']       = $msgInfo['content'];
        $ret                = new ApiClient('bi_rpcv2', '', 'lh.get_task_detail', $this->lang);
        $ret->setParams($data);
        $res = $ret->execute();
        $this->getDI()->get('logger')->write_log("apologize_detail 参数:".json_encode($data).";结果:".json_encode($res),
            'info');
        if (!isset($res['result'])) {
            return $this->jsonReturn(['code' => -1, 'msg' => $res['error'], 'data' => []]);
        }
        if ($res['result']['code'] == 1) {
            $apologizeInfo = $res['result']['data'];
            $data          = [
                'staff_name'               => $this->userinfo['name'],
                'related_id'               => $msgInfo['related_id'],
                'outsource_name'           => $apologizeInfo['staff_info_name'].'('.$apologizeInfo['staff_info_id'].')',
                'order_num'                => $apologizeInfo['pno'],
                'remark'                   => $apologizeInfo['remark'] ?? '',
                'abnormal_time'            => $apologizeInfo['abnormal_time'],
                'compliant_name'           => $apologizeInfo['compliant_name'],
                'compliant_phone'          => $apologizeInfo['compliant_phone'],
                'parcel_state'             => $apologizeInfo['parcel_state'],
                'apologize_status'         => $apologizeInfo['apologize_status'],
                'complaints_type_name'     => $apologizeInfo['complaints_type_name'],
                'complaints_sub_type_name' => $apologizeInfo['complaints_sub_type_name'],
                'timeout_hours'            => $apologizeInfo['timeout_hours'] ?? 72,
            ];
            return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
        }
        $msg = isset($res['result']['msg']) ? $res['result']['msg'] : $res['error'];
        return $this->jsonReturn(['code' => -1, 'msg' => $msg, 'data' => []]);
    }

    /**
     * 获取试用期目标消息详情
     * @return null
     * @throws BusinessException
     * @throws ValidationException
     */
    public function get_probation_target_msgAction()
    {
        $param = $this->paramIn;

        $validations = [
            "business_id" => "Required|StrLenGeLe:1,20|>>>:" . $this->getTranslation()->_('miss_args'),
            "type"        => "Required|IntGeLe:1,3|>>>:" . $this->getTranslation()->_('miss_args'),
        ];

        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];

        $data = (new ProbationTargetServer($this->lang, $this->timezone))->getProbationTargetMsg($param);

        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }

    /**
     * 消息签字
     * @return null
     * @throws BusinessException
     * @throws ValidationException
     */
    public function probation_target_result_submitAction()
    {
        $param = $this->paramIn;

        $validations = [
            "business_id" => "Required|StrLenGeLe:1,20|>>>:" . $this->getTranslation()->_('miss_args'),
            "type"        => "Required|IntGeLe:1,3|>>>:" . $this->getTranslation()->_('miss_args'),
            'sign_url'    => "Required|StrLenGeLe:1,2000|>>>:" . $this->getTranslation()->_('miss_args'),
        ];

        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];

        (new ProbationTargetServer($this->lang, $this->timezone))->probationTargetResultSubmitUseLock($param);

        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => null]);
    }

    /**
     * 消息驳回
     * @return null
     * @throws BusinessException
     * @throws ValidationException
     */
    public function probation_target_result_rejectAction()
    {
        $param = $this->paramIn;

        $validations = [
            "business_id"   => "Required|StrLenGeLe:1,20|>>>:" . $this->getTranslation()->_('miss_args'),
            "type"          => "Required|IntGeLe:1,3|>>>:" . $this->getTranslation()->_('miss_args'),
            "business_type" => "Required|IntGeLe:1,3|>>>:" . $this->getTranslation()->_('miss_args'),
        ];

        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];

        (new ProbationTargetServer($this->lang, $this->timezone))->probationTargetResultRejectUseLock($param);

        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => null]);
    }

    /**
     * 外协员工道歉消息详情
     */
    public function submit_apologizeAction()
    {
        $msg_id                 = $this->paramIn['msg_id'];
        $related_id             = $this->paramIn['related_id'];
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        if (empty($related_id)) {
            $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }
        if (empty($msg_id)) {
            $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }
        $param['msg_id'] = $msg_id;
        $server          = new BackyardServer($this->lang, $this->timezone);
        // 验证该员工是否收到道歉消息
        $msgInfo = $server->getMessageCourierDetail($param);

        $data['related_id'] = $related_id;
        $data['type']       = $msgInfo['content'];
        $ret                = new ApiClient('bi_rpcv2', '', 'lh.change_task_status', $this->lang);
        $ret->setParams($data);
        $res = $ret->execute();
        $this->getDI()->get('logger')->write_log("submit_apologize 参数:".json_encode($data).";结果:".json_encode($res),
            'info');
        if (!isset($res['result'])) {
            return $this->jsonReturn(['code' => -1, 'msg' => $res['error'], 'data' => []]);
        }
        if ($res['result']['code'] == 1) {
            return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => '']);
        }
        $msg = isset($res['result']['msg']) ? $res['result']['msg'] : $res['error'];
        return $this->jsonReturn(['code' => -1, 'msg' => $msg, 'data' => []]);
    }


    //普通带 知悉按钮的消息 点知悉后设置已读
    public function isReadAction()
    {
        $return_contract = ['content' => ''];
        $msg_id          = $this->paramIn['msg_id'];

        if (empty($msg_id)) {
            return $this->jsonReturn($this->checkReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => null]));
        }

        $param['msg_id']   = $msg_id;
        $param['staff_id'] = $this->userinfo['staff_id'];
        $server            = new BackyardServer($this->lang, $this->timezone);
        $res               = $server->msg_detail($param);
        if (empty($res)) {
            $this->getDI()->get('logger')->write_log("参数:".json_encode($param).";结果:".json_encode($res));
            return $this->jsonReturn($this->checkReturn(['data' => $return_contract]));
        }

        //点击 阅读按钮 设置已读
        if (!empty($this->paramIn['is_click'])) {
            $server->has_read_operation($msg_id,true);
            $res['data']['read_state'] = 1;
        }


        $res['data']['top_text'] = '';
        if (empty($res['data']['read_state'])) {
            $res['data']['foot_text'] = $this->getTranslation()->_('staff_shift_message_1');
        } else {
            $res['data']['foot_text'] = $this->getTranslation()->_('staff_shift_message_2');
        }
        $return_contract = $res['data'] ?? '';
        return $this->jsonReturn($this->checkReturn(['data' => $return_contract]));
    }

    /**
     * 17259 暴力分拣处罚结果
     */
    public function getPunishInfoAction()
    {
        $param['msg_id']        = $this->request->get('msg_id', 'trim');

        $validations         = [
            "msg_id" => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        $data = (new PunishServer())->getPunishInfo($param);

        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }

    /**
     * 21322【TH|BY|消息】 外协仓管自动发送合同-消息详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/88418
     */
    public function getOsStaffContractMsgAction()
    {
        $param['msg_id'] = $this->request->get('msg_id', 'trim');
        $this->validateCheck($param, ['msg_id' => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args')]);
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $data = (new AttendanceServer($this->lang, $this->timezone))->getOsStaffContractMsg($param);
        return $this->jsonReturn(['code' => ErrCode::SUCCESS, 'msg' => 'ok', 'data' => $data]);
    }

    /**
     * 21322【TH|BY|消息】 外协仓管自动发送合同-签字
     * @api https://yapi.flashexpress.pub/project/93/interface/api/88421
     */
    public function signOsStaffContractSubmitAction()
    {
        $param['msg_id']        = $this->paramIn['msg_id'] ?? '';
        $param['sign_url']      = $this->paramIn['sign_url'] ?? '';
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $validations            = [
            'msg_id'    => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
            'sign_url'  => 'Required|StrLenGe:1|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $result = (new AttendanceServer($this->lang, $this->timezone))->signOsStaffContractSubmitUseLock($param);
        if ($result) {
            $server = new BackyardServer($this->lang, $this->timezone);
            // 消息处理成功后，写入队列
            $server->addHaveReadMsgToMns($param['msg_id']);

            return $this->jsonReturn(['code' => ErrCode::SUCCESS, 'msg' => 'ok', 'data' => ['result' => 'success']]);
        }
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 获取转正评估阶段完成消息详情
     * @Token
     * @return null
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getProbationStageDoneMsgAction()
    {
        $param = $this->paramIn;
        $validations = [
            "msg_id" => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];
        $data = (new ProbationServer($this->lang, $this->timezone))->getProbationStageDoneMsg($param);
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }

    /**
     * 获取转正评估目标制定提醒消息详情
     * @Token
     * @return null
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getProbationGoalRemindMsgAction()
    {
        $param = $this->paramIn;
        $validations = [
            "msg_id" => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];
        $data = (new ProbationServer($this->lang, $this->timezone))->getProbationGoalRemindMsg($param);
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }

    /**
     * @Token
     * @return null
     * @throws ValidationException
     */
    public function submitProbationStageDoneMsgAction()
    {
        $param = $this->paramIn;
        $validations = [
            "msg_id" => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
            'sign_img_url'  => 'Required|StrLenGe:1|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];
        /**
         * @see ProbationServer::submitProbationStageDoneMsg()
         */
        $data = (new ProbationServer($this->lang, $this->timezone))->submitProbationStageDoneMsgUseLock($param);
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => []]);
    }

    /**
     * 获取推荐简历提交后BY消息通知TA消息详情
     * @return null
     * @throws ValidationException
     */
    public function resumeRecommendSubmitTaMsgAction()
    {
        $param = $this->paramIn;
        $validations = [
            "msg_id" => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];
        $data = (new ResumeRecommendServer($this->lang, $this->timezone))->resumeRecommendSubmitTaMs($param);
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }
    /**
     * 阅读时长的普通消息-置已读
     */
    public function readMsgNormalAction()
    {
        $param = $this->paramIn;
        $validations = [
            "msg_id" => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];
        $server              = new BackyardServer($this->lang, $this->timezone);
        $data   = $server->readMsgNormalUseLock($param);
        $this->jsonReturn(['code' => 1, 'message' => 'ok', 'data' => $data]);
    }

    /**
     * my 获取转正评估阶 二阶段最后评估人 通过后 签字消息pdf详情
     * @Token
     * @return null
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getProbationConfirmationLetterAction()
    {
        $param = $this->paramIn;
        $validations = [
            "msg_id" => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];
        $data = (new ProbationServer($this->lang, $this->timezone))->getProbationConfirmationLetter($param);
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }

    /**
     * my 二阶段最后评估人通过后的pdf消息 签字
     * @Token
     * @return null
     * @throws ValidationException
     */
    public function submitProbationConfirmationLetterAction()
    {
        $param = $this->paramIn;
        $validations = [
            "msg_id" => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];
        /**
         * @see ProbationServer::submitProbationConfirmationLetter()
         */
        $data = (new ProbationServer($this->lang, $this->timezone))->submitProbationConfirmationLetterUseLock($param);
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => []]);
    }

    /**
     * 二阶段最后评估人通过后的pdf消息 - 重新发送EmployeeConfirmationLetter
     * @Token
     * @return null
     * @throws ValidationException
     */
    public function resendEmployeeConfirmationLetterAction()
    {
        $param = $this->paramIn;
        $validations = [
            'staff_info_id'  => 'Required|Int',
        ];
        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];
        /**
         * @see ProbationServer::resendEmployeeConfirmationLetter()
         */
        $data = (new ProbationServer($this->lang, $this->timezone))->resendEmployeeConfirmationLetterUseLock($param);
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => []]);
    }

    /**
     * 二阶段最后评估人通过后的pdf消息 - 重新发送NonComfortLetter
     * @Token
     * @return null
     * @throws ValidationException
     */
    public function resendNonComfortLetterAction()
    {
        $param = $this->paramIn;
        $validations = [
            'staff_info_id'  => 'Required|Int',
        ];
        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];
        /**
         * @see ProbationServer::resendNonComfortLetter()
         */
        $data = (new ProbationServer($this->lang, $this->timezone))->resendNonComfortLetterUseLock($param);
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => []]);
    }

    /**
     * 获取转正消息详情
     * @Token
     * @return null
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getProbationFormalMsgAction()
    {
        $param = $this->paramIn;
        $validations = [
            "msg_id" => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];
        $data = (new ProbationServer($this->lang, $this->timezone))->getProbationFormalMsg($param);
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }

    /**
     * 获取转正不通过bp消息-详情
     * @Token
     * @return null
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getProbationConfirmationLetterBpMsgAction()
    {
        $param = $this->paramIn;
        $validations = [
            "msg_id" => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $param['user_id'] = $this->userinfo['staff_id'];
        $data = (new ProbationServer($this->lang, $this->timezone))->getProbationConfirmationLetterBpMsg($param);
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }
}
