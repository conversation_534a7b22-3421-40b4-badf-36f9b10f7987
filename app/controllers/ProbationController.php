<?php
namespace FlashExpress\bi\App\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditContractWorkerModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Models\backyard\HrProbationContractWorkerModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\ProbationServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;

class ProbationController extends Controllers\ControllerBase
{
    protected $paramIn;
    protected $server;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->get();

        //会带个_url参数
        unset($this->paramIn['_url']);

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        //$this->paramIn = array_filter($this->paramIn);
    }


    /**
     * 审批-转正评估 大红点
     * @return null
     */
    public function getNumAction(){
        try {
	        $data  = (new ProbationServer($this->lang,$this->timezone))->getAuditNum($this->userinfo['id']);
            $this->jsonReturn($data);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("probation getNum 异常信息:" . $e->getMessage(), 'error');
           $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    public function listAction(){
        $systemMaintenance = (new SettingEnvServer)->getSetVal('system_maintenance');
        if (isCountry('PH') && $systemMaintenance) {
            throw new ValidationException('The system is under maintenance. Please try again later.');
        }
        $paramIn   = $this->paramIn;
        $validations = [
            "is_deal" => "Required|IntIn:1,2,3",
            'page_num'=>"IntGe:1",
            'page_size'=>"IntGe:1",
            "cur_level"=>"IntGe:0",
        ];
        $this->validateCheck($paramIn, $validations);

        $paramIn = array_only($paramIn,array_keys($validations));
        $paramIn['audit_id'] =$this->userinfo['id'];


        $data = (new ProbationServer($this->lang,$this->timezone))->getByList($paramIn);
        $this->jsonReturn($data);
    }


    public function detailAction() {

        $paramIn     = $this->paramIn;
        $validations = [
            "id" => "Required|IntGe:0",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn             = array_only($paramIn, array_keys($validations));
        $paramIn['audit_id'] = $this->userinfo['id'];
        $data                = (new ProbationServer($this->lang, $this->timezone))->getByDetail($paramIn);
        $this->jsonReturn($data);
    }


    public function auditAction() {

        $paramIn     = $this->paramIn;
        $validations = [
            "id"     => "Required|IntGe:0",
            'score'  => "Required",
            'remark' => "StrLenGeLe:0,150",
            'pic'    => "StrLenGeLe:0,255",
            'job_content' => 'StrLenGeLe:0,2550',  // 1000字
        ];
        $this->validateCheck($paramIn, $validations);

        $paramIn             = array_only($paramIn, array_keys($validations));
        $paramIn['audit_id'] = $this->userinfo['id'];
        //防止重复提交
        $cache_key = 'probation_controller_audit_' . $paramIn['id'];
        $data = $this->atomicLock(function () use ( $paramIn ) {
            return (new ProbationServer($this->lang, $this->timezone))->probation_audit($paramIn['id'], $paramIn['audit_id'], $paramIn);
        }, $cache_key, 10, false);
        if ($data === false) { //没有获取到锁
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('ticket_repeat_msg')));
        }
        return $this->jsonReturn($data);

    }


    /**
     * 根据模板获得分数
     */
    public function getScoreAction(){
        try {
            $paramIn   = $this->paramIn;
            $validations = [
                'score'=>"Required",
                'cur_level'=>"Required|IntIn:1,2",
            ];
            $this->validateCheck($paramIn, $validations);
            $paramIn = array_only($paramIn,array_keys($validations));

	        $data = (new ProbationServer($this->lang,$this->timezone))->getScoreFromTpl($paramIn['score'], $paramIn['cur_level']);

            return $this->jsonReturn(self::checkReturn(['data'=>$data]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("probation_get_score 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 激活
     */
    public function activeAction() {

        $paramIn     = $this->paramIn;
        $validations = [
            "id" => "Required|IntGe:0",
        ];
        if (isCountry('PH')) {
            $validations['probation_channel_type'] ='Required|IntGe:0';
        }
        $this->validateCheck($paramIn, $validations);
        $paramIn             = array_only($paramIn, array_keys($validations));
        $paramIn['audit_id'] = $this->userinfo['id'];
        //防止重复点击 因为只能激活一次 所以 点一次 缓存 10 秒
        $cache_key = 'probation_controller_active_' . $paramIn['id'];
        $returnArr = $this->atomicLock(function () use ( $paramIn ) {
            return (new ProbationServer($this->lang, $this->timezone))->active($paramIn);
        }, $cache_key, 10, false);
        if ($returnArr === false) { //没有获取到锁
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('ticket_repeat_msg')));
        }
        return $this->jsonReturn($returnArr);
    }



    // 上级跟进评估消息中 上级 与 被评估人详情 请求前置是消息链接搜索 followUpEmployees
    // 现在看已经没有国家调用
    //messageFollowUpInfo -》 isReadAcknowledgement -》未读 点击发送 sendAcknowledgement
    public function messageFollowUpInfoAction()
    {
        try {
            $params = $this->paramIn;
            $this->checkReturn($params, [
                'probation_id' => 'Required|IntGe:1',
                'cul_level' => 'Required|IntIn:1,2',
                'manger_id' => 'Required|IntGe:1',
            ]);

            $hrProbation = HrProbationModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $params['probation_id'],
                ],
            ]);
            if ($hrProbation) {
                $hrProbation = $hrProbation->toArray();
                $staffServer =   new StaffServer();
                $staff = $staffServer ->getStaffInfoById($hrProbation['staff_info_id']);
                $manger = $staffServer ->getStaffInfoById($params['manger_id']);
                $result = [
                    'staff' => $staff,
                    'manger' => $manger,
                ];
                $probationServer = $this->class_factory('ProbationServer',$this->lang, $this->timezone);

                if (in_array($staff['job_title'], $probationServer->getCountryJobs())) {
                    $result['days'] = 0;
                } else {
                    $result['days'] = 14;
                }

                $this->jsonReturn($this->checkReturn(['data' => $result]));
            } else {
                throw new \Exception('not fund probation id ' . $params['probation_id']);
            }

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("messageFollowUpInfoAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }
  //followUpEmployees
   public function isReadAcknowledgementAction()
   {
       try {
           $params = $this->paramIn;
           $this->checkReturn($params, [
               'id' => 'Required|IntGe:1',
               'probation_id' => 'Required|IntGe:1',
               'cul_level' => 'Required|IntIn:1,2',
           ]);
           // 将消息 已读
           $hrProbationModel = HrProbationAuditModel::findFirst([
               'conditions' => 'id = :id:',
               'bind' => ['id' => $params['id']],
           ]);

           $isRead = 0;
           if ($hrProbationModel) {
               $messageId = $hrProbationModel->message_id;
               $message = MessageCourierModel::findFirst([
                   "conditions" => " id = :id:",
                   'bind' => ['id' => $messageId],
               ]);
               if ($message && $message->read_state == 1) {
                   $isRead = 1;
               }
           }
           $this->jsonReturn($this->checkReturn(['data' => ['is_read' => $isRead]]));
       } catch (\Exception $e) {
           $this->getDI()->get('logger')->write_log("isReadAcknowledgementAction 异常信息:" . $e->getMessage(), 'error');
           return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
       }

   }

    //确认面谈并发送Acknowledgement 点击后确认按钮 向被评估人发送消息
    // 发送信息 MESSAGE_CATEGORY_70 = 70; 转正评估跟进  url followUpEmployees 进入页面触发
    public function sendAcknowledgementAction()
    {
        try {
            $params = $this->paramIn;
            // 按钮类型 1 确认面谈并发送Acknowledgement  2  确认面谈并不对员工做离职处理
            if ($params['button_type'] == 2) {
                $this->checkReturn($params, [
                    'id' => 'Required|IntGe:1',
                ]);
            } else {
                $this->checkReturn($params, [
                    'id' => 'Required|IntGe:1',
                    'probation_id' => 'Required|IntGe:1',
                    'cul_level' => 'Required|IntIn:1,2',
                    'manger_id' => 'Required|IntGe:1',
                ]);
                if ( $this->userinfo['id'] != $params['manger_id'] ) {
                    throw new \Exception('消息接受人与处理人不一致');
                }
                $hrProbation = HrProbationModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => [
                        'id' => $params['probation_id'],
                    ],
                ]);
                if ($hrProbation) {
                    $probationServer =
                        new ProbationServer($this->lang, $this->timezone);
                    $probationServer->sendAckNowledgement($params);

                } else {
                    throw new \Exception('not fund probation id ' . $params['probation_id']);
                }
            }

            // 将消息 已读
            $hrProbationModel = HrProbationAuditModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']],
            ]);
            if ($hrProbationModel) {
                $hrProbationAudit = $hrProbationModel->toArray();
                $backyardServer =
                new BackyardServer($this->lang, $this->timezone);
                $result = $backyardServer->has_read_operation($hrProbationAudit['message_id'],true);
                $this->getDI()->get('logger')->write_log("sendAcklowledgement :" . $hrProbationAudit['message_id'] . ' ' . $result , 'info');
            }


            $this->jsonReturn($this->checkReturn(1));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("sendAckNowLedgementAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }

    /**
     * acknowledgment消息详情请求 前置 前端请求消息MessageEnums::MESSAGE_CATEGORY_71 /#/employeesPush
     * @return void|null
     */
    public function signProbationInfoAction()
    {
        try {
            $params = $this->paramIn;
            $this->validateCheck($params, [
                'id' => 'Required|IntGe:1',
                'probation_id' => 'Required|IntGe:1',
            ]);
            if (isCountry('PH') && isset($params['probation_channel_type'])
            && $params['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3) {
                $hrProbationModel = HrProbationAuditContractWorkerModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $params['id']],
                ]);
                $hrProbation = HrProbationContractWorkerModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $params['probation_id']],
                ]);
            } else {
                $hrProbationModel = HrProbationAuditModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $params['id']],
                ]);
                $hrProbation = HrProbationModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $params['probation_id']],
                ]);
            }
            $result = [];
            if ($hrProbationModel && $hrProbation) {
                $result['option_type'] = $hrProbationModel->option_type;
                $result['sign_url'] = $hrProbationModel->sign_url;
                $result['pdf_path'] = empty($hrProbation->pdf_path) ? ($hrProbation->before_sign_pdf_url ?? '') : $hrProbation->pdf_path;
                $result['sign_status'] = empty($hrProbation->pdf_path) ? 0 : 1;
                $result['acknowledge_remark'] = $hrProbationModel->acknowledge_remark;
            }

            $this->jsonReturn($this->checkReturn(['data' => $result]));
        } catch (\Exception $e) {

            $this->getDI()->get('logger')->write_log("signProbationInfoAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }

    // 被评估人签字消息 消息页面按钮 MessageEnums::MESSAGE_CATEGORY_71
    //只有 PH 有
    public function signProbationAction()
    {
        try {
            $params = $this->paramIn;
            $this->validateCheck($params, [
                'id' => 'Required|IntGe:1',
                'probation_id' => 'Required|IntGe:1',
                'cul_level' => 'Required|IntIn:1,2',
//                'option_type' => 'Required|IntIn:1,2,3',
//                'acknowledge_remark' => 'Required',
                'sign_url' => 'Required',
            ]);
            $params['probation_channel_type'] = $params['probation_channel_type'] ?? null;

            $probationServer = $this->class_factory('ProbationServer',$this->lang, $this->timezone);
            $probationServer->signProbation($params);
            if ($params['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3) {
                $hrProbationModel = HrProbationAuditContractWorkerModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $params['id']],
                ]);
            } else {
                $hrProbationModel = HrProbationAuditModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $params['id']],
                ]);
            }

            if ($hrProbationModel) {
                $hrProbationAudit = $hrProbationModel->toArray();
                // 将消息 已读
                $backyardServer = new BackyardServer($this->lang, $this->timezone);
                $result = $backyardServer->has_read_operation($hrProbationAudit['acknowledgement_message_id'],true);
                $this->getDI()->get('logger')->write_log("signProbationAction :" . $hrProbationAudit['acknowledgement_message_id'] . ' ' . $result, 'info');
            }

            $this->jsonReturn($this->checkReturn(1));
        } catch (\Exception $e) {

            $this->getDI()->get('logger')->write_log("signProbationAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }

    /**
     * 查询消息详情 调用来源 消息 hmc 搜索 probationary-contract-message
     * 目前只有马来
     */
    public function probationResignDetailAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        //[2]数据验证
        $validations = [
            "msg_id" => "Required|StrLenGeLe:1,200|>>>:[msg_id] parameter is wrong",
        ];

        $this->validateCheck($paramIn, $validations);

        $data = (new ProbationServer($this->lang, $this->timezone))->probationResignDetail($paramIn);

        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 试用期未通过的员工通知 -结果通知
     * @return void|null
     * @throws Exception
     */
    public function notPassNoticeAction()
    {
        $param       = $this->paramIn;
        $validations = [
            "staff_info_id" => "Required|Int",
        ];
        $this->validateCheck($param, $validations);
        /**
         * @see ProbationServer::notPassNotice()
         */
        $data = (new ProbationServer($this->lang, $this->timezone))->notPassNoticeUseLock($param);
        $this->jsonReturn($data);
    }

    /**
     * @return void
     * @throws BusinessException
     * @throws ValidationException
     */
    public function detailNonFrontLineAction()
    {
        $param       = $this->paramIn;
        $validations = [
            "staff_info_id" => "Required|Int",
        ];
        $this->validateCheck($param, $validations);
        $param['operator_id'] = $this->userinfo['id'];
        $data = (new ProbationServer($this->lang, $this->timezone))->detailNonFrontLine($param);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * @return void
     * @throws BusinessException
     * @throws ValidationException
     */
    public function scoreSubmitNonFrontLineAction()
    {
        $param       = $this->paramIn;
        $validations = [
            "id" => "Required|Int",
            "staff_info_id" => "Required|Int",
        ];
        $this->validateCheck($param, $validations);
        $param['operator_id'] = $this->userinfo['id'];
        /**
         * @see ProbationServer::scoreSubmitNonFrontLine()
         */
        $data = (new ProbationServer($this->lang, $this->timezone))->scoreSubmitNonFrontLineUseLock($param);
        $this->jsonReturn($data);
    }

    /**
     * @return void
     */
    public function enumerateAction()
    {
        $data = (new ProbationServer($this->lang, $this->timezone))->scoreEnumerateNonFrontLine();
        $return_data['scoreEnumerateNonFrontLine'] = $data;
        $this->jsonReturn($this->checkReturn(['data' => $return_data]));
    }
}