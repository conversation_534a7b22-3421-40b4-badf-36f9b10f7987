<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\PayrollServer;
use Exception;
use FlashExpress\bi\App\Server\PersoninfoServer;
use FlashExpress\bi\App\Server\VoucherServer;

class PayrollController extends Controllers\ControllerBase
{


    public function initialize()
    {
        $this->cross();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->lang = $this->processingDefault($this->request->getHeaders(), 'Accept-Language', 1, 'th');
        if(function_exists('molten_get_traceid')){
            $traceid = molten_get_traceid();
        }
        header('traceid:' . ($traceid ?? ''));
        //处理语言相关
        $this->languagePack = $this->di->get('languagePack');
        $this->languagePack->setLanguage($this->lang);  //设置语言环境，server  controller repository通用
        $this->t = $this->languagePack->t;              //获取全局语言包赋值到controller层

    }
    protected static $code= '(*^SSH*SOS^^BACK&&FLASH';

    /**
     * 改成前后端分离了所以 这个先弃用了
     */
    public function get_pdfAction()
    {
        try {
            $_user = $this->getDI()->get('session')->get('payroll_user');

            if (empty($_user)) {
                $this->jsonReturn($this->checkReturn(-3,'Please login'));
            }
            $staff_id = $_user['id'];
            $key = $this->request->get('key','string');
            if ($key !=  md5(static::$code . $staff_id)) {
                $this->jsonReturn($this->checkReturn(-3,'key invalid'));
            }
            $paramIn["month"] = $this->request->get('date','string', date('Y-m',strtotime(date('Y-m') . '-1 month')));
            $hcm_rpc = new ApiClient('hcm_rpc', '', 'get_payroll_pdf');
            $hcm_rpc->setParams(
                [
                    "staff_id" => $staff_id,
                    "month" => $paramIn["month"],
                ]
            );
            $return = $hcm_rpc->execute();
            if(isset($return['result']['code']) && $return['result']['code'] == 1) {
                $this->jsonReturn(self::checkReturn(["data" => ["url" => $return['result']['data'] ?? '']]));
            } else {
                $this->jsonReturn($this->checkReturn(-3, $return['result']['msg'] ?? 'Not found'));
                $this->getDI()->get('logger')->write_log("get_pdfAction:get_payroll_pdf:" . json_encode($return ,true));
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("getRoleList:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }

    /**
     * 工资条专属校验-下载工资明细
     */
    public function loginAction()
    {
        try{
            $message = 'Wrong account or password';
            $data = $this->paramIn;
            if (!isset($data['user']) || !isset($data['pwd'])) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('login_all')));
            }
            $_data['account'] = $data['user'];
            $_data['pwd'] = $data['pwd'];
            $return_data = (new PayrollServer())->verify_fle($_data);
            if (empty($return_data)) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('login_all')));
            }
            if (!isset($data['date'])) {
                $data['date'] =  date('Y-m',strtotime(date('Y-m') . ' -1 month'));
            }
            $lang = $this->lang;
            if (isset($data['lang'])) {
                $lang = $data['lang'];
            }

            //获取付款凭证 文件
            if(!empty($data['source']) && $data['source'] == 'payment') {
                $where['staff_id'] = $_data['account'];
                $where['start_date'] = $data['start_date'];
                $where['type'] = VoucherServer::TYPE_PAYMENT;
                $returnArr = (new PersoninfoServer($this->lang,$this->timezone))->getVoucherFromFbi($where);
                $res['url'] = !empty($returnArr['url']) ? 'https://vpdf.flashexpress.com/web/viewer.html?file=' . $returnArr['url'] : '';
                $this->jsonReturn(self::checkReturn(['data' => $res]));
            }

            //获取代扣税凭证 文件
            if(!empty($data['source']) && $data['source'] == 'withholding_tax') {
                $where['staff_id'] = $_data['account'];
                $where['start_date'] = $data['start_date'];
                $where['type'] = VoucherServer::TYPE_TAX;
                $returnArr = (new PersoninfoServer($this->lang,$this->timezone))->getVoucherFromFbi($where);
                $res['url'] = !empty($returnArr['url']) ? 'https://vpdf.flashexpress.com/web/viewer.html?file=' . $returnArr['url'] : '';
                $this->jsonReturn(self::checkReturn(['data' => $res]));
            }


            $hcm_rpc = new ApiClient('hcm_rpc', '', 'get_payroll_pdf',$lang);
            $hcm_rpc->setParams(
                [
                    "staff_id" => $data['user'],
                    "month" => $data["date"],
                ]
            );
            $return = $hcm_rpc->execute();
            if(isset($return['result']['code']) && $return['result']['code'] == 1) {
                $this->jsonReturn(self::checkReturn(["data" => ["url" => $return['result']['data'] ?? '']]));
            } else {
                $this->jsonReturn($this->checkReturn(-3, $return['result']['msg'] ?? 'Not found'));
                $this->getDI()->get('logger')->write_log("get_pdfAction:get_payroll_pdf:" . json_encode($return ,true));
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("getRoleList:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }
}