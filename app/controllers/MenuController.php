<?php
namespace FlashExpress\bi\App\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Modules\Th\Server\WmsPlanServer;
use FlashExpress\bi\App\Repository\ResumeRepository;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\LoginServer;
use FlashExpress\bi\App\Server\ProbationServer;
use FlashExpress\bi\App\Server\ResumeServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\TicketServer;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\ResumeRecommendRepository;

class MenuController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        //会带个_url参数
        unset($this->paramIn['_url']);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    /**
     * by index
     */
    public function indexAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] = $this->userinfo;
        $validations         = [
            "staff_id" => "Required|Int"
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理ge
        $server = new BackyardServer($this->lang, $this->timezone);
        $unAuditNumRes = $server->getWaitAuditData($paramIn);
        $plan_wms_task_count = $server->setExpire(300)->planWmsTaskCountMsgFromCache($this->userinfo['staff_id']);
        if (isCountry('ID')) {
            $flashPayMallUrl = (new SettingEnvServer())->getSetVal('flash_pay_mall_url');
        }
        $return = [
            //总和
            'total_audit_num'         => $unAuditNumRes['un_audit_num'],
            'ProbationGetNum'         => $unAuditNumRes['probation_data'],
            'waitAuditNum'            => $unAuditNumRes['wait_audit_data'],
            'TicketGetNum'            => $unAuditNumRes['ticket_data'],
            'interview_count'         => $unAuditNumRes['interview_data'],
            'resume_filter'           => $unAuditNumRes['resume_filter_data'],
            'resumeRecommendNum'      => $unAuditNumRes['commitment_data'],
            'InventoryCheckTaskCount' => ['num' => $unAuditNumRes['inventory_check_task_count']],
            'planWmsTaskCount'        => ['num' => $plan_wms_task_count ?? 0],
            'kpiCount'                => ['num' => $unAuditNumRes['kpi_count']],//kpi目标
            'oaFolderNum'             => $unAuditNumRes['oa_folder_num'] ?? 0,
            'packageAllotNum'         => ['num' => $unAuditNumRes['package_allot_num']],
            'reimbursementApplyNum'   => $unAuditNumRes['reimbursement_apply_num'] ?? 0,
            'flashPayMall'            => ['url' => $flashPayMallUrl ?? ''],
        ];
        $this->getDI()->get('logger')->write_log("menu_index_{$this->userinfo['staff_id']}: ".json_encode($return,JSON_UNESCAPED_UNICODE),'info');
        return $this->jsonReturn(self::checkReturn(['data' =>$return]));
    }

    /**
     * 登录
     * @return void
     * @throws BusinessException
     */
    public function getOALoginInfoAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        $data = (new LoginServer($this->lang))->getOALoginInfo($this->userinfo['staff_id']);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }


    /**
     * 登录
     * @return void
     * @throws BusinessException
     */
    public function getHCMLoginInfoAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        $data = (new LoginServer($this->lang))->getHCMLoginURL($this->userinfo['staff_id']);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }


}
