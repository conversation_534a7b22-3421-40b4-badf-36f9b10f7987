<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\PasswordHash;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\LoginServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Repository\BySettingRepository;
use Exception;

//hr客户端 基类
class ClientController extends Controllers\ControllerBase{
//    public $timezone;

    const H_LAT = 'HEAD_OFFICE_LAT';
    const H_LNG = 'HEAD_OFFICE_LNG';

    //总部员工 需要禁止打卡的部门 fulfillment
    protected $att_ban_department = array(
        15
    );
    public function initialize(){
        $this->cross();
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn)){
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);

        //时区配置
        $this->timezone = $this->getDI()['config']['application']['timeZone'];
    }

    //新版 backyard 登陆 废弃 登陆不做迁移
    public function loginAction(){
        $data = $this->paramIn;
        $result = array('code' => -803,'message' => 'fail','data'=> null);
        try{
            $data['staff_id'] = $this->paramIn['login'];
            $headerData = $this->request->getHeaders();
            /**
             * Host	*************:9090
            X-FLE-EQUIPMENT-TYPE	backyard
            Accept	application/json,image/*
            Accept-Encoding	gzip;q=1.0, compress;q=0.5
            Content-Type	application/json
            Content-Length	174
            User-Agent	name:APP_1.1.6;os:iOS_12.0;device:x86_64,Simulator;language:zh-Hans;user:iPhone%20X
            X-DEVICE-ID	089A386A-E54F-4899-B1F5-1C0C19854EE7

            Cache-Control: no-cache
            Accept-Language: zh-CN
            X-Device-Lng: 100.310000
            X-Device-Lat: 13.4500000
            X-Version: 1.9.9
            User-Agent: name:KIT_1.9.9;
            os:Android_4.4.4;
            device:Neolix 1-C-I;
            language:zh-CN
            Content-Type: application/json;
            charset=UTF-8:
             */

            $data['lang'] = $this->processingDefault($headerData, 'Accept-Language');
            //backyard
            $data['equipment_type'] = $this->processingDefault($headerData, 'X-Fle-Equipment-Type');

            $result = $this->verify($data);
            // 判断验证结果 登录成功 返回 token  和用户相关信息
            if($result['code'] == 1) {
                $this->getDI()->get('logger')->write_log($result, 'info');
                $this->jsonReturn($result);
            }else{
                http_response_code(388);
                $this->jsonReturn($result);
            }

        }catch (\Exception $e){
            http_response_code(500);
            $this->getDI()->get('logger')->write_log($e->getMessage(), 'info');
            $result['message'] = $e->getMessage();
            $this->jsonReturn($result);
        }
    }


    /**
     * @param string $post
     * @return array
     */
    public function verify($post = '')
    {
        $hash = new PasswordHash(10, false);

        $return = array('code' => 1, 'message' =>'success', 'data' => (object)array());
        $lang = $this->lang = empty($post['lang'])? 'zh-CN': $post['lang'];
        $uid = isset($post['staff_id']) ? $post['staff_id'] : 0;
        $version = $post['version'];

        if(empty($post['staff_id'])){
            $return['code'] = -1;
            $return['message'] = 'no such account';
            return $return;
        }

        //先查 fle  要调整为 bi -- todo
        $staff_model = new StaffRepository($lang);
        $info = $staff_model->login_info($uid);

        //找到不该用户
        if(empty($info)){
            $return['code'] = -1;
            $return['message'] = 'no such account';
            return $return;
        }
        //员工已离职
        if($info['state'] == 2){
            $return['code'] = 100253;
            $return['message'] = 'This account has been suspended';
            return $return;
        }elseif ($info['state'] == 3){
            $return['code'] = 100253;
            $return['message'] = 'Non-working staff cannot log in the system';
            return $return;
        }

        //子账号
        if($info['is_sub_staff'] == 1){
            $return['code'] = -188;
            $return['message'] = 'sub staff can not login';
            return $return;
        }
        if(empty($post['equipment_type']) || $post['equipment_type'] != 'backyard'){
            $return['code'] = -1;
            $return['message'] = 'EQUIPMENT-TYPE error';
            return $return;
        }

        //验证版本
        $latest_version = env('client_version');
        if(!empty($latest_version) && $latest_version != $version){
            $data['version_info'] = array(
                'version' => $latest_version,
                'current_time' => time(),
                'time_interval' => null,
                'download_url' => env('app_download_url'),
            );

            $return['data'] = $data;
            $return['code'] = 100101;
            $return['message'] = 'your app need update';
            return $return;
        }

        //验证密码
        $bool = $hash->checkPassword($post['password'], $info['encrypted_password']);
        if(!$bool){
            $return['code'] = -108;
            $return['message'] = 'wrong password';
            return $return;
        }

        //验证通过 取数据
        $info['clientsd'] = empty($post['clientsd']) ? '' : $post['clientsd'];
        $info['clientid'] = empty($post['clientid']) ? '' : $post['clientid'];
        $info['equipment_type'] = 3;//3 设备类型统一 为 backyard
        $info['os'] = empty($post['os']) ? '' : $post['os'];
        $info['version'] = empty($post['version']) ? '' : $post['version'];
        $setting = new BySettingRepository($lang);
        //获取是否可外勤打卡 白名单规则 field_punch ->false 网点员工 非白名单不可外勤
        $info['field_punch'] = true;
        //如果是 网点 外协员工、加盟商员工不显示 formal = 0 和 store category 6 ->wage_display false
        $info['wage_display'] = true;
        if($info['organization_type'] == 1){//网点
            $info['field_punch'] = false;
//            $store_server = new SysStoreServer($lang);
//            $store_info = $store_server->getStoreByid($info['organization_id']);
            //坐标数据 网点取store  总部 固定配置 setting_env
            $info['store_lat'] = empty($info['store_lat']) ? 0 : $info['store_lat'];
            $info['store_lng'] = empty($info['store_lng']) ? 0 : $info['store_lng'];

            if($info['store_category'] == 6)
                $info['wage_display'] = false;

            if($info['formal'] == 0)
                $info['wage_display'] = false;

        }else if($info['organization_type'] == 2){//部门
            //总部坐标 取配置表
            $info['store_lat'] = $setting->get_setting(self::H_LAT);
            $info['store_lng'] = $setting->get_setting(self::H_LNG);

            // Fullfillment部门的在白名单的可以外勤打卡
            if(in_array($info['department_id'],$this->att_ban_department))
                $info['field_punch'] = false;
        }

        //获取打卡白名单 可以打卡
//        $attendance_ignore = $setting->get_setting('ATT_IGNORE');
//        if(!empty($attendance_ignore) && strstr($attendance_ignore,$info['id']))
//            $info['field_punch'] = true;

        $data_info = $this->crypt_token($info);

        //拼接头像 暂时不用了 by 没地方用
        /**
         * "staff_info_avatar_url": {
        "id": "17253",
        "name": null,
        "object_key": null,
        "object_url": "https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/reimburse/1557388719-73ec8f87ba9a4a4f8dd70ea3ac4d477c.jpg",
        "object_url_th": "https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/reimburse/1557388719-73ec8f87ba9a4a4f8dd70ea3ac4d477c.jpg?x-oss-process=style/th",
        "object_url_x3": "https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/reimburse/1557388719-73ec8f87ba9a4a4f8dd70ea3ac4d477c.jpg?x-oss-process=style/x3",
        "image_name": null,
        "image_key": null,
        "created_at": null
        },
         */

        $return['data'] = $data_info;
        return $return;

    }

    /**
     * 认证后 用户信息 并写 员工设备表
     * @param $info
     * @return array
     */
    protected function crypt_token($info){
        $di = $this->getDI();
        $cache_time=  24 * 3600;//1天

        //生成token
        $str = $di['config']['application'][' '];
        $time = time();
        $staff_id = $info['id'];
        $auth = sha1(md5($time.$str.$staff_id));
        $cache_key = md5($auth);//缓存用户信息 键
        $session_id = "{$time}_{$auth}_{$staff_id}";//返回伪session
        //随便生成一个 目前没用
        $tid = $this->crypt_tid($str,$info['clientid']);

        //写 staff_device_info
        $insert['staff_info_id'] = $info['id'];
        $insert['serversd'] = $time;
        $insert['clientsd'] = $info['clientsd'];
        $insert['clientid'] = $info['clientid'];
        $insert['equipment_type'] = $info['equipment_type'];
        $insert['tid'] = $tid;
        $insert['session_id'] = $session_id;
        $insert['current_ip'] = $this->request->getServer('REMOTE_ADDR');
        $insert['os'] = $info['os'];
        $insert['version'] = $info['version'];
        $insert['device_type'] = 0;
        if($info['os'] != 'ios')
            $insert['device_type'] = 1;
        //华为 暂时没参数 先屏蔽
        if(false){
            $insert['device_type'] = 2;
        }

        $server = new LoginServer($this->lang,$this->timezone);
        $server->device_info_save($insert);

        $cache = $this->getDI()->get('redis');

        /**
         *
         * "sessionid": "1594298682_05bacf020c02fa246e62cb40bd91b88d345a25e9748055927569a867b2492a31_22009",
        "tid": "2a18bf5b8e8995893829aa508ef179477dbd9409e7cebda354e9af63a95c4bdc",
        "staff_info_id": 22009,
        "staff_info_name": "1",
        "staff_info_avatar_url": null,
        "current_time": 1593693882,
        "store_lat": 40.03025600, courier:app:backyard:head_office:lat
        "store_lng": 116.41045300, courier:app:backyard:head_office:lng
        "field_punch": false,//是否可外勤打卡 (Fullfillment部门的在白名单的可以外勤打卡) java 配置表 courier:app:backyard:field_punch_staff
        "wage_display": true
         */

//缺失字段  name mobile organization_name store_category positions
        $user = [
            'sessionid' => $session_id,
            'tid' => $tid,
            'staff_info_id' => $info['id'],
            'staff_info_name' => $info['name'],
            'organization_id' => $info['organization_id'],
            'organization_type' => $info['organization_type'],
            'organization_name' => empty($info['organization_name']) ? '' : $info['organization_name'],
            'store_category' => empty($info['store_category']) ? '' : $info['store_category'],
            'position_category' => $info['position_category'],
            'department_id' => $info['department_id'],
            'job_title' => $info['job_title'],
            'current_time' => $time,
            'store_lat' => $info['store_lat'],
            'store_lng' =>  $info['store_lng'],
            'field_punch' =>  $info['field_punch'],
            'wage_display' =>  $info['wage_display'],

            'name' => $info['name'],
            'mobile' => $info['mobile'],
            'positions' => empty($info['position_category']) ? array() : explode(',',$info['position_category']),
        ];

        $cache->save($cache_key, json_encode($user),$cache_time);
        return $user;
    }


    //免登接口 入参 tid 和 session id  根据当前设备以及session 是否过期 判断是否可登陆
    public function re_loginAction(){
        http_response_code(399);
        $result = array('code' => -833,'message' => 'Validation failed','data'=> null);
        //获取设备
        $headerData       = $this->request->getHeaders();
        $device = $this->processingDefault($headerData, 'X-Device-Id');
        $token = $this->paramIn['sessionid'];
        $tid = $this->paramIn['tid'];
        $current_version = $this->paramIn['version'];
        if(empty($token) || empty($tid))
            return $this->jsonReturn($result);
        $str = $this->getDI()['config']['application']['authenticate'];
        //验证单点 不同设备登陆 只有一个有效
        $check_tid = $this->crypt_tid($str,$device);
        if($tid != $check_tid)
            return $this->jsonReturn($result);

        //验证 session
        $cache = $this->getDI()->get('redis');
        $arr = explode('_', $token);
        $time     = $arr[0];
        $staff_id = $arr[2];
        $auth     = sha1(md5($time . $str . $staff_id));

        if ($auth != $arr[1])
            return $this->jsonReturn($result);


        //验证单点登陆 不同设备 只能一台有效
        $server = new LoginServer($this->lang,$this->timezone);
        $check_device = $server->check_device($staff_id, $device);
        if(!$check_device){
            return $this->jsonReturn(array('code' => 100111, 'message' => 'you have changed device,please login again'));
        }

        //版本号不同 需强制升级
        $latest_version = env('client_version');
        if(!empty($latest_version) && $latest_version != $current_version){
            $data['version_info'] = array(
                'version' => $latest_version,
                'current_time' => time(),
                'time_interval' => null,
                'download_url' => env('app_download_url'),
            );
            return $this->jsonReturn(array('code' => 100101, 'message' => 'your app need update', 'data' => $data));
        }

        $cache_key = md5($auth);
        $res = $cache->get($cache_key);
        //验证后获取缓存信息
        if (empty($res))
            return $this->jsonReturn(array('code' => -3000,'message' => 'token expired','data'=> null));
        $data_list['code'] = 1;
        $data_list['message'] = 'success';
        $data_list['data'] = json_decode($res, true);
        //更新当前时间戳
        $data_list['data']['current_time'] = time();
        $this->getDI()->get('logger')->write_log($data_list, 'info');

        //返回值
        http_response_code(200);
        return $this->jsonReturn($data_list);

    }

    //生成 TID

    /**
     * @param $key 私钥密码
     * @param $client_id 设备码 089A386A-E54F-4899-B1F5-1C0C19854EE7
     * @param string $equipment_type 设备源
     * @return string
     */
    protected function crypt_tid($key,$client_id,$equipment_type = 'backyard'){
        /**java
         * 生成TID a5370d9952b5fed6de59a19bfc3c13bd0a84325490dfcb4f4405eb3efb91b28b
         * public String generateTID(String clientId, String clientSd, Instant time, EquipmentTypeCategory equipmentType) {
        StringBuilder builder = new StringBuilder(INITIALIZE_STRING_BUFFER);
        StringBuilder append = builder.append(clientId)
        .append(tidFirstSalt)
        .append(clientSd)
        .append("kylin")
        .append(time.getEpochSecond());
        Optional.ofNullable(equipmentType).ifPresent(e -> append.append(e.getValue()));
        return DigestUtils.sha256HexDigest(append.toString());
         */

        $str = $client_id . $key  . $equipment_type;
        return hash('sha256',$str);

    }


    public function token_clearAction(){

    }


    public function downloadUrlAction()
    {
        $settingEnvServer =
            new SettingEnvServer();

        $android =
            $settingEnvServer->getSetVal('ALL_URL_ANDROID_3');
        $ios =
            $settingEnvServer->getSetVal('url_ios_download');

        $this->jsonReturn($this->checkReturn(['data' => ['android' => $android, 'ios' => $ios]]));
    }

    public function resetPasswordAction()
    {
        $paramIn = $this->paramIn;

        $add_hour = $this->getDI()['config']['application']['add_hour'];
        if (gmdate('Y-m-d', time() + $add_hour * 3600) <= '2022-09-01') {

            //[3]获取outsourcing staff订单详情
            $ac = new ApiClient('hr_rpc', '', 'reset_password', $this->lang);
            $ac->setParams([
                'staff_info_id' => base64_decode($paramIn['staff_info_id']),
                'fbid' => base64_decode($paramIn['staff_info_id']),
            ]);
            $ret = $ac->execute();
            if ($ret && isset($ret['code']) && $ret['code'] == 1) {

                $this->jsonReturn($this->checkReturn(1));
            }

            $this->jsonReturn($this->checkReturn(-3));
        } else {

            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4034')));
        }

    }




}
