<?php
namespace FlashExpress\bi\App\Controllers\Pay;
use FlashExpress\bi\App\Server\FlashPayServer;

/**
 * 员工商城～FlashPay支付回调
 */
class InteriorgoodsController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * flash pay同步回调接口
     *
     * @api https://yapi.flashexpress.pub/project/93/interface/api/45883
     * @return mixed
     */
    public function payTradeSyncAction()
    {
        $paramIn = $this->paramIn;
        $uri = $_SERVER['REQUEST_URI'];

        $flash_pay_server = new FlashPayServer($this->lang, $this->timezone);
        $code = $flash_pay_server->payTradeSync($paramIn);
        $uri_arr = explode("/", $uri);
        $platform = array_pop($uri_arr);
        $lang = array_pop($uri_arr);
        $redirect_url = env('sign_url').'/#/shopTransitPage?code='.$code.'&lang='.$lang.'&platform='.$platform;
        return $this->response->redirect($redirect_url);
    }

    /**
     * flash pay异步回调接口
     * @api https://yapi.flashexpress.pub/project/93/interface/api/45841
     * @throws \Exception
     */
    public function payTradeNoSyncAction()
    {
        $paramIn = $this->paramIn;
        $flash_pay_server = new FlashPayServer($this->lang, $this->timezone);
        $result = $flash_pay_server->payTradeNoSync($paramIn);
        return $this->jsonReturn($result);
    }
}