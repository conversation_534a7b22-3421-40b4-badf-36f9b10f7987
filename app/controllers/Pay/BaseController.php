<?php
namespace FlashExpress\bi\App\Controllers\Pay;

use FlashExpress\bi\App\Controllers;
/**
 * Class PayController
 * @package App\Controllers
 */
class BaseController extends Controllers\ControllerBase
{
    public $paramIn;
    public function initialize(){
        header('traceid:' .  molten_get_traceid());
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn)) {
            $this->paramIn = json_decode($this->request->getRawBody(), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }
}