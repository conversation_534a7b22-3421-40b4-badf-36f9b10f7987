<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\HrStaffContractEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\CounselorServer;
use FlashExpress\bi\App\Server\HireTypeChangeServer;
use FlashExpress\bi\App\Server\HrStaffContractServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\ResumeServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Repository\InteriorGoodsRepository;
use FlashExpress\bi\App\Server\WorkdayServer;


class HrController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);

    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 获取到岗确认列表
     * @param int    status             1=已入职, 2=待入职, 3=未入职
     * @param int    page_size          页面个数
     * @param int    page_num           页数
     * @return string
     */
    public function getEntryListAction()
    {
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['store_id'] = $this->userinfo['organization_id'];
        $logger              = $this->getDI()->get('logger');
        $method              = "getEntryList";
        $param               = [
            'worknode_id' => $paramIn['store_id'],
            'status'      => $paramIn['status'],
            'staff_id'    => $paramIn['staff_id'],
            'page_size'   => $paramIn['page_size'] ?? 30,
            'page_num'    => $paramIn['page_num'] ?? 1,
        ];

        //[2]验证
        $validations = [
            "status" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        if($param['status'] == 2) {//待入职列表
            //[3]发送请求
            $logger->write_log("svc method {$method} param:" . json_encode($param), 'info');

            $fle_rpc = (new ApiClient("winhr_rpc",'', $method, $this->lang));
            $fle_rpc->setParams($param);
            $result = $fle_rpc->execute();

            if ($result['result']['code'] == 1) {
                if (isCountry(['TH','PH','MY']) && !empty($result['result']['data']['dataList'])) {
                    $result['result']['data']['dataList'] = (new StaffServer($this->lang))->replace_entry_data($result['result']['data']['dataList']);
                }
                $this->jsonReturn($this->checkReturn(['data' => $result['result']['data']]));
            }
        }
        $service =  Tools::reBuildCountryInstance( new StaffServer($this->lang), [$this->lang]);
        $result = $service->getEntryList($param);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 获取到岗确认详情
     * @param int    entry_id  入职ID
     * @return string
     */
    public function getEntryDetailAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;
        $staffId = $this->userinfo['staff_id'];
        $logger  = $this->getDI()->get('logger');
        $method  = "getEntryDetail";

        //[2]验证
        $validations = [
            "resume_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $param   = [
            'userinfo'  => ['id' => $staffId],
            'resume_id' => $paramIn['resume_id'],
        ];

        //[3]发送请求
        $logger->write_log("svc method {$method} param:" . json_encode($param), 'info');

        $fle_rpc = (new ApiClient("winhr_rpc",'',$method, $this->lang));
        $fle_rpc->setParams($param);
        $result = $fle_rpc->execute();

        if ($result['result']['code'] == 1) {
            if (isCountry(['TH','PH','MY']) && !empty($result['result']['data'])) {
                $result['result'] = (new StaffServer($this->lang))->replace_entry_data($result['result']);
            }
            
            //性别转义
            $result['result']['data']['sex_title'] =
                $result['result']['data']['sex'] == \FlashExpress\bi\App\library\enums::$sex_type['male']
                ? $this->getTranslation()->_('4900')
                : $this->getTranslation()->_('4901');
            
            // https://flashexpress.feishu.cn/wiki/SbiIwNq3YiCfdKkjeYccklxKnGe
            // 前端用这个结构
            $result['result']['data']['userinfo']['staff_id'] = $this->userinfo['staff_id'] ?? '';
            $result['result']['data']['userinfo']['name'] = $this->userinfo['name'] ?? '';
            $this->jsonReturn($this->checkReturn([
                'data' => $result['result']['data']
            ]));
        } else {
            $resultMsg = $result['result']['msg'] ?? $this->getTranslation()->_('4008');
            $this->jsonReturn(self::checkReturn(-3, $resultMsg));
        }

    }

    /**
     * 到岗确认
     * @param int   entry_id  入职ID
     * @param int   status    1-到岗 2-未到岗
     * @return string
     */
    public function entryConfirmAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;
        $staffId = $this->userinfo['staff_id'];
        $logger  = $this->getDI()->get('logger');

        $param   = [
            'resume_id' => $paramIn['resume_id'],
            'staff_id'  => $staffId,

            // 辅导员ID
            'instructor_id' => !empty($paramIn['counselor_staff_id']) ? $paramIn['counselor_staff_id'] : '',
            'shift_id'  => $paramIn['shift_id'],
            'identity_status' => !empty($paramIn['identity_status']) ? $paramIn['identity_status'] : '',
            'reason_code'     => !empty($paramIn['reason_code']) ? $paramIn['reason_code'] : [],
            'reason_remark'   => !empty($paramIn['reason_remark']) ? $paramIn['reason_remark'] : '',
        ];

        //[2]验证
        $validations = [
            "resume_id" => "Required|Int",
            "status"    => "Required|IntIn:1,2",
            "shift_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);


        // [2.1] 验证辅导工号是否存在
        if (!empty($param['instructor_id'])) {
            $exist_counselor_info = (new HrStaffInfoServer())::getUserInfoByStaffInfoId($param['instructor_id'], 'id');
            if (empty($exist_counselor_info->id)) {
                return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('counselor_not_exist')));
            }
        }

        // [2.2] 到岗 - 绑定辅导员校验
        if ($paramIn['status'] == 1) {
            $method = 'entryAddCounselorCheck';

            $logger->write_log("svc method {$method} param:" . json_encode($param), 'info');

            $fle_rpc = (new ApiClient("winhr_rpc",'',$method, $this->lang));
            $fle_rpc->setParams($param);
            $result = $fle_rpc->execute();


            $logger->write_log("svc method {$method} response:" . json_encode($result), 'info');

            if ($result['result']['code'] == 0) {
                $this->jsonReturn(self::checkReturn(-3, $result['result']['msg']));
            }
        }

        if (isCountry("MY")){
            //校验轮休默认休息日&赋值
            $param['default_rest_day_date'] = [];
            if ($paramIn['status'] == 1 && isset($paramIn['working_day_rest_type'],$paramIn['default_rest_day_date'])) {
                if ($paramIn['working_day_rest_type'] == HrStaffInfoModel::WEEK_WORKING_DAY_6. HrStaffInfoModel::REST_TYPE_1
                    && count((array)$paramIn['default_rest_day_date'])!=1) {
                    $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('choose_default_rest_day_1')));
                }
                if ($paramIn['working_day_rest_type'] == HrStaffInfoModel::WEEK_WORKING_DAY_5. HrStaffInfoModel::REST_TYPE_1
                    && count((array)$paramIn['default_rest_day_date'])!=2) {
                    $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('choose_default_rest_day_2')));
                }
                if (in_array($paramIn['working_day_rest_type'],[
                    HrStaffInfoModel::WEEK_WORKING_DAY_6. HrStaffInfoModel::REST_TYPE_1,
                    HrStaffInfoModel::WEEK_WORKING_DAY_5. HrStaffInfoModel::REST_TYPE_1,
                    HrStaffInfoModel::WEEK_WORKING_DAY_FREE. HrStaffInfoModel::REST_TYPE_1,
                ])) {
                    $param['default_rest_day_date'] = (array) $paramIn['default_rest_day_date'];
                }
            }
        }

        //[3]发送请求
        if ($paramIn['status'] == 1) {
            $method  = "entryAdd";
        } else {
            $method  = "entryCancel";
        }
        $logger->write_log("svc method {$method} param:" . json_encode($param), 'info');

        $param['source'] = 'by';//区分到岗确认来源

        $fle_rpc = (new ApiClient("winhr_rpc",'',$method, $this->lang));
        $fle_rpc->setParams($param);
        $result = $fle_rpc->execute();
        if ($result['result']['code'] == 1) {
            $this->jsonReturn($this->checkReturn(['data' => $result['result']['data']]));
        } else {
            $result = $result['result']['msg'] ?? $this->getTranslation()->_('4008');
            $logger->write_log("svc method {$method} response:" . json_encode($result), 'info');
            $this->jsonReturn(self::checkReturn(-3, $result));
        }
    }

    /**
     * 辅导员搜索
     *
     * @param int worknode_id  网点ID
     * @param string keyword   搜索关键词
     *
     * @retun string json
     */
    public function counselorSearchAction()
    {
        // [1] 参数定义
        $paramIn = $this->paramIn;
        $staffId = $this->userinfo['staff_id'];
        $logger  = $this->getDI()->get('logger');

        $param   = [
            'sys_store_id' => $paramIn['worknode_id'],
            'keyword' => trim($paramIn['keyword']),
            'show_num' => $paramIn['show_num'] ?? 20,
            'staff_id'  => $staffId,
        ];

        try {
            // [2] 录入参数格式校验
            $validations = [
                "sys_store_id" => "Required|StrLen:10|>>>:" . $this->getTranslation()->_('4010'),
                "keyword"    => "Required|StrLenGe:1|>>>:" . $this->getTranslation()->_('counselor_option_cannot_null'),
            ];

            $this->validateCheck($param, $validations);

            // [3] 数据获取
            $param['fields'] = 'staff_info_id,name,sys_store_id,hire_date,job_title';
            $result = (new HrStaffInfoServer($this->lang,$this->timezone))->getStaffListBySearch($param);

            $data = [];

            // [4] 输出格式调整
            if (!empty($result)) {
                $jobIds = array_values(array_filter(array_column($result, 'job_title')));
                $jobs = HrJobTitleModel::find([
                    'conditions' => 'id in ({ids:array})',
                    'bind' => ['ids' => $jobIds]
                ])->toArray();
                $jobs = array_column($jobs, null, 'id');
                $add_hour = $this->getDI()['config']['application']['add_hour'];
                foreach ($result as $staff) {
                    $data[] = [
                        'counselor_staff_id' => $staff['staff_info_id'],
                        'counselor_staff_name' => $staff['name'],
                        'counselor_value' => '（'.$staff['staff_info_id'].'）'.$staff['name'],
                        'counselor_job_name' =>  isset($jobs[$staff['job_title']]) ? $jobs[$staff['job_title']]['job_name'] : '',
                        'counselor_hire_days' =>  floor((time() - strtotime($staff['hire_date'])) / 86400),
                    ];
                }
            }

            return $this->jsonReturn($this->checkReturn(['data' => $data]));
        } catch (Exception $e) {
            $logger->write_log("backyard hr counselorSearch param:" . json_encode($param) . " error message :" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取员工合同
     */
    public function staffContractAction()
    {
        $method = "getStaffContract";
        $staff_info_id =  $this->userinfo['staff_id'];
        //查看支援信息
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($this->userinfo['staff_id']);
        if ($supportStaffInfo) {
            $staff_info_id = $supportStaffInfo['staff_info_id'];
        }
        $param = ['staff_id' => $staff_info_id];
        //归档消息的
        if ((isCountry('PH') || isCountry('TH')) && !empty($this->paramIn['msg_id'])) {
            $server = new BackyardServer($this->lang, $this->timezone);
            $data   = $server->getMessageCourierDetail([
                'msg_id'        => $this->paramIn['msg_id'],
                'staff_info_id' => [$staff_info_id, $this->userinfo['staff_id']],
            ]);
            $param['ct_id'] = $data['related_id'] ?? '';
        }

        //[3]发送请求
        $fle_rpc = (new ApiClient("winhr_rpc", '', $method, $this->lang));
        $fle_rpc->setParams($param);
        $result = $fle_rpc->execute();

        if ($result && $result['result']['code'] == 1) {
            $contract_data = $result['result']['data'];
            $contract_data['wait_sign_num'] = 0;
            if (isCountry(['TH','PH','MY'])) {
                $contract_data['wait_sign_num'] = HrStaffContractServer::getInstance($this->lang, $this->timezone)->getWaitSingContractNum([$staff_info_id]);
            }

            return $this->jsonReturn($this->checkReturn([
                'data' => $contract_data,
            ]));
        }

        return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
    }

    /**
     * 拒绝签署合同
     * @return array
     */
    public function contractRefuseAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "ct_id"          => "Required|IntGt:0",
            "operate_reason" =>  "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('min_max_char_len', ['min' => 1,'max' => 500]),
        ];

        $this->validateCheck($paramIn, $validations);

        //操作
        $res = HrStaffContractServer::getInstance($this->lang,$this->timezone)->doContractSignAndFeedbackUseLock($paramIn,HrStaffContractEnums::CONTRACT_OPT_TYPE_REFUSE, $this->userinfo['id']);


        $returnArr['data'] = ['contract_data' => $res];

        return $this->jsonReturn(self::checkReturn($returnArr));
    }

    /**
     * 电子合同签字提交接口
     */
    public function contractSignAction(){

        $paramIn             = $this->paramIn;
        $validations         = [
            "ct_id"    => "Required|IntGt:0",
            "sign_img" => "Required|StrLenGeLe:1,200",
        ];
        $this->validateCheck($paramIn, $validations);

        $signResult = HrStaffContractServer::getInstance($this->lang,$this->timezone)->doContractSignAndFeedbackUseLock($paramIn,1, $this->userinfo['id']);

        //重新获取合同最新数据返回
        $res =  HrStaffContractServer::getInstance($this->lang,$this->timezone)->getContractDetail($paramIn['ct_id'], $this->userinfo['id']);
        $res['wait_sign_num'] = $signResult['wait_sign_num'];


        $returnArr['data'] = ['contract_data'=>$res];

        return $this->jsonReturn(self::checkReturn($returnArr));


    }

    /**
     * 电子反馈接口
     */
    public function contractFeedbackAction(){

        $paramIn             = $this->paramIn;
        $validations         = [
            "ct_id"    => "Required|IntGt:0",
        ];
        $this->validateCheck($paramIn, $validations);
        //处理合同状态和消息是否已读，如果有错误则抛出异常
        HrStaffContractServer::getInstance($this->lang,$this->timezone)->doContractSignAndFeedbackUseLock($paramIn,2,$this->userinfo['staff_id']);

        //重新获取合同最新数据返回
        $contract_detail = HrStaffContractServer::getInstance($this->lang,$this->timezone)->getContractDetail($paramIn['ct_id'], $this->userinfo['staff_id']);
        $returnArr['data'] = ['contract_data'=>$contract_detail];

        return $this->jsonReturn(self::checkReturn($returnArr));

    }

    /**
     * 是否有待签字合同
     */
    public function isShowContractSignBtnAction(){
        $paramIn             = $this->paramIn;
        $validations         = [
            "msg_id"    => "Required|StrLenGeLe:10,100",
        ];
        $this->validateCheck($paramIn, $validations);
        $retunArr = HrStaffContractServer::getInstance($this->lang,$this->timezone)->isShowSignBtn($this->userinfo['staff_id'],$paramIn['msg_id']);
        return $this->jsonReturn($retunArr);
    }

    /**
     * @description 获取个人代理入职列表
     * status 1=已入职, 2=待入职, 3=未入职
     */
    public function getAgentEntryListAction()
    {
        $paramIn = $this->paramIn;
        $staffId = $this->userinfo['staff_id'];

        //[2]验证
        $validations = [
            'status'    => "Required|Int",
            'page_num'  => "Required|Int",
            'page_size' => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //[3]返回入职列表
        $result = (new HireTypeChangeServer($this->lang, $this->timezone))->getAgentEntryList($paramIn, $staffId);
        $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    /**
     * @description 获取个人代理入职详情
     */
    public function getAgentEntryDetailAction()
    {
        $paramIn = $this->paramIn;
        //[2]验证
        $validations = [
            'id'    => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //[3]返回入职列表
        $result = (new HireTypeChangeServer($this->lang, $this->timezone))->getAgentEntryDetail($paramIn['id']);
        if ($result){
            // https://flashexpress.feishu.cn/wiki/SbiIwNq3YiCfdKkjeYccklxKnGe
            $result['userinfo']['staff_id'] = $this->userinfo['staff_id'] ?? '';
            $result['userinfo']['name']     = $this->userinfo['name'] ?? '';
        }
        $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    /**
     * 个人代理  lnt 到岗确认
     * @param int id        入职ID
     * @param int status    1-到岗 2-未到岗
     */
    public function agentEntryConfirmAction()
    {
        $paramIn = $this->paramIn;
        $staffId = $this->userinfo['staff_id'];

        //[2]验证
        $validations = [
            'id'       => "Required|Int",
            'status'   => "Required|Int",
            "shift_id" => 'IfIntEq:status,1|Int',
        ];
        $this->validateCheck($paramIn, $validations);

        //[3]返回入职列表
        /**
         * @see HireTypeChangeServer::agentEntryConfirm()
         */
        $result = (new HireTypeChangeServer($this->lang, $this->timezone))->agentEntryConfirmUseLock($paramIn, $staffId);
        $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    /**
     * 辅导员列表
     */
    public function getCounselorListAction()
    {
        $paramIn = $this->paramIn;

        $validations = [
            "worknode_id" => "Required|StrLen:10|>>>:" . $this->getTranslation()->_('4010'),
            "job_title"   => "StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('3008'),
        ];

        $this->validateCheck($paramIn, $validations);

        $service =  Tools::reBuildCountryInstance(new CounselorServer($this->lang));
        $result = $service->getList([
            'sys_store_id' => $paramIn['worknode_id'],
            'job_title'    => $paramIn['job_title'] ?? 0,
        ]);

        $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    /**
     * 到岗确认，身份证确认本人
     * 不同
     */
    public function identityStatusSubmitAction()
    {
        $paramIn = $this->paramIn;

        $validations = [
            "resume_id" => "Required|Int",
            "identity_status"   => "Required|IntIn:2,3|>>>:identity_status params error",
        ];

        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $method = 'identity_status_submit';

        $logger  = $this->getDI()->get('logger');
        $logger->write_log("svc method {$method} param:" . json_encode($paramIn), 'info');

        $fle_rpc = (new ApiClient("winhr_rpc",'',$method, $this->lang));
        $fle_rpc->setParams($paramIn);
        $result = $fle_rpc->execute();

        $logger->write_log("svc method {$method} response:" . json_encode($result), 'info');

        if ($result['result']['code'] != 1) {
            $this->jsonReturn(self::checkReturn(-3, $result['result']['msg']));
        }

        $this->jsonReturn(self::checkReturn(['data' => $result['result']['data']]));
    }
}
