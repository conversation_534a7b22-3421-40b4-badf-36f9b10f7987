<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\ReemploymentServer;

class ReemploymentController extends ControllerBase
{
    /**
     * @return void
     */
    public function auditAction()
    {
        $params = $this->paramIn;

        $validations = [
            "status"   => "Required|IntIn:2,3",
            "audit_id" => "Required|Int",
        ];
        $this->validateCheck($params, $validations);
        if ($params['status'] == enums::APPROVAL_STATUS_REJECTED && (empty($params['reject_reason']))) {
            $this->jsonReturn($this->checkReturn('-1', 'reject_reason'));
        }
        if ($params['status'] == enums::APPROVAL_STATUS_REJECTED) {
            if (mb_strlen($params['reject_reason']) > 500) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1020')));
            }
        }
        $returnArr = (new ReemploymentServer($this->lang, $this->timezone))->auditUseLock($params, $this->userinfo['staff_id']);
        $this->jsonReturn($returnArr);
    }
}