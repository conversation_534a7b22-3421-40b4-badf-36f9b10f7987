<?php

namespace FlashExpress\bi\App\Controllers;


use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\BusinesstripServer;

class BusinesstripController extends Controllers\ControllerBase
{

    public function initialize()
    {
        parent::initialize();
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 创建出差申请(不包含外出申请)
     * @Access  public
     * @Param   request
     * @Return  array
     * @throws ValidationException
     */
    public function addTripAction()
    {
        //[1]入参和验证
        $paramIn                  = $this->paramIn;
        $userinfo                 = $this->userinfo;
        $validations              = [
            "oneway_or_roundtrip" => "Required|IntIn:1,2|>>>:".$this->getTranslation()->_('7122'),
            "departure_city"      => "Required|StrLenGeLe:1,50|>>>:".$this->getTranslation()->_('7128'),
            "destination_city"    => "Required|StrLenGeLe:1,50|>>>:".$this->getTranslation()->_('7128'),
            "start_time"          => "Required|Date|>>>:".$this->getTranslation()->_('7124'),
            "end_time"            => "Required|Date|>>>:".$this->getTranslation()->_('7124'),
            "remark"              => "Required|StrLenGeLe:10,500|>>>:".$this->getTranslation()->_('7126'),
            "business_trip_type"  => "Required|IntIn:1,2,3,4|>>>:出差类型不正确",
        ];
        $paramIn['traffic_tools'] = isset($paramIn['traffic_tools']) ? $paramIn['traffic_tools'] : 0;
        //普通出差
        if ($paramIn['business_trip_type'] != BusinessTripModel::BTY_YELLOW) {
            $validations['traffic_tools']      = "Required|IntIn:1,2,3,4|>>>:".$this->getTranslation()->_('7122');
            $validations['reason_application'] = "Required|StrLenGeLe:10,50|>>>:".$this->getTranslation()->_('7127');
        } else {
            $validations['traffic_tools']           = "Required|IntIn:3,4|>>>:".$this->getTranslation()->_('7122');
            $validations['car_no']                  = "Required|StrLenGeLe:2,10|>>>:".$this->getTranslation()->_('2105');//车牌号不正确
            $validations['reason_application_type'] = "Required|IntIn:1,2|>>>:出差理由类型不正确";
        }
        //如果有其他交通工具，输入名称
        if ($paramIn['traffic_tools'] == 4) {
            $validations['other_traffic_name'] = "Required|StrLenGeLe:1,20|>>>:".$this->getTranslation()->_('7124');
        }

        $tripServer = new BusinesstripServer($this->lang, $this->timezone);

        //如果是境外出差 需要选择目的地国家
        if($paramIn['business_trip_type'] == BusinessTripModel::BTY_FOREIGN){

            $countryRegion = $tripServer->getEnumsList();
            $validations['destination_country'] = "Required|IntIn:".implode(",", array_column($countryRegion['destination_country'], 'value'))."|>>>:".$this->getTranslation()->_('destination_country_hint');
           //如果是目的地国家是其他 必填 国家名称
            if (isset($paramIn['destination_country']) && $paramIn['destination_country'] == HrStaffInfoModel::WORKING_COUNTRY_OTHER) {
                $validations['destination_country_name'] = 'Required|StrLenGeLe:1,55|>>>:'.$this->getTranslation()->_('destination_country_name_hint');
            }
        }

        $this->validateCheck($paramIn, $validations);
        if ($paramIn['start_time'] > $paramIn['end_time']) {
            throw new ValidationException($this->getTranslation()->_('7125'));
        }
        
        if ($tripServer->checkTime($paramIn['start_time'], $paramIn['end_time'], $userinfo['id'])) {
            throw new ValidationException($this->getTranslation()->_('business_trip_check_time'));
        }
        $returnArr = $tripServer->setLockConf(10)->addTripUseLock($paramIn, $userinfo);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 审批通过的出差 撤销时候的验证
     * @return void
     * @throws ValidationException
     */
    public function beforeCancelCheckAction()
    {
        $paramIn = $this->paramIn;
        //状态 1 待审核 2 审核通过 3 驳回 4 撤销
        $validations = [
            "status"   => "Required|Required|IntIn:1,2,3,4|>>>:" . $this->getTranslation()->_('miss_args'),
            "audit_id" => "Required|Required|Int|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $tripServer    = new BusinesstripServer($this->lang, $this->timezone);
        $paramIn['id'] = $paramIn['audit_id'];
        $tripServer->beforeCancelCheck($paramIn);
        $this->jsonReturn(self::checkReturn(1));
    }


    /**
     * 出差审批
     * @Access  public
     * @Param   request
     * @Return  Array
     */
    public function checkTripAction()
    {
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;
        //状态 1 待审核 2 审核通过 3 驳回 4 撤销
        $validations = [
            "status"   => "Required|Required|IntIn:1,2,3,4|>>>:".$this->getTranslation()->_('miss_args'),
            "audit_id" => "Required|Required|Int|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        //如果驳回，验证驳回理由
        if ($paramIn['status'] == 3) {
            $validations['reject_reason'] = "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('1020');
        }
        $this->validateCheck($paramIn, $validations);
        $tripServer = new BusinesstripServer($this->lang, $this->timezone);
        //id 和驳回理由转换
        $paramIn['id']     = $paramIn['audit_id'];
        $paramIn['reason'] = empty($paramIn['reject_reason']) ? '' : $paramIn['reject_reason'];
        $res               = $tripServer->updateTripStatusUseLock($paramIn, $userinfo);

        if ($res === true) {
            $returnArray['data'] = $paramIn['id'];
            //[3]成功数据返回
            $this->jsonReturn(self::checkReturn($returnArray));
        } else {
            //[3]异常数据返回
            $this->jsonReturn(self::checkReturn(-3, $res));
        }
    }

    /**
     * 出差类型列表
     */
    public function typeListAction()
    {
        $data = (new BusinesstripServer($this->lang, $this->timezone))->getTypeList();
        $this->jsonReturn(self::checkReturn(['data' => $data]));
    }

    /**
     * @description: 获取静态枚举
     * @param null
     * @return:
     * @author: L.J
     * @time: 2023/1/10 15:30
     */
    public function getEnumsListAction()
    {
        $data = (new BusinesstripServer($this->lang, $this->timezone))->getEnumsList();
        $this->jsonReturn(self::checkReturn(['data' => $data]));
    }


}
