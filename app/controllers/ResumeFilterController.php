<?php
namespace FlashExpress\bi\App\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Server\ResumeServer;

/**
 * 简历筛选
 */
class ResumeFilterController extends Controllers\ControllerBase
{
    /**
     * 列表
     */
    public function listAction()
    {
        try {
            $params = $this->paramIn;
            $this->validateCheck($params, [
                                     "type" => 'Required|IntIn:1,2,3',
                                     "sub_type" => 'IntIn:0,52,53',
                                     "page" => 'Required|Int',
                                     "size" => "Required|IntGeLe:1,20|>>>:" . "'page size' invalid input",
                                     "keyword" => "Str",
                                 ]);
            $params['staff_info_id'] = $this->userinfo['staff_id'];
            $list = (new ResumeServer($this->lang, $this->timezone))->getResumeFilterList($params);
            $this->jsonReturn($this->checkReturn(['data'=>$list]));
        } catch (BusinessException $b) {
            $this->jsonReturn($this->checkReturn(ErrCode::FAIL, $b->getMessage()));
        } catch (Exception $e) {
            $this->jsonReturn($this->checkReturn(ErrCode::FAIL, $e->getMessage()));
        }
    }

    /**
     * 详情
     */
    public function detailAction()
    {
        try {
            $params = $this->paramIn;
            $this->validateCheck($params, [
                                     "id" => 'Required|Str',
                                 ]);
            $params['staff_info_id'] = $this->userinfo['staff_id'];
            $detail = (new ResumeServer($this->lang, $this->timezone))->getResumeFilterDetail($params);
            $this->jsonReturn($this->checkReturn(['data'=>$detail]));
        } catch (BusinessException $b) {
            $this->jsonReturn($this->checkReturn(ErrCode::FAIL, $b->getMessage()));
        } catch (Exception $e) {
            $this->jsonReturn($this->checkReturn(ErrCode::FAIL, $e->getMessage()));
        }
    }

    /**
     * 筛选结果提交
     */
    public function saveAction()
    {
        try {
            $params = $this->paramIn;
            $this->validateCheck(
                $params,
                [
                     "filter_state" => 'Required|IntIn:52,53',
                     "id" => 'Required|Str',
                 ]
            );

            $params['staff_info_id'] = $this->userinfo['staff_id'];
            $params['name'] = $this->userinfo['name'];
            $resultData = $this->atomicLock(function () use ($params) {
                return  (new ResumeServer($this->lang, $this->timezone))->saveResumeFilterResult($params);
            }, 'resume_filter_' . $this->userinfo['staff_id']);
            if ($resultData === false) { //没有获取到锁
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('ticket_repeat_msg')));
            }
            $this->jsonReturn($this->checkReturn(ErrCode::SUCCESS));
        } catch (BusinessException $b) {
            $this->jsonReturn($this->checkReturn(ErrCode::FAIL, $b->getMessage()));
        } catch (Exception $e) {
            $this->jsonReturn($this->checkReturn(ErrCode::FAIL, $e->getMessage()));
        }
    }
}
