<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\RoyaltyServer;
use Exception;


class RoyaltyController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->server = ['royalty' => new RoyaltyServer($this->lang, $this->timezone, $this->userinfo)];
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode($this->request->getRawBody(), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        //记录访问日志
        $this->url_log($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 获取提出详细
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getRoyaltyInfoAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] =  $this->userinfo;
        $returnArr = $this->server['royalty']->getRoyaltyInfo($paramIn);
        $this->jsonReturn($returnArr);
    }
}