<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\WmsServer;
use FlashExpress\bi\App\library\HttpCurl;
use FlashExpress\bi\App\Server\SysStoreServer;
use Exception;

class WmsController extends Controllers\ControllerBase
{
    protected $server;

    public function initialize()
    {
        parent::initialize();
        $this->server = ['wms' => new WmsServer($this->lang, $this->timezone),
            'store' => new SysStoreServer($this->timezone)];
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 商品列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function goodsListAction()
    {

        //[1]入参
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        $returnArr = $this->server['wms']->wmsList($userinfo, $paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);

    }

    /**
     * 網點列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function storeListAction()
    {
        try {
            //[1]入参
            $paramIn = $this->paramIn;
            $userinfo = $this->userinfo;
            $organization_id = $userinfo['organization_id'];
            $store_category = $userinfo['store_category'];
            $positions = $userinfo['positions'];
            $staff_info_id = $userinfo['id'];
            $store_name = $paramIn['store_name'] ?? '';
            $admin_group = UC('wmsRole')['admin_group'];
            //[2]业务处理
            $returnArray =  [];

            if (isset($admin_group[$staff_info_id])) {
                if ($admin_group[$staff_info_id] == $staff_info_id) {
                    $admin_type = 3;
                    $returnArr = $this->server['store']->searchStore($store_name , 6);
                    foreach ($returnArr as $k => $v){
                        $returnArray[$k]['id'] = $v['id'];
                        $returnArray[$k]['name'] = $v['name'];
                    }
                }
            } else if ($store_category == \FlashExpress\bi\App\library\enums::$stores_category['hub']) {
                $admin_type = 2;
                $returnArr = $this->server['store']->getStoreByid($organization_id);
                $returnArray = [ 'id' => $returnArr['id'], 'name' => $returnArr['name'] ];

            } else {
                $admin_type = 1;
            }
            $returnData['data']['admin_type'] = $admin_type;
            $returnData['data']['dataList'] = $returnArray;
        } catch (\Exception $e) {
            $this->wLog('store-err', $e->getMessage() . json_encode($paramIn) . json_encode($userinfo), '>>>storeListAction');
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
        //[3]数据返回
        $this->jsonReturn($this->checkReturn($returnData));
    }

    /**
     * 商品推荐数量
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function goodsRecommentNumAction()
    {

        try {
            //[1]入参
            $paramIn = $this->paramIn;
            $userinfo = $this->userinfo;
            //[2]业务处理
            $returnArr['code'] = 1;
            $returnArr['msg'] = '请求成功!';

            $storeId = isset($paramIn['store_id']) && $paramIn['store_id'] ? $paramIn['store_id'] : $userinfo['organization_id'];
            $returnArr['data'] = (new WmsServer($this->lang, $this->timezone))->goodsRecommnetNumSV2($storeId, $paramIn);
            $returnArr['data'] = $this->processingDefault($returnArr, 'data', 2);
        } catch (\Exception $e) {
            $this->wLog('goodsRecommentNum', $e->getMessage() . json_encode($paramIn) . json_encode($userinfo), 'wms');
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
        //[3]数据返回
        $this->jsonReturn($returnArr);

    }

    /**
     * 商品订单创建
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addOrderAction()
    {
        try {
            //[1]入参
            $paramIn = $this->paramIn;
            $userinfo = $this->userinfo;
            $validations = [
                "reason_application" => "Required|StrLenGeLe:10,500|>>>:" . $this->getTranslation()->_('7102'),
            ];
            $this->validateCheck($paramIn, $validations);

            $barCodeArr = array_column($paramIn['goods_order_detail'], 'bar_code');
            if (count($barCodeArr) != count(array_unique($barCodeArr))) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('7103')));
            }
            $this->getDI()->get("logger")->write_log("wmsAddOrder: " . json_encode($paramIn, JSON_UNESCAPED_UNICODE), "info");
            //如果是网点经理或者主管
            $isManager = $this->server['wms']->isManagers($userinfo['id']);
            if ($isManager) {
                //每人每月可提交一次申请
                $existsWeek = $this->server['wms']->existsMonth($userinfo);
                if (sizeof($existsWeek) > 0) {
                      return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('7105')));
                }

                $paramIn['original_order_status'] = 1;
                $returnArr = $this->server['wms']->addOrderS($paramIn, $userinfo);

            } else {
                //original_order_status 是原来状态 order_status修改后状态
                $paramIn['original_order_status'] = 1;
                $returnArr = $this->server['wms']->addOrderS($paramIn, $userinfo);
            }
        } catch (\Exception $e) {
            $this->wLog('order_err', $e->getMessage() . json_encode($paramIn) . json_encode($userinfo), 'wms');
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('2109')));
        }
        $this->jsonReturn($returnArr);

    }

    /**
     * 商品订单审核
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkOrderAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        //兼容前端参数
        $paramIn['order_status'] = isset($paramIn['order_status']) ? $paramIn['order_status'] : $paramIn['status'];
        $paramIn['order_id'] = isset($paramIn['order_id']) ? $paramIn['order_id'] : $paramIn['audit_id'];

        //[2]业务处理
        try{
            $res = $this->server['wms']->updateWmsOrder($paramIn, $userinfo);
            if ($res) {
                $returnArray['data'] = $paramIn['order_id'];
            }
            //[3]数据返回
            $this->jsonReturn(self::checkReturn($returnArray));
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('wms_checkorder file ' . $e->getFile() . ' line ' . $e->getLine() . ' msg ' . $e->getMessage() , 'info');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
     * 网点员工列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function organizationUserAction()
    {

        //[1]入参
        $paramIn = $this->paramIn;
        //[2]业务处理
        $returnArr = $this->server['wms']->organizationUserS($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);

    }
}
