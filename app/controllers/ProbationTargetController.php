<?php
namespace FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetDetailModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetModel;
use FlashExpress\bi\App\Server\ProbationTargetServer;

class ProbationTargetController extends Controllers\ControllerBase
{
    protected $paramIn;
    protected $server;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->get();

        //会带个_url参数
        unset($this->paramIn['_url']);

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 试用期目标列表
     * @return void|null
     */
    public function listAction()
    {
        $params  = $this->paramIn;

        $validations = [
            "tab_setting_state" => "Required|IntIn:" . implode_array_keys(HrProbationTargetModel::$tab_setting_state_list) . "|>>>:" . $this->getTranslation()->_('miss_args'),
            "setting_state"     => "IntGeLe:0,10|>>>:" . $this->getTranslation()->_('miss_args'),
            "sign_state"        => "IntGeLe:0,10|>>>:" . $this->getTranslation()->_('miss_args'),
            "send_state"        => "IntGeLe:0,10|>>>:" . $this->getTranslation()->_('miss_args'),
        ];

        $this->validateCheck($params, $validations);
        $params['user_id'] = $this->userinfo['staff_id'];

        $data = (new ProbationTargetServer($this->lang, $this->timezone))->list($params);

        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }

    /**
     * 试用期目标列表
     * @return void|null
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = $this->paramIn;

        $validations = [
            "staff_info_id" => "Required|StrLenGeLe:1,10|>>>:" . $this->t->_('miss_args'),
        ];

        $this->validateCheck($params, $validations);
        $params['user_id'] = $this->userinfo['staff_id'];

        $data = (new ProbationTargetServer($this->lang, $this->timezone))->detail($params);

        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }

    /**
     * 试用期目标保存
     * @return void|null
     * @throws ValidationException
     */
    public function saveAction()
    {
        $params = $this->paramIn;

        $params['staff_info_id'] = (int)$params['staff_info_id'];
        $params['stage']         = (int)$params['stage'];
        $params['duty_info']     = trim($params['duty_info']);

        $validate = [
            'staff_info_id' => 'Required|IntGeLe:1,9999999999|>>>:' . $this->t->_('miss_args'),
            'duty_info'     => 'Required|StrLenGeLe:0,10000|>>>:' . $this->t->_('miss_args'),
            'stage'         => 'Required|IntIn:' . implode_array_keys(HrProbationTargetDetailModel::$stageList) . '|>>>:' . $this->t->_('miss_args'),
        ];

        if (!empty($params['target_info'])) {
            //清除空格
            foreach ($params['target_info'] as &$v) {
                $v['name']   = trim($v['name']);
                $v['info']   = trim($v['info']);
                $v['weight'] = trim($v['weight']);
            }

            $validate['target_info']           = 'ArrLenLe:100|>>>:' . $this->t->_('miss_args');
            $validate['target_info[*].name']   = 'StrLenGeLe:0,10000|>>>:' . $this->t->_('miss_args');
            $validate['target_info[*].info']   = 'StrLenGeLe:0,10000|>>>:' . $this->t->_('miss_args');
            $validate['target_info[*].weight'] = 'StrLenGeLe:0,3|>>>:' . $this->t->_('miss_args');
        }

        $this->validateCheck($params, $validate);
        $params['user_id'] = $this->userinfo['staff_id'];

        (new ProbationTargetServer($this->lang, $this->timezone))->saveUseLock($params);

        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => null]);
    }

    /**
     * 试用期目标保存
     * @return void|null
     * @throws ValidationException
     */
    public function saveSendAction()
    {
        $params = $this->paramIn;

        $params['staff_info_id'] = (int)$params['staff_info_id'];
        $params['stage']         = (int)$params['stage'];
        $params['duty_info']     = trim($params['duty_info']);

        $validate = [
            'staff_info_id' => 'Required|IntGeLe:1,9999999999|>>>:' . $this->t->_('miss_args'),
            'duty_info'     => 'Required|StrLenGeLe:1,10000|>>>:' . $this->t->_('miss_args'),
            'stage'         => 'Required|IntIn:' . implode_array_keys(HrProbationTargetDetailModel::$stageList) . '|>>>:' . $this->t->_('miss_args'),
        ];

        if (!empty($params['target_info'])) {
            //清除空格
            foreach ($params['target_info'] as &$v) {
                $v['name']   = trim($v['name']);
                $v['info']   = trim($v['info']);
                $v['weight'] = trim($v['weight']);
            }

            $validate['target_info']           = 'ArrLenLe:100|>>>:' . $this->t->_('miss_args');
            $validate['target_info[*].name']   = 'StrLenGeLe:1,10000|>>>:' . $this->t->_('miss_args');
            $validate['target_info[*].info']   = 'StrLenGeLe:1,10000|>>>:' . $this->t->_('miss_args');
            $validate['target_info[*].weight'] = 'IntGeLe:1,100|>>>:' . $this->t->_('miss_args');
        }

        $this->validateCheck($params, $validate);
        $params['user_id'] = $this->userinfo['staff_id'];

        (new ProbationTargetServer($this->lang, $this->timezone))->saveSendUseLock($params);

        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => null]);
    }

    /**
     * 试用期目标发送
     * @return void|null
     * @throws ValidationException
     */
    public function sendAction()
    {
        $params = $this->paramIn;

        $validations = [
            "staff_info_id" => "Required|IntGeLe:1,9999999999|>>>:" . $this->t->_('miss_args'),
        ];

        $this->validateCheck($params, $validations);
        $params['user_id'] = $this->userinfo['staff_id'];

        (new ProbationTargetServer($this->lang, $this->timezone))->sendUseLock($params);

        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => null]);
    }

    /**
     * 试用期目标列表
     * @return void|null
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $params = $this->paramIn;

        $validations = [
            "staff_info_id" => "Required|IntGeLe:1,9999999999|>>>:" . $this->t->_('miss_args'),
        ];

        $this->validateCheck($params, $validations);
        $params['user_id'] = $this->userinfo['staff_id'];

        (new ProbationTargetServer($this->lang, $this->timezone))->cancelUseLock($params);

        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => null]);
    }

    /**
     * 试用期目标列表
     * @return void|null
     * @throws ValidationException
     */
    public function viewSignUrlAction()
    {
        $params = $this->paramIn;

        $validations = [
            "staff_info_id" => "Required|IntGeLe:1,9999999999|>>>:" . $this->t->_('miss_args'),
        ];

        $this->validateCheck($params, $validations);
        $params['user_id'] = $this->userinfo['staff_id'];

        $data = (new ProbationTargetServer($this->lang, $this->timezone))->viewSignUrl($params);

        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }

    /**
     * 试用期目标红点接口
     * @return void|null
     */
    public function redCountAction()
    {
        $params['user_id'] = $this->userinfo['staff_id'];
        $data = (new ProbationTargetServer($this->lang, $this->timezone))->redCount($params);//接口展示
        return $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $data]);
    }
}