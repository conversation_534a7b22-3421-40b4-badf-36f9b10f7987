<?php

namespace FlashExpress\bi\App\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SuspendWorkServer;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\PersoninfoServer;
use FlashExpress\bi\App\Server\StaffServer;

class SuspendWorkController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * 添加暂停个人代理接单
     * @return void|null
     * @throws Exception
     */
    public function addSuspendWorkAction()
    {
        $param                    = $this->paramIn;
        $suspend_work_reason_type = (new SettingEnvServer())->getSetVal('suspend_work_reason_type');
        $suspend_work_reason_type = $suspend_work_reason_type ? json_decode($suspend_work_reason_type, true) : [];
        $validations              = [
            "staff_info_id"  => "Required|Int",
            "reason_type"    => "Required|IntIn:" . implode(",", array_keys($suspend_work_reason_type)),
            "reason_remarks" => "Required|StrLenGeLe:10,500|>>>:" . $this->getTranslation()->_('agent_suspend_work_reason_remarks_error'),
            "days"           => "Required|IntIn:1,2,3|>>>:days param error",
        ];
        $this->validateCheck($param, $validations);
        $param['submitter_id'] = $this->userinfo['staff_id'];
        $returnArr             = (new SuspendWorkServer($this->lang, $this->timezone))->addSuspendWorkUseLock($param);
        $this->jsonReturn($returnArr);
    }

    /**
     * 审批操作
     * @return void
     */
    public function auditAction()
    {
        $params = $this->paramIn;

        $validations = [
            "status"   => "Required|IntIn:2,3,4",
            "audit_id" => "Required|Int",
        ];
        $this->validateCheck($params, $validations);
        // 驳回
        if ($params['status'] == enums::APPROVAL_STATUS_REJECTED) {
            if (empty($params['reject_reason']) || mb_strlen($params['reject_reason']) > 500) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1020')));
            }
        }
        // 撤销
        if ($params['status'] == enums::APPROVAL_STATUS_CANCEL) {
            if (empty($params['reason']) || mb_strlen($params['reason']) > 500) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1030')));
            }
        }
        $returnArr = (new SuspendWorkServer($this->lang, $this->timezone))->auditUseLock($params,
            $this->userinfo['staff_id']);
        $this->jsonReturn($returnArr);
    }


    /**
     * 输入工号回显个人信息
     * @return void
     * @throws ValidationException
     */
    public function getStaffInfoAction()
    {
        $params      = $this->paramIn;
        $validations = [
            "staff_info_id" => "Required|Int",
        ];
        $this->validateCheck($params, $validations);
        $submitter_id          = $this->userinfo['staff_id'];
        $staff_info            = (new StaffServer($this->lang, $this->timezone))->get_staff($params['staff_info_id']);
        $staff_info            = $staff_info['data'] ?? [];
        $checkSubmitLimitation = (new SuspendWorkServer($this->lang,
            $this->timezone))->checkSubmitLimitation($staff_info, $submitter_id);
        if ($checkSubmitLimitation['is_submit'] === false) {
            throw new ValidationException($checkSubmitLimitation['tip']);
        }
        // 避免返回敏感信息
        $staff_info = array_intersect_key($staff_info,
            array_flip(['hire_type_text', 'position_name', 'store_name', 'department_name', 'staff_info_id', 'name']));
        $this->jsonReturn($this->checkReturn(['data' => $staff_info]));
    }

    /**
     * 暂停原因下拉
     * @return void
     */
    public function getSuspendWorkReasonTypeAction()
    {
        $data = (new SuspendWorkServer($this->lang, $this->timezone))->getSuspendWorkReasonType();
        $return_data = [];
        foreach ($data as $k=>$v){
            $return_data[] =  
                [
                    "value"  => $k,
                    "lable" => $v,
                ];
        }
        $this->jsonReturn($this->checkReturn(['data' => $return_data]));
    }
    
    
}
