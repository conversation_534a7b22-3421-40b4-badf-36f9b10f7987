<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\ReimbursementServer;

/**
 * 报销申请-相关操作控制器
 * @package FlashExpress\bi\App\Controllers
 */
class ReimbursementController extends Controllers\ControllerBase
{
    protected $paramIn;
    protected $reimbursementServer;

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();

        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }

        $this->reimbursementServer = new ReimbursementServer($this->lang, $this->timezone);
    }

    /**
     * 申请列表-筛选项枚举
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90011
     */
    public function getEnumsAction()
    {
        /**
         * @see reimbursementServer::getEnums()
         */
        $result = $this->reimbursementServer->getEnums($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 申请列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90017
     */
    public function getApplyListAction()
    {
        /**
         * @see reimbursementServer::getApplyList()
         */
        $result = $this->reimbursementServer->getApplyList($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 签字提醒
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90062
     */
    public function signatureReminderAction()
    {
        /**
         * @see reimbursementServer::signatureReminder()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(3, true)->signatureReminderUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 确认详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90065
     */
    public function confirmedDetailAction()
    {
        /**
         * @see reimbursementServer::confirmedDetail()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(5, true)->confirmedDetailUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 提交
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90065
     */
    public function submitAction()
    {
        /**
         * @see reimbursementServer::submit()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(20, true)->submitUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 撤回
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90071
     */
    public function cancelAction()
    {
        /**
         * @see reimbursementServer::cancel()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(5, true)->cancelUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 下载
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90074
     */
    public function downloadAction()
    {
        /**
         * @see reimbursementServer::download()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(5, true)->downloadUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 查看/查看详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90077
     */
    public function viewAction()
    {
        /**
         * @see reimbursementServer::view()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(1, true)->viewUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 去确认信息页
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90080
     */
    public function goConfirmInfoAction()
    {
        /**
         * @see reimbursementServer::goConfirmInfo()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(1, true)->goConfirmInfoUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 拒绝确认
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90086
     */
    public function rejectConfirmAction()
    {
        /**
         * @see reimbursementServer::rejectConfirm()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(3, true)->rejectConfirmUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 确认无误
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90089
     */
    public function agreeConfirmAction()
    {
        /**
         * @see reimbursementServer::agreeConfirm()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(5, true)->agreeConfirmUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 作废
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90095
     */
    public function invalidAction()
    {
        /**
         * @see reimbursementServer::invalid()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(3, true)->invalidUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 去签字信息页
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90101
     */
    public function goSignInfoAction()
    {
        /**
         * @see reimbursementServer::goSignInfo()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(2, true)->goSignInfoUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 拒绝签字
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90119
     */
    public function rejectSignAction()
    {
        /**
         * @see reimbursementServer::rejectSign()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(2, true)->rejectSignUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 获取申请人身份证信息
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90122
     */
    public function getApplyIDInfoAction()
    {
        /**
         * @see reimbursementServer::getApplyIDInfo()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(2, true)->getApplyIDInfoUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 签字提交
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90125
     */
    public function submitSignInfoAction()
    {
        /**
         * @see reimbursementServer::submitSignInfo()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(10, true)->submitSignInfoUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 去签字鉴权
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90269
     */
    public function checkSignAuthAction()
    {
        /**
         * @see reimbursementServer::checkSignAuth()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(1, true)->checkSignAuthUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 去确认鉴权
     * @api https://yapi.flashexpress.pub/project/93/interface/api/90272
     */
    public function checkConfirmAuthAction()
    {
        /**
         * @see reimbursementServer::checkConfirmAuth()
         * @see reimbursementServer::setLockConf()
         */
        $result = $this->reimbursementServer->setLockConf(1, true)->checkConfirmAuthUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

}
