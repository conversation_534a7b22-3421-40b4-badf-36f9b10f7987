<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\backyard\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Modules\My\Server\AttendanceServer;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Server\PublicHolidayServer;
use FlashExpress\bi\App\Server\StaffSupportStoreServer;
use FlashExpress\bi\App\Server\WorkbenchServer;
use WebGeeker\Validation\Validation;
use FlashExpress\bi\App\Server\StaffServer;
use Exception;

/**
 * by 对外提供服务
 * Class InterfaceController
 */
class InterfaceController extends \FlashExpress\bi\App\Core\PhalBaseController
{
    public $timezone;

    //全局参数获取
    protected $paramIn = null;

    public function initialize()
    {
        if(function_exists('molten_get_traceid')){
            $traceid = molten_get_traceid();
        }
        header('traceid:' . ($traceid ?? ''));
        $this->timezone = $this->getDI()['config']['application']['timeZone'];
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn)){
            $this->paramIn = json_decode($this->request->getRawBody(), true);
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 查找预计排班的人
     */
    public function workStaffByStaffsAction()
    {
        $paramIn             = $this->paramIn;
        $validations = [
            "staff_ids" => "Required|Str",
            "date" => "Date",
        ];
        try {
            Validation::validate($paramIn, $validations);
            $date = date("Y-m-d");
            if(!empty($paramIn['date'])){
                $date = $paramIn['date'];
            }
            $returnArr = (new StaffServer())->WorkOnStaffs($paramIn,$date);
            $this->logger->write_log($returnArr,'info');
            return $this->returnJson(ErrCode::SUCCESS,'ok',$returnArr);
        } catch (\Exception $e) {
            $this->logger->write_log($e->getMessage(),'info');
            return $this->returnJson(ErrCode::ERROR,$e->getMessage(),[]);
        }
        //[3]数据返回
    }


    /**
     * 查找请假状态
     */
    public function leaveByStaffAction()
    {
        $paramIn             = $this->paramIn;
        $validations = [
            "staff_id" => "Required|Str",
            "date" => "Date",
        ];
        try {
            Validation::validate($paramIn, $validations);
            $this->logger->write_log($paramIn,'info');
            $date = date("Y-m-d");
            if(!empty($paramIn['date'])){
                $date = $paramIn['date'];
            }
            $returnArr = (new StaffServer())->StaffListLeave($paramIn['staff_id'],$date);
            $bool = true;
            $this->logger->write_log($returnArr,'info');
            return $this->returnJson(ErrCode::SUCCESS,'ok',$returnArr ? true:false);
        } catch (\Exception $e) {
            $this->logger->write_log($e->getMessage(),'info');
            return $this->returnJson(ErrCode::SUCCESS,'error',false);
        }
        //[3]数据返回
    }


    /**
     * 工作台地址
     */
    public function workbenchAction()
    {
        $paramIn             = $this->paramIn;
        $validations = [
            "organization_id" => "Str",
            "device_id" => "Str",
        ];
        try {
            Validation::validate($paramIn, $validations);
            $paramIn['from_kit'] = true;
            $returnArr = (new WorkbenchServer('en', $this->timezone))->getInfo($paramIn);
            $this->logger->write_log(['returnArr'=>$returnArr,'paramIn'=>$paramIn],'info');
            return $this->returnJson(ErrCode::SUCCESS,'ok',$returnArr);
        } catch (Exception $e) {
            $this->logger->write_log($e->getMessage(),'info');
            return $this->returnJson(ErrCode::SUCCESS,'error',false);
        }
    }
    /**
     * @description:获取 补卡 请假  学习计划 出勤统计 和 工作台地址
     * <AUTHOR> L.J
     * @time       : 2022/10/11 19:11
     */
    public function  get_menuAction(){
        $paramIn     = $this->paramIn;
        $validations = [
            'organization_id' => 'Str',     //网点 id
            'device_id'       => 'Str',
            "staff_id"        => 'Str',          //用户 id
            "lang"            => 'Str',         //当前登录语言
        ];
        Validation::validate($paramIn, $validations);
        $paramIn['from_kit'] = true;
        $returnArr = (new WorkbenchServer($paramIn['lang'], $this->timezone))->getMenuFromCache($paramIn);
        $this->logger->write_log(['returnArr'=>$returnArr,'paramIn'=>$paramIn],'info');
        return $this->returnJson(ErrCode::SUCCESS,'ok',$returnArr);

    }

    public function getPublicHolidayAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            'start_date'      => 'Required|Date',
        ];
        Validation::validate($paramIn, $validations);

        $server = new PublicHolidayServer();
        $server = Tools::reBuildCountryInstance($server, [$paramIn['lang'], $this->timezone]);
        $returnArr = $server->getHolidays($paramIn);
        $this->logger->write_log(['returnArr'=>$returnArr,'paramIn'=>$paramIn],'info');
        return $this->returnJson(ErrCode::SUCCESS,'ok',$returnArr);
    }

    /**
     * 获取支援中的数据
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws Exception
     */
    public function getStaffSupportInfoAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            'staff_info_id' => 'Required|Required|IntGtLt:0,1000000000',
            'date_at'       => 'Required|Date',
        ];
        Validation::validate($paramIn, $validations);

        $server           = new StaffSupportStoreServer();
        $supportStaffInfo = $server->getStaffSupportingInfo($paramIn['staff_info_id'], $paramIn['date_at']);
        $this->logger->write_log(['returnArr' => $supportStaffInfo, 'paramIn' => $paramIn], 'info');
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $supportStaffInfo);
    }

    public function getStaffInfoByDepartmentJobTitleAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            'query_info'=> 'Required|Arr|ArrLenGe:1',
        ];
        Validation::validate($paramIn, $validations);

        $server           = new StaffServer();
        $supportStaffInfo = $server->getStaffInfoByDepartmentJobTitle($paramIn['query_info']);
        $this->logger->write_log(['returnArr' => $supportStaffInfo, 'paramIn' => $paramIn], 'info');
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $supportStaffInfo);
    }

    public function opcache_get_statusAction()
    {
        $data = opcache_get_status();
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

}
