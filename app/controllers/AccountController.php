<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\PasswordHash;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\LoginServer;

//hr客户端 基类
class AccountController extends Controllers\ControllerBase
{

    /**
     *  登出
     */
    public function sign_outAction()
    {
        $param['staff_id'] = $this->userinfo['id'];
        $server = new LoginServer($this->lang,$this->timezone);
        $server->sign_out($param);
        $this->jsonReturn(array('code' => 1,'message' => '','data' => null));
    }


    public function valid_passwordAction()
    {
        $staff_id = $this->userinfo['id'];
        $re       = new StaffRepository($this->lang);
        $info     = $re->login_info($staff_id);
        if (empty($info)) {
            $this->jsonReturn(['code' => -1, 'message' => 'staff id error', 'data' => '']);
        }
        $pass_word = $this->paramIn['password'];
        $hash      = new PasswordHash(10, false);
        $bool      = $hash->checkPassword($pass_word, $info['encrypted_password']);
        $msg       = '';
        if (!$bool) {
            $msg = $this->getTranslation()->_('approval.password_tip');
        }
        $this->jsonReturn(['code' => intval($bool), 'message' => $msg, 'data' => null]);
    }


    public function device_tokenAction()
    {
        try {
            $server = new LoginServer($this->lang,$this->timezone);
            $this->paramIn['staff_id'] = $this->userinfo['id'];
            $res = $server->tokenUploadFle($this->paramIn);
            $this->jsonReturn($res);
        } catch (ValidationException $v) {
            http_response_code(388);
            $this->jsonReturn(['code' => $v->getCode(), 'message' => $v->getMessage(), 'data' => null]);
        } catch (\Exception $e) {
            http_response_code(500);
            $this->getDI()->get('logger')->write_log('new_device_login' . $e->getMessage());
            $this->jsonReturn(['code' => -803, 'message' => 'fail', 'data' => null]);
        }

    }

    //注销账户
    public function cancellationAction(){
        $server = new LoginServer($this->lang,$this->timezone);
        $headerData       = $this->request->getHeaders();
        $this->paramIn['staff_id'] = $this->userinfo['id'];
        $this->paramIn['session_id'] = isset($headerData['X-Fle-Session-Id']) ? $this->processingDefault($headerData, 'X-Fle-Session-Id') : $this->processingDefault($headerData, 'X-By-Session-Id');
        $res = $server->cancelAccount($this->paramIn);
        $this->jsonReturn($res);

    }

}
