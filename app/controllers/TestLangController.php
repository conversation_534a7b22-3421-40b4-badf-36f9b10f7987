<?php

namespace FlashExpress\bi\App\Controllers;


use app\enums\LangEnums;
use FlashExpress\bi\App\library\Language;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\ResignRepository;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\MessageServer;
use Phalcon\Mvc\User\Component;

class TestLangController extends ControllerBase
{
    public static $num = 1;
    public $testNum;
    
    public function initialize()
    {
        parent::initialize();
        
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        
    }
    
    /**
     * 测试语言环境OLD
     * 测试兼容以前使用的方式
     */
    public function oldMethodAction()
    {
        if (RUNTIME == 'pro') {
            exit;
        }
        
        echo '测试开始' . PHP_EOL;
        
        $this->testController();
        echo '------------' . PHP_EOL;
        
        $this->testServer();
        echo '------------' . PHP_EOL;
        
        $this->testRepository();
        echo '------------' . PHP_EOL;
        
        $this->testEnums();
        echo '------------' . PHP_EOL;
    }
    
    /**
     * 测试语言环境
     * 测试新方式使用
     * 正式环境不使用
     */
    public  function newMethodAction()
    {
        if (RUNTIME == 'pro') {
            exit;
        }
        
        echo '测试开始' . PHP_EOL;
        //测试Contorller
        echo '测试ContorllerSTART' . PHP_EOL;
        echo 'controller当前lang的值为', $this->lang . PHP_EOL;
        echo '当前系统语言:';
        echo $this->t->_('monthly') . PHP_EOL;//当前环境字符集合获取
        
        echo '指定语言获取语言包，不修改系统变量' . PHP_EOL;
        echo $this->languagePack->getTranslation('th')->_('monthly') . PHP_EOL;
        
        echo '当前系统语言:';
        echo $this->t->_('monthly') . PHP_EOL;//当前环境字符集合获取
        echo '------------' . PHP_EOL;
        
        //测试Server
        $server = new BaseServer();
        
        echo '测试ServerSTART' . PHP_EOL;
        echo 'Server当前lang的值为', $server->lang . PHP_EOL;
        
        echo '当前系统语言:';
        echo $server->t->_('monthly') . PHP_EOL;//当前环境字符集合获取
        
        echo '指定语言获取语言包，不修改系统变量' . PHP_EOL;
        echo $server->languagePack->getTranslation('th')->_('monthly') . PHP_EOL;
        
        echo '当前系统语言:';
        echo $server->t->_('monthly') . PHP_EOL;//当前环境字符集合获取
        echo '------------' . PHP_EOL;
        
        
        //测试Repository
        $repositoy = new BaseRepository();
        
        echo '测试RepositorySTART' . PHP_EOL;
        echo 'Server当前lang的值为', $repositoy->lang . PHP_EOL;
        echo '当前系统语言:';
        echo $repositoy->t->_('monthly') . PHP_EOL;//当前环境字符集合获取
        
        echo '指定语言获取语言包，不修改系统变量' . PHP_EOL;
        echo $repositoy->languagePack->getTranslation('th')->_('monthly') . PHP_EOL;
        
        echo '当前系统语言:';
        echo $repositoy->t->_('monthly') . PHP_EOL;//当前环境字符集合获取
        echo '------------' . PHP_EOL;
    }
    
    private function testController()
    {
        //测试Contorller
        echo '测试ContorllerSTART' . PHP_EOL;
        
        echo 'controller当前lang的值为', $this->lang . PHP_EOL;
        
        //测试不传递参数获取语言包，当前语言
        echo $this->getTranslation()->_('monthly') . PHP_EOL;
        
        //老的写法，直接切换 ，只对controller层生效
        $this->lang = 'th';
        echo $this->getTranslation()->_('monthly') . PHP_EOL;
        
        echo '测试ContorllerEnd' . PHP_EOL;
    }
    
    private function testServer()
    {
        //测试Contorller
        echo '测试ServerSTART' . PHP_EOL;
        
        //不传参数
        $message = new MessageServer('en', '+8:00');
        
        echo 'Server当前lang的值为', $message->lang . PHP_EOL;
        
        //测试不传递参数获取语言包，当前语言
        echo $message->getTranslation()->_('monthly') . PHP_EOL;
        
        //老的写法，直接切换 ，只对server层生效
        $message->lang = 'th';
        echo $message->getTranslation()->_('monthly') . PHP_EOL;
        
        //老的写法，直接切换 ，只对server层生效
        echo $message->getTranslation('zh')->_('monthly') . PHP_EOL;
        
        echo PHP_EOL . PHP_EOL . '不切换server Lang' . PHP_EOL;
        //老的写法,只修改单次，不切换lang
        echo $message->getTranslation('zh')->_('monthly') . PHP_EOL;
        echo $message->getTranslation()->_('resume_filter_result_title_not_pass') . PHP_EOL;
        
        echo PHP_EOL . PHP_EOL . '切换server Lang' . PHP_EOL;
        //老的写法,只修改单次，切换lang
        echo $message->getTranslationByLang('zh')->_('monthly') . PHP_EOL;
        echo $message->getTranslation()->_('resume_filter_result_title_not_pass') . PHP_EOL;
        
        echo '测试ServerEnd' . PHP_EOL;
    }
    
    private function testRepository()
    {
        echo '测试RepositorySTART' . PHP_EOL;
        
        echo '测试不传参' . PHP_EOL;
        $resign = new ResignRepository('');
        
        echo 'Repository当前lang的值为', $resign->lang . PHP_EOL;
        
        //测试不传递参数获取语言包，当前语言
        echo $resign->getTranslation()->_('monthly') . PHP_EOL;
        
        //老的写法，直接切换 ，只对Repository层生效
        $resign->lang = 'th';
        echo $resign->getTranslation()->_('monthly') . PHP_EOL;
        
        echo PHP_EOL . PHP_EOL . '测试传参zh';
        //测试传参
        $resign = new ResignRepository('zh');
        
        echo 'Repository当前lang的值为', $resign->lang . PHP_EOL;
        
        //测试不传递参数获取语言包，当前语言
        echo $resign->getTranslation()->_('monthly') . PHP_EOL;
        
        //老的写法，直接切换 ，只对Repository层生效
        $resign->lang = 'th';
        echo $resign->getTranslation()->_('monthly') . PHP_EOL;
        
        echo '测试RepositoryEND' . PHP_EOL;
    }
    
    private function testEnums()
    {
        echo '测试enumsSTART' . PHP_EOL;
        
        echo '测试不传参' . PHP_EOL;
        var_dump(LangEnums::getTranslation());
        
        echo '测试传参' . PHP_EOL;
        echo LangEnums::getTranslation('zh', 'monthly') . PHP_EOL;
        
        echo '测试enumsEND' . PHP_EOL;
    }
}