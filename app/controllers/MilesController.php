<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Server\CheckPunchOutServer;
use FlashExpress\bi\App\Server\MilesServer;
use Exception;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\SettingEnvServer;

class MilesController extends Controllers\ControllerBase
{


    public function initialize(){
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
        $this->url_log($this->paramIn);
    }

    /**
     * 里程上报 创建
     * 参数：

     *
     * val mileage_date: String, val kilometres: Long, val mileage_record_type: Int, val mileage_images: List<ImageKey>, val change_car: Int
     */
    public function createAction(){
        try{
            $param = $this->paramIn;
            $server = new MilesServer($this->lang,$this->timezone);
            $setVal = (new SettingEnvServer())->getSetVal('punch_out_switch');
            //增加拦截验证
            if(!empty($setVal) && $param['mileage_record_type'] == $server::MILES_END_TYPE){
                $check = $this->mile_check_punch_out($param);
                if(!empty($check) && $check['code'] == '-3'){
                    //里程 fbi 那边返回-3 了 客户端区分不开
                    $check['code'] = '-4';
                    return $this->jsonReturn($check);
                }
            }
            $param['user_info'] = $this->userinfo;
            $flag = $server->add($param);
            if(!is_bool($flag) && is_string($flag)){
                $this->logger->write_log("create_miles_{$this->userinfo['id']} ".$flag,'info');
                return $this->ajax_fle_return($flag, -1, null);

            }
            return $this->ajax_fle_return('', 1, null);
        }catch (\Exception $e){
            $this->logger->write_log("get_miles_info_{$this->userinfo['id']} ".$e->getMessage(),'info');
            return $this->ajax_fle_return($e->getMessage(), 0, null);
        }
    }


    /**
     * 获取里程信息
     * GET请求
    参数：
    mileageDate 	打卡时间 字符串格式
     */
    public function get_infoAction(){
        try{
            $date = $this->request->get('mileageDate');
            if(empty($date))
                return $this->ajax_fle_return('date error', 0, null);

            $server = new MilesServer($this->lang,$this->timezone);
            $info = $server->miles_info($this->userinfo,$date);

            $this->logger->write_log("get_miles_info_{$this->userinfo['id']} ".json_encode($info),'info');

            return $this->ajax_fle_return('', 1, $info);
        }catch (\Exception $e){

            $this->logger->write_log("get_miles_info_{$this->userinfo['id']} ".$e->getMessage());
            return $this->ajax_fle_return($e->getMessage(), 0, null);
        }
    }


    //里程图片上传
    /**
     * GET请求
    参数：
    fileName	"SMI_mileage" + "_\(time).jpg" 上班； "EMI_mileage" + "_\(time).jpg"下班  time为当前时间戳
     */
    public function uploadAction(){
        try{
            $method = 'buildPutObjectParams';

            $url = env('api_img_upload');

            $filename = $this->request->get('fileName');
            if(empty($filename))
                return $this->ajax_fle_return('parameter error need fileName', -1, null);

            //里程上报 文件夹
            $type = MilesServer::MILES_OSS_TYPE;
//            $type = 'WORK_ATTENDANCE_SOURCE';
            $check_name = strtoupper($filename);

            $sub_type = '';
            //匹配 子类型
            if(strstr($check_name,MilesServer::START_MILEAGE_IMAGE))
                $sub_type = MilesServer::START_MILEAGE_IMAGE;
            if(strstr($check_name,MilesServer::START_GATE_IMAGE))
                $sub_type = MilesServer::START_GATE_IMAGE;
            if(strstr($check_name,MilesServer::END_MILEAGE_IMAGE))
                $sub_type = MilesServer::END_MILEAGE_IMAGE;
            if(strstr($check_name,MilesServer::END_GATE_IMAGE))
                $sub_type = MilesServer::END_GATE_IMAGE;


            if(empty($type))
                return $this->ajax_fle_return('parameter error need correct fileName', -1, null);


            $this->logger->write_log("format_url_fle_{$this->userinfo['id']} {$type} {$url}",'info');

            $fle_rpc = (new ApiClient($url,'',$method, $this->lang));
            $fle_rpc->setParam([$type, $filename, $sub_type]);
            $return = $fle_rpc->execute();

            $this->logger->write_log("format_url_fle_{$this->userinfo['id']} ".json_encode($return),'info');

            //文件上传成功
            if (isset($return['result'])) {
                $data['code'] = 1;
                $data['message'] = 'success';
                $data['data'] = $return['result'];
                die(json_encode($data,JSON_UNESCAPED_UNICODE+JSON_UNESCAPED_SLASHES+JSON_PRETTY_PRINT));
            }

            //文件上传失败
            return $this->ajax_fle_return('upload_failed', 0, null);
        }catch (\Exception $e){
            $this->logger->write_log("miles_upload_{$this->userinfo['id']} ".$e->getMessage());
            return $this->ajax_fle_return('upload_failed',0,null);
        }

    }

    // 新增接口 上传里程 视频 中转调 fbi
    public function upload_videoAction(){
        try{
            $staff_info_id = $this->userinfo['id'];
            $param = $this->paramIn;
            $this->logger->write_log("miles_report {$staff_info_id} upload_video request ".json_encode($param,JSON_UNESCAPED_UNICODE), 'info');
            $param['user_info'] = $this->userinfo;

            $ac = new ApiClient('bi_rpcv2', '', 'mileage.upload_video',$this->lang);
            $ac->setParams($param);
            $ac_result = $ac->execute();
            $this->logger->write_log("miles_report {$staff_info_id} upload_video request ".json_encode($param,JSON_UNESCAPED_UNICODE)." res:".json_encode($ac_result,JSON_UNESCAPED_UNICODE)." ", 'info');

            if(!empty($ac_result) && isset($ac_result['result'])){
                return $this->ajax_fle_return($ac_result['result']['msg'], $ac_result['result']['code'], $ac_result['result']['data']);
            }

            return $this->ajax_fle_return('', 0, null);
        }catch (\Exception $e){
            $this->logger->write_log("upload_video_{$this->userinfo['id']} ".$e->getMessage(),'info');
            return $this->ajax_fle_return($e->getMessage(), 0, null);
        }
    }

    /**
     * 更新审批状态
     *
     */
    public function updateApprovalAction()
    {
        $params = $this->paramIn;

        $this->validateCheck([
            "status" => $params['status'],
            "id"     => $params['audit_id'],
        ], [
            "status" => 'Required|IntIn:2,3,4',
            "id"     => 'Required|Int',
        ]);

        $result   = '';
        $userinfo = $this->userinfo;

        $param['remark']     = isset($params['reject_reason']) && $params['reject_reason'] ? $params['reject_reason'] : '';
        $param['operator_id'] = $userinfo['staff_id'];                        //这是操作人
        $param['biz_type']    = enums::$audit_type['MILEAGE'];                //类型
        $param['reason']      = $params['reason'] ?? '';                     //备注
        $param['status']      = $params['status'];                           //类型
        $param['id']          = $params['audit_id'];                         //id

        $result = (new MilesServer($this->lang, $this->timezone))->setLockConf(5)->updateApproveUseLock($param);

        $this->jsonReturn($result);
    }


    //下班里程保存接口 需要验证下拦截
    public function mile_check_punch_out($param){
        $paramsIn['is_skip']     = 1;
        $paramsIn['platform']    = $this->platform;
        $paramsIn['shift_type']  = $param['shift_type'] ?? 0;
        $paramsIn['shift_index'] = $param['shift_index'] ?? 0;
        $userInfo                = $this->userinfo;
        $checkPunchOutServer     = Tools::reBuildCountryInstance(new CheckPunchOutServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
        $data                    = $checkPunchOutServer->check_staff_punch_out($userInfo, $paramsIn);
        $this->logger->write_log("miles_check_punch_out {$this->userinfo['id']} " . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
        return $data;
    }



}