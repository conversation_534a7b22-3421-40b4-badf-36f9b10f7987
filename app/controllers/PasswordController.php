<?php

/**
 */

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\EmailServer;
use FlashExpress\bi\App\Server\PasswordServer;
use FlashExpress\bi\App\Server\SmsServer;

class PasswordController extends ControllerBase
{
    public function initialize()
    {
        if (function_exists('molten_get_traceid')) {
            $traceid = molten_get_traceid();
        }
        header('traceid:' . ($traceid ?? ''));
        if (strpos(strtolower($this->request->getContentType()), 'application/json') !== false) {
            $this->paramIn = $this->request->getJsonRawBody(true);
        } else {
            $this->paramIn = $this->request->get();
        }
        $this->paramIn = filter_param($this->paramIn);
        $headerData       = $this->request->getHeaders();
        $language         = $this->processingDefault($headerData, 'Accept-Language', 1, getCountryDefaultLang());
        $this->lang = $language;
        $this->cross();
        $this->dealLangPack();

    }

    /**
     * 找回密码
     * 获取临时票据
     * @return void
     * @throws BusinessException
     */
    public function getTicketAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "staff_id" => "Required|Int",
            "mobile"   => "Required|StrLenGeLe:5,20|>>>:" . $this->getTranslation()->_('4179'),
        ];
        $this->validateCheck($paramIn, $validations);
        $sever  = new PasswordServer($this->lang, $this->timezone);
        $result = $sever->findPasswordGetTicket($paramIn['staff_id'], $paramIn['mobile']);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 人脸上传
     * @return void
     * @throws BusinessException
     * @throws \ReflectionException
     */
    public function formatUrlFleAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "facial_compare_ticket" => "Required|StrLenGeLe:10,100|>>>:ticket error",
        ];
        $this->validateCheck($paramIn, $validations);

        $sever  = new PasswordServer($this->lang, $this->timezone);
        $result = $sever->faceFormatUrlFle($paramIn['facial_compare_ticket']);
        $this->jsonReturn($this->checkReturn(['data' => $result['result']]));

    }



    /**
     * 人脸比对
     * @return void
     * @throws BusinessException
     * @throws ValidationException
     */
    public function facialCompareAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "facial_compare_ticket" => "Required|StrLenGeLe:10,100|>>>:ticket error",
            "face_image"            => "Required|StrLenGeLe:10,500|>>>:face_image error",
        ];
        $this->validateCheck($paramIn, $validations);
        $sever  = new PasswordServer($this->lang, $this->timezone);
        $result = $sever->facialCompare($paramIn['facial_compare_ticket'], $paramIn['face_image']);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * @return void
     * @throws ValidationException|BusinessException
     */
    public function sendOTPAction()
    {
        $paramIn = $this->paramIn;
        if (empty($paramIn['edit_password_ticket'])) {
            throw new ValidationException('need ticket');
        }
        $cache               = $this->getDI()->get('redisLib');
        $staffId             = $cache->get($paramIn['edit_password_ticket']);
        $paramIn['staff_id'] = $staffId;

        if (!empty($paramIn['mobile'])) {
            $mobile_rules = isCountry('PH') ? 'StrLen:11' : 'StrLenGeLe:9,20';
            $this->validateCheck($paramIn, [
                "mobile"   => "Required|{$mobile_rules}|>>>:" . $this->getTranslation()->_('4117'),
            ]);
            $paramIn['biz_type'] = SmsServer::SMS_BIZ_TYPE_PASSWORD;
            $result = (new SmsServer($this->lang, $this->timezone))->sendSms($paramIn);
            $this->jsonReturn($this->checkReturn($result));
        }
        if (!empty($paramIn['personal_email'])) {
            $result = (new EmailServer($this->lang, $this->timezone))->send($staffId,$paramIn['personal_email']);
            $this->jsonReturn($this->checkReturn(['data' => $result]));
        }
        throw new ValidationException($this->t->_('data_error'));
    }


    /**
     * @return void
     * @throws ValidationException|BusinessException
     */
    public function editAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "edit_password_ticket" => "Required|StrLenGeLe:1,100|>>>:edit_password_ticket error",
            "password"             => "Required|StrLen:6|>>>:password error",
            "confirm_password"     => "Required|StrLen:6|>>>:confirm_password error",
        ];
        $this->validateCheck($paramIn, $validations);
        $sever  = new PasswordServer($this->lang, $this->timezone);
        $result = $sever->edit($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }


}
