<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\SickServer;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\SickCertificateServer;


class SickCertificateController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode($this->request->getRawBody(),true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 新建
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function addAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $paramIn['user_info'] = $this->userinfo;
        $validations         = [
            "leave_id"  => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        $server = new SickCertificateServer($this->lang,$this->timezone);
        $returnArr = $server->setLockConf(60,true)->addRecordUseLock($paramIn);
        $this->jsonReturn($returnArr);
    }

    /**
     * 审批
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function updateAction()
    {
        $paramIn                  = $this->paramIn;
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $paramIn['user_info']     = $this->userinfo;
        $validations              = [
            "staff_info_id" => "Required|Int",
            "audit_id"      => "Required|Int",
            "status"          => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        $server    = new SickCertificateServer($this->lang, $this->timezone, $this->userinfo);
        $returnArr = $server->setLockConf(60, true)->updateSickCertificateUseLock($paramIn);
        $this->jsonReturn($returnArr);

    }

    /**
     * 获取病假的信息
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getLeaveInfoAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $paramIn['user_info'] = $this->userinfo;
        $validations         = [
            "leave_id"           => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        $server = new SickCertificateServer($this->lang,$this->timezone);
        $returnArr = $server->getLeaveInfo($paramIn,$this->userinfo);

        $this->jsonReturn($returnArr);
    }

    //病假材料列表页  病假申请维度
    public function certificateListAction(){
        $paramIn             = $this->paramIn;
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $paramIn['user_info'] = $this->userinfo;
        $server = new SickCertificateServer($this->lang,$this->timezone);
        $returnArr = $server->getSickCertificateList($paramIn);
        $this->jsonReturn($returnArr);
    }

    public function certificateDetailAction(){
        $paramIn             = $this->paramIn;
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $paramIn['user_info'] = $this->userinfo;
        $validations         = [
            "leave_id"           => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        $server = new SickCertificateServer($this->lang,$this->timezone);
        $returnArr = $server->getSickCertificateDetail($paramIn);
        $this->jsonReturn($returnArr);
    }

    //审批状态 和 材料提交状态的枚举
    public function statusEnumAction(){
        $server = new SickCertificateServer($this->lang,$this->timezone);
        $returnArr = $server->statusEnums();
        $this->jsonReturn($returnArr);
    }


    public function testAction(){
        $server = new AttendanceServer($this->lang, $this->timezone);
        $info['staff_info_id'] = 28213;
        $p['attendance_category'] = 1;
        $p['attendance_date'] = '2024-07-01';
        $server->sickMessage($info, $p);
    }


}
