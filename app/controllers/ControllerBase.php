<?php

namespace FlashExpress\bi\App\Controllers;

use Exception;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\MobileHelper;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\LoginServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Traits\FlashTokenTrait;
use Stringy\StaticStringy;
use WebGeeker\Validation\Validation;
use FlashExpress\bi\App\library\ApiClient;



class ControllerBase extends \FlashExpress\bi\App\Core\PhalBaseController
{
    use FlashTokenTrait;

    public $lang; // 语言变量
    public $t;//语言集合
    public $languagePack;//语言对象

    public $userinfo; //当前登录用户信息
    public $timezone ;

    //全局参数获取
    protected $paramIn = null;

    public $platform; // 平台头
    public $x_fle_session_id; //
    protected $server_session;



    /**
     * 普通用户session
     */
    const SESSION_TYPE_NORMAL = 1;
    /**
     * 停职用户session
     */
    const SESSION_TYPE_SUSPENSION = 2;


    /**
     * @return void
     * @throws BusinessException
     * @throws ValidationException
     */
    public function initialize()
    {
        parent::initialize();
        $this->initLogic();
        //验证版本 是否切换
        //登陆是否迁移 并且 版本号 env 之后
        if(env('break_away_from_ms') && isCountry(['VN','ID'])){
            $this->loginCheckBy();
        }else{
            $this->LoginCheck();
        }
        BaseServer::setPlatform($this->getSourcePlatform());//平台来源枚举值 1 by  2 kit
            //模板关闭
        $this->view->disable();
    }
    //处理语言相关
    protected function dealLangPack()
    {
        $this->languagePack = $this->di->get('languagePack');
        $this->languagePack->setLanguage($this->lang);  //设置语言环境，server  controller repository通用
        $this->t = $this->languagePack->t;              //获取全局语言包赋值到controller层
    }

    /**
     * 获取平台来源, 在请求头中获取
     * @param $source
     * @return int
     */
    public function getSourcePlatform()
    {
        $headerData  = $this->request->getHeaders();
        $flePlatform = $this->processingDefault($headerData, 'Fle-Platform');//平台来源，区分 kit,by;
        return CommonEnums::$sourcePlatformList[$flePlatform] ?? 0;
    }


    public function onConstruct()
    {
    }


    /**
     * 验证用户登录 状态 ，若未登录 ，则中转到登录页面
     * @return void
     * @throws ValidationException
     */
    public function LoginCheck()
    {
        $headerData          = $this->request->getHeaders();
        $x_fle_session_id    = isset($headerData['X-Fle-Session-Id']) ? $this->processingDefault($headerData,
            'X-Fle-Session-Id') : $this->processingDefault($headerData, 'X-By-Session-Id');
        $platform            = $this->processingDefault($headerData, 'By-Platform');
        $language            = $this->processingDefault($headerData, 'Accept-Language');
        $timezone            = $this->processingDefault($headerData, 'Timezone');
        $device_id           = $this->processingDefault($headerData, 'X-Device-Id');
        $sub_to_master_token = $this->processingDefault($headerData, 'Sub-To-Master-Token');
        if (in_array($timezone, ['+07:00', '+08:00'])) {
            $this->timezone = $timezone;
        }
        if (empty($x_fle_session_id) || empty($language)) {
            $error['X-By-Session-Id'] = $x_fle_session_id;
            $error['By-Platform'] = $platform;
            $error['Accept-Language'] = $language;
            $this->jsonReturn($this->checkReturn(-3, $error));
        }

        //处理预请求
        if ($this->request->getMethod() == 'OPTIONS') {
            $this->jsonReturn($this->checkReturn(1));
        }
        if (get_runtime() == 'dev') {
            $this->getDI()->get('logger')->write_log([
                'UserAgent'        => $_SERVER['HTTP_USER_AGENT'],
                'x_fle_session_id' => $x_fle_session_id,
                '$]device_id'      => $device_id,
            ], 'info');
        }

        $agentInfo      = MobileHelper::getUserAgent();
        $equipment_type = $agentInfo['app_name'];

        $this->platform         = $platform;
        $this->lang             = $language;
        //处理语言相关
        $this->dealLangPack();
        $this->x_fle_session_id = $x_fle_session_id;

        if (empty($x_fle_session_id) || $x_fle_session_id == 'null') {
            http_response_code(422);
            $this->jsonReturn($this->checkReturn(['code' => 100112], 'SessionID expired'));
        }
        if (!empty($sub_to_master_token)) {
            $loginServer = new LoginServer($this->lang, $this->timezone);
            $array       = explode('_', $x_fle_session_id);
            $subId       = end($array);
            $loginServer->loginKick($subId, $device_id);//设备互踢
            $staffId = $this->setSessionPrefix(self::$sub_to_master_session_prefix)->validateToken($sub_to_master_token);//主账号
            $staffInfoResult['result'] = $loginServer->staffCheck($staffId);
        } elseif ($this->checkLoginType($x_fle_session_id) == self::SESSION_TYPE_NORMAL) {
            $staffInfoResult = (new LoginServer($this->lang))->checkStaffTokenToJava($x_fle_session_id, $device_id, $equipment_type);
        } else {
            $staffInfoResult = (new LoginServer())->checkSuspendTokenToFauFromCache($x_fle_session_id);
        }
        if (!empty($staffInfoResult['result'])) {
            $user_info             = $staffInfoResult['result'];
            $user_info['staff_id'] = $user_info['id'] ?? 0;
            $this->userinfo        = $user_info;
            header('uid:' . $user_info['id']);
            //非在职 直接重新登录
            if (isset($user_info['state']) && $user_info['state'] != 1) {
                $error_message_key = 'non_working_error';
                if (in_array($user_info['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
                    $error_message_key = 'non_working_error_13';
                }
                http_response_code(422);
                $this->jsonReturn($this->checkReturn([
                    'code' => 100112,
                    'msg'  => $this->getTranslation()->_($error_message_key),
                ]));
            }
        } else {
            http_response_code(422);
            $this->jsonReturn($this->checkReturn(['code' => 100112], 'SessionID expired'));
        }
    }

    /**
     * 处理默认值
     * @param $paramIn
     * @param $paramIn 数组|$parameter 参数名称 |$type 类型 1 字符串 2整型 3数组 4 布尔类型
     * @return string
     */
    public function processingDefault($paramIn , $parameter, $type = 1, $default = '')
    {
        switch($type)
        {
            case 1:
                $default = !empty($default) ? $default : '';
                break;
            case 2:
                $default = !empty($default) ? $default : 0;
                break;
            case 3:
                $default = !empty($default) ? $default : [];
                break;
            case 4:
                $default = !empty($default) ? $default : true;
                break;
        }
        return isset($paramIn[$parameter]) && !empty($paramIn[$parameter]) ? $paramIn[$parameter] : $default;
    }

    /**
     * 基于方法的获取分布式原子锁
     *
     * 使用方法：
     * 在上层调用
     * $this->atomicLock(function () use () {}, someKey, 20)
     * 没有获取到锁 返回false
     * 获取到锁 执行方法体
     *
     * @param  $func
     * @param  string $key 全局唯一值 按业务定义
     * @param int $expire 默认十秒钟 按业务执行时间评估
     * @param bool $isDel
     * @return mixed 是否获取到锁
     * @throws Exception 匿名函数中遇到抛出异常
     */
    public function atomicLock($func, $key, $expire = 10, $isDel = true)
    {
        $redis = $this->getDI()->get("redisLib");
        $LUASCRIPT = <<<LUA
local key = KEYS[1]
local ttl = ARGV[1]
if (redis.call('setnx', key, 1) == 1) then
    return redis.call('expire', key, ttl)
elseif (redis.call('ttl', key) == -1) then
    return redis.call('expire', key, ttl)
end
    return 0
LUA;
        $isLock = $redis->eval($LUASCRIPT, [$key, $expire], 1);
        if ($isLock) {
            try {
                $result = $func();
            } catch (\Exception $e) {
                $redis->del($key);
                throw $e;
            }
            if ($isDel) {
                $redis->del($key);
            }
            return $result;
        }

        // 没有获得锁
        return false;
    }

    /**
     * [getTranslation 获取语言对象]
     * @return [type] [返回一个对应语言对象]
     */
    protected function getTranslation($lang='')
    {
        return $this->languagePack->getTranslation($lang?:$this->lang);
    }


    /**
     * 验证是否为正确的工号
     * @param $params
     * @return void
     * @throws ValidationException
     */
    protected function validateStaffId($params)
    {
        if (isset($params['staff_id']) && !is_staff_info_id($params['staff_id'])) {
            throw new ValidationException($this->getTranslation()->_('not_staff_info_id'));
        }
        if (isset($params['staff_info_id']) && !is_staff_info_id($params['staff_info_id'])) {
            throw new ValidationException($this->getTranslation()->_('not_staff_info_id'));
        }
    }


    /**
     * 参数校验 校验错误抛出
     * @Access  public
     * @Param   $paramIn  | $validations 校验规则
     * @Return  array
     */
    public function validateCheck($paramIn = [], $validations = [])
    {
        try {
            $this->validateStaffId($paramIn);
            Validation::validate($paramIn, $validations);
        } catch (\Exception $e) {

            $this->getDI()->get('logger')->write_log('validateCheck ' .  json_encode([
                    'paramIn' => $paramIn,
                    'validations' => $validations,
                ], JSON_UNESCAPED_UNICODE), 'info');
            $this->jsonReturn($this->checkReturn(-3, $e->getMessage()));
        }
    }


    protected function returnJson($code, $message, $data=null,$statusCode= 200)
    {
        $result = [
            'code' => $code,
            'message' => $message,
            'data'=> $data,
        ];
        $this->response->setStatusCode($statusCode);
        $this->response->setJsonContent($result);
        return $this->response;
    }

    /**
     * 记录日志
     * @Access  public
     * @Param   request
     * @Return  array
     * @param string $title
     * @param $message
     * @param string $model
     * @param string $level
     */
    public function wLog($title = '', $message, $model = '', $level = 'info')
    {
        // 记录日志
        if (!empty($message)) {
            if (is_array($message)) {
                $message = json_encode($message);
            }

            //默认级别 为info
            if ($level != 'error')
                $level = 'info';


            $debugInfo = debug_backtrace();
            $debugInfo = empty($debugInfo[1]) ? array() : $debugInfo[1];

            $function = empty($debugInfo['function']) ? '' : $debugInfo['function'];
            $class = empty($debugInfo['class']) ? '' : $debugInfo['class'];

            //截取 最后 类名  FlashExpress\\bi\\App\\Controllers\\ControllerBase
            if (strstr($class, '\\')) {
                $arr = explode('\\', $class);
                $class = end($arr);
            }

            //写入日志 统一方法
            $message = "{$model}:{$title}-{$message}";
            $logger = $this->getDI()->get('logger');
            $logger->write_log($message, $level, $class, $function);
        }
    }
    /**
     * 记录访问日志
     * @param $param
     */
    function url_log($param)
    {
        return true;
    }

    /**
     * @param $sessionId
     * @return int
     */
    private function checkLoginType($sessionId)
    {
        if (StaticStringy::contains($sessionId,'_')){
            return self::SESSION_TYPE_NORMAL;
        }else{
            return self::SESSION_TYPE_SUSPENSION;
        }
    }


    /**
     * by系统登录验证
     * 验证用户登录 状态 ，若未登录 ，则中转到登录页面
     * @throws ValidationException|BusinessException
     */
    public function loginCheckBy()
    {
        $headerData       = $this->request->getHeaders();
        $x_fle_session_id = isset($headerData['X-Fle-Session-Id']) ? $this->processingDefault($headerData,
            'X-Fle-Session-Id') : $this->processingDefault($headerData, 'X-By-Session-Id');
        $platform         = $this->processingDefault($headerData, 'By-Platform');
        $language         = $this->processingDefault($headerData, 'Accept-Language', 1, getCountryDefaultLang());
        $device_id        = $this->processingDefault($headerData, 'X-Device-Id');

        $timezone = $this->processingDefault($headerData, 'Timezone');
        if (in_array($timezone, ['+07:00', '+08:00'])) {
            $this->timezone = $timezone;
        }
        if (empty($x_fle_session_id) || empty($language)) {
            http_response_code(422);
            throw new ValidationException('session id error',100112);
        }

        //处理预请求
        if ($this->request->getMethod() == 'OPTIONS') {
            $this->jsonReturn(['code' => 1, 'message' => 'success', 'data' => null]);
        }

        $this->platform         = $platform;
        $this->lang             = $language;
        //处理语言相关
        $this->dealLangPack();
        //员工信息
        $staffInfo = (new LoginServer($this->lang,$this->timezone))->getStaffInfoBYToken($x_fle_session_id,$device_id);
        
        $this->x_fle_session_id = $x_fle_session_id;
        $this->getDI()->get('logger')->write_log(['header'=>$headerData,'staffInfo'=>$staffInfo], 'info');
        if ($staffInfo) {
            $staffInfo['staff_id'] = $staffInfo['staff_info_id'] = $staffInfo['id'];
            $staffInfo['lang']     = $this->lang;
            $staffInfo['timezone'] = $this->timezone;
            $this->userinfo        = $staffInfo;
            header('uid:' . $staffInfo['id']);
        } else {
            http_response_code(422);
            throw new ValidationException($this->t->_('login_timeout'), 100112);
        }
    }

    //判断 版本号 是否走新的登录验证
    public function checkVersion(){
        $headerData  = $this->request->getHeaders();
        $version = $this->processingDefault($headerData, 'X-Version');//平台来源，区分 kit,by;
        $os = $this->processingDefault($headerData, 'X-Os');//平台来源，安卓还是苹果
        if (RUNTIME == 'dev') {
            if (empty($version)) {
                http_response_code(422);
                throw new ValidationException('X-Version empty!!!', 100112);
            }
            if (empty($os)) {
                http_response_code(422);
                throw new ValidationException('X-Os empty!!!', 100112);
            }
        }

        if (!empty($version)) {
            return true;
        }

        $os = strtolower($os);

        //没配置 走旧接口
        if (strrpos($os, 'android') !== false) {
            $os = 'android';
        }
        if (strrpos($os, 'ios') !== false) {
            $os = 'ios';
        }
        if (empty($os)) {
            return false;
        }

        $limitVersion = env($os . '_login_version');
        if(empty($version) || empty($limitVersion)){
            return false;
        }
        return check_version($limitVersion,$version);
    }

}
