<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\WorkflowServer;

class WorkflowController extends Controllers\ControllerBase
{
    
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 获取可视化审批流，下拉列表框使用
     */
    public function visualizationAction()
    {
        $workflowServiceObj = new WorkflowServer($this->lang, $this->timezone);
        $list = $workflowServiceObj->getWorkflowRelateType();
        return $this->jsonReturn(
            self::checkReturn(
                [
                    'data' => $list
                ]
            )
        );
    }

    /**
     * 获取审批流预览
     */
    public function getPreviewAction()
    {
        $staffId = $this->request->get('staff_id', 'int'); // 员工id
        $relateType = $this->request->get("relate_type", 'int'); // 可视化审批流主键id

        $validations = [
            "staff_id" => "Required|Int|IntGe:1", // 传递的参数必须大于等于1
            "relate_type" => "Required|Int|IntGe:1",
        ];
        $param = [
            'staff_id' => $staffId,
            'relate_type' => $relateType
        ];
        $this->validateCheck($param, $validations);

        //调用sevice层
        $workflowServiceObj = new WorkflowServer($this->lang, $this->timezone);
        $stream = $workflowServiceObj->getPreview($staffId, $relateType);

        return $this->jsonReturn(
            self::checkReturn(
                [
                    'data' => $stream
                ]
            )
        );
        $this->validateCheck($param, $validations);
    }
}