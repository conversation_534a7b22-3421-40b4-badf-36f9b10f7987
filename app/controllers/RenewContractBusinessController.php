<?php
/**
 * Author: Bruce
 * Date  : 2024-07-06 19:23
 * Description:
 */

namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use  FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\RenewContractBusinessServer;

class RenewContractBusinessController extends Controllers\ControllerBase
{
    public function initialize()
    {
        parent::initialize();
        $method = $this->request->getMethod();

        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }

        //会带个_url参数
        unset($this->paramIn['_url']);

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 个人代理合同续约 审批
     */
    public function updateAction()
    {
        $paramIn = $this->paramIn;

        $validations         = [
            "audit_id"           => "Required|Int",
            "status"              => "Required|IntIn:2,3|>>>:status " . $this->getTranslation()->_('miss_args'),
            "reject_reason"      => "StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('1020'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = (new RenewContractBusinessServer($this->lang, $this->timezone))->updateUseLock($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    public function getContractInfoAction()
    {
        $paramIn = $this->paramIn;

        $validations         = [
            "id"           => "Required|Int|>>>:id " . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = Tools::reBuildCountryInstance(new RenewContractBusinessServer($this->lang, $this->timezone), [$this->lang, $this->timezone])->getContractInfo($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 消息已读 提交 同意 不同意 续约
     */
    public function submitAction()
    {
        $paramIn = $this->paramIn;

        $validations         = [
            "id"           => "Required|Int|>>>:id " . $this->getTranslation()->_('miss_args'),
            "status"           => "Required|Int|>>>:status " . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = Tools::reBuildCountryInstance(new RenewContractBusinessServer($this->lang, $this->timezone), [$this->lang, $this->timezone])->submitUseLock($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 打卡页-续约入口详情
     * MY
     * TH 选择过，打卡页入口详情
     */
    public function noticeDetailAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = Tools::reBuildCountryInstance(new RenewContractBusinessServer($this->lang, $this->timezone), [$this->lang, $this->timezone])->noticeDetail($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * MY 打卡页提交 生成审批流
     * TH 使用
     * @throws \ReflectionException
     */
    public function noticeSubmitAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $returnArr = Tools::reBuildCountryInstance(new RenewContractBusinessServer($this->lang, $this->timezone), [$this->lang, $this->timezone])->noticeSubmitUseLock($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    public function rejectAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $returnArr = Tools::reBuildCountryInstance(new RenewContractBusinessServer($this->lang, $this->timezone), [$this->lang, $this->timezone])->rejectUseLock($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }
}