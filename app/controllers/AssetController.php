<?php
namespace FlashExpress\bi\App\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\AssetServer;
use FlashExpress\bi\App\library\enums;

use App\Country\Tools;
use FlashExpress\bi\App\Server\SettingEnvServer;

class AssetController extends Controllers\ControllerBase
{
    protected $paramIn;
    protected $assetServer;
    protected $staffServer;
    protected $AuditServer;

    public function initialize()
    {
        parent::initialize();
        $this->assetServer = new AssetServer($this->lang, $this->timezone);
        $this->staffServer = new \FlashExpress\bi\App\Server\StaffServer($this->lang, $this->timezone);
        $this->AuditServer = new \FlashExpress\bi\App\Server\AuditServer($this->lang, $this->timezone);
        $this->AuditServer = Tools::reBuildCountryInstance($this->AuditServer, [$this->lang, $this->timezone]);
        $this->paramIn     = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 获取列表
     */
    public function listAction()
    {
        //[1]入参
        $paramIn               = $this->paramIn;
        $paramIn['staff_info'] = $this->userinfo;

        //[2]业务处理
        try {
            $returnArr = $this->assetServer->getInventoryAssetList($paramIn);
        } catch (\Exception $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 更新操作
     */
    public function updateAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //输入为空
        if (!isset($paramIn['dataList']) || empty($paramIn['dataList'])) {
            return $this->jsonReturn(self::checkReturn(["code" => -3, "msg" => $this->getTranslation()->_('err_msg_unvalid_input'), "data" => []]));
        }
        //验证输入数据
        //验证规则：total_cnt = available_cnt + unavailable_cnt
        foreach ($paramIn['dataList'] as $k => $v) {
            if (isset($v['available_cnt']) && //!is_int($v['available_cnt']) &&
                isset($v['unavailable_cnt']) && //!is_int($v['unavailable_cnt']) &&
                isset($v['total_cnt'])//&& !is_int($v['total_cnt'])
            ) {
                if ($v['available_cnt'] + $v['unavailable_cnt'] != $v['total_cnt']) {
                    return $this->jsonReturn(self::checkReturn(["code" => -3, "msg" => $this->getTranslation()->_('err_msg_unvalid_input'), "data" => $k]));
                }
            } else {
                return $this->jsonReturn(self::checkReturn(["code" => -3, "msg" => $this->getTranslation()->_('err_msg_unvalid_input'), "data" => $k]));
            }
        }

        //[2]业务处理
        try {
            $returnArr = $this->assetServer->updateAssetCnt($paramIn);
        } catch (\Exception $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 申请资产-可申请资产列表
     * 网点负责人才可以使用，不同负责人类型看到的资产列表不一样
     * zcc
     */
    public function getListAction()
    {

        try {
            $sourceType =  isset($this->paramIn['source_type']) ? $this->paramIn['source_type'] : '';
            //todo 验证用户是否有该模块权限
            if (
                !$this->AuditServer->getASPermission(['staff_id' => $this->userinfo['id']])
            ) {
                return $this->jsonReturn(self::checkReturn(['data' => null]));
            }

            //todo 获取资产可申请列表

            $list = $this->assetServer->getList($this->userinfo, $sourceType);

//            $spreeList = [];
//            if (!$sourceType || $sourceType != AssetServer::SOURCE_TYPE_PUBLIC_ASSETS) {
//                $spreeList = $this->assetServer->getSpressListV2($this->userinfo['id'], $sourceType);
//            }
//
//            if ($spreeList) {
//                $list = array_merge($spreeList, $list);
//            }
            if ($list) {
                $returnData['data']['dataList'] = $list;
            }

            //资产附件状态
            $assetsHandoerData                    = $this->assetServer->getAssetsHandoer([
                "staff_id" => $this->userinfo["staff_id"]
            ]);
            $annexList = $this->assetServer->getAnnexList([
                "staff_id" => $this->userinfo['staff_id'],
            ]);
            $_annexList = [];
            foreach ($annexList as $k=>$v){
                $img_prefix = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
                $_annexList[$k]["url"] = $img_prefix . $v['object_key'];
                $_annexList[$k]["file_type"] = $v['file_type'];
            }
            $returnData['data']['annex']["data"] = $_annexList;
            $returnData['data']["annex"]["state"] = $assetsHandoerData["state"] ?? 1;
            if ($assetsHandoerData["state"] == enums::$assets_handover_state["not_uploaded"]) {
                $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00001');
            } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["audited"]) {
                $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00002');
            } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["not_pass"]) {
                $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00003');
            } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["adopt"]) {
                $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00004');
            } else {
                $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00001');
            }
            if(empty($annexList)&&$assetsHandoerData["state"]!=4){
                $res=$this->assetServer->getWinhrProve($this->userinfo);
                if($res){
                    $returnData['data']["annex"] =array(
                        'data'=>array(
                            array(
                                "url"=>$res['id_card_pic'],
                                'file_type'=>"2"
                            ),
                            array(
                                "url"=>$res['sign_name_pic'],
                                'file_type'=>"15"
                            )
                        ),
                        'state'=>"4",
                        'state_name'=>$this->getTranslation()->_('asset_00004')
                    );
                }
            }
            //todo 输出json数据
            $this->jsonReturn($this->checkReturn($returnData));
        }catch (Exception $e){
            $this->getDI()->get('logger')->write_log("getListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }


    /**
     * 申请资产-负责人提交申请接口
     * zcc
     */
    public function submitApplyAction()
    {
        try{
            //todo 验证用户是否有该模块权限
            if (
            !$this->AuditServer->getASPermission(['staff_id' => $this->userinfo['id']])
            ) {
                return $this->jsonReturn(self::checkReturn(['data' => null]));
            }

            //todo 参数验证
            if (empty($this->paramIn['assets']) || empty($this->paramIn['reason'])) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('miss_args')));
            }

            //校验状态
            $assetsHandoerData = $this->assetServer->getAssetsHanderByStaffId($this->userinfo["staff_id"]
            );
            $res=$this->assetServer->getWinhrProve($this->userinfo);
            if (!$assetsHandoerData&&!$res){
                // 没有上传过
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('asset_00005')));
            }

            //申请订单
            $this->jsonReturn($this->assetServer->addAssetsOrder($this->paramIn,$this->userinfo));
        }catch (Exception $e){
            $this->getDI()->get('logger')->write_log("submitApplyAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }

    /**
     * 申请资产-我的批量资产
     */
    public function getMyBatchAssetsAction()
    {
        $list = $this->assetServer->getMyBatchAssets($this->userinfo['staff_id']);
        if ($list) {
            $returnData['data']['dataList'] = $list;
        } else {
            $returnData = ['data' => []];
        }

        //获取签名
        $annexList = $this->assetServer->getAnnexList([
            "staff_id" => $this->userinfo['staff_id'],
        ]);
        foreach ($annexList as $k=>$v){
            $img_prefix = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
            $annexList[$k]["url"] = $img_prefix . $v['object_key'];
        }
        $returnData['data']['annex']['data'] = $annexList;

        $assetsHandoerData                    = $this->assetServer->getAssetsHandoer([
            "staff_id" => $this->userinfo["staff_id"]
        ]);
        $returnData['data']["annex"]["state"] = $assetsHandoerData["state"] ?? 1;
        if ($assetsHandoerData["state"] == enums::$assets_handover_state["not_uploaded"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00001');
        } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["audited"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00002');
        } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["not_pass"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00003');
        } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["adopt"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00004');
        } else {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00001');
        }
        $returnData['data']["annex"]["reason"] = $assetsHandoerData["reason"] ?? "";
        $returnData['data']["annex"]["approver_staff_id"] = $assetsHandoerData["approver_staff_id"] ?? "";
        if(empty($annexList)&&$assetsHandoerData["state"]!=4){
            $res=$this->assetServer->getWinhrProve($this->userinfo);
            if($res){
                $returnData['data']["annex"] =array(
                    'data'=>array(
                        array(
                            "url"=>$res['id_card_pic'],
                            'file_type'=>"2"
                        ),
                        array(
                            "url"=>$res['sign_name_pic'],
                            'file_type'=>"15"
                        )
                    ),
                    'state'=>"4",
                    'state_name'=>$this->getTranslation()->_('asset_00004')
                );
            }
        }
        //todo 输出json数据
        $this->jsonReturn($this->checkReturn($returnData));

    }

    /**
     * 申请资产-我的资产
     */
    public function getMyAssetsAction()
    {
        $sourceType = isset($this->paramIn['source_type']) ? $this->paramIn['source_type'] : '';
        //todo 获取我的资产
        $list = $this->assetServer->getMyAssets($this->userinfo['staff_id'], $sourceType);
        if ($list) {
            $returnData['data']['dataList'] = $list;
        } else {
            $returnData = ['data' => []];
        }

        //获取签名
        $annexList = $this->assetServer->getAnnexList([
            "staff_id" => $this->userinfo['staff_id'],
        ]);
        foreach ($annexList as $k=>$v){
            $img_prefix = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
            $annexList[$k]["url"] = $img_prefix . $v['object_key'];
        }
        $returnData['data']['annex']['data'] = $annexList;

        $assetsHandoerData                    = $this->assetServer->getAssetsHandoer([
            "staff_id" => $this->userinfo["staff_id"]
        ]);
        $returnData['data']["annex"]["state"] = $assetsHandoerData["state"] ?? 1;
        if ($assetsHandoerData["state"] == enums::$assets_handover_state["not_uploaded"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00001');
        } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["audited"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00002');
        } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["not_pass"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00003');
        } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["adopt"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00004');
        } else {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00001');
        }
        $returnData['data']["annex"]["reason"] = $assetsHandoerData["reason"] ?? "";
        $returnData['data']["annex"]["approver_staff_id"] = $assetsHandoerData["approver_staff_id"] ?? "";
        if(empty($annexList)&&$assetsHandoerData["state"]!=4){
            $res=$this->assetServer->getWinhrProve($this->userinfo);
            if($res){
                $returnData['data']["annex"] =array(
                    'data'=>array(
                        array(
                            "url"=>$res['id_card_pic'],
                            'file_type'=>"2"
                        ),
                        array(
                            "url"=>$res['sign_name_pic'],
                            'file_type'=>"15"
                        )
                    ),
                    'state'=>"4",
                    'state_name'=>$this->getTranslation()->_('asset_00004')
                );
            }
        }
        //todo 输出json数据
        $this->jsonReturn($this->checkReturn($returnData));

    }

    /**
     * 申请资产-撤销/同意/驳回
     */
    public function checkorderAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $paramIn['staff_name'] = $this->userinfo['name'];

        try {
            $validations = [
                "audit_id" => "Required|StrLenGeLe:4,50",
                "status" => "Required|IntIn:2,3,4",
            ];
            if ($paramIn['status'] != 4) {
                $validations["modify_assets_num"] = "Required|Arr";
            }
            $this->validateCheck($paramIn, $validations);

            if ($paramIn['status'] == 2 && empty($paramIn['modify_assets_num'])) {
                $this->jsonReturn($this->checkReturn('-1', 'modify_assets_num'));
            }

            //[2]业务处理
            list($result, $msg) = $this->assetServer->updateAssetInfo($paramIn, $this->userinfo);
            if (!$result) {
                $this->jsonReturn($this->checkReturn(-3, $msg ?? $this->getTranslation()->_('4102')));
            }
            $returnArray['data'] = $paramIn['audit_id'];
            //[3]数据返回
            $this->jsonReturn(self::checkReturn($returnArray));
        } catch (Exception $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 资产交接-待接收资产数量
     */
    public function transferNumAction()
    {
        $sourceType = isset($this->paramIn['source_type']) ? $this->paramIn['source_type'] : '';
        $staff_id = $this->userinfo['id'];
        $count = $this->assetServer->getTransferNum($staff_id, $sourceType);
        $this->jsonReturn($this->checkReturn(['data' => ['transfer_num' => $count]]));

    }


    /**
     * 资产交接-员工校验
     */
    public function checkStaffAction()
    {

        $paramIn = $this->paramIn;
        if (!isset($paramIn['staff_id']) || empty($paramIn['staff_id'])) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('please_entry_staff_no')));
        }
        $validations = [
            "staff_id" => "Required|Int",
            "type" => "Required|Int"
        ];
        $this->validateCheck($paramIn, $validations);

        //公共资产 个人资产 验证接收者工号是否为在职状态，不是在职不可转交
        $staff_info = $this->staffServer->get_staff($paramIn['staff_id']);
        if (empty($staff_info['data'])) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1001')));
        }
        //验证是否在职
        if($staff_info['data']['state'] != 1) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('err_msg_should_on_hire')));
        }
	
	
	    /*****************************************************************************/
	    /**
	     * @description:资产交接-员工校验 1. BY-我的-个人/公共资产-转交资产，需要支持跨网点转交。在进行当面转交/邮寄转交时，输入的转交人工号，可以输入所有在职员工工号
	     *
	     * @param :staff_id:用户 id
	     * @param :type:
	     *
	     * @return     :data
	     * <AUTHOR> L.J
	     * @time       : 2021/8/11 11:12
	     */
        //白名单
//        if ( $this->AuditServer->getASPermission(['staff_id' => $paramIn['staff_id']], true)
//            || ($this->AuditServer->getASPermission(['staff_id' => $this->userinfo['id']], true) && $this->AuditServer->getASPermission(['staff_id' => $this->userinfo['id']]))
//        ) {
//            //$staff_info = $this->staffServer->get_staff($paramIn['staff_id']);
//            //if (empty($staff_info['data']) || $staff_info['data']['state'] != 1) {
//            //    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1001')));
//            //}
//            $staff_info['data']['staff_id'] = $staff_info['data']['staff_info_id'];
//            $staff_info['data']['staff_name'] = $staff_info['data']['name'];
//
//            $this->jsonReturn($this->checkReturn(['data' => $staff_info['data']]));
//        }
//
//        $departmentInfo = (new StaffRepository($this->lang))->getStaffpositionV2($paramIn['staff_id']);
//        if (!(isset($paramIn['source_type']) && $paramIn['source_type'] == AssetServer::SOURCE_TYPE_PUBLIC_ASSETS)) {
//            //个人资产转交 接收者工号是不是通过了身份审核，没有通过则不能进行转交。
//            $assetsHandoerData = $this->assetServer->getAssetsHandoer(["staff_id" => $paramIn['staff_id'], "state" => 4]);
//            $res=$this->assetServer->getWinhrProve($paramIn);
//            if(empty($assetsHandoerData)&&!$res) {
//                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_certificate_identity_verification')));
//            }
//
//            if(!empty($departmentInfo)){
//                // 总部之间直接转交
//                if($departmentInfo['organization_type'] == 2 && $this->userinfo['organization_type'] == 2){
//                    $staff_info['data']['staff_id'] = $paramIn['staff_id'];
//                    $staff_info['data']['staff_name'] = $departmentInfo['name'];
//                    $staff_info['data']['store_id'] = $departmentInfo['organization_id'];
//                    $staff_info['data']['store_name'] = $departmentInfo['department_name'];
//                    $this->jsonReturn($this->checkReturn(['data' => $staff_info['data']]));
//                }
//            }
//
//            //当面转交验证是否相同网点
//            if ($paramIn['type'] == 1 && $this->userinfo['id'] != '39585') {
//                $data = $this->staffServer->get_staff($paramIn['staff_id']);
//                if (!isset($data['data']['sys_store_id']) || $this->userinfo['organization_id'] != $data['data']['sys_store_id']) {
//                    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('7100')));
//                }
//            }
//
//            // 邮寄判断是否是网点负责人
//            if ($paramIn['type'] == 2 && $this->userinfo['id'] != '39585') {
//                if (!$this->assetServer->isSiteEmailManager($paramIn['staff_id'])) {
//                    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('enter_stsff_number')));
//                }
//            }
//        } else {
//            //公共资产转交 接收者工号是不是有公共资产的接收权限，没有权限则不能转交
//            if (!$this->AuditServer->getASPermission(['staff_id' => $paramIn['staff_id']]) //网点用户 判断权限
//            ) {
//                // 此员工不能拥有公共资产
//                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_have_public_assets')));
//            }
//        }


//        $staff_info = $this->staffServer->get_staff($paramIn['staff_id']);
//
//        if (empty($staff_info['data']) || $staff_info['data']['state'] != 1) {
//            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1001')));
//        }
	    /*****************************************************************************/
        $staff_info['data']['staff_id'] = $staff_info['data']['staff_info_id'];
        $staff_info['data']['staff_name'] = $staff_info['data']['name'];

        $this->jsonReturn($this->checkReturn(['data' => $staff_info['data']]));

    }

    /**
     * 资产交接-批量转交资产确认
     */
    public function transferBatchAction()
    {
        $paramIn = $this->paramIn;
        if (!isset($paramIn['staff_id']) || empty($paramIn['staff_id'])) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('please_entry_staff_no')));
        }
        $validations = [
            "goods_id" => "Required|Int",
            "nums" => "Required|Int",
            "staff_id" => "Required|Int",
            "type" => "Required|IntIn:1,2",
        ];
        if (isset($paramIn['type']) && $paramIn['type'] == 2) {
            $validations['pno'] = "Required|StrLenGeLe:4,50";
        }
        $paramIn['pno'] = isset($paramIn['pno']) ? $paramIn['pno'] : '';
        $this->validateCheck($paramIn, $validations);

        $paramIn['user_id'] = $this->userinfo['id'];
        if ($paramIn['user_id'] == $paramIn['staff_id']) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4102')));
        }

        //验证单号
        if ($paramIn['type'] == 2) {
            if (!$this->assetServer->checkParcelPno(strtoupper($paramIn['pno']))) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_parcel_yet')));
            }
        }

        if (!$this->assetServer->updateBatchAssetsTransfer($paramIn)) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4102')));
        }

        $this->jsonReturn($this->checkReturn([]));
    }


    /**
     * 资产交接-转交资产确认
     */
    public function transferAction()
    {

        $paramIn = $this->paramIn;
        if (!isset($paramIn['staff_id']) || empty($paramIn['staff_id'])) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('please_entry_staff_no')));
        }
        $validations = [
            "id" => "Required|Int",
            "staff_id" => "Required|Int",
            "type" => "Required|IntIn:1,2",
            "pno"  =>"IfIntEq:type,2|Required|StrLenGeLe:4,50|>>>:".$this->getTranslation()->_('no_parcel_yet'),
            'operate_status'=>"IfIntEq:staff_id,-1|Required|IntIn:9,10,11",
            'bar_code'=>'Required|StrLenGe:1',
            'assets_goods_id'=>'Required|Int',
        ];


        /*if (isset($paramIn['type']) && $paramIn['type'] == 2) {
            $validations['pno'] = "Required|StrLenGeLe:4,50";
        }*/

        $paramIn['pno'] = isset($paramIn['pno']) ? $paramIn['pno'] : '';

        $this->validateCheck($paramIn, $validations);

        //员工互转，永远是3，转交中,转交给总部，不修改状态，根据transfer_state判断
        if($paramIn['staff_id']!=-1){
            $paramIn['operate_status'] =3;
        }else{
            //只有当转交给总部选择邮寄时 验证该资产总部是否接收
            if ($paramIn['type'] == 2 &&  !$this->assetServer->assetsRepairAddress($paramIn['bar_code'],$paramIn['operate_status'])) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_assets_to_headquarters')));
            }
        }

        $paramIn['user_id'] = $this->userinfo['id'];
        if ($paramIn['user_id'] == $paramIn['staff_id']) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4102')));
        }

        $one = $this->assetServer->getAssetsByStaff($paramIn);
        if (empty($one)) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4701')));
        }

        //验证单号
        if ($paramIn['type'] == 2) {
            if (!$this->assetServer->checkParcelPno(strtoupper($paramIn['pno']))) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_parcel_yet')));
            }
        }


        if (!$this->assetServer->updaeAssetsTransfer($paramIn)) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4102')));
        }
        $this->jsonReturn($this->checkReturn([]));

    }

    public function transferBatchConfirmAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['reason'] = isset($paramIn['reason']) ? $paramIn['reason'] : '';
        $validations = [
            "goods_id" => "Required|Int",
            "revoke" => "Required|Int",
            "transfer_id" => "Required|Int"
        ];

        $this->validateCheck($paramIn, $validations);
        if (!$this->assetServer->transferBatchConfirm($paramIn)) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4102')));
        }
        $this->jsonReturn($this->checkReturn([]));
    }

    /**
     * 资产交接-接收确认
     */
    public function transferConfirmAction()
    {

        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['reason'] = isset($paramIn['reason']) ? $paramIn['reason'] : '';
        $validations = [
            "id" => "Required|Int",
            "revoke" => "Required|Int",
        ];

        $this->validateCheck($paramIn, $validations);

        if (!$this->assetServer->transferConfirm($paramIn)) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4102')));
        }

        $this->jsonReturn($this->checkReturn([]));

    }

    /**
     * 资产交接 待接受状态撤销
     */
    public function transferRevokeAction() {

        $paramIn = $this->paramIn;
        $assetId = $paramIn['assets_id'];
        $remark = $paramIn['remark'];
        $assets = $this->assetServer->getTransferAssetInfobyStaffInfoId($this->userinfo["staff_id"], $assetId);//资产详情

        if(empty($assets)) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_received_cannot_withdrawn')));
        }

        //更新状态
        if(!$this->assetServer->updateAssetsTransferRevoke($assets,$this->userinfo["staff_id"],$remark)) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4102')));
        }
        $this->jsonReturn($this->checkReturn([]));
    }

    /**
     * 申请资产-批量待接收资产列表
     */
    public function getBatchTransferAction()
    {
        $list = $this->assetServer->getBatchTransferInfo($this->userinfo['staff_id']);
        if ($list) {
            $returnData['data']['dataList'] = $list;
        } else {
            $returnData['data']['dataList'] = [];
        }

        $assetsHandoerData                    = $this->assetServer->getAssetsHandoer([
            "staff_id" => $this->userinfo["staff_id"]
        ]);
        $annexList = $this->assetServer->getAnnexList([
            "staff_id" => $this->userinfo['staff_id'],
        ]);
        $_annexList = [];
        foreach ($annexList as $k=>$v){
            $img_prefix = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
            $_annexList[$k]["url"] = $img_prefix . $v['object_key'];
            $_annexList[$k]["file_type"] = $v['file_type'];
        }
        $returnData['data']['annex']["data"] = $_annexList;
        $returnData['data']["annex"]["state"] = $assetsHandoerData["state"] ?? 1;
        if ($assetsHandoerData["state"] == enums::$assets_handover_state["not_uploaded"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00001');
        } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["audited"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00002');
        } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["not_pass"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00003');
        } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["adopt"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00004');
        } else {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00001');
        }

        //todo 输出json数据
        $this->jsonReturn($this->checkReturn($returnData));

    }

    /**
     * 申请资产-待接收资产列表
     */
    public function getTransferListAction()
    {
        $sourceType = isset($this->paramIn['source_type']) ? $this->paramIn['source_type'] : '';
        $list = $this->assetServer->getTransferInfo($this->userinfo['staff_id'], $sourceType);
        if ($list) {
            $returnData['data']['dataList'] = $list;
        } else {
            $returnData['data']['dataList'] = [];
        }

        $assetsHandoerData                    = $this->assetServer->getAssetsHandoer([
            "staff_id" => $this->userinfo["staff_id"]
        ]);
        $annexList = $this->assetServer->getAnnexList([
            "staff_id" => $this->userinfo['staff_id'],
        ]);
        $_annexList = [];
        foreach ($annexList as $k=>$v){
            $img_prefix = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
            $_annexList[$k]["url"] = $img_prefix . $v['object_key'];
            $_annexList[$k]["file_type"] = $v['file_type'];
        }
        $returnData['data']['annex']["data"] = $_annexList;
        $returnData['data']["annex"]["state"] = $assetsHandoerData["state"] ?? 1;
        if ($assetsHandoerData["state"] == enums::$assets_handover_state["not_uploaded"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00001');
        } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["audited"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00002');
        } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["not_pass"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00003');
        } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["adopt"]) {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00004');
        } else {
            $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00001');
        }
        if(empty($annexList)&&$assetsHandoerData["state"]!=4){
            $res=$this->assetServer->getWinhrProve($this->userinfo);
            if($res){
                $returnData['data']["annex"] =array(
                    'data'=>array(
                        array(
                            "url"=>$res['id_card_pic'],
                            'file_type'=>"2"
                        ),
                        array(
                            "url"=>$res['sign_name_pic'],
                            'file_type'=>"15"
                        )
                    ),
                    'state'=>"4",
                    'state_name'=>$this->getTranslation()->_('asset_00004')
                );
            }
        }
        //todo 输出json数据
        $this->jsonReturn($this->checkReturn($returnData));

    }

    /**
     * 附件上传
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function uploadAnnexAction()
    {
        try {
            $paramIn   = $this->paramIn;
            $obj = new AssetServer($this->lang, $this->timezone);
            $paramIn["staff_id"] = $this->userinfo['staff_id'];
            $returnArr = $obj->uploadAnnex($paramIn);
            $this->jsonReturn($returnArr);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("uploadAnnexAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }


    public function getTransferStatusListAction()
    {
        try {
            $paramIn   = $this->paramIn;
            $obj = new AssetServer($this->lang, $this->timezone);
            $paramIn["job_title"] = $this->userinfo['job_title'];
            $data = $obj->getTransferStatusList($paramIn);
            return $this->jsonReturn($this->checkReturn($data));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getTransferStatusListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }


    /**
     *
     * 获取待接收资产信息
     *
     */
    public function getTransferAssetAction()
    {
        try {
            $paramIn = $this->paramIn;
            $validations  =  [
                'assets_id'=>'Required|Int'
            ];
            $this->validateCheck($paramIn, $validations);
            $assetId = $paramIn['assets_id'];
            $assets = $this->assetServer->getTransferAsset($assetId);

            return $this->jsonReturn($this->checkReturn(["data" => $assets]));
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("getTransferAsset 异常 : " . $e->getTraceAsString() . $e->getMessage(), "error");
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }

    /**
     *
     * 扣除薪金、工资与其他收入赔偿损失的同意书
     *
     *
     */
    public function salaryDeductionPdfAction()
    {
        try {
            $staffId = $this->userinfo['id'];
            $lang=$this->request->get('lang','trim');
            $url = env("fbi_url") . '/Datatmp/salary_deduction?staff_id=' . $staffId.'&lang='.$lang;

            $this->jsonReturn($this->checkReturn(['data' => [
                'url' => $url
            ]]));

        } catch (\Exception $exception) {

            $this->getDI()->get('logger')->write_log('salary_deduction_pdf ' . sprintf("Err_msg: %s, Err_File: %s, Err_Line:%s ",
                    $exception->getMessage(),
                    $exception->getFile(),
                    $exception->getLine()
                ), 'error');

            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }


    /**
     *
     * 申请资产单 pdf
     *
     *
     */
    public function applyAssetListAction()
    {
        try {

            $staffId = $this->userinfo['id'];
            $lang=$this->request->get('lang','trim');
            $url =  env("fbi_url") . '/Datatmp/apply_asset_list?staff_id=' . $staffId.'&lang='.$lang;

            $this->jsonReturn($this->checkReturn(['data' => [
                'url' => $url
            ]]));

        } catch (\Exception $exception) {

            $this->getDI()->get('logger')->write_log('apply_assets_list_pdf ' . sprintf("Err_msg: %s, Err_File: %s, Err_Line:%s ",
                    $exception->getMessage(),
                    $exception->getFile(),
                    $exception->getLine()
                ), 'error');

            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }

    /**
     * DESC: 获取资产管理中的指定资产的邮寄地址
     */
    public function getAssetsRepairInfoAction(){
        try {
            $params['bar_code'] = $this->request->get('bar_code','trim');
            $params['type'] = $this->request->get('operate_status','int!');
            $validations = [
                'bar_code'=>'Required|StrLenGe:1',
                'type'=>'Required|IntIn:9,10,11',
            ];
            $this->validateCheck($params, $validations);
            $assets = $this->assetServer->assetsRepairAddress($params['bar_code'],$params['type']);
            return $this->jsonReturn($this->checkReturn(["data" => $assets]));
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("getAssetsRepairInfo 异常 : " . $e->getTraceAsString() . $e->getMessage(), "error");
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * DESC:获取转移被拒绝的数量
     */
    public function getRefuseNumAction(){
        try {
            $sourceType = isset($this->paramIn['source_type']) ? $this->paramIn['source_type'] : '';
            $staffId = $this->userinfo['id'];

            $assets = $this->assetServer->getRefuseNum($sourceType,$staffId);
            return $this->jsonReturn($this->checkReturn(["data" => $assets]));

        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("getRefuseNum 异常 : " . $e->getTraceAsString() . $e->getMessage(), "error");
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * DESC:未接收资产撤销
     */
    public function notReceivedTransferRevokeAction(){
        $paramIn = $this->paramIn;
        $assetId = $paramIn['assets_id'];
        $paramIn['staff_id'] = $this->userinfo['id'];

        if (!$this->assetServer->isHeadquartersRefuse($assetId,$paramIn)){
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('not_received')));
        }
        $validations =[
            "assets_id" => "Required|Int",
            "transfer_type_num" => "Required|Int",
            "pno"=>"Required|Str",
            "remark"=>"Required|Str"
        ];
        $this->validateCheck($paramIn, $validations);
        //写入一个撤销的日志
        try {
            $status = $this->assetServer->insertAssetsInfoLog([
                'assets_id'=>$paramIn['assets_id'],
                'staff_info_id'=>$paramIn['staff_id'],
                'transfer_staff_id'=>-1,
                'transfer_type'=>$paramIn['transfer_type_num'],
                'pno'=> $paramIn['pno'],
                'transfer_state' => 3,
                'created_at' => gmdate('Y-m-d H:i:s',time()),
                'remark'=>$paramIn['remark']
            ]);
            if (!$status){
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4102')));
            }
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("notReceivedTransferRevoke 异常 : " . $e->getTraceAsString() . $e->getMessage(), "error");
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

        $this->jsonReturn($this->checkReturn([]));
    }

}
