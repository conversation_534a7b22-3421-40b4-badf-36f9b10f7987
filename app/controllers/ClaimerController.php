<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\ClaimerServer;
use FlashExpress\bi\App\library\enums;
use Exception;

class ClaimerController extends Controllers\ControllerBase
{

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode($this->request->getRawBody(),true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
    }

    /**
     *
     * 修改理赔申请
     *
     */
    public function updateClaimerAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations         = [
            "staff_id"           => "Required|Int",
            "audit_id"           => "Required|Int",
            "status"           => "Required|IntIn:".enums::$audit_status['approved'].",".enums::$audit_status['dismissed']."|>>>:status is not invalid",
            "reject_reason"      => "Required|StrLenGeLe:1,1000|>>>:" . $this->getTranslation()->_('5110'),
        ];
        $this->validateCheck($paramIn, $validations);

        ClaimerServer::getInstance($this->lang)->updateClaimerApprovalUseLock($this->userinfo['staff_id'], $paramIn['audit_id'], $paramIn['status'], $paramIn['reject_reason']);
        
        return $this->jsonReturn($this->checkReturn(1));
    }

}