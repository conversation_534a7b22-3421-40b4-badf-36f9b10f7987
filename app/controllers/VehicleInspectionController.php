<?php


namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Server\VehicleInspectionServer;

class VehicleInspectionController extends ControllerBase
{

    public function submitAction()
    {

        $params = $this->paramIn;

        $validations = [
            "mileage_num" => "Required|Str",
            "vehicle_video" => "Required|Str",
        ];
        $this->validateCheck($params, $validations);

        $result = (new VehicleInspectionServer($this->lang, $this->timezone))->submitUseLock($this->userinfo['staff_id'],$params);
        $this->jsonReturn($this->checkReturn(['data'=>$result]));
    }


}