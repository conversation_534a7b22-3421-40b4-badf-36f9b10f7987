<?php
/**
 * Author: Bruce
 * Date  : 2021-11-16 19:58
 * Description:
 */

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\ExtinguisherServer;
use Exception;
use FlashExpress\bi\App\Server\FreightDiscountServer;


class ExtinguisherController extends Controllers\ControllerBase

{
    public $paramIn = [];
    public $server  = [];
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode($this->request->getRawBody(), true);
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct() {
        parent::onConstruct();
    }

    /**
     * 查询未检查数量
     */
    public function pubAssetsListAction()
    {
        try {
            $paramIn             = $this->paramIn;
            $paramIn['userinfo'] = $this->userinfo;
            $result = (new ExtinguisherServer($this->lang, $this->timezone))->getPubAssetsList($paramIn);
            $this->jsonReturn($this->checkReturn(['data'=> $result]));
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('pubAssetsList:'. $e->getMessage(), 'error');
            } else {
                $this->getDI()->get('logger')->write_log('pubAssetsList:'. $e->getMessage(), 'info');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

    }

    /**
     * 查看任务列表，灭火器编码
     */
    public function checkTaskListAction()
    {
        try {
            $paramIn             = $this->paramIn;
            $paramIn['userinfo'] = $this->userinfo;
            $result = (new ExtinguisherServer($this->lang, $this->timezone))->checkTaskListInfo($paramIn);
            $this->jsonReturn($this->checkReturn(['data'=> $result]));
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('checkTaskList:'. $e->getMessage(), 'error');
            } else {
                $this->getDI()->get('logger')->write_log('checkTaskList:'. $e->getMessage(), 'info');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 查看灭火器详情
     */
    public function detailAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        //[2]数据验证
        $validations = [
            "extinguisher_info_id"     => "Required|IntGt:0",
        ];
        $this->validateCheck($paramIn, $validations);
        try {
            $result = (new ExtinguisherServer($this->lang, $this->timezone))->getExtinguisherInfoDetail($paramIn);
            $this->jsonReturn($result);
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('extinguisher-detail:'. $e->getMessage(), 'error');
            } else {
                $this->getDI()->get('logger')->write_log('extinguisher-detail:'. $e->getMessage(), 'info');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取检查结果
     */
    public function inspectionItemsAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        //[2]数据验证
        $validations = [
            "task_id"     => "Required|IntGt:0"
        ];
        $this->validateCheck($paramIn, $validations);
        try {
            $result = (new ExtinguisherServer($this->lang, $this->timezone))->getInspectionItemsInfo($paramIn);
            $this->jsonReturn($this->checkReturn(['data'=> $result]));
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('inspectionItems:'. $e->getMessage(), 'error');
            } else {
                $this->getDI()->get('logger')->write_log('inspectionItems:'. $e->getMessage(), 'info');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 提交检查 结果
     */
    public function storeCheckResultAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['id'];
        //[2]数据验证
        $validations = [
            "task_id"     => "Required|IntGt:0",
            "extinguisher_type"     => "Required|IntGt:0",
            'items' => 'Required|ArrLen:8',//8个问题
            "items[*].problem_key"     => "Required|StrLenGe:1",
            "items[*].type"     => "Required|IntIn:1,2",
            "photo_url_list"     => "Required|ArrLenGe:1",
            "photo_url_list[*].bucket_name"     => "Required|StrLenGe:1",
            "photo_url_list[*].object_key"     => "Required|StrLenGe:1",
            "photo_url_list[*].file_name"     => "Required|StrLenGe:1",
            "sign_info"     => "Required|Obj",
            "sign_info.bucket_name"     => "Required|StrLenGe:1",
            "sign_info.object_key"     => "Required|StrLenGe:1",
            "sign_info.file_name"     => "Required|StrLenGe:1",
        ];
        $this->validateCheck($paramIn, $validations);
        try {
            $redisKey  = "lock_check_extinguisher_task_id_{$paramIn['task_id']}";

            $returnArr = $this->atomicLock(function () use ($paramIn) {
                return  (new ExtinguisherServer($this->lang, $this->timezone))->storeCheckResult($paramIn);
            }, $redisKey, 10);

            if ($returnArr === false) { //没有获取到锁
                return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
            }

            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('storeCheckResult:'. $e->getMessage(), 'error');
            } else {
                $this->getDI()->get('logger')->write_log('storeCheckResult:'. $e->getMessage(), 'info');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }
}