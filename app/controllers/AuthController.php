<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\LoginServer;

//hr客户端 基类
class AuthController extends Controllers\ControllerBase
{
    public function initialize()
    {

        if (function_exists('molten_get_traceid')) {
            $traceid = molten_get_traceid();
        }
        header('traceid:' . ($traceid ?? ''));

        $this->cross();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //时区配置
        $headerData = $this->request->getHeaders();
        $timezone   = $this->processingDefault($headerData, 'Timezone');
        if (in_array($timezone, ['+07:00', '+08:00'])) {
            $this->timezone = $timezone;
        }else{
            $this->timezone = $this->config->application->timeZone;
        }
        //处理语言相关
        $language           = $this->processingDefault($headerData, 'Accept-Language', 1, getCountryDefaultLang());
        $this->lang         = $language;
        $this->languagePack = $this->di->get('languagePack');
        $this->languagePack->setLanguage($this->lang);  //设置语言环境，server  controller repository通用
        $this->t = $this->languagePack->t;              //获取全局语言包赋值到controller层
        //模板关闭
        $this->view->disable();
    }

    /**
     * 登录迁移
     * 参数 {"device_imei":"","device_model":"21061119AG","device_sn":"","device_sn_code":"6fa27af012414d9e","login":"48432","password":"956289","clientid":"6fa27af012414d9e","clientsd":"1700722935390","os":"android","version":"2.2.9"}
     */
    public function new_device_loginAction()
    {
        $data = $this->paramIn;
        try {
            $data['staff_id']       = $this->paramIn['login'];
            $headerData             = $this->request->getHeaders();
            $data['equipment_type'] = strtolower($this->processingDefault($headerData, 'X-Fle-Equipment-Type'));
            $data['device_id']      = $this->processingDefault($headerData, 'X-Device-Id');
            $server                 = new LoginServer($this->lang, $this->timezone);
            $result                 = $server->verifyLogin($data);
            // 判断验证结果 登录成功 返回 token  和用户相关信息
            $this->getDI()->get('logger')->write_log($result, 'info');
            $this->jsonReturn($result);
        } catch (ValidationException|BusinessException $v) {
            http_response_code(388);
            $this->jsonReturn(['code' => $v->getCode(), 'message' => $v->getMessage(), 'data' => null]);
        } catch (\Exception $e) {
            http_response_code(500);
            $this->getDI()->get('logger')->write_log('new_device_login' . $e->getMessage());
            $this->jsonReturn(['code' => -803, 'message' => 'fail', 'data' => null]);
        }
    }


    //免登接口 入参 tid 和 session id  根据当前设备以及session 是否过期 判断是否可登陆
    public function device_reloginAction()
    {
        try {
            $param                   = $this->paramIn;

            $param['header']         = $this->request->getHeaders();
            $this->getDI()->get('logger')->write_log($param, 'info');
            $param['equipment_type'] = strtolower($this->processingDefault($param['header'], 'X-Fle-Equipment-Type'));
            $server                  = new LoginServer($this->lang, $this->timezone);
            $res                     = $server->reLogin($param);
            //返回值
            http_response_code(200);
            return $this->jsonReturn($res);
        } catch (ValidationException $v) {
            http_response_code(399);
            $this->jsonReturn(['code' => $v->getCode(), 'message' => $v->getMessage(), 'data' => null]);
        } catch (\Exception $e) {
            http_response_code(500);
            $this->getDI()->get('logger')->write_log($e->getMessage(), 'info');
            $result['message'] = $e->getMessage();
            $this->jsonReturn($result);
        }
    }

    //清登录锁
    public function clearLoginAction()
    {
        $staffId = $this->paramIn['staff_info_id'];
        $cache   = $this->getDI()->get('redisLib');
        $server  = new LoginServer($this->lang, $this->timezone);
        $cache->delete($server->lockPrefix.$staffId);
        $cache->delete($server->lockErrorNum.$staffId);
        echo 'success';
    }

    /**
     * {
    "login":"工号",
    "device_id":"当前设备id",
    "device_name":"当前设备名称",
    "type": 1是本人 2不是本人,
    "sign":"时间戳_随机数_加密串",
    }
     */
    public function recordAction()
    {
        $param                   = $this->paramIn;
        $param['header']         = $this->request->getHeaders();
        $param['equipment_type'] = strtolower($this->processingDefault($param['header'], 'X-Fle-Equipment-Type'));
        $server                  = new LoginServer($this->lang, $this->timezone);
        $res                     = $server->checkSign($param);
        $this->jsonReturn($res);
    }


}
