<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\VanContainerServer;

class VanContainerController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }


    /**
     * 添加
     *
     * @return void
     */
    public function add_van_containerAction(){
        $param = $this->paramIn;

        $validations         = [
            "type"   => "Required|IntIn:1,2",
            "video_url"  => "StrLenGeLe:0,200",
            "plate_number" => "IfIntEq:type,1|Required|StrLenGeLe:1,20|>>>:" . $this->getTranslation()->_('van_container_save_error_002'),
            "license_location" => "IfIntEq:type,1|Required|StrLenGeLe:1,512|>>>:" . $this->getTranslation()->_('van_container_save_error_003'),
        ];

        $this->validateCheck($param, $validations);

        // 验证车牌号码的规则：泰文辅音+数字 共计7位
        if (mb_strlen($param['plate_number']) && !preg_match("/^[กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรลวศษสหฬอฮ0-9-]{1,7}$/u",
                $param['plate_number'])) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('plate_number_th_error')));
        }

        $param['user_info'] = $this->userinfo;
        $server = new VanContainerServer($this->lang, $this->timezone);
        $data = $server->addContainerUseLock($param);
        return $this->jsonReturn($this->checkReturn(['data' => $data]));
    }


    //详情 车厢信息tab
    public function container_infoAction(){
        $param = $this->paramIn;
        $param['user_info'] = $this->userinfo;
        $server = new VanContainerServer($this->lang, $this->timezone);
        $data = $server->getLastInfo($param);
        return $this->jsonReturn($this->checkReturn(['data' => $data]));
    }


    //提交记录列表
    public function container_listAction(){
        $param = $this->paramIn;
        $param['user_info'] = $this->userinfo;
        $server = new VanContainerServer($this->lang, $this->timezone);
        $data = $server->getContainerList($param);
        return $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    //获取驳回消息内容的标亮
    public function get_reject_dataAction(){
        $param = $this->paramIn;
        $validations         = [
            "id"   => "Required|Int",
        ];
        $this->validateCheck($param, $validations);
        $param['user_info'] = $this->userinfo;
        $data = (new VanContainerServer($this->lang,$this->timezone))->getMsgData($param);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 获取员工最新车辆信息
     *
     * @return void
     * @throws ValidationException
     */
    public function vehicle_infoAction()
    {
        $data = (new VanContainerServer($this->lang, $this->timezone))->getStaffVehicleInfo($this->userinfo['id']);

        return $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 获取可选项信息
     */
    public function getSysStaticAction(){
        $data = (new VanContainerServer($this->lang, $this->timezone))->getSysStatic();
        return $this->jsonReturn($this->checkReturn(['data' => $data]));
    }


}
