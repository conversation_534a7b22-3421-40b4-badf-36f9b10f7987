<?php

namespace FlashExpress\bi\App\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\AdvanceFuelServer;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\PersoninfoServer;

class AdvanceFuelController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * 预支油费签字
     * @return void|null
     * @throws Exception
     */
    public function signAdvanceFuelAction()
    {
        $param       = $this->paramIn;
        $validations = [
            "sign_url" => "Required|Str",
        ];
        $this->validateCheck($param, $validations);
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $returnArr = (new AdvanceFuelServer($this->lang, $this->timezone))->signAdvanceFuelUseLock($param);
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取预支油费pdf模版
     * @return void
     */
    public function getAdvanceFuelInfoAction()
    {
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $server                 = new AdvanceFuelServer($this->lang, $this->timezone);
        $returnArr              = $server->getAdvanceFuelInfo($param);
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 审批操作
     * @return void
     */
    public function auditAction()
    {
        $params = $this->paramIn;

        $validations = [
            "status"   => "Required|IntIn:2,3",
            "audit_id" => "Required|Int",
        ];
        $this->validateCheck($params, $validations);
        if ($params['status'] == enums::APPROVAL_STATUS_REJECTED && (empty($params['reject_reason']))) {
            $this->jsonReturn($this->checkReturn('-1', 'reject_reason'));
        }
        if ($params['status'] == enums::APPROVAL_STATUS_REJECTED) {
            if (mb_strlen($params['reject_reason']) > 500) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1020')));
            }
        }
        $returnArr = (new AdvanceFuelServer($this->lang, $this->timezone))->auditUseLock($params,
            $this->userinfo['staff_id']);
        $this->jsonReturn($returnArr);
    }

    /**
     * 修改邮箱
     * @return void
     */
    public function updateEmailAction()
    {
        $params             = $this->paramIn;
        $validations        = [
            "personal_email" => "Required|Str",
        ];
        $params['staff_id'] = $this->userinfo['staff_id'];
        $this->validateCheck($params, $validations);
        $returnArr = (new PersoninfoServer($this->lang, $this->timezone))->updatePersonInfobymobileUseLock($params);
        $this->jsonReturn($returnArr);
    }
}
