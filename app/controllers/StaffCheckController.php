<?php

namespace FlashExpress\bi\App\Controllers;


use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\CheckPhoneServer;
use FlashExpress\bi\App\library\Exception\ValidationException;

class StaffCheckController extends Controllers\ControllerBase
{

    /**
     * 验证号码类型
     * @return void
     */
    public function mobileInfoAction()
    {
        $service = new CheckPhoneServer($this->lang, $this->timezone);
        $result  = $service->mobileInfo($this->userinfo['staff_id']);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }


    /**
     * 员工手机号码准确性校验 保存
     * @return void
     * @throws ValidationException
     */
    public function saveCheckMobileAction()
    {
        $params = $this->paramIn;
        $this->validateCheck([
            'type'        => $params['type'] ?? '',
            'mobile'      => $params['mobile'] ?? '',
            'verify_code' => $params['verify_code'] ?? '',
        ], [
            'mobile'      => 'Required|StrLenGeLe:1,20|>>>:mobile error',
            'type'        => 'Required|Int|>>>:type error',
            'verify_code' => 'Required|Int|>>>:verify_code error',
        ]);

        $service = new CheckPhoneServer($this->lang, $this->timezone);
        $result  = $service->saveCheckMobileUseLock($this->userinfo['staff_id'], $params);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }


}

