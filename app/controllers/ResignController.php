<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\MaterialAssetsEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\LeaveManagerModel;
use FlashExpress\bi\App\Models\backyard\MsgAssetModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReasonModel;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\MaterialAssetServer;
use FlashExpress\bi\App\Server\ResignServer;
use FlashExpress\bi\App\Server\AssetServer;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysServer;
use WebGeeker\Validation\Validation;
use Exception;

class ResignController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->server = [
            'resign' => new ResignServer($this->lang, $this->timezone),
            'assets' => new AssetServer($this->lang, $this->timezone),
            'backyard' => new BackyardServer($this->lang, $this->timezone),
        ];
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    protected function validationMobile(){

    }

    /**
     * @param $mobile
     * @param $staff_info_id
     * @return void
     * @throws ValidationException
     */
    protected function checkMobileIsExist($mobile, $staff_info_id)
    {
        $validations = [];
        if (isCountry(['TH','PH'])) {
            $validations['agent_mobile'] = "Required|Regexp:/^\d{10}$/|>>>:" . $this->getTranslation()->_('err_msg_individual_contractor_mobile');
        } elseif (isCountry('MY')) {
            $validations['agent_mobile'] = "Required|Regexp:/^\d{10,11}$/|>>>:" . $this->getTranslation()->_('err_msg_individual_contractor_mobile');
        }
        $this->validateCheck(['agent_mobile'=>$mobile], $validations);

        if (isCountry('MY')) {
            if (strpos($mobile, '0') !== 0) {
                $mobile = str_pad($mobile, 11, "0", STR_PAD_LEFT);
            }
        } elseif (isCountry('PH')) {
            $mobile = str_pad($mobile, 11, "0", STR_PAD_LEFT);
        }
        $commonMobile = HrStaffInfoModel::count([
            'conditions' => 'is_sub_staff = :is_sub_staff: and state in ({states:array}) and ((staff_info_id != :staff_info_id: and mobile = :mobile:) or mobile_company = :mobile_company:)',
            'bind'       => [
                'staff_info_id'  => $staff_info_id,
                'mobile'         => $mobile,
                'mobile_company' => $mobile,
                'is_sub_staff'   => HrStaffInfoModel::IS_SUB_STAFF_0,
                'states'         => [HrStaffInfoModel::STATE_1, HrStaffInfoModel::STATE_3],
            ],
        ]);
        if ($commonMobile) {
            //个人号码与其他在职员工重复，请重新输入
            throw new ValidationException($this->getTranslation()->_('mobile_already_exists'));
        }
    }


    /**
     * @return null
     * @throws ValidationException
     */
    public function validateAgentMobileAction()
    {
        $paramIn['staff_id']     = $this->userinfo['staff_id'];
        $paramIn['agent_mobile'] = $this->paramIn['agent_mobile'];
        $this->checkMobileIsExist($paramIn['agent_mobile'], $paramIn['staff_id']);
        return $this->jsonReturn($this->checkReturn(['data' => []]));
    }


    /**
     * 添加离职申请
     * @param datetime leave_date       离职日期
     * @param int      work_handover    交接人id
     * @param array    goods            交接物品
     * @param int      reason           离职原因
     * @param string   remark
     * @throws ValidationException|BusinessException
     */
    public function addResignAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['url']       = $this->config->hr->hc_api_url . '/svc/by_api';
        $paramIn['userinfo']  = $this->userinfo;

        $validations = [
            "last_work_date"=> "Required|Date",
            "leave_date"    => "Required|Date",
            "work_handover" => "Required|Int",
            "reason"        => "Required|Int",
            "remark"        => "Required|StrLenGeLe:20,500|>>>:". $this->getTranslation()->_('err_msg_50_500'),
        ];

        if (isCountry(['TH','MY','PH'])) {
            if ($paramIn['reason'] == StaffLeaveReasonModel::LEAVE_REASON_96) {
                $agentJobTitle = (new SysServer())->getAgentJAllowJobTitleConfig($this->userinfo['organization_id']);
                $agentJobTitle && $validations['agent_job_title'] = "Required|IntIn:" . $agentJobTitle . "|>>>:" . $this->getTranslation()->_('err_msg_individual_contractor_job_title');
                $check_date = date('Y-m-d', strtotime('+2 day midnight'));
                if($paramIn['last_work_date'] < $check_date){
                    throw new ValidationException($this->getTranslation()->_('err_msg_individual_contractor_last_work_date_v2',['last_work_date'=>$check_date]));
                }
                //验证手机号码是否合格
                $this->checkMobileIsExist($paramIn['agent_mobile'],$paramIn['staff_id']);
                unset($validations['remark']);//个人待离职不需要备注
            }
        }

        $this->validateCheck($this->paramIn, $validations);
        $server = new ResignServer($this->lang, $this->timezone);
        $today  = date('Y-m-d');

        //最后工作日期字段，日历选择器只可选今天以及之后的日期
        if (strtotime($paramIn['last_work_date']) < strtotime($today . ' + ' . $server->getResignationAdvanceDays($paramIn['staff_id']) . ' days')) {
            throw new ValidationException($this->getTranslation()->_('enter_valid_data'));
        }

        //离职日期字段，等于最后工作日+1天
        if (strtotime($paramIn['leave_date']) != strtotime($paramIn['last_work_date']) + 86400) {
            throw new ValidationException($this->getTranslation()->_('enter_valid_data'));
        }

        if (!empty($paramIn['email'])) {

            if (!filter_var($paramIn['email'], FILTER_VALIDATE_EMAIL)) {
                throw new ValidationException($this->getTranslation()->_('please_input_personal_email'));
            }

            $emailPrefix = [
                '@flashexpress.com',
                '@flashpay.com',
                '@flashfin.com',
                '@flashlogistics.co.th',
                '@flashmoney.co.th',
                '@flashfulfillment.co.th',
                '@flashfulfillment.my',
                '@flashfulfillment.ph',
                '@flashfulfillment.vn',
                '@flashexpress.id',
                '@flashexpress.la',
                '@flashexpress.my',
                '@flashexpress.ph',
                '@flashexpress.vn',
                '@futurecommerce.cn',
                '@flashdesk.ai',
            ];

            if (in_array(mb_substr($paramIn['email'], mb_strrpos($paramIn['email'], '@')), $emailPrefix)) {
                throw new ValidationException($this->getTranslation()->_('please_input_personal_email'));
            }
        }

        try{
            /**
             * @see ResignServer::addResign
             */
            $returnArr = $server->addResignUseLock($paramIn);

            // 改 个人邮箱
            if (!empty($paramIn['email'])) {
                $rpcClient = new ApiClient('hr_rpc', '', 'hr_staff_personal_email');
                $rpcClient->setParams([
                    'staff_info_id' => $this->userinfo['staff_id'],
                    'personal_email' => $paramIn['email'],
                    'operaterId' => $this->userinfo['staff_id'],
                ]);
                $resultData = $rpcClient->execute();
            }

            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (ValidationException|BusinessException $e) {
            throw $e;
        } catch (Exception $e) {
            if ($e->getCode() == '1000') {
                $this->getDI()->get('logger')->write_log("addResign failed: " . $e->getMessage(), 'info');
            } else {
                $this->getDI()->get('logger')->write_log("addResign failed: " . $e->getMessage() . " " . $e->getTraceAsString());
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 更新离职申请
     */
    public function updateResignAction()
    {
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['staff_name'] = $this->userinfo['name'];
        $status              = $paramIn['status'] ?? 0;

        try{
            $server = new ResignServer($this->lang, $this->timezone);

            //[2]数据验证
            if ($status != 0 && $status != enums::$audit_status['revoked']) { //同意/驳回
                $validations = [
                    "audit_id"     => "Required",
                    "status"       => "Required|Int",
                ];
                if ($status == enums::$audit_status['dismissed']) { //驳回
                    $validations['reject_reason'] = "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1020');
                }
                $this->validateCheck($paramIn, $validations);
                $returnArr  = $server->updateResign($paramIn);
            } else { //撤销
                //[2]数据验证
                $detail = $server->getResignDetail(['resign_id' => $paramIn['audit_id']]);
                if ($detail['submitter_id'] == $paramIn['staff_id']) {

                    $validations = [
                        "audit_id"     => "Required",
                        "status"       => "Required|Int",
                        "cancel_reason"=> "Required|StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('1030'),
                    ];
                } else {

                    $validations = [
                        "audit_id"     => "Required",
                        "status"       => "Required|Int",
                        "cancel_reason"=> "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1030'),
                    ];
                }

                $this->validateCheck($paramIn, $validations);

                $returnArr  = $server->cancelResign($paramIn);
            }
        }catch (\Exception $e) {
            if ($e->getCode() == '1000') {
                $this->getDI()->get('logger')->write_log("updateResign auditId: ". $paramIn['audit_id'] .":" . $e->getMessage(),'info');
            }else{
                $this->getDI()->get('logger')->write_log("updateResign auditId: ". $paramIn['audit_id'] .":" . $e->getMessage());
            }
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        if($paramIn['status']==3 or $paramIn['status']==4){

            $server->readLeaveMsg($paramIn['audit_id']);
        }
        //[3]数据返回
        return $this->jsonReturn($returnArr);
    }
    /**
     * 获取静态枚举
     */
    public function getStaticEnumsAction()
    {
        //[2]业务处理
        $server     = new \FlashExpress\bi\App\Server\ResignServer($this->lang, $this->timezone);
        $returnData = $server->getStaticEnums($this->userinfo['staff_id']);
        return $this->jsonReturn($this->checkReturn(['data'=> ['dataList'=>$returnData]]));
    }


    /**
     * 获取物品价格表和离职原因
     */
    public function getGoodsPriceAction()
    {
        //[2]业务处理
        try{
            $server     = new \FlashExpress\bi\App\Server\ResignServer($this->lang, $this->timezone);
            $returnData = $server->getGoodsPriceAndResignReason($this->userinfo['staff_id']);
        }catch (\Exception $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        return $this->jsonReturn($this->checkReturn(['data'=> ['dataList'=>$returnData]]));
    }

    /**
     * backyard 未审批的离职申请
     * <AUTHOR> <<EMAIL>>
     * @link http://192.168.0.225:3000/project/93/interface/api/4337
     */
    public function getNotApprovalsAction()
    {
        try {
            $staffId = $this->userinfo['staff_id'];
            $this->getDI()->get('logger')->write_log("getnotapprovals " . $staffId, 'info');

            $result = (new ResignServer($this->lang, $this->timezone))->getNotApprovals($staffId);

            return $this->jsonReturn($this->checkReturn(["data"=>$result]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("getnotapprovals " . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3));
        }
    }

    /**
     * 获取离职资产确认接口
     */
    public function getAssetDetailAction()
    {
        try {
            //[1]获取数据
            $paramIn = $this->paramIn;

            //[2]数据验证
            $validations = [
                "staff_info_id" => "Required",
            ];
            $this->validateCheck($paramIn, $validations);
            $this->getDI()->get("logger")->write_log("getAssetDetail " . $this->lang . " "  . json_encode($paramIn, JSON_UNESCAPED_UNICODE), "info");

            //新需求 如果该离职申请 的资产状态 资产部处理过 则 不需要主管处理 弹窗提示

            $leaveManager = LeaveManagerModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind'       => ['staff_info_id' => $paramIn['staff_info_id']],
            ]);
            //如果资产部处理过 则默认 主管已处理 不需要处理了
            if(!empty($leaveManager) && !empty($leaveManager->assets_remand_state)){
                $leaveManager->is_has_superior = 1;
                $leaveManager->save();
                // 主管离职资产确认  更新消息
                $msgAssetInfo=MsgAssetModel::find(
                    [
                        'conditions' => 'staff_id = :staff_id:  and type = :type:',
                        'bind'       => ['staff_id' => $paramIn['staff_info_id'],'type' => '1'],
                    ]
                )->toArray();
                if($msgAssetInfo){
                    foreach ($msgAssetInfo as $item){
                        $rs = $this->server['backyard']->has_read_operation($item['msg_id']);
                    }

                }
                //主管离职资产确认  更新消息已读
            }
            $bi_params = [
                'staff_info_id' => $paramIn['staff_info_id'],
            ];
            $bi_rpc = (new ApiClient('bi_rpc','','getStaffLeaveAssets', $this->lang));
            $bi_rpc->setParams($bi_params);
            $bi_return = $bi_rpc->execute();

            $this->getDI()->get("logger")->write_log("getAssetDetail " . json_encode($bi_return, JSON_UNESCAPED_UNICODE), "info");
            $data = $bi_return['result']['data'];
            //前端判断是否显示
            $data['is_show_button'] = empty($leaveManager) || empty($leaveManager->assets_remand_state) ? 1 : 0;

            //如果是已处理1  //前端判断逻辑 == 0 弹窗提示 资产部处理 ==1 显示按钮  -1 不弹窗也不显示按钮
            if (!empty($leaveManager) && $leaveManager->is_has_superior == 1) {
                $data['is_show_button'] = -1;
            }
            return $this->jsonReturn($this->checkReturn(["data" => $data]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("Resign:getAssetDetail:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3));
        }
    }

    /**
     * 主管审核资产提交接口
     */
    public function confirmAssetAction()
    {
        try {
            $paramIn = $this->paramIn;
            $staffId = $this->userinfo['staff_id'];

            //[2]数据验证
            $validations = [
                "staff_info_id" => "Required|Int|>>>:" . $this->getTranslation()->_('please try again'),
                "assets"        => "Required|Arr|>>>:" . $this->getTranslation()->_('please try again'),
            ];
            $this->validateCheck($paramIn, $validations);

            $submitter = $paramIn['staff_info_id'];
            $assets    = $paramIn['assets'] ?? [];
            $publicAssets    = $paramIn['public_assets'] ?? [];
            $batchAssets = $paramIn['batch_assets'] ?? [];

            $bi_params = [
                'staff_info_id' => $submitter,
                'operator_id'   => $staffId,
                'assets'        => $assets,
                'public_assets' => $publicAssets,
                'batch_assets'  => $batchAssets,
            ];
            $bi_rpc = (new ApiClient('bi_rpc','','postStaffLeaveAssetsStatus', $this->lang));
            $bi_rpc->setParams($bi_params);
            $bi_return = $bi_rpc->execute();

            $this->getDI()->get('logger')->write_log("Resign:getAssetConfirm:" . json_encode($bi_return), 'info');

            // 主管离职资产确认  更新消息
            $msgAssetInfo=MsgAssetModel::find(
                [
                    'conditions' => 'staff_id = :staff_id:  and type = :type:',
                    'bind'       => ['staff_id' => $paramIn['staff_info_id'],'type' => '1'],
                ]
            )->toArray();
            if($msgAssetInfo){
                foreach ($msgAssetInfo as $info){
                    $rs = $this->server['backyard']->has_read_operation($info['msg_id']);
                }

            }
            //主管离职资产确认  更新消息已读

            return $this->jsonReturn($this->checkReturn(["data"=>$bi_return['result']['data'] ?? []]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("Resign:getAssetConfirm:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3));
        }
    }

    /**
     * 主管审核资产列表接口
     */
    public function getAssetListAction()
    {
        try {
            //[1]获取参数
            $paramIn = $this->paramIn;
            $staffId = $this->userinfo['staff_id'];

            //[2]数据验证
            $validations = [
                "status"     => "Required",
                "page"       => "Required|Int",
                //新数据的上次偏移量
                "oa_offset" => "Required|Int",
            ];
            $this->validateCheck($paramIn, $validations);

            $status  = $this->paramIn['status'] ?? 1;
            $page    = $this->paramIn['page'] ?? 1;
            $pagesize= $this->paramIn['pagesize'] ?? 20;

            $bi_params = [
                'staffId'       => $staffId,
                'is_process'    => $status == 1 ? 0 : 1, //0-待处理 1-已处理
                'page'          => $page,
                'pagesize'      => $pagesize,
            ];
            $oa_params = [
                'is_process' => $status == 1 ? 0 : 1, //0-待处理 1-已处理
                'pagesize' => $pagesize,
                'oa_offset' => $paramIn['oa_offset'] ?? 0,
                'search_staff_id' => $paramIn['search_staff_id'] ?? 0,
                'search_leave_date' => $paramIn['search_leave_date'] ?? '',
                'search_last_work_date' => $paramIn['search_last_work_date'] ?? '',
            ];
            $return_data = [
                'data' => [], //返回新老混合数据
                'is_over' => MaterialAssetsEnums::LEAVE_ASSETS_IS_OVER_YES, //老数据的是否加载结束标识,新数据修复并继续兼容
                'oa_offset' => 0, //新数据使用的偏移量
            ];
            $new_assets_server = new MaterialAssetServer($this->lang, $this->timezone);
            if ($oa_params['oa_offset'] > 0) {
                //当前页有oa_offset(新数据,继续往后读新数据)
                $return_data = $new_assets_server->getAssetsManagerList($oa_params, $this->userinfo);
            } else {
                //当前页oa_offset没值(上一页没有新数据,需要判断老数据是否读完)
                //1.先查老数据
                $bi_rpc = (new ApiClient('bi_rpc','','leaveStaffList', $this->lang));
                $bi_rpc->setParams($bi_params);
                $bi_return = $bi_rpc->execute();
                //2.老数据是否读完
                if (isset($bi_return['result']['data']['data'])) {
                    $old_data_count = count($bi_return['result']['data']['data']);
                    if ($old_data_count == 0) {
                        //全读完了,去读新数据
                        $return_data = $new_assets_server->getAssetsManagerList($oa_params, $this->userinfo);
                    } elseif ($old_data_count < $pagesize) {
                        //老数据读到最后一页了,剩下部分读新数据
                        $oa_params['pagesize'] = $pagesize-$old_data_count;
                        $return_data = $new_assets_server->getAssetsManagerList($oa_params, $this->userinfo);
                        //组合新老数据
                        $old_data = $bi_return['result']['data']['data'] ?? [];
                        $new_data = $return_data['data'] ?? [];
                        $return_data['data'] = array_merge($old_data, $new_data);
                    } else {
                        //剩下的情况就是 $old_data_count == $pagesize, 这种情况不去查新数据
                        $return_data['data'] = $bi_return['result']['data']['data'] ?? [];
                        $return_data['is_over'] = MaterialAssetsEnums::LEAVE_ASSETS_IS_OVER_NO;
                    }
                } else {
                    $this->getDI()->get('logger')->write_log("getAssetList-bireturn-error : " . json_encode($bi_return, JSON_UNESCAPED_UNICODE), 'info');
                    //fbi没有正常返回,去读oa
                    $return_data = $new_assets_server->getAssetsManagerList($oa_params, $this->userinfo);
                }
            }

            return $this->jsonReturn($this->checkReturn(["data" => $return_data]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("getnotapprovals " . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3));
        }
    }

    /**
     *
     *
     * 离职主管审批资产价格列表
     *
     */
    public function assetsMoneyAction()
    {
        try {
//            $result = $this->server['assets']->assetsMoney($this->userinfo['staff_id']);
            //$result = $this->server['assets']->assetsByStaffId($this->userinfo['staff_id']);
            $params = $this->request->get();
            $params['staff_id'] = $this->userinfo['staff_id'];
            $new_assets_server = new MaterialAssetServer($this->lang, $this->timezone);
            $result = $new_assets_server->assetsByStaffId($params);
            return $this->jsonReturn($this->checkReturn($result));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("assetsMoney: " . $e->getMessage() . $e->getTraceAsString(), 'error');
            return $this->jsonReturn(self::checkReturn(-3));
        }
    }

    /**
     * 离职申请-离职须知-资产列表
     * @date 2023/3/16
     */
    public function assetsMoneyDetailAction()
    {
        try {
            $params = $this->request->get();
            $params['staff_id'] = $this->userinfo['staff_id'];
            $new_assets_server = new MaterialAssetServer($this->lang, $this->timezone);
            $result = $new_assets_server->getAssetsDetailByStaffId($params);
            return $this->jsonReturn($this->checkReturn($result));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("assetsMoneyDetail: " . $e->getMessage() . $e->getTraceAsString(), 'error');
            return $this->jsonReturn(self::checkReturn(-3));
        }
    }

    /**
     * 获取离职单类型
     * 如果是即所属部门:Network Operaions，职位:Van Courier，车辆来源:【租用公司车辆】的员工 显示独特的离职申请
     */
    public function getResignTypesAction(){
        try {
            //[1]获取数据
            $paramIn['staff_info_id'] = $this->userinfo['staff_id'];

            //[2]数据验证
            $validations = [
                "staff_info_id" => "Required",
            ];
            $this->validateCheck($paramIn, $validations);
            $this->getDI()->get("logger")->write_log("getResignTypes " . $this->lang . " "  . json_encode($paramIn, JSON_UNESCAPED_UNICODE), "info");

            $data = $this->server['resign']->getResignTypes($paramIn['staff_info_id']);
            return $this->jsonReturn($this->checkReturn(["data" => $data]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("Resign:getResignTypes:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3));
        }

    }


    /**
     *
     * 获取提交人信息
     * 判断是否是销售代表
     *
     */
    public function getUserInfoAction()
    {
        $data = ( new ResignServer($this->lang, $this->timezone))->getUserData($this->userinfo['staff_id']);
        return $this->jsonReturn($this->checkReturn(["data" => $data]));

    }


    /**
     *
     * 获取离职接收书 地址
     *
     */
    public function acceptionBookAction()
    {
        try {
            $resignId = $this->request->get('resign_id');

            $this->validateCheck([
                'resign_id' => $resignId,
            ], [
                'resign_id' => 'Required|Int',
            ]);
            $resignServer = Tools::reBuildCountryInstance($this->server['resign'], [$this->lang, $this->timezone]);
            $acceptionBook = $resignServer->getResignInfo($resignId);

            $this->jsonReturn($this->checkReturn(['data' => $acceptionBook]));

        } catch (\Exception $e) {

            $this->getDI()->get('logger')->write_log("Resign:acceptionBook: M_Error: " . $e->getMessage() . " M_File: " . $e->getFile() . " M_Line: " . $e->getLine(), 'error');
            return $this->jsonReturn(self::checkReturn(-3));
        }
    }

    /**
     * 离职问卷详情
     * @return null
     * @throws Exception
     */
    public function questionnaireInfoAction()
    {
        $server = new ResignServer($this->lang, $this->timezone);
        $data = $server->questionnaireInfo();
        return $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 保存离职问卷
     * @return void
     * @throws BusinessException
     */
    public function questionnaireSaveAction()
    {

        $params = $this->paramIn;
        $msg_id = $params['msg_id'] ?? '';
        $questionnaire_data = $params['questionnaire_data'] ?? '';
        $this->validateCheck([
            'mst_id' => $msg_id,
        ], [
            'mst_id' => 'Required|Str',
        ]);

        $staffId = $this->userinfo['staff_id'];
        $server = new ResignServer($this->lang, $this->timezone);
        $data = $server->questionnaireSave($staffId,$msg_id,$questionnaire_data);
        $this->jsonReturn($this->checkReturn(['data' => $data]));

    }









}
