<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\KpiServer;

/**
 * 个人绩效KPI目标控制器
 * Class KpiController
 * @package FlashExpress\bi\App\Controllers
 */

class KpiController extends Controllers\ControllerBase
{
    protected $paramIn;

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * 个人绩效-通知消息详情页面
     * @api https://yapi.flashexpress.pub/project/93/interface/api/42587
     */
    public function msgDetailAction()
    {
        $validations = [
            'activity_id' => 'Required|IntGe:1|>>>:activity id'. $this->getTranslation()->_('miss_args'), //活动ID
        ];

        //验证
        $this->validateCheck($this->paramIn, $validations);
        $activity_id = $this->paramIn['activity_id'];
        $list =  (new KpiServer($this->lang,$this->timezone))->getMsgDetail($activity_id);
        $this->jsonReturn($this->checkReturn(array('data' => $list)));
    }

    /**
     * 个人绩效-核心价值观
     * @api https://yapi.flashexpress.pub/project/93/interface/api/42601
     */
    public function valuesAction()
    {
        $validations = [
            'activity_id' => 'Required|IntGe:1|>>>:activity id'. $this->getTranslation()->_('miss_args'), //活动ID
        ];

        //验证
        $this->validateCheck($this->paramIn, $validations);
        $activity_id = $this->paramIn['activity_id'];
        $list =  (new KpiServer($this->lang,$this->timezone))->getValues($activity_id);
        $this->jsonReturn($this->checkReturn(array('data' => $list)));
    }

    /**
     * 个人绩效-关键绩效指标
     * @api https://yapi.flashexpress.pub/project/93/interface/api/42846
     */
    public function indicatorsAction()
    {
        $validations = [
            'activity_id' => 'Required|IntGe:1|>>>:activity id' . $this->getTranslation()->_('miss_args'), //活动ID
        ];

        //验证
        $this->validateCheck($this->paramIn, $validations);
        $activity_id = $this->paramIn['activity_id'];
        $list = (new KpiServer($this->lang,$this->timezone))->getIndicators($activity_id, $this->userinfo['staff_id']);
        $this->jsonReturn($this->checkReturn(array('data' => $list)));
    }

    /**
     * 获取员工参与的活动列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/42825
     */
    public function getParticipateActivityListAction()
    {
        $list = (new KpiServer($this->lang,$this->timezone))->getParticipateActivityList($this->userinfo);
        $this->jsonReturn($this->checkReturn(array('data' => $list)));
    }

    /**
     * 员工kpi签名
     * @api https://yapi.flashexpress.pub/project/93/interface/api/43042
     */
    public function staffSignatureAction()
    {
        $validations = [
            'id' => 'Required|IntGe:1|>>>:id' . $this->getTranslation()->_('miss_args'),
            'bucket_name' => 'Required|StrLenGeLe:1,63|>>>:bucket_name ' . $this->getTranslation()->_('miss_args'),
            'object_key' => 'Required|StrLenGeLe:1,100|>>>:object_key ' . $this->getTranslation()->_('miss_args'),
            'file_name' => 'Required|StrLenGeLe:1,200|>>>:file_name ' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($this->paramIn, $validations);
        $result = (new KpiServer($this->lang,$this->timezone))->staffSignature($this->paramIn, $this->userinfo);
        return $this->jsonReturn($result);
    }
}