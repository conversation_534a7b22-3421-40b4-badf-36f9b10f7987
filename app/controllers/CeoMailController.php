<?php

namespace FlashExpress\bi\App\Controllers;

// CEO 信箱 2.0 版本 2020.11

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\CeoMailServer;
use WebGeeker\Validation\Validation;

class CeoMailController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }

        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 验证分类是否已知晓
     * @return void
     * @throws Exception
     */
    public function checkIsConfirmByCategoryIdAction()
    {
        $paramIn['category_id'] = $this->request->get('category_id', 'trim');
        // 参数校验
        $validations = [
            "category_id" => "Required|IntGeLe:1,2000|>>>:category_id error",
        ];
        $this->validateCheck($paramIn, $validations);

        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        // 获取问题分类列表
        $data = (new CeoMailServer($this->lang, $this->timezone))->checkIsConfirmByCategoryId($paramIn);
        return $this->jsonReturn(self::checkReturn(['data' => $data]));
    }

    /**
     * 验证分类是否已知晓
     * @return void
     * @throws Exception
     */
    public function saveAgreementAction()
    {
        $paramIn = $this->paramIn;
        // 参数校验
        $validations = [
            "category_id" => "Required|IntGeLe:1,2000|>>>:category_id error",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        // 获取问题分类列表
        $data = (new CeoMailServer($this->lang, $this->timezone))->saveConsentAgreementUseLock($paramIn);
        return $this->jsonReturn(self::checkReturn(['data' => $data]));
    }


    // 问题分类列表
    public function problemCategoryListAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        // 获取问题分类列表
        $data = (new CeoMailServer($this->lang, $this->timezone))->getCategoryList($paramIn);
        return $this->jsonReturn(self::checkReturn(['data' => $data]));
    }

    // 问题工单列表
    public function problemOrderListAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $data                = (new CeoMailServer($this->lang, $this->timezone))->getProblemOrderList($paramIn);
        return $this->jsonReturn(self::checkReturn(['data' => $data]));
    }

    // 问题工单创建

    /**
     * @throws Exception
     */
    public function problemOrderCreateAction()
    {
        $paramIn             = $this->paramIn;
        $param['staff_info'] = $this->userinfo;

        $param['problem_category'] = $paramIn['problem_category'];
        $param['problem_desc']     = trim($paramIn['problem_desc']);
        $param['problem_image']    = $paramIn['problem_image'];

        // 参数校验
        $validations = [
            "problem_category" => "Required|IntGeLe:1,300|>>>:" . $this->getTranslation()->_('string_length_limit',
                    ['min' => 1, 'max' => 300]),
            "problem_desc"     => "Required|StrLenGeLe:1,1000|>>>:" . $this->getTranslation()->_('string_length_limit',
                    ['min' => 1, 'max' => 1000]),
        ];
        $this->validateCheck($param, $validations);
        $result = (new CeoMailServer($this->lang, $this->timezone))->problemOrderCreateUseLock($param);
        return $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    /**
     * 校验：已存在相同类型问题的工单，工作人员正在加急回复中，请您耐心等待
     * @throws Exception
     */
    public function problemOrderCreateCheckAction()
    {
        $paramIn             = $this->paramIn;
        $param['staff_info'] = $this->userinfo;

        $param['problem_category'] = $paramIn['problem_category'];

        // 参数校验
        $validations = [
            "problem_category" => "Required|IntGeLe:1,300|>>>:" . $this->getTranslation()->_('string_length_limit',
                    ['min' => 1, 'max' => 300]),
        ];
        $this->validateCheck($param, $validations);
        $result = (new CeoMailServer($this->lang, $this->timezone))->problemOrderCreateCheck($param);
        return $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    // 问题工单详情

    /**
     * @throws Exception
     */
    public function problemOrderDetailAction()
    {
        $paramIn             = $this->paramIn;
        $param['staff_id']   = $this->userinfo['id'];
        $param['problem_no'] = $paramIn['problem_no'];

        // 参数校验
        $validations = [
            "problem_no" => "Required|StrLen:28|>>>:problem_no param error",
        ];
        $this->validateCheck($param, $validations);
        $data = (new CeoMailServer($this->lang, $this->timezone))->getProblemOrderDetail($param['problem_no'],
            $param['staff_id']);
        return $this->jsonReturn(self::checkReturn(['data' => $data]));
    }

    // 继续提问: 针对平台的回复
    public function staffContinueAskAction()
    {
        $paramIn             = $this->paramIn;
        $param['staff_info'] = $this->userinfo;

        try {
            $param['problem_no']    = $paramIn['problem_no'];
            $param['problem_desc']  = trim($paramIn['problem_desc']);
            $param['problem_image'] = $paramIn['problem_image'];

            // 参数校验
            $validations = [
                "problem_no" => "Required|StrLen:28|>>>:problem_no param error",
            ];

            if (!empty($param['problem_desc'])) {
                $validations['problem_desc'] = "Required|StrLenGeLe:1,1000|>>>:" . $this->getTranslation()->_('string_length_limit',
                        ['min' => 1, 'max' => 1000]);
            }

            $this->validateCheck($param, $validations);

            if (empty($param['problem_desc']) && empty($param['problem_image'])) {
                return $this->jsonReturn(self::checkReturn(-1));
            }

            $result = (new CeoMailServer($this->lang, $this->timezone))->continueFeedback($param);
            if ($result) {
                $data = [
                    'code' => 1,
                    'msg'  => 'success',
                    'data' => [],
                ];
            } else {
                $data = [
                    'code' => -1,
                    'msg'  => 'fail',
                    'data' => [],
                ];
            }

            return $this->jsonReturn(self::checkReturn($data));
        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("CEO MAIL: staffContinueAsk-" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

    // 问题工单评价
    public function evaluateSubmitAction()
    {
        $paramIn           = $this->paramIn;
        $param['staff_id'] = $this->userinfo['id'];

        try {
            $param['problem_no']            = $paramIn['problem_no'];
            $param['staff_score']           = $paramIn['staff_score'];// 评分 1-5 必填
            $param['staff_evaluate']        = $paramIn['staff_evaluate'];// 评价常用语
            $param['staff_evaluate_remark'] = trim($paramIn['staff_evaluate_remark']);// 评价 必填

            // 参数校验
            $validations = [
                "problem_no"            => "Required|StrLen:28|>>>:problem_no param error",
                "staff_score"           => "Required|IntGeLe:1,5|>>>:staff_score param error",
                "staff_evaluate_remark" => "StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('content_limit_len'),
            ];

            $this->validateCheck($param, $validations);

            $result = (new CeoMailServer($this->lang, $this->timezone))->problemOrderEvaluate($param);
            if ($result) {
                $data = [
                    'code' => 1,
                    'msg'  => 'success',
                    'data' => [],
                ];
            } else {
                $data = [
                    'code' => -1,
                    'msg'  => 'fail',
                    'data' => [],
                ];
            }

            return $this->jsonReturn(self::checkReturn($data));
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("CEO MAIL: evaluateSubmit-" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

    // 问题反馈未读数量
    public function feedbackUnReadNumAction()
    {
        try {
            $param['staff_id'] = $this->userinfo['id'];

            $result = (new CeoMailServer($this->lang, $this->timezone))->getFeedbackUnreadCount($param);
            $data   = [
                'code' => 1,
                'msg'  => 'success',
                'data' => $result,
            ];

            return $this->jsonReturn(self::checkReturn($data));
        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("CEO MAIL: feedbackUnReadNum-" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

    /**
     * 是否同意关闭会话
     */
    public function problemOrderCloseAction()
    {
        $paramIn             = $this->paramIn;
        $param['staff_info'] = $this->userinfo;

        $param['problem_no']      = $paramIn['problem_no'];
        $param['auto_sys_msg_id'] = $paramIn['auto_sys_msg_id'];
        $param['state']           = $paramIn['state'];

        // 参数校验
        $validations = [
            "problem_no"      => "Required|StrLen:28|>>>:problem_no param error",
            "auto_sys_msg_id" => "Required|StrLenGeLe:1,28|>>>:auto_sys_msg_id param error",
            "state"           => "Required|IntIn:1,2|>>>:state param error",
        ];

        $this->validateCheck($param, $validations);

        (new CeoMailServer($this->lang, $this->timezone))->problemOrderClose($param);

        return $this->jsonReturn(self::checkReturn(1));
    }

    // 问题工单详情
    public function problemOrderDetailFromMessageAction()
    {
        $paramIn           = $this->paramIn;
        $param['staff_id'] = $this->userinfo['id'];

        try {
            $param['problem_no'] = $paramIn['problem_no'];

            // 参数校验
            $validations = [
                "problem_no" => "Required|StrLen:28|>>>:problem_no param error",
            ];

            $this->validateCheck($param, $validations);

            $data   = (new CeoMailServer($this->lang,
                $this->timezone))->getProblemOrderDetailFromMessage($param['problem_no'], $param['staff_id']);
            $result = [
                'code' => 1,
                'msg'  => 'success',
                'data' => $data,
            ];

            return $this->jsonReturn(self::checkReturn($result));
        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->write_log("CEO MAIL: problemOrderDetail-" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

    /**
     * flash box 投诉跟进 列表
     */
    public function followListAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['id'];

        // 参数校验
        $validations = [
            "status" => "Required|IntIn:1,2|>>>:status param error",
        ];

        $this->validateCheck($paramIn, $validations);

        $result = (new CeoMailServer($this->lang, $this->timezone))->followList($paramIn);

        return $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    /**
     * flash box 投诉跟进 详情
     * @throws ValidationException
     */
    public function followDetailAction()
    {
        $paramIn            = $this->paramIn;
        $param['staff_id']  = $this->userinfo['id'];
        $param['follow_id'] = $paramIn['follow_id'];
        // 参数校验
        $validations = [
            "follow_id" => "Required|IntGe:1|>>>:follow_id param error",
        ];

        $this->validateCheck($param, $validations);

        $result = (new CeoMailServer($this->lang, $this->timezone))->followDetail($param);

        return $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    /**
     * 问题分类
     */
    public function getFollowSysInfoAction()
    {
        $result = (new CeoMailServer($this->lang, $this->timezone))->getFollowSysInfo();

        return $this->jsonReturn(self::checkReturn(['data' => $result]));
    }

    /**
     * flash box 处理跟进
     * @throws ValidationException
     */
    public function addFollowAction()
    {
        $paramIn = $this->paramIn;

        // 参数校验
        $validations = [
            "follow_id"      => "Required|IntGe:1|>>>:follow_id param error",
            "fix_result"     => "Required|IntGe:1|>>>:fix_result param error",
            "category"       => "Required|IntGe:1|>>>:category param error",
            "follow_message" => "Required|StrLenGeLe:1,1000|>>>:follow_message param error",
            "image_url"      => "Required|Arr|ArrLenLe:5|>>>:image_url param error",
        ];

        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['id'];

        $result = (new CeoMailServer($this->lang, $this->timezone))->addFollowUseLock($paramIn);

        return $this->jsonReturn(self::checkReturn(['data' => $result]));
    }


    /**
     * flash box 处理跟进详情页 获取 工单对话
     * @throws ValidationException
     */
    public function getOrderDetailByFollowAction()
    {
        $paramIn = $this->paramIn;

        // 参数校验
        $validations = [
            "follow_id" => "Required|IntGe:1|>>>:follow_id param error",
        ];

        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['id'];

        $result = (new CeoMailServer($this->lang, $this->timezone))->getOrderDetailByFollow($paramIn);

        return $this->jsonReturn(self::checkReturn(['data' => $result]));
    }


}

