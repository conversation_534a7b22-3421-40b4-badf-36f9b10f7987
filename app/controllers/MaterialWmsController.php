<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\MaterialWmsServer;

/**
 * 耗材相关操作控制器
 * Class MaterialAssetController
 * @package FlashExpress\bi\App\Controllers
 */
class MaterialWmsController extends Controllers\ControllerBase
{
    protected $paramIn;
    protected $materialWmsServer;

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        $this->materialWmsServer = new MaterialWmsServer($this->lang, $this->timezone);
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * 耗材申请-选择耗材列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/67677
     */
    public function getWmsListAction()
    {
        $result = $this->materialWmsServer->getWmsList($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }


    /**
     * 耗材申请-基本信息
     * @api https://yapi.flashexpress.pub/project/93/interface/api/67682
     */
    public function getAddDefaultAction()
    {
        $result = $this->materialWmsServer->getAddDefault($this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }


    /**
     * 耗材申请-提交
     * @api https://yapi.flashexpress.pub/project/93/interface/api/67697
     */
    public function applyAddAction()
    {
        $res = (new MaterialWmsServer($this->lang, $this->timezone))->setLockConf(5)->applyAddUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($res));




    }

    /**
     * 耗材申请-撤回
     * @api https://yapi.flashexpress.pub/project/93/interface/api/67692
     */
    public function cancelAction()
    {
        $res = (new MaterialWmsServer($this->lang, $this->timezone))->setLockConf(5)->cancelUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($res));
    }

    /**
     * 耗材申请-通过
     * @api https://yapi.flashexpress.pub/project/93/interface/api/67702
     */
    public function passAction()
    {
        $res = (new MaterialWmsServer($this->lang, $this->timezone))->setLockConf(5)->passUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($res));
    }

    /**
     * 耗材申请-驳回
     * @api https://yapi.flashexpress.pub/project/93/interface/api/67707
     */
    public function rejectAction()
    {
        $res = (new MaterialWmsServer($this->lang, $this->timezone))->setLockConf(5)->rejectUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($res));
    }

    /**
     * 耗材申请-详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/67687
     */
    public function detailAction()
    {
        $result = $this->materialWmsServer->detail($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材申请-查看出库信息
     * @api https://yapi.flashexpress.pub/project/93/interface/api/67717
     */
    public function getWmsOutStorageListAction()
    {
        $result = $this->materialWmsServer->getWmsOutStorageList($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }
    /**
     * 耗材申请-查看路由
     * @api https://yapi.flashexpress.pub/project/93/interface/api/67722
     */
    public function getOutboundTrackingInfoAction()
    {
        $result = $this->materialWmsServer->getOutboundTrackingInfo($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材申请-获取收货人
     * @api https://yapi.flashexpress.pub/project/93/interface/api/72117
     */
    public function getWmsConsigneeListAction()
    {
        $result = $this->materialWmsServer->getWmsConsigneeList($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材申请-获取抄送收货人站内信消息详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/79792
     */
    public function getConsigneeCCMsgAction()
    {
        $result = $this->materialWmsServer->getConsigneeCCMsg($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材申请-获取审批通过或驳回站内信消息详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/87965
     */
    public function getAuditMsgAction()
    {
        $result = $this->materialWmsServer->getAuditMsg($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材申请-根据快递单号查看物料详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/91283
     */
    public function getOutboundExpressProductAction()
    {
        $result = $this->materialWmsServer->getOutboundExpressProduct($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材调拨单-站内信详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/91478
     */
    public function getPackageAllotMsgInfoAction()
    {
        $result = $this->materialWmsServer->getPackageAllotMsgInfo($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }
    /**
     * 耗材调拨单-获取枚举
     * @api https://yapi.flashexpress.pub/project/93/interface/api/91652
     */
    public function getPackageAllotOptionsDefaultAction()
    {
        $result = $this->materialWmsServer->getPackageAllotOptionsDefault($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材调拨单-待处理-总数
     * @api https://yapi.flashexpress.pub/project/93/interface/api/91973
     */
    public function getPackageAllotCountAction()
    {
        $result = $this->materialWmsServer->getPackageAllotCount($this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材调拨单-待处理/已处理列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/91508
     */
    public function getPackageAllotListAction()
    {
        $result = $this->materialWmsServer->getPackageAllotList($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材调拨单-查看
     * @api https://yapi.flashexpress.pub/project/93/interface/api/91511
     */
    public function getPackageAllotInfoAction()
    {
        $result = $this->materialWmsServer->getPackageAllotInfo($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材调拨单-查看-查看物流
     * https://yapi.flashexpress.pub/project/93/interface/api/91514
     */
    public function getPackageAllotExpressListAction()
    {
        $result = $this->materialWmsServer->getPackageAllotExpressList($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材调拨单-查看-查看物流-路由
     * @api https://yapi.flashexpress.pub/project/93/interface/api/91517
     */
    public function getPackageAllotExpressRouteAction()
    {
        $result = $this->materialWmsServer->getPackageAllotExpressRoute($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材调拨单-无法调出
     * @api https://yapi.flashexpress.pub/project/93/interface/api/91523
     */
    public function cancelPackageAllotAction()
    {
        $result = $this->materialWmsServer->setLockConf(3)->cancelPackageAllotUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材调拨单-确认调出
     * @api https://yapi.flashexpress.pub/project/93/interface/api/91529
     */
    public function confirmOutPackageAllotAction()
    {
        $result = $this->materialWmsServer->setLockConf(3)->confirmOutPackageAllotUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 耗材调拨单-确认已调入
     * @api https://yapi.flashexpress.pub/project/93/interface/api/91526
     */
    public function confirmInPackageAllotAction()
    {
        $result = $this->materialWmsServer->setLockConf(3)->confirmInPackageAllotUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }
}
