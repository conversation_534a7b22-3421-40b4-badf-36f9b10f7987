<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers\ControllerBase AS GlobalControllerBase;
use FlashExpress\bi\App\Enums\InventoryCheckEnums;
use FlashExpress\bi\App\Server\MaterialInventoryCheckServer;

/**
 * 新资产-资产盘点操作控制器
 * Class MaterialInventoryCheckController
 * @package FlashExpress\bi\App\Controllers
 */
class MaterialInventoryCheckController extends GlobalControllerBase
{
    protected $paramIn;
    protected $inventoryCheckServer;

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        $this->inventoryCheckServer = new MaterialInventoryCheckServer($this->lang, $this->timezone);
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * 资产盘点-资产盘点通知消息详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78352
     */
    public function msgDetailAction()
    {
        $result = $this->inventoryCheckServer->getMsgDetail($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-开始盘点/去盘点
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78892
     */
    public function taskStartAction()
    {
        $result = $this->inventoryCheckServer->taskStart($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-任务清单-待处理、已处理总数
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78372
     */
    public function taskCountAction()
    {
        $staff_id = $this->userinfo['staff_id'];
        //待处理数
        $processed_count_ret = $this->inventoryCheckServer->getTaskCount(['type' => InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PENDING, 'staff_id' => $staff_id]);
        $processed_count = (isset($processed_count_ret['data']) && $processed_count_ret['data']) ? $processed_count_ret['data'] : 0;
        //已结束数
        $completed_count_ret = $this->inventoryCheckServer->getTaskCount(['type' => InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PROCESSED, 'staff_id' => $staff_id]);
        $completed_count = (isset($completed_count_ret['data']) && $completed_count_ret['data']) ? $completed_count_ret['data'] : 0;

        $data = ['processed_count' => $processed_count, 'completed_count' => $completed_count];
        return $this->jsonReturn(self::checkReturn(['data' => $data]));
    }

    /**
     * 资产盘点-任务清单-待处理、已处理列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78377
     */
    public function taskListAction()
    {
        $result = $this->inventoryCheckServer->getTaskList($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-获取任务详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/79667
     */
    public function getTaskInfoAction()
    {
        $result = $this->inventoryCheckServer->getTaskInfo($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-待盘点、已盘点总数
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78402
     */
    public function taskAssetCountAction()
    {
        //待盘点数
        $this->paramIn['type'] = 1;
        $wait_inventory_count = $this->inventoryCheckServer->getTaskAssetCount($this->paramIn, $this->userinfo);
        //已盘点数
        $this->paramIn['type'] = 2;
        $inventory_count = $this->inventoryCheckServer->getTaskAssetCount($this->paramIn, $this->userinfo);
        $data = ['wait_inventory_count' => $wait_inventory_count, 'inventory_count' => $inventory_count];
        return $this->jsonReturn(self::checkReturn(['data' => $data]));
    }

    /**
     * 资产盘点-资产盘点清单-待盘点、已盘点列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78407
     */
    public function taskAssetListAction()
    {
        $result = $this->inventoryCheckServer->getTaskAssetList($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-未盘到
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78417
     */
    public function loseAction()
    {
        $result = $this->inventoryCheckServer->setLockConf(3)->loseUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-批量未盘到
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78472
     */
    public function batchLoseAction()
    {
        $result = $this->inventoryCheckServer->setLockConf(5)->batchLoseUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-盘到
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78422
     */
    public function matchAction()
    {
        $result = $this->inventoryCheckServer->setLockConf(3)->matchUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-批量盘到
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78477
     */
    public function batchMatchAction()
    {
        $result = $this->inventoryCheckServer->setLockConf(5)->batchMatchUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-信息有误
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78427
     */
    public function notMatchAction()
    {
        $result = $this->inventoryCheckServer->setLockConf(3)->notMatchUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-修改使用人
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78497
     */
    public function staffSearchAction()
    {
        $result = $this->inventoryCheckServer->staffSearch($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-修改使用网点
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78502
     */
    public function storeSearchAction()
    {
        $result = $this->inventoryCheckServer->storeSearch($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-查看信息有误
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78427
     */
    public function viewNotMatchAction()
    {
        $result = $this->inventoryCheckServer->viewNotMatch($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-修改数量
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78432
     */
    public function updateNumAction()
    {
        $result = $this->inventoryCheckServer->setLockConf(3)->updateNumUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-搜索资产
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78457
     */
    public function barcodeSearchAction()
    {
        $result = $this->inventoryCheckServer->barcodeSearch($this->paramIn);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-添加资产
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78447
     */
    public function addAction()
    {
        $result = $this->inventoryCheckServer->setLockConf(3)->addUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-删除
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78492
     */
    public function delAction()
    {
        $result = $this->inventoryCheckServer->setLockConf(3)->delUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }


    /**
     * 资产盘点-资产盘点清单-扫码盘点
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78482
     */
    public function isOwnerAction()
    {
        $result = $this->inventoryCheckServer->isOwner($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-确认无资产
     * @api https://yapi.flashexpress.pub/project/93/interface/api/78487
     */
    public function doneAction()
    {
        $result = $this->inventoryCheckServer->setLockConf(3)->doneUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }

    /**
     * 资产盘点-资产盘点清单-更新盘点
     * @api https://yapi.flashexpress.pub/project/93/interface/api/87392
     */
    public function updateAction()
    {
        $result = $this->inventoryCheckServer->setLockConf(3)->updateUseLock($this->paramIn, $this->userinfo);
        return $this->jsonReturn(self::checkReturn($result));
    }
}
