<?php
/**
 * 个人信息 扣税信息收集
 * Created by PhpStorm.
 * User: nick
 * Date: 6/25/23
 * Time: 2:50 PM
 */

namespace FlashExpress\bi\App\Controllers;

//https://flashexpress.feishu.cn/docx/W7sgdkUrKoUOE8xYyjycJq4cnqm
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\TaxCollectionServer;

class TaxCollectionController extends ControllerBase
{

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    //个税信息详情
    public function taxInfoAction()
    {
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $server                 = new TaxCollectionServer($this->lang, $this->timezone);
        $data                   = $server->collectInfo($param);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }



    /**
     * 个税信息 添加
     * @return void
     * @throws ValidationException
     */
    public function taxInfoSaveAction()
    {
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        $validations = [
            "is_disabled"                 => "Required|IntIn:1,2",
            "marital"                     => "Required|IntIn:1,2,3",
            "spouse_work"                 => "Required|IntIn:0,1,2",
            "spouse_is_disabled"          => "Required|IntIn:0,1,2",
            "child_under_18"              => "Required|IntGe:0",
            "child_over_18_study"         => "Required|IntGe:0",
            "child_over_18_end"           => "Required|IntGe:0",
            "child_under_18_disabled"     => "Required|IntGe:0",
            "child_disabled_and_studying" => "Required|IntGe:0",
        ];
        $this->validateCheck($param, $validations);

        $server = new TaxCollectionServer($this->lang, $this->timezone);
        if (get_runtime() == 'dev') {
            $server->collectSave($param);
        } else {
            $server->collectSaveUseLock($param);
        }
        $this->jsonReturn($this->checkReturn([]));
    }


    //枚举接口
    public function taxEnumAction()
    {
        $params['staff_info_id'] = $this->userinfo['staff_id'];

        $server = new TaxCollectionServer($this->lang, $this->timezone);
        $data   = $server->enumData($params);
        $this->jsonReturn($data);
    }
}