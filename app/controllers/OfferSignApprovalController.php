<?php
namespace FlashExpress\bi\App\Controllers;

use app\enums\StaffAuditStatusEnums;
use App\Library\Enums;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\OfferSignApproveServer;
use FlashExpress\bi\App\Server\SalaryServer;
use Exception;

class OfferSignApprovalController extends Controllers\ControllerBase
{
    public function initialize()
    {
        parent::initialize(); // TODO: Change the autogenerated stub
    }

    /**
     * 更新审批状态
     */
    public function updateApprovalAction()
    {
        $params = $this->paramIn;

        $this->validateCheck([
            "status" => $params['status'],
            "id" => $params['audit_id'],
        ], [
            "status" => 'Required|IntIn:2,3,4',
            "id" => 'Required|Int',
        ]);

        $result = '';
        $userinfo = $this->userinfo;

        try {
            $returnArr = $this->atomicLock(function () use ($params, $userinfo, &$result) {
                $remark = isset($params['reject_reason']) && $params['reject_reason'] ? $params['reject_reason'] : '';
                return (new OfferSignApproveServer($this->lang, $this->timezone))->updateApprove($userinfo, $params['audit_id'], $params['status'], $remark);
            }, 'approval_offer_sign_' . $this->userinfo['staff_id']);

            if ($returnArr === false) { //没有获取到锁
                $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
            }

            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            $this->logger->write_log("[updateApprovalAction]error:" . $e->getMessage() . $e->getTraceAsString());
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
     * 获取offer签字记录
     */
	public function getOfferSignDetailAction()
    {
        try{
            $params = $this->paramIn;
            $this->validateCheck([
                "id" => $params['id'],
            ], [
                "id" => 'Required|Int',
            ]);

            $audit_status = StaffAuditStatusEnums::getCodeTxtMap();

            //获取详情
            $returnData['data'] = (new OfferSignApproveServer($this->lang, $this->timezone))->getRecord($params['id']);
            if($returnData['data']){
                //薪资审批撤销，offer签字也会撤销，状态为：0
                if($returnData['data']['approve_state'] == 0){
                    $returnData['data']['approve_state'] = 4;
                }
                $returnData['data']['audit_id'] = $returnData['data']['id']  ?? '';
                $returnData['data']['approve_state_text'] = $audit_status[$returnData['data']['approve_state']];
                $prefix = env('img_prefix');
                $returnData['data']['pdf_path'] = $prefix . $returnData['data']['pdf_path'];
            }

            $this->jsonReturn($this->checkReturn($returnData));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("getOfferSignDetailAction:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
	}
}