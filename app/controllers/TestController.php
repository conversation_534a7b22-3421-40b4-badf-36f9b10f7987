<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Server\DepartmentServer;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use Exception;

/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/3/26
 * Time: 下午5:43
 */


class TestController extends \FlashExpress\bi\App\Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode($this->request->getRawBody(),true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * 测试
     * @return void
     */
    public function indexAction()
    {
        $result = array('code' => 1,'message' => 'hello world', 'data'=> $this->userinfo);
        $res = (new StaffServer('en'))->setLockConf(20)->testUseLock($result);
        $this->jsonReturn($res);
    }

    public function langAction(){
        $notice = $this->getTranslation()->_('can_not_match_show_mailbox',['mailbox'=>'<EMAIL>']);
        $this->jsonReturn(['data'=>$notice]);
    }

    public function infoAction()
    {
        $this->jsonReturn($this->userinfo);
    }


}
