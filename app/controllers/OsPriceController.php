<?php
/**
 *
 * 外协 特殊价格 审批
 * Created by PhpStorm.
 * User: nick
 * Date: 10/20/21
 * Time: 4:27 PM
 */

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Server\OsPriceServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use Exception;

class OsPriceController extends ControllerBase{

    public $job_ids = array(13,110);
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
        $this->url_log($this->paramIn);
    }

    public function addAction(){
        try{
            $param = $this->paramIn;
            $param['user_info'] = $this->userinfo;
            $server = new OsPriceServer($this->lang,$this->timezone);
            $flag = $server->add($param);
            return $this->jsonReturn(self::checkReturn($flag));


        }catch (ValidationException $v){
            return $this->jsonReturn(self::checkReturn(-3, $v->getMessage()));
        }catch (\Exception $e){
            return $this->jsonReturn(self::checkReturn(-3, "server error"));
        }

    }

    //除了网点 其他枚举接口
    public function get_enumAction(){
        $data['store_category'] = OsPriceServer::$store_category;
        $job_ids = $this->job_ids;

        //不确定 是指定 两个快递员职位 还是 包含courier的所有职位 先用这两个
        $data['job_title'] = HrJobTitleModel::find([
//            'conditions' => "job_name like '%courier%'",
            'conditions' => "id in ({ids:array}) ",
            'columns' => 'id,job_name',
            'bind' => ['ids' => $job_ids]
        ])->toArray();

        return $this->jsonReturn(self::checkReturn(array('data' => $data)));
    }


    public function search_storeAction(){
        $param = $this->paramIn;
        $category = empty($param['store_category']) ? 0 : intval($param['store_category']);
        if(empty($category))
            $this->jsonReturn(self::checkReturn(-3,'need store category select'));
        if(empty($param['search_name']))
            $this->jsonReturn(self::checkReturn(-3,'need store search name'));

        $sys_server = new SysStoreServer($this->lang,$this->timezone);
        $list = $sys_server->searchStore(trim($param['search_name']),0,$category);
        $return = array();
        if(!empty($list)){
            foreach ($list as $v){

                $row['id'] = $v['id'];
                $row['name'] = $v['name'];
                $return[] = $row;
            }
        }
        $this->jsonReturn(self::checkReturn(array('data' => $return)));
    }

    /**
     * 获取 基本价格
     */
    public function get_price_listAction()
    {
        $param = $this->paramIn;
        $server = new OsPriceServer($this->lang,$this->timezone);
        $res = $server->get_base_price($param);
        $this->jsonReturn(self::checkReturn($res));
    }

    //审核
    public function audit_priceAction(){
        try{
            $param = $this->paramIn;
            $user_info = $this->userinfo;
            $server = new OsPriceServer($this->lang,$this->timezone);
            $return = $server->update_status($param,$user_info);
            $this->jsonReturn($return);
        }catch (ValidationException $v){
            $this->jsonReturn(self::checkReturn(-3, $v->getMessage()));
        }catch (\Exception $e){
            $this->logger->write_log("audit_opr " . json_encode($param) . $e->getMessage() . $e->getTraceAsString());
            $this->jsonReturn(array('code' => -1, 'message' => 'failed', 'data' => null));
        }
    }

}
