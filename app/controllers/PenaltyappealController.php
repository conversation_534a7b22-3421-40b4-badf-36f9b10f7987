<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\PenaltyAppealServer;
use Exception;

class PenaltyappealController extends Controllers\ControllerBase
{

    public $paramIn;

    public function initialize()
    {
        parent::initialize(); // TODO: Change the autogenerated stub

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }


    /**
     *
     * 修改审批状态
     * https://yapi.flashexpress.pub/project/93/interface/api/26042
     *
     */
    public function updatePenaltyAction()
    {
        try {
            $paramIn = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $validations = [
                "staff_id" => "Required|Int",
                "audit_id" => "Required|Int",

                "status" => "Required|IntIn:" . enums::$audit_status['approved'] . "," . enums::$audit_status['dismissed'] . "," . enums::$audit_status['revoked'] . "|>>>:status is not invalid",
                "reject_reason" => "IfIntEq:status," . enums::$audit_status['dismissed'] . "|Required|StrLenGeLe:1,1000|>>>:" . $this->getTranslation()->_('5110'),

                "reason" => "IfIntEq:status," . enums::$audit_status['approved'] . "|Required|StrLenGeLe:0,1000",
            ];
            $this->validateCheck($paramIn, $validations);
            $reason = null;
            if ($paramIn['status'] == enums::$audit_status['approved']) $reason = $paramIn['reason'];
            if ($paramIn['status'] == enums::$audit_status['dismissed']) $reason = $paramIn['reject_reason'];

            (new PenaltyAppealServer($this->lang, '+07:00'))->updateApprove(
                $paramIn['audit_id'],
                $paramIn['staff_id'],
                $paramIn['status'],
                $reason
            );

        } catch (\Exception $e) {

            $this->getDI()->get("logger")->write_log("approval_approve_error "
                . $e->getFile()
                . " line " . $e->getLine()
                . " message " . $e->getMessage()
                . " trace " . $e->getTraceAsString(), "error");

            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }

        $this->jsonReturn($this->checkReturn([]));
    }


}