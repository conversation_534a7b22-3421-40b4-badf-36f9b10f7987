<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Server\ReportServer;
use FlashExpress\bi\App\library\HttpCurl;
use Exception;
use FlashExpress\bi\App\Server\WorkbenchServer;

class WorkbenchController extends Controllers\ControllerBase
{

    public function initialize()
    {
        parent::initialize();
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    public function indexAction()
    {
        $userinfo = $this->userinfo;
        try {
            $returnArr = (new WorkbenchServer($this->lang, $this->timezone))->getInfo($userinfo);
            return $this->jsonReturn(self::checkReturn(['data'=>$returnArr]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 测试接口
     */
    public function tAction()
    {
        $userinfo = $this->userinfo;
        $organizationId = $this->request->get('store_id');
        try {
            $setting_model = new BySettingRepository();
            $dc_auth_code=  $setting_model->get_setting('dc_staff_statistics_auth_code');
            $s = auth_code($organizationId, 'ENCODE', $dc_auth_code);
            return $this->jsonReturn(self::checkReturn(['data'=>$s]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * @description: 获取首页菜单
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/8/9 21:23
     */

    public function  get_menuAction(){
        $paramIn = $this->userinfo;
        $paramIn['lang'] = $this->lang;
        //获取权限
        /**
         * @see WorkbenchServer::getMenu()
         */
        $data = (new WorkbenchServer($this->lang, $this->timezone))->getMenuFromCache($paramIn);
        return $this->jsonReturn(self::checkReturn(['data'=>$data]));

    }

}
