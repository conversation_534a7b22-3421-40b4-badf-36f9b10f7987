<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Server\PaperDocumentServer;

class PaperDocumentController extends ControllerBase
{
    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);

    }

    /**
     * 根据二维码获取单据基本信息
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function getBaseInfoAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations = [
            "sn" => "Required|StrLenGe:1",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $returnArr = (new PaperDocumentServer($this->lang , $this->timezone))->getBasicInfo($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取单据详细信息
     * @return void
     */
    public function getPaperDetailAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations = [
            "sn" => "Required|StrLenGe:1",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $returnArr = (new PaperDocumentServer($this->lang , $this->timezone))->getPaperDetail($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 纸质单据确认
     */
    public function confirmAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations = [
            "sn"             => "Required|StrLenGe:1",
            "confirm_status" => "Required|IntIn:3,4",
            "reason_type"    => "Required|Arr",
            "remark"         => "StrLenGe:0",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $returnArr = (new PaperDocumentServer($this->lang , $this->timezone))->confirm($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn($returnArr));
    }

    /**
     * 纸质单据批量确认
     */
    public function batchConfirmAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations = [
            "sn" => "Required|ArrLenGe:1",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $returnArr = (new PaperDocumentServer($this->lang , $this->timezone))->batchConfirm($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn($returnArr));
    }

    /**
     * 纸质单据提交
     */
    public function submitAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations = [
            "sn" => "Required|StrLenGe:1",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $returnArr = (new PaperDocumentServer($this->lang , $this->timezone))->submit($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn($returnArr));
    }

    /**
     * 获取纸质单据待补全原因
     */
    public function getConfirmReasonTypeListAction()
    {
        $returnArr = (new PaperDocumentServer($this->lang , $this->timezone))->getConfirmReasonTypeList();
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }
}