<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers\ControllerBase;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\FreightDiscountModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\AdjustRoleServer;
use FlashExpress\bi\App\Server\AuditListServer;
use FlashExpress\bi\App\Server\DepartmentServer;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\HrShiftServer;
use FlashExpress\bi\App\Server\MilesServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use Exception;


/**
 * 该控制器仅用于测试同学造数据用
 */
class QaApiController extends \FlashExpress\bi\App\Controllers\ControllerBase
{

    protected $paramIn;

    public function initialize()
    {
        if(!in_array(RUNTIME,['dev','tra','training'])){
            $this->jsonReturn($this->checkReturn(['data' => [],'code'=>-1,'msg'=>'接口只适用于测试、training环境']));
        }
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode($this->request->getRawBody(),true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }

    }

    /**
     * 里程上报
     */
    public function create_milesAction()
    {
        try{
            $params = $this->paramIn;
            $this->validateCheck([
                                     "mileage_date" => $params['mileage_date'],
                                     "staff_info_id" => $params['staff_info_id'],
                                     "kilometres" => $params['kilometres'],
                                     "mileage_record_type" => $params['mileage_record_type'],
                                     "image" => $params['image'],
                                 ], [
                                     "mileage_date" => 'Required|Date|>>>:输入日期 Y-m-d',
                                     "staff_info_id" => 'Required|Int|>>>:输入工号',
                                     "kilometres" => 'Required|Int|>>>:输入里程',
                                     "mileage_record_type" => 'Required|IntIn:1,2|>>>:输入1:上班或2:下班',
                                     "image" => 'Str|StrLenLe:500|>>>:图片地址',
                                 ]);
            (new MilesServer($this->lang,$this->timezone))->testAdd($params);
            $this->jsonReturn($this->checkReturn(1));
        } catch (Exception $e) {
            $this->jsonReturn($this->checkReturn(['data' => [],'code'=>-1,'msg'=>$e->getMessage()]));
        }
    }


    public function toolAction(){
        $param = $this->paramIn;

        if(empty($param['attendance_date']) || empty($param['staff_info_id']))
            return $this->jsonReturn(array('code' => -3, 'message' => 'need date or staff id input', 'data' => null));


        if(empty($param['start_time']) && empty($param['end_time']))
            return $this->jsonReturn(array('code' => -3, 'message' => 'need time input', 'data' => null));

        if(!empty($param['start_time'])){
            $add_hour = $this->config->application->add_hour;
            $start_time = date('Y-m-d H:i:s',strtotime($param['start_time']) - $add_hour * 3600);
        }
        if(!empty($param['end_time'])){
            $add_hour = $this->config->application->add_hour;
            $end_time = date('Y-m-d H:i:s',strtotime($param['end_time']) - $add_hour * 3600);
        }

        $date = date('Y-m-d',strtotime($param['attendance_date']));
        $staff_id = intval($param['staff_info_id']);

        $check = StaffWorkAttendance::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
        if(!empty($check)){
            if(!empty($start_time))
                $check->started_at = $start_time;

            if(!empty($end_time))
                $check->end_at = $end_time;

            $check->update();
            return $this->jsonReturn(array('code' => 1, 'message' => 'success', 'data' => null));
        }

        //不存在数据 获取员工信息 班次 等
        $staff_re    = new StaffRepository($this->lang);
        $staff_info  = $staff_re->getStaffPosition($staff_id);
        $shiftServer = new HrShiftServer();
        $shift_data  = $shiftServer->getShiftInfos($staff_id, [$date]);
        $shift_data  = $shift_data[$date] ?? [];

        //组装数据
        $insert['staff_info_id']     = $staff_id;
        $insert['attendance_date']   = $date;
        $insert['shift_start']       = $shift_data['start'] ?? '';
        $insert['shift_end']         = $shift_data['end'] ?? '';
        $insert['shift_id']          = $shift_data['shift_id'] ?? 0;
        $insert['organization_id']   = empty($staff_info['sys_store_id']) ? null : $staff_info['sys_store_id'];
        $insert['organization_type'] = empty($staff_info['organization_type']) ? null : $staff_info['organization_type'];

        $insert['started_store_id'] = $insert['organization_id'];
        $insert['end_store_id']     = $insert['organization_id'];
        $insert['job_title']        = $staff_info['job_title'];

        if(!empty($param['start_time'])){//上班
            $add_hour = $this->config->application->add_hour;
            $time = date('Y-m-d H:i:s',strtotime($param['start_time']) - $add_hour * 3600);
            $insert['started_at'] = $time;
            $insert['started_state'] = 5;
            $insert['started_remark'] = 'auto_system';//自动化
        }

        if(!empty($param['end_time'])){//下班
            $add_hour = $this->config->application->add_hour;
            $time = date('Y-m-d H:i:s',strtotime($param['end_time']) - $add_hour * 3600);
            $insert['end_at'] = $time;
            $insert['end_state'] = 5;
            $insert['end_remark'] = 'auto_system';
        }

        $model = new StaffWorkAttendance();
        $flag = $model->create($insert);

        if($flag)
            return $this->jsonReturn(array('code' => 1, 'message' => 'success', 'data' => null));
        return $this->jsonReturn(array('code' => -1, 'message' => 'failed', 'data' => null));


    }

    //配置轮休
    public function workdayAction(){
        $param = $this->paramIn;
        $staff_id = intval($param['staff_info_id']);
        $date = date("Y-m-d",strtotime($param['date']));

        if(empty($staff_id) || empty($date))
            return $this->jsonReturn(array('code' => -3, 'message' => 'need date or staff id input', 'data' => null));

        $check = HrStaffWorkDayModel::findFirst("staff_info_id = {$staff_id} and date_at = '{$date}'");

        if(!empty($check))
            return $this->jsonReturn(array('code' => 1, 'message' => 'success', 'data' => null));

        $insert['staff_info_id'] = $staff_id;
        $insert['month'] = date('Y-m',strtotime($date));;
        $insert['date_at'] = $date;
        $insert['operator'] = $this->userinfo['id'];
        $insert['remark'] = "auto system";
        $model = new HrStaffWorkDayModel();
        $flag = $model->create($insert);

        if($flag)
            return $this->jsonReturn(array('code' => 1, 'message' => 'success', 'data' => null));
        return $this->jsonReturn(array('code' => -1, 'message' => 'failed', 'data' => null));

    }


}
