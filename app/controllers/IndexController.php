<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\OtherServer;
use FlashExpress\bi\App\library\HttpCurl;
use Exception;
use FlashExpress\bi\App\Server\PunchInServer;

class IndexController extends Controllers\ControllerBase
{

    public function initialize()
    {
        parent::initialize();
        $this->server  = ['other' => new OtherServer($this->lang , $this->timezone) ];
    }

    public function onConstruct()
    {
        //echo 'IndexController onConstruct<br>';
        parent::onConstruct();

    }

    public function indexAction()
    {
        $this->jsonReturn(self::checkReturn(['data' => $this->userinfo]));
    }

    /**
     * 打卡提示未回款金额
     * @Access  public
     * @Param   request
     * @Return  array
     * @throws ValidationException
     */
    public function remittanceDialogAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        $validations = [
            "clock_type" => "IntIn:1,2|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        //[2]业务处理
        if(empty($paramIn['clock_type'])){
            $paramIn['clock_type'] = 2;
        }
        //上班打卡
        if($paramIn['clock_type'] == 2){
            (new PunchInServer())->pushData($userinfo);
        }

        if ((new AttendanceServer($this->lang,
            $this->timezone))->isSeniorBranchSupervisorVirtualId($this->userinfo['staff_id'])) {
            throw new ValidationException($this->t->_('virtual_staff_id_error_01'),-3);
        }
        //在系统设置-HRIS-punch_rule范围内的职位，当天为Off 且 当日没有申请加班（含待审批和已同意），不允许上/下班打卡，提示“休息日不能打卡”
        if(isCountry('PH')){
            $punchServer = new PunchInServer($this->lang, $this->timezone);
            $workdayNotice = $punchServer->checkOff($paramIn, $userinfo, 'punch_in');
            if($workdayNotice !== true){
                $this->jsonReturn($workdayNotice);
            }
        }

        $cache       = $this->getDI()->get('redis');
        $cacheTime   = 3;//秒
        $organization_id = $userinfo['organization_id'];
        $cacheKey    = 'clock_type_'.$paramIn['clock_type'].'remittanceDialog_' . $organization_id;
        //如果是快递员下班或者缓存中不存在
        $toselect = ( $paramIn['clock_type'] == 1 && in_array(1, $userinfo['positions']) );
        if($toselect){
            $returnArr = (new OtherServer($this->lang , $this->timezone))->remittanceDialogS($paramIn, $userinfo);
        }else{
            $returnArr = $cache->get($cacheKey);
            if (empty($returnArr)){
                $returnArr = (new OtherServer($this->lang , $this->timezone))->remittanceDialogS($paramIn, $userinfo);
                $cache->save($cacheKey, $returnArr, $cacheTime);
            }
        }
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
}