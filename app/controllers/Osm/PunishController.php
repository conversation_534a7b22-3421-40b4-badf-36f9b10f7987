<?php
/**
 * Author: Bruce
 * Date  : 2023-07-31 15:32
 * Description:
 */

namespace FlashExpress\bi\App\Controllers\Osm;


use FlashExpress\bi\App\Server\Osm\PunishServer;

class PunishController extends ControllerBase
{
    public function initialize()
    {
        parent::initialize();
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 获取处罚列表
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function getListAction()
    {
        $params      = $this->paramIn;
        $validations = [
            "month"  => "Required|Str|>>>:month" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($params, $validations);
        $result = (new PunishServer($this->lang))->getListInfo($params, $this->osm_user_info);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 获取处罚详情
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function detailAction()
    {
        $params      = $this->paramIn;
        $validations = [
            "penalty_id"  => "Required|Str|>>>:month" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($params, $validations);
        $result = (new PunishServer($this->lang))->detail($params);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

}