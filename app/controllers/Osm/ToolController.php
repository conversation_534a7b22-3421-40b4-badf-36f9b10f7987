<?php
namespace FlashExpress\bi\App\Controllers\Osm;

use FlashExpress\bi\App\Server\ToolServer;

class ToolController extends ControllerBase
{
    public $paramIn = [];
    public $server  = [];
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode($this->request->getRawBody(), true);
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function img_uploadAction()
    {
        $paramIn = $this->paramIn;
        $result  = (new ToolServer($this->lang, $this->timezone))->img_upload($paramIn);
        return $this->jsonReturn($result);
    }
    
}
