<?php
/**
 * Author: Bruce
 * Date  : 2022-12-01 19:08
 * Description:
 */

namespace FlashExpress\bi\App\Controllers\Osm;


use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Server\Osm\OutsourcingOrderServer;
use App\Country\Tools;

class OutsourcingOrderController extends ControllerBase
{
    public function initialize()
    {
        parent::initialize();
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 获取外协申请订单列表
     */
    public function getOrderListAction()
    {
        $params      = $this->paramIn;
        $server    = Tools::reBuildCountryInstance(new OutsourcingOrderServer($this->lang),[$this->lang, $this->timezone]);
        $result = $server->getOrderList($params, $this->osm_user_info);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 获取外协订单已配置人员信息
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function getOrderStaffListAction()
    {
        $params      = $this->paramIn;
        $validations = [
            "order_id"  => "Required|Int|>>>:order_id" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($params, $validations);
        $result = (new OutsourcingOrderServer($this->lang))->getOrderStaffList($params);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }


    /**
     * 获取外协订单考勤excel
     * @return void
     * @throws BusinessException
     */
    public function getAttendanceExcelAction()
    {
        $params      = $this->paramIn;
        $validations = [
            "serial_no"  => "Required|Str|>>>:serial_no" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($params, $validations);
        $result = (new OutsourcingOrderServer($this->lang))->getAttendanceExcel($params['serial_no']);
        $this->jsonReturn($this->checkReturn(['data' => ['file_url' => $result]]));
    }

    /**
     * 获取可选人员列表
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function getOptionalStaffListAction()
    {
        $params      = $this->paramIn;
        $validations = [
            "order_id"  => "Required|Int|>>>:order_id" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($params, $validations);
        $params['company_id'] = $this->osm_user_info['id'];

        $result = (new OutsourcingOrderServer($this->lang))->getOptionalStaffList($params);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    public function submitStaffAction()
    {
        $params      = $this->paramIn;
        $validations = [
            "order_id"  => "Required|Int|>>>:order_id" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($params, $validations);
        $params['company_id'] = $this->osm_user_info['id'];

        //[4]业务处理
        $returnArr = $this->atomicLock(function () use ($params) {
            return (new OutsourcingOrderServer($this->lang))->submitStaff($params);
        }, 'submitStaff_'.md5(json_encode($params)), 300);

        if ($returnArr === false) { //没有获取到锁
            $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
        }
        $this->jsonReturn($this->checkReturn($returnArr));
    }
}