<?php

namespace FlashExpress\bi\App\Controllers\Osm;

use App\Country\Tools;
use FlashExpress\bi\App\Server\Osm\AttendanceServer;
use FlashExpress\bi\App\Server\Osm\OutsourcingOrderServer;
use FlashExpress\bi\App\Server\Osm\OutsourcingStaffServer;

class OutsourcingStaffController extends ControllerBase
{

    public function initialize()
    {
        parent::initialize();
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 获取枚举数据
     */
    public function getSysInfoAction()
    {
        $result = Tools::reBuildCountryInstance(new OutsourcingStaffServer($this->lang), [$this->lang])->getSysInfo($this->osm_user_info);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 添加外协员工
     */
    public function addOutsourcingStaffAction()
    {
        $params      = $this->paramIn;

        $validations = [
            "identity_file_a" => "Required|StrLenGeLe:1,255|>>>:".$this->getTranslation()->_('outsourcing_company_no_identity_img'),
            "face_img_path"   => "Required|StrLenGeLe:1,255|>>>:".$this->getTranslation()->_('outsourcing_company_no_face_img'),
            "name"            => "Required|StrLenGeLe:1,32|>>>:".$this->getTranslation()->_('outsourcing_company_no_name'),
            "sex"             => "Required|IntIn:1,2|>>>:".$this->getTranslation()->_('outsourcing_company_no_sex'),
            "nationality"     => "Required|IntGe:1|>>>:".$this->getTranslation()->_('outsourcing_company_no_nationality'),
            "identity"        => "Required|StrLenGeLe:1,32|>>>:".$this->getTranslation()->_('outsourcing_company_no_identity'),
            "sys_store_id"    => "Required|StrLenGe:1|>>>:".$this->getTranslation()->_('outsourcing_company_no_store'),
            "job_id"          => "Required|IntGe:1|>>>:".$this->getTranslation()->_('outsourcing_company_no_job'),
        ];
        $this->validateCheck($params, $validations);
        $outsourcingStaffServer = Tools::reBuildCountryInstance(new OutsourcingStaffServer($this->lang), [$this->lang]);
        /**
         * @see OutsourcingStaffServer::addOutsourcingStaff
         */
        $result = $outsourcingStaffServer->addOutsourcingStaffUseLock($params, $this->osm_user_info);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 更新员工信息
     */
    public function updateOutsourcingStaffAction()
    {
        $params      = $this->paramIn;

        $validations = [
            'staff_info_id'   => "Required|IntGe:1|>>>:".$this->getTranslation()->_('outsourcing_company_no_staff_id'),
            "identity_file_a" => "Required|StrLenGeLe:1,255|>>>:".$this->getTranslation()->_('outsourcing_company_no_identity_img'),
            "face_img_path"   => "Required|StrLenGeLe:1,255|>>>:".$this->getTranslation()->_('outsourcing_company_no_face_img'),
            "name"            => "Required|StrLenGeLe:1,50|>>>:".$this->getTranslation()->_('outsourcing_company_no_name'),
            "sex"             => "Required|IntIn:1,2|>>>:".$this->getTranslation()->_('outsourcing_company_no_sex'),
            "nationality"     => "Required|IntGe:1|>>>:".$this->getTranslation()->_('outsourcing_company_no_nationality'),
            "identity"        => "Required|StrLenGeLe:1,32|>>>:".$this->getTranslation()->_('outsourcing_company_no_identity'),
            "sys_store_id"    => "Required|StrLenGe:1|>>>:".$this->getTranslation()->_('outsourcing_company_no_store'),
            "job_id"          => "Required|IntGe:1|>>>:".$this->getTranslation()->_('outsourcing_company_no_job'),
        ];
        $this->validateCheck($params, $validations);
        $outsourcingStaffServer = Tools::reBuildCountryInstance(new OutsourcingStaffServer($this->lang), [$this->lang]);
        $result = $outsourcingStaffServer->updateOutsourcingStaffUseLock($params, $this->osm_user_info);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 删除外协员工
     */
    public function delOutsourcingStaffAction()
    {
        $params      = $this->paramIn;
        $validations = [
            'staff_info_id' => "Required|IntGe:1|>>>:".$this->getTranslation()->_('outsourcing_company_no_staff_id'),
        ];
        $this->validateCheck($params, $validations);
        $result = (new OutsourcingStaffServer($this->lang))->delOutsourcingStaff($params, $this->osm_user_info);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 获取单个员工信息
     */
    public function getOneOutsourcingStaffAction()
    {
        $params      = $this->paramIn;
        $validations = [
            'staff_info_id' => "Required|IntGe:1|>>>:".$this->getTranslation()->_('outsourcing_company_no_staff_id'),
        ];
        $this->validateCheck($params, $validations);
        $result = (new OutsourcingStaffServer($this->lang))->getOneOutsourcingStaff($params, $this->osm_user_info);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 获取外协员工列表
     */
    public function getOutsourcingStaffListAction()
    {
        $params      = $this->paramIn;
        $validations = [
            "name"  => "StrLenGeLe:0,50",
            'page'  => "Required|IntGe:1",
            'limit' => "Required|IntGe:1",
        ];
        $this->validateCheck($params, $validations);
        $result = (new OutsourcingStaffServer($this->lang))->getOutsourcingStaffList($params, $this->osm_user_info);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 获取登录人 所属外协公司信息
     * 用于协议信息
     */
    public function getLoginCompanyInfoAction()
    {
        $result = (new OutsourcingStaffServer($this->lang))->getLoginCompanyInfo($this->osm_user_info);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 是否同意隐私协议，提交
     */
    public function submitAgreementAction()
    {
        $params      = $this->paramIn;
        $validations = [
            'is_agreement' => "Required|IntIn:1,2",
        ];
        $this->validateCheck($params, $validations);

        $userInfo = $this->osm_user_info;

        $redisKey  = "submit_agreement_{$userInfo['id']}";

        $result = $this->atomicLock(function () use ($params, $userInfo) {
            return  (new OutsourcingStaffServer($this->lang))->submitAgreement($params, $userInfo);
        }, $redisKey, 10);

        if ($result === false) { //没有获取到锁
            return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
        }

        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 获取未读数量
     */
    public function unReadCountAction()
    {
        $data = [
            'un_read_number'         => (new OutsourcingOrderServer($this->lang))->getOutSourcingOrderCount($this->osm_user_info['id']),
            'un_read_att_correction' => (new AttendanceServer($this->lang))->getPendingCount($this->osm_user_info['id']),
        ];
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }


}