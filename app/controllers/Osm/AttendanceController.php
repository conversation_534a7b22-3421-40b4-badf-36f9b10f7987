<?php

namespace FlashExpress\bi\App\Controllers\Osm;

use FlashExpress\bi\App\Server\Osm\AttendanceServer;

class AttendanceController extends ControllerBase
{

    /**
     * 补卡列表
     */
    public function correctionListAction()
    {
        $this->validateCheck($this->paramIn, [
            'status' => "Required|IntIn:1,2,3",
        ]);
        [$page, $pageSize] = $this->getPaginationParams();
        $server = new AttendanceServer($this->lang, $this->timezone);
        [$list, $count] = $server->getCorrectionList($this->osm_user_info['id'], $this->paramIn, $page, $pageSize);

        $this->jsonReturn($this->checkReturn(['data'=>  ['items' => $list, 'count' => $count]]));

    }

    /**
     * 完善补卡申请
     */
    public function fillCorrectionAction()
    {
        $params = $this->paramIn;
        $this->validateCheck($params, [
            'id'                  => "Required|Int",
            'correction_datetime' => "Required|DateTime",
            'reason'              => "Required|StrLenGeLe:2,800",
            'extra'               => "Required|ArrLenGeLe:1,3",
            'extra[*]'            => "Url",
        ]);

        $server = new AttendanceServer($this->lang, $this->timezone);
        $server->fillCorrectionUseLock($params['id'],$params['correction_datetime'],$params['reason'],$params['extra']);
        $this->jsonReturn($this->checkReturn(['data'=>true]));
    }


}