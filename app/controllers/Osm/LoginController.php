<?php

namespace FlashExpress\bi\App\Controllers\Osm;

use FlashExpress\bi\App\Server\Osm\LoginServer;

class LoginController extends ControllerBase
{

    public function initialize()
    {
        if(function_exists('molten_get_traceid')){
            $traceid = molten_get_traceid();
        }
        header('traceid:' . ($traceid ?? ''));
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);

        $headerData = $this->request->getHeaders();
        $language   = $this->processingDefault($headerData, 'Accept-Language', 1, 'th');
        $this->lang = $language;
    }

    /**
     * 外协公司登录接口
     */
    public function loginAction()
    {
        $params     = $this->paramIn;
        $headerData = $this->request->getHeaders();
        //处理默认数据
        $params['lang']         = $this->lang = $this->processingDefault($headerData, 'Accept-Language', 0, 'zh-CN');
        $params['ip_address']   = $this->request->getServer('REMOTE_ADDR');   //IP address
        $params['lng']          = $this->processingDefault($headerData, 'X-Device-Lng');    //经度
        $params['lat']          = $this->processingDefault($headerData, 'X-Device-Lat');    //纬度
        $params['version']      = $this->processingDefault($headerData, 'X-Version');    //版本
        $params['device_model'] = $this->processingDefault($headerData, 'X-Device-Model');    //设备型号
        $params['os']           = $this->processingDefault($headerData, 'X-Os');    //设备型号
        $params['os_version']   = $this->processingDefault($headerData, 'X-Os-Version');    //设备型号

        $phoneLenConf = [
            'TH' => '10,10',
            'MY' => '10,11',
        ];
        $phoneLen = $phoneLenConf[get_country_code()] ?? '10,10';

        $translationKey = isCountry("MY") ? 'outsourcing_company_name_no_null_my' : 'outsourcing_company_name_no_null';

        $validations = [
            "login"        => "Required|StrLenGeLe:{$phoneLen}|>>>:".$this->getTranslation()->_($translationKey),
            "password"     => "Required|StrLen:6|>>>:".$this->getTranslation()->_('outsourcing_company_wrong_password2'),
            "clientid"     => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "clientsd"     => "Required|Int|>>>:".$this->getTranslation()->_('miss_args'),
            "os"           => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "version"      => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "lang"         => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "device_model" => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($params, $validations);
        if (!isset($params['select_privacy']) || $params['select_privacy'] != 1) {
            $this->jsonReturn($this->checkReturn(-3,
                $this->getTranslation()->_('outsourcing_company_no_select_privacy')));
        }

        $result = (new LoginServer($this->lang))->login($params);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 外协公司登出接口
     */
    public function logoutAction()
    {
        $this->loginCheck();

        $headerData                 = $this->request->getHeaders();
        $params['x_osm_session_id'] = $this->processingDefault($headerData, 'X-Osm-Session-Id');

        $validations = [
            "x_osm_session_id" => "Required|Str|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($params, $validations);
        $result = (new LoginServer($this->lang))->logout($params);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 校验密码
     */
    public function verifyPasswordAction()
    {
        $this->loginCheck();
        $params      = $this->paramIn;
        $validations = [
            "password" => "Required|StrLen:6|>>>:".$this->getTranslation()->_('outsourcing_company_wrong_password2'),
        ];
        $this->validateCheck($params, $validations);

        $result = (new LoginServer($this->lang))->verifyPassword($params, $this->osm_user_info);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 修改密码
     */
    public function resetPasswordAction()
    {
        $this->loginCheck();

        $params     = $this->paramIn;
        $headerData = $this->request->getHeaders();
        //处理默认数据
        $params['lang']             = $this->lang = $this->processingDefault($headerData, 'Accept-Language', 0,
            'zh-CN');
        $params['ip_address']       = $this->request->getServer('REMOTE_ADDR');   //IP address
        $params['lng']              = $this->processingDefault($headerData, 'X-Device-Lng');    //经度
        $params['lat']              = $this->processingDefault($headerData, 'X-Device-Lat');    //纬度
        $params['version']          = $this->processingDefault($headerData, 'X-Version');    //版本
        $params['device_model']     = $this->processingDefault($headerData, 'X-Device-Model');    //设备型号
        $params['os']               = $this->processingDefault($headerData, 'X-Os');    //设备型号
        $params['x_osm_session_id'] = $this->processingDefault($headerData, 'X-Osm-Session-Id');
        $validations                = [
            "clientid"     => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "clientsd"     => "Required|Int|>>>:".$this->getTranslation()->_('miss_args'),
            "os"           => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "version"      => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "lang"         => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "device_model" => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "new_password" => "Required|StrLen:6|>>>:".$this->getTranslation()->_('outsourcing_company_wrong_password2'),
        ];
        $this->validateCheck($params, $validations);
        $result = (new LoginServer($this->lang))->resetPassword($params, $this->osm_user_info);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 解除密码超限
     */
    function unfreezePasswordAction()
    {
        //$this->loginCheck();
        $params      = $this->paramIn;
        $validations = [
            "company_phone" => "Required|StrLen:10|>>>:".$this->getTranslation()->_('outsourcing_company_name_no_null'),
        ];
        $this->validateCheck($params, $validations);
        $result = (new LoginServer($this->lang))->unfreezePassword($params['company_phone']);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 无需登录，验证app是否最新版本，是否强更
     */
    public function verifyAppVersionAction()
    {
        $headerData        = $this->request->getHeaders();
        $params['version'] = isset($headerData['X-Version']) ? $headerData['X-Version'] : '';    //版本
        $params['os']      = isset($headerData['X-Os']) ? $headerData['X-Os'] : '';    //系统

        $validations = [
            "os"      => "Required|StrLenGe:1|>>>:".$this->getTranslation()->_('miss_args'),
            "version" => "Required|StrLenGe:1|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($params, $validations);

        $result = (new LoginServer($this->lang))->verifyAppVersion($params);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 注销账号
     */
    public function cancellationAction()
    {
        $this->loginCheck();

        $headerData                 = $this->request->getHeaders();
        $params['x_osm_session_id'] = $this->processingDefault($headerData, 'X-Osm-Session-Id');

        $validations = [
            "x_osm_session_id" => "Required|Str|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($params, $validations);
        $result = (new LoginServer($this->lang))->cancellation($params, $this->osm_user_info);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 获取用户device token
     */
    public function getDeviceTokenAction()
    {
        $this->loginCheck();
        $params = $this->paramIn;

        $headerData = $this->request->getHeaders();

        $params['accept_language']  = $this->processingDefault($headerData, 'Accept-Language', 0, 'zh-CN');
        $params['os']               = $this->processingDefault($headerData, 'X-Os');    //设备型号

        $validations = [
            "os"               => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "accept_language"  => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "device_token"     => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($params, $validations);

        $userInfo = $this->osm_user_info;

        $redisKey  = "submit_agreement_{$userInfo['id']}";

        $result = $this->atomicLock(function () use ($params, $userInfo) {
            return  (new LoginServer($this->lang))->addDeviceToken($params, $userInfo);
        }, $redisKey, 10);

        if ($result === false) { //没有获取到锁
            return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
        }

        $this->jsonReturn($this->checkReturn($result));
    }
}