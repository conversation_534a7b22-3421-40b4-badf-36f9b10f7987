<?php

namespace FlashExpress\bi\App\Controllers\Osm;

use Exception;
use FlashExpress\bi\App\Core\PhalBaseController;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyModel;
use Phalcon\Translate\Adapter\NativeArray;
use WebGeeker\Validation\Validation;

class ControllerBase extends PhalBaseController
{
    public $lang; // 语言变量
    public $timezone;  //时区
    public $osm_user_info; //当前登录用户信息

    //全局参数获取
    protected $paramIn = null;
    
    public function initialize()
    {
        parent::initialize();

        $this->initLogic();
        
        //检查是否已登录
        $this->loginCheck();
        
        //模板关闭
        $this->view->disable();
    }

    
    /**
     * 检测登录
     */
    public function loginCheck()
    {
        $headerData = $this->request->getHeaders();
        $x_osm_session_id = $this->processingDefault($headerData, 'X-Osm-Session-Id');
        $language = $this->processingDefault($headerData, 'Accept-Language', 1, 'th');
        
        if (empty($x_osm_session_id)) {
            $error['X-Osm-Session-Id'] = $x_osm_session_id;
            $error['Accept-Language'] = $language;
            $this->jsonReturn(self::checkReturn(['code' => ErrCode::VALIDATE_ERROR, 'msg' => $error]));
        }
        
        $di = $this->getDI();
        $str = $di['config']['application']['authenticate'];
        $arr = explode('_', $x_osm_session_id);
        $time = $arr[0];
        $staff_id = $arr[2];
        $auth = sha1(md5($time . $str . $staff_id));
        //验证是否登录
        if ($auth == $arr[1]) {
            $cache_key = md5($auth);
            //验证后获取缓存信息
            $cache = $di->get('redisLib');
            if (!empty($cache->get($cache_key))) {
                $result = json_decode($cache->get($cache_key), true);
            }
        }
        if (empty($result)) {
            $this->getDI()->get('logger')->write_log('login-check-fail' . json_encode($headerData), 'info');
            $this->jsonReturn(['code' => ErrCode::LOGIN_CHECK_TOKEN_EXPIRED, 'msg' => 'token expired', 'message' => 'token expired', 'data' => null]);
        }
        header('uid:' . $staff_id);
        //验证是否注销--临时方案
        $outsourcing_company_model = new OutsourcingCompanyModel();
        $outsourcing_company = $outsourcing_company_model->getOneByPhone($staff_id);
        if (empty($outsourcing_company) || $outsourcing_company['deleted'] != 0) {
            $this->getDI()->get('logger')->write_log('cancellation_check' . json_encode($outsourcing_company), 'info');
            $this->jsonReturn(['code' => ErrCode::LOGIN_CHECK_TOKEN_EXPIRED, 'msg' => 'token expired', 'message' => 'token expired', 'data' => null]);
        }
        $this->lang = $language;
        $this->osm_user_info = $result;
        $this->getDI()->get('logger')->write_log('login-check-success' . json_encode($result), 'info');
    }
    
    
    /**
     * 处理默认值
     * @param $paramIn
     * @param $paramIn 数组|$parameter 参数名称 |$type 类型 1 字符串 2整型 3数组 4 布尔类型
     * @return string
     */
    public function processingDefault($paramIn = [], $parameter, $type = 1, $default = '')
    {
        switch ($type) {
            case 1:
                $default = !empty($default) ? $default : '';
                break;
            case 2:
                $default = !empty($default) ? $default : 0;
                break;
            case 3:
                $default = !empty($default) ? $default : [];
                break;
            case 4:
                $default = !empty($default) ? $default : true;
                break;
        }
        return isset($paramIn[$parameter]) && !empty($paramIn[$parameter]) ? $paramIn[$parameter] : $default;
    }
    
    /**
     * [getTranslation 获取语言对象]
     * @return [type] [返回一个对应语言对象]
     */
    protected function getTranslation()
    {
        if (empty($this->lang)) {
            $lang = 'th';
        } else {
            $lang = $this->lang;
        }
        //TODO 语言包字符处理 language:th-TH
        $lang = substr($lang, 0, 2);
        if (in_array($lang, [
            'zh',
            'zh-CN'
        ])) {
            $lang = 'zh-CN';
        }
        
        //客户端手机 非限定语言环境 默认泰语
        $lang_arr = array('th', 'en', 'zh-CN', 'vi', 'lo');
        if (!in_array($lang, $lang_arr))
            $lang = getCountryDefaultLang();
        // 返回一个语言包
        $path = APP_PATH . "/messages/" . $lang . ".php";
        require $path;
        
        // 返回一个翻译的对象
        return new NativeArray(["content" => $messages]);
    }
    
    /**
     * 参数校验 校验错误抛出
     * @Access  public
     * @Param   $paramIn 入参校验数组 | $validations 校验规则
     * @Return  array
     */
    public function validateCheck($paramIn = [], $validations = [])
    {
        try {
            Validation::validate($paramIn, $validations);
        } catch (\Exception $e) {
            
            $this->getDI()->get('logger')->write_log('validateCheck ' . json_encode([
                    'paramIn' => $paramIn,
                    'validations' => $validations,
                ], JSON_UNESCAPED_UNICODE), 'info');
            $this->jsonReturn($this->checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 基于方法的获取分布式原子锁
     *
     * 使用方法：
     * 在上层调用
     * $this->atomicLock(function () use () {}, someKey, 20)
     * 没有获取到锁 返回false
     * 获取到锁 执行方法体
     *
     * @param  $func
     * @param  string $key 全局唯一值 按业务定义
     * @param int $expire 默认十秒钟 按业务执行时间评估
     * @param bool $isDel
     * @return mixed 是否获取到锁
     * @throws Exception 匿名函数中遇到抛出异常
     */
    public function atomicLock($func, $key, $expire = 10, $isDel = true)
    {
        $redis = $this->getDI()->get("redisLib");
        $LUASCRIPT = <<<LUA
local key = KEYS[1]
local ttl = ARGV[1]
if (redis.call('setnx', key, 1) == 1) then
    return redis.call('expire', key, ttl)
elseif (redis.call('ttl', key) == -1) then
    return redis.call('expire', key, ttl)
end
    return 0
LUA;
        $isLock = $redis->eval($LUASCRIPT, [$key, $expire], 1);
        if ($isLock) {
            try {
                $result = $func();
            } catch (\Exception $e) {
                $redis->del($key);
                throw $e;
            }
            if ($isDel) {
                $redis->del($key);
            }
            return $result;
        }

        // 没有获得锁
        return false;
    }

    /**
     * 获取分页参数
     */
    public function getPaginationParams()
    {
        $page = $this->paramIn['page'] ?? 1;
        $size = $this->paramIn['page_size'] ?? 20;
        return [$page, $size];
    }}