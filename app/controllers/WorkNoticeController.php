<?php
/**
 * Author: Bruce
 * Date  : 2024-11-14 00:12
 * Description:
 */

namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use  FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\WorkNoticeServer;


class WorkNoticeController extends Controllers\ControllerBase
{
    
    public function listAction()
    {
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $workNoticeServer = Tools::reBuildCountryInstance(new WorkNoticeServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
        $returnArr = $workNoticeServer->list($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

}