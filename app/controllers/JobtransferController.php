<?php
namespace FlashExpress\bi\App\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Server\JobtransferServer;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Repository\JobtransferRepository;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\WorkflowServer;

class JobtransferController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;
    protected $shopCategory;
    protected $networkCategory;
    protected $hubCategory;
    protected $typeUnion;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 获取员工信息
     */
    public function getStaffInfoAction()
    {
        try {
            $paramIn        = $this->paramIn;
            $objJobtransfer = new JobtransferServer($this->lang, $this->timezone);
            $staffId        = $paramIn["staff_id"] ?? "";

            //获取登录人信息
            $data  = $objJobtransfer->getStaffInfo(["staff_id" => $staffId]);
            //获取部门列表
            $model = new FleSysDepartmentModel();
            $networkDeptIds = $model->getNetworkDepartmentIds();
            $shopDeptIds = $model->getShopDepartmentIds();
            $hubDeptIds = $model->getHubDepartmentIds();

            if (isset($data['department_id']) && in_array($data['department_id'], $networkDeptIds)) {
                $departmentIds = $networkDeptIds;
            } else if (isset($data['department_id']) && in_array($data['department_id'], $shopDeptIds)) {
                $departmentIds = $shopDeptIds;
            } else if (isset($data['department_id']) && in_array($data['department_id'], $hubDeptIds)) {
                $departmentIds = $hubDeptIds;
            } else {
                $departmentIds = [];
            }

            if (empty($departmentIds)) {
                throw new ValidationException('Please check department');
            }

            $departmentList = FleSysDepartmentModel::find([
                'conditions' => "id IN ({dept_ids:array})",
                'bind'       => [
                    'dept_ids' => $departmentIds
                ],
                'columns' => 'id, name'
            ])->toArray();

            //获取一阶段转岗详情
            $repo = new JobtransferRepository($this->timezone);
            $jobTsfStatus = $repo->getJobTransferAudit([
                'jt_staff_id' => $staffId
            ]);

            $departmenInfo = (new DepartmentRepository())->getDepartmentNameById($jobTsfStatus['department_id']);
            $storeInfo = $repo->getStoreInfo(['store_id' => $jobTsfStatus['store_id']]);
            $jobTitleInfo = $repo->getJobTitleName($jobTsfStatus['position_id']);

            $this->jsonReturn($this->checkReturn(["data" => [
                'staffInfo'       => $data,
                'department_list' => $departmentList,
                'car_owner'       => $objJobtransfer->getCarOwnerList(),
                'step_one'        => [
                    'store_id'  => $jobTsfStatus['store_id'] ?? '',
                    'store_name'  => $storeInfo['name'] ?? '',
                    'department_id'  => $jobTsfStatus['department_id'] ?? '',
                    'department_name'  => $departmenInfo ?? '',
                    'job_title'  => $jobTsfStatus['position_id'] ?? '',
                    'job_title_name'  => $jobTitleInfo['job_name'] ?? '',
                ]
            ]]));
        } catch (ValidationException $v) {
            $this->getDI()->get('logger')->write_log($v->getMessage(),'info');
            $this->jsonReturn($this->checkReturn(-3, $v->getMessage()));
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("getStaffInfoAction:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 转岗验证审批人是否满足条件
     * @throws \Exception
     */
    public function checkTransferOptimizationAction()
    {
        //[1]获取参数
        $paramIn = $this->paramIn;
        $curStaffId = $this->userinfo['id'] ?? '';
        $storeId = $this->userinfo['organization_type'] == 1 ? $this->userinfo['organization_id'] : -1;
        $objJobtransfer = new JobtransferServer($this->lang, $this->timezone);
        $jobTsfStatus = new JobtransferRepository($this->timezone);
        $sysList = new SysStoreServer($this->timezone);
        $staffId = $paramIn["staff_id"] ?? "";

        //是否与申请人属于同一网点或属于管辖范围
        //网点负责人：限制可为负责的网点内员工转岗
        //DM：限制可为负责的片区内员工转岗
        //AM：限制可为负责的大区内员工转岗
        //网点负责人 & hub supervisor & 片区经理
        //校验员工信息
        //不能给自己申请转岗
        //申请调岗员工需要在职
        try {
            //[2]校验
            $validations = [
                "staff_id" => "Required|Int|>>>:" . $this->getTranslation()->_('please_entry_staff_no'),
            ];
            $this->validateCheck($this->paramIn, $validations);
            //获取转岗员工信息
            $transferInfo = $objJobtransfer->getTransferInfo([
                "staff_id" => $staffId,
            ]);
            //网点获取片区和大区的id
            $storeRegionPieceId = $sysList->getStoreRegionPiece($transferInfo['sys_store_id']);

            //转岗人和审核人不能是相同的人，并且在职
            if ($staffId == $curStaffId || !$transferInfo || $transferInfo["state"] != enums::$service_status['incumbency']) {
                $this->logger->write_log("不在职", 'info');
                throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
            }

            //job_transfer表中拿到数据相关员工的转岗状态
            $jobTsfStatus = $jobTsfStatus->getJobTransferAuditOptimization([
                'jt_staff_id' => $staffId
            ]);

            //验证是否是为转岗
            if (isset($jobTsfStatus['approval_state']) && isset($jobTsfStatus['state']) && (
                    $jobTsfStatus['approval_state'] == enums::$audit_status['panding'] ||
                    $jobTsfStatus['approval_state'] == enums::$audit_status['approved'] && $jobTsfStatus['state'] == enums::$job_transfer_state['to_be_transfered']
                )
            ) {
                $this->logger->write_log("验证是否是为转岗", 'info');
                throw new \Exception($this->getTranslation()->_('jobtransfer_0027'), enums::$ERROR_CODE['1000']);
            }

            //获取审核人一级部门
            $reviewerStaffInfo = $objJobtransfer->getTransferInfo([
                'staff_id' => $curStaffId
            ]);
            //一级部门不一样的审核人和转岗人被pass
            if ($transferInfo['sys_department_id'] !== $reviewerStaffInfo['sys_department_id']) {
                $this->logger->write_log("一级部门不一样的审核人和转岗人被pass", 'info');
                throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
            }

            //校验转岗前网点 & 部门是否存在hrbp如果不存在，则不能申请
            $hrbp = (new WorkflowServer($this->lang, $this->timezone))->findHRBP($transferInfo['node_department_id'], ["store_id" => $transferInfo['sys_store_id']]);
            if (empty($hrbp)) {
                $this->logger->write_log("不存在hrbp", 'info');
                throw new \Exception($this->getTranslation()->_('jobtransfer_0039'), enums::$ERROR_CODE['1000']);
            }

            //获取片区经理的管理片区
            //获取大区经理的管理片区
            $pieces = $objJobtransfer->getManagerPieces($curStaffId);
            $regions = $objJobtransfer->getManagerRegions($curStaffId);

            if (in_array($reviewerStaffInfo['job_title'], [enums::$job_title['area_manager'], enums::$job_title['regional_manager']]) && isset($regions) && is_array($regions)) {

                if (!in_array($storeRegionPieceId['manage_region'], $regions)) {
                    //被转岗人与大区经理不在同一个大区
                    $this->logger->write_log("被转岗人与大区经理不在同一个大区", 'info');
                    throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
                } else {
                    $this->jsonReturn($this->checkReturn(['data' => true]));
                }
            }
            if ($reviewerStaffInfo['job_title'] == enums::$job_title['district_manager'] && isset($pieces) && is_array($pieces)) {
                if (!in_array($storeRegionPieceId['manage_piece'], $pieces)) {
                    //被转岗人与片区经理不在同一个片区、
                    $this->logger->write_log("被转岗人与片区经理不在同一个片区", 'info');
                    throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
                } else {
                    $this->jsonReturn($this->checkReturn(['data' => true]));
                }
            }

            //[网点负责人]被转岗人所在网点是申请人的下辖网点的
            $manageStoreIds = $sysList->checkOutletManager($curStaffId);
            if (!empty($manageStoreIds)){
                if (! in_array($transferInfo["sys_store_id"], $manageStoreIds)) {
                    $this->logger->write_log("被转岗人所在网点是申请人的下辖网点的", 'info');
                    throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
                } else {
                    $this->jsonReturn($this->checkReturn(['data' => true]));
                }
            }

            //Hub Supervisor职位：限制仅可为所属网点内员工转岗
            if ($reviewerStaffInfo['job_title'] == enums::$job_title['hub_supervisor'] &&
                $transferInfo["sys_store_id"] != $storeId) {

                throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
            }

            $this->jsonReturn($this->checkReturn(['data' => true]));
        } catch (\Exception $e) {
            if ($e->getCode() == '1000') {
                $this->getDI()->get('logger')->write_log("checkStaffInfoAction:异常信息:" . $e->getMessage(), 'info');
            } else {
                $this->getDI()->get('logger')->write_log("checkStaffInfoAction:异常信息:" . $e->getMessage());
            }
            $this->jsonReturn($this->checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 添加转岗信息
     * @Access  public
     * @Param   request
     * @Return  json
     */
    public function addJobtransferAction()
    {
        try {
            $paramIn             = $this->paramIn;
            $paramIn['userinfo'] = $this->userinfo ?? [];

            if (!$paramIn['userinfo']) {
                throw new \Exception($this->getTranslation()->_('jobtransfer_0004'),enums::$ERROR_CODE['1005']);
            }
            $objJobtransfer = new JobtransferServer($this->lang, $this->timezone);

            //可选转岗日期为当日后1天-90天内的任意日期
            $startDate = date('Y-m-d', time() + 1 * 86400);
            $endDate   = date('Y-m-d', time() + 90 * 86400);

            //校验参数
            $validations = [
                "staff_id"              => "Required|Int",
                "type"                  => "Required|Int",
                "after_department_id"   => "Required|Int",
                "after_store_id"        => "Required|Str",
                "after_position_id"     => "Required|Int",
                "after_role_ids"        => "Required",
                "hc_id"                 => "Required|Int",
                "after_date"            => "Required|DateFromTo:{$startDate},{$endDate}",
                "job_handover_staff_id" => "Required|Int",
                "reason"                => "Required|StrLenGeLe:10,500|>>>:" . $this->getTranslation()->_('jobtransfer_0001'),
                "after_working_day_rest_type" => "Required|Int",
            ];
            $this->validateCheck($paramIn, $validations);

            $reids_key  = 'lock_addJobtransfer_' . $this->userinfo['staff_id'].'_'.$paramIn['staff_id'];
            //[2]业务处理
            $returnArr = $this->atomicLock(function () use ($paramIn,$objJobtransfer) {
                return $objJobtransfer->addJobtransfer($paramIn);
            }, $reids_key, 10, false);

            if ($returnArr === false) { //没有获取到锁
                $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('5202')));
            }


            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            if ($e->getCode() == '1000') {
                $this->getDI()->get('logger')->write_log("AddJobtransferAction:异常信息:" . $e->getMessage(),'info');
            }elseif($e->getCode() == enums::$ERROR_CODE['1005']) {
                $this->getDI()->get('logger')->write_log("AddJobtransferAction:异常信息:" . $e->getMessage(),'notice');
            }else{
                $this->getDI()->get('logger')->write_log("AddJobtransferAction:异常信息:" . $e->getMessage());
            }
            $this->jsonReturn($this->checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 更新转岗
     * @param int       audit_id        审批ID
     * @param int       status          审批状态
     * @param string    reject_reason   驳回原因
     * @return string
     */
    public function updateJobtransferAction()
    {
        try {
            //[1]入参校验
            $paramIn                               = $this->paramIn;
            $paramIn['staff_id']                   = $this->userinfo['staff_id'];
            $paramIn['name']                       = $this->userinfo['name'];
            $paramIn['positions']                  = $this->userinfo['positions'];
            $paramIn['organization_id']            = $this->userinfo['organization_id'];
            $paramIn['organization_type']          = $this->userinfo['organization_type'];
            $paramIn['param']['position_category'] = $this->userinfo['positions'];
            $paramIn['param']['store_id']          = $this->userinfo['organization_id'];
            $paramIn['userinfo']                   = $this->userinfo;
            $logger                                = $this->getDI()->get('logger');

            $validations = [
                "audit_id"      => "Required|Int",
                "status"        => "Required|Int",
                "reject_reason" => "Required|StrLenGeLe:0,500",
            ];

            //获取转岗后职位
            if (!isset($paramIn['audit_id']) || empty($paramIn['audit_id'])) {
                $this->validateCheck($paramIn, $validations);
            }

            if ($paramIn['status'] == enums::APPROVAL_STATUS_CANCEL) {
                $returnArr = (new JobtransferServer($this->lang, $this->timezone))->cancelJobtransfer($paramIn['audit_id'], $paramIn['reject_reason'], $paramIn['staff_id']);
                $this->jsonReturn($returnArr);
            }

            //校验是否HRBP已经审批薪资
            $detailInfo = JobTransferModel::findFirst($paramIn['audit_id']);

            //校验是否HRBP已经审批薪资
            //是HRBP需要审批角色、薪资
            //非HRBP提交了薪资并且有待审批
            if (isset($paramIn['positions']) && in_array(68, $paramIn['positions']) ||
                isset($detailInfo->after_base_salary) && !empty($detailInfo->after_base_salary)) { //当前登录人为hrbp或已经提交了薪资
                $detailInfo = $detailInfo->toArray();

                $codeList = [
                    8 => 'base_salary',
                    9 => 'exp_allowance',
                    10=> 'position_allowance',
                    11=> 'car_rental',
                    12=> 'trip_payment',
                    13=> 'notebook_rental',
                    15=> 'food_allowance',
                    16=> 'dangerous_area',
                    17=> 'house_rental',
                ];

                $codeJobTransfer = [
                    //19=> 'role_ids',
                    //20=> 'upload_files',
                    21=> 'hc_expiration_date',
                    22=> 'car_owner',
                    23=> 'rental_car_cteated_at',
                ];
                if (isset($paramIn['extend']) && is_array($paramIn['extend']) && $paramIn['extend']) {
                    foreach ($codeList as $key => $value) {
                        if (isset($paramIn['extend']['code' . $key]) && $paramIn['extend']['code' . $key]) {
                            $paramIn[$value] = $paramIn['extend']['code' . $key]['value'];
                        }
                    }

                    //上传图片
                    //角色
                    //hc过期日期
                    if (isset($paramIn['status']) && $paramIn['status'] == enums::APPROVAL_STATUS_APPROVAL) {
                        foreach ($codeJobTransfer as $key => $value) {
                            if (isset($paramIn['extend']['code' . $key]) && $paramIn['extend']['code' . $key]) {
                                $paramIn[$value] = $paramIn['extend']['code' . $key];
                            }
                        }
                    }
                }

                //如果HRBP已经审批薪资
                //则检验薪资信息
                switch ($detailInfo['after_position_id']) {
                    case enums::$job_title['van_courier']:
                    case enums::$job_title['bike_courier']:
                        $validations = array_merge($validations, [
                            'car_rental'     => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'dangerous_area' => 'IfIntEq:status,2|IntGeLe:0,99999',
                        ]);
                        break;
                    case enums::$job_title['dc_officer']:
                    case enums::$job_title['assistant_branch_supervisor']:
                    case enums::$job_title['shop_officer']:
                    case enums::$job_title['shop_cashier']:
                    case enums::$job_title['hub_staff']:
                    case enums::$job_title['warehouse_staff']:
                    case enums::$job_title['warehouse_staff_sorter']:
                    case enums::$job_title['mini_cs_officer']:
                    case enums::$job_title['store_officer']:
                        $validations = array_merge($validations, [
                            'exp_allowance'   => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'notebook_rental' => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'dangerous_area'  => 'IfIntEq:status,2|IntGeLe:0,99999',
                        ]);
                        break;
                    case enums::$job_title['branch_supervisor']:
                    case enums::$job_title['shop_supervisor']:
                    case enums::$job_title['store_supervisor']:
                    case enums::$job_title['hub_supervisor']:
                        $validations = array_merge($validations, [
                            'position_allowance'  => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'notebook_rental'     => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'dangerous_area'      => 'IfIntEq:status,2|IntGeLe:0,99999',
                        ]);
                        break;
                    default:
                        $validations = array_merge($validations, [
                            'position_allowance'  => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'notebook_rental'     => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'house_rental'        => 'IfIntEq:status,2|IntGeLe:0,99999',
                        ]);
                        break;
                }
                $validations = array_merge($validations, [
                    'base_salary'    => 'IfIntEq:status,2|IntGeLe:1,99999|>>>:' . $this->getTranslation()->_('job_transfer_err'),
                    'food_allowance' => 'IfIntEq:status,2|IntGeLe:0,99999',
                ]);
                //如果是hrbp校验角色、车辆所属
                if (isset($paramIn['positions']) && in_array(68, $paramIn['positions']) &&
                    isset($paramIn['status']) && $paramIn['status'] == enums::APPROVAL_STATUS_APPROVAL
                ) {
                    $vehicleJobTitle = explode(',',(new SettingEnvServer())->getSetVal('job_title_vehicle_type'))?:JobTransferEnums::TRANSFER_DEFAULT_JOB_TITLE;
                    if (in_array($detailInfo['after_position_id'],$vehicleJobTitle)) {
                        $validations = array_merge($validations, [
                            'car_owner'  => 'Required|IntIn:1,2,3',//1：个人车辆、2：公司车辆、3：借用车辆
                            'rental_car_cteated_at'  => 'IfIntEq:car_owner,2|Required|Date|>>>:'.$this->getTranslation()->_('vehicle_info_0009'),//用车时间
                        ]);
                    }

                    $validations = array_merge($validations, [
                        'role_ids'    => 'Required|ArrLenGe:1|>>>:' . $this->getTranslation()->_('jobtransfer_0034'),
                        'upload_files' => 'Required|ArrLenGe:1|>>>:' . $this->getTranslation()->_('2106'),
                    ]);

                    $validations = array_merge($validations, [
                        "after_working_day_rest_type" => "Required|Int",
                    ]);
                }
            }
            $this->validateCheck($paramIn, $validations);

            //[2]业务处理

            $returnArr = (new JobtransferServer($this->lang, $this->timezone))->updateJobtransfer($paramIn);
        } catch (\Exception $e) {
            if ($e->getCode() == '1000') {
                $logger->write_log("updateJobtransferAction:异常信息:" . $e->getMessage() . $e->getTraceAsString() ,'info');
            }else{
                $logger->write_log("updateJobtransferAction:异常信息:" . $e->getMessage() . $e->getTraceAsString(), 'notice');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 转岗-部门下拉
     */
    public function getDepartmentListAction()
    {
        try {
            $paramIn        = $this->paramIn;
            $objJobtransfer = new JobtransferServer($this->lang, $this->timezone);

            //校验参数
            $validations = [
                "staff_id"      => "Required|Int|>>>:". $this->getTranslation()->_('please_entry_staff_no'),
            ];
            $this->validateCheck($paramIn, $validations);
            //获取部门列表
            $data = $objJobtransfer->getDepartmentList($paramIn);

            if (empty($data)) {
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no department')));
            }
            $this->jsonReturn($this->checkReturn($data));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getDepartmentListAction:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 转岗获取hc下拉列表
     * @Access  public
     * @Param   request
     * @Return  json
     */
    public function getHcListAction()
    {
        try {
            $paramIn        = $this->paramIn;
            $objJobtransfer = new JobtransferServer($this->lang, $this->timezone);

            //校验参数
            $validations = [
                "department_id" => "Required|Int",
                "store_id"      => "Required|Str",
                "job_title_id"  => "Required|Int",
            ];
            $this->validateCheck($paramIn, $validations);
            //获取hc列表
            $data = $objJobtransfer->getHcList($paramIn);

            if (empty($data)) {
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('jobtransfer_0024')));
            }

            $this->jsonReturn(self::checkReturn($data));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getHcListAction:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 转岗获取职位下拉列表
     * @Access  public
     * @Param   request
     * @Return  json
     */
    public function getPositionListAction()
    {
        try {
            //[1]传入参数
            $paramIn        = $this->paramIn;
            $objJobtransfer = new JobtransferServer($this->lang, $this->timezone);

            //[2]校验参数
            $validations = [
                "department_id" => "Required|Int",
            ];
            if (isCountry('MY')) {
                $validations['staff_id'] = 'Required|IntGe:1|>>>:staff_id param error';
            }
            $this->validateCheck($paramIn, $validations);

            $data  = $objJobtransfer->getPositionList($paramIn);
            $this->jsonReturn(self::checkReturn($data));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getPositionListAction:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 转岗获取网点下拉列表
     * @Access  public
     * @Param   request
     * @Return  json
     */
    public function getStoreListAction()
    {
        try {
            //[1]传入参数
            $paramIn        = $this->paramIn;
            $objJobtransfer = new JobtransferServer($this->lang, $this->timezone);
            $param          = [];

            //[2]校验参数
            $validations = [
                "department_id" => "Required|Int",
            ];
            $this->validateCheck($paramIn, $validations);

            $departmentId   = $paramIn["department_id"] ?? "";

            //获取部门列表
            $model = new FleSysDepartmentModel();
            $networkDeptIds = $model->getNetworkDepartmentIds();
            $shopDeptIds = $model->getShopDepartmentIds();
            $hubDeptIds = $model->getHubDepartmentIds();

            if (in_array($departmentId, $networkDeptIds)) {
                //Network Management
                $param = [
                    "category" => $this->networkCategory,
                ];
            } elseif (in_array($departmentId, $shopDeptIds)) {
                //Shop Project
                $param = [
                    "category" => $this->shopCategory,
                ];
            } elseif (in_array($departmentId, $hubDeptIds)) {
                //Hub
                $param = [
                    "category" => $this->hubCategory,
                ];
            }
            //获取网点下拉列表
            $data = $objJobtransfer->getStoreList($param);
            $this->jsonReturn(self::checkReturn(["data" => $data]));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getStoreListAction:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 转岗获取详情
     * @Access  public
     * @Param   request
     * @Return  json
     */
    public function getJobtransferInfoAction()
    {
        try {
            $paramIn = $this->paramIn;
            //校验参数
            $validations = [
                "id" => "Required|Int",
            ];
            $this->validateCheck($paramIn, $validations);

            $objJobtransfer = new JobtransferServer($this->lang, $this->timezone);
            $data           = $objJobtransfer->getJobtransferInfo($paramIn);
            $this->jsonReturn(self::checkReturn(["data" => $data]));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getIsPayAdjustmentType:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取转岗人的角色
     */
    public function getRoleListAction()
    {
        try {
            $paramIn = $this->paramIn;
            //校验参数
            $validations = [
                "department_id" => "Required|Int",
                "job_title_id"  => "Required|Int",
            ];
            $this->validateCheck($paramIn, $validations);

            $ac = new ApiClient('hr_rpc', '', 'department_job_title_role', $this->lang);
            $ac->setParams(
                [
                    "department_id" => $paramIn["department_id"],
                    "job_title_id"  => $paramIn["job_title_id"],
                ]
            );
            $return = $ac->execute();
            $data   = $return["result"] ?? [];
            $error  = $return["error"]["message"] ?? "";
            if ($error) {
                $this->jsonReturn($this->checkReturn(-3, $error));
            } else {
                $this->jsonReturn(self::checkReturn(["data" => ["dataList" => $data]]));
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getRoleList:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取车辆归属下拉
     */
    public function getCarOwnerListAction()
    {
        try {
            $paramIn = $this->paramIn;

            $result = (new JobtransferServer($this->lang, $this->timezone))->getCarOwnerList($paramIn);

            $this->jsonReturn($this->checkReturn(['data' => [
                'dataList' => $result
            ]]));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getRoleList:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }
}