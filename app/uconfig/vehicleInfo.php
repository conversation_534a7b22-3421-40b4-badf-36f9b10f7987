<?php

/**
 * 可以查看车辆的品牌和型号
 */
$returnDataV2 = [
    'vehicle_brand' => [
        [
            'value' => '1',
            'label' => 'Toyota',
            'data'  => [
                ['value' => '1', 'label' => 'HILUX - Tiger'],
                ['value' => '2', 'label' => 'HILUX - VIGO'],
                ['value' => '3', 'label' => 'Revo Standard Cab'],
                ['value' => '100', 'label' => 'Other'],
            ],
        ],
        [
            'value' => '2',
            'label' => 'Isuzu',
            'data'  => [
                ['value' => '1', 'label' => 'D-MAX -SPARK'],
                ['value' => '2', 'label' => 'D-MAX -STEALTH'],
                ['value' => '3', 'label' => 'D-MAX -SPACE CAB'],
                ['value' => '4', 'label' => 'NLR 130'],
                ['value' => '5', 'label' => 'ELF euro2 130'],
                ['value' => '6', 'label' => 'FRR 210/190'],
                ['value' => '7', 'label' => 'NQR 175'],
                ['value' => '8', 'label' => 'NPR 150'],
                ['value' => '9', 'label' => 'NMR 130'],
                ['value' => '10', 'label' => 'TFR/240'],
                ['value' => '11', 'label' => 'DECA'],
                ['value' => '100', 'label' => 'Other'],
            ],
        ],
        [
            'value' => '3',
            'label' => 'Nissan',
            'data'  => [
                ['value' => '1', 'label' => 'NAVARA - King CAB'],
                ['value' => '2', 'label' => 'NAVARA - Single CAB'],
                ['value' => '3', 'label' => 'FRONTIER'],
                ['value' => '100', 'label' => 'Other'],
            ],
        ],
        [
            'value' => '4',
            'label' => 'Misubishi',
            'data'  => [
                ['value' => '1', 'label' => 'TITAN - Single CAB'],
                ['value' => '2', 'label' => 'TITAN - MEGA CAB'],
                ['value' => '100', 'label' => 'Other'],
            ],
        ],
        [
            'value' => '5',
            'label' => 'Suzuki',
            'data'  => [
                ['value' => '1', 'label' => 'CARRY'],
                ['value' => '100', 'label' => 'Other'],
            ],
        ],
        [
            'value' => '6',
            'label' => 'Hino',
            'data'  => [
                ['value' => '1', 'label' => 'SIRI 500FC9J'],
                ['value' => '100', 'label' => 'Other'],
            ],
        ],
        [
            'value' => '7',
            'label' => 'Tata',
            'data'  => [
                ['value' => '1', 'label' => 'TATA ULTRA 10140'],
                ['value' => '100', 'label' => 'Other'],
            ],
        ],
        [
            'value' => '9',
            'label' => 'FORD',
            'data'  => [
                ['value' => '1', 'label' => 'Standard Cab XL 4x2 LR 6MT'],
            ],
        ],
        [
            'value' => '10',
            'label' => 'SGMW',
            'data'  => [
                ['value' => '1', 'label' => 'NEOMOR-D01'],
                ['value' => '100', 'label' => 'Other'],
            ],
        ],
        [
            'value' => '8',
            'label' => 'Other',
            'data'  => [
                ['value' => '100', 'label' => 'Other'],
            ],
        ],

    ],
    'vehicle_size' => [
        [ 'value' => '1', 'label'=> '4W'],
        [ 'value' => '2', 'label'=> '4WJ'],
        [ 'value' => '3', 'label'=> '6W5.5'],
        [ 'value' => '4', 'label'=> '6W6.5'],
        [ 'value' => '5', 'label'=> '6W7.5'],
        [ 'value' => '6', 'label'=> '10W'],
        [ 'value' => '7', 'label'=> 'Other'],
    ],
    'oil_type' => [
        '1'  => 'DIESEL B7',
        '2'  => 'DIESEL B10',
        '3'  => 'Gas NGV',
        '4'  => 'Gas LPG',
    ],
    'oil_company' => [
        [ 'value' => '1', 'label'=> 'Shell'],
        [ 'value' => '2', 'label'=> 'PTT'],
        [ 'value' => '3', 'label'=> 'PT'],
    ],
    'branch_mileage' => [
        'TH47020201'  => 200000,
        'TH59060104'  => 200000,
        'TH13050401'  => 200000,
        'TH71110201'  => 200000,
    ],
];
return $returnDataV2;
