<?php

use FlashExpress\bi\App\library\enums;

/**
 * 外协员工职位
 * @Access  public
 * @Param   request
 * @Return  array
 */
$returnData = [
    'osData'                  => [
        '3'  => '网点经理',
        '18' => '网点主管',
    ],
    'osLongPeriodRequestRole' => [
        '3'  => '网点经理',
        '21' => '区域经理',
    ],
    'approvalId'              => [
        '1' => env('os_test_staff_1', '17469'),
        '2' => env('os_test_staff_2', '30989'),
        '3' => env('os_test_staff_3', '19953'),
    ],
    'jobTitles'               => [
        enums::$job_title['van_courier']            => 'Van Courier',
        enums::$job_title['car_courier']            => 'Car Courier',
        enums::$job_title['bike_courier']           => 'Bike Courier',
        enums::$job_title['boat_courier']           => 'Boat Courier',
        enums::$job_title['warehouse_staff_sorter'] => 'Warehouse Staff (Sorter)',
        enums::$job_title['hub_staff']              => 'Hub Staff',
        enums::$job_title['onsite_officer']         => 'Onsite Officer',
        enums::$job_title['shop_officer']           => 'Shop Officer',
        enums::$job_title['tricycle_courier']       => 'Tricycle Courier',
        enums::$job_title['hub_operator']           => 'Hub Operator',
        enums::$job_title['truck_courier']          => 'Truck Driver',
        enums::$job_title['outsource']              => 'Outsource',
        enums::$job_title['security_outsource']     => 'Security Outsource',
    ],
    'jobTitlesNot'            => [
        '110'  => 'Van Courier',
        '13'   => 'Bike Courier',
        '452'  => 'Boat Courier',
        '1000' => 'Tricycle Courier',
        '1199' => 'Car Courier',
    ],
    'osMotorcadeRequestRole'  => [
        '31' => '角色为线路中控人员',
        '32' => '线路规划管理员',
    ],
    'hubOsRoles' => [
        enums::$roles['DISTRIBUTION_MANAGER']
    ]
];

return $returnData;
