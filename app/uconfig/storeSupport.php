<?php

use FlashExpress\bi\App\library\enums;

/**
 * 网点支援
 * @Access  public
 * @Param   request
 * @Return  array
 */
$returnData = [
    'jobTitles' => [
        enums::$job_title['van_courier'] => 'Van Courier',
        enums::$job_title['tricycle_courier'] => 'Tricycle Courier',
        enums::$job_title['bike_courier'] => 'Bike Courier',
        enums::$job_title['branch_supervisor'] => 'Branch Supervisor',
        enums::$job_title['car_courier'] => 'Car Courier',
        enums::$job_title['dc_officer'] => 'DC Officer',
    ],
    'jobTitles_PH' => [
        enums::$job_title['van_courier'] => 'Van Courier',
        enums::$job_title['tricycle_courier'] => 'Tricycle Courier',
        enums::$job_title['bike_courier'] => 'Bike Courier',
        enums::$job_title['branch_supervisor'] => 'DC Supervisor',
        enums::$job_title['car_courier'] => 'Car Courier',
        enums::$job_title['dc_officer'] => 'DC Officer',
    ],
];

return $returnData;
