<?php


namespace FlashExpress\bi\App\Modules\Vn\Controllers;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\Vn\Server\AttendanceCalendarServer;
use FlashExpress\bi\App\Modules\Vn\Server\AuditServer;
use FlashExpress\bi\App\Modules\Vn\Server\LeaveServer;
use FlashExpress\bi\App\Repository\StaffRepository;
use Exception;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Repository\BySettingRepository;

class AuditController extends BaseController
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->server  = ['audit' => new AuditServer($this->lang, $this->timezone)];
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
        $this->url_log($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();

    }

    /**
     * 获取审批列表权限
     * @param int staff_id 登录用户
     * @return Json
     */
    public function getAuditlistPermissionAction()
    {

        //[1]入参校验
        $paramIn                      = $this->paramIn;
        $headerData       = $this->request->getHeaders();
        (isset($headerData['X-Demonstration-App']) || $this->request->get('x-demonstration-app')) && $paramIn['is_demonstration_app'] = 1;
        $paramIn['staff_id']          = $this->userinfo['staff_id'];
        $paramIn['positions']         = $this->userinfo['positions'];
        $paramIn['job_title']         = $this->userinfo['job_title'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['department_id']     = $this->userinfo['department_id'];

        $validations = [
            "staff_id" => "Required|Int"
        ];
        $this->validateCheck($paramIn, $validations);

        $this->getDI()->get('logger')->write_log('getAuditlistPermission ' .json_encode($paramIn,JSON_UNESCAPED_UNICODE), 'info');

        //[2]业务处理
        $audit_server = (new AuditServer($this->lang, $this->timezone));
        $staffInfo              = (new StaffRepository($this->lang))->getStaffPosition($paramIn['staff_id']);
        $staffInfo['positions'] = $this->userinfo['positions'];
        $audit_server->setStaffInfo($staffInfo);
        $returnArr = $audit_server->getListPermissionFromCache($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }



    /**
     * 请假添加
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function leaveAddAction()
    {
        try {
            //[1]入参校验
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $validations         = [
                "staff_id"         => "Required|Int",
                "leave_type"       => "Required|Int",
                "leave_start_time" => "Required|Date|>>>:" . $this->getTranslation()->_('1023'),
                'leave_start_type' => "Required|IntIn:1,2",
                'leave_end_type'   => "Required|IntIn:1,2",
                "leave_end_time"   => "Required|Date|>>>:" . $this->getTranslation()->_('1024'),
                "audit_reason"     => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1019')
            ];
            $this->validateCheck($paramIn, $validations);

            (new BackyardServer($this->lang, $this->timezone))->leaveCheckMsg($paramIn['staff_id']);

            //年假 例子
            if(in_array($paramIn['leave_type'],[enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_19, enums::LEAVE_TYPE_4])){
                //暂时关闭年假申请 1 -关闭 不让申请 0 可以申请
                $annualSwitch = (new BySettingRepository($this->lang))->get_setting('annual_switch');
                if(!empty($annualSwitch) && $annualSwitch == 1){
                    throw new ValidationException($this->getTranslation()->_('annual_forbidden'));
                }
                $returnArr = (new LeaveServer($this->lang, $this->timezone))->setLockConf(60,true)->saveVacationUseLock($paramIn);
            }else{
                $returnArr =  (new AuditServer($this->lang, $this->timezone))->setLockConf(60,true)->leaveAddUseLock($paramIn);
            }

            $this->jsonReturn($returnArr);
        } catch (ValidationException $e) {
            //请假时间跟班次判断二次确认弹窗
            if ($e->getCode() == 10086) {
                return $this->returnJson(1, $e->getMessage(), ['code' => -1,'message' => $e->getMessage(),'data' => ['param' => 'is_submit']]);//二次确认
            }
            throw $e;
        }

    }

//    /**
//     * 获取 补卡/请假类型 只用在 补卡 type 1类型
//     * @Access  public
//     * @Param   request
//     * @Return  jsonData
//     */
//    public function getTypeBookAction()
//    {
//        try{
//            //[1]入参 参数校验
//            $paramIn     = $this->paramIn;
//            $validations = [
//                "type" => "Required|Int"
//            ];
//            $this->validateCheck($paramIn, $validations);
//            $paramIn['user_info'] = $this->userinfo;
//
//            //[2]业务处理
//            $returnArr = (new AuditServer($this->lang, $this->timezone))->getTypeBook($paramIn);
//
//            //[3]数据返回
//            $this->jsonReturn($returnArr);
//        }catch (\Exception $e){
//            $this->getDI()->get('logger')->write_log('audit getTypeBook' . $e->getMessage(), 'error');
//            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
//        }
//    }



    /**
     * 换算时间
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function conversionTimeAction()
    {
        try{
            //[1]入参校验
            $paramIn     = $this->paramIn;
            $validations = [
                "leave_start_time" => "Required|Date",
                'leave_start_type' => "Required|IntIn:1,2",
                'leave_end_type'   => "Required|IntIn:1,2",
                "leave_end_time"   => "Required|Date",
            ];
            $this->validateCheck($paramIn, $validations);
            $paramIn['staff_id'] = $this->userinfo['id'];

            //[2]业务处理
            $returnArr = (new AuditServer($this->lang, $this->timezone))->conversionTime($paramIn);

            //[3]数据返回
            $this->jsonReturn($returnArr);
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('audit conversionTime' . $e->getMessage(), 'error');
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }



    //请假类型 产假 对应模板 返回 对应 定制下拉菜单
    public function get_templateAction(){
        $paramIn   = $this->paramIn;

        if(empty($paramIn['template_type']))
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));

        $server = new AuditServer($this->lang,$this->timezone);
        $return = $server->get_template(intval($paramIn['template_type']));

        $this->jsonReturn(self::checkReturn(array('data' => $return)));
    }


}