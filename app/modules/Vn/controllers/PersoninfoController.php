<?php
namespace FlashExpress\bi\App\Modules\Vn\Controllers;

use Exception;
use FlashExpress\bi\App\Modules\Vn\Server\PersoninfoServer;
use FlashExpress\bi\App\Controllers\PersoninfoController as BasePersonInfoController;


class PersoninfoController extends BasePersonInfoController
{
    /**
     * @description:修改个人信息
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/9/1 20:20
     */
    public function  updateStaffInfoAction(){
        //[1]入参
        $paramIn = [];
        if(isset($this->paramIn['tax_card'])){
            $paramIn['tax_card'] = $this->paramIn['tax_card'];
        }
        if(isset($this->paramIn['bank_branch_name'])){
            $paramIn['bank_branch_name'] = $this->paramIn['bank_branch_name'];
        }
        if(isset($this->paramIn['bank_account'])) {
            $paramIn['bank_account'] = $this->paramIn['bank_account'];
        }
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations = [
            'tax_card'=>'StrLenLe:100|>>>:'.'tax card fill in unqualified !',
            //'bank_branch_name'=>'StrLenLe:100|>>>:'.'bank branch name fill in unqualified !',
            'bank_account' => 'StrLenLe:100|>>>:bank_no_name fill in unqualified !',
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr =  (new PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->updatePersonInfobymobileUseLock($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
}
