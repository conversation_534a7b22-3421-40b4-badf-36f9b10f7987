<?php
namespace FlashExpress\bi\App\Modules\Vn\Controllers;

use Exception;
use FlashExpress\bi\App\Modules\Vn\Server\WmsPlanServer;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\ProbationServer;
use FlashExpress\bi\App\Server\ResumeServer;
use FlashExpress\bi\App\Server\TicketServer;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\ResumeRecommendRepository;
use FlashExpress\bi\App\Controllers\MenuController as BaseMenuController;

class MenuController extends BaseMenuController
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        //会带个_url参数
        unset($this->paramIn['_url']);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    /**
     * by index
     */
    public function indexAction()
    {

        //[1]入参校验
        $paramIn             = $this->paramIn;
        $headerData       = $this->request->getHeaders();
        (isset($headerData['X-Demonstration-App']) || $this->request->get('x-demonstration-app')) && $paramIn['is_demonstration_app'] = 1;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] = $this->userinfo;
        $validations         = [
            "staff_id" => "Required|Int"
        ];
        $this->validateCheck($paramIn, $validations);

        $this->getDI()->get('logger')->write_log('menu index ' .json_encode($paramIn,JSON_UNESCAPED_UNICODE), 'info');

        //库存盘点数量
        $plan_wms_task_count =  (new WmsPlanServer($this->lang,$this->timezone))->setExpire(300)->planWmsTaskCountMsgFromCache($this->userinfo['staff_id']);
        //[2]业务处理
        $server = new BackyardServer($this->lang, $this->timezone);
        $unAuditNumRes = $server->getWaitAuditData($paramIn);
        if(isset($paramIn['is_demonstration_app'])){
            $unAuditNumRes['probation_data'] = ['is_show'=>'0','num'=>'0'] ;
            $unAuditNumRes['ticket_data'] = ['is_it'=>0,'is_dm'=>0,'reply_num'=>0,'audit_num'=>0] ;
            $unAuditNumRes['interview_data'] = ['num'=>0];
            $unAuditNumRes['resume_filter_data'] = ['is_show'=>0,'num'=>0];

            $num = $unAuditNumRes['wait_audit_data']['num'] ?? 0;
            $rsNum = $unAuditNumRes['wait_audit_data']['rs_num'] ?? 0;
            $unAuditNumRes['un_audit_num'] = $num + $rsNum;
        }
        $return = [
            //总和
            'total_audit_num'         => $unAuditNumRes['un_audit_num'],
            'ProbationGetNum'         => $unAuditNumRes['probation_data'],
            'waitAuditNum'            => $unAuditNumRes['wait_audit_data'],
            'TicketGetNum'            => $unAuditNumRes['ticket_data'],
            'interview_count'         => $unAuditNumRes['interview_data'],
            'resume_filter'           => $unAuditNumRes['resume_filter_data'],
            'resumeRecommendNum'      => $unAuditNumRes['commitment_data'],
            'InventoryCheckTaskCount' => ['num' => $unAuditNumRes['inventory_check_task_count']],
            'planWmsTaskCount'        => ['num' => $plan_wms_task_count ?? 0],
            'kpiCount'                => ['num' => $unAuditNumRes['kpi_count']],//kpi目标
            'oaFolderNum'             => $unAuditNumRes['oa_folder_num'] ?? 0,
            'packageAllotNum'         => ['num' => $unAuditNumRes['package_allot_num']],
            'reimbursementApplyNum'   => $unAuditNumRes['reimbursement_apply_num'] ?? 0,
        ];
        $this->getDI()->get('logger')->write_log("menu_index_{$this->userinfo['staff_id']}: ".json_encode($return,JSON_UNESCAPED_UNICODE),'info');
        return $this->jsonReturn(self::checkReturn(['data' =>$return]));
    }


}
