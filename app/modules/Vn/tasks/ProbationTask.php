<?php
namespace FlashExpress\bi\App\Modules\Vn\Tasks;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Server\WorkflowServer;
use Phalcon\Db;
use FlashExpress\bi\App\Server\ProbationServer;
use Exception;
use ProbationTask as BaseProbationTask;


class ProbationTask extends  BaseProbationTask
{

    /**
     * 第一阶段评估插入
     * 每天执行，
     * @param array $params
     */

    public function firstAction(array $params)
    {
        $start = "";
        if (isset($params[0])) {
            $start = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }

        $bll = new ProbationServer($this->lang, $this->add_hour);
        if (empty($start)) {
            $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        }
        $evaluate_day    = $bll->evaluate_day;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day[1]);                          //第一阶段
        $hire_date_end   = $this->getDateByDays($start, $evaluate_day[1] - 1);
        $staffs          = $bll->getStaffs($hire_date_begin, $hire_date_end, 0, 0, $bll->job_grade_exceed,true); // 查询 14 级及以下的人
//        $this->myLogger("查询第一阶段 {$bll->job_grade_exceed} 级以下的数据" . $hire_date_begin . '===' . $hire_date_end . ' '. implode(',',array_column($staffs,'staff_info_id')));
        //查询 17 级以上的数据
        $evaluate_day_exceed = $bll->evaluate_day_exceed;
        $hire_date_begin     = $this->getDateByDays($start, $evaluate_day_exceed[1]);
        $hire_date_end   = $this->getDateByDays($start, $evaluate_day_exceed[1] - 1);
        $staffs_two          = $bll->getStaffs($hire_date_begin, $hire_date_end, 0, $bll->job_grade_exceed,'',true);    //查询 14 级以上的人
        $staffs              = array_merge($staffs, $staffs_two);                                               //合并两个数据组
//        $this->myLogger("查询第一阶段 {$bll->job_grade_exceed} 级以上的数据" . $hire_date_begin . '===' . $hire_date_end . ' '. implode(',',array_column($staffs_two,'staff_info_id')));

        if (empty($staffs)) {
            $this->myLogger('第一阶段没有找到符合的数据');
            return false;
        }

        $db = $this->getDI()->get('db');
        $staffIds = array_column($staffs,'staff_info_id');
        $existStaffIds = $bll->getExistHrProbationByStaffIds($staffIds);
        foreach ($staffs as $staff) {
            if (!empty($staff['status']) && $staff['status'] == $bll::STATUS_FORMAL){
                continue;
            }
            if (
                !empty($staff['cur_level']) &&
                $staff['cur_level'] == $bll::CUR_LEVEL_FIRST &&
                !empty($staff['first_audit_status']) &&
                $staff['first_audit_status'] != HrProbationModel::FIRST_AUDIT_STATUS_WAIT
            ){
                // 已执行过的数据
                continue;
            }
            
            if (empty($staff['manager_id'])) {
                $this->myLogger('staff= ' . $staff['staff_info_id'] . ' manager_id is null 第一阶段评估没有找到上级','error');
                continue;
            }
            if (in_array($staff['staff_info_id'],$existStaffIds)) {
                $this->myLogger('staff  ' . $staff['staff_info_id'] . ' 已经存在');
                continue;
            }
            $this->myLogger("firstAction 第一阶段评估插入 需要执行的数据 staff= " . $staff['staff_info_id']);

            try {
                $db->begin();
                //判断此人什么等级
                $formal_days = (int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ? $bll->formal_days : $bll->formal_days_exceed;
                $formal_at   = $this->getDateByDays($staff['hire_date'], $formal_days, 1);
                //每阶段评估时间
                $evaluate_time = (int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ? $bll->duration_day : $bll->duration_day_exceed;

                $res = $db->insertAsDict("hr_probation", ['staff_info_id' => $staff['staff_info_id'],'first_audit_status' => HrProbationModel::FIRST_AUDIT_STATUS_RUN, 'created_at' => $start, 'formal_at' => $formal_at]);
                if (!$res) {
                    throw new Exception('hr_probation insert fail');
                }
                $probation_id = $db->lastInsertId();
                $res          = $db->insertAsDict('hr_probation_audit', [
                    'probation_id'  => $probation_id,
                    'staff_info_id' => $staff['staff_info_id'],
                    'audit_id'      => $staff['manager_id'],
                    'tpl_id'        => $bll->getTplIdByJobTitleGradeV2($staff['job_title_grade_v2']),
                    'created_at'    => gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600),
                    //第一次上级评审截止3天以后
                    'deadline_at'   => $this->getDateByDays($start, $evaluate_time['1']['1'] ?? 3, 1),

                    'updated_at' => gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600),
                    'version'=>$bll->version,
                    'show_time' => date("Y-m-d H:i:s"),
                ]);
                if (!$res) {
                    throw new Exception('hr_probation_audit insert fail');
                }
                $db->commit();
                //发送push
                $bll->push_notice_higher($staff['manager_id'], $staff['staff_info_id']);
            } catch (\Exception $e) {
                $db->rollback();
                //实习只进入一直，大概就是有重复进入的报错。改成info
                $this->myLogger('staff=' . $staff['staff_info_id'] . ' first insert fail,message=' . $e->getMessage(), 'info');
            }
        }

        $this->myLogger('第一阶段评估执行完毕======end');

    }




    /**
     * 13362 发送审核已通过和未通过的员工 和 他们的上级与hrbp，通过和未通过是不同的消息
     * 取 3 天后转正 的人, 越南 入职61天转正日期，58天开始发送消息
     * 每天执行
     */
    public function send_msg_to_staffAction($params=[])
    {
        $bll = new ProbationServer($this->lang, $this->add_hour);

        //今天
        $start = gmdate("Y-m-d", time() + ( $this->add_hour)*3600);
	    $start = $end = $this->getDateByDays($start, 3, 1);//3 天后转正人的
        if (isset($params[0])) {
            $start = $end = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }

        $staffs = $bll->getStaffsByFormalDate($start, $end, [ProbationServer::STATUS_PASS, ProbationServer::STATUS_NOT_PASS, ProbationServer::STATUS_FORMAL]); // 已通过 未通过
        if (empty($staffs)) {
            $this->myLogger("send msg to staff:no probation staffs on formal_at between" . $start . "===" . $end . "=====end");
            return;
        }
        $WorkflowServer = (new WorkflowServer($this->lang, $this->timezone));

        foreach ($staffs as $staff) {
            /*$html = <<<EOF
    <p>เรียน  {$staff['name']}</p>
    <p>เรื่อง แจ้งผลการทดลองงาน</p><br/>
    <p style='text-indent:2em'>ตามที่บริษัทฯได้รับท่านเข้าปฏิบัติงานทดลองงานนั้น บริษัทฯ ขอแสดงความยินดีที่ท่าน มีผลการปฏิบัติงานเป็นที่น่าพอใจ และผ่านการทดลองงาน มีผลตั้งแต่วันที่{$staff['formal_at']}เป็นต้นไป  ทั้งนี้ท่านสามารถใช้สิทธิการเบิกสวัสดิการพนักงานและสิทธิประโยชน์อื่น ๆ ตามที่บริษัทกำหนด  โดยท่านสามารถขอทราบรายละเอียดเพิ่มเติมได้ที่ฝ่าย HR</p>
    <p style='text-indent:2em'>จึงเรียนมาเพื่อโปรดทราบ</p><br/>
    <p style="text-indent: 18em">ขอแสดงความนับถือ</p>  
    <p style="text-indent: 18em">ฝ่ายบริหารทรัพยากรบุคคล</p>                                                                         
    <p style="text-indent: 18em">บริษัท แฟลช เอ็กซ์เพรส จำกัด</p>
EOF;*/


            $staffIds = $WorkflowServer->findHRBP($staff['node_department_id'], ["store_id" => $staff['sys_store_id']]);
            $staffIds = array_merge(explode(",", $staffIds), explode(",", $WorkflowServer->findJurisdictionAreaStaffIds($staff['node_department_id'], ["store_id" => $staff['sys_store_id']])));
            $staffIds = array_merge($staffIds, [$staff['staff_info_id'], $staff['manger']]);

            foreach ($staffIds as $staffId) {

                if ($staff['status'] == ProbationServer::STATUS_PASS || $staff['status'] == ProbationServer::STATUS_FORMAL) {
                    // 通过
                    $html = addslashes("<div style='font-size: 30px'>" . $bll->getMsgTemplateByUserId($staffId,"hr_probation_passed_msg",[
                            'name_id' => $staff['name'] . '/' . $staff['staff_info_id'],
                            'department' => $staff['department_name'],
                            'job_name' => $staff['job_name'],
                            'store_name' => $staff['sys_store_id'] == -1 ? "Head Office" : $staff['store_name'],
                            'name' => $staff['name'],
                            'formal_at'=>$staff['formal_at'],
                        ]) . "</div>");
                } else {
                    // 不通过
                    $html = addslashes("<div style='font-size: 30px'>" . $bll->getMsgTemplateByUserId($staffId,"hr_probation_not_passed_msg",[
                            'name_id' => $staff['name'] . '/' . $staff['staff_info_id'],
                            'department' => $staff['department_name'],
                            'job_name' => $staff['job_name'],
                            'store_name' => $staff['sys_store_id'] == -1 ? "Head Office" : $staff['store_name'],
                            'name' => $staff['name'],
                        ]) . "</div>");
                }

                $id = time() . $staffId . rand(1000000, 9999999);
                $param['staff_users'] = [$staffId];//数组 多个员工id
                $param['message_title'] = $bll->getMsgTemplateByUserId($staffId,'hr_probation_field_msg_to_staff_title');
                $param['message_content'] = $html;
                $param['staff_info_ids_str'] = $staffId;
                $param['id'] = $id;
                $param['category'] = -1;

                $this->getDI()->get('logger')->write_log('send_msg_to_staffAction-param:' . json_encode($param), 'info');
                $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
                $bi_rpc->setParams($param);
                $res = $bi_rpc->execute();
                $this->getDI()->get('logger')->write_log('send_msg_to_staffAction-result:' . json_encode($res), 'info');
                if ($res && $res['result']['code'] == 1) {
                    $kitId    = $res['result']['data'][0];
                    $this->myLogger('send_msg_to_staffAction message_backyard  写入message成功' . $staffId." message_id".$kitId, 'info');
                }else{
                    $this->myLogger('send_msg_to_staffAction message_backyard  写入message失败' . $staffId, 'error');
                }
            }

        }

        $this->myLogger("send msg to staff: probation staffs on formal_at between" . $start . "===" . $end . "=====end");
    }
	
	
	
	/**
	 * 给已通过的员工，并修改状态成，已转正
	 *
	 * 每天执行
	 */
	public function save_probation_staffAction()
	{
		$bll = new ProbationServer($this->lang, $this->add_hour);
		
		//今天
		$start = gmdate("Y-m-d", time() + ( $this->add_hour)*3600);
		$end = $this->getDateByDays($start, 1, 1);//1 天后转正人的
		
		
		$staffs = $bll->getStaffsByFormalDate($start, $end,[ProbationServer::STATUS_PASS],true);
        $staffs = array_filter($staffs, function ($v) {
            return !(
                $v['status'] == HrProbationModel::STATUS_FORMAL &&
                $v['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE &&
                $v['second_status'] == HrProbationModel::SECOND_STATUS_PASS
            );
        });
		if (empty($staffs)) {
			$this->myLogger("修改转正,没有数据 日期" . $start . "===" . $end . "=====end");
			return;
		}
		
		$staffIdArr = array_column($staffs, "staff_info_id");
		$flag = $bll->formalStaffs($staffIdArr);
		
		if (!$flag) {
			$this->myLogger("修改转正修改失败: formal staff_info_id in (" . implode(",", $staffIdArr) . ")", "error");
			return;
		}
		
		
		foreach ($staffs as $staff) {
			
			$bll->putFormalLog(-1,$staff['staff_info_id'],2,4);
			
		}
		
		$this->myLogger("修改转正修改成功 between" . $start . "===" . $end . "=====end");
	}

	
}