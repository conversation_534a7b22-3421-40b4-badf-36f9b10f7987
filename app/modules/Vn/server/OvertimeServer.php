<?php

namespace FlashExpress\bi\App\Modules\Vn\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\HrShiftServer;
use FlashExpress\bi\App\Server\OvertimeServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\StaffServer;

class OvertimeServer extends GlobalBaseServer
{

    //超时加班 只能是 上班之后 申请晚上下班之后的 对应时长 需要动态获取
    public $overOt = [
        HrOvertimeModel::OVERTIME_1 => [],
    ];

    //全天加班 上班时间开始就可以申请的 节假日 或者 休息日加班 对应时长 是固定的
    public $holidayOt = [
        HrOvertimeModel::OVERTIME_2 => [],
        HrOvertimeModel::OVERTIME_4 => [],
    ];

    /**
     * 获取加班类型
     * @Access  public
     * @Param   array $paramIn
     * @Param   array $userinfo
     * @Return  array
     */
    public function getTypeOvertime($paramIn = [], $userinfo = [])
    {
        //获取加班类型加班类型
        $data                           = $this->getAllOtType();
        $returnData['data']['dataList'] = $data;
        if (!empty($paramIn['is_svc'])) { //bi工具 或者其他 不需要限制的 直接调用
            return $this->checkReturn($returnData);
        }

        $staff_re = new StaffRepository($this->lang);
        if (empty($this->staffInfo)) {
            $this->staffInfo = $staff_re->getStaffPosition($paramIn['staff_id']);
        }

        //获取有权限的类型和时长
        $data = $this->getTypeDuration($data);

        //佳雪需求：基层员工用模板1 非基层员工用模板2
        if ($userinfo) {
            $returnData['data']['template_type'] = 1;
        } else {
            $returnData['data']['template_type'] = 2;
        }
        $returnData['data']['dataList'] = $data;
        return $this->checkReturn($returnData);
    }

    /**
     * 获取加班类型
     * @return array[]
     */
    public function getAllOtType($locale = '')
    {

        $new_duration = array(
            array('time_hour' => 4,'time_text' => $this->getTranslation($locale)->_('ot_4_new_text')),
            array('time_hour' => 8,'time_text' => $this->getTranslation($locale)->_('ot_8_new_text')),
        );
        $new_customer = array(
            array('time_hour' => 2,'time_text' => '2h'),
            array('time_hour' => 3,'time_text' => '3h'),
            array('time_hour' => 4,'time_text' => '4h'),
        );

        return [
            [
                //工作日加班
                'code' => '1',
                'msg'  => $this->getTranslation($locale)->_('5131'),
                'sub_msg' => '',
                'duration' => $new_customer
            ],[
                //休息日加班
                'code' => '2',
                'msg'  => $this->getTranslation($locale)->_('5129'),
                'sub_msg' => '',
                'duration' => $new_duration
            ],[
                //节假日加班
                'code' => '4',
                'msg'  => $this->getTranslation($locale)->_('5130'),
                'sub_msg' => '',
                'duration' => $new_duration
            ]
        ];
    }

    //给审批流历史用的 类型数据
    public function getApprovalOtType($locale = '')
    {
        return $this->getAllOtType($locale);
    }

    /**
     * 新建加班
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function addOvertimeV3($paramIn = [])
    {
        $this->param   = $paramIn;
        $staffId       = $this->processingDefault($paramIn, 'staff_id',2);
        $type          = $this->processingDefault($paramIn, 'type',2);
        $start_time    = $this->processingDefault($paramIn, 'start_time');
        $reason        = $this->processingDefault($paramIn, 'reason');
        $duration      = floatval($paramIn['duration']);
        $date          = $this->processingDefault($paramIn, 'date_at');
        $reason        = addcslashes(stripslashes($reason),"'");

        if(empty($date) || empty($start_time) || empty($type)){
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        //拼接endtime
        $startTime  = strtotime($start_time);
        $start_time = date('Y-m-d H:i:s',$startTime);
        $endTime    = empty($end_time) ? 0 : strtotime($end_time);
        if(!empty($endTime)){
            $end_time   = date('Y-m-d H:i:s',$endTime);
        }else{
            $endTime    = $startTime + $duration * 3600;
            $end_time   = date('Y-m-d H:i:s',$endTime);
        }

        // 校验时间段 可选日期为近5天(前天、昨天和今天 明天后天) 如果是bi工具 不做时间校验
        //新需求 验证逻辑 修改 https://l8bx01gcjr.feishu.cn/docs/doccnznGnDKzb4akgPuhYQq5oHc
        $shiftServer = new HrShiftServer();
        $shift_info = $shiftServer->getShiftInfos($staffId, [$date]);
        $shift_info = $shift_info[$date] ?? [];
        if(empty($shift_info)) { //没有班次信息 不让申请
            return $this->checkReturn(-3, $this->getTranslation()->_('no_shift_notice'));
        }
        $staff_re    = new StaffRepository($this->lang);
        $this->staffInfo = $staff_re->getStaffPosition($staffId);
        if (empty($this->staffInfo)) {
            throw new ValidationException('can not find staff');
        }
        if(empty($paramIn['is_bi'])){
            $this->checkOTTimePeriod($type, $shift_info, $date);
        }

        $this->checkTypeDuration();

        //!!!!!!  加班 校验逻辑
        $paramIn['shift_info'] = $shift_info;
        $check_data = $this->checkOvertime($paramIn);

        if($check_data['code'] != 1)
            return $check_data;


        //新增了 bi工具 补记录 状态直接为审核通过 不发push 审核人为 操作工具hr
        $higher = '';//bi工具 直接审核通过 需要记录操作人 带申请的 不需要记录上级
        $state = 1;
        if(!empty($paramIn['is_bi'])){
            $higher = $paramIn['operator'];
            $state = 2;
        }
        $serialNo = $this->getID();
        $insertParam = [
            'staff_id'   => $staffId,
            'type'       => $type,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'reason' => $reason,
            'reject_reason' => '',
            'state'     => $state,
            'duration'     => $check_data['data']['duration'],
            'higher_staff_id' => $higher,
            'is_anticipate' =>$check_data['data']['is_anticipate'],
            'date_at' => $date,
            'references' => array(),
            'serial_no' => (!empty($serialNo) ?'OT'.$serialNo : NULL),
            'wf_role'   => 'ot_new'
        ];

        $db = $this->getDI()->get('db');
        $db->begin();
        $overtimeId = $this->overtime->addOvertime($insertParam);

        if ($overtimeId && empty($paramIn['is_bi'])) { //非bi途径
            try {

                //创建
                $server = new ApprovalServer($this->lang, $this->timezone);
                $requestId = $server->create($overtimeId, AuditListEnums::APPROVAL_TYPE_OVERTIME, $staffId);
                if (!$requestId) {
                    throw new \Exception('创建审批流失败');
                }
                $db->commit();
            } catch (\Exception $e){
                $db->rollback();
                $this->wLog('pushError',$e->getMessage(), 'overtime');
                return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
            }
        } else if (!empty($paramIn['is_bi'])) { //bi添加加班
            $db->commit();
            return $this->checkReturn(['data'=>['overtime_id'=>$overtimeId]]);
        } else {
            $db->rollback();
            return $this->checkReturn(-3,$this->getTranslation()->_('4101'));
        }
        return $this->checkReturn(['data'=>['overtime_id'=>$overtimeId]]);
    }

    /**
     * 修改状态
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @throws \Exception
     */
    public function updateOvertimeV3($paramIn = [])
    {
        $staffId       = $this->processingDefault($paramIn, 'staff_id',2);
        $overtimeId    = $this->processingDefault($paramIn, 'audit_id',2);
        $reject_reason = $this->processingDefault($paramIn, 'reject_reason');
        $state         = $this->processingDefault($paramIn, 'status',2);

        //获取审批详情
        $overtimeList = $this->overtime->infoOvertime(['overtime_id'=>$overtimeId]);
        if (empty($overtimeList)){
            return $this->checkReturn(-3,$this->getTranslation()->_('4102'));
        }

        //oa 审批处理中 不能操作
        if($overtimeList['in_approval'] == HrOvertimeModel::IN_APPROVAL && empty($paramIn['is_mq'])){
            return $this->checkReturn(-3,$this->getTranslation()->_('ot_in_approval_notice'));
        }
        $server = new ApprovalServer($this->lang, $this->timezone);
        if ($state == enums::$audit_status['approved']) {
            //同意
            $flag = $server->approval($overtimeId, AuditListEnums::APPROVAL_TYPE_OVERTIME, $staffId);
        } else {
            //驳回
            $flag = $server->reject($overtimeId, AuditListEnums::APPROVAL_TYPE_OVERTIME, $reject_reason, $staffId);
        }

        return $this->checkReturn(['data'=>['audit_id'=>$overtimeId]]);
    }

    /**
     * 校验申请加班日期、班次
     * @param int $overtime_type 加班类型
     * @param array $shift_info 班次信息
     * @param string $date 加班日期
     * @return void
     * @throws \Exception
     */
    public function checkOTTimePeriod($overtime_type, $shift_info, $date)
    {
        $date_tmp = strtotime($date);
        $beforeTime = $behindTime = strtotime(date('Y-m-d'),time());

        //OFF Day加班、Rest Day加班、法定节假日加班班
        //ot 可以多两天时间区间限制
        if(in_array($overtime_type, array_keys($this->holidayOt))) {
            $behindTime = strtotime(date("Y-m-d",strtotime("+2 day")));
        }
        // 通过日期判断新旧班次
        $start = $shift_info['start'];
        $end   = $shift_info['end'];

        //如果是班次跨天的员工，比如23：00-8：00班次，跨天的班次，可以申请前一天的加班。
        $shift_start = strtotime("{$date} {$start}");
        $shift_end = strtotime("{$date} {$end}");
        if($shift_start > $shift_end && in_array($overtime_type, array_keys($this->overOt))) {
            $beforeTime = strtotime(date("Y-m-d",strtotime("-1 day")));
        }

        if($date_tmp < $beforeTime || $date_tmp > $behindTime) {

            //获取OT类型
            $typeList = $this->getAllOtType();
            $typeArr = array_column($typeList, 'msg', 'code');

            //在许可时间外申请，提示错误
            if(in_array($overtime_type, [2, 4])) {
                $notice_str = str_replace('{type}',$typeArr[$overtime_type],$this->getTranslation()->_('err_msg_ot_over_2_days'));
            } else {
                $notice_str = $this->getTranslation()->_('err_msg_ot_only_cur_day');
            }
            throw new \Exception($this->getTranslation()->_($notice_str), enums::$ERROR_CODE['1000']);
        }
    }

    /**
     * 校验除 时间范围内的 其他逻辑 添加ot 和bi 的 修改ot
     * 加班类型
     * 1-工作日加班
     * 2-休息日加班
     * 4-节假日加班
     * @param $paramIn
     * @throws \Exception
     */
    public function checkOvertime($paramIn)
    {
        $staffId       = $this->processingDefault($paramIn, 'staff_id',2);
        $type          = $this->processingDefault($paramIn, 'type',2);
        $start_time    = $this->processingDefault($paramIn, 'start_time');
        $duration      = floatval($paramIn['duration']);
        $date          = $this->processingDefault($paramIn, 'date_at');

        $startTime = strtotime($start_time);
        $endTime = $startTime + $duration * 3600;

        $current_date = date('Y-m-d',time());//今天日期
        $start_date = date('Y-m-d',$startTime);//申请开始时间日期

        //新需求 OT申请开始时间不得晚于申请日期次日中午12点。
        $_date = date('Y-m-d 12:00:00', strtotime ("+1 day", strtotime($date)));
        $_diffdate = strtotime($_date);

        if ($startTime > $_diffdate){
            return $this->checkReturn(-3, $this->getTranslation()->_('ot_time_limit'));
        }

        $u_info = $this->staffInfo;
        if(empty($this->staffInfo)){
            $staff_model = new StaffRepository();
            $u_info = $this->staffInfo = $staff_model->getStaffPosition($staffId);
            if (empty($u_info)) {
                return $this->checkReturn(-3, 'can not find staff');
            }
        }
        //新增验证 日期限制逻辑 hcm 配置页面
        $this->checkLimitDate();

        //申请各类[ot总时长]加班日期在[自然月1号-月底]之间不能超过[40]小时
        $firstDayOfMonth = date("Y-m-01", strtotime($date));
        $lastDayOfMonth = date("Y-m-t", strtotime($date)); // 't' 表示该月的最后一天

        $staffOtInfo = HrOvertimeModel::findFirst([
            'conditions' => 'staff_id = :staff_info_id: and state in (1,2) and date_at >= :start_at: and date_at <= :end_at:',
            'bind'       => [
                'staff_info_id' => $staffId,
                'start_at'      => $firstDayOfMonth,
                'end_at'        => $lastDayOfMonth,
            ],
            'columns' => 'sum(duration) as total_duration'
        ]);
        if ($staffOtInfo) { //本月存在加班
            $staffOtInfo = $staffOtInfo->toArray();
            if ($staffOtInfo['total_duration'] >= 40 || ($staffOtInfo['total_duration'] + $duration) > 40) {
                return $this->checkReturn(-3, $this->getTranslation()->_('err_msg_more_than_40'));
            }
        }

        //新需求 网点总部逻辑 按照 该员工工作日期判断 5天为总部 6天为网点
        $organization_type = $u_info['organization_type'];

        //开始时间的日期不能早于申请日期 只试用于凌晨加班 的跨天 且不能超过两天
        $sub = (strtotime($start_date) - strtotime($date))/3600;
        if($start_date < $date)
            return $this->checkReturn(-3, $this->getTranslation()->_('wrong_date'));
        if($sub >= 48)
            return $this->checkReturn(-3, $this->getTranslation()->_('wrong_date'));

        //如选择“周末和假期加班”，系统验证该申请日期是否为公休日和周末，如申请日期非周末和假期
        $holidays = (new AuditServer($this->lang, $this->timezone))->get_holidays($u_info);
//        $holidays = array_column($holidays, 'day');

        $is_rest = false;

        $overtimeRepo = new OvertimeRepository($this->timeZone);
        $is_workdays = $overtimeRepo->get_workdays($staffId , $date);//当天是否是轮休日
        if(!empty($is_workdays))
            $is_rest = true;

        //校验休息日
        $this->checkRestDay($type, $is_rest, $holidays, $date);


        //判断是预申请还是 补申请
        $is_anticipate = 0;
        if($start_date >= $current_date){
            $is_anticipate = 1;//是预申请
        }

        //看是否是补申请 并且 获取该日期打卡时间 校验加班时间 是否在打卡时间之内
        $att_model = new AttendanceRepository($this->lang, $this->timeZone);
        $add_hour = $this->getDI()['config']['application']['add_hour'];

        //转换零时区
        $start = date('Y-m-d H:i:s',$startTime - $add_hour * 3600);
        $end = date('Y-m-d H:i:s',$endTime - $add_hour * 3600 - 300);
        $att_info = $att_model->getAttendanceInfo($staffId,$start,$end);

        //打卡间隔时长 新需求 增加考勤容错率 5分钟 由小时 改为分钟
        $attendance_last = 0;
        $allowed_min = 5;
        if(!empty($att_info))
            $attendance_last = floor((strtotime($att_info['end_at']) -  strtotime($att_info['started_at']))/60);//上下班打卡间隔时长

        //如果是当天申请 且有打卡记录 视为 补申请
        if($is_anticipate == 1 && !empty($att_info))
            $is_anticipate = 0;

        //所选日期没有上班打卡记录和下班打卡记录 并且是补申请；
        if(empty($att_info) && $is_anticipate == 0){
            return $this->checkReturn(-3, $this->getTranslation()->_('1101'));
        }

        // 校验实际 加班 时长
        $act_last = $duration;
        if($act_last >= 24)
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_24'));

        if ($act_last <= 0){
            return $this->checkReturn(-3, $this->getTranslation()->_('5101'));
        }


        $ext_server = new OvertimeExtendServer($this->lang,$this->timeZone);

        if($type == 1){
            //新需求 https://l8bx01gcjr.feishu.cn/docs/doccnznGnDKzb4akgPuhYQq5oHc
            $new_check = $ext_server->extend_check($paramIn,$u_info);
            if($new_check['code'] != 1)
                return $new_check;



            //新增逻辑
            /**
             *  所选的开始时间+所选OT时长不得晚于实际的下班打卡时间 ---根据开始结束时间区间获取打卡记录 可以解决
            如果当天无请假，需要当天出勤超过11小时（9+2）,提示：当日出勤时长不足11小时，不能申请OT！
            如果当天请假半天，需要当天出勤超过6小时（4+2），提示：当日出勤时长不足6小时，不能申请OT！
            如果当天请假1天（且无打卡），不可以申请OT  提示：当天请假，不能申请OT！
             * 越南 后来改成 5 和10 小时 石阳 https://flashexpress.feishu.cn/docx/ML9rdSJKkowIlsxQBdtcQnB1nFd
             */
            //补申请 且有打卡记录 或者当天已打卡申请 都算补申请
            if($is_anticipate == 0){//补申请 判断请假 验证时长 关联请假
                //获取当天是否有请假记录
                $leave_param['staff_id'] = $staffId;
                $leave_param['day'] = $att_info['attendance_date'];
                $leave_info = (new AuditRepository())->getLevelData($leave_param);
                if(!empty($leave_info)){
                    //0 未请假 1 上午请假 2 下午请假 3 全天请假
                    if($leave_info['level_state'] == 1 || $leave_info['level_state'] == 2){
                        if($attendance_last < (5 * 60 - $allowed_min)){
                            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_attendance_limit_5'));
                        }
                    }
                }else{//没有请假 判断是否大于11小时
                    if($attendance_last < (10 * 60 - $allowed_min)) {
                        return $this->checkReturn(-3, $this->getTranslation()->_('overtime_attendance_limit_10'));
                    }
                }

                //节假日加班 校验打卡时长 所选时长必须小于或等于实际出勤计算的时长，否则不能提交。
                if(($act_last * 60 - $allowed_min) > $attendance_last){
                    return $this->checkReturn(-3, $this->getTranslation()->_('overtime_limit'));
                }
            }

        }

        //节假日、休息日上班类型
        if(in_array($type, [2,4])){
            //申请时，选择开始时间和结束时间，开始日期和结束日期必须为同一天或相邻；
            if($act_last >= 24)
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_24'));

            //根据开始时间和结束时间计算出勤天数
//            //4～8小时，算0.5天
//            //8小时及以上，算1天
//            if($act_last > 4 && $act_last < 8)
//                $act_last = 4;
//            if($act_last >= 8)
//                $act_last = 8;

            if($act_last < 4){//时长＜4小时，不能申请该项。提示：当日出勤时长不足4小时，不能申请OT！
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_work_limit_4'));
            }


            //补申请
            if($is_anticipate == 0){
                //小于4小时不能申请
                if($attendance_last < (4 * 60 - $allowed_min)){
                    return $this->checkReturn(-3, $this->getTranslation()->_('overtime_work_limit_4'));
                }
                //时长与打卡记录不符 不能申请
                if(($act_last * 60 - $allowed_min) > $attendance_last){
                    return $this->checkReturn(-3, $this->getTranslation()->_('overtime_limit'));
                }
            }

        }

        //校验 同一天是否存在 有交集的ot
        if(empty($paramIn['is_edit'])){
            $is_contain = $ext_server->check_ot_record($staffId,$date,$startTime,$endTime,$type);
            if($is_contain['code'] != 1)
                return $is_contain;
        }


        $res['data']['duration'] = $act_last;
        $res['data']['is_anticipate'] = $is_anticipate;
        return $this->checkReturn($res);
    }

    /**
     * 校验休息日
     * @param int $type 加班类型
     * @param bool $is_rest 是否为休息日
     * @param $holidays
     * @param $date
     * @return void
     * @throws \Exception
     */
    public function checkRestDay(int $type, bool $is_rest, $holidays, $date)
    {
        if ($type != 4 && in_array($date, $holidays) && $is_rest) { //ph + 休息日重合,只能申请PH
            throw new \Exception($this->getTranslation()->_('err_msg_ot_only_ph_ot'), enums::$ERROR_CODE['1000']);
        } else if ($type != 4 && (in_array($date, $holidays) && !$is_rest)) { //ph 或 休息日 不能申请工作日加班
            throw new \Exception($this->getTranslation()->_('err_msg_ot_only_ph_ot'), enums::$ERROR_CODE['1000']);
        } else if ($type != 2 && !in_array($date, $holidays) && $is_rest) {
            throw new \Exception($this->getTranslation()->_('err_msg_ot_only_off_ot'), enums::$ERROR_CODE['1000']);
        } else if ($type != 1 && !in_array($date, $holidays) && !$is_rest) {
            throw new \Exception($this->getTranslation()->_('err_msg_ot_only_normal_ot'), enums::$ERROR_CODE['1000']);
        }
    }


    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //[1]获取加班详情数据
        $result = $this->overtime->infoOvertime(['overtime_id'=>$auditId]);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }

        //[2]组织详情数据
        $t = $this->getTranslation();
        $overtimeTypeList = $this->getAllOtType();
        $overtimeType = array_column($overtimeTypeList, 'msg','code');
//        $overtimeSubtype = array_column($overtimeTypeList, 'sub_msg', 'code');

        $detailLists = [
            'apply_parson'       => sprintf('%s ( %s )', $staff_info['name'] , $staff_info['id']),
            'apply_department'   => sprintf('%s - %s', $staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
            'OT_date'       => $result['date_at'],
            'OT_type'       => ($overtimeType[$result['type']] ?? ''),
            'start_time'    => $result['start_time'],
            'end_time'      => (in_array($result['type'], [2,4]) && $result['duration'] == 8)
                ? date('Y-m-d H:i:s', strtotime($result['end_time'] . ' + 1 hours'))
                : $result['end_time'],
            'duration'      => $result['duration'],
            'OT_reason'     => $result['reason'],
            'ot_detail_6' => $staff_info['store_name'],
        ];

        $references = json_decode($result['references'], true) ?? '';

        if (isset($references['store_category']) && in_array($references['store_category'], explode(',', HrOvertimeModel::$job_apply['shop_category']))
            && in_array($references['job_title'], [enums::$job_title['shop_officer'], enums::$job_title['shop_cashier']])
        ) {
            $detailLists = array_merge($detailLists, [
                'ot_detail_11' => $references['region_name'] ?? '', //员工所在大区名称
                'attendance_num' => $references['attendance_num'] ?? '', //员工申请OT日所在网点出勤人数
                'parcel_num' => $references['parcel_num'] ?? '', //员工申请OT日所在网点总揽派件量
                'ot_detail_10' => ($references['all_effective_num'] ?? 0) . ' ' .  $t->_('ot_detail_4'),//上周SHOP相关网点平均工作效率
                'ot_detail_2' => ($references['store_effective_num'] ?? 0) . ' ' .  $t->_('ot_detail_4'),//上周特定网点平均工作效率
            ]);

            if (date("Y-m-d", strtotime($result['start_time'])) > date('Y-m-d', strtotime($result['created_at']))) {
                unset($detailLists['attendance_num']);
                unset($detailLists['parcel_num']);
            }

            if ($result['type'] == 1) { //1。5倍工资 才显示
                $detailLists = array_merge($detailLists, [
                    'ot_detail_3' => ($references['duration'] ?? 0) . "h"
                ]);
            }
            if ($result['type'] == 2) { //3倍工资 才显示
                $detailLists = array_merge($detailLists, [
                    'ot_detail_9' => ($references['duration'] ?? 0) . "h"
                ]);
            }
            if ($result['type'] == 4) { //1倍工资 才显示
                $detailLists = array_merge($detailLists, [
                    'ot_detail_8' => ($references['duration'] ?? 0) . "h"
                ]);
            }
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        $data = [
            'title'       => $this->auditlist->getAudityType(enums::$audit_type['OT']),
            'id'          => $result['overtime_id'],
            'staff_id'    => $result['staff_id'],
            'type'        => enums::$audit_type['OT'],
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['state'],
            'status_text' => $this->auditlist->getAuditStatus('10' . $result['state']),
            'notice'      => $result['notice'] ?? '',
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        $returnData['data']['head']   = $data;
        return $returnData;
    }

    /**
     * 审批完成回调方法
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        //如果为最终审批状态，则同步更新审批状态
        if ($isFinal) {
            $info        = HrOvertimeModel::findFirst("overtime_id = {$auditId}");
            if ($state == Enums::APPROVAL_STATUS_REJECTED) {
                if (isset($extend['staff_id'])) {
                    $staff = HrStaffInfoModel::findFirst([
                        'conditions' => ' staff_info_id = :staff_id: ',
                        'bind'       => ['staff_id' => $extend['staff_id']]
                    ]);
                    if ($staff) {
                        $staff = $staff->toArray();
                    }
                }
                $data['approver_id']   = isset($extend['staff_id']) ? $extend['staff_id'] : 0;
                $data['approver_name'] = isset($staff) && $staff ? $staff['name'] : '';
                $data['reject_reason'] = isset($extend['remark']) ? $extend['remark'] : '';
            }
            $data['state'] = $state;
            $data['in_approval'] = HrOvertimeModel::NOT_IN_APPROVAL;

            $this->getDI()->get('db')->updateAsDict(
                'hr_overtime',
                $data,
                'overtime_id = '.$auditId
            );
            $this->sendMessage($info->toArray(),$state);
        }
    }


    /**
     * 编辑加班  bi工具
     * @param $paramIn
     */
    public function edit_overtime($paramIn){
        $staffId       = $this->processingDefault($paramIn, 'staff_id',2);
        //工作日校验 加班类型1=工作日，2=节假日加班，3=晚班 4-节假日正常上班
        $type          = $this->processingDefault($paramIn, 'type',2);
        $start_time    = $this->processingDefault($paramIn, 'start_time');
        $date          = $this->processingDefault($paramIn, 'date_at');
        $duration      = floatval($paramIn['duration']);
        $overtime_id = intval($paramIn['overtime_id']);

        if(empty($date) || empty($start_time) || empty($type) || empty($overtime_id)){
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        $startTime = strtotime($start_time);
        $endTime = $startTime + floatval($duration) * 3600;
        $end_time = date('Y-m-d H:i:s',$endTime);
        $paramIn['is_edit'] = 1;
        $shiftServer = new HrShiftServer();
        $shift_info = $shiftServer->getShiftInfos($staffId, [$date]);
        $shift_info = $shift_info[$date] ?? [];
        $paramIn['shift_info'] = $shift_info;
        $res = $this->checkOvertime($paramIn);

        if($res['code'] != 1){
            return $res;
        }

        $model = new OvertimeRepository($this->timezone);
        $info = $model->getInfoById($overtime_id);
        if($info['state'] != 2){//非审核通过的 记录 不允许 修改 没走完正常审核逻辑
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_bi_notice'));
        }

        $update_data = [
            'type'       => $type,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'state'     => 2,//bi 工具 直接审核通过
            'duration'     => $res['data']['duration'],
            'higher_staff_id' => $paramIn['operator'],
            'is_anticipate' =>$res['data']['is_anticipate'],
            'date_at' => $date,
        ];

        $model = new OvertimeRepository($this->timezone);
        $flag = $model->updateInfoByTable('hr_overtime','overtime_id',$overtime_id,$update_data);
        if($flag)
            return $this->checkReturn([]);
        else
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
    }
    //越南 部门不同 ot 规则不同 规则文案放后端了
    public function ruleConfirm($staffInfo){
        $extendServer = new OvertimeExtendServer($this->lang, $this->timezone);
        $ffmFlag = $extendServer->ffmCondition($staffInfo);
        if($ffmFlag){
            return $this->getTranslation()->_('ot_rule_vn_ffm');
        }
        return $this->getTranslation()->_('ot_rule_vn');
    }

}
