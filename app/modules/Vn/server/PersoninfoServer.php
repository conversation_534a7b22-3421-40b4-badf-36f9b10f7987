<?php

namespace FlashExpress\bi\App\Modules\Vn\Server;

use FlashExpress\bi\App\library\ApiClient;

class PersoninfoServer extends \FlashExpress\bi\App\Server\PersoninfoServer
{

    /**
     * 获取工资表明细
     * @param $staffId
     * @param $month
     * @return array|mixed
     * @throws \ReflectionException
     */
    public function getSalaryInfoFromHCM($paramIn)
    {
        $data    = [];
        $month   = $paramIn['month'];
        $staffId = $paramIn['staff_id'];
        //传一个当前月份 如果没有记录 会返回 有薪资记录的最新的一个月  （逻辑直接放到hcm 来处理）
        $param['month']    = $month;
        $param['staff_id'] = $staffId;

        $ac = new ApiClient('hcm_rpc', '', 'get_salary_data', $this->lang);
        $ac->setParams($param);
        $ac_result = $ac->execute();
        $this->logger->write_log("staff {$staffId} getSalaryInfoFromHCM request:" . json_encode($param,
                JSON_UNESCAPED_UNICODE) . " res:" . json_encode($ac_result, JSON_UNESCAPED_UNICODE) . " ", 'info');

        if (!empty($ac_result) && !empty($ac_result['result']['data'])) {
            $data = $this->salary_data_format($ac_result['result']['data']);
            //工资条新增 有效无效ot 数量
            if (!empty($data)) {
                //考勤数量统计
                $salaryAttendanceStat = $this->salaryAttendanceStat($staffId, $data['salary_cycle'], 'only_total');
                $data                 = array_merge($data, $salaryAttendanceStat);
                [$data['effect_num'], $data['invalid_num']] = $this->formatOtData($staffId, $data['salary_cycle']);
            }
        }

        return $data;
    }



    /**
     * 格式化工资表 给前端
     * @param $salary_data
     * @return mixed
     */
    public function salary_data_format($salary_data)
    {
        if(!isset($salary_data['salary_date'])){
            return  [];
        }

        $salary_title = $this->getTranslation()->_('salary_title_1');
        $table_salary['title'] = "{$salary_title} {$salary_data['salary_date']}";
        $table_salary['staff_id'] = $salary_data['staff_id'];
        $table_salary['Name'] = $salary_data['name'];
        $table_salary['Position'] = $salary_data['position'];
        $table_salary['dep'] = $salary_data['department'];
        $table_salary['bank_no'] = $salary_data['bank_no'];
        $table_salary['date'] = $salary_data['salary_date'];
        $table_salary['Total_Income'] = $salary_data['total_income']['amount'];
        $table_salary['Total_Deduct'] = $salary_data['total_deduction']['amount'];
        $table_salary['NET_INCOME'] = $salary_data['net_amount']['amount'];
        $table_salary['transfer_day'] = $salary_data['transfer_day'] ?? null;
        return array_merge($salary_data,$table_salary);

    }

    /**
     * 修改基本资料
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function updatePersonInfobymobile($paramIn = [])
    {
        //[1]参数定义
        $mobile = empty($paramIn['mobile']) ? '' : $paramIn['mobile'];
        $id_number = empty($paramIn['id_number']) ? '' : $paramIn['id_number'];
        $personal_email = empty($paramIn['personal_email']) ? '' : $paramIn['personal_email'];

        if (!empty($mobile)) {
            $PostData['mobile']= $mobile;
        }

        if (!empty($id_number)) {
            $PostData['identity'] = $id_number;
        }
        if(isset($paramIn['nick_name'])) {
            $nick_name = empty($paramIn['nick_name']) ? '' : $paramIn['nick_name'];
            $PostData['nick_name'] = $nick_name;
        }
        if (isset($paramIn['personal_email'])) {
            $PostData['personal_email'] = $personal_email;
        }

        //税卡号
        if(isset($paramIn['tax_card'])) {
            $PostData['tax_card'] = $paramIn['tax_card'];
        }

        if(isset($paramIn['bank_account']) && !empty($paramIn['bank_account'])) {
            $PostData['bank_no_name'] = $paramIn['bank_account'];
        }

        // 允许为空的字段
        if (isset($paramIn['empty_field']) && is_array($paramIn['empty_field']) && !empty($paramIn['empty_field'])) {
            $PostData['empty_field'] = $paramIn['empty_field'];
        }

        if (isset($PostData) && $PostData) {
            $PostData['fbid'] = $paramIn['staff_id'];
            $apiClient = new ApiClient('hr_rpc', '', 'sync-item', $this->lang);
            $apiClient->setParams($PostData);
            $result = $apiClient->execute();
            if (isset($result['result']['code']) && $result['result']['code'] == 0) {
                return $this->checkReturn(['msg' => $this->getTranslation()->_('5002')]);
            } else {
                return $this->checkReturn([
                    'code' => -3,
                    'msg'  => $result['result']['msg'] ?? $this->getTranslation()->_('update_person_info_fail'),
                ]);
            }
        }

        return $this->checkReturn(['msg'=>$this->getTranslation()->_('5002')]);
    }
}
