<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/9/8
 * Time: 2:26 PM
 */

namespace FlashExpress\bi\App\Modules\Vn\Server;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffDaysFreezeModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReadModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Modules\Vn\Server\Vacation\AnnualServer;
use FlashExpress\bi\App\Modules\Vn\Server\Vacation\MaternityServer;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\StaffPublicHolidayRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

use FlashExpress\bi\App\Server\LeaveServer AS GlobalBaseServer;
use Exception;
use FlashExpress\bi\App\Modules\Vn\Server\Vacation\InternationalServer;

class LeaveServer extends GlobalBaseServer {

    public $leaveObject;
    const LEAVE_DAYS = 12;
    public function format_leave_insert($staffId,$param){
        $leaveStartTime = $param['start_time'];
        $leaveEndTime = $param['end_time'];
        $leaveType = $param['leave_type'];
        $holidays = $param['holidays'];
        $sub_day_off = $param['sub_day'];
        $leaveStartType = $param['start_type'];
        $leaveEndType = $param['end_type'];
        $creat_year = date("Y");

        $insert = array();
        if ((strtotime($leaveEndTime) - strtotime($leaveStartTime)) / (24 * 3600) >= 1) {//跨天
            $key        = $start_date = date('Y-m-d', strtotime($leaveStartTime));
            $end_date   = date('Y-m-d', strtotime($leaveEndTime));
            $date_array = array();
            while ($key <= $end_date) {
                $date_array[] = $key;
                $key          = date("Y-m-d", strtotime("+1 day", strtotime($key)));
            }

            foreach ($date_array as $k => $date) {
                //whether in ones holidays or not
                if (in_array($date, $holidays) && in_array($leaveType, $sub_day_off))
                    continue;

                //请假多天 拆开单天
                $insert[$k]['staff_info_id'] = $staffId;
                $insert[$k]['date_at']       = $date;
                $insert[$k]['type']          = 0;
                $insert[$k]['year_at']       = date('Y', strtotime($date));
                //第一天
                if ($date == $start_date) {
                    $insert[$k]['type'] = ($leaveStartType == 1) ? 0 : $leaveStartType;
                }
                //最后一天
                if ($date == $end_date) {
                    $insert[$k]['type'] = ($leaveEndType == 2) ? 0 : $leaveEndType;
                }

                if(in_array($leaveType,array(enums::LEAVE_TYPE_1,enums::LEAVE_TYPE_4,enums::LEAVE_TYPE_5,enums::LEAVE_TYPE_19)))
                    $insert[$k]['year_at'] = $creat_year;
            }


        } else {//不跨天
            if (in_array($leaveStartTime, $holidays) && in_array($leaveType, $sub_day_off))
                return $this->checkReturn(-3, $this->getTranslation()->_('day off for the apply date'));
            else {
                $insert[0]['staff_info_id'] = $staffId;
                $insert[0]['date_at']       = $leaveStartTime;
                $insert[0]['type'] = 0;
                if ($leaveStartType == $leaveEndType)//半天
                    $insert[0]['type'] = $leaveStartType;
                $insert[0]['year_at']       = date('Y', strtotime($leaveStartTime));
            }

            if(in_array($leaveType,array(enums::LEAVE_TYPE_1,enums::LEAVE_TYPE_4,enums::LEAVE_TYPE_5,enums::LEAVE_TYPE_19)))
                $insert[0]['year_at'] = $creat_year;
        }
        return array('code' => 1,'msg' => '','data' => $insert);

    }

    //根据额度 标记 年假所属年份
    public function year_flag($staffId,$insert,$month_arr,$leave_param){
        $year_type = $leave_param['year_type'];
        $left_days = $leave_param['left_days'];
        $leaveStartTime = $leave_param['leaveStartTime'];
        // 拼接 拆分表 归属年 year_at 字段
        $start_year = date('Y',strtotime($leaveStartTime));
        $add_row = array();//出现不够减情况 额外新增一条  merge到 insert
        foreach ($insert as $k => $in) {
            $insert[$k]['year_at'] = date('Y', time());
            //优先逻辑  如果日期 在 当年 3月 31号 失效之前 才计算剩余假期 打标记 否则 一律按当年算
            if (!in_array(date('m', strtotime($in['date_at'])), $month_arr)) {
                $insert[$k]['year_at'] = date('Y',strtotime($in['date_at']));
                continue;
            } else {
                if ($year_type == 1)//下面有可能更改为1
                    $insert[$k]['year_at'] = $start_year;
                if ($year_type == -1)
                    $insert[$k]['year_at'] = date('Y', strtotime("{$leaveStartTime} -1 year"));

                if ($year_type == 2) {//需拆分
                    $duration = empty($in['type']) ? 1 : 0.5;
                    if ($left_days > 0 && ($left_days - $duration >= 0)) {//还够减
                        $insert[$k]['year_at'] = date('Y', strtotime("{$leaveStartTime} -1 year"));
                        $left_days             = $left_days - $duration;
                    } else if ($left_days > 0 && ($left_days - $duration < 0)) {//不够减了（剩余0。5 本次记录 需要1天 只能是这种情况 把本条记录更改为半天 额外新增一条半天记录）
                        $insert[$k]['type']    = 1;
                        $insert[$k]['year_at'] = date('Y', strtotime("{$leaveStartTime} -1 year"));

                        //拼接剩下半天 标记归属年 为今年 merge
                        $add_row[0]['staff_info_id'] = $staffId;
                        $add_row[0]['date_at']       = $in['date_at'];
                        $add_row[0]['type']          = 2;
                        $add_row[0]['year_at']       = $start_year;

                        $left_days = 0;//减没了
                        $year_type = 1;//剩下的 都是 当年的了
                    } else if ($left_days == 0) {//上次循环 减没了
                        $insert[$k]['year_at'] = $start_year;
                        $year_type             = 1;
                    }

                }
            }

        }

        if (!empty($add_row))
            $insert = array_merge($insert, $add_row);
        return $insert;
    }

    //新需求 计算 根据职等 对应的假期额度 have day
    public function get_have_day($staff_id,$should_days){
        if(empty($staff_id) || empty($should_days))
            return 0;

        $count_day = StaffDaysFreezeModel::findFirst($staff_id);
        if(empty($count_day))
            return 0;


        $current_tmp = strtotime(date('Y-m-d',strtotime("-1 day")));//昨天00 也就是 任务跑过的日期
        $start_tmp = strtotime(date('Y-01-01'));//周期第一天 00点
        $year = date('Y');
        $all_days = 365;
        if(year_type($year))
            $all_days = 366;

        //获取剩余天数
        $left_days = $all_days - (($current_tmp - $start_tmp) / (24 * 3600));
        $left_days = $should_days * ($left_days / $all_days);//每年剩余天数 对应职等天数的份数 例：剩余200天 职等7天  7 * (200/365)
        $have_days = $count_day->days + $left_days;
        $have_days = round($have_days,enums::ROUND_NUM);
        $int_day   = floor($have_days);//2
        $have_days = $have_days > ($int_day + 0.5) ? ($int_day + 0.5) : $int_day;//2.5
        return $have_days;
    }




    // 根据员工 工作日 不同 返回不同 ph 返回 map list
    public function ph_days($staff_info)
    {
        $bind['type']       = $staff_info['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_5 ? [
            ThailandHolidayModel::TYPE_DEFAULT,
            ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_5,
        ] : [ThailandHolidayModel::TYPE_DEFAULT, ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_6];
        $data               = ThailandHolidayModel::find([
            'conditions' => 'type in ({type:array})',
            'columns'    => 'day,holiday_type',
            'bind'       => $bind,
        ])->toArray();
        $staffPublicHoliday = (new StaffPublicHolidayRepository($this->lang, $this->timezone))->getStaffData($staff_info['staff_info_id']);
        if ($staffPublicHoliday) {
            $data = array_merge($data, $staffPublicHoliday);
        }
        return $data;
    }


    /**
     * 根据员工信息 计算区间内是否存在休息日 返回对应休息日日期数组
     * 用于计算 请假区间 扣除包含休息日的天数
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     */
    public function staff_off_days($staff_id, $start_date, $end_date = '')
    {
        if (empty($end_date))
            $end_date = $start_date;

        //获取员工信息
        $model       = new StaffRepository($this->lang);
        $staff_info  = $model->getStaffPosition($staff_id);

        $rest = $model->get_work_days_between($staff_id, $start_date, $end_date);
        $rest = !empty($rest) ? array_column($rest, 'date_at') : [];

        $holidays = $this->ph_days($staff_info);
	    if(!empty($holidays)){
		    $holidays =  array_column($holidays, 'day');
		    $rest = array_merge($rest,$holidays);
	    }

        $this->logger->write_log("ignore rest_day {$staff_id} ".json_encode($rest),'info');

        return $rest;

    }


    //根据各种规则 计算员工额度 按月份 固化表
    public function get_year_leave_days($staff_info){
        //获取 当月额度
        $freeze_info = StaffDaysFreezeModel::findFirst("staff_info_id = {$staff_info['staff_info_id']} and leave_type = ".enums::LEAVE_TYPE_1);
        $freeze = 0;
        if(!empty($freeze_info->days))
            $freeze = $freeze_info->days;

        return round($freeze,2);
    }



    //根据员工入职日期 计算额外增加的天数 每5年 加一天
    public function get_year_add_days($staff_info,$current_date = ''){
        $current_tmp = time();
        if(!empty($current_date))
            $current_tmp = strtotime($current_date);
        $hire_tmp = strtotime($staff_info['hire_date']);

        //如果满5的倍数
        $year = date('Y',$current_tmp) - date('Y',$hire_tmp);
        $year_days = $year / 5;
        if($year_days <= 0)
            return 0;

        $year_days = intval($year_days);
        if($year_days == 0)
            return $year_days;
        //判断 月份
        if(date('m',$current_tmp) > date('m',$hire_tmp))
            return $year_days;

        //如果 当前月 是入职月 判断是 15号前后
        if(date('m',$current_tmp) == date('m',$hire_tmp)){
            if(date('d',$hire_tmp) > 15)
                return $year_days - 1;
            return $year_days;
        }

        return $year_days - 1;

    }



    //获取 额度详情信息列表
    public function getAnnualDetail($param){
        $leaveObject = $this->getLeaveObj(enums::LEAVE_TYPE_1);
        return $leaveObject->detailList($param);
    }

    //获取对应假期的额度
    public function getVacationDays($staffId,$leaveType, $extend = []){
        $param['staff_id'] = $staffId;
        $param['leave_type'] = $leaveType;
        $param = array_merge($param,$extend);
        $leaveObject = $this->getLeaveObj($param['leave_type']);
        return $leaveObject->handleSearch($param);
    }

    //非审核通过 撤销操作 返还额度
    public function cancelVacation($auditInfo,$staffInfo,$state, $extend = []){
        $extend['status'] = $state;
        $this->leaveObject = $this->getLeaveObj(intval($auditInfo['leave_type']));
        $this->leaveObject->returnRemainDays($auditInfo['audit_id'],$staffInfo,$extend);
    }

    /**
     * 对应本国的 所有类型 映射类
     * @param int $leaveType
     * @throws ValidationException
     */
    public function getLeaveObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_1:
                $leaveObj =  new AnnualServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_19:
                $leaveObj =  new InternationalServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_4:
                $leaveObj =  new MaternityServer($this->lang,$this->timeZone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }
    //task 初始化额度 调用
    public function getInstanceObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_19://跨国探亲
                $leaveObj = InternationalServer::getInstance($this->lang,$this->timezone);
                break;
            case enums::LEAVE_TYPE_4://产假
                $leaveObj = MaternityServer::getInstance($this->lang,$this->timezone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }

    /**
     * @param
     * @throws ValidationException
     * @return array
     */
    public function saveVacation($param){
        $this->leaveObject = $this->getLeaveObj(intval($param['leave_type']));
        $auditServer = new AuditServer($this->lang, $this->timeZone);
        $staffRe    = new StaffRepository($this->lang);
        $staffInfo  = $staffRe->getStaffPosition($param['staff_id']);
        $leave_lang = AuditRepository::$leave_type;
        $typeData   = $auditServer->staffLeaveType($staffInfo);
        if (empty($typeData)) {
            throw new ValidationException('wrong leave type');
        }
        if (!in_array($param['leave_type'], array_keys($typeData)) && $param['leave_type'] != enums::LEAVE_TYPE_15) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004').$this->getTranslation()->_($leave_lang[$param['leave_type']]));
        }

        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            $audit_id = $this->leaveObject->handleCreate($param);

            //没生成id
            if (empty($audit_id)) {
                throw new ValidationException($this->getTranslation()->_('1009'));
            }

            //非工具操作申请 创建审批相关
            if (empty($param['is_bi'])) {
                $auditServer = new AuditServer($this->lang, $this->timeZone);
                $param['time_out'] = $this->leaveObject->timeOut ?? null;
                $auditServer->saveApproval($audit_id, $param);
            }

            $db->commit();

            $return['data'] = ['leave_day' => $this->leaveObject->leave_day ?? 0];
            return $this->checkReturn($return);
        }catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }




}