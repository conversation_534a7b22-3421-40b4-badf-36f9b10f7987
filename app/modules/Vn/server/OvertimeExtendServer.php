<?php

namespace FlashExpress\bi\App\Modules\Vn\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Server\OvertimeExtendServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SysDepartmentServer;

class OvertimeExtendServer extends GlobalBaseServer
{
    /**
     * @param $staffId
     * @param $date
     * @param $start_time 时间戳
     * @param $end_time
     * @param $type
     * @return array
     */
    public function check_ot_record($staffId,$date,$start_time,$end_time,$type)
    {
        //检查是否有重复记录
        $type_arr = array(1,2,4);
        $exist = HrOvertimeModel::find(
            [
                'conditions' => "state in (1,2) and staff_id = {$staffId}  and date_at = '{$date}' and type in({type_arr:array})",
                'bind'       => [
                    'type_arr'=> $type_arr,
                ],
                'columns' => 'overtime_id'
            ]
        )->toArray();
        if(!empty($exist)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('5102'));
        }

        return $this->checkReturn([]);
    }

    /**
     * 新增的逻辑验证
     * @param $param
     * @param $user_info
     * @return array
     */
    public function extend_check($param, $user_info)
    {
        $config_hour = $this->config->application->add_hour;
        $staff_id = $user_info['staff_info_id'];
        $date = date('Y-m-d',strtotime($param['date_at']));

        //刘佳雪需求 https://l8bx01gcjr.feishu.cn/docs/doccnb3dtAR0TdQySz2RTsA26ge#ky8QZy
        //全部向后后推9.5小时
        $add_hour = 9.5;

        //新增需求 ffm 非总部  职级小于等于 15 为9小时 https://flashexpress.feishu.cn/docx/ML9rdSJKkowIlsxQBdtcQnB1nFd
        $ffmFlag = $this->ffmCondition($user_info);
        if($ffmFlag){
            $add_hour = 9;
        }

        //获取请假记录 如果加班日当天 存在下午请假
        $leave_re = new AuditRepository($this->lang);
        $leave_info = $leave_re->get_leave_date($staff_id,$date,$date);
        //如果是请上午假 加5小时 其他情况不让申请ot 休息日类型假期 剔除
        if(!empty($leave_info) && $leave_info[0]['leave_type'] != 15){
            if($leave_info[0]['type'] != 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_leave_limit'));
            }
            $add_hour = 4.5;
        }

        //没有班次信息 不让申请
        if(empty($param['shift_info'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('no_shift_notice'));
        }

        //如果 没打上班卡 不让申请
        $att_info = StaffWorkAttendance::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
        if(empty($att_info)){
            //没上班卡 判断是否出差 取出差打卡 上班卡信息
            $att_info = StaffAuditReissueForBusinessModel::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
            if(empty($att_info))
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_att_start'));
            $att_info = $att_info->toArray();
            $att_info['started_at'] = $att_info['start_time'];
        }else{
            $att_info = $att_info->toArray();
        }

        if(empty($att_info['started_at'])){
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_att_start'));
        }

        //去秒数
        $att_info['started_at'] = date('Y-m-d H:i:00',strtotime("{$att_info['started_at']}"));

        //通过日期判断新旧班次
        $start = $param['shift_info']['start'];
        $end = $param['shift_info']['end'];
        //跟班次比对 如果是迟到 加班开始时间 应该在 迟到小时+1小时 时间整点
        $shift_start_time = strtotime("{$date} {$start}");
        $shift_end_time = strtotime("{$date} {$end}");
        if($start > $end){//跨天 加一天
            $shift_end_time += 24 * 3600;
        }
        $card_time = strtotime($att_info['started_at']) - $this->second + ($config_hour * 3600);
        //没迟到  取班次时间整点 加对应的小时数
        $limit_start = date('Y-m-d H:i:s',$shift_start_time + ($add_hour * 3600));
        if($card_time > $shift_start_time){//如果迟到 取打卡时间 加1小时 再加上对应的小时数
            if($ffmFlag){//直接加9。5
                $card_i = date("i",$card_time);//上班打卡时间[向前]取最近的半点或整点
                $card_i = $card_i >= 30 ? '30' : '00';
                $startTime = date("Y-m-d H:{$card_i}:00",$card_time);
                $limit_start = date("Y-m-d H:i:00",strtotime($startTime) + ($add_hour * 3600) + (0.5 * 3600));
            }else{
                $shift_i = date("i",$shift_start_time);//取班次的分钟 产品说只有 00点 和 30 点
                $limit_start = date("Y-m-d H:{$shift_i}:00",$card_time + 3600);
                $limit_start = date('Y-m-d H:i:s',strtotime($limit_start) + ($add_hour * 3600));
            }
        }

        $ot_start_time = date('Y-m-d H:i:s',strtotime($param['start_time']));
        $l1 = date('Y-m-d H:i:s',$shift_start_time);
        $l2 = date('Y-m-d H:i:s',$card_time);
        $this->logger->write_log("{$staff_id} {$ffmFlag} add_hour {$add_hour},shift_start_time {$l1},card_time(-59) {$l2},limit_start {$limit_start},ot_start_time {$ot_start_time} ",'info');
        /*刘佳雪需求*/
        if(!isset($param['is_bi']) or empty($param['is_bi'])) {
            if ($ot_start_time < $limit_start)
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_forbidden'));
        }
        //开始时间 不能 早于 班次结束时间
        $datetime_end = date('Y-m-d H:i:s', $shift_end_time);
        if($ot_start_time < $datetime_end){
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_end_time_forbidden'));
        }

        return $this->checkReturn([]);
    }

    //FFM & 职级<=F15 & 所属网点≠ head Office： 返回true
    public function ffmCondition($staffInfo){
        if(empty($staffInfo)){
            return false;
        }
        if($staffInfo['job_title_grade_v2'] > 15){
            return false;
        }
        if($staffInfo['sys_store_id'] == enums::HEAD_OFFICE_ID){
            return false;
        }
        //ffm 部门 id 20001
        $departmentServer = new SysDepartmentServer($this->lang, $this->timezone);
        $allIds = $departmentServer->getDepartmentIds(20001);
        if(!in_array($staffInfo['node_department_id'], $allIds)){
            return false;
        }
        return true;
    }



}