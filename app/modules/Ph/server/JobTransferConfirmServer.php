<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;


use FlashExpress\bi\App\Enums\JobTransferConfirmEnums;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\Enums\PdfEnums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Repository\JobtransferRepository;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Server\JobTransferConfirmServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\StaffServer;


//转岗确认
class JobTransferConfirmServer extends GlobalBaseServer
{
    public static $validateConfirm = [
        'id'       => 'Required|Int',
        'state'    => 'Required|Int',
        'sign_url' => 'IfIntEq:state,2|Required|StrLenGe:1',
        'version'  => 'IfIntEq:state,2|Required|StrLenGe:1',
    ];

    /**
     * @description 获取转岗详情
     * 1. 部门、职位、职级、工作网点
     * 2. 薪资
     * 3. Short Notice时间
     * 4. 年假天数
     *
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function getConfirmDetail($params)
    {
        $id               = $params['id'];
        $staffInfoId      = $params['staff_info_id'];
        $templateCnf      = $params['template_cnf'] ?? PdfEnums::RESPONSE_TYPE_IMAGE;

        //获取转岗信息
        $jobTransferRepo = new JobtransferRepository($this->timeZone);
        $transferInfo = $jobTransferRepo->getJobTransferDetailById($id);
        if (empty($transferInfo)) {
            return [];
        }

        //获取页眉页脚
        $queryParams = [
            'staff_info_id' => $staffInfoId,
            'department_id' => $transferInfo['after_department_id'],
        ];
        $templateCnf = (new PdfHelperServer())->getHeaderAndFooter($queryParams, $templateCnf);

        //查询职位名称 ids
        $sysObj       = new SysListRepository();
        $positionIds  = [$transferInfo['current_position_id'], $transferInfo['after_position_id']];
        $positionData = $sysObj->getPositionList(['ids' => $positionIds]);
        $positionData = array_column($positionData, 'name', 'id');

        //查询网点名称 ids
        $storeIds     = [$transferInfo['current_store_id'], $transferInfo['after_store_id']];
        $storeData    = $sysObj->getStoreList(['ids' => $storeIds]);
        $storeData    = array_column($storeData, 'name', 'id');
        $transferReverseParams = [
            'position_data'       => $positionData,
        ];
        //获取确认状态
        $confirmState = JobTransferConfirmEnums::getCodeTxtMap();

        //如果已经转岗确认了，获取固化的数据
        if ($transferInfo['confirm_state'] == JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM) {
            //获取被转岗人信息
            $staffService = new StaffServer();
            $staffInfo    = $staffService->getStaffInfoSpecColumns($staffInfoId,
                "h.staff_info_id, 
                h.name as staff_name,
                h.identity,
                h.formal,
                date_format(h.hire_date,'%Y-%m-%d') hire_date");

            //没转岗确认实时取薪资、年假天数、short Notice
            $transferDetail = $this->getTransferPendingConfirmInfo($transferInfo, $staffInfo);
            $afterDate      = $transferInfo['after_date'] ?? '';
        } else {
            $transferData   = json_decode($transferInfo['confirm_content'], true);
            $staffInfo      = $transferData['staff_info'] ?? [];
            $transferDetail = $transferData['transfer_info'] ?? [];
            $afterDate      = $transferData['persist_info']['after_date'] ?? ''; //确认完成需要取固化的日期
        }

        //将转岗确认的数据进行排序
        $sortTransferDetail = $transferDetail['salary'] ?? [];
        if (!empty($sortTransferDetail)) {
            $this->sortArrayRecursively($sortTransferDetail);
        }

        $result = [
            //转岗日期
            'after_date'             => $afterDate,
            //工号
            'staff_info_id'          => $staffInfoId,
            //姓名
            'staff_name'             => $staffInfo['staff_name'] ?? '',
            //入职日期
            'hire_date'              => $staffInfo['hire_date'] ?? '',
            //员工签名
            'staff_sign_url'         => $transferInfo['sign_url'] ?? '',
            //转岗前后工作地点
            'before_store_name'      => $storeData[$transferInfo['current_store_id']] ?? '',
            'after_store_name'       => $storeData[$transferInfo['after_store_id']] ?? '',
            //确认状态
            'confirm_state'          => $transferInfo['confirm_state'] ?? '',
            'confirm_state_label'    => $this->getTranslation()->_($confirmState[$transferInfo['confirm_state']]),
            //确认单
            'confirm_list'           => $this->formatterDate($transferDetail, $transferReverseParams),
            //签字日期
            'confirm_date'           => !empty($transferInfo['confirm_date']) ? $transferInfo['confirm_date'] : '',
            //用于在转岗确认时，对比确认的数据版本与获取的转岗确认的数据的版本是否一致
            'version'                => md5(json_encode($sortTransferDetail, JSON_UNESCAPED_UNICODE)),
            'type'                   => $transferInfo['type'],
            'state'                  => $transferInfo['state'],
            //For 测试：给测试同学展示薪资详情
            'salary_data'            => RUNTIME == 'dev' ? $transferDetail['salary'] ?? [] : [],
        ];
        $result = array_merge($result, $templateCnf);

        return ['data' => $result];
    }

    /**
     * 获取转岗确认相关信息
     * @param $transfer_info
     * @param $staff_info
     * @return array
     * @throws \Exception
     */
    public function getTransferPendingConfirmInfo($transfer_info, $staff_info): array
    {
        //薪资
        $queryParams = ['job_transfer_id' => $transfer_info['id']];
        if ($transfer_info['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
            $queryParams['is_special_batch_transfer'] = true;
        }
        $salaryInfo = $this->getTransferSalaryById($queryParams);

        return [
            'basic' => [
                [
                    'before' => $transfer_info['current_position_id'],
                    'after'  => $transfer_info['after_position_id'],
                    'name'   => 'Position',
                ],
                [
                    'before' => $transfer_info['current_job_title_grade'],
                    'after'  => $transfer_info['after_job_title_grade'],
                    'name'   => 'Job Grade',
                ],
            ],
            //薪资项3项：基本薪资、无税津贴、其他无税津贴
            'salary' => [
                [
                    'before' => $this->responseSalaryItem($salaryInfo, 'base_salary_before'),
                    'after'  => $this->responseSalaryItem($salaryInfo, 'base_salary_after'),
                    'name'   => 'Basic Pay',
                ],
                [
                    'before' => $this->responseSalaryItem($salaryInfo, 'deminimis_benefits_before'),
                    'after'  => $this->responseSalaryItem($salaryInfo, 'deminimis_benefits_after'),
                    'name'   => 'De Minimis',
                ],
                [
                    'before' => $this->responseSalaryItem($salaryInfo, 'other_non_taxable_allowance_before'),
                    'after'  => $this->responseSalaryItem($salaryInfo, 'other_non_taxable_allowance_after'),
                    'name'   => 'Other Non-Taxable',
                ],
                [
                    'before' => $this->responseSalaryItem($salaryInfo, ['base_salary_before', 'deminimis_benefits_before', 'other_non_taxable_allowance_before']),
                    'after'  => $this->responseSalaryItem($salaryInfo, ['base_salary_after', 'deminimis_benefits_after', 'other_non_taxable_allowance_after']),
                    'name'   => 'Total',
                ],
            ],
        ];
    }

    /**
     * @param $salary_info
     * @param $column
     * @return int|null
     */
    public function responseSalaryItem($salary_info, $column)
    {
        if (is_array($column)) {
            $total = 0;
            foreach ($column as $v) {
                if (isset($salary_info[$v])) {
                    $total += $salary_info[$v];
                }
            }
            return sprintf("%s %s", intval($total), 'PHP');
        } else {
            return isset($salary_info[$column]) ? sprintf("%s %s", intval($salary_info[$column]), 'PHP'): '';
        }
    }

    /**
     * 转义数据
     * @param array $transferDetail
     * @param array $params
     * @return array
     */
    private function formatterDate(array $transferDetail, array $params): array
    {
        if (empty($transferDetail['basic'])) {
            return [];
        }

        foreach ($transferDetail['basic'] as &$value) {
            if ($value['name'] == 'Position') {
                $value['after'] = $params['position_data'][$value['after']] ?? '';
                $value['before'] = $params['position_data'][$value['before']] ?? '';
            }
            if ($value['name'] == 'Job Grade') {
                $value['after'] = sprintf('F%d', $value['after']);
                $value['before'] = sprintf('F%d', $value['before']);
            }
        }
        $transferDetailList = array_merge(...array_values($transferDetail));
        return array_values(array_filter($transferDetailList));
    }

    /**
     * 获取要固化的转岗确认详情
     * @param $params
     * @param $jobTransferInfo
     * @return array
     * @throws ValidationException
     * @throws \Exception
     */
    public function getPersistConfirmBaseInfo($params, $jobTransferInfo): array
    {
        $state   = $params['state'];
        $version = $params['version'] ?? '';

        $staffService = new StaffServer();
        $staffInfo    = $staffService->getStaffInfoSpecColumns($jobTransferInfo->staff_id,
            "h.staff_info_id, 
                h.name as staff_name,
                h.identity,
                h.formal,
                date_format(h.hire_date,'%Y-%m-%d') hire_date");

        //获取确认详情
        //将转岗确认的数据进行排序
        $transferDetail     = $this->getTransferPendingConfirmInfo($jobTransferInfo->toArray(), $staffInfo);

        //确认同意需要验证版本
        if (!empty($version) && $state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS) {
            $sortTransferDetail = $transferDetail['salary'];
            $this->sortArrayRecursively($sortTransferDetail);
            if ($version != md5(json_encode($sortTransferDetail, JSON_UNESCAPED_UNICODE))) {
                throw new ValidationException($this->getTranslation()->_('err_msg_job_transfer_confirm_version_not_latest'), ErrCode::JOB_TRANSFER_CONFIRM_DATE_VERSION_NOT_LATEST);
            }
        }
        return [
            'transfer_info' => $transferDetail,
            'staff_info'    => $staffInfo,
            'persist_info'  => [
                'after_date' => $jobTransferInfo->after_date
            ],
        ];
    }

    /**
     * @description 获取待确认转岗信息
     * @param $staff_info_id
     * @return mixed
     */
    public function getPendingConfirmInfo($staff_info_id)
    {
        $checkStaffIds = [$staff_info_id];
        $staffInfo     = (new StaffServer())->getStaffById($staff_info_id);

        //如当前账号是子账号
        if (!empty($staffInfo) && $staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            //获取主账号信息
            $supportInfo = HrStaffApplySupportStoreModel::findFirst("sub_staff_info_id = $staff_info_id");
            if (!empty($supportInfo)) {
                $checkStaffIds[] = $supportInfo['staff_info_id'];
            }
        }
        //一线、非一线待转岗、待确认
        //特殊批量转岗，转岗成功、待确认 需拦截下班打卡
        return JobTransferModel::findFirst([
            'conditions' => 'staff_id in({staff_id:array}) and (type in({normal_type:array}) and state = :normal_state: and 
                confirm_state = :confirm_state: or type = :special_type: and state = :special_state: and confirm_state = :confirm_state:)',
            'bind'       => [
                'staff_id'      => $checkStaffIds,
                'normal_type'   => [JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE, JobTransferEnums::JOB_TRANSFER_TYPE_NOT_FRONT_LINE],
                'normal_state'  => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED,
                'confirm_state' => JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM,
                'special_type'  => JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL,
                'special_state' => JobTransferModel::JOBTRANSFER_STATE_TRANSFERED,
            ],
        ]);
    }

}