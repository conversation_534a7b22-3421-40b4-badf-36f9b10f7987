<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use App\Country\Tools;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\MessageWarningModel;
use FlashExpress\bi\App\Models\coupon\MessageContentModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\JobTitleRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\WorkflowServer;
use FlashExpress\bi\App\Server\MessageServer as GlobalBaseServer;

class MessageServer extends GlobalBaseServer
{

    protected $re;
    public $timezone;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang, $timezone);
        $this->timezone = $timezone;
    }

    protected function dealMessageRead($msg_id, $staff_info_id, $waring_id){
        //如果保存成功 设置为已读
        $db = $this->getDI()->get('db_coupon');
        $db->updateAsDict("message_courier", ['read_state' => 1,'top_state'=> 0], ['conditions' => "id='" .$msg_id . "'"]);

        // 消息已读
        $server = new BackyardServer($this->lang, $this->timezone);
        $server->addHaveReadMsgToMns($msg_id);

        // 签字日志
        $sign_log_data = [
            'staff_info_id'      => $staff_info_id,
            'kit_id'             => $msg_id,
            'message_warning_id' => $waring_id,
            'is_auto_sign'       => 0,
        ];
        $this->getDI()->get('db')->insertAsDict('message_warning_sign_log', $sign_log_data);
        return true;
    }

    protected function updateWarningInfo($warning_id,$updateInfo){
        $model = new BaseRepository($this->lang);
        return $model->updateInfoByTable('message_warning', 'id', $warning_id, $updateInfo);
    }





    //电子警告书 电子签名 保存图片

    /**
     * @throws ValidationException|\ReflectionException
     */
    public function sign_for_warning($param)
    {
        $param = filter_param($param);
        // 更新签名图片
        //判断身份 后 发送给上级消息 或 证人消息

        $warningInfo = $this->getWarningMessage($param['staff_info_id'], $param['msg_id']);

        switch ($warningInfo['role']) {
            case self::MSG_STAFF_TYPE_STAFF:
                if (!empty($warningInfo['img_url'])) { //被警告人已经签字，则提示勿重复提交
                    throw new ValidationException($this->getTranslation()->_('5202'));
                }
                $staffServer = new StaffServer();
                $staffInfo   = $staffServer->getStaffInfo(['staff_info_id' => $param['staff_info_id']]);
                if (empty($staffInfo)) {
                    throw new ValidationException($this->getTranslation()->_('manger_no_exists'));
                }
                // 获取上级
                $staffMangerInfo         = $staffServer->getStaffInfo(['staff_info_id' => $staffInfo['manger']]);
                $updateInfo['img_url']   = $param['url'];         // 更新签名;
                $updateInfo['sign_time'] = gmdate("Y-m-d H:i:s"); // 签名时间;
                //签字状态
                if (!empty($warningInfo['superior_img'])  && (empty($warningInfo['is_transfer']) || !empty($warningInfo['witness1_img']))) {
                    $updateInfo['sign_state'] = MessageWarningModel::SING_STATE_COMPLETED;
                }
                // 消息更新为已读
                if (!$warningInfo['superior_kit_id']) {
                    // 如果没有发送给上级 发送上级给消息
                    $updateInfo['superior_id']   = $staffMangerInfo['staff_info_id'];
                    $updateInfo['superior_name'] = $staffMangerInfo['name'];
                    $this->sendWarningMessage($staffMangerInfo['staff_info_id'], $warningInfo['id'],
                        $warningInfo['role']);
                }
                break;
            case self::MSG_STAFF_TYPE_SUPERIOR:
                if (!empty($warningInfo['superior_img'])) { //上级已经签字，则提示勿重复提交
                    throw new ValidationException($this->getTranslation()->_('5202'));
                }

                $updateInfo['superior_img'] =  $param['url'];
                $updateInfo['superior_remark'] =  $param['superior_remark']??'';
                //签字状态
                if (!empty($warningInfo['img_url']) && empty($warningInfo['is_transfer'])) {
                    $updateInfo['sign_state'] = MessageWarningModel::SING_STATE_COMPLETED;
                }
                //是否需要证人
                $isNeedWitness = $this->checkIsNeedWitness($warningInfo);
                if ($isNeedWitness) {
                    if (empty($param['witness1_staff_id'])) {
                        throw new ValidationException($this->getTranslation()->_('miss_args'));
                    }
                }


                if (!empty($param['witness1_staff_id'])) {
                    //证人列表
                    $witness1 = $this->sign_warning_witness_list($param);
                    $sign_warning_witness_list_arr = array_merge($witness1['witness_staff_list'],$witness1['default_witness_supplement_staff_list']);
                    
                    if (!in_array($param['witness1_staff_id'],array_column($sign_warning_witness_list_arr,'staff_info_id'))) {
                        throw new ValidationException($this->getTranslation()->_('witness_info_error_ph'));
                    }
                    $witnessInfo = (new StaffServer())->getStaffById($param['witness1_staff_id']);
                    $updateInfo['witness1_id']   = $param['witness1_staff_id'];
                    $updateInfo['witness1_name'] =  $witnessInfo['name']??'';
                    // 发送证人消息
                    $this->sendWarningMessage($param['witness1_staff_id'], $warningInfo['id'], $warningInfo['role']);
                }
                break;
            case self::MSG_STAFF_TYPE_WITNESS_1:
                if (!empty($warningInfo['witness1_img'])) {
                    throw new ValidationException($this->getTranslation()->_('5202'));
                }
                //签字状态
                if (!empty($warningInfo['img_url']) && !empty($warningInfo['superior_img'])) {
                    $updateInfo['sign_state'] = MessageWarningModel::SING_STATE_COMPLETED;
                }
                $updateInfo['witness1_img'] = $param['url'];
                break;
        }

        //更新警告信息
        isset($updateInfo) && $this->updateWarningInfo($warningInfo['id'], $updateInfo);
        // 消息更新为已读
        $this->dealMessageRead($param['msg_id'], $param['staff_info_id'], $warningInfo['id']);

        return true;
    }

    /**
     * 上级验证证人
     *
     * 发送给上级的消息ID
     * @param  $msg_id
     * 证人ID
     * @param $staffId
     * @return array
     *
     */
    public function witenessInfo($msg_id, $staffId)
    {
        $message = MessageCourierModel::findFirst([
            "conditions" => " id = :id:",
            'bind' => ['id' => $msg_id],
        ]);
        if(!$message){
            return $this->checkReturn(-3, $this->getTranslation()->_('message_no_exists'));
        }
        $message = $message->toArray();
        $warningInfo = $this->getWarningMessage($message['staff_info_id'], $msg_id);

        if ($staffId == $warningInfo['staff_info_id']) {
            return $this->checkReturn(-3, $this->getTranslation()->_('witness_no_warning_staff'));
        }

        //获取被警告人网点
        $staff_store = HrStaffInfoModel::findFirst([
            "conditions" => " staff_info_id = :staff_info_id:",
            'bind' => ['staff_info_id' => $warningInfo['staff_info_id']],
            'columns' => ['sys_store_id']
        ]);
        $sys_store_id = '';
        if ($staff_store) {
            $sys_store_id = $staff_store->sys_store_id;

        }

        //获取证人信息
        $staffInfo = HrStaffInfoModel::findFirst([
            "conditions" => " staff_info_id = :warning_staff_id:",
            "bind" => ['warning_staff_id' => $staffId],
        ]);
        if (!$staffInfo) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
        }
        $staffInfo = $staffInfo->toArray();

        //获取证人角色
        $positions = HrStaffInfoPositionModel::find([
            "conditions" => " staff_info_id = :warning_staff_id:",
            "bind" => ['warning_staff_id' => $staffId],
        ])->toArray();
        $positionsArr = array_column($positions, 'position_category');

        $er_job_ids = (new SettingEnvServer())->getSetVal('er_job_ids');
        $er_job_ids = explode( ',', $er_job_ids);

        // 证人与被警告人所在同一个网点
        // 或证人是外协员工
        // 或证人职位er officer的员工
        // 或HRBP
        if ($staffInfo['state'] == 1 && (
            $staffInfo['sys_store_id'] == $sys_store_id && $staffInfo['formal'] == 0
            || in_array($staffInfo['job_title'], $er_job_ids)
            || in_array(68, $positionsArr)
            )
        ) {
            return $this->checkReturn(["data" => [
                "staff_id" => $staffInfo['staff_info_id'],
                "name" => $staffInfo['name'],
            ]]);
        }
        return $this->checkReturn(-3, $this->getTranslation()->_('witness_info_error_ph'));
    }

    /**
     * 获取是否需要证人
     *
     * 当登录人是被警告人的上级的时候,可能不需要证人(被警告人1天内签字)
     * 其他情况都需要证人
     *
     * @param array $paramIn
     * @param $staffId
     * @return array
     */
    public function getWarningMessageInfo(array $paramIn, $staffId): array
    {
        $msgId = $this->processingDefault($paramIn, 'msg_id');

        //获取警告书
        $warningInfo  = $this->getWarningMessage($staffId, $msgId);

        if (empty($warningInfo)) {
            throw new ValidationException($this->getTranslation()->_('message_no_exists'));
        }
        $result = ['is_need_witeness' => true];
        //当前登录是
        if ($warningInfo['staff_info_id'] == $staffId || $warningInfo['witness1_id'] == $staffId) {
            $result['is_need_witeness'] = false;
            return $result;
        }
        return ['is_need_witeness' => $this->checkIsNeedWitness($warningInfo)];
    }

    /**
     * 是否需要证人
     * @param $warningInfo
     * @return bool
     */
    protected function checkIsNeedWitness($warningInfo): bool
    {
        return boolval($warningInfo['is_transfer']);
    }


    /**
     * @throws \ReflectionException
     * @throws ValidationException
     */
    public function get_sign_warning_pdf_data($param)
    {
        $param         = filter_param($param);
        $msg_id        = $param['msg_id'] ?? '';
        $staff_info_id = $param['staff_info_id'] ?? '';
        if (empty($msg_id) || empty($staff_info_id)) {
            throw new ValidationException($this->t->_('miss_args'));
        }
        // 获取警告书信息
        $warningInfo = $this->getWarningMessage($staff_info_id, $msg_id);
        if (empty($warningInfo)) {
            throw new ValidationException($this->t->_('data_error'));
        }
        $warning_id = $warningInfo['id'];
        $staffInfo  = (new StaffRepository())->getStaffInfoAllOne($warningInfo['staff_info_id']);

        if(!empty($warningInfo['department_id'])) {
            $staffInfo['department_name'] = (new DepartmentRepository($this->lang))->getDepartmentNameById($warningInfo['department_id']);
        }

        if(!empty($warningInfo['job_title'])) {
            $jobTitle = JobTitleRepository::getJobTitleInfo($warningInfo['job_title']);
            $staffInfo['job_title_name'] = !empty($jobTitle) ? $jobTitle->job_name : $staffInfo['job_title_name'];
        }

        if(!empty($warningInfo['store_id'])) {
            $storeName = SysStoreRepository::getStoreName($warningInfo['store_id']);
            $staffInfo['store_name'] = !empty($storeName) ? $storeName : $staffInfo['store_name'];
        }

        $staffMangerInfo = (new StaffRepository())->getStaffInfoAllOne($staffInfo['manger']);
        if(!empty($warningInfo['witness1_id'])){
            $witnessInfo = (new StaffRepository())->getStaffInfoAllOne($warningInfo['witness1_id']);
        }
        $length          = 5;
        if ($warning_id > 99999) {
            $length = strlen($warning_id);
        }
        $warningInfo['created_at']                = show_time_zone($warningInfo['created_at']);
        $warningType                              = $warningInfo['warning_type'] ?? 0;
        $warningInfo["numbering"]                 = MessageWarningModel::WARNING_TYPE_NUMBERING_PH[$warningType] ?? "";
        $warningInfo['number']                    = str_pad($warningInfo['number'], $length, "0", STR_PAD_LEFT);
        $warningInfo['year']                      = date("Y", strtotime($warningInfo['created_at']));
        $warningInfo['date']                      = date("m", strtotime($warningInfo['created_at'])) . "/" . date("d",
                strtotime($warningInfo['created_at']));
        $warningInfo['job_title_name']            = $staffInfo['job_title_name'] ?? '';
        $warningInfo['superior_job_name']         = $staffMangerInfo['job_title_name'] ?? '';
        $warningInfo['witness1_job_name']         = $witnessInfo['job_title_name'] ?? '';
        $warningInfo['storeName']                 = $staffInfo['store_name'];
        $warningInfo['hire_date']                 = date('m/d/Y',strtotime($staffInfo['hire_date']));
        $warningInfo['department']                = $staffInfo['department_name'];
        $warningInfo['superior_name']             = $staffMangerInfo['name'] ?? '';
        $warningInfo['current_param_msg_is_read'] = MessageCourierModel::READ_STATE_UNREAD;
        $message                                  = MessageCourierModel::findFirst([
            "conditions" => " id = :id:",
            'bind'       => ['id' => $msg_id],
        ]);
        $is_fix_read = false;
        switch ($warningInfo['role']) {
            case self::MSG_STAFF_TYPE_STAFF:
                if (!empty($warningInfo['img_url'])) { //被警告人已经签字，则提示勿重复提交
                    $is_fix_read = true;
                }
                break;
            case self::MSG_STAFF_TYPE_SUPERIOR:
                if (!empty($warningInfo['superior_img'])) { //上级已经签字，则提示勿重复提交
                    $is_fix_read = true;
                }
                break;
            case self::MSG_STAFF_TYPE_WITNESS_1:
                if (!empty($warningInfo['witness1_img'])) {
                    $is_fix_read = true;
                }
                break;
        }
        if ($message) {
            if($message->read_state != MessageCourierModel::READ_STATE_HAS_READ && $is_fix_read){
                $message->read_state = MessageCourierModel::READ_STATE_HAS_READ;
                $message->top_state = 0;
                $message->save();
            }
            if($message->read_state == MessageCourierModel::READ_STATE_HAS_READ){
                $warningInfo['current_param_msg_is_read'] = MessageCourierModel::READ_STATE_HAS_READ;
            }
        }

        //获取公司信息
        $client = new ApiClient('hcm_rpc', '', 'get_warning_message_company_info');
        $client->setParams(['staff_info_id' => $staffInfo['staff_info_id']]);
        $res = $client->execute();
        return array_merge($warningInfo, $res['result']['data'] ?? []);
    }

    /**
     * 警告书签字证人下拉列表逻辑
     * @param $param
     * @return array
     * @throws ValidationException
     * @throws \ReflectionException
     */
    public function sign_warning_witness_list($param)
    {
        $return_data = ['default_witness_staff_list' => [], 'witness_staff_list' => []];
        $param       = filter_param($param);
        $message_model = Tools::reBuildCountryInstance(new MessageServer($this->lang, $this->timeZone), [$this->lang, $this->timeZone]);
        $warningInfo = $message_model->getWarningMessage($param['staff_info_id'], $param['msg_id']);
        if (empty($warningInfo)) {
            throw new ValidationException($this->t->_('miss_args'));
        }
        // 全节点的员工
        $all_node_staff_id = array_values(array_filter([
             intval($warningInfo['staff_info_id']),
             intval($warningInfo['superior_id']),
             intval($warningInfo['witness1_id']),
        ]));

        $staffInfoData            = HrStaffInfoModel::findFirst([
            "staff_info_id = :staff_info_id:",
            "bind" => [
                "staff_info_id" => $warningInfo['staff_info_id'],
            ],
        ]);

        // 这块逻辑公共的
        // 与被警告员工同网点员工 (非总部 非个人代理 在职、非子账号、不含外协)
        if ($staffInfoData['sys_store_id'] != '-1' && !empty($staffInfoData['sys_store_id'])) {
            $staff_common = $this->getStaffByCondition('sys_store_id = :store_id: and staff_info_id not in ({all_node_staff_id:array})',
                ['all_node_staff_id' => $all_node_staff_id, 'store_id' => $staffInfoData['sys_store_id']]);

        }

        $settingEnvServer       = new SettingEnvServer();
        $without_witness_staffs = $settingEnvServer->getSetVal('sign_warning_not_default_witness_staff_ids', ',');


        // 这块逻辑公共的
        //ER[80]角色员工
        $staff_er = $this->getOnJobERStaffs($all_node_staff_id);

        $staffInfoData = $staffInfoData ? $staffInfoData->toArray() : [];

        // 20108需求 默认证人为空后的补充逻辑 
        $hrbps = (new WorkflowServer($this->lang, $this->timeZone))->findHRBP($staffInfoData['node_department_id'],['store_id' => $staffInfoData['sys_store_id']]);
        $hrbps = explode(',', $hrbps);
        $default_witness_supplement_staff_list = [];
        if ($hrbps) {
            // HRBP
            $default_witness_supplement_staff_list      = $this->getStaffByCondition('staff_info_id in ({staff_info_id:array}) and staff_info_id not in ({all_node_staff_id:array})',
                [
                    'all_node_staff_id' => $all_node_staff_id,
                    'staff_info_id'     => $hrbps,
                ]);
            if (count($default_witness_supplement_staff_list) <= 1){
                // ER
                $default_witness_supplement_staff_list = array_merge($default_witness_supplement_staff_list,$this->getOnJobERStaffs($all_node_staff_id));
            }
        }
        $return_data['default_witness_supplement_staff_list'] = array_values(array_filter(array_column($default_witness_supplement_staff_list,null,'staff_info_id')));
        
        // 1、被警告员工属于Network Management[125]及下级
        $departmentIds = (new DepartmentRepository($this->lang))->getDepartIds(enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH);
        if (in_array($staffInfoData['node_department_id'], $departmentIds)){
            $setting_env                           = new SettingEnvServer();
            $sign_warning_witness_list_default     = $setting_env->getSetVal('sign_warning_witness_list_default_phnw');
            if(!empty($sign_warning_witness_list_default)){
                $default_witness_staff = $this->getStaffByCondition('staff_info_id in ({staff_info_id:array}) and staff_info_id not in ({all_node_staff_id:array})',['all_node_staff_id'=>array_values(array_merge($all_node_staff_id,$without_witness_staffs)),'staff_info_id'=>explode(',',$sign_warning_witness_list_default)]);
                $staff_config = $this->getStaffByCondition('staff_info_id in ({staff_info_id:array}) and staff_info_id not in ({all_node_staff_id:array})',['all_node_staff_id'=>$all_node_staff_id,'staff_info_id'=>explode(',',$sign_warning_witness_list_default)]);
                if(!empty($default_witness_staff)){
                    $default_witness_staff_list[0] =  $default_witness_staff[array_rand($default_witness_staff)];
                    $default_witness_staff_list[0]['type'] = 1;
                }
            }
            $return_data['default_witness_staff_list'] = $default_witness_staff_list ?? [];
            $return_data['witness_staff_list'] = array_values(array_column(array_filter(array_merge($staff_config ?? [],
                $staff_common ?? [], $staff_er ?? [])), null, 'staff_info_id'));

            // 提示语 证人可选择同网点员工、NetworkQC、ER员工
            $return_data['prompt'] = $this->t->_('warning_witness_store_networkqc_er_staff');
            return  $return_data;
        }

        //2. 被警告员工属于其他
        //被警告员工上上级，可修改为其他人
        $staff_manger = (new StaffServer())->getStaffInfo(['staff_info_id' => $staffInfoData['manger']], 'manger');
        $staff_manger_manger = $this->getStaffByCondition('staff_info_id = :staff_info_id: and staff_info_id not in ({all_node_staff_id:array})',
            ['all_node_staff_id' => $all_node_staff_id, 'staff_info_id' => $staff_manger['manger']]);
        if ($staff_manger_manger) {
            $staff_manger_manger[0]['type'] = 1;
            if (!in_array($staff_manger['manger'], $without_witness_staffs)) {
                $default_witness_staff_list            = $staff_manger_manger;
                $default_witness_staff_list[0]['type'] = 1;
            }
        }

        //bp
        $hrbps = (new WorkflowServer($this->lang, $this->timeZone))->findHRBP($staffInfoData['node_department_id'],
            ['store_id' => $staffInfoData['sys_store_id']]);
        $hrbps = explode(',', $hrbps);
        if ($hrbps) {
            $staff_hrbp = $this->getStaffByCondition('staff_info_id in ({staff_info_id:array}) and staff_info_id not in ({all_node_staff_id:array}) ',['all_node_staff_id'=>array_values(array_merge($all_node_staff_id,$without_witness_staffs)),'staff_info_id'=>$hrbps]);
        }

        $return_data['default_witness_staff_list'] = $default_witness_staff_list ?? [];
        $return_data['witness_staff_list'] = array_values(array_column(array_filter(array_merge($staff_manger_manger??[],$staff_hrbp ?? [],
            $staff_common ?? [], $staff_er ?? [])), null, 'staff_info_id'));

        // 提示语 证人可选择同网点员工、或是ER、BP角色的在职员工
        $return_data['prompt'] = $this->t->_('warning_witness_store_er_bp_staff');
        return $return_data;
    }

    /**
     * 尝试
     * 根据员工ID和消息ID
     * 获取警告信
     * 并且返回当前身份
     *
     * @param $staffId
     * @param $msgId
     *
     */
    public function getWarningMessage($staffId, $msgId)
    {
        $warningInfo = $this->getWarningMessageFromQueryList($staffId, $msgId);
        $is_need_sign = true;
        //如果根据msg_id查不到警告书内容，则根据related_id获取
        //并将msg_id回写到message_warning表
        if (!$warningInfo) {
            $warning = $is_need_sign = false;

            $signHistory = $this->getWarningTransferInfo($staffId, $msgId);
            if(!empty($signHistory)) {
                $warningInfo = MessageWarningModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $signHistory['message_warning_id']],
                ]);
                if(!empty($warningInfo)) {
                    $warningInfo = $warningInfo->toArray();
                    $warningInfo['role'] = '';
                    $warningInfo['is_need_sign'] = $is_need_sign;
                    return $warningInfo;
                }
            }

            $message_courier = MessageCourierModel::findFirst([
                'conditions' => "id = :id:",
                'bind'       => ['id' => $msgId],
            ]);
            if ($message_courier) {
                $message_content = MessageContentModel::findFirst([
                    'conditions' => "id = :id:",
                    'bind'       => ['id' => $message_courier->message_content_id],
                ]);
                if ($message_content && !empty($message_content->related_id)) {
                    $warning = MessageWarningModel::findFirst([
                        'conditions' => "id = :id:",
                        'bind'       => ['id' => $message_content->related_id],
                    ]);
                }
            }
            if ($warning) {
                if ($warning->staff_info_id == $staffId) {
                    $warning->kit_id = $msgId;
                } elseif ($warning->superior_id == $staffId) {
                    $warning->superior_kit_id = $msgId;
                } elseif ($warning->witness1_id == $staffId) {
                    $warning->witness1_kit_id = $msgId;
                } elseif ($warning->witness2_id == $staffId) {
                    $warning->witness2_kit_id = $msgId;
                } elseif ($warning->operator_id == $staffId) {
                    $warning->operator_kit_id = $msgId;
                } else {
                    $this->getDI()->get("logger")->write_log("warning_message 警告书获取失败，匹配不到对应的员工工号 " . json_encode([
                            $staffId,
                            $msgId,
                        ], JSON_UNESCAPED_UNICODE), "error");
                    return [];
                }
                $warning->save();
                $warningInfo = $warning->toArray();
            }
        }


        if ($warningInfo['staff_info_id'] == $staffId && $warningInfo['kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_STAFF;
        } elseif ($warningInfo['superior_id'] == $staffId && $warningInfo['superior_kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_SUPERIOR;
        } elseif ($warningInfo['witness1_id'] == $staffId && $warningInfo['witness1_kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_WITNESS_1;
        } elseif ($warningInfo['witness2_id'] == $staffId && $warningInfo['witness2_kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_WITNESS_2;
        } elseif ($warningInfo['operator_id'] == $staffId && $warningInfo['operator_kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_OPERATOR;
        }
        $warningInfo['is_need_sign'] = $is_need_sign;

        return $warningInfo;
    }

}