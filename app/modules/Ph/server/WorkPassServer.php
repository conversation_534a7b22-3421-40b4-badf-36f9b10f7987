<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Server\OsStaffServer AS GlobalBaseServer;
use FlashExpress\bi\App\Models\backyard\WorkPassModel;
use FlashExpress\bi\App\Repository\WorkPassRepository;

class WorkPassServer extends GlobalBaseServer {

    const WP = 'WP';

    /**
     * 读取用户最新一条workpass
     * @param type $staff_id
     * @return boolean
     */
    public function getWorkPass($staff_id) {

        $result = (new WorkPassRepository($this->lang, $this->timezone))->getOneByStaffId($staff_id);
        if ($result) {
            $work_pass = $result->toArray();
            $work_pass['is_valid'] = $this->isValidAction($work_pass['expiration_date']);
            $work_pass['staff_id'] = $this->generateCodeAction($work_pass['id']);
            $work_pass['job_number'] = $staff_id;

            return $work_pass;
        }
        return false;
    }

    public function addWorkPass($staff_id) {

        $result = (new WorkPassRepository($this->lang, $this->timezone))->addRecord($staff_id);

        if ($result) {
       		return $this->getWorkPass($staff_id); 
	}
    }

    /**
     * 生成编号
     * @param type $id
     * @return type
     */
    private function generateCodeAction($id) {
        return self::WP . str_pad($id, 8, "0", STR_PAD_LEFT);
    }

    /**
     * 是否过期
     * @param type $date
     * @return type
     */
    private function isValidAction($date) {
	$t = strtotime($date);
        return strtotime('+1 day',$t) > strtotime(date('y-m-d')) ? 0 : 1;
    }

}
