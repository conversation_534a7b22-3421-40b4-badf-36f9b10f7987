<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;


use FlashExpress\bi\App\Enums\PdfEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Server\BaseServer;

class PdfHelperServer extends BaseServer
{
    /**
     * 根据公司 ID 获取相应公司的页眉页脚
     * @param $paramsIn
     * @param int $response_type
     * @return array
     * @throws BusinessException
     */
    public function getHeaderAndFooter($paramsIn, $response_type = PdfEnums::RESPONSE_TYPE_DIRECT): array
    {
        $result = [
            'header_template'   => '',
            'footer_template'   => '',
            'company_full_name' => '',
        ];
        if (empty($paramsIn)) {
            return $result;
        }

        $fle_rpc = (new ApiClient('hcm_rpc', '', 'get_company_config_info', $this->lang));
        $fle_rpc->setParams($paramsIn);
        $res = $fle_rpc->execute();
        if (isset($res['error'])) {
            throw new BusinessException($res['error']);
        }
        $companyInfo = $res['result']['data'];

        return $this->getHeaderFooterStructure($companyInfo, $response_type);
    }

    /**
     * @param $companyConfigInfo
     * @param $response_type
     * @return array
     */
    public function getHeaderFooterStructure($companyConfigInfo, $response_type): array
    {
        if ($response_type == PdfEnums::RESPONSE_TYPE_IMAGE) {
            return [
                'header_template'    => $companyConfigInfo['company_logo_url_base64'] ?? '',
                'footer_template'    => $companyConfigInfo['company_address'] ?? '',
                'company_full_name'  => $companyConfigInfo['company_name'] ?? '',
                'company_short_name' => $companyConfigInfo['company_short_name'] ?? '',
                'company_phone'      => $companyConfigInfo['company_phone'] ?? '',
                'company_web_url'    => $companyConfigInfo['company_web_url'] ?? '',
            ];
        }

        $header = '<div style="width: 100%">
  <div
    style="
      width: 85%;
      margin: 0 auto;
      padding: 0 0 2mm;
      border-bottom: 3px solid #000;
      display: flex;
      align-items: center;
      justify-content: space-between;
    "
  >
    <span style="font-size: 5mm;margin-left: 3mm;"><strong>' . $companyConfigInfo['company_short_name'] . '</strong></span>
    <img
      style="margin: 0; padding: 0; border: 0; outline: none; width: 30mm"
      src="' . $companyConfigInfo['company_logo_url_base64'] . '"
    />
  </div>
</div>';

        $footer = '<div style="width: 210mm; font-weight: bold; font-size: 8px; text-align: center;margin: 0 10mm;">
  <div>' . $companyConfigInfo['company_address'] . '</div>
  <div>Tel: ' . $companyConfigInfo['company_phone'] . ' Website: <a>' . $companyConfigInfo['company_web_url'] . '</a></div>
</div>';
        return [
            'header_template'    => $header,
            'footer_template'    => $footer,
            'company_full_name'  => $companyConfigInfo['company_name'],
            'company_short_name' => $companyConfigInfo['company_short_name'],
            'company_phone'      => $companyConfigInfo['company_phone'] ?? '',
            'company_web_url'    => $companyConfigInfo['company_web_url'] ?? '',
        ];
    }

    /**
     * 匹配图片
     * @param $date
     * @param $type
     * @return string
     */
    private function extractImage($date, $type)
    {
        if ($type == PdfEnums::RESPONSE_TYPE_IMAGE) {
            preg_match('/<img[^>]+src="([^"]+)"/', $date, $matches);
            return $matches[1];
        }
        return $date;
    }
}