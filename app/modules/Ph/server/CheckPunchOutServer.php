<?php


namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\CheckPunchOutServer as BaseCheckPunchOutServer;
use FlashExpress\bi\App\Server\HrShiftServer;
use FlashExpress\bi\App\Server\KpiServer;
use FlashExpress\bi\App\Server\MaterialInventoryCheckServer;
use FlashExpress\bi\App\Server\OtherServer;
use FlashExpress\bi\App\Server\ProbationTargetServer;
use FlashExpress\bi\App\Server\PunchInServer;
use FlashExpress\bi\App\Server\WorkdayServer;
use FlashExpress\bi\App\Server\WorkdaySettingServer;


class CheckPunchOutServer extends BaseCheckPunchOutServer
{


    protected function punch_out_check_java(): \FlashExpress\bi\App\Server\CheckPunchOutServer
    {
        $attendanceServer = (new AttendanceServer($this->lang, $this->timezone));
        $attendanceInfo = $attendanceServer->attendance_info(['user_info'=>$this->staffInfo]);

        $shiftServer    = new HrShiftServer();
        $shiftInfo      = $shiftServer->getShiftInfos($this->staffInfo['id'], [$attendanceInfo['attendance_date']]);
        $shift_start = $shiftInfo[$attendanceInfo['attendance_date']]['start'] ?? '09:00';
        $shift_end   = $shiftInfo[$attendanceInfo['attendance_date']]['end'] ?? '18:00';
        $shiftStartTime  = date('Y-m-d H:i:s',strtotime($attendanceInfo['attendance_date'] . ' '.$shift_start));
        $shiftEndTime  = date('Y-m-d H:i:s',strtotime($attendanceInfo['attendance_date'] . ' '.$shift_end));
        if($shift_start > $shift_end){
            $shiftEndTime  = date('Y-m-d H:i:s',strtotime($attendanceInfo['attendance_date'] . ' '.$shift_end)+86400);
        }
        //请求参数
        $params= ['staff_info_id' => $this->staffInfo['id'],'platform'=>$this->platform,'attendance_date'=>$attendanceInfo['attendance_date'],'shift_start'=>$shiftStartTime,'shift_end'=>$shiftEndTime];
        $fle_rpc = new ApiClient('fle', 'com.flashexpress.fle.svc.api.StaffInfoSvc', 'offDuty', $this->lang);
        $fle_rpc->setParams($params);
        $fle_return = $fle_rpc->execute();
        $this->logger->write_log(['punch_out_check_java'=>$params,'result'=>$fle_return], 'info');
        if ($fle_return['code'] == 1) {
            $data = $fle_return['result'];
            //本业务不验证，可以下班打卡
            if ($data['off_duty_enabled']) {
                //没有提示，直接流转到下一个流程
                if (empty($data['notes'])) {
                    return $this;
                }

                $msg = '';
                foreach ($data['notes'] as $note) {
                    $msg .= $note['message']." \r\n";
                }
                $this->dialog_status      = 1;//要展示弹窗
                $this->dialog_msg         = $msg;
                $this->dialog_must_status = 0;              //可以跳过
                $this->business_type      = 'un_remittance';//可以跳过都用未回款的
                return $this;
            }

            //不能下班 必然是有原因的
            if (!empty($data['notes'])) {
                $msg                  = [];
                $change_business_type = false;
                $dialog_must_status   = 1;//不能跳过
                $dialog_status        = 1;//弹窗

                foreach ($data['notes'] as $key => $note) {

                    if(empty($this->business_type)){
                        $this->business_type = in_array($note['business_type'],
                            array_keys(self::JAVA_TASK_CONFIG)) ? $note['business_type'] : 'un_remittance';
                        $this->init_business_type = $this->business_type ;
                    }
                    $message = $note['message'];
                    if(isset($note['sub_off_duty_enabled']) && !$note['sub_off_duty_enabled']){
                        $message = '***'.$note['message'].'***';
                    }

                    //不是kit 就要换提示，去kit操作
                    switch ($note['business_type']) {
                        case 'un_finished_task'://kit by都有
                            $msg[] = $message;
                            break;
                        case 'un_finished_assign_task'://kit by都有
                            $msg[] = $message;
                            $this->business_type = 'un_finished_task';
                            break;
                        case 'un_close_dispatch_task'://仅kit有
                            $msg[]                = $message;
                            $this->business_type = 'un_finished_task';
                            break;
                        case 'store_revisit':
                            //不是kit 就转换成默认的一个提示的类型
                            $msg[]                = $message;
                            if ($this->platform != enums::RB_KIT) {
                                $change_business_type = 'un_finished_task';
                            }
                            break;
                        case 'store_un_remittance':
                        case 'un_count_batch':
                            $change_business_type = 'un_remittance';
                            $msg[]                = $message;
                            break;
                        default:
                            $msg[] = $message;
                            break;
                    }
                }

                $this->business_type      = $change_business_type ?: $this->business_type;
                $this->dialog_must_status = $dialog_must_status;              //不能跳过
                $this->dialog_status      = $dialog_status;                   //要展示弹窗
                $this->dialog_msg         = implode("\r\n", $msg);            //有提示 拼在一起

                //打卡开关 就是只提示，可以跳过
                $set_model = SettingEnvModel::findFirstByCode("by_clock_open");
                if (!empty($set_model) && $set_model->set_val) {
                    $this->dialog_must_status = 0;
                }

                //验证用户是否在ces培训 TODO 未回款
                if ((new OtherServer($this->lang, $this->timezone))->checkStaffIsCesTraining($this->staffInfo)) {
                    $this->is_ces_tra         = 1;
                    $this->dialog_must_status = 0;
                }
                return $this;
            }
        }
        return $this;
    }

    /**
     * 验证未读消息
     * @return array
     */
    protected function check_un_read_message()
    {
        $returnData = [];
        $backyardServer = new BackyardServer($this->lang ,$this->timezone);
        $messageInfo = $backyardServer->punch_out_msg(['staff_id'=>$this->staffInfo['id'],'request_channel'=>'off_work_call']);
        if(!empty($messageInfo)){

            $data['msg_id'] = strval(current($messageInfo));
            //构建业务类型返回参数
            $returnData['business_type'] = 'un_read_message';
            $returnData['message_detail'] = $data;
        }
        $this->logger->write_log('check_un_read_message '.$this->staffInfo['id'].' result:'.json_encode($returnData,JSON_UNESCAPED_UNICODE) ,'info');
        return $returnData;
    }

    /**
     * 验证bi下班
     * @return array
     */
    public function check_bi_task(): array
    {
        $returnData       = [];
        if (!env('check_bi_task', 1)) {
            return $returnData;
        }
        $attendanceServer = (new AttendanceServer($this->lang, $this->timezone));
        $attendanceInfo   = $attendanceServer->attendance_info(['user_info' => $this->staffInfo]);
        $data             = [
            'manager_id' => $this->staffInfo['id'],
            'stat_date'  => $attendanceInfo['attendance_date'],
        ];

        $ret = new ApiClient('bi_rpcv2', '', 'staffstatisticaldata.get_not_complete_false_staff', $this->lang);
        $ret->setParams($data);
        $result = $ret->execute();

        $this->logger->write_log(['check_bi_task'=>$data,'result'=>$result] ,'info');

        if (isset($result['result']) && $result['result']['code'] != 1) {
            $this->dialog_status      = 1;
            $this->dialog_must_status = 1;
            $this->dialog_msg         = $result['result']['msg'];
            $this->business_type      = 'un_remittance';
            $this->init_business_type = 'un_remittance';
            return $this->build_fbi_data();
        }
        return $returnData;
    }

    public function check_staff_punch_out($staffInfo, $paramsIn): array
    {
        $returnData      = [
            'data' => (object)[],
            'code' => -3,
            'msg'  => 'fail',
        ];
        $this->staffInfo = $staffInfo;
        $this->platform  = $paramsIn['platform'];
        if (env('close_punch_out_check', 0)) {
            return $returnData;
        }
        //验证java
        $check_java_task = $this->check_java_task($paramsIn['is_skip']);
        if (!empty($check_java_task)) {
            $returnData['data'] = $check_java_task;
            return $returnData;
        }

        // Fbi的任务
        $check_fbi = $this->check_bi_task();
        if(!empty($check_fbi)){
            $returnData['data'] = $check_fbi;
            return $returnData;
        }

        //验证未读的消息
        $check_un_read_message = $this->check_un_read_message();
        if (!empty($check_un_read_message)) {
            $returnData['data'] = $check_un_read_message;
            return $returnData;
        }

        // 验证未完成学习计划
        $check_un_finished_study = $this->check_un_finished_study();
        if (!empty($check_un_finished_study)) {
            $returnData['data'] = $check_un_finished_study;
            return $returnData;
        }
        //验证是否有未添加辅导员的员工
        $check_un_instructor = $this->check_un_instructor_task();
        if(!empty($check_un_instructor)) {
            $returnData['data'] = $check_un_instructor;
            return $returnData;
        }
        //验证是否有未添加派件码的员工
        $check_un_delivery_code = $this->check_un_delivery_code_task();
        if(!empty($check_un_delivery_code)) {
            $returnData['data'] = $check_un_delivery_code;
            return $returnData;
        }
        // 验证kpi 是否已经完成
        $verifyKpiResult = (new KpiServer($this->lang, $this->timezone))->verifyKpiIsCompleted($staffInfo);
        if (!empty($verifyKpiResult)) {
            $returnData['data'] = $verifyKpiResult;
            return $returnData;
        }

        //验证是否有未处理完成资产
        $verify_has_resign_assets = $this->check_un_has_resign_assets();
        if (!empty($verify_has_resign_assets)) {
            $returnData['data'] = $verify_has_resign_assets;
            return $returnData;
        }
            
        //验证是否有未盘任务
        $verify_has_undone_inventory_task = (new MaterialInventoryCheckServer($this->lang, $this->timezone))->verifyHasUndoneInventoryTask($staffInfo);
        if (!empty($verify_has_undone_inventory_task)) {
            $returnData['data'] = $verify_has_undone_inventory_task;
            return $returnData;
        }
        //验证 所有下级 自由轮休轮休配置天数 是否符合要求
        $workdaySettingNotice = (new WorkdaySettingServer($this->lang, $this->timezone))->checkWorkdaySetting($staffInfo);
        if (!empty($workdaySettingNotice)) {
            $returnData['data'] = $workdaySettingNotice;
            return $returnData;
        }

        //21954非一线员工试用期管理优化
        $probationTargetServer = new ProbationTargetServer($this->lang, $this->timezone);
        $targetCheck       = $probationTargetServer->checkStaffManagerSign($staffInfo['id']);
        if (!empty($targetCheck)) {
            $returnData['data'] = $targetCheck;
            return $returnData;
        }

        //验证是否有待确认转岗
        $check_unconfirmed_transfer = $this->check_unconfirmed_transfer();
        if (!empty($check_unconfirmed_transfer)) {
            $returnData['data'] = $check_unconfirmed_transfer;
            return $returnData;
        }

        //验证是否有未完成转正评估目标制定
        $check_probation_target = $this->check_probation_target();
        if (!empty($check_probation_target)) {
            $returnData['data'] = $check_probation_target;
            return $returnData;
        }

        //验证是否有到最后一天期限没有完成评估或有已超时的评估
        $check_probation_timeout = $this->check_probation_timeout();
        if (!empty($check_probation_timeout)) {
            $returnData['data'] = $check_probation_timeout;
            return $returnData;
        }

        //如果是 off 没加班 不让打卡
        $punchServer = new PunchInServer($this->lang, $this->timezone);
        $workdayNotice = $punchServer->checkOff($paramsIn, $staffInfo,'punch_out');
        if($workdayNotice !== true){
            $returnData['data'] = $workdayNotice['data'];
            return $returnData;
        }

        $returnData['code'] = 1;
        $returnData['msg']  = 'success';
        return $returnData;
    }

    /**
     * 验证是否有未添加辅导员的员工 非网点主管角色不验证
     * @return array
     */
    protected function check_un_instructor_task(): array
    {
        $this->logger->write_log('check_un_instructor_task '.$this->staffInfo['staff_id']. ' staff_info:'.json_encode($this->staffInfo,JSON_UNESCAPED_UNICODE),'info');
        $returnData = [];
        //非网点主管角色不验证
        if(!in_array(18, $this->staffInfo['positions'])){
            return  $returnData;
        }
        $paramIn['is_three_day'] = true;
        $paramIn['user'] = $this->staffInfo;
        $count = (new StaffServer())->getInstructorCount($paramIn);
        if($count > 0) {
            $returnData['business_type'] = 'un_finished_study';
            $returnData['training_detail'] = [
                'message' => $this->getTranslation()->_('add_instructor_error_4'),
                'url' => env("sign_url").'/#/boardConfirm?active=tab-two&tab=1&sub_tab=2',
            ];
        }
        $this->logger->write_log('check_un_instructor_task '.$this->staffInfo['staff_id']. ' result:'.json_encode($count,JSON_UNESCAPED_UNICODE),'info');
        return $returnData;
    }

    /**
     * 验证是否有未添加辅导员的员工 非网点主管角色不验证
     * @return array
     */
    protected function check_un_delivery_code_task(): array
    {
        $this->logger->write_log('check_un_delivery_code_task ' . $this->staffInfo['staff_id'] . ' staff_info:' . json_encode($this->staffInfo,
                JSON_UNESCAPED_UNICODE), 'info');
        $returnData = [];
        //非网点主管角色不验证
        if (!in_array(18, $this->staffInfo['positions'])) {
            return $returnData;
        }
        $paramIn['is_three_day'] = true;
        $paramIn['user']         = $this->staffInfo;
        $count                   = (new StaffServer())->getDeliveryCodeCount($paramIn);
        if ($count > 0) {
            $returnData['business_type']   = 'un_finished_study';
            $returnData['training_detail'] = [
                'message' => $this->getTranslation()->_('has_unfinished_delivery_code'),
                'url'     => env("sign_url") . '/#/boardConfirm?active=tab-two&tab=1&sub_tab=2',
            ];
        }
        $this->logger->write_log('check_un_delivery_code_task ' . $this->staffInfo['staff_id'] . ' result:' . json_encode($count,
                JSON_UNESCAPED_UNICODE), 'info');
        return $returnData;
    }

    /**
     * @return array
     */
    protected function check_unconfirmed_transfer(): array
    {
        $staffId = $this->staffInfo['staff_id'];
        $transferInfo = (new JobTransferConfirmServer($this->lang, $this->timezone))->getPendingConfirmInfo($staffId);
        if (!empty($transferInfo)) {
            $returnData['business_type'] = 'un_confirmed_transfer';
            $returnData['un_confirmed_transfer'] = [
                'message' => $this->getTranslation()->_('job_transfer.err_msg_exist_confirm'),
                'url'     => env("sign_url") . sprintf('/#/job-transfer/confirm-detail?id=%d&from=app', $transferInfo->id),
            ];
        }
        return $returnData ?? [];
    }
}