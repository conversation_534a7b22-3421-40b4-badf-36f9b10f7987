<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;


use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkDetectFaceRecordModel;
use FlashExpress\bi\App\Modules\Ph\library\Enums\HrJobTitleEnums;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StaffDeviceInfoRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Server\BaseServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\BusinesstripServer;
use FlashExpress\bi\App\Server\AttendanceBusinessServer;
use FlashExpress\bi\App\Server\Penalty\AttendancePenaltyServer;
use Phalcon\Forms\Element\Date;
use FlashExpress\bi\App\Server\AttendanceServer as BaseAttendanceServer;

use FlashExpress\bi\App\Models\backyard\HrStaffShiftModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftPresetModel;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\HrShiftServer;
use Exception;


class AttendanceServer extends BaseAttendanceServer
{
    public $timezone;
    //protected $score = '90';//人脸匹配 分数界定值


    public function __construct($lang, $timezone)
    {
        $this->timezone = $timezone;
        parent::__construct($lang,$timezone);
    }



    //匹配成功 后 保存打卡记录

    /**
     * attendance_category        1上班； 2下班
     * attendance_state    1内勤；2外勤
     * attendance_date    打卡时间 字符串格式
     * click_at 时间戳
     * click_lat    纬度
     * click_lng    经度
     * remark 备注 （如果是内勤，该参数为nil）
     * os   “ios”
     * clientid
     * click_url
     * @param $param
     * https://l8bx01gcjr.feishu.cn/docs/doccnbhEvKdNws5LnQhnM4uV1wI
     * 如果是外勤打卡 1. 1.0版本老挝员工外勤打卡时提交的打卡理由与外勤打卡记录将被发送至员工直线上级的企业邮箱中
     *
     * @return array
     * @throws BusinessException
     */

    public function save_attendance($param)
    {
        $staff_id = $param['user_info']['id'];
        $date = $param['attendance_date'];

        //外勤打卡 需要备注
        if ($param['attendance_state'] == StaffWorkAttendanceModel::STATE_FIELD_PERSONNEL_CARD && empty($param['remark'])) {
            return array('code' => 100324, 'message' => $this->getTranslation()->_('miss_args'), 'data' => null);
        }
        $staff_re   = new StaffRepository($this->lang);
        $staff_info = $staff_re->getStaffpositionV2($staff_id);
        if ($param['attendance_state'] == StaffWorkAttendanceModel::STATE_FIELD_PERSONNEL_CARD) {
            if (!$this->get_punch_field($staff_info)) {
                throw new BusinessException($this->getTranslation()->_('not_within_the_attendance_range'));
            }
        }

        //是否是工作日
        $staff_model = new StaffRepository($this->lang);
        $bi_staff_info = $param['bi_staff_info'] = $staff_model->getStaffPosition($staff_id);
        $working_day = $staff_model->get_is_working_day($bi_staff_info, $date);

        $this->isCustomer = $this->isBelongCustomer($bi_staff_info);
        
//        //获取班次 需求变更 如果打未来日期的上班卡 需要取预设没生效的 班次信息
        //获取班次 需求变更 如果打未来日期的上班卡 需要取预设没生效的 班次信息
        $shiftServer = new HrShiftServer();
        $shiftInfo   = $shiftServer->getShiftInfos($staff_id,[$date]);
        $shift_start = $shiftInfo[$date]['start'] ?? '09:00';
        $shift_end   = $shiftInfo[$date]['end'] ?? '18:00';
        $shift_id    = $shiftInfo[$date]['shift_id'] ?? 0;
        //如果是主播 班次信息是空
        $liveJobId = (new SettingEnvServer())->getSetVal('free_shift_position');
        $liveJobId = empty($liveJobId) ? [] : explode(',', $liveJobId);
        if(in_array($param['user_info']['job_title'],$liveJobId)){
            $shift_start = $shift_end = '';
        }

        $user_info = $param['user_info'];
        $masterStaffId = 0;
        $supportStaffInfo = [];
        $isOpen = (new SettingEnvServer())->getSetVal(AttendanceServer::SUB_STAFF_SUPPORT_SWITCH_KEY);
        $this->logger->write_log("sub_staff_support isOpen=$isOpen", 'info');

        if($isOpen == AttendanceServer::SUB_STAFF_SUPPORT_CLOSE) {
            //去支援的员工 班次使用申请表的
            if ($supportStaffInfo = (new AttendanceRepository($this->lang, $this->timezone))->getSupportOsStaffInfo($staff_id,$date)) {
                $log_info = "support staff shift and organization_id changed !!! organization_id before:" . $user_info['organization_id'] . ' after:' . $supportStaffInfo['store_id'];
                $this->logger->write_log($log_info, 'info');
//                $shift_start = $supportStaffInfo['shift_start'];
//                $shift_end = $supportStaffInfo['shift_end'];
//                $shift_id = $supportStaffInfo['shift_id'];

                $user_info['organization_id'] = $supportStaffInfo['store_id'];
            }
            $this->logger->write_log("sub_staff_support supportinfo=" .  json_encode($supportStaffInfo, JSON_UNESCAPED_UNICODE), 'info');

        } else {
            // 校验子账号打卡是否在支援期间内
            $isSubStaff = $bi_staff_info['is_sub_staff'] ?? 0;
            if($isSubStaff) {
                $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportInfoBySubStaff($staff_id,$date);
                if(empty($supportStaffInfo['staff_info_id'])){
                    return array('code' => 100337, 'message' => $this->getTranslation()->_('staff_support_expired'), 'data' => null);
                }
                $masterStaffId = $supportStaffInfo['staff_info_id'];
//                $shift_start = $supportStaffInfo['shift_start'];
//                $shift_end = $supportStaffInfo['shift_end'];
//                $shift_id = $supportStaffInfo['shift_id'];

            }

            $this->logger->write_log("sub_staff_support masterStaffId=$masterStaffId, isSubStaff=$isSubStaff, supportinfo=" .  json_encode($supportStaffInfo, JSON_UNESCAPED_UNICODE), 'info');

        }

        //打卡没有坐标
        if (empty($param['click_lat']) || empty($param['click_lng'])) {
            return array('code' => 100328, 'message' => $this->getTranslation()->_('wrong coordinate'), 'data' => null);
        }

        //重新验证 考勤日期 上下班类型 和 获取对应打卡网点坐标
        $p['client_id'] = $param['clientid'];
        $p['lat'] = $param['click_lat'];
        $p['lng'] = $param['click_lng'];
        $p['user_info'] = $param['user_info'];
        $baseAttendanceServer = new AttendanceServer($this->lang,$this->timezone);
        $att_info = $baseAttendanceServer->attendance_info($p);

        //日期不对 100325
        if ($att_info['attendance_date'] != $param['attendance_date']) {
            return array('code' => 100325, 'message' => $this->getTranslation()->_('wrong date'), 'data' => null);
        }

        //上下班类型不对  未打卡 1 已打卡 2 却卡 3
        if ($param['attendance_category'] == 1 && $att_info['click_in'] != 1) {
            return array('code' => 100336, 'message' => $this->getTranslation()->_('wrong attendance type'), 'data' => null);
        }
        if ($param['attendance_category'] == 2 && $att_info['click_after'] != 1) {
            return array('code' => 100336, 'message' => $this->getTranslation()->_('wrong attendance type'), 'data' => null);
        }

        //新需求 https://l8bx01gcjr.feishu.cn/docs/doccnZzrd3PvTSB7BDw2fCphGJf#
        //判断是否在出差状态 没有出差 走原来流程
        $business_re = new BusinesstripServer($this->lang, $this->timezone);
        $business_trip_type = $business_re->getExitTypeByDate($staff_id, $date);
        //存在出差状态 打卡操作 不写打卡表生成审批
        if ($business_trip_type) {//生成审批记录 但是 不保存大列表 每天 中午12点 跑一次  attendance_date 等于昨天的记录
            $param['shift_start'] = $shift_start;
            $param['shift_end'] = $shift_end;
            $param['shift_id'] = $shift_id;
            $param['staff_info_id'] = $staff_id;
            $param['working_day'] = $working_day;
            $param['business_trip_type'] = $business_trip_type['business_trip_type'];
            $param['destination_country'] = $business_trip_type['destination_country'];
            return $this->check_on_trip($param);
        }
        //判定 add 还是 更新
        $re = new AttendanceRepository($this->lang, $this->timezone);
        $info = $re->getDateInfo($staff_id, $date);

        //获取同设备 打卡次数
        $device_re = new StaffDeviceInfoRepository($this->lang);
        $client_num = $device_re->get_click_num($param['clientid'], $param['attendance_date'], $param['attendance_category']);
        $client_num = intval($client_num) + 1;
        $started_at = ''; //上班卡时间  0 时区
        $currentTime = time();

        $insert['shift_start'] = $shift_start;
        $insert['shift_end']   = $shift_end;
        $insert['shift_id']    = $shift_id;
        $insert['working_day'] = $working_day;

        if (empty($info)) {
            //组织 保存数据
            $insert['attendance_date'] = $param['attendance_date'];
            $insert['staff_info_id'] = $staff_id;
            $insert['organization_id'] = ($bi_staff_info['sys_store_id'] == '-1') ? $bi_staff_info['sys_department_id'] : $bi_staff_info['sys_store_id'];
            $insert['organization_type'] = $user_info['organization_type'];
            $insert['job_title'] = intval($bi_staff_info['job_title']);
            $insert['shift_start'] = $shift_start;
            $insert['shift_end'] = $shift_end;
            $insert['shift_id'] = $shift_id;
            $insert['working_day'] = $working_day;
            if ($param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_ON) {
                $started_at = $insert['started_at'] = gmdate('Y-m-d H:i:s', $currentTime);
                $insert['started_state'] = intval($param['attendance_state']);
                $insert['started_staff_lat'] = $param['click_lat'];
                $insert['started_staff_lng'] = $param['click_lng'];
                $insert['started_store_id'] = empty($att_info['store_id']) ? null : $att_info['store_id'];
                $insert['started_store_lng'] = empty($att_info['store_lng']) ? null : $att_info['store_lng'];
                $insert['started_store_lat'] = empty($att_info['store_lat']) ? null : $att_info['store_lat'];
                $insert['started_clientid'] = $param['clientid'];
                $insert['started_clientid_num'] = $client_num;
                $insert['started_equipment_type'] = $param['equipment'] == 'KIT' ? 1 : 3;//KIT 1  BACKYARD 3
                $insert['started_os'] = $param['os'];
                $insert['started_path'] = $param['click_url'];//带模块的文件名  attxxx/aa.jpg
                //$bucket =  $this->getDI()['config']['application']['oss_bucket'];
                $insert['started_bucket'] = $this->getDI()['config']['application']['oss_bucket'];//固定
                $insert['started_remark'] = empty($param['remark']) ? '' : $param['remark'];
            } else {//下班打卡
                $insert['end_at'] = gmdate('Y-m-d H:i:s', $currentTime);
                $insert['end_state'] = intval($param['attendance_state']);
                $insert['end_staff_lat'] = $param['click_lat'];
                $insert['end_staff_lng'] = $param['click_lng'];
                $insert['end_store_id'] = empty($att_info['store_id']) ? null : $att_info['store_id'];
                $insert['end_store_lng'] = empty($att_info['store_lng']) ? null : $att_info['store_lng'];
                $insert['end_store_lat'] = empty($att_info['store_lat']) ? null : $att_info['store_lat'];
                $insert['end_clientid'] = $param['clientid'];
                $insert['end_clientid_num'] = $client_num;
                $insert['end_equipment_type'] = $param['equipment'] == 'KIT' ? 1 : 3;//KIT 1  BACKYARD 3
                $insert['end_os'] = $param['os'];
                $insert['end_path'] = $param['click_url'];//带模块的文件名  attxxx/aa.jpg
                //$bucket =  $this->getDI()['config']['application']['oss_bucket'];
                $insert['end_bucket'] = $this->getDI()['config']['application']['oss_bucket'];//固定
                $insert['end_remark'] = empty($param['remark']) ? '' : $param['remark'];
            }
        } else {
            if ($param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_ON) {
                $started_at = $insert['started_at'] = gmdate('Y-m-d H:i:s', $currentTime);
                $insert['started_state'] = intval($param['attendance_state']);
                $insert['started_staff_lat'] = $param['click_lat'];
                $insert['started_staff_lng'] = $param['click_lng'];

                $insert['started_store_id'] = empty($att_info['store_id']) ? null : $att_info['store_id'];
                $insert['started_store_lng'] = empty($att_info['store_lng']) ? null : $att_info['store_lng'];
                $insert['started_store_lat'] = empty($att_info['store_lat']) ? null : $att_info['store_lat'];
                $insert['started_clientid'] = $param['clientid'];
                $insert['started_clientid_num'] = $client_num;
                $insert['started_equipment_type'] = $param['equipment'] == 'KIT' ? 1 : 3;//KIT 1  BACKYARD 3
                $insert['started_os'] = $param['os'];
                $insert['started_path'] = $param['click_url'];//带模块的文件名  attxxx/aa.jpg
                $insert['started_bucket'] = $this->getDI()['config']['application']['oss_bucket'];//固定
                $insert['started_remark'] = empty($param['remark']) ? '' : $param['remark'];
            } else {//下班打卡

                $started_at = !empty($info['start_data']) ?  $info['start_data'] : '';

                $insert['end_at'] = gmdate('Y-m-d H:i:s', $currentTime);
                $insert['end_state'] = intval($param['attendance_state']);
                $insert['end_staff_lat'] = $param['click_lat'];
                $insert['end_staff_lng'] = $param['click_lng'];

                $insert['end_store_id'] = empty($att_info['store_id']) ? null : $att_info['store_id'];
                $insert['end_store_lng'] = empty($att_info['store_lng']) ? null : $att_info['store_lng'];
                $insert['end_store_lat'] = empty($att_info['store_lat']) ? null : $att_info['store_lat'];
                $insert['end_clientid'] = $param['clientid'];
                $insert['end_clientid_num'] = $client_num;
                $insert['end_equipment_type'] = $param['equipment'] == 'KIT' ? 1 : 3;//KIT 1  BACKYARD 3
                $insert['end_os'] = $param['os'];
                $insert['end_path'] = $param['click_url'];//带模块的文件名  attxxx/aa.jpg
                $insert['end_bucket'] = $this->getDI()['config']['application']['oss_bucket'];//固定
                $insert['end_remark'] = empty($param['remark']) ? '' : $param['remark'];
            }
        }

        // 主账号打卡
        if($masterStaffId == 0){
            if(empty($info['id'])){
                $db = $this->getDI()->get('db');
                $flag = $db->insertAsDict('staff_work_attendance', $insert);
            } else {
                if (empty($info['shift_start'])) {
                    $insert['shift_start'] = $shift_start;
                    $insert['shift_end']   = $shift_end;
                    $insert['shift_id']    = $shift_id;
                    $insert['working_day'] = $working_day;
                }
                $att_model = new AttendanceRepository($this->lang, $this->timezone);
                $flag = $att_model->updateInfo($info['id'], $insert);
            }

            //外勤打卡数据--发送给上级：消息+邮件
            if ($param['attendance_state'] == StaffWorkAttendanceModel::STATE_FIELD_PERSONNEL_CARD && $flag) {
                $data['staff_info_id']       = $param['user_info']['id'];//工号
                $data['name']                = $param['user_info']['name'];//姓名
                $data['clock_time']          = $param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_ON ? $insert['started_at'] : $insert['end_at'];//零时区,打卡时间
                $data['attendance_category'] = $param['attendance_category'];//上/下班 卡
                $data['staff_lat']           = $param['click_lat'];//纬度
                $data['staff_lng']           = $param['click_lng'];//经度
                $data['attendance_date']     = $param['attendance_date'];//考勤日期
                $data['remark']              = $param['remark'];//外勤原因
                $this->sendFieldPersonnelMessage($data);
            }
        } else {
            if (empty($info['id'])) {
                $flag = $this->insertSubStaffAttendance($date, $insert, $masterStaffId, $shift_start, $shift_end , $shift_id);
            } else {
                if (empty($info['shift_start'])) {
                    $insert['shift_start'] = $shift_start;
                    $insert['shift_end']   = $shift_end;
                    $insert['shift_id']    = $shift_id;
                    $insert['working_day'] = $working_day;
                }
                $flag = $this->updateSubStaffAttendance($info['id'], $date, $insert, $masterStaffId, $shift_start, $shift_end , $shift_id);
            }
        }

        $format['bucket'] = $this->config->application->oss_bucket;
        $format['path']   = $param['click_url'];
        $sourceUrl        = $this->format_oss($format);
        //查询底片
        //如果是 子账号，就拿子账号的底片， $masterStaffId 为空 $staff_id 是 主账号，$masterStaffId 不为空，$staff_id 是 子账号
        $check_staff_info_id = empty($masterStaffId) ? $staff_id : $masterStaffId;
        $faceImageUrl = $this->get_face_img($check_staff_info_id);
        if(empty($faceImageUrl)) {
            $this->getDI()->get('logger')->write_log(['saveAttendanceScanFaceRecord_save_attendance' => ['staff_info_id' => $check_staff_info_id, 'remark' => '未找到底片请核查']] ,'error');
        }

        //写入扫脸记录表
        $scanFaceRecordData = [
            'staff_info_id'   => $check_staff_info_id,
            'attendance_date' => $param['attendance_date'],
            'type'            => $param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_ON ? StaffWorkAttendanceModel::ATTENDANCE_ON : StaffWorkAttendanceModel::ATTENDANCE_OFF,
            'operating_mode'  => $param['attendance_state'],
            'event_time'      => gmdate('Y-m-d H:i:s',$currentTime),
            'lng'             => $param['click_lng'],
            'lat'             => $param['click_lat'],
            'client_id'       => $param['clientid'],
            'os'              => $param['os'],
            'face_img'        => $sourceUrl,
            'face_negative'   => !empty($faceImageUrl) ? $faceImageUrl : $sourceUrl,
        ];
        $this->saveAttendanceScanFaceRecord($scanFaceRecordData);

        //推送上下班打卡时间和坐标到MS
        $clock_data = [
            'staffInfoId' => intval($staff_id),
            'clockAt'     => $currentTime,
            'lat'         => $param['click_lat'],
            'lng'         => $param['click_lng'],
            'type'        => intval($param['attendance_category']),//1,上班，2下班
            'deviceId'    => $param['clientid'],
        ];
        $this->sendClockToMs($clock_data);
        $this->logger->write_log("staff {$staff_id} attendance flag:{$flag} working_day:{$working_day} ", 'info');
        //通知fbi 发送 mobile dc pdf 只有下班
        if($param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_OFF && $staff_info['job_title'] == enums::$job_title['mobile_dc']){
            $mqData = [
                'staff_info_id'   => intval($staff_id),
                'attendance_date' => $param['attendance_date'],
                'start_time'      => empty($info['started_at']) ? null : strtotime($info['started_at']),
                'card_time'       => $currentTime,//下班打卡时间
            ];
            $sendData['jsonCondition']    = json_encode($mqData);
            $sendData['handleType']       = RocketMQ::TAG_SEND_D_SHEET_MOBILE_DC;
            $sendData['shardingOrderKey'] = $staff_id;
            $rmq                          = new RocketMQ('click_out_send_dsheet');
            $rid                          = $rmq->sendOrderlyMsg($sendData);//有序
            $this->logger->write_log('send_dsheet ' . $rid . 'data:' . json_encode($sendData), $rid ? 'info' : 'error');
        }

        //下班打卡通知客服系统
        if($param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_OFF && $this->isCustomer){
            $this->syncWebHookCustomer($bi_staff_info, $param);
        }
        //下班打卡通知FBI提成
        if($param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_OFF){
            $this->clickOutSendMQToBI($bi_staff_info, $supportStaffInfo['store_id']??'');
        }

        if($flag && $working_day == StaffWorkAttendanceModel::WORK_DAY_UN_PH_UN_REST){
            //指定部门下的部分职位有处罚
            $attendancePenaltyServer = new AttendancePenaltyServer($this->lang,$this->timezone);
            if($bi_staff_info['formal'] == HrStaffInfoModel::FORMAL_1 && $attendancePenaltyServer->checkIsNeedPenalty($bi_staff_info['job_title'],$bi_staff_info['sys_department_id'],$bi_staff_info['sys_store_id'])){
                return (new AttendancePenaltyServer($this->lang,$this->timezone))->push($staff_id, $param['attendance_date'], $param['attendance_category']);
            }
            //获取上班卡时间
            $this->sendAbnormalAttendanceMessage($staff_id, $param['attendance_date'], $param['attendance_category'],
                $shift_start, $shift_end, $shift_id,$started_at);
        }
        //外协员工记录上班打卡时所在外协公司
        if (in_array($bi_staff_info['staff_type'], [1, 2, 3])
            && $bi_staff_info['sys_store_id'] != '-1') {
            $this->syncAttendanceForCompanyNameEfPush($staff_id, $date);
        }
        $this->sendOcwAgreementMessagePush($bi_staff_info,$param,$shift_id,$shift_start,$shift_end);
        $this->logger->write_log("save_attendance_{$staff_id} {$flag} " . json_encode($insert), 'info');
        return $flag;
    }

    /**
     * @param $bi_staff_info
     * @param $param
     * @return void
     */
    private function sendOcwAgreementMessagePush($bi_staff_info,$param,$shift_id,$shift_start,$shift_end): void
    {
        if (
            !empty($bi_staff_info['hire_type']) &&
            $bi_staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_11 &&
            $bi_staff_info['formal'] == HrStaffInfoModel::FORMAL_0 &&
            !empty($bi_staff_info['job_title']) &&
            $param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_ON
        ){
            $this->logger->write_log([
                'function'  => 'sendOcwAgreementMessagePush',
                'bi_staff_info' => $bi_staff_info,
                'param'    => $param,
            ],'info');
            $hub_ocw_attendance_job_title_config = (new SettingEnvServer())->getSetVal('hub_ocw_attendance_job_title_config',',');
            $ffm_ocw_attendance_job_title_config = (new SettingEnvServer())->getSetVal('ffm_ocw_attendance_job_title_config',',');
            if (in_array($bi_staff_info['job_title'], $hub_ocw_attendance_job_title_config) || in_array($bi_staff_info['job_title'], $ffm_ocw_attendance_job_title_config)){
                $data = json_encode([
                    'staff_info_id' => $bi_staff_info['staff_info_id'], 
                    'attendance_date' => $param['attendance_date'],
                    'shift_id' => $shift_id,
                    'shift_start' => $shift_start,
                    'shift_end' => $shift_end,
                ]);
                $this->logger->write_log('sendOcwAgreementMessagePush data  ' . $data, "info");
                $this->redisLib->lpush(RedisEnums::SEND_OCW_AGREEMENT_MESSAGE_PUSH, $data);
                return;
            }
        }
    }


    //新增需求 出差期间 必须打卡 并且 非真实写入打卡表 需要走审批流程
    public function check_on_trip($param)
    {
        $staff_id = $param['staff_info_id'];
        $shift_start = $param['shift_start'];
        $shift_end = $param['shift_end'];
        $shift_id = $param['shift_id'] ?? 0;
        //判断是否已经存在 出差打卡申请 记录
        $ab_server = new AttendanceBusinessServer($this->lang, $this->timezone);
        $check_info = $ab_server->find_by_date($staff_id, $param['attendance_date']);
        if (!empty($check_info)) {
            //判断 上班存在
            if (!empty($check_info['start_time']) && $param['attendance_category'] == 1) {
                $return['code'] = -1;
                $return['message'] = $this->getTranslation()->_('business_card_notice');
                $return['data'] = null;
                return $return;
            }
            //下班存在
            if (!empty($check_info['end_time']) && $param['attendance_category'] == 2) {
                $return['code'] = -1;
                $return['message'] = $this->getTranslation()->_('business_card_notice');
                $return['data'] = null;
                return $return;
            }
        }
        $started_at = ''; //上班卡时间  0 时区

        //判断是否需要根据时区调整时间 https://flashexpress.feishu.cn/docx/NuQpdsXRSoSJtxxt0AKcaEwanPb
        //获取出差打卡时的时区
        $this->logger->write_log("getBusinessAttendanceTimeZone {$staff_id} " . json_encode($param),'info');

        $time_zone = $this->getBusinessAttendanceTimeZone($param);  //时区默认是 当地时区


        //保存补卡申请操作
        $bus_insert['staff_info_id'] = $staff_id;
        $bus_insert['attendance_date'] = $param['attendance_date'];

        if (empty($check_info) || empty($check_info->shift_start)) {
            $bus_insert['start_shift'] = $shift_start;
            $bus_insert['end_shift']   = $shift_end;
            $bus_insert['shift_id']    = $shift_id;
        }
        $bus_insert['working_day'] = $param['working_day'];
        //第二天 当地时间中午12点 跑任务
        $bus_insert['task_time'] = date('Y-m-d 12:00:00', strtotime("+1 day"));
        $bus_insert['business_trip_type'] = $param['business_trip_type'] ?? BusinessTripModel::BTY_NORMAL;
        $currentTime = gmdate('Y-m-d H:i:s',time());

        if ($param['attendance_category'] == 1) {//上班
            $started_at = $bus_insert['start_time'] = $currentTime;
            $bus_insert['start_lat'] = $param['click_lat'];
            $bus_insert['start_lng'] = $param['click_lng'];
            $bus_insert['started_path'] = $param['click_url'];
            $bus_insert['started_bucket'] = $this->getDI()['config']['application']['oss_bucket'];//固定
            $bus_insert['start_reason'] = empty($param['remark']) ? '' : $param['remark'];
            $bus_insert['start_time_zone'] = $time_zone;
            $bus_insert['started_os'] = $param['os'];
            $bus_insert['started_clientid'] = $param['clientid'];

        } else {//下班
            $started_at = empty($check_info['start_time']) ? '' : $check_info['start_time'];
            $bus_insert['end_time'] = $currentTime;
            $bus_insert['end_lat'] = $param['click_lat'];
            $bus_insert['end_lng'] = $param['click_lng'];
            $bus_insert['end_path'] = $param['click_url'];
            $bus_insert['end_bucket'] = $this->getDI()['config']['application']['oss_bucket'];
            $bus_insert['end_reason'] = empty($param['remark']) ? '' : $param['remark'];
            $bus_insert['end_time_zone'] = $time_zone;
            $bus_insert['end_os'] = $param['os'];
            $bus_insert['end_clientid'] = $param['clientid'];

        }
        $ab_server->apply_by_business($bus_insert, $check_info);

        $format['bucket'] = $this->config->application->oss_bucket;
        $format['path']   = $param['click_url'];
        $sourceUrl        = $this->format_oss($format);
        //查询底片
        $faceImageUrl = $this->get_face_img($staff_id);
        if(empty($faceImageUrl)) {
            $this->getDI()->get('logger')->write_log(['saveAttendanceScanFaceRecord_business_trip_type' => ['staff_info_id' => $staff_id, 'remark' => '未找到底片请核查']] ,'error');
        }

        //写入扫脸记录表，刷一次记一次
        $scanFaceRecordData = [
            'staff_info_id'   => $staff_id,
            'attendance_date' => $param['attendance_date'],
            'type'            => $param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_ON ? StaffWorkAttendanceModel::ATTENDANCE_ON : StaffWorkAttendanceModel::ATTENDANCE_OFF,
            'operating_mode'  => AttendanceBusinessServer::getStaffWorkAttendanceStateByBusinessTripType($bus_insert['business_trip_type']),
            'event_time'      => $currentTime,
            'lng'             => $param['click_lng'],
            'lat'             => $param['click_lat'],
            'client_id'       => $param['clientid'],
            'os'              => $param['os'],
            'face_img'        => $sourceUrl,
            'face_negative'   => !empty($faceImageUrl) ? $faceImageUrl : $sourceUrl,
        ];
        $this->saveAttendanceScanFaceRecord($scanFaceRecordData);

        $this->getDI()->get('logger')->write_log("出差期间外勤打卡操作 ".json_encode($bus_insert,JSON_UNESCAPED_UNICODE), 'info');
        //工作日
        if($param['working_day'] == StaffWorkAttendanceModel::WORK_DAY_UN_PH_UN_REST){
            //指定部门下的部分职位有处罚
            $attendancePenaltyServer = new AttendancePenaltyServer($this->lang,$this->timezone);
            if($param['bi_staff_info']['formal'] == HrStaffInfoModel::FORMAL_1 && $attendancePenaltyServer->checkIsNeedPenalty($param['bi_staff_info']['job_title'],$param['bi_staff_info']['sys_department_id'],$param['bi_staff_info']['sys_store_id'])){
                return (new AttendancePenaltyServer($this->lang,$this->timezone))->push($staff_id, $param['attendance_date'], $param['attendance_category']);
            }
            $this->sendAbnormalAttendanceMessage($staff_id, $param['attendance_date'], $param['attendance_category'],
                $shift_start, $shift_end, $shift_id,$started_at);
        }

        return true;
    }

    /**
     * 异常考勤
     * 工作日打卡 迟到早退消息提醒
     * @param $staff_info_id
     * @param $attendance_date
     * @param $attendance_category
     * @param $shift_start
     * @param $shift_end
     * @param $shift_id
     * @param $started_at  打卡时间  Y-m-d H:i:s
     * @return bool
     */
    private function sendAbnormalAttendanceMessage($staff_info_id,$attendance_date,$attendance_category,$shift_start,$shift_end,$shift_id=0,$started_at='')
    {

        if(empty($shift_start) || empty($shift_end)){
            $this->getDI()->get('logger')->write_log("员工:{ $staff_info_id}- 没有班次信息","info");
            return  true;
        }

        //准时打卡的上班时间
        $attendance_start_at = strtotime($attendance_date.' '.$shift_start);
        //准时打卡的下班时间
        $attendance_end_at = strtotime($attendance_date.' '.$shift_end);
        //当前时间
        $add_hour = $this->config->application->add_hour;
        $current_time = strtotime(gmdate('Y-m-d H:i:s', time())) + $add_hour*3600;//当前菲律宾日期
        //把上班打卡时间 变成当地时间
        $started_at_time = empty($started_at) ? '' : (strtotime($started_at) + $add_hour*3600);//当前菲律宾时间

        //获取弹性班次
        $HrShiftServer = new HrShiftServer();
        //实际弹性打卡偏移量  秒
        $current_flexible_time = $HrShiftServer->getCurrentFlexibleShiftTime([
            'started_at_time' => $started_at_time,
            'shift_id'   => $shift_id,
            'date'=>$attendance_date,
        ]);

        $this->logger->write_log("sendAbnormalAttendanceMessage $staff_info_id,$attendance_date,$attendance_category,$shift_start,$shift_end ,$current_time,$current_flexible_time", 'info');


        $auditRepository = new AuditRepository($this->lang);

        $leaveInfo = $auditRepository->getStaffLeaveInfoByDate($staff_info_id,$attendance_date);
        if(!empty($leaveInfo)){
            //请了两个半天假的还打卡，就是加班的，不发消息
            if(count($leaveInfo) > 1){
                return true;
            }
            $leaveInfo = current($leaveInfo);
            //请了全天假的还打卡，就是加班的，不发消息
            if($leaveInfo['type'] == 0){
                return true;
            }
            //请了上午假，上班时间延后5小时
            //请上午假 没有弹性打卡
            if($leaveInfo['type'] == 1){
                $current_flexible_time = '';
                $attendance_start_at = strtotime($attendance_date.' '.$shift_start)+(5*3600);
            }
            //请了下午假，下班时间提前5小时
            if($leaveInfo['type'] == 2){
                $attendance_end_at = strtotime($attendance_date.' '.$shift_end)-(5*3600);
            }
        }

        $title = $content = $time_str = '';

        //判断是否存在弹性打卡偏移量 存在就证明是弹性打卡
        if (!empty($current_flexible_time)) {
            //重新赋值 准时打卡的上班时间
            $attendance_start_at += $current_flexible_time;
            //重新赋值 准时打卡的下班时间  加上弹性的时间
            $attendance_end_at += $current_flexible_time;
            //发送消息 提醒下班打卡时间   发这个消息 就不会有迟到消息了
            if ($attendance_category == 1) {
                $title   = $this->getTranslation()->_('flexible_attendance_title');
                $content = $this->getTranslation()->_('flexible_attendance_content', [
                    'work_started_at' => date("H:i", $started_at_time),
                    'work_end_at'     => date('H:i', $attendance_end_at),
                ]);
            }
        }

        if($attendance_category == 1){
            //迟到
            if($current_time >= $attendance_start_at + 60){
                $timeInfo = DateHelper::dealSecondsToHoursAndMinutes($current_time-$attendance_start_at);
                if($timeInfo['hours']>0){
                    $time_str .= $timeInfo['hours'].'h';
                }
                if($timeInfo['minutes']>0){
                    $time_str .= $timeInfo['minutes'].'minutes';
                }
                $title = 'Coming late reminder';
                $content = "You are being reminded that you are {$time_str} late already.You will be punished for a certain amount in accordance with the company's regulations.";

            }
        }else{
            //早退
            if($current_time <= $attendance_end_at - 60){
                $timeInfo = DateHelper::dealSecondsToHoursAndMinutes($attendance_end_at-$current_time);
                if($timeInfo['hours']>0){
                    $time_str .= $timeInfo['hours'].'h';
                }
                if($timeInfo['minutes']>0){
                    $time_str .= $timeInfo['minutes'].'minutes';
                }
                $title = 'Leave early reminder';
                $content = "You are being reminded that you have left {$time_str} early .You will be punished for a certain amount in accordance with the company's regulations.";
            }
        }

        if(!$title){
            return  true;
        }
        //发送消息
        $data = [
            "staff_info_id"     => $staff_info_id,    //提交人ID
            "title"             => $title,
            "category"          => -1,          //类别
            "content"           => "<div style='font-size: 40px'>".$content."</div>>",    //内容
        ];

        try {
            $this->getDI()->get('logger')->write_log("员工:{ $staff_info_id}-{$title}提醒，请求参数:" . json_encode($data,JSON_UNESCAPED_UNICODE),"info");
            $fle_rpc = (new ApiClient('bi_rpc','','message_to_backyard', $this->lang));
            $fle_rpc->setParams($data);
            $fle_return = $fle_rpc->execute();//响应
            $this->getDI()->get('logger')->write_log("员工{$title}提醒，请求参数:" . json_encode($data,JSON_UNESCAPED_UNICODE)."response:".json_encode($fle_return,JSON_UNESCAPED_UNICODE),"info");
            return  !isset($fle_return['error']);
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("员工{$title}提醒，请求参数:" . json_encode($data,JSON_UNESCAPED_UNICODE)." Exception:". $e->getMessage(),"error");
            return  false;
        }
    }

    protected function clickOutSendMQToBI($staffInfo, $support_store_id)
    {
        if (!in_array($staffInfo['job_title'], [
                HrJobTitleEnums::BIKE_COURIER,
                HrJobTitleEnums::VAN_COURIER,
                HrJobTitleEnums::TRICYCLE_COURIER,
                HrJobTitleEnums::TRUCK_DRIVER_DAY,
            ]) || !in_array($staffInfo['formal'], [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN])) {
            return true;
        }
        $data                         = [
            'staff_info_id'    => $staffInfo['staff_info_id'],
            'support_store_id' => $support_store_id ?: '',
            'store_id'         => $staffInfo['sys_store_id'],
            'off_work_time'    => date('Y-m-d H:i:s'),
        ];
        $sendData['jsonCondition']    = json_encode($data);
        $sendData['handleType']       = RocketMQ::TAG_INCENTIVE_COURIER_PUNCH_OUT;
        $sendData['shardingOrderKey'] = $staffInfo['staff_info_id'];
        $rmq                          = new RocketMQ('incentive_courier_punch_out');
        return $rmq->sendOrderlyMsg($sendData);//有序
    }


}
