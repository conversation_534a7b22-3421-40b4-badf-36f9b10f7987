<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\DeliveryCodeServer;
use FlashExpress\bi\App\Server\StaffServer as GlobalServer;


class StaffServer extends GlobalServer
{
    /**
     * 已入职需设置辅导员count 统计 职位  Bike courier  DC Officer  Van Courier  Tricycle Courier Truck Driver
     * @var int[]
     */
    public static $show_job_title = [13,37,110,1000,1194,1675];
    /**
     * 已入职需设置辅导员count 统计 部门 Network Management（125）
     * @var array
     */
    public static $counselor_department_id = [125];
    public static $show_delivery_code_job_title = [13,110,1000]; //目前只有PH一个国家，以后有多个国家这里可以差异化

    /**
     * 获取展示辅导员的职位
     * @return array
     */
    public function getShowJobTitle() :array
    {
        return static::$show_job_title;
    }

    /**
     * 获取展示 派送码的职位
     * @return array
     */
    public function getShowDeliveryCodeJobTitle(): array
    {
        return static::$show_delivery_code_job_title;
    }

    /**
     * 获取展示 派送码配置
     * @param $store_ids
     * @return array
     * @throws \Exception
     */
    public function getDeliveryCodeByStoreIds($store_ids): array
    {
        if (empty($store_ids)) {
            return [];
        }
        return (new DeliveryCodeServer())->getDeliveryCodeByStoreIds($store_ids);
    }

    /**
     * 获取展示 派送码配置
     * @param $store_id
     * @return array
     * @throws \Exception
     */
    public function getDeliveryCode($store_id): array
    {
        if (empty($store_id)) {
            return [];
        }
        $deliveryCodeInfo = (new DeliveryCodeServer())->getDeliveryCodeByStoreIds([$store_id]);
        return !empty($deliveryCodeInfo) ? current($deliveryCodeInfo) : [];
    }

    /**
     * 获取 未配置辅导员、未配置派件码员工Count数
     * @param $paramIn
     * @return mixed
     * @throws \Exception
     */
    public function getStaffJobTitleInstructorCount($paramIn)
    {
        $staffId    = $paramIn['user']['staff_id'];
        $isThreeDay = $paramIn['is_three_day'] ?? false;

        //1. 获取未配置辅导员数据
        //获取当前用户管辖信息
        [$deptIds, $storeIds] = $this->getManagerInfo($staffId);
        $networkDepartmentIds = $this->getNetWorkInfo();
        $params = [
            'show_job_title'         => $this->getShowJobTitle(),
            'network_department_ids' => $networkDepartmentIds,
            'manage_department_ids'  => $deptIds,
            'manage_store_ids'       => $storeIds,
            'manager_id'             => $staffId,
            'is_three_day'           => $isThreeDay,
            'is_show_instructor'     => true,
            'is_show_delivery_code'  => false,
        ];
        $builder = $this->getEntryListBuilder($params);
        $showInstructorList = $builder->columns(['e.resume_id'])->getQuery()->execute()->toArray();
        $showInstructorList = array_column($showInstructorList, 'resume_id');

        //2. 获取未配置派件码数据
        $params = [
            'show_job_title'         => $this->getShowDeliveryCodeJobTitle(),
            'network_department_ids' => $networkDepartmentIds,
            'manage_department_ids'  => $deptIds,
            'manage_store_ids'       => $storeIds,
            'manager_id'             => $staffId,
            'is_three_day'           => $isThreeDay,
            'is_show_instructor'     => false,
            'is_show_delivery_code'  => true,
        ];
        $builder              = $this->getEntryListBuilder($params);
        $showDeliveryCodeList = $builder->columns(['e.resume_id', 'h.sys_store_id'])->getQuery()->execute()->toArray();
        $showDeliveryCodeArr  = [];
        if (!empty($showDeliveryCodeList)) {
            //获取派件码
            $showDeliveryCodeStoreIds = array_column($showDeliveryCodeList, 'sys_store_id');
            $deliveryCode             = $this->getDeliveryCodeByStoreIds($showDeliveryCodeStoreIds);
            $deliveryCode             = array_column($deliveryCode, null, 'storeId');
            foreach ($showDeliveryCodeList as $item) {
                if (!isset($deliveryCode[$item['sys_store_id']])) {
                    continue;
                }
                $config = $deliveryCode[$item['sys_store_id']];
                if (count($config['deliveryCodes']) == 0 || $config['bindLimit'] == 0) {
                    continue;
                }
                $showDeliveryCodeArr[] = $item['resume_id'];
            }
        }
        $totalCnt = count(array_values(array_unique(array_merge($showInstructorList, $showDeliveryCodeArr))));
        $this->logger->write_log(sprintf('[getStaffJobTitleInstructorCount]staff %d, instructor = %s, delivery code=%s, total cnt:%d',
            $staffId,
            json_encode($showInstructorList),
            json_encode($showDeliveryCodeArr),
            $totalCnt), 'info');
        return $totalCnt;
    }

    /**
     * 获取 未配置辅导员员工Count数
     * @param $paramIn
     * @return mixed
     * @throws \Exception
     */
    public function getDeliveryCodeCount($paramIn)
    {
        $staffId    = $paramIn['user']['staff_id'];
        $isThreeDay = $paramIn['is_three_day'] ?? false;

        [$deptIds, $storeIds] = $this->getManagerInfo($staffId);
        $networkDepartmentIds = $this->getNetWorkInfo();
        $params = [
            'show_job_title'         => $this->getShowDeliveryCodeJobTitle(),
            'network_department_ids' => $networkDepartmentIds,
            'manage_department_ids'  => $deptIds,
            'manage_store_ids'       => $storeIds,
            'manager_id'             => $staffId,
            'is_three_day'           => $isThreeDay,
            'is_show_instructor'     => false,
            'is_show_delivery_code'  => true,
        ];
        $builder              = $this->getEntryListBuilder($params);
        $showDeliveryCodeList = $builder->columns(['e.resume_id', 'h.sys_store_id'])->getQuery()->execute()->toArray();
        $count  = 0;
        if (!empty($showDeliveryCodeList)) {
            //获取派件码
            $showDeliveryCodeStoreIds = array_column($showDeliveryCodeList, 'sys_store_id');
            $deliveryCode = $this->getDeliveryCodeByStoreIds($showDeliveryCodeStoreIds);
            $deliveryCode = array_column($deliveryCode, null,'storeId');

            foreach ($showDeliveryCodeList as $item) {
                if (!isset($deliveryCode[$item['sys_store_id']])) {
                    continue;
                }
                $config = $deliveryCode[$item['sys_store_id']];
                if (count($config['deliveryCodes']) == 0 || $config['bindLimit'] == 0) {
                    continue;
                }
                $count += 1;
            }
        }
        return $count;
    }

    /**
     * 获取 未配置辅导员员工Count数
     * @param $paramIn
     * @return mixed
     * @throws \Exception
     */
    public function getInstructorCount($paramIn)
    {
        $staffId    = $paramIn['user']['staff_id'];
        $isThreeDay = $paramIn['is_three_day'] ?? false;

        //1. 获取未配置辅导员数据
        //获取当前用户管辖信息
        [$deptIds, $storeIds] = $this->getManagerInfo($staffId);
        $networkDepartmentIds = $this->getNetWorkInfo();
        $params = [
            'show_job_title'         => $this->getShowJobTitle(),
            'network_department_ids' => $networkDepartmentIds,
            'manage_department_ids'  => $deptIds,
            'manage_store_ids'       => $storeIds,
            'manager_id'             => $staffId,
            'is_three_day'           => $isThreeDay,
            'is_show_instructor'     => true,
            'is_show_delivery_code'  => false,
        ];
        $builder = $this->getEntryListBuilder($params);
        $count = $builder->columns(['count(1) as count'])->getQuery()->getSingleResult();
        return intval($count->count);
    }

    /**
     * 获取入职列表builder
     * @param $params
     * @return mixed
     */
    private function getEntryListBuilder($params)
    {
        $showJobTitle         = $params['show_job_title'];
        $networkDepartmentIds = $params['network_department_ids'];
        $storeIds             = $params['manage_store_ids'];
        $departmentIds        = $params['manage_department_ids'];
        $managerId            = $params['manager_id'];
        $isThreeDay           = $params['is_three_day'];
        $isShowInstructor     = $params['is_show_instructor'];
        $isShowDeliveryCode   = $params['is_show_delivery_code'];

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['h' => HrStaffInfoModel::class]);

        // 泰国存在通过winhr导入个人代理的情况，即：不走正常的招聘流程
        // 所以泰国需要排掉这种途径的人 [!!!!! 这里与列表的逻辑要一致 !!!!!]
        $builder->join(HrEntryModel::class, 'h.staff_info_id = e.staff_id', 'e');
        $builder->where('h.formal = 1 AND h.state = 1 AND h.is_sub_staff = 0');
        //入职日期
        $date = date('Y-m-d 00:00:00', strtotime("-7 days")); //默认7天内

        if ($isShowInstructor) {
            //辅导员未配置
            $builder->andWhere('h.instructor_id is null');
            if ($isThreeDay) {
                $date = date('Y-m-d 00:00:00', strtotime("-3 days"));
            }
        }
        if ($isShowDeliveryCode) {
            //派件码未配置
            $builder->andWhere('e.delivery_code = \'\' and h.hire_type = :hire_type:', ['hire_type' => HrHcModel::HIRE_TYPE_CONTRACT_LABOUR]);
        }
        $builder->andWhere('hire_date >= :date:', ['date' => $date]);

        //权限
        $conditions           = '';
        $bindParams['manger'] = $managerId;
        if(!empty($storeIds)) {
            $conditions .= ' or h.sys_store_id in ({store_ids:array})';
            $bindParams['store_ids'] = $storeIds;
        }
        if(!empty($departmentIds)) {
            $conditions .= ' or h.node_department_id in ({dept_ids:array})';
            $bindParams['dept_ids'] = $departmentIds;
        }
        $builder->andWhere('h.manger = :manger:' . $conditions, $bindParams);

        //配置
        if ($networkDepartmentIds) {
            $builder->andWhere('h.node_department_id in ({node_department_id:array})', ['node_department_id' => $networkDepartmentIds]);
        }
        if ($showJobTitle) {
            $builder->andWhere('h.job_title IN ({job_title:array})', ['job_title' => $showJobTitle]);
        }
        return $builder;
    }

    /**
     * 添加辅导员、派件码
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function addStaffInstructor($paramIn)
    {
        if (!isset($paramIn['instructor_id']) && !isset($paramIn['delivery_code'])) {
            throw new ValidationException('miss args');
        }

        //保存辅导员
        if (isset($paramIn['instructor_id']) && $paramIn['instructor_id']) {

            $staff_info = HrStaffInfoModel::findFirst([
                'columns'    => 'staff_info_id,name,job_title,sys_department_id,node_department_id,sys_store_id,instructor_id,hire_date',
                'conditions' => 'staff_info_id = :staff_info_id: AND state = 1 AND formal = 1',
                'bind'       => [
                    'staff_info_id' => $paramIn['staff_info_id'],
                ],
            ]);
            if(empty($staff_info['instructor_id'])) {
                $addInstructorResult = parent::addStaffInstructor($paramIn);
                $this->logger->write_log('addStaffInstructor:' . json_encode($addInstructorResult), 'info');
            }
        }

        if (isset($paramIn['instructor_id']) && $paramIn['delivery_code'] &&
            isset($paramIn['store_id']) && $paramIn['store_id']) {
            $entryInfo = HrEntryModel::findFirstByStaffId($paramIn['staff_info_id']);
            if (empty($entryInfo)) {
                throw new BusinessException('invalid data');
            }
            $entryInfo->delivery_code = implode(',', $paramIn['delivery_code']);
            $entryInfo->save();
            try {
                $paramsArray = [
                    'staff_id'       => $paramIn['staff_info_id'],
                    'operator_id'    => $paramIn['user']['staff_id'],
                    'store_id'       => $paramIn['store_id'],
                    'delivery_codes' => $paramIn['delivery_code'],
                ];
                $server = new DeliveryCodeServer();
                $rest = $server->bindDeliveryCode($paramsArray);
                $this->logger->write_log('addStaffInstructor bind delivery code' . json_encode($paramsArray) . json_encode($rest) , 'info');
            } catch (\Exception $e) {
                $this->logger->write_log('bindDeliveryCodee err' . $e->getMessage());
            }
        }

        return $this->checkReturn(['data' => true]);
    }

    /**
     * @param $params
     * @return array
     */
    public function getDeliveryCodeInfo($params = [])
    {
        //是否显示派件码
        //仅PH存在派件码 职位bike courier[13] / van courier[110] / tricycle courier[1000]、并且是个人代理
        $isShowDeliveryCode = false;
        $deliverCodeStoreId = '';
        if ($params['status'] == HrEntryModel::STATUS_TO_BE_EMPLOYED) {

            //待入职以入职前的HC的职位、雇佣类型为准
            if ($params['hire_type'] == HrHcModel::HIRE_TYPE_CONTRACT_LABOUR && in_array($params['position_id'], $this->getShowDeliveryCodeJobTitle())) {
                $isShowDeliveryCode = true;
                $deliverCodeStoreId = $params['worknode_id'];
            }

        } else if ($params['status'] == HrEntryModel::STATUS_EMPLOYED) {
            $staffInfoId = $params['staff_id'];
            $staffInfo = $this->getStaffInfo(['staff_info_id' => $staffInfoId], 'staff_info_id,sys_store_id,job_title,hire_type');

            //已入职以入职后的职位、雇佣类型为准
            if (!empty($staffInfo) && $staffInfo['hire_type'] == HrHcModel::HIRE_TYPE_CONTRACT_LABOUR &&
                in_array($staffInfo['job_title'], $this->getShowDeliveryCodeJobTitle())) {
                $isShowDeliveryCode = true;
                $deliverCodeStoreId = $staffInfo['sys_store_id'];
            }
        } else {
            //未入职以入职前的HC的职位、雇佣类型为准
            if ($params['hire_type'] == HrHcModel::HIRE_TYPE_CONTRACT_LABOUR && in_array($params['position_id'], $this->getShowDeliveryCodeJobTitle())) {
                $isShowDeliveryCode = true;
            }
        }
        if (!empty($deliverCodeStoreId)) {
            $deliveryCodeInfo = $this->getDeliveryCode($deliverCodeStoreId);
        }

        //获取派件码显示
        $entryInfo = HrEntryModel::findFirstByEntryId($params['entry_id']);
        if (!empty($entryInfo)) {
            $deliveryCode = $entryInfo->delivery_code;
        }

        return [
            'is_show_delivery_code' => $isShowDeliveryCode,
            'delivery_code_info'    => $deliveryCodeInfo ?? [],
            'delivery_code'         => $deliveryCode ?? '',
        ];
    }
}