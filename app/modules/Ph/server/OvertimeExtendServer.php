<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 11/3/21
 * Time: 8:01 PM
 */

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\WorkflowEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditPermissionModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\StatisticForOvertimeModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Modules\Ph\enums\OvertimeEnums;
use FlashExpress\bi\App\Modules\Ph\library\Enums\HrJobTitleEnums;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\OvertimeExtendServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\VacationServer;

class OvertimeExtendServer extends GlobalBaseServer
{

    public function check_permission($staff_id, $type_list = [])
    {
        $default_v = 'x';//默认皮配值 前提条件是 其他匹配值没有x
        if (empty($staff_id)) {
            return [];
        }
        //获取员工信息
        $staff_re = new StaffRepository($this->lang);
        $staff_info = $staff_re->getStaffPosition($staff_id);
        if (empty($staff_info)) {
            return [];
        }
        if (empty($staff_info['node_department_id'])) {
            return [];
        }

        //获取所属公司id
        $dep_info = SysDepartmentModel::findFirst("id = {$staff_info['node_department_id']} and deleted = 0");
        if (empty($dep_info)) {
            return [];
        }
        //调试
//        $dep_info->ancestry_v3 = '123/222/1/4/32';
//        $dep_info->ancestry_v3 = '999/2';
        $ancestry = explode('/', $dep_info->ancestry_v3);
        if ($ancestry[0] != '999')//错误组织架构数据
        {
            return [];
        }

        //补全5个层级
        $ancestry[0] = empty($ancestry[0]) ? $default_v : $ancestry[0];
        $ancestry[1] = empty($ancestry[1]) ? $default_v : $ancestry[1];
        $ancestry[2] = empty($ancestry[2]) ? $default_v : $ancestry[2];
        $ancestry[3] = empty($ancestry[3]) ? $default_v : $ancestry[3];
        $ancestry[4] = empty($ancestry[4]) ? $default_v : $ancestry[4];


        //所有配置权限记录
        $module_id = AuditPermissionModel::MODULE_P_1;
        $permission_list = AuditPermissionModel::get_permission($module_id);
        if (empty($permission_list))//没有配置对应公司限制条件 不在限制公司之内 都可申请
        {
            if (empty($type_list)) {
                return true;
            }


            return $type_list;
        }

        //获取对应 限制条件的最终值
        $format = [];
        foreach ($permission_list as $v) {//组装 key => value
            $l1 = empty($v['ancestry_1']) ? "{$default_v}" : "{$v['ancestry_1']}";
            $l2 = empty($v['ancestry_2']) ? "_{$default_v}" : "_{$v['ancestry_2']}";
            $l3 = empty($v['ancestry_3']) ? "_{$default_v}" : "_{$v['ancestry_3']}";
            $l4 = empty($v['ancestry_4']) ? "_{$default_v}" : "_{$v['ancestry_4']}";
            $l5 = empty($v['ancestry_5']) ? "_{$default_v}" : "_{$v['ancestry_5']}";
            $job_id = empty($v['job_title_id']) ? "_{$default_v}" : "_{$v['job_title_id']}";
            $grade = empty($v['job_title_grade']) ? "_{$default_v}" : "_{$v['job_title_grade']}";
            $k = $l1 . $l2 . $l3 . $l4 . $l5 . $job_id . $grade;
            $format[$k] = json_decode($v['permission_value']);
        }


        //排列组合 当前员工 的key
        //def 为 默认key
        $combination_arr = [
            [$ancestry[0], "{$default_v}"],
            ['_' . $ancestry[1], "_{$default_v}"],
            ['_' . $ancestry[2], "_{$default_v}"],
            ['_' . $ancestry[3], "_{$default_v}"],
            ['_' . $ancestry[4], "_{$default_v}"],
            ['_' . $staff_info['job_title'], "_{$default_v}"],//职位
            ['_' . $staff_info['job_title_grade_v2'], "_{$default_v}"],//职级

        ];
        $staff_key_arr = combination($combination_arr);
        $staff_key_arr = array_unique($staff_key_arr[0]);
        //获取 匹配上的 并且 默认值数量最少的
        $correct_k = [];
        $num = count($combination_arr) + 1;//默认 x 数量 用x 拆分 num 为数量+1
        $final_value = [];//最终返回的 权限值
        if (!empty($staff_key_arr)) {
            //获取所有匹配上的key
            foreach ($format as $key => $val) {
                if (in_array($key, $staff_key_arr)) {
                    $correct_k[] = $key;
                }
            }
            $cks = [];
            $k = '';
            foreach ($correct_k as $ck => $item) {
                $n = explode($default_v, $item);
                if ($num > count($n)) {
                    $final_value = $item;
                    $num = count($n);
                    $k = $ck;
                } else {
                    if ($num == count($n)) {
                        $cks[] = $ck;
                        $cks[] = $k;
                    }
                }
            }
            //看数量相同的 是不是只有一个
            if (!empty($cks)) {
                $check_correct = [];
                foreach ($cks as $ck) {
                    $check_correct[] = $correct_k[$ck];
                }
                $this->logger->write_log("ot_check_permission {$staff_id} " . json_encode($correct_k), 'info');
                $same_value = get_final_key($check_correct, $default_v);
                //相同数的 和最终key 比对 default的 数量  取默认值最少的
                $final_value = count(explode($default_v, $final_value)) > count(explode($default_v,
                    $same_value)) ? $final_value : $same_value;
            }

        }
        $this->logger->write_log("ot_check_permission {$staff_id} final_value {$final_value} end", 'info');
        if (empty($final_value))//一个都没匹配上 返回空
        {
            return [];
        }
        $final_value = $format[$final_value];

        if (empty($type_list)) {
            //入口按钮权限判断 返回bool
            return empty($final_value) ? false : true;
        }
        $return = [];
        $data = array_column($type_list, null, 'code');
        foreach ($final_value as $type) {
            $return[] = $data[$type];
        }
        return $return;
    }


    //新增的逻辑验证
    public function extend_check($param, $user_info)
    {
        $config_hour = $this->config->application->add_hour;
        $staff_id = $user_info['staff_info_id'];
        $date = date('Y-m-d', strtotime($param['date_at']));
        //网点类型为HUB、B-HUB以及OS的员工  推9小时 其他 推10小时
        //os  后来修改为 os 网点类型  8
        $add_hour = 10;
        //网点类型 hub  8  b-hub 12
        $limit_store = [enums::$stores_category['hub'],enums::$stores_category['bhub'],enums::$stores_category['os'],enums::$stores_category['ffm']];

        if ($user_info['organization_type'] == 1) {
            $staff_re = new StaffRepository($this->lang);
            $store_info = $staff_re->getStaffStoreInfo($user_info['sys_store_id']);
            if (!empty($store_info) && in_array($store_info['category'], $limit_store)) {
                $add_hour = 9;
            }
        }
        //获取请假记录 如果加班日当天 存在下午请假
        $leave_re = new AuditRepository($this->lang);
        $leave_info = $leave_re->get_leave_date($staff_id, $date, $date);
        //如果是请上午假 加5小时 其他情况不让申请ot 休息日类型假期 剔除
        if (!empty($leave_info) && $leave_info[0]['leave_type'] != 15) {
            if ($leave_info[0]['type'] != 1) {
                throw new ValidationException($this->getTranslation()->_('overtime_leave_limit'));

            }
            $add_hour = ($add_hour == 10) ? 5 : 4.5;
        }

        //没有班次信息 不让申请
        if (empty($param['shift_info'])) {
            throw new ValidationException($this->getTranslation()->_('no_shift_notice'));
        }

        //如果 没打上班卡 不让申请
        $att_info = StaffWorkAttendanceModel::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
        if (empty($att_info)) {
            //没上班卡 判断是否出差 取出差打卡 上班卡信息
            $att_info = StaffAuditReissueForBusinessModel::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
            if (empty($att_info)) {
                throw new ValidationException($this->getTranslation()->_('overtime_att_start'));
            }
            $att_info = $att_info->toArray();
            $att_info['started_at'] = $att_info['start_time'];
        } else {
            $att_info = $att_info->toArray();
        }

        if (empty($att_info['started_at'])) {
            throw new ValidationException($this->getTranslation()->_('overtime_att_start'));
        }

        //去秒数
        $att_info['started_at'] = date('Y-m-d H:i:00', strtotime("{$att_info['started_at']}"));

        //通过日期判断新旧班次
        $start = $param['shift_info']['start'];

        //跟班次比对 如果是迟到 加班开始时间 应该在 迟到小时+1小时 时间整点
        $shift_start_time = strtotime("{$date} {$start}");
        $card_time = strtotime($att_info['started_at']) - $this->second + ($config_hour * 3600);
        //没迟到  取班次时间整点 加对应的小时数
        $limit_start = date('Y-m-d H:i:s', $shift_start_time + ($add_hour * 3600));
        if ($card_time > $shift_start_time) {//如果迟到 取打卡时间 加1小时 再加上对应的小时数
            $shift_i = date("i", $shift_start_time);//取班次的分钟
            $limit_start = date("Y-m-d H:{$shift_i}:00", $card_time + 3600);
            $limit_start = date('Y-m-d H:i:s', strtotime($limit_start) + ($add_hour * 3600));
        }

        $ot_start_time = date('Y-m-d H:i:s', strtotime($param['start_time']));
        $l1 = date('Y-m-d H:i:s', $shift_start_time);
        $l2 = date('Y-m-d H:i:s', $card_time);
        $this->logger->write_log("{$staff_id} add_hour {$add_hour},shift_start_time {$l1},card_time(-59) {$l2},limit_start {$limit_start},ot_start_time {$ot_start_time} ",
            'info');
        /*刘佳雪需求*/
        if (!isset($param['is_bi']) || empty($param['is_bi'])) {
            if ($ot_start_time < $limit_start) {
                throw new ValidationException($this->getTranslation()->_('overtime_forbidden'));
            }

            //针对 nw 部门 网点员工 增加限制 申请时间不能超过 班次结束时间 https://flashexpress.feishu.cn/docx/doxcnfR0AGGp7SPFD79cJVxsbse
            $phNwId = enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH;
            if ($user_info['sys_department_id'] == $phNwId && $user_info['organization_type'] == 1) {
                if (strtotime(date('Y-m-d H:i:00')) > strtotime($param['shift_info']['end_datetime'])) {
                    throw new ValidationException($this->getTranslation()->_('ot_in_shift_notice'));
                }
            }

        }

        return $this->checkReturn([]);
    }


    /**
     * @param $staffId
     * @param $date
     * @param $start_time 时间戳
     * @param $end_time
     * @param $type
     * @return array
     */
    public function check_ot_record($staffId, $date, $start_time, $end_time, $type)
    {
        // 互斥 https://flashexpress.feishu.cn/docx/Jgl5d7l4io6pTbxsQMHcdMIfnfL
        $start_time = date('Y-m-d H:i:s', $start_time);
        $end_time = date('Y-m-d H:i:s', $end_time);
        //检查是否有重复记录
        $type_arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];
        //互斥逻辑
        if($type == HrOvertimeModel::OVERTIME_1){
            //工作日加班 所有互斥
        }
        if(in_array($type,[HrOvertimeModel::OVERTIME_2,HrOvertimeModel::OVERTIME_3])){
            //2，3 为一组 和其他所有 互斥
            $type_arr = array_diff($type_arr,[2,3]);
        }
        if(in_array($type,[HrOvertimeModel::OVERTIME_4,HrOvertimeModel::OVERTIME_5])){
            $type_arr = array_diff($type_arr,[4,5]);
        }
        if(in_array($type,[HrOvertimeModel::OVERTIME_6,HrOvertimeModel::OVERTIME_7])){
            $type_arr = array_diff($type_arr,[6,7]);
        }
        if(in_array($type,[HrOvertimeModel::OVERTIME_8,HrOvertimeModel::OVERTIME_9])){
            $type_arr = array_diff($type_arr,[8,9]);
        }
        if(in_array($type,[HrOvertimeModel::OVERTIME_10,HrOvertimeModel::OVERTIME_11])){
            $type_arr = array_diff($type_arr,[10,11]);
        }
        //把本次申请类型加上
        $type_arr = array_merge($type_arr,[$type]);

        $exist = HrOvertimeModel::find(
            [
                'conditions' => "state in (1,2) and staff_id = {$staffId}  and date_at = '{$date}' and type in({type_arr:array})",
                'bind' => [
                    'type_arr' => array_values($type_arr),
                ],
                'columns' => 'overtime_id',
            ]
        )->toArray();
        if (!empty($exist)) {
            throw new ValidationException($this->getTranslation()->_('5102'));
        }

        if (!in_array($type, [2, 3, 4, 5, 6, 7, 8, 9, 10, 11])) {//1。5倍ot 下面不需要校验
            return $this->checkReturn([]);
        }
        $where = "state in (1,2) and staff_id = {$staffId}  and date_at = '{$date}' 
                          and (
                            (start_time < '{$start_time}' and end_time > '{$start_time}') or 
                            (start_time < '{$end_time}' and end_time > '{$end_time}') or 
                            (start_time < '{$start_time}' and end_time > '{$end_time}') or 
                            (start_time >= '{$start_time}' and end_time <= '{$end_time}') 
                          )";

        switch ($type) {
            case 2:
                $where .= " and type = 3";
                break;
            case 3:
                $where .= " and type = 2";
                break;
            case 4:
                $where .= " and type = 5";
                break;
            case 5:
                $where .= " and type = 4";
                break;
            case 6:
                $where .= " and type = 7";
                break;
            case 7:
                $where .= " and type = 6";
                break;
            case 8:
                $where .= " and type = 9";
                break;
            case 9:
                $where .= " and type = 8";
                break;
            case 10:
                $where .= " and type = 11";
                break;
            case 11:
                $where .= " and type = 10";
                break;
            default:
                break;
        }
        $check = HrOvertimeModel::find([
            'conditions' => $where,
            'bind' => [
                'type_arr' => $type_arr,
            ],
            'columns' => 'overtime_id',
        ])->toArray();
        $this->logger->write_log("ot_check_exist {$staffId} start_time {$start_time},end_time {$end_time} ", 'info');

        if (!empty($check)) {
            throw new ValidationException($this->getTranslation()->_('5102'));
        }

        return $this->checkReturn([]);
    }

    public function getReference($staffId, $param)
    {
        $type = $param['type'];
        $date = $param['date_at'];
        $references = $detail = $extend = [];
        $staffInfo = (new StaffRepository())->getStaffPosition($staffId);
        $return = [$references, $extend, $detail];

        //获取参考数据 审批流 和 业务逻辑限制 用 只有 nw 部门 类型1 快递员 类型2 dc officer
        $envModel = new SettingEnvServer();
        $setting_code = ['dept_network_management_id', 'parcel_job_title', 'ph_other_job_title'];
        $setting_val = $envModel->listByCode($setting_code);
        if (!empty($setting_val)) {
            $setting_val = array_column($setting_val, 'set_val', 'code');
        }

        $nw_id = $setting_val['dept_network_management_id'] ?? '';
        if (!empty($nw_id) && $staffInfo['sys_department_id'] == $nw_id) {
            if ($staffInfo['sys_store_id'] == '-1')//非网点员工 不显示 额外数据
            {
                return $return;

            }
            $courier_job = empty($setting_val['parcel_job_title']) ? [] : explode(',',
                $setting_val['parcel_job_title']);
            $other_job = empty($setting_val['ph_other_job_title']) ? [] : explode(',',
                $setting_val['ph_other_job_title']);

            //只有 快递员职位 或者 dc o 才计算数据
            if (!in_array($staffInfo['job_title'],
                    $courier_job) && $staffInfo['job_title'] != enums::$job_title['dc_officer']) {
                return $return;

            }
            $storeInfo = SysStoreModel::findFirst([
                'conditions' => 'id = :store_id:',
                'bind' => ['store_id' => $staffInfo['sys_store_id']],
            ]);
            $references['store_name'] = $this->getStoreDetailName($staffInfo['sys_store_id']);

            $ot_re = new OvertimeRepository($this->timezone);
            //获取该员工一个月的加班时长
            $start_month = date("Y-m-01", strtotime($date));
            $end_month = date('Y-m-d', strtotime("{$start_month} last day of "));
            $references['staff_duration'] = $ot_re->get_duration($staffId, $start_month, $end_month, $type);

            //当天 应派件数量 fbi 接口 https://flashexpress.feishu.cn/docx/doxcneuGBVPlIQ8WPfSVaaJJHja
            $param['store_id'] = $staffInfo['sys_store_id'];
            $param['date'] = $date;
            $ac = new ApiClient('bi_rpcv2', '', 'dc.get_dc_should_delivery_count', $this->lang);
            $ac->setParams($param);
            $ac_result = $ac->execute();
            $this->logger->write_log("delivery today num {$staffInfo['sys_store_id']}_{$date} request " . json_encode($param,
                    JSON_UNESCAPED_UNICODE) . " res " . json_encode($ac_result, JSON_UNESCAPED_UNICODE) . " ", 'info');

            $references['should_delivery_today'] = 0;
            if (!empty($ac_result) && isset($ac_result['result']['data'])) {
                $references['should_delivery_today'] = $ac_result['result']['data'];
            }

            //nw快递员
            $rate_job_title = [];
            $parcel_type = '';
            if (!empty($courier_job) && in_array($staffInfo['job_title'], $courier_job)) {
                $parcel_type = 'courier';
                $rate_job_title = $courier_job;
            }

            //nw dc officer
            if ($staffInfo['job_title'] == enums::$job_title['dc_officer']) {//37   DC Supervisor 16 Truck Driver 1194
                $parcel_type = 'dc_officer';
                $rate_job_title = [enums::$job_title['dc_officer']];

            }
            //出勤人数 出勤率 相关
            $rate_data = $this->attendance_rate($date, $rate_job_title, $staffInfo['sys_store_id']);
            $references['store_job_staff_num'] = 0;//对应日期 所属网点 指定职位的 在职人数
            $references['attendance_rate'] = "0/0";//出勤率

            if (!empty($rate_data)) {
                //在职快递员总人数
                $references['store_job_staff_num'] = count($rate_data['in_staff']);
                //出勤率
                $att_num = count($rate_data['all_num']);
                $staff_num = count($rate_data['in_staff']) + count($rate_data['out_staff']);
                $rate = empty($staff_num) ? 0 : round($att_num / $staff_num, 2) * 100;
                $references['attendance_rate'] = "{$att_num}/{$staff_num}({$rate}%)";
            }

            //3天 网点 人效
            $start_date = date('Y-m-d', strtotime("{$date} -3 day"));
            $end_date = date('Y-m-d', strtotime("{$date} -1 day"));
            $references['store_rate'] = $this->effect($start_date, $end_date, [$staffInfo['sys_store_id']],
                $rate_job_title, $parcel_type);

            //3天 片区人效
            $piece_id = $storeInfo->manage_piece;
            $references['piece_rate'] = 0;
            if (!empty($piece_id)) {
                $piece_stores = SysStoreModel::find([
                    'columns' => 'id',
                    'conditions' => 'manage_piece = :piece_id: and state = 1',
                    'bind' => ['piece_id' => $piece_id],
                ])->toArray();
                $piece_stores = empty($piece_stores) ? [] : array_column($piece_stores, 'id');
                $references['piece_rate'] = $this->effect($start_date, $end_date, $piece_stores, $rate_job_title,
                    $parcel_type);
            }

            //获取整个网点 指定职位的 加班时长
            $all_job_title = array_merge($courier_job, $other_job);
            $all_job_staff = HrStaffInfoModel::find([
                'columns' => 'staff_info_id',
                'conditions' => "sys_store_id = :store_id: and state in (1,3) and is_sub_staff = 0 and job_title in ({all_job:array})",
                'bind' => [
                    'store_id' => $staffInfo['sys_store_id'],
                    'all_job' => array_values($all_job_title),
                ],
            ])->toArray();
            $all_job_staff = empty($all_job_staff) ? [] : array_column($all_job_staff, 'staff_info_id');
            $references['store_duration'] = $ot_re->get_duration($all_job_staff, $start_month, $end_month, $type);
        }

        //为了保持 相同的返回值 先这样
        return [$references, $extend, $detail];
    }


    /**
     * @param $date 统计日期
     * @param $job_titles 统计指定职位
     * @param $store_id 统计网点
     * @return array  all_num 所有打卡员工 in_staff 所属网点内职位员工总数 out_staff 外来打卡人数
     */
    public function attendance_rate($date, $job_titles, $store_id)
    {

        if (empty($job_titles) || empty($store_id)) {
            return [];
        }
        //所属网点的 快递员职位的 人数 分母1
        $store_courier_data = HrStaffInfoModel::find([
            'columns' => 'staff_info_id',
            'conditions' => "sys_store_id = :store_id: and state = 1 and formal = 1 and is_sub_staff = 0 and job_title in ({courier_job:array})",
            'bind' => [
                'store_id' => $store_id,
                'courier_job' => $job_titles,
            ],
        ])->toArray();
        //所属网点的 所有在职 快递员 包括外协
        $store_courier_staff = empty($store_courier_data) ? [] : array_column($store_courier_data, 'staff_info_id');


        //出勤率 : 所有出勤人数 / 快递员总人数（所属人+外来人） 不包含 子账号(把支援的重复数据 排除掉)
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['a' => StaffWorkAttendanceModel::class]);
        $builder->columns('a.staff_info_id');
        $builder->andWhere('a.attendance_date = :date:', ['date' => $date]);
        $builder->andWhere('started_store_id = :store_id: or end_store_id = :store_id:', ['store_id' => $store_id]);
        $builder->inWhere('a.job_title', $job_titles);
        $all_attendance_data = $builder->getQuery()->execute()->toArray();

        $out_staff = [];
        if (!empty($all_attendance_data)) {
            //分子
            $all_attendance_data = array_column($all_attendance_data, 'staff_info_id');
            //外来打卡人员 分母2
            $out_staff = array_values(array_diff($all_attendance_data, $store_courier_staff));
        }

        $return = ['all_num' => $all_attendance_data, 'in_staff' => $store_courier_staff, 'out_staff' => $out_staff];
        $this->logger->write_log("attendance_rate {$date} {$store_id} " . json_encode($return), 'info');
        return $return;
    }

    /**
     * 和attendance_num方法逻辑保持一致
     * @param $date
     * @param $job_titles
     * @param $stores
     * @return array
     */
    public function attendance_num_statistic($date, $job_titles, $stores): array
    {
        if (empty($date) || empty($stores)) {
            return [];
        }

        if (!is_array($stores)) {
            $stores = [$stores];
        }

        $str_stores     = getIdsStr($stores);
        $str_job_titles = getIdsStr($job_titles);
        $sql            = "SELECT count(a.staff_info_id) as total, s.sys_store_id AS sys_store_id FROM hr_staff_info AS s  INNER JOIN staff_work_attendance AS a ON s.staff_info_id = a.staff_info_id WHERE a.attendance_date = '{$date}' AND (a.started_store_id IN ($str_stores) OR a.end_store_id IN ($str_stores)) AND a.job_title IN ($str_job_titles) AND s.is_sub_staff = 0 group by s.sys_store_id";
        $stat           = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return array_column($stat, 'total', 'sys_store_id');
    }
    /**
     * 获取指定条件下 的出勤人数总计
     * 如果修改请同步修改 attendance_num_statistic method
     * @param $date --ot 表单 日期
     * @param $job_titles --指定职位
     * @param $stores --指定网点 数组集合
     */
    public function attendance_num($date, $job_titles, $stores)
    {
        if (empty($date) || empty($stores)) {
            return [];
        }

        if (!is_array($stores)) {
            $stores = [$stores];
        }

        $str_stores     = getIdsStr($stores);
        $str_job_titles = getIdsStr($job_titles);
        $sql            = "SELECT a.staff_info_id AS staff_info_id, s.sys_store_id AS sys_store_id FROM hr_staff_info AS s  INNER JOIN staff_work_attendance AS a ON s.staff_info_id = a.staff_info_id WHERE a.attendance_date = '{$date}' AND (a.started_store_id IN ($str_stores) OR a.end_store_id IN ($str_stores)) AND a.job_title IN ($str_job_titles) AND s.is_sub_staff = 0";
        return $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);



        $builder = $this->modelsManager->createBuilder();
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->join(StaffWorkAttendanceModel::class, 's.staff_info_id = a.staff_info_id', 'a');
        $builder->columns('a.staff_info_id,s.sys_store_id');
        $builder->andWhere('a.attendance_date = :date:', ['date' => $date]);
        $builder->andWhere('started_store_id in ({store_id:array}) or end_store_id in ({store_id:array})',
            ['store_id' => $stores]);
        $builder->inWhere('a.job_title', $job_titles);
        $builder->andWhere('s.is_sub_staff = 0');
        $all_attendance_data = $builder->getQuery()->execute()->toArray();

        return $all_attendance_data;
    }


    /**
     * https://flashexpress.feishu.cn/docx/doxcnqG5vA5Yw4TE8JVe0xDU6Fg
     * 获取大区 片区 指定时间区间内 人效 （不包含补卡，不含支援、外协）
     * @param $start_date
     * @param $end_date
     * @param $store_ids 获取网点 数据
     * @param array $job_titles 指定的职位
     * @param string $type 快递员 取派件  dc  取揽派件
     * @return mixed
     */
    public function effect($start_date, $end_date, $store_ids, $job_titles, $type = 'courier')
    {
        if (empty($store_ids) || empty($job_titles)) {
            return 0;
        }

        //不同 职位 取不同的 数据
        $parcel_column = 'date_at,sum(delivery_num + pickup_num) as num';
        if ($type == 'dc_officer') {
            $parcel_column = 'date_at,sum(delivery_num) as num';
        }

        //出勤人数 分母
        $attendance_num = StaffWorkAttendanceModel::find([
            'columns' => 'attendance_date,count(1) as num',
            'conditions' => 'attendance_date between :start: and :end: and job_title in ({jobs:array}) and organization_id in ({stores:array})',
            'bind' => ['start' => $start_date, 'end' => $end_date, 'jobs' => $job_titles, 'stores' => $store_ids],
            'group' => 'attendance_date',
        ])->toArray();

        $attendance_num = empty($attendance_num) ? [] : array_column($attendance_num, 'num', 'attendance_date');

        //揽派件数 statistic_for_overtime 分子
        $parcel_num = StatisticForOvertimeModel::find([
            'columns' => $parcel_column,
            'conditions' => 'date_at between :start: and :end:  and store_id in ({stores:array})',
            'bind' => ['start' => $start_date, 'end' => $end_date, 'stores' => $store_ids],
            'group' => 'date_at',
        ])->toArray();

        $parcel_num = empty($parcel_num) ? [] : array_column($parcel_num, 'num', 'date_at');

        //人效 总和
        $step = $start_date;
        $i = 0;
        $in_all = 0;
        while ($step <= $end_date) {
            $i++;

            $up = $parcel_num[$step] ?? 0;
            $low = $attendance_num[$step] ?? 0;
            if (!empty($low)) {
                $in_all += round($up / $low, enums::ROUND_NUM);
            }
            $step = date('Y-m-d', strtotime("{$step} +1 day"));
        }

        //除以 天数 保留一位小数点
        return round($in_all / $i, 1);
    }


    //获取新增的 nw 对应的 detail 信息 最终审批之前 动态调接口 审批之后 固化数据
    //针对 SP、BDC和PDC网点类型的
    //DC Officer+DC supervisorBranch Supervisor+Assistant DC Supervisor职位的网点员工：
    public function nwNewDetail($staffInfo, $overtimeInfo, $extParam)
    {
        $date = $overtimeInfo['date_at'];
        $storeInfo = $extParam['storeInfo'];
        $references = json_decode($overtimeInfo['references'], true);
        $limit_job_titles = HrJobTitleEnums::$limit_ot_job_title;

        $detailInfo = [];
        $storeCategoryList = [enums::$stores_category['sp'], enums::$stores_category['bdc']];

        $auditInfo = AuditApplyModel::findFirst([
            'conditions' => 'biz_type = :biz_type: and biz_value = :biz_value:',
            'bind' => [
                'biz_type' => AuditListEnums::APPROVAL_TYPE_OVERTIME,
                'biz_value' => $overtimeInfo['overtime_id'],
            ],
        ]);
        $auditInfo = empty($auditInfo) ? [] : $auditInfo->toArray();

        //延时审批已经创建审批流，并保存了人效相关数据
        if ($auditInfo['delay_state'] == WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_HAS_CREATED) {
            if (in_array($storeInfo['category'], $storeCategoryList)) {
                $detailInfo['store_today_effect'] = $references['store_today_effect'] ?? '';//当天网点效率
                $detailInfo['piece_today_effect'] = $references['piece_today_effect'] ?? '';
            }

            if ($storeInfo['category'] == enums::$stores_category['pdc']) {
                $detailInfo['store_today_effect'] = $references['store_today_effect'] ?? '';//当天网点效率
                $detailInfo['pdc_effect'] = $references['pdc_effect'] ?? '';
                $detailInfo['pdc_seal_rate'] = $references['pdc_seal_rate'] ?? '';
            }
            return $detailInfo;
        }

        //fbi 接口 https://yapi.flashexpress.pub/project/59/interface/api/63050
        $param['store_id'] = $storeInfo['id'];
        $param['store_category'] = $storeInfo['category'];
        $param['piece_id'] = $storeInfo['manage_piece'];
        $param['date'] = $date;
        $this->logger->write_log("nwNewDetail {$staffInfo['staff_info_id']} create request " . json_encode($param,
                JSON_UNESCAPED_UNICODE), 'info');

        $ac = new ApiClient('bi_rpcv2', '', 'dc.get_sp_pdc_rate_date', $this->lang);
        $ac->setParams($param);
        $ac_result = $ac->execute();
        $this->logger->write_log("nwNewDetail {$staffInfo['staff_info_id']} create request " . json_encode($param,
                JSON_UNESCAPED_UNICODE) . " res:" . json_encode($ac_result, JSON_UNESCAPED_UNICODE) . " ", 'info');

        if (empty($ac_result) || !isset($ac_result['result'])) {
            return [];
        }

        $data = $ac_result['result']['data'][0];

        $currentInfo = $data['store_data'];
        if (empty($currentInfo)) {
            return [];
        }

        //单个网点出勤人数
        //todo 如果 这个 使用的 $limit_job_titles 变更 请同步修改 attendance_num_statistic 方法的参数
        $store_num = $this->attendance_num($date, $limit_job_titles, $storeInfo['id']);
        $store_num = count(array_column($store_num, 'staff_info_id'));

        //sp网点类型的数据结构
        //   - 所在网点当天人效=（所在网点当天揽收量+所在网点当天妥投量 ）/ 所在网点当天【DC Officer+DCBranch Supervisor】出勤人数，单位：件/人，四舍五入保留一位小数
        //    - 所在片区当天平均人效= （所在片区当天揽收量+所在片区当天妥投量） / 所在片区当天【DC Officer+DCBranch Supervisor】出勤人数（所在片区的网点只取SP类型的网点），单位：件/人，四舍五入保留一位小数
        //    - 出勤人数：在OT申请日员工所属网点至少有一次打卡记录的【DC Officer+DC supervisorBranch Supervisor】人数（不包含补卡，含支援、外协）

        if (in_array($storeInfo['category'], $storeCategoryList)) {
            //单个网点 揽派件数
            $parcel_num = $currentInfo['store_pickup_count'] + $currentInfo['store_delivery_count'];
            $detailInfo['store_today_effect'] =  $this->cal_store_today_effect($parcel_num,$store_num);//当天网点效率
            $detailInfo['piece_today_effect'] = $this->pieceEffect($date, $storeInfo, $data['piece_data']);
        }

        //pdc 类型的 数据结构
        //    - 所在网点当天人效=网点揽件总操作量/所在网点当天【DC Officer+DC supervisor+Assistant DC supervisor】出勤人数 单位：件/人，四舍五入保留一位小数
        //    - 所有PDC网点当天平均人效= 所有PDC网点当天人效之和 / PDC网点总数，单位：件/人，四舍五入保留一位小数
        //    - 所在网点集包率：取自FBI-DC报表管理-DCSP网点集包率，格式为百分比，四舍五入保留整数
        //    - 出勤人数：在OT申请日员工所属网点至少有一次打卡记录的DC Officer+DC supervisor人数（不包含补卡，含支援、外协）

        if ($storeInfo['category'] == enums::$stores_category['pdc']) {

            //接口文档 https://flashexpress.feishu.cn/docx/MXhodaIIoovqNLxSqXzcv8m8nUh
            //网点揽件总操作量
            $param_pdc['store_id']   = $storeInfo['id'];
            $param_pdc['begin_date'] = $date;
            $param_pdc['end_date']   = $date;
            $this->logger->write_log("nwNewDetail {$staffInfo['staff_info_id']} create request " . json_encode($param_pdc, JSON_UNESCAPED_UNICODE), 'info');

            $ac = new ApiClient('ard_api', '', 'deliverycount.get_latest_delivery_by_store_date', $this->lang);
            $ac->setParams($param_pdc);
            $ac_result = $ac->execute();
            $this->logger->write_log("nwNewDetail {$staffInfo['staff_info_id']} create request " . json_encode($param_pdc,
                    JSON_UNESCAPED_UNICODE) . " res:" . json_encode($ac_result, JSON_UNESCAPED_UNICODE) . " ", 'info');

            if (empty($ac_result) || !isset($ac_result['result'])) {
                return [];
            }


            $storeTotalOperation = $ac_result['result']['data'][0];
            //网点揽件总操作量
            $parcel_num = isset($storeTotalOperation['pickup_operation_count']) && $storeTotalOperation['pickup_operation_count']
                ? $storeTotalOperation['pickup_operation_count']
                : 0;
            $detailInfo['store_today_effect'] = $this->cal_store_today_effect($parcel_num,$store_num);//当天网点效率

            //获取PDC操作量
            $params_pdc['piece_id']       = '';
            $params_pdc['store_category'] = $storeInfo['category'];
            $params_pdc['begin_date']     = $date;
            $params_pdc['end_date']       = $date;
            $piece_data = $this->getLatestDelivery($params_pdc);
            $piece_data = array_column($piece_data, null,'store_id');

            //所有PDC网点当天平均人效= 所有PDC网点当天人效之和 / PDC网点总数，单位：件/人，四舍五入保留一位小数
            $detailInfo['pdc_effect'] = $this->pdcEffect($date, $piece_data);

            //所在网点集包率
            $param_pdc_rate['store_id']   = $storeInfo['id'];
            $param_pdc_rate['begin_date'] = $date;
            $param_pdc_rate['end_date']   = $date;
            $this->logger->write_log("nwNewDetail {$staffInfo['staff_info_id']} create request " . json_encode($param_pdc_rate, JSON_UNESCAPED_UNICODE), 'info');

            $ac = new ApiClient('ard_api', '', 'deliverycount.get_latest_pack_rate', $this->lang);
            $ac->setParams($param_pdc_rate);
            $pdc_seal_rate = $ac->execute();
            $this->logger->write_log("nwNewDetail {$staffInfo['staff_info_id']} create request " . json_encode($param_pdc_rate,
                    JSON_UNESCAPED_UNICODE) . " res:" . json_encode($pdc_seal_rate, JSON_UNESCAPED_UNICODE) . " ", 'info');

            if (empty($pdc_seal_rate) || !isset($pdc_seal_rate['result'])) {
                return [];
            }
            $detailInfo['pdc_seal_rate'] = $pdc_seal_rate['result']['data'][0]['pack_rate'] ?? 0;
        }

        return $detailInfo;
    }

    public function cal_store_today_effect($parcel_num,$store_num)
    {
        $store_effect = 0;
        if (!empty($store_num)) {
            $store_effect = round($parcel_num / $store_num, 1);
        }
        return $store_effect;
    }
    /**
     * 获取片区/指定网点类型的操作量
     * @param $params
     * @return array
     */
    public function getLatestDelivery($params = [])
    {
        $params_area['piece_id']       = $params['piece_id'] ?? '';
        $params_area['store_category'] = $params['store_category'] ?? '';
        $params_area['begin_date']     = $params['begin_date'];
        $params_area['end_date']       = $params['end_date'];
        $this->logger->write_log("nwNewDetail getLatestDelivery create request " . json_encode($params_area, JSON_UNESCAPED_UNICODE), 'info');

        $ac = new ApiClient('ard_api', '', 'deliverycount.get_latest_delivery_by_piece', $this->lang);
        $ac->setParams($params_area);
        $ac_result = $ac->execute();
        $this->logger->write_log("nwNewDetail getLatestDelivery create request " . json_encode($params_area,
                JSON_UNESCAPED_UNICODE) . " res:" . json_encode($ac_result, JSON_UNESCAPED_UNICODE) . " ", 'info');

        if (empty($ac_result) || !isset($ac_result['result'])) {
            return [];
        }
        return $ac_result['result']['data'] ?? [];
    }

    //整理 片区效率数据
    protected function pieceEffect($date, $storeInfo, $pieceData)
    {
        $limit_job_titles = HrJobTitleEnums::$limit_ot_job_title;
        //片区对应网点
        $piece_stores = SysStoreModel::find([
            'columns' => 'id',
            'conditions' => 'category = :sp_category: and manage_piece = :piece_id: and state = 1',
            'bind' => ['sp_category' => $storeInfo['category'], 'piece_id' => $storeInfo['manage_piece']],
        ])->toArray();
        $piece_stores = empty($piece_stores) ? [] : array_column($piece_stores, 'id');

        $att_num = $this->attendance_num($date, $limit_job_titles, $piece_stores);
        $att_num = count(array_column($att_num, 'staff_info_id'));

        if (empty($att_num)) {
            return 0;
        }

        $parcel_num = 0;

        foreach ($pieceData as $store_id => $pieceDatum) {
            if (!in_array($store_id, $piece_stores)) {
                continue;
            }
            $num = $pieceDatum['store_pickup_count'] + $pieceDatum['store_delivery_count'];
            $parcel_num += $num;
        }

        $this->logger->write_log("pieceEffect {$date} {$parcel_num} " . json_encode($piece_stores), 'info');

        return round($parcel_num / $att_num, 1);

    }

    //pdc 网点的 效率
    protected function pdcEffect($date, $pieceData)
    {
        $limit_job_titles = HrJobTitleEnums::$limit_ot_job_title;
        //片区对应网点
        $pdcStores = SysStoreModel::find([
            'columns' => 'id',
            'conditions' => 'category = :pdc_category: and state = 1',
            'bind' => ['pdc_category' => enums::$stores_category['pdc']],
        ])->toArray();

        if (empty($pdcStores)) {
            return 0;
        }

        $pdcStores = array_column($pdcStores, 'id');
        $att_num = $this->attendance_num($date, $limit_job_titles, $pdcStores);
        if (empty($att_num)) {
            return 0;
        }

        //按网点 分组 每个网点有多少出勤工号
        $att_info = [];
        foreach ($att_num as $v) {
            $att_info[$v['sys_store_id']][] = $v['staff_info_id'];
        }

        $allPdcEffect = 0;

        //pdc 所有网点效率之和
        //  - 所有PDC网点当天平均人效= 所有PDC网点当天人效之和 / PDC网点总数，单位：件/人，四舍五入保留一位小数
        foreach ($pdcStores as $store) {
            //该网点 包裹数
            $num = empty($pieceData[$store]) ? 0 : $pieceData[$store]['total'];

            //该网点出勤数
            $att_count = empty($att_info[$store]) ? 0 : count($att_info[$store]);

            //sum
            if (empty($att_count)) {
                continue;
            }
            $allPdcEffect += round($num / $att_count, 1);
        }

        //pdc网点总数
        $pdcCount = count($pdcStores);

        $this->logger->write_log("pdcEffect {$date} {$allPdcEffect} " . json_encode($pdcStores), 'info');

        return round($allPdcEffect / $pdcCount, 1);
    }


    //nw 指定职位 获取对应月份 申请的不带薪假期（只有紧急假）Van Courier、Bike Courier、Tricycle Courier、Truck Driver、DC Officer、 DC Supervisor职位：BY-OT审批详情页所有OT类型新增字段：
    public function getUnpaidLeaveDays($staffInfo,$start,$end){
        $jobTitles = [
            OvertimeEnums::$job_title['van_courier'],
            OvertimeEnums::$job_title['bike_courier'],
            OvertimeEnums::$job_title['tricycle_courier'],
            OvertimeEnums::$job_title['truck_courier'],
            OvertimeEnums::$job_title['dc_officer'],
            OvertimeEnums::$job_title['dc_supervisor'],
        ];
        if(!in_array($staffInfo['job_title'], $jobTitles)){
            return null;
        }

        //非nw  返回null
        if($staffInfo['sys_department_id'] != enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH){
            return null;
        }

        $vacationServer = new VacationServer($this->lang,$this->timezone);
        $monthUnpaidDays = $vacationServer->leaveDaysBetween($staffInfo['staff_info_id'],$start,$end,enums::LEAVE_TYPE_23);
        return $monthUnpaidDays;
    }


}