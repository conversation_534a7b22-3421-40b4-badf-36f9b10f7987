<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Server\QuickOfferServer as BaseCancelQuickOfferServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class QuickOfferServer extends BaseCancelQuickOfferServer
{

    protected $noNeedDriverLicenseJd = [10914];

    /**
     * 差异化字段绑定
     * @param $resumeModel
     * @param $paramIn
     * @param $is_create
     * @return mixed
     */
    protected function bindResumeData($resumeModel, $paramIn, $is_create)
    {
        $resumeModel->first_name            = $paramIn['first_name'];
        $resumeModel->last_name             = $paramIn['last_name'];
        $resumeModel->name                  = str_replace(' ', '', $paramIn['first_name']. $resumeModel->middle_name . $paramIn['last_name']. $resumeModel->suffix_name);
        $resumeModel->residence_house_num   = $paramIn['residence_house_num'];
        $resumeModel->residence_village_num = $paramIn['residence_village_num'];                                                                                                           //村庄号
        $resumeModel->residence_village     = $paramIn['residence_village'];                                                                                                               //村庄
        $resumeModel->residence_street      = $paramIn['residence_street'];                                                                                                                //街道
        $resumeModel->residence_alley       = $paramIn['residence_alley'];                                                                                                                 //巷
        $resumeModel->residence_country     = $paramIn['residence_country'];                                                                                                               //国家
        $resumeModel->residence_government  = $paramIn['residence_government'];                                                                                                            //省份
        $resumeModel->residence_city        = $paramIn['residence_city'];                                                                                                                  //市
        $resumeModel->residence_town        = $paramIn['residence_town'];                                                                                                                  //县
        $resumeModel->residence_postcodes   = $paramIn['residence_postcodes'];
        if ($resumeModel->fit == 1) {
            $resumeModel->register_house_num   = $paramIn['residence_house_num'];
            $resumeModel->register_village_num = $paramIn['residence_village_num'];                                                                                                           //村庄号
            $resumeModel->register_village     = $paramIn['residence_village'];                                                                                                               //村庄
            $resumeModel->register_street      = $paramIn['residence_street'];                                                                                                                //街道
            $resumeModel->register_alley       = $paramIn['residence_alley'];                                                                                                                 //巷
            $resumeModel->register_country     = $paramIn['residence_country'];                                                                                                               //国家
            $resumeModel->register_government  = $paramIn['residence_government'];                                                                                                            //省份
            $resumeModel->register_city        = $paramIn['residence_city'];                                                                                                                  //市
            $resumeModel->register_town        = $paramIn['residence_town'];                                                                                                                  //县
            $resumeModel->register_postcodes   = $paramIn['residence_postcodes'];
        }
        if (!$is_create) {
            $resumeModel->name = str_replace(' ', '',
                $resumeModel->first_name . $resumeModel->middle_name . $resumeModel->last_name . $resumeModel->suffix_name);
        } else {
            $resumeModel->resume_last_operator       = $paramIn['staff_id'];
            $resumeModel->resume_last_operation_time = gmdate('Y-m-d H:i:s');
            $resumeModel->recommender_staff_id       = $paramIn['staff_id'];
            $resumeModel->recommend_store_id         = $paramIn['worknode_id'];
        }
        return $resumeModel;
    }


    /**
     * @param $paramIn
     * @return null
     * @throws BusinessException
     */
    protected function checkAndGetResume($paramIn)
    {
        $t = $this->getTranslation();
        //是否存在于简历库
        $resumeInfo = HrResumeModel::findFirst([
            'conditions' => "phone = :phone: and deleted = 0",
            'bind'       => ['phone' => $paramIn['phone']],
        ]);
        $this->checkEntry($resumeInfo ? $resumeInfo->id : 0,
            $t->_('qo_error_msg_002', ['mobile' => $paramIn['phone']]));
        return $resumeInfo;
    }

    protected function fullParamInData($paramIn)
    {
        $current_nationality = (new SettingEnvServer())->getSetVal('s_f_d_nationality');

        if ($paramIn['residence_country'] != $current_nationality && !empty($paramIn['residence_government_text']) && empty($paramIn['residence_government'])) {
            $paramIn['residence_government'] = $paramIn['residence_government_text'];
        }

        if ($paramIn['residence_country'] != $current_nationality && !empty($paramIn['residence_city_text']) && empty($paramIn['residence_city'])) {
            $paramIn['residence_city'] = $paramIn['residence_city_text'];
        }

        if ($paramIn['residence_country'] != $current_nationality && !empty($paramIn['residence_town_text']) && empty($paramIn['residence_town'])) {
            $paramIn['residence_town'] = $paramIn['residence_town_text'];
        }
        return $paramIn;
    }


    /**
     * 必填字段验证
     * @return string[]
     */

    protected function getDefaultValidationsField(): array
    {
        return [
            "first_name"            => "StrLenGeLe:0,50|>>>:first_name" . $this->getTranslation()->_('4038'),
            "last_name"             => "StrLenGeLe:0,50|>>>:last_name" . $this->getTranslation()->_('4037'),
            "store_id"              => "StrLenGeLe:1,50|>>>:store_id" . $this->getTranslation()->_('4028'),
            "hc_id"                 => "Required|Int",
            "phone"                 => "Required|StrLenGeLe:11,11|>>>:" . $this->getTranslation()->_('4117'),  // 手机号
            "type"                  => 'Required|IntIn:' . implode(',',
                    array_keys(self::$type_setting_key_map)) . '|>>>:type' . $this->getTranslation()->_('reserve_type_error'),
            "expected_arrivaltime"  => 'Required|Date',
            "job_id"                => 'Required|Int',
            "residence_country"     => "StrLenGeLe:1, 3|>>>:residence_country error",      //居住地-国家
            "residence_house_num"   => "StrLenGeLe:0,50|>>>:residence_house_num error",    //居住地门牌号
            "residence_village_num" => "StrLenGeLe:0,50|>>>:residence_village_num error",  //居住地村号
            "residence_village"     => "StrLenGeLe:0,50|>>>:residence_village error",      //居住地村庄
            "residence_alley"       => "StrLenGeLe:0,50|>>>:residence_alley error",        //居住地巷
            "residence_street"      => "StrLenGeLe:0,50|>>>:residence_street error",       //居住地街道
            "residence_government"  => "StrLenGeLe:1,50|>>>:residence_government error",   // 居住地--府
            "residence_city"        => "StrLenGeLe:1,50|>>>:residence_city error",         // 居住地--市
            "residence_town"        => "StrLenGeLe:1,50|>>>:residence_town error",         // 居住地--镇
            "residence_postcodes"   => "StrLenGeLe:1,50|>>>:residence_postcodes error",    //居住地-邮编
        ];
    }


}