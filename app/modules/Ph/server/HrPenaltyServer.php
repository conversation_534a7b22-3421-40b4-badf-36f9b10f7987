<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrPenaltyAppealModel;
use FlashExpress\bi\App\Models\backyard\HrPenaltyDetailModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\HrPenaltyServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\Penalty\BasePenaltyServer;
use FlashExpress\bi\App\Server\StaffServer;

class HrPenaltyServer extends GlobalBaseServer
{

    public $timezone;
    public $lang;
    const PENALTY_MAX_AMOUNT_PER_DAY = 500; //每日处罚最大限额

    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->timezone = $timezone;
        $this->lang     = $lang;

        parent::__construct($lang, $timezone);
    }

    /**
     * @description 发起处罚申诉
     * @param array $paramIn
     * @return array
     * @throws ValidationException
     */
    public function addPenaltyAppeal(array $paramIn = []): array
    {
        //[1]获取参数
        $staffInfoId     = $this->processingDefault($paramIn, 'staff_id');
        $staffStoreId    = $this->processingDefault($paramIn, 'store_id');
        $appealReason    = $this->processingDefault($paramIn, 'appeal_reason');
        $penaltyDetailId = $this->processingDefault($paramIn, 'penalty_detail_id');
        $image           = $this->processingDefault($paramIn, 'image');

        //[2.1]校验处罚ID是否有效
        if (empty($penaltyDetailId)) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $penaltyDetail = HrPenaltyDetailModel::findFirst([
            "id = :penalty_detail_id:",
            "bind" => [
                "penalty_detail_id" => $penaltyDetailId
            ]
        ]);
        if (empty($penaltyDetail)) {
            throw new ValidationException($this->getTranslation()->_('invalid data'));
        }
        if ($penaltyDetail->penalty_money == 0) {
            throw new ValidationException($this->getTranslation()->_('validate_msg_no_need_appeal'));
        }
        if ($penaltyDetail->state == HrPenaltyDetailModel::PENALTY_STATE_INVALID) {
            $HrPenaltyReasonArr = HrPenaltyDetailModel::getPenaltyExpireReasonMap();
            $HrPenaltyReason = $this->getTranslation()->_($HrPenaltyReasonArr[$penaltyDetail->expiry_reason] ?? "");
            throw new ValidationException($this->getTranslation()->_('validate_msg_penalty_has_invalid', ['penalty_reason' => $HrPenaltyReason]));
        }
        $isExistPenaltyAppeal = HrPenaltyAppealModel::findFirst([
            "penalty_detail_id = :penalty_detail_id: and status in({approval_state:array})",
            "bind" => [
                "penalty_detail_id" => $penaltyDetailId,
                "approval_state" => [
                    enums::APPROVAL_STATUS_PENDING,
                    enums::APPROVAL_STATUS_APPROVAL,
                    enums::APPROVAL_STATUS_REJECTED,
                ]
            ]
        ]);
        if (!empty($isExistPenaltyAppeal)) {
            throw new ValidationException($this->getTranslation()->_('validate_msg_exist_appeal_record'));
        }

        //[2.2]校验是否过了申诉期
        //处罚创建时间是0时区时间，应该使用当地时间 + 1天 (因为如果使用0时区时间，则存在创建处罚时间是前一天时间的情况)
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $validateDate = date("Y-m-d", strtotime($penaltyDetail->created_at . "1 day") + $add_hour * 3600);
        if ($validateDate < date("Y-m-d")) {
            throw new ValidationException($this->getTranslation()->_('validate_msg_has_expired'));
        }

        //[3]组织数据插入业务主数据
        $db = $this->getDI()->get('db');
        try {
            $createTime = gmdate("Y-m-d H:i:s", time());

            $db->begin();
            $model = new HrPenaltyAppealModel();
            $model->staff_info_id = $staffInfoId;
            $model->store_id = $staffStoreId ?? '';
            $model->serial_no = sprintf("HPA%s", $this->getRandomId());
            $model->appeal_reason = $appealReason;
            $model->penalty_detail_id = $penaltyDetailId;
            $model->image = json_encode($image, JSON_UNESCAPED_UNICODE);
            $model->created_at = $createTime;
            $result = $model->create();
            $auditId = $result ? $model->id : 0;

            //处罚表同步保存申请时间
            $penaltyDetail->last_appeal_time = $createTime;
            $penaltyDetail->save();

            //创建审批
            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($auditId, AuditListEnums::APPROVAL_TYPE_HR_PENALTY_APPEAL, $staffInfoId);
            if (!$requestId) {
                throw new \Exception('创建审批流失败');
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->write_log("err hr penalty appeal create" . $e->getMessage(), 'info');
            return $this->checkReturn(-3, $e->getMessage());
        }
        return $this->checkReturn(["audit_id" => $auditId]);
    }

    /**
     * @description 审批处罚申诉
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function updatePenaltyAppeal($paramIn = [])
    {
        //[1]获取参数
        $staffId      = $this->processingDefault($paramIn, 'staff_id', 2);
        $auditId      = $this->processingDefault($paramIn, 'audit_id', 2);
        $status       = $this->processingDefault($paramIn, 'status', 2);
        $rejectReason = $this->processingDefault($paramIn, 'reject_reason', 1);
        $rejectReason = addcslashes(stripslashes($rejectReason), "'");
        $approvalReason = $this->processingDefault($paramIn, 'reason', 1);
        $approvalReason = addcslashes(stripslashes($approvalReason), "'");

        //[2]获取详情
        $penaltyAppealInfo = HrPenaltyAppealModel::findFirst($auditId);
        if (empty($penaltyAppealInfo)) {
            throw new ValidationException($this->getTranslation()->_('miles_C100100'), ErrCode::VALIDATE_ERROR);
        }

        //[4]验证
        //[4-1]如果已经是最终状态，啥操作都做不了
        if ($penaltyAppealInfo->status != enums::APPROVAL_STATUS_PENDING) { //已经是最终状态,不能修改
            throw new ValidationException($this->getTranslation()->_('err_msg_status_updated'),
                ErrCode::VALIDATE_ERROR);
        }

        $this->getDI()->get('db')->begin();
        try {
            //同意或者驳回等分开处理
            $server = new ApprovalServer($this->lang, $this->timezone);
            if ($status == enums::APPROVAL_STATUS_APPROVAL) {
                //同意
                $server->approval($auditId, AuditListEnums::APPROVAL_TYPE_HR_PENALTY_APPEAL, $staffId, $approvalReason);
            } else {
                if ($status == enums::APPROVAL_STATUS_REJECTED) {
                    //驳回
                    $server->reject($auditId, AuditListEnums::APPROVAL_TYPE_HR_PENALTY_APPEAL, $rejectReason, $staffId);
                } else {
                    //撤销
                    $server->cancel($auditId, AuditListEnums::APPROVAL_TYPE_HR_PENALTY_APPEAL, $rejectReason, $staffId);
                }
            }

            $this->getDI()->get('db')->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log('update fleet failure:'.$e->getMessage(), 'error');
            return $this->checkReturn(-3, $e->getMessage());
        }
        return $this->checkReturn([]);
    }

    /**
     * @description 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result = HrPenaltyAppealModel::findFirst($auditId);
        if (empty($result)) {
            return false;
        }

        $result = $result instanceof HrPenaltyAppealModel ? $result->toArray() : [];

        //申请人信息
        $staffInfo = (new StaffServer())->get_staff($result['staff_info_id']);
        if($staffInfo['data']){
            $staffInfo = $staffInfo['data'];
        }
        $storeInfo = SysStoreModel::findFirst([
            "id = :store_id:",
            "bind" => [
                "store_id" => $result['store_id']
            ],
            "columns" => "id,name"
        ]);

        $penaltyDetailInfo = HrPenaltyDetailModel::findFirst([
            "id = :penalty_detail_id:",
            "bind" => [
                "penalty_detail_id" => $result['penalty_detail_id'],
            ],
        ]);
        if (empty($penaltyDetailInfo)) {
            return false;
        }
        $penaltyReasonMap = HrPenaltyDetailModel::getPenaltyReasonMap();

        //组织详情数据
        $detailLists = [
            'apply_parson'              => sprintf('%s ( %s )', $staffInfo['name'] ?? '', $staffInfo['id'] ?? ''),
            'apply_department'          => sprintf('%s - %s', $staffInfo['depart_name'] ?? '',
                $staffInfo['job_name'] ?? ''),
            're_field_apply_store_name' => !empty($storeInfo) ? $storeInfo->name : "",
            'hr_penalty_reason'         => $this->getTranslation()->_($penaltyReasonMap[$penaltyDetailInfo->penalty_reason] ?? ""),
            'penalty_amount'            => $penaltyDetailInfo->penalty_money ?? 0,
            'penalty_date'              => $penaltyDetailInfo->attendance_date ?? "",
            'appeal_reason'             => $result['appeal_reason'] ?? "",
            'picture'                   => $result['image'] ? json_decode($result['image'], true): [],
        ];

        if ($comeFrom == 2) { //只有审批人才能看见审批提醒
            $detailLists = array_merge($detailLists,
                ['approval_notice_title' => $this->getTranslation()->_('approval_notice')]);
        }

        if ($result['state'] == enums::$audit_status['dismissed']) { //已经驳回，需要显示驳回原因
            $detailLists = array_merge($detailLists, [
                'reject_reason1' => $result['reject_reason'] ?? '',
            ]);
        }
        $returnData['data']['detail'] = $this->format($detailLists);
        $auditRepo = new AuditlistRepository($this->lang, $this->timezone);

        $data = [
            'title'       => $auditRepo->getAudityType(AuditListEnums::APPROVAL_TYPE_HR_PENALTY_APPEAL),
            'id'          => $result['id'],
            'staff_id'    => $result['staff_info_id'],
            'type'        => AuditListEnums::APPROVAL_TYPE_HR_PENALTY_APPEAL,
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['status'],
            'status_text' => $auditRepo->getAuditStatus('10' . $result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        //当前用户是否已经审批
        if ($comeFrom == 2) { //获取审批人的审批状态
            $info = AuditApprovalModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and approval_id = :approval_id: and deleted = 0",
                'bind' => [
                    'type'  => AuditListEnums::APPROVAL_TYPE_HR_PENALTY_APPEAL,
                    'value' => $auditId,
                    'approval_id' => $user
                ],
                'order' => 'id desc'
            ]);
            $state = $info instanceof AuditApprovalModel ? $info->getState() : 0;
            $option = ['beforeApproval' => "1,2", 'afterApproval' => '0'];

        } else { //获取申请的审批状态
            $state = $result['status'];
            $option = ['beforeApproval' => '3', 'afterApproval' => '0'];
        }
        $data['options'] = $this->getStaffOptions(['option' => $option, 'status' => $state]);
        $returnData['data']['head'] = $data;

        return $returnData;
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $penaltyAppealInfo = HrPenaltyAppealModel::findFirst($auditId);
        if (!empty($penaltyAppealInfo)) {
            $penaltyDetailInfo = HrPenaltyDetailModel::findFirst([
                "id = :penalty_detail_id:",
                "bind" => [
                    "penalty_detail_id" => $penaltyAppealInfo->penalty_detail_id,
                ],
            ]);
            if (empty($penaltyDetailInfo)) {
                return "";
            }

            $param = [
                [
                    'key'   => 'hr_penalty_reason',
                    'value' => $penaltyDetailInfo->penalty_reason,
                ],
                [
                    'key'   => 'penalty_amount',
                    'value' => $penaltyDetailInfo->penalty_money,
                ],
                [
                    'key'   => 'penalty_date',
                    'value' => $penaltyDetailInfo->attendance_date,
                ],
            ];
        }
        return $param ?? "";
    }

    /**
     * 审批结束回调函数
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {


        //更新申请表状态
        $auditInfo = HrPenaltyAppealModel::findFirst($auditId);
        if (empty($auditInfo)) {
            throw new \Exception($this->getTranslation()->_("4008"));
        }

        $this->getDI()->get('db')->updateAsDict("hr_penalty_appeal",
            ['is_new' => 0],
            [
                'conditions' => "penalty_detail_id = ? and id != ?",
                'bind'       => [$auditInfo->penalty_detail_id, $auditInfo->id],
            ]
        );

        if (!$isFinal) {
            return false;
        }

        if ($state == enums::APPROVAL_STATUS_REJECTED) {
            $auditInfo->reject_reason = $extend['remark'] ?? '';
        }
        $auditInfo->status = $state;
        $auditInfo->update();


        //如果最终审批结果为同意，则需要将现有处罚失效
        if ($state == enums::APPROVAL_STATUS_APPROVAL) {

            //更新处罚状态 => 已失效，并补充失效原因
            $hrPenaltyDetail = HrPenaltyDetailModel::findFirst([
                "id = :penalty_detail_id:",
                "bind" => [
                    "penalty_detail_id" => $auditInfo->penalty_detail_id
                ]
            ]);
            if (empty($hrPenaltyDetail) || $hrPenaltyDetail->state == HrPenaltyDetailModel::PENALTY_STATE_INVALID) {
                $this->logger->write_log(sprintf("申诉已失效, HrPenaltyAppeal id %d, HrPenaltyDetail id %d", $auditInfo->id, $auditInfo->penalty_detail_id),'alert');
                //throw new \Exception("4008");
            }
            $hrPenaltyDetail->state = HrPenaltyDetailModel::PENALTY_STATE_INVALID;
            $hrPenaltyDetail->expiry_reason = HrPenaltyDetailModel::EXPIRY_REASON_HAS_APPEAL;
            $hrPenaltyDetail->expiry_date = date("Y-m-d");
            $hrPenaltyDetail->save();

            $reasonMap = HrPenaltyDetailModel::getPenaltyReasonMap();
            //发送撤销处罚消息
            $server = new BasePenaltyServer($this->lang, $this->timezone);
            $data = [
                "staff_users"        => [$hrPenaltyDetail->staff_info_id], //提交人ID
                "message_title"      => $this->getTranslation()->_('penalty_cancellation_notice'),
                "message_content"    => $this->getTranslation()->_('penalty_cancellation_notice_content',
                    [
                        'attendance_date' => $hrPenaltyDetail->attendance_date,
                        'penalty_reason'  => $this->getTranslation()->_($reasonMap[$hrPenaltyDetail->penalty_reason] ?? ""),
                    ]),
                "category"           => -1,                                   //类别
                "staff_info_ids_str" => $hrPenaltyDetail->staff_info_id,      //内容
                "id"                 => time().$hrPenaltyDetail->staff_info_id.rand(1000000, 9999999),
            ];
            $server->sendMessage($data);
            
            //处罚消息设置为已读
            $server->doReadMessage($hrPenaltyDetail);
        }
    }

    /**
     * @description 获取审批参数
     * @param int $auditId
     * @param $user
     * @param null $state
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        return [];
    }

    /**
     * @description 获取处罚详情
     * @param $penalty_detail_id
     * @return array
     */
    public function getPenaltyDetail($penalty_detail_id): array
    {
        if (empty($penalty_detail_id)) {
            return [];
        }

        //获取处罚详情
        $detailInfo = HrPenaltyDetailModel::findFirst($penalty_detail_id);
        if (empty($detailInfo)) {
            return [];
        }
        $detailInfoArr = $detailInfo->toArray();

        //处罚人信息
        $staffInfoDetail = HrStaffInfoModel::findFirst([
            "staff_info_id = :staff_info_id:",
            "bind" => [
                "staff_info_id" => $detailInfoArr['staff_info_id']
            ],
            "staff_info_id, name"
        ]);

        //返回信息
        $response = [
            'penalty_reason'      => intval($detailInfoArr['penalty_reason']),
            'penalty_date'        => $detailInfoArr['attendance_date'],
            'penalty_money'       => $detailInfoArr['penalty_money'],
            'state'               => intval($detailInfoArr['state']),
            'early_late_minutes'  => 0,
            'early_late_hours'    => 0,
            'penalty_detail_id'   => intval($penalty_detail_id),
            'staff_info'          => empty($staffInfoDetail)  ? "" : sprintf("%s(%s)", $staffInfoDetail->name, $staffInfoDetail->staff_info_id),
        ];

        //计算迟到、早退分钟数
        if (in_array($detailInfoArr['penalty_reason'], [HrPenaltyDetailModel::PENALTY_REASON_COMING_LATE, HrPenaltyDetailModel::PENALTY_REASON_LEAVE_EARLY])) {
            $earlyLateTime                  = DateHelper::dealSecondsToHoursAndMinutes($detailInfoArr['early_late_seconds']);
            $response['early_late_minutes'] = $earlyLateTime['minutes'];
            $response['early_late_hours']   = $earlyLateTime['hours'];
        }

        //计算迟到、早退、旷工扣时
        if ($detailInfoArr['penalty_reason'] == HrPenaltyDetailModel::PENALTY_REASON_AB) {
            if ($detailInfoArr['early_late_ab_hour'] == 8) {
                $response['early_late_ab_time'] = "1 day";
            } else {
                $response['early_late_ab_time'] = "0.5 day";
            }
        } else {
            if ($detailInfoArr['early_late_ab_hour'] > 4) {
                $response['early_late_ab_time'] = "1 day";
            } else if ($detailInfoArr['early_late_ab_hour'] > 2) {
                $response['early_late_ab_time'] = "0.5 day";
            } else {
                $response['early_late_ab_time'] = sprintf("%.1f h", $detailInfoArr['early_late_ab_hour']);
            }
        }

        return $response;
    }

    /**
     * @description 计算处罚时间
     * @param $detailInfoArr
     * @return array
     */
    private function calcPenaltyTime($detailInfoArr): array
    {
        $response = [];
        //计算迟到、早退分钟数
        if (in_array($detailInfoArr['penalty_reason'], [HrPenaltyDetailModel::PENALTY_REASON_COMING_LATE, HrPenaltyDetailModel::PENALTY_REASON_LEAVE_EARLY])) {
            $earlyLateTime                  = DateHelper::dealSecondsToHoursAndMinutes($detailInfoArr['early_late_seconds']);
            $response['early_late_minutes'] = $earlyLateTime['minutes'];
            $response['early_late_hours']   = $earlyLateTime['hours'];
        }

        //计算迟到、早退、旷工扣时
        if ($detailInfoArr['penalty_reason'] == HrPenaltyDetailModel::PENALTY_REASON_AB) {
            if ($detailInfoArr['early_late_ab_hour'] == 8) {
                $response['ab_days'] = 1;
            } else {
                $response['ab_days'] = 0.5;
            }
        }
        return $response;
    }

    /**
     * @description 获取处罚列表
     * @param array $paramIn
     * @return array
     */
    public function getList(array $paramIn = []): array
    {
        $builder     = $this->generateBuilder($paramIn);
        $list        = $builder->columns('attendance_date,sum(penalty_money) as penalty_money_origin')
            ->getQuery()->execute()->toArray();
        $totalAmount = 0;
        if (!empty($list)) {
            foreach ($list as $key => $item) {
                $list[$key]['penalty_money'] = intval(min($item['penalty_money_origin'], self::PENALTY_MAX_AMOUNT_PER_DAY));
            }
            $amountList  = array_column($list, 'penalty_money');
            $totalAmount = array_sum($amountList);
        }
        return $this->checkReturn(['data' => ['dataList' => $list, 'total_amount' => $totalAmount]]);
    }

    /**
     * @description 组织构建SQL builder
     * @param $paramIn
     * @return mixed
     */
    protected function generateBuilder($paramIn = [])
    {
        $month       = $paramIn['month'];
        $halfMonth   = $paramIn['half_month'];
        $staffInfoId = $paramIn['staff_info_id'];
        if (!empty($halfMonth) && !empty($month)) {
            if (CommonEnums::FIRST_HALF_MONTH == $halfMonth) {
                $startDate = date("Y-m-01", strtotime($month));
                $endDate   = date("Y-m-15", strtotime($month));
            } else {
                $startDate = date("Y-m-15", strtotime($month));
                $endDate   = date("Y-m-01", strtotime($month . "+1 month"));
            }
        } else {
            if (date("d") > 15) {
                $startDate = date("Y-m-15");
                $endDate   = date("Y-m-01", strtotime("+1 month"));
            } else {
                $startDate = date("Y-m-01");
                $endDate   = date("Y-m-15");
            }
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(HrPenaltyDetailModel::class);
        $builder->where("attendance_date >= :start_date: and attendance_date < :end_date:",
            ['start_date' => $startDate, "end_date" => $endDate]);
        $builder->andWhere('state = :state:', ['state' => HrPenaltyDetailModel::PENALTY_STATE_TAKE_EFFECT]);
        $builder->andWhere("staff_info_id = :staff_info_id: ", ['staff_info_id' => $staffInfoId]);
        $builder->orderBy("attendance_date asc");
        $builder->groupBy("attendance_date");
        return $builder;
    }

    /**
     * @description 获取详情
     * @param $paramIn
     * @return mixed
     */
    public function getDetailInfo($paramIn = [])
    {
        $staffInfoId = $paramIn['staff_info_id'];
        $attendanceDate = $paramIn['attendance_date'];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('attendance_date,penalty_reason,early_late_seconds,early_late_ab_hour,id as penalty_detail_id,penalty_money,penalty_date');
        $builder->from(HrPenaltyDetailModel::class);
        $builder->where("attendance_date = :attendance_date:", ['attendance_date' => $attendanceDate]);
        $builder->andWhere('state = :state:', ['state' => HrPenaltyDetailModel::PENALTY_STATE_TAKE_EFFECT]);
        $builder->andWhere("staff_info_id = :staff_info_id: ", ['staff_info_id' => $staffInfoId]);
        $builder->orderBy("id asc");
        $penaltyList = $builder->getQuery()->execute()->toArray();

        $response = [];
        if (empty($penaltyList)) {
            return $this->checkReturn(['data' => []]);
        }
        $penaltyReasonMap = HrPenaltyDetailModel::getPenaltyReasonMap();

        foreach ($penaltyList as $key => $item) {
            //处理迟到、早退、旷工时间
            $penaltyTime = $this->calcPenaltyTime($item);
            if ($item['penalty_reason'] == HrPenaltyDetailModel::PENALTY_REASON_AB) {

                //xx天
                if ($penaltyTime['ab_days'] == 1) {
                    $time = sprintf("%d%s", $penaltyTime['ab_days'], $this->getTranslation()->_('by_day'));
                } else {
                    $time = sprintf("%.1f%s", $penaltyTime['ab_days'], $this->getTranslation()->_('by_day'));
                }
            } else { //迟到早退

                //xx小时xx分钟
                if (!empty($penaltyTime['early_late_hours'])) {
                    $time = sprintf("%d%s%d%s", $penaltyTime['early_late_hours'],
                        $this->getTranslation()->_('by_0f397c4f'),
                        $penaltyTime['early_late_minutes'],
                        $this->getTranslation()->_('by_f397c4f2')
                    );
                } else {
                    //xx分钟
                    $time = sprintf("%d%s", $penaltyTime['early_late_minutes'], $this->getTranslation()->_('by_f397c4f2'));
                }
            }

            //处理处罚原因
            $penaltyContent = sprintf("%s %s", $this->getTranslation()->_($penaltyReasonMap[$item['penalty_reason']] ?? ""), $time);
            $response[$key] = [
                'penalty_detail_id' => intval($item['penalty_detail_id']),
                'attendance_date'   => $item['attendance_date'],
                'penalty_money'     => intval($item['penalty_money']),
                'penalty_time'      => $penaltyContent,
                'penalty_date'      => $item['penalty_date'], //该日期用于计算处罚申诉按钮是否失效
            ];
        }

        //计算当日处罚金额
        $amountList        = array_column($penaltyList, 'penalty_money');
        $totalAmountOrigin = array_sum($amountList);
        $totalAmount       = min($totalAmountOrigin, self::PENALTY_MAX_AMOUNT_PER_DAY);

        return $this->checkReturn(['data' => [
            'dataList'        => $response,
            'total_amount'    => $totalAmount,
            'attendance_date' => $attendanceDate,
        ]]);
    }

    /**
     * @description 支持可查询近3个月（含当前月）
     * @return array
     */
    public function getValidMonth(): array
    {
        $monthList = [];
        $time = date("Y-m-01", strtotime("-2 month"));
        $stopMonth = date("Y-m-01");
        while ($time <= $stopMonth) {
            $monthList[] = date("Y-m", strtotime($time));
            $time = date("Y-m-01", strtotime($time. "+1 month"));
        }
        return $monthList;
    }
}