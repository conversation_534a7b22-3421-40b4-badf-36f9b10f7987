<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStoreApplySupportModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\OsStaffRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StoreSupportRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\StoreSupportServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use Exception;


class StoreSupportServer extends GlobalBaseServer
{
    public $timezone;
    public $lang;

    protected $store_support;
    protected $os;
    protected $staff;
    protected   $auditlist;

    public $apply_store_category = [1, 10, 13];

    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->timezone = $timezone;
        $this->lang     = $lang;
        $this->os = new OsStaffRepository($timezone);
        $this->store_support = new StoreSupportRepository($timezone);
        $this->staff = new StaffRepository();
        $this->auditlist= new AuditlistRepository($this->lang, $this->timezone);

        parent::__construct($lang);
    }

    /**
     * 网点申请支援
     * @param $paramIn
     * @return array
     */
    public function addStoreApplySupport($paramIn) {
        $staff_info_id  = $this->processingDefault($paramIn, 'staff_info_id');
        $job_title_id   = $this->processingDefault($paramIn, 'job_title_id');
        $employ_date    = $this->processingDefault($paramIn, 'employment_date');
        $employment_days = $this->processingDefault($paramIn, 'employment_days');
        $shift_id       = $this->processingDefault($paramIn, 'shift_id');
        $reason_type    = $this->processingDefault($paramIn, 'reason_type', 2);
        $reason         = $this->processingDefault($paramIn, 'reason');
        $reason         = addcslashes(stripslashes($reason), "'");
        $store_id       = $this->processingDefault($paramIn, 'store_id');
        $demand_num     = $this->processingDefault($paramIn, 'demand_num');

        $logger = $this->getDI()->get('logger');
        $db = $this->getDI()->get('db');
        try {
            $staff_info = $this->staff->checkoutStaff($staff_info_id);
            $employment_end_date = date('Y-m-d', strtotime($employ_date) + 86400 * ($employment_days -1));
            $store_detail = SysStoreModel::findFirst([
                'conditions' => 'id = :store_id:',
                'bind' => [
                    'store_id' => $store_id
                ]
            ]);

            if(empty($store_detail)) {
                $logger->write_log('addStoreApplySupport 找不到网点信息: 参数：store_id - '. $store_id);
                throw new BusinessException($this->getTranslation()->_('4008'));
            }
            $store_detail = $store_detail->toArray();
            $shift_info  = $this->os->getWorkShift($shift_id);
            if (empty($shift_info)) {
                $logger->write_log('addStoreApplySupport 找不到班次:getWorkShift: 参数：shift_id - '. $shift_id);
                throw new BusinessException($this->getTranslation()->_('4008'));
            }

            $insertData = [
                'serial_no'             => 'SAS' . $this->getRandomId(),
                'staff_info_id'         => $staff_info_id,
                'apply_type'            => 1,
                'job_title_id'          => $job_title_id,
                'department_id'         => $staff_info['department_id'] ?? '',
                'store_id'              => $store_id,
                'store_name'            => $store_detail['name'],
                'store_region_id'       => $store_detail['manage_region'],
                'store_piece_id'        => $store_detail['manage_piece'],
                'employment_begin_date' => $employ_date,
                'employment_end_date'   => $employment_end_date,
                'employment_days'       => $employment_days,
                'shift_id'              => $shift_id,
                'shift_type'            => $shift_info['type'] ?? '',
                'shift_start'           => $shift_info['start'] ?? '',
                'shift_end'             => $shift_info['end'] ?? '',
                'demand_num'            => $demand_num,
                'reason_type'           => $reason_type,
                'reason'                => $reason,
                'status'                => enums::$audit_status['panding'],
                'final_audit_num'       => $demand_num,
            ];

            $db->begin();
            $apply_id = $this->store_support->insert_store_support('hr_store_apply_support' ,$insertData);
            if (empty($apply_id)) {
                $logger->write_log('hr_store_apply_support 插入失败 - 参数: '. json_encode($insertData));
                throw new Exception($this->getTranslation()->_('4008'));
            }

            $extend = $this->getIsManager($staff_info_id, $store_id);
            //创建审批流
            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($apply_id, AuditListEnums::APPROVAL_TYPE_SAS, $staff_info_id, null, $extend);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
            $db->commit();

            return $this->checkReturn([]);
        } catch (Exception $e) {
            $db->rollback();
            $logger->write_log('addStoreApplySupport'. $e->getMessage() . $e->getTraceAsString());
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    public function updateStoreApplySupport($paramIn = []) {
        $staff_info_id = $this->processingDefault($paramIn, 'staff_id', 2);
        $status  = $this->processingDefault($paramIn, 'status', 2);
        $reason  = $this->processingDefault($paramIn, 'reject_reason', 1);
        $support_id = $this->processingDefault($paramIn, 'audit_id', 2);
        $reason   = addcslashes(stripslashes($reason),"'");
        $demand_num = $this->processingDefault($paramIn, 'demand_num', 2);

        //详情
        $detail = $this->getStoreSupportApplyDetail($support_id);
        if (empty($detail)) {
            throw new Exception($this->getTranslation()->_('4008'), enums::$ERROR_CODE['1000']);
        }

        if ($detail['status'] != enums::$audit_status['panding'] && $status == enums::$audit_status['revoked']) {
            throw new Exception($this->getTranslation()->_('please try again'), enums::$ERROR_CODE['1000']);
        }

        try {
            //同意或者驳回等分开处理
            if ($status == enums::$audit_status['approved']) {
                //同意
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->approval($support_id, AuditListEnums::APPROVAL_TYPE_SAS, $staff_info_id);

                $this->getDI()->get('db')->updateAsDict(
                    'hr_store_apply_support',
                    ['final_audit_num' => $demand_num],
                    'id = '. $support_id
                );
            } else if ($status == enums::$audit_status['dismissed']) {
                //驳回
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->reject($support_id, AuditListEnums::APPROVAL_TYPE_SAS, $reason, $staff_info_id);
            } else {
                //撤销
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->cancel($support_id, AuditListEnums::APPROVAL_TYPE_SAS, $reason, $staff_info_id);
            }
            return $this->checkReturn([]);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("updateStoreApplySupport failure:" . $e->getMessage() . $e->getTraceAsString(), "notice");
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
    }

    //申请详情
    public function getDetail(int $auditId, $user, $comeFrom) {
        $result = $this->getStoreSupportApplyDetail($auditId);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_info_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }

        $applying_num = $this->getStaffApplySupport($result['serial_no'], 1);
        $approve_num = $this->getStaffApplySupport($result['serial_no'], 2);

        $detailLists = [
            'apply_parson'      => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? ''),
            'apply_department'  => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
            'store_support_support_apply_type' => $this->getTranslation()->_('store_support_support_apply_type_1'),
            'store_support_staff_job_title' => UC('storeSupport')['jobTitles_PH'][$result['job_title_id']] ?? '',
            'store_name'        => $result['store_name'],
            'store_support_employment_begin_date' => $result['employment_begin_date'],
            'employment_days'   => $result['employment_days'],
            'work_shift'        => $result['work_shift'],
            //'store_support_demand_num'        => $result['final_audit_num'],
            'store_support_applying_num'      => $applying_num, //申请中人数
            'store_support_approve_num'       => $approve_num,  //已通过人数
            'store_support_reason_type'       => $this->getTranslation()->_('store_support_request_reason_'.$result['reason_type']),
            'store_support_reason'            => $result['reason'],
        ];

        //驳回状态，需要显示驳回原因
        if ($result['status'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $data = [
            'title'      => $this->auditlist->getAudityType(enums::$audit_type['SAS']),
            'id'         => $result['id'],
            'staff_id'   => $result['staff_info_id'],
            'type'       => enums::$audit_type['SAS'],
            'created_at' => date('Y-m-d H:i:s', strtotime($result['created_at']) + $add_hour * 3600),
            'updated_at' => date('Y-m-d H:i:s', strtotime($result['updated_at']) + $add_hour * 3600),
            'status'     => $result['status'],
            'status_text'=> $this->auditlist->getAuditStatus('10' . $result['status']),
            'serial_no'  => $result['serial_no'] ?? '',
            'demand_num' => $result['final_audit_num'],
            'applying_num' => $applying_num, //申请中人数
            'approve_num' => $approve_num,  //已通过人数
            'employment_days'   => $result['employment_days'],
        ];

        //当前用户是否已经审批
        $apply_num_edit = 0;
        if ($comeFrom == 2) { //获取审批人的审批状态
            $infos = AuditApprovalModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and approval_id = :approval_id: and deleted = 0",
                'bind' => [
                    'type'  => enums::$audit_type['SAS'],
                    'value' => $auditId,
                    'approval_id' => $user
                ],
                'order' => 'id desc'
            ]);
            $state = $infos->getState() ?? 0;
            $option = ['beforeApproval' => "1,2", 'afterApproval' => '0'];

            $apply_num_edit = $state == 1 ? 1 : 0;
        } else { //获取申请的审批状态
            $state = $result['status'];
            $option = ['beforeApproval' => '3', 'afterApproval' => '0'];
        }
        $data['options'] = $this->getStaffOptions(['option' => $option, 'status' => $state]);

        array_splice($returnData['data']['detail'], 8, 0, [['key' => $this->getTranslation()->_('store_support_demand_num'), 'value' => $result['final_audit_num'], 'apply_num_edit' => $apply_num_edit, 'tips' => null]]);
        /*
        if ($result['status'] == 2) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('final_num'), 'value' => $result['final_audit_num']],
            ];
        } else if ($result['status'] == 3) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('reject_reason'), 'value' => $result['reject_reason']],
            ];
        }
        */
        if ($result['status'] == 3) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('reject_reason'), 'value' => $result['reject_reason']],
            ];
        }

        $returnData['data']['head']   = $data;
        return $returnData;
    }

    /**
     * 设置回调属性
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            // 更新 支援状态
            $support_status = $this->getSupportStatusById($auditId, $state);
            $params = ['status' => $state, 'support_status' => $support_status];
            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                $params['approval_agreed_time'] = gmdate('Y-m-d H:i:s');
            }
            $this->getDI()->get('db')->updateAsDict(
                'hr_store_apply_support',
                $params,
                'id = '.$auditId
            );
        }
    }

    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info     = $this->getStoreSupportApplyDetail($auditId);
        if (!empty($info)) {
            $job_title_name = UC('storeSupport')['jobTitles_PH'][$info['job_title_id']] ?? '';
            $param = [
                [
                    'key'   => "store_support_staff_job_title",
                    'value' => $job_title_name
                ],
                [
                    'key'   => "store",
                    'value' => $info['store_name']
                ],
                [
                    'key'   => "store_support_demand_num",
                    'value' => $info['final_audit_num']
                ]
            ];
        }
        return $param ?? [];
    }

    /**
     * 获取审批流参数
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $auditInfo = HrStoreApplySupportModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return false;
        }

        return $this->getIsManager($auditInfo->staff_info_id, $auditInfo->store_id);
    }

    //是否是 am dm store_manager 负责人
    public function getIsManager($staff_info_id, $store_id) {
        //是否AM
        $manageRegionAll = SysManageRegionModel::find([
            'conditions' => 'deleted = 0 and type = 1 '
        ]);
        $region_arr = $manageRegionAll->toArray();
        $region_ids = array_column($region_arr, 'id');
        $region_manager_ids = array_column($region_arr, 'manager_id');
        //3-大区负责人
        if(in_array($staff_info_id, $region_manager_ids)) {
            $manageRegion = true;
        } else {
            $manageRegion = false;
        }
        //是否DM
        $managePiece = SysManagePieceModel::find([
            'conditions' => 'manager_id = :staff_id: and deleted = 0 and manage_region_id in ({manage_region_ids:array})',
            'bind'       => ['staff_id' => $staff_info_id, 'manage_region_ids' => $region_ids],
        ])->toArray();

        //是否网点负责人
        $manageStore = SysStoreModel::find([
            'conditions' => 'manager_id = :staff_id: and state = 1 and category in ({apply_category:array})',
            'bind'       => [
                'staff_id' => $staff_info_id,
                'apply_category' => $this->apply_store_category
                ],
        ])->toArray();

        return [
            'is_am' => $manageRegion,
            'is_dm' => !empty($managePiece),
            'is_store_manager' => !empty($manageStore),
            'DM_store_ids' => [$store_id],
            'AM_store_ids' => [$store_id]
        ];
    }

    //申请详情
    public function getStoreSupportApplyDetail($support_id) {
        $store_apply_support_detail = HrStoreApplySupportModel::findFirst([
            'conditions' => "id = :support_id:",
            'bind' => [
                'support_id' => $support_id
            ]
        ]);
        $detail = [];
        if(!empty($store_apply_support_detail)) {
            $detail = $store_apply_support_detail->toArray();
            $shift_info  = $this->os->getWorkShift($detail['shift_id']);
            if (!empty($shift_info)) {
                if ($shift_info['type'] == 'EARLY') {
                    $shift = $this->getTranslation()->_('shift_early');
                } else if ($shift_info['type'] == 'MIDDLE') {
                    $shift = $this->getTranslation()->_('shift_middle');
                } else if ($shift_info['type'] == 'NIGHT') {
                    $shift = $this->getTranslation()->_('shift_night');
                } else {
                    $shift = $this->getTranslation()->_('shift_early');
                }
                $detail['work_shift'] = $shift . ' ' . $shift_info['start'] . ' - ' . $shift_info['end'];
            }
        }

        return $detail;
    }

    //申请支援枚举值
    public function getStoreApplySysInfo() {
        try {
            $job_title_list = [
                [
                    'job_title_id'      => enums::$job_title['van_courier'],
                    'job_title_name'    => 'Van Courier',
                ],
                [
                    'job_title_id'      => enums::$job_title['bike_courier'],
                    'job_title_name'    => 'Bike Courier',
                ],
                [
                    'job_title_id'      => enums::$job_title['tricycle_courier'],
                    'job_title_name'    => 'Tricycle Courier',
                ],
                [
                    'job_title_id'      => enums::$job_title['dc_officer'],
                    'job_title_name'    => 'DC Officer',
                ],
                [
                    'job_title_id'      => enums::$job_title['branch_supervisor'],
                    'job_title_name'    => 'DC Supervisor',
                ],
            ];

            $shift_list = $this->getShiftList();
            foreach ($shift_list as $k => &$item) {
                switch ($item['type']) {
                    case 'EARLY':
                        $item['type_text'] = $this->getTranslation()->_('shift_early');
                        break;
                    case 'MIDDLE':
                        $item['type_text'] = $this->getTranslation()->_('shift_middle');
                        break;
                    case 'NIGHT':
                        $item['type_text'] = $this->getTranslation()->_('shift_night');
                        break;
                    default:
                        break;
                }
                $item['shift_text'] = $item['type_text'] .' '. $item['start'] .'-'. $item['end'];
            }

            $return_arr = [
                'job_title' => $job_title_list,
                'shift_list' => $shift_list,
                'reason' => $this->getOsStaffReqReason(),
            ];

            return $return_arr;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getSupportStoreList error:" . $e->getMessage() . $e->getTraceAsString());
            return [];
        }
    }

    //申请原因
    public function getOsStaffReqReason()
    {
        $retReason = [
            ["code" => 1, "title" => $this->getTranslation()->_('store_support_request_reason_1')],
            ["code" => 2, "title" => $this->getTranslation()->_('store_support_request_reason_2')],
            ["code" => 3, "title" => $this->getTranslation()->_('store_support_request_reason_3')],
            ["code" => 99, "title" => $this->getTranslation()->_('store_support_request_reason_99')],
        ];
        return $retReason;
    }

    //获取可申请支援的网点列表
    public function getSupportStoreList($paramIn) {
        try {
            $staff_info_id = $this->processingDefault($paramIn['userinfo'], 'staff_id', 2);
            //大区
            $region_list = SysManageRegionModel::find([
                'conditions' => "manager_id = :manager_id: and deleted = 0",
                'bind' => [
                    'manager_id' => $staff_info_id
                ]
            ])->toArray();
            $region_store_list = [];
            if(!empty($region_list)) {
                $region_ids = array_column($region_list, 'id');
                $region_store_list = $this->getStoreListByRegionIds($region_ids, $this->apply_store_category);
                $region_store_list = array_column($region_store_list, null, 'id');
            }

            $piece_store_list = [];
            $piece_list = SysManagePieceModel::find([
                'conditions' => "manager_id = :manager_id: and deleted = 0",
                'bind' => [
                    'manager_id' => $staff_info_id
                ]
            ])->toArray();
            if(!empty($piece_list)) {
                $piece_ids = array_column($piece_list, 'id');
                $piece_store_list = $this->getStoreListByPieceIds($piece_ids, $this->apply_store_category);
                $piece_store_list = array_column($piece_store_list, null, 'id');
            }

            //SP、BDC、CDC
            $store_list = SysStoreModel::find([
                'columns' => 'id, name, category, manage_region, manage_piece',
                'conditions' => "manager_id = :manager_id: and state = 1 and category in ({apply_category:array})",
                'bind' => [
                    'manager_id' => $staff_info_id,
                    'apply_category' => $this->apply_store_category,
                ]
            ])->toArray();

            if(!empty($store_list)) {
                $store_list = array_column($store_list, null, 'id');
            } else {
                $store_list = [];
            }

            $list = array_merge($region_store_list, $piece_store_list, $store_list);
            return $list;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getSupportStoreList 参数:" . json_encode($paramIn) .'; error:' . $e->getMessage() . $e->getTraceAsString());
            return [];
        }
    }

    //获取可申请支援的网点列表
    public function getSupportStoreListV2($paramIn) {
        try {
            $staff_info_id = $this->processingDefault($paramIn['userinfo'], 'staff_id', 2);

            $gray_level_store_list = [
                'PH17010100','PH17030100','PH17050200','PH17130200','PH17140100','PH17210H00','PH17290100','PH18030100','PH18030101','PH18040100',
                'PH18060100','PH18060200','PH18060800','PH18080600','PH18080B01','PH18110100','PH18180200','PH19100300','PH20070300','PH21120200',
                'PH57090200','PH61020200','PH61020201','PH61180100','PH61180101','PH61180200','PH61180400','PH61180B00','PH61181700','PH61181X00',
                'PH61182000','PH61183900'];

            //大区
            $region_list = SysManageRegionModel::find([
                'conditions' => "manager_id = :manager_id: and deleted = 0",
                'bind' => [
                    'manager_id' => $staff_info_id
                ]
            ])->toArray();
            $region_store_list = [];
            if(!empty($region_list)) {
                $region_ids = array_column($region_list, 'id');
                $region_store_list = SysStoreModel::find([
                    'columns' => 'id, name, category, manage_region, manage_piece',
                    'conditions' => "manage_region in ({region_ids:array}) and state = 1 and category in ({apply_category:array}) and id in ({store_ids:array})",
                    'bind' => [
                        'region_ids' => $region_ids,
                        'apply_category' => $this->apply_store_category,
                        'store_ids' => $gray_level_store_list,
                    ]
                ])->toArray();
                $region_store_list = !empty($region_store_list) ? array_column($region_store_list, null, 'id') : [];
            }

            $piece_store_list = [];
            $piece_list = SysManagePieceModel::find([
                'conditions' => "manager_id = :manager_id: and deleted = 0",
                'bind' => [
                    'manager_id' => $staff_info_id
                ]
            ])->toArray();
            if(!empty($piece_list)) {
                $piece_ids = array_column($piece_list, 'id');
                $piece_store_list = SysStoreModel::find([
                    'columns' => 'id, name, category, manage_region, manage_piece',
                    'conditions' => "manage_piece in ({piece_ids:array}) and state = 1 and category in ({apply_category:array}) and id in ({store_ids:array})",
                    'bind' => [
                        'piece_ids' => $piece_ids,
                        'apply_category' => $this->apply_store_category,
                        'store_ids' => $gray_level_store_list,
                    ]
                ])->toArray();
                $piece_store_list = !empty($piece_store_list) ? array_column($piece_store_list, null, 'id') : [];
            }

            //SP、BDC、CDC
            $store_list = SysStoreModel::find([
                'columns' => 'id, name, category, manage_region, manage_piece',
                'conditions' => "manager_id = :manager_id: and state = 1 and id in ({store_ids:array})",
                'bind' => [
                    'manager_id' => $staff_info_id,
                    'store_ids' => $gray_level_store_list,
                ]
            ])->toArray();

            if(!empty($store_list)) {
                $store_list = array_column($store_list, null, 'id');
            } else {
                $store_list = [];
            }

            $list = array_merge($region_store_list, $piece_store_list, $store_list);
            return $list;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getSupportStoreList 参数:" . json_encode($paramIn) .'; error:' . $e->getMessage() . $e->getTraceAsString());
            return [];
        }
    }


    //根据大区id 获取指定网点类型列表
    public function getStoreListByRegionIds($region_ids, $apply_category) {
        try {
            $store = SysStoreModel::find([
                'columns' => 'id, name, category, manage_region, manage_piece',
                'conditions' => "manage_region in ({region_ids:array}) and state = 1 and category in ({apply_category:array})",
                'bind' => [
                    'region_ids' => $region_ids,
                    'apply_category' => $apply_category,
                ]
            ])->toArray();
            return $store;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getStoreListByRegionIds 参数:" . json_encode($region_ids) .'; error:' . $e->getMessage() . $e->getTraceAsString());
            return [];
        }
    }

    //根据片区id 获取指定网点类型列表
    public function getStoreListByPieceIds($piece_ids, $apply_category) {
        try {
            $store = SysStoreModel::find([
                'columns' => 'id, name, category, manage_region, manage_piece',
                'conditions' => "manage_piece in ({piece_ids:array}) and state = 1 and category in ({apply_category:array})",
                'bind' => [
                    'piece_ids' => $piece_ids,
                    'apply_category' => $apply_category,
                ]
            ])->toArray();
            return $store;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getStoreListByPieceIds 参数:" . json_encode($piece_ids) .'; error:' . $e->getMessage() . $e->getTraceAsString());
            return [];
        }
    }

    //获取已申请的人数
    public function getStaffApplySupport($serial_no, $status) {
        $count = HrStaffApplySupportStoreModel::count([
            'conditions' => "store_apply_support_serial_no = :serial_no: and status = :status:",
            'bind' => [
                'serial_no' => $serial_no,
                'status' => $status,
            ]
        ]);
        return $count;
    }

    public function getShiftList()
    {
        //可选班次为：所有早班、所有中班 、 13:00-22:00（晚班）
//        $query_sql  = "
//            --
//            SELECT
//                id
//                ,type
//                ,start
//                ,end
//            FROM `hr_shift`
//            WHERE id NOT IN (12,13,14,15,16,17,18,19,20,21,22,23,26,28,29,30,40,41,42,43,44,45,46,47,48,49,35)
//            and shift_attendance_type = :shift_attendance_type
//            order by type,start asc";
//        //有 end  不能使用 ->find ??
//        $returnData = $this->getDI()->get('db_rby')->query($query_sql,['shift_attendance_type'=>HrShiftModel::SHIFT_ATTENDANCE_TYPE_FIXED])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
//        return $returnData;

        return HrShiftModel::find([
            'columns'    => 'id,type,start,[end]',
            'conditions' => 'id NOT IN ({ids:array}) and shift_attendance_type = :shift_attendance_type: and shift_group = :shift_group:',
            'bind'       => [
                'ids' => self::$not_in_shift,
                'shift_attendance_type' => HrShiftModel::SHIFT_ATTENDANCE_TYPE_FIXED,
                'shift_group' => HrShiftModel::SHIFT_GROUP_FULL_DAY_SHIFT
            ],
            'order'      => "type,start asc",
        ])->toArray();
    }
}