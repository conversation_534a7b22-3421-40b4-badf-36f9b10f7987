<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrOperateLogsModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditContractWorkerModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\LeaveScenarioModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReasonModel;
use FlashExpress\bi\App\Models\backyard\MessagePdfModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Server\ProbationServer as BaseServer;
use FlashExpress\bi\App\Server\StaffServer;

/**
 * @method submitProbationEvaluationContractV3FromHcmUseLock($probation_audit_id)
 */
class ProbationServer extends BaseServer
{
    /**
     * 根据入职时间获得员工
     * @param string $hire_date_begin
     * @param string $hire_date_end
     * @param int $is_delay 是否是延长试用期的用户
     * @param string $greater_job_title_grade_v2
     * @param string $less_job_title_grade_v2
     * @param bool $date_logic
     * @return array
     */
    public function getStaffs($hire_date_begin='', $hire_date_end='',$is_delay=0,$greater_job_title_grade_v2='',$less_job_title_grade_v2='',$date_logic = false,$probation_channel_type = 1): array
    {
        $sql = "
            select
                hsi.staff_info_id,
                hsi.job_title_level,
                hsi.job_title_grade_v2,
                hsi.hire_date,
                hsi.manger as manager_id,
                hp.cur_level,
                hp.contract_formal_date,
                hp.probation_channel_type,
                hp.status,
                hp.id as probation_id,
                hp.second_audit_status,
                hp.first_audit_status
            from
                hr_staff_info as hsi
            left join
                hr_probation as hp on hp.staff_info_id = hsi.staff_info_id
            where
                hsi.state!=2 and hsi.formal=1 and hsi.is_sub_staff = 0 and hsi.hire_type = 1 and (hp.is_system=0 or hp.is_system is NULL) and (hp.hire_type is null or hp.hire_type=1)
        ";

        $hire_date_end = date("Y-m-d", strtotime("-6 month", strtotime($hire_date_begin)));
        $sql .= 'and (
            hp.contract_formal_date is null and  probation_channel_type!=5 and hsi.hire_date <= :hire_date_begin and hsi.hire_date>=:hire_date_end
            or   hp.contract_formal_date is not null and probation_channel_type=5 and hp.contract_formal_date <= :hire_date_begin and hp.contract_formal_date>=:hire_date_end
        )';

        if(empty($is_delay)){
            $sql.=" and (hp.is_delay=0 or hp.is_delay is NULL)";
        }else{
            $sql.=" and hp.is_delay=1";
        }
        $bind = ['hire_date_begin' => $hire_date_begin, 'hire_date_end' => $hire_date_end];
        //增加条件
        if (!empty($greater_job_title_grade_v2)) {
            $sql .= ' and hsi.job_title_grade_v2 > :begin_job_title_grade_v2 ';
            $bind['begin_job_title_grade_v2'] = (int)$greater_job_title_grade_v2;
        }

        //增加条件
        if (!empty($less_job_title_grade_v2)) {
            $sql .= ' and ifnull(hsi.job_title_grade_v2,0) <= :end_job_title_grade_v2 ';
            $bind['end_job_title_grade_v2'] = (int)$less_job_title_grade_v2;
        }
        return $this->getDI()->get("db_rby")->query(
            $sql,
            $bind
        )->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }
    /**
     * @description:提交转正评估评审
     *
     * @param string $id
     * @param string $audit_id 用户 id
     * @param array  $params
     * @param int  $deadline_at_one  第一阶段上上级过期时间
     * @param int  $deadline_at_tow 第二阶段上上级过期时间
     * @param int  $is_fail_msg 未通过是否发送消息给被评估员工上级，以及被评估员工所属HRBP 目前只有 Id 发送  2 是发送
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/9/9 14:41
     */
    public function probation_audit($id = '', $audit_id = '', $params = [], $deadline_at_one = 3, $deadline_at_tow = 0, $is_fail_msg = 1)
    {
            if (empty($id) || empty($audit_id) || empty($params['probation_channel_type'])) {
                throw new ValidationException($this->getTranslation()->_('miss_args'));
            }
            $lang = $this->getTranslation();
            //没有分数不行!!
            if (empty($params['score'])) {
                throw new ValidationException('score ' . $lang->_('miss_args'));
            }
        [$dbName,$auditDbName] = $this->getProbationDbName($params['probation_channel_type']);
            $sql = "
            select
                hpa.*,
                hpt.item_ids,
                hpt.score_rule,
                hp.is_system,
                hp.formal_at,
                hp.hire_type,
                hp.probation_channel_type,
                hp.status as hp_status,
                hp.is_active as hp_is_active
            from
                $auditDbName as hpa
                left join hr_probation_tpl as hpt on hpa.tpl_id = hpt.id
                left join $dbName as hp on hp.id=hpa.probation_id
            where
                hpa.id=:id and hpa.audit_id=:audit_id
        ";

            $arr = $this->getDI()->get("db_rby")->fetchOne($sql, \PDO::FETCH_ASSOC, ["id" => $id, "audit_id" => $audit_id]);
            if (empty($arr)) {
                throw new ValidationException('not found');
            }
            if (!empty($arr['hp_status']) && $arr['hp_status'] == HrProbationModel::STATUS_FORMAL) {
                throw new ValidationException($lang->_('probation_status_err_1'));
            }

            if ($arr['audit_status'] != 1) {
                throw new ValidationException('has already audit');
            }


            if ($arr['is_system'] == 1) {
                throw new ValidationException('system auto');
            }
            //月薪制合同工单独处理
            if ($arr['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT) {
                $this->submitProbationEvaluation($arr,$params);
                return $this->checkReturn(['data' => true]);
            }
            if ($arr['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3) {
                $this->submitProbationEvaluationContractV3($arr,$params);
                return $this->checkReturn(['data' => true]);
            }
            $score = $params['score'];
            //第二阶段没选择是否让继续工作了，通过分数判断下
            if ($arr['cur_level'] == 2 && $this->getScore($score['second_score'], 1) < $this->passMark) {
                $params['is_terminate'] = HrProbationAuditModel::IS_TERMINATE;
            }
            if ($arr['cur_level'] == 2 && $this->getScore($score['second_score'], 1) >=$this->passMark) {
                $params['is_terminate'] = HrProbationAuditModel::IS_NO_TERMINATE;
            }
            // 第一 第二阶段 评估分数 小于六分
            if (($arr['cur_level'] == 1 && $this->getScore($score['score'], 1) < $this->passMark
                    || $arr['cur_level'] == 2 && $this->getScore($score['second_score'], 1) < $this->passMark)
                && (empty($params['is_terminate']))) {
                throw new ValidationException($lang->_('miss_args'));
            }
            //下个审批人
            $manager_id = $this->getManagerId($audit_id);
            //查询用户等级
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => [
                    'staff_info_id' => $arr['staff_info_id'] ?? '',
                ],
            ]);
            $staffInfo = !empty($staffInfo) ? $staffInfo->toArray() : [];

            //获取增加天数
            $evaluate_time = $this->duration_day;//递增天数
            //18 级以下  发两次
            $is_send = $arr['audit_level'] == self::AUDIT_LEVEL_1 && !empty($manager_id);
            //18 级以上 发三次  第三次是 cpo
            if (isset($staffInfo['job_title_grade_v2']) && $staffInfo['job_title_grade_v2'] > $this->job_grade_exceed &&
                $arr['version'] == $this->version) {
                $cpo_staff_info_id = $this->cpo_staff_id;
                //如果当前审批人 == cop 或者 当前阶段是第三级评估  则不发送了   false 为不发送
                $is_send = !($audit_id == $cpo_staff_info_id || $arr['audit_level'] >= self::AUDIT_LEVEL_3);
                //如果当前是第二级评估   则 下次评估人为 cpo 评估
                $manager_id = $arr['audit_level'] >= self::AUDIT_LEVEL_2 ? $cpo_staff_info_id : $manager_id;
                $evaluate_time = $this->duration_day_exceed;//递增的截止天数

            }

            $remark = $params['remark'] ?? '';
            $pic = $params['pic'] ?? '';
            $job_content = $params['job_content'] ?? ''; //工作内容

            $item = []; //hr_probation表
            $data = []; //hr_probation_audit表

            //如果info与库里不同,证明已经修改
            foreach ($score['list'] as $k => $v) {
                foreach ($v['list'] as $kk => $vv) {
                    if ($lang->_($vv['info_key']) != $vv['info']) {
                        $score['list'][$k]['list'][$kk]['is_update'] = 1;
                    }
                }
            }

            //自己根据规则算一遍
            $score = $this->getScoreFromTpl($score, $arr['cur_level']);
            //没有分数不行!!
            if (empty($score)) {
                throw new \Exception('score 没有获取到!!!');
            }
            $data['score'] = json_encode($score, JSON_UNESCAPED_UNICODE);//score存json
            $data['audit_status'] = 2;
            $data['remark'] = $remark;
            $data['pic'] = $pic;
            $data['show_time']   = date("Y-m-d H:i:s");

            $data['is_terminate'] = $params['is_terminate'] ?? 0;

            //$score_num = $this->getScore($score['score'], 1);
            $item['updated_at'] = gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600);
            $data['updated_at'] = $item['updated_at'];
            $data['job_content'] = $job_content;
            //第一次
            if ($arr['cur_level'] == self::CUR_LEVEL_FIRST) {
                $score_num = $this->getScore($score['score'], 1);
                $item['first_score'] = $score_num;
                $item['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_RUN;
                if (!$is_send) {
                    //不给下一级发了就说明是最后一级了
                    $item['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_DONE;
                    if ($score_num >= $this->passMark) {
                        $item['first_status'] = HrProbationModel::FIRST_STATUS_PASS;
                    }
                }
            } //第二次
            else {
                $score_num = $this->getScore($score['second_score'], 1);
                $item['second_score'] = $score_num;

                //第二阶段上上级评过+第二阶段上级改成上级和上上级都更改主表
                //if ($arr['audit_level'] == 2) {
                if ($score_num >= $this->passMark) {
                    $item['status'] = self::STATUS_PASS;    //已通过
                    if ($item['updated_at'] > $arr['formal_at']) {
                        $is_send = false;
                        $item['status'] = self::STATUS_FORMAL;
                        $item['formal_staff_id'] = $audit_id;
                        $item['formal_at'] = gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600);
                    }
                } else {
                    $item['status'] = self::STATUS_NOT_PASS;    //未通过
                }
                $item['remark'] = $data['remark'];

                //把通过不通过的状态传过去
                $data['status'] = $item['status'];
                //不发了 赋值终态
                if (!($is_send && !empty($manager_id))) {
                    $item['second_audit_status'] = HrProbationModel::SECOND_AUDIT_STATUS_DONE;
                     if (in_array($item['status'],[self::STATUS_PASS,self::STATUS_FORMAL])) {
                        $item['second_status'] = HrProbationModel::SECOND_STATUS_PASS;
                    }
                }
            }

            $db = $this->getDI()->get("db");


            try {
                $db->begin();

                $db->updateAsDict("hr_probation_audit", $data, ["conditions" => "id=" . intval($id)]);
                $db->updateAsDict("hr_probation", $item, ["conditions" => 'id=' . intval($arr['probation_id'])]);

                //如果可以评估 并且有下一级评估人 并且没有转正
                if ($is_send && !empty($manager_id)) {
                    //下一级评审，内容大部分相同
                    $tmp                  = [];
                    $tmp['probation_id']  = $arr['probation_id'];
                    $tmp['staff_info_id'] = $arr['staff_info_id'];
                    $tmp['tpl_id']        = $arr['tpl_id'];
                    $tmp['audit_id']      = $manager_id;
                    $tmp['audit_level']   = ((int)$arr['audit_level']) + 1;
                    $tmp['cur_level']     = $arr['cur_level'];
                    $tmp['audit_status']  = 1;
                    $tmp['status']        = 0;
                    $tmp['score']         = $data['score'];
                    $tmp['created_at']    = $item['updated_at'];
                    $tmp['updated_at']    = $item['updated_at'];
                    $tmp['remark']        = $remark;                 //同步上级评审意见
                    $tmp['pic']           = $pic;                    //同步上级图片
                    $tmp['job_content']   = $job_content;            //工作内容
                    $tmp['version']       = $arr['version'];         //版本
                    $tmp['show_time']     = date("Y-m-d H:i:s");

                    if ($arr['hp_is_active'] == 1){
                        $now_date = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
                        $tmp['deadline_at']        = $this->getDateByDays($now_date, 3, 1);
                    }else{
                        $day                = $evaluate_time[$arr['cur_level']][$tmp['audit_level']] ?? $deadline_at_one;
                        $tmp['deadline_at'] = $this->getDateByDays($arr['deadline_at'], $day, 1);
                    }
                    $db->insertAsDict("hr_probation_audit", $tmp);
                }

                $db->commit();
            } catch (\Exception $e) {
                $db->rollback();
                $this->getDI()->get('logger')->write_log('ProbationServer_audit---' . $e->getMessage() . '-----' . $e->getLine());
                throw $e;
            }
            $this->getDI()->get('logger')->write_log(['tmp'=>$tmp??[]],'info');

            //给上级发送push
            if (!empty($tmp)) {
                $this->push_notice_higher($tmp['audit_id'], $tmp['staff_info_id']);
            }

            //上上级不通过，给上级员工发送消息
            if ($arr['cur_level'] == self::CUR_LEVEL_SECOND && $arr['audit_level'] == 2 && $item['status'] == self::STATUS_NOT_PASS) {
                $this->sendMsgToManagerByStaffId($arr['staff_info_id'], $this->getManagerId($arr['staff_info_id']));
            }

            //记录转正日志
            if (isset($item['status']) && $item['status'] == self::STATUS_FORMAL) {
                $this->putFormalLog($audit_id, $arr['staff_info_id'], $arr['hp_status'], self::STATUS_FORMAL);
            }

            return $this->checkReturn(['data' => true]);
    }

    /**
     * @description:详情
     *
     * @param string $id
     * @param string $audit_id
     *
     * @return array :
     * <AUTHOR> L.J
     * @time       : 2021/9/8 20:49
     */
    public function getByDetail($paramIn = [])
    {
        $id = $paramIn['id'];
        $probationChannelType = $paramIn['probation_channel_type'];
        $audit_id = $paramIn['audit_id'] ?? 0;
        $staff_info_id = $paramIn['staff_id'] ?? 0;

        if (!isset($paramIn['type']) && (empty($id) || empty($audit_id)) || empty($probationChannelType)) {
            throw new \Exception($this->getTranslation()->_('miss_args'));
        }

        [$dbName,$auditDbName] = $this->getProbationDbName($probationChannelType);
        $sql = "
            select
                hpa.*,
                it.value as avatar,
                hp.status as hp_status,
                hp.is_active as hp_active,
                hp.hire_type,
                hp.contract_formal_date,
                hp.probation_channel_type,
                hp.formal_at,
                hp.first_audit_status,
                hp.first_score,
                hp.second_score
            from
                $auditDbName as hpa
            left join hr_staff_items as it on it.staff_info_id=hpa.staff_info_id and it.item='PROFILE_OBJECT_KEY'
            left join $dbName as hp on hp.id=hpa.probation_id
            where  hpa.id=:id ";
        $where_data['id'] = $id;
        if (!empty($audit_id)) {
            $sql .= 'and hpa.audit_id=:audit_id';
            $where_data['audit_id'] = $audit_id;
        }
        if (!empty($staff_info_id)) {
            $sql .= 'and hpa.staff_info_id=:staff_info_id';
            $where_data['staff_info_id'] = $staff_info_id;
        }

        $arr = $this->getDI()->get('db_rby')->fetchOne($sql, \PDO::FETCH_ASSOC, $where_data);
        if (empty($arr)) {
            throw new \Exception($this->getTranslation()->_('4008'));
        }
        //获取员工信息
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $arr['staff_info_id'] ?? '',
            ],
            'columns'    => 'name,staff_info_id,hire_date,sys_store_id,job_title,sys_department_id,job_title_grade_v2',
        ]);
        $staffInfo = !empty($staffInfo) ? $staffInfo->toArray() : [];
        $arr = array_merge($arr, $staffInfo);
        $job_title = HrJobTitleModel::findFirst([
            'conditions' => 'id = :id: ',
            'bind'       => ['id' => $staffInfo['job_title'] ?? ''],
            'columns'    => 'id,job_name',
        ]);
        $arr['job_name'] = !empty($job_title) ? $job_title->job_name : '';
        $department_name = SysDepartmentModel::findFirst([
            'conditions' => 'id = :id: ',
            'bind'       => ['id' => $staffInfo['sys_department_id'] ?? ''],
            'columns'    => 'id,name',
        ]);
        $arr['department_name'] = !empty($department_name) ? $department_name->name : '';

        if (!empty($arr['hire_date'])) {
            $arr['hire_date'] = date('Y-m-d', strtotime($arr['hire_date']));
        }

        if (!empty($arr['avatar'])) {
            $arr['avatar'] = $this->getDI()['config']['application']['img_prefix'] . $arr['avatar'];
        } else {
            $arr['avatar'] = '';
        }


        //获取上一级的评估
        $audit_level = empty($arr['audit_level'] - 1) ? 1 : $arr['audit_level'] - 1;
        $auditCondition = [
            'conditions' => ' probation_id = :probation_id: and audit_level = :audit_level: and cur_level = :cur_level: ', // 上级
            'bind'       => [
                'probation_id' => $arr['probation_id'],
                'cur_level'    => $arr['cur_level'],
                'audit_level'  => $audit_level,
            ],
        ];
        $HrProbationAudit = $probationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3
            ? HrProbationAuditContractWorkerModel::findFirst($auditCondition)
            : HrProbationAuditModel::findFirst($auditCondition);
        $HrProbationAudit = $HrProbationAudit ? $HrProbationAudit->toArray() : [];

        $arr['first_audit_level'] = [
            'is_terminate'      => (string)($arr['is_terminate']),
            'last_is_terminate' => (string)($HrProbationAudit['is_terminate'] ?? 0),
            'remark'            => (string)($HrProbationAudit ? $HrProbationAudit['remark'] : ''),
        ];
        $first_audit_level_score = 0;
        if ($HrProbationAudit) {
            $first_audit_level_score_map = json_decode($HrProbationAudit['score'], true);
            $first_audit_level_score     = (int)($HrProbationAudit['cur_level'] == self::CUR_LEVEL_FIRST ? $first_audit_level_score_map['score'] : $first_audit_level_score_map['second_score']);
        }
        $arr['first_audit_level']['score'] = $first_audit_level_score;

        $arr['rules'] = [];

        $rule_num = 5;
        if ($arr['cur_level'] == self::CUR_LEVEL_SECOND) {
            $rule_num = 6;
        }
        for ($i = 1; $i <= $rule_num; $i++) {
            $arr['rules'][] = $this->getTranslation()->_('hr_probation_rule_' . $i);
        }
        if (isCountry('PH') &&  $arr['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT) {
            $arr['rules'][] = $this->getTranslation()->_('hr_probation_rule_common');
        }
        $arr['hire_type_text'] = empty($arr['hire_type']) ?'' : $this->getTranslation()->_('hire_type_'.$arr['hire_type']);

        $tpl = $this->getTplItem($arr['tpl_id'], $arr['score']);
        if (empty($tpl['code'])) {
            throw new \Exception(json_encode($tpl));
        }
        $tpl['score']['score'] = empty($tpl['score']['score']) ? $this->getScore($arr['first_score']) : $tpl['score']['score'];
        //计算规则
        $arr['score_rule_text'] = $tpl['score_rule_text'];
        $arr['score'] = $tpl['score'];

        $logs = $this->getProbationLogs($arr['probation_id'],$probationChannelType);
        if (empty($logs['code'])) {
            throw new \Exception(json_encode($logs));
        }
        $arr['audit_logs'] = $logs['data'];

        if ($arr['sys_store_id'] == -1) {
            $arr['sys_store_id'] = enums::HEAD_OFFICE;
        }

        $arr['is_can_active'] = $this->isCanActive($arr);

        $returnData['data'] = $arr;
        return $this->checkReturn($returnData);
    }

    /**
     * 月薪制合同工V3 提交转正评估
     * @param $hrProbationAudit
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function submitProbationEvaluationContractV3($hrProbationAudit, $params): array
    {
        $score = $params['score'];
        //下个审批人
        $manager_id           = $this->getManagerId($hrProbationAudit['audit_id']);
        $probationEdit        = []; //hr_probation表
        $hrProbationAuditEdit = []; //hr_probation_audit表
        //如果info与库里不同,证明已经修改
        foreach ($score['list'] as $k => $v) {
            foreach ($v['list'] as $kk => $vv) {
                if ($this->getTranslation()->_($vv['info_key']) != $vv['info']) {
                    $score['list'][$k]['list'][$kk]['is_update'] = 1;
                }
            }
        }
        //自己根据规则算一遍
        $score = $this->getScoreFromTpl($score, $hrProbationAudit['cur_level']);
        if (empty($score)) {
            throw new BusinessException('score error');
        }
        $score_num                     = $hrProbationAudit['cur_level'] == HrProbationModel::CUR_LEVEL_FIRST ? $this->getScore($score['score'],
            1) : $this->getScore($score['second_score'], 1);
        $status                        = $score_num >= 6000 ? HrProbationModel::STATUS_PASS : HrProbationModel::STATUS_NOT_PASS;
        $scoreEncode                   = json_encode($score, JSON_UNESCAPED_UNICODE);
        $remark                        = $params['remark'] ?? '';
        $pic                           = $params['pic'] ?? '';
        $jobContent                    = $params['job_content'] ?? '';
        $isTerminate                   = $params['is_terminate'] ?? 0;//完全由操作者决定
        $updateDate                    = date("Y-m-d H:i:s");
        $endLevel                      = $hrProbationAudit['audit_level'] == self::AUDIT_LEVEL_2;
        $probationEdit['audit_status'] = HrProbationAuditModel::AUDIT_STATUS_PROCESSED; // 已处理
        $probationEdit['updated_at']   = $updateDate;
        //第一阶段
        if ($hrProbationAudit['cur_level'] == HrProbationModel::CUR_LEVEL_FIRST) {
            $probationEdit['first_score'] = $score_num;
            if ($endLevel) {
                $probationEdit['first_status']       = $status == HrProbationModel::STATUS_PASS ? HrProbationModel::FIRST_STATUS_PASS : HrProbationModel::FIRST_STATUS_NOT_PASS;
                $probationEdit['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_DONE;
            }
        } else {
            //第二几阶段
            $probationEdit['remark']       = $remark;
            $probationEdit['second_score'] = $score_num;
            $probationEdit['status']       = $status;//第二阶段才处理主表状态
            if ($endLevel) {
                $probationEdit['second_status']       = $status == HrProbationModel::STATUS_PASS ? HrProbationModel::FIRST_STATUS_PASS : HrProbationModel::FIRST_STATUS_NOT_PASS;
                $probationEdit['second_audit_status'] = HrProbationModel::SECOND_AUDIT_STATUS_DONE;
            }
        }
        $hrProbationAuditEdit['follow_staff_id'] = $this->getManagerId($hrProbationAudit['staff_info_id']);
        $hrProbationAuditEdit['updated_at']      = $updateDate;
        $hrProbationAuditEdit['show_time']       = $updateDate;
        $hrProbationAuditEdit['score']           = $scoreEncode;
        $hrProbationAuditEdit['audit_status']    = HrProbationAuditModel::AUDIT_STATUS_PROCESSED; //已处理
        $hrProbationAuditEdit['remark']          = $remark;
        $hrProbationAuditEdit['pic']             = $pic;
        $hrProbationAuditEdit['is_terminate']    = $isTerminate;
        $hrProbationAuditEdit['job_content']     = $jobContent;
        $hrProbationAuditEdit['status']          = $status;
        $db = $this->db;
        try {
            $this->logger->write_log([ 'hrProbationAudit'=>$hrProbationAudit,'probationEdit' => $probationEdit,'hrProbationAuditEdit' =>  $hrProbationAuditEdit], 'info');
            $db->begin();
            $db->updateAsDict("hr_probation_audit_contract_worker", $hrProbationAuditEdit,
                ["conditions" => "id=" . $hrProbationAudit['id']]);
            $db->updateAsDict("hr_probation_contract_worker", $probationEdit,
                ["conditions" => 'id=' . intval($hrProbationAudit['probation_id'])]);
            //如果可以评估 并且有下一级评估人 并且没有转正
            if ($hrProbationAudit['audit_level'] == self::AUDIT_LEVEL_1 && !empty($manager_id) && $hrProbationAudit['hp_status'] != HrProbationModel::STATUS_FORMAL) {
                $db->insertAsDict("hr_probation_audit_contract_worker", [
                    'probation_id'  => $hrProbationAudit['probation_id'],
                    'staff_info_id' => $hrProbationAudit['staff_info_id'],
                    'tpl_id'        => $hrProbationAudit['tpl_id'],
                    'audit_id'      => $manager_id,
                    'audit_level'   => self::AUDIT_LEVEL_2,
                    'cur_level'     => $hrProbationAudit['cur_level'],
                    'audit_status'  => HrProbationAuditModel::AUDIT_STATUS_PENDING,
                    'status'        => 0,
                    'score'         => $scoreEncode,
                    'created_at'    => $probationEdit['updated_at'],
                    'updated_at'    => $probationEdit['updated_at'],
                    'remark'        => $remark,
                    'pic'           => $pic,
                    'job_content'   => $jobContent,
                    'version'       => $hrProbationAudit['version'],
                    'deadline_at'   => $this->getDateByDays($hrProbationAudit['deadline_at'], 3, 1),
                    'is_terminate'  => 0,
                ]);
                //发送待评估消息
                $this->sendConfirmationEvaluationMsg($manager_id);
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->write_log('submitProbationEvaluationContractV3---' . $e->getMessage() . '-----' . $e->getLine());
            return $this->checkReturn(["code" => ErrCode::SYSTEM_ERROR, 'msg' => 'system error']);
        }
        //最后审批后发送评估结果消息
        if ($endLevel && $hrProbationAudit['cur_level'] == HrProbationModel::CUR_LEVEL_SECOND) {
            $this->sendEvaluationResultsMessage($status, $hrProbationAudit);
        }
        //上上级评估终止工作发送签字消息
        if ($endLevel && $isTerminate == HrProbationAuditModel::IS_TERMINATE) {
            $this->sendAckNowledgement([
                'probation_id' => $hrProbationAudit['probation_id'],
                'cul_level'    => $hrProbationAudit['cur_level'],
                'id'           => $hrProbationAudit['id'],
                'probation_channel_type'           => HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3,
            ]);
        }
        //上上级不通过，给上级员工发送消息
        if ($hrProbationAudit['cur_level'] == self::CUR_LEVEL_SECOND && $hrProbationAudit['audit_level'] == HrProbationAuditModel::AUDIT_LEVEL_SECOND && $status == self::STATUS_NOT_PASS) {
            $this->sendMsgToManagerByStaffId($hrProbationAudit['staff_info_id'], $this->getManagerId($hrProbationAudit['staff_info_id']));
        }
        return $this->checkReturn(['data' => true]);
    }

    /**
     * 系统后台编辑分数 给被评估人发信息
     * @param $hrProbationAuditId
     * @return array
     * @throws ValidationException
     */
    public function submitProbationEvaluationContractV3FromHcm($hrProbationAuditId): array
    {
        $sql = "
            select
                hpa.*,
                hp.is_system,
                hp.formal_at
            from
                hr_probation_audit_contract_worker as hpa
                join hr_probation_contract_worker as hp on hp.id=hpa.probation_id
            where hpa.id=:id ";

        $hrProbationAudit = $this->db->fetchOne($sql, \PDO::FETCH_ASSOC, ["id" => $hrProbationAuditId]);
        if (empty($hrProbationAudit)) {
            $this->logger->write_log('submitProbationEvaluationContractV3FromHcm 没发现数据 ' . $hrProbationAuditId);
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $this->logger->write_log('submitProbationEvaluationContractV3FromHcm  ' . $hrProbationAuditId . ' ' . json_encode($hrProbationAudit),
            'info');
        if ($hrProbationAudit['cur_level'] == HrProbationModel::CUR_LEVEL_SECOND && $hrProbationAudit['is_system'] == HrProbationModel::IS_SYSTEM) {
            //给被评估人发消息
            $this->sendEvaluationResultsMessage($hrProbationAudit['status'], $hrProbationAudit);
            //未通过发送 签字消息 (主 附表 status 枚举一致)
            if ($hrProbationAudit['status'] == HrProbationModel::STATUS_NOT_PASS) {
                $this->sendAckNowledgement([
                    'probation_id' => $hrProbationAudit['probation_id'],
                    'cul_level'    => $hrProbationAudit['cur_level'],
                    'id'           => $hrProbationAudit['id'],
                    'probation_channel_type'           => HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3,
                ]);
            }
        } else {
            $this->logger->write_log('submitProbationEvaluationContractV3FromHcm  数据异常 不能发送 请检查 ' . $hrProbationAuditId);
        }
        return $this->checkReturn(['data' => true]);
    }
    /**
     * @return array
     */
    public function getCountryJobs()
    {
        return [enums::$job_title['bike_courier'], enums::$job_title['van_courier'], enums::$job_title['tricycle_courier'], enums::$job_title['truck_courier'], enums::$job_title['dc_officer']];
    }

    /**
     * @param $staffInfoId
     * @return int|mixed|void
     */
    public function syncStaffState($staffInfoId)
    {
        $staffServer = new StaffServer($this->lang, $this->timeZone);
        $staffInfo = $staffServer->getStaffInfoById($staffInfoId);

        if ($staffInfo) {
            if ($staffInfo['state'] == HrStaffInfoModel::STATE_RESIGN) {
                $this->getDI()->get('logger')->write_log(['syncStaffState' => $staffInfo, 'remark' => '员工已经离职'],
                    'info');
                return true;
            }

            $params = [
                'staff_info_id'   => $staffInfoId,
                'leave_type'      => HrStaffInfoModel::LEAVE_TYPE_DISMISSAL_NO_COMPENSATION,//辞退（不赔偿）
                'leave_reason'    => StaffLeaveReasonModel::LEAVE_REASON_85,                //未达到试用期标准
                'leave_scenario'  => LeaveScenarioModel::LEAVE_SCENARIO_55,                 // 员工未通过试用期（提前通知）
                'leave_source'    => 8,                                                     //试用期未通过
                'wait_leave_date' => $this->getLeaveDateForAck($staffInfo),
            ];
            $client = new ApiClient('hr_rpc', '', 'hr_staff_wait_leave');
            $client->setParams($params);
            $res = $client->execute();
            $this->getDI()->get('logger')->write_log('员工状态变更：' . json_encode($params) . '; 结果：' . json_encode($res), 'info');
            return $res['result']['code'] ?? 0;
        }
    }

    /**
     * 试用期月薪制合同工后台提交评估
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function storeProbationAuditByHCM($params): array
    {
        $hrProbationAudit = $this->getExpiredAndProbationaryPeriod($params['probation_id'] ?? 0);
        if (empty($hrProbationAudit)) {
            throw new BusinessException('No corresponding data found');
        }
        $params['is_system'] = HrProbationModel::IS_SYSTEM;
        return $this->submitProbationEvaluation($hrProbationAudit, $params);
    }

    /**
     * 月薪制合同工 待处理 评估超时
     * @param $probationId
     * @return mixed
     */
    public function getExpiredAndProbationaryPeriod($probationId)
    {
        $sql = "
            select
                hp.is_system,
                hp.formal_at,
                hp.hire_type,
                hp.status as hp_status,
                hpa.*
            from
                hr_probation as hp
                left join hr_probation_audit as hpa on hp.id=hpa.probation_id
            where
                hp.id=:id and ((hp.status=:status_1 and hp.audit_status=:audit_status_1) or hp.status=:status_2 or (hp.status=:status_3 and hp.formal_at > :now_date)) order by hpa.id desc";
        return $this->db_rby->fetchOne($sql, \PDO::FETCH_ASSOC,
            ["id"           => $probationId,
             'status_1'        => HrProbationModel::STATUS_PROBATION,
             'status_2'        => HrProbationModel::STATUS_NOT_PASS,
             'status_3'        => HrProbationModel::STATUS_PASS,
             'audit_status_1'  => HrProbationModel::AUDIT_STATUS_TIMEOUT,
             'now_date'        => date('Y-m-d'),
            ]);
    }
    /**
     * 月薪制合同工 提交转正评估
     * @param $hrProbationAudit
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function submitProbationEvaluation($hrProbationAudit, $params): array
    {
        $isSystem = $params['is_system'] ?? HrProbationModel::IS_NOT_SYSTEM;//这个系统操作是后台编辑分数过来的注意识别
        $score    = $params['score'];
        //下个审批人
        $manager_id           = $this->getManagerId($hrProbationAudit['audit_id']);
        $probationEdit        = []; //hr_probation表
        $hrProbationAuditEdit = []; //hr_probation_audit表

        //如果info与库里不同,证明已经修改
        foreach ($score['list'] as $k => $v) {
            foreach ($v['list'] as $kk => $vv) {
                if ($this->getTranslation()->_($vv['info_key']) != $vv['info']) {
                    $score['list'][$k]['list'][$kk]['is_update'] = 1;
                }
            }
        }
        //自己根据规则算一遍
        $score = $this->getScoreFromTpl($score, $hrProbationAudit['cur_level']);
        if (empty($score)) {
            throw new BusinessException('score error');
        }
        $score_num   = $this->getScore($score['score'], 1);
        $status      = $score_num >= 6000 ? HrProbationModel::STATUS_PASS : HrProbationModel::STATUS_NOT_PASS;
        $scoreEncode = json_encode($score, JSON_UNESCAPED_UNICODE);
        $remark      = $params['remark'] ?? '';
        $pic         = $params['pic'] ?? '';
        $jobContent  = $params['job_content'] ?? '';
        $isTerminate = $params['is_terminate'] ?? 0;//完全由操作者决定
        $updateDate  = gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600);

        $probationEdit['first_score'] = $score_num;
        $probationEdit['updated_at']  = $updateDate;
        if (!$isSystem) {
            $hrProbationAuditEdit['score']        = $scoreEncode;
            $hrProbationAuditEdit['audit_status'] = HrProbationAuditModel::AUDIT_STATUS_PROCESSED; //已处理
            $hrProbationAuditEdit['remark']       = $remark;
            $hrProbationAuditEdit['pic']          = $pic;
            $hrProbationAuditEdit['is_terminate'] = $isTerminate;
            $hrProbationAuditEdit['job_content']  = $jobContent;
            $hrProbationAuditEdit['status']       = $status;
            if ($hrProbationAudit['audit_level'] == self::AUDIT_LEVEL_2) {
                $probationEdit['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_DONE;
            }
        }
        $hrProbationAuditEdit['follow_staff_id'] = $this->getManagerId($hrProbationAudit['staff_info_id']);
        $hrProbationAuditEdit['updated_at']      = $updateDate;
        $endLevel                                = $hrProbationAudit['audit_level'] == self::AUDIT_LEVEL_2 || $isSystem;
        if ($endLevel) {
            $probationEdit['audit_status']       = HrProbationAuditModel::AUDIT_STATUS_PROCESSED; // 已处理
            $probationEdit['status']             = $status;
            $probationEdit['first_status']       = $probationEdit['status'] == HrProbationModel::STATUS_PASS ? HrProbationModel::FIRST_STATUS_PASS : HrProbationModel::FIRST_STATUS_NOT_PASS;
        }



        if ($isSystem) {
            $probationEdit['is_system'] = HrProbationModel::IS_SYSTEM;
        }
        $db = $this->db;
        try {
            $db->begin();
            $db->updateAsDict("hr_probation_audit", $hrProbationAuditEdit,
                ["conditions" => "id=" . $hrProbationAudit['id']]);
            $db->updateAsDict("hr_probation", $probationEdit,
                ["conditions" => 'id=' . intval($hrProbationAudit['probation_id'])]);
            //如果可以评估 并且有下一级评估人 并且没有转正 并不是系统操作
            if ($hrProbationAudit['audit_level'] == self::AUDIT_LEVEL_1 && !empty($manager_id) || $isSystem) {

                $audit_id = $isSystem ? ($params['operate_id'] ?? 0) : $manager_id;

                $db->insertAsDict("hr_probation_audit", [
                    'probation_id'  => $hrProbationAudit['probation_id'],
                    'staff_info_id' => $hrProbationAudit['staff_info_id'],
                    'tpl_id'        => $hrProbationAudit['tpl_id'],
                    'audit_id'      => $audit_id,
                    'audit_level'   => $isSystem ? 0 : 2,//2上上级
                    'cur_level'     => $hrProbationAudit['cur_level'],
                    'audit_status'  => $isSystem ? HrProbationAuditModel::AUDIT_STATUS_PROCESSED : HrProbationAuditModel::AUDIT_STATUS_PENDING,
                    'status'        => $isSystem ? $status : 0,
                    'score'         => $scoreEncode,
                    'created_at'    => $probationEdit['updated_at'],
                    'updated_at'    => $probationEdit['updated_at'],
                    'remark'        => $remark,
                    'pic'           => $pic,
                    'job_content'   => $jobContent,
                    'version'       => $hrProbationAudit['version'],
                    'deadline_at'   => $this->getDateByDays($hrProbationAudit['deadline_at'], 3, 1),
                    'is_terminate'  => $isSystem ? 0 : $isTerminate,
                ]);
                if (!$isSystem) {
                    //发送待评估消息
                    $this->sendConfirmationEvaluationMsg($audit_id);
                }


            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->write_log('submitProbationEvaluation---' . $e->getMessage() . '-----' . $e->getLine());
            return $this->checkReturn(["code" => ErrCode::SYSTEM_ERROR, 'msg' => 'system error']);
        }
        //最后审批后发送评估结果消息
        if ($endLevel) {
            $this->sendEvaluationResultsMessage($status, $hrProbationAudit);
        }
        //最终评估不通过 发送签字消息
        if ($isSystem && $status == HrProbationModel::STATUS_NOT_PASS ||  $hrProbationAudit['audit_level'] == self::AUDIT_LEVEL_2 && $isTerminate == HrProbationAuditModel::IS_TERMINATE && !$isSystem) {
            $this->sendAckNowledgement([
                'probation_id' => $hrProbationAudit['probation_id'],
                'cul_level'    => $hrProbationAudit['cur_level'],
                'id'           => $hrProbationAudit['id'],
                'probation_channel_type'           => HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT,
            ]);
        }
        return $this->checkReturn(['data' => true]);
    }


    /**
     * 给评估人发消息
     * @param $staff_info_id
     * @return array|bool|mixed|null
     */
    protected function sendConfirmationEvaluationMsg($staff_info_id)
    {
        if (empty($staff_info_id)) {
            return false;
        }

        $client = new ApiClient('hcm_rpc', '', 'send_confirmation_evaluation_msg', $this->lang);
        $client->setParams(['staff_info_id' => $staff_info_id]);
        return $client->execute();
    }
    

    /**
     * 发送 通过或者不通过消息
     * @param $status
     * @param $hrProbationAudit
     * @return void
     */
    public function sendEvaluationResultsMessage($status, $hrProbationAudit)
    {
        $staffInfo = (new StaffRepository())->getStaffInfoAllOne($hrProbationAudit['staff_info_id']);
        $lang      = (new StaffServer())->getLanguage($hrProbationAudit['staff_info_id']);
        $t         = $this->getTranslation($lang);
        $params    = [
            'name_id'    => $staffInfo['name'] . '/' . $staffInfo['staff_info_id'],
            'department' => $staffInfo['department_name'],
            'job_name'   => $staffInfo['job_title_name'],
            'store_name' => $staffInfo['store_name'],
            'name'       => $staffInfo['name'],
        ];
        if ($status == HrProbationModel::STATUS_PASS) {
            $params['formal_at'] = $hrProbationAudit['formal_at'];
            $html                = addslashes("<div style='font-size: 30px'>" . $t->_("hr_probation_passed_msg_ph",
                    $params) . "</div>");
        } else {
            $html = addslashes("<div style='font-size: 30px'>" . $t->_("hr_probation_not_passed_msg",
                    $params) . "</div>");
        }
        $param['staff_users']        = [$hrProbationAudit['staff_info_id']];//数组 多个员工id
        $param['message_title']      = $t->_('hr_probation_field_msg_to_staff_title');
        $param['message_content']    = $html;
        $param['staff_info_ids_str'] = $hrProbationAudit['staff_info_id'];
        $param['id']                 = time() . $hrProbationAudit['staff_info_id'] . rand(1000000, 9999999);
        $param['category']           = -1;

        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $lang));
        $bi_rpc->setParams($param);
        $res = $bi_rpc->execute();
        $this->logger->write_log([$param,$res], 'info');
    }

     /**
     * 获取离职时间
     * @param $staffInfo
     * @return false|string
     */
    public function getLeaveDateForAck($staffInfo)
    {
        $add_hour = $this->config->application->add_hour;
        $jobs     = $this->getCountryJobs();

        if ($staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_2) {
            return  gmdate("Y-m-d", time() + $add_hour * 3600 + 86400);
        }
        if (in_array($staffInfo['job_title'], $jobs) || in_array($staffInfo['node_department_id'],
                $this->getSpecialDepartmentForLEaveDate())) {
            $leaveDate = gmdate("Y-m-d", time() + $add_hour * 3600 + 86400);
        } else {
            $leaveDate = gmdate("Y-m-d", time() + $add_hour * 3600 + 14 * 86400);
        }

        return $leaveDate;
    }

    /**
     * Hub Management一级部门以及子部门
     * Flash Fulfillment - Philippines Fulfillment - Warehouse Operations部门以及子部门
     */
    protected function getSpecialDepartmentForLEaveDate(): array
    {
        $model                  = new SysDepartmentModel;
        $hubManagementIds       = $model->getSpecifiedDeptAndSubDept(126);
        $warehouseOperationsIds = $model->getSpecifiedDeptAndSubDept(27);
        return array_merge($hubManagementIds, $warehouseOperationsIds);
    }
    /**
     * 被终止工作的员工 发送 Acknowledgement
     * 筛选逻辑来源于 send_msg_on_terminate 方法
     * 第一阶段   最后一级 选择不让员工继续工作后，入职90天后，直接发送Acknowledgement签字消息给员工
     * 第二阶段   最后一级 评分低于6分(不让员工继续工作)，入职145天后，直接发送Acknowledgement签字消息给员工
     * @param $params
     * @param $check_days -- 入职多少天
     * @param int $cur_level -- 1上级审批 & 2 上上级审批
     * @param int $is_exceed -- 17 级以上的 有cpo 参与审批的
     */
    public function taskSendAcknowledgement($params, $check_days,$cur_level=1,$is_exceed = 0,$hcm_params = [])
    {
        if (isset($params[0])) {
            $formalDate = $params[0];
            if (strtotime($formalDate) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
            if (isset($params[1])) {
                $staffIds = $params[1];
            }
        } else {
            $formal_days =  !empty($is_exceed) ? $this->formal_days_exceed : $this->formal_days;
            $formalDate = $this->getDateByDays(date('Y-m-d'), ($formal_days - $check_days), 1);
            //脚本凌晨1点执行导致的不包含当天的场景
            $formalDate = date('Y-m-d', strtotime($formalDate .' -1 day'));
        }
        $probationConditions = 'formal_at = :formal_at: and cur_level = :cur_level: and hire_type!=:hire_type: and status in ({status:array})';
        $probationBind = ['formal_at' => $formalDate, 'cur_level' => $cur_level,'hire_type' => HrStaffInfoModel::HIRE_TYPE_2,'status' =>[HrProbationModel::STATUS_PROBATION,HrProbationModel::STATUS_NOT_PASS]];
        if (!empty($hcm_params['staff_info_id'])){
            $probationConditions = 'formal_at <= :formal_at: and cur_level = :cur_level: and hire_type!=:hire_type: and status in ({status:array}) and staff_info_id = :staff_info_id:';
            $probationBind = ['staff_info_id' => $hcm_params['staff_info_id'],'formal_at' => $formalDate, 'cur_level' => $cur_level,'hire_type' => HrStaffInfoModel::HIRE_TYPE_2,'status' =>[HrProbationModel::STATUS_PROBATION,HrProbationModel::STATUS_NOT_PASS]];
        }
        if (isset($staffIds)) {
            $probationConditions .= ' and staff_info_id in ({staff_ids:array})';
            $probationBind['staff_ids'] = $staffIds;
        }
        $hrProbations = HrProbationModel::find([
            'conditions' => $probationConditions,
            'bind' => $probationBind,
        ])->toArray();
       $log = 'taskSendAcknowledgement formal_days'. ($formal_days ?? 0) .' formalDate' . $formalDate .'  cur_level' . $cur_level. ' hrProbations staffIds ' . implode(',',array_column($hrProbations,'staff_info_id'));
//       echo $log. PHP_EOL;
       $log .= ' check_days '.$check_days;
       $log .= ' is_exceed '.$is_exceed;
       $this->logger->write_log($log,'info');
        $probationIds =
            array_column($hrProbations, 'id');
        if ($probationIds) {
            $probationChannelTypeMap = array_column($hrProbations,'probation_channel_type','id');
            $conditions = 'probation_id in ({probation_ids:array}) and cur_level = :cur_level: and is_terminate = 1 ';
            $bind      = [
                'probation_ids' => $probationIds,
                'cur_level'     => $cur_level,
            ];
            if (empty($hcm_params['staff_info_id'])){
                if (!empty($is_exceed)) {
                    $conditions       .= " and audit_id = :audit_id: and version = :version:";
                    $bind['audit_id'] = $this->cpo_staff_id;
                    $bind['version'] = $this->version;
                }else{
                    $conditions       .= '  and audit_level = 2 ';
                }
            }
            $probationAudits = HrProbationAuditModel::find([
                'conditions' => $conditions, // 查询  上上级审批 且是终止
                'bind'       => $bind,
            ])->toArray();


            $this->logger->write_log('taskSendAcknowledgement ' . $formalDate .' ' . $cur_level. ' HrProbationAuditModel ' . json_encode($probationAudits, JSON_UNESCAPED_UNICODE), 'info');

            if ($probationAudits) {
                $staffIds = array_column($probationAudits, 'staff_info_id');
                $staffs = HrStaffInfoModel::find([
                    'conditions' => ' staff_info_id in ({staff_ids:array})',
                    'bind' => ['staff_ids' => $staffIds],
                ])->toArray();
                $staffs = array_column($staffs, null, 'staff_info_id');

                //去下重
                $probationAudits = array_column($probationAudits, null,'staff_info_id');
                foreach ($probationAudits as $probationAudit) {
                    if (isset($staffs[$probationAudit['staff_info_id']])) {
                        //过滤$is_exceed 为 0 并且大于17级  ||  $is_exceed =1 小于 18 级的数据  并且 version = $this->version
                        if(
                            ((empty($is_exceed) && $staffs[$probationAudit['staff_info_id']]['job_title_grade_v2'] > $this->job_grade_exceed)
                                ||
                                (!empty($is_exceed) && $staffs[$probationAudit['staff_info_id']]['job_title_grade_v2'] <= $this->job_grade_exceed))
                            &&  $probationAudit['version'] == $this->version
                        ){
                            $this->logger->write_log('taskSendAcknowledgement -:'.json_encode([
                                    'staff_info_id' => $probationAudit['staff_info_id'],
                                    'job_title_grade_v2' => $staffs[$probationAudit['staff_info_id']]['job_title_grade_v2'],
                                    'is_exceed'=>$is_exceed,
                                    'formalDate'=>$formalDate,
                                    'cur_level'=>$cur_level,
                                    'probation_channel_type' => $probationChannelTypeMap[$probationAudit['probation_id']],
                                ]), 'info');
                            continue;
                        }
                    }
                    if (!empty($probationAudit['acknowledgement_message_id'])){
                        continue;
                    }
                    $this->sendAckNowledgement([
                        'probation_id' => $probationAudit['probation_id'],
                        'id'           => $probationAudit['id'],
                        'cul_level'    => $cur_level,
                        'probation_channel_type' => $probationChannelTypeMap[$probationAudit['probation_id']]
                    ]);
                    $this->db->updateAsDict("hr_probation_audit", [
                        'follow_staff_id' => $staffs[$probationAudit['staff_info_id']]['manger'],
                    ], ["conditions" => "id=" . intval($probationAudit['id'])]);
                }
            }
        }

    }

    /**
     * 过了转正时间，但是由于超时未评估依然是试用期状态，需要自动转正
     */
    public function autoConvert($day)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hp' => HrProbationModel::class]);
        $builder->join(HrStaffInfoModel::class, " hsi.staff_info_id = hp.staff_info_id", 'hsi');
        $builder->columns(['hp.staff_info_id,hp.status']);
        $builder->where('hsi.state=:state: and is_sub_staff=:is_sub_staff: and hp.status!=:status: and datediff(curdate(),date(hsi.hire_date)) > :time: and hp.hire_type!=:hire_type:',
            [
                'is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0,
                'state'        => HrStaffInfoModel::STATE_ON_JOB,
                'status'       => HrProbationModel::STATUS_FORMAL,
                'time'         => $day,
                'hire_type'    => HrStaffInfoModel::HIRE_TYPE_2,
            ]);
        $list     = $builder->getQuery()->execute()->toArray();
        $staffIds = array_column($list, 'staff_info_id');
        if ($staffIds) {
            $data['status']     = HrProbationModel::STATUS_FORMAL;
            $data['updated_at'] = gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600);
            $this->db->updateAsDict("hr_probation", $data,
                ["conditions" => 'staff_info_id in (' . implode(",", $staffIds) . ')']);
            $insertLogData = [];
            foreach ($list as $item) {
                $insertLogData[] = [
                    "operater"      => 10000,
                    "staff_info_id" => $item['staff_info_id'],
                    "type"          => 'hr_probation',
                    "before"        => json_encode(['body' => ['status' => $item['status']]],
                        JSON_UNESCAPED_UNICODE),
                    "after"         => json_encode(['body' => ['status' => HrProbationModel::STATUS_FORMAL]],
                        JSON_UNESCAPED_UNICODE),
                ];
            }
            (new BaseRepository())->batch_insert('hr_operate_logs', $insertLogData);
            $this->logger->write_log(implode(',', $staffIds), 'info');
        }
    }

    public function addAttendance($data)
    {
        $dbName = 'hr_probation_attendance';
        if (isset($data['probation_channel_type']) && $data['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3) {
            $dbName = 'hr_probation_attendance_contract_worker';
        }
        if (array_key_exists('probation_channel_type', $data)) {
            unset($data['probation_channel_type']);
        }
        $startDate = null;
        if (isset($data['contract_formal_date'])) {
            $startDate = $data['contract_formal_date'];
        }
        if (array_key_exists('contract_formal_date', $data)) {
            unset($data['contract_formal_date']);
        }
        $data['late']   = $this->getLateMinute($data['staff_info_id'], $startDate);
        $data['sick']   = $this->getDaysByType($data['staff_info_id'], 3, $startDate); //病假
        $data['casual'] = $this->getDaysByType($data['staff_info_id'], 23, $startDate);//紧急事假
        $data['lack']   = $this->getLackNum($data['staff_info_id'], $startDate);
        return $this->db->insertAsDict($dbName, $data);
    }

    /**
     * 迟到分钟数
     * @param $staff_info_id
     * @param null $startDate
     * @return int|mixed
     */
    public function getLateMinute($staff_info_id,$startDate = null)
    {
        $now = date('Y-m-d');
        $sql  = "
            select sum(late_times)/10*60  as num from attendance_data_v2 where staff_info_id=:id and stat_date<'$now'
        ";
        if ($startDate) {
            $sql .= " and stat_date>='$startDate'";
        }
        $item = $this->db_rby->fetchOne($sql, \PDO::FETCH_ASSOC, ["id" => $staff_info_id]);
        if (empty($item['num'])) {
            return 0;
        }
        return $item['num'];
    }

    /**
     * 获得缺勤天数
     * @param $staff_info_id
     * @param null $startDate
     * @return integer
     */
    public function getLackNum($staff_info_id,$startDate = null): int
    {
        $now = date('Y-m-d');
        $sql  = "
            select sum(AB) as num from attendance_data_v2 where staff_info_id=:id and stat_date<'$now'
        ";
        if ($startDate) {
            $sql .= " and stat_date>='$startDate'";
        }
        $item = $this->db_rby->fetchOne($sql, \PDO::FETCH_ASSOC, ["id" => $staff_info_id]);
        if (empty($item['num'])) {
            return 0;
        }
        return $item['num'];
    }

    /**
     * 删除历史发送的消息数据
     * @param $probation_data
     * @param $staff_info
     * @param $status
     * @return void
     */
    public function hcm_edit_score_restart_signature($probation_data,$staff_info,$status,$probation_audit_id,$db)
    {
        // 产品要求 离职 和 待离职 都不执行
        if ($staff_info['state'] == HrStaffInfoModel::STATE_2 || ($staff_info['state'] == HrStaffInfoModel::STATE_1 && $staff_info['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE)) {
            return;
        }
        // 删除历史发送的消息数据
        $this->deleteMessageData($probation_data);
        // 删除历史发送的ack消息数据
        $probation_obj = HrProbationModel::findFirst([
            'conditions' => "id = :id:",
            'bind'       => ['id' => $probation_data['id']],
        ]);
        if ($probation_obj) {
            $this->logger->write_log('hcm_edit_score_restart_signature staff_info_id: ' . $probation_data['staff_info_id'] . ' pdf_path:  ' . $probation_obj->pdf_path . " sign_time: " . $probation_obj->sign_time . " sign_url: " . $probation_obj->sign_url,
                "info");
            $probation_obj->pdf_path  = '';
            $probation_obj->sign_time = '1970-01-01 00:00:00';
            $probation_obj->sign_url  = '';
            $probation_obj->before_sign_pdf_url  = '';
            $probation_obj->save();
        }

        $audit_data = HrProbationAuditModel::find([
            'conditions' => 'probation_id = :probation_id:',
            'bind'       => ['probation_id' => $probation_data['id']],
        ])->toArray();
        $db->updateAsDict("hr_probation_audit", [
            "acknowledgement_message_id" => '',
            'follow_staff_id' => 0,
            'is_terminate' => 0
        ], ["conditions" => "id != ".$probation_audit_id." and probation_id = ".$probation_data['id']]);
        if ($audit_data){
            foreach ($audit_data as $item){
                if (!empty($item['acknowledgement_message_id'])){
                    $message_courier = MessageCourierModel::findFirst([
                        'conditions' => "id = :id:",
                        'bind' => ['id' => $item['acknowledgement_message_id']],
                    ]);
                    if ($message_courier) {
                        $message_courier->is_del = MessageCourierModel::IS_DELETED_YES;
                        $message_courier->save();
                    }
                }
            }
        }
        if ($status == HrProbationModel::STATUS_NOT_PASS){
            // 重新触发ack发送
            if ($staff_info['job_title_grade_v2'] > $this->job_grade_exceed){
                $this->taskSendAcknowledgement([], $this->second_check_days_exceed,2, 1,['staff_info_id' => $probation_data['staff_info_id']]);
            }else{
                $this->taskSendAcknowledgement([], $this->second_check_days,2,0,['staff_info_id' => $probation_data['staff_info_id']]);
            }
        }
        // 重新触发签字
        if ($probation_data['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE){
            // 发送阶段完成发送签字消息 被评估人和被评估人的上级
            $rpc_params = [
                'staff_info_id' => $probation_data['staff_info_id'],
                'customize_cur_level' => 2,
                'lang' => $this->lang,
            ];
            $this->logger->write_log('hcm_edit_score_restart_signature data  ' . json_encode($rpc_params), "info");
            $this->redisLib->lpush(RedisEnums::SEND_PROBATION_STAGE_DONE_MESSAGE, json_encode($rpc_params));
        }
    }
}
