<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use  FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\OtherServer as BaseOtherServer;


class OtherServer extends BaseOtherServer
{

    /**
     * 网点快递员出纳角色/ 网点经理/ 主管打卡提醒消息
     * @Access  public
     * @Param   request
     * @Return  array
     * @param $paramIn
     * @param $userinfo
     * @return array
     */
    public function remittanceDialogS($paramIn, $userinfo)
    {
        $returnArr['dialog_status'] = 0;
        $returnArr['dialog_msg'] = '';
        $returnArr['dialog_must_status'] = 0;
        $returnArr['is_ces_tra'] = 0;
        $country_pre = '_'.strtolower(env('country_code', 'th'));
        if ($paramIn['clock_type'] == 1) {
            //打卡开关 就是只提示，可以跳过
            $can_jump = false;
            $set_model = SettingEnvModel::findFirst("code = 'by_clock_open' ");
            if (!empty($set_model) && $set_model->set_val) {
                $can_jump = true;
            }

            //验证用户是否在ces培训
            if($this->checkStaffIsCesTraining($userinfo)){
                $returnArr['is_ces_tra'] = 1;
                $can_jump = true;
            }

            //仓管员/快递员角色打下班卡时提示自己未回款的总金额：
            //网点经理/ 主管打下班卡时提示本网点快递员未回款总金额：
            $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.StaffInfoSvc','offDuty', $this->lang);
            $fle_rpc->setParams(['staff_info_id'=>$userinfo['id']]);
            $fle_return = $fle_rpc->execute();
       
            $this->logger->write_log('remittanceDialogS offDuty staff_id:'.$userinfo['id']. ' java return:'.json_encode($fle_return,JSON_UNESCAPED_UNICODE) .' staff_info:'.json_encode($userinfo['id'],JSON_UNESCAPED_UNICODE),'info');

            if($fle_return['code'] == 1){
                $data = $fle_return['result'];
                //本业务不验证，可以下班打卡
                if($data['off_duty_enabled'] == true){
                    //没有提示，直接流转到下一个流程
                    if(empty($data['notes'])){
                        $returnData['data'] = $returnArr;
                        return $this->checkReturn($returnData);
                    }
                    $msg = "";
                    //有提示 拼在一起 返回
                    if(!empty($data['notes'])){
                        foreach ($data['notes'] as $note) {
                            $msg .=  $note['message']." \r\n";
                        }
                    }
                    $returnArr['dialog_status'] = 1;
                    $returnArr['dialog_must_status'] = 0;
                    $returnArr['dialog_msg'] = $msg;
                    $returnData['data'] = $returnArr;
                    return $this->checkReturn($returnData);
                }
                //不能下班 必然是有原因的
                //有提示 拼在一起
                if($data['off_duty_enabled'] == false && !empty($data['notes'])){
                    $msg = "";
                    foreach ($data['notes'] as $note) {
                        if(isset($note['business_type']) && $note['business_type'] == 'un_count_batch'){
                            $can_jump = false;
                        }
                        $msg .=  $note['message']." \r\n";
                    }
                    $returnArr['dialog_status'] = 1;
                    $returnArr['dialog_must_status'] = $can_jump?0:1;
                    $returnArr['dialog_msg'] = $msg;
                    $returnData['data'] = $returnArr;
                    return $this->checkReturn($returnData);
                }
            }
        }
        if ($paramIn['clock_type'] == 2) {
            if ( in_array(3, $userinfo['positions']) || in_array(18, $userinfo['positions'])) {
                //网点经理/ 主管打上班卡时显示快递员因回公款停职的员工
                $res = (new OtherRepository())->stopDutiestaff($userinfo);
                if( $res ){
                    $staffids = implode( array_column($res,'staff_info_id') ,"\r\n" );
                    $show_msg = $this->getTranslation()->_('remittanceDialogMsg_5'.$country_pre)??$this->getTranslation()->_('remittanceDialogMsg_5');
                    $show_msg = str_replace('{staffids}',$staffids,$show_msg);
                    $returnArr['dialog_status'] = 1;
                    $returnArr['dialog_msg'] = $show_msg;
                }
            }else if (in_array(4, $userinfo['positions'])   ) {
                //网点出纳角色/ 打上班卡时显示快递员未上缴的金额和应汇款的金额：
                $res = (new OtherRepository())->courierReceivable($userinfo, 2);
                if(isset($res)){
                    //查询根据网点员工id查询回款情况
                    $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.StoreReceivableBillSvc','findUnpaidByStoreId', $this->lang);
                    $fle_rpc->setParams($userinfo['organization_id']);
                    $fle_return = $fle_rpc->execute();
                    if(isset($fle_return['result']['unpaid_amount_count']) && $fle_return['result']['unpaid_amount_count'] > 0){
                        $res['receivable_amount'] = $fle_return['result']['unpaid_total_amount'];
                        $res['receivable_count'] = $fle_return['result']['unpaid_amount_count'];
                    }
                }
                $resCod = (new OtherRepository())->onCodReceivable($userinfo);
                if(isset($resCod)){
                    //查询根据网点员工id查询回款情况
                    $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.StoreReceivableBillSvc','findUnpaidStoreCodBillTotalById', $this->lang);
                    $fle_rpc->setParams($userinfo['organization_id']);
                    $fle_return = $fle_rpc->execute();
                    if(isset($fle_return['result']['unpaid_amount_count']) && $fle_return['result']['unpaid_amount_count'] > 0){
                        $res['receivable_amount'] = $fle_return['result']['unpaid_total_amount'];
                        $res['receivable_count'] = $fle_return['result']['unpaid_amount_count'];
                    }
                }
                $resParce = (new OtherRepository())->onParcelReceivable($userinfo);
                if(isset($resCod)){
                    //查询根据网点员工id查询回款情况
                    $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.StoreReceivableBillSvc','findUnpaidStoreBillTotalById', $this->lang);
                    $fle_rpc->setParams($userinfo['organization_id']);
                    $fle_return = $fle_rpc->execute();
                    if(isset($fle_return['result']['unpaid_amount_count']) && $fle_return['result']['unpaid_amount_count'] > 0){
                        $res['receivable_amount'] = $fle_return['result']['unpaid_total_amount'];
                        $res['receivable_count'] = $fle_return['result']['unpaid_amount_count'];
                    }
                }
                if( $res['receivable_count'] > 0){
                    $amount = bcadd( $resCod['cod_amount'] , $resParce['parcel_amount'] , 2 ) ;
                    $show_msg = $this->getTranslation()->_('remittanceDialogMsg_3'.$country_pre)??$this->getTranslation()->_('remittanceDialogMsg_3');
                    $show_msg = str_replace('{receivable_amount}',$res['receivable_amount'],$show_msg);
                    $show_msg = str_replace('{amount}',$amount,$show_msg);
                    $returnArr['dialog_status'] = 1;
                    $returnArr['dialog_msg'] = $show_msg;
                }
            }
        }
        $returnData['data'] = $returnArr;
        return $this->checkReturn($returnData);
    }

}
