<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\FleetAuditModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Server\FleetServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\WorkflowServer;


class FleetServer extends GlobalBaseServer
{


    /**
     * FDC国家差异化逻辑
     * @throws ValidationException
     */
    protected function countryFDCLogic($t, $arriveTime, $auditType, $fdCourierId, $source, $paramIn): array
    {
        $settingEnv                        = new SettingEnvServer();
        $fleet_expected_arrival_time_check = $settingEnv->getSetVal('fleet_expected_date_check');//期望到达时间限制
        $now                               = date('Y-m-d H:i:s');
        if ($fleet_expected_arrival_time_check && $auditType == enums::$fleet_audit_type['Normal']) {
            [$check_min, $check_max] = json_decode($fleet_expected_arrival_time_check, true);
            if (strtotime($arriveTime) < strtotime($now . ' + ' . $check_min . ' Hours')) {
                throw new ValidationException($t->_('fleet_expected_date_check_start', ['x' => $check_min]));
            }
            if (strtotime($arriveTime) > strtotime($now . ' + ' . $check_max . ' Hours')) {
                throw new ValidationException($t->_('fleet_expected_date_check_end', ['x' => $check_max]));
            }
        }
        $setFlag         = self::FDC_STATE_NOT_BY;
        $runningMileages = 0;
        return [$setFlag, $runningMileages];
    }

    /**
     * @param $paramIn
     * @return void
     * @throws ValidationException
     */
    public function batchAddValidation($paramIn)
    {
        if (empty($paramIn['audit_type'])) {
            throw new ValidationException('[audit_type]' . $this->getTranslation()->_('miss_args'));
        }
        $validations = [
            "car_type" => "Required|Int",
            "capacity" => "Required|IntGtLe:0,5000",
            "reason"   => "StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('1019'),
        ];
        if ($paramIn['audit_type'] == enums::$fleet_audit_type['Normal']) {
            $validations['store_ids'] = "Required|Arr";
        }
        if ($paramIn['audit_type'] == enums::$fleet_audit_type['FD_courier']) {
            $validations['start_store'] = "Required|StrLenGe:1";
            $validations['end_store']   = "Required|StrLenGe:1";
        }
        $this->validateCheck($paramIn, $validations);
    }
    public function setPropertyV1(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $detail = FleetAuditModel::findFirst($auditId);
        if (empty($detail)) {
            throw new \Exception("fleet info error");
        }
        if ($isFinal) {
            $this->getDI()->get('db')->updateAsDict(
                'fleet_audit',
                ['status' => $state],
                [
                    'conditions' => 'id = ?',
                    'bind'       => [$auditId],
                ]

            );

            //更新staff_audit_approval表
            $data = StaffAuditApprovalModel::findFirst([
                'conditions' => 'type = :type: and audit_id = :audit_id: and is_shown = 2 and level = 1',
                'bind'       => [
                    'audit_id' => $auditId,
                    'type'     => AuditListEnums::APPROVAL_TYPE_FLEET,
                ],
            ]);
            if ($data instanceof StaffAuditApprovalModel) {
                $data->status = $state;
                $data->update();
            }
            //新增逻辑 撤销操作要同步 java
            $setting = (new SettingEnvServer())->getSetVal('fleet_sync_switch');
            if ($state == enums::APPROVAL_STATUS_CANCEL && (!empty($setting) && $setting == 1)) {
                $url                = $this->config->api->java_http_url;
                $param['serial_no'] = $detail->getSerialNo();
                $url                .= "/svc/fleet/audit/tandem/change_status";
                $res                = $this->httpPost($url, $param);
                if (empty($res['code']) || $res['code'] != 1) {//回滚
                    $this->logger->write_log("fleet sync cancel {$auditId} ".json_encode($param).json_encode($res));
                    throw new \Exception('fleet sync cancel error');
                }
            }

            //发送消息
            $this->sendMassage($state, $detail->toArray());
        } else {
            $approvals  = $extend['approval'];
            $nextNodeId = $extend['next_node_id'];
            if ($state != enums::APPROVAL_STATUS_APPROVAL) {
                return true;
            }
            //审批同意
            $this->fleet->updateFleetInTime(['fleetId' => $auditId]);

            //需重新获取下
            $detail = FleetAuditModel::findFirst($auditId);

            //汽运中控专员、汽运中控经理
            //当前节点是否为最后一个审批节点
            $server = new WorkflowServer($this->lang, $this->timezone);
            $applyModel = AuditApplyModel::findFirst([
                'conditions' => 'biz_value = :audit_id: and biz_type = :audit_type:',
                'bind' => [
                    'audit_id' => $auditId,
                    'audit_type' => AuditListEnums::APPROVAL_TYPE_FLEET
                ]
            ]);
            $isFinal = $server->isFinalNode($applyModel, $extend['staff_id'], $nextNodeId);

            $auditInfo = WorkflowNodeModel::findFirst([
                'conditions' => 'id = :node_id:',
                'bind' => ['node_id' => $nextNodeId]
            ]);

            if (!$isFinal) {
                return true;
            }
            $auditInfo = $auditInfo->toArray();
            //ccd 前一个审批节点 审核通过 需要同步给 ms 一些数据
            $this->logger->write_log(sprintf("fleet id {%s} update ,result => %s", $auditId,
                json_encode($auditInfo)), 'info');

            $model               = new StaffAuditApprovalModel();
            $model->type         = AuditListEnums::APPROVAL_TYPE_FLEET;
            $model->audit_id     = $auditId;
            $model->level        = 1;
            $model->status       = enums::$audit_status['panding_approval'];
            $model->is_shown     = 2;
            $model->submitter_id = $detail->submitter ?? '';
            $model->staff_ids    = implode(',', $approvals);
            $model->save();

            //多国家开关
            $setting = (new SettingEnvServer())->getSetVal('fleet_sync_switch');
            if (empty($setting) || $setting != 1) {
                return true;
            }
            $this->sendFleetDataToMs($detail);
        }
    }

    /**
     * @description:发送信息给申请人和审批人 https://flashexpress.feishu.cn/docx/OTlTda5OPoD14Bx5Nqocun7GnDf
     * @param string $state 审批状态
     * @param array $fleet_info 数据
     * @return bool :
     * @author: L.J
     * @time: 2022/11/4 11:15
     */

    public function sendMassage($state = '', $fleet_info = [])
    {
        //驳回 和同意
        if (!in_array($state, [
                enums::APPROVAL_STATUS_REJECTED,
                enums::APPROVAL_STATUS_APPROVAL,
            ]) || empty($fleet_info)) {
            return false;
        }
        $submitter_id = $fleet_info['submitter_id'] ?? '';
        //发短信给申请人
        //判断申请人是否在职 和获取手机号
        $submitter_info = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $submitter_id,
            ],
            'columns'    => "staff_info_id,mobile,name,state",
        ]);
        //找不到申请人
        if (empty($submitter_info)) {
            return false;
        }
        $submitter_info = $submitter_info->toArray();
        //查找已经审批同意的人
        $approval_list = AuditApprovalModel::find([
            'columns'    => 'approval_id',
            'conditions' => " biz_value = :value: and biz_type = :type: and state = :state: and deleted = 0",
            'bind'       => [
                'type'  => enums::$audit_type['VA'],
                'value' => $fleet_info['id'],
                'state' => Enums::APPROVAL_STATUS_APPROVAL,
            ],
        ])->toArray();
        $approval_list = array_column($approval_list, 'approval_id');
        //把申请人塞进去,要给申请人也发
        $staff_list = array_values(array_unique(array_filter(array_merge($approval_list, [$submitter_id]))));
        //获取用户语言
        $staff_list_lang = (new StaffServer())->getBatchStaffLanguage($staff_list);
        //获取出发网点和目的地网点
        $store_list = SysStoreModel::find([
            'conditions' => 'id in ({store_ids:array})',
            'bind'       => ['store_ids' => [$fleet_info['start_store'], $fleet_info['end_store']]],
            'columns'    => "short_name,id",
        ])->toArray();
        $store_list = array_column($store_list, 'short_name', 'id');

        //期望到车时间
        $expected_date = !empty($fleet_info['expected_date']) ? date('Y-m-d H:i',
            strtotime($fleet_info['expected_date'])) : '';
        //驳回 %staff_name% 申请的  %start_store% - %via_store_short_name% - %end_store%  期望到车时间为 %expected_date% 的 %audit_type_title%  已驳回。如遇紧急情况急需车辆，请及时向上级反馈协调。
        //通过 %staff_name% 申请的 %start_store% - %via_store_short_name% - %end_store% 期望到车时间为 %expected_date% 的 %audit_type_title%  已通过。
        //发 push  和站内信  给 审批人
        $message_content  = $state == enums::APPROVAL_STATUS_REJECTED ? 'fleet_message_rejected' : 'fleet_message_approval';
        $PublicRepository = new PublicRepository();
        $log              = [];
        //申请类型翻译
        $audit_type_txt        = $this->getFleetAuditType();
        $audit_type_txt = array_column($audit_type_txt, 'type_txt_key', 'type');

        foreach ($staff_list as $staff_id) {
            $lang            = $staff_list_lang[$staff_id] ?? getCountryDefaultLang();
            //加班车申请类型
            $audit_type_title_key = $audit_type_txt[$fleet_info['audit_type']] ?? '';
            $audit_type_title = empty($audit_type_title_key) ? '' : $this->getTranslation($lang)->_($audit_type_title_key);

            $message_title   = $this->getTranslation($lang)->_('fleet_message_approval_title');
            $message_content = $this->getTranslation($lang)->_($message_content, [
                'staff_name'           => $submitter_info['name'] ?? '',
                'start_store'          => $store_list[$fleet_info['start_store']] ?? '',
                'via_store_short_name' => $fleet_info['via_store_short_name'] ?? '',
                'end_store'            => $store_list[$fleet_info['end_store']] ?? '',
                'expected_date'        => $expected_date,
                'audit_type_title'     => $audit_type_title ?? '',
            ]);
            //发送push
            $pushParam = [
                'staff_info_id'   => $staff_id,
                'message_title'   => $message_title,
                'message_content' => $message_content,
                'is_submitter'    => $staff_id == $submitter_id ? 1 : 2,
            ];
            $PublicRepository->pushMessageAndJumpToFinish($pushParam);
            //发送站内信
            $kit_param                       = [];
            $kit_param['staff_info_ids_str'] = $staff_id;
            $kit_param['staff_users']        = [0 => ['id' => $staff_id]];
            $kit_param['message_title']      = $message_title;
            $kit_param['message_content']    = addslashes("<div style='font-size: 40px'>" . $message_content. "</div>");;
            $kit_param['add_userid']         = 10000;
            $kit_param['category']           = -1; //普通消息
            $bi_rpc                          = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
            $bi_rpc->setParams($kit_param);
            $res   = $bi_rpc->execute();
            $log[] = ['result' => $res, 'staff_id' => $staff_id];
        }
        $this->logger->write_log('FleetServer-sendMassage by 加班车申请发送站内信 '.json_encode($log),'info');

        //手机号存在 并且在职 才发短信
        if (!empty($submitter_info['mobile']) && $submitter_info['state'] == HrStaffInfoModel::STATE_ON_JOB) {
            //发送短信
            // 通过 您申请的 %始发站code% - %途经地% - %目的地% 期望到车时间为 %期望到车时间% 的 %申请类型% 已通过。
            //驳回 您申请的 %始发站code% - %途经地% - %目的地% 期望到车时间为 %期望到车时间% 的 %申请类型% 已驳回。如遇紧急情况急需车辆，请及时向上级反馈协调。
            $phone_message_key = $state == enums::APPROVAL_STATUS_REJECTED ? 'fleet_phone_message_rejected' : 'fleet_phone_message_approval';
            $phone_lang        = $staff_list_lang[$submitter_id] ?? getCountryDefaultLang();
            $message           = $this->getTranslation($phone_lang)->_($phone_message_key, [
                'start_store'          => $store_list[$fleet_info['start_store']] ?? '',
                'via_store_short_name' => $fleet_info['via_store_short_name'] ?? '',
                'end_store'            => $store_list[$fleet_info['end_store']] ?? '',
                'expected_date'        => $expected_date,
                'audit_type_title'     => $audit_type_title ?? '',
            ]);
            $return            = curlJsonRpc($this->config->api->api_send_sms,
                curlJsonRpcStr($submitter_info['mobile'], $message, 'backyard_fleet_approval'));
            $this->logger->write_log("FleetServer-sendMassage by 加班车申请发送短信给申请人 message=> {$message}  ".json_encode([
                    'submitter_info' => $submitter_info,
                    'message_return' => $return,
                ]),'info');
        } else {
            $this->logger->write_log('FleetServer-sendMassage by 加班车申请没有发送短信给申请人  '.json_encode($submitter_info),'info');
        }
        return true;
    }

    /**
     * 详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     * @throws \Exception
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $params = [
            'fleet_id' => $auditId,
            'require_extend' => true,
        ];

        //获取车型详情
        $result  = $this->getFleetDetail($params);
        $carType = $this->getCarTypeList($result['car_type']);

        //申请人信息
        $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }

        $reason_type_list = array_column($this->getReasonType(),'type_txt','type');
        if ($result['reason_type'] == 99) {
            $show_reason = $reason_type_list[$result['reason_type']] . '-' . $result['reason'] ?? '';
        } else {
            $show_reason = $reason_type_list[$result['reason_type']];
        }

        //组织详情数据
        $detailLists = [
            'apply_parson'       => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? ''),
            'apply_department'   => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
            'car_type'           => $carType['type_txt'],
            'capacity'           => $result['capacity'] . $this->getTranslation()->_('piece') ?? '',
            'arrive_time'        => $result['expected_date'] ?? '',
            'region'             => $result['region'] ?? '',
            'reason_application' => $show_reason,
            'photo'              => $result['image_path'] ?? '',
            'app_type'           => $result['audit_type_title'] ?? '',
        ];
        if ($result['audit_type'] == enums::$fleet_audit_type['FD_courier']) { //如果是FD Courier类型的加班车申请，需要显示司机的工号
            $detailLists = array_merge($detailLists, [
                'start_store'       => $result['start_store_name'] ?? '',
                'end_store'         => $result['end_store_name'] ?? '',
                'fd_driver_id'      => $result['fd_courier_id'] ?? '',
            ]);
        } else {

            $storeNameArr = [$result['start_store_short_name'] ?? '', $result['via_store_short_name'] ?? '', $result['end_store_short_name'] ?? ''];
            $storeNameArr = array_filter($storeNameArr);
            $detailLists = array_merge($detailLists, [
                'temporary_route'    => implode(" - ", $storeNameArr)
            ]);
        }
        if ($result['status'] == enums::$audit_status['approved']) { //已经审批通过，需要显示车牌、公司
            $detailLists = array_merge(['company' => $result['company_name'] ?? '', 'plate_number' => $result['plate_number'] ?? ''], $detailLists, ['driver_name' => $result['driver'] ?? '', 'driver_phone' => $result['driver_phone'] ?? '']);
        }
        if ($result['status'] == enums::$audit_status['dismissed']) { //已经驳回，需要显示驳回原因
            $detailLists = array_merge($detailLists, [
                'reject_reason1' => $result['reject_type_text'] ?? '',
                'remark'        => $result['reject_reason'] ?? '',
            ]);
        }

        //获取当日包裹详情、班车详情
        $statisticsParameter = [
            'start_store' => $result['start_store'],
            'end_store'   => $result['end_store'],
            'serial_no'   => $result['serial_no'],
        ];
        $fleetStatisticsPackage = $this->getFleetStatisticsPackage($statisticsParameter);
        $fleetStatisticsShuttleBus = $this->getFleetStatisticsShuttleBus($statisticsParameter);

        $head = [
            'title'                => $this->auditlist->getAudityType(enums::$audit_type['VA']),
            'id'                   => $result['id'],
            'staff_id'             => $result['submitter_id'],
            'type'                 => enums::$audit_type['VA'],
            'created_at'           => $result['created_at'],
            'updated_at'           => $result['updated_at'],
            'status'               => $result['status'],
            'status_text'          => $this->auditlist->getAuditStatus('10' . $result['status']),
            'serial_no'            => $result['serial_no'] ?? '',
            'show_schedule_status' => $result['schedule_banner']['show_schedule_status'] ?? 0,
            'schedule_status_text' => $result['schedule_banner']['schedule_status_text'] ?? '',
        ];
        //调度信息
        if (in_array($result['status'], [enums::APPROVAL_STATUS_CANCEL, enums::APPROVAL_STATUS_REJECTED])) {
            $head['schedule_state']   = FleetAuditModel::CADENCE_STATUS_NORMAL;
        } else {
            $head['schedule_state']   = $result['cadence_status'] ?? FleetAuditModel::CADENCE_STATUS_PENDING;
        }
        $head['terminate_reason'] = $result['terminate_reason'] ?? "";

        $returnData['data']['head'] = $head;
        $returnData['data']['detail'] = $this->format($detailLists);
        $returnData['data']['extend'] = $result['extend'] ?? []; //时刻表
        $returnData['data']['cadence_detail'] = $result['cadence_detail'] ?? []; //调度信息
        $returnData['data']['statistics_package'] = $fleetStatisticsPackage['data'] ?? NULL;//当日包裹详情
        $returnData['data']['statistics_shuttle_bus'] = $fleetStatisticsShuttleBus['data'] ?? NULL;//当日班车详情
        $returnData['data']['ccd'] = $result['ccd'] ?? null;

        return $returnData;
    }

    /**
     * ph 作废
     * @description 获取当日班车详情
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function getTodayFleetDetail($params = []): array
    {
        $param = [
            'id'            => $params['id'],
            'serial_no'     => $params['serial_no'],
            'start_store'   => $params['start_store'],
            'end_store'     => $params['end_store'],
            'via_store_ids' => $params['via_store_ids'],
            'status'        => $params['status'] == enums::APPROVAL_STATUS_PENDING ? enums::$audit_status['panding_approval'] : $params['status'],
        ];
        $url = $this->config->api->java_http_url . "/svc/line/task/statistics/today";
        $res = $this->httpPost($url, $param);

        if ($res['code'] != ErrCode::SUCCESS) {
            return [];
        }
        foreach ($res['data'] as $k => $v) {
            if (empty($v['list'])) {
                continue;
            }

            $row = [];
            foreach ($v['list'] as $item) {
                $lineId = $item['line_id'];
                if (!isset($res['data'][$k]['list'][$lineId])) {
                    $row[$lineId]['line_name']       = $item['line_name'];
                    $row[$lineId]['car_pecification'] = $item['car_type_text'];
                }
                $row[$lineId]['othertable'][] = [
                    'outlets'              => $item['store_name'],
                    'state'                => $item['car_state_text'],
                    'estimated_time'       => !empty($item['estimate_start_time']) ? date("Y-m-d H:i:s",
                        $item['estimate_start_time']) : '',
                    'real_time'            => !empty($item['actual_start_time']) ? date("Y-m-d H:i:s",
                        $item['actual_start_time']) : '',
                    'expected_arrive_date' => !empty($item['expected_arrive_date']) ? date("Y-m-d H:i:s",
                        $item['expected_arrive_date']) : '',
                ];
            }
            $res['data'][$k]['list'] = array_values($row);
        }
        return $res['data'];
    }

    /**
     * ph 作废
     * @description 获取包裹数据
     * @param array $params
     * @return array
     */
    private function getTodayFleetParcelsDetail(array $params = [])
    {
        $param = [
            'id'            => $params['id'],
            'serial_no'     => $params['serial_no'],
            'start_store'   => $params['start_store'],
            'end_store'     => $params['end_store'],
            'via_store_ids' => $params['via_store_ids'],
            'status'        => enums::$audit_status['panding_approval'],
        ];
        $url = $this->config->api->java_http_url . "/svc/line/task/statistics/parcels";
        $res = $this->httpPost($url, $param);

        if ($res['code'] != ErrCode::SUCCESS) {
            return [];
        }

        return $res['data'];
    }
}
