<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\ApprovalFinderServer;
use FlashExpress\bi\App\Server\JobTransferValidateServer as BaseJobTransferValidateServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysDepartmentServer;

class JobTransferValidateServer extends BaseJobTransferValidateServer
{
    const DEFAULT_RULES = [
        'checkTransferStaffId',             //被转岗工号是否存在
        'checkTransferStaffOnJobState',     //被转岗人是否在职
        'checkTransferStaffAgent',          //被转岗人是否个人代理
        'checkTransferStaff',               //不能为自己转岗
        'checkInProcessTransfer',           //是否存在进行中的转岗
        'checkProbation',                   //试用期不能转岗
        'checkPermission',                  //转岗权限
    ];

    const CREATE_RULES = [
        'checkTransferStaffId',             //被转岗工号是否存在
        'checkTransferStaffOnJobState',     //被转岗人是否在职
        'checkTransferStaffAgent',          //被转岗人是否个人代理
        'checkTransferStaff',               //不能为自己转岗
        'checkInProcessTransfer',           //是否存在进行中的转岗
        'checkProbation',                   //试用期不能转岗
        'checkPermission',                  //转岗权限
        'checkHandoverStaffId',             //工作交接人工号是否存在
        'checkHandoverStaffOnJobState',     //工作交接人是否在职
        'checkHandoverStaffValid',          //工作交接人是否有效
        'checkAfterDate',                   //校验转岗日期
    ];

    const CHECK_RULES = [
        'checkTransferStaffId',             //被转岗工号是否存在
        'checkTransferStaffOnJobState',     //被转岗人是否在职
        'checkTransferStaffAgent',          //被转岗人是否个人代理
        'checkTransferStaff',               //不能为自己转岗
        'checkInProcessTransfer',           //是否存在进行中的转岗
        'checkPermission',                  //转岗权限
        'checkFrontLine',                   //是否一线职位
    ];

    //特殊转岗
    const SPECIAL_CHECK_RULES = [
        'checkTransferStaffId',             //被转岗工号是否存在
        'checkTransferStaffOnJobState',     //被转岗人是否在职
        'checkTransferStaffAgent',          //被转岗人是否个人代理
        'checkInProcessTransfer',           //是否存在进行中的转岗
        'checkSpecialPermission',           //转岗权限
        'checkFrontLine',                   //是否一线职位
        'checkAfterDate',                   //校验转岗日期
    ];

    /**
     * @description 校验工作交接人是否有效
     *
     * 有效的工作交接人
     *  转岗前是网点员工
     *      - 被转岗员工原网点员工
     *      - 被转岗人装岗前属于Network Management部门下，可填写被转岗员工原网点的片区负责人、大区负责人
     *      - 被转岗人装岗前属于Retail Management部门下，可填写被转岗员工原网点的大区负责人
     *      - 被转岗人装岗前属于Hub Management以及Flash Freight Hub部门下，可填写被转岗员工所属部门负责人
     *  转岗前是总部员工
     *      - 被转岗员工原所属部门内员工和原所属部门负责人
     *
     * @return int
     * @checkRule
     */
    protected function checkHandoverStaffValid(): int
    {
        $validHandoverStaff = [];
        //被转岗人信息
        $transferStaffInfo = $this->getJobTransferStaffInfo();
        $handoverStaffInfo = $this->getJobHandoverStaffInfo();

        if ($handoverStaffInfo['staff_info_id'] == $transferStaffInfo['staff_info_id']) {
            return ErrCode::JOB_TRANSFER_HANDOVER_STAFF_IS_TRANSFER_STAFF_DATE_ERROR;
        }

        //被转岗人所属部门信息
        $departmentInfo = (new SysDepartmentServer())->getDepartmentDetail($transferStaffInfo['node_department_id']);

        if ($transferStaffInfo['store_id'] == enums::HEAD_OFFICE_ID) {

            //同部门下的员工
            $isSameDepartment     = ApprovalFinderServer::getInstance()
                ->isSpecifiedStaffBelongDepartment($handoverStaffInfo['staff_info_id'], $transferStaffInfo['node_department_id']);
            $validHandoverStaff[] = $departmentInfo['manager_id'] ?? '';
            if (!$isSameDepartment && !in_array($handoverStaffInfo['staff_info_id'], $validHandoverStaff)) {
                return ErrCode::JOB_TRANSFER_HANDOVER_STAFF_NOT_VALID_ERROR;
            }
        } else {

            $departmentConfig = (new SettingEnvServer())->listByCode([
                'dept_network_management_id',
                'dept_shop_management_id',
                'dept_hub_management_id',
            ]);
            $departmentIdList = array_column($departmentConfig, 'set_val', 'code');

            //同网点下员工
            $validHandoverStaff = ApprovalFinderServer::getInstance()->findSpecStoreStaff($transferStaffInfo['store_id']);
            if ($transferStaffInfo['sys_department_id'] == $departmentIdList['dept_network_management_id']) {

                //片区负责人、大区负责人
                $validHandoverStaff[] = ApprovalFinderServer::getInstance()->getPieceManagerByStoreId($transferStaffInfo['store_id']);
                $validHandoverStaff[] = ApprovalFinderServer::getInstance()->getRegionManagerByStoreId($transferStaffInfo['store_id']);
            }

            if ($transferStaffInfo['sys_department_id'] == $departmentIdList['dept_shop_management_id']) {

                //大区负责人
                $validHandoverStaff[] = ApprovalFinderServer::getInstance()->getRegionManagerByStoreId($transferStaffInfo['store_id']);
            }

            if ($transferStaffInfo['sys_department_id'] == $departmentIdList['dept_hub_management_id']) {

                //所属部门负责人
                $validHandoverStaff[] = $departmentInfo['manager_id'] ?? '';
            }

            if (!in_array($handoverStaffInfo['staff_info_id'], $validHandoverStaff)) {
                return ErrCode::JOB_TRANSFER_HANDOVER_STAFF_NOT_VALID_ERROR;
            }
        }

        return ErrCode::SUCCESS;
    }

    /**
     * @description 获取员工信息
     * @param $staff_info_id
     * @return array
     */
    protected function getSpecifiedStaffInfo($staff_info_id): array
    {
        $staffInfo = (new StaffServer())->getStaffInfoSpecColumns($staff_info_id, $this->staff_info_columns);

        if (!empty($staffInfo) && $staffInfo['store_id'] == '-1') {
            $staffInfo['store_name'] = enums::HEAD_OFFICE;
        }
        return $staffInfo;
    }

    /**
     * @description 校验被转岗人在职状态
     * @return int
     * @checkRule
     */
    protected function checkTransferStaffAgent(): int
    {
        $staffInfo = $this->getJobTransferStaffInfo();
        if (!in_array($staffInfo['hire_type'], [HrStaffInfoModel::HIRE_TYPE_UN_PAID, HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT])) {
            return ErrCode::SUCCESS;
        }
        return ErrCode::JOB_TRANSFER_NOT_SUPPORT_AGENT;
    }
}