<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrStoreCarRentalModel;
use FlashExpress\bi\App\Models\backyard\HrStoreSubsidiesCategoryModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysAttachmentModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\JobtransferServer as BaseJobtransferServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysDepartmentServer;
use FlashExpress\bi\App\Server\WorkflowServer;

use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;


class JobtransferServer extends BaseJobtransferServer
{
    protected $bhubCategory;
    protected $bulkyCategory;
    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang,$timezone);
        //这里是hub 部门的可选网点类型
        $bhubCategory = (new SettingEnvServer())->getSetVal('jobtransfer_bhub_category');
        $this->bhubCategory     = empty($bhubCategory) ? (UC("jobtransfer")["bhub_category"] ?? "") : $bhubCategory;
        //这里是Network Bulky 的可选网点类型
        $bulkyCategory = (new SettingEnvServer())->getSetVal('jobtransfer_bulky_category');
        $this->bulkyCategory     =  empty($bulkyCategory) ? (UC("jobtransfer")["bulky_category"] ?? "") : $bulkyCategory;
    }

	
	/**
	 * 添加转岗信息
	 * @Access  public
	 * @Param   array
	 * @Return  array
	 */
	public function addJobtransfer($paramIn = [])
	{
		//[1]获取参数
		$staffId = $this->processingDefault($paramIn, 'staff_id', 2);
		$departmentId = $this->processingDefault($paramIn, 'after_department_id', 2);
		$storeId = $this->processingDefault($paramIn, 'after_store_id');
		$jobHandoverStaffId = $this->processingDefault($paramIn, 'job_handover_staff_id', 2);
		$jobTitle= $this->processingDefault($paramIn, 'after_position_id', 2);
		$hcId    = $this->processingDefault($paramIn, 'hc_id', 2);
		$type    = $this->processingDefault($paramIn, 'type');
		$userinfo = $this->processingDefault($paramIn, 'userinfo');
		$date    = $this->processingDefault($paramIn, 'after_date');
		$reason  = $this->processingDefault($paramIn, 'reason');
		$carOwner = $this->processingDefault($paramIn, 'car_owner', 2);
		$rentalCarCteatedAt = $this->processingDefault($paramIn, 'rental_car_cteated_at', 2);
		$source  = $this->processingDefault($paramIn, 'source', 'by');
        $after_working_day_rest_type = $this->processingDefault($paramIn, 'after_working_day_rest_type', 2);
		
		//[2]参数校验
		//[2-1]校验员工信息
		$staffInfo = $this->getStaffInfo([
			                                 "staff_id" => $staffId,
		                                 ]);
		if (!$staffInfo) {
			throw new BusinessException($this->getTranslation()->_('jobtransfer_0004'),enums::$ERROR_CODE['1000']);
		}
		//校验数据  会抛出异常  由外层解析
		$this->checkVerification($staffId,$jobHandoverStaffId,$staffInfo);
		
		//获取网点信息
		$afterStoreData = $this->jobtransfer->getStoreInfo([
			                                                   "store_id" => $storeId,
		                                                   ]);
		//获取当前直线上级id
		$currentManagerId = $this->getJobtransferStaffMangerId($staffId);
		//获取当前虚线上级id
		$currentIndirectMangerId = $this->getJobtransferStaffIndirectMangerId($staffId);
		//获取转岗后直线上级
		$afterManagerId = $this->getafterManagerIdInfo($storeId,$departmentId, $jobTitle, $date);
		//获取被转岗人的薪资信息
		$staffInfoSalary = $this->staff->getStaffSalary($staffId);
		$this->getDI()->get('logger')->write_log('current salary:' . json_encode($staffInfoSalary), 'info');
        //查询当前关联的HC
        $hcInfo = HrHcModel::findFirst([
            'conditions' => 'hc_id = :hc_id:',
            'columns'    => 'surplusnumber,demandnumber,expirationdate,hc_id,state_code',
            'bind'       => ['hc_id' => $hcId],
        ]);
		if (empty($hcInfo) || $hcInfo->state_code != HrHcModel::STATE_RECRUITING || $hcInfo->surplusnumber <= 0) {
			throw new BusinessException($this->getTranslation()->_('4414'), enums::$ERROR_CODE['1000']);
		}
		//添加添加车辆来源
		//车辆来源：1-个人车辆；2-租用公司车辆; 3-借用车辆；
		$vehicleInfo=VehicleInfoModel::findFirst(
			[
				'conditions' => 'uid = :uid: and approval_status=2',
				'bind' => ['uid' => $staffId]
			]
		);
		if($vehicleInfo){
			$vehicleInfo=$vehicleInfo->toArray();
		}
		$this->getDI()->get('logger')->write_log('by addJobtransfer:查询车辆信息 '.json_encode($vehicleInfo)  , 'info');

        $base_staff_info = $this->jobtransfer->getBaseStaffInfo($staffId);
        $before_working_day_rest_type = $base_staff_info['week_working_day'] . $base_staff_info['rest_type'];

		//添加转岗信息
		$param = [
			"staff_id"                   => $staffId ?? 0,
			"hc_id"                      => $hcId ?? "",
			"hc_expiration_date"         => $hcInfo->expirationdate,
			"type"                       => $type ?? 0,
			"current_department_id"      => $staffInfo["department_id"] ?? 0,
			"current_store_id"           => $staffInfo["hr_staff_sys_store_id"] ?? "",
			"current_position_id"        => $staffInfo["job_title"] ?? 0,
			"current_indirect_manger_id" => intval($currentIndirectMangerId) ?? 0,
			"current_manager_id"         => intval($currentManagerId) ?? 0,
			"current_role_id"            => $staffInfo['position_category'] ?? 0,
			"after_manager_id"           => intval($afterManagerId) ?? 0,
			"after_department_id"        => $departmentId ?? 0,
			"after_store_id"             => $storeId ?? "",
			"after_position_id"          => $jobTitle ?? 0,
			"after_date"                 => $date ?? "",
			"reason"                     => $reason ?? "",
			"job_transfer_id"            => null,
			"job_handover_staff_id"      => $jobHandoverStaffId ?? 0,
			"submitter_id"               => $userinfo['staff_id'] ?? 0,
			"approval_state"             => enums::APPROVAL_STATUS_PENDING,
			"car_owner"                  => $carOwner ?? null,
			"rental_car_cteated_at"      => !empty($rentalCarCteatedAt) ? $rentalCarCteatedAt: null,
			"state"                      => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED,
			"serial_no"                  => $this->getRandomId(),
			"before_base_salary"         => $staffInfoSalary['base_salary'] ?? 0,
			"before_exp_allowance"       => $staffInfoSalary['exp_allowance'] ?? 0,
			"before_position_allowance"  => $staffInfoSalary['position_allowance'] ?? 0,
			"before_car_rental"          => $staffInfoSalary['car_rental'] ?? 0,
			"before_trip_payment"        => $staffInfoSalary['trip_payment'] ?? 0,
			"before_notebook_rental"     => $staffInfoSalary['notebook_rental'] ?? 0,
			"before_recommended"         => $staffInfoSalary['recommended'] ?? 0,
			"before_food_allowance"      => $staffInfoSalary['food_allowance'] ?? 0,
			"before_dangerous_area"      => $staffInfoSalary['dangerous_area'] ?? 0,
			"before_house_rental"        => $staffInfoSalary['house_rental'] ?? 0,
			"vehicle_source"             => $vehicleInfo['vehicle_source'] ?? 0,
            "before_working_day_rest_type" => $before_working_day_rest_type,
            "after_working_day_rest_type" => $after_working_day_rest_type,
		];
		$db = $this->getDI()->get('db');
		$db->begin();
		try {
			//减hc
			(new HcServer($this->lang,$this->timezone))->updateHc([
				                                                      'id'            => $hcId,
				                                                      'surplusnumber' => $hcInfo->surplusnumber ?? 0,
				                                                      'demandnumber'  => $hcInfo->demandnumber ?? 0,
			                                                      ]);
			$this->getDI()->get('logger')->write_log("param ====:" . json_encode($param), 'info');
			$jobTransferId = $this->jobtransfer->addJobtransfer($param);
			if (empty($jobTransferId)) {
				throw new BusinessException($this->getTranslation()->_('jobtransfer_0004'));
			}
			$otherObj = new OtherRepository($this->timezone, $this->lang);
			$summary  = $this->genSummary($jobTransferId, $this->typeUnion);
			$insertAuditData[] = [
				'id_union'       => 'tf_' . $jobTransferId,
				'staff_id_union' => $userinfo["id"],
				'type_union'     => $this->typeUnion,
				'status_union'   => 101,
				'store_id'       => $userinfo["organization_type"] == 2 ? '' : $userinfo["organization_id"],
				'data'           => "",
				'table'          => 'job_transfer',
				'created_at'     => gmdate('Y-m-d H:i:s', time()),
				'origin_id'      => $jobTransferId,
				'summary'        => json_encode($summary, JSON_UNESCAPED_UNICODE),
				'approval_id'    => 0,
			];
			$otherObj->insterUnion($insertAuditData);
			//指定子审批流
			$flowCode = $this->getWorkflowCode([
				                                   'category' => $afterStoreData["category"],
				                                   'source'   => $source,
				                                   'hr_staff_sys_department_id' =>$staffInfo['hr_staff_sys_department_id'],// 一级部门
			                                   ]);
			$department = SysDepartmentModel::findFirst([
				                                            'conditions' => 'id = :id:',
				                                            'bind' => [
					                                            'id' => $departmentId
				                                            ]
			                                            ]);
			$firstDepartmentId = (new SettingEnvServer())->getSetVal('dept_hub_management_id');
			if ($department) {
				$department = $department->toArray();
				$Ids = explode('/', $department['ancestry_v3']);
				//查询一级部门
				$sys_department = SysDepartmentModel::findFirst([
					                                                'conditions' => 'id in ({Ids:array})  and level = 1 and deleted = 0',
					                                                'bind' => [
						                                                'Ids' => $Ids
					                                                ]
				                                                ]);
				
				$firstDepartmentId =$sys_department->id ?? $firstDepartmentId;
			}
			
			//添加转岗申请
			$ret = (new ApprovalServer($this->lang, $this->timezone))->create($jobTransferId, enums::$audit_type['TF'], $userinfo['staff_id'], null, [
				"AM_store_ids" => [$staffInfo["store_id"], $storeId],
				"DM_store_ids" => [$staffInfo["store_id"], $storeId],
				"Head_store_ids" => [$staffInfo["store_id"], $storeId],
				"store_id" => $staffInfo["store_id"],
				"flow_code"  => $flowCode ?? 1,
				"department_id" => $departmentId,
				"transfer_department_id" => $staffInfo["department_id"], //转岗人对应的hrbp
				"Array_department_id" => [$staffInfo["department_id"], $departmentId, $firstDepartmentId], //转岗前部门，转岗后部门，转岗后一级部门（由于只有hub用可写死25）
                "Array_store_id" => [$staffInfo["store_id"], $storeId], //转岗前网点，转岗后网点
			]);
			if ($ret === false) {
				$db->rollback();
			} else {
				$db->commit();
			}
		} catch (\Exception $e) {
			$db->rollback();
			$this->getDI()->get('logger')->write_log('addJobtransfer:' . $e->getMessage() . $e->getTraceAsString() , 'notice');
			return $this->checkReturn(-3, $e->getMessage());
		}
		return $this->checkReturn(["id" => $jobTransferId]);
	}
	
	
	/**
	 * 校验转岗申请
	 * @param array $paramIn
	 * @return array
	 * @throws \Exception
	 */
	public function checkJobTransferApply($paramIn = []): array
	{
		//[1]获取参数
		$submitterId = $this->processingDefault($paramIn, 'submitter_id', 2);
		$data = $this->processingDefault($paramIn, 'data', 3);
		$failArr = [];
		try {
			//[1]校验申请人权限
			$storeManagerInfo = SysStoreModel::find([
				                                        'conditions' => 'manager_id = :manager_id:',
				                                        'bind'       => ['manager_id' => $submitterId],
				                                        'column'     => 'id,manager_id'
			                                        ])->toArray();
			$regionMangerInfo = SysManageRegionModel::find([
				                                               'conditions' => 'manager_id = :manager_id:',
				                                               'bind'       => ['manager_id' => $submitterId],
				                                               'column'     => 'id,manager_id'
			                                               ])->toArray();
			$pieceMangerInfo = SysManagePieceModel::find([
				                                             'conditions' => 'manager_id = :manager_id:',
				                                             'bind'       => ['manager_id' => $submitterId],
				                                             'column'     => 'id,manager_id'
			                                             ])->toArray();
			$staffInfo = HrStaffInfoModel::findFirst([
				                                         'conditions' => 'staff_info_id = :staff_info_id:',
				                                         'bind'       => ['staff_info_id' => $submitterId],
				                                         'column'     => 'id,node_department_id'
			                                         ]);
			$submitterInfo = HrStaffInfoModel::findFirst([
				                                             'conditions' => 'staff_info_id = :id:',
				                                             'bind' => ['id' => $submitterId],
				                                             'column' => 'sys_department_id'
			                                             ]);
			$envModel = new SettingEnvServer();
			$hubManagementId = $envModel->getSetVal('dept_hub_management_id');
			$shopManagementId = $envModel->getSetVal('dept_shop_management_id');
			$networkManagementId = $envModel->getSetVal('dept_network_management_id');
			$networkPlanId = $envModel->getSetVal('dept_network_planning_id');
			//检查申请人权限
			//shop只有AM可以申请
			//hub只有网点负责人可以申请
			//如果既不是AM、DM、网点负责人、不在Network Planning部门，也不可以申请
			if (isset($submitterInfo->sys_department_id) && $submitterInfo->sys_department_id == $shopManagementId && !(isset($regionMangerInfo) && $regionMangerInfo) ||
			    isset($submitterInfo->sys_department_id) && $submitterInfo->sys_department_id == $networkManagementId  && !(isset($storeManagerInfo) && $storeManagerInfo) && !(isset($regionMangerInfo) && $regionMangerInfo) &&
			    !(isset($pieceMangerInfo) && $pieceMangerInfo) && isset($staffInfo->node_department_id) && $staffInfo->node_department_id != $networkPlanId
			) {
				$failArr[] = [
					'staff_id' => $submitterId,
					'column' => "",
					'reason' => $this->getTranslation()->_('jobtransfer_0004')
				];
			}
			//[2]校验申请数据
			if (!empty($data) && is_array($data)) {
				$t = $this->getTranslation();
				foreach ($data as $key => $value) {
					//[2-1]校验员工信息
					$staffInfo = $this->getStaffInfo([
						                                 "staff_id" => $value['staff_id'],
					                                 ]);
					if (!$staffInfo) {
						$failArr[] = [
							'staff_id' => $value['staff_id'],
							'column' => "staff_id",
							'reason' => $this->getTranslation()->_('1001')
						];
						continue;
					}
					//[2-2]校验交接员工信息
					//交接人需要在职、在编、非子账号
					//交接人不能是转岗员工自己、需要在职
					$jobHandoverStaffInfo = $this->getStaffInfo([
						                                            "staff_id" => $value['job_handover_staff_id'],
					                                            ]);
					//如果不是本网点的员工
					$jobHandoverStaffIds = [];
					if(!isset($jobHandoverStaffInfo["store_id"]) || $jobHandoverStaffInfo["store_id"] != $staffInfo["store_id"]){
						$jobHandoverStaffIds = $this->getJobHandoverStaffId($staffInfo);
					}
					if (!$jobHandoverStaffInfo || $jobHandoverStaffInfo["state"] != enums::$service_status['incumbency'] ||
					    $jobHandoverStaffInfo["formal"] != 1 || $jobHandoverStaffInfo["is_sub_staff"] != 0 ||
					    ($jobHandoverStaffInfo["store_id"] != $staffInfo["store_id"] && !in_array($value['job_handover_staff_id'],$jobHandoverStaffIds)  && $staffInfo['hr_staff_sys_store_id'] != '-1' ) ||
					    $value['job_handover_staff_id'] == $value['staff_id'])
					{
						$failArr[] = [
							'staff_id' => $value['staff_id'],
							'column' => "job_handover_staff_id",
							'reason' => $this->getTranslation()->_('jobtransfer_0020')
						];
						continue;
					}
					
					//如果是总部网点员工    不是同一个部门   并且交接人 不是部门负责人
					if($staffInfo['hr_staff_sys_store_id'] == '-1' && $jobHandoverStaffInfo['hr_staff_node_department_id'] != $staffInfo['hr_staff_node_department_id']){
						//查询是否为部门负责人
						$deptinfo = SysDepartmentModel::findFirst([
							                                          'conditions' => "manager_id = :staff_info_id: and id = :current_department_id:",
							                                          'bind' => [
								                                          'staff_info_id'  =>  $value['job_handover_staff_id'],
								                                          'current_department_id'       => $staffInfo['hr_staff_node_department_id']
							                                          ]
						                                          ]);
						if(empty($deptinfo)){
							//工作交接人错误，请填写您部门内的员工
							$failArr[] = [
								'staff_id' => $value['staff_id'],
								'column' => "job_handover_staff_id",
								'reason' => $this->getTranslation()->_('jobtransfer_0050')
							];
						}
						
					}
					
					//[2-3]校验是否重复添加
					$_re = $this->jobtransfer->getJobtransferInfo([
						                                              "staff_id" => $value['staff_id'],
						                                              "state"    => 1,
					                                              ]);
					if ($_re) {
						$failArr[] = [
							'staff_id' => $value['staff_id'],
							'column' => "staff_id",
							'reason' => $this->getTranslation()->_('5202')
						];
						continue;
					}
					$_re = $this->jobtransfer->getJobtransferInfo([
						                                              "staff_id" => $value['staff_id'],
					                                              ]);
					if (!empty($_re) && $_re['approval_state'] == enums::$audit_status['approved'] && $_re['state'] == enums::$job_transfer_state['to_be_transfered']) {
						$failArr[] = [
							'staff_id' => $value['staff_id'],
							'column' => "staff_id",
							'reason' => $this->getTranslation()->_('jobtransfer_0018')
						];
						continue;
					}
					//查询当前关联的HC
					$hcInfo = HrHcModel::findFirst([
						                               'conditions' => 'hc_id = :hc_id:',
						                               'columns' => 'surplusnumber,demandnumber,expirationdate,hc_id',
						                               'bind' => ['hc_id' => $value['hc_id']]
					                               ]);
					if (empty($hcInfo)) {
						$failArr[] = [
							'staff_id' => $value['staff_id'],
							'column' => "HcId",
							'reason' => $this->getTranslation()->_('jobtransfer_0024')
						];
						continue;
					}
				}
			}
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log("checkJobTransferApply:" . $e->getMessage() . $e->getTraceAsString(), 'notice');
		}
		return $failArr ?? [];
	}
	/**
	 * 创建
	 * @param array $paramIn
	 * @param $submitter_id
	 * @param $timezone
	 * @param $uuid
	 * @return array
	 * @throws \Exception
	 */
	public function create($paramIn = [], $submitter_id, $timezone, $uuid): array
	{
		//[1]获取参数
		$staffId = $this->processingDefault($paramIn, 'staff_id', 2);
		$departmentId = $this->processingDefault($paramIn, 'after_department_id', 2);
		$storeId = $this->processingDefault($paramIn, 'after_store_id');
		$jobHandoverStaffId = $this->processingDefault($paramIn, 'job_handover_staff_id', 2);
		$jobTitle= $this->processingDefault($paramIn, 'after_position_id', 2);
		$hcId    = $this->processingDefault($paramIn, 'hc_id', 2);
		$type    = $this->processingDefault($paramIn, 'type');
		$userinfo = $this->processingDefault($paramIn, 'userinfo');
		$date    = $this->processingDefault($paramIn, 'after_date');
		$reason  = $this->processingDefault($paramIn, 'reason');
		$carOwner = $this->processingDefault($paramIn, 'car_owner', 2);
		$rentalCarCteatedAt = $this->processingDefault($paramIn, 'rental_car_cteated_at', 2);
        $after_working_day_rest_type = $this->processingDefault($paramIn, 'after_working_day_rest_type', 2);
		
		//[2]参数校验
		//[2-1]校验员工信息
		$staffInfo = $this->getStaffInfo([
			                                 "staff_id" => $staffId,
		                                 ]);
		if (!$staffInfo) {
			throw new \Exception($this->getTranslation()->_('jobtransfer_0004'),enums::$ERROR_CODE['1000']);
		}
		//校验数据  会抛出异常  由外层解析
		$this->checkVerification($staffId,$jobHandoverStaffId,$staffInfo);
		
		
		//获取网点信息
		$afterStoreData = $this->jobtransfer->getStoreInfo([
			                                                   "store_id" => $storeId,
		                                                   ]);
		//获取当前直线上级id
		$currentManagerId = $this->getJobtransferStaffMangerId($staffId);
		//获取当前虚线上级id
		$currentIndirectMangerId = $this->getJobtransferStaffIndirectMangerId($staffId);

		$afterManagerId = $this->getafterManagerIdInfo($storeId,$departmentId);
		//获取被转岗人的薪资信息
		$staffInfoSalary = $this->staff->getStaffSalary($staffId);
		$this->getDI()->get('logger')->write_log('current salary:' . json_encode($staffInfoSalary), 'info');
		//查询当前关联的HC
		$hcInfo = HrHcModel::findFirst([
			                               'conditions' => 'hc_id = :hc_id:',
			                               'columns' => 'surplusnumber,demandnumber,expirationdate,hc_id',
			                               'bind' => ['hc_id' => $hcId]
		                               ]);
		if (empty($hcInfo)) {
			throw new \Exception($this->getTranslation()->_('4414'), enums::$ERROR_CODE['1000']);
		}
		//如果所选hc已经等于0了就不能再减少了
		$hcInfo = HrHcModel::findFirst([
			                               'conditions' => 'hc_id = :hc_id: and surplusnumber > 0',
			                               'columns' => 'surplusnumber,demandnumber,expirationdate',
			                               'bind' => ['hc_id' => $hcId]
		                               ]);
		if (!isset($hcInfo->surplusnumber) || isset($hcInfo->surplusnumber) && $hcInfo->surplusnumber == 0) {
			throw new \Exception($this->getTranslation()->_('job_transfer_err_2'), enums::$ERROR_CODE['1000']);
		}
		//添加添加车辆来源
		//车辆来源：1-个人车辆；2-租用公司车辆; 3-借用车辆；
		$vehicleInfo=VehicleInfoModel::findFirst(
			[
				'conditions' => 'uid = :uid: and approval_status=2',
				'bind' => ['uid' => $staffId]
			]
		);
		if($vehicleInfo){
			$vehicleInfo=$vehicleInfo->toArray();
		}
		$this->getDI()->get('logger')->write_log('oa addJobtransfer:查询车辆信息 '.json_encode($vehicleInfo)  , 'info');

        $base_staff_info = $this->jobtransfer->getBaseStaffInfo($staffId);
        $before_working_day_rest_type = $base_staff_info['week_working_day'] . $base_staff_info['rest_type'];

		//添加转岗信息
		$param = [
			"staff_id"                   => $staffId ?? 0,
			"hc_id"                      => $hcId ?? "",
			"hc_expiration_date"         => $hcInfo->expirationdate,
			"type"                       => $type ?? 0,
			"current_department_id"      => $staffInfo["department_id"] ?? 0,
			"current_store_id"           => $staffInfo["hr_staff_sys_store_id"] ?? "",
			"current_position_id"        => $staffInfo["job_title"] ?? 0,
			"current_indirect_manger_id" => intval($currentIndirectMangerId) ?? 0,
			"current_manager_id"         => intval($currentManagerId) ?? 0,
			"current_role_id"            => $staffInfo['position_category'] ?? 0,
			"after_manager_id"           => intval($afterManagerId) ?? 0,
			"after_department_id"        => $departmentId ?? 0,
			"after_store_id"             => $storeId ?? "",
			"after_position_id"          => $jobTitle ?? 0,
			"after_date"                 => $date ?? "",
			"reason"                     => $reason ?? "",
			"job_transfer_id"            => null,
			"job_handover_staff_id"      => $jobHandoverStaffId ?? 0,
			"submitter_id"               => $userinfo['staff_id'] ?? 0,
			"approval_state"             => enums::APPROVAL_STATUS_PENDING,
			"car_owner"                  => $carOwner ?? null,
			"rental_car_cteated_at"      => !empty($rentalCarCteatedAt) ? $rentalCarCteatedAt: null,
			"state"                      => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED,
			"serial_no"                  => $this->getRandomId(),
			"before_base_salary"         => $staffInfoSalary['base_salary'] ?? 0,
			"before_exp_allowance"       => $staffInfoSalary['exp_allowance'] ?? 0,
			"before_position_allowance"  => $staffInfoSalary['position_allowance'] ?? 0,
			"before_car_rental"          => $staffInfoSalary['car_rental'] ?? 0,
			"before_notebook_rental"     => $staffInfoSalary['notebook_rental'] ?? 0,
			"before_recommended"         => $staffInfoSalary['recommended'] ?? 0,
			"before_food_allowance"      => $staffInfoSalary['food_allowance'] ?? 0,
			"before_dangerous_area"      => $staffInfoSalary['dangerous_area'] ?? 0,
			"before_house_rental"        => $staffInfoSalary['house_rental'] ?? 0,
			"before_island_allowance"    => $staffInfoSalary['island_allowance'] ?? 0,
			"before_gasoline_allowance"  => $staffInfoSalary['gasoline_allowance'] ?? 0,
			"after_base_salary"          => $staffInfoSalary['base_salary'] ?? 0,
			"after_exp_allowance"        => $staffInfoSalary['exp_allowance'] ?? 0,
			"after_position_allowance"   => $staffInfoSalary['position_allowance'] ?? 0,
			"after_car_rental"           => $staffInfoSalary['car_rental'] ?? 0,
			"after_notebook_rental"      => $staffInfoSalary['notebook_rental'] ?? 0,
			"after_recommended"          => $staffInfoSalary['recommended'] ?? 0,
			"after_food_allowance"       => $staffInfoSalary['food_allowance'] ?? 0,
			"after_dangerous_area"       => $staffInfoSalary['dangerous_area'] ?? 0,
			"after_house_rental"         => $staffInfoSalary['house_rental'] ?? 0,
			"after_island_allowance"     => $staffInfoSalary['island_allowance'] ?? 0,
			"after_gasoline_allowance"   => $staffInfoSalary['gasoline_allowance'] ?? 0,
			"data_source"                => 2,
			"vehicle_source"             => $vehicleInfo['vehicle_source'] ?? 0,
            "after_working_day_rest_type" => $after_working_day_rest_type,
            "before_working_day_rest_type" => $before_working_day_rest_type,
		];
		$db = $this->getDI()->get('db');
		$db->begin();
		try {
			//减hc
			(new HcServer($this->lang,$this->timezone))->updateHc([
				                                                      'id'            => $hcId,
				                                                      'surplusnumber' => $hcInfo->surplusnumber ?? 0,
				                                                      'demandnumber'  => $hcInfo->demandnumber ?? 0,
			                                                      ]);
			$this->getDI()->get('logger')->write_log("param ====:" . json_encode($param), 'info');
			$jobTransferId = $this->jobtransfer->addJobtransfer($param);
			if (empty($jobTransferId)) {
				throw new \Exception($this->getTranslation()->_('jobtransfer_0004'));
			}
			//指定子审批流
			$flowCode = $this->getWorkflowCode([
				                                   'category' => $afterStoreData["category"],
				                                   'source'   => 2,
				                                   'hr_staff_sys_department_id' =>$staffInfo['hr_staff_sys_department_id'],// 一级部门
			                                   ]);
//            $shopCategoryArr    = explode(",", $this->shopCategory);
//            $hubCategoryArr     = explode(",", $this->hubCategory);
//            $bhubCategoryArr     = explode(",", $this->bhubCategory);
//            $bulkyCategoryArr     = explode(",", $this->bulkyCategory);
//
//            if (in_array($afterStoreData["category"], $hubCategoryArr)) {
//                $afterDepartmentId = (new SettingEnvServer())->getSetVal('dept_hub_management_id');
//            } elseif (in_array($afterStoreData["category"], $shopCategoryArr)) {
//                $afterDepartmentId = (new SettingEnvServer())->getSetVal('dept_shop_management_id');
//            } elseif (in_array($afterStoreData["category"], $bhubCategoryArr)) {
//                $afterDepartmentId = (new SettingEnvServer())->getSetVal('flash_freight_hub_management_id');
//            } elseif (in_array($afterStoreData["category"], $bulkyCategoryArr)) {
//                $afterDepartmentId = (new SettingEnvServer())->getSetVal('dept_network_bulky_id');
//            }else {
//                $afterDepartmentId = (new SettingEnvServer())->getSetVal('dept_network_management_id');
//            }
			//获取一级部门
            $afterDepartmentId = (new SysDepartmentServer())->searchSysDeptId($departmentId);

			
			//添加转岗申请
			$ret = (new ApprovalServer($this->lang, $this->timezone))->create($jobTransferId, enums::$audit_type['TFOA'], $userinfo['staff_id'], null, [
				"Head_store_ids" => [$staffInfo["store_id"], $storeId],
				"store_id" => $staffInfo["store_id"],
				"flow_code"  => $flowCode ?? 1,
				"department_id" => $staffInfo["department_id"],
				"transfer_department_id" => $staffInfo["department_id"], //转岗人对应的hrbp
				"Array_department_id" => [$staffInfo["department_id"], $departmentId, $afterDepartmentId], //转岗前部门，转岗后部门，转岗后一级部门
                "Array_store_id" => [$staffInfo["store_id"], $storeId], //转岗前网点，转岗后网点
			]);
			if ($ret === false) {
				$db->rollback();
			} else {
				$db->commit();
			}
		} catch (\Exception $e) {
			$db->rollback();
			$this->getDI()->get('logger')->write_log('addJobtransfer:' . $e->getMessage() . $e->getTraceAsString() , 'notice');
			return $this->checkReturn(-3, $e->getMessage());
		}
		return $this->checkReturn(["id" => $jobTransferId]);
	}
	/**
	 * 获取子审批流
	 * @param array $paramIn
	 * @return int
	 */
	private function getWorkflowCode($paramIn = [])
	{
		$category = $paramIn['category'];
		$source = $paramIn['source'];
		$hr_staff_sys_department_id = $paramIn['hr_staff_sys_department_id'] ?? '-1';// 部门
		$shopCategoryArr    = explode(",", $this->shopCategory);
		$hubCategoryArr     = explode(",", $this->hubCategory);
		$networkCategoryArr = explode(",", $this->networkCategory);
		$bhubCategoryArr = explode(",", $this->bhubCategory);
		$bulkyCategoryArr     = explode(",", $this->bulkyCategory);
		//获取部门
		$firstDepartmentId = (new SettingEnvServer())->getSetVal('dept_hub_management_id');
		//如果是 hub 部门
		if($hr_staff_sys_department_id == $firstDepartmentId){
			$flowCode = $source == 2 ? 8 : 8;
		} else if (in_array($category, $hubCategoryArr)) {
			$flowCode = $source == 2 ? 6 : 3;
		} elseif (in_array($category, $networkCategoryArr)) {
			$flowCode = $source == 2 ? 4 : 1;
		} elseif (in_array($category, $shopCategoryArr)) {
			$flowCode = $source == 2 ? 5 : 2;
		} elseif(in_array($category, $bhubCategoryArr)){
			$flowCode = $source == 2 ? 7 : 4;
		} elseif(in_array($category, $bulkyCategoryArr)){
			$flowCode = $source == 2 ? 4 : 1;
		}
		return $flowCode;
	}

	/**
	 * @description:获取转岗后的直线上级
	 * @param $after_store_id
	 * @param $after_job_title
	 * @param $after_demp_id
	 * @param $afterDate
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/7/8 14:45
	 */
	public function getafterManagerIdInfo($after_store_id,$after_demp_id=0, $after_job_title = 0, $afterDate = '', $transfer_staff_info_id = 0)
	{
		$afterManagerId = 0;
		try {
			// 2. 转岗后是总部员工
			//    1. 当转岗后部门有部门负责人时，转岗员工转至新部门后直线上级默认取新部门负责人
			//    2. 当转岗后部门没有负责人时，转岗员工转至新网点后直线上级取上级部门负责人
			if($after_store_id == '-1'){
				//查找部门负责人
				$departmentInfo        = SysDepartmentModel::findFirst($after_demp_id);
				if($departmentInfo) {
					if ($departmentInfo->manager_id) {
						$afterManagerId = $departmentInfo->manager_id;
					}
					if (empty($afterManagerId)) {
						//找上级部门
						$departmentInfo        = SysDepartmentModel::findFirst($departmentInfo->ancestry);
						$afterManagerId = $departmentInfo->manager_id ?? $afterManagerId;
					}
				}
			}else {
                $afterJobTitleConfig = (new SettingEnvServer())->getSetVal('after_transfer_job_title_config',',');

                //如果 DC Supervisor【16】 职位
                if(in_array($after_job_title, $afterJobTitleConfig)) {
                    $afterManagerId = $this->getOrgManager($after_demp_id, $after_store_id, $afterDate);
                } else {
                    //获取网点信息
                    $afterStoreData = $this->jobtransfer->getStoreInfo([
                        "store_id" => $after_store_id,
                    ]);

                    //获取转岗后直线上级
                    $afterManagerId = $this->getJobtransferStoreManager([
                        "category" => $afterStoreData["category"],
                        "store_id" => $after_store_id,
                    ]);

                    if($this->checkManager($afterManagerId, $afterDate)) {
                        return $afterManagerId;
                    }
                    $afterOrgManagerId = $this->getOrgManager($after_demp_id, $after_store_id, $afterDate);
                    $afterManagerId = empty($afterOrgManagerId) ? $afterManagerId : $afterOrgManagerId;
                }
			}
		} catch (\Exception $e) {
			if ($e->getCode() == enums::$ERROR_CODE['1000']) {
				$this->getDI()->get('logger')->write_log('getafterManagerIdInfo:' . $e->getMessage() . $e->getTraceAsString(), 'error');
			}else{
				$this->getDI()->get('logger')->write_log('getafterManagerIdInfo:' . $e->getMessage() . $e->getTraceAsString());
			}
            return $afterManagerId;
		}
		
		return $afterManagerId;
	}
	
	
	
	
	
	/**
     * 转岗-详细
     * @Access  public
     * @Param   array
     * @Return  bool
     */
    public function getJobtransferInfo($paramIn = [])
    {
        $id   = $this->processingDefault($paramIn, 'id', 2);
        $data = $this->jobtransfer->getJobtransferInfo([
            "id" => $id,
        ]);
        if (!$data) {
            return [];
        }
        $sysObj = new SysListRepository();
        //查询部门名称 ids
        $departmentIdsArr                = array_unique(array_merge([$data["current_department_id"]], [$data["after_department_id"]]));
        $departmentData                  = $sysObj->getDepartmentList([
            "ids" => $departmentIdsArr,
        ]);
        $departmentData                  = array_column($departmentData, 'name', 'id');
        $data["current_department_name"] = $departmentData[$data["current_department_id"]] ?? "";
        $data["after_department_name"]   = $departmentData[$data["after_department_id"]] ?? "";

        //查询hc
        $hcInfo = HrHcModel::findFirst([
            'conditions' => "hc_id = :hc_id:",
            'bind' => [
                'hc_id'  => $data['hc_id'],
            ]
        ])->toArray();

        $positionList = [$data["current_position_id"], $data["after_position_id"], $hcInfo['job_title']];
        $positionList = array_filter($positionList);

        //查询职位名称 ids
        $positionIdsArr                = array_unique($positionList);
        $positionData                  = $sysObj->getPositionList([
            "ids" => $positionIdsArr,
        ]);
        $positionData                  = array_column($positionData, 'name', 'id');
        $data["current_position_name"] = $positionData[$data["current_position_id"]] ?? "";
        $data["after_position_name"]   = $positionData[$data["after_position_id"]] ?? "";

        //查询网点名称 ids
        $storeIdsArr                = array_unique(array_merge([$data["current_store_id"]], [$data["after_store_id"]]));
        $storeIds                   = getIdsStr($storeIdsArr);
        $storeData                  = $sysObj->getStoreList([
            "ids" => $storeIds,
        ]);
        $storeData                  = array_column($storeData, 'name', 'id');
        $data["current_store_name"] = $storeData[$data["current_store_id"]] ?? "";
        $data["after_store_name"]   = $storeData[$data["after_store_id"]] ?? "";

        //查询员工名称 ids
        $staffIdsArr                          = array_unique(array_merge([$data["staff_id"]], [$data["current_manager_id"]], [$data["current_indirect_manger_id"]], [$data["after_manager_id"]], [$data["job_handover_staff_id"]]));
        $staffIds                             = getIdsStr($staffIdsArr);
        $staffDataList                        = $sysObj->getStaffList([
            "ids" => $staffIds,
        ]);
        $staffData                            = array_column($staffDataList, 'name', 'id');

        //查询转岗后的角色
        $roles = [];
        $roleTranslation = $this->getRolesTranslation();
        if (isset($data['after_role_ids']) && $data['after_role_ids']) {
            $roleInfo = RolesModel::find([
                'columns' => "id,name,name_th,name_en",
                'conditions' => "id in ({role_id:array}) and status = 1",
                'bind' => [
                    'role_id'  => explode(',', $data['after_role_ids']),
                ]
            ])->toArray();
            foreach ($roleInfo as $role) {
                $roleName = $this->lang == 'zh-CN' ? ($roleTranslation[$role['id']]['role_name_zh'] ?? "")
                    : ($this->lang == 'en' ? $roleTranslation[$role['id']]['role_name_en'] ?? "" : $roleTranslation[$role['id']]['role_name_th'] ?? '');
                $roles[] = $roleName;
                $rolesName[] = ['label' => $roleName, 'value' => $role['id']];
            }
            $data["after_role_name"]              = $roles ? implode(',', $roles) : "";
            $data['after_role_name_arr']          = $rolesName ?? [];
        }

        //查询转岗前的角色
        if (isset($data['current_role_id']) && $data['current_role_id']) {
            $roleInfo = RolesModel::find([
                'columns' => "id,name,name_th,name_en",
                'conditions' => "id in ({role_id:array}) and status = 1",
                'bind' => [
                    'role_id'  => explode(',', $data['current_role_id']),
                ]
            ])->toArray();
            foreach ($roleInfo as $role) {
                $roleName = $this->lang == 'zh-CN' ? ($roleTranslation[$role['id']]['role_name_zh'] ?? "")
                    : ($this->lang == 'en' ? $roleTranslation[$role['id']]['role_name_en'] ?? "" : $roleTranslation[$role['id']]['role_name_th'] ?? '');
                $rolesName[] = ['label' => $roleName, 'value' => $role['id']];
            }
            $data['current_role_name_arr']    = $rolesName ?? [];
        }

        //车辆类型
        $carType = $this->getCarOwnerList();
        $carTypeName = array_column($carType, 'key','value');

        //获取被转岗人入职日期
        $staffHireDataList                    = array_column($staffDataList, 'hire_date', 'id');

        $data["staff_name"]                   = $staffData[$data["staff_id"]] ?? "";
        $data["current_manager_name"]         = $staffData[$data["current_manager_id"]] ?? "";
        $data["current_indirect_manger_name"] = $staffData[$data["current_indirect_manger_id"]] ?? "";
        $data["after_manager_name"]           = $staffData[$data["after_manager_id"]] ?? "";
        $data["job_handover_staff_name"]      = $staffData[$data["job_handover_staff_id"]] ?? "";
        $data["hc_job_title"]                 = $hcInfo['job_title'] ?? "";
        $data["hc_job_title_name"]            = $positionData[$hcInfo['job_title']] ?? "";
        $data['car_owner_label']              = $carTypeName[$data['car_owner'] ?? 0] ?? '';
        $data['rental_car_cteated_at']        = $data['rental_car_cteated_at'] ?? '';
        $data['staff_hire_date']              = $staffHireDataList[$data['staff_id']] ?? '';
        $data['before_base_salary']           = $data['before_base_salary'] / 100;

        $data["after_salary"] = [
            enums::$audit_detail_btns['input_base_salary'] => $data['after_base_salary'] / 100, //基本工资
            enums::$audit_detail_btns['input_performance_allowance'] => $data['after_performance_allowance'] / 100, //绩效
            enums::$audit_detail_btns['input_car_rental'] => $data['after_car_rental'] / 100, //租车津贴
            enums::$audit_detail_btns['input_trip_payment'] => $data['after_trip_payment'] / 100, //油费补贴
            enums::$audit_detail_btns['input_notebook_rental'] => $data['after_notebook_rental'] / 100, //电脑补贴
            enums::$audit_detail_btns['input_food_allowance'] => $data['after_food_allowance'] / 100, //餐补
            enums::$audit_detail_btns['input_dangerous_area'] => $data['after_dangerous_area'] / 100, //危险区域

            enums::$audit_detail_btns['input_house_rental'] => $data['after_house_rental'] / 100, //租房补贴
            enums::$audit_detail_btns['input_island_allowance'] => $data['after_island_allowance'] / 100, //海岛补贴

            enums::$audit_detail_btns['input_deminimis_benefits'] => $data['after_deminimis_benefits'] / 100, //无税金补贴
            enums::$audit_detail_btns['input_other_non_taxable_allowance'] => $data['after_other_non_taxable_allowance'] / 100, //其他无税金补贴
            enums::$audit_detail_btns['input_other_taxable_allowance'] => $data['after_other_taxable_allowance'] / 100, //其他纳税津贴
            enums::$audit_detail_btns['input_phone_subsidy'] => $data['after_phone_subsidy'] / 100, //话费补贴
        ];

        return $data;
    }

    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom): array
    {
        //获取转岗详情
        $result  = $this->getJobtransferInfo(['id' => $auditId]);
        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }

        //组织详情数据-通用详情
        $detailLists = [
            'apply_parson'     => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? ''),
            'apply_department' => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
            'jobtransfer_0031' => sprintf('%s ( %s )', $result['staff_name'], $result['staff_id']),
            'jobtransfer_0010' => $result['current_department_name'],
            'jobtransfer_0012' => $result['current_position_name'],
            'jobtransfer_0007' => $result['current_store_name'],
            'before_working_day_rest_type' => !empty($result['before_working_day_rest_type']) ? $this->getTranslation()->_('working_day_rest_type_'.$result['before_working_day_rest_type']) : '',
            'jobtransfer_0014' => $result['after_department_name'],
            'jobtransfer_0015' => $result['after_position_name'],
            'jobtransfer_0008' => $result['after_store_name'],
            'jobtransfer_0033' => $result['hc_id'] ?? '',
            'after_working_day_rest_type' => !empty($result['after_working_day_rest_type']) ? $this->getTranslation()->_('working_day_rest_type_'.$result['after_working_day_rest_type']) : '',
            'jobtransfer_0016' => $result['after_date'],
            'work_handover'    => sprintf('%s ( %s )', $result['job_handover_staff_name'], $result['job_handover_staff_id']),
            'jobtransfer_0017' => $result['reason'],
        ];
        if (!empty($result['after_role_ids'] )) {
            $detailLists = array_merge($detailLists, [
                'jobtransfer_0035' => $result['after_role_name'] ?? '',
            ]);
        }
        
        //转岗后职位为Van Courier、Bike Courier、Tricycle Courier Truck Courier 时显示车辆来源
        $vehicleJobTitle = explode(',',(new SettingEnvServer())->getSetVal('job_title_vehicle_type'));

        if (in_array($result['after_position_id'], $vehicleJobTitle)) {

            if ($result['car_owner'] == 1) {
                $detailLists = array_merge($detailLists, [
                    'jobtransfer_0036' => $result['car_owner_label'] ?? '',
                ]);
            } else {
                $detailLists = array_merge($detailLists, [
                    'jobtransfer_0036' => $result['car_owner_label'] ?? '',
                    'start_time'       => $result['rental_car_cteated_at'] ?? '',
                ]);
            }
        }
        //高阶审批人才可以看到的详情
        if (!empty($result['senior_auditor']) && in_array($user, explode(',', $result['senior_auditor']))) {
            $detailLists = array_merge($detailLists, [
                'jobtransfer_0038' => $result['before_base_salary'] ?? '',
                'jobtransfer_0037' => $this->decodeRejectSalaryDetail($result['reject_salary_detail']),
                'photo'            => $result['image_path'] ?? [],
            ]);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        //获取当前审批人的角色
        $roleInfo = HrStaffInfoPositionModel::findFirst([
            'conditions' => "staff_info_id = :staff_id: and position_category = 68",
            'bind' => [
                'staff_id'  => $user,
            ],
        ]);

        $data = [
            'title'      => $this->auditlist->getAudityType($this->typeUnion),
            'id'         => $result['id'],
            'staff_id'   => $result['submitter_id'],
            'type'       => $this->typeUnion,
            'created_at' => $result['created_at'],
            'updated_at' => $result['updated_at'],
            'status'     => $result['state'],
            'hc_begin_date' => date("Y-m-d", strtotime($result['created_at']) + 1 * 86400),
            'hc_end_date' => date("Y-m-d", strtotime($result['hc_expiration_date'])),
            'status_text'=> $this->auditlist->getAuditStatus('10' . $result['state']),
            'serial_no'  => $result['serial_no'] ?? '',
            'after_job_title' => $result['after_position_id'],
            'after_department_id' => $result['after_department_id'],
            'staff_hire_date' => $result['staff_hire_date'],
            'is_hrbp'   => $roleInfo ? 1 : 0, //0-无hrbp角色 1-有hrbp角色
            'show_vehicle_job_title' =>  $vehicleJobTitle,
            'after_working_day_rest_type' => $result['after_working_day_rest_type'],
            'after_working_day_rest_type_text' => !empty($result['after_working_day_rest_type']) ? $this->getTranslation()->_('working_day_rest_type_'.$result['after_working_day_rest_type']) : '',
        ];

        if ($comeFrom == 2) { //审批人可以审批相应的薪资信息
            //获取当前审批人的审批状态
            $appInfo = AuditApprovalModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and approval_id = :approval_id: and deleted = 0",
                'bind' => [
                    'type'  => enums::$audit_type['TF'],
                    'value' => $auditId,
                    'approval_id' => $user
                ],
                'order' => 'id desc'
            ]);
            $state  = isset($appInfo) && $appInfo ? $appInfo->getState() : 0;

            $auditApply = AuditApplyModel::findFirst([
                'conditions' => ' biz_value = :value: and biz_type = :type: ',
                'bind' => [
                    'value' => $auditId,
                    'type' => enums::$audit_type['TF']
                ]
            ]);
            $afterApproval = 0;
            if ($auditApply) {
                $auditApply = $auditApply->toArray();
                $node = WorkflowNodeModel::findFirst([
                    'conditions' => ' flow_id = :flow_id: and auditor_type = :type: and find_in_set(:auditor_id:, auditor_id) ',
                    'bind' => [
                        'flow_id' => $auditApply['flow_id'],
                        'type' => enums::WF_NODE_HRBP,
                        'auditor_id' => $user
                    ]
                ]);
                if ($node && $result['approval_state'] == enums::APPROVAL_STATUS_PENDING) {
                    $afterApproval = 3;
                }
            }

            $option = ['beforeApproval' => "1,2", 'afterApproval' => $afterApproval];

            //在审批时,审批人的操作
            $options = $this->getStaffOptions(['option' => $option, 'status' => $state]);

            //存在待审批
            //已经填写过薪资的审批人、或者是hrbp
            if (isset($state) && $state == enums::APPROVAL_STATUS_PENDING && ($result['after_base_salary'] != 0 || !empty($roleInfo))) {
                $tmpOption = [];
                foreach ($result['after_salary'] as $k => $v) {
                    $tmpOption['code' . $k] = intval($v);
                }

                if (!empty($roleInfo)) { //hrbp可以修改图片、角色、
                    //如果转岗前与转岗后职位相同，并且是第一次填写 要代入转岗前的角色
                    $roles = $result['after_position_id'] == $result['current_position_id'] ? ($result['current_role_name_arr'] ?? []) : [];
                    $optionData = array_merge((array)$options ?? [], $tmpOption ?? [], [
                        //转岗后职位
                        'code19' => isset($result['after_role_ids']) && $result['after_role_ids']
                            ? ($result['after_role_name_arr'] ?? [])
                            : $roles,
                        //图片
                        'code20' => $result['image_path'] ?? "",
                        //转岗日期
                        'code21' => isset($result['after_date']) && $result['after_date']
                            ? date('Y-m-d', strtotime($result['after_date']))
                            : '',
                    ]);

                    if (isset($result['car_owner']) && $result['car_owner'] == 2) { //借用公司车辆
                        $optionData = array_merge($optionData, [
                            'code22' => isset($result['car_owner']) && $result['car_owner']
                                ? ['label' => $result['car_owner_label'], 'value' => $result['car_owner']]
                                : '',
                            'code23' => date("Y-m-d", strtotime($result['rental_car_cteated_at']))
                        ]);
                    } else {
                        $optionData = array_merge($optionData, [
                            'code22' => isset($result['car_owner']) && $result['car_owner']
                                ? ['label' => $result['car_owner_label'], 'value' => $result['car_owner']]
                                : '',
                        ]);
                    }


                } else {
                    $optionData =  array_merge($tmpOption ?? [], (array)$options ?? []);
                }
            } else {
                $optionData =  array_merge((array)$options ?? []);
            }
        } else { //获取申请的审批状态
            $option = ['beforeApproval' => '0', 'afterApproval' => '0'];
            $state = 1;

            //在审批时,审批人的操作
            $optionData = $this->getStaffOptions(['option' => $option, 'status' => $state]);
        }

        $data['options'] = $optionData;
        $returnData['data']['head']   = $data;
        return $returnData;
    }


    /**
     * @param $detailInfo
     * @param $params
     * @return false|string
     */
    public function encodeRejectSalaryDetail($detailInfo,$params)
    {
        $this->getDI()->get('logger')->write_log("encodeRejectSalaryDetail信息: detailInfo:" .json_encode($detailInfo,JSON_UNESCAPED_UNICODE).' params'.json_encode($params,JSON_UNESCAPED_UNICODE) ,'info');

        $salaryFields = [
            'base_salary',//基本工资
            'position_allowance',//职位津贴
            'exp_allowance',//经验津贴
            'food_allowance',//餐补
            'dangerous_area',//危险区域津贴
            'notebook_rental',//电脑补贴
            'house_rental', //租房津贴
            'car_rental',//租车津贴
            'trip_payment',//油补
            'gasoline_allowance',//销售油补
            'recommended',//推荐补贴
            'island_allowance',//海岛补贴
            'performance_allowance',//绩效补贴
            'deminimis_benefits',//无税补贴
            'other_non_taxable_allowance',//其他无税金补贴
            'other_taxable_allowance',//其他纳税津贴
            'phone_subsidy',//通话补贴
        ];

        $salaryFieldsChanged = [];
        foreach ($salaryFields as $v1){
            $old = ($detailInfo['after_'.$v1]/100);
            $new = ($params['after_'.$v1]/100);
            if ($old != $new)
                $salaryFieldsChanged['job_transfer.'.$v1] =  $old. ' => ' . $new;
        }
        return json_encode($salaryFieldsChanged);
    }

    /**
     * 获取转岗后上级
     * @param $afterDepartId
     * @param $afterStoreId
     * @param $afterDate
     * @return int|string
     */
    public function getOrgManager($afterDepartId, $afterStoreId, $afterDate)
    {
        $model          = new FleSysDepartmentModel();
        $networkDeptIds = $model->getNetworkDepartmentIds();
        $hubDeptIds     = $model->getHubDepartmentIds();

        //1. NW一级部门及其子部门下：对应转岗后组织架构上网点对应的DM，没有DM或者DM离职，取AM，没有AM取所属部门负责人
        //一直没有取到一级部门负责人
        if (in_array($afterDepartId, $networkDeptIds)) {
            $manager = (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getOrganizationRegionPieceManagerId($afterStoreId);
            //DM
            if (!empty($manager['piece_manager_id']) && $this->checkManager($manager['piece_manager_id'], $afterDate)) {
                return $manager['piece_manager_id'];
            }
            //AM
            if (!empty($manager['region_manager_id']) && $this->checkManager($manager['region_manager_id'], $afterDate)) {
                return $manager['region_manager_id'];
            }
            //查找 转岗后的 所属部门负责人
            $deptMangerId = $this->getDepartmentManger($afterDepartId);
            if ($this->checkManager($deptMangerId, $afterDate)) {
                return $deptMangerId;
            }

            //一级部门
            $firstDepartmentId = (new SysDepartmentServer())->searchSysDeptId($afterDepartId);
            // 找 所属部门 -》 所属部门的一级部门 区间内的 部门负责人
            $deptMangerId = $this->getGraduallyDeptManager($afterDepartId, $firstDepartmentId, $afterDate);
            if($deptMangerId) {
                return $deptMangerId;
            }

            //一级部门负责人不用验证 是否 待离职
            $deptMangerId = $this->getDepartmentManger($firstDepartmentId);
            return $deptMangerId;
        }

        //HUB
        if (in_array($afterDepartId, $hubDeptIds)) {
            //查找 转岗后的 所属部门负责人
            $deptMangerId = $this->getDepartmentManger($afterDepartId);
            if ($this->checkManager($deptMangerId, $afterDate)) {
                return $deptMangerId;
            }
            //一级部门
            $firstDepartmentId = (new SysDepartmentServer())->searchSysDeptId($afterDepartId);
            // 找 所属部门 -》 所属部门的一级部门 区间内的 部门负责人
            $deptMangerId = $this->getGraduallyDeptManager($afterDepartId, $firstDepartmentId, $afterDate);
            if($deptMangerId) {
                return $deptMangerId;
            }

            //一级部门负责人不用验证 是否 待离职
            $deptMangerId = $this->getDepartmentManger($firstDepartmentId);
            return $deptMangerId;
        }

        return 0;
    }

    /**
     * 转岗获取网点下拉列表》》》》组织架构上部门关联的网点
     * @param $departmentId
     * @return array
     */
    public function getStoreListByDepartmentId($departmentId)
    {
        $returnData['data'] = [];
        if (empty($departmentId)) {
            return $returnData;
        }
        $departmentIds = (new SysDepartmentModel())->getSpecifiedDeptAndSubDept($departmentId);

        $storeIds = (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getDepartmentStoreRelationInfo($departmentIds);

        $model          = new FleSysDepartmentModel();
        $hubDeptIds     = $model->getHubDepartmentIds();

        if (!empty($storeIds)) {
            //获取网点信息
            $data       = SysStoreModel::find([
                'conditions' => "id in ({store_ids:array})",
                'bind'       => [
                    'store_ids' => $storeIds,
                ],
                'columns'    => 'id, name',
            ])->toArray();

            $returnData['data'] = $data;
        }

        if (in_array($departmentId, $hubDeptIds)) {
            $returnData['data'][] = [
                'id' => enums::HEAD_OFFICE_ID,
                'name' => enums::HEAD_OFFICE
            ];
        }

        return $returnData;
    }
}