<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\AuditDetailOperationsEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\OsStaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditOptionRule;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\OsStaffServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysDepartmentServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use Exception;

class OsStaffServer extends GlobalBaseServer
{
    protected $disableShiftIds = [];

    public $is_mobile_dc = false;

    /**
     * 添加外协员工申请
     * @param array $paramIn    传入参数
     * @return mixed
     * @throws Exception
     */
    public function addOsStaff($paramIn = [])
    {
        //[1]参数定义
        $staffId    = $this->processingDefault($paramIn,'staff_id');
        $jobId      = $this->processingDefault($paramIn,'job_id');
        $employDate = $this->processingDefault($paramIn,'employment_date');
        $employDays = $this->processingDefault($paramIn, 'employment_days');
        $shiftId    = $this->processingDefault($paramIn,'shift_id');
        $demendNum  = $this->processingDefault($paramIn,'demend_num');
        $reason     = $this->processingDefault($paramIn,'reason');
        $reason     = addcslashes(stripslashes($reason),"'");
        $submit_store_id = $this->processingDefault($paramIn, 'store_id');
        $reasonType = $this->processingDefault($paramIn,'reason_type', 2);
        $imagePathArr  = $this->processingDefault($paramIn,'image_path');
        $osType     = $this->processingDefault($paramIn,'os_type', 2);
        $store_id   = $this->processingDefault($paramIn,'organization_id');

        $hire_os_type   = $this->processingDefault($paramIn,'hire_os_type',2,11);
        $out_company_id  = $this->processingDefault($paramIn, 'company_id', 2);

        $need_remark      = $this->processingDefault($paramIn, 'need_remark');
        $need_remark      = addcslashes(stripslashes($need_remark), "'");
        $shift_begin_time = $this->processingDefault($paramIn, 'shift_begin_time');
        $shift_duration   = $this->processingDefault($paramIn, 'shift_duration', 2);

        $staffInfo = $this->staff->checkoutStaff($staffId);

        //由于operation部门在ms中存的是子部门,
        //并且hr中job_title与部门的关联关系是job_title与一级部门的关联而不是子部门关联
        //该位置必须查询申请人在bi系统中的部门
        $staff_info = $this->staff->getStaffInfoById($staffId);
        if ($staffInfo) {
            $department_id = $osType == enums::$os_staff_type['motorcade'] ? 32: $staffInfo['department_id'];
        } else {
            $department_id = null;
        }

        if (empty($department_id)) {
            throw new ValidationException($this->getTranslation()->_('err_msg_do_not_have_department'));
        }
        //如果是车队外协需调整申请的网点
        $store_id = $osType == enums::$os_staff_type['motorcade'] ? $submit_store_id: $store_id;

        $shift_end_time = '';
        //hub 外协 申请订单。
        $hubOsJobTitles = (new SettingEnvServer())->getSetVal('hub_os_select_roles',',');
        if (in_array($jobId, $hubOsJobTitles)) {
            $employDateShiftStart = strtotime($employDate.' '.$shift_begin_time);
            // 不可选择距离当前时间小于1小时开始的班次
            if (time() > ($employDateShiftStart - 60 * 60)) {
                throw new ValidationException($this->getTranslation()->_('shift_error'));
            }
            $shiftId = 0;
            $shift_duration_minute = $shift_duration * 60;
            $shift_end_time = date('H:i', strtotime(date('Y-m-d').$shift_begin_time) + $shift_duration_minute * 60);
            $departmentInfo = $this->getLeave2DepartmentInfoByStoreId($submit_store_id);
            $department_id = $departmentInfo->id ?? null;
            $store_id = $submit_store_id;
        } else {
            //非车队外协需要验证
            //部门下是否存在该职位
            if ($osType != enums::$os_staff_type['motorcade']) {
                $relate = $this->os->getRelation($department_id, $jobId);

                if (empty($relate)) {
                    throw new ValidationException($this->getTranslation()->_('err_msg_job_title_department_dismatch'));
                }
            }

            $shift_info = $this->getShiftDetail($shiftId);
            if(!empty($shift_info)) {
                $shift_begin_time = $shift_info['start'];
                $shift_end_time = $shift_info['end'];
            }
        }



        //[3]组织数据插入业务主数据
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $insertData = [
                'serial_no'         => 'OS' . $this->getRandomId(),
                'os_type'           => $osType,
                'staff_id'          => $staffId,
                'job_id'            => intval($jobId),
                'department_id'     => $department_id,
                'store_id'          => $store_id,
                'employment_date'   => $employDate,
                'employment_days'   => $employDays,
                'shift_id'          => $shiftId,
                'status'            => enums::$audit_status['panding'],
                'demend_num'        => $demendNum,
                'final_audit_num'   => $demendNum,
                'reason_type'       => $reasonType,
                'reason'            => $reason,
                'wf_role'           => 'os_new',
                'hire_os_type'      => $hire_os_type,
                'out_company_id'    => !empty($out_company_id) ? $out_company_id : 0,
                'need_remark'       => $need_remark,
                'shift_begin_time'  => $shift_begin_time,
                'shift_end_time'    => $shift_end_time,
            ];
            $osStaffId = $this->os->insertOsStaff($insertData);
            if (empty($osStaffId)) {
                throw new Exception($this->getTranslation()->_('4008'));

            }
            //插入业务关联图片
            if(!empty($imagePathArr)) {
                $insertImgData  = [];

                foreach($imagePathArr as $image) {
                    $insertImgData[] = [
                        'id'         => $osStaffId,
                        'image_path' => $image,
                    ];
                }
                $this->pub->batchInsertImgs($insertImgData, 'OUTSOURCING_STAFF');
            }
            //HRBP根据申请的网点查找
            $extend['store_id'] = $store_id;

            //这里是 from 表单的内容,用于查找审批人
            $extend['from_submit'] = [
                'sys_store_id' => $store_id,
                'audit_type'   => AuditListEnums::APPROVAL_TYPE_OS,
            ];

            //创建
            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($osStaffId, AuditListEnums::APPROVAL_TYPE_OS, $staffId,null, $extend);
            if (!$requestId) {
                throw new \Exception('创建审批流失败');
            }
            $db->commit();
        } catch (ValidationException $vException){
	        $db->rollback();
            throw $vException;
        }  catch (\Exception $e){
            $db->rollback();
            throw $e;
        }

        return $this->checkReturn([]);
    }

    /**
     * 获取审批流角色
     * 详细见 doc/OS_workflow.md
     * @param $paramIn
     * @return string
     */
    public function getOsStaffWorkflowRole($paramIn)
    {
        //[1]获取参数
        $staffId    = $this->processingDefault($paramIn,'staff_id');
        $jobId      = $this->processingDefault($paramIn,'job_id');
        $osType     = $this->processingDefault($paramIn,'os_type', 2);
        $departmentId = $this->processingDefault($paramIn,'department_id', 2);

        //[2]获取员工详情
        $staffInfo = $this->staff->getStaffPositionv3($staffId);

        //[3]找到合适的审批流
        if ($osType == enums::$os_staff_type['normal']) { //短期外协
            if($jobId == enums::$job_title['hub_operator']) { //HUB STAFF职位 改成 hub_operator https://l8bx01gcjr.feishu.cn/docs/doccnGcRAXzbLJiuE3JvrDsEONf#
                //申请人 -> 上级 -> 30286
                $flowCode = 1;
            } else if (in_array($jobId, [enums::$job_title['bike_courier'],
                enums::$job_title['van_courier'],
                enums::$job_title['boat_courier'],
                enums::$job_title['warehouse_staff_sorter']] //bike courier、van courier、boat courier、warehouse staff(sorter)职位
            )) {
                if ($departmentId == enums::$department['Fulfillment'] && $jobId == enums::$job_title['warehouse_staff_sorter']) {
                    //申请人→网点负责人→fulfillment部门负责人
                    $flowCode = 6;
                } else {
                    //申请人 -> 上级 -> 申请人所在网点对应的大区经理
                    $flowCode = 2;
                }
            } else {
                $flowCode = 2;
            }
        } else if ($osType == enums::$os_staff_type['long_term']) { //长期外协
            //申请人-> 上级 -> 所属区域的HRBP终审
            $flowCode = 7;
        } else { //车队外协申请
            //申请人-> 51290
            $flowCode = 5;
        }

        //onsite_officer、shop_officer的长期、短期外协申请审批流是一样的
        if ($jobId == enums::$job_title['onsite_officer'] && in_array($staffInfo['category'], [enums::$stores_category['os'],enums::$stores_category['hub']])) {
            //申请人 -> 申请人所在网点负责人 -> 21715
            $flowCode = 4;
        } else if ($jobId == enums::$job_title['shop_officer'] && in_array($staffInfo['category'], [enums::$stores_category['shop_pickup_only'],enums::$stores_category['shop_ushop']])) {
            //申请人 -> 网点负责人 -> 20467 (Shop Operation Manager) ->17574
            $flowCode = 3;
        }

        return $flowCode;
    }


    /**
     * 获取职位列表
     * @param array $paramIn
     * @return array
     */
    public function getOsJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $osType     = $this->processingDefault($paramIn, 'type');
        $staffInfo  = $this->processingDefault($paramIn, 'userinfo');
        //[2]组织列表
        //1)短期外协职位下拉列表[全部职位]
        //2)长期外协职位下拉列表[Hub Staff|Onsite Officer|Shop Officer]
        //3)车队外协下拉列表[Van Courier]
        switch ($osType) {
            case enums::$os_staff_type['normal']: //短期外协
                $returnArr = $this->getShortTermJobTitleList(['staff_id' => $staffInfo['id']]);
                break;
            case enums::$os_staff_type['long_term']: //长期外协
                $returnArr = $this->getLongTermJobTitleList(['staff_id' => $staffInfo['id']]);
                break;
            case enums::$os_staff_type['motorcade']: //车队外协
                $returnArr = [
                    [
                        'job_id'        => enums::$job_title['van_courier'],
                        'job_title'     => 'Van Courier',
                    ],
                ];
                break;
            default:
                $returnArr = [];
                break;
        }

        //hub 外协 申请订单。取可以申请的职位
        if($osType == enums::$os_staff_type['normal'] && $this->checkIsHubPermission($staffInfo['id'])) {
            $returnArr        = [];
            $hubOsSelectRoles = (new SettingEnvServer())->getSetVal('hub_os_select_roles', ',');
            $jobs             = HrJobTitleModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $hubOsSelectRoles],
                'columns'    => 'id,job_name',
            ])->toArray();
            foreach ($jobs as $v) {
                $returnArr[] = [
                    'job_id'    => $v['id'],
                    'job_title' => $v['job_name'],
                ];
            }
            return $returnArr;
        }


        return $returnArr;
    }

    /**
     * 获取外协员工职位列表
     * @param array $paramIn
     * @return array
     */
    public function getShortTermJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_id');

        //[2]获取申请人所在部门
        $staffInfo = $this->staff->getStaffInfoById($staffId);

        //昕哲需求 ： https://shimo.im/docs/93vWtkjkXWc6tWxG/read
        //短期外协职位列表
        //1)Network Management、Network Operations部门,职位列表：[Van Courier、Bike Courier、Warehouse Staff (Sorter)]；
        //如果有岛屿还需增加[Boat Courier]
        //2)Shop Management 职位列表
        //2.1) shop_pickup_only、shop_ushop类型网点 [Shop Officer、Warehouse Staff (Sorter)]
        //2.2) OS类型网点 [Onsite Officer、Warehouse Staff (Sorter)]
        //2.3) 其他网点 [Warehouse Staff (Sorter)]
        //3)Fulfillment  [Warehouse Staff (Sorter)]
        //3)Hub Management  [Warehouse Staff (Sorter)、Hub Staff]


        //$department = SettingEnvModel::find("code = 'dept_network_operations_id'");
        $department = SettingEnvModel::find([
            'conditions' => 'code in ({code:array})',
            'bind'       => [
                'code' => [
                    'dept_network_operations_id',
                    'dept_hub_management_id',
                    'dept_fulfillment_id',
                    'dept_flash_philippines_network',
                ],
            ],
        ])->toArray();

        $department = array_column($department, 'set_val', 'code');
        switch ($staffInfo['sys_department_id']) {
            case $department['dept_flash_philippines_network']:
            case $department['dept_network_operations_id']:
                $returnArr = [
                    [
                        'job_id'    => enums::$job_title['van_courier'],
                        'job_title' => 'Van Courier',
                    ],
                    [
                        'job_id'    => enums::$job_title['bike_courier'],
                        'job_title' => 'Bike Courier',
                    ],
                    [
                        'job_id'    => enums::$job_title['dc_officer'],
                        'job_title' => 'DC Officer',
                    ],
                    [
                        'job_id'    => enums::$job_title['boat_courier'],
                        'job_title' => 'Boat Courier',
                    ],
                    [
                        'job_id'    => enums::$job_title['tricycle_courier'],
                        'job_title' => 'Tricycle Courier',
                    ],
                    [
                        'job_id'    => enums::$job_title['truck_courier'],
                        'job_title' => 'Truck Driver',
                    ],
                    [
                        'job_id'    => enums::$job_title['mobile_dc'],
                        'job_title' => 'Mobile DC',
                    ],
                ];
                break;
            case $department['dept_hub_management_id']:
//                if ($staffInfo['category'] == enums::$stores_category['os']) {
//                    $returnArr = [
//                        [
//                            'job_id'    => enums::$job_title['onsite_officer'],
//                            'job_title' => 'Onsite Officer',
//                        ],
//                    ];
//                }
                if ($staffInfo['category'] == enums::$stores_category['hub']) {
                    $returnArr = [
                        [
                            'job_id'    => enums::$job_title['hub_operator'],
                            'job_title' => 'Hub Operator',
                        ],
                    ];
                }
                break;
            case $department['dept_fulfillment_id']:
                $returnArr = [
                    [
                        'job_id'    => enums::$job_title['warehouse_staff_sorter'],
                        'job_title' => 'Warehouse Staff (Sorter)',
                    ],
                ];
                break;
            default:
                $returnArr = [];
                break;
        }
        if (!empty($staffInfo['node_department_id'])){
            $sysDepartmentServer = (new SysDepartmentServer());
            $departmentIds = array_merge(
                $sysDepartmentServer->getDepartmentIdsUseSettingEnv('dept_fulfillment_id'),
                $sysDepartmentServer->getDepartmentIdsUseSettingEnv('dept_flash_material_warehouse')
            );
            if (in_array($staffInfo['node_department_id'], $departmentIds)){
                $relate = (new OsStaffRepository($this->timezone))->getRelation($staffInfo['node_department_id'], enums::$job_title['warehouse_staff']);
                if (!empty($relate)) {
                    $returnArr = array_merge($returnArr ?? [],[
                        [
                            'job_id'    => enums::$job_title['warehouse_staff'],
                            'job_title' => 'Warehouse Staff',
                        ],
                    ]);
                }
            }
        }

        return $returnArr;
    }

    /**
     * 获取有效的外协员工列表
     * @param array $paramIn
     * @return array
     */
    public function getLongTermJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_id');

        //[2]获取申请人所在部门
        $staff_info = $this->staff->getStaffInfoById($staffId);

        //申请列表
        //2)OS类型网点可以申请Onsite Officer
        //3)HUB类型可以申请Hub Staff
        $returnArr = [];
//        if($staff_info['category'] == enums::$stores_category['os']) {
//            $returnArr     = [
//                [
//                    'job_id'        => enums::$job_title['onsite_officer'],
//                    'job_title'     => 'Onsite Officer',
//                ]
//            ];
//        }
        if($staff_info['category'] == enums::$stores_category['hub'] || $staff_info['category'] == enums::$stores_category['os']) {
            $returnArr     = [
                [
                    'job_id'        => enums::$job_title['hub_operator'],
                    'job_title'     => 'Hub Operator',
                ],
            ];
        }
        if (!empty($staff_info['node_department_id'])){
            $sysDepartmentServer = (new SysDepartmentServer());
            $departmentIds = array_merge(
                $sysDepartmentServer->getDepartmentIdsUseSettingEnv('dept_fulfillment_id'),
                $sysDepartmentServer->getDepartmentIdsUseSettingEnv('dept_flash_material_warehouse')
            );
            if (in_array($staff_info['node_department_id'], $departmentIds)){
                $relate = (new OsStaffRepository($this->timezone))->getRelation($staff_info['node_department_id'], enums::$job_title['warehouse_staff']);
                if (!empty($relate)) {
                    $returnArr = array_merge($returnArr,[
                        [
                            'job_id'    => enums::$job_title['warehouse_staff'],
                            'job_title' => 'Warehouse Staff',
                        ],
                    ]);
                }
            }
        }
        return $returnArr;
    }

    /**
     * 验证是否走 HUB 外协
     * @param $staff_id
     * @return bool
     * @throws BusinessException
     */
    protected function checkIsHubPermission($staff_id)
    {
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('OS_staff_apply_permission')
            ->setParameters(['staff_info_id' => $staff_id])
            ->getConfig();
        $this->logger->write_log("OS_staff_apply_permission  {$staff_id} " . json_encode($res), 'info');
        return $res['response_data'] ?? false;
    }


    /**
     * 获取请求列表
     * @throws BusinessException
     */
    public function getRequestList($paramIn = [])
    {
        //[1]参数定义
        $staffInfo = $this->processingDefault($paramIn, 'userinfo');
        $jobId     = $this->processingDefault($paramIn, 'job_id', 2);
        $os_type     = $this->processingDefault($paramIn, 'os_type', 2);
        $select_store_id     = $this->processingDefault($paramIn, 'store_id', 1);
        $storeId   = $staffInfo['organization_id'] ?? '';
        $storeName = $staffInfo['organization_name'] ?? '';
        $type      = $staffInfo['organization_type'] ?? '';
        $jobTitle  = $staffInfo['job_title'] ?? '';
        $positions = $staffInfo['positions'] ?? [];
        $employment_date = !empty($paramIn['employment_date']) ? $paramIn['employment_date'] : '';

        //可以申请车队外协申请的角色
        $motorcadeRequestRole = UC('outsourcingStaff')['osMotorcadeRequestRole'];

        //[3]长期外协申请条件
        // 1-HUB部门的网点经理、区域经理
        // 2-申请人是特殊网点（OS_CLD-Chilindo、OS_LAS-KA）的正主管
        // 3-OS、UShop、Shop(pickup only)

        //规则配置 OS_staff_apply_permission 是否有长期外协选项权限
        $hub_permission = $this->checkIsHubPermission($staffInfo['id']);
        //os_mode已经废弃， os_type.code 对应了雇佣模式
        //os_type.code控制雇佣日期范围
        if ($hub_permission) {
            $ret['os_mode'] = enums::$os_staff_type['long_term'];//废弃字段
            $ret['os_type'] = [
                ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
                ["code" => 2, "title" => $this->getTranslation()->_('os_request_mode_2')],
            ];
        } else {
            if ($type == 2 && $storeId == enums::$department['Transportation'] && array_intersect($positions,
                    array_keys($motorcadeRequestRole))) { //车队外协
                $ret['os_mode'] = enums::$os_staff_type['motorcade'];
                $ret['os_type'] = [
                    ["code" => 3, "title" => $this->getTranslation()->_('os_request_mode_3')],
                ];
            } else { //短期外协
                $ret['os_mode'] = enums::$os_staff_type['normal'];
                $ret['os_type'] = [
                    ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
                ];
            }
        }
        $ret['store_list'] = [];

        //hub 外协工单，分拨经理 有入口权限
        $is_out_company   = false;
        $out_company_list = [];
        $hubOsJobTitles    = (new SettingEnvServer())->getSetVal('hub_os_select_roles', ',');

        if (($hub_permission && $os_type ==  enums::$os_staff_type['normal']) || in_array($jobId, $hubOsJobTitles)) {
            if (empty($select_store_id)) {
                $select_store_id = $storeId;
            }
            if ($departmentInfo = $this->getLeave2DepartmentInfoByStoreId($select_store_id)) {
                $department = $departmentInfo->name;
            }
            $storeInfo  = (new SysStoreServer($this->timezone))->getStoreName([$select_store_id]);
            $storeName  = $storeInfo[$select_store_id] ?? '';
            $storeId    = $select_store_id;
            $is_out_company   = true;
            $out_company_list = $this->getOutCompanyInfo();
            $this->setDisableShift([]);
            $ret['store_list'] = $this->getHubOsStoreList();
        } else {
            $department = (new DepartmentRepository())->getDepartmentNameById($staffInfo['department_id'] ?? '');
        }

        $os_half_shift_position    = (new SettingEnvServer())->getSetVal('os_half_shift_position', ',');
        $ret['store_id']         = $storeId;
        $ret['store_name']       = $ret['os_mode'] == enums::$os_staff_type['motorcade'] ? '' : $storeName;
        $ret['department']       = $department??'';
        $ret['shift_info_half']  = [];
        $ret['shift_info']       = $this->getShiftList($jobId,HrShiftModel::SHIFT_GROUP_FULL_DAY_SHIFT,$employment_date);
        if (in_array($jobId,$os_half_shift_position)){
            $ret['shift_info_half']  = $this->getShiftList($jobId,HrShiftModel::SHIFT_GROUP_HALF_DAY_SHIFT,$employment_date);
        }
        $ret['reason']           = $this->getOsStaffReqReason($storeId);
        $ret['is_out_company']   = $is_out_company;
        $ret['out_company_list'] = $out_company_list;
        $ret['shift_duration']   = $this->getShiftDuration();
        
        $ret['os_half_shift_position']   = $os_half_shift_position;
        return $ret;
    }

    /**
     * 根据部门关联网点id获取二级部门名称
     * @param $store_id
     * @return string
     */
    public function getLeave2DepartmentInfoByStoreId($store_id)
    {
        $departmentModel = SysdepartmentModel::findFirst([
            'conditions' => 'relevance_store_id = :relevance_store_id:',
            'bind'       => [
                'relevance_store_id' => $store_id,
            ],
        ]);
        if (empty($departmentModel)) {
            return null;
        }
        $level2DepartmentModel = SysdepartmentModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => [
                'id' => $departmentModel->ancestry,
            ],
        ]);
        if (empty($level2DepartmentModel)) {
            return null;
        }
        return $level2DepartmentModel;
    }

    public function getHubOsStoreList()
    {
        return SysStoreModel::find([
            'conditions' => 'use_state = :use_state: and category in ({category:array})',
            'bind'       => [
                'category'  => [enums::$stores_category['hub'], enums::$stores_category['bhub']],
                'use_state' => SysStoreModel::USE_STATE_YES,
            ],
            'columns'    => 'id as store_id,name as store_name',
        ])->toArray();
    }


    /**
     * 获取外协员工详情
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getOsStaffDetail($paramIn = []): array
    {
        //[1]参数定义
        $osStaff_id = $this->processingDefault($paramIn, 'id', 2);

        //[2]请求详情
        $returnData = $this->os->getOsStaffInfo($osStaff_id);
        if (empty($returnData)) {
            return [];
        }
        //查找网点
        $storeNames = (new SysStoreServer)->getStoreName([$returnData['store_id']]);
        $reasons    = array_column($this->getOsStaffReqReason($returnData['store_id']), 'title', 'code');

        $returnData['store_name'] = $storeNames[$returnData['store_id']] ?? '';
        $returnData['reason']     = $reasons[$returnData['reason']] ?? "";

        $returnData['shift_name'] = $returnData['shift_begin_time'] . ' - ' . $returnData['shift_end_time'];
        if (!empty($returnData['shift_id'])) {
            //查找班次
            $shiftInfo = $this->os->getWorkShift($returnData['shift_id']);
            if (!empty($shiftInfo)) {
                if ($shiftInfo['type'] == 'EARLY') {
                    $shift = $this->getTranslation()->_('shift_early');
                } else {
                    if ($shiftInfo['type'] == 'MIDDLE') {
                        $shift = $this->getTranslation()->_('shift_middle');
                    } else {
                        if ($shiftInfo['type'] == 'NIGHT') {
                            $shift = $this->getTranslation()->_('shift_night');
                        } else {
                            $shift = $this->getTranslation()->_('shift_early');
                        }
                    }
                }
                $returnData['work_shift'] = $shift . ' ' . $shiftInfo['start'] . ' - ' . $shiftInfo['end'];
                $returnData['shift_name'] = $shiftInfo['start'] . ' - ' . $shiftInfo['end'];
            }
        } else {
            $returnData['work_shift'] = $returnData['shift_begin_time'] . ' - ' . $returnData['shift_end_time'];
        }

        //非车队外协需要查看网点的参考数据
        if ($returnData['status'] == 1 && $returnData['os_type'] != enums::$os_staff_type['motorcade'] && empty($returnData['out_company_id']) && empty($returnData['out_company_data'])) {
            $server               = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
            $returnData['extend'] = $server->get_apply_form($returnData, $this->timezone);
        }
        return $returnData;
    }

    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result = $this->getOsStaffDetail(['id' => $auditId]);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        if ($result['store_id']) {
            $storeData = SysStoreModel::findFirst([
                'conditions' => "id = :store_id:",
                'bind'       => [
                    'store_id' => $result['store_id'],
                ],
            ]);

            if ($storeData) {
                $store_department = $this->getStoreDepartment();
                $department       = isset($storeData->category) && $storeData->category
                    ? ($store_department[$storeData->category] ?? '')
                    : "";
            }
        }

        $hubOsJobTitles = (new SettingEnvServer())->getSetVal('hub_os_select_roles', ',');
        $isHuoOs = in_array($result['job_id'], $hubOsJobTitles);
        if ($isHuoOs) {
            $department = (new SysDepartmentServer())->getDepartmentDetail($result['department_id'])['name'] ?? '';
        }

        //获取待审批的审批人
        $approvalList = AuditApprovalModel::find([
            'columns'    => 'approval_id',
            'conditions' => "biz_value = :value: and biz_type = :type: and state = 1 and deleted = 0",
            'bind'       => [
                'type'  => AuditListEnums::APPROVAL_TYPE_OS,
                'value' => $auditId,
            ],
        ])->toArray();
        $approvalListArr = array_column($approvalList, 'approval_id');
        
        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }
        //有修改显示修改的
        $demendNum = !empty($result['final_audit_num']) ? $result['final_audit_num']: ($result['demend_num'] ?? '');

        //组织详情数据
        $detailLists = [
            'apply_parson'       => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? ''),
            'apply_department'   => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
            'os_mode'            => $this->getTranslation()->_('os_request_mode_' . $result['os_type']),
            'os_staff_job_title' => $result['job_title'] ?? '',
            'os_outsourcing_hire_type'=> $this->getTranslation()->_('os_outsourcing_hire_type_' . $result['hire_os_type']),//外协雇佣类型
            'department'         => $department ?? '',
            'store'              => $result['store_name'] ?? '',
            'employment_date'    => $result['employment_date'] ?? '',
            'employment_days'    => $result['employment_days'] ?? '',
            'work_shift'         => $result['work_shift'] ?? '',
            'demend_num'         => $demendNum ?? '',
            'reason_app'         => $result['reason'] ?? '',
            'remark'             => $result['remark'] ?? '',
            'live_photo'         => $result['image_path'] ?? '',
        ];
        if (!$isHuoOs && in_array($user, $approvalListArr) && $result['status'] == enums::APPROVAL_STATUS_PENDING) {
            // 网点在职卡车司机数量
            $store_truck_driver_staff_num = 0;
            if (!empty($result['store_id'])){
                $onJobStaff = (new OsStaffRepository($this->timeZone))->getStoreOnJobStaffNum($result['store_id'], [
                    enums::$job_title['truck_courier'],
                    enums::$job_title['truck_courier_night'],
                ]);
                $store_truck_driver_staff_num = ($onJobStaff[enums::$job_title['truck_courier']] ?? 0) + ($onJobStaff[enums::$job_title['truck_courier_night']] ?? 0);
            }
            $detailLists['store_truck_driver_staff_num'] = (string)$store_truck_driver_staff_num;
        }
        if(!empty($result['out_company_id'])) {
            $res = $this->getOutCompanyInfoOne($result['out_company_id']);
            $detailLists['out_os_company_name'] = !empty($res) ? $res['company_name'] : '';
        }

        //驳回状态，需要显示驳回原因
        if ($result['status'] == enums::APPROVAL_STATUS_REJECTED) {
            $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        $hubOsSelectRoles    = (new SettingEnvServer())->getSetValFromCache('hub_os_select_roles', ',');


        $data = [
            'title'      => $this->auditlist->getAudityType(AuditListEnums::APPROVAL_TYPE_OS),
            'id'         => $result['id'],
            'staff_id'   => $result['staff_id'],
            'type'       => AuditListEnums::APPROVAL_TYPE_OS,
            'created_at' => $result['created_at'],
            'updated_at' => $result['updated_at'],
            'status'     => $result['status'],
            'is_stint'     => $result['os_type'] ==  1 && in_array($result['job_id'],$hubOsSelectRoles) ? 1 : 0,
            'status_text'=> $this->auditlist->getAuditState($result['status']),
            'serial_no'  => $result['serial_no'] ?? '',
            'demend_num' => $demendNum ?? '',
            'final_audit_num'   => $result['final_audit_num'],
            'employment_days'   => $result['employment_days'],
            'job_title_id'   => $result['job_id'],
            'os_type'   => $result['os_type'],
        ];

        //审批时候也能修改人数 要给前端传一个 最大能修改数量
        if($result['job_id'] == enums::$job_title['hub_operator'] || $result['job_id'] == enums::$job_title['warehouse_staff']){
            $data['apply_num'] = 200;
        }

        //待审批的审批人需要显示参考表
        if (in_array($user, $approvalListArr) && $result['status'] == 1) {
            $returnData['data']['os_extend'] = $result['extend'] ?? [];
        }

        if ($result['status'] == enums::APPROVAL_STATUS_APPROVAL) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('final_num'), 'value' => $result['final_audit_num']],
            ];
        } else if ($result['status'] == enums::APPROVAL_STATUS_REJECTED) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('reject_reason'), 'value' => $result['reject_reason']],
            ];
        }

        $returnData['data']['head']   = $data;
        return $returnData;
    }

    /**
     * @description 自定义审批人审批按钮
     * @param $auditId
     * @return array
     */
    public function customiseOptions($auditId): array
    {
        return array_merge(AuditDetailOperationsEnums::BUTTON_COMMON_APPROVAL, [6,18]);
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(true,
            false,
            false,
            false,
            false,
            false);
    }

    // -------- country 迁移过来的------
    //获取审批详情的 tbl 列表
    public function get_apply_form($returnData, $timezone)
    {
//        $this->os             = new OsStaffRepository($this->timeZone);
        $data                 = [];
        $dates                = [];
        $returnData['extend'] = [];
        $start_time           = strtotime(date("Y-m-d", strtotime("-4 day")));
        $end_time             = strtotime(date("Y-m-d", strtotime("-1 day")));
        $ret                  = $this->os->getOsStaffReferInfo($returnData['store_id'], $start_time, $end_time);

        //是否是 mobile dc 有额外字段
        $this->is_mobile_dc = $isMobileDC = $returnData['job_id'] == enums::$job_title['mobile_dc'];
        $workNum = [];
        if ($ret) {
            //在职van bike人数
            $van_cnt  = $this->os->getCourierCnt($returnData['store_id'], 110);
            $bike_cnt = $this->os->getCourierCnt($returnData['store_id'], 13);
            //在职仓管人数
            $soles_cnt = $this->os->getCourierSoles($returnData['store_id'], 2);
            if($isMobileDC){
                //mobile dc  在职人数 查打卡表
                $attendanceParam['storeId']   = $returnData['store_id'];
                $attendanceParam['startDate'] = date('Y-m-d', $start_time);
                $attendanceParam['endDate']   = date('Y-m-d', $end_time);
                $attendanceNum = $this->getWorkNum($attendanceParam);//mobile dc 对应日期的出勤人数

                //获取工作量 https://yapi.flashexpress.pub/project/59/interface/api/85106
                $ac = new ApiClient('bi_rpcv2', '', 'dc.getMobileDCHandoverCount', $this->lang);
                $ac->setParams($attendanceParam);
                $ac_result = $ac->execute();
                $workNum = ($ac_result['result']['code'] == 1 && !empty($ac_result['result']['data']))
                    ? array_column($ac_result['result']['data'], 'handoverCount', 'date')
                    : [];
            }

            //在仓大件数量
            $queryParam['storeId']   = $returnData['store_id'];
            $queryParam['startDate'] = date('Y-m-d', $start_time);
            $queryParam['endDate']   = date('Y-m-d', $end_time);
            $client = new ApiClient('ard_api', '', 'dc.should_delivery_large_parcel', $this->lang);
            $client->setParams($queryParam);
            $queryResult = $client->execute();

            $largeParcel = ($queryResult['result']['code'] == 1 && !empty($queryResult['result']['data']))
                ? array_column($queryResult['result']['data'], 'largeParcelCnt', 'date')
                : [];
        }
        if ((isset($ret['avg']) && $ret['avg']) || (isset($ret['cnt']) && $ret['cnt']) || (isset($ret['undertake']) && $ret['undertake'])) {

            $dates     = DateHelper::DateRange($start_time, $end_time);
            $van_cnt   = $van_cnt ?? 0;
            $bike_cnt  = $bike_cnt ?? 0;
            $soles_cnt = $soles_cnt ?? 0;
            foreach ($dates as $date) {
                $realDate = $date;
                $date = date("m-d", strtotime($date));
                //揽件量
                $delivery_undertake = $ret['undertake'][$date] ?? 0;
                //应派件数
                $delivery_cnt = intval($ret['cnt'][$date] ?? 0);
                //人均妥投
                $parcel_avg = $ret['avg'][$date] ?? 0;
                //大件包裹数量
                $large_parcel = $largeParcel[$realDate] ?? 0;
                //人均效能
                $delivery_avg = $soles_cnt == 0 ? 0 : round(($delivery_cnt + $delivery_undertake) / $soles_cnt, 2);

                $row['stat_date']          = ['num' => $date, 'is_red' => 1];//日期
                $row['delivery_undertake'] = ['num' => $delivery_undertake, 'is_red' => 1];//揽件量
                $row['delivery_cnt']       = ['num' => $delivery_cnt, 'is_red' => 1];//应派件数
                $row['parcel_avg']         = ['num' => $parcel_avg, 'is_red' => 1];//人均妥投
                $row['delivery_avg']       = ['num' => $delivery_avg, 'is_red' => 1];//人均效能[仓管人效]
                $row['large_parcel_no']    = ['num' => $large_parcel, 'is_red' => 1];//在仓大件数量

                //移动dc 额外字段
                if ($isMobileDC) {
                    $num = $attendanceNum[$realDate] ?? 0;
                    $row['mobile_dc_work_rate'] = ['num' => 0, 'is_red' => 1];
                    if (!empty($num)) {
                        $rate                       = round(($workNum[$realDate] ?: 0) / $num, 2);
                        $row['mobile_dc_work_rate'] = ['num' => $rate, 'is_red' => 1];
                    }
                }
                $data[] = $row;
            }

            $rowItem = [];
            $columns = [
                'delivery_undertake' => 'undertake_count',
                'delivery_cnt'       => 'parcel_count',
                'parcel_avg'         => 'parcel_per',
                'delivery_avg'       => 'parcel_avg',
                'large_parcel_no'    => 'large_parcel_no',
            ];
            if ($isMobileDC) {
                $columns['mobile_dc_work_rate'] = 'mobile_dc_work_rate';
            }
            foreach ($columns as $column => $t) {
                $rowItem[$column][] = ['num' => $this->getTranslation()->_($t), 'is_red' => 1];
                foreach ($data as $item) {
                    $rowItem[$column][] = $item[$column];
                }
            }
        }

        $retData              = $this->generateTableV2($this->getTranslation()->_('last_4_days_parcel'), $dates, array_values($rowItem));
        $retData['van_cnt']   = $van_cnt ?? 0;
        $retData['bike_cnt']  = $bike_cnt ?? 0;
        $retData['soles_cnt'] = $soles_cnt ?? 0;
        array_push($returnData['extend'], $retData);
        return $returnData['extend'];
    }


    /**
     * 组织表数据
     * @param string $title     表头
     * @param array  $tableData 表数据
     * @return array
     */
    public function generateTable($title, $tableData)
    {
        $returnData = [];
        if ($tableData) {
            $detail = [];
            foreach ($tableData as $k => $v) {
                $row = [
                    $v['stat_date'],
                    $v['delivery_undertake'] ?? '',
                    $v['delivery_cnt'] ?? '',
                    $v['parcel_per'] ?? '',
                    $v['delivery_avg'] ?? '',
                ];
                if($this->is_mobile_dc){
                    $row[] = $v['mobile_dc_work_rate'] ?? '';
                }
                $detail[] = $row;
            }

            $returnData = [
                'title'     => $title,
                'title_two' => '',
                'th'        => [
                    $this->getTranslation()->_('date'),
                    $this->getTranslation()->_('undertake_count'),
                    $this->getTranslation()->_('parcel_count'),
                    $this->getTranslation()->_('parcel_per'),
                    $this->getTranslation()->_('parcel_avg'),
                ],
                'td'        => $detail,
            ];
            if($this->is_mobile_dc){
                $returnData['th'][] = $this->getTranslation()->_('mobile_dc_work_rate');
            }

        }
        return $returnData;
    }

    /**
     * 组织表数据
     * @param array $title     表头
     * @param array  $tableData 表数据
     * @return array
     */
    public function generateTableV2($label, $title, $tableData)
    {
        $returnData = [];
        if (empty($tableData) || empty($label)) {
            return $returnData;
        }

        foreach ($title as $k => &$v) {
            $v = date('m-d', strtotime($v));
        }

        array_unshift($title, $this->getTranslation()->_('date'));
        return [
            'title'     => $label,
            'title_two' => '',
            'th'        => $title,
            'td'        => $tableData,
        ];
    }


    public function get_this_month_os_happening($jobId, $store_id, $submit_store_id, $osType, $timezone, $roles = [])
    {
        $returnData = [
            'is_remind' => 1,  //1 不需要提醒 2 需要提醒
            'info'      => [
                'store_name'         => '',
                'os_num'             => ['num' => "0", 'is_remind' => 1],//累计外协人次
                'inefficient_os_num' => ['num' => "0", 'is_remind' => 1],//累计低效外协
                'inefficient_os_pe'  => ['num' => "0%", 'is_remind' => 1],//低效外协占比
            ],
        ];

        //外协能申请的人数 暂时放这个接口 以后整改再说
        if ($jobId == enums::$job_title['hub_operator'] || $jobId == enums::$job_title['warehouse_staff']) {
            $returnData['apply_num'] = 200;
        }
        $hubOsJobTitles = (new SettingEnvServer())->getSetVal('hub_os_select_roles', ',');
        if (in_array($jobId, $hubOsJobTitles)) {
            $returnData['apply_num'] = 200;
        }

        return $returnData;
    }

    public function get_os_risk_prompt($jobId, $store_id, $submit_store_id, $osType, $timezone)
    {

        $returnData = [
            'is_remind' => 1,//1 不需要提醒 2 需要提醒
            'info_list' => [//返回 ['date_val'='6/09','compliance_line'=>100,'formal_human_effect'=>10];  //达标线   正式员工派件人效
            ],
            'x'         => 0,//没有达标的天数
        ];
        return $returnData;

    }


    // ------------------ country  迁移 结束-----------






}