<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Enums\HrStaffPositionEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Modules\Ph\library\Enums\CommonEnums;
use FlashExpress\bi\App\Repository\AdministrationOrderRepository;
use FlashExpress\bi\App\Repository\AdministrationQuestionTypeRepository;
use FlashExpress\bi\App\Repository\StaffWorkAttendanceRepository;
use FlashExpress\bi\App\Server\AdministrationOrderServer as BaseAdministrationOrderServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysDepartmentServer;
use FlashExpress\bi\App\Server\SysStoreServer;

class AdministrationOrderServer extends BaseAdministrationOrderServer
{

    /**
     * 新逻辑 判断是否有权限展示 "行政工单" 菜单
     * @param $staffInfo
     * @return bool
     */
    public function checkDealAdministrationOrderPermission($staffInfo): bool
    {
        $config = (new SettingEnvServer())->listByCode([
            'administrative_work_order_staff_id',
            'administrative_work_order_job_title',
            'administrative_work_order_department_id',
        ]);
        if (empty($config)) {
            return false;
        }
        $config = array_column($config, 'set_val', 'code');

        if (!empty($config['administrative_work_order_staff_id']) && in_array($staffInfo['id'],
                explode(',', $config['administrative_work_order_staff_id']))) {
            return true;
        }
        if (!empty($config['administrative_work_order_job_title']) && in_array($staffInfo['job_title'],
                explode(',', $config['administrative_work_order_job_title']))) {
            return true;
        }
        if (!empty($config['administrative_work_order_department_id']) && in_array($staffInfo['department_id'],
                explode(',', $config['administrative_work_order_department_id']))) {
            return true;
        }
        return false;
    }

    /**
     * 新逻辑 行政工单菜单权限入口获取
     * @param array $userInfo
     * @return int[]
     */
    public function getXzNums(array $userInfo): array
    {
        $data = [
            // 工单提交数据
            'xz_submit_num' => 0,
            // "行政工单" 菜单入口权限
            "is_xz"         => 0,
        ];
        // 提交人的 已经回复数据
        $replyData            = (new AdministrationOrderRepository())->getInfoByParams([
            'status'           => enums::TICKET_STATUS_REPLY,
            'created_staff_id' => $userInfo['id'],
        ], ['count(*) AS nums']);
        $data['xz_reply_num'] = $replyData ? (int)$replyData[0]['nums'] : 0;

        // hr 待回复数
        if (true === $this->checkDealAdministrationOrderPermissionFromCache($userInfo)) {
            $data['is_xz']         = 1;
            $data['xz_submit_num'] = $this->getAfterPermissionNum($userInfo,
                ['status' => enums::TICKET_STATUS_WAIT_REPLY]);
        }
        return $data;
    }

    /**
     * PH 国家权限差异化处理
     * 获取 待回复、已回复、已关闭工单列表
     * @param $paramIn
     * @param $userInfo
     * @return array
     */
    public function list($paramIn, $userInfo): array
    {
        if (empty($paramIn['page_num'])) {
            $paramIn['page_num'] = 1;
        }

        if (empty($paramIn['page_size'])) {
            $paramIn['page_size'] = 20;
        }

        // [1]翻页处理
        $pageNum  = intval($paramIn['page_num']);
        $pageSize = intval($paramIn['page_size']);
        $start    = ($pageNum - 1) * $pageSize;

        // [2]组织查询参数
        $params = [
            'status' => (int)$paramIn['status']    // 处理状态：1待回复，2已回复，3已关闭'
        ];

        // 问题类型
        if (isset($paramIn['question_type_id']) && !empty($paramIn['question_type_id'])) {
            $params['question_type_id'] = (int)$paramIn['question_type_id'];
        }

        // from=1 表示请求来自提交入口 需要拼接提交人的员工号
        if (1 == $paramIn['from']) {
            $params['created_staff_id'] = (int)$userInfo['id'];
        }

        $params['from'] = $paramIn['from'];
        
        $colum  = [
            "m.id",                 // 主键id
            "m.order_code",         // 行政工单号码
            "m.question_type_id",   // 问题类型
            "m.store_id",           // 问题所在网点
            "m.line_id",            // line_id
            "m.mobile",             // mobile
            "m.info",               // 问题详情
            "m.pics",               // 图片
            "m.updated_at",         // 更新时间
            "m.created_at",         // 提交时间
            "m.status",             // 状态
            "m.created_staff_id",   // 提交人信息
            "m.created_staff_name", // 提交姓名
        ];
        $order  = ['field' => 'm.created_at', 'sort' => 'DESC'];
        $result = $this->getAfterPermissionList($userInfo, $params, $colum, $order, $pageSize, $start);

        // 返回数据
        $data = [
            "dataList"   => [],
            "pagination" => [
                "pagination" => $pageNum,
                "pageSize"   => $pageSize,
                "count"      => 0,
            ],
        ];

        if (empty($result['list']) || !is_array($result)) {
            return $data;
        }

        $storeIds = array_unique(array_column($result['list'], 'store_id'));
        // 去掉总部
        if ($index = array_search('-1', $storeIds)) {
            unset($storeIds[$index]);
        }

        // [4]获取网点
        $storeList = [];
        $storeRet  = SysStoreModel::find([
            'conditions' => "id IN ({store_ids:array})",
            'bind'       => [
                'store_ids' => array_values($storeIds),
            ],
            "columns"    => 'id,name',
        ])->toArray();
        if (!empty($storeRet)) {
            $storeList = array_column($storeRet, null, 'id');
        }

        // [5]获取问题类型
        $questionTypeList = [];
        $questionTypeIds  = array_values(array_unique(array_column($result['list'], 'question_type_id')));
        $params           = [
            "id" => $questionTypeIds,
        ];
        $questionTypeRet  = (new AdministrationQuestionTypeRepository())->getQuestionTypeByParams($params,
            ["id", "t_key"]);
        if (!empty($questionTypeRet)) {
            $questionTypeList = array_column($questionTypeRet, null, 'id');
        }

        // [6]数据组装处理
        foreach ($result['list'] as $key => &$val) {
            $val['store_name'] = "";
            if ('-1' == $val['store_id']) {
                $val['store_name'] = enums::HEAD_OFFICE;
            }

            if (!empty($storeList[$val['store_id']])) {
                $val['store_name'] = $storeList[$val['store_id']]['name'];
            }
            $val['pics']               = json_decode($val['pics'], true);
            $val['question_type_name'] = $this->getTranslation()->_($questionTypeList[$val['question_type_id']]['t_key']);
        }
        $data['dataList']                 = $result['list'];
        $data['pagination']['pagination'] = $pageNum;
        $data['pagination']['pageSize']   = $pageSize;
        $data['pagination']['count']      = $result['total'];
        return $data;
    }

    /**
     *  获取经过权限过滤后的工单列表
     * @param $staffInfo
     * @param $params
     * @param $colum
     * @param $order
     * @param $pageSize
     * @param $start
     * @return array
     */
    public function getAfterPermissionList($staffInfo, $params, $colum, $order, $pageSize, $start): array
    {
        $permission = isset($params['from']) && $params['from'] == 1 ? [] : $this->getStaffPermissionFromCache($staffInfo);
        $this->getDI()->get('logger')->write_log(['getStaffPermission' => $permission, 'staffInfo' => $staffInfo],
            'info');
        return (new AdministrationOrderRepository())->getListUseStaffInfo(array_merge($permission, $params), $colum,
            $order, $pageSize, $start);
    }


    /**
     * 获取经过权限过滤后的工单数量
     * @param $staffInfo
     * @param array $params
     * @return int
     */
    public function getAfterPermissionNum(array $staffInfo, array $params = []): int
    {
        $permission = isset($params['from']) && $params['from'] == 1 ? [] : $this->getStaffPermissionFromCache($staffInfo);
        $this->getDI()->get('logger')->write_log(['getStaffPermission' => $permission, 'staffInfo' => $staffInfo],
            'info');
        return (new AdministrationOrderRepository())->getNumUseStaffInfo(array_merge($permission, $params));
    }

    /**
     * ph 国家行政工单 数据权限差异化
     * 获取行政工单数据权限
     * @param $staffInfo
     * @return array|bool
     */
    public function getStaffPermission(array $staffInfo)
    {
        // 1.1 HRIS管理员[41] 超级管理员[99] HR系统管理员[115] 系统管理员[14]
        if (array_intersect($staffInfo['positions'], [
            HrStaffPositionEnums::ROLE_HRIS,
            HrStaffPositionEnums::ROLE_SYSTEM_ADMIN,
            HrStaffPositionEnums::ROLE_HR_SYSTEM_ADMIN,
            HrStaffPositionEnums::ROLE_SUPER_ADMIN,
        ])) {
            return [];
        }

        //1.2 HRBP[68] 查看自己管辖范围内的员工提交的工单数据
        if (array_intersect($staffInfo['positions'], [HrStaffPositionEnums::ROLE_HRBP])) {
            $relations = (new StaffServer($this->lang,
                $this->timeZone))->getStaffJurisdiction($staffInfo['id']);
            $stores    = (new StaffWorkAttendanceRepository())->getManagerStore($staffInfo['id']);
            return [
                'multi_permission' => [
                    'manage_departments' => $relations['departments'] ?: ['-100'],
                    'stores'             => array_column($stores, 'store_id'),
                ],
            ];
        }
        //2.1 cpo 组织下员工  查看"问题所在网点"为“Head office”的工单数据
        $sysDepartmentServer = new SysDepartmentServer();
        if (in_array($staffInfo['department_id'], $sysDepartmentServer->getDepartmentIdsUseSettingEnv('dept_cpo_id'))) {
            return ['source_store_id' => '-1'];
        }

        //2.2 Warehouse Procurement Department组织以及下级组织下的员工
        $aboutWPD   = $sysDepartmentServer->getDepartmentIds(         CommonEnums::WAREHOUSE_PROCUREMENT_DEPARTMENT);
        if (in_array($staffInfo['department_id'], $aboutWPD)) {
            // 查看Network Management一级部门以及子部门员工提交的数据（未来支持配置更多的部门以及子部门）
            $manageDepartmentId = (new SettingEnvServer())->getSetVal('wpd_manage_department_id',',');
            if (!empty($manageDepartmentId)) {
                $manageAllDepartment = [];
                foreach ($manageDepartmentId as $v) {
                    $manageAllDepartment = array_merge($manageAllDepartment,
                        $sysDepartmentServer->getDepartmentIds($v));
                }
                return [
                    'multi_permission' => [
                        'departments' => array_values($manageAllDepartment) ?: ['-100'],
                    ]
                ];
            }
        }

        //负责的网点
        $sysServer = new SysStoreServer($this->timeZone);
        //负责的大区、片区、网点 to 网点ID
        $managerAllStoreIds = $sysServer->getStoreByManager($staffInfo['id']);
        //负责的组织以及下级组织
        $managerDepartmentIds = $sysDepartmentServer->getManagerDepartmentIds($staffInfo['id']);
        //所在一级部门及子部门 负责的组织以及下级组织
        $sysDepartmentId = $staffInfo['organization_id'];
        if ($staffInfo['organization_type'] == 1) {
            $staffInfoModel  = HrStaffInfoModel::findFirstByStaffInfoId($staffInfo['id']);
            $sysDepartmentId = $staffInfoModel ? $staffInfoModel->sys_department_id : 0;
        }

        $currentDepartmentIds = $sysDepartmentServer->getDepartmentIds($sysDepartmentId);
        return [
            'multi_permission' => [
                'stores'      => array_values($managerAllStoreIds) ?: ['-100'],
                'departments' => array_values(array_merge($currentDepartmentIds, $managerDepartmentIds)) ?: ['-100'],
            ],
        ];
    }

}