<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;



class CompanyTerminationContractServer extends \FlashExpress\bi\App\Server\CompanyTerminationContractServer{

    /**
     * 解约原因(每个国家分开写)
     * @return array[]
     */
    protected function getReasonEnum(): array
    {
        $t = $this->getTranslation();
        return [
            ['value' => '5', 'label' => $t->_('termination_reason_5')],
            ['value' => '6', 'label' => $t->_('termination_reason_6')],
            ['value' => '7', 'label' => $t->_('termination_reason_7')],
            ['value' => '8', 'label' => $t->_('termination_reason_8')],
        ];
    }

}
