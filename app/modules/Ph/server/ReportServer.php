<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;


use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\ViolationReasonTypeModel;
use FlashExpress\bi\App\Repository\ReportRepository;
use FlashExpress\bi\App\Server\ReportServer as BaseReportServer;

class ReportServer extends BaseReportServer
{

    protected function getReportShowDetail($result, $staff_info, $imgArr, $report_staff_info)
    {
        $t = $this->getTranslation();
        $violationInfo       = ViolationReasonTypeModel::findFirstByTId($result['reason']);
        $parentViolationInfo = ViolationReasonTypeModel::findFirstByTId($violationInfo->pid);

        return [
            [
                'type'  => 'apply_parson',
                'key'   => $t->_('apply_parson'),
                'value' => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? ''),
            ],
            [
                'type'  => 'apply_department',
                'key'   => $t->_('apply_department'),
                'value' => sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? ''),
            ],
            [
                'type'  => 'report_id',
                'key'   => $t->_('report_id'),
                'value' => $report_staff_info['staff_id'] ?? '',
            ],
            [
                'type'  => 'report_name',
                'key'   => $t->_('report_name'),
                'value' => $report_staff_info['name'] ?? '',
            ],
            [
                'type'  => 'report_job_name',
                'key'   => $t->_('report_job_name'),
                'value' => $report_staff_info['job_name'] ?? '',
            ],
            [
                'type'  => 'report_store_name',
                'key'   => $t->_('report_store_name'),
                'value' => $report_staff_info['store_name'] ?? '',
            ],
            [
                'type'  => 'report_hire_type_text',
                'key'   => $t->_('hire_type'),
                'value' => $report_staff_info['hire_type_text'] ?? '',
            ],
            [
                'type'  => 'report_type',
                'key'   => $t->_('report_type'),
                'value' => $t->_('type-' . $result['report_type']),
            ],
            [
                'type'  => 'violation_reason_level_1',
                'key'   => $t->_('violation_reason_level_1'),
                'value' => $t->_($parentViolationInfo->t_key),
            ],
            [
                'type'  => 'violation_reason_level_2',
                'key'   => $t->_('violation_reason_level_2'),
                'value' => $t->_('t_warning_' . $result['reason']),
            ],
            [
                'type'  => 'event_date',
                'key'   => $t->_('event_date'),
                'value' => $result['event_date'] ?? '',
            ],
            [
                'type'  => 'report_remark',
                'key'   => $t->_('report_remark'),
                'value' => $result['remark'] ?? '',
            ],
            [
                'type'  => 'picture',
                'key'   => $t->_('picture'),
                'value' => $imgArr ?? [],
            ],
        ];
    }

    public function validateReportReason($paramIn)
    {
        $data = $this->dictReportS([], ['is_svc' => true]);
        $list = array_column($data['data']['reason'], 'id');
        if (!in_array($paramIn['reason'], $list)) {
            throw new ValidationException($this->getTranslation()->_('4012'));
        }
        return true;
    }


    public function dictReportS($userinfo, $paramIn)
    {
        $loginStaffId       = $userinfo['staff_id'];
        $reportType = ReportRepository::$reportType;
        unset($reportType['3']);
        $typeArr = [];
        foreach ($reportType as $id => $type) {
            $typeArr[$id] = [
                'id'   => $id,
                'type' => $this->getTranslation()->_($type),
            ];
        }
        //【举报】配置允许按管辖范围举报的工号|[举报原因类型]，举报原因类型1=正式上级，2=正式qaqc，3=个人代理上级(仅MY)，4=个人代理qaqc(仅MY)，5=正式员工(仅PH)，多个工号|[类型之间];分隔
        // 配置允许按管辖范围举报的工号可以举报的 举报原因
        $reportJurisdictionConfig = $this->checkReportJurisdictionConfig($loginStaffId);

        $this->logger->write_log(['checkReportJurisdictionConfig' => ['loginStaffId' => $loginStaffId, 'reportJurisdictionConfig' => $reportJurisdictionConfig]], 'info');

        if (empty($paramIn['is_svc'])) {
            $list   = (new ReportRepository($this->lang, $this->timezone))->violationReasonTypeList(false, true, $reportJurisdictionConfig['reason_types']);
            $reason = array_values(list_to_tree($list, 'id', 'pid', 'children'));
        } else {
            $reason = (new ReportRepository($this->lang, $this->timezone))->violationReasonTypeList(false, false, $reportJurisdictionConfig['reason_types']);
        }
        $data['data'] = [
            'reason' => $reason,
            'type'   => $typeArr,
        ];
        return $data;
    }


}