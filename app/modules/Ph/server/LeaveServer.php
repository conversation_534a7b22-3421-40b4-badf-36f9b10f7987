<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/9/8
 * Time: 2:26 PM
 */

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\StaffDaysFreezeModel;
use FlashExpress\bi\App\Models\backyard\StaffLastYearDaysModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Modules\Ph\Server\Vacation\AnnualServer;
use FlashExpress\bi\App\Modules\Ph\Server\Vacation\MaternityServer;
use FlashExpress\bi\App\Modules\Ph\Server\Vacation\SickServer;
use FlashExpress\bi\App\Modules\Ph\Server\Vacation\SingleParentServer;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;

use FlashExpress\bi\App\Repository\AuditRepository;

use FlashExpress\bi\App\Server\LeaveServer AS GlobalBaseServer;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Modules\Ph\Server\Vacation\InternationalServer;


class LeaveServer extends GlobalBaseServer {

    public $leaveObject;
    public $initType = [enums::LEAVE_TYPE_19,enums::LEAVE_TYPE_20,enums::LEAVE_TYPE_4,enums::LEAVE_TYPE_3];
    //菲律宾 返回所有ph 以及 对应类型 返回 map
    public function ph_days($staff_info)
    {
        return ThailandHolidayModel::find([
            'columns'    => 'day,holiday_type'
        ])->toArray();
    }

    /**
     *
     * "F0~F14：7/15
    F15~F16：9/15
    F17~F18：12/15
    F19（含）以上above： 12/17"
     * @param $staff_info
     * @return array|int|mixed
     */
    public function get_year_leave_days($staff_info)
    {
        $grade = $staff_info['job_title_grade_v2'];
        if($grade <= 14 && $grade >= 0)
            return 7;
        if($grade > 14 && $grade <= 16)
            return 9;
        if($grade > 16 )
            return 12;

        return 7;
    }


    //新需求 计算 根据职等 对应的假期额度 have day
    public function get_have_day($staff_id,$should_days){
        if(empty($staff_id) || empty($should_days))
            return 0;

        $count_day = StaffDaysFreezeModel::findFirst("staff_info_id = {$staff_id}");
        if(empty($count_day))
            return 0;


        $current_tmp = strtotime(date('Y-m-d',strtotime("-1 day")));//昨天00 也就是 任务跑过的日期
        $start_tmp = strtotime(date('Y-01-01'));//周期第一天 00点
        $year = date('Y');
        $all_days = 365;
        if(year_type($year))
            $all_days = 366;

        //获取剩余天数
        $left_days = $all_days - (($current_tmp - $start_tmp) / (24 * 3600));
        $left_days = $should_days * ($left_days / $all_days);//每年剩余天数 对应职等天数的份数 例：剩余200天 职等7天  7 * (200/365)
        $have_days = $count_day->days + $left_days;
        $have_days = round($have_days,enums::ROUND_NUM_READ);
        $int_day   = floor($have_days);//2
        $have_days = $have_days > ($int_day + 0.5) ? ($int_day + 0.5) : $int_day;//2.5
        return $have_days;
    }



    //年假额度 最大上限
    public function get_year_leave_max_days($staff_info)
    {
        $grade = $staff_info['job_title_grade_v2'];
        if($grade < 19)
            return 15;
        else
            return 17;

    }

    public function check_leave_4($need_days, $limit_days,$used_days,$sub_type,$leave_info)
    {
        if(empty($sub_type) || !in_array($sub_type,array(1,2,3,4)))
            return  $this->getTranslation()->_('miss_args');
        /**
         * 定制逻辑
         * 每次生产额度105天
        怀孕期间流产或者终止妊娠60天  type 1
        单亲父母多享受15天  type 2
        特殊规定：女性可以申请100天，剩下的5天给予丈夫休带薪假期  type 3
         */
        if($sub_type == 1)
            $limit_days = 60;
        if($sub_type == 2)
            $limit_days += 15;
        if($sub_type == 3)
            $limit_days -= 5;
//        if($sub_type == 4) 选择 无 天数不变

        if ($used_days >= $limit_days || ($need_days + $used_days) > $limit_days)
            return $this->getTranslation()->_('leave_limit');

        return true;

    }

    public function locate_check($leaveType,$need_days,$staff_info)
    {
        $work_day = empty($staff_info['week_working_day']) ? 5 : $staff_info['week_working_day'];
        //新冠相关假期 一次申请 最大 10天限制  可申请多次
        if(in_array($leaveType,array(25,26))){
            if($work_day == 5){//5天班 限制 8天最多
                $str = $this->getTranslation()->_('leave_limit_8');
                return $need_days > 8 ? $str : true;
            }
            if($work_day == 6){//6天班限制9天最多
                $str = $this->getTranslation()->_('leave_limit_9');
                return $need_days > 9 ? $str : true;
            }
        }

        return true;
    }


    /**
     * 根据员工信息 计算区间内是否存在休息日 返回对应休息日日期数组
     * 用于计算 请假区间 扣除包含休息日的天数
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     */
    public function staff_off_days($staff_id, $start_date, $end_date = '')
    {
        if (empty($end_date))
            $end_date = $start_date;

        //获取员工信息
        $model       = new StaffRepository($this->lang);
        $staff_info  = $model->getStaffPosition($staff_id);

        $rest = $model->get_work_days_between($staff_id, $start_date, $end_date);
        $rest = !empty($rest) ? array_column($rest, 'date_at') : [];

        $holidays = $this->ph_days($staff_info);
        if(!empty($holidays)){
            $holidays = array_column($holidays,'day');
            $rest = array_merge($rest,$holidays);

        }

        $this->logger->write_log("ignore rest_day {$staff_id} ".json_encode($rest),'info');
        return $rest;

    }



    /**
     * @param $param
     * @throws ValidationException
     * @return array
     */
    public function saveVacation($param)
    {
        $this->leaveObject = $this->getLeaveObj(intval($param['leave_type']));
        $auditServer       = new AuditServer($this->lang, $this->timeZone);

        //类型验证
        $staffRe    = new StaffRepository($this->lang);
        $staffInfo  = $staffRe->getStaffPosition($param['staff_id']);
        $leave_lang = AuditRepository::$leave_type;
        $typeData   = $auditServer->staffLeaveType($staffInfo);
        if (empty($typeData)) {
            throw new ValidationException('wrong leave type');
        }
        if (!in_array($param['leave_type'], array_keys($typeData)) && !in_array($param['leave_type'], [enums::LEAVE_TYPE_15, enums::LEAVE_TYPE_25, enums::LEAVE_TYPE_26])) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004') . $this->getTranslation()->_($leave_lang[$param['leave_type']]));
        }

        $db                = $this->getDI()->get('db');
        $db->begin();
        try {
            $audit_id = $this->leaveObject->handleCreate($param);
            //没生成id
            if (empty($audit_id)) {
                throw new ValidationException($this->getTranslation()->_('1009'));
            }

            //非工具操作申请 创建审批相关
            if (empty($param['is_bi'])) {
                $param['time_out'] = $this->leaveObject->timeOut ?? null;
                $auditServer->saveApproval($audit_id, $param);
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            throw $e;
        }

        $this->handleLeave($param);
        $return['data'] = ['leave_day' => $this->leaveObject->leave_day ?? 0];
        return $this->checkReturn($return);
    }

    //获取对应假期的额度
    public function getVacationDays($staffId, $leaveType, $extend = [])
    {
        $param['staff_id']   = $staffId;
        $param['leave_type'] = $leaveType;
        $param               = array_merge($param, $extend);
        $leaveObject         = $this->getLeaveObj($param['leave_type']);
        return $leaveObject->handleSearch($param);
    }

    //非审核通过 撤销操作 返还额度
    public function cancelVacation($auditInfo, $staffInfo, $state, $extend = [])
    {
        $extend['status']             = $state;
        $this->leaveObject            = $this->getLeaveObj(intval($auditInfo['leave_type']));
        $this->leaveObject->leaveType = intval($auditInfo['leave_type']);
        $this->leaveObject->returnRemainDays($auditInfo['audit_id'], $staffInfo, $extend);
    }


    /**
     * 对应本国的 所有类型 映射类
     * @param int $leaveType
     * @return
     * @throws ValidationException
     */
    public function getLeaveObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_1:
                $leaveObj =  new AnnualServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_19://跨国探亲
                $leaveObj =  new InternationalServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_20:
                $leaveObj =  new SingleParentServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_4:
                $leaveObj =  new MaternityServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_3:
                $leaveObj =  new SickServer($this->lang,$this->timeZone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }


    public function getInstanceObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_19://跨国探亲
                $leaveObj = InternationalServer::getInstance($this->lang,$this->timezone);
                break;
            case enums::LEAVE_TYPE_20:
                $leaveObj =  SingleParentServer::getInstance($this->lang,$this->timezone);
                break;
            case enums::LEAVE_TYPE_4:
                $leaveObj =  MaternityServer::getInstance($this->lang,$this->timezone);
                break;
            case enums::LEAVE_TYPE_3:
                $leaveObj =  SickServer::getInstance($this->lang,$this->timezone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }

    //获取 额度详情信息列表
    public function getAnnualDetail($param){
        $leaveObject = $this->getLeaveObj(enums::LEAVE_TYPE_1);
        return $leaveObject->detailList($param);
    }

    /**
     * @description 提交请假变更数据
     * @param $params
     * @return void
     */
    public function handleLeave($params = [])
    {
        $server = new AuditServer($this->lang, $this->timeZone);
        $staffId = $params['staff_id'] ?? $params['staff_info_id'];
        $server->handleLeave($params['leave_start_time'], $params['leave_end_time'], $staffId);
    }






}