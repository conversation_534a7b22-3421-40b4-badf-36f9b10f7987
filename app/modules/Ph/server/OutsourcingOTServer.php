<?php
/**
 * Author: Bruce
 * Date  : 2023-05-25 21:16
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Ph\Server;


use FlashExpress\bi\App\Server\OsStaffServer;
use FlashExpress\bi\App\Server\OutsourcingOTServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class OutsourcingOTServer extends GlobalBaseServer
{
    /**
     * 根据当前登入者信息判断是否未分拨经理
     * @param array $userInfo
     * @return bool
     */
    public function isHubFenBoManger(array $userInfo): bool
    {
        // 拿到分拨经理的角色id
        $hubOsRoles    = (new SettingEnvServer())->getSetValFromCache('hub_os_roles', ',');
        $loginPositions = $userInfo['positions'];

        if (array_intersect($hubOsRoles, $loginPositions)) {
            return true;
        }
        return false;
    }

    /**
     * 根据登入这id获取网点
     * @param array $userInfo
     * @return array|\Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getStoreListByLoginId(array $userInfo)
    {
        $store_list = [];
        //返回所属网点
        if (1 == $userInfo['organization_type']) {
            $store_list[0]['store_id']   = $userInfo['organization_id'];
            $store_list[0]['store_name'] = $userInfo['organization_name'];
            return $store_list;
        }
        return $store_list;
    }

    /**
     * by申请页基本数据
     * @param array $userInfo
     * @return array
     */
    public function getOutsourcingOTPre(array $userInfo): array
    {
        $data = [];
        // 网点信息
        $data['store_list'] = $this->getStoreListByLoginId($userInfo);
        // 加班时长
        $data['duration'] = $this->getDuration();
        // 外协公司信息
        $data['os_company_list'] = (new OsStaffServer($this->lang, $this->timeZone))->getOutCompanyInfo();
        return $data;
    }

}