<?php


namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysServer as GlobalBaseServer;

class SysServer extends GlobalBaseServer
{
    /**
     * @description 获取雇佣类型
     * @return array[]
     */
    public function getHireTypesList($job_title = 0, $store_id = '',$jd_id = 0): array
    {
        $agentJobTitle = (new SettingEnvServer)->getSetVal('individual_contractor_job_title', ',');
        $monthJobTitle = (new SettingEnvServer)->getSetVal('monthly_contract_employees_job_title', ',');


        //正式员工
        $hireTypeEnum[] = HrStaffInfoModel::HIRE_TYPE_1;

        //在月薪制中配置
        if (in_array($job_title, $monthJobTitle)) {
            $hireTypeEnum[] = HrStaffInfoModel::HIRE_TYPE_2;
        }

        //实习生
        $hireTypeEnum[] = HrStaffInfoModel::HIRE_TYPE_5;

        //在个人代理中配置
        if (in_array($job_title, $agentJobTitle)) {
            $hireTypeEnum[] = HrStaffInfoModel::HIRE_TYPE_UN_PAID;
        }

        $returnData = [];
        foreach ($hireTypeEnum as $k => $v) {
            $returnData[$k]['key']   = intval($v);
            $returnData[$k]['value'] = $this->getTranslation()->_('hire_type_' . $v);
        }

        return array_values($returnData);
    }
}