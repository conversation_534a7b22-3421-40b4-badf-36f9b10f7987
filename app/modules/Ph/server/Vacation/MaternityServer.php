<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 5/5/23
 * Time: 2:16 PM
 */

namespace FlashExpress\bi\App\Modules\Ph\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\Vacation\MaternityServer as GlobalServer;


class MaternityServer extends GlobalServer implements LeaveInterface
{

    private static $instance = null;

    public $today;

    public $thisYear;


    const SUB_TYPE_STOP = 1;
    const SUB_TYPE_SINGLE = 2;
    const SUB_TYPE_GIVE = 3;
    const SUB_TYPE_NULL = 4;
    public $addArr = [
        self::SUB_TYPE_STOP   => -45,//怀孕期间流产或者终止妊娠60天
        self::SUB_TYPE_SINGLE => 15,//单亲父母多享受15天
        self::SUB_TYPE_GIVE   => -5,//女性可以申请100天，剩下的5天给予丈夫休带薪假期
        self::SUB_TYPE_NULL   => 0,//无 基础105
    ];

    public $subTextArr = [
        self::SUB_TYPE_STOP   => 'leave_4_1',//怀孕期间流产或者终止妊娠60天
        self::SUB_TYPE_SINGLE => 'leave_4_2',//单亲父母多享受15天
        self::SUB_TYPE_GIVE   => 'leave_4_3',//女性可以申请100天，剩下的5天给予丈夫休带薪假期
        self::SUB_TYPE_NULL   => 'leave_4_4',//无 基础105
    ];



    //任务 每年初始化调用
    public static function getInstance($lang, $timezone)
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self($lang, $timezone);
        }
        return self::$instance;
    }

    //验证自类型参数
    public function subTypeCheck(){
        $tmpArr = $this->addArr;
        if(empty($this->paramModel['sub_type']) || !in_array($this->paramModel['sub_type'],array_keys($tmpArr))){
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
    }

    //获取额外天数
    public function getAddDays()
    {
        return $this->addArr[$this->paramModel['sub_type']] ?? 0;
    }

    //菲律宾 老挝 越南 产假 有特殊详情页展示字段
    public function formatDetailInfo($subType){
        $key = $this->subTextArr[$subType];
        if(empty($key)){
            return '';
        }
        return $this->getTranslation()->_($key);
    }


}