<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 3/6/23
 * Time: 10:44 AM
 */


namespace FlashExpress\bi\App\Modules\Ph\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveProperty;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Server\Vacation\SickServer as GlobalServer;


class SickServer extends GlobalServer implements LeaveInterface
{

    private static $instance = null;

    public $isMonthCasualDate = '';//转正式员工日期 标记正式员工情况 之前是不是 月薪合同工
    public $today;
    //是否跨年 如果跨年 需要拆成两半 标记成另外一年
    public $thisYear;
    public $anotherYear = false;
    public $unNormalDays = 3;//没转正员工 最多3天

    //操作扣减额度用
    public $currentApplyDays = 0;      //当前周期 申请天数过期日

    //任务 每年初始化调用
    public static function getInstance($lang, $timezone)
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self($lang, $timezone);
        }
        return self::$instance;
    }

    /**
     * 申请 保存入口
     * @param $param
     * @return mixed
     * @throws ValidationException
     */
    public function handleCreate($param)
    {
        //初始化数据
        $this->initData($param);

        //逻辑验证
        $this->businessCheck();

        //format 数据
        $this->dataFormat();

        //保存
        $this->dataSave();

        return $this->auditId;
    }

    public function initData($param)
    {
        parent::initData($param);

        //查询item 看有没有转正式员工日期
        $item = HrStaffItemsModel::findFirst([
            'conditions' => 'item = :code: and staff_info_id = :staff_id:',
            'bind'       => ['code' => 'CONVERSION_PERMANENT_DATE', 'staff_id' => $this->staffInfo['staff_info_id']],
        ]);
        if ($item) {
            $this->isMonthCasualDate = $item->value;
        }
        $this->today    = date('Y-m-d');
        $this->thisYear = date('Y');
        //判断是否跨年
        $this->isOverOneYear();

        //获取额度
        $this->getLimitDays();

    }

    //整理数据 数据结构 audit, split, img
    protected function dataFormat()
    {
        //先保存 主表 拿audit id
        $this->saveAudit();

        //拆分表 audit id
        $this->formatSplitData();
    }


    //额度查询 入口
    public function handleSearch($param)
    {
        //初始化数据
        $this->initSearch($param);


        $this->getLimitDays();

        //整理成 day_sub  day_limit
        return $this->formatLimitDays();
    }

    public function initSearch($param)
    {
        parent::initSearch($param);
        //查询item 看有没有转正式员工日期
        $item = HrStaffItemsModel::findFirst([
            'conditions' => 'item = :code: and staff_info_id = :staff_id:',
            'bind' => ['code' => 'CONVERSION_PERMANENT_DATE', 'staff_id' => $this->staffInfo['staff_info_id']],
        ]);
        if($item){
            $this->isMonthCasualDate = $item->value;
        }
        $this->today = date('Y-m-d');
        $this->thisYear = date('Y');
    }

    public function getLimitDays()
    {
        //获取额度
        $this->limitDays['limit'] = $this->limitDays['sub'] = 0.0;
        $this->thisYear = $this->thisYear ?: date('Y');
        //没有权限
        if (!$this->applyPermission($this->staffInfo)) {
            return;
        }

        //获取额度
        $info = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'year_at'    => $this->thisYear,
                'leave_type' => enums::LEAVE_TYPE_3,
            ],
        ]);
        if (empty($info)) {
            [$this->limitDays['limit'], $this->limitDays['sub']] = $this->initSickDays($this->thisYear);
        } else {
            $this->limitDays['limit'] = half_num($info->freeze_days);
            $this->limitDays['sub']   = half_num($info->days);
        }

        //如果没转正 找去年申请的
        if (!$this->hireCheck()) {
            //获取对应年已经申请的病假
            $start       = date('Y-m-d', strtotime($this->staffInfo['hire_date']));
            $end         = date('Y-12-31', strtotime('+2 year'));
            $appliedDays = $this->leaveDaysBetween($this->staffInfo['staff_info_id'], $start, $end, enums::LEAVE_TYPE_3);
            //没转正 取最小值
            $this->limitDays['limit'] = min($this->limitDays['limit'], $this->unNormalDays);//总额度 分母
            $this->limitDays['sub']   = max(0, $this->limitDays['limit'] - $appliedDays);//剩余额度 分子
            //另外一年的 额度清空
            $this->limitDays['other_sub'] = $this->limitDays['other_limit'] = half_num(0);
            return;
        }

        //跨年申请 并且是转正员工 才能查另外一年的额度 否则就只有今年的额度
        if ($this->anotherYear) {
            $thatRemain = StaffLeaveRemainDaysModel::findFirst([
                'columns'    => 'staff_info_id,leave_type,days,leave_days,year,freeze_days',
                'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
                'bind'       => [
                    'staff_id'   => $this->staffInfo['staff_info_id'],
                    'leave_type' => enums::LEAVE_TYPE_3,
                    'year_at'    => $this->anotherYear,
                ],
            ]);

            //申请的另外一年没额度 要初始化
            if (empty($thatRemain)) {
                [
                    $this->limitDays['other_sub'],
                    $this->limitDays['other_limit'],
                ] = $this->initSickDays($this->anotherYear);
            } else {
                $this->limitDays['other_sub']   = half_num($thatRemain->days);
                $this->limitDays['other_limit'] = half_num($thatRemain->freeze_days);
            }
        }
        return;
    }

    public function businessCheck()
    {
        //公共验证逻辑
        $this->publicValidate();
        //没权限 返回异常
        if (!$this->applyPermission($this->staffInfo)) {
            throw new ValidationException($this->getTranslation()->_('2107'));
        }
        //前天 昨天 - 未来
        $this->timeCheck();

        //没有额度记录 异常数据验证
        if (empty($this->limitDays) || !isset($this->limitDays['limit'])) {
            $this->logger->write_log("额度异常 {$this->staffInfo['staff_info_id']} " . json_encode($this->limitDays), 'info');
            throw new ValidationException($this->getTranslation()->_('leave_limit'));
        }

        //是否跨年 当前 跨后一天  或者工具补 当年跨前一年
        $startYear      = date('Y', strtotime($this->paramModel['leave_start_time']));
        $endYear        = date('Y', strtotime($this->paramModel['leave_end_time']));
        //今年请明年 申请的全是明年 的日期 还没发放等1号之后申请
        if ($this->thisYear < $startYear && $this->thisYear < $endYear) {
            throw new ValidationException($this->getTranslation()->_('not_send_yet'));
        }

        $applyDays = $this->thisNum + $this->nextNum;//申请总天数
        //说明跳过休息日之后 用0天
        if (empty($applyDays)) {
            throw new ValidationException('day off for the apply date');
        }
        $leftDays = $this->limitDays['sub'] + ($this->limitDays['other_sub'] ?? 0);
        //总额度 不够用
        $t_key = 'leave_limit';
        if(!$this->hireCheck()){
            $t_key = 'un_hire_notice_3';
            //试用期的额度 不是满额
            $leftDays = $this->limitDays['sub'];
            if($this->limitDays['limit'] < $this->unNormalDays){
                $t_key = 'leave_limit';
            }
        }

        //都加一起看够不够 没转正用
        if ($applyDays > $leftDays) {
            throw new ValidationException($this->getTranslation()->_($t_key));
        }
        if(!$this->hireCheck()){
            return true;
        }

        //当前周期不够
        if($this->thisNum > $this->limitDays['sub']){
            $this->logger->write_log("额度当前额度不够 {$this->staffInfo['staff_info_id']} " . json_encode($this->limitDays), 'info');
            throw new ValidationException($this->getTranslation()->_($t_key));
        }
        //跨年 另外一年的额度不够
        if(!empty($this->anotherYear) && $this->nextNum > $this->limitDays['other_sub']){
            $this->logger->write_log("额度另外一年额度不够 {$this->staffInfo['staff_info_id']} " . json_encode($this->limitDays), 'info');
            throw new ValidationException($this->getTranslation()->_($t_key));
        }

        return true;
    }

    //验证申请日期
    public function timeCheck()
    {
        //请假日期限制
        if (empty($this->paramModel['is_bi'])) {
            $this->timeValidate(-2, 100, 'leave_3_notice');
        }
    }

    public function dataSave()
    {
        //保存 申请 上传图片
        $this->saveImgData();
        //保存 拆分表记录
        $this->saveSplitData();
        //额度更新 当前周期 和上个周期
        $this->saveRemainData();
    }


    //申请操作 额度表 扣除带薪病假的
    protected function saveRemainData()
    {
        if (!empty($this->thisNum)) {
            $this->updateRemain($this->thisYear, $this->thisNum);
        }
        if (!empty($this->nextNum)) {
            $this->updateRemain($this->anotherYear, $this->nextNum);
        }
    }

    /**
     * @param $year
     * @param $days 如果是申请操作 为正数  撤销返还操作 为负数
     *
     */
    protected function updateRemain($year, $days)
    {
        $remain = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => $this->leaveProperty->leave_type,
                'year_at'    => $year,
            ],
        ]);

        //如果没数据 应该是明年初始化 直接保存数据后返回 不需要减去当前申请额度 初始化里面算完了
        if(empty($remain)){
            $this->initSickDays($year);
            return ;
        }

        $remain = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => $this->leaveProperty->leave_type,
                'year_at'    => $year,
            ],
        ]);

        $remain->days       = $remain->days - $days;
        $remain->leave_days = $remain->leave_days + $days;
        $remain->update();
    }



    //申请权限
    public function applyPermission($staffInfo)
    {
        //正式员工
        if ($staffInfo['formal'] == HrStaffInfoModel::FORMAL_1
            && $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_1
            && $staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF_0
        ) {
            return true;
        }

        return false;
    }


    //是否跨年
    public function isOverOneYear()
    {
        //是否跨年 当前 跨后一天  或者工具补 当年跨前一年
        $startYear      = date('Y', strtotime($this->paramModel['leave_start_time']));
        $endYear        = date('Y', strtotime($this->paramModel['leave_end_time']));

        //今年请去年 工具
        if($this->thisYear > $startYear && $this->thisYear > $endYear){
            $this->anotherYear = $startYear;
        }

        //如果是跨年申请 标记好另外一年 获取对应额度
        if ($startYear != $endYear) {
            $this->anotherYear = ($this->thisYear == $startYear) ? $endYear : $startYear;
        }
    }

    //初始化一条 对应年周期的 带薪病假额度数据  初始化任务也调用 year 有可能是去年 工具来申请的
    public function initSickDays($year,$staffInfo = [])
    {
        //任务调用 传员工信息
        if(!empty($staffInfo)){
            $this->staffInfo = $staffInfo;
        }

        //病假总额度 额度从24年开始 固化之前没有
        $freeze_days = $this->leaveProperty->days;

        $currentYear = date('Y');
        if($year < $currentYear){
            throw new ValidationException('工具申请历史数据，没有查询到对应年的额度，联系开发补数据');
        }

        //正式员工 首年 不是满额
        $hireYear   = date('Y', strtotime($this->staffInfo['hire_date']));
        $normalFlag = empty($this->isMonthCasualDate) && $currentYear == $hireYear;
        //月薪转正式首年
        $unNormalFlag = !empty($this->isMonthCasualDate) && date('Y', strtotime($this->isMonthCasualDate)) == $currentYear;
        $countDate    = $this->staffInfo['hire_date'];
        if ($unNormalFlag) {
            $countDate = $this->isMonthCasualDate;
        }
        //首年 计算 当前年就是首年
        if (($normalFlag || $unNormalFlag) && $year == $currentYear) {
            //应有额度
            $timeTmp     = strtotime("{$currentYear}-12-31") - strtotime($countDate) + (24 * 3600);
            $days        = round($timeTmp / (24 * 3600) / 365, 4) * $freeze_days;
            $freeze_days = half_num($days);

//            //没转正的正式员工 只有3天
//            $freeze_days = min($days,$this->unNormalDays);
        }

        //获取对应年已经申请的病假
        $start       = $year.'-01-01';
        $end         = $year.'-12-31';
        $appliedDays = $this->leaveDaysBetween($this->staffInfo['staff_info_id'], $start, $end, enums::LEAVE_TYPE_3);

        $insert['staff_info_id'] = $this->staffInfo['staff_info_id'];
        $insert['leave_type']    = enums::LEAVE_TYPE_3;
        $insert['year']          = $year;
        $insert['task_date']     = date('Y-m-d');
        $insert['freeze_days']   = $freeze_days;
        $insert['days']          = bcsub($insert['freeze_days'], $appliedDays,2);
        $insert['leave_days']    = $appliedDays;

        $model = new StaffLeaveRemainDaysModel();
        $model->create($insert);
        return [half_num($insert['days']), half_num($insert['freeze_days'])];
    }

    //每年1号 初始化任务用
    public function conditionsStr()
    {
        $conditions = 'formal = :formal: and hire_type = :hire_type: and is_sub_staff = :is_sub_staff:';
        $bind       = ['formal'       => HrStaffInfoModel::FORMAL_1,
                       'hire_type'    => HrStaffInfoModel::HIRE_TYPE_1,
                       'is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0,
        ];

        return [$conditions, $bind];
    }


    //任务 年1月1号 初始化一下在职人员额度数据
    public function taskInitialize($staffInfo)
    {
        $year = date('Y');
        //查询item 看有没有转正式员工日期
        $item = HrStaffItemsModel::findFirst([
            'conditions' => 'item = :code: and staff_info_id = :staff_id:',
            'bind' => ['code' => 'CONVERSION_PERMANENT_DATE', 'staff_id' => $staffInfo['staff_info_id']],
        ]);
        if($item){
            $this->isMonthCasualDate = $item->value;
        }
        $this->today = date('Y-m-d');
        $this->initSickDays($year, $staffInfo);
    }


}