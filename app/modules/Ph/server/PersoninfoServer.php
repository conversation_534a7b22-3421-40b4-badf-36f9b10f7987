<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use Exception;
use FlashExpress\bi\App\Enums\HrStaffIdentityAnnexEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateTime;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Models\backyard\FleetAllowanceModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffInfoAuditCheckModel;
use FlashExpress\bi\App\Models\backyard\StaffTaxableIncomeFormerLogModel;
use FlashExpress\bi\App\Models\backyard\StaffTaxableIncomeFormerModel;
use FlashExpress\bi\App\Models\backyard\SalaryGongZiModel;
use FlashExpress\bi\App\Modules\Ph\library\Enums\CeoMailEnums;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\AiServer;
use FlashExpress\bi\App\Server\HrShiftServer;
use FlashExpress\bi\App\Server\PersoninfoServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\ProbationServer;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Server\HrStaffInfoServer;

class PersoninfoServer extends GlobalBaseServer
{

    public function getFleetAllowance($staff_id, $month): array
    {
        $result         = [
            'all_total_amount' => 0,
            'list'             => [],
        ];
        $allowance_data = FleetAllowanceModel::find([
            'columns'    => ['date_at', 'proof_no', 'arrival_at as route_time', 'amount', 'route_name','mileage'],
            'conditions' => 'staff_info_id = :staff_info_id: and stat_month = :stat_month: and is_deleted = 0',
            'bind'       => [
                'staff_info_id' => $staff_id,
                'stat_month'    => $month,
            ],
            'order'      => 'date_at asc ,arrival_at asc',
        ])->toArray();
        if (empty($allowance_data)) {
            return $result;
        }

        $all_total_amount = 0;
        $tmp_list         = [];
        foreach ($allowance_data as $data) {
            $all_total_amount              = bcadd($all_total_amount, $data['amount'], 2);
            $tmp_list[$data['date_at']][] = $data;
        }
        $list = [];
        foreach ($tmp_list as $data_at => $item) {
            $tmp_item            = [];
            $tmp_item['date_at'] = $data_at;
            $tmp_item['total_amount'] = 0;
            foreach ($item as $item_data) {
                $tmp_item['total_amount'] = bcadd($tmp_item['total_amount'],$item_data['amount'],2) ;
                $tmp_item['detail'][]     = $item_data;
            }
            $list[] = $tmp_item;
        }
        $result['list']             = $list;
        $result['all_total_amount'] = $all_total_amount;
        return $result;
    }


    /**
     * 获取工资表明细
     * @param $paramIn
     * @return array|mixed
     * @throws \ReflectionException
     */
    public function getSalaryInfoFromHCM($paramIn)
    {
        $month             = $paramIn['month'];
        $staffId           = $paramIn['staff_id'];
        $param['month']    = $month;
        $param['staff_id'] = $staffId;

        $ac = new ApiClient('hcm_rpc', '', 'get_salary_data', $this->lang);
        $ac->setParams($param);
        $ac_result = $ac->execute();
        $this->logger->write_log("staff {$staffId} getSalaryInfoFromHCM request:" . json_encode($param,
                JSON_UNESCAPED_UNICODE) . " res:" . json_encode($ac_result, JSON_UNESCAPED_UNICODE) . " ", 'info');
        $data = [];
        if (!empty($ac_result['result']['data'])) {
            $data   = $this->salary_data_format($ac_result['result']['data']);
            $model  = SalaryGongZiModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:  and  excel_month = :excel_month:  and status = :status:',
                'bind'       => [
                    'staff_info_id' => $param['staff_id'],
                    'status'        => SalaryGongZiModel::SALARY_STATUS_PAID,
                    'excel_month'   => $param['month'],
                ],
            ]);
            $salary = $model ? $model->toArray() : [];
            if ($salary) {
                //个税
                $data['salary_tax'] = [
                    'header' => [
                        'taxable_pay'          => 'Taxable Pay',
                        'tax_amount'           => 'Tax Amount',
                        'incentive_tax'        => 'Incentive Tax',
                        'incentive_tax_refund' => 'Incentive Tax Refund',
                        'salary_tax'           => 'Salary Tax',
                    ],
                    'body'   => [
                        'taxable_pay'          => bcadd($salary['tax_base'], 0, 2),
                        'tax_amount'           => bcadd($salary['tax'], 0, 2),
                        'incentive_tax'        => bcadd($salary['incentive_tax'], 0, 2),
                        'incentive_tax_refund' => bcadd($salary['incentive_tax_rebund'], 0, 2),
                        'salary_tax'           => bcadd($salary['salary_tax'], 0, 2),
                    ],
                ];
            }
            //考勤数量统计
            $salaryAttendanceStat = $this->salaryAttendanceStat($staffId, $data['salary_cycle'], 'only_total');
            $data                 = array_merge($data, $salaryAttendanceStat);
            //工资条新增 有效无效ot 数量
            [$data['effect_num'], $data['invalid_num']] = $this->formatOtData($staffId, $data['salary_cycle']);
            $data['flash_box_url'] = env('h5_endpoint') . CeoMailEnums::FLASH_BOX_CATEGORY;
            $fleet_allowance_info = $this->getFleetAllowance($staffId, $month);
            $data['fleet_allowance'] = ['is_show' => false, 'total_amount' => 0];
            if ($fleet_allowance_info['all_total_amount'] > 0) {
                $data['fleet_allowance']['is_show']      = true;
                $data['fleet_allowance']['total_amount'] = $fleet_allowance_info['all_total_amount'];
            }
        }
        return $data;
    }



    /**
     * 格式化工资表 给前端
     * @param $salary_data
     * @return mixed
     */
    public function salary_data_format($salary_data)
    {
        if(!isset($salary_data['salary_date'])){
            return  [];
        }

        $salary_title = $this->getTranslation()->_('salary_title_1');
        $table_salary['title'] = "{$salary_title} {$salary_data['salary_date']}";
        $table_salary['staff_id'] = $salary_data['staff_id'];
        $table_salary['Name'] = $salary_data['name'];
        $table_salary['Position'] = $salary_data['position'];
        $table_salary['dep'] = $salary_data['department'];
        $table_salary['bank_no'] = $salary_data['bank_no'];
        $table_salary['date'] = $salary_data['salary_date'];
//        $table_salary['Total_Income'] = $salary_data['Total_Income']['amount'];
//        $table_salary['Total_Deduct'] = $salary_data['Total_Deduct']['amount'];
//        $table_salary['NET_INCOME'] = $salary_data['Net_Income']['amount'];
        $return_data = array_merge($salary_data,$table_salary);

        return $return_data;

    }

    /**
     * 获取个人信息
     * @Access  public
     * @Param   request
     * @Return  info
     */
    public function getPerson($paramIn = [])
    {
        $staff_id = $paramIn['staff_id'];

        $staff_model        = new StaffRepository($this->lang);
        $info               = $staff_model->getStaffPosition($staff_id);
        $returnData['data'] = [];
        if (empty($info)) {
            return $this->checkReturn($returnData);
        }

        //获取上级信息
        $ot_model     = new OvertimeRepository($this->timezone);
        $manager_id   = $ot_model->getHigherStaffId(['staff_info_id' => $staff_id]);
        $manager_id   = intval($manager_id['value']);
        $manager_info = $staff_model->getStaffPosition($manager_id);

        $staff_car_no = $staff_model->getAvatar($staff_id, 'CAR_NO');

        $person_data['name']          = $info['name'];
        $person_data['name_en']       = $info['name_en'];
        $person_data['job_number']    = $info['staff_info_id'];
        $person_data['entry_date']    = empty($info['hire_date']) ? '' : date('Y-m-d', strtotime($info['hire_date']));
        $person_data['department_id'] = $info['sys_department_id'];
        $person_data['store_id']      = $info['sys_store_id'];
        $person_data['position_id']   = $info['job_title'];
        $person_data['superior_id']   = empty($manager_info) ? '' : $manager_info['staff_info_id'];
        $person_data['superior_name'] = empty($manager_info) ? '' : $manager_info['name'];
        $person_data['id_number']     = $info['identity'];
        $person_data['mobile']        = $info['mobile'];
        $person_data['formal']        = intval($info['formal']);
        $person_data['bank_no']       = $info['bank_no'];
        $person_data['nick_name']     = $info['nick_name'];
        $person_data['hire_type']     = $info['hire_type'];
        [
            $person_data['mobile_company'],
            $person_data['is_show_mobile_company'],
        ] = self::isEditCompanyMobile($info['formal'],$info['is_sub_staff'], $info['hire_type'], $info['mobile_company']);

        //新增银行卡
        $type         = intval($info['bank_type']);
        $default_type = 2;
        $type         = empty($type) ? $default_type : $type;

        $person_data['bank_type'] = $type;
        $person_data['bank']      = $this->showBankTypeName($type);
        //菲律宾社保号,医保号,公积金号
        $person_data['fund_num']              = $info['fund_num'];
        $person_data['social_security_num']   = $info['social_security_num'];
        $person_data['medical_insurance_num'] = $info['medical_insurance_num'];

        $staff_bank_no_name            = $staff_model->getAvatar($staff_id, 'BANK_NO_NAME');
        $person_data['bank_account']   = $staff_bank_no_name;
        $person_data['email']          = $info['email'] ?? '';
        $person_data['personal_email'] = $info['personal_email'] ?? '';

        $person_data['department_name'] = $info['depart_name'];
        if ($info['sys_store_id'] != -1) {
            $store_info = $staff_model->getStaffStoreInfo($info['sys_store_id']);
        }
        $person_data['store_category'] = empty($store_info) ? '' : $store_info['category'];
        $person_data['store_name']    = empty($store_info) ? '' : $store_info['name'];
        $person_data['position_name'] = $info['job_name'];
        $person_data['car_no']        = $staff_car_no;

        //获取班次 去掉了 这字段
        $person_data['shift_info'] = '';

        //新增 职等 和职级
        $level                          = $staff_model::$level;
        $person_data['job_title_grade'] = $info['job_title_grade_v2'];
        if (empty($info['job_title_level'])) {
            $info['job_title_level'] = 1;
        }
        $person_data['job_title_level'] = $level[$info['job_title_level']] ?? '';

        $backupInfo = $staff_model->getAvatarByArgs($staff_id,
            ['BACKUP_BANK_NO', 'BACKUP_BANK_NO_NAME', 'BACKUP_BANK_TYPE', 'TAX_CARD']);
        $backupList = array_column($backupInfo, null, 'item');
        /**
         * https://flashexpress.feishu.cn/docx/Rj5Vd12XPoifoBxYGoec1OD4ned
         * 备用银行卡信息
         * 银行：默认BDO
         * 账户名称：默认员工姓名
         */
        $person_data['backup_bank_no']        = !empty($backupList['BACKUP_BANK_NO']['value']) ? trim($backupList['BACKUP_BANK_NO']['value']) : "";
        $person_data['backup_bank_no_name']   = !empty($backupList['BACKUP_BANK_NO_NAME']['value']) ? trim($backupList['BACKUP_BANK_NO_NAME']['value']) : $info['name'];
        $person_data['backup_bank_type']      = !empty($backupList['BACKUP_BANK_TYPE']['value']) ? trim($backupList['BACKUP_BANK_TYPE']['value']) : "26";
        $person_data['backup_bank_type_list'] = [];
        
        if (!empty($person_data['backup_bank_type']) && !empty($this->showBankTypeName($person_data['backup_bank_type']))) {
            $person_data['backup_bank_type_list'] = [
                [
                    "backup_bank_type" => $person_data['backup_bank_type'],
                    "backup_bank_name" => $this->showBankTypeName($person_data['backup_bank_type']),
                ],
            ];
        }

        // 税号
        $person_data['tax_card'] = !empty($backupList['TAX_CARD']['value']) ? trim($backupList['TAX_CARD']['value']) : "";

        //试用期状态，1试用期，2已通过，3未通过，4已转正
        $person_data['is_pop'] = in_array($info['status'] ?? ProbationServer::STATUS_PROBATION,
            [ProbationServer::STATUS_PROBATION, ProbationServer::STATUS_NOT_PASS]) ? 0 : 1;

        // 获取附件信息 之前调用就get_staff_identity_annex 方法
        $identity_annex = $this->getStaffAnnexInfo($paramIn, $person_data);
        //是否有底片
        $person_data['is_has_face_negatives'] = !empty((new AttendanceRepository($this->lang, $this->timeZone))->get_attendance_photo($staff_id));
        $returnData['data'] = array_merge($person_data, $identity_annex);
        return $this->checkReturn($returnData);
    }


    /**
     * ph 附件表更新为 hr_staff_annex_info
     * 该方法是get_staff_identity_annex方法的替代版本
     * @param $paramIn
     * @param array $data
     * @return array
     */
    public function getStaffAnnexInfo($paramIn, array $data = []): array
    {
        //身份证审核认证信息
        $person_data['id_card_upload_status']        = 0;   // 是否上传
        $person_data['id_card_upload_status_text']   = $this->getTranslation()->_('id_card_upload_status_0');
        $person_data['id_card_audit_status']         = '';  // 是否审核通过
        $person_data['id_card_audit_status_text']    = '';
        $person_data['id_card_file_url']             = '';  // 身份证图片地址
        $person_data['reject_reason']                = '';  // 身份证拒绝原因
        $person_data['backup_bank_card_photo']       = "";  // 备用银行卡照片
        $person_data['backup_bank_card_audit_state'] = HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD; // 备用银行卡审核状态，待上传
        $person_data['backup_bank_card_reject_reason'] = "";

        try {
            $staff_id  = $paramIn['staff_id'];
            $annexRet  = HrStaffAnnexInfoModel::find([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => ['staff_info_id' => $staff_id],
            ])->toArray();
            $annexList = array_column($annexRet, null, 'type');

            if (empty($annexList)) {
                // 附件表无数据 且 不存在备用银行卡卡号，返回待上传
                if (empty($data['backup_bank_no'])) {
                    $person_data['backup_bank_card_audit_state'] = HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
                }
                // todo 此处不return 默认返回待上传
            }

            //身份证审核认证信息
            if (isset($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD])) {
                $idCardInfo                                = $annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD];
                $person_data['id_card_upload_status']      = 1;//是否上传 0未上传 1已上传
                $person_data['id_card_upload_status_text'] = $this->getTranslation()->_('id_card_upload_status_1');
                $audit_state                               = $idCardInfo['audit_state'];//是否审核通过 0 未审核 1通过 2 未通过
                if ($audit_state == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT && $idCardInfo['reject_reason']) {
                    $person_data['reject_reason'] = $idCardInfo['reject_reason'];//驳回原因
                }
                $person_data['id_card_audit_status']      = $audit_state;
                $person_data['id_card_audit_status_text'] = $this->getTranslation()->_('id_card_audit_status_'.$audit_state);
                $person_data['id_card_file_url']          = $idCardInfo['annex_path_front'];//身份证图片地址
            }
            
            // 银行卡
            $_bank_card_audit_state = isset($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]) && !is_null($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state'])
                ? $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state']
                : null;
            // 有号码 有图片 附件状态不为null 附件状态是啥就是啥
            if (is_numeric($_bank_card_audit_state)) {
                $person_data['bank_card_audit_state']      = (int)$_bank_card_audit_state;
                $person_data['bank_card_audit_state_text'] = $this->getTranslation()->_(HrStaffAnnexInfoModel::$audit_state_key[$_bank_card_audit_state]);
            }else{
                // 默认 待上传
                $person_data['bank_card_audit_state']      = HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
                $person_data['bank_card_audit_state_text'] = $this->getTranslation()->_(HrStaffAnnexInfoModel::$audit_state_key[HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD]);
            }
            $person_data['bank_card_photo'] = $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['annex_path_front'] ?? "";
            // 拒绝时展示拒绝原因
            if ($person_data['bank_card_audit_state'] == 2 && $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['reject_reason']) {
                $person_data['bank_card_reject_reason'] = $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['reject_reason'];
            }

            // 备用银行卡的处理-这里是回写
            if (isset($annexList[HrStaffAnnexInfoModel::TYPE_BACKUP_BANK_CARD])) {
                $backBankInfo = $annexList[HrStaffAnnexInfoModel::TYPE_BACKUP_BANK_CARD];
                // 备用银行卡照片
                $person_data['backup_bank_card_photo'] = $backBankInfo["annex_path_front"] ?? "";
                if (is_numeric($backBankInfo['audit_state'])) {
                    $person_data['backup_bank_card_audit_state'] = (int) $backBankInfo['audit_state']; // 备用银行审核状态
                }
                // hcm 审核时可以清空 卡号，清空卡号是 该状态值为null了
                if (is_null($backBankInfo['audit_state'])) {
                    $person_data['backup_bank_card_audit_state'] = HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
                }
                if ($person_data['backup_bank_card_audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT && $backBankInfo['reject_reason']) {
                    $person_data['backup_bank_card_reject_reason'] = trim($backBankInfo['reject_reason']);
                }
            }

            // 医保卡、社保卡、公积金、税卡的处理
            $otherCard = [
                "social_security_info"   => [
                    'type'  => HrStaffAnnexInfoModel::TYPE_SOCIAL_SECURITY,
                    'field' => 'social_security_num',
                ],
                "provident_fund_info"    => [
                    'type'  => HrStaffAnnexInfoModel::TYPE_FUND,
                    'field' => 'fund_num',
                ],
                "medical_insurance_info" => [
                    'type'  => HrStaffAnnexInfoModel::TYPE_MEDICAL_INSURANCE,
                    'field' => 'medical_insurance_num',
                ],
                "tax_card_info"          => [
                    'type'  => HrStaffAnnexInfoModel::TYPE_TAX_CARD,
                    'field' => 'tax_card',
                ],
            ];
            foreach ($otherCard as $key => $val) {
                $fileUrl      = "";
                $rejectReason = "";
                $status       = HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
                $statusText   = $this->getTranslation()->_(HrStaffAnnexInfoModel::$audit_state_key[HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD]);
                $type         = $val['type'];
                if (isset($annexList[$type])) {
                    $fileUrl = $annexList[$type]['annex_path_front'];
                    if ($annexList[$type]['audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT && $annexList[$type]['reject_reason']) {
                        $rejectReason = $annexList[$type]['reject_reason'];
                    }
                    // hcm 审核时可以清空 卡号，清空卡号是 该状态值为null了
                    if (!is_null($annexList[$type]['audit_state'])) {
                        $status     = $annexList[$type]['audit_state'];
                        $statusText = $this->getTranslation()->_(HrStaffAnnexInfoModel::$audit_state_key[$annexList[$type]['audit_state']]);
                    }
                }
                $person_data[$key] = [
                    "card_number"   => !empty($annexList[$type]['card_number']) ? trim($annexList[$type]['card_number']) : (isset($data[$val['field']]) ? trim($data[$val['field']]) : ""),
                    'file_url'      => trim($fileUrl),
                    "reject_reason" => trim($rejectReason),
                    "status"        => (int)$status,
                    "status_text"   => $statusText,
                ];
            }
            return $person_data;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('get_staff_identity_annex ---'.$e->getMessage().'-----'.$e->getLine());
            return $person_data;
        }
    }

    /**
     * 获取员工身份证附件信息
     * @param $paramIn
     * @return mixed
     */
    public function get_staff_identity_annex($paramIn, $data = []) {
        //身份证审核认证信息
        $person_data['id_card_upload_status'] = 0;//是否上传
        $person_data['id_card_upload_status_text'] = $this->getTranslation()->_('id_card_upload_status_0');
        $person_data['id_card_audit_status'] = '';//是否审核通过
        $person_data['id_card_audit_status_text'] = '';
        $person_data['id_card_file_url'] = '';//身份证图片地址
        $person_data['reject_reason'] = '';
        $person_data['backup_bank_card_photo']       = "";   // 备用银行卡照片
        $person_data['backup_bank_card_audit_state'] = null; // 备用银行卡审核状态，默认为空

        try {
            $staff_id = $paramIn['staff_id'];
            $annexRet  = HrStaffAnnexInfoModel::find([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => ['staff_info_id' => $staff_id],
            ])->toArray();
            $annexList = array_column($annexRet, null, 'type');


            if(!empty($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD])){
                //身份证审核认证信息
                $person_data['id_card_upload_status'] = 1;//是否上传 0未上传 1已上传
                $person_data['id_card_upload_status_text'] = $this->getTranslation()->_('id_card_upload_status_1');
                $audit_state = $annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['audit_state'];//是否审核通过 0 未审核 1通过 2 未通过
                if($audit_state == HrStaffIdentityAnnexEnums::AUDIT_STATE_UN_PASSED && $annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['reject_reason']){
                    $person_data['reject_reason'] = $annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['reject_reason'];//驳回原因
                }
                $person_data['id_card_audit_status'] = $audit_state;
                $person_data['id_card_audit_status_text'] = $this->getTranslation()->_('id_card_audit_status_'.$audit_state);
                $person_data['id_card_file_url'] = $annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['annex_path_front'];//身份证图片地址
                $person_data['backup_bank_card_photo'] = $annexList[HrStaffAnnexInfoModel::TYPE_BACKUP_BANK_CARD]['annex_path_front'] ?? "";       // 备用银行卡照片
                if (is_numeric($annexList[HrStaffAnnexInfoModel::TYPE_BACKUP_BANK_CARD]['audit_state'])) {
                    $person_data['backup_bank_card_audit_state'] = (int) $annexList[HrStaffAnnexInfoModel::TYPE_BACKUP_BANK_CARD]['audit_state']; // 备用银行审核状态
                }
            }

            // 存在备用银行卡卡号，且审核状态为null，历史数据，返回待审核状态
            if (!empty($data['backup_bank_no']) && is_null($person_data['backup_bank_card_audit_state'])) {
                $person_data['backup_bank_card_audit_state'] = 0;
            }

            // 不存在备用银行卡卡号，且审核状态为null，历史数据，返回待上传
            if (empty($data['backup_bank_no']) && is_null($person_data['backup_bank_card_audit_state'])) {
                $person_data['backup_bank_card_audit_state'] = 999;
            }

            return $person_data;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('get_staff_identity_annex ---'.$e->getMessage().'-----'.$e->getLine());
            return $person_data;
        }
    }


    /**
     * @description: 是否显示前公司纳税收入项
     *             - 显示逻辑：入职日期在2022年1月1日及以后的员工
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/7/12 17:31
     */
    public function isShowFormerTaxableIncome($paramIn=[]){
        $returnData['data']['is_show_former_taxable_income'] = 0; //0 不展示 1 展示
        $compare_date =  '2022-01-01'; // 对比时间
        //查询用户 id
        try {
            $staff_id = $paramIn['staff_id'] ?? '';
            $staff_info = HrStaffInfoModel::findFirst([
                                                                            'conditions' => "staff_info_id = :staff_info_id: and hire_date >= :hire_date: ",
                                                                            'bind' => [
                                                                                'staff_info_id'  => $staff_id,
                                                                                'hire_date' =>$compare_date,
                                                                            ],
                                                                        ]);

            if(!empty($staff_info)){
                $returnData['data']['is_show_former_taxable_income'] = 1;  //展示出来
            }

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('isShowBeforeTaxableIncome ---'.$e->getMessage().'-----'.$e->getLine());
        }

        return $this->checkReturn($returnData);
    }

    /**
     * @description:获取前公司纳税收入项详情
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/7/12 19:26
     */
    public function getDetailFormerTaxableIncome($paramIn=[]){
        $staff_id = $paramIn['staff_id'] ?? '';
        $returnData=[
            'staff_info_id'=>$staff_id, //用户 id
            'pay_tax_year'=>'',//年
            'tax_amount'=>'',//金额
            'is_exist'=>0,//0 代表以前没有
        ];

        try {
            //查询用户信息
            $info = StaffTaxableIncomeFormerModel::findFirst([
                                                          'conditions' => "staff_info_id = :staff_info_id:",
                                                          'bind' => [
                                                              'staff_info_id'  => $staff_id,
                                                          ],
                                                          'order' => 'id desc',
                                                      ]);

            if(empty($info)){
                //查询用户入职日期
                $staff_info = HrStaffInfoModel::findFirst([
                                                              'conditions' => "staff_info_id = :staff_info_id: ",
                                                              'bind' => [
                                                                  'staff_info_id'  => $staff_id,
                                                              ],
                                                          ]);
                if(!empty($staff_info)){
                    $returnData['pay_tax_year'] = (string) date('Y',strtotime($staff_info->hire_date));
                }
            }else{
                $returnData = $info->toArray();
                $returnData['is_exist']=1;//存在
            }

        } catch (\Exception $e) {
            $code= ErrCode::ERROR;
            $msg=  $this->getTranslation()->_('please try again');
            $this->getDI()->get('logger')->write_log('getDetailFormerTaxableIncome ---'.$e->getMessage().'-----'.$e->getLine());
        }

        return $this->checkReturn(['data'=>$returnData,'code'=>$code ?? ErrCode::SUCCESS,'msg'=>$msg ?? '']);
    }

    /**
     * @description:保存前公司纳税收入项详情
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/7/12 19:26
     */
    public function saveFormerTaxableIncome($paramIn=[]){
        $returnData=[];
        $staff_id = $paramIn['staff_id'] ?? '';
        $tax_amount = $paramIn['tax_amount'] ?? 0; //金额
        $db = $this->getDI()->get("db");
        //开启事务
        $db->begin();
        try {
            $time =  gmdate('Y-m-d H:i:s'); //修改时间or 创建时间
            $tax_amount_before = 0;//原来的金额
            $obj = StaffTaxableIncomeFormerModel::findFirst([
                                                                 'conditions' => "staff_info_id = :staff_info_id:",
                                                                 'bind' => [
                                                                     'staff_info_id'  => $staff_id,
                                                                 ],
                                                                 'order' => 'id desc',
                                                             ]);

            if(empty($obj)){
                //查询用户入职日期
                $staff_info = HrStaffInfoModel::findFirst([
                                                              'conditions' => "staff_info_id = :staff_info_id: ",
                                                              'bind' => [
                                                                  'staff_info_id'  => $staff_id,
                                                              ],
                                                          ]);
                if(empty($staff_info)){
                    throw new \Exception('could not find it staff_info');
                }
                $obj = new StaffTaxableIncomeFormerModel();
                $obj->staff_info_id = $staff_id; //用户 id
                $obj->pay_tax_year = date('Y',strtotime($staff_info->hire_date)); //年
                $obj->created_at =$time; //创建时间
            }else{
                $tax_amount_before = $obj->tax_amount;
            }
            $obj->updated_at = $time; //修改时间
            $obj->tax_amount = $tax_amount; //金额
            $obj->save();
            //创建日志
            $obj_log = new StaffTaxableIncomeFormerLogModel();
            $obj_log->taxable_income_former_id = $obj->id; //父 id
            $obj_log->tax_amount_before =$tax_amount_before;  //原金额
            $obj_log->tax_amount_after =$obj->tax_amount;   //现金额
            $obj_log->staff_info_id = $obj->staff_info_id;   //用户 id
            $obj_log->created_at = $time; //创建时间 零时区
            $obj_log->save();
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $code= ErrCode::ERROR;
            $msg=  $this->getTranslation()->_('please try again');
            $this->getDI()->get('logger')->write_log('saveFormerTaxableIncome ---'.$e->getMessage().'-----'.$e->getLine());
        }

        return $this->checkReturn(['data'=>$returnData,'code'=>$code ?? ErrCode::SUCCESS,'msg'=>$msg ?? '']);
    }

    /**
     * @param int $type
     * @param string $resultType
     * @param array $cardType
     * @return array|void
     */
    public function checkCardType (int $type, string $resultType, array $cardType) {
        /**
         * 审核结果与需要审核的类型不符合时 给与相应的提示
         * 例子：审核公积金时，上传了一个合法的社保照片
         */
        if (isset($cardType[$type]) && $cardType[$type] != $resultType) {
            // 提示 请上传要求的证件类型
            return false;
        }
        return true;
    }

    /**
     * ph 国家证件审核接口，支持 税号、医保卡、社保卡、和公积金卡
     * 替代方法 ai_id_card_audit
     * @param array $paramIn
     * @return array
     */
    public function ai_id_card_audit($paramIn): array
    {
        $this->getDI()->get('logger')->write_log('aiIdCardAudit params:'.json_encode($paramIn), 'info');
        $staff_id    = $paramIn['staff_id'];
        $id_card_url = $paramIn['file_url'] ?? '';
        $type        = $paramIn['type'];    // 1、身份证审核 2、备用银行卡审核 3、社保号审核 4、公积金审核 5、医疗保险号审核 6、税号审核
        $card_number = trim($paramIn['card_number']);
        $nationality = false;   // 默认不走ai审核

        // 查询员工国籍、税号
        $hr_staff_item = HrStaffItemsModel::find([
            'conditions' => "staff_info_id = :staff_info_id: and item IN ('NATIONALITY','TAX_CARD')",
            'bind'       => [
                'staff_info_id' => $staff_id,
            ],
        ])->toArray();
        $hr_staff_list = array_column($hr_staff_item, null, 'item');
        if ((int)$hr_staff_list['NATIONALITY']['value'] == HrStaffInfoModel::WORKING_COUNTRY_PH) {
            // 如果是菲律宾国籍需求走ai审核逻辑
            $nationality = true;
        }

        // 返回结构
        $error_msg          = '';
        $returnData['data'] = [
            'code'   => 0,  // 0 不可提交  1 可提交
            'msg'    => $error_msg,
            'result' => [
                'status'        => 2,   // 1 表示审核通过 2 表示审核失败 默认给审核失败，成功时会修改这个值
                'type'          => $type,
                'card_number'   => $card_number,
                'file_url'      => $id_card_url,
            ],
        ];

        $maxUploadCount = 3; //4;
        $cardType       = [
            HrStaffAnnexInfoModel::TYPE_SOCIAL_SECURITY   => 'social-security-system-card',  // 社保
            HrStaffAnnexInfoModel::TYPE_FUND              => 'pag-ibig-card',                // 公积金
            HrStaffAnnexInfoModel::TYPE_MEDICAL_INSURANCE => 'phil-health-card',             // 医疗保险
            HrStaffAnnexInfoModel::TYPE_TAX_CARD          => 'tax-id-card',                  // 税卡号
        ];
        $cardTypeKeys   = array_keys($cardType);
        $interceptType  = ['pag-ibig-card', 'social-security-system-card', 'pro-id-card'];
        if (in_array($type, $cardTypeKeys)) {
            // 税号、医保、公积金、和社保只需上传审核一次即可
            $maxUploadCount = 1;
            array_shift($interceptType);
            array_shift($interceptType);
        }

        // 非菲律宾国家不走ai审核
        if (!$nationality) {
            $returnData['data']['code']                   = 1;
            $returnData['data']['result']['status']       = 2;
            $returnData['data']['result']['upload_count'] = (HrStaffAnnexInfoModel::TYPE_ID_CARD == $type) ? 4 : 1;
            $returnData['data']['result']['file_url']     = $id_card_url;
            $returnData['data']['result']['msg']          = $this->getTranslation()->_('id_card_ai_audit_error_6');
            $this->getDI()->get('logger')->write_log('非菲律宾籍 aiIdCardAudit 结果:'.json_encode($returnData), 'info');
            return $this->checkReturn($returnData);
        }

        // AI 审核逻辑处理
        $redis     = $this->getDI()->get('redisLib');
        $redis_key = 'STAFF_AI_ID_CARD_UPLOAD_COUNT_'.$staff_id.'_'.$type;
        $redis->expire($redis_key, 30 * 60);
        $upload_count = $redis->incr($redis_key);
        $returnData['data']['result']['upload_count'] = in_array($type, $cardTypeKeys) ? 1 : $upload_count;
        $internal_img_url = $id_card_url;
        if (RUNTIME != 'dev') {
            // 公网转内网地址
            $settingEnv = (new \FlashExpress\bi\App\Server\SettingEnvServer())->listByCode([
                "server_point",
                "server_point_internal",
            ]);
            if (!empty($settingEnv)) {
                $log                     = [
                    "staff_info_id"    => $staff_id,
                    "img_url"          => $id_card_url,
                    "internal_img_url" => "",
                ];
                $set_values              = array_column($settingEnv, null, 'code');
                $internal_img_url        = str_ireplace($set_values['server_point']['set_val'],
                    $set_values['server_point_internal']['set_val'], $id_card_url);
                $log["internal_img_url"] = $id_card_url;
                $this->getDI()->get('logger')->write_log('ph-公网地址转内网地址 result'.json_encode($log), 'info');
            }
        }

        // 调用ai接口进行验证
        $post_result = $this->ai_id_card_ocr_post($internal_img_url);
        $ai_result   = $post_result['data'] ?? [];
        $result = $ai_result['result'] ?? [];
        // ai识别记录结果
        $auditCheckInfo = StaffInfoAuditCheckModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and type = :type: ',
            'bind'       => [
                'staff_info_id' => $staff_id,
                'type'          => $type,
            ],
        ]);
        if (empty($auditCheckInfo)) {
            $auditCheckInfo = new StaffInfoAuditCheckModel();
        }
        $auditCheckInfo->staff_info_id   = $staff_id;
        $auditCheckInfo->type            = $type;
        // 查询员工的基本信息
        $infoObj = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $staff_id,
            ],
        ]);
        $info    = [];
        if (!empty($infoObj)) {
            $info         = $infoObj->toArray();
            $info['name'] = trim(strtoupper($info['name']));
            // “姓”+“名”+“中间名”
            $info['splice_name'] = strtoupper(trim($info['last_name']).' '.trim($info['first_name']).' '.trim($info['middle_name']));
        }
        // 如果 没与查询到数据，则直接返回
        if (empty($info)) {
            $this->getDI()->get('logger')->write_log(__CLASS__.' function: ph-ai_id_card_audit 查询员工信息为空，结果:'.json_encode([
                    'staff_info_id' => $staff_id,
                ]), 'info');
            $returnData['data']['msg'] = $this->getTranslation()->_('id_card_ai_audit_error_4');
            return $this->checkReturn($returnData);
        }
        //获取生日
        $staffItems = (new StaffRepository())->getSpecStaffItemsInfo($staff_id, ['BIRTHDAY']);
        $info['birthday'] = !empty($staffItems['BIRTHDAY']) ? $staffItems['BIRTHDAY'] : '';
        $info['card_number'] = $card_number;
        $method = '';
        switch ($type) {
            case HrStaffAnnexInfoModel::TYPE_SOCIAL_SECURITY:
                $method = 'aiCompareSocialSecuritySystemCard';
                break;
            case HrStaffAnnexInfoModel::TYPE_FUND:
                $method = 'aiComparePagIbigCard';
                break;
            case HrStaffAnnexInfoModel::TYPE_MEDICAL_INSURANCE:
                $method = 'aiComparePhilHealthCard';
                break;
            case HrStaffAnnexInfoModel::TYPE_TAX_CARD:
                $method = 'aiCompareTaxIdCard';
                break;
            case HrStaffAnnexInfoModel::TYPE_ID_CARD:
                $method = 'aiCompareIdCard';
                break;
            default:
                break;
        }
        $compare_id_card = (new AiServer())->ai_compare([
            'ai_data'    => $result,
            'staff_info' => $info,
            'method' => $method,
        ]);
        
        $auditCheckInfo->ai_recognition_data = $compare_id_card ? json_encode($compare_id_card, JSON_UNESCAPED_UNICODE) : '';
        if ($auditCheckInfo->save() !== true) {
            $this->getDI()->get('logger')->write_log('staff_info_audit_check 保存失败 staff_info_id : ' . $staff_id,
                'error');
        }
        // 这里之前顺序错了 调整下顺序
        if ($upload_count > $maxUploadCount) {
            //未通过审核
            $msg                = $this->getTranslation()->_('id_card_ai_audit_error_5');
            $returnData['data'] = [
                'code'   => 1,
                'msg'    => '',
                'result' => [
                    'status'       => 2,
                    'upload_count' => $returnData['data']['result']['upload_count'],
                    'file_url'     => $id_card_url,
                    'msg'          => $msg,
                    'type'         => $type,
                    'card_number'  => $card_number,
                ],
            ];
            return $this->checkReturn($returnData);
        }
        
        $this->getDI()->get('logger')->write_log('ph-ai审核审核结果 result：'.json_encode($ai_result).' 参数: '.json_encode([
                'staff_info_id' => $staff_id,
                'id_card_url'   => $internal_img_url,
            ]), 'info');

        // todo 方便测试 核对结果
        $returnData['data']['result']['ai_result'] = json_encode($ai_result, JSON_UNESCAPED_UNICODE);
        if (empty($ai_result)) {
            // 请求超时
            return $this->checkReturn(-3, $this->getTranslation()->_('id_card_ai_request_error'));
        }

        // ai 返回错误
        if (strtoupper($ai_result['status']) == 'ERROR') {
            switch ($ai_result['error']['code']) {
                case 'IMAGE_SIZE_EXCEED':
                    // 图片尺寸过大，请上传10M以内的图片
                    $error_msg = $this->getTranslation()->_('id_card_ai_audit_error_1');
                    break;
                case 'SIGNATURE_FAILURE':
                case 'MISSING_PARAMETER':
                case 'INPUT_ERROR':
                case 'INTERNAL_ERROR':
                case 'IMAGE_DOWNLOAD_ERROR':
                    //'未检测到证件，请保证证件无遮挡';
                    $error_msg = $this->getTranslation()->_('id_card_ai_audit_error_9');
                    break;
                case 'NO_TARGET_DETECTED':
                    // 请按照规范要求上传证件
                    $returnData['data']['code'] = 1;    // 允许提交
                    $error_msg = $this->getTranslation()->_('id_card_ai_audit_error_7');
                    $returnData['data']['result']['msg'] = $error_msg;
                    break;
                default:
                    $error_msg = $this->getTranslation()->_('id_card_ai_audit_error_10');//'未检测到证件，请重新上传';
            }
            $returnData['data']['msg'] = $error_msg;
            return $this->checkReturn($returnData);
        }
        if (in_array($result['document_type'], $interceptType)) {
            // document_type = "pag-ibig-card"时 返回的证件类型为公积金卡
            // document_type = "social-security-system-card"返回的证件类型为社保卡
            // document_type = "pro-id-card" 返回的证件类型为职业卡
            // 提示 请上传要求的证件类型
            $returnData['data']['msg'] = $this->getTranslation()->_('id_card_ai_audit_error_8');
            return $this->checkReturn($returnData);
        }

        if (in_array($type, $cardTypeKeys)) {
            $checkResult = $this->checkCardType($type, $result['document_type'], $cardType);
            if (!$checkResult) {
                $returnData['data']['msg'] = $this->getTranslation()->_('id_card_ai_audit_error_8');
                return $this->checkReturn($returnData);
            }
        }
        
        // ai 审核通过
        if (!empty($compare_id_card['is_pass'])) {
            // 审核通过 清除 key
            $redis->delete($redis_key);

            //通过审核
            $returnData['data']['code']                   = 1;
            $returnData['data']['msg']                    = '';
            $returnData['data']['result']['status']       = 1;
            $returnData['data']['result']['upload_count'] = 1;
            $returnData['data']['result']['file_url']     = $id_card_url;
            $returnData['data']['result']['msg']          = $this->getTranslation()->_('id_card_ai_audit_success');
        }

        // ai 审核未通过
        if (empty($compare_id_card['is_pass'])) {
            //未通过审核
            $msg = $this->getTranslation()->_('id_card_ai_audit_error_5');
            if ($type == HrStaffAnnexInfoModel::TYPE_ID_CARD) {
                // 身份证审核前三次审核失败的提示
                $msg = $this->getTranslation()->_('id_card_ai_audit_error_4');
            }
            $returnData['data']['code']                   = 1;
            $returnData['data']['msg']                    = '';
            $returnData['data']['result']['status']       = 2;
            $returnData['data']['result']['upload_count'] = $upload_count;
            $returnData['data']['result']['file_url']     = $id_card_url;
            $returnData['data']['result']['msg']          = $msg;
        }

        // 存储到redis中
        $redis_id_card_key = 'STAFF_AI_ID_CARD_AUDIT_'.$staff_id.'_'.$type;
        $redis->set($redis_id_card_key, json_encode($returnData['data']), 600);
        $this->getDI()->get('logger')->write_log('ph-aiIdCardAudit 结果:'.json_encode($returnData), 'info');
        return $this->checkReturn($returnData);
    }

    /**
     * 菲律宾国家证件提交
     * 替换ai_id_card_submit方法
     * 菲律宾照片提交
     * @param $paramIn
     * @return array
     */
    public function ai_id_card_submit($paramIn): array
    {
        $this->getDI()->get('logger')->write_log('aiIdCardSubmit params:'.json_encode($paramIn), 'info');
        $ai_status   = $paramIn['status'] ?? HrStaffAnnexInfoModel::AI_AUDIT_STATE_REJECT;
        $staff_id    = $paramIn['staff_id'];
        $file_url    = $paramIn['file_url'];
        $card_number = isset($paramIn['card_number']) ? trim($paramIn['card_number']) : '';
        $type        = $paramIn['type'];

        // 从附件信息表查询身份证信息
        $annexInfo = HrStaffAnnexInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: AND type = :type:',
            'bind'       => [
                'staff_info_id' => $staff_id,
                // 附件类型：身份证
                'type'          => $type,
            ],
        ]);

        //记录审核前状态
        $audit_before_state = $annexInfo->audit_state ?? HrStaffAnnexInfoModel::AUDIT_STATE_NOT_REVIEWED;

        // 身份证、如果存在 并且已经审核通过
        if (!empty($annexInfo) && $annexInfo->audit_state == HrStaffAnnexInfoModel::AUDIT_STATE_PASSED && $type == HrStaffAnnexInfoModel::TYPE_ID_CARD) {
            return $this->checkReturn(-3, '通过审核的不需要再进行上传');
        }

        // 如果未找到数据
        if (empty($annexInfo)) {
            $annexInfo                = new HrStaffAnnexInfoModel();
            $annexInfo->staff_info_id = $staff_id;
            $annexInfo->type          = $type;
        }

        $redis              = $this->getDI()->get('redisLib');
        $redis_id_card_key  = 'STAFF_AI_ID_CARD_AUDIT_'.$staff_id.'_'.$type;
        $redis_id_card_info = $redis->get($redis_id_card_key);
        if (!empty($redis_id_card_info)) {
            $redis_id_card_info = json_decode($redis_id_card_info, true);
            $ai_status          = $redis_id_card_info['result']['status'];
            $file_url           = $redis_id_card_info['result']['file_url'];
            $redis->delete($redis_id_card_key);
        }

        //todo 到岗H5完善信息页面，只有身份证证照片，没有身份证号，没有传递时，从员工信息表中读取，落到审核表
        if (empty($card_number) && $type == HrStaffAnnexInfoModel::TYPE_ID_CARD) {
            $staffObj = (new HrStaffInfoServer($this->lang, $this->timeZone))->getUserInfoByStaffInfoId($staff_id,
                'identity,social_security_num,fund_num,medical_insurance_num');
            // 身份证
            $card_number = $staffObj->identity ? trim($staffObj->identity) : "";
        }

        $annexInfo->annex_path_front = $file_url;
        if ($card_number) {
            $annexInfo->card_number = $card_number;
        }
        $annexInfo->ai_audit_state      = $ai_status;
        $annexInfo->reject_reason      = '';
        $annexInfo->ai_audit_state_date = date('Y-m-d H:i:s');
        // 如果 ai审核通过，则 人工审核标记为通过
        $annexInfo->audit_state = ($ai_status == HrStaffAnnexInfoModel::AI_AUDIT_STATE_PASSED) ? HrStaffAnnexInfoModel::AUDIT_STATE_PASSED : HrStaffAnnexInfoModel::AUDIT_STATE_NOT_REVIEWED;
        if ($annexInfo->audit_state == HrStaffAnnexInfoModel::AUDIT_STATE_PASSED){
            $annexInfo->audit_state_date = date('Y-m-d H:i:s');
            $annexInfo->audit_staff_info_id = 10000;
        }else{
            $annexInfo->audit_state_date = null;
            $annexInfo->audit_staff_info_id = null;
        }
        // 附件信息存储
        if ($annexInfo->save() !== true) {
            return $this->checkReturn(-3, 'error');
        }

        $auditCheckInfo = StaffInfoAuditCheckModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and type = :type: ',
            'bind'       => [
                'staff_info_id' => $staff_id,
                'type'          => $type,
            ],
        ]);
        if (!empty($auditCheckInfo) && !empty($auditCheckInfo->ai_recognition_data)) {
            $auditCheckInfo->ai_recognition_end_data = $auditCheckInfo->ai_recognition_data;
            if ($auditCheckInfo->save() !== true) {
                $this->getDI()->get('logger')->write_log('staff_info_audit_check 保存失败 staff_info_id : '.$staff_id,'error');
            }
        }
        
        // 非菲律宾国籍 不走ai审核，同事也不插入审核日志
        $hr_staff_item = HrStaffItemsModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and item = 'NATIONALITY'",
            'bind'       => [
                    'staff_info_id' => $staff_id,
            ],
        ]);
        if (isset($hr_staff_item->value) && $hr_staff_item->value == HrStaffInfoModel::WORKING_COUNTRY_PH) {
            // 记录审核日志
            $this->addAiAuditLog($staff_id, $audit_before_state, $ai_status, $type);
        }
        return $this->checkReturn([]);
    }

    /**
     * 记录审核日志
     * @param $staff_id
     * @param $audit_before_state
     * @param $status
     * @param $type
     */
    public function addAiAuditLog($staff_id, $audit_before_state, $status, $type)
    {
        $audit_log        = [
            'staff_info_id'      => $staff_id,
            'audit_id'           => 10000,
            'audit_name'         => $this->getTranslation()->_('ai_audit'),
            'audit_before_state' => $audit_before_state,
            'audit_after_state'  => $status,
            'type'               => (int)$type,
        ];
        $audit_log_result = $this->getDI()->get('db')->insertAsDict('staff_identity_annex_audit_log', $audit_log);
        if (!$audit_log_result) {
            $msg = "identity annex audit log insert fail:".var_export($audit_log, true).PHP_EOL;
            $this->getDI()->get("logger")->write_log($msg, 'info');
        }
    }

    /**
     * ph 国家附件表更新为hr_staff_annex_info
     * @param $params
     * @return bool
     */
    public function modifyHrStaffIdentityAnnex($params)
    {
        // 查询银行卡信息是否存在
        $annexInfo = HrStaffAnnexInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: AND type = :type:',
            'bind'       => [
                'staff_info_id' => $params['staff_id'],
                'type'          => HrStaffAnnexInfoModel::TYPE_BANK_CARD,
            ],
        ]);

        // 如果未查询到该用户信息则新建
        if (empty($annexInfo)) {
            $annexInfo                = new HrStaffAnnexInfoModel();
            $annexInfo->staff_info_id = $params['staff_id'];
            $annexInfo->type = HrStaffAnnexInfoModel::TYPE_BANK_CARD;
        }

        // 银行卡照片
        $annexInfo->annex_path_front = trim($params['bank_card_photo']);
        // 银行卡卡号
        $annexInfo->card_number = trim($params['bank_no']);
        // 人工审核状态
        $annexInfo->audit_state = HrStaffAnnexInfoModel::AUDIT_STATE_NOT_REVIEWED;        // 待审核
        if ($annexInfo->save() !== true) {
            throw new Exception('HrStaffAnnexInfoModel update error ');
        }
        return true;
    }
}
