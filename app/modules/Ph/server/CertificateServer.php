<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/5/28
 * Time: 下午2:15
 */


namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class CertificateServer extends BaseServer
{

    public $timezone;

    public function __construct($lang = 'th', $timezone)
    {
        $this->timezone = $timezone;
        $this->lang     = $lang;
        parent::__construct($this->lang);
    }


    //获取 预览证明下载所需的数据 用于在职证明和薪资证明
    public function get_pdf_data($param)
    {
        $sub_type   = empty($param['sub_type']) ? 0 : intval($param['sub_type']);
        $staff_id   = $param['staff_id'];
        $staff_re   = new StaffRepository($this->lang);
        $staff_info = $staff_re->getStaffPosition($staff_id);

        $sql         = " select * from hr_staff_salary where staff_info_id = {$staff_id} ";
        $salary_info = $this->getDI()->get('db_rby')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);

        $pdf_data['base_salary'] = round($salary_info['base_salary'] / 100, 2);
        $pdf_data['name']        = preg_replace("/\(.*\)/", "", $staff_info['name']);
        $pdf_data['name_en']     = empty($staff_info['name_en']) ? $staff_info['name'] : $staff_info['name_en'];

        $pdf_data['job_title_name']  = $staff_info['job_name'];
        $pdf_data['department_name'] = $staff_info['depart_name'];

        $pdf_data['hire_date'] = date('Y-m-d', strtotime($staff_info['hire_date']));

        $current_month            = intval(date('m', time()));
        $pdf_data['current_date'] = date('d').' '.StaffRepository::$month_en[$current_month].' '.date('Y');

        $pdf_data['en_money'] = $pdf_data['split_money'] = '';
        if (!empty($pdf_data['base_salary'])) {
            //英文钱 字符串
            $pdf_data['en_money'] = num2Word($pdf_data['base_salary'], 'peso');
            //英泰文 数字钱  千分位
            $pdf_data['split_money'] = number_format($pdf_data['base_salary']);
        }
        $pdf_data['use_type_text']        = empty(enums::$use_type[$sub_type]) ? '' : enums::$use_type[$sub_type];
        $pdf_data['staff_info_id']        = $staff_id;
        $pdf_data['sub_type']             = $this->get_sub_type();
        $pdf_data['email']                = $staff_info['email'];
        $pdf_data['personal_email']       = $staff_info['personal_email'];
        
        //[1]入参
        $ac = new ApiClient('hcm_rpc', '', 'get_staff_certificate_info' ,$this->lang);
        $ac->setParams([
            "staff_info_id" => $staff_id,
        ]);
        $ac_result = $ac->execute();
        if($ac_result["result"]['code'] == 1) {
            //整理数据
            $data = $ac_result["result"]['data'];

        } else {
            //记日志 获取薪资信息失败u
            $this->logger->write_log("get_staff_certificate_info_failed : {$staff_id}", 'notice');
        }

        //代表人信息
        $pdf_data['proveDownloadSetting']['url'] = $data['labor_sign_url'] ?? '';
        $pdf_data['proveDownloadSetting']['name'] = $data['labor_name'] ?? '';
        $pdf_data['proveDownloadSetting']['job_title'] = $data['labor_job_title'] ?? '';

        // 公司信息
        $pdf_data['header_company_name']       = $data['header_company_name'] ?? '';
        $pdf_data['header_logo_url']           = $data['header_logo_url'] ?? '';
        $pdf_data['footer_content']            = $data['footer_content'] ?? '';
        $pdf_data['content_company_name']      = $data['content_company_name'] ?? '';
        $pdf_data['company_phone']             = $data['company_phone'] ?? '';

        return $pdf_data;
    }


    //菲律宾证明下载新增枚举
    protected function get_sub_type()
    {
        $type_data = enums::$use_type;
        $data      = [];
        foreach ($type_data as $k => $v) {
            $row['code'] = $k;
            $row['name'] = $v;
            $data[]      = $row;
        }
        return $data;
    }


}