<?php
/**
 * Author: Bruce
 * Date  : 2024-02-25 21:55
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Ph\Server\Osm;


use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyModel;
use FlashExpress\bi\App\Models\backyard\StaffHikvisionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\HubOutsourcingOvertimeDetailRepository;
use FlashExpress\bi\App\Repository\HubOutsourcingOvertimeRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\Osm\OutsourcingOrderServer;
use FlashExpress\bi\App\Server\Osm\OutsourcingOTServer as GlobalBaseServer;


class OutsourcingOTServer  extends GlobalBaseServer
{

    /**
     * 获取外协员工加班审批单
     * @param array $params
     * @param array $user_info
     * @return void
     */
    public function getOutsourcingOTList(array $params, array $user_info)
    {
        $params['page']      = empty($params['page']) ? 1 : $params['page'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size'] = ($params['page_size'] > 100) ? 100 : $params['page_size'];
        $params['company_id'] = $user_info['id'];

        $result = [
            'total'     => 0,
            'cur_page'  => (int)$params['page'],
            'page_size' => (int)$params['page_size'],
            'list'      => [],
        ];

        // 查询外协公司id是否合法
        $outsourcing_company_model = new OutsourcingCompanyModel();
        $outsourcing_company       = $outsourcing_company_model->getOneById($params['company_id']);
        if (empty($outsourcing_company) || $outsourcing_company['deleted'] != 0) {
            $this->getDI()->get('logger')->write_log('outsoucing-company-login-no-exists'.json_encode(['company_id' =>  $params['company_id']]),
                'info');
            throw new ValidationException($this->getTranslation()->_('outsourcing_company_no_exists'),
                ErrCode::VALIDATE_ERROR);
        }

        $count_columns = ['COUNT(*) AS count'];
        $result_count  = $this->getOutsourcingOTQuery($params, $count_columns, true);
        if ($result_count['count'] == 0) {
            return $result;
        }

        $count_list  = [
            'id',
            'store_id',
            'ot_date',
            'start_time',
            'end_time',
            'duration',
            'demand_num',
            'osm_state',
            'img',
        ];
        $result_list = $this->getOutsourcingOTQuery($params, $count_list);
        $store_ids   = array_values(array_unique(array_column($result_list, 'store_id')));
        $sys_store_list = [];
        if (!empty($store_ids)) {
            $sys_store_arr  = SysStoreModel::find([
                'conditions' => 'id in ({ids:array}) ',
                'bind'       => ['ids' => $store_ids],
                'columns'    => "id,name",
            ])->toArray();
            $sys_store_list = array_column($sys_store_arr, null, 'id');
        }
        foreach ($result_list as $key => &$val) {
            $val['id']             = (int)$val['id'];
            $val['demand_num']     = (int)$val['demand_num'];
            $val['osm_state']      = (int)$val['osm_state'];
            $val['ot_title']       = isset($sys_store_list[$val['store_id']]) ? $sys_store_list[$val['store_id']]['name'] :"";
            $b_time                = date("H:i", strtotime($val['start_time']));
            $e_time                = date("H:i", strtotime($val['end_time']));
            $val['ot_time']        = $b_time.'-'.$e_time;
            $val['osm_state_text'] = $this->getTranslation()->_(HubOutsourcingOvertimeModel::$osm_state_text[$val['osm_state']]);
            $val['img']            = json_decode($val['img']);
            unset($val['start_time'], $val['end_time']);
        }
        $result['total'] = (int)$result_count['count'];
        $result['list']  = $result_list;
        return $result;
    }

    /**
     * sql 查询
     * @param array $params
     * @param array $columns
     * @param bool $isCount
     * @return array
     */
    public function getOutsourcingOTQuery(array $params, array $columns = [], bool $isCount = false): array
    {
        if (empty($params)) {
            return [];
        }
        $str_column = empty($columns) ? '*' : implode(',', $columns);
        $builder    = $this->modelsManager->createBuilder();
        $builder->columns($str_column);
        $builder->from(['o' => HubOutsourcingOvertimeModel::class]);
        $builder = $this->getBuilderWhere($builder, $params);
        if ($isCount) {
            $builder->columns($str_column);
            return $builder->getQuery()->getSingleResult()->toArray();
        }

        if (empty($params['is_all'])) {
            $builder->limit($params['page_size'], $params['page_size'] * ($params['page'] - 1));
        }
        $builder->orderBy('id DESC');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 生成查询条件
     * @param object $builder
     * @param array $params
     * @return object
     */
    public function getBuilderWhere(object $builder, array $params): object
    {
        // osm状态
        if (isset($params['osm_state']) && !empty($params['osm_state'])) {
            $builder->andWhere('osm_state = :osm_state:', ['osm_state' => (int)$params['osm_state']]);
        }

        // 外协公司id
        if (isset($params['company_id']) && !empty($params['company_id'])) {
            $builder->andWhere('outsourcing_company_id = :outsourcing_company_id:',
                ['outsourcing_company_id' => (int)$params['company_id']]);
        }

        // 加班日期
        if (isset($params['ot_date']) && !empty($params['ot_date'])) {
            $builder->andWhere('ot_date = :ot_date:', ['ot_date' => $params['ot_date']]);
        }

        if (isset($params['start_time']) && !empty($params['start_time']) && isset($params['end_time']) && !empty($params['end_time'])) {
            // 1、db中的开始时间落在了传入时间区间的中间
            // 2、db中的结束时间落在了传入时间区间的中间
            // 3、传入的时间区间吧db的开始和结束时间包裹起来了
            // 4、db的时间区间把传入的时间区间包裹起来了
            if (isset($params['source']) && $params['source'] == 'order') {
                $builder->andWhere('(start_time >= :start_time: AND start_time < :end_time:)
            OR (end_time > :start_time: AND end_time <= :end_time:)
            OR (start_time >= :start_time: AND end_time <= :end_time:) 
            OR (start_time <= :start_time: AND end_time >= :end_time:)',
                    ['start_time' => $params['start_time'], 'end_time' => $params['end_time']]);
            } else {
                $builder->andWhere('(start_time >= :start_time: AND start_time <= :end_time:)
            OR (end_time >= :start_time: AND end_time <= :end_time:)
            OR (start_time >= :start_time: AND end_time <= :end_time:) 
            OR (start_time <= :start_time: AND end_time >= :end_time:)',
                    ['start_time' => $params['start_time'], 'end_time' => $params['end_time']]);
            }
        }

        // 审批状态
        if (isset($params['apply_state']) && is_array($params['apply_state']) && !empty($params['apply_state'])) {
            $builder->andWhere('apply_state IN ({apply_state:array})', ['apply_state' => $params['apply_state']]);
        }

        return $builder;
    }

    /**
     * osm 配置外协加班人员 提交加班审批单
     * @param array $params
     * @return array
     */
    public function submitOutsourcingOT(array $params): array
    {
        $ot_id     = $this->processingDefault($params, 'id', 2);
        $staff_ids = $this->processingDefault($params, 'staff_ids', 3);

        // 1、根据osm传入的id 查询是否存在此加班申请单
        $outsourcing_ot_info = (new HubOutsourcingOvertimeRepository($this->timeZone))->getOutsourcingOTById($ot_id);
        if (empty($outsourcing_ot_info)) {
            // 未找到有效外协加班申请
            return $this->checkReturn(-3, $this->getTranslation()->_('os_ot_data_empty'));
        }
        $apply_staff_id = $outsourcing_ot_info['apply_staff_id'];

        // 查询当前订单是否配置过外协员工
        $outsourcing_ot_detail_info = (new HubOutsourcingOvertimeDetailRepository($this->timeZone))->getOutsourcingOTDetailByOtId($ot_id);
        if (!empty($outsourcing_ot_detail_info)) {
            // 相同外协加班申请不能重复配置
            return $this->checkReturn(-3, $this->getTranslation()->_('os_ot_data_repeat'));
        }

        // todo 需要添加对外协员工的过滤 验证是否在相同时间段有加班数据，是否和外协订单的时间有冲突等
        $os_ot_where              = [
            'company_id' => $outsourcing_ot_info['outsourcing_company_id'],
            'start_time' => $outsourcing_ot_info['start_time'],
            'end_time'   => $outsourcing_ot_info['end_time'],
        ];
        $use_staff_info_ids       = (new OutsourcingOTServer($this->lang,
            $this->timeZone))->getOutsourcingOTIntersection($os_ot_where);


        // 2、获取外协员工工单与当前选择加班时间有交接的员工
        $outsourcing_order_params = [
            'out_company_id'  => $outsourcing_ot_info['outsourcing_company_id'], // 1,//
            'employment_date' => $outsourcing_ot_info['ot_date'],                // todo 测试 '2023-02-07',//
            'start_time'      => $outsourcing_ot_info['start_time'],             // '2023-02-07 18:30:00',//
            'end_time'        => $outsourcing_ot_info['end_time'],               // '2023-02-07 17:00:00',//
        ];
        $order_use_staff_info_ids = (new OutsourcingOrderServer($this->lang))->getUsingStaffInfoV2($outsourcing_order_params);
        // 3、两种有交集的员工做合集并去重
        $use_staff_info_ids = array_values(array_unique(array_merge($use_staff_info_ids, $order_use_staff_info_ids)));

        //如果提交的 工号与未被释放的工号，有交集
        if (array_intersect($staff_ids, $use_staff_info_ids)) {
            throw new ValidationException($this->getTranslation()->_('isset_using_staff'));
        }

        //OutsourcingCompanyStaffHikvisionModel
        $outsourcing_staff_list = (new StaffHikvisionModel())->getOutsourcingStaffByStaffAndCompanyId($staff_ids,
            $outsourcing_ot_info['outsourcing_company_id']);
        if (count($outsourcing_staff_list) < count($staff_ids)) {
            // 请选择正确的外协员工
            // 查询出的数据 如果比 传递的数据少，说明传递的数据中一定有不属于当前外协公司的数据
            return $this->checkReturn(-3, $this->getTranslation()->_('os_ot_staff_error'));
        }

        $serialNo = $this->getID();
        // db 操作
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            // 组织插入数据
            $insert_os_ot_detail = [];
            foreach ($staff_ids as $key => $val) {
                $insert_os_ot_detail[$key] = [
                    'serial_no'                   => !empty($serialNo) ? trim($serialNo) : '',
                    'hub_outsourcing_overtime_id' => (int)$ot_id,
                    'staff_id'                    => (int)$val,
                ];
            }
            (new HubOutsourcingOvertimeDetailRepository($this->timeZone))->batch_insert('hub_outsourcing_overtime_detail',
                $insert_os_ot_detail);

            // 更新主表状态
            $update_data = [
                'serial_no'   => !empty($serialNo) ? trim($serialNo) : '',
                // 审核中
                'osm_state'   => HubOutsourcingOvertimeModel::OSM_STATE_AUDIT,
                // 待审批
                'apply_state' => enums::APPROVAL_STATUS_PENDING,
            ];
            (new HubOutsourcingOvertimeRepository($this->timeZone))->updateOutsourcingOTById($update_data, $ot_id);

            $extend = [
                'store_id'     => $outsourcing_ot_info['store_id'],
                'AM_store_ids' => [$outsourcing_ot_info['store_id']],
            ];
            //创建
            $server    = new ApprovalServer($this->lang, $this->timeZone);
            $requestId = $server->create($ot_id, AuditListEnums::APPROVAL_TYPE_OUTSOURCING_OT, $apply_staff_id, null, $extend);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $this->logger->write_log("add_outsourcingOvertime 审批流创建异常 {$apply_staff_id}".$e->getMessage());
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }
        return $this->checkReturn(['data' => ['os_ot_id' => $ot_id]]);
    }


}