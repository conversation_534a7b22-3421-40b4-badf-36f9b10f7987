<?php

namespace FlashExpress\bi\App\Modules\Ph\Server\Osm;

use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeModel;
use FlashExpress\bi\App\Models\backyard\StaffHikvisionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Modules\Ph\enums\OsmEnums;
use FlashExpress\bi\App\Server\Osm\OutsourcingStaffServer AS GlobalBaseServer;

class OutsourcingStaffServer extends GlobalBaseServer
{
    /**
     * 获取枚举数据
     * @param $user_info
     * @return array
     */
    public function getSysInfo($user_info){
        try {
            //性别
            foreach (OsmEnums::$sex as $key=>$val){
                $data['sex'][] = [
                    'code' => $key,
                    'name' => $this->getTranslation()->_($val),
                ];
            }
            //国籍
            foreach (OsmEnums::$nationality_list as $key => $val) {
                $data['nationality_list'][] = [
                    'code' => $key,
                    'name' => $this->getTranslation()->_($val),
                ];
            }
            //职位
            foreach (OsmEnums::$job_title as $key => $val) {
                $data['job_list'][] = [
                    'code' => $key,
                    'name' => $this->getTranslation()->_($val),
                ];
            }
            //网点
            $store_list = (new SysStoreModel())->getStoreListByCategory(array_keys(OsmEnums::$store_category));
            foreach ($store_list as $key => $val) {
                $data['store_list'][] = [
                    'code' => $val['id'],
                    'name' => $val['name'],
                ];
            }
            $result = [
                'code' => ErrCode::SUCCESS,
                'msg' => 'Success',
                'data' => $data
            ];
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('get-osm-sys-info:' . $user_info['company_phone'], 'error');
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg' => 'server error',
                'data' => []
            ];
        }
        return $result;
    }

    /**
     * 获取单个外协员工信息
     * @param $params
     * @param $user_info
     * @return array
     */
    public function getOneOutsourcingStaff($params, $user_info){
        try {
            $hr_staff_model = new HrStaffInfoModel();
            $staff_info = $hr_staff_model->getOneByStaffId($params['staff_info_id']);
            //1.1员工不存在
            if (empty($staff_info)) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_exists'));
            }
            //1.2员工已不是外协
            if ($staff_info['formal'] != HrStaffInfoModel::FORMAL_0) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_outsourcing'));
            }
            //1.3员工已离职
            if ($staff_info['state'] != HrStaffInfoModel::STATE_1) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_leave'));
            }

            $staff_hikvision_Model = new StaffHikvisionModel();
            $staff_hikvision = $staff_hikvision_Model->getOneByStaff($params['staff_info_id']);
            //1.4海康附属信息不存在或员工不属于当前公司
            if (empty($staff_hikvision) || empty($staff_hikvision['outsourcing_company_id'])) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_no_company'));
            }
            //1.5员工已离职
            if ($staff_hikvision['deleted'] == StaffHikvisionModel::DELETED_YES) {
                throw new ValidationException($this->getTranslation()->_('outsourcing_company_staff_leave'));
            }

            //国籍
            $item_model = new HrStaffItemsModel();
            $nationality_info = $item_model->getOneByStaffId($staff_info['staff_info_id'], HrStaffItemsModel::ITEM_NATIONALITY);

            //网点信息
            $store_model = new SysStoreModel();
            $store = $store_model->getOneStoreById($staff_info['sys_store_id']);

            //职位
            $job_model = new HrJobTitleModel();
            $job = $job_model->getOneById($staff_info['job_title']);

            //身份证照片
            $identity_front = $item_model->getOneByStaffId($staff_info['staff_info_id'], HrStaffItemsModel::ITEM_IDENTITY_FRONT_KEY);

            $data = [
                'staff_info_id' => $staff_info['staff_info_id'],
                'name' => $staff_info['name'],
                'sex' => (int)$staff_info['sex'],
                'sex_name' => !empty($staff_info['sex']) ? $this->getTranslation()->_(\FlashExpress\bi\App\Enums\OsmEnums::$sex[$staff_info['sex']]) : '',
                'nationality' => !empty($nationality_info) ? (int)$nationality_info['value'] : 0,
                'nationality_name' => !empty($nationality_info) ? $this->getTranslation()->_(OsmEnums::$nationality_list[$nationality_info['value']]): '',
                'identity' => $staff_info['identity'],
                'sys_store_id' => $staff_info['sys_store_id'],
                'sys_store_name' => !empty($store) ? $store['name'] : '',
                'job_id' => (int)$staff_info['job_title'],
                'job_name' => !empty($job) ? $job['job_name'] : '',
                'identity_file_a' => !empty($identity_front) ? env('img_prefix', '') . $identity_front['value'] : '',
                'face_img_path' => $staff_hikvision['face_img_path'],
            ];
            $result = [
                'code' => ErrCode::SUCCESS,
                'msg' => 'Success',
                'data' => $data,
            ];
        } catch (ValidationException $e){
            $this->getDI()->get('logger')->write_log('update-outsourcing-staff-Validation-fail' . json_encode(array_merge($params,$user_info)), 'info');
            $result = [
                'code' => ErrCode::VALIDATE_ERROR,
                'msg' => $e->getMessage(),
                'data' => [],
            ];
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('update-outsourcing-staff-fail' . json_encode(array_merge($params,$user_info)), 'error');
            $result = [
                'code' => ErrCode::SYSTEM_ERROR,
                'msg' => 'server error',
                'data' => [],
            ];
        }
        return $result;
    }


    /**
     * 获取外协加班未读数量
     * @param $company_id
     * @return int
     */
    public function getUnReadOutsourcingOt($company_id)
    {
        $count_columns = ['COUNT(*) AS count'];
        $params        = [
            'osm_state'  => HubOutsourcingOvertimeModel::OSM_STATE_PENDING,
            'company_id' => $company_id,
        ];

        $result_count = (new OutsourcingOTServer($this->lang, $this->timeZone))->getOutsourcingOTQuery($params,
            $count_columns,
            true);
        return (int)$result_count['count'];
    }
}