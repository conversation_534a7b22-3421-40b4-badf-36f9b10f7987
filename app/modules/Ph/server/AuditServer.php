<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\Enums\SettingEnvEnums;
use FlashExpress\bi\App\Enums\SysStoreCateEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Modules\Ph\enums\OvertimeEnums;
use FlashExpress\bi\App\Modules\Ph\library\Enums\CommonEnums;
use FlashExpress\bi\App\Modules\Ph\Server\Vacation\InternationalServer;
use FlashExpress\bi\App\Modules\Ph\Server\Vacation\SickServer;
use FlashExpress\bi\App\Modules\Ph\Server\Vacation\SingleParentServer;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\ResumeRecommendRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditExtendServer;
use FlashExpress\bi\App\Server\AuditServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\HrShiftServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\KpiServer;
use FlashExpress\bi\App\Server\OsPriceServer;
use FlashExpress\bi\App\Server\PaperDocumentServer;
use FlashExpress\bi\App\Server\Penalty\AttendancePenaltyServer;
use FlashExpress\bi\App\Server\Penalty\BasePenaltyServer;
use FlashExpress\bi\App\Server\ProbationTargetServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SuspensionServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\Vacation\TrainingServer;

class AuditServer extends GlobalBaseServer
{


    public static $c_days = 20;

    protected $forbidden   = array(1, 2, 9, 10, 11, 19);//试用期不能申请的类型
    protected $time_limit_start = array(3, 7, 15, 16, 18,21,22,25,26);//限制开始时间 只能当天以后（不在这些类型里面的 才限制）
    public $one_time = array(5,10);//入职以来 只能申请一次
    public $one_send = array(16,7,21);//入职以来 限制总额不限制次数
    public $sub_day = array(1,2,3,7,10,16,5,19,20,22,23,25,26);//需要跳过休息日的类型
    public $newVersionType = [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_19, enums::LEAVE_TYPE_4, enums::LEAVE_TYPE_3];//改版新结构类型

    /**
     * 获取权限
     * @param array paramIn
     * @return array
     */
    public function getListPermission($paramIn = [])
    {
        //补卡
        $result['AT'] = $this->isMakeUpPermission() ? 1 : 2; //默认存在
        //请假
        $result['LE'] = $this->isLeavePermission() ? 1 : 2;     //默认存在
        //个人代理请假
        $result['LE_UNPAID_PH'] = $this->unPaidLeavePermission() ? 1 : 2;//个人代理请假类型
        //LH 移除
        //$result['LH'] = $this->checkLcDataPosition($paramIn) == true ? 1 : 2;
        //新耗材申请上线老的耗材入口移除
        //$result['Wms'] = $this->getWmsPermission($paramIn) == true ? 1 : 2;
        //修改里程权限 9792
        $result['Mile'] = 2;//$this->getMilePermission($paramIn) == true ? 1 : 2;
        //加班-所有人都可以申请OT
        $result['OT'] = $this->getOTPermission($paramIn) == true ? 1 : 2;
        //外协加班权限
        $result['OS_OT'] = $this->getOsOtPermission($paramIn) == true ? 1 : 2;
        //出差
        $result['Trip'] = $this->getTripPermission($paramIn) == true ? 1 : 2;
        //车辆里程
        $result['Vehicle'] = 2;
        //HC
        $result['HC'] = $this->checkHcDataRole($paramIn) == true ? 1 : 2;
        //补申请
        $result['apply'] = $this->getApplyPermission($paramIn) == true ? 1 : 2;
        //加班车
        $result['fleet'] = $this->getFleetPermission($paramIn) == true ? 1 : 2;
        //离职-所有人都可以申请离职
        $result['resign'] = $this->isResignPermission() ? 1 : 2;
        //解约
        $result['cancel_contract'] = $this->checkIsShowCancelContract() ? 1 : 2;
        //解约个人代理
        $result['company_termination_contract'] = $this->isCompanyTerminationContractPermission() ? 1 : 2;
        //外协员工
        $result['OS'] = $this->getOSPermission($paramIn) == true ? 1 : 2;
        //资产申请
        //$result['Asset'] = $this->getASPermission($paramIn) == true ? 1 : 2;
        //举报
        $result['Report'] = $this->getReportPermission($paramIn) == true ? 1 : 2;
        //到岗确认
        $result['Confirm'] = $this->getEntryConfirmPermission($paramIn) == true ? 1 : 2;
        //工资条pdf
        $result['salary'] = $this->salary_pdf_permission($paramIn) == true ? 1 : 2;
        //油费补贴
        $result['fuel_subsidy'] = $this->getFuelSubsidyPermission($paramIn) == true ? 1 : 2;
        //在职证明
        $result['on_job'] = $this->on_job_pdf_permission($paramIn) == true ? 1: 2;
        //工资证明
        $result['payroll'] = $this->on_job_pdf_permission($paramIn) == true ? 1: 2;
        //离职资产确认
        $result['resign_as'] = $this->getResignAssetPermision($paramIn) == true ? 1: 2;
        //转岗
        $result['TF'] = $this->getJobTransferPermissionV2($paramIn) == true ? 1: 2;
        //运费申请
        //$result['FD_nw'] = $this->getFreightDiscNetworkPermission($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_shop'] = $this->getFreightDiscShopPermission($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_other'] = $this->getFreightDiscOtherPermission($paramIn) == true ? 1 : 2;
        //抄送列表
        $result['CC'] = 1;
        //黄牌项目出差
        $result['yc_Trip'] = 2;
        //外出申请
        $result['go_trip'] = $this->getTripPermission($paramIn) == true ? 1 : 2 ;
        // 销售CRM
        $result['Sales_CRM'] = $this->getSalesCRMPermission($paramIn) == true ? 1 : 2;

        //工服购买排除外协员工
        $result['InteriorOrder'] = $this->getInteriorOrdersPermission($paramIn) == true ? 1 : 2;

        // 后勤-报销申请
        $result['fuel_budget']  = 2;

        //by 我的面试
        $result['my_interview'] = (new InterviewRepository($this->timezone))->myInterview($paramIn) == true ? 1 : 2;

        $result['WorkPass'] = $this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID ? 2 : 1;
        //我的简历
        $resumeRecommendNum   = (new ResumeRecommendRepository())->getShowTotal($paramIn);
        $result['rr_is_show'] = !empty($resumeRecommendNum['is_show']) ? 1 : 2;
        //        //-- todo 第一版上线 工号指定配置 二板上之后 下面去掉用上面的
        //        $result['fuel_budget'] = 2;
        //        $staff_ids = env('fuel_staff');
        //        if(!empty($staff_ids)){
        //            $arr = explode(',',$staff_ids);
        //            if(in_array($paramIn['staff_id'],$arr))
        //                $result['fuel_budget'] = 1;
        //        }

        //外协特殊价格审批
        $result['os_price'] = (new OsPriceServer($this->timezone))->permission($paramIn) == true ? 1 : 2;
        //网点支援
        $result['SAS']  = $this->getSASPermission($paramIn) == true ? 1 : 2;
        $result['SASS'] = $this->getSASSPermission($paramIn) == true ? 1 : 2;

        //领用包材
        $result['package'] = 1;
        //众包申请
        $result['Crowdsources'] = $this->getCrowdsources($paramIn) ? 1 : 2;
        //新版资产申请权限
        $result['NAS'] = $this->getNewAssetPermission($paramIn) ? 1 : 2;

        //申请耗材
        $result['apply_consumables'] = $this->getConsumablesPermission($paramIn) ? 1 : 2;
        //全员展示，证明下载-BDO授权书
        $result['BDO'] = 2;

//        $bool               = (new \FlashExpress\bi\App\Server\AdministrationOrderServer())->isFlashFulfillmentDepartment($paramIn,
//            $setVal);
        $result['xz_order'] = $this->getAdministrationOrderPermission($paramIn) ? 1 : 2;
        //审批-人事-KPI目标
        $result['kpi'] = (new KpiServer())->getKpiPermission($paramIn) ? 1 : 2;

        // 外协员工加班申请入口
        $result['outsourcing_ot'] = (true === (new OutsourcingOTServer($this->lang,$this->timezone))->isHubFenBoManger($paramIn)) ? 1 : 2;
        //quick offer
        $result['quick_offer'] = $this->quickOfferPermission() ? 1 : 2;
        //OA文件夹
        $result['oa_folder'] = $this->OAFolderPermission() ? 1 : 2;
        //hcm入口
        $result['HCM'] = $this->isShowHCMPermission() ? 1 : 2;
        //停职申请
        $result['RNTE'] = (new SuspensionServer($this->lang,$this->timezone))->isShowSuspensionAuditPermission($paramIn) ? 1 : 2;
        //车辆维修申请
        $result['vehicle_repair_request'] = $this->isShowVehicleRepairRequestPermission() ? 1 : 2;
        //交车&验车
        $result['vehicle_delivery_and_inspection'] = $this->isShowVehicleDeliveryAndInspectionPermission() ? 1 : 2;
        //过滤外协员工权限
        $paramIn['permission_list'] = $result;
        $result = $this->outsourcingPermissionFilter($paramIn);
        //纸质单据确认
        $result['paper'] = (new PaperDocumentServer($this->lang, $this->timezone))->getPaperPermission($paramIn) ? 1 : 2;

        //试用期目标
        $result['probation_target'] = (new ProbationTargetServer($this->lang, $this->timezone))->getMenuPermission($paramIn) ? 1 : 2;


        //过滤外协员工权限
        $paramIn['permission_list'] = $result;
        $result = $this->outsourcingPermissionFilter($paramIn);
        //耗材调拨入口
        $result['package_allot'] = $this->isShowPackageAllotPermission($this->staffInfo) ? 1 : 2;
        return $this->checkReturn(['data' => $result]);
    }

    /**
     * 获取资产申请权限 和 我的tab  公共资产显示
     * @param array paramIn
     * @param boolean paramIn 是否只判断白名单
     * @return boolean
     */
    public function getASPermission($paramIn = [], $isOnlyWhiteList = false)
    {
        $staffId = $paramIn['staff_id'];
        //[2]获取当前员工职位
        $staff_re = new StaffRepository($this->lang);
        $info = $staff_re->getStaffPositionv3($staffId);

        //申请权限
        //白名单 ：工号为[19685,24455,40450,25921,29053] 或者 职位是Regional Manager(79)、Assistant Regional Manager(269)
        //DC、SP网点: 网点正主管(职位是16)
        //SHOP网点: 网点正主管
        //HUB网点: 指定工号
        $staffArr = explode(',', env('asset_request_white_list', "24455,40450,38983,19685,47094,19060,21426,21328"));
        if (in_array($staffId, $staffArr) || in_array($info['job_title'], [
                enums::$job_title['regional_manager'],
                enums::$job_title['district_manager'],
                enums::$job_title['area_manager'],
            ])) {
            return true;
        }

        //pdc判断
        if(in_array($info['category'],SysStoreCateEnums::PDC_CATE_CODE)){
            if (in_array($info['job_title'], [
                enums::$job_title['branch_supervisor'],//16
                enums::$job_title['district_manager'],//269
                enums::$job_title['regional_manager'],//79
                enums::$job_title['network_support_officer'],//556
                enums::$job_title['network_support_supervisor'],//555
                enums::$job_title['network_support_manager'],//554
            ])) {
                return true;
            }
        }

        if ($isOnlyWhiteList === true) {
            // 只验证工号职位白名单
            return false;
        }

        return $this->check_asset_permission($info);
    }


    public function check_asset_permission($info)
    {
        //20211214 按照11172需求修改 需求产品刘丹丹
        $setting_server = new SettingEnvServer();
        if($info['organization_type'] == 2){//总部的人 全部给权限
            return true;
        }
        $wrs_staff = $setting_server->getSetVal('wrs_staff_check_asset_ids');
        if(!empty($info['job_title']) && in_array($info['job_title'],explode(',',$wrs_staff))){
            return true;
        }else{
            return false;
        }
    }


    /**
     * 获取物料申请权限
     * @param array paramIn
     * @return boolean
     */
    public function getWmsPermission($paramIn = [])
    {
        //[1]校验添加权限
        $admin_group = UC('wmsRole')['admin_group'];
        $staffId     = $paramIn['staff_id'];

        //admin小組有提交物料权限
        if (isset($admin_group[$staffId])) {
            return true;
        }

        return $this->check_wrs_permission($staffId);
    }


    //country 里面搬过来的
    public function check_wrs_permission($staff_id)
    {
        //20211214 按照11172需求修改 需求产品刘丹丹
        $staff_re = new StaffRepository($this->lang);
        $info = $staff_re->getStaffPositionv3($staff_id);

        $setting_server = new SettingEnvServer();
        $wrs_staff = $setting_server->getSetVal('wrs_staff_check_wrs_ids');

        if(!empty($info['job_title']) && in_array($info['job_title'],explode(',',$wrs_staff))){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 获取出差申请权限
     * @param array paramIn
     * @return boolean
     */
    public function getTripPermission($paramIn = [])
    {
        if ($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return false;
        }
        $setVal = (new SettingEnvServer())->getSetVal(SettingEnvEnums::CLOSE_ENTRANCE_JOBTITLE_IDS);
        $jobIds = empty($setVal) ? [] : explode(',', $setVal);
        if($jobIds && in_array($paramIn['job_title'], $jobIds)){
            return false;
        }
        return true;
    }

    /**
     * 获取外协员工申请权限
     * @param array $paramIn
     * @return boolean
     */
    public function getOSPermission($paramIn = [])
    {
        // 迁移 uc 配置 outsourcingstaff jobTitlesNot
        $jobTitlesData = [
            enums::$job_title['van_courier']      => 'Van Courier',
            enums::$job_title['bike_courier']     => 'Bike Courier',
            enums::$job_title['boat_courier']     => 'Boat Courier',
            enums::$job_title['tricycle_courier'] => 'Tricycle Courier',
            enums::$job_title['car_courier']      => 'Car Courier',
            enums::$job_title['truck_courier']    => 'Truck Driver',
        ];
        $hubRequestRole       = UC('outsourcingStaff')['osLongPeriodRequestRole'];
        $motorcadeRequestRole = UC('outsourcingStaff')['osMotorcadeRequestRole'];
        $roles                = $this->processingDefault($paramIn, 'positions', 3);
        $job_title            = $this->processingDefault($paramIn, 'job_title', 2);
        $organizationId       = $this->processingDefault($paramIn, 'organization_id', 2);
        $type                 = $this->processingDefault($paramIn, 'organization_type', 2);
        $departmentId         = $this->processingDefault($paramIn, 'department_id', 2);

        $hubOsRoles    = (new SettingEnvServer())->getSetValFromCache('hub_os_roles', ',');

        //hub 外协工单，响应角色的人 有入口权限
        if(array_intersect($hubOsRoles, $roles)) {
            return true;
        }


        if ((!empty($jobTitlesData) || !empty($hubRequestRole)) || !empty($motorcadeRequestRole)) {

            $store = (new SysStoreServer())->getStoreByid($organizationId);


            if ($type == 2) { 
                return false;
            }

            if (!isset($jobTitlesData[$job_title]) || (isset($store) && ($store['category'] == 8 || $store['category'] == 9))) {
                return true;
            }
        }
        return false;
    }
    public function salary_pdf_permission($param)
    {
        if (empty($this->staffInfo)){
            return false;
        }
        if (!in_array($this->staffInfo['formal'],[1,2])) {
            return false;
        }
        //个人代理不展示
        if ($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return false;
        }
        return true;
    }

    public function on_job_pdf_permission($param)
    {

        if (empty($this->staffInfo)) {
            return false;
        }
        if ($this->staffInfo['working_country'] != HrStaffInfoModel::WORKING_COUNTRY_PH) {
            return false;
        }

        //非编制 不展示  or 个人代理不展示
        if ($this->staffInfo['formal'] != 1  || $this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return false;
        }
        return true;
    }

    /**
     * 获取销售CRM权限
     * @param mixed $paramIn
     * @doc https://l8bx01gcjr.feishu.cn/docs/doccnPxTa5v78XVc6Q2Hq6C1xOe
     */
    public function getSalesCRMPermission($paramIn): bool
    {
        $staffId = $paramIn["staff_id"];
        $department_id = $paramIn["department_id"];

        //CRM 白名单 crm_white_list = 57776
        $senv = (new SettingEnvServer())->getSetVal('crm_white_list');
        if(!empty($senv) && in_array($staffId,explode(',',$senv))){
            return true;
        }

        if (empty($staffId) || empty($department_id)) {
            return false;
        }

        $department_ids = (new SettingEnvServer())->getSetVal('crm_department_ids');

        $department_ids_array = explode(',',$department_ids);
        // 有权限的部门IDS
        $permission_department_ids = [];

        // 获取所有部门
        $all_department_ids = SysDepartmentModel::find([
                                                           'conditions' => 'type = :type:',
                                                           'bind' => ['type' => 2],
                                                           'columns' => ['id', 'ancestry_v3']
                                                       ])->toArray();

        foreach ($all_department_ids as $item) {
            $ancestry_v3 = explode('/', $item['ancestry_v3']);
            if (array_intersect($department_ids_array, $ancestry_v3)) {
                $permission_department_ids[] = $item['id'];
            }
        }
        return in_array($department_id, $permission_department_ids);
    }


    /**
     * 新需求 定制 菲律宾 申请加班入口权限
     * https://l8bx01gcjr.feishu.cn/docs/doccnYP2pzjtkfHuGEJNHCUAY5g 第三部分
     *
     */
    public function getOTPermission($paramIn): bool
    {

        $staffId = $paramIn["staff_id"];
        if (empty($staffId)) {
            return false;
        }

        //调rpc 配置
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('OT_apply_rules')
            ->setParameters(['staff_info_id' => $staffId])
            ->getConfig();
        $setting = [];
        if(!empty($res['response_type']) && $res['response_type'] == ConditionsRulesEnums::RESPONSE_TYPE_VALUE){
            $setting = $res['response_data'];
        }
        return (bool)$setting;
    }

    public function getOsOtPermission($paramIn): bool
    {
        $staffId = $paramIn["staff_id"];
        if (empty($staffId)) {
            return false;
        }
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('OS_OT_apply_permission')
            ->setParameters(['staff_info_id' => $staffId])
            ->getConfig();
        $setting = [];
        if(!empty($res['response_type']) && $res['response_type'] == ConditionsRulesEnums::RESPONSE_TYPE_VALUE){
            $setting = $res['response_data'];
        }
        return (bool)$setting;


    }



    public function get_holidays($staff_info){
        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $data = $leave_server->ph_days($staff_info);
        if(!empty($data))
            return array_column($data,'day');
        return array();

    }

    public function type_book($locale = '')
    {
        $data[] = [
            'code' => '1',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2003')
        ];
        $data[] = [
            'code' => '3',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2005')
        ];
        $data[] = [
            'code' => '10',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2012')
        ];
        $data[] = [
            'code' => '4',
            'type' => 2,
            'msg'  => $this->getTranslation($locale)->_('2006') //产假
            ,'template_type' => enums::LEAVE_TEMPLATE_TYPE_1
        ];
        $data[] = [
            'code' => '5',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2007') //'陪产假',
        ];
        $data[] = [
            'code' => '7',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2009')
            ,'need_img' => 1
        ];
        $data[] = [
            'code' => '15',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2017')
        ];
        $data[] = [
            'code' => '16',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2018')
        ];

        $data[] = [
            'code' => '20',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('leave_20')
            ,'need_img' => 1
        ];
        $data[] = [
            'code' => '21',
            'type' => 2,
            'msg'  => $this->getTranslation($locale)->_('leave_21') //女性特殊假
        ];
        $data[] = [
            'code' => '22',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('leave_22')
        ];
        $data[] = [
            'code' => '19',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2022')
        ];
        $data[] = [
            'code' => '23',
            'type' => 2,
            'msg'  => $this->getTranslation($locale)->_('leave_23')
        ];

//        $data[] = [
//            'code' => '25',
//            'type' => 2,
//            'msg'  => $this->getTranslation($locale)->_('leave_25')
//            ,'need_img' => 1
//        ];
//
//        $data[] = [
//            'code' => '26',
//            'type' => 2,
//            'msg'  => $this->getTranslation($locale)->_('leave_26_ph')
//            ,'need_img' => 1
//        ];

        $data[] = [
            'code' => '41',
            'type' => 0,
            'msg'  => $this->getTranslation($locale)->_('leave_41')
        ];


        return $data;
    }



    /**实习生员工 定制类型 公司培训 和 无薪假
     * code 类型枚举
     * type 1 带薪 2 不带薪 3 不拼接
     * color 1 黄色 2 灰色
     * @return array
     */
    public function practise_type_book(){
        $data[] = [
            'code' => '16',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2018')
        ];

        $data[] = [
            'code' => '23',
            'type' => 2,
            'msg'  => $this->getTranslation()->_('leave_23')
        ];
        return $data;
    }

    //网点申请支援权限
    public function getSASPermission($paramIn) {
        //1. 申请职位权限：District Manager、Area Manager、DC Supervisor；取数：OA组织架构的部门负责人
        $staffInfoId = $paramIn['staff_id'];
        $manageStore = SysStoreModel::find([
                            'conditions' => 'manager_id = :staff_id: and state = 1 and category IN (1,10,13)',
                            'bind'       => ['staff_id' => $staffInfoId],
                        ])->toArray();

        $manageRegionAll = SysManageRegionModel::find([
                                'conditions' => 'deleted = 0 and type = 1 '
                            ]);
        $region_arr = $manageRegionAll->toArray();
        $region_ids = array_column($region_arr, 'id');
        $region_manager_ids = array_column($region_arr, 'manager_id');

        //2-片区负责人
        $managePiece = SysManagePieceModel::find([
                            'conditions' => 'manager_id = :staff_id: and deleted = 0 and manage_region_id in ({manage_region_ids:array})',
                            'bind'       => ['staff_id' => $staffInfoId, 'manage_region_ids' => $region_ids],
                        ])->toArray();

        //3-大区负责人
        if(in_array($staffInfoId, $region_manager_ids)) {
            $manageRegion = true;
        } else {
            $manageRegion = false;
        }

        return !empty($manageStore) || !empty($managePiece) || $manageRegion;
    }

    //灰度
    public function getSASPermissionV2($paramIn) {
        //灰度网点
        $gray_level_store_list = [
            'PH17010100','PH17030100','PH17050200','PH17130200','PH17140100','PH17210H00','PH17290100','PH18030100','PH18030101','PH18040100',
            'PH18060100','PH18060200','PH18060800','PH18080600','PH18080B01','PH18110100','PH18180200','PH19100300','PH20070300','PH21120200',
            'PH57090200','PH61020200','PH61020201','PH61180100','PH61180101','PH61180200','PH61180400','PH61180B00','PH61181700','PH61181X00',
            'PH61182000','PH61183900'];
        //1. 申请职位权限：District Manager、Area Manager、DC Supervisor；取数：OA组织架构的部门负责人
        $staffInfoId = $paramIn['staff_id'];
        $manageStore = SysStoreModel::find([
            'conditions' => 'id in({gray_level_store_ids:array}) and manager_id = :staff_id: and state = 1',
            'bind'       => [
                'staff_id' => $staffInfoId,
                'gray_level_store_ids' => $gray_level_store_list
            ],
        ])->toArray();

        /*
        //灰度片区
        $gray_level_piece_ids = [3, 6];
        //2-片区负责人
        $managePiece = SysManagePieceModel::find([
            'conditions' => 'id in({gray_level_piece_ids:array}) and manager_id = :staff_id: and deleted = 0 ',
            'bind'       => [
                'staff_id' => $staffInfoId,
                'gray_level_piece_ids' => $gray_level_piece_ids
            ],
        ])->toArray();
        return !empty($manageStore) || !empty($managePiece);
        */
        $is_show = false;
        if(in_array($staffInfoId, [117201, 119092, 117135, 117281])) {
            $is_show = true;
        }
        return !empty($manageStore) || $is_show;
    }

    //员工申请支援网点权限
    public function getSASSPermission($paramIn) {
        //1. Tricycle Courier、Van Courier、Bike Courier、DC Supervisor：只能看到和提交对应职位的支援申请
        $job_title = $this->processingDefault($paramIn, 'job_title', 2);
        if(in_array($job_title, [
            enums::$job_title['van_courier'],
            enums::$job_title['bike_courier'],
            enums::$job_title['tricycle_courier'],
            enums::$job_title['branch_supervisor'],
            enums::$job_title['regional_manager'],
            enums::$job_title['district_manager'],
            enums::$job_title['dc_officer'],
            enums::$job_title['assistant_branch_supervisor'],
        ])) {
            return true;
        }
        return false;
    }

    //灰度
    public function getSASSPermissionV2($paramIn) {
        //灰度网点
        $gray_level_store_list = [
            'PH17010100','PH17030100','PH17050200','PH17130200','PH17140100','PH17210H00','PH17290100','PH18030100','PH18030101','PH18040100',
            'PH18060100','PH18060200','PH18060800','PH18080600','PH18080B01','PH18110100','PH18180200','PH19100300','PH20070300','PH21120200',
            'PH57090200','PH61020200','PH61020201','PH61180100','PH61180101','PH61180200','PH61180400','PH61180B00','PH61181700','PH61181X00',
            'PH61182000','PH61183900'];

        $user_store_id = $this->processingDefault($paramIn, 'organization_id', 1);
        $staffInfoId = $paramIn['staff_id'];
        $is_permissions = false;
        if(!empty($user_store_id) && in_array($user_store_id, $gray_level_store_list)) {
            //1. Tricycle Courier、Van Courier、Bike Courier、DC Supervisor：只能看到和提交对应职位的支援申请
            $job_title = $this->processingDefault($paramIn, 'job_title', 2);
            if(in_array($job_title, [
                enums::$job_title['van_courier'],
                enums::$job_title['bike_courier'],
                enums::$job_title['tricycle_courier'],
                enums::$job_title['branch_supervisor'],
            ])) {
                $is_permissions = true;
            }
        }
        $is_show = false;
        if(in_array($staffInfoId, [117201, 119092, 117135, 117281])) {
            $is_show = true;
        }

        return $is_permissions || $is_show;
    }




    /**
     * 获取剩余假期天数
     * @param $param
     */
    public function get_left_holidays($param)
    {
        $showType    = [enums::LEAVE_TYPE_1,enums::LEAVE_TYPE_19];
        //hcm 工具获取所有假期额度
        if (!empty($param['is_svc']) && $param['is_svc'] == 1) {
            $data = $this->rpcHolidaysNum($param);
            return $this->checkReturn(['data' => $data]);
        }

        $staff_id = intval($param['staff_id']);
        //所有假期类型
        $data = $this->type_book();
        //根据员工属性 获取对应权限类型
        $data = $this->staffLeaveType($param['user_info'], $data);

        //是否可以今天请假，1代表可以申请今天，0代表不能申请今天
        $canleavetoday = (new SettingEnvServer())->getSetVal('canleavetoday');

        $leave_server = new LeaveServer($this->lang, $this->timezone);
        foreach ($data as $k => &$da) {
            $type = intval($da['code']);
            //需要在请假页面显示天数的假期类型 1
            if (in_array($type, [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_19])) {//改版后调用
                $da              = array_merge($da, $leave_server->getVacationDays($staff_id, $type));
            }
            if (in_array($type, $showType)) {
                //改类型 前端用
                $da['day_limit'] = empty($da['day_limit']) ? '0' : (string)$da['day_limit'];
                $da['day_sub']   = empty($da['day_limit']) ? '0' : (string)$da['day_sub'];
            }

            //仅能选择今天之后的日期（canleavetoday=0不含今天）
            if($type == enums::LEAVE_TYPE_41) {
                $da['start_day_leave'] = !empty($canleavetoday) && $canleavetoday == self::CAN_LEAVE_TODAY ? date('Y-m-d') : date('Y-m-d', strtotime("+1 day"));
            }
        }
        $data = $leave_server->explain_read_flag($param['user_info'], $data);
        return $this->checkReturn(['data' => $data]);
    }


    public function rpcHolidayBody($permissionData, $leave_server){
        foreach ($permissionData as $k => &$da) {
            $type = intval($da['code']);
            //额度显示 需要排除的 类型
            if (in_array($type, [enums::LEAVE_TYPE_15, enums::LEAVE_TYPE_25, enums::LEAVE_TYPE_26])) {
                unset($permissionData[$type]);
                continue;
            }

            //需要在请假页面显示天数的假期类型 1 和类型2
            if (in_array($type, $this->newVersionType)) {//改版后调用
                $da              = array_merge($da, $leave_server->getVacationDays($this->staffId, $type, ['is_svc' => 1]));
                continue;
            }

            //一次性假期
            if (!empty($this->oneTypes) && in_array($type, $this->oneTypes)) {
                $da['day_limit'] = $da['day_sub'] = $this->limit_array[$type] ?? 0;//分母 应有总额度
                //one time 类型 看有没有 没有就是没申请过 是全额
                if (in_array($type, $this->one_time) && !empty($this->remainData[$type])) {
                    $da['day_sub'] = 0;
                }
                if (in_array($type, $this->one_send) && !empty($this->remainData[$type])) {
                    $da['day_sub'] = half_num($this->remainData[$type]['days']);
                }
                continue;
            }

            if (!in_array($type, $this->limit_types)) {
                $da['text'] = 'no_limit';//没有 unset掉 并且 额度表没类型 说明 是不限制额度
            }

            //需要计算的类型 还没改版
            $da['day_limit'] = $this->limit_array[$type] ?? 0;//分母 应有总额度
            $sum             = empty($this->sum_days[$type]['num']) ? 0 : $this->sum_days[$type]['num'];//已用的 前端没用 不限时了
            $da['day_sub']   = $da['day_limit'] - $sum;//分子 今年剩余额度

            if ($da['day_sub'] < 0) {
                $da['day_sub'] = 0;
            }
        }
        return $permissionData;
    }


    //根据员工工号属性 获取对应能申请的假期类型
    public function staffLeaveType($staff_info,$data = []){
        if(empty($data)){
            $data = $this->type_book();
        }
        $leave_server = new LeaveServer($this->lang, $this->timezone);
        //新增权限判断
        $permission = $leave_server->leavePermission($staff_info);
        if(!$permission){
            return [];
        }
        //实习生类型
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_INTERN){
            $data = $this->practise_type_book();
        }

        //外协类型
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_0){
            $data = $this->os_type_book();
        }
        //无底薪员工
        if($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID){
            $data = $this->hire_un_paid_book(enums::LEAVE_TYPE_41);
        }

        //月薪制合同工 只有 紧急假 和 公司培训 跟实习生一样
        if($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_2){
            $data = $this->practise_type_book();
        }

        $data = array_column($data, null, 'code');

        if (empty($staff_info['sex']) ||  $staff_info['sex'] == 1) {
            //男性过滤掉 产检，产假 和女性特殊假
            unset($data[4],$data[21],$data[22]);
        } else {
            //女性过滤掉 陪产假，国家军训假，出家假
            unset($data[5]);
        }
        unset($data[15]);
        //试用期不可申请
        if ($staff_info['probation_status'] != HrProbationModel::STATUS_FORMAL && isset($data[enums::LEAVE_TYPE_21])) {
            unset($data[enums::LEAVE_TYPE_21]);
        }
        // 23年5月1号之后 非正式员工 关闭病假入口
        $sickServer = new SickServer($this->lang,$this->timezone);
        if(!$sickServer->applyPermission($staff_info)){
            unset($data[enums::LEAVE_TYPE_3]);
        }

        //单亲育儿假 权限
        $singleServer = new SingleParentServer($this->lang,$this->timezone);
        if(!$singleServer->applyPermission($staff_info)){
            unset($data[enums::LEAVE_TYPE_20]);
        }

        // 国籍与工作所在地不一致的员工， 会有跨国探亲假 如果数据表没有 触发发放操作
        $nationServer = new InternationalServer($this->lang,$this->timezone);
        if(!$nationServer->applyPermission($staff_info)){
            unset($data[19]);
        }

        //无底薪员工
        if($staff_info['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID){
            unset($data[41]);
        }

        return $data;
    }

    /**
     * 请假添加 泰国请假没有对接可视化 已经迁移至国家目录
     * @Access  public
     * @Param   request
     * @Return  array
     *
     * ！！！ 涉及表 staff_audit 主表 staff_audit_leave_split 拆分表
     */
    public function leaveAdd($paramIn = [])
    {
        //[1]参数定义
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $auditReason    = $this->processingDefault($paramIn, 'audit_reason');
        $imagePathArr   = $this->processingDefault($paramIn, 'image_path');
        $auditReason    = strip_tags(addcslashes(stripslashes($auditReason), "'"));

        $db = StaffAuditModel::beginTransaction($this);
        $serialNo  = $this->getRandomId();

        //[2]用户校验
        $staff_model = new StaffRepository();
        $staffData   = $staff_model->getStaffPosition($staffId);
        if (empty($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
        }

        if ($staffData['is_sub_staff'] == 1) {
            return $this->checkReturn(-3, $this->getTranslation()->_('sub_staff_disable'));
        }
        if ($this->checkStaffFormal($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('os_or_franchisee_staff_disable'));
        }
        $leave_day = $paramIn['leave_day'] = $this->conversionTime(array_merge(['type' => 2], $paramIn));

        //请假日期 就是休息日区间 无需申请 假期类型包括 年假,事假,婚嫁,出家假,个人培训假（不带薪）,病假,绝育手术假,公司培训假,家人去世假。
        if ($leave_day == 0)
            return $this->checkReturn(-3, $this->getTranslation()->_('day off for the apply date'));

        //-- staff holidays between start and end
        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $holidays = $paramIn['holidays'] = $leave_server->staff_off_days($staffId, $leaveStartTime, $leaveEndTime);
        //[3]校验选择时间内是请假记录
        $checkData = $this->checkLeaveDataByDate($paramIn);

        $this->getDI()->get("logger")->write_log("leave params ".json_encode($paramIn,JSON_UNESCAPED_UNICODE) .' check res:'.json_encode($checkData,JSON_UNESCAPED_UNICODE),'info');

        if (isset($checkData['code']) && $checkData['code'] == 0) {
            return $this->checkReturn(-3, $checkData['msg']);
        }
        //新增验证班次时间二次确认提示 https://flashexpress.feishu.cn/docx/WqJcd6xFNoctnux4qPEcKTZcnkg
        if (empty($paramIn['is_bi']) && empty($paramIn['is_submit'])){
            if($staffData['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID){
                $leave_server->checkShiftLeave($staffId,$paramIn);
            }
        }

        //判断是否跨天 保存 拆分表 staff_audit_leave_split
        $insert_param['leave_type'] = $leaveType;
        $insert_param['start_time'] = $leaveStartTime;
        $insert_param['end_time'] = $leaveEndTime;
        $insert_param['start_type'] = $leaveStartType;
        $insert_param['end_type'] = $leaveEndType;
        $insert_param['holidays'] = $holidays;
        $insert_param['sub_day'] = $this->sub_day;

        $r = $leave_server->format_leave_insert($staffId,$insert_param);
        if($r['code'] == 1)
            $insert = $r['data'];
        else
            return $r;

        $audit_model = new AuditRepository($this->lang);
        {
            if($leaveType == enums::LEAVE_TYPE_15 && !empty($paramIn['is_bi'])){
                $check_param['operator'] = $paramIn['operator'] ?? 0;
                $check_param['staff_id'] = $staffId;
                $check_param['date'] = date('Y-m-d',strtotime($leaveStartTime));
                $this->leave_for_workday($check_param);
            }else if(in_array($leaveType,$this->one_time)){
                //增加 一次性额度扣减
                $remain_model = new StaffLeaveRemainDaysModel();
                $remain_row['staff_info_id'] = $staffId;
                $remain_row['leave_type'] = $leaveType;
                $remain_row['days'] = 0;
                $remain_row['leave_days'] = $leave_day;
                $flag = $remain_model->create($remain_row);
                $this->getDI()->get("logger")->write_log("leave_one_time {$staffId} {$flag}" . json_encode($remain_row),'info');
            }else if(in_array($leaveType,$this->one_send)){
                //一次性发放额度 先查询 有没有额度 如果没有额度 不让申请 需要在hris 那边初始化
                $remain_info = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                    'bind' => ['staff_info_id' => $staffId,'leave_type' => $leaveType],
                ]);

                //如果没有 说明没初始化成功 提示错误
                if(empty($remain_info))
                    return $this->checkReturn(-3, 'init failed');

                //操作额度
                $remain_info->days -= $leave_day;
                $remain_info->leave_days += $leave_day;
                $remain_info->updated_at = gmdate('Y-m-d H:i:s');
                $remain_info->update();
            }else if($leaveType == enums::LEAVE_TYPE_20){
                //如果申请去年的 按当年算 申请明年的 按明年算
                $insert_param['need_num'] = 1;
                $num_arr = $leave_server->format_leave_insert($staffId,$insert_param);
                //扣除额度
                $p['this_year'] = $num_arr['data']['this_num'];
                $p['next_year'] = $num_arr['data']['next_num'];
                $p['leave_type'] = $leaveType;
                $leave_server->update_leave_days($staffId,enums::YEAR_ADD,$p);
            }
        }

        $status = 1;
        if (!empty($paramIn['is_bi'])){
            $status = 2;
            $auditReason .= "|system_tool_add";
        }
        $timeOut = date('Y-m-d 00:00:00',strtotime('+3 day'));
        //[4]请假插入
        try {
            $insetData = [
                'staff_info_id'    => $staffId,
                'leave_type'       => $leaveType,
                'leave_start_time' => $this->assemblyData($leaveStartTime, 1, $leaveStartType),
                'leave_start_type' => $leaveStartType,
                'leave_end_time'   => $this->assemblyData($leaveEndTime, 2, $leaveEndType),
                'leave_end_type'   => $leaveEndType,
                'audit_reason'     => $auditReason,
                'status'           => $status,
                'audit_type'       => enums::$audit_type['LE'],
                'leave_day'        => $leave_day,
                'serial_no'        => (!empty($serialNo) ? 'LE' . $serialNo : NULL),
                'time_out'         => $timeOut,
            ];

            //不同国家 存在 子类型 产假等
            if(!empty($paramIn['sub_type'])){
                $insetData['template_comment'] = json_encode(array("leave_{$leaveType}_key" => intval($paramIn['sub_type'])));
            }
            $db->insertAsDict("staff_audit", $insetData);
            $auditId = $db->lastInsertId();
            if (!$auditId) {
                throw new \Exception("插入staff_audit 失败" . json_encode($insetData, JSON_UNESCAPED_UNICODE));
            }
            if($insert && is_array($insert)) {
                foreach ($insert as &$v) {
                    $v['audit_id'] = $auditId;
                }
                $result = $audit_model->batch_insert("staff_audit_leave_split", $insert);
                if (!$result) {
                    throw new \Exception("插入staff_audit_leave_split 失败" . json_encode($insert, JSON_UNESCAPED_UNICODE));
                }
            }
            if (empty($paramIn['is_bi'])) {

                $extend = $this->getWorkflowExtend($insetData, $staffData);
                $extend['time_out'] = $timeOut;
                $res = (new ApprovalServer($this->lang, $this->timezone))->create($auditId, enums::$audit_type['LE'], $staffId,null,$extend);
                if (!$res) {
                    throw new \Exception('audit staff insert leaveAdd workflow fail' . $res);
                }
            } else {
                $this->handleLeave($insetData['leave_start_time'], $insetData['leave_end_time'], $staffId);
            }
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("leaveaddworkflow error:". $e->getMessage() . " " . $e->getTraceAsString());
            $db->rollBack();
            return $this->checkReturn(-3, $this->getTranslation()->_('1009'));
        }


        //插入图片
        if (!empty($imagePathArr)) {
            foreach ($imagePathArr as $k => $v) {
                $insertImgData = [
                    'audit_id'   => $auditId,
                    'image_path' => $v
                ];
                $audit_model->auditImgInsert($insertImgData);
            }
        }

        $return['data'] = array('leave_day' => $paramIn['leave_day']);
        return $this->checkReturn($return);
    }


    /**
     *
     * 验证请假规则
     * @param array $paramIn
     * @return array
     */
    public function checkLeaveDataByDate($paramIn = [])
    {
        //[1]参数定义 leave_start_time 和 leave_end_time 是 ymd 类型
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $imagePathArr   = $this->processingDefault($paramIn, 'image_path');
        $leave_days = $paramIn['leave_day'];

        $param = [
            'staff_id'         => $staffId,
            'leave_start_time' => date('Y-m-d',strtotime($leaveStartTime)),
            'leave_start_type' => $leaveStartType,
            'leave_end_time'   => date('Y-m-d',strtotime($leaveEndTime)),
            'leave_end_type' => $leaveEndType,
        ];


        //开始时间 结束时间 验证
        if ($leaveEndTime < $leaveStartTime) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
        }
        if ($leaveEndTime == $leaveStartTime) {
            if ($leaveStartType > $leaveEndType) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
            }
        }

        //图片最多限制5张
        if (!empty($imagePathArr) && count($imagePathArr) > 5) {
            return $this->checkReturn(-3, $this->getTranslation()->_('at most 5 photos'));
        }

        $audit_model = new AuditRepository($this->lang);
        //[3]查询请假记录
        $levelData = $this->checkExistLeave($param);
        if (!empty($levelData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1012'));
        }


        //新增规则 二期需求 https://shimo.im/sheets/94eQG9bXB8oMZlnT/MODOC
        //试用期：从入职第1天开始算，第1-90天
        //不能申请的假期类型： 1年假，2带薪事假，5陪产假，9个人受训假，10婚假，11出家假 --  新版需求把 6 8 去掉了 后来又把7 去掉了
        //新增需求 试用期判断 不用固定120天 https://l8bx01gcjr.feishu.cn/docs/doccne9Sx3V9c0YesAU97rFTkBb hr_probation 状态为4的字段
        $forbidden = $this->forbidden;
        $leaveType = intval($leaveType);

        $leave_lang = $audit_model::$leave_type;
        //获取job title name 入职时间
        $staff_model = new StaffRepository($this->lang);
        $staff_info  = $staff_model->getStaffPosition($staffId);
        $hire_date   = $staff_info['hire_date'];
        $entry       = strtotime($staff_info['hire_date']);
        if (empty($staff_info) || empty($hire_date)) {
            return $this->checkReturn(-3, 'no permission to apply');
        }

        //类型验证
        $typeData = $this->staffLeaveType($staff_info);
        if (empty($typeData)) {
            throw new ValidationException('wrong leave type');
        }
        if (!in_array($leaveType, array_keys($typeData)) &&  !in_array($leaveType, [enums::LEAVE_TYPE_15,enums::LEAVE_TYPE_25,enums::LEAVE_TYPE_26])) {
            throw new ValidationException($this->getTranslation()->_('2107') . $leaveType);
        }


        //新增 请假白名单 不限制入职天数 修改为 查看是否入职
        if (in_array($leaveType, $forbidden) && $hire_date >= '2020-06-13 00:00:00') {
            //试用期状态，1试用期，2已通过，3未通过，4已转正 适用于 xxx 之后 之前的数据默认都转正 因为没管旧数据
            if($staff_info['status'] != 4 ){
                $message = str_replace('leave_type', $this->getTranslation()->_($leave_lang[$leaveType]), $this->getTranslation()->_('probation_limit'));
                return $this->checkReturn(-3, $message);
            }
            if (!empty($staff_info['formal_at']) && $leaveStartTime < $staff_info['formal_at']) {
                $message = $this->getTranslation()->_('probation_before_limit');
                return $this->checkReturn(-3, $message);
            }

        }

        //除了 3，15，16 类型 其他类型必须 大于当前时间  bi工具 不需要时间验证 新增可以候补家人去世假类型 http://193x782t53.imwork.net:29667/zentao/story-view-3638.html 新增无薪病假
        // $time_limit_start 表示 除了这些假期类型 其余的 必须大于当前时间
        if (!in_array($leaveType, $this->time_limit_start) && empty($paramIn['is_bi'])) {
            //请假日期必须大于等于当前日期
            if ($leaveStartTime < date('Y-m-d', time())) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1018'));
            }
        }

        //19310: 是否可以今天请假，1代表可以申请今天，0代表不能申请今天
        $canleavetoday = (new SettingEnvServer())->getSetVal('canleavetoday');
        if ($leaveType == enums::LEAVE_TYPE_41 && empty($paramIn['is_bi'])) {
            //请假日期必须大于等于当前日期
            $start_day_leave = !empty($canleavetoday) && $canleavetoday == self::CAN_LEAVE_TODAY ? date('Y-m-d') : date('Y-m-d', strtotime("+1 day"));

            if (strtotime($leaveStartTime) < strtotime($start_day_leave) && $start_day_leave == date('Y-m-d')) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1018'));
            }

            if (strtotime($leaveStartTime) < strtotime($start_day_leave) && $start_day_leave == date('Y-m-d', strtotime("+1 day"))) {
                return $this->checkReturn(-3, $this->getTranslation()->_('can_leave_today_outweigh'));
            }
        }


        // 需要先检验 次数 和总额度 是否非法 然后再拆分  一次限制的年假(4,5,6,8,10,11) 去掉 产检一次性限制
        if (in_array($leaveType, $this->one_time)) {//http://193x782t53.imwork.net:29667/zentao/story-view-3112.html
            //验证 员工入职以后 是否请过
            $remain_info = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                'bind'       => ['staff_info_id' => $staffId, 'leave_type' => $leaveType],
            ]);

            if (!empty($remain_info)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }

            //是否超出 限定 额度
            $limit_days = $audit_model->get_leave_days_by_type($leaveType);
            if (!empty($limit_days) && $leave_days > $limit_days) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }
        }
        //女性特殊假试用期不可申请
        if ($leaveType == enums::LEAVE_TYPE_21 && $staff_info['probation_status'] != HrProbationModel::STATUS_FORMAL) {
            return $this->checkReturn(-3, $this->getTranslation()->_('leave_probation_notice'));
        }
        //不限次数 的 总和 限制 假期 新增假期类型 16 员工培训假 不限次数 限制总和
        if (in_array($leaveType, $this->one_send)) {
            //验证 员工入职以后 是否请过
            $remain_info  = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                'bind'       => ['staff_info_id' => $staffId, 'leave_type' => $leaveType],
            ]);
            $leave_server = new LeaveServer($this->lang, $this->timezone);
            if (empty($remain_info)) {
                $remain_info = $leave_server->init_leave(['staff_info_id' => $staffId, 'leave_type' => $leaveType]);
            }

            if (empty($remain_info)) {
                return $this->checkReturn(-3, $this->getTranslation()->_("init type {$leaveType} failed "));
            }

            if ($remain_info->days <= 0 || ($remain_info->days - $leave_days) < 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }
        }

        //新冠特殊限制
        $work_day = empty($staff_info['week_working_day']) ? 5 : $staff_info['week_working_day'];
        //新冠相关假期 一次申请 最大 10天限制  可申请多次
        if(in_array($leaveType,array(enums::LEAVE_TYPE_25,enums::LEAVE_TYPE_26))){
            if($work_day == 5 && $paramIn['leave_day'] > 8){//5天班 限制 8天最多
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit_8'));
            }
            if($work_day == 6 && $paramIn['leave_day'] > 9){//6天班限制9天最多
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit_9'));
            }
        }


        //拼接 验证逻辑 参数 check_leave_type 额度验证
        $leave_info['leave_type'] = $leaveType;
        $leave_info['img']        = $imagePathArr;
        $leave_info['is_bi']      = empty($paramIn['is_bi']) ? '' : $paramIn['is_bi'];
        $leave_info['sub_type'] = empty($paramIn['sub_type']) ? 0 : $paramIn['sub_type'];

        $leave_info['leave_start_type'] = $leaveStartType;
        $leave_info['leave_end_type'] = $leaveEndType;
        $leave_info['holidays'] = $paramIn['holidays'];
        $leave_info['unpaid_confirm'] = $paramIn['unpaid_confirm'] ?? 0;
        //本次请假  持续天数 如果跨年 //如果跨年 并且 不是年假 要拆开 每年分别是几天 和 每年的 开始结束时间 因为部分假期类型是按年度区分 今年可以请n天明年仍然可以请n天 还可以一次性跨年请2n天
        //！！新需求  如果部分请假类型其中包含法定假日 等休息日  需要扣除掉 不占用 该请假类型的额度
        //新需求 如果是产假 请跨年 不占用2年额度 按创建时间 每年一次
        if (date('Y', strtotime($leaveStartTime)) != date('Y', strtotime($leaveEndTime))
            && !in_array($leaveType,array(enums::LEAVE_TYPE_1,enums::LEAVE_TYPE_4,enums::LEAVE_TYPE_19))
            && !in_array($leaveType,$this->one_time)
            && !in_array($leaveType,$this->one_send)
        ) {
            //新需求 拆分时候 如果当天是休息日 剔除掉
            $leave_server = new LeaveServer($this->lang,$this->timezone);
            $date_array = $leave_server->format_year_date($leaveType,$leaveStartTime,$leaveEndTime,$paramIn['holidays']);
            $flag       = false;
            foreach ($date_array as $y) {//array('2019' => array('2019-12-30','2019-10-31') )) 有几年就有几个 正常应该就两年 没人能请一整年假期 还干不干了
                $need_days = 0;
                sort($y);
                $leave_info['leave_start_time'] = $y[0];//该年开始 时间
                $leave_info['leave_end_time']   = end($y);//该年 结束时间

                //拆分计算 本次请假 需要天数
                foreach ($y as $date) {
                    if ($date == $leaveStartTime) {
                        $need_days += ($leaveStartType == 1) ? 1 : 0.5;
                    } else if ($date == $leaveEndTime) {//最后一天
                        $need_days += ($leaveEndType == 2) ? 1 : 0.5;
                    } else {//区间内 肯定是一整天
                        $need_days += 1;
                    }
                }
                //验证每年的额度 是否 够用
                $flag = $this->check_leave_type($leave_info, $staff_info, $need_days);
                if ($flag !== true)
                    break;
            }

            if ($flag !== true)
                return $flag;
        } else {
            $need_days                      = $paramIn['leave_day'];
            $leave_info['leave_start_time'] = $leaveStartTime;
            $leave_info['leave_end_time']   = $leaveEndTime;
            $flag                           = $this->check_leave_type($leave_info, $staff_info, $need_days);
            if ($flag !== true)
                return $flag;
        }
        return $this->checkReturn(1);
    }

    /**
     *请假逻辑
     * 年假 额度 按创建时间计算  按当前时间 获取 今年年假 计算额度
     * 其他假期 如果跨年 先取出 开始年 和额度
     * 然后结束年 和 额度 两次计算 是否合法请假
     * 计算剩余额度 需要 获取 已经请的假期记录 如果存在跨年 需要再次拆分 留下 当前判定年的天数
     *
     * @param $leave_info 截取后的 请假开始时间和结束时间
     * @param $staff_info
     * @param $need_days 截断后的 请假天数 比如 19年 1天 20年2天 总数是3天 need_days 两次传参 1， 2
     * @return bool
     */
    protected function check_leave_type($leave_info, $staff_info, $need_days)
    {
        $leaveType        = $leave_info['leave_type'];
        $leaveStartTime   = $leave_info['leave_start_time'];
        $leaveEndTime     = $leave_info['leave_end_time'];
        $staffId          = $staff_info['staff_info_id'];
        $audit_model      = new AuditRepository($this->lang);
        $imagePathArr     = $leave_info['img'];
        $paramIn['is_bi'] = $leave_info['is_bi'];
        //额度计算
        //新需求 如果请假天数 跨年 需做新规则处理 如果是年假 按当前 年 计算
        $year            = date('Y', strtotime($leaveStartTime));
        $apply_month     = date('m', strtotime($leaveStartTime));
        $apply_end_month = date('m', strtotime($leaveEndTime));
        $current_year    = date('Y', time());
        //新算法 用拆分表
        if ($leaveType == 1 && in_array($apply_month, $this->month_arr))//20年 请 21年 4月之后的年假 不需要取当年的剩余额度
        {
            $applied_days = $audit_model->get_used_leave_days($staffId, $current_year, $leaveType);
        } else {
            $applied_days = $audit_model->get_used_leave_days($staffId, $year, $leaveType);
        }

        //除了年假  之外  每种类型的额度
        $limit_days = $audit_model->get_all_leave_days();

        $sum_days             = $count_days = [];
        $sum_days[$leaveType] = $count_days[$leaveType] = 0;
        if (!empty($applied_days)) {
            $info                   = $applied_days[0];
            $sum_days[$leaveType]   = $info['num'];
            $count_days[$leaveType] = empty($info['audit_ids']) ? 0 : count(explode(',', $info['audit_ids']));
        }

        $leave_server = new LeaveServer($this->lang, $this->timezone);
        //根据请假类型定制 逻辑
        if ($leaveType == enums::LEAVE_TYPE_3) {//[2]检测日期[结束时间必须大于开始时间] 病假可以选择近三天 bi工具排除

            //如果是 23年5月1号之后入职的人 要看他是否转正 没转正 只有3天额度
            $pointDate = (new SettingEnvServer())->getSetVal('ph_sick_point_date');
            if(!empty($pointDate) && date('Y-m-d',strtotime($staff_info['hire_date'])) >= $pointDate){
                if(!($staff_info['status'] == 4 || $staff_info['hire_date'] < '2020-06-13 00:00:00')){
                    $limit_days[$leaveType] = enums::SICK_DAYS;
                    //试用期 提示定制
                    if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType]) {
                        return $this->checkReturn(-3, $this->getTranslation()->_('un_hire_notice_3'));
                    }
                }
            }

            if ($leaveStartTime < date('Y-m-d', strtotime('-2 days')) && empty($paramIn['is_bi'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1026'));
            }

            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType]) {
                $notice_msg = $this->getTranslation()->_('leave_limit');
                return $this->checkReturn(-3, $notice_msg);
            }

        } else if ($leaveType == enums::LEAVE_TYPE_4) {//产假 一次 且不能超过98天 2021-04-07 修改为 按创建时间算 每年一次 每次90天

            $check_4 = $leave_server->check_create_year($staffId,$leaveType);
            if(!$check_4)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

            //需要分流  菲律宾 定制 额度根据子选项 选择天数会有不同 取子类型
            $sub_type = empty($leave_info['sub_type']) ? 0 : intval($leave_info['sub_type']);
            $flag = $leave_server->check_leave_4($need_days,$limit_days[$leaveType],$sum_days[$leaveType],$sub_type,$leave_info);
            if($flag !== true)
                return $this->checkReturn(-3, $flag);

        } else if ($leaveType == enums::LEAVE_TYPE_5) {//陪产假 一次  7天

        } else if ($leaveType == enums::LEAVE_TYPE_7) {//家人去世假 5天
            //去世假 开始时间+7天 必须在当前时间的之前
            if (empty($paramIn['is_bi']) && $leaveStartTime < date('Y-m-d', strtotime('-7 day'))) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_7'));
            }
            //去世假 图片必填
            if (empty($imagePathArr)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_7_img'));
            }
        } else if ($leaveType == enums::LEAVE_TYPE_10) {//婚假 一次 不超过3天
            //新增需求 婚嫁 入职需大雨6个月才能申请
            $hire_6_month = strtotime("{$staff_info['hire_date']} +6 month");
            if (time() < $hire_6_month) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_10_notice'));
            }

        } else if ($leaveType == 16) { //公司培训假 只能选择今天、明天和后天，或者过去8天的日期 且不能超过8天 后改成 2天前
            $trainingServer             = new TrainingServer($this->lang, $this->timezone);
            $trainingServer->staffInfo  = $staff_info;
            $trainingServer->paramModel = $leave_info;
            if ($trainingServer->checkTrainingLeave()) {
                return $this->checkReturn(-3, $this->getTranslation()->_('training_leave_notice'));
            }
        } else if ($leaveType == 15) {//休息日 可以申请前天、昨天、今天、明天、后天。

            if (empty($paramIn['is_bi'])) {
                $beforeTime = strtotime(date("Y-m-d", strtotime("-2 day")));
                $behindTime = strtotime(date("Y-m-d", strtotime("+2 day")));
                if (strtotime($leaveStartTime) < $beforeTime || strtotime($leaveStartTime) > $behindTime) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('overtime_five_days'));
                }
                if (strtotime($leaveEndTime) < $beforeTime || strtotime($leaveEndTime) > $behindTime) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('overtime_five_days'));
                }
            }

            //新需求 按工作天数 区分 6天为单休
            if ($staff_info['week_working_day'] != 6) {
                return $this->checkReturn(-3, $this->getTranslation()->_('jobtransfer_0004'));
            }

            $check_rest = $leave_server->get_rest_times($staff_info, $leave_info);
            if ($check_rest['code'] != 1) {
                return $this->checkReturn(-3, $check_rest['msg']);
            }

            $times = $check_rest['data'];

            if ($times >= 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_for_five'));
            }

            //新需求 休息日 只能是1天 大于小于都不行 https://shimo.im/docs/kv6rgP3GXDT69wdc/read
            if ($need_days < 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_for_half'));
            }

            if ($need_days > 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }

        } else if ($leaveType == enums::LEAVE_TYPE_20) {//单亲育儿假 7天 可以申请3天之内和未来的日期 一次
            //试用期不可申请
            if ($staff_info['probation_status'] != HrProbationModel::STATUS_FORMAL) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_probation_notice'));
            }

            //新需求 https://flashexpress.feishu.cn/docx/IflkdaO28oy3fRxXQg7cCZphnXg
            $singleServer = new SingleParentServer($this->lang,$this->timezone);
            $days = $singleServer->getDays($staff_info, $year);
            if($need_days > $days){
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }


        } else if ($leaveType == 21) {//女性特殊假 适用所有因妇科疾病而接受手术的人 入职时间开始+6个月的员工有资格申请此类假期 无限制次数，请完为止
//            if (date('Y-m-d') < date('Y-m-d', strtotime("{$staff_info['hire_date']} + 6 month"))) {
//                return $this->checkReturn(-3, $this->getTranslation()->_('leave_21_notice'));
//            }

            $date_3 = date('Y-m-d', strtotime("-3 day"));//可以申请3天之内和未来的日期
            if ($leaveStartTime <= $date_3 && empty($paramIn['is_bi'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_3_notice'));
            }


        } else if ($leaveType == 22) {//家庭暴力假 无限制
            $date_3 = date('Y-m-d', strtotime("-3 day"));//可以申请3天之内和未来的日期
            if (empty($paramIn['is_bi'])) {
                if ($leaveStartTime <= $date_3) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('leave_3_notice'));
                }
            }

            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType]) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }

        }  else if ($leaveType == 23) {//紧急假 无限制
            if ($need_days > 5) { //佳雪需求 菲律宾 紧急假 不限制次数，一次最多申请5天
                $message = str_replace('limit_days', 5, $this->getTranslation()->_('leave_limit_x_days'));
                return $this->checkReturn(-3, $message);
            }
        } else if ($leaveType == 25) {//隔离假
            $beforeTime = strtotime(date("Y-m-d", strtotime("-6 day"))); //7天前
            if (strtotime($leaveStartTime) < $beforeTime && empty($paramIn['is_bi'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit_start'));
            }

            if (empty($imagePathArr) || count($imagePathArr) > 5) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_img_most'));
            }

        }  else if ($leaveType == 26) {//新冠治疗
            $beforeTime = strtotime(date("Y-m-d", strtotime("-6 day"))); //7天前
            if (strtotime($leaveStartTime) < $beforeTime && empty($paramIn['is_bi'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit_start'));
            }

            if (empty($imagePathArr) || count($imagePathArr) > 5) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_img_most'));
            }

        }else if ($leaveType == enums::LEAVE_TYPE_12) {

        }else if($leaveType == enums::LEAVE_TYPE_41){
            if($staff_info['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID){
                return $this->checkReturn(-3, $this->getTranslation()->_('jobtransfer_0004'));
            }
            //只能申请 3天及以后日期 >= +3day
            $limitDate = date('Y-m-d', strtotime('+3 day'));
            if($leaveStartTime < $limitDate && empty($paramIn['is_bi']) && empty($leave_info['unpaid_confirm'])){
                throw new ValidationException($this->getTranslation()->_('unpaid_confirm_notice_ph'),10010);
            }
        }else {//非法请求
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        return true;
    }
    /**
     * 换算时间
     * 新需求 需根据员工属性 扣除休息日 和ph  6天-找轮休  5天 找周六日
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function conversionTime($paramIn = [])
    {
        //[1]参数定义
        //leave type 1 上午 2 下午
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $type           = $this->processingDefault($paramIn, 'type', 2, 1);//类型 1外部调用 2内部调用
        $staff_id       = $paramIn['staff_id'];


        //[2]换算时间
        if ($leaveEndTime < $leaveStartTime || ($leaveEndTime == $leaveStartTime) && $leaveEndType < $leaveStartType) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
        }
        $leave_day = 0;
        $key       = $start_date = date('Y-m-d', strtotime($leaveStartTime));
        $end_date  = date('Y-m-d', strtotime($leaveEndTime));
        //获取该员工 请假区间的 休息日 和 ph
        $leave_server = new LeaveServer($this->lang, $this->timezone);
        $holidays     = $leave_server->staff_off_days($staff_id, $leaveStartTime, $leaveEndTime);
        while ($key <= $end_date) {
            if (in_array($key, $holidays) && in_array($leaveType, $this->sub_day)) {
                $key = date("Y-m-d", strtotime("+1 day", strtotime($key)));
                continue;
            }
            $add = 1;
            if ($key == $start_date && $leaveStartType != 1) {
                $add = 0.5;
            }
            if ($key == $end_date && $leaveEndType != 2) {
                $add = 0.5;
            }

            $leave_day += $add;
            $key       = date("Y-m-d", strtotime("+1 day", strtotime($key)));
        }

        if ($type == 1) {
            $returnData['data']['day'] = $leave_day;
            return $this->checkReturn($returnData);
        }
        return $leave_day;
    }

    public function type_book_show_msg($paramIn)
    {
        return false;
    }

    /**
     * @description:众包申请 网点负责人、大区负责人、片区负责人
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/21 16:00
     */
    public function getCrowdsources(array $paramIn = []){
        $staffId = $paramIn['staff_id'] ?? 0;
        //https://flashexpress.feishu.cn/docx/doxcni0QzkToQuedPJ0B7FFvOgd
        if(empty($staffId)){
            return false;
        }
        //是否网点负责人
        $manageStore = SysStoreModel::findFirst([
                                               'conditions' => 'manager_id = :staff_id: and state = 1 ',
                                               'bind'       => [
                                                   'staff_id' => $paramIn['staff_id']
                                               ],
                                           ]);
        if(!empty($manageStore)){
            return  true;
        }

        //是否为片区负责人
        $isPieceManager                = SysManagePieceModel::findFirst([
                                                                            'conditions' => 'manager_id = :staff_id: and deleted = 0',
                                                                            'bind'       => [
                                                                                'staff_id' => $staffId,
                                                                            ]
                                                                        ]);
        if(!empty($isPieceManager)){
            return  true;
        }

        //是否为大区负责人
        $isRegionManager                = SysManageRegionModel::findFirst([
                                                                              'conditions' => 'manager_id = :staff_id: and deleted = 0',
                                                                              'bind'       => [
                                                                                  'staff_id' => $staffId,
                                                                              ]
                                                                          ]);
        if(!empty($isRegionManager)){
            return  true;
        }

        return false;
    }


    public function setATProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            // 是最终审批
            $auditDetail = StaffAuditModel::findFirst([
                'conditions' => ' audit_id = :audit_id: ',
                'bind'       => ['audit_id' => $auditId]
            ]);
            if (empty($auditDetail)) {
                throw new \Exception('audit info error');
            }
            $auditDetail = $auditDetail->toArray();
            if (isset(self::$paramIn['staff_id'])) {
                $staff = HrStaffInfoModel::findFirst([
                    'conditions' => ' staff_info_id = :staff_id: ',
                    'bind'       => ['staff_id' => self::$paramIn['staff_id']]
                ]);
                if ($staff) {
                    $staff = $staff->toArray();
                }
            }
            $updateData = [
                'status'        => $state,
                'audit_id'      => $auditId,
                'reject_reason' => self::$paramIn['reject_reason'] ?? '',
                'approver_id'   => self::$paramIn['staff_id'] ?? 0,
                'approver_name' => isset($staff) && $staff ? $staff['name'] : ''
            ];
            $auditEditFlag = $this->re['audit']->auditEditStatus($updateData);
            if (!$auditEditFlag) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1014'));
            }
            //回调修改打卡时间
            if ($state == enums::$audit_status['approved']) {
                //新需求 回写 班次到打卡表 徐华伟
                $attServer   = new HrShiftServer();
                $shift_data  = $attServer->getShiftInfos($auditDetail['staff_info_id'], [$auditDetail['attendance_date']]);
                $shift_start = $shift_data[$auditDetail['attendance_date']]['start'] ?? '';
                $shift_end   = $shift_data[$auditDetail['attendance_date']]['end'] ?? '';
                $shift_id    = $shift_data[$auditDetail['attendance_date']]['shift_id'] ?? 0;

                $updateData = [
                    'attendance_type'   => $auditDetail['attendance_type'],
                    'attendance_date'   => $auditDetail['attendance_date'],
                    'reissue_card_date' => $auditDetail['reissue_card_date'],
                    'staff_info_id'     => $auditDetail['staff_info_id'],
                    'shift_start'       => $shift_start,
                    'shift_end'         => $shift_end,
                    'shift_id'          => $shift_id,
                ];
                $this->re['audit']->editWorkAttendance($updateData);

                //PH将针对Network Management下的Bike Courier、Van Courier、Truck Driver、 Tricycle Courier、 DC Officer、DC Supervisor
                //的迟到、早退、旷工进行罚款
                $attendancePenaltyServer = new AttendancePenaltyServer($this->lang, $this->timezone);
                $attendancePenaltyServer->push(
                    $auditDetail['staff_info_id'],
                    $auditDetail['attendance_date'],
                    BasePenaltyServer::SRC_MAKE_UP
                );
            }
        }
        return true;
    }

    public function setLEProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            // 是最终审批
            $auditDetail = StaffAuditModel::findFirst([
                'conditions' => ' audit_id = :audit_id: ',
                'bind'       => ['audit_id' => $auditId]
            ]);
            if(empty($auditDetail)){
                throw new \Exception($this->getTranslation()->_('1014'));
            }
            $auditDetail = $auditDetail->toArray();
            if (isset(self::$paramIn['staff_id'])) {
                $staff = HrStaffInfoModel::findFirst([
                    'conditions' => ' staff_info_id = :staff_id: ',
                    'bind'       => ['staff_id' => self::$paramIn['staff_id']]
                ]);
                if ($staff) {
                    $staff = $staff->toArray();
                }
            }
            if (!$extend['super']){
                $state = $this->transformState($state,$extend['is_cancel'] ?? 0);
            }
            $updateData = [
                'status'        => $state,
                'audit_id'      => $auditId,
                'reject_reason' => self::$paramIn['reject_reason'] ?? '',
                'approver_id'   => self::$paramIn['staff_id'] ?? 0,
                'approver_name' => isset($staff) && $staff ? $staff['name'] : ''
            ];
            $auditEditFlag = $this->re['audit']->auditEditStatus($updateData);
            if (!$auditEditFlag) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1014'));
            }
            //不接单通知，提交通过后发送消息给 上级+上上级
            if ($state == enums::$audit_status['approved'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_41) {
                $this->sendMessageToManager($auditDetail);
            }
            //撤销个人代理请假（不服务通知）bi处罚用
            if ($state == enums::$audit_status['revoked'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_41) {
                $bi_rpc = (new ApiClient('bi_rpcv2', '', 'abnormal.staff_leave_revoke', $this->lang));
                $bi_rpc->setParams(['audit_id' => $auditId]);
                $bi_return = $bi_rpc->execute();
                if (!isset($bi_return['result'], $bi_return['result']['code']) || $bi_return['result']['code'] !=1) {
                    $this->logger->write_log(['abnormal.staff_leave_revoke 返回结果失败',$auditId]);
                }
            }
            //审核通过 休息日
            if($state == enums::$audit_status['approved'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_15){
                $check_param['staff_id'] = $auditDetail['staff_info_id'];
                $check_param['date'] = date('Y-m-d',strtotime($auditDetail['leave_start_time']));
                $this->leave_for_workday($check_param);
            }
            //撤销休息日
            if($state == enums::$audit_status['revoked'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_15){
                $ext_server = new AuditExtendServer($this->lang,$this->timezone);
                $del_param['staff_info_id'] = $auditDetail['staff_info_id'];
                $del_param['date_at'] = date('Y-m-d',strtotime($auditDetail['leave_start_time']));
                $del_param['operate_id'] = $extend['operate_id'] ?? 0;
                $ext_server->cancel_for_leave($del_param);
            }
            //跨国探亲假 和其他假期 非审核通过 操作返还 目前其他类型 只有 带薪事假 陆续兼容
            if (in_array($auditDetail['leave_type'], [ enums::LEAVE_TYPE_2]) && $state != enums::$audit_status['approved']) {
                $leave_server = new LeaveServer($this->lang, $this->timezone);
                $p['leave_type'] = $auditDetail['leave_type'];
                $p['audit_id'] = $auditId;
                $leave_server->update_leave_days($auditDetail['staff_info_id'],enums::YEAR_RE_BACK,$p);
            }
            //一次性的假期 one time  one send 需要操作staff_leave_remaining_days 表
            if($state != enums::$audit_status['approved']){
                if(in_array($auditDetail['leave_type'],$this->one_time)){
                    //delete remain
                    StaffLeaveRemainDaysModel::find([
                        'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                        'bind' => ['staff_info_id' => $auditDetail['staff_info_id'], 'leave_type' => $auditDetail['leave_type']]
                    ])->delete();
                }
                if(in_array($auditDetail['leave_type'],$this->one_send)){
                    //update remain
                    $remain_info = StaffLeaveRemainDaysModel::findFirst([
                        'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                        'bind' => ['staff_info_id' => $auditDetail['staff_info_id'], 'leave_type' => $auditDetail['leave_type']]
                    ]);
                    if(!empty($remain_info)){
                        $leave_days = $auditDetail['leave_day'];
                        $remain_info->days += $leave_days;
                        $remain_info->leave_days -= $leave_days;
                        $remain_info->updated_at = gmdate('Y-m-d');
                        $remain_info->update();
                    }
                }

                //通用返还额度
                if(in_array($auditDetail['leave_type'],[enums::LEAVE_TYPE_1,enums::LEAVE_TYPE_20,enums::LEAVE_TYPE_19,enums::LEAVE_TYPE_4,enums::LEAVE_TYPE_3])){
                    //获取员工信息
                    $staff_model = new StaffRepository($this->lang);
                    $staff_info = $staff_model->getStaffPosition($auditDetail['staff_info_id']);
                    $leave_server = new LeaveServer($this->lang, $this->timezone);
                    $leave_server->cancelVacation($auditDetail,$staff_info,$state);
                }
            }
            //PH将针对Network Management下的Bike Courier、Van Courier、Truck Driver、 Tricycle Courier、 DC Officer、DC Supervisor
            //的迟到、早退、旷工进行罚款
            if (($state == enums::APPROVAL_STATUS_CANCEL && ($extend['super'] ?? 0 == 1 || $extend['remark'] ?? '' == 'system tool cancel') || $state == enums::APPROVAL_STATUS_APPROVAL) ) {
                //从HCM撤销请假 & 请假通过
                $this->handleLeave($auditDetail['leave_start_time'], $auditDetail['leave_end_time'], $auditDetail['staff_info_id']);
            }
            //驳回 超时 发消息push
            $this->sendMessage($auditDetail,$state);

        }

        return true;
    }


    /**
     * 获取耗材权限
     * @param array paramIn
     * @return boolean
     */
    public function getConsumablesPermission($paramIn = [])
    {
        // 开始判断申请权限
        $bool     = false;
        $staffId  = $paramIn['staff_id'];
        $staff_re = new StaffRepository($this->lang);
        $info     = $staff_re->getStaffPositionv3($staffId);
        if ($info['organization_type'] == 2) {
            return true;
        }
        //权限限制
        $key_code = enums::MATERIAL_WMS_OPEN_RULE;
        $wms_open_rule =  (new StaffRepository())->getOaSettingEnvAuthority($key_code);
        if (!empty($wms_open_rule)) {
            $rule = json_decode($wms_open_rule, true);
            if (!empty($rule['job_ids']) && in_array($info['job_title'], explode(',', $rule['job_ids']))) {
                $bool = true;
            }
        }
        return $bool;
    }

    //所有类型 都放开 不要删除
    public function type_book_history()
    {
        $data[] = [
            'code' => '1',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2003')
        ];
        $data[] = [
            'code' => '3',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2005')
        ];
        $data[] = [
            'code' => '10',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2012')
        ];
        $data[] = [
            'code'          => '4',
            'type'          => 2,
            'msg'           => $this->getTranslation()->_('2006') //产假
            ,
            'template_type' => enums::LEAVE_TEMPLATE_TYPE_1
        ];
        $data[] = [
            'code' => '5',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2007') //'陪产假',
        ];
        $data[] = [
            'code'     => '7',
            'type'     => 1,
            'msg'      => $this->getTranslation()->_('2009')
            ,
            'need_img' => 1
        ];
        $data[] = [
            'code' => '15',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2017')
        ];
        $data[] = [
            'code' => '16',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2018')
        ];

        $data[] = [
            'code' => '20',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('leave_20')
        ];
        $data[] = [
            'code' => '21',
            'type' => 2,
            'msg'  => $this->getTranslation()->_('leave_21') //女性特殊假
        ];
        $data[] = [
            'code' => '22',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('leave_22')
        ];
        $data[] = [
            'code' => '19',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2022')
        ];
        $data[] = [
            'code' => '23',
            'type' => 2,
            'msg'  => $this->getTranslation()->_('leave_23')
        ];

        $data[] = [
            'code'     => '25',
            'type'     => 2,
            'msg'      => $this->getTranslation()->_('leave_25')
            ,
            'need_img' => 1
        ];

        $data[] = [
            'code'     => '26',
            'type'     => 2,
            'msg'      => $this->getTranslation()->_('leave_26_ph')
            ,
            'need_img' => 1
        ];

        $data[] = [
            'code' => '41',
            'type' => 0,
            'msg'  => $this->getTranslation()->_('leave_41')
        ];
        return $data;
    }

    /**
     * @description 提交请假变更数据
     * @param $leave_start_time
     * @param $leave_end_time
     * @param $staff_info_id
     * @return void
     */
    public function handleLeave($leave_start_time, $leave_end_time, $staff_info_id)
    {
        $key       = date('Y-m-d', strtotime($leave_start_time));
        $end_date  = date('Y-m-d', strtotime($leave_end_time));
        $attendancePenaltyServer = new AttendancePenaltyServer($this->lang, $this->timezone);
        while ($key <= $end_date) {
            $attendancePenaltyServer->push($staff_info_id, $key, BasePenaltyServer::SRC_ADD_LEAVE);
            $key = date("Y-m-d", strtotime("+1 day", strtotime($key)));
        }
    }

    public function getWorkflowExtend($auditData, $staffData)
    {
        $extend['flow_code'] = 'season_1';
        if(in_array($auditData['leave_type'],array(enums::LEAVE_TYPE_25,enums::LEAVE_TYPE_26))){
            $extend['flow_code'] = 'covid';
            $extend['store_id'] = $staffData['sys_store_id'];
        }

        //刘佳雪需求
        //https://l8bx01gcjr.feishu.cn/sheets/shtcnIRaoE8md3f3u1uAuvKd4Be
        $nwFlag = $this->isBelongByAncestry($staffData['staff_info_id'],enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH);
        if ($nwFlag) {
            $extend['flow_code'] = 'season_2';
        }
        return $extend;
    }

    /**
     * 获取薪资周期
     * @param $now
     * @return array
     */
    public function getPayCycle($now = null)
    {
        $timestamp = isset($now) ? strtotime($now):time();
        if (date('j', $timestamp) >= 16){
            $start = date("Y-m-16",$timestamp);
            $end = date("Y-m-t",$timestamp);
        }else{
            $start = date("Y-m-01",$timestamp);
            $end = date("Y-m-15",$timestamp);
        }

        return [$start, $end];
    }

    /**
     * 新版 - 获取ph 国家拥有行政工单提交入口的权限
     * @param array $paramIn
     * @return bool
     */
    public function getAdministrationOrderPermission(array $paramIn): bool
    {
        /**
         * 需求：18385
         * 部门（一级部门及其子部门） | 权限
         * Network Management | 此一级部门下职级是F15以及以上职级的员工均可以有提交入口权限
         * Hub Management | 此一级部门下职级是F15以及以上职级的员工均可以有提交入口权限 |
         * F Commerce | 此子公司下的所有员工都没有提交入口权限 |
         * 其他 | 其他组织所有人都有提交入口权限 |
         */

        if (empty($paramIn)) {
            return false;
        }

        $login_user_dept_id = $paramIn['department_id'];
        $staff_info_id      = $paramIn['staff_id'];
        $dept_rep_obj       = new DepartmentRepository();
        // [1] F Commerce 及子部门都无权限
        $f_commerce = $dept_rep_obj->getDepartmentChildInfo([CommonEnums::F_COMMERCE]);
        if (!empty($f_commerce) && in_array($login_user_dept_id, $f_commerce)) {
            return false;
        }

        // [2] Network Management\Hub Management 职级15以下 无权
        $network_hub = $dept_rep_obj->getDepartmentChildInfo([
            CommonEnums::NETWORK_MANAGEMENT,
            CommonEnums::HUB_MANAGEMENT,
        ]);
        if (!empty($network_hub) && in_array($login_user_dept_id, $network_hub)) {
            // 查询职级
            $user_info = HrStaffInfoServer::getUserInfoByStaffInfoId($staff_info_id,
                'staff_info_id,job_title_grade_v2');
            if (empty($user_info->job_title_grade_v2) || $user_info->job_title_grade_v2 < 15) {
                return false;
            }
        }
        return true;
    }
}
