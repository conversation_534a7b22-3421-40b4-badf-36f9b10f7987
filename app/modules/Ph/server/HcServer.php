<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Server\HcServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class HcServer extends GlobalBaseServer
{
    /**
     * 审批完成回调方法
     * @param int $auditId
     * @param int $state
     * @param array $extend
     * @param bool $isFinal
     * @return mixed|void`
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $info = $this->hc->infoHc(['hc_id' => $auditId]);
        if ($isFinal) {
            //更新hr_myApproval、hr_approval
            $this->processHcFinalApproval($auditId, $state, $extend);

            if ($state == 2) {
                //[1]更新状态
                $this->getDI()->get('db')->updateAsDict(
                    'hr_hc',
                    [
                        'state_code'            => 2,
                        'approval_stage'        => $info['approval_stage'] + 1,
                        'approval_state_code'   => 6,
                        'approval_completion_time' => gmdate('Y-m-d H:i:s', time())
                    ],
                    "hc_id = {$auditId}"
                );
                //[2]Email
                //所属部门是Network Operations (NW-PH) && 工作地点在网点需要发送邮件
                $envModel = new SettingEnvServer();
                $departId = $envModel->getSetVal('dept_network_operations_id');

                $model = new SysDepartmentModel();
                $departSubIds = $model->getSpecifiedDeptAndSubDept($departId);
                if (in_array($info['department_id'], $departSubIds) && $info['worknode_id'] != '-1') {
                    $this->sendMail($auditId);
                }
            } else if ($state == 3) {
                //hc主表最终状态
                $this->getDI()->get('db')->updateAsDict(
                    'hr_hc',
                    [
                        'state_code'            => 1,
                        'approval_stage'        => $info['approval_stage'] + 1,
                        'approval_state_code'   => 5,
                        'approval_completion_time' => gmdate('Y-m-d H:i:s', time())
                    ],
                    "hc_id = {$auditId}"
                );
                //处理hc列表
                $this->processHcApproval($auditId, $state, $extend);
            } else {
                //hc主表最终状态
                $this->getDI()->get('db')->updateAsDict(
                    'hr_hc',
                    [
                        'state_code'            => 1,
                        'approval_stage'        => $info['approval_stage'] + 1,
                        'approval_state_code'   => 4,
                        'approval_completion_time' => gmdate('Y-m-d H:i:s', time())
                    ],
                    "hc_id = {$auditId}"
                );
            }
        } else {
            //非最终状态
            $this->getDI()->get('db')->updateAsDict(
                'hr_hc',
                [
                    'approval_stage'        => $info['approval_stage'] + 1,
                    'approval_state_code'   => 7,
                    'approval_completion_time' => gmdate('Y-m-d H:i:s', time())
                ],
                "hc_id = {$auditId}"
            );

            //处理hc列表
            $this->processHcApproval($auditId, $state, $extend);
        }
    }
}