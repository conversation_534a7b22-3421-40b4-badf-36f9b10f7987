<?php
namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Models\backyard\VehicleMileageModel;
use FlashExpress\bi\App\Modules\Ph\library\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\VehicleRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Models\backyard\HrEconomyAbilityModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditDetailOptionServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\AuditListServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\VehicleServer as BaseVehicleServer;

class VehicleServer extends BaseVehicleServer
{

    public $isVan = false;

    const OTHER = 100;
    /**
     * @var PublicRepository
     */
    private $public;
    /**
     * @var AuditListServer
     */
    private $auditlist;
    /**
     * @var OvertimeRepository
     */
    private $ov;
    /**
     * @var VehicleRepository
     */
    private $vehicle;

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang);
        $this->vehicle = new VehicleRepository($lang, $timezone);
        $this->public = new PublicRepository();
        $this->auditlist = new AuditListServer($lang, $timezone);
        $this->ov = new OvertimeRepository($timezone);
    }

    /**
     * 枚举类型(转换为前端口需要的方式)
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function truckEnumVehicleS($paramIn = []): array
    {

        $returnData = VehicleInfoEnums::CONFIG_VEHICLE_INFO;
        $returnData['vehicle_brand'] = VehicleInfoEnums::VEHICLE_BRAND_AND_MODEL_ITEM_TRUCK;
        $returnData['oil_type']=[];
        $returnData['oil_type'][] = ['value' => VehicleInfoEnums::OIL_TYPE_002, 'label' => VehicleInfoEnums::OIL_TYPE_ITEM[VehicleInfoEnums::OIL_TYPE_002]];;

        // 车辆来源
        foreach (VehicleInfoEnums::VEHICLE_SOURCE_ITEM as $vs_k => $vs_v) {
            $returnData['vehicle_source_item'][] = [
                'value' => $vs_k,
                'label' => $this->getTranslation()->_($vs_v),
            ];
        }

        // 驾照类型
        $driver_license_item = [];
        foreach (VehicleInfoEnums::DRIVER_LICENSE_TYPE_ITEM as $l_k => $l_v) {
            $driver_license_item[] = [
                'value' => $l_k,
                'label' => $l_v,
            ];
        }

        // 车型
        $returnData['vehicle_size'] = [];
        $returnData['vehicle_size'][] = ['value' => array_search('6W',self::getVehicleSize(false)),'label'=>'6W'];//6W

        $returnData['driver_license_item'] = $driver_license_item;
        $returnData['driving_license_vehicle_restrictions'] = array_values(VehicleInfoEnums::DRIVING_LICENSE_VEHICLE_RESTRICTIONS);

        $resData['vehicle_enum'] = $returnData;
        return $resData;
    }


    /**
     * 枚举类型(转换为前端口需要的方式)
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function enumVehicleS($paramIn = [])
    {
        $returnData = VehicleInfoEnums::CONFIG_VEHICLE_INFO;
        $returnData['vehicle_brand'] = VehicleInfoEnums::VEHICLE_BRAND_AND_MODEL_ITEM_DEFAULT;

        $returnData['oil_type'] = [];
        //油类型
        foreach (VehicleInfoEnums::OIL_TYPE_ITEM as $k => $v) {
            $returnData['oil_type'][]=[
                'value' => intval($k ),
                'label' => $v,
            ];
        }
        // 车辆来源
        foreach (VehicleInfoEnums::VEHICLE_SOURCE_ITEM as $vs_k => $vs_v) {
            $returnData['vehicle_source_item'][] = [
                'value' => $vs_k,
                'label' => $this->getTranslation()->_($vs_v),
            ];
        }

        // 驾照类型
        $driver_license_item = [];
        foreach (VehicleInfoEnums::DRIVER_LICENSE_TYPE_ITEM as $l_k => $l_v) {
            $driver_license_item[] = [
                'value' => $l_k,
                'label' => $l_v,
            ];
        }


        // 车型
        $returnData['vehicle_size'] = [];
        foreach (self::getVehicleSize(false)  as $k => $v) {
            $returnData['vehicle_size'][] = [
                'value' => strval($k),
                'label' => $v
            ];
        }

        $returnData['driver_license_item'] = $driver_license_item;
        $returnData['driving_license_vehicle_restrictions'] = array_values(VehicleInfoEnums::DRIVING_LICENSE_VEHICLE_RESTRICTIONS);

        $resData['vehicle_enum'] = $returnData;
        return $resData;
    }

    /**
     * 车辆信息
     * @Access  public
     * @param $paramIn
     * @return array
     */
    public function getVehicleInfoS($paramIn = [])
    {
        $res = $this->vehicle->getVehicleInfoR($paramIn['id']);
        $returnData['vehicle_info'] = array();

        //如果存在车辆信息显示车辆型号品牌名称
        if ($res) {
            //TODO 判断本月是否能换车
            $res['change_car']  = 1;
            $returnData['vehicle_info'] = $res;
        }

        $returnData['vehicle_info'] = $this->handleVehicleInfo($returnData['vehicle_info'], $paramIn);

        return $returnData;
    }


    /**
     * 检查是否已充值
     * @param $oil_number
     * @param $staff_id
     * @return array
     */
    public function getCheckOilNumberByIsIntoMoney($oil_number,$staff_id=0)
    {
        if(empty($oil_number)){
            return [];
        }
        $is_exist_oil_number = 'select prepaid_no,staff_id,oil_number from staff_mileage_record_prepaid_info where oil_number = :oil_number and state = 0  ';
        $bind['oil_number'] = $oil_number;
        if($staff_id){
            $is_exist_oil_number .= " and staff_id = :staff_id";
            $bind['staff_id'] = $staff_id;
        }
        $result = $this->getDI()->get('db')->fetchOne($is_exist_oil_number ,\Phalcon\Db::FETCH_ASSOC,$bind);
        return $result ?? [];
    }


    /**
     * 创建车辆信息 userinfo 里只有 id 和 job_title 如果要用别的 记得在外层新增参数
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addVehicleInfoS($paramIn , $userinfo=[],$operate_staff_id = '')
    {
        $returnData['data'] = [];

        // 整合入表字段
        $vehicleData = $this->filterVehicleData($paramIn, $userinfo);
        $this->getDI()->get('logger')->write_log('kit_add_vehicle_info vehicleData: '.json_encode($vehicleData, JSON_UNESCAPED_UNICODE), 'info');

        //查询验证是否有数据
        $vehicleInfo = $this->vehicle->getVehicleInfoR($vehicleData['uid']);
        if (!empty($vehicleInfo) && ($vehicleInfo['approval_status'] == VehicleInfoEnums::APPROVAL_PENDING_CODE)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0006'));

        }

        // 验证车牌号 是否 与 其他在职人的车牌号重复
        if ($vehicleData['vehicle_type'] != VehicleInfoEnums::VEHICLE_TYPE_TRUCK_CODE  && $vehicleData['plate_number'] && $this->checkPlateNumberIsExist($vehicleData['plate_number'], $userinfo['id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0001'));
        }

        // 验证发动机号 是否 与 其他在职人的发动机号重复
        if ($vehicleData['vehicle_type'] != VehicleInfoEnums::VEHICLE_TYPE_TRUCK_CODE && $this->checkEngineNoIsExist($vehicleData['engine_number'], $userinfo['id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0003'));
        }

        // 验证开始用车日期, 不得早于入职日期
        $hire_date = $this->getStaffHireDate($userinfo['id']);
        if ($vehicleData['vehicle_source'] == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE && !empty($vehicleData['vehicle_start_date']) && $vehicleData['vehicle_start_date'] < $hire_date) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0007'));
        }

        unset($vehicleData['unit_price']);//2021-10-18 钟杏 、雷光  by不计算单价

        if($this->isVan){
            if(empty($vehicleInfo)){
                //校验油卡卡号是否已经存在
                if(!empty($vehicleData['oil_number'])){
                    if($vehicleData['vehicle_type'] != VehicleInfoEnums::VEHICLE_TYPE_TRUCK_CODE &&  $exist_staff_id = $this->checkOilNoIsExist($vehicleData['oil_number'],$userinfo['id'])){
                        return $this->checkReturn(-3,str_replace("XXX",$exist_staff_id, $this->getTranslation()['oil_number_008']));
                    }
                    $vehicleData['oil_subsidy_type'] = VehicleInfoEnums::OIL_SUBSIDY_TYPE_2;
                    $vehicleData['is_open'] = VehicleInfoEnums::OIL_CARD_STATUS_OPENED;
                    $add_hour = $this->getDI()['config']['application']['add_hour'];
                    $vehicleData['open_date'] = gmdate('Y-m-d',time()+$add_hour*3600+86400);//next day ;
                }else{
                    $vehicleData['oil_subsidy_type'] = VehicleInfoEnums::OIL_SUBSIDY_TYPE_1;
                }
            }else{
                if(!empty($vehicleData['oil_number'])){

                    if($vehicleData['vehicle_type'] != VehicleInfoEnums::VEHICLE_TYPE_TRUCK_CODE &&  $exist_staff_id = $this->checkOilNoIsExist($vehicleData['oil_number'],$userinfo['id'])){
                        return $this->checkReturn(-3,str_replace("XXX",$exist_staff_id, $this->getTranslation()['oil_number_008']));
                    }

                    if(!empty($vehicleInfo['oil_number'])){

                        // 油卡号是否变更，若变更，则需校验旧油卡是否未充值
                        $uid = $vehicleData['vehicle_type'] == VehicleInfoEnums::VEHICLE_TYPE_TRUCK_CODE  ? 0 : $userinfo['id'];
                        $oil_checkout = $this->getCheckOilNumberByIsIntoMoney($vehicleInfo['oil_number'],$uid);
                        if(!empty($oil_checkout) && ($oil_checkout['oil_number'] != $vehicleData['oil_number'])){
                            return $this->checkReturn(-3, $this->getTranslation()['fuel_manage_oil_number_no_checkout']);
                        }
                    }else{
                        $vehicleData['oil_subsidy_type'] = VehicleInfoEnums::OIL_SUBSIDY_TYPE_2;
                        $vehicleData['is_open'] = VehicleInfoEnums::OIL_CARD_STATUS_OPENED;
                        $add_hour = $this->getDI()['config']['application']['add_hour'];
                        $vehicleData['open_date'] = gmdate('Y-m-d',time()+$add_hour*3600+86400);//next day ;
                        $this->getDI()->get('logger')->write_log('kit_add_vehicle_info oil_subsidy_type form 1 to 2 : '.json_encode($vehicleData, JSON_UNESCAPED_UNICODE), 'info');
                    }
                }
            }

            if(isset($vehicleData['oil_number'])&&$vehicleData['oil_number']){
                if(!($vehicleData['oil_img']&&$vehicleData['oil_company'])){
                    return $this->checkReturn(-3, 'oil_info_error');
                }
            }else{
                if(!(empty($vehicleData['oil_img'])&&empty($vehicleData['oil_company']))){
                    return $this->checkReturn(-3, 'oil_info_error');
                }
            }
        }

        if (empty($vehicleInfo)) {
            $vehicleData['formal_data'] = '';
            $vehicleData['creator_id'] = $userinfo['id'];
            $vehicleData['create_channel'] = VehicleInfoEnums::VEHICLE_ADD_CHANNEL_KIT;

            //kit首次提交油卡信息后 没有提交油卡信息时，不设置默认值
            if($vehicleData['oil_number']){
                $add_hour = $this->getDI()['config']['application']['add_hour'];
                $vehicleData['oil_subsidy_type'] = VehicleInfoEnums::OIL_SUBSIDY_TYPE_2;//补贴到油卡
                $vehicleData['is_open'] = VehicleInfoEnums::OIL_CARD_STATUS_OPENED;//默认开通
                $vehicleData['open_date'] = gmdate('Y-m-d', time() + $add_hour * 3600+86400); // 日期;//开通日期 次日
                $vehicleData['balance'] = 0;//初始金额

            }
            $res = $this->vehicle->addVehicleInfoR($vehicleData);
        } else {
            $res = $this->vehicle->updateVehicleInfo($vehicleData);
        }

        if ($res) {
            // 车辆信息日志
            $vehicleLogData = [];
            $vehicleLogData['staff_id'] = $vehicleData['uid'];
            $vehicleLogData['operate_staff_id'] = $operate_staff_id ?? $vehicleData['uid'];
            $vehicleLogData['text'] = json_encode($vehicleData, JSON_UNESCAPED_UNICODE);
            $this->vehicle->addVehicleInfoLog($vehicleLogData);

            $returnData['data'] = $vehicleData['uid'];
            return $this->checkReturn($returnData);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }
    }

    /**
     * 获取油卡充值记录
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffRecord($mouth, $staff_id)
    {
        $beign = date('Y-m-d', mktime(0, 0, 0, $mouth, 1, date('Y')));
        $end = date('Y-m-d', mktime(23, 59, 59, $mouth + 1, 0, date('Y')));
        $sql = "SELECT	s_mr.staff_info_id,
                mileage_date,
                start_kilometres,
                end_kilometres,
                prepaid_slip_no,
                s_mr.money,
                LEFT ( s_mr.updated_at, 10 ) AS updated_at,
                s_mr_info.recharge_at ,
                s_mr_info.state ,
                v_info.unit_price
            FROM
                staff_mileage_record as s_mr
            left join staff_mileage_record_prepaid_info as s_mr_info on s_mr.prepaid_slip_no = s_mr_info.prepaid_no and s_mr_info.`staff_id` = s_mr.`staff_info_id`
            left join vehicle_info as v_info on s_mr.staff_info_id = v_info.uid
            WHERE
                s_mr.staff_info_id = {$staff_id} 
                AND mileage_date >= '{$beign}' 
                AND mileage_date <= '{$end}';
        ";

        $info_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (empty($info_data)) {
            return [];
        }
        $list = [];
        foreach ($info_data as $key => $value) {
            $list[$key]['created_at'] = $value['mileage_date'];
            $list[$key]['kilometres'] =
                ($value['end_kilometres'] > 0 && $value['start_kilometres'] > 0) && ($value['end_kilometres'] > $value['start_kilometres']) ?
                bcdiv(bcsub($value['end_kilometres'], $value['start_kilometres']), 1000) : "NA";
            $list[$key]['fuel_prepaid'] = 0;
            $list[$key]['fuel_subsidies'] = 0; //补贴金额;
            $list[$key]['buy_data'] = "NA";
            $list[$key]['fuel_prepaid'] = bcdiv($value['money'],100,2); //预计补贴金额
            if($info_data[$key]['state']){
                $list[$key]['fuel_subsidies'] = bcdiv($value['money'],100,2); //补贴金额;
                $list[$key]['buy_data'] = $value['updated_at'];
            }
            $list[$key]['recharge_at'] = $value['recharge_at'] ?? "NA";
        }
        return $list;
    }


    /**
     * 格式化车辆信息详情
     * @param array $vehicle_info
     * @param array $paramIn
     * @return mixed
     */
    protected function handleVehicleInfo(array $vehicle_info, array $paramIn)
    {
        if (empty($paramIn)) {
            return [];
        }

        // 为空, 补充默认字段 及 默认值
        if (empty($vehicle_info)) {
            // 车辆品牌及车辆型号、购买日期、油卡公司 van
            $vehicle_info['vehicle_brand'] = '';
            $vehicle_info['vehicle_brand_text'] = '';
            $vehicle_info['vehicle_model'] = '';
            $vehicle_info['vehicle_model_text'] = '';
            //$vehicle_info['buy_date'] = null;
            $vehicle_info['oil_number'] = '';
            $vehicle_info['oil_company'] = 0;

            // 车辆照片/机动车登记证 &&
            $vehicle_info['vehicle_img'] = [];
            $vehicle_info['registration_certificate_img'] = '';

            $vehicle_info['vehicle_registration_number'] = '';//机动车登记编号 new
            $vehicle_info['vehicle_registration_date'] = null;//机动车登记日期 new
            $vehicle_info['vehicle_proof_number'] = '';//车辆凭证号码 new
            $vehicle_info['vehicle_proof_img'] = '';//车辆凭证图片 new
            $vehicle_info['car_long'] = '';//车辆 new
            $vehicle_info['car_width'] = '';//车辆 new
            $vehicle_info['car_high'] = '';//车辆 new


            // 车辆保险 &&
            $vehicle_info['insurance_policy_number'] = '';
            $vehicle_info['insurance_start_date'] = null;
            $vehicle_info['insurance_end_date'] = null;

            // 车辆税 &&
            $vehicle_info['vehicle_tax_expiration_date'] = null;
            $vehicle_info['vehicle_tax_certificate_img'] = '';

            // 驾照信息 &&
            $vehicle_info['driver_license_type'] = '';
            $vehicle_info['driver_license_type_other_text'] = '';
            $vehicle_info['driver_license_start_date'] = null;
            $vehicle_info['driver_license_end_date'] = null;
            $vehicle_info['driving_licence_img'] = '';
            $vehicle_info['driving_license_vehicle_restrictions'] = [];//驾照车辆限制 new
            $vehicle_info['driving_license_vehicle_restrictions_str'] = [];//驾照车辆限制 new

        } else {
            // 删除无需字段
            unset($vehicle_info['id']);
            unset($vehicle_info['deleted']);
            unset($vehicle_info['money']);
            unset($vehicle_info['is_open']);
            unset($vehicle_info['open_date']);
            unset($vehicle_info['updated_at']);
            unset($vehicle_info['created_at']);
            unset($vehicle_info['is_cut_money']);
            unset($vehicle_info['balance']);
            unset($vehicle_info['unit_price']);
            unset($vehicle_info['approval_staff_id']);
            unset($vehicle_info['approval_time']);
            unset($vehicle_info['creator_id']);
            unset($vehicle_info['editor_id']);
            unset($vehicle_info['create_channel']);
            unset($vehicle_info['buy_date']);
        }

        // 车辆类型, 职位优先
//        var_dump($user_info['job_title']);exit;
        $vehicle_info['vehicle_type'] = VehicleInfoEnums::JOB_VEHICLE_TYPE_REL_CODE[$paramIn['job_title']];
        $vehicle_info['vehicle_type_label'] = VehicleInfoEnums::VEHICLE_TYPE_ITEM[$vehicle_info['vehicle_type']];

        // 车辆来源: 同步fbi-hr_is数据
        if (empty($vehicle_info['vehicle_source'])) {
            $hr_staff_info = HrStaffInfoServer::getUserInfoByStaffInfoId($paramIn['id'], 'vehicle_source, vehicle_use_date');
            $hr_staff_info = $hr_staff_info ? $hr_staff_info->toArray() : [];

            $vehicle_info['vehicle_source'] = $hr_staff_info['vehicle_source'] ?? VehicleInfoEnums::VEHICLE_SOURCE_PERSONAL_CODE;
            $vehicle_info['vehicle_start_date'] = $hr_staff_info['vehicle_use_date'] ?? null;
        }

        $vehicle_info['vehicle_source_label'] = $vehicle_info['vehicle_source'] ? $this->getTranslation()->_(VehicleInfoEnums::VEHICLE_SOURCE_ITEM[$vehicle_info['vehicle_source']]) : '';

        // 车牌号 hr-is取默认值, 如没有，则再从whr_is取默认值
        if (empty($vehicle_info['plate_number'])) {
            $vehicle_info['plate_number'] = (new StaffRepository())->getAvatar($paramIn['id'], 'CAR_NO');
        }

        // 上牌地点/发动机号码/驾照号码 whr-is取默认值
        if (
            empty($vehicle_info['plate_number'])
        ||
            empty($vehicle_info['license_location'])
        ||
            empty($vehicle_info['engine_number'])
        ||
            empty($vehicle_info['driver_license_number'])
        ) {

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['entry' => HrEntryModel::class]);
            $builder->innerJoin(HrEconomyAbilityModel::class,'entry.resume_id = hr.resume_id', 'hr');
            $builder->where('entry.staff_id = :staff_id:', ['staff_id' => $paramIn['id']]);
            $builder->columns([
                'hr.car_number',// 车牌号
                'hr.driver_number',//驾照号
                'hr.place_cards',//上牌地点
                'hr.car_engine_number',//发动机号
            ]);
            $win_staff_info = $builder->getQuery()->getSingleResult();
            if (!empty($win_staff_info)) {
                $vehicle_info['plate_number'] = !empty($vehicle_info['plate_number']) ? $vehicle_info['plate_number'] : $win_staff_info->car_number ?? '';
                $vehicle_info['license_location'] = !empty($vehicle_info['license_location']) ? $vehicle_info['license_location'] : $win_staff_info->place_cards ?? '';
                $vehicle_info['engine_number'] = !empty($vehicle_info['engine_number']) ? $vehicle_info['engine_number'] : $win_staff_info->car_engine_number ?? '';
                $vehicle_info['driver_license_number'] = !empty($vehicle_info['driver_license_number']) ? $vehicle_info['driver_license_number'] : $win_staff_info->driver_number ?? '';
            }
        }


        $vehicle_setting = VehicleInfoEnums::CONFIG_VEHICLE_INFO;

        $vehicle_info['oil_company']=intval($vehicle_info['oil_company']);

        // 油卡企业
        $vehicle_info['oil_company_label'] = '';
        if (!empty($vehicle_info['oil_company'])) {
            $oil_company_conf = array_column($vehicle_setting['oil_company'], 'label', 'value');
            $vehicle_info['oil_company_label'] = $oil_company_conf[$vehicle_info['oil_company']] ?? '';
        }

        // 车辆品牌/车辆型号
        $vehicle_info['vehicle_brand_label'] = '';
        $vehicle_info['vehicle_model_label'] = '';
        if (in_array($vehicle_info['vehicle_type'],[VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE,VehicleInfoEnums::VEHICLE_TYPE_TRUCK_CODE]) && !empty($vehicle_info['vehicle_brand'])) {
            $vehicle_brand_conf = array_column($vehicle_setting['vehicle_brand'], null, 'value');
            $vehicle_model_conf = array_column($vehicle_brand_conf[$vehicle_info['vehicle_brand']]['data'], 'label', 'value');

            $vehicle_info['vehicle_brand_label'] = $vehicle_brand_conf[$vehicle_info['vehicle_brand']]['label'] ?? '';
            $vehicle_info['vehicle_model_label'] = !empty($vehicle_info['vehicle_model']) ? $vehicle_model_conf[$vehicle_info['vehicle_model']] : '';
        }

        // 车型
        $vehicle_info['vehicle_size'] = $vehicle_info['vehicle_size'] ?? '';

        $vehicle_info['vehicle_size_label'] = self::getVehicleSize(true)[$vehicle_info['vehicle_size']]?? '';
        //
        $vehicle_info['vehicle_img'] = !empty( $vehicle_info['vehicle_img'])? explode("\n", $vehicle_info['vehicle_img']):[];


        // 油类型
        $vehicle_info['oil_type'] = $vehicle_info['oil_type'] ?? '';
        $vehicle_info['oil_type_label'] = $vehicle_info['oil_type'] ? $vehicle_setting['oil_type'][$vehicle_info['oil_type']] : '';

        // 驾照图片(两张) &&
        $vehicle_info['driving_licence_img_item'] = [];
        if (!empty($vehicle_info['driving_licence_img'])) {
            $vehicle_info['driving_licence_img_item'] = explode("\n", $vehicle_info['driving_licence_img']);
        }

        // 驾照类型 &&
        $vehicle_info['driver_license_type_label'] = '';
        if (!empty($vehicle_info['driver_license_type'])) {
            foreach (VehicleInfoEnums::DRIVER_LICENSE_TYPE_ITEM as $license_k => $license_v) {
                $vehicle_info['driver_license_type_label'] = $license_v;
            }
        }

        //驾照车辆限制
        $vehicle_info['driving_license_vehicle_restrictions_str'] = empty($vehicle_info['driving_license_vehicle_restrictions']) ? '': $this->getDrivingLicenseVehicleRestrictionsFromIds( $vehicle_info['driving_license_vehicle_restrictions']) ;
        $vehicle_info['driving_license_vehicle_restrictions'] = empty($vehicle_info['driving_license_vehicle_restrictions'])?[]:explode(',', $vehicle_info['driving_license_vehicle_restrictions']) ;

        // 审核状态
        $vehicle_info['approval_status'] = $vehicle_info['approval_status'] ?? VehicleInfoEnums::APPROVAL_UN_SUBMITTED_CODE;

        // 员工入职日期
        $vehicle_info['staff_hire_date'] = $this->getStaffHireDate($paramIn['id']);

        return $vehicle_info;
    }

    /**
     * 根据驾照车辆限制id 获取名称
     * @param string $ids
     * @return string
     */
    private function getDrivingLicenseVehicleRestrictionsFromIds(string $ids)
    {
        $names = [];
        $ids_arr  = explode(',',$ids);
        $drivingLicenseVehicleRestrictions = VehicleInfoEnums::DRIVING_LICENSE_VEHICLE_RESTRICTIONS;
        foreach ($drivingLicenseVehicleRestrictions as $item)
        {
            if(in_array($item['value'],$ids_arr)){
                $names[] = $item['label'];
            }
        }
        return implode(',',$names);
    }

    /**
     * 提取不同职位需入库的字段
     * @param array $vehicle_data
     * @param array $user_info
     * @return array $data
     */
    protected function filterVehicleData(array $vehicle_data, array $user_info)
    {
        // 公共字段
        $data = [
            'uid' => $user_info['id'],
            'vehicle_source' => $vehicle_data['vehicle_source'],
            'plate_number' => $vehicle_data['plate_number'],
            'license_location' => $vehicle_data['license_location'],
            'registration_certificate_img' => $vehicle_data['registration_certificate_img'],
            'vehicle_img' => implode("\n",$vehicle_data['vehicle_img']),
            'insurance_policy_number' => $vehicle_data['insurance_policy_number'],
            'insurance_start_date' => $vehicle_data['insurance_start_date'],
            'insurance_end_date' => $vehicle_data['insurance_end_date'],
            'vehicle_tax_expiration_date' => $vehicle_data['vehicle_tax_expiration_date'],
            'vehicle_tax_certificate_img' => $vehicle_data['vehicle_tax_certificate_img'],
            'driver_license_type' => $vehicle_data['driver_license_type'],
            'driver_license_type_other_text' => $vehicle_data['driver_license_type'] != 100 ? '' : $vehicle_data['driver_license_type_other_text'] ?? '',
            'driver_license_number' => $vehicle_data['driver_license_number'],
            'driver_license_start_date' => $vehicle_data['driver_license_start_date'],
            'driver_license_end_date' => $vehicle_data['driver_license_end_date'],
            'driving_licence_img' => implode("\n", $vehicle_data['driving_licence_img_item']),
            'vehicle_type' => VehicleInfoEnums::VEHICLE_TYPE_BIKE_CODE,
            'engine_number' => $vehicle_data['engine_number'],

            // 重置审核信息
            'approval_status' => VehicleInfoEnums::APPROVAL_PENDING_CODE,
            'approval_staff_id' => '',
            'approval_time' => null,
            'approval_remark' => '',

            'editor_id' => $user_info['id'],
            //new add
            'vehicle_proof_number' => $vehicle_data['vehicle_proof_number']??'',//车辆凭证号码
            'vehicle_proof_pay_date' => $vehicle_data['vehicle_proof_pay_date']??null,//车辆凭证付款时间
            'vehicle_proof_img' => $vehicle_data['vehicle_proof_img']??'',//车辆凭证图片
            'vehicle_registration_date' => $vehicle_data['vehicle_registration_date']??null,//机动车登记日期
            'vehicle_registration_number' => $vehicle_data['vehicle_registration_number']??'',//机动车登记编号
            'driving_license_vehicle_restrictions' => implode(',',$vehicle_data['driving_license_vehicle_restrictions']),//驾照车辆限制如1,3,4

        ];

        // 用车开始日期
        if ($vehicle_data['vehicle_source'] == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
            $data['vehicle_start_date'] = $vehicle_data['vehicle_start_date'];
        }

        // van truck 特有字段
        if (in_array($user_info['job_title'], VehicleInfoEnums::VAN_OR_TRUCK_JOB_GROUP_ITEM)) {
            $this->isVan = true;
            $data['vehicle_brand'] = $vehicle_data['vehicle_brand'];
            $data['vehicle_brand_text'] = $vehicle_data['vehicle_brand'] != 100 ? '' : $vehicle_data['vehicle_brand_text'] ?? '';
            $data['vehicle_model'] = $vehicle_data['vehicle_model'];
            $data['vehicle_model_text'] = $vehicle_data['vehicle_model'] != 100 ? '' : $vehicle_data['vehicle_model_text'] ?? '';
            $data['vehicle_size'] = $vehicle_data['vehicle_size'];
            $data['oil_type'] = $vehicle_data['oil_type'];
            $data['oil_number'] = $vehicle_data['oil_number']??'';
            $data['oil_company'] = $vehicle_data['oil_company']??0;
            $data['oil_img'] = $vehicle_data['oil_img']??'';
            $data['vehicle_type'] = $user_info['job_title'] == VehicleInfoEnums::JOB_TRUCK_TITLE_ID? VehicleInfoEnums::VEHICLE_TYPE_TRUCK_CODE : VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE;
            if($vehicle_data['vehicle_brand'] == 100 || $vehicle_data['vehicle_model'] == 100){
                $data['car_long'] = $vehicle_data['car_long'];
                $data['car_width'] = $vehicle_data['car_width'];
                $data['car_high'] = $vehicle_data['car_high'];
            }else{
                $vehicleLWH = $this->getVehicleLWH($vehicle_data['vehicle_brand'],$vehicle_data['vehicle_model']);
                $data['car_long'] = $vehicleLWH['car_long'];
                $data['car_width'] = $vehicleLWH['car_width'];
                $data['car_high'] = $vehicleLWH['car_high'];
            }

        } elseif ($user_info['job_title'] == VehicleInfoEnums::JOB_TRICYCLE_TITLE_ID) {

            $data['vehicle_type'] = VehicleInfoEnums::VEHICLE_TYPE_TRICYCLE_CODE;
        }
       // bike 字段
        if(in_array($user_info['job_title'], VehicleInfoEnums::BIKE_JOB_GROUP_ITEM)){
            $data['motorcycle_side_bag_proof_num'] = $vehicle_data['motorcycle_side_bag_proof_num'] ?? '';
            $data['motorcycle_side_bag_proof_img'] = $vehicle_data['motorcycle_side_bag_proof_img'] ?? '';
        }
        return $data;
    }

    /**
     * 获取车辆的长宽高
     * @param $vehicle_brand
     * @param $vehicle_model
     * @return string[]
     */
    private function getVehicleLWH($vehicle_brand,$vehicle_model)
    {
        $lwh = [
            'car_long'=>'0',
            'car_width'=>'0',
            'car_high'=>'0',
        ];
        $info  = VehicleInfoEnums::CONFIG_VEHICLE_INFO['vehicle_brand'];
        foreach ($info as $item) {
            if($item['value'] == $vehicle_brand){
                foreach ($item['data'] as $vm) {
                    if($vm['value'] == $vehicle_model){
                        $lwh['car_long']  = $vm['car_long'];
                        $lwh['car_width']  = $vm['car_width'];
                        $lwh['car_high']  = $vm['car_high'];
                        return $lwh;
                    }
                }
            }
        }
        return  $lwh;
    }



    /**
     * shell公司油卡是否有效
     * @param $oil_card 油卡号
     * @return bool
     */
    public function validationOilCard($oil_card){

        $settingEnv = new SettingEnvServer();
        $switch =$settingEnv->getSetVal('validation_oil_card');

        if($switch ==0)//无需验证
            return true;

        $shell_api_path = $settingEnv->getSetVal('shell_api_oil_card');
        $shell_api_key = $settingEnv->getSetVal('shell_api_key');
        $shell_api_secret = $settingEnv->getSetVal('shell_api_secret');

        $post_url = $shell_api_path.'/card/search';
        $base64Str = base64_encode($shell_api_key.':'.$shell_api_secret);

        $header = array(
            "Authorization: Basic {$base64Str}",
            "apikey: {$shell_api_key}",
            "Content-Type: application/json"
        );

        $post_params['ColCoCode'] = $settingEnv->getSetVal('shell_colco_code');
        $post_params['ColCoId'] = $settingEnv->getSetVal('shell_col_cold');
        $post_params['ColCoCountryCode'] = 'PH';
        $post_params['PayerNumber'] = $settingEnv->getSetVal('shell_payer_number');
        $post_params['CardStatus'][] = 'ACTIVE';
        $post_params['AccountNumber'] = $settingEnv->getSetVal('shell_account_number');
        $post_params['IncludeCards'][] = ['PAN'=>$oil_card];
        $post_params['PAN'] = $oil_card;

        $send_params = json_encode($post_params);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $post_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 55,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS =>$send_params,
            CURLOPT_HTTPHEADER => $header,
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        if(!$response){
            $this->getDI()->get('logger')->write_log('validationOilCard: timeout params:'.json_encode($post_params,JSON_UNESCAPED_UNICODE), 'notice');
            return false;
        }

        $decode = json_decode($response ,true);
        $this->getDI()->get('logger')->write_log('validationOilCard: params:'.json_encode($post_params,JSON_UNESCAPED_UNICODE).' return: '.json_encode($decode, JSON_UNESCAPED_UNICODE), 'info');

        if(!isset($decode['Cards']) or !$decode['Cards'])
            return false;

        return true;
    }

    /**
     * @descriptio 创建审批
     * @param array $paramIn
     * @param $userinfo
     * @return array
     */
    public function addVehicleS($paramIn = [], $userinfo): array
    {
        $paramIn['staff_info_id'] = $userinfo['id'];
        $ac                       = new ApiClient('bi_rpcv2', '', 'mileage.repair_vehicle', $this->lang);
        $ac->setParams($paramIn);
        $result = $ac->execute();

        $this->logger->write_log("addVehicleS result" . json_encode($result), 'info');

        if (empty($result)) {
            throw new ValidationException($this->getTranslation()->_('please try again'));
        }
        if ($result['result']['code'] != ErrCode::SUCCESS) {
            throw new ValidationException($result['result']['msg']);
        }

        return $this->checkReturn([]);
    }

    /**
     * 修改记录状态记录日志
     * @Return  array
     */
    public function updateVehicleStatus($paramIn, $userinfo): array
    {
        //获取请求参数
        $auditId = $this->processingDefault($paramIn, 'id');
        $status = $this->processingDefault($paramIn, 'status');
        $staffId = $this->processingDefault($userinfo, 'id');
        $rejectReason = $this->processingDefault($paramIn, 'reject_reason');

        //获取记录信息
        $result = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('please try again'));
        }
        $result = $result->toArray();

        //当前状态如果已经审批，申请人不可撤销
        if ($result['state'] != enums::APPROVAL_STATUS_PENDING && $status == enums::APPROVAL_STATUS_CANCEL) {
            return $this->checkReturn(-3, $this->getTranslation()->_('2206'));
        }
        //申请人撤销后,审批人不能审批
        if ($result['state'] == enums::APPROVAL_STATUS_CANCEL) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1016'));
        }

        $this->getDI()->get('db')->begin();
        try {
            //同意或者驳回等分开处理
            if ($status == enums::APPROVAL_STATUS_APPROVAL) {

                //同意
                $server = new ApprovalServer($this->lang, $this->timeZone);
                $server->approval($auditId, AuditListEnums::APPROVAL_TYPE_VEHICLE, $staffId, null);
            } else if ($status == enums::APPROVAL_STATUS_REJECTED) {

                //驳回
                $server = new ApprovalServer($this->lang, $this->timeZone);
                $server->reject($auditId, AuditListEnums::APPROVAL_TYPE_VEHICLE, $rejectReason, $staffId);
            } else {
                //撤销
                $server = new ApprovalServer($this->lang, $this->timeZone);
                $server->cancel($auditId, AuditListEnums::APPROVAL_TYPE_VEHICLE, $rejectReason, $staffId);
            }

        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log('update fleet failure:' . $e->getMessage(), 'error');
            return $this->checkReturn([]);
        }
        $this->getDI()->get('db')->commit();

        return $this->checkReturn([]);
    }

    /**
     * @description 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     * @throws BusinessException
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //获取详情数据
        $result = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($result)) {
            throw new BusinessException('invalid data');
        }
        $result     = $result->toArray();
        $detailInfo = $this->detail($result['serial_no']);

        //组织详情数据
        $detailLists = [
            'apply_parson'       => $detailInfo['apply_parson'],
            'apply_department'   => $detailInfo['apply_department'],
            'mileage_date'       => $detailInfo['mileage_date'],
            'start_kilometres'   => $detailInfo['start_kilometres'],
            'started_img'        => $detailInfo['started_img'],
            'end_kilometres'     => $detailInfo['end_kilometres'],
            'end_img'            => $detailInfo['end_img'],
        ];
        $returnData['data']['detail'] = $this->format($detailLists);

        $auditListRepo = new AuditlistRepository($this->lang, $this->timeZone);
        $add_hour  = $this->config->application->add_hour;
        $data      = [
            'title'       => $auditListRepo->getAudityType(AuditListEnums::APPROVAL_TYPE_VEHICLE),
            'id'          => $result['id'],
            'staff_id'    => $result['submitter_id'],
            'type'        => AuditListEnums::APPROVAL_TYPE_VEHICLE,
            'created_at'  => date('Y-m-d H:i:s', (strtotime($result['created_at']) + $add_hour * 3600)),
            'updated_at'  => date('Y-m-d H:i:s', (strtotime($result['updated_at']) + $add_hour * 3600)),
            'status'      => $result['state'],
            'status_text' => $auditListRepo->getAuditStatus('10' . $result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        $returnData['data']['head'] = $data;

        return $returnData;
    }

    /**
     * @description 获取详情
     * @param $serial_no
     * @return array
     */
    public function detail($serial_no): array
    {
        $data['serial_no'] = $serial_no;
        $ac                       = new ApiClient('bi_rpcv2', '', 'approval.get_repair_info', $this->lang);
        $ac->setParams($data);
        $result = $ac->execute();

        $this->logger->write_log("vehicle detail result" . json_encode($result), 'info');

        return $result['result']['data'] ?? [];
    }

    /**
     * @description 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info = SystemExternalApprovalModel::findFirst($auditId);
        if (!empty($info)) {
            $summary = json_decode($info->summary, true);
        } else {
            $summary = json_decode([], true);
        }
        return $summary;
    }

    /**
     * @description 回调接口
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return mixed|void
     * @throws \Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('setProperty  没有找到数据  '.$auditId);
            }
            $SystemExternalApprovalModel->state      = $state;
            $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());
            $SystemExternalApprovalModel->save();

            //向bi同步数据
            $data = [
                'serial_no'              => $SystemExternalApprovalModel->serial_no,
                'status'                 => $state,
                'approval_staff_info_id' => $extend['staff_id'],
            ];
            $ac   = new ApiClient('bi_rpcv2', '', 'approval.approval_repair_mileage', $this->lang);
            $ac->setParams($data);
            $result = $ac->execute();

            $this->logger->write_log("setProperty result" . json_encode($result), 'info');
        }
        return true;
    }

    /**
     * @description  获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $auditInfo = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return [];
        }
        $parameters = json_decode($auditInfo->approval_parameters, true);
        return $parameters ?? [];
    }

    /**
     * 里程信息【Van快递员新增“修改里程表数”使用】
     * @param array $paramIn
     * @param array $userinfo
     * @return array
     */
    public function getMileageModifyS($paramIn = [], $userinfo = []): array
    {
        //向bi同步数据
        $data = [
            'staff_info_id' => $userinfo['id'],
            'mileage_date'  => $paramIn['mileage_date'],
        ];
        $ac   = new ApiClient('bi_rpcv2', '', 'mileage.get_modify_info', $this->lang);
        $ac->setParams($data);
        $result = $ac->execute();

        if (empty($result) || $result['code'] != 1) {
            return $this->checkReturn([]);
        }

        $returnData['data']         = $result['data']['record_count'];
        $returnData['data']['list'] = $result['data']['list'];

        return $this->checkReturn($returnData);
    }

    /**
     * 创建【Van快递员新增“修改里程表数”使用】
     * @param array $paramIn
     * @param $userinfo
     * @return array
     */
    public function addMileageModifyS($paramIn = [], $userinfo): array
    {
        //向bi同步数据
        $data = [
            'staff_info_id'    => $userinfo['id'],
            'mileage_date'     => $paramIn['mileage_date'],
            'start_kilometres' => $paramIn['start_kilometres'],
            'end_kilometres'   => $paramIn['end_kilometres'],
        ];
        $ac   = new ApiClient('bi_rpcv2', '', 'mileage.modify_vehicle', $this->lang);
        $ac->setParams($data);
        $ac->execute();

        return $this->checkReturn([]);
    }
}


