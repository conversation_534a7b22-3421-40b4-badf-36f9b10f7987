<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;


//转岗确认单
use FlashExpress\bi\App\Enums\PdfEnums;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\formPdfServer;

class JobTransferAcknowledgementServer extends BaseServer
{

    /**
     * 获取确认单模板
     * @param $tmpl_type
     * @return  string
     */
    public function getAcknowledgementTmpl($tmpl_type = ''): string
    {
        $filePath = '/views/job_transfer/ph_transfer_confirm.ftl';
        $upload_result = OssHelper::uploadFile(APP_PATH . $filePath, true);
        return $upload_result['object_url'];
    }

    /**
     * 生成转岗确认单
     * @param $params
     * @return string
     * @throws \Exception
     */
    public function generateJobTransferPdf($params): string
    {
        $params['template_cnf']        = PdfEnums::RESPONSE_TYPE_DIRECT;
        $params['is_salary_with_unit'] = true;
        $params['sign_url']            = $params['sign_url'] ?? '';
        $confirmDate                   = $params['confirm_date'] ?? '';

        //获取转岗确认单
        $transferData   = (new JobTransferConfirmServer($this->lang,
            $this->timeZone))->getConfirmDetail($params);
        $transferDetail = $transferData['data'];
        $tmplPathUrl    = $this->getAcknowledgementTmpl();

        //组织 pdf 数据，生成 pdf
        $temp_params    = [
            'before_store_name' => $transferDetail['before_store_name'],
            'after_store_name'  => $transferDetail['after_store_name'],
            'staff_info_id'     => $transferDetail['staff_info_id'],
            'hire_date'         => $transferDetail['hire_date'],
            'staff_full_name'   => $transferDetail['staff_name'],
            'confirm_date'      => !empty($confirmDate)
                ? $confirmDate
                : $transferDetail['confirm_date'],
            'confirm_list'      => $transferDetail['confirm_list'],
            'after_date'        => $transferDetail['after_date'],
        ];
        $pdf_header_footer_setting = [
            'displayHeaderFooter' => true,
            'headerTemplate'      => $transferDetail['header_template'],
            'footerTemplate'      => $transferDetail['footer_template'],
        ];
        $sign_image             = [
            ['name' => 'staff_sign_url', 'url' => $params['sign_url'] ?: $transferDetail['staff_sign_url']],
        ];

        try {
            $pdf_file_data = (new formPdfServer())->generatePdf($tmplPathUrl, $temp_params, $sign_image,
                'job_transfer_confirm',
                $pdf_header_footer_setting, 'attchment');
        } catch (\Exception $e) {
            echo $e->getMessage() . $e->getTraceAsString();
        }

        if (empty($pdf_file_data['object_url'])) {
            $this->logger->write_log(['makeTerminationFileUrl  error', $tmplPathUrl, $params]);
            return '';
        }
        return $pdf_file_data['object_url'];
    }
}