<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/11/20
 * Time: 下午2:51
 */


namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\BackyardServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\MessageServer;
use FlashExpress\bi\App\Server\ResumeRecommendServer;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;

class BackyardServer extends GlobalBaseServer
{


    protected function getPunchOutMsg($staff_id){
        $db  = $this->getDI()->get('db_coupon_r');
        $sql = "select mc.id, mc.category, mc.category_code, message_content_id, m.related_id, m.message
                    from message_courier mc 
                    inner join  message_content as m on mc.message_content_id = m.id
                    where mc.staff_info_id = :staff_info_id and mc.read_state = 0 and mc.is_del = 0 
                    order by mc.created_at desc";

        return $db->query($sql, ['staff_info_id' => $staff_id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 下班卡 验证消息
     * @param $param
     * @return array
     */
    public function punch_out_msg($param)
    {
        $msg_ids = $this->get_new_msg($param);
        if (empty($msg_ids)) {
            return $msg_ids;
        }
        return $this->filterProbationFollowUpMsg($msg_ids);
    }

    /**
     * 获取红点数量.
     * @param $req
     * @return string[]
     */
    public function getRedDotsNum($req){
        //CEO回复未读 & 消息未读
        $unReadMsgRes = $this->un_read(['staff_id' => $req['staff_id']]);
        $ceoUnReadNum = $unReadMsgRes['ceo_unread'] ?? 0;
        //审批
        $unAuditNumRes = $this->getWaitAuditData($req);
        //个人资产
        //$unMyAssetNum = $this->getMyAssetNum($req['staff_id']);
        //公共资产
        //$unPubassetNum = $this->getPubAssetNum($req['staff_id']);
        //身份证照片小红点
        $myinfoCount = 0;
        if($this->redCountCompareMobileVersion()) {
            $myinfoCount = (new ToolServer($this->lang, $this->timezone))->getPersonalInformationRedCount($req['staff_id']);
        } else {
            $myinfoCount = $this->getMyInfoCount($req['staff_id']);
        }

        //资产管理小红点
        $unAssetManagementNum = $this->getAssetManagementNum($req['staff_id']);

        $return = [
            'un_read_num' => (string)$ceoUnReadNum,// 我的-CEO信箱
            'backyard_unread_num' => (string)($unReadMsgRes['msg_unread'] ?? 0),// 消息tab
            'un_audit_num' => (string)($unAuditNumRes['un_audit_num'] ?? 0),// 审批tab
            "un_only_audit_num" => (string)($unAuditNumRes['waitAuditNum'] ?? 0),//首页弹出提示
            "un_myinfo_count" => (string)$myinfoCount,// 我的-个人信息
            "un_asset_management_num" => (string)$unAssetManagementNum,//我的-资产管理
            "un_myabout_all" => (string)($ceoUnReadNum + $myinfoCount + $unAssetManagementNum),//我的tab （总和）
        ];
        $this->getDI()->get('logger')->write_log("getRedDotsNum_{$req['staff_id']}: ".json_encode($return,JSON_UNESCAPED_UNICODE), 'info');
        return $return;
    }

    /**
     * @deprecated
     * ph 附件表更新为 hr_staff_annex_info
     * 个人信息菜单角标数量
     * @param $staffId
     * @return int
     */
    public function getMyInfoCount($staffId)
    {
        $myInfoCount = 0;
        $annexRet    = HrStaffAnnexInfoModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => ['staff_info_id' => $staffId],
        ])->toArray();
        $annexList   = array_column($annexRet, null, 'type');

        $allType = [
            HrStaffAnnexInfoModel::TYPE_SOCIAL_SECURITY,
            HrStaffAnnexInfoModel::TYPE_FUND,
            HrStaffAnnexInfoModel::TYPE_MEDICAL_INSURANCE,
            HrStaffAnnexInfoModel::TYPE_TAX_CARD,
        ];

        //个人代理，只保留 税号
        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoOne($staffId, 'hire_type,bank_no');
        if(!empty($staffInfo) && $staffInfo['hire_type'] == HrHcModel::HIRE_TYPE_CONTRACT_LABOUR) {
            $allType = [
                HrStaffAnnexInfoModel::TYPE_TAX_CARD,
            ];
        }

        // 银行卡号为空 或者 审核被拒绝 都需要有数字1的提示
        if (
            empty($staffInfo['bank_no']) || 
            empty($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['annex_path_front']) ||
            (!empty($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]) && $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT)
        ){
            if ($staffInfo['hire_type'] == HrHcModel::HIRE_TYPE_CONTRACT_LABOUR){
                $myInfoCount++;
            }
        }

        // ph 只有审核拒绝 才会出现 +1的小红点 备用银行卡
//        if (isset($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]) && $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT) {
//            $myInfoCount++;
//        }

        // 身份证未上传 或者 拒绝时 红点需要 +1
        if (empty($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]) || ($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT)) {
            $myInfoCount++;
        }

        foreach ($allType as $key => $type) {
            // 医保、公积金、社保号、税号 // 清空卡号时 audit_state 字段会置空
            if (empty($annexList[$type]) || is_null($annexList[$type]['audit_state']) || ($annexList[$type]['audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT)) {
                // 未上传 或者 审核拒绝需要+1
                $myInfoCount++;
            }
        }

        //ph个人信息菜单增加 承诺书电子签 数量提醒
        $commitment_num = (new ResumeRecommendServer($this->lang,
            $this->timezone))->getCommitmentNum(['staff_id' => $staffId]);
        if ($commitment_num) {
            $myInfoCount++;
        }

        $this->getDI()->get('logger')->write_log("getMyInfoCount_$staffId: myinfoCount:$myInfoCount", 'info');
        return $myInfoCount;
    }

    /**
     * 处理警告书消息详情
     * @param $data
     * @param $warning_info
     * @param $msg_id
     * @param $link
     * @return mixed
     */
    public function warningMessageDetail($data, $warning_info, $msg_id, $link)
    {
        //0 查看  1直接签字  2 选择证人后签字
        $state = 0;
        $url   = env('h5_endpoint') . 'warn-letter-message?msg_id=' . $msg_id;
        if ($warning_info['role'] == MessageServer::MSG_STAFF_TYPE_STAFF && empty($warning_info['img_url'])) {
            $state = 1;
        } elseif ($warning_info['role'] == MessageServer::MSG_STAFF_TYPE_SUPERIOR && empty($warning_info['superior_img'])) {
            $state = $warning_info['is_transfer'] ? 2 : 1;
        } elseif ($warning_info['role'] == MessageServer::MSG_STAFF_TYPE_WITNESS_1 && empty($warning_info['witness1_img'])) {
            $state = 1;
        }
        // 重置消息内容
        $url             .= '&state=' . $state;
        $data['content'] = '<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" /><div style="position: fixed; left: 0; top: 0; width: 100%; height: 100%"><iframe src="' . $url . '" width="100%" height="100%" frameborder="0"></iframe></div>';
        // 客户端优先加载url 否则加载content
        $data['msg_detail_url'] = $url;
        return $data;
    }

}
