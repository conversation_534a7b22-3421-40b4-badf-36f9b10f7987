<?php
/**
 * Author: Bruce
 * Date  : 2022-03-17 20:27
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Modules\Ph\library\Enums\AbortList;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\RoyaltyServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\ShareCenterServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Controllers\ToolController as BaseToolController;

use Exception;
use FlashExpress\bi\App\Server\ToolServer;

class ToolController extends BaseToolController
{
    public $paramIn = [];
    public $server  = [];
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        $this->server = [
            'royalty' => new RoyaltyServer($this->lang, $this->timezone, $this->userinfo),
            'staff'   => new StaffServer(),
            'audit'   => new AuditServer($this->lang, $this->timezone),
            'department'   => new DepartmentRepository(),
        ];
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode($this->request->getRawBody(), true);
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 我的页面输出地址菜单和配置
     */
    public function aboutAction()
    {
        //页面输出地址菜单和配置
        $higherStaff         = AbortList::DEFAULT_CONFIG;
        $paramIn['userinfo'] = $this->userinfo;

        $StaffServer   = new StaffServer();
        $RoyaltyServer = new RoyaltyServer($this->lang, $this->timezone, $this->userinfo);


        $headerData     = $this->request->getHeaders();
        $equipment_type = $this->processingDefault($headerData, 'X-Fle-Equipment-Type');
        $equipment_type = strtoupper($equipment_type);
        $enum_type      = \FlashExpress\bi\App\library\enums::EQM_TYPE;
        $is_by_ask      = isset($enum_type[$equipment_type]) && $enum_type[$equipment_type] == 3 ? true : false; //是否为 by 请求
        //判断是否为可显示账号设置
        $this->getDI()->get('logger')->write_log('tool-about 1', "info");
        $is_account_settings = $is_by_ask ? $StaffServer->isShowAccountSettingsFromCache($paramIn['userinfo']['id']) : false;

        $this->getDI()->get('logger')->write_log('tool-about 2', "info");
        $staffInfo = $StaffServer->get_staffFromCache($paramIn['userinfo']['id'])['data'];
        if ($staffInfo) {
            $returenData = $StaffServer->getStoreManagerFromCache($staffInfo['store_id']);
        }
        $this->getDI()->get('logger')->write_log('tool-about 3', "info");
        //获取kit 前端h5 域名
        $envModel    = new SettingEnvServer();
        $kitUrlHost  = $envModel->getSetValFromCache('KIT_UI_URL_HOST');

        $env_url_v3    = urlencode(env('h5_endpoint'));


        $shareServer = new ShareCenterServer($this->lang, $this->timezone);
        foreach ($higherStaff as $key => &$val) {
            $higherStaff[$key]['title'] = $this->getTranslation()->_('abort_list_'.$val['id']);
            //联合判断有没有权限看没有权限是空数据
            if ($val['id'] == 'royalty') {
                //拼接提成
                $returnArr = $RoyaltyServer->getRoyaltyInfoFromCache($paramIn);
                if ($returnArr['code'] == 1) {
                    $higherStaff[$key]['title'] = $this->getTranslation()->_('abort_list_'.$val['id'])
                        .$returnArr['data']['dataList']['royaltyInfo']['monthRoyaltyTitle'];
                } else {
                    unset($higherStaff[$key]);
                }
            }

            //判断权限
            if ($val['id'] == 'asset') {
                if (empty($returenData) || $returenData != $paramIn['userinfo']['id']) { //该网点不存在负责人或当前登录的人不是负责人
                    unset($higherStaff[$key]);
                }
            }

            //个人资产
            if ($val['id'] == 'myasset') {
                $val['dst'] = $val['dst'].'/person';//仅用于BY——我的-个人资产->跳转
            }

            //紧急事故 权限
            if ($val['id'] == 'emergency') {
                if (!in_array($staffInfo['formal'],array(0,1,4))) {
                    unset($higherStaff[$key]);
                }
            }

            //网点信息权限 当前登陆人 是网点员工 并且是 网点负责人 才显示
            if ($val['id'] == 'storeinfo') {
                //网点负责人 为空 可能登陆员工是总部 或者 所属网点没负责人  或者 不是当前登陆人
                if (empty($staffInfo['store_manager_id']) || $staffInfo['store_manager_id'] != $this->userinfo['id']) {
                    unset($higherStaff[$key]);
                }
            }

            //个人信息-是否有待签署合同
            if ($val['id'] == 'myinfo') {
                $unhandle_contract = [];
                $contractObj       = HrStaffContractModel::find([
                    'conditions' => 'staff_id = :staff_id: and contract_is_deleted = 0 and contract_is_need = 1 and contract_status=30',
                    'bind'       => [
                        'staff_id' => $this->userinfo['id'],
                    ],
                ]);
                if (!empty($contractObj)) {
                    $unhandle_contract = $contractObj->toArray();
                }
                $val['is_have_unhandle_contract'] = !empty($unhandle_contract) ? true : false;

                $val['badge_field_name'] = 'un_myinfo_count';
            }

            //seller信息绑定，只有菲律宾有。
            if ($val['id'] == 'seller') {
                // bi svc 接口判断当前登录人是否为 network子部门及本部门
                $data['staff_info_id'] = $this->userinfo['staff_id'];
                $res = (new \FlashExpress\bi\App\Modules\Ph\Server\ToolServer($this->lang, $this->timezone))->setExpire(3600)->checkSellerFromCache($data['staff_info_id']);
                if ($res['code'] == 0 || $res['result']['code'] == 0) {
                    unset($higherStaff[$key]);
                }
            }

            // 账号设置
            if ($val['id'] == 'account_cancellation') {
                if (!$is_account_settings) { //如果不显示 就销毁
                    unset($higherStaff[$key]);
                }
            }

            // 文件共享中心
            if ($val['id'] == 'shareCenter') {
                $val['is_new'] = $shareServer->checkShareCenterHaveNewFile($paramIn['userinfo']['id']);
            }

            //个人代理隐藏 1. Flash box入口 2. 规章制度&SOP 3. 信息共享中心
            if($staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID){
                if(in_array($val['id'],['shareCenter','qa','sop'])){
                    unset($higherStaff[$key]);
                }
            }

            //切换国家地址
            //sign_url：urlencode('https://backyard-ui.flashexpress.com')；kit_ui_url：https://kit-ui2.flashexpress.ph

            // $val['id'] 为 seller  时，只有菲律宾有，跳转到 kit 地址；
            $env_url    = urlencode(env('sign_url'));

            if($val['id'] == 'seller') {
                $env_url = urlencode($kitUrlHost);
            }
            //Flash box入口 仅 编制 实习生可见
            if($val['id'] == 'mailbox' && !in_array($staffInfo['formal'], [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN])){
                unset($higherStaff[$key]);
            }

            //flash box 跳到 新 url
            if($val['id'] == 'mailbox') {
                $env_url = urlencode(env('h5_endpoint'));
            }

            if ($val['id'] == 'faq') {
                $env_url = $env_url_v3;
            }

            //设置 子账号，合作商加盟商工号
            if ($val['id'] == 'setting' && ToolServer::isHiddenSettingConfig($staffInfo['formal'],$staffInfo['is_sub_staff'],$paramIn['userinfo']['id'])) {
                unset($higherStaff[$key]);
            }

            $val['dst'] = str_replace('{url}', $env_url, $val['dst']);


        }
        $higherStaff = array_values($higherStaff);
        $returnData  = [
            'code' => 1,
            'msg'  => 'success',
            'data' => $higherStaff,
        ];
        $this->getDI()->get('logger')->write_log('tool-about 5', "info");
        $this->jsonReturn($returnData);
    }
}