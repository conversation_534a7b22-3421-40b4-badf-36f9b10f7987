<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers\Osm;

use FlashExpress\bi\App\Controllers\Osm\OutsourcingStaffController AS GlobalControllerBase;
use FlashExpress\bi\App\Modules\Ph\Server\Osm\OutsourcingStaffServer;
use FlashExpress\bi\App\Server\Osm\OutsourcingOrderServer;
use FlashExpress\bi\App\Server\Osm\AttendanceServer;


class OutsourcingStaffController extends GlobalControllerBase
{
    /**
     * 获取枚举数据
     */
    public function getSysInfoAction()
    {
        $result = (new OutsourcingStaffServer($this->lang))->getSysInfo($this->osm_user_info);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 获取单个员工信息
     */
    public function getOneOutsourcingStaffAction()
    {
        $params      = $this->paramIn;
        $validations = [
            'staff_info_id' => "Required|IntGe:1|>>>:".$this->getTranslation()->_('outsourcing_company_no_staff_id'),
        ];
        $this->validateCheck($params, $validations);
        $result = (new OutsourcingStaffServer($this->lang))->getOneOutsourcingStaff($params, $this->osm_user_info);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * 获取未读数量
     */
    public function unReadCountAction()
    {
        $data = [
            'un_read_number'         => (new OutsourcingOrderServer($this->lang))->getOutSourcingOrderCount($this->osm_user_info['id']),
            'un_read_outsourcing_ot' => (new OutsourcingStaffServer($this->lang))->getUnReadOutsourcingOt($this->osm_user_info['id']),
            'un_read_att_correction' => (new AttendanceServer($this->lang))->getPendingCount($this->osm_user_info['id']),
        ];
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }
}