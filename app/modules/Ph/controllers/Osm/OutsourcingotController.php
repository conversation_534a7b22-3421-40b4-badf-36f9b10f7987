<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers\Osm;

use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\Ph\Server\Osm\OutsourcingOTServer AS OsmOutsourcingOTServer;
use FlashExpress\bi\App\Controllers\Osm\OutsourcingotController AS GlobalControllerBase;

class OutsourcingotController extends GlobalControllerBase
{
    public function initialize()
    {
        parent::initialize();
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 获取外协员工加班审批工单的列表
     * @return void
     * @throws ValidationException
     */
    public function getOutsourcingOTListAction()
    {
        $params      = $this->paramIn;
        $validations = [
            // 1->待处理 2 -> 审核中 3 -> 已处理
            'osm_state' => "Required|IntIn:1,2,3|>>>:'osm_state' invalid input",
            "page"      => "Required|IntGe:1",
            "page_size" => "Required|IntGe:1",
        ];
        $this->validateCheck($params, $validations);
        $userInfo = $this->osm_user_info;
        $data     = (new OsmOutsourcingOTServer($this->lang, $this->timezone))->getOutsourcingOTList($params,
            $userInfo);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }


    /**
     * OSM配置好加班外协人员后，点击提交调用该接口
     * @return void
     */
    public function submitOutsourcingOTAction()
    {
        $params      = $this->paramIn;
        $this->getDI()->get("logger")->write_log("submitOutsourcingOTAction-params: " . json_encode($params, JSON_UNESCAPED_UNICODE), "info");
        $validations = [
            // 1->待处理 2 -> 审核中 3 -> 已处理
            'id'           => "Required|IntGt:0|>>>:'id' invalid input",
            'staff_ids'    => 'Required|Arr|ArrLenGeLe:1,2000|>>>:staff_ids error',
            'staff_ids[*]' => 'Required|StrLenGe:1|>>>:staff_id error',
        ];
        $this->validateCheck($params, $validations);
        $params['user_info'] = $this->osm_user_info;
        $returnArr           = (new OsmOutsourcingOTServer($this->lang,
            $this->timezone))->submitOutsourcingOTUseLock($params);
        $this->jsonReturn($this->checkReturn($returnArr));
    }
}