<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers\Osm;

use FlashExpress\bi\App\Controllers\Osm\LoginController AS GlobalControllerBase;
use FlashExpress\bi\App\Server\Osm\LoginServer;

class LoginController extends GlobalControllerBase
{
    /**
     * 外协公司登录接口
     */
    public function loginAction()
    {
        $params     = $this->paramIn;
        $headerData = $this->request->getHeaders();
        //处理默认数据
        $params['lang']         = $this->lang = $this->processingDefault($headerData, 'Accept-Language', 0, 'zh-CN');
        $params['ip_address']   = $this->request->getServer('REMOTE_ADDR');   //IP address
        $params['lng']          = $this->processingDefault($headerData, 'X-Device-Lng');    //经度
        $params['lat']          = $this->processingDefault($headerData, 'X-Device-Lat');    //纬度
        $params['version']      = $this->processingDefault($headerData, 'X-Version');    //版本
        $params['device_model'] = $this->processingDefault($headerData, 'X-Device-Model');    //设备型号
        $params['os']           = $this->processingDefault($headerData, 'X-Os');    //设备型号
        $params['os_version']   = $this->processingDefault($headerData, 'X-Os-Version');    //设备型号

        $validations = [
            "login"        => "Required|StrLen:11|>>>:".$this->getTranslation()->_('outsourcing_company_name_no_null_ph'),
            "password"     => "Required|StrLen:6|>>>:".$this->getTranslation()->_('outsourcing_company_wrong_password2'),
            "clientid"     => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "clientsd"     => "Required|Int|>>>:".$this->getTranslation()->_('miss_args'),
            "os"           => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "version"      => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "lang"         => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
            "device_model" => "Required|str|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($params, $validations);
        if (!isset($params['select_privacy']) || $params['select_privacy'] != 1) {
            $this->jsonReturn($this->checkReturn(-3,
                $this->getTranslation()->_('outsourcing_company_no_select_privacy')));
        }

        $result = (new LoginServer($this->lang))->login($params);
        $this->jsonReturn($this->checkReturn($result));
    }
}