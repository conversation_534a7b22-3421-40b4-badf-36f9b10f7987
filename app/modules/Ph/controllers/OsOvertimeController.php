<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 11/3/21
 * Time: 7:58 PM
 */

namespace FlashExpress\bi\App\Modules\Ph\Controllers;
use FlashExpress\bi\App\Controllers\ControllerBase;

class OsOvertimeController extends ControllerBase{

    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode($this->request->getRawBody(),true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
        $this->url_log($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }




}