<?php
namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\Ph\library\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Modules\Ph\Server\VehicleServer;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\SettingEnvServer;
use Exception;
class VehicleController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 车辆信息，枚举列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function enumVehicleAction()
    {
        $returnArr['data'] = [];
        // 非特定职位，无权限访问数据 PH:Van courier[110]、Bike Courier[13]、Tricycle Courier[1000]、Truck Driver[1194]
        $vehicleJobTitle = explode(',',(new SettingEnvServer())->getSetVal('job_title_vehicle_type')) ;

        $staff_info_id = $this->userinfo['id'];
        $checkStaff = (new StaffRepository())->checkoutStaffBi($staff_info_id);
        if ($checkStaff['is_sub_staff'] == 1){
            // 子账号需切到主账号 获取信息
            $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportInfoBySubStaff($staff_info_id);
            if (empty($supportStaffInfo)){
                $this->jsonReturn($this->checkReturn($returnArr));
            }
            $staff_info_id = $supportStaffInfo['staff_info_id'];
            $checkStaff = (new StaffRepository())->checkoutStaffBi($staff_info_id);
        }

        if (!in_array($checkStaff['job_title'], $vehicleJobTitle)) {
            $this->jsonReturn($this->checkReturn($returnArr));
        }
        $infoArr = (new VehicleServer($this->lang, $this->timezone))->getVehicleInfoS(["id"=>$staff_info_id,"job_title"=>$checkStaff['job_title']]);

        if($checkStaff['job_title'] == VehicleInfoEnums::JOB_TRUCK_TITLE_ID){
            //truck返回特定的类型
            $enumArr = (new VehicleServer($this->lang, $this->timezone))->truckEnumVehicleS();

        }else{
            //如果车辆信息不存在输出类型(判断废弃，同时返回)
            $enumArr = (new VehicleServer($this->lang, $this->timezone))->enumVehicleS();
        }
        $returnArr['data'] = $infoArr + $enumArr;


        $this->jsonReturn($this->checkReturn($returnArr));
    }

    /**
     * 创建车辆信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addVehicleInfoAction()
    {
        try {

            //[1]入参和验证
            $paramIn = trim_array($this->paramIn);
            $userinfo = $this->userinfo;

            $this->getDI()->get('logger')->write_log('kit_add_vehicle_info, 请求参数: '.json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');
            $this->getDI()->get('logger')->write_log('kit_add_vehicle_info, 当前用户: '.json_encode($userinfo, JSON_UNESCAPED_UNICODE), 'info');

            $checkStaff = (new StaffRepository())->checkoutStaffBi($userinfo['id']);
            $staff_info_id = $userinfo['id'];
            if ($checkStaff['is_sub_staff'] == 1){
                // 子账号需切到主账号 获取信息
                $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportInfoBySubStaff($staff_info_id);
                if (empty($supportStaffInfo)){
                    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
                }
                $staff_info_id = $supportStaffInfo['staff_info_id'];
                $checkStaff = (new StaffRepository())->checkoutStaffBi($staff_info_id);
            }

            // 职位权限校验: 仅限 VehicleInfoEnums::JOB_TITLE_ITEM 列出的职位可操作
            if (!array_key_exists($checkStaff['job_title'], VehicleInfoEnums::JOB_TITLE_ITEM)) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
            }

            if (!is_array($paramIn['driving_licence_img_item']) && !empty($paramIn['driving_licence_img_item'])) {
                $paramIn['driving_licence_img_item'] = json_decode($paramIn['driving_licence_img_item']);
            }

            // 公共验证字段
            $validations = [
                'vehicle_source' => 'Required|IntIn:1,2,3|>>>:vehicle_source' . $this->getTranslation()->_('miss_args'),
                'vehicle_start_date' => 'IfIntEq:vehicle_source,2|Required|Date|>>>:vehicle_start_date' . $this->getTranslation()->_('miss_args'),
                //"plate_number" => "Required|StrLenGeLe:1,50|>>>:" . $this->getTranslation()->_('7130'),
                "engine_number" => "Required|StrLenGeLe:10,17|>>>:" . $this->getTranslation()->_('vehicle_info_0002'),
                'license_location' => 'Required|StrLenGeLe:1,512|>>>:license_location' . $this->getTranslation()->_('miss_args'),
                "registration_certificate_img" => "Required|StrLenGeLe:1,255|>>>:registration_certificate_img" . $this->getTranslation()->_('miss_args'),
                "vehicle_img" => "Required|Arr|ArrLen:3|>>>:" . $this->getTranslation()->_('vehicle_img_validation'),
                "insurance_policy_number" => "Required|StrLenGeLe:1,50|>>>:insurance_policy_number" . $this->getTranslation()->_('miss_args'),
                "insurance_start_date" => "Required|Date|>>>:insurance_start_date" . $this->getTranslation()->_('miss_args'),
                "insurance_end_date" => "Required|Date|>>>:insurance_end_date" . $this->getTranslation()->_('miss_args'),
                "vehicle_tax_expiration_date" => "Required|Date|>>>:vehicle_tax_expiration_date" . $this->getTranslation()->_('miss_args'),
                "vehicle_tax_certificate_img" => "Required|StrLenGeLe:1,255|>>>:vehicle_tax_certificate_img" . $this->getTranslation()->_('miss_args'),
                "driver_license_type" => "Required|IntIn:1|>>>:driver_license_type" . $this->getTranslation()->_('miss_args'),
                "driver_license_type_other_text" => "IfIntEq:driver_license_type,100|Required|StrLenGeLe:1,200|>>>:driver_license_type_other_text" . $this->getTranslation()->_('miss_args'),
                "driver_license_number" => "Required|StrLenGeLe:1,128|>>>:driver_license_number" . $this->getTranslation()->_('miss_args'),
                "driver_license_start_date" => "Required|Date|>>>:driver_license_start_date" . $this->getTranslation()->_('miss_args'),
                "driver_license_end_date" => "Required|Date|>>>:driver_license_end_date" . $this->getTranslation()->_('miss_args'),
                "driving_licence_img_item" => "Required|ArrLen:2|>>>:" . $this->getTranslation()->_('vehicle_info_0008'),
                //new
                'vehicle_registration_date' => 'Date|>>>:vehicle_registration_date' . $this->getTranslation()->_('error_message'),
                'vehicle_registration_number' => 'StrLenGeLe:1,50|>>>:vehicle_registration_number' . $this->getTranslation()->_('error_message'),
                'vehicle_proof_number' => 'StrLenGeLe:1,50|>>>:vehicle_proof_number' . $this->getTranslation()->_('error_message'),
                'vehicle_proof_pay_date' => 'Date|>>>:vehicle_proof_pay_date' . $this->getTranslation()->_('error_message'),
                'vehicle_proof_img' => 'StrLenGeLe:1,255|>>>:vehicle_proof_img' . $this->getTranslation()->_('error_message'),
                'driving_license_vehicle_restrictions' => 'Arr|ArrLenLe:8|>>>:driving_license_vehicle_restrictions' . $this->getTranslation()->_('error_message'),

            ];

            // van职位 特有字段验证
            if (in_array($checkStaff['job_title'],VehicleInfoEnums::VAN_OR_TRUCK_JOB_GROUP_ITEM)) {
                $vehicle_size = implode(',',array_keys(VehicleServer::getVehicleSize(false)));
                $other_validations = [
                    "vehicle_brand" => "Required|IntIn:".implode(',',array_column(VehicleInfoEnums::CONFIG_VEHICLE_INFO['vehicle_brand'],'value'))."|>>>:vehicle_brand" . $this->getTranslation()->_('miss_args'),
                    "vehicle_brand_text" => "IfIntEq:vehicle_brand,100|Required|StrLenGeLe:1,200|>>>:vehicle_brand_text" . $this->getTranslation()->_('miss_args'),
                    "vehicle_model" => "Required|IntGe:1|>>>:vehicle_model" . $this->getTranslation()->_('miss_args'),
                    "vehicle_model_text" => "IfIntEq:vehicle_model,100|Required|StrLenGeLe:1,200|>>>:vehicle_model_text" . $this->getTranslation()->_('miss_args'),
                    "vehicle_size" => "Required|IntIn:$vehicle_size|>>>:vehicle_size".$this->getTranslation()->_('miss_args'),
                    "oil_type" => "Required|IntIn:1,2|>>>:oil_type" . $this->getTranslation()->_('miss_args'),
                    "oil_company" => "IntIn:0,1,2|>>>:oil_company" . $this->getTranslation()->_('miss_args'),
                    'oil_img' => 'StrLenGeLe:0,255|>>>:oil_img' . $this->getTranslation()->_('error_message'),
                    "oil_number" => "IfIntNe:oil_company,0|Required|Regexp:/^\d{10,20}$/|>>>:" . $this->getTranslation()->_('fuel_manage_is_oil_number'),
                ];

                if($paramIn['vehicle_brand'] == 100 || $paramIn['vehicle_model'] == 100){
                    $car_l_w_h = [
                        'car_long' => 'Required|Regexp:/^[0-9]+(.[0-9]{1,3})?$/|>>>:car_long' . $this->getTranslation()->_('error_message'),
                        'car_width' => 'Required|Regexp:/^[0-9]+(.[0-9]{1,3})?$/|>>>:car_width' . $this->getTranslation()->_('error_message'),
                        'car_high' => 'Required|Regexp:/^[0-9]+(.[0-9]{1,3})?$/|>>>:car_high' . $this->getTranslation()->_('error_message'),
                    ];
                    $other_validations = array_merge($other_validations,$car_l_w_h);
                }


                $validations = array_merge($validations, $other_validations);

                //Van类型，校验油卡有效性
                if(isset($paramIn['oil_company']) && $paramIn['oil_company'] == VehicleInfoEnums::OIL_COMPANY_SHELL_CODE){
                    $is_effective =   (new VehicleServer($this->lang, $this->timezone))->validationOilCard($paramIn['oil_number']);
                    if(!$is_effective){
                        return   $this->jsonReturn($this->checkReturn(-3, 'The fuel card number is incorrect or invalid, please check and resubmit'));
                    }
                }
            }
            //bike
            if (in_array($checkStaff['job_title'], VehicleInfoEnums::BIKE_JOB_GROUP_ITEM)){
                $bike_validations = [
                    'motorcycle_side_bag_proof_num' => 'StrLenGeLe:0,50|>>>:motorcycle_side_bag_proof_num' . $this->getTranslation()->_('error_message'),
                    'motorcycle_side_bag_proof_img' => 'StrLenGeLe:0,255|>>>:motorcycle_side_bag_proof_img' . $this->getTranslation()->_('error_message'),
                ];
                $validations = array_merge($validations, $bike_validations);
            }
            $this->validateCheck($paramIn, $validations);

            // 验证车牌号码的规则：允许英文字母+数字
            if (!preg_match("/^[a-zA-Z0-9]{1,50}$/u",$paramIn['plate_number'])) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('plate_number_other_error')));
            }

            //车辆凭证付款日期验证
            if ($paramIn['vehicle_proof_pay_date'] > date("Y-m-d")) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('7131')));
            }

            if ($paramIn['insurance_end_date'] < $paramIn['insurance_start_date']) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0004')));
            }

            if ($paramIn['driver_license_end_date'] < $paramIn['driver_license_start_date']) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0005')));
            }

            if(empty($checkStaff) || $checkStaff['formal'] != 1){
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('staff_vehicle_notice_1')));
            }

            $returnArr = (new VehicleServer($this->lang, $this->timezone))->addVehicleInfoS($paramIn, ["id"=>$staff_info_id,"job_title"=>$checkStaff['job_title']],$this->userinfo['id']);

            $this->getDI()->get('logger')->write_log('kit_add_vehicle_info, 返回结果: '.json_encode($returnArr, JSON_UNESCAPED_UNICODE), 'info');
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('kit_add_vehicle_info, 异常信息: params:'.json_encode($paramIn).' '.$e->getMessage().' '.$e->getTraceAsString(), 'info');
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 打卡里程查询
     * @throws ValidationException
     */
    public function getMileageAction()
    {
        //[1]入参和验证
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;

        $returnArr = (new VehicleServer($this->lang, $this->timezone))->getMileageSV2($paramIn, $userinfo);
        $this->jsonReturn($returnArr);
    }

    /**
     * 创建上报车辆里程申请
     * @Return  array
     * @throws BusinessException
     */
    public function addVehicleAction()
    {
        //[1]入参和验证
        $paramIn     = $this->paramIn;
        $userinfo    = $this->userinfo;
        $validations = [
            "mileage_date"     => "Required|Date|>>>:" . $this->getTranslation()->_('miss_args'),
            "start_kilometres" => "Required|IntGtLt:0,100000000000000|>>>:" . $this->getTranslation()->_('miss_args'), //只能输入1-999999999
            "started_img"      => "Required|StrLenGeLe:1,200|>>>:" . $this->getTranslation()->_('miss_args'),
            "started_bucket"   => "Required|StrLenGeLe:1,100|>>>:" . $this->getTranslation()->_('miss_args'),
            "started_path"     => "Required|StrLenGeLe:1,100|>>>:" . $this->getTranslation()->_('miss_args'),
            "end_kilometres"   => "Required|Required|IntGtLt:0,1000000000000|>>>:" . $this->getTranslation()->_('miss_args'),
            "end_img"          => "Required|StrLenGeLe:1,200|>>>:" . $this->getTranslation()->_('miss_args'),
            "end_bucket"       => "Required|StrLenGeLe:1,100|>>>:" . $this->getTranslation()->_('miss_args'),
            "end_path"         => "Required|StrLenGeLe:1,100|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);

        //申请操作
        $vehicleServer = new VehicleServer($this->lang, $this->timezone);
        $returnArr     = $vehicleServer->setLockConf(10)->addVehicleSUseLock($paramIn, $userinfo);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 审核
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkVehicleAction()
    {
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;

        $validations = [
            "status"   => "Required|Required|IntIn:1,2,3,4|>>>:" . $this->getTranslation()->_('miss_args'),
            "audit_id" => "Required|Required|Int|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        //如果驳回，验证驳回理由
        if ($paramIn['status'] == enums::APPROVAL_STATUS_REJECTED) {
            $validations['reject_reason'] = "Required|StrLenGeLe:1,50|>>>:" . $this->getTranslation()->_('7124');
        }
        $this->validateCheck($paramIn, $validations);

        //id 和驳回理由转换
        $paramIn['id'] = $paramIn['audit_id'];
        $res = (new VehicleServer($this->lang, $this->timezone))->updateVehicleStatusUseLock($paramIn, $userinfo);

        //[3]成功数据返回
        $this->jsonReturn($this->checkReturn($res));
    }

}
