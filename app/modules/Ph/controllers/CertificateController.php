<?php
namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Modules\Ph\Server\CertificateServer;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\PersoninfoServer;
use Exception;
class CertificateController extends Controllers\ControllerBase{


    public function initialize(){
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn)){
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
            $this->paramIn = filter_param($this->paramIn);
        }
    }

    public function onConstruct(){
        parent::onConstruct();
    }

    //页面加载个人信息邮箱月份等接口
    public function salary_pdfAction(){
        $staff_server = new PersoninfoServer($this->lang,$this->timezone);
        $data = $staff_server->salary_need($this->userinfo['staff_id']);
        if(empty($data))
            return $this->jsonReturn(self::checkReturn(array('data' => $data)));

        $start = empty($data['min_date']) ? '' : substr($data['min_date'],0,-1);
        $end = empty($data['max_date']) ? '' : substr($data['max_date'],0,-1);

        $arr = array();
        if($start != $end){
            $item = $start;
            while ($item <= $end){
                $arr[] = $item.'1';
                $arr[] = $item.'2';
                $item = date('Y-m',strtotime("{$item} +1 month"));
            }
        }else{
            $arr = array($start.'1',$start.'2');
        }

        if($arr[0] != $data['min_date'])
            array_shift($arr);
        $e = end($arr);
        if($e != $data['max_date'])
            array_pop($arr);

        $current_time = date('Y-m-d H:i:s');
        $last_month = date('Y-m',strtotime("-1 month"));
        foreach ($arr as $k => $month){
            //当时间达当月25日17点时，当月上半月显示可选；当时间达次月10日17点时，当月下半月显示可选
            //翻译过来就是 没到 25- 17：00 当月上半月之后的不展示  没到10 17：00 上个月下半月之后的不展示
            if($current_time < date("Y-m-24 17:00:00") && $month > date("Y-m").'1')
                unset($arr[$k]);

            if($current_time < date("Y-m-10 17:00:00") && $month >= $last_month . '2')
                unset($arr[$k]);

            //另外一种 流量 非正常情况
            if($current_time >= date("Y-m-24 17:00:00") && $month > date("Y-m").'1')
                unset($arr[$k]);
        }
        sort($arr);
        $arr = array_reverse($arr);

        //整理 根据月份维度 最多显示 不超过12个月
        $i = 0;
        $month_arr = $final_month = array();
        foreach ($arr as $v){
            if($i == 12)
                break;
            if(in_array(substr($v,0,-1),$month_arr)){
                $final_month[] = $v;
                continue;

            }
            $month_arr[] = substr($v,0,-1);
            $final_month[] = $v;
            $i++;
        }

        //整理成前端要的
        $return = array();
        foreach ($final_month as $v){
            $year = substr($v,0,4);
            $month = intval(substr($v,5,2));
            $type = substr($v,-1);

            $current_month = substr($v,0,-1);
            //当月最后一天
            $last_day = date('d',strtotime("{$current_month} last day of"));
            $between_day = $type == 1 ? '1-15' : "16-{$last_day}";
            $text = substr(StaffRepository::$month_en[$month],0,3). ' ' . $between_day;
            $val = "{$year}-".str_pad($month,2,"0",STR_PAD_LEFT).$type;
            $row['label'] = $text;
            $row['value'] = $val;
            $return[$year][] = $row;
        }

        $final = array();
        foreach ($return as $k => &$v){
            $r['year'] = $k;
            $r['item'] = $v;
            $final[] = $r;
        }
        $f['date_list'] = $final;
        $f['email'] = $data['email'];
        $f['personal_email'] = $data['personal_email'];
        return $this->jsonReturn(self::checkReturn(array('data' => $f)));

    }


    //操作发送接口
    public function salary_pdf_downloadAction(){

        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $param['type'] = 1;
        $param['mail_type'] = empty($this->paramIn['mail_type']) ? 1 : intval($this->paramIn['mail_type']);
        $start = $this->paramIn['start_month'];
        $end = $this->paramIn['end_month'];

        $start = date('Y-m',strtotime(substr($start,0,-1)));
        $end = date('Y-m',strtotime(substr($end,0,-1)));

        $arr = array();
        if($start != $end){
            $item = date('Y-m',strtotime("{$start} +1 month"));
            while ($item < $end){
                $arr[] = $item.'1';
                $arr[] = $item.'2';
                $item = date('Y-m',strtotime("{$item} +1 month"));
            }
            //把结束拼进去
            $arr = array_merge($arr,array($this->paramIn['start_month'],$this->paramIn['end_month']));
            if(substr($this->paramIn['start_month'],-1) == 1)//如果是1上半月  把2下半月 拼进去 别问我啥玩意 我也不想这么干
                $arr = array_merge($arr,array($start.'2'));

            if(substr($this->paramIn['end_month'],-1) == 2)
                $arr = array_merge($arr,array($end.'1'));
        }else{
            $arr = array_unique(array($this->paramIn['start_month'],$this->paramIn['end_month']));
        }
        sort($arr);

        $param['date'] = $arr;
        $method = 'by_download';

        $fle_rpc = (new ApiClient("hcm_rpc",'',$method, $this->lang));
        $fle_rpc->setParams($param);
        $res = $fle_rpc->execute();


        if(!empty($res['result']))
            return $this->jsonReturn($res['result']);

        return $this->jsonReturn(self::checkReturn(-3,'connect error'));

    }

    //在职证明 页面加载接口 员工基本信息 薪资构成等
    public function on_jobAction(){
        try{
            $param = $this->paramIn;
            $param['staff_id'] = $this->userinfo['id'];
            $cer_server = new CertificateServer($this->lang,$this->timezone);
            $pdf_data = $cer_server->get_pdf_data($param);
            return $this->jsonReturn(self::checkReturn(array('data' => $pdf_data)));
        }catch (\Exception $e){
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }

    }


    //在职证明 操作发送接口
    public function on_job_downloadAction(){
        try{

            $param['staff_info_id'] = $this->userinfo['staff_id'];
            $param['type'] = 2;
            $param['date'] = array(date('Y-m-d',time()));
            $param['mail_type'] = empty($this->paramIn['mail_type']) ? 1 : intval($this->paramIn['mail_type']);
            $param['sub_type'] = $this->paramIn['sub_type'];

            $method = 'by_download';

            $fle_rpc = (new ApiClient("hcm_rpc",'',$method, $this->lang));
            $fle_rpc->setParams($param);
            $res = $fle_rpc->execute();

            if(!empty($res['result']))
                return $this->jsonReturn($res['result']);

            return $this->jsonReturn(self::checkReturn(-3,'connect error'));
        }catch (\Exception $e){
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }
    }

    //工资证明
    public function payroll_downloadAction(){
        try{
            $param['staff_info_id'] = $this->userinfo['staff_id'];
            $param['type'] = 4;
            $param['date'] = array(date('Y-m-d',time()));
            $param['mail_type'] = empty($this->paramIn['mail_type']) ? 1 : intval($this->paramIn['mail_type']);
            $param['sub_type'] = $this->paramIn['sub_type'];

            $method = 'by_download';

            $fle_rpc = (new ApiClient("hcm_rpc",'',$method, $this->lang));
            $fle_rpc->setParams($param);
            $res = $fle_rpc->execute();

            if(!empty($res['result']))
                return $this->jsonReturn($res['result']);

            return $this->jsonReturn(self::checkReturn(-3,'connect error'));
        }catch (\Exception $e){
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }
    }


    /**
     * 获取用户信息
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function getBdoAuthInfoAction()
    {
        $data = (new PersoninfoServer($this->lang,$this->timezone))->getBdoAuthInfo($this->userinfo['staff_id']);
        return $this->jsonReturn(self::checkReturn(array('data' => $data ?? [])));
    }

    /**
     * 发送BDO至邮箱
     * @throws Exception
     */
    public function sendBdoEmailAction()
    {
        $paramIn                  = $this->paramIn;
        $paramIn['staff_info_id'] = $this->userinfo['id'];
        //[2]数据验证
        $validations = [
            "email" => "Required|Email",
        ];
        $this->validateCheck($paramIn, $validations);

        $redisKey  = "send_bdo_email_{$paramIn['staff_info_id']}";
        $returnArr = $this->atomicLock(function () use ($paramIn) {
            return (new PersoninfoServer($this->lang, $this->timezone))->sendBdoEmailInfo($paramIn);
        }, $redisKey, 10);

        if ($returnArr === false) { //没有获取到锁
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('ticket_repeat_msg')));
        }

        $this->jsonReturn($returnArr);
    }
}