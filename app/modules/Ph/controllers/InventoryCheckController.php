<?php
namespace FlashExpress\bi\App\Modules\Ph\Controllers;
use FlashExpress\bi\App\Enums\InventoryCheckEnums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\Ph\Server\InventoryCheckServer;
/**
 * 资产盘点控制器
 * Class InventoryCheckController
 */
class InventoryCheckController extends BaseController
{
    protected $inventoryCheckServer;
    protected $paramIn;

    /**
     * 初始化
     */
    public function initialize()
    {
        $this->getDI()->get('logger')->write_log('旧资产盘点功能已下线，不应产生请求，请联系前端同学协助排查遗漏入口请求', 'error');
        exit();
        parent::initialize();
        $this->inventoryCheckServer = new InventoryCheckServer($this->lang, $this->timezone);
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * 资产盘点-通知消息详情页面
     * @api https://yapi.flashexpress.pub/project/93/interface/api/34278
     */
    public function msgDetailAction()
    {
        try {
            $validations = [
                'inventory_check_id' => 'Required|IntGe:1|>>>:inventory check id'. $this->getTranslation()->_('miss_args'), //盘点单ID
            ];

            //验证
            $this->validateCheck($this->paramIn, $validations);
            $params['inventory_check_id'] = $this->paramIn['inventory_check_id'];
            $params['staff_id'] = $this->userinfo['staff_id'];
            $list = $this->inventoryCheckServer->getMsgDetail($params);
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("msgDetailAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 资产盘点-获取待盘点等的数量
     * @api https://yapi.flashexpress.pub/project/93/interface/api/34957
     */
    public function getCheckCountAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations = [
                'inventory_check_id' => 'Required|IntGe:1|>>>:inventory check id'. $this->getTranslation()->_('miss_args'), //盘点单ID
            ];
            //验证
            $this->validateCheck($paramIn, $validations);
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $data = $this->inventoryCheckServer->getCheckCount($paramIn);
            $this->jsonReturn($this->checkReturn(array('data' => $data)));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("startAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 资产盘点-资产清单-单项资产（个人、公共）
     * @api https://yapi.flashexpress.pub/project/93/interface/api/34236
     */
    public function singleListAction()
    {
        try {
            $validations = [
                'inventory_check_id' => 'Required|IntGe:1|>>>:inventory check id'. $this->getTranslation()->_('miss_args'), //盘点单ID
                "page_offset" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),//页码
                "page_size" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),//每页条数
                "goods_name" => "Str|>>>:" . $this->getTranslation()->_('miss_args'),//资产名称
            ];
            //验证
            $this->validateCheck($this->paramIn, $validations);
            $params['inventory_check_id'] = $this->paramIn['inventory_check_id'];
            $params['page_offset'] = $this->processingDefault($this->paramIn, 'page_offset', 2);
            $params['page_size'] = $this->processingDefault($this->paramIn, 'page_size', 2,InventoryCheckEnums::PAGE_SIZE);
            //资产名称做0的特殊处理
            if (isset($this->paramIn['goods_name']) && mb_strlen($this->paramIn['goods_name']) > 0) {
                $params['goods_name'] = $this->paramIn['goods_name'];
            } else {
                $params['goods_name'] = $this->processingDefault($this->paramIn, 'goods_name', 1,'');
            }
            $params['staff_id'] = $this->userinfo['staff_id'];
            $list = $this->inventoryCheckServer->getMySingleAssetsList($params);
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (ValidationException $e){
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("singleListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 资产盘点-资产清单-多项资产（公共）
     * @api https://yapi.flashexpress.pub/project/93/interface/api/34250
     */
    public function multipleListAction()
    {
        try {
            $validations = [
                'inventory_check_id' => 'Required|IntGe:1|>>>:inventory check id'. $this->getTranslation()->_('miss_args'), //盘点单ID
                "page_offset" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),//页码
                "page_size" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),//每页条数
                "goods_name" => "Str|>>>:" . $this->getTranslation()->_('miss_args'),//资产名称
            ];

            //验证
            $this->validateCheck($this->paramIn, $validations);
            $params['inventory_check_id'] = $this->paramIn['inventory_check_id'];
            $params['page_offset'] = $this->processingDefault($this->paramIn, 'page_offset', 2);
            $params['page_size'] = $this->processingDefault($this->paramIn, 'page_size', 2,InventoryCheckEnums::PAGE_SIZE);
            //资产名称做0的特殊处理
            if (isset($this->paramIn['goods_name']) && mb_strlen($this->paramIn['goods_name']) > 0) {
                $params['goods_name'] = $this->paramIn['goods_name'];
            } else {
                $params['goods_name'] = $this->processingDefault($this->paramIn, 'goods_name', 1,'');
            }
            $params['staff_id'] = $this->userinfo['staff_id'];
            $list = $this->inventoryCheckServer->getMyMultipleAssetsList($params);
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (ValidationException $e){
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("multipleListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 资产盘点-审批-盘点单-待处理,已结束列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/34299
     */
    public function taskListAction()
    {
        try {
            $validations = [
                "page_num" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),//页码
                "page_size" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),//每页条数
                "type" => "IntIn:1,2|>>>:" . $this->getTranslation()->_('miss_args'),//列表类型，1待处理，2已完成
            ];

            //验证
            $this->validateCheck($this->paramIn, $validations);
            $params['page_num'] = $this->processingDefault($this->paramIn, 'page_num', 2,InventoryCheckEnums::PAGE_NUM);
            $params['page_size'] = $this->processingDefault($this->paramIn, 'page_size', 2,InventoryCheckEnums::PAGE_SIZE);
            $params['type'] = $this->processingDefault($this->paramIn, 'type', 2,1);
            $list = $this->inventoryCheckServer->getTaskList($params, $this->userinfo);
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("taskListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 资产盘点-审批-盘点单-待处理,已结束数量
     * @api https://yapi.flashexpress.pub/project/93/interface/api/36084
     */
    public function taskCountAction()
    {
        try {
            $staff_id = $this->userinfo['staff_id'];
            //待处理数
            $processed_count = $this->inventoryCheckServer->getTaskCount(['type'=>1,'staff_id'=>$staff_id]);
            //已结束数
            $completed_count = $this->inventoryCheckServer->getTaskCount(['type'=>2,'staff_id'=>$staff_id]);
            $data = ['processed_count'=>$processed_count, 'completed_count'=>$completed_count];
            $this->jsonReturn($this->checkReturn(array('data' => $data)));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("taskListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 资产盘点-根据资产名字获取资产列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/34306
     */
    public function getAssetListByNameAction()
    {
        try {
            $validations = [
                "page_num" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),//页码
                "page_size" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),//每页条数
                "name" => "Str|>>>:" . $this->getTranslation()->_('miss_args')
            ];

            //验证
            $this->validateCheck($this->paramIn, $validations);
            $page_num = $this->processingDefault($this->paramIn, 'page_num', 2,InventoryCheckEnums::PAGE_NUM);
            $page_size = $this->processingDefault($this->paramIn, 'page_size', 2,InventoryCheckEnums::PAGE_SIZE);
            $name = $this->processingDefault($this->paramIn, 'name', 1);
            $list = $this->inventoryCheckServer->getAssetListByName($name, $page_num, $page_size);
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("taskListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 资产盘点-记录首次点击开始盘点按钮的时间
     * @api https://yapi.flashexpress.pub/project/93/interface/api/34950
     */
    public function startAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations = [
                'inventory_check_id' => 'Required|IntGe:1|>>>:inventory check id'. $this->getTranslation()->_('miss_args'), //盘点单ID
            ];
            //验证
            $this->validateCheck($paramIn, $validations);
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $data = $this->inventoryCheckServer->start($paramIn);
            $this->jsonReturn($this->checkReturn(array('data' => $data)));
        } catch (ValidationException $e){
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("startAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 检测单项资产是否在该员工名下
     * @api https://yapi.flashexpress.pub/project/93/interface/api/34887
     */
    public function isOwnerAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验、资产盘点-盘到（sn有误）、没盘到、扫码盘点
            $validations = [
                'inventory_check_id' => 'Required|IntGe:1|>>>:inventory check id'. $this->getTranslation()->_('miss_args'), //盘点单ID
                'asset_code' => 'Required|StrLenGeLe:1,100|>>>:asset code'. $this->getTranslation()->_('miss_args'),//资产编码
            ];
            //验证
            $this->validateCheck($paramIn, $validations);
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $data = $this->inventoryCheckServer->isOwner($paramIn);
            $data['type'] = 1;
            $this->jsonReturn($this->checkReturn(array('data'=> $data)));
        } catch (ValidationException $e) {
            if ($e->getCode() == 0) {
                return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
            } else if ($e->getCode() == 2) {
                //资产不在员工名下
                return $this->jsonReturn($this->checkReturn(array('data'=> ['check_asset_id'=>0, 'type'=>2], 'msg'=>$e->getMessage())));
            } else{
                return $this->jsonReturn(self::checkReturn(['code'=>$e->getCode(), 'msg'=>$e->getMessage()]));
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("isOwnerAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(['code'=>$e->getCode(), 'msg'=>$e->getMessage()]));
        }
    }

    /**
     * 资产盘点～单项资产～盘到（sn有误）、没盘到
     * @api https://yapi.flashexpress.pub/project/93/interface/api/34796
     */
    public function singleInventoryAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验、资产盘点-盘到（sn有误）、没盘到
            $validations = [
                'inventory_check_id' => 'Required|IntGe:1|>>>:inventory check id'. $this->getTranslation()->_('miss_args'), //盘点单ID
                'staff_rel_id'  => 'Required|IntGe:1|>>>:staff rel id error'. $this->getTranslation()->_('miss_args'),//任务ID
                'assets_goods_id' => 'Required|IntGe:1|>>>:assets goods id'. $this->getTranslation()->_('miss_args'),//资产商品ID
                'is_public' => 'Required|IntIn:0,1|>>>:is public'. $this->getTranslation()->_('miss_args'),//1 公共资产 0个人资产
                'bar_code'  => 'Required|StrLenLe:20|>>>:bar code'. $this->getTranslation()->_('miss_args'),//barcode
                'goods_name_en'  => 'StrLenLe:100|>>>:goods name en'. $this->getTranslation()->_('miss_args'),//资产名称（英文）
                'goods_name_th'  => 'StrLenLe:100|>>>:goods name th'. $this->getTranslation()->_('miss_args'),//资产名称（泰文）
                'goods_name_zh'  => 'StrLenLe:100|>>>:goods name zh'. $this->getTranslation()->_('miss_args'),//资产名称（中文）
                'type' => 'Required|IntIn:'.InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_GET.','.InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_NO.'|>>>:type'.$this->getTranslation()->_('miss_args'),//盘点类型
                'assets_info_id' => 'Required|IntGe:1|>>>:assets info id'. $this->getTranslation()->_('miss_args'),//资产ID
                'asset_code' => 'IfIntEq:type,'.InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_CODE_GET.'|Required|StrLenGeLe:1,100|>>>:asset code'. $this->getTranslation()->_('asset_code_required'),//资产编码
                'sn_code'   => 'StrLenLe:50|>>>:sn code'. $this->getTranslation()->_('miss_args'),//sn_code
                'is_revise_sn_code' => 'IntIn:0,1|>>>:is revise sn code'. $this->getTranslation()->_('miss_args'),//sn有错误 0否，1是
                'revise_sn_code' => 'IfIntEq:is_revise_sn_code,1|Required|StrLenGeLe:4,50|>>>:revise sn code'. $this->getTranslation()->_('revise_sn_code_required'),//修正后的sn编码
                'sn_code_file_arr'                => 'ArrLenGeLe:0,1',//sn 图片
                'sn_code_file_arr[*].file_name'   => 'StrLenGeLe:0,300',
                'sn_code_file_arr[*].bucket_name' => 'StrLenGeLe:0,300',
                'sn_code_file_arr[*].object_key'  => 'StrLenGeLe:0,300',
                'reason' => 'IfIntEq:type,'.InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_NO.'|Required|StrLenGeLe:5,500|>>>:reason'. $this->getTranslation()->_('inventory_check_no_reason'),//没盘到原因必填
            ];
            //验证
            $this->validateCheck($paramIn, $validations);
            $paramIn['is_revise_sn_code'] = $this->processingDefault($paramIn, 'is_revise_sn_code', 2);//sn有错误 0否，1是默认为无错
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $data = $this->inventoryCheckServer->singleInventory($paramIn);
            $this->jsonReturn($this->checkReturn(array('data' => $data)));
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("inventoryAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 资产盘点～多项资产～修改数量、确认
     * @api https://yapi.flashexpress.pub/project/93/interface/api/34985
     */
    public function multipleInventoryAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验、多项资产修改数量、确认
            $validations = [
                'inventory_check_id' => 'Required|IntGe:1|>>>:inventory check id'. $this->getTranslation()->_('miss_args'), //盘点单ID
                'staff_rel_id'  => 'Required|IntGe:1|>>>:staff rel id error'. $this->getTranslation()->_('miss_args'),//任务ID
                'assets_goods_id' => 'Required|IntGe:1|>>>:assets goods id'. $this->getTranslation()->_('miss_args'),//资产商品ID
                'bar_code'  => 'Required|StrLenLe:20|>>>:bar code'. $this->getTranslation()->_('miss_args'),//barcode
                'goods_name_en'  => 'StrLenLe:100|>>>:goods name en'. $this->getTranslation()->_('miss_args'),//资产名称（英文）
                'goods_name_th'  => 'StrLenLe:100|>>>:goods name th'. $this->getTranslation()->_('miss_args'),//资产名称（泰文）
                'goods_name_zh'  => 'StrLenLe:100|>>>:goods name zh'. $this->getTranslation()->_('miss_args'),//资产名称（中文）
                'sys_asset_num' =>'Required|IntGe:1|>>>:sys asset num'. $this->getTranslation()->_('miss_args'),//多项资产系统中记录的数量
                'actual_asset_num' =>'IntGe:0|>>>:actual asset num'. $this->getTranslation()->_('actual_asset_num_required'),//多项资产员工手里实际的数量
            ];
            //验证
            $this->validateCheck($paramIn, $validations);
            //如果员工实际的资产数量比系统中记录的数量小，原因没有填写或者填写了原因小于5个字或者大于500个字提示
            if ((intval($paramIn['actual_asset_num']) < intval($paramIn['sys_asset_num'])) && (empty($paramIn['reason']) || mb_strlen($paramIn['reason'])<5 || mb_strlen($paramIn['reason'])>500)) {
                throw new ValidationException('reason'.$this->getTranslation()->_('inventory_check_num_small_reason'));
            }
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $data = $this->inventoryCheckServer->multipleInventory($paramIn);
            $this->jsonReturn($this->checkReturn(array('data' => $data)));
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("multipleInventoryAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 资产盘点～手动添加、扫码新增
     * @api https://yapi.flashexpress.pub/project/93/interface/api/35020
     */
    public function addInventoryAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations = [
                'inventory_check_id' => 'Required|IntGe:1|>>>:inventory check id'. $this->getTranslation()->_('miss_args'), //盘点单ID
                'goods_name_en'  => 'Required|StrLenLe:100|>>>:goods name en'. $this->getTranslation()->_('asset_name_required'),//资产名称（英文）
                'goods_name_th'  => 'Required|StrLenLe:100|>>>:goods name th'. $this->getTranslation()->_('asset_name_required'),//资产名称（泰文）
                'goods_name_zh'  => 'Required|StrLenLe:100|>>>:goods name zh'. $this->getTranslation()->_('asset_name_required'),//资产名称（中文）
                'bar_code'  => 'StrLenLe:20|>>>:bar code'. $this->getTranslation()->_('miss_args'),//barcode
                'sn_code'   => 'StrLenLe:50|>>>:sn code'. $this->getTranslation()->_('sn_code_required'),//sn_code
                'asset_code' => 'StrLenLe:100|>>>:asset code'. $this->getTranslation()->_('asset_code_required'),//资产编码
                'asset_num' =>'IntGtLt:0,10000|>>>:asset num'. $this->getTranslation()->_('hand_add_asset_num_max'),//数量
                'type' => 'Required|IntIn:'.InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_HAND_ADD.','.InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_CODE_ADD.'|>>>:type'.$this->getTranslation()->_('miss_args'),//盘点类型,3手动新增，5扫码新增
            ];
            //验证
            $this->validateCheck($paramIn, $validations);
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $data = $this->inventoryCheckServer->addInventory($paramIn);
            $this->jsonReturn($this->checkReturn(array('data' => $data)));
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("addInventoryAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

    }

    /**
     * 资产盘点-记录点击结束盘点按钮的信息
     * @api https://yapi.flashexpress.pub/project/93/interface/api/34971
     */
    public function stopAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations = [
                'inventory_check_id' => 'Required|IntGe:1|>>>:inventory check id'. $this->getTranslation()->_('miss_args'), //盘点单ID
            ];
            //验证
            $this->validateCheck($paramIn, $validations);
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $data = $this->inventoryCheckServer->stop($paramIn);
            $this->jsonReturn($this->checkReturn(array('data' => $data)));
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }  catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("stopAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }
}