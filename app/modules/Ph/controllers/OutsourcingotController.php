<?php
/**
 * Author: Bruce
 * Date  : 2023-05-26 17:36
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\Ph\Server\OutsourcingOTServer;


class OutsourcingotController extends Controllers\ControllerBase
{

    /**
     * 获取基本数据
     * @return null
     */
    public function getOutsourcingOTPreAction()
    {
        $store_list = (new OutsourcingOTServer($this->lang, $this->timezone))->getOutsourcingOTPre($this->userinfo);
        return $this->jsonReturn(["code" => ErrCode::SUCCESS, "msg" => "success", 'data' => $store_list]);
    }

    /**
     * 提交外协员工加班工单，此时不创建审批流，需要向osm发送push
     * 真正发起审批的操作 在osm 配置好 外协人员后才可以 创建审批流
     * @return void
     * @throws ValidationException
     */
    public function addAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] = $this->userinfo;
        $validations         = [
            // 网点id string 必填, 如果为空则提示：不能为空
            "store_id"               => "Required|StrLenGe:1|>>>:".$this->getTranslation()->_('parameter_is_empty',
                    ['param' => 'store_id']),
            // 加班开始时间 string 必填 格式 Y-m-d H:s:i 正点或者半点 如果为空则提示：不能为空
            "ot_start_time"          => "Required|StrLenGe:1|>>>:".$this->getTranslation()->_('parameter_is_empty',
                    ['param' => 'ot_start_time']),
            // 时长 strinig 必填 格式2 or 2.5 如果为空则提示：不能为空
            "duration"               => "StrLenGeLe:1,3|>>>:".$this->getTranslation()->_('parameter_is_empty',
                    ['param' => 'duration']),
            // 外协员工人数 ini 必填 最大三位数
            "demand_num"             => "IntGeLe:1,999",
            // 外协公司id，必填 如果为空则提示：不能为空
            "outsourcing_company_id" => "StrLenGe:1|>>>:".$this->getTranslation()->_('parameter_is_empty',
                    ['param' => 'outsourcing_company_id']),
            // 加班原因，必填，string 字数在2-800之间 超出范围 提示 ：必须在2-800个字之间
            "reason"                 => "StrLenGeLe:2,800",
            "img"                    => "Arr|ArrLenGeLe:1,3",
            'img[*]'                 => 'Required|StrLenGe:1|>>>:img error',
        ];
        $this->validateCheck($paramIn, $validations);

        $bool = (new OutsourcingOTServer($this->lang, $this->timezone))->isHubFenBoManger($this->userinfo);
        // 不是分拨经理 不能请求该controller, 直接提示无权限
        if (false === $bool) {
            throw new ValidationException("No permission");
        }

        $returnArr = (new OutsourcingOTServer($this->lang, $this->timezone))->addUseLock($paramIn, $this->userinfo);
        $this->jsonReturn($returnArr);
    }
}