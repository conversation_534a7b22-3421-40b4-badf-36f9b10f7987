<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers\HrController as BaseHrController;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\InteriorGoodsRepository;
use FlashExpress\bi\App\Server\DeliveryCodeServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\StaffServer;

class HrController extends BaseHrController
{
    /**
     * 到岗确认 重写的 目前只加了轮休默认休息日校验
     * @param int   entry_id  入职ID
     * @param int   status    1-到岗 2-未到岗
     * @return string
     */
    public function entryConfirmAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;
        $staffId = $this->userinfo['staff_id'];
        $logger  = $this->getDI()->get('logger');

        $param   = [
            'resume_id' => $paramIn['resume_id'],
            'staff_id'  => $staffId,

            // 辅导员ID
            'instructor_id' => !empty($paramIn['counselor_staff_id']) ? $paramIn['counselor_staff_id'] : '',
            'shift_id'  => $paramIn['shift_id'],
            'delivery_code' => !empty($paramIn['delivery_code']) ? $paramIn['delivery_code'] : '',
            'identity_status' => !empty($paramIn['identity_status']) ? $paramIn['identity_status'] : '',
            'hand_identity_url' => !empty($paramIn['hand_identity_url']) ? $paramIn['hand_identity_url'] : '',
        ];

        //[2]验证
        $validations = [
            "resume_id" => "Required|Int",
            "status"    => "Required|IntIn:1,2",
            "shift_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);


        // [2.1] 验证辅导工号是否存在
        if (!empty($param['instructor_id'])) {
            $exist_counselor_info = (new HrStaffInfoServer())::getUserInfoByStaffInfoId($param['instructor_id'], 'id');
            if (empty($exist_counselor_info->id)) {
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('counselor_not_exist')));
            }
        }

        // [2.3] 到岗 - 绑定辅导员校验
        if ($paramIn['status'] == 1) {
            $method = 'entryAddCounselorCheck';

            $logger->write_log("svc method {$method} param:" . json_encode($param), 'info');

            $fle_rpc = (new ApiClient("winhr_rpc",'',$method, $this->lang));
            $fle_rpc->setParams($param);
            $result = $fle_rpc->execute();


            $logger->write_log("svc method {$method} response:" . json_encode($result), 'info');

            if ($result['result']['code'] == 0) {
                $this->jsonReturn(self::checkReturn(-3, $result['result']['msg']));
            }
        }
        //校验轮休默认休息日&赋值
        $param['default_rest_day_date'] = [];
        if ($paramIn['status'] == 1 && isset($paramIn['working_day_rest_type'],$paramIn['default_rest_day_date'])) {
            if ($paramIn['working_day_rest_type'] == HrStaffInfoModel::WEEK_WORKING_DAY_6. HrStaffInfoModel::REST_TYPE_1
                && count((array)$paramIn['default_rest_day_date'])!=1) {
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('choose_default_rest_day_1')));
            }
            if ($paramIn['working_day_rest_type'] == HrStaffInfoModel::WEEK_WORKING_DAY_5. HrStaffInfoModel::REST_TYPE_1
                && count((array)$paramIn['default_rest_day_date'])!=2) {
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('choose_default_rest_day_2')));
            }
            if (in_array($paramIn['working_day_rest_type'], [
                HrStaffInfoModel::WEEK_WORKING_DAY_FREE . HrStaffInfoModel::REST_TYPE_1,
                HrStaffInfoModel::WEEK_WORKING_DAY_6 . HrStaffInfoModel::REST_TYPE_1,
                HrStaffInfoModel::WEEK_WORKING_DAY_5 . HrStaffInfoModel::REST_TYPE_1,
            ])) {
                $param['default_rest_day_date'] = (array)$paramIn['default_rest_day_date'];
            }
            $param['working_day_rest_type'] = $paramIn['working_day_rest_type'];
        }

        //[3]发送请求
        if ($paramIn['status'] == 1) {
            $method  = "entryAdd";
        } else {
            //新增取消原因
            $param['cancel_type']           = $paramIn['cancel_type'] ?? 0;
            $param['cancel_reason_type']    = $paramIn['cancel_reason_type'] ?? '';
            $param['cancel_reason_content'] = $paramIn['cancel_reason_content'] ?? '';

            $method  = "entryCancel";
        }

        $param['source'] = 'by';//区分到岗确认来源

        $logger->write_log("svc method {$method} param:" . json_encode($param), 'info');

        $fle_rpc = (new ApiClient("winhr_rpc",'',$method, $this->lang));
        $fle_rpc->setParams($param);
        $result = $fle_rpc->execute();

        $logger->write_log('interior_goods_repository_data' . json_encode($result).'/'.json_encode($paramIn), 'info');
        if ($result['result']['code'] == 1) {
            if ($paramIn['status'] == 1 && !empty($paramIn['barcode']) && !empty($result['result']['data']['staff_id'])) {
                //记录数据
                (new InteriorGoodsRepository())->addEntrySize(['barcode' => $paramIn['barcode'], 'staff_id' => $result['result']['data']['staff_id']], ['id' => $staffId]);
            }

            if ($paramIn['status'] == 1 && !empty($param['delivery_code']) && !empty($result['result']['data']['staff_id'])) {
                $entryInfo = HrEntryModel::findFirstByResumeId($paramIn['resume_id']);
                if (!empty($entryInfo)) {
                    $entryInfo->delivery_code = implode(',', $param['delivery_code']);
                    $entryInfo->save();
                }
                try {
                    $paramsArray = [
                        'staff_id'       => $result['result']['data']['staff_id'],
                        'operator_id'    => $staffId,
                        'store_id'       => $paramIn['store_id'],
                        'delivery_codes' => $paramIn['delivery_code'],
                    ];
                    $server = new DeliveryCodeServer();
                    $rest = $server->bindDeliveryCode($paramsArray);
                    $this->logger->write_log('entryConfirmAction bind delivery code' . json_encode($paramsArray) . json_encode($rest) , 'info');
                } catch (\Exception $e) {
                    $this->logger->write_log('bindDeliveryCodee err' . $e->getMessage());
                }
            }

            $this->jsonReturn($this->checkReturn([
                'data' => $result['result']['data']
            ]));
        } else {
            $result = $result['result']['msg'] ?? $this->getTranslation()->_('4008');
            $logger->write_log("svc method {$method} response:" . json_encode($result), 'info');
            $this->jsonReturn(self::checkReturn(-3, $result));
        }
    }


    /**
     * 获取到岗确认详情
     * @param int    entry_id  入职ID
     * @return string
     */
    public function getEntryDetailAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;
        $staffId = $this->userinfo['staff_id'];
        $logger  = $this->getDI()->get('logger');
        $method  = "getEntryDetail";


        //[2]验证
        $validations = [
            "resume_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $param   = [
            'userinfo'  => ['id' => $staffId],
            'resume_id' => $paramIn['resume_id'],
        ];

        //[3]发送请求
        $logger->write_log("svc method {$method} param:" . json_encode($param), 'info');

        $fle_rpc = (new ApiClient("winhr_rpc",'',$method, $this->lang));
        $fle_rpc->setParams($param);
        $result = $fle_rpc->execute();

        if ($result['result']['code'] == 1) {
            if (isCountry(['TH','PH','MY']) && !empty($result['result']['data'])) {
                $result['result'] = (new StaffServer($this->lang))->replace_entry_data($result['result']);
            }
            //性别转义
            $result['result']['data']['sex_title'] =
                $result['result']['data']['sex'] == \FlashExpress\bi\App\library\enums::$sex_type['male']
                    ? $this->getTranslation()->_('4900')
                    : $this->getTranslation()->_('4901');

            $size_arr = [];
            $logger->write_log('get_entry_detail_data' . json_encode($result), 'info');
            $goods_repository = new InteriorGoodsRepository();
            if (!empty($result['result']['data']['department_id']) && !empty($result['result']['data']['position_id']) && !empty($result['result']['data']['worknode_id'])) {
                $size_arr = $goods_repository->getGoodsId($result['result']['data']['department_id'], $result['result']['data']['position_id'], $result['result']['data']['worknode_id']);
            }
            //个人代理不展示尺寸
            if (!empty($result['result']['data']['hire_type'])) {
                $size_arr['is_size'] = $result['result']['data']['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID ? 0 : $size_arr['is_size'];
            }
            $size_info = !empty($result['result']['data']['staff_id']) ? $goods_repository->getGoodsSize($result['result']['data']['staff_id']) : [] ;
            $result['result']['data']['size'] = $size_info['entry_size'] ?? '';
            $result['result']['data']['goods_id'] = $size_info['entry_goods_id'] ?? '';
            $result['result']['data']['is_size'] = $size_arr['is_size'] ?? 0;
            $result['result']['data']['size_img_path'] = !empty($size_arr['goods_size_all']) ? $size_arr['goods_size_all'][0]['size_img_path'] : '';
            $result['result']['data']['size_arr'] = $size_arr['goods_size_all'] ?? [];

            //获取派件码
            $staffServer = Tools::reBuildCountryInstance(new StaffServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
            $deliveryCodeInfo = $staffServer->getDeliveryCodeInfo($result['result']['data']);

            $result['result']['data']['delivery_code_info']    = $deliveryCodeInfo['delivery_code_info'] ?? [];
            $result['result']['data']['delivery_code']         = $deliveryCodeInfo['delivery_code'] ?? '';
            $result['result']['data']['is_show_delivery_code'] = $deliveryCodeInfo['is_show_delivery_code'] ? 1: 0; //1=显示 0=不显示

            // https://flashexpress.feishu.cn/wiki/SbiIwNq3YiCfdKkjeYccklxKnGe
            // 前端用这个结构
            $result['result']['data']['userinfo']['staff_id'] = $this->userinfo['staff_id'] ?? '';
            $result['result']['data']['userinfo']['name'] = $this->userinfo['name'] ?? '';

            $this->jsonReturn($this->checkReturn([
                'data' => $result['result']['data']
            ]));
        } else {
            $resultMsg = $result['result']['msg'] ?? $this->getTranslation()->_('4008');
            $this->jsonReturn(self::checkReturn(-3, $resultMsg));
        }
    }

}
