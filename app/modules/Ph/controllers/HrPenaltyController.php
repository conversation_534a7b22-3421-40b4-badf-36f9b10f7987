<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers;


use FlashExpress\bi\App\Controllers\HrPenaltyController as GlobalControllerBase;
use FlashExpress\bi\App\Modules\Ph\Server\HrPenaltyServer;
use FlashExpress\bi\App\Server\BackyardServer;

/**
 * Hr处罚申诉申请
 */
class HrPenaltyController extends GlobalControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * @description 发起申诉
     */
    public function addAppealAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['store_id'] = $this->userinfo['organization_id'];

        $validations = [
            "appeal_reason"     => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('1019'),
            "penalty_detail_id" => "Required|Int",
            "image"             => "Required|Arr|ArrLenGeLe:1,9",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $returnArr = (new HrPenaltyServer($this->lang, $this->timezone))->addPenaltyAppealUseLock($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * @description 申诉审批
     */
    public function updateAppealAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];

        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|Int",
            "reason"        => 'IfIntEq:status,2|Required|StrLenGeLe:1,500',
            "reject_reason" => "IfIntEq:status,3|Required|StrLenGeLe:1,500",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $returnArr = (new HrPenaltyServer($this->lang,$this->timezone))->updatePenaltyAppealUseLock($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * @description 获取处罚详情
     */
    public function getPenaltyDetailAction()
    {
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        if (empty($param)) {
            $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }

        //兼容从消息详情、考勤日历-处罚详情-申诉，2个页面获取处罚详情的case
        if (!isset($param['msg_id']) && !isset($param['penalty_detail_id'])) {
            $this->jsonReturn(['code' => 0, 'msg' => $this->getTranslation()->_('miss_args'), 'data' => []]);
        }

        $penaltyDetailId = '';
        if (!empty($param['msg_id'])) {
            $server = new BackyardServer($this->lang, $this->timezone);
            $msgInfo = $server->getMessageCourierDetail($param);
            if (empty($msgInfo)) {
                $this->jsonReturn(['code' => 0, 'msg' => '员工没有收到该消息', 'data' => []]);
            }
            $penaltyDetailId = $msgInfo['related_id'];
        }

        if (!empty($param['penalty_detail_id'])) {
            $penaltyDetailId = $param['penalty_detail_id'];
        }

        $returnArr = (new HrPenaltyServer($this->lang,$this->timezone))->getPenaltyDetail($penaltyDetailId);

        $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $returnArr]);
    }

    /**
     * @description 获取处罚详情
     */
    public function getListAction()
    {
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        $validations = [
            "month"      => "Required|StrLenGeLe:1,7",
            "half_month" => "Required|Int",
        ];
        $this->validateCheck($this->paramIn, $validations);

        $returnArr = (new HrPenaltyServer($this->lang,$this->timezone))->getList($param);
        $this->jsonReturn($returnArr);
    }

    /**
     * @description 获取处罚详情
     */
    public function getPenaltyDetailByDateAction()
    {
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        $validations = [
            "attendance_date" => "Required|Date",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //获取处罚详情
        $returnArr = (new HrPenaltyServer($this->lang,$this->timezone))->getDetailInfo($param);
        $this->jsonReturn($returnArr);
    }

    public function getValidMonthAction()
    {
        $returnArr = (new HrPenaltyServer($this->lang,$this->timezone))->getValidMonth();
        $this->jsonReturn(['code' => 1, 'msg' => 'ok', 'data' => $returnArr]);
    }
}