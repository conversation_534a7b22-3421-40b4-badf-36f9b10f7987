<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 11/3/21
 * Time: 7:58 PM
 */

namespace FlashExpress\bi\App\Modules\Ph\Controllers;
use FlashExpress\bi\App\Controllers\ControllerBase;
use FlashExpress\bi\App\Modules\Ph\Server\OvertimeServer;
use Exception;

class OvertimeController extends ControllerBase{

    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode($this->request->getRawBody(),true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
        $this->url_log($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 获取加班类型
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getTypeOvertimeAction()
    {

        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['job_title']= $this->userinfo['job_title'];//id
        $paramIn['organization_type']= $this->userinfo['organization_type'];
        $paramIn['organization_id']= $this->userinfo['organization_id'];
        $paramIn['department_id']= $this->userinfo['department_id'];//fle 部门为子部门id 对应 hr系统 node_department_id
        $returnArr = (new OvertimeServer($this->lang, $this->timezone, $this->userinfo))->getTypeOvertime($paramIn,$this->userinfo);

        $this->jsonReturn($returnArr);
    }


    /**
     * 新建加班
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function addOvertimeAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] = $this->userinfo;
        $validations         = [
            "staff_id"  => "Required|Int",
            "reason"    => "Required|StrLenGeLe:1,1000|>>>:" . $this->getTranslation()->_('5110'),
        ];
        $this->validateCheck($paramIn, $validations);
        $returnArr = (new OvertimeServer($this->lang, $this->timezone, $this->userinfo))->setLockConf(60,true)->addOvertimeV3UseLock($paramIn);
        $this->jsonReturn($returnArr);
    }



}