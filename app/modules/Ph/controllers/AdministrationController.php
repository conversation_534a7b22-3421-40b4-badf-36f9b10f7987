<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Modules\Ph\Server\AdministrationOrderServer;
use FlashExpress\bi\App\Server\AdministrationQuestionTypeServer;
use FlashExpress\bi\App\Server\AdministrationLogServer;
use Exception;

class AdministrationController extends BaseController
{
    protected $paramIn;
    public    $loginUserIdentity = [
        //'isSubDepart' => false, // 默认不属于Flash Fulfillment子公司
        'isHrJob'     => false, // 默认不属于Operations Support Specialist 职位
    ];

    /**
     * @throws Exception
     */
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }

        $this->loginUserIdentity['isHrJob'] = (new AdministrationOrderServer())->checkDealAdministrationOrderPermission($this->userinfo);
    }

    /**
     * 行政工单上报接口
     * @return Json
     */
    public function addAction()
    {
        $paramIn = $this->paramIn;
        //[1]入参校验
        $validations = [
            "question_type_id" => "Required|IntGt:0|>>>:question_type_id param error", // 问题类型 必填
            "store_id"         => "Required|StrLenGeLe:1,10|>>>:".$this->getTranslation()->_('max_char_len',
                    ['len' => 10]),
            "mobile"           => "Required|StrLenGeLe:1,50|>>>:".$this->getTranslation()->_('max_char_len', ['len' => 50]), // ph 联系方式必填
            "info"             => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('max_char_len',
                    ['len' => 500]),
            "pics"             => "Arr|ArrLenLe:10|>>>:".$this->getTranslation()->_('max_pics_10'), // 最多10个 可以为空
        ];
        $this->validateCheck($paramIn, $validations);

        // pic 数组格式的验证
        if (!empty($paramIn['pics'])) {
            $ret = (new AdministrationOrderServer())->checkPics($paramIn['pics']);
            if (is_bool($ret) && false === $ret) {
                $this->jsonReturn(["code" => ErrCode::FAIL, "msg" => "pic param error", 'data' => []]);
            }
        }

        // [2] 业务逻辑处理
        $data = (new AdministrationOrderServer())->addAdministrationOrderUseLock($paramIn, $this->userinfo);
        return $this->jsonReturn($data);
    }

    /**
     * 获取问题类型数据
     * @return Json
     */
    public function getItemTypeAction()
    {
        $list = (new AdministrationQuestionTypeServer($this->lang))->getQuestionType();
        if (is_bool($list) && false === $list) {
            return $this->jsonReturn(["code" => ErrCode::FAIL, "msg" => "question type id not exist", 'data' => []]);
        }
        return $this->jsonReturn(["code" => ErrCode::SUCCESS, "msg" => "success", 'data' => $list]);
    }


    /** 获取提交人默认信息
     * @return Json
     */
    public function getDefaultAction()
    {
        $data = (new AdministrationOrderServer())->getDefault($this->userinfo);
        return $this->jsonReturn($data);
    }

    /**
     * 获取 回复数 待回复数
     * @return Json
     */
    public function getNumsAction()
    {
        $data = (new AdministrationOrderServer())->getXzNums($this->userinfo);
        return $this->jsonReturn(["code" => ErrCode::SUCCESS, "msg" => "success", 'data' => $data]);
    }

    /**
     * 获取已回复、待回复、已关闭工单列表
     * @return Json
     */
    public function listAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "status"    => "Required|IntIn:1,2,3|>>>:status param error", // 1待回复，2已回复，3已关闭
            "from"      => "Required|IntIn:1,2|>>>:from param error",     // 1 请求来自提交 2 请求来自菜单 "我的工单"
            'page_num'  => "IntGe:1",
            'page_size' => "IntGe:1",
        ];

        $this->validateCheck($paramIn, $validations);

        // 传递为空时 查询所有类型的数据
        if (empty($paramIn['question_type_id'])) {
            $paramIn['question_type_id'] = 0;
        }

        $data    = (new AdministrationOrderServer($this->lang))->list($paramIn, $this->userinfo);
        return $this->jsonReturn(["code" => ErrCode::SUCCESS, "msg" => "success", 'data' => $data]);
    }

    /**
     * 工单关闭接口
     * @return Json
     */
    public function closeAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "id"           => "Required|IntGe:1|>>>:id param error",
            'close_reason' => "Required|StrLenGeLe:1,50|>>>:".sprintf($this->getTranslation()->_('parameter_length'),
                    'close_reason', 1, 50),
        ];

        // [1] 参数验证
        $this->validateCheck($paramIn, $validations);
        $paramIn = array_only($paramIn, array_keys($validations));

        // [2] 业务处理
        $result = (new AdministrationOrderServer())->closeOrderUseLock($paramIn, $this->userinfo);
        return $this->jsonReturn($result);
    }

    /**
     * 获取沟通记录
     * @return Json
     */
    public function getLogListAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "id"        => "Required|IntGe:1|>>>:id param error",
            'page_num'  => "IntGe:1",
            'page_size' => "IntGe:1",
        ];

        // [1] 参数验证
        $this->validateCheck($paramIn, $validations);
        $paramIn = array_only($paramIn, array_keys($validations));

        // [2] 业务处理
        $data = (new AdministrationLogServer($this->lang))->getLogList($paramIn, $this->userinfo, $this->loginUserIdentity);
        return $this->jsonReturn(["code" => ErrCode::SUCCESS, "msg" => "success", 'data' => $data]);
    }

    /**
     * 消息回复
     * @return Json
     */
    public function replyAction()
    {
        $paramIn = $this->paramIn;
        //[1]入参校验
        $validations = [
            "id"   => "Required|IntGt:0|>>>:id param error", // 工单id
            "mark" => "StrLenGeLe:0,500|>>>:".$this->getTranslation()->_('max_char_len',
                    ['len' => 500]),
            "pics" => "Arr|ArrLenLe:10|>>>:".$this->getTranslation()->_('max_pics_10'), // 最多10个 可以为空
            "from" => "Required|IntIn:1,2|>>>:from param error",     // 1 请求来自提交 2 请求来自菜单 "我的工单"
        ];
        $this->validateCheck($paramIn, $validations);

        // 如果 mark 和 图片都为空 则给出提示
        if (empty($paramIn['mark']) && empty($paramIn['pics'])) {
            $this->jsonReturn(["code" => ErrCode::FAIL, "msg" => "params empty", 'data' => []]);
        }

        // pic 数组格式的验证
        if (!empty($paramIn['pics'])) {
            $ret = (new AdministrationOrderServer())->checkPics($paramIn['pics']);
            if (is_bool($ret) && false === $ret) {
                $this->jsonReturn(["code" => ErrCode::FAIL, "msg" => "pic param error", 'data' => []]);
            }
        }

        $paramIn = array_only($paramIn, array_keys($validations));
        // [2] 业务逻辑处理
        $result = (new AdministrationLogServer())->replyUseLock($paramIn, $this->userinfo, $this->loginUserIdentity);
        return $this->jsonReturn($result);
    }

    /** 获取工单详情
     * @return Json
     */
    public function orderDetailAction()
    {
        $paramIn = $this->paramIn;
        //[1]入参校验
        $validations = [
            "id" => "Required|IntGt:0|>>>:id param error", // 工单id
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn = array_only($paramIn, array_keys($validations));
        // [2] 业务逻辑处理
        $result = (new AdministrationOrderServer($this->lang))->orderDetail($paramIn, $this->userinfo, $this->loginUserIdentity);
        return $this->jsonReturn($result);
    }

}
