<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON>
 * Date: 2021/12/1
 * Time: 14:32
 */

namespace FlashExpress\bi\App\Modules\Ph\Controllers;
use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Server\ResumeRecommendServer;

class CommitmentController extends BaseController
{
    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 获取推荐简历电子签
     */
    public function getCommitmentAction()
    {
        //[1]入参
//        $validations = [
//            "msg_id" => "Required|StrLenGeLe:1,100",
//        ];
//        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        try{
            $info = (new ResumeRecommendServer($this->lang, $this->timezone))->getCommitment($this->userinfo);
            //[3]数据返回
            $return_arr = ['code'=>1,'data'=>$info];
            $this->jsonReturn(self::checkReturn($return_arr));
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("getCommitment异常信息：  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString());
            $this->jsonReturn(self::checkReturn(-3, 'server error'));
        }
    }

    /**
     * 前端 承诺书电子签详情
     */
    public function getCommitmentInfoAction()
    {

        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        //[2]业务处理
        try {
            $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->getCommitmentInfo($paramIn['userinfo']);
            //[3]数据返回
            $return_arr = ['code' => 1, 'data' => $returnArr];
            $this->jsonReturn($return_arr);
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:getResumeInit" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString());
            $this->jsonReturn(self::checkReturn(-3, 'server error'));
        }
    }


    public function demoPdfAction(){
        (new ResumeRecommendServer($this->lang,$this->timezone))->demoPdf();
    }
    /**
     * 获取授权书pdf
     */
    public function getCommitentPdfAction(){


        try{

            $pdf_file_data =  (new ResumeRecommendServer($this->lang,$this->timezone))->getCommitmentPdf($this->userinfo);
            $pdf_file = $pdf_file_data ? env("img_prefix").$pdf_file_data['object_key'] : '';
            $return['data'] = ['pdf_path'=> $pdf_file];
            $this->jsonReturn(self::checkReturn($return));

        }catch (BusinessException $be){
            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $be->getFile() . " E_Line:" . $be->getLine() ." E_function:getCommitentPdfAction". " E_Msg: " . $be->getMessage() . " E_Trace: " . $be->getTraceAsString(),'info');
            $this->jsonReturn(self::checkReturn(-3, 'server error'));
        }catch (\Exception $e){
            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() ." E_function:getCommitentPdfAction". " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString(),'error');
            $this->jsonReturn(self::checkReturn(-3, 'server error'));
        }
    }

    /**
     * 电子合同签字提交接口
     */
    public function saveCommitmentSignAction(){

        $paramIn             = $this->paramIn;
        $validations         = [
            "sign_img" => "Required|StrLenGeLe:1,200",
        ];
        $this->validateCheck($paramIn, $validations);
        $logger = $this->getDI()->get('logger');
        try {
            //todo 保存电子签
            (new ResumeRecommendServer($this->lang,$this->timezone))->saveCommitmentSign($this->userinfo,$paramIn['sign_img']);
            //返回
            $this->jsonReturn(self::checkReturn(1));

        }catch (BusinessException $be){
            //系统记录异常
            $logger->write_log("by简历授权书电子签-saveCommitentSignAction 异常 :" . $be->getMessage(), 'info');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        } catch (Exception $e) {

            //系统记录异常
            $logger->write_log("by简历授权书电子签-saveCommitentSignAction 异常 :" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }
}