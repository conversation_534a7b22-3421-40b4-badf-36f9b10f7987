<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use Exception;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\Ph\Server\BackyardServer;
use FlashExpress\bi\App\Modules\Ph\Server\MessageServer;
use FlashExpress\bi\App\Server\SalaryServer;

class BackyardController extends BaseController
{

    public $staff_id;
    public $miss_args;
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $userinfo=$this->userinfo;
        $this->staff_id=$userinfo['id'];
        $this->miss_args=$this->getTranslation()->_('miss_args');

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();

    }
    public function un_read_and_auditAction(){
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] =  $this->userinfo;
        $validations         = [
            "staff_id" => "Required|Int"
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $server = new BackyardServer($this->lang, $this->timezone);
        if (env('un_read_and_audit', 0)) {
            $return = $server->getRedDotsNum($paramIn);
        } else {
            $return = [
                'un_read_num'             => (string)'0',// 我的-CEO信箱
                'backyard_unread_num'     => '0',        // 消息tab
                'un_audit_num'            => '0',        // 审批tab
                "un_only_audit_num"       => '0',        //首页弹出提示
                "un_myinfo_count"         => '0',        // 我的-个人信息
                "un_asset_management_num" => '0',        //我的-资产管理
                "un_myabout_all"          => '0',        //我的tab （总和）
            ];
        }

        $this->getDI()->get('logger')->write_log("un_read_and_audit_{$this->userinfo['staff_id']}: ".json_encode($return,JSON_UNESCAPED_UNICODE),'info');
        return $this->jsonReturn($this->checkReturn(array('data' => $return)));
    }


    //警告书签名保存接口
    public function sign_warningAction(){
        $param = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "msg_id" => "Required|StrLenGeLe:1,100",
            "url"    => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        //[2]业务处理\
        $returnArr = $this->atomicLock(function () use ($param) {
            $message_server = new MessageServer($this->lang,$this->timezone);
            $message_server->sign_for_warning($param);
        }, 'sign_warning_msg_id_' . $param['msg_id'],20);
        if ($returnArr === false) { //没有获取到锁
            return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
        }
        //[3]数据返回
        $this->jsonReturn($this->checkReturn(1));
    }


    /**
     * 获取是否需要证人
     * @return void
     * @throws ValidationException
     */
    public function getWarningMessageInfoAction()
    {
        $param = $this->paramIn;

        $message_server = new MessageServer($this->lang,$this->timezone);
        $returnArr = $message_server->getWarningMessageInfo($param, $this->userinfo['staff_id']);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data'=>$returnArr]));
    }

    /**
     * @return void
     * @throws ValidationException
     * @throws \ReflectionException
     */
    public function get_sign_warning_pdf_dataAction()
    {
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        //[2]业务处理
        $message_server = new MessageServer($this->lang, $this->timezone);
        $returnArr      = $message_server->get_sign_warning_pdf_data($param);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data'=>$returnArr]));
    }


    /**
     * 警告书签字证人下拉列表
     * @return void
     * @throws Exception
     */
    public function sign_warning_witness_listAction()
    {
        $param                  = $this->paramIn;
        $validations         = [
            "msg_id" => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        //[2]业务处理
        $message_server = new MessageServer($this->lang, $this->timezone);
        $returnArr      = $message_server->sign_warning_witness_list($param);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data'=>$returnArr]));
    }



}
