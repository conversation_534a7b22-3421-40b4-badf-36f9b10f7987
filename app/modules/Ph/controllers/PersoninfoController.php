<?php
namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Modules\Ph\Server\PersoninfoServer;
use FlashExpress\bi\App\Controllers\PersoninfoController as BasePersoninfoController;
use FlashExpress\bi\App\Server\SysServer;


class PersoninfoController extends BasePersoninfoController
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
        }
        $this->paramIn = filter_param($this->paramIn);
    }



    /**
     * 个人信息
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function personAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->getPerson($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * @description:修改个人信息
     * @param null
     * @return     :
     */
    public function  updateStaffInfoAction(){
        //[1]入参
        if(!empty($this->paramIn['tax_card']) && !preg_match('/^[0-9]{9}$/', $this->paramIn['tax_card'])){
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_("tax_card_len_error")));
        }

        if (empty($this->paramIn['tax_card'])) {
            $this->paramIn['empty_field'] = ['tax_card'];
        }
        $this->paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr =  (new \FlashExpress\bi\App\Server\PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->updatePersonInfobymobileUseLock($this->paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * @description:是否显示前公司纳税收入项
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/7/12 17:29
     */

    public function isShowMenuAction(){
        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->isShowFormerTaxableIncomeFromCache($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);

    }

    /**
     * @description:添加公司纳税收入项
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/7/12 18:14
     */
    public function saveFormerTaxableIncomeAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "tax_amount" => "Required|FloatLe:10000000.0|>>>:".$this->getTranslation()->_('former_taxable_income_tax_amount'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $returnArr           = (new PersoninfoServer($this->lang, $this->timezone,
            $this->userinfo))->saveFormerTaxableIncomeUseLock($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * @description:获取公司纳税收入项详情
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/7/12 18:14
     */
    public function getDetailFormerTaxableIncomeAction(){
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = (new PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->getDetailFormerTaxableIncome($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 菲律宾新增身份证ai审核接口
     * @return JSON
     */
    public function aiIdCardAuditAction()
    {
        //[1]入参
        $paramIn     = $this->paramIn;
        $validations = [
            "file_url"    => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('max_char_len',
                    ['len' => 500]),
            // 1、身份证审核 2、备用银行卡审核 3、社保号审核 4、公积金审核 5、医疗保险号审核 6、税号审核
            // 备用银行卡不走ai审核，暂时接受2参数
            "type"        => "Required|IntIn:1,3,4,5,6|>>>:".$this->getTranslation()->_('miss_args'),
            "card_number" => "Required|StrLenGeLe:1,32|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $model               = new PersoninfoServer($this->lang, $this->timezone);
        $return              = $model->ai_id_card_auditUseLock($paramIn);
        return $this->jsonReturn($return);
    }

    /**
     * 证件提交接口
     * @return JSON
     */
    public function aiIdCardSubmitAction()
    {
        //[1]入参
        $paramIn     = $this->paramIn;
        $validations = [
            "file_url"    => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('max_char_len',
                    ['len' => 500]),
            // 1、身份证审核 2、备用银行卡审核 3、社保号审核 4、公积金审核 5、医疗保险号审核 6、税号审核
            // 备用银行卡不走ai审核，暂时接受2参数
            "type"        => "Required|IntIn:1,3,4,5,6|>>>:".$this->getTranslation()->_('miss_args'),
            "card_number" => "Required|StrLenGeLe:1,32|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $phPersonObj = (new PersoninfoServer($this->lang, $this->timezone));

        if (HrStaffAnnexInfoModel::TYPE_ID_CARD == $paramIn['type']) {
            $paramIn['identity'] = $paramIn['card_number'];
        }
        if (HrStaffAnnexInfoModel::TYPE_SOCIAL_SECURITY == $paramIn['type']) {
            $paramIn['social_security_num'] = $paramIn['card_number'];
        }
        if (HrStaffAnnexInfoModel::TYPE_FUND == $paramIn['type']) {
            $paramIn['fund_num'] = $paramIn['card_number'];
        }
        if (HrStaffAnnexInfoModel::TYPE_MEDICAL_INSURANCE == $paramIn['type']) {
            $paramIn['medical_insurance_num'] = $paramIn['card_number'];
        }
        if (HrStaffAnnexInfoModel::TYPE_TAX_CARD == $paramIn['type']) {
            $paramIn['tax_card'] = $paramIn['card_number'];
        }
        // 更新各种卡号信息
        $result = $phPersonObj->updatePersonInfoUseLock($paramIn);
        if ($result['code'] == 1) {
            //之前调用ai_id_card_submit方法，现在修改为调用aiIdCardSubmit
            $return = $phPersonObj->ai_id_card_submitUseLock($paramIn);
            return $this->jsonReturn($return);
        }
        $this->jsonReturn($result);
    }

    /**
     * @Deprecated
     * ph 国家修改备用银行卡号 OR 手机号
     * @Access  public
     * @Return  jsonData
     * @throws ValidationException
     */
    public function updatePersonInfobymobileAction()
    {
        /**
         * 修改备用银行卡的传参如下：
         *  {
         *      "backup_bank_no":"************",
         *      "backup_bank_card_photo":"https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/backyardUpload/**********-40ed6770a226429690e9baa34d06abd6.png",
         *      "verify_code":"896443",
         *      "verify_type":1
         *  }
         * 修改手机号传参如下：
         *  {
         *      "mobile":"***********",
         *      "verify_code":"205296",
         *      "verify_type":1
         *  }
         *
         * 注意：修改该逻辑时注意兼容
         */
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        // 手机验证码验证
        if (!$this->check_code($paramIn, $paramIn['staff_id'])) {
            $returnData = [
                'code' => ErrCode::ERROR,
                'msg'  => $this->getTranslation()->_('auth_code_error'),
                'data' => null,
            ];
            $this->jsonReturn($returnData);
        }

        //[2]业务处理
        if (!empty($paramIn['bank_no']) && !empty($paramIn['bank_type'])){
            (new SysServer($this->lang, $this->timezone))->bankNoVerify($paramIn['bank_type'],$paramIn['bank_no']);
        }
        
        $personObj = (new PersoninfoServer($this->lang, $this->timezone,
            $this->userinfo));
        if (isset($paramIn['bank_no'])) {
            // 入参验证
            if (empty($paramIn['bank_card_photo'])) {
                return $this->jsonReturn([
                    'code' => ErrCode::ERROR,
                    'msg'  => 'bank_card_photo cannot be empty',
                    'data' => null,
                ]);
            }
            $result = $personObj->modifyHrStaffIdentityAnnexUseLock($paramIn);
        }

        if (isset($result) && $result['code'] != 1) {
            // 附件表如果更新失败 则直接返回
            $this->jsonReturn($result);
        }


        // 更新信息 备用银行卡 或者手机号 - 这里是更新员工信息的
        $returnArr = $personObj->updatePersonInfobymobileUseLock($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    public function fleetAllowanceAction()
    {
        $month = $this->request->get('month');
        $server              = new PersoninfoServer($this->lang, $this->timezone);
        $result = $server->getFleetAllowance($this->userinfo['staff_id'],$month);
        $this->jsonReturn($this->checkReturn(['data'=>$result]));
    }

}
