<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/9/7
 * Time: 4:05 PM
 */

namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\BusinesstripServer;


class BusinesstripController extends BaseController
{

    public function initialize()
    {
        parent::initialize();
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 创建出差申请(不包含外出申请)
     * @Access  public
     * @Param   request
     * @Return  array
     * @throws ValidationException
     */
    public function addTripAction()
    {
        //[1]入参和验证
        $paramIn                  = $this->paramIn;
        $userinfo                 = $this->userinfo;
        $validations              = [
            "oneway_or_roundtrip" => "Required|IntIn:1,2|>>>:".$this->getTranslation()->_('7122'),
            "departure_city"      => "Required|StrLenGeLe:1,50|>>>:".$this->getTranslation()->_('7128'),
            "destination_city"    => "Required|StrLenGeLe:1,50|>>>:".$this->getTranslation()->_('7128'),
            "start_time"          => "Required|Date|>>>:".$this->getTranslation()->_('7124'),
            "end_time"            => "Required|Date|>>>:".$this->getTranslation()->_('7124'),
            "remark"              => "Required|StrLenGeLe:10,500|>>>:".$this->getTranslation()->_('7126'),
            "business_trip_type"  => "Required|IntIn:1,2,3,4|>>>:出差类型不正确",
        ];
        $paramIn['traffic_tools'] = $paramIn['traffic_tools'] ?? 0;
        //普通出差
        if ($paramIn['business_trip_type'] != BusinessTripModel::BTY_YELLOW) {
            $validations['traffic_tools']      = "Required|IntIn:1,2,3,4|>>>:".$this->getTranslation()->_('7122');
            $validations['reason_application'] = "Required|StrLenGeLe:10,50|>>>:".$this->getTranslation()->_('7127');
        } else {
            $validations['traffic_tools']           = "Required|IntIn:3,4|>>>:".$this->getTranslation()->_('7122');
            $validations['car_no']                  = "Required|StrLenGeLe:2,10|>>>:".$this->getTranslation()->_('2105');//车牌号不正确
            $validations['reason_application_type'] = "Required|IntIn:1,2|>>>:出差理由类型不正确";
        }
        //如果有其他交通工具，输入名称
        if ($paramIn['traffic_tools'] == 4) {
            $validations['other_traffic_name'] = "Required|StrLenGeLe:1,20|>>>:".$this->getTranslation()->_('7124');
        }

        $tripServer = new BusinesstripServer($this->lang, $this->timezone);
        //如果是境外出差 需要选择目的地国家
        if ($paramIn['business_trip_type'] == BusinessTripModel::BTY_FOREIGN) {

            $countryRegion = $tripServer->getEnumsList();
            $validations['destination_country'] = 'Required|IntIn:'.implode(',',
                    array_column($countryRegion['destination_country'], 'value')).'|>>>:'.$this->getTranslation()->_('destination_country_hint');
            //如果是目的地国家是其他 必填 国家名称
            if (isset($paramIn['destination_country']) && $paramIn['destination_country'] == HrStaffInfoModel::WORKING_COUNTRY_OTHER) {
                $validations['destination_country_name'] = 'Required|StrLenGeLe:1,55|>>>:'.$this->getTranslation()->_('destination_country_name_hint');
            }
        }


        $this->validateCheck($paramIn, $validations);
        if ($paramIn['start_time'] > $paramIn['end_time']) {
            throw new ValidationException($this->getTranslation()->_('7125'));
        }
        //菲律宾出差最多7天 https://l8bx01gcjr.feishu.cn/docs/doccnikGJoOXf7kZ03v2GLettjb
        if ((strtotime($paramIn['end_time']) - strtotime($paramIn['start_time'])) / (24 * 3600) > 6) {
            throw new ValidationException($this->getTranslation()->_('trip_7'));
        }

        if ($tripServer->checkTime($paramIn['start_time'], $paramIn['end_time'], $userinfo['id'])) {
            throw new ValidationException($this->getTranslation()->_('business_trip_check_time'));
        }
        //申请操作
        $returnArr = $tripServer->addTripUseLock($paramIn, $userinfo);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 创建外出申请
     * 外出申请也是一种出差类型
     * @throws ValidationException
     */
    public function createGoOutTripAction()
    {
        $paramIn                       = $this->paramIn;
        $userinfo                      = $this->userinfo;
        $validations                   = [
            "destination_city"   => "Required|StrLenGeLe:1,100|>>>:".$this->getTranslation()->_('go_out_location_valid'),
            //外出地点
            "start_time"         => "Required|Date|>>>:".$this->getTranslation()->_('7124'),
            "end_time"           => "Required|Date|>>>:".$this->getTranslation()->_('7124'),
            "reason_application" => "Required|StrLenGeLe:10,500|>>>:".$this->getTranslation()->_('go_out_reason_application_valid'),
            //外出事由
        ];
        $paramIn['business_trip_type'] = BusinessTripModel::BTY_GO_OUT;

        $this->validateCheck($paramIn, $validations);
        if ($paramIn['start_time'] > $paramIn['end_time']) {
            throw new ValidationException($this->getTranslation()->_('7125'));
        }
        $tripServer = new BusinesstripServer($this->lang, $this->timezone);
        if ($tripServer->checkTime($paramIn['start_time'], $paramIn['end_time'], $userinfo['id'])) {
            throw new ValidationException($this->getTranslation()->_('business_trip_check_time'));
        }
        //申请操作
        $returnArr = $tripServer->addTripUseLock($paramIn, $userinfo);
        $this->jsonReturn($returnArr);
    }
}