<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers;


use Exception;
use FlashExpress\bi\App\Controllers\StaffController as BaseStaffController;
use FlashExpress\bi\App\Modules\Ph\Server\StaffServer;

class StaffController extends BaseStaffController
{
    /**
     * 添加辅导员
     */
    public function addStaffInstructorAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['user'] = $this->userinfo;
        $validations = [
            "staff_info_id" => "Required|int",
        ];
        $this->validateCheck($paramIn, $validations);
        $staffServer         = new StaffServer($this->lang, $this->timezone);
        $result = $staffServer->addStaffInstructor($paramIn);
        $this->jsonReturn($result);
    }
}