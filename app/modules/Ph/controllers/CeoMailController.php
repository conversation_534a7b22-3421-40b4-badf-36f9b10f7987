<?php
/**
 * Author: Bruce
 * Date  : 2022-04-23 21:51
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Ph\Controllers;


use FlashExpress\bi\App\Modules\Ph\Server\CeoMailServer;

class CeoMailController extends BaseController
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }

        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();

    }

    // 问题分类列表
    public function problemCategoryListAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        // 获取问题分类列表
        $data = (new CeoMailServer($this->lang, $this->timezone))->getCategoryList($paramIn);

        $result = [
            'code' => 1,
            'msg' => 'success',
            'data' => $data
        ];
        return $this->jsonReturn(self::checkReturn($result));
    }
}