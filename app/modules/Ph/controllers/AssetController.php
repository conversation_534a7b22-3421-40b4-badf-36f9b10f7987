<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Modules\Ph\Server\AssetServer;
use FlashExpress\bi\App\library\enums;
use Exception;

class AssetController extends BaseController
{

    protected $paramIn;
    protected $assetServer;
    protected $staffServer;
    protected $AuditServer;


    public function initialize()
    {
        parent::initialize();
        $this->assetServer = Tools::reBuildCountryInstance(new \FlashExpress\bi\App\Server\AssetServer($this->lang, $this->timezone), [$this->lang, $this->timezone]) ;
        $this->staffServer = Tools::reBuildCountryInstance(new \FlashExpress\bi\App\Server\StaffServer($this->lang, $this->timezone), [$this->lang, $this->timezone]) ;
        $this->AuditServer = Tools::reBuildCountryInstance(new \FlashExpress\bi\App\Server\AuditServer($this->lang, $this->timezone), [$this->lang, $this->timezone]) ;
        $this->paramIn     = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    public function demoAction()
    {
        var_dump(
            $this->assetServer->getDetail()
        );
    }

    /**
     * 申请资产-可申请资产列表
     * 网点负责人才可以使用，不同负责人类型看到的资产列表不一样
     * zcc
     */
    public function getListAction()
    {

        try {
            $sourceType =  isset($this->paramIn['source_type']) ? $this->paramIn['source_type'] : '';
            //todo 验证用户是否有该模块权限
            if (
            !$this->AuditServer->getASPermission(['staff_id' => $this->userinfo['id']])
            ) {
                return $this->jsonReturn(self::checkReturn(['data' => new stdClass()]));
            }

            //todo 获取资产可申请列表

            $list = $this->assetServer->getList($this->userinfo, $sourceType);

//            $spreeList = [];
//            if (!$sourceType || $sourceType != AssetServer::SOURCE_TYPE_PUBLIC_ASSETS) {
//                $spreeList = $this->assetServer->getSpressListV2($this->userinfo['id'], $sourceType);
//            }
//
//            if ($spreeList) {
//                $list = array_merge($spreeList, $list);
//            }
            if ($list) {
                $returnData['data']['dataList'] = $list;
            }

            //资产附件状态
            $assetsHandoerData                    = $this->assetServer->getAssetsHandoer([
                "staff_id" => $this->userinfo["staff_id"]
            ]);
            $annexList = $this->assetServer->getAnnexList([
                "staff_id" => $this->userinfo['staff_id'],
            ]);
            $_annexList = [];
            foreach ($annexList as $k=>$v){
                $img_prefix = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
                $_annexList[$k]["url"] = $img_prefix . $v['object_key'];
                $_annexList[$k]["file_type"] = $v['file_type'];
            }
            $returnData['data']['annex']["data"] = $_annexList;
            $returnData['data']["annex"]["state"] = $assetsHandoerData["state"] ?? 1;
            if ($assetsHandoerData["state"] == enums::$assets_handover_state["not_uploaded"]) {
                $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00001');
            } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["audited"]) {
                $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00002');
            } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["not_pass"]) {
                $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00003');
            } elseif ($assetsHandoerData["state"] == enums::$assets_handover_state["adopt"]) {
                $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00004');
            } else {
                $returnData['data']["annex"]["state_name"] = $this->getTranslation()->_('asset_00001');
            }
            if(empty($annexList)&&$assetsHandoerData["state"]!=4){
                $res=$this->assetServer->getWinhrProve($this->userinfo);
                if($res){
                    $returnData['data']["annex"] =array(
                        'data'=>array(
                            array(
                                "url"=>$res['id_card_pic'],
                                'file_type'=>"2"
                            ),
                            array(
                                "url"=>$res['sign_name_pic'],
                                'file_type'=>"15"
                            )
                        ),
                        'state'=>"4",
                        'state_name'=>$this->getTranslation()->_('asset_00004')
                    );
                }
            }
            //todo 输出json数据
            $this->jsonReturn($this->checkReturn($returnData));
        }catch (Exception $e){
            $this->getDI()->get('logger')->write_log("getListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }



    /**
     * 申请资产-负责人提交申请接口
     * zcc
     */
    public function submitApplyAction()
    {
        try{
            //todo 验证用户是否有该模块权限
            if (
            !$this->AuditServer->getASPermission(['staff_id' => $this->userinfo['id']])
            ) {
                return $this->jsonReturn(self::checkReturn(['data' => new stdClass()]));
            }

            //todo 参数验证
            if (empty($this->paramIn['assets']) || empty($this->paramIn['reason'])) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('miss_args')));
            }

            //校验状态
            $assetsHandoerData = $this->assetServer->getAssetsHanderByStaffId($this->userinfo["staff_id"]
            );
            $res=$this->assetServer->getWinhrProve($this->userinfo);
            if (!$assetsHandoerData&&!$res){
                // 没有上传过
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('asset_00005')));
            }

            //申请订单
            $this->jsonReturn($this->assetServer->addAssetsOrder($this->paramIn,$this->userinfo));
        }catch (Exception $e){
            $this->getDI()->get('logger')->write_log("submitApplyAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }





}
