<?php


namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use Exception;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Modules\Ph\Server\StaffSupportStoreServer;

class StaffsupportstoreController extends BaseController
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 员工申请支援
     * @return null
     * @throws BusinessException
     */
    public function addStaffSupportApplyAction() {
        $paramIn  = $this->paramIn;
        $paramIn['param'] = $this->userinfo;
        $paramIn['staff_id']    = $this->userinfo['staff_id'];
        $paramIn['name']  = $this->userinfo['name'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['param']['position_category']  = $this->userinfo['positions'];
        $paramIn['param']['store_id']  = $this->userinfo['organization_id'];
        $paramIn['userinfo']  = $this->userinfo;

        $validations = [
            "id"      => "Required|Str", //网点申请支援编号
        ];
        $this->validateCheck($this->paramIn, $validations);
        /**
         * @see StaffSupportStoreServer::addStaffApplySupport
         */
        $returnArr = (new StaffSupportStoreServer($this->lang, $this->timezone))->addStaffApplySupportUseLock($paramIn);
        return $this->jsonReturn($returnArr);
    }

    //员工申请支援 审批更新
    public function updateStaffSupportApplyAction() {
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['name']  = $this->userinfo['name'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['param']['position_category']  = $this->userinfo['positions'];
        $paramIn['param']['store_id']  = $this->userinfo['organization_id'];
        $paramIn['userinfo']  = $this->userinfo;

        $logger = $this->getDI()->get('logger');
        $cache = $this->getDI()->get('redis');
        $redis_key  = 'lock_update_staff_apply_support_' . $paramIn['audit_id'];
        try {
            $validations = [
                "audit_id"      => "Required|Int",
                "status"        => "Required|Int",
                "reject_reason" => "Required|StrLenGeLe:0,500",
            ];
            $this->validateCheck($this->paramIn, $validations);

            $returnArr = $this->atomicLock(function () use ($paramIn) {
                return  (new StaffSupportStoreServer($this->lang, $this->timezone))->updateStaffApplySupport($paramIn);
            }, $redis_key, 20);

            if ($returnArr === false) { //没有获取到锁
                return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
            }

            return $this->jsonReturn($returnArr);
        } catch (Exception $e) {
            $cache->delete($redis_key); //释放锁
            if ($e->getCode() == '1000') {
                $logger->write_log("Staff_Support_StoreController:updateStaffApplySupport:auditId:". $paramIn['audit_id'] . $e->getMessage(), 'info');
            } else {
                $logger->write_log("Staff_Support_StoreController:updateStaffApplySupport:auditId:". $paramIn['audit_id'] . $e->getMessage(), 'notice');
            }
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    //获取需要支援的网点列表接口
    public function getStoreSupportListAction() {
        $paramIn                = $this->paramIn;
        $paramIn['staff_id']    = $this->userinfo['staff_id'];
        $paramIn['name']        = $this->userinfo['name'];
        $paramIn['positions']   = $this->userinfo['positions'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['param']['position_category']  = $this->userinfo['positions'];
        $paramIn['param']['store_id']  = $this->userinfo['organization_id'];
        $paramIn['userinfo']    = $this->userinfo;

        try {
            $validations = [
                "store_name"        => "StrLenGeLe:0,50",
                "employment_date"   => "Date",
            ];
            $this->validateCheck($this->paramIn, $validations);

            $returnArr = (new StaffSupportStoreServer($this->lang, $this->timezone))->getStoreSupportList($paramIn);
            return $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $returnArr]]));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("Store_SupportController:getStoreSupportList:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }
}