<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\PayrollServer;

class PayrollController extends Controllers\PayrollController
{
    /**
     * 工资条专属校验-下载工资明细
     */
    public function loginAction()
    {
        try{
            $message = 'Wrong account or password';
            $data = $this->paramIn;
            if (!isset($data['user']) || !isset($data['pwd'])) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('login_all')));
            }
            $_data['account'] = $data['user'];
            $_data['pwd'] = $data['pwd'];
            $return_data = (new PayrollServer())->verify_fle($_data);
            if (empty($return_data)) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('login_all')));
            }
            if (!isset($data['date'])) {
                $data['date'] =  date('Y-m',strtotime(date('Y-m') . ' -1 month'));
            }
            
            $hcm_rpc = new ApiClient('hcm_rpc', '', 'get_payroll_pdf','en');
            $hcm_rpc->setParams(
                [
                    "staff_id" => $data['user'],
                    "month" => $data["date"],
                ]
            );
            $return = $hcm_rpc->execute();
            if(isset($return['result']['code']) && $return['result']['code'] == 1) {
                $this->jsonReturn(self::checkReturn(["data" => ["url" => $return['result']['data'] ?? '']]));
            } else {
                $this->jsonReturn($this->checkReturn(-3, $return['result']['msg'] ?? 'Not found'));
                $this->getDI()->get('logger')->write_log("get_pdfAction:get_payroll_pdf:" . json_encode($return ,true));
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("getRoleList:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }
}