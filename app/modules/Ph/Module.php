<?php

namespace FlashExpress\bi\App\Modules\Ph;

use Phalcon\DiInterface;
use Phalcon\Loader;
use Phalcon\Mvc\ModuleDefinitionInterface;
use Phalcon\Config;


class Module implements ModuleDefinitionInterface
{
    /**
     * Registers an autoloader related to the module
     *
     * @param DiInterface $di
     */
    public function registerAutoloaders(DiInterface $di = null)
    {
        $loader = new Loader();

        $loader->registerNamespaces([
            'FlashExpress\bi\App\Modules\Ph\Controllers' =>  __DIR__ . '/controllers/',
            'FlashExpress\bi\App\Modules\Ph\Models'      =>  __DIR__ . '/models/',
            'FlashExpress\bi\App\Modules\Ph\Server'      =>  __DIR__ . '/server/',
            'FlashExpress\bi\App\Modules\Ph\library'     =>  __DIR__ . '/library/',
            'FlashExpress\bi\App\Modules\Ph\Tasks'       =>  __DIR__ . '/tasks/',
            'FlashExpress\bi\App\Modules\Ph\enums'       =>  __DIR__ . '/enums/',
                                    ]);
        $loader->register();
    }

    /**
     * Registers services related to the module
     *
     * @param DiInterface $di
     */
    public function registerServices(DiInterface $di)
    {
        $config = $di['config'];
        if (file_exists(__DIR__ . '/config/config.php')) {

            $config = $di['config'];

            $override = new Config(include __DIR__ . '/config/config.php');

            if ($config instanceof Config) {
                $config->merge($override);
            } else {
                $config = $override;
            }
        }
    }
}
