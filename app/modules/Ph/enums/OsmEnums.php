<?php

namespace FlashExpress\bi\App\Modules\Ph\enums;

use FlashExpress\bi\App\Enums\BaseEnums;

class OsmEnums extends BaseEnums
{
    public static function getCodeTxtMap($lang = '', string $code = '')
    {
        $txt = self::$code ?? '';
        return $txt;
    }

    //国籍
    const NATIONALITY_1 = 1;    //泰国
    const NATIONALITY_6 = 6;    //老挝
    const NATIONALITY_9 = 9;    //缅甸
    const NATIONALITY_10 = 10;  //柬埔寨
    const NATIONALITY_4 = 4;    //菲律宾
    const NATIONALITY_3 = 3;    //马来西亚
    const NATIONALITY_7 = 7;    //印尼
    public static $nationality_list = [
        self::NATIONALITY_4  => 'nationality_4',
        self::NATIONALITY_1  => 'nationality_1',
        self::NATIONALITY_6  => 'nationality_6',
        self::NATIONALITY_9  => 'nationality_9',
        self::NATIONALITY_10 => 'nationality_10',
        self::NATIONALITY_3  => 'nationality_3',
        self::NATIONALITY_7  => 'nationality_7',
    ];

    //支持的网点类型
    public static $store_category = [
        8  => 'HUB',
        12 => 'B-HUB',
    ];

    //支持的可选职位
    public static $job_title = [
        1461 => 'Outsource',
        1462 => 'Security Outsource',
    ];

    //性别
    const SEX_1 = 1;            //男
    const SEX_2 = 2;            //女
    public static $sex = [
        self::SEX_1 => '4900',
        self::SEX_2 => '4901',
    ];
}