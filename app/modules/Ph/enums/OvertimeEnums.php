<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/1/23
 * Time: 8:41 PM
 */

namespace FlashExpress\bi\App\Modules\Ph\enums;

use FlashExpress\bi\App\Enums\BaseEnums;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;

class OvertimeEnums extends BaseEnums
{
    public static function getCodeTxtMap($lang = '')
    {
        $txt = $code ?? '';
        return $txt;
    }

    //数据来源bi-hr_job_title表
    public static $job_title = [
        'dc_officer' => 37,
        'dc_supervisor' => 16,
        'bike_courier' => 13,
        'van_courier'   => 110,
        'tricycle_courier' => 1000,
        'truck_courier'=>1194,
    ];

    //ot 时长描述 翻译
    public static $normalDurationText = [
        HrOvertimeModel::OVERTIME_4 => 'ot_4_new_text',
        HrOvertimeModel::OVERTIME_8 => 'ot_8_new_text',
    ];


}