<?php
namespace FlashExpress\bi\App\Modules\Ph\Tasks;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferSignApproveModel;

class OfferSignTask extends \BaseTask
{


    /**
     *
     * 修复数据
     *
     */
    public function fix_pdf_pathAction()
    {

        $signApproveList =
        HrInterviewOfferSignApproveModel::find([
            'conditions' => 'approve_state = 2' // 取已经审批通过的数据
        ])->toArray();

        $db = $this->getDI()->get("db");

        foreach ($signApproveList as $item) {
            $rpcClient = (new ApiClient('winhr_rpc','','addOfferSignDate', $this->lang));
            $rpcClient->setParams(['resume_id' => $item['resume_id']]);
            $return = $rpcClient->execute();
            $this->getDI()->get('logger')->write_log("winhr_rpc addOfferSignDate:" . json_encode($return,JSON_UNESCAPED_UNICODE), 'info');
            echo "winhr_rpc addOfferSignDate: " . $item['resume_id'] . " " . json_encode($return,JSON_UNESCAPED_UNICODE) . "\r\n";
            if (isset($return['result']['code']) && $return['result']['code'] == 1) {
                //更新hr_interview_offer_sign_approve pdf地址
                $new_pdf_path = $return['result']['data']['new_pdf_path'];

                if ($new_pdf_path) {

                    $update_approve_data['pdf_path'] = $new_pdf_path;
                    $db->updateAsDict("hr_interview_offer_sign_approve", $update_approve_data,"id=". $item['id']);
                }

            }
        }
    }







}
