<?php


namespace FlashExpress\bi\App\Modules\Ph\Tasks;

use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\Models\backyard\HrOutSourcingBlacklistModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkDetectFaceRecordModel;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Server\SettingEnvServer;
use AttendanceTask as BaseAttendanceTask;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\library\Mail;


class AttendanceTask extends BaseAttendanceTask
{


    /**
     * @description 发送邮件 - 仅公司外协(非众包外协) 每日09:00发送昨日稽查数据
     * 内容
     *  标题：{日期} 疑似作弊外协账号名单
     *  正文：{日期} 在外协员工打卡环节，稽查到以下外协账号使用者面部特征，与公司既有正式员工相匹配，请核实是否有虚假外协账号作弊情况，名单见附件。
     *
     * 取值说明：
     *  {日期}：取前一日日期，格式：2023年1月13日
     *
     * 通知对象：泰国HR邮件组、泰国Network QC邮件组、产品邮件组
     * 发送时间：09:00AM
     *
     * 表头：
     *  1. 外协工号 ｜ 2. 外协身份证号 ｜ 3. 打卡扫描照片 ｜ 4. 正式员工底片 ｜ 5. 正式员工工号 ｜ 6. 打卡时间
     */
    public function sendEmailForPunchCardAction($input = [])
    {
        $date = empty($input[0]) ? date('Y-m-d') : $input[0];

        $this->info(' sendEmailForPunchCardAction start! date=>'.$date);

        $lastDate = date('Y-m-d', strtotime("$date -1 day"));

        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.staff_info_id,s.os_submit_face_image_path,s.work_attendance_path,s.match_staff_info_id,s.created_at,hsi.identity,hsi.name,hsi.mobile,hsi.mobile_company,hsi.company_name_ef,hsi.sys_store_id");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = s.staff_info_id', 'hsi');
        $builder->where('s.created_at > :start_time: and hsi.hire_type != :hire_type: and type = :type:', [
            'start_time' => date("Y-m-d 00:00:00", strtotime($lastDate)),
            'hire_type'  => HrStaffInfoModel::HIRE_TYPE_12,
            'type'       => StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS,
        ]);
        $builder->andWhere('s.created_at <= :end_time:', ['end_time' => date("Y-m-d 00:00:00", strtotime($date))]);
        $builder->andWhere('s.state = :state:', ['state' => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_MATCH]);
        $list = $builder->getQuery()->execute()->toArray();

        if (empty($list)) {
            $this->info("无数据".PHP_EOL);
            return false;
        }
        $t = $this->getTranslation(getCountryDefaultLang());

        $storeIdsArr = array_column($list,'sys_store_id');
        $storeIds                   = getIdsStr($storeIdsArr);
        $storeInfoList = (new SysListRepository())->getStoreList(['ids' => $storeIds]);
        $storeInfoListToId = array_column($storeInfoList, 'name', 'id');

        $data = [];
        foreach ($list as $item) {
            $mobile          = !empty($item['mobile']) ? $item['mobile'] : '';
            $mobile_company  = !empty($item['mobile_company']) ? $item['mobile_company'] : '';
            $company_name_ef = !empty($item['company_name_ef']) ? $item['company_name_ef'] : '';
            $name            = !empty($item['name']) ? $item['name'] : '';
            $storeName       = $storeInfoListToId[$item['sys_store_id']] ?? '';
            $mobileData = [];
            if(!empty($mobile_company)) {
                $mobileData[] = $mobile_company;
            }
            if(!empty($mobile)) {
                $mobileData[] = $mobile;
            }
            $mobileString = !empty($mobileData) ? implode(',', $mobileData) : '';

            $data[] = [
                $storeName,
                $item['staff_info_id'],
                $name,
                $mobileString,
                is_null($item['identity']) ? '' : $item['identity'],
                $item['os_submit_face_image_path'],
                $item['work_attendance_path'],
                $item['match_staff_info_id'],
                date("Y-m-d H:i:s", strtotime($item['created_at'])),
                $company_name_ef
            ];
        }

        $header    = [
            $t->_('att_os_staff_store_name'),//外协员工所属网点名称
            $t->_('att_os_staff_no'),       //外协工号
            $t->_('att_os_staff_name'),     //外协姓名
            $t->_('att_os_staff_phone'),    //外协电话
            $t->_('att_os_staff_identity'), //外协身份证号
            $t->_('att_punch_in_image'),    //打卡扫描照片
            $t->_('att_staff_image_url'),   //正式员工底片
            $t->_('att_staff_no'),          //正式员工工号
            $t->_('attendance_time'),       //打卡时间
            $t->_('att_os_company_name'),   //供应商名称
        ];
        $file_name = (new OssHelper)->exportExcel($header, $data, 'detected_data');

        //标题：{日期} 疑似作弊外协账号名单
        //正文：{日期} 在外协员工打卡环节，稽查到以下外协账号使用者面部特征，与公司既有正式员工相匹配，请核实是否有虚假外协账号作弊情况，名单见附件。
        $title   = $t->_('att_os_email_title', ['detected_date' => $lastDate]);
        $content = $t->_('att_os_email_content', ['detected_date' => $lastDate]);
        $content .= sprintf("<a href='%s'>  %s  </a>", $file_name['data'], $t->_('click_here'));

        //获取发送邮件列表
        $emailList = explode(',', (new SettingEnvServer())->getSetVal('outsourcing_attendance_remind_email'));

        if (empty($emailList)) {
            $this->logger->write_log('sendEmailForPunchCardAction, 收件人不能为空:'.json_encode($emailList,
                    JSON_UNESCAPED_UNICODE), 'notice');
            return false;
        }
        $sendEmail = Mail::send(
            $emailList,
            $title,
            $content);
        $this->info(' send email data=>'.json_encode([$emailList, $title, $content],
                JSON_UNESCAPED_UNICODE));
        if ($sendEmail) {
            $this->info(' send success!');
            $this->logger->write_log("sendEmailForRegularInspectAction success!", "notice");
        } else {
            $this->logger->write_log('sendEmailForPunchCardAction, 发送邮件失败:'.json_encode($emailList,
                    JSON_UNESCAPED_UNICODE), 'notice');
        }
        $this->info(' sendEmailForPunchCardAction end!');
    }


    /**
     * @description 发送邮件 每日23:30发送当日稽查数据并处理非众包拉黑
     * 内容
     *  标题：{日期} 疑似作弊外协账号名单
     *  正文：{日期}因稽查到以下外协账号，有非本人登陆操作记录，疑似有作弊行为，现已将以下账号移入外协黑名单。
     * 请通知对应供应商公司指派新的外协员工，并核实作弊情况是否真实，若有系统误判，请及时与泰国产品部门申诉。
     * 名单见附件。
     *
     * 取值说明：
     *  {日期}：取当日日期，格式：2023年1月13日
     *
     * 通知对象：泰国Network QC邮件组、产品邮件组
     * 发送时间：23:30AM
     *
     * 表头：
     *  1. 外协工号 ｜ 2. 外协身份证号｜ 3. 账号留存底片 ｜ 4. 打卡扫描照片 ｜ 5. 违规时间
     */
    public function sendEmailForRegularInspectAction($input = [])
    {
        $date = empty($input[0]) ? date('Y-m-d') : $input[0];

        $this->info(' sendEmailForRegularInspectAction start date=>'.$date);

        //公司外协--  (非众包外协)
        $list = $this->getFaceRecordList($date, HrStaffInfoModel::HIRE_TYPE_11);
        if (!empty($list)) {
            //发送邮件
            //获取发送邮件列表
            $emailList = explode(',', (new SettingEnvServer())->getSetVal('outsourcing_attendance_remind_email'));
            if (empty($emailList)) {
                $this->logger->write_log('sendEmailForRegularInspectAction, 非众包外协 收件人不能为空:'.json_encode($emailList,
                        JSON_UNESCAPED_UNICODE), 'notice');
            }
            $doSendEmailResult = $this->doSendEmail($list, $emailList, date('Y-m-d'));
            $this->info( "sendEmailForRegularInspectAction hire_type=>11 doSendEmail =>".json_encode($doSendEmailResult,JSON_UNESCAPED_UNICODE));
            //拉黑操作并且发送消息
            $doSendMsgResult = $this->doSendMsg($list);
            $this->info(" sendEmailForRegularInspectAction  hire_type=>11 doSendMsg =>".json_encode($doSendMsgResult,JSON_UNESCAPED_UNICODE));

        }

        $this->info(' sendEmailForRegularInspectAction end');
    }


    /**
     * @deprecated 废弃
     * @description 众包数据每天凌晨 4 点处理- 昨天数据-发消息-拉黑
     * 内容
     *  标题：{日期} 疑似作弊外协账号名单
     *  正文：{日期}因稽查到以下外协账号，有非本人登陆操作记录，疑似有作弊行为，现已将以下账号移入外协黑名单。
     * 请通知对应供应商公司指派新的外协员工，并核实作弊情况是否真实，若有系统误判，请及时与泰国产品部门申诉。
     * 名单见附件。
     *
     * 取值说明：
     *  {日期}：取当日日期，格式：2023年1月13日
     */

    public function sendCrowdsourcingToMsgAction($input = [])
    {
        $date = empty($input[0]) ? date('Y-m-d') : $input[0];

        $this->info(' sendCrowdsourcingToMsg start date=>'.$date);

        $list = $this->getFaceRecordList(date('Y-m-d', strtotime("$date -1 day")), HrStaffInfoModel::HIRE_TYPE_12);
        if (!empty($list)) {
            //发送消息
            $doSendMsgResult = $this->doSendMsg($list);
            $this->info(' sendCrowdsourcingToMsgAction  hire_type=>12 doSendMsg =>'.json_encode($doSendMsgResult,
                    JSON_UNESCAPED_UNICODE));
        }

        $this->info(' sendCrowdsourcingToMsg end');
    }

    //获取数据
    public function getFaceRecordList($date = '', $hire_type = 11)
    {
        $start_time = empty($date) ? date('Y-m-d 00:00:00') : date('Y-m-d 00:00:00', strtotime($date));
        $end_time   = date('Y-m-d 00:00:00', strtotime("{$start_time} +1 day"));

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.id,s.staff_info_id,s.organization_id,s.attendance_date,s.match_staff_info_id,
            s.os_face_image_source_path,s.os_submit_face_image_path,s.work_attendance_path,s.state,
            s.created_at,s.updated_at
        ');
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->join(HrStaffInfoModel::class, 'hsi.staff_info_id = s.staff_info_id', 'hsi');
        $builder->where('s.created_at > :start_time:', ['start_time' => $start_time]);
        $builder->andWhere('s.created_at <= :end_time:', ['end_time' => $end_time]);
        $builder->andWhere('s.state = :state:', ['state' => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_DETECTED]);
        $builder->andWhere('hsi.hire_type = :hire_type:', ['hire_type' => $hire_type]);
        $builder->andWhere('s.type = :type:', ['type' => StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @description 发送邮件
     * @param $list
     * @param $emailList
     * @return bool
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    private function doSendEmail($list = [], $emailList = [],$lastDate='')
    {
        $staffIds = array_column($list, 'staff_info_id');
        if (empty($staffIds)) {
            $this->logger->write_log('doSendEmail, list不能为空:'.json_encode($list), 'notice');
        }

        if (empty($emailList)) {
            $this->logger->write_log('doSendEmail, 收件人不能为空:'.json_encode($emailList), 'notice');
        }

        $lastDate = $lastDate ? : date('Y-m-d', strtotime('-1 day'));

        $staffInfo    = HrStaffInfoModel::find([
            'staff_info_id in({staff_ids:array})',
            'bind'    => [
                'staff_ids' => $staffIds,
            ],
            'columns' => 'staff_info_id,name,identity,mobile,mobile_company,company_name_ef,sys_store_id',
        ])->toArray();

        $storeIdsArr = array_column($staffInfo,'sys_store_id');
        $storeIds                   = getIdsStr($storeIdsArr);
        $storeInfoList = (new SysListRepository())->getStoreList(['ids' => $storeIds]);
        $storeInfoListToId = array_column($storeInfoList, 'name', 'id');

        $staffInfoArr = array_column($staffInfo, null, 'staff_info_id');

        $data = [];
        foreach ($list as $item) {
            $identity = $staffInfoArr[$item['staff_info_id']]['identity'] ?? '';
            $mobile          = $staffInfoArr[$item['staff_info_id']]['mobile'] ?? '';
            $mobile_company  = $staffInfoArr[$item['staff_info_id']]['mobile_company'] ?? '';
            $company_name_ef = $staffInfoArr[$item['staff_info_id']]['company_name_ef'] ?? '';
            $name            = $staffInfoArr[$item['staff_info_id']]['name'] ?? '';
            $storeId         = $staffInfoArr[$item['staff_info_id']]['sys_store_id'] ?? '';
            $storeName       = $storeInfoListToId[$storeId] ?? '';
            $mobileData = [];
            if(!empty($mobile_company)) {
                $mobileData[] = $mobile_company;
            }
            if(!empty($mobile)) {
                $mobileData[] = $mobile;
            }
            $mobileString = !empty($mobileData) ? implode(',', $mobileData) : '';
            $data[]   = [
                $storeName,
                $item['staff_info_id'],
                $name,                                                             //{外协姓名}：取疑似作弊的外协员工姓名
                $mobileString,                                                     //{外协电话}：取疑似作弊的外协员工个人电话及企业电话，如有多个，中间逗号隔开
                $identity,
                $item['os_face_image_source_path'],                                 //{账号留存底片}：取该疑似作弊外协员工在系统内留存的底片
                $item['os_submit_face_image_path'],                                 //{打卡扫描照片}：取当日KIT人脸校验时留存的扫描面部照片
                date('Y-m-d H:i:s', strtotime($item['created_at'])),         //{违规时间}：取人脸校验未识别通过的时间，精确到时-分-秒
                $company_name_ef,                                                   //{供应商名称}：取疑似作弊的外协员工工号的外协公司名称，没有为空
            ];
        }

        $t = $this->getTranslation(getCountryDefaultLang());


        $header    = [
            $t->_('att_os_staff_store_name'),                   //外协员工所属网点名称
            $t->_('att_os_staff_no'),                           //外协工号
            $t->_('att_os_staff_name'),                         //外协姓名
            $t->_('att_os_staff_phone'),                        //外协电话
            $t->_('att_os_staff_identity'),                     //外协身份证号
            $t->_('att_staff_account_image_url'),               //账号留存底片
            $t->_('att_staff_punch_card_image_url'),            //打卡扫描照片
            $t->_('att_created_at'),                            //违规时间
            $t->_('att_os_company_name'),                       //供应商名称
        ];
        $file_name = (new OssHelper)->exportExcel($header, $data, 'detected_data');

        //标题：{日期} 疑似作弊外协账号名单
        //正文：{日期}因稽查到以下外协账号，有非本人登陆操作记录，疑似有作弊行为，现已将以下账号移入外协黑名单。
        // 请通知对应供应商公司指派新的外协员工，并核实作弊情况是否真实，若有系统误判，请及时与泰国产品部门申诉。
        // 点击链接下载文件
        $title   = $t->_('att_os_email_title', ['detected_date' => $lastDate]);
        $content = $t->_('att_email_detect_content_1', ['detected_date' => $lastDate]).'<br/>';
        $content .= $t->_('att_email_detect_content_2').'<br/>';
        $content .= sprintf("<a href='%s'>  %s  </a>", $file_name['data'], $t->_('click_here'));

        $sendEmail = Mail::send(
            $emailList,
            $title,
            $content);
        $this->info(' send email data=>'.json_encode([$emailList, $title, $content],
                JSON_UNESCAPED_UNICODE));
        if ($sendEmail) {
            $this->info(' send success!');
            return true;
        } else {
            $this->logger->write_log('sendEmailForRegularInspectAction, 发送邮件失败:'.$emailList, 'notice');
            return false;
        }
    }


    /**
     * @description:拉黑并且发送消息
     * @author: L.J
     * @time: 2023/2/17 16:42
     */
    public function doSendMsg($list = [])
    {
        $staffIds = array_column($list, 'staff_info_id');
        if (empty($staffIds)) {
            $this->logger->write_log('doSendMsg, list不能为空:'.json_encode($list), 'notice');
            return false;
        }

        $staffInfo    = HrStaffInfoModel::find([
            'staff_info_id in({staff_ids:array})',
            'bind'    => [
                'staff_ids' => $staffIds,
            ],
            'columns' => 'staff_info_id,name,identity,mobile',
        ])->toArray();
        $staffInfoArr = array_column($staffInfo, null, 'staff_info_id');
        //获取网点主管
        $storeIds            = array_values(array_unique(array_column($list, 'organization_id')));
        $storeSupervisorList = [];
        if (!empty($storeIds)) {
            $storeSupervisorArr = HrStaffInfoModel::find([
                'sys_store_id in({store_ids:array}) and job_title = :job_title: and state = 1 and formal = 1 and is_sub_staff = 0',
                'bind'    => [
                    'store_ids' => $storeIds,
                    'job_title' => enums::$job_title['branch_supervisor'],
                ],
                'columns' => 'staff_info_id,sys_store_id',
            ])->toArray();
            foreach ($storeSupervisorArr as $v) {
                $storeSupervisorList[$v['sys_store_id']][] = $v['staff_info_id'];
            }
        }


        $publicRepo  = new PublicRepository();
        $StaffServer = new StaffServer();
        foreach ($list as $item) {
            $identity = $staffInfoArr[$item['staff_info_id']]['identity'] ?? '';
            $mobile   = $staffInfoArr[$item['staff_info_id']]['mobile'] ?? '';

            //加入外协黑名单
            $info = HrOutSourcingBlacklistModel::findFirst([
                '(identity = :id: or mobile = :mobile:) and status = 1 and reason_code = :reason_code:',
                'bind' => [
                    'id'          => $identity,
                    'mobile'      => $mobile,
                    'reason_code' => HrOutSourcingBlacklistModel::REASON_CODE_FACE_DIFF,
                ],
            ]);
            if (empty($info)) {
               $this->addOsBlacklist($item['staff_info_id']);
            }

            $storeSupervisor = $storeSupervisorList[$item['organization_id']] ?? [];

            if (empty($storeSupervisor)) {
                continue;
            }

            $storeSupervisorArr = array_values($storeSupervisor);
            $staffLang          = $StaffServer->getBatchStaffLanguage($storeSupervisorArr);

            foreach ($storeSupervisorArr as $staffId) {
                //给网点主管发送网点员工账号停用消息
                $lang = $staffLang[$staffId] ?? getCountryDefaultLang();
                $t    = $this->getTranslation($lang);

                $title   = $t->_('store_staff_account_deactivation_notice', [
                    'staff_info_id' => $item['staff_info_id'],
                ]);
                $content = $t->_('store_staff_account_deactivation_content', [
                    'staff_info_id' => $item['staff_info_id'],
                ]);

                $content .= $item['staff_info_id'].'<br/>';
                $content .= ($staffInfoArr[$item['staff_info_id']]['name'] ?? '').'<br/>';
                $content .= date('Y-m-d H:i:s', strtotime($item['created_at'])).'<br/>';

                $param = [
                    'staff_info_id'   => $staffId,
                    'message_title'   => $title,
                    'message_content' => $content,
                    'type'            => MessageEnums::CATEGORY_GENERAL,
                ];
                $publicRepo->sendMessageToSubmitter($param);
            }
        }

        $this->info(' send msg success!');
        return true;
    }


}