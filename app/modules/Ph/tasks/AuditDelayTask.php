<?php

namespace FlashExpress\bi\App\Modules\Ph\Tasks;


use FlashExpress\bi\App\Modules\Ph\Server\OvertimeServer;

class AuditDelayTask extends \RocketMqBaseTask
{
    public function initialize()
    {
        $this->tq = "create-audit-delay";//业务上先择的消费rmq的内容
        parent::initialize(); // TODO: Change the autogenerated stub
    }

    /**
     * 消费者
     * @param $msgBody
     * @return bool
     */
    protected function processOneMsg($msgBody)
    {
        $messageBody = json_decode($msgBody, true);
        if (empty($messageBody)) {
            return false;
        }
        $this->logger->write_log(['AuditDelayTask' => $msgBody], 'info');
        $msgData    = json_decode($messageBody['data'], true);
        $auditType  = $msgData['audit_type'];
        $auditValue = $msgData['audit_value'];

        try {
            echo '有数据:'. json_encode($messageBody['data'], JSON_UNESCAPED_UNICODE) .PHP_EOL;
            $res = (new OvertimeServer($this->lang, $this->timezone))->createDelay($auditType, $auditValue);
            if ($res === false) {
                return false;
            }
        } catch (\Exception $e) {
            echo $e->getMessage(). $e->getTraceAsString() .PHP_EOL;
            $this->logger->write_log('AuditDelayTask main ' . sprintf("Err_msg: %s, Err_File: %s, Err_Line:%s ",
                    $e->getMessage(),
                    $e->getFile(),
                    $e->getLine()
                ), 'error');
            return false;
        }
        return true;
    }
}