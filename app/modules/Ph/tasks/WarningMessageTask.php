<?php

namespace FlashExpress\bi\App\Modules\Ph\Tasks;

use FlashExpress\bi\App\Models\backyard\MessageWarningModel;
use FlashExpress\bi\App\Modules\Ph\Server\StaffServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\MessageServer;
use WarningMessageTask as BaseWarningMessageTask;

class WarningMessageTask extends BaseWarningMessageTask
{


    /**
     * 每天跑超过一天没有签字的 警告书
     *
     */
    public function mainAction($params)
    {
        $settingEnvServer = new SettingEnvServer();
        //警告人不签字自动转交天数
        $transfer_days = $settingEnvServer->getSetVal('sign_warning_automatic_transfer_days');
        $transfer_days = $transfer_days > 0 ? intval($transfer_days) : 1;
        $begin         = zero_time_zone(date('Y-m-d H:i:s', strtotime(" - $transfer_days day")));
        $model = MessageWarningModel::find([
            'conditions' => " warning_type in ({warning_types:array}) AND kit_id != '' and img_url = '' and superior_id = 0 and is_delete = :is_delete: and created_at <= :end:",
            'bind'       => [
                'end'           => $begin,
                'is_delete'     => MessageWarningModel::DELETE_NO,
                'warning_types' => array_keys(MessageWarningModel::$warning_types),
            ],
            'order'=>'id desc'
        ]);
        if ($model) {
            $staffSever    = new StaffServer();
            $messageServer = new MessageServer($this->lang, $this->timezone);

            foreach ($model as $item) {
                $staffInfo = $staffSever->getStaffInfo(['staff_info_id' => $item->staff_info_id], 'manger');
                if ($staffInfo) {
                    $m = MessageWarningModel::findFirstById($item->id);
                    if ($m && $m->superior_id > 0) {
                        continue;
                    }
                    $re = $messageServer->sendWarningMessage($staffInfo['manger'], $item->id,
                        MessageServer::MSG_STAFF_TYPE_STAFF);
                    if ($re) {
                        $item->is_transfer = 1;
                        $item->save();
                    }
                    echo  $item->id .'=>'.intval($re). PHP_EOL;
                }
            }
        }
        echo 'success';
    }

}