<?php
/**
 * Created by PhpStor<PERSON>.
 * User: nick
 * Date: 2/14/22
 * Time: 4:53 PM
 */



namespace FlashExpress\bi\App\Modules\Ph\Tasks;


use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\WorkflowEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalDelayModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Modules\Ph\Server\OvertimeExtendServer;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\WorkflowServer;

class OvertimeTask extends \BaseTask {

    const DEFAULT_LIMIT_NUM = 650; //每10分钟执行数

    protected $baseServer;
    protected $overtimeRe;
    //明后两天 如果是 ph 提醒对应员工 不用自己申请加班 https://flashexpress.feishu.cn/docx/doxcnfR0AGGp7SPFD79cJVxsbse
    //每天北京时间 早上9点 跑
    public function remindNwStaffAction(){
        ini_set('memory_limit', '-1');
        $nwOperationId = enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH;
        //跑明天 后天 2天
        $checkDate = [date('Y-m-d', strtotime("+1 day")), date('Y-m-d', strtotime("+2 day"))];

        $existPh = ThailandHolidayModel::find([
            'conditions' => 'day in ({dates:array})',
            'bind'       => ['dates' => $checkDate],
        ])->toArray();

        if (empty($existPh)) {
            die('remindNwStaff 非ph 不用跑 '.json_encode($checkDate));
        }

        //获取指定员工 在职 子账号也发
        $builder = $this->modelsManager->createBuilder();
        $column  = "s.staff_info_id,s.name,j.job_name";
        $builder->columns($column);
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrJobTitleModel::class, 's.job_title = j.id ', 'j');
        $builder->andWhere("s.state = 1 ");
        $builder->andWhere("s.sys_store_id != '-1'");
        $builder->andWhere("s.sys_department_id = :dep:", ['dep' => $nwOperationId]);
        //增加 排除外协 条件
        $builder->andWhere("s.formal != 0");
        $builder->inWhere("s.hire_type", [1,2,3,4]);

        $staffData = $builder->getQuery()->execute()->toArray();

        if (empty($staffData)) {
            die('remindNwStaffAction 没员工数据');
        }

        $staffIds = array_column($staffData, 'staff_info_id');
        //获取对应语言环境 只有 中文和英文
        $hcRe         = new HcRepository($this->timezone);
        $staffAccount = $hcRe->getStaffAcceptLang($staffIds);
        $chinese      = [];//中文和英文两种
        if (!empty($staffAccount)) {
            foreach ($staffAccount as $account) {
                $l = strtolower(substr($account['accept_language'], 0, 2));
                if ($l == 'zh') {
                    $chinese[] = $account['staff_info_id'];
                }
            }
        }
        $this->baseServer = new BaseServer();

        //出现连续两天 ph 会有两个 一半只有一个
        foreach ($existPh as $ph) {
            $phDate = $ph['day'];
            //有没有配置轮休
            $workData = HrStaffWorkDayModel::find([
                'columns'    => 'staff_info_id,date_at',
                'conditions' => 'staff_info_id in ({ids:array}) and date_at = :date:',
                'bind'       => ['ids' => $staffIds, 'date' => $phDate],
            ])->toArray();
            if (!empty($workData)) {
                $workData = array_column($workData, 'date_at', 'staff_info_id');
            }

            foreach ($staffData as $staff) {
                //如果配置了休息 不上班 跳过不发消息
                if (!empty($workData[$staff['staff_info_id']])) {
                    $this->getDI()->get('logger')->write_log("remindNwStaff_{$staff['staff_info_id']} {$phDate} 配置了轮休",
                        'info');
                    continue;
                }

                //默认语言 英语
                $lang = 'en';
                if (in_array($staff['staff_info_id'], $chinese)) {
                    $lang = 'zh-CN';
                }

                [$title, $content] = $this->remindFormat($staff, $lang, $phDate);

                $staff_info_id               = $staff['staff_info_id'];
                $id                          = time().$staff_info_id.rand(1000000, 9999999);
                $param['staff_users']        = [$staff_info_id];//数组 多个员工id
                $param['message_title']      = $title;
                $param['message_content']    = $content;
                $param['staff_info_ids_str'] = $staff_info_id;
                $param['id']                 = $id;
                $param['category']           = MessageEnums::MESSAGE_CATEGORY_75;


                $this->getDI()->get('logger')->write_log('remindNwStaffAction 发送消息 '.json_encode($param), 'info');
                $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
                $bi_rpc->setParams($param);
                $bi_rpc->execute();
            }
        }

        echo 'remindNwStaffAction 跑完了';
    }

    //接上个任务 自动给员工跑加班数据 审批通过状态 不走审批流 每天凌晨一点 跑 -3 day 那一天的数据
    public function sysNwOvertimeAction($params)
    {
        ini_set('memory_limit', '-1');
        //要保证 出差打卡 的超时关闭时间之后跑这个加班任务
        $taskDate = date('Y-m-d', strtotime('-3 day'));

        if (!empty($params[0])) {
            $taskDate = $params[0];
        }

        //是否是 ph
        $phInfo = ThailandHolidayModel::findFirst([
            'conditions' => 'day = :date:',
            'bind'       => ['date' => $taskDate],
        ]);
        if (empty($phInfo)) {
            die('任务日期 不是ph 不用跑');
        }

        //根据OT日期判断是RH加班1倍日薪(type 4) / SH加班1.3倍日薪 (type 6)  holiday_type 1rh 2 sh
        $otType = $phInfo->holiday_type == 1 ? 4 : 6;

        $nwOperationId = enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH;
        //指定 网点员工 并且有打卡记录
        $builder = $this->modelsManager->createBuilder();
        $column  = "s.staff_info_id,s.name,j.job_name,a.started_at,a.end_at";
        $builder->columns($column);
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrJobTitleModel::class, 's.job_title = j.id ', 'j');
        $builder->Join(StaffWorkAttendanceModel::class, 's.staff_info_id = a.staff_info_id', 'a');
        $builder->andWhere('s.state in ({states:array}) or (s.leave_date >= :leave_date: and s.state = :leave_state:)',
            ['states' => [HrStaffInfoModel::STATE_ON_JOB,HrStaffInfoModel::STATE_SUSPENSION], 'leave_date' => date("Y-m-d 00:00:00",strtotime('-30 days')),'leave_state'=>HrStaffInfoModel::STATE_RESIGN]);
        $builder->andWhere("s.sys_store_id != '-1'");
        $builder->andWhere("s.sys_department_id = :dep:", ['dep' => $nwOperationId]);
        $builder->andWhere("a.attendance_date = :date:", ['date' => $taskDate]);
        $builder->andWhere("a.started_at is not null and a.end_at is not null");
        $builder->andWhere("s.formal != 0");
        $builder->inWhere("s.hire_type", [1,2,3,4]);
        $staffData = $builder->getQuery()->execute()->toArray();

        //已经申请了 4，6 类型ot  不需要跑加班数据了
        if (empty($staffData)) {
            die('没有员工 或者没有上班数据 不用跑了');
        }

        $staffIds  = array_column($staffData, 'staff_info_id');
        $existData = HrOvertimeModel::find([
            'columns'    => 'staff_id,state',
            'conditions' => 'staff_id in ({ids:array}) and type in (4,6,8,10) and date_at = :date: and state in (1,2)',
            'bind'       => ['ids' => $staffIds, 'date' => $taskDate],
        ])->toArray();

        if (!empty($existData)) {
            $existData = array_column($existData, 'state', 'staff_id');
        }

        //获取对应语言环境 只有 中文和英文
        $hcRe         = new HcRepository($this->timezone);
        $staffAccount = $hcRe->getStaffAcceptLang($staffIds);
        $chinese      = [];//中文和英文两种
        if (!empty($staffAccount)) {
            foreach ($staffAccount as $account) {
                $l = strtolower(substr($account['accept_language'], 0, 2));
                if ($l == 'zh') {
                    $chinese[] = $account['staff_info_id'];
                }
            }
        }

        //有没有配置轮休
        $workData = HrStaffWorkDayModel::find([
            'columns'    => 'staff_info_id,date_at',
            'conditions' => 'staff_info_id in ({ids:array}) and date_at = :date:',
            'bind'       => ['ids' => $staffIds, 'date' => $taskDate],
        ])->toArray();
        if (!empty($workData)) {
            $workData = array_column($workData, 'date_at', 'staff_info_id');
        }

        $this->baseServer = new BaseServer();
        $this->overtimeRe = new OvertimeRepository($this->timezone);

        foreach ($staffData as $staff) {
            //如果存在 记录 日志
            if (!empty($existData[$staff['staff_info_id']])) {
                $this->getDI()->get('logger')->write_log("sysNwOvertime_{$staff['staff_info_id']} {$taskDate} 申请了加班 状态 {$existData[$staff['staff_info_id']]}",
                    'info');
                continue;
            }

            //计算时长
            $res = $this->countDuration($staff['started_at'], $staff['end_at']);

            if (empty($res['duration'])) {
                $this->getDI()->get('logger')->write_log("countDuration_{$staff['staff_info_id']} {$taskDate} 计算时长  计数为0 ".json_encode($res),
                    'info');
                continue;
            }

            if(!empty($workData[$staff['staff_info_id']])){
                $this->getDI()->get('logger')->write_log("sysNwOvertime_{$staff['staff_info_id']} {$taskDate} 是休息日 ",
                    'info');
                continue;
            }

            //生成ot 记录
            $insertParam = [
                'staff_id'        => $staff['staff_info_id'],
                'type'            => $otType,
                'start_time'      => $res['start'],
                'end_time'        => $res['end'],
                'reason'          => 'system auto add for ph',
                'reject_reason'   => '',
                'state'           => enums::APPROVAL_STATUS_APPROVAL,
                'duration'        => $res['duration'],
                'higher_staff_id' => '',
                'is_anticipate'   => 0,
                'date_at'         => $taskDate,
                'references'      => json_encode([], JSON_UNESCAPED_UNICODE),
                'serial_no'       => $this->baseServer->getID(),
                'wf_role'         => 'ot_new',
            ];
            $this->overtimeRe->addOvertime($insertParam);

            //发消息
            $lang = 'en';
            if (in_array($staff['staff_info_id'], $chinese)) {
                $lang = 'zh-CN';
            }

            [$title, $content] = $this->detailFormat($staff, $lang, $insertParam);

            $staff_info_id               = $staff['staff_info_id'];
            $id                          = time().$staff_info_id.rand(1000000, 9999999);
            $param['staff_users']        = [$staff_info_id];//数组 多个员工id
            $param['message_title']      = $title;
            $param['message_content']    = $content;
            $param['staff_info_ids_str'] = $staff_info_id;
            $param['id']                 = $id;
            $param['category']           = MessageEnums::MESSAGE_CATEGORY_75;


            $this->getDI()->get('logger')->write_log('remindNwStaffAction 发送消息 '.json_encode($param), 'info');
            $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
            $bi_rpc->setParams($param);
            $bi_rpc->execute();
        }
        echo '跑完了';
    }


    //整理 提醒任务的 消息内容 task 1
    protected function remindFormat($staffInfo,$lang,$date){
        $title = $this->baseServer->getTranslationByLang($lang)->_('remindnwmsgtitle');
        $zh_content = <<<DOC
                    您好！</br></br>
                    姓名：{$staffInfo['name']}，工号：{$staffInfo['staff_info_id']}，职位：{$staffInfo['job_name']}</br></br>
                    {$date} 是节假日且当天已安排班次，无须申请加班，系统将自动计算加班费。</br></br>
                    如您有任何问题，请及时联系直线上级或部门负责人。</br></br>

DOC;

        $en_content = <<<DOC
                    Hello！</br></br>
                    Name：{$staffInfo['name']}，Employee number：{$staffInfo['staff_info_id']}，Position：{$staffInfo['job_name']}</br></br>
                    {$date} is a holiday and the shift has been scheduled on that day, no need to apply for overtime，the system will automatically calculate the overtime salary.</br></br>
                    If you have any concerns, please contact your line supervisor or department head in time.</br></br>

DOC;

        if($lang == 'zh-CN')
            $lang = 'zh';
        $var = "{$lang}_content";

        $content = $$var ?? '';

        return [$title,$content];
    }




    //整理 生成ot 记录后 详情消息内容 task 2
    protected function detailFormat($staffInfo,$lang,$param){
        //加班 类型对应描述
        $language =  $this->baseServer->getTranslationByLang($lang);
        $typeText = array(
            4 => $language->_('5115') . $language->_('1_time_salary'),
            6 => $language->_('5117') . $language->_('1.3_times_salary'),
        );

        $text = $typeText[$param['type']] ?? '';

        $duration = $param['duration'] == 8 ? 9 : $param['duration'];
        $param['end_time'] = $param['duration'] == 8 ? date('Y-m-d H:i:s',strtotime("{$param['end_time']} +1 hour")) : $param['end_time'];


        $date = $param['date_at'];

        $title = $language->_('detailnwmsgtitle');
        $zh_content = <<<DOC
                    您好！</br>
                    姓名：{$staffInfo['name']}，工号：{$staffInfo['staff_info_id']}，职位：{$staffInfo['job_name']}</br></br>
                    {$date}是节假日且当天已安排班次，系统自动计算加班费，详情如下：</br></br>
                    OT日期：{$date}</br>
                    加班类型：{$text}</br>
                    开始时间：{$param['start_time']}</br>
                    结束时间：{$param['end_time']}</br>
                    时长：{$duration} h</br>
                    加班原因：节假日当天安排加班并出勤</br>

DOC;

        $en_content = <<<DOC
                    Hello！</br>
                    Name：{$staffInfo['name']}，Employee number：{$staffInfo['staff_info_id']}，Position：{$staffInfo['job_name']}</br></br>
                    {$date} is a holiday and the shift has been   scheduled on that day, the system automatically calculates the overtime salary,   details as follows:</br></br>
                    OT date: {$date}</br>
                    Type of OT: {$text}</br>
                    Start time: {$param['start_time']}</br>
                    End time: {$param['end_time']}</br>
                    Duration: {$duration} h</br>
                    Reason for OT: Overtime work is scheduled and attended   on the day of the holiday</br>

DOC;

        if ($lang == 'zh-CN') {
            $lang = 'zh';
        }
        $var = "{$lang}_content";

        $content = $$var ?? '';

        return [$title, $content];
    }


    //上班卡时间后的第一个半点（如无半点则选第一个整点） 计算加班时长
    protected function countDuration($startTime,$endTime){
        $date      = date('Y-m-d H:00:00', strtotime($startTime));
        $half      = date('Y-m-d H:30:00', strtotime($startTime));
        $startTime = date('Y-m-d H:i:00', strtotime($startTime));//去秒误差

        //超过半点 取正点
        if ($startTime > $half) {
            $start = strtotime("{$date} +1 hour");
        } else {
            $start = strtotime($half);
        }

        if ($startTime == $date) {
            $start = strtotime($startTime);
        }


        $endTmp   = strtotime($endTime);
        $subHour  = intval(($endTmp - $start) / 3600);
        $duration = 0;
        if ($subHour >= 4) {
            $duration = 4;
        }
        if ($subHour >= 9) {
            $duration = 8;
        }

        $add_hour = $this->config->application->add_hour;

        $return['start']    = date('Y-m-d H:i:s', $start + $add_hour * 3600);
        $return['end']      = date('Y-m-d H:i:s', $start + ($duration + $add_hour) * 3600);
        $return['duration'] = $duration;

        return $return;
    }


    /**
     * 菲律宾 审批列表 部分审批 需要延时到 第二天8点之后 进审批列表（分批次添加）
     * 每天 上午 7：30 跑任务
     *
     * @param $param
     * @return void
     * php app/cli.php overtime addDelayApproval
     */
    public function addDelayApprovalAction($param)
    {
        //每天跑前一天的 延时审批数据
        $dates[] = date('Y-m-d',strtotime('-1 day'));
        $dates[] = date('Y-m-d',strtotime('-2 day'));

        //参数 测试用
        if(!empty($param[0])){
            $dates = [];
            $dates[] = date('Y-m-d',strtotime("{$param[0]}"));
            $dates[] = date('Y-m-d',strtotime("{$param[0]} -1 day"));
        }
        $this->logger->write_log("addDelayApproval 延时审批数据 {$dates[0]}  开始" , 'info');

        //获取需处理延时数据
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('aa.biz_value');
        $builder->from(['aa' => AuditApplyModel::class]);
        $builder->innerjoin(HrOvertimeModel::class, "aa.biz_type = 4 and aa.biz_value = ho.overtime_id", "ho");
        $builder->where('ho.is_delay = :is_delay:', ['is_delay' => 1]);
        $builder->andWhere('aa.delay_state = :delay_state:', ['delay_state' => WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_PENDING]);
        $builder->inWhere('ho.date_at', $dates);
        $builder->limit(self::DEFAULT_LIMIT_NUM);
        $data = $builder->getQuery()->execute()->toArray();
        if (empty($data)) {
            $this->logger->write_log("addDelayApproval 延时审批数据 {$dates[0]}  overtime 没有数据", 'info');
            echo "没延时审批数据1", PHP_EOL;
            return;
        }
        $auditIds  = array_column($data, 'biz_value');

        $rmq = new RocketMQ('create-audit-delay');
        foreach ($auditIds as $auditId) {
            $this->logger->write_log("addDelayApproval 延时审批数据 id:{$auditId}", 'info');

            $insertParams = [
                'audit_type'  => AuditListEnums::APPROVAL_TYPE_OVERTIME,
                'audit_value' => $auditId,
            ];
            $rmq->setType(RocketMQ::TAG_CREATE_AUDIT_DELAY);
            $rid = $rmq->sendMsgByTag($insertParams);

            echo sprintf("addDelayApproval，id = %d，rid = %s", $auditId, $rid), PHP_EOL;
            $this->logger->write_log(sprintf("addDelayApproval，id = %d，rid = %s", $auditId, $rid), 'info');
        }

        $this->logger->write_log("addDelayApproval 添加延时审批数据到 mq 结束", 'info');
    }

    /**
     * 菲律宾 审批列表 部分审批 需要延时到 第二天8点之后 进审批列表（一次性添加）
     * 每天 上午 7：30 跑任务
     *
     * @param $param
     * @return void
     * php app/cli.php overtime addDelayApprovalV2
     */
    public function addDelayApprovalV2Action($param)
    {
        //每天跑前一天的 延时审批数据
        $dates[] = date('Y-m-d',strtotime('-1 day'));
        $dates[] = date('Y-m-d',strtotime('-2 day'));
        //参数 测试用
        if(!empty($param[0])){
            $dates = [];
            $dates[] = date('Y-m-d',strtotime("{$param[0]}"));
            $dates[] = date('Y-m-d',strtotime("{$param[0]} -1 day"));
        }
        $this->logger->write_log("addDelayApproval 延时审批数据 {$dates[0]}  开始" , 'info');

        //获取需处理延时数据
        $data = HrOvertimeModel::find([
            'columns' => 'overtime_id,state',
            'conditions' => 'date_at in ({dates:array}) and is_delay = 1 and state in ({states:array})',
            'bind' => [
                'dates' => $dates,
                'states' => array(enums::APPROVAL_STATUS_PENDING)
            ]
        ])->toArray();
        if (empty($data)) {
            $this->logger->write_log("addDelayApproval 延时审批数据 {$dates[0]}  overtime 没有数据", 'info');
            echo "没延时审批数据1", PHP_EOL;
            return;
        }
        $auditIds  = array_column($data, 'overtime_id');
        //延时审批表
        $approvalData = AuditApplyModel::find([
            'conditions' => 'biz_type = :biz_type: and biz_value in ({biz_value:array}) and delay_state = :delay_state:',
            'bind'       => [
                'biz_type'    => AuditListEnums::APPROVAL_TYPE_OVERTIME,
                'biz_value'   => $auditIds,
                'delay_state' => WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_PENDING,
            ],
        ]);
        if (empty($approvalData->toArray())) {
            $this->logger->write_log("addDelayApproval 延时审批数据 {$dates[0]}  审批临时表 没有数据", 'info');
            echo "没延时审批数据2", PHP_EOL;
            return;
        }

        $appPendingDelayIds = array_unique(array_column($approvalData->toArray(), 'biz_value'));
        $subIds             = array_diff($auditIds, $appPendingDelayIds);
        if (!empty($subIds)) {
            $this->logger->write_log("addDelayApproval 延时审批数据 {$dates[0]}  有部分数据 不符合条件 有可能昨天跑过了" . json_encode($subIds),
                'info');
        }
        $rmq = new RocketMQ('create-audit-delay');
        foreach ($approvalData as $daObj) {
            $this->logger->write_log("addDelayApproval 延时审批数据 id:{$daObj->biz_value}", 'info');

            $insertParams = [
                'audit_type'  => AuditListEnums::APPROVAL_TYPE_OVERTIME,
                'audit_value' => $daObj->biz_value,
            ];
            $rmq->setType(RocketMQ::TAG_CREATE_AUDIT_DELAY);
            $rid = $rmq->sendMsgByTag($insertParams);

            echo sprintf("addDelayApproval，id = %d，rid = %s", $daObj->biz_value, $rid), PHP_EOL;
            $this->logger->write_log(sprintf("addDelayApproval，id = %d，rid = %s", $daObj->biz_value, $rid), 'info');
        }

        $this->logger->write_log("addDelayApproval 添加延时审批数据到 mq 结束", 'info');
    }

    public function fixAction($param){

        $ids = explode(',',$param[0]);
        //延时审批表
        $approvalData = AuditApprovalDelayModel::find([
            'conditions' => 'id in ({ids:array}) and is_process = 0',
            'bind' => [
                'ids' => $ids
            ]
        ]);

        $appIds = array_unique(array_column($approvalData->toArray(),'biz_value'));

        $approvalModel = new AuditApprovalModel();
        $wfServer = new WorkflowServer($this->lang,$this->timezone);
        foreach ($approvalData as $daObj){
            $this->getDI()->get('db')->begin();

            $applyData = AuditApplyModel::findFirst([
                'conditions' => 'biz_type = :biz_type: and biz_value = :biz_value:',
                'bind' => [
                    'biz_type' => enums::$audit_type['OT'],
                    'biz_value' => $daObj->biz_value,
                ],
                "for_update" => true,
            ]);

            if(empty($applyData)){
                continue;
            }
            //保存 approval 的数组
            $insert = $daObj->getInsertData();
            //有可能 自己提前撤销
            if(!empty($stateDate[$daObj->biz_value]) && $stateDate[$daObj->biz_value] != enums::APPROVAL_STATUS_PENDING){
                $insert['state'] = $stateDate[$daObj->biz_value];
                $insert['audit_time'] = gmdate('Y-m-d H:i:s',strtotime($applyData->final_approval_time));
            }

            $clone = clone $approvalModel;
            $clone->create($insert);

            $daObj->is_process = 1;
            $daObj->update();
            $this->getDI()->get('db')->commit();

            //如果 是待审批 发 push
            if($insert['state'] != enums::APPROVAL_STATUS_PENDING){
                continue;
            }

            if($insert['submitter_id'] == $insert['approval_id']){
                continue;
            }

            $wfServer->sendPush($insert['approval_id'],$applyData);
        }
    }




}
