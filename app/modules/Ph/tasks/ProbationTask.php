<?php
namespace FlashExpress\bi\App\Modules\Ph\Tasks;

use Exception;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\LeaveScenarioModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReasonModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Server\MailServer;
use FlashExpress\bi\App\Server\ProbationServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use ProbationTask as BaseProbationTask;


class ProbationTask extends  BaseProbationTask
{

    public function fixMessageAction($params)
    {
        $probationAuditId = $params[0];
        $cur_level        = $params[1]??0;

        $probationAudit = HrProbationAuditModel::findFirst($probationAuditId);
        if (empty($probationAudit)) {
            exit('暂无数据');
        }

        if (empty($cur_level)) {
            exit('need cur_level');
        }


        $probationAudit = $probationAudit->toArray();


        $staffInfo = HrStaffInfoModel::findFirst("staff_info_id = " . $probationAudit['staff_info_id']);

        if (empty($staffInfo)) {
            exit('员工不存在');
        }
        
        $db   = $this->getDI()->get("db");
        $bll  = new ProbationServer($this->lang, $this->add_hour);
        $src  = env('sign_url') . "/#/followUpEmployees?id=" . $probationAudit['id'] . "&probation_id=" . $probationAudit['probation_id'] . "&cul_level=" . $cur_level . "&manger_id=" . $staffInfo->manger;
        $html = "<meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no\" /><iframe src='{$src}' width='100%' height='85%'></iframe>";

        $param['staff_users']        = [$staffInfo->manger];//数组 多个员工id
        $param['message_title']      = $bll->getMsgTemplateByUserId($staffInfo->manger, 'probation_terminate_notice');
        $param['message_content']    = $html;
        $param['staff_info_ids_str'] = $staffInfo->manger;
        $param['id']                 = time() . $staffInfo->manger . rand(1000000, 9999999);
        $param['category']           = MessageEnums::MESSAGE_CATEGORY_70;//消息为 70 跟进试用期未通过员工
        $param['related_id']         = strtotime(' +2 day midnight');
        $this->getDI()->get('logger')->write_log('send_msg_on_terminate :param:' . json_encode($param), 'info');
        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($param);
        $res = $bi_rpc->execute();
        var_dump( $staffInfo->manger,$res);
        $this->getDI()->get('logger')->write_log('send_msg_on_terminate -result:' . json_encode($res), 'info');
        if ($res && $res['result']['code'] == 1) {
            $kitId = $res['result']['data'][0];
            $db->updateAsDict("hr_probation_audit", [
                'message_id'      => $kitId,
                'follow_staff_id' => $staffInfo->manger,
            ], ["conditions" => "id=" . intval($probationAudit['id'])]);
        }
    }

    public function fixedTaskSendAcknowledgementAction()
    {
        $bll = new \FlashExpress\bi\App\Modules\Ph\Server\ProbationServer($this->lang, $this->add_hour);
        $timeMap = [
            '2025-03-28',
            '2025-03-30',
            '2025-03-31',
            '2025-04-01',
            '2025-04-03',
            '2025-04-09',
            '2025-04-17',
            '2025-04-18',
            '2025-04-20',
            '2025-04-25',
            '2025-04-29',
            '2025-05-05',
            '2025-05-06',
            '2025-05-11',
            '2025-05-12',
        ];
        foreach ($timeMap as $time) {
            $bll->taskSendAcknowledgement([$time,[234873,235060,235223,235224,235225,235293,235600,235775,236336,236985,237111,237113,237216,237874,238800,238814,238827,239701,239746,239747,239748,239756,239757,239759,239761,239771,239884,240211,240376,241407,241455,241458,241459,241475,241476,241715,241904]], $bll->first_check_days);
        }
    }

    /**
     * 检查上上级 是否在评估中 选择了终止该员工的工作 并发送消息到上级
     * @param array $params
     * @return void
     */
    public function taskSendAcknowledgementAction(array $params)
    {
        $start = "";
        if (isset($params[0])) {
            $start = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }
        $bll = new \FlashExpress\bi\App\Modules\Ph\Server\ProbationServer($this->lang, $this->add_hour);
        // 检查上上级 是否在评估中 选择了终止该员工的工作 并发送消息到上级
        $bll->taskSendAcknowledgement($params, $bll->first_check_days);
        $bll->taskSendAcknowledgement($params, $bll->second_check_days,2);
        // 17 级以上的
        // 检查cpo 是否在评估中 选择了终止该员工的工作 并发送消息到上级
        $bll->taskSendAcknowledgement($params, $bll->first_check_days_exceed,1,1);
        $bll->taskSendAcknowledgement($params, $bll->second_check_days_exceed,2,1);
        $this->myLogger('taskSendAcknowledgementAction 执行完毕======end');
    }
    
	/**
	 * 第一阶段评估插入
	 * 每天执行，
	 * @param array $params
	 */
	
	public function firstAction(array $params)
	{
		$start = "";
		if (isset($params[0])) {
			$start = $params[0];
			if (strtotime($start) === false) {
				echo '传入的时间格式不对' . PHP_EOL;
				return;
			}
		}

        $bll = new \FlashExpress\bi\App\Modules\Ph\Server\ProbationServer($this->lang, $this->add_hour);
        
		if (empty($start)) {
			$start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
		}

        $evaluate_day = $bll->evaluate_day;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day[1]-1); //第一阶段
        $staffs          = $bll->getStaffs($hire_date_begin, '', 0, 0, $bll->job_grade_exceed,true); // 查询 14 级及以下的人
        $this->myLogger("查询第一阶段 {$bll->job_grade_exceed} 级以下的数据 日期小于等于" . $hire_date_begin . '===' . implode(',',array_column($staffs,'staff_info_id')));
        //查询 17 级以上的数据
        $evaluate_day_exceed = $bll->evaluate_day_exceed;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day_exceed[1]-1);
        $staffs_two      = $bll->getStaffs($hire_date_begin, '', 0, $bll->job_grade_exceed,'',true);    //查询 14 级以上的人
        $staffs          = array_merge($staffs, $staffs_two);                                         //合并两个数据组
        $this->myLogger("查询第一阶段 {$bll->job_grade_exceed} 级以上的数据 日期小于等于" . $hire_date_begin . '==='. implode(',',array_column($staffs_two,'staff_info_id')));

        if (empty($staffs)) {
            $this->myLogger('第一阶段没有找到符合的数据');
            return;
		}
		$hrProbationAudit = $bll->getProbationAuditStaffIdsByStaffIds(array_column($staffs,'staff_info_id'));

        $staffIds = array_column($staffs,'staff_info_id');
        $nonFrontLineProbationStaff = $bll->getNonFrontLineProbationStaff($staffIds,2);
        
		$db = $this->getDI()->get("db");
        foreach ($staffs as $staff) {
            if (!empty($staff['status']) && $staff['status'] == $bll::STATUS_FORMAL){
                continue;
            }
            if (
                !empty($staff['cur_level']) &&
                $staff['cur_level'] == $bll::CUR_LEVEL_FIRST &&
                !empty($staff['first_audit_status']) &&
                $staff['first_audit_status'] != HrProbationModel::FIRST_AUDIT_STATUS_WAIT
            ){
                // 已执行过的数据
                continue;
            }
            if (isset($hrProbationAudit[$staff['staff_info_id']])) {
                continue;
            }
            if (empty($staff['manager_id'])) {
                $this->myLogger('staff= ' . $staff['staff_info_id'] . ' manager_id is null 第一阶段评估没有找到上级','error');
                continue;
            }
            if (empty($staff['probation_id'])) {
                $this->myLogger('staff= ' . $staff['staff_info_id'] . ' 检查 hr_probation没数据','error');
                continue;
            }
            if (
                isset($nonFrontLineProbationStaff['probation_data'][$staff['staff_info_id']]) &&
                $nonFrontLineProbationStaff['probation_data'][$staff['staff_info_id']] == HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE &&
                isset($nonFrontLineProbationStaff['probation_staff']) &&
                !in_array($staff['staff_info_id'],$nonFrontLineProbationStaff['probation_staff'])
            ){
                if (empty($nonFrontLineProbationStaff['target'][$staff['staff_info_id']]['target_info'])){
                    feishu_push("TH,该员工目标未制定 staff_info_id:{$staff['staff_info_id']}",'https://open.feishu.cn/open-apis/bot/v2/hook/355f5d1f-3bfe-4218-a9d9-9554837af929');
                    continue;
                }
                $target_info = $nonFrontLineProbationStaff['target'][$staff['staff_info_id']]['target_info'];
                $tpl_id = null;
            }else{
                $target_info = null;
                $tpl_id = $bll->getTplIdByJobTitleGradeV2($staff['job_title_grade_v2']);
            }
            
            $this->myLogger("firstAction 第一阶段评估插入 需要执行的数据 staff= " . $staff['staff_info_id']);

            try {
                $db->begin();


                $probation_id = $staff['probation_id'];
                $db->updateAsDict("hr_probation", [
                    "first_audit_status" => HrProbationModel::FIRST_AUDIT_STATUS_RUN,
                    'updated_at' => date('Y-m-d H:i:s'),
                ], ["conditions" => "id = $probation_id"]);

                //每阶段评估时间
                $evaluate_time = (int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ? $bll->duration_day : $bll->duration_day_exceed;
                $db->insertAsDict('hr_probation_audit', [
                    'probation_id'  => $probation_id,
                    'staff_info_id' => $staff['staff_info_id'],
                    'audit_id'      => $staff['manager_id'],
                    'tpl_id'        => $tpl_id,
                    'score'         => $target_info,
                    'created_at'    => date('Y-m-d H:i:s'),
                    //第一次上级评审截止3天以后
                    'deadline_at'   => $this->getDateByDays($start, $evaluate_time['1']['1'] ?? 3, 1),

                    'updated_at' => date('Y-m-d H:i:s'),
                    'version'=>$bll->version,
                    'show_time' => date("Y-m-d H:i:s"),

                ]);
                $db->commit();
                //发送push
                $bll->push_notice_higher($staff['manager_id'], $staff['staff_info_id']);
            } catch (\Exception $e) {
                $db->rollback();
                $this->myLogger('staff=' . $staff['staff_info_id'] . ' first insert fail,message=' . $e->getMessage(), 'info');
            }
        }


        $this->myLogger('第一阶段评估执行完毕======end');
	}
    /**
     * 第二阶段评估插入
     * 直接从 TH 复制过来 主要是 为了使用 ph getStaffs 自己的方法
     * @param array $params
     */
    public function secondAction(array $params=[])
    {
        $start = "";
        if (isset($params[0])) {
            $start = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }

        $bll = new \FlashExpress\bi\App\Modules\Ph\Server\ProbationServer($this->lang, $this->add_hour);
        if (empty($start)) {
            $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        }
        $evaluate_day = $bll->evaluate_day;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day[2]-1);
        $staffs = $bll->getStaffs($hire_date_begin, '',0,0,$bll->job_grade_exceed,true);
        $this->myLogger("查询第二阶段 {$bll->job_grade_exceed} 级以下的数据 小于日期" . $hire_date_begin . '===' . implode(',',array_column($staffs,'staff_info_id')));

        //查询 17 级以上的数据
        $evaluate_day_exceed = $bll->evaluate_day_exceed;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day_exceed[2]-1);
        $staffs_two = $bll->getStaffs($hire_date_begin, '',0,$bll->job_grade_exceed,'',true);
        $staffs = array_merge($staffs,$staffs_two);//合并两个数据组
        $this->myLogger("查询第二阶段 {$bll->job_grade_exceed} 级以上的数据 小于日期" . $hire_date_begin . '===' . implode(',',array_column($staffs_two,'staff_info_id')));

        if (empty($staffs)) {
            $this->myLogger("第二阶段评估没有数据=====end");
            return;
        }
        $staffIds = array_column($staffs,'staff_info_id');
        $nonFrontLineProbationStaff = $bll->getNonFrontLineProbationStaff($staffIds,2);
        $db = $this->getDI()->get("db");
        foreach ($staffs as $staff) {
            if (!empty($staff['status']) && $staff['status'] == $bll::STATUS_FORMAL){
                continue;
            }
            if (
                !empty($staff['cur_level']) &&
                $staff['cur_level'] == $bll::CUR_LEVEL_SECOND &&
                !empty($staff['second_audit_status']) &&
                $staff['second_audit_status'] != HrProbationModel::SECOND_AUDIT_STATUS_WAIT
            ){
                // 已执行过的数据
                continue;
            }

            if (empty($staff['manager_id'])) {
                $this->myLogger("staff= " . $staff['staff_info_id'] . " manager_id is null 第二阶段评估没有上级");
                continue;
            }

            $item = $bll->getLastestProbationAudit($staff['staff_info_id']);

            //最新的item为空||或者不是第一次评审||或者还是待处理
            if (empty($item) || $item['cur_level'] != 1 || $item['audit_status'] == 1) {
                $this->myLogger("staff= " . $staff['staff_info_id'] . " 第一次评审没有完成或者已经进入第二次评审 cur_level ".$item['cur_level']." audit_status ".$item['audit_status']);
                continue;
            }

            if (
                isset($nonFrontLineProbationStaff['probation_data'][$staff['staff_info_id']]) &&
                $nonFrontLineProbationStaff['probation_data'][$staff['staff_info_id']] == HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE &&
                isset($nonFrontLineProbationStaff['probation_staff']) &&
                !in_array($staff['staff_info_id'],$nonFrontLineProbationStaff['probation_staff'])
            ){
                if (empty($nonFrontLineProbationStaff['target'][$staff['staff_info_id']]['target_info'])){
                    feishu_push("PH,该员工目标未制定 staff_info_id:{$staff['staff_info_id']}",'https://open.feishu.cn/open-apis/bot/v2/hook/355f5d1f-3bfe-4218-a9d9-9554837af929');
                    continue;
                }
                $target_info = $nonFrontLineProbationStaff['target'][$staff['staff_info_id']]['target_info'];
                $tpl_id = null;
            }else{
                $target_info = $item['score'];
                $tpl_id = $item['tpl_id'] ?? null;
            }

            $this->myLogger("secondAction 需要执行的数据 staff= " . $staff['staff_info_id'] . " cur_level ".$item['cur_level']." audit_status ".$item['audit_status']);

            try {
                $db->begin();
                //每阶段评估时间
                $evaluate_time =(int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ? $bll->duration_day : $bll->duration_day_exceed;

                $res = $db->insertAsDict("hr_probation_audit", [
                    "probation_id" => $item['probation_id'],
                    "staff_info_id" => $staff['staff_info_id'],
                    'cur_level' => 2,
                    'tpl_id' => $tpl_id,
                    'score' => $target_info,
                    'audit_id' => $staff['manager_id'],
                    'created_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'deadline_at' => $this->getDateByDays($start, $evaluate_time['2']['1'] ?? 6, 1),
                    //第二次上级评审截止6天以后，75天0点，80天24：00，改成85天24:00,86
                    'second_deadline_at'=> $this->getDateByDays($start, $evaluate_time['2']['1'] ?? 6, 1),
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'version'=>$bll->version,
                    'show_time' => date("Y-m-d H:i:s"),
                ]);
                if (!$res) {
                    throw new Exception("hr_probation_audit insert fail");
                }
                $updateProbation = [
                    "cur_level"           => 2,
                    "is_active"           => HrProbationModel::IS_ACTIVE_DEFAULT,
                    'second_audit_status' => HrProbationModel::SECOND_AUDIT_STATUS_RUN,
                    'updated_at'          => gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600),
                ];
                if ($staff['probation_channel_type'] != HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_TO_FORMAL) {
                    $job_title_grade_v2 = empty($staff['job_title_grade_v2']) ? 0 : $staff['job_title_grade_v2'];
                    $startDate          = empty($staff['contract_formal_date']) ? $staff['hire_date'] : $staff['contract_formal_date'];
                    if ($job_title_grade_v2 <= $bll->job_grade_exceed) {
                        $second_evaluate_start = $bll->getDateByDays($startDate,
                            $bll->evaluate_day[2] - 1, 1);
                        $second_evaluate_end   = $bll->getDateByDays($startDate,
                            $bll->second_check_days - 1, 1);
                    } else {
                        $second_evaluate_start = $bll->getDateByDays($startDate,
                            $bll->evaluate_day_exceed[2] - 1, 1);
                        $second_evaluate_end   = $bll->getDateByDays($startDate,
                            $bll->second_check_days_exceed - 1, 1);
                    }
                    $updateProbation['second_evaluate_start'] = $second_evaluate_start;
                    $updateProbation['second_evaluate_end']   = $second_evaluate_end;
                }

                $res = $db->updateAsDict("hr_probation", $updateProbation, ["conditions" => "staff_info_id = " . $staff['staff_info_id']]);
                if (!$res) {
                    throw new Exception("hr_probation update fail");
                }

                $db->commit();

                //发送push
                $bll->push_notice_higher($staff['manager_id'],$staff['staff_info_id']);

            } catch (\Exception $e) {
                $db->rollback();
                $this->myLogger("staff=" . $staff['staff_info_id'] . " second insert fail,message=" . $e->getMessage(), "error");
            }
        }

        $this->myLogger("第二阶段执行完毕======end");
    }

    /**
     * 没执行
     * @deprecated
     * @param $params
     * @return void
     */
    public function auto_leaveAction($params)
    {
        $currentDay = $params && isset($params[0]) ? $params[0] : date('Y-m-d');
        //要 0 时区的时间
        $startTime = gmdate('Y-m-d H:i:s',strtotime(date('Y-m-d 00:00:00', strtotime($currentDay . " -14 days"))));
        $endTime = gmdate('Y-m-d H:i:s',strtotime(date('Y-m-d 23:59:59', strtotime($currentDay . " -14 days"))));

        $db = $this->getDI()->get("db");
        //要 0 时区的时间

        $courierStartTime = gmdate('Y-m-d H:i:s',strtotime(date('Y-m-d 00:00:00', strtotime($currentDay . " -1 days"))));
        $courierEndTime = gmdate('Y-m-d H:i:s',strtotime(date('Y-m-d 23:59:59', strtotime($currentDay . " -1 days"))));

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hp' => HrProbationModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, " hsi.staff_info_id = hp.staff_info_id", 'hsi');
        $builder->columns(['hp.*']);
        $bindParams = [
            'start_time' => $startTime,
            'end_time' => $endTime,
            'courier_start_time' => $courierStartTime,
            'courier_end_time' => $courierEndTime,
            'job_ids' => [
                enums::$job_title['bike_courier'],
                enums::$job_title['van_courier'],
                enums::$job_title['tricycle_courier'],
            ],
        ];
        $builder->where(" ((hp.sign_time >= :start_time: and hp.sign_time <= :end_time: and hsi.job_title not in ({job_ids:array})) or (hsi.job_title in ({job_ids:array}) and hp.sign_time >= :courier_start_time: and hp.sign_time <= :courier_end_time: )) and hp.is_delay=0 and hsi.state=1 and hsi.wait_leave_state=0",$bindParams );
        $this->myLogger(' auto_leaveAction 每天需要离职的试用期未通过且签了字的试用期员工 bindParams=>'.json_encode($bindParams));

        $data = $builder->getQuery()->execute()->toArray();
        if ($data) {
            foreach ($data as $item) {

                $attributes = [];
                $attributes['state'] = 2;//离职
                $attributes['leave_date'] = $currentDay;//离职日期
                $attributes['is_auto_system_change'] = 1;
                $attributes['leave_reason'] = StaffLeaveReasonModel::LEAVE_REASON_85; // 未通过试用期（提前通知）
                $attributes['leave_scenario'] = LeaveScenarioModel::LEAVE_SCENARIO_55; // 员工未通过试用期（提前通知）
                $attributes['leave_type'] = HrStaffInfoModel::LEAVE_TYPE_DISMISSAL_NO_COMPENSATION; // 辞退（不赔偿）
                $attributes['leave_source'] = 8; //  试用期未通过员工
                $attributes['staff_info_id'] = $item['staff_info_id'];
                $hr_rpc = (new ApiClient('hr_rpc', '', 'update_staff_info', 'zh-CN'));
                $hr_rpc->setParams($attributes);
                $result = $hr_rpc->execute();
                $this->myLogger("probation auto_leaveAction " . json_encode($attributes, JSON_UNESCAPED_UNICODE) . "===" . json_encode($result, JSON_UNESCAPED_UNICODE));

                // 签了未通过字 将状态改为未通过
                $res = $db->updateAsDict("hr_probation", [
                    "status" => 3,
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                ], ["conditions" => "staff_info_id = ".$item['staff_info_id']]);
            }

        }
        $this->myLogger(' auto_leaveAction 每天需要离职的试用期未通过且签了字的试用期员工 完毕======end');


    }

    // 每周五找上周五到当前 试用期员工对未通过试用期签字人统计发送相应邮箱
    public function send_email_by_signsAction($params)
    {
        try {
            $currentDay = $params && isset($params[0]) && $params[0] ? $params[0] : date('Y-m-d');

            $startDay = gmdate('Y-m-d 08:00:00', strtotime($currentDay . ' -7 days'));
            $endDay = gmdate('Y-m-d 08:00:00', strtotime($currentDay));

            $fleSysDepartmentModel = new FleSysDepartmentModel();
            $settingEnvServer = new SettingEnvServer();
            $ossHelper = new OssHelper();
            $emailsMaps = $settingEnvServer->getSetVal('probation_department_email_map');
            $emailsMaps = json_decode($emailsMaps, true);
            foreach ($emailsMaps as $emailsMap) {
                $departmentIds = $emailsMap['department_ids'];
                $departmentIds = explode(',', $departmentIds);
                $specialDepartmentIds = [];
                foreach ($departmentIds as $departmentId) {
                    $specialDepartmentIds = array_merge($specialDepartmentIds,
                        $fleSysDepartmentModel->getSpecifiedDeptAndSubDept($departmentId)
                    );
                }
                $specialDepartmentIds = array_values($specialDepartmentIds);
                $builder = $this->modelsManager->createBuilder();
                $builder->columns([
                    "hsi.name",
                    "hsi.staff_info_id",
                    "sd.name as department_name",
                    "hjt.job_name as job_name",
                    "hsi.staff_info_id",
                    "hsi.job_title",
                    "hp.cur_level",
                    "hp.first_score",
                    "hp.sign_time",
                    "hp.second_score",
                ]);
                $builder->from(['hp' => HrProbationModel::class]);
                $builder->leftJoin(HrStaffInfoModel::class, "hsi.staff_info_id = hp.staff_info_id", 'hsi');
                $builder->leftJoin(SysDepartmentModel::class, " hsi.node_department_id = sd.id", 'sd');
                $builder->leftJoin(HrJobTitleModel::class, "hjt.id = hsi.job_title", 'hjt');
                $builder->where("hp.sign_time >= :start_time: and hp.sign_time <= :end_time: and hsi.node_department_id in ({department_ids:array})", [
                    'start_time' => $startDay, 'end_time' => $endDay, 'department_ids' => $specialDepartmentIds,
                ]);

                $datas =
                $builder->getQuery()->execute()->toArray();
                $header = [
                    'Employee ID', 'Employee Name', 'Position', 'Department', 'Result of Evaluation', 'Date of end Probation Period:',
                ];
                $rows = [];
                foreach ($datas as $data) {
                    $score = $data['first_score'];
                    if ($data['cur_level'] == 2) {
                        $score = $data['second_score'];
                    }
                    if (in_array($data['job_title'], [enums::$job_title['bike_courier'], enums::$job_title['van_courier'], enums::$job_title['tricycle_courier'] ])) {
                        $rows[] = [$data['staff_info_id'], $data['name'], $data['job_name'], $data['department_name'], $score >= 6000 ? 'Passed' : 'Failed', date('Y-m-d', strtotime($data['sign_time'] . ' + 1 days '.( $this->add_hour).' hours'))];
                    } else {
                        $rows[] = [$data['staff_info_id'], $data['name'], $data['job_name'], $data['department_name'], $score >= 6000 ? 'Passed' : 'Failed', date('Y-m-d', strtotime($data['sign_time'] . ' + 14 days '.( $this->add_hour).' hours'))];
                    }
                }
                $result = $ossHelper->exportExcel($header, $rows);
                $excel_path = sys_get_temp_dir() . '/' . md5( time()) . '.pdf';
                file_put_contents($excel_path,file_get_contents($result['data']));
                $sendEmail = \FlashExpress\bi\App\library\Mail::send(
                    explode(",", $emailsMap['emails']),
                    $emailsMap['title'],
                    'The Attachment is the summary of the employees whose probationary period is about to be terminated ',
                    $excel_path,
                    'Summary of employees terminated during the probationary period.xlsx');
                unlink($excel_path);

                $this->getDI()->get('logger')->write_log
                ('send_email_by_signs ' . json_encode(['url' => $result['data'], 'emails' => $emailsMap['emails'], 'result' => $sendEmail], JSON_UNESCAPED_UNICODE), 'info');

            }
        } catch (\Exception $e) {

            $this->getDI()->get('logger')->write_log('send_email_by_signs error ' . json_encode([
                    'Error_Msg' => $e->getMessage(),
                    'Error_File' => $e->getFile(),
                    'Error_Line' => $e->getLine(),
                ]) , 'error');
        }
    }



	

	


	/**
	 * @description: 给未完成转正评估的人发送消息  不允许打卡消息  每天凌晨 3 点执行
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/9/15 22:42
	 */
	public function send_probation_messageAction()
	{
		//查询出明天就过期,并且还没有审批的转正评估
		try {
			$bll = new ProbationServer($this->lang, $this->add_hour);
			
			$start = gmdate("Y-m-d 00:00:00", time() + ($this->add_hour) * 3600);
			
			//开始时间=截止日期比当前时间多1天
			//结束时间=截止日期比当期时间多2天（比开始时间多一天）
			$start = $this->getDateByDays($start, 1, 1);//大于等于
			$end   = $this->getDateByDays($start, 1, 1);//<
			
			
			//查询出明天就过期的转正评估
			$staffs = $bll->getStaffsByDeadlineDate($start, $end);
			if (empty($staffs)) {
				$this->myLogger('没有快过期的转正评估,不需要发消息');
				return false;
			}
			$staffIds        = array_column($staffs,'audit_id', "audit_id");
			//查询应该发消息的人的语言
			$staffAccount = (new \FlashExpress\bi\App\Repository\HcRepository($this->timezone))->getStaffAcceptLang($staffIds);
		
			//按照语言分组
			$language_list_infos = [];
			foreach ($staffAccount as $v) {
					$language_list_infos[substr($v['accept_language'], 0, 2)][] = $v['staff_info_id'];
					unset($staffIds[$v['staff_info_id']]); //销毁相关变量
			}
			//最后得到  语言的分组$language_list_infos['th'] = [1,2,3];
			//为了防止人数过多,按照 1000 一条拆分消息
			$send_message_infos = [];
			foreach ($language_list_infos as $k => $v) {
				$send_message_infos[] = [
					'lang'      => $k,
					'staff_ids' => array_chunk($v, 1000),
				];
			}
			$send_message_infos[] = [
				'lang'      => 'en',
				'staff_ids' => array_chunk($staffIds, 1000),
			];
		
			
			$message_title = [
				'zh' => $bll->getTranslationByLang('zh-CN')->t('hr_probation_reminder'),
				'en' => $bll->getTranslationByLang('en')->t('hr_probation_reminder'),
				'th' => $bll->getTranslationByLang('th')->t('hr_probation_reminder'),
			];
			$message_title_default = $bll->getTranslationByLang('en')->t('hr_probation_reminder');
			$message_content = 'You still have an unfinished Probation Period Evaluation, please complete the Probation Period Evaluation first';
			
			foreach ($send_message_infos as $v) {
				foreach ($v['staff_ids'] as $vv) {
					$kit_param                       = [];
					$kit_param['staff_info_ids_str'] = implode(',', $vv);
					$staff_users                     = [];
					foreach ($vv as $vvv) {
						$staff_users[] = ['id' => $vvv];
					}
					$kit_param['staff_users']     = $staff_users;
					$kit_param['message_title']   = $message_title_default;
					$kit_param['message_content'] = $message_content;
					if (!empty($v['lang']) && isset($message_title[$v['lang']])) { //存在语言 取语言的翻译
						$kit_param['message_title']   = $message_title[$v['lang']];
					}
					$kit_param['add_userid']      = 10000;
					$kit_param['category']        = MessageEnums::MESSAGE_CATEGORY_TRANSFER_EVALUATION;
					try {
						$bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
						$bi_rpc->setParams($kit_param);
						$res = $bi_rpc->execute();
					} catch (\Exception $e) {
						$this->myLogger('send_probation_message 发送转正评估提醒 发送消息失败 param:' . json_encode($kit_param) . " ==> 异常：{$e->getMessage()}", 'error');
						continue;
					}
					if ($res && $res['result']['code'] == 1) {
						$kitId[] = $res['result']['data'][0];
					} else {
						$this->myLogger('send_probation_message 发送转正评估提醒 发送消息失败 param:' . json_encode($kit_param) . " ==> result:" . json_encode($res) . " 异常：{$e->getMessage()}", 'error');
					}
					sleep(1);
				}
			}
			$this->myLogger('send_probation_message 发送转正评估提醒 成功发送消息 id :' . json_encode($kitId));
			
		} catch (\Exception $e) {
			$this->logger->write_log("task：send_probation_message 发送转正评估提醒  异常：{$e->getMessage()}", 'error');
			die("send_probation_message 发送转正评估提醒  异常 " . $e->getMessage());
		}
		
	}
	
	


    /**
     * @description:【PH】评估提醒和评估结果通过邮件发送 https://l8bx01gcjr.feishu.cn/docs/doccnY1etAeCiIMpVCq4aYtaxtn#wwojS8
     *             每天早晨 8 点发送
     * @param null
     * @return     : 
     * <AUTHOR> L.J
     * @time       : 2022/4/13 14:59
     */

    public  function  remind_sen_emailAction(){
        try {
            //获取今天
            $day_date  = gmdate("Y-m-d", time() + ($this->add_hour) * 3600);
            $day_title = gmdate("d/m/Y", time() + ($this->add_hour) * 3600);
            //昨天
            $yesterday_date = $this->getDateByDays($day_date, 1);
            $bll            = new ProbationServer($this->lang, $this->add_hour);
            //获取部门邮件组
            $dep_email = (new SettingEnvServer())->getSetVal('probation_remind_sen_email');
            if(empty($dep_email)){
                $this->myLogger( "remind_sen_emailAction ==> 没有邮件组! 日期 {$yesterday_date} -- {$day_date} -- ");
                exit;
            }
            $dep_email = json_decode($dep_email,true);
            //只取在职和停职的员工，不包含待离职和离职员工
            //查询昨天超时的第一阶段  有时间限制 只取昨天过期的
            $st_one_time_out_arr = $bll->getStaffsByDeadlineDateOnTheJob($yesterday_date, $day_date, $bll::CUR_LEVEL_FIRST, 0, [$bll::AUDIT_STATUS_TIMEOUT],false,true);

            //查询所有超时的第二阶段 (只有上级的)  没有时间限制
            $st_two_time_out_arr = $bll->getStaffsByDeadlineDateOnTheJob('', '', $bll::CUR_LEVEL_SECOND, 1, [$bll::AUDIT_STATUS_TIMEOUT],true,true);

            //查询昨天为截止日期的第二阶段结果  取已处理的  未激活的
            $st_two_arr = $bll->getStaffsByDeadlineDateOnTheJob($yesterday_date, $day_date, $bll::CUR_LEVEL_SECOND, 0, [$bll::AUDIT_STATUS_DEAL],true);

            if (empty($st_one_time_out_arr) && empty($st_two_time_out_arr) && empty($st_two_arr)) {
                $this->myLogger( "remind_sen_emailAction ==> 没有找到数据! 日期 {$yesterday_date} -- {$day_date} -- ");
                exit;
            }

            //开始拼装数据
            foreach ($dep_email as $k => $v) {
                //标题
                $dep_email[$k]['title'] = sprintf($v['title'], $day_title);
                //第一部分数据拼接
                foreach ($st_one_time_out_arr as $st_one_time_out_arr_k => $st_one_time_out_arr_v) {
                    $st_one_time_out_arr_v['ancestry_v3'] = explode('/', $st_one_time_out_arr_v['ancestry_v3']);
                    //存在交集 就证明属于这个部门
                    if (!empty(array_intersect($v['department_ids'], $st_one_time_out_arr_v['ancestry_v3']))) {
                        $dep_email[$k]['st_one_time_out_arr'][] = $st_one_time_out_arr_v;
                    }
                }
                //第二部分数据拼接
                foreach ($st_two_time_out_arr as $st_two_time_out_arr_k => $st_two_time_out_arr_v) {
                    $st_two_time_out_arr_v['ancestry_v3'] = explode('/', $st_two_time_out_arr_v['ancestry_v3']);
                    //存在交集 就证明属于这个部门
                    if (!empty(array_intersect($v['department_ids'], $st_two_time_out_arr_v['ancestry_v3']))) {
                        $dep_email[$k]['st_two_time_out_arr'][] = $st_two_time_out_arr_v;
                    }
                }

                //第三部分数据拼接
                foreach ($st_two_arr as $st_two_arr_k => $st_two_arr_v) {
                    $st_two_arr_v['ancestry_v3'] = explode('/', $st_two_arr_v['ancestry_v3']);
                    //存在交集 就证明属于这个部门
                    if (!empty(array_intersect($v['department_ids'], $st_two_arr_v['ancestry_v3']))) {
                        $dep_email[$k]['st_two_arr'][] = $st_two_arr_v;
                    }
                }
            }

            //发送邮件
            foreach ($dep_email as $k => $v) {
                //获取模板
                $dep_email[$k]['mail_content'] = $this->getMailTemplate($v['st_one_time_out_arr'] ?? [], $v['st_two_time_out_arr'] ?? [], $v['st_two_arr'] ?? []);
                if ($dep_email[$k]['mail_content']) {
                    //发邮件
                    $dep_email[$k]['mail_res'] = $this->sendMail($dep_email[$k]['mail_content']." <br/> <br/><br/>-- ".RUNTIME, $v['email'], $v['title']);
                }
            }

            $this->myLogger( "remind_sen_emailAction ==> 发送邮件!  数据 ".json_encode($dep_email, JSON_UNESCAPED_UNICODE));

        } catch (\Exception $e) {
            $this->myLogger( "remind_sen_emailAction ==> 异常!".json_encode([
                                                                              'Err_File' => $e->getFile(),
                                                                              'Err_Line' => $e->getLine(),
                                                                              'Err_Message' => $e->getMessage(),
                                                                              'Err_Code' => $e->getCode(),
                                                                          ], JSON_UNESCAPED_UNICODE), 'error');
        }
        die("完成!");
    }

    public function pending_to_not_passAction($params)
    {
        $date = $params[0] ?? date('Y-m-d');
        $server = new \FlashExpress\bi\App\Modules\Ph\Server\ProbationServer($this->lang, $this->timezone);
        $hrProbations = $server->getStaffSigned($date);
        $server->failStaffs($hrProbations);
        $hrProbations = $server->getStaffSigned($date,HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3);
        $server->failStaffs($hrProbations,HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3);
    }

    /**
     * 评估未通过继续留用的在转正日期修改为已转正
     * @param $params
     * @return void
     */
    public function not_pass_to_formalAction($params)
    {
        $date = $params[0] ?? date('Y-m-d');
        $server = new \FlashExpress\bi\App\Modules\Ph\Server\ProbationServer($this->lang, $this->timezone);

        $staffs = $server->getStaffsNotTerminated($date);
        $staffIds = array_column($staffs, 'staff_info_id');
        if (empty($staffIds)){
            return;
        }
        $this->myLogger("修改转正修改开始");
        $flag = $server->formalStaffs($staffIds);
        if (!$flag) {
            $this->myLogger("修改转正修改失败: formal staff_info_id in (" . implode(",", $staffIds) . ")", "error");
            return;
        }

        foreach ($staffs as $staff) {
            $server->putFormalLog(-1, $staff['staff_info_id'], 3, 4);
        }

        $this->myLogger("修改转正修改成功");
    }

    /**
     * 分别计算 第一阶段 第二阶段 上上级是否选择了终止该员工的工作
     * 如果是 该员工的上级by推消息
     * @param $params
     *
     */
    private function send_msg_on_terminate($params, $check_days,$cur_level=1,$is_exceed = 0)
    {
        $bll = new ProbationServer($this->lang, $this->add_hour);
        if (isset($params[0])) {
            $formalDate = $params[0];
            if (strtotime($formalDate) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        } else {
            $formal_days =  !empty($is_exceed) ? $bll->formal_days_exceed : $bll->formal_days;
            $formalDate = $this->getDateByDays(date('Y-m-d'), ($formal_days - $check_days), 1);

        }

        $hrProbations = HrProbationModel::find([
            'conditions' => 'formal_at = :formal_at: and cur_level = :cur_level: ',
            'bind' => ['formal_at' => $formalDate, 'cur_level' => $cur_level],
        ])->toArray();
        $this->myLogger('send_msg_to_terminate ' . $formalDate .' ' . $cur_level. ' hrProbations ' . json_encode($hrProbations, JSON_UNESCAPED_UNICODE));
        $probationIds =
            array_column($hrProbations, 'id');
        if ($probationIds) {
            $conditions = 'probation_id in ({probation_ids:array}) and cur_level = :cur_level: and is_terminate = 1 '; // 查询  上上级审批 且是终止
            $bind      = [
                'probation_ids' => $probationIds,
                'cur_level'     => $cur_level,
            ];
            if (!empty($is_exceed)) {
                $conditions       .= " and audit_id = :audit_id: and version = :version:";
                $bind['audit_id'] = $bll->cpo_staff_id;
                $bind['version'] = $bll->version;
            }else{
                $conditions       .= '  and audit_level = 2 ';
            }

            $probationAudits = HrProbationAuditModel::find([
                'conditions' => $conditions, // 查询  上上级审批 且是终止
                'bind'       => $bind,
            ])->toArray();


            $this->getDI()->get('logger')->write_log('send_msg_to_terminate ' . $formalDate .' ' . $cur_level. ' HrProbationAuditModel ' . json_encode($probationAudits, JSON_UNESCAPED_UNICODE), 'info');

            if ($probationAudits) {
                $staffIds = array_column($probationAudits, 'staff_info_id');
                $staffs = HrStaffInfoModel::find([
                    'conditions' => ' staff_info_id in ({staff_ids:array})',
                    'bind' => ['staff_ids' => $staffIds],
                ])->toArray();
                $staffs = array_column($staffs, null, 'staff_info_id');

                $db = $this->getDI()->get("db");
                //去下重
                $probationAudits = array_column($probationAudits, null,'staff_info_id');
                foreach ($probationAudits as $probationAudit) {
                    if (isset($staffs[$probationAudit['staff_info_id']])) {
                        //过滤$is_exceed 为 0 并且大于17级  ||  $is_exceed =1 小于 18 级的数据  并且 version = $bll->version
                        if(
                            ((empty($is_exceed) && $staffs[$probationAudit['staff_info_id']]['job_title_grade_v2'] > $bll->job_grade_exceed)
                                ||
                                (!empty($is_exceed) && $staffs[$probationAudit['staff_info_id']]['job_title_grade_v2'] <= $bll->job_grade_exceed))
                            &&  $probationAudit['version'] == $bll->version
                        ){
                            $this->getDI()->get('logger')->write_log('send_msg_on_terminate -:'.json_encode([
                                    'staff_info_id' => $probationAudit['staff_info_id'],
                                    'job_title_grade_v2' => $staffs[$probationAudit['staff_info_id']]['job_title_grade_v2'],
                                    'is_exceed'=>$is_exceed,
                                    'formalDate'=>$formalDate,
                                    'cur_level'=>$cur_level,
                                ]), 'info');
                            continue;
                        }

                        $src = env('sign_url') . "/#/followUpEmployees?id=" . $probationAudit['id'] . "&probation_id=" . $probationAudit['probation_id']. "&cul_level=" . $cur_level . "&manger_id=" . $staffs[$probationAudit['staff_info_id']]['manger'];
                        $html = "<meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no\" /><iframe src='{$src}' width='100%' height='85%'></iframe>";

                        $param['staff_users'] = [$staffs[$probationAudit['staff_info_id']]['manger']];//数组 多个员工id
                        $param['message_title'] = $bll->getMsgTemplateByUserId($staffs[$probationAudit['staff_info_id']]['manger'], 'probation_terminate_notice');
                        $param['message_content'] = $html;
                        $param['staff_info_ids_str'] = $staffs[$probationAudit['staff_info_id']]['manger'];
                        $param['id'] = time() . $staffs[$probationAudit['staff_info_id']]['manger'] . rand(1000000, 9999999);
                        $param['category'] = MessageEnums::MESSAGE_CATEGORY_70;//消息为 70 跟进试用期未通过员工
                        $param['related_id'] = strtotime(' +2 day midnight');
                        $this->getDI()->get('logger')->write_log('send_msg_on_terminate :param:' . json_encode($param), 'info');
                        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
                        $bi_rpc->setParams($param);
                        $res = $bi_rpc->execute();
                        $this->getDI()->get('logger')->write_log('send_msg_on_terminate -result:' . json_encode($res), 'info');
                        if ($res && $res['result']['code'] == 1) {
                            $kitId = $res['result']['data'][0];
                            $db->updateAsDict("hr_probation_audit", [
                                'message_id' => $kitId,
                                'follow_staff_id' => $staffs[$probationAudit['staff_info_id']]['manger'],
                            ], ["conditions" => "id=" . intval($probationAudit['id'])]);
                        }
                    }
                }
            }
        }

    }


    /*
     * 发送邮件
     */
    private function sendMail($content,$toUsers,$title) {
        if (empty($toUsers)) {
            return false;
        }
        $ret   =  (new MailServer())->send_mail( $toUsers, $title, $content);
        $this->getDI()->get('logger')->write_log("send probation mail to:" . implode(',', $toUsers) .', content is:' .  $content.',result is:'. $ret, 'info');
        return true;
    }

    /**
     * @description: 模板
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/4/13 19:06
     */
    private function  getMailTemplate($st_one_time_out_arr,$st_two_time_out_arr,$st_two_arr){
        $bll            = new ProbationServer($this->lang, $this->add_hour);
        //没有东西
        if(empty($st_one_time_out_arr) && empty($st_two_time_out_arr) && empty($st_two_arr)){
            return '';
        }
        $content = "";
        //第一个模板
        if(!empty($st_one_time_out_arr)){
            $content .= " <br/> ";
            $content .= " <p>The following is the 1st stage evaluation which timed out：</p>";
            $content .= " <br/> ";
            $content .= " 
                <table cellpadding='3' border='1' cellspacing='0' width='100%' style='font-size: 20px'>
                <tr>
                    <th>Name(ID)</th>
                    <th>Position</th>
                    <th>Department</th>
                    <th>Branch</th>
                    <th>Evaluation Type</th>
                    <th>Evaluator(ID)</th>
                    <th>Deadline</th>
                </tr>";
//            <tr>
//                    <th>{姓名} (工号)</th>
//                    <th>职位名称</th>
//                    <th>部门</th>
//                    <th>网点</th>
//                    <th> 1st stage evaluation </th>
//                    <th>审批人</th>
//                    <th>截止日期</th>
//                </tr>";
            foreach($st_one_time_out_arr as $k=>$v){
                $content .= "
                <tr>
                    <td>{$v['name']} ({$v['staff_info_id']})</td> 
                    <td>{$v['job_title_name']}</td>
                    <td>{$v['node_department_name']}</td>
                    <td>{$v['sys_store_name']}</td>
                    <td> 1st stage evaluation </td>
                    <td>{$v['audit_id_name']} ({$v['audit_id']})</td>
                    <td>{$v['deadline_at']}</td>
                </tr>";
            }
            $content .= "</table>
                        </br>
                        ";
        }

        //第二个模板
        if(!empty($st_two_time_out_arr)){
            $content .= " <br/> ";
            $content .= " <p>The following is the 2nd stage evaluation which timed out：</p>";
            $content .= " <br/> ";
            $content .= " 
                <table cellpadding='3' border='1' cellspacing='0' width='100%' style='font-size: 20px'>
                <tr>
                    <th>Name(ID)</th>
                    <th>Position</th>
                    <th>Department</th>
                    <th>Branch</th>
                    <th>Evaluation Type</th>
                    <th>Evaluator(ID)</th>
                    <th>Deadline</th>
                </tr>";
            //            <tr>
            //                    <th>{姓名} (工号)</th>
            //                    <th>职位名称</th>
            //                    <th>部门</th>
            //                    <th>网点</th>
            //                    <th> 1st stage evaluation </th>
            //                    <th>审批人</th>
            //                    <th>截止日期</th>
            //                </tr>";
            foreach($st_two_time_out_arr as $k=>$v){
                $content .= "
                <tr>
                    <td>{$v['name']} ({$v['staff_info_id']})</td> 
                    <td>{$v['job_title_name']}</td>
                    <td>{$v['node_department_name']}</td>
                    <td>{$v['sys_store_name']}</td>
                    <td> 2nd stage evaluation  </td>
                    <td>{$v['audit_id_name']} ({$v['audit_id']})</td>
                    <td>{$v['deadline_at']}</td>
                </tr>";
            }
            $content .= "</table>
                        </br>
                        ";
        }
        //第三个
        if(!empty($st_two_arr)){
            $content .= " <br/> ";
            $content .= " <p>The following is the result of the 2nd stage evaluation yesterday : </p>";
            $content .= " <br/> ";
            $content .= " 
                <table cellpadding='3' border='1' cellspacing='0' width='100%' style='font-size: 20px'>
                <tr>
                    <th>Name(ID)</th>
                    <th>Position</th>
                    <th>Department</th>
                    <th>Branch</th>
                    <th>Evaluation Type</th>
                    <th>Evaluation Phase</th>
                    <th>Evaluator(ID)</th>
                    <th>Pass or not</th>
                    <th>Deadline</th>
                </tr>";
            //            <tr>
            //                    <th>{姓名} (工号)</th>
            //                    <th>职位名称</th>
            //                    <th>部门</th>
            //                    <th>网点</th>
            //                    <th> 1st stage evaluation </th>
            //                    <th>审批人</th>
            //                    <th>截止日期</th>
            //                </tr>";
            foreach($st_two_arr as $k=>$v){
                $Evaluation_Phase = $v['audit_level'] == $bll::CUR_LEVEL_FIRST ? 'Immediate superior': 'Upper-level superior'; //1 是上级 2 是上上级
                $Pass_or_not = $v['status'] == $bll::STATUS_NOT_PASS ? 'No' : 'Yes';//3 是未通过
                $content .= "
                <tr>
                    <td>{$v['name']} ({$v['staff_info_id']})</td> 
                    <td>{$v['job_title_name']}</td>
                    <td>{$v['node_department_name']}</td>
                    <td>{$v['sys_store_name']}</td>
                    <td> 2nd stage evaluation </td>
                    <td>{$Evaluation_Phase}</td>
                    <td>{$v['audit_id_name']} ({$v['audit_id']})</td>
                    <td>{$Pass_or_not}</td>
                    <td>{$v['deadline_at']}</td>
                </tr>";
            }
            $content .= "</table>
                        </br>
                        ";
        }

        return $content;
    }

    /**
     * 修复遗留数据
     * @return void
     */
    public function fixAction()
    {
        $staffs = [163793,163993];
        $server = new \FlashExpress\bi\App\Modules\Ph\Server\ProbationServer($this->lang,$this->add_hour);
        foreach ($staffs as $staff){
            $server->syncStaffState($staff);
        }
    }

    /**
     * 过了转正时间，但是由于超时未评估依然是未转正状态，需要自动转正
     * @param $params
     */
    public function auto_convertAction($params){
        $day = empty($params[0]) ? 180 : $params[0];
        $server = new \FlashExpress\bi\App\Modules\Ph\Server\ProbationServer($this->lang,$this->add_hour);
        $server->autoConvert($day);
    }

    public function send_pass_msgAction()
    {
        die;
        $params = ['formal_at' => '2024-07-11', 'staff_info_id' => 121630];
        $server = new \FlashExpress\bi\App\Modules\Ph\Server\ProbationServer($this->lang, $this->add_hour);
        $server->sendEvaluationResultsMessage(HrProbationModel::STATUS_PASS, $params);
    }


    /**
     *获得考勤
     *每天执行
     * 纪律考核
     */
    public function get_attendanceAction()
    {
        $bll = new \FlashExpress\bi\App\Modules\Ph\Server\ProbationServer($this->lang, $this->add_hour);

        $now = gmdate("Y-m-d 00:00:00", time() + ($this->add_hour) * 3600);
        //第一阶段时间
        $day_1 = $bll->first_check_days;
        //查询 18 级一下的
        $start_1 = $this->getDateByDays($now, $day_1);     //>=45 天之前
        $end_1   = $this->getDateByDays($now, $day_1 - 1); //<44天

        //第二阶段时间
        $day_2   = $bll->second_check_days;
        $start_2 = $this->getDateByDays($now, $day_2);
        $end_2   = $this->getDateByDays($now, $day_2 - 1);

        $staffs_1 = $bll->getStaffs($start_1, $end_1, 0, 0, $bll->job_grade_exceed); // 查询 17 级及以下的人
        $staffs_2 = $bll->getStaffs($start_2, $end_2, 0, 0, $bll->job_grade_exceed); // 查询 17 级及以下的人
        $this->myLogger('试用期考勤正式员工' . $bll->job_grade_exceed . '级及以下 第一阶段入职日期 now- ' . $day_1 . '=' . $start_1 . '< ' . $end_1 . ' staffIds:' . json_encode(array_column($staffs_1,
                'staff_info_id')));
        $this->myLogger('试用期考勤正式员工' . $bll->job_grade_exceed . '级及以下 第二阶段入职日期 now-' . $day_2 . '=' . $start_2 . '< ' . $end_1 . ' staffIds:' . json_encode(array_column($staffs_2,
                'staff_info_id')));
        //查询 18 级以上的
        $day_1   = $bll->first_check_days_exceed;
        $start_1 = $this->getDateByDays($now, $day_1);
        $end_1   = $this->getDateByDays($now, $day_1 - 1);

        $day_2 = $bll->second_check_days_exceed;

        $start_2 = $this->getDateByDays($now, $day_2);
        $end_2   = $this->getDateByDays($now, $day_2 - 1);

        $staffs_1_two = $bll->getStaffs($start_1, $end_1, 0, $bll->job_grade_exceed); // 查询 17 级及以上的人
        $staffs_2_two = $bll->getStaffs($start_2, $end_2, 0, $bll->job_grade_exceed); // 查询 17 级及以上的人
        $this->myLogger('试用期考勤正式员工' . $bll->job_grade_exceed . '级以上 第一阶段入职日期  now- ' . $day_1 . '=' . $start_1 . '< ' . $end_1 . ' staffIds:' . json_encode(array_column($staffs_1_two,
                'staff_info_id')));
        $this->myLogger('试用期考勤正式员工' . $bll->job_grade_exceed . '级以上 第二阶段入职日期  now- ' . $day_2 . '=' . $start_2 . '< ' . $end_1 . ' staffIds:' . json_encode(array_column($staffs_2_two,
                'staff_info_id')));
        $staffs_1 = array_merge($staffs_1, $staffs_1_two);//合并两个数据组
        $staffs_2 = array_merge($staffs_2, $staffs_2_two);//合并两个数据组

        //合同工 入职日期到评估截止日期
        $monthContractDate     = $this->getDateByDays($now, 75 + 5);
        $monthContractStaffIds = $bll->getMonthContractStaffIds($monthContractDate,
            HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT);
        $this->myLogger('get_attendance  获取月薪制合同工评估考勤入职日期  now -75+5=' . $monthContractDate . ' staffIds:' . json_encode(array_column($monthContractStaffIds,
                'staff_info_id')));
        $staffs_1 = array_merge($staffs_1, $monthContractStaffIds);
        //合同工 V3
        $monthContractV3LevelFirstDate     = $this->getDateByDays($now, 60 + 5);
        $monthContractV3LevelFirstStaffIds = $bll->getMonthContractStaffIds($monthContractV3LevelFirstDate,
            HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3);
        $this->myLogger('get_attendance  获取月薪制合同工v3第一阶段评估考勤入职日期 now-60+5=' . $monthContractV3LevelFirstDate . ' staffIds:' . json_encode(array_column($monthContractV3LevelFirstStaffIds,
                'staff_info_id')));
        $staffs_1 = array_merge($staffs_1, $monthContractV3LevelFirstStaffIds);

        $monthContractV3LevelSecondDate     = $this->getDateByDays($now, 120 + 5);
        $monthContractV3LevelSecondStaffIds = $bll->getMonthContractStaffIds($monthContractV3LevelSecondDate,
            HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3);
        $this->myLogger('get_attendance  获取月薪制合同工v3第二阶段评估考勤入职日期 now-120+5=' . $monthContractV3LevelSecondDate . ' staffIds:' . json_encode(array_column($monthContractV3LevelSecondStaffIds,
                'staff_info_id')));
        $staffs_2 = array_merge($staffs_2, $monthContractV3LevelSecondStaffIds);
        if (empty($staffs_1) && empty($staffs_2)) {
            $this->myLogger('get_attendance  staffs 为空，不用执行' . '=====end');
            return;
        }

        $data = [];

        if (!empty($staffs_1)) {
            foreach ($staffs_1 as $staff) {
                $tmp                           = [];
                $tmp['cur_level']              = 1;
                $tmp['staff_info_id']          = $staff['staff_info_id'];
                $tmp['probation_channel_type'] = $staff['probation_channel_type'];
                $tmp['contract_formal_date']   = $staff['contract_formal_date'];
                $data[]                        = $tmp;
            }
        }

        if (!empty($staffs_2)) {
            foreach ($staffs_2 as $staff) {
                $tmp                           = [];
                $tmp['cur_level']              = 2;
                $tmp['staff_info_id']          = $staff['staff_info_id'];
                $tmp['probation_channel_type'] = $staff['probation_channel_type'];
                $tmp['contract_formal_date']   = $staff['contract_formal_date'];
                $data[]                        = $tmp;
            }
        }

        foreach ($data as $staff) {
            if ($bll->isHaveAttendance($staff['staff_info_id'], $staff['cur_level'],
                $staff['probation_channel_type'])) {
                continue;
            }
            $flag = $bll->addAttendance($staff);
            if (!$flag) {
                $this->myLogger("get_attendance staffs==add attendance error==" . $staff['staff_info_id'], "error");
            }
        }
        $this->myLogger('get_attendance staffs end');
    }
    
    public function actAction()
    {
        $a = (new ProbationServer($this->lang, $this->add_hour))->getProbationActValuesJson(['act_version'=>1]);
        print_r($a);die;
    }

}