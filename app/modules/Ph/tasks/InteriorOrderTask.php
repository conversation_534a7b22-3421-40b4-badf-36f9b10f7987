<?php
/**
 *InteriorOrderTask.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/8/10 0010 19:56
 */

namespace FlashExpress\bi\App\Modules\Ph\Tasks;

use app\enums\LangEnums;
use FlashExpress\bi\App\library\Mail;
use FlashExpress\bi\App\Models\backyard\HeadquartersAddressModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersModel;
use FlashExpress\bi\App\Enums\InteriorOrderStatusEnums;
use FlashExpress\bi\App\Models\backyard\InteriorStaffEntrySizeModel;
use FlashExpress\bi\App\Models\oa\SysDepartmentPcCode;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\BankListRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\InteriorGoodsRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StoreRepository;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use  FlashExpress\bi\App\Modules\Ph\Server\InteriorGoodsServer;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SyncWarehoseServer;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsSkuModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersGoodsSkuModel;
use FlashExpress\bi\App\Models\backyard\SysStoreGoodsModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Models\backyard\InteriorBatchApplyModel;
use FlashExpress\bi\App\Models\backyard\InteriorBatchApplyProductModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsModel;
use FlashExpress\bi\App\Enums\InteriorGoodsEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Enums\InteriorGoodsPayMethodEnums;
use FlashExpress\bi\App\Enums\InteriorOrderFundStatusEnums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use Exception;


class InteriorOrderTask extends \BaseTask
{
    /**
     * 已经发货的订单 7天后自动收货
     * Created by: Lqz.
     * CreateTime: 2020/8/11 0011 20:47
     */
    public function autoReceivedAction()
    {
        $InteriorGoodsServer = new InteriorGoodsServer();
        $autoReceivedDays = InteriorOrderStatusEnums::ORDER_STATUS_AUTO_RECEIVED_DAYS;
        $lastDate = gmdate('Y-m-d H:i:s', time() - $autoReceivedDays * 24 * 3600);

        $conditions = "order_status = :orderStatus: and  send_at <= :lastDate:";
        $bind = ["orderStatus" => InteriorOrderStatusEnums::ORDER_STATUS_SEND_CODE, "lastDate" => $lastDate];

        $ordersObj = InteriorOrdersModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
        ]);
        $orders = $ordersObj->toArray();
        if ($orders) {
            $count = count($orders);
            echo "共计需要自动收货订单:{$count} 个" . PHP_EOL;
            foreach ($orders as $key => $order) {
                $num = $key + 1;
                $loginUser = [
                    'staff_id' => $order['staff_id'],
                ];
                $params = [
                    'order_code' => $order['order_code'],
                ];
                $res = $InteriorGoodsServer->saveOrderRecevied($loginUser, $params, 'auto_received_order');
                if ($res === true) {
                    echo "第{$num}条SUCCESS:order_code:{$order['order_code']}自动收货成功" . PHP_EOL;
                } else {
                    echo $msg = "第{$num}条ERROR:order_code:{$order['order_code']}自动收货失败" . PHP_EOL;;
                    $logger = $this->getDI()->get('logger');
                    $logger->write_log($msg, 'info');
                }
            }
            echo "自动收货订单:{$count} 个已全部执行完毕" . PHP_EOL;
        } else {
            echo "No orders need to auto received" . PHP_EOL;;
        }
    }

    /**
     * 离职当日，未审核，待发货，自定取消订单
     * Created by: Lqz.
     * CreateTime: 2020/8/12 0012 18:58
     */
    public function autoCancelOrderAction()
    {
        $InteriorGoodsServer = new InteriorGoodsServer();
        // 查询未审核的，未发货的订单（待发货，预售）
        $conditions = "order_status IN ({orderStatus:array}) AND pay_method = :pay_method: AND is_audited = 0";
        $bind       = [
            "orderStatus" => [
                InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE,
            ],
            'pay_method'  => InteriorGoodsPayMethodEnums::PAY_METHOD_DEDUCTION_OF_WAGES_CODE,
        ];
        $ordersObj = InteriorOrdersModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
        ]);
        $orders = $ordersObj->toArray();
        if ($orders) {
            $staffIds = array_column($orders, 'staff_id');
            // 查询员工离职日期小于当前日期的数据，对比离职日期
            $staffsObj = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({staff_ids:array}) and state = 2',
                'bind' => ['staff_ids' => $staffIds],
            ]);
            $staffs = $staffsObj->toArray();
            $staffsKey = array_column($staffs, null, 'staff_info_id');
            $successNum = $errorNum = 0;
            foreach ($orders as $key => $order) {
                $num = $key + 1;
                //如果存在该订单的员工，取消订单
                if (isset($staffsKey[$order['staff_id']])) {
                    $loginUser = [
                        'staff_id' => $order['staff_id'],
                    ];
                    $params = [
                        'order_code'    => $order['order_code'],
                        'cancel_reason' => InteriorOrderStatusEnums::ORDER_CANCEL_REASON_PRE_SALE,
                    ];
                    $res = $InteriorGoodsServer->cancelOrder($loginUser, $params, 'system_auto_canceled');
                    if ($res === true) {
                        $successNum++;
                        echo "第{$num}条SUCCESS:order_code:{$order['order_code']}自动取消订单成功" . PHP_EOL;
                    } else {
                        $errorNum++;
                        echo $msg = "第{$num}条ERROR:order_code:{$order['order_code']}自动取消订单失败" . var_export($res,
                                true) . PHP_EOL;
                        $logger = $this->getDI()->get('logger');
                        $logger->write_log($msg, 'info');
                    }
                } else {
                    echo "订单【{$order['order_code']}】员工ID：{$order['staff_id']},暂无离职信息" . PHP_EOL;
                }
            }
            echo "自动取消订成功:{$successNum} 个，失败{$errorNum}个，已全部执行完毕" . PHP_EOL;
        } else {
            echo "No orders need to auto cancel" . PHP_EOL;;
        }
    }

    /**
     * 没有库存的取消订单
     * Created by: Lqz.
     * CreateTime: 2020/8/12 0012 18:58
     */
    public function cancelOrderByLosAction($args)
    {
        //暂定传一个参数,第一次走产品默认给的商品code,之后可传参
        $good_code = $args[0] ?? null;
        $goods_sku_code = ['FEX00092', 'FEX00093', 'FEX00098', 'FEX00115', 'FEX00116', 'FEX00118', 'FEX00119'];
        if ($good_code) {
            $goods_sku_code = [$good_code];
        }
        $InteriorGoodsServer = new InteriorGoodsServer();
        // 查询未审核的，未发货的订单
        $conditions = "order_status in ({orderStatus:array}) and is_audited = 0";
        $bind = [
            "orderStatus" =>
                [
                    InteriorOrderStatusEnums::ORDER_STATUS_WARITING_SUBMIT_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE,
                ],
        ];
        $ordersObj = InteriorOrdersModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
        ]);
        //        商品编号FEX00092 FEX00093 FEX00098 FEX00115 FEX00116 FEX00118 FEX00119
        $orders = $ordersObj->toArray();
        $orders_codes = array_column($orders, 'order_code');
        //用户
        $orders_user = array_column($orders, 'staff_id', 'order_code');

        $can_cancel = InteriorOrdersGoodsSkuModel::find([
            'conditions' => 'order_code in({order_code:array}) and goods_sku_code in ({goods_sku_code:array})',
            'bind' => ['order_code' => $orders_codes, 'goods_sku_code' => $goods_sku_code],
            'order' => 'id desc',
        ]);

        $can_cancel = $can_cancel->toArray();
        if ($can_cancel) {
            $successNum = $errorNum = 0;
            foreach ($can_cancel as $key => $order_code) {
//                不走server方法直接撤销订单,代码保留
//                $params    = [
//                    'order_code' => $order_code['order_code']
//                ];
//                $loginUser = [
//                    'staff_id' => $orders_user[$order_code['order_code']]
//                ];
//                //根据订单编号撤销定单
//                $res       = $InteriorGoodsServer->cancelOrder($loginUser, $params, 'system_auto_canceled');
                //直接修改订单表
                $orderObj = $InteriorGoodsServer->getStaffOrder($orders_user[$order_code['order_code']],
                    $order_code['order_code'], true);
                if (!$orderObj) {
                    echo "订单不存在:order_code:{$order_code['order_code']}" . PHP_EOL;
                    continue;
                }

                // 取消订单
                $orderObj->order_status = InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE;
                $date = date('Y-m-d H:i:s');
                $remark = "staff_cancel_order";
                $orderObj->remark = $orderObj->remark . "【{$remark}:{$date}】";
                $orderObj->canceled_at = $date;
                $res = $orderObj->save();
                $syncData = [
                    'orderSn' => $order_code['order_code'],
                    'lang' => $this->lang,
                ];
                $syncWarehoseServer = new SyncWarehoseServer();
                $res_wms = $syncWarehoseServer->syncCancelOrderToWmsCancelOutbound($syncData);
                //如果返回报错记录日志
                if ($res_wms['code'] != 1) {
                    echo "ERROR:order_code:{$order_code['order_code']}调用仓储接口失败 ERROR_MSG: 【{$res_wms['msg']}】" . PHP_EOL;
                }
                if ($res === true) {
                    $successNum++;
                    echo "SUCCESS:order_code:{$order_code['order_code']}取消订单成功" . PHP_EOL;
                } else {
                    $errorNum++;
                    echo $msg = "ERROR:order_code:{$order_code['order_code']}取消订单失败" . var_export($res, true) . PHP_EOL;
                    $logger = $this->getDI()->get('logger');
                    $logger->write_log($msg, 'info');
                }
            }
            echo "取消订成功:{$successNum} 个，失败{$errorNum}个，已全部执行完毕" . PHP_EOL;
        } else {
            echo "No orders need to auto cancel" . PHP_EOL;;
        }
    }

    /**
     * 获取审核结果
     * Created by: Lqz.
     * CreateTime: 2020/8/12 0012 19:16
     */
    public function getWmsOrderAuditAction()
    {
        $page = 1;
        $size = 200;
        $count = 0;
        do {
            // 查询未审核的，未发货的订单（待发货，预售, 已取消的）
            $conditions = "order_status in ({orderStatus:array}) and is_audited = 0 and out_sn != :out_sn: and out_status = :out_status: and submit_at >= :submit_at_start: and submit_at <= :submit_at_end:";
            //跟产品沟通确认后，只取近3个月的数据同步scm的状态
            $start = gmdate("Y-m-d H:i:s", strtotime("-3 month"));
            $bind = [
                "orderStatus" =>
                    [
                        InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                        InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE,
                    ],
                "out_sn" => '',
                "out_status" => 1,
                'submit_at_start' => $start,
                'submit_at_end' => gmdate('Y-m-d H:i:s'),
            ];
            $ordersObj = InteriorOrdersModel::find([
                'conditions' => $conditions,
                'bind' => $bind,
                'offset' => ($page - 1) * $size,
                'limit' => $size,
                "order" => "id desc",
            ]);
            $orders = $ordersObj->toArray();
            $successNum = 0;
            echo $page;
            if ($orders) {
                $syncServer = new SyncWarehoseServer();
                foreach ($ordersObj as $order) {
                    $postData = ['outSn' => $order->out_sn];
                    $postData['lang'] = $this->lang;
                    $res = $syncServer->syncOrderStatusFromWmsGetOutboundOrderStatus($postData, true);
                    if (isset($res['data']['status']) && in_array($res['data']['status'],
                            [10, 40, 50, 60, 70, 80, 90, 100, 110])) {
                        if ($res['data']['status'] == 10 || $res['data']['status'] == 40) {
                            $order->is_audited = 2;
                        } else {
                            $order->is_audited = 1;
                        }
                        foreach ($res['data']['statusDetail'] as $_status) {
                            if (in_array($_status['status'], [10, 40, 50])) {
                                $order->audited_at = $_status['time'];
                                $order->audited_desc = $_status['statusDesc'];
                            }
                        }
                        $order->save();
                        $successNum++;
                        echo "订单号{$order->order_code} 获取审核信息通过并同步完成" . PHP_EOL;;
                    } else {
                        echo "订单号{$order->order_code} 获取审核信息" . var_export($res, true);
                    }
                }
            }
            $num = count($orders);
            $count += $num;
            $page++;
        } while ($num > 0);
        echo "共计同步查询{$count}条订单，成功通过{$successNum}条" . PHP_EOL;
    }

    /**
     *同步 wms 库存并修改预售订单为待发货状态，修改sku库存并记录日志
     * Created by: Lqz.
     * CreateTime: 2020/8/13 0013 11:58
     */
    public function getWmsGoodsStockAction()
    {
        $conditions = "status = 1";
        $bind = [];
        $goodsSkusObj = InteriorGoodsSkuModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
//                "for_update" => true
        ]);
        $goodsSkus = $goodsSkusObj->toArray();
        if ($goodsSkus) {
            //获取sku关联的spu
            $goods_ids = array_values(array_unique(array_column($goodsSkus, 'goods_id')));
            $goods_list = InteriorGoodsModel::find([
                'columns' => 'id, goods_type',
                'conditions' => 'id in ({ids:array})',
                'bind' => ['ids' => $goods_ids],
            ])->toArray();
            $goods_list = array_column($goods_list, null, 'id');
            $goods_type_stock_ids = (new InteriorGoodsServer)->getGoodsTypeStockId();
            // 获取wms库存
            foreach ($goodsSkus as $_sku) {
                $goods_type = $goods_list[$_sku['goods_id']]['goods_type'] ?? 0;
                $barCodesStr = $_sku['goods_sku_code'];
                $syncServer = new SyncWarehoseServer();
                $postData = [
                    "barCode" => $barCodesStr,
                    "goodsStatus" => "normal",
                    'lang' => $this->lang,
                    'warehouseId' => $goods_type_stock_ids[$goods_type] ?? '',
                ];
                $res = $syncServer->syncGoodsInventoryFromWmsGoodsStock($postData, true);
                if (isset($res['data'][$barCodesStr])) {
                    $wmsInventoryList = $res['data'][$barCodesStr];
                    $db = InteriorOrdersModel::beginTransaction($this);
                    try {
                        $sku = InteriorGoodsSkuModel::findFirst([
                            'conditions' => "id = :sku_id:",
                            'bind' => ['sku_id' => $_sku['id']],
                            "for_update" => true,
                        ]);
                        // 如果不存在则初始化库存为 0
                        if (isset($wmsInventoryList['availableInventory'])) {
                            $_availableInventory = $wmsInventoryList['availableInventory'];
                            // 2020-12-02 增加记录通不过来的总库存呢
                            $toSurplusNum = $_availableInventory;
                            $chNum = $toSurplusNum - $sku->surplus_num;
                            InteriorGoodsServer::saveSkuSurplusNumAndLog($sku, 0, $chNum, $toSurplusNum,
                                'sync_from_wms_total');

                            // 如果库存小于0， 则有预售情况查询预售订单，并变更为待发货
                            // 查询该sku 预售订单的sku和订单
                            $conditions = "goods_sku_id = :skuId: and current_sku_pre_sale = 1";
                            $bind = ["skuId" => $sku->id];
                            $preOrderSkusObj = InteriorOrdersGoodsSkuModel::find([
                                'conditions' => $conditions,
                                'bind' => $bind,
                                'limit' => 20,
                                'order' => 'id asc',
                            ]);
                            $preOrderSkus = $preOrderSkusObj->toArray();
                            if ($preOrderSkus && $_availableInventory) {
                                foreach ($preOrderSkusObj as $_OrderSkuObj) {
                                    // 如果当前库存大于预售，则改变当前order_goods_sku预售状态，获取order_goods_sku 对用 order
                                    if ($_availableInventory < $_OrderSkuObj->buy_num) {
                                        continue;
                                    }
                                    // 获取该订单的其他order_sku,是否有预售的
                                    $conditions = "order_code = :order_code: and current_sku_pre_sale = 1 and id != :orderSkuId:";
                                    $bind = [
                                        "order_code" => $_OrderSkuObj->order_code,
                                        'orderSkuId' => $_OrderSkuObj->id,
                                    ];
                                    $_thisOrderOtherPreOrderSku = InteriorOrdersGoodsSkuModel::findFirst([
                                        'conditions' => $conditions,
                                        'bind' => $bind,
                                    ]);
                                    //$_OrderSkuObj->current_sku_pre_sale = 0;
                                    //$_OrderSkuObj->save();
                                    $_availableInventory -= $_OrderSkuObj->buy_num;
                                    if (!$_thisOrderOtherPreOrderSku) {
                                        // 无其他预售则改变订单为待发货
                                        $conditions = "order_code = :order_code: and order_status = :order_status:";
                                        $bind = [
                                            "order_code" => $_OrderSkuObj->order_code,
                                            "order_status" => InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE,
                                        ];
                                        $_preOrder = InteriorOrdersModel::findFirst([
                                            'conditions' => $conditions,
                                            'bind' => $bind,
                                            "for_update" => true,
                                        ]);
                                        if ($_preOrder) {
                                            $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($_preOrder->staff_id);
                                            //员工不存在
                                            if (empty($userInfo)) {
                                                continue;
                                            }
                                            //在职
                                            if ($userInfo->state == HrStaffInfoModel::STATE_1 && $userInfo->wait_leave_state == HrStaffInfoModel::WAITING_LEAVE_NO) {
                                                // 预售单是不向仓储同步的
                                                $syncRes = $this->syncAddOrderToWmsReturnWarehouseAdd($_preOrder,
                                                    ($goods_type_stock_ids[$_preOrder->goods_type] ?? ''));
                                                if ($syncRes['code'] != 1 || !$syncRes['data']) {
                                                    $msg = "该SKU{sku_id:$sku->id}预售订单{order_code:$_preOrder->order_code}同步下单仓储失败wms_msg:" . var_export($syncRes,
                                                            true) . 'aaaa' . PHP_EOL;
                                                    echo $msg;
                                                    $logger = $this->getDI()->get('logger');
                                                    $logger->write_log($msg, 'info');

                                                    $_preOrder->out_status = 2;
                                                    $_preOrder->save();

                                                    continue;
                                                } else {
                                                    $_preOrder->order_status = InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE;
                                                    $_preOrder->out_sn = $syncRes['data'];
                                                    $_preOrder->save();
                                                    $msg = "预售订单【{$_OrderSkuObj->order_code}】已修改为【待发货】";
                                                }
                                            } elseif ($userInfo->state == HrStaffInfoModel::STATE_2) {
                                                //如果员工离职，则不推送SCM出库，并且将订单状态改成已取消
                                                $date = date('Y-m-d H:i:s');
                                                $_preOrder->order_status = InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE;
                                                $_preOrder->cancel_reason = InteriorOrderStatusEnums::ORDER_CANCEL_REASON_PRE_SALE;
                                                $_preOrder->canceled_at = $date;
                                                $_preOrder->updated_at = $date;
                                                $msg = "预售订单【{$_OrderSkuObj->order_code}】已修改为【已取消】";
                                                $_preOrder->save();
                                            } else {
                                                $msg = "预售订单【{$_OrderSkuObj->order_code}】员工非在职、离职无需变更订单状态";
                                            }
                                        } else {
                                            $msg = "无预售---出现预售sku与预售order不一致";
                                        }
                                    } else {
                                        $msg = "有其他预售sku";
                                    }

                                    //当前商品改为不是预售，有可能下单失败，continue，不执行这段代码，10分钟后重新执行
                                    $_OrderSkuObj->current_sku_pre_sale = 0;
                                    $_OrderSkuObj->save();

                                    // 剩余库存保存给 sku
                                    $toSurplusNum = $_availableInventory;
                                    echo "该SKU{sku_id:$sku->id}----有预售订单库存已修改【{$_availableInventory}】----" . $msg . PHP_EOL;
                                    InteriorGoodsServer::saveSkuSurplusNumAndLog($sku, 0, $_OrderSkuObj->buy_num,
                                        $toSurplusNum,
                                        "sync_from_wms_to_pre_sell_order【order_code:{$_OrderSkuObj->order_code},order_sku_id:{$_OrderSkuObj->id}】");
                                }
                                $toSurplusNum = $_availableInventory;
                                echo "该SKU{sku_id:$sku->id}库存同步为{$toSurplusNum}" . PHP_EOL;;
                                InteriorGoodsServer::saveSkuSurplusNumAndLog($sku, 0, 0, $toSurplusNum,
                                    'sync_from_wms');
                            } else {
                                $toSurplusNum = $_availableInventory;
                                echo "该SKU{sku_id:$sku->id}----无预售订单或库存【{$toSurplusNum}】不足改为【{$toSurplusNum}】" . PHP_EOL;
                                InteriorGoodsServer::saveSkuSurplusNumAndLog($sku, 0, 0, $toSurplusNum,
                                    'sync_from_wms');
                            }
                        } else {
                            InteriorGoodsServer::saveSkuSurplusNumAndLog($sku, 0, 0, 0, 'sync_from_wms_no_data');
                        }
                        $db->commit();
                    } catch (\Exception $e) {
                        $db->rollback();
                        $msg = "interOrderSku同步wms库存,变更预售订单状态为待发货出错：" . $e->getMessage() . PHP_EOL;
                        echo $msg;
                        $logger = $this->getDI()->get('logger');
                        $logger->write_log($msg, 'info');
                    }
                } else {
                    echo "该SKUsku_id:{$_sku['id']}----无库存" . var_export($res, true) . PHP_EOL;
                }
            }
        }
        echo "执行完毕！" . PHP_EOL;
        exit;
    }

    private function syncAddOrderToWmsReturnWarehouseAdd($_preOrder, $warehouseId)
    {
        $conditions = "order_code = :order_code:";
        $bind = ["order_code" => $_preOrder->order_code];
        $preOrderSkusObj = InteriorOrdersGoodsSkuModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'order' => 'id desc',
        ]);
        $preOrderSkusArr = $preOrderSkusObj->toArray();
        $syncWmsPostData = $goods = [];
        foreach ($preOrderSkusArr as $k => $_preOrderSkus) {
            $buyNum = $_preOrderSkus['buy_num'];
            $price = $_preOrderSkus['buy_price'];
            $amount = $_preOrderSkus['pay_amount'];
            $goods[] = [
                'i' => $k,
                "barCode" => $_preOrderSkus['goods_sku_code'],
                "goodsName" => $_preOrderSkus['goods_name_th'],
                "specification" => $_preOrderSkus['attr_1'],
                "num" => $buyNum,
                //出库商品json，其中price的单位为萨当，即1泰铢=100萨当
                "price" => ($price * 100),
                "remark" => $amount,
            ];
        }

        if (empty($_preOrder->node_sn)) {
            $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($_preOrder->staff_id);
            if (empty($userInfo)) {
                return ['code' => 0, 'data' => '没有找到该工号' . $_preOrder->staff_id];
            }
            $userInfo = $userInfo->toArray();
            $nodeSn   = (new InteriorGoodsServer())->getNodeSn($_preOrder->goods_type, $userInfo, $_preOrder->receive_store_id);
        } else {
            $nodeSn = $_preOrder->node_sn;
        }

        if (empty($nodeSn)) {
            return ['code' => 0, 'data' => '没有找到该工号对应pc_code' . $_preOrder->staff_id];
        }

        $syncWmsPostData['nodeSn'] = $nodeSn;
        $syncWmsPostData['consigneeName'] = $_preOrder->staff_name;
        $syncWmsPostData['consigneePhone'] = $_preOrder->staff_mobile;
        $syncWmsPostData['province'] = $_preOrder->receive_province_name;
        $syncWmsPostData['city'] = $_preOrder->receive_city_name;
        $syncWmsPostData['district'] = $_preOrder->receive_district_name;
        $syncWmsPostData['postalCode'] = $_preOrder->receive_postal_code;
        $syncWmsPostData['consigneeAddress'] = $_preOrder->receive_address;
        $syncWmsPostData['orderSn'] = $_preOrder->order_code;
        $syncWmsPostData['node_department_id'] = $_preOrder->node_department_id;
        $syncWmsPostData['deliveryWay'] = 'express';
        $syncWmsPostData['goods'] = json_encode($goods, JSON_UNESCAPED_UNICODE);
        $syncWmsPostData['remark'] = "GF_order_server_crontab：【staff_id：{ $_preOrder->staff_id;}】";
        $syncWmsPostData['lang'] = 'zh-CN';
        //19024需求，员工商城分仓配置，用于配置不同商品类型的出库仓库ID
        if (!$warehouseId) {
            return [
                'code' => 0,
                'msg' => $this->getTranslation()->_('interior_goods_stock_unset') . $_preOrder->staff_id,
            ];
        }
        $syncWmsPostData['warehouseId'] = $warehouseId;
        $syncWarehoseServer = new SyncWarehoseServer();
        $res = $syncWarehoseServer->syncAddOrderToWmsReturnWarehouseAdd($syncWmsPostData, true);
        // 同步node_sn
        $res['node_sn'] = $nodeSn;

        return $res;
    }

    /**
     * 同步wms发货状态
     * Created by: Lqz.
     * CreateTime: 2020/8/14 0014 21:47
     * php cli.php Interior_order getWmsOrderSend
     */
    public function getWmsOrderSendAction()
    {
        $conditions = "order_status in ({orderStatus:array})";
        $bind = [
            "orderStatus" =>
                [
                    InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE,
                ],
        ];
        $ordersObj = InteriorOrdersModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
        ]);
        $orders = $ordersObj->toArray();
        $successNum = 0;
        if ($orders) {
            $syncServer = new SyncWarehoseServer();
            foreach ($ordersObj as $order) {
                $postData = [
                    'orderSn' => $order->order_code,
                    'lang' => $this->lang,
                ];
                $res = $syncServer->syncOrderStatusFromWmsGetOutboundOrderStatus($postData, true);
                if ($res['data']) {
                    if (in_array($res['data']['status'], [100, 110])) {
                        echo "进行修改发货状态" . PHP_EOL;
                        $beforeTxt = $order->order_status;
                        $afterTxt = InteriorOrderStatusEnums::ORDER_STATUS_SEND_CODE;
                        echo "修改订单号{$order->order_code}【{$beforeTxt}】to【{$afterTxt}】" . PHP_EOL;
                        $order->order_status = InteriorOrderStatusEnums::ORDER_STATUS_SEND_CODE;
                        foreach ($res['data']['statusDetail'] as $_status) {
                            if ($_status['status'] == 100) {
                                $order->send_at = $_status['time'];
                            }
                        }
                        $order->save();
                        $successNum++;
                    } elseif ($res['data']['status'] == 10) {
                        echo "修改订单号{$order->order_code}" . PHP_EOL;
                        $order->order_status = InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE;
                        $order->cancel_reason = InteriorOrderStatusEnums::ORDER_CANCEL_REASON_SCM;
                        $date = date('Y-m-d H:i:s');
                        $order->canceled_at = $date;
                        $order->updated_at = $date;
                        $order->remark = $order->remark . "【scm_cancel:{$date}】";
                        if (in_array($order->pay_method, [
                            InteriorGoodsPayMethodEnums::PAY_METHOD_OFFLINE_PAY,
                            InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE,
                        ])) {
                            $order->fund_status = InteriorOrderFundStatusEnums::FUND_STATUS_REFUNDING;
                        }
                        $order->save();
                        $successNum++;
                    } elseif (in_array($res['data']['status'], [20, 30, 40, 50, 60, 70, 80, 90])) {
                        if ($order->order_status == InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE) {
                            echo "订单{ $order->order_code}wms修改【待发货】";
                            $order->order_status = InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE;
                            $order->out_sn = $res['data']['outSn'];
                            $order->remark = $order->remark . 'sync_pre_sale_status';
                            $order->save();
                            $_conditions = "order_code = :order_code:";
                            $_bind = ["order_code" => $order->order_code];
                            $preOrderSkusObj = InteriorOrdersGoodsSkuModel::find([
                                'conditions' => $_conditions,
                                'bind' => $_bind,
                                'order' => 'id desc',
                            ]);
                            foreach ($preOrderSkusObj as $_preOrderSku) {
                                $_preOrderSku->current_sku_pre_sale = 0;
                                $_preOrderSku->save();
                            }
                        } else {
                            echo "订单{ $order->order_code} 状态为 " . $res['data']['status'] . '暂不修改' . PHP_EOL;
                        }
                    } else {
                        echo "订单{ $order->order_code}wms 返回数据失败" . var_export($res, true);
                    }
                } else {
                    echo "订单{ $order->order_code}wms 返回数据失败" . var_export($res, true);
                }
            }
        }
        $count = count($orders);
        echo "共计查询{$count}:成功修订单状态为{$successNum}条" . PHP_EOL;
        echo "执行完毕！" . PHP_EOL;
        exit;
    }

    // 统计销量到goods_skus表
    public function statisticsSaleNumAction()
    {
        $goodsSkusObj = InteriorGoodsSkuModel::find();
        $goodsSkus = $goodsSkusObj->toArray();
        if ($goodsSkus) {
            $conditionsO = 'order_status = :orderStatus:';
            $bindO = [
                'orderStatus' => InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE,
            ];
            $caceledOrdersObj = InteriorOrdersModel::find([
                'conditions' => $conditionsO,
                'bind' => $bindO,
                'column' => 'order_code',
            ]);
            $caceledOrders = $caceledOrdersObj->toArray();
            $successNum = 0;
            foreach ($goodsSkus as $sku) {
                $db = InteriorGoodsSkuModel::beginTransaction($this);
                try {
                    // 加排他锁
                    $_conditions = "id=:id:";
                    $_bind = [
                        'id' => $sku['id'],
                    ];
                    $_sku = InteriorGoodsSkuModel::findFirst([
                        'conditions' => $_conditions,
                        'bind' => $_bind,
                        "for_update" => true,
                    ]);

                    if ($caceledOrders) {
                        $caceledOrderCodes = array_column($caceledOrders, 'order_code');
                        $conditions = 'goods_sku_id = :goodsSkuId: and order_code not in ({orderCode:array})';
                        $bind = [
                            'goodsSkuId' => $sku['id'],
                            'orderCode' => $caceledOrderCodes,
                        ];
                    } else {
                        $conditions = 'goods_sku_id = :goodsSkuId:';
                        $bind = [
                            'goodsSkuId' => $sku['id'],
                        ];
                    }
                    $thisSkuSaleNum = InteriorOrdersGoodsSkuModel::sum(
                        [
                            'conditions' => $conditions,
                            'bind' => $bind,
                            'column' => 'buy_num',
                        ]);
                    $_sku->sale_num = is_null($thisSkuSaleNum) ? 0 : $thisSkuSaleNum;
                    $_sku->save();
                    $db->commit();
                    $successNum++;
                    echo "{$_sku->id}修改销量:【{$_sku->sale_num}】" . PHP_EOL;
                } catch (\Exception $e) {
                    $db->rollback();
                    $msg = "该sku【sku_id:{ $_sku->id}】统计销量出错" . $e->getMessage() . PHP_EOL;
                    echo $msg;
                    $logger = $this->getDI()->get('logger');
                    $logger->write_log($msg, 'info');
                }
            }
        } else {
            echo "没有skus数据";
        }
        $count = count($goodsSkus);
        echo "共计查询{$count}:成功统计成功{$successNum}条" . PHP_EOL;
        echo "执行完毕！" . PHP_EOL;
        exit;
    }

    /**
     * 下单没成功的，重新判断下
     */
    public function recommit_orderAction()
    {
        $orders = InteriorOrdersModel::find(
            [
                'conditions' => 'out_status = 2',
                'columns' => 'id',
            ]
        )->toArray();

        if (empty($orders)) {
            $this->getDI()->get('logger')->write_log("InteriorOrderTask = recommit_order ==no_data", 'info');
            return;
        }
        $this->getDI()->get('logger')->write_log("InteriorOrderTask = recommit_order ==flag", 'info');

        $syncServer = new SyncWarehoseServer();
        $interior_goods_server = new InteriorGoodsServer();
        $interior_goods_scm_stock_ids = $interior_goods_server->getGoodsTypeStockId();
        $orderCode = '';
        foreach ($orders as $order) {
            try {
                $this->db->begin();
                $item = InteriorOrdersModel::findFirst(
                    [
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $order['id']],
                        'for_update' => true,
                    ]
                );
                if (empty($item)) {
                    throw new Exception('not found item');
                }

                $orderCode = $item->order_code;

                $postData = [
                    'orderSn' => $item->order_code,
                    'lang' => $this->lang,
                ];

                //就已取消不需要重新下
                if ($item->order_status == InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE) {
                    $item->out_status = 1;
                } else {
                    $log = false;

                    $res = $syncServer->getOrderDetail($postData, 1);

                    //19024需求，员工商城分仓配置，用于配置不同商品类型的出库仓库ID
                    $postData['warehouseId'] = $interior_goods_scm_stock_ids[$item->goods_type] ?? '';

                    //如果不存在，需要重新下单。
                    //不存在，也会返回1，但是data是个空数组
                    if (empty($res) || $res['code'] != 1 || empty($res['data'])) {
                        // 更新省市区和邮编,历史数据不同步更新
                        if (!empty($item->receive_store_id)) {
                            $partinfo = $interior_goods_server->getOrderNewAddressInfo($item->receive_store_id);
                            $item->receive_province_name = isset($partinfo[0]) ? $partinfo[0]['name'] : '';
                            $item->receive_city_name = isset($partinfo[1]) ? $partinfo[1]['name'] : '';
                            $item->receive_district_name = isset($partinfo[2]) ? $partinfo[2]['name'] : '';
                            $item->receive_postal_code = isset($partinfo[3]) ? $partinfo[3] : '';
                            $item->receive_address = isset($partinfo[4]) ? $partinfo[4] : '';
                        }
                        $res = $this->syncAddOrderToWmsReturnWarehouseAdd($item, $postData['warehouseId']);
                        if (empty($res) || $res['code'] != 1) {
                            $item->fail_num++;
                            $item->fail_reason = $res['msg'] ?? '';
                            $log = true;
                        } else {
                            $item->out_sn = $res['data'];
                            $item->out_status = 1;
                            $item->node_sn = $res['node_sn'];
                        }
                    } else {
                        $item->out_sn = $res['data']['outSn'];
                        $item->out_status = 1;
                    }
                    $this->logger->write_log("InteriorOrderTask recommit_order => orderSn:".$item->order_code.', data:'.json_encode($res,
                            JSON_UNESCAPED_UNICODE), ($log ? 'notice' : 'info'));
                }
                $item->save();
                $this->db->commit();
            } catch (Exception $e) {
                $this->db->rollback();
                $this->logger->write_log("InteriorOrderTask orderCode===" . $orderCode);
            }
        }
    }

    /**
     * 自动在员工入职前3天、第7天发送下单提醒
     * php cli.php Interior_order send_submit_order
     */
    public function send_submit_orderAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        echo '任务名称: ' . $process_name . PHP_EOL;
        echo '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            //获取拥有权限配置
            $setting_env = new SettingEnvServer();
            $set_val = $setting_env->getSetVal('interior_free_buy_permission_set');
            if (empty($set_val)) {
                echo '新入职员工可购买免费工服的员工属性配置未设置，无需发送' . PHP_EOL;
                exit();
            }

            //员工属性配置未设置
            $set_val = json_decode($set_val, true);
            $job_ids = $set_val['job_ids'] ?? [];
            $node_department_ids = $set_val['department_ids'] ?? [];
            if (empty($job_ids) && empty($node_department_ids)) {
                echo '新入职员工可购买免费工服的员工属性配置未设置，无需发送' . PHP_EOL;
                exit();
            }

            //获取各网点类型配置的免费商品id组
            $sys_store_goods_list = (new InteriorGoodsRepository())->getFreeGoodsForSaleArr();
            if (empty($sys_store_goods_list)) {
                echo '可购买的免费工服未设置，无需发送' . PHP_EOL;
                exit();
            }

            //员工入职前3天、第7天发送下单提醒
            $this->sendThreeSubmitOrderMsg($job_ids, $node_department_ids, $sys_store_goods_list);
            $this->sendSevenSubmitOrderMsg($job_ids, $node_department_ids, $sys_store_goods_list);
        } catch (Exception $e) {
            echo $e->getMessage() . PHP_EOL;
        }
        echo PHP_EOL . '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit();
    }

    /**
     * 给入职3天员工发消息
     * @param array $job_ids 职位id组
     * @param array $node_department_ids 部门id组
     * @param array $sys_store_goods_list 各网点类型配置的免费商品组
     */
    private function sendThreeSubmitOrderMsg($job_ids, $node_department_ids, $sys_store_goods_list)
    {
        $day = 3;
        echo PHP_EOL . $day . '天，消息开始发送' . PHP_EOL;
        //员工的入职日期在发送消息当天（含）的前3天内，比如5.3号发消息，取入职时间为5.1-5.3
        $start_hire_date = date('Y-m-d', strtotime('-2 days'));
        $end_hire_date = date('Y-m-d', time());
        $buy_num = 0;
        echo $day . '天，员工入职日期范围【' . $start_hire_date . '至' . $end_hire_date . '】' . PHP_EOL;
        $params = [
            'page_num' => 1,
            'start_hire_date' => $start_hire_date,
            'end_hire_date' => $end_hire_date,
            'job_ids' => $job_ids,
            'node_department_ids' => $node_department_ids,
        ];
        $staff_list = $this->getStaffList($params);
        while (!empty($staff_list)) {
            echo $day . '天，第' . $params['page_num'] . '页，开始发送消息' . PHP_EOL;
            //找到满足提交的员工 && 发送消息
            $this->handleSendStaff($staff_list, $sys_store_goods_list, $buy_num, $day);
            echo $day . '天，第' . $params['page_num'] . '页，已发送完毕' . PHP_EOL;

            sleep(1);
            $params['page_num'] += 1;
            $staff_list = $this->getStaffList($params);
        }
        echo $day . '天，消息发送已结束' . PHP_EOL;
    }

    /**
     * 给入职7天员工发消息
     * @param array $job_ids 职位id组
     * @param array $node_department_ids 部门id组
     * @param array $sys_store_goods_list 各网点类型配置的免费商品组
     */
    private function sendSevenSubmitOrderMsg($job_ids, $node_department_ids, $sys_store_goods_list)
    {
        $day = 7;
        echo PHP_EOL . $day . '天，消息开始发送' . PHP_EOL;
        //免费购买工服数量限制
        $setting_env = new SettingEnvServer();
        $buy_num = intval($setting_env->getSetVal('interior_free_limit_num'));
        if ($buy_num <= 0) {
            echo $day . '天，免费购买工服数量限制未设置，无需发送' . PHP_EOL;
            //终止循环
            exit();
        }

        //员工的入职日期在发送消息的前8天(含）至之前90天（为了效率考虑，增加时间限制），比如5.9号发消息时，取入职时间2023-02-08至2023-05-01
        $start_hire_date = date('Y-m-d', strtotime('-90 days'));
        $end_hire_date = date('Y-m-d', strtotime('-8 days'));
        echo $day . '天，员工入职日期范围【' . $start_hire_date . '至' . $end_hire_date . '】' . PHP_EOL;

        $params = [
            'page_num' => 1,
            'start_hire_date' => $start_hire_date,
            'end_hire_date' => $end_hire_date,
            'job_ids' => $job_ids,
            'node_department_ids' => $node_department_ids,
        ];
        $staff_list = $this->getStaffList($params);
        while (!empty($staff_list)) {
            echo $day . '天，第' . $params['page_num'] . '页，开始发送消息' . PHP_EOL;
            //找到满足提交的员工 && 发送消息
            $this->handleSendStaff($staff_list, $sys_store_goods_list, $buy_num, $day);
            echo $day . '天，第' . $params['page_num'] . '页，已发送完毕' . PHP_EOL;

            sleep(1);
            $params['page_num'] += 1;
            $staff_list = $this->getStaffList($params);
        }
        echo $day . '天，消息发送已结束' . PHP_EOL;
    }

    /**
     * 获取入职前3天/7天的员工列表
     * @param array $params 请求参数组
     * @return mixed
     */
    private function getStaffList($params)
    {
        $page_size = 200;//每页条数
        $page_num = $params['page_num'];//页码
        $start_hire_date = $params['start_hire_date'];//入职起始日期
        $end_hire_date = $params['end_hire_date'];//入职截止日期
        $job_ids = $params['job_ids'];//职位id组
        $node_department_ids = $params['node_department_ids'];//部门id组

        //按照条件筛选员工列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['staff_info_id', 'IF( sys_store_id =- 1,- 1, category ) AS category']);
        $builder->from(['hr' => HrStaffInfoModel::class]);
        $builder->leftjoin(SysStoreModel::class, 'store.id = hr.sys_store_id', 'store');
        $builder->where('hr.state = :state:', ['state' => HrStaffInfoModel::STATE_1]);
        $builder->andWhere('hr.wait_leave_state = :wait_leave_state:', ['wait_leave_state' => 0]);
        $builder->andWhere('is_sub_staff = :is_sub_staff:', ['is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0]);
        $builder->inWhere('hr.formal', [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN]);
        $builder->betweenWhere('hr.hire_date', $start_hire_date, $end_hire_date);
        if (!empty($job_ids)) {
            $builder->inWhere('hr.job_title', $job_ids);
        }
        if (!empty($node_department_ids)) {
            $builder->inWhere('hr.node_department_id', $node_department_ids);
        }
        $builder->limit($page_size, ($page_size * ($page_num - 1)));
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 处理获得需要发送的工号组&&发送消息
     * @param array $staff_list
     * @param array $sys_store_goods_list 网点类型配置的免费商品id组
     * @param integer $buy_num 3天无购买0，7天超过2件
     * @param integer $day 天数
     */
    private function handleSendStaff($staff_list, $sys_store_goods_list, $buy_num, $day)
    {
        if (!empty($staff_list)) {
            $staff_ids = array_column($staff_list, 'staff_info_id');
            $staff_info = array_column($staff_list, null, 'staff_info_id');

            //获取员工的所属网点的类型中配置了免费工服购买商品的员工id组
            $set_free_staff_ids = [];
            foreach ($staff_ids as $staff_id) {
                $one_staff_info = $staff_info[$staff_id];
                //若员工所属网点类型未配置免费工服购买商品，直接跳过
                if (!isset($sys_store_goods_list[$one_staff_info['category']])) {
                    continue;
                }
                //将设置的免费商品id与员工工号绑定
                $set_free_staff_ids[$staff_id] = $sys_store_goods_list[$one_staff_info['category']];
            }

            //获取满足条件下的员工已经购买过免费工服的员工组
            if ($set_free_staff_ids) {
                $free_staff_ids = array_keys($set_free_staff_ids);
                $builder = $this->modelsManager->createBuilder();
                $builder->columns(['o.staff_id', 'SUM( sku.buy_num ) AS buy_num ']);
                $builder->from(['o' => InteriorOrdersModel::class]);
                $builder->leftjoin(InteriorOrdersGoodsSkuModel::class, 'sku.order_code = o.order_code ', 'sku');
                $builder->inWhere('o.staff_id', $free_staff_ids);
                $builder->andWhere('o.order_status != :order_status: AND sku.is_free = :is_free:', [
                    'order_status' => InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE,
                    'is_free' => InteriorOrderStatusEnums::ORDER_SKU_IS_FREE_YES,
                ]);
                $builder->groupBy('o.staff_id ');
                if ($day == 3) {
                    $builder->having('buy_num > ' . $buy_num);
                } else {
                    $builder->having('buy_num >= ' . $buy_num);
                }
                $buy_staff_list = $builder->getQuery()->execute()->toArray();
                $buy_staff_ids = array_column($buy_staff_list, 'staff_id');

                $send_staff_ids = array_diff($free_staff_ids, $buy_staff_ids);
                if (!empty($send_staff_ids)) {
                    $goods_staff_ids = [];
                    //给满足条件的员工开始发消息
                    foreach ($send_staff_ids as $staff_id) {
                        $goods_staff_ids[$set_free_staff_ids[$staff_id]][] = ['id' => $staff_id];
                    }
                    foreach ($goods_staff_ids as $goods_id => $staff_info_ids) {
                        $this->sendMsg($goods_id, $day, $staff_info_ids);
                    }
                } else {
                    echo $day . '天，当前页数据下暂无找到需要发送的员工信息' . PHP_EOL;
                }
            }
        } else {
            echo $day . '天，当前页数据下暂无找到需要发送的员工信息' . PHP_EOL;
        }
    }

    /**
     * 发送消息
     * @param integer $goods_id 商品id
     * @param integer $day 天数
     * @param array $staff_info_ids 消息员工组
     */
    private function sendMsg($goods_id, $day, $staff_info_ids)
    {
        echo $day . '天，商品id：【' . $goods_id . '】发送员工【' . json_encode($staff_info_ids,
                JSON_UNESCAPED_UNICODE) . '】' . PHP_EOL;
        //拼接push接口数据
        $data = [
            'staff_users' => $staff_info_ids,  //提交人ID
            'message_title' => 'Free working clothes order reminders',  //标题
            'message_content' => '/receiveFreeUniforms?goods_id=' . $goods_id . '&day=' . $day,//内容
            'category' => MessageEnums::MESSAGE_CATEGORY_CODE_INTERIOR_ORDER,
        ];

        //调用push接口
        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($data);
        $bi_rpc->execute();
    }


    /**
     * 自动下单工服
     * @Date: 9/17/23 6:02 PM
     * php cli.php interior_order autoAddOrder
     **/
    public function autoAddOrderAction()
    {
        ini_set('memory_limit', '512M');
        //入职开始时间
        $start_entry_time = date('Y-m-d 00:00:00', time() - 7 * 86400);
        $end_entry_time = date('Y-m-d 00:00:00', time());
        echo '查询开始时间为' . $start_entry_time . '结束时间为 ' . $end_entry_time . PHP_EOL;
        $setting_env = new SettingEnvServer();
        $auto_department_ids = $setting_env->getSetVal('auto_interior_orders_department_ids', ',');
        $auto_job_ids = $setting_env->getSetVal('auto_interior_orders_job_ids', ',');
        $manage_region_ids = $setting_env->getSetVal('shop_free_manage_region_ids', ',');
        $entry_size_arr = InteriorStaffEntrySizeModel::find([
            'conditions' => 'is_order = :is_order: and is_deleted  = :is_deleted: and entry_time >= :start_entry_time: and entry_time < :end_entry_time:',
            'bind' => [
                'is_order' => InteriorOrderStatusEnums::INTERIOR_BATCH_IS_ORDER_YES,
                'is_deleted' => enums::IS_DELETED_NO,
                'start_entry_time' => $start_entry_time,
                'end_entry_time' => $end_entry_time,

            ],
        ]);
        if (empty($entry_size_arr->toArray())) {
            echo '暂无数据' . PHP_EOL;
            exit;
        }
        $size_arr = $entry_size_arr->toArray();
        $staff_ids = array_values(array_unique(array_column($size_arr, 'staff_id')));
        $staff_arr = (new HrStaffInfoModel())->getStaffInfo($staff_ids);
        $staff_arr = array_column($staff_arr, null, 'staff_info_id');

        $store_ids = array_values(array_unique(array_column($size_arr, 'sys_store_id')));
        $store_arr = (new SysStoreModel())->getStoreArr($store_ids);
        if (in_array('-1', $store_ids)) {
            $head_office = [];
            $head = HeadquartersAddressModel::findFirst([
                'conditions' => 'id > :id:',
                'bind' => [
                    'id' => enums::IS_DELETED_NO,
                ],
                'columns' => '-1 as id,office_name as name,province_code,city_code,district_code,address as detail_address,postal_code, 0 as manage_region',
                'order' => 'id asc',
                'limit' => 1,
            ]);
            if (!empty($head)) {
                $head_office = ['-1' => $head->toArray()];
            }
            $store_arr = array_column(array_merge($store_arr, $head_office), null, 'id');
        }

        $goods_repository = new InteriorGoodsRepository();
        $barcode_arr = array_values(array_unique(array_column($size_arr, 'barcode')));
        $barcode_arr = $goods_repository->getBarcodeInteriorGoodsAll($barcode_arr);

        $interior_orders_arr = $goods_repository->getInteriorOrdersGoodsSkuNum($staff_ids);
        foreach ($entry_size_arr as $size) {
            $order = [];
            $msg = '';
            try {
                if (in_array($store_arr[$size->sys_store_id]['manage_region'], $manage_region_ids)) {
                    throw new ValidationException('不能在非免费大区');
                } elseif ($staff_arr[$size->staff_id]['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE) {
                    throw new ValidationException($size->staff_id . '员工不能是待离职');
                } elseif (!in_array($size->node_department_id, $auto_department_ids)) {
                    throw new ValidationException('不在自动批量下单部门里面');
                } elseif (!in_array($staff_arr[$size->staff_id]['job_title'], $auto_job_ids)) {
                    throw new ValidationException('不在自动批量下单职位里面');
                } elseif (in_array($size->staff_id, array_keys($interior_orders_arr))) {
                    throw new ValidationException('员工已下单');
                } elseif (!in_array($size->barcode, array_keys($barcode_arr))) {
                    throw new Exception('spu已经停售或是不存在');
                } else {
                    $size_arr = $size->toArray();

                    //当前库存大于0 待发货 小于等于0 预定中
                    $surplus_num = !empty($barcode_arr) && !empty($barcode_arr[$size->barcode]) ? $barcode_arr[$size->barcode]['surplus_num'] : 0;
                    $size_arr['is_pre_sale'] = InteriorOrderStatusEnums::INTERIOR_BATCH_IS_PRE_SALE_YES;
                    if ($surplus_num > 0) {
                        $size_arr['is_pre_sale'] = InteriorOrderStatusEnums::INTERIOR_BATCH_IS_PRE_SALE_NO;
                    }
                    $order = $this->submitOrder($size_arr, $store_arr, $barcode_arr, 2);
                }
            } catch (ValidationException $e) {
                $this->logger->write_log('interior_order_auto_add_order：工号（ ' . $size->staff_id . ' ）/' . $e->getMessage(),
                    'info');
            } catch (Exception $e) {
                $msg = $e->getMessage();
                $this->logger->write_log('interior_order_auto_add_order' . $msg, 'notice');
            }
            $orde_code = $order['orde_code'] ?? '';
            $size->is_order = InteriorOrderStatusEnums::INTERIOR_BATCH_IS_ORDER_NO;//is_order 是否下入职免费订单 1 否 2是
            $size->is_order_status = $msg == '' ? InteriorOrderStatusEnums::INTERIOR_BATCH_IS_ORDER_YES : InteriorOrderStatusEnums::INTERIOR_BATCH_IS_ORDER_NO;//下单状态 1成功  2失败
            $size->order_reason = $msg == '' ? $orde_code : $msg;
            $size->updated_at = date('Y-m-d H:i:s', time());
            if ($size->update() === false) {
                $this->logger->write_log('interior_order_auto_add_order_save_size_err' . $msg . '/' . $order['orde_code']);
                echo '执行数据' . $size->id . '执行失败: 原因是' . $msg . PHP_EOL;
            } else {
                if (!empty($orde_code)) {
                    echo '执行数据' . $size->id . '创建订单成功为:' . $orde_code . PHP_EOL;
                } else {
                    echo '执行数据' . $size->id . '创建订单失败 原因是:' . $msg . PHP_EOL;
                }
                $this->logger->write_log('interior_order_auto_add_order_save_size' . $size->id . '/' . $orde_code . '/' . $msg,
                    'info');
            }
        }
    }

    /**
     * 批量下单
     * @Date: 9/17/23 6:02 PM
     * php cli.php interior_order interiorBatchApplyOrder
     **/
    public function interiorBatchApplyOrderAction()
    {
        ini_set("memory_limit", "512M");
        $setting_env = new SettingEnvServer();
        $buy_num = intval($setting_env->getSetVal('interior_free_limit_num'));

        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['ibap.id, ibap.apply_id, ibap.staff_id, ibap.sum_buy_num, ibap.staff_state, ibap.goods_id, ibap.buy_num, ibap.barcode, ibap.size, ibap.goods_name_zh, ibap.goods_name_th, ibap.goods_name_en, ibap.manage_region, ibap.manage_piece, ibap.sys_store_id, ibap.sys_store_name, iba.is_reissue']);
        $builder->from(['ibap' => InteriorBatchApplyProductModel::class]);
        $builder->leftJoin(InteriorBatchApplyModel::class, 'iba.id = ibap.apply_id', 'iba');
        $builder->where('ibap.is_order_status = :is_order_status: and ibap.is_deleted = :is_deleted: and iba.status = :status:',
            [
                'is_order_status' => 0,
                'is_deleted' => enums::IS_DELETED_NO,
                'status' => InteriorOrderStatusEnums::INTERIOR_BATCH_APPLY_STATUS_PASS,
            ]);
        $product_arr = $builder->getQuery()->execute()->toArray();
        if (empty($product_arr)) {
            echo '暂无数据';
            exit;
        }
        $store_ids = array_values(array_unique(array_column($product_arr, 'sys_store_id')));
        $store_arr = (new SysStoreModel())->getStoreArr($store_ids);
        if (in_array('-1', $store_ids)) {
            $head_office = [];
            $head = HeadquartersAddressModel::findFirst([
                'conditions' => 'id > :id:',
                'bind' => [
                    'id' => enums::IS_DELETED_NO,
                ],
                'columns' => '-1 as id,office_name as name,province_code,city_code,district_code,address as detail_address,postal_code',
                'order' => 'id asc',
                'limit' => 1   //批量下单，产品确定目前获取总部第一个地址，需求17683
            ]);

            if (!empty($head)) {
                $head_office = ['-1' => $head->toArray()];
            }
            $store_arr = array_column(array_merge($store_arr, $head_office), null, 'id');
        }
        $goods_repository = new InteriorGoodsRepository();
        $barcode_arr = array_values(array_unique(array_column($product_arr, 'barcode')));
        $barcode_arr = $goods_repository->getBarcodeInteriorGoodsAll($barcode_arr);
        $staff_ids = array_values(array_unique(array_column($product_arr, 'staff_id')));
        $staff_arr = (new HrStaffInfoModel())->getStaffInfo($staff_ids);
        $staff_arr = array_column($staff_arr, null, 'staff_info_id');
        $interior_orders_arr = $goods_repository->getInteriorOrdersGoodsSkuNum($staff_ids);

        $goods_barcode = array_values(array_unique(array_column($product_arr, 'barcode')));
        $goods_arr = $goods_repository->getBarcodeInteriorGoodsAll($goods_barcode);
        $goods_arr = array_column($goods_arr, null, 'goods_id');

        foreach ($product_arr as $product) {
            $msg = '';
            $order = [];
            $product_obj = InteriorBatchApplyProductModel::findFirst($product['id']);
            try {
                if ($product['is_reissue'] == InteriorGoodsEnums::INTERIOR_BATCH_APPLY_IS_REISSUE_YES) {
                    $order = $this->submitOrder($product, $store_arr, $barcode_arr, 4);
                    if (isset($order['msg'])) {
                        throw new Exception($order['msg']);
                    }
                } else {
                    $last_buy_num = $buy_num - ($interior_orders_arr[$product['staff_id']]['buy_nums'] ?? 0);
                    if (empty($staff_arr[$product['staff_id']]) || ($staff_arr[$product['staff_id']]['state'] == HrStaffInfoModel::STATE_1 && $staff_arr[$product['staff_id']]['wait_leave_state'] == HrStaffInfoModel::STATE_1) || $staff_arr[$product['staff_id']]['state'] != HrStaffInfoModel::STATE_1) {
                        throw new Exception('员工非正式在职员工');
                    } elseif ($last_buy_num < $product['buy_num']) {
                        throw new Exception('超出免费工服购买上限');
                    } else {
                        //现有库存
                        $surplus_num = !empty($goods_arr) && !empty($goods_arr[$product['goods_id']]) ? $goods_arr[$product['goods_id']]['surplus_num'] : 0;
                        if ($surplus_num > 0) {
                            $product['is_pre_sale'] = InteriorOrderStatusEnums::INTERIOR_BATCH_IS_PRE_SALE_NO;//待发货
                        } else {
                            $product['is_pre_sale'] = InteriorOrderStatusEnums::INTERIOR_BATCH_IS_PRE_SALE_YES;//预定中
                        }
                        $order = $this->submitOrder($product, $store_arr, $barcode_arr, 3);
                    }
                }
            } catch (\Exception $e) {
                $msg = $e->getMessage();
                $this->logger->write_log('interior_order_auto_add_order' . $msg);
            }
            $orde_code = $order['orde_code'] ?? '';
            //1 下单成功  2 下单失败
            $product_obj->is_order_status = $msg == '' ? InteriorOrderStatusEnums::INTERIOR_BATCH_IS_ORDER_YES : InteriorOrderStatusEnums::INTERIOR_BATCH_IS_ORDER_NO;
            $product_obj->order_reason = $msg == '' ? $orde_code : $msg;
            $product_obj->updated_at = date('Y-m-d H:i:s', time());
            if ($product_obj->update() === false) {
                $this->logger->write_log('interior_order_auto_add_order_save_size_err' . $msg . '/' . $orde_code);
                echo '执行数据' . $product_obj->id . '执行失败: 原因是' . $msg . PHP_EOL;
            } else {
                if (!empty($orde_code)) {
                    echo '执行数据' . $product_obj->id . '创建订单成功为:' . $orde_code . PHP_EOL;
                } else {
                    echo '执行数据' . $product_obj->id . '创建订单失败 原因是:' . $msg . PHP_EOL;
                }
                $this->logger->write_log('interior_order_auto_add_order_save_size' . $product_obj->id . '/' . $orde_code . '/' . $msg,
                    'info');
            }
        }
    }


    /**
     * 下单
     * @param array $size_arr
     * @param array $store_arr
     * @param array $barcode_arr
     * @param int $source 来源
     * @return  array
     **/
    public function submitOrder($size_arr, $store_arr, $barcode_arr, $source = 2)
    {
        $this->lang = 'en';
        $login_user['staff_id'] = $size_arr['staff_id'];
        $param_in = [
            'pay_method' => '1',//支付方式-工资抵扣
            'is_has_free' => 1,//是否有免费商品0否，1是
            'is_pre_sale' => $size_arr['is_pre_sale'] ?? 0,//是否预售0非1是
            'city_code' => $store_arr[$size_arr['sys_store_id']]['city_code'],
            'detail_address' => $store_arr[$size_arr['sys_store_id']]['detail_address'],
            'district_code' => $store_arr[$size_arr['sys_store_id']]['district_code'],
            'postal_code' => $store_arr[$size_arr['sys_store_id']]['postal_code'],
            'province_code' => $store_arr[$size_arr['sys_store_id']]['province_code'],
            'staff_store_id' => $size_arr['sys_store_id'],
            'platform' => 'by-task',
            'source' => $source,
            'is_reissue' => $size_arr['is_reissue'] ?? 0,
            'buy_goods_ids_arr' => [
                [
                    'goods_sku_code' => $size_arr['barcode'],
                    'goods_type' => $barcode_arr[$size_arr['barcode']]['goods_type'],
                    'goods_id' => $size_arr['goods_id'],
                    'goods_sku_id' => $barcode_arr[$size_arr['barcode']]['id'],
                    'buy_num' => $size_arr['buy_num'] ?? 1,//下单数量
                    'is_free' => $barcode_arr[$size_arr['barcode']]['is_free'],
                ],
            ],
        ];

        $order = (new InteriorGoodsServer($this->lang))->submitOrder($login_user, $param_in);
        if (isset($order['msg'])) {
            throw new Exception($order['msg']);
        }
        return $order;
    }


    /**
     * 无头件下单1小时不上传支付凭证的订单自动取消
     * php cli.php Interior_order autoCancelTimeoutUnclaimedOrder
     */
    public function autoCancelTimeoutUnclaimedOrderAction()
    {
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $ordersObj = InteriorOrdersModel::find([
            'columns' => ['id'],
            'conditions' => 'payment_voucher_status = :payment_voucher_status: and pay_method = :pay_method: and goods_type = :goods_type: and order_status = :order_status: and pay_method = :pay_method: and created_at < :created_at:',
            'bind' => [
                'payment_voucher_status' => InteriorGoodsPayMethodEnums::GOODS_PAYMENT_VOUCHER_STATUS_ING,
                'pay_method' => InteriorGoodsPayMethodEnums::PAY_METHOD_OFFLINE_PAY,
                'goods_type' => InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE,
                'order_status' => InteriorOrderStatusEnums::ORDER_STATUS_WARITING_SUBMIT_CODE,
                'created_at' => date('Y-m-d H:i:s', (time() - 3600)),
            ],
        ]);
        $orders = $ordersObj->toArray();
        if ($orders) {
            $cancel_order_arr = array_column($orders, 'id');
            $count = count($cancel_order_arr);
            $cancel_order_ids = implode(',', $cancel_order_arr);
            try {
                $date = gmdate("Y-m-d H:i:s", time() + $add_hour * 3600);
                $res = $this->db->updateAsDict(
                    'interior_orders',
                    [
                        'order_status' => InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE,
                        'updated_at' => $date,
                        'canceled_at' => $date,
                        'cancel_reason' => InteriorOrderStatusEnums::ORDER_CANCEL_REASON_SYSTEM,
                    ],
                    ["conditions" => "id in (" . $cancel_order_ids . ")"]
                );
                if (!$res) {
                    throw new \Exception("auto cancel timeout unpaid orders failed");
                }
                $msg = "autoCancelTimeoutUnclaimedOrderAction超时1小时未未上传支付凭证的订单自动取消订单ID组为：【" . $cancel_order_ids . "】；共计操作取消订单数：【" . $count . "】条" . PHP_EOL;
            } catch (\Exception $e) {
                $msg = "autoCancelTimeoutUnpaid超时1小时未未上传支付凭证的订单自动取消失败：" . $e->getMessage() . PHP_EOL;

            }
            echo $msg;
            $this->getDI()->get('logger')->write_log($msg, 'info');
        } else {
            echo "No expired orders need to auto cancel" . PHP_EOL;;
        }
    }


    /**
     * 一次性脚本处理表 sys_store_goods到新表里面
     * php cli.php Interior_order store_goods_to_interior_goods
     **/

    public function store_goods_to_interior_goodsAction()
    {
        $store_goods = SysStoreGoodsModel::find()->toArray();
        if ($store_goods) {
            foreach ($store_goods as $goods_store_cate) {
                $rs = [];
                $goods_id_arr = explode(',', $goods_store_cate['goods_id']);
                foreach ($goods_id_arr as $item) {
                    $store_cate_arr = explode(',', $goods_store_cate['store_cate_id']);
                    foreach ($store_cate_arr as $store_cate) {
                        $rel['goods_id'] = $item;
                        $rel['permission_type'] = 1;
                        $rel['store_cate_id'] = $store_cate;
                        $rel['sys_store_cate'] = $goods_store_cate['sys_store_cate'];
                        $rel['created_at'] = date('Y-m-d H:i:s');
                        $rel['created_id'] = $goods_store_cate['updated_id'];
                        $rs[] = $rel;
                    }
                }
                if (!empty($rs)) {
                    $model = new AttendanceRepository($this->lang, $this->timezone);
                    $result = $model->batch_insert('interior_goods_store_cate_rel', $rs);
                    if ($result) {
                        $affectedRows = $this->getDI()->get('db')->affectedRows();
                        echo "插入 interior_goods_store_cate_rel表 {$affectedRows} 行数据购买权限", PHP_EOL;
                    }
                }
                $free_goods_id_arr = explode(',', $goods_store_cate['free_goods_id']);
                $rs_free_arr = [];
                foreach ($free_goods_id_arr as $item_free) {
                    $store_cate_free_arr = explode(',', $goods_store_cate['store_cate_id']);
                    foreach ($store_cate_free_arr as $store_cate_free) {
                        $rel_free['goods_id'] = $item_free;
                        $rel_free['permission_type'] = 2;
                        $rel_free['store_cate_id'] = $store_cate_free;
                        $rel_free['sys_store_cate'] = $goods_store_cate['sys_store_cate'];
                        $rel_free['created_at'] = date('Y-m-d H:i:s');
                        $rel_free['created_id'] = $goods_store_cate['updated_id'];
                        $rs_free_arr[] = $rel_free;
                    }
                }
                if (!empty($rs_free_arr)) {
                    $model_free = new AttendanceRepository($this->lang, $this->timezone);
                    $result_free = $model_free->batch_insert('interior_goods_store_cate_rel', $rs_free_arr);
                    if ($result_free) {
                        $affectedRows = $this->getDI()->get('db')->affectedRows();
                        echo "插入 interior_goods_store_cate_rel表 {$affectedRows} 行数据免费权限", PHP_EOL;
                    }
                }
            }
        } else {
            echo "没有skus数据";
        }
        echo '开始处理是否限购,是否免费开关历史数据' . PHP_EOL;
        $setting_model = new SettingEnvServer();
        $goodsIds = $setting_model->getSetVal($code = 'interior_buy_limit_goods_id', ',');
        $interior_goods_arr = InteriorGoodsModel::find();
        if (!empty($interior_goods_arr->toArray())) {
            foreach ($interior_goods_arr as $item_goods) {
                $item_goods->is_limit_buy = 0;
                $item_goods->is_free = 0;
                if (in_array($item_goods->id, $goodsIds)) {
                    $item_goods->is_limit_buy = 1;
                }
                if ($item_goods->free_num > 0) {
                    $item_goods->is_free = 1;
                }
                if ($item_goods->save() === false) {
                    echo "修改 interior_goods 表 失败" . $item_goods->id, PHP_EOL;
                }
            }
        }
        echo "执行完毕！" . PHP_EOL;
        exit;
    }


    /**
     * 支付批证审核通过，脚本开始开始添加出库单
     *  php cli.php interior_order sync_add_order_to_wms
     */
    public function sync_add_order_to_wmsAction()
    {
        $interior_orders_obj = InteriorOrdersModel::find([
            'conditions' => 'goods_type = :goods_type: and payment_voucher_audit_status = :payment_voucher_audit_status: and payment_voucher_status = :payment_voucher_status: and out_status = :out_status:',
            'bind' => [
                'goods_type' => InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE,
                'payment_voucher_audit_status' => InteriorOrderStatusEnums::PAYMENT_VOUCHER_AUDIT_STATUS_FINISH,
                'payment_voucher_status' => InteriorGoodsPayMethodEnums::GOODS_PAYMENT_VOUCHER_STATUS_UPLOADED,
                'out_status' => InteriorOrderStatusEnums::OUT_STATUS_INIT,
            ],
        ]);
        $count = count($interior_orders_obj->toArray());
        echo "支付批证审核通过，脚本开始开始添加出库单数量是:{$count}" . PHP_EOL;
        if ($count > 0) {
            $interior_goods_scm_stock_ids = (new InteriorGoodsServer())->getGoodsTypeStockId();
            foreach ($interior_orders_obj as $interior_orders) {
                $goods_sku_arr = $interior_orders->getOrdersGoodsSku()->toArray();
                $sync_wms_data = $goods = [];
                if (!empty($goods_sku_arr)) {
                    foreach ($goods_sku_arr as $key => $good) {
                        $goods[] = [
                            'i' => $key + 1,
                            'barCode' => $good['goods_sku_code'],
                            'goodsName' => $good['goods_name_th'],
                            'specification' => $good['attr_1'],
                            'num' => $good['buy_num'],
                            'price' => ($good['buy_price'] * 100),
                            'remark' => $good['pay_amount'],
                        ];
                    }
                    $sync_wms_data['nodeSn'] = $interior_orders->node_sn;
                    $sync_wms_data['consigneeName'] = $interior_orders->staff_name;
                    $sync_wms_data['consigneePhone'] = $interior_orders->staff_mobile;
                    $sync_wms_data['province'] = $interior_orders->receive_province_name;
                    $sync_wms_data['city'] = $interior_orders->receive_city_name;
                    $sync_wms_data['district'] = $interior_orders->receive_district_name;
                    $sync_wms_data['postalCode'] = $interior_orders->receive_postal_code;
                    $sync_wms_data['consigneeAddress'] = $interior_orders->receive_address;
                    $sync_wms_data['orderSn'] = $interior_orders->order_code;
                    $sync_wms_data['node_department_id'] = $interior_orders->node_department_id;
                    $sync_wms_data['deliveryWay'] = 'express';
                    $sync_wms_data['goods'] = json_encode($goods, JSON_UNESCAPED_UNICODE);
                    $sync_wms_data['remark'] = $interior_orders->remark;
                    $sync_wms_data['lang'] = $this->lang;
                    //19024需求，员工商城分仓配置，用于配置不同商品类型的出库仓库ID
                    $sync_wms_data['warehouseId'] = $interior_goods_scm_stock_ids[$interior_orders->goods_type] ?? '';
                    if (!$sync_wms_data['warehouseId']) {
                        //未配置出库仓库id直接失败
                        $res['code'] = 0;
                        $res['msg'] = $this->getTranslation()->_('interior_goods_stock_unset');
                    } else {
                        $sync_warehose_server = new SyncWarehoseServer();
                        $res = $sync_warehose_server->syncAddOrderToWmsReturnWarehouseAdd($sync_wms_data, true);
                    }
                    if (empty($res) || $res['code'] != 1 || !$res['data']) {
                        $this->getDI()->get('logger')->write_log('无头件出库失败, 出库订单及数据是(' . $interior_orders->order_code . '):' . json_encode($sync_wms_data,
                                JSON_UNESCAPED_UNICODE) . ' 失败返回数据：' . json_encode($res, JSON_UNESCAPED_UNICODE),
                            'error');
                        $interior_orders->fail_num = 1;
                        $interior_orders->out_status = 2;
                        $interior_orders->fail_reason = $res['msg'] ?? '';
                    } else {
                        $interior_orders->out_status = 1;
                        $interior_orders->out_sn = $res['data'];
                        $interior_orders->order_status = InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE;
                        $interior_orders->remark = $interior_orders->remark . "【wms_out_sn：{$res['data']}】";
                    }
                    if ($interior_orders->save() === false) {
                        $this->getDI()->get('logger')->write_log('无头件出库后修改数据失败，订单号号是(' . $interior_orders->order_code . ')',
                            'error');
                    } else {
                        echo "出库订单{$interior_orders->order_code}出库" . ($interior_orders->out_status == 1 ? '成功，出库单号是' . $interior_orders->out_sn : '失败') . PHP_EOL;
                    }
                }
            }
            echo "出库全部执行完毕" . PHP_EOL;
        }
    }

    /**
     * 员工商城定时邮件推送待退款数据
     */
    public function sendRefundReminderAction()
    {
        $log = '发送员工商城待退款数据邮件提醒' . PHP_EOL;
        try {
            //邮箱
            $emails = (new SettingEnvServer())->getSetVal('interior_refund_reminder_emails');
            if ($emails) {
                // 获取上周一
                $last_monday_date = date('Y-m-d', strtotime('last week Monday'));

                // 获取上周日
                $last_sunday_date = date('Y-m-d', strtotime('last week Sunday'));

                $interior_orders = InteriorOrdersModel::find([
                    'conditions' => 'fund_status = :fund_status: AND goods_type = :goods_type: AND canceled_at >= :canceled_at_start: AND canceled_at <= :canceled_at_end:',
                    'bind'       => [
                        'fund_status'       => InteriorOrderFundStatusEnums::FUND_STATUS_REFUNDING,
                        'goods_type'        => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES,
                        'canceled_at_start' => $last_monday_date . ' 00:00:00',
                        'canceled_at_end'   => $last_sunday_date . ' 23:59:59',
                    ],
                ])->toArray();
                if (!empty($interior_orders)) {
                    //获取网点名称
                    $store_ids  = array_values(array_unique(array_column(array_filter($interior_orders,
                        function ($order) {
                            return $order['staff_store_id'] != enums::HEAD_OFFICE_ID;
                        }), 'staff_store_id')));
                    $store_list = (new StoreRepository())->getStoreListByIds($store_ids);

                    //获取部门
                    $department_ids  = array_values(array_unique(array_column($interior_orders, 'node_department_id')));
                    $department_list = (new DepartmentRepository())->getDepartmentByIds($department_ids);
                    $department_list = array_column($department_list, null, 'id');

                    //获取员工信息
                    $staff_ids  = array_values(array_unique(array_column($interior_orders, 'staff_id')));
                    $staff_list = (new StaffRepository())->getStaffListByStaffIds($staff_ids);

                    //获取员工名下银行信息
                    $staff_bank_type = array_values(array_unique(array_column($staff_list, 'bank_type')));
                    $bank_list       = (new BankListRepository())->getBankListByIds($staff_bank_type);

                    //标题
                    $title_zh = "{$last_monday_date}至{$last_sunday_date}工服待退款数据";
                    $title_en = "Data on work clothes to be refunded from {$last_monday_date} to {$last_sunday_date}";

                    $log .= '订单数据是：' . $title_zh . PHP_EOL;

                    //附件名称组
                    $file_name = [
                        LangEnums::LANG_CODE_ZH_CN => $title_zh . '.xlsx',
                        LangEnums::LANG_CODE_EN    => $title_en . '.xlsx',
                    ];
                    //附件地址
                    $file_path = [];

                    //需要导出中文、泰文两个附件信息
                    foreach ([LangEnums::LANG_CODE_ZH_CN, LangEnums::LANG_CODE_EN] as $lang) {
                        $order_status = InteriorOrderStatusEnums::getCodeTxtMap($lang);    //订单状态
                        $pay_method   = InteriorGoodsPayMethodEnums::payMethod($lang);     //支付方式
                        $fund_status  = InteriorOrderFundStatusEnums::getCodeTxtMap($lang);//款项状态
                        $header       = [
                            LangEnums::getTranslation($lang, 'serial_number'),                //序号
                            LangEnums::getTranslation($lang, 'interior_order_code'),          //订单编号
                            LangEnums::getTranslation($lang, 'interior_out_sn'),              //出库单号
                            LangEnums::getTranslation($lang, 'interior_order_flash_pay_code'),//FlashPay交易号
                            LangEnums::getTranslation($lang, 'interior_order_status'),        //订单状态
                            LangEnums::getTranslation($lang, 'interior_pay_method'),          //支付方式
                            LangEnums::getTranslation($lang, 'interior_order_fund_code'),     //款项状态
                            LangEnums::getTranslation($lang, 'interior_order_pay_amount'),    //订单金额
                            LangEnums::getTranslation($lang, 'interior_staff_id'),            //员工工号
                            LangEnums::getTranslation($lang, 'interior_staff_name'),          //姓名
                            LangEnums::getTranslation($lang, 'interior_store_name'),          //网点名称
                            LangEnums::getTranslation($lang, 'interior_department_name'),     //部门
                            LangEnums::getTranslation($lang, 'interior_submit_time'),         //下单时间
                            LangEnums::getTranslation($lang, 'interior_flash_pay_at'),        //支付日期
                            LangEnums::getTranslation($lang, 'interior_canceled_at'),         //取消订单时间
                            LangEnums::getTranslation($lang, 're_field_bank_type'),           //收款人开户银行
                            LangEnums::getTranslation($lang, 're_field_bank_account'),        //收款人账号
                            LangEnums::getTranslation($lang, 're_field_bank_name'),           //收款人户名
                        ];
                        $excel_data   = [];//excel数据
                        foreach ($interior_orders as $key => $order) {
                            $excel_data[] = [
                                ($key + 1),
                                $order['order_code'],
                                $order['out_sn'],
                                $order['flash_pay_code'],
                                $order_status[$order['order_status']],
                                $pay_method[$order['pay_method']],
                                $fund_status[$order['fund_status']],
                                $order['pay_amount'],
                                $order['staff_id'],
                                $order['staff_name'],
                                ($order['staff_store_id'] == enums::HEAD_OFFICE_ID) ? enums::HEAD_OFFICE : ($store_list[$order['staff_store_id']]['name'] ?? ''),
                                $department_list[$order['node_department_id']]['name'] ?? '',
                                //所属部门
                                $order['submit_at'],
                                $order['flash_pay_at'],
                                $order['canceled_at'],
                                $bank_list[$staff_list[$order['staff_id']]['bank_type'] ?? '']['bank_name'] ?? '(unknown)',
                                //收款人开户银行
                                $staff_list[$order['staff_id']]['bank_no'] ?? '',
                                //收款人账号
                                $staff_list[$order['staff_id']]['name'] ?? '',
                                //收款人户名
                            ];
                        }

                        //上传附件
                        $config = [
                            'path' => sys_get_temp_dir(),
                        ];
                        $excel  = new \Vtiful\Kernel\Excel($config);
                        // 此处会自动创建一个工作表
                        $fileObject  = $excel->fileName($file_name[$lang]);
                        $file_path[] = $fileObject->header($header)->data($excel_data)->output();
                    }

                    //发送邮件
                    $email_arr = explode(',', $emails);
                    $title     = "{$title_zh}({$title_en})";
                    $content   = "<p>附件是{$last_monday_date}至{$last_sunday_date}需要退款的员工商城工服订单，请查收并处理！</p><p>该邮件为系统自动发送，无需回复！</p>";
                    $content   .= "<p>The attachment is the employee mall uniform order that needs to be refunded from {$last_monday_date} to {$last_sunday_date}. Please check and process it!</p><p>This email is automatically sent by the system, no reply is required!</p>";

                    $send_result = Mail::send($email_arr, $title, $content, $file_path, array_values($file_name));
                    if ($send_result) {
                        $log .= '邮件发送成功，收据邮箱：' . $emails . PHP_EOL;
                    } else {
                        $log .= '邮件发送失败，收据邮箱：' . $emails . PHP_EOL;
                    }
                } else {
                    $log .= '暂无待退款数据，无需发送' . PHP_EOL;
                }
            } else {
                $log .= '未配置邮箱，无需发送' . PHP_EOL;
            }
        } catch (\Exception $e) {
            $log .= '发送出现异常，原因可能是：' . $e->getMessage() . PHP_EOL;
        }

        $this->info($log, true);
        exit();
    }

    /**
     * 系统自动处理出库单 - 工服 每小时一次
     *
     * php app/cli.php interior_order auto_audit_outbound
     */
    public function auto_audit_outboundAction()
    {
        $this->checkLock(__METHOD__, 7200);

        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            $orderModels = InteriorOrdersModel::find([
                'conditions' => "order_status = :order_status: AND auto_audit_outbound_status = :auto_audit_outbound_status: AND goods_type = :goods_type: AND out_sn != :out_sn:",
                'bind'       => [
                    'order_status'               => InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                    'auto_audit_outbound_status' => InteriorOrderStatusEnums::ORDER_AUTO_AUDIT_OUTBOUND_STATUS_NO,
                    'goods_type'                 => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES,
                    'out_sn'                     => '',
                ],
            ]);

            $orders = $orderModels->toArray();

            $log .= '待自动审核出库订单共 ' . count($orders) . ' 笔' . PHP_EOL;
            if (!empty($orders)) {
                // 查询员工离职状态
                $staffIds = array_column($orders, 'staff_id');

                // 查询员工离职日期小于当前日期的数据，对比离职日期
                $staffs = HrStaffInfoModel::find([
                    'conditions' => 'staff_info_id in ({staff_ids:array})',
                    'bind'       => ['staff_ids' => $staffIds],
                    'columns'    => ['staff_info_id', 'state', 'wait_leave_state'],
                ])->toArray();
                $staffs = array_column($staffs, null, 'staff_info_id');

                $interiorGoodsServer = new InteriorGoodsServer();

                $successNum    = $errorNum = $staffNullNum = $staffMidNum = $processedNum = 0;
                $staffLeaveNum = $leaveCancelSuccessNum = $leaveCancelErrorNum = 0;

                foreach ($orderModels as $key => $orderObj) {
                    $_log = "待处理订单-{$key}: {$orderObj->staff_id} - {$orderObj->order_code} - {$orderObj->out_sn}, pay_amount={$orderObj->pay_amount}";

                    $orderInfo = InteriorOrdersModel::findFirst([
                        'conditions' => 'order_code = :order_code:',
                        'bind'       => ['order_code' => $orderObj->order_code],
                        'columns'    => ['order_status', 'auto_audit_outbound_status'],
                    ]);
                    if ($orderInfo->order_status != InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE || $orderInfo->auto_audit_outbound_status != InteriorOrderStatusEnums::ORDER_AUTO_AUDIT_OUTBOUND_STATUS_NO) {
                        $processedNum++;
                        $_log .= ", 订单已被处理[当前状态: order_status->{$orderInfo->order_status}, auto_audit_outbound_status->{$orderInfo->auto_audit_outbound_status}], 跳过" . PHP_EOL;
                        $this->info($_log, true);
                        continue;
                    }

                    // 自费工服, 直接审核出库
                    if ($orderObj->pay_amount > 0) {
                        if ($interiorGoodsServer->autoAuditOutboundOrder($orderObj)) {
                            $successNum++;
                            $_log .= ', 成功' . PHP_EOL;
                        } else {
                            $errorNum++;
                            $_log .= ', 失败' . PHP_EOL;
                        }
                        $this->info($_log, true);
                    } else {
                        // 免费工服, 看员工在职状态
                        $staff_info = $staffs[$orderObj->staff_id] ?? [];

                        // 员工表不存在, 不处理
                        if (empty($staff_info)) {
                            $staffNullNum++;
                            $_log .= ', 员工不在员工表, 未处理' . PHP_EOL;
                            $this->info($_log, true);
                            continue;
                        }

                        // 停职 或 待离职 不处理
                        if ($staff_info['state'] == HrStaffInfoModel::STATE_3 || ($staff_info['state'] == HrStaffInfoModel::STATE_1 && $staff_info['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE)) {
                            $staffMidNum++;
                            $_log .= ", 员工停职或待离职[{$staff_info['state']}-{$staff_info['wait_leave_state']}], 未处理" . PHP_EOL;
                            $this->info($_log, true);
                            continue;
                        }

                        // 离职 取消出库单, 取消员工订单
                        if ($staff_info['state'] == HrStaffInfoModel::STATE_2) {
                            $staffLeaveNum++;
                            $_log .= '员工已离职, ';

                            $loginUser = [
                                'staff_id' => $orderObj->staff_id,
                            ];
                            $params    = [
                                'order_code'    => $orderObj->order_code,
                                'cancel_reason' => InteriorOrderStatusEnums::ORDER_CANCEL_REASON_PRE_SALE,
                            ];
                            $res       = $interiorGoodsServer->cancelOrder($loginUser, $params,
                                'auto_audit_outbound_staff_leave_canceled');
                            if ($res === true) {
                                $leaveCancelSuccessNum++;
                                $_log .= '取消SCM出库单成功, 订单取消状态标记成功' . PHP_EOL;
                            } else {
                                $leaveCancelErrorNum++;
                                $_log .= "取消SCM出库单异常[msg-{$res['msg']}], 订单状态未处理" . PHP_EOL;
                            }

                            $this->info($_log, true);
                            continue;
                        }

                        // 在职, 自动审核出库单
                        if ($interiorGoodsServer->autoAuditOutboundOrder($orderObj)) {
                            $successNum++;
                            $_log .= ', 成功' . PHP_EOL;
                        } else {
                            $errorNum++;
                            $_log .= ', 失败' . PHP_EOL;
                        }

                        $this->info($_log, true);
                    }

                    sleep(1);
                }

                $log .= "执行完毕: 标记成功 {$successNum} 个, 标记失败 {$errorNum} 个; 被其他脚本处理的 {$processedNum} 个(本次跳过未处理的); 员工信息不在员工表的 {$staffNullNum} 个, 员工停职/待离职的 {$staffMidNum} 个; ";
                $log .= "员工离职的 {$staffLeaveNum} 个 (取消成功 {$leaveCancelSuccessNum} 个, 取消失败 {$leaveCancelErrorNum} 个)" . PHP_EOL;
            } else {
                $log .= '无待自动审核的出库' . PHP_EOL;
            }

        } catch (\Exception $e) {
            $log .= '脚本异常，原因可能是：' . $e->getMessage() . PHP_EOL;
        }

        $this->clearLock(__METHOD__);

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->info($log, true);
        exit();
    }

}
