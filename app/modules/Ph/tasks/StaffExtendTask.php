<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2/14/22
 * Time: 4:53 PM
 */



namespace FlashExpress\bi\App\Modules\Ph\Tasks;


use App\Country\Tools;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoExtendMode;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveExtendModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\StatisticForOvertimeModel;
use FlashExpress\bi\App\Models\backyard\AttendanceDataV2Model;
use FlashExpress\bi\App\Models\bi\DeliveryAvgCountModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\bi\StorePickupDataModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Modules\Ph\Server\AuditServer;
use FlashExpress\bi\App\Modules\Ph\Server\LeaveServer;
use FlashExpress\bi\App\Modules\Ph\Server\Vacation\AnnualServer;
use FlashExpress\bi\App\Modules\Ph\Server\Vacation\SingleParentServer;
use FlashExpress\bi\App\Repository\AuditRepository;

use FlashExpress\bi\App\Models\backyard\StaffDaysFreezeModel;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\VacationServer;
use StaffExtendTask as GlobalTask;

class StaffExtendTask extends GlobalTask {

    public $model;
    public $auditModel;


    //每天发放 年假额度 任务
    public function freeze_by_levelAction($param)
    {
        //任务锁
        $key   = 'freeze_by_level_lock';
        $redis = $this->getDI()->get("redisLib");
        $rs    = $redis->set($key, 1, ['nx', 'ex' => 10 * 60]);//锁10分钟
        if (RUNTIME != 'dev' && !$rs) {
            die('task is running');
        }
        $today = date('Y-m-d');

        if (!empty($param[0])) {
            $today = $param[0];
        }

        if (!empty($param[1])) {
            $staffParam['staff_info_id'] = $param[1];
        }

        try {

            $staffParam['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_1];
            $staffParam['state'] = [HrStaffInfoModel::STATE_1,HrStaffInfoModel::STATE_3];
            $staff_list = $this->annualStaffList($today, $staffParam);

            if (empty($staff_list)) {
                return true;
            }

            $staff_list = array_column($staff_list, null, 'staff_info_id');

            $dep_model = new DepartmentRepository($this->lang);
            $c_staffs  = $dep_model->get_c_level();

            //新旧规则 交接节点日期
            $setting_model  = new BySettingRepository();
            $point_date = $setting_model->get_setting('point_date');

            $annualServer = new AnnualServer($this->lang, $this->timezone);
            $leaveServer = new LeaveServer($this->lang, $this->timezone);
            foreach ($staff_list as $staff_id => $staff_info) {
                if (empty($staff_id)) {
                    continue;
                }
                $flag = $leaveServer->leavePermission($staff_info);
                if(!$flag){
                    echo $staff_info['staff_info_id'].' 非工作所在国家 不发年假';
                    continue;
                }

                //获取当前周期的额度信息
                $cycle_info = $annualServer->get_cycle($staff_info);
                if (empty($cycle_info)) {
                    echo $staff_info['staff_info_id'].'没有入职日期';
                    continue;
                }

                //新增 计算年假日期逻辑 https://flashexpress.feishu.cn/docx/USK1dOhAiod8STxnC0ccJqacn4d
                if(!empty($staff_info['annual_date'])){
                    //清空日期 不累计年假
                    if($staff_info['annual_date'] == HrStaffInfoExtendMode::ANNUAL_STOP_DATE){
                        echo 'base 清空日期 不计算年假 ' . "{$staff_info['staff_info_id']} ". $staff_info['annual_date'] ;
                        continue;
                    }
                    //日期在当天之后 不累计
                    if($staff_info['annual_date'] > $today){
                        echo 'base 没到计算日期 不计算年假 ' . "{$staff_info['staff_info_id']} ". $staff_info['annual_date'];
                        continue;
                    }
                }

                //下个周期
                $this->getDI()->get('db')->begin();
                $nextCycle = $cycle_info['cycle'] + 1;

                $current_remain = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year = :cycle: and leave_type = :leave_type:',
                    'bind'       => [
                        'staff_id'   => $staff_id,
                        'cycle'      => $cycle_info['cycle'],
                        'leave_type' => enums::LEAVE_TYPE_1,
                    ],
                ]);

                $isNewHire = false;//是否 新入职员工 true 新员工, false 非新员工
                //有可能是新入职员工
                if (empty($current_remain) || $current_remain->freeze_days == 0) {
                    $isContinue = true;//需要继续往下走的 用下面的add day 包括新入职和迁移账号
                    if ($cycle_info['cycle'] == 1 && empty($staff_info['annual_date'])) {
                        $isNewHire      = true;//由于 入职日期 不准确是昨天 所以用这个判断
                    } else {
                        $this->getDI()->get('logger')->write_log("freeze_by_level {$staff_info['staff_info_id']} 当前周期额度异常 没有当前周期额度信息 初始化1天额度", 'info');
                    }
                    $current_remain = $annualServer->initAnnual($staff_info,$cycle_info['cycle'],['task_date'=>$today]);
                }

                //任务判定是否跑过 每天只能跑一次
                if (!empty($current_remain) && $current_remain->task_date >= $today && empty($isContinue)) {
                    $this->getDI()->get('db')->commit();
                    continue;
                }

                //新需求 用历史最高职级 不用实时的
                $ext_info = StaffLeaveExtendModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year_at = :cycle:',
                    'bind'       => [
                        'staff_id'   => $staff_info['staff_info_id'],
                        'leave_type' => enums::LEAVE_TYPE_1,
                        'cycle'      => $cycle_info['cycle'],
                    ],
                ]);
                if (empty($ext_info)) {
                    $this->getDI()->get('db')->rollback();
                    $this->getDI()->get('logger')->write_log("freeze_by_level {$staff_info['staff_info_id']} 当前周期额度扩展表 异常");
                    continue;
                }

                //与当前职等比较 有可能刚升职
                if ($staff_info['job_title_grade_v2'] > $ext_info->job_title_grade) {
                    $ext_info->job_title_grade = $staff_info['job_title_grade_v2'];
                    $ext_info->update();
                }
                //降职情况 取历史最高 替换当前的 v2
                $staff_info['job_title_grade_v2'] = max($ext_info->job_title_grade, $staff_info['job_title_grade_v2']);

                if (in_array($staff_info['staff_info_id'], $c_staffs)) {
                    //c level 固定 20天
                    $should_day = enums::C_LEVEL_DAYS;
                } else {//正常员工
                    $should_day = $this->getGradeDaysNormal($staff_info);
                }

                //没有自然年概念 都是 365
                $add_day = round($should_day / 365, enums::ROUND_NUM);
                if ($isNewHire) {//新入职员工 第一次递增额度 * 2 因为入职当天 任务没跑这个人
                    $add_day += $add_day;
                }

                $current_remain->freeze_days += $add_day;
                $current_remain->days        += $add_day;//加一天额度
                $cycle_last_date             = date('Y-m-d', strtotime("{$cycle_info['count_day']} -1 day"));
                //当前周期 开始时间
                $this_cycle_start = date('Y-m-d',strtotime("{$cycle_info['count_day']} -1 year"));
                if ($today == $cycle_last_date) {//当天是 当前周期 最后一天 加一天额度 然后初始化 下一年记录 并且当年额度 +1天
                    if ($this_cycle_start < $point_date){
                        //非完整周期的不满0.5的结余要加到下个周期里；
                        $freezeDays = round($current_remain->freeze_days - half_num($current_remain->freeze_days),enums::ROUND_NUM);
                    }else{
                        $freezeDays = 0;
                    }
                    //特殊逻辑 在2022 最后一天之前 要把超额的 顺延到下个周期
                    if ($current_remain->days < 0) {
                        $changeDays = half_num($current_remain->days);//-1
                        $this->currentCycleFlag($staff_info, abs($changeDays), $cycle_info['cycle'], $nextCycle);
                        $current_remain->days = 0;
                        $current_remain->leave_days += $changeDays;
                        $ext_info->left_all_days = $changeDays;
                        $ext_info->update();
                    }else{
                        $changeDays = 0;
                    }
                    $annualServer->initAnnual($staff_info,$nextCycle,['freeze_days'=>$freezeDays,'days'=>$freezeDays+$changeDays,'leave_days'=>abs($changeDays),'task_date'=>$today]);
                    //c级别 和 19天的 由于5位小数点 四舍五入 舍去了 需要加一天额度
                    if(in_array($should_day, [19,20])){
                        $current_remain->days += $add_day;
                    }
                }

                $current_remain->task_date = $today;
                $current_remain->update();

                $this->getDI()->get('db')->commit();
                $this->logger->write_log("freeze_by_level_{$staff_info['staff_info_id']}_{$cycle_info['cycle']} {$today} 增加额度 {$add_day} ",'info');
            }

            $redis->delete($key);
            $this->logger->write_log("freeze_by_level {$today} 年假固化任务 跑完了 ",'notice');

        } catch (\Exception $e) {
            $this->logger->write_log("freeze_by_level {$today} 任务数据失败 ".$e->getTraceAsString());
            $this->getDI()->get('db')->rollback();
            die('freeze_by_level 任务异常 '.$e->getTraceAsString());
        }
    }



    //新版 初始化任务 https://flashexpress.feishu.cn/docx/Dtg4dGExdoXvdixQfWSct8a3nzb
    public function freeze_firstAction($param)
    {
        ini_set('memory_limit', '-1');
        try {
            $staff_id = '';
            if (!empty($param[0])) {
                $staff_id = $param[0];
            }
            //任务日期
            $today         = date('Y-m-d');
            $staff_list = $this->annualStaffList($today,$staff_id);
            if (empty($staff_list)) {
                die('没有员工数据');
            }

            //特殊情况 c level
            $dep_model = new DepartmentRepository($this->lang);
            $c_staffs  = $dep_model->get_c_level();


            //员工 旧规则 freeze 表保存的 应发放额度
            $freezeData = StaffDaysFreezeModel::find([
                'conditions' => 'staff_info_id in ({staff_ids:array})',
                'bind' => [
                    'staff_ids' => array_column($staff_list,'staff_info_id'),
                ]
            ])->toArray();
            //22年 固化的 应有额度
            $freezeData = empty($freezeData) ? [] : array_column($freezeData,'days','staff_info_id');

            $annualServer = new AnnualServer($this->lang, $this->timezone);
            $remainModel  = new StaffLeaveRemainDaysModel();
            $extModel     = new StaffLeaveExtendModel();
            $leaveServer = new LeaveServer($this->lang,$this->timezone);
            foreach ($staff_list as $staff_info) {
                $staff_id  = $staff_info['staff_info_id'];
                $cycleInfo = $annualServer->get_cycle($staff_info);

                //最高职级 ext 记录用 计算时候用当前的 目前业务还没定 是分段算还是 用当前 最后定了 用历史最高职等
                $staff_info['job_title_grade_v2'] = $this->getHighestGrade($staff_info);

                //freeze days 额度 作为总数
                $should_days = $freezeData[$staff_id] ?? 0;
                //额外加上 满周年额度
                $yearAdd      = $leaveServer->over_one_year_days($staff_info);//满周年额度
                $levelDays = $leaveServer->get_year_leave_days($staff_info);
                //一般员工的上限天数
                $max = $leaveServer->get_year_leave_max_days($staff_info);
                $maxDays = min($max,bcadd($yearAdd,$levelDays));
                $yearAdd = bcsub($maxDays,$levelDays);

                $should_days += $yearAdd;
                //小C人 20天
                if(in_array($staff_id,$c_staffs)){
                    $should_days = enums::C_LEVEL_DAYS;
                }

                $daysFor2022 = half_num($should_days);
                //22年 计算完的 余数
                $daysForCycle = bcsub($should_days, $daysFor2022, enums::ROUND_NUM);

                //22年 使用年假天数
                $usedDaysFor2022 = $annualServer->leaveDaysByDate($staff_id, 2022);
                $leftFor2022     = bcsub($daysFor2022, $usedDaysFor2022, 1);
                //当前周期 最后一天
                $cycle_last_date = date('Y-m-d', strtotime("{$cycleInfo['count_day']} -1 day"));

                $this->getDI()->get('db')->begin();
                $usedForCycle = 0;
                if ($leftFor2022 < 0) {//小于0  说明 请超额了 泰国 预发放导致
                    $usedForCycle = $leftFor2022;//超额 负数要用 新逻辑 当前周期的 填平
                    //如果 正好最后一天了 直接 挪到下周期 判断周期是当前还是下周期
                    $cycle = ($today == $cycle_last_date) ? ($cycleInfo['cycle'] + 1) : $cycleInfo['cycle'];
                    //2022 超额的 找出来 标记成档期当前周期
                    $this->currentCycleFlag($staff_info, abs($leftFor2022), 2022, $cycle);
                }

                //初始化 22年 remain
                $row['staff_info_id'] = $staff_id;
                $row['leave_type']    = enums::LEAVE_TYPE_1;
                $row['year']          = 2022;
                $row['task_date']     = $today;
                $row['freeze_days']   = $should_days;
                $row['days']          = $leftFor2022 < 0 ? 0 : $leftFor2022;//剩余额度
                $row['leave_days']    = $leftFor2022 < 0 ? $daysFor2022 : $usedDaysFor2022;//使用额度

                $cloneOld = clone $remainModel;
                $cloneOld->create($row);

                //初始化 22年的 ext
                $ext['staff_info_id']   = $staff_id;
                $ext['leave_type']      = enums::LEAVE_TYPE_1;
                $ext['year_at']         = 2022;
                $ext['left_all_days']   = $leftFor2022;//22年 剩余额度
                $ext['job_title_level'] = $staff_info['job_title_level'];
                $ext['job_title_grade'] = $staff_info['job_title_grade_v2'];
                //入库 ext表
                $extendOld = clone $extModel;
                $extendOld->create($ext);


                //特殊情况 初始化 当天正好是 当前周期最后一天 要初始化下个周期数据 并且把 超额的额度 放到下周起继续 持平
                if ($today == $cycle_last_date) {
                    //下个周期 remain
                    $remain['staff_info_id'] = $staff_id;
                    $remain['leave_type']    = enums::LEAVE_TYPE_1;
                    $remain['year']          = $cycleInfo['cycle'] + 1;
                    $remain['task_date']     = $today;
                    $remain['freeze_days']   = $daysForCycle;//也要结余过来
                    $remain['days']          = $usedForCycle;//22年超额的 -》到上周期 -》 到这周期
                    $remain['leave_days']    = abs($usedForCycle);

                    //下个周期的 ext
                    $extRow['staff_info_id']   = $staff_id;
                    $extRow['leave_type']      = enums::LEAVE_TYPE_1;
                    $extRow['year_at']         = $cycleInfo['cycle'] + 1;
                    $extRow['left_all_days']   = $usedForCycle;//记录上周期剩余 有可能是负数 以后不会变
                    $extRow['job_title_level'] = $staff_info['job_title_level'];
                    $extRow['job_title_grade'] = $staff_info['job_title_grade_v2'];
                } else {//初始化当天 不是 最后一天 正常用当前周期 初始化
                    //初始化一条 当前周期的 额度 把小数 放进去
                    $remain['staff_info_id'] = $staff_id;
                    $remain['leave_type']    = enums::LEAVE_TYPE_1;
                    $remain['year']          = $cycleInfo['cycle'];
                    $remain['task_date']     = $today;
                    $remain['freeze_days']   = $daysForCycle;
                    $remain['days']          = bcadd($daysForCycle, $usedForCycle, enums::ROUND_NUM);//加上超额的 持平
                    $remain['leave_days']    = abs($usedForCycle);//超额使用22年的的天数 放到 当前周期 没超额 就是0

                    //初始化 extend
                    $extRow['staff_info_id']   = $staff_id;
                    $extRow['leave_type']      = enums::LEAVE_TYPE_1;
                    $extRow['year_at']         = $cycleInfo['cycle'];
                    $extRow['left_all_days']   = $leftFor2022;
                    $extRow['job_title_level'] = $staff_info['job_title_level'];
                    $extRow['job_title_grade'] = $staff_info['job_title_grade_v2'];
                }
                //入库 remain
                $cloneNew = clone $remainModel;
                $cloneNew->create($remain);
                //入库 ext表
                $extendClone = clone $extModel;
                $extendClone->create($extRow);
                $this->getDI()->get('db')->commit();
            }

        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            die('初始化额度报错 '.$e->getMessage().'-------'.$e->getTraceAsString());
        }
    }

    /**
     * 获取员工 应有的总额度 分母
     * @param $staff_info
     * @return array|int|mixed
     */
    public function getGradeDaysNormal($staff_info)
    {
        if (empty($staff_info)) {
            return 0;
        }

        $annualServer = new AnnualServer($this->lang, $this->timezone);
        $shouldDays   = $annualServer->getShouldDays($staff_info);//职等额度
        $yearAdd      = $annualServer->overOneYear($staff_info);//满周年额度

        $maxDay = $annualServer->getMaxDays($staff_info);//最高额度 不能超过多少天
        return ($shouldDays + $yearAdd) > $maxDay ? $maxDay : ($shouldDays + $yearAdd);
    }



    //从fbi 固化 揽派件 数量 时间跟泰国一样 
    public function overtime_statisticAction($param){
        //上周所有网点总揽件量+上周所有网点总派件量   delivery_avg_count  store_pickup_data_emr
        $yesterday = date("Y-m-d",strtotime("-1 day"));
        if(!empty($param[0]))
            $yesterday = date('Y-m-d',strtotime($param[0]));//指定日期

        try{
            $stores = SysStoreModel::find([
                'columns' => 'id',
                'conditions' => 'state = 1'
            ])->toArray();

            //主要维度数据 网点 为空 不继续往下跑了
            if(empty($stores)){
                $this->getDI()->get('logger')->write_log("overtime_statistic_{$yesterday} not store data",'info');
                die('not store data');
            }

            $all_store = array_column($stores,'id');
            unset($stores);

            $pickup = StorePickupDataModel::find([
                'columns' => 'store_id,count_total',
                'conditions' => 'stat_date = :yesterday: and store_id in ({stores:array})',
                'bind' => ['yesterday' => $yesterday,'stores' => $all_store],
            ])->toArray();

            if(!empty($pickup)){
                $pickup = array_column($pickup,'count_total', 'store_id');
            }


            $delivery = DeliveryAvgCountModel::find([
                'columns' => 'store_id,delivery_count',
                'conditions' => 'created_at = :yesterday: and store_id in ({stores:array})',
                'bind' => ['yesterday' => $yesterday,'stores' => $all_store],
            ])->toArray();


            if(!empty($delivery)){
                $delivery = array_column($delivery,'delivery_count', 'store_id');
            }

            $model = new StatisticForOvertimeModel();
            //组织数据
            foreach ($all_store as $store){
                $row['store_id'] = $store;
                $row['date_at'] = $yesterday;
                $row['delivery_num'] = empty($delivery[$store]) ? 0 : $delivery[$store];
                $row['pickup_num'] = empty($pickup[$store]) ? 0 : $pickup[$store];
                $row['attendance_time'] = 0;
                $row['attendance_time_for_37'] = 0;
                $row['number_for_37'] = 0;
                $row['flag'] = 0;

                $clone = clone $model;
                $clone->save($row);
            }

            echo '跑完了 ' . $yesterday;

        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('overtime_statistic_固化加班申请数据比例失败 '.$yesterday);
        }

    }


    //菲律宾 ph  25 =》 24 修跳过休息日的假期数据 https://flashexpress.feishu.cn/docx/D7ICdHwXso2sajx2NPAcnTH5nLf
    public function fixLeaveAction()
    {
        //三种情况
        /**
         *
         * -- 24号 截止日期的人   131
         * select staff_info_id,leave_type
         * from staff_audit where
         * audit_type  = 2
         * and leave_type in (1,2,3,7,10,16,5,19,20,22,23,25,26)
         * and status  in (1,2)
         * and created_at  <= '2023-02-23 11:02:33'
         * and date( leave_end_time)  = '2023-02-24'
         * -- 35
         *
         * and date( leave_start_time)  = '2023-02-25'
         *
         * -- 96
         * and  date( leave_start_time) <= '2023-02-24'  and date( leave_end_time)  >= '2023-02-25'
         */


        $date_24 = '2023-02-24';
        $date_25 = '2023-02-25';

        // 262 条数据
        $data = StaffAuditModel::find([
            'columns'    => 'audit_id,staff_info_id,leave_type,date(leave_start_time) leave_start_time,date(leave_end_time) leave_end_time,leave_start_type,leave_end_type',
            'conditions' => "leave_type in (1,2,3,7,10,16,5,19,20,22,23,25,26) and status  in (1,2) and created_at  <= '2023-02-23 11:02:33'
            and 
            (date(leave_end_time)  = '{$date_24}' or date(leave_start_time)  = '{$date_25}' or (date(leave_start_time) <= '{$date_24}'  and date(leave_end_time)  >= '{$date_25}'))
            ",
        ])->toArray();


        $cancel_rows = $other_rows = [];
        foreach ($data as $da) {
            //撤销重新申请的
            if ($da['leave_end_time'] == $date_24) {
                $cancel_rows[] = $da;
            }

            //包含 24，25的 排除调一次申请类型
            if (!in_array($da['leave_type'],
                    [5, 10, 20]) && $da['leave_start_time'] <= $date_24 && $da['leave_end_time'] >= $date_25) {
                $other_rows[] = $da;
            }
        }

        //先取消 重新申请
        $server      = new AuditServer($this->lang, $this->timezone);
        $leaveServer = new LeaveServer($this->lang, $this->timezone);
        $insert      = [];
        foreach ($cancel_rows as $info) {
            try {
                $param  = [
                    'staff_id'      => $info['staff_info_id'],
                    'audit_id'      => $info["audit_id"],
                    'reject_reason' => 'system task cancel by ph_fix_task',
                    'status'        => enums::$audit_status['revoked'],
                    'is_bi'         => 1,
                ];
                $flag_1 = $server->auditEditStatus($param);

                //如果 就是开始结束 都是24 就不用重新申请了
                if ($info['leave_start_time'] == $info['leave_end_time']) {
                    continue;
                }

                //重新申请
                $addParam['staff_id']   = $info['staff_info_id'];
                $addParam['leave_type'] = $info['leave_type'];;
                $addParam['leave_start_time'] = $info['leave_start_time'];
                $addParam['leave_start_type'] = $info['leave_start_type'];
                $addParam['leave_end_time']   = $info['leave_end_time'];
                $addParam['leave_end_type']   = $info['leave_end_type'];
                $addParam['audit_reason']     = 'system task';
                $addParam['is_bi']            = 1;

                $insert[] = $addParam;

                $this->getDI()->get('logger')->write_log("fixLeaveAction {$info['staff_info_id']} {$info["audit_id"]} 撤销重新申请 1 ".json_encode($flag_1),
                    'info');
            } catch (ValidationException $ve) {
                echo $ve->getMessage();
                $this->getDI()->get('logger')->write_log("fixLeaveAction {$info['staff_info_id']} {$info["audit_id"]} 撤销重新申请提示 1 ".$ve->getMessage());
                continue;
            } catch (\Exception $e) {
                echo $e->getTraceAsString();
                $this->getDI()->get('logger')->write_log("fixLeaveAction {$info['staff_info_id']} {$info["audit_id"]} 撤销重新申请异常 1 ".$e->getMessage());
                continue;
            }
        }


        //撤销 重新申请的
        if (!empty($insert)) {
            foreach ($insert as $in) {
                try {
                    if ($in['leave_type'] == enums::LEAVE_TYPE_1) {
                        $flag_2 = $leaveServer->saveVacation($in);
                    } else {
                        $flag_2 = $server->leaveAdd($in);
                    }
                    $this->getDI()->get('logger')->write_log("fixLeaveAction {$in['staff_id']} 撤销重新申请 2 ".json_encode($flag_2),
                        'info');
                } catch (ValidationException $ve) {
                    echo $ve->getMessage();
                    $this->getDI()->get('logger')->write_log("fixLeaveAction {$in['staff_id']} 撤销重新申请提示 2 ".$ve->getMessage());
                    continue;
                } catch (\Exception $e) {
                    echo $e->getTraceAsString();
                    $this->getDI()->get('logger')->write_log("fixLeaveAction {$in['staff_id']} 撤销重新申请异常 2 ".$e->getMessage());
                    continue;
                }
            }
        }

        //获取这些人 25号 是不是 全天ab
        $staffs = array_values(array_column($other_rows, 'staff_info_id'));
        $is_ab  = AttendanceDataV2Model::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'staff_info_id in ({ids:array}) and stat_date = :date_at: and AB = 10',
            'bind'       => ['ids' => $staffs, 'date_at' => $date_25],
        ])->toArray();

        $ab_staff = array_column($is_ab, 'staff_info_id');

        foreach ($other_rows as $audit) {
            $staff_id = $audit['staff_info_id'];
            $audit_id = $audit['audit_id'];
            //要挪动24 到25 的 条件是25 有ab的
            if (in_array($staff_id, $ab_staff)) {
                StaffAuditLeaveSplitModel::find([
                    'conditions' => 'audit_id = :audit_id: and date_at = :date_24:',
                    'bind'       => ['audit_id' => $audit_id, 'date_24' => $date_24],
                ])->update(['date_at' => $date_25]);
                continue;
            }


            //要删除24 split 并且返还1天 额度的  1 7 10 5 16 19 20
            //找出 24号 对应的额度  年假有可能是两个半天
            $split = StaffAuditLeaveSplitModel::find([
                'conditions' => 'audit_id = :audit_id: and date_at = :date_at:',
                'bind'       => ['audit_id' => $audit_id, 'date_at' => $date_24],
            ]);

            if (!empty($split->toArray())) {
                //查数据 只有年假会有两条记录
                foreach ($split as $sp) {
                    //一次性假期 不需要年的条件 直接增加1天额度  7 16
                    if (in_array($audit['leave_type'], $server->one_send)) {
                        $remainInfo = StaffLeaveRemainDaysModel::findFirst([
                            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: ',
                            'bind'       => [
                                'staff_id'   => $staff_id,
                                'leave_type' => $audit['leave_type'],
                            ],
                        ]);
                        if (empty($remainInfo)) {
                            continue;
                        }
                        $addDays                = $sp->type > 0 ? 0.5 : 1;//返还一天还是半天额度
                        $remainInfo->leave_days = $remainInfo->leave_days - $addDays;
                        $remainInfo->days       = $remainInfo->days + $addDays;
                        $remainInfo->update();
                    } else {//1  19 查数据 也没有19  就是1 年假了
                        $remainInfo = StaffLeaveRemainDaysModel::findFirst([
                            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
                            'bind'       => [
                                'staff_id'   => $staff_id,
                                'leave_type' => $audit['leave_type'],
                                'year_at'    => $sp->year_at,
                            ],
                        ]);
                        if (empty($remainInfo)) {
                            continue;
                        }
                        $addDays                = $sp->type > 0 ? 0.5 : 1;
                        $remainInfo->leave_days = $remainInfo->leave_days - $addDays;
                        $remainInfo->days       = $remainInfo->days + $addDays;
                        $remainInfo->update();
                    }
                }

                $this->getDI()->get('logger')->write_log("fixLeaveAction {$staff_id} {$audit_id} {$date_24} split 删除了 ",
                    'info');
                $split->delete();
            }
        }
    }


    //原 一次性假期 单亲育儿 20  改为 按年发放每年7天 刷数据 刷今年和 去年的数据
    public function fixSingleAction(){
        //删除 目前数据库 育儿假记录 25条记录 不用备份
        StaffLeaveRemainDaysModel::find([
            'conditions' => 'leave_type = :leave_type:',
            'bind'       => [
                'leave_type' => enums::LEAVE_TYPE_20,
            ],
        ])->delete();

        //正式员工数据
        $staffData = HrStaffInfoModel::find([
            'columns' => 'staff_info_id',
            'conditions' => 'state in (1,3) and formal = 1 and hire_type = 1 and is_sub_staff = 0',
        ])->toArray();

        $this->auditModel = new AuditRepository($this->lang);
        $this->model = new StaffLeaveRemainDaysModel();
        foreach ($staffData as $staff){
            $this->countSingleLeave($staff,2022);
            $this->countSingleLeave($staff,2023);
        }

        echo '跑完了';

    }

    protected function countSingleLeave($staff,$year){
        $useInfo = $this->auditModel->get_used_leave_days($staff['staff_info_id'], $year, '20');
        if (!empty($useInfo) && !empty($useInfo[0]['num'])) {
            $use = $useInfo[0]['num'];
        }
        $use = $use ?? 0;
        $row['staff_info_id'] = $staff['staff_info_id'];
        $row['leave_type']    = enums::LEAVE_TYPE_20;
        $row['year']          = $year;
        $row['freeze_days']   = enums::SINGLE_DAYS_UN;
        $row['days']          = max(enums::SINGLE_DAYS_UN - $use, 0);//剩余
        $row['leave_days']    = $use;//已使用

        $clone = clone $this->model;
        $clone->create($row);
        $this->logger->write_log("initSingleLeave {$staff['staff_info_id']} {$year} 完成",'info');
    }


    public function initSickAction($param){
        //取 正式
        $condition = " state in (1,3) and formal = 1 and hire_type = 1 and is_sub_staff = 0";
        if(!empty($param[1])){
            $condition .= " and staff_info_id = " . $param[1];
        }
        $staff_list = HrStaffInfoModel::find(
            [
                'conditions' => $condition,
            ]
        )->toArray();
        $year = $param[0] ?? date('Y');
        $model = new StaffLeaveRemainDaysModel();
        $server = new VacationServer($this->lang,$this->timezone);
        foreach ($staff_list as $staff){
            //如果 存在记录 不操作
            $remainInfo = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
                'bind'       => [
                    'staff_id'   => $staff['staff_info_id'],
                    'leave_type' => enums::LEAVE_TYPE_3,
                    'year_at'    => $year,
                ],
            ]);
            if (!empty($remainInfo)) {
                continue;
            }
            //获取对应年已经申请的病假
            $start       = $year.'-01-01';
            $end         = $year.'-12-31';
            $appliedDays = $server->leaveDaysBetween($staff['staff_info_id'], $start, $end, enums::LEAVE_TYPE_3);

            //给10天
            $row['staff_info_id'] = $staff['staff_info_id'];
            $row['leave_type'] = enums::LEAVE_TYPE_3;
            $row['year'] = $year;
            $row['task_date'] = date('Y-m-d');
            $row['freeze_days'] = 10;
            $row['days'] =  bcsub(10, $appliedDays,2);
            $row['leave_days']    = $appliedDays;

            $c = clone $model;
            $c->create($row);
        }

        echo '跑完了';
    }


}
