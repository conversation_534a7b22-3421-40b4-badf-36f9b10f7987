<?php
// CEO 信箱 2.0版本 2020.11

namespace  FlashExpress\bi\App\Modules\Ph\library\Enums;

use FlashExpress\bi\App\Enums\CeoMailEnums as BaseCeoMailEnums;

class CeoMailEnums extends BaseCeoMailEnums
{
    // 需要阅读信息承诺书的分类 = 针对大类(子类继承大类该属性) Information Commitment
    const ABOUT_COMPLAINT_CATEGORY_ID = 11; // 关于投诉
    const MUST_READ_INFORMATION_COMMITMENT_CATEGORY_IDS = [
        self::ABOUT_COMPLAINT_CATEGORY_ID
    ];

    const ABOUT_COMPENSATION_CATEGORY_ID = 115;//关于薪酬

    const ABOUT_OTHER_SYS_CATEGORY_ID = 212;//其他系统问题

    const ABOUT_BANK_CATEGORY_ID = 1;//关于银行账户/工牌

    const ABOUT_EXPRESS_SYS_OTHER_CATEGORY_ID = 206;//关于快递系统-其他

    const ABOUT_INCENTIVE_ID = 146;// 关于提成
}
