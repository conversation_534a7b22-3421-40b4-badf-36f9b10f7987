<?php


namespace FlashExpress\bi\App\Modules\Ph\library\Enums;


final class HrJobTitleEnums
{
    const BIKE_COURIER = 13; //Bike Courier
    const VAN_COURIER = 110; //Van Courier
    const TRICYCLE_COURIER = 1000; //Tricycle Courier
    const TRUCK_DRIVER_DAY = 1194; //Truck Driver (Day)
    const DC_OFFICER = 37; //DC Officer
    const BRANCH_SUPERVISOR = 16; //Branch Supervisor
    const ASSISTANT_DC_SUPERVISOR = 1553; //Assistant DC Supervisor

    //加班指定职位
    public static $limit_ot_job_title = [
        self::DC_OFFICER,
        self::BRANCH_SUPERVISOR,
        self::ASSISTANT_DC_SUPERVISOR,
    ];
}