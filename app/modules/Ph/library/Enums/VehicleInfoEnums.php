<?php
// 车辆信息管理 2.0版本 2021.03

namespace  FlashExpress\bi\App\Modules\Ph\library\Enums;

class VehicleInfoEnums extends \FlashExpress\bi\App\Enums\VehicleInfoEnums
{


    // 驾照类型
    const DRIVER_LICENSE_TYPE_001 = 1;//1. Professional License

    const DRIVER_LICENSE_TYPE_ITEM = [
        self::DRIVER_LICENSE_TYPE_001 => 'Professional License',
    ];

    //驾照车辆限制
    const DRIVING_LICENSE_VEHICLE_RESTRICTIONS = [
        '1'=>['value'=>'1','label'=>'Motorcycles / Motorized tricycles'],
        '2'=>['value'=>'2','label'=>'Vehicle up to 4500 kgs G V W'],
        '3'=>['value'=>'3','label'=>'Vehicle above 4500 kgs G V W'],
        '4'=>['value'=>'4','label'=>'Automatic clutch up to 4500 G V W'],
        '5'=>['value'=>'5','label'=>'Automatic clutch above 4500 G V W'],
        '6'=>['value'=>'6','label'=>'Articulated vehicle 1600 kgs G V W and below'],
        '7'=>['value'=>'7','label'=>'Articulated vehicle 1601 up to 4500 G V W'],
        '8'=>['value'=>'8','label'=>'Articulated vehicle 4501 & above G V W'],
    ];

    const VEHICLE_TYPE_ITEM = [
        self::VEHICLE_TYPE_BIKE_CODE => self::VEHICLE_TYPE_BIKE_TEXT,
        self::VEHICLE_TYPE_VAN_CODE => self::VEHICLE_TYPE_VAN_TEXT,
        self::VEHICLE_TYPE_TRICYCLE_CODE => self::VEHICLE_TYPE_TRICYCLE_TEXT,
        self::VEHICLE_TYPE_TRUCK_CODE => self::VEHICLE_TYPE_TRUCK_TEXT,

    ];

    // 职位与车辆类型
    const JOB_VEHICLE_TYPE_REL_CODE = [
        self::JOB_VAN_TITLE_ID => self::VEHICLE_TYPE_VAN_CODE,
        self::JOB_VAN_PROJECT_TITLE_ID => self::VEHICLE_TYPE_VAN_CODE,
        self::JOB_BIKE_TITLE_ID => self::VEHICLE_TYPE_BIKE_CODE,
        self::JOB_TRICYCLE_TITLE_ID=>self::VEHICLE_TYPE_TRICYCLE_CODE,
        self::JOB_TRUCK_TITLE_ID=>self::VEHICLE_TYPE_TRUCK_CODE,
    ];

    // 职位与车辆类型
    const JOB_VEHICLE_TYPE_REL_TEXT = [
        self::JOB_VAN_TITLE_ID => self::VEHICLE_TYPE_VAN_TEXT,
        self::JOB_VAN_PROJECT_TITLE_ID => self::VEHICLE_TYPE_VAN_TEXT,
        self::JOB_BIKE_TITLE_ID => self::VEHICLE_TYPE_BIKE_TEXT,
        self::JOB_TRICYCLE_TITLE_ID=>self::VEHICLE_TYPE_TRICYCLE_TEXT,
    ];

    // van truck 相关职位分组
    const VAN_OR_TRUCK_JOB_GROUP_ITEM = [
        self::JOB_VAN_TITLE_ID,
        self::JOB_VAN_PROJECT_TITLE_ID,
        self::JOB_TRUCK_TITLE_ID,
    ];


    // bike 相关职位分组
    const BIKE_JOB_GROUP_ITEM = [
        self::JOB_BIKE_TITLE_ID,
    ];
    // 可从 HR-IS 系统 同步过来的枚举字段列表
    const HR_IS_STAFF_CAR_NO_KEY = 'CAR_NO';// 车牌号
    const HR_IS_STAFF_CAR_TYPE_KEY = 'CAR_TYPE';// 车牌类型
    const HR_IS_STAFF_MANGER_KEY = 'MANGER';// 直线主管
    const HR_IS_STAFF_DRIVER_LICENSE_KEY = 'DRIVER_LICENSE';//驾驶证号
    const HR_IS_STAFF_ITEMS = [
        self::HR_IS_STAFF_CAR_NO_KEY,
        self::HR_IS_STAFF_CAR_TYPE_KEY,
        self::HR_IS_STAFF_MANGER_KEY,
        self::HR_IS_STAFF_DRIVER_LICENSE_KEY,
    ];

    // 油类型
    const OIL_TYPE_001 = 1;//汽油
    const OIL_TYPE_002 = 2;//柴油
    const OIL_TYPE_ITEM = [
        self::OIL_TYPE_001 => 'Gasoline',
        self::OIL_TYPE_002 => 'Diesel',
    ];

    //油卡补贴方式1:工资卡2:油卡
    const OIL_SUBSIDY_TYPE_1 = 1;
    const OIL_SUBSIDY_TYPE_2 = 2;
    const OIL_SUBSIDY_TYPE_ITEMS = [
        self::OIL_SUBSIDY_TYPE_1=>'oil_subsidy_type_1',
        self::OIL_SUBSIDY_TYPE_2=>'oil_subsidy_type_2',
    ];

    // 油卡开通状态
    const OIL_CARD_STATUS_NOT_OPENED = 0;
    const OIL_CARD_STATUS_OPENED = 1;
    const OIL_CARD_STATUS_ITEM = [
        self::OIL_CARD_STATUS_NOT_OPENED => 'not_open',
        self::OIL_CARD_STATUS_OPENED => 'already_opened'
    ];

    // 油卡公司
    const OIL_COMPANY_SHELL_CODE = 1;
    const OIL_COMPANY_PETRON_CODE = 2;
    const OIL_COMPANY_ITEM = [
        self::OIL_COMPANY_SHELL_CODE => 'Shell',
        self::OIL_COMPANY_PETRON_CODE => 'Petron',
    ];

    //从uconfig迁移过来的
    //全部的车辆品牌和型号
    const CONFIG_VEHICLE_INFO = [
        'vehicle_brand' => [
            [
                'value' => '1',
                'label'=> 'DONGFENG',
                'data' => [
                    ['value' => '1','car_long'=>'3.658','car_width'=>'1.9','car_high'=>'2.095', 'label'=> 'CAPTAIN-E 12ft'],
                    ['value' => '100', 'label'=> 'Other','car_long'=>'0','car_width'=>'0','car_high'=>'0'],
                ]
            ],
            [
                'value' => '2',
                'label'=> 'FOTON',
                'data' => [
                    ['value'=>'1','car_long'=>'3.658','car_width'=>'1.99','car_high'=>'2.08','label'=>'TORNADO M2.6C F-VAN 12ft'],
                    ['value'=>'2','car_long'=>'3.962','car_width'=>'1.99','car_high'=>'1.845','label'=>'TORNADO M2.6C MPV 13ft'],
                    ['value'=>'3','car_long'=>'3.658','car_width'=>'1.99','car_high'=>'1.845','label'=>'TORNADO M2.6C MPV 12ft'],
                    ['value'=>'4','car_long'=>'3.353','car_width'=>'1.99','car_high'=>'1.845','label'=>'TORNADO M2.6C MPV 11ft'],
                    ['value'=>'5','car_long'=>'3.048','car_width'=>'1.725','car_high'=>'1.845','label'=>'GRATOUR MT F-VAN 10ft'],
                    ['value'=>'6','car_long'=>'3.048','car_width'=>'1.725','car_high'=>'1.845','label'=>'GRATOUR MT WINGVAN 10ft'],
                    ['value'=>'7','car_long'=>'2.71','car_width'=>'1.68','car_high'=>'1.875','label'=>'GRATOUR MINIVAN'],
                    ['value'=>'8','car_long'=>'2.815','car_width'=>'1.52','car_high'=>'1.6','label'=>'HARABAS TM 300 F-VAN'],
                    ['value'=>'9','car_long'=>'2.815','car_width'=>'1.52','car_high'=>'1.6','label'=>'HARABAS TM 300 WING VAN'],
                    ['value'=>'10','car_long'=>'2.735','car_width'=>'1.575','car_high'=>'1.4','label'=>'HARABAS TM 300 MPV'],
                    ['value'=>'11','car_long'=>'2.82','car_width'=>'1.52','car_high'=>'1.32','label'=>'TRANSVAN'],
                    ['value'=>'12','car_long'=>'2.72','car_width'=>'1.5','car_high'=>'1.34','label'=>'GRATOUR MT MPV'],
                    ['value'=>'13','car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=>'M4.2c'],
                    ['value'=>'100','car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],

                ]
            ],
            [
                'value' => '3',
                'label'=> 'HYUNDAI',
                'data' => [
                    ['value'=>'1','car_long'=>'3.658','car_width'=>'1.7','car_high'=>'1.96','label'=>'H100 aluminum van 12ft'],
                    ['value'=>'2','car_long'=>'3.353','car_width'=>'1.7','car_high'=>'1.96','label'=>'H100 aluminum van 11ft'],
                    ['value'=>'3','car_long'=>'3.05','car_width'=>'1.7','car_high'=>'1.96','label'=>'H100 aluminum van 10ft'],
                    ['value'=>'4','car_long'=>'3.05','car_width'=>'1.7','car_high'=>'1.73','label'=>'H100 FB TYPE'],
                    ['value'=>'100','car_long'=>'0','car_width'=>'0','car_high'=>'0', 'label'=> 'Other'],
                ]
            ],
            [
                'value' => '4',
                'label'=> 'ISUZU',
                'data' => [
                    ['value'=>'1','car_long'=>'3.658','car_width'=>'1.695','car_high'=>'1.995','label'=>'QKR 77 aluminum van 12ft'],
                    ['value'=>'2','car_long'=>'3.658','car_width'=>'1.59','car_high'=>'1.96','label'=>'NLR 77 aluminum van 12ft'],
                    ['value'=>'3','car_long'=>'3.658','car_width'=>'1.59','car_high'=>'1.96','label'=>'TRAVIZ aluminum van 12ft'],
                    ['value'=>'4','car_long'=>'3.353','car_width'=>'1.695','car_high'=>'1.995','label'=>'QKR 77 aluminum van 11ft'],
                    ['value'=>'5','car_long'=>'3.172','car_width'=>'1.695','car_high'=>'1.995','label'=>'QKR 77 aluminum van 10ft'],
                    ['value'=>'6','car_long'=>'3.353','car_width'=>'1.59','car_high'=>'1.96','label'=>'NLR 77 aluminum van 11ft'],
                    ['value'=>'7','car_long'=>'3.353','car_width'=>'1.59','car_high'=>'1.96','label'=>'TRAVIZ aluminum van 11ft'],
                    ['value'=>'8','car_long'=>'3.172','car_width'=>'1.59','car_high'=>'1.96','label'=>'NLR 77 aluminum van 10ft'],
                    ['value'=>'9','car_long'=>'3.048','car_width'=>'1.59','car_high'=>'1.96','label'=>'TRAVIZ aluminum van 10ft'],
                    ['value'=>'10','car_long'=>'2.81','car_width'=>'1.66','car_high'=>'1.755','label'=>'TRAVIZ FB TYPE'],
                    ['value'=>'11','car_long'=>'2.365','car_width'=>'1.775','car_high'=>'1.207','label'=>'D-MAX FLEXIQUBE'],
                    ['value'=>'100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],
                ]
            ],
            [
                'value' => '5',
                'label'=> 'JAC',
                'data' => [
                    ['value' => '1','car_long'=>'3.658','car_width'=>'2.134','car_high'=>'1.829','label'=>'PRINCE aluminum van 12ft'],
                    ['value' => '2','car_long'=>'3.353','car_width'=>'2.134','car_high'=>'1.829','label'=>'PRINCE aluminum van 11ft'],
                    ['value' => '3','car_long'=>'3.353','car_width'=>'1.73','car_high'=>'2.24','label'=>'PRINCE FB TYPE'],
                    ['value' => '4','car_long'=>'3.048','car_width'=>'2.134','car_high'=>'1.829','label'=>'PRINCE aluminum van 10f'],
                    ['value' => '100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],
                ]
            ],
            [
                'value' => '6',
                'label'=> 'JMC',
                'data' => [
                    ['value' => '1','car_long'=>'3.658','car_width'=>'1.829','car_high'=>'1.829','label'=>'N700 12ft'],
                    ['value' => '2','car_long'=>'3.048','car_width'=>'1.829','car_high'=>'1.829','label'=>'JMH 10ft'],
                    ['value' => '100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],
                ]
            ],
            [
                'value' => '7',
                'label'=> 'KIA',
                'data' => [
                    ['value' => '1','car_long'=>'3.658','car_width'=>'1.892','car_high'=>'1.892','label'=>'k2500 aluminum van 12ft'],
                    ['value' => '2','car_long'=>'3.353','car_width'=>'1.829','car_high'=>'1.892','label'=>'k2500 aluminum van 11ft'],
                    ['value' => '3','car_long'=>'3.048','car_width'=>'1.892','car_high'=>'1.892','label'=>'k2500 aluminum van 10ft'],
                    ['value' => '4','car_long'=>'3.052','car_width'=>'1.74','car_high'=>'1.684','label'=>'k2500 FB TYPE'],
                    ['value' => '100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],
                ]
            ],
            [
                'value' => '8',
                'label'=> 'MITSUBISH',
                'data' => [
                    ['value' => '1','car_long'=>'3.658','car_width'=>'1.7','car_high'=>'1.845','label'=>'L300 aluminum van 12ft'],
                    ['value' => '2','car_long'=>'3.353','car_width'=>'1.7','car_high'=>'1.845','label'=>'L300 aluminum van 11ft'],
                    ['value' => '3','car_long'=>'3.048','car_width'=>'1.7','car_high'=>'1.845','label'=>'L300 aluminum van 10ft'],
                    ['value' => '4','car_long'=>'3.048','car_width'=>'1.676','car_high'=>'1.768','label'=>'L300 FB TYPE'],
                    ['value' => '100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],

                ]
            ],
            [
                'value' => '9',
                'label'=> 'TOYOTA',
                'data' => [
                    ['value' => '1','car_long'=>'3.86','car_width'=>'1.95','car_high'=>'2.28','label'=>'GL GRANDIA TOURER'],
                    ['value' => '2','car_long'=>'3.21','car_width'=>'1.95','car_high'=>'1.99','label'=>'GL GRANDIA'],
                    ['value' => '3','car_long'=>'3.21','car_width'=>'1.95','car_high'=>'1.99','label'=>'COMMUTER'],
                    ['value' => '4','car_long'=>'2.405','car_width'=>'1.8','car_high'=>'1.264','label'=>'HILUX FLEET 2.4 4X2 CARGO'],
                    ['value' => '5','car_long'=>'2.405','car_width'=>'1.8','car_high'=>'1.264','label'=>'HILUX FLEET 2.4 4X2 FX'],
                    ['value' => '100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],
                ]
            ],
            [
                'value' => '10',
                'label'=> 'SINOTRUCK',
                'data' => [
                    ['value' => '1','car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=>'H3'],
                ]
            ],
            [
                'value' => '100',
                'label'=> 'Other',
                'data' => [
                    ['value' => '100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],
                ]
            ],
        ],
        'vehicle_size' => [],
        'oil_type' => self::OIL_TYPE_ITEM,
        'oil_company' => [
            [ 'value' => self::OIL_COMPANY_SHELL_CODE, 'label'=> self::OIL_COMPANY_ITEM[self::OIL_COMPANY_SHELL_CODE]],
            [ 'value' => self::OIL_COMPANY_PETRON_CODE, 'label'=> self::OIL_COMPANY_ITEM[self::OIL_COMPANY_PETRON_CODE]],
        ],
        'branch_mileage' => [
        ],
    ];

    //Van bike
    const VEHICLE_BRAND_AND_MODEL_ITEM_DEFAULT  = [
        [
            'value' => '1',
            'label'=> 'DONGFENG',
            'data' => [
                ['value' => '1','car_long'=>'3.658','car_width'=>'1.9','car_high'=>'2.095', 'label'=> 'CAPTAIN-E 12ft'],
                ['value' => '100', 'label'=> 'Other','car_long'=>'0','car_width'=>'0','car_high'=>'0'],
            ]
        ],
        [
            'value' => '2',
            'label'=> 'FOTON',
            'data' => [
                ['value'=>'1','car_long'=>'3.658','car_width'=>'1.99','car_high'=>'2.08','label'=>'TORNADO M2.6C F-VAN 12ft'],
                ['value'=>'2','car_long'=>'3.962','car_width'=>'1.99','car_high'=>'1.845','label'=>'TORNADO M2.6C MPV 13ft'],
                ['value'=>'3','car_long'=>'3.658','car_width'=>'1.99','car_high'=>'1.845','label'=>'TORNADO M2.6C MPV 12ft'],
                ['value'=>'4','car_long'=>'3.353','car_width'=>'1.99','car_high'=>'1.845','label'=>'TORNADO M2.6C MPV 11ft'],
                ['value'=>'5','car_long'=>'3.048','car_width'=>'1.725','car_high'=>'1.845','label'=>'GRATOUR MT F-VAN 10ft'],
                ['value'=>'6','car_long'=>'3.048','car_width'=>'1.725','car_high'=>'1.845','label'=>'GRATOUR MT WINGVAN 10ft'],
                ['value'=>'7','car_long'=>'2.71','car_width'=>'1.68','car_high'=>'1.875','label'=>'GRATOUR MINIVAN'],
                ['value'=>'8','car_long'=>'2.815','car_width'=>'1.52','car_high'=>'1.6','label'=>'HARABAS TM 300 F-VAN'],
                ['value'=>'9','car_long'=>'2.815','car_width'=>'1.52','car_high'=>'1.6','label'=>'HARABAS TM 300 WING VAN'],
                ['value'=>'10','car_long'=>'2.735','car_width'=>'1.575','car_high'=>'1.4','label'=>'HARABAS TM 300 MPV'],
                ['value'=>'11','car_long'=>'2.82','car_width'=>'1.52','car_high'=>'1.32','label'=>'TRANSVAN'],
                ['value'=>'12','car_long'=>'2.72','car_width'=>'1.5','car_high'=>'1.34','label'=>'GRATOUR MT MPV'],
                // ['value'=>'13','car_long'=>'','car_width'=>'','car_high'=>'','label'=>'M4.2c'], 这个被使用了，不要占用13
                ['value'=>'100','car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],

            ]
        ],
        [
            'value' => '3',
            'label'=> 'HYUNDAI',
            'data' => [
                ['value'=>'1','car_long'=>'3.658','car_width'=>'1.7','car_high'=>'1.96','label'=>'H100 aluminum van 12ft'],
                ['value'=>'2','car_long'=>'3.353','car_width'=>'1.7','car_high'=>'1.96','label'=>'H100 aluminum van 11ft'],
                ['value'=>'3','car_long'=>'3.05','car_width'=>'1.7','car_high'=>'1.96','label'=>'H100 aluminum van 10ft'],
                ['value'=>'4','car_long'=>'3.05','car_width'=>'1.7','car_high'=>'1.73','label'=>'H100 FB TYPE'],
                ['value'=>'100','car_long'=>'0','car_width'=>'0','car_high'=>'0', 'label'=> 'Other'],
            ]
        ],
        [
            'value' => '4',
            'label'=> 'ISUZU',
            'data' => [
                ['value'=>'1','car_long'=>'3.658','car_width'=>'1.695','car_high'=>'1.995','label'=>'QKR 77 aluminum van 12ft'],
                ['value'=>'2','car_long'=>'3.658','car_width'=>'1.59','car_high'=>'1.96','label'=>'NLR 77 aluminum van 12ft'],
                ['value'=>'3','car_long'=>'3.658','car_width'=>'1.59','car_high'=>'1.96','label'=>'TRAVIZ aluminum van 12ft'],
                ['value'=>'4','car_long'=>'3.353','car_width'=>'1.695','car_high'=>'1.995','label'=>'QKR 77 aluminum van 11ft'],
                ['value'=>'5','car_long'=>'3.172','car_width'=>'1.695','car_high'=>'1.995','label'=>'QKR 77 aluminum van 10ft'],
                ['value'=>'6','car_long'=>'3.353','car_width'=>'1.59','car_high'=>'1.96','label'=>'NLR 77 aluminum van 11ft'],
                ['value'=>'7','car_long'=>'3.353','car_width'=>'1.59','car_high'=>'1.96','label'=>'TRAVIZ aluminum van 11ft'],
                ['value'=>'8','car_long'=>'3.172','car_width'=>'1.59','car_high'=>'1.96','label'=>'NLR 77 aluminum van 10ft'],
                ['value'=>'9','car_long'=>'3.048','car_width'=>'1.59','car_high'=>'1.96','label'=>'TRAVIZ aluminum van 10ft'],
                ['value'=>'10','car_long'=>'2.81','car_width'=>'1.66','car_high'=>'1.755','label'=>'TRAVIZ FB TYPE'],
                ['value'=>'11','car_long'=>'2.365','car_width'=>'1.775','car_high'=>'1.207','label'=>'D-MAX FLEXIQUBE'],
                ['value'=>'100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],
            ]
        ],
        [
            'value' => '5',
            'label'=> 'JAC',
            'data' => [
                ['value' => '1','car_long'=>'3.658','car_width'=>'2.134','car_high'=>'1.829','label'=>'PRINCE aluminum van 12ft'],
                ['value' => '2','car_long'=>'3.353','car_width'=>'2.134','car_high'=>'1.829','label'=>'PRINCE aluminum van 11ft'],
                ['value' => '3','car_long'=>'3.353','car_width'=>'1.73','car_high'=>'2.24','label'=>'PRINCE FB TYPE'],
                ['value' => '4','car_long'=>'3.048','car_width'=>'2.134','car_high'=>'1.829','label'=>'PRINCE aluminum van 10f'],
                ['value' => '100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],
            ]
        ],
        [
            'value' => '6',
            'label'=> 'JMC',
            'data' => [
                ['value' => '1','car_long'=>'3.658','car_width'=>'1.829','car_high'=>'1.829','label'=>'N700 12ft'],
                ['value' => '2','car_long'=>'3.048','car_width'=>'1.829','car_high'=>'1.829','label'=>'JMH 10ft'],
                ['value' => '100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],
            ]
        ],
        [
            'value' => '7',
            'label'=> 'KIA',
            'data' => [
                ['value' => '1','car_long'=>'3.658','car_width'=>'1.892','car_high'=>'1.892','label'=>'k2500 aluminum van 12ft'],
                ['value' => '2','car_long'=>'3.353','car_width'=>'1.829','car_high'=>'1.892','label'=>'k2500 aluminum van 11ft'],
                ['value' => '3','car_long'=>'3.048','car_width'=>'1.892','car_high'=>'1.892','label'=>'k2500 aluminum van 10ft'],
                ['value' => '4','car_long'=>'3.052','car_width'=>'1.74','car_high'=>'1.684','label'=>'k2500 FB TYPE'],
                ['value' => '100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],
            ]
        ],
        [
            'value' => '8',
            'label'=> 'MITSUBISH',
            'data' => [
                ['value' => '1','car_long'=>'3.658','car_width'=>'1.7','car_high'=>'1.845','label'=>'L300 aluminum van 12ft'],
                ['value' => '2','car_long'=>'3.353','car_width'=>'1.7','car_high'=>'1.845','label'=>'L300 aluminum van 11ft'],
                ['value' => '3','car_long'=>'3.048','car_width'=>'1.7','car_high'=>'1.845','label'=>'L300 aluminum van 10ft'],
                ['value' => '4','car_long'=>'3.048','car_width'=>'1.676','car_high'=>'1.768','label'=>'L300 FB TYPE'],
                ['value' => '100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],

            ]
        ],
        [
            'value' => '9',
            'label'=> 'TOYOTA',
            'data' => [
                ['value' => '1','car_long'=>'3.86','car_width'=>'1.95','car_high'=>'2.28','label'=>'GL GRANDIA TOURER'],
                ['value' => '2','car_long'=>'3.21','car_width'=>'1.95','car_high'=>'1.99','label'=>'GL GRANDIA'],
                ['value' => '3','car_long'=>'3.21','car_width'=>'1.95','car_high'=>'1.99','label'=>'COMMUTER'],
                ['value' => '4','car_long'=>'2.405','car_width'=>'1.8','car_high'=>'1.264','label'=>'HILUX FLEET 2.4 4X2 CARGO'],
                ['value' => '5','car_long'=>'2.405','car_width'=>'1.8','car_high'=>'1.264','label'=>'HILUX FLEET 2.4 4X2 FX'],
                ['value' => '100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],
            ]
        ],
        //这个被使用了，不要占用10
//        [
//            'value' => '10',
//            'label'=> 'Sinotruck',
//            'data' => [
//                ['value' => '1','car_long'=>'','car_width'=>'','car_high'=>'','label'=>'H3'],
//            ]
//        ],
        [
            'value' => '100',
            'label'=> 'Other',
            'data' => [
                ['value' => '100', 'car_long'=>'0','car_width'=>'0','car_high'=>'0','label'=> 'Other'],
            ]
        ],
    ];

    // TRUCK 车辆品牌及车辆型号列表
    const VEHICLE_BRAND_AND_MODEL_ITEM_TRUCK  = [
        [
            'value' => '2',
            'label'=> 'FOTON',
            'data' => [
                ['value'=>'13','car_long'=>'','car_width'=>'','car_high'=>'','label'=>'M4.2c','oil_subsidies_level'=>'D'],

            ]
        ],
        [
            'value' => '10',
            'label'=> 'SINOTRUCK',
            'data' => [
                ['value' => '1','car_long'=>'','car_width'=>'','car_high'=>'','label'=>'H3','oil_subsidies_level'=>'D'],
            ]
        ],
    ];

    public static function getCodeTxtMap($lang = '', $code = '')
    {
    }

}
