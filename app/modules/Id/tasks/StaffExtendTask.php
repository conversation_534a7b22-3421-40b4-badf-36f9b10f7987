<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/6/17
 * Time: 下午6:23
 */

namespace FlashExpress\bi\App\Modules\Id\Tasks;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoExtendMode;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveExtendModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Modules\Id\Server\Vacation\AnnualServer;
use FlashExpress\bi\App\Modules\Id\Server\LeaveServer;
use FlashExpress\bi\App\Repository\DepartmentRepository;

use FlashExpress\bi\App\Models\backyard\HrOvertimeForLeaveModel;
use StaffExtendTask as GlobalTask;

class StaffExtendTask extends GlobalTask
{

    //上线初始化年假额度 跑一次 模拟当月1号
    public function freeze_firstAction($param)
    {
        ini_set('memory_limit', '-1');
        try {
            $staff_id = 0;
            if (!empty($param[0])) {
                $staff_id = $param[0];
            }
            $today      = date('Y-m-d');
            $staff_list = $this->annualStaffList($today,$staff_id);
            if (empty($staff_list)) {
                die('没有员工数据');
            }

            //特殊情况 c level
            $dep_model = new DepartmentRepository($this->lang);
            $c_staffs  = $dep_model->get_c_level();

            $annualServer = new AnnualServer($this->lang, $this->timezone);
            $remainModel  = new StaffLeaveRemainDaysModel();
            $extModel     = new StaffLeaveExtendModel();
            foreach ($staff_list as $staff_info) {
                $staff_id  = $staff_info['staff_info_id'];
                $cycleInfo = $annualServer->get_cycle($staff_info);

                //开始计算 天数的时间 默认 22年 1月1号
                $startDate = '2022-01-01';
                if (strtotime($startDate) < strtotime($staff_info['hire_date'])) {
                    $startDate = $staff_info['hire_date'];
                }
                //分母应有额度
                if (in_array($staff_id, $c_staffs)) {
                    $should_days = enums::C_LEVEL_DAYS;
                } else {
                    $should_days = AnnualServer::$shouldDay;
                }

                //应发放额度 2022
                $should_days = $this->countDays($should_days, $startDate);
                $daysFor2022 = half_num($should_days);
                //22年 计算完的 余数
                $daysForCycle = bcsub($should_days, $daysFor2022, enums::ROUND_NUM);

                //22年 使用年假天数
                $usedDaysFor2022 = $annualServer->leaveDaysByDate($staff_id, 2022);
                //22年 剩余额度
                $leftFor2022 = bcsub($daysFor2022, $usedDaysFor2022, 1);
                //当前周期 最后一天
                $cycle_last_date = date('Y-m-d', strtotime("{$cycleInfo['count_day']} -1 day"));

                $this->getDI()->get('db')->begin();

                //初始化 22年 remain
                $row['staff_info_id'] = $staff_id;
                $row['leave_type']    = enums::LEAVE_TYPE_1;
                $row['year']          = 2022;
                $row['task_date']     = $today;
                $row['freeze_days']   = $should_days;
                $row['days']          = $leftFor2022;//剩余额度
                $row['leave_days']    = $usedDaysFor2022;//使用额度

                $cloneOld = clone $remainModel;
                $cloneOld->create($row);

                //初始化 22年的 ext
                $ext['staff_info_id']   = $staff_id;
                $ext['leave_type']      = enums::LEAVE_TYPE_1;
                $ext['year_at']         = 2022;
                $ext['left_all_days']   = $leftFor2022;//22年 剩余额度
                $ext['job_title_level'] = $staff_info['job_title_level'];
                $ext['job_title_grade'] = $staff_info['job_title_grade_v2'];
                //入库 ext表
                $extendOld = clone $extModel;
                $extendOld->create($ext);

                $cycle = $cycleInfo['cycle'];
                //特殊情况 初始化 当天正好是 当前周期最后一天
                if ($today == $cycle_last_date) {
                    $cycle = $cycleInfo['cycle'] + 1;
                }

                //初始化一条 当前周期的 额度 把小数 放进去
                $remain['staff_info_id'] = $staff_id;
                $remain['leave_type']    = enums::LEAVE_TYPE_1;
                $remain['year']          = $cycle;
                $remain['task_date']     = $today;
                $remain['freeze_days']   = $daysForCycle;
                $remain['days']          = $daysForCycle;
                $remain['leave_days']    = 0;

                //初始化 extend
                $extRow['staff_info_id']   = $staff_id;
                $extRow['leave_type']      = enums::LEAVE_TYPE_1;
                $extRow['year_at']         = $cycle;
                $extRow['left_all_days']   = $leftFor2022;
                $extRow['job_title_level'] = $staff_info['job_title_level'];
                $extRow['job_title_grade'] = $staff_info['job_title_grade_v2'];

                //入库 remain
                $cloneNew = clone $remainModel;
                $cloneNew->create($remain);
                //入库 ext表
                $extendClone = clone $extModel;
                $extendClone->create($extRow);
                $this->getDI()->get('db')->commit();
            }
        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            die('初始化额度报错 '.$e->getMessage().'-------'.$e->getTraceAsString());
        }
    }


    //每月1号 发上个月额度
    public function freeze_by_levelAction($param)
    {
        //任务锁
        $key   = 'freeze_by_level_lock';
        $redis = $this->getDI()->get("redisLib");
        $rs    = $redis->set($key, 1, ['nx', 'ex' => 10 * 60]);//锁10分钟
        if (!$rs && RUNTIME == 'pro') {
            echo 'task is running';
            return ;
        }
        $today = date('Y-m-d');

        if (!empty($param[0])) {
            $today = $param[0];
        }

        if (!empty($param[1])) {
            $staffParam['staff_info_id'] = (int)$param[1];
        }

        try {
            $staffParam['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2];
            $staffParam['state']     = [HrStaffInfoModel::STATE_1, HrStaffInfoModel::STATE_3];
            $staff_list              = $this->annualStaffList($today, $staffParam);

            if (empty($staff_list)) {
                return true;
            }

            $staff_list = array_column($staff_list, null, 'staff_info_id');
            $dep_model = new DepartmentRepository($this->lang);
            $c_staffs  = $dep_model->get_c_level();

            $annualServer = new AnnualServer($this->lang, $this->timezone);
            $leaveServer = new LeaveServer($this->lang, $this->timezone);
            foreach ($staff_list as $staff_id => $staff_info) {
                if (empty($staff_id)) {
                    continue;
                }

                $flag = $leaveServer->leavePermission($staff_info);
                if(!$flag){
                    echo $staff_info['staff_info_id'].' 非工作所在国家 不发年假';
                    continue;
                }

                //获取当前周期的额度信息
                $cycle_info = $annualServer->get_cycle($staff_info);
                if (empty($cycle_info)) {
                    echo $staff_info['staff_info_id'].'没有入职日期';
                    continue;
                }

                //新增 计算年假日期逻辑 https://flashexpress.feishu.cn/docx/USK1dOhAiod8STxnC0ccJqacn4d
                if(!empty($staff_info['annual_date'])){
                    //清空日期 不累计年假
                    if($staff_info['annual_date'] == HrStaffInfoExtendMode::ANNUAL_STOP_DATE){
                        echo 'base 清空日期 不计算年假 ' . "{$staff_info['staff_info_id']} ". $staff_info['annual_date'] ;
                        continue;
                    }
                    //日期在当天之后 不累计
                    if($staff_info['annual_date'] > $today){
                        echo 'base 没到计算日期 不计算年假 ' . "{$staff_info['staff_info_id']} ". $staff_info['annual_date'];
                        continue;
                    }
                }

                //下个周期
                $this->getDI()->get('db')->begin();
                $nextCycle = $cycle_info['cycle'] + 1;

                $current_remain = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year = :cycle: and leave_type = :leave_type:',
                    'bind'       => [
                        'staff_id'   => $staff_id,
                        'cycle'      => $cycle_info['cycle'],
                        'leave_type' => enums::LEAVE_TYPE_1,
                    ],
                ]);

                $isNewHire = false;//是否 新入职员工 true 新员工, false 非新员工
                //有可能是新入职员工
                if (empty($current_remain) || $current_remain->freeze_days == 0) {
                    $isContinue = true;//需要继续往下走的 用下面的add day 包括新入职和迁移账号
                    if ($cycle_info['cycle'] == 1 && empty($staff_info['annual_date'])) {
                        $isNewHire = true;//由于 入职日期 不准确是昨天 所以用这个判断
                    } else {
                        $this->getDI()->get('logger')->write_log("freeze_by_level {$staff_info['staff_info_id']} 当前周期额度异常 没有当前周期额度信息 初始化1天额度", 'info');
                    }
                    $current_remain = $annualServer->initAnnual($staff_info, $cycle_info['cycle'],['task_date' => $today]);
                }

                //任务判定是否跑过 每天只能跑一次
                if (!empty($current_remain) && $current_remain->task_date >= $today && empty($isContinue)) {
                    $this->getDI()->get('db')->commit();
                    continue;
                }

                //新需求 用历史最高职级 不用实时的
                $ext_info = StaffLeaveExtendModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year_at = :cycle:',
                    'bind'       => [
                        'staff_id'   => $staff_info['staff_info_id'],
                        'leave_type' => enums::LEAVE_TYPE_1,
                        'cycle'      => $cycle_info['cycle'],
                    ],
                ]);
                if (empty($ext_info)) {
                    $this->getDI()->get('db')->rollback();
                    $this->getDI()->get('logger')->write_log("freeze_by_level {$staff_info['staff_info_id']} 当前周期额度扩展表 异常");
                    continue;
                }

                //c level 固定 20天
                if (in_array($staff_info['staff_info_id'], $c_staffs)) {
                    $should_day = enums::C_LEVEL_DAYS;
                } else {
                    $should_day = AnnualServer::$shouldDay;
                }

                //没有自然年概念 都是 365
                $add_day = round($should_day / 365, enums::ROUND_NUM);
                if ($isNewHire) {//新入职员工 第一次递增额度 * 2 因为入职当天 任务没跑这个人
                    $add_day += $add_day;
                }

                $current_remain->freeze_days += $add_day;
                $current_remain->days        += $add_day;//加一天额度
                $cycle_last_date             = date('Y-m-d', strtotime("{$cycle_info['count_day']} -1 day"));
                if ($today == $cycle_last_date) {//当天是 当前周期 最后一天 加一天额度 然后初始化 下一年记录 并且当年额度 +1天
                    $remains = $this->getNewCycleRemains($staff_id);
                    if ($remains->count() == 1){
                        //非完整周期的不满0.5的结余要加到下个周期里；
                        $freezeDays = round($current_remain->freeze_days - half_num($current_remain->freeze_days),enums::ROUND_NUM);
                    }else{
                        $freezeDays = 0;
                    }
                    $annualServer->initAnnual($staff_info, $nextCycle, ['freeze_days'=>$freezeDays,'days'=>$freezeDays,'task_date' => $today]);
                    //c级别 和 19天的 由于5位小数点 四舍五入 舍去了 需要加一天额度
                    if(in_array($should_day, [19,20])){
                        $current_remain->days += $add_day;
                    }
                }

                $current_remain->task_date = $today;
                $current_remain->update();

                $this->getDI()->get('db')->commit();
                $this->logger->write_log("freeze_by_level_{$staff_info['staff_info_id']}_{$cycle_info['cycle']} {$today} 增加额度 {$add_day} ",
                    'info');
            }

            $redis->delete($key);
            $this->logger->write_log("freeze_by_level {$today} 年假固化任务 跑完了 ", 'notice');
        } catch (\Exception $e) {
            $this->logger->write_log("freeze_by_level {$today} 任务数据失败 ".$e->getTraceAsString());
            $this->getDI()->get('db')->rollback();
            die('freeze_by_level 任务异常 '.$e->getTraceAsString());
        }
    }

    /**
     * @param $params
     * @return void
     */
    public function freeze_secondAction($params)
    {
        ini_set('memory_limit', '-1');
        $staff_id = '';
        if (!empty($params[0])) {
            $staff_id = $params[0];
        }

        $today         = date('Y-m-d');
        $staff_list = $this->annualStaffList($today,$staff_id);

        if (empty($staff_list)) {
            die('没有员工数据');
        }
        foreach ($staff_list as $staff){
            if ( date('md',strtotime($staff['hire_date'])) == '1201'){
                $remains = StaffLeaveRemainDaysModel::find(
                    [
                        'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type:',
                        'bind'       => [
                            'staff_id'   => $staff['staff_info_id'],
                            'leave_type' => enums::LEAVE_TYPE_1,
                        ],
                        'order' => 'year asc',
                    ]
                );
                $first = $remains->getFirst();
                $second = $remains->getLast();
                $days = round($second->freeze_days - half_num($second->freeze_days), enums::ROUND_NUM);
                $first->freeze_days += $days;
                $first->days        += $days;
                $this->echo("{$staff['staff_info_id']},周期{$first->year}需要调整{$days}");
                $first->update();
            }else{
                $remains = $this->getNewCycleRemains($staff['staff_info_id']);
                if ($remains->count() == 2){
                    $first = $remains->getFirst();
                    $days = round($first->freeze_days - half_num($first->freeze_days),enums::ROUND_NUM);
                    $second = $remains->getLast();
                    $second->freeze_days += $days;
                    $second->days        += $days;
                    $this->echo("{$staff['staff_info_id']},周期{$second->year}需要调整{$days}");
                    $second->update();
                }
            }
        }
    }


    //工具 额外增加 补休假 工号 天数 日期（要算失效日期）
    public function lieu_toolAction($param)
    {
        $staff_id     = intval($param[0]);
        $days         = intval($param[1]);//需要传 *10 的天数
        $date         = date('Y-m-d', strtotime($param[2]));
        $leave_server = new LeaveServer($this->lang, $this->timezone);

        $flag = $leave_server->add_remain_days($staff_id, $days, $date, 3);

        var_dump($flag);
    }


    /**
     * 兑换调休假
     */
    public function ot_for_leaveAction()
    {
        echo "开始换调休假...", PHP_EOL;
        //刘佳雪要求跑30天之前的数据
        $dateTime = date("Y-m-d 00:00:00", strtotime("-30 days"));
        $data     = HrOvertimeForLeaveModel::find([
            'conditions' => ' created_at > :date_time: and state in(1,3)',
            'bind'       => [
                'date_time' => $dateTime,
            ],
        ])->toArray();
        if (empty($data)) {
            echo "无待兑换为调休假的加班 ...", PHP_EOL;
            return false;
        }

        echo sprintf("共 %s 条数据需要处理...", count($data)), PHP_EOL;
        $leave_server = new LeaveServer($this->lang, $this->timezone);

        foreach ($data as $v) {
            echo sprintf("员工号 %s ,日期 %s", $v['staff_info_id'], $v['date_at']), PHP_EOL;
            //获取当天的打卡时长
            $attendanceInfo = StaffWorkAttendance::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and attendance_date = :date:',
                'bind'       => [
                    'staff_info_id' => $v['staff_info_id'],
                    'date'          => $v['date_at'],
                ],
            ]);
            if (empty($attendanceInfo)) {
                echo "没有加班打卡", PHP_EOL;
                continue;
            }
            $attendanceInfo = $attendanceInfo->toArray();

            //获取详情
            $info = HrOvertimeForLeaveModel::findFirst($v['id']);
            if (empty($info)) {
                continue;
            }

            //第2天中午12点，监测审批通过的Rest Day OT、OFF Day OT、PH OT当天是否有有效的打卡时间，如果有根据以下条件给补上相应的调休假额度
            //2小时=<打卡时长且申请4个小时OT，转为0.5天的调休假
            //打卡时长>=7小时且申请8个小时OT，转为1天的调休假
            if (isset($attendanceInfo['started_at']) && $attendanceInfo['started_at'] &&
                isset($attendanceInfo['end_at']) && $attendanceInfo['end_at']) {
                //计算上下班打开间隔
                $lastTime = strtotime($attendanceInfo['end_at']) - strtotime($attendanceInfo['started_at']);
                if ($lastTime >= 4 * 3600) { //可以发调休假
                    //如果时长不满足要求
                    if ($lastTime < 9 * 3600 && bccomp($v['duration'], 8) >= 0) {
                        $info->setState(3);//不符合转化条件
                        $info->update();

                        echo sprintf("%s 由于上下班打卡时长不足7小时, 不能兑换OT", $v['staff_info_id']), PHP_EOL;
                        continue;
                    }

                    if ($lastTime >= 9 * 3600 && bccomp($v['duration'], 8) >= 0) { //9小时以上1天
                        $days = 1.0;
                    } else { //2到7小时给半天
                        $days = 0.5;
                    }

                    //新版补休假
                    $leave_server->add_remain_days($v['staff_info_id'], $days, $v['date_at'], 1);

                    $info->setState(2);
                    $info->update();

                    echo sprintf("%s 兑换调休假 %s 天", $v['staff_info_id'], $days), PHP_EOL;
                } else {
                    $info->setState(3);
                    $info->update();

                    echo sprintf("%s 由于上下班打卡时长不足2小时, 不能兑换OT", $v['staff_info_id']), PHP_EOL;
                }
            } else {
                $info->setState(3);
                $info->update();

                echo sprintf("%s 由于上下班打卡不全, 不能兑换OT", $v['staff_info_id']), PHP_EOL;
            }
        }
    }

}