<?php
/**
 * Author: Bruce
 * Date  : 2022-12-30 14:23
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Id\Tasks;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\ReportAbsenteeismLogModel;
use FlashExpress\bi\App\Models\backyard\ReportAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\AttendanceDataV2Model;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\ReportRepository;

use FlashExpress\bi\App\Server\WhiteListServer;
use ReportAbsenteeismTask as BaseReportAbsenteeismTask;

class ReportAbsenteeismTask extends BaseReportAbsenteeismTask
{

    /**
     * $params[0] 日期
     * $params[1] 逗号隔开的工号
     * 印尼，旷工举报
     * @param $params
     */
    public function reportAbsenteeismStaffAction($params)
    {
        $date = $params[0] ?? date("Y-m-d");;
        $staff_info_ids = !empty($params[1]) ? explode(',', $params[1]) : [];
        $logger = $this->getDI()->get('logger');

        // 获取本日的迟到早退应该进行举报的日期(跨天班次举报前天的，不跨天班次举报昨天的)
        $where['start_date']     = date('Y-m-d', strtotime("$date -2 day"));
        $where['end_date']       = date('Y-m-d', strtotime("$date -1 day"));
        $where['staff_info_ids'] = $staff_info_ids;

        $staffAttendanceData = $this->getAbsenteeismStaffInfo($where);

        if (empty($staffAttendanceData)) {
            return;
        }

        //获取打卡白名单 //当天在白名单的不进行旷工举报
        $whiteListServer = new WhiteListServer();
        $hcm_rpc_result = $whiteListServer->attendanceList(['start_date'=>$where['start_date'],'end_date'=>$where['end_date']]);
        $attendance_white_list = $hcm_rpc_result['result']['data'] ?? [];
        if (empty($attendance_white_list)) {
            $this->logger->write_log('获取白名单失败!,重新跑任务吧','error');
            exit();
        }




        //查询出前天，昨天的 缺卡员工信息
        $staffAttendList = [];
        $abStaffIds = [];
        foreach ($staffAttendanceData as $oneStaffData) {

            $staffAttendList[$oneStaffData['staff_info_id']][$oneStaffData['event_date']] = $oneStaffData;
            $abStaffIds[] = $oneStaffData['staff_info_id'];
        }
        // 获取举报日志
        $reportAbsenteeismList = $this->getAbsenteeismInfo($where);
        $reportAbsenteeismLog  = [];
        foreach ($reportAbsenteeismList as $oneAbsenteeism) {
            $reportAbsenteeismLog[] = $oneAbsenteeism['staff_info_id'].'_'.$oneAbsenteeism['attendance_date'];
        }
        $logger->write_log('*********************[reportAbsenteeismStaffAction]Start:'.date('Y-m-d H:i:s').'*******************',
            'info');


        //指定日期的出差打卡数据
        $staffBusinessList = !empty($abStaffIds) ? $staffBusinessList = $this->getBusinessData($abStaffIds, $where['end_date']) : [];

        foreach ($staffAttendList as $key => $oneStaffAttendance) {
            //前天
            $beforeYesterdayAttendanceData = $oneStaffAttendance[$where['start_date']] ?? [];
            if ($beforeYesterdayAttendanceData && !empty($beforeYesterdayAttendanceData['shift_start']) && !empty($beforeYesterdayAttendanceData['shift_end'])) {
                // 检查前天的班次是不是跨天班次, 不是跨天跳过（不是跨天班次，则昨天已经举报过，这里不用在处理了）
                if ($this->isCrossShift($beforeYesterdayAttendanceData['shift_start'],
                    $beforeYesterdayAttendanceData['shift_end'])) {
                    //员工id存在,已举报过，跳过。
                    if (!in_array($key."_".$where['start_date'], $reportAbsenteeismLog)) {
                        //员工id存在白名单中不进行举报
                        $whiteDays = $whiteListServer->whiteListDays($key,[$where['start_date']],$attendance_white_list);
                        echo  $key.'_'.$where['start_date'] .'是否是白名单：'.implode(',',$whiteDays).PHP_EOL;
                        if (count($whiteDays) < 1) {
                            $report_month = $this->getAttendanceMonth($where['start_date']);
                            $this->sendReport($beforeYesterdayAttendanceData, $report_month);
                        }

                    } else {
                        $logger->write_log('[reportAbsenteeismStaffAction]员工已举报过，跳过。 staff_info_id:'.$key.', date:'.$where['start_date'],
                            'info');
                    }
                }
            }

            //昨天
            $yesterdayAttendance = $oneStaffAttendance[$where['end_date']] ?? [];
            if ($yesterdayAttendance && !empty($yesterdayAttendance['shift_start']) && !empty($yesterdayAttendance['shift_end'])) {
                // 考勤日期是昨天,跨天班次需要明天进行举报，这里只处理不跨天班次,跨天班次，跳过
                if ($this->isCrossShift($yesterdayAttendance['shift_start'],
                    $yesterdayAttendance['shift_end'])) {
                    continue;
                }
                //员工id存在,已举报过，跳过。
                if (in_array($key."_".$where['end_date'], $reportAbsenteeismLog)) {
                    $logger->write_log('[reportAbsenteeismStaffAction]员工已举报过，跳过。 staff_info_id:'.$key.', date:'.$where['end_date'],
                        'info');
                    continue;
                }

                //出差记录，如果存在出差记录，则为出差。不计旷工。
                if (in_array($key, $staffBusinessList)) {
                    $logger->write_log('[reportAbsenteeismStaffAction]员工出差，不举报跳过。 staff_info_id:'.$key.', date:'.$where['end_date'],
                        'info');
                    continue;
                }
                //员工id存在白名单中不进行举报
                $whiteDays = $whiteListServer->whiteListDays($key,[$where['end_date']],$attendance_white_list);
                echo  $key.'_'.$where['end_date'] .'是否是白名单：'. implode(',',$whiteDays).PHP_EOL;
                if (count($whiteDays) < 1) {
                    $report_month = $this->getAttendanceMonth($where['end_date']);
                    $this->sendReport($yesterdayAttendance, $report_month);
                }

            }
        }
        $logger->write_log('*********************[reportAbsenteeismStaffAction]End:'.date('Y-m-d H:i:s').'*******************',
            'info');
    }

    /**
     * 获取某一天的 出差卡
     * @param $staff_info_ids
     * @param $date
     * @return array
     */
    public function getBusinessData($staff_info_ids, $date)
    {
        $staffAuditReissueForBusinesses = StaffAuditReissueForBusinessModel::find([
            'conditions' => ' staff_info_id in ({staff_ids:array}) and attendance_date = :date: ',
            'bind'       => [
                'staff_ids' => $staff_info_ids,
                'date'      => $date,
            ],
            'columns'    => ['staff_info_id']
        ])->toArray();

        return array_column($staffAuditReissueForBusinesses, 'staff_info_id') ;
    }

    /**
     * 发送举报
     * @param $eventInfo
     * @param $report_month
     */
    public function sendReport($eventInfo, $report_month)
    {
        //考勤日未举报过，操作：进行举报
        //调用举报方法，进行举报
        $report_data   = $this->createReportDataInfo($eventInfo);
        $report_result = $this->addReport($report_data);

        //写入report_absenteeism_log表
        $eventInfo['num'] = 1;//1次 按天的维度，默认给1
        $report_log       = $this->createReportLogData($eventInfo, $report_month, $report_result);
        $this->addReportAbsenteeismLog($report_log);

        //输出结果
        $this->outputMsg($report_result, $eventInfo);
    }

    //生成举报数据
    function createReportDataInfo($staff_info)
    {
        $return['paramIn']  = [
            'report_type' => ReportAuditModel::REPORT_TYPE_WARNING_LETTER,
            'reason'      => ReportRepository::REPORT_TYPE_ABSENTEEISM,
            'remark'      => "The system automatically reports the no punch in and no punch out in one day",
            'report_id'   => $staff_info['staff_info_id'],
            'event_date'  => $staff_info['event_date'],
        ];
        $return['userinfo'] = [
            'id'                => 10000,
            'staff_id'          => 10000,
            'organization_type' => 2,
        ];
        return $return;
    }

    /**
     * 获取缺卡信息
     * @param $params
     * @return mixed
     */
    public function getAbsenteeismStaffInfo($params)
    {
        //获取当月整天缺勤员工信息 不包括总部员工
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['att' => AttendanceDataV2Model::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'att.staff_info_id = hr.staff_info_id', 'hr');
        $builder->where("att.PH = :PH: ", ['PH' => 0]);//非公休日
        $builder->andWhere("att.OFF = :OFF: ", ['OFF' => 0]);//非调休
        $builder->andWhere("att.leave_time_type = :leave_time_type: ", ['leave_time_type' => 0]);//非请假
        $builder->andWhere("hr.state = :state: ", ['state' => HrStaffInfoModel::STATE_1]);
        $builder->andWhere("att.attendance_started_at IS NULL");
        $builder->andWhere("att.attendance_end_at IS NULL");
        $builder->andWhere("att.stat_date in ({stat_date:array}) ",
            ['stat_date' => [$params['start_date'], $params['end_date']]]);
        if (!empty($params['staff_info_ids'])) {
            $builder->andWhere("att.staff_info_id in ({staff_info_ids:array}) ",
                ['staff_info_ids' => $params['staff_info_ids']]);
        }
        $builder->columns("att.staff_info_id,stat_date as event_date, shift_start, shift_end ");

        $staff_list = $builder->getQuery()->execute()->toArray();

        return $staff_list;
    }

    /**
     * 获取某个考勤日已举报的员工信息
     * @param $params
     * @return array
     */
    public function getAbsenteeismInfo($params)
    {
        //获取当月已举报员工信息
        $report_absenteeism_list = ReportAbsenteeismLogModel::find([
            'conditions' => 'attendance_date in ({attendance_date:array}) and report_state = :state:',
            'bind'       => [
                'attendance_date' => [$params['start_date'], $params['end_date']],
                'state'           => ReportAbsenteeismLogModel::REPORT_STATE_SUCCESS,
            ],
            'columns'    => ['staff_info_id, attendance_date'],
        ])->toArray();

        return $report_absenteeism_list;
    }

    /**
     * 看考勤日期属于哪个考勤月
     * @param $attendanceDate
     * @return false|string
     */
    public function getAttendanceMonth($attendanceDate)
    {
        $attendanceTime = strtotime($attendanceDate);
        // 按照考勤周期，每个自然月24号到次月23号
        // 检查当前所属的周期
        $thisMonthCycleBeginInt = strtotime(date('Y-m-24'));
        //小于 24号---当月周期
        if ($attendanceTime < $thisMonthCycleBeginInt) {
            $attendance_month = date('Y-m');
        } else {
            //大于等于 24号---下个月周期
            $attendance_month = date('Y-m', strtotime("+ 1 month"));
        }

        return $attendance_month;
    }

    /**
     * Notes: 检查一个班次是跨天班次
     * @param string $shiftStart
     * @param string $shiftEnd
     * @return bool
     */
    private function isCrossShift(string $shiftStart, string $shiftEnd): bool
    {
        $date         = date('Y-m-d');
        $startTimeInt = strtotime($date.' '.$shiftStart);
        $endTimeInt   = strtotime($date.' '.$shiftEnd);
        if ($startTimeInt > $endTimeInt) {
            return true;
        }
        return false;
    }

}