<?php

namespace FlashExpress\bi\App\Modules\Id\Controllers;

use FlashExpress\bi\App\Controllers\OvertimeController as BaseOvertimeController;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Modules\Id\Server\OvertimeServer;
use Exception;

class OvertimeController extends BaseOvertimeController
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
        $this->url_log($this->paramIn);
    }

    /**
     * 新建加班
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function addOvertimeAction()
    {
        try {
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
            $paramIn['userinfo'] = $this->userinfo;
            $validations         = [
                "staff_id"  => "Required|Int",
                "reason"    => "Required|StrLenGeLe:1,1000|>>>:" . $this->getTranslation()->_('5110'),
            ];
            $this->validateCheck($paramIn, $validations);
            $server = new OvertimeServer($this->lang,$this->timezone);
            $returnArr = $server->setLockConf(60,true)->addOvertimeV3UseLock($paramIn);
            $this->jsonReturn($returnArr);
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("addOvertime {$paramIn['staff_info_id']} " .  $e->getMessage() .  $e->getTraceAsString());
            $this->jsonReturn(self::checkReturn(-3, 'server error'));
        }
    }

    /**
     * 修改加班申请
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function updateOvertimeAction()
    {
        try {
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];

            $validations         = [
                "staff_id"           => "Required|Int",
                "audit_id"           => "Required|Int",
                "reject_reason"      => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1020'),
            ];
            $this->validateCheck($paramIn, $validations);

            //审批加班
            $server = new OvertimeServer($this->lang,$this->timezone);
            $returnArr = $server->setLockConf(60,true)->updateOvertimeV3UseLock($paramIn);

            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('updateOvertime:'. $e->getMessage(), 'error');
            } else {
                $this->getDI()->get('logger')->write_log('updateOvertime:'. $e->getMessage(), 'info');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取加班类型
     */
    public function getTypeOvertimeAction()
    {
        try {
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];

            //获取请假类型数据
            $server = new OvertimeServer($this->lang,$this->timezone);
            $returnArr = $server->getTypeOvertime($paramIn);

            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('getTypeOvertime:'. $e->getMessage(), 'notice');
            } else {
                $this->getDI()->get('logger')->write_log('getTypeOvertime:'. $e->getMessage(), 'info');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }
}