<?php

namespace FlashExpress\bi\App\Modules\Id\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\PayrollServer;

class PayrollController extends Controllers\PayrollController
{
    /**
     * 工资条 验证账号密码 根据参数返回对应的 pdf 文件 印尼有两个pdf 一个工资条 一个非固定补贴
     */
    public function loginAction()
    {
        $message = 'Wrong account or password';
        $data = $this->paramIn;
        if (!isset($data['user']) || !isset($data['pwd'])) {
            $this->jsonReturn($this->checkReturn(-3, $message));
        }
        $_data['account'] = $data['user'];
        $_data['pwd'] = $data['pwd'];
        $return_data = (new PayrollServer())->verify_fle($_data);
        if (empty($return_data)) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('login_all')));
        }
        if (!isset($data['date'])) {
            $data['date'] =  date('Y-m',strtotime(date('Y-m') . ' -1 month'));
        }
        $method = 'get_payroll_pdf';
        if(isset($data['unfixed']) && $data['unfixed'] == 1){
            $method = 'get_unfixed_pdf';
        }

        $hcm_rpc = new ApiClient('hcm_rpc', '', $method,$this->lang);
        $hcm_rpc->setParams(
            [
                "staff_id" => $data['user'],
                "month" => $data["date"],
            ]
        );
        $return = $hcm_rpc->execute();
        if(isset($return['result']['code']) && $return['result']['code'] == 1) {
            $this->jsonReturn(self::checkReturn(["data" => ["url" => $return['result']['data'] ?? '']]));
        } else {
            $this->jsonReturn($this->checkReturn(-3, $return['result']['msg'] ?? 'Not found'));
            $this->getDI()->get('logger')->write_log("get_pdfAction:get_payroll_pdf:" . json_encode($return ,true));
        }
    }

}