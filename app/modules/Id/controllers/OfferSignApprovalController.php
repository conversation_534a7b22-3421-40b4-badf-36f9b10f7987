<?php

namespace FlashExpress\bi\App\Modules\Id\Controllers;

use FlashExpress\bi\App\Modules\Id\Server\OfferSignApproveServer;
use FlashExpress\bi\App\Controllers\OfferSignApprovalController as GlobalController;

class OfferSignApprovalController extends GlobalController
{


    /**
     * 更新审批状态
     *
     */
    public function updateApprovalAction()
    {
        $params = $this->paramIn;

        $this->validateCheck([
            "status" => $params['status'],
            "id"     => $params['audit_id'],
        ], [
            "status" => 'Required|IntIn:2,3,4',
            "id"     => 'Required|Int',
        ]);

        $result   = '';
        $userinfo = $this->userinfo;
        $this->atomicLock(function () use ($params, $userinfo, &$result) {
            $remark = isset($params['reject_reason']) && $params['reject_reason'] ? $params['reject_reason'] : '';
            $result = (new OfferSignApproveServer($this->lang, $this->timezone))->updateApprove($userinfo,
                $params['audit_id'], $params['status'], $remark);
        }, 'approval_offer_sign_'.$this->userinfo['staff_id']);
        if ($result === false) { //没有获取到锁
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('ticket_repeat_msg')));
        }
        $this->jsonReturn($result);
    }


}