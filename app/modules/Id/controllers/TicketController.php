<?php
namespace FlashExpress\bi\App\Modules\Id\Controllers;

use FlashExpress\bi\App\Controllers\TicketController as BaseTicketController;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use Exception;


class TicketController extends BaseTicketController
{
    protected $paramIn;
    /**
     * @var \FlashExpress\bi\App\Server\TicketServer
     */
    protected $ticketServer;

    public function initialize()
    {
        parent::initialize();
        $this->ticketServer = new \FlashExpress\bi\App\Server\TicketServer($this->lang, $this->timezone,$this->userinfo);
        $this->paramIn     = $this->request->get();
        
        //会带个_url参数
        unset($this->paramIn['_url']);

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        //$this->paramIn = array_filter($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 提交工单
     */
    public function addAction(){
        try {
            $paramIn   = $this->paramIn;

            if(empty($paramIn['line_id'])){
                unset($paramIn['line_id']);
            }

            $validations = [
                "item_type" => "Required|IntGe:0",
                "line_id"=>"StrLenGeLe:0,20",
                "mobile"=>"Required|StrLenGeLe:9,12",
                "info"=>"Required|StrLenGeLe:1,500",
                'pics'=>"StrLenGeLe:0,1000",
                //"device_store_id" => "Required|StrLen:10",    // 设备所在网点 10 位
            ];

            if (!empty($paramIn['anydesk_id'])) {
                $validations['anydesk_id'] = "StrLenGeLe:1,20";
            }

            if (!empty($paramIn['item_code'])) { // 资产编号
                $validations['item_code'] = "StrLenGeLe:1,20";
            }

            if (!empty($paramIn['device_store_id']) && $paramIn['device_store_id'] != '-1') {
                $validations['device_store_id'] = "Required|StrLen:10";
            }

            if (!empty($paramIn['device_store_id']) && $paramIn['device_store_id'] == '-1') {
                $validations['device_store_id'] = "Required|StrLen:2";
            }

            $this->validateCheck($paramIn, $validations);

            $paramIn = array_only($paramIn,array_keys($validations));

            $data = $this->ticketServer->add($paramIn);
            return $this->jsonReturn($data);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("add 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

}
