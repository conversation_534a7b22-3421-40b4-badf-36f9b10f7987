<?php
/**
 * Author: Bruce
 * Date  : 2022-11-11 03:34
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Id\Controllers;

use FlashExpress\bi\App\Controllers\BackyardController as BaseBackyard;
use FlashExpress\bi\App\Modules\Id\Server\BackyardServer;
use FlashExpress\bi\App\Modules\Id\Server\MessageServer;


class BackyardController extends BaseBackyard
{

    public    $staff_id;
    public    $miss_args;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $userinfo        = $this->userinfo;
        $this->staff_id  = $userinfo['id'];
        $this->miss_args = $this->getTranslation()->_('miss_args');

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    //警告书签名保存接口
    public function sign_warningAction()
    {
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "msg_id" => "Required|StrLenGeLe:1,100",
            "url"    => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);

        $returnArr = $this->atomicLock(function () use ($param) {
            $message_server = new MessageServer($this->lang,$this->timezone);
            $message_server->sign_for_warning($param);
        }, 'sign_warning_msg_id_' . $param['msg_id']);
        if ($returnArr === false) { //没有获取到锁
            return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
        }
        $this->jsonReturn($this->checkReturn(1));
    }
}