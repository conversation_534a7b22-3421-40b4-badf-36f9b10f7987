<?php
namespace FlashExpress\bi\App\Modules\Id\Controllers;
use FlashExpress\bi\App\Modules\Id\Server\PersoninfoServer;
use FlashExpress\bi\App\Controllers\PersoninfoController as BasePersonInfoController;

class PersoninfoController extends BasePersonInfoController
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
        }
        $this->paramIn = filter_param($this->paramIn);
    }


    /**
     * 修改企业手机号
     */
    public function updateCompanyMobileAction() {
        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        //[2]业务处理
        $returnArr = (new PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->updateMobileCompany($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


}
