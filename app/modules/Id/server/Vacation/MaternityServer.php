<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 5/5/23
 * Time: 2:16 PM
 */
namespace FlashExpress\bi\App\Modules\Id\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\StaffLeaveProperty;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Server\Vacation\MaternityServer as GlobalServer;


class MaternityServer extends GlobalServer implements LeaveInterface{

    private static $instance = null;

    public $today;

    public $thisYear;


    //任务 每年初始化调用
    public static function getInstance($lang,$timezone){
        if(!self::$instance instanceof self){
            self::$instance = new self($lang,$timezone);
        }
        return self::$instance;
    }


    //撤销返还 印尼可以多次申请 所以不能直接全部返还 后来改成 一年只能申请一次 方法保留(历史数据处理还得用这个)
    public function returnRemainDays($auditId, $staffInfo, $extend = [])
    {
        $this->staffInfo = $staffInfo;

        //获取应有额度
        $this->leaveProperty = StaffLeaveProperty::findFirst([
            'conditions' => "leave_type = :leave_type: and is_delete = 0",
            'bind'       => ['leave_type' => enums::LEAVE_TYPE_4],
        ]);

        $splitInfo = $this->getUsedDays($auditId);

        //没有拆分表额度信息
        if (empty($splitInfo)) {
            $this->logger->write_log("拆分表信息异常 {$this->staffInfo['staff_info_id']} {$auditId}", 'info');
            return true;
        }

        $remainData = StaffLeaveRemainDaysModel::find([
            'column'     => 'staff_info_id,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycle:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_4,
                'cycle'      => array_keys($splitInfo),
            ],
        ]);


        if (empty($remainData->toArray())) {
            $this->logger->write_log("额度表信息异常 {$this->staffInfo['staff_info_id']} {$auditId} ".json_encode($splitInfo),
                'info');
            return true;
        }

        foreach ($remainData as $remain) {
            $needBackDays = $splitInfo[$remain->year] ?? 0;//需要返还的 额度
            //使用额度 减少
            $remain->days       += $needBackDays;
            $remain->leave_days -= $needBackDays;
            $remain->update();
        }

        $this->logger->write_log("额度返还成功 {$this->staffInfo['staff_info_id']} {$auditId} ".json_encode($splitInfo), 'info');
    }


}