<?php

namespace FlashExpress\bi\App\Modules\Id\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditLogModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeAccidentBusinessModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\WorkflowServer as GlobalBaseServer;

class WorkflowServer extends GlobalBaseServer
{

    /**
     * 获取申请的审批日志
     * @param $request
     * @param $user
     * @return array
     * @throws InnerException
     */
    public function getAuditLogs($request, $user)
    {
        $id_staff_info_id_hide = [56780];
        $data                  = [];
        if (empty($request)) {
            $this->logger->write_log("getAuditLogs request 不存在! ", 'info');
            return $data;
        }
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        //切表
        $logs = $this->getAuditLogDetail($request);
        if ($logs) {
            $t        = $this->getTranslation();
            $staffIds = array_column($logs, 'staff_id');
            $start    = array_shift($logs);


            $builder = $this->modelsManager->createBuilder();
            $builder->columns('hsi.staff_info_id,
                hsi.name,
                hsi.nick_name,
                hsi.job_title_grade_v2,
                hsi.job_title,
                hjt.job_name,
                sd.name as department_name');
            $builder->from(['hsi' => HrStaffInfoModel::class]);
            $builder->leftjoin(HrJobTitleModel::class, 'hjt.id = hsi.job_title', 'hjt');
            $builder->leftjoin(SysDepartmentModel::class, 'sd.id = hsi.node_department_id', 'sd');
            $builder->inWhere('hsi.staff_info_id', $staffIds);
            $staffInfoArr = $builder->getQuery()->execute()->toArray();

            $jobTitleIdList = array_column($staffInfoArr, 'job_title', 'staff_info_id');

            //获取需要隐藏的职位
            //Human Capital 职组 id = 15
            $staffNeedToHide = $this->getHideStaffInfo($staffIds, $jobTitleIdList);
            $id_staff_info_id_hide = array_merge($id_staff_info_id_hide, $staffNeedToHide);

            //申请节点处理
            $staff_id = $start['staff_id'];
            $name     = $start['staff_name'];
            if (isCountry('ID')) {
                $name     = in_array($staff_id, $id_staff_info_id_hide) ? '' : $name;
                $staff_id = in_array($staff_id, $id_staff_info_id_hide) ? '' : $staff_id;
            }
            $data[] = [
                'staff_id'     => $staff_id,
                'name'         => $name,
                'position'     => $start['staff_job_title_name'],
                'department'   => $start['staff_department'],
                'status'       => $t->_('flow_audit_action.0'),
                'status_code'  => 8,
                'time'         => gmdate("Y-m-d H:i:s", strtotime($start['audit_at']) + $add_hour * 3600),
                'times_tamp'   => (string)strtotime(gmdate("Y-m-d H:i:s",
                    strtotime($start['audit_at']) + $add_hour * 3600)), // 时间戳
                'remark'       => '',
                "is_ok"        => 0,
                "audit_info"   => '',
                'is_show_icon' => 1,
            ];

            // 已经展示的节点 和 审批人
            $already_displayed_nodes = [];

            //已经处理的log
            foreach ($logs as $log) {
                $auditInfo = "";
                $state_txt = $t->_(Enums::$approval_status[$log['audit_action']]);
                //如果是撤销审批申请
                if ($request->submitter_id == $start['staff_id'] && $request->is_cancel > 0 && $log['audit_action'] == Enums::APPROVAL_STATUS_PENDING) {
                    $state_txt = $t->_(Enums::$approval_status[Enums::WF_ACTION_CREATE_CANCEL]);
                }

                if (!empty($log['audit_action']) && in_array($log['audit_action'], Enums::$wf_action_need_parse)) {
                    if (strpos($log['audit_info'], '|')) {
                        $info      = explode('|', $log['audit_info']);
                        $auditInfo = str_replace("{x}", $info[1], $t->_($info[0]));
                    } else {
                        $auditInfo = $t->_($log['audit_info']);
                    }
                }
                $auditInfo = !empty($auditInfo) ? $auditInfo : $log['audit_info'];
                if ($log['audit_action'] == enums::WF_ACTION_CC) {
                    $auditInfo = '';
                }

                $staff_id = $log['staff_id'];
                $name     = $log['staff_name'];
                if (isCountry('ID')) {
                    $name     = in_array($staff_id, $id_staff_info_id_hide) ? '' : $name;
                    $staff_id = in_array($staff_id, $id_staff_info_id_hide) ? '' : $staff_id;
                }
                $data[] = [
                    'staff_id'     => $staff_id,
                    'name'         => $name,
                    'department'   => $log['staff_department'],
                    'position'     => $log['staff_job_title_name'],
                    'status'       => $state_txt,
                    'status_code'  => $log['audit_action'],
                    'time'         => gmdate("Y-m-d H:i:s", strtotime($log['audit_at']) + $add_hour * 3600),
                    'times_tamp'   => (string)strtotime(gmdate("Y-m-d H:i:s",
                        strtotime($log['audit_at']) + $add_hour * 3600)), // 时间戳
                    'remark'       => $auditInfo,                         //与audit_info统一
                    "audit_info"   => $auditInfo,                         //与remark统一
                    "is_ok"        => 0,
                    'is_show_icon' => array_key_exists($log['flow_node_id'], $already_displayed_nodes) ? 0 : 1,
                ];

                $already_displayed_nodes[$log['flow_node_id']][] = $log['staff_id'];
            }

            //待审批的log
            if ($request->getState() == Enums::APPROVAL_STATUS_PENDING
            ) {
                $submitter = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :uid:',
                    'bind'       => [
                        'uid' => $start['staff_id'],
                    ],
                ]);
                $current   = $this->getCurrentAuditor($request, $submitter->staff_info_id ?? '');
                if ($current) {
                    // 获取当前节点类型: 会签类型展示样式单独处理
                    $current_node = WorkflowNodeModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind'       => [
                            'id' => $request->current_flow_node_id,
                        ],
                    ]);
                    $jobTitleIdList = array_column($current, 'job_title', 'staff_info_id');

                    //获取需要隐藏的职位
                    //Human Capital 职组 id = 15
                    $staffNeedToHide = $this->getHideStaffInfo(array_column($current, 'staff_info_id'), $jobTitleIdList);
                    $id_staff_info_id_hide = array_merge($id_staff_info_id_hide, $staffNeedToHide);

                    // 会签节点: 日志样式单独调整
                    if ($current_node->type == enums::NODE_COUNTERSIGN) {
                        foreach ($current as $key => $item) {
                            // 如果当前节点，当前人已审批，则剔除
                            $current_node_already_auditor_ids = $already_displayed_nodes[$request->current_flow_node_id] ?? [];
                            if (!empty($current_node_already_auditor_ids) && in_array($item['staff_info_id'],
                                    $current_node_already_auditor_ids)) {
                                continue;
                            }

                            $staff_id = $item['staff_info_id'] ?? '';
                            $name     = $item['staff_name'] ?? '';
                            if (isCountry('ID')) {
                                $name     = in_array($staff_id, $id_staff_info_id_hide) ? '' : $name;
                                $staff_id = in_array($staff_id, $id_staff_info_id_hide) ? '' : $staff_id;
                            }
                            $approval = [
                                'staff_id'    => $staff_id,
                                'name'        => $name,
                                'department'  => $item['department_name'] ?? '',
                                'position'    => $item['job_title_name'],
                                'status'      => $t->_('audit_status.'.$request->getState()),
                                'status_code' => $request->getState(),
                                'time'        => null,
                                'times_tamp'  => '',
                                'remark'      => '',
                                "is_ok"       => 1,
                                "audit_info"  => "",
                            ];

                            // 如果当前节点还没有审批, 那么只有第一个待审批人前面的圆点需要展示
                            if (empty($already_displayed_nodes)) {
                                $approval['is_show_icon'] = $key == 0 ? 1 : 0;
                            } else {
                                $approval['is_show_icon'] = array_key_exists($request->current_flow_node_id,
                                    $already_displayed_nodes) ? 0 : 1;
                            }

                            array_push($data, $approval);
                        }
                    } else {
                        $staffs   = [];
                        $staffIds = [];
                        foreach ($current as $item) {
                            $staff_id = $item['staff_info_id'] ?? '';
                            $name     = $item['staff_name'] ?? '';
                            if (isCountry('ID')) {
                                $name     = in_array($staff_id, $id_staff_info_id_hide) ? '' : $name;
                                $staff_id = in_array($staff_id, $id_staff_info_id_hide) ? '' : $staff_id;
                            }

                            $staffIds[] = $item['staff_info_id'];
                            $staffs     = [
                                'staff_id'   => $staff_id,
                                'name'       => $name,
                                'department' => $item['department_name'] ?? '',
                                'position'   => $item['job_title_name'],
                            ];
                        }
                        $approval = [
                            'status'       => $t->_('audit_status.'.$request->getState()),
                            'status_code'  => $request->getState(),
                            'time'         => null,
                            'times_tamp'   => '',
                            'remark'       => '',
                            "is_ok"        => 1,
                            "audit_info"   => "",
                            'is_show_icon' => 1,
                        ];
                        $approval = array_merge($approval, $staffs);
                        array_push($data, $approval);
                    }
                }
            }

            $staffInfoArrs = array_column($data, 'staff_id');

            //追加昵称字段
            $staffArr = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in({staffs:array})',
                'bind'       => ['staffs' => $staffInfoArrs],
                'columns'    => ['staff_info_id', 'nick_name'],
            ])->toArray();
            $staffArr = array_column($staffArr, 'nick_name', 'staff_info_id');
            foreach ($data as $k => $v) {
                $data[$k]['name'] = isset($staffArr[$v['staff_id']]) && $staffArr[$v['staff_id']]
                    ? $v['name']."({$staffArr[$v['staff_id']]})"
                    : $v['name'];
                if (isCountry('ID')) {
                    $data[$k]['name'] = in_array($staff_id, $id_staff_info_id_hide) ? '' : $data[$k]['name'];
                }
            }
        }
        return $data;
    }

    /**
     * 获取申请的审批日志
     * @param $request
     * @param $user
     * @param int $datetime_formatter
     * @return array
     * @throws InnerException
     * @throws \Exception
     */
    public function getAuditLogsV2($request, $user, int $datetime_formatter = AuditListEnums::DATETIME_FORMATTER_DEFAULT): array
    {
        if(empty($request)){
            $this->logger->write_log("getAuditLogs request 不存在! ",'info');
            return [];
        }

        // 获取日志数据
        $logs = $this->getAuditLogDetail($request);
        if (empty($logs)) {
            return [];
        }

        $staffRepository       = new StaffRepository();
        $t                     = $this->getTranslation();
        $alreadyDisplayedNodes = [];                                            // 已经展示的节点和审批人
        $nickNameList          = [];                                            //员工昵称
        $staffJobTitleGrade    = [];                                            //员工职级
        $staffIdsFromLogs      = array_column($logs, 'staff_id');    //获取审批流中全部员工ID
        $startNodeLog          = $logs[0];
        $nodeIds               = array_column($logs, 'flow_node_id');//获取全部节点
        $nodeIds               = array_merge($nodeIds, [$request->current_flow_node_id]);
        $staffNeedToHide       = [];

        $nodeInfo = $this->getNodeByIds($nodeIds);
        $nodeMark = array_column($nodeInfo, 'node_mark', 'id');
        $nodeInfo = array_column($nodeInfo, 'type', 'id');
        [$ccFront, $ccBack, $ccNodeStaffIds] = $this->getCcNodeStaffIds($nodeIds);

        if ($request->getState() == Enums::APPROVAL_STATUS_PENDING) { //待审批工号
            $currentNodeApprovalIds = $this->getSpecifyNodeStaffIds($request->current_flow_node_id, $startNodeLog['staff_id'] ?? '');
        }
        $staffIds = array_merge($staffIdsFromLogs ?? [], $currentNodeApprovalIds ?? [], $ccNodeStaffIds ?? []);
        $staffIds = array_values(array_unique(array_filter($staffIds)));
        if (!empty($staffIds)) { //追加昵称字段

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('hsi.staff_info_id,
                hsi.name,
                hsi.nick_name,
                hsi.job_title_grade_v2,
                hsi.job_title,
                hjt.job_name,
                sd.name as department_name');
            $builder->from(['hsi' => HrStaffInfoModel::class]);
            $builder->leftjoin(HrJobTitleModel::class, 'hjt.id = hsi.job_title', 'hjt');
            $builder->leftjoin(SysDepartmentModel::class, 'sd.id = hsi.node_department_id', 'sd');
            $builder->inWhere('hsi.staff_info_id', $staffIds);
            $staffInfoArr = $builder->getQuery()->execute()->toArray();

            $staffNameList = array_column($staffInfoArr, 'name', 'staff_info_id');
            $nickNameList = array_column($staffInfoArr, 'nick_name', 'staff_info_id');
            $staffJobTitleGrade = array_column($staffInfoArr, 'job_title_grade_v2', 'staff_info_id');
            $jobTitleList = array_column($staffInfoArr, 'job_name', 'staff_info_id');
            $jobTitleIdList = array_column($staffInfoArr, 'job_title', 'staff_info_id');
            $deptList = array_column($staffInfoArr, 'department_name', 'staff_info_id');

            //获取需要隐藏的职位
            //Human Capital 职组 id = 15
            $staffNeedToHide = $this->getHideStaffInfo($staffIds, $jobTitleIdList);
        }

        $params = [
            'nick_name'             => $nickNameList ?? [],
            'staff_name'            => $staffNameList ?? [],
            'staff_job_title_name'  => $jobTitleList ?? [],
            'staff_department'      => $deptList ?? [],
            'staff_job_title_grade' => $staffJobTitleGrade ?? [],
            'staff_need_to_hide'    => $staffNeedToHide ?? [],
        ];
        $independentAction = [enums::APPROVAL_STATUS_CANCEL, enums::WF_ACTION_APPROVAL_SUPERIOR];

        //已经处理的log
        foreach ($logs as $log) {

            $uniqueKey = $log['flow_node_id'];
            $auditInfo = "";

            //如果是撤销审批申请
            if($request->submitter_id == $log['staff_id'] && $request->is_cancel > 0 && $log['audit_action'] == Enums::WF_ACTION_CREATE_CANCEL) {
                $state_txt = $t->_(Enums::$wf_action_status[Enums::WF_ACTION_CREATE_CANCEL]);
            } else {
                $state_txt = $t->_(Enums::$wf_action_status[$log['audit_action']]);
            }

            if (!empty($log['audit_action']) && in_array($log['audit_action'], Enums::$wf_action_need_parse)) {
                if (strpos($log['audit_info'], '|')) {
                    $info = explode('|', $log['audit_info']);
                    $auditInfo = str_replace("{x}", $info[1], $t->_($info[0]));
                } else {
                    $auditInfo = $t->_($log['audit_info']);
                }
            }
            $auditInfo = !empty($auditInfo) ? $auditInfo: $log['audit_info'];
            if ($log['audit_action'] == enums::WF_ACTION_CC) {
                $auditInfo = '';
            }

            $approvalInfo = [
                'position'    => $log['staff_job_title_name'],
                'department'  => $log['staff_department'],
                'sort'        => intval($staffJobTitleGrade[$log['staff_id']]) ?? 0,
                'state_txt'   => $state_txt,
                'status_code' => $log['audit_action'],
                'audit_info'  => $auditInfo,
            ];
            if (in_array($log['staff_id'], $staffNeedToHide)) {
                $approvalInfo = array_merge($approvalInfo, [
                    'staff_id'    => "",
                    'staff_name'  => "",
                ]);
            } else {
                $approvalInfo = array_merge($approvalInfo, [
                    'staff_id'    => $log['staff_id'],
                    'staff_name'  => $this->formatStaffName($log['staff_name'], $nickNameList[$log['staff_id']] ?? ''),
                ]);
            }

            if (isset($data[$uniqueKey]) && !in_array($log['audit_action'], $independentAction)) { //撤销节点要独立为一个节点

                $data[$uniqueKey]["approval_info"][] = $approvalInfo;
                $data[$uniqueKey]["action_time"] = $this->formatDateTime($log['audit_at'], $datetime_formatter);
                $data[$uniqueKey]["status_code"] = $log['audit_action'];
            } else {

                //撤销节点要独立为一个节点
                if (in_array($log['audit_action'], $independentAction)) {
                    $uniqueKey = $uniqueKey . '-' . $log['audit_action'];
                }

                $data[$uniqueKey] = [
                    'state_txt'         => $this->formatStateCodeTxt($log['audit_action'], $log['flow_node_id'], $nodeMark),
                    'status_code'       => $log['audit_action'],
                    'action_time'       => $this->formatDateTime($log['audit_at'], $datetime_formatter),
                    "approval_type"     => intval($log['audit_action'] != enums::WF_ACTION_CANCEL ? ($nodeInfo[$log['flow_node_id']] ?? 0) : 0), //或签 会签 抄送
                    "process_state"     => 1,
                    "approval_info"     => [$approvalInfo],
                ];
            }
            $alreadyDisplayedNodes[$uniqueKey][] = $log['staff_id'];

            //会签同意+判断一下当前节点是否审批完成
            //或签同意肯定抄送
            //驳回操作+同意/驳回操作都抄送
            if (isset($ccBack[$uniqueKey]) && $ccBack[$uniqueKey] &&
                (in_array($log['audit_action'], [enums::WF_ACTION_APPROVE, enums::WF_ACTION_APPROVAL_EMPTY]) ||
                    $log['audit_action'] == enums::WF_ACTION_APPROVE_COUNTERSIGN && $request->current_flow_node_id != $log['flow_node_id'] ||
                    in_array($log['audit_action'], [
                        enums::APPROVAL_STATUS_REJECTED,
                        enums::APPROVAL_STATUS_REJECTED_ROLL_BACK,
                        enums::APPROVAL_STATUS_APPROVAL_EMPTY_AUTO_REJECT,
                    ]) &&
                    $ccBack[$uniqueKey]['type'] == WorkflowNodeAccidentBusinessModel::CC_BEFORE_NODE
                )
            ) {
                $data[$uniqueKey . '-1'] = $this->generateCcLog($uniqueKey, $ccBack, $params);
            }
        }

        //待审批的log
        if ($request->getState() == Enums::APPROVAL_STATUS_PENDING) {
            $current = $this->getCurrentAuditorV2($currentNodeApprovalIds ?? []);
            if (empty($current)) {
                return [];
            }

            // 获取当前节点类型: 会签类型展示样式单独处理
            $currentNode = WorkflowNodeModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $request->current_flow_node_id,
                ],
            ]);
            if (empty($currentNode)) {
                $this->logger->write_log(sprintf('workflow node id %s do not exist!', $request->current_flow_node_id));
                throw new \Exception("workflow node data error, please check!");
            }

            //标记审核
            $stateTxt = $currentNode->getNodeMark() == WorkflowNodeModel::NODE_MARK_AUDIT
                ? $t->_('audit_status.' . Enums::WF_ACTION_AUDIT_PENDING)
                : $t->_('audit_status.' . Enums::WF_ACTION_PENDING);

            if ($currentNode->type == enums::NODE_COUNTERSIGN) {
                foreach ($current as $key => $item) {
                    // 如果当前节点，当前人已审批，则剔除
                    $currentNodeAlreadyAuditorIds = $alreadyDisplayedNodes[$request->current_flow_node_id] ?? [];
                    if (!empty($currentNodeAlreadyAuditorIds) && in_array($item['staff_info_id'], $currentNodeAlreadyAuditorIds)) {
                        continue;
                    }
                    $currentPending = [
                        'position'    => $item['job_title_name'],
                        'department'  => $item['department_name'],
                        'sort'        => intval($staffJobTitleGrade[$item['staff_info_id']]) ?? 0,
                        'state_txt'   => $stateTxt,
                        'status_code' => $request->getState(),
                        'audit_info'  => '',
                    ];
                    if (in_array($item['staff_info_id'], $staffNeedToHide)) {
                        $currentPending = array_merge($currentPending, [
                            'staff_id'    => "",
                            'staff_name'  => "",
                        ]);
                    } else {
                        $currentPending = array_merge($currentPending, [
                            'staff_id'    => $item['staff_info_id'] ?? '',
                            'staff_name'  => $this->formatStaffName($item['staff_name'], $nickNameList[$item['staff_info_id']] ?? ''),
                        ]);
                    }
                    $approval[] = $currentPending;
                }

                if (isset($data[$request->current_flow_node_id])) {

                    $approval = array_merge($data[$request->current_flow_node_id]["approval_info"], $approval);
                    $data[$request->current_flow_node_id] = [
                        'state_txt'         => $this->formatStateCodeTxt($request->getState(), $request->getCurrentFlowNodeId(), $nodeMark),
                        'status_code'       => $request->getState(),
                        'action_time'       => null,
                        "process_state"     => 1,
                        "approval_type"     => intval($nodeInfo[$request->current_flow_node_id] ?? 0), //或签 会签 抄送
                        "approval_info"     => $approval ?? []
                    ];
                } else {
                    $data[$request->current_flow_node_id] = [
                        'state_txt'         => $this->formatStateCodeTxt($request->getState(), $request->getCurrentFlowNodeId(), $nodeMark),
                        'status_code'       => $request->getState(),
                        'action_time'       => null,
                        "process_state"     => 1,
                        "approval_type"     => intval($nodeInfo[$request->current_flow_node_id] ?? 0), //或签 会签 抄送
                        "approval_info"     => $approval ?? []
                    ];
                }

            } else {
                foreach ($current as $item) {

                    $currentPending = [
                        'position'    => $item['job_title_name'],
                        'department'  => $item['department_name'] ?? '',
                        'sort'        => intval($staffJobTitleGrade[$item['staff_info_id']]) ?? 0,
                        'state_txt'   => $stateTxt,
                        'status_code' => 1,
                        "audit_info"  => "",
                    ];
                    if (in_array($item['staff_info_id'], $staffNeedToHide)) {
                        $currentPending = array_merge($currentPending, [
                            'staff_id'    => "",
                            'staff_name'  => "",
                        ]);
                    } else {
                        $currentPending = array_merge($currentPending, [
                            'staff_id'    => $item['staff_info_id'] ?? '',
                            'staff_name'  => $this->formatStaffName($item['staff_name'], $nickNameList[$item['staff_info_id']] ?? ''),
                        ]);
                    }
                    $approval[] = $currentPending;
                }

                if (isset($data[$request->current_flow_node_id])) {

                    $approval = array_merge($data[$request->current_flow_node_id]["approval_info"], $approval ?? []);
                    $data[$request->current_flow_node_id] = [
                        'state_txt'         => $this->formatStateCodeTxt($request->getState(), $request->getCurrentFlowNodeId(), $nodeMark),
                        'status_code'       => $request->getState(),
                        'action_time'       => null,
                        "approval_type"     => intval($nodeInfo[$request->current_flow_node_id] ?? 0), //或签 会签 抄送
                        "process_state"     => 1,
                        "approval_info"     => $approval ?? []
                    ];
                } else {
                    $data[$request->current_flow_node_id] = [
                        'state_txt'     => $this->formatStateCodeTxt($request->getState(), $request->getCurrentFlowNodeId(), $nodeMark),
                        'status_code'   => $request->getState(),
                        'action_time'   => null,
                        'approval_type' => intval($nodeInfo[$request->current_flow_node_id] ?? 0), //或签 会签 抄送
                        "process_state" => 1,
                        'approval_info' => $approval ?? [],
                    ];
                }

            }
        }

        foreach ($data as &$item) {
            $count = count($item['approval_info']);
            if ($count > 1) {

                //如果同时存在同意的 + 待审批的，那么
                //已经审批同意的sort追加 100在排序因为
                //已经同意的在最上面
                $keySort = array_map(function ($v, $k) use($count) {
                    if (!in_array($v['status_code'], [enums::WF_ACTION_PENDING, enums::WF_ACTION_CC])) {
                        $v['sort'] = ($count - $k) * 100;
                    }
                    return $v;
                }, $item['approval_info'], array_keys($item['approval_info']));

                $keySort = array_column($keySort, 'sort');
                array_multisort($keySort, SORT_NUMERIC, SORT_DESC, $item['approval_info']);
            }
        }

        return array_values($data);
    }

    /**
     * 获取申请的审批日志
     * @param $request
     * @param $user
     * @param int $datetime_formatter
     * @return array
     * @throws InnerException
     * @throws \Exception
     */
    public function getAuditLogsV3($request, $user, int $datetime_formatter = AuditListEnums::DATETIME_FORMATTER_DEFAULT)
    {
        $data = $this->publicLog($request, $user, $datetime_formatter);
        if(empty($data)){
            return [];
        }
        if (in_array($request->getBizType(), [AuditListEnums::APPROVAL_TYPE_LE, AuditListEnums::APPROVAL_TYPE_BT,])) {
            $data = $this->chunkLogs($request, $data);
        }
        return array_values($data);
    }

    /**
     * 获取申请的审批日志
     * @param $request
     * @param $user
     * @param int $datetime_formatter
     * @return array
     * @throws InnerException
     * @throws \Exception
     */
    public function getAuditLogsV4($request, $user, int $datetime_formatter = AuditListEnums::DATETIME_FORMATTER_DEFAULT)
    {
        $data = $this->publicLog($request, $user, $datetime_formatter);
        if(empty($data)){
            return [];
        }

        if (in_array($request->getBizType(), [AuditListEnums::APPROVAL_TYPE_LE,AuditListEnums::APPROVAL_TYPE_BT,])) {
            $data = $this->chunkLogsV4($request, $data);
        }
        return array_values($data);
    }

    public function publicLog($request, $user, int $datetime_formatter = AuditListEnums::DATETIME_FORMATTER_DEFAULT)
    {
        if(empty($request)){
            $this->logger->write_log("getAuditLogs request 不存在! ",'info');
            return [];
        }

        // 获取日志数据
        $logs = $this->getAuditLogDetail($request);
        if (empty($logs)) {
            return [];
        }

        $t                     = $this->getTranslation();
        $alreadyDisplayedNodes = [];                              //已经展示的节点和审批人
        $nickNameList          = [];                              //员工昵称
        $staffJobTitleGrade    = [];                              //员工职级
        $startNodeLog          = $logs[0];
        $staffIdsFromLogs      = array_column($logs, 'staff_id');
        $staffNeedToHide       = [];

        //获取全部节点
        $nodeInfo = $this->getFlowNodes($request->getFlowId())->toArray();

        $totalStaffAuditor = array_column($nodeInfo, 'auditor_id');
        $nodeIds           = array_column($nodeInfo, 'id');
        $nodeMark          = array_column($nodeInfo, 'node_mark', 'id');
        $nodeInfo          = array_column($nodeInfo, 'type', 'id');
        [$ccFront, $ccBack, $ccNodeStaffIds] = $this->getCcNodeStaffIds($nodeIds);

        //1. 获取一条完整审批流上的申请人、审批人、抄送人
        if ($request->getState() == Enums::APPROVAL_STATUS_PENDING) { //待审批工号
            $currentNodeApprovalIds = $this->getSpecifyNodeStaffIds($request->getCurrentFlowNodeId(), $startNodeLog['staff_id'] ?? '');
        }
        $totalStaffAuditorInfo = array_map(function ($v) {
            return !empty($v) ? explode(',', $v) : [];
        }, $totalStaffAuditor);
        $totalStaffAuditorInfo = array_values(array_unique(array_merge(...$totalStaffAuditorInfo)));

        $staffIds = array_merge($totalStaffAuditorInfo, $ccNodeStaffIds ?? [], $staffIdsFromLogs);
        $staffIds = array_values(array_unique(array_filter($staffIds)));
        if (!empty($staffIds)) {
            $staffRepository    = new StaffRepository();
            $staffInfoArr       = $staffRepository->getSpecifyStaffInfo($staffIds);
            $staffNameList      = array_column($staffInfoArr, 'staff_name', 'staff_info_id');
            $nickNameList       = array_column($staffInfoArr, 'nick_name', 'staff_info_id');
            $staffJobTitleGrade = array_column($staffInfoArr, 'job_title_grade_v2', 'staff_info_id');
            $jobTitleList       = array_column($staffInfoArr, 'job_name', 'staff_info_id');
            $deptList           = array_column($staffInfoArr, 'department_name', 'staff_info_id');
            $jobTitleIdList     = array_column($staffInfoArr, 'job_title', 'staff_info_id');

            //获取需要隐藏的职位
            //Human Capital 职组 id = 15
            $staffNeedToHide = $this->getHideStaffInfo($staffIds, $jobTitleIdList);
        }
        $params = [
            'nick_name'             => $nickNameList ?? [],
            'staff_name'            => $staffNameList ?? [],
            'staff_job_title_name'  => $jobTitleList ?? [],
            'staff_department'      => $deptList ?? [],
            'staff_job_title_grade' => $staffJobTitleGrade ?? [],
            'staff_need_to_hide'    => $staffNeedToHide ?? [],
        ];
        //撤销操作、上级离职转交需要展示为独立的节点
        $independentAction = [enums::WF_ACTION_CANCEL, enums::WF_ACTION_APPROVAL_SUPERIOR];

        //2. 根据审批日志，整理已经审批完部分
        foreach ($logs as $log) {
            //审批流节点ID作为唯一键值，用于聚合同一节点下的多个审批人或抄送人
            $uniqueKey = $log['flow_node_id'];
            //撤销节点要独立为一个节点
            if (in_array($log['audit_action'], $independentAction)) {
                $uniqueKey = $uniqueKey . '-' . $log['audit_action'];
            }
            //用于保存自动审批、自动驳回、自动转交等系统操作的情况时，应该给予的提示语
            $auditInfo = "";

            //申请人 & 申请撤销流程 & 创建撤销动作
            //提示语为：撤销发起审批
            if($request->getSubmitterId() == $log['staff_id'] &&
                $request->getIsCancel() > AuditApplyModel::AUDIT_SEQUENCE_NORMAL &&
                $log['audit_action'] == Enums::WF_ACTION_CREATE_CANCEL
            ) {
                $state_txt = $t->_(Enums::$wf_action_status[Enums::WF_ACTION_CREATE_CANCEL]);
            } else {
                $state_txt = $t->_(Enums::$wf_action_status[$log['audit_action']]);
            }

            //处理特殊提示语
            //例如: 部门负责人找不到的情况，需要提示"审批流中缺少{xxxx}部门负责人信息，请联系HR处理"
            //数据以 "error_message_translation_key|department_id" 格式保存数据, [ err_msg_wf_department_manager_not_exist｜4  ]
            if (!empty($log['audit_action']) && in_array($log['audit_action'], Enums::$wf_action_need_parse)) {
                if (strpos($log['audit_info'], '|')) {
                    $info = explode('|', $log['audit_info']);
                    $auditInfo = str_replace("{x}", $info[1], $t->_($info[0]));
                } else {
                    $auditInfo = $t->_($log['audit_info']);
                }
            }
            $auditInfo = !empty($auditInfo) ? $auditInfo: $log['audit_info'];
            if ($log['audit_action'] == enums::WF_ACTION_CC){
                $auditInfo = '';
            }

            $approvalInfo = [
                'position'    => $log['staff_job_title_name'],
                'department'  => $log['staff_department'],
                'sort'        => intval($staffJobTitleGrade[$log['staff_id']]) ?? 0,
                'state_txt'   => $state_txt,
                'status_code' => $log['audit_action'],
                'audit_info'  => $auditInfo,
            ];
            if (in_array($log['staff_id'], $staffNeedToHide)) {
                $approvalInfo = array_merge($approvalInfo, [
                    'staff_id'    => "",
                    'staff_name'  => "",
                ]);
            } else {
                $approvalInfo = array_merge($approvalInfo, [
                    'staff_id'    => $log['staff_id'],
                    'staff_name'  => $this->formatStaffName($log['staff_name'], $nickNameList[$log['staff_id']] ?? ''),
                ]);
            }
            if (isset($data[$uniqueKey]) && !in_array($log['audit_action'], $independentAction)) {
                $data[$uniqueKey]["approval_info"][] = $approvalInfo;
                $data[$uniqueKey]["action_time"] = $this->formatDateTime($log['audit_at'], $datetime_formatter);
                $data[$uniqueKey]["status_code"] = $log['audit_action'];
            } elseif (isset($data[$uniqueKey]) && $log['audit_action'] == enums::WF_ACTION_APPROVAL_SUPERIOR) {
                $data[$uniqueKey]["approval_info"][] = $approvalInfo;
                $data[$uniqueKey]["action_time"] = $this->formatDateTime($log['audit_at'], $datetime_formatter);
                $data[$uniqueKey]["status_code"] = $log['audit_action'];
            } else {
                $data[$uniqueKey] = [
                    'state_txt'         => $this->formatStateCodeTxt($log['audit_action'], $log['flow_node_id'], $nodeMark),
                    'status_code'       => $log['audit_action'],
                    'action_time'       => $this->formatDateTime($log['audit_at'], $datetime_formatter),
                    "approval_type"     => intval($log['audit_action'] != enums::WF_ACTION_CANCEL ? ($nodeInfo[$log['flow_node_id']] ?? 0) : 0), //或签 会签 抄送
                    "process_state"     => AuditLogModel::PROCESS_BAR_HIGHLIGHT,
                    "approval_info"     => [$approvalInfo],
                ];
            }
            $alreadyDisplayedNodes[$uniqueKey][] = $log['staff_id'];

            //处理节点后抄送,需要当前节点审批同意
            //或签同意、自动通过、会签完全同意、驳回操作(不勾选仅同意抄送)
            if (isset($ccBack[$uniqueKey]) && $ccBack[$uniqueKey] &&
                (in_array($log['audit_action'], [enums::WF_ACTION_APPROVE, enums::WF_ACTION_APPROVAL_EMPTY]) ||
                    $log['audit_action'] == enums::WF_ACTION_APPROVE_COUNTERSIGN && $request->getCurrentFlowNodeId() != $log['flow_node_id'] ||
                    $log['audit_action'] == enums::WF_ACTION_REJECT && $ccBack[$uniqueKey]['type'] == WorkflowNodeAccidentBusinessModel::CC_TYPE_NORMAL
                ) && in_array($ccBack[$uniqueKey]['status'], [WorkflowNodeAccidentBusinessModel::STATUS_TO_BE_PROCESSED, WorkflowNodeAccidentBusinessModel::STATUS_HAS_PROCESSED])
            ) {
                $params['cc_time']       = $this->formatDateTime($log['audit_at'], $datetime_formatter);
                $params['process_state'] = AuditLogModel::PROCESS_BAR_HIGHLIGHT;
                $data[$uniqueKey . '-1'] = $this->generateCcLog($uniqueKey, $ccBack, $params);
            }
        }

        //3. 如果未审批完成，整理未审批完部分
        if ($request->getState() == Enums::APPROVAL_STATUS_PENDING) {

            $current = $this->getCurrentAuditorV2($currentNodeApprovalIds ?? []);
            if (empty($current)) {
                return [];
            }

            // 获取当前节点类型: 会签类型展示样式单独处理
            $currentNodeInfo = $this->getNodeByIds([$request->getCurrentFlowNodeId()]);
            $currentNode     = !empty($currentNodeInfo) ? current($currentNodeInfo) : [];
            if (empty($currentNode)) {
                $this->logger->write_log(sprintf('workflow node id %s do not exist!', $request->getCurrentFlowNodeId()));
                throw new \Exception("workflow node data error, please check!");
            }

            //标记审核
            $stateTxt = $currentNode['node_mark'] == WorkflowNodeModel::NODE_MARK_AUDIT
                ? $t->_('audit_status.' . Enums::WF_ACTION_AUDIT_PENDING)
                : $t->_('audit_status.' . Enums::WF_ACTION_PENDING);

            if ($currentNode['type'] == enums::NODE_COUNTERSIGN) {
                foreach ($current as $key => $item) {
                    // 如果当前节点，当前人已审批，则剔除
                    $currentNodeAlreadyAuditorIds = $alreadyDisplayedNodes[$request->getCurrentFlowNodeId()] ?? [];
                    if (!empty($currentNodeAlreadyAuditorIds) && in_array($item['staff_info_id'], $currentNodeAlreadyAuditorIds)) {
                        continue;
                    }

                    $currentPending = [
                        'position'    => $item['job_title_name'],
                        'department'  => $item['department_name'],
                        'sort'        => intval($staffJobTitleGrade[$item['staff_info_id']]) ?? 0,
                        'state_txt'   => $t->_('audit_status.' . $request->getState()),
                        'status_code' => $request->getState(),
                        'audit_info'  => '',
                    ];
                    if (in_array($item['staff_info_id'], $staffNeedToHide)) {
                        $currentPending = array_merge($currentPending, [
                            'staff_id'    => "",
                            'staff_name'  => "",
                        ]);
                    } else {
                        $currentPending = array_merge($currentPending, [
                            'staff_id'    => $item['staff_info_id'] ?? '',
                            'staff_name'  => $this->formatStaffName($item['staff_name'], $nickNameList[$item['staff_info_id']] ?? ''),
                        ]);
                    }
                    $approval[] = $currentPending;
                }

                if (isset($data[$request->getCurrentFlowNodeId()])) {
                    $approval = array_merge($data[$request->getCurrentFlowNodeId()]["approval_info"], ($approval ?? []));
                }
                $data[$request->getCurrentFlowNodeId()] = [
                    'state_txt'         => $this->formatStateCodeTxt($request->getState(), $request->getCurrentFlowNodeId(), $nodeMark),
                    'status_code'       => $request->getState(),
                    'action_time'       => null,
                    "process_state"     => AuditLogModel::PROCESS_BAR_HIGHLIGHT,
                    "approval_type"     => intval($nodeInfo[$request->getCurrentFlowNodeId()] ?? 0), //或签 会签 抄送
                    "approval_info"     => $approval ?? []
                ];
            } else {
                foreach ($current as $item) {
                    $currentPending = [
                        'position'    => $item['job_title_name'],
                        'department'  => $item['department_name'] ?? '',
                        'sort'        => intval($staffJobTitleGrade[$item['staff_info_id']]) ?? 0,
                        'state_txt'   => $stateTxt,
                        'status_code' => $request->getState(),
                        "audit_info"  => "",
                    ];
                    if (in_array($item['staff_info_id'], $staffNeedToHide)) {
                        $currentPending = array_merge($currentPending, [
                            'staff_id'    => "",
                            'staff_name'  => "",
                        ]);
                    } else {
                        $currentPending = array_merge($currentPending, [
                            'staff_id'    => $item['staff_info_id'] ?? '',
                            'staff_name'  => $this->formatStaffName($item['staff_name'], $nickNameList[$item['staff_info_id']] ?? ''),
                        ]);
                    }
                    $approval[] = $currentPending;
                }

                if (isset($data[$request->getCurrentFlowNodeId()])) {

                    $approval = array_merge($data[$request->getCurrentFlowNodeId()]["approval_info"], $approval ?? []);
                    $data[$request->getCurrentFlowNodeId(). "-10"] = [
                        'state_txt'         => $this->formatStateCodeTxt($request->getState(), $request->getCurrentFlowNodeId(), $nodeMark),
                        'status_code'       => $request->getState(),
                        'action_time'       => null,
                        "approval_type"     => intval($nodeInfo[$request->getCurrentFlowNodeId()] ?? 0), //或签 会签 抄送
                        "process_state"     => AuditLogModel::PROCESS_BAR_HIGHLIGHT,
                        "approval_info"     => $approval ?? []
                    ];
                } else {
                    $data[$request->getCurrentFlowNodeId()] = [
                        'state_txt'     => $this->formatStateCodeTxt($request->getState(), $request->getCurrentFlowNodeId(), $nodeMark),
                        'status_code'   => $request->getState(),
                        'action_time'   => null,
                        'approval_type' => intval($nodeInfo[$request->getCurrentFlowNodeId()] ?? 0), //或签 会签 抄送
                        "process_state" => AuditLogModel::PROCESS_BAR_HIGHLIGHT,
                        'approval_info' => $approval ?? [],
                    ];
                }
            }
            //处理节点后抄送
            if (isset($ccBack[$request->getCurrentFlowNodeId()]) && $ccBack[$request->getCurrentFlowNodeId()]) {
                $params['process_state'] = AuditLogModel::PROCESS_BAR_DARKNESS;
                $data[$request->getCurrentFlowNodeId() . '-1'] = $this->generateCcLog($request->getCurrentFlowNodeId(), $ccBack, $params);
            }

            //剩余节点
            $leftNode = $this->getWorkflowNodeByFlowId($request, $user);
            $leftNodeInfo = $this->getNodeByIds($leftNode, true);
            foreach ($leftNodeInfo as $node) {
                $uniqueKey = $node['id'];

                $approval = [];
                switch ($node['type']){
                    case enums::NODE_CC:
                        $action = enums::WF_ACTION_CC;
                        break;
                    default:
                        $action = enums::WF_ACTION_APPROVE;
                        break;
                }

                if (isset($node['auditor_id']) && $node['auditor_id']) {
                    $auditorIds = explode(',', $node['auditor_id']);
                    foreach ($auditorIds as $auditorId) {

                        $left = [
                            'position'    => $jobTitleList[$auditorId] ?? '',
                            'department'  => $deptList[$auditorId] ?? '',
                            'sort'        => intval($staffJobTitleGrade[$auditorId]) ?? 0,
                            'state_txt'   => "",
                            'status_code' => "",
                            "audit_info"  => "",
                        ];
                        if (in_array($auditorId, $staffNeedToHide)) {
                            $left = array_merge($left, [
                                'staff_id'    => "",
                                'staff_name'  => "",
                            ]);
                        } else {
                            $left = array_merge($left, [
                                'staff_id'    => $auditorId ?? '',
                                'staff_name'  => $this->formatStaffName($staffNameList[$auditorId] ?? '', $nickNameList[$auditorId] ?? ''),
                            ]);
                        }
                        $approval[] = $left;
                    }
                } else {
                    $approval[] = [
                        'staff_id'    => enums::SYSTEM_STAFF_ID,
                        'staff_name'  => 'System',
                        'position'    => '',
                        'department'  => '',
                        'sort'        => 0,
                        'state_txt'   => "",
                        'status_code' => "",
                        "audit_info"  => "",
                    ];
                }

                $lastNodeKey = $uniqueKey;
                if (isset($data[$uniqueKey])) {
                    $lastNodeKey = $lastNodeKey . '-10';
                    $data[$lastNodeKey] = [
                        'state_txt'         => $this->formatStateCodeTxt($action, $node['id'], $nodeMark),
                        'status_code'       => $request->getState(),
                        'action_time'       => null,
                        "process_state"     => AuditLogModel::PROCESS_BAR_DARKNESS,
                        "approval_type"     => intval($nodeInfo[$node['id']] ?? 0), //或签 会签 抄送
                        "approval_info"     => $approval ?? []
                    ];
                } else {
                    $data[$lastNodeKey] = [
                        'state_txt'         => $this->formatStateCodeTxt($action, $node['id'], $nodeMark),
                        'status_code'       => $request->getState(),
                        'action_time'       => null,
                        "process_state"     => AuditLogModel::PROCESS_BAR_DARKNESS,
                        "approval_type"     => intval($nodeInfo[$node['id']] ?? 0), //或签 会签 抄送
                        "approval_info"     => $approval ?? []
                    ];
                }


                //处理节点后抄送,需要当前节点审批同意
                //审批同意、审批
                if (isset($ccBack[$lastNodeKey]) && $ccBack[$lastNodeKey]) {
                    $data[$lastNodeKey . '-1'] = $this->generateCcLog($lastNodeKey, $ccBack, $params);
                }
            }
        }

        if (empty($data)) {
            return [];
        }

        //审批已经完成，追加最终节点
        $data[] = [
            'state_txt'         => $this->formatStateCodeTxt(enums::WF_ACTION_FINAL_COMPLETED),
            'status_code'       => enums::WF_ACTION_FINAL_COMPLETED,
            'action_time'       => null,
            "process_state"     => $request->getState() != Enums::APPROVAL_STATUS_PENDING? AuditLogModel::PROCESS_BAR_HIGHLIGHT: AuditLogModel::PROCESS_BAR_DARKNESS,
            "approval_type"     => enums::NODE_FINAL,
            "approval_info"     => []
        ];

        //4. 多个人的情况需要根据职级排序
        foreach ($data as &$item) {
            $count = count($item['approval_info']);
            if ($count > 1) {

                //如果同时存在同意的 + 待审批的，那么
                //已经审批同意的sort追加 100在排序因为
                //已经同意的在最上面
                $keySort = array_map(function ($v, $k) use($count) {
                    if (!in_array($v['status_code'], [enums::WF_ACTION_PENDING, enums::WF_ACTION_CC])) {
                        $v['sort'] = ($count - $k) * 100;
                    }
                    return $v;
                }, $item['approval_info'], array_keys($item['approval_info']));

                $keySort = array_column($keySort, 'sort');
                array_multisort($keySort, SORT_NUMERIC, SORT_DESC, $item['approval_info']);
            }
        }
        return $data;
    }


    /**
     * 组织抄送日志
     * @param $node_id
     * @param $cc_list
     * @param $params
     * @param int $process_state
     * @return array
     */
    public function generateCcLog($node_id, $cc_list, $params): array
    {
        $nickNameList       = $params['nick_name'];
        $staffNameList      = $params['staff_name'];
        $staffJobTitleName  = $params['staff_job_title_name'];
        $staffDepartment    = $params['staff_department'];
        $staffJobTitleGrade = $params['staff_job_title_grade'];
        $staffNeedToHide    = $params['staff_need_to_hide'];
        $processState       = $params['process_state'] ?? AuditLogModel::PROCESS_BAR_DARKNESS;
        $ccTime             = $params['cc_time'] ?? null;

        foreach ($cc_list[$node_id]['auditor_id'] as $staffId) {
            $currentStaff = [
                'position'    => $staffJobTitleName[$staffId] ?? '',
                'department'  => $staffDepartment[$staffId] ?? '',
                'sort'        => $staffJobTitleGrade[$staffId] ?? 0,
                'state_txt'   => null,
                'status_code' => enums::WF_ACTION_CC,
                'audit_info'  => '',
            ];

            if (in_array($staffId, $staffNeedToHide)) {
                $currentStaff = array_merge($currentStaff, [
                    'staff_id'    => "",
                    'staff_name'  => "",
                ]);
            } else {
                $currentStaff = array_merge($currentStaff, [
                    'staff_id'    => $staffId,
                    'staff_name'  => $this->formatStaffName($staffNameList[$staffId] ?? '', $nickNameList[$staffId] ?? ''),
                ]);
            }

            $cc[] = $currentStaff;
        }
        return [
            'state_txt'         => $this->getTranslation()->_('audit_cc'),
            'status_code'       => enums::WF_ACTION_CC,
            'action_time'       => $ccTime,
            "approval_type"     => enums::NODE_CC, //抄送
            "process_state"     => $processState,
            "approval_info"     => $cc ?? [],
        ];
    }

    /**
     * @description 获取隐藏工号信息
     * @param $staffIds
     * @param $jobTitleIdList
     * @return array
     */
    public function getHideStaffInfo($staffIds, $jobTitleIdList)
    {
        //获取需要隐藏的职位
        //Human Capital 职组 id = 15
        $staffRepository    = new StaffRepository();
        $jobTitleNeedToHide = $staffRepository->getJobTitleByGroupId(15);
        $staffCountryInfo   = $staffRepository->getStaffCountryInfo($staffIds);
        if (!empty($jobTitleIdList) && !empty($jobTitleNeedToHide) && !empty($staffCountryInfo)) {
            foreach ($staffIds as $staffId) {

                //职位在Human Capital职组中，并且非印尼国籍的人需要隐藏姓名工号
                if (isset($jobTitleIdList[$staffId]) && in_array($jobTitleIdList[$staffId], $jobTitleNeedToHide) &&
                    isset($staffCountryInfo[$staffId]) && $staffCountryInfo[$staffId] != HrStaffInfoModel::WORKING_COUNTRY_ID
                ) {

                    $staffNeedToHide[] = $staffId;
                }
            }
        }
        return $staffNeedToHide ?? [];
    }
}