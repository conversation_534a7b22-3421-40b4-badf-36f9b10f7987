<?php
/**
 * Author: Bruce
 * Date  : 2022-04-23 21:55
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Id\Server;

use FlashExpress\bi\App\Modules\Id\library\Enums\CeoMailEnums;
use FlashExpress\bi\App\Server\CeoMailServer as GlobalBaseServer;

class CeoMailServer extends GlobalBaseServer
{
    /**
     * 获取需要阅读承诺书的信息
     * @return array
     */
    public function getCommitmentCategoryInfo()
    {
        return CeoMailEnums::MUST_READ_INFORMATION_COMMITMENT_CATEGORY_IDS;
    }

    /**
     * 获取 关于薪酬分类
     * @param array $params
     * @return int
     */
    public function getCompensationCategory($params = [])
    {
        return CeoMailEnums::ABOUT_COMPENSATION_CATEGORY_ID;
    }
}