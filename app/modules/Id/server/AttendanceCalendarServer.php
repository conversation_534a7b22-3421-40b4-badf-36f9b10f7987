<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/5/8
 * Time: 下午3:25
 */

namespace FlashExpress\bi\App\Modules\Id\Server;

use FlashExpress\bi\App\Enums\AttendanceCalendarEnums;
use FlashExpress\bi\App\Server\AttendanceCalendarServer as BaseServer;

class AttendanceCalendarServer extends BaseServer
{

    public function getHolidayData($startDate='',$endDate=''): array
    {
        $holiday_data = (new LeaveServer())->ph_days($this->staffInfo);
        return empty($holiday_data) ? [] : array_column($holiday_data, NULL,'day');
    }
}