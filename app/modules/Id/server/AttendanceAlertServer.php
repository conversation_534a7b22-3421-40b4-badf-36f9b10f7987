<?php

namespace FlashExpress\bi\App\Modules\Id\Server;


use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\WhiteListServer;

class AttendanceAlertServer extends BaseServer
{
    /**
     * Notes: 检查印尼员工是否存在迟到早退
     * @param $staffId
     * @param $staffNodeDepartmentId
     * @param $staffJobTitle
     * @return bool
     */
    public static function checkStaffIsHaveLateEarly($staffId, $staffNodeDepartmentId, $staffJobTitle): bool
    {
        $settingEnv = new SettingEnvServer();
        // 格式：50001|1250,50001|1251 前面是部门ID后面是职位ID
        $setting = $settingEnv->getSetVal('skip_late_early_alert_dep_job_title');
        if ($setting) {
            // 支持配置多个
            $setting = explode(',', $setting);
            foreach ($setting as $item) {
                $t            = explode('|', $item);
                $departmentId = $t[0] ?? 0;
                $jobTitle     = $t[1] ?? 0;
                if ($departmentId && $jobTitle) {
                    // 检查员工职位：F-commerce下职位为Live Stream Host（ 职位ID为 1250）的员工，不提醒
                    $department = SysDepartmentModel::findFirst([
                        'conditions' => 'id = :id: AND deleted = 0',
                        'bind'       => [
                            'id' => $staffNodeDepartmentId,
                        ],
                    ]);
                    if ($department) {
                        $ancestryV3 = explode('/', $department->ancestry_v3);
                        if (in_array($departmentId, $ancestryV3) && $staffJobTitle == $jobTitle) {
                            return false;
                        }
                    }
                }
            }
        }

        //迟到早退判断   当天 加缓存
        //迟到早退举报,判断举报当天在白名单不举报
        //打卡当天在白名单也不提示
        $date = date('Y-m-d');
        $hcm_rpc_result = (new WhiteListServer())->attendanceListFromCache(['start_date'=>$date,'end_date'=>$date]);
        $attendance_white_list = $hcm_rpc_result['result']['data'] ?? [];
        //当地发薪不打卡白名单
        if (in_array($staffId,
                $attendance_white_list[$date]['type_paid_locally'] ?? [])
            ||
            in_array($staffId,
                $attendance_white_list[$date]['type_not_paid_locally'] ?? [])) {
            //在打卡白名单里
            return false;
        }

        return true;
    }
}