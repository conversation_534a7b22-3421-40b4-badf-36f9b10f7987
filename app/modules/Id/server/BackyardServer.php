<?php
/**
 * Author: Bruce
 * Date  : 2023-02-23 00:59
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Id\Server;
use FlashExpress\bi\App\Server\BackyardServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\MessageServer;


class BackyardServer extends GlobalBaseServer
{
    /**
     * 处理ID警告书消息详情
     * @param $data
     * @param $warning_info
     * @param $msg_id
     * @param $link
     * @return mixed
     */
    public function warningMessageDetail($data, $warning_info, $msg_id, $link)
    {
        if ($warning_info['role'] == MessageServer::MSG_STAFF_TYPE_STAFF) {//被警告人，置为已读
            // 消息已读
            $this->has_read_operation($msg_id,true);

        } elseif ($warning_info['role'] == MessageServer::MSG_STAFF_TYPE_SUPERIOR && !$warning_info['superior_img']) {
            $url             = env('sign_url')."/#/eleWarnState1";
            $data['content'] .= "<a href='{$url}?msg_id={$msg_id}' style='display: inline-block; cursor: pointer; margin-left: 10px;font-size: 15px;'>{$link}</a>";

        } elseif ($warning_info['role'] == MessageServer::MSG_STAFF_TYPE_OPERATOR && !$warning_info['operator_img']) {
            $url             = env('sign_url')."/#/eleWarnState1";
            $data['content'] .= "<a href='{$url}?msg_id={$msg_id}' style='display: inline-block; cursor: pointer; margin-left: 10px;font-size: 15px;'>{$link}</a>";
        }

        return $data;
    }
}