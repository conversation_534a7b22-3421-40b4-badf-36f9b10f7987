<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/9/8
 * Time: 2:26 PM
 */

namespace FlashExpress\bi\App\Modules\Id\Server;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffDaysFreezeModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReadModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Modules\Id\Server\Vacation\MaternityServer;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\BaseServer;

use FlashExpress\bi\App\Models\backyard\StaffLeaveInLieuModel;
use App\Country\Tools;
use FlashExpress\bi\App\Server\LeaveServer AS GlobalBaseServer;
use FlashExpress\bi\App\Modules\Id\Server\Vacation\AnnualServer;
use Exception;
use FlashExpress\bi\App\Modules\Id\Server\Vacation\InternationalServer;

class LeaveServer extends GlobalBaseServer {


    const LEAVE_DAYS = 12;
    public $leaveObject;




    //根据员工 工作日 不同 返回不同 ph 返回 map list
    public function ph_days($staff_info)
    {
        $bind['type']       = $staff_info['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_5 ? [
            ThailandHolidayModel::TYPE_DEFAULT,
            ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_5,
        ] : [ThailandHolidayModel::TYPE_DEFAULT, ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_6];
        return ThailandHolidayModel::find([
            'conditions' => 'type in ({type:array})',
            'columns'    => 'day,holiday_type',
            'bind'       => $bind,
        ])->toArray();
    }


    /**
     * 根据员工信息 计算区间内是否存在休息日 返回对应休息日日期数组
     * 用于计算 请假区间 扣除包含休息日的天数
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     */
    public function staff_off_days($staff_id, $start_date, $end_date = '')
    {
        if (empty($end_date))
            $end_date = $start_date;

        //获取员工信息
        $model       = new StaffRepository($this->lang);
        $staff_info  = $model->getStaffPosition($staff_id);
        $rest = $model->get_work_days_between($staff_id, $start_date, $end_date);
        $rest = !empty($rest) ? array_column($rest, 'date_at') : [];
        $holidays = $this->ph_days($staff_info);
	    if(!empty($holidays)){
		    $holidays =  array_column($holidays, 'day');
		    $rest = array_merge($rest,$holidays);
	    }

        $this->logger->write_log("ignore rest_day {$staff_id} ".json_encode($rest),'info');

        return $rest;

    }


    //根据各种规则 计算员工额度 按月份 固化表
    public function get_year_leave_days($staff_info){
        //获取 当月额度
        $freeze_info = StaffDaysFreezeModel::findFirst("staff_info_id = {$staff_info['staff_info_id']} and leave_type = ".enums::LEAVE_TYPE_1);
        $freeze = 0;
        if(!empty($freeze_info->days))
            $freeze = $freeze_info->days;

        return round($freeze,2);
    }


    //补休假 增加到 失效额度表
    public function add_remain_days($staff_id,$days,$add_date,$data_source,$operator_id = 10000){
        if(empty($add_date))
            return false;

        $days = round($days/10,1);//任务参数 不支持 n.5 传过来 扩大10 在这缩回来

        $invalid_date = date('Y-m-d',strtotime("{$add_date} + 90 day"));

        $model = new StaffLeaveInLieuModel();
        for($i = $days; $i > 0; $i -= 0.5){
            $clone = clone $model;

            $row['staff_info_id'] = $staff_id;
            $row['date_at'] = $add_date;
            $row['invalid_date'] = $invalid_date;
            $row['days'] = 0.5;
            $row['data_source'] = $data_source;
            $row['operator_id'] = 10000;
            if($data_source == 3)
                $row['operator_id'] = $operator_id;

            $clone->create($row);
        }

        return true;
    }

    //获取当前剩余 有效额度
    public function get_remain_days($staff_id){
        $current_date = date('Y-m-d');

        //获取当前时间 没失效的额度天数
        $info = StaffLeaveInLieuModel::find([
            'columns' => "state",
            'conditions' => "staff_info_id = {$staff_id} and invalid_date >= '{$current_date}'",
        ])->toArray();
        $return['in_all'] = $return['used'] = 0;
        if(!empty($info)){
            foreach ($info as $in){
                $return['in_all'] += 0.5;
                if($in['state'] == 2)
                    $return['used'] += 0.5;
            }
        }

        return $return;
    }

    //扣除对应固化的额度天数  目前只有马来的补休
    public function sub_leave_days($staff_id, $days){
        $current_date = date('Y-m-d');
        //新计算方法 用补休假 拆分表
        $data = StaffLeaveInLieuModel::find([
            'conditions' => "state = 1 and staff_info_id = {$staff_id} and invalid_date >= '{$current_date}'"
        ]);

        if(empty($data))
            return false;

        $step = $days;
        foreach ($data as $da){
            if($step <= 0)
                break;

            $da->state = 2;
            $da->update();
            $step -= 0.5;
        }

        return true;

    }

    /**
     * @throws ValidationException
     * @param array
     * @return array
     */
    public function saveVacation($param){
        $this->leaveObject = $this->getLeaveObj(intval($param['leave_type']));
        $auditServer = new AuditServer($this->lang, $this->timeZone);

        //类型验证
        $staffRe    = new StaffRepository($this->lang);
        $staffInfo  = $staffRe->getStaffPosition($param['staff_id']);
        $leave_lang = AuditRepository::$leave_type;
        $typeData   = $auditServer->staffLeaveType($staffInfo);
        if (empty($typeData)) {
            throw new ValidationException('wrong leave type');
        }
        if (!in_array($param['leave_type'], array_keys($typeData)) && $param['leave_type'] != enums::LEAVE_TYPE_15) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004') . $this->getTranslation()->_($leave_lang[$param['leave_type']]));
        }

        $db = $this->getDI()->get('db');
        $db->begin();
        try{
            $audit_id = $this->leaveObject->handleCreate($param);
            //没生成id
            if (empty($audit_id)) {
                throw new ValidationException($this->getTranslation()->_('1009'));
            }

            //非工具操作申请 创建审批相关
            if (empty($param['is_bi'])) {
                $param['time_out'] = $this->leaveObject->timeOut ?? null;
                $auditServer->saveApproval($audit_id, $param);
            }

            $db->commit();
            $return['data'] = ['leave_day' => $this->leaveObject->leave_day ?? 0];
            return $this->checkReturn($return);
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }

    //获取对应假期的额度
    public function getVacationDays($staffId,$leaveType, $extend = []){
        $param['staff_id'] = $staffId;
        $param['leave_type'] = $leaveType;
        $param = array_merge($param,$extend);
        $leaveObject = $this->getLeaveObj($param['leave_type']);
        return $leaveObject->handleSearch($param);
    }

    //非审核通过 撤销操作 返还额度
    public function cancelVacation($auditInfo,$staffInfo,$state, $extend = []){
        $extend['status'] = $state;
        $this->leaveObject = $this->getLeaveObj(intval($auditInfo['leave_type']));
        $this->leaveObject->returnRemainDays($auditInfo['audit_id'],$staffInfo,$extend);
    }

    //对应本国的 所有类型 映射类

    /**
     * @throws ValidationException
     */
    public function getLeaveObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_1:
                $leaveObj =  new AnnualServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_19://跨国探亲
                $leaveObj =  new InternationalServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_4:
                $leaveObj =  new MaternityServer($this->lang,$this->timeZone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }
    //task 初始化额度 调用
    public function getInstanceObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_19://跨国探亲
                $leaveObj = InternationalServer::getInstance($this->lang,$this->timezone);
                break;
            case enums::LEAVE_TYPE_4://产假
                $leaveObj = MaternityServer::getInstance($this->lang,$this->timezone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }

    //获取 额度详情信息列表
    public function getAnnualDetail($param){
        $leaveObject = $this->getLeaveObj(enums::LEAVE_TYPE_1);
        return $leaveObject->detailList($param);
    }


}