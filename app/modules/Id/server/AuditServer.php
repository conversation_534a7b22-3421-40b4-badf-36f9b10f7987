<?php

namespace FlashExpress\bi\App\Modules\Id\Server;

use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Modules\Id\enums\OvertimeEnums;
use FlashExpress\bi\App\Modules\Id\Server\Vacation\AnnualServer;
use FlashExpress\bi\App\Modules\Id\Server\Vacation\InternationalServer;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditExtendServer;
use FlashExpress\bi\App\Server\AuditListServer;
use FlashExpress\bi\App\Server\AuditServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\KpiServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\Vacation\TrainingServer;

class AuditServer extends GlobalBaseServer
{
    //https://l8bx01gcjr.feishu.cn/sheets/shtcn7BEJ22gbGMGASycT5Cgkbb?sheet=667dcf
    protected $month_arr = enums::LEAVE_INVALID_MONTH;//年假失效月
    protected $last_day = enums::LEAVE_INVALID_DATE;//年假失效日
    public $sub_day = array(1,3,5,10,12,16,19,31,33,30,34,35,13);//需要跳过休息日的类型
    protected $forbidden   = array(enums::LEAVE_TYPE_1,enums::LEAVE_TYPE_4,enums::LEAVE_TYPE_5,enums::LEAVE_TYPE_10,enums::LEAVE_TYPE_36);//试用期不能申请的类型
    protected $time_limit_start = array(4,5,10,12,19,32,30,34,35,36,13);//限制开始时间 只能当天以后
    public $one_time = array();//入职以来 只能申请一次
    public $one_send = array(enums::LEAVE_TYPE_16,enums::LEAVE_TYPE_10,enums::LEAVE_TYPE_30,enums::LEAVE_TYPE_34,enums::LEAVE_TYPE_35,enums::LEAVE_TYPE_36);//入职以来 限制总额不限制次数
    protected $by_create_time = array(1,4,5);//额度按创建时间计算 针对跨年的申请
    protected $show_type    = '1,19,13';//请假页面 需要展示额度的 类型
    public static $c_days = 20;//c 级别 20天

    protected $need_img = array(3,4,5,10,30,31,32,33,34,35,36);//图片必填的请假类型

    public $newVersionType = [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_19, enums::LEAVE_TYPE_4];//改版新结构类型
    /**
     * 获取权限
     * @param array paramIn
     * @return array
     */
    public function getListPermission( $paramIn = [] ) {


        //补卡
        $result['AT'] = 1;     //默认存在
        //请假
        $result['LE'] = $this->isLeavePermission() ? 1 : 2;     //默认存在
        //LH 移除
        //$result['LH'] = $this->checkLcDataPosition($paramIn) == true ? 1 : 2;
        //新耗材申请上线老的耗材入口移除
        //$result['Wms'] = $this->getWmsPermission($paramIn) == true ? 1 : 2;
        //修改里程权限
        $result['Mile'] = $this->getMilePermission($paramIn) == true ? 1 : 2;
        //加班-需求 https://l8bx01gcjr.feishu.cn/docs/doccnIzLTlZxBjvNHdZiSIo8eVd#
        $result['OT'] = $this->getOTPermission($paramIn) == true ? 1 : 2;
        //出差
        $result['Trip'] = $this->getTripPermission($paramIn) == true ? 1 : 2;
        //车辆里程
        $result['Vehicle'] = $this->getVehiclePermission($paramIn) == true ? 1 : 2;
        //HC
        $result['HC'] = $this->checkHcDataRole($paramIn) == true ? 1 : 2;
        //补申请
        $result['apply'] = $this->getApplyPermission($paramIn) == true ? 1 : 2;
        //加班车
        $result['fleet'] = $this->getFleetPermission($paramIn) == true ? 1 : 2;
        //离职-所有人都可以申请离职
        $result['resign'] = $this->isResignPermission() ? 1 : 2;
        //外协员工
        $result['OS'] = $this->getOSPermission($paramIn) == true ? 1 : 2;
        //资产申请
        //$result['Asset'] = $this->getASPermission($paramIn) == true ? 1 : 2;
        //举报
        $result['Report'] = $this->getReportPermission($paramIn) == true ? 1 : 2;
        //到岗确认
        $result['Confirm'] = $this->getEntryConfirmPermission($paramIn) == true ? 1 : 2;
        //工资条pdf
        $result['salary'] = 2; //$this->salary_pdf_permission($paramIn) == true ? 1 : 2;
        //油费补贴
        $result['fuel_subsidy'] = $this->getFuelSubsidyPermission($paramIn) == true ? 1 : 2;
        //在职证明
        $result['on_job'] = 2; //$this->on_job_pdf_permission($paramIn) == true ? 1: 2;
        //工资证明
        $result['payroll'] = 2;//$this->on_job_pdf_permission($paramIn) == true ? 1: 2;
        //离职资产确认
        $result['resign_as'] = $this->getResignAssetPermision($paramIn) == true ? 1 : 2;
        //转岗
        $result['TF'] = $this->getJobtransferPermission($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_nw'] = $this->getFreightDiscNetworkPermission($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_shop'] = $this->getFreightDiscShopPermission($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_other'] = $this->getFreightDiscOtherPermission($paramIn) == true ? 1 : 2;
        //抄送列表
        $result['CC'] = 1;
        //黄牌项目出差
        $result['yc_Trip'] = 2;//$this->getYCTripPermission($paramIn) == true ? 1 : 2;

        // 销售CRM
        $result['Sales_CRM'] = $this->getSalesCRMPermission($paramIn) == true ? 1 : 2;

        //工服购买排除外协员工
        $result['InteriorOrder'] = $this->getInteriorOrdersPermission($paramIn) == true ? 1 : 2;

        // 后勤-报销申请
        $result['fuel_budget']  = 2;

        //我的面试
        $result['my_interview'] = (new InterviewRepository($this->timezone))->myInterview($paramIn) == true ? 1 : 2;
        //新版资产申请权限
        $result['NAS'] = $this->getNewAssetPermission($paramIn) ? 1 : 2;

        $bool               = (new \FlashExpress\bi\App\Server\AdministrationOrderServer())->isFlashFulfillmentDepartment($paramIn,
            $setVal);
        $result['xz_order'] = (true === $bool) ? 1 : 2;
        //审批-人事-KPI目标
        $result['kpi'] = (new KpiServer())->getKpiPermission($paramIn) ? 1 : 2;
        //OA文件夹
        $result['oa_folder'] = $this->OAFolderPermission() ? 1 : 2;
        //hcm入口
        $result['HCM'] = $this->isShowHCMPermission() ? 1 : 2;
        //过滤外协员工权限
        $paramIn['permission_list'] = $result;
        $result                     = $this->outsourcingPermissionFilter($paramIn);
        //耗材调拨入口
        $result['package_allot'] = $this->isShowPackageAllotPermission($this->staffInfo) ? 1 : 2;
        //flash pay 商城
        $result['flash_pay_mall'] = $this->isShowFlashPayMallPermission() ? 1 : 2;

        return $this->checkReturn(['data' => $result]);
    }

    /**
     * @return bool
     */
    protected function isShowFlashPayMallPermission(): bool
    {
        $config_staff_ids = (new SettingEnvServer())->getSetVal('flash_pay_mall_staff_ids',',');
        return in_array($this->staffInfo['staff_info_id'], $config_staff_ids);

    }

    /**
     * 获取外协员工申请权限
     * @param array $paramIn
     * @return boolean
     */
    public function getOSPermission($paramIn = [])
    {
        $jobTitlesData        = UC('outsourcingStaff')['jobTitlesNot'];
        $hubRequestRole       = UC('outsourcingStaff')['osLongPeriodRequestRole'];
        $motorcadeRequestRole = UC('outsourcingStaff')['osMotorcadeRequestRole'];
        $roles                = $this->processingDefault($paramIn, 'positions', 3);
        $job_title            = $this->processingDefault($paramIn, 'job_title', 2);
        $organizationId       = $this->processingDefault($paramIn, 'organization_id', 2);
        $type                 = $this->processingDefault($paramIn, 'organization_type', 2);
        $departmentId         = $this->processingDefault($paramIn, 'department_id', 2);

        if ((!empty($jobTitlesData) || !empty($hubRequestRole)) || !empty($motorcadeRequestRole)) {

            //1-[短期外协申请条件][网点在编人员并且不jobTitlesData崗位才可以申请]
            //2-[长期外协申请条件][hub网点网点经理、区域经理可以申请]['TH02020402','TH02030208'网点正主管][shop project部门所有员工]
            //3-[车队外协申请条件][Line haul & Transportation部门(26)，角色为线路中控人员(31)/线路规划管理员的员工申请(32)]

            $store = (new SysStoreServer())->getStoreByid($organizationId);


            if ($type == 2) { //除了车队外协可以是总部员工申请，其余都是网点员工才能申请
                return false;
            }

            if (!isset($jobTitlesData[$job_title]) || (isset($store) && ($store['category'] == 8 || $store['category'] == 9))) {
                return true;
            }
        }
        return false;
    }


    /**
     * 获取资产申请权限 和 我的tab  公共资产显示
     * @param array paramIn
     * @param boolean paramIn 是否只判断白名单
     * @return boolean
     */
    public function getASPermission($paramIn = [], $isOnlyWhiteList = false)
    {
        $staffId = $paramIn['staff_id'];
        //[2]获取当前员工职位
        $staff_re = new StaffRepository($this->lang);
        $info = $staff_re->getStaffPositionv3($staffId);

        //申请权限
        //白名单 ：工号为[19685,24455,40450,25921,29053] 或者 职位是Regional Manager(79)、Assistant Regional Manager(269)
        //DC、SP网点: 网点正主管(职位是16)
        //SHOP网点: 网点正主管
        //HUB网点: 指定工号
        $staffArr = explode(',', env('asset_request_white_list', "24455,40450,38983,19685,47094,19060,21426,21328"));
        if (in_array($staffId, $staffArr) || in_array($info['job_title'], [
                enums::$job_title['regional_manager'],
                enums::$job_title['district_manager'],
                enums::$job_title['area_manager'],
            ])) {
            return true;
        }

        if ($isOnlyWhiteList === true) {
            // 只验证工号职位白名单
            return false;
        }

        return $this->check_asset_permission($info);
    }


    public function check_asset_permission($info)
    {
        $staffId = $info['id'];
        //申请权限
        //白名单 ：工号为[19685,24455,40450,25921,29053] 或者 职位是Regional Manager(79)、Assistant Regional Manager(269)
        //DC、SP网点: 网点正主管(职位是16)
        //SHOP网点: 网点正主管
        //HUB网点: 指定工号
        $staffArr = explode(',', env('asset_request_white_list', "24455,40450,38983,19685,47094,19060,21426,21328"));
        if (in_array($staffId, $staffArr) || in_array($info['job_title'], [
                enums::$job_title['regional_manager'],
                enums::$job_title['district_manager'],
                enums::$job_title['area_manager'],
//                enums::$job_title['van_courier'],//https://l8bx01gcjr.feishu.cn/docs/doccnTaliIYM0g0I59txV3dIjQb# 第五部分
            ])) {
            return true;
        }

        if ($info['organization_type'] == 2) {
            // 总部用户
            $staff_re = new StaffRepository($this->lang);
            $staffInfo = $staff_re->checkoutStaffBi($staffId);
            if ($staffInfo && isset($staffInfo['job_title_level']) && in_array($staffInfo['job_title_level'], [2, 3, 4]) || in_array($staffId, [19982, 31856])) {
                return true;
            }
        }
        //1(DC)，2(SP)，457(shop)，8(HUB)
        if (empty($info) || !in_array($info['category'], [1, 2, 4, 5, 7, 8, 9, 10, 12])) {
            return false;
        }

        switch ($info['category']) {
            // DC/SP
            case 1:
            case 2:
                // BDC
            case 10:
                if (in_array($info['job_title'], [enums::$job_title['branch_supervisor']])) {
                    return true;
                }
                break;
            // shop
            case 4:
            case 5:
            case 7:
                //OS
            case 9:
                if (in_array($info['job_title'], [enums::$job_title['shop_supervisor'], enums::$job_title['onsite_supervisor']])) {
                    return true;
                }
                break;
            //hub
            case 8:
                if(in_array($info['job_title'], [
                    enums::$job_title['hub_supervisor'],
                    enums::$job_title['hub_manager'],
                    enums::$job_title['hub_admin_officer'],
                    enums::$job_title['freight_hub_manager'],
                    enums::$job_title['freight_hub_outbound_supervisor'],
                    enums::$job_title['freight_hub_QAQC_supervisor'],
                    enums::$job_title['freight_hub_inbound_supervisor']])
                ) {
                    return true;
                }
                break;
            case 12:
                if (in_array($info['job_title'], [
                    enums::$job_title['hub_manager'],
                    enums::$job_title['hub_supervisor'],
                    enums::$job_title['hub_admin_officer'],
                    enums::$job_title['freight_hub_manager'],
                    enums::$job_title['freight_hub_inbound_supervisor'],
                    enums::$job_title['freight_hub_QAQC_supervisor'],
                    enums::$job_title['freight_hub_outbound_supervisor'],
                ])) {
                    return true;
                }
        }

        return false;
    }

    /**
     * 获取物料申请权限
     * @param array paramIn
     * @return boolean
     */
    public function getWmsPermission($paramIn = [])
    {
        //[1]校验添加权限
        $admin_group = UC('wmsRole')['admin_group'];
        $staffId     = $paramIn['staff_id'];

        //admin小組有提交物料权限
        if (isset($admin_group[$staffId])) {
            return true;
        }

        return $this->check_wrs_permission($staffId);
    }



    /**
     * 请假添加
     * @Access  public
     * @Param   request
     * @Return  array
     *
     * ！！！ 涉及表 staff_audit 主表 staff_audit_leave_split 拆分表
     */
    public function leaveAdd($paramIn = [])
    {
        //[1]参数定义
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $auditReason    = $this->processingDefault($paramIn, 'audit_reason');
        $imagePathArr   = $this->processingDefault($paramIn, 'image_path');
        $auditReason    = addcslashes(stripslashes($auditReason), "'");

        $db = StaffAuditModel::beginTransaction($this);
        $serialNo  = $this->getRandomId();

        //[2]用户校验
        $staff_model = new StaffRepository();
        $staffData   = $staff_model->getStaffPosition($staffId);
        if (empty($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
        }
        if ($staffData['is_sub_staff'] == 1) {
            return $this->checkReturn(-3, $this->getTranslation()->_('sub_staff_disable'));
        }
        if ($this->checkStaffFormal($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('os_or_franchisee_staff_disable'));
        }
        $leave_day = $paramIn['leave_day'] = $this->conversionTime(array_merge(['type' => 2], $paramIn));

        //请假日期 就是休息日区间 无需申请 假期类型包括 年假,事假,婚嫁,出家假,个人培训假（不带薪）,病假,绝育手术假,公司培训假,家人去世假。
        if ($leave_day == 0)
            return $this->checkReturn(-3, $this->getTranslation()->_('day off for the apply date'));
        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $holidays = $paramIn['holidays'] = $leave_server->staff_off_days($staffId, $leaveStartTime, $leaveEndTime);

        //[3]校验选择时间内是请假记录
        $checkData = $this->checkLeaveDataByDate($paramIn);
        if (isset($checkData['code']) && $checkData['code'] == 0) {
            return $this->checkReturn(-3, $checkData['msg']);
        }
        //新增验证班次时间二次确认提示 https://flashexpress.feishu.cn/docx/WqJcd6xFNoctnux4qPEcKTZcnkg
        if (empty($paramIn['is_bi']) && empty($paramIn['is_submit'])){
            $leave_server->checkShiftLeave($staffId,$paramIn);
        }

        //判断是否跨天 保存 拆分表 staff_audit_leave_split
        $insert_param['leave_type'] = $leaveType;
        $insert_param['start_time'] = $leaveStartTime;
        $insert_param['end_time'] = $leaveEndTime;
        $insert_param['start_type'] = $leaveStartType;
        $insert_param['end_type'] = $leaveEndType;
        $insert_param['holidays'] = $holidays;
        $insert_param['sub_day'] = $this->sub_day;

        $r = $leave_server->format_leave_insert($staffId,$insert_param);
        if($r['code'] == 1)
            $insert = $r['data'];
        else
            return $r;

        $audit_model = new AuditRepository($this->lang);
        //如果是年假 需拆分 并且去年年假在有效期内
        {
            if($leaveType == enums::LEAVE_TYPE_13){
                //扣除额度表
                $leave_server->sub_leave_days($staffId,$leave_day);
            }else if($leaveType == enums::LEAVE_TYPE_15 && !empty($paramIn['is_bi'])){//替换轮休 工具操作时候用
                $check_param['operator'] = $paramIn['operator'] ?? 0;
                $check_param['staff_id'] = $staffId;
                $check_param['date'] = date('Y-m-d',strtotime($leaveStartTime));
                $this->leave_for_workday($check_param);
            }else if(in_array($leaveType,$this->one_time)){
                //增加 一次性额度扣减
                $remain_model = new StaffLeaveRemainDaysModel();
                $remain_row['staff_info_id'] = $staffId;
                $remain_row['leave_type'] = $leaveType;
                $remain_row['days'] = 0;
                $remain_row['leave_days'] = $leave_day;
                $flag = $remain_model->create($remain_row);
                $this->getDI()->get("logger")->write_log("leave_one_time {$staffId} {$flag}" . json_encode($remain_row),'info');
            }else if(in_array($leaveType,$this->one_send)){
                //一次性发放额度 先查询 有没有额度 如果没有额度 不让申请 需要在hris 那边初始化
                $remain_info = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                    'bind' => ['staff_info_id' => $staffId,'leave_type' => $leaveType],
                ]);

                //如果没有 说明没初始化成功 提示错误
                if(empty($remain_info))
                    return $this->checkReturn(-3, 'init failed');

                //操作额度
                $remain_info->days -= $leave_day;
                $remain_info->leave_days += $leave_day;
                $remain_info->updated_at = gmdate('Y-m-d H:i:s');
                $remain_info->update();
            }
        }

        $status = 1;
        if (!empty($paramIn['is_bi'])){
            $status = 2;
            $auditReason .= "|system_tool_add";
        }
        $extend['time_out'] = date('Y-m-d 00:00:00',strtotime('+3 day'));
        //[4]请假插入
        try {
            $insetData = [
                'staff_info_id'    => $staffId,
                'leave_type'       => $leaveType,
                'leave_start_time' => $this->assemblyData($leaveStartTime, 1, $leaveStartType),
                'leave_start_type' => $leaveStartType,
                'leave_end_time'   => $this->assemblyData($leaveEndTime, 2, $leaveEndType),
                'leave_end_type'   => $leaveEndType,
                'audit_reason'     => $auditReason,
                'status'           => $status,
                'audit_type'       => enums::$audit_type['LE'],
                'leave_day'        => $leave_day,
                'serial_no'        => (!empty($serialNo) ? 'LE' . $serialNo : NULL),
                'time_out'         => $extend['time_out'],
            ];

            //不同国家 存在 子类型 产假等
            if(!empty($paramIn['sub_type'])){
                $insetData['template_comment'] = json_encode(array("leave_{$leaveType}_key" => intval($paramIn['sub_type'])));
            }
            $db->insertAsDict("staff_audit", $insetData);
            $auditId = $db->lastInsertId();
            if (!$auditId) {
                throw new \Exception("插入staff_audit 失败" . json_encode($insetData, JSON_UNESCAPED_UNICODE));
            }
            if($insert && is_array($insert)) {
                foreach ($insert as &$v) {
                    $v['audit_id'] = $auditId;
                }
                $result = (new BaseRepository())->batch_insert("staff_audit_leave_split", $insert);
                if (!$result) {
                    throw new \Exception("插入staff_audit_leave_split 失败" . json_encode($insert, JSON_UNESCAPED_UNICODE));
                }
            }
            if (empty($paramIn['is_bi'])) {

                $res = (new ApprovalServer($this->lang, $this->timezone))->create($auditId, enums::$audit_type['LE'], $staffId,null, $extend);
                if (!$res) {
                    throw new \Exception('audit staff insert leaveAdd workflow fail' . $res);
                }
            }
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("leaveaddworkflow error ". $e->getMessage() . " " . $e->getTraceAsString());
            $db->rollBack();
            return $this->checkReturn(-3, $this->getTranslation()->_('1009'));
        }


        //插入图片
        if (!empty($imagePathArr)) {
            foreach ($imagePathArr as $k => $v) {
                $insertImgData = [
                    'audit_id'   => $auditId,
                    'image_path' => $v,
                ];
                $audit_model->auditImgInsert($insertImgData);
            }
        }

        $return['data'] = array('leave_day' => $paramIn['leave_day']);
        return $this->checkReturn($return);
    }

    /**
     *
     * 验证请假规则
     * @param array $paramIn
     * @return array
     */
    public function checkLeaveDataByDate($paramIn = [])
    {
        //[1]参数定义 leave_start_time 和 leave_end_time 是 ymd 类型
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $imagePathArr   = $this->processingDefault($paramIn, 'image_path');
        $leave_days     = $paramIn['leave_day'];

        $param = [
            'staff_id'         => $staffId,
            'leave_start_time' => date('Y-m-d',strtotime($leaveStartTime)),
            'leave_start_type' => $leaveStartType,
            'leave_end_time'   => date('Y-m-d',strtotime($leaveEndTime)),
            'leave_end_type' => $leaveEndType,
        ];


        //开始时间 结束时间 验证
        if ($leaveEndTime < $leaveStartTime) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
        }
        if ($leaveEndTime == $leaveStartTime) {
            if ($leaveStartType > $leaveEndType) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
            }
        }
        $audit_model = new AuditRepository($this->lang);


        //图片必填的类型
        if (in_array($leaveType, $this->need_img) && empty($imagePathArr)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('22328_leave_application'));
        }

        //图片最多限制5张
        if (!empty($imagePathArr) && count($imagePathArr) > 5) {
            return $this->checkReturn(-3, $this->getTranslation()->_('at most 5 photos'));
        }

        //[3]查询请假记录
        $levelData = $this->checkExistLeave($param);
        if (!empty($levelData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1012'));
        }


        //新增规则 二期需求 https://shimo.im/sheets/94eQG9bXB8oMZlnT/MODOC
        //新增需求 试用期判断 不用固定120天 https://l8bx01gcjr.feishu.cn/docs/doccne9Sx3V9c0YesAU97rFTkBb hr_probation 状态为4的字段
        $leaveType  = intval($leaveType);
        $leave_lang = $audit_model::$leave_type;
        //获取job title name 入职时间
        $staff_model = new StaffRepository($this->lang);
        $staff_info  = $staff_model->getStaffPosition($staffId);
        $hire_date   = $staff_info['hire_date'];
        $entry       = strtotime($staff_info['hire_date']);
        if (empty($staff_info) || empty($hire_date)) {
            return $this->checkReturn(-3, 'no permission to apply');
        }

        //类型验证
        $typeData = $this->staffLeaveType($staff_info);
        if (empty($typeData)) {
            throw new ValidationException('wrong leave type');
        }
        if (!in_array($leaveType, array_keys($typeData)) && $leaveType != enums::LEAVE_TYPE_15) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004').$this->getTranslation()->_($leave_lang[$leaveType]));
        }

        //新增 请假白名单 不限制入职天数 修改为 查看是否入职
        if (in_array($leaveType,
                $this->forbidden) && $hire_date >= '2020-06-13 00:00:00' && !($leaveType == enums::LEAVE_TYPE_1 && $staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_2)) {
            //试用期状态，1试用期，2已通过，3未通过，4已转正 适用于 xxx 之后 之前的数据默认都转正 因为没管旧数据
            if ($staff_info['status'] != 4) {
                $message = str_replace('leave_type', $this->getTranslation()->_($leave_lang[$leaveType]),
                    $this->getTranslation()->_('probation_limit'));
                return $this->checkReturn(-3, $message);
            }
            if (!empty($staff_info['formal_at']) && $leaveStartTime < $staff_info['formal_at']) {
                $message = $this->getTranslation()->_('probation_before_limit');
                return $this->checkReturn(-3, $message);
            }
        }

        //限制 所有假期 申请限制区间 3天前-- 以后 工具申请 不限制
        if (empty($paramIn['is_bi']) && in_array($leaveType, $this->time_limit_start)) {
            //请假日期必须大于等于当前日期
            if ($leaveStartTime < date('Y-m-d')) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1018'));
            }
        }


        // 需要先检验 次数 和总额度 是否非法 然后再拆分
        if (in_array($leaveType, $this->one_time)) {
            //验证 员工入职以后 是否请过
            $remain_info = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                'bind'       => ['staff_info_id' => $staffId, 'leave_type' => $leaveType],
            ]);

            if (!empty($remain_info)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }

            //是否超出 限定 额度
            $limit_days = $audit_model->get_leave_days_by_type($leaveType);
            if (!empty($limit_days) && $leave_days > $limit_days) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }
        }

        //不限次数 的 总和 限制 假期
        if (in_array($leaveType, $this->one_send)) {
            //验证 员工入职以后 是否请过
            $remain_info  = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                'bind'       => ['staff_info_id' => $staffId, 'leave_type' => $leaveType],
            ]);
            $leave_server = new LeaveServer($this->lang, $this->timezone);
            if (empty($remain_info)) {
                $remain_info = $leave_server->init_leave(['staff_info_id' => $staffId, 'leave_type' => $leaveType]);
            }

            if (empty($remain_info)) {
                return $this->checkReturn(-3, $this->getTranslation()->_("init type {$leaveType} failed "));
            }

            if ($remain_info->days <= 0 || ($remain_info->days - $leave_days) < 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }
        }


        //拼接 验证逻辑 参数 check_leave_type 额度验证
        $leave_info['leave_type'] = $leaveType;
        $leave_info['img']        = $imagePathArr;
        $leave_info['is_bi']      = empty($paramIn['is_bi']) ? '' : $paramIn['is_bi'];
        $leave_info['sub_type']   = empty($paramIn['sub_type']) ? 0 : $paramIn['sub_type'];
        //新增传参
        $leave_info['leave_start_type'] = $leaveStartType;
        $leave_info['leave_end_type']   = $leaveEndType;
        $leave_info['holidays']         = $paramIn['holidays'];
        //本次请假  持续天数 如果跨年 //如果跨年 并且 不是年假 要拆开 每年分别是几天 和 每年的 开始结束时间 因为部分假期类型是按年度区分 今年可以请n天明年仍然可以请n天 还可以一次性跨年请2n天
        //！！新需求  如果部分请假类型其中包含法定假日 等休息日  需要扣除掉 不占用 该请假类型的额度
        //新需求 如果是产假 请跨年 不占用2年额度 按创建时间 每年一次
        if (date('Y', strtotime($leaveStartTime)) != date('Y', strtotime($leaveEndTime))
            && $leaveType != enums::LEAVE_TYPE_19
            && !in_array($leaveType, $this->by_create_time)
            && !in_array($leaveType, $this->one_time)
            && !in_array($leaveType, $this->one_send)) {
            $leave_server = new LeaveServer($this->lang, $this->timezone);
            //新需求 拆分时候 如果当天是休息日 剔除掉
            $date_array = $leave_server->format_year_date($leaveType, $leaveStartTime, $leaveEndTime,
                $paramIn['holidays']);
            $flag       = false;
            foreach ($date_array as $y) {//array('2019' => array('2019-12-30','2019-10-31') )) 有几年就有几个 正常应该就两年 没人能请一整年假期 还干不干了
                $need_days = 0;
                sort($y);
                $leave_info['leave_start_time'] = $y[0];//该年开始 时间
                $leave_info['leave_end_time']   = end($y);//该年 结束时间

                //拆分计算 本次请假 需要天数
                foreach ($y as $date) {
                    if ($date == $leaveStartTime) {
                        $need_days += ($leaveStartType == 1) ? 1 : 0.5;
                    } else {
                        if ($date == $leaveEndTime) {//最后一天
                            $need_days += ($leaveEndType == 2) ? 1 : 0.5;
                        } else {//区间内 肯定是一整天
                            $need_days += 1;
                        }
                    }
                }
                //验证每年的额度 是否 够用
                $flag = $this->check_leave_type($leave_info, $staff_info, $need_days);
                if ($flag !== true) {
                    break;
                }
            }

            if ($flag !== true) {
                return $flag;
            }
        } else {
            $need_days                      = $paramIn['leave_day'];
            $leave_info['leave_start_time'] = $leaveStartTime;
            $leave_info['leave_end_time']   = $leaveEndTime;
            $flag                           = $this->check_leave_type($leave_info, $staff_info, $need_days);
            if ($flag !== true) {
                return $flag;
            }
        }
        return $this->checkReturn(1);
    }

    /**
     *请假逻辑
     * 年假 额度 按创建时间计算  按当前时间 获取 今年年假 计算额度
     * 其他假期 如果跨年 先取出 开始年 和额度
     * 然后结束年 和 额度 两次计算 是否合法请假
     * 计算剩余额度 需要 获取 已经请的假期记录 如果存在跨年 需要再次拆分 留下 当前判定年的天数
     *
     * @param $leave_info 截取后的 请假开始时间和结束时间
     * @param $staff_info
     * @param $need_days 截断后的 请假天数 比如 19年 1天 20年2天 总数是3天 need_days 两次传参 1， 2
     * @return bool
     */
    protected function check_leave_type($leave_info, $staff_info, $need_days)
    {
        $leaveType        = $leave_info['leave_type'];
        $leaveStartTime   = $leave_info['leave_start_time'];
        $leaveEndTime     = $leave_info['leave_end_time'];
        $entry            = strtotime($staff_info['hire_date']);
        $staffId          = $staff_info['staff_info_id'];
        $audit_model      = new AuditRepository($this->lang);
        $imagePathArr     = $leave_info['img'];
        $paramIn['is_bi'] = $leave_info['is_bi'];
        $sub_type = intval($leave_info['sub_type']);
        //额度计算
        //新需求 如果请假天数 跨年 需做新规则处理 如果是年假 按当前 年 计算
        $year  = date('Y', strtotime($leaveStartTime));
        $apply_month = date('m',strtotime($leaveStartTime));
        $apply_end_month = date('m',strtotime($leaveEndTime));
        $current_year = date('Y',time());
        //新算法 用拆分表
        if($leaveType == 1 && in_array($apply_month,$this->month_arr))//20年 请 21年 4月之后的年假 不需要取当年的剩余额度
            $applied_days = $audit_model->get_used_leave_days($staffId, $current_year, $leaveType);
        else
            $applied_days = $audit_model->get_used_leave_days($staffId, $year, $leaveType);

        //每种年假的额度
        $limit_days = $audit_model->get_all_leave_days();

        $sum_days             = $count_days = array();
        $sum_days[$leaveType] = $count_days[$leaveType] = 0;
        if (!empty($applied_days)) {
            $info                   = $applied_days[0];
            $sum_days[$leaveType]   = $info['num'];
            $count_days[$leaveType] = empty($info['audit_ids']) ? 0 : count(explode(',', $info['audit_ids']));
        }

        $leave_server = new LeaveServer($this->lang,$this->timezone);
        if ($leaveType == enums::LEAVE_TYPE_3) {//带薪病假

            if (empty($paramIn['is_bi']) && $leaveStartTime < date('Y-m-d', strtotime('-2 day')))
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_3_notice'));

        } else if ($leaveType == enums::LEAVE_TYPE_4) {//产假
            $used_days = $audit_model->get_used_leave_days($staffId, $current_year, $leaveType);
            if(!empty($used_days))
                $used_days = $used_days[0]['num'];
            else
                $used_days = 0;

            if($need_days > $limit_days[$leaveType]){
                $str = str_replace('{X}',$limit_days[$leaveType], $this->getTranslation()->_('leave_notice_at_most'));
                return $this->checkReturn(-3, $str);
            }

            if (($need_days + $used_days) > $limit_days[$leaveType])
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

        }else if($leaveType == enums::LEAVE_TYPE_5){//陪产假
            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));


        }else if($leaveType == enums::LEAVE_TYPE_10){//婚假
//            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
//                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));


        }else if($leaveType == enums::LEAVE_TYPE_12){//不带新事假 无限制


        }else if ($leaveType == 13) {//补休假 固化额度
            //新需求 补休假 换数据表了
            $return = $leave_server->get_remain_days($staffId);
            $have_days = ($return['in_all'] - $return['used']) > 0 ? $return['in_all'] - $return['used'] : 0;
            if($need_days >  $have_days)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

        }else if ($leaveType == enums::LEAVE_TYPE_16) {//员工培训假
            $trainingServer             = new TrainingServer($this->lang, $this->timezone);
            $trainingServer->staffInfo  = $staff_info;
            $trainingServer->paramModel = $leave_info;
            if ($trainingServer->checkTrainingLeave()) {
                return $this->checkReturn(-3, $this->getTranslation()->_('training_leave_notice'));
            }
        }else if ($leaveType == enums::LEAVE_TYPE_31) {//丧家

            if (empty($paramIn['is_bi']) && $leaveStartTime < date('Y-m-d', strtotime('-2 day')))
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_3_notice'));

            if ($need_days > 2) {//每次最多3天
                $str = str_replace('{X}','2', $this->getTranslation()->_('leave_notice_days'));
                return $this->checkReturn(-3, $str);
            }

        }else if ($leaveType == enums::LEAVE_TYPE_32) {//流产假
            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));


        }else if ($leaveType == enums::LEAVE_TYPE_33) {//其他家人去世
            if (empty($paramIn['is_bi']) && $leaveStartTime < date('Y-m-d', strtotime('-2 day')))
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_3_notice'));

            if ($need_days > 1) {//每次最多3天
                $str = str_replace('{X}','1', $this->getTranslation()->_('leave_notice_days'));
                return $this->checkReturn(-3, $str);
            }
        }else if ($leaveType == enums::LEAVE_TYPE_30) {//孩子结婚假
            if ($need_days > 2) {//每次最多3天
                $str = str_replace('{X}','2', $this->getTranslation()->_('leave_notice_days'));
                return $this->checkReturn(-3, $str);
            }
//            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
//                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

        } else if ($leaveType == enums::LEAVE_TYPE_34) {//儿子割礼
            if ($need_days > 2) {
                $str = str_replace('{X}','2', $this->getTranslation()->_('leave_notice_days'));
                return $this->checkReturn(-3, $str);
            }
//            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
//                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

        } else if ($leaveType == enums::LEAVE_TYPE_35) {//儿童洗礼
            if ($need_days > 2) {
                $str = str_replace('{X}','2', $this->getTranslation()->_('leave_notice_days'));
                return $this->checkReturn(-3, $str);
            }
//            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
//                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

        }  else if ($leaveType == enums::LEAVE_TYPE_36) {//朝觐假
            //满5年才能申请
            if(date('Y-m-d H:i:s') < date('Y-m-d H:i:s',strtotime("{$staff_info['hire_date']} +5 year")))
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_36_notice'));

//            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
//                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
        } else if ($leaveType == 15) {//休息日 可以申请前天、昨天、今天、明天、后天。
            if (empty($paramIn['is_bi']) && $leaveStartTime < date('Y-m-d', strtotime('-2 day')))
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_3_notice'));


            //新需求 按工作天数 区分 6天为单休
            if($staff_info['week_working_day'] != 6)
                return $this->checkReturn(-3, $this->getTranslation()->_('jobtransfer_0004'));

            $check_rest = $leave_server->get_rest_times($staff_info,$leave_info);
            if($check_rest['code'] != 1)
                return $this->checkReturn(-3, $check_rest['msg']);

            $times = $check_rest['data'];

            if ($times >= 1)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_for_five'));

            //新需求 休息日 只能是1天 大于小于都不行 https://shimo.im/docs/kv6rgP3GXDT69wdc/read
            if ($need_days < 1)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_for_half'));

            if ($need_days > 1)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

        } else {//非法请求
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        return true;
    }

//    /**
//     * 获取 补卡/请假类型
//     * @Access  public
//     * @Param   request
//     * type 1 补卡 枚举
//     * 2 请假类型 枚举 (请假的客户端不调用 只有内部left holiday 用)
//     * @Return  array
//     * 1 年假 3病假 4产假 5陪产假 10婚假 12不带新事假 15休息日 16员工培训家 19跨国探亲假 31丧家 32流产假 33其他家人丧家 30子女结婚假 34儿子割礼 35儿童洗礼 36朝觐假
//     *
//     */
//    public function getTypeBook($paramIn = [])
//    {
//        $type = $this->processingDefault($paramIn, 'type', 2, 1);
//        switch ($type) {
//            case 1;
//                $data = [
//                    [
//                        'code' => '1',
//                        'msg'  => $this->getTranslation()->_('2001')
//                    ],
//                    [
//                        'code' => '2',
//                        'msg'  => $this->getTranslation()->_('2002')
//                    ]
//                ];
//
//                break;
//            case 2;//新增是否带薪属性  1-带薪 2-不带薪
//                $data = $this->type_book();
//                break;
//            default:
//                $data = [
//
//                ];
//                break;
//        }
//
//        $returnData['data']['dataList'] = $data;
//        if ($type == 1) {
//            $returnData['data']['need_img'] = false;
//        }
//        //新增个人代理字段
//        $info = HrStaffInfoModel::findFirst("staff_info_id = {$paramIn['user_info']['id']}");
//        $returnData['data']['hire_type'] = empty($info) ? 0 :$info->hire_type;
//
//        return $this->checkReturn($returnData);
//    }

    //1 年假 3病假 4产假 5陪产假 10婚假 12不带新事假 15休息日 16员工培训家 19跨国探亲假 31丧家 32流产假 33其他家人丧家 30子女结婚假 34儿子割礼 35儿童洗礼 36朝觐假
    //病假、产假、陪产假、婚假、孩子结婚假、丧假、流产假、其他家人丧家、儿子割礼、儿童洗礼、朝觐假：  BY请假申请页的「图片」为必填：
    public function type_book($locale = '')
    {

        $data = [
            [
                'code' => '1',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('2003'),
            ],
            [
                'code' => '3',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('2005')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '4',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('2006')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ], [
                'code' => '5',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('2007')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '10',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('2012')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '12',
                'type' => enums::UN_SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('2014'),
            ],
            [
                'code' => '15',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('2017'),
            ],
            [
                'code' => '16',
                'type' => 1,
                'msg'  => $this->getTranslation($locale)->_('2018'),
            ],
            [
                'code' => '19',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('2022'),
            ],
            [
                'code' => '31',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('leave_31')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '32',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('leave_32')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '33',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('leave_33')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '30',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('leave_30')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '34',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('leave_34')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '35',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('leave_35')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '36',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation($locale)->_('leave_36')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],

        ];
        return $data;
    }

    /**
     * 获取剩余假期天数
     * @param $param
     */
    public function get_left_holidays($param)
    {
        //by 显示额度的类型
        $showType = [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_13, enums::LEAVE_TYPE_19];

        //hcm 工具获取所有假期额度
        if (!empty($param['is_svc']) && $param['is_svc'] == 1) {
            $data = $this->rpcHolidaysNum($param);
            return $this->checkReturn(['data' => $data]);
        }

        $staff_id = intval($param['staff_id']);
        //所有假期类型
        $data = $this->type_book();
        //根据员工属性 获取对应权限类型
        $data = $this->staffLeaveType($param['user_info'], $data);

        $leave_server = new LeaveServer($this->lang, $this->timezone);
        foreach ($data as $k => &$da) {
            $type = intval($da['code']);
            //需要在请假页面显示天数的假期类型 1
            if (in_array($type, [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_19])) {//改版后调用
                $da              = array_merge($da, $leave_server->getVacationDays($staff_id, $type));
            }

            if (in_array($type, $showType)) {
                //改类型 前端用
                $da['day_limit'] = empty($da['day_limit']) ? '0' : (string)$da['day_limit'];
                $da['day_sub']   = empty($da['day_limit']) ? '0' : (string)$da['day_sub'];
            }
        }
        $data = $leave_server->explain_read_flag($param['user_info'], $data);
        return $this->checkReturn(['data' => $data]);
    }


    public function rpcHolidayBody($permissionData, $leave_server)
    {
        foreach ($permissionData as $k => &$da) {
            $type = intval($da['code']);
            //额度显示 需要排除的 类型
            if (in_array($type, [enums::LEAVE_TYPE_15])) {
                unset($permissionData[$type]);
                continue;
            }

            //需要在请假页面显示天数的假期类型 1 和类型2
            if (in_array($type, $this->newVersionType)) {//改版后调用
                $da = array_merge($da, $leave_server->getVacationDays($this->staffId, $type, ['is_svc' => 1]));
                continue;
            }

            //一次性假期
            if (!empty($this->oneTypes) && in_array($type, $this->oneTypes)) {
                $da['day_limit'] = $da['day_sub'] = $this->limit_array[$type] ?? 0;//分母 应有总额度
                //one time 类型 看有没有 没有就是没申请过 是全额
                if (in_array($type, $this->one_time) && !empty($this->remainData[$type])) {
                    $da['day_sub'] = 0;
                }
                if (in_array($type, $this->one_send) && !empty($this->remainData[$type])) {
                    $da['day_sub'] = half_num($this->remainData[$type]['days']);
                }
                continue;
            }

            if (!in_array($type, $this->limit_types)) {
                $da['text'] = 'no_limit';//没有 unset掉 并且 额度表没类型 说明 是不限制额度
            }

            //需要计算的类型 还没改版
            $da['day_limit'] = $this->limit_array[$type] ?? 0;//分母 应有总额度
            $sum             = empty($this->sum_days[$type]['num']) ? 0 : $this->sum_days[$type]['num'];//已用的 前端没用 不限时了
            $da['day_sub']   = $da['day_limit'] - $sum;//分子 今年剩余额度

            if ($da['day_sub'] < 0) {
                $da['day_sub'] = 0;
            }
        }
        return $permissionData;
    }

    //根据员工工号属性 获取对应能申请的假期类型
    public function staffLeaveType($staff_info,$data = []){
        if(empty($data)){
            $data = $this->type_book();
        }
        $leave_server = new LeaveServer($this->lang, $this->timezone);
        //新增权限判断
        $permission = $leave_server->leavePermission($staff_info);
        if(!$permission){
            return [];
        }
        //实习生类型
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_INTERN){
            $data = $this->practise_type_book();
        }

        //外协类型
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_0){
            $data = $this->os_type_book();
        }
        $data = array_column($data, null, 'code');

        if (empty($staff_info['sex']) ||  $staff_info['sex'] == 1) {
            //男性过滤掉 产检，产假 和女性特殊假
            unset($data[4],$data[17],$data[32]);
        } else {
            //女性过滤掉 陪产假，国家军训假，出家假
            unset($data[5]);
        }
        //休息日 去掉 工具要展示
        unset($data[15]);


        // 国籍与工作所在地不一致的员工， 会有跨国探亲假 如果数据表没有 触发发放操作
        $nationServer = new InternationalServer($this->lang,$this->timezone);
        if(!$nationServer->applyPermission($staff_info)){
            unset($data[19]);
        }

        return $data;
    }

    /**
     * 换算时间
     * 新需求 需根据员工属性 扣除休息日 和ph  6天-找轮休  5天 找周六日
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function conversionTime($paramIn = [])
    {
        //[1]参数定义
        //leave type 1 上午 2 下午
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $type           = $this->processingDefault($paramIn, 'type', 2, 1);//类型 1外部调用 2内部调用
        $staff_id       = $paramIn['staff_id'];


        //[2]换算时间
        if ($leaveEndTime < $leaveStartTime || ($leaveEndTime == $leaveStartTime) && $leaveEndType < $leaveStartType) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
        }
        $leave_day = 0;
        $key       = $start_date = date('Y-m-d', strtotime($leaveStartTime));
        $end_date  = date('Y-m-d', strtotime($leaveEndTime));
        //获取该员工 请假区间的 休息日 和 ph
        $leave_server = new LeaveServer($this->lang, $this->timezone);
        $holidays     = $leave_server->staff_off_days($staff_id, $leaveStartTime, $leaveEndTime);
        while ($key <= $end_date) {
            if (in_array($key, $holidays) && in_array($leaveType, $this->sub_day)) {
                $key = date("Y-m-d", strtotime("+1 day", strtotime($key)));
                continue;
            }
            $add = 1;
            if ($key == $start_date && $leaveStartType != 1) {
                $add = 0.5;
            }
            if ($key == $end_date && $leaveEndType != 2) {
                $add = 0.5;
            }

            $leave_day += $add;
            $key       = date("Y-m-d", strtotime("+1 day", strtotime($key)));
        }

        if ($type == 1) {
            $returnData['data']['day'] = $leave_day;
            return $this->checkReturn($returnData);
        }
        return $leave_day;
    }


    public function get_holidays($staff_info)
    {
        $leave_server = new LeaveServer($this->lang, $this->timezone);
        $data         = $leave_server->ph_days($staff_info);
        if (!empty($data)) {
            return array_column($data, 'day');
        }
        return [];
    }


    /**
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return array|true|void
     * @throws \Exception
     */
    public function setLEProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            // 是最终审批
            $auditDetail = StaffAuditModel::findFirst([
                'conditions' => ' audit_id = :audit_id: ',
                'bind' => ['audit_id' => $auditId],
            ]);
            if(empty($auditDetail)){
                throw new \Exception('wrong leave info');
            }
            $auditDetail = $auditDetail->toArray();
            if (isset(self::$paramIn['staff_id'])) {
                $staff = HrStaffInfoModel::findFirst([
                    'conditions' => ' staff_info_id = :staff_id: ',
                    'bind' => ['staff_id' => self::$paramIn['staff_id']],
                ]);
                if ($staff) {
                    $staff = $staff->toArray();
                }
            }
            if (!$extend['super']){
                $state = $this->transformState($state,$extend['is_cancel'] ?? 0);
            }
            $updateData = [
                'status'        => $state,
                'audit_id'      => $auditId,
                'reject_reason' => isset(self::$paramIn['reject_reason']) ? self::$paramIn['reject_reason'] : '',
                'approver_id'   => isset(self::$paramIn['staff_id']) ? self::$paramIn['staff_id'] : 0,
                'approver_name' => isset($staff) && $staff ? $staff['name'] : '',
            ];
            $auditEditFlag = $this->re['audit']->auditEditStatus($updateData);
            if (!$auditEditFlag) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1014'));
            }

            //审核通过 休息日
            if($state == enums::$audit_status['approved'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_15){
                $check_param['staff_id'] = $auditDetail['staff_info_id'];
                $check_param['date'] = date('Y-m-d',strtotime($auditDetail['leave_start_time']));
                $this->leave_for_workday($check_param);

            }
            //撤销休息日
            if($state == enums::$audit_status['revoked'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_15){
                $ext_server = new AuditExtendServer($this->lang,$this->timezone);
                $del_param['staff_info_id'] = $auditDetail['staff_info_id'];
                $del_param['date_at'] = date('Y-m-d',strtotime($auditDetail['leave_start_time']));
                $del_param['operate_id'] = $extend['operate_id'] ?? 0;
                $ext_server->cancel_for_leave($del_param);
            }
            //马来 补休假 如果 非审核通过的最终审批 需返还对应额度  印尼 也启用 补休假
            if($state != enums::$audit_status['approved'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_13){
                $leave_server = new LeaveServer($this->lang,$this->timezone);
                $leave_server->re_back_leave_days($auditDetail);
            }
            //跨国探亲假 和其他假期 非审核通过 操作返还 目前其他类型 只有 带薪事假 陆续兼容
            if (in_array($auditDetail['leave_type'], [enums::LEAVE_TYPE_2]) && $state != enums::$audit_status['approved']) {
                $leave_server = new LeaveServer($this->lang, $this->timezone);
                $p['leave_type'] = $auditDetail['leave_type'];
                $p['audit_id'] = $auditId;
                $leave_server->update_leave_days($auditDetail['staff_info_id'],enums::YEAR_RE_BACK,$p);
            }
            //一次性的假期 one time  one send 需要操作staff_leave_remaining_days 表
            if($state != enums::$audit_status['approved']){
                if(in_array($auditDetail['leave_type'],$this->one_time)){
                    //delete remain
                    StaffLeaveRemainDaysModel::find([
                        'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                        'bind' => ['staff_info_id' => $auditDetail['staff_info_id'], 'leave_type' => $auditDetail['leave_type']],
                    ])->delete();
                }
                if(in_array($auditDetail['leave_type'],$this->one_send)){
                    //update remain
                    $remain_info = StaffLeaveRemainDaysModel::findFirst([
                        'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                        'bind' => ['staff_info_id' => $auditDetail['staff_info_id'], 'leave_type' => $auditDetail['leave_type']],
                    ]);
                    if(!empty($remain_info)){
                        $leave_days = $auditDetail['leave_day'];
                        $remain_info->days += $leave_days;
                        $remain_info->leave_days -= $leave_days;
                        $remain_info->updated_at = gmdate('Y-m-d');
                        $remain_info->update();
                    }
                }

                //通用返还额度
                if(in_array($auditDetail['leave_type'],[enums::LEAVE_TYPE_1,enums::LEAVE_TYPE_19,enums::LEAVE_TYPE_4])){
                    //获取员工信息
                    $staff_model = new StaffRepository($this->lang);
                    $staff_info = $staff_model->getStaffPosition($auditDetail['staff_info_id']);
                    $leave_server = new LeaveServer($this->lang, $this->timezone);
                    $leave_server->cancelVacation($auditDetail,$staff_info,$state);
                }

            }
            //驳回 超时 发消息push
            $this->sendMessage($auditDetail,$state);
        }

        return true;
    }

    //所有类型 都放开 不要删除
    public function type_book_history()
    {

        $data = [
            [
                'code' => '1',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('2003'),
            ],
            [
                'code' => '3',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('2005')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '4',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('2006')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ], [
                'code' => '5',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('2007')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '10',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('2012')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '12',
                'type' => enums::UN_SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('2014'),
            ],
            [
                'code' => '13',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('2015'),
            ],
            [
                'code' => '15',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('2017'),
            ],
            [
                'code' => '16',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2018'),
            ],
            [
                'code' => '19',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('2022'),
            ],
            [
                'code' => '31',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('leave_31')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '32',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('leave_32')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '33',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('leave_33')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '30',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('leave_30')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '34',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('leave_34')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '35',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('leave_35')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],
            [
                'code' => '36',
                'type' => enums::SALARY_LEAVE,
                'msg'  => $this->getTranslation()->_('leave_36')
                ,'need_img' => enums::LEAVE_NEED_IMG,
            ],


        ];
        return $data;
    }

    /**
     * 获取薪资周期
     * @param $now
     * @return array
     */
    public function getPayCycle($now = null)
    {
        $timestamp = isset($now) ? strtotime($now):time();
        if (date('j', $timestamp) >= 24){
            $start = date("Y-m-24",$timestamp);
            $end = date("Y-m-23",strtotime("next month",$timestamp));
        }else{
            $start = date("Y-m-24",strtotime("previous month",$timestamp));
            $end = date("Y-m-23",$timestamp);
        }

        return [$start, $end];
    }
}
