<?php

namespace FlashExpress\bi\App\Modules\Id\Server;


use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\AttendanceServer as BaseAttendanceServer;


class AttendanceServer extends BaseAttendanceServer
{

    /**
     * 人脸图片上传
     * @param $staff_id
     * @param $filename
     * @return void
     * @throws BusinessException
     * @throws ValidationException
     */
    public function faceFormatUrlFle($staff_id, $filename)
    {
        return $this->faceFormatUrlHcm($staff_id, $filename);
    }


}
