<?php
/**
 * Created by PhpStorm.
 * User: weijian
 * Date: 2019-04-08
 * Time: 17:35
 */

namespace FlashExpress\bi\App\Modules\Id\Server;

use FlashExpress\bi\App\Enums\CeoMailEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\PersoninfoServer as GlobalPersoninfoServer;

class PersoninfoServer extends GlobalPersoninfoServer
{
    public $timezone;
    public function __construct($lang = 'zh-CN', $timezone, $userInfo = array())
    {
        parent::__construct($lang,$timezone);
        $this->timezone = $timezone;
    }


    public function updateMobileCompany($paramIn = []) {
        try {
            $staffId = $paramIn['staff_id'];
            $mobile_company = empty($paramIn['mobile_company']) ? '' : $paramIn['mobile_company'];

            if(!empty($mobile_company)) {

                if(!preg_match("/[0-9]{8,11}/", $mobile_company)) {
                    return $this->checkReturn(-3, 'The Mobile Number must be 8-11 digits');
                }

                $sql = "select staff_info_id,mobile_company from hr_staff_info where staff_info_id != :staff_info_id and mobile_company = :mobile_company and formal in (1, 4) and is_sub_staff = 0 and state in (1, 3);";
                $staff_list = $this->getDI()->get('db_rby')->query($sql,['staff_info_id' => $staffId, 'mobile_company' => $mobile_company])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if(!empty($staff_list)) {
                    $staff_ids = implode(",",array_column($staff_list, 'staff_info_id'));
                    $msg = str_replace('{staff_info_id}', $staff_ids, $this->getTranslation()->_('update_mobile_company_error_1'));
                    $this->getDI()->get('logger')->write_log('企业手机号'.$paramIn['mobile_company'].'被使用，工号：'.$staff_ids,'info');
                    return $this->checkReturn(-3, $msg);
                }
            }

            $ac = new ApiClient('hr_rpc', '', 'update_mobile_company');
            $ac->setParams([
                "staff_info_id" => $staffId,
                "mobile_company" => $mobile_company
            ]);
            $ac_result = $ac->execute();
            if($ac_result["result"]['code'] == 1) {
                return $this->checkReturn(['msg' => $this->getTranslation()->_('5002')]);
            } else {
                return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
            }
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('updateMobileCompany:'.$e->getMessage().'--行号：'.$e->getLine());
            return $this->checkReturn(['msg' => $this->getTranslation()->_('5002')]);
        }
    }

    /**
     * @param $paramIn
     * @return array|mixed
     * @throws \ReflectionException
     */
    public function getSalaryInfoFromHCM($paramIn)
    {
        $data = [];
        $month   = $paramIn['month'];
        $staffId = $paramIn['staff_id'];
        //传一个当前月份 如果没有记录 会返回 有薪资记录的最新的一个月  （逻辑直接放到hcm 来处理）
        $param['month'] = $month;
        $param['staff_id'] = $staffId;

        $ac = new ApiClient('hcm_rpc', '', 'get_salary_data',$this->lang);
        $ac->setParams($param);
        $ac_result = $ac->execute();
        $this->logger->write_log("staff {$staffId} getSalaryInfoFromHCM request:".json_encode($param,JSON_UNESCAPED_UNICODE)." res:".json_encode($ac_result,JSON_UNESCAPED_UNICODE)." ", 'info');

        if(!empty($ac_result) && !empty($ac_result['result']['data'])){
            $data = $ac_result['result']['data'];
            //考勤数量统计
            $salaryAttendanceStat  = $this->salaryAttendanceStat($staffId,$data['salary_cycle'],'only_total');
            $data = array_merge($data,$salaryAttendanceStat);
            //工资条新增 有效无效ot 数量
            [$data['effect_num'],$data['invalid_num']] = $this->formatOtData($staffId,$data['salary_cycle']);
            $data['flash_box_url'] = env('h5_endpoint') . CeoMailEnums::FLASH_BOX_CATEGORY;
        }


        return $data;
    }


}
