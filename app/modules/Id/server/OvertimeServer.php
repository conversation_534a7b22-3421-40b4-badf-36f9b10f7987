<?php

namespace FlashExpress\bi\App\Modules\Id\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrOvertimeForLeaveModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Repository\AuditApplyRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\HrShiftServer;
use FlashExpress\bi\App\Server\OvertimeServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;

class OvertimeServer extends GlobalBaseServer
{

    //超时加班 只能是 上班之后 申请晚上下班之后的 对应时长 需要动态获取
    public $overOt = [
        HrOvertimeModel::OVERTIME_1 => [],
        HrOvertimeModel::OVERTIME_3 => [],
        HrOvertimeModel::OVERTIME_5 => [],
    ];

    //全天加班 上班时间开始就可以申请的 节假日 或者 休息日加班 对应时长 是固定的
    public $holidayOt = [
        HrOvertimeModel::OVERTIME_2 => [],
        HrOvertimeModel::OVERTIME_4 => [],

    ];


    const PERMISSION_GRADE = 16;
    /**
     * 获取加班类型
     * @Access  public
     * @Param   array $paramIn
     * @Param   array $userinfo
     * @Return  array
     */
    public function getTypeOvertime($paramIn = [], $userinfo = [])
    {
        $data                           = $this->getAllOtType();
        $returnData['data']['dataList'] = $data;

        if (!empty($paramIn['is_svc'])) {
            return $this->checkReturn($returnData);
        }

        $staff_re = new StaffRepository($this->lang);
        if (empty($this->staffInfo)) {
            $this->staffInfo = $staff_re->getStaffPosition($paramIn['staff_id']);
        }
        //获取有权限的类型和时长
        $data = $this->getTypeDuration($data);

        $returnData['data']['dataList'] = $data;
        return $this->checkReturn($returnData);
    }

    /**
     * 判断员工是否为一线员工
     * @param $staffInfo
     * @return bool
     */
    public function isFirstLineStaff($staffInfo): bool
    {
        $jobTitle   = $staffInfo['job_title'] ?? '';
        $department = $staffInfo['node_department_id'] ?? '';

        $settingEnv = new SettingEnvServer();
        $config     = $settingEnv->getSetVal('first_line_jobs');
        if (empty($config) || empty($jobTitle) || empty($department)) {
            return false;
        }

        $config = explode(',', $config);
        foreach ($config as $v) {
            //业务配错了 用中文的竖线
            $v = str_replace('｜','|',$v);
            [$cfg_department, $cfg_job_title] = explode('|', $v);

            if ($cfg_department == $department && $cfg_job_title == $jobTitle) {
                return true;
            }
        }
        return false;
    }


    //这里不做 权限判断 详情页 hcm工具 回显都要用 有权限都写上面去
    public function getAllOtType($locale = '')
    {
        $new_duration = [
            ['time_hour' => 4, 'time_text' => '4h'],
            ['time_hour' => 5, 'time_text' => '5h'],
            ['time_hour' => 6, 'time_text' => '6h'],
            ['time_hour' => 7, 'time_text' => '7h'],
            ['time_hour' => 8, 'time_text' => '8h'],
        ];
        $new_customer = [
            ['time_hour' => 2, 'time_text' => '2h'],
            ['time_hour' => 3, 'time_text' => '3h'],
            ['time_hour' => 4, 'time_text' => '4h'],
        ];
        return [
            [
                //工作日加班
                'code'     => '1',
                'msg'      => $this->getTranslation($locale)->_('5141'),
                'sub_msg'  => '',
                'duration' => $new_customer,
            ],
            [
                //休息日加班
                'code'     => '2',
                'msg'      => $this->getTranslation($locale)->_('5113'),
                'sub_msg'  => '',
                'duration' => $new_duration,
            ],
            [
                //休息日超时
                'code'     => '3',
                'msg'      => $this->getTranslation($locale)->_('5114'),
                'sub_msg'  => '',
                'duration' => $new_customer,
            ],
            [
                //节假日加班
                'code'     => '4',
                'msg'      => $this->getTranslation($locale)->_('5124'),
                'sub_msg'  => '',
                'duration' => $new_duration,
            ],
            [
                //节假日超时
                'code'     => '5',
                'msg'      => $this->getTranslation($locale)->_('5125'),
                'sub_msg'  => '',
                'duration' => $new_customer,
            ],
        ];
    }

    //给审批流历史用的 类型数据
    public function getApprovalOtType($locale = '')
    {
        return $this->getAllOtType($locale);
    }

    //高层 只有这几个选项 以后搬权限配置表 audit_permission
    public function upLevelType($isUnderlyingStaff)
    {
        //一线 比非一线 多一种 工作日加班类型 https://flashexpress.feishu.cn/docx/SIeidtZ5rodz7xxoRvEcGfUon5e
        if($isUnderlyingStaff){ // 有3个类型 1，2，4
            $showTypes = [HrOvertimeModel::OVERTIME_1,HrOvertimeModel::OVERTIME_2,HrOvertimeModel::OVERTIME_4];
        }else{//2种  2，4
            $showTypes = [HrOvertimeModel::OVERTIME_2,HrOvertimeModel::OVERTIME_4];
        }

        $data = $this->getAllOtType();
        $return = [];
        foreach ($data as $da){
            if(in_array($da['code'],$showTypes)){
                $return[] = $da;
            }
        }

        return $return;
    }

    /**
     * 新建加班
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function addOvertimeV3($paramIn = [])
    {
        $this->param   = $paramIn;
        $staffId    = $this->processingDefault($paramIn, 'staff_id', 2);
        $type       = $this->processingDefault($paramIn, 'type', 2);
        $start_time = $this->processingDefault($paramIn, 'start_time');
        $reason     = $this->processingDefault($paramIn, 'reason');
        $duration   = floatval($paramIn['duration']);
        $date       = $this->processingDefault($paramIn, 'date_at');
        $reason     = addcslashes(stripslashes($reason), "'");

        if (empty($date) || empty($start_time) || empty($type)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        //拼接endtime
        $startTime  = strtotime($start_time);
        $start_time = date('Y-m-d H:i:s', $startTime);
        $endTime    = empty($end_time) ? 0 : strtotime($end_time);
        if (!empty($endTime)) {
            $end_time = date('Y-m-d H:i:s', $endTime);
        } else {
            $endTime  = $startTime + floatval($duration) * 3600;
            $end_time = date('Y-m-d H:i:s', $endTime);
        }

        // 校验时间段 可选日期为近5天(前天、昨天和今天 明天后天) 如果是bi工具 不做时间校验
        //新需求 验证逻辑 修改 https://l8bx01gcjr.feishu.cn/docs/doccnznGnDKzb4akgPuhYQq5oHc
        $shiftServer = new HrShiftServer();
        $shift_info = $shiftServer->getShiftInfos($staffId,[$date]);
        $shift_info = $shift_info[$date] ?? [];
        if (empty($shift_info))  //没有班次信息 不让申请
        {
            throw new ValidationException($this->getTranslation()->_('no_shift_notice'));
        }

        $staff_re    = new StaffRepository($this->lang);
        $this->staffInfo = $staff_re->getStaffPosition($staffId);
        if (empty($this->staffInfo)) {
            throw new ValidationException('can not find staff');
        }

        if (empty($paramIn['is_bi'])) {
            $this->checkOTTimePeriod($type, $shift_info, $date);
        }

        $this->checkTypeDuration();

        //!!!!!!  加班 校验逻辑
        $paramIn['shift_info'] = $shift_info;
        $check_data            = $this->checkOvertime($paramIn);

        if ($check_data['code'] != 1) {
            return $check_data;
        }

        //获取参考数据
        [$references, $extend] = $this->getReference($staffId, $this->param);

        // 所有人OT将不再转化为调休假
        // https://flashexpress.feishu.cn/docx/doxcntgSbzo9pFzy315uy8evsLc
        // 职级F16（含）以上：不能申请加班，直接关闭加班申请入口
        // 线职位的职级F15（含）以下：只能申请休息日和节假日加班，享受非固定津贴和加班费
        // 非一线职位的职级F15（含）以下：只能申请休息日和节假日加班，享受非固定津贴，不享受加班费
        $sub_type = 0;//sub type 0 -- 用于薪酬 1 -- 用于调休

        //新增了 bi工具 补记录 状态直接为审核通过 不发push 审核人为 操作工具hr
        $higher = ''; //bi工具 直接审核通过 需要记录操作人 带申请的 不需要记录上级
        $state  = 1;
        if (!empty($paramIn['is_bi'])) {
            $higher = $paramIn['operator'];
            $state  = 2;
        }
        $serialNo    = $this->getID();
        $insertParam = [
            'staff_id'        => $staffId,
            'type'            => $type,
            'start_time'      => $start_time,
            'end_time'        => $end_time,
            'reason'          => $reason,
            'reject_reason'   => '',
            'state'           => $state,
            'duration'        => $check_data['data']['duration'],
            'higher_staff_id' => $higher,
            'is_anticipate'   => $check_data['data']['is_anticipate'],
            'date_at'         => $date,
            'references'      => json_encode($references),
            'serial_no'       => (!empty($serialNo) ? 'OT'.$serialNo : null),
            'wf_role'         => 'ot_new',
            'sub_type'        => $sub_type,
        ];

        $db = $this->getDI()->get('db');
        $db->begin();
        $overtimeId = $this->overtime->addOvertime($insertParam);

        if ($overtimeId && empty($paramIn['is_bi'])) { //非bi途径
            try {

                //创建
                $server    = new ApprovalServer($this->lang, $this->timezone);
                $requestId = $server->create($overtimeId, AuditListEnums::APPROVAL_TYPE_OVERTIME, $staffId, null, $extend);
                if (!$requestId) {
                    throw new \Exception('创建审批流失败');
                }
                $db->commit();
            } catch (\Exception $e) {
                $db->rollback();
                $this->wLog('pushError', $e->getMessage(), 'overtime');
                return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
            }
        } else {
            if (!empty($paramIn['is_bi'])) { //bi添加加班
                //添加到给调休假的表
                $otModel = new HrOvertimeForLeaveModel();
                $otModel->setOvertimeId($overtimeId);
                $otModel->setStaffInfoId($staffId);
                $otModel->setDuration($check_data['data']['duration']);
                $otModel->setDateAt($date);
                $otModel->save();

                $db->commit();
                return $this->checkReturn(['data' => ['overtime_id' => $overtimeId]]);
            } else {
                $db->rollback();
                return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
            }
        }
        return $this->checkReturn(['data' => ['overtime_id' => $overtimeId]]);
    }

    /**
     * 修改状态
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @throws \Exception
     */
    public function updateOvertimeV3($paramIn = [])
    {
        $staffId       = $this->processingDefault($paramIn, 'staff_id', 2);
        $overtimeId    = $this->processingDefault($paramIn, 'audit_id', 2);
        $reject_reason = $this->processingDefault($paramIn, 'reject_reason');
        $state         = $this->processingDefault($paramIn, 'status', 2);

        //获取审批详情
        $overtimeList = $this->overtime->infoOvertime(['overtime_id' => $overtimeId]);
        if (empty($overtimeList)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }
        //oa 审批处理中 不能操作
        if($overtimeList['in_approval'] == HrOvertimeModel::IN_APPROVAL && empty($paramIn['is_mq'])){
            return $this->checkReturn(-3,$this->getTranslation()->_('ot_in_approval_notice'));
        }
        $server = new ApprovalServer($this->lang, $this->timezone);
        if ($state == enums::$audit_status['approved']) {
            //同意
            $flag = $server->approval($overtimeId, AuditListEnums::APPROVAL_TYPE_OVERTIME, $staffId);
        } else {
            //驳回
            $flag = $server->reject($overtimeId, AuditListEnums::APPROVAL_TYPE_OVERTIME, $reject_reason, $staffId);
        }
        return $this->checkReturn(['data' => ['audit_id' => $overtimeId]]);
    }

    /**
     * 校验申请加班日期、班次
     * @param int $overtime_type 加班类型
     * @param array $shift_info 班次信息
     * @param string $date 加班日期
     * @return void
     * @throws \Exception
     */
    public function checkOTTimePeriod($overtime_type, $shift_info, $date)
    {
        $date_tmp   = strtotime($date);
        $beforeTime = $behindTime = strtotime(date('Y-m-d'), time());

        //OFF Day加班、Rest Day加班、法定节假日加班班
        //ot 可以多两天时间区间限制
        if (in_array($overtime_type, array_keys($this->holidayOt))) {
            $behindTime = strtotime(date("Y-m-d", strtotime("+2 day")));
        }

        //如果是班次跨天的员工，比如23：00-8：00班次，跨天的班次，可以申请前一天的加班。
        $shift_start = strtotime("{$date} {$shift_info['start']}");
        $shift_end   = strtotime("{$date} {$shift_info['end']}");
        if ($shift_start > $shift_end && in_array($overtime_type, array_keys($this->overOt))) {
            $beforeTime = strtotime(date("Y-m-d", strtotime("-1 day")));
        }

        if ($date_tmp < $beforeTime || $date_tmp > $behindTime) {
            //获取最全的列表
            $typeList = $this->getAllOtType();
            $typeArr  = array_column($typeList, 'msg', 'code');

            //在许可时间外申请，提示错误
            if (in_array($overtime_type, [2, 4])) {
                $notice_str = str_replace('{type}', $typeArr[$overtime_type],
                    $this->getTranslation()->_('err_msg_ot_over_2_days'));
            } else {
                if ($overtime_type == 1) {
                    $notice_str = $this->getTranslation()->_('err_msg_ot_only_cur_day');
                } else {
                    $notice_str = str_replace('{type}', $typeArr[$overtime_type],
                        $this->getTranslation()->_('err_msg_ot_only_rest_day'));
                }
            }
            throw new ValidationException($this->getTranslation()->_($notice_str));
        }
    }

    /**
     * 校验除 时间范围内的 其他逻辑 添加ot 和bi 的 修改ot
     * 加班类型
     * 1-工作日加班1.25倍日薪,
     * 2-休息日加班1.3倍日薪,
     * 3休息日超时加班1.69倍日薪,
     * 4-RH加班1倍日薪,
     * 5-RH超时加班2.6倍日薪,
     * @param $paramIn
     * @throws \Exception
     */
    public function checkOvertime($paramIn)
    {
        $staffId    = $this->processingDefault($paramIn, 'staff_id', 2);
        $type       = $this->processingDefault($paramIn, 'type', 2);
        $start_time = $this->processingDefault($paramIn, 'start_time');
        $duration   = floatval($paramIn['duration']);
        $date       = $this->processingDefault($paramIn, 'date_at');

        $startTime = strtotime($start_time);
        $endTime   = $startTime + floatval($duration) * 3600;

        $current_date = date('Y-m-d', time());    //今天日期
        $start_date   = date('Y-m-d', $startTime);//申请开始时间日期
        //添加和修改都有
        $u_info = $this->staffInfo;

        //申请ot  类型相关限制 休息日 工作日 ph 等 https://l8bx01gcjr.feishu.cn/docs/doccnIzLTlZxBjvNHdZiSIo8eVd#

        //如选择“周末和假期加班”，系统验证该申请日期是否为公休日和周末，如申请日期非周末和假期
        $audit_server = new AuditServer($this->lang, $this->timezone);
        $holidays     = $audit_server->get_holidays($u_info);

        //如果 是节假日 不能请 休息日和工作日加班
        if (in_array($date, $holidays) && !in_array($type, [4, 5])) {
            throw new ValidationException($this->getTranslation()->_('wrong_ot_type_on_holiday'));
        }

        //查询是不是 休息日
        $is_rest = false;
        $check   = HrStaffWorkDayModel::findFirst("staff_info_id = {$staffId} and date_at = '{$date}'");
        if (!empty($check)) {
            $is_rest = true;
        }
        //休息日加班类型 限制 并且 不是节假日
        if ($is_rest && !in_array($date, $holidays) && !in_array($type, [2, 3])) {
            throw new ValidationException($this->getTranslation()->_('wrong_ot_type_on_rest_day'));
        }

        //新增验证 日期限制逻辑 hcm 配置页面
        $this->checkLimitDate();

        //工作日  非休息日 和节假日
        if (!in_array($date, $holidays) && !$is_rest) {
            if ($type != 1) {
                throw new ValidationException($this->getTranslation()->_('wrong_ot_type_on_work_day'));
            }
        }


        //法律规定 如果申请 工作日加班 所在自然周不能超过 18小时总时长 限制不能申请
        if ($type == 1) {
            //获取所在周 的工作日类型加班 审批状态 1，2
            $week_start = weekStart($date);
            $week_end   = weekEnd($date);
            $check      = HrOvertimeModel::findFirst([
                'columns'    => 'sum(duration) as duration',
                'conditions' => "staff_id = {$staffId} and state in (1,2) and date_at between '{$week_start}' and '{$week_end}'",
                'group'      => 'staff_id',
            ]);

            if (!empty($check) && ($check->duration > 18 || ($check->duration + $duration) > 18)) {
                $left = 18 - $check->duration;
                throw new ValidationException($this->getTranslation()->_('ph_18_hour_notice',['left' => $left]));
            }
        }


        //判断是预申请还是 补申请
        $is_anticipate = 0;
        if ($start_date >= $current_date) {
            $is_anticipate = 1;//是预申请
        }


        // 校验实际 加班 时长
        $act_last = $duration;
        if ($act_last >= 24) {
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_24'));
        }

        if ($act_last <= 0) {
            return $this->checkReturn(-3, $this->getTranslation()->_('5101'));
        }


        $ext_server = new OvertimeExtendServer($this->lang, $this->timeZone);
        //只能当天申请的 并且 必须打上班卡的 类型

        if (in_array($type, [1, 3, 5])) {
            //新需求 https://l8bx01gcjr.feishu.cn/docs/doccnznGnDKzb4akgPuhYQq5oHc
            $new_check = $ext_server->extend_check($paramIn, $u_info);
            if ($new_check['code'] != 1) {
                return $new_check;
            }
        }
        //节假日上班类型
        if (in_array($type, [2, 4])) {
            //申请时，选择开始时间和结束时间，开始日期和结束日期必须为同一天或相邻；
            if ($act_last >= 24) {
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_24'));
            }
        }


        //校验 同一天是否存在 有交集的ot
        if (empty($paramIn['is_edit'])) {
            $is_contain = $ext_server->check_ot_record($staffId, $date, $startTime, $endTime, $type);
            if ($is_contain['code'] != 1) {
                return $is_contain;
            }
        }


        $res['data']['duration']      = $act_last;
        $res['data']['is_anticipate'] = $is_anticipate;
        return $this->checkReturn($res);
    }


    /**
     * 获取依赖数据
     *   1. 职位为Hub Supervisor 以及 Branch Supervisor的员工开放所有加班类型的申请入口
     *   2. 基本工资大于等于2000，没有加班申请入口; 基本工资小于2000，开放所有加班类型的申请入口
     *
     * @param $staffId
     * @param $type
     * @param $date
     * @return array
     */
    public function getReference($staffId, $param)
    {
        //马来固化申请人的职位
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $staffId,
            ],
        ]);
        if ($staffInfo) {
            return [["job_title" => $staffInfo->job_title ?? 0], []];
        } else {
            return [[], []];
        }
    }


    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //[1]获取加班详情数据
        $result = $this->overtime->infoOvertime(['overtime_id' => $auditId]);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_id']);
        if ($staff_info['data']) {
            $staff_info = $staff_info['data'];
        }

        //[2]组织详情数据
        $t                = $this->getTranslation();
        $overtimeTypeList = $this->getAllOtType();
        $overtimeType     = array_column($overtimeTypeList, 'msg', 'code');
//        $overtimeSubtype = array_column($overtimeTypeList, 'sub_msg', 'code');

        $detailLists = [
            'apply_parson'     => sprintf('%s ( %s )', $staff_info['name'], $staff_info['id']),
            'apply_department' => sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? ''),
            'OT_date'          => $result['date_at'],
            'OT_type'          => ($overtimeType[$result['type']] ?? ''),
            'start_time'       => $result['start_time'],
            'end_time'         => (in_array($result['type'], array_keys($this->holidayOt)) && $result['duration'] == 8)
                ? date('Y-m-d H:i:s', strtotime($result['end_time'].' + 1 hours'))
                : $result['end_time'],
            'duration'         => $result['duration'],
            'OT_reason'        => $result['reason'],
            'ot_detail_6'      => $staff_info['store_name'],
        ];

        $references = json_decode($result['references'], true) ?? '';

        if (isset($references['store_category']) && in_array($references['store_category'],
                explode(',', HrOvertimeModel::$job_apply['shop_category']))
            && in_array($references['job_title'],
                [enums::$job_title['shop_officer'], enums::$job_title['shop_cashier']])
        ) {
            $detailLists = array_merge($detailLists, [
                'ot_detail_11'   => $references['region_name'] ?? '',                                  //员工所在大区名称
                'attendance_num' => $references['attendance_num'] ?? '',                               //员工申请OT日所在网点出勤人数
                'parcel_num'     => $references['parcel_num'] ?? '',                                   //员工申请OT日所在网点总揽派件量
                'ot_detail_10'   => ($references['all_effective_num'] ?? 0).' '.$t->_('ot_detail_4'),  //上周SHOP相关网点平均工作效率
                'ot_detail_2'    => ($references['store_effective_num'] ?? 0).' '.$t->_('ot_detail_4'),//上周特定网点平均工作效率
            ]);

            if (date("Y-m-d", strtotime($result['start_time'])) > date('Y-m-d', strtotime($result['created_at']))) {
                unset($detailLists['attendance_num']);
                unset($detailLists['parcel_num']);
            }

            if ($result['type'] == 1) { //1。5倍工资 才显示
                $detailLists = array_merge($detailLists, [
                    'ot_detail_3' => ($references['duration'] ?? 0)."h",
                ]);
            }
            if ($result['type'] == 2) { //3倍工资 才显示
                $detailLists = array_merge($detailLists, [
                    'ot_detail_9' => ($references['duration'] ?? 0)."h",
                ]);
            }
            if ($result['type'] == 4) { //1倍工资 才显示
                $detailLists = array_merge($detailLists, [
                    'ot_detail_8' => ($references['duration'] ?? 0)."h",
                ]);
            }
        }

        $returnData['data']['detail'] = $this->format($detailLists);

        $data = [
            'title'       => $this->auditlist->getAudityType(enums::$audit_type['OT']),
            'id'          => $result['overtime_id'],
            'staff_id'    => $result['staff_id'],
            'type'        => enums::$audit_type['OT'],
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['state'],
            'status_text' => $this->auditlist->getAuditStatus('10'.$result['state']),
            'notice'      => $result['notice'] ?? '',
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        $returnData['data']['head'] = $data;
        return $returnData;
    }


    /**
     * 审批完成回调方法
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        //如果为最终审批状态，则同步更新审批状态
        if ($isFinal) {
            $info        = HrOvertimeModel::findFirst("overtime_id = {$auditId}");
            $info->state = $state;
            if ($state == Enums::APPROVAL_STATUS_REJECTED) {
                if (isset($extend['staff_id'])) {
                    $staff = HrStaffInfoModel::findFirst([
                        'conditions' => ' staff_info_id = :staff_id: ',
                        'bind'       => ['staff_id' => $extend['staff_id']]
                    ]);
                    if ($staff) {
                        $staff = $staff->toArray();
                    }
                }
                $info->approver_id   = isset($extend['staff_id']) ? $extend['staff_id'] : 0;
                $info->approver_name = isset($staff) && $staff ? $staff['name'] : '';
                $info->reject_reason = isset($extend['remark']) ? $extend['remark'] : '';
            }
            $info->in_approval = HrOvertimeModel::NOT_IN_APPROVAL;
            $info->update();
            if ($state == enums::APPROVAL_STATUS_APPROVAL && $info->sub_type == 1) {
                //添加到给调休假的表
                $otModel = new HrOvertimeForLeaveModel();
                $otModel->setOvertimeId($auditId);
                $otModel->setStaffInfoId($info->staff_id);
                $otModel->setDuration($info->duration);
                $otModel->setDateAt($info->date_at);
                $otModel->save();

                $this->logger->write_log(sprintf("%s 已经添加调休假(ot id %s)", $info->staff_id, $auditId), 'info');
            }
            $this->sendMessage($info->toArray(),$state);
        }
    }

    //工具 调用 非app

    /**
     * 编辑加班  bi工具
     * @param $paramIn
     */
    public function edit_overtime($paramIn)
    {
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        //工作日校验 加班类型1=工作日，2=节假日加班，3=晚班 4-节假日正常上班
        $type        = $this->processingDefault($paramIn, 'type', 2);
        $start_time  = $this->processingDefault($paramIn, 'start_time');
        $date        = $this->processingDefault($paramIn, 'date_at');
        $duration    = floatval($paramIn['duration']);
        $overtime_id = intval($paramIn['overtime_id']);

        if (empty($date) || empty($start_time) || empty($type) || empty($overtime_id)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        $startTime             = strtotime($start_time);
        $endTime               = $startTime + floatval($duration) * 3600;
        $end_time              = date('Y-m-d H:i:s', $endTime);
        $paramIn['is_edit']    = 1;
        $shiftServer           = new HrShiftServer();
        $shift_info            = $shiftServer->getShiftInfos($staffId, [$date]);
        $paramIn['shift_info'] = $shift_info[$date] ?? [];
        $staff_model           = new StaffRepository();
        $this->staffInfo       = $staff_model->getStaffPosition($staffId);
        $this->param           = $paramIn;
        $res                   = $this->checkOvertime($paramIn);

        if ($res['code'] != 1) {
            return $res;
        }

        $model = new OvertimeRepository($this->timezone);
        $info  = $model->getInfoById($overtime_id);
        if ($info['state']  !=  enums::APPROVAL_STATUS_APPROVAL && empty($paramIn['due_to_holiday'])){//非审核通过的 记录 不允许 修改 没走完正常审核逻辑
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_bi_notice'));
        }

        $update_data = [
            'type'            => $type,
            'start_time'      => $start_time,
            'end_time'        => $end_time,
            'state'           => isset($paramIn['due_to_holiday']) ? $info['state'] :  enums::APPROVAL_STATUS_APPROVAL,//bi 工具 直接审核通过 因holiday变更的除外
            'duration'        => $res['data']['duration'],
            'higher_staff_id' => $paramIn['operator'],
            'is_anticipate'   => $res['data']['is_anticipate'],
            'date_at'         => $date,
        ];

        $model = new OvertimeRepository($this->timezone);
        $flag  = $model->updateInfoByTable('hr_overtime', 'overtime_id', $overtime_id, $update_data);
        if($flag){
            if ($type != $info['type']) {
                (new AuditApplyRepository)->editOtTypeOfSummary($overtime_id,$staffId,$type);
            }
            return $this->checkReturn([]);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }
    }

    /**
     * 获取加班类型，用于hcm-api系统展示
     * @param $paramIn
     * @return array
     */
    public function getOvertimeTypeList($paramIn = [])
    {
        if(empty($paramIn['staff_id'])) {
            return [];
        }
        $data = $this->getTypeOvertime($paramIn);
        return $data['data']['dataList'] ?? [];
    }


}
