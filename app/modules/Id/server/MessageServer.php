<?php
/**
 * Author: Bruce
 * Date  : 2022-11-11 03:38
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Id\Server;

use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\MessageServer AS GlobalBaseServer;


class MessageServer extends GlobalBaseServer
{
    protected $re;
    public    $timezone;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang, $timezone);
        $this->timezone = $timezone;
    }

    //电子警告书 电子签名 保存图片
    public function sign_for_warning($param)
    {
        $param = filter_param($param);
        // 更新签名图片
        //判断身份 后 发送给上级消息 或 证人消息
        $warningInfo = $this->getWarningMessage($param['staff_info_id'], $param['msg_id']);

        $db = $this->getDI()->get("db");

        if (!empty($warningInfo['role']) && $warningInfo['role'] == self::MSG_STAFF_TYPE_OPERATOR) {
            if ($warningInfo['operator_img']) {
                return $this->checkReturn(-3, $this->getTranslation()->_('5202'));
            }
            if (empty($warningInfo['superior_id'])) {
                $this->getDI()->get("logger")->write_log($warningInfo['staff_info_id']." 不存在上级", "error");
                return $this->checkReturn([]);
            }

            // 更新表中更新签名
            $flag = $db->updateAsDict("message_warning", [
                "operator_img" => $param['url'] // 更新签名
            ], ["conditions" => 'id='.$warningInfo['id']]);

            if (!$warningInfo['superior_kit_id']) {
                // 如果没有发送给上级 发送上级给消息
                $this->sendWarningMessage($warningInfo['superior_id'], $warningInfo['id'], $warningInfo['role']);
            }
        } else {
            if (!empty($warningInfo['role']) && $warningInfo['role'] == self::MSG_STAFF_TYPE_SUPERIOR) {
                if ($warningInfo['superior_img']) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('5202'));
                }

                // 更新签名
                // 证人信息
                $flag = $db->updateAsDict("message_warning", [
                    "superior_img" => $param['url'],
                ], ["conditions" => 'id='.$warningInfo['id']]);

                // 发送证人消息，my没有证人消息，只写pdf
                $this->sendWarningMessage($warningInfo['staff_info_id'], $warningInfo['id'], $warningInfo['role']);
            }
        }

        // 消息更新为已读
        if (isset($flag) && $flag) {
            $db = $this->getDI()->get('db_coupon');
            $db->updateAsDict("message_courier", ['read_state' => 1], ['conditions' => "id='".$param['msg_id']."'"]);

            // 消息已读
            $server = new BackyardServer($this->lang, $this->timezone);
            $server->addHaveReadMsgToMns($param['msg_id']);
            // 签字日志
            $sign_log_data = [
                'staff_info_id'      => $param['staff_info_id'] ?? 0,
                'kit_id'             => $param['msg_id'] ?? '',
                'message_warning_id' => $warningInfo['id'] ?? 0,
                'is_auto_sign'       => 0,
            ];
            $this->getDI()->get('db')->insertAsDict('message_warning_sign_log', $sign_log_data);
        }

        return true;
    }
}