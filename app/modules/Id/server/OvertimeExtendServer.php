<?php

namespace FlashExpress\bi\App\Modules\Id\Server;

use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditPermissionModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\OvertimeExtendServer as GlobalBaseServer;

class OvertimeExtendServer extends GlobalBaseServer
{
    /**
     * @param $staffId
     * @param $date
     * @param $start_time 时间戳
     * @param $end_time
     * @param $type
     * @return array
     */
    public function check_ot_record($staffId,$date,$start_time,$end_time,$type)
    {
        $start_time = date('Y-m-d H:i:s',$start_time);
        $end_time = date('Y-m-d H:i:s',$end_time);
        //检查是否有重复记录  2，3 可以重复 4，5 可以重复
        $type_arr = array(1,2,3,4,5);

        //互斥逻辑
        if($type == HrOvertimeModel::OVERTIME_1){
            //工作日加班 所有互斥
        }
        if(in_array($type,[HrOvertimeModel::OVERTIME_2,HrOvertimeModel::OVERTIME_3])){
            //2，3 为一组 和其他所有 互斥
            $type_arr = array_diff($type_arr,[2,3]);
        }
        if(in_array($type,[HrOvertimeModel::OVERTIME_4,HrOvertimeModel::OVERTIME_5])){
            $type_arr = array_diff($type_arr,[4,5]);
        }
        //把本次申请类型加上
        $type_arr = array_merge($type_arr,[$type]);

        $exist = HrOvertimeModel::find(
            [
                'conditions' => "state in (1,2) and staff_id = {$staffId}  and date_at = '{$date}' and type in({type_arr:array})",
                'bind'       => [
                    'type_arr'=> $type_arr,
                ],
                'columns' => 'overtime_id'
            ]
        )->toArray();
        if(!empty($exist)) {
            throw new ValidationException($this->getTranslation()->_('5102'));
        }

        if(!in_array($type,array(2,3,4,5))) {//1。5倍ot 下面不需要校验
            return $this->checkReturn([]);
        }
        $where = "state in (1,2) and staff_id = {$staffId}  and date_at = '{$date}' 
                          and (
                            (start_time < '{$start_time}' and end_time > '{$start_time}') or 
                            (start_time < '{$end_time}' and end_time > '{$end_time}') or 
                            (start_time < '{$start_time}' and end_time > '{$end_time}') or 
                            (start_time >= '{$start_time}' and end_time <= '{$end_time}') 
                          )";

        switch ($type) {
            case 2:
                $where .= " and type != 3";
                break;
            case 3:
                $where .= " and type != 2";
                break;
            case 4:
                $where .= " and type != 5";
                break;
            case 5:
                $where .= " and type != 4";
                break;
            default:
                break;
        }
        $check = HrOvertimeModel::find([
            'conditions' => $where,
            'bind'       => [
                'type_arr'=> $type_arr,
            ],
            'columns' => 'overtime_id'
        ])->toArray();
        $this->logger->write_log("ot_check_exist {$staffId} start_time {$start_time},end_time {$end_time} ",'info');

        if(!empty($check))
            throw new ValidationException($this->getTranslation()->_('5102'));

        return $this->checkReturn([]);
    }

    /**
     * 新增的逻辑验证
     * @param $param
     * @param $user_info
     * @return array
     */
    public function extend_check($param, $user_info)
    {
        $config_hour = $this->config->application->add_hour;
        $staff_id = $user_info['staff_info_id'];
        $date = date('Y-m-d',strtotime($param['date_at']));

        //刘佳雪需求 https://l8bx01gcjr.feishu.cn/docs/doccnIzLTlZxBjvNHdZiSIo8eVd#
        //全部向后后推9.5小时
        $add_hour = 9.5;

        //获取请假记录 如果加班日当天 存在下午请假
        $leave_re = new AuditRepository($this->lang);
        $leave_info = $leave_re->get_leave_date($staff_id,$date,$date);
        //如果是请上午假 加5小时 其他情况不让申请ot 休息日类型假期 剔除
        if(!empty($leave_info) && $leave_info[0]['leave_type'] != 15){
            if($leave_info[0]['type'] != 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_leave_limit'));
            }
            $add_hour = 5;
        }

        //没有班次信息 不让申请
        if(empty($param['shift_info'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('no_shift_notice'));
        }

        //如果 没打上班卡 不让申请
        $att_info = StaffWorkAttendance::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
        if(empty($att_info)){
            //没上班卡 判断是否出差 取出差打卡 上班卡信息
            $att_info = StaffAuditReissueForBusinessModel::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
            if(empty($att_info))
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_att_start'));
            $att_info = $att_info->toArray();
            $att_info['started_at'] = $att_info['start_time'];
        }else{
            $att_info = $att_info->toArray();
            $param['shift_info']['start'] = !empty($att_info['shift_start']) ? $att_info['shift_start'] : $param['shift_info']['start'];
        }

        if(empty($att_info['started_at'])){
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_att_start'));
        }

        //去秒数 容差 59秒
        $att_info['started_at'] = date('Y-m-d H:i:00',strtotime("{$att_info['started_at']}"));
        //跟班次比对 如果是迟到 加班开始时间 应该在 迟到小时+1小时 时间整点
        $shift_start_time = strtotime("{$date} {$param['shift_info']['start']}");
        $card_time = strtotime($att_info['started_at']) - $this->second + ($config_hour * 3600);
        //没迟到  取班次时间整点 加对应的小时数
        $limit_start = date('Y-m-d H:i:s',$shift_start_time + ($add_hour * 3600));
        if($card_time > $shift_start_time){//如果迟到 取打卡时间 加1小时 再加上对应的小时数
            $shift_i = date("i",$shift_start_time);//取班次的分钟
            $limit_start = date("Y-m-d H:{$shift_i}:00",$card_time + 3600);
            $limit_start = date('Y-m-d H:i:s',strtotime($limit_start) + ($add_hour * 3600));
        }

        $ot_start_time = date('Y-m-d H:i:s',strtotime($param['start_time']));
        $l1 = date('Y-m-d H:i:s',$shift_start_time);
        $l2 = date('Y-m-d H:i:s',$card_time);
        $this->logger->write_log("{$staff_id} add_hour {$add_hour},shift_start_time {$l1},card_time(-59) {$l2},limit_start {$limit_start},ot_start_time {$ot_start_time} ",'info');
        /*刘佳雪需求*/
        if(empty($param['is_bi'])) {
            if ($ot_start_time < $limit_start)
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_forbidden'));
        }

        return $this->checkReturn([]);
    }

}