<?php

namespace  FlashExpress\bi\App\Modules\Th\library\Enums;


/**
 * 常用枚举数据
 * Class enums
 */
final class CommonEnums
{
    const FLASH_HOME_OPERATION = 1222;         //Flash Home Operation
    const FLASH_FULFILMENT_COMPANY_ID = 20001; //Flash Fulfillment
    const FLASH_MONEY_COMPANY_ID = 30001;      //Flash Money
    const FLASH_COMMERCE_COMPANY_ID = 50001;   //F Commerce
    const FLASH_PAY_COMPANY_ID = 60001;        //Flash Pay
    //不展示在职证明的部门
    const HIDDEN_ON_JOB_PDF_SYS_DEPARTMENT_IDS = [
        self::FLASH_FULFILMENT_COMPANY_ID,
        self::FLASH_MONEY_COMPANY_ID,
        self::FLASH_COMMERCE_COMPANY_ID,
        self::FLASH_HOME_OPERATION,
        self::FLASH_PAY_COMPANY_ID,
    ];

    //van courier
    const JO<PERSON>_TITLE_VAN_COURIER =  110;

    //ev van courier
    const JOB_TITLE_EV_VAN_COURIER =  1930;

    //Van Courier (Project)
    const JOB_TITLE_VAN_COURIER_PROJECT =  1015;
    //bike courier
    const JOB_TITLE_BIKE_COURIER =  13;
    //boat courier
    const JOB_TITLE_BOAT_COURIER =  452;
    //或tricycle courier，
    const JOB_TITLE_TRICYCLE_COURIER =  1000;
    //van feeder
    const JOB_TITLE_VAN_FEEDER =  1497;
    //courier installation staff
    const JOB_TITLE_COURIER_INSTALLATION_STAFF =  1663;
    //installation staff
    const JOB_TITLE_INSTALLATION_STAFF =  1664;

    //Branch Supervisor[16]
    const JOB_TITLE_BRANCH_SUPERVISOR =  16;
    //Senior Branch Supervisor
    const JOB_TITLE_SENIOR_BRANCH_SUPERVISOR =  1786;
    //DC Officer[37]
    const JOB_TITLE_DC_OFFICER =  37;
    //Assistant Branch Supervisor[451]
    const JOB_TITLE_ASSISTANT_BRANCH_SUPERVISOR =  451;
    //Pickup Driver [1844]
    const JOB_TITLE_PICKUP_DRIVER = 1844;
}