<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/1/23
 * Time: 8:41 PM
 */

namespace FlashExpress\bi\App\Modules\Th\library\Enums;

use FlashExpress\bi\App\Enums\BaseEnums;

class OvertimeEnums extends BaseEnums
{
    public static function getCodeTxtMap($lang = '')
    {
        $txt = $code ?? '';
        return $txt;
    }

    //泰国 审批流走向 参数枚举
    const APPROVAL_CHECK_STORE = 1;//hub 网点
    const APPROVAL_CHECK_HUB_HEAD = 2;//hub 总部
    const APPROVAL_CHECK_NW_BS = 3;//nw branch_supervisor
    const APPROVAL_CHECK_NW_BS_BULKY = 8;// nw bulky branch_supervisor
    const APPROVAL_CHECK_NW_COURIER = 4;//nw 快递
    const APPROVAL_CHECK_NW_DC = 6;//nw 仓管
    const APPROVAL_CHECK_NW_DC_BULKY = 7;//nw bulky 仓管
    const APPROVAL_CHECK_DEFAULT = 5;//nw 默认

    const FFM_SHOW_TYPE_OVER = 1;//ffm 超时加班参考数据 出勤相关
    const FFM_SHOW_TYPE_REST = 2;//ffm 节假日加班参考数据 排班相关

}