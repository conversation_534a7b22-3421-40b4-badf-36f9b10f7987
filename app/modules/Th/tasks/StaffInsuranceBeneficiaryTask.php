<?php

namespace FlashExpress\bi\App\Modules\Th\Tasks;

use FlashExpress\bi\App\Modules\Th\Server\StaffInsuranceBeneficiaryServer;

class StaffInsuranceBeneficiaryTask extends \BaseTask
{
    /**
     * 给未编辑保险受益人工号发送消息
     * @param $params
     * @return void
     */
    public function send_messageAction($params)
    {
        $date        = $params[0] ?? '';
        $staffInfoId = $params[1] ?? '';

        $staffInsuranceBeneficiaryServer = new StaffInsuranceBeneficiaryServer($this->lang, $this->timezone);;
        $data = $staffInsuranceBeneficiaryServer->getInsuranceBeneficiaryStaffList($date,$staffInfoId);

        if (empty($data)) {
            $this->echo('Success');
            return;
        }

        $staffInsuranceBeneficiaryServer->sendRemindMessage($data);

        $this->echo('SUCCESS');
    }
}