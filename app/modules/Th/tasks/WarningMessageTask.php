<?php
/**
 * Author: Bruce
 * Date  : 2023-02-16 16:37
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Th\Tasks;

use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\MessageWarningModel;
use FlashExpress\bi\App\Models\backyard\MessageWarningTransferSignModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\WarningSignConfigModel;
use FlashExpress\bi\App\Server\MessageServer;
use WarningMessageTask as BaseWarningMessageTask;

class WarningMessageTask extends BaseWarningMessageTask
{

    /**
     * th new 每天跑超过三天没有签字的 警告书 进行转交
     * @param $params
     * @return void
     */
    public function mainAction($params)
    {
        // 获取全部没签的数据
        $log  = 'WarningMessageTask mainAction, 系统所在国家: ' . strtoupper(env('country_code', 'TH')) . PHP_EOL;
        $log  .= '开始时间: ' . date('Y-m-d H:i:i:s') . PHP_EOL;
        $messageServer = new MessageServer($this->lang, $this->timezone);
        $data = $messageServer->warning_message_transmit($params);
        $log .= ",有效处理警告书ids : " . json_encode($data, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        $log .= "结束时间: " . date('Y-m-d H:i:i:s') . PHP_EOL;
        $this->getDI()->get('logger')->write_log($log, 'info');
        exit($log);
    }

    /**
     * th 一次性 修复数据
     * @param $params
     * @return void
     */
    public function fix_dataAction($params)
    {
        // 获取全部没签的数据
        $log  = 'WarningMessageTask mainAction, 系统所在国家: ' . strtoupper(env('country_code', 'TH')) . PHP_EOL;
        $log  .= '开始时间: ' . date('Y-m-d H:i:i:s') . PHP_EOL;
        $messageServer = new MessageServer($this->lang, $this->timezone);
        $data = $messageServer->warning_message_transmit_fix_data($params);
        $log .= ",有效处理警告书ids : " . json_encode($data, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        $log .= "结束时间: " . date('Y-m-d H:i:i:s') . PHP_EOL;
        $this->getDI()->get('logger')->write_log($log, 'info');
        exit($log);
    }

    /**
     * 每天跑超过三天没有签字的 警告书
     *
     */
    public function main1Action($params)
    {
        try {
            if (isset($params[0])) {
                $date  = $params[0];
                $begin = gmdate('Y-m-d H:i:s', strtotime($date . ' 00:00:00'));
                $end   = gmdate('Y-m-d H:i:s', strtotime($date . ' 23:59:59'));
            } else {
                $date  = date('Y-m-d', strtotime(" -3 days "));
                $begin = gmdate('Y-m-d H:i:s', strtotime($date . ' 00:00:00'));
                $end   = gmdate('Y-m-d H:i:s', strtotime($date . ' 23:59:59'));
            }

            $where['start']           = $begin;
            $where['end']             = $end;
            $where['role']            = MessageServer::MSG_STAFF_TYPE_STAFF;
            $toSuperiorWarningMessage = $this->getMessageWarningList($where);

            $messageServer = new MessageServer($this->lang, $this->timezone);
            //转交给上级
            foreach ($toSuperiorWarningMessage as $k => $message) {
                // 获取上级
                $mangerInfo = $messageServer->getSuperiorInfo($message['staff_info_id']);
                if ($mangerInfo) {
                    $messageServer->sendWarningMessage($mangerInfo['staff_id'], $message['id'],
                        MessageServer::MSG_STAFF_TYPE_STAFF, $mangerInfo['level']);
                    echo "warning_message ok  " . json_encode($message, JSON_UNESCAPED_UNICODE) . "\r\n";
                    $this->getDI()->get("logger")->write_log("warning_message ok  " . json_encode($message,
                            JSON_UNESCAPED_UNICODE), "info");
                }
            }

            $needFixWarningToId = [];
            $transferSignList   = $this->getMessageWarningTransferSign($where);
            if ($transferSignList) {
                $transferWarningIds = array_values(array_unique(array_column($transferSignList, 'message_warning_id')));
                $needFixWarning     = $this->getMessageWarningList(['ids' => $transferWarningIds]);
                $needFixWarningToId = array_column($needFixWarning, null, 'id');
            }

            $transferSuperior = [];//转上上级或hrbp
            $transferWitness  = [];//证人转hrbp 或 er
            $transferStaffId  = [];//转交过的工号，防止重复发送
            //按 警告id + type 分组
            foreach ($transferSignList as $oneTransfer) {
                if ($oneTransfer['type'] == MessageWarningTransferSignModel::TRANSFER_TYPE_SUPERIOR) {
                    $transferSuperior[$oneTransfer['message_warning_id']][] = $oneTransfer;
                }

                if ($oneTransfer['type'] == MessageWarningTransferSignModel::TRANSFER_TYPE_WITNESS) {
                    $transferWitness[$oneTransfer['message_warning_id']][] = $oneTransfer;
                }
                $transferStaffId[$oneTransfer['message_warning_id']][] = $oneTransfer['staff_info_id'];//已发过的工号
            }

            //转交上级
            foreach ($transferSuperior as $key => $oneData) {
                $isMaxLevel   = false;//是否转交到HRBP
                $maxLevel     = 0;    //转交给哪一级
                $maxLevelInfo = [];
                //找出 节点 最高
                foreach ($oneData as $one) {
                    if ($one['level'] == MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_BP) {
                        $isMaxLevel = true;
                    }
                    if ($one['level'] > $maxLevel) {
                        $maxLevel     = $one['level'];
                        $maxLevelInfo = $one;
                    }
                }
                //最高节点,已经转交给HRBP 无需处理，跳过
                if ($isMaxLevel) {
                    continue;
                }

                //未找到警告信息 跳过
                if (!isset($needFixWarningToId[$key])) {
                    continue;
                }

                //上级位置已经签完字 跳过
                if (!empty($needFixWarningToId[$key]['superior_img'])) {
                    continue;
                }
                //上级位置未签完字进行处理
                //如果上级未签字，则继续转交
                if ($maxLevel == MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR) {
                    $this->transferSuperior($messageServer, $maxLevelInfo, $needFixWarningToId[$key],
                        $transferStaffId[$key]);
                }

                //如果已经转交到上上级，则发给 hrbp
                if ($maxLevel == MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR_SUPERIOR) {
                    $this->transferSuperiorToHrBp($messageServer, $needFixWarningToId[$key]);
                }
            }

            $erConfigs      = $this->getWarningSignConfig();
            $erConfigsToIds = array_column($erConfigs, 'staff_info_id');
            //转交证人
            foreach ($transferWitness as $key => $oneData) {
                $isSendHr  = false;//是否是HR
                $isSendEr  = false;//是否是Er
                $bpStaffId = 0;
                $erStaffId = 0;
                //找出 节点 最高
                foreach ($oneData as $one) {
                    if ($one['level'] == MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_BP) {
                        $isSendHr  = true;
                        $bpStaffId = $one['staff_info_id'];
                    }
                    if ($one['level'] == MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER) {
                        $isSendEr  = true;
                        $erStaffId = $one['staff_info_id'];
                    }
                }

                //如果两个证人选的一个hrbp 一个er，挑过
                if ($isSendHr == true && $isSendEr == true) {
                    continue;
                }

                //未找到警告信息 跳过
                if (!isset($needFixWarningToId[$key])) {
                    continue;
                }

                //两个证人都签完字，跳过
                if (!empty($needFixWarningToId[$key]['witness1_img']) && !empty($needFixWarningToId[$key]['witness2_img'])) {
                    continue;
                }

                //获取上级角色,是否为 HRBP
                $superiorIsHrbp = $this->checkHrBp($needFixWarningToId[$key]['superior_id']);

                //两个证人都没签字 且 非er hrbp
                if ($isSendHr == false && $isSendEr == false && empty($needFixWarningToId[$key]['witness1_img']) && !empty($needFixWarningToId[$key]['witness1_kit_id']) && empty($needFixWarningToId[$key]['witness2_img']) && !empty($needFixWarningToId[$key]['witness2_kit_id'])) {
                    //上级是HR
                    $witnessIds = [];
                    if ($superiorIsHrbp) {
                        //换一个与上级HR 不重复的hr
                        $hrStaffId = $this->transferWitnessToHrBp($messageServer, $needFixWarningToId[$key],
                            $superiorIsHrbp);
                        if ($hrStaffId) {//存入要 发送的 人名单
                            $witnessIds[] = $hrStaffId;
                        }
                        //没找到hr 并且 没有er
                        if (!$hrStaffId && empty($erConfigsToIds)) {
                            continue;
                        }
                        //选了1个hr,er不为空
                        if (!empty($witnessIds) && !empty($erConfigsToIds)) {
                            $witnessKey   = array_rand($erConfigsToIds);
                            $witnessIds[] = $erConfigsToIds[$witnessKey];
                            $messageServer->sendWarningMessage(implode(',', $witnessIds), $key,
                                MessageServer::MSG_STAFF_TYPE_SUPERIOR);
                            continue;
                        }
                        //没有er 跳过
                        if (empty($erConfigsToIds)) {
                            continue;
                        }
                        //如果没有HR 则选2个 er
                        if (count($erConfigsToIds) >= 2) {
                            $keys = array_rand($erConfigsToIds, 2);
                            foreach ($keys as $oneKey) {
                                $witnessIds[] = $erConfigsToIds[$oneKey];
                            }
                            $messageServer->sendWarningMessage(implode(',', $witnessIds), $key,
                                MessageServer::MSG_STAFF_TYPE_SUPERIOR);
                            continue;
                        }
                        //只有1个就发一个er
                        $messageServer->sendWarningMessage(implode(',', $erConfigsToIds), $key,
                            MessageServer::MSG_STAFF_TYPE_SUPERIOR);
                        continue;
                    }
                    //如果上级位置不是HRBP，则发给HR+ER， 修改证人1和2
                    $hrStaffId = $this->transferWitnessToHrBp($messageServer, $needFixWarningToId[$key],
                        $superiorIsHrbp);
                    if ($hrStaffId) {//存入要 发送的 人名单
                        $witnessIds[] = $hrStaffId;
                    }

                    //没找到hr 并且 没有er
                    if (!$hrStaffId && empty($erConfigsToIds)) {
                        continue;
                    }

                    if (empty($witnessIds)) {
                        //如果没有HR 则选2个 er
                        if (count($erConfigsToIds) >= 2) {
                            $keys = array_rand($erConfigsToIds, 2);
                            foreach ($keys as $oneKey) {
                                $witnessIds[] = $erConfigsToIds[$oneKey];
                            }
                            $messageServer->sendWarningMessage(implode(',', $witnessIds), $key,
                                MessageServer::MSG_STAFF_TYPE_SUPERIOR);
                            continue;
                        }
                    } else {
                        if ($erConfigsToIds) {
                            $keys         = array_rand($erConfigsToIds);
                            $witnessIds[] = $erConfigsToIds[$keys];
                        }
                    }
                    $messageServer->sendWarningMessage(implode(',', $witnessIds), $key,
                        MessageServer::MSG_STAFF_TYPE_SUPERIOR);
                    continue;
                }

                //如果两个证人 选了1个 HRBP, 将另一个证人，转给er
                if ($isSendHr == true && $isSendEr == false) {
                    if (empty($erConfigsToIds)) {
                        continue;
                    }
                    $keys          = array_rand($erConfigsToIds);
                    $erStaffIdFind = $erConfigsToIds[$keys];

                    //当前证人中存在 er 中, 则从所有 er 中 去除当前证人，再随机找一个 ER, (用于 去重)。
                    if (in_array($erStaffIdFind,
                        [$needFixWarningToId[$key]['witness1_id'], $needFixWarningToId[$key]['witness2_id']])) {
                        $newErConfigsToIds = array_diff($erConfigsToIds,
                            [$needFixWarningToId[$key]['witness1_id'], $needFixWarningToId[$key]['witness2_id']]);
                        if (empty($newErConfigsToIds)) {
                            continue;
                        }
                        $keys          = array_rand($newErConfigsToIds);
                        $erStaffIdFind = $newErConfigsToIds[$keys];
                    }

                    if ($bpStaffId == $needFixWarningToId[$key]['witness1_id'] && empty($needFixWarningToId[$key]['witness1_img'])) {
                        //发给 证人2
                        //如果证人2已经是 er 则不再找了。
                        if (in_array($needFixWarningToId[$key]['witness2_id'],
                                $erConfigsToIds) && !empty($needFixWarningToId[$key]['witness2_img'])) {
                            continue;
                        }

                        $messageServer->sendWarningMessage($erStaffIdFind, $key,
                            MessageServer::MSG_STAFF_TYPE_SUPERIOR,
                            MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER, 2);
                        continue;
                    }

                    if ($bpStaffId == $needFixWarningToId[$key]['witness2_id'] && empty($needFixWarningToId[$key]['witness2_img'])) {
                        //发给 证人1
                        //如果证人1已经是 er 则不再找了。
                        if (in_array($needFixWarningToId[$key]['witness1_id'],
                                $erConfigsToIds) && !empty($needFixWarningToId[$key]['witness1_img'])) {
                            continue;
                        }

                        $messageServer->sendWarningMessage($erStaffIdFind, $key,
                            MessageServer::MSG_STAFF_TYPE_SUPERIOR,
                            MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER, 1);
                        continue;
                    }
                    continue;
                }
                //如果两个证人 选了1个 ER, 将另一个证人，转给HRBP
                if ($isSendHr == false && $isSendEr == true) {
                    //找HRBP
                    $hrStaffId = $this->transferWitnessToHrBp($messageServer, $needFixWarningToId[$key],
                        $superiorIsHrbp);
                    if (empty($hrStaffId)) {//未找到 跳过
                        continue;
                    }

                    //当前证人中存在, 则换一个 hrbp
                    if (in_array($hrStaffId,
                        [$needFixWarningToId[$key]['witness1_id'], $needFixWarningToId[$key]['witness2_id']])) {
                        //查找被警告人所属管辖的HRBP
                        $sendStaffInfo = $messageServer->getHrBp($needFixWarningToId[$key]['staff_info_id']);
                        //把上级位置的 工号也过滤掉，上级位置为 hrbp 也不可以重复。
                        $all_hrbp = array_diff($sendStaffInfo['all_hrbp_id'], [
                            $needFixWarningToId[$key]['witness1_id'],
                            $needFixWarningToId[$key]['witness2_id'],
                            $needFixWarningToId[$key]['superior_id'],
                        ]);
                        if (empty($all_hrbp)) {
                            continue;
                        }
                        $keys      = array_rand($all_hrbp);
                        $hrStaffId = $all_hrbp[$keys];
                    }

                    if ($erStaffId == $needFixWarningToId[$key]['witness1_id']) {
                        //发给 证人2
                        //判断证人 2 如果是 hr 则跳过。不重新指定 HRBP
                        if ($this->checkHrBp($needFixWarningToId[$key]['witness2_id'])) {
                            continue;
                        }

                        $messageServer->sendWarningMessage($hrStaffId, $key, MessageServer::MSG_STAFF_TYPE_SUPERIOR,
                            MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_BP, 2);
                        continue;
                    }

                    if ($erStaffId == $needFixWarningToId[$key]['witness2_id']) {
                        //发给证人1
                        //判断证人1 如果是 hr 则跳过。不重新指定 HRBP
                        if ($this->checkHrBp($needFixWarningToId[$key]['witness1_id'])) {
                            continue;
                        }

                        $messageServer->sendWarningMessage($hrStaffId, $key, MessageServer::MSG_STAFF_TYPE_SUPERIOR,
                            MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_BP, 1);
                        continue;
                    }
                    continue;
                }
                //处理两个 证人 非 hrbp 非 er 且只有1个人签字了。
                $witness = 0;
                if (empty($needFixWarningToId[$key]['witness1_img']) && !empty($needFixWarningToId[$key]['witness1_kit_id'])) {
                    $witness = 1;
                }
                if (empty($needFixWarningToId[$key]['witness2_img']) && !empty($needFixWarningToId[$key]['witness2_kit_id'])) {
                    $witness = 2;
                }
                //不知道发给哪个证人，则跳过
                if (empty($witness)) {
                    continue;
                }

                //先找HRBP，如果没找到则发给er
                $staffId = $this->transferWitnessToHrBp($messageServer, $needFixWarningToId[$key], $superiorIsHrbp);
                $level   = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_BP;//bp
                if (empty($staffId)) {//未找到hr_则找er
                    if (empty($erConfigsToIds)) {
                        continue;
                    }
                    $keys    = array_rand($erConfigsToIds);
                    $staffId = $erConfigsToIds[$keys];
                    $level   = MessageWarningTransferSignModel::TYPE_WITNESS_LEVEL_ER;//er
                }

                $messageServer->sendWarningMessage($staffId, $key, MessageServer::MSG_STAFF_TYPE_SUPERIOR, $level,
                    $witness);
            }
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("warning_message   Error_msg " . $e->getMessage() . " Error_file " . $e->getFile() . " Error_line " . $e->getFile() . " " . $e->getTraceAsString(),
                "error");
        }
    }

    /**
     * 转交上级
     * @param $transferInfo
     * @param $warningInfo
     * @param $messageServer
     * @param $transferStaffId
     * @return bool
     */
    public function transferSuperior($messageServer, $transferInfo, $warningInfo, $transferStaffId)
    {
        $level         = MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR_SUPERIOR;
        $sendStaffInfo = $messageServer->getMangerLevelInfo($transferInfo['staff_info_id'],
            MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR_SUPERIOR);
        //如果是上上级，已经被发送过。则直接找HRBP, 上级 与 上上级相同的情况 才会触发
        if (empty($sendStaffInfo) || in_array($sendStaffInfo['staff_id'], $transferStaffId)) {
            $sendStaffInfo = $messageServer->getHrBp($warningInfo['staff_info_id']);//查找被警告人所属管辖的HRBP
            $level         = MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_BP;
        }
        if (empty($sendStaffInfo)) {
            $this->logger->write_log('警告书id:' . $warningInfo['id'] . '被警告人：' . $warningInfo['staff_info_id'] . '没有找到上级,HRBP 也没找到',
                'notice');
            return false;
        }
        $messageServer->sendWarningMessage($sendStaffInfo['staff_id'], $warningInfo['id'],
            MessageServer::MSG_STAFF_TYPE_STAFF, $level);
        return true;
    }

    /**
     * 上级位置转交给Hrbp
     * @param $warningInfo
     * @param $messageServer
     * @param $transferStaffId
     * @return bool
     */
    public function transferSuperiorToHrBp($messageServer, $warningInfo)
    {
        $sendStaffInfo = $messageServer->getHrBp($warningInfo['staff_info_id']);//查找被警告人所属管辖的HRBP
        if (empty($sendStaffInfo)) {
            $this->logger->write_log('警告书id:' . $warningInfo['id'] . '被警告人：' . $warningInfo['staff_info_id'] . '没有查找上级未找到HRBP',
                'notice');
            return false;
        }
        $messageServer->sendWarningMessage($sendStaffInfo['staff_id'], $warningInfo['id'],
            MessageServer::MSG_STAFF_TYPE_STAFF, MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_BP);
        return true;
    }

    /**
     * 证人位置转交给Hrbp
     * @param $warningInfo
     * @param $messageServer
     * @param $superiorIsHrbp
     * @param $witnessNum -发送给证人编号
     * @return bool
     */
    public function transferWitnessToHrBp($messageServer, $warningInfo, $superiorIsHrbp = false)
    {
        $sendStaffInfo = $messageServer->getHrBp($warningInfo['staff_info_id']);//查找被警告人所属管辖的HRBP
        if (empty($sendStaffInfo)) {
            $this->logger->write_log('警告书id:' . $warningInfo['id'] . '被警告人：' . $warningInfo['staff_info_id'] . '证人未签字，转发给Hrbp,但未找到HRBP',
                'notice');
            return 0;
        }
        if ($superiorIsHrbp && $sendStaffInfo['staff_id'] == $warningInfo['superior_id']) {//如果要 发的hrbp 是上级位置的，则换一个hr
            $hrbpStaffId = 0;
            //找一个不重复的Hr
            foreach ($sendStaffInfo['all_hrbp_id'] as $oneHr) {
                if ($oneHr != $sendStaffInfo['staff_id']) {
                    $hrbpStaffId = $oneHr;
                }
            }
            if (empty($hrbpStaffId)) {//没找到其他hr;
                return 0;
            }
            $sendStaffInfo['staff_id'] = $hrbpStaffId;
        }

        return $sendStaffInfo['staff_id'];
    }

    /**
     * 获取警告书信息
     * @param $params
     * @return mixed
     */
    public function getMessageWarningList($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(MessageWarningModel::class);
        $builder->where(" warning_type in ({warning_types:array})",
            ['warning_types' => array_keys(MessageWarningModel::$warning_types)]);
        $builder->andWhere('is_delete = :is_delete:', ['is_delete' => MessageWarningModel::DELETE_NO]);


        // 自警告书生成之日起：获取被警告人3天内没有签字的警告书信息
        if (!empty($params['role']) && $params['role'] == MessageServer::MSG_STAFF_TYPE_STAFF) {
            $builder->andWhere(" kit_id != '' and img_url = '' and superior_id = 0 ");
        }

        if (!empty($params['start'])) {
            $builder->andWhere("created_at >= :start: ", ['start' => $params['start']]);
        }
        if (!empty($params['end'])) {
            $builder->andWhere("created_at <= :end:", ['end' => $params['end']]);
        }

        if (!empty($params['ids'])) {
            $builder->andWhere("id in ({ids:array})", ['ids' => $params['ids']]);
        }

        $builder->columns('id, staff_info_id, kit_id, superior_id, superior_kit_id, superior_img, witness1_id, witness1_kit_id, witness1_img, witness2_id, witness2_kit_id, witness2_img');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取警告书非被警告人记录
     * @param $params
     * @return mixed
     */
    public function getMessageWarningTransferSign($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(MessageWarningTransferSignModel::class);
        $builder->where("created_at >= :start:", ['start' => $params['start']]);
        $builder->andWhere("created_at <= :end:", ['end' => $params['end']]);
        $builder->columns('id, message_warning_id, kit_id, staff_info_id, level, type, created_at');
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 获取员工在职状态
     * @param $staffInfoIds
     * @return mixed
     */
    public function getStaffInfoState($staffInfoIds)
    {
        if ($staffInfoIds) {
            return [];
        }
        return HrStaffInfoModel::find([
            'conditions' => "staff_info_id in ({staff_ids:array}) and item='MANGER'",
            'bind'       => ['staff_ids' => $staffInfoIds],
            'columns'    => 'staff_info_id,state',
        ])->toArray();
    }

    /**
     * 获取签名配置信息
     * @param $staff_info_id
     * @return array
     */
    public function getWarningSignConfig()
    {
        return WarningSignConfigModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'signature_node=:signature_node:',
            'bind'       => [
                'signature_node' => WarningSignConfigModel::SIGNATURE_NODE_WITNESS,
            ],
        ])->toArray();
    }

    /**
     * 判断是否为 HRBP
     * @param $staffId
     * @return bool
     */
    public function checkHrBp($staffId)
    {
        //获取上级角色
        $staffRoles = [];
        $positions  = HrStaffInfoPositionModel::find([
            "conditions" => " staff_info_id = :warning_staff_id:",
            "bind"       => ['warning_staff_id' => $staffId],
        ])->toArray();
        if (!empty($positions)) {
            $staffRoles = array_column($positions, 'position_category');
        }
        return in_array(RolesModel::ROLE_HR_BP, $staffRoles) ? true : false;
    }

}