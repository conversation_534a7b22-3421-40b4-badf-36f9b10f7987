<?php

namespace FlashExpress\bi\App\Modules\Th\Tasks;

use FlashExpress\bi\App\Models\backyard\HrStaffIdentityAnnexModel;
use FlashExpress\bi\App\Modules\Th\Server\PersoninfoServer;

class PersonInfoTask extends \BaseTask
{

    public function ai_bank_card_examineAction($params)
    {
        $offset = $params[0] ?? 0;
        $limit = 15000;
        $log  = '任务: PersonInfoTask ai_bank_card_examineAction 开始时间: '.date('Y-m-d H:i:i:s').PHP_EOL;
        $data = (new PersoninfoServer($this->lang, $this->timezone, []))->ai_bank_card_examine($offset,$limit);

        $log .= "本次待处理员工数: ".count($data[0]).PHP_EOL;
        $log .= "本次实际处理员工数: ".(count($data[0]) - count($data[1])).PHP_EOL;
        if ($data[1]) {
            $log .= "本次处理失败员工ids: ".json_encode($data[1], JSON_UNESCAPED_UNICODE).PHP_EOL;
        }
        $log .= "结束时间: ".date('Y-m-d H:i:i:s').PHP_EOL;
        $this->getDI()->get('logger')->write_log($log, 'info');
        exit($log);
    }


    /**
     * 一次性脚本 刷银行卡审核状态
     */
    public function salary_to_bank_cardAction($params)
    {
        $salary_date = $params[0] ?? '2023-11';
        $log  = '任务: PersonInfoTask salary_to_bank_cardAction 开始时间: '.date('Y-m-d H:i:i:s').PHP_EOL;
        $data = (new PersoninfoServer($this->lang, $this->timezone, []))->salary_to_bank_card($salary_date);
        $log .= "本次处理员工数: ".$data["count"].PHP_EOL;
        $log .= "本次处理员工ids: ".$data["ids"].PHP_EOL;
        $log .= "本次处理员工diff_error_ids: ".$data["diff_error_ids"].PHP_EOL;
        $log .= "结束时间: ".date('Y-m-d H:i:i:s').PHP_EOL;
        $this->getDI()->get('logger')->write_log($log, 'info');
        exit($log);

    }


}