<?php

namespace FlashExpress\bi\App\Modules\Th\Tasks;

use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Server\ApprovalServer;
use AuditTask as AuditBaseTask;

class AuditTask extends AuditBaseTask
{
    public function initialize()
    {
        $this->tq = "create-approval";//业务上先择的消费rmq的内容
        parent::initialize(); // TODO: Change the autogenerated stub
    }
    /**
     * 
     * mainAction
     * @param $msgBody
     * @return bool
     */
    protected function processOneMsg($msgBody): bool
    {
        try {
            //[1]获取消息体
            $this->logger->write_log('processOneMsg th' . base64_decode($msgBody), 'info');
            $msgData = $this->getMessageData($msgBody);
            if (empty($msgData)) {
                return true;
            }

            //[2]获取参数
            $submitterId = $msgData['data']['apply_user'];
            $uuid        = $msgData['data']['apply_uuid'];
            $auditType   = $msgData['data']['apply_type'];
            $data        = $msgData['data']['apply_data'];
            $applyTime   = $msgData['data']['apply_timestamp'];

            //获取实例
            $server = new ApprovalServer($this->lang, $this->timezone);
            $appServer = $server->getInstance($auditType);

            if (method_exists($appServer, 'consumerProcessing')) {
                $appServer->consumerProcessing($data, $submitterId, $auditType, $uuid, $applyTime);
            }

        } catch (\Exception $e) {
            $this->logger->write_log('TaskAudit:processOneMsg:' . $e->getMessage());
            return true;
        }
        return true;
    }

    /**
     * 发送异常，补发 消息
     * @param $params
     * @return bool
     */
    public function sendAction($params)
    {
        if (empty($params[0])) {
            echo "请传 消息参数";
            return false;
        }

        $rmq = new RocketMQ('create-approval');
        $params = $params[0];
        $params = json_decode($params, true);
        $rid = $rmq->sendToMsg($params);
        if ($rid) {
            echo "success";
            return true;
        }
        echo "fail";
        return false;
    }
}