<?php
/**
 *InteriorOrderTask.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/8/10 0010 19:56
 */
namespace FlashExpress\bi\App\Modules\Th\Tasks;

use app\enums\LangEnums;
use FlashExpress\bi\App\Enums\InteriorGoodsEnums;
use FlashExpress\bi\App\Enums\InteriorOrderFundStatusEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Mail;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersModel;
use FlashExpress\bi\App\Enums\InteriorOrderStatusEnums;
use FlashExpress\bi\App\Enums\InteriorGoodsPayMethodEnums;
use FlashExpress\bi\App\Repository\BankListRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StoreRepository;
use FlashExpress\bi\App\Modules\Th\Server\InteriorGoodsServer;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\FlashPayServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class InteriorOrderTask extends \BaseTask
{
    /**
     * 离职当日，未审核，待发货，自定取消订单
     * 离职的员工有未审核待发货（工资抵扣）、预定中的订单才可以做取消操作
     * Created by: Lqz.
     * CreateTime: 2020/8/12 0012 18:58
     */
    public function autoCancelOrderAction()
    {
        $InteriorGoodsServer = new InteriorGoodsServer();
        // 查询未审核的，未发货的订单（待发货，预售）
        $conditions = 'order_status in ({orderStatus:array}) and is_audited = 0 and pay_method = :pay_method:';
        $bind = [
            "orderStatus" =>
                [
                    InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE,
                ],
            'pay_method' => InteriorGoodsPayMethodEnums::PAY_METHOD_DEDUCTION_OF_WAGES_CODE
        ];
        $ordersObj  = InteriorOrdersModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        $orders     = $ordersObj->toArray();
        if ($orders) {
            $staffIds = array_column($orders, 'staff_id');
            // 查询员工离职日期小于当前日期的数据，对比离职日期
            $staffsObj  = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({staff_ids:array}) and state = 2',
                'bind'       => ['staff_ids' => $staffIds],
            ]);
            $staffs     = $staffsObj->toArray();
            $staffsKey  = array_column($staffs, null, 'staff_info_id');
            $successNum = $errorNum = 0;
            foreach ($orders as $key => $order) {
                $num = $key + 1;
                //如果存在该订单的员工，取消订单
                if (isset($staffsKey[$order['staff_id']])) {
                    $loginUser = [
                        'staff_id' => $order['staff_id']
                    ];
                    $params = [
                        'order_code'    => $order['order_code'],
                        'cancel_reason' => InteriorOrderStatusEnums::ORDER_CANCEL_REASON_PRE_SALE,
                    ];
                    $res       = $InteriorGoodsServer->cancelOrder($loginUser, $params, 'system_auto_canceled');
                    if ($res === true) {
                        $successNum++;
                        echo "第{$num}条SUCCESS:order_code:{$order['order_code']}自动取消订单成功" . PHP_EOL;
                    } else {
                        $errorNum++;
                        echo $msg = "第{$num}条ERROR:order_code:{$order['order_code']}自动取消订单失败" . var_export($res, true) . PHP_EOL;
                        $logger = $this->getDI()->get('logger');
                        $logger->write_log($msg, 'info');
                    }
                } else {
                    echo "订单【{$order['order_code']}】员工ID：{$order['staff_id']},暂无离职信息" . PHP_EOL;
                }
            }
            echo "自动取消订成功:{$successNum} 个，失败{$errorNum}个，已全部执行完毕" . PHP_EOL;
        } else {
            echo "No orders need to auto cancel" . PHP_EOL;;
        }
    }

    /**
     * 同步FlashPay交易状态到订单表
     * 1. 获取FlashPay在线支付待付款、支付中的订单列表；
     * 然后根据每一笔订单去获取pay那边的当前交易状态；
     * 其中0、交易待支付（该订单不做任何处理）；
     * 2、交易处理中（需将该订单状态由待付款变更为支付中，已是支付中的订单可不做处理）；
     * 3、交易成功（需将该订单状态由待付款(支付中)的订单在同步SCM成功后状态变更为待发货）；
     * 4、交易失败； 5、交易关闭（需将该订单变更为已取消）
     */
    public function syncFlashPayTradeStatusAction()
    {
        $ordersObj = InteriorOrdersModel::find([
            'conditions' => 'order_status in ({order_status:array}) and pay_method = :pay_method:',
            'bind' => [
                'order_status' => [
                    InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_PAYING_CODE,
                ],
                'pay_method' => InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE,
            ],
        ]);
        if ($ordersObj->toArray()) {
            foreach ($ordersObj as $order) {
                $msg = '订单号：【' . $order->order_code . '】在脚本syncFlashPayTradeStatus同步FlashPay交易状态到订单状态';
                try {
                    $flash_pay_server = new FlashPayServer();
                    $flash_pay_server->syncFlashPayTradeStatus($order);
                    $msg .= '成功' . PHP_EOL;
                } catch (\Exception $e) {
                    $msg = '异常：【' . $e->getMessage() . '】' . PHP_EOL;
                }
                echo $msg;
                $logger = $this->getDI()->get('logger');
                $logger->write_log($msg, 'info');
            }
        } else {
            echo 'no order need sync tradeStatus to OrderStatus' . PHP_EOL;;
        }
    }

    /**
     * FlashPay在线支付订单超过1小时未支付的订单自动取消脚本
     * 获取支付方式为FlashPay的下单时间超过1小时待支付的订单做订单取消操作
     */
    public function autoCancelTimeoutUnpaidAction()
    {
        $ordersObj = InteriorOrdersModel::find([
            'columns' => ['id'],
            'conditions' => "order_status = :order_status: and pay_method = :pay_method: and order_expire_at < :order_expire_at:",
            'bind' => [
                'order_status' => InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE,
                'pay_method' => InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE,
                'order_expire_at' => date('Y-m-d H:i:s'),
            ],
        ]);

        $orders = $ordersObj->toArray();
        if ($orders) {
            $cancel_order_arr = array_column($orders, 'id');
            $count = count($cancel_order_arr);
            $cancel_order_ids = implode(',', $cancel_order_arr);
            try {
                $date = date('Y-m-d H:i:s');
                $res = $this->db->updateAsDict(
                    'interior_orders',
                    [
                        'order_status' => InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE,
                        'updated_at' => $date,
                        'canceled_at' => $date,
                        'cancel_reason' => InteriorOrderStatusEnums::ORDER_CANCEL_REASON_SYSTEM,
                    ],
                    ["conditions" => 'id in (' . $cancel_order_ids . ')']
                );
                if (!$res) {
                    throw new \Exception('auto cancel timeout unpaid orders failed');
                }
                $msg = 'autoCancelTimeoutUnpaid超时1小时未支付订单自动取消订单ID组为：【' . $cancel_order_ids . '】；共计操作取消订单数：【' . $count . '】条' . PHP_EOL;
            } catch (\Exception $e) {
                $msg = 'autoCancelTimeoutUnpaid超时1小时未支付订单自动取消失败：' . $e->getMessage() . PHP_EOL;

            }
            echo $msg;
            $logger = $this->getDI()->get('logger');
            $logger->write_log($msg, 'info');
        } else {
            echo 'No expired orders need to auto cancel' . PHP_EOL;;
        }
    }

    /**
     * 员工商城定时邮件推送待退款数据
     */
    public function sendRefundReminderAction()
    {
        $log = '发送员工商城待退款数据邮件提醒' . PHP_EOL;
        try {
            //邮箱
            $emails = (new SettingEnvServer())->getSettingEnvValueMap('interior_refund_reminder_emails');
            if ($emails) {
                // 获取上个月的第一天
                $last_monday_date = date('Y-m-01', strtotime('last month'));

                // 获取上个月最后一天
                $last_sunday_date = date('Y-m-t', strtotime('last month'));
                $log .= "数据范围是：{$last_monday_date}至{$last_sunday_date}" . PHP_EOL;
                $interior_orders = InteriorOrdersModel::find([
                    'conditions' => 'goods_type = :goods_type: AND fund_status = :fund_status: AND canceled_at >= :canceled_at_start: AND canceled_at <= :canceled_at_end:',
                    'bind' => [
                        'goods_type' => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES,
                        'fund_status' => InteriorOrderFundStatusEnums::FUND_STATUS_REFUNDING,
                        'canceled_at_start' => $last_monday_date . ' 00:00:00',
                        'canceled_at_end' => $last_sunday_date . ' 23:59:59',
                    ],
                ])->toArray();
                if (!empty($interior_orders)) {
                    //获取网点名称
                    $store_ids = array_values(array_unique(array_column(array_filter($interior_orders, function ($order) { return $order['staff_store_id'] != enums::HEAD_OFFICE_ID; }), 'staff_store_id')));
                    $store_list = (new StoreRepository())->getStoreListByIds($store_ids);

                    //获取部门
                    $department_ids = array_values(array_unique(array_column($interior_orders, 'node_department_id')));
                    $department_list = (new DepartmentRepository())->getDepartmentByIds($department_ids);
                    $department_list = array_column($department_list, null, 'id');

                    //获取员工信息
                    $staff_ids = array_values(array_unique(array_column($interior_orders, 'staff_id')));
                    $staff_list = (new StaffRepository())->getStaffListByStaffIds($staff_ids);

                    //获取员工名下银行信息
                    $staff_bank_type = array_values(array_unique(array_column($staff_list, 'bank_type')));
                    $bank_list = (new BankListRepository())->getBankListByIds($staff_bank_type);

                    //标题
                    $title_zh = "{$last_monday_date}至{$last_sunday_date}工服待退款数据";
                    $title_th = "ข้อมูลรอคืนเงินของชุดทำงานตั้งแต่ {$last_monday_date} ถึง {$last_sunday_date}";

                    $log .= '订单数据是：' . $title_zh . PHP_EOL;

                    //附件名称组
                    $file_name = [
                        LangEnums::LANG_CODE_ZH_CN => $title_zh . '.xlsx',
                        LangEnums::LANG_CODE_TH => $title_th . '.xlsx'
                    ];

                    //附件地址
                    $file_path = [];

                    //实际文件名（用于邮件发送）
                    $actual_file_names = [];

                    //FFM货主
                    $ffm_mach_code = (new InteriorGoodsServer())->getFfmMachCode();

                    //需要导出中文、泰文两个附件信息
                    foreach ([LangEnums::LANG_CODE_ZH_CN, LangEnums::LANG_CODE_TH] as $lang) {
                        $order_status = InteriorOrderStatusEnums::getCodeTxtMap($lang);//订单状态
                        $pay_method = InteriorGoodsPayMethodEnums::payMethod($lang);//支付方式
                        $fund_status = InteriorOrderFundStatusEnums::getCodeTxtMap($lang);//款项状态
                        $header = [
                            LangEnums::getTranslation($lang, 'serial_number'),//序号
                            LangEnums::getTranslation($lang, 'interior_order_code'),//订单编号,
                            LangEnums::getTranslation($lang, 'interior_out_sn'),//出库单号
                            LangEnums::getTranslation($lang, 'interior_order_flash_pay_code'),//FlashPay交易号
                            LangEnums::getTranslation($lang, 'interior_order_status'),//订单状态
                            LangEnums::getTranslation($lang, 'interior_pay_method'),//支付方式
                            LangEnums::getTranslation($lang, 'interior_order_fund_code'),//款项状态
                            LangEnums::getTranslation($lang, 'interior_order_pay_amount'),//订单金额
                            LangEnums::getTranslation($lang, 'interior_staff_id'),//员工工号
                            LangEnums::getTranslation($lang, 'interior_staff_name'),//姓名
                            LangEnums::getTranslation($lang, 'interior_store_name'),//网点名称
                            LangEnums::getTranslation($lang, 'interior_department_name'),//部门
                            LangEnums::getTranslation($lang, 'interior_submit_time'),//下单时间
                            LangEnums::getTranslation($lang, 'interior_flash_pay_at'),//支付日期
                            LangEnums::getTranslation($lang, 'interior_canceled_at'),//取消订单时间
                            LangEnums::getTranslation($lang, 're_field_bank_type'),//收款人开户银行
                            LangEnums::getTranslation($lang, 're_field_bank_account'),//收款人账号
                            LangEnums::getTranslation($lang, 're_field_bank_name'),//收款人户名
                        ];
                        $excel_data = [];//excel数据
                        $group_counters = []; // 每个分组的计数器
                        $group_log_parts = []; // 用于构建日志的数组

                        foreach ($interior_orders as $key => $order) {
                            // 安全的货主判断逻辑
                            $email_key = $this->determineEmailKeyByMachCode($order['mach_code'], $ffm_mach_code);

                            // 验证email_key的有效性
                            if (!in_array($email_key, ['Express', 'FFM'])) {
                                $log .= "跳过订单 {$order['order_code']}，因为无法确定货主类型，mach_code: {$order['mach_code']}" . PHP_EOL;
                                continue;
                            }

                            // 只有当对应的邮箱配置不为空时，才分组数据
                            if (empty($emails[$email_key])) {
                                $log .= "跳过订单 {$order['order_code']}，因为 {$email_key} 邮箱配置为空" . PHP_EOL;
                                continue;
                            }

                            // 初始化分组计数器
                            if (!isset($group_counters[$email_key])) {
                                $group_counters[$email_key] = 0;
                            }
                            $group_counters[$email_key]++;

                            // 同时更新日志信息（避免后续循环）
                            $group_log_parts[$email_key] = "  {$email_key}: {$group_counters[$email_key]} 条记录";

                            $excel_data[$email_key][] = [
                                $group_counters[$email_key], // 使用分组内的连续序号
                                $order['order_code'],
                                $order['out_sn'],
                                $order['flash_pay_code'],
                                $order_status[$order['order_status']],
                                $pay_method[$order['pay_method']],
                                $fund_status[$order['fund_status']],
                                $order['pay_amount'],
                                $order['staff_id'],
                                $order['staff_name'],
                                ($order['staff_store_id'] == enums::HEAD_OFFICE_ID) ? enums::HEAD_OFFICE : ($store_list[$order['staff_store_id']]['name'] ?? ''),
                                $department_list[$order['node_department_id']]['name'] ?? '',//所属部门
                                $order['submit_at'],
                                $order['flash_pay_at'],
                                $order['canceled_at'],
                                $bank_list[$staff_list[$order['staff_id']]['bank_type'] ?? '']['bank_name'] ?? '(unknown)',//收款人开户银行
                                $staff_list[$order['staff_id']]['bank_no'] ?? '',//收款人账号
                                $staff_list[$order['staff_id']]['name'] ?? '',//收款人户名
                            ];
                        }

                        // 记录数据分组统计（直接使用主循环中构建的日志信息）
                        $log .= "数据分组统计（语言：{$lang}）：" . PHP_EOL;
                        if (!empty($group_log_parts)) {
                            $log .= implode(PHP_EOL, $group_log_parts) . PHP_EOL;
                        } else {
                            $log .= "  无数据需要处理" . PHP_EOL;
                        }

                        //上传附件
                        $config = [
                            'path' => sys_get_temp_dir()
                        ];

                        foreach ($excel_data as $email_key => $value) {
                            //那个元素下没有数据直接跳过
                            if (empty($value)) {
                                continue;
                            }

                            // 为每个分组创建独立的Excel文件对象
                            $excel = new \Vtiful\Kernel\Excel($config);

                            // 修复：为每个货主生成不同的文件名，避免混淆
                            $fileName = str_replace('.xlsx', "_{$email_key}.xlsx", $file_name[$lang]);

                            $fileObject = $excel->fileName($fileName);
                            $file_path[$email_key][] = $fileObject->header($header)->data($value)->output();

                            // 记录实际的文件名用于邮件发送
                            if (!isset($actual_file_names[$email_key])) {
                                $actual_file_names[$email_key] = [];
                            }
                            $actual_file_names[$email_key][] = $fileName;
                        }
                    }

                    //发送邮件
                    foreach ($emails as $email_key => $email) {
                        // 检查是否有对应的数据文件需要发送
                        if (empty($file_path[$email_key]) || !is_array($file_path[$email_key])) {
                            $log .= "跳过发送邮件到 {$email}，因为 {$email_key} 没有待发送的数据文件" . PHP_EOL;
                            continue;
                        }

                        // 验证文件路径的有效性
                        $valid_files = [];
                        foreach ($file_path[$email_key] as $file) {
                            if (!empty($file) && file_exists($file)) {
                                $valid_files[] = $file;
                            } else {
                                $log .= "警告：{$email_key} 的文件路径无效或文件不存在: {$file}" . PHP_EOL;
                            }
                        }

                        if (empty($valid_files)) {
                            $log .= "跳过发送邮件到 {$email}，因为 {$email_key} 没有有效的文件" . PHP_EOL;
                            continue;
                        }

                        // 发送邮件
                        $email_arr = explode(',', $email);
                        $title = "{$title_zh}({$title_th})";
                        $content = "<p>附件是{$last_monday_date}至{$last_sunday_date}需要退款的员工商城工服订单，请查收并处理！</p><p>该邮件为系统自动发送，无需回复！</p>";
                        $content .= "<p>เอกสารแนบคือคำสั่งซื้อชุดทำงานของร้านค้าพนักงาน ตั้งแต่ {$last_monday_date} ถึง {$last_sunday_date} ที่พนักงานขอเงินคืน โปรดตรวจสอบและดำเนินการ</p><p>ระบบส่งอีเมลนี้โดยอัตโนมัติ ไม่จำเป็นต้องตอบกลับ</p>";
                        $log .= "准备发送 {$email_key} 邮件到: " . implode(',', $email_arr) . "，附件数量: " . count($valid_files) . PHP_EOL;
                        $send_result = Mail::send($email_arr, $title, $content, $valid_files, $actual_file_names[$email_key] ?? []);

                        if ($send_result) {
                            $log .= "邮件发送成功，收件邮箱：{$email_key} -> " . $email . PHP_EOL;
                        } else {
                            $log .= "邮件发送失败，收件邮箱：{$email_key} -> " . $email . PHP_EOL;
                        }
                    }
                } else {
                    $log .= '暂无待退款数据，无需发送' . PHP_EOL;
                }
            } else {
                $log .= '未配置邮箱，无需发送' . PHP_EOL;
            }
        } catch (\Exception $e) {
            $log .= '发送出现异常，原因可能是：'. $e->getMessage() . PHP_EOL;
        }
        $this->logger->write_log($log, 'info');
        exit($log);
    }

    /**
     * 系统自动处理出库单 - 工服 每小时一次
     *
     * php app/cli.php interior_order auto_audit_outbound
     */
    public function auto_audit_outboundAction()
    {
        $this->checkLock(__METHOD__, 7200);

        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            $orderModels = InteriorOrdersModel::find([
                'conditions' => "order_status = :order_status: AND auto_audit_outbound_status = :auto_audit_outbound_status: AND goods_type = :goods_type: AND out_sn != :out_sn:",
                'bind'       => [
                    'order_status'               => InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                    'auto_audit_outbound_status' => InteriorOrderStatusEnums::ORDER_AUTO_AUDIT_OUTBOUND_STATUS_NO,
                    'goods_type'                 => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES,
                    'out_sn'                     => '',
                ],
            ]);

            $orders = $orderModels->toArray();

            $log .= '待自动审核出库订单共 ' . count($orders) . ' 笔' . PHP_EOL;
            if (!empty($orders)) {
                // 查询员工离职状态
                $staffIds = array_column($orders, 'staff_id');

                // 查询员工离职日期小于当前日期的数据，对比离职日期
                $staffs = HrStaffInfoModel::find([
                    'conditions' => 'staff_info_id in ({staff_ids:array})',
                    'bind'       => ['staff_ids' => $staffIds],
                    'columns'    => ['staff_info_id', 'state', 'wait_leave_state'],
                ])->toArray();
                $staffs = array_column($staffs, null, 'staff_info_id');

                $interiorGoodsServer = new InteriorGoodsServer();

                $successNum    = $errorNum = $staffNullNum = $staffMidNum = $processedNum = 0;
                $staffLeaveNum = $leaveCancelSuccessNum = $leaveCancelErrorNum = 0;
                foreach ($orderModels as $key => $orderObj) {
                    $_log = "待处理订单-{$key}: {$orderObj->staff_id} - {$orderObj->order_code} - {$orderObj->out_sn}, pay_amount={$orderObj->pay_amount}";

                    $orderInfo = InteriorOrdersModel::findFirst([
                        'conditions' => 'order_code = :order_code:',
                        'bind'       => ['order_code' => $orderObj->order_code],
                        'columns'    => ['order_status', 'auto_audit_outbound_status'],
                    ]);
                    if ($orderInfo->order_status != InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE || $orderInfo->auto_audit_outbound_status != InteriorOrderStatusEnums::ORDER_AUTO_AUDIT_OUTBOUND_STATUS_NO) {
                        $processedNum++;
                        $_log .= ", 订单已被处理[当前状态: order_status->{$orderInfo->order_status}, auto_audit_outbound_status->{$orderInfo->auto_audit_outbound_status}], 跳过" . PHP_EOL;
                        $this->info($_log, true);
                        continue;
                    }

                    // 自费工服, 直接审核出库
                    if ($orderObj->pay_amount > 0) {
                        if ($interiorGoodsServer->autoAuditOutboundOrder($orderObj)) {
                            $successNum++;
                            $_log .= ', 成功' . PHP_EOL;
                        } else {
                            $errorNum++;
                            $_log .= ', 失败' . PHP_EOL;
                        }
                        $this->info($_log, true);
                    } else {
                        // 免费工服, 看员工在职状态
                        $staff_info = $staffs[$orderObj->staff_id] ?? [];

                        // 员工表不存在, 不处理
                        if (empty($staff_info)) {
                            $staffNullNum++;
                            $_log .= ', 员工不在员工表, 未处理' . PHP_EOL;
                            $this->info($_log, true);
                            continue;
                        }

                        // 停职 或 待离职 不处理
                        if ($staff_info['state'] == HrStaffInfoModel::STATE_3 || ($staff_info['state'] == HrStaffInfoModel::STATE_1 && $staff_info['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE)) {
                            $staffMidNum++;
                            $_log .= ", 员工停职或待离职[{$staff_info['state']}-{$staff_info['wait_leave_state']}], 未处理" . PHP_EOL;
                            $this->info($_log, true);
                            continue;
                        }

                        // 离职 取消出库单, 取消员工订单
                        if ($staff_info['state'] == HrStaffInfoModel::STATE_2) {
                            $staffLeaveNum++;
                            $_log .= '员工已离职, ';

                            $loginUser = [
                                'staff_id' => $orderObj->staff_id,
                            ];
                            $params    = [
                                'order_code'    => $orderObj->order_code,
                                'cancel_reason' => InteriorOrderStatusEnums::ORDER_CANCEL_REASON_PRE_SALE,
                            ];
                            $res       = $interiorGoodsServer->cancelOrder($loginUser, $params,
                                'auto_audit_outbound_staff_leave_canceled');
                            if ($res === true) {
                                $leaveCancelSuccessNum++;
                                $_log .= '取消SCM出库单成功, 订单取消状态标记成功' . PHP_EOL;
                            } else {
                                $leaveCancelErrorNum++;
                                $_log .= "取消SCM出库单异常[msg-{$res['msg']}], 订单状态未处理" . PHP_EOL;
                            }

                            $this->info($_log, true);
                            continue;
                        }

                        // 在职, 自动审核出库单
                        if ($interiorGoodsServer->autoAuditOutboundOrder($orderObj)) {
                            $successNum++;
                            $_log .= ', 成功' . PHP_EOL;
                        } else {
                            $errorNum++;
                            $_log .= ', 失败' . PHP_EOL;
                        }

                        $this->info($_log, true);
                    }

                    sleep(1);
                }

                $log .= "执行完毕: 标记成功 {$successNum} 个, 标记失败 {$errorNum} 个; 被其他脚本处理的 {$processedNum} 个(本次跳过未处理的); 员工信息不在员工表的 {$staffNullNum} 个, 员工停职/待离职的 {$staffMidNum} 个; ";
                $log .= "员工离职的 {$staffLeaveNum} 个 (取消成功 {$leaveCancelSuccessNum} 个, 取消失败 {$leaveCancelErrorNum} 个)" . PHP_EOL;
            } else {
                $log .= '无待自动审核的出库' . PHP_EOL;
            }

        } catch (\Exception $e) {
            $log .= '脚本异常，原因可能是：' . $e->getMessage() . PHP_EOL;
        }

        $this->clearLock(__METHOD__);

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->info($log, true);
        exit();
    }

    /**
     * 安全的货主判断逻辑
     * @description: 根据订单的mach_code安全地确定邮件分组key
     * @author: AI
     * @date: 2025-08-05 20:30:00
     * @param string|null $orderMachCode 订单的货主代码
     * @param string|null $ffmMachCode FFM货主代码
     * @return string 返回 'FFM' 或 'Express'
     */
    private function determineEmailKeyByMachCode(?string $orderMachCode, ?string $ffmMachCode): string
    {
        // 参数验证
        if (empty($orderMachCode)) {
            // 如果订单货主代码为空，默认为Express
            return 'Express';
        }

        if (empty($ffmMachCode)) {
            // 如果FFM货主代码配置为空，所有订单都归为Express
            return 'Express';
        }

        // 严格比较货主代码
        return (trim($orderMachCode) === trim($ffmMachCode)) ? 'FFM' : 'Express';
    }
}
