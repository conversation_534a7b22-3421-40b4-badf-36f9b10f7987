<?php

namespace FlashExpress\bi\App\Modules\Th\Tasks;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Server\JobTransferV2Server;
use JobtransferDoTransferTask as BaseDoTransferTask;

class JobtransferDoTransferTask extends BaseDoTransferTask
{

    public function initialize()
    {
        $this->tq = "do-job-transfer";//业务上先择的消费rmq的内容
        parent::initialize();
    }

    /**
     * 处理消息队列
     * @param $msgBody
     */
    protected function processOneMsg($msgBody)
    {
        $this->logger->write_log('processOneMsg th' . base64_decode($msgBody), 'info');
        $msg_data = $this->getMessageData($msgBody);
        if (empty($msg_data)) {
            return true;
        }

        //[1]获取参数
        $jobTransferId = $msg_data['data']['transfer_id'] ?? 0;
        $operatorId    = $msg_data['data']['operator_id'] ?? 0;
        $server        = Tools::reBuildCountryInstance(new JobTransferV2Server($this->lang, $this->timezone),
            [$this->lang, $this->timezone]);

        //加锁
        $cacheKey = "do_job_transfer_" . $jobTransferId . '_' . $operatorId;
        $server->setTransferLock('do_job_transfer', $cacheKey, $jobTransferId,30);

        //[2]校验
        $transferInfo = JobTransferModel::findFirst($jobTransferId);
        if (isset($transferInfo->state) && $transferInfo->state != JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR) {
            $this->logger->write_log("processOneMsg 转岗状态异常[转岗状态 - {$transferInfo->state}]:" . json_encode($msg_data['data'], JSON_UNESCAPED_UNICODE), 'info');
            return true;
        }

        //获取转岗信息
        $transferList = $server->getJobtransferList([
            "id"    => $jobTransferId,
            "state" => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR
        ]);

        if (empty($transferList)) {
            return true;
        }

        $data = $server->doJobTransfer(['data'           => $transferList,
                                        'manual_operate' => JobTransferEnums::MANUAL_DO_JOB_TRANSFER,
                                        'operator_id'    => $operatorId,
        ]);
        $this->logger->write_log("svc do_job_transfer 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE),
            'info');

        $server->remTransferLock('do_job_transfer', $cacheKey, $jobTransferId);
        return true;
    }

    public function fixAction(){
        //    "jobtransfer_do_transfer_main":"cd /mnt/www/app && php cli.php jobtransfer_do_transfer main",
        $data = base64_encode('{"locale":"en","data":{"transfer_id":"52260","operator_id":"58984"}}');
        $this->processOneMsg($data);
    }


}