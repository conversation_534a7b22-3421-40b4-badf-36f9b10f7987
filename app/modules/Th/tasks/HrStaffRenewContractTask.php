<?php

namespace FlashExpress\bi\App\Modules\Th\Tasks;

use App\Country\Tools;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Modules\Th\Server\HrStaffRenewContractServer;
use FlashExpress\bi\App\Server\ApprovalServer;

class HrStaffRenewContractTask extends \BaseTask
{
    /**
     * 距离当前合同的合同到期日第60天时（北京时间早8:00开始发送）
     * 系统自动发送消息到直线上级
     * @return void
     * @throws \ReflectionException
     */
    public function send_propose_messageAction() {
        echo 'begin' . PHP_EOL;

        $server = new HrStaffRenewContractServer($this->lang, $this->timezone);

        if (!$server->sendRenewContractMessage()) {
            $this->echo('DATA EMPTY');
            exit();
        }

        $this->echo('SUCCESS');
    }

    /**
     * 任务执行时间：每天早上8：50执行
     * 超时关闭
     * @return void
     */
    public function time_outAction()
    {
        echo 'begin' . PHP_EOL;
        $date = DateHelper::localToUtc(date('Y-m-d 00:00:00', strtotime('-29 days')));
        echo '业务日期：' . $date . PHP_EOL;

        $pending_list = AuditApplyModel::find([
            'conditions' => "biz_type = :type: and state = :state: and state = :state: and created_at < :date:",
            'bind'       => [
                'type'  => enums::$audit_type['RC'],
                'state' => enums::$audit_status['panding'],
                'date'  => $date,
            ],
        ])->toArray();

        $approve = new ApprovalServer($this->lang, $this->timezone);
        foreach ($pending_list as $key => $value) {
            $result = $approve->timeOut($value['biz_value'], enums::$audit_type['RC']);
            if(!$result) {
                //超时关闭失败
                $this->logger->write_log([
                    'function' => 'HrStaffRenewContractTask -> time_outAction',
                    'message' => '超时关闭失败',
                    'params' => $value,
                    'result' => $result,
                ]);
                echo '超时关闭 => 失败参数：' . json_encode($value) . PHP_EOL;
                echo '超时关闭失败结果:' . json_encode($result) . PHP_EOL;
            } else {
                echo '超时关闭 => 成功参数：' . json_encode($value) . PHP_EOL;
            }
        }

        echo 'end' . PHP_EOL;
    }

    /**
     * 任务执行时间：每天早上8点执行
     * 超时关闭
     * @return void
     */
    public function send_auto_approvalAction()
    {
        echo 'begin' . PHP_EOL;
        $date = DateHelper::localToUtc(date('Y-m-d 00:00:00', strtotime('-6 days')));
        echo '业务日期：' . $date . PHP_EOL;

        $server = new HrStaffRenewContractServer($this->lang, $this->timezone);

        if (!$server->sendAutoContract($date)) {
            $this->echo('DATA EMPTY');
            exit();
        }

        $this->echo('SUCCESS');
    }
}