<?php

namespace FlashExpress\bi\App\Modules\Th\Tasks;

use ReinstatementTask as BaseReinstatementTask;

class ReinstatementTask  extends BaseReinstatementTask
{
    public $timeout_day = 1;

    /**
     * handleHold参数
     * @param $staff_info_id
     * @param $reason
     * @return array
     */
    public function getHoldDataFormat($staff_info_id, $reason): array
    {
        switch ($reason){
            case '6':
                $holdSource = '3';
                $holdReason = 'off_3_days';
                break;
            case '7':
                $holdSource = '4';
                $holdReason = 'fail_to_submit_public_funds';
                break;
            default:
                $holdSource = '';
                $holdReason = '';
                break;
        }
        $holdData = [];
        $holdData['staff_info_id'] = $staff_info_id;
        $holdData['hold_source'] = $holdSource;
        $holdData['hold_reason'] = $holdReason;
        $holdData['handle_hold'] = 1;
        $holdData['operator_id'] = -1;

        return $holdData;
    }
}