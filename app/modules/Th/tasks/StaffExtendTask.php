<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/6/17
 * Time: 下午6:23
 */

namespace FlashExpress\bi\App\Modules\Th\Tasks;


//员工扩展任务类
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoExtendMode;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffJobLevelLogModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveExtendModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Modules\Th\Server\LeaveServer;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\AnnualServer;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\SickServer;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use StaffExtendTask as GlobalTask;

class StaffExtendTask extends GlobalTask
{
    //每天发放 年假额度 任务
    public function freeze_by_levelAction($param)
    {
        //任务锁
        $key   = 'freeze_by_level_lock';
        $redis = $this->getDI()->get("redisLib");
        $rs    = $redis->set($key, 1, ['nx', 'ex' => 10 * 60]);//锁10分钟
        if (!$rs && RUNTIME == 'pro') {
            echo 'task is running';
            return ;
        }
        $today = date('Y-m-d');

        if (!empty($param[0])) {
            $today = $param[0];
        }

        if (!empty($param[1])) {
            $staffParam['staff_info_id'] = (int)$param[1];
        }

        try {

            $staffParam['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_1,HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3];
            $staffParam['state'] = [HrStaffInfoModel::STATE_1,HrStaffInfoModel::STATE_3];
            $staff_list = $this->annualStaffList($today, $staffParam);

            if (empty($staff_list)) {
                return true;
            }

            $staff_list = array_column($staff_list, null, 'staff_info_id');

            $dep_model = new DepartmentRepository($this->lang);
            $c_staffs  = $dep_model->get_c_level();

            //特殊情况 指定工号 21年 之后额度拉满
            $setting_model  = new BySettingRepository();
            $max_days_staff = $setting_model->get_setting('MAX_DAYS');
            $max_days_staff = explode(',', $max_days_staff);

            $annualServer = new AnnualServer($this->lang, $this->timezone);
            $leaveServer = new LeaveServer($this->lang, $this->timezone);
            foreach ($staff_list as $staff_id => $staff_info) {
                if (empty($staff_id)) {
                    continue;
                }
                $flag = $leaveServer->leavePermission($staff_info);
                if(!$flag){
                    echo $staff_info['staff_info_id'].' 非工作所在国家 不发年假';
                    continue;
                }

                //获取当前周期的额度信息
                $cycle_info = $annualServer->get_cycle($staff_info);
                if (empty($cycle_info)) {
                    echo $staff_info['staff_info_id'].'没有入职日期';
                    continue;
                }

                //新增 计算年假日期逻辑 https://flashexpress.feishu.cn/docx/USK1dOhAiod8STxnC0ccJqacn4d
                if(!empty($staff_info['annual_date'])){
                    //清空日期 不累计年假
                    if($staff_info['annual_date'] == HrStaffInfoExtendMode::ANNUAL_STOP_DATE){
                        echo 'base 清空日期 不计算年假 ' . "{$staff_info['staff_info_id']} ". $staff_info['annual_date'] ;
                        continue;
                    }
                    //日期在当天之后 不累计
                    if($staff_info['annual_date'] > $today){
                        echo 'base 没到计算日期 不计算年假 ' . "{$staff_info['staff_info_id']} ". $staff_info['annual_date'];
                        continue;
                    }
                }

                $this->getDI()->get('db')->begin();
                //下个周期
                $nextCycle = $cycle_info['cycle'] + 1;
                $current_remain = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year = :cycle: and leave_type = :leave_type:',
                    'bind'       => [
                        'staff_id'   => $staff_id,
                        'cycle'      => $cycle_info['cycle'],
                        'leave_type' => enums::LEAVE_TYPE_1,
                    ],
                ]);

                $isNewHire = false;//是否 新入职员工 true 新员工, false 非新员工
                //有可能是新入职员工
                if (empty($current_remain) || $current_remain->freeze_days == 0) {
                    $isContinue = true;//需要继续往下走的 用下面的add day 包括新入职和迁移账号
                    if ($cycle_info['cycle'] == 1 && empty($staff_info['annual_date'])) {
                        $isNewHire      = true;//由于 入职日期 不准确是昨天 所以用这个判断
                    } else {
                        $this->getDI()->get('logger')->write_log("freeze_by_level {$staff_info['staff_info_id']} 当前周期额度异常 没有当前周期额度信息 初始化1天额度", 'info');
                    }
                    $current_remain = $annualServer->initAnnual($staff_info,$cycle_info['cycle'],['task_date'=>$today]);
                }

                //任务判定是否跑过 每天只能跑一次
                if (!empty($current_remain) && $current_remain->task_date >= $today && empty($isContinue)) {
                    $this->getDI()->get('db')->commit();
                    continue;
                }

                //新需求 用历史最高职级 不用实时的
                $ext_info = StaffLeaveExtendModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year_at = :cycle:',
                    'bind'       => [
                        'staff_id'   => $staff_info['staff_info_id'],
                        'leave_type' => enums::LEAVE_TYPE_1,
                        'cycle'      => $cycle_info['cycle'],
                    ],
                ]);
                if (empty($ext_info)) {
                    $this->getDI()->get('db')->rollback();
                    $this->getDI()->get('logger')->write_log("freeze_by_level {$staff_info['staff_info_id']} 当前周期额度扩展表 异常");
                    continue;
                }

                //与当前职等比较 有可能刚升职
                if ($staff_info['job_title_grade_v2'] > $ext_info->job_title_grade) {
                    $ext_info->job_title_grade = $staff_info['job_title_grade_v2'];
                    $ext_info->update();
                }
                //降职情况 取历史最高 替换当前的 v2
                $staff_info['job_title_grade_v2'] = max($ext_info->job_title_grade, $staff_info['job_title_grade_v2']);

                if (in_array($staff_info['staff_info_id'], $c_staffs)) {
                    //c level 固定 20天
                    $should_day = enums::C_LEVEL_DAYS;
                } elseif (!empty($max_days_staff) && in_array($staff_id, $max_days_staff)) {
                    //特殊工号 额度拉满
                    $should_day = $annualServer->getMaxDays($staff_info);
                } else {//正常员工
                    $should_day = $this->getGradeDaysNormal($staff_info);
                }

                //没有自然年概念 都是 365
                $add_day = round($should_day / 365, enums::ROUND_NUM);
                if ($isNewHire) {//新入职员工 第一次递增额度 * 2 因为入职当天 任务没跑这个人
                    $add_day += $add_day;
                }

                $current_remain->freeze_days += $add_day;
                $current_remain->days        += $add_day;//加一天额度
                $cycle_last_date             = date('Y-m-d', strtotime("{$cycle_info['count_day']} -1 day"));
                if ($today == $cycle_last_date) {//当天是 当前周期 最后一天 加一天额度 然后初始化 下一年记录 并且当年额度 +1天
                    $remains = $this->getNewCycleRemains($staff_id);
                    if ($remains->count() == 1){
                        //非完整周期的不满0.5的结余要加到下个周期里；
                        $freezeDays = round($current_remain->freeze_days - half_num($current_remain->freeze_days),enums::ROUND_NUM);
                    }else{
                        $freezeDays = 0;
                    }
                    //特殊逻辑 在2022 最后一天之前 要把超额的 顺延到下个周期
                    if ($current_remain->days < 0) {
                        $changeDays = half_num($current_remain->days);//-1
                        $this->currentCycleFlag($staff_info, abs($changeDays), $cycle_info['cycle'], $nextCycle);
                        $current_remain->days = 0;
                        $current_remain->leave_days += $changeDays;
                        $ext_info->left_all_days = $changeDays;
                        $ext_info->update();
                    }else{
                        $changeDays = 0;
                    }
                    $annualServer->initAnnual($staff_info,$nextCycle,['freeze_days'=>$freezeDays,'days'=>$freezeDays+$changeDays,'leave_days'=>abs($changeDays),'task_date'=>$today]);
                    //c级别 和 19天的 由于5位小数点 四舍五入 舍去了 需要加一天额度
                    if(in_array($should_day, [19,20])){
                        $current_remain->days += $add_day;
                    }
                }

                $current_remain->task_date = $today;
                $current_remain->update();

                $this->getDI()->get('db')->commit();
                $this->logger->write_log("freeze_by_level_{$staff_info['staff_info_id']}_{$cycle_info['cycle']} {$today} 增加额度 {$add_day} ",'info');
            }

            $redis->delete($key);
            $this->logger->write_log("freeze_by_level {$today} 年假固化任务 跑完了 ",'notice');

        } catch (\Exception $e) {
            $this->logger->write_log("freeze_by_level {$today} 任务数据失败 ".$e->getTraceAsString());
            $this->getDI()->get('db')->rollback();
            die('freeze_by_level 任务异常 '.$e->getTraceAsString());
        }
    }

    //新版 初始化任务 https://flashexpress.feishu.cn/docx/doxcnbZWry1cpFm5UUbIrZ3z5Ge
    //按 历史最高职等 分段 发放22年1月到10-31 额度 把满周年额度 也放当分母 放到 remain 表
    public function freeze_firstAction($param)
    {
        ini_set('memory_limit', '-1');
        try {
            $staff_id = '';
            if (!empty($param[0])) {
                $staff_id = $param[0];
            }

            $today         = date('Y-m-d');
            $staff_list = $this->annualStaffList($today,$staff_id);

            if (empty($staff_list)) {
                die('没有员工数据');
            }


            //特殊情况 c level
            $dep_model = new DepartmentRepository($this->lang);
            $c_staffs  = $dep_model->get_c_level();

            //特殊情况 指定工号 21年 之后额度拉满
            $setting_model  = new BySettingRepository();
            $max_days_staff = $setting_model->get_setting('MAX_DAYS');
            $max_days_staff = explode(',', $max_days_staff);

            $annualServer = new AnnualServer($this->lang, $this->timezone);
            $remainModel  = new StaffLeaveRemainDaysModel();
            $extModel     = new StaffLeaveExtendModel();
            foreach ($staff_list as $staff_info) {
                $staff_id  = $staff_info['staff_info_id'];
                $cycleInfo = $annualServer->get_cycle($staff_info);

                //最高职级 ext 记录用 计算时候用当前的 目前业务还没定 是分段算还是 用当前 最后定了 用历史最高职等
                $staff_info['job_title_grade_v2'] = $this->getHighestGrade($staff_info);

                //开始计算 天数的时间 默认 22年 1月1号
                $startDate = '2022-01-01';
                if (strtotime($startDate) < strtotime($staff_info['hire_date'])) {
                    $startDate = $staff_info['hire_date'];
                }
                //分母应有额度
                if (in_array($staff_id, $c_staffs)) {
                    $should_days = enums::C_LEVEL_DAYS;
                } else {
                    if (!empty($max_days_staff) && in_array($staff_id, $max_days_staff)) {
                        $should_days = $annualServer->getMaxDays($staff_info);
                    } else {
                        $should_days = $this->getGradeDaysNormal($staff_info);
                    }
                }

                //应发放额度 2022
                $should_days = $this->countDays($should_days, $startDate);

                $daysFor2022 = half_num($should_days);
                //22年 计算完的 余数
                $daysForCycle = bcsub($should_days, $daysFor2022, enums::ROUND_NUM);

                //22年 使用年假天数
                $usedDaysFor2022 = $annualServer->leaveDaysByDate($staff_id, 2022);
                $leftFor2022     = bcsub($daysFor2022, $usedDaysFor2022, 1);
                //当前周期 最后一天
                $cycle_last_date = date('Y-m-d', strtotime("{$cycleInfo['count_day']} -1 day"));

                $this->getDI()->get('db')->begin();
                $usedForCycle = 0;
                if ($leftFor2022 < 0) {//小于0  说明 请超额了 泰国 预发放导致
                    $usedForCycle = $leftFor2022;//超额 负数要用 新逻辑 当前周期的 填平
                    //如果 正好最后一天了 直接 挪到下周期 判断周期是当前还是下周期
                    $cycle = ($today == $cycle_last_date) ? ($cycleInfo['cycle'] + 1) : $cycleInfo['cycle'];
                    //2022 超额的 找出来 标记成档期当前周期
                    $this->currentCycleFlag($staff_info, abs($leftFor2022), 2022, $cycle);
                }

                //初始化 22年 remain
                $row['staff_info_id'] = $staff_id;
                $row['leave_type']    = enums::LEAVE_TYPE_1;
                $row['year']          = 2022;
                $row['task_date']     = $today;
                $row['freeze_days']   = $should_days;
                $row['days']          = $leftFor2022 < 0 ? 0 : $leftFor2022;//剩余额度
                $row['leave_days']    = $leftFor2022 < 0 ? $daysFor2022 : $usedDaysFor2022;//使用额度

                $cloneOld = clone $remainModel;
                $cloneOld->create($row);

                //初始化 22年的 ext
                $ext['staff_info_id']   = $staff_id;
                $ext['leave_type']      = enums::LEAVE_TYPE_1;
                $ext['year_at']         = 2022;
                $ext['left_all_days']   = $leftFor2022;//22年 剩余额度
                $ext['job_title_level'] = $staff_info['job_title_level'];
                $ext['job_title_grade'] = $staff_info['job_title_grade_v2'];
                //入库 ext表
                $extendOld = clone $extModel;
                $extendOld->create($ext);


                //特殊情况 初始化 当天正好是 当前周期最后一天 要初始化下个周期数据 并且把 超额的额度 放到下周起继续 持平
                if ($today == $cycle_last_date) {
                    //下个周期 remain
                    $remain['staff_info_id'] = $staff_id;
                    $remain['leave_type']    = enums::LEAVE_TYPE_1;
                    $remain['year']          = $cycleInfo['cycle'] + 1;
                    $remain['task_date']     = $today;
                    $remain['freeze_days']   = 0;
                    $remain['days']          = $usedForCycle;//22年超额的 -》到上周期 -》 到这周期
                    $remain['leave_days']    = abs($usedForCycle);

                    //下个周期的 ext
                    $extRow['staff_info_id']   = $staff_id;
                    $extRow['leave_type']      = enums::LEAVE_TYPE_1;
                    $extRow['year_at']         = $cycleInfo['cycle'] + 1;
                    $extRow['left_all_days']   = $usedForCycle;//记录上周期剩余 有可能是负数 以后不会变
                    $extRow['job_title_level'] = $staff_info['job_title_level'];
                    $extRow['job_title_grade'] = $staff_info['job_title_grade_v2'];
                } else {//初始化当天 不是 最后一天 正常用当前周期 初始化
                    //初始化一条 当前周期的 额度 把小数 放进去
                    $remain['staff_info_id'] = $staff_id;
                    $remain['leave_type']    = enums::LEAVE_TYPE_1;
                    $remain['year']          = $cycleInfo['cycle'];
                    $remain['task_date']     = $today;
                    $remain['freeze_days']   = $daysForCycle;
                    $remain['days']          = bcadd($daysForCycle, $usedForCycle, enums::ROUND_NUM);//加上超额的 持平
                    $remain['leave_days']    = abs($usedForCycle);//超额使用22年的的天数 放到 当前周期 没超额 就是0

                    //初始化 extend
                    $extRow['staff_info_id']   = $staff_id;
                    $extRow['leave_type']      = enums::LEAVE_TYPE_1;
                    $extRow['year_at']         = $cycleInfo['cycle'];
                    $extRow['left_all_days']   = $leftFor2022;
                    $extRow['job_title_level'] = $staff_info['job_title_level'];
                    $extRow['job_title_grade'] = $staff_info['job_title_grade_v2'];
                }
                //入库 remain
                $cloneNew = clone $remainModel;
                $cloneNew->create($remain);
                //入库 ext表
                $extendClone = clone $extModel;
                $extendClone->create($extRow);
                $this->getDI()->get('db')->commit();
            }

        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            die('初始化额度报错 '.$e->getMessage().'-------'.$e->getTraceAsString());
        }
    }
    /**
     * @param $params
     * @return void
     */
    public function freeze_secondAction($params)
    {
        ini_set('memory_limit', '-1');
        $staff_id = '';
        if (!empty($params[0])) {
            $staff_id = $params[0];
        }

        $today         = date('Y-m-d');
        $staff_list = $this->annualStaffList($today,$staff_id);

        if (empty($staff_list)) {
            die('没有员工数据');
        }
        foreach ($staff_list as $staff){
            if ( date('md',strtotime($staff['hire_date'])) == '1101'){
                $remains = StaffLeaveRemainDaysModel::find(
                    [
                        'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type:',
                        'bind'       => [
                            'staff_id'   => $staff['staff_info_id'],
                            'leave_type' => enums::LEAVE_TYPE_1,
                        ],
                        'order' => 'year asc',
                    ]
                );
                $first = $remains->getFirst();
                $second = $remains->getLast();
                $days = round($second->freeze_days - half_num($second->freeze_days), enums::ROUND_NUM);
                $first->freeze_days += $days;
                $first->days        += $days;
                $this->echo("{$staff['staff_info_id']},周期{$first->year}需要调整{$days}");
                $first->update();
            }else{
                $remains = $this->getNewCycleRemains($staff['staff_info_id']);
                if ($remains->count() == 2){
                    $first = $remains->getFirst();
                    $days = round($first->freeze_days - half_num($first->freeze_days),enums::ROUND_NUM);
                    $second = $remains->getLast();
                    $second->freeze_days += $days;
                    $second->days        += $days;
                    $this->echo("{$staff['staff_info_id']},周期{$second->year}需要调整{$days}");
                    $second->update();
                }
            }
        }
    }



    /**
     * @param $staff_id
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getNewCycleRemains($staff_id)
    {
        return StaffLeaveRemainDaysModel::find(
            [
                'conditions' => 'staff_info_id = :staff_id: and year < 2022 and leave_type = :leave_type:',
                'bind'       => [
                    'staff_id'   => $staff_id,
                    'leave_type' => enums::LEAVE_TYPE_1,
                ],
                'order' => 'year asc',
            ]
        );
    }

    //把2022年 或者 22年挪到当前周期的 超额使用的天数标记为 指定周期
    public function currentCycleFlag($staff_info, $days, $searchCycle, $cycle)
    {
        //22年 的记录 split
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.*');
        $builder->from(['s' => StaffAuditLeaveSplitModel::class]);
        $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
        $builder->andWhere("s.staff_info_id = :staff_id:", ['staff_id' => $staff_info['staff_info_id']]);
        $builder->andWhere('a.leave_type = :leave_type:', ['leave_type' => enums::LEAVE_TYPE_1]);
        $builder->andWhere('s.year_at = :year:', ['year' => $searchCycle]);
        $builder->inWhere('a.status', [1, 2]);
        $builder->orderBy('s.date_at desc');
        $splitInfo = $builder->getQuery()->execute();

        if (empty($splitInfo->toArray())) {
            return;
        }
        $left  = $days;
        $model = new StaffAuditLeaveSplitModel();
        foreach ($splitInfo as $item) {
            if ($left <= 0) {
                break;
            }
            $num = ($item->type == 0) ? 1 : 0.5;
            //拆分一半一半
            if ($left > 0 && $left - $num < 0) {
                //下午 变当前周期
                $item->type    = 2;
                $item->year_at = $cycle;
                $item->update();

                //增加一条 上午 2022的记录
                $insert['staff_info_id'] = $staff_info['staff_info_id'];
                $insert['audit_id']      = $item->audit_id;
                $insert['date_at']       = $item->date_at;
                $insert['type']          = 1;
                $insert['year_at']       = $searchCycle;
                $clone                   = clone $model;
                $clone->create($insert);
                break;
            }

            $item->year_at = $cycle;
            $item->update();
            $left -= $num;
        }
    }



    /**
     * 获取员工 应有的总额度 分母
     * @param $staff_info
     * @return array|int|mixed
     */
    public function getGradeDaysNormal($staff_info)
    {
        if (empty($staff_info)) {
            return 0;
        }

        $annualServer = new AnnualServer($this->lang, $this->timezone);
        $shouldDays   = $annualServer->getShouldDays($staff_info);//职等额度
        $yearAdd      = $annualServer->overOneYear($staff_info);//满周年额度

        $maxDay = $annualServer->getMaxDays($staff_info);//最高额度 不能超过多少天
        return ($shouldDays + $yearAdd) > $maxDay ? $maxDay : ($shouldDays + $yearAdd);
    }

    //计算 天数 对应额度是多少
    public function countDays($shouldDays, $startDate, $endDate = '')
    {
        $step = round($shouldDays / 365, enums::ROUND_NUM);

        //计算天数
        if (empty($endDate)) {
            $endDate = date('Y-m-d');
        }

        $countDays = (strtotime($endDate) - strtotime($startDate)) / (24 * 3600);
        $countDays = abs($countDays) + 1;//算上结束日期那一天
        return $step * $countDays;
    }


    //旧版本的 获取 最高职等 用staff_job_level_log
    public function getHighestGrade($staff_info)
    {
        $logGrade = StaffJobLevelLogModel::findFirst([
            'columns'    => 'max(job_title_grade) as job_title_grade',
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $staff_info['staff_info_id']],
        ]);

        if (empty($logGrade)) {
            return $staff_info['job_title_grade_v2'];
        }
        return $logGrade->job_title_grade > $staff_info['job_title_grade_v2'] ? $logGrade->job_title_grade : $staff_info['job_title_grade_v2'];
    }


    //初始化 22年 23年 泰国 病假额度 到remain 不考虑新冠假 获取额度时候计算
    public function initSickAction($param){
        $year = date('Y');
        if(!empty($param[0])){
            $year = intval($param[0]);
        }
        //离职的人也要跑 只要 2022-01-01 之后离职的人
        $leaveDate = '2022-01-01';

        //取 正式 和实习生
        $condition = " formal in (1,4) and is_sub_staff = 0 and (state in (1,3) or (state = 2 and leave_date >= :leave_date:))";

        $staff_list = HrStaffInfoModel::find(
            [
                'conditions' => $condition,
                'columns'    => 'staff_info_id,formal',
                'bind' => ['leave_date' => $leaveDate]
            ]
        )->toArray();

        if(empty($staff_list)){
            die('没有员工数据 不需要初始化病假额度');
        }
        $sickServer = new SickServer($this->lang,$this->timezone);
        foreach ($staff_list as $staff){
            $remainInfo      = StaffLeaveRemainDaysModel::findFirst([
                'columns'    => 'staff_info_id,leave_type,days,leave_days,year,freeze_days',
                'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
                'bind'       => [
                    'staff_id'   => $staff['staff_info_id'],
                    'leave_type' => enums::LEAVE_TYPE_3,
                    'year_at'    => $year,
                ],
            ]);

            //当年
            if(empty($remainInfo)){
                $sickServer->initSickDays($year,$staff);
            }
        }

        echo '跑完了';
    }



    /**
     *
    select * from `staff_leave_remaining_days` where `leave_type`  = 1 and `staff_info_id`  in (
    select r.`staff_info_id`
    from `staff_leave_remaining_days` r
    join `hr_staff_info` s on r.`staff_info_id`  = s.`staff_info_id`
    where r.`leave_type`  = 1 and r.`year`  = 2022 and r.days < 0  and s.`node_department_id`  in (
    select id from `sys_department` where (ancestry_v3 like '999/222/50001/%' or ancestry_v3 like '999/222/70001/%' or id in (50001,70001))  and deleted = 0
    )
    )

     */
    public function fixFhrAction(){
        //22年 额度 负数的人 排除超过2个周期的人
        $data = StaffLeaveRemainDaysModel::find([
            'conditions' => 'leave_type  = 1 and year  = 2022 and days < 0 and staff_info_id not in (23776,61376,605535,606296,606310,607095)',
        ]);

        foreach ($data as $da){
            $moveDays = $da->days;
            $staffInfo['staff_info_id'] = $da->staff_info_id;
            $this->currentCycleFlag($staffInfo,abs($moveDays),2022,1);

            //负数 放到当当前周期来
            $remainInfo = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and leave_type = 1 and year = 1',
                'bind' => ['staff_id' => $da->staff_info_id]

            ]);

            if(empty($remainInfo)){
                continue;
            }
            $remainInfo->days = $remainInfo->days + $moveDays;
            $remainInfo->leave_days = $remainInfo->leave_days + abs(half_num($moveDays));
            $remainInfo->update();
            $this->getDI()->get('logger')->write_log("fixFhrAction {$da->staff_info_id} 挪动 {$moveDays} 到第一周期", 'info');
        }

        echo '跑完了';
    }


}