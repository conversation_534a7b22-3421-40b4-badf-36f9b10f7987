<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Models\oa\MaterialPackageCategoryModel;
use FlashExpress\bi\App\Enums\InventoryCheckEnums;
use FlashExpress\bi\App\Models\oa\MaterialWmsPlantaskModel;
use FlashExpress\bi\App\Models\oa\MaterialWmsPlantaskInfoModel;
use FlashExpress\bi\App\Models\oa\MaterialPackageSkuModel;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\WmsPlanServer AS BaseWmsPlanServer;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;


class WmsPlanServer extends BaseWmsPlanServer
{
    public $timezone;

    /**
     * WmsPlanServer constructor.
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang, $timezone);
    }


    /**
     * 获取计划列表
     * @Date: 2022-05-30 22:58
     * @return:
     **@author: peak pan
     */
    public function getMyplanList($params)
    {
        $page_size = $params['page_size'];
        $page_offset = $page_size * ($params['page_num'] - 1);
        $data = [
            'items' => [],
            'pagination' => [
                'page_num' => intval($page_offset) + 1,
                'page_size' => intval($page_size),
                'wait_count' => 0,
                'finish_count' => 0,
            ],
        ];

        $items = [];
        //获取单项资产的数量
        $count = $this->getMyPlanWmsCount($params);
        $rs_count = $this->getMyPlanWmsCount($params, 1);
        if ($count > 0) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'main.id',
                'main.plan_name',
                'main.is_relay',
                'main.status as plan_status',
                'main.store_id',
                'main.store_name',
                'main.plan_manager_id as manager_id',
                'main.plan_manager_name as manager_name',
                'main.department_name',
                'main.sta_time as plan_sta_time',
                'main.end_time as plan_end_time',
                'main.send_time',
                'main.department_id'
            ]);
            $builder->from(['main' => MaterialWmsPlantaskModel::class]);
            if ($params['type'] == 1) {
                $builder->where('main.plan_manager_id = :staff_info_id: and main.is_deleted = :is_deleted: and main.end_time >= :end_time: and  status not in({status:array}) and main.is_task = :is_task:', ['staff_info_id' => $params['staff_id'], 'is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'end_time' => date('Y-m-d H:i:s', time()), 'status' => [InventoryCheckEnums::PLAN_NOUN_STATUS_REVOKE, InventoryCheckEnums::PLAN_NOUN_STATUS_END], 'is_task' => InventoryCheckEnums::PLAN_IS_TASK_YES]);
            } else {
                $builder->where('main.plan_manager_id = :staff_info_id: and main.is_deleted = :is_deleted: and (main.end_time < :end_time: or  status in({status:array}))', ['staff_info_id' => $params['staff_id'], 'is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'end_time' => date('Y-m-d H:i:s', time()), 'status' => [InventoryCheckEnums::PLAN_NOUN_STATUS_REVOKE, InventoryCheckEnums::PLAN_NOUN_STATUS_END]]);
            }
            //修改为由前端控制计算offset值
            $builder->limit($page_size, $page_offset);
            $builder->orderby('main.id');
            $items = $builder->getQuery()->execute()->toArray();
            foreach ($items as &$item) {
                $is_relay_show = 0;
                $is_plan_show = 0;
                if ($item['plan_status'] == InventoryCheckEnums::PLAN_NOUN_STATUS_NOT_STARTED && $item['is_relay'] == 1 && strtotime($item['plan_end_time']) > time()) {
                    $is_relay_show = 1;
                }
                if (in_array($item['plan_status'], [InventoryCheckEnums::PLAN_NOUN_STATUS_NOT_STARTED, InventoryCheckEnums::PLAN_NOUN_STATUS_ING]) && strtotime($item['plan_end_time']) > time() && strtotime($item['plan_sta_time']) < time()) {
                    $is_plan_show = 1;
                }

                if (in_array($item['plan_status'], [InventoryCheckEnums::PLAN_NOUN_STATUS_NOT_STARTED, InventoryCheckEnums::PLAN_NOUN_STATUS_ING]) && strtotime($item['plan_end_time']) < time()) {
                    $item['plan_status'] = InventoryCheckEnums::PLAN_NOUN_STATUS_END;
                }

                $item['is_relay_show'] = $is_relay_show;//0不显示   1显示
                $item['is_plan_show'] = $is_plan_show;//0不显示   1显示 开始盘库
                $item['plan_status_name'] = $this->getTranslation()->_(InventoryCheckEnums::$wms_plan_status[$item['plan_status']]);
            }

        }
        $data['items'] = $items ?? [];
        $data['pagination']['wait_count'] = $params['type'] == 1 ? $count : $rs_count;
        $data['pagination']['finish_count'] = $params['type'] == 2 ? $count : $rs_count;
        return $data;

    }

    /**
     * 获取盘点任务点的列表
     * @Date: 2022-05-30 22:58
     * @return:
     **@author: peak pan
     */
    public function getMyplanInfoList($params)
    {
        $lang =  getLangToDefault(substr(strtolower($this->lang), 0, 2));
        $page_size = $params['page_size'];
        $page_offset = $page_size * ($params['page_num'] - 1);
        $data = [
            'items' => [],
            'pagination' => [
                'page_num' => intval($page_offset) + 1,
                'page_size' => intval($page_size),
                'wait_count' => 0,
                'finish_count' => 0,
            ],
        ];
        $items = [];
        //获取单项资产的数量
        $count = $this->getMyPlanWmsinfoCount($params);
        $rs_count = $this->getMyPlanWmsinfoCount($params, 1);
        $notwhere_count = $this->getNotWherePlanWmsinfoCount($params);
        $taskData = $this->getMyPlanWmsOne($params);

        if ($count > 0) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'main.id',
                'main.plan_name',
                'main.plan_manager_id',
                'main.store_name',
                'main.plan_end_time',
                'main.goods_name_' . $lang . ' as goods_name',
                'main.specs_model',
                'main.unit',
                'main.plan_nub',
                'main.image_path',
                'main.plan_image_path',
                'main.plan_task_id',
                'plan_status',
            ]);
            $builder->from(['main' => MaterialWmsPlantaskInfoModel::class]);

            if ($params['type'] == 1) {
                $builder->where(' main.is_deleted = :is_deleted: and main.plan_end_time >= :plan_end_time: and  plan_status != :plan_status: ', ['is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'plan_end_time' => date('Y-m-d H:i:s', time()), 'plan_status' => InventoryCheckEnums::PLAN_NOUN_STATUS_END]);
            } else {
                $builder->where(' main.is_deleted = :is_deleted: and (main.plan_end_time < :plan_end_time: or plan_status in ({plan_status:array}))', ['is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'plan_end_time' => date('Y-m-d H:i:s', time()), 'plan_status' => [3, 4]]);
            }

            if (!empty($params['category'])) {
                $builder->andWhere('main.category = :category:', ['category' => $params['category']]);

            }

            if (isset($params['goods_name']) && $params['goods_name'] != '') {
                $builder->andWhere('main.goods_name_' . $lang . ' like :goods_name:', ['goods_name' => '%' . $params['goods_name'] . '%']);
            }

            if (isset($params['specs_model']) && $params['specs_model'] != '') {
                $builder->andWhere('main.specs_model like :specs_model:', ['specs_model' => '%' . $params['specs_model'] . '%']);
            }

            if (!empty($params['id'])) {
                $builder->andWhere('main.plan_task_id = :plan_task_id:', ['plan_task_id' => $params['id']]);
            }

            //修改为由前端控制计算offset值
            $builder->limit($page_size, $page_offset);
            $builder->orderby('main.id');
            $items = $builder->getQuery()->execute()->toArray();

            if ($items) {
                $wmsPlantask = (new MaterialWmsPlantaskModel())->findFirst(
                    [
                        'conditions' => "id = :id:",
                        'bind' => ['id' => $params['id']],
                    ]
                );
                if ($wmsPlantask->id && $wmsPlantask->status == InventoryCheckEnums::PLAN_NOUN_STATUS_NOT_STARTED && strtotime($wmsPlantask->end_time) > time()) {
                    $wmsPlantask->status = InventoryCheckEnums::PLAN_NOUN_STATUS_ING;
                    $wmsPlantask->is_relay = InventoryCheckEnums::IS_DELETED_NO;
                    $wmsPlantask->updated_at = date('Y-m-d H:i:s');
                    $wmsPlantask->update();
                    $db = $this->getDI()->get('db_oa');
                    $material_wms = $db->updateAsDict("material_wms_plantask_info", [
                        "reality_sta_time" => date('Y-m-d H:i:s')
                    ], [
                        "conditions" => "plan_task_id = ? ",
                        "bind" => [
                            $params['id']
                        ]
                    ]);
                }
            }
            foreach ($items as &$item) {
                $is_submits = 1;
                $item['plan_nub'] = $item['plan_nub'] === null ? '' : $item['plan_nub'];
                $item['is_photo'] = $wmsPlantask->is_photo;
                $item['is_photo_camera'] = $wmsPlantask->is_photo_camera;
                if ($params['type'] == 1) {
                    if ($item['plan_status'] == InventoryCheckEnums::PLAN_NOUN_STATUS_END || strtotime($item['plan_end_time']) < time()) {
                        $is_submits = 0;
                    }
                } else {
                    if (strtotime($item['plan_end_time']) < time()) {
                        $is_submits = 0;
                    }
                }
                $item['is_submits'] = $is_submits;
            }
        }
        $data['items'] = $items ?? [];
        $data['pagination']['wait_count'] = $params['type'] == 1 ? $count : $rs_count;
        $data['pagination']['finish_count'] = $params['type'] == 2 ? $count : $rs_count;
        $data['pagination']['plan_name'] = empty($taskData['plan_name']) ? '' : $taskData['plan_name'];
        $data['pagination']['plan_manager_id'] = !empty($taskData['plan_manager_id']) ? $taskData['plan_manager_id'] : '';
        $data['pagination']['plan_manager_name'] = !empty($taskData['plan_manager_name']) ? $taskData['plan_manager_name'] : '';
        $data['pagination']['department_name'] = empty($taskData['department_name']) ? '' : $taskData['department_name'];
        $data['pagination']['store_name'] = empty($taskData['store_name']) ? '' : $taskData['store_name'];
        $data['pagination']['plan_end_time'] = empty($taskData['end_time']) ? '' : $taskData['end_time'];

        $is_all_submits = 1;
        if (in_array($taskData['status'], [3, 4]) || $data['pagination']['finish_count'] != $notwhere_count) {
            $is_all_submits = 0;
        }
        $data['pagination']['is_all_submits'] = $is_all_submits;

        return $data;
    }

    /**
     * 获取盘点任务点的详情数据
     * @Date: 2022-05-30 22:57
     * @return:
     **@author: peak pan
     */
    public function getMyplanInfoDetail($params)
    {
        $lang =  getLangToDefault(substr(strtolower($this->lang), 0, 2));
        $page_size = $params['page_size'];
        $page_offset = $page_size * ($params['page_num'] - 1);
        $data = [
            'items' => [],
            'pagination' => [
                'page_num' => intval($page_offset) + 1,
                'page_size' => intval($page_size),
                'wait_count' => 0,
                'finish_count' => 0,
            ],
        ];

        $items = [];
        //获取单项资产的数量
        $count = $this->getMyPlanWmsinfoDetailCount($params);
        $rs_count = $this->getMyPlanWmsinfoDetailCount($params, 1);
        $taskData = $this->getMyPlanWmsOne($params);

        if ($count > 0) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'main.id',
                'main.plan_name',
                'main.plan_manager_id',
                'main.store_name',
                'main.plan_end_time',
                'main.goods_name_' . $lang . ' as goods_name',
                'main.specs_model',
                'main.unit',
                'main.plan_nub',
                'main.image_path',
                'main.plan_image_path',
                'main.plan_task_id',
                'plan_status',
            ]);
            $builder->from(['main' => MaterialWmsPlantaskInfoModel::class]);

            $builder->where(' main.is_deleted = :is_deleted: and main.plan_end_time < :plan_end_time:', ['is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'plan_end_time' => date('Y-m-d H:i:s', time())]);

            if (!empty($params['id'])) {
                $builder->andWhere('main.plan_task_id = :plan_task_id:', ['plan_task_id' => $params['id']]);
            }

            //修改为由前端控制计算offset值
            $builder->limit($page_size, $page_offset);
            $builder->orderby('main.id');
            $items = $builder->getQuery()->execute()->toArray();
        }
        $data['items'] = $items ?? [];
        $data['pagination']['wait_count'] = $params['type'] == 1 ? $count : $rs_count;
        $data['pagination']['finish_count'] = $params['type'] == 2 ? $count : $rs_count;
        $data['pagination']['plan_name'] = empty($taskData['plan_name']) ? '' : $taskData['plan_name'];
        $data['pagination']['plan_manager_id'] = !empty($taskData['plan_manager_id']) ? $taskData['plan_manager_name'] . '(' . $taskData['plan_manager_id'] . ')' : '';
        $data['pagination']['plan_manager_name'] = !empty($taskData['plan_manager_name']) ? $taskData['plan_manager_name'] : '';
        $data['pagination']['store_name'] = empty($taskData['store_name']) ? '' : $taskData['store_name'];
        $data['pagination']['plan_end_time'] = empty($taskData['plan_end_time']) ? '' : $taskData['plan_end_time'];

        return $data;

    }

    /**
     * 筛选分类和数据
     * @Date: 2022-05-16 09:42
     * @return:
     **@author: peak pan
     */
    public function getPlanSkuEnums()
    {
        $code = 1;
        $message = $real_message = '';
        $list = (new MaterialPackageSkuModel())->find([
            'conditions' => ' status = :status:',
            'bind' => [
                'status' => InventoryCheckEnums::PLAN_MATERIAL_PACKAGE_SKU_STATUS,
            ]
        ]);
        if (!empty($list)) {
            $sku_arr = $list->toArray();
            $barcode_arr = [];
            $goods_name_zh = [];
            $goods_name_th = [];
            $goods_name_en = [];
            $specs_model_arr = [];
            foreach ($sku_arr as $value) {
                $barcode['id'] = $value['id'];
                $barcode['label'] = $value['barcode'];
                $barcode_arr[] = $barcode;
                $name_zh['id'] = $value['id'];
                $name_zh['label'] = $value['goods_name_zh'];
                $goods_name_zh[] = $name_zh;
                $name_th['id'] = $value['id'];
                $name_th['label'] = $value['goods_name_th'];
                $goods_name_th[] = $name_th;
                $name_en['id'] = $value['id'];
                $name_en['label'] = $value['goods_name_en'];
                $goods_name_en[] = $name_en;
                $model_arr['id'] = $value['id'];
                $model_arr['label'] = $value['specs_model'];
                $specs_model_arr[] = $model_arr;
            }
            $data['goods_name_arr'] = [
                'goods_name_zh' => $goods_name_zh,
                'goods_name_th' => $goods_name_th,
                'goods_name_en' => $goods_name_en
            ];
            $data['specs_model_arr'] = $specs_model_arr;

        } else {
            $data = [];
        }
        $lang =  getLangToDefault(substr(strtolower($this->lang), 0, 2));
        $title = 'goods_name_' . $lang;
        $packageCategoryArr = (new MaterialPackageCategoryModel())->find([
            'conditions' => ' status = :status: and is_deleted=:is_deleted:',
            'bind' => [
                'status' => InventoryCheckEnums::PLAN_MATERIAL_PACKAGE_SKU_STATUS,
                'is_deleted' => InventoryCheckEnums::IS_DELETED_NO,
            ],
            'columns' => 'id,' . $title,
        ])->toArray();
        $st = [];
        foreach ($packageCategoryArr as $value_) {
            $rs['id'] = $value_['id'];
            $rs['label'] = $value_[$title];
            $st[] = $rs;
        }
        $data['spackage_ategory_arr'] = $st;
        return [
            'code' => $code,
            'message' => $code == InventoryCheckEnums::SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 转单确定
     * @Date: 2022-05-16 09:57
     * @author: peak pan
     **/
    public function relaySave($params)
    {

        $code = 1;
        $message = $real_message = '';
        $this->saveNotTaskChek(['plan_task_id' => $params['plan_id']]);

        try {
            $db = MaterialWmsPlantaskInfoModel::beginTransaction($this);
            $plantaskOne = (new MaterialWmsPlantaskModel())->findFirst([
                'conditions' => ' id = :id:',
                'bind' => [
                    'id' => $params['plan_id']
                ]
            ]);
            if(empty($plantaskOne)){
                $messages = 'plan-task-info-relaySave' . ' Not plan task id:' . $params['plan_id'];
                throw new \Exception($message);
            }
            //开始盘点
            $unionStatus = $db->updateAsDict("material_wms_plantask_info", [
                "is_relay" => 0, "plan_manager_id" => $params['staff_id'], "plan_relay_manager_id" => $plantaskOne->plan_manager_id, "updated_at" => date('Y-m-d H:i:s')
            ], [
                "conditions" => " plan_task_id = ? ",
                "bind" => [
                    $params['plan_id']
                ]
            ]);
            if (!$unionStatus) {
                $messages = 'plan-task-info-relaySave' . '失败 转派人:' . $plantaskOne->plan_manager_id . '/盘点任务id:' . $params['plan_id'];
                throw new \Exception($message);
            }

            //
            $hrStaffInfo = HrStaffInfoModel::findFirst([
                'conditions' => ' staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $params['staff_id']
                ]
            ]);
            if (!empty($hrStaffInfo)) {
                $hrStaff = $hrStaffInfo->toArray();
            }
            if ($plantaskOne) {
                $plantaskOne->plan_manager_id = $params['staff_id'];
                $plantaskOne->plan_manager_name = $hrStaff['name'] ?? '-';
                $plantaskOne->is_relay = 0;
                $plantaskOne->updated_at = date('Y-m-d H:i:s');
                $insert_flag = $plantaskOne->save();
                if (!$insert_flag) {
                    $db->rollBack();
                    throw new \Exception('plan-task-relaySave error');
                }
            }
            $db->commit();
            return [
                'code' => $code,
                'message' => $code == InventoryCheckEnums::SUCCESS ? 'success' : $message,
                'data' => true,
            ];
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('plan-task-relaySave' . $e->getMessage());
            throw $e;
        }
    }


    /**
     * 盘点是否能操作任务数据
     * @Date: 2022-05-26 12:48
     * @return:
     **@author: peak pan
     */
    public function saveNotTaskChek($params)
    {
        $code = 1;
        $message = $real_message = '';
        $t = $this->getTranslation();
        $plantaskOne = (new MaterialWmsPlantaskModel())->findFirst([
            'conditions' => ' id = :id:',
            'bind' => [
                'id' => $params['plan_task_id']
            ]
        ]);

        if (empty($plantaskOne)) {
            $code = 0;
            $message = $real_message = 'No counting task found';
        } else {
            if (empty($plantaskOne->id) || $plantaskOne->is_relay == 0) {
                $code = 0;
                $message = $real_message = $t->_('material_wms_plantask_not_relay_where');
            } elseif ($plantaskOne->status == 3) {
                $code = 0;
                $message = $real_message = $t->_('material_wms_plantask_status_where');
            } elseif ($plantaskOne->status == 4) {
                $code = 0;
                $message = $real_message = $t->_('material_wms_plantask_over_where');
            } elseif (strtotime($plantaskOne->end_time) < time()) {
                $code = 0;
                $message = $real_message = $t->_('material_wms_plantask_end_time_where');
            }
        }

        if (empty($code)) {
            return [
                'code' => 0,
                'message' => $message,
                'data' => [],
            ];

        }

    }


    /**
     * 单个盘点数据确定
     * @Date: 2022-05-16 09:57
     * @author: peak pan
     **/
    public function staPlanSubmits($params)
    {
        $code = 1;
        $message = $real_message = '';
        $db = MaterialWmsPlantaskInfoModel::beginTransaction($this);
        try {
        $planWmsObj = (new MaterialWmsPlantaskInfoModel())->findFirst(
            [
                'conditions' => ' id = :id:',
                'bind' => [
                    'id' => $params['id']
                ]
            ]);

            if(empty($planWmsObj)){
                throw new \Exception('plan-wms-create cannot be empty');
            }
            $this->saveNotTaskChek($planWmsObj->toArray());
            //盘点的资产ID
            if ($planWmsObj) {
                $planWmsObj->reference_stock_number = $this->getReferenceStockNumber($planWmsObj);
                $planWmsObj->plan_nub = (int)$params['plan_nub'];
                $planWmsObj->plan_image_path = $params['plan_image_path'];
                $planWmsObj->plan_sku_end_time = date('Y-m-d H:i:s');
                $planWmsObj->plan_status = InventoryCheckEnums::PLAN_NOUN_STATUS_END;
                $planWmsObj->updated_at = date('Y-m-d H:i:s');
                $insert_flag = $planWmsObj->save();
                if (!$insert_flag) {
                    throw new \Exception('plan-wms-create error');
                }
            }
            $db->commit();
            return [
                'code' => $code,
                'message' => $code == InventoryCheckEnums::SUCCESS ? 'success' : $message,
                'data' => true,
            ];
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('plan-wms-staPlanSubmits' . $e->getMessage());
            throw $e;
        }
    }


    /**
     * 查询任务详情
     * @Date: 2022-05-21 11:27
     * @return:
     **@author: peak pan
     */

    public function getplanWmsDetail($params)
    {
        $code = 1;
        $message = $real_message = '';
        $plantaskInfo = MaterialWmsPlantaskModel::findFirst([
            'conditions' => 'id = :plan_id: and plan_manager_id=:plan_manager_id: ',
            'bind' => [
                'plan_id' => $params['plan_id'],
                'plan_manager_id' => $params['staff_id'],

            ]
        ]);

        if (empty($plantaskInfo)) {
            $code = 0;
            $message = $real_message = $this->getTranslation()->_('plan_wms_not_task_save');
        }

        /* if ( !empty($plantaskInfo) && strtotime($plantaskInfo->end_time) < time()) {
             $code = 0;
             $message = $real_message = $this->getTranslation()->_('plan_wms_time_not_sta_no_save');
         }
         if (!empty($plantaskInfo) &&  $plantaskInfo->status == 3) {
             $code = 0;
             $message = $real_message = $this->getTranslation()->_('plan_wms_revoke_not_save');
         }*/

        $data = [];
        if (!empty($plantaskInfo) && $code == 1) {
            $plantaskInfo_arr = $plantaskInfo->toArray();

            $planInfoCount = [];
            $plantaskInfoCount = MaterialWmsPlantaskInfoModel::find([
                'conditions' => 'plan_task_id = :plan_task_id: and plan_status != :plan_status: and plan_end_time > :plan_end_time:',
                'bind' => [
                    'plan_task_id' => $params['plan_id'],
                    'plan_status' => InventoryCheckEnums::PLAN_NOUN_STATUS_END,
                    'plan_end_time' => date('Y-m-d H:i:s')
                ]
            ]);

            if (!empty($plantaskInfoCount)) {
                $planInfoCount = $plantaskInfoCount->toArray();
            }
            $data['plan_name'] = $plantaskInfo_arr['plan_name'];
            $data['send_time'] = $plantaskInfo_arr['send_time'];
            $data['plan_remark'] = $plantaskInfo_arr['plan_remark'];
            $data['send_time'] = $plantaskInfo_arr['send_time'];
            $data['plan_count'] = count($planInfoCount) ?? 0;
            $data['plan_sta_time'] = $plantaskInfo_arr['sta_time'] ?? '';
            $data['plan_end_time'] = $plantaskInfo_arr['end_time'] ?? '';
            $data['id'] = $plantaskInfo_arr['id'] ?? '';
        }

        return [
            'code' => $code,
            'message' => $code == InventoryCheckEnums::SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }


    /**
     * 开始盘点 校验接口
     * @Date: 2022-06-06 16:24
     * @return:
     **@author: peak pan
     */
    public function getplanWmsStaChek($params)
    {
        $code = 1;
        $message = $real_message = '';
        $plantaskInfo = MaterialWmsPlantaskModel::findFirst([
            'conditions' => 'id = :plan_id: and plan_manager_id=:plan_manager_id: ',
            'bind' => [
                'plan_id' => $params['plan_id'],
                'plan_manager_id' => $params['staff_id'],

            ]
        ]);

        if (empty($plantaskInfo)) {
            $code = 0;
            $message = $real_message = $this->getTranslation()->_('plan_wms_not_task_save');
        }

        if (!empty($plantaskInfo) && strtotime($plantaskInfo->end_time) < time()) {
            $code = 0;
            $message = $real_message = $this->getTranslation()->_('plan_wms_time_not_sta_no_save');
        }
        if (!empty($plantaskInfo) && $plantaskInfo->status == 3) {
            $code = 0;
            $message = $real_message = $this->getTranslation()->_('plan_wms_revoke_not_save');
        }

        if (!empty($plantaskInfo) && $plantaskInfo->status == 4) {
            $code = 0;
            $message = $real_message = $this->getTranslation()->_('plan_wms_status_end.4');
        }

        $data = [];
        if (!empty($plantaskInfo) && $code == 1) {
            $plantaskInfo_arr = $plantaskInfo->toArray();


            $data['is_plan'] = true;
            if (strtotime($plantaskInfo_arr['end_time']) < time() || in_array($plantaskInfo_arr['status'], [InventoryCheckEnums::PLAN_NOUN_STATUS_END, InventoryCheckEnums::PLAN_NOUN_STATUS_REVOKE])) {
                $data['is_plan'] = false;
            }
        }
        return [
            'code' => $code,
            'message' => $code == InventoryCheckEnums::SUCCESS ? 'success' : $message,
            'data' => $data,
        ];

    }


    /**
     * 总盘点提交确定
     * @Date: 2022-05-16 09:57
     * @author: peak pan
     **/
    public function endPlanWmsSubmit($params)
    {
        $code = 1;
        $message = $real_message = '';

        $this->saveNotTaskChek(['plan_task_id' => $params['task_id']]);

        $db = MaterialWmsPlantaskModel::beginTransaction($this);
        try {
            //盘点的资产ID
            $planWmsObj = (new MaterialWmsPlantaskModel())->findFirst(
                [
                    'conditions' => ' id = :id:',
                    'bind' => [
                        'id' => $params['task_id']
                    ]
                ]);
            if ($planWmsObj) {
                $planWmsObj->status = InventoryCheckEnums::PLAN_NOUN_STATUS_END;
                $planWmsObj->updated_at = date('Y-m-d H:i:s');
                $insert_flag = $planWmsObj->save();
                if (!$insert_flag) {
                    throw new \Exception('plan-wms-endPlanWmsSubmit error');
                }
            }
            $unionStatus = $db->updateAsDict("material_wms_plantask_info", [
                "reality_end_time" => date('Y-m-d H:i:s')
            ], [
                "conditions" => " plan_task_id = ? ",
                "bind" => [
                    $params['task_id']
                ]
            ]);


            if (!$unionStatus) {
                $db->rollBack();
                $messages = 'plan-task-endPlanWmsSubmit' . '失败 /盘点任务id:' . $params['task_id'];
                throw new \Exception($message);
            }

            $db->commit();
            return [
                'code' => $code,
                'message' => $code == InventoryCheckEnums::SUCCESS ? 'success' : $message,
                'data' => true,
            ];
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('plan-wms-endPlanWmsSubmit' . $e->getMessage());
            throw $e;
        }

        $code = 1;
        $message = $real_message = '';
        $data = true;
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }


    /**
     * 获取数量
     * @Date: 2022-05-30 22:51
     * @return:
     **@author: peak pan
     */
    public function planWmsTaskCountMsg($staff_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['main' => MaterialWmsPlantaskModel::class]);
        $builder->where('main.plan_manager_id = :plan_manager_id: and main.is_deleted = :is_deleted: and main.end_time > :end_time: and  status in (1,2) and is_task = :is_task:', ['plan_manager_id' => $staff_id, 'is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'end_time' => date('Y-m-d H:i:s', time()), 'is_task' => InventoryCheckEnums::PLAN_IS_TASK_YES]);
        $totalInfo = $builder->getQuery()->getSingleResult();
        return intval($totalInfo->total);

    }


    /**
     * 获取任务条数
     * @Date: 2022-05-30 22:52
     * @return:
     **@author: peak pan
     */
    public function getMyPlanWmsCount($params, $status = 0)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['main' => MaterialWmsPlantaskModel::class]);

        if (!empty($status)) {
            $params['type'] = $params['type'] == 1 ? 2 : 1;
        }

        if ($params['type'] == 1) {
            //待处理
            $builder->where('main.plan_manager_id = :staff_info_id: and main.is_deleted = :is_deleted: and main.end_time >= :end_time: and  status not in({status:array}) and main.is_task = :is_task:', ['staff_info_id' => $params['staff_id'], 'is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'end_time' => date('Y-m-d H:i:s', time()), 'status' => [InventoryCheckEnums::PLAN_NOUN_STATUS_REVOKE, InventoryCheckEnums::PLAN_NOUN_STATUS_END], 'is_task' => InventoryCheckEnums::PLAN_IS_TASK_YES]);
        } else {
            //已结束
            $builder->where('main.plan_manager_id = :plan_manager_id: and main.is_deleted = :is_deleted: and (main.end_time < :end_time: or  status in({status:array}))', ['plan_manager_id' => $params['staff_id'], 'is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'end_time' => date('Y-m-d H:i:s', time()), 'status' => [InventoryCheckEnums::PLAN_NOUN_STATUS_REVOKE, InventoryCheckEnums::PLAN_NOUN_STATUS_END]]);
        }

        $totalInfo = $builder->getQuery()->getSingleResult();
        return intval($totalInfo->total);

    }


    /**
     * 获取盘点项目数据
     * @Date: 2022-05-30 22:52
     * @return:
     **@author: peak pan
     */
    public function getMyPlanWmsinfoCount($params, $status = 0)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['main' => MaterialWmsPlantaskInfoModel::class]);

        if (!empty($status)) {
            $params['type'] = $params['type'] == 1 ? 2 : 1;
        }
        if ($params['type'] == 1) {
            $builder->where(' main.is_deleted = :is_deleted: and main.plan_end_time >= :plan_end_time: and  plan_status != :plan_status: ', ['is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'plan_end_time' => date('Y-m-d H:i:s', time()), 'plan_status' => InventoryCheckEnums::PLAN_NOUN_STATUS_END]);
        } else {
            $builder->where(' main.is_deleted = :is_deleted: and (main.plan_end_time < :plan_end_time: or plan_status in ({plan_status:array}))', ['is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'plan_end_time' => date('Y-m-d H:i:s', time()), 'plan_status' => [3, 4]]);
        }

        if (!empty($params['category'])) {
            $builder->andWhere('main.category = :category:', ['category' => $params['category']]);
        }

        if (isset($params['goods_name']) && $params['goods_name'] != '') {
            $lang =  getLangToDefault(substr(strtolower($this->lang), 0, 2));
            $builder->andWhere('main.goods_name_' . $lang . ' like :goods_name:', ['goods_name' => '%' . $params['goods_name'] . '%']);
        }

        if (isset($params['specs_model']) && $params['specs_model'] != '') {
            $builder->andWhere('main.specs_model like :specs_model:', ['specs_model' => '%' . $params['specs_model'] . '%']);
        }

        if (!empty($params['id'])) {
            $builder->andWhere('main.plan_task_id = :plan_task_id:', ['plan_task_id' => $params['id']]);
        }
        $totalInfo = $builder->getQuery()->getSingleResult();
        return intval($totalInfo->total);
    }


    /**
     * 统计不加条件的总条数
     * @Date: 2022-05-30 22:52
     * @return:
     **@author: peak pan
     */
    public function getNotWherePlanWmsinfoCount($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['main' => MaterialWmsPlantaskInfoModel::class]);
        $builder->where(' main.is_deleted = :is_deleted: ', ['is_deleted' => InventoryCheckEnums::IS_DELETED_NO]);
        if (!empty($params['id'])) {
            $builder->andWhere('main.plan_task_id = :plan_task_id:', ['plan_task_id' => $params['id']]);
        }
        $totalInfo = $builder->getQuery()->getSingleResult();
        return intval($totalInfo->total);
    }


    /**
     * 获取盘点项目详情
     * @Date: 2022-05-30 22:53
     * @return:
     **@author: peak pan
     */
    public function getMyPlanWmsinfoDetailCount($params, $status = 0)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['main' => MaterialWmsPlantaskInfoModel::class]);

        $builder->where(' main.is_deleted = :is_deleted: and main.plan_end_time < :plan_end_time:', ['is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'plan_end_time' => date('Y-m-d H:i:s', time())]);

        if (!empty($params['id'])) {
            $builder->andWhere('main.plan_task_id = :plan_task_id:', ['plan_task_id' => $params['id']]);
        }
        $totalInfo = $builder->getQuery()->getSingleResult();
        return intval($totalInfo->total);
    }


    /**
     * 获取一条任务的数据
     * @Date: 2022-05-30 22:53
     * @return:
     **@author: peak pan
     */
    public function getMyPlanWmsOne($params)
    {

        $plantas = MaterialWmsPlantaskModel::findFirst([
            "conditions" => "id = :id:",
            'bind' => [
                'id' => $params['id']
            ],
        ]);
        return  empty($plantas) ? [] : $plantas->toArray();
    }


    /**
     * 根据员工的账号好网点 获取用户的手机号
     * @Date: 2022-05-15 22:47
     * @return:
     **@author: peak pan
     */
    public function getStaffStoreInfo($params)
    {
        $code = 1;
        $message = '';
        if ($params['store_id']) {
            $userInfo = HrStaffInfoModel::findFirst([
                "conditions" => "staff_info_id = :staff_info_id: and  sys_store_id = :sys_store_id:",
                'bind' => [
                    'staff_info_id' => $params['staff_id'],
                    'sys_store_id' => $params['store_id'],
                ],
            ]);
        } elseif ($params['department_id']) {
            $department_ids = $this->getDepartmentSubset($params['department_id']);
            if(empty($department_ids)){
                $userInfo =[];
            }else {
                $userInfo = HrStaffInfoModel::findFirst([
                    "conditions" => "staff_info_id = :staff_info_id: and  node_department_id in ({node_department_id:array})",
                    'bind' => [
                        'staff_info_id' => $params['staff_id'],
                        'node_department_id' => $department_ids,
                    ],
                ]);
            }
        }

        if (empty($userInfo)) {
            $code = 0;
            if ($params['store_id']) {
                $content = $this->getTranslation()->_('plan_wms_store_not_user');
                $message = str_replace(["{store_name}", "{store_name}"], [$params['store_name'], $params['store_name']], $content);
            } else {
                $content = $this->getTranslation()->_('plan_wms_department_not_user');
                $message = str_replace(["{department_name}", "{department_name}"], [$params['department_name'], $params['department_name']], $content);
            }
        }

        if (!empty($userInfo) && $userInfo->state == 2) {
            $code = 0;
            $message = $this->getTranslation()->_('plan_wms_user_not_job_status');
        }
        if (!empty($userInfo) && $userInfo->state == 3) {
            $code = 0;
            $message = $this->getTranslation()->_('plan_wms_user_not_job_status');
        }

        $data = [];
        if ($code == 1) {
            $data = [
                'mobile' => $userInfo->mobile,
                'mobile_company' => $userInfo->mobile_company,
                'staff_info_id' => $userInfo->staff_info_id,
                'name' => $userInfo->name
            ];
        }

        return [
            'code' => $code,
            'message' => $code == InventoryCheckEnums::SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }


    /**
     * 根据部门查询子部门集合
     * @Date: 2022-06-07 16:46
     * @return:
     **@author: peak pan
     */
    public function getDepartmentSubset($department_id)
    {
        $dept_list = [];
        $dept_detail = SysDepartmentModel::findFirst(['conditions' => "id = :id: and deleted = 0",
            'bind' => [
                'id' => $department_id
            ],
            'columns' => 'id,ancestry_v3']);
        if (!empty($dept_detail)) {
            $ancestry_v3 = empty($dept_detail->ancestry_v3) ? $dept_detail->id : $dept_detail->ancestry_v3;
            $dept_list = SysDepartmentModel::find([
                'conditions' => ' ancestry_v3 like :ancestry: or id = :id: ',
                'bind' => [
                    'ancestry' => $ancestry_v3 . '/%',
                    'id' => $dept_detail->id,
                ],
                'columns' => ['id'],
            ])->toArray();
            $dept_list = array_column($dept_list, 'id');
        }
        return $dept_list;
    }

    /**
     * 获取任务盘点数据详情
     * @Date: 2022-05-30 22:54
     * @return:
     **@author: peak pan
     */
    public function getInventoryCheckInfoById($inventory_check_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialWmsPlantaskInfoModel::class]);
        $builder->where('id = :inventory_check_id: and is_deleted = :is_deleted:', ['inventory_check_id' => $inventory_check_id, 'is_deleted' => InventoryCheckEnums::IS_DELETED_NO]);
        $detail = $builder->getQuery()->execute()->getFirst();
        return empty($detail) ? [] : $detail->toArray();
    }

}
