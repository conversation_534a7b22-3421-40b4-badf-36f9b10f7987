<?php
namespace FlashExpress\bi\App\Modules\Th\Server;
use FlashExpress\bi\App\Enums\AssetsGoodsEnums;
use FlashExpress\bi\App\Enums\InventoryCheckEnums;
use FlashExpress\bi\App\Enums\MaterialAttachmentEnums;
use FlashExpress\bi\App\Models\oa\MaterialAttachmentModel;
use FlashExpress\bi\App\Models\oa\MaterialInventoryCheckModel;
use FlashExpress\bi\App\Models\oa\MaterialInventoryCheckStaffAssetsModel;
use FlashExpress\bi\App\Models\oa\MaterialInventoryCheckStaffModel;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\BaseServer AS GlobalBaseServer;
/**
 * 资产盘点服务层
 * Class InventoryCheckServer
 * @package FlashExpress\bi\App\Modules\Th\Server
 */

class InventoryCheckServer extends GlobalBaseServer
{
    public $timezone;

    /**
     * InventoryCheckServer constructor.
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone='+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * 资产盘点-通知消息详情页面
     * @param array $params['inventory_check_id'=>'盘点单','staff_id'=>'员工工号'] 查询条件
     * @return array
     */
    public function getMsgDetail($params)
    {
        $data = [
            "inventory_check_id" => $params['inventory_check_id'],
            'start_at' => "",
            "end_at" => "",
            "count" => 0
        ];
        //盘点单ID
        $inventory_check_id = $params['inventory_check_id'];
        $inventory_check_info = $this->getInventoryCheckInfoById($inventory_check_id);
        //改盘点下发的盘单任务信息
        $inventory_check_staff_info = $this->getInventoryCheckStaffInfo($inventory_check_id, $params['staff_id']);
        if ($inventory_check_info && $inventory_check_staff_info) {
            if ($inventory_check_staff_info['status'] !=InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE && strtotime($inventory_check_info['end_at']) <= time()) {
                //员工未点击完成盘点，但是盘点周期结束了也要该状态为已完成
                $inventory_check_staff_info['status'] = (string)InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE;
            }
            $params['task_id'] = $inventory_check_staff_info['id'];
            $count =  $this->getWaitAssetsCount($params);
            $data['start_at'] = $inventory_check_info['start_at'];
            $data['end_at'] = $inventory_check_info['end_at'];
            $data['first_check_at'] = $inventory_check_staff_info['check_start_at'];
            $data['status'] = $inventory_check_staff_info['status'];
            $data['count'] = $count;
        }
        return $data;
    }

    /**
     * 获取待盘点数量 = 原始获取的总数（单项资产数量为1；多项资产数量的系统数量）- 盘点结果状态不包含扫码新增或者手工新增的资产总数（单项资产其他盘点结果状态数量为1，多项资产数量的系统数量）
     * @param array $params['inventory_check_id'=>'盘点单','staff_id'=>'员工工号'] 筛选条件
     * @return int|mixed
     */
    private function getWaitAssetsCount($params)
    {
        $assetServer = new AssetServer($this->lang, $this->timezone);
        //获取单项资产总数
        $single_assets_count = $assetServer->getMySingleAssetsCount($params);
        //获取多项资产总数
        $multiple_assets_count = $assetServer->getMyMultipleAssetsCount($params);
        return $single_assets_count+$multiple_assets_count;
    }

    /**
     * 获取已盘点资产数
     * @param integer $task_id 盘点任务ID
     * @return mixed
     */
    public function getHasCheckAssetsCount($task_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['s' => MaterialInventoryCheckStaffAssetsModel::class]);
        $builder->andWhere('s.staff_rel_id = :task_id:', ['task_id' => $task_id]);
        $builder->andWhere('s.is_deleted = :is_deleted:', ['is_deleted' => InventoryCheckEnums::IS_DELETED_NO]);
        $builder->andWhere('s.type not in ({types:array})', ['types' => [InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_HAND_ADD, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_CODE_ADD]]);
        $count_info = $builder->columns('COUNT(s.task_id) as total')->getQuery()->getSingleResult();
        return (int)($count_info->total);    }

    /**
     * 获取待盘点等的数量
     * @param array $params['inventory_check_id'=>'盘点单','staff_id'=>'员工工号'] 筛选条件
     * @return mixed
     * @throws \Exception
     */
    public function getCheckCount($params)
    {
        //检验盘点任务的合理性
        $this->taskCheck($params['inventory_check_id'], $params['staff_id']);
        //待盘点数量
        $data['wait_check_count'] = $this->getWaitAssetsCount($params);
        return $data;
    }

    /**
     * 获取已盘单的资产列表
     * @param integer $inventory_check_id 盘点单ID
     * @param integer $staff_id 员工工号
     * @param integer $is_batch 资产类型0单项，1多项
     * @return array
     */
    public function getHasCheckAssets($inventory_check_id, $staff_id, $is_batch)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['s.assets_goods_id', 's.assets_info_id']);
        $builder->from(['s' => MaterialInventoryCheckStaffAssetsModel::class]);
        $builder->where('s.inventory_check_id = :inventory_check_id: and s.staff_id = :staff_id:', ['inventory_check_id' => $inventory_check_id, 'staff_id'=>$staff_id]);
        $builder->andWhere('s.is_deleted = :is_deleted:', ['is_deleted' => InventoryCheckEnums::IS_DELETED_NO]);
        if ($is_batch == AssetsGoodsEnums::IS_BATCH_NO) {
            //单项资产
            $builder->andWhere('s.is_batch = :is_batch: and s.assets_info_id > :assets_info_id:', ['is_batch' => AssetsGoodsEnums::IS_BATCH_NO, 'assets_info_id' => 0]);
        } else if ($is_batch == AssetsGoodsEnums::IS_BATCH_YES) {
            //多项资产
            $builder->andWhere('s.is_batch = :is_batch: and s.is_public=:is_public: and s.assets_goods_id > :assets_goods_id:', ['is_batch' => AssetsGoodsEnums::IS_BATCH_YES, 'is_public'=>AssetsGoodsEnums::IS_PUBLIC, 'assets_goods_id' => 0]);
        }
        $items = $builder->getQuery()->execute();
        return $items ? $items->toArray() : [];
    }

    /**
     * 获取资产盘点-资产清单-单项资产（个人、公共）列表
     * @param array $params['inventory_check_id'=>'盘点单','staff_id'=>'员工工号', 'goods_name'=>'资产名称'] 查询条件
     * @return array
     * @throws ValidationException
     */
    public function getMySingleAssetsList($params)
    {
        //盘点单ID
        $inventory_check_id = $params['inventory_check_id'];
        //员工工号
        $staff_id = $params['staff_id'];
        //检验盘点任务的合理性
        $task_info = $this->taskCheck($inventory_check_id, $staff_id);
        $asset_list = [];
        //存在盘点任务且盘点任务未完成才可查看资产清单
        if ($task_info) {
            //可以正常进行盘点，则需要筛选出来改员工的资产清单
            $assetServer = new AssetServer($this->lang, $this->timezone);
            $asset_list = $assetServer->getMySingleAssetsList($params);
            if ($asset_list['items']) {
                $asset_list['items'] = $this->formatAssetList($asset_list['items'], $inventory_check_id, $task_info['id']);
            }
        }
        return $asset_list;
    }

    /**
     * 获取资产盘点-资产清单-多项资产（公共）列表
     * @param array $params['inventory_check_id'=>'盘点单','staff_id'=>'员工工号', 'goods_name'=>'资产名称'] 查询条件
     * @return array
     * @throws ValidationException
     */
    public function getMyMultipleAssetsList($params)
    {
        //盘点单ID
        $inventory_check_id = $params['inventory_check_id'];
        //员工工号
        $staff_id = $params['staff_id'];
        $task_info = $this->taskCheck($inventory_check_id, $staff_id);
        $asset_list = [];
        //存在盘点任务且盘点任务未完成才可查看资产清单
        if ($task_info) {
            //可以正常进行盘点，则需要筛选出来改员工的资产清单
            $assetServer = new AssetServer($this->lang, $this->timezone);
            $asset_list = $assetServer->getMyMultipleAssetsList($params);
            if ($asset_list['items']) {
                $asset_list['items'] = $this->formatAssetList($asset_list['items'], $inventory_check_id, $task_info['id']);
            }
        }
        return $asset_list;
    }

    /**
     * 检测员工盘点任务合法性
     * @param integer $inventory_check_id 盘点单ID
     * @param integer $staff_id 员工工号
     * @return array
     * @throws ValidationException
     */
    private function taskCheck($inventory_check_id, $staff_id)
    {
        //验证改员工是否存在改盘点任务以及改盘点任务是否已结束
        $inventory_check_info = $this->getInventoryCheckInfoById($inventory_check_id);
        if (!$inventory_check_info || $inventory_check_info['status'] == InventoryCheckEnums::INVENTORY_CHECK_STATUS_DONE) {
            //未找到盘点单或者盘点周期已结束
            throw new ValidationException($this->getTranslation()->_('inventory_check_info_error'));
        } else {
            //改员工是否存在改盘点单下发的盘单任务
            $inventory_check_staff_info = $this->getInventoryCheckStaffInfo($inventory_check_id, $staff_id);
            if (!$inventory_check_staff_info) {
                //未找到改员工改条盘点单下发的盘点任务
                throw new ValidationException($this->getTranslation()->_('inventory_check_task_error'));
            }
        }
        return $inventory_check_staff_info;
    }

    /**
     * 根据盘点单ID获取盘点信息
     * @param integer $inventory_check_id 盘点单ID
     * @return array
     */
    public function getInventoryCheckInfoById($inventory_check_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialInventoryCheckModel::class]);
        $builder->where('id = :inventory_check_id: and is_deleted = :is_deleted:', ['inventory_check_id' => $inventory_check_id, 'is_deleted' => InventoryCheckEnums::IS_DELETED_NO]);
        $detail = $builder->getQuery()->execute()->getFirst();
        return empty($detail) ? [] : $detail->toArray();
    }

    /**
     * 获取指定员工指定盘点单下发的盘点任务信息
     * @param integer $inventory_check_id 盘点单ID
     * @param integer $staff_id 员工工号
     * @return array
     */
    public function getInventoryCheckStaffInfo($inventory_check_id, $staff_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialInventoryCheckStaffModel::class]);
        $builder->where('inventory_check_id = :inventory_check_id: and staff_id = :staff_id: and is_deleted = :is_deleted:', ['inventory_check_id' => $inventory_check_id, "staff_id"=>$staff_id, 'is_deleted' => InventoryCheckEnums::IS_DELETED_NO]);
        $detail = $builder->getQuery()->execute()->getFirst();
        return empty($detail) ? [] : $detail->toArray();
    }

    /**
     * 格式化资产
     * @param array $list 资产清单
     * @param integer $inventory_check_id 盘点单ID
     * @param integer $inventory_check_staff_id 盘单任务ID
     * @return array
     */
    private function formatAssetList($list, $inventory_check_id, $inventory_check_staff_id)
    {
        if (!$list) return [];
        //订单详情列表
        foreach ($list as &$item) {
            $item['inventory_check_id'] = $inventory_check_id;
            $item['staff_rel_id'] = $inventory_check_staff_id;
        }
        return $list;
    }

    /**
     * 资产盘点-审批-盘点单-待处理,已结束记录数
     * @param array $params['staff_id'=>'员工工号','type'=>'1待处理，2已处理'] 查询条件
     * @return mixed
     */
    public function getTaskCount($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(task.id) as total');
        $builder->from(['task' => MaterialInventoryCheckStaffModel::class]);
        $builder->leftjoin(MaterialInventoryCheckModel::class, "task.inventory_check_id=main.id", "main");
        $builder = $this->getTaskCondition($builder, $params);
        $count_info = $builder->getQuery()->getSingleResult();
        return (int)($count_info->total);
    }

    /**
     * 资产盘点-审批-盘点单-待处理,已结束列表
     * @param array $params['staff_id'=>'员工工号','type'=>'1待处理，2已处理'] 查询条件
     * @param array $user_info 当前登陆者信息组
     * @return array
     */
    public function getTaskList($params, $user_info)
    {
        $params['staff_id'] = $user_info['staff_id'];
        $page_size = $params['page_size'];
        $page_num  = $params['page_num'];
        $data = [
            'items'      => [],
            'pagination' => [
                'page_num' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        $items = [];
        //获得数量
        $count = $this->getTaskCount($params);
        if ($count > 0) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'main.name',
                'main.end_at',
                'task.id',
                'task.inventory_check_id',
                'task.created_at',
                'task.status',
                'task.check_end_at'
            ]);
            $builder->from(['task' => MaterialInventoryCheckStaffModel::class]);
            $builder->leftjoin(MaterialInventoryCheckModel::class, "task.inventory_check_id=main.id", "main");
            $builder = $this->getTaskCondition($builder, $params);
            $offset    = $page_size * ($page_num - 1);
            $builder->limit($page_size, $offset);
            $items_obj = $builder->getQuery()->execute();
            $items = $items_obj ? $items_obj->toArray() : [];
            $items = $this->formatTaskList($items, $user_info, $params['type']);
        }
        $data['items']                     = $items ?? [];
        $data['pagination']['total_count'] = $count;
        return $data;
    }

    /**
     * 组装盘点查询条件
     * @param object $builder 查询器对象
     * @param array $condition['staff_id'=>'员工工号','type'=>'1待处理，2已处理'] 筛选条件组
     * @return mixed
     */
    private function getTaskCondition($builder, $condition)
    {
        $builder->where('task.staff_id = :staff_id: and task.is_deleted = :task_is_deleted: and main.is_deleted = :is_deleted:', ['staff_id'=>$condition['staff_id'], 'task_is_deleted'=>InventoryCheckEnums::IS_DELETED_NO, 'is_deleted'=>InventoryCheckEnums::IS_DELETED_NO]);
        $condition['type'] = $condition['type'] ?? 1;
        if ($condition['type'] == 1) {
            //待处理(未点击完成盘点按钮也没到盘点结束时间)
            $builder->andWhere('task.status < :status: and main.end_at > :now:', ['status'=>InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE, 'now' =>date('Y-m-d H:i:s')]);
        } else if ($condition['type'] == 2) {
            //已完成(点了完成盘点按钮或者未点击完成盘点按钮但超过了盘点结束时间)
            $builder->andWhere('(task.status = :status:) or (main.end_at <= :now: and task.status<:status:)', ['status'=>InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE, 'now' =>date('Y-m-d H:i:s')]);
        }
        return $builder;
    }

    /**
     * 格式化任务列表
     * @param array $list 任务列表
     * @param array $user_info 当前用户信息
     * @param integer $type 1待处理，2已完成
     * @return array
     */
    private function formatTaskList($list, $user_info, $type)
    {
        if (!$list) {
            return [];
        }
        $t = $this->getTranslation();
        foreach ($list as &$values) {
            $values['staff_name'] = $user_info['name'].'('.$user_info['staff_id'].')';
            if ($type == 1) {
                //待处理列表
                $values['count'] = $this->getWaitAssetsCount(['inventory_check_id' => $values['inventory_check_id'], 'staff_id'=>$user_info['staff_id']]);
            } else if ($type == 2) {
                //若员工未点击“结束盘点”按钮，则显示Asset在创建盘点单时，设定的盘点结束时间
                $values['end_at'] = $values['check_end_at'] ?? $values['end_at'];
                //已完成(点了完成盘点按钮或者未点击完成盘点按钮但超过了盘点结束时间)
                $values['status'] = InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE;
                //已完成列表，1期需求没有，设计稿放了也没讲明白规则，放到2期处理
                //$values['count'] = $this->getHasCheckAssetsCount($values['id']);
            }
            $values['status_text'] = $t->_(InventoryCheckEnums::$inventory_check_staff_status[$values['status']]);
        }
        return $list;
    }

    /**
     * 根据资产名称获取资产列表（去重复的）
     * @param string $name 资产名称
     * @param integer $page_num 当前页数
     * @param integer $page_size 每页条数
     * @return array
     */
    public function getAssetListByName($name, $page_num, $page_size)
    {
        $assetServer = new AssetServer($this->lang, $this->timezone);
        return $assetServer->getAssetListByName($name, $page_num, $page_size);
    }

    /**
     * 记录首次点击开始盘点按钮的时间
     * @param array $params['staff_id'=>'员工工号', 'inventory_check_id'=>'盘点单ID']查询条件
     * @return bool
     * @throws ValidationException
     */
    public function start($params)
    {
        //检验盘点任务的合理性
        $inventory_check_staff_info = $this->taskCheck($params['inventory_check_id'], $params['staff_id']);
        if ($inventory_check_staff_info['check_start_at'] && $inventory_check_staff_info['status'] != InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE) {
            //已经点击过
            return true;
        } else {
            //记录首次点击开始盘点
            $node_sql = "update material_inventory_check_staff set check_start_at = :check_start_at, status=:status, updated_at= :check_start_at where id = :id";
            return $this->getDI()->get('db_oa')->execute($node_sql, ['check_start_at' => date('Y-m-d H:i:s'), 'status'=>InventoryCheckEnums::INVENTORY_CHECK_STAFF_ING, 'id'=>$inventory_check_staff_info['id']]);
        }
    }

    /**
     * 检测单项资产是否在该员工名下
     * @param array $params['asset_code'=>'资产编码', 'staff_id'=>'员工工号', 'inventory_check_id'=>'盘点单ID']查询条件
     * @return array
     * @throws ValidationException
     */
    public function isOwner($params)
    {
        try {
            //检验盘点任务的合理性
            $inventory_check_staff_info = $this->taskCheck($params['inventory_check_id'], $params['staff_id']);
            $assetServer = new AssetServer($this->lang, $this->timezone);
            $data = $assetServer->isOwner($params);
            if ($data) {
                //在员工名下,保存扫码盘点
                $data['inventory_check_id'] = $params['inventory_check_id'];
                $data['staff_id'] = $params['staff_id'];
                $data['staff_rel_id'] = $inventory_check_staff_info['id'];
                $data['type'] = InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_CODE_GET;
                return $this->singleInventory($data);
            }
        } catch (ValidationException $e) {
            throw $e;
        }  catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('single-inventory- create' . $e->getMessage());
            throw $e;
        }
        return $data;
    }

    /**
     * 资产盘点～单项资产～盘到（sn有误）、没盘到、扫码盘点
     * @param array $params[
     *  'assets_goods_id'=>'资产商品ID','assets_info_id'=>'资产ID', 'bar_code'=>'barcode','asset_code'=>'资产编码','sn_code'=>'sn码','is_public'=>'1 公共资产 0个人资产','staff_id'=>'员工工号',
     *  'goods_name_en'=>'资产名称（英文）','goods_name_th'=>'资产名称（泰文）','goods_name_zh'=>'资产名称（中文）','inventory_check_id'=>'盘点单ID','staff_rel_id'=>'任务ID'
     *  'type'=>'盘点类型1盘到，2没盘到，5扫码盘点', 'reason'=>'没盘到原因','is_revise_sn_code'=>'sn有错误 0否，1是（点击了sn有误？）','revise_sn_code'=>'修正后的sn编码',
     *  'sn_code_file_arr'=>['file_name'=>'上传图片原始名称','bucket_name'=>'oss bucketName','object_key'=>'oss 对象 key 值']
     * ] 盘点数据
     * @return array
     * @throws ValidationException
     */
    public function singleInventory($params)
    {
        $inventory_check_id = $params['inventory_check_id'];//盘点单ID
        $staff_id = $params['staff_id'];//员工ID
        //检验盘点任务的合理性
        $this->taskCheck($inventory_check_id, $staff_id);
        $db = MaterialInventoryCheckStaffAssetsModel::beginTransaction($this);
        try {
            $type = $params['type'];//盘点类型
            $staff_rel_id = $params['staff_rel_id'];//任务ID
            $assets_goods_id = $params['assets_goods_id'];//资产商品ID
            $assets_info_id = $params['assets_info_id'];//资产ID
            $is_revise_sn_code = isset($params['is_revise_sn_code']) ? $params['is_revise_sn_code']: 0;//sn有错误 0否，1是
            //判断改任务下该资产是否已经被盘点过
            $staffInfo = MaterialInventoryCheckStaffAssetsModel::findFirst([
                'conditions' => "staff_rel_id = :staff_rel_id: and inventory_check_id = :inventory_check_id: and staff_id = :staff_id: and assets_info_id = :assets_info_id: and is_batch=:is_batch: and is_deleted=:deleted:",
                'bind' => [
                    'staff_rel_id' => $staff_rel_id,
                    'inventory_check_id' => $inventory_check_id,
                    'staff_id' => $staff_id,
                    'assets_info_id' => $assets_info_id,
                    'is_batch' => AssetsGoodsEnums::IS_BATCH_NO,
                    'deleted' => InventoryCheckEnums::IS_DELETED_NO
                ],
            ]);
            if (!empty($staffInfo)) {
                //存在盘点记录，则提示已经盘点过，不能重复盘点
                throw new ValidationException($this->getTranslation()->_('repeat_inventory_check_info_error'));
            }
            //开始盘点
            $model = new MaterialInventoryCheckStaffAssetsModel();
            $model->inventory_check_id = $inventory_check_id;
            $model->staff_id = $staff_id;
            $model->staff_rel_id = $staff_rel_id;
            $model->assets_goods_id = $assets_goods_id;
            $model->assets_info_id = $assets_info_id;
            $model->is_public = $params['is_public'];
            $model->is_batch = AssetsGoodsEnums::IS_BATCH_NO;
            $model->goods_name_en = $params['goods_name_en'] ?? '';
            $model->goods_name_th = $params['goods_name_th'] ?? '';
            $model->goods_name_zh = $params['goods_name_zh'] ?? '';
            $model->bar_code = $params['bar_code'];
            $model->asset_code = $params['asset_code'] ?? '';
            $model->sn_code = $params['sn_code'] ?? '';
            $model->revise_sn_code = $params['revise_sn_code'] ?? '';
            $model->reason = $params['reason'] ?? '';
            $model->type = ($is_revise_sn_code == 1) ? InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_GET :$type;
            $model->sys_asset_num = 1;//单项资产默认为1
            $model->actual_asset_num = 1;//单项资产默认为1
            $model->created_at = date('Y-m-d H:i:s');//创建时间
            $model->updated_at = date('Y-m-d H:i:s');//更新时间
            //保存数据
            if (!$model->save()) {
                $messages = $model->getMessages();
                foreach ($messages as $message) {
                    $message = '_' . $message;
                }
                throw new \Exception($message);
            }
            //盘点的资产ID
            $check_asset_id = $model->id;
            //sn错误附件
            $sn_code_file_arr = $params['sn_code_file_arr'] ?? [];
            if ($sn_code_file_arr && $is_revise_sn_code == 1) {
                //存在sn图片,只有sn码有误才存储图片，防止前端误传
                $img_insert['oss_bucket_type'] = MaterialAttachmentEnums::OSS_BUCKET_TYPE_INVENTORY_CHECK;
                $img_insert['oss_bucket_key'] = $check_asset_id;
                $img_insert['bucket_name'] = $sn_code_file_arr[0]['bucket_name'];//固定
                $img_insert['object_key'] = $sn_code_file_arr[0]['object_key'];
                $img_insert['file_name'] = $sn_code_file_arr[0]['file_name'];
                $img_insert['created_at'] = date('Y-m-d H:i:s');//创建时间
                $img_model = new MaterialAttachmentModel();
                $insert_flag = $img_model->create($img_insert);
                if (!$insert_flag) {
                    throw new \Exception('single-inventory- create error');
                }
            }
            $db->commit();
            return ['check_asset_id' => $check_asset_id];
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('single-inventory- create' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 资产盘点～多项资产～修改数量、确认
     * @param array $params[
     *  'assets_goods_id'=>'资产商品ID', 'bar_code'=>'barcode','staff_id'=>'员工工号',
     *  'goods_name_en'=>'资产名称（英文）','goods_name_th'=>'资产名称（泰文）','goods_name_zh'=>'资产名称（中文）',
     *  'inventory_check_id'=>'盘点单ID','staff_rel_id'=>'任务ID', 'reason'=>'没盘到原因'
     * ] 盘点数据
     * @return array
     * @throws ValidationException
     */
    public function multipleInventory($params)
    {
        try {
            $inventory_check_id = $params['inventory_check_id'];//盘点单ID
            $staff_id = $params['staff_id'];//员工ID
            //检验盘点任务的合理性
            $this->taskCheck($inventory_check_id, $staff_id);
            $staff_rel_id = $params['staff_rel_id'];//任务ID
            $assets_goods_id = $params['assets_goods_id'];//资产商品ID
            //判断改任务下该资产是否已经被盘点过
            $staffInfo = MaterialInventoryCheckStaffAssetsModel::findFirst([
                'conditions' => "staff_rel_id = :staff_rel_id: and inventory_check_id = :inventory_check_id: and staff_id = :staff_id: and assets_goods_id = :assets_goods_id: and is_public=:is_public: and is_batch=:is_batch: and is_deleted=:deleted:",
                'bind' => [
                    'staff_rel_id' => $staff_rel_id,
                    'inventory_check_id' => $inventory_check_id,
                    'staff_id' => $staff_id,
                    'assets_goods_id' => $assets_goods_id,
                    'is_public' => AssetsGoodsEnums::IS_PUBLIC,
                    'is_batch' => AssetsGoodsEnums::IS_BATCH_YES,
                    'deleted' => InventoryCheckEnums::IS_DELETED_NO
                ],
            ]);
            if (!empty($staffInfo)) {
                //存在盘点记录，则提示已经盘点过，不能重复盘点
                throw new ValidationException($this->getTranslation()->_('repeat_inventory_check_info_error'));
            }
            //开始盘点
            $model = new MaterialInventoryCheckStaffAssetsModel();
            $model->inventory_check_id = $inventory_check_id;
            $model->staff_id = $staff_id;
            $model->staff_rel_id = $staff_rel_id;
            $model->assets_goods_id = $assets_goods_id;
            $model->is_public = AssetsGoodsEnums::IS_PUBLIC;
            $model->is_batch = AssetsGoodsEnums::IS_BATCH_YES;
            $model->goods_name_en = $params['goods_name_en'] ?? '';
            $model->goods_name_th = $params['goods_name_th'] ?? '';
            $model->goods_name_zh = $params['goods_name_zh'] ?? '';
            $model->bar_code = $params['bar_code'];
            $model->sys_asset_num = $params['sys_asset_num'];
            $model->actual_asset_num = $params['actual_asset_num'] ?? 0;
            $model->reason = $params['reason'] ?? '';
            $model->type = InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_BATCH;
            $model->created_at = date('Y-m-d H:i:s');//创建时间
            $model->updated_at = date('Y-m-d H:i:s');//更新时间
            //保存数据
            if (!$model->save()) {
                $messages = $model->getMessages();
                foreach ($messages as $message) {
                    $message = '_' . $message;
                }
                throw new \Exception($message);
            }
            //盘点的资产ID
            $check_asset_id = $model->id;
            return ['check_asset_id' => $check_asset_id];
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('multiple-inventory- create' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 资产盘点～手动添加、扫码新增
     * @param array $params[
     *  'staff_id'=>'员工工号', 'inventory_check_id'=>'盘点单ID','asset_code'=>'资产编码','sn_code'=>'sn码','asset_num'=>'数量'
     *  'goods_name_en'=>'资产名称（英文）','goods_name_th'=>'资产名称（泰文）','goods_name_zh'=>'资产名称（中文）',
     * ]盘点数据
     * @return bool
     * @throws ValidationException
     */
    public function addInventory($params)
    {
        try {
            $inventory_check_id = $params['inventory_check_id'];
            $staff_id = $params['staff_id'];
            //检验盘点任务的合理性
            $inventory_check_staff_info = $this->taskCheck($inventory_check_id, $staff_id);
            $asset_code = $params['asset_code'] ?? '';
            $sn_code = $params['sn_code'] ?? '';
            //检测数据的合理性
            $this->checkAddData($inventory_check_id, $staff_id, $asset_code, $sn_code);
            //开始新增
            $model = new MaterialInventoryCheckStaffAssetsModel();
            $model->inventory_check_id = $inventory_check_id;
            $model->staff_id = $staff_id;
            $model->staff_rel_id = $inventory_check_staff_info['id'];
            $model->goods_name_en = $params['goods_name_en'] ?? '';
            $model->goods_name_th = $params['goods_name_th'] ?? '';
            $model->goods_name_zh = $params['goods_name_zh'] ?? '';
            $model->asset_code = $asset_code;
            $model->sn_code = $sn_code;
            $model->asset_num = $params['asset_num'] ?? 0;
            $model->type = $params['type'] ?? InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_HAND_ADD;
            $model->bar_code = $params['bar_code'] ?? '';//bar_code
            $model->created_at = date('Y-m-d H:i:s');//创建时间
            $model->updated_at = date('Y-m-d H:i:s');//更新时间
            if ($model->type == InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_CODE_ADD) {
                //针对扫码新增需要增加实际数量的入库
                $model->actual_asset_num = 1;
            }
            //保存数据
            if (!$model->save()) {
                $messages = $model->getMessages();
                foreach ($messages as $message) {
                    $message = '_' . $message;
                }
                throw new \Exception($message);
            }
            //盘点的资产ID
            $check_asset_id = $model->id;
            return ['check_asset_id' => $check_asset_id];
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('add-inventory- create' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 在手动新增、扫码新增界面，针对提交的按钮，增加校验如下：
     * 1.若当前界面资产编码，SN码均为空，则无校验。
     * 2.若当前界面资产编码，SN码均不为空，即包含资产编码有值SN无值，资产编码无值SN有值，资产编码和SN都有值，则校验当前填写的资产编码+SN码，
     * 和当前员工名下的本次盘点任务里盘点结果状态为“盘到”、“扫码盘点”、“扫码新增”或者“手动新增”的去匹配，如果有一致，则提示“已盘点成功，请勿重新添加”
     * 注：第2种校验，资产编码填成1，SN填为空；SN填为1，资产编码为空，是2个不同的东西，可以写入到库里
     * @param integer $inventory_check_id 盘点单ID
     * @param integer $staff_id 员工工号
     * @param string $asset_code 资产编码
     * @param string $sn_code sn编码
     * @return bool
     * @throws ValidationException
     */
    public function checkAddData($inventory_check_id, $staff_id, $asset_code = '', $sn_code = '')
    {
        if (empty($asset_code) && empty($sn_code)) {
            //若当前界面资产编码，SN码均为空，则无校验。
            return true;
        } else {
            $is_exists = MaterialInventoryCheckStaffAssetsModel::findFirst([
                'conditions' => 'inventory_check_id = :inventory_check_id: and staff_id = :staff_id: and type in ({types:array}) and is_deleted = :is_deleted: and (asset_code=:asset_code: and sn_code= :sn_code:)',
                'bind' => [
                    'inventory_check_id' => $inventory_check_id,
                    'staff_id' => $staff_id,
                    'types' => [InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_GET, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_HAND_ADD, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_CODE_GET, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_CODE_ADD],
                    'is_deleted' => InventoryCheckEnums::IS_DELETED_NO,
                    'asset_code' => $asset_code,
                    'sn_code' => $sn_code
                ]
            ]);
            if ($is_exists) {
                //该编号对应的资产均已盘点完毕
                throw new ValidationException($this->getTranslation()->_('repeat_inventory_check_info_error'));
            } else {
               return true;
            }
        }
    }

    /**
     * 记录点击结束盘点按钮的信息
     * @param array $params['staff_id'=>'员工工号', 'inventory_check_id'=>'盘点单ID']查询条件
     * @return bool
     * @throws ValidationException
     */
    public function stop($params)
    {
        //检验盘点任务的合理性
        $inventory_check_staff_info = $this->taskCheck($params['inventory_check_id'], $params['staff_id']);
        if ($inventory_check_staff_info['check_end_at'] && $inventory_check_staff_info['status'] == InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE) {
            //已经点击过
            return true;
        } else {
            //把原有审批流 delete & 重新固化当前审批流 & 返回固化审批流ID
            $node_sql = "update material_inventory_check_staff set check_end_at = :check_end_at, status=:status, updated_at= :check_end_at where id = :id";
            return $this->getDI()->get('db_oa')->execute($node_sql, ['check_end_at' => date('Y-m-d H:i:s'), 'status'=> InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE, 'id'=>$inventory_check_staff_info['id']]);
        }
    }

    /**
     * 检测指定盘点周期该员工的扫码新增资产盘点记录下资产编码、sn码的唯一性。
     * @param integer $inventory_check_id 盘点单ID
     * @param integer $staff_id 员工工号
     * @param string $code 编码
     * @return mixed
     */
    public function checkCodeAddAssetCodeOrSnCodeUnique($inventory_check_id, $staff_id, $code)
    {
        return MaterialInventoryCheckStaffAssetsModel::findFirst([
            'conditions' => 'inventory_check_id = :inventory_check_id: and staff_id = :staff_id: and type =:type: and is_deleted = :is_deleted: and (asset_code=:code: or sn_code= :code:)',
            'bind' => [
                'inventory_check_id' => $inventory_check_id,
                'staff_id' => $staff_id,
                'type' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_CODE_ADD,
                'is_deleted' => InventoryCheckEnums::IS_DELETED_NO,
                'code' => $code
            ]
        ]);
    }
}