<?php
/**
 * Created by PhpStor<PERSON>.
 * User: nick
 * Date: 11/3/21
 * Time: 8:01 PM
 */

namespace FlashExpress\bi\App\Modules\Th\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\OvertimeHourSettingDetailModel;
use FlashExpress\bi\App\Models\backyard\OvertimeMonthHourSettingModel;
use FlashExpress\bi\App\Models\backyard\OvertimeOneMonthHourSettingModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Modules\Th\library\Enums\OvertimeEnums;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\LeaveServer;
use FlashExpress\bi\App\Server\OvertimeServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\WorkflowServer;
use FlashExpress\bi\App\Server\HrShiftServer;

class OvertimeServer extends GlobalBaseServer{

    public $nwSpecialJob = [];

    /**
     * 新建加班
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function addOvertimeV3($paramIn = [])
    {
        $this->param           = $paramIn;
        $this->param['reason'] = addcslashes(stripslashes($this->param['reason']), "'");
        $staffId               = $this->param['staff_id'];

        //拼接end time
        $this->param['start_time']       = date('Y-m-d H:i:s', strtotime($this->param['start_time']));
        $this->param['start_time_stamp'] = strtotime($this->param['start_time']);
        $this->param['end_time_stamp']   = $this->param['start_time_stamp'] + floatval($this->param['duration']) * 3600;
        $this->param['end_time']         = date('Y-m-d H:i:s', $this->param['end_time_stamp']);

        $ext_server  = new OvertimeExtendServer($this->lang,$this->timezone);
        $staff_re    = new StaffRepository($this->lang);
        $shiftServer = new HrShiftServer();
        // 校验时间段 可选日期为近5天(前天、昨天和今天 明天后天) 如果是bi工具 不做时间校验
        //新需求 验证逻辑 修改 https://l8bx01gcjr.feishu.cn/docs/doccnznGnDKzb4akgPuhYQq5oHc
        $shiftInfo = $shiftServer->getShiftInfos($staffId, [$this->param['date_at']]);
        $this->shiftInfo = empty($shiftInfo) ? [] : $shiftInfo[$this->param['date_at']];
        $this->staffInfo = $staff_re->getStaffPosition($staffId);
        //如果是历史日期 要取固化表 替换当前信息 职位和网点
        if (strtotime($this->param['date_at']) < strtotime(date('Y-m-d'))) {
            $this->transferInfo              = HrStaffTransferModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and stat_date = :date_at:',
                'bind'       => ['staff_id' => $this->staffInfo['staff_info_id'], 'date_at' => $this->param['date_at']],
            ]);
            $this->staffInfo['job_title']    = empty($this->transferInfo) ? $this->staffInfo['job_title'] : $this->transferInfo['job_title'];
            $this->staffInfo['sys_store_id'] = empty($this->transferInfo) ? $this->staffInfo['sys_store_id'] : $this->transferInfo['store_id'];
            $this->staffInfo['hire_type']    = empty($this->transferInfo) ? $this->staffInfo['hire_type'] : $this->transferInfo['hire_type'];
        }

        if (empty($this->staffInfo)) {
            throw new ValidationException('can not find staff');
        }

        //时间区间 验证 工具过来的 跳过
        if (empty($paramIn['is_bi'])) {
            $this->checkOTTimePeriod($this->param['type'], $this->shiftInfo, $this->param['date_at']);
        }

        //验证 该员工 有没有对应的加班类型和时长 包括工具过来的
        $this->checkTypeDuration();

        $this->checkNwLimitDateV2($paramIn);

        //获取参考数据 审批流 和 业务逻辑限制 用
        $this->setConfigData();
        $this->nwSpecialJob = $ext_server->nwSpecialJob = $this->configData['OT-1.5OT_Approval_position'];
        $ext_server->configData = $this->configData;
        //获取审批流参考数据 和详情展示数据
        [$this->reference, $extend,$this->detailData] = $ext_server->getReference($staffId, $this->param);
        //dc 计算下 剩余加班小时预算 需要用本次时长 这个时长 只算 dc officer 但是 ass branch supervisor 要一样的审批流 所以 如果是 ass 申请 这里不能加上 不占用 dco的预算
        if (isset($this->reference['dc_in_all_hours'])
            && in_array($this->staffInfo['job_title'],$this->nwSpecialJob)
        ) {
            $this->reference['dc_in_all_hours'] += $this->param['duration'];
            $this->reference['dc_left_hours']   = $this->reference['dc_should_hours'] - $this->reference['dc_in_all_hours'];
            $this->detailData['dc_left_hours']  = $this->reference['dc_left_hours'];
        }

        //!!!!!!  加班 校验逻辑
        $paramIn['shift_info'] = $this->shiftInfo;
        $check_data = $this->checkOvertime($paramIn);

        //新增弹窗 模式 code 是1 有 is_error 1 前端需要弹confirm
        if ($check_data['code'] != 1 || !empty($check_data['data']['is_error'])) {
            return $check_data;
        }

        //判断 周期内 累计 duration 需要加上本次
        $appliedDuration             = $this->reference['duration'] ?? 0;
        $this->reference['duration'] = $this->detailData['duration'] = $appliedDuration + $this->param['duration'];

        //新增了 bi工具 补记录 状态直接为审核通过 不发push 审核人为 操作工具hr
        $higher = '';//bi工具 直接审核通过 需要记录操作人 带申请的 不需要记录上级
        $state  = enums::APPROVAL_STATUS_PENDING;
        if (!empty($paramIn['is_bi'])) {
            $higher = $paramIn['operator'];
            $state  = enums::APPROVAL_STATUS_APPROVAL;
        }

        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            $serialNo    = $this->getID();
            $insertParam = [
                'staff_id'        => $staffId,
                'job_title'       => $this->staffInfo['job_title'],
                'sys_store_id'    => $this->staffInfo['sys_store_id'],
                'hire_type'       => $this->staffInfo['hire_type'],
                'type'            => $this->param['type'],
                'start_time'      => $this->param['start_time'],
                'end_time'        => $this->param['end_time'],
                'reason'          => $this->param['reason'],
                'reject_reason'   => '',
                'state'           => $state,
                'duration'        => $this->param['duration'],
                'higher_staff_id' => $higher,
                'is_anticipate'   => $this->is_anticipate,
                'date_at'         => $this->param['date_at'],
                'references'      => json_encode($this->reference, JSON_UNESCAPED_UNICODE),
                'serial_no'       => (!empty($serialNo) ? 'OT' . $serialNo : null),
                'wf_role'         => 'ot_new',
                'detail_data'     => json_encode($this->detailData, JSON_UNESCAPED_UNICODE),
            ];

            $model = new HrOvertimeModel();
            $flag  = $model->create($insertParam);
            if (!$flag) {
                $db->rollback();
                $this->logger->write_log("add_overtime 审批流创建失败 {$staffId}".json_encode($insertParam));
                throw new \Exception('save overtime error');
            }

            //bi添加加班
            if (!empty($paramIn['is_bi'])) {
                $db->commit();
                return $this->checkReturn(['data' => ['overtime_id' => $model->overtime_id]]);
            }

            //非工具添加 走审批
            $approvalFlag = $this->saveApproval($model->overtime_id, $extend);
            if ($approvalFlag) {
                $db->commit();
                return $this->checkReturn(['data' => ['overtime_id' => $model->overtime_id]]);
            }
            //审批流操作失败了
            $db->rollback();
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->write_log("add_overtime 审批流创建异常 {$this->staffInfo['id']}".$e->getMessage());
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }
    }

    /**
     *
     * 校验除 时间范围内的 其他逻辑 添加ot 和bi 的 修改ot
     * 加班类型1=工作日，2=节假日加班，3=晚班 4-节假日正常上班
     *
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function checkOvertime($paramIn){
        $staffId       = $this->staffInfo['id'];
        $current_date = date('Y-m-d',time());//今天日期
        $start_date = date('Y-m-d',$this->param['start_time_stamp']);//申请开始时间日期
        $date = $this->param['date_at'];

        //新需求 OT申请开始时间不得晚于申请日期次日中午12点。
        $_date = date('Y-m-d 12:00:00', strtotime ("+1 day", strtotime($date)));

        if ($this->param['start_time'] > $_date) {
            throw new ValidationException($this->getTranslation()->_('ot_time_limit'));
        }

        //开始时间的日期不能早于申请日期 只试用于凌晨加班 的跨天 且不能超过两天
        $sub = (strtotime($start_date) - strtotime($date)) / 3600;
        if ($start_date < $date) {
            throw new ValidationException($this->getTranslation()->_('wrong_date'));
        }
        if ($sub >= 48) {
            throw new ValidationException($this->getTranslation()->_('wrong_date'));
        }

        //新增验证 日期限制逻辑 hcm 配置页面
        $this->checkLimitDate();

        //如选择“周末和假期加班”，系统验证该申请日期是否为公休日和周末，如申请日期非周末和假期
        //朝晖 口头需求 type=4的时候，和type=2 一样
        $ext_server  = new OvertimeExtendServer($this->lang, $this->timezone);
        $this->checkRestDay($this->param['type']);

        //看是否是补申请 并且 获取该日期打卡时间 校验加班时间 是否在打卡时间之内
        $att_model = new AttendanceRepository($this->lang, $this->timeZone);
        $add_hour  = $this->config->application->add_hour;

        //转换零时区
        $start    = date('Y-m-d H:i:s', $this->param['start_time_stamp'] - $add_hour * 3600);
        $end      = date('Y-m-d H:i:s', $this->param['end_time_stamp'] - $add_hour * 3600 - 300);
        $att_info = $att_model->getAttendanceInfo($staffId, $start, $end);

        //打卡间隔时长 新需求 增加考勤容错率 5分钟 由小时 改为分钟
        $attendance_last = 0;
        $allowed_min     = 5;
        //上下班打卡间隔时长
        if (!empty($att_info)) {
            $attendance_last = round((strtotime($att_info['end_at']) - strtotime($att_info['started_at'])) / 60,1);
        }

        //判断是预申请还是 补申请
        if ($start_date >= $current_date) {
            $this->is_anticipate = HrOvertimeModel::IS_ANTICIPATE;//是预申请
        }
        //如果是当天申请 且有打卡记录 视为 补申请
        if ($this->is_anticipate && !empty($att_info)) {
            $this->is_anticipate = HrOvertimeModel::UN_ANTICIPATE;
        }

        //所选日期没有上班打卡记录和下班打卡记录 并且是补申请；
        if($this->is_check_attendance && empty($att_info) && empty($this->is_anticipate)){
            throw new ValidationException($this->getTranslation()->_('1101'));
        }
        //时长不能超过8小时 没啥用
        $this->param['duration'] = $this->param['duration'] > 8 ? 8 : $this->param['duration'];

        //工作日加班 和 节假日 加班  超时类型加班判断
        $type = $this->param['type'];
        if(in_array($type,[HrOvertimeModel::OVERTIME_1,HrOvertimeModel::OVERTIME_2])){
            //新需求 https://l8bx01gcjr.feishu.cn/docs/doccnznGnDKzb4akgPuhYQq5oHc
            $ext_server->extend_check($paramIn, $this->staffInfo);

            //新增逻辑  这段好像也没啥用
            /**
             *  所选的开始时间+所选OT时长不得晚于实际的下班打卡时间 ---根据开始结束时间区间获取打卡记录 可以解决
            如果当天无请假，需要当天出勤超过11小时（9+2）,提示：当日出勤时长不足11小时，不能申请OT！
            如果当天请假半天，需要当天出勤超过6小时（4+2），提示：当日出勤时长不足6小时，不能申请OT！
            如果当天请假1天（且无打卡），不可以申请OT  提示：当天请假，不能申请OT！
             */
            //补申请 且有打卡记录 或者当天已打卡申请 都算补申请
            if($this->is_anticipate == HrOvertimeModel::UN_ANTICIPATE){//补申请 判断请假 验证时长 关联请假
                //获取当天是否有请假记录
                $leave_param['staff_id'] = $staffId;
                $leave_param['day'] = $this->param['date_at'];
                $leave_info = (new AuditRepository($this->lang))->getLevelData($leave_param);
                if(!empty($leave_info)){
                    //0 未请假 1 上午请假 2 下午请假 3 全天请假
                    if($leave_info['level_state'] == 1 || $leave_info['level_state'] == 2){
                        if($attendance_last < (5 * 60 - $allowed_min)){
                            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_attendance_limit_5'));
                        }
                    }
                }else{//没有请假 判断是否大于11小时
                    if($attendance_last < (10 * 60 - $allowed_min)){
                        return $this->checkReturn(-3, $this->getTranslation()->_('overtime_attendance_limit_10'));
                    }
                }
            }
        }

        //节假日上班类型  并且非预申请 跟考勤时长做对比
        if ($this->is_check_attendance && in_array($type, [
                HrOvertimeModel::OVERTIME_4,
                HrOvertimeModel::OVERTIME_5,
                HrOvertimeModel::OVERTIME_6,
            ]) && $this->is_anticipate == 0) {
            //考勤时长 小于4小时不能申请
            if($attendance_last < (4 * 60 - $allowed_min)){
                throw new ValidationException($this->getTranslation()->_('overtime_work_limit_4'));
            }
        }
        if(in_array($type, [HrOvertimeModel::OVERTIME_1,HrOvertimeModel::OVERTIME_4,HrOvertimeModel::OVERTIME_5,HrOvertimeModel::OVERTIME_6])){
            //新增需求 1倍ot 增加 额度限制 https://flashexpress.feishu.cn/docx/VyMedbkOUog6eAxr4CXcosh6nIb
            $oneCheck = $this->checkOneMonthBudget();
            //返回值 有可能是 二次确认的结构
            if(is_array($oneCheck)){
                return $oneCheck;
            }
        }

        //时长与打卡记录不符 不能申请
        if($this->is_check_attendance && $this->is_anticipate == HrOvertimeModel::UN_ANTICIPATE){
            if(($this->param['duration'] * 60 - $allowed_min) > $attendance_last){
                throw new ValidationException($this->getTranslation()->_('overtime_limit'));
            }
        }

        //校验 同一天是否存在 有交集的ot
        if(empty($paramIn['is_edit'])){
            $ext_server->check_ot_record($staffId,$date,$this->param['start_time_stamp'],$this->param['end_time_stamp'],$type);
        }

        //如果是 工具 不做新增 dc 等校验 https://flashexpress.feishu.cn/docx/doxcn635y5rMuLlchwXtVHISmwE
        if($this->staffInfo['organization_type'] == HrStaffInfoModel::ORGANIZATION_STORE
            && empty($paramIn['is_edit']) && empty($paramIn['is_bi'])
            && in_array($this->staffInfo['job_title'],$this->nwSpecialJob)

        ){
            $res = false;
            if($type == HrOvertimeModel::OVERTIME_1){
                //这是每周的配置 先不动
                $res = $this->checkOtBudget();
            }
            //返回值 有可能是 二次确认的结构
            if(is_array($res)){
                return $res;
            }
        }
        return $this->checkReturn([]);

    }

    //验证ot 预算是否够用 工具过来的不验证 这个验证只针对 dco
    public function checkOtBudget(){
        // nw dc 申请的 才有这个值 判断是否超预算
        if(isset($this->reference['dc_in_all_hours'])){
            //给前端 定制 confirm 返回
            if($this->reference['dc_in_all_hours'] > $this->reference['dc_should_hours']){
                $this->logger->write_log("add_overtime dc_officer {$this->staffInfo['id']} 时长不够 {$this->reference['dc_in_all_hours']}_{$this->reference['dc_should_hours']} " ,'info');
                return array('code' => 1,'msg' => $this->getTranslation()->_('dc_ot_alert'), 'data' => ['is_error' => 1]);
            }
        }else{
            //看配置的网点 有没有 也要限制 但是不影响审批流
            $setting_server  = new SettingEnvServer();
            $settingStores = $setting_server->getBusinessValue('setting_store');
            $settingStores = empty($settingStores) ? '' : explode(',',$settingStores);
            $settingStores = array_map('trim',$settingStores);
            if(empty($settingStores) || !in_array($this->staffInfo['sys_store_id'],$settingStores)){
                return true;
            }
            $ext_server = new OvertimeExtendServer($this->lang,$this->timezone);
            $ext_server->nwSpecialJob = $this->nwSpecialJob;
            $p['store_id'] = $this->staffInfo['sys_store_id'];
            $p['date'] = $this->param['date_at'];
            $p['type'] = $this->param['type'];
            $res = $ext_server->budgetHour($p);
            //没配置 不限制
            if(empty($res)){
                return true;
            }
            //已经申请的 + 本次申请的时长
            $in_all = bcadd($res['dc_in_all_hours'],$this->param['duration'],1);

            if($res['dc_in_all_hours'] > $res['dc_should_hours'] || $in_all > $res['dc_should_hours']){
                $this->logger->write_log("add_overtime dc_officer {$this->staffInfo['id']} {$this->staffInfo['sys_store_id']} 时长不够 {$res['dc_in_all_hours']}_{$res['dc_should_hours']} " ,'info');
                return array('code' => 1,'msg' => $this->getTranslation()->_('dc_ot_alert'), 'data' => ['is_error' => 1]);
            }
            //有额度 保存detail
            $this->detailData['dc_left_hours'] = bcsub($res['dc_should_hours'],$in_all,1);
        }

        return true;
    }


    //验证 1.5倍 和1 倍ot 合并验证 是否符合额度要求
    public function checkOneMonthBudget()
    {
        $t = $this->getTranslation();
        //总部员工
        if ($this->staffInfo['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            return true;
        }
        //正式 月薪 日薪 之外 不限制
        if (!in_array($this->staffInfo['hire_type'],
            [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3])) {
            return true;
        }
        //非 nw 部门 不限制  新增逻辑 https://flashexpress.feishu.cn/docx/NgTEd8l8foU3YIxLHVzcRnBKnzf
        if ($this->staffInfo['sys_department_id'] != enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID) {
            return true;
        }

        //职位配置
        $job_arr = (new SettingEnvServer())->listByCode(['1OT_Courier_Limit', '1OT_DCO_Limit', 'Must_have_OThours', 'LimitOT']);
        $job_arr = array_column($job_arr, 'set_val', 'code');
        $courier = empty($job_arr['1OT_Courier_Limit']) ? [] : explode(',', $job_arr['1OT_Courier_Limit']);
        $dc      = empty($job_arr['1OT_DCO_Limit']) ? [] : explode(',', $job_arr['1OT_DCO_Limit']);
        $bySwitch  = $job_arr['Must_have_OThours'] ?? 0;//by 申请的开关
        $toolSwitch = $job_arr['LimitOT'] ?? 0;//工具申请的开关 by 申请的 这开关没用 优先级高
        //如果 是工具过来的 并且 工具开关 关闭 不走下面限制逻辑
        if(empty($toolSwitch) && (!empty($this->param['is_bi']) || !empty($this->param['is_edit']))){
            $this->logger->write_log(['checkOneMonthBudget' => "toolSwitch {$toolSwitch}"],'info');
            return true;
        }
        $this->logger->write_log("checkOneMonthBudget setting" . json_encode($job_arr),'info');
        //非配置职位内的 不验证
        if (!in_array($this->staffInfo['job_title'], $dc) && !in_array($this->staffInfo['job_title'], $courier)) {
            return true;
        }
        //1。5倍 ot 限制
        if ($this->param['type'] == HrOvertimeModel::OVERTIME_1) {
            $moduleType = OvertimeHourSettingDetailModel::MODULE_TYPE_WORK_DAY;
            //工作日的加班 只有 dc 才限制
            if(!in_array($this->staffInfo['job_title'], $dc)){
                return true;
            }
        } else {
            $moduleType = OvertimeHourSettingDetailModel::MODULE_TYPE_OFF_DAY;
        }
        $monthSetting = OvertimeHourSettingDetailModel::findFirst([
            'conditions' => 'store_id = :store_id: and module_type = :module_type:and start_date <= :date: and end_date >= :date: and is_delete = 0',
            'bind'       => ['store_id'    => $this->staffInfo['sys_store_id'],
                             'date'        => $this->param['date_at'],
                             'module_type' => $moduleType,
            ],
        ]);

        //返回值
        $code = ErrCode::SUCCESS; //by 前端 二次确认要返回成功
        if (!empty($this->param['is_edit']) || !empty($this->param['is_bi'])) {
            $code = ErrCode::ERROR;//工具过来的 要error
        }
        $return = [
            'code' => $code,
            'msg'  => '',
            'data' => ['is_error' => 1],
        ];
        //开关关闭 并且没有配置 不限制
        if (empty($monthSetting) && empty($bySwitch)) {
            return true;
        }
        //开启 必须配置
        if (empty($monthSetting) && !empty($bySwitch)) {
            $return['msg'] = $t->_('ot_hour_must_set');
            return $return;
        }
        $key = '';//配置的小时数字段
        $hireArray = [];
        //日薪制员工 取类型 5，6
        if ($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_3) {
            $type = [HrOvertimeModel::OVERTIME_5, HrOvertimeModel::OVERTIME_6];
            $key  = 'daily_';
            $hireArray = [HrStaffInfoModel::HIRE_TYPE_3];
            //1。5倍
            if ($moduleType == OvertimeHourSettingDetailModel::MODULE_TYPE_WORK_DAY) {
                $type = [HrOvertimeModel::OVERTIME_1];
            }
        } else {//正式和月薪 取 4
            $type = [HrOvertimeModel::OVERTIME_4];
            $key  = 'formal_';
            $hireArray = [HrStaffInfoModel::HIRE_TYPE_1,HrStaffInfoModel::HIRE_TYPE_2];
            //1。5倍
            if ($moduleType == OvertimeHourSettingDetailModel::MODULE_TYPE_WORK_DAY) {
                $type = [HrOvertimeModel::OVERTIME_1];
            }
        }
        if (in_array($this->staffInfo['job_title'], $courier)) {
            $job_arr = $courier;
            $key     .= 'courier_hour';
        } else {
            $job_arr = $dc;
            $key     .= 'dc_hour';
        }

        $monthStart  = $monthSetting->start_date;
        $monthEnd    = $monthSetting->end_date;
        $settingHour = $monthSetting->$key;
        //如果是 null 不限制
        if (is_null($settingHour) && empty($bySwitch)) {
            return true;
        }
        if (is_null($settingHour) && !empty($bySwitch)) {
            $return['msg'] = $t->_('ot_hour_must_set');
            return $return;
        }
        //如果是0 表示不能申请
        if ($settingHour <= 0) {//本月加班时长较高，暂不支持申请
            $return['msg'] = $this->getTranslation()->_('ot_hour_limit_notice');
            return $return;
        }
        $conditions = 'date_at between  :start_time: and :end_time: and state in (1,2) and type in ({types:array}) and sys_store_id = :store_id: and job_title in ({job_title:array}) and hire_type in({hire_type:array})';
        $bind       = [
            'start_time' => $monthStart,
            'end_time'   => $monthEnd,
            'types'      => $type,
            'store_id'   => $this->staffInfo['sys_store_id'],
            'job_title'  => $job_arr,
            'hire_type'  => $hireArray,
        ];
        //工具过来的编辑操作 要排除 修改的ot记录
        if (!empty($this->param['overtime_id'])) {
            $conditions          .= ' and overtime_id != :overtime_id:';
            $bind['overtime_id'] = $this->param['overtime_id'];
        }
        $applyHour = HrOvertimeModel::sum([
            'column'     => 'duration',
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        $applyHour = empty($applyHour) ? 0 : $applyHour;
        $this->logger->write_log(['checkOneMonthBudget_' . $this->staffInfo['staff_info_id'] => "applyHour {$applyHour} settingHour {$settingHour} current {$this->param['duration']}"],'info');

        $in_all = bcadd($applyHour, $this->param['duration'], 1);
        //额度不够
        if ($applyHour > $settingHour || $in_all > $settingHour) {
            $return['msg'] = $this->getTranslation()->_('ot_hour_limit_notice');
            return $return;
        }
        //1.5倍的 要记录一个 剩余额度
        if($moduleType == OvertimeHourSettingDetailModel::MODULE_TYPE_WORK_DAY){
            $this->detailData['dc_month_left_hours'] = bcsub($settingHour, $in_all, 1);
        }
        return true;
    }


    /**
     * 校验申请加班日期、班次
     * @param int $overtime_type    加班类型
     * @param array $shift_info     班次信息
     * @param string $date          加班日期
     * @return void
     */
    public function checkOTTimePeriod($overtime_type, $shift_info, $date)
    {
        $date_tmp   = strtotime($date);
        $beforeTime = $behindTime = strtotime(date('Y-m-d'), time());
        //可以多两天时间区间限制
        if (in_array($overtime_type,
            [HrOvertimeModel::OVERTIME_4, HrOvertimeModel::OVERTIME_5, HrOvertimeModel::OVERTIME_6])
        )
        {
            $behindTime = strtotime(date("Y-m-d", strtotime("+2 day")));
        }

        // 根据生效日期取新或旧班次
        $start = $shift_info['start'] ?? '';
        $end = $shift_info['end'] ?? '';

        //- 如果是班次跨天的员工，比如23：00-8：00班次，跨天的班次，可以申请前一天的加班。
        $shift_start = strtotime("{$date} {$start}");
        $shift_end   = strtotime("{$date} {$end}");
        if ($shift_start > $shift_end && in_array($overtime_type,array_keys($this->overOt))) {
            $beforeTime = strtotime(date("Y-m-d", strtotime("-1 day")));
        }
        if ($date_tmp < $beforeTime || $date_tmp > $behindTime) {
            $notice_str = '';

            if (in_array($overtime_type,
                [HrOvertimeModel::OVERTIME_4, HrOvertimeModel::OVERTIME_5, HrOvertimeModel::OVERTIME_6])) {
                $notice_str = 'overtime_two_days';
            }
            if ($overtime_type == HrOvertimeModel::OVERTIME_1) {
                $notice_str = 'overtime_one_days_1';
            }
            if ($overtime_type == HrOvertimeModel::OVERTIME_2) {
                $notice_str = 'overtime_one_days_2';
            }
            throw new ValidationException($this->getTranslation()->_($notice_str), enums::$ERROR_CODE['1000']);
        }
    }

    //申请加班操作 验证 有没有类型和时长的权限
    public function checkTypeDuration()
    {
        $checkParam['staff_id'] = $this->staffInfo['staff_info_id'];
        $typeList               = $this->getTypeOvertime($checkParam);

        if ($typeList['code'] != 1 || empty($typeList['data']['dataList'])) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004'));
        }
        //整理数据
        $typeList = array_column($typeList['data']['dataList'], null, 'code');

        //能申请的 类型
        $typeArr = array_keys($typeList);
        if(!in_array($this->param['type'], $typeArr)){
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004'));
        }

        //能申请的时长
        $durationArr = array_column($typeList[$this->param['type']]['duration'], 'time_hour');
        $this->logger->write_log("checkTypeDuration {$this->staffInfo['id']} ".json_encode($typeArr).json_encode($durationArr), 'info');
        if (!in_array($this->param['duration'], $durationArr)) {
            throw new ValidationException($this->getTranslation()->_('ot_duration_permission'));
        }

        return true;
    }

    /**
     * 校验休息日
     * @param int $type 加班类型
     * @return bool
     * @throws \Exception
     */
    public function checkRestDay(int $type)
    {
        //获取员工信息
        $model      = new StaffRepository($this->lang);
        $staff_info = $this->staffInfo;
        $date       = $this->param['date_at'];
        //轮休数据
        $restDay = $model->get_work_days_between($this->staffInfo['staff_info_id'], $date, $date);
        $restDay = !empty($restDay) ? array_column($restDay, 'date_at') : [];

        //ph 数据
        $leave_server = new LeaveServer($this->lang, $this->timezone);
        $leave_server = Tools::reBuildCountryInstance($leave_server, [$this->lang, $this->timezone]);
        $holidays     = $leave_server->ph_days($staff_info);
        $holidays     = empty($holidays) ? [] : array_column($holidays, 'day');

        //正式员工原逻辑 轮休和 ph 混合判断
        $isRest = in_array($date, $restDay) || in_array($date, $holidays);
        if (!$isRest && in_array($type, [HrOvertimeModel::OVERTIME_2, HrOvertimeModel::OVERTIME_4])) {
            throw new ValidationException($this->getTranslation()->_('overtime_weekend'), enums::$ERROR_CODE['1000']);
        }
        //非休息 和ph 不能申请超时
        if ($isRest && $type == HrOvertimeModel::OVERTIME_1) {
            throw new ValidationException($this->getTranslation()->_('overtime_weekend_forbidden'),
                enums::$ERROR_CODE['1000']);
        }
        //日薪员工 休息日
        if ($type == HrOvertimeModel::OVERTIME_5 && !in_array($date, $restDay)) {
            throw new ValidationException($this->getTranslation()->_('daily_rest_notice'));
        }
        //日薪 节假日
        if ($type == HrOvertimeModel::OVERTIME_6 && !in_array($date, $holidays)) {
            throw new ValidationException($this->getTranslation()->_('daily_holiday_notice'));
        }
        //如果 休息日和ph 重合  不能申请 休息日加班
        if(in_array($date, $restDay) && in_array($date, $holidays) && $type == HrOvertimeModel::OVERTIME_5){
            throw new ValidationException($this->getTranslation()->_('daily_holiday_rest_notice'));
        }
        return true;
    }


    /**
     * 编辑加班  bi工具
     * @param $paramIn
     */
    public function edit_overtime($paramIn)
    {
        $paramIn['is_edit'] = 1;
        $this->param        = $paramIn;

        $staffId = $this->param['staff_id'];

        //拼接end time
        $this->param['start_time']       = date('Y-m-d H:i:s', strtotime($this->param['start_time']));
        $this->param['start_time_stamp'] = strtotime($this->param['start_time']);
        $this->param['end_time_stamp']   = $this->param['start_time_stamp'] + floatval($this->param['duration']) * 3600;
        $this->param['end_time']         = date('Y-m-d H:i:s', $this->param['end_time_stamp']);

        $staff_re    = new StaffRepository($this->lang);
        // 校验时间段 可选日期为近5天(前天、昨天和今天 明天后天) 如果是bi工具 不做时间校验
        //新需求 验证逻辑 修改 https://l8bx01gcjr.feishu.cn/docs/doccnznGnDKzb4akgPuhYQq5oHc
        $shiftServer = new HrShiftServer();
        $shiftInfo = $shiftServer->getShiftInfos($staffId,[$this->param['date_at']]);
        $this->shiftInfo = empty($shiftInfo) ? [] : $shiftInfo[$this->param['date_at']];
        $this->staffInfo = $staff_re->getStaffPosition($staffId);
        //如果是历史日期 要取固化表 替换当前信息 职位和网点
        if (strtotime($this->param['date_at']) < strtotime(date('Y-m-d'))) {
            $this->transferInfo              = HrStaffTransferModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and stat_date = :date_at:',
                'bind'       => ['staff_id' => $this->staffInfo['staff_info_id'], 'date_at' => $this->param['date_at']],
            ]);
            $this->staffInfo['job_title']    = empty($this->transferInfo) ? $this->staffInfo['job_title'] : $this->transferInfo['job_title'];
            $this->staffInfo['sys_store_id'] = empty($this->transferInfo) ? $this->staffInfo['sys_store_id'] : $this->transferInfo['store_id'];
            $this->staffInfo['hire_type']    = empty($this->transferInfo) ? $this->staffInfo['hire_type'] : $this->transferInfo['hire_type'];
        }
        if (empty($this->staffInfo)) {
            throw new ValidationException('can not find staff');
        }
        $validations = [
            "overtime_id" => "Required|Int",
            "date_at"     => "Required|Date",
            "start_time"  => "Required|Str",
            "type"        => "Required|Int",
            "duration"    => "Required",
        ];

        $this->validateCheck($this->param, $validations);
        $this->param['shift_info'] = $paramIn['shift_info'] = $this->shiftInfo;
        $res                       = $this->checkOvertime($paramIn);

        if ($res['code'] != 1) {
            return $res;
        }

        $model = new OvertimeRepository($this->timezone);
        $info  = $model->getInfoById($this->param['overtime_id']);
        if ($info['state'] != enums::APPROVAL_STATUS_APPROVAL) {//非审核通过的 记录 不允许 修改 没走完正常审核逻辑
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_bi_notice'));
        }

        $update_data = [
            'type'            => $this->param['type'],
            'start_time'      => $this->param['start_time'],
            'end_time'        => $this->param['end_time'],
            'state'           => enums::APPROVAL_STATUS_APPROVAL,//bi 工具 直接审核通过
            'duration'        => $this->param['duration'],
            'higher_staff_id' => $this->param['operator'],
            'is_anticipate'   => $this->is_anticipate,
            'date_at'         => $this->param['date_at'],
        ];

        $model = new OvertimeRepository($this->timezone);
        $flag  = $model->updateInfoByTable('hr_overtime', 'overtime_id', $this->param['overtime_id'], $update_data);
        if ($flag) {
            return $this->checkReturn([]);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }
    }


    /**
     * 获取 加班类型  svc 和 controller 调用 svc 返回所有类型 非svc 根据员工判定权限
     *
     * @param array $paramIn
     * @param array $userinfo
     * @return array
     */
    public function getTypeOvertime($paramIn = [], $userinfo = [])
    {
        $data = $this->getAllOtType();
        //如果是工具svc  展示全部类型
        if(!empty($paramIn['is_svc'])){
            $returnData['data']['dataList'] = $data;
            return $this->checkReturn($returnData);
        }

        //java 那边登录信息 缓存时间长 信息变动 会不同步 需要重新查询
        $staff_re = new StaffRepository($this->lang);
        if(empty($this->staffInfo)){
            $this->staffInfo = $staff_re->getStaffPosition($paramIn['staff_id']);
        }

        //调rpc 配置
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('OT_apply_rules')
            ->setParameters(['staff_info_id' => $paramIn['staff_id']])
            ->getConfig();

        $setting = [];
        if(!empty($res['response_type']) && $res['response_type'] == ConditionsRulesEnums::RESPONSE_TYPE_VALUE){
            $setting = $res['response_data'];
        }
        $data = $this->formatPermissionOvertimeType($data, $setting);
        $data = $this->getTypeDuration($data);


        //ffm 模板 用别的 类型 3
        $audit_server = new AuditServer($this->lang,$this->timezone);
        $ffm_flag = $audit_server->isFullDepartment($paramIn['staff_id']);
        //模板文案 前端
        if ($userinfo && isset($userinfo['store_category']) && in_array($userinfo['store_category'], [
                enums::$stores_category['hub'],
                enums::$stores_category['bhub'],
                enums::$stores_category['os'],
            ])
        ) {
            $returnData['data']['template_type'] = enums::OT_TEMPLATE_1;
        } elseif ($ffm_flag) {
            $limitJobs = (new SettingEnvServer())->getSetVal('ffm_norest_ot_position', ',');
            $returnData['data']['template_type'] = enums::OT_TEMPLATE_3;
            if(in_array($this->staffInfo['job_title'], $limitJobs)){
                $returnData['data']['template_type'] = enums::OT_TEMPLATE_5;
            }
        } else {
            $returnData['data']['template_type'] = enums::OT_TEMPLATE_2;
        }

        //日薪制员工弹窗
        if($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_3){
            $returnData['data']['template_type'] = enums::OT_TEMPLATE_4;
        }

        $returnData['data']['dataList'] = $data;
        return $this->checkReturn($returnData);

    }

    //所有加班类型展示 这里面不做权限判断
    public function getAllOtType($locale = ''){
        $new_duration = array(
            array('time_hour' => 4,'time_text' => $this->getTranslation($locale)->_('ot_4_new_text')),
            array('time_hour' => 8,'time_text' => $this->getTranslation($locale)->_('ot_8_new_text')),
        );
        //小时选项
        $new_customer = array();
        $i = 2;
        while ($i <= 8){
            $new_customer[] = array('time_hour' => $i, 'time_text' => "{$i}h");
            $i ++;
        }
        return  [
            [
                'code' => (string)HrOvertimeModel::OVERTIME_1,
                'msg'  => $this->getTranslation($locale)->_('5107'),
                'sub_msg' => $this->getTranslation($locale)->_('1.5_times_salary'),
                'duration' => $new_customer,
            ],
            [
                'code' => (string)HrOvertimeModel::OVERTIME_2,
                'msg'  => $this->getTranslation($locale)->_('5108'),
                'sub_msg' => $this->getTranslation($locale)->_('3_times_salary'),
                'duration' => $new_customer,
            ],
            [
                'code' => (string)HrOvertimeModel::OVERTIME_4,
                'msg'  => $this->getTranslation($locale)->_('5111'),
                'sub_msg' => $this->getTranslation($locale)->_('1_times_salary'),
                'duration' => $new_duration,
            ],
            [
                'code' => (string)HrOvertimeModel::OVERTIME_5,
                'msg'  => $this->getTranslation($locale)->_('daily_ot_rest_day'),//日薪制员工 休息日加班
                'sub_msg' => $this->getTranslation($locale)->_('daily_ot_2_times'),
                'duration' => $new_duration,
            ],
            [
                'code' => (string)HrOvertimeModel::OVERTIME_6,
                'msg'  => $this->getTranslation($locale)->_('daily_ot_holiday'),//日薪制员工 节假日加班
                'sub_msg' => $this->getTranslation($locale)->_('daily_ot_1_times'),
                'duration' => $new_duration,
            ],
        ];

    }

    //给审批流历史用的 类型数据
    public function getApprovalOtType($locale = '')
    {
        return $this->getAllOtType($locale);
    }



    /**
     * 获取加班类型，用于hcm-api系统展示 根据工号 展示对应权限的加班选项
     */
    public function getOvertimeTypeList($paramIn = [])
    {
        if(empty($paramIn['staff_id'])) {
            return [];
        }
        $data = $this->getTypeOvertime($paramIn);
        return $data['data']['dataList'] ?? [];

    }


    /**
     * 审批完成回调方法
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        //如果为最终审批状态，则同步更新审批状态
        if ($isFinal) {
            $overtimeInfo = HrOvertimeModel::findFirst($auditId);
            if (empty($overtimeInfo)) {
                throw new \Exception('ot info error');
            }
            $overtimeInfo->state = $state;
            $overtimeInfo->in_approval = HrOvertimeModel::NOT_IN_APPROVAL;
            if ($state == Enums::APPROVAL_STATUS_REJECTED) {
                if (isset($extend['staff_id'])) {
                    $staff = HrStaffInfoModel::findFirst([
                        'conditions' => ' staff_info_id = :staff_id: ',
                        'bind'       => ['staff_id' => $extend['staff_id']],
                    ]);
                    if ($staff) {
                        $staff = $staff->toArray();
                    }
                }
                $overtimeInfo->approver_id   = isset($extend['staff_id']) ? $extend['staff_id'] : 0;
                $overtimeInfo->approver_name = isset($staff) && $staff ? $staff['name'] : '';
                $overtimeInfo->reject_reason = isset($extend['remark']) ? $extend['remark'] : '';
            }
            $overtimeInfo->update();

            $staff_re   = new StaffRepository($this->lang);
            $staff_info = $staff_re->getStaffPosition($overtimeInfo->staff_id);
            $this->staffInfo = $staff_info;

            //如果 是 nw 或者 bu 的 dc  需要固化数据
            //这里是 network management 部门 和 network bulky 的  DC Officer 职位
            $this->setConfigData();
            $extServer = new OvertimeExtendServer($this->lang,$this->timezone);
            $extServer->configData = $this->configData;
            $extServer->nwSpecialJob = $this->configData['OT-1.5OT_Approval_position'];
            $limitJobTitle = $this->nwSpecialJob = $this->configData['OT-1.5OT_Approval_position'];
            //组织架构变更 新逻辑 https://flashexpress.feishu.cn/docx/Lx7pd6oO3o9TMix6WVLcRxt6nde
            $audit_server            = new AuditServer($this->lang, $this->timezone);
            $is_bulky                = $audit_server->isBelongByAncestry($overtimeInfo->staff_id, enums::NETWORK_BULKY_AREA);
            $is_pcc                  = $audit_server->isBelongByAncestry($overtimeInfo->staff_id, enums::NETWORK_PICKUP_CONTROL_CENTER);
            $is_ffm                  = $audit_server->isFullDepartment($overtimeInfo->staff_id);
            $networkManagementId = $this->configData['dept_network_management_id'];
            //修改需求 只有 1。5倍和 3倍 固化
            if (in_array($overtimeInfo->type,[HrOvertimeModel::OVERTIME_1,HrOvertimeModel::OVERTIME_2]) && ($staff_info['sys_department_id'] == $networkManagementId || $is_bulky || $is_pcc)
                    && in_array($staff_info['job_title'], $limitJobTitle)
            ) {
                //固化详情数据
                $detailData = $extServer->freezeDetail($overtimeInfo->toArray());
                $overtimeInfo->detail_data = json_encode($detailData, JSON_UNESCAPED_UNICODE);
                $overtimeInfo->update();
            }

            //shop 要固化的数据
            $shop_flag = $audit_server->isBelongByAncestry($overtimeInfo->staff_id,enums::SALES_CRM_ACCESS_SHOP_DEPARTMENT_ID);
            if($shop_flag && in_array($staff_info['job_title'], [
                    enums::$job_title['shop_officer'],
                    enums::$job_title['shop_supervisor'],
                    enums::$job_title['th_senior_shop_officer']]))
            {
                $extServer->staffInfo = $staff_info;
                $detailData = $extServer->freezeShopDetail($overtimeInfo->toArray());
                $overtimeInfo->detail_data = json_encode($detailData, JSON_UNESCAPED_UNICODE);
                $overtimeInfo->update();
            }
            //ffm 最后审批 固化参考数据
            if($is_ffm){
                $ffmOvertimeDetails = $this->getFfmOvertimeDetails($overtimeInfo->toArray(), true);
                if(!empty($ffmOvertimeDetails)){
                    $overtimeInfo->detail_data = json_encode($ffmOvertimeDetails, JSON_UNESCAPED_UNICODE);
                    $overtimeInfo->update();
                }
            }

            //1 倍ot 的 dc 和 courier 固化 出勤率 数据
            if (in_array($overtimeInfo->type, [
                    HrOvertimeModel::OVERTIME_4,
                    HrOvertimeModel::OVERTIME_5,
                    HrOvertimeModel::OVERTIME_6,
                ])
                && (in_array($staff_info['job_title'], $this->configData['1OT_Courier_Limit'])
                    || in_array($staff_info['job_title'], $this->configData['1OT_DCO_Limit'])))
            {
                $otDetail = json_decode($overtimeInfo->detail_data, true);
                $this->setConfigData();
                $extServer->configData = $this->configData;
                //历史数据 审批中的 没有新增的字段 也保存成新的
                if(!isset($otDetail['staff_hire_type'])){
                    $otDetail = $extServer->one_ot_detail($staff_info, $overtimeInfo->toArray());
                    //获取实时数据
                    $rate = $extServer->oneOtAttendanceRate($staff_info['sys_store_id'], $overtimeInfo->date_at);
                    $otDetail['one_ot_attendance_rate'] = "{$rate['on_num']}/{$rate['staff_num']}({$rate['rate']}%)";
                }else{//新增数据 直接固化 出勤率
                    $rateData = $extServer->oneOtAttendanceRate($staff_info['sys_store_id'], $overtimeInfo->date_at);
                    $otDetail['one_ot_attendance_rate'] = "{$rateData['on_num']}/{$rateData['staff_num']}({$rateData['rate']}%)";
                }
                $overtimeInfo->detail_data = json_encode($otDetail, JSON_UNESCAPED_UNICODE);
                $overtimeInfo->update();
            }
            //获取部门ID
            $envModel     = new SettingEnvServer();
            $setting_code = ['flash_freight_hub_management_id', 'dept_hub_management_id'];
            $setting_val  = $envModel->listByCode($setting_code);
            if (!empty($setting_val)) {
                $setting_val = array_column($setting_val, 'set_val', 'code');
            }

            $flashFreightHubId = $setting_val['flash_freight_hub_management_id'] ?? '';
            $hubManagementId   = $setting_val['dept_hub_management_id'] ?? '';
            if ($state == enums::$audit_status['approved'] && in_array($staff_info['sys_department_id'],
                    [$hubManagementId, $flashFreightHubId])) {
                $flow_server = new WorkflowServer($this->lang, $this->timezone);
                $apply_info  = AuditApplyModel::findFirst([
                    'conditions' => 'biz_type = :biz_type: and biz_value = :biz_value:',
                    'bind'       => ['biz_type' => AuditListEnums::APPROVAL_TYPE_OVERTIME, 'biz_value' => $auditId],
                ]);
                //要抄送的工号
                $audit_hrbp_cc = $flow_server->findHRBP($staff_info['node_department_id'],
                    ['store_id' => $staff_info['sys_store_id']]);
                $audit_hrbp_cc = explode(',', $audit_hrbp_cc);
                $flow_server->final_approval_cc($audit_hrbp_cc, $apply_info);
            }

            $this->sendMessage($overtimeInfo->toArray(),$state);
        }
    }

    /**
     * 获取详情 只有泰国用
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //[1]获取加班详情数据
        $result = $this->overtime->infoOvertime(['overtime_id' => $auditId]);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_id']);
        if (empty($staff_info['data'])) {
            throw new ValidationException('wrong hrs staff info');
        }

        $staff_info = $this->staffInfo = $staff_info['data'];
        $extServer = new OvertimeExtendServer($this->lang,$this->timezone);

        //[2]组织详情数据
        $t            = $this->getTranslation();
        $overtimeType = $this->getDetailOtType();
        $references = json_decode($result['references'], true) ?? '';
        $detailData = json_decode($result['detail_data'], true) ?? '';
        //head 信息
        $data = [
            'title'       => $this->auditlist->getAudityType(enums::$audit_type['OT']),
            'id'          => $result['overtime_id'],
            'staff_id'    => $result['staff_id'],
            'type'        => enums::$audit_type['OT'],
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['state'],
            'status_text' => $this->auditlist->getAuditStatus('10'.$result['state']),
            'notice'      => $result['notice'] ?? '',
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        $duration_count = round((strtotime($result['end_time']) - strtotime($result['start_time'])) / 3600, 1);

        $detailLists = [
            'apply_parson'     => sprintf('%s ( %s )', $staff_info['name'], $staff_info['id']),
            'apply_department' => sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? ''),
            'OT_date'          => $result['date_at'],
            'OT_type'          => ($overtimeType[$result['type']] ?? ''),
            'start_time'       => $result['start_time'],
            'end_time'         => $result['end_time'],
            'duration'         => $duration_count,
            'OT_reason'        => $result['reason'],
            'ot_detail_6'      => $staff_info['store_name'],
        ];

        //根据配置项类型 展示结束时间 是否加1小时 只有泰国和菲律宾 公用 其他国家都有
        if (in_array($result['type'], array_keys($this->holidayOt)) && $result['duration'] >= 8) {
            $detailLists['end_time'] = date('Y-m-d H:i:s', strtotime($result['end_time'].' + 1 hours'));
        }

        $audit_server = new AuditServer($this->lang, $this->timezone);
        $is_bulky     = $audit_server->isBelongByAncestry($result['staff_id'], enums::NETWORK_BULKY_AREA);
        $is_pcc       = $audit_server->isBelongByAncestry($result['staff_id'], enums::NETWORK_PICKUP_CONTROL_CENTER);
        $shop_flag    = $audit_server->isBelongByAncestry($result['staff_id'], enums::SALES_CRM_ACCESS_SHOP_DEPARTMENT_ID);
        $is_ffm       = $audit_server->isFullDepartment($result['staff_id']);
        //对应国家 配置了 env  才会显示
        $this->setConfigData();
        $extServer->configData = $this->configData;
        $extServer->nwSpecialJob = $this->configData['OT-1.5OT_Approval_position'];
        //修改需求 为 只有 1。5倍 和3倍的 dc 才展示这些
        if(in_array($result['type'], [HrOvertimeModel::OVERTIME_1, HrOvertimeModel::OVERTIME_2]) && in_array($references['job_title'],$this->configData['OT-1.5OT_Approval_position'])
            //额外网点类型判断 以后要去掉
            && in_array($staff_info['store_category'], [
                enums::$stores_category['sp'],
                enums::$stores_category['dc'],
                enums::$stores_category['shop_pickup_only'],
                enums::$stores_category['shop_pickup_delivery'],
                enums::$stores_category['shop_ushop'],
                enums::$stores_category['bdc'],
                enums::$stores_category['cdc'],
            ])
        ){
            //获取定制 详情页字段
            $ext_param['networkManagementId'] = $this->configData['dept_network_management_id'];
            $other_detail_data                = $extServer->ot_dc_detail($staff_info, $result, $ext_param);
            $detailLists                      = array_merge($detailLists, $other_detail_data);
        }

        //月度剩余
        if(isset($detailData['dc_month_left_hours'])){
            $detailLists['dc_month_left_hours'] = ($detailData['dc_month_left_hours'] ?? 0)."h";
        }

        //shop 相关的详情展示 先不动
        if ($shop_flag &&
            in_array($staff_info['job_title'], [
                enums::$job_title['shop_officer'],
                enums::$job_title['shop_supervisor'],
                enums::$job_title['th_senior_shop_officer']])
        ) {
            $shop_detail = $extServer->shop_detail($staff_info, $result);
            $detailLists = array_merge($detailLists, $shop_detail);
        }

        //一倍ot 定制职位 的详情参考数据展示
        if (in_array($result['type'], [
                HrOvertimeModel::OVERTIME_4,
                HrOvertimeModel::OVERTIME_5,
                HrOvertimeModel::OVERTIME_6,
            ])
            && (in_array($staff_info['job_title'], $this->configData['1OT_Courier_Limit'])
                || in_array($staff_info['job_title'], $this->configData['1OT_DCO_Limit'])))
        {
            //获取 出勤率
            $detailData = $extServer->getOneOtDetail($staff_info, $result);

            //两个table 放head里 然后 unset 注意 历史数据
            if(isset($detailData['fbi_effective_num'])){
                $data['fbi_effective_num'] = $detailData['fbi_effective_num'];
                unset($detailData['fbi_effective_num']);
            }
            if(isset($detailData['ms_parcel_num'])){
                $data['ms_parcel_num'] = $detailData['ms_parcel_num'];
                unset($detailData['ms_parcel_num']);
            }

            $detailLists = array_merge($detailLists, $detailData);
        }
        //ffm 部门 详情 参考数据 审批完成 固化 雇佣类型1，2 formal 1，4 非外协， 非自账号
        if($is_ffm && in_array($staff_info['hire_type'], [1,2,5]) && in_array($staff_info['formal'], [1,4]) && $staff_info['is_sub_staff'] == 0){
            $ffmOvertimeDetails = $this->getFfmOvertimeDetails($result);
            //第一个表格 2月加班统计
            if(isset($ffmOvertimeDetails['ffm_duration'])){
                $data['ffm_duration'] = $ffmOvertimeDetails['ffm_duration'];
                unset($detailData['ffm_duration']);
            }
            //第二个表格 出勤统计
            if(isset($ffmOvertimeDetails['ffm_attendance'])){
                $data['ffm_attendance'] = $ffmOvertimeDetails['ffm_attendance'];
                unset($detailData['ffm_attendance']);
            }
        }
        
        if (isset($references['is_alert']) && $references['is_alert'] && ($is_bulky || $is_pcc)) { //需要提醒时，判断是否为最终节点
            $server = new WorkflowServer($this->lang, $this->timezone);
            $applyModel = AuditApplyModel::findFirst([
                'conditions' => 'biz_value = :audit_id: and biz_type = :audit_type:',
                'bind' => [
                    'audit_id' => $auditId,
                    'audit_type' => AuditListEnums::APPROVAL_TYPE_OVERTIME,
                ],
            ]);
            $isFinal = $server->isFinalNode($applyModel, $user);

            //员工加班提交时间的当月（自然月）累计1倍OT大于24小时的
            //爆仓直接通过，但若不爆仓，仅最后一级审批人点击「同意」后不弹出弹窗，直接同意通过，其余审批人需填写同意原因
            $duration = $references['duration'] ?? 0;
            $phNum = $references['ph_num'] ?? 0;
            if ($duration > (24 + 8 * $phNum) && $isFinal) {
                $references['is_alert'] = 0;
            } else {
                $references['is_alert'] = 1;
            }
        }

        $data['is_alert'] = $references['is_alert'] ?? 0;//针对 dc 休息日申请ot 要弹审批通过的原因 如果是 ph 或者爆仓 可以不填写

        // 格式化详情数据
        $returnData['data']['detail'] = $this->format($detailLists);
        $returnData['data']['head'] = $data;
        
        return $returnData;
    }

    /**
     * 添加 ot  保存审批流
     * staffInfo
     * @param $id
     * @param $extend
     * @return bool
     */
    public function saveApproval($id,$extend){
        //创建审批流
        $server = new ApprovalServer($this->lang, $this->timezone);

        //没有对接可视化的部门(Network Management[4]、Retail Management[13])
        //nw 对接可视化 去掉id
        $nonVisualizeDepIds = [13];
        $nwSwitch = (new SettingEnvServer())->getSetVal('ot_nw_switch');
        if($nwSwitch == 1){
            $nonVisualizeDepIds = [4, 13];
        }

        //获取其子部门
        $SysDepartmentModel = new SysDepartmentModel();
        foreach ($nonVisualizeDepIds as $dep_id) {
            $deptIds = $SysDepartmentModel->getSpecifiedDeptAndSubDept($dep_id);
            $nonVisualizeDepIds = array_merge($nonVisualizeDepIds, $deptIds);
        }
        if (in_array($this->staffInfo['node_department_id'], $nonVisualizeDepIds)) { //Network Management、Retail Management部门下审批流是手动配置的
            $workflowViewState = WorkflowModel::WORKFLOW_MANUAL;
        } else { //可视化
            $workflowViewState = WorkflowModel::WORKFLOW_VISUALIZE;
        }

        //可视化上线后，这里可以删除
        //==============
        $switchState = (new SettingEnvServer())->getSetVal('ot_view_switch');
        if ($switchState == 1) {
            $workflowViewState = WorkflowModel::WORKFLOW_MANUAL;
        }
        //==============

        //获取审批流ID
        $workflow_id = $server->getWorkflowId(AuditListEnums::APPROVAL_TYPE_OVERTIME, $workflowViewState);

        $requestId = $server->create($id, AuditListEnums::APPROVAL_TYPE_OVERTIME, $this->staffInfo['id'],null,$extend,'', $workflow_id);
        if (!$requestId) {
            throw new \Exception('创建审批流失败');
        }
        return true;
    }



    //nw 部门制定日期 不能申请对应倍数的ot 临时方案 以后删除
    public function checkNwLimitDateV2($paramIn)
    {
        $envModel     = new SettingEnvServer();
        $limitJobTitle  = $envModel->getSetVal('nw_limit_jot_title',',');
        //nw部门 临时限制 日期 不能申请 工具不限制 配置需求上了之后 这个要删除
        if (!in_array($this->staffInfo['job_title'], $limitJobTitle)
            || $this->staffInfo['sys_department_id'] != enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID
            || !empty($paramIn['is_bi'])) {
            return true;
        }

        $limitDates  = $envModel->getSetVal('nw_limit_date_2025',',');
        //ot 限制
        if (in_array($this->param['date_at'], $limitDates)) {
            throw new ValidationException($this->getTranslation()->_('limit_date_15'));
        }

        //以下只限制 1倍ot
        if(!in_array($this->param['type'], [4,5,6])){
            return true;
        }
        $limitDates  = $envModel->getSetVal('nw_limit_date_2025_one',',');
        //ot 限制
        if (!empty($limitDates) && in_array($this->param['date_at'], $limitDates)) {
            throw new ValidationException($this->getTranslation()->_('limit_date_15'));
        }

        return true;
    }


    /**
     * 获取FFM部门员工的加班详情
     * @param array $otInfo 加班信息
     * @param bool $isAudit 是否是审批进来
     * @return array 格式化后的加班详情数据
     */
    public function getFfmOvertimeDetails($otInfo, $isAudit = false)
    {
        $return = [];
        if (!in_array($otInfo['type'],
            [HrOvertimeModel::OVERTIME_1, HrOvertimeModel::OVERTIME_2, HrOvertimeModel::OVERTIME_4])) {
            return $return;
        }
        //非审核通过 取detail
        if (!$isAudit && $otInfo['state'] != enums::$audit_status['panding']) {
            $detailData = json_decode($otInfo['detail_data'], true) ?? '';
            if(isset($detailData['ffm_duration'])){
                $return['ffm_duration'] = $detailData['ffm_duration'];
            }
            if(isset($detailData['ffm_attendance'])){
                $return['ffm_attendance'] = $detailData['ffm_attendance'];
            }
            return $return;
        }

        //第一个表格 2月加班统计
        $return['ffm_duration'] = $this->ffm_table_1($otInfo);

        //第二个表格 出勤统计
        $return['ffm_attendance'] = $this->ffm_table_2($otInfo);
        return $return;
    }

    private function ffm_table_1($otInfo)
    {
        // 获取两个月内的日期范围
        $start = date('Y-m-01', strtotime('-1 months'));
        $end   = date('Y-m-t');

        // 如果状态是待审批(1)，则实时计算
        $overtimeRecords = HrOvertimeModel::find([
            'conditions' => 'staff_id = :staff_id: AND date_at BETWEEN :start_date: AND :end_date: AND state IN (1,2) ',
            'bind'       => [
                'staff_id'   => $otInfo['staff_id'],
                'start_date' => $start,
                'end_date'   => $end,
            ],
            'columns'    => 'type, date_at, duration',
        ])->toArray();

        $overtimeData = [];
        foreach ($overtimeRecords as $ot) {
            $month              = date('Y-m', strtotime($ot['date_at']));
            $key                = "{$month}_{$ot['type']}";
            $overtimeData[$key] = $overtimeData[$key] ?? 0;
            $overtimeData[$key] += $ot['duration'];
        }
        //整理数据
        $monthList = [date('Y-m', strtotime($start)), date('Y-m', strtotime($end))];
        $res       = [];
        foreach ($monthList as $month) {
            $row['month']  = $month;
            $key           = "{$month}_" . HrOvertimeModel::OVERTIME_1;
            $row['type_1'] = ($overtimeData[$key] ?? 0) . ' h';

            $key           = "{$month}_" . HrOvertimeModel::OVERTIME_4;
            $row['type_4'] = ($overtimeData[$key] ?? 0) . ' h';

            $key           = "{$month}_" . HrOvertimeModel::OVERTIME_2;
            $row['type_2'] = ($overtimeData[$key] ?? 0) . ' h';

            $res[] = $row;
        }
        return $res;
    }

    /**
     * 获取FFM表格2的参考数据
     * @param array $otInfo 加班信息
     * @return array 返回包含在职人数、排班人数、出勤人数等数据的数组
     */
    /**
     * 获取FFM表格2的参考数据
     * @param array $otInfo 加班信息
     * @return array 返回包含在职人数、排班人数、出勤人数等数据的数组
     */
    private function ffm_table_2($otInfo)
    {
        // 获取员工信息
        $staffInfo    = $this->staffInfo;
        $overtimeDate = $otInfo['date_at'];

        // 1. 获取在职员工ID列表和人数
        $activeStaffData = $this->getActiveStaffIds(
            $staffInfo['sys_department_id'],
            $staffInfo['node_department_id'],
            $staffInfo['sys_store_id'],
            $staffInfo['job_title'],
            $overtimeDate
        );

        $activeStaffIds   = $activeStaffData['ids'];
        $activeStaffCount = $activeStaffData['count'];

        // 2. 根据加班类型获取统计数据
        if (in_array($otInfo['type'], [HrOvertimeModel::OVERTIME_1, HrOvertimeModel::OVERTIME_2])) {
            // 出勤相关（针对加班 type 是 1 和 2）
            $attendanceCount = $this->getAttendanceCount($overtimeDate, $activeStaffIds);
            $attendanceRate  = $this->calculateRate($attendanceCount, $activeStaffCount);
            return [
                'res_type'           => OvertimeEnums::FFM_SHOW_TYPE_OVER,
                'active_staff_count' => $activeStaffCount,
                'stat_count'         => $attendanceCount,
                'stat_rate'          => $attendanceRate,
            ];
        }

        if ($otInfo['type'] == HrOvertimeModel::OVERTIME_4) {
            // 排班相关（针对加班 type 4）
            $scheduledCount = $this->getScheduledCount($overtimeDate, $activeStaffIds);
            $scheduledRate  = $this->calculateRate($scheduledCount, $activeStaffCount);
            return [
                'res_type'           => OvertimeEnums::FFM_SHOW_TYPE_REST,
                'active_staff_count' => $activeStaffCount,
                'stat_count'         => $scheduledCount,
                'stat_rate'          => $scheduledRate,
            ];
        }

        return [];
    }

    /**
     * 获取在职员工ID列表和数量
     * @param int $deptId 部门ID
     * @param int $nodeDeptId 子部门ID
     * @param string $storeId 网点ID
     * @param int $jobTitle 职位ID
     * @param string $date 日期
     * @return array ['ids' => [], 'count' => int]
     */
    private function getActiveStaffIds($deptId, $nodeDeptId, $storeId, $jobTitle, $date)
    {
        $today        = date('Y-m-d');
        $isHistorical = strtotime($date) < strtotime($today);

        if ($isHistorical) {
            // 历史日期从固化表获取
            $statDate = date('Y-m-d', strtotime($date));
            $query    = $this->modelsManager->createBuilder()
                ->columns('staff_info_id')
                ->from(HrStaffTransferModel::class)
//                ->where('sys_department_id = :deptId:', ['deptId' => $deptId])
                ->andWhere('node_department_id = :nodeDeptId:', ['nodeDeptId' => $nodeDeptId])
                ->andWhere('store_id = :storeId:', ['storeId' => $storeId])
                ->andWhere('job_title = :jobTitle:', ['jobTitle' => $jobTitle])
                ->andWhere('stat_date = :statDate:', ['statDate' => $statDate])
                ->andWhere('state = :state:', ['state' => HrStaffInfoModel::STATE_ON_JOB]);
        } else {
            // 当前或未来日期从员工表获取
            $query = $this->modelsManager->createBuilder()
                ->columns('staff_info_id')
                ->from(['s' => HrStaffInfoModel::class])
//                ->where('s.sys_department_id = :deptId:', ['deptId' => $deptId])
                ->andWhere('s.node_department_id = :nodeDeptId:', ['nodeDeptId' => $nodeDeptId])
                ->andWhere('s.sys_store_id = :storeId:', ['storeId' => $storeId])
                ->andWhere('s.job_title = :jobTitle:', ['jobTitle' => $jobTitle])
                ->andWhere('s.state = :state:', ['state' => HrStaffInfoModel::STATE_ON_JOB]);
        }

        $result = $query->getQuery()->execute()->toArray();
        $ids    = array_column($result, 'staff_info_id');

        return [
            'ids'   => $ids,
            'count' => count($ids),
        ];
    }

    /**
     * 获取出勤人数
     * @param string $date 日期
     * @param array $activeStaffIds 在职员工ID数组
     * @return int
     */
    private function getAttendanceCount($date, $activeStaffIds)
    {
        if (empty($activeStaffIds)) {
            return 0;
        }

        // 查询这些员工的出勤记录
        $query = $this->modelsManager->createBuilder()
            ->columns('staff_info_id')
            ->from(StaffWorkAttendance::class)
            ->inWhere('staff_info_id', $activeStaffIds)
            ->andWhere('attendance_date = :date:', ['date' => $date]);

        $result = $query->getQuery()->execute();
        return $result ? count($result->toArray()) : 0;
    }

    /**
     * 获取排班人数
     * @param string $date 日期
     * @param array $activeStaffIds 在职员工ID数组
     * @return int
     */
    private function getScheduledCount($date, $activeStaffIds)
    {
        if (empty($activeStaffIds)) {
            return 0;
        }

        // 查询这些员工的排班记录
        $query = $this->modelsManager->createBuilder()
            ->columns('staff_info_id')
            ->from(HrStaffWorkDayModel::class)
            ->inWhere('staff_info_id', $activeStaffIds)
            ->andWhere('date_at = :date:', ['date' => $date]);

        $scheduledStaffIds = array_column($query->getQuery()->execute()->toArray(), 'staff_info_id');

        // 计算在职但未排班的员工数
        return count(array_diff($activeStaffIds, $scheduledStaffIds));
    }

    /**
     * 计算比率
     * @param int $numerator 分子
     * @param int $denominator 分母
     * @return string
     */
    private function calculateRate($numerator, $denominator)
    {
        return $denominator > 0 ? round(($numerator / $denominator) * 100) . '%' : '0%';
    }


    public function setConfigData()
    {
        //这里是 network management 部门 和 network bulky 的  DC Officer 职位
        $envModel     = new SettingEnvServer();
        $setting_code = [
            'dept_network_management_id',
            'OT-1.5OT_Approval_position',
            'ot_s1_job_ids',
            '1OT_Courier_Limit',
            '1OT_DCO_Limit',
        ];
        $setting_val  = $envModel->listByCode($setting_code);
        if (!empty($setting_val)) {
            $setting_val = array_column($setting_val, 'set_val', 'code');
        }

        $this->configData['dept_network_management_id'] = $setting_val['dept_network_management_id'] ?? 4;
        $this->configData['ot_s1_job_ids']              = empty($setting_val['ot_s1_job_ids']) ? [] : explode(',', $setting_val['ot_s1_job_ids']);
        $this->configData['1OT_Courier_Limit']          = empty($setting_val['1OT_Courier_Limit']) ? [] : explode(',', $setting_val['1OT_Courier_Limit']);
        $this->configData['OT-1.5OT_Approval_position'] = empty($setting_val['OT-1.5OT_Approval_position']) ? [] : explode(',', $setting_val['OT-1.5OT_Approval_position']);
        $this->configData['1OT_DCO_Limit']              = empty($setting_val['1OT_DCO_Limit']) ? [] : explode(',', $setting_val['1OT_DCO_Limit']);
    }
}
