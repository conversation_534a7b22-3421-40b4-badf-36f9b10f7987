<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Server\BaseServer AS GlobalBaseServer;

class DemoServer extends GlobalBaseServer
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return DemoService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    public static $validate_param = [
        'name' => 'StrLenGeLe:1,50',
        'age' => 'IntGt:0',
    ];

    public function getDemoList($params)
    {
        return $params;
    }

}