<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Enums\JobTransferConfirmEnums;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\JobTransferMessageServer;
use FlashExpress\bi\App\Server\JobTransferV2Server as BaseJobTransferServer;
use FlashExpress\bi\App\Server\StaffServer;

class JobTransferV2Server extends BaseJobTransferServer
{
    /**
     * 设置回调
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return void
     * @throws InnerException
     * @throws ValidationException
     * @throws \Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        //获取详情
        if ($isFinal) {
            $transferDetail = JobTransferModel::findFirst($auditId);
            if (empty($transferDetail)) {
                throw new InnerException(sprintf('data id %d not exist', $auditId));
            }
            if ($transferDetail->approval_state_stage_one == enums::APPROVAL_STATUS_PENDING) {

                //审批同意-> 转岗确认
                $this->processStageOne($transferDetail, $state, $extend, $isFinal);

            } else {
                $this->processStageTwo($transferDetail, $state, $extend, $isFinal);
            }

            if ($state != enums::APPROVAL_STATUS_APPROVAL && !empty($transferDetail->hc_id)) {
                (new HcServer($this->lang, $this->timeZone))->remittanceHc($transferDetail->hc_id);
            }

            $jobTransferInfo = $this->getJobTransferInfo(['id' => $auditId]);
            if (empty($jobTransferInfo)) {
                throw new \Exception('data err');
            }

            $needSendAuditFinishMessageFlag = $transferDetail->approval_state_stage_one == enums::APPROVAL_STATUS_APPROVAL &&
                $transferDetail->approval_state_stage_two == enums::APPROVAL_STATUS_PENDING &&
                $transferDetail->confirm_state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS &&
                $state == enums::APPROVAL_STATUS_APPROVAL || in_array($state, [
                    enums::APPROVAL_STATUS_REJECTED,
                    enums::APPROVAL_STATUS_TIMEOUT]);

            if ($needSendAuditFinishMessageFlag) {
                //推送文本内容
                $submitterLang              = (new StaffServer())->getLanguage($jobTransferInfo['submitter_id']);
                $messageData                = $this->getJobtransferInfo(['id' => $auditId]);
                $messageData['audit_state'] = $this->getTranslation($submitterLang)->_('audit_status.' . $state);
                JobTransferMessageServer::getInstance()->noticeAuditFinish($jobTransferInfo['submitter_id'],
                    $submitterLang,
                    $messageData,
                    JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE
                );
                $this->logger->write_log("pushAndSendMessageToSubmitter:转岗最终审批-" . $jobTransferInfo['submitter_id'],"info");
            }

            //固化转岗确认数据
            //待确认时才固化
            if (in_array($transferDetail->type, [JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE, JobTransferEnums::JOB_TRANSFER_TYPE_NOT_FRONT_LINE]) &&
                $transferDetail->approval_state_stage_one == enums::APPROVAL_STATUS_APPROVAL &&
                $transferDetail->approval_state_stage_two == enums::APPROVAL_STATUS_PENDING &&
                $transferDetail->confirm_state == JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM &&
                in_array($state, [enums::APPROVAL_STATUS_REJECTED, enums::APPROVAL_STATUS_TIMEOUT, enums::APPROVAL_STATUS_CANCEL])) {

                $server = $this->class_factory("JobTransferConfirmServer",$this->lang, $this->timeZone);
                $insertConfirmContent = $server->getPersistConfirmBaseInfo(['state' => $state], $transferDetail);

                $transferDetail->sign_url        = '';
                $transferDetail->confirm_date    = date('Y-m-d');
                $transferDetail->confirm_content = json_encode($insertConfirmContent, JSON_UNESCAPED_UNICODE);
                $transferDetail->save();
                $this->logger->write_log('setProperty save confirm_content ' . json_encode($insertConfirmContent, JSON_UNESCAPED_UNICODE), 'info');
            }
        }
    }
}