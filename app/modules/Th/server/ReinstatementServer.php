<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrOrganizationDepartmentPieceRelationModel;
use FlashExpress\bi\App\Models\backyard\HrOrganizationDepartmentRegionRelationModel;
use FlashExpress\bi\App\Models\backyard\HrOrganizationDepartmentStoreRelationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffNotRefundedSuspensionTaskModel;
use FlashExpress\bi\App\Models\backyard\ReinstatementRequestModel;
use FlashExpress\bi\App\Models\backyard\SuspensionManageLogModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditOptionRule;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\ReinstatementServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\WorkflowServer;

class ReinstatementServer extends GlobalBaseServer
{
    /**
     * 创建审批
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function addRequest($params): bool
    {
        $user = $params['user_info'];
        [$checkResult, $log, $staff_info] = $this->checkStaffApply($params['staff_info_id'], $user['staff_id']);

        //马来之能期望 今天和明天恢复
        if($params['expected_date'] < date('Y-m-d') || $params['expected_date'] > date('Y-m-d',strtotime('+1 day'))){
            throw new ValidationException($this->getTranslation()->_('reinstatement_date_notice'));
        }

        if ($checkResult){
            try {
                $this->getDI()->get('db')->begin();
                $staff_info_id = $params['staff_info_id'];
                $log_id = $log['id'];
                $this->updateReinstatementRequestOld($staff_info_id, $log_id);
                //保存申请数据
                $model = new ReinstatementRequestModel();
                $model->serial_no          = 'RN' . $this->getRandomId(); //申请编号
                $model->staff_info_id      = $staff_info_id; //恢复在职工号
                $model->origin_state       = HrStaffInfoModel::STATE_3; //暂时
                $model->reason             = $staff_info['stop_duty_reason']; //停职原因
                $model->reason_explanation = $params['reason_explanation']; //申请原因
                $model->stop_duties_date   = date('Y-m-d', strtotime($log['stop_duties_date'])); //停职日期
                $model->expected_date      = $params['expected_date'];
                $model->attach             = json_encode($params['attach']); //附件地址
                $model->ref_id             = $log_id; //关联停职记录id
                $model->state              = enums::APPROVAL_STATUS_PENDING; //审批状态
                $model->handled            = ReinstatementRequestModel::NOT_HANDLED; //是否脚本处理过
                $model->submitter_id       = $user['staff_id']; //提交人
                $extend['time_out'] = date('Y-m-d 00:00:00',strtotime('+3 day'));
                $model->time_out = $extend['time_out'];
                if (!$model->save()){
                    throw new InnerException("数据保存错误: 复职申请 ". json_encode($params));
                }

                //这里是 from 表单的内容,用于查找审批人
                $extend['from_submit'] = [
                    'sys_store_id'  => $staff_info['sys_store_id'], //恢复在职人网点
                    'department_id' => $staff_info['node_department_id'], //恢复在职人部门
                    'staff_info_id' => $staff_info['staff_info_id'], //恢复在职人工号
                ];

                //审批流
                $appServer = new ApprovalServer($this->lang, $this->timeZone);
                $app = $appServer->create($model->id, AuditListEnums::APPROVAL_TYPE_REINSTATEMENT, $user['staff_id'], null, $extend);
                if (!$app){
                    throw new InnerException("审批流创建错误：复职申请 ".json_encode($params));
                }
                $this->getDI()->get('db')->commit();
                return true;
            } catch (\Exception $e) {
                $this->getDI()->get('db')->rollback();
                $this->logger->write_log("复职申请提交失败：{$e->getMessage()}");
                return false;
            }
        }
        return false;
    }

    /**
     * 返回恢复在职工号信息
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function getStaffInfo($params): array
    {
        $staff_info_id = $params['staff_info_id'];
        $user = $params['user_info'];

        [$checkResult, $log, $staff_info] = $this->checkStaffApply($staff_info_id, $user['staff_id']);

        $suspension_log = $this->getSuspensionLogList($staff_info_id);

        //职位
        $job_title_info = (new HrJobTitleModel())->getOneById($staff_info['job_title']);
        //网点名称
        $store_info = (new SysStoreModel())->getOneStoreById($staff_info['sys_store_id']);
        //部门名称
        $department_name = (new DepartmentRepository())->getDepartmentNameById($staff_info['node_department_id']);

        $data = [
            'staff_info_id'         => $staff_info_id,
            'staff_name'            => $staff_info['name'],
            'job_title'             => $staff_info['job_title'],
            'job_title_name'        => $job_title_info['job_name'] ?? '',
            'store_id'              => $staff_info['sys_store_id'],
            'store_name'            => $store_info['name'] ?? '',
            'department_id'         => $staff_info['node_department_id'],
            'department_name'       => $department_name,
            'stop_duty_reason'      => $staff_info['stop_duty_reason'],
            'stop_duty_reason_text' => $this->getSuspensionReason()[$staff_info['stop_duty_reason']] ?? '',
            'stop_duties_date'      => date('Y-m-d', strtotime($staff_info['stop_duties_date'])),
            'suspension_log'        => $suspension_log,
        ];
        return $this->checkReturn(['data' => $data]);
    }

    /**
     * 详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     */
    public function getDetail(int $auditId, $user, $comeFrom): array
    {
        $request = $this->getRequestById($auditId);
        $head = [
            'title'      => (new AuditlistRepository($this->lang, $this->timeZone))->getAudityType(AuditListEnums::APPROVAL_TYPE_REINSTATEMENT),
            'id'         => $request->id,
            'staff_id'   => $request->staff_info_id,
            'type'       => AuditListEnums::APPROVAL_TYPE_REINSTATEMENT,
            'created_at' => DateHelper::utcToLocal($request->created_at),
            'updated_at' => DateHelper::utcToLocal($request->updated_at),
            'status'     => $request->state,
            'serial_no'  => $request->serial_no ?? '',
        ];
        $staff_info = (new StaffServer())->getStaffInfoById($request->staff_info_id);
        $ss = new SysStoreServer();
        $storeRegionPieceId = $ss->getStoreRegionPiece($staff_info['sys_store_id']);
        if (empty($storeRegionPieceId['manage_region'])) {
            $storeRegionPiece['manage_region'] = '';
        } else {
            $storeRegionName = $ss->getStoreRegionName($storeRegionPieceId['manage_region']);
            $storeRegionPiece['manage_region'] = $storeRegionName['manage_region'];
        }
        if (empty($storeRegionPieceId['manage_piece'])) {
            $storeRegionPiece['manage_piece'] = '';
        } else {
            $storePieceName = $ss->getStorePieceName($storeRegionPieceId['manage_piece']);
            $storeRegionPiece['manage_piece'] = $storePieceName['manage_piece'];
        }

        $submitter_info = (new StaffServer())->getStaffInfoById($request->submitter_id);

        $detail = [
            'apply_parson'              => sprintf('%s ( %s )', $submitter_info['staff_name'] ?? '', $submitter_info['staff_info_id'] ?? ''),
            'apply_department'          => sprintf('%s - %s', $submitter_info['department_name'] ?? '', $submitter_info['job_name'] ?? ''),
            'apply_reinstatement_staff' => sprintf('%s ( %s )', $staff_info['staff_name'] ?? '', $staff_info['staff_info_id'] ?? ''),
            'staff_store'               => $staff_info['store_name'] ?? '',
            'region_piece_name'         => $storeRegionPiece['manage_region'] . '-' . $storeRegionPiece['manage_piece'],
            'staff_job_title'           => $staff_info['job_name'] ?? '',
            'hire_type'                 => $this->getTranslation()->_('hire_type_' . $staff_info['hire_type']), // 雇佣类型,
            'stop_duties_date'          => $request->stop_duties_date,
            'stop_duty_reason'          => $this->getSuspensionReason()[$request->reason] ?? '',
            'reason_explanation'        => $request->reason_explanation,
            'expected_return_date'      => $request->expected_date ?? '',
            'attach'                    => json_decode($request->attach, true),
        ];

        $returnData['data']['suspension_log_list'] = $this->getSuspensionLogList($request->staff_info_id); //停职记录
        $returnData['data']['head']                = $head;
        $returnData['data']['detail']              = $this->format($detail);

        return $returnData;
    }

    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false, false, false, false, false, false);
    }

    /**
     * 验证停职恢复在职员工信息是否符合要求
     * @param $staffId
     * @param bool $checkDuplicate
     * @return array|void
     * @throws BusinessException
     */
    public function checkStaffApply($staffId, $submitter_id,$checkDuplicate = true)
    {
        $staff_info = (new StaffRepository($this->lang))->getStaffInfoByStaffId($staffId,
            'hsi.staff_info_id,hsi.name,hsi.sys_store_id,hsi.node_department_id,hsi.job_title,hsi.formal,hsi.is_sub_staff,hsi.state,hsi.stop_duties_date,hsi.stop_duty_reason');

        if(empty($staff_info)) {
            throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg1'));
        }

        //非编制、子账号不能申请恢复,提示：请输入正式员工工号
        if(!in_array($staff_info['formal'],[HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN]) || $staff_info['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg7'));
        }

        //只能给网点员工申请
        if($staff_info['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg14'));
        }

        //验证员工是否管辖范围内
        if(!$this->validateStaffJurisdiction($staffId, $submitter_id)) {
            throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg14'));
        }

        //员工状态为离职不可申请，提示：员工已离职，如需申请请线下申请再次入职
        if($staff_info['state'] == HrStaffInfoModel::STATE_RESIGN) {
            throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg8'));
        }

        //员工状态为在职不可申请，提示：员工已在职，不需要再申请
        if($staff_info['state'] == HrStaffInfoModel::STATE_ON_JOB) {
            throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg9'));
        }

        if($staff_info['state'] == HrStaffInfoModel::STATE_SUSPENSION) {
            $log = $this->getLatestSuspensionLog($staffId);
            if (!$log){
                throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg'));//没有停职记录
            }

            //员工状态为停职，停职原因不是未回公款、连续旷工，不可申请，提示：当前申请恢复在职功能只支持由于连续旷工和未回公款导致的停职，该员工的停职原因不符
            if(!in_array($staff_info['stop_duty_reason'], [HrStaffInfoModel::STOP_DUTY_REASON_ABSENTEEISM, HrStaffInfoModel::STOP_DUTY_REASON_NOT_REFUNDED])) {
                throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg10'));
            }

            //员工状态为停职，本次停职已有审批中、已通过状态的恢复在职申请，不可再次申请，提示：已为该员工申请过恢复在职，请勿重复申请
            $approveStatus = [
                enums::APPROVAL_STATUS_PENDING,
                enums::APPROVAL_STATUS_APPROVAL,
            ];
            $count = $this->getRequests($approveStatus,['ref_id'=>$log['id']])->count();
            if ($checkDuplicate && $count > 0){
                throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg11'));//已为该员工申请过恢复在职，请勿重复申请
            }

            //员工的停职原因为未回公款，当前时间超过停职日期的23:59不允许申请，提示：未回公款停职员工仅停职第一天可以申请，该员工不可申请，请联系LP
            $current_day = date('Y-m-d');
            if($staff_info['stop_duty_reason'] == HrStaffInfoModel::STOP_DUTY_REASON_NOT_REFUNDED && strtotime($current_day) > strtotime(date('Y-m-d', strtotime($staff_info['stop_duties_date'])))) {
                throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg12'));
            }

            //恢复在职检查ms
            $this->onJobCheekMs($staffId);
            return [true, $log, $staff_info];
        }
    }

    /**
     * 审批列表卡片
     * @inheritDoc
     */
    public function genSummary(int $auditId, $user)
    {
        $request = $this->getRequestById($auditId);
        $staff_info = (new StaffServer())->getStaffInfoById($request->staff_info_id);
        //特殊逻辑，计算超时时间
        $time_out = date('Y-m-d H:i:s', strtotime($request->created_at . '-1 day'));

        $data = [
            [
                'key' => 'reinstatement_staff',
                'value' => sprintf('%s ( %s )', $staff_info['staff_name'] ?? '', $staff_info['staff_info_id'] ?? ''),
            ],
            [
                'key' => 'staff_store',
                'value' => $staff_info['store_name']
            ],
            [
                'key' => 'staff_job_title',
                'value' => $staff_info['job_name'] ?? '',
            ],
            [
                'key' => 'stop_duty_reason',
                'value' => $request->reason,
            ],
            [
                'key' => 'created_at',
                'value' => $time_out,
            ]
        ];
        return $data;
    }

    /**
     * @inheritDoc
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal){
            $request = $this->getRequestById($auditId);
            if ($request){
                //更新审批状态
                $request->state = $state;
                $res = true;
                $staffServer = new StaffServer();
                $this->staffInfo =  $staffServer->getStaffInfoById($request->staff_info_id);
                //如果 非停职状态 不发消息 下面用
                if($this->staffInfo['state'] != enums::$service_status['suspension']){
                    $res = false;
                }
                if ($state == enums::APPROVAL_STATUS_APPROVAL){
                    $today = date('Y-m-d');
                    $request->effective_date = $request->expected_date;
                    //更新生效时间 https://flashexpress.feishu.cn/wiki/EwM4w5O1CiavztkQK0McVbKsnzh 审批通过立即生效 期望日期之前审批通过
                    if (strtotime($request->expected_date) <= strtotime($today)) {
                        $request->handled = ReinstatementRequestModel::HANDLED;
                        if ($res) {
                            $res = $this->reinstatement($request, $today);
                            if ($res !== false) {//停职状态 记录 实际恢复时间
                                //修改实际恢复时间
                                $request->effective_date = date('Y-m-d H:i:s');
                                [$result1, $result2] = $res;
                                $this->logger->write_log("恢复在职审批通过 立即生效 {$request->staff_info_id} {$result1},{$result2}",
                                    'info');
                            }
                        }
                    }
                }
                //驳回原因回写
                if($state != enums::APPROVAL_STATUS_APPROVAL){
                    $request->reject_reason = $extend['remark'] ?? '';
                }
                $request->save();
                //如果 已经是在职了 不发消息
                if($res === false){
                    return true;
                }
                //发送短信提醒
                $this->composeAndSendMsg($state,$request,$extend);
            }
        }
    }

    /**
     * 发送消息
     * @param $status
     * @param $request
     * @param $extend
     * @return void
     */
    public function composeAndSendMsg($status,$request, $extend)
    {
        $staffServer = new StaffServer();
        if(empty($this->staffInfo)){
            $this->staffInfo =  $staffServer->getStaffInfoById($request->staff_info_id);
        }
        if(empty($this->staffInfo)){
            return true;
        }
        switch ($status){
            case enums::APPROVAL_STATUS_APPROVAL:
                //审批通过处理；
                $effectiveDate = date('Y-m-d', strtotime($request->effective_date));
                //给员工发短信
                $msg_key = in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether) ? 'reinstatement_approve_sms_unpaid' : 'reinstatement_approve_sms';
                $this->sendSms([$this->staffInfo], $msg_key, ['date' => $effectiveDate], 'th');
                //给上级发消息
                $manager = $staffServer->getStaffMangerId($request->staff_info_id);
                if (!empty($manager)){
                    $manager = array_column($manager,'value');

                    $this->sendMessage($manager, 'reinstatement_approve_title_v2', 'reinstatement_approve_manager_msg_v2', [
                        'staff_name'    => $this->staffInfo['staff_name'],
                        'staff_info_id' => $this->staffInfo['staff_info_id']
                    ], [
                        'staff_name'    => $this->staffInfo['staff_name'],
                        'staff_info_id' => $this->staffInfo['staff_info_id'],
                        'dep_name'      => $this->staffInfo['department_name'],
                        'job_name'      => $this->staffInfo['job_name'],
                        'store_name'    => $this->staffInfo['store_name'],
                        'date'          => $effectiveDate,
                    ]);
                }

                //给HRBP发消息
                $hrbps = (new WorkflowServer($this->lang,$this->timeZone))->findHRBP($this->staffInfo['node_department_id'],['store_id'=>$this->staffInfo['sys_store_id']]);
                if ($hrbps){

                    $this->sendMessage(explode(',', $hrbps), 'reinstatement_approve_title_v2', 'reinstatement_approve_hrbp_msg_v2', [
                        'staff_name'    => $this->staffInfo['staff_name'],
                        'staff_info_id' => $this->staffInfo['staff_info_id']
                    ], [
                        'staff_name'    => $this->staffInfo['staff_name'],
                        'staff_info_id' => $this->staffInfo['staff_info_id'],
                        'dep_name'      => $this->staffInfo['department_name'],
                        'job_name'      => $this->staffInfo['job_name'],
                        'store_name'    => $this->staffInfo['store_name'],
                        'date'          => $effectiveDate,
                    ]);
                }

                break;
            case enums::APPROVAL_STATUS_REJECTED:
                //审批驳回处理 申请人-BY普通消息
                $this->sendMessage([$request->submitter_id], 'reinstatement_approve_rejected_title', 'reinstatement_approve_rejected_msg', [
                    'staff_name'    => $this->staffInfo['staff_name'],
                    'staff_info_id' => $this->staffInfo['staff_info_id']
                ], [
                    'staff_name'    => $this->staffInfo['staff_name'],
                    'staff_info_id' => $this->staffInfo['staff_info_id'],
                    'dep_name'      => $this->staffInfo['department_name'],
                    'job_name'      => $this->staffInfo['job_name'],
                    'store_name'    => $this->staffInfo['store_name'],
                    'reject_reason' => $extend['remark'] ?? ''
                ]);
                break;
            case enums::APPROVAL_STATUS_TIMEOUT:
                //审批超时处理 给申请人-BY普通消息
                $this->sendMessage([$request->submitter_id], 'reinstatement_approve_timeout_title', 'reinstatement_approve_timeout_msg', [
                    'staff_name'    => $this->staffInfo['staff_name'],
                    'staff_info_id' => $this->staffInfo['staff_info_id']
                ], [
                    'staff_name'    => $this->staffInfo['staff_name'],
                    'staff_info_id' => $this->staffInfo['staff_info_id'],
                    'dep_name'      => $this->staffInfo['department_name'],
                    'job_name'      => $this->staffInfo['job_name'],
                    'store_name'    => $this->staffInfo['store_name'],
                ]);
                break;
            default:
                break;
        }
    }

    /**
     * 审批流条件参数
     * @inheritDoc
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $request = $this->getRequestById($auditId);
        //停职原因
        $reason = $request ? $request->reason : 0;

        //获取员工信息
        $StaffRepository = new StaffRepository($this->lang);

        $columns = 'hsi.staff_info_id, group_concat(hsip.position_category) as position_category,hsi.job_title_grade_v2,hsi.node_department_id,hsi.sys_store_id,hsi.job_title,hsi.hire_type';
        $reportUser = $StaffRepository->getStaffInfoByStaffId($request->staff_info_id, $columns);

        $reportStoreInfo = HrStaffInfoServer::getStaffStoreBySysStoreId($reportUser['sys_store_id'], 'category');

        return [
            'reason'                            => $reason, //被申请人停职原因
            'request_department_id_from_submit' => $reportUser['node_department_id'], //被申请人所属部门
            'sys_store_id_from_submit'          => $reportUser['sys_store_id'], //被申请人所属网点
            'sys_store_category_from_submit'    => !empty($reportStoreInfo) ? $reportStoreInfo->category : 0, //被申请人所属网点类型
            'job_title_id_from_submit'          => $reportUser['job_title'], //被申请人职位
            'job_title_grade_v2_from_submit'    => $reportUser['job_title_grade_v2'], //被申请人职级
            'position_category_from_submit'     => !empty($reportUser['position_category']) ? explode(',', $reportUser['position_category']) : [],//被申请人角色
            'hire_type_from_submit'             => $reportUser['hire_type'], //被申请人雇佣类型
        ];
    }




    /**
     * 获取配置部门及子部门
     * @return array|false
     */
    public function getSettingEnvByDepartmentIds()
    {
        $permission_department_ids = [];
        $dep_env = (new SettingEnvServer())->getSetVal('reinstatement_department_list');
        if (empty($dep_env)) {
            return false;
        }
        $dep_env = explode(',', $dep_env);
        $department_model = new SysDepartmentModel();
        foreach ($dep_env as $departmentId) {
            $departmentIds = $department_model->getSpecifiedDeptAndSubDept($departmentId);
            $permission_department_ids = array_merge($permission_department_ids, $departmentIds);
        }
        return $permission_department_ids;
    }

    /**
     * 恢复在职入口权限
     * @param $paramIn
     * @return bool
     */
    public function getReinstatementPermission($paramIn): bool
    {
        //部门负责人/大区负责人/片区负责人/网点负责人/上级
        $staff_info_id = $paramIn['staff_id'];

        $permission_department_ids = $this->getSettingEnvByDepartmentIds();

        if($this->isStaffManagerByDepartment($staff_info_id, $permission_department_ids)) {
            return true;
        }

        //网点负责人
        $store_ids = $this->getStaffStoreManagerByDepartment($staff_info_id, $permission_department_ids);
        if(count($store_ids) > 0) {
            return true;
        }

        //片区负责人
        $piece_ids = $this->getStaffPieceManagerByDepartment($staff_info_id, $permission_department_ids);
        if(count($piece_ids) > 0) {
            return true;
        }

        //大区负责人
        $region_ids = $this->getStaffRegionManagerByDepartment($staff_info_id, $permission_department_ids);
        if(count($region_ids)) {
            return true;
        }

        //部门负责人
        $department_ids = $this->getStaffDepartmentManagerByDepartment($staff_info_id, $permission_department_ids);
        if(count($department_ids) > 0) {
            return true;
        }

        return false;
    }

    /**
     * 是否在指定部门下有下级员工
     * @param $staff_info_id
     * @param $department_ids
     * @return bool
     */
    public function isStaffManagerByDepartment($staff_info_id, $department_ids): bool
    {
        $staff_count = HrStaffInfoModel::count([
            'conditions' => 'formal IN ({formal:array}) and state IN ({state:array}) and is_sub_staff = :is_sub_staff: and node_department_id IN ({department_ids:array}) and manger = :manager_id:',
            'bind'       => [
                'formal'         => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                'state'          => [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPENSION],
                'is_sub_staff'   => HrStaffInfoModel::IS_SUB_STAFF_0,
                'department_ids' => $department_ids,
                'manager_id'     => $staff_info_id,
            ],
        ]);

        return $staff_count > 0;
    }

    /**
     * 是否指定部门下网点负责人
     * @param $staff_info_id
     * @param $department_ids
     * @return array
     */
    public function getStaffStoreManagerByDepartment($staff_info_id, $department_ids): array
    {
        $store_builder = $this->modelsManager->createBuilder();
        $store_builder->columns('relation.store_id');
        $store_builder->from(['relation' => HrOrganizationDepartmentStoreRelationModel::class]);
        $store_builder->leftjoin(SysStoreModel::class, 'relation.store_id = store.id', 'store');
        $store_builder->where('relation.state = 1 and relation.is_deleted = 0 and relation.level_state = 1');
        $store_builder->inWhere('relation.department_id', $department_ids);
        $store_builder->andWhere('store.manager_id = :manager_id:', ['manager_id' => $staff_info_id]);
        $store_info = $store_builder->getQuery()->execute()->toArray();
        return array_column($store_info, 'store_id');
    }

    /**
     * 是否指定部门下片区负责人
     * @param $staff_info_id
     * @param $department_ids
     * @return array
     */
    public function getStaffPieceManagerByDepartment($staff_info_id, $department_ids): array
    {
        $piece_builder = $this->modelsManager->createBuilder();
        $piece_builder->columns('relation.piece_id');
        $piece_builder->from(['relation' => HrOrganizationDepartmentPieceRelationModel::class]);
        $piece_builder->leftjoin(SysManagePieceModel::class, 'relation.piece_id = manage_piece.id', 'manage_piece');
        $piece_builder->where('relation.state = 1 and relation.is_deleted = 0 and relation.level_state = 1');
        $piece_builder->inWhere('relation.department_id', $department_ids);
        $piece_builder->andWhere('manage_piece.manager_id = :manager_id:', ['manager_id' => $staff_info_id]);
        $piece_info = $piece_builder->getQuery()->execute()->toArray();
        return array_column($piece_info, 'piece_id');
    }

    /**
     * 是否指定部门下负责大区
     * @param $staff_info_id
     * @param $department_ids
     * @return array
     */
    public function getStaffRegionManagerByDepartment($staff_info_id, $department_ids): array
    {
        $region_builder = $this->modelsManager->createBuilder();
        $region_builder->columns('relation.region_id');
        $region_builder->from(['relation' => HrOrganizationDepartmentRegionRelationModel::class]);
        $region_builder->leftjoin(SysManageRegionModel::class, 'relation.region_id = manage_region.id', 'manage_region');
        $region_builder->where('relation.state = 1 and relation.is_deleted = 0');
        $region_builder->inWhere('relation.department_id', $department_ids);
        $region_builder->andWhere('manage_region.manager_id = :manager_id:', ['manager_id' => $staff_info_id]);
        $region_info = $region_builder->getQuery()->execute()->toArray();
        return array_column($region_info, 'region_id');
    }

    /**
     * 获取员工负责部门
     * @param $staff_info_id
     * @param $department_ids
     * @return array
     */
    public function getStaffDepartmentManagerByDepartment($staff_info_id, $department_ids): array
    {
        $list = SysDepartmentModel::find([
            'conditions' => 'id in ({department_ids:array}) and manager_id = :manager_id: and deleted = 0',
            'bind'       => [
                'department_ids' => $department_ids,
                'manager_id'     => $staff_info_id,
            ],
        ])->toArray();
        return array_column($list, 'id');
    }

    /**
     * 查询工号未回款金额
     * @param $staff_info_id
     * @return int|mixed
     * @throws BusinessException
     */
    public function getStaffUnPayAmount($staff_info_id)
    {
        $un_pay_amount = 0;

        //查询是否有未回款金额
        $fau    = new RestClient('fau');
        $receivable_result = $fau->execute(RestClient::METHOD_GET, sprintf('/svc/staff/receivable/%s/info_unclear', $staff_info_id));
        if (isset($receivable_result['code']) && $receivable_result['code'] == 1) {
            $un_pay_amount += intval($receivable_result['data']['unPayAmount'] ?? 0);
        } else {
            throw new BusinessException($this->getTranslation()->t('suspended_reinstatement_error_1'));
        }

        //查询是否有未汇款金额
        $remittance_result = $fau->execute(RestClient::METHOD_GET, sprintf('/svc/staff/remittance/%s/info', $staff_info_id));
        if (isset($remittance_result['code']) && $remittance_result['code'] == 1) {
            $un_pay_amount += intval($remittance_result['data']['unPayAmount'] ?? 0);
        } else {
            throw new BusinessException($this->getTranslation()->t('suspended_reinstatement_error_1'));
        }

        return $un_pay_amount;
    }

    //验证提交工号是否在管辖范围内
    public function validateStaffJurisdiction($apply_staff_info_id, $submitter_id): bool
    {
        $department_ids = $this->getSettingEnvByDepartmentIds();
        [$jurisdiction_department_ids, $jurisdiction_store_ids] = $this->getStaffJurisdictionByDepartmentIds($submitter_id, $department_ids);

        if(!empty($jurisdiction_department_ids)) {
            //管辖部门内员工
            $count = HrStaffInfoModel::count([
                'conditions' => 'node_department_id IN ({department_ids:array}) and staff_info_id = :staff_info_id:',
                'bind'       => [
                    'department_ids' => $jurisdiction_department_ids,
                    'staff_info_id'  => $apply_staff_info_id
                ],
            ]);
            if($count > 0) {
                return true;
            }
        }

        //管辖大区/片区/网点员工
        if(!empty($jurisdiction_store_ids)) {
            $count = HrStaffInfoModel::count([
                'conditions' => 'sys_store_id IN ({sys_store_ids:array}) and staff_info_id = :staff_info_id:',
                'bind'       => [
                    'sys_store_ids' => $jurisdiction_store_ids,
                    'staff_info_id'  => $apply_staff_info_id
                ],
            ]);
            if($count > 0) {
                return true;
            }
        }

        //验证是否下级员工
        $count = HrStaffInfoModel::count([
            'conditions' => 'node_department_id IN ({department_ids:array}) and manger = :manager_id: and staff_info_id = :staff_info_id:',
            'bind'       => [
                'department_ids' => $department_ids,
                'manager_id'     => $submitter_id,
                'staff_info_id'  => $apply_staff_info_id
            ],
        ]);
        if($count > 0) {
            return true;
        }

        return false;
    }

    /**
     * 获取指定工号指定部门内的管辖范围
     * @param $staff_info_id
     * @param $department_ids
     * @return array
     */
    public function getStaffJurisdictionByDepartmentIds($staff_info_id, $department_ids): array
    {
        $jurisdiction_department_ids = $this->getStaffDepartmentManagerByDepartment($staff_info_id, $department_ids);

        $jurisdiction_region_ids = $this->getStaffRegionManagerByDepartment($staff_info_id, $department_ids);

        $jurisdiction_piece_ids = $this->getStaffPieceManagerByDepartment($staff_info_id, $department_ids);

        $jurisdiction_store_ids = $this->getStaffStoreManagerByDepartment($staff_info_id, $department_ids);

        $region_store_list = $this->getManageOrganizationByRelationInfo($jurisdiction_region_ids, HrOrganizationDepartmentStoreRelationModel::MANAGE_ORG_TYPE_REGION);
        $region_store_ids = array_column($region_store_list, 'store_id');

        $piece_store_list = $this->getManageOrganizationByRelationInfo($jurisdiction_piece_ids, HrOrganizationDepartmentStoreRelationModel::MANAGE_ORG_TYPE_PIECE);
        $piece_store_ids = array_column($piece_store_list, 'store_id');

        $jurisdiction_store_ids = array_values(array_unique(array_merge($region_store_ids, $piece_store_ids, $jurisdiction_store_ids)));
        return [$jurisdiction_department_ids, $jurisdiction_store_ids];
    }

    /**
     * @description 查询部门关联的网点
     * @param $items
     * @param $type
     * @return array
     */
    public function getManageOrganizationByRelationInfo($items, $type): array
    {
        if (empty($items)) {
            return [];
        }

        $bind = [
            'deleted'        => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
            'state'          => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
            'level_state'    => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            'items_ids'      => $items
        ];
        $conditions = 'is_deleted = :deleted: and state = :state: and level_state = :level_state: and ';

        switch ($type) {
            case HrOrganizationDepartmentStoreRelationModel::MANAGE_ORG_TYPE_DEPARTMENT:
                $conditions .= 'department_id in ({items_ids:array})';
                break;
            case HrOrganizationDepartmentStoreRelationModel::MANAGE_ORG_TYPE_REGION:
                $conditions .= 'region_id in ({items_ids:array})';
                break;
            case HrOrganizationDepartmentStoreRelationModel::MANAGE_ORG_TYPE_PIECE:
                $conditions .= 'piece_id in ({items_ids:array})';
                break;
            default:
                $conditions .= 'store_id in ({items_ids:array})';
                break;
        }

        return HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
    }

    /**
     * 停职恢复在职 停职原因是旷工/未回公款，
     * 向恢复在职员工本人的“个人号码”发送手机短信（泰语）
     * 向恢复在职员工直线上级、对应HRBP发送BY/KIT-Push通知（根据用户登录语言）
     * @param $staff
     * @return bool
     */
    public function suspendedReinstatementSendMsg($staff): bool
    {
        $staff_info_id = $staff['staff_info_id'];
        $manager_id    = $staff['manager_id'];

        //向恢复在职员工本人的“个人号码”发送手机短信（泰语）
        $this->sendSms([$staff], 'suspended_reinstatement_staff_send_sms', ['staff_info_id' => $staff_info_id], 'th');

        //向恢复在职员工直线上级、对应HRBP发送BY/KIT-Push通知（根据用户登录语言）
        $hrbps = (new WorkflowServer($this->lang, $this->timeZone))->findHRBP($staff['node_department_id'], ['store_id' => $staff['sys_store_id']]);

        $send_push_staffs[] = $manager_id;
        if ($hrbps) {
            $hrbps_arr = explode(',', $hrbps);
            $send_push_staffs = array_merge($hrbps_arr, $send_push_staffs);
        }

        $this->sendPush([
            'staffs'              => $send_push_staffs,
            'title_key'           => 'suspended_reinstatement_push_title',
            'title_placeholder'   => [],
            'content_key'         => 'suspended_reinstatement_push_content',
            'content_placeholder' => ['staff_info_id' => $staff_info_id, 'staff_name' => $staff['name']],
        ]);

        return true;
    }

}