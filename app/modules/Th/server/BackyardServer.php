<?php
/**
 * Created by PhpStorm.
 * User: mxk
 * Date: 2022/05/07
 * Time: 下午2:51
 */
namespace FlashExpress\bi\App\Modules\Th\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Models\backyard\MessageWarningTransferSignModel;
use FlashExpress\bi\App\Server\BackyardServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\ExtinguisherServer;
use FlashExpress\bi\App\Server\MessageServer;
use FlashExpress\bi\App\Server\KpiServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\ToolServer;

class BackyardServer extends GlobalBaseServer{

    /**
     * 获取红点数量
     * 现状：泰国重写此方法，新增字段un_asset_manager_num，un_mykpi_num
     * @param $req
     * @return string[]
     */
    public function getRedDotsNum($req){
        //CEO回复未读 & 消息未读
        $unReadMsgRes = $this->un_read(['staff_id' => $req['staff_id']]);
        $ceoUnReadNum = $unReadMsgRes['ceo_unread'] ?? 0;
        $msgUnReadNum = $unReadMsgRes['msg_unread'] ?? 0;

        //审批
        $unAuditNumRes = $this->getWaitAuditData($req);
        $unAuditNum = $unAuditNumRes['un_audit_num'] ?? 0;
        $onlyUnAuditNum = $unAuditNumRes['waitAuditNum'] ?? 0;

        //个人资产
        //$unMyAssetNum = $this->getMyAssetNum($req['staff_id']);

        //公共资产
        $unPubassetNum = $this->getPubAssetNum($req['staff_id']);
        //公共资产-灭火器检查数量
        $extinguisherCheckNum = $this->getExtinguisherCheckNum($req);
        $unPubassetNum += $extinguisherCheckNum;

        //身份证照片小红点
        $myinfoCount = 0;
        if($this->redCountCompareMobileVersion()) {
            $tool_server = Tools::reBuildCountryInstance(new ToolServer($this->lang, $this->timezone),[$this->lang, $this->timezone]);
            $myinfoCount = $tool_server->getPersonalInformationRedCount($req['staff_id']);
        } else {
            $myinfoCount = $this->getMyInfoCount($req['staff_id']);
        }

        //资产管理小红点
        $unAssetManagementNum = $this->getAssetManagementNum($req['staff_id']);

        $return = [
            'un_read_num' => (string)$ceoUnReadNum,//我的-CEO信箱
            'backyard_unread_num' => (string)$msgUnReadNum,//消息tab
            'un_audit_num' => (string)$unAuditNum,//审批tab
            "un_only_audit_num" => (string)$onlyUnAuditNum,//首页弹出提示
            "un_pubasset_num" => (string)$unPubassetNum,//我的-公共资产
            "un_asset_manager_num" => (string)$extinguisherCheckNum,//灭火器检查数量
            "un_myinfo_count" => (string)$myinfoCount,//我的-个人信息
            "un_asset_management_num" => (string)$unAssetManagementNum,//我的-资产管理
            "un_myabout_all" => (string)($unPubassetNum + $ceoUnReadNum + $myinfoCount + $unAssetManagementNum),//我的tab （总和）
        ];
        return $return;
    }

    /**
     * 灭火器待检查数量
     * @param $paramIn
     * @return int
     */
    public function getExtinguisherCheckNum($paramIn){
        $service = new ExtinguisherServer($this->lang,$this->timezone);
        $notCheckNumber = $service->getPubAssetsList($paramIn);
        $number = 0;
        if(isset($notCheckNumber['extinguisher_check']) && $notCheckNumber['extinguisher_check']) {
            $number = $notCheckNumber['number'];
        }
        return $number;
    }

    /**
     * 处理警告书消息详情
     * @param $data
     * @param $warning_info
     * @param $msg_id
     * @param $link
     * @return mixed
     */
    public function warningMessageDetail($data, $warning_info, $msg_id, $link)
    {
        $state = 0;
        $url = env('h5_endpoint').'warn-letter-message?msg_id='.$msg_id;
        if($warning_info['is_need_sign'] == false) {//不需要签字的置为已读
            // 消息已读
            $this->has_read_operation($msg_id,true);
            $this->addHaveReadMsgToMns($msg_id);
            // 重置消息内容
            $url .= '&state='.$state;
            // 客户端优先加载url 否则加载content
            $data['msg_detail_url'] = $url;
            $data['must_submit'] = 1;
            $data['content'] = '<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" /><div style="position: fixed; left: 0; top: 0; width: 100%; height: 100%"><iframe src="'.$url.'" width="100%" height="100%" frameborder="0"></iframe></div>';
            return $data;
        }
        if ($warning_info['role'] == MessageServer::MSG_STAFF_TYPE_STAFF && !$warning_info['img_url']) {
            $state = 1;
        } elseif ($warning_info['role'] == MessageServer::MSG_STAFF_TYPE_SUPERIOR && !$warning_info['superior_img']) {

            // 获取警告书签字天数限制-非警告系统
            $setting_env                           = new SettingEnvServer();
            $no_warning_system_limitation_days     = $setting_env->getSetVal('no_warning_system_limitation_days');
            $no_warning_system_limitation_days_arr = json_decode($no_warning_system_limitation_days, true);
            // 获取警告书签字天数限制-警告系统
            $warning_system_limitation_days        = $setting_env->getSetVal('warning_system_limitation_days');
            $warning_system_limitation_days_arr    = json_decode($warning_system_limitation_days, true);

            if (empty($warning_info['staff_warning_message_id'])){
                // 非警告系统发出「HCM-快捷工具-电子警告书-发送警告书」
                $staff_day = $no_warning_system_limitation_days_arr['staff'];
            }else{
                // 警告系统发出「HCM-警告系统-违规行为复核-发送警告书」
                $staff_day = $warning_system_limitation_days_arr['staff'];
            }

            if (($warning_info['img_url'] || gmdate("Y-m-d", strtotime($warning_info['created_at']." + ".$staff_day." days")) > gmdate("Y-m-d"))) {
                // 已签字 或者 消息下发时间在三天内
                $state = 2;
            } else {
                $jobTile         = $this->getDI()->get("db_rby")->fetchOne("select hr_job_title.* from hr_job_title left join hr_staff_info on hr_staff_info.job_title = hr_job_title.id where hr_staff_info.staff_info_id = ".$warning_info['superior_id'],
                    \Phalcon\Db::FETCH_ASSOC);
                $jobTile         = ($jobTile && isset($jobTile['job_name'])) ? $jobTile['job_name'] : '';
                if($warning_info['level'] == MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR) {
                    $identity = $this->getTranslation()->_('superior');

                } elseif($warning_info['level'] == MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR_SUPERIOR) {
                    $identity = $this->getTranslation()->_('superior_superior');

                } else {
                    $identity = $this->getTranslation()->_('hrbp');

                }
                $state = 4;
                $url = env('h5_endpoint').'warn-letter-message?msg_id='.$msg_id.'&name='.$warning_info['superior_name'].'&job_name='.$jobTile.'&day='.date("Y-m-d").'&identity='.$identity;
            }
        } elseif (
            $warning_info['role'] == MessageServer::MSG_STAFF_TYPE_WITNESS_1 && !$warning_info['witness1_img']
            ||
            $warning_info['role'] == MessageServer::MSG_STAFF_TYPE_WITNESS_2 && !$warning_info['witness2_img']
        ) {
            $state = 3;
        }
        // 重置消息内容
        $url .= '&state='.$state;
        $data['content'] = '<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" /><div style="position: fixed; left: 0; top: 0; width: 100%; height: 100%"><iframe src="'.$url.'" width="100%" height="100%" frameborder="0"></iframe></div>';
        // 客户端优先加载url 否则加载content
        $data['msg_detail_url']  = $url;
        $data['must_submit'] = 1;
        return $data;
    }
}
