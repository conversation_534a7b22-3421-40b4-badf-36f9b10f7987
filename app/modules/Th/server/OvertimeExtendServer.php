<?php
/**
 * Created by PhpStor<PERSON>.
 * User: nick
 * Date: 11/3/21
 * Time: 8:01 PM
 */

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\AuditPermissionModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\OvertimeHourSettingDetailModel;
use FlashExpress\bi\App\Models\backyard\OvertimeOneMonthHourSettingModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StatisticForOvertimeRepository;
use FlashExpress\bi\App\Server\OvertimeExtendServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel as byDepartment;

use FlashExpress\bi\App\Server\AuditServer;

class OvertimeExtendServer extends GlobalBaseServer{

    public $hour = 10;
    public $ffmHour = 9.5;
    public $ffmHour2 = 9;
    public $nwSpecialJob;

    public function getStaffInfo($staffId){
        if(empty($this->staffInfo)){
            return (new StaffRepository())->getStaffPosition($staffId);
        }
        return $this->staffInfo;
    }


    public function check_permission($staff_id, $type_list = [])
    {
        $default_v = 'x';//默认皮配值 前提条件是 其他匹配值没有x
        if (empty($staff_id)) {
            return [];
        }
        //获取员工信息
        $staff_re   = new StaffRepository($this->lang);
        $staff_info = $staff_re->getStaffPosition($staff_id);
        if (empty($staff_info)) {
            return [];
        }
        if (empty($staff_info['node_department_id'])) {
            return [];
        }


        //获取所属公司id
        $dep_info = byDepartment::findFirst("id = {$staff_info['node_department_id']} and deleted = 0");
        if (empty($dep_info)) {
            return [];
        }
        //调试
//        $dep_info->ancestry_v3 = '123/222/1/4/32';
//        $dep_info->ancestry_v3 = '999/2';
        $ancestry = explode('/', $dep_info->ancestry_v3);
        if ($ancestry[0] != '999')//错误组织架构数据
        {
            return [];
        }

        //补全5个层级
        $ancestry[0] = empty($ancestry[0]) ? $default_v : $ancestry[0];
        $ancestry[1] = empty($ancestry[1]) ? $default_v : $ancestry[1];
        $ancestry[2] = empty($ancestry[2]) ? $default_v : $ancestry[2];
        $ancestry[3] = empty($ancestry[3]) ? $default_v : $ancestry[3];
        $ancestry[4] = empty($ancestry[4]) ? $default_v : $ancestry[4];


        //所有配置权限记录
        $module_id       = AuditPermissionModel::MODULE_P_1;
        $version         = 'ot_2';
        $permission_list = AuditPermissionModel::get_permission($module_id, $version);
        if (empty($permission_list))//没有配置对应公司限制条件 不在限制公司之内 都可申请
        {
            if (empty($type_list)) {
                return true;
            }

            return $type_list;
        }

        //获取对应 限制条件的最终值
        $format = [];
        foreach ($permission_list as $v) {//组装 key => value
            $l1         = empty($v['ancestry_1']) ? "{$default_v}" : "{$v['ancestry_1']}";
            $l2         = empty($v['ancestry_2']) ? "_{$default_v}" : "_{$v['ancestry_2']}";
            $l3         = empty($v['ancestry_3']) ? "_{$default_v}" : "_{$v['ancestry_3']}";
            $l4         = empty($v['ancestry_4']) ? "_{$default_v}" : "_{$v['ancestry_4']}";
            $l5         = empty($v['ancestry_5']) ? "_{$default_v}" : "_{$v['ancestry_5']}";
            $job_id     = empty($v['job_title_id']) ? "_{$default_v}" : "_{$v['job_title_id']}";
            $grade      = empty($v['job_title_grade']) ? "_{$default_v}" : "_{$v['job_title_grade']}";
            $k          = $l1.$l2.$l3.$l4.$l5.$job_id.$grade;
            $format[$k] = json_decode($v['permission_value']);
        }


        //排列组合 当前员工 的key
        //def 为 默认key
        $combination_arr = [
            [$ancestry[0], "{$default_v}"],
            ['_'.$ancestry[1], "_{$default_v}"],
            ['_'.$ancestry[2], "_{$default_v}"],
            ['_'.$ancestry[3], "_{$default_v}"],
            ['_'.$ancestry[4], "_{$default_v}"],
            ['_'.$staff_info['job_title'], "_{$default_v}"],//职位
            ['_'.$staff_info['job_title_grade_v2'], "_{$default_v}"],//职级

        ];
        $staff_key_arr   = combination($combination_arr);
        $staff_key_arr   = array_unique($staff_key_arr[0]);
        //获取 匹配上的 并且 默认值数量最少的
        $correct_k   = [];
        $num         = count($combination_arr) + 1;//默认 x 数量 用x 拆分 num 为数量+1
        $final_value = [];//最终返回的 权限值
        if (!empty($staff_key_arr)) {
            //获取所有匹配上的key
            foreach ($format as $key => $val) {
                if (in_array($key, $staff_key_arr)) {
                    $correct_k[] = $key;
                }
            }
            $cks = [];
            $k   = '';
            foreach ($correct_k as $ck => $item) {
                $n = explode($default_v, $item);
                if ($num > count($n)) {
                    $final_value = $item;
                    $num         = count($n);
                    $k           = $ck;
                } else {
                    if ($num == count($n)) {
                        $cks[] = $ck;
                        $cks[] = $k;
                    }
                }
            }
            //看数量相同的 是不是只有一个
            if (!empty($cks)) {
                $check_correct = [];
                foreach ($cks as $ck) {
                    $check_correct[] = $correct_k[$ck];
                }
                $this->logger->write_log("ot_check_permission {$staff_id} ".json_encode($correct_k), 'info');
                $same_value = get_final_key($check_correct, $default_v);
                //相同数的 和最终key 比对 default的 数量  取默认值最少的
                $final_value = count(explode($default_v, $final_value)) > count(explode($default_v,
                    $same_value)) ? $final_value : $same_value;
            }
        }
        $this->logger->write_log("ot_check_permission {$staff_id} final_value {$final_value} end", 'info');
        if (empty($final_value))//一个都没匹配上 返回空
        {
            return [];
        }
        $final_value = $format[$final_value];

        if (empty($type_list)) {
            //入口按钮权限判断 返回bool
            return empty($final_value) ? false : true;
        }
        $return = [];
        $data   = array_column($type_list, null, 'code');
        foreach ($final_value as $type) {
            $return[] = $data[$type];
        }
        return $return;
    }


    //新增的逻辑验证
    public function extend_check($param, $user_info)
    {
        $config_hour = $this->config->application->add_hour;
        $staff_id    = $user_info['staff_info_id'];
        $date        = $param['date_at'];
        //网点类型为HUB、B-HUB以及OS的员工  推9小时 其他 推10小时
        //os  后来修改为 os 网点类型  8
        $add_hour = $this->hour;
        $ffm_add_2 = $this->ffmHour2;
        //网点类型 hub  8  b-hub 12
        $limit_store = [enums::$stores_category['hub'], enums::$stores_category['os'], enums::$stores_category['bhub']];
        //网点员工 并且特殊配置网点 少半小时
        if ($user_info['organization_type'] == HrStaffInfoModel::ORGANIZATION_STORE) {
            $staff_re   = new StaffRepository($this->lang);
            $store_info = $staff_re->getStaffStoreInfo($user_info['sys_store_id']);
            if (!empty($store_info) && in_array($store_info['category'], $limit_store)) {
                $add_hour = bcsub($this->hour, 0.5, 1);
            }
        }
        //新增需求 ffm 公司的人 也是 9。5 小时 https://flashexpress.feishu.cn/docx/doxcnxBtAziWG4tag6f0TzgxpHg
        //新增需求  ffm https://flashexpress.feishu.cn/docx/FmF6dI114oplvMxq5gccDubPn0U
        $audit_server = new AuditServer($this->lang, $this->timezone);
        $ffm_flag    = $audit_server->isFullDepartment($staff_id);
        $limitJobs = [];
        if ($ffm_flag) {//9.5
            $add_hour = $this->ffmHour;
            $limitJobs = (new SettingEnvServer())->getSetVal('ffm_norest_ot_position', ',');
        }
        // 检查是否为特殊员工：FFM部门 + 特定职位(300、1301、917) key
        $is_special_staff = $ffm_flag && in_array($user_info['job_title'], $limitJobs);

        //获取请假记录 如果加班日当天 存在下午请假
        $leave_re   = new AuditRepository($this->lang);
        $leave_info = $leave_re->get_leave_date($staff_id, $date, $date);
        $is_leave   = false;
        //如果是请上午假 加5小时 其他情况不让申请ot 休息日类型假期 剔除
        if (!empty($leave_info) && $leave_info[0]['leave_type'] != enums::LEAVE_TYPE_15) {
            if ($leave_info[0]['type'] != StaffAuditLeaveSplitModel::SPLIT_TYPE_1) {
                throw new ValidationException($this->getTranslation()->_('overtime_leave_limit'));
            }
            $is_leave = true;
        }

        //没有班次信息 不让申请
        if (empty($param['shift_info']) || empty($param['shift_info']['start'])) {
            throw new ValidationException($this->getTranslation()->_('no_shift_notice'));
        }
        $this->shiftInfo = $param['shift_info'];

        //如果 没打上班卡 不让申请
        $att_info = StaffWorkAttendanceModel::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
        if (empty($att_info)) {
            //没上班卡 判断是否出差 取出差打卡 上班卡信息
            $att_info = StaffAuditReissueForBusinessModel::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
            if (empty($att_info)) {
                throw new ValidationException($this->getTranslation()->_('overtime_att_start'));
            }
            $att_info               = $att_info->toArray();
            $att_info['started_at'] = $att_info['start_time'];
        } else {
            $att_info = $att_info->toArray();
        }

        if (empty($att_info['started_at'])) {
            throw new ValidationException($this->getTranslation()->_('overtime_att_start'));
        }
        //如果是工具 下面不验证了
        if (!empty($param['is_bi'])) {
            return true;
        }

        $start = $this->shiftInfo['start'];
        //去秒数
        $att_info['started_at'] = date('Y-m-d H:i:00', strtotime("{$att_info['started_at']}"));
        //跟班次比对 如果是迟到 加班开始时间 应该在 迟到小时+1小时 时间整点
        $shift_start_time = strtotime("{$date} {$start}");
        $card_time        = strtotime($att_info['started_at']) - $this->second + ($config_hour * 3600);

        //如果请假 要减5小时
        if($is_leave){
            $ffm_add_2 -= 5;
            $add_hour -= 5;
        }
        //用户选的加班开始
        $ot_start_time = date('Y-m-d H:i:s', strtotime($param['start_time']));
        //没迟到  取班次时间整点 加对应的小时数
        $limit_start = $this->getLimitStart($card_time, $shift_start_time, $add_hour);

        $l1 = date('Y-m-d H:i:s', $shift_start_time);
        $l2 = date('Y-m-d H:i:s', $card_time);
        $this->logger->write_log("{$staff_id} add_hour {$add_hour},shift_start_time {$l1},card_time(-59) {$l2},limit_start {$limit_start},ot_start_time {$ot_start_time},is_special_staff {$is_special_staff} ", 'info');

        //如果 ffm 并且定制职位 再取个 +9小时的  限制开始和 ot 开始 差值 小于9小时 提示错误，如果等于9小时 时长只能选1小时 如果 大于等于9.5小时 跳过
        if($is_special_staff){
            $limit_start_9 = $this->getLimitStart($card_time, $shift_start_time, $ffm_add_2);
            $this->logger->write_log(['ffm_special_staff' => $limit_start_9], 'info');
            if ($ot_start_time == $limit_start_9 && $param['duration'] != 1) {
                throw new ValidationException($this->getTranslation()->_('22466_overtime_forbidden_one_hour'));
            }
            //如果是 9.5的 不能有1小时
            if ($ot_start_time >= $limit_start && $param['duration'] == 1) {
                throw new ValidationException($this->getTranslation()->_('22466_duration_one_hour_error'));
            }
            $limit_start = $limit_start_9;
        }

        //工具申请 不限制这块 不知道为啥  产品-- 刘佳雪
        if ($ot_start_time < $limit_start) {
            throw new ValidationException($this->getTranslation()->_('overtime_forbidden'));
        }
        return true;
    }

    public function getLimitStart($card_time, $shift_start_time, $add_hour)
    {
        $limit_start = date('Y-m-d H:i:s', $shift_start_time + ($add_hour * 3600));
        if ($card_time > $shift_start_time) {//如果迟到 取打卡时间 加1小时 再加上对应的小时数
            $shift_i     = date("i", $shift_start_time);//取班次的分钟
            $limit_start = date("Y-m-d H:{$shift_i}:00", $card_time + 3600);
            $limit_start = date('Y-m-d H:i:s', strtotime($limit_start) + ($add_hour * 3600));
        }
        return $limit_start;
    }

    public function check_ot_record($staffId, $date, $start_time, $end_time, $type)
    {
        $start_time = date('Y-m-d H:i:s', $start_time);
        $end_time   = date('Y-m-d H:i:s', $end_time);
        //检查是否有重复记录
        $type_arr = [1, 2, 4, 5, 6];
        if (in_array($type, [2, 4, 5, 6])) {
            $type_arr = array_merge([1], [$type]);
        }
        //所有的1倍加班互斥 并且和 1。5倍互斥
        if(in_array($type,[4,5,6])){
            $type_arr = [1,4,5,6];
        }
        $exist = HrOvertimeModel::find(
            [
                'conditions' => "state in (1,2) and staff_id = {$staffId}  and date_at = '{$date}' and type in({type_arr:array})",
                'bind'       => [
                    'type_arr' => $type_arr,
                ],
                'columns'    => 'overtime_id',
            ]
        )->toArray();
        if (!empty($exist)) {
            throw new ValidationException($this->getTranslation()->_('5102'));
        }

        if (!in_array($type, [2, 4, 5, 6]))//1。5倍ot 下面不需要校验
        {
            return true;
        }
        //是否存在交集
        $where = "state in (1,2) and staff_id = {$staffId}  and date_at = '{$date}' 
                          and (
                            (start_time < '{$start_time}' and end_time > '{$start_time}') or 
                            (start_time < '{$end_time}' and end_time > '{$end_time}') or 
                            (start_time < '{$start_time}' and end_time > '{$end_time}') or 
                            (start_time > '{$start_time}' and end_time < '{$end_time}') 
                          )";

        if ($type == HrOvertimeModel::OVERTIME_2) {
            $where .= " and type in (4,5,6)";
        } elseif (in_array($type,[HrOvertimeModel::OVERTIME_4,HrOvertimeModel::OVERTIME_5,HrOvertimeModel::OVERTIME_6])) {
            $where .= " and type = 2";
        }
        $check = HrOvertimeModel::find([
            'conditions' => $where,
            'columns'    => 'overtime_id',
        ])->toArray();
        $this->logger->write_log("ot_check_exist {$staffId} start_time {$start_time},end_time {$end_time} ", 'info');

        if (!empty($check)) {
            throw new ValidationException($this->getTranslation()->_('5102'));
        }

        return true;
    }


    /**
     *
     *
     *  'category'         => $references['store_category'] ?? 0,
    'department_id'         => $references['sys_department_id'] ?? 0,
    'node_department_id'    => isset($references['node_department_id']) ? $references['node_department_id'] : 0,
    'k'                     => $references['all_effective_num'] ?? 0,
    'k1'                    => $references['store_effective_num'] ?? 0,
    'duration'              => $references['duration'] ?? 0,
    'type'                  => $detail['type'],
    'job_title'             => $references['job_title'] ?? 0,
    'organization_type'             => $references['organization_type'] ?? 0,
    'company_id'             => $references['company_id'] ?? 0,
    'ot_date' => 'date_at',//表单填写的加班日期
    'staff_info_id' => $detail['staff_id'],//申请人 工号要获取对应的ph
    'dc_in_all_hours' => $references['dc_in_all_hours'] ?? 0,//当前网点 dc 已经申请的小数
    'dc_should_hours' => $references['dc_should_hours'] ?? 0, //当前网点 申请时候 应有的小时预算
    'ph_num' => $references['dc_should_hours'] ?? 0, //申请当月 有多少个 ph
     * 获取依赖数据
     * 获取 审批流 code
     * @param $staffId
     * @param $type
     * @param $date
     * @return array
     * @throws \Exception
     */
    public function getReference($staffId, $param)
    {
        $type = $param['type'];
        $date = $param['date_at'];
        //固化数据
        $staffInfo = $this->staffInfo = $this->getStaffInfo($staffId);
        $extend['flow_code'] = enums::FLOW_CODE_OT;

        //获取网点信息
        $storeInfo = false;
        if($staffInfo['organization_type'] == 1){
            $storeInfo = SysStoreModel::findFirst([
                'conditions' => "id = :store_id:",
                'bind' => [
                    'store_id'  => $staffInfo['sys_store_id'],
                ],
            ]);
        }

        //是否是 子公司
        $audit_server = new AuditServer($this->lang,$this->timezone);
        $pay_flag = $audit_server->isFullDepartment($staffId,'999/222/60001');
        $money_flag = $audit_server->isFullDepartment($staffId,'999/222/30001');

        //架构调整 新需求 https://flashexpress.feishu.cn/docx/Lx7pd6oO3o9TMix6WVLcRxt6nde
        $is_bulky = $audit_server->isBelongByAncestry($staffId,enums::NETWORK_BULKY_AREA);
        $is_pcc = $audit_server->isBelongByAncestry($staffId,enums::NETWORK_PICKUP_CONTROL_CENTER);
        //新版 shop 详情标记
        $shop_flag = $audit_server->isBelongByAncestry($staffId,enums::SALES_CRM_ACCESS_SHOP_DEPARTMENT_ID);

        $references = $detailData = array();
        if (in_array($staffInfo['sys_department_id'],[$this->configData['dept_network_management_id']]) || $is_bulky || $is_pcc) {
            $extend['flow_code'] = enums::FLOW_CODE_OT_SUB;//定制 加班 子审批流 这两个部门 所有人都走新审批

            if(in_array($staffInfo['job_title'],$this->nwSpecialJob)){
                $statistic_type = enums::OT_STATISTIC_TYPE_1;
                if ($is_bulky || $is_pcc) {
                    $statistic_type = enums::OT_STATISTIC_TYPE_3;
                }

                // network management 部门  DC Officer 职位
                $references             = $this->get_effective($staffInfo['sys_store_id'], $staffInfo, $date, $type,
                    $date, $statistic_type);
                $references['duration'] = $this->getMonthDuration($staffId, $date, $type);
                $store_name             = $this->getStoreDetailName($staffInfo['sys_store_id']);

                $references               = array_merge($references, [
                    'sys_department_id'  => $staffInfo['sys_department_id'],
                    'node_department_id' => $staffInfo['node_department_id'],
                    'job_title'          => $staffInfo['job_title'],
                    'store_name'         => $store_name ?? '',
                ]);
                $references['ph_num']     = $this->get_ph_num($staffInfo, $date);
                $detailData['store_name'] = $store_name ?? '';
            }

            //新需求 这两个部门 部分职位 只需要获取时长 用于 走审批流 不做展示 https://flashexpress.feishu.cn/docx/doxcn635y5rMuLlchwXtVHISmwE
            if($staffInfo['job_title'] == enums::$job_title['branch_supervisor'] || in_array($staffInfo['job_title'],$this->configData['ot_s1_job_ids'])){
                $references['duration'] = $this->getMonthDuration($staffId, $date, $type);
                $references['ph_num'] = $this->get_ph_num($staffInfo,$date);

            }

            //新需求 nw 都走season 审批流 用的字段
            $references['sys_department_id'] = $staffInfo['sys_department_id'];
            $references['node_department_id'] = $staffInfo['node_department_id'];
            $references['type'] = $type;
            $references['job_title'] = $staffInfo['job_title'];
            //pcc 和 bulky 要一样的逻辑 后来拆开 不一样了
            $references['is_bulky'] = $is_bulky ? 1 : 0;
            $references['is_pcc'] = $is_pcc ? 1 : 0;
        } elseif ($shop_flag &&
            in_array($staffInfo['job_title'], [
                enums::$job_title['shop_officer'],
                enums::$job_title['shop_supervisor'],
                enums::$job_title['th_senior_shop_officer']])
        ) {
            //区域名称
            $regionPieceRe             = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
            $regionPieceInfo           = $regionPieceRe->getOrganizationRegionPieceManagerId($staffInfo['sys_store_id']);
            $detailData['region_name'] = $regionPieceInfo['region_name'] ?? '';
            $references['region_id']   = $references['region_id'] ?? '';//调揽件接口用 详情页展示 申请时候固化

            //累计时长
            $detailData['duration']           = $references['duration'] = $this->getMonthDuration($staffId, $date, $type);
            $references['store_category']     = !empty($storeInfo) && $storeInfo->category ? $storeInfo->category : null;
            $references['sys_department_id']  = $staffInfo['sys_department_id'];
            $references['node_department_id'] = $staffInfo['node_department_id'];
            $references['job_title']          = $staffInfo['job_title'];

            /**
             * 这两个字段删除了 审批流有问题 产品说审批流不变 这个字段只是详情展示去掉了 别的该用还用
             * all_effective_num  store_effective_num
             */
            $statistic_mode                    = new StatisticForOvertimeRepository();
            $data                              = $statistic_mode->find_by_store($staffInfo['sys_store_id'], $date, enums::OT_STATISTIC_TYPE_2);
            $references['all_effective_num']   = $data['all_effective_num'] ?? 0;
            $references['store_effective_num'] = $data['store_effective_num'] ?? 0;

        } else {
            //新审批流需求 需增加2个字段 组织机构和所属公司https://l8bx01gcjr.feishu.cn/docs/doccnM3g2ev63nhQDTFk15I13Vg
            $references = [
                'sys_department_id' => $staffInfo['sys_department_id'],
                'node_department_id' => $staffInfo['node_department_id'],
                'job_title'         => $staffInfo['job_title'],
                'organization_type' => $staffInfo['organization_type'],
                'company_id' => $staffInfo['company_id'],
            ];
            //判断 是否走子审批流 ext code
            $flag = $this->check_ot_flow($staffInfo);
            if($flag) {
                $extend['flow_code'] = enums::FLOW_CODE_OT_SUB;//定制 加班 子审批流
            }
        }
        //1倍 ot 新增字段 https://flashexpress.feishu.cn/docx/VyMedbkOUog6eAxr4CXcosh6nIb
        if (in_array($type, array_keys($this->holidayOt)) && (in_array($staffInfo['job_title'], $this->configData['1OT_Courier_Limit'])
                || in_array($staffInfo['job_title'], $this->configData['1OT_DCO_Limit'])))
        {
            $one_ot_detail = $this->one_ot_detail($staffInfo, $param, true);
            $detailData = $one_ot_detail;
        }

        //额外增加了两个字段 真对子公司用
        $references['pay_flag'] = $pay_flag;
        $references['money_flag'] = $money_flag;

        if (empty($references)) {
            throw new \Exception('no ot references data');
        }

        return [$references, $extend,$detailData];
    }

    //1倍ot 指定职位 获取详情参考数据 展示用 申请时候固化
    public function one_ot_detail($staffInfo, $param, $isAddDuration = false)
    {
        $type = $param['type'];
        $date = $param['date_at'];
        //正式员工、月薪制合同工、日薪制合同工
        if (!in_array($staffInfo['hire_type'],
            [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3])) {
            return [];
        }
        if ($staffInfo['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            return [];
        }
        $formalHireType = [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2];
        $dailyHireType  = [HrStaffInfoModel::HIRE_TYPE_3];
        $formalTypeArr  = [HrOvertimeModel::OVERTIME_4];
        $dailyTypeArr   = [HrOvertimeModel::OVERTIME_5, HrOvertimeModel::OVERTIME_6];

        $res['ot_detail_6']      = $this->getStoreDetailName($staffInfo['sys_store_id']);//员工所在网点：格式为 所属大区-片区-网点
        $res['staff_hire_type'] = !empty($staffInfo['hire_type']) ? $staffInfo['hire_type'] : 0;//员工雇佣类型
        //- 网点出勤率(排上班出勤人数/在职人数)
        $res['one_ot_attendance_rate'] = '';//占个位置 需要审批通过固化

        //提交申请的员工申请OT日期所在的月份的个人累计1倍OT时长，含已通过+待审核（固化） 取当前员工雇佣类型对应的ot 类型
        $ot_types               = $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_3 ? $dailyTypeArr : $formalTypeArr;
        $start_date             = date("Y-m-01", strtotime($date));
        $end_date               = date('Y-m-d', strtotime("{$start_date} last day of "));
        $model                  = new OvertimeRepository($this->timeZone);
        $duration               = $model->get_duration($staffInfo['staff_info_id'], $start_date, $end_date, $ot_types);
        $res['one_ot_duration'] = empty($duration) ? 0 : round($duration, 1);
        $res['one_ot_duration'] = $isAddDuration ? bcadd($res['one_ot_duration'], $param['duration'], 1) : $res['one_ot_duration'];

        //网点本月累计 日薪员工 1OT时长（包含本次申请）：x h
        $durationParam['job_title'] = (in_array($staffInfo['job_title'], $this->configData['1OT_Courier_Limit'])) ? $this->configData['1OT_Courier_Limit'] : $this->configData['1OT_DCO_Limit'];
        $durationParam['hire_type'] = $dailyHireType;
        $daily_duration               = $model->get_duration_by_store($staffInfo['sys_store_id'], $start_date, $end_date, $dailyTypeArr, $durationParam);
        $res['one_ot_daily_duration'] = empty($daily_duration) ? 0 : round($daily_duration, 1);
        //如果本次申请是 日薪类型才加上本次时长
        if(in_array($type, [HrOvertimeModel::OVERTIME_5, HrOvertimeModel::OVERTIME_6]) && $isAddDuration){
            $res['one_ot_daily_duration'] += $param['duration'];
        }

        //网点本月累计 正式员工
        $durationParam['hire_type']    = $formalHireType;
        $formal_duration               = $model->get_duration_by_store($staffInfo['sys_store_id'], $start_date, $end_date, $formalTypeArr, $durationParam);
        $res['one_ot_formal_duration'] = empty($formal_duration) ? 0 : round($formal_duration, 1);
        //如果是正式员工 申请类型4 才加本次申请时长
        if($type == HrOvertimeModel::OVERTIME_4 && $isAddDuration){
            $res['one_ot_formal_duration'] += $param['duration'];
        }

        //- 网点本月剩余可申请1OT时长：x h
        $setting                  = OvertimeHourSettingDetailModel::findFirst([
            'conditions' => 'store_id = :store_id: and is_delete = 0 and start_date <= :date_at: and end_date >= :date_at: and module_type = :module_type:',
            'bind'       => ['store_id' => $staffInfo['sys_store_id'], 'date_at' => $date, 'module_type' => OvertimeHourSettingDetailModel::MODULE_TYPE_OFF_DAY],
        ]);

        $res['one_ot_month_left'] = '-';//默认展示-
        if (!empty($setting)) {
            //日薪制员工 取类型 5，6
            if ($staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_3) {
                $key = 'daily_';
                $durationParam['hire_type'] = $dailyHireType;
                $typeArr = $dailyTypeArr;
            } else {//正式和月薪 取 4
                $key = 'formal_';
                $durationParam['hire_type'] = $formalHireType;
                $typeArr = $formalTypeArr;
            }
            if (in_array($staffInfo['job_title'], $this->configData['1OT_Courier_Limit'])) {
                $key .= 'courier_hour';
            } else {
                $key .= 'dc_hour';
            }
            $this->logger->write_log(['one_ot_detail_setting' => $setting->toArray(),'key' => $key, 'param' => $durationParam], 'info');
            if (!is_null($setting->$key)) {
                //新需求改动 只取配置像对应的时间区间的 ot 小时
                $sub = $model->get_duration_by_store($staffInfo['sys_store_id'], $setting->start_date, $setting->end_date, $typeArr, $durationParam);
                $sub = $isAddDuration ? bcadd($sub, $param['duration'], 1) : $sub;
                //配置小时 减去 该类型员工已申请时长
                $res['one_ot_month_left'] = bcsub($setting->$key, $sub, 1);
            }
        }

        //仓管员1.5OT历史逻辑，在1OT中同步展示 只有仓管 展示
        if(in_array($staffInfo['job_title'], $this->configData['1OT_DCO_Limit'])){
            $statistic_mode     = new StatisticForOvertimeRepository();
            $data               = $statistic_mode->find_by_store($staffInfo['sys_store_id'], $date, enums::OT_STATISTIC_TYPE_1);
            $res['ot_detail_1'] = $data['all_effective_num'] ?? 0;//上周所 有 网点 揽派件平均工作效率
            $res['ot_detail_2'] = $data['store_effective_num'] ?? 0;//上周所 在 网点 揽派件平均工作效率
        }
        //两个表格
        if (in_array($staffInfo['job_title'], $this->configData['1OT_Courier_Limit'])) {
            //人效 fbi接口 刘文超
            $ac                  = new ApiClient('ard_api', '', 'Deliverycount.storeDeliveryAverageByTime', $this->lang);
            $biParam['storeId']  = $staffInfo['sys_store_id'];
            $dateList            = DateHelper::DateRange(strtotime('-7 days'), strtotime('-1 days'));
            $biParam['dateList'] = $dateList;
            $ac->setParams($biParam);
            $biRes                    = $ac->execute();
            $res['fbi_effective_num'] = [];
            if (!empty($biRes['result']) && isset($biRes['result']['data'])) {
                $res['fbi_effective_num'] = $biRes['result']['data'];
            }
            //近 三个月 3次审批通过的 加班数据对应的日期
            $start_date           = date('Y-m-d', strtotime('-90 days'));
            $end_date             = date('Y-m-d', strtotime('-1 days'));
            $month_3_date         = HrOvertimeModel::find([
                'conditions' => 'staff_id = :staff_id: and date_at between :start_date: and :end_date: and state = 2 and type in ({types:array})',
                'bind'       => [
                    'staff_id'   => $staffInfo['staff_info_id'],
                    'start_date' => $start_date,
                    'end_date'   => $end_date,
                    'types'      => $ot_types,
                ],
                'order'      => 'date_at desc',
                'limit'      => 3,
            ])->toArray();
            $res['ms_parcel_num'] = [];
            if (empty($month_3_date)) {
                return $res;
            }
            //找这些日期的 支援账号
            $msDate = array_column($month_3_date, 'date_at');
            $arr    = $bind = [];
            foreach ($msDate as $k => $da) {
                $key        = 'da_' . $k;
                $arr[]      = "employment_begin_date <= :{$key}: and employment_end_date >= :{$key}:";
                $bind[$key] = $da;
            }
            $msWhere               = '(' . implode(') or ( ', $arr) . ')';
            $bind['staff_id']      = $staffInfo['staff_info_id'];
            $subData               = HrStaffApplySupportStoreModel::find([
                'columns'    => 'sub_staff_info_id, employment_begin_date, employment_end_date',
                'conditions' => "staff_info_id = :staff_id: and status = 2 and support_status in (2,3) and ({$msWhere})",
                'bind'       => $bind,
            ])->toArray();
            $subStaff              = empty($subData) ? [] : array_column($subData, 'sub_staff_info_id');
            $msParam['staffIds']   = empty($subStaff) ? [$staffInfo['staff_info_id']] : array_merge($subStaff, [$staffInfo['staff_info_id']]);
            $msParam['queryDates'] = $msDate;
            $api                   = new RestClient('pms');
            $msRes                   = $api->execute(RestClient::METHOD_POST, '/svc/staff/ticket/backyard/count', $msParam);
            if (empty($msRes['code']) || $msRes['code'] != ErrCode::SUCCESS) {
                //记录日志
                $this->logger->write_log(['one_ot_detail_pms' => $msRes]);
            }else{
                //java那边返回的是null
                foreach ($msRes['data'] as $k => &$v){
                    $v['is_support'] = false;
                    if(empty($v['pickupParcelCount'])){
                        $v['pickupParcelCount'] = 0;
                    }
                    if(empty($v['ticketDeliveryFinishedCount'])){
                        $v['ticketDeliveryFinishedCount'] = 0;
                    }
                    if(!empty($subData)){//如果存在支援 要标记出来
                        foreach ($subData as $sub){
                            if($v['date'] >= $sub['employment_begin_date'] && $v['date'] <= $sub['employment_end_date']){
                                $v['is_support'] = true;
                            }
                        }
                    }
                }
                $msRes['data'] = array_values($msRes['data']);
                $res['ms_parcel_num'] = $msRes['data'] ?? [];
            }
        }

        //整理数据 加上 单位
        $res['one_ot_duration']        .= ' h';
        $res['one_ot_formal_duration'] .= ' h';
        $res['one_ot_daily_duration']  .= ' h';
        $res['one_ot_month_left']      = ($res['one_ot_month_left'] == '-') ? '-' : $res['one_ot_month_left'] . ' h';
        return $res;
    }

    //看是否需要固化 详情页用
    public function getOneOtDetail($staffInfo, $applyInfo){
        $detailData = json_decode($applyInfo['detail_data'], true);
        unset($detailData['duration']);
        //正式员工、月薪制合同工、日薪制合同工
        if (!in_array($staffInfo['hire_type'],
            [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3])) {
            return [];
        }
        if ($staffInfo['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            return [];
        }

        //如果是历史数据 并且是审批完成的 不展示
        if(!isset($detailData['staff_hire_type']) && $applyInfo['state'] != enums::$audit_status['panding']){
            return [];
        }
        //历史数据 并且 是审批中 要展示新字段
        if(!isset($detailData['staff_hire_type']) && $applyInfo['state'] == enums::$audit_status['panding']){
            $detailData                    = $this->one_ot_detail($staffInfo, $applyInfo);
            $detailData['staff_hire_type'] = $this->getTranslation()->_('hire_type_' . $detailData['staff_hire_type']);
            //仓管 特有的数据
            if (isset($detailData['ot_detail_1'])) {
                $detailData['ot_detail_1'] = ($detailData['ot_detail_1'] ?? 0) . ' ' . $this->getTranslation()->_('ot_detail_4');//上周所有网点平均工作效率
                $detailData['ot_detail_2'] = ($detailData['ot_detail_2'] ?? 0) . ' ' . $this->getTranslation()->_('ot_detail_4');//上周所在网点平均工作效率
            }
            //获取实时数据
            $rate = $this->oneOtAttendanceRate($staffInfo['sys_store_id'], $applyInfo['date_at']);
            $detailData['one_ot_attendance_rate'] = "{$rate['on_num']}/{$rate['staff_num']}({$rate['rate']}%)";
            return $detailData;
        }

        //以下 是上线之后 新申请的数据
        $detailData['staff_hire_type'] = $this->getTranslation()->_('hire_type_' . $detailData['staff_hire_type']);
        //仓管 特有的数据
        if (isset($detailData['ot_detail_1'])) {//历史数据没有这两个 不显示
            $detailData['ot_detail_1'] = ($detailData['ot_detail_1'] ?? 0) . ' ' . $this->getTranslation()->_('ot_detail_4');//上周所有网点平均工作效率
            $detailData['ot_detail_2'] = ($detailData['ot_detail_2'] ?? 0) . ' ' . $this->getTranslation()->_('ot_detail_4');//上周所在网点平均工作效率
        }
        //非待审批 直接返回
        if($applyInfo['state'] != enums::$audit_status['panding']){
            return $detailData;
        }
        //获取实时数据
        $rate = $this->oneOtAttendanceRate($staffInfo['sys_store_id'], $applyInfo['date_at']);
        $detailData['one_ot_attendance_rate'] = "{$rate['on_num']}/{$rate['staff_num']}({$rate['rate']}%)";
        return $detailData;
    }


    //- 网点出勤率(排上班出勤人数/在职人数) 审批操作 固化用
    public function oneOtAttendanceRate($storeId, $dateAt)
    {
        $return['on_num'] = $return['staff_num'] = 0;
        $return['rate']   = '0%';
        //网点所有 非离职 人数
        $staff_num = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'sys_store_id = :store_id: and state in ({states:array}) and job_title in ({job_title:array}) and hire_type in ({hire_type:array}) and formal = 1 and is_sub_staff = 0',
            'bind'       => [
                'store_id'  => $storeId,
                'states'    => [HrStaffInfoModel::STATE_1, HrStaffInfoModel::STATE_3],
                'job_title' => $this->configData['1OT_Courier_Limit'],
                'hire_type' => [
                    HrStaffInfoModel::HIRE_TYPE_1,
                    HrStaffInfoModel::HIRE_TYPE_2,
                    HrStaffInfoModel::HIRE_TYPE_3,
                ],
            ],
        ])->toArray();
        if (empty($staff_num)) {
            return $return;
        }
        $staff_num           = array_column($staff_num, 'staff_info_id');

        $this->logger->write_log("oneOtAttendanceRate staff_id " . json_encode($staff_num), 'info');
        //对应日期 排班是on的人
        $off_num = HrStaffWorkDayModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'staff_info_id in ({staff_id:array}) and date_at = :date_at: ',
            'bind'       => [
                'date_at'  => $dateAt,
                'staff_id' => $staff_num,
            ],
        ])->toArray();
        $off_num = empty($off_num) ? [] : array_column($off_num, 'staff_info_id');
        $this->logger->write_log("oneOtAttendanceRate off_num " . json_encode($off_num), 'info');
        //总人数
        $return['staff_num'] = count($staff_num);
        //出勤的人
        $return['on_num'] = bcsub(count($staff_num), count($off_num));
        //出勤率
        $return['rate']   = round($return['on_num'] / $return['staff_num'], 2) * 100;
        return $return;
    }


    /**
     * 获取 网点工作效率值 和该员工 当月申请的ot 时长
     * @param $store_id string 员工申请加班时候 所在网点id 保存在 加班申请表
     * @param $staff_info  申请员工 工号
     * @param $date date 申请创建日期
     * @param $type int 加班类型
     * @param $ot_date date 申请加班的日期 非创建日期
     * @param $statistic_flag 1默认 一期数据 nw DC officer  2 二期shop 相关 3 nb dc officer
     * @return array|bool
     */
    public function get_effective($store_id, $staff_info, $date, $type, $ot_date,$statistic_flag = 1)
    {
        //判断该员工网点 是否是 指定分类 即网点类型为DC/SP/BDC 网点类型 1，2，10
        $store_server = new SysStoreServer($this->lang);
        $store_info = $store_server->getStoreByid($store_id);
        if(empty($store_info))
            return false;

        $staff_id = $staff_info['staff_info_id'];
        //不在指定分类
        // 网点类型为DC/SP/BDC/CDC
        if (!in_array($store_info['category'], [
            enums::$stores_category['sp'],
            enums::$stores_category['dc'],
            enums::$stores_category['shop_pickup_only'],
            enums::$stores_category['shop_pickup_delivery'],
            enums::$stores_category['shop_ushop'],
            enums::$stores_category['bdc'],
            enums::$stores_category['cdc'],
        ])) {
            return false;
        }
        $statistic_mode = new StatisticForOvertimeRepository();
        $data = $statistic_mode->find_by_store($store_id,$date,$statistic_flag);
        if(empty($data))//默认值
            $data['all_effective_num'] =
            $data['store_effective_num'] =
            $data['job_num'] =
            $data['attendance_num'] =
            $data['parcel_num'] = 0;

        //shop相关 获取 单独字段
        if($statistic_flag == enums::OT_STATISTIC_TYPE_2){
            $ot_date_info = $statistic_mode->find_by_store_date($store_id,$ot_date);
            $data['attendance_num'] = $ot_date_info['attendance_time'];//所在网点出勤人数
            $data['parcel_num'] = $ot_date_info['delivery_num'] + $ot_date_info['pickup_num'];//所在网点总揽件量
        }

        //新需求  如果是 dc officer 1.5倍  type 1 类型  要取所在网点 有几个 dc 并且看 近7天揽件
        if($statistic_flag == enums::OT_STATISTIC_TYPE_1){
            if ($type == HrOvertimeModel::OVERTIME_1) {//1。5倍 加班
                $setting_server = new SettingEnvServer();
                $settingStores  = $setting_server->getBusinessValue('setting_store');
                $settingStores  = empty($settingStores) ? '' : explode(',', $settingStores);
                $settingStores = array_map('trim',$settingStores);
                //不在配置项网点
                if (in_array($store_id, $settingStores)) {
                    $p['store_id'] = $store_id;
                    $p['date']     = $date;
                    $p['type']     = $type;
                    $budget        = $this->budgetHour($p);
                    $data          = array_merge($data, $budget);
                }
            }
            //新需求 dc  1倍 获取 是否爆仓字段 和对应的 剩余应派件
            //接口地址 https://yapi.flashexpress.pub/project/59/interface/api/55543
            if(in_array($type,[HrOvertimeModel::OVERTIME_4,HrOvertimeModel::OVERTIME_5,HrOvertimeModel::OVERTIME_6])){//1倍加班
                //调接口 获取是否爆仓
                $param['store_id'] = $store_id;
                $ac                = new ApiClient('bi_rpcv2', '', 'dc.get_explosive_ware_house', $this->lang);
                $ac->setParams($param);
                $ac_result = $ac->execute();
                $this->logger->write_log("ot_get_explosive_ware_house {$staff_id} request ".json_encode($param,
                        JSON_UNESCAPED_UNICODE)." res:".json_encode($ac_result, JSON_UNESCAPED_UNICODE)." ", 'info');

                /**
                 * "is_explode_warehouse": 1, //是否爆仓 1代表爆仓 0代表没有爆仓
                 * "rest_count": 3 //爆仓后剩余应派件量，如果没爆仓返回0
                 */
                $data['is_boom'] = $data['pick_left'] = 0;

                if (!empty($ac_result) && isset($ac_result['result'])) {
                    $data['is_boom']   = $ac_result['result']['data']['is_explode_warehouse'];
                    $data['pick_left'] = $ac_result['result']['data']['rest_count'];
                }

                //查询 是否是 off  看轮休表
                $is_off           = HrStaffWorkDayModel::findFirst("staff_info_id = {$staff_id} and date_at = '{$date}'");
                $data['is_alert'] = 0;
                $is_ph            = $this->get_ph_num($staff_info, $date, true);

                //弹窗前提 如果是off 申请1倍 弹  如果 当天是 ph  或者 爆仓 不弹
                if (!empty($is_off) && !$is_ph && !$data['is_boom']) {
                    $data['is_alert'] = 1;
                }
            }

        }

        //新需求 bulky 部门的 dc  拆出来
        if($statistic_flag == enums::OT_STATISTIC_TYPE_3){
            if(in_array($type,[HrOvertimeModel::OVERTIME_4,HrOvertimeModel::OVERTIME_5,HrOvertimeModel::OVERTIME_6])){//1倍加班 跟 nw 部门的 一样
                //调接口 获取是否爆仓
                $param['store_id'] = $store_id;
                $ac = new ApiClient('bi_rpcv2', '', 'dc.get_explosive_ware_house',$this->lang);
                $ac->setParams($param);
                $ac_result = $ac->execute();
                $this->logger->write_log("ot_get_explosive_ware_house {$staff_id} request ".json_encode($param,JSON_UNESCAPED_UNICODE)." res:".json_encode($ac_result,JSON_UNESCAPED_UNICODE)." ", 'info');


                $data['is_boom'] = $data['pick_left'] = 0;

                if(!empty($ac_result) && isset($ac_result['result'])){
                    $data['is_boom'] = $ac_result['result']['data']['is_explode_warehouse'];
                    $data['pick_left'] = $ac_result['result']['data']['rest_count'];
                }

                //查询 是否是 off  看轮休表
                $is_off = HrStaffWorkDayModel::findFirst("staff_info_id = {$staff_id} and date_at = '{$date}'");
                $data['is_alert'] = 0;
                $is_ph = $this->get_ph_num($staff_info,$date,true);

                //弹窗前提 如果是off 申请1倍 弹  如果 当天是 ph  或者 爆仓 不弹
                if (!empty($is_off) && !$is_ph && !$data['is_boom']) {
                    $data['is_alert'] = 1;
                }
            }
        }

        $this->logger->write_log("over_time effective {$staff_id} ".json_encode($data),'info');
        return $data;
    }

    //获取对应类型 一个月的加班时长
    public function getMonthDuration($staffId,$date,$type){
        //获取该员工一个月的加班时长
        $start_date = date("Y-m-01",strtotime($date));
        $end_date = date('Y-m-d',strtotime("{$start_date} last day of "));
        $model = new OvertimeRepository($this->timeZone);
        $duration = $model->get_duration($staffId,$start_date,$end_date,$type);
        return empty($duration) ? 0 : round($duration,1);
    }


    //根据 网点在职的 dc 和 后台配置 获取 加班申请的总时长和剩余时长
    public function budgetHour($param)
    {
        $store_id                = $param['store_id'];
        $type                    = $param['type'];
        $date                    = $param['date'];
        $data['dc_in_all_hours'] = $data['dc_should_hours'] = 0;
        //看配置项有没有配置网点
        $codeList        = ['setting_num_ak', 'setting_num_parcel'];
        $setting_server  = new SettingEnvServer();
        $businessSetting = $setting_server->listByBusinessCode($codeList);
        $businessSetting = empty($businessSetting) ? '' : array_column($businessSetting, 'set_val', 'code');

        //获取所在网点 几个 dc
        //新需求 需要计算 dc  和 abs
        $dc_staff = $this->get_dc_num($store_id,$this->nwSpecialJob);//cd  和 abs 共同计算
        //当前所在网点 dc 已经申请的小时数 dc_in_all_hours  Y
        $week_start = weekStart($date);
        $week_end   = weekEnd($date);
        $model      = new OvertimeRepository($this->timeZone);
        //一周内已经申请的对应ot类型的时长
        $data['dc_in_all_hours'] = $model->get_duration($dc_staff, $week_start, $week_end, $type);

        //n个dc 匹配对应的 值 预算总数  K  dc_should_hours
        $dc_num = count($dc_staff);
        //根据配置 获取对应的时长预算
        $ak_setting = empty($businessSetting['setting_num_ak']) ? [] : explode(',', $businessSetting['setting_num_ak']);
        $is_set     = false;
        $maxNum = 0;
        $settingData = [];

        //配置项是 竖线分割
        foreach ($ak_setting as $v) {
            [$num, $hour] = explode('|', $v);
            $settingData[$num] = $hour;
            if ($dc_num == $num) {
                $is_set = true;
                $data['dc_should_hours'] = $hour ?? 0;
            }
            $maxNum = max($maxNum,$num);

        }
        $this->logger->write_log("budgetHour {$store_id} {$data['dc_in_all_hours']} ".json_encode($ak_setting), 'info');
        //如果没匹配上配置项 不用往下走了 改了 没匹配上 如果大于最大值 取最大值对应的
        if ($is_set !== true && ($dc_num > $maxNum)) {
            //如果 大于最大值 取最大
            $data['dc_should_hours'] = $settingData[$maxNum];
        }
        //如果小于最大值 业务逻辑申请不限制 审批流正常走默认 4级审批
        if ($is_set !== true && ($dc_num < $maxNum)) {
            return [];
        }

        //如果没有包裹分数线配置
        if (empty($businessSetting['setting_num_parcel'])) {
            return $data;
        }

        //调接口 获取 是否需要 上调 14 https://yapi.flashexpress.pub/project/59/interface/api/55285
        $param['store_id'] = $store_id;
        $ac                = new ApiClient('bi_rpcv2', '', 'dc.get_avg_pickup_operation_count', $this->lang);
        $ac->setParams($param);
        $ac_result = $ac->execute();
        $this->logger->write_log("ot_get_explosive_ware_house {$store_id} request ".json_encode($param,
                JSON_UNESCAPED_UNICODE)." res:".json_encode($ac_result, JSON_UNESCAPED_UNICODE)." ", 'info');

        if (!empty($ac_result) && !empty($ac_result['result'])) {
            //获取 分数线 配置项 前面是件数 后面是 小时数 还有可能配置多个
            [$score, $addHour] = explode('|', $businessSetting['setting_num_parcel']);
            $pick_num = $ac_result['result']['data']['avg_count'] ?? 0;
            //超过分数线 额度增加对应的配置小时数
            if ($pick_num > intval($score)) {
                $data['dc_should_hours'] += intval($addHour) ?? 0;
            }
        }
        return $data;
    }



    /**
     * 额外新增逻辑  判断 申请的ot date 所在自然月 有多少个ph
     * @param $staff_info
     * @param $date
     * @param bool $is_bool 需要判断  参数日期 是不是ph 返回bool
     * @return bool|int
     */
    public function get_ph_num($staff_info,$date,$is_bool = false){
        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $ph_data = $leave_server->ph_days($staff_info);
        $ph_num = 0;
        if(empty($ph_data))
            return 0;

        $ph_data = array_column($ph_data,'day');
        //判断日期 是不是 ph
        if($is_bool === true)
            return in_array($date,$ph_data);


        $month_start = date('Y-m-01',strtotime($date));
        $month_end = date('Y-m-d',strtotime("{$date} last day of"));//最后一天

        foreach ($ph_data as $date){
            if($date >= $month_start && $date <= $month_end)
                $ph_num++;
        }


        return $ph_num;
    }


    //bulky 排除 部分转过来的大区的人 不能用 bulky 的规则 Area3 -id3、Area6 -id 6、Area14 id-31
    public function bulkyIgnore($storeId){
        //获取 定制大区配置id
        $setting_server = new SettingEnvServer();
        $limitAreaIds = $setting_server->getSetVal('bulky_ignore_region');
        if(empty($limitAreaIds)){
            return true;
        }
        $limitAreaIds = explode(',',$limitAreaIds);

        //是否属于 这几个大区 如果属于 返回false
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.id');
        $builder->from(['s' => SysStoreModel::class]);
        $builder->join(SysManageRegionModel::class, 's.manage_region = r.id', 'r');
        $builder->inWhere('r.id', $limitAreaIds);
        $builder->andWhere('s.id = :storeId:', ['storeId' => $storeId]);
        $isBelong = $builder->getQuery()->execute()->getFirst();

        if(empty($isBelong)){
            return true;
        }

        //如果 属于 新搬过来的 大区 返回假 不走 bulky逻辑
        return false;
    }



    /**
     * 最终审批 固化定制的数据 详情用 用新增字段
     * should_delivery_today 当天应派件
     * should_pickup_today  当天揽件包裹量
     * store_job_attendance_num  对应日期、所属网点、指定职位的[出勤人数] (计算用 不展示)
     * attendance_rate  出勤率
     * dc_today_effect  人效
     * @param $applyInfo
     * @return mixed
     */
    public function freezeDetail($applyInfo){
        $this->staffInfo = $this->getStaffInfo($applyInfo['staff_id']);
        //最终审批通过 调用 fbi 接口 获取动态数据 固化到 json里
        $json_arr = json_decode($applyInfo['detail_data'], true);
        //接口参数
        $param['store_id'] = $this->staffInfo['sys_store_id'];
        $param['date']     = $applyInfo['date_at'];

        //一共下面这几个字段要固化
        $json_arr['should_delivery_today']    = $this->sendRequest($param, 'dc.get_dc_should_delivery_count') ?? 0;
        $json_arr['should_pickup_today']      = $this->sendRequest($param, 'dc.get_dc_operation_count') ?? 0;
        $json_arr['store_job_attendance_num'] = 0;
        $json_arr['attendance_rate']          = "0/0";
        $json_arr['dc_today_effect']          = '0 '.$this->getTranslation()->_('rate_unit');


        //当天 仓管 出勤率
        $rateJobs = $this->nwSpecialJob;
        $rate_data = $this->attendance_rate($applyInfo['date_at'], $rateJobs, $this->staffInfo['sys_store_id']);
        if (!empty($rate_data)) {
            //出勤率
            $json_arr['store_job_attendance_num'] = $att_num = count($rate_data['all_num']); //对应网点 职位 出勤人数

            $staff_num                   = count($rate_data['in_staff']) + count($rate_data['out_staff']);
            $rate                        = empty($staff_num) ? 0 : round($att_num / $staff_num, 2) * 100;
            $json_arr['attendance_rate'] = sprintf("%d/%d(%d%%)", $att_num, $staff_num, $rate);
        }

        $attendance_num = $json_arr['store_job_attendance_num'] ?? 0;//对应日期 所属网点 指定职位的 出勤人数
        //当天 仓管 人效 实时调用接口 取前面三个的数据计算 审批通过以后 固化  所在网点当天应派包裹量+所在网点当天揽件包裹量）/实际DC Officer出勤人数
        if ($attendance_num > 0) {
            $effect                      = ($json_arr['should_delivery_today'] + $json_arr['should_pickup_today']) / $attendance_num;
            $json_arr['dc_today_effect'] = round($effect, 1).' '.$this->getTranslation()->_('rate_unit');
        }
        return $json_arr;

    }


    //泰国 ot 详情页 用到的 rpc 数据 只有 dc officer 和 assi branch supervisor 用到
    public function ot_dc_detail($staff_info, $apply_info, $param = [])
    {
        $this->staffInfo            = $staff_info;
        $references                 = json_decode($apply_info['references'], true);
        $detailData                 = json_decode($apply_info['detail_data'], true);
        $isNw = false;
        //现在不是 nw 的 也会进来 有的字段只针对 nw 才展示 还要按顺序展示
        if(!empty($param['networkManagementId']) && $staff_info['sys_department_id'] == $param['networkManagementId']){
            $isNw = true;
        }
        $detailLists = [];
        if($isNw){
            $detailLists['ot_detail_6'] = $detailData['store_name'] ?? $references['store_name'] ?? '';//员工网点名称 带 大区片区
            //新需求 要夹在这块 https://flashexpress.feishu.cn/docx/doxcne2vUHNJAf8WSoeqOkWW5bt

            $detailLists['should_delivery_today'] = 0;//当天应派件 实时调用接口 审批通过以后 固化
            $detailLists['should_pickup_today']   = 0;//当天揽件包裹量 实时调用接口 审批通过以后 固化

            if ($apply_info['state'] != enums::APPROVAL_STATUS_PENDING) {//最终状态 直接取数据 没有就没有了
                $detailLists['should_delivery_today'] = $detailData['should_delivery_today'] ?? $references['should_delivery_today'] ?? 0;
                $detailLists['should_pickup_today']   = $detailData['should_pickup_today'] ?? $references['should_pickup_today'] ?? 0;
                $detailLists['attendance_rate']       = $detailData['attendance_rate'] ?? $references['attendance_rate'] ?? '';//出勤率
                $detailLists['dc_today_effect']       = $detailData['dc_today_effect'] ?? $references['dc_today_effect'] ?? 0 .' '.$this->getTranslation()->_('rate_unit');//人效
            } else {//带审批状态 调rpc
                $freezeData = $this->freezeDetail($apply_info);
                //详情显示 要有顺序
                $detailLists['should_delivery_today'] = $freezeData['should_delivery_today'];
                $detailLists['should_pickup_today']   = $freezeData['should_pickup_today'];
                $detailLists['attendance_rate']       = $freezeData['attendance_rate'];//出勤率
                $detailLists['dc_today_effect']       = $freezeData['dc_today_effect'];
            }

            //以前就有的 数据
            $detailLists['ot_detail_1'] = ($references['all_effective_num'] ?? 0).' '.$this->getTranslation()->_('ot_detail_4');//上周所有网点平均工作效率
            $detailLists['ot_detail_2'] = ($references['store_effective_num'] ?? 0).' '.$this->getTranslation()->_('ot_detail_4');//上周所在网点平均工作效率

            //当月 累计ot 时长
            $type_key    = HrOvertimeModel::$th_ot_key;
            $ot_type_key = $type_key[$apply_info['type']] ?? 'invalid_ot_type';
            //累计时长
            $detailLists[$ot_type_key] = ($references['duration'] ?? 0)."h";
        }


        //有配置 ak值 的网点 并且是 dco 职位 展示这个字段 历史数据 没有detail  所以要两个都判断
        if((isset($detailData['dc_left_hours']) || isset($references['dc_left_hours']))
            && $apply_info['type'] == HrOvertimeModel::OVERTIME_1
            && $this->staffInfo['job_title'] == enums::$job_title['dc_officer']
        ){
            $detailLists['dc_left_hours'] = ($detailData['dc_left_hours'] ?? 0)."h";
        }

        //新需求 abs 历史不展示 剩余小时 所以只需要判断 abs 有这个数据 就展示
        if($this->staffInfo['job_title'] == enums::$job_title['assistant_branch_supervisor'] && isset($detailData['dc_left_hours'])){
            $detailLists['dc_left_hours'] = ($detailData['dc_left_hours'] ?? 0)."h";
        }

        if ($isNw && in_array($apply_info['type'],
                [HrOvertimeModel::OVERTIME_4, HrOvertimeModel::OVERTIME_5, HrOvertimeModel::OVERTIME_6])
        ) { //1倍工资 才显示
            //1倍 新增显示 是否爆仓和 剩余件数
            if (isset($references['is_boom'])) {//是否爆仓字段
                $detailLists['dc_situation'] = $this->getTranslation()->_('is_not_boom');//没爆
                if ($references['is_boom'] == 1) {
                    //pick_left 剩余件
                    $replace_text                = ['pick_left' => $references['pick_left'] ?? 0];
                    $detailLists['dc_situation'] = $this->getTranslation()->_('is_boom', $replace_text);
                }
            }
        }
        return $detailLists;
    }

    //新版 shop 部门加班详情页参考数据
    public function shop_detail($staff_info, $apply_info, $param = []){
        //下面 freeze 用
        $this->staffInfo = $staff_info;

        //是否已经审批完成
        $isFinished = $apply_info['state'] != enums::APPROVAL_STATUS_PENDING;

        //只有 1 和 1。5倍有 3倍入口关了
        if(!in_array($apply_info['type'], [HrOvertimeModel::OVERTIME_1,HrOvertimeModel::OVERTIME_4])){
            return [];
        }

        $references = json_decode($apply_info['references'], true);
        $detailData = json_decode($apply_info['detail_data'], true);
        $durationKey = $apply_info['type'] == HrOvertimeModel::OVERTIME_1 ? 'ot_detail_3' : 'ot_detail_8';

        //审批完成 直接取detail
        if ($isFinished) {
            //区域名称
            $detail['ot_detail_11'] = $detailData['region_name'];
            // 只有1。5倍有
            if ($apply_info['type'] == HrOvertimeModel::OVERTIME_1) {
                //出勤人数 新版的在detail里面 旧版的 在references
                $detail['attendance_num']  = isset($references['attendance_num']) ? ($references['attendance_num'] ?: '0') : ($detailData['attendance_num'] ?: '0');
                //所在网点当天出勤率 表里带单位
                $detail['shop_attendance_rate'] = $detailData['shop_attendance_rate'];
                //员工申请OT日期所在网点总揽件量
                $detail['shop_pickup_num'] = empty($detailData['shop_pickup_num']) ? '0' : (string)$detailData['shop_pickup_num'];
            }
            //近7天所在网点平均工作效率
            $detail['shop_store_effect']  = (empty($detailData['shop_store_effect']) ? 0 : $detailData['shop_store_effect']) . ' ' . $this->getTranslation()->_('day_effect_unit');
            //近7天所在大区平均工作效率
            $detail['shop_region_effect'] = (empty($detailData['shop_region_effect']) ? 0 : $detailData['shop_region_effect']) . ' ' . $this->getTranslation()->_('day_effect_unit');
            //申请人本月累计1.5倍OT时长
            $detail[$durationKey]        = ($detailData['duration'] ?: "0").'h';
            return $detail;
        }

        //区域名称
        $detail['ot_detail_11'] = $detailData['region_name'];
        //动态数据 实时获取的
        $rpcData = $this->freezeShopDetail($apply_info);
        if ($apply_info['type'] == HrOvertimeModel::OVERTIME_1) {
            //出勤人数
            $detail['attendance_num'] = $rpcData['attendance_num'] ?: '0';
            //出勤率
            $detail['shop_attendance_rate'] = $rpcData['shop_attendance_rate'];
            //网点揽件数
            $detail['shop_pickup_num'] = $rpcData['shop_pickup_num'] ?: '0';
        }

        //近7天所在网点平均工作效率
        $detail['shop_store_effect'] = $rpcData['shop_store_effect'] . ' ' . $this->getTranslation()->_('day_effect_unit');

        //近7天所在大区平均工作效率  get_shopu_project_by_region_date6
        $detail['shop_region_effect'] = $rpcData['shop_region_effect'] . ' ' . $this->getTranslation()->_('day_effect_unit');

        //申请人本月累计OT时长
        $detail[$durationKey] = ($detailData['duration'] ?? "0").'h';
        return $detail;

    }
    //实时计算参考数据 和 最终审批完成固化用
    public function freezeShopDetail($applyInfo)
    {
        //最终审批通过 调用 fbi 接口 获取动态数据 固化到 json里
        $json       = json_decode($applyInfo['detail_data'], true);
        $references = json_decode($applyInfo['references'], true);

        //出勤相关
        $jobTitles = [
            enums::$job_title['shop_officer'],
            enums::$job_title['shop_supervisor'],
            enums::$job_title['th_senior_shop_officer'],
        ];
        //用创建日期
        $date    = date('Y-m-d', strtotime($applyInfo['created_at']));

        if ($applyInfo['type'] == HrOvertimeModel::OVERTIME_1) {
            //出勤人数 非审批通过 实时计算
            $attendanceData         = $this->attendance_rate($date, $jobTitles, $this->staffInfo['sys_store_id']);
            $json['attendance_num'] = count($attendanceData['all_num']);
            //所在网点当天出勤率
            $att_num                      = count($attendanceData['all_num']);
            $staff_num                    = count($attendanceData['in_staff']) + count($attendanceData['out_staff']);
            $rate                         = empty($staff_num) ? 0 : round($att_num / $staff_num, 2) * 100;
            $json['shop_attendance_rate'] = "{$rate}%";

            //接口参数 这个之前就有 用的是 ot 日期 不是创建日期
            $param['store_id']   = $this->staffInfo['sys_store_id'];
            $param['begin_date'] = $applyInfo['date_at'];
            $param['end_date']   = $applyInfo['date_at'];
            //当天网点
            $storeData = $this->sendRequest($param, 'deliverycount.get_shopu_project_by_store_date', 'ard_api');
            $storeData = !empty($storeData) ? array_column($storeData, null, 'stat_date') : [];
            //申请日期网点揽件
            $json['shop_pickup_num'] = $storeData[$date]['total'] ?: 0;
        }

        //网点人效 近7天 昨天- 前7 不包括今天 今天没数据
        $param['store_id']   = $this->staffInfo['sys_store_id'];
        $param['begin_date'] = date('Y-m-d', strtotime("-7 day"));
        $param['end_date']   = date('Y-m-d', strtotime("-1 day"));
        $storeDataBefore     = $this->sendRequest($param, 'deliverycount.get_shopu_project_by_store_date', 'ard_api');
        $storeDataBefore     = empty($storeDataBefore) ? [] : $storeDataBefore;
        //网点揽件
        $storeNum = array_sum(array_column($storeDataBefore, 'total'));
        //网点出勤
        $storeAttendance = array_sum(array_column($storeDataBefore, 'attendance_count'));
        $storeEffect     = 0;
        if (!empty($storeAttendance)) {
            $storeEffect = round($storeNum / $storeAttendance, 0);
        }

        //大区人效 近7天 昨天- 前7 不包括今天 今天没数据
        $param['region_id'] = $references['region_id'];
        if (empty($param['region_id'])) {
            //历史数据 要重新取一下
            $regionPieceRe      = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
            $regionPieceInfo    = $regionPieceRe->getOrganizationRegionPieceManagerId($this->staffInfo['sys_store_id']);
            $param['region_id'] = $regionPieceInfo['region_id'];
        }
        $regionDataBefore = $this->sendRequest($param, 'deliverycount.get_shopu_project_by_region_date', 'ard_api');
        $regionDataBefore = empty($regionDataBefore) ? [] : $regionDataBefore;
        //大区揽件
        $regionNum = array_sum(array_column($regionDataBefore, 'total'));
        //网点出勤
        $regionAttendance = array_sum(array_column($regionDataBefore, 'attendance_count'));
        $regionEffect     = 0;
        if (!empty($regionAttendance)) {
            $regionEffect = round($regionNum / $regionAttendance, 0);
        }

        //近7天所在网点平均工作效率
        $json['shop_store_effect'] = $storeEffect;
        //近7天所在大区平均工作效率
        $json['shop_region_effect'] = $regionEffect;

        return $json;
    }

}