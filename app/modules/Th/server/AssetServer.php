<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\AssetsGoodsEnums;
use FlashExpress\bi\App\Enums\AssetsInfoEnums;
use FlashExpress\bi\App\Enums\InventoryCheckEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AssetsGoodsModel;
use FlashExpress\bi\App\Models\backyard\AssetsInfoModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\oa\MaterialInventoryCheckStaffAssetsModel;
use FlashExpress\bi\App\Repository\AssetRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Repository\HrStaffContractRepository;
use FlashExpress\bi\App\Repository\WmsRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AssetServer AS GlobalBaseServer;
use Phalcon\Db\Column;
use Phalcon\Db;
use FlashExpress\bi\App\library\Exception\ValidationException;

class AssetServer extends GlobalBaseServer
{
    public function __construct($lang = 'zh-CN', $timezone = '+08:00')
    {
        parent::__construct($lang, $timezone);

        $this->timezone = $timezone;
        $this->other    = Tools::reBuildCountryInstance(new OtherRepository($timezone, $lang), [$timezone, $lang]);
        $this->wms      = Tools::reBuildCountryInstance( new WmsRepository());
        $this->storeS   = Tools::reBuildCountryInstance(new SysStoreServer($timezone), [$timezone]) ;
        $this->audit    = Tools::reBuildCountryInstance(new AuditRepository($this->lang), [$this->lang]) ;
        $this->staff    = Tools::reBuildCountryInstance( new StaffRepository($this->lang), [$this->lang]);
        $this->asset    = Tools::reBuildCountryInstance( new AssetRepository($this->lang), [$this->lang]);
        $this->contract = Tools::reBuildCountryInstance(new HrStaffContractRepository($this->lang), [$this->lang]) ;
    }

    /**
     * 获取网点可申请资产列表
     * @param $userinfo
     * @return array
     */
    public function getList($userinfo, $sourceType = null)
    {
        $staff_info_id   = $userinfo['id'];// 登录用户ID（员工ID）
        $organization_id = $userinfo['organization_id'];//网点ID
        $job_title       = $userinfo['job_title'];//职位ID

        //白名单
        // 白名单 泰国去掉
//        if (Tools::reBuildCountryInstance((new AuditServer($this->lang, $this->timezone)), [$this->lang, $this->timezone])->getASPermission(["staff_id" => $staff_info_id], true)) {
//            if ($sourceType && $sourceType == self::SOURCE_TYPE_PUBLIC_ASSETS) {
//                $sql = "--
//                            SELECT
//                                id,
//                                bar_code,
//                                goods_name_en,
//                                goods_name_th,
//                                goods_name_zh,
//                                sku_bar_code
//                            FROM
//                                assets_goods
//                            WHERE
//                                type IN ( 1, 2, 3, 5, 6, 7)
//                                AND deleted = 0
//                                AND category = 1 and is_public = 1 GROUP BY bar_code";
//            } else {
//                $sql = "--
//                            SELECT
//                                id,
//                                bar_code,
//                                goods_name_en,
//                                goods_name_th,
//                                goods_name_zh,
//                                sku_bar_code
//                            FROM
//                                assets_goods
//                            WHERE
//                                type IN ( 1, 2, 3, 5, 6, 7)
//                                AND deleted = 0
//                                AND category = 1 and is_public = 0 GROUP BY bar_code";
//            }
//
//        } else {
            //todo 根据员工ID获取出对应的网点信息
            $sql       = "SELECT category, manager_id FROM sys_store where id = '{$organization_id}'";
            $sys_store = $this->getDI()->get('db_fle')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
            $type      = $this->getUserSite($sys_store['category'], $userinfo);

            if ($sourceType && $sourceType == self::SOURCE_TYPE_PUBLIC_ASSETS) {
                $sql = "SELECT id,bar_code, goods_name_en,goods_name_th,goods_name_zh,sku_bar_code FROM assets_goods where type = '{$type}' and deleted = 0 and category = 1 and is_public = 1 ";
            } else {
                $sql = "SELECT id,bar_code, goods_name_en,goods_name_th,goods_name_zh,sku_bar_code FROM assets_goods where type = '{$type}' and deleted = 0 and category = 1 and is_public = 0 ";
            }
//        }
        $assets_list = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $list        = [];
        if (is_array($assets_list) && !empty($assets_list)) {

            $stock_flag = false;
            $params     = [];
            //现在改成不传barCode,返回全部，实时
            $params['lang'] = $this->lang;
            $stock_data     = (new BaseRepository())->getDataFromWms(env("api_wms_goodsStock"), $params);
            if ($stock_data['code'] == 1) {
                $stock_flag = true;
            }

            foreach ($assets_list as $key => $value) {
                $list[$key] = [
                    'id'                  => $value['id'],
                    'bar_code'            => $value['bar_code'],
                    'goods_name'          => '',
                    'available_inventory' => 0
                ];
                if ($stock_flag) {
                    $barCodeArr = [];

                    //如果规格的bar_code为空，则用bar_code
                    if (empty($value['sku_bar_code'])) {
                        //如果bar_code不为空，则放进数组
                        if (!empty($value['bar_code'])) {
                            $barCodeArr[] = $value['bar_code'];
                        }
                    } else {
                        $barCodeArr = explode(",", $value['sku_bar_code']);
                    }

                    if (!empty($barCodeArr)) {
                        foreach ($barCodeArr as $bar_code) {
                            if (isset($stock_data['data'][$bar_code])) {
                                $list[$key]['available_inventory'] += intval($stock_data['data'][$bar_code]['availableInventory']);
                            }
                        }
                    }
                }

                switch ($this->lang) {
                    case 'en':
                        $list[$key]['goods_name'] = $value['goods_name_en'];
                        break;
                    case 'th':
                        $list[$key]['goods_name'] = $value['goods_name_th'];
                        break;
                    default:
                        $list[$key]['goods_name'] = $value['goods_name_zh'];
                }


            }
        }

        return $list;

    }
    /**
     * 创建订单
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addAssetsOrder($paramIn = [], $userinfo)
    {
        $time = date("YmdHis");
        $create = gmdate('Y-m-d H:i:s');
        $rand = rand(1000, 9999);
        $orderId = $time . $rand;
        $auditBLL = new \FlashExpress\bi\App\Server\AuditServer($this->lang, $this->timezone);
        $auditBLL = Tools::reBuildCountryInstance($auditBLL, [$this->lang, $this->timezone]);
        if (!$auditBLL->getASPermission($userinfo)) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }
        //获取订单相关人信息
        //$admin_group = UC('wmsRole')['admin_group'];
        $organization_id = $userinfo['organization_id'];
        $uInfo = $this->wms->userStoreInfo($organization_id);
        $uInfoArr = array_merge(empty($uInfo) ? [] : $uInfo, $userinfo);
        $staffInfo = (new StaffServer())->getStaffById($userinfo['staff_id']);
//        $store_info = $this->storeS->getStoreByid($organization_id);
//        $manage_region = isset($store_info['manage_region']) ? $store_info['manage_region'] : '';
//        $store_category = isset($store_info['category']) ? $store_info['category'] : '';

        /**
         * 14082【BY｜TH/LA】NW资产耗材权限与审批流优化
         * TH总部员工申请资产/耗材传输scm地址优化
         * 针对总部申请人，由原来写死的地址修改为获取总部地址第一条数据，并将详细地址固化
         */
        $headOffice=[];
        $pc_code = '';
        if($uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2']) {
            //获取总部地址
            $headOffice =  $this->wms->getHeadquartersAddress();
            /**
             * 14771【BY-TH/PH/MY/LA】申请物料、申请资产和SCM接口逻辑优化
             * 总部员工申请资产/耗材时，需要记录成本中心
             */
            $pc_code = $this->wms->getPcCode($staffInfo);
        }
        //格式化订单数据
        $serialNo = uniqid('as');
        $returnData['data']['dataList'] = [];
        $orderData['order_union_id'] = $serialNo;//订单号
        $orderData['organization_id'] = $organization_id ;//网点编号
        $orderData['pc_code'] = $pc_code;//成本中心
        $orderData['staff_info_id'] = $userinfo['staff_id'];
        $orderData['status'] = 1;
        $orderData['created_at'] = $create;
        $orderData['reason'] = $paramIn['reason'];//原因
        $orderData['shipping_user'] = $uInfoArr['name'];//收货人姓名
        $orderData['consignee_phone'] = $staffInfo && isset($staffInfo['mobile']) ? $staffInfo['mobile'] : '';
        $orderData['consignee_address'] = $uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2'] ? $headOffice['address'] : $uInfoArr['detail_address'];
        $orderData['province_code'] = $uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2'] ? $headOffice['province_code'] : $uInfoArr['province_code'];//省
        $orderData['city_code'] = $uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2'] ? $headOffice['city_code']: $uInfoArr['city_code'];//市
        $orderData['postal_code'] = $uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2'] ? $headOffice['postal_code'] : $uInfoArr['postal_code'];//邮编
        $orderData['district_code'] = $uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2'] ? $headOffice['district_code'] :$uInfoArr['district_code'];//区
        $orderData['use_people_id'] = empty($paramIn['use_people_id']) ? 0 : $paramIn['use_people_id'];
        $orderData['use_store_id'] = empty($paramIn['use_store_id']) ? '' : $paramIn['use_store_id'];
        if (empty($paramIn['assets'])) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }
        $orderDetailData = [];
        $assetIds = array_column($paramIn['assets'], "id");
        // 根据用户的权限 过滤 不合法的ID
        list($assetType, $assetIds) = $this->filterAssetsIds($assetIds, $userinfo);
        if (!$assetIds) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        //获取资产
        $goodsInfo = $this->goodsInfo($assetIds);
        if (empty($goodsInfo)) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        foreach ($goodsInfo as $key => $value) {
            $orderDetailData[$value['id']] = $value;
        }

        $orderDetailDataFormat = array();
        foreach ($paramIn['assets'] as $key => $value) {

            if (!isset($value['nums']) || $value['nums'] <= 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('7101'));
            }
            if (isset($orderDetailData[$value['id']])) {
                $orderDetailDataFormat[$key]['goods_id'] = isset($value['id']) ? $value['id'] : 0;
                $orderDetailDataFormat[$key]['recomment_num'] = isset($value['nums']) ? $value['nums'] : 0;
                //$orderDetailDataFormat[$key]['approval_num'] = isset($value['nums']) ? $value['nums'] : 0;
                $orderDetailDataFormat[$key]['created_at'] = $create;
            }
        }


        $db = WorkflowModel::beginTransaction($this);
        try {
            $orderId = $this->audit->addOrderInfo($orderData, $orderDetailDataFormat, $assetType);
            if (!$orderId) {
                $this->getDI()->get('logger')->write_log('assets_order_info =>' . json_encode($paramIn) . json_encode($userinfo), 'error');
                return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
            }

            $assetData = UC('wmsRole')['assetApprover'];
            $returnData['data'] = $orderId;
            $staffId = $uInfoArr['id'];
            $storeId = $uInfoArr['organization_type'] == 2 ? '-1' : $uInfoArr['organization_id'];
            $server = new \FlashExpress\bi\App\Server\AuditListServer($this->lang, $this->timezone);
            $summary = $server->generateSummary($orderId, 16);
            $summary = json_encode($summary, JSON_UNESCAPED_UNICODE);
            $insertAuditData[] = [
                'id_union' =>  $assetType == self::ASSET_TYPE[0] ? 'as_' . $orderId : 'asp_' . $orderId,
                'staff_id_union' => $staffId,
                'type_union' => $assetType == self::ASSET_TYPE[0] ? enums::$audit_type['AS'] : enums::$audit_type['ASP'],
                'status_union' => 101,
                'store_id' => $storeId,
                'table' => 'assets_order',
                'created_at' => gmdate('Y-m-d H:i:s', time()),
                'origin_id' => $orderId,
                'summary' => $summary,
                'approval_id' => 0,
            ];
            $info = $this->staff->getStaffPositionv3($staffId);
	        //用人部门ID查找到公司
	        $dInfo = SysDepartmentModel::findFirst([
		                                               'conditions' => 'id = :department_id:',
		                                               'bind' => [
			                                               'department_id' => $info['department_id'],
		                                               ]
	                                               ]);
            if (!($this->other->insterUnion($insertAuditData) &&
                (new ApprovalServer($this->lang, $this->timezone))->create($orderId,
                    $assetType == self::ASSET_TYPE[0] ? enums::$audit_type['AS'] : enums::$audit_type['ASP'],
                    $userinfo['staff_id']
                    , null, ['flow_code' => 'v1', 'category' => $info['category'] ?? '', 'company_id' => $dInfo ? $dInfo->company_id : ''])) // 泰国迁移审批流 https://l8bx01gcjr.feishu.cn/sheets/shtcnmxAm93nEa8Zs5EZMQRykQh?sheet=oNVgMs
            ) {
                throw new \Exception('create workflow failing');
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('assets_order_info =>' . json_encode($paramIn) . json_encode($userinfo), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }
        return $this->checkReturn($returnData);
    }

    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $workflowParams = parent::getWorkflowParams($auditId, $user, $state); // TODO: Change the autogenerated stub
        $info = $this->staff->getStaffPositionv3($workflowParams['staff_info_id']);
        //用人部门ID查找到公司
        $dInfo = SysDepartmentModel::findFirst([
            'conditions' => 'id = :department_id:',
            'bind' => [
                'department_id' => $info['department_id'],
            ]
        ]);

        $workflowParams['organization_type'] = $info['organization_type'] ?? '';
        $workflowParams['category'] = $info['category'] ?? '';
        $workflowParams['company_id'] = $dInfo ? $dInfo->company_id : '';

        return $workflowParams;
    }

    /**
     * 获取资产盘点-资产清单-单项资产（个人、公共）总数
     * @param array $params['inventory_check_id'=>'盘点单','staff_id'=>'员工工号'] 查询条件
     * @return mixed
     */
    public function getMySingleAssetsCount($params)
    {
        $lang = substr(strtolower($this->lang), 0, 2);
        $lang = empty($lang) ? 'zh' : $lang;
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['ai' => AssetsInfoModel::class]);
        $builder->leftjoin(AssetsGoodsModel::class, "ai.assets_goods_id=ag.id", "ag");
        $builder = $this->getSingleCondition($builder, $params, $lang);
        return $builder->getQuery()->execute()->count();
    }

    /**
     * 获取资产盘点-资产清单-单项资产（个人、公共）列表
     * @param array $params['inventory_check_id'=>'盘点单','staff_id'=>'员工工号', 'goods_name'=>'资产名称'] 查询条件
     * @return array
     */
    public function getMySingleAssetsList($params)
    {
        $page_size = $params['page_size'];
        $page_offset  = $params['page_offset'];
        $data = [
            'items'      => [],
            'pagination' => [
                'page_offset' => intval($page_offset),
                'per_page'     => intval($page_size),
                'total_count'  => 0,
            ],
        ];

        $items = [];
        //获取单项资产的数量
        $count = $this->getMySingleAssetsCount($params);
        if ($count > 0) {
            $lang = substr(strtolower($this->lang), 0, 2);
            $lang = empty($lang) ? 'zh' : $lang;
            //没有语言 默认英文
            $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);

            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'ai.id as assets_info_id',
                'ai.asset_code',
                'ai.sn_code',
                'ai.assets_goods_id',
                'ag.bar_code',
                'ag.is_public',
                'ag.is_batch',
                'ag.goods_name_'.$lang.' as goods_name',
                'ag.goods_name_en',
                'ag.goods_name_th',
                'ag.goods_name_zh',
            ]);
            $builder->from(['ai' => AssetsInfoModel::class]);
            $builder->leftjoin(AssetsGoodsModel::class, "ai.assets_goods_id=ag.id", "ag");
            $builder = $this->getSingleCondition($builder, $params, $lang);
            //修改为由前端控制计算offset值
            $builder->limit($page_size, $page_offset);
            $builder->orderby('ai.id');
            $items = $builder->getQuery()->execute()->toArray();
        }
        $data['items']                     = $items ?? [];
        $data['pagination']['total_count'] = $count;
        return $data;
    }

    /**
     * 组装单项资产查询条件
     * @param object $builder 查询器对象
     * @param array $condition['inventory_check_id'=>'盘点单','staff_id'=>'员工工号', 'goods_name'=>'资产名称'] 筛选条件组
     * @param string $lang 当前语言包
     * @return mixed
     */
    private function getSingleCondition($builder, $condition, $lang)
    {
        $builder->where('ai.staff_info_id = :staff_info_id: and ai.state = :state: and ag.is_batch=:is_batch:', ['staff_info_id'=>$condition['staff_id'], 'state'=>AssetsInfoEnums::STATE_OUT, 'is_batch'=>AssetsGoodsEnums::IS_BATCH_NO]);
        //需要过滤掉已经盘点过的单项资产
        $has_check_assets_list = (new InventoryCheckServer())->getHasCheckAssets($condition['inventory_check_id'], $condition['staff_id'], AssetsGoodsEnums::IS_BATCH_NO);
        if ($has_check_assets_list) {
            //存在已经盘点过的单项资产
            $assets_info_ids = array_column($has_check_assets_list, 'assets_info_id');
            $builder->andWhere('ai.id not in ({ids:array})', ['ids'=>$assets_info_ids]);
        }
        if (isset($condition['goods_name']) && mb_strlen($condition['goods_name']) > 0) {
            //资产名称搜索
            $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);
            $builder->andWhere('ag.goods_name_'.$lang.' like :goods_name:', ['goods_name'=>'%'.$condition['goods_name'].'%']);
        }
        return $builder;
    }

    /**
     * 获取资产盘点-资产清单-多项资产（公共）总数
     * @param array $params['inventory_check_id'=>'盘点单','staff_id'=>'员工工号'] 查询条件
     * @return int|mixed
     */
    public function getMyMultipleAssetsCount($params)
    {
        $count = 0;
        $lang = substr(strtolower($this->lang), 0, 2);
        $lang = empty($lang) ? 'zh' : $lang;
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(ag.id) as count');
        $builder->from(['ai' => AssetsInfoModel::class]);
        $builder->leftjoin(AssetsGoodsModel::class, "ai.assets_goods_id=ag.id", "ag");
        $builder = $this->getMyMultipleCondition($builder, $params, $lang);
        if ($builder === false) return $count;
        $data = $builder->getQuery()->execute()->getFirst();
        if (!empty($data)) {
            $data = $data->toArray();
            $count = $data['count'];
        }
        return $count;
    }

    /**
     * 获取资产盘点-资产清单-多项资产（公共）列表
     * @param array $params['inventory_check_id'=>'盘点单','staff_id'=>'员工工号', 'goods_name'=>'资产名称'] 查询条件
     * @return array
     */
    public function getMyMultipleAssetsList($params)
    {
        $page_size = $params['page_size'];
        $page_offset  = $params['page_offset'];
        $data = [
            'items'      => [],
            'pagination' => [
                'page_offset' => intval($page_offset),
                'per_page'     => intval($page_size),
                'total_count'  => 0,
            ],
        ];
        $items = [];
        //获取多项资产的数量
        $count = $this->getMyMultipleAssetsCount($params);
        if ($count > 0) {
            $lang = substr(strtolower($this->lang), 0, 2);
            $lang = empty($lang) ? 'zh' : $lang;
            //没有语言 默认英文
            $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);

            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'count(ag.id) as sys_asset_num',
                'ag.bar_code',
                'ag.id as assets_goods_id',
                'ag.is_public',
                'ag.is_batch',
                'ag.goods_name_'.$lang.' as goods_name',
                'ag.goods_name_en',
                'ag.goods_name_th',
                'ag.goods_name_zh',
            ]);
            $builder->from(['ai' => AssetsInfoModel::class]);
            $builder->leftjoin(AssetsGoodsModel::class, "ai.assets_goods_id=ag.id", "ag");
            $builder = $this->getMyMultipleCondition($builder, $params, $lang);
            if ($builder === false) return $data;
            $builder->groupby('ag.id');
            //修改为由前端控制计算offset值
            $builder->limit($page_size, $page_offset);
            $builder->orderby('assets_goods_id');
            $items = $builder->getQuery()->execute()->toArray();
        }
        $data['items']                     = $items ?? [];
        $data['pagination']['total_count'] = $count;
        return $data;
    }

    /**
     * 组装多项资产查询条件
     * @param object $builder 查询器对象
     * @param array $condition['inventory_check_id'=>'盘点单','staff_id'=>'员工工号', 'goods_name'=>'资产名称'] 筛选条件组
     * @param string $lang 当前语言包
     * @return mixed
     */
    private function getMyMultipleCondition($builder, $condition, $lang)
    {
        $assets_info_list = $this->getMyAssetsTransferLogList($condition['staff_id']);
        if (!$assets_info_list) return false;
        $assets_info_ids = array_column($assets_info_list, "assets_id");
        $builder->where('ai.id in ({ids:array})', ['ids'=>$assets_info_ids]);
        //需要过滤掉已经盘点过的多项资产
        $has_check_assets_list = (new InventoryCheckServer())->getHasCheckAssets($condition['inventory_check_id'], $condition['staff_id'], AssetsGoodsEnums::IS_BATCH_YES);
        if ($has_check_assets_list) {
            //存在已经盘点过的多项资产
            $assets_goods_id = array_column($has_check_assets_list, 'assets_goods_id');
            $builder->andWhere('ag.id not in ({goods_ids:array})', ['goods_ids'=>$assets_goods_id]);
        }
        if (isset($condition['goods_name']) && mb_strlen($condition['goods_name']) > 0) {
            //没有语言 默认英文
            $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);
            //资产名称搜索

            $builder->andWhere('ag.goods_name_'.$lang.' like :goods_name:', ['goods_name'=>'%'.$condition['goods_name'].'%']);
        }
        return $builder;
    }

    /**
     * 获取确实在我名下的多项资产记录
     * @param integer $staff_id 员工工号
     * @return array
     */
    private function getMyAssetsTransferLogList($staff_id)
    {
        $assetsLog = [];
        $list = $this->getMyAllMultipleList($staff_id);
        if ($list) {
            $assetsIdArr = array_column($list, "assets_info_id");
            $assetsIds = implode(",", $assetsIdArr);
            //先查询名下最大id
            $first_sql = "-- 
            select max(id) as id from assets_info_log where transfer_staff_id = :transfer_id and assets_id in ({$assetsIds}) and transfer_state = 1 group by assets_id";

            $assets = $this->getDI()->get("db")->fetchAll($first_sql, Db::FETCH_ASSOC, [
                "transfer_id" => $staff_id
            ], [
                "assets_ids" => Db\Column::BIND_PARAM_STR,
                "transfer_id" => Db\Column::BIND_PARAM_INT
            ]);
            if(!empty($assets)) {
                $assets_arr = array_column($assets, 'id');
                $assets_ids = implode(",", $assets_arr);

                $sql       = " --
            select 
                id,staff_info_id,assets_id
            from 
                assets_info_log 
            where 
                id in ({$assets_ids}); ";
                $assetsLog = $this->getDI()->get("db")->fetchAll($sql, Db::FETCH_ASSOC);
            }
        }
        return $assetsLog;
    }

    /**
     * 获取我名下申请过的所有多项资产列表
     * @param integer $staff_id 员工工号
     * @return array
     */
    private function getMyAllMultipleList($staff_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['ai.id as assets_info_id']);
        $builder->from(['ai' => AssetsInfoModel::class]);
        $builder->leftjoin(AssetsGoodsModel::class, "ai.assets_goods_id=ag.id", "ag");
        $where = "ai.staff_info_id = :staff_info_id: AND ai.operate_status not in ({operate_status_arr:array}) AND ai.state=:state: AND ag.is_public = :is_public: AND ag.deleted = :deleted: AND ag.is_batch = :is_batch:";
        $values = [
            'staff_info_id'=>$staff_id,
            'operate_status_arr' => [AssetsInfoEnums::OPERATE_STATUS_TO_BE_RECEIVED, AssetsInfoEnums::OPERATE_STATUS_TRANSFER],
            'state'=>AssetsInfoEnums::STATE_OUT,
            'is_public' =>AssetsGoodsEnums::IS_PUBLIC,
            'deleted'=>AssetsGoodsEnums::IS_DELETED_NO,
            'is_batch'=>AssetsGoodsEnums::IS_BATCH_YES
        ];
        $builder->where($where, $values);
        $items = $builder->getQuery()->execute();
        return $items ? $items->toArray() : [];
    }

    /**
     * 根据资产名称获取资产列表（去重复的）
     * @param string $name 资产名称
     * @param integer $page_num 当前页数
     * @param integer $page_size 每页条数
     * @return array
     */
    public function getAssetListByName($name, $page_num, $page_size)
    {
        $data = [
            'items'      => [],
            'pagination' => [
                'page_num' => intval($page_num),
                'per_page'     => intval($page_size),
                'total_count'  => 0,
            ],
        ];
        $lang = substr(strtolower($this->lang), 0, 2);
        $lang = empty($lang) ? 'zh' : $lang;
        //没有语言 默认英文
        $lang = AssetsGoodsModel::getGoodsNameTranslate($lang);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['goods.goods_name_'.$lang.' as goods_name','goods.goods_name_en', 'goods.goods_name_th', 'goods.goods_name_zh', 'goods.bar_code']);
        $builder->from(['goods' => AssetsGoodsModel::class]);
        $builder->where('deleted = :deleted:', ['deleted'=>AssetsGoodsEnums::IS_DELETED_NO]);
        if($name) {
            $builder->andWhere('goods.goods_name_'.$lang.' like :name:', ['name' => '%'.$name.'%']);
        }
        $builder->groupby('goods.bar_code');
        $count = $builder->getQuery()->execute()->count();
        if($count > 0) {
            $builder->limit($page_size, ($page_num-1)*$page_size);
            $items = $builder->getQuery()->execute();
            $items = $items ? $items->toArray() : [];
            $data['items'] = $items;
        }
        $data['pagination']['total_count'] = $count;
        return $data;
    }

    /**
     * 检测单项资产是否在该员工名下
     * @param $params['asset_code'=>'资产编码', 'staff_id'=>'员工工号', 'inventory_check_id'=>'盘点单ID']查询条件
     * @return array|mixed
     * @throws ValidationException
     */
    public function isOwner($params)
    {
        $staff_id = $params['staff_id'];//员工工号
        $inventory_check_id = $params['inventory_check_id'];//盘点单ID
        $code = $params['asset_code'];//扫描出来的编码
        //根据资产编号或sn编号获取符合条件的资产列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'ai.id as assets_info_id',
            'ag.bar_code',
            'ai.asset_code',
            'ai.sn_code',
            'ai.assets_goods_id',
            'ag.is_public',
            'ag.is_batch',
            'ag.goods_name_en',
            'ag.goods_name_th',
            'ag.goods_name_zh',
        ]);
        $builder->from(['ai' => AssetsInfoModel::class]);
        $builder->leftjoin(AssetsGoodsModel::class, "ai.assets_goods_id=ag.id", "ag");
        $builder->where('ai.staff_info_id = :staff_info_id: and ai.state = :state: and ag.deleted = :deleted: and ag.is_batch=:is_batch:',
            ['staff_info_id'=>$staff_id, 'state'=>AssetsInfoEnums::STATE_OUT, 'deleted'=>AssetsGoodsEnums::IS_DELETED_NO, 'is_batch'=>AssetsGoodsEnums::IS_BATCH_NO]);
        $builder->andWhere('ai.asset_code = :code: or sn_code = :code:', ['code'=>$code]);
        $items = $builder->getQuery()->execute();
        $list=  $items ? $items->toArray() : [];
        //最终的结果
        $asset_info = [];
        //如果查到对应资产
        if (!empty($list)) {
            $asset_list_ids = array_column($list, null, "assets_info_id");
            //检索所有用户已盘点的单项资产清单
            $has_check_assets = (new InventoryCheckServer())->getHasCheckAssets($inventory_check_id, $staff_id, AssetsGoodsEnums::IS_BATCH_NO);
            if ($has_check_assets) {
                //查到的对应资产存在已盘点记录不可重复盘点
                $has_assets_info_ids = array_column($has_check_assets, "assets_info_id");
                foreach ($asset_list_ids as $assets_info_id => $info) {
                    if (!in_array($assets_info_id, $has_assets_info_ids)) {
                        //存在未盘点则直接返回
                        $asset_info = $info;
                        break;
                    }
                }
            } else {
                //还没有盘点过任何资产,讲首条资产信息返回
                $asset_info = $list[0];
            }
        } else {
            //在该员工名下没找到该资产信息，需要在该员工名下扫码新增资产盘点清单里过滤下该编码是否存在与资产编码或者sn码记录里
            $is_exists = (new InventoryCheckServer())->checkCodeAddAssetCodeOrSnCodeUnique($inventory_check_id, $staff_id, $code);
            if ($is_exists) {
                //该编号对应的资产均已盘点完毕
                throw new ValidationException($this->getTranslation()->_('repeat_inventory_check_info_error'));
            } else {
                //不再该员工名下且该员工也未扫码新增过该资产编码或sn码可以跳转到扫码新增
                throw new ValidationException($this->getTranslation()->_('asset_not_in_your_name'), 2);
            }
        }
        if ($asset_info) {
            return $asset_info;
        } else {
            //该编号对应的资产均已盘点完毕
            throw new ValidationException($this->getTranslation()->_('repeat_inventory_check_info_error'));
        }
    }
}