<?php


namespace FlashExpress\bi\App\Modules\Th\Server;

use App\Country\Tools;
use app\enums\LangEnums;
use Exception;
use FlashExpress\bi\App\Enums\InteriorGoodsEnums;
use FlashExpress\bi\App\Enums\InteriorOrderFundStatusEnums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Enums\InteriorGoodsPayMethodEnums;
use FlashExpress\bi\App\Enums\InteriorGoodsStatusEnums;
use FlashExpress\bi\App\Enums\InteriorOrderAuditedEnums;
use FlashExpress\bi\App\Enums\InteriorOrderStatusEnums;
use FlashExpress\bi\App\Enums\ReturnMsgEnums;
use FlashExpress\bi\App\Models\backyard\HeadquartersAddressModel;
use FlashExpress\bi\App\Models\backyard\InteriorDepartmentModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsSkuLogModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsSkuModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersGoodsSkuModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersModel;
use FlashExpress\bi\App\Models\backyard\InteriorStaffsShoppingCartModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Models\backyard\SysDistrictModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\oa\SysDepartmentPcCode;
use FlashExpress\bi\App\Repository\InteriorGoodsRepository;
use FlashExpress\bi\App\Repository\StoreRepository;
use FlashExpress\bi\App\Repository\WmsRepository;
use FlashExpress\bi\App\Models\backyard\SysStoreGoodsModel;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\FlashPayServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\InteriorGoodsServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SyncWarehoseServer;
use FlashExpress\bi\App\Server\CsrfTokenServer;

class InteriorGoodsServer extends GlobalBaseServer
{
    const IS_FREE = 1;//免费
    const IS_NO_FREE = 0;
    const FREE_COUNT_NUB = 1; //限免个数

    private static $hire_limit_days = 7;


    //by-员工商城-商品列表
    public static $validate_goods_list = [
        'goods_type' => 'IntIn:' . InteriorGoodsEnums::GOODS_TYPE_ORDER_ALL . ',' . InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES . ',' . InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE . '|>>>:params error goods_type', //商品类型,
        'goods_name' => 'StrLenGeLe:1,80|>>>:goods_name error',
        'page'       => 'IntGt:0',
        'limit'      => 'IntGt:0',
    ];

    /**
     * 获取员工可买商品类别
     * @param array $userInfo
     * @return array|string[]
     */
    public static function getUserEnableBuyGoodsCate(array $userInfo)
    {
        $storeCateCode = HrStaffInfoServer::getStaffStoreCateCode($userInfo);
        (new BaseServer())->wLog('StoreCateCode', $storeCateCode);
        if (isset($storeCateCode['msg'])) {
            return $storeCateCode;
        }
        $goods_ids = (new InteriorGoodsRepository())->getInteriorGoodsStoreCateRelAll($storeCateCode);
        (new BaseServer())->wLog('log_goods_ids:', $goods_ids);
        return $goods_ids;
    }

    /**
     * @param $goodsId
     * @return mixed
     */
    private static function _getUserEnableBuyGoods($goodsId)
    {
        $goodsStatusArr = [
            InteriorGoodsStatusEnums::GOODS_STATUS_ON_SALE
        ];
        $conditions     = " status in ({status:array}) and id = :id:";
        $bind           = ['status' => $goodsStatusArr, 'id' => $goodsId];

        $goodsInfo = InteriorGoodsModel::findFirst(
            array(
                'conditions' => $conditions,
                'bind'       => $bind,
            )
        );
        return $goodsInfo;
    }

    /**
     * 确认提交订单
     * @param array $loginUser
     * @param array $params
     * @return array|string[]
     */
    public function submitOrder(array $loginUser, array $params)
    {
        $this->getDI()->get('logger')->write_log("InteriorOrder=submit_order==login_user" . json_encode($loginUser, JSON_UNESCAPED_UNICODE) . 'time' . time() . '; submit params => ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        $db = InteriorOrdersModel::beginTransaction($this);
        try {
            $orderRemark       = '';
            $goodsIdsArr       = $params['buy_goods_ids_arr'];
            $isPreSale         = $params['is_pre_sale'];
            $platform          = $params['platform'] ?? '';
            $staffId           = $loginUser['staff_id'];
            $provinceCode      = $params['province_code'];
            $cityCode          = $params['city_code'];
            $districtCode      = $params['district_code'];
            $address           = $params['detail_address'];
            $postalCode        = $params['postal_code'];
            $storeId           = $params['staff_store_id']; // 前端传网点编号或总部id收货地址ID
            $userInfo          = HrStaffInfoServer::getUserInfoByStaffInfoId($staffId);
            $userInfo          = $userInfo->toArray();
            $provinceArr       = SysProvinceModel::getProvincesArrByCodeArr([$provinceCode], 'code,name');
            $cityArr           = SysCityModel::getCitiesArrByCodeArr([$cityCode], 'code,name');
            $districtArr       = SysDistrictModel::getDistrictsArrByCodeArr([$districtCode], 'code,name');
            $province          = trim($provinceArr[$provinceCode]['name'] ?? '');
            $city              = trim($cityArr[$cityCode]['name'] ?? '');
            $district          = trim($districtArr[$districtCode]['name'] ?? '');

            if (!$userInfo['mobile']) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_mobile_not_null'));
            }
            if (!$province) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_province_not_null'));
            }
            if (!$city) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_city_not_null'));
            }
            if (!$district) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_district_not_null'));
            }
            if (!$address) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_address_not_null'));
            }
            if (!$postalCode) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_postalcode_not_null'));
            }

            $this->authorityCheck($userInfo, $params);
            //如果是购物车提交的订单 删除购物对应的订单 ，1 表示购物车过来 0表示直接下单
            if ($params['source'] == 1) {
                $this->delShoppingCart($userInfo, $params);
            }

            //工服和无头件不可以混合提交 & 获取提交的订单商品类型
            $goods_type = $this->unclaimedValidation($params);

            // FFM商品校验逻辑，获取货主配置信息
            $ffmOwnerConfig = $this->validateFfmGoods($params, $storeId);

            //出库单接口中nodeSn传参逻辑
            $nodeSn = $this->getNodeSn($goods_type, $userInfo, $storeId);

            //一个订单支付方式只能有一种
            $new_goods_stock = [];
            //如果是无头件时时查询库存
            if ($goods_type == InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
                $sku_code_str    = implode(',', array_column($params['buy_goods_ids_arr'], 'goods_sku_code'));
                $new_goods_stock = $this->getGoodsBarcodeStock($sku_code_str, $goods_type);
                if (empty($new_goods_stock)) {
                    throw new ValidationException($this->getTranslation()->_('interior_goods_available_inventory'));
                }
            }

            // 多个商品生成一个订单对应多个商品
            $orderCode = $this->generateOrderCode();
            $currentTime = date('Y-m-d H:i:s');
            $total_num = 0;//购买总数
            $totalPayAmount = 0;
            $syncWmsPostData = $goods = $goods_flash_pay = [];
            //用于区分此次免费与非免费提交（0非免费，>0免费）
            $is_free_order_goods_num = 0;
            $_thisByNum = 0;

            $this->getDI()->get('logger')->write_log("InteriorOrder=submit_order=node_sn_2" . $orderCode . '_' . $nodeSn . 'time' . time(), 'info');

            foreach ($goodsIdsArr as $k => $reqItems) {
                $goodsId           = $reqItems['goods_id'];
                $skuId             = $reqItems['goods_sku_id'];
                $buyNum            = $reqItems['buy_num'];
                $barcode           = $reqItems['goods_sku_code'];
                if ($buyNum <= 0) {
                    continue;
                }

                $goodsSkuObj = $this->getGoodsSkuInfo($goodsId, $skuId, true);
                if (!$goodsSkuObj) {
                    throw new ValidationException(LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_goods_was_not_found));
                }
                $price = $goodsSkuObj->price;
                // 计算订单总金额
                if (isset($reqItems['is_free']) && self::IS_FREE == $reqItems['is_free'] && $goods_type == InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES) {
                    $price = 0;
                }
                $amount         = sprintf('%.2f', $price * $buyNum);
                $totalPayAmount += $amount;

                if ($goods_type == InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES) {
                    // 修改后库存
                    $toSurplusNum = $goodsSkuObj->surplus_num - $buyNum;
                    if (!$isPreSale && $toSurplusNum < 0) {
                        throw new ValidationException(LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_inventory_not_enough));
                    }
                    if ($toSurplusNum < 0) {
                        throw new ValidationException($this->getTranslation()->_('interior_goods_available_inventory'));
                    }

                    // 修改库存
                    $goodsSkuObj->sale_num += $buyNum;

                    // 记录库存修改日志
                    self::saveSkuSurplusNumAndLog($goodsSkuObj, $staffId, $buyNum, $toSurplusNum, 'staff_buy');
                } elseif ($goods_type == InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
                    //无头件校验每个库存是否充足
                    if ($new_goods_stock[$barcode] < $buyNum) {
                        throw new ValidationException($this->getTranslation()->_('interior_goods_available_inventory'));
                    }
                }

                $order_goods_sku_data = [];
                if (self::IS_NO_FREE == $reqItems['is_free']) {
                    $_thisByNum += $buyNum;
                }
                $total_num += $buyNum;

                //加入订单商品表
                $orderGoodsSkuObj = new InteriorOrdersGoodsSkuModel();
                if (isset($reqItems['is_free']) && self::IS_FREE == $reqItems['is_free']) {
                    $order_goods_sku_data['is_free'] = 1;
                    $is_free_order_goods_num++;
                }

                $order_goods_sku_data['order_code']           = $orderCode;
                $order_goods_sku_data['goods_id']             = $goodsSkuObj->goods_id;
                $order_goods_sku_data['goods_sku_id']         = $goodsSkuObj->id;
                $order_goods_sku_data['goods_sku_code']       = $goodsSkuObj->goods_sku_code;
                $order_goods_sku_data['goods_name_en']        = $goodsSkuObj->goods_name_en;
                $order_goods_sku_data['goods_name_th']        = $goodsSkuObj->goods_name_th;
                $order_goods_sku_data['goods_name_zh']        = $goodsSkuObj->goods_name_zh;
                $order_goods_sku_data['img_path']             = $goodsSkuObj->img_path;
                $order_goods_sku_data['attr_1']               = $goodsSkuObj->attr_1;
                $order_goods_sku_data['attr_2']               = $goodsSkuObj->attr_2;
                $order_goods_sku_data['unit_en']              = $goodsSkuObj->unit_en;
                $order_goods_sku_data['unit_th']              = $goodsSkuObj->unit_th;
                $order_goods_sku_data['unit_zh']              = $goodsSkuObj->unit_zh;
                $order_goods_sku_data['unit_num']             = $goodsSkuObj->unit_num;
                $order_goods_sku_data['buy_price']            = $price;
                $order_goods_sku_data['buy_num']              = $buyNum;
                $order_goods_sku_data['pay_amount']           = $amount;
                $order_goods_sku_data['current_sku_pre_sale'] = 0;
                $success                                      = $db->insertAsDict(
                    $orderGoodsSkuObj->getSource(),
                    $order_goods_sku_data
                );
                if ($success === false) {
                    throw new BusinessException('订单提交 - interior_orders_goods_sku 写入失败, data = ' . json_encode($order_goods_sku_data, JSON_UNESCAPED_UNICODE), ErrCode::DATABASE_ERROR);
                }
                $goods[] = [
                    'i'             => $k,
                    'barCode'       => $goodsSkuObj->goods_sku_code,
                    'goodsName'     => $goodsSkuObj->goods_name_th,
                    'specification' => $goodsSkuObj->attr_1,
                    'num'           => $buyNum,
                    'price'         => ($price * 100),//出库商品json，其中price的单位为萨当，即1泰铢=100萨当
                    'remark'        => $amount
                ];

                //用于向FlashPay在线支付传递的参数
                $goods_flash_pay[] = [
                    'goodsId'   => $goodsSkuObj->goods_sku_code,//商品的barcode
                    'goodsName' => $goodsSkuObj->goods_name_en,//商品的名称
                    'quantity'  => intval($buyNum),//商品数量
                    'price'     => intval($price * 100),//商品的单价*100
                ];
            }

            // 生成订单数据,付费的订单状态默认为待付款，支付方式，免费的订单状态为待发货
            if ($is_free_order_goods_num > 0) {
                //免费商品的订单，状态为待发货;支付方式为工资抵扣;订单类型为免费
                $orderStatus     = InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE;
                $payMethod       = InteriorGoodsPayMethodEnums::PAY_METHOD_DEDUCTION_OF_WAGES_CODE;
                $orderType       = InteriorOrderStatusEnums::ORDER_TYPE_FREE;
                $order_expire_at = null;
            } else {
                //非免费商品的订单，状态为待付款;支付方式为FlashPay在线支付;订单类型为自费
                $orderStatus     = InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE;
                $payMethod       = InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE;
                $orderType       = InteriorOrderStatusEnums::ORDER_TYPE_OWN_PAY;
                $order_expire_at = $this->getOrderExpireTime($currentTime);
            }

            // 工服
            if ($goods_type == InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES) {
                $goodsIds         = array_column($goodsIdsArr, 'goods_id');
                $has_buy_free_num = $this->getUserFreeByNumNew($loginUser);
                $goodsList        = InteriorGoodsModel::find([
                    'conditions' => 'id in({goods_id:array}) and free_num >0',
                    'bind'       => ['goods_id' => $goodsIds],
                    'columns'    => ['id', 'free_num']
                ]);
                if (empty($goodsList)) {
                    throw new ValidationException($this->getTranslation()->_('interior_goods_data_not_null'));
                }
                $goodsList      = $goodsList->toArray();
                $free_goods_ids = array_column($goodsList, 'id');

                $myDataArr = [
                    'loginUser'        => $loginUser,
                    'goodsIdsArr'      => $goodsIdsArr,//提交的数据
                    'freeBuyNum'       => $total_num,//本次购买总数
                    'has_buy_free_num' => $has_buy_free_num,//已经购买免费数
                    'goodsIds'         => $goodsIds,//本费不免费的数据
                    'free_goods_ids'   => $free_goods_ids //限免goods_id
                ];
                $res_rs    = $this->getStaffBuyAuthNew($myDataArr);
                if (isset($res_rs['msg'])) {
                    throw new ValidationException($res_rs['msg']);
                }

                if ($this->checkStaffIsFirstOrder($staffId)) {
                    $syncWmsPostData['markName'] = 'FirstOrder';
                }
            }

            $order_data = [
                'staff_id'               => $staffId,
                'node_department_id'     => $userInfo['node_department_id'] ?: $userInfo['sys_department_id'],//子部门ID
                'staff_name'             => $userInfo['name'],
                'staff_mobile'           => $userInfo['mobile'],
                'staff_store_id'         => $userInfo['sys_store_id'], //员工属性的总部ID或者所属网点ID
                'receive_store_id'       => $storeId,//员工下单时提交过来的收货地址ID（总部地址ID或网点ID）
                'receive_province_name'  => $province,
                'receive_city_name'      => $city,
                'receive_district_name'  => $district,
                'receive_address'        => $address,
                'receive_postal_code'    => $postalCode,
                'order_code'             => $orderCode,
                'pay_amount'             => $totalPayAmount,
                'pay_method'             => $payMethod,
                'submit_at'              => $currentTime,
                'order_expire_at'        => $order_expire_at,
                'order_status'           => $orderStatus,
                'order_type'             => $orderType,
                'delivery_way'           => $ffmOwnerConfig ? 'self' : 'express',
                'out_status'             => InteriorOrderStatusEnums::OUT_STATUS_INIT,
                'remark'                 => "GF_order：【staff_id：{$staffId}】" . $orderRemark,
                'created_at'             => $currentTime,
                'updated_at'             => $currentTime,
                'node_sn'                => $nodeSn,
                'goods_type'             => $goods_type,
                'order_source'           => !empty($params['source']) ? $params['source'] : 1,
                'mach_code'              => $ffmOwnerConfig ? $ffmOwnerConfig['mach_code'] : env('wms_mchId'),//记录FFM或EXPRESS货主
            ];

            $orderObj = new InteriorOrdersModel();
            $success    = $db->insertAsDict(
                $orderObj->getSource(),
                $order_data
            );
            if ($success === false) {
                throw new BusinessException('订单提交 - interior_orders 写入失败, data = ' . json_encode($order_data, JSON_UNESCAPED_UNICODE), ErrCode::DATABASE_ERROR);
            }
            $orderObj->id = $db->lastInsertId();

            // 向wms同步出库订单,如果有预售就不往仓储同步
            $_orderObj = InteriorOrdersModel::findFirst([
                'conditions' => 'id = ' . $orderObj->id
            ]);

            // 免费的直接scm出库
            if ($orderType == InteriorOrderStatusEnums::ORDER_TYPE_FREE) {
                $syncWmsPostData['nodeSn']             = $nodeSn;
                $syncWmsPostData['consigneeName']      = $userInfo['name'];
                $syncWmsPostData['consigneePhone']     = $userInfo['mobile'];
                $syncWmsPostData['province']           = $province;
                $syncWmsPostData['city']               = $city;
                $syncWmsPostData['district']           = $district;
                $syncWmsPostData['postalCode']         = $postalCode;
                $syncWmsPostData['consigneeAddress']   = $address;
                $syncWmsPostData['orderSn']            = $orderCode;
                $syncWmsPostData['node_department_id'] = $order_data['node_department_id'];
                $syncWmsPostData['goods']              = json_encode($goods, JSON_UNESCAPED_UNICODE);
                $syncWmsPostData['remark']             = "GF_order：【staff_id：{$staffId}】" . $orderRemark;
                $syncWmsPostData['lang']               = $this->lang;

                //V22367 FFM货主则需传递分仓配置货主、仓库、配送方式自提；快递仍为原有逻辑不变
                $syncWmsPostData['mchId'] = $order_data['mach_code'];
                if ($ffmOwnerConfig) {
                    $syncWmsPostData['warehouseId'] = $ffmOwnerConfig['stock_id'];
                    $syncWmsPostData['deliveryWay'] = 'self';//自提
                } else {
                    $syncWmsPostData['warehouseId'] = $this->getGoodsTypeStockId($goods_type);
                    $syncWmsPostData['deliveryWay'] = 'express';//快递
                }

                if (!$syncWmsPostData['warehouseId']) {
                    throw new ValidationException($this->getTranslation()->_('interior_goods_stock_unset'));
                }

                $syncWarehoseServer = new SyncWarehoseServer();
                $res = $syncWarehoseServer->syncAddOrderToWmsReturnWarehouseAdd($syncWmsPostData);
                if (empty($res) || $res['code'] != 1 || !$res['data']) {
                    $_orderObj->fail_num    = 1;
                    $_orderObj->out_status  = InteriorOrderStatusEnums::OUT_STATUS_FAILED;//出库单失败，但有可能是超时失败，后续重试
                    $_orderObj->fail_reason = $res['msg'] ?? '';
                } else {
                    $_orderObj->out_sn = $res['data'];
                    $_orderObj->out_status = InteriorOrderStatusEnums::OUT_STATUS_SUCCESS;//出库成功
                    $_orderObj->remark = $_orderObj->remark . "【wms_out_sn：{$res['data']}】";
                }
                $_orderObj->is_audited = $_orderObj->is_audited ?? 0;
                $_orderObj->audited_desc = $_orderObj->audited_desc ?? '';
                $_orderObj->out_sn = $_orderObj->out_sn ?? '';
                if ($_orderObj->save() === false) {
                    throw new BusinessException('订单提交interior_orders 修改失败, data = ' . json_encode($_orderObj->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::DATABASE_ERROR);
                }
            } else if ($orderType == InteriorOrderStatusEnums::ORDER_TYPE_OWN_PAY) {
                $flash_pay_method = InteriorOrderStatusEnums::FLASH_PAY_METHOD_H5;

                $create_order_param = [
                    'staff_id'         => $staffId,
                    'order_code'       => $orderCode,
                    'order_submit_at'  => $currentTime,
                    'order_expire_at'  => $order_expire_at,
                    'order_pay_amount' => $totalPayAmount,
                    'goods_flash_pay'  => $goods_flash_pay,
                    'platform'         => $platform,
                    'goods_type'       => $goods_type,
                    'mach_code'        => $_orderObj->mach_code,
                ];

                //自费的订单提交需要走FlashPay在线支付调取收银台接口，在支付成功的情况下才向wms同步出库单并将订单状态变更为待发货
                $flash_pay_create_order_result = $this->getFlashPayCreateOrderResult($create_order_param, InteriorOrderStatusEnums::FLASH_PAY_METHOD_H5);
                if ($flash_pay_create_order_result['code'] == 0) {
                    $flash_pay_data = $flash_pay_create_order_result['data'];
                    //记录pay交易号
                    $_orderObj->flash_pay_code = !empty($flash_pay_data['tradeNo']) ? $flash_pay_data['tradeNo'] : '';
                    $_orderObj->save();
                } else {
                    throw new ValidationException($flash_pay_create_order_result['message'], $flash_pay_create_order_result['code']);
                }
            }

            $db->commit();

            $payMethodTxt = InteriorGoodsPayMethodEnums::getCodeTxtMap();
            return [
                'orde_code'            => $orderCode,
                'total_pay_amount'     => $totalPayAmount,
                'total_pay_amount_fmt' => InteriorGoodsEnums::fmtAmount($totalPayAmount, $this->lang),
                'pay_method'           => $payMethod,
                'pay_method_txt'       => $payMethodTxt[$payMethod],
                'order_created_at'     => $currentTime,
                'flash_pay_method'     => $flash_pay_method ?? '',
                'flash_pay_data'       => $flash_pay_data ?? []
            ];
        } catch (ValidationException $e) {
            $db->rollback();
            $this->wLog('submitOrder', $e->getFile()
                . 'line' . $e->getLine()
                . 'message' . $e->getMessage()
                . 'trace' . $e->getTraceAsString(), "warning");
            return [
                'msg' => $e->getMessage()
            ];
        } catch (BusinessException $e) {
            $db->rollback();
            $this->wLog('submitOrder', $e->getFile()
                . 'line' . $e->getLine()
                . 'message' . $e->getMessage()
                . 'trace' . $e->getTraceAsString(), 'warning');

            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_creation_failed)
            ];
        } catch (\Exception $e) {
            $db->rollback();
            $this->wLog('submitOrder', $e->getFile()
                . 'line' . $e->getLine()
                . 'message' . $e->getMessage()
                . 'trace' . $e->getTraceAsString(), 'error');
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_creation_failed)
            ];
        }


    }

    /**
     * 获取订单列表
     * Created by: Lqz.
     * @param array $loginUser
     * @param array $params
     * @return array
     * CreateTime: 2020/8/10 0010 14:43
     */
    public function getOrderList(array $loginUser, array $params)
    {
        $page         = $params['page'];
        $limit        = $params['limit'];
        $staffId      = $loginUser['staff_id'];
        $conditions   = "staff_id = :staffId: ";
        $bind         = ["staffId" => $staffId];
        $orderListObj = InteriorOrdersModel::find(
            array(
                'conditions' => $conditions,
                'bind'       => $bind,
                'order'      => 'id desc',
                'limit'      => $limit,
                'offset'     => ($page - 1) * $limit
            )
        );
        $orderList    = $orderListObj->toArray();
        if ($orderList) {
            $orderCodesArr     = array_column($orderList, 'order_code');
            $orderGoodsSkusObj = InteriorOrdersGoodsSkuModel::find([
                'conditions' => 'order_code in({order_code:array})',
                'bind'       => ['order_code' => $orderCodesArr],
                'order'      => 'id desc',
            ]);
            $orderGoodsSkus    = $orderGoodsSkusObj->toArray();
            // 查找每个订单下面的全部商品
            $orderStatusEnums = InteriorOrderStatusEnums::getCodeTxtMap($this->lang);
            $auditedEnums     = InteriorOrderAuditedEnums::getCodeTxtMap($this->lang);
            $fundStatusEnums  = InteriorOrderFundStatusEnums::getCodeTxtMap($this->lang);
            foreach ($orderList as &$order) {
                if ($order['order_status'] == InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE && $order['order_expire_at'] <= date('Y-m-d H:i:s')) {
                    //针对待付款状态订单到了过期时间，脚本还没置为已取消则要变更下状态
                    $order['order_status'] = InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE;
                }
                $order['order_status_txt'] = $orderStatusEnums[$order['order_status']];
                $order['fund_status_txt'] = $fundStatusEnums[$order['fund_status']];
                $order['is_audited_txt']   = $auditedEnums[$order['is_audited']];
                $order['pay_amount_fmt']   = InteriorGoodsEnums::fmtAmount($order['pay_amount'], $this->lang);
//                $order['created_at']       = $order['created_at'];


                $_orderCode = $order['order_code'];
                foreach ($orderGoodsSkus as $orderGoodsSku) {
                    if ($orderGoodsSku['order_code'] == $_orderCode) {
                        $orderGoodsSku['buy_price_fmt']   = InteriorGoodsEnums::fmtAmount($orderGoodsSku['buy_price'], $this->lang);
                        $orderGoodsSku['pay_amount_fmt']  = InteriorGoodsEnums::fmtAmount($orderGoodsSku['pay_amount'], $this->lang);
                        $orderGoodsSku['show_goods_name'] = self::convertGoodsNameByLang($this->lang, $orderGoodsSku);

                        $order['goods_skus'][] = $orderGoodsSku;
                    }
                }
            }
        }
        $count    = InteriorOrdersModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        $pageData = [
            'order_list'   => $orderList,
            'current_page' => $page,
            'count'        => $count,
            'limit'        => $limit
        ];
        return $pageData;
    }

    /**
     * 获取订单
     * Created by: Lqz.
     * @param array $loginUser
     * @param array $params
     * CreateTime: 2020/8/10 0010 15:08
     * @throws ValidationException
     */
    public function getOrderInfo(array $loginUser, array $params)
    {
        $orderCode = $params['order_code'];
        $staffId   = $loginUser['staff_id'];
        $orderObj  = $this->getStaffOrder($staffId, $orderCode);
        if (!$orderObj) {
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_was_not_found)
            ];
        }
        $payMethodMap              = InteriorGoodsPayMethodEnums::payMethod($this->lang);
        $orderSkusBoj              = $orderObj->getOrdersGoodsSku();
        $order                     = $orderObj->toArray();
        $orderSkus                 = $orderSkusBoj->toArray();
        $order['pay_method_name']  = $payMethodMap[$order['pay_method']];
        $orderStatusEnums          = InteriorOrderStatusEnums::getCodeTxtMap($this->lang);
        $auditedEnums              = InteriorOrderAuditedEnums::getCodeTxtMap($this->lang);
        $fundStatusEnums           = InteriorOrderFundStatusEnums::getCodeTxtMap($this->lang);
        //针对待付款状态订单到了过期时间，脚本还没置为已取消则要变更下状态
        $order['order_status']     = ($order['order_status'] == InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE && $order['order_expire_at'] <= date('Y-m-d H:i:s')) ? InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE : $order['order_status'];
        $order['order_status_txt'] = $orderStatusEnums[$order['order_status']];
        $order['fund_at']          = $order['fund_at'] ? substr($order['fund_at'], 0, 10) : null;
        $order['fund_status_txt'] = $fundStatusEnums[$order['fund_status']];
        $order['is_audited_txt']   = $auditedEnums[$order['is_audited']];
        $order['pay_amount_fmt']   = InteriorGoodsEnums::fmtAmount($order['pay_amount'], $this->lang);
        $order['goods_skus']       = null;
        $wrs_server = new WmsServer($this->lang,$this->timeZone);
        foreach ($orderSkus as $orderGoodsSku) {
            $orderGoodsSku['show_goods_name'] = self::convertGoodsNameByLang($this->lang, $orderGoodsSku);
            if ($orderGoodsSku['order_code'] == $order['order_code']) {
                $orderGoodsSku['buy_price_fmt']  = $wrs_server->format_amount($orderGoodsSku['buy_price']);
                $orderGoodsSku['pay_amount_fmt'] = $wrs_server->format_amount($orderGoodsSku['pay_amount']);
                $order['goods_skus'][]           = $orderGoodsSku;
            }
        }
        $tracking = '';
        $result = (new SyncWarehoseServer())->outBoundTracking($order['order_code'], $order['mach_code']);
        if ($result && isset($result['expressSn'])) {
            $tracking = $result['expressSn'];
        }

        $order['tracking'] = $tracking;
        return ['order_info' => $order];
    }

    public function getStaffOrder($staffId, $orderCode, $forUpdate = false)
    {
        $conditions = "staff_id = :staffId: and order_code = :orderCode:";
        $bind       = ["staffId" => $staffId, "orderCode" => $orderCode];
        $orderObj   = InteriorOrdersModel::findFirst(
            array(
                'conditions' => $conditions,
                'bind'       => $bind,
                "for_update" => $forUpdate
            )
        );
        return $orderObj;
    }

    /**
     * 修改sku 库存并记录日志
     * @param InteriorGoodsSkuModel $goodsSkuObj
     * @param $staffId
     * @param $toSurplusNum
     * @param $remark
     */
    public static function saveSkuSurplusNumAndLog(InteriorGoodsSkuModel $goodsSkuObj, $staffId, $chNum, $toSurplusNum, $remark)
    {
        if ($goodsSkuObj->surplus_num == $toSurplusNum) {
            return;
        }
        $logObj                   = new InteriorGoodsSkuLogModel();
        $logObj->staff_id         = $staffId;
        $logObj->goods_id         = $goodsSkuObj->goods_id;
        $logObj->goods_sku_id     = $goodsSkuObj->id;
        $logObj->from_surplus_num = $goodsSkuObj->surplus_num;
        $logObj->to_surplus_num   = $toSurplusNum;
        $logObj->current_ch_num   = $chNum;
        $logObj->remark           = $remark;
        $logObj->save();
        // 回滚goods_sku库存
        $goodsSkuObj->surplus_num = $toSurplusNum;
        $goodsSkuObj->save();
    }


    public static function convertGoodsNameByLang(string $lang, array $goods)
    {
        switch ($lang) {
            case LangEnums::LANG_CODE_EN :
                $currentName = $goods['goods_name_en'];
                break;
            case LangEnums::LANG_CODE_ZH_CN :
                $currentName = $goods['goods_name_zh'];
                break;
            default:
                // v13847需求, 若当地语言为空, 则取英文
                $currentName = !empty($goods['goods_name_th']) ? $goods['goods_name_th'] : $goods['goods_name_en'];
        }

        return $currentName;
    }

    public function getUserByNum($thisBuyNum, $loginUser, $setting_model)
    {

        $limit       = $setting_model->getSetVal($code = 'interior_buy_limit_num');
        $orderSkuNum = 0;
        $staffId     = $loginUser['staff_id'];
        $ordesObj    = InteriorOrdersModel::find([
            'conditions' => 'staff_id = :staffId: and order_status not in ({orderStatus:array}) and goods_type = :goods_type: and created_at >= :createdAtLeft: and created_at <= :createdAtRight:',
            'bind'       => [
                'staffId'        => $staffId,
                'orderStatus'    => [InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE, InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE],
                'goods_type'     => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES,
                'createdAtLeft'  => date('Y-m-1 00:00:00'),
                'createdAtRight' => date('Y-m-t 23:59:59'),
            ],
            'columns'    => 'order_code'
        ]);
        $orders      = $ordesObj->toArray();
        $codes       = array_column($orders, 'order_code');
        if ($codes) {
            $conditions  = 'order_code in ({orderCode:array}) and is_free = 0';
            $orderSkuNum = InteriorOrdersGoodsSkuModel::sum([
                'conditions' => $conditions,
                'bind'       => [
                    'orderCode' => $codes
                ],
                'column'     => 'buy_num'
            ]);
        }
        $canBuyNum = $limit - $thisBuyNum - $orderSkuNum;
        return $canBuyNum >= 0 ? true : false;
    }

    /**
     * 检查员工是否是首次下单工服
     * @param $staffId
     * @return bool
     */
    private function checkStaffIsFirstOrder($staffId): bool
    {
        $count = InteriorOrdersModel::count([
            'staff_id = :staff_id: AND order_status IN ({order_status:array}) and goods_type = :goods_type: ',
            'bind' => [
                'staff_id'     => $staffId,
                'order_status' => [
                    InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_SEND_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_RECEIVED_CODE,
                ],
                'goods_type'   => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES
            ]
        ]);
        $this->wLog('checkStaffIsFirstOrder', '工号:' . $staffId . '是否首次下单count:' . $count);
        if ($count == 0) {
            return true;
        }
        return false;
    }

    public static function getStaffStoreCateEnableGoods()
    {
        $goods_cate = SysStoreGoodsModel::find();
        $goods_cate = $goods_cate->toArray();
        foreach ($goods_cate as &$item) {
            $item['store_cate_id'] = explode(",", $item['store_cate_id']) ?? '';
            $item['goods_id']      = explode(",", $item['goods_id']) ?? '';
        }
        return $goods_cate;
    }

    public function getCodeTxtMap()
    {
        $goodsCates = self::getStaffStoreCateEnableGoods();
        $goodsCates = array_column($goodsCates, 'store_cate_id', 'sys_store_cate') ?? [];
        return $goodsCates;

    }

    /**
     * 限免免费数量
     * */
    public function getUserFreeByNum($loginUser, $goodsIds, $whereTime = 0)
    {
        $orderSkuNum = 0;
        $staffId     = $loginUser['staff_id'];
        if (empty($whereTime)) {
            $ordesObj = InteriorOrdersModel::find([
                'conditions' => 'staff_id = :staffId: and order_status != :orderStatus: ',
                'bind'       => [
                    'staffId'     => $staffId,
                    'orderStatus' => InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE,
                ],
                'columns'    => 'order_code'
            ]);

        } else {
            $date     = date("Y-m-d");
            $staTime  = date('Y-m-d 00:00:00', strtotime($date));
            $endTime  = date('Y-m-d 23:59:59', strtotime("$staTime +1 month -1 day"));
            $ordesObj = InteriorOrdersModel::find([
                'conditions' => 'staff_id = :staffId: and order_status != :orderStatus: and created_at BETWEEN :staTime: and :endTime:',
                'bind'       => [
                    'staffId'     => $staffId,
                    'orderStatus' => InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE,
                    'staTime'     => $staTime,
                    'endTime'     => $endTime,
                ],
                'columns'    => 'order_code'
            ]);
        }

        $orders = $ordesObj->toArray();
        $codes  = array_column($orders, 'order_code');
        if ($codes) {
            $orderSkuNum = InteriorOrdersGoodsSkuModel::find([
                'conditions' => 'order_code in ({orderCode:array}) and goods_id in ({goodsId:array}) and is_free =1  group by goods_id',
                'bind'       => [
                    'orderCode' => $codes,
                    'goodsId'   => $goodsIds
                ],
                'columns'    => 'goods_id,sum(buy_num) as buy_nums'
            ])->toArray();
        }
        $order_sku_num = [];
        if (!empty($orderSkuNum)) {
            $order_sku_num = array_column($orderSkuNum, 'buy_nums', 'goods_id');
        }
        return $order_sku_num;
    }


    /**
     * 获取员工限免商品类别
     * @param array $userInfo
     * @return array|string[]
     */
    public function getUserEnableFreeGoodsCate(array $userInfo)
    {
        $storeCateCode = HrStaffInfoServer::getStaffStoreCateCode($userInfo);
        (new BaseServer())->wLog('StoreCateCode', $storeCateCode);

        if (isset($storeCateCode['msg'])) {
            return $storeCateCode;
        }
        $goods_ids = (new InteriorGoodsRepository())->getInteriorGoodsStoreCateRelAll($storeCateCode);
        return $goods_ids;
    }


    /**
     * 新需求逻辑变更 需求11533 https://l8bx01gcjr.feishu.cn/docs/doccnXPQF9RegncaEhbOH63aUS0
     * 限免免费数量总和
     * */
    public function getUserFreeByNumNew($loginUser)
    {
        $order_sku_num = 0;
        $staffId       = $loginUser['staff_id'];
        $orders_obj    = InteriorOrdersModel::find([
            'conditions' => 'staff_id = :staffId: and order_status not in ({orderStatus:array}) and goods_type = :goods_type: ',
            'bind'       => [
                'staffId'     => $staffId,
                'orderStatus' => [InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE, InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE],
                'goods_type'  => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES
            ],
            'columns'    => 'order_code'
        ])->toArray();

        if (empty($orders_obj)) {
            return $order_sku_num;
        }
        $codes = array_values(array_unique(array_column($orders_obj, 'order_code')));
        if (!empty($codes)) {
            $order_sku_obj = InteriorOrdersGoodsSkuModel::findFirst([
                'conditions' => 'order_code in ({orderCode:array})  and is_free =1',
                'bind'       => [
                    'orderCode' => $codes,
                ],
                'columns'    => 'sum(buy_num) as buy_nums'
            ]);
            if (!empty($order_sku_obj)) {
                $order_sku_num = $order_sku_obj->toArray();
            }
        }
        return $order_sku_num['buy_nums'] ?? 0;
    }

    public function goodsSize($goods_id)
    {
        return InteriorGoodsEnums::getGoodsSize($goods_id, $this->lang);

    }

    /**
     * 在向wms同步库存的时候需要获取总部或网点的最新省、市、区、邮编信息
     * @param array $orderData 物料订单/资产订单信息组
     * @return array
     */
    public function getOrderNewAddressInfo($staff_store_id)
    {
        $province    = [];
        $city        = [];
        $district    = [];
        $postal_code = '';

        $wmsRepository = new WmsRepository();
        if (is_numeric($staff_store_id)) {
            //如果此值是个数字则表示总部
            $new_order_address = $this->getHeadquartersAddress($staff_store_id);
        } else {
            //代表网点
            $new_order_address = $wmsRepository->userStoreInfo($staff_store_id);
        }
        if ($new_order_address) {
            //获取新地址的省、市、区、信息
            $province = $wmsRepository->pdcInfo($new_order_address['province_code'], 1);
            $city     = $wmsRepository->pdcInfo($new_order_address['city_code'], 2);
            $district = $wmsRepository->pdcInfo($new_order_address['district_code'], 3);
            //如果网点邮编为空，取得市区邮编
            $district_postal_code = $district['postal_code'] ? explode(',', $district['postal_code']) :
                [$new_order_address['postal_code']];
            $postal_code          = $district_postal_code ? $district_postal_code[0] : '';
        }

        return [$province, $city, $district, $postal_code, $new_order_address['detail_address'] ?? ''];
    }

    /**
     * 获取当前国家的总部数据
     * @Date: 2022-03-05 15:44
     * @return:
     **@author: wangqi
     */
    public function getHeadquartersAddress($staff_store_id)
    {

        $headOffice = HeadquartersAddressModel::findFirst([
            'columns'    => "office_name as name,address detail_address,province_code,city_code,postal_code,district_code",
            'conditions' => "id = :id:",
            'bind'       => [
                'id' => $staff_store_id
            ],
            'limit'      => 1,
            'order'      => 'id asc'
        ]);

        if (!empty($headOffice)) {
            return $headOffice->toArray();
        } else {
            return [];
        }
    }

    /**
     * 购物车删除
     * @param array $user 登录人信息
     * @param array $params 提交的数据
     * @throws ValidationException
     **/
    public function delShoppingCart(array $user, array $params)
    {
        $goods_sku_ids           = array_values(array_unique(array_column($params['buy_goods_ids_arr'], 'goods_sku_id')));
        $cart_goods_sku_list     = InteriorStaffsShoppingCartModel::find(
            [
                'conditions' => 'staff_id = :staffId: and goods_sku_id in ({goods_sku_id:array}) ',
                'bind'       => ['staffId' => $user['staff_info_id'], 'goods_sku_id' => $goods_sku_ids],
            ]);
        $cart_goods_sku_list_arr = $cart_goods_sku_list->toArray();
        if ($cart_goods_sku_list_arr) {
            foreach ($cart_goods_sku_list as $cart_goods) {
                if ($cart_goods->delete() === false) {
                    //您当前所在网点类型不允许下单此类型商品，请更换商品后再下单
                    throw new ValidationException($this->getTranslation()->_('interior_staffs_shopping_cart_del_err'));
                }
            }
        }

    }


    /**
     * 上传支付凭证
     * @param array $params 提交的数据
     * @throws ValidationException
     **/
    public function uploadPaymentVoucher(array $params)
    {

        $interior_orders = InteriorOrdersModel::findFirst(
            [
                'conditions' => 'order_code = :order_code:',
                'bind'       => ['order_code' => $params['order_code']],
            ]);

        //订单数据不可为空
        if (empty($interior_orders)) {
            throw new ValidationException($this->getTranslation()->_('interior_orders_null'));
        }

        //订单创建人和上传支付凭证人员不相等
        if ($interior_orders->staff_id != $params['staff_id']) {
            throw new ValidationException($this->getTranslation()->_('interior_orders_payment_voucher_not_staff_id'));
        }

        //订单类型不对，不可上传支付凭证
        if ($interior_orders->goods_type != InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
            throw new ValidationException($this->getTranslation()->_('interior_orders_type_err'));
        }

        //订单支付方式不对
        if ($interior_orders->pay_method != InteriorGoodsPayMethodEnums::PAY_METHOD_OFFLINE_PAY) {
            throw new ValidationException($this->getTranslation()->_('interior_pay_method_err'));
        }

        //待上传支付凭证状态不对
        if ($interior_orders->payment_voucher_status != InteriorGoodsPayMethodEnums::GOODS_PAYMENT_VOUCHER_STATUS_ING) {
            throw new ValidationException($this->getTranslation()->_('interior_payment_voucher_status_err'));
        }

        //订单状态为0的时候 才可以上传支付凭证
        if ($interior_orders->order_status != InteriorOrderStatusEnums::ORDER_STATUS_WARITING_SUBMIT_CODE) {
            throw new ValidationException($this->getTranslation()->_('interior_order_status_err'));
        }
        $goods_sku_arr = InteriorOrdersGoodsSkuModel::find(
            [
                'conditions' => 'order_code = :order_code:',
                'bind'       => ['order_code' => $params['order_code']],
                'columns'    => ['goods_sku_code', 'goods_name_th', 'attr_1', 'buy_num', 'buy_price', 'pay_amount']
            ])->toArray();

        $goods = $res = [];
        if (!empty($goods_sku_arr)) {
            foreach ($goods_sku_arr as $key => $good) {
                $goods[] = [
                    'i'             => $key + 1,
                    'barCode'       => $good['goods_sku_code'],
                    'goodsName'     => $good['goods_name_th'],
                    'specification' => $good['attr_1'],
                    'num'           => $good['buy_num'],
                    'price'         => ($good['buy_price'] * 100),
                    'remark'        => $good['pay_amount']
                ];
            }

            $sync_wms_data['nodeSn']             = $interior_orders->node_sn;
            $sync_wms_data['consigneeName']      = $interior_orders->staff_name;
            $sync_wms_data['consigneePhone']     = $interior_orders->staff_mobile;
            $sync_wms_data['province']           = $interior_orders->receive_province_name;
            $sync_wms_data['city']               = $interior_orders->receive_city_name;
            $sync_wms_data['district']           = $interior_orders->receive_district_name;
            $sync_wms_data['postalCode']         = $interior_orders->receive_postal_code;
            $sync_wms_data['consigneeAddress']   = $interior_orders->receive_address;
            $sync_wms_data['orderSn']            = $interior_orders->order_code;
            $sync_wms_data['node_department_id'] = $interior_orders->node_department_id;
            $sync_wms_data['deliveryWay']        = 'express';
            $sync_wms_data['goods']              = json_encode($goods, JSON_UNESCAPED_UNICODE);
            $sync_wms_data['remark']             = $interior_orders->remark;
            $sync_wms_data['lang']               = $this->lang;

            $syncWarehoseServer = new SyncWarehoseServer();
            $res                = $syncWarehoseServer->syncAddOrderToWmsReturnWarehouseAdd($sync_wms_data);
        }
        if (empty($res) || $res['code'] != 1 || !$res['data']) {
            $this->getDI()->get('logger')->write_log('无头件出库失败, 出库数据是：' . json_encode($sync_wms_data, JSON_UNESCAPED_UNICODE) . ' 失败返回数据：' . json_encode($res, JSON_UNESCAPED_UNICODE), 'error');
            throw new Exception($this->getTranslation()->_('goods_unclaimed_return_ware_house'));
        } else {
            $interior_orders->out_sn       = $res['data'];
            $interior_orders->order_status = InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE;
            $interior_orders->remark       = $interior_orders->remark . "【wms_out_sn：{$res['data']}】";
        }

        $interior_orders->payment_voucher        = $params['payment_voucher'];
        $interior_orders->payment_voucher_status = InteriorGoodsPayMethodEnums::GOODS_PAYMENT_VOUCHER_STATUS_UPLOADED;
        if ($interior_orders->save() === false) {
            throw new Exception($this->getTranslation()->_('goods_unclaimed_upload_payment'));
        }

        return ['out_sn' => $res['data'] ?? ''];


    }


    /**
     * 获取员工商城里面的语言后缀
     * @Date: 7/17/23 5:03 PM
     * @param string $lang 语种
     * @return string
     **/
    public function getLangFix(string $lang = 'en')
    {
        switch ($lang) {
            case LangEnums::LANG_CODE_EN :
                $lang = 'en';
                break;
            case LangEnums::LANG_CODE_ZH_CN :
                $lang = 'zh';
                break;
        }
        return $lang;
    }

    /**
     * 根据时间获取当前无头订单购买的金额
     * @param  $staff_id 当前用户id
     * @param  $now_amount 当前提交的订单金额
     * @return bool
     **/
    public function getRangeSettingsMoney($staff_id, $now_amount)
    {
        $bool                    = true;
        $interior_goods_settings = (new SettingEnvServer())->getSetVal('interior_goods_time_range_settings_money');
        $day                     = date('d');
        $config_data             = json_decode($interior_goods_settings, true);
        $amount                  = 0;
        $start_day               = 0;
        $date                    = [];
        foreach ($config_data as $config_one) {
            $date = explode('-', $config_one['date']);
            if ($day >= $date[0] && $day <= $date[1]) {
                $amount    = $config_one['amount'];
                $start_day = $date[0];
                break;
            }
        }

        //$add_hour   = env('add_hour') * 3600 ?? 0;
        $add_hour   = 0;
        $start_time = date('Y-m-d H:i:s', (strtotime(date('Y-m') . '-' . $start_day . ' 00:00:00') - $add_hour));
        $end_time   = date('Y-m-d H:i:s', (time() - $add_hour));

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['io' => InteriorOrdersModel::class]);
        $builder->columns('sum(io.pay_amount) as amount');
        $builder->Where('io.staff_id = :staff_id:', ['staff_id' => $staff_id]);
        $builder->andWhere('io.goods_type = :goods_type:', ['goods_type' => InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE]);
        $builder->andWhere('io.pay_method = :pay_method:', ['pay_method' => InteriorGoodsPayMethodEnums::PAY_METHOD_DEDUCTION_OF_WAGES_CODE]);
        $builder->andWhere('io.order_status not in ({order_status:array})', ['order_status' => [InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE, InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE]]);
        $builder->betweenWhere('io.created_at', $start_time, $end_time);

        $interior_goods_amount = $builder->getQuery()->execute()->getFirst()->toArray();
        $interior_goods_amount = $interior_goods_amount['amount'] ?? 0;
        $sum_amount            = bcadd($interior_goods_amount, $now_amount, 2);
        if ($sum_amount >= $amount) {
            $bool = false;
        }
        $log = [
            'new_amount'            => '当前提交的金额是:' . $now_amount,
            'staff_id'              => '当前用户是:' . $staff_id,
            'config_data'           => $config_data,
            'start_day'             => '配置开始时间:' . $start_day . '查询开始时间:' . $start_time,
            'end_day'               => '配置结束时间:' . $date[1] . '查询结束时间:' . $end_time,
            'interior_goods_amount' => '查询的时间范围金额是:' . $interior_goods_amount,
            'add_hour'              => '时区为:' . $add_hour,
            'bool'                  => '返回的结果是:' . $bool,
        ];
        $this->getDI()->get('logger')->write_log('根据时间获取当前无头订单购买的金额数据查询结果(getRangeSettingsMoney):' . json_encode($log, JSON_UNESCAPED_UNICODE), 'info');
        return $bool;
    }

    /**
     * 取消订单
     * Created by: Lqz.
     * @param array $loginUser
     * @param array $params
     * @return bool|string[]
     * CreateTime: 2020/8/10 0010 19:43
     */
    public function cancelOrder(array $loginUser, array $params, $remark = 'staff_cancel_order')
    {
        $orderCode = $params['order_code'];
        $staffId   = $loginUser['staff_id'];
        $db        = InteriorOrdersModel::beginTransaction($this);
        try {
            $orderObj = $this->getStaffOrder($staffId, $orderCode, true);
            if (!$orderObj) {
                $db->rollback();
                return [
                    'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_was_not_found)
                ];
            }
            //订单未审核或者订单状态在待发货、预定中、待付款的才可取消
            if ($orderObj->is_audited != InteriorOrderAuditedEnums::IS_AUDITED_WARITING_CODE || !in_array($orderObj->order_status, [InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE, InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE, InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE])) {
                $db->rollback();
                return [
                    'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_can_not_cancel)
                ];
            }
            $orderStatus = $orderObj->order_status;
            // 取消订单
            $orderObj->order_status = InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE;
            $date                   = date('Y-m-d H:i:s');
            $orderObj->remark       = $orderObj->remark . "【{$remark}:{$date}】";
            $orderObj->cancel_reason = $params['cancel_reason'] ?? InteriorOrderStatusEnums::ORDER_CANCEL_REASON_SELF;
            $orderObj->canceled_at = $date;
            $orderObj->updated_at = $date;
            // 回滚订单goods_sku库存
            $orderSkusObj = $orderObj->getOrdersGoodsSku();
            $orderSkusArr = $orderSkusObj->toArray();
            foreach ($orderSkusArr as $orderSku) {
                $goodsSkuObj           = $this->getGoodsSkuInfo($orderSku['goods_id'], $orderSku['goods_sku_id'], true);
                if(!$goodsSkuObj){
                    continue;
                }
                $toSurplusNum          = $goodsSkuObj->surplus_num + $orderSku['buy_num'];
                $_saleNum              = ($goodsSkuObj->sale_num - $orderSku['buy_num']);
                $goodsSkuObj->sale_num = $_saleNum >= 0 ? $_saleNum : 0;
                // 记录库存修改日志
                self::saveSkuSurplusNumAndLog($goodsSkuObj, $staffId, $orderSku['buy_num'], $toSurplusNum, $remark);
            }
            // 预售单取消订单
            if ($orderStatus == 2) {
                $orderObj->save();
                $db->commit();
            } else if ($orderStatus == InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE) {
                //如果FlashPay待付款的订单直接直接取消，支付成功才同步scm所以无需去scm取消
                $orderObj = $this->cancelPayOrder($orderObj);
                $orderObj->save();
                $db->commit();
            } else {
                $syncData = [
                    'orderSn' => $orderCode,
                    'lang'    => $this->lang,
                    'mchId'   => $orderObj->mach_code,
                ];
                $syncWarehoseServer = new SyncWarehoseServer();
                $wmsRes = $syncWarehoseServer->syncCancelOrderToWmsCancelOutbound($syncData);
                if ($wmsRes['code'] == 1) {
                    if ($orderObj->pay_method == InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE) {
                        //如果是FlashPay在线支付的待发货，需要将款项状态变更为退款中
                        $orderObj->fund_status = InteriorOrderFundStatusEnums::FUND_STATUS_REFUNDING;
                    }
                    $orderObj->save();
                    $db->commit();
                } else {
                    $db->rollback();
                    return ['msg' => "取消订单失败【{$wmsRes['msg']}】", 'data' => $wmsRes];
                }
            }
        } catch(ValidationException $e) {
            $db->rollback();
            return [
                'msg' => LangEnums::getTranslation($this->lang, $e->getMessage())
            ];
        } catch (\Exception $e) {
            $db->rollback();
            $this->wLog('cancelOrder', $e->getMessage());
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_cancel_error)
            ];
        }
        return true;
    }

    /**
     * FFM商品校验逻辑
     * @description: 校验FFM主体商品的提交规则和库存，返回配置的货主信息
     * @author: AI
     * @date: 2025-08-05 20:30:00
     * @param array $params 订单参数
     * @param string $storeId 收货地址网点ID
     * @return array 返回货主配置信息，如果不是FFM商品则返回[]
     * @throws ValidationException
     */
    private function validateFfmGoods(array $params, string $storeId): array
    {
        // 获取FFM主体工服SPU ID配置
        $settingEnvServer = new SettingEnvServer();
        $ffmGoodsIds = $settingEnvServer->getSetVal('interior_goods_ffm_good_ids', ',');

        // 如果没有配置FFM商品ID，则跳过校验
        if (empty($ffmGoodsIds)) {
            return [];
        }

        // 检查订单中的商品是否包含FFM商品
        $orderGoodsIds = array_column($params['buy_goods_ids_arr'], 'goods_id');
        $ffmGoodsInOrder = array_intersect($orderGoodsIds, $ffmGoodsIds);
        $nonFfmGoodsInOrder = array_diff($orderGoodsIds, $ffmGoodsIds);

        // 校验1：FFM主体商品不允许与其他商品混合提交
        if (!empty($ffmGoodsInOrder) && !empty($nonFfmGoodsInOrder)) {
            throw new ValidationException($this->getTranslation()->_('22367_ffm_interior_goods_mix_error'));
        }

        // 如果订单中没有FFM商品，则无需进一步校验
        if (empty($ffmGoodsInOrder)) {
            return [];
        }

        // 校验2：检查收货地址是否在分仓配置中
        $warehouseConfig = $this->getWarehouseConfigByStoreId($storeId);
        if (empty($warehouseConfig)) {
            // 记录warning告警日志
            $storeInfo = (new StoreRepository())->getStoreDetail($storeId);
            $this->getDI()->get('logger')->write_log(
                "员工商城分仓规则未配置 - 收货地址网点名称: {$storeInfo['name']}, 收货地址网点编码: {$storeId}",
                'warning'
            );

            throw new ValidationException($this->getTranslation()->_('22367_ffm_interior_orders_warehouse_error'));
        }

        // 校验3：检查FFM商品库存
        // 由于前面已经校验过混合商品，执行到这里说明订单中全是FFM商品，直接传递所有商品
        $this->validateFfmGoodsStock($params['buy_goods_ids_arr'], $warehouseConfig);

        // 返回货主配置信息
        return [
            'mach_code' => $warehouseConfig['mach_code'],
            'stock_id'  => $warehouseConfig['stock_id'],
        ];
    }

    /**
     * 校验FFM商品库存
     * @description: 根据分仓配置查询FFM商品在SCM中的可售库存
     * @param array $ffmGoodsArr 购买商品数组（调用时已确保全部为FFM商品）
     * @param array $warehouseConfig 分仓配置
     * @return bool
     * @throws ValidationException
     * @author: AI
     * @date: 2025-08-05 20:30:00
     */
    private function validateFfmGoodsStock(array $ffmGoodsArr, array $warehouseConfig): bool
    {
        // 获取商品的SKU信息
        $skuCodes = [];
        $skuQuantities = [];

        // 直接处理所有商品，因为调用方已确保全部为FFM商品
        foreach ($ffmGoodsArr as $goodsItem) {
            $skuCodes[] = $goodsItem['goods_sku_code'];
            $skuQuantities[$goodsItem['goods_sku_code']] = $goodsItem['buy_num'];
        }

        // 使用分仓配置中的货主和仓库信息查询库存
        $stockData = $this->getFfmGoodsStock($skuCodes, $warehouseConfig);

        // 检查每个SKU的库存是否充足
        foreach ($skuCodes as $skuCode) {
            $availableStock = $stockData[$skuCode] ?? 0;
            $requiredQuantity = $skuQuantities[$skuCode] ?? 0;

            //scm的SKU可售库存<=0
            if ($availableStock <= 0) {
                throw new ValidationException($this->getTranslation()->_('22367_ffm_interior_orders_stock_error', ['barcode' => $skuCode]));
            }

            if ($availableStock < $requiredQuantity) {
                throw new ValidationException($this->getTranslation()->_('22367_ffm_interior_orders_stock_error', ['barcode' => $skuCode]));
            }
        }
        return true;
    }

    /**
     * 获取FFM商品库存
     * @description: 根据分仓配置的货主信息查询SCM库存
     * @author: AI
     * @date: 2025-08-05 20:30:00
     * @param array $skuCodes SKU编码数组
     * @param array $warehouseConfig 分仓配置
     * @return array 库存数据，key为SKU编码，value为库存数量
     */
    private function getFfmGoodsStock(array $skuCodes, array $warehouseConfig): array
    {
        $stockData = [];

        try {
            $syncWarehouseServer = new SyncWarehoseServer();

            // 构建查询参数，使用分仓配置中的货主信息
            $postData = [
                'barCode'     => implode(',', $skuCodes),
                'goodsStatus' => 'normal',
                'lang'        => $this->lang,
                'warehouseId' => $warehouseConfig['stock_id'], // 使用配置中的仓库ID
            ];

            // 直接使用分仓配置中的货主代码
            $machCode = $warehouseConfig['mach_code'];

            // 添加货主相关参数
            $postData['mchId'] = $machCode;

            // 调用库存查询接口
            $stockResult = $syncWarehouseServer->syncGoodsStockWithErrBarcode($postData);
            $scmResultData = $stockResult['data'];
            if (!empty($scmResultData) && !empty($scmResultData['stock'])) {
                foreach ($scmResultData['stock'] as $key => $inventory) {
                    $stockData[$key] = $inventory['availableInventory'];
                }
            }
        } catch (\Exception $e) {
            // 记录错误日志
            $this->getDI()->get('logger')->write_log(
                'FFM商品库存查询失败: ' . $e->getMessage() . ', SKU: ' . implode(',', $skuCodes),
                'error'
            );
        }

        return $stockData;
    }
}
