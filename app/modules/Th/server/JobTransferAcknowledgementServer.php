<?php

namespace FlashExpress\bi\App\Modules\Th\Server;


//转岗确认单
use FlashExpress\bi\App\Enums\JobTransferConfirmEnums;
use FlashExpress\bi\App\Enums\PdfEnums;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\formPdfServer;
use FlashExpress\bi\App\Server\JobTransferConfirmServer;

class JobTransferAcknowledgementServer extends BaseServer
{

    /**
     * 获取确认单模板
     * @param $tmpl_type
     * @return  string
     */
    public function getAcknowledgementTmpl($tmpl_type = ''): string
    {
        switch ($tmpl_type) {
            case JobTransferConfirmEnums::CONFIRM_TYPE_LANG_EN:
                $filePath = '/views/job_transfer/th_transfer_confirm_en.ftl';
                break;
            case JobTransferConfirmEnums::CONFIRM_TYPE_LANG_ZH:
                $filePath = '/views/job_transfer/th_transfer_confirm_zh.ftl';
                break;
            default:
                $filePath = '/views/job_transfer/th_transfer_confirm_th.ftl';
                break;
        }
        $upload_result = OssHelper::uploadFile(APP_PATH . $filePath, true);
        return $upload_result['object_url'];
    }

    /**
     * 生成转岗确认单
     * @param $params
     * @return string
     * @throws \Exception
     */
    public function generateJobTransferPdf($params): string
    {
        $params['template_cnf']        = PdfEnums::RESPONSE_TYPE_DIRECT;
        $params['staff_lang']          = $params['staff_lang'] ?? 'zh';

        //获取转岗确认单
        $transferData   = (new JobTransferConfirmServer($this->lang,
            $this->timeZone))->getConfirmDetail($params);
        $transferDetail = $transferData['data'];

        $tmplPathUrl    = $this->getAcknowledgementTmpl(substr(strtolower($params['staff_lang']), 0, 2));


        //组织 pdf 数据，生成 pdf
        $temp_params    = [
            'staff_name'                   => $transferDetail['staff_name'],
            'before_job_title_name'        => $transferDetail['before_job_title_name'],
            'before_department_name'       => $transferDetail['before_department_name'],
            'before_store_name'            => $transferDetail['before_store_name'],
            'hire_date'                    => $transferDetail['hire_date'],
            'after_job_title_name'         => $transferDetail['after_job_title_name'],
            'after_department_name'         => $transferDetail['after_department_name'],
            'after_store_name'             => $transferDetail['after_store_name'],
            'before_job_title_grade'       => $transferDetail['before_job_title_grade'],
            'after_job_title_grade'        => $transferDetail['after_job_title_grade'],
            'before_working_day_rest_type' => $transferDetail['before_working_day_rest_type'],
            'after_working_day_rest_type'  => $transferDetail['after_working_day_rest_type'],
            'before_manager_id'            => $transferDetail['before_manager_id'],
            'after_manager_id'             => $transferDetail['after_manager_id'],
        ];
        $pdf_header_footer_setting = [
            'displayHeaderFooter' => true,
            'headerTemplate'      => $transferDetail['header_template'],
            'footerTemplate'      => $transferDetail['footer_template'],
        ];

        try {
            $pdf_file_data = (new formPdfServer())->generatePdf($tmplPathUrl, $temp_params, [],
                sprintf('job_transfer_confirm_%s', date('YmdHis')),
                $pdf_header_footer_setting, 'attchment');
        } catch (\Exception $e) {
            echo $e->getMessage() . $e->getTraceAsString();
        }

        if (empty($pdf_file_data['object_url'])) {
            $this->logger->write_log(['makeTerminationFileUrl  error', $tmplPathUrl, $params]);
            return '';
        }
        return $pdf_file_data['object_url'];
    }
}