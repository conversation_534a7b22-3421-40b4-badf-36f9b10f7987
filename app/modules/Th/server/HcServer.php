<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Enums\WorkingCountryEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HRStaffingModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Server\ArdApiServer;
use FlashExpress\bi\App\Server\HcServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysDepartmentServer;
use FlashExpress\bi\App\Server\SysStoreServer;

class HcServer extends GlobalBaseServer
{


    /**
     * shop部门展示网点统计数据的职位
     * @return array
     */
    protected function getShopShowStoreDataJobTitle(): array
    {
        return [enums::$job_title['shop_officer'], enums::$job_title['th_senior_shop_officer']];
    }


    /**
     * @param $result
     * @return array|mixed|object|\stdClass
     * @throws BusinessException
     */
    public function getDisplayStoreData($result)
    {
        //所属部门”为Network Management部门及其子部门，且“工作网点”为非总部时
        $nw_department_ids = (new SysDepartmentServer())->getDepartmentIdsUseSettingEnv('dept_network_management_id');
        $shop_department_ids    = (new SysDepartmentServer())->getDepartmentIdsUseSettingEnv('dept_shop_management_id');
        if ($result['worknode_id'] != Enums::HEAD_OFFICE_ID && in_array($result['department_id'],
                $nw_department_ids)) {
            if ($result['approval_state_code'] != enums::$audit_status['panding_approval']) {
                $store_data =  json_decode($result['store_data'], true);
            } else {
                $store_data = $this->getStoreData($result['worknode_id']);
            }
            if (!empty($store_data)) {
                $store_data['data_type'] = 1;//network
            } else {
                $store_data = (object)[];
            }
            return $store_data;
        } elseif ($result['worknode_id'] != Enums::HEAD_OFFICE_ID && in_array($result['department_id'],
                $shop_department_ids) && in_array($result['job_title'],
                $this->getShopShowStoreDataJobTitle())) {
            if ($result['approval_state_code'] != enums::$audit_status['panding_approval']) {
                $store_data = empty($result['store_data']) ? [] :  json_decode($result['store_data'], true);
            } else {
                $store_data = $this->getShopStoreData($result['worknode_id']);
            }
            if (!empty($store_data)) {
                $store_data['data_type'] = 2;//shop
            } else {
                $store_data = (object)[];
            }
            return $store_data;
        }
        return (object)[];
    }

    /**
     * @param $result
     * @return string
     */
    protected function getDcoCourierRatio($result)
    {
        if (!empty($result['dco_courier_ratio'])){
            return $result['dco_courier_ratio'];
        }
        $staffServer = new StaffServer();
        //在职（包含待离职）- 待离职 + 待入职 + 停职
        $params1 = [
            'job_title' => [enums::$job_title['dc_officer'], enums::$job_title['cdc_officer']],
            'store_id' => $result['worknode_id'],
        ];
        $dcoCount = $staffServer->getStaffCount($params1);//DC Officer+CDC Officer
        $params2 = [
            'job_title' => [enums::$job_title['bike_courier'],enums::$job_title['van_courier'],enums::$job_title['boat_courier']],
            'store_id' => $result['worknode_id'],
        ];
        $courierCount = $staffServer->getStaffCount($params2);

        //网点副主管
        $assistant_branch_supervisor_count = $staffServer->getStaffCount([
            'job_title' => [enums::$job_title['assistant_branch_supervisor']],
            'store_id' => $result['worknode_id'],
        ]);

        return "{$dcoCount}:{$assistant_branch_supervisor_count}:{$courierCount}";
    }

    /**
     * 获取语言翻译
     */
    public function getLanguageAbilityText($languageKeys)
    {
        if (empty($languageKeys)) {
            return '';
        }

        $languageKeys = explode(',', $languageKeys);

        $data = [];

        foreach (enums::$languageAbility as $k => $v) {
            if (in_array($k, $languageKeys)) {
                $data[] = $v;
            }
        }

        return $data ? implode(', ', $data) : '';
    }

    /**
     * @param $detailLists
     * @param $result
     * @return void
     */
    public function getCountryDetail(&$detailLists, $result)
    {
        $deptServer = new SysDepartmentServer();
        $storeServer = new SysStoreServer();
        $network = $deptServer->getDepartmentIdsUseSettingEnv('dept_network_management_id');
        $retail = $deptServer->getDepartmentIdsUseSettingEnv('dept_shop_management_id');
        if ((in_array($result['department_id'],$network)||in_array($result['department_id'],$retail)) && $result['worknode_id'] != -1){
            $regionAndPiece = $storeServer->getRegionAndPieceName($result['worknode_id']);
            $detailLists['hr_probation_field_store_area_text'] = $regionAndPiece['manage_region'] . ' '. $regionAndPiece['manage_piece'];//大区片区
        }

        if (in_array($result['job_title'], [enums::$job_title['dc_officer'], enums::$job_title['cdc_officer']])) {
            $dco_courier_ratio = $this->getDcoCourierRatio($result);
            $dco_courier_ratio_key = 'dco_courier_ratio';
            $dco_courier_ratio_key_tips = 'hc_dco_courier_ratio_tip';
            if(count(explode(':', $dco_courier_ratio)) > 2) {
                $dco_courier_ratio_key = 'dco_courier_ratio_v2';
                $dco_courier_ratio_key_tips = 'hc_dco_courier_ratio_tip_v2';
            }
            $detailLists[$dco_courier_ratio_key] = [
                'value'    => $dco_courier_ratio,
                'key_tips' => $this->getTranslation()->t($dco_courier_ratio_key_tips),
                'key_icon' => 'info-o',
            ];
        }
        if (!empty($result['language_ability'])) {
            $detailLists['language_ability'] = $this->getLanguageAbilityText($result['language_ability']);
        }
    }

    /**
     * 固化统计数据
     * @param $info
     * @return array
     * @throws BusinessException
     */
    public function getCountryPropertyData($info)
    {
        $data = [];
        $data['dco_courier_ratio'] = $this->getDcoCourierRatio($info);

        //所属部门”为Network Management部门及其子部门，且“工作网点”为非总部时
        $nw_dept_ids = (new SysDepartmentServer())->getDepartmentIdsUseSettingEnv('dept_network_management_id');
        if ($info['worknode_id'] != Enums::HEAD_OFFICE_ID && in_array($info['department_id'], $nw_dept_ids)) {
            $store_data         = $this->getStoreData($info['worknode_id']);
            $store_data['data_type'] = 1;
            $data['store_data'] = json_encode($store_data);
        }
        $shop_dept_ids = (new SysDepartmentServer())->getDepartmentIdsUseSettingEnv('dept_shop_management_id');
        if ($info['worknode_id'] != Enums::HEAD_OFFICE_ID
            && in_array($info['department_id'], $shop_dept_ids)
            && in_array($info['job_title'], $this->getShopShowStoreDataJobTitle())) {
            $store_data         = $this->getShopStoreData($info['worknode_id']);
            $store_data['data_type'] = 2;
            $data               = [];
            $data['store_data'] = json_encode($store_data);
        }
        return $data;
    }


    /**
     * @param $store_id
     * @return array
     * @throws BusinessException
     */
    protected function getShopDeliveryData($store_id): array
    {
        $begin_date = date('Y-m-d', strtotime("-7 day"));
        $end_date   = date('Y-m-d', strtotime("-1 day"));
        //获取网点 揽件量 和 出勤人数
        $storeStatisticData           = ArdApiServer::getShopStoreStatisticsData($store_id, $begin_date, $end_date);
        $store_avg_total_pickup_count = array_sum(array_column($storeStatisticData, 'total'));
        $store_avg_total_staff_count  = array_sum(array_column($storeStatisticData, 'attendance_count'));
        $data                   = [
            //网点近7天网点日均揽件量
            "store_avg_total_pickup_count" => round($store_avg_total_pickup_count/7),
            //网点近7天网点日均揽件人数
            "store_avg_total_staff_count"  => round($store_avg_total_staff_count/7),
            //网点近7天揽件人效
            "store_efficiency"             => $store_avg_total_staff_count ? round($store_avg_total_pickup_count / $store_avg_total_staff_count) :0,
            //大区近7天揽件人效
            "region_store_efficiency"      => 0,
        ];
        $storeInfo                    = (new SysStoreServer())->getStoreInfoByid($store_id);
        if (!empty($storeInfo['manage_region'])) {
            //获取大区 揽件量 和 出勤人数
            $regionStatisticData                   = ArdApiServer::getShopRegionStatisticsData($storeInfo['manage_region'],
                $begin_date,
                $end_date);
            $region_avg_total_pickup_count         = array_sum(array_column($regionStatisticData, 'total'));
            $region_avg_total_staff_count          = array_sum(array_column($regionStatisticData, 'attendance_count'));
            $data['region_store_efficiency'] = $region_avg_total_staff_count ? round($region_avg_total_pickup_count / $region_avg_total_staff_count) : 0;
        }
        return $data;
    }


    /**
     * shop部门网点统计数据
     * @param $store_id
     * @param null $job_title
     * @return array
     * @throws BusinessException
     */
    protected function getShopStoreData($store_id, $job_title = null): array
    {
        $delivery_data = $this->getShopDeliveryData($store_id);

        //网点在职人数 & 网点当月异动人数
        $staff_data = $this->getAboutStaffData($store_id);
        $params = [
            'store_id'   => $store_id,
            'job_titles' => $this->getShopShowStoreDataJobTitle(),
        ];
        //网点待招聘HC数量
        $staff_data['store_surplus_hc_count'] = $this->getStoreWaitingRecruitHcCount($params);

        //获取大区下网点数据
        $region_store_ids= $this->getRegionStoreIdsByStoreId($store_id);
        $params = [
            'store_ids'  => $region_store_ids,
            'job_titles' => $this->getShopShowStoreDataJobTitle(),
        ];
        //大区待招聘HC数量
        $staff_data['region_surplus_hc_count'] = $this->getRegionWaitingRecruitHcCount($params);

        $region_staff_data = $this->getRegionAboutStaffData($region_store_ids);


        return array_merge($delivery_data, $staff_data,$region_staff_data);
    }


    /**
     * network部门网点统计数据
     * @param $store_id
     * @param null $job_title
     * @return array
     */
    public function getStoreData($store_id,$job_title=null): array
    {

        $store_delivery_date = $this->getDeliveryByStoreDate($store_id);

        $job_titles = [
            enums::$job_title['bike_courier'],
            enums::$job_title['van_courier'],
            enums::$job_title['boat_courier'],
        ];
        //网点在职人数 & 网点当月异动人数
        $store_staff = $this->getAboutStaffData($store_id,$job_titles);
        //网点待招聘HC数量
        $store_staff['store_surplus_hc_count'] = $this->getStoreWaitingRecruitHcCount(['store_id' => $store_id,'job_titles' => $job_titles,]);
        //片区待招聘HC数量
        $store_staff['piece_surplus_hc_count'] = $this->getPieceWaitingRecruitHcCount(['store_id' => $store_id,'job_titles'=>$job_titles]);

        return array_merge($store_delivery_date, $store_staff);
    }

    /**
     * 网点数据 近7天日均进港量/近7天日均揽件量/近7天日均积压量/近7天派件人效
     * - 近7天日均进港量： 取FBI-DC报表管理-每日运营统计报表中（T+1）“揽件总操作量”字段，取近7天的平均值
     * - 近7天日均揽件量：日均揽件量_近7天  （T+1），取近7天的平均值
     * - 近7天日均积压量：日均积压量_近7天（T+1），取近7天的平均值
     * - 近7天派件人效：网点出勤派件人效（取FBI-数据看板-派件人均人效趋势图-派件人均人效（T+1））
     *   取数逻辑：近七天的所有有效妥投的件数/七天的执行任务的派件员总数量 取数逻辑：近七天的所有有效妥投的件数/七天的执行任务的派件员总数量
     * @param $store_id
     * @return int[]
     */
    public function getDeliveryByStoreDate($store_id): array
    {
        $return_arr = [
            'avg_count_delivery_not_signed' => 0, //近7天日均积压量
            'avg_pickup_operation_count'    => 0, //近7天日均进港量
            'avg_total_count'               => 0, //近7天日均揽件量
            'store_efficiency'              => 0, //人效
        ];

        $end_date   = date('Y-m-d', strtotime("-1 day"));
        $begin_date = date('Y-m-d', strtotime("-7 day"));

        $bi_rpc = new ApiClient('ard_api', '', 'deliverycount.get_delivery_by_store_date', $this->lang);
        $bi_rpc->setParams([
            'store_id'   => $store_id,
            'begin_date' => $begin_date,
            'end_date'   => $end_date,
        ]);
        $rpc_result = $bi_rpc->execute();

        if (isset($rpc_result['result']) && $rpc_result['result']['code'] == 1) {
            $data = $rpc_result['result']['data'];

            $count_delivery_not_signed = 0; //积压量
            $pickup_operation_count    = 0; //进港量
            $total_count               = 0; //揽件量
            $delivery_count            = 0; //有效妥投的件数
            $delivery_man_count        = 0; //派件员总数量

            foreach ($data as $key => $value) {
                if (!empty($value['count_delivery_not_signed'])) {
                    $count_delivery_not_signed += $value['count_delivery_not_signed']; //积压量
                }

                if (isset($value['inbound_count'])) {
                    if (!empty($value['inbound_count'])) {
                        $pickup_operation_count += $value['inbound_count']; //进港量 新字段
                    }
                } else {
                    if (isset($value['pickup_operation_count']) && !empty($value['pickup_operation_count'])) {
                        $pickup_operation_count += $value['pickup_operation_count']; //进港量
                    }
                }
                if (!empty($value['total_count'])) {
                    $total_count += $value['total_count']; //揽件量
                }
                if (!empty($value['delivery_count'])) {
                    $delivery_count += $value['delivery_count']; //有效妥投的件数
                }
                if (!empty($value['delivery_man_count'])) {
                    $delivery_man_count += $value['delivery_man_count']; //派件员总数量
                }
            }

            $store_efficiency = $delivery_man_count != 0 ? $delivery_count / $delivery_man_count : 0;
            $return_arr['avg_count_delivery_not_signed'] = round($count_delivery_not_signed / 7);        //近7天日均积压量
            $return_arr['avg_pickup_operation_count']    = round($pickup_operation_count / 7);           //近7天日均进港量
            $return_arr['avg_total_count']               = round($total_count / 7);                      //近7天日均揽件量
            $return_arr['store_efficiency']              = round($store_efficiency, 2); //近7天派件人效
        }
        return $return_arr;
    }

    /**
     * @description 获取在职人数
     * 指定部门、职位、工作所在国家, 在职、在编、不含子账号、不含实习生、不含待离职
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getOnJobStaffCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        $conditions = 'node_department_id = :node_department_id: and job_title in ({job_title_id:array}) and
                       formal in(1,4) and state = 1 and is_sub_staff = 0 and wait_leave_state = 0 and 
                       working_country = :working_country:';
        $bind       = [
            'node_department_id' => $departmentId,
            'job_title_id'       => $jobTitle,
            'working_country'    => WorkingCountryEnums::getWorkingCountry(),
        ];

        //获取指定部门、网点、职位的，在职、在编、非子账号的人数
        return HrStaffInfoModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * @description 获取停职人数
     * 指定部门、职位、工作所在国家, 在职、在编、不含子账号、不含实习生、不含待离职
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getSuspendCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        $conditions = 'node_department_id = :node_department_id: and job_title in ({job_title_id:array}) and
                       formal in(1,4) and state = 3 and is_sub_staff = 0 and wait_leave_state = 0 and 
                       working_country = :working_country:';
        $bind       = [
            'node_department_id' => $departmentId,
            'job_title_id'       => $jobTitle,
            'working_country'    => WorkingCountryEnums::getWorkingCountry(),
        ];

        //获取指定部门、网点、职位的，在职、在编、非子账号的人数
        return HrStaffInfoModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * @description 获取待离职人数
     * 指定部门、职位、工作所在国家, 在职、在编、不含子账号、不含实习生、不含待离职
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getWaitLeaveCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        $conditions = 'node_department_id = :node_department_id: and job_title in ({job_title_id:array}) and
                       formal in(1,4) and state = 1 and is_sub_staff = 0 and wait_leave_state = 1 and 
                       working_country = :working_country:';
        $bind       = [
            'node_department_id' => $departmentId,
            'job_title_id'       => $jobTitle,
            'working_country'    => WorkingCountryEnums::getWorkingCountry(),
        ];

        //获取指定部门、网点、职位的，在职、在编、非子账号的人数
        return HrStaffInfoModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * @description 获取待入职人数 ,
     * 新逻辑：进入到入职表数据
     *  旧逻辑存在漏洞，存在部分总部的人不上传附件还发了offer，却没有入到入职表
     * 旧逻辑：已发offer人数
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getPendingEntryCount($departmentId, $storeId, $jobTitle = null): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("count(1) as cou");
        $builder->from(['h' => HrHcModel::class]);
        $builder->leftJoin(HrEntryModel::class,'e.hc_id = h.hc_id','e');
        $builder->andWhere('e.deleted = :deleted:', ['deleted' => Enums::IS_DELETED_NO]);
        $builder->andWhere('e.status = :status:', ['status' => HrEntryModel::STATUS_TO_BE_EMPLOYED]);
        $builder->andWhere('h.department_id = :dept_id:', ['dept_id' => $departmentId]);
        $builder->inWhere('h.job_title', $jobTitle);
        $count = $builder->getQuery()->execute()->getFirst();

        return intval($count->cou);
    }

    /**
     * 获取招聘中人数
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return int
     */
    public function getSurplusCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }
        $conditions = 'state_code = 2 and department_id = :department_id: and
                            job_title in ({job_title_id:array})  and deleted = 1';
        $bind       = [
            'department_id' => $departmentId,
            'job_title_id'  => $jobTitle,
        ];
        $count      = HrHcModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => "sum(surplusnumber) as cou",
        ])->toArray();

        return $count['cou'] ?? 0;
    }

    /**
     * @desc 获取已提交的HC总人数（待审批）
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return int
     */
    public function getBudgetAdoptCount($departmentId, $storeId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }
        $conditions = 'state_code = 1 and approval_state_code = 7 and department_id = :department_id: and job_title IN({job_title_id:array}) and deleted = 1';
        $bind = [
            'department_id' => $departmentId,
            'job_title_id'  => $jobTitle,
        ];
        $count = HrHcModel::sum([
            'conditions' => $conditions,
            'bind'       => $bind,
            'column'     => 'demandnumber',
        ]);
        return $count ?? 0;
    }

    /**
     * 获得待转岗人数
     * @param $department_id
     * @param $store_id
     * @param $jobTitleIds
     * @return int
     */
    public function getJobTransferCount($department_id, $store_id, $jobTitleIds)
    {
        //审核通过，待转岗
        $conditions = 'approval_state =2 and state=1 and current_department_id = :department_id: and current_position_id in ({job_title_id:array}) ';
        $bind = [];
        $bind['department_id'] = $department_id;
        $bind['job_title_id'] = $jobTitleIds;

        return JobTransferModel::count(
            [
                'conditions' => $conditions,
                'bind'=>$bind
            ]
        );
    }

    /**
     * @param $month
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return array
     */
    public function getHcBudgetCount($month, $departmentId, $storeId, $jobTitle): array
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return [];
        }
        $share_department_position = $this->share_department_position;
        //如果是共享部门
        if(isset($share_department_position[$departmentId])) {
            $share_position_arr = $share_department_position[$departmentId] ?? [];
            //如果是共享职位
            if(!empty($share_position_arr) && in_array($jobTitle, $share_position_arr) ) {
                $jobTitle = $share_position_arr;
            } else {
                $jobTitle = [$jobTitle];
            }
        } else {
            $jobTitle = [$jobTitle];
        }
        //获取在职人数
        $onJobCnt = $this->getOnJobStaffCount($departmentId, null, $jobTitle);

        //获取待入职人数
        $pendingEntryCnt = $this->getPendingEntryCount($departmentId, null, $jobTitle);

        //获取剩余人数  相当于招聘中的人数
        $surplusCnt = $this->getSurplusCount($departmentId, null, $jobTitle);

        //4.获取已提交的HC总人数（待审批）
        $pendingCnt = $this->getBudgetAdoptCount($departmentId, null, $jobTitle);

        return [
            'on_job'        => $onJobCnt,       //在职人数
            'pending_entry' => $pendingEntryCnt,//待入职人数
            'surplus'       => $surplusCnt,     //招聘中的人数
            'pendingCon'    => $pendingCnt      //已提交待审批
        ];
    }
}