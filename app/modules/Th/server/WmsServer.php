<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Models\backyard\WmsOrderModel;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Server\WmsServer AS GlobalBaseServer;


class WmsServer extends GlobalBaseServer
{
	
	//一个月只能申请一次的职位
	public function isMoons($staffId)
	{
		$info = $this->staffRep->getStaffPositionv3($staffId);
		
		if(empty($info)){
			return false;
		}
		//20210823 产品说 不验证网点类型
		if (in_array($info['job_title'], [enums::$job_title['branch_supervisor'],
		                                  enums::$job_title['regional_manager'],
		                                  enums::$job_title['onsite_supervisor'],
		                                  enums::$job_title['hub_supervisor'],
		                                  enums::$job_title['hub_manager'],
		                                  enums::$job_title['freight_hub_outbound_supervisor'],
		                                  enums::$job_title['freight_hub_manager'],
		                                  enums::$job_title['store_supervisor'],
                                          enums::$job_title['cdc_supervisor'],
        ])) {
			return true;
		}
		
//		switch($info['category']){
//			// DC/SP
//			case enums::$stores_category['sp']:
//			case enums::$stores_category['dc']:
//				if(in_array($info['job_title'], [enums::$job_title['branch_supervisor'],
//				])){
//					return true;
//				}
//				break;
//			// BDC
//			case enums::$stores_category['bdc']:
//				if(in_array($info['job_title'], [enums::$job_title['branch_supervisor'],
//				                                 enums::$job_title['regional_manager'],
//				])){
//					return true;
//				}
//				break;
//			// shop
//			case enums::$stores_category['shop_pickup_only']:
//			case enums::$stores_category['shop_pickup_delivery']:
//			case enums::$stores_category['shop_ushop']:
//				if(in_array($info['job_title'], [enums::$job_title['shop_supervisor'],
//				])){
//					return true;
//				}
//				break;
//			//OS
//			case enums::$stores_category['os']:
//				if(in_array($info['job_title'], [enums::$job_title['onsite_supervisor']])){
//					return true;
//				}
//				break;
//			//hub
//			case enums::$stores_category['hub']:
//				if(in_array($info['job_title'], [enums::$job_title['hub_supervisor'],
//				                                 enums::$job_title['hub_manager'],
//				])){
//					return true;
//				}
//				break;
//			case enums::$stores_category['bhub']:
//				if(in_array($info['job_title'], [enums::$job_title['freight_hub_outbound_supervisor'],
//				                                 enums::$job_title['freight_hub_manager'],
//				])){
//					return true;
//				}
//				break;
//		}
		
		return false;
		
	}

	/**
	 *
	 * 一个月的申请记录
	 *
	 * @param array $userinfo
	 *
	 */
	public function existsMonth($userinfo = [])
	{
		$time = date("Y-m-01");
		$sql = "SELECT count(1) AS total FROM wms_order where order_status in (1, 2)  and apply_user = {$userinfo['id']}   and  `created_at` >= '{$time}'";
		$Detail_data = $this->getDI()->get('db')->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC);
		return $Detail_data['total'];
		
//		$Detail_data = WmsOrderModel::findFirst([
//			                                       'columns' => 'count(1) AS total',
//			                                       'conditions' => " order_status in (1, 2)  and apply_user = :apply_user:   and  `created_at` >= ':created_at:' ",
//			                                       'bind' => ['apply_user' => $userinfo['id'] ,'created_at' => $time]
//		                                       ]);
//		if (empty($Detail_data)) {
//			$Detail_data->toArray();
//		}
//		return empty($Detail_data) ? 0 :$Detail_data['total'];
	}
	
	/**
	 * 创建订单
	 * @Access  public
	 * @Param   request
	 * @Return  array
	 */
	public function addOrderS($paramIn = [], $userinfo)
	{
		$time = date("YmdHis");
		$rand = rand(1000, 9999);
		$orderId = $time . $rand;
		
		//获取订单相关人信息
		$admin_group = UC('wmsRole')['admin_group'];
		$organization_id = (isset($paramIn['organization_id']) && !empty($paramIn['organization_id'])) ? $paramIn['organization_id'] : $userinfo['organization_id'];
		$uInfo = $this->wms->userStoreInfo($organization_id);
		$uInfoArr = array_merge(empty($uInfo) ? [] : $uInfo, $userinfo, [
			"store_id" => $organization_id
		]);
		$staffInfo = (new StaffServer())->getStaffById($userinfo['staff_id']);

        /**
         * 14082【BY｜TH/LA】NW资产耗材权限与审批流优化
         * TH总部员工申请资产/耗材传输scm地址优化
         * 针对总部申请人，由原来写死的地址修改为获取总部地址第一条数据，并将详细地址固化
         */
        $headOffice=[];
        $pc_code = '';
        if($uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2']) {
            $headOffice = $this->wms->getHeadquartersAddress();
            /**
             * 14771【BY-TH/PH/MY/LA】申请物料、申请资产和SCM接口逻辑优化
             * 总部员工申请资产/耗材时，需要记录成本中心
             */
            $pc_code = $this->wms->getPcCode($staffInfo);
        }
		//格式化订单数据
		$serialNo = $this->getRandomId();
		$returnData['data']['dataList'] = [];
		$orderData['order_id'] = $orderId;//订单号
		$orderData['organization_id'] = $organization_id;//网点编号
        $orderData['pc_code'] = $pc_code;//成本中心
		$orderData['reason_application'] = isset($paramIn['reason_application']) ? $paramIn['reason_application'] : "";//申请理由字数限制
		$orderData['consignee_phone'] = $staffInfo && isset($staffInfo['mobile']) ? $staffInfo['mobile'] : '';//联系方式
		//organization_type  ==2 为总部
		$orderData['consignee_address'] =$uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2'] ? $headOffice['address'] : $uInfoArr['detail_address'];//详细地址
		$orderData['province_code'] = $uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2'] ? $headOffice['province_code'] : $uInfoArr['province_code'];//省
		$orderData['city_code'] = $uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2'] ? $headOffice['city_code'] : $uInfoArr['city_code'];//市
		$orderData['district_code'] = $uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2'] ? $headOffice['district_code'] :$uInfoArr['district_code'];//区
		$orderData['postal_code'] = $uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2'] ? $headOffice['postal_code'] : $uInfoArr['postal_code'];//邮编
		$orderData['apply_user'] = $uInfoArr['id'] ?? $userinfo['staff_id'];//申请人
		$orderData['shipping_user'] = $uInfoArr['name'] ?? '';//收货人姓名
		$orderData['order_status'] = isset($paramIn['order_status']) ? $paramIn['order_status'] : 1;//订单状态
		$orderData['serial_no'] = (!empty($serialNo) ? 'MA' . $serialNo : NULL); //序列号
		//        $orderData['wf_role'] = $wf_role;
		$orderData['wf_role'] = '';
		
		//格式化订单商品详情数据
		$paramIn['goods_order_detail'] = isset($paramIn['goods_order_detail']) ? $paramIn['goods_order_detail'] : "";
		$orderDetailData = $paramIn['goods_order_detail'];
		$barCodeArr = array_column($orderDetailData, "bar_code");
		
		//获取订单相关人货物
		$goodsInfo = $this->wms->goodsInfo($barCodeArr);
		$orderDetailDataFormat = array();
		$goodsArr = array();
		//$orderDetailDataFormat = array();
		if ($orderDetailData) {
			foreach ($goodsInfo as $k => $v) {
				$goodsArr[$v['bar_code']]['price'] = $v['price'];
				$goodsArr[$v['bar_code']]['specification'] = $v['specification'];
				//根据语言存储商品名称
				if ($this->lang == 'en') {
					$goodsArr[$v['bar_code']]['goods_name'] = $v['goods_name_en'];
					$goodsArr[$v['bar_code']]['nuit'] = $v['nuit_en'];
				} elseif ($this->lang == 'th') {
					$goodsArr[$v['bar_code']]['goods_name'] = $v['goods_name_th'];
					$goodsArr[$v['bar_code']]['nuit'] = $v['nuit_th'];
				} else {
					$goodsArr[$v['bar_code']]['goods_name'] = $v['goods_name_zh'];
					$goodsArr[$v['bar_code']]['nuit'] = $v['nuit_zh'];
				}
			}
		}
		if ($orderDetailData) {
			foreach ($orderDetailData as $k => $v) {
				$orderDetailDataFormat[$k]['sort'] = isset($v['sort']) ? intval($v['sort']) : 0;
				$orderDetailDataFormat[$k]['bar_code'] = isset($v['bar_code']) ? $v['bar_code']: 0;
				$orderDetailDataFormat[$k]['num'] = isset($v['num']) ? intval($v['num']): 0;
				$orderDetailDataFormat[$k]['branch_repertory'] = isset($v['branch_repertory']) ? intval($v['branch_repertory']) : 0;//网点库存
				$orderDetailDataFormat[$k]['recomment_num'] = isset($v['recommend_num']) ? intval($v['recommend_num']) : 0;//推荐数量
				$orderDetailDataFormat[$k]['approval_num'] = $orderDetailDataFormat[$k]['num'];//申请数->审批数
				if ($v['num'] <= 0) {
					return $this->checkReturn(-3, $this->getTranslation()->_('7101'));
				}
				$orderDetailDataFormat[$k]['order_id'] = $orderId;
				$orderDetailDataFormat[$k]['employ_user'] = isset($v['employ_user']) ? implode(",", $v['employ_user']) : "";
				//判断是否都是本网点员工
				$userSumArr = $this->wms->notAllOrganizationR($orderDetailDataFormat[$k]['employ_user'], $organization_id);
				if (sizeof($userSumArr) < sizeof($v['employ_user']) || empty($v['employ_user'])) {
					return $this->checkReturn(-3, $this->getTranslation()->_('7100'));
				}
				$orderDetailDataFormat[$k]['price'] = isset($goodsArr[$v['bar_code']]['price']) ? $goodsArr[$v['bar_code']]['price'] : 0;
				$orderDetailDataFormat[$k]['goods_name'] = isset($goodsArr[$v['bar_code']]['goods_name']) ? $goodsArr[$v['bar_code']]['goods_name'] : '';
				$orderDetailDataFormat[$k]['nuit'] = isset($goodsArr[$v['bar_code']]['nuit']) ? $goodsArr[$v['bar_code']]['nuit'] : '';
			}
		}
		
		$db = WorkflowModel::beginTransaction($this);
		try {
			$wmsOrderData = $this->wms->addOrderR($orderData, $orderDetailDataFormat);
			if (!$wmsOrderData) {
				$db->rollBack();
				$this->wLog('order_err4101_s', json_encode($paramIn) . json_encode($userinfo));
				return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
			}
			
			$orderData['id'] = $wmsOrderData;
			$returnData['data'] = $orderId;
			$requestType = 9;
			$staffId = $uInfoArr['id'];
			$organization_type = $uInfoArr['organization_type'];
			$store_id = $uInfoArr['organization_id'];
			//[4]同步数据到列表
			$info = $this->wms->getWmsOrderMongoR($orderData['id']);
			$server = new \FlashExpress\bi\App\Server\AuditListServer($this->lang, $this->timezone);
			$summary = $server->generateSummary($info['id'], $requestType);
			
			$insertAuditData[] = [
				'id_union' => 'id_' . $info['id'],
				'staff_id_union' => $staffId,
				'type_union' => $requestType,
				'status_union' => 101,
				'store_id' => $organization_type == 2 ? '' : $store_id,
				'data' => json_encode($info),
				'table' => 'wms_order',
				'created_at' => gmdate('Y-m-d H:i:s', time()),
				'origin_id' => $info['id'],
				'summary' => json_encode($summary, JSON_UNESCAPED_UNICODE),
				'approval_id' => 0,
			];
			$staff_info = $this->staffRep->getStaffPositionv3($userinfo['staff_id']);
			$extend = ['flow_code'=>'v1','category'=>$staff_info['category']];
			//判断相应职位获取本身负责的大区
			$extend['region_area'] = $this->getJobCategory($userinfo['staff_id'],$userinfo['job_title']);
			//判断员工是否是PPC部门及其子部门下的员工
            $extend['is_ppc_staff'] = (new AuditServer($this->lang, $this->timezone))->isBelongByAncestry($userinfo['staff_id'], enums::NETWORK_PICKUP_CONTROL_CENTER);
			if (!($this->other->insterUnion($insertAuditData) &&
			      (new ApprovalServer($this->lang, $this->timezone))
				      ->create($wmsOrderData, enums::$audit_type['MA'], $userinfo['staff_id'],null,$extend)
			)) {
				throw new \Exception('addOrder_s 插入审批流失败 ' . json_encode($paramIn, JSON_UNESCAPED_UNICODE) . " " . json_encode($userinfo, JSON_UNESCAPED_UNICODE));
			}
			
			$db->commit();
		} catch (\Exception $e) {
			$db->rollBack();
			return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
		}
		
		
		
		
		//        foreach ($approvalIDArr as $finalApprover) {
		//            $insertAuditData[] = [
		//                'id_union' => 'id_' . $info['id'],
		//                'staff_id_union' => $staffId,
		//                'type_union' => $requestType,
		//                'status_union' => 107,
		//                'store_id' => $organization_type == 2 ? '' : $store_id,
		//                'data' => json_encode($info),
		//                'table' => 'wms_order',
		//                'created_at' => gmdate('Y-m-d H:i:s', time()),
		//                'origin_id' => $info['id'],
		//                'summary' => json_encode($summary, JSON_UNESCAPED_UNICODE),
		//                'approval_id' => $finalApprover,
		//            ];
		//        }
		
		//[5]追加审批日志
		//        $staff_name = $this->pub->getStaffName($staffId);
		//        $log['staff_id'] = $staffId;
		//        $log['type'] = $requestType;
		//        $log['original_type'] = 1;
		//        $log['original_id'] = $info['id'];
		//        $log['operator'] = $staffId;
		//        $log['operator_name'] = $staff_name;
		//        $log['to_status_type'] = 1;
		//        $this->other->insertLog($log);
		//
		//        [6]追加审批流
		//        $ApprovalArr['audit_id'] = $info['id'];
		//        $ApprovalArr['type'] = $requestType;
		//        $ApprovalArr['level'] = 1;
		//        $ApprovalArr['status'] = 7;
		//        $ApprovalArr['submitter_id'] = $staffId;
		//        $ApprovalArr['staff_ids'] = $userinfo['staff_ids'] ?? 0;
		//        $this->other->insertApproval($ApprovalArr);
		
		return $this->checkReturn($returnData);
	}
	
	
	/**
	 * @description:	//一个月只能申请4次的职位
	 *
	 * @param $staffId    用户 id
	 *
	 * @return     :  true
	 * <AUTHOR> L.J
	 * @time       : 2021/9/15 15:56
	 */

	public function isManagerFour($staffId='')
	{
		if(empty($staffId)){
			return false;
		}
		$info = $this->staffRep->getStaffPositionv3($staffId);
		
		if (empty($info)) {
			return false;
		}
		//这些职位每个月能申请 4 次
		if (in_array($info['job_title'], [enums::$job_title['area_manager'],
		                                  enums::$job_title['shop_supervisor'],
		])) {
			return true;
		}
		return false;
	}


    /**
     * 样例
     * from_node_id | to_node_id | valuate_formula | valuate_code
     * -------------+------------+-----------------+-------------
     *      4       |     5      |    $p1 == 4     | getSubmitterDepartment
     *
     * 表示当提交人的部门为4时，审批节点4的下一个节点是5
     * 需要在 getWorkflowParams 中返回申请人所在的部门字段
     *
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $wmsOrder = WmsOrderModel::findFirst([
            'conditions' => ' id = :id:',
            'bind' => ['id' => $auditId]
        ]);
        if(!empty($wmsOrder)) {
            $wmsOrder = $wmsOrder->toArray();
            $userInfo = HrStaffInfoModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id:',
                'bind' => ['staff_id' => $wmsOrder['apply_user']]
            ]);
            if (!empty($userInfo)) {
                $userInfo = $userInfo->toArray();
                $department_info = SysDepartmentModel::findFirst($userInfo['node_department_id']);
                $ancestry = empty($department_info) ? '' : $department_info->ancestry_v3;
                $store_info = $this->storeS->getStoreByid($wmsOrder['organization_id']);
                $staff_info = (new StaffRepository())->getStaffPositionv3($wmsOrder['apply_user']);
                //判断相应职位获取本身负责的大区
                $region_area = $this->getJobCategory( $wmsOrder['apply_user'],$userInfo['job_title']);
                //判断员工是否是PPC部门及其子部门下的员工
                $is_ppc_staff = (new AuditServer($this->lang, $this->timezone))->isBelongByAncestry($wmsOrder['apply_user'], enums::NETWORK_PICKUP_CONTROL_CENTER);
                $level = 0;
                if (in_array($department_info->type, [2, 3]) && $department_info->level == 1) {
                    $level = 1 ;
                }
                return [
                    'category'=>$staff_info['category'],
                    'job_title' => $userInfo['job_title'],
                    'node_department_id' => $userInfo['node_department_id'],
                    'sys_store_id' => $userInfo['sys_store_id'],
                    'ancestry' => $ancestry,
                    'manage_region' => $store_info['manage_region'],
                    'k1' => $this->wf_role2(['id' => $wmsOrder['apply_user']], $store_info),
                    'region_area'=>$region_area,
                    'level'         => $level,//部门等级
                    'is_ppc_staff' => $is_ppc_staff
                ];
            }
        }
        //没找到申请人信息 返回空
        return [
            'category' => 0,
            'job_title' => 0,
            'node_department_id' => 0,
            'sys_store_id' => 0,
            'ancestry' => '',
            'manage_region' => '',
            'k1' => '',
            'region_area'=>[],
            'level' => 0,
            'is_ppc_staff' => 0
        ];

    }
	
	
	

}
