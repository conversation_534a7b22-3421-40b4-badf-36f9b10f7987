<?php
/**
 * Author: Bruce
 * Date  : 2024-11-26 17:04
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Th\Server;


use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractBusinessApplyModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\HrStaffContractBusinessApplyRepository;
use FlashExpress\bi\App\Repository\HrStaffContractRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditCallbackServer;
use FlashExpress\bi\App\Server\AuditOptionRule;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\RenewContractBusinessServer as GlobalBaseServer;

class RenewContractBusinessServer extends GlobalBaseServer
{
    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        //申请人不可撤销
        return new AuditOptionRule(true,
            false,
            false,
            false,
            false,
            false);
    }

    /**
     * 存在一级审批，不允许撤销
     * @param $auditId
     * @return bool
     */
    public function applicantCustomiseOptionsInApprovalProcess($auditId): bool
    {
        //todo 查询业务数据，当前审批状态如果是非待审批，则返回 false;
        $hrStaffContractBusinessApplyRepository = new HrStaffContractBusinessApplyRepository($this->timezone);
        $contractInfo       = $hrStaffContractBusinessApplyRepository->getOneById($auditId, 'status');
        if (empty($contractInfo)) {
            return false;
        }

        if ($contractInfo['status'] != enums::APPROVAL_STATUS_PENDING) {
            return false;
        }

        $info = AuditApprovalModel::findFirst([
            'conditions' => 'biz_type = :audit_type: and biz_value = :audit_id: and state = :state: and deleted = 0',
            'bind' => [
                'audit_type' => AuditListEnums::APPROVAL_TYPE_IC_RENEWAL,
                'audit_id' => $auditId,
                'state' => enums::APPROVAL_STATUS_APPROVAL
            ],
        ]);
        return empty($info) ? true : false;
    }

    /**
     * 审批
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function update($paramIn)
    {
        $staffId       = $this->processingDefault($paramIn, 'staff_id', 2);
        $id            = $this->processingDefault($paramIn, 'audit_id', 2);
        $status        = $this->processingDefault($paramIn, 'status', 2);
        $reject_reason = $this->processingDefault($paramIn, 'reject_reason');

        $hrStaffContractBusinessApplyRepository = new HrStaffContractBusinessApplyRepository($this->timezone);
        $contractInfo       = $hrStaffContractBusinessApplyRepository->getOneById($id, 'status');

        if (empty($contractInfo)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }

        if ($contractInfo['status'] == enums::APPROVAL_STATUS_APPROVAL) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        if ($contractInfo['status'] == enums::APPROVAL_STATUS_REJECTED) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        if ($contractInfo['status'] == enums::APPROVAL_STATUS_CANCEL) {
            throw new ValidationException($this->getTranslation()->_('cancel_notice'));
        }

        $server = new ApprovalServer($this->lang, $this->timezone);
        if ($status == enums::$audit_status['approved']) {
            // 同意
            $server->approval($id, AuditListEnums::APPROVAL_TYPE_IC_RENEWAL, $staffId);
        } elseif ($status == enums::$audit_status['dismissed']) {
            // 驳回
            $server->reject($id, AuditListEnums::APPROVAL_TYPE_IC_RENEWAL, $reject_reason, $staffId);
            // 撤销
        } elseif ($status == enums::$audit_status['revoked']) {//撤销
            $server->cancel($id, AuditListEnums::APPROVAL_TYPE_IC_RENEWAL, $reject_reason, $staffId);
        }

        return $this->checkReturn(['data' => ['id' => $id]]);
    }

    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $hrStaffContractBusinessApplyRepository = new HrStaffContractBusinessApplyRepository($this->timezone);
            $contractInfo       = $hrStaffContractBusinessApplyRepository->getOneById($auditId);

            if (empty($contractInfo)) {
                throw new ValidationException($this->getTranslation()->_('1015') . '[From RenewContractBusiness 审批终态设置]');
            }

            if ($contractInfo['status'] == enums::APPROVAL_STATUS_APPROVAL) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From RenewContractBusiness 审批终态设置]');
            }

            if ($contractInfo['status'] == enums::APPROVAL_STATUS_REJECTED) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From RenewContractBusiness 审批终态设置]');
            }

            if ($contractInfo['status'] == enums::APPROVAL_STATUS_TIMEOUT) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From RenewContractBusiness 审批终态设置]');
            }

            if(in_array($state, [enums::APPROVAL_STATUS_APPROVAL, enums::APPROVAL_STATUS_REJECTED, enums::APPROVAL_STATUS_TIMEOUT])) {
                $data['id']              = $contractInfo['id'];
                $data['contract_id']     = $contractInfo['contract_id'];
                $data['business_type']   = $contractInfo['business_type'];
                $data['business_status'] = $contractInfo['business_status'];
                $data['audit_status']    = $state;
                //修改 在职状态， 释放hold
                AuditCallbackServer::createData(AuditListEnums::APPROVAL_TYPE_IC_RENEWAL, $data);
            }

            $updateData['status']           = $state;
            $updateData['audit_time']       = date('Y-m-d H:i:s');

            $db                   = $this->getDI()->get('db');
            $db->updateAsDict('hr_staff_contract_business_apply', $updateData,
                [
                    'conditions' => 'id = ?',
                    'bind'       => [$auditId],
                ]
            );
        }
    }

    /**
     * 可视化超时，按照表单去超时
     *
     * @param $audit_id
     * @return string
     */
    public function getAuditFormOvertimeDate($audit_id): string
    {
        $hrStaffContractBusinessApplyRepository = new HrStaffContractBusinessApplyRepository($this->timezone);
        $applyInfo       = $hrStaffContractBusinessApplyRepository->getOneById($audit_id);
        if (empty($applyInfo)) {
            return '';
        }
        $audit_json = json_decode($applyInfo['audit_json'], true);

        return empty($audit_json['leave_date']) ? '' : $audit_json['leave_date'];
    }

    /**
     * 超时关闭，驳回。发送消息
     * @param $data
     * @return bool
     * @throws BusinessException
     */
    public function delayCallBack($data)
    {
        $rmq = new RocketMQ('renew-contract-business');
        $rmq->setType(RocketMQ::TAG_NAME_RENEW_CONTRACT);
        $rid = $rmq->sendMsgByTag($data);
        $this->logger->write_log('backyard to hcm rmq-renew-contract-business exception: ' . $rid, 'info');
        if(!$rid) {//mq 发送失败
            $this->logger->write_log(['RenewContractBusinessServer-setProperty-mq-fail' => $data]);
            throw new BusinessException($this->getTranslation()->_('data_error'));
        }

        return true;
    }

    /**
     * MY 个人代理 合同到期 打卡页是否展示入口
     * @param $params
     * @return mixed
     */
    public function noticeData($params)
    {
        $data['is_display'] = self::NOTICE_IS_DISPLAY_NO;
        $data['is_deposit'] = self::NOTICE_IS_DEPOSIT_NO;
        $data['url'] = '';
        $data['contract_end_date'] = '';
        $data['contract_id'] = '';
        $data['fail_message'] = '';
        $data['status'] = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_PENDING;
        $data['staff_info_id'] = '';
        $data['name'] = '';
        $staffRepository = new StaffRepository();
        $staffData       = $staffRepository->getStaffInfoOne($params['staff_id'], ['staff_info_id', 'name', 'hire_type']);

        if (empty($staffData)) {
            return $data;
        }

        $rpc             = new ApiClient("ard_api", '', 'proxyInvoice.get_ic_deposit_state', 'en');
        $param['staff_info_id']   = $staffData['staff_info_id'];
        $rpc->setParams($param);
        $res = $rpc->execute();
        if (isset($res['error'])) {
            throw new BusinessException($res['error']);
        }
        if (isset($res['result']['code']) && $res['result']['code'] != 1) {
            throw new BusinessException($res['msg']);
        }
        //是否缴纳押金
        $data['is_deposit'] = $res['result']['data']['deposit_state'] == self::NOTICE_IS_DEPOSIT_NO ? self::NOTICE_IS_DEPOSIT_NO : self::NOTICE_IS_DEPOSIT_YES;

        $data['staff_info_id'] = $staffData['staff_info_id'];
        $data['name'] = $staffData['name'];
        //个人代理，兼职个人代理
        if(!in_array($staffData['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            $data['fail_message'] = '雇佣类型，非个人代理，兼职个人代理';
            return $data;
        }

        //查询待续约的合同，最新的待续约，可能跟消息对应的合同不一致
        $contractWhere['staff_id'] = $params['staff_id'];
        $contractWhere['contract_status'] = HrStaffContractModel::CONTRACT_STATUS_TO_BE_RENEWED;
        $contractInfo = HrStaffContractRepository::getOne($contractWhere);

        if(empty($contractInfo)) {
            $data['fail_message'] = '未找到合同信息';
            return $data;
        }

        $data['contract_id'] = $contractInfo['id'];
        $data['contract_end_date'] = $contractInfo['contract_end_date'];

        //取最后一条业务数据
        $applyWhere['contract_id'] = $contractInfo['id'];
        $applyInfo = HrStaffContractBusinessApplyRepository::getOneByContractId($applyWhere);

        if(empty($applyInfo)) {
            $data['fail_message'] = 'base 数据错误';
            return $data;
        }

        $submitPageUrl = env('h5_endpoint') . self::RENEW_CONTRACT_SUBMIT_PAGE;
        $submitPageUrlNoSelect = env('h5_endpoint') . self::RENEW_CONTRACT_SUBMIT_PAGE_SELECT_PENDING;

        //如果 没有传则 用主账号获取
        if(empty($params['master_staff_flag'])) {
            $params['master_staff_flag'] = $this->setSessionPrefix(self::$sub_to_master_session_prefix)->generateSessionId($params['staff_id']);
        }

        if ($params['master_staff_flag']) {
            $questionMarkPosition = strpos($submitPageUrl, '?');
            // 查找是否有问号
            if ($questionMarkPosition !== false) {
                $submitPageUrl = $submitPageUrl. '&sub_to_master_token='. $params['master_staff_flag'];
            } else {
                $submitPageUrl = $submitPageUrl. '?sub_to_master_token='. $params['master_staff_flag'];
            }

            $questionMarkPosition = strpos($submitPageUrlNoSelect, '?');
            // 查找是否有问号
            if ($questionMarkPosition !== false) {
                $submitPageUrlNoSelect = $submitPageUrlNoSelect. '&sub_to_master_token='. $params['master_staff_flag'];
            } else {
                $submitPageUrlNoSelect = $submitPageUrlNoSelect. '?sub_to_master_token='. $params['master_staff_flag'];
            }
        }

        //消息的 业务状态：待处理
        if(in_array($applyInfo['business_status'], [HrStaffContractBusinessApplyModel::BUSINESS_STATUS_PENDING]) && $applyInfo['business_type'] != HrStaffContractBusinessApplyModel::TH_BUSINESS_TYPE_WORKFLOW && strtotime(date('Y-m-d')) <= strtotime($contractInfo['contract_end_date'])) {
            $data['is_display'] = self::NOTICE_IS_DISPLAY_YES;
            $data['url'] = $submitPageUrlNoSelect;
            $data['status'] = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_PENDING;
            return $data;
        }

        //业务状态：同意， 业务类型一定是 审批流
        if($applyInfo['business_status'] == HrStaffContractBusinessApplyModel::BUSINESS_STATUS_AGREE && $applyInfo['business_type'] == HrStaffContractBusinessApplyModel::TH_BUSINESS_TYPE_WORKFLOW && strtotime(date('Y-m-d')) <= strtotime($contractInfo['contract_end_date'])) {
            //审批状态是 待审批及已同意，不展示入口，否则展示
            if(in_array($applyInfo['status'], [enums::$audit_status['panding'], enums::$audit_status['approved']])) {
                $data['fail_message'] = '审批状态是 待审批或已同意';
                return $data;
            }
            //其余情况是要展示的：驳回，超时关闭，撤销
            $data['is_display'] = self::NOTICE_IS_DISPLAY_YES;
            $data['url'] = $submitPageUrl;
            $data['status'] = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_AGREE;
            return $data;
        }

        //最新状态是不续约，则展示入口
        if($applyInfo['business_status'] == HrStaffContractBusinessApplyModel::BUSINESS_STATUS_REJECT && strtotime(date('Y-m-d')) <= strtotime($contractInfo['contract_end_date'])) {
            $data['is_display'] = self::NOTICE_IS_DISPLAY_YES;
            $data['url'] = $submitPageUrl;
            $data['status'] = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_REJECT;
            return $data;
        }
        $data['fail_message'] = '不展示入口信息';
        return $data;
    }

    /**
     * 打卡页直接提交，消息 同意续约
     * 创建审批流
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function addAudit($params)
    {
        if(empty($params) || empty($params['contract_id'])) {
            throw new BusinessException('params contract_id is not empty:' . $this->getTranslation()->_('data_error'));//未找到数据
        }

        //查询待续约的合同
        $contractWhere['staff_id'] = $params['staff_info_id'];
        $contractWhere['contract_status'] = HrStaffContractModel::CONTRACT_STATUS_TO_BE_RENEWED;
        $contractInfo = HrStaffContractRepository::getOne($contractWhere);

        if(empty($contractInfo)) {
            $data['fail_message'] = '未找到合同信息';
            return $data;
        }

        $staffRepository = new StaffRepository();
        $staffData       = $staffRepository->getStaffInfoOne($params['staff_info_id']);

        if (empty($staffData)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//未找到数据
        }


        $where['contract_id'] = $params['contract_id'];
        $where['audit_status'] = [enums::$audit_status['panding']];//待审批
        $pendingInfo = HrStaffContractBusinessApplyRepository::getOneByContractId($where);

        //该合同 已经存在 待审批的审批流无需再创建
        if (!empty($pendingInfo) && !empty($pendingInfo['serial_no'])) {
            $this->logger->write_log("个人代理续约劳动合同 当前合同id已存在待审批，无需审批 id:" . $pendingInfo['id'] . ', contract_id:' . $params['contract_id'], 'notice');
            return [];
        }

        $businessWhere['contract_id'] = $params['contract_id'];
        $businessWhere['business_type_not'] = HrStaffContractBusinessApplyModel::TH_BUSINESS_TYPE_WORKFLOW;
        $businessData = HrStaffContractBusinessApplyRepository::getOneByContractId($businessWhere);

        $storeInfo = SysStoreRepository::getSysStoreInfo($staffData['sys_store_id'], ['manage_region', 'manage_piece']);

        $staffCurrentData['sys_store_id']      = $staffData['sys_store_id'] ?? '';
        $staffCurrentData['job_title']         = $staffData['job_title'] ?? 0;
        $staffCurrentData['hire_type']         = $staffData['hire_type'] ?? 0;
        $staffCurrentData['hire_date']         = !empty($staffData['hire_date']) ? date('Y-m-d', strtotime($staffData['hire_date'])) : '';
        $staffCurrentData['contract_end_date'] = $businessData['contract_end_date'] ?? '';
        $staffCurrentData['region_id']         = $storeInfo['manage_region'] ?? 0;
        $staffCurrentData['piece_id']          = $storeInfo['manage_piece'] ?? 0;
        $contract_end_date = $businessData['contract_end_date'] ?? '';
        $staffCurrentData['leave_date']        = !empty($contract_end_date) ? date('Y-m-d', strtotime("{$contract_end_date} +1 days")) : '';

        //固化当前 员工信息 ；
        $audit_json = json_encode($staffCurrentData, JSON_UNESCAPED_UNICODE);

        $serialNo   = $this->getRandomId();

        $db = $this->getDI()->get('db');
        try {
            $db->begin();

            $time = date('Y-m-d H:i:s');
            $applyData = new HrStaffContractBusinessApplyModel();
            $applyData->contract_id = $params['contract_id'];
            $applyData->staff_info_id = $staffData['staff_info_id'];
            $applyData->business_type = HrStaffContractBusinessApplyModel::MY_BUSINESS_TYPE_WORKFLOW;
            $applyData->audit_created_at = $time;//审批创建时间
            $applyData->business_operation_time = $time;

            $applyData->status = enums::$audit_status['panding'];
            $applyData->business_status = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_AGREE;
            $applyData->serial_no = (!empty($serialNo) ? 'EC' . $serialNo : null);
            $applyData->audit_json = $audit_json;
            $applyData->contract_start_date = $businessData['contract_start_date'];
            $applyData->contract_end_date = $businessData['contract_end_date'];

            $applyData->save();
            $id = $applyData->id;

            //创建审批
            $server    = new ApprovalServer($this->lang, $this->timeZone);
            $requestId = $server->create($id, AuditListEnums::APPROVAL_TYPE_IC_RENEWAL,  $staffData['staff_info_id'], null, []);
            if (!$requestId) {
                throw new Exception($this->getTranslation()->_('4101'));
            }

            //将消息的业务状态置为 续约
            $updateData['business_status'] = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_AGREE;
            $updateData['business_operation_time'] = $time;
            $db->updateAsDict('hr_staff_contract_business_apply', $updateData,
                [
                    'conditions' => 'contract_id = ?',
                    'bind'       => [$params['contract_id']],
                ]
            );

            $db->commit();
        } catch (Exception $exception) {
            $db->rollBack();
            throw new Exception($this->getTranslation()->_('4101'));
        }

        return ['id' => $id];
    }

    /**
     * 获取消息详情数据
     * @param $params
     * @return mixed
     * @throws BusinessException
     */
    public function getContractInfo($params)
    {
        [$businessData, $staffData] = $this->validateData($params);

        //直接将消息 置为已读
        (new BackyardServer($this->lang,$this->timezone))->has_read_operation($businessData['msg_id'],false);

        if($businessData['business_status'] == HrStaffContractBusinessApplyModel::BUSINESS_STATUS_PENDING) {
            $contractWhere['id'] = $businessData['contract_id'];
            $contractInfo = HrStaffContractRepository::getOne($contractWhere);
            if($contractInfo['contract_status'] != HrStaffContractModel::CONTRACT_STATUS_TO_BE_RENEWED) {
                $businessData['business_status'] = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_CANCEL;
                $update['business_status'] = $businessData['business_status'];
                $db = $this->getDI()->get('db');
                $db->updateAsDict(
                    'hr_staff_contract_business_apply',
                    $update,
                    'id = '.$businessData['id']
                );
            }
        }


        $result['staff_info_id'] = $staffData['staff_info_id'] ?? '';
        $result['name']          = $staffData['name'] ?? '';
        $result['date']          = $businessData['contract_end_date'];
        $result['status']        = intval($businessData['business_status']);//是否同意续期：1同意，2不同意, 3 已作废

        return $result;
    }

    /**
     * TH 不续约
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function reject($params)
    {
        //查询待续约的合同，最新的待续约，可能跟消息对应的合同不一致
        $contractWhere['staff_id'] = $params['staff_id'];
        $contractWhere['contract_status'] = HrStaffContractModel::CONTRACT_STATUS_TO_BE_RENEWED;
        $contractInfo = HrStaffContractRepository::getOne($contractWhere);

        if(empty($contractInfo)) {
            throw new BusinessException($this->getTranslation()->_('renew_contract_submit_data_error'));//当前合同状态，无法提交
        }

        $where['contract_id'] = $contractInfo['id'];
        $applyData = HrStaffContractBusinessApplyRepository::getOneByContractId($where);

        if(empty($applyData)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//当前合同状态，无法提交
        }

        if($applyData['business_status'] == HrStaffContractBusinessApplyModel::BUSINESS_STATUS_REJECT) {
            return [];
        }
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $updateData['business_status'] = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_REJECT;
            $updateData['business_operation_time'] = date('Y-m-d H:i:s');
            $db->updateAsDict('hr_staff_contract_business_apply', $updateData,
                [
                    'conditions' => 'contract_id = ?',
                    'bind'       => [$contractInfo['id']],
                ]
            );

            $data['id'] = $applyData['id'];
            $data['contract_id'] = $applyData['contract_id'];
            $data['business_type'] = $applyData['business_type'];
            $data['business_status'] = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_REJECT;
            $rmq = new RocketMQ('renew-contract-business');
            $rmq->setType(RocketMQ::TAG_NAME_RENEW_CONTRACT);
            $rid = $rmq->sendMsgByTag($data);
            $this->logger->write_log('backyard to hcm rmq-renew-contract-business exception: ' . $rid, 'info');
            if(!$rid) {//mq 发送失败
                $this->logger->write_log(['RenewContractBusinessServer-reject-mq-fail' => $data]);
                throw new Exception($this->getTranslation()->_('accident_report_influence_scope_3'));
            }

            $db->commit();
            return [];
        } catch (Exception $e) {
            $db->rollBack();
            $this->logger->error(['function' => 'RenewContractBusinessServer-reject', 'message' => $e->getMessage(), 'Line' => $e->getLine(), 'params' => $params]);
            throw new Exception($this->getTranslation()->_('accident_report_influence_scope_3'));
        }
    }

}