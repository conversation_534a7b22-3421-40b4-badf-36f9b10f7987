<?php

namespace FlashExpress\bi\App\Modules\Th\Server;


use FlashExpress\bi\App\Enums\AuditDetailOperationsEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffOutsourcingModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Modules\Th\library\Enums\HrJobTitleEnums;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\HrStaffOutsourcingUrgentRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditOptionRule;
use FlashExpress\bi\App\Server\OsStaffServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysDepartmentServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use Exception;
use FlashExpress\bi\App\Models\backyard\HrStaffOutsourcingUrgentModel;


class OsStaffServer extends GlobalBaseServer
{


    /**
     * 获取外协员工职位列表
     * @param array $paramIn
     * @return array
     */
    public function getShortTermJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_id');

        //[2]获取申请人所在部门
        $staffInfo = $this->staff->getStaffInfoById($staffId);

        //昕哲需求 ： https://shimo.im/docs/93vWtkjkXWc6tWxG/read
        //短期外协职位列表
        //1)Network Management、Network Operations部门,职位列表：[Van Courier、Bike Courier、Warehouse Staff (Sorter)]；
        //如果有岛屿还需增加[Boat Courier]
        //2)Shop Management 职位列表
        //2.1) shop_pickup_only、shop_ushop类型网点 [Shop Officer、Warehouse Staff (Sorter)]
        //2.2) OS类型网点 [Onsite Officer、Warehouse Staff (Sorter)]
        //2.3) 其他网点 [Warehouse Staff (Sorter)]
        //3)Fulfillment  [Warehouse Staff (Sorter)]
        //3)Hub Management  [Warehouse Staff (Sorter)、Hub Staff]
        switch ($staffInfo['sys_department_id']) {
            case enums::$department['Network Management']:
            case enums::$department['Network Bulky']:
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['van_courier'],
                        'job_title'     => 'Van Courier',
                    ],
                    [
                        'job_id'        => enums::$job_title['bike_courier'],
                        'job_title'     => 'Bike Courier',
                    ],
                    [
                        'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                        'job_title'     => 'Warehouse Staff (Sorter)',
                    ],
                    [
                        'job_id'        => enums::$job_title['tricycle_courier'],
                        'job_title'     => 'Tricycle Courier',
                    ],
                ];
                if ($this->os->isStoreIsland($staffInfo['sys_store_id'])) {
                    $returnArr = array_merge($returnArr, [
                        [
                            'job_id'        => enums::$job_title['boat_courier'],
                            'job_title'     => 'Boat Courier',
                        ],
                    ]);
                }
                break;
            case enums::$department['Network Operations']:
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['van_courier'],
                        'job_title'     => 'Van Courier',
                    ],
                    [
                        'job_id'        => enums::$job_title['bike_courier'],
                        'job_title'     => 'Bike Courier',
                    ],
                    [
                        'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                        'job_title'     => 'Warehouse Staff (Sorter)',
                    ],
                ];
                if ($this->os->isStoreIsland($staffInfo['sys_store_id'])) {
                    $returnArr = array_merge($returnArr, [
                        [
                            'job_id'        => enums::$job_title['boat_courier'],
                            'job_title'     => 'Boat Courier',
                        ],
                    ]);
                }
                break;
            case enums::$department['Shop Management']:
                if (in_array($staffInfo['category'], [enums::$stores_category['shop_pickup_only'], enums::$stores_category['shop_ushop']])) {
                    $returnArr     = [
                        [
                            'job_id'        => enums::$job_title['shop_officer'],
                            'job_title'     => 'Shop Officer',
                        ],
                        [
                            'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                            'job_title'     => 'Warehouse Staff (Sorter)',
                        ],
                    ];

                } else if (in_array($staffInfo['category'], [enums::$stores_category['os']])) {
                    $returnArr     = [
                        [
                            'job_id'        => enums::$job_title['onsite_officer'],
                            'job_title'     => 'Onsite Officer',
                        ],
                        [
                            'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                            'job_title'     => 'Warehouse Staff (Sorter)',
                        ],
                    ];
                } else {
                    $returnArr     = [
                        [
                            'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                            'job_title'     => 'Warehouse Staff (Sorter)',
                        ],
                    ];
                }
                break;
            case enums::$department['Fulfillment']:
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                        'job_title'     => 'Warehouse Staff (Sorter)',
                    ],
                ];
                break;
            case enums::$department['Hub Management']:
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                        'job_title'     => 'Warehouse Staff (Sorter)',
                    ],
                    [
                        'job_id'        => enums::$job_title['hub_staff'],
                        'job_title'     => 'Hub Staff',
                    ],
                ];
                break;
            default:
                $returnArr = [];
                break;
        }
        return $returnArr;
    }

    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result = $this->getOsStaffDetail(['id' => $auditId]);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }
        $demendNum = !empty($result['final_audit_num']) ? $result['final_audit_num']: ($result['demend_num'] ?? '');
        if (!empty($result['out_company_data'])){
            $out_company_data_arr = json_decode($result['out_company_data'], true);
            $out_company_ids_arr = array_column($out_company_data_arr, 'out_company_id');
            $company_data = OutsourcingCompanyModel::find([
                'conditions' => 'id in ({company_id:array}) ',
                'bind'       => ['company_id' => $out_company_ids_arr],
                'columns'    => 'id,company_name',
            ]);
            $company_data = $company_data ? $company_data->toArray() : [];
            if($company_data) {
                $company_data = array_column($company_data, 'company_name', 'id');
            }
            $out_company_arr = [];
            foreach ($out_company_data_arr as $k=>$v){
                if (!isset($company_data[$v['out_company_id']])){
                    continue;
                }

                $out_company_arr[$k]['serial_num'] = $k+1;
                $out_company_arr[$k]['out_company_id'] = $v['out_company_id'];
                $out_company_arr[$k]['company_name'] = $company_data[$v['out_company_id']] ?? '';
                //申请人数
                $out_company_arr[$k]['demend_num'] = $v['demend_num'];
                //核定人数（最终核定人数）
                if(!empty($v['final_audit_num'])) {
                    $out_company_arr[$k]['final_audit_num'] = $v['final_audit_num'];
                }

                if(!empty($v['apply_update_num'])) {
                    $out_company_arr[$k]['apply_update_num'] = $v['apply_update_num'];
                }
            }
        }else{
            $out_company_arr = [];
            if(!empty($result['out_company_id'])) {
                $res = $this->getOutCompanyInfoOne($result['out_company_id']);

                $outCompanyName = !empty($res) ? $res['company_name'] : '';
                $out_company_info = [
                    'serial_num'=> 1,
                    'out_company_id'=> $result['out_company_id'],
                    'company_name'=> empty($outCompanyName) ? '' : $outCompanyName,
                    'demend_num'         => $demendNum,
                ];
                $out_company_arr[] = $out_company_info;
            }
        }

        $department = (new DepartmentRepository())->getDepartmentNameById($staff_info['node_department_id'] ?? '');
        $ffmOsJobTitles = array_column($this->getFFMOsJobTitle(), 'job_id');
        if (in_array($result['job_id'], $ffmOsJobTitles)) {
            $department = (new SysDepartmentServer())->getDepartmentDetail($result['department_id'])['name'] ?? '';
        }
        $hireOsType = $result['hire_os_type'];
        $detailLists['apply_parson']       = sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? '');
        $detailLists['apply_department']   = sprintf('%s - %s', $staff_info['depart_name'] ?? '',
            $staff_info['job_name'] ?? '');
        $detailLists['os_mode']            = $this->getTranslation()->_('os_request_mode_' . $result['os_type']);
        $detailLists['os_staff_job_title'] = $result['job_title'] ?? '';
        $detailLists['os_outsourcing_hire_type'] = $this->getTranslation()->_("os_outsourcing_hire_type_{$hireOsType}");//雇佣类型
        $detailLists['department']         = $department ?? '';
        $detailLists['store']              = $result['store_name'] ?? '';
        $detailLists['employment_date']    = $result['employment_date'] ?? '';
        $detailLists['employment_days']    = $result['employment_days'] ?? '';
        $detailLists['work_shift']         = $result['work_shift'] ?? '';
        //非hub外协的 展示申请人数
        if(empty($out_company_arr)) {
            $detailLists['demend_num']         = $demendNum ?? '';
        }
        if(isset($result['part_time'])) {
            $detailLists['part_time']      = $result['part_time'] ?? '';
        }
        $detailLists['reason_app']         = $result['reason'] ?? '';
        $detailLists['remark']             = $result['remark'] ?? '';
        $detailLists['live_photo']         = $result['image_path'] ?? '';

        //驳回状态，需要显示驳回原因
        if ($result['status'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
        }

        $returnData['data']['detail'] = $this->format($detailLists);
        $data = [
            'title'      => $this->auditlist->getAudityType(enums::$audit_type['OS']),
            'id'         => $result['id'],
            'staff_id'   => $result['staff_id'],
            'type'       => enums::$audit_type['OS'],
            'created_at' => $result['created_at'],
            'updated_at' => $result['updated_at'],
            'status'     => $result['status'],
            'is_stint'     => isCountry('TH') && $result['os_type'] ==  1 && in_array($result['job_id'],
                [
                    enums::$job_title['van_courier'],
                    enums::$job_title['bike_courier'],
                    enums::$job_title['boat_courier'],
                    enums::$job_title['tricycle_courier'],
                    enums::$job_title['outsource'],
                    enums::$job_title['security_outsource'],
                ]) ? 1 : 0,
            'status_text'=> $this->auditlist->getAuditStatus('10' . $result['status']),
            'serial_no'  => $result['serial_no'] ?? '',
            'demend_num' => $demendNum,
            'final_audit_num'   => $result['final_audit_num'],
            'employment_days'   => $result['employment_days'],
            'source_category'   => $result['source_category'],
            'out_company_list'  => $out_company_arr,
        ];

        //获取待审批的审批人
        $approvalList = AuditApprovalModel::find([
            'columns' => 'approval_id',
            'conditions' => "biz_value = :value: and biz_type = :type: and state = 1 and deleted = 0",
            'bind' => [
                'type'  => enums::$audit_type['OS'],
                'value' => $auditId,
            ],
        ])->toArray();
        $approvalListArr = array_column($approvalList, 'approval_id');

        //待审批的审批人需要显示参考表 ffm订单不需要展示参考数据
        if (in_array($user, $approvalListArr) && $result['status'] == 1) {
            $returnData['data']['os_extend'] = $result['extend'] ?? [];
        }

        if ($result['status'] == 2) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('final_num'), 'value' => $result['final_audit_num']],
            ];
        } else if ($result['status'] == 3) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('reject_reason'), 'value' => $result['reject_reason']],
            ];
        }

        //如果存在可编辑字段，需返回默认参数
        $auditShowType  = $this->getAuditDetailRequest()->getAuditShowType();
        $auditStateType = $this->getAuditDetailRequest()->getAuditStateType();
        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);

        //审批状态为待审批 && 并且查看我的待审批 && 存在可编辑字段时，
        //返回当前节点的可编辑字段
        if ($result['status'] == enums::APPROVAL_STATUS_PENDING &&
            $auditShowType . $auditStateType == '21' &&
            $approvalServer->isExistCanEditField($auditId, AuditListEnums::APPROVAL_TYPE_OS) && in_array($result['job_id'],$this->getOutsourceCanEditNnumJobTitle())
        ) {
            //获取可编辑字段
            $canEditField = $approvalServer->getCanEditFieldColumns($auditId, AuditListEnums::APPROVAL_TYPE_OS,
                AuditDetailOperationsEnums::RESPONSE_STRUCTURE_COLUMN_NAME);
        }


        $is_update_demend_num = false;
        if($result['status'] == enums::APPROVAL_STATUS_APPROVAL &&  in_array($result['job_id'],$this->getOutsourceCanEditNnumJobTitle()) && $result['staff_id'] == $user) {
            $number = (new SettingEnvServer())->getSetVal('os_hub_support_reducing_order_num');
            $number = empty($number) ? 2 : $number;
            $number = $number * 60;
            $currentTime = date('Y-m-d H:i:s');
            $shiftStartTime = $result['employment_date'] . ' ' . $result['shift_begin_time'];
            $shiftStartTime = date('Y-m-d H:i:00', strtotime("{$shiftStartTime} -{$number} minutes"));

            $is_update_demend_num = strtotime($currentTime) < strtotime($shiftStartTime);
        }

        // outsource 职位 $canEditField 中 存在 ：final_audit_num 字段 则 审批人展示：修改按钮
        $returnData['data']['can_edit_field'] = $canEditField ?? [];
        $returnData['data']['is_update_demend_num'] = $is_update_demend_num;
        $returnData['data']['head']   = $data;
        return $returnData;
    }

    /**
     * @description 自定义审批人审批按钮
     * @param $auditId
     * @return array
     */
    public function customiseOptions($auditId): array
    {
        $result = $this->getOsStaffDetail(['id' => $auditId]);
        if (!empty($result['out_company_data'])){
            return array_merge(AuditDetailOperationsEnums::BUTTON_COMMON_APPROVAL, [18]);
        }else{
            return array_merge(AuditDetailOperationsEnums::BUTTON_COMMON_APPROVAL, [6,18]);
        }
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(true,
            false,
            false,
            false,
            false,
            false);
    }

    protected function getUpdateValidationJobTitle()
    {
        $jobTitles = [enums::$job_title['outsource'], enums::$job_title['security_outsource']];

        return array_merge($jobTitles, array_column($this->getFFMOsJobTitle(), 'job_id'));
    }

    /**
     * 可以修改需求人数的职位
     * @return array
     */
    protected function getOutsourceCanEditNnumJobTitle(): array
    {
        $jobTitles = [enums::$job_title['outsource']];

        return array_merge($jobTitles, array_column($this->getFFMOsJobTitle(), 'job_id'));

    }




    public function getFFMOsJobTitle()
    {
        return [
            [
                'job_id' => HrJobTitleEnums::JOB_TITLE_OUTSOURCE_WAREHOUSE_SORTER,
                'job_title' => 'Outsource-Warehouse Sorter',
            ],
            [
                'job_id' => HrJobTitleEnums::JOB_TITLE_OUTSOURCE_PARCEL_UNLOADER,
                'job_title' => 'Outsource-Parcel Unloader',
            ],
        ];

    }


    /**
     * 获取职位列表
     * @param array $paramIn
     * @return array
     */
    public function getOsJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $osType     = $this->processingDefault($paramIn, 'type');
        $staffInfo  = $this->processingDefault($paramIn, 'userinfo');
        $p['staff_id'] = $staffInfo['staff_id'];
        $ffmFlag = $this->ffmPermission($p);
        if ($ffmFlag) {
            return $this->getFFMOsJobTitle();
        }

        //hub 外协 申请订单。
        $hubOsRoles = UC('outsourcingStaff')['hubOsRoles'];
        if(array_intersect($hubOsRoles, $staffInfo['positions'])) {
            if($osType == enums::$os_staff_type['normal']) {
                return [
                    [
                        'job_id' => enums::$job_title['outsource'],
                        'job_title' => 'Outsource',
                    ],
                ];
            } else {
                return [
                    [
                        'job_id' => enums::$job_title['security_outsource'],
                        'job_title' => 'Security Outsource',
                    ],
                ];
            }
        }

        //pdc 可选职位
        $pdcManagerList = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getManagerByDepartmentIdFromCache([self::DEPARTMENT_ID_PDC_OPERATIONS]);
        if (in_array($staffInfo['staff_id'], $pdcManagerList)){
            return [
                [
                    'job_id' => enums::$job_title['outsource'],
                    'job_title' => 'Outsource',
                ],
            ];
        }


        //[2]组织列表
        //1)短期外协职位下拉列表[全部职位]
        //2)长期外协职位下拉列表[Hub Staff|Onsite Officer|Shop Officer]
        //3)车队外协下拉列表[Van Courier]
        switch ($osType) {
            case enums::$os_staff_type['normal']: //短期外协
                $returnArr = $this->getShortTermJobTitleList(['staff_id' => $staffInfo['id']]);
                break;
            case enums::$os_staff_type['long_term']: //长期外协
                $returnArr = $this->getLongTermJobTitleList(['staff_id' => $staffInfo['id']]);
                break;
            case enums::$os_staff_type['motorcade']: //车队外协
                $returnArr = [
                    [
                        'job_id'        => enums::$job_title['van_courier'],
                        'job_title'     => 'Van Courier',
                    ],
                ];
                break;
            default:
                $returnArr = [];
                break;
        }
        return $returnArr;
    }

    /**
     * 获取请求列表
     */
    public function getRequestList($paramIn = [])
    {
        //[1]参数定义
        $staffInfo      = $this->processingDefault($paramIn, 'userinfo');
        $jobId          = $this->processingDefault($paramIn, 'job_id', 2);
        $select_store_id= $this->processingDefault($paramIn, 'store_id', 1);
        $shift_duration = $this->processingDefault($paramIn, 'shift_duration', 2);
        $hireOsType     = $this->processingDefault($paramIn, 'hire_os_type');
        $storeId        = $staffInfo['organization_id'] ?? '';
        $storeName      = $staffInfo['organization_name'] ?? '';
        $type           = $staffInfo['organization_type'] ?? '';
        $jobTitle       = $staffInfo['job_title'] ?? '';
        $positions      = $staffInfo['positions'] ?? [];
        $sourceCategory = 0;//来源类型

        //[2]获取网点详情
        $storeInfo = (new SysStoreServer())->getStoreByid($storeId);

        //hub可以申请长期外协的角色
        $hubRequestRole = UC('outsourcingStaff')['osLongPeriodRequestRole'];

        //可以申请车队外协申请的角色
        $motorcadeRequestRole = UC('outsourcingStaff')['osMotorcadeRequestRole'];

        //[3]长期外协申请条件
        // 1-HUB部门的网点经理、区域经理
        // 2-申请人是特殊网点（OS_CLD-Chilindo、OS_LAS-KA）的正主管
        // 3-OS、UShop、Shop(pickup only)
        $stores   = env('os_staff_long_term_stores', "'TH02020402','TH02030208'");
        $storeIds = explode(',', $stores);
        $p['staff_id'] = $staffInfo['staff_id'];
        $ffmFlag = $this->ffmPermission($p);
        if($ffmFlag){
            $ret['os_mode'] = enums::$os_staff_type['normal'];
            $ret['os_type'] = [
                ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
            ];
        }else if (in_array($storeId, $storeIds) && $jobTitle == enums::$job_title['branch_supervisor'] ||
            $storeInfo['category'] == enums::$stores_category['hub'] && !empty(array_intersect($positions,
                array_keys($hubRequestRole))) ||
            in_array($storeInfo['category'], [
                enums::$stores_category['shop_pickup_only'],
                enums::$stores_category['shop_ushop'],
                enums::$stores_category['os'],
            ])
        ) {
            $ret['os_mode'] = enums::$os_staff_type['long_term'];
            $ret['os_type'] = [
                ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
                ["code" => 2, "title" => $this->getTranslation()->_('os_request_mode_2')],
            ];
        } else {
            if ($type == 2 && $storeId == enums::$department['Transportation'] && array_intersect($positions,
                    array_keys($motorcadeRequestRole))) { //车队外协
                $ret['os_mode'] = enums::$os_staff_type['motorcade'];
                $ret['os_type'] = [
                    ["code" => 3, "title" => $this->getTranslation()->_('os_request_mode_3')],
                ];
            } else { //短期外协
                $ret['os_mode'] = enums::$os_staff_type['normal'];
                $ret['os_type'] = [
                    ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
                ];
            }
        }

        //PDC Operations 负责人
        $pdcManagerList = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getManagerByDepartmentIdFromCache([self::DEPARTMENT_ID_PDC_OPERATIONS]);

        $hubOsRoles = UC('outsourcingStaff')['hubOsRoles'];
        //hub 外协工单，分拨经理 有入口权限
        $is_out_company   = false;
        $out_company_list = [];

        //这个部门id java那边的 应该是对应 node department id
        $departmentInfo = SysDepartmentModel::findFirst([
            'conditions' => 'id = :dep_id:',
            'bind' => ['dep_id' => $staffInfo['department_id']],
        ]);
        $ret['department']             = $departmentInfo->name ?? '';
        $ret['department_id']          = $departmentInfo->id ?? '';

        //网点可选项为 单选，默认当前申请人所属网点。可选择作为网点负责人负责的Fulfillment类型的网点
        if ($ffmFlag) {
            $storeList = $this->getFFMStoreList();
            $is_out_company   = true;
            $out_company_list = $this->getOutCompanyInfo();
            $this->setDisableShift([]);
            $ret['store_list'] = $storeList;
            if ($select_store_id && $departmentInfo = $this->getFFMLeave2DepartmentInfoByStoreId($select_store_id)) {
                $ret['department']    = $departmentInfo->name ?? '';
                $ret['department_id'] = $departmentInfo->id ?? '';
                $storeInfo            = (new SysStoreServer($this->timezone))->getStoreName([$select_store_id]);
                $storeName            = $storeInfo[$select_store_id] ?? '';
                $storeId              = $select_store_id;
            }
        }else if (array_intersect($hubOsRoles, $positions)) {
            //短期外协
            $ret['os_mode']   = enums::$os_staff_type['normal'];
            $ret['os_type']   = [
                ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
                ["code" => 2, "title" => $this->getTranslation()->_('os_request_mode_2')],
            ];
            $is_out_company   = true;
            $out_company_list = $this->getOutCompanyInfo();
            $this->setDisableShift([]);//分拨经理查看全部的班次

            //分拨经理查 管辖网点
            $ret['store_list'] = $this->getStaffManageStore($staffInfo['staff_id']);
        } else if (in_array($staffInfo['staff_id'], $pdcManagerList)){
            //短期外协
            $ret['os_mode']   = enums::$os_staff_type['normal'];
            $ret['os_type']   = [
                ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
            ];
            $is_out_company   = true;
            $out_company_list = $this->getOutCompanyInfo();
            $this->setDisableShift([]);//分拨经理查看全部的班次

            //分拨经理查 管辖网点
            $ret['store_list'] = $this->getStaffManagePdcStore($staffInfo['staff_id']);
        } else {
            $ret['store_list'] = [['store_id' => $storeId, 'store_name' => $storeName]];
        }

        $ret['store_id'] = $storeInfo['id'] ?? '';//有可能是总部 就是空则取所属部门的关联网点(前端控制 后端只返回 所属网点)

        //获取班次列表 半天或者全天
        $shiftInfo = [];
        if(!empty($jobId) && !empty($hireOsType)){
            if($jobId == enums::$job_title['warehouse_staff_sorter'] && $hireOsType == HrStaffOutsourcingModel::HIRE_OS_TYPE_3){
                $shiftInfo = $this->getShiftList($jobId, HrShiftModel::SHIFT_GROUP_HALF_DAY_SHIFT);
            }else{
                $shift_group = $is_out_company && isset(HrShiftModel::$shiftGroup[$shift_duration]) ? HrShiftModel::$shiftGroup[$shift_duration] : HrShiftModel::SHIFT_GROUP_FULL_DAY_SHIFT;
                $shiftInfo             = $this->getShiftList($jobId, $shift_group);
            }
        }
        $ret['store_name']             = $ret['os_mode'] == enums::$os_staff_type['motorcade'] ? '' : $storeName;
        $ret['source_category']        = $sourceCategory;
        $ret['shift_duration_default'] = $is_out_company && $jobId != enums::$job_title['security_outsource'] ? HrShiftModel::SHIFT_DURATION_1 : 0;//非HUB 0不展示 班次时长，默认1 ：9小时，2：:5小时，3：:4小时
        $ret['shift_info']             = $shiftInfo;
        $ret['reason']                 = $this->getOsStaffReqReason($storeId);
        $ret['is_out_company']         = $is_out_company;
        $ret['out_company_list']       = $out_company_list;
        $ret['shift_duration']         = $this->getShiftDuration();
        //半天班次 跟 上面合并到一个字段 这个字段废弃
//        $ret['shift_half_day']   = $jobId == enums::$job_title['warehouse_staff_sorter'] ? $this->getShiftList($jobId, HrShiftModel::SHIFT_GROUP_HALF_DAY_SHIFT) : [];

        return $ret;
    }

    /**
     * 外协员工申请班次时长
     * @return array[]
     */
    public function getShiftDuration(): array
    {
        return [
            ['shift_duration' => HrShiftModel::SHIFT_DURATION_1, 'shift_duration_text' => '9h'],
            ['shift_duration' => HrShiftModel::SHIFT_DURATION_2, 'shift_duration_text' => '5h'],
            ['shift_duration' => HrShiftModel::SHIFT_DURATION_3, 'shift_duration_text' => '4h'],
        ];
    }


    public function getFFMLeave2DepartmentInfoByStoreId($storeId)
    {
        if (empty($storeId)) {
            return null;
        }
        $departmentModel = SysdepartmentModel::findFirst([
            'conditions' => 'relevance_store_id = :relevance_store_id:',
            'bind'       => [
                'relevance_store_id' => $storeId,
            ],
        ]);
        if (empty($departmentModel)) {
            return null;
        }
        return $departmentModel;
    }



    /**
     * 添加外协员工申请
     * @param array $paramIn    传入参数
     * @return mixed
     * @throws Exception
     */
    public function addOsStaff($paramIn = [])
    {
        //[1]参数定义
        $staffId         = $this->processingDefault($paramIn, 'staff_id');
        $jobId           = $this->processingDefault($paramIn, 'job_id');
        $employDate      = $this->processingDefault($paramIn, 'employment_date');
        $employDays      = $this->processingDefault($paramIn, 'employment_days');
        $shiftId         = $this->processingDefault($paramIn, 'shift_id');
        $demendNum       = $this->processingDefault($paramIn, 'demend_num');
        $reason          = $this->processingDefault($paramIn, 'reason');
        $reason          = addcslashes(stripslashes($reason), "'");
        $submit_store_id = $this->processingDefault($paramIn, 'store_id');
        $reasonType      = $this->processingDefault($paramIn, 'reason_type', 2);
        $imagePathArr    = $this->processingDefault($paramIn, 'image_path');
        $osType          = $this->processingDefault($paramIn, 'os_type', 2);
        $store_id        = $this->processingDefault($paramIn, 'organization_id');
        $out_company_id  = $this->processingDefault($paramIn, 'company_id', 2);
        $out_company_data= $this->processingDefault($paramIn, 'out_company_data',3);
        $need_remark     = $this->processingDefault($paramIn, 'need_remark');

        $need_remark     = addcslashes(stripslashes($need_remark), "'");
        $osHireType      = $this->processingDefault($paramIn, 'hire_os_type');//雇佣类型

        $staffInfo = $this->staff->checkoutStaff($staffId);

        //由于operation部门在ms中存的是子部门,
        //并且hr中job_title与部门的关联关系是job_title与一级部门的关联而不是子部门关联
        //该位置必须查询申请人在bi系统中的部门
        $staff_info = $this->staff->getStaffInfoById($staffId);
        if ($staffInfo) {
            $department_id = $osType == enums::$os_staff_type['motorcade'] ? 32 : $staffInfo['department_id'];
        } else {
            $department_id = null;
        }

        if (empty($department_id)) {
            throw new ValidationException($this->getTranslation()->_('err_msg_do_not_have_department'));
        }

        if (!empty($out_company_data)){
            if (count($out_company_data) != count(array_unique(array_column($out_company_data, 'out_company_id')))) {
                throw new ValidationException($this->getTranslation()->_('err_msg_out_company_repeat'));
            }
            foreach ($out_company_data as $v){
               if (empty($v['out_company_id']) || empty($v['demend_num']) || intval($v['out_company_id'])<=0 || intval($v['demend_num'])<=0){
                   throw new ValidationException($this->getTranslation()->_('err_msg_out_company_repeat'));
               }
            }
        }

        $shift_info = $this->getShiftDetail($shiftId);
        $shift_start = '';
        $shift_end = '';
        if(!empty($shift_info)) {
            $shift_start = $shift_info['start'];
            $shift_end = $shift_info['end'];
        }

        $duration = [];
        if ($osType == enums::$os_staff_type['normal']) {
            $duration = $this->getDurationByShiftId([
                'shift_id'        => $shiftId,
                'employment_date' => $employDate,
            ]);
        }

        $pdcManagerList = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getManagerByDepartmentIdFromCache([self::DEPARTMENT_ID_PDC_OPERATIONS]);

        //hub 外协 申请订单。
        $hubOsRoles = UC('outsourcingStaff')['hubOsRoles'];
        $is_short_hub_os = false;


        $is_ffm_os = in_array($jobId, array_column($this->getFFMOsJobTitle(), 'job_id'));

        if ($is_ffm_os && $departmentInfo = $this->getFFMLeave2DepartmentInfoByStoreId($submit_store_id)) {
            $department_id = $departmentInfo->id ?? null;
        }

       if ($is_ffm_os || array_intersect($hubOsRoles, $paramIn['param']['position_category']) || in_array($staffId, $pdcManagerList)) {
            if(!empty($submit_store_id)) {
                //分拨经理角色 网点是选择的网点
                $store_id = $submit_store_id;
            }
            $employDateShiftStart = strtotime($employDate . ' ' . $shift_start);
            if ($osType == enums::$os_staff_type['normal']) {
                $is_short_hub_os = true;
                if (isset($duration['is_pressing']) && true === $duration['is_pressing']) {
                    // 同一天内 相同班次紧急外协用人申请只能提交一次
                    $params        = [
                        'shift_id'    => $shiftId,
                        'submit_date' => date('Y-m-d'),
                        'store_id'    => $store_id,
                    ];
                    $urgent_result = (new HrStaffOutsourcingUrgentRepository($this->timezone))->getOutsourcingUrgentByShiftIdAndSubmitDate($params);
                    if (!empty($urgent_result)) {
                        // 紧急班次每天只能申请一次
                        throw new ValidationException($this->getTranslation()->_('urgent_only_apply_once'));
                    }
                }
                // 不可选择距离当前时间小于1小时开始的班次
                if (time() > ($employDateShiftStart - 60 * 60)) {
                    throw new ValidationException($this->getTranslation()->_('shift_error'));
                }
            } else {
                // 不可选择距离当前时间小于6小时开始的班次
                if (time() > ($employDateShiftStart - 6 * 60 * 60)) {
                    throw new ValidationException($this->getTranslation()->_('shift_error_6'));
                }
            }
        } else {
            $res = $this->addOsStaffValidation($staffInfo['department_id'], $staff_info['category'], $department_id, $osType,
                $jobId);
            if($res) {
                throw new ValidationException($this->getTranslation()->_('err_msg_job_title_department_dismatch'));
            }
            //非分拨经理角色保持原逻辑
            $store_id = $osType == enums::$os_staff_type['motorcade'] ? $submit_store_id : $store_id;
        }
        //追加网点白名单
        $storeInfo = SysStoreModel::findFirstById($store_id);
        if ($storeInfo) {
            $settingEnv = (new SettingEnvServer())->listByCode([
                enums::SYSTEM_EXTERNAL_APPROVAL_WHITE_LIST,
                enums::SYSTEM_EXTERNAL_APPROVAL_USE_WHITE_LIST_STORE_CATEGORY,
            ]);
            $settingEnv = array_column($settingEnv, 'set_val', 'code');
            if (!empty($settingEnv[enums::SYSTEM_EXTERNAL_APPROVAL_USE_WHITE_LIST_STORE_CATEGORY]) && in_array($storeInfo->category,
                    explode(',', $settingEnv[enums::SYSTEM_EXTERNAL_APPROVAL_USE_WHITE_LIST_STORE_CATEGORY]))) {
                //如果申请网点不在白名单中，就拦截
                if (!empty($settingEnv[enums::SYSTEM_EXTERNAL_APPROVAL_WHITE_LIST]) && !in_array($store_id,
                        explode(',', $settingEnv[enums::SYSTEM_EXTERNAL_APPROVAL_WHITE_LIST]))) {
                    throw new ValidationException($this->getTranslation()->_('err_msg_store_not_support',
                        ['store_name' => $storeInfo->name]));
                }
            }
        }

        //[3]组织数据插入业务主数据
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $insertData = [
                'serial_no'       => 'OS'.$this->getRandomId(),
                'os_type'         => $osType,
                'staff_id'        => $staffId,
                'job_id'          => intval($jobId),
                'department_id'   => $department_id,
                'store_id'        => $store_id,
                'employment_date' => $employDate,
                'employment_days' => $employDays,
                'shift_id'        => $shiftId,
                'status'          => enums::$audit_status['panding'],
                'demend_num'      => !empty($out_company_data) ? array_sum(array_column($out_company_data,'demend_num')) : $demendNum,
                'final_audit_num' => !empty($out_company_data) ? array_sum(array_column($out_company_data,'demend_num')) : $demendNum,
                'reason_type'     => $reasonType,
                'reason'          => $reason,
                'wf_role'         => 'os_new',
                'out_company_id'  => !empty($out_company_id) ? $out_company_id : 0,
                'out_company_data'=> !empty($out_company_data) ? json_encode($out_company_data, JSON_UNESCAPED_UNICODE) : '',
                'need_remark'     => $need_remark,
                'shift_begin_time'=> $shift_start,
                'shift_end_time'  => $shift_end,
                'source_category'=> $this->sourceCategory,
                'hire_os_type'   => $osHireType,//雇佣类型
            ];
            $osStaffId  = $this->os->insertOsStaff($insertData);
            if (empty($osStaffId)) {
                throw new Exception($this->getTranslation()->_('4008'));
            }

            // 如果是短期外协工单 且 是紧急的情况下 需要插入数据
            if ($duration['duration'] && $is_short_hub_os) {
                $insert_urgent_data = [
                    'serial_no'        => $insertData['serial_no'],
                    'shift_id'         => $shiftId,
                    'shift_begin_time' => $employDate . ' ' . $shift_start . ':00',
                    'submit_date'      => date('Y-m-d'),
                ];
                $last_id            = (new HrStaffOutsourcingUrgentRepository($this->timezone))->insert($insert_urgent_data);
                if (empty($last_id)) {
                    throw new Exception($this->getTranslation()->_('4008'));
                }
            }

            //插入业务关联图片
            if (!empty($imagePathArr)) {
                $insertImgData = [];

                foreach ($imagePathArr as $image) {
                    $insertImgData[] = [
                        'id'         => $osStaffId,
                        'image_path' => $image,
                    ];
                }
                $this->pub->batchInsertImgs($insertImgData, 'OUTSOURCING_STAFF');
            }

            //HRBP根据申请的网点查找
            $extend['store_id'] = $store_id;

            //这里是 from 表单的内容,用于查找审批人
            $extend['from_submit'] = [
                'sys_store_id' => $store_id,
                'audit_type'   => AuditListEnums::APPROVAL_TYPE_OS,
            ];

            //创建
            $server    = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($osStaffId, AuditListEnums::APPROVAL_TYPE_OS, $staffId, null, $extend);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
            $db->commit();
        } catch (ValidationException $vException) {
            $db->rollback();
            throw $vException;
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }

        return $this->checkReturn([]);
    }

    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info     = $this->getOsStaffDetail(['id' => $auditId]);
        if (!empty($info)) {
            $param[] = [
                    'key'   => "os_staff_job_title",
                    'value' => $info['job_title'],
                ];
            $param[] = [
                    'key'   => "store",
                    'value' => $info['store_name'],
                ];
            $param[] = [
                    'key'   => "demend_num",
                    'value' => !empty($info['final_audit_num']) ? $info['final_audit_num'] : $info['demend_num'],
                ];

            $param[] = [
                'key'   => "employment_date",
                'value' => $info['employment_date'] ?? '',
            ];

            $param[] = [
                'key'   => "employment_days",
                'value' => $info['employment_days'] ?? '',
            ];

            $param[] = [
                'key'   => "os_shift_time",
                'value' => $info['shift_name'] ?? '',
            ];

            if(isset($info['part_time']) && isset($info['os_shift_time'])) {
                $param[] = [
                    'key'   => "part_time",
                    'value' => $info['part_time'],
                ];
            }
        }
        return $param ?? [];
    }

    /**
     * [2.4]校验天数
     * @param $employmentDays
     * @param $osStaffInfo
     * @return bool
     * @throws Exception
     */
    public function checkEmploymentDays($employmentDays, $osStaffInfo)
    {
        //[2.4]校验天数
        if (isset($employmentDays) && $employmentDays) {
            if (in_array($osStaffInfo['os_type'], [enums::$os_staff_type['motorcade'], enums::$os_staff_type['normal']])) { //车队外协 、短期外协都是1~7天
                if ($employmentDays < 1 || $employmentDays > 7) {
                    throw new Exception("'employment_days' invalid input", enums::$ERROR_CODE['1000']);
                }
            } else { //长期外协是90到365天

                //hub 外协 申请订单。
                if ($osStaffInfo['job_id'] ==  enums::$job_title['security_outsource'] && $osStaffInfo['os_type'] == enums::$os_staff_type['long_term']) {
                    if($employmentDays < 15 || $employmentDays > 90) {
                        throw new Exception("'employment_days' invalid input", enums::$ERROR_CODE['1000']);
                    }
                    return true;
                }

                if ($employmentDays < 90 || $employmentDays > 365) {
                    throw new Exception("'employment_days' invalid input", enums::$ERROR_CODE['1000']);
                }
            }
        }
        return true;
    }

    /**
     * 获取 班次 信息 条件
     * @param int $jobId
     * @param $shift_group
     * @return array
     */
    public function getCondition($jobId = 0, $shift_group)
    {
        //如果是 security_outsource 职位 则 查询 hub 安保职位对应的 班次信息
        $shift_group = $jobId == enums::$job_title['security_outsource'] ? HrShiftModel::SHIFT_GROUP_HUB_SECURITY_OUTSOURCE_SHIFT : $shift_group;
        $condition = "shift_attendance_type = :shift_attendance_type: and shift_group = :shift_group:";
        $bind['shift_attendance_type'] =  HrShiftModel::SHIFT_ATTENDANCE_TYPE_FIXED;
        $bind['shift_group']           = $shift_group;

        return [$condition, $bind];
    }
}