<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 5/5/23
 * Time: 2:16 PM
 */
namespace FlashExpress\bi\App\Modules\Th\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\Server\Vacation\MaternityServer as GlobalServer;


class MaternityServer extends GlobalServer implements LeaveInterface{

    private static $instance = null;

    public $today;

    public $thisYear;


    //任务 每年初始化调用
    public static function getInstance($lang,$timezone){
        if(!self::$instance instanceof self){
            self::$instance = new self($lang,$timezone);
        }
        return self::$instance;
    }


}