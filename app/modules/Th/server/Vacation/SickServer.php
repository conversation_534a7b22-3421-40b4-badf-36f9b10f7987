<?php
/**
 * Created by <PERSON>pStor<PERSON>.
 * User: nick
 * Date: 3/6/23
 * Time: 10:44 AM
 */


namespace FlashExpress\bi\App\Modules\Th\Server\Vacation;


use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SickLeaveCertificateModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditImageModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffConsentAgreementLogModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveProperty;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Modules\Th\Server\AuditServer;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\FaceCompareServer;
use FlashExpress\bi\App\Server\LeaveServer;
use FlashExpress\bi\App\Server\MessageServer;
use FlashExpress\bi\App\Server\Vacation\SickServer as GlobalServer;

use FlashExpress\bi\App\Server\WorkflowServer;


class SickServer extends GlobalServer implements LeaveInterface
{

    private static $instance = null;

    public $limitDays;//额度 带薪的
    public $paidType   = enums::LEAVE_TYPE_3;//带薪病假
    public $unPaidType = enums::LEAVE_TYPE_18;//不带薪病假

    public $paidDays = 30;

    //如果需要拆分两个类型  这个就是带薪的结束
    public $paidEndTime;
    public $paidEndType;
    public $unPaidStartTime;
    public $unPaidStartType;

    public $thisAll        = 0;//今年的总额度
    public $thisNum        = 0;//当年要占用的 带薪额度
    public $thisPaidDate   = [];//当年 带薪额度占用日期
    public $thisUnPaidDate = [];//当年不带薪

    public $otherAll        = 0;//跨年 另外一年的总额度
    public $otherNum        = 0;//跨年 另外一年要占用的 带薪额度
    public $otherPaidDate   = [];//跨年 带薪额度占用日期
    public $otherUnPaidDate = [];//另一年不带薪

    public $allDate = [];//主类型的 日期和 类型

    //是否跨年 如果跨年 需要拆成两半 标记成另外一年
    public $thisYear;
    public $isBetweenTwoYears = false;

    //操作状态
    public $status;
    public $serialNo;//审批编号 子类型公用
    //拆分表数据 最有可能4条记录 今年 2条 另外一年 2条
    public $thisPaidAuditId;
    public $thisUnPaidAuditId;
    public $thisPaidSplit   = [];//带薪病假拆分
    public $thisUnPaidSplit = [];//不带薪拆分

    //另外一年的
    public $otherPaidAuditId;
    public $otherUnPaidAuditId;
    public $otherPaidSplit   = [];//带薪病假拆分
    public $otherUnPaidSplit = [];//不带薪拆分
    const SICK_CERTIFICATE = 1;//病例证明
    const SICK_ANTICIPATE = 2;//预约单
    const SICK_RECEIPT = 3;//票据
    const SICK_OTHER = 4;//其他
    public static $sickImgCategory = [
        self::SICK_CERTIFICATE => 'certificate',
        self::SICK_ANTICIPATE  => 'anticipate',
        self::SICK_RECEIPT     => 'receipt',
        self::SICK_OTHER       => 'other',
    ];

    public $categoryEnum = [
        'certificate' => self::SICK_CERTIFICATE,
        'anticipate'  => self::SICK_ANTICIPATE,
        'receipt'     => self::SICK_RECEIPT,
        'other'       => self::SICK_OTHER,
    ];


    public $ticketKey = '';
    public $dayLine = 3;//病假超过3天逻辑不同
    public $workDayLimit = 7;//病假结束日期 往后推7天

    public static $sendTypeApply = 1;//申请病假发送消息类型 前端消息内容用
    public static $sendTypeAttendance = 2;//上班打卡发送消息类型 前端消息内容用
    public static $sendTypeCertificate = 3;//材料驳回发送消息类型 前端消息内容用


    //任务 每年初始化调用
    public static function getInstance(){
        if(!self::$instance instanceof self){
            self::$instance = new self();
        }
        return self::$instance;
    }

    //保存病假
    public function handleCreate($param)
    {
        //初始化数据
        $this->initData($param);

        //逻辑验证
        $flag = $this->businessCheck();
        //需要二次确认 结构
        if (is_array($flag) && empty($this->paramModel['is_submit'])) {
            return $flag;
        }

        //format 数据 保存用
        $this->dataFormat();

        //保存
        $this->dataSave();

        //提交病假后 需要发送消息
        $this->sendSickMessage();

        return $this->auditId;
    }


    //申请 添加请假 初始化数据
    protected function initData($param)
    {
        //属性 附值
        $staff_re         = new StaffRepository($this->lang);
        $this->staffInfo  = $staff_re->getStaffPosition($param['staff_id']);
        $this->paramModel = $param;

        //假期模型 是带薪病假的 类型 3
        $this->leaveProperty = StaffLeaveProperty::findFirst([
            'conditions' => "leave_type = :leave_type:",
            'bind' => ['leave_type' => $this->paidType]
        ]);

        $this->leaveServer = $leaveServer    = new LeaveServer($this->lang, $this->timeZone);
        $this->holidays = $leaveServer->staff_off_days($this->staffInfo['staff_info_id'],
            $this->paramModel['leave_start_time'], $this->paramModel['leave_end_time']);

        //判断是否跨年
        $this->isOverOneYear();

        //获取额度
        $this->getLimitDays();

        //整理入参 看申请区间 是否跨年 分成两年的时间区间
        $this->initSides();

        //人脸对比凭证
        if(!empty($this->paramModel['ticket'])){
            $this->ticketKey = $this->paramModel['ticket'];
        }



    }


    //获取对应假期额度对象 先获取 带薪病假额度 然后判断本次申请 带薪扣多少 不带薪扣多少
    public function getLimitDays()
    {
        $return['limit'] = $return['sub'] = 0.0;
        $this->limitDays = $return;
        $thisYear        = $this->thisYear;

        $remainInfo      = StaffLeaveRemainDaysModel::findFirst([
            'columns'    => 'staff_info_id,leave_type,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_3,
                'year_at'    => $thisYear,
            ],
        ]);

        //如果没有 可能是新入职员工 要重新 生成一条
        if (empty($remainInfo)) {
            [$return['sub'], $return['limit']] = $this->initSickDays($thisYear);
        }else{
            $return['sub'] = half_num($remainInfo->days);
            $return['limit'] = half_num($remainInfo->freeze_days);
        }

        //获取 已经用过的  26 类型的 假期天数 和带薪病假 公用额度 看要不要加
        $start         = $thisYear.'-01-01';
        $end           = $thisYear.'-12-31';
        $covidDays     = $this->leaveDaysBetween($this->staffInfo['staff_info_id'], $start, $end, enums::LEAVE_TYPE_26);
        $return['sub'] = $return['sub'] - $covidDays;

        //跨年申请
        if ($this->isBetweenTwoYears) {
            $thatRemain = StaffLeaveRemainDaysModel::findFirst([
                'columns'    => 'staff_info_id,leave_type,days,leave_days,year,freeze_days',
                'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
                'bind'       => [
                    'staff_id'   => $this->staffInfo['staff_info_id'],
                    'leave_type' => enums::LEAVE_TYPE_3,
                    'year_at'    => $this->isBetweenTwoYears,
                ],
            ]);

            //申请的另外一年没额度 要初始化
            if (empty($thatRemain)) {
                [$return['other_sub'], $return['other_limit']] = $this->initSickDays($this->isBetweenTwoYears);
            }else{
                $return['other_sub'] = half_num($thatRemain->days);
                $return['other_limit'] = half_num($thatRemain->freeze_days);
            }
            //疫情假 占用带薪病假额度
            $start               = $this->isBetweenTwoYears.'-01-01';
            $end                 = $this->isBetweenTwoYears.'-12-31';
            $covidDays           = $this->leaveDaysBetween($this->staffInfo['staff_info_id'], $start, $end,
                enums::LEAVE_TYPE_26);
            $return['other_sub'] = $return['other_sub'] - $covidDays;
        }

        //非工具过来的
        if (empty($this->paramModel['is_svc'])) {
            $this->limitDays = $return;
            return $return;
        }


        //如果是 页面查询工具过来的 并且 病假额度是0  要算一下 不带薪病假 天数
        $start                 = $thisYear.'-01-01';
        $end                   = $thisYear.'-12-31';
        $return['unpaid_days'] = $this->leaveDaysBetween($this->staffInfo['staff_info_id'], $start, $end,
            $this->unPaidType);
        $this->limitDays       = $return;
        return $return;
    }

    //是否跨年
    public function isOverOneYear()
    {
        //是否跨年 当前 跨后一天  或者工具补 当年跨前一年
        $this->thisYear = date('Y');
        $startYear      = date('Y', strtotime($this->paramModel['leave_start_time']));
        $endYear        = date('Y', strtotime($this->paramModel['leave_end_time']));
        //今年请明年 申请的全是明年 的日期 还没发放等1号之后申请
        if ($this->thisYear < $startYear && $this->thisYear < $endYear) {
            throw new ValidationException($this->getTranslation()->_('not_send_yet'));
        }
        //今年请去年 工具
        if($this->thisYear > $startYear && $this->thisYear > $endYear){
            $this->thisYear = $startYear;
        }

        //如果是跨年申请 标记好另外一年 获取对应额度
        if ($startYear != $endYear) {
            $this->isBetweenTwoYears = ($this->thisYear == $startYear) ? $endYear : $startYear;
        }
    }


    //根据 申请区间 判定 是否跨年 拆分成 今年和 另外一年
    public function initSides()
    {
        //申请之前的剩余额度
        $thisSub  = $this->limitDays['sub'];
        $otherSub = $this->limitDays['other_sub'] ?? 0;

        $thisYear = $this->thisYear;
        $step     = $this->paramModel['leave_start_time'];
        while ($step <= $this->paramModel['leave_end_time']) {
            //默认 都是1天 只有两边的可能 存在半天
            $days = 1;
            $type = StaffAuditLeaveSplitModel::SPLIT_TYPE_0;
            //前边
            if ($step == $this->paramModel['leave_start_time'] && $this->paramModel['leave_start_type'] == StaffAuditLeaveSplitModel::SPLIT_TYPE_2) {
                $days = 0.5;
                $type = StaffAuditLeaveSplitModel::SPLIT_TYPE_2;
            }
            //后边
            if ($step == $this->paramModel['leave_end_time'] && $this->paramModel['leave_end_type'] == StaffAuditLeaveSplitModel::SPLIT_TYPE_1) {
                $days = 0.5;
                $type = StaffAuditLeaveSplitModel::SPLIT_TYPE_1;
            }


            //今年额度
            if (date('Y', strtotime($step)) == $thisYear) {
                //还有额度 跳过 ph休息日
                if ($thisSub > 0 && in_array($step, $this->holidays)) {
                    $step = date('Y-m-d', strtotime("{$step} +1 day"));
                    continue;
                }
                //有可能半天 要拆分
                if ($thisSub > 0 && ($thisSub - $days) < 0) {
                    $this->allDate[$step] = $type;
                    $days = 0.5;
                    //前半天
                    $this->thisPaidDate[$step] = StaffAuditLeaveSplitModel::SPLIT_TYPE_1;
                    $thisSub                   -= $days;
                    //后半天 是超额的
                    $this->thisUnPaidDate[$step] = StaffAuditLeaveSplitModel::SPLIT_TYPE_2;
                    //算今年用到带薪额度的总数
                    $this->thisNum += $days;
                    $this->thisAll += 1;
                    $step = date('Y-m-d', strtotime("{$step} +1 day"));
                    continue;
                }

                if ($thisSub > 0) {
                    $this->thisPaidDate[$step] = $type;
                    $thisSub                   -= $days;
                } else {// 都是超额的
                    $this->allDate[$step] = $type;
                    $this->thisUnPaidDate[$step] = $type;
                    $step                        = date('Y-m-d', strtotime("{$step} +1 day"));
                    $this->thisAll               += $days;
                    continue;
                }
                $this->allDate[$step] = $type;
                //算今年用到带薪额度的总数
                $this->thisNum += $days;
                $this->thisAll += $days;
            } else {
                //还有额度 跳过 ph休息日
                if ($otherSub > 0 && in_array($step, $this->holidays)) {
                    $step = date('Y-m-d', strtotime("{$step} +1 day"));
                    continue;
                }
                //有可能半天 要拆分
                if ($otherSub > 0 && ($otherSub - $days) < 0) {
                    $this->allDate[$step] = $type;
                    //额度为半天扣减
                    $days = 0.5;
                    //前半天
                    $this->otherPaidDate[$step] = StaffAuditLeaveSplitModel::SPLIT_TYPE_1;
                    $otherSub                   -= $days;
                    //后半天 是超额的
                    $this->otherUnPaidDate[$step] = StaffAuditLeaveSplitModel::SPLIT_TYPE_2;
                    //走到这 都是 带薪的 扣额度
                    $this->otherNum += $days;
                    $this->otherAll += 1;
                    $step = date('Y-m-d', strtotime("{$step} +1 day"));
                    continue;
                }
                if ($otherSub > 0) {
                    $this->otherPaidDate[$step] = $type;
                    $otherSub                   -= $days;
                } else {// 都是超额的
                    $this->allDate[$step] = $type;
                    $this->otherUnPaidDate[$step] = $type;
                    $step                         = date('Y-m-d', strtotime("{$step} +1 day"));
                    $this->otherAll               += $days;
                    continue;
                }
                $this->allDate[$step] = $type;
                //走到这 都是 带薪的 扣额度
                $this->otherNum += $days;
                $this->otherAll += $days;
            }
            $step = date('Y-m-d', strtotime("{$step} +1 day"));
        }
    }


    //初始化一条 对应年周期的 带薪病假额度数据  初始化任务也调用
    public function initSickDays($year,$staffInfo = [])
    {
        //任务调用 传员工信息
        if(!empty($staffInfo)){
            $this->staffInfo = $staffInfo;
        }

        //获取对应年已经申请的病假
        $start       = $year.'-01-01';
        $end         = $year.'-12-31';
        $appliedDays = $this->leaveDaysBetween($this->staffInfo['staff_info_id'], $start, $end, $this->paidType);

        //实习生没有带薪病假额度
        $days = $this->leaveProperty->days ?? $this->paidDays;
        if($this->staffInfo['formal'] == HrStaffInfoModel::FORMAL_INTERN){
            $days = 0;
        }

        $insert['staff_info_id'] = $this->staffInfo['staff_info_id'];
        $insert['leave_type']    = $this->paidType;
        $insert['year']          = $year;
        $insert['task_date']     = date('Y-m-d');
        $insert['freeze_days']   = $days;
        $insert['days']          = bcsub($insert['freeze_days'], $appliedDays,2);
        $insert['leave_days']    = $appliedDays;

        $model = new StaffLeaveRemainDaysModel();
        $model->create($insert);
        return [half_num($insert['days']), half_num($insert['freeze_days'])];
    }

    //业务验证

    /**
     * @return void
     * @throws ValidationException
     */
    public function businessCheck()
    {
        $t = $this->getTranslation();
        //验证用户信息
        if (empty($this->staffInfo)) {
            throw new ValidationException($t->_('invalid staff id'));
        }

        if (empty($this->staffInfo['hire_date'])) {
            throw new ValidationException($t->_('invalid hire date'));
        }

        if (in_array($this->staffInfo['formal'], [HrStaffInfoModel::FORMAL_0, HrStaffInfoModel::FORMAL_FRANCHISEE_OTHER, HrStaffInfoModel::FORMAL_FRANCHISEE])) {
            throw new ValidationException($t->_('os_or_franchisee_staff_disable'));
        }

        //新增权限判断 https://flashexpress.feishu.cn/docx/USK1dOhAiod8STxnC0ccJqacn4d
        if(!$this->leaveServer->leavePermission($this->staffInfo)){
            throw new ValidationException('working country not match');
        }

        //开始时间 结束时间 验证
        if ($this->paramModel['leave_end_time'] < $this->paramModel['leave_start_time']) {
            throw new ValidationException($t->_('1010'));
        }
        //同一天 上下午 反了
        if ($this->paramModel['leave_end_time'] == $this->paramModel['leave_start_time']
            && $this->paramModel['leave_start_type'] > $this->paramModel['leave_end_type']) {
            throw new ValidationException($t->_('1010'));
        }

        $auditServer = new AuditServer($this->lang,$this->timeZone);
        //[3]查询请假记录
        $levelData = $auditServer->checkExistLeave($this->paramModel);
        if (!empty($levelData)) {
            throw new ValidationException($t->_('1012'));
        }


        //验证申请时间 不能今天之前
        if (empty($this->paramModel['is_bi'])) {
            $this->timeValidate(-3, 100, '1026');
        }

        //新需求 带薪病假 超过3天 要上传图片 只针对工具
        $need_days = $this->thisAll + $this->otherAll;
        if ($need_days >= 3 && empty($this->paramModel['image_path']['certificate']) && $this->paramModel['is_bi']) {
            throw new ValidationException($t->_('sick_certificate_need'));
        }

//        //如果是工具过来 证明资料必须有
//        $certificateKey = self::$sickImgCategory[self::SICK_CERTIFICATE];
//        if(!empty($this->paramModel['is_bi']) && empty($this->paramModel['image_path'][$certificateKey])){
//            throw new ValidationException($t->_('sick_certificate_notice'));
//        }

//        //图片最多限制5张
//        if (!empty($this->paramModel['image_path']) && count($this->paramModel['image_path']) > 5) {
//            throw new ValidationException('at most 5 photos');
//        }

        //病假图片分4个分类
        $imgFlag = false;
        foreach ($this->paramModel['image_path'] as $category => $item){
            if(!empty($item)){
                //选择其他 必须填写 other_content
                if($category == self::$sickImgCategory[self::SICK_OTHER] && empty($this->paramModel['other_content'])){
                    throw new ValidationException("need attachment description");//前端有限制 工具会走到这
                }

                if(count($item) > 3){
                    throw new ValidationException("at most 3 pictures");//前端有限制 工具会走到这
                }
                $imgFlag = true;
                break;
            }
        }
        if($imgFlag === false){
            throw new ValidationException($t->_('sick_at_least_one'));
        }

        //申请的日期 计算出来 天数是 0
        if ($this->thisAll == 0 && $this->otherAll == 0) {
            throw new ValidationException($t->_('day off for the apply date'));
        }

        if (empty($this->paramModel['is_bi']) && empty($this->paramModel['is_submit'])) {
            $this->leaveServer->checkShiftLeave($this->staffInfo['staff_info_id'],$this->paramModel);
        }

        //存在人脸对比 验证凭证
        if(!empty($this->ticketKey)){
            //人脸对比凭证验证
            (new FaceCompareServer())->checkTicket($this->ticketKey,$this->staffInfo['staff_info_id']);
            //如果对比过了 必须要照片 坐标可能是0 没获取到 就不管了
            if(empty($this->paramModel['leave_face_img'])){
                throw new ValidationException("need face_img and coordinate");
            }
        }


    }


    //整理保存入库的数据 带薪或者 不带薪 有可能是三条请假数据
    protected function dataFormat()
    {
        $this->saveAudit();

        $this->saveChildAudit();
        //整理子记录 带薪 或者不带薪 主表和拆分表数据

        $this->formatSplitTag();
    }


    //申请保存
    public function dataSave()
    {
        //保存 申请 上传图片
        $this->saveImgData();
        //保存 拆分表记录
        $this->saveSplitData();

        //保存子 申请的 拆分数据
        $this->saveChildSplitData($this->thisPaidSplit);
        $this->saveChildSplitData($this->thisUnPaidSplit);
        $this->saveChildSplitData($this->otherPaidSplit);
        $this->saveChildSplitData($this->otherUnPaidSplit);

        //额度更新 当前周期 和上个周期
        $this->saveRemainData();
    }


    //按自然年 拆分申请区间 到天 并标记到对应的所在周期内
    protected function formatSplitTag()
    {
        $data = $this->allDate;
        foreach ($data as $date => $dayType) {
            $row['audit_id']        = $this->auditId;
            $row['staff_info_id']   = $this->staffInfo['staff_info_id'];
            $row['date_at']         = $date;
            $row['type']            = $dayType;
            $row['year_at']         = date('Y', strtotime($date));
            $this->leaveSplitInfo[] = $row;
        }

        //整理 子记录的 split
        $this->thisPaidSplit    = $this->formatChildSplit($this->thisPaidDate, $this->thisPaidAuditId, $this->thisYear);
        $this->thisUnPaidSplit  = $this->formatChildSplit($this->thisUnPaidDate, $this->thisUnPaidAuditId,
            $this->thisYear);

        $this->otherPaidSplit   = $this->formatChildSplit($this->otherPaidDate, $this->otherPaidAuditId,
            $this->isBetweenTwoYears);
        $this->otherUnPaidSplit = $this->formatChildSplit($this->otherUnPaidDate, $this->otherUnPaidAuditId,
            $this->isBetweenTwoYears);
    }

    protected function formatChildSplit($data, $auditId, $year)
    {
        if (empty($data)) {
            return [];
        }
        $insert = [];
        //整理 子记录的 split
        foreach ($data as $date => $dayType) {
            $row['audit_id']      = $auditId;
            $row['staff_info_id'] = $this->staffInfo['staff_info_id'];
            $row['date_at']       = $date;
            $row['type']          = $dayType;
            $row['year_at']       = $year;
            $insert[]             = $row;
        }
        return $insert;
    }


    //撤销返还 只操作 带薪时间额度 类型 3
    public function returnRemainDays($auditId, $staffInfo,$extend = [])
    {
        $this->staffInfo = $staffInfo;
        $this->auditId   = $auditId;
        //一条主记录 可能对应 2条 跨年的 带薪病假记录 要 分别返还
        $childInfo = StaffAuditModel::find([
            'conditions' => 'staff_info_id = :staff_id: and parent_id = :audit_id:',
            'bind'       => ['staff_id'   => $staffInfo['staff_info_id'],
                             'audit_id'   => $auditId,
            ],
        ]);

        //最多两条数据 跨年情况
        foreach ($childInfo as $auditInfo) {
            //不带薪病假 不需要返还 没有额度记录
            if($auditInfo->leave_type == enums::LEAVE_TYPE_18){
                continue;
            }
            $splitInfo = $this->getUsedDays($auditInfo->audit_id);
            $this->returnDays($splitInfo);
        }
        //病假 涉及子记录状态更新 要把状态传过来
        $status = $extend['status'] ?? enums::APPROVAL_STATUS_CANCEL;
        //没有 审批状态 直接改成撤销 页面也没有显示的地方 算工资用
        $childInfo->update(['status' => $status]);

        //新增逻辑 非审核通过病假 需要修改对应的材料申请修改状态
        $c_data = SickLeaveCertificateModel::find([
            'conditions' => 'audit_id = :audit_id:',
            'bind'       => ['audit_id' => $auditId],
        ]);
        if (empty($c_data->toArray())) {
            return;
        }

        $approvalId = $extend['staff_id'] ?? 10000;
        //业务ids
        $ids = array_column($c_data->toArray(), 'id');
        $c_data->update(['status' => $extend['status'], 'approval_time' => date('Y-m-d H:i:s')]);
        //audit apply
        $applyData = AuditApplyModel::find([
            'conditions' => 'biz_type = :biz_type: and biz_value in ({ids:array})',
            'bind'       => ['biz_type' => AuditListEnums::APPROVAL_TYPE_SICK_CERTIFICATE, 'ids' => $ids],
        ]);
        //audit approval
        AuditApprovalModel::find([
            'conditions' => 'biz_type = :biz_type: and biz_value in ({ids:array})',
            'bind'       => ['biz_type' => AuditListEnums::APPROVAL_TYPE_SICK_CERTIFICATE, 'ids' => $ids],
        ])->update(['state' => $extend['status']]);
        $server = new WorkflowServer($this->lang, $this->timeZone);
        if(!empty($applyData)){
            foreach ($applyData as $app){
                $server->saveAuditlog($app, $approvalId, $status, $extend['remark'] ?? '');
            }
        }
        $applyData->update(['state' => $extend['status']]);
        return;
    }

    //操作需求上线前 历史数据的 带薪病假
    public function returnHistorySick($auditId,$staffInfo){
        $this->staffInfo = $staffInfo;
        $this->auditId   = $auditId;
        $splitInfo = $this->getUsedDays($auditId);
        $this->returnDays($splitInfo);
    }

    protected function returnDays($splitInfo)
    {
        if(empty($splitInfo)){
            $this->logger->write_log("拆分表信息异常 {$this->staffInfo['staff_info_id']} {$this->auditId} ".json_encode($splitInfo));
            return true;
        }
        $remainData = StaffLeaveRemainDaysModel::find([
            'column'     => 'staff_info_id,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycle:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_3,
                'cycle'      => array_keys($splitInfo),
            ],
        ]);

        if (empty($remainData->toArray())) {
            $this->logger->write_log("额度表信息异常 {$this->staffInfo['staff_info_id']} {$this->auditId} ".json_encode($splitInfo));
            return true;
        }

        foreach ($remainData as $remain) {
            $needBackDays = $splitInfo[$remain->year] ?? 0;//需要返还的 额度
            //使用额度 减少
            $remain->days       += $needBackDays;
            $remain->leave_days -= $needBackDays;
            $remain->update();
        }
    }


    //申请操作 额度表 扣除带薪病假的
    protected function saveRemainData()
    {
        if (!empty($this->thisNum)) {
            $this->updateRemain($this->thisYear, $this->thisNum);
        }
        if (!empty($this->otherNum)) {
            $this->updateRemain($this->isBetweenTwoYears, $this->otherNum);
        }
    }

    /**
     * @param $year
     * @param $days 如果是申请操作 为正数  撤销返还操作 为负数
     *
     */
    protected function updateRemain($year, $days)
    {
        $Remain = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_3,
                'year_at'    => $year,
            ],
        ]);

        $Remain->days       = $Remain->days - $days;
        $Remain->leave_days = $Remain->leave_days + $days;
        $Remain->update();
    }


    //-------- 保存相关
    protected function saveAudit()
    {
        $this->serialNo = $this->getRandomId();
        $reason         = addcslashes(stripslashes($this->paramModel['audit_reason']), "'");
        $this->status   = enums::APPROVAL_STATUS_PENDING;
        if (!empty($this->paramModel['is_bi'])) {
            $this->status = enums::APPROVAL_STATUS_APPROVAL;
            $reason       .= "|system_tool_add";
        }
        $timeOut = date('Y-m-d 00:00:00',strtotime('+3 day'));
        $insetData = [
            'staff_info_id'    => $this->staffInfo['staff_info_id'],
            'leave_type'       => $this->paramModel['leave_type'],
            'leave_start_time' => $this->assemblyData($this->paramModel['leave_start_time'], 1,
                $this->paramModel['leave_start_type']),
            'leave_start_type' => $this->paramModel['leave_start_type'],
            'leave_end_time'   => $this->assemblyData($this->paramModel['leave_end_time'], 2,
                $this->paramModel['leave_end_type']),
            'leave_end_type'   => $this->paramModel['leave_end_type'],
            'audit_reason'     => $reason,
            'status'           => $this->status,
            'audit_type'       => enums::$audit_type['LE'],
            'leave_day'        => $this->thisAll + $this->otherAll,
            'serial_no'        => (!empty($this->serialNo) ? 'LE'.$this->serialNo : null),
            'time_out'         => $timeOut,
        ];
        $insetData['source_type'] = (in_array($this->staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) ? 1 : 0;
        $this->leave_day = $this->thisNum + $this->nextNum;
        //如果存在对比 要坐标和人脸图片
        if (!empty($this->ticketKey)) {
            $references['leave_lat']       = $this->paramModel['leave_lat'] ?? 0;
            $references['leave_lng']       = $this->paramModel['leave_lng'] ?? 0;
            $img_prefix                    = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
            $references['leave_face_img']  = $img_prefix.$this->paramModel['leave_face_img'];
            $insetData['template_comment'] = json_encode($references);
        }
        //子类型 如果超过3天 必须要提交材料申请
        $insetData['sub_status'] = StaffAuditModel::SICK_SUB_STATUS_YES;
        //病例证明 $this->paramModel['image_path']['certificate']
        $isCertificate = $this->paramModel['image_path'][self::$sickImgCategory[self::SICK_CERTIFICATE]] ?? [];
        //只有超过3天的病假 才有没补充的状态(以病例证明为准) 工具证明是必填 不用考虑
        if ($this->leave_day >= $this->dayLine && empty($isCertificate)) {
            $insetData['sub_status'] = StaffAuditModel::SICK_SUB_STATUS_NO;
        }
        //其他分类 需要写描述
        if(!empty($this->paramModel['image_path']['other']) && !empty($this->paramModel['other_content'])){
            $insetData['other_content'] = $this->paramModel['other_content'];
        }

        $auditModel = new StaffAuditModel();
        $auditModel->create($insetData);
        $this->auditId = $auditModel->audit_id;
        $this->timeOut = $timeOut;
    }

    //保存本次请假的 子类型 带薪或者不带薪
    protected function saveChildAudit()
    {
        //保存今年带薪的
        if (!empty($this->thisPaidDate)) {
            $leaveDays             = $this->thisNum;
            $this->thisPaidAuditId = $this->formatChild($this->thisPaidDate, enums::LEAVE_TYPE_3, $leaveDays);
        }
        //今年不带薪
        if (!empty($this->thisUnPaidDate)) {
            $leaveDays               = $this->thisAll - $this->thisNum;
            $this->thisUnPaidAuditId = $this->formatChild($this->thisUnPaidDate, enums::LEAVE_TYPE_18, $leaveDays);
        }

        //另外一年 带薪
        if (!empty($this->otherPaidDate)) {
            $leaveDays              = $this->otherNum;
            $this->otherPaidAuditId = $this->formatChild($this->otherPaidDate, enums::LEAVE_TYPE_3, $leaveDays);
        }
        if (!empty($this->otherUnPaidDate)) {
            $leaveDays                = $this->otherAll - $this->otherNum;
            $this->otherUnPaidAuditId = $this->formatChild($this->otherUnPaidDate, enums::LEAVE_TYPE_18, $leaveDays);
        }
    }

    //如果子记录
    protected function formatChild($dateArr, $leaveType, $leaveDays)
    {
        $list = array_keys($dateArr);
        sort($list);
        $start = $list[0];
        $end   = end($list);

        $startType = $dateArr[$start];//如果是全天0 替换成 上午
        if($startType == StaffAuditLeaveSplitModel::SPLIT_TYPE_0){
            $startType = StaffAuditLeaveSplitModel::SPLIT_TYPE_1;
        }
        $endType   = $dateArr[$end];//如果是 全天0  替换成下午
        if($endType == StaffAuditLeaveSplitModel::SPLIT_TYPE_0){
            $endType = StaffAuditLeaveSplitModel::SPLIT_TYPE_2;
        }
        $insetData = [
            'staff_info_id'    => $this->staffInfo['staff_info_id'],
            'leave_type'       => $leaveType,
            'leave_start_time' => $this->assemblyData($start, 1, $startType),
            'leave_start_type' => $startType,
            'leave_end_time'   => $this->assemblyData($end, 2, $endType),
            'leave_end_type'   => $endType,
            'audit_reason'     => '',
            'status'           => $this->status,
            'audit_type'       => enums::$audit_type['LE'],
            'leave_day'        => $leaveDays,
            'serial_no'        => (!empty($this->serialNo) ? 'LE'.$this->serialNo : null),
            'parent_id'        => $this->auditId,
        ];

        $auditModel = new StaffAuditModel();
        $auditModel->create($insetData);
        return $auditModel->audit_id;
    }


    //split 表 保存
    protected function saveChildSplitData($data)
    {
        if (empty($data)) {
            return true;
        }
        $splitModel = new StaffAuditLeaveSplitModel();
        foreach ($data as $v) {
            $clone = clone $splitModel;
            $clone->create($v);
        }
    }


    public function handleSearch($param)
    {
        //初始化数据
        $this->initSearch($param);


        //获取额度
        $this->getLimitDays();


        //整理成 day_sub  day_limit
        return $this->formatLimitDays();
    }


    //获取假期额度 初始化数据
    protected function initSearch($param)
    {
        //属性 附值
        $staff_re         = new StaffRepository($this->lang);
        $this->staffInfo  = $staff_re->getStaffPosition($param['staff_id']);
        $this->paramModel = $param;

        //假期模型 是带薪病假的 类型 3
        $this->leaveProperty = StaffLeaveProperty::findFirst([
            'conditions' => "leave_type = :leave_type: and is_delete = 0",
            'bind' => ['leave_type' => $this->paidType]
        ]);

        $this->thisYear = date('Y');
    }

    //查询额度用 整理申请记录  day_sub  day_limit
    protected function formatLimitDays()
    {
        $return['day_limit'] = "{$this->limitDays['limit']}";//总额度
        $return['day_sub']   = "{$this->limitDays['sub']}";//剩余额度 有可能是负数
        //不带薪病假的适用天数 hcm 展示用
        if(!empty($this->paramModel['is_svc'])){
            $unpaid = [
                'code' => '18',
                'type' => 2,
                'msg'  => $this->getTranslation()->_('sick_over'),
                'day_limit' => '',
                'day_sub' => $this->limitDays['unpaid_days'] ?? 0,
            ];
            $return['unpaid_info'] = $unpaid;//不带薪 病假 已使用
        }
        return $return;
    }


    //病假 计算天数调用
    public function formatLeaveDays($param)
    {
        $this->initData($param);
        //本次请假 天数总和
        $days = $this->thisNum + $this->otherNum;
        //本次请假 超出带薪病假的天数
        $overDays = $days - $this->thisAll - $this->otherAll;

        //实习生 不计算超出额度
        if($this->staffInfo['formal'] == HrStaffInfoModel::FORMAL_INTERN){
            $overDays = 0;
        }

        $allDays = $this->thisAll + $this->otherAll;

        //超出的天数 不是本次的 还要加上 当年 所有超出的
        $thisYear = date('Y');
        $start                 = $thisYear.'-01-01';
        $end                   = $thisYear.'-12-31';
        $unpaid = $this->leaveDaysBetween($this->staffInfo['staff_info_id'], $start, $end, $this->unPaidType);
        $overDays = bcsub($overDays,$unpaid,2);
        return [$allDays, floatval($overDays)];
    }


    //审核通过 修改 子病假记录状态
    public function updateSickState($staffId,$auditId){
        StaffAuditModel::find([
            'conditions' => 'parent_id = :audit_id: and staff_info_id = :staff_id: and audit_type = :audit_type:',
            'bind' => ['audit_id' => $auditId,'staff_id' => $staffId,'audit_type' => StaffAuditModel::AUDIT_TYPE_LEAVE]
        ])->update(['status' => enums::APPROVAL_STATUS_APPROVAL]);

    }

    //每年1号 初始化任务用
    public function conditionsStr()
    {
        $conditions = 'is_sub_staff = :is_sub_staff:';
        $bind       = ['is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0];

        return [$conditions, $bind];
    }

    //任务 年1月1号 初始化一下在职人员额度数据 只保存 不计算 实习生0
    public function taskInitialize($staffInfo){

        //默认 天数
        $days = $this->paidDays;
        if($staffInfo['formal'] == HrStaffInfoModel::FORMAL_INTERN){
            $days = 0;
        }

        $model = new StaffLeaveRemainDaysModel();
        $insert['staff_info_id'] = $staffInfo['staff_info_id'];
        $insert['leave_type'] = enums::LEAVE_TYPE_3;
        $insert['year'] = date('Y');
        $insert['task_date'] =  date('Y-m-d');
        $insert['freeze_days'] = $insert['days'] = $days;

        return $model->create($insert);


    }

    //新增逻辑  申请病假成功后要发消息
    public function sendSickMessage()
    {
        $staffId = $this->staffInfo['staff_info_id'];

        //看有没有支援
        $attendanceRe = new AttendanceRepository($this->lang, $this->timeZone);
        if ($this->staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            $supportInfo = $attendanceRe->getSupportInfoBySubStaff($staffId);
            if (empty($supportInfo)) {
                $this->logger->write_log("no_sub_staff_info {$staffId}",'info');
                return true;
            }
            $staffId = $supportInfo['staff_info_id'];
        } else {
            $supportInfo = $attendanceRe->getSupportOsStaffInfo($staffId);
        }
        $contentParam['send_type'] = self::$sendTypeApply;
        $contentParam['leave_day'] = $this->leave_day;//请假天数
        $contentParam['url_type'] = 'url';//url 需要点击跳转 text 文本
        $contentParam['date_at'] = '';
        if ($this->leave_day >= $this->dayLine) {
            //获取假期结束 第一个工作日
            $firstDay = $this->getFirstWorkDay($this->paramModel['leave_end_time']);
            if(empty($firstDay)){
                $this->logger->write_log("no workday {$staffId}");
                return true;
            }
            $contentParam['date_at'] = $firstDay;
        }
        $extend['category'] = MessageEnums::MESSAGE_CATEGORY_CODE_SICK_CERTIFICATE;

        //主账号发送
        $title   = $this->getTranslation()->_('sick_leave_add_title');
        $content = 'sick-leave-materials-message?' . http_build_query($contentParam);
        //发送
        $server = new MessageServer($this->lang, $this->timeZone);
        $server->sendMessage($staffId, $title, $content, $extend);

        //子账号
        if (!empty($supportInfo)) {
            $subStaffId = $supportInfo['sub_staff_info_id'];
            if (empty($subStaffId)) {
                //还没生成工号 不发
                $this->logger->write_log("no_sub_staff_id {$staffId}", 'info');
                return true;
            }
            $contentParam['url_type'] = 'text';//子账号不跳
            $content = 'sick-leave-materials-message?' . http_build_query($contentParam);
            $server->sendMessage($subStaffId, $title, $content, $extend);
        }

        return true;
    }

    //上班打卡 判断是否有病假 发消息
    public function sickMessageAttendance($param)
    {
        $staffId = $param['staff_info_id'];
        $staffRe = new StaffRepository($this->lang);
        $attendanceRe = new AttendanceRepository($this->lang, $this->timeZone);
        $this->staffInfo = $staffRe->getStaffPosition($staffId);

        if(empty($this->staffInfo)){
            echo '没有员工信息 '.$param['staff_info_id'];
            return true;
        }

        //看是否支援
        if ($this->staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            $supportInfo = $attendanceRe->getSupportInfoBySubStaff($staffId, $param['attendance_date']);
            if (empty($supportInfo)) {
                $this->logger->write_log("att_msg no_sub_staff_info {$staffId}",'info');
                return true;
            }
            $staffId = $supportInfo['staff_info_id'];
        } else {
            $supportInfo = $attendanceRe->getSupportOsStaffInfo($staffId);
        }

        $limitDate = $param['attendance_date'] . ' 00:00:00';//打卡日期
        $data = $this->getSickData($staffId, $limitDate);

        if(empty($data)){
            return true;
        }

        //获取 所有ph
        $leaveServer = new LeaveServer($this->lang, $this->timeZone);
        $dates   = array_column($data, 'leave_end_time');
        $start   = min($dates);
        $start   = date('Y-m-d', strtotime($start));//最早的病假结束日期
        $allRest = $leaveServer->staff_off_days($staffId, $start, $param['attendance_date']);
        //看打卡这天是不是工作日 工作日不发
        if(!empty($allRest) && in_array($param['attendance_date'], $allRest)){
            $this->logger->write_log("attendance_date is rest {$staffId}", 'info');
            return true;
        }

        //看发过没有
        $ids = [$staffId];
        if(!empty($supportInfo)){
            $ids[] = $supportInfo['sub_staff_info_id'];
        }
        $auditIds = array_column($data, 'audit_id');
        $exist = StaffConsentAgreementLogModel::find([
            'columns' => "staff_info_id,related_id, concat(staff_info_id,'_',related_id) as u_key",
            'conditions' => 'staff_info_id in ({ids:array}) and related_id in ({audit_ids:array}) and type = :category:',
            'bind' => [
                'ids' => $ids,
                'audit_ids' => $auditIds,
                'category' => StaffConsentAgreementLogModel::Type_SICK_MSG_SEND,
            ]
        ])->toArray();
        $exist = empty($exist) ? [] : array_column($exist, null, 'u_key');

        //看有没有发起撤销审批
        $approvalData = AuditApplyModel::find([
            'columns' => 'biz_value, is_cancel',
            'conditions' => 'biz_type = :type: and biz_value in ({ids:array}) and state = :state:',
            'bind' => ['type' => enums::$audit_type['LE'],'ids' => $auditIds, 'state' => enums::$audit_status['panding']],
        ])->toArray();
        $approvalData = empty($approvalData) ? [] : array_column($approvalData,'is_cancel', 'biz_value');

        $contentParam['send_type'] = self::$sendTypeAttendance;
        $contentParam['url_type']  = 'url';//url 需要点击跳转 text 文本
        $contentParam['date_at']   = date('Y-m-d');
        $extend['category'] = MessageEnums::MESSAGE_CATEGORY_CODE_SICK_CERTIFICATE;
        //主账号发送
        $title   = $this->getTranslation($param['lang'])->_('sick_leave_attendance_title');
        $content = 'sick-leave-materials-message?' . http_build_query($contentParam);
        $server  = new MessageServer($this->lang, $this->timeZone);
        $existModel = new StaffConsentAgreementLogModel();
        foreach ($data as $da){
            $auditId = $da['audit_id'];
            //如果存在 撤销申请 跳过
            if(!empty($approvalData[$auditId])){
                $this->logger->write_log("is_cancel ing.. {$auditId} {$staffId}", 'info');
                continue;
            }

            //如果已经超过7个工作日 就不发了 新增逻辑
            $leaveEnd = date('Y-m-d', strtotime($da['leave_end_time']));//病假结束日期
            $restPh   = $this->formatPhDays($allRest, $leaveEnd, $param['attendance_date']);//结束日期-打卡日期 有几个休息日ph
            $restNum  = count($restPh);
            $add      = $this->workDayLimit + $restNum;
            $lastDate = date('Y-m-d', strtotime("{$leaveEnd} +{$add} days"));//7 + 休息ph之后的第一个工作日
            if ($param['attendance_date'] > $lastDate) {//超过7+天了 不发消息
                $this->logger->write_log("over 7 days {$lastDate} ".json_encode($restPh), 'info');
                continue;
            }

            $masterKey = "{$staffId}_{$auditId}";
            if(empty($exist[$masterKey])){
                //发送主账号
                $server->sendMessage($staffId, $title, $content,$extend);
                //保存发送过记录
                $existRow['staff_info_id'] = $staffId;
                $existRow['type']          = StaffConsentAgreementLogModel::Type_SICK_MSG_SEND;
                $existRow['related_id']    = $auditId;
                $existModel->create($existRow);
            }
            //子账号
            if(!empty($supportInfo)){
                $subStaffId = $supportInfo['sub_staff_info_id'];
                if (empty($subStaffId)) {
                    //还没生成工号 不发
                    $this->logger->write_log("no_sub_staff_id {$staffId}", 'info');
                    continue;
                }
                $contentParam['url_type'] = 'text';//url 需要点击跳转 text 文本
                $subKey = "{$supportInfo['sub_staff_info_id']}_{$auditId}";
                if(!empty($exist[$subKey])){
                    $content = 'sick-leave-materials-message?' . http_build_query($contentParam);
                    $server->sendMessage($supportInfo['sub_staff_info_id'], $title, $content,$extend);
                }
            }
        }
        return true;
    }

    /**
     * @param $staffInfo 这个是登陆的 user info
     * 申请人  和审批人
     */
    public function sickPunchOut($staffInfo){
        $res = $this->checkPunchOutApply($staffInfo);
        if(empty($res)){
            $res = $this->checkPunchOutApproval($staffInfo);
        }

        return $res;
    }
    //申请人 拦截验证
    public function checkPunchOutApply($loginUserInfo){
        $limitDate = date('Y-m-d 00:00:00');
        //获取员工信息
        $staff_model = new StaffRepository($this->lang);
        $staff_info  = $staff_model->getStaffPosition($loginUserInfo['id']);
        $staffId     = $loginUserInfo['id'];
        if ($staff_info['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            $attendanceRe = new AttendanceRepository($this->lang, $this->timeZone);
            $supportInfo  = $attendanceRe->getSupportInfoBySubStaff($staff_info['staff_info_id']);
            if (empty($supportInfo)) {
                $this->logger->write_log("no_sub_staff_info {$staff_info['staff_info_id']}",'info');
                return true;
            }
            $staffId = $supportInfo['staff_info_id'];
        }
        $list = $this->getSickData($staffId, $limitDate);

        if (empty($list)) {
            return [];
        }
        /**
         * 还未上传材料（不存在待审批、已同意的材料），拦截下班打卡（包含子账号），提示：“病假所需材料未上传，请上传材料”，并跳转到材料补充列表页；
         * 如果已经超过可上传时间，提示“已经超过病假材料上传期限，请撤销病假” 这条逻辑不要了
         */
        $leaveServer = new LeaveServer($this->lang, $this->timeZone);
        //所有的ph 和休息日
        $dates   = array_column($list, 'leave_end_time');
        $start   = min($dates);
        $start   = date('Y-m-d', strtotime($start));//最早的病假结束日期
        $today   = date('Y-m-d');//今天日期
        $allRest = $leaveServer->staff_off_days($loginUserInfo['id'], $start, $today);
        //看请假结束日期 和今天比 是否超时 即超过病假后第一个工作日+6天的 23:59:59后
        $returnData = [];
        foreach ($list as $data){
            //看有几个工作日
            $leaveEnd = date('Y-m-d', strtotime($data['leave_end_time']));
            $restPh   = $this->formatPhDays($allRest, $leaveEnd, $today);
            $restNum  = count($restPh);
            $add      = $this->workDayLimit + $restNum;//病假结束日期 到今天 有几个休息ph
            $lastDate = date('Y-m-d', strtotime("{$leaveEnd} +{$add} days"));//第一个工作日
            if ($today <= $lastDate) {//还可以补充材料 跳转添加页面
                //有未处理续签合同审批
                $returnData['business_type']   = 'un_finished_study';
                $returnData['training_detail'] = [
                    'message' => $this->getTranslation()->_('sick_certificate_upload_notice'),// 提示消息内容
                    'url'     => env("h5_endpoint") . 'sick-leave-materials',
                ];
                break;
            }
//            //已经超过日期了 看是否在撤销审批中 新增需求 这逻辑不要了
//            $applyInfo = AuditApplyModel::findFirst([
//                'conditions' => "biz_type = :type: and biz_value = :id:",
//                'bind'       => ['type' => enums::$audit_type['LE'], 'id' => $data['audit_id']],
//            ]);
//            //已经撤销审批中
//            if (!empty($applyInfo) && $applyInfo->is_cancel > 0) {
//                continue;
//            }
//
//            //已经超时 提示撤销
//            $returnData['business_type']     = 'un_remittance';
//            $returnData['remittance_detail'] = [
//                'dialog_msg'         => $this->getTranslation()->_('sick_certificate_timeout_notice'),// 提示消息内容
//                'dialog_status'      => 1, //弹窗
//                'dialog_must_status' => 1, //是否跳过 1不能跳过
//                'is_ces_tra'         => 0, //是否ces培训 0否
//            ];
//            break;
        }
        return $returnData;
    }
    //审批人 拦截验证 待审批的病假
    public function checkPunchOutApproval($loginUserInfo){
        $auditType = enums::$audit_type['SICK_CERTIFICATE'];
        $builder   = $this->modelsManager->createBuilder();
        $builder->columns('audit.flow_id');
        $builder->from(['audit' => AuditApprovalModel::class]);
        $builder->andWhere('audit.biz_type = :audit_type:', ['audit_type' => $auditType]);
        $builder->andWhere('audit.approval_id = :staff_info_id:', ['staff_info_id' => $loginUserInfo['id']]);
        $builder->andWhere('audit.state = :state:', ['state' => enums::$audit_status['panding']]);
        $info = $builder->getQuery()->execute()->toArray();

        if(empty($info)){
            return [];
        }

        //日志
        $this->logger->write_log("打卡拦截 待审批病假 ".json_encode($info), 'info');

        $returnData['business_type']          = 'un_finished_study'; //'staff_kpi_to_be_confirmed';
        $returnData['training_detail']        = [
            'message'       => $this->getTranslation()->_('sick_certificate_approval'),//存在病假材料未审批
            'url'     => env('sign_url') . '/#/Approval',//跳转地址我的审批
        ];
        return $returnData;
    }


    //获取符合条件的病假记录 限制时间范围 6个月之内的请假数据
    public function getSickData($staffId, $limitDate){
        $beginDate = date('Y-m-d 00:00:00', strtotime("{$limitDate} -6 month"));
        $data = StaffAuditModel::find([
            'columns'    => 'audit_id,leave_start_time,leave_end_time,sub_status',
            'conditions' => 'audit_type = :audit_type: and staff_info_id = :staff_id: and leave_type = :leave_type: and status in ({status:array})
                            and sub_status = :sub_status: and leave_day >= :day_line: and leave_end_time >:begin_date: and leave_end_time < :limit_date:',
            'bind'       => [
                'audit_type' => StaffAuditModel::AUDIT_TYPE_LEAVE,
                'staff_id'   => $staffId,
                'leave_type' => enums::LEAVE_TYPE_38,
                'sub_status' => StaffAuditModel::SICK_SUB_STATUS_NO,
                'day_line'  => $this->dayLine,
                'begin_date' => $beginDate,
                'limit_date' => $limitDate,
                'status'     => [enums::$audit_status['panding'], enums::$audit_status['approved']],
            ],
        ])->toArray();
        return $data;
    }


    public function formatPhDays($dateList, $start, $end){
        $return = [];
        foreach ($dateList as $date){
            if($start <= $date && $date <= $end){
                $return[] = $date;
            }
        }
        return $return;
    }

    //图片表 重写
    protected function saveImgData()
    {
        if (empty($this->paramModel['image_path'])) {
            return;
        }

        $imgModel = new StaffAuditImageModel();
        foreach ($this->paramModel['image_path'] as $k => $v) {
            if(empty($v)){
                continue;
            }
            foreach ($v as $path){
                $insertImgData = [
                    'audit_id'   => $this->auditId,
                    'image_path' => $path,
                    'business_category' => $this->categoryEnum[$k] ?? self::SICK_OTHER,//病假图片分类
                ];
                $clone         = clone $imgModel;
                $clone->create($insertImgData);
            }
        }
    }

    //获取 员工待上传资料的病假数量
    public function getSickCertificateUploadNum($staffId){
        $count = StaffAuditModel::count([
            'conditions' => 'staff_info_id = :staff_id: and audit_type = :audit_type: and leave_type = :leave_type: and status in ({status:array}) and sub_status = :sub_status:',
            'bind' => [
                'staff_id' => $staffId,
                'audit_type' => StaffAuditModel::AUDIT_TYPE_LEAVE,
                'leave_type' => enums::LEAVE_TYPE_38,
                'status' => [enums::$audit_status['approved'], enums::$audit_status['panding']],
                'sub_status' => StaffAuditModel::SICK_SUB_STATUS_NO,
            ]
        ]);
        return $count;

    }
    //取未来 指定日期之后的第一个工作日
    public function getFirstWorkDay($leaveEnd){
        $step = $start = date('Y-m-d', strtotime("{$leaveEnd} +1 day"));
        $end = date('Y-m-d', strtotime("{$start} +1 month"));
        $leverServer = new LeaveServer($this->lang, $this->timeZone);
        $holidays = $leverServer->staff_off_days($this->staffInfo['staff_info_id'], $start, $end);
        if(empty($holidays)){
            return $start;
        }
        $date = '';
        while ($step <= $end){
            if(!in_array($step, $holidays)){
                $date = $step;
                break;
            }
            $step = date('Y-m-d', strtotime("{$step} +1 day"));
        }
        return $date;

    }


}