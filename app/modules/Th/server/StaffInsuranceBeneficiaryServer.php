<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use app\enums\InsuranceBeneficiaryEnums;
use FlashExpress\bi\App\Enums\CeoMailEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\WorkingCountryEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoExtendModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInsuranceBeneficiaryBaseModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInsuranceBeneficiaryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Models\backyard\SysDistrictModel;
use FlashExpress\bi\App\Server\formPdfServer;
use FlashExpress\bi\App\Server\StaffInsuranceBeneficiaryServer as BaseCancelContractServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysServer;
use FlashExpress\bi\App\Server\ToolServer;
use Exception;

class StaffInsuranceBeneficiaryServer extends BaseCancelContractServer
{
    /**
     * 验证用户是否有菜单入口
     * @param $staffInfoId
     * @return bool
     */
    public function verifyStaffInsuranceBeneficiaryCollectMenu($staffInfoId): bool
    {
        //关闭入口-等待产品通知打卡
//        return false;

        //获取用户信息
        $staffInfo = (new HrStaffInfoModel)->getOneByStaffId($staffInfoId);

        if (empty($staffInfo)) {
            return false;
        }

        // 【工作所在国家是泰国】或【工作所在国家是老挝并且国籍是泰国】
        if (
            !(
                ($staffInfo['working_country'] == HrStaffInfoModel::WORKING_COUNTRY_TH)
                ||
                ($staffInfo['working_country'] == HrStaffInfoModel::WORKING_COUNTRY_LA && $staffInfo['nationality'] == HrStaffInfoModel::NATIONALITY_TH)
            )
        ) {
            return false;
        }

        // 职级：F0<=职级<=F15
        if (!($staffInfo['job_title_grade_v2'] >= 0 && $staffInfo['job_title_grade_v2'] <= 15)) {
            return false;
        }

        // 不要子账号
        if ($staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            return false;
        }

        // 正式员工
        if ($staffInfo['formal'] != HrStaffInfoModel::FORMAL_1) {
            return false;
        }

        //雇佣类型为正式员工、日薪制特殊合同工、月薪制特殊合同工
        if (!in_array($staffInfo['hire_type'],
            [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
                HrStaffInfoModel::HIRE_TYPE_3
            ]
        )) {
            return false;
        }

        //在职状态为在职/待离职/停职
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_2) {
            return false;
        }

        //入职时间大于等于7天(包含入职当天)
        if (strtotime($staffInfo['hire_date']) > strtotime('-6 days')) {
            return false;
        }

        return true;
    }


    /**
     * 获取当前使用的保单
     * @param $staffInfoId
     * @return array
     */
    public function getInsuranceBeneficiaryData($baseId): array
    {
        $data = HrStaffInsuranceBeneficiaryModel::find([
            'conditions' => 'base_id = :base_id:',
            'bind'       => [
                'base_id' => $baseId,
            ],
            'order'      => ' id ASC',
        ]);

        return $data ? $data->toArray() : [];
    }

    /**
     * 获取当前使用的保单
     * @param $staffInfoId
     * @return array
     */
    public function getInsuranceBeneficiaryBaseDetail($staffInfoId): array
    {
        $data = HrStaffInsuranceBeneficiaryBaseModel::findFirst([
            'conditions' => ' staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staffInfoId,
            ],
            'order'      => ' id DESC',
        ]);

        return $data ? $data->toArray() : [];
    }

    /**
     * 检查用户是否提交
     * @param $staffInfoId
     * @return bool
     */
    public function checkStaffSubmit($staffInfoId): bool
    {
        if ($this->getInsuranceBeneficiaryBaseDetail($staffInfoId)) {
            return true;
        }

        return false;
    }


    /**
     * 保存/预览保险受益人保单
     * @param $params
     * @return array|boolean
     * @throws ValidationException
     */
    public function saveInsuranceBeneficiary($params)
    {
        $staffInfoId = $params['staff_info_id'];

        if (!$staffInfoId) {
            throw new ValidationException($this->t->_('1001'));
        }

        //生成pdf
        if ($params['is_submit'] == InsuranceBeneficiaryEnums::INSURANCE_BENEFICIARY_UNSUBMITTED) {
            $pdfPath = $this->generateInsuranceBeneficiaryPdf($params);

            if (!$pdfPath) {
                // 保险单生成失败，请重新提交
                throw new ValidationException($this->t->_('beneficiary_generate_pdf_error'));
            }

            return ['annex_path' => $pdfPath];
        }

        //真实提交
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            //插入主表
            $hrStaffInsuranceBeneficiaryBaseModel = new HrStaffInsuranceBeneficiaryBaseModel();

            $hrStaffInsuranceBeneficiaryBaseModel->staff_info_id  = $staffInfoId;
            $hrStaffInsuranceBeneficiaryBaseModel->marital        = $params['marital'];
            $hrStaffInsuranceBeneficiaryBaseModel->staff_sign_img = $params['staff_sign_img'];
            $hrStaffInsuranceBeneficiaryBaseModel->annex_path     = $params['annex_path'];

            if (!$hrStaffInsuranceBeneficiaryBaseModel->save()) {
                throw new BusinessException('BaseModel Save Error');
            }

            //插入分表
            $beneficiaryData = [];
            foreach ($params['beneficiary'] as $beneficiary) {
                $beneficiaryData[] = [
                    'base_id'                => $hrStaffInsuranceBeneficiaryBaseModel->id,
                    'staff_info_id'          => $staffInfoId,
                    'relation'               => $beneficiary['relation'],
                    'beneficiary_name'       => $beneficiary['beneficiary_name'],
                    'beneficiary_identity'   => $beneficiary['beneficiary_identity'],
                    'beneficiary_mobile'     => $beneficiary['beneficiary_mobile'],
                    'beneficiary_proportion' => $beneficiary['beneficiary_proportion'],
                ];
            }

            if (!empty($beneficiaryData)) {
                (new HrStaffInsuranceBeneficiaryModel())->batch_insert($beneficiaryData, 'db');
            }

            //修改员工表
            $hrStaffInfoExtendModel = HrStaffInfoExtendModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $staffInfoId,
                ],
            ]);

            if (empty($hrStaffInfoExtendModel)) {
                $hrStaffInfoExtendModel = new HrStaffInfoExtendModel();
                $hrStaffInfoExtendModel->staff_info_id = $staffInfoId;
            }

            $hrStaffInfoExtendModel->insurance_beneficiary_status = HrStaffInfoExtendModel::INSURANCE_BENEFICIARY_STATUS_SUCCESS;

            if (!$hrStaffInfoExtendModel->save()) {
                throw new BusinessException('StaffInfoExtend Save Error！');
            }

            if (!$db->commit()) {
                throw new BusinessException('Commit Save Error！');
            }

            return ['annex_path' => $params['annex_path']];
        } catch (Exception $e) {
            $db->rollback();
            $this->logger->write_log('保险受益人提交失败 '. $e->getFile() . " E_Line:" . $e->getLine() . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 生成保险受益人保单pdf
     * @param $params
     * @return string|boolean
     */
    public function generateInsuranceBeneficiaryPdf($params)
    {
        try {
            $tempFile = BASE_PATH . '/public/pdf_template/staff_insurance_beneficiary.ftl';
            $pdfTempUrl = (new ToolServer($this->lang, $this->timezone))->getPdfTemp($tempFile);

            $pdfData = $this->getInsuranceBeneficiaryPdfData($params);

            $pdfHeaderFooterSetting = [
                'displayHeaderFooter' => false,
                'headerTemplate'      => '',
                'footerTemplate'      => '',
            ];

            $imgData                  = [['name' => 'staff_sign_img', 'url' => $params['staff_sign_img'] ?? '']];

            $pdfFileData = (new formPdfServer())->generatePdf($pdfTempUrl, $pdfData, $imgData, '', $pdfHeaderFooterSetting);

            //生产pdf失败
            if (empty($pdfFileData['object_url'])) {
                throw new Exception('object_url Error!');
            }

            return $pdfFileData['object_url'];
        } catch (Exception $e) {
            $this->logger->write_log("generateInsuranceBeneficiaryPdf Error: " . $e->getFile() . " E_Line:" . $e->getLine() . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString());
        }

        return false;
    }


    /**
     * 获取员工基础数据
     * @param $staffInfoId
     * @return array
     */
    public function getInsuranceBeneficiaryStaffInfo($staffInfoId): array
    {
        $staffInfo = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId);

        //员工基本信息
        $staffItems = HrStaffItemsModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staffInfoId,
            ],
        ])->toArray();

        $staffItems = array_column($staffItems, 'value','item');
        $countryValue = WorkingCountryEnums::getWorkingCountry();

        $address['register_country']     = $staffItems['REGISTER_COUNTRY'] ?? '';
        $address['register_province']    = $staffItems['REGISTER_PROVINCE'] ?? '';
        $address['register_city']        = $staffItems['REGISTER_CITY'] ?? '';
        $address['register_district']    = $staffItems['REGISTER_DISTRICT'] ?? '';
        $address['register_postcodes']   = $staffItems['REGISTER_POSTCODES'] ?? '';
        $address['register_house_num']   = $staffItems['REGISTER_HOUSE_NUM'] ?? '';
        $address['register_village_num'] = $staffItems['REGISTER_VILLAGE_NUM'] ?? '';
        $address['register_village']     = $staffItems['REGISTER_VILLAGE'] ?? '';
        $address['register_alley']       = $staffItems['REGISTER_ALLEY'] ?? '';
        $address['register_street']      = $staffItems['REGISTER_STREET'] ?? '';

        $registerAddress = '';

        if (!empty($address['register_house_num'])) {
            $registerAddress .= $address['register_house_num']. ' ';
        }

        if (!empty($address['register_village_num'])) {
            $registerAddress .= $address['register_village_num']. ' ';
        }

        if (!empty($address['register_village'])) {
            $registerAddress .= $address['register_village']. ' ';
        }

        if (!empty($address['register_alley'])) {
            $registerAddress .= $address['register_alley']. ' ';
        }

        if (!empty($address['register_street'])) {
            $registerAddress .= $address['register_street']. ' ';
        }

        if ($address['register_country'] == $countryValue) {
            if (!empty($address['register_district'])) {
                $districtInfo = SysDistrictModel::findFirst([
                    'columns'    => 'code,name,en_name',
                    'conditions' => 'code = :code:',
                    'bind'       => ['code' => $address['register_district']],
                ]);

                $registerAddress .= $districtInfo->name ?? '';
                $registerAddress .= ' ';
            }

            if (!empty($address['register_city'])) {
                $cityInfo = SysCityModel::findFirst([
                    'columns'    => 'code,name,en_name',
                    'conditions' => 'code = :code:',
                    'bind'       => ['code' => $address['register_city']],
                ]);

                $registerAddress .= $cityInfo->name ?? '';
                $registerAddress .= ' ';
            }

            $sysServer = new SysServer();
            $province  = array_column($sysServer->getSysProvinceList(), 'name', 'code');

            if (!empty($address['register_province'])) {
                $registerAddress .= $province[$address['register_province']] ?? '';
                $registerAddress .= ' ';
            }
        } else {
            if (!empty($address['register_district'])) {
                $registerAddress .= $address['register_district'] . ' ';
            }

            if (!empty($address['register_city'])) {
                $registerAddress .= $address['register_city'] . ' ';
            }

            if (!empty($address['register_province'])) {
                $registerAddress .= $address['register_province'] . ' ';
            }
        }

        if (!empty($address['register_postcodes'])) {
            $registerAddress .= $address['register_postcodes'];
        }

        $sexText = '';
        if ($staffInfo['sex'] == HrStaffInfoModel::SEX_MALE) {
            $sexText = $this->getTranslation()->_('4900');
        } elseif ($staffInfo['sex'] == HrStaffInfoModel::SEX_FEMALE) {
            $sexText = $this->getTranslation()->_('4901');
        }

        return [
            'staff_name'       => $staffInfo['name'] ?? '',
            'staff_info_id'    => $staffInfo['staff_info_id'] ?? '',
            'identity'         => $staffInfo['identity'] ?? '',
            'birthday'         => $staffItems['BIRTHDAY'] ?? '',
            'mobile'           => $staffInfo['mobile'] ?? '',
            'sex'              => $staffInfo['sex'] ?? '',
            'sex_text'         => $sexText,
            'register_address' => $registerAddress,
        ];
    }


    /**
     * 获取组装PDF表单项
     * @param $params
     * @return array
     */
    public function getInsuranceBeneficiaryPdfData($params): array
    {
        $userInfo                = $this->getInsuranceBeneficiaryStaffInfo($params['staff_info_id']);
        $beneficiary             = $params['beneficiary'] ?? [];

        $beneficiaryData = [];
        foreach ($beneficiary as $value) {
            if (!empty($value)) {
                $beneficiaryData[] = [
                    'relation_name'          => $this->t->_(HrStaffInsuranceBeneficiaryModel::$relation_list[$value['relation']]),
                    'beneficiary_name'       => $value['beneficiary_name'] ?? '',
                    'beneficiary_proportion' => !empty($value['beneficiary_proportion']) ? strval(intval($value['beneficiary_proportion'])) : '',
                ];
            }
        }

        $userInfo['marital']     = $params['marital'] ?? 0;
        $userInfo['beneficiary'] = $beneficiaryData;
        $userInfo['sign_date']   = date('Y-m-d');

        return $userInfo;
    }

    /**
     * 获取受益人详细信息
     * @param $params
     * @return array
     */
    public function getInsuranceBeneficiaryDetail($params): array
    {
        $staffInfoId = $params['staff_info_id'] ?? 0;

        $data = $this->getInsuranceBeneficiaryStaffInfo($staffInfoId);

        $baseData = $this->getInsuranceBeneficiaryBaseDetail($staffInfoId);

        $beneficiary = [];
        if (!empty($baseData)) {
            $beneficiaryData = $this->getInsuranceBeneficiaryData($baseData['id'] ?? 0);

            foreach ($beneficiaryData as $v) {
                if (!empty($v)) {
                    $beneficiary[] = [
                        'relation'               => $v['relation'],
                        'beneficiary_name'       => $v['beneficiary_name'],
                        'beneficiary_proportion' => $v['beneficiary_proportion'],
                    ];
                }
            }
        }

        $data['marital']       = $baseData['marital'] ?? '';
        $data['annex_path']    = $baseData['annex_path'] ?? '';
        $data['beneficiary']   = $beneficiary;
        $data['flash_box_url'] = env('h5_endpoint') . CeoMailEnums::FLASH_BOX_UPDATE_INFO_CATEGORY;

        return $data;
    }

    /**
     * 枚举接口
     * @return array|array[]
     */
    public function sysInfo(): array
    {
        $relationList = $maritalList = [];
        foreach (HrStaffInsuranceBeneficiaryModel::$relation_list as $key => $value) {
            $relationList[] = ['key' => (string)$key, 'value' => $this->t->_($value)];
        }

        foreach (HrStaffInsuranceBeneficiaryModel::$marita_list as $key => $value) {
            $maritalList[] = ['key' => (string)$key, 'value' => $this->t->_($value)];
        }

        return [
            'relation_list' => $relationList,
            'marital_list'  => $maritalList,
        ];
    }

    /**
     * 获取未填写保单的员工
     * @param $hireDate
     * @return array
     */
    public function getInsuranceBeneficiaryStaffList($hireDate, $staffInfoId = ''): array
    {
        if (!$hireDate) {
            $hireDate = date('Y-m-d',strtotime("-6 days"));
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_info.staff_info_id');
        $builder->from(['staff_info' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrStaffInsuranceBeneficiaryBaseModel::class, 'staff_info.staff_info_id = beneficiary_base.staff_info_id', 'beneficiary_base');

        //员工表
        //【工作所在国家是泰国】或【工作所在国家是老挝并且国籍是泰国】
        $builder->andWhere(
            'staff_info.working_country = :working_country_th: OR (staff_info.working_country = :working_country_la: AND nationality = :nationality:)',
            [
                'working_country_th' => HrStaffInfoModel::WORKING_COUNTRY_TH,
                'working_country_la' => HrStaffInfoModel::WORKING_COUNTRY_LA,
                'nationality'        => HrStaffInfoModel::NATIONALITY_TH,
            ]
        );

        //F0<=职级<=F15
        $builder->andWhere(
            'staff_info.job_title_grade_v2 >= :job_title_grade_start: AND staff_info.job_title_grade_v2 <= :job_title_grade_end:',
            ['job_title_grade_start' => 0 ,'job_title_grade_end' => 15]
        );

        //雇佣类型为正式员工日薪制特殊合同工、月薪制特殊合同工，
        $builder->andWhere('staff_info.hire_type IN ({hire_type:array})', [
            'hire_type' => [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
                HrStaffInfoModel::HIRE_TYPE_3,
            ],
        ]);

        //不要子账号
        $builder->andWhere('staff_info.is_sub_staff = :is_sub_staff:',
            ['is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0]
        );

        //在编
        $builder->andWhere('staff_info.formal = :formal:', ['formal' => HrStaffInfoModel::FORMAL_1]);

        //在职状态为在职/待离职/停职
        $builder->andWhere('staff_info.state != :state: ', ['state' => HrStaffInfoModel::STATE_2]);

        //员工入职后第7天
        $builder->andWhere('staff_info.hire_date <= :hire_date:', ['hire_date' => $hireDate]);

        //未填写过
        $builder->andWhere('beneficiary_base.id IS NULL');

        if ($staffInfoId) {
            $builder->andWhere('staff_info.staff_info_id = :staff_info_id:', ['staff_info_id' => $staffInfoId]);
        }

        $builder->orderby('staff_info.staff_info_id');

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 发送保险受益人保单提醒消息
     * @param $data
     * @return true
     */
    public function sendRemindMessage($data): bool
    {
        $list         = array_chunk($data, 100);
        $staffAccount = new StaffServer();
        $detailUrl    = env('h5_endpoint') . "insurance-beneficiary-message";
        $date         = date('Ymd');
        $redis        = $this->getDI()->get('redis');

        //发送消息
        foreach ($list as $item) {
            $staffIds = array_values(array_unique(array_column($item, 'staff_info_id')));

            //获取语言包
            $staffLang = $staffAccount->getBatchStaffLanguage($staffIds);

            foreach ($item as $value) {
                $staffInfoId = $value['staff_info_id'] ?? 0;

                $key = "insurance-beneficiary-message-" . $date . '-' . $staffInfoId;

                if ($redis->exists($key)) {
                    $this->getDI()->get('logger')->write_log('保险受益人消息发送重复 ' . $staffInfoId, 'info');
                    continue;
                }

                $lang         = $staffLang[$value['staff_info_id']] ?? getCountryDefaultLang();
                $messageTitle = $this->getTranslationByLang($lang)->_('insurance_beneficiary_message_th_title');

                $param['staff_info_ids_str'] = $value['staff_info_id'];
                $param['staff_users']        = [$value['staff_info_id']];
                $param['message_title']      = $messageTitle;
                $param['message_content']    = "<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' /><div style='postion:fixed;left:0;top:0;width:100%;height:100%'><iframe src='{$detailUrl}' width='100%' height='100%' scrolling='no' frameborder='0'></iframe></div>";
                $param['category']           = MessageEnums::MESSAGE_CATEGORY_SYS;
                $param['id']                 = time() . $staffInfoId . rand(1000000, 9999999);

                $hcm_rpc = new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang);
                $hcm_rpc->setParams($param);
                $res = $hcm_rpc->execute();

                if (!isset($res['result']['code']) || $res['result']['code'] != ErrCode::SUCCESS) {
                    $this->getDI()->get('logger')->write_log('保险受益人消息发送失败 ' . json_encode($param) . ' ' . json_encode($res));
                }

                $redis->save($key, 1, 3600 * 12);

                $this->getDI()->get('logger')->write_log('保险受益人消息发送成功 ' . $staffInfoId . ' ' . $key, 'info');
            }
        }

        return true;
    }
}