<?php
namespace FlashExpress\bi\App\Modules\Th\Server;

use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrEconomyAbilityModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\VanContainerModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\VehicleRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\VanContainerServer;
use FlashExpress\bi\App\Server\VehicleInfoAuditServer;
use FlashExpress\bi\App\Server\VehicleServer as GlobalVehicleServer;

class VehicleServer extends GlobalVehicleServer
{

    public $isVan = false;
    const OTHER = 100;
    const NETWORK_TAG = 1;//NETWORK部门
    const SHOP_TAG = 2;// SHOP 部门
    /**
     * @var PublicRepository
     */
    private $public;
    /**
     * @var AuditListServer
     */
    private $auditlist;
    /**
     * @var OvertimeRepository
     */
    private $ov;
    /**
     * @var VehicleRepository
     */
    private $vehicle;

    public function __construct($lang, $timezone='+08:00')
    {
        parent::__construct($lang,$timezone);
        $this->vehicle = new VehicleRepository($lang, $timezone);
    }


    public function getVehicleBrandMap() :array
    {
        $returnData = UC('vehicleInfo');
        foreach ($returnData['vehicle_brand'] as $key => $val) {
            if ($val['value'] == 8) {
                $returnData['vehicle_brand'][$key]['label'] = $this->getTranslation()->_('2016');
            }
        }
        return array_column($returnData['vehicle_brand'], 'label', 'value');
    }




    /**
     * 枚举类型(转换为前端口需要的方式)
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function enumVehicleS($paramIn = [])
    {
        $returnData = UC('vehicleInfo');
        //只有油的类型需要翻译
        $ot = $this->vehicle->getOilType();
        $t = $this->getTranslation();
        $oilType = [];
        foreach ($ot as $key => $item) {
            $oilType[]=['value' => intval($key), 'label' => $item];
        }

        $returnData['oil_type'] = $oilType;
        foreach($returnData['vehicle_brand'] as $key => $val){
            if($val['value'] == 8) {
                $returnData['vehicle_brand'][$key]['label'] = $t->_('2016');
            }
            foreach ($val['data'] as $key1 => $val1) {
                if($val1['value'] == VehicleServer::OTHER){
                    $val1['label'] = $t->_('2016');
                    $returnData['vehicle_brand'][$key]['data'][$key1] = $val1;
                }
            }
        }

        // 车辆来源
        foreach (VehicleInfoEnums::VEHICLE_SOURCE_ITEM as $vs_k => $vs_v) {
            if ($paramIn['job_title'] == VehicleInfoEnums::JOB_PICKUP_DRIVER && $vs_k != VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
                continue;
            }
            $returnData['vehicle_source_item'][] = [
                'value' => $vs_k,
                'label' => $t->_($vs_v),
            ];
        }

        // 驾照类型
        $driver_license_item = [];
        foreach (VehicleInfoEnums::DRIVER_LICENSE_TYPE_ITEM as $l_k => $l_v) {
            if (is_array($l_v) && !empty($l_v)) {
                $driver_license_item[$l_k] = [
                    'value' => $l_k,
                    'label' => $t->_(VehicleInfoEnums::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.$l_k),
                ];

                foreach ($l_v as $sub_k => $sub_v) {
                    $driver_license_item[$l_k]['data'][] = [
                        'value' => $sub_k,
                        'label' => $t->_($sub_v),
                    ];
                }
            } else {
                $driver_license_item[$l_k] = [
                    'value' => $l_k,
                    'label' => $t->_($l_v),
                ];
            }
        }
        $returnData['vehicle_size'] = [];
        // 车型
        foreach (self::getVehicleSize(false)  as $k => $v) {
            $returnData['vehicle_size'][] = [
                'value' => strval($k),
                'label' => $v,
            ];
        }

        $returnData['driver_license_item'] = array_values($driver_license_item);

        //油卡企业
        foreach(VehicleInfoEnums::OIL_COMPANY_ITEM as $key=>$val){
            $returnData['oil_company_item'][] = [
                'value' => $key,
                'label' => $val,
            ];
        }

        //车厢类型 van 特有
        foreach (VanContainerModel::$typeList as $key => $val) {
            $returnData['container_type'][] = [
                'value' => strval($key),
                'label' => $t->_($val),
            ];
        }

        //上牌地点可选项
        $license_plate_location = $this->getLicensePlateLocationFromCache();
        $returnData['license_plate_location'] = [];
        //车厢类型 van 特有
        foreach ($license_plate_location as $val) {
            $returnData['license_plate_location'][] = [
                'value' => $val,
                'label' => $val,
            ];
        }


        $resData['vehicle_enum'] = $returnData;
        return $resData;
    }

    /**
     * 创建车辆信息 userinfo 里只有 id 和 job_title 如果要用别的 记得在外层新增参数
     * @Access  public
     * @Return  array
     * @throws ValidationException
     */
    public function addVehicleInfoS($paramIn , $userinfo,$operate_staff_id = '')
    {
        $returnData['data'] = [];

        // 整合入表字段
        $vehicleData = $this->filterVehicleData($paramIn, $userinfo);

        //查询验证是否有数据
        $vehicleInfo = $this->vehicle->getVehicleInfoR($vehicleData['uid']);

        //上牌地点可选项
        $license_plate_location = $this->getLicensePlateLocationFromCache();
        if(!empty($vehicleData['license_location']) && !in_array($vehicleData['license_location'], $license_plate_location)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_license_location_select'));
        }

        // 验证车牌号+上牌地点 是否 与 其他在职人的车牌号重复(如果职位是Pickup Driver[1844]，则允许与其他员工重复)
        if ($userinfo['job_title'] != VehicleInfoEnums::JOB_PICKUP_DRIVER && $this->checkPlateNumberIsExist($vehicleData['plate_number'], $userinfo['id'], $vehicleData['license_location'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0001'));
        }

        // 验证发动机号 是否 与 其他在职人的发动机号重复
        if (!empty($vehicleData['engine_number']) && $this->checkEngineNoIsExist($vehicleData['engine_number'], $userinfo['id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0003'));
        }

        // 验证开始用车日期, 不得早于入职日期
        $staffInfo = (new StaffServer())->getStaffInfo(['staff_info_id' => $userinfo['id']]);
        $hire_date = substr($staffInfo['hire_date'], 0, 10);
        if ($vehicleData['vehicle_source'] == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE && !empty($vehicleData['vehicle_start_date']) && $vehicleData['vehicle_start_date'] < $hire_date) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0007'));
        }

        if(isset($paramIn['edit_src']) && $paramIn['edit_src'] == 'mileage'){
            if (!empty($vehicleInfo) && $paramIn['plate_number'] == $vehicleInfo['plate_number'] && $paramIn['license_location'] == $vehicleInfo['license_location']) {
                return $this->checkReturn(-3, $this->getTranslation()->_('21334_vehicle_info_error_02'));
            }
        }
        
        $isChangeCar = empty($vehicleInfo) || ($paramIn['plate_number'] != $vehicleInfo['plate_number'] || $paramIn['license_location'] != $vehicleInfo['license_location']);

        $isOilFeiJobTitle = in_array($userinfo['job_title'],$this->getMileageJobTitleFromCache());

        //非初次填报 && 换车
        if (isset($paramIn['is_new_view'])
            && !$this->checkStaffSelfSubmit($userinfo['id'])
            && $isOilFeiJobTitle
            && $isChangeCar) {
            if (empty($vehicleData['vehicle_check_img_1']) || empty($vehicleData['vehicle_check_img_2'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('21334_vehicle_info_error_03'));
            }
            if (empty($vehicleData['vehicle_check_video'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('21334_vehicle_info_error_01'));
            }
            if ((new VanContainerServer($this->lang, $this->timeZone))->isShowVanContainer($staffInfo) && empty($paramIn['container_type'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('need_container_type'));
            }

        }

        if($this->isVan) {

            //油卡企业、油卡号、油卡图片要么都为空，要么都不为空
            if (!empty($vehicleData['oil_number']) && (empty($vehicleData['oil_company']) || empty($vehicleData['oil_img']))) {
                return $this->checkReturn(-3, $this->getTranslation()->_('oil_info_error'));

            }
            if (!empty($vehicleData['oil_company']) && (empty($vehicleData['oil_number']) || empty($vehicleData['oil_img']))) {
                return $this->checkReturn(-3, $this->getTranslation()->_('oil_info_error'));

            }
            if (!empty($vehicleData['oil_img']) && (empty($vehicleData['oil_number']) || empty($vehicleData['oil_company']))) {
                return $this->checkReturn(-3, $this->getTranslation()->_('oil_info_error'));
            }

            //校验油卡卡号是否已经存在
            if (!empty($vehicleData['oil_number']) && $exist_staff_id = $this->checkOilNoIsExist($vehicleData['oil_number'], $userinfo['id'])) {
                return $this->checkReturn(-3, str_replace("XXX", $exist_staff_id, $this->getTranslation()['oil_number_008']));
            }
            // 油卡号是否变更，若变更，则需校验旧油卡是否未充值
            if (!empty($vehicleInfo) &&  !empty($vehicleData['oil_number']) && !empty($vehicleInfo['oil_number'])) {
                $oil_checkout = $this->getCheckOilNumberByIsIntoMoney($vehicleInfo['oil_number'], $userinfo['id']);
                if (!empty($oil_checkout) && ($oil_checkout['oil_number'] != $vehicleData['oil_number'])) {
                    return $this->checkReturn(-3, $this->getTranslation()['fuel_manage_oil_number_no_checkout']);
                }
            }
        }
        if ($isChangeCar) {
            $vehicleData['last_change_car_date'] =  date('Y-m-d H:i:s');
        }
        $vehicle_edit_not_supervisor_audit_day = (new SettingEnvServer())->getSetVal('vehicle_edit_not_supervisor_audit_day') ?: '23';
        if (RUNTIME == 'pro') {
            $vehicle_edit_not_supervisor_audit_day = '23';
        }

        $isQuickAudit = strtotime(date('Y-m-d')) == strtotime(date('Y-m-' . $vehicle_edit_not_supervisor_audit_day));
        $db = $this->getDI()->get("db");
        //开启事务
        $db->begin();
        try {
            if (!$isOilFeiJobTitle) {
                $vehicleData['approval_status'] = VehicleInfoEnums::APPROVAL_WAIT_NW_CODE;
            }

            //特定职位需要上级主管审批
            $audit_id = 0;
            if($isOilFeiJobTitle && ($isChangeCar || $this->checkStaffSelfSubmit($userinfo['id']))){
                $vehicleData['approval_status'] = $isQuickAudit ? VehicleInfoEnums::APPROVAL_WAIT_NW_CODE : VehicleInfoEnums::APPROVAL_PENDING_CODE;
                $before['plate_number']         = $vehicleInfo['plate_number'] ?? '';
                $before['license_location']     = $vehicleInfo['license_location'] ?? '';
                $before['vehicle_brand']        = $vehicleInfo['vehicle_brand'] ?? '';
                $before['vehicle_brand_text']   = $vehicleInfo['vehicle_brand_text'] ?? '';
                $before['container_type']       = $paramIn['container_type']??0;
                //发起审批
                $audit_id = (new VehicleInfoAuditServer($this->lang, $this->timeZone))->add($userinfo['id'],$before,$isQuickAudit);
            }
            $vehicleData['vehicle_audit_id'] = $audit_id;
            if (empty($vehicleInfo)) {
                $vehicleData['creator_id'] = $userinfo['id'];
                $vehicleData['create_channel'] = VehicleInfoEnums::VEHICLE_ADD_CHANNEL_KIT;
                $vehicleData['formal_data'] = '';
                $res = $this->vehicle->addVehicleInfoR($vehicleData);
            } else {
                $res = $this->vehicle->updateVehicleInfo($vehicleData);
            }

            if (!$res) {
                throw new \Exception($this->getTranslation()->_('4101'));
            }
            // 车辆信息日志
            $vehicleLogData = [];
            $vehicleLogData['staff_id'] = $vehicleData['uid'];
            $vehicleLogData['operate_staff_id'] = $operate_staff_id ?? $vehicleData['uid'];
            $vehicleLogData['text'] = json_encode($vehicleData, JSON_UNESCAPED_UNICODE);
            $this->vehicle->addVehicleInfoLog($vehicleLogData);


            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }

        $this->createDrivingLicenseIdentifyRequest($vehicleLogData);

        if ($isOilFeiJobTitle && $isChangeCar) {
            //通知bi换车了
            $this->syncChangeCarInfo($vehicleData);
        }

        $returnData['data'] = $vehicleData['uid'];
        return $this->checkReturn($returnData);
    }



    /**
     * 同步换车信息
     * @param $vehicleData
     * @return array|bool|mixed|null
     */
    protected function syncChangeCarInfo($vehicleData)
    {
        $param = [
            'plate_number'     => $vehicleData['plate_number'],
            'staff_info_id'    => $vehicleData['uid'],
            'change_date'      => date('Y-m-d'),
            'license_location' => $vehicleData['license_location'],
        ];
        $ac    = new ApiClient('ard_api', '', 'mileage.vehicle_change_log', $this->lang);
        $ac->setParams($param);
        return $ac->execute();
    }


    /**
     * 提取不同职位需入库的字段
     * @param array $vehicle_data
     * @param array $user_info
     * @return array $data
     */
    protected function filterVehicleData(array $vehicle_data, array $user_info)
    {
        // 公共字段
        $data = [
            'uid'                            => $user_info['id'],
            'vehicle_source'                 => $vehicle_data['vehicle_source'],
            'plate_number'                   => $vehicle_data['plate_number'],
            'license_location'               => $vehicle_data['license_location'],
            'registration_certificate_img'   => $vehicle_data['registration_certificate_img'],
            'vehicle_img'                    => $vehicle_data['vehicle_img'],
            'insurance_policy_number'        => $vehicle_data['insurance_policy_number'],
            'insurance_start_date'           => $vehicle_data['insurance_start_date']?:null,
            'insurance_end_date'             => $vehicle_data['insurance_end_date']?:null,
            'vehicle_tax_expiration_date'    => $vehicle_data['vehicle_tax_expiration_date']?:null,
            'vehicle_tax_certificate_img'    => $vehicle_data['vehicle_tax_certificate_img'],
            'driver_license_type'            => $vehicle_data['driver_license_type'],
            'driver_license_type_other_text' => $vehicle_data['driver_license_type'] != 100 ? '' : $vehicle_data['driver_license_type_other_text'] ?? '',
            'driver_license_number'          => $vehicle_data['driver_license_number'],
            'driver_license_start_date'      => $vehicle_data['driver_license_start_date'],
            'driver_license_end_date'        => $vehicle_data['driver_license_end_date'],
            'driving_licence_img'            => implode("\n", $vehicle_data['driving_licence_img_item']),
            'vehicle_type'                   => VehicleInfoEnums::VEHICLE_TYPE_BIKE_CODE,
            'engine_number'                  => $vehicle_data['engine_number'],
            'vehicle_check_img_1'            => $vehicle_data['vehicle_check_img_1'] ?? '',
            'vehicle_check_img_2'            => $vehicle_data['vehicle_check_img_2'] ?? '',
            'vehicle_check_video'            => $vehicle_data['vehicle_check_video'] ?? '',
            'approval_staff_id'              => '',
            'approval_time'                  => null,
            'approval_remark'                => '',
            'editor_id'                      => $user_info['id'],
        ];

        // 用车开始日期
        if ($vehicle_data['vehicle_source'] == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
            $data['vehicle_start_date'] = $vehicle_data['vehicle_start_date'];
        }

        // van 特有字段
        if (in_array($user_info['job_title'], VehicleInfoEnums::VAN_JOB_GROUP_ITEM)) {
            $this->isVan                = true;
            $data['vehicle_brand']      = $vehicle_data['vehicle_brand'];
            $data['vehicle_brand_text'] = $vehicle_data['vehicle_brand'] != 8 ? '' : $vehicle_data['vehicle_brand_text'] ?? '';
            $data['vehicle_model']      = $vehicle_data['vehicle_model'];
            $data['vehicle_model_text'] = $vehicle_data['vehicle_model'] != 100 ? '' : $vehicle_data['vehicle_model_text'] ?? '';
            $data['vehicle_size']       = $vehicle_data['vehicle_size'];
            $data['buy_date']           = $vehicle_data['buy_date']?:null;
            //电车 不需要油卡相关信息
            if ($user_info['job_title'] != VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID) {
                $data['oil_type']    = $vehicle_data['oil_type'];
                $data['oil_number']  = $vehicle_data['oil_number'] ?? '';
                $data['oil_company'] = $vehicle_data['oil_company'] ?? 0;
                $data['oil_img']     = $vehicle_data['oil_img'] ?? '';
            }
            $data['vehicle_type'] = VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE;
        }

        return $data;
    }


    /**
     * 格式化车辆信息详情
     * @param array $vehicle_info
     * @param array $paramIn
     * @return array|mixed
     */
    protected function handleVehicleInfo(array $vehicle_info, array $paramIn)
    {
        if (empty($paramIn)) {
            return [];
        }

        // 为空, 补充默认字段 及 默认值
        if (empty($vehicle_info)) {
            // 车辆品牌及车辆型号、购买日期、油卡公司 van
            $vehicle_info['vehicle_brand'] = '';
            $vehicle_info['vehicle_brand_text'] = '';
            $vehicle_info['vehicle_model'] = '';
            $vehicle_info['vehicle_model_text'] = '';
            $vehicle_info['buy_date'] = null;
            $vehicle_info['oil_number'] = '';
            $vehicle_info['oil_company'] = '';

            // 车辆照片/机动车登记证 &&
            $vehicle_info['vehicle_img'] = '';
            $vehicle_info['registration_certificate_img'] = '';

            // 车辆保险 &&
            $vehicle_info['insurance_policy_number'] = '';
            $vehicle_info['insurance_start_date'] = null;
            $vehicle_info['insurance_end_date'] = null;

            // 车辆税 &&
            $vehicle_info['vehicle_tax_expiration_date'] = null;
            $vehicle_info['vehicle_tax_certificate_img'] = '';

            // 驾照信息 &&
            $vehicle_info['driver_license_type'] = '';
            $vehicle_info['driver_license_type_other_text'] = '';
            $vehicle_info['driver_license_start_date'] = null;
            $vehicle_info['driver_license_end_date'] = null;
            $vehicle_info['driving_licence_img'] = '';
        } else {
            // 删除无需字段
            unset($vehicle_info['id']);
            unset($vehicle_info['deleted']);
            unset($vehicle_info['money']);
            unset($vehicle_info['is_open']);
            unset($vehicle_info['open_date']);
            unset($vehicle_info['updated_at']);
            unset($vehicle_info['created_at']);
            unset($vehicle_info['is_cut_money']);
            unset($vehicle_info['balance']);
            unset($vehicle_info['unit_price']);
            unset($vehicle_info['approval_staff_id']);
            unset($vehicle_info['approval_time']);
            unset($vehicle_info['creator_id']);
            unset($vehicle_info['editor_id']);
            unset($vehicle_info['create_channel']);
        }

        // 车辆类型, 职位优先
        $vehicle_info['vehicle_type'] = VehicleInfoEnums::JOB_VEHICLE_TYPE_REL_CODE[$paramIn['job_title']];
        $vehicle_info['vehicle_type_label'] = VehicleInfoEnums::VEHICLE_TYPE_ITEM[$vehicle_info['vehicle_type']];
        // 车辆来源: 同步fbi-hr_is数据
        $hr_staff_info = HrStaffInfoServer::getUserInfoByStaffInfoId($paramIn['id'], 'vehicle_source, vehicle_use_date,hire_type');

        $hr_staff_info = $hr_staff_info ? $hr_staff_info->toArray() : [];
        $vehicle_info['hire_type'] = intval($hr_staff_info['hire_type']);

        if (empty($vehicle_info['vehicle_source'])) {
            $vehicle_info['vehicle_source'] = $hr_staff_info['vehicle_source'] ?? VehicleInfoEnums::VEHICLE_SOURCE_PERSONAL_CODE;
            $vehicle_info['vehicle_start_date'] = $hr_staff_info['vehicle_use_date'] ?? null;
        }

        $vehicle_info['vehicle_source_label'] = $vehicle_info['vehicle_source'] ? $this->getTranslation()->_(VehicleInfoEnums::VEHICLE_SOURCE_ITEM[$vehicle_info['vehicle_source']]) : '';

        // 车牌号 hr-is取默认值, 如没有，则再从whr_is取默认值
        if (empty($vehicle_info['plate_number'])) {
            $vehicle_info['plate_number'] = (new StaffRepository())->getAvatar($paramIn['id'], 'CAR_NO');
        }

        // 上牌地点/发动机号码/驾照号码 whr-is取默认值
        if (
            $this->checkStaffSelfSubmit($paramIn['id']) &&
            ( empty($vehicle_info['plate_number'])
                ||
                empty($vehicle_info['license_location'])
                ||
                empty($vehicle_info['engine_number'])
                ||
                empty($vehicle_info['driver_license_number'])
            )
        ) {

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['entry' => HrEntryModel::class]);
            $builder->innerJoin(HrEconomyAbilityModel::class,'entry.resume_id = hr.resume_id', 'hr');
            $builder->where('entry.staff_id = :staff_id:', ['staff_id' => $paramIn['id']]);
            $builder->columns([
                'hr.car_number',// 车牌号
                'hr.driver_number',//驾照号
                'hr.place_cards',//上牌地点
                'hr.car_engine_number',//发动机号
            ]);
            $win_staff_info = $builder->getQuery()->getSingleResult();
            if (!empty($win_staff_info)) {
                $vehicle_info['plate_number'] = !empty($vehicle_info['plate_number']) ? $vehicle_info['plate_number'] : $win_staff_info->car_number ?? '';
                $vehicle_info['license_location'] = !empty($vehicle_info['license_location']) ? $vehicle_info['license_location'] : $win_staff_info->place_cards ?? '';
                $vehicle_info['engine_number'] = !empty($vehicle_info['engine_number']) ? $vehicle_info['engine_number'] : $win_staff_info->car_engine_number ?? '';
                $vehicle_info['driver_license_number'] = !empty($vehicle_info['driver_license_number']) ? $vehicle_info['driver_license_number'] : $win_staff_info->driver_number ?? '';
            }
        }

        $vehicle_setting = UC('vehicleInfo');

        $vehicle_info['oil_company']=intval($vehicle_info['oil_company']);

        // 油卡企业
        $vehicle_info['oil_company_label'] = '';
        if (!empty($vehicle_info['oil_company'])) {
            $oil_company_conf = array_column($vehicle_setting['oil_company'], 'label', 'value');
            $vehicle_info['oil_company_label'] = $oil_company_conf[$vehicle_info['oil_company']] ?? '';
        }

        // 车辆品牌/车辆型号
        $vehicle_info['vehicle_brand_label'] = '';
        $vehicle_info['vehicle_model_label'] = '';
        if ($vehicle_info['vehicle_type'] == VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE && !empty($vehicle_info['vehicle_brand'])) {
            $vehicle_brand_conf = array_column($vehicle_setting['vehicle_brand'], null, 'value');
            $vehicle_model_conf = array_column($vehicle_brand_conf[$vehicle_info['vehicle_brand']]['data'], 'label', 'value');

            $vehicle_info['vehicle_brand_label'] = $vehicle_brand_conf[$vehicle_info['vehicle_brand']]['label'] ?? '';
            $vehicle_info['vehicle_model_label'] = !empty($vehicle_info['vehicle_model']) ? $vehicle_model_conf[$vehicle_info['vehicle_model']] : '';
        }

        // 车型
        $vehicle_info['vehicle_size'] = $vehicle_info['vehicle_size'] ?? '';
        $vehicle_info['vehicle_size_label'] = self::getVehicleSize(true)[$vehicle_info['vehicle_size']]?? '';

        // 油类型
        $vehicle_info['oil_type'] = $vehicle_info['oil_type'] ?? '';
        $vehicle_info['oil_type_label'] = $vehicle_info['oil_type'] ? $vehicle_setting['oil_type'][$vehicle_info['oil_type']] : '';

        // 驾照图片(两张) &&
        $vehicle_info['driving_licence_img_item'] = [];
        if (!empty($vehicle_info['driving_licence_img'])) {
            $vehicle_info['driving_licence_img_item'] = explode("\n", $vehicle_info['driving_licence_img']);
        }

        // 驾照类型 &&
        $vehicle_info['driver_license_type_label'] = '';
        if (!empty($vehicle_info['driver_license_type'])) {
            foreach (VehicleInfoEnums::DRIVER_LICENSE_TYPE_ITEM as $license_k => $license_v) {
                if ($license_k == $vehicle_info['driver_license_type']) {
                    $driver_license_type_label = is_array($license_v) ? VehicleInfoEnums::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.$license_k : $license_v;
                    $vehicle_info['driver_license_type_label'] = $this->getTranslation()->_($driver_license_type_label);

                    break;
                }

                if (is_array($license_v)) {
                    foreach ($license_v as $sub_k => $sub_v) {
                        if ($sub_k == $vehicle_info['driver_license_type']) {
                            $vehicle_info['driver_license_type_label'] = $this->getTranslation()->_($sub_v);
                            break;
                        }
                    }
                }
            }
        }

        // 审核状态
        $vehicle_info['approval_status'] = $vehicle_info['approval_status'] ?? VehicleInfoEnums::APPROVAL_UN_SUBMITTED_CODE;

        // 员工入职日期
        $vehicle_info['staff_hire_date'] = $this->getStaffHireDate($paramIn['id']);
        //电车  不需要油卡相关信息
        $vehicle_info['is_var_hidden_oil_card'] = $paramIn['job_title'] == VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID;
        $vehicle_info['is_show_project_num']    = in_array($paramIn['job_title'],
            [VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID, VehicleInfoEnums::JOB_VAN_PROJECT_TITLE_ID]);

        return $vehicle_info;
    }

    /**
     * 里程上报昨日下班上报里程数与当日上班上报里程数差值是否>500KM 不换车则加备注
     * @param $staff_info_id
     * @param $large_mileage_reason
     * @return bool
     */
    public function addLargeMileageReason($staff_info_id, $large_mileage_reason): bool
    {
        $master_staff_id = StaffRepository::getMasterStaffIdBySubStaff($staff_info_id) ?: $staff_info_id;

        $vehicleInfo = VehicleInfoModel::findFirst([
            'conditions' => 'uid = :uid: ',
            'bind'       => ['uid' => $master_staff_id],
        ]);
        if (!empty($vehicleInfo)) {
            $vehicleInfo->large_mileage_reason = $large_mileage_reason;
            $vehicleInfo->audit_status         = VehicleInfoEnums::APPROVAL_WAIT_NW_CODE;;
            $vehicleInfo->save();
            $vehicleLogData                     = [];
            $vehicleLogData['staff_id']         = $master_staff_id;
            $vehicleLogData['operate_staff_id'] = $staff_info_id;
            $vehicleLogData['text']             = json_encode($vehicleInfo->toArray(), JSON_UNESCAPED_UNICODE);
            $this->vehicle->addVehicleInfoLog($vehicleLogData);
        }
        return false;
    }
}
