<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AttendanceWhiteListModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffCriminalRecordModel;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\SickServer;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\CheckPhoneServer;
use FlashExpress\bi\App\Server\CheckPunchOutServer as BaseCheckPunchOutServer;
use FlashExpress\bi\App\Server\JobTransferConfirmServer;
use FlashExpress\bi\App\Server\MaterialInventoryCheckServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\ProbationTargetServer;
use FlashExpress\bi\App\Server\KpiServer;
use FlashExpress\bi\App\Server\WhiteListServer;

class CheckPunchOutServer extends BaseCheckPunchOutServer {

    /**
     * @throws ValidationException|BusinessException
     */
    public function check_staff_punch_out($staffInfo,$paramsIn)
    {
        $returnData = [
            'data'=> (object)[],'code'=>-3,'msg'=>'fail',
        ];
        $this->staffInfo = $staffInfo;
        $this->platform = $paramsIn['platform'];

        if (env('close_punch_out_check', 0)) {
            return $returnData;
        }

        //java验证
        $check_java_task = $this->check_java_task($paramsIn['is_skip']);
        $this->logger->write_log(['check_staff_punch_out'=>$paramsIn,'check_java_task'=>$check_java_task,'staff_info'=>$this->staffInfo] ,'info');
        if(!empty($check_java_task)){
            $returnData['data'] = $check_java_task;
            return $returnData;
        }

        //验证未读的消息
        $check_un_read_message = $this->check_un_read_message();
        if(!empty($check_un_read_message)){
            $returnData['data'] = $check_un_read_message;
            return $returnData;
        }

        // 验证未完成学习计划
        $check_un_finished_study = $this->check_un_finished_study();
        if(!empty($check_un_finished_study)){
            $returnData['data'] = $check_un_finished_study;
            return $returnData;
        }

        //验证是否有未添加辅导员的员工
        $check_un_instructor = $this->check_un_instructor_task();
        if(!empty($check_un_instructor)) {
            $returnData['data'] = $check_un_instructor;
            return $returnData;
        }

        // 验证kpi 是否已经完成
        $verifyKpiResult = (new KpiServer($this->lang,$this->timezone))->verifyKpiIsCompleted($staffInfo);
        if (!empty($verifyKpiResult)) {
            $returnData['data'] = $verifyKpiResult;
            return $returnData;
        }

        //验证是否有未处理完成资产
        $verify_has_resign_assets = $this->check_un_has_resign_assets();
        if (!empty($verify_has_resign_assets)) {
            $returnData['data'] = $verify_has_resign_assets;
            return $returnData;
        }

        //验证是否有未完成合同续签审批
        $verify_has_has_approval_renew_contract = $this->check_un_has_approval_renew_contract();
        if (!empty($verify_has_has_approval_renew_contract)) {
            $returnData['data'] = $verify_has_has_approval_renew_contract;
            return $returnData;
        }

        //验证是否有待确认转岗
        $check_unconfirmed_transfer = $this->check_unconfirmed_transfer();
        if (!empty($check_unconfirmed_transfer)) {
            $returnData['data'] = $check_unconfirmed_transfer;
            return $returnData;
        }

        //验证是否有未盘任务
        $verify_has_undone_inventory_task = (new MaterialInventoryCheckServer($this->lang, $this->timezone))->verifyHasUndoneInventoryTask($staffInfo);
        if (!empty($verify_has_undone_inventory_task)) {
            $returnData['data'] = $verify_has_undone_inventory_task;
            return $returnData;
        }

        //新增申请病假没提交资料拦截 只有泰国病假 https://flashexpress.feishu.cn/docx/W8tJd2XoIoLt24xhb14cb80pnMb
        $sickServer = new SickServer($this->lang,$this->timezone);
        $sickCheck = $sickServer->sickPunchOut($staffInfo);
        if(!empty($sickCheck)){
            $returnData['data'] = $sickCheck;
            return $returnData;
        }

        //20866【TH|HCM|试用期】非一线员工试用期管理优化
        $probationTargetServer = new ProbationTargetServer($this->lang, $this->timezone);
        $targetCheck       = $probationTargetServer->checkStaffManagerSign($staffInfo['id']);
        if (!empty($targetCheck)) {
            $returnData['data'] = $targetCheck;
            return $returnData;
        }

        //20951【TH｜BY/OA】员工手机号码准确性校验
        $checkPhoneServer = new CheckPhoneServer($this->lang, $this->timezone);
        $phoneCheck       = $checkPhoneServer->checkPunchOut($staffInfo['id']);
        if (!empty($phoneCheck)) {
            $returnData['data'] = $phoneCheck;
            return $returnData;
        }

        //验证是否有未完成转正评估目标制定
        $check_probation_target = $this->check_probation_target();
        if (!empty($check_probation_target)) {
            $returnData['data'] = $check_probation_target;
            return $returnData;
        }

        //验证是否有第二阶段超时且审核完犯罪记录的转正评估
        $check_probation_criminal = $this->check_probation_criminal();
        if (!empty($check_probation_criminal)) {
            $returnData['data'] = $check_probation_criminal;
            return $returnData;
        }

        //验证是否有到最后一天期限没有完成评估或有已超时的评估
        $check_probation_timeout = $this->check_probation_timeout();
        if (!empty($check_probation_timeout)) {
            $returnData['data'] = $check_probation_timeout;
            return $returnData;
        }

        $returnData['code'] = 1;
        $returnData['msg'] = 'success';
        return  $returnData;
    }

    public function check_probation_timeout(): array
    {
        $returnData       = [];
        $staff_info_id = $this->staffInfo['id'];
        if (empty($staff_info_id)){
            return $returnData;
        }
        // 考勤打卡白名单
        $isInfWhite =  (new WhiteListServer())->isInfWhiteList($staff_info_id,date('Y-m-d'),[AttendanceWhiteListModel::TYPE_PAID_LOCALLY,AttendanceWhiteListModel::TYPE_NOT_PAID_LOCALLY]);
        if ($isInfWhite){
            return $returnData;
        }

        $before_check = HrProbationAuditModel::findFirst([
            "audit_id = :staff_info_id:",
            "bind" => [
                "staff_info_id" => $staff_info_id,
            ],
        ]);
        if (empty($before_check)) {
            return $returnData;
        }
        // 转正白名单
        $probation_staff = (new SettingEnvServer())->getSetValToArray('probation_staff');
        $deadline = date('Y-m-d', strtotime('+1 day'));
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("a.audit_status,a.staff_info_id,a.deadline_at,a.is_active,a.audit_level");
        $builder->from(['a' => HrProbationAuditModel::class]);
        $builder->leftjoin(HrProbationModel::class, "p.staff_info_id = a.staff_info_id", 'p');
        $builder->leftjoin(HrStaffInfoModel::class, "p.staff_info_id = s.staff_info_id", 's');
        $builder->leftjoin(StaffCriminalRecordModel::class, "c.staff_info_id = a.staff_info_id", 'c');
        $builder->andWhere('p.status != :p_status: and p.probation_channel_type = :probation_channel_type: and a.audit_id = :audit_id: and s.state != :state: and p.is_system = :is_system: and c.review_status = :review_status:', [
            'audit_id' => $staff_info_id,
            'state'=>HrStaffInfoModel::STATE_2,
            'probation_channel_type'=>HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE,
            'p_status'=>HrProbationModel::STATUS_FORMAL,
            'is_system'=>HrProbationModel::IS_NOT_SYSTEM,
            'review_status'=>StaffCriminalRecordModel::REVIEW_STATUS_PASS,
        ]);
        if (!empty($probation_staff)){
            $builder->notInWhere('s.staff_info_id ',$probation_staff);
        }
        $builder->orderby('a.id desc');
        $staffData = $builder->getQuery()->execute()->toArray();
        if (empty($staffData)){
            return $returnData;
        }
        $is_check_probation_timeout = false;
        foreach ($staffData as $value){
            if ($value['audit_level'] == HrProbationAuditModel::AUDIT_LEVEL_FIRST && $value['audit_status'] == HrProbationAuditModel::AUDIT_STATUS_TIMEOUT && $value['is_active'] == 0){
                $is_check_probation_timeout = true;
                break;
            }
            if (!empty($value['deadline_at']) && $value['audit_status'] == HrProbationAuditModel::AUDIT_STATUS_PENDING && $value['deadline_at'] <= $deadline){
                $is_check_probation_timeout = true;
                break;
            }
        }
        if ($is_check_probation_timeout){
            $returnData['business_type']     = 'un_remittance';
            $returnData['remittance_detail'] = [
                'dialog_msg'         => $this->getTranslation()->_('probation_timeout_error'),// 提示消息内容
                'dialog_status'      => 1, //弹窗
                'dialog_must_status' => 1, //是否跳过 1不能跳过
                'is_ces_tra'         => 0, //是否ces培训 0否
            ];
        }
        return $returnData;
    }

    /**
     * 验证是否有未添加辅导员的员工 非网点主管角色不验证
     * @return array
     */
    protected function check_un_instructor_task() {
        $this->logger->write_log('check_un_instructor_task '.$this->staffInfo['staff_id']. ' staff_info:'.json_encode($this->staffInfo,JSON_UNESCAPED_UNICODE),'info');
        $returnData = [];
        //非网点主管角色不验证
        if(!in_array(18, $this->staffInfo['positions'])){
            return  $returnData;
        }
        //$paramIn['store_id'] = $this->staffInfo['organization_id'];
        $paramIn['is_three_day'] = true;
        $paramIn['user'] = $this->staffInfo;
        $count = (new StaffServer())->getStaffJobTitleInstructorCount($paramIn);
        if($count > 0) {
            $returnData['business_type'] = 'un_finished_study';
            $returnData['training_detail'] = [
                'message' => $this->getTranslation()->_('add_instructor_error_4'),
                'url' => env("sign_url").'/#/boardConfirm?active=tab-two&tab=1&sub_tab=2',
            ];
        }
        $this->logger->write_log('check_un_instructor_task '.$this->staffInfo['staff_id']. ' result:'.json_encode($count,JSON_UNESCAPED_UNICODE),'info');
        return $returnData;
    }

    /**
     * 验证是否有未完成续签合同审批
     * @return array
     */
    protected function check_un_has_approval_renew_contract() {
        $returnData       = [];
        $staff_info_id = $this->staffInfo['id'];
        $date          = zero_time_zone(date('Y-m-d 00:00:00', strtotime('-5 days')));

        $pending_list = AuditApprovalModel::find([
            'conditions' => "biz_type = :type: and approval_id = :approval_id: and state = :state: and created_at < :date:",
            'bind'       => [
                'type'  => enums::$audit_type['RC'],
                'state' => enums::$audit_status['panding'],
                'approval_id' => $staff_info_id,
                'date'  => $date,
            ],
        ])->toArray();

        if(!empty($pending_list)) {
            //有未处理续签合同审批
            $returnData['business_type']     = 'un_remittance';
            $returnData['remittance_detail'] = [
                'dialog_msg'         => $this->getTranslation()->_('has_approval_renew_contract_error_1'),// 提示消息内容
                'dialog_status'      => 1, //弹窗
                'dialog_must_status' => 1, //是否跳过 1不能跳过
                'is_ces_tra'         => 0, //是否ces培训 0否
            ];
        }
        return $returnData;
    }

    /**
     * 校验是否存在转岗未确认（子账户登陆要判断主账号是否存在）
     * @return array
     */
    protected function check_unconfirmed_transfer(): array
    {
        $staffId = $this->staffInfo['staff_id'];
        $transferInfo = (new JobTransferConfirmServer($this->lang, $this->timezone))->getPendingConfirmInfo($staffId);
        if (!empty($transferInfo)) {
            $returnData['business_type'] = 'un_confirmed_transfer';

            if ($transferInfo->after_hire_type == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                $errMsgExistConfirm = 'job_transfer.err_msg_exist_agent_confirm';
            } else {
                $errMsgExistConfirm = 'job_transfer.err_msg_exist_confirm';
            }
            $returnData['un_confirmed_transfer'] = [
                'message' => $this->getTranslation()->_($errMsgExistConfirm),
                'url'     => env("sign_url") . sprintf('/#/job-transfer/confirm-detail?id=%d&from=app', $transferInfo->id),
            ];
        }
        return $returnData ?? [];
    }
}