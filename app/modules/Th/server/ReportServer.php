<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\AuditLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\ReportAuditModel;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class ReportServer extends \FlashExpress\bi\App\Server\ReportServer
{

    /**
     *
     *
     *  6. 针对所有举报(上级举报、QAQC举报)撤销规则
     * 1. 一级审批人审批前，申请人可撤销
     * 2. 终审同意前，仅最后一个同意的审批人可撤销
     * 3. 终审后任何人不允许撤销
     *
     * @param $comeFrom
     * @param $auditId
     * @param $user
     * @param $result
     * @param $infos
     * @param $option
     * @param $state
     * @return array
     */
    public function getOption($comeFrom, $auditId, $user, $result, $infos, $option, $state)
    {
        if ($result['status'] != enums::$audit_status['panding']) {
            //终审后任何人不允许撤销
            $state  = $result['status'];
            $option = ['beforeApproval' => '0', 'afterApproval' => '0'];
        } else {
            if ($comeFrom == 2) {
                // 我审批的
                $state  = $infos['state'] ?? 0;
                $option = ['beforeApproval' => "1,2", 'afterApproval' => '0'];
                if ($state == enums::$audit_status['approved']) {
                    $info = AuditLogModel::findFirst([
                        'conditions' => 'flow_id = :flow_id:',
                        'bind'       => ['flow_id' => $infos['flow_id']],
                        'order'      => 'id desc',
                    ]);
                    if ($info) {
                        $info = $info->toArray();
                        if ($info['staff_id'] == $user) {
                            //没有到终审 并且 最后的同意是自己批的
                            $option = ['beforeApproval' => '', 'afterApproval' => '3'];
                        }
                    }
                }
            } else {
                // 我申请的
                $info   = AuditApprovalModel::findFirst([
                    'conditions' => "biz_value = :value: and biz_type = :type: and deleted = 0",
                    'bind'       => [
                        'type'  => enums::$audit_type['RE'],
                        'value' => $auditId,
                    ],
                    'order'      => 'id asc',
                ]);
                $state  = enums::$audit_status['panding'];
                $option = ['beforeApproval' => '3', 'afterApproval' => '0'];
                if ($info) {
                    $info  = $info->toArray();
                    $state = $info['state'];
                    if ($state != enums::$audit_status['panding']) {
                        // 一级审批人批过
                        $option = ['beforeApproval' => '0', 'afterApproval' => '0'];
                    }
                }
            }
        }

        return [$option, $state];
    }

    /**
     * TH 举报申请，审批节点，非终审同意 等于 6，要将 审批状态 $status 1 才可以使前端 撤销按钮 的提交值改为
     * 梳理了半天的bug, 做的兼容。
     * 之前bug表现为：前端撤销按钮，提交参数为 1，而不是4.
     * 因为 status 为6的情况下 转为 2 ，前端处理逻辑，status != 2的情况下 提交参数 为 4
     *
     * 6:非终审同意
     * 1:待审批
     * @param $status
     * @return int|mixed
     */
    public function transferAuditStatus($status)
    {
        $status = $status == 6 ? 1 : $status;
        return $this->auditlist->transferAuditStatus($status);
    }

    public function getWarningStatus($status, $warning_status)
    {
        if (in_array($status, [
                enums::APPROVAL_STATUS_REJECTED,
                enums::APPROVAL_STATUS_CANCEL,
                enums::APPROVAL_STATUS_TIMEOUT,
            ]) && $warning_status == ReportAuditModel::REPORT_WARNING_STATUS_UN_PROCESS) {
            return ReportAuditModel::REPORT_WARNING_STATUS_NO_PROCESS;
        }

        if (in_array($status, [
                enums::APPROVAL_STATUS_PENDING,
                enums::APPROVAL_STATUS_APPROVAL,
            ]) && $warning_status == ReportAuditModel::REPORT_WARNING_STATUS_UN_PROCESS) {
            return ReportAuditModel::REPORT_WARNING_STATUS_UN_PROCESS;
        }

        return $warning_status;
    }

}