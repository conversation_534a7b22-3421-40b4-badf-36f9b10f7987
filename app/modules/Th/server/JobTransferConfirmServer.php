<?php

namespace FlashExpress\bi\App\Modules\Th\Server;


use FlashExpress\bi\App\Enums\JobTransferConfirmEnums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Modules\Th\library\Enums\CommonEnums;
use FlashExpress\bi\App\Repository\JobtransferRepository;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Server\JobTransferConfirmServer as BaseJobTransferConfirmServer;
use FlashExpress\bi\App\Server\PdfHelperServer;
use FlashExpress\bi\App\Server\StaffServer;

class JobTransferConfirmServer extends BaseJobTransferConfirmServer
{
    /**
     * @description 获取转岗详情
     * @param $params
     * @return array
     * @throws BusinessException
     * @throws BusinessException
     */
    public function getConfirmDetail($params)
    {
        $id          = $params['id'];
        $staffInfoId = $params['staff_info_id'];

        //获取转岗人信息
        $staffService = new StaffServer();
        $staffInfo = $staffService->getStaffInfoSpecColumns($staffInfoId,
            "h.staff_info_id, 
                h.name as staff_name, 
                sd.name as sys_department_name, 
                date_format(h.hire_date,'%Y-%m-%d') hire_date");
        //获取转岗信息
        $jobTransferRepo = new JobtransferRepository($this->timeZone);
        $detail = $jobTransferRepo->getJobTransferDetailById($id);

        //查询职位名称 ids
        $sysObj         = new SysListRepository();
        $positionIds    = [$detail['current_position_id'], $detail['after_position_id']];
        $positionData   = $sysObj->getPositionList(['ids' => $positionIds]);
        $positionData   = array_column($positionData, 'name', 'id');

        //查询网点名称 ids
        $storeIds  = [$detail['current_store_id'], $detail['after_store_id']];
        $storeData = $sysObj->getStoreList(['ids' => $storeIds]);
        $storeData = array_column($storeData, 'name', 'id');

        //查询部门名称 ids
        $departmentIds  = [$detail['current_department_id'], $detail['after_department_id']];
        $departmentData = $sysObj->getDepartmentList(['ids' => $departmentIds]);
        $departmentData = array_column($departmentData, 'name', 'id');

        //获取确认状态
        $confirmState = JobTransferConfirmEnums::getCodeTxtMap();

        //获取页眉页脚
        if (in_array($detail['after_hire_type'], HrStaffInfoModel::$agentTypeTogether)) { //个人代理
            $queryParams = [
                'company_id' => CommonEnums::FLASH_HOME_OPERATION,
            ];
            $templateCnf = (new PdfHelperServer())->getHeaderAndFooterByCompanyId($queryParams);
        } else {
            $queryParams = [
                'staff_info_id' => $staffInfoId,
                'department_id' => $detail['after_department_id'],
            ];
            $templateCnf = (new PdfHelperServer())->getHeaderAndFooter($queryParams);
        }

        //获取转岗前后工资
        $salaryDetail = $this->getTransferSalaryById(['job_transfer_id' => $id]);
        if (isCountry()) {

            if (in_array($detail['after_hire_type'], HrStaffInfoModel::$agentTypeTogether)) { //个人代理无薪资
                $base_salary_before = $base_salary_after = null;
            } else {
                $beforeUnit         = $detail['current_hire_type'] != HrStaffInfoModel::HIRE_TYPE_3 ? $this->getTranslation()->_('transfer_unit_month') : $this->getTranslation()->_('transfer_unit_daily');
                $afterUnit          = $detail['after_hire_type'] != HrStaffInfoModel::HIRE_TYPE_3 ? $this->getTranslation()->_('transfer_unit_month') : $this->getTranslation()->_('transfer_unit_daily');
                $base_salary_before = isset($salaryDetail['base_salary_before']) ? $salaryDetail['base_salary_before'] . ' ' . $beforeUnit : null;
                $base_salary_after  = isset($salaryDetail['base_salary_after']) ? $salaryDetail['base_salary_after'] . ' ' . $afterUnit : null;
            }
        } else {
            $base_salary_before = $salaryDetail['base_salary_before'] ?? null;
            $base_salary_after  = $salaryDetail['base_salary_after'] ?? null;
        }
        $result = [
            'staff_info_id'                => $staffInfoId,
            'staff_name'                   => $staffInfo['staff_name'],
            'belong_store_name'            => $storeData[$detail['current_store_id']] ?? '',
            'first_level_department_name'  => $staffInfo['sys_department_name'],
            'before_job_title_name'        => $positionData[$detail['current_position_id']] ?? '',
            'before_store_name'            => $storeData[$detail['current_store_id']] ?? '',
            'before_department_name'       => $departmentData[$detail['current_department_id']] ?? '',
            'before_job_title_grade'       => sprintf('F%d', $detail['current_job_title_grade']),
            'before_base_salary'           => $base_salary_before,
            'before_working_day_rest_type' => $this->getTranslation()->_('working_day_rest_type_' . $detail['before_working_day_rest_type']) ?? '',
            'before_manager_id'            => $detail['current_manager_id'],
            'after_job_title_name'         => $positionData[$detail['after_position_id']] ?? '',
            'after_store_name'             => $storeData[$detail['after_store_id']] ?? '',
            'after_department_name'        => $departmentData[$detail['after_department_id']] ?? '',
            'after_job_title_grade'        => sprintf('F%d', $detail['after_job_title_grade']),
            'after_base_salary'            => $base_salary_after,
            'after_working_day_rest_type'  => $this->getTranslation()->_('working_day_rest_type_' . $detail['after_working_day_rest_type']) ?? '',
            'after_manager_id'             => $detail['after_manager_id'],
            'hire_date'                    => $staffInfo['hire_date'],
            'confirm_state'                => $detail['confirm_state'],
            'confirm_state_label'          => $this->getTranslation()->_($confirmState[$detail['confirm_state']]),
            'type'                         => $detail['type'],
            'state'                        => $detail['state'],
            'before_hire_type'             => $detail['current_hire_type'],
            'after_hire_type'              => $detail['after_hire_type'],
            'after_date'                   => $detail['after_date'],
        ];
        $result = array_merge($result, $templateCnf);

        return $this->checkReturn(['data' => $result]);
    }

    /**
     * 获取要固化的转岗确认详情
     * @param $params
     * @param $jobTransferInfo
     * @return array
     * @throws ValidationException
     * @throws \Exception
     */
    public function getPersistConfirmBaseInfo($params, $jobTransferInfo): array
    {
        $state   = $params['state'];
        $version = $params['version'] ?? '';

        //获取确认详情
        //将转岗确认的数据进行排序
        $transferDetail     = $this->getTransferPendingConfirmInfo($jobTransferInfo->toArray());

        //确认同意需要验证版本
        if (!empty($version) && $state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS) {
            $sortTransferDetail = $transferDetail['salary'];
            $this->sortArrayRecursively($sortTransferDetail);
            if ($version != md5(json_encode($sortTransferDetail, JSON_UNESCAPED_UNICODE))) {
                throw new ValidationException($this->getTranslation()->_('err_msg_job_transfer_confirm_version_not_latest'), ErrCode::JOB_TRANSFER_CONFIRM_DATE_VERSION_NOT_LATEST);
            }
        }
        return [
            'transfer_info' => $transferDetail,
            'persist_info'  => [
                'after_date' => $jobTransferInfo->after_date
            ],
            'lang' => $this->lang,
        ];
    }

    public function getTransferPendingConfirmInfo($transfer_info)
    {
        //薪资
        $salaryInfo = $this->getTransferSalaryById(['job_transfer_id' => $transfer_info['id']]);

        return [
            'basic' => [
                [
                    'before' => $transfer_info['current_position_id'],
                    'after'  => $transfer_info['after_position_id'],
                    'name'   => 'Job Title',
                ],
                [
                    'before' => $transfer_info['current_department_id'],
                    'after'  => $transfer_info['after_department_id'],
                    'name'   => 'Department',
                ],
                [
                    'before' => $transfer_info['current_store_id'],
                    'after'  => $transfer_info['after_store_id'],
                    'name'   => 'Branch',
                ],
                [
                    'before' => $transfer_info['current_job_title_grade'],
                    'after'  => $transfer_info['after_job_title_grade'],
                    'name'   => 'Job Grade',
                ],
                [
                    'before' => $transfer_info['before_working_day_rest_type'],
                    'after'  => $transfer_info['after_working_day_rest_type'],
                    'name'   => 'Work Days',
                ],
                [
                    'before' => $transfer_info['current_manager_id'],
                    'after'  => $transfer_info['after_manager_id'],
                    'name'   => 'Manager',
                ],
            ],
            'salary' => [
                [
                    'before' => $salaryInfo['base_salary_before'],
                    'after'  => $salaryInfo['base_salary_after'],
                    'name'   => 'Salary',
                ]
            ],
        ];
    }
}