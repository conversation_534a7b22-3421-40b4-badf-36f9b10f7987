<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Enums\AiStateEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\BanklistModel;
use FlashExpress\bi\App\Models\backyard\HrStaffIdentityAnnexModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\SalaryGongZiModel;
use FlashExpress\bi\App\Models\backyard\StaffInfoAuditCheckModel;
use FlashExpress\bi\App\Repository\BankListRepository;
use FlashExpress\bi\App\Repository\StaffInfoAuditCheckRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\AiServer;
use FlashExpress\bi\App\Server\PersoninfoServer as GlobalBaseServer;


class PersoninfoServer extends GlobalBaseServer
{
    /**
     * 服务与一次性脚本 根据11月份发放薪资刷银行卡审核状态
     * @param string $salary_date
     * @return array
     */
    public function salary_to_bank_card($salary_date = '2023-11')
    {
        $return_data = ["count" => 0, "ids" => "", "diff_error_ids" => ""];
        // 薪资信息
        $salary = SalaryGongZiModel::find([
            'columns'    => 'staff_info_id,bank_no,bank_type,staff_name',
            'conditions' => 'excel_month = :excel_month: and status = :status:',
            'bind'       => ['excel_month' => $salary_date, 'status' => SalaryGongZiModel::SALARY_STATUS_PAID],
        ])->toArray();
        $salary = array_column($salary, null, 'staff_info_id');
        // 银行卡审核信息
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('
                    staff_identity.id,
                    staff_identity.staff_info_id,
                    staff_info.bank_no,
                    staff_info.name
                ');
        $builder->from(['staff_identity' => HrStaffIdentityAnnexModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'staff_info.staff_info_id = staff_identity.staff_info_id',
            'staff_info');
        $builder->where('staff_identity.bank_card_audit_state = 0 and staff_info.state != 2 and bank_no !=\'\'');
        $identity_annex_list = $builder->getQuery()->execute()->toArray();
        $identity_annex_list = array_column($identity_annex_list, null, 'staff_info_id');
        $data                = array_intersect_key($identity_annex_list, $salary);
        $update_id_arr       = [];
        $diff_error_ids      = "";
        foreach ($data as $k => $v) {
            if ($salary[$v['staff_info_id']]['bank_no'] == $identity_annex_list[$v['staff_info_id']]['bank_no'] && $salary[$v['staff_info_id']]['staff_name'] == $identity_annex_list[$v['staff_info_id']]['name']) {
                $update_id_arr[] = $v['staff_info_id'];
            } else {
                $diff_error_ids .= $v['staff_info_id'] . ',';
            }
        }
        if (empty($update_id_arr)) {
            return $return_data;
        }
        $id_str = implode(',', $update_id_arr);
        $res    = $this->getDI()->get('db')->updateAsDict('hr_staff_identity_annex',
            ["bank_card_audit_state" => 1], [
                'conditions' => "staff_info_id in (" . $id_str . ")",
            ]);
        if (!$res) {
            $this->getDI()->get("logger")->write_log('salary_to_bank_card update error update_id_str:' . $id_str,
                'error');
        }
        $return_data["count"]          = count($update_id_arr);
        $return_data["ids"]            = $id_str;
        $return_data["diff_error_ids"] = $diff_error_ids;
        return $return_data;
    }

    public function ai_bank_card($bank_card_url, $staff_info_id, $bank_no = '',$bank_type = 0)
    {
        $db = $this->getDI()->get("db");

        try {
            $db->begin();
            $ai_audit_state = 2; // ai审核状态 1通过 2未通过

            $t           = $this->getTranslation();
            $imgArrayUrl = explode('?', $bank_card_url);//地址有参数
            $imgArray    = explode('.', $imgArrayUrl[0]);
            $file_type   = end($imgArray);
            if (!in_array(strtolower($file_type), ['jpg', 'jpeg', 'png'])) {
                throw new ValidationException($t->_('os_staff_face_check_file_type_error'));
            }

            $params    = ['url' => $bank_card_url];
            $params    = http_build_query($params);
            $ai_result = AiServer::getInstance()->setConfig(enums::IDENTIFY_BANK_CARD)->send($params, $staff_info_id);

            if (strtoupper($ai_result['status']) != 'OK') {
                switch ($ai_result['error']['code']) {
                    case 'IMAGE_SIZE_EXCEED':
                        $msg       = $t->_('bank_card_ai_audit_error_1'); // 图片尺寸过大，请上传10M以内的图片
                        $is_submit = false;
                        break;
                    case 'NO_TARGET_DETECTED':
                        $msg       = $t->_('bank_card_ai_audit_error_2'); // 请按照规范要求上传照片
                        $is_submit = true;
                        break;
                    default:
                        $msg       = $t->_('bank_card_ai_audit_error_3');// 未检测到存折，请保证照片无遮挡
                        $is_submit = false;
                }
            } else {
                $redis     = $this->getDI()->get('redisLib');
                $redis_key = 'STAFF_AI_BANK_CARD_UPLOAD_COUNT_'.$staff_info_id;
                $redis->expire($redis_key, 30 * 60);
                $upload_count = $redis->incr($redis_key);

                $str_replace_arr = ["-", "—", "/", "·", ".", " ", "\n"];
                $ai_bank_no      = str_replace($str_replace_arr, '', $ai_result['result']['account_no']);
                $ai_bank_no_name = $ai_result['result']['account_name'];
                $ai_bank_name    = strtoupper($ai_result['result']['bank_name']);

                $builder = $this->modelsManager->createBuilder();
                $builder->columns('
                    staff_info.staff_info_id,
                    staff_info.hire_type,
                    staff_info.bank_no,
                    staff_items.value as bank_no_name
                ');
                $builder->from(['staff_info' => HrStaffInfoModel::class]);
                $builder->leftJoin(HrStaffItemsModel::class,
                    'staff_info.staff_info_id = staff_items.staff_info_id and staff_items.item = \'BANK_NO_NAME\'',
                    'staff_items');
                $builder->where('staff_info.staff_info_id = :staff_info_id:',
                    ['staff_info_id' => $staff_info_id]);
                $data               = $builder->getQuery()->execute()->getFirst();
                $staff_info         = empty($data) ? [] : $data->toArray();

                $staff_bank_no      = str_replace($str_replace_arr, '', ($bank_no ? $bank_no : $staff_info['bank_no']));

                $staff_bank_no_name = $staff_info['bank_no_name'];

                /**
                 * 返回的name_th==员工信息的姓名
                AI识别结果的特殊情况：开头น.ส.视作นางสาว和HCM的员工信息进行对比
                AI：น.ส. == HCM：นางสาว
                AI：น.ส. == HCM：น.ส.
                 */
                $nameFlag1 = $this->checkString(nameSpecialCharsReplace($ai_bank_no_name), $staff_bank_no_name);

                //兼容泰文 女
                $thPrefixString = !empty($ai_bank_no_name) ? substr($ai_bank_no_name, 0, 8) : '';
                $aiName = strcasecmp($thPrefixString, 'น.ส.') === 0 ?  'นางสาว' . substr($ai_bank_no_name, 8) : $ai_bank_no_name;
                $nameFlag2 = $this->checkString(nameSpecialCharsReplace($aiName), $staff_bank_no_name);

                $name_th_flag = ($nameFlag1 || $nameFlag2) ? true : false;
                //ai识别的银行
                $ai_bank_type = 0;
                if (!empty($staff_info['hire_type']) && $staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                    //获取AI审核的银行名称
                    $ai_bank_list = AiStateEnums::getAiApiBankList();

                    //系统银行名称
                    $bank_list = (new BankListRepository())->getBankList();



                    //ai银行信息 - AI返回名称和HCM银行名称全部转大写去除空格符号比对
                    $ai_bank_name = strtoupper(str_replace(' ', '', $ai_result['result']['bank_name']));

                    //先匹配AI的集合
                    foreach ($ai_bank_list as $k => $v) {
                        $bank_name = strtoupper(str_replace(' ', '', $k));

                        if ($bank_name == $ai_bank_name) {
                            $ai_bank_type = $v;
                        }
                    }

                    //AI的没有从数据库匹配
                    if (!$ai_bank_type) {
                        foreach ($bank_list as $v) {
                            $bank_name = strtoupper(str_replace(' ', '', $v['bank_name']));
                            if ($bank_name == $ai_bank_name) {
                                $ai_bank_type = $v['bank_id'];
                            }
                        }
                    }

                    if (
                        ($staff_bank_no == $ai_bank_no)
                        && ($name_th_flag)
                        && ($bank_type == $ai_bank_type)
                        && ($bank_type > 0)
                    ) {
                        $ai_audit_state = 1;
                        $is_submit = true;
                        $msg       = $t->_('bank_card_ai_audit_6');
                    } else {
                        $is_submit = true;
                        $msg       = $t->_('bank_card_ai_audit_error_5'); // AI审核未通过，点击提交将进入人工审核
                    }
                } else {
                    if ($staff_bank_no == $ai_bank_no && $name_th_flag && $ai_bank_name == $this->showBankTypeName(2)) {
                        $ai_audit_state = 1;
                        $redis->delete($redis_key);
                        $is_submit = true;
                        $msg       = $t->_('bank_card_ai_audit_6');
                    } else {
                        if ($upload_count <= 1) {
                            $is_submit = false;
                            $msg       = $t->_('bank_card_ai_audit_error_4'); // AI审核不通过，请确保SCB银行存折账号正确并重新上传
                        } else {
                            $is_submit = true;
                            $msg       = $t->_('bank_card_ai_audit_error_5'); // AI审核未通过，点击提交将进入人工审核
                        }
                    }
                }

                //将 ai 识别到的数据保存下来
                $aiRecognitionData['bank_name']       = $ai_result['result']['bank_name'];
                $aiRecognitionData['bank_name_after'] = $ai_bank_name;//ai 识别出来，处理后的数据， 用来跟员工信息中的银行类型做对比
                $aiRecognitionData['hire_type']       = $staff_info['hire_type'];
                $aiRecognitionData['bank_type']       = $ai_bank_type;
                $aiRecognitionData['account_name']    = $ai_result['result']['account_name'];
                $aiRecognitionData['account_no']      = $ai_bank_no;
                $aiRecognitionData['staff_info_id']   = $staff_info_id;

                $updateData['staff_info_id']       = $staff_info_id;
                $updateData['ai_recognition_data'] = json_encode($aiRecognitionData, JSON_UNESCAPED_UNICODE);
                $updateData['type']                = StaffInfoAuditCheckModel::TYPE_BANK_CAR;

                $conditions = 'staff_info_id = :staff_info_id: and type = :type: ';
                $bind       = [
                    'staff_info_id' => $staff_info_id,
                    'type'          => StaffInfoAuditCheckModel::TYPE_BANK_CAR,
                ];

                $auditInfo = StaffInfoAuditCheckRepository::getStaffAuditCheckInfo('*', $conditions, $bind);
                if ($auditInfo) {
                    $db->updateAsDict("staff_info_audit_check", $updateData,
                        ["conditions" => "id='{$auditInfo['id']}'"]);
                } else {
                    $db->insertAsDict("staff_info_audit_check", $updateData);
                }

            }
            $returnData = [
                'code' => 1,
                'msg'  => $msg,
                'data' => [
                    'is_submit'      => $is_submit, // 是否可提交
                    'ai_audit_state' => $ai_audit_state // ai审核状态 1通过 2未通过
                ],
            ];

            //记录审核日志
            $audit_log        = [
                'staff_info_id'      => $staff_info_id,
                'audit_id'           => 10000,
                'audit_name'         => 'AI',
                'audit_before_state' => 0,
                'audit_after_state'  => $ai_audit_state,
                'reject_reason'      => '',
                'type'               => 2  // 1 表示身份证日志，2 表示银行卡日志
            ];
            $audit_log_result = $db->insertAsDict('staff_identity_annex_audit_log', $audit_log);
            if (!$audit_log_result) {
                $msg = "staff_identity_annex_audit_log error fail:".var_export($audit_log, true).PHP_EOL;
                $this->getDI()->get("logger")->write_log($msg, 'info');
            }
            $db->commit();

            return $this->checkReturn($returnData);
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('ai_bank_card_ocr_post --- '.$e->getMessage().' ----- '.$e->getLine().' ----- '.$e->getFile());
            return $this->checkReturn(-3, 'error');
        }
    }

    public function ai_bank_card_examine($offset,$limit)
    {
        $identity_annex_list = HrStaffIdentityAnnexModel::find([
            'conditions' => "bank_card_photo != '' and bank_card_audit_state = 0",
            'offset'=>$offset,
            'limit'=>$limit,
        ]);
        $data                = $identity_annex_list->toArray();
        $PersonInfoServer    = (new PersoninfoServer('th', $this->getDI()['config']['application']['timeZone']));
        $error_staff_info_id = [];
        $pids                = [];
        foreach ($data as $k => $v) {
            // 加个缓存避免重复跑的时候频繁调用ai
            $redisKey = 'ai_bank_card_examine_8:'.md5($v['bank_card_photo'].$v['staff_info_id']);
            $cache    = $this->getDI()->get('redisLib');
            if ($returnData = $cache->get($redisKey)) {
//                        $ai_res = json_decode($returnData, true);
                continue;
            } else {
                $ai_res = $PersonInfoServer->ai_bank_card($v['bank_card_photo'], $v['staff_info_id']);
                $cache->set($redisKey, json_encode($ai_res), 86400);
            }

            if ($ai_res["code"] == 1 && $ai_res["data"]["ai_audit_state"] == 1) {
                $bank_card_audit_state = 1;
                $ai_audit_state        = 1;
            } elseif ($ai_res["code"] == 1 && $ai_res["data"]["ai_audit_state"] == 2) {
                $bank_card_audit_state = 0;
                $ai_audit_state        = 2;
            } else {
                continue;
            }
            // 审核通过
            $new_date = date('Y-m-d H:i:s');
            $sql = "update hr_staff_identity_annex set bank_card_audit_state = ".$bank_card_audit_state.",ai_audit_state=".$ai_audit_state.",audit_staff_info_id =10000,audit_state_date='".$new_date."',ai_audit_state_date='".$new_date."',reject_reason='' where id = ".$v["id"];
            $res = $this->getDI()->get('db')->query($sql);
            if (!$res) {
                //保存失败
                $error_staff_info_id[] = $v['staff_info_id'];
                continue;
            }
        }

        return [$data, $error_staff_info_id];
    }

    //获取员工基本信息 和薪资构成信息 在职证明 预览接口哟过
    public function on_job_need($param){
        //个人信息
        $staff_model = new StaffRepository($this->lang);
        $info = $staff_model->getStaffPosition($param['staff_id']);
        if(empty($info))
            return false;

        $PostData['fbid'] = $param['staff_id'];
        $apiClient = new ApiClient('hr_rpc', '', 'staffs-salary', $this->lang);
        $apiClient->setParams($PostData);
        $result = $apiClient->execute();
        if ($result['code'] == 1) {
            $salary_data = $result['result'];
        }
        $return = array();
        if(isset($salary_data) && $salary_data['code'] === 0 && !empty($salary_data['body']))
            $return['base_salary'] = $salary_data['body']['base_salary'];

        //[1]入参
        $staff_id = $param['staff_id'];
        $ac = new ApiClient('hcm_rpc', '', 'get_staff_certificate_info' ,$this->lang);
        $ac->setParams([
            "staff_info_id" => $staff_id,
        ]);
        $ac_result = $ac->execute();
        if($ac_result["result"]['code'] == 1) {
            //整理数据
            $data = $ac_result["result"]['data'];

        } else {
            //记日志 获取薪资信息失败u
            $this->logger->write_log("get_staff_certificate_info_failed : {$staff_id}", 'notice');
        }

        $return['name'] = $info['name'];
        $return['name_en'] = $info['name_en'];

        $return['hire_date'] = $info['hire_date'];
        $return['job_name'] = $info['job_name'];
        $return['email'] = $info['email'];
        $return['personal_email'] = $info['personal_email'];
        $return['depart_name'] = $info['depart_name'];

        //入职日期
        $return['hire_year_th'] = date('Y',strtotime($info['hire_date'])) + 543;
        $month_key = intval(date('m',strtotime($info['hire_date'])));
        $return['hire_month_th'] = StaffRepository::$month_th[$month_key];

        //日期格式改为日+月（文字）+佛历年份 ，如 4  มิถุนายน พ.ศ. 2563；
        $month = intval(date('m',time()));
        $return['send_date'] = intval(date('d',time())) . ' ' . StaffRepository::$month_th[$month] . ' ' . (date('Y',time()) + 543);

        // 公司信息
        $return['header_company_name']       = $data['header_company_name'] ?? '';
        $return['header_logo_url']           = $data['header_logo_url'] ?? '';
        $return['footer_content']            = $data['footer_content'] ?? '';
        $return['content_company_name']      = $data['content_company_name'] ?? '';
        $return['content_company_name_th']   = $data['content_company_name_th'] ?? '';
        $return['company_seal_url']          = $data['company_seal_url'] ?? '';
        $return['job_title_name_position']   = $data['job_title_name_position'] ?? '';
        $return['payroll_email']             = $data['payroll_email'] ?? '';
        $return['legal_person_name_en']      = $data['name_en'] ?? '';
        $return['legal_person_name_th']      = $data['name_th'] ?? '';
        $return['legal_person_job_title_en'] = $data['job_title_en'] ?? '';
        $return['legal_person_job_title_th'] = $data['job_title_th'] ?? '';
        $return['legal_person_sign_img_url'] = $data['sign_img_url'] ?? '';
        $return['company_phone']             = $data['company_phone'] ?? '';

        return $return;
    }
}
