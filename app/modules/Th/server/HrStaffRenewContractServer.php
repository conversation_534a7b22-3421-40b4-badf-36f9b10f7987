<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffRenewContractApplyModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Modules\Th\library\Enums\CommonEnums;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\HrStaffRenewContractServer as GlobalBaseServer;
use Exception;
use FlashExpress\bi\App\Server\StaffServer;

class HrStaffRenewContractServer extends GlobalBaseServer
{
    /**
     * 获取需要续签的合同员工列表
     * @param $params
     * @return array
     */
    public function getStaffRenewContractList()
    {
        $startDate = $params['date'] ?? date('Y-m-d', strtotime('+40 days'));
        $endDate   = $params['date'] ?? date('Y-m-d', strtotime('+60 days'));

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('
            hr_staff_contract.id as contract_id,
            hr_staff_contract.contract_name,
            hr_staff_contract.contract_path,
            hr_staff_contract.contract_start_date,
            hr_staff_contract.contract_end_date,
            hr_staff_contract.contract_ld_no,
            hr_staff_info.staff_info_id,
            hr_staff_info.sys_store_id,
            hr_staff_info.name as staff_info_name,
            hr_staff_info.formal,
            hr_staff_info.hire_type,
            hr_staff_info.job_title,
            hr_staff_info.sys_store_id,
            hr_staff_info.node_department_id,
            hr_staff_info.manger
        ');
        $builder->from(['hr_staff_contract' => HrStaffContractModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hr_staff_contract.staff_id = hr_staff_info.staff_info_id ', 'hr_staff_info');
        $builder->leftJoin(HrStaffRenewContractApplyModel::class, 'hr_staff_contract.id = contract_apply.contract_id ', 'contract_apply');

        //固定期劳动合同
        $builder->andWhere('hr_staff_contract.contract_date_is_long = :contract_date_is_long:', ['contract_date_is_long' => HrStaffContractModel::CONTRACT_DATE_IS_LONG_NO]);

        //合同类型 1=>劳动合同
        $builder->andWhere('hr_staff_contract.contract_type = :contract_type:', ['contract_type' => HrStaffContractModel::CONTRACT_TYPE_LABOR_CONTRACT]);
        //合同状态 80=>待续签
        $builder->andWhere('hr_staff_contract.contract_status = :contract_status:', ['contract_status' => HrStaffContractModel::CONTRACT_STATUS_TO_BE_RENEWED]);
        //合同删除状态 0=>未删除
        $builder->andWhere('hr_staff_contract.contract_is_deleted = :contract_is_deleted:', ['contract_is_deleted' => HrStaffContractModel::CONTRACT_DELETED_NO]);
        //合同截止日期
        $builder->andWhere('hr_staff_contract.contract_end_date >= :contract_start_date:', ['contract_start_date' => $startDate]);
        $builder->andWhere('hr_staff_contract.contract_end_date <= :contract_end_date:', ['contract_end_date' => $endDate]);

        $builder->andWhere('contract_apply.id IS NULL');
        //在职
        $builder->andWhere('hr_staff_info.state = :state:', ['state' => HrStaffInfoModel::STATE_1]);
        $builder->andWhere('hr_staff_info.hire_type != :hire_type:', ['hire_type' => HrStaffInfoModel::HIRE_TYPE_UN_PAID]);
        $builder->andWhere('hr_staff_info.wait_leave_state = :wait_leave_state:', ['wait_leave_state' => HrStaffInfoModel::WAITING_LEAVE_NO]);

        //职位判断
        $builder->andWhere('hr_staff_info.job_title IN ({job_title:array}) OR hr_staff_info.hire_type = :day_hire_type:',
            [
                'job_title'     => [
                    CommonEnums::JOB_TITLE_VAN_COURIER,
                    CommonEnums::JOB_TITLE_BIKE_COURIER,
                    CommonEnums::JOB_TITLE_BOAT_COURIER,
                    CommonEnums::JOB_TITLE_TRICYCLE_COURIER,
                    CommonEnums::JOB_TITLE_VAN_FEEDER,
                    CommonEnums::JOB_TITLE_COURIER_INSTALLATION_STAFF,
                    CommonEnums::JOB_TITLE_INSTALLATION_STAFF,
                    CommonEnums::JOB_TITLE_EV_VAN_COURIER,
                ],
                'day_hire_type' => HrStaffInfoModel::HIRE_TYPE_3,
            ]);

        //员工类型 formal 1=>正式员工
        $builder->andWhere('hr_staff_info.formal = :formal:', ['formal' => HrStaffInfoModel::FORMAL_1]);
        $builder->orderby('hr_staff_contract.id ASC');

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 查找需要发起续签的合同 给直线上级发送消息
     * @param $params
     * @return bool
     */
    public function sendRenewContractMessage()
    {
        $list = $this->getStaffRenewContractList();
        if(empty($list)) {
            //未找到需要续签的合同
            return false;
        }

        //查找上级信息
        $manager_ids = array_column($list, 'manger');
        $manager_info_list = (new HrStaffInfoServer())->getUserInfoByStaffInfoIds($manager_ids, 'staff_info_id,state');
        $manager_info_list = array_column($manager_info_list, null, 'staff_info_id');

        //获取上级管辖的片区信息
        $manager_piece_list = $this->getManagerPieceList($manager_ids);

        $db            = $this->getDI()->get('db');
        $sysStoreModel = new SysStoreModel();

        foreach ($list as $key => $value) {
            $this->logger->write_log('sendRenewContractMessage Start '.json_encode($value), 'info');

            $manager_id         = $value['manger'] ?? 0;
            $manager_info       = $manager_info_list[$manager_id] ?? [];
            $manager_piece_info = $manager_piece_list[$manager_id] ?? [];

            try {
                $db->begin();

                //查员工片区
                $myStoreInfo = $sysStoreModel->getOneStoreById($value['sys_store_id'] ?? '');

                //业务表创建数据
                $new_id = $this->createRenewContractApply([
                    'renew_staff_info_id' => $value['staff_info_id'],
                    'contract_id'         => $value['contract_id'],
                    'contract_name'       => $value['contract_name'],
                    'contract_start_date' => $value['contract_start_date'],
                    'contract_end_date'   => $value['contract_end_date'],
                    'contract_ld_no'      => $value['contract_ld_no'],
                    'manager_id'          => $manager_id,
                ]);

                if(empty($new_id)) {
                    throw new BusinessException('sendRenewContractMessageError hr_staff_renew_contract_apply SaveError');
                }

                //1.直属上级如果为离职或空，不发送消息，直接发起续签审批
                //2.如果直线上级为片区负责人，不发送消息，直接发起续签审批
                if (
                    empty($manager_info)
                    || ($manager_info['state'] == HrStaffInfoModel::STATE_RESIGN)
                    || (!empty($myStoreInfo['manage_piece']) && in_array($myStoreInfo['manage_piece'],$manager_piece_info))
                ) {
                    $this->logger->write_log([
                        'function'      => 'sendRenewContractMessage',
                        'message'       => '直属上级如果为离职或空或者为片区负责人，不发送消息，直接发起续签审批',
                        'staff_info_id' => $value['staff_info_id'],
                        'contract_id'   => $value['contract_id'],
                        'manager_info'  => $manager_info,
                        'manager_piece_info' => $manager_piece_info,
                    ], 'info');

                    //创建审批
                    $this->createRenewContractAudit(['audit_id' => $new_id, 'renew_staff_info_id' => $value['staff_info_id']]);
                } else {
                    $msg_id = time() . $manager_id . rand(1000000, 9999999);

                    //修改msg编号
                    if (!$this->updateRenewContractApplyById($new_id,['msg_id' => $msg_id])) {
                        throw new BusinessException('sendRenewContractMessageError sendProposeMessage saveError');
                    }

                    //发送消息
                    $res = $this->sendProposeMessage([
                        'staff_info_id' => $manager_id,
                        'message_title' => 'renew_contract_add_proposal_message_title',
                        'audit_id'      => $new_id,
                        'msg_id'        => $msg_id,
                    ]);

                    if ($res['result']['code'] != 1) {
                        throw new BusinessException('sendRenewContractMessageError sendProposeMessage sendError');
                    }
                }
                if (!$db->commit()) {
                    throw new Exception('sendRenewContractMessageError commitError');
                }
            } catch (Exception $e) {
                $db->rollback();
                $this->logger->write_log('sendRenewContractMessage error'.$e->getMessage().'-'.json_encode($value));
                continue;
            }
        }

        return true;
    }

    /**
     * 根据合同id查找合同续签审批
     * @param $contract_ids
     * @return array
     */
    public function getRenewContractApplyListByContractIds($contract_ids): array
    {
        if (empty($contract_ids)) {
            return [];
        }
        return HrStaffRenewContractApplyModel::find([
            'conditions' => "contract_id in ({contract_ids:array})",
            'bind'       => [
                'contract_ids' => $contract_ids,
            ],
        ])->toArray();
    }

    /**
     * 获取工号管辖片区
     * @param $manager_ids
     * @return array
     */
    public function getManagerPieceList($manager_ids): array
    {
        $list = SysManagePieceModel::find([
            'conditions' => 'manager_id in ({manager_ids:array}) and deleted = 0',
            'bind'       => [
                'manager_ids' => $manager_ids,
            ],
        ])->toArray();

        $return_data = [];

        foreach ($list as $key => $value) {
            $return_data[$value['manager_id']][] = $value['id'];
        }

        return $return_data;
    }

    /**
     * 发送续签合同意见收集消息
     * @param $params
     * @return array|bool|mixed|null
     */
    public function sendProposeMessage($params) {
        $staff_info_id = $params['staff_info_id'] ?? 0;
        $message_title = $params['message_title'] ?? '';
        $audit_id      = $params['audit_id'] ?? 0;
        $msg_id        = $params['msg_id'] ?? 0;

        $staff_server = new StaffServer();
        $lang = $staff_server->getLanguage($staff_info_id);

        $message_content = $audit_id;

        $t = $this->getTranslation($lang);

        $message_params['id']                 = $msg_id;
        $message_params['staff_users']        = [$staff_info_id];
        $message_params['message_title']      = $t->_($message_title);
        $message_params['message_content']    = $message_content;
        $message_params['staff_info_ids_str'] = $staff_info_id;
        $message_params['category']           = MessageEnums::MESSAGE_CATEGORY_RENEW_CONTRACT_PROPOSE;
        $message_params['related_id']         = strtotime(date("Y-m-d 00:00:00", strtotime("+7 day")));

        $bi_rpc = new ApiClient('bi_rpc', '', 'add_kit_message', $lang);
        $bi_rpc->setParams($message_params);
        return $bi_rpc->execute();
    }

    /**
     * 审批详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array|mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $detail = HrStaffRenewContractApplyModel::findFirst([
            'conditions' => "id = :id:",
            'bind'       => ['id' => $auditId],
        ]);

        if (empty($detail)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        $result = $detail->toArray();

        //申请人信息
        $staffInfo = (new StaffServer())->getStaffInfoById($result['renew_staff_info_id']);

        $detailLists = [
            'staff_info_id'     => $result['renew_staff_info_id'], //工号
            'staff_name'        => $staffInfo['staff_name'],       //姓名
            'position_name'     => $staffInfo['job_name'],         //职位名称
            'department'        => $staffInfo['department_name'],  //所属部门
            'staff_store'       => $staffInfo['store_name'],       //所属网点
            'contract_end_date' => $result['contract_end_date'],   //合同到期时间
        ];

        $returnData['data']['detail'] = $this->format($detailLists);

        $auditRepo = new AuditlistRepository($this->lang, $this->timezone);
        $data      = [
            'title'       => $auditRepo->getAudityType(AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT),
            'id'          => $result['id'],
            'staff_id'    => $result['renew_staff_info_id'],
            'type'        => AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT,
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['status'],
            'status_text' => $auditRepo->getAuditStatus('10' . $result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        //直线上级续签建议
        $proposal_list = [
            'contract_renew_proposal' => !empty($result['proposal'])
                ? $this->getTranslation()->_('contract_renew_proposal_' . $result['proposal'])
                : $this->getTranslation()->_('contract_renew_proposal_empty'), //续签建议
            'contract_renew_proposal_reason' => $result['proposal_reason'], //原因
        ];

        $returnData['data']['manager_proposal'] = $this->format($proposal_list);

        if($comeFrom == 1) {
            $data['options'] = (object)[];
        }

        $returnData['data']['head'] = $data;

        return $returnData;
    }

    /**
     * 审批流回调属性
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        $detail = HrStaffRenewContractApplyModel::findFirst([
            'conditions' => "id = :id:",
            'bind'       => ['id' => $auditId],
        ]);

        if (empty($detail->first_approval_id)) {
            $approval_ids              = $this->getFirstApproval($auditId);
            $detail->first_approval_id = $approval_ids;
            $detail->save();
        }

        if ($isFinal) {
            $detail->status = $state;
            $detail->save();

            //同意
            if ($state == enums::$audit_status['approved']) {
                //系统自动发送给员工 发送消息调用接口
                $data = ['contract_id' => $detail->contract_id];
                $rmq  = new RocketMQ('hr-contract-add');
                $rid  = $rmq->sendToMsg($data,10);
                $this->getDI()->get('logger')->write_log([
                    'function' => 'HrStaffRenewContractServer-setProperty',
                    'rid'      => $rid,
                    'auditId'  => $auditId,
                    'state'    => $state,
                ], 'info');
            }
        }
    }

    /**
     * 添加续签建议
     * @param $params
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addProposal($params): array
    {
        $audit_id        = $params['audit_id'];
        $proposal        = $params['proposal'];
        $proposal_reason = $params['proposal_reason'];
        $staff_info_id   = $params['staff_id'];

        $db        = $this->getDI()->get('db');
        $db_coupon = $this->getDI()->get('db_coupon');

        try {
            $db->begin();
            $db_coupon->begin();

            $detail = HrStaffRenewContractApplyModel::findFirst([
                'conditions' => "id = :id: and manager_id = :manager_id:",
                'bind'       => ['id' => $audit_id, 'manager_id' => $staff_info_id],
            ]);

            if(empty($detail)) {
                throw new BusinessException($this->getTranslation()->_('renew_contract_add_proposal_error_1')); //未找到续签信息
            }

            if(!empty($detail->proposal) || !empty($detail->proposal_reason)) {
                throw new BusinessException($this->getTranslation()->_('renew_contract_add_proposal_error_3')); //已经填写过续签建议
            }

            $detail->proposal = $proposal;
            $detail->proposal_reason = $proposal_reason;

            if(!$detail->save()) {
                throw new BusinessException($this->getTranslation()->_('renew_contract_add_proposal_error_4'));//续签建议保存失败
            }

            //创建审批流
            if (!$detail->status) {
                $this->createRenewContractAudit([
                    'audit_id' => $audit_id,
                    'renew_staff_info_id' => $detail->renew_staff_info_id
                ]);
            }

            //查询消息
            $message_courier = MessageCourierModel::findFirst([
                'conditions' => "id = :id:",
                'bind' => ['id' => $detail->msg_id],
            ]);


            if ($message_courier) {
                $message_courier->read_state = MessageCourierModel::READ_STATE_HAS_READ;
                $message_courier->save();
            }

            $db_coupon->commit();
            $db->commit();
        } catch (ValidationException|BusinessException $e) {
            $db->rollback();
            $db_coupon->rollback();
            return $this->checkReturn(-3, $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();
            $db_coupon->rollback();
            throw $e;
        }

        return $this->checkReturn([]);
    }

    /**
     * 获取续签详情
     * @param $params
     * @return array
     */
    public function getStaffRenewMessageContent($params): array
    {
        $audit_id = $params['audit_id'];
        $detail = HrStaffRenewContractApplyModel::findFirst([
            'conditions' => "id = :id:",
            'bind'       => ['id' => $audit_id],
        ]);

        if (!$detail) {
            throw new BusinessException($this->getTranslation()->_('contract_data_does_not_exist'));
        }

        $detail = $detail->toArray();

        //申请人信息
        $staff_info = HrStaffInfoModel::getOneByStaffId($detail['renew_staff_info_id']);

        return [
            'is_submit'         => $detail['proposal'] ? 1 : 0,
            'staff_info_id'     => $detail['renew_staff_info_id'],
            'staff_name'        => $staff_info['name'] ?? '',
            'contract_end_date' => $detail['contract_end_date'],
        ];
    }

    /**
     * 创建续签合同业务数据
     * @param $params
     * @return int
     */
    public function createRenewContractApply($params): int
    {
        $renew_staff_info_id = $params['renew_staff_info_id'];    //续签人id
        $contract_id         = $params['contract_id'];            //合同id
        $contract_name       = $params['contract_name'];          //合同名称
        $contract_start_date = $params['contract_start_date'];    //合同开始时间
        $contract_end_date   = $params['contract_end_date'];      //合同结束时间
        $contract_ld_no      = $params['contract_ld_no'];         //合同编号
        $manager_id          = $params['manager_id'];             //上级id

        $model = new HrStaffRenewContractApplyModel();
        $model->renew_staff_info_id = $renew_staff_info_id;
        $model->serial_no           = 'RC' . $this->getRandomId();
        $model->contract_id         = $contract_id;
        $model->contract_name       = $contract_name;
        $model->contract_start_date = $contract_start_date;
        $model->contract_end_date   = $contract_end_date;
        $model->contract_ld_no      = $contract_ld_no;
        $model->manager_id          = $manager_id;

        $result  = $model->create();

        return $result ? $model->id : 0;
    }

    /**
     * 更新续签合同业务数据
     * @param $params
     * @return int
     */
    public function updateRenewContractApplyById($id,$parmas)
    {
        if (empty($parmas)) {
            return false;
        }

        $hrStaffRenewContractApplyModel = HrStaffRenewContractApplyModel::findFirst([
            'conditions' => "id = :id:",
            'bind'       => ['id' => $id],
        ]);

        if (!$hrStaffRenewContractApplyModel) {
            return false;
        }

        if (!empty($parmas['msg_id'])) {
            $hrStaffRenewContractApplyModel->msg_id = $parmas['msg_id'];
        }

        if (isset($parmas['status'])) {
            $hrStaffRenewContractApplyModel->status = $parmas['status'];
        }

        return $hrStaffRenewContractApplyModel->save();
    }

    /**
     * 创建续签合同审批流
     * @param $params
     * @return bool
     * @throws BusinessException
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function createRenewContractAudit($params): bool
    {
        $auditId = $params['audit_id'];                         //业务id
        $renew_staff_info_id = $params['renew_staff_info_id'];  //续签人id

        //调整业务表状态
        $res = $this->updateRenewContractApplyById($params['audit_id'],[
            'status' => enums::$audit_status['panding']
        ]);

        if (!$res) {
            throw new BusinessException($this->getTranslation()->_('contract_create_workflow_error'));
        }

        //创建审批
        $server    = new ApprovalServer($this->lang, $this->timezone);
        $requestId = $server->create($auditId, AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT, $renew_staff_info_id);
        if (!$requestId) {
            //审批流创建失败
            throw new BusinessException($this->getTranslation()->_('contract_create_workflow_error'));
        }

        return true;
    }

    /**
     * 查找需要发起续签的合同 给直线上级发送消息
     * @param $params
     * @return bool
     */
    public function sendAutoContract($date)
    {
        //发起续签审批
        $hrStaffRenewContractApplyList = HrStaffRenewContractApplyModel::find([
            'conditions' => "status = :status: and proposal = :proposal: and is_auto_cancel = :is_auto_cancel: and created_at < :date:",
            'bind'       => [
                'status'         => HrStaffRenewContractApplyModel::STATUS_DEFAULT,
                'proposal'       => HrStaffRenewContractApplyModel::PROPOSAL_DEFAULT,
                'is_auto_cancel' => HrStaffRenewContractApplyModel::IS_AUTO_CANCEL_NO,
                'date'           => $date,
            ],
        ])->toArray();

        if (empty($hrStaffRenewContractApplyList)) {
            return false;
        }

        $this->getDI()->get('logger')->write_log('sendAutoContract list '.json_encode($hrStaffRenewContractApplyList), 'info');

        $db = $this->getDI()->get('db');

        foreach ($hrStaffRenewContractApplyList as $v) {

            try {
                $db->begin();

                //创建审批
                $this->createRenewContractAudit(['audit_id' => $v['id'], 'renew_staff_info_id' => $v['renew_staff_info_id']]);

                if (!$db->commit()) {
                    throw new Exception('sendAutoContract Send Error');
                }
            } catch (Exception $e) {
                $db->rollback();
                $this->getDI()->get('logger')->write_log('sendAutoContract error '.$e->getMessage().' '.json_encode($v));
                continue;
            }
        }

        return true;
    }

    /**
     * 撤销审批
     * @param $params
     * @return array
     * @throws Exception
     */
    public function winHrRevokeApply($params): array
    {
        $this->getDI()->get('logger')->write_log('winHrRevokeApply start' .json_encode($params), 'info');

        $userinfo      = $params['userinfo'];
        $contract_id   = $params['contract_id'];
        $revoke_remark = $params['revoke_remark'];

        $detail = HrStaffRenewContractApplyModel::findFirst([
            'conditions' => "contract_id = :contract_id:",
            'bind'       => ['contract_id' => $contract_id],
        ]);

        if (empty($detail)) {
            $this->getDI()->get('logger')->write_log([
                'function' => 'winHrRevokeApply',
                'message'  => '未找到指定合同审批数据',
                'params'   => $params,
            ], 'notice');
            return $this->checkReturn([]);
        }

        $detail->is_auto_cancel = HrStaffRenewContractApplyModel::IS_AUTO_CANCEL_YES;

        if (!$detail->save()) {
            //reids队列执行，不返回直接执行，记录错误就可以
            $this->getDI()->get('logger')->write_log('winHrRevokeApply is_auto_cancel_error '. json_encode($params));
        }

        //删除消息
        if ($detail->msg_id) {
            //查询消息
            $message_courier = MessageCourierModel::findFirst([
                'conditions' => "id = :id:",
                'bind' => ['id' => $detail->msg_id],
            ]);

            if ($message_courier) {
                $message_courier->is_del = MessageCourierModel::IS_DELETED_YES;
                $message_courier->save();
            }
        }

        if ($detail->status) {
            //撤销
            $server = new ApprovalServer($this->lang, $this->timezone);
            $extend['super'] = 1;
            $server->cancel($detail->id, AuditListEnums::APPROVAL_TYPE_RENEW_CONTRACT, $revoke_remark, $userinfo['id'], $extend);
            $this->getDI()->get('logger')->write_log('winHrRevokeApply success' .json_encode($params), 'info');
        }

        return $this->checkReturn([]);
    }
}

