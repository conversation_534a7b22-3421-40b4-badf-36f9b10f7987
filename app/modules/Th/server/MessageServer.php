<?php
/**
 * Author: Bruce
 * Date  : 2023-02-23 00:18
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Models\backyard\MessageWarningModel;
use FlashExpress\bi\App\Models\backyard\MessageWarningTransferSignModel;
use FlashExpress\bi\App\Server\MessageServer AS GlobalBaseServer;
use Phalcon\Db;


class MessageServer extends GlobalBaseServer
{
    public function getWarningMessage($staffId, $msgId)
    {
        $warningInfo = $this->getWarningMessageFromQueryList($staffId, $msgId);

        //是否需要签字
        $is_need_sign = true;
        $is_overtime = false;
        //兼容历史查询警告书信息,通过这里找到的警告书，都不需要签字
        if(!$warningInfo) {
            $is_need_sign = false;
            $signHistory = $this->getWarningTransferInfo($staffId, $msgId);
            if(empty($signHistory)) {
                $this->getDI()->get("logger")->write_log("warning_message 警告书获取失败，移交记录中匹配不到对应警告书 " . json_encode([$staffId, $msgId], JSON_UNESCAPED_UNICODE), "notice");
                return [];
            }

            $warningInfo = MessageWarningModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $signHistory['message_warning_id']],
            ]);
            if(empty($warningInfo)) {
                $this->getDI()->get("logger")->write_log("warning_message 警告书获取失败，匹配不到警告书 " . json_encode([$staffId, $msgId], JSON_UNESCAPED_UNICODE), "error");
                return [];
            }
            $warningInfo = $warningInfo->toArray();

            //抄送消息，只阅读不签字。
            if($signHistory['type']  == MessageWarningTransferSignModel::TRANSFER_TYPE_CC) {
                $warningInfo['is_need_sign'] = $is_need_sign;
                $warningInfo['is_overtime'] = $is_overtime;
                return $warningInfo;
            }

            if($signHistory['type']  == MessageWarningTransferSignModel::TRANSFER_TYPE_SUPERIOR) {
                $warningInfo['role'] = self::MSG_STAFF_TYPE_SUPERIOR;
            } else {
                $warningInfo['role'] = self::MSG_STAFF_TYPE_WITNESS;
            }
            $is_overtime = true;
            $warningInfo['is_need_sign'] = $is_need_sign;
            $warningInfo['is_overtime'] = $is_overtime;
            return $warningInfo;
        }
        $warningInfo['is_overtime'] = $is_overtime;//默认不超时

        if ($warningInfo['staff_info_id'] == $staffId && $warningInfo['kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_STAFF;

        } else if ($warningInfo['superior_id'] == $staffId && $warningInfo['superior_kit_id'] == $msgId) {
            $signHistory = $this->getWarningTransferInfo($staffId, $msgId);
            //上级位置，转交人数大于1 肯定是超时，当前上级位置的人
            $transferNumber = MessageWarningTransferSignModel::count([
                'conditions' => 'message_warning_id=:message_warning_id: AND type = :type:',
                'bind' => ['message_warning_id' => $warningInfo['id'], 'type' => MessageWarningTransferSignModel::TRANSFER_TYPE_SUPERIOR],
            ]);
            //如果大于1 则超时
            if($transferNumber > 1) {
                $is_overtime = true;
            }
            $warningInfo['is_overtime'] = $is_overtime;
            $warningInfo['role'] = self::MSG_STAFF_TYPE_SUPERIOR;
            //获取阅读消息的身份：上级，上上级，bp
            $warningInfo['level'] = !empty($signHistory) && $signHistory['type'] == MessageWarningTransferSignModel::TRANSFER_TYPE_SUPERIOR ? $signHistory['level'] :  MessageWarningTransferSignModel::TYPE_SUPERIOR_LEVEL_SUPERIOR;

        } else if ($warningInfo['witness1_id'] == $staffId && $warningInfo['witness1_kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_WITNESS_1;

        } else if ($warningInfo['witness2_id'] == $staffId && $warningInfo['witness2_kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_WITNESS_2;

        } else if ($warningInfo['operator_id'] == $staffId && $warningInfo['operator_kit_id'] == $msgId) {
            $warningInfo['role'] = self::MSG_STAFF_TYPE_OPERATOR;
        }
        $warningInfo['is_need_sign'] = $is_need_sign;


        return $warningInfo;
    }
}