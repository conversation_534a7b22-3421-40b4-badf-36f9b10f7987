<?php
/**
 * Author: Bruce
 * Date  : 2023-05-25 21:16
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Th\Server;

use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeOrderModel;
use FlashExpress\bi\App\Modules\Th\library\Enums\HrJobTitleEnums;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\HubOutsourcingOvertimeRepository;
use FlashExpress\bi\App\Repository\OsStaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditOptionRule;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\OsStaffServer;
use FlashExpress\bi\App\Server\OutsourcingOTServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SysStoreServer;

class OutsourcingOTServer extends GlobalBaseServer
{

    /**
     * 获取外协订单中员工数据
     * @param array $params
     * @param array $user_info
     * @return array
     */
    public function getOutsourcingStaff(array $params, array $user_info): array
    {
        $store_list = $this->getStoreListByLoginId($user_info);
        $params['store_ids'] = array_column($store_list,'store_id');
        return $this->getOutsourcingOrder($params);
    }

    /**
     * 用于获取审批详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        // TODO: Implement getDetail() method.
        // 查询外协加班业务表
        $outsourcing_ot_info = (new HubOutsourcingOvertimeRepository($this->timeZone))->getOutsourcingOTById($auditId);
        // 查询网点
        $store_info = (new SysStoreServer($this->lang,
            $this->timeZone))->getStoreInfoByid($outsourcing_ot_info['store_id']);
        // 查询申请人
        $apply_staff_info = HrStaffInfoServer::getUserInfoByStaffInfoId($outsourcing_ot_info['apply_staff_id'],
            "id,staff_info_id,name")->toArray();

        // 格式化时间段
        $b_time = date('H:i', strtotime($outsourcing_ot_info['start_time']));
        $e_time = date('H:i', strtotime($outsourcing_ot_info['end_time']));

        //查找班次
        $shiftInfo  = (new OsStaffRepository($this->timeZone))->getWorkShift($outsourcing_ot_info['shift_id']);
        $os_ot_shift = '';
        if (!empty($shiftInfo)) {
            $os_ot_shift = $shiftInfo['start'] . ' - ' . $shiftInfo['end'];
        }

        // 详情页面的详细信息
        $detailLists = [
            // 申请人信息
            'os_ot_sponsor'    => $apply_staff_info['name'].'('.$apply_staff_info['staff_info_id'].')',
            // 加班网点
            'os_ot_store'      => !empty($store_info['name']) ? trim($store_info['name']) : '',
            // 加班日期
            'os_ot_date'       => $outsourcing_ot_info['ot_date'],
            // 加班时间
            'os_ot_time'       => $b_time.' - '.$e_time,
            // 加班班次
            'os_ot_shift'      => $os_ot_shift,
            // 加班人数
            'os_ot_demand_num' => $outsourcing_ot_info['demand_num'],
            // 加班证明
            'os_ot_img'        => json_decode($outsourcing_ot_info['img']),
        ];

        $request = AuditApplyModel::findFirst([
            'conditions' => "biz_value = :value: and biz_type = :type:",
            'bind'       => [
                'type'  => enums::$audit_type['OS_OT'],
                'value' => $auditId,
            ],
        ]);
        // 如果为驳回状态 则追加驳回原因
        if ($outsourcing_ot_info['apply_state'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason1' => $request->getRejectReason() ?? '',]);
        }

        // 拼组数据 + 翻译
        $detailLists                  = $this->format($detailLists);
        $returnData['data']['detail'] = $detailLists;

        $add_hour = $this->config->application->add_hour;
        // 组织header信息
        $data = [
            'title'      => (new AuditlistRepository($this->lang,
                $this->timeZone))->getAudityType(enums::$audit_type['OS_OT']),
            'id'         => $outsourcing_ot_info['id'],
            'staff_id'   => $outsourcing_ot_info['apply_staff_id'],
            'type'       => enums::$audit_type['OS_OT'],
            'created_at' => date('Y-m-d H:i:s',strtotime("{$request->created_at}") +( $add_hour * 3600)),//$outsourcing_ot_info['created_at'],
            'updated_at' => date('Y-m-d H:i:s',strtotime("{$request->updated_at}") +( $add_hour * 3600)), //$outsourcing_ot_info['updated_at'],
            'status'     => $outsourcing_ot_info['apply_state'],
            'serial_no'  => $outsourcing_ot_info['serial_no'],
        ];

        $returnData['data']['head'] = $data;

        return $returnData;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(true, false, false, false, false, false);
    }


    /**
     * by申请页基本数据
     * @param array $userInfo
     * @return array
     */
    public function getOutsourcingOTPre(array $userInfo): array
    {
        $data = [];
        // 登入者所属网点
        $data['login_store_info'] = $this->affiliatedStore($userInfo);
        // 网点信息
        $data['store_list'] = $this->getStoreListByLoginId($userInfo);
        // 加班时长
        $data['duration'] = $this->getDuration();
        // 外协公司信息
        $data['os_company_list'] = (new OsStaffServer($this->lang, $this->timeZone))->getOutCompanyInfo();
        // 外协加班数据
        $params['job_id'] = [
            enums::$job_title['outsource'],
            HrJobTitleEnums::JOB_TITLE_OUTSOURCE_PARCEL_UNLOADER,
            HrJobTitleEnums::JOB_TITLE_OUTSOURCE_WAREHOUSE_SORTER,
        ];
        $data['order_shift_list'] = $this->getOrderShiftList(array_column($data['store_list'],'store_id'),$params);
        return $data;
    }

    /**
     * 根据登入这id获取网点
     * @param array $userInfo
     * @return array|\Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getStoreListByLoginId(array $userInfo)
    {
        $osStaffServer = new OsStaffServer($this->lang, $this->timeZone);
        // 根据当前登入用户的id，查询他oa 组织架构上的负责的网点
        $store_list = $osStaffServer->getStaffManagePdcStore($userInfo['staff_id']);
        if (!empty($store_list)) {
            return $store_list;
        }
        $ffmOs = $osStaffServer->ffmPermission($userInfo);
        if ($ffmOs) {
            return $osStaffServer->getFFMStoreList();
        }

        // 产品要求：如果没有查询到数据，返回所属网点
        if (1 == $userInfo['organization_type']) {
            $store_list[0]['store_id']   = $userInfo['organization_id'];
            $store_list[0]['store_name'] = $userInfo['organization_name'];
            return $store_list;
        }
        return $store_list;
    }
}