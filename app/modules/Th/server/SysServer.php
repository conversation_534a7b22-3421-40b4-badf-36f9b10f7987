<?php


namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Models\backyard\HcHireTypeBranchModel;
use FlashExpress\bi\App\Models\backyard\HcHireTypePositionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\HcHireTypeConfigServer;
use FlashExpress\bi\App\Server\SysServer as GlobalBaseServer;


class SysServer extends GlobalBaseServer
{
    /**
     * @description 获取雇佣类型
     * @return array[]
     */
    public function getHireTypesList($job_title = 0, $store_id = '',$jd_id = 0): array
    {
        $ids = $this->getHireTypesIds($job_title, $store_id);

        $returnData = [];
        foreach ($ids as $k => $v) {
            $returnData[$k]['key']   = intval($v);
            $returnData[$k]['value'] = $this->getTranslation()->_('hire_type_' . $v);
        }
        return array_values($returnData);
    }

    /**
     * 获取国家支持的雇佣类型
     * @param int $job_title
     * @param string $store_id
     * @return array
     */
    public function getHireTypesIds($job_title , $store_id): array
    {
        $returnData = [
            HrStaffInfoModel::HIRE_TYPE_1,
            HrStaffInfoModel::HIRE_TYPE_5,
        ];

        if (!$job_title) {
            return $returnData;
        }
        //1. 若填写的【职位】【工作地点】属于HCM-Winhr设置-HC雇佣类型管理-按网点Tab生效中配置项的职位、工作地点，则【雇佣类型】枚举值为命中配置项对应的雇佣类型的并集
        $hireTypeBranch = (new HcHireTypeConfigServer($this->lang, $this->timezone))->getBranchHireTypeList($job_title,
            $store_id);
        if (!empty($hireTypeBranch)) {
            return $hireTypeBranch;
        }
        //2. 否则，若填写的职位属于HCM-Winhr设置-HC雇佣类型管理-按职位Tab生效中配置项的职位，则【雇佣类型】枚举值为命中配置项对应的雇佣类型的并集
        $hireTypePosition = (new HcHireTypeConfigServer($this->lang, $this->timezone))->getPositionHireTypeList($job_title);
        if (!empty($hireTypePosition)) {
            return $hireTypePosition;
        }
        //3. 否则，【雇佣类型】枚举值为：正式员工、实习生
        return $returnData;
    }
}