<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\BusinessTripEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\WorkflowEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\BusinessTripEditModel;
use FlashExpress\bi\App\Models\backyard\BusinessTripImgModel;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\WorkFromHomeApplyModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StoreRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\AuditListServer;
use FlashExpress\bi\App\Server\AuditOptionRule;
use FlashExpress\bi\App\Server\BusinesstripServer as GlobalServer;

use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysServer;
use FlashExpress\bi\App\Server\SysStoreServer;

class BusinesstripServer extends GlobalServer
{

    protected $trip;
    public    $timezone;
    public    $param;

    public $isShowEdit = false;

    /**
     * 创建订单
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addTrip($paramIn = [], $userinfo)
    {
        $this->param = $paramIn;
        $this->businessCheck($userinfo);

        //格式化订单数据
        $serialNo = $this->getRandomId();

        $tripData['destination_type']           = $paramIn['destination_type'] ?: 0;//出差理由 & 外出事由
        $tripData['destination_store']          = $paramIn['destination_store'] ?? "";//出差理由 & 外出事由
        $tripData['destination_store_distance'] = $this->getStoreDistance($userinfo,
            $tripData['destination_store']);//出差人距离最远的网点距离
        $tripData['destination_text']           = $paramIn['destination_text'] ?? "";//出差理由 & 外出事由
        $tripData['is_stay']                    = $paramIn['is_stay'] ?? "";//出差理由 & 外出事由

        $tripData['reason_application']       = $paramIn['reason_application'] ?? "";//出差理由 & 外出事由
        $tripData['traffic_tools']            = $paramIn['traffic_tools'] ?? 0;//交通工具 1飞机 2火车 3汽车 4其他
        $tripData['other_traffic_name']       = $paramIn['other_traffic_name'] ?? '';//其他交通工具
        $tripData['oneway_or_roundtrip']      = $paramIn['oneway_or_roundtrip'] ?? 1;//单程1往返2
        $tripData['departure_city']           = $paramIn['departure_city'] ?? '';//出发城市 历史数据 或者 境外出差会有 其余都存
        $tripData['destination_city']         = $paramIn['destination_city'] ?? '';//目的城市 & 外出地点
        $tripData['start_time']               = $paramIn['start_time'] ?? '';//开始时间
        $tripData['end_time']                 = $paramIn['end_time'] ?? '';//结束时间
        $tripData['days_num']                 = (strtotime($tripData['end_time']) - strtotime($tripData['start_time'])) / 3600 / 24 + 1;//出差天数
        $tripData['remark']                   = $paramIn['remark'] ?? '';//备注
        $tripData['status']                   = !empty($paramIn['is_hcm']) ? enums::$audit_status['approved'] : enums::$audit_status['panding'];//订单状态 工具过来的直接审核通过
        $tripData['apply_user']               = $userinfo['id'] ?? 1;//申请人
        $tripData['serial_no']                = !empty($serialNo) ? 'BT' . $serialNo : null;
        $tripData['business_trip_type']       = $paramIn['business_trip_type'] ?? BusinessTripModel::BTY_NORMAL;//1普通出差2黄牌项目出差
        $tripData['reason_application_type']  = $paramIn['reason_application_type'] ?? 0;//1办理黄牌2考驾照
        $tripData['car_no']                   = $paramIn['car_no'] ?? '';
        $tripData['destination_country']      = $paramIn['destination_country'] ?: 0; //目的地国家
        $tripData['destination_country_name'] = $paramIn['destination_country_name'] ?? '';  //目的地国家名称 选择其他时候需要输入
        //只有境外出差的时候才保存 目的地国家和国家名称
        if ($tripData['business_trip_type'] == BusinessTripModel::BTY_FOREIGN) {
            $tripData['destination_country']      = $paramIn['destination_country'];   //目的地国家
            $tripData['destination_country_name'] = $tripData['destination_country'] == HrStaffInfoModel::WORKING_COUNTRY_OTHER ? $paramIn['destination_country_name'] : '';        //目的地国家名称 选择其他的时候 才保存 国家名称
        } else {//境内出差
            $tripData['departure_city']        = '';//出发城市 历史数据 或者 境外出差会有 其余都存空
            $tripData['destination_city']      = '';//目的城市
            $tripData['departure_city_code']   = $paramIn['departure_city'];//城市code
            $tripData['destination_city_code'] = $paramIn['destination_city'];//城市code
        }
        $tripData['approval_type'] = BusinessTripModel::APPROVAL_TYPE_NORMAL;
        //出差原因 枚举 翻译key
        if ($tripData['destination_type'] == BusinessTripEnums::DESTINATION_TYPE_STORE) {
            $tripData['reason_enum'] = BusinessTripEnums::$store_reason[$paramIn['reason_enum']] ?? '';
        } else {
            $tripData['reason_enum'] = BusinessTripEnums::$other_reason[$paramIn['reason_enum']] ?? '';
        }
        $tripData['is_tool'] = empty($paramIn['is_hcm']) ? 0 : 1;//是否是工具过来的

        //黄牌 入口关闭了
        if ($tripData['business_trip_type'] == BusinessTripModel::BTY_YELLOW) {
            throw new ValidationException('business_trip_type error ');
        }

        $db = $this->getDI()->get("db");
        $db->begin();
        try {
            $db->insertAsDict('business_trip', $tripData);
            $lastInsertId = $db->lastInsertId();
            $tripImgData  = $paramIn['image_path'] ?? [];
            if (!empty($tripImgData) && !empty($lastInsertId)) {
                $imageData = [];
                foreach ($tripImgData as $k => $v) {
                    $imageData[$k]['business_trip_id'] = $lastInsertId;
                    $imageData[$k]['img_path']         = $v;
                }
                (new BaseRepository())->batch_insert('business_trip_img', $imageData);
            }
            $audit_type = $this->getAuditTypeByBTY((int)$tripData['business_trip_type']);
            //如果是工具申请出差 不走审批流 apply 加一条记录
            if (!empty($paramIn['is_hcm'])) {
                $flag = $this->saveApply($paramIn, $lastInsertId);
            } else {
                //泰国 黄牌关闭 只能是10 普通出差
                $flag = (new ApprovalServer($this->lang, $this->timezone))->create($lastInsertId, $audit_type,
                    $userinfo['id']);
            }

            if (!$flag) {
                $db->rollBack();
                throw new \Exception('创建审批流失败');
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log('add trip error ' . $e->getMessage() . ' ' . json_encode($tripData,
                    JSON_UNESCAPED_UNICODE));
            return $this->checkReturn(-3, $this->getTranslation()->_('2109'));
        }
        return $this->checkReturn(['data' => ['id' => $lastInsertId, 'bt_no' => $tripData['serial_no']]]);
    }

    public function saveApply($param, $auditId)
    {
        $insert['biz_type']            = AuditListEnums::APPROVAL_TYPE_BT;
        $insert['biz_value']           = $auditId;
        $insert['state']               = enums::$audit_status['approved'];
        $insert['submitter_id']        = $param['staff_info_id'];
        $insert['summary']             = json_encode($this->genSummary($auditId, $insert['submitter_id']),
            JSON_UNESCAPED_UNICODE);
        $insert['final_approver']      = $insert['submitter_id'];
        $insert['final_approval_time'] = date('Y-m-d H:i:s');
        $model                         = new AuditApplyModel();
        return $model->create($insert);
    }

    //修改出差
    public function editTrip($param)
    {
        $id       = (int)$param['id'];
        $tripInfo = BusinessTripModel::findFirst($id);
        if (empty($tripInfo)) {
            throw new ValidationException("trip info error");
        }
        //不是自己不能修改
        if ($tripInfo->apply_user != $param['staff_info_id']) {
            throw new ValidationException("no permission");
        }
        $checkRes = $this->checkEditButton($param, $tripInfo);
        if ($this->isShowEdit === false && !empty($checkRes)) {
            throw new ValidationException($checkRes);
        }

        //修改表记录 保存
        $insert['origin_id']      = $tripInfo->id;
        $insert['staff_info_id']  = $tripInfo->apply_user;
        $insert['old_is_stay']    = $tripInfo->is_stay;
        $insert['old_start_time'] = $tripInfo->start_time;
        $insert['old_end_time']   = $tripInfo->end_time;
        $insert['old_days_num']   = $tripInfo->days_num;
        $insert['new_is_stay']    = (int)$param['is_stay'];
        $insert['new_start_time'] = $param['start_time'];
        $insert['new_end_time']   = $param['end_time'];
        $insert['new_days_num']   = (strtotime($param['end_time']) - strtotime($param['start_time'])) / 3600 / 24 + 1;//出差天数
        $insert['edit_remark']    = $param['edit_remark'];
        $editModel                = new BusinessTripEditModel();

        $db = $this->getDI()->get("db");
        $db->begin();
        try {
            $editModel->create($insert);
            //更新 approval_type = 3
            $tripInfo->approval_type = BusinessTripModel::APPROVAL_TYPE_EDIT;
            $tripInfo->status        = enums::$audit_status['panding'];
            $tripInfo->created_at    = DateHelper::localToUtc();//创建时间 里面和外面要更新为 撤销审批申请时间
            $tripInfo->update();

            $audit_type = $this->getAuditTypeByBTY((int)$tripInfo['business_trip_type']);
            //泰国 黄牌关闭 只能是10 普通出差 $id, $audit_type, $tripInfo->apply_user
            $approvalParam['audit_id']     = $id;
            $approvalParam['audit_type']   = $audit_type;
            $approvalParam['user']         = $tripInfo->apply_user;
            $approvalParam['model']        = WorkflowEnums::WORKFLOW_CREATE_MODEL_EDIT;
            $approvalParam['summary_data'] = $this->getEditSummary($tripInfo, $insert);

            $flag = (new ApprovalServer($this->lang, $this->timezone))->createEx($approvalParam);

            if (!$flag) {
                $db->rollBack();
                throw new \Exception('创建审批流失败');
            }
            $db->commit();
            return $this->checkReturn([]);
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log('edit trip error ' . $e->getMessage() . ' ' . json_encode($insert,
                    JSON_UNESCAPED_UNICODE));
            return $this->checkReturn(-3, $this->getTranslation()->_('2109'));
        }
    }

    /**
     * @param $userInfo java 缓存信息
     * @throws ValidationException
     */
    public function businessCheck($userInfo)
    {
        $t = $this->getTranslation();
        //开始时间大于结束时间
        if ($this->param['start_time'] > $this->param['end_time']) {
            throw new ValidationException($t->_('7125'));
        }

        //开始时间 限制 默认7天内 今天是12月10日，配置7，则可选12月3日及之后的日期，之前日期置灰不可选
        if (empty($this->param['is_hcm'])) {
            $settingEnv  = new SettingEnvServer();
            $settingDays = $settingEnv->getSetVal('bt_start_days');
            $settingDays = empty($settingDays) ? 7 : $settingDays;
            $limitStart  = date('Y-m-d', strtotime("-{$settingDays} days"));
            if (strtotime($this->param['start_time']) < strtotime($limitStart)) {
                throw new ValidationException('start date error,at most on ' . $limitStart);
            }
        }
        //最多3张图片 前端有限制
        if (!empty($this->param['image_path']) && count($this->param['image_path']) > 3) {
            throw new ValidationException('at most 3 pictures');
        }

        //时间区间 120天
        $limitEnd = date('Y-m-d', strtotime("{$this->param['start_time']} +120 day"));
        if (strtotime($this->param['end_time']) >= strtotime($limitEnd)) {
            throw new ValidationException('end date error,at least on ' . $limitEnd);
        }

        //是否存在交集 包括 修改表
        $exist = $this->checkExistTime($this->param['start_time'], $this->param['end_time'], $userInfo['id']);
        if (!empty($exist)) {
            [$noticeStart, $noticeEnd] = [$exist[0]['start_time'], $exist[0]['end_time']];
            throw new ValidationException($t->_('business_trip_check_time_new',
                ['start' => $noticeStart, 'end' => $noticeEnd]));
        }

        //存在支援
        $supportExist = $this->checkExistSupport($this->param['start_time'], $this->param['end_time'], $userInfo['id']);
        if (!empty($supportExist)) {
            [$noticeStart, $noticeEnd] = [
                $supportExist[0]['employment_begin_date'],
                $supportExist[0]['employment_end_date'],
            ];
            throw new ValidationException($t->_('business_trip_check_support',
                ['start' => $noticeStart, 'end' => $noticeEnd]));
        }

        //新增需求 如果出差日期存在居家办公 不让申请出差 后确定逻辑 工具也限制限制 https://flashexpress.feishu.cn/docx/Tf3gdBgpWoqzZ7xgooGcZe8qn0c
        $workHomeExist = WorkFromHomeApplyModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and state in (1,2)  and home_date between :start_time: and :end_time:',
            'bind'       => [
                'staff_info_id' => $userInfo['id'],
                'start_time'    => $this->param['start_time'],
                'end_time'      => $this->param['end_time'],
            ],
        ]);
        if (!empty($workHomeExist)) {
            throw new ValidationException($t->_('work_home_apply_exist'));
        }
        return true;
    }

    //是否能修改 逻辑 详情页 给前端展示按钮 和 修改保存接口用
    public function checkEditButton($param, $tripObj)
    {
        $t = $this->getTranslation();
        //是否编辑过
        if ($tripObj->is_edit == BusinessTripModel::IS_EDIT) {
            return $t->_('bs_edit_notice');
        }

        //当前审批状态是否是审核通过
        if ($tripObj->status != enums::$audit_status['approved']) {
            return $t->_('bs_edit_status_error');
        }

        //是否在其他审批中
        if ($tripObj->approval_type != BusinessTripModel::APPROVAL_TYPE_FINISHED) {
            return $t->_('bs_edit_status_error');
        }

        //是否存在其他出差
        $exist = $this->checkExistTime($param['start_time'], $param['end_time'], $param['staff_info_id'], $tripObj->id);
        if (!empty($exist)) {
            return $t->_('business_trip_check_time_new');
        }

        //存在支援
        $supportExist = $this->checkExistSupport($param['start_time'], $param['end_time'], $param['staff_info_id']);
        if (!empty($supportExist)) {
            return $t->_('business_trip_check_support');
        }

        //是否关联 报销单
        $oaRes = $this->checkOa($tripObj->serial_no);
        if (isset($oaRes['error'])) {
            return $oaRes['error'];
        }
        if ($oaRes['result'] !== false) {
            return $t->_('bt_cancel_check_1');
        }

        //当前记录可以修改
        $this->isShowEdit = true;
        return '';
    }

    //获取最远的网点距离员工所属组织的距离
    public function getStoreDistance($userInfo, $otherIds)
    {
        if (empty($otherIds)) {
            return '';
        }
        $this->getDI()->get("logger")->write_log('getStoreDistance ' . json_encode($userInfo), 'info');
        //选择的网点 信息
        $storeIds  = explode(',', $otherIds);
        $storeList = SysStoreModel::find([
            'columns'    => 'id as store_id,name,lat,lng',
            'conditions' => 'id in ({ids:array})',
            'bind'       => ['ids' => $storeIds],
        ])->toArray();
        if (in_array(enums::HEAD_OFFICE_ID, $storeIds)) {
            $storeList[] = [
                'store_id' => enums::HEAD_OFFICE_ID,
                'name'     => enums::HEAD_OFFICE,
                'lat'      => BusinessTripEnums::HEAD_LAT,
                'lng'      => BusinessTripEnums::HEAD_LNG,
            ];
        }
        if (empty($storeList)) {
            return '';
        }
        $columnStore = array_column($storeList, 'name', 'store_id');
        //总部 固定取  headquarters_attendance_range id = 4 的 群里沟通决定
        if ($userInfo['organization_type'] == HrStaffInfoModel::ORGANIZATION_DEPARTMENT) {
            $lat = BusinessTripEnums::HEAD_LAT;
            $lng = BusinessTripEnums::HEAD_LNG;
        } else {//网点 员工
            $staffStore = $userInfo['organization_id'] ?? '';
            if (empty($staffStore)) {//hcm 过来的
                $staffStore = $userInfo['sys_store_id'] ?? '';
            }
            $storeInfo = SysStoreModel::findFirst("id = '{$staffStore}'");
            $lat       = empty($storeInfo) ? '' : $storeInfo->lat;
            $lng       = empty($storeInfo) ? '' : $storeInfo->lng;
        }

        //距离
        $server = new AttendanceServer($this->lang, $this->timezone);
        $res    = $server->get_most_close($storeList, $lat, $lng, 2);

        //网点名称 + 距离
        $distance = empty($res['distance']) ? '0' : round($res['distance'] / 1000, 2);
        $str      = "{$columnStore[$res['store_id']]} {$distance} km";
        return $str;
    }


    public function checkOa($serial_no)
    {
        //调用OA
        $cli = new ApiClient('oa_rpc', '', 'check_bt_is_reimbursed', $this->lang);
        $cli->setParams(['serial_no' => $serial_no]);
        $rpc_result = $cli->execute();
        return $rpc_result;
    }


    /**
     * 判断所选日期内是否有出差申请记录
     * @param $start
     * @param $end
     * @param $user_id
     * @param int $currentId //马来可以修改记录 当前出差表的记录id 修改申请时候有
     * @return array
     */
    public function checkExistTime($start, $end, $user_id, int $currentId = 0)
    {
        //出差表
        $conditions = 'status in (1,2) and apply_user= :apply_user:';
        $bind       = ['apply_user' => $user_id, 'start' => $start, 'end' => $end];
        if (!empty($currentId)) {
            $conditions .= ' and id != :id:';
            $bind['id'] = $currentId;
        }
        $conditions .= ' and !(start_time > :end: or  end_time < :start:)';

        $exist = BusinessTripModel::find([
            'columns'    => ' start_time, end_time',
            'conditions' => $conditions,
            'bind'       => $bind,

        ])->toArray();
        //要返回 具体那个记录的日期 冲突了
        if (!empty($exist)) {
            return $exist;
        }

        //出差修改表
        $editConditions = 'status in (1,2) and staff_info_id= :staff_info_id:';
        $editBind       = ['staff_info_id' => $user_id, 'start' => $start, 'end' => $end];
        $editConditions .= ' and !(new_start_time > :end: or  new_end_time < :start:)';

        $exist = BusinessTripEditModel::find([
            'columns'    => 'new_start_time as start_time, new_end_time as end_time',
            'conditions' => $editConditions,
            'bind'       => $editBind,

        ])->toArray();
        if (!empty($exist)) {
            return $exist;
        }
        return [];
    }

    //判断 是否存在支援
    public function checkExistSupport($start, $end, $user_id)
    {
        $conditions = 'status in (1,2) and support_status in (1,2,3) and staff_info_id = :staff_id:';
        $conditions .= ' and !(employment_begin_date > :end: or  employment_end_date < :start:)';
        $bind       = ['staff_id' => $user_id, 'start' => $start, 'end' => $end];

        $exist = HrStaffApplySupportStoreModel::find([
            'columns'    => 'employment_begin_date, employment_end_date',
            'conditions' => $conditions,
            'bind'       => $bind,

        ])->toArray();
        if (!empty($exist)) {
            return $exist;
        }
        return [];
    }


    /**
     * 审批同意的出差 撤销时验证
     * @param $paramIn
     * @return bool
     * @throws ValidationException
     */
    public function beforeCancelCheck($paramIn): bool
    {
        //获取记录信息
        $result = $this->trip->getTripR($paramIn);
        if (empty($result)) {
            throw new ValidationException("can not find info");
        }
        if ($result['status'] != enums::APPROVAL_STATUS_APPROVAL) {
            return true;
        }
        //调用OA
        $rpc_result = $this->checkOa($result['serial_no']);
        if (isset($rpc_result['error'])) {
            throw new ValidationException($rpc_result['error']);
        }
        if ($rpc_result['result'] === false) {
            return true;
        }
        throw new ValidationException($this->getTranslation()->_('bt_cancel_check_1'));
    }


    /**
     * 修改记录状态记录日志
     * @Access  public
     * @Param   request
     * @Return  array
     * @throws ValidationException
     */
    public function updateTripStatus($paramIn, $userinfo)
    {
        //获取记录信息
        $result = $this->trip->getTripR($paramIn);
        if (empty($result)) {
            throw new ValidationException("can not find info");
        }

        $audit_type = $this->getAuditTypeByBTY((int)$result['business_trip_type']);
        //申请人撤销后,审批人不能审批
        if ($result['status'] == 4) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        $res             = false;
        $approval_server = new ApprovalServer($this->lang, $this->timezone);

        //审核通过
        if ($paramIn['status'] == enums::APPROVAL_STATUS_APPROVAL) {
            $res = $approval_server->approval($paramIn['id'], $audit_type, $userinfo['id']);
            return (bool)$res;
        }
        //驳回
        if ($paramIn['status'] == enums::APPROVAL_STATUS_REJECTED) {
            $res = $approval_server->reject($paramIn['id'], $audit_type, $paramIn['reason'], $userinfo['id']);
            return (bool)$res;
        }
        //撤销
        if ($paramIn['status'] == enums::APPROVAL_STATUS_CANCEL || $paramIn['status'] == enums::APPROVAL_STATUS_PENDING) {
            $type = $this->determineCancelType($paramIn['id'], $audit_type);
            if ($type == 1) {
                //审批中撤销
                $res = (new ApprovalServer($this->lang, $this->timezone))->cancel($paramIn['id'], $audit_type, '',
                    $userinfo['id']);
                return (bool)$res;
            }

            //要注意 修改中审批 不能发起撤销申请 todo
            if ($result['approval_type'] == BusinessTripModel::APPROVAL_TYPE_EDIT) {
                throw new ValidationException('state error, in modification');
            }

            //非审核通过 不能发起撤销申请
            if ($result['status'] != enums::$audit_status['approved']) {
                throw new ValidationException("record status error");
            }
            //审批通过的出差 撤销时候验证是否已经报销了
            $this->beforeCancelCheck($paramIn);

            $db = $this->getDI()->get("db");
            $db->begin();
            $bus_info                = BusinessTripModel::findFirst($result['id']);
            $bus_info->status        = 1;
            $bus_info->created_at    = DateHelper::localToUtc();//创建时间 里面和外面要更新为 撤销审批申请时间
            $bus_info->approval_type = BusinessTripModel::APPROVAL_TYPE_CANCEL;
            $bus_info->update();
            //操作 审批流 apply 和 approval 变更为撤销审批状态
            $res = $approval_server->cancel_create($paramIn['audit_id'], $audit_type, $paramIn['reason'],
                $result['apply_user'], 'id_' . $paramIn['audit_id']);
            $res ? $db->commit() : $db->rollBack();
        }
        return (bool)$res;
    }

    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result = BusinessTripModel::findFirst($auditId);
        if (empty($result)) {
            throw new ValidationException('trip id error');
        }
        $editInfo = BusinessTripEditModel::findFirst("origin_id = {$result['id']}");
        //获取提交人用户信息
        $staff_info = (new StaffRepository($this->lang))->getStaffPosition($result->apply_user);

        //所有枚举 接口
        $countryRegion = $this->getEnumsListFromCache();
        $audit_type    = $this->getAuditTypeByBTY((int)$result->business_trip_type);

        $resultImg = BusinessTripImgModel::find("business_trip_id = {$auditId}")->toArray();
        $resultImg = empty($resultImg) ? [] : array_column($resultImg, 'img_path');
        //多语言切换
        $tran_type   = $this->trip->getTransportationType();
        $single_type = $this->trip->getSingleroundtripType();
        if ($result->traffic_tools == 4) {//如果是其他类型交通工具显示录入的具体名称
            $traffic_tools = ($tran_type[$result->traffic_tools] ?? '') . "[" . $result->other_traffic_name . "]";
        } else {
            $traffic_tools = $tran_type[$result->traffic_tools] ?? '';
        }
        //是否显示编辑按钮
        $checkParam['start_time']    = $result->start_time;
        $checkParam['end_time']      = $result->end_time;
        $checkParam['staff_info_id'] = $result->apply_user;
        if ($user == $result->apply_user) {
            $this->checkEditButton($checkParam, $result);
        }

        $t          = $this->getTranslation();
        $detailData = empty($result->detail_data) ? '' : json_decode($result->detail_data, true);

        //申请人
        $returnData['data']['detail'][] = [
            'key'   => $t->_('apply_parson'),
            'value' => sprintf('%s ( %s )', $staff_info['name'] ?? '',
                $staff_info['id'] ?? ''),
        ];
        $returnData['data']['detail'][] = [
            'key'   => $t->_('apply_department'),
            'value' => $staff_info['depart_name'] ?? '',
        ];//部门
        $returnData['data']['detail'][] = [
            'key'   => $t->_('apply_job_title_name'),
            'value' => $staff_info['job_name'] ?? '',
        ];//职位
        //网点
        if ($staff_info['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            $returnData['data']['detail'][] = [
                'key'   => $t->_('apply_store'),
                'value' => enums::HEAD_OFFICE,
            ];
        } else {
            $storeInfo                      = (new SysStoreServer($this->lang,
                $this->timezone))->getRegionAndPieceName($staff_info['sys_store_id']);
            $returnData['data']['detail'][] = [
                'key'   => $t->_('apply_store'),
                'value' => $storeInfo['store_name'],
            ];
            //大区片区 region_piece_name
            $returnData['data']['detail'][] = [
                'key'   => $t->_('region_piece_name'),
                'value' => "{$storeInfo['manage_region']}-{$storeInfo['manage_piece']}",
            ];
        }
        //境内 境外
        $returnData['data']['detail'][] = [
            'key'   => $t->_('business_trip_type'),
            'value' => $result->business_trip_type == BusinessTripModel::BTY_DOMESTIC
                ? $t->_('business_trip_domestic')
                : $t->_('business_trip_foreign'),
        ];

        //目的地类型 网点 非网点 bs_destination_type_store
        if ($result->business_trip_type == BusinessTripModel::BTY_DOMESTIC) {
            $returnData['data']['detail'][] = [
                'key'   => $t->_('destination_type'),
                'value' => $result->destination_type == BusinessTripEnums::DESTINATION_TYPE_STORE
                    ? $t->_('bs_destination_type_store')
                    : $t->_('bs_destination_type_other'),
            ];

            //出差原因
            $returnData['data']['detail'][] = [
                'key'   => $t->_('bs_reason'),
                'value' => $t->_($result->reason_enum),
            ];
        }
        //出差事由
        $returnData['data']['detail'][] = [
            'key'   => $t->_('reason_application'),
            'value' => $result->reason_application,
        ];
        $returnData['data']['detail'][] = [
            'key'   => $t->_('traffic_tools'),
            'value' => $traffic_tools,
        ];//交通工具
        //单程往返
        $returnData['data']['detail'][] = [
            'key'   => $t->_('oneway_or_roundtrip'),
            'value' => $single_type[$result->oneway_or_roundtrip],
        ];
        //城市 历史数据 写departure_city  新需求 改为城市code  然后取翻译
        if (!empty($result['departure_city'])) {
            $returnData['data']['detail'][] = [
                'key'   => $t->_('departure_city'),
                'value' => $result->departure_city,
            ];
        } else {
            $pcName                         = $this->formatProvinceCity($result->departure_city_code);
            $returnData['data']['detail'][] = [
                'key'   => $t->_('departure_city'),
                'value' => $pcName,
            ];
        }
        //境外出差并且选择了国家
        if ($result->business_trip_type == BusinessTripModel::BTY_FOREIGN && !empty($result->destination_country)) {
            $countryNameList = array_column($countryRegion['destination_country'], 'key', 'value');

            //境外出差
            $returnData['data']['detail'][] = [
                'key'   => $t->_('destination_country'),
                'value' => $countryNameList[$result->destination_country] ?? '',
            ];
            //出差国家是其他- 显示国家名称
            if ($result->destination_country == HrStaffInfoModel::WORKING_COUNTRY_OTHER) {
                $returnData['data']['detail'][] = [
                    'key'   => $t->_('destination_country_name'),
                    'value' => $result->destination_country_name,
                ];
            }
        }
        //目的地城市
        if (!empty($result['destination_city'])) {
            $returnData['data']['detail'][] = [
                'key'   => $t->_('destination_city'),
                'value' => $result->destination_city,
            ];
        } else {
            $pcName                         = $this->formatProvinceCity($result->destination_city_code);
            $returnData['data']['detail'][] = [
                'key'   => $t->_('destination_city'),
                'value' => $pcName,
            ];
        }

        // 网点出差类型才有
        if (!empty($result->destination_store) && $result->destination_type == BusinessTripEnums::DESTINATION_TYPE_STORE) {
            //目的地网点
            $storeServer = new SysStoreServer($this->lang, $this->timezone);
            $storeIds    = explode(',', $result->destination_store);
            $storeData   = $storeServer->getStoreName($storeIds);
            if (in_array(enums::HEAD_OFFICE_ID, $storeIds)) {
                $storeData[enums::HEAD_OFFICE_ID] = enums::HEAD_OFFICE;
            }
            $storeNames                     = implode(',', array_values($storeData));
            $returnData['data']['detail'][] = [
                'key'   => $t->_('bs_destination_store'),
                'value' => $storeNames,
            ];
            //最远目的地网点 距离和名称
            $returnData['data']['detail'][] = [
                'key'   => $t->_('bs_store_distance'),
                'value' => $result->destination_store_distance ?: '',
            ];
        }

        //目的地 手动输入 非网点类型展示 destination_text
        if ($result->destination_type == BusinessTripEnums::DESTINATION_TYPE_OTHER) {
            $returnData['data']['detail'][] = [
                'key'   => $t->_('bs_destination_text'),
                'value' => $result->destination_text,
            ];
        }
        $showEditFlag = false;//是否显示 修改信息 只有状态是1，2的需要替换
        if (!empty($editInfo) && in_array($editInfo->status,
                [enums::$audit_status['panding'], enums::$audit_status['approved']])) {
            $showEditFlag = true;
        }
        //是否报销住宿费
        $isStay                         = ($showEditFlag && !empty($editInfo)) ? $editInfo->new_is_stay : $result->is_stay;
        $text                           = $isStay == BusinessTripEnums::YES ? $t->_('store_support_is_stay_1') : $t->_('store_support_is_stay_0');
        $returnData['data']['detail'][] = [
            'key'   => $t->_('bs_is_stay'),
            'value' => $text,
        ];

        $returnData['data']['detail'][] = [
            'key'   => $t->_('start_time'),
            'value' => ($showEditFlag && !empty($editInfo)) ? $editInfo->new_start_time : $result->start_time,
        ];
        $returnData['data']['detail'][] = [
            'key'   => $t->_('end_time'),
            'value' => ($showEditFlag && !empty($editInfo)) ? $editInfo->new_end_time : $result->end_time,
        ];
        $returnData['data']['detail'][] = [
            'key'   => $t->_('days_num'),
            'value' => ($showEditFlag && !empty($editInfo)) ? $editInfo->new_days_num : $result->days_num,
        ];
        $returnData['data']['detail'][] = ['key' => $t->_('remark'), 'value' => $result->remark];
        $returnData['data']['detail'][] = ['key' => $t->_('photo'), 'value' => $resultImg];


        //字段解释 ：申请理由，交通工具，单程1往返2 ,出发城市,到达城市,开始时间,结束时间,出差天数,备注
        $auditListRe = new AuditlistRepository($this->lang, $this->timezone);
        $data        = [
            'title'        => $auditListRe->getAudityType($audit_type),
            'origin_id'    => $result['id'],
            'id'           => $result['id'],
            'staff_id'     => $result['apply_user'],
            'is_stay'      => $result['is_stay'],
            'start_time'   => $result['start_time'],
            'end_time'     => $result['end_time'],
            'days_num'     => $result['days_num'],
            'type'         => $audit_type,
            'created_at'   => show_time_zone($result['created_at']),
            'updated_at'   => show_time_zone($result['updated_at']),
            'status'       => $result['status'],
            'status_text'  => $auditListRe->getAuditStatus('10' . $result['status']),
            'serial_no'    => $result['serial_no'] ?? '',
            'is_pop'       => $result['status'] == 2,
            'is_show_edit' => $this->isShowEdit,//是否 显示 修改按钮
        ];
        //fbi 接口数据 表格 审批通过之后 固化
        if (!empty($storeIds) && !empty($result['destination_store']) && $result['destination_type'] == BusinessTripEnums::DESTINATION_TYPE_STORE) {
            $data['parcel_info'] = $detailData['parcel_info'] ?? [];
            if ($result->status == enums::$audit_status['panding'] && empty($detailData)) {
                $date                = show_time_zone($result->created_at);
                $start               = date('Y-m-d', strtotime("{$date} -7 day"));
                $end                 = date('Y-m-d', strtotime("{$date} -1 day"));
                $data['parcel_info'] = $this->getStoreParcel($storeIds, $start, $end);
            }
        }
        //如果修改过 并且修改审核通过  获取修改前信息数据
        $data['edit_info'] = null;

        if (!empty($editInfo)) {
            $stayBefore        = $editInfo->old_is_stay == BusinessTripEnums::YES ? $t->_('store_support_is_stay_1') : $t->_('store_support_is_stay_0');
            $stayAfter         = $editInfo->new_is_stay == BusinessTripEnums::YES ? $t->_('store_support_is_stay_1') : $t->_('store_support_is_stay_0');
            $row['is_stay']    = $stayBefore . ' -> ' . $stayAfter;
            $row['start_time'] = $editInfo->old_start_time . ' -> ' . $editInfo->new_start_time;
            $row['end_time']   = $editInfo->old_end_time . ' -> ' . $editInfo->new_end_time;
            $row['days_num']   = $editInfo->old_days_num . ' -> ' . $editInfo->new_days_num;
            $row['created_at'] = show_time_zone($editInfo->created_at);
            $data['edit_info'] = $row;
        }
        $returnData['data']['head'] = $data;
        return $returnData;
    }


    /**
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        $result = $this->trip->getTripR(['id' => $auditId]);//通过id获取详情
        if ($result['business_trip_type'] != BusinessTripModel::BTY_YELLOW) {
            $cancelAfterFlag = true;
            if ($result['is_cancel'] == BusinessTripModel::IS_CANCEL) {
                $cancelAfterFlag = false;
            }
            $rule = new AuditOptionRule(true, $cancelAfterFlag, false, false, false, false);
            //如果修改审批中或者 撤销审批中 不展示 撤销按钮
            if (in_array($result['approval_type'],
                [BusinessTripModel::APPROVAL_TYPE_EDIT, BusinessTripModel::APPROVAL_TYPE_CANCEL])) {
                $rule = new AuditOptionRule(false, false, false, false, false, false);
            }
        } else {
            $rule = new AuditOptionRule(true, false, false, false, false, false);
        }
        return $rule;
    }

    /**
     * 申请人自定义按钮
     * @param $auditId
     * @return bool
     */
    public function applicantCustomiseOptions($auditId): bool
    {
        $info = BusinessTripModel::findFirst($auditId);
        if (empty($info)) {
            return false;
        }
        if ($info['is_tool'] == 1) {
            return false;
        }
        return true;
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $info = BusinessTripModel::findFirst($auditId);
        if (empty($info)) {
            return [];
        }

        if (!empty($info['destination_city'])) {
            $cityName = $info->destination_city;
        } else {
            $cityName = "code {$info['destination_city_code']}";
        }

        $param = [
            [
                'key'   => "destination_city",
                'value' => $cityName,
            ],
            [
                'key'   => "start_time",
                'value' => $info['start_time'],
            ],
            [
                'key'   => "end_time",
                'value' => $info['end_time'],
            ]
            ,
            [
                'key'   => "created_at",
                'value' => show_time_zone($info['created_at']),
            ],
        ];
        return $param;
    }

    /**
     * @param $info
     * @param $summaryData
     * @param int $type 1 申请时候 换成新的  2 非审批通过 还原成之前的
     * @return array|array[]
     */
    public function getEditSummary($info, $editInfo, int $type = 1)
    {
        if (empty($info)) {
            return [];
        }

        if (!empty($info['destination_city'])) {
            $cityName = $info['destination_city'];
        } else {
            $cityName = "code {$info['destination_city_code']}";
        }

        if ($type == 1) {//申请 操作 替换成新的
            $start = $editInfo['new_start_time'];
            $end   = $editInfo['new_end_time'];
        } else {
            $start = $editInfo['old_start_time'];
            $end   = $editInfo['old_end_time'];
        }

        $param = [
            [
                'key'   => "destination_city",
                'value' => $cityName,
            ],
            [
                'key'   => "start_time",
                'value' => $start,
            ],
            [
                'key'   => "end_time",
                'value' => $end,
            ]
            ,
            [
                'key'   => "created_at",
                'value' => show_time_zone($info['created_at']),
            ],
        ];
        return $param;
    }


    /**
     * 通申请类型寻找审批类型
     * @param $business_trip_type
     * @return int
     */
    public function getAuditTypeByBTY($business_trip_type): int
    {
        switch ($business_trip_type) {
            case BusinessTripModel::BTY_YELLOW://只有泰国 入口已经关闭
                $audit_type = AuditListEnums::APPROVAL_TYPE_YCBT;
                break;
            case BusinessTripModel::BTY_GO_OUT://菲律宾
                $audit_type = AuditListEnums::APPROVAL_TYPE_GO;
                break;
            default:
                $audit_type = AuditListEnums::APPROVAL_TYPE_BT;
                break;
        }
        return $audit_type;
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if (!$isFinal) {
            return true;
        }

        $info = BusinessTripModel::findFirst($auditId);
        if (empty($info)) {
            throw new ValidationException('empty trip info');
        }

        //非撤销和修改流程 的驳回和超时 记录最后审批人信息
        if (!in_array($info->approval_type,
                [BusinessTripModel::APPROVAL_TYPE_CANCEL, BusinessTripModel::APPROVAL_TYPE_EDIT]) && in_array($state,
                [enums::$audit_status['dismissed'], enums::$audit_status['timedout']])) {
            //回写 超时 审批人
            $info->approve_user = $extend['staff_id'] ?? 0;//超时 或者驳回 审批人
            if (!empty($extend['remark'])) {
                $info->reason = $extend['remark'];
            }
        }
        //正常审批 固化 detail 字段 fbi 接口
        if ($info->approval_type == BusinessTripModel::APPROVAL_TYPE_NORMAL && $info->destination_type == BusinessTripEnums::DESTINATION_TYPE_STORE) {
            $date                = show_time_zone($info->created_at);
            $start               = date('Y-m-d', strtotime("{$date} -7 day"));
            $end                 = date('Y-m-d', strtotime("{$date} -1 day"));
            $storeIds            = empty($info->destination_store) ? [] : explode(',', $info->destination_store);
            $json['parcel_info'] = $this->getStoreParcel($storeIds, $start, $end);
            $info->detail_data   = json_encode($json);
        }

        //如果是修改审批审核通过 先备份修改前的 到修改表 修改后的 写入业务表
        if ($info->approval_type == BusinessTripModel::APPROVAL_TYPE_EDIT) {
            //最终审批 并且是 修改审批完成 不管是否通过 都标记 之后不能再次申请修改
            $info->is_edit = BusinessTripModel::IS_EDIT;
            $editInfo      = BusinessTripEditModel::findFirst("origin_id = {$info['id']}");
            if (empty($editInfo)) {
                //先报警 没有对应的修改信息
                $this->logger->write_log("saveEditAgree error {$info['id']}");
            } else {
                $editInfo->approve_user = $extend['staff_id'] ?? 0;
                $editInfo->status       = $state;
                $editInfo->update();
            }
            //审批通过
            if ($state == enums::$audit_status['approved']) {
                $info->is_stay    = $editInfo->new_is_stay;
                $info->start_time = $editInfo->new_start_time;
                $info->end_time   = $editInfo->new_end_time;
                $info->days_num   = $editInfo->new_days_num;
            } else {
                //修改 审批列表 summary 卡片 还原
                $summary    = $this->getEditSummary($info, $editInfo, 2);
                $auditApply = AuditApplyModel::findFirst([
                    'conditions' => 'biz_type = :biz_type: and biz_value = :biz_value:',
                    'bind'       => ['biz_type' => AuditListEnums::APPROVAL_TYPE_BT, 'biz_value' => $auditId],
                ]);
                if (!empty($auditApply)) {
                    $auditApply->summary = json_encode($summary, JSON_UNESCAPED_UNICODE);
                    $auditApply->update();
                }
                //驳回 要发消息 新增需求
                if ($state == enums::$audit_status['dismissed']) {
                    $staffLang = (new StaffServer())->getLanguage($info->apply_user);
                    //发消息
                    $title   = $this->getTranslation()->_('bs_reject_edit_title');
                    $content = $this->getTranslation()->_('bs_reject_edit_content',
                        ['no' => $info->serial_no, 'reason' => $extend['remark'] ?? '']);
                    $content = addslashes("<div style='font-size: 30px'>" . $content . "</div>");;
                    $extend['category'] = MessageEnums::MESSAGE_CATEGORY_ORDINARY;
                    $extend['lang']     = empty($staffLang) ? 'th' : $staffLang;
                    $msgServer          = new MessageServer($this->lang, $this->timeZone);
                    $msgServer->sendMessage($info->apply_user, $title, $content, $extend);
                }
                //修改审批不通过 原记录 要改为审核通过状态 其他不变
                $state = enums::$audit_status['approved'];
            }
        }

        //撤销申请
        if ($info->approval_type == BusinessTripModel::APPROVAL_TYPE_CANCEL) {
            //撤销审批流 同意 最终状态改为撤销
            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                $state = enums::APPROVAL_STATUS_CANCEL;
                //如果 存在撤销记录 并且是审核通过了 要改成撤销
                $editInfo = BusinessTripEditModel::findFirst("origin_id = {$info['id']} and status = 2");
                if (!empty($editInfo)) {
                    $editInfo->status = enums::APPROVAL_STATUS_CANCEL;
                    $editInfo->update();
                }
            }
            //撤销审批流 驳回 最终状态改回 审批通过
            if ($state == enums::APPROVAL_STATUS_REJECTED) {
                $state = enums::APPROVAL_STATUS_APPROVAL;
            }
            $info->is_cancel = BusinessTripModel::IS_CANCEL;//撤销过不能再发起撤销的标记
        }

        $info->status        = $state;
        $info->approval_type = BusinessTripModel::APPROVAL_TYPE_FINISHED;//当前任意审批结束 修改状态

        $info->update();
        return true;
    }

    /**
     * 样例
     * from_node_id | to_node_id | valuate_formula | valuate_codp
     * -------------+------------+-----------------+-------------
     *      4       |     5      |    $p1 == 4     | getSubmitterDepartment
     * 表示当提交人的部门为4时，审批节点4的下一个节点是5
     * 需要在 getWorkflowParams 中返回申请人所在的部门字段
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $businessDetail = BusinessTripModel::findFirst([
            'conditions' => 'id = :id: ',
            'bind'       => ['id' => $auditId],
        ]);
        if (empty($businessDetail)) {
            return [];
        }
        $staffId   = $businessDetail['apply_user'];
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: ',
            'bind'       => ['staff_id' => $staffId],
        ]);
        $staffInfo = $staffInfo ? $staffInfo->toArray() : [];

        $departmentId = 0;
        if ($staffInfo) {
            if (isset($staffInfo['node_department_id']) && $staffInfo['node_department_id']) {
                $departmentId = $staffInfo['node_department_id'];
            } else {
                $departmentId = $staffInfo['sys_department_id'];
            }
        }

        return [
            'department_id'          => $departmentId,
            'w_f_condition_days_num' => $businessDetail['days_num'] ?? 0, //获取出差和外出天数
        ];
    }

    /**
     * @description: 获取静态枚举
     * @param null
     * @return:
     * @author: L.J
     * @time: 2023/1/10 15:32
     */
    public function getEnumsList()
    {
        $ac = new ApiClient('hcm_rpc', '', 'getDictionaryByDictCode', $this->lang);
        $ac->setParams(['dict_code' => 'working_country']);
        $result = $ac->execute();
        if ($result['result'] && $result['result']['code'] != 1) {
            $data['destination_country'] = [];//目的地国家
        } else {
            $response = [];
            foreach ($result['result']['data'] as $v) {
                $response[] = [
                    'key'   => $v['label'],
                    'value' => $v['value'],
                ];
            }
            $data['destination_country'] = $response;//目的地国家
        }

        //目的地类型
        $data['destination_type_list'] = BusinessTripEnums::getDestinationType($this->lang);

        //出差国家类型 境内境外
        $data['country_type'] = [
            ['key' => $this->getTranslation()->_('business_trip_domestic'), 'value' => BusinessTripModel::BTY_DOMESTIC],
            ['key' => $this->getTranslation()->_('business_trip_foreign'), 'value' => BusinessTripModel::BTY_FOREIGN],
        ];

        //网点出差 原因
        $data['business_trip_reason_list_1'] = BusinessTripEnums::getStoreReason($this->lang);
        //非网点出差 原因
        $data['business_trip_reason_list_2'] = BusinessTripEnums::getOtherReason($this->lang);
        //出差工具
        $data['traffic_tools_list'] = BusinessTripEnums::getTripTools($this->lang);
        //单程往返
        $data['oneway_or_round_list'] = BusinessTripEnums::getTripRound($this->lang);
        //是否
        $data['whether'] = BusinessTripEnums::getWhether($this->lang);

        //城市和省联动
        $data['province_city'] = (new SysServer($this->lang, $this->timezone))->getProvinceCitySelectFromCache();

        //天数配置
        $envModel     = new SettingEnvServer();
        $setting_code = [
            'bt_start_days',
            'bt_duration_days',
        ];
        $setting_val  = $envModel->listByCode($setting_code);
        if (!empty($setting_val)) {
            $setting_val = array_column($setting_val, 'set_val', 'code');
        }
        $data['start_limit']    = $setting_val['bt_start_days'] ?? '7';//开始时间不能早于7天
        $data['duration_limit'] = $setting_val['bt_duration_days'] ?? '120';//最大时间跨度120天

        return $data;
    }

    public function searchStore($param)
    {
        $data   = (new StoreRepository())->searchStore($param);
        $data[] = [
            'id'   => '-1',
            'name' => 'Head Office',
        ];
        return $data;
    }

    //fbi 获取 网点日期内的揽件和未妥投等
    public function getStoreParcel($storeIds, $start, $end)
    {
        if (empty($storeIds)) {
            return ['dates' => [], 'store' => null, 'tips' => []];
        }
        $fle_rpc                = new ApiClient("ard_api", '', 'deliverycount.get_latest_parcel_num_by_store_date',
            'en');
        $param['store_id_list'] = $storeIds;
        $param['begin_date']    = $start;
        $param['end_date']      = $end;
        $fle_rpc->setParams($param);
        $res = $fle_rpc->execute();
        if (isset($res['error'])) {
            $this->logger->write_log('bt getStoreParcel ' . $res['error'] . " request " . json_encode($param), 'info');
            return ['dates' => [], 'store' => null, 'tips' => []];
        }
        if (isset($res['result']['code']) && $res['result']['code'] != 1) {
            $this->logger->write_log('bt getStoreParcel ' . $res['msg'] . " request " . json_encode($param), 'info');
        }
        $data = $res['result']['data'];
        //整理接口数据
        $formatData = [];
        foreach ($data as $da) {
            $date_v           = date('m/d', strtotime($da['stat_date']));
            $key              = $date_v . '_' . $da['store_id'];
            $formatData[$key] = $da;
        }

        //获取网点信息
        $storeInfo = SysStoreModel::find([
            'columns'    => 'id,category,name',
            'conditions' => 'id in ({ids:array})',
            'bind'       => ['ids' => $storeIds],
        ])->toArray();
        $storeInfo = array_column($storeInfo, null, 'id');
        //日期转格式
        $dates = [];
        $i     = 1;
        $step  = $start;
        while ($i <= 7) {
            $dates[] = date('m/d', strtotime($step));
            $i++;
            $step = date('m/d', strtotime("{$step} +1 day"));
        }
        $nwCategory   = [
            enums::$stores_category['dc'],
            enums::$stores_category['sp'],
            enums::$stores_category['bdc'],
            enums::$stores_category['pdc'],
            enums::$stores_category['cdc'],
        ];
        $shopCategory = [
            enums::$stores_category['shop_pickup_only'],
            enums::$stores_category['shop_pickup_delivery'],
            enums::$stores_category['shop_ushop'],
        ];
        //根据分类取字段
        /**
         * 1. NetWork（DC、SP、BDC、PDC、CDC）取自-FBI-DC报表管理- DC每日运营统计报表-【当日未妥投包裹量】字段
         * 2. Shop（SHOP(pickup-only)、SHOP(pickup&delivery)、USHOP）取自-FBI-SHOP&U_project Dashboard监控【当日揽件量】
         * 3. Flash Home（FH）取自-fle_staging.parcel_info，ticket_pickup_store_id【当日揽件量】
         * 4. 其他网点类型不需要展示该项数据
         */
        $infoData = [];
        $nw       = $shop = $home = false;
        foreach ($storeIds as $storeId) {
            if ($storeId == enums::HEAD_OFFICE_ID) {
                continue;
            }
            if (!in_array($storeInfo[$storeId]['category'], $nwCategory)
                && !in_array($storeInfo[$storeId]['category'], $shopCategory)
                && $storeInfo[$storeId]['category'] != enums::$stores_category['fh']
            ) {
                continue;
            }
            $row['name'] = $storeInfo[$storeId]['name'];
            $row['data'] = [];
            foreach ($dates as $date) {
                $k = $date . '_' . $storeId;
                if (in_array($storeInfo[$storeId]['category'], $nwCategory)) {
                    $nw                 = true;
                    $row['data'][$date] = $formatData[$k]['count_delivery_not_signed'] ?: 0;
                }
                if (in_array($storeInfo[$storeId]['category'], $shopCategory)) {
                    $shop               = true;
                    $row['data'][$date] = $formatData[$k]['count_shop_delivery'] ?: 0;
                }
                if ($storeInfo[$storeId]['category'] == enums::$stores_category['fh']) {
                    $home               = true;
                    $row['data'][$date] = $formatData[$k]['count_delivery'] ?: 0;
                }
            }
            $infoData[] = $row;
        }
        $strArr = [];
        if ($nw) {
            $strArr[] = $this->getTranslation()->_('bs_network_tips');
        }
        if ($shop) {
            $strArr[] = $this->getTranslation()->_('bs_shop_tips');
        }
        if ($home) {
            $strArr[] = $this->getTranslation()->_('bs_home_tips');
        }
        return ['dates' => $dates, 'store' => $infoData, 'tips' => $strArr];
    }

    /**
     * 根据单号 获取 列表
     * @param array $serial_no_item
     * @return array
     */
    public function getTripListBySerialNoItem(array $serial_no_item = [])
    {
        $serial_no_item = array_filter($serial_no_item);
        if (empty($serial_no_item)) {
            return $this->checkReturn(['data' => []]);
        }

        $list = BusinessTripModel::find([
            'conditions' => 'serial_no IN ({serial_no_item:array})',
            'bind'       => ['serial_no_item' => array_values($serial_no_item)],
            'columns'    => [
                'serial_no',
                'departure_city',
                'departure_city_code',
                'destination_city',
                'destination_city_code',
                'start_time',
                'end_time',
                'apply_user',
                'business_trip_type',
                'destination_country',
                'destination_country_name',
                'reason_enum',
                'is_stay',
                'days_num',
                'status',
            ],
            'order'      => 'serial_no ASC',
        ])->toArray();

        if (empty($list)) {
            return $this->checkReturn(['data' => []]);
        }

        //所有枚举 接口
        $countryRegion   = $this->getEnumsListFromCache();
        $countryNameList = array_column($countryRegion['destination_country'], 'key', 'value');

        $t = $this->getTranslation();

        $auditListRe = new AuditlistRepository($this->lang, $this->timezone);

        foreach ($list as &$val) {
            $val['business_trip_type_label'] = '';
            $val['destination_label']        = '';

            $val['departure_city_label']   = '';
            $val['destination_city_label'] = '';

            $val['trip_reason'] = '';

            if ($val['business_trip_type'] == BusinessTripModel::BTY_DOMESTIC) {
                $val['business_trip_type_label'] = $t->_('business_trip_domestic');
                $val['destination_label']        = !empty($val['destination_city_code']) ? $this->formatProvinceCity($val['destination_city_code'],
                    ' ') : $val['destination_city'];

                $val['trip_reason'] = $t->_($val['reason_enum']);
            } elseif ($val['business_trip_type'] == BusinessTripModel::BTY_FOREIGN) {
                $val['business_trip_type_label'] = $t->_('business_trip_foreign');
                $val['destination_label']        = $countryNameList[$val['destination_country']] ?? '';
            }


            $val['departure_city_label']   = !empty($val['departure_city_code']) ? $this->formatProvinceCity($val['departure_city_code'],
                ' ') : $val['departure_city'];
            $val['destination_city_label'] = !empty($val['destination_city_code']) ? $this->formatProvinceCity($val['destination_city_code'],
                ' ') : $val['destination_city'];

            $val['is_stay_label'] = $t->_('store_support_is_stay_' . $val['is_stay']) ?? '';
            $val['status_label']  = $auditListRe->getAuditStatus('10' . $val['status']);
        }

        return $this->checkReturn(['data' => $list]);
    }



}