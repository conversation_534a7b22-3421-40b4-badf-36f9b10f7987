<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;
use FlashExpress\bi\App\Models\backyard\HrBlackListModel;
use FlashExpress\bi\App\Models\backyard\HrFamilyModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\HrJDRepository;
use FlashExpress\bi\App\Server\HrStaffContractServer;
use FlashExpress\bi\App\Server\OutsourcingBlackListServer;
use FlashExpress\bi\App\Server\QuickOfferServer as BaseCancelQuickOfferServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class QuickOfferServer extends BaseCancelQuickOfferServer
{

    protected $needDriverLicenseJd = ['10003', '10916', '10543'];

    protected function getOtherValidationsFieldByJD($jd_id): array
    {
        $result = [];
        if (in_array($jd_id, $this->needDriverLicenseJd)) {
            $result["driver_license_front"] = "StrLenGeLe:1,255|>>>:driver_license_front error";
        }
        return $result;
    }

    protected function fullParamInData($paramIn)
    {
        return $paramIn;
    }

    protected function validationStaffInfoMobile($paramIn)
    {
        $phoneExist = HrStaffInfoModel::findFirst([
            'conditions' => ' mobile = :mobile: and state in (1,3) and is_sub_staff = 0',
            'bind'       => ['mobile' => $paramIn['phone']],
        ]);
        if ($phoneExist) {
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_002',
                ['mobile' => $paramIn['phone']]));
        }

        $identityExist = HrStaffInfoModel::findFirst([
            'conditions' => ' identity = :identity: and state in (1,3) and is_sub_staff = 0',
            'bind'       => ['identity' => $paramIn['credentials_num']],
        ]);
        if ($identityExist) {
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_009',
                ['credentials_num' => $paramIn['credentials_num']]));
        }
    }

    /**
     * @throws BusinessException
     */
    protected function validationBlackList($paramIn)
    {
        //[3]发送请求
        $apiClient = (new ApiClient("winhr_rpc", '', 'check_black_grey_list', $this->lang));
        $apiClient->setParams(['identity' => $paramIn['credentials_num'],'hc_id'=>$paramIn['hc_id']]);
        $result = $apiClient->execute();

        if (empty($result) || empty($result['result']['code'])) {
            throw new BusinessException($this->getTranslation()->_('server_error'));
        }
        $data = $result['result']['data'];
        if ($data['is_black_list']) {
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_008',
                ['credentials_num' => $paramIn['credentials_num']]));
        }
        if (!empty($paramIn['credentials_num'])) {
            $outsourcingBlackList = (new OutsourcingBlackListServer())->check($paramIn['credentials_num'], 'winhr', false,$this->lang);
            if ($outsourcingBlackList && $outsourcingBlackList['is_black']){
                throw new BusinessException($this->getTranslation()->_('qo_error_msg_008',
                    ['credentials_num' => $paramIn['credentials_num']]));
            }
        }
    }



    /**
     * 获取jd
     * @param $type
     * @return array
     */
    public function getJDList($type): array
    {
        $key = self::$type_setting_key_map[$type] ?? '';

        $settingServer = new SettingEnvServer();
        $qo_jds        = $settingServer->getSetVal($key, ',');
        if (empty($qo_jds)) {
            return [];
        }
        $jdList = HrJDRepository::getJDList($qo_jds);
        foreach ($jdList as &$item) {
            $item['is_need_driver_license'] = in_array($item['job_id'], $this->needDriverLicenseJd);
        }
        return $jdList;
    }

    /**
     * 获取静态枚举
     * @return array
     */
    public function enums(): array
    {
        $result['type']              = $this->getType();
        $result['relationship_list'] = $this->getRelationshipList();
        return $result;
    }

    /**
     * 其他简历信息
     * @param $resumeModel
     * @param $paramIn
     * @return bool
     */
    protected function bindOtherResumeData($resumeModel, &$paramIn): bool
    {
        $familyModel = HrFamilyModel::findFirstByResumeId($resumeModel->id);

        if (empty($familyModel)) {
            $familyModel            = new HrFamilyModel();
            $familyModel->resume_id = $resumeModel->id;
        }
        $familyModel->relationship                  = $paramIn['relationship'];
        $familyModel->relationship_first_name       = $paramIn['relationship_first_name'];
        $familyModel->relationship_last_name        = $paramIn['relationship_last_name'];
        $familyModel->relationship_mobile           = $paramIn['relationship_phone'];
        $familyModel->relationship_mobile_area_code = $this->getPhoneAreaCode();
        $familyModel->save();
        if (!empty($paramIn['credentials_img'])) {
            //驾照正面
            // '0=其他，1=本人半身照，2=身份证正面，3=身份证反面，4=户籍照第一页，5=户籍照应聘者本人信息页，6=兵役服役证明，7=成绩报告单，
//8=清白证明书，9=补充附件，10=驾驶证正面，11=驾驶证反面，12=车辆登记薄，13=车辆照片，14=车辆使用授权，
//15=签名，16=个人简历, 17=残疾证正面，18=残疾证反面, 19=最高学历证书，20=OR附件，21=承诺书，22=授权书';

            $annexModel = HrAnnexModel::find([
                'conditions' => 'type = 1 and oss_bucket_key = :resume_id: and deleted = 0 and file_type = 2',
                'bind'       => [
                    'resume_id' => $resumeModel->id,
                ],
            ]);
            $image_path = str_replace(env('img_prefix'), '', $paramIn['credentials_img']);

            $paramIn['credentials_img'] = $image_path;
            if (!empty($annexModel)) {
                foreach ($annexModel as $item) {
                    $item->deleted = 1;
                    $item->save();
                }
            }
            $annexModel = new HrAnnexModel();
            $annexModel->create([
                'oss_bucket_key'  => $resumeModel->id,
                'oss_bucket_type' => 'HR_CV_ANNEX',
                'object_key'      => $image_path,
                'type'            => 1,
                'bucket_name'     => '',
                'original_name'   => '',
                'file_type'       => 2,
            ]);
        }

        return true;
    }


    /**
     * 差异化字段绑定
     * @param $resumeModel
     * @param $paramIn
     * @param $is_create
     * @return mixed
     */
    protected function bindResumeData($resumeModel, $paramIn, $is_create)
    {
        $resumeModel->first_name           = $paramIn['first_name'];
        $resumeModel->last_name            = $paramIn['last_name'];
        $resumeModel->name                 = str_replace(' ', '', $paramIn['first_name'] . $paramIn['last_name']);
        $resumeModel->credentials_num      = $paramIn['credentials_num'];
        $resumeModel->credentials_category = 2;//身份证
        if ($is_create) {
            $resumeModel->resume_last_operator       = $paramIn['staff_id'];
            $resumeModel->resume_last_operation_time = gmdate('Y-m-d H:i:s');
            $resumeModel->recommender_staff_id       = $paramIn['staff_id'];
            $resumeModel->recommend_store_id         = $paramIn['worknode_id'];
        }
        return $resumeModel;
    }

    /**
     * 必填字段验证
     * @return string[]
     */

    protected function getDefaultValidationsField(): array
    {
        return [
            "first_name"              => "StrLenGeLe:0,50|>>>:first_name" . $this->getTranslation()->_('4038'),
            "last_name"               => "StrLenGeLe:0,50|>>>:last_name" . $this->getTranslation()->_('4037'),
            "store_id"                => "StrLenGeLe:1,50|>>>:store_id" . $this->getTranslation()->_('4028'),
            "hc_id"                   => "Required|Int",
            "phone"                   => "Required|StrLenGeLe:10,10|>>>:" . $this->getTranslation()->_('4117'),
            // 手机号
            "type"                    => 'Required|IntIn:' . implode(',',
                    array_keys(self::$type_setting_key_map)) . '|>>>:type' . $this->getTranslation()->_('reserve_type_error'),
            "expected_arrivaltime"    => 'Required|Date',
            "job_id"                  => 'Required|Int',
            "credentials_num"         => "StrLenGeLe:13,13|>>>:credentials_num error",                      //证件号
            "relationship_first_name" => "Required|StrLenGeLe:0,100|>>>:relationship_first_name error ",   // 紧急联系人名
            "relationship_last_name"  => "Required|StrLenGeLe:0,100|>>>:relationship_last_name error ",    // 紧急联系人姓
            "relationship"            => "Required|Int",                                                   // 紧急联系关系
            "relationship_phone"      => "Required|StrLenGeLe:10,10|>>>: relationship_phone error ",       // 紧急联系人手机号
            "credentials_img"         => "StrLenGeLe:1,255|>>>:credentials_img error",                     //身份证证明

        ];
    }

    protected function bindDetailData(&$result)
    {
        $list                        = array_column($this->getRelationshipList(), 'label', 'value');
        $result['relationship']      = intval($result['relationship']);
        $result['relationship_text'] = $list[$result['relationship']] ?? '';
        return [];
    }


}