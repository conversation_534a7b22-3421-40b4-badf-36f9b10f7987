<?php

namespace FlashExpress\bi\App\Modules\Th\Server;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Mail;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\LeaveScenarioModel;
use FlashExpress\bi\App\Models\backyard\StaffFaceBlacklistHitRecordModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReasonModel;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Server\StaffFaceBlacklistServer as BaseStaffFaceBlacklistServer;
use FlashExpress\bi\App\Server\WorkflowServer;

class StaffFaceBlacklistServer extends BaseStaffFaceBlacklistServer
{
    protected $hold_reason = 'investigate_the_impersonation';//调查冒名顶替


    const TMP_STAFF_TYPE_PERMANENT = 1;//正式员工
    const TMP_STAFF_TYPE_INTERN = 4;   //实习生
    const TMP_STAFF_TYPE_IC = 2;       //个人代理
    const TMP_STAFF_TYPE_OS = 3;       //外协


    protected $title_key_map = [
        self::TMP_STAFF_TYPE_PERMANENT => '21119_message_title_default',
        self::TMP_STAFF_TYPE_INTERN    => '21119_message_title_intern',
        self::TMP_STAFF_TYPE_IC        => '21119_message_title_ic_os',
        self::TMP_STAFF_TYPE_OS        => '21119_message_title_ic_os',
    ];

    protected $content_key_map = [
        self::TMP_STAFF_TYPE_PERMANENT => '21119_message_content_default',
        self::TMP_STAFF_TYPE_INTERN    => '21119_message_content_intern',
        self::TMP_STAFF_TYPE_IC        => '21119_message_content_ic_os',
        self::TMP_STAFF_TYPE_OS        => '21119_message_content_ic_os',
    ];


    protected function checkIsSendContract(): bool
    {

        return $this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID;
    }



    private function staffType(): int
    {
        if (in_array($this->staffInfo['formal'],
            [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN])) {
            $type = self::TMP_STAFF_TYPE_PERMANENT;
            if (in_array($this->staffInfo['hire_type'],
                HrStaffInfoModel::$agentTypeTogether)) {
                $type = self::TMP_STAFF_TYPE_IC;
            } elseif ($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_5) {
                $type = self::TMP_STAFF_TYPE_INTERN;
            }
        } else {
            $type = self::TMP_STAFF_TYPE_OS;
        }
        return $type;
    }


    private function getMessageTitleAndContent(): array
    {
        $params      = $this->params;
        $staffInfo   = $this->staffInfo;
        $content_key = $this->content_key_map[$this->staffType()];
        $title_key   = $this->title_key_map[$this->staffType()];
        $staff_lang  = $params['staff_lang'] ?? getCountryDefaultLang();
        $t           = $this->getTranslation($staff_lang);
        $hit_date    = substr($params['action_time'], 0, 10);
        return [
            $t->_($title_key),
            $t->_($content_key, [
                'staff_name' => $staffInfo['name'],
                'staff_id'   => $staffInfo['staff_info_id'],
                'hit_date'   => $hit_date,
                'end_date'   => date('Y-m-d', strtotime($hit_date . ' + 6 days')),
            ]),
        ];
    }

    /**
     * 给员工发通知消息
     * @return bool
     */
    protected function sendMessageToStaff(): bool
    {
        $params    = $this->params;
        $staffInfo = $this->staffInfo;
        [$title, $content] = $this->getMessageTitleAndContent();
        $sms_rpc = new ApiClient('sms_rpc', '', 'send', $params['staff_lang'] ?? getCountryDefaultLang(),
            'face_blacklist');
        $messageParams = [
            'mobile' => $staffInfo['mobile'],
            'msg'    => $title . "\n" . $content,
            'code'   => 'th',
            'delay'  => 0,
        ];
        //viber发送 如果 PH 要发短信 请粘贴这些参数
//        if (isCountry('PH')) {
//            $messageParams['nation'] = 'PH';
//            $messageParams['type'] =  0;// 文本消息， 固定类型
//            $messageParams['service_provider'] =  9;// 服务商 Viber 固定值
//        }
        $sms_rpc->setParams($messageParams);
        $sms_rpc->execute();

        $content                        = addslashes("<div style='font-size: 30px'>" . $content . '</div>');
        $msgId                          = time() . $staffInfo['staff_info_id'] . rand(1000000, 9999999);
        $paramMsg['staff_users']        = [$staffInfo['staff_info_id']];//数组 多个员工id
        $paramMsg['message_title']      = $title;
        $paramMsg['message_content']    = $content;
        $paramMsg['staff_info_ids_str'] = $staffInfo['staff_info_id'];
        $paramMsg['id']                 = $msgId;
        $msgClient                      = new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang);
        $msgClient->setParams($paramMsg);
        $msgClient->execute();

        return true;
    }


    /**
     * 给员工的管理者发通知消息
     * @param $msg_to_other_staff
     * @return bool
     */
    protected function sendMessageToManager($msg_to_other_staff): bool
    {
        $staffInfo = $this->staffInfo;

        if (!empty($staffInfo['manger'])) {
            $msg_to_other_staff[] = $staffInfo['manger'];
        }

        $regionPieceManager = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getOrganizationRegionPieceManagerId($staffInfo['sys_store_id']);

        if (!empty($regionPieceManager['region_manager_id'])) {
            $msg_to_other_staff[] = $regionPieceManager['region_manager_id'];
        }

        if (!empty($regionPieceManager['piece_manager_id'])) {
            $msg_to_other_staff[] = $regionPieceManager['piece_manager_id'];
        }
        $hrbps = (new WorkflowServer($this->lang, $this->timeZone))->findHRBP($staffInfo['node_department_id'],
            ['store_id' => $staffInfo['sys_store_id']]);

        $msg_to_other_staff = array_values(array_filter(array_unique(array_merge($msg_to_other_staff,
            explode(',', $hrbps)))));

        if (in_array($this->staffType() ,[self::TMP_STAFF_TYPE_PERMANENT,self::TMP_STAFF_TYPE_INTERN])) {
            $title = '21119_face_blacklist_msg_title_th';
        } else {
            $title = '21119_face_blacklist_msg_title_th_ic';
        }
        $this->logger->write_log(['msg_to_other_staff' => $msg_to_other_staff], 'info');
        $this->sendMessage($msg_to_other_staff, $title);
        return true;
    }


    protected function sendEmail($email_box): bool
    {
        if (empty($email_box)) {
            return true;
        }
        $lang = 'en';
        $t    = $this->getTranslation($lang);

        if (in_array($this->staffType() ,[self::TMP_STAFF_TYPE_PERMANENT,self::TMP_STAFF_TYPE_INTERN])) {
            $title = '21119_face_blacklist_email_title_th';
        } else {
            $title = '21119_face_blacklist_email_title_th_ic';
        }
        $current_base_staff_info = $this->dealStaffBaseInfo($this->staffInfo);
        $black_base_staff_info   = $this->dealStaffBaseInfo($this->matchStaffInfo);
        $send_data               = $this->getSendStaffData($current_base_staff_info, $black_base_staff_info, $lang);
        $send_data               = $this->dealEmailData($send_data);

        //发送邮件
        $email_title   = $t->_($title);
        $email_content = $t->_('21119_face_blacklist_email_content_th', $send_data);
        $sendEmail     = Mail::send(explode(';', $email_box), $email_title, $email_content);
        $this->logger->write_log("dealFaceBlacklistEmail：" . $sendEmail, 'info');
        return true;
    }

    /**
     * 修改员工状态
     * @return bool
     * @throws BusinessException
     */
    protected function handleStaffState(): bool
    {
        $params    = $this->params;
        $staffInfo = $this->staffInfo;
        if (in_array($this->staffType() ,[self::TMP_STAFF_TYPE_PERMANENT,self::TMP_STAFF_TYPE_INTERN])) {
            //正式员工是停职
            $updateStaffInfo = [
                'staff_info_id'    => $staffInfo['staff_info_id'],
                'state'            => HrStaffInfoModel::STATE_SUSPENSION,
                'stop_duties_date' => substr($params['action_time'], 0, 10),
                'stop_duty_reason' => HrStaffInfoModel::STOP_DUTY_REASON_IMPERSONATE,
                'operater'         => '-1',
            ];
        } elseif ($this->staffType() == self::TMP_STAFF_TYPE_IC) {
            //[2.1]员工改离职
            $updateStaffInfo = [
                'staff_info_id'  => $staffInfo['staff_info_id'],
                'state'          => HrStaffInfoModel::STATE_2,
                'leave_date'     => substr($params['action_time'], 0, 10),
                'leave_source'   => HrStaffInfoModel::LEAVE_SOURCE_FACE_BLACKLIST,
                'leave_reason'   => StaffLeaveReasonModel::LEAVE_REASON_80,
                'leave_scenario' => LeaveScenarioModel::LEAVE_SCENARIO_12,
                'leave_type'     => HrStaffInfoModel::LEAVE_TYPE_DISMISSAL_NO_COMPENSATION,
                'operater'       => '-1',
            ];
        } else {
            $updateStaffInfo = [
                'staff_info_id' => $staffInfo['staff_info_id'],
                'state'         => HrStaffInfoModel::STATE_2,
                'leave_date'    => substr($params['action_time'], 0, 10),
                'operater'      => '-1',
            ];
        }
        $this->doUpdateStaffInfo($updateStaffInfo);
        return true;
    }

    /**
     * 个人代理自动生成hc
     * @return bool
     */
    protected function createHC(): bool
    {
        if (in_array($this->staffInfo['formal'], [
                HrStaffInfoModel::FORMAL_1,
                HrStaffInfoModel::FORMAL_INTERN,
            ]) && $this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            $hc_id = $this->copyHc($this->staffInfo['staff_info_id']);
            if ($hc_id){
                $hitRecord = StaffFaceBlacklistHitRecordModel::findFirst($this->params['hit_record_id']);
                $hitRecord->create_hc_id = $hc_id;
                $hitRecord->save();
            }
        }
        return false;
    }





}