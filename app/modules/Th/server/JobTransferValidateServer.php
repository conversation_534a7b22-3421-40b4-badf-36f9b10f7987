<?php

namespace FlashExpress\bi\App\Modules\Th\Server;


use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Server\HcHireTypeConfigServer;
use FlashExpress\bi\App\Server\JobTransferValidateServer as BaseJobTransferValidateServer;
use FlashExpress\bi\App\Server\SysStoreServer;

class JobTransferValidateServer extends BaseJobTransferValidateServer
{

    const CREATE_RULES = [
        'checkTransferStaffId',             //被转岗工号是否存在
        'checkTransferStaffOnJobState',     //被转岗人是否在职
        'checkTransferStaffAgent',          //被转岗人是否个人代理
        'checkTransferStaff',               //不能为自己转岗
        'checkInProcessTransfer',           //是否存在进行中的转岗
        'checkProbation',                   //试用期不能转岗
        'checkPermission',                  //转岗权限
        'checkHandoverStaffId',             //工作交接人工号是否存在
        'checkHandoverStaffOnJobState',     //工作交接人是否在职
        'checkHandoverStaffValid',          //工作交接人是否有效
        'checkAfterDate',                   //校验转岗日期
        'checkAfterStoreId',                //校验转岗网点
    ];

    //特殊转岗
    const SPECIAL_CHECK_RULES = [
        'checkTransferStaffId',             //被转岗工号是否存在
        'checkTransferStaffOnJobState',     //被转岗人是否在职
        'checkTransferStaffAgent',          //被转岗人是否个人代理
        'checkInProcessTransfer',           //是否存在进行中的转岗
        'checkSpecialPermission',           //转岗权限
        'checkFrontLine',                   //是否一线职位
        'checkAfterDate',                   //校验转岗日期
        'checkAfterStoreId',                //校验转岗网点
    ];

    const DEFAULT_RULES = [
        'checkTransferStaffId',             //被转岗工号是否存在
        'checkTransferStaffOnJobState',     //被转岗人是否在职
        //'checkTransferStaffAgent',          //被转岗人是否个人代理
        'checkTransferStaff',               //不能为自己转岗
        'checkInProcessTransfer',           //是否存在进行中的转岗
        'checkProbation',                   //试用期不能转岗
        'checkPermission',                  //转岗权限
    ];

    const CHECK_RULES = [
        'checkTransferStaffId',             //被转岗工号是否存在
        'checkTransferStaffOnJobState',     //被转岗人是否在职
        //'checkTransferStaffAgent',          //被转岗人是否个人代理
        'checkTransferStaff',               //不能为自己转岗
        'checkInProcessTransfer',           //是否存在进行中的转岗
        //'checkProbation',                   //试用期不能转岗
        'checkPermission',                  //转岗权限
        'checkFrontLine',                   //是否一线职位
    ];

    /**
     * @description 校验网点是否营业
     * @return int
     */
    public function checkAfterStoreId()
    {
        if ($this->getAfterStoreId() == enums::HEAD_OFFICE_ID){
            return ErrCode::SUCCESS;
        }
        if (empty($this->getAfterStoreId())) {
            return ErrCode::SUCCESS;
        }
        $storeInfo = (new SysStoreServer())->getStoreInfoByid($this->getAfterStoreId());
        if (empty($storeInfo) || $storeInfo['use_state'] != SysStoreModel::USE_STATE_YES){
            return ErrCode::JOB_TRANSFER_STORE_ID_ERROR;
        }
        return ErrCode::SUCCESS;
    }

    /**
     * @description 校验被转岗人在职状态
     * @return int
     * @checkRule
     */
    protected function checkTransferStaffAgent(): int
    {
        $staffInfo = $this->getJobTransferStaffInfo();
        if ($staffInfo['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return ErrCode::SUCCESS;
        }

        //个人代理校验职位
        return $this->checkTransferStaffAgentJobTitle();
    }
}