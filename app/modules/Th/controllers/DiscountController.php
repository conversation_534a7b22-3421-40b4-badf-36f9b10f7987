<?php
namespace FlashExpress\bi\App\Modules\Th\Controllers;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Modules\Th\Server\AuditServer;
use FlashExpress\bi\App\Server\DiscountServer;
use Exception;

class DiscountController extends BaseController
{
    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn)){
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 年底促销
     * 查询客户列表
     */
    public function findCustomerForEndSaleAction()
    {
        //[1]入参校验
        $paramIn = $this->request->get();

        //[2]数据验证
        $validations = [
            "condition" => "Required|StrLenGeLe:1,80",
        ];
        $this->validateCheck($paramIn, $validations);

        try {
            $logger = $this->getDI()->get('logger');

            //请求客户列表
            $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.DiscountApplySvc','getDiscountApplyUser', $this->lang);
            $params=[
                "staff_info_id"=> intval($this->userinfo['staff_id']),
                "store_id"=> strval($this->userinfo['organization_id']),
                "key_word"=> $paramIn['condition'],
                "source"=> "BY"
            ];

            $logger->write_log('findCustomerForEndSale request: ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
            $fle_rpc->setParams($params);
            $fle_return = $fle_rpc->execute();
            $logger->write_log('findCustomerForEndSale response: ' . json_encode($fle_return, JSON_UNESCAPED_UNICODE), 'info');

            if (empty($fle_return['result'])) { //                没有查到客户信息
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_no_costomer')));
            }
            $translations = $this->getTranslation();
            foreach ($fle_return['result'] as $K => $item) {
                $fle_return['result'][$K]['price_type_text'] = $translations->_('price_type_' . $item['price_type']) ?? '';
                $fle_return['result'][$K]['created_at'] = isset($item['created_at']) ? date("Y-m-d", $item['created_at']) : '';
            }

        } catch (\Exception $e) {
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        $this->jsonReturn(self::checkReturn(['data' => ['dataList' => $fle_return['result']]]));
    }

    /**
     * 获取附件详情
     */
    public function getFileDetailForEndSaleAction()
    {
        //[1]入参校验
        $paramIn = $this->request->get();

        //[2]数据验证
        $validations = [
            "customer_id" => "Required|StrLenGeLe:1,80",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_info_id']     = $this->userinfo['staff_id'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];

        try {
            $logger = $this->getDI()->get('logger');
            $logger->write_log('getDetailForEndSale request: ' . json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');
            $detail = (new DiscountServer($this->lang,$this->timezone))->getFileDetailForEndSale($paramIn);
            //[3]数据返回
            $logger->write_log('getDetailForEndSale response: ' . json_encode($detail, JSON_UNESCAPED_UNICODE), 'info');

            $this->jsonReturn(self::checkReturn(['data' => ['file_path'=>$detail]]));

        } catch (\Exception $e) {
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

    }

    /**
     * 年底促销
     * 提交
     */
    public function createConfigForEndSaleAction()
    {
        //[1]入参校验
        $paramIn = $this->paramIn;
        $paramIn['staff_info_id']     = $this->userinfo['staff_id'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['positions']         = $this->userinfo['positions'];


        //[2]数据验证 PHP不做数据值的校验
        $validations = [
            "customer_id" => "Required|StrLenGeLe:1,80",
            "gradient_parcel_discount_detail" => "Required|Arr",
            'file_path'=>"Required|Arr",
        ];

        $logger = $this->getDI()->get('logger');
        $logger->write_log('createConfigForEndSale request: ' . json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');
        $this->validateCheck($paramIn, $validations);

        try {

            $check = (new AuditServer)->getFreightDiscYESPermission($paramIn);

            if(!$check){
                //todo
                $this->jsonReturn(self::checkReturn(-3, 'disable'));
            }

            //申请操作
            $res = $this->atomicLock(function () use ($paramIn) {
                return (new DiscountServer($this->lang,$this->timezone))->addFreightDiscountForEndSale($paramIn);
            }, 'add_discount_yes_' . $paramIn['customer_id'], 3, false);

            $logger->write_log('findCustomerForEndSale response: ' . json_encode($res, JSON_UNESCAPED_UNICODE), 'info');

        } catch (\Exception $e) {
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        $this->jsonReturn(self::checkReturn(1));
    }

}