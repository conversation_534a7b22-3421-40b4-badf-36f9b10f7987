<?php
namespace FlashExpress\bi\App\Modules\Th\Controllers;

use FlashExpress\bi\App\Controllers\WmsController AS GlobalControllerBase;
use FlashExpress\bi\App\Modules\Th\Server\WmsServer;
use Exception;

class WmsController extends GlobalControllerBase
{

	/**
	 * 商品订单创建
	 * @Access  public
	 * @Param   request
	 * @Return  array
	 */
	public function addOrderAction()
	{
		try {
			//[1]入参
			$paramIn = $this->paramIn;
			$userinfo = $this->userinfo;
			$validations = [
				"reason_application" => "Required|StrLenGeLe:10,500|>>>:" . $this->getTranslation()->_('7102'),
			];
			$this->validateCheck($paramIn, $validations);

			$barCodeArr = array_column($paramIn['goods_order_detail'], 'bar_code');
			if (count($barCodeArr) != count(array_unique($barCodeArr))) {
				return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('7103')));
			}
			$this->getDI()->get("logger")->write_log("wmsAddOrder: " . json_encode($paramIn, JSON_UNESCAPED_UNICODE), "info");
			//如果是这些条件 没人每月只可提交一次
			$isManager = (new WmsServer($this->lang, $this->timezone))->isMoons($userinfo['id']);
			if ($isManager) {
				//每人每月可提交一次申请
				$existsWeek = (new WmsServer($this->lang, $this->timezone))->existsMonth($userinfo);
				if ($existsWeek > 0) {
					return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('7105')));
				}
			}
			//如果是这些人可以提交 4 次
			$isManagerFour = (new WmsServer($this->lang, $this->timezone))->isManagerFour($userinfo['id']);
			if($isManagerFour){
				//每人每月可提交4次申请
				$existsWeek = (new WmsServer($this->lang, $this->timezone))->existsMonth($userinfo);
				if ($existsWeek > 3) {
					return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('7105')));
				}
			}
			//original_order_status 是原来状态 order_status修改后状态
			$paramIn['original_order_status'] = 1;
			$returnArr = (new WmsServer($this->lang, $this->timezone))->addOrderS($paramIn, $userinfo);

		} catch (\Exception $e) {
			$this->wLog('order_err', $e->getMessage() . json_encode($paramIn) . json_encode($userinfo), 'wms');
			return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('2109')));
		}
		$this->jsonReturn($returnArr);

	}

    /**
     * 商品订单审核
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkOrderAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        //兼容前端参数
        $paramIn['order_status'] = isset($paramIn['order_status']) ? $paramIn['order_status'] : $paramIn['status'];
        $paramIn['order_id'] = isset($paramIn['order_id']) ? $paramIn['order_id'] : $paramIn['audit_id'];

        //[2]业务处理
        try{
            $res = (new WmsServer($this->lang, $this->timezone))->updateWmsOrder($paramIn, $userinfo);
            if ($res) {
                $returnArray['data'] = $paramIn['order_id'];
            }
            //[3]数据返回
            $this->jsonReturn(self::checkReturn($returnArray));
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('wms_checkorder file ' . $e->getFile() . ' line ' . $e->getLine() . ' msg ' . $e->getMessage() , 'info');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

}
