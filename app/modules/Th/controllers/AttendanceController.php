<?php

namespace FlashExpress\bi\App\Modules\Th\Controllers;

use FlashExpress\bi\App\Controllers\AttendanceController as BaseAttendanceController;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Server\AttendanceImageVerifyServer;

class AttendanceController extends BaseAttendanceController
{
    /**
     * 人脸照片对比接口
     */
    public function image_verifyAction()
    {
        $parameters                  = $this->paramIn;
        $headerData                  = $this->request->getHeaders();
        $parameters['device']        = $this->processingDefault($headerData, 'User-Agent');
        $parameters['user_info']     = $this->userinfo;
        $parameters['staff_info_id'] = $this->userinfo['id'];
        /**
         * @see AttendanceImageVerifyServer::verifyImage()
         */
        (new AttendanceImageVerifyServer($this->lang))->verifyImageUseLock($parameters);
        $this->jsonReturn(['code' => ErrCode::SUCCESS, 'message' => "success", 'data' => null]);
    }
}