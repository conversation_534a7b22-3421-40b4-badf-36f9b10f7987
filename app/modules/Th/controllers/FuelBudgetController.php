<?php

namespace FlashExpress\bi\App\Modules\Th\Controllers;

use FlashExpress\bi\App\Models\oa\SysDepartmentPcCode;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Server\AssetServer;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\ResignServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\TicketServer;
use FlashExpress\bi\App\Server\FuelBudgetServer;
use Exception;

class FuelBudgetController extends BaseController
{

    public $staff_id;
    public $miss_args;
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $userinfo=$this->userinfo;
        $this->staff_id=$userinfo['id'];
        $this->miss_args=$this->getTranslation()->_('miss_args');

        $this->server  = [
            'audit' => new AuditServer($this->lang,$this->timezone),
            'asset' => new AssetServer($this->lang,$this->timezone),
            'staff'   => new StaffServer(),
            'ticket' =>new TicketServer($this->lang,$this->timezone,$this->userinfo),
            'resign'=>new ResignServer($this->lang,$this->timezone)
        ];

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();

    }

    /**
     * 费用所属中心
     * @return false|string|void
     */
    public function costCentreAction()
    {
        try{
            $nodeSn = (new FuelBudgetServer($this->lang,$this->timezone))->get_cost_centre($this->userinfo);

            return $this->jsonReturn($this->checkReturn(array('data' => $nodeSn)));
        }catch (\Exception $e){
            $logger = $this->getDI()->get('logger');
            $logger->write_log("backyardController:un_read_and_audit-" .$e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }
}
