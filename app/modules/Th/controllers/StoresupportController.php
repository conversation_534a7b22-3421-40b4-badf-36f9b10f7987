<?php


namespace FlashExpress\bi\App\Modules\Th\Controllers;

use Exception;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Modules\Th\Server\StoreSupportServer;

class StoresupportController extends BaseController
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    //网点支援申请
    public function addStoreApplySupportAction() {
        $paramIn  = $this->paramIn;
        $paramIn['param'] = $this->userinfo;
        $paramIn['param']['position_category'] = $this->userinfo['positions'];
        $paramIn['param']['store_id'] = $this->userinfo['organization_id'];
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $paramIn['organization_id'] = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $logger = $this->getDI()->get('logger');

        try {
            $validations = [
                "job_title_id"      => "Required|IntIn:13,16,110,37|>>>:" . $this->getTranslation()->_('3008'),
                "employment_date"   => "Required|Date",
                "employment_days"   => "Required|IntGeLe:1,10",
                "demand_num"        => "Required|IntGeLe:1,50|>>>:" . "'demand_num' invalid input",
                "shift_id"          => "Required|Int",
                "store_id"          => "Required|StrLenGeLe:1,10",
                "reason_type"       => "Required|IntIn:1,2,3,99",
            ];
            if ($paramIn['reason_type'] == 99) { //reason_type=99 校验
                $validations = array_merge($validations, ["reason" => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1019')]);
            }
            $this->validateCheck($this->paramIn, $validations);

            $returnArr = (new StoreSupportServer($this->lang, $this->timezone))->addStoreApplySupport($paramIn);
            return $this->jsonReturn($returnArr);
        } catch (BusinessException $b_e) {
            $logger->write_log("Store_SupportController:addStoreApplySupport:request:" . json_encode($paramIn) . ":error:" . $b_e->getMessage(),'info');
            return $this->jsonReturn(self::checkReturn(-3, $b_e->getMessage()));
        } catch (Exception $e) {
            $logger->write_log("Store_SupportController:addStoreApplySupport:request:" . json_encode($paramIn) . ":error:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    //网点支援申请 审批更新
    public function updateStoreApplySupportAction() {
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['name']  = $this->userinfo['name'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['param']['position_category']  = $this->userinfo['positions'];
        $paramIn['param']['store_id']  = $this->userinfo['organization_id'];
        $paramIn['userinfo']  = $this->userinfo;

        $logger = $this->getDI()->get('logger');
        $cache = $this->getDI()->get('redis');

        $redis_key  = 'lock_update_store_apply_support_' . $paramIn['audit_id'];
        try {
            $validations = [
                "audit_id"      => "Required|Int",
                "status"        => "Required|Int",
                "reject_reason" => "Required|StrLenGeLe:0,500",
                "demand_num"    => "Required|IntGeLe:1,50",
            ];
            $this->validateCheck($this->paramIn, $validations);

            $returnArr = $this->atomicLock(function () use ($paramIn) {
                return  (new StoreSupportServer($this->lang, $this->timezone))->updateStoreApplySupport($paramIn);
            }, $redis_key, 5);

            if ($returnArr === false) { //没有获取到锁
                return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
            }

            return $this->jsonReturn($returnArr);
        } catch (Exception $e) {
            $cache->delete($redis_key); //释放锁
            if ($e->getCode() == '1000') {
                $logger->write_log("Store_SupportController:updateStoreApplySupport:auditId:". $paramIn['audit_id'] . $e->getMessage(), 'info');
            } else {
                $logger->write_log("Store_SupportController:updateStoreApplySupport:auditId:". $paramIn['audit_id'] . $e->getMessage(), 'notice');
            }
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    //获取班次 职位 申请原因等信息
    public function getStoreApplySysInfoAction() {
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        try {
            $returnArr = (new StoreSupportServer($this->lang, $this->timezone))->getStoreApplySysInfo();
            return $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $returnArr]]));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("Store_SupportController:getStoreApplyInfo:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    //获取可申请支援的网点列表
    public function getSupportStoreListAction() {
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        try {
            $returnArr = (new StoreSupportServer($this->lang, $this->timezone))->getSupportStoreList($paramIn);
            return $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $returnArr]]));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("Store_SupportController:getStoreApplySysInfo:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }
}