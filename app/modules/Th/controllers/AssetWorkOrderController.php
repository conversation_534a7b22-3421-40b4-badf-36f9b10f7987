<?php
namespace FlashExpress\bi\App\Modules\Th\Controllers;

use FlashExpress\bi\App\Enums\AssetWorkOrderEnums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\AssetWorkOrderServer;

/**
 * 资产工单-控制器
 * Class AssetWorkOrderController
 * @package FlashExpress\bi\App\Modules\Th\Controllers
 */
class AssetWorkOrderController extends BaseController
{
    protected $paramIn;
    protected $assetWorkOrderServer;

    //工单ID
    const ID_VALIDATION = 'Required|IntGe:1|>>>:id param error';
    //1提交者，2审批者
    const FROM_VALIDATION = 'Required|IntIn:'.AssetWorkOrderEnums::TYPE_SUBMIT.','.AssetWorkOrderEnums::TYPE_AUDIT.'|>>>:from param error';
    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->assetWorkOrderServer = new AssetWorkOrderServer($this->lang, $this->timezone);
    }

    /**
     * 资产工单-提交人信息
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63712
     */
    public function getDefaultAction()
    {
        $data = $this->assetWorkOrderServer->getDefault($this->userinfo);
        return $this->jsonReturn($data);
    }

    /**
     * 资产工单-问题类型
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63722
     */
    public function getQuestionItemAction()
    {
        $data = $this->assetWorkOrderServer->getQuestionItem();
        return $this->jsonReturn($data);
    }

    /**
     * 资产工单-问题所在仓
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63727
     */
    public function getStoreListAction()
    {
        // [1] 获取参数进行验证
        $paramIn     = $this->paramIn;
        $validations = [
            "category_id" => "Required|IntGt:0|>>>:category_id param error", // categoryId
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn = array_only($paramIn, array_keys($validations));
        $data = $this->assetWorkOrderServer->getStoreList($paramIn);
        return $this->jsonReturn($data);
    }

    /**
     * 资产工单-提交
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63792
     */
    public function addAction()
    {
        $paramIn = $this->paramIn;
        //入参校验
        $validations = [
            "type_id" => "Required|IntGt:0|>>>:question_type_id param error", // 问题类型 必填
            "store_id" => "Required|StrLenGeLe:1,10|>>>:store_id".$this->getTranslation()->_('max_char_len', ['len' => 10]),
            "line_id" => "Required|StrLenGeLe:1,50|>>>:line_id".$this->getTranslation()->_('max_char_len', ['len' => 50]),
            "mobile" => "StrLenGeLe:0,50|>>>:".$this->getTranslation()->_('max_char_len', ['len' => 50]),
            "info" => "Required|StrLenGeLe:1,500|>>>:info".$this->getTranslation()->_('max_char_len', ['len' => 500]),
            "pics" => "Arr|ArrLenLe:10|>>>:pics".$this->getTranslation()->_('max_pics_10'), // 最多10个 可以为空
        ];
        $this->validateCheck($paramIn, $validations);

        //pic数组格式的验证
        if (!empty($paramIn['pics'])) {
            $ret = (new AssetWorkOrderServer())->checkPics($paramIn['pics']);
            if (is_bool($ret) && false === $ret) {
                $this->jsonReturn(["code" => ErrCode::FAIL, "msg" => "pic param error", 'data' => []]);
            }
        }

        //业务逻辑处理
        $data = (new AssetWorkOrderServer())->addUseLock($paramIn, $this->userinfo);
        return $this->jsonReturn($data);
    }

    /**
     * 资产工单-获取回复数\待回复数
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63732
     */
    public function getNumsAction()
    {
        //获取参数进行验证
        $paramIn     = $this->paramIn;
        $validations = [
            "from" => "IntIn:".AssetWorkOrderEnums::TYPE_VALIDATION_RULE."|>>>:" . $this->getTranslation()->_('miss_args'),//0默认全部，1提交人，2审批人
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn = array_only($paramIn, array_keys($validations));
        $data = $this->assetWorkOrderServer->getAssetWorkOrderData($this->userinfo, $paramIn['type'] ?? AssetWorkOrderEnums::TYPE_ALL);
        unset($data['is_asset_work_order']);
        return $this->jsonReturn($this->checkReturn(["data" => $data]));
    }

    /**
     * 资产工单-获取待回复、已回复、已关闭工单列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63797
     */
    public function listAction()
    {
        $paramIn = $this->paramIn;
        $validations = [
            'status' => "Required|IntIn:".enums::TICKET_STATUS_WAIT_REPLY.",".enums::TICKET_STATUS_REPLY.",".enums::TICKET_STATUS_CLOSED."|>>>:status param error", // 1待回复，2已回复，3已关闭
            'from' => self::FROM_VALIDATION,
            'type_id' => 'IntGe:0',
            'page_num'  => "IntGe:1",
            'page_size' => "IntGe:1",
        ];
        $this->validateCheck($paramIn, $validations);

        $paramIn = array_only($paramIn, array_keys($validations));
        $paramIn['page_num'] = $this->processingDefault($paramIn, 'page_num', 2, AssetWorkOrderEnums::PAGE_NUM);
        $paramIn['page_size'] = $this->processingDefault($paramIn, 'page_size', 2,AssetWorkOrderEnums::PAGE_SIZE);
        $data = $this->assetWorkOrderServer->list($paramIn, $this->userinfo);
        return $this->jsonReturn($data);
    }

    /**
     * 资产工单-详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63817
     */
    public function detailAction()
    {
        $paramIn = $this->paramIn;
        $validations = [
            'id' => self::ID_VALIDATION, // 工单id
            'from' => self::FROM_VALIDATION,
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn = array_only($paramIn, array_keys($validations));
        $result = $this->assetWorkOrderServer->detail($paramIn, $this->userinfo);
        return $this->jsonReturn($result);
    }

    /**
     * 资产工单-沟通记录
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63857
     */
    public function getLogListAction()
    {
        $paramIn = $this->paramIn;
        $validations = [
            'id' => self::ID_VALIDATION,
            'from' => self::FROM_VALIDATION,
            'page_num'  => "IntGe:1",
            'page_size' => "IntGe:1",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn = array_only($paramIn, array_keys($validations));
        $paramIn['page_num'] = $this->processingDefault($paramIn, 'page_num', 2, AssetWorkOrderEnums::PAGE_NUM);
        $paramIn['page_size'] = $this->processingDefault($paramIn, 'page_size', 2,AssetWorkOrderEnums::PAGE_SIZE);
        $data = $this->assetWorkOrderServer->getLogList($paramIn, $this->userinfo);
        return $this->jsonReturn($data);

    }

    /**
     * 资产工单-关闭
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63917
     */
    public function closeAction()
    {
        $paramIn = $this->paramIn;
        $validations = [
            'id' => self::ID_VALIDATION,
            'from' => self::FROM_VALIDATION,
            'close_reason' => "Required|StrLenGeLe:1,500|>>>:".sprintf($this->getTranslation()->_('parameter_length'), 'close_reason', 1, 500),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn = array_only($paramIn, array_keys($validations));
        $result = $this->assetWorkOrderServer->closeUseLock($paramIn, $this->userinfo);
        return $this->jsonReturn($result);
    }

    /**
     * 资产工单-回复
     * @api https://yapi.flashexpress.pub/project/93/interface/api/63897
     */
    public function replyAction()
    {
        $paramIn = $this->paramIn;
        $validations = [
            "id"   => self::ID_VALIDATION,
            'from' => self::FROM_VALIDATION,
            "mark" => "StrLenGeLe:0,500|>>>:".$this->getTranslation()->_('max_char_len', ['len' => 500]),
            "pics" => "Arr|ArrLenLe:5|>>>:".$this->getTranslation()->_('max_pics_5'), // 最多5个 可以为空
        ];
        $this->validateCheck($paramIn, $validations);

        //如果 mark 和 图片都为空,则给出提示
        if (empty($paramIn['mark']) && empty($paramIn['pics'])) {
            $this->jsonReturn(["code" => ErrCode::FAIL, "msg" => "please enter the content", 'data' => []]);
        }

        //pic数组格式的验证
        if (!empty($paramIn['pics'])) {
            $ret = $this->assetWorkOrderServer->checkPics($paramIn['pics']);
            if (is_bool($ret) && false === $ret) {
                $this->jsonReturn(["code" => ErrCode::FAIL, "msg" => "pic param error", 'data' => []]);
            }
        }
        $paramIn = array_only($paramIn, array_keys($validations));
        // [2] 业务逻辑处理
        $result = $this->assetWorkOrderServer->replyUseLock($paramIn, $this->userinfo);
        return $this->jsonReturn($result);
    }
}
