<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 10/26/21
 * Time: 11:05 AM
 */

namespace FlashExpress\bi\App\Modules\Th\Controllers;

use FlashExpress\bi\App\Controllers\OsPriceController as osBaseController;
use Exception;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Server\OsPriceServer;
use FlashExpress\bi\App\Server\SysStoreServer;

class OsPriceController extends osBaseController
{
    public $job_ids = array(13,110,1000);// 多一个1000

    public function initialize()
    {
        parent::initialize();
    }

    //枚举接口
    //泰国追加DC类型网点
    //https://flashexpress.feishu.cn/docx/EmcZdBFpyoJt7BxS5azc6gJhnig
    public function get_enumAction()
    {
        $data['store_category'] = [
            ['id' => SysStoreServer::CATEGORY_DC, 'name' => 'DC'],
            ['id' => SysStoreServer::CATEGORY_SP, 'name' => 'SP'],
            ['id' => SysStoreServer::CATEGORY_BDC, 'name' => 'BDC'],
        ];
        $job_ids = $this->job_ids;

        //不确定 是指定 两个快递员职位 还是 包含courier的所有职位 先用这两个
        $data['job_title'] = HrJobTitleModel::find([
            'conditions' => "id in ({ids:array}) ",
            'columns' => 'id,job_name',
            'bind' => ['ids' => $job_ids]
        ])->toArray();
        $this->jsonReturn(self::checkReturn(['data' => $data]));
    }
}