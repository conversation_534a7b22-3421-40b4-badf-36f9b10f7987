<?php

namespace FlashExpress\bi\App\Modules\Th\Controllers;

use app\enums\InsuranceBeneficiaryEnums;
use FlashExpress\bi\App\Controllers\StaffController as BaseStaffController;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInsuranceBeneficiaryModel;
use FlashExpress\bi\App\Modules\Th\Server\StaffInsuranceBeneficiaryServer;

class StaffController extends BaseStaffController
{
    /**
     * 保存保险受益人
     * @return void
     * @throws ValidationException
     */
    public function saveInsuranceBeneficiaryAction()
    {
        $params = $this->paramIn;

        $valid = [
            'marital'                               => 'Required|IntIn:' . implode_array_keys(HrStaffInsuranceBeneficiaryModel::$marita_list) . '|>>>:' . $this->getTranslation()->_('miss_args_field',['field' => 'marital']),
            'staff_sign_img'                        => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args_field',['field' => 'staff_sign_img']),
            'is_submit'                             => 'Required|IntIn:' . InsuranceBeneficiaryEnums::INSURANCE_BENEFICIARY_UNSUBMITTED . ',' . InsuranceBeneficiaryEnums::INSURANCE_BENEFICIARY_SUBMITTED . '|>>>:' . $this->getTranslation()->_('miss_args_field',['field' => 'is_submit']),
            'beneficiary'                           => 'Required|Arr|ArrLenGeLe:1,3|>>>:' . $this->getTranslation()->_('miss_args_field',['field' => 'beneficiary']),
            'beneficiary[*].relation'               => 'Required|IntIn:' . implode_array_keys(HrStaffInsuranceBeneficiaryModel::$relation_list) . '|>>>:' . $this->getTranslation()->_('miss_args_field',['field' => 'relation']),
            'beneficiary[*].beneficiary_name'       => 'Required|StrLenGeLe:1,50|>>>:' . $this->getTranslation()->_('miss_args_field',['field' => 'beneficiary_name']),
            'beneficiary[*].beneficiary_proportion' => 'Required|IntGeLe:1,100|>>>:' . $this->getTranslation()->_('miss_args_field',['field' => 'beneficiary_proportion']),
        ];

        if ($params['is_submit'] == InsuranceBeneficiaryEnums::INSURANCE_BENEFICIARY_SUBMITTED) {
            $valid['annex_path'] = 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args_field',['field' => 'annex_path']);
        }

        $this->validateCheck($params, $valid);

        //计算是否为100
        if (!empty($params['beneficiary'])) {
            if (array_sum(array_column($params['beneficiary'], 'beneficiary_proportion')) != 100) {
                throw new ValidationException($this->getTranslation()->_('beneficiary_proportion_sum_not_100'));
            }
        }

        $params['staff_info_id'] = $this->userinfo['id'];

        $result = (new StaffInsuranceBeneficiaryServer($this->lang, $this->timezone))->setLockConf(10)->saveInsuranceBeneficiaryUseLock($params);

        if (!$result) {
            throw new ValidationException('Insurance Beneficiary Error');
        }

        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }
}