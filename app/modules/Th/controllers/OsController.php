<?php

namespace FlashExpress\bi\App\Modules\Th\Controllers;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Controllers\OsController as osBaseController;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\Th\Server\OsStaffServer;
use Exception;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;

class OsController extends osBaseController
{


    /**
     * 获取可申请职位下拉列表
     */
    public function getOsJobTitleListAction() {

        //[1]传入参数
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        $validations = [
            "type" => "Required|IntIn:1,2,3",
        ];
        $this->validateCheck($this->paramIn, $validations);

        $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getOsJobTitleList($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));

    }

    /**
     * 获取班次、申请职位、申请原因列表；所属部门；
     */
    public function getShiftListAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //[2]业务处理
        try{
            $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getRequestList($paramIn);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("OsController:getShiftList:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        return $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $returnArr]]));
    }

    /**
     * 添加外协员工申请
     * @param int    job_id             外协职位
     * @param string employment_date    雇佣日期
     * @param int    employment_days    雇佣天数
     * @param int    shift_id           班次ID
     * @param int    demend_num         申请人数
     * @param string reason             申请原因
     * @return json
     */
    public function addOsStaffAction()
    {
        //[1]入参校验
        $paramIn                               = $this->paramIn;
        $paramIn['param']                      = $this->userinfo;
        $paramIn['param']['position_category'] = $this->userinfo['positions'];
        $paramIn['param']['store_id']          = $this->userinfo['organization_id'];
        $paramIn['staff_id']                   = $this->userinfo['staff_id'];
        $paramIn['organization_id']            = $this->userinfo['organization_id'];
        $paramIn['organization_type']          = $this->userinfo['organization_type'];
        $logger                                = $this->getDI()->get('logger');

        $hubOsRoles = UC('outsourcingStaff')['hubOsRoles'];
        //ffm 来源订单
        $server = new OsStaffServer($this->lang, $this->timezone);
        //[2]数据验证
        if (!isset($paramIn['os_type']) ||
            empty($paramIn['os_type']) ||
            !in_array($paramIn['os_type'], [
                enums::$os_staff_type['normal'],
                enums::$os_staff_type['long_term'],
                enums::$os_staff_type['motorcade'],
            ])) {
            throw new ValidationException("'os_type' invalid input");
        }
        $pdcManagerList = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getManagerByDepartmentIdFromCache([OsStaffServer::DEPARTMENT_ID_PDC_OPERATIONS]);

        $is_ffm_os = false;
        switch ($paramIn['os_type']) {
            case enums::$os_staff_type['normal']:
                //新增来源 ffm 申请
                $ffm_os_job_title =  array_column($server->getFFMOsJobTitle(),'job_id');
                $is_ffm_os = in_array($paramIn['job_id'],$ffm_os_job_title);
                if($is_ffm_os){
                    $validation_sub = [
                        "employment_days" => "Required|IntGeLe:1,7",
                        "demend_num"      => "Required|IntGeLe:1,200|>>>:"."'demend_num' invalid input",
                    ];
                }else

                //hub 外协工单，分拨经理 提交申请校验规
                //PDC 负责人
                if (array_intersect($hubOsRoles, $this->userinfo['positions']) || in_array($paramIn['staff_id'], $pdcManagerList)) {
                    $validation_sub = [
                        "job_id"          => "Required|IntIn:".enums::$job_title['outsource']."|>>>:".$this->getTranslation()->_('3008'),
                        "employment_days" => "Required|IntIn:1",
                        "demend_num"      => "Required|IntGeLe:1,200|>>>:"."'demend_num' invalid input",
                    ];
                } else {
                    $validation_sub = [
                        "job_id"          => "Required|IntIn:98,111,473,13,452,110,271,1000|>>>:".$this->getTranslation()->_('3008'),
                        "employment_days" => "Required|IntGeLe:1,7",
                        "demend_num"      => "Required|IntGeLe:1,50|>>>:"."'demend_num' invalid input",
                    ];
                }

                break;
            case enums::$os_staff_type['long_term']:
                if (array_intersect($hubOsRoles, $this->userinfo['positions'])) {
                    $validation_sub = [
                        "job_id"          => "Required|IntIn:".enums::$job_title['security_outsource']."|>>>:".$this->getTranslation()->_('3008'),
                        "employment_days" => "Required|IntGeLe:15,90",
                        "demend_num"      => "Required|IntGeLe:1,200|>>>:"."'demend_num' invalid input",
                    ];
                } else {
                    $validation_sub = [
                        "job_id"          => "Required|IntIn:271,98,473|>>>:".$this->getTranslation()->_('3008'),
                        "employment_days" => "Required|IntGeLe:90,365|>>>:".$this->getTranslation()->_('err_msg_more_days'),
                        "demend_num"      => "Required|IntGeLe:1,50|>>>:"."'demend_num' invalid input",
                    ];
                }
                break;
            case enums::$os_staff_type['motorcade']:
                $validation_sub = [
                    "job_id"          => "Required|IntIn:110|>>>:".$this->getTranslation()->_('3008'),
                    "employment_days" => "Required|IntGeLe:1,7",
                    "demend_num"      => "Required|IntGeLe:1,100|>>>:"."'demend_num' invalid input",
                ];
                break;
            default:
                $validation_sub = [];
                break;
        }

        //[3]数据验证
        //短期外协的可选雇用日期为从申请日期的第二天起+7天
        //长期外协的可选雇用日期为从申请日期的第二天起+90日
        $dateFrom = date("Y-m-d", time() + 86400);
        $dateTo   = $paramIn['os_type'] == enums::$os_staff_type['normal']
            ? date("Y-m-d", time() + 8 * 86400)
            : date("Y-m-d", time() + 91 * 86400);
        $validations = [
            "employment_date" => "Required|DateFromTo:{$dateFrom},{$dateTo}|>>>:".$this->getTranslation()->_('err_msg_invalid_date'),
            "shift_id"        => "Required|Int",
            "reason_type"     => "Required|IntGeLe:0,8",
        ];

        //hub 外协工单，分拨经理 提交申请校验规则
        // pdc
        if ($is_ffm_os || array_intersect($hubOsRoles, $this->userinfo['positions']) || in_array($paramIn['staff_id'], $pdcManagerList)) {
            //今日或次日
            $dateFrom                       = date("Y-m-d");
            $dateTo                         = date("Y-m-d", strtotime("+6 day"));
            $validations["employment_date"] = "Required|DateFromTo:{$dateFrom},{$dateTo}|>>>:employment_date invalid input";
            $validations['need_remark']     = "StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('os_need_remark_error');
        }

        if (in_array($paramIn['reason_type'], [0, 2])) { //离职或其他原因需验证reason字段[原因备注字段]
            $valid       = [
                "reason" => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('1019'),
            ];
            $validations = array_merge($validations, $valid);
        }
        $this->validateCheck($this->paramIn, array_merge($validations, $validation_sub));


        //[4]业务处理
        /**
         * @see OsStaffServer::addOsStaff
         */
        $returnArr = $server->addOsStaffUseLock($paramIn);

        //[3]数据返回
        return $this->jsonReturn($returnArr);
    }
}