<?php

namespace FlashExpress\bi\App\Modules\Th\Controllers;

use FlashExpress\bi\App\Modules\Th\Server\BackyardServer;
use FlashExpress\bi\App\Server\AssetServer;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\ExtinguisherServer;
use FlashExpress\bi\App\Server\ResignServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\TicketServer;
use Exception;

class BackyardController extends BaseController
{

    public $staff_id;
    public $miss_args;
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $userinfo=$this->userinfo;
        $this->staff_id=$userinfo['id'];
        $this->miss_args=$this->getTranslation()->_('miss_args');


        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();

    }

    /**
     * CEO信箱 \审批 未读合并
     * @return false|string|void
     */
    public function un_read_and_auditAction(){
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] =  $this->userinfo;
        $validations         = [
            "staff_id" => "Required|Int"
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $server = new BackyardServer($this->lang, $this->timezone);
        $return = $server->getRedDotsNum($paramIn);
        $this->getDI()->get('logger')->write_log("un_read_and_audit_{$this->userinfo['staff_id']}: ".json_encode($return,JSON_UNESCAPED_UNICODE),'info');
        return $this->jsonReturn($this->checkReturn(array('data' => $return)));
    }
}
