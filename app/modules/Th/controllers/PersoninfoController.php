<?php

namespace FlashExpress\bi\App\Modules\Th\Controllers;

use FlashExpress\bi\App\Modules\Th\Server\PersoninfoServer;
use FlashExpress\bi\App\Controllers\PersoninfoController as BasePersoninfoController;


class PersoninfoController extends BasePersoninfoController
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function ai_bank_cardAction()
    {
        //[1]入参
        $paramIn                      = $this->paramIn;
        $paramIn['staff_id']          = $this->userinfo['staff_id'];
//        $paramIn['bank_card_img_url'] = 'https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/**********-282741386da044beb1b24cbc19b05ad5.jpeg';

        //[2]业务处理
        $returnArr = (new PersoninfoServer($this->lang, $this->timezone,
            $this->userinfo))->ai_bank_card($paramIn['bank_card_img_url'], $paramIn['staff_id'], $paramIn['bank_no'], $paramIn['bank_type'] ?? 0);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

}
