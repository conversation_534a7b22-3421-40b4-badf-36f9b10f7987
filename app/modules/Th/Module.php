<?php

namespace FlashExpress\bi\App\Modules\Th;

use Phalcon\DiInterface;
use Phalcon\Loader;
use Phalcon\Mvc\ModuleDefinitionInterface;
use Phalcon\Config;

class Module implements ModuleDefinitionInterface
{
    public function registerAutoloaders(DiInterface $di = null)
    {
        $loader = new Loader();

        $loader->registerNamespaces([
            'FlashExpress\bi\App\Modules\Th\Controllers' =>  __DIR__ . '/controllers/',
            'FlashExpress\bi\App\Modules\Th\Models'      =>  __DIR__ . '/models/',
            'FlashExpress\bi\App\Modules\Th\Server'      =>  __DIR__ . '/server/',
            'FlashExpress\bi\App\Modules\Th\library'     =>  __DIR__ . '/library/',
            'FlashExpress\bi\App\Modules\Th\Tasks'       =>  __DIR__ . '/tasks/',
        ]);


        $loader->register();
    }

    public function registerServices(DiInterface $di)
    {
        $config = $di['config'];
        if (file_exists(__DIR__ . '/config/config.php')) {

            $config = $di['config'];

            $override = new Config(include __DIR__ . '/config/config.php');

            if ($config instanceof Config) {
                $config->merge($override);
            } else {
                $config = $override;
            }
        }

    }
}