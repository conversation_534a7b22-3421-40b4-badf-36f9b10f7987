<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/6/17
 * Time: 下午6:23
 */

namespace FlashExpress\bi\App\Modules\La\Tasks;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoExtendMode;
use FlashExpress\bi\App\Models\backyard\StaffDaysFreezeModel;
use FlashExpress\bi\App\Models\backyard\StaffLastYearDaysModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveExtendModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Modules\La\Server\LeaveServer;
use FlashExpress\bi\App\Modules\La\Server\Vacation\AnnualServer;
use StaffExtendTask as GlobalTask;


class StaffExtendTask extends GlobalTask
{

    //新版 https://flashexpress.feishu.cn/docx/IMk7drFnEoKAubxplDUc8nzznYg
    public function freeze_firstAction($param)
    {
        ini_set('memory_limit', '-1');
        try {
            $staff_id = '';
            if (!empty($param[0])) {
                $staff_id = $param[0];
            }
            //任务日期
            $today = date('Y-m-d');
            //离职的也跑
            $condition = " formal = 1 and is_sub_staff = 0 and hire_type in ({hireTypes:array}) and hire_date < :today:";
            //只有正式员工
            $bind = [
                'hireTypes' => [HrStaffInfoModel::HIRE_TYPE_1],
                'today'     => $today,
            ];
            //测试用
            if (!empty($staff_id)) {
                $condition        .= " and staff_info_id = :staff_id:";
                $bind['staff_id'] = $staff_id;
            }

            $staff_list = HrStaffInfoModel::find([
                'conditions' => $condition,
                'columns'    => 'staff_info_id,state,hire_date,job_title_level,job_title_grade_v2,wait_leave_state,hire_type',
                'bind'       => $bind,
            ])->toArray();
            if (empty($staff_list)) {
                die('没有员工数据');
            }

            //员工 旧规则 freeze 表保存的 应发放额度
            $freezeData = StaffDaysFreezeModel::find([
                'columns'    => 'staff_info_id,days,used_days',
                'conditions' => 'staff_info_id in ({staff_ids:array})',
                'bind'       => [
                    'staff_ids' => array_column($staff_list, 'staff_info_id'),
                ],
            ])->toArray();
            //23年 固化的 应有额度
            $freezeData = empty($freezeData) ? [] : array_column($freezeData, null, 'staff_info_id');

            //22年 历史数据
            $year22Days = StaffLastYearDaysModel::find([
                'columns'    => 'staff_info_id,act_got_days,left_days',
                'conditions' => 'staff_info_id in ({staff_ids:array}) and year_time = 2022',
                'bind'       => [
                    'staff_ids' => array_column($staff_list, 'staff_info_id'),
                ],
            ])->toArray();
            $year22Days = empty($year22Days) ? [] : array_column($year22Days, null, 'staff_info_id');

            $annualServer = new AnnualServer($this->lang, $this->timezone);
            $remainModel  = new StaffLeaveRemainDaysModel();
            $extModel     = new StaffLeaveExtendModel();
            foreach ($staff_list as $staff_info) {
                $staff_id  = $staff_info['staff_info_id'];
                $cycleInfo = $annualServer->get_cycle($staff_info);

                if(empty($cycleInfo)){
                    echo $staff_id . ' 没有入职日期';
                    continue;
                }

                //freeze days 额度 作为总数
                $should_days = $freezeData[$staff_id]['days'] ?? 0;
                $use_days    = $freezeData[$staff_id]['used_days'] ?? 0;
                $daysFor2022 = half_num($should_days);

                $this->getDI()->get('db')->begin();

                //初始化  remain
                $row['staff_info_id'] = $staff_id;
                $row['leave_type']    = enums::LEAVE_TYPE_1;
                $row['year']          = 2023;
                $row['task_date']     = $today;
                $row['freeze_days']   = $should_days;
                $row['days']          = $daysFor2022 - $use_days;//剩余额度
                $row['leave_days']    = $use_days;//使用额度

                $cloneOld = clone $remainModel;
                $cloneOld->create($row);

                //初始化 ext
                $ext['staff_info_id']   = $staff_id;
                $ext['leave_type']      = enums::LEAVE_TYPE_1;
                $ext['year_at']         = 2023;
                $ext['left_all_days']   = $daysFor2022 - $use_days;// 剩余额度
                $ext['job_title_level'] = $staff_info['job_title_level'];
                $ext['job_title_grade'] = $staff_info['job_title_grade_v2'];
                //入库 ext表
                $extendOld = clone $extModel;
                $extendOld->create($ext);

                //初始化一条 当前周期的 要等6月1号才开始跑递增 如果 正好是周期最后一天 初始化下个周期
                $cycle = $cycleInfo['cycle'];
                $cycle_last_date = date('Y-m-d', strtotime("{$cycleInfo['count_day']} -1 day"));
                if($today == $cycle_last_date){
                    $cycle = $cycle + 1;
                }
                $remain['staff_info_id'] = $staff_id;
                $remain['leave_type']    = enums::LEAVE_TYPE_1;
                $remain['year']          = $cycle;
                $remain['task_date']     = $today;
                //要把小数点 挪到当前周期
                $remain['freeze_days'] = $remain['days'] = bcsub($should_days, $daysFor2022, enums::ROUND_NUM);
                $remain['leave_days']  = 0;

                //初始化 extend
                $extRow['staff_info_id']   = $staff_id;
                $extRow['leave_type']      = enums::LEAVE_TYPE_1;
                $extRow['year_at']         = $cycle;
                $extRow['left_all_days']   = 0;
                $extRow['job_title_level'] = $staff_info['job_title_level'];
                $extRow['job_title_grade'] = $staff_info['job_title_grade_v2'];

                //入库 remain
                $cloneNew = clone $remainModel;
                $cloneNew->create($remain);
                //入库 ext表
                $extendClone = clone $extModel;
                $extendClone->create($extRow);

                //新增逻辑 历史2022年数据 也保存到 新数据表
                if (!empty($year22Days[$staff_id])) {
                    $row_2022['staff_info_id'] = $staff_id;
                    $row_2022['leave_type']    = enums::LEAVE_TYPE_1;
                    $row_2022['year']          = 2022;
                    $row_2022['task_date']     = $today;
                    $row_2022['freeze_days']   = $year22Days[$staff_id]['act_got_days'];
                    $row_2022['days']          = $year22Days[$staff_id]['left_days'];
                    $row_2022['leave_days']    = half_num($year22Days[$staff_id]['act_got_days']) - $year22Days[$staff_id]['left_days'];

                    //初始化 ext
                    $ext['staff_info_id']   = $staff_id;
                    $ext['leave_type']      = enums::LEAVE_TYPE_1;
                    $ext['year_at']         = 2022;
                    $ext['left_all_days']   = half_num($year22Days[$staff_id]['act_got_days']);// 剩余额度
                    $ext['job_title_level'] = $staff_info['job_title_level'];
                    $ext['job_title_grade'] = $staff_info['job_title_grade_v2'];

                    //入库 remain
                    $clone2022 = clone $remainModel;
                    $clone2022->create($row_2022);
                    //入库 ext表
                    $extendClone2022 = clone $extModel;
                    $extendClone2022->create($ext);
                }
                $this->getDI()->get('db')->commit();
            }
        } catch (\Exception $e) {
            die('初始化额度报错 '.$e->getMessage().'-------'.$e->getTraceAsString());
            $this->getDI()->get('db')->rollback();
        }
    }

    //23年 6月1号开始跑 新版任务
    public function freeze_by_levelAction($param)
    {
        //任务锁
        $key   = 'freeze_by_level_lock';
        $redis = $this->getDI()->get("redisLib");
        $rs    = $redis->set($key, 1, ['nx', 'ex' => 10 * 60]);//锁10分钟
        if (!$rs && RUNTIME == 'pro') {
            echo 'task is running';
            return ;
        }
        $today = date('Y-m-d');

        if (!empty($param[0])) {
            $today = $param[0];
        }

        if (!empty($param[1])) {
            $staffParam['staff_info_id'] = (int)$param[1];
        }

        try {
            //只有正式
            $staffParam['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_1];
            $staffParam['state'] = [HrStaffInfoModel::STATE_1,HrStaffInfoModel::STATE_3];
            $staff_list = $this->annualStaffList($today, $staffParam);

            if (empty($staff_list)) {
                return true;
            }

            $staff_list   = array_column($staff_list, null, 'staff_info_id');
            $annualServer = new AnnualServer($this->lang, $this->timezone);
            $leaveServer = new LeaveServer($this->lang, $this->timezone);
            foreach ($staff_list as $staff_id => $staff_info) {
                if (empty($staff_id)) {
                    continue;
                }
                $flag = $leaveServer->leavePermission($staff_info);
                if(!$flag){
                    echo $staff_info['staff_info_id'].' 非工作所在国家 不发年假';
                    continue;
                }

                //获取当前周期的额度信息
                $cycle_info = $annualServer->get_cycle($staff_info);
                if (empty($cycle_info)) {
                    echo $staff_info['staff_info_id'].'没有入职日期';
                    continue;
                }


                //新增 计算年假日期逻辑 https://flashexpress.feishu.cn/docx/USK1dOhAiod8STxnC0ccJqacn4d
                if(!empty($staff_info['annual_date'])){
                    //清空日期 不累计年假
                    if($staff_info['annual_date'] == HrStaffInfoExtendMode::ANNUAL_STOP_DATE){
                        echo 'base 清空日期 不计算年假 ' . "{$staff_info['staff_info_id']} ". $staff_info['annual_date'] ;
                        continue;
                    }
                    //日期在当天之后 不累计
                    if($staff_info['annual_date'] > $today){
                        echo 'base 没到计算日期 不计算年假 ' . "{$staff_info['staff_info_id']} ". $staff_info['annual_date'];
                        continue;
                    }
                }

                $this->getDI()->get('db')->begin();
                //下个周期
                $nextCycle = $cycle_info['cycle'] + 1;
                $current_remain = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year = :cycle: and leave_type = :leave_type:',
                    'bind'       => [
                        'staff_id'   => $staff_id,
                        'cycle'      => $cycle_info['cycle'],
                        'leave_type' => enums::LEAVE_TYPE_1,
                    ],
                ]);
                //应有额度
                $should_day = $annualServer->getGradeDaysNormal($staff_info);

                //没有自然年概念 都是 365
                $add_day = round($should_day / 365, enums::ROUND_NUM);
                //有可能是新入职员工 或者迁移的 直接初始化一条数据
                if (empty($current_remain) || $current_remain->freeze_days == 0) {
                    $ext['freeze_days'] = $ext['days'] = $add_day;
                    //如果是 迁移员工 只加1天额度
                    if(!empty($staff_info['annual_date'])){
                        $ext['freeze_days'] = $ext['days'] = 0;
                    }
                    $ext['task_date']   = $today;
                    $current_remain     = $annualServer->initAnnual($staff_info, $cycle_info['cycle'], $ext);
                } elseif ($current_remain->task_date >= $today) {
                    //任务判定是否跑过 每天只能跑一次
                    $this->getDI()->get('db')->commit();
                    continue;
                }

                $current_remain->freeze_days += $add_day;
                $current_remain->days        += $add_day;//加一天额度
                $current_remain->task_date   = $today;
                $current_remain->update();
                //当天是 周期最后一天 初始化下周 额度0 记录
                $cycle_last_date = date('Y-m-d', strtotime("{$cycle_info['count_day']} -1 day"));
                if ($today == $cycle_last_date) {//当天是 当前周期 最后一天 加一天额度 然后初始化 下一年记录 并且当年额度 +1天
                    $remains = $this->getNewCycleRemains($staff_id);
                    if ($remains->count() == 1){
                        //非完整周期的不满0.5的结余要加到下个周期里；
                        $freezeDays = round($current_remain->freeze_days - half_num($current_remain->freeze_days),enums::ROUND_NUM);
                    }else{
                        $freezeDays = 0;
                    }
                    $annualServer->initAnnual($staff_info, $nextCycle, ['freeze_days'=>$freezeDays,'days'=>$freezeDays,'task_date' => $today]);
                }
                $this->getDI()->get('db')->commit();
                $this->logger->write_log("freeze_by_level_{$staff_info['staff_info_id']}_{$cycle_info['cycle']} {$today} 增加额度 {$add_day} ",
                    'info');
            }

            $redis->delete($key);
            $this->logger->write_log("freeze_by_level {$today} 年假固化任务 跑完了 ", 'notice');
        } catch (\Exception $e) {
            $this->logger->write_log("freeze_by_level {$today} 任务数据失败 ".$e->getTraceAsString());
            $this->getDI()->get('db')->rollback();
            die('freeze_by_level 任务异常 '.$e->getTraceAsString());
        }
    }
}