<?php

namespace FlashExpress\bi\App\Modules\La\Library\Enums;

use app\enums\LangEnums;
use FlashExpress\bi\App\Enums\BaseEnums;
use FlashExpress\bi\App\library\enums;

class InteriorGoodsEnums extends BaseEnums
{
    const CURRENCY_SYMBOL = '฿';

    const UNDEFINED_GOODS_CODE         = '0';  //未定义
    const POLO_GOODS_CODE              = '4';  //polo衫
    const T_SHIRT_GOODS_CODE           = '1';  //黑色T恤
    const COAT_GOODS_CODE              = '2';  //外套
    const TROUSERS_GOODS_CODE          = '3';  //长裤
    const FH_T_SHIRT_BLACK_GOODS_CODE  = '5';  //flash homeT恤（黑）
    const FH_T_SHIRT_YELLOW_GOODS_CODE = '6';  // flash homeT恤（黄）
    const FH_POLO_GOODS_CODE           = '7';  //flash home polo衫
    const FH_COAT_GOODS_CODE           = '8';  //flash home外套
    const HELMET_GOODS_CODE            = '9';  //头盔
    const MOTORCYCLE_BOX_GOODS_CODE    = '10'; //摩托车尾包
    const POLO_BLACK_GOODS_CODE        = '11'; //Polo衫黑色


    public static function getCodeTxtMap($lang = '')
    {
        $data = [
            self::UNDEFINED_GOODS_CODE         => '未定义',
            self::POLO_GOODS_CODE              => 'polo衫',
            self::T_SHIRT_GOODS_CODE           => '黑色T恤',
            self::COAT_GOODS_CODE              => '外套',
            self::TROUSERS_GOODS_CODE          => '长裤',
            self::FH_T_SHIRT_BLACK_GOODS_CODE  => 'flash homeT恤（黑）',
            self::FH_T_SHIRT_YELLOW_GOODS_CODE => 'flash homeT恤（黄）',
            self::FH_POLO_GOODS_CODE           => 'flash home polo衫',
            self::FH_COAT_GOODS_CODE           => 'flash home外套',
            self::HELMET_GOODS_CODE            => '安全帽/头盔',
            self::MOTORCYCLE_BOX_GOODS_CODE    => '摩托车尾包',

        ];
        return $data;
    }

    public static function fmtAmount($amount, $lang = '')
    {
        $countryCode=ucfirst(strtolower(env('country_code','Th')));
        return enums::$country_conf[$countryCode]['currency_symbol'] . $amount;
    }

    public static function getStaffStoreCateEnableGoodsCodeMap()
    {
        return [
            SysStoreCateEnums::HO_CODE   => [
                self::POLO_GOODS_CODE,
                self::T_SHIRT_GOODS_CODE,
                self::COAT_GOODS_CODE,
                self::TROUSERS_GOODS_CODE,
                self::HELMET_GOODS_CODE,
                self::MOTORCYCLE_BOX_GOODS_CODE,
            ],
            SysStoreCateEnums::DC_CODE   => [
                self::POLO_GOODS_CODE,
                self::COAT_GOODS_CODE,
                self::TROUSERS_GOODS_CODE,
                self::HELMET_GOODS_CODE,
                self::MOTORCYCLE_BOX_GOODS_CODE,
            ],
            SysStoreCateEnums::SP_CODE   => [
                self::POLO_GOODS_CODE,
                self::COAT_GOODS_CODE,
                self::TROUSERS_GOODS_CODE,
                self::HELMET_GOODS_CODE,
                self::MOTORCYCLE_BOX_GOODS_CODE,
            ],
            SysStoreCateEnums::SHOP_CODE => [
                self::POLO_GOODS_CODE,
                self::COAT_GOODS_CODE,
                self::TROUSERS_GOODS_CODE,
                self::HELMET_GOODS_CODE,
                self::MOTORCYCLE_BOX_GOODS_CODE,
            ],
            SysStoreCateEnums::BDC_CODE  => [
                self::POLO_GOODS_CODE,
                self::COAT_GOODS_CODE,
                self::TROUSERS_GOODS_CODE,
                self::HELMET_GOODS_CODE,
                self::MOTORCYCLE_BOX_GOODS_CODE,
            ],
            SysStoreCateEnums::HUB_CODE  => [
                self::T_SHIRT_GOODS_CODE,
                self::COAT_GOODS_CODE,
                self::TROUSERS_GOODS_CODE,
                self::HELMET_GOODS_CODE,
                self::MOTORCYCLE_BOX_GOODS_CODE,
            ],
            SysStoreCateEnums::B_HUB_CODE  => [
                self::T_SHIRT_GOODS_CODE,
                self::COAT_GOODS_CODE,
                self::TROUSERS_GOODS_CODE,
                self::HELMET_GOODS_CODE,
                self::MOTORCYCLE_BOX_GOODS_CODE,
            ],
            SysStoreCateEnums::OS_CODE   => [
                self::T_SHIRT_GOODS_CODE,
                self::COAT_GOODS_CODE,
                self::TROUSERS_GOODS_CODE,
                self::HELMET_GOODS_CODE,
                self::MOTORCYCLE_BOX_GOODS_CODE,
            ],
            SysStoreCateEnums::FH_CODE   => [
                self:: FH_T_SHIRT_BLACK_GOODS_CODE,
                self:: FH_T_SHIRT_YELLOW_GOODS_CODE,
                self:: FH_COAT_GOODS_CODE,
                self:: FH_POLO_GOODS_CODE,
                self::HELMET_GOODS_CODE,
                self::MOTORCYCLE_BOX_GOODS_CODE,
            ],
        ];

    }

    // 商品尺码
    public static function getGoodsSize($goodsCode, $lang)
    {
//        $lang            = strtolower($lang);
        $yf_sizeNameRow1 = [
            LangEnums::LANG_CODE_ZH_CN => '尺码',
            LangEnums::LANG_CODE_EN    => 'Size',
            LangEnums::LANG_CODE_TH    => 'ขนาด'
        ];
        $yf_sizeNameRow2 = [
            LangEnums::LANG_CODE_ZH_CN => '胸围 (英寸)',
            LangEnums::LANG_CODE_EN    => 'Bust (inch)',
            LangEnums::LANG_CODE_TH    => 'รอบอก (นิ้ว)'
        ];
        $yf_sizeNameRow3 = [
            LangEnums::LANG_CODE_ZH_CN => '长度 (英寸) ',
            LangEnums::LANG_CODE_EN    => 'Length (inch)',
            LangEnums::LANG_CODE_TH    => 'ความยาว (นิ้ว)'
        ];
        $kz_sizeNameRow2 = [
            LangEnums::LANG_CODE_ZH_CN => '臀围 (英寸) ',
            LangEnums::LANG_CODE_EN    => 'Hip (inch)',
            LangEnums::LANG_CODE_TH    => 'สะโพก (นิ้ว)'
        ];
        $title           = [
            LangEnums::LANG_CODE_ZH_CN => [
                self::UNDEFINED_GOODS_CODE         => '未定义',
                self::POLO_GOODS_CODE              => 'polo衫 每种 165 泰铢',
                self::T_SHIRT_GOODS_CODE           => 'T恤 每种 120 泰铢',
                self::COAT_GOODS_CODE              => '外套 每种 350 泰铢',
                self::TROUSERS_GOODS_CODE          => '长裤 每种 280 泰铢',
                self::FH_T_SHIRT_BLACK_GOODS_CODE  => 'T恤 每种 120 泰铢',
                self::FH_T_SHIRT_YELLOW_GOODS_CODE => 'T恤 每种 120 泰铢',
                self::FH_POLO_GOODS_CODE           => 'polo衫 每种 165 泰铢',
                self::FH_COAT_GOODS_CODE           => '外套 每种 350 泰铢',
                self::POLO_BLACK_GOODS_CODE        => 'polo衫 每种 145 泰铢',


            ],
            LangEnums::LANG_CODE_EN    => [
                self::UNDEFINED_GOODS_CODE         => '未定义',
                self::POLO_GOODS_CODE              => 'polo衫 每种 165 泰铢',
                self::T_SHIRT_GOODS_CODE           => 'T恤 每种 120 泰铢',
                self::COAT_GOODS_CODE              => '外套 每种 350 泰铢',
                self::TROUSERS_GOODS_CODE          => '长裤 每种 280 泰铢',
                self::FH_T_SHIRT_BLACK_GOODS_CODE  => 'T恤 每种 120 泰铢',
                self::FH_T_SHIRT_YELLOW_GOODS_CODE => 'T恤 每种 120 泰铢',
                self::FH_POLO_GOODS_CODE           => 'polo衫 每种 165 泰铢',
                self::FH_COAT_GOODS_CODE           => '外套 每种 350 泰铢',
                self::POLO_BLACK_GOODS_CODE        => 'polo衫 每种 145 泰铢',

            ],
            LangEnums::LANG_CODE_TH    => [
                self::UNDEFINED_GOODS_CODE         => '未定义',
                self::POLO_GOODS_CODE              => 'เสื้อโปโล ทุกขนาด 165 บาท',
                self::T_SHIRT_GOODS_CODE           => 'เสื้อคอกลม ทุกขนาด 120 บาท',
                self::COAT_GOODS_CODE              => 'เสื้อแจ็คเก็ต ทุกขนาด 350 บาท',
                self::TROUSERS_GOODS_CODE          => 'กางเกงขายาว ทุกขนาด 280 บาท',
                self::FH_T_SHIRT_BLACK_GOODS_CODE  => 'เสื้อคอกลม ทุกขนาด 120 บาท',
                self::FH_T_SHIRT_YELLOW_GOODS_CODE => 'เสื้อคอกลม ทุกขนาด 120 บาท',
                self::FH_POLO_GOODS_CODE           => 'เสื้อโปโล ทุกขนาด 165 บาท',
                self::FH_COAT_GOODS_CODE           => 'เสื้อแจ็คเก็ต ทุกขนาด 350 บาท',
                self::POLO_BLACK_GOODS_CODE        => 'เสื้อโปโล ทุกขนาด 145 บาท',


            ],
        ];
        $sizes           = [
            self::POLO_GOODS_CODE              => [
                'title' => $title[$lang][self::POLO_GOODS_CODE],
                'size'  => [
                    [
                        'name' => $yf_sizeNameRow1[$lang],
                        'size' => [
                            'col1' => 'S',
                            'col2' => 'M',
                            'col3' => 'L',
                            'col4' => 'XL',
                            'col5' => '2XL',

                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow2[$lang],
                        'size' => [
                            'col1' => '34',
                            'col2' => '37',
                            'col3' => '40',
                            'col4' => '42',
                            'col5' => '44',

                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow3[$lang],
                        'size' => [
                            'col1' => '24',
                            'col2' => '26',
                            'col3' => '28',
                            'col4' => '30',
                            'col5' => '32',

                        ]
                    ],
                ]
            ],
            self::T_SHIRT_GOODS_CODE           => [
                'title' => $title[$lang][self::T_SHIRT_GOODS_CODE],
                'size'  => [
                    [
                        'name' => $yf_sizeNameRow1[$lang],
                        'size' => [
                            'col1' => 'S',
                            'col2' => 'M',
                            'col3' => 'L',
                            'col4' => 'XL',
                            'col5' => '2XL',
                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow2[$lang],
                        'size' => [
                            'col1' => '34',
                            'col2' => '37',
                            'col3' => '40',
                            'col4' => '42',
                            'col5' => '44',

                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow3[$lang],
                        'size' => [
                            'col1' => '24',
                            'col2' => '26',
                            'col3' => '28',
                            'col4' => '30',
                            'col5' => '32',
                        ]
                    ],
                ]
            ],
            self::COAT_GOODS_CODE              => [
                'title' => $title[$lang][self::FH_COAT_GOODS_CODE],
                'size'  => [
                    [
                        'name' => $yf_sizeNameRow1[$lang],
                        'size' => [
                            'col1' => 'S',
                            'col2' => 'M',
                            'col3' => 'L',
                            'col4' => 'XL',
                            'col5' => '2XL',

                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow2[$lang],
                        'size' => [
                            'col1' => '42',
                            'col2' => '44',
                            'col3' => '46',
                            'col4' => '48',
                            'col5' => '50',

                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow3[$lang],
                        'size' => [
                            'col1' => '25.5',
                            'col2' => '26',
                            'col3' => '27',
                            'col4' => '27.5',
                            'col5' => '28',
                        ]
                    ],
                ]
            ],
            self::TROUSERS_GOODS_CODE          => [
                'title' => $title[$lang][self::TROUSERS_GOODS_CODE],
                'size'  => [
                    [
                        'name' => $yf_sizeNameRow1[$lang],
                        'size' => [
                            'col1' => '28',
                            'col2' => '30',
                            'col3' => '32',
                            'col4' => '34',
                            'col5' => '36',
                            'col6' => '38',
                            'col7' => '40',
                            'col8' => '42',
                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow3[$lang],
                        'size' => [
                            'col1' => '36',
                            'col2' => '37',
                            'col3' => '38',
                            'col4' => '39',
                            'col5' => '40',
                            'col6' => '41',
                            'col7' => '42',
                            'col8' => '43',
                        ]
                    ],
                ]
            ],
            self::FH_T_SHIRT_BLACK_GOODS_CODE  => [
                'title' => $title[$lang][self::T_SHIRT_GOODS_CODE],
                'size'  => [
                    [
                        'name' => $yf_sizeNameRow1[$lang],
                        'size' => [
                            'col1' => 'S',
                            'col2' => 'M',
                            'col3' => 'L',
                            'col4' => 'XL',
                            'col5' => '2XL',
                            'col6' => '4XL',
                            'col7' => '6XL',
                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow2[$lang],
                        'size' => [
                            'col1' => '36',
                            'col2' => '38',
                            'col3' => '40',
                            'col4' => '42',
                            'col5' => '44',
                            'col6' => '48',
                            'col7' => '52',
                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow3[$lang],
                        'size' => [
                            'col1' => '25',
                            'col2' => '26',
                            'col3' => '27',
                            'col4' => '28',
                            'col5' => '29',
                            'col6' => '31',
                            'col7' => '33',
                        ]
                    ],
                ]
            ],
            self::FH_T_SHIRT_YELLOW_GOODS_CODE => [
                'title' => $title[$lang][self::T_SHIRT_GOODS_CODE],
                'size'  => [
                    [
                        'name' => $yf_sizeNameRow1[$lang],
                        'size' => [
                            'col1' => 'S',
                            'col2' => 'M',
                            'col3' => 'L',
                            'col4' => 'XL',
                            'col5' => '2XL',
                            'col6' => '4XL',
                            'col7' => '6XL',
                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow2[$lang],
                        'size' => [
                            'col1' => '36',
                            'col2' => '38',
                            'col3' => '40',
                            'col4' => '42',
                            'col5' => '44',
                            'col6' => '48',
                            'col7' => '52',
                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow3[$lang],
                        'size' => [
                            'col1' => '25',
                            'col2' => '26',
                            'col3' => '27',
                            'col4' => '28',
                            'col5' => '29',
                            'col6' => '31',
                            'col7' => '33',
                        ]
                    ],
                ]
            ],
            self::FH_POLO_GOODS_CODE           => [
                'title' => $title[$lang][self::FH_POLO_GOODS_CODE],
                'size'  => [
                    [
                        'name' => $yf_sizeNameRow1[$lang],
                        'size' => [
                            'col1' => 'S',
                            'col2' => 'M',
                            'col3' => 'L',
                            'col4' => 'XL',
                            'col5' => '2XL',
                            'col6' => '3XL',
                            'col7' => '4XL',
                            'col8' => '6XL',
                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow2[$lang],
                        'size' => [
                            'col1' => '38',
                            'col2' => '40',
                            'col3' => '42',
                            'col4' => '44',
                            'col5' => '46',
                            'col6' => '48',
                            'col7' => '50',
                            'col8' => '54',
                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow3[$lang],
                        'size' => [
                            'col1' => '26',
                            'col2' => '27',
                            'col3' => '28',
                            'col4' => '29',
                            'col5' => '30',
                            'col6' => '31',
                            'col7' => '32',
                            'col8' => '34',
                        ]
                    ],
                ]
            ],
            self::FH_COAT_GOODS_CODE           => [
                'title' => $title[$lang][self::FH_COAT_GOODS_CODE],
                'size'  => [
                    [
                        'name' => $yf_sizeNameRow1[$lang],
                        'size' => [
                            'col1' => 'S',
                            'col2' => 'M',
                            'col3' => 'L',
                            'col4' => 'XL',
                            'col5' => '2XL',
                            'col6' => '4XL',
                            'col7' => '6XL',
                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow2[$lang],
                        'size' => [
                            'col1' => '42',
                            'col2' => '44',
                            'col3' => '46',
                            'col4' => '48',
                            'col5' => '50',
                            'col6' => '54',
                            'col7' => '58',
                        ]
                    ],
                    [
                        'name' => $yf_sizeNameRow3[$lang],
                        'size' => [
                            'col1' => '25.5',
                            'col2' => '26',
                            'col3' => '27',
                            'col4' => '27.5',
                            'col5' => '28',
                            'col6' => '30',
                            'col7' => '32',
                        ]
                    ],
                ]
            ],
            self::HELMET_GOODS_CODE            => [
                'title' => $title[$lang][self::UNDEFINED_GOODS_CODE],
                'size'  => [
                    [
                        'name' => $yf_sizeNameRow1[$lang],
                        'size' => [
                            'col1' => 'S',
                        ]
                    ],
                ]
            ],
            self::MOTORCYCLE_BOX_GOODS_CODE    => [
                'title' => $title[$lang][self::UNDEFINED_GOODS_CODE],
                'size'  => [
                    [
                        'name' => $yf_sizeNameRow1[$lang],
                        'size' => [
                            'col1' => 'S',
                        ]
                    ],
                ]
            ],
            self::POLO_BLACK_GOODS_CODE    => [
                'title' => $title[$lang][self::POLO_BLACK_GOODS_CODE],
                'size'  => [
                    [
                        'name' => $yf_sizeNameRow1[$lang],
                        'size' => [
                            'col1' => 'S',
                            'col2' => 'M',
                            'col3' => 'L',
                            'col4' => 'XL',
                            'col5' => '2XL',
                            'col6' => '3XL',
                            'col7' => '4XL',
                            'col8' => '5XL',
                            'col9' => '6XL',
                        ]
                    ],

                ]
            ],
        ];
        return $sizes[$goodsCode] ?? [];

    }




}