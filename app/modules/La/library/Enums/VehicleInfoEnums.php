<?php

namespace  FlashExpress\bi\App\Modules\La\library\Enums;

class VehicleInfoEnums
{
    // 翻译系统前缀
    const TRANSLATION_PREFIX = 'vehicle_info_';

    // 车辆来源
    const TRANSLATION_PREFIX_VEHICLE_SOURCE = self::TRANSLATION_PREFIX.'vehicle_source_';
    const VEHICLE_SOURCE_PERSONAL_CODE = 1;// 使用个人车辆
    const VEHICLE_SOURCE_RENTAL_CODE = 2;// 租用公司车辆
    const VEHICLE_SOURCE_BORROW_CODE = 3;// 借用车辆
    const VEHICLE_SOURCE_ITEM = [
        self::VEHICLE_SOURCE_PERSONAL_CODE => self::TRANSLATION_PREFIX_VEHICLE_SOURCE.self::VEHICLE_SOURCE_PERSONAL_CODE,
        self::VEHICLE_SOURCE_RENTAL_CODE => self::TRANSLATION_PREFIX_VEHICLE_SOURCE.self::VEHICLE_SOURCE_RENTAL_CODE,
        self::VEHICLE_SOURCE_BORROW_CODE => self::TRANSLATION_PREFIX_VEHICLE_SOURCE.self::VEHICLE_SOURCE_BORROW_CODE,
    ];

    // 驾照类型
    const DRIVER_LICENSE_TYPE_001 = 1;
    const DRIVER_LICENSE_TYPE_002 = 2;
    const DRIVER_LICENSE_TYPE_003 = 3;
    const DRIVER_LICENSE_TYPE_004 = 4;
    const DRIVER_LICENSE_TYPE_005 = 5;
    const DRIVER_LICENSE_TYPE_006 = 6;
    const DRIVER_LICENSE_TYPE_007 = 7;
    const DRIVER_LICENSE_TYPE_008 = 8;

    const DRIVER_LICENSE_TYPE_ITEM = [
        self::DRIVER_LICENSE_TYPE_001 => 'A',
        self::DRIVER_LICENSE_TYPE_002 => 'A1',
        self::DRIVER_LICENSE_TYPE_003 => 'A2',
        self::DRIVER_LICENSE_TYPE_004 => 'B',
        self::DRIVER_LICENSE_TYPE_005 => 'C',
        self::DRIVER_LICENSE_TYPE_006 => 'D',
        self::DRIVER_LICENSE_TYPE_007 => 'D1',
        self::DRIVER_LICENSE_TYPE_008 => 'E',
    ];


    // 审核状态
    const TRANSLATION_PREFIX_APPROVAL_STATUS = self::TRANSLATION_PREFIX.'approval_status_';
    const APPROVAL_UN_SUBMITTED_CODE = 0;// 未提交
    const APPROVAL_PENDING_CODE = 1;//待审核
    const APPROVAL_PASSED_CODE = 2;//通过
    const APPROVAL_REJECT_CODE = 3;//驳回
    const APPROVAL_STATUS_ITEM = [
        self::APPROVAL_UN_SUBMITTED_CODE => self::TRANSLATION_PREFIX_APPROVAL_STATUS.self::APPROVAL_UN_SUBMITTED_CODE,
        self::APPROVAL_PENDING_CODE => self::TRANSLATION_PREFIX_APPROVAL_STATUS.self::APPROVAL_PENDING_CODE,
        self::APPROVAL_PASSED_CODE => self::TRANSLATION_PREFIX_APPROVAL_STATUS.self::APPROVAL_PASSED_CODE,
        self::APPROVAL_REJECT_CODE => self::TRANSLATION_PREFIX_APPROVAL_STATUS.self::APPROVAL_REJECT_CODE,
    ];

    // 车辆添加渠道
    const VEHICLE_ADD_CHANNEL_KIT = 'kit';
    const VEHICLE_ADD_CHANNEL_FBI = 'fbi';

    // 车辆模块有权限的职位
    const JOB_VAN_TITLE_ID = 110;
    const JOB_BIKE_TITLE_ID = 13;


    const JOB_VAN_TITLE_NAME = 'Van Courier';
    const JOB_BIKE_TITLE_NAME = 'Bike Courier';


    const JOB_TITLE_ITEM = [
        self::JOB_VAN_TITLE_ID => self::JOB_VAN_TITLE_NAME,
        self::JOB_BIKE_TITLE_ID => self::JOB_BIKE_TITLE_NAME,
    ];

    // 车辆类型
    const VEHICLE_TYPE_BIKE_CODE = 0;
    const VEHICLE_TYPE_VAN_CODE = 1;


    const VEHICLE_TYPE_BIKE_TEXT = 'Bike';
    const VEHICLE_TYPE_VAN_TEXT = 'Van';
    const VEHICLE_TYPE_CAR_TEXT = 'Car';
    const VEHICLE_TYPE_TRICYCLE_TEXT = 'Tricycle';

    const VEHICLE_TYPE_ITEM = [
        self::VEHICLE_TYPE_BIKE_CODE => self::VEHICLE_TYPE_BIKE_TEXT,
        self::VEHICLE_TYPE_VAN_CODE => self::VEHICLE_TYPE_VAN_TEXT,

    ];

    // 职位与车辆类型
    const JOB_VEHICLE_TYPE_REL_CODE = [
        self::JOB_VAN_TITLE_ID => self::VEHICLE_TYPE_VAN_CODE,
        self::JOB_BIKE_TITLE_ID => self::VEHICLE_TYPE_BIKE_CODE,

    ];

    // 职位与车辆类型
    const JOB_VEHICLE_TYPE_REL_TEXT = [
        self::JOB_VAN_TITLE_ID => self::VEHICLE_TYPE_VAN_TEXT,
        self::JOB_BIKE_TITLE_ID => self::VEHICLE_TYPE_BIKE_TEXT,
    ];

    // van car 相关职位分组
    const VAN_JOB_GROUP_ITEM = [
        self::JOB_VAN_TITLE_ID,
    ];


    // 可从 HR-IS 系统 同步过来的枚举字段列表
    const HR_IS_STAFF_CAR_NO_KEY = 'CAR_NO';// 车牌号
    const HR_IS_STAFF_CAR_TYPE_KEY = 'CAR_TYPE';// 车牌类型
    const HR_IS_STAFF_MANGER_KEY = 'MANGER';// 直线主管
    const HR_IS_STAFF_DRIVER_LICENSE_KEY = 'DRIVER_LICENSE';//驾驶证号
    const HR_IS_STAFF_ITEMS = [
        self::HR_IS_STAFF_CAR_NO_KEY,
        self::HR_IS_STAFF_CAR_TYPE_KEY,
        self::HR_IS_STAFF_MANGER_KEY,
        self::HR_IS_STAFF_DRIVER_LICENSE_KEY,
    ];

    // 油类型
    const OIL_TYPE_001 = 1;//DIESEL
    const OIL_TYPE_002 = 2;//GASOLINE#91
    const OIL_TYPE_003 = 3;//GASOLINE#91
    const OIL_TYPE_ITEM = [
        self::OIL_TYPE_001 => 'DIESEL',
        self::OIL_TYPE_002 => 'GASOLINE#91',
        self::OIL_TYPE_003 => 'GASOLINE#95',
    ];

    //油卡补贴方式1:工资卡2:油卡
    const OIL_SUBSIDY_TYPE_1 = 1;
    const OIL_SUBSIDY_TYPE_2 = 2;
    const OIL_SUBSIDY_TYPE_ITEMS = [
        self::OIL_SUBSIDY_TYPE_1=>'oil_subsidy_type_1',
        self::OIL_SUBSIDY_TYPE_2=>'oil_subsidy_type_2',
    ];

    // 油卡开通状态
    const OIL_CARD_STATUS_NOT_OPENED = 0;
    const OIL_CARD_STATUS_OPENED = 1;
    const OIL_CARD_STATUS_ITEM = [
        self::OIL_CARD_STATUS_NOT_OPENED => 'not_open',
        self::OIL_CARD_STATUS_OPENED => 'already_opened'
    ];

    // 油卡公司
    const OIL_COMPANY_SHELL_CODE = 1;
    const OIL_COMPANY_PTT_CODE = 2;
    const OIL_COMPANY_PT_CODE = 3;
    const OIL_COMPANY_ITEM = [
        self::OIL_COMPANY_SHELL_CODE => 'Shell',
        self::OIL_COMPANY_PTT_CODE => 'PTT',
        self::OIL_COMPANY_PT_CODE => 'PT',
    ];

    // 车型
    const VEHICLE_SIZE_ITEM = [
        [ 'value' => '1', 'label'=> '4W1T-10ft'],
        [ 'value' => '2', 'label'=> '6W5T-14ft'],
        [ 'value' => '3', 'label'=> '6W5T-17ft'],
        [ 'value' => '4', 'label'=> '6W7.5T-17ft'],
        [ 'value' => '5', 'label'=> '6W7.5T-20ft'],
        [ 'value' => '6', 'label'=> '6W10T-24ft'],
        [ 'value' => '7', 'label'=> '10W'],
        [ 'value' => '8', 'label'=> 'Other']
    ];



    //从uconfig迁移过来的
    const CONFIG_VEHICLE_INFO = [
        'vehicle_brand' => [
            [ 'value' => '1',
                'label'=> 'Toyota',
                'data' => [
                    ['value' => '1', 'label'=> 'HILUX - Tiger'],
                    ['value' => '2', 'label'=> 'HILUX - VIGO'],
                    ['value' => '3', 'label'=> 'Revo Standard Cab'],
                    ['value' => '100', 'label'=> 'Other'],
                ]
            ],
            [ 'value' => '2',
                'label'=> 'Isuzu',
                'data' => [
                    ['value' => '1', 'label'=> 'D-MAX -SPARK'],
                    ['value' => '2', 'label'=> 'D-MAX -STEALTH'],
                    ['value' => '3', 'label'=> 'D-MAX -SPACE CAB'],
                    ['value' => '4', 'label'=> 'NLR 130'],
                    ['value' => '5', 'label'=> 'ELF euro2 130'],
                    ['value' => '6', 'label'=> 'FRR 210/190'],
                    ['value' => '7', 'label'=> 'NQR 175'],
                    ['value' => '8', 'label'=> 'NPR 150'],
                    ['value' => '9', 'label'=> 'NMR 130'],
                    ['value' => '10', 'label'=> 'TFR/240'],
                    ['value' => '11', 'label'=> 'DECA'],
                    ['value' => '100', 'label'=> 'Other'],
                ]
            ],
            [ 'value' => '3',
                'label'=> 'Nissan',
                'data' => [
                    ['value' => '1', 'label'=> 'NAVARA - King CAB'],
                    ['value' => '2', 'label'=> 'NAVARA - Single CAB'],
                    ['value' => '3', 'label'=> 'FRONTIER'],
                    ['value' => '100', 'label'=> 'Other'],
                ]
            ],
            [ 'value' => '4',
                'label'=> 'Misubishi',
                'data' => [
                    ['value' => '1', 'label'=> 'TITAN - Single CAB'],
                    ['value' => '2', 'label'=> 'TITAN - MEGA CAB'],
                    ['value' => '100', 'label'=> 'Other'],
                ]
            ],
            [ 'value' => '5',
                'label'=> 'Suzuki',
                'data' => [
                    ['value' => '1', 'label'=> 'CARRY'],
                    ['value' => '100', 'label'=> 'Other'],
                ]
            ],
            [ 'value' => '6',
                'label'=> 'Hino',
                'data' => [
                    ['value' => '1', 'label'=> 'SIRI 500FC9J'],
                    ['value' => '100', 'label'=> 'Other'],
                ]
            ],
            [ 'value' => '7',
                'label'=> 'Tata',
                'data' => [
                    ['value' => '1', 'label'=> 'TATA ULTRA 10140'],
                    ['value' => '100', 'label'=> 'Other'],
                ]
            ],
            [
                'value' => '8',
                'label'=> 'FORD',
                'data' => [
                    ['value' => '1', 'label'=> 'Standard Cab XL 4x2 LR 6MT'],
                ]
            ],
            [ 'value' => '99',
                'label'=> 'Other',
                'data' => [
                    ['value' => '100', 'label'=> 'Other'],
                ]
            ],
        ],
        'vehicle_size' => self::VEHICLE_SIZE_ITEM,
        'oil_type' => self::OIL_TYPE_ITEM,
        'oil_company' => [
            [ 'value' => self::OIL_COMPANY_SHELL_CODE, 'label'=> self::OIL_COMPANY_ITEM[self::OIL_COMPANY_SHELL_CODE]],
            [ 'value' => self::OIL_COMPANY_PTT_CODE, 'label'=> self::OIL_COMPANY_ITEM[self::OIL_COMPANY_PTT_CODE]],
            [ 'value' => self::OIL_COMPANY_PT_CODE, 'label'=> self::OIL_COMPANY_ITEM[self::OIL_COMPANY_PT_CODE]],
        ],
        'branch_mileage' => [
        ],
    ];


    public static function getCodeTxtMap($lang = '', $code = '')
    {
    }

}
