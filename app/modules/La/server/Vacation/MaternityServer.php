<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 5/5/23
 * Time: 2:16 PM
 */

namespace FlashExpress\bi\App\Modules\La\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\Vacation\MaternityServer as GlobalServer;


class MaternityServer extends GlobalServer implements LeaveInterface
{

    private static $instance = null;

    public $today;

    public $thisYear;

    const SUB_TYPE_NULL = 4;
    const SUB_TYPE_MORE = 5;
    public $addArr = [
        self::SUB_TYPE_NULL => 0,//无 基础105
        self::SUB_TYPE_MORE => 15,//多胞胎 120天
    ];

    public $subTextArr = [
        self::SUB_TYPE_NULL => 'leave_4_4',//无 基础105
        self::SUB_TYPE_MORE => 'leave_4_5',//多胞胎 120天
    ];


    //任务 每年初始化调用
    public static function getInstance($lang, $timezone)
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self($lang, $timezone);
        }
        return self::$instance;
    }


    //验证申请日期 各国不一样 印尼 泰国 菲律宾 限制开始时间当天之后 越南 -3  老挝 -2 马来 没限制
    public function timeCheck()
    {
        //请假日期必须大于等于当前日期
        if (empty($this->paramModel['is_bi'])) {
            $this->timeValidate(-2, 100, 'leave_3_notice');
        }
    }

    //验证自类型参数
    public function subTypeCheck(){
        $tmpArr = $this->addArr;
        if(empty($this->paramModel['sub_type']) || !in_array($this->paramModel['sub_type'],array_keys($tmpArr))){
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
    }


    //获取额外天数
    public function getAddDays()
    {
        return $this->addArr[$this->paramModel['sub_type']] ?? 0;
    }

    //菲律宾 老挝 越南 产假 有特殊详情页展示字段
    public function formatDetailInfo($subType){
        $key = $this->subTextArr[$subType];
        if(empty($key)){
            return '';
        }
        return $this->getTranslation()->_($key);
    }


}