<?php

namespace FlashExpress\bi\App\Modules\La\Server;

use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Repository\StaffPublicHolidayRepository;
use FlashExpress\bi\App\Server\BaseServer;

class HolidayServer extends BaseServer
{
    /**
     * 假期分组
     * @param $params
     * @return array
     */
    private function delHolidayGroup($params): array
    {
        $condition = '';
        $bind      = [];
        if (isset($params['date'])) {
            $condition    = 'day >=  :date:';
            $bind['date'] = date('Y-m-d', strtotime($params['date']));
        }
        $holiday = ThailandHolidayModel::find([
            'conditions' => $condition,
            'columns'    => 'day,type',
            'bind'       => $bind,
        ])->toArray();
        //假期分组
        $common_days = $week_working_day_6 = $week_working_day_5 = $female_days = $china_holidays = [];
        foreach ($holiday as $item) {
            switch ($item['type']) {
                case ThailandHolidayModel::TYPE_DEFAULT:
                    $common_days[] = $item['day'];
                    break;
                case ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_6:
                    $week_working_day_6[] = $item['day'];
                    break;
                case ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_5:
                    $week_working_day_5[] = $item['day'];
                    break;
                case ThailandHolidayModel::TYPE_FEM:
                    $female_days[] = $item['day'];
                    break;
                case ThailandHolidayModel::TYPE_CH:
                    $china_holidays[] = $item['day'];
                    break;
            }
        }
        return [
            'common_days'        => $common_days,
            'week_working_day_5' => $week_working_day_5,
            'week_working_day_6' => $week_working_day_6,
            'female_days'        => $female_days,
            'china_holidays'     => $china_holidays,
        ];
    }

    /**
     * 处理员工的假期
     * @param $staffInfo
     * @param $holidayGroup
     * @return mixed
     */
    private function delStaffHoliday($staffInfo, $holidayGroup)
    {
        //公共的
        $returnDays = $holidayGroup['common_days'];
        //5天班
        if ($staffInfo['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_5) {
            $returnDays = array_merge($returnDays, $holidayGroup['week_working_day_5']);
        }
        //6天班
        if ($staffInfo['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_6) {
            $returnDays = array_merge($returnDays, $holidayGroup['week_working_day_6']);
        }
        //女士
        if ($staffInfo['sex'] == HrStaffInfoModel::SEX_FEMALE) {
            $returnDays = array_merge($returnDays, $holidayGroup['female_days']);
        }
        //中国籍
        if ($staffInfo['country_id'] == HrStaffItemsModel::NATIONALITY_CHINA) {
            $returnDays = array_merge($returnDays, $holidayGroup['china_holidays']);
        }
        sort($returnDays);
        return $returnDays;
    }

    /**
     * 获取员工维度的法定节假日  【不包含 补的PH】
     * @param array $params #['staff' => [['staff_info_id','sex','country_id',week_working_day]],'date']
     * @return array
     */
    public function getStaffHoliday(array $params = []): array
    {
        $data         = [];
        $holidayGroup = $this->delHolidayGroup($params);
        if (isset($params['staff'])) {
            $staffPublicHoliday = (new StaffPublicHolidayRepository($this->lang,
                $this->timeZone))->getMultiStaffData(array_column($params['staff'], 'staff_info_id'));
            foreach ($params['staff'] as $item) {
                $data[$item['staff_info_id']] = array_merge($this->delStaffHoliday($item, $holidayGroup),
                    $staffPublicHoliday[$item['staff_info_id']] ?? []);
            }
        }
        return $data;
    }

}