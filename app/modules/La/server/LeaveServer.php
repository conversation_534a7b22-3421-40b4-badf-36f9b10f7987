<?php
/**
 * Created by PhpStor<PERSON>.
 * User: nick
 * Date: 2021/9/8
 * Time: 2:26 PM
 */

namespace FlashExpress\bi\App\Modules\La\Server;

use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Modules\La\Server\Vacation\AnnualServer;
use FlashExpress\bi\App\Modules\La\Server\Vacation\MaternityServer;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffPublicHolidayRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\BaseServer;

use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Models\backyard\StaffDaysFreezeModel;
use FlashExpress\bi\App\Repository\BySettingRepository;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\LeaveServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use Exception;
use FlashExpress\bi\App\Modules\La\Server\Vacation\InternationalServer;

class LeaveServer extends GlobalBaseServer {

    public static $holiday = [];
    public $leaveObject;

    /**
     * 根据员工信息 计算区间内是否存在休息日 返回对应休息日日期数组
     * 用于计算 请假区间 扣除包含休息日的天数
     * @param $staff_id
     * @param $start_date
     * @param string $end_date
     * @return array
     */
    public function staff_off_days($staff_id, $start_date, $end_date = '')
    {
        if (empty($end_date)) {
            $end_date = $start_date;
        }

        //获取员工信息
        $model      = new StaffRepository($this->lang);
        $staff_info = $model->getStaffPosition($staff_id);

        $rest = $model->get_work_days_between($staff_id, $start_date, $end_date);
        $rest = !empty($rest) ? array_column($rest, 'date_at') : [];

        $country_info = (new StaffRepository())->getStaffCountryInfo([$staff_id]);
        $holiday_data = $this->ph_days([
            'staff' => [
                [
                    'staff_info_id'      => $staff_id,
                    'sex'                => $staff_info['sex'] ?? 1,
                    'country_id'         => $country_info[$staff_id] ?? 1,
                    'week_working_day'   => $staff_info['week_working_day'] ?? 6,
                    'node_department_id' => $staff_info['node_department_id'] ?? 0,
                    'job_id'             => $staff_info['job_id'] ?? 0,
                ],
            ],
        ]);
        if (isset($holiday_data[$staff_id])) {
            $rest = array_merge($rest, $holiday_data[$staff_id]);
        }

        $this->logger->write_log("ignore rest_day {$staff_id} ".json_encode($rest), 'info');

        return $rest;

    }

    /**
     * 获取员工维度的法定节假日
     * 从方法与HCM (new HolidayService())->getStaffHoliday 一致 修改请都改！！
     * @param $staff_info ['staff' => [['staff_info_id','sex','country_id',week_working_day]],'month'=>'']
     * @return array
     */
    public function ph_days($staff_info)
    {
        $staff_info['date'] = isset($staff_info['month']) ? $staff_info['month'] . '-01' : date('Y-m-d',strtotime('-1 years'));
        return (new HolidayServer())->getStaffHoliday($staff_info);
    }

    /**
     * 老挝6天班固定休息日的员工，增加5月2日为PH
     * @param $staff_info
     * @return array
     */
    public function getPhDays2022($staff_info){
        $staffId = $staff_info['staff_info_id'];
        $weekWorkingDay = $staff_info['week_working_day'];
        $nodeDepartmentId = $staff_info['node_department_id'];
        $jobId = $staff_info['job_id'];
        if($weekWorkingDay != 6){
            return [];
        }

        $settingEnv = (new SettingEnvServer())->listByCode(['staff_ph','department_jobtitle_ph','add_ph_days_2022']);
        $settingEnv = array_column($settingEnv,'set_val','code');
        $staffIds = !empty($settingEnv['staff_ph']) ? explode(',', $settingEnv['staff_ph']) : [];
        $departmentConfig = !empty($settingEnv['department_jobtitle_ph']) ? explode(',', $settingEnv['department_jobtitle_ph']) : [];
        $addPhDays2022 = !empty($settingEnv['add_ph_days_2022']) ? explode(',', $settingEnv['add_ph_days_2022']) : [];

        if(in_array($staffId, $staffIds)) {
            $dates = $this->formatPhDays2022($addPhDays2022);
            $this->getDI()->get('logger')->write_log("6天班员工{$staffId}命中工号白名单增加PH:". json_encode($dates),'info');
            return $dates;
        }

        $departmentConfig = $departmentConfig ? array_flip($departmentConfig) : [];
        if(! isset($departmentConfig[$nodeDepartmentId . '-' . $jobId])){
            $this->getDI()->get('logger')->write_log("6天班员工{$staffId}未命中部门职位白名单",'info');
            return [];
        }

        $dates = $this->formatPhDays2022($addPhDays2022);
        $this->getDI()->get('logger')->write_log("6天班员工{$staffId}命中部门职位白名单增加PH:". json_encode($dates),'info');
        return $dates;
    }

    /**
     * 获取2022的新增的ph
     * @param $phDays2022
     * @return array
     */
    public function formatPhDays2022($phDays2022){
        if(empty($phDays2022)){
            $this->getDI()->get('logger')->write_log("命中白名单员工ph未配置",'error');
            return [];
        }
        $dates = [];
        foreach($phDays2022 as $v){
            if(empty($v)){
                continue;
            }
            $dates[] = $v;
        }
        return $dates;
    }

    //老挝固定额度 应有年假
    public function get_year_leave_days($staff_info)
    {
        return 15;
    }


    //年假额度 最大上限
    public function get_year_leave_max_days($staff_info)
    {

        return 15;
    }



    //根据额度 标记 年假所属年份
    public function year_flag($staffId,$insert,$month_arr,$leave_param){
        $year_type = $leave_param['year_type'];
        $left_days = $leave_param['left_days'];
        $leaveStartTime = $leave_param['leaveStartTime'];
        // 拼接 拆分表 归属年 year_at 字段
        $start_year = date('Y',strtotime($leaveStartTime));
        $add_row = array();//出现不够减情况 额外新增一条  merge到 insert
        foreach ($insert as $k => $in) {
            $insert[$k]['year_at'] = date('Y', time());
            //优先逻辑  如果日期 在 当年 3月 31号 失效之前 才计算剩余假期 打标记 否则 一律按当年算
            if (!in_array(date('m', strtotime($in['date_at'])), $month_arr)) {
                $insert[$k]['year_at'] = date('Y',strtotime($in['date_at']));
                continue;
            } else {
                if ($year_type == 1)//下面有可能更改为1
                    $insert[$k]['year_at'] = $start_year;
                if ($year_type == -1)
                    $insert[$k]['year_at'] = date('Y', strtotime("{$leaveStartTime} -1 year"));

                if ($year_type == 2) {//需拆分
                    $duration = empty($in['type']) ? 1 : 0.5;
                    if ($left_days > 0 && ($left_days - $duration >= 0)) {//还够减
                        $insert[$k]['year_at'] = date('Y', strtotime("{$leaveStartTime} -1 year"));
                        $left_days             = $left_days - $duration;
                    } else if ($left_days > 0 && ($left_days - $duration < 0)) {//不够减了（剩余0。5 本次记录 需要1天 只能是这种情况 把本条记录更改为半天 额外新增一条半天记录）
                        $insert[$k]['type']    = 1;
                        $insert[$k]['year_at'] = date('Y', strtotime("{$leaveStartTime} -1 year"));

                        //拼接剩下半天 标记归属年 为今年 merge
                        $add_row[0]['staff_info_id'] = $staffId;
                        $add_row[0]['date_at']       = $in['date_at'];
                        $add_row[0]['type']          = 2;
                        $add_row[0]['year_at']       = $start_year;

                        $left_days = 0;//减没了
                        $year_type = 1;//剩下的 都是 当年的了
                    } else if ($left_days == 0) {//上次循环 减没了
                        $insert[$k]['year_at'] = $start_year;
                        $year_type             = 1;
                    }

                }
            }

        }

        if (!empty($add_row))
            $insert = array_merge($insert, $add_row);
        return $insert;
    }


    public function check_year_leave($leave_info, $staff_info,$have_days)
    {
        $audit_model      = new AuditRepository($this->lang);
        $staffId          = $staff_info['staff_info_id'];
        //去年剩余额度 按申请开始时间年为准 获取前一年数据
        $last_year_info = $audit_model->get_last_year_info($staffId, date('Y', strtotime("{$leave_info['leave_start_time']} -1 year")));
        if (empty($last_year_info)) {
            $last_year_left = 0;

        } else {
            $last_year_left = $last_year_info['left_days'] >= 0 ? $last_year_info['left_days'] : 0;
        }

        $freeze_info = StaffDaysFreezeModel::findFirst($staffId);
        $have_days = 0;
        if(empty($have_days))
            $have_days = $freeze_info->days;
        //四舍五入
        $int_day   = floor($have_days);//2
        $have_days = $have_days > ($int_day + 0.5) ? ($int_day + 0.5) : $int_day;//2.5

        $month_arr = enums::LEAVE_INVALID_MONTH;
        $apply_month = date('m',strtotime($leave_info['leave_start_time']));
        if (!in_array($apply_month, $month_arr)) {
            return $have_days;
        }else{
            return $have_days + $last_year_left;
        }
    }


    public function check_leave_4($need_days, $limit_days,$used_days,$sub_type,$leave_info)
    {

        if(empty($sub_type) || !in_array($sub_type,array(4,5)))
            return  $this->getTranslation()->_('miss_args');
        //只有老挝 可以申请今天、昨天、前天或未来的日期
        if ($leave_info['leave_start_time'] < date('Y-m-d', time() - 48 * 3600) && empty($leave_info['is_bi'])) {
            return $this->getTranslation()->_('leave_3_notice');
        }
        /**
         * 定制逻辑
         * 每次生产额度105天
        怀孕期间流产或者终止妊娠60天  type 1
        单亲父母多享受15天  type 2
        特殊规定：女性可以申请100天，剩下的5天给予丈夫休带薪假期  type 3
         */
        if($sub_type == 5)
            $limit_days = 120;
//        if($sub_type == 4) 选择 无 天数不变

        if ($used_days >= $limit_days || ($need_days + $used_days) > $limit_days)
            return $this->getTranslation()->_('leave_limit');

        return true;
    }



    //老挝没有 这逻辑 返回0
    public function over_one_year_days($staff_info){
        return 0;

    }



    //获取 休息次数 请休息日类型假期用 从请假校验逻辑 搬过来的
    public function get_rest_times($staff_info,$leave_info){
        $staff_id = $staff_info['staff_info_id'];
        //获取请假月份 开始 结束 日期 新需求 http://193x782t53.imwork.net:29667/zentao/story-view-4065-1-project-89.html 周期从自然月更改为 30天周期
        //更改为 周期为自然周 判断请假日期 所在星期 是否存在记录 业务逻辑不变 http://193x782t53.imwork.net:29667/zentao/story-view-4281.html
        $today  = date('Y-m-d', strtotime($leave_info['leave_start_time']));
        $monday = weekStart($today) . " 00:00:00";
        $sunday = weekEnd($today) . " 23:59:59";

        //如果请休息日当天为 轮休 提示轮休日不用请假 https://shimo.im/docs/tRWKQ8wrt9JVWDWP/read
        $staff_re = new StaffRepository($this->lang);
        $is_work  = $staff_re->getWorkdays(array($staff_id), $today);
        if (!empty($is_work))//当天是休息日 不用请假
            return $this->checkReturn(-3, $this->getTranslation()->_('rest_day'));

        $audit_model = new AuditRepository($this->lang);
        $month_leave = $audit_model->get_leave_days($staff_id, $monday, $sunday, enums::LEAVE_TYPE_15);
        //新需求 如果是 ph 不记入休息日 天数
        $country_info = (new StaffRepository($this->lang))->getStaffCountryInfo([$staff_id]);
        $holiday_data = $this->ph_days([
            'staff' => [
                [
                    'staff_info_id' => $staff_id ,
                    'sex' => $staff_info['sex'] ?? 1,
                    'country_id' => $country_info[$staff_id] ?? 1,
                    'week_working_day' => $staff_info['week_working_day'] ?? 6,
                    'node_department_id' => $staff_info['node_department_id'] ?? 0,
                    'job_id' => $staff_info['job_id'] ?? 0,
                ]
            ]
        ]);
        $ph_day = $holiday_data[$staff_id] ?? array();

        //ph 不让申请休息日 https://shimo.im/docs/tWPXXJD3xhQhKy83
        if(in_array($today,$ph_day))
            return $this->checkReturn(-3, $this->getTranslation()->_('ph_day'));

        //每种类型的假期 都有多少天
        $days = $times = 0;
        if(empty($month_leave))
            return $this->checkReturn(array('code' => 1,'msg'=>'','data' => $times));

        $leave_dates = array();
        foreach ($month_leave as $day) {
            if (in_array(date('Y-m-d', strtotime($day['leave_start_time'])), $ph_day))
                continue;

            $days += $day['leave_day'];
            $leave_dates[] = date('Y-m-d', strtotime($day['leave_start_time']));
        }

        if(empty($leave_dates))
            return $this->checkReturn(array('code' => 1,'msg'=>'','data' => $times));

        //排除ph后 剩余的 已请 休息日
        $leave_dates = array_unique($leave_dates);
        sort($leave_dates);
        //总申请请假次数
        $times = count($month_leave);

        //新增需求 如果超过5次 但是申请过的休息日当天出勤时间大于等于8.5小时且没有申请OT，不占用休息日请假次数。
        //周期改为一周 次数为1天
        if(empty($times))
            return $this->checkReturn(array('code' => 1,'msg'=>'','data' => $times));

        //获取大于8。5小时的 考勤记录
        $att_model = new AttendanceRepository($this->lang, $this->timezone);
        $att_list  = $att_model->getSignInfo($staff_id, $leave_dates[0], end($leave_dates));
        if(empty($att_list))
            return $this->checkReturn(array('code' => 1,'msg'=>'','data' => $times));

        //存在 休息日来上班的情况 看有没有申请加班
        $check_ot_date = array();
        foreach ($att_list as $att) {
            if (in_array($att['date_at'], $leave_dates) && !empty($att['second_last']) && $att['second_last'] / 3600 >= 8.5) {
                $check_ot_date[] = $att['date_at'];
            }
        }

        if(empty($check_ot_date))
            return $this->checkReturn(array('code' => 1,'msg'=>'','data' => $times));

        //判断是否有 ot记录 如果没有 次数减1
        $ot_model = new OvertimeRepository($this->timezone);
        foreach ($check_ot_date as $ot_date) {
            $record = $ot_model->getOtByDate($staff_id, $ot_date);
            if (empty($record))
                $times--;
            else {//如果 存在ot  并且 驳回撤销状态 也不算次数
                $st_arr = array_column($record, 'state');
                if (!in_array(1, $st_arr) && !in_array(2, $st_arr))
                    $times--;
            }
        }
        return $this->checkReturn(array('code' => 1,'msg'=>'','data' => $times));
    }

    //获取 额度详情信息列表
    public function getAnnualDetail($param){
        $leaveObject = $this->getLeaveObj(enums::LEAVE_TYPE_1);
        return $leaveObject->detailList($param);
    }

    //获取对应假期的额度
    public function getVacationDays($staffId,$leaveType, $extend = []){
        $param['staff_id'] = $staffId;
        $param['leave_type'] = $leaveType;
        $param = array_merge($param,$extend);
        $leaveObject = $this->getLeaveObj($param['leave_type']);
        return $leaveObject->handleSearch($param);
    }

    //非审核通过 撤销操作 返还额度
    public function cancelVacation($auditInfo,$staffInfo,$state, $extend = []){
        $extend['status'] = $state;
        $this->leaveObject = $this->getLeaveObj(intval($auditInfo['leave_type']));
        $this->leaveObject->returnRemainDays($auditInfo['audit_id'],$staffInfo,$extend);
    }

    /**
     * 对应本国的 所有类型 映射类
     * @param int $leaveType
     * @return AnnualServer|string
     * @throws ValidationException
     */
    public function getLeaveObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_1:
                $leaveObj =  new AnnualServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_19:
                $leaveObj =  new InternationalServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_4:
                $leaveObj =  new MaternityServer($this->lang,$this->timeZone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }
    //task 初始化额度 调用
    public function getInstanceObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_19://跨国探亲
                $leaveObj = InternationalServer::getInstance($this->lang,$this->timezone);
                break;
            case enums::LEAVE_TYPE_4://产假
                $leaveObj = MaternityServer::getInstance($this->lang,$this->timezone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }

    /**
     * @param
     * @throws ValidationException
     * @return array
     */
    public function saveVacation($param){
        $this->leaveObject = $this->getLeaveObj(intval($param['leave_type']));

        $auditServer = new AuditServer($this->lang, $this->timeZone);
        $staffRe     = new StaffRepository($this->lang);
        $staffInfo   = $staffRe->getStaffPosition($param['staff_id']);
        $typeData    = $auditServer->staffLeaveType($staffInfo);
        $leave_lang  = AuditRepository::$leave_type;

        if (empty($typeData)) {
            throw new ValidationException('wrong leave type');
        }
        if (!in_array($param['leave_type'], array_keys($typeData)) && $param['leave_type'] != enums::LEAVE_TYPE_15) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004') . $this->getTranslation()->_($leave_lang[$param['leave_type']]));
        }
        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            $audit_id = $this->leaveObject->handleCreate($param);

            //没生成id
            if (empty($audit_id)) {
                throw new ValidationException($this->getTranslation()->_('1009'));
            }

            //非工具操作申请 创建审批相关
            if (empty($param['is_bi'])) {
                $param['time_out'] = $this->leaveObject->timeOut ?? null;
                $auditServer->saveApproval($audit_id, $param);
            }

            $db->commit();

            $return['data'] = ['leave_day' => $this->leaveObject->leave_day ?? 0];
            return $this->checkReturn($return);
        }catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }


}