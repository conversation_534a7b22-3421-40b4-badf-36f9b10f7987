<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/5/8
 * Time: 下午3:25
 */

namespace FlashExpress\bi\App\Modules\La\Server;

use FlashExpress\bi\App\Enums\AttendanceCalendarEnums;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\AttendanceCalendarServer as BaseServer;

class AttendanceCalendarServer extends BaseServer
{
    public function getHolidayData($startDate='',$endDate=''): array
    {
        $staff_info_id = $this->staffInfo['staff_info_id'] ?? 0;
        $country_info = (new StaffRepository())->getStaffCountryInfo([$staff_info_id]);
        $holiday_data = (new LeaveServer())->ph_days([
            'staff' => [
                [
                    'staff_info_id' => $staff_info_id ,
                    'sex' => $this->staffInfo['sex'] ?? 1,
                    'country_id' => $country_info[$staff_info_id] ?? 1,
                    'week_working_day' => $this->staffInfo['week_working_day'] ?? 6,
                    'node_department_id' => $this->staffInfo['node_department_id'] ?? 0,
                    'job_id' => $this->staffInfo['job_id'] ?? 0,
                ]
            ]
        ]);
        return $holiday_data[$staff_info_id] ? array_flip($holiday_data[$staff_info_id]) : [];
    }
}