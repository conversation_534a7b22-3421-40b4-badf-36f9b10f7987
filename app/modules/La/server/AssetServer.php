<?php

namespace FlashExpress\bi\App\Modules\La\Server;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HeadquartersAddressModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Repository\AssetRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\HrStaffContractRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\WmsRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AssetServer as BaseAssetServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\AuditServer;
use App\Country\Tools;

class AssetServer extends BaseAssetServer
{

    public function __construct($lang = 'zh-CN', $timezone = '+08:00')
    {
        parent::__construct($lang, $timezone);
        $this->timezone = $timezone;
        $this->other    = Tools::reBuildCountryInstance(new OtherRepository($timezone, $lang), [$timezone, $lang]);
        $this->wms      = Tools::reBuildCountryInstance( new WmsRepository());
        $this->storeS   = Tools::reBuildCountryInstance(new SysStoreServer($timezone), [$timezone]) ;
        $this->audit    = Tools::reBuildCountryInstance(new AuditRepository($this->lang), [$this->lang]) ;
        $this->staff    = Tools::reBuildCountryInstance( new StaffRepository($this->lang), [$this->lang]);
        $this->asset    = Tools::reBuildCountryInstance( new AssetRepository($this->lang), [$this->lang]);
        $this->contract = Tools::reBuildCountryInstance(new HrStaffContractRepository($this->lang), [$this->lang]) ;
    }

    /**
     * 创建订单
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addAssetsOrder($paramIn = [], $userinfo)
    {
        $time = date("YmdHis");
        $create = gmdate('Y-m-d H:i:s');
        $rand = rand(1000, 9999);
        $orderId = $time . $rand;
//        $auditBLL = new \FlashExpress\bi\App\Server\AuditServer($this->lang, $this->timezone);
        $auditBLL = Tools::reBuildCountryInstance(new AuditServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
        if (!$auditBLL->getASPermission($userinfo)) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }
        //获取订单相关人信息
        //$admin_group = UC('wmsRole')['admin_group'];
        $organization_id = $userinfo['organization_id'];
        $uInfo = $this->wms->userStoreInfo($organization_id);
        $uInfoArr = array_merge(empty($uInfo) ? [] : $uInfo, $userinfo);
        $staffInfo = (new StaffServer())->getStaffById($userinfo['staff_id']);
//        $store_info = $this->storeS->getStoreByid($organization_id);
//        $manage_region = isset($store_info['manage_region']) ? $store_info['manage_region'] : '';
//        $store_category = isset($store_info['category']) ? $store_info['category'] : '';
        //格式化订单数据

        $headOffice=[];
        $pc_code = '';
        if($uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2']) {
            //产品确定 除泰国其他都是一个总部
            $headOffice =  $this->wms->getHeadquartersAddress();
            /**
             * 14771【BY-TH/PH/MY/LA】申请物料、申请资产和SCM接口逻辑优化
             * 总部员工申请资产/耗材时，需要记录成本中心
             */
            $pc_code = $this->wms->getPcCode($staffInfo);
        }

        $serialNo = uniqid('as');
        $returnData['data']['dataList'] = [];
        $orderData['order_union_id'] = $serialNo;//订单号
        $orderData['organization_id'] = $organization_id ;//网点编号
        $orderData['pc_code'] = $pc_code;//成本中心
        $orderData['staff_info_id'] = $userinfo['staff_id'];
        $orderData['status'] = 1;
        $orderData['created_at'] = $create;
        $orderData['reason'] = $paramIn['reason'];//原因
        $orderData['shipping_user'] = $uInfoArr['name'];//收货人姓名
        $orderData['consignee_phone'] = $staffInfo && isset($staffInfo['mobile']) ? $staffInfo['mobile'] : '';
        $orderData['consignee_address'] = $uInfoArr['organization_type'] == 2 ? $headOffice['address'] : $uInfoArr['detail_address'];
        $orderData['province_code'] = $uInfoArr['organization_type'] == 2 ? $headOffice['province_code']: $uInfoArr['province_code'];//省
        $orderData['city_code'] = $uInfoArr['organization_type'] == 2 ? $headOffice['city_code'] : $uInfoArr['city_code'];//市
        $orderData['district_code'] = $uInfoArr['organization_type'] == 2 ? $headOffice['district_code'] :$uInfoArr['district_code'];;//区
        $orderData['postal_code'] = $uInfoArr['organization_type'] == 2 ? $headOffice['postal_code'] : $uInfoArr['postal_code'];//邮编

        $orderData['use_people_id'] = empty($paramIn['use_people_id']) ? 0 : $paramIn['use_people_id'];
        $orderData['use_store_id'] = empty($paramIn['use_store_id']) ? '' : $paramIn['use_store_id'];
        if (empty($paramIn['assets'])) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }
        $orderDetailData = [];
        $assetIds = array_column($paramIn['assets'], "id");
        // 根据用户的权限 过滤 不合法的ID
        list($assetType, $assetIds) = $this->filterAssetsIds($assetIds, $userinfo);
        if (!$assetIds) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        //获取资产
        $goodsInfo = $this->goodsInfo($assetIds);
        if (empty($goodsInfo)) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        foreach ($goodsInfo as $key => $value) {
            $orderDetailData[$value['id']] = $value;
        }

        $orderDetailDataFormat = array();
        foreach ($paramIn['assets'] as $key => $value) {

            if (!isset($value['nums']) || $value['nums'] <= 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('7101'));
            }
            if (isset($orderDetailData[$value['id']])) {
                $orderDetailDataFormat[$key]['goods_id'] = isset($value['id']) ? $value['id'] : 0;
                $orderDetailDataFormat[$key]['recomment_num'] = isset($value['nums']) ? $value['nums'] : 0;
                //$orderDetailDataFormat[$key]['approval_num'] = isset($value['nums']) ? $value['nums'] : 0;
                $orderDetailDataFormat[$key]['created_at'] = $create;
            }
        }


        $db = WorkflowModel::beginTransaction($this);
        try {
            $orderId = $this->audit->addOrderInfo($orderData, $orderDetailDataFormat, $assetType);
            if (!$orderId) {
                $this->getDI()->get('logger')->write_log('assets_order_info =>' . json_encode($paramIn) . json_encode($userinfo), 'error');
                return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
            }

            $returnData['data'] = $orderId;
            $staffId = $uInfoArr['id'];

            $info = $this->staff->getStaffPositionv3($staffId);
            //用人部门ID查找到公司
            $dInfo = SysDepartmentModel::findFirst([
                'conditions' => 'id = :department_id:',
                'bind' => [
                    'department_id' => $info['department_id'],
                ]
            ]);
            if (!(new ApprovalServer($this->lang, $this->timezone))->create($orderId,
                    $assetType == self::ASSET_TYPE[0] ? enums::$audit_type['AS'] : enums::$audit_type['ASP'],
                    $userinfo['staff_id'],null, ['flow_code' => 'v1', 'category' => $info['category'] ?? '', 'company_id' => $dInfo ? $dInfo->company_id : '']
                )
            ) {
                throw new \Exception('create workflow failing');
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('assets_order_info =>' . json_encode($paramIn) . json_encode($userinfo), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }

//        if (!$this->other->insterUnion($insertAuditData)) {
//            $this->getDI()->get('logger')->write_log('assets_order_info =>' . json_encode($paramIn) . json_encode($userinfo), 'error');
//            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
//        }
//
//        //[5]追加审批日志
//        $log['staff_id'] = $staffId;
//        $log['type'] = $assetType == self::ASSET_TYPE[0] ? enums::$audit_type['AS'] : enums::$audit_type['ASP'];
//        $log['original_type'] = 1;
//        $log['original_id'] = $orderId;
//        $log['operator'] = $staffId;
//        $log['operator_name'] = $userinfo['name'];
//        $log['to_status_type'] = 1;
//        $this->other->insertLog($log);
        return $this->checkReturn($returnData);
    }



}
