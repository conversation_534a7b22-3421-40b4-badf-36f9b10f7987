<?php

namespace FlashExpress\bi\App\Modules\La\Server;

use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\ProbationServer as BaseProbationServer;
use Exception;

/**
 * @method probationAuditUseLock()
 */
class ProbationServer extends BaseProbationServer
{

    public $special_job_grade = 17; // 17 级按照 18 级的转正时间走 但是没有第三次评估
    public $special_job_day = 3; // 17 级按照 18 级的转正时间走 但是没有第三次评估    17 和 18 级 评估截止时间误差数

    /**
     * @description:提交转正评估评审
     * @param string $id
     * @param string $audit_id 用户 id
     * @param array $params
     * @param int $deadline_at_one 第一阶段上上级过期时间
     * @return     : []
     * @throws ValidationException
     * <AUTHOR> L.J
     * @time       : 2021/9/9 14:41
     */
    public function probation_audit( $id = '', $audit_id = '', $params = [], $deadline_at_one = 3,$is_fail_msg='') {

        if (empty($id) || empty($audit_id)) {
            throw new \Exception($this->getTranslation()->_('miss_args'));
        }
        $lang = $this->getTranslation();
        //没有分数不行!!
        if(empty($params['score'])){
            throw new ValidationException('score '. $lang->_('miss_args'));
        }
        //获取评估信息
        // 将消息 已读
        $probation_audit_data = HrProbationAuditModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
        //没有找到数据
        if(empty($probation_audit_data)){
            throw new ValidationException($lang->_('2201'));
        }
        $probation_audit_data = $probation_audit_data->toArray();
        //评估人不是 当前人
        if($probation_audit_data['audit_id'] != $audit_id){
            throw new ValidationException($lang->_('2107'));
        }
        if($probation_audit_data['audit_status'] != self::AUDIT_STATUS_PENDING){
            throw new ValidationException($lang->_('4012'));
        }

        //获取评估主记录
        $probation_data = HrProbationModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => [
                'id' => $probation_audit_data['probation_id']
            ]
        ]);
        $probation_data = !empty($probation_data)? $probation_data->toArray() : [];

        if (!empty($probation_data['status']) && $probation_data['status'] == HrProbationModel::STATUS_FORMAL) {
            throw new ValidationException($lang->_('probation_status_err_1'));
        }
        
        //下个审批人
        $manager_id = $this->getManagerId($audit_id);
        //查询用户等级
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $probation_data['staff_info_id'] ?? '',
            ],
        ]);
        $staffInfo = !empty($staffInfo) ? $staffInfo->toArray() : [];

        $is_send = false;                    //是否继续发送下一级评估
        //获取增加天数
        $evaluate_time = isset($staffInfo['job_title_grade_v2']) && $staffInfo['job_title_grade_v2'] > ($this->job_grade_exceed) ? $this->duration_day_exceed : $this->duration_day;//递增天数
        //18 级以下  发两次
        $is_send = $probation_audit_data['audit_level'] == self::AUDIT_LEVEL_1 && !empty($manager_id);
        //18 级以上 发三次  第三次是 cpo

         //这里的分界线是  17 special_job_grade
        if (isset($staffInfo['job_title_grade_v2']) && $staffInfo['job_title_grade_v2'] > ($this->special_job_grade) &&
            $probation_audit_data['version'] == $this->version) {
            $cpo_staff_info_id = $this->cpo_staff_id;
            //如果当前审批人 == cop 或者 当前阶段是第三级评估  则不发送了   false 为不发送
            $is_send = !($audit_id == $cpo_staff_info_id || $probation_audit_data['audit_level'] >= self::AUDIT_LEVEL_3);
            //如果当前是第二级评估   则 下次评估人为 cpo 评估
            $manager_id    = $probation_audit_data['audit_level'] >= self::AUDIT_LEVEL_2  ? $cpo_staff_info_id : $manager_id;
            $evaluate_time = $this->duration_day_exceed;//递增的截止天数
        }
        //分数
        $score       = $params['score'];
        $remark      = isset($params['remark']) ? $params['remark'] : '';
        $pic         = isset($params['pic']) ? $params['pic'] : '';
        $job_content = $params['job_content'] ?? ''; //工作内容
        $item = []; //hr_probation表
        $data = []; //hr_probation_audit表

        //如果info与库里不同,证明已经修改
        foreach ($score['list'] as $k => $v) {
            foreach ($v['list'] as $kk => $vv) {
                if ($lang->_($vv['info_key']) != $vv['info']) {
                    $score['list'][$k]['list'][$kk]['is_update'] = 1;
                }
            }
        }
        //自己根据规则算一遍
        $score = $this->getScoreFromTpl($score, $probation_audit_data['cur_level']);
        //没有分数不行!!
        if(empty($score)){
            throw new \Exception('score 没有获取到!!!');
        }
        $data['score']        = json_encode($score, JSON_UNESCAPED_UNICODE);//score存json
        $data['audit_status'] = 2;
        $data['remark']       = isset($params['remark']) ? $params['remark'] : '';
        $data['pic']          =  isset($params['pic']) ? $params['pic'] : '';
        $item['updated_at'] = gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600);
        $data['show_time']   = date("Y-m-d H:i:s");
        $data['updated_at'] = $item['updated_at'];
        $data['job_content'] = $params['job_content'] ?? ''; //工作内容


        //第一阶段
        if ($probation_audit_data['cur_level'] == self::CUR_LEVEL_FIRST) {
            $item['first_score']            = $this->getScore($score['score'], 1);
            $item['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_RUN;
            if (!$is_send) {
                //不给下一级发了就说明是最后一级了
                $item['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_DONE;
                if ($item['first_score'] >= $this->passMark) {
                    $item['first_status'] = HrProbationModel::FIRST_STATUS_PASS;
                }
            }
        } //第二阶段
        else {
            $item['second_score']            = $this->getScore($score['second_score'], 1);
            //第二阶段上上级评过+第二阶段上级改成上级和上上级都更改主表
            if ($item['second_score'] >= $this->passMark) {
                $item['status'] = self::STATUS_PASS;    //已通过
                if ($item['updated_at'] > $probation_data['formal_at']) {
                    $is_send = false;
                    $item['status']          = self::STATUS_FORMAL;
                    $item['formal_staff_id'] = $audit_id;
                    $item['formal_at']       = gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600);
                }
            } else {
                $item['status'] = self::STATUS_NOT_PASS;    //未通过
            }
            $item['remark'] = $data['remark'];
            //把通过不通过的状态传过去
            $data['status'] = $item['status'];
            //不发了 赋值终态
            if (!($is_send && !empty($manager_id))) {
                $item['second_audit_status'] = HrProbationModel::SECOND_AUDIT_STATUS_DONE;
                if (in_array($item['status'],[self::STATUS_PASS,self::STATUS_FORMAL])) {
                    $item['second_status'] = HrProbationModel::SECOND_STATUS_PASS;
                }
            }
        }
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $db->updateAsDict('hr_probation_audit', $data, ['conditions' => 'id=?', 'bind' => [$probation_audit_data['id']]]);
            $db->updateAsDict("hr_probation", $item, ['conditions' => 'id=?', 'bind' => [$probation_data['id']]]);

            //需要判断是否需要评审
            if ($is_send && !empty($manager_id)) {

                //下一级评审，内容大部分相同
                $tmp                  = [];
                $tmp['probation_id']  = $probation_audit_data['probation_id'];
                $tmp['staff_info_id'] = $probation_audit_data['staff_info_id'];
                $tmp['tpl_id']        = $probation_audit_data['tpl_id'];
                $tmp['audit_id']      = $manager_id;
                $tmp['audit_level']   = ((int)$probation_audit_data['audit_level']) + 1;
                $tmp['cur_level']     = $probation_audit_data['cur_level'];
                $tmp['audit_status']  = self::AUDIT_STATUS_PENDING;
                $tmp['status']        = 0;
                $tmp['score']         = $data['score'];
                $tmp['created_at']    = $item['updated_at'];
                $tmp['updated_at']    = $item['updated_at'];
                $tmp['remark']        = $remark;                     //同步上级评审意见
                $tmp['pic']           = $pic;                        //同步上级图片
                $tmp['job_content']   = $job_content;                //工作内容
                $tmp['version']       = $probation_audit_data['version'];         //版本
                $tmp['show_time']     = date("Y-m-d H:i:s");

                if ($probation_audit_data['hp_is_active'] == 1){
                    $now_date = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
                    $tmp['deadline_at']        = $this->getDateByDays($now_date, 3, 1);
                }else{
                    $day                = $evaluate_time[$probation_audit_data['cur_level']][$tmp['audit_level']] ?? $deadline_at_one;
                    $tmp['deadline_at'] = $this->getDateByDays($probation_audit_data['deadline_at'], $day, 1);
                }
                $db->insertAsDict('hr_probation_audit', $tmp);
            }

            //如果是上上级评估需要发送消息
            if ($probation_audit_data['audit_level'] == self::AUDIT_LEVEL_2) {
                //插入消息表
                $msg_data = [
                    'probation_audit_id' => $id,
                    'created_at'         => gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600),
                    'updated_at'         => gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600),
                ];
                $res      = $this->getDI()->get('db')->insertAsDict('hr_probation_message', $msg_data);
                if (!$res) {
                    throw new \Exception($this->getTranslation()->_('no_server'));
                }
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log('ProbationServer_audit---' . $e->getMessage() . '-----' . $e->getLine());
            return $this->checkReturn(['msg' => $e->getMessage()]);
        }
        //给上级发送push
        if (!empty($tmp)) {
            $this->push_notice_higher($tmp['audit_id'], $tmp['staff_info_id']);
        }

        //记录转正日志
        if (isset($item['status']) && $item['status'] == self::STATUS_FORMAL) {
            $this->putFormalLog($audit_id, $probation_data['staff_info_id'], $probation_data['status'], self::STATUS_FORMAL);
        }

        return $this->checkReturn(['data' => true]);

    }


}
