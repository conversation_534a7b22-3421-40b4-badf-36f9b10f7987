<?php

namespace FlashExpress\bi\App\Modules\La\Server;

use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditPermissionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftOperateLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Modules\La\enums\OvertimeEnums;
use FlashExpress\bi\App\Modules\La\Server\Vacation\InternationalServer;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditListServer;
use FlashExpress\bi\App\Server\AuditServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\KpiServer;
use FlashExpress\bi\App\Server\OsPriceServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\Vacation\TrainingServer;
use FlashExpress\bi\App\Server\WorkdayServer;

class AuditServer extends GlobalBaseServer
{

    public static $c_days = 15;//老挝任何人都是15天
    protected $forbidden   = array(enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_2,enums::LEAVE_TYPE_4,enums::LEAVE_TYPE_19);//试用期不能申请的类型
    protected $time_limit_start = array(enums::LEAVE_TYPE_3, enums::LEAVE_TYPE_4,enums::LEAVE_TYPE_16);//除了这些假期 限制开始时间 只能当天以后
    public $one_time = array();//入职以来 只能申请一次
    public $one_send = array(enums::LEAVE_TYPE_16);//入职以来 限制总额不限制次数
    public $sub_day = array(1,2,19,24,16);//需要跳过休息日的类型
    protected $need_img = array(enums::LEAVE_TYPE_2);//图片必填的请假类型

    public $newVersionType = [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_19, enums::LEAVE_TYPE_4];//改版新结构类型
    /**
     * 获取权限
     * @param array paramIn
     * @return array
     */
    public function getListPermission( $paramIn = [] ) {

        //补卡
        $result['AT'] = 1; //默认存在
        //请假
        $result['LE'] = $this->isLeavePermission() ? 1 : 2;     //默认存在

        //LH移除
        //$result['LH'] = $this->checkLcDataPosition($paramIn) == true ? 1 : 2;
        //新耗材申请上线老的耗材入口移除
        //$result['Wms'] = $this->getWmsPermission($paramIn) == true ? 1 : 2;
        //修改里程权限
        $result['Mile'] = $this->getMilePermission($paramIn) == true ? 1 : 2;
        //加班-所有人都可以申请OT
        $result['OT'] = $this->getOTPermission($paramIn) == true ? 1 : 2;
        //出差
        $result['Trip'] = $this->getTripPermission($paramIn) == true ? 1 : 2;
        //车辆里程
        $result['Vehicle'] = $this->getVehiclePermission($paramIn) == true ? 1 : 2;
        //HC
        $result['HC'] = $this->checkHcDataRole($paramIn) == true ? 1 : 2;
        //补申请
        $result['apply'] = $this->getApplyPermission($paramIn) == true ? 1 : 2;
        //加班车
        $result['fleet'] = $this->getFleetPermission($paramIn) == true ? 1 : 2;
        //离职-所有人都可以申请离职
        $result['resign'] = $this->isResignPermission() ? 1 : 2;
        //外协员工
        $result['OS'] = $this->getOSPermission($paramIn) == true ? 1 : 2;
        //资产申请
        //$result['Asset'] = $this->getASPermission($paramIn) == true ? 1 : 2;
        //举报
        $result['Report'] = $this->getReportPermission($paramIn) == true ? 1 : 2;
        //到岗确认
        $result['Confirm'] = $this->getEntryConfirmPermission($paramIn) == true ? 1 : 2;
        //工资条pdf
        $result['salary'] = $this->salary_pdf_permission($paramIn) == true ? 1 : 2;
        //油费补贴
        $result['fuel_subsidy'] = $this->getFuelSubsidyPermission($paramIn) == true ? 1 : 2;
        //在职证明
        $result['on_job'] = $this->on_job_pdf_permission($paramIn) == true ? 1 : 2;
        //工资证明
        $result['payroll'] = $this->on_job_pdf_permission($paramIn) == true ? 1 : 2;
        //离职资产确认
        $result['resign_as'] = $this->getResignAssetPermision($paramIn) == true ? 1 : 2;
        //转岗
        $result['TF'] = $this->getJobtransferPermission($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_nw'] = $this->getFreightDiscNetworkPermission($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_shop'] = $this->getFreightDiscShopPermission($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_other'] = $this->getFreightDiscOtherPermission($paramIn) == true ? 1 : 2;
        //抄送列表
        $result['CC'] = 1;
        //黄牌项目出差
        $result['yc_Trip'] = $this->getYCTripPermission($paramIn) == true ? 1 : 2;

        // 销售CRM
        $result['Sales_CRM'] = $this->getSalesCRMPermission($paramIn) == true ? 1 : 2;

        //工服购买排除外协员工
        $result['InteriorOrder'] = $this->getInteriorOrdersPermission($paramIn) == true ? 1 : 2;

        // 后勤-报销申请
        $result['fuel_budget']  = 2;

        $result['my_interview'] = (new InterviewRepository($this->timezone))->myInterview($paramIn) == true ? 1 : 2;
        //外协特殊价格审批
        $result['os_price'] = (new OsPriceServer($this->timezone))->permission($paramIn) == true ? 1 : 2;

        //领用包材
        $result['package'] = 1;
        //审批-人事-KPI目标
        $result['kpi'] = (new KpiServer())->getKpiPermission($paramIn) ? 1 : 2;
        //新版资产申请权限
        $result['NAS'] = $this->getNewAssetPermission($paramIn) ? 1 : 2;

        //申请耗材
        $result['apply_consumables'] = $this->getConsumablesPermission($paramIn) ? 1 : 2;
        //OA文件夹
        $result['oa_folder'] = $this->OAFolderPermission() ? 1 : 2;
        //hcm入口
        $result['HCM'] = $this->isShowHCMPermission() ? 1 : 2;

        //过滤外协员工权限
        $paramIn['permission_list'] = $result;
        $result                     = $this->outsourcingPermissionFilter($paramIn);
        //耗材调拨入口
        $result['package_allot'] = $this->isShowPackageAllotPermission($this->staffInfo) ? 1 : 2;

        return $this->checkReturn(['data' => $result]);
    }


    /**
     * 获取物料申请权限
     * @param array paramIn
     * @return boolean
     */
    public function getWmsPermission($paramIn = [])
    {
        //[1]校验添加权限
        $admin_group = UC('wmsRole')['admin_group'];
        $staffId     = $paramIn['staff_id'];

        //admin小組有提交物料权限
        if (isset($admin_group[$staffId])) {
            return true;
        }
        return $this->check_wrs_permission($staffId);
    }

    /**
     * 检测耗材权限
     * 需求地址：https://l8bx01gcjr.feishu.cn/docs/doccnTWmgRJ1WpyCKY2dirWQ7jb
     * @param  string $staff_id 申请人ID
     * @return bool
     */
    public function check_wrs_permission($staff_id)
    {
        //总部的所有人都有权限申请
        $staff_re = new StaffRepository($this->lang);
        $info = $staff_re->getStaffPositionv3($staff_id);

        $setting_server = new SettingEnvServer();
        $wrs_staff = $setting_server->getSetVal('wrs_staff_check_wrs_ids');
        if (!empty($info['job_title']) && in_array($info['job_title'], explode(',', $wrs_staff))) {
            return true;
        } else {
            return false;
        }

        //获取耗材模块权限列表
        $module = AuditPermissionModel::MODULE_P_3;
        $list = AuditPermissionModel::get_permission($module);
        if(empty($list))
            return false;

        //按照网点可申请职位角色ID组合数组
        $permission = array();
        foreach ($list as $item) {
            $k = "{$item['store_category']}_{$item['job_title_id']}";
            $permission[$k] = $item['permission_value'];
        }
        $store_server = new SysStoreServer($this->lang,$this->timeZone);
        $store_info = $store_server->getStoreByid($info['organization_id']);
        if(empty($store_info))
            return false;
        $staff_key = "{$store_info['category']}_{$info['job_title']}";
        if(!empty($permission[$staff_key]))
            return true;

        return false;
    }

    /**
     * 获取资产申请权限 和 我的tab  公共资产显示
     * @param array paramIn
     * @param boolean paramIn 是否只判断白名单
     * @return boolean
     */
    public function getASPermission($paramIn = [], $isOnlyWhiteList = false)
    {
        $staffId = $paramIn['staff_id'];
        //[2]获取当前员工职位
        $staff_re = new StaffRepository($this->lang);
        $info = $staff_re->getStaffPositionv3($staffId);
        //申请权限
        //白名单 ：工号为[19685,24455,40450,25921,29053] 或者 职位是Regional Manager(79)、Assistant Regional Manager(269)
        //DC、SP网点: 网点正主管(职位是16)
        //SHOP网点: 网点正主管
        //HUB网点: 指定工号
        $staffArr = explode(',', env('asset_request_white_list', "24455,40450,38983,19685,47094,19060,21426,21328"));
        if (in_array($staffId, $staffArr) || in_array($info['job_title'], [
                enums::$job_title['regional_manager'],
                enums::$job_title['district_manager'],
                enums::$job_title['area_manager'],
            ])) {
            return true;
        }

        if ($isOnlyWhiteList === true) {
            // 只验证工号职位白名单
            return false;
        }

        return $this->check_asset_permission($info);
    }


    /**
     * 检测资产权限
     * 需求地址：https://l8bx01gcjr.feishu.cn/docs/doccnTWmgRJ1WpyCKY2dirWQ7jb
     * @param array $info 当前登陆用户信息
     * @return bool
     */
    public function check_asset_permission($info)
    {

        $setting_server = new SettingEnvServer();
        if($info['organization_type'] == 2){//总部的人 全部给权限
            return true;
        }
        $wrs_staff = $setting_server->getSetVal('wrs_staff_check_asset_ids');
        if(!empty($info['job_title']) && in_array($info['job_title'],explode(',',$wrs_staff))){
            return true;
        }else{
            return false;
        }
        //获取资产模块权限列表
        $module = AuditPermissionModel::MODULE_P_2;
        $list = AuditPermissionModel::get_permission($module);
        if(empty($list))
            return false;

        //按照网点可申请职位角色ID组合数组
        $permission = array();
        foreach ($list as $item) {
            $k = "{$item['store_category']}_{$item['job_title_id']}";
            $permission[$k] = $item['permission_value'];
        }
        //用于改需求文档第7步，老挝个人/公共资产权限调整。可申请权限配置
        //如果H0,网点类型是-1
        if($info['organization_type'] == 2) {
            //总部 的 store_category -1
            $staff_key = "-1_{$info['job_title']}";
            if(!empty($permission[$staff_key]))
                return true;
        }else{
            //网点的找网点类型
            $store_server = new SysStoreServer($this->lang,$this->timeZone);
            $store_info = $store_server->getStoreByid($info['organization_id']);
            if(empty($store_info))
                return false;
            $staff_key = "{$store_info['category']}_{$info['job_title']}";
            if(!empty($permission[$staff_key]))
                return true;
        }
        return false;
    }


    /**
     * 获取外协员工申请权限
     * @param array $paramIn
     * @return boolean
     */
    public function getOSPermission($paramIn = [])
    {
        $jobTitlesData        = UC('outsourcingStaff')['jobTitlesNot'];
        $hubRequestRole       = UC('outsourcingStaff')['osLongPeriodRequestRole'];
        $motorcadeRequestRole = UC('outsourcingStaff')['osMotorcadeRequestRole'];
        $roles                = $this->processingDefault($paramIn, 'positions', 3);
        $job_title            = $this->processingDefault($paramIn, 'job_title', 2);
        $organizationId       = $this->processingDefault($paramIn, 'organization_id', 2);
        $type                 = $this->processingDefault($paramIn, 'organization_type', 2);
        $departmentId         = $this->processingDefault($paramIn, 'department_id', 2);

        if ((!empty($jobTitlesData) || !empty($hubRequestRole)) || !empty($motorcadeRequestRole)) {
            //1. 短期外协：全网网点非快递员职位（非bike courier，van courier，boat courier）的所有正式员工
            //2. 长期外协：所属网点类型为[8]Hub、[9]OS、[4]SHOP(pickup-only)、[5]SHOP(pickup&delivery)、[7]USHOP的员工
            //3. 车队外协：暂无
            $store = (new SysStoreServer())->getStoreByid($organizationId);

            if ($type == 2) { //除了车队外协可以是总部员工申请，其余都是网点员工才能申请
                return false;
            }

            if (!isset($jobTitlesData[$job_title]) || (isset($store) && in_array($store['category'], [enums::$stores_category['shop_pickup_only'], enums::$stores_category['shop_ushop'], enums::$stores_category['shop_pickup_delivery'], enums::$stores_category['os'], enums::$stores_category['hub']]))) {
                return true;
            }
        }
        return false;
    }

    //返回ph list
    public function get_holidays($staff_info)
    {
        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $country_info = (new StaffRepository($this->lang))->getStaffCountryInfo([$staff_info['staff_info_id']]);
        $param = [
            'staff' => [
                [
                    'staff_info_id' => $staff_info['staff_info_id'] ,
                    'sex' => $staff_info['sex'] ?? 1,
                    'country_id' => $country_info[$staff_info['staff_info_id']] ?? 1,
                    'week_working_day' => $staff_info['week_working_day'] ?? 6,
                    'node_department_id' => $staff_info['node_department_id'] ?? 0,
                    'job_id' => $staff_info['job_id'] ?? 0,
                ]
            ]
        ];
        $data = $leave_server->ph_days($param);
        if(!empty($data))
            return $data[$staff_info['staff_info_id']];
        return array();


//        $data = ThailandHolidayModel::find([
//            'columns'    => 'day,holiday_type'
//        ])->toArray();
//        if(!empty($data))
//            $data = array_column($data,'day');
//
//        return $data;
    }

    public function type_book($locale = '')
    {
        $data = [
            [
                'code' => '1',
                'type' => 1,
                'msg'  => $this->getTranslation($locale)->_('2003')
            ],
            [
                'code' => '2',
                'type' => 1,
                'msg'  => $this->getTranslation($locale)->_('2004')
                ,'need_img' => 1
            ],
            [
                'code' => '3',
                'type' => 1,
                'msg'  => $this->getTranslation($locale)->_('2005')
            ],
            [
                'code' => '19',
                'type' => 1,
                'msg'  => $this->getTranslation($locale)->_('2022')
            ],
            [
                'code' => '4',
                'type' => 1,
                'msg'  => $this->getTranslation($locale)->_('2006')
                ,'template_type' => enums::LEAVE_TEMPLATE_TYPE_2
            ],
            [
                'code' => '24',
                'type' => 0,
                'msg'  => $this->getTranslation($locale)->_('leave_24')
            ],
            [
                'code' => '15',
                'type' => 1,
                'msg'  => $this->getTranslation($locale)->_('2017')
            ],
            [
                'code' => '16',
                'type' => 1,
                'msg'  => $this->getTranslation($locale)->_('leave_la_16')
            ],

        ];
        return $data;
    }

    /**
    实习生员工 定制类型 公司培训 和 无薪假
     * code 类型枚举
     * type 1 带薪 2 不带薪 3 不拼接
     * color 1 黄色 2 灰色
     * @return array
     */
    public function practise_type_book(){
        $data = [
            [
                'code' => '24',
                'type' => 0,
                'msg'  => $this->getTranslation()->_('leave_24')
            ],
            [
                'code' => '16',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('leave_la_16')
            ],

        ];
        return $data;
    }
    

    /**
     * 获取剩余假期天数
     * @param $param
     */
    public function get_left_holidays($param)
    {
        //by 显示额度的类型
        $showType = [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_2, enums::LEAVE_TYPE_19];

        //hcm 工具获取所有假期额度
        if (!empty($param['is_svc']) && $param['is_svc'] == 1) {
            $data = $this->rpcHolidaysNum($param);
            return $this->checkReturn(['data' => $data]);
        }

        $staff_id = intval($param['staff_id']);
        //所有假期类型
        $data = $this->type_book();
        //根据员工属性 获取对应权限类型
        $data = $this->staffLeaveType($param['user_info'], $data);


        //类型还没固化 要计算
        $countType    = '2';
        $year         = date('Y', time());
        $audit_model  = new AuditRepository($this->lang);
        $applied_days = $audit_model->get_used_leave_days($staff_id, $year, $countType);
        $sum_days     = array_column($applied_days, null, 'leave_type');
        //获取所有 假期限制 额度
        $limit_array = $audit_model->get_all_leave_days();

        $leave_server = new LeaveServer($this->lang, $this->timezone);
        foreach ($data as $k => &$da) {
            $type = intval($da['code']);
            //需要在请假页面显示天数的假期类型 1
            if (in_array($type, [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_19])) {//改版后调用
                $da              = array_merge($da, $leave_server->getVacationDays($staff_id, $type));
            }
            if ($type == enums::LEAVE_TYPE_2) {//带薪事假 要展示额度
                $da['day_limit'] = $limit_array[$type] ?? 0;//分母 应有总额度
                $da['day_sub']   = $da['day_limit'] - ($sum_days[$type]['num'] ?? 0);//分子 今年剩余额度

            }

            if (in_array($type, $showType)) {
                //改类型 前端用
                $da['day_limit'] = empty($da['day_limit']) ? '0' : (string)$da['day_limit'];
                $da['day_sub']   = empty($da['day_limit']) ? '0' : (string)$da['day_sub'];
            }
        }
        $data = $leave_server->explain_read_flag($param['user_info'], $data);
        return $this->checkReturn(['data' => $data]);
    }


    public function rpcHolidayBody($permissionData, $leave_server){
        //处理 有权限的类型额度 返回给hcm
        foreach ($permissionData as $k => &$da) {
            $type = intval($da['code']);
            //额度显示 需要排除的 类型
            if (in_array($type, [enums::LEAVE_TYPE_15, enums::LEAVE_TYPE_25, enums::LEAVE_TYPE_26])) {
                unset($permissionData[$type]);
                continue;
            }

            //需要在请假页面显示天数的假期类型 1 和类型2
            if (in_array($type, [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_19, enums::LEAVE_TYPE_4])) {//改版后调用
                $da              = array_merge($da, $leave_server->getVacationDays($this->staffId, $type, ['is_svc' => 1]));
                continue;
            }

            //一次性假期
            if (!empty($this->oneTypes) && in_array($type, $this->oneTypes)) {
                $da['day_limit'] = $da['day_sub'] = $this->limit_array[$type] ?? 0;//分母 应有总额度
                //one time 类型 看有没有 没有就是没申请过 是全额
                if (in_array($type, $this->one_time) && !empty($this->remainData[$type])) {
                    $da['day_sub'] = 0;
                }
                if (in_array($type, $this->one_send) && !empty($this->remainData[$type])) {
                    $da['day_sub'] = half_num($this->remainData[$type]['days']);
                }
                continue;
            }

            if (!in_array($type, $this->limit_types)) {
                $da['text'] = 'no_limit';//没有 unset掉 并且 额度表没类型 说明 是不限制额度
            }

            //需要计算的类型 还没改版
            $da['day_limit'] = $this->limit_array[$type] ?? 0;//分母 应有总额度
            $sum             = empty($this->sum_days[$type]['num']) ? 0 : $this->sum_days[$type]['num'];//已用的 前端没用 不限时了
            $da['day_sub']   = $da['day_limit'] - $sum;//分子 今年剩余额度

            if ($da['day_sub'] < 0) {
                $da['day_sub'] = 0;
            }
        }
        return $permissionData;
    }

    //根据员工工号属性 获取对应能申请的假期类型
    public function staffLeaveType($staff_info,$data = []){
        if(empty($data)){
            $data = $this->type_book();
        }
        $leave_server = new LeaveServer($this->lang, $this->timezone);
        //新增权限判断
        $permission = $leave_server->leavePermission($staff_info);
        if(!$permission){
            return [];
        }

        //实习生类型
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_INTERN){
            $data = $this->practise_type_book();
        }

        //外协类型
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_0){
            $data = $this->os_type_book();
        }
        $data = array_column($data, null, 'code');
        if (empty($staff_info['sex']) ||  $staff_info['sex'] == 1) {
            //男性过滤掉 产检，产假 和女性特殊假
            unset($data[4],$data[17]);
        } else {
            //女性过滤掉 陪产假，国家军训假，出家假
            unset($data[5],$data[6]);
        }
        //仅「6天班轮休」保留入口 邮件需求 全部去掉休息日
        unset($data[15]);

        // 国籍与工作所在地不一致的员工， 会有跨国探亲假 如果数据表没有 触发发放操作
        $nationServer = new InternationalServer($this->lang,$this->timezone);
        if(!$nationServer->applyPermission($staff_info)){
            unset($data[19]);
        }

        return $data;
    }


    /**
     * 请假添加 泰国请假没有对接可视化 已经迁移至国家目录
     * @Access  public
     * @Param   request
     * @Return  array
     *
     * ！！！ 涉及表 staff_audit 主表 staff_audit_leave_split 拆分表
     */
    public function leaveAdd($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $leaveType = $this->processingDefault($paramIn, 'leave_type', 2);
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType = $this->processingDefault($paramIn, 'leave_end_type');
        $auditReason = $this->processingDefault($paramIn, 'audit_reason');
        $imagePathArr = $this->processingDefault($paramIn, 'image_path');
        $auditReason = strip_tags(addcslashes(stripslashes($auditReason), "'"));

        $db = StaffAuditModel::beginTransaction($this);
        $serialNo = $this->getRandomId();

        //[2]用户校验
        $staff_model = new StaffRepository();
        $staffData = $staff_model->getStaffPosition($staffId);
        if (empty($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
        }
        if ($staffData['is_sub_staff'] == 1) {
            return $this->checkReturn(-3, $this->getTranslation()->_('sub_staff_disable'));
        }
        if ($this->checkStaffFormal($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('os_or_franchisee_staff_disable'));
        }
        $leave_day = $paramIn['leave_day'] = $this->conversionTime(array_merge(['type' => 2], $paramIn));

        //请假日期 就是休息日区间 无需申请 假期类型包括 年假,事假,婚嫁,出家假,个人培训假（不带薪）,病假,绝育手术假,公司培训假,家人去世假。
        if ($leave_day == 0) {
            return $this->checkReturn(-3, $this->getTranslation()->_('day off for the apply date'));
        }
        $leave_server = new LeaveServer();

        $holidays = $paramIn['holidays'] = $leave_server->staff_off_days($staffId, $leaveStartTime, $leaveEndTime);
        //[3]校验选择时间内是请假记录
        $checkData = $this->checkLeaveDataByDate($paramIn);

        $this->getDI()->get("logger")->write_log("leave params ".json_encode($paramIn,
                JSON_UNESCAPED_UNICODE).' check res:'.json_encode($checkData, JSON_UNESCAPED_UNICODE), 'info');

        if (isset($checkData['code']) && $checkData['code'] == 0) {
            return $this->checkReturn(-3, $checkData['msg']);
        }
        //新增验证班次时间二次确认提示 https://flashexpress.feishu.cn/docx/WqJcd6xFNoctnux4qPEcKTZcnkg
        if (empty($paramIn['is_bi']) && empty($paramIn['is_submit'])){
            $leave_server->checkShiftLeave($staffId,$paramIn);
        }

        //判断是否跨天 保存 拆分表 staff_audit_leave_split
        $insert_param['leave_type'] = $leaveType;
        $insert_param['start_time'] = $leaveStartTime;
        $insert_param['end_time']   = $leaveEndTime;
        $insert_param['start_type'] = $leaveStartType;
        $insert_param['end_type']   = $leaveEndType;
        $insert_param['holidays']   = $holidays;
        $insert_param['sub_day']    = $this->sub_day;

        $r = $leave_server->format_leave_insert($staffId, $insert_param);
        if ($r['code'] == 1) {
            $insert = $r['data'];
        } else {
            return $r;
        }

        $audit_model = new AuditRepository($this->lang);
        //如果是年假 需拆分 并且去年年假在有效期内
        {
            if ($leaveType == enums::LEAVE_TYPE_15 && !empty($paramIn['is_bi'])) {
                $check_param['operator'] = $paramIn['operator'] ?? 0;
                $check_param['staff_id'] = $staffId;
                $check_param['date'] = date('Y-m-d', strtotime($leaveStartTime));
                $this->leave_for_workday($check_param);
            }else if(in_array($leaveType,$this->one_time)){
                //增加 一次性额度扣减
                $remain_model = new StaffLeaveRemainDaysModel();
                $remain_row['staff_info_id'] = $staffId;
                $remain_row['leave_type'] = $leaveType;
                $remain_row['days'] = 0;
                $remain_row['leave_days'] = $leave_day;
                $flag = $remain_model->create($remain_row);
                $this->getDI()->get("logger")->write_log("leave_one_time {$staffId} {$flag}" . json_encode($remain_row),'info');
            }else if(in_array($leaveType,$this->one_send)){
                //一次性发放额度 先查询 有没有额度 如果没有额度 不让申请 需要在hris 那边初始化
                $remain_info = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                    'bind' => ['staff_info_id' => $staffId,'leave_type' => $leaveType],
                ]);

                //如果没有 说明没初始化成功 提示错误
                if(empty($remain_info))
                    return $this->checkReturn(-3, 'init failed');

                //操作额度
                $remain_info->days -= $leave_day;
                $remain_info->leave_days += $leave_day;
                $remain_info->updated_at = gmdate('Y-m-d H:i:s');
                $remain_info->update();
            }
        }

        $status = 1;
        if (!empty($paramIn['is_bi'])) {
            $status = 2;
            $auditReason .= "|system_tool_add";
        }
        $timeOut = date('Y-m-d 00:00:00',strtotime('+3 day'));
        //[4]请假插入
        try {
            $insetData = [
                'staff_info_id' => $staffId,
                'leave_type' => $leaveType,
                'leave_start_time' => $this->assemblyData($leaveStartTime, 1, $leaveStartType),
                'leave_start_type' => $leaveStartType,
                'leave_end_time' => $this->assemblyData($leaveEndTime, 2, $leaveEndType),
                'leave_end_type' => $leaveEndType,
                'audit_reason' => $auditReason,
                'status' => $status,
                'audit_type' => enums::$audit_type['LE'],
                'leave_day' => $leave_day,
                'serial_no' => (!empty($serialNo) ? 'LE' . $serialNo : NULL),
                'time_out'  => $timeOut,
            ];

            //不同国家 存在 子类型 产假等
            if (!empty($paramIn['sub_type'])) {
                $insetData['template_comment'] = json_encode(array("leave_{$leaveType}_key" => intval($paramIn['sub_type'])));
            }
            $db->insertAsDict("staff_audit", $insetData);
            $auditId = $db->lastInsertId();
            if (!$auditId) {
                throw new \Exception("插入staff_audit 失败" . json_encode($insetData, JSON_UNESCAPED_UNICODE));
            }
            if ($insert && is_array($insert)) {
                foreach ($insert as &$v) {
                    $v['audit_id'] = $auditId;
                }
                $result = $audit_model->batch_insert("staff_audit_leave_split", $insert);
                if (!$result) {
                    throw new \Exception("插入staff_audit_leave_split 失败" . json_encode($insert, JSON_UNESCAPED_UNICODE));
                }
            }
            if (empty($paramIn['is_bi'])) {

                $extend = $this->getWorkflowExtend($insetData,$staffData);
                $extend['time_out'] = $timeOut;
                $res = (new ApprovalServer($this->lang, $this->timezone))->create($auditId, enums::$audit_type['LE'], $staffId, null, $extend);
                if (!$res) {
                    throw new \Exception('audit staff insert leaveAdd workflow fail' . $res);
                }
            }
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("leaveaddworkflow error:" . $e->getMessage() . " " . $e->getTraceAsString());
            $db->rollBack();
            return $this->checkReturn(-3, $this->getTranslation()->_('1009'));
        }


        //插入图片
        if (!empty($imagePathArr)) {
            foreach ($imagePathArr as $k => $v) {
                $insertImgData = [
                    'audit_id' => $auditId,
                    'image_path' => $v
                ];
                $audit_model->auditImgInsert($insertImgData);
            }
        }

        $return['data'] = array('leave_day' => $paramIn['leave_day']);
        return $this->checkReturn($return);
    }

    /**
     *
     * 验证请假规则
     * @param array $paramIn
     * @return array
     */
    public function checkLeaveDataByDate($paramIn = [])
    {
        //[1]参数定义 leave_start_time 和 leave_end_time 是 ymd 类型
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $imagePathArr   = $this->processingDefault($paramIn, 'image_path');
        $leave_days     = $paramIn['leave_day'];

        $param = [
            'staff_id'         => $staffId,
            'leave_start_time' => date('Y-m-d',strtotime($leaveStartTime)),
            'leave_start_type' => $leaveStartType,
            'leave_end_time'   => date('Y-m-d',strtotime($leaveEndTime)),
            'leave_end_type' => $leaveEndType,
        ];


        //开始时间 结束时间 验证
        if ($leaveEndTime < $leaveStartTime) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
        }
        if ($leaveEndTime == $leaveStartTime) {
            if ($leaveStartType > $leaveEndType) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
            }
        }

        //图片最多限制5张
        if (!empty($imagePathArr) && count($imagePathArr) > 5) {
            return $this->checkReturn(-3, $this->getTranslation()->_('at most 5 photos'));
        }

        //图片必填的类型
        if (in_array($leaveType, $this->need_img) && empty($imagePathArr)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('22328_leave_application'));
        }

        //[3]查询请假记录
        $audit_model = new AuditRepository($this->lang);
        $levelData = $this->checkExistLeave($param);
        if (!empty($levelData)){
            return $this->checkReturn(-3, $this->getTranslation()->_('1012'));
        }


        //新增规则 二期需求 https://shimo.im/sheets/94eQG9bXB8oMZlnT/MODOC
        //试用期：从入职第1天开始算，第1-90天
        //不能申请的假期类型： 1年假，2带薪事假，5陪产假，9个人受训假，10婚假，11出家假 --  新版需求把 6 8 去掉了 后来又把7 去掉了
        //新增需求 试用期判断 不用固定120天 https://l8bx01gcjr.feishu.cn/docs/doccne9Sx3V9c0YesAU97rFTkBb hr_probation 状态为4的字段
        $forbidden = $this->forbidden;
        $leaveType = intval($leaveType);

        $leave_lang = $audit_model::$leave_type;
        //获取job title name 入职时间
        $staff_model = new StaffRepository($this->lang);
        $staff_info  = $staff_model->getStaffPosition($staffId);
        $hire_date   = $staff_info['hire_date'];
        $entry       = strtotime($staff_info['hire_date']);
        if (empty($staff_info) || empty($hire_date)) {
            return $this->checkReturn(-3, 'no permission to apply');
        }

        //类型验证
        $typeData = $this->staffLeaveType($staff_info);
        if (empty($typeData)) {
            throw new ValidationException('wrong leave type');
        }
        if (!in_array($leaveType, array_keys($typeData)) && $leaveType != enums::LEAVE_TYPE_15) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004').$this->getTranslation()->_($leave_lang[$leaveType]));
        }

        //新增 请假白名单 不限制入职天数 修改为 查看是否入职
        if (in_array($leaveType, $forbidden) && $hire_date >= '2020-06-13 00:00:00') {
            //试用期状态，1试用期，2已通过，3未通过，4已转正 适用于 xxx 之后 之前的数据默认都转正 因为没管旧数据
            if ($staff_info['status'] != 4) {
                $message = str_replace('leave_type', $this->getTranslation()->_($leave_lang[$leaveType]),
                    $this->getTranslation()->_('probation_limit'));
                return $this->checkReturn(-3, $message);
            }
            if (!empty($staff_info['formal_at']) && $leaveStartTime < $staff_info['formal_at']) {
                $message = $this->getTranslation()->_('probation_before_limit');
                return $this->checkReturn(-3, $message);
            }
        }

        //除了 3，15，16 类型 其他类型必须 大于当前时间  bi工具 不需要时间验证 新增可以候补家人去世假类型 http://193x782t53.imwork.net:29667/zentao/story-view-3638.html 新增无薪病假
        // $time_limit_start 表示 除了这些假期类型 其余的 必须大于当前时间
        if (!in_array($leaveType, $this->time_limit_start) && empty($paramIn['is_bi'])) {
            //请假日期必须大于等于当前日期
            if ($leaveStartTime < date('Y-m-d', time())) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1018'));
            }
        }


        // 需要先检验 次数 和总额度 是否非法 然后再拆分  一次限制的年假(4,5,6,8,10,11) 去掉 产检一次性限制
        if (in_array($leaveType, $this->one_time)) {
            //验证 员工入职以后 是否请过
            $remain_info = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                'bind'       => ['staff_info_id' => $staffId, 'leave_type' => $leaveType],
            ]);

            if (!empty($remain_info)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }

            //是否超出 限定 额度
            $limit_days = $audit_model->get_leave_days_by_type($leaveType);
            if (!empty($limit_days) && $leave_days > $limit_days) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }
        }
        //不限次数 的 总和 限制 假期 新增假期类型 16 员工培训假 不限次数 限制总和
        if (in_array($leaveType, $this->one_send)) {
            //验证 员工入职以后 是否请过
            $remain_info  = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                'bind'       => ['staff_info_id' => $staffId, 'leave_type' => $leaveType],
            ]);
            $leave_server = new LeaveServer($this->lang, $this->timezone);
            if (empty($remain_info)) {
                $remain_info = $leave_server->init_leave(['staff_info_id' => $staffId, 'leave_type' => $leaveType]);
            }

            if (empty($remain_info)) {
                return $this->checkReturn(-3, $this->getTranslation()->_("init type {$leaveType} failed "));
            }

            if ($remain_info->days <= 0 || ($remain_info->days - $leave_days) < 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }
        }


        //拼接 验证逻辑 参数 check_leave_type 额度验证
        $leave_info['leave_type'] = $leaveType;
        $leave_info['img']        = $imagePathArr;
        $leave_info['is_bi']      = empty($paramIn['is_bi']) ? '' : $paramIn['is_bi'];
        $leave_info['sub_type']   = empty($paramIn['sub_type']) ? 0 : $paramIn['sub_type'];

        //新增传参
        $leave_info['leave_start_type'] = $leaveStartType;
        $leave_info['leave_end_type']   = $leaveEndType;
        $leave_info['holidays']         = $paramIn['holidays'];
        //本次请假  持续天数 如果跨年 //如果跨年 并且 不是年假 要拆开 每年分别是几天 和 每年的 开始结束时间 因为部分假期类型是按年度区分 今年可以请n天明年仍然可以请n天 还可以一次性跨年请2n天
        //！！新需求  如果部分请假类型其中包含法定假日 等休息日  需要扣除掉 不占用 该请假类型的额度
        //新需求 如果是产假 请跨年 不占用2年额度 按创建时间 每年一次
        if (date('Y', strtotime($leaveStartTime)) != date('Y', strtotime($leaveEndTime))
            && !in_array($leaveType, [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_4, enums::LEAVE_TYPE_19])
            && !in_array($leaveType, $this->one_time)
            && !in_array($leaveType, $this->one_send)) {
            $leave_server = new LeaveServer($this->lang, $this->timezone);
            //新需求 拆分时候 如果当天是休息日 剔除掉
            $date_array = $leave_server->format_year_date($leaveType, $leaveStartTime, $leaveEndTime,
                $paramIn['holidays']);
            $flag       = false;
            foreach ($date_array as $y) {//array('2019' => array('2019-12-30','2019-10-31') )) 有几年就有几个 正常应该就两年 没人能请一整年假期 还干不干了
                $need_days = 0;
                sort($y);
                $leave_info['leave_start_time'] = $y[0];//该年开始 时间
                $leave_info['leave_end_time']   = end($y);//该年 结束时间

                //拆分计算 本次请假 需要天数
                foreach ($y as $date) {
                    if ($date == $leaveStartTime) {
                        $need_days += ($leaveStartType == 1) ? 1 : 0.5;
                    } else {
                        if ($date == $leaveEndTime) {//最后一天
                            $need_days += ($leaveEndType == 2) ? 1 : 0.5;
                        } else {//区间内 肯定是一整天
                            $need_days += 1;
                        }
                    }
                }
                //验证每年的额度 是否 够用
                $flag = $this->check_leave_type($leave_info, $staff_info, $need_days);
                if ($flag !== true) {
                    break;
                }
            }

            if ($flag !== true) {
                return $flag;
            }
        } else {
            $need_days                      = $paramIn['leave_day'];
            $leave_info['leave_start_time'] = $leaveStartTime;
            $leave_info['leave_end_time']   = $leaveEndTime;
            $flag                           = $this->check_leave_type($leave_info, $staff_info, $need_days);
            if ($flag !== true) {
                return $flag;
            }
        }
        return $this->checkReturn(1);
    }

    /**
     *请假逻辑
     * 年假 额度 按创建时间计算  按当前时间 获取 今年年假 计算额度
     * 其他假期 如果跨年 先取出 开始年 和额度
     * 然后结束年 和 额度 两次计算 是否合法请假
     * 计算剩余额度 需要 获取 已经请的假期记录 如果存在跨年 需要再次拆分 留下 当前判定年的天数
     *
     * @param $leave_info 截取后的 请假开始时间和结束时间
     * @param $staff_info
     * @param $need_days 截断后的 请假天数 比如 19年 1天 20年2天 总数是3天 need_days 两次传参 1， 2
     * @return bool
     */
    protected function check_leave_type($leave_info, $staff_info, $need_days)
    {
        $leaveType = $leave_info['leave_type'];
        $leaveStartTime = $leave_info['leave_start_time'];
        $leaveEndTime = $leave_info['leave_end_time'];
        $entry = strtotime($staff_info['hire_date']);
        $staffId = $staff_info['staff_info_id'];
        $audit_model = new AuditRepository($this->lang);
        $imagePathArr = $leave_info['img'];
        $paramIn['is_bi'] = $leave_info['is_bi'];
        //额度计算
        //新需求 如果请假天数 跨年 需做新规则处理 如果是年假 按当前 年 计算
        $year = date('Y', strtotime($leaveStartTime));
        $apply_month = date('m', strtotime($leaveStartTime));
        $apply_end_month = date('m', strtotime($leaveEndTime));
        $current_year = date('Y', time());
        //新算法 用拆分表
        if ($leaveType == 1 && in_array($apply_month, $this->month_arr))//20年 请 21年 4月之后的年假 不需要取当年的剩余额度
            $applied_days = $audit_model->get_used_leave_days($staffId, $current_year, $leaveType);
        else
            $applied_days = $audit_model->get_used_leave_days($staffId, $year, $leaveType);

        //除了年假  之外  每种类型的额度
        $limit_days = $audit_model->get_all_leave_days();

        $sum_days = $count_days = array();
        $sum_days[$leaveType] = $count_days[$leaveType] = 0;
        if (!empty($applied_days)) {
            $info = $applied_days[0];
            $sum_days[$leaveType] = $info['num'];
            $count_days[$leaveType] = empty($info['audit_ids']) ? 0 : count(explode(',', $info['audit_ids']));
        }

        $leave_server = new LeaveServer($this->lang, $this->timezone);
        if ($leaveType == 2) {//带薪事假 不能超过7天
            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            //最多不能超过5天
            if($need_days > 5){
                $str = str_replace('{x}','5',$this->getTranslation()->_('ph_notice'));
                return $this->checkReturn(-3,$str);
            }

        } else if ($leaveType == 3) {//[2]检测日期[结束时间必须大于开始时间] 病假可以选择近三天 bi工具排除

            if ($leaveStartTime < date('Y-m-d', time() - 48 * 3600) && empty($paramIn['is_bi'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1026'));
            }

            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType]) {
                $notice_msg = $this->getTranslation()->_('leave_limit');
                return $this->checkReturn(-3, $notice_msg);
            }

        } else if ($leaveType == 4) {//产假 一次 且不能超过98天 2021-04-07 修改为 按创建时间算 每年一次 每次90天

            $check_4 = $leave_server->check_create_year($staffId, $leaveType);
            if (!$check_4)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

            //需要分流  菲律宾 定制 额度根据子选项 选择天数会有不同 取子类型
            $sub_type = empty($leave_info['sub_type']) ? 0 : intval($leave_info['sub_type']);
            $flag = $leave_server->check_leave_4($need_days, $limit_days[$leaveType], $sum_days[$leaveType], $sub_type, $leave_info);
            if ($flag !== true)
                return $this->checkReturn(-3, $flag);

        } else if ($leaveType == 15) {//休息日 可以申请前天、昨天、今天、明天、后天。

            //新需求 按工作天数 区分 6天为单休
            if($staff_info['week_working_day'] != 6)
                return $this->checkReturn(-3, $this->getTranslation()->_('jobtransfer_0004'));

            $check_rest = $leave_server->get_rest_times($staff_info,$leave_info);
            if($check_rest['code'] != 1)
                return $this->checkReturn(-3, $check_rest['msg']);

            $times = $check_rest['data'];

            if ($times >= 1)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_for_five'));

            //新需求 休息日 只能是1天 大于小于都不行 https://shimo.im/docs/kv6rgP3GXDT69wdc/read
            if ($need_days < 1)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_for_half'));

            if ($need_days > 1)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

        } else if ($leaveType == 16) { //公司培训假 只能选择今天、明天和后天，或者过去8天的日期 且不能超过8天 改为过去两天
            $trainingServer             = new TrainingServer($this->lang, $this->timezone);
            $trainingServer->staffInfo  = $staff_info;
            $trainingServer->paramModel = $leave_info;
            if ($trainingServer->checkTrainingLeave()) {
                return $this->checkReturn(-3, $this->getTranslation()->_('training_leave_notice'));
            }
        } else if ($leaveType == 24) {//老挝新增 无薪休假

        } else if ($leaveType == enums::LEAVE_TYPE_12) {//外协用

        } else {//非法请求
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        return true;
    }

    /**
     * 换算时间
     * 新需求 需根据员工属性 扣除休息日 和ph  6天-找轮休  5天 找周六日
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function conversionTime($paramIn = [])
    {
        //[1]参数定义
        //leave type 1 上午 2 下午
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $type           = $this->processingDefault($paramIn, 'type', 2, 1);//类型 1外部调用 2内部调用
        $staff_id       = $paramIn['staff_id'];


        //[2]换算时间
        if ($leaveEndTime < $leaveStartTime || ($leaveEndTime == $leaveStartTime) && $leaveEndType < $leaveStartType) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
        }
        $leaveServer = new LeaveServer($this->lang, $this->timezone);
        $leave_day   = 0;
        $key         = $start_date = date('Y-m-d', strtotime($leaveStartTime));
        $end_date    = date('Y-m-d', strtotime($leaveEndTime));
        //获取该员工 请假区间的 休息日 和 ph
        $holidays = $leaveServer->staff_off_days($staff_id, $leaveStartTime, $leaveEndTime);
        while ($key <= $end_date) {
            if (in_array($key, $holidays) && in_array($leaveType, $this->sub_day)) {
                $key = date("Y-m-d", strtotime("+1 day", strtotime($key)));
                continue;
            }
            $add = 1;
            if ($key == $start_date && $leaveStartType != 1) {
                $add = 0.5;
            }
            if ($key == $end_date && $leaveEndType != 2) {
                $add = 0.5;
            }

            $leave_day += $add;
            $key       = date("Y-m-d", strtotime("+1 day", strtotime($key)));
        }

        if ($type == 1) {
            $returnData['data']['day'] = $leave_day;
            return $this->checkReturn($returnData);
        }
        return $leave_day;
    }



    //如果 审批通过 休息日类型的假期 判断该员工 是 工作6天的 需要 覆盖该日期所在周的轮休记录 如果该申请 审核通过以后 被撤销 需还原 之前的轮休日期
    protected function leave_for_workday($param){
        try{
            //获取员工信息
            $re = new StaffRepository($this->lang);
            $staff_info = $re->getStaffPosition($param['staff_id']);
            if (empty($staff_info) || $staff_info['week_working_day'] == 5) {
                return;
            }

            $operateId = $param['operator'] ?? $param['staff_id'];
            //获取轮休信息 get_work_days_between
            $start = weekStart($param['date']);
            $end = weekEnd($param['date']);
            $work_days = $re->get_work_days_between($param['staff_id'],$start,$end);
            if(!empty($work_days)){
                //获取 ph
                $leave_server = new LeaveServer($this->lang,$this->timezone);

                $country_info = (new StaffRepository($this->lang))->getStaffCountryInfo([$staff_info['staff_info_id']]);
                $ph_param = [
                    'staff' => [
                        [
                            'staff_info_id' => $staff_info['staff_info_id'] ,
                            'sex' => $staff_info['sex'] ?? 1,
                            'country_id' => $country_info[$staff_info['staff_info_id']] ?? 1,
                            'week_working_day' => $staff_info['week_working_day'] ?? 6,
                            'node_department_id' => $staff_info['node_department_id'] ?? 0,
                            'job_id' => $staff_info['job_id'] ?? 0,
                        ]
                    ]
                ];
                $holiday = $leave_server->ph_days($ph_param);
                $holiday = empty($holiday) ? array() : $holiday[$staff_info['staff_info_id']];

                //只能存在一个轮休日 如果有多个 排除ph
                $row = array();
                foreach ($work_days as $k => $d){
                    if(in_array($d['date_at'], $holiday) || !empty($d['src_week'])){
                        unset($work_days[$k]);
                        continue;
                    }
                    $row = $d;
                    break;
                }
            }

            //如果 轮休 日期 有人后来配置了 就跳过
            if(!empty($row)){
                if($row['date_at'] == $param['date']){
                    $this->getDI()->get('logger')->write_log('请休息日 是同一天'.json_encode($row),'info');
                    return ;
                }
                //保存 备份 轮休数据 并删除
                $re->del_workday($row['staff_info_id'],$row['date_at']);
                $this->getDI()->get('db')->insertAsDict('work_day_replace', $row);
                $this->getDI()->get('logger')->write_log('请休息日 备份轮休'.json_encode($row),'info');

                //轮休日志操作记录表 删除
                $logServer          = new WorkdayServer($this->lang, $this->timezone);
                $row['operate_id'] = $operateId;
                $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_CANCEL_OFF, $row);
            }

            //操作覆盖
            $insert['staff_info_id'] = $param['staff_id'];
            $insert['month'] = date('Y-m',strtotime($param['date']));
            $insert['date_at'] = $param['date'];
            $insert['operator'] = $param['staff_id'];
            $insert['remark'] = 'leave replace';
            $model = new HrStaffWorkDayModel();
            $model->create($insert);
            //轮休日志操作记录表 来新增
            $logServer = new WorkdayServer($this->lang, $this->timezone);
            $insert['operate_id'] = $operateId;
            $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_ADD_OFF, $insert);
            //log
            $this->getDI()->get('logger')->write_log('请休息日 覆盖轮休'.json_encode($insert),'info');
            return ;
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('请休息日 覆盖轮休 异常' . $e->getMessage());
            return;
        }

    }


    public function type_book_show_msg($paramIn)
    {
        return false;
    }


    /**
     * 获取耗材权限
     * @param array paramIn
     * @return boolean
     */
    public function getConsumablesPermission($paramIn = [])
    {
        // 开始判断申请权限
        $bool = false;
        $staffId  = $paramIn['staff_id'];
        $staff_re = new StaffRepository($this->lang);
        $info     = $staff_re->getStaffPositionv3($staffId);
        if ($info['organization_type'] == 2) {
            return true;
        }
        //权限限制
        $key_code = enums::MATERIAL_WMS_OPEN_RULE;
        $wms_open_rule =  (new StaffRepository())->getOaSettingEnvAuthority($key_code);

        if (!empty($wms_open_rule)) {
            $rule = json_decode($wms_open_rule, true);
            if (!empty($rule['job_ids']) && in_array($info['job_title'], explode(',', $rule['job_ids']))) {
                $bool = true;
            }
        }
        return $bool;
    }

    //所有类型 都放开 不要删除
    public function type_book_history()
    {
        $data = [
            [
                'code' => '1',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2003')
            ],
            [
                'code' => '2',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2004')
                ,'need_img' => 1
            ],
            [
                'code' => '3',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2005')
            ],
            [
                'code' => '19',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2022')
            ],
            [
                'code' => '4',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2006')
                ,'template_type' => enums::LEAVE_TEMPLATE_TYPE_2
            ],
            [
                'code' => '24',
                'type' => 0,
                'msg'  => $this->getTranslation()->_('leave_24')
            ],
            [
                'code' => '15',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2017')
            ],
            [
                'code' => '16',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('leave_la_16')
            ],
        ];
        return $data;
    }

    public function getWorkflowExtend($auditData, $staffData)
    {
        $extend['flow_code'] = 'season_1';
        return $extend;
    }

    /**
     * 获取薪资周期
     * @param $now
     * @return array
     */
    public function getPayCycle($now = null)
    {
        $timestamp = isset($now) ? strtotime($now):time();
        $start = date("Y-m-01",$timestamp);
        $end = date("Y-m-t",$timestamp);
        return [$start, $end];
    }

}
