<?php
namespace FlashExpress\bi\App\Modules\La\Server;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HeadquartersAddressModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\WmsServer as BaseWmsServer;

class WmsServer extends BaseWmsServer
{
    /**
     * 创建订单
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addOrderS($paramIn = [], $userinfo)
    {
        $time = date("YmdHis");
        $rand = rand(1000, 9999);
        $orderId = $time . $rand;

        //获取订单相关人信息
        $admin_group = UC('wmsRole')['admin_group'];
        $organization_id = (isset($paramIn['organization_id']) && !empty($paramIn['organization_id'])) ? $paramIn['organization_id'] : $userinfo['organization_id'];
        $uInfo = $this->wms->userStoreInfo($organization_id);
        $uInfoArr = array_merge(empty($uInfo) ? [] : $uInfo, $userinfo, [
            "store_id" => $organization_id
        ]);
        //格式化订单数据
        $headOffice=[];
        $pc_code = '';
        if($uInfoArr['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_2']) {
            //产品确定 除泰国其他都是一个总部
            $headOffice =  $this->wms->getHeadquartersAddress();
            /**
             * 14771【BY-TH/PH/MY/LA】申请物料、申请资产和SCM接口逻辑优化
             * 总部员工申请资产/耗材时，需要记录成本中心
             */
            $staffInfo = (new StaffServer())->getStaffById($userinfo['staff_id']);
            $pc_code = $this->wms->getPcCode($staffInfo);
        }
        $serialNo = $this->getRandomId();
        $returnData['data']['dataList'] = [];
        $orderData['order_id'] = $orderId;//订单号
        $orderData['organization_id'] = $organization_id;//网点编号
        $orderData['pc_code'] = $pc_code;//成本中心
        $orderData['reason_application'] = isset($paramIn['reason_application']) ? $paramIn['reason_application'] : "";//申请理由字数限制
        $orderData['consignee_phone'] = $uInfoArr['mobile'];//联系方式
        $orderData['consignee_address'] = $uInfoArr['organization_type'] == 2 ? $headOffice['address'] : $uInfoArr['detail_address'];;//详细地址
        $orderData['province_code'] = $uInfoArr['organization_type'] == 2 ? $headOffice['province_code'] : $uInfoArr['province_code'];//省
        $orderData['city_code'] = $uInfoArr['organization_type'] == 2 ? $headOffice['city_code']: $uInfoArr['city_code'];//市
        $orderData['district_code'] = $uInfoArr['organization_type'] == 2 ? $headOffice['district_code'] :$uInfoArr['district_code'];;//区
        $orderData['postal_code'] = $uInfoArr['organization_type'] == 2 ? $headOffice['postal_code']: $uInfoArr['postal_code'];//邮编
        $orderData['apply_user'] = $uInfoArr['id'];//申请人
        $orderData['shipping_user'] = $uInfoArr['name'];//收货人姓名
        $orderData['order_status'] = isset($paramIn['order_status']) ? $paramIn['order_status'] : 1;//订单状态
        $orderData['serial_no'] = (!empty($serialNo) ? 'MA' . $serialNo : NULL); //序列号
//        $orderData['wf_role'] = $wf_role;
        $orderData['wf_role'] = '';
        //格式化订单商品详情数据
        $paramIn['goods_order_detail'] = isset($paramIn['goods_order_detail']) ? $paramIn['goods_order_detail'] : "";
        $orderDetailData = $paramIn['goods_order_detail'];
        $barCodeArr = array_column($orderDetailData, "bar_code");
        //获取订单相关人货物
        $goodsInfo = $this->wms->goodsInfo($barCodeArr);
        $orderDetailDataFormat = array();
        $goodsArr = array();
        //$orderDetailDataFormat = array();
        if ($orderDetailData) {
            foreach ($goodsInfo as $k => $v) {
                $goodsArr[$v['bar_code']]['price'] = $v['price'];
                $goodsArr[$v['bar_code']]['specification'] = $v['specification'];
                //根据语言存储商品名称
                if ($this->lang == 'en') {
                    $goodsArr[$v['bar_code']]['goods_name'] = $v['goods_name_en'];
                    $goodsArr[$v['bar_code']]['nuit'] = $v['nuit_en'];
                } elseif ($this->lang == 'th') {
                    $goodsArr[$v['bar_code']]['goods_name'] = $v['goods_name_th'];
                    $goodsArr[$v['bar_code']]['nuit'] = $v['nuit_th'];
                } else {
                    $goodsArr[$v['bar_code']]['goods_name'] = $v['goods_name_zh'];
                    $goodsArr[$v['bar_code']]['nuit'] = $v['nuit_zh'];
                }
            }
        }
        if ($orderDetailData) {
            foreach ($orderDetailData as $k => $v) {
                $orderDetailDataFormat[$k]['sort'] = isset($v['sort']) ? intval($v['sort']) : 0;
                $orderDetailDataFormat[$k]['bar_code'] = isset($v['bar_code']) ? $v['bar_code']: 0;
                $orderDetailDataFormat[$k]['num'] = isset($v['num']) ? intval($v['num']): 0;
                $orderDetailDataFormat[$k]['branch_repertory'] = isset($v['branch_repertory']) ? intval($v['branch_repertory']) : 0;//网点库存
                $orderDetailDataFormat[$k]['recomment_num'] = isset($v['recommend_num']) ? intval($v['recommend_num']) : 0;//推荐数量
                $orderDetailDataFormat[$k]['approval_num'] = $orderDetailDataFormat[$k]['num'];//申请数->审批数
                if ($v['num'] <= 0) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('7101'));
                }
                $orderDetailDataFormat[$k]['order_id'] = $orderId;
                $orderDetailDataFormat[$k]['employ_user'] = isset($v['employ_user']) ? implode(",", $v['employ_user']) : "";
                //判断是否都是本网点员工
                $userSumArr = $this->wms->notAllOrganizationR($orderDetailDataFormat[$k]['employ_user'], $organization_id);
                if (sizeof($userSumArr) < sizeof($v['employ_user']) || empty($v['employ_user'])) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('7100'));
                }
                $orderDetailDataFormat[$k]['price'] = isset($goodsArr[$v['bar_code']]['price']) ? $goodsArr[$v['bar_code']]['price'] : 0;
                $orderDetailDataFormat[$k]['goods_name'] = isset($goodsArr[$v['bar_code']]['goods_name']) ? $goodsArr[$v['bar_code']]['goods_name'] : '';
                $orderDetailDataFormat[$k]['nuit'] = isset($goodsArr[$v['bar_code']]['nuit']) ? $goodsArr[$v['bar_code']]['nuit'] : '';
            }
        }

        $db = WorkflowModel::beginTransaction($this);
        try {
            $wmsOrderData = $this->wms->addOrderR($orderData, $orderDetailDataFormat);
            if (!$wmsOrderData) {
                $db->rollBack();
                $this->wLog('order_err4101_s', json_encode($paramIn) . json_encode($userinfo));
                return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
            }

            $orderData['id'] = $wmsOrderData;
            $returnData['data'] = $orderId;
            $requestType = 9;
            $staffId = $uInfoArr['id'];
            $organization_type = $uInfoArr['organization_type'];
            $store_id = $uInfoArr['organization_id'];
            //[4]同步数据到列表
            $info = $this->wms->getWmsOrderMongoR($orderData['id']);
            $server = new \FlashExpress\bi\App\Server\AuditListServer($this->lang, $this->timezone);
            $summary = $server->generateSummary($info['id'], $requestType);

            $insertAuditData[] = [
                'id_union' => 'id_' . $info['id'],
                'staff_id_union' => $staffId,
                'type_union' => $requestType,
                'status_union' => 101,
                'store_id' => $organization_type == 2 ? '' : $store_id,
                'data' => json_encode($info),
                'table' => 'wms_order',
                'created_at' => gmdate('Y-m-d H:i:s', time()),
                'origin_id' => $info['id'],
                'summary' => json_encode($summary, JSON_UNESCAPED_UNICODE),
                'approval_id' => 0,
            ];
            $staff_info = $this->staffRep->getStaffPositionv3($userinfo['staff_id']);
            $extend = ['flow_code'=>'v1','category'=>$staff_info['category']];
            //判断相应职位获取本身负责的大区
            $extend['region_area'] = $this->getJobCategory($userinfo['staff_id'],$userinfo['job_title']);

            if (!($this->other->insterUnion($insertAuditData) &&
                (new ApprovalServer($this->lang, $this->timezone))
                    ->create($wmsOrderData, enums::$audit_type['MA'], $userinfo['staff_id'],null,$extend)
            )) {
                throw new \Exception('addOrder_s 插入审批流失败 ' . json_encode($paramIn, JSON_UNESCAPED_UNICODE) . " " . json_encode($userinfo, JSON_UNESCAPED_UNICODE));
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }



//        foreach ($approvalIDArr as $finalApprover) {
//            $insertAuditData[] = [
//                'id_union' => 'id_' . $info['id'],
//                'staff_id_union' => $staffId,
//                'type_union' => $requestType,
//                'status_union' => 107,
//                'store_id' => $organization_type == 2 ? '' : $store_id,
//                'data' => json_encode($info),
//                'table' => 'wms_order',
//                'created_at' => gmdate('Y-m-d H:i:s', time()),
//                'origin_id' => $info['id'],
//                'summary' => json_encode($summary, JSON_UNESCAPED_UNICODE),
//                'approval_id' => $finalApprover,
//            ];
//        }

        //[5]追加审批日志
//        $staff_name = $this->pub->getStaffName($staffId);
//        $log['staff_id'] = $staffId;
//        $log['type'] = $requestType;
//        $log['original_type'] = 1;
//        $log['original_id'] = $info['id'];
//        $log['operator'] = $staffId;
//        $log['operator_name'] = $staff_name;
//        $log['to_status_type'] = 1;
//        $this->other->insertLog($log);
//
//        [6]追加审批流
//        $ApprovalArr['audit_id'] = $info['id'];
//        $ApprovalArr['type'] = $requestType;
//        $ApprovalArr['level'] = 1;
//        $ApprovalArr['status'] = 7;
//        $ApprovalArr['submitter_id'] = $staffId;
//        $ApprovalArr['staff_ids'] = $userinfo['staff_ids'] ?? 0;
//        $this->other->insertApproval($ApprovalArr);

        return $this->checkReturn($returnData);
    }



    // -------------  country  搬过来的
    public static $symbol = '₭';

    public function check_times($staff_id)
    {
        return array();
    }

    public function format_amount($amount)
    {
        return self::$symbol . $amount;
    }

    // ------------country  迁移 结束 ----------



}
