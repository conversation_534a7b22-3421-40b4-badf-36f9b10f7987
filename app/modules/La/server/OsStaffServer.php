<?php

namespace FlashExpress\bi\App\Modules\La\Server;

use FlashExpress\bi\App\Enums\AuditDetailOperationsEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditOptionRule;
use FlashExpress\bi\App\Server\OsStaffServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use Exception;


use FlashExpress\bi\App\library\DateHelper;

class OsStaffServer extends GlobalBaseServer
{
    /**
     * 添加外协员工申请
     * @param array $paramIn    传入参数
     * @return mixed
     * @throws Exception
     */
    public function addOsStaff($paramIn = [])
    {
        //[1]参数定义
        $staffId    = $this->processingDefault($paramIn,'staff_id');
        $jobId      = $this->processingDefault($paramIn,'job_id');
        $employDate = $this->processingDefault($paramIn,'employment_date');
        $employDays = $this->processingDefault($paramIn, 'employment_days');
        $shiftId    = $this->processingDefault($paramIn,'shift_id');
        $demendNum  = $this->processingDefault($paramIn,'demend_num');
        $reason     = $this->processingDefault($paramIn,'reason');
        $reason     = addcslashes(stripslashes($reason),"'");
        $submit_store_id = $this->processingDefault($paramIn, 'store_id');
        $reasonType = $this->processingDefault($paramIn,'reason_type', 2);
        $imagePathArr  = $this->processingDefault($paramIn,'image_path');
        $osType     = $this->processingDefault($paramIn,'os_type', 2);
        $store_id   = $this->processingDefault($paramIn,'organization_id');

        $staffInfo = $this->staff->checkoutStaff($staffId);

        //由于operation部门在ms中存的是子部门,
        //并且hr中job_title与部门的关联关系是job_title与一级部门的关联而不是子部门关联
        //该位置必须查询申请人在bi系统中的部门
        $staff_info = $this->staff->getStaffInfoById($staffId);
        if ($staffInfo) {
            //$department_id = $osType == enums::$os_staff_type['motorcade'] ? 32: $staffInfo['department_id'];
            $department_id = $staffInfo['department_id'];
        } else {
            $department_id = null;
        }

        if (empty($department_id)) {
            throw new ValidationException($this->getTranslation()->_('err_msg_do_not_have_department'));
        }

        //非车队外协需要验证
        //部门下是否存在该职位
        if ($osType != enums::$os_staff_type['motorcade']) {
            $relate = $this->os->getRelation($department_id, $jobId);
            if (empty($relate)) {
                throw new ValidationException($this->getTranslation()->_('err_msg_job_title_department_dismatch'));
            }
        }


        //获取转岗审批流Code
        $extend['flow_code'] = $this->getOsStaffWorkflowRole([
            'job_id'        => $jobId,
            'os_type'       => $osType,
            'staff_id'      => $staffId,
            'department_id' => $staffInfo['department_id'],
        ]);


        //如果是车队外协需调整申请的网点
        $store_id = $osType == enums::$os_staff_type['motorcade'] ? $submit_store_id: $store_id;

        $shift_info  = $this->getShiftDetail($shiftId);
        $shift_start = '';
        $shift_end = '';
        if(!empty($shift_info)) {
            $shift_start = $shift_info['start'];
            $shift_end = $shift_info['end'];
        }

        //[3]组织数据插入业务主数据
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $insertData = [
                'serial_no'         => 'OS' . $this->getRandomId(),
                'os_type'           => $osType,
                'staff_id'          => $staffId,
                'job_id'            => intval($jobId),
                'department_id'     => $department_id,
                'store_id'          => $store_id,
                'employment_date'   => $employDate,
                'employment_days'   => $employDays,
                'shift_id'          => $shiftId,
                'status'            => enums::$audit_status['panding'],
                'demend_num'        => $demendNum,
                'final_audit_num'   => $demendNum,
                'reason_type'       => $reasonType,
                'reason'            => $reason,
                'wf_role'           => 'os_new',
                'shift_begin_time'  => $shift_start,
                'shift_end_time'    => $shift_end
            ];
            $osStaffId = $this->os->insertOsStaff($insertData);
            if (empty($osStaffId)) {
                throw new Exception($this->getTranslation()->_('4008'));

            }
            //插入业务关联图片
            if(!empty($imagePathArr)) {
                $insertImgData  = [];

                foreach($imagePathArr as $image) {
                    $insertImgData[] = [
                        'id'         => $osStaffId,
                        'image_path' => $image
                    ];
                }
                $this->pub->batchInsertImgs($insertImgData, 'OUTSOURCING_STAFF');
            }

            //HRBP根据申请的网点查找
            $extend['store_id'] = $store_id;

            //这里是 from 表单的内容,用于查找审批人
            $extend['from_submit'] = [
                'sys_store_id' => $store_id,
                'audit_type'   => AuditListEnums::APPROVAL_TYPE_OS,
            ];

            //创建
            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($osStaffId, AuditListEnums::APPROVAL_TYPE_OS, $staffId,null, $extend);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
            $db->commit();
        } catch (ValidationException $vException){
            $db->rollback();
            throw $vException;
        }  catch (Exception $e){
            $db->rollback();
            throw $e;
        }

        return $this->checkReturn([]);
    }

    /**
     * 更新外协员工申请
     * @param array $paramIn
     * @return array|void
     * @throws Exception
     */
    public function updateOsStaff($paramIn = [])
    {
        //[1]获取参数
        $this->wLog('更新外协员工申请', $paramIn);
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $status  = $this->processingDefault($paramIn, 'status', 2);
        $reason  = $this->processingDefault($paramIn, 'reject_reason', 1);
        $osStaffId = $this->processingDefault($paramIn, 'audit_id', 2);
        $employmentDays = $this->processingDefault($paramIn, 'employment_days', 2);
        $approvalData = $this->processingDefault($paramIn, 'demend_num', 2);
        $reason   = addcslashes(stripslashes($reason),"'");

        //[2]校验
        //[2.1]校验审批ID是否存在
        $osStaffInfo = $this->getOsStaffDetail(['id'=>$osStaffId]);
        if (empty($osStaffInfo)) {
            throw new Exception($this->getTranslation()->_('4008'), enums::$ERROR_CODE['1000']);
        }

        //[2.2]校验是否已经是最终状态
        //如果是，则不能撤销
        if ($osStaffInfo['status'] != enums::$audit_status['panding'] && $status == enums::$audit_status['revoked']) {
            throw new Exception($this->getTranslation()->_('please try again'), enums::$ERROR_CODE['1000']);
        }

        //[2.3]校验审批人数
        if (isset($approvalData) && $approvalData) {
            if ($approvalData < 1 || $approvalData > 50) {
                throw new Exception("'approval_data' invalid input", enums::$ERROR_CODE['1000']);
            }
        }

        //[2.4]校验天数
        if (isset($employmentDays) && $employmentDays) {
            if (in_array($osStaffInfo['os_type'], [enums::$os_staff_type['motorcade'], enums::$os_staff_type['normal']])) { //车队外协 、短期外协都是1~7天
                if ($employmentDays < 1 || $employmentDays > 7) {
                    throw new Exception("'employment_days' invalid input", enums::$ERROR_CODE['1000']);
                }
            } else { //长期外协是90到365天
                if ($employmentDays < 30 || $employmentDays > 365) {
                    throw new Exception("'employment_days' invalid input", enums::$ERROR_CODE['1000']);
                }
            }
        }

        $paramData = [
            'id'                => $osStaffInfo['id'],
            'reject_reason'     => $reason,
            //'demend_num'        => $approvalData ?? 0,
            'employment_days'   => $employmentDays,
            'final_audit_num'   => $status == enums::$audit_status['approved'] ? ($approvalData ?? $osStaffInfo['demend_num']) : 0
        ];

        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            //更新数据
            $this->getDI()->get('db')->updateAsDict(
                'hr_staff_outsourcing',
                $paramData,
                "id = " . $osStaffId
            );

            //同意或者驳回等分开处理
            if ($status == enums::$audit_status['approved']) {
                //同意
                $server = new ApprovalServer($this->lang, $this->timezone);
                $approval_res = $server->approval($osStaffId, AuditListEnums::APPROVAL_TYPE_OS, $staffId);
                if (!empty($approval_res) && !empty($paramData['final_audit_num'])){
                    $insertParams = [
                        'audit_type' => AuditListEnums::APPROVAL_TYPE_OS,
                        'audit_value'=> $osStaffId,
                        'update_list' => [
                            'demend_num' => $paramData['final_audit_num'],
                            'employment_days' => empty($employmentDays) ? 0 : $employmentDays,
                        ],
                    ];
                    $mq = new RocketMQ('audit-list-update');
                    $mq->setType(RocketMQ::TAG_AUDIT_LIST_SUMMARY_UPDATE);
                    $rid = $mq->sendMsgByTag($insertParams,5);
                    $this->logger->write_log('updateOsStaff audit-list-update 2 rid:' . $rid . 'data:' . json_encode($insertParams),
                        $rid ? 'info' : 'error');
                }
            } else if ($status == enums::$audit_status['dismissed']) {
                //驳回
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->reject($osStaffId, AuditListEnums::APPROVAL_TYPE_OS, $reason, $staffId);
            } else {
                //撤销
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->cancel($osStaffId, AuditListEnums::APPROVAL_TYPE_OS, $reason, $staffId);
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log("updateOsStaff failure:" . $e->getMessage() . $e->getTraceAsString(), "notice");
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        return $this->checkReturn([]);
    }

    /**
     * 获取审批流角色
     * 详细见 doc/OS_workflow.md
     * @param $paramIn
     * @return string
     */
    public function getOsStaffWorkflowRole($paramIn)
    {
        //[1]获取参数
        $staffId    = $this->processingDefault($paramIn,'staff_id');
        $jobId      = $this->processingDefault($paramIn,'job_id');
        $osType     = $this->processingDefault($paramIn,'os_type', 2);
        $departmentId = $this->processingDefault($paramIn,'department_id', 2);

        //[2]获取员工详情
        $staffInfo = $this->staff->getStaffPositionv3($staffId);

        //[3]找到合适的审批流
        if ($osType == enums::$os_staff_type['normal']) { //短期外协
            if($jobId == enums::$job_title['hub_staff']) { //HUB STAFF职位
                //申请人 -> 上级 -> 118822
                $flowCode = 1;
            } else if (in_array($jobId, [enums::$job_title['bike_courier'], enums::$job_title['van_courier'], enums::$job_title['warehouse_staff_sorter']])) {
                //bike courier、van courier、warehouse staff(sorter)职位
                if ($departmentId == 10007 && $jobId == enums::$job_title['warehouse_staff_sorter']) {
                    //申请人→网点负责人→118822
                    $flowCode = 6;
                } else {
                    //申请人 -> 上级 -> 申请人所在网点对应的大区经理
                    $flowCode = 2;
                }
            } else {
                $flowCode = 2;
            }
        } else { //长期外协
            //申请人-> 78534
            $flowCode = 7;
        }

        //onsite_officer、shop_officer的长期、短期外协申请审批流是一样的
        if ($jobId == enums::$job_title['onsite_officer'] && $staffInfo['category'] == enums::$stores_category['os']) {
            //申请人 -> 申请人所在网点负责人 -> 21715
            $flowCode = 4;
        } else if ($jobId == enums::$job_title['shop_officer'] && in_array($staffInfo['category'], [enums::$stores_category['shop_pickup_only'],enums::$stores_category['shop_ushop']])) {
            //申请人 -> 网点负责人 -> 20467 (Shop Operation Manager) ->17574
            $flowCode = 3;
        }

        return $flowCode;
    }

    /**
     * 获取请求列表
     */
    public function getRequestList($paramIn = [])
    {
        //[1]参数定义
        $staffInfo  = $this->processingDefault($paramIn, 'userinfo');
        $jobId = $this->processingDefault($paramIn, 'job_id', 2);
        $storeId    = $staffInfo['organization_id'] ?? '';
        $storeName  = $staffInfo['organization_name'] ?? '';
        $type       = $staffInfo['organization_type'] ?? '';
        $jobTitle   = $staffInfo['job_title'] ?? '';
        $positions  = $staffInfo['positions'] ?? [];

        //[2]获取网点详情
        $storeInfo = (new SysStoreServer())->getStoreByid($storeId);

        //hub可以申请长期外协的角色
        $hubRequestRole = UC('outsourcingStaff')['osLongPeriodRequestRole'];

        if (in_array($storeInfo['category'], [enums::$stores_category['hub'], enums::$stores_category['os'], enums::$stores_category['shop_pickup_only'], enums::$stores_category['shop_pickup_delivery'], enums::$stores_category['shop_ushop']])
        ) {
            $ret['os_mode'] = enums::$os_staff_type['long_term'];
            $ret['os_type'] = [
                ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
                ["code" => 2, "title" => $this->getTranslation()->_('os_request_mode_2')],
            ];
        } else { //短期外协
            $ret['os_mode'] = enums::$os_staff_type['normal'];
            $ret['os_type'] = [
                ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
            ];
        }
        $ret['store_list'] = [];
        //只有泰国有外协公司，统一数据结构。
        $is_out_company   = false;
        $out_company_list = [];

        $department = (new DepartmentRepository())->getDepartmentNameById($staffInfo['department_id'] ?? '');
        $ret['store_name']  = $ret['os_mode'] == enums::$os_staff_type['motorcade'] ? '': $storeName;
        $ret['department']  = $department;
        $ret['shift_info']  = $this->getShiftList($jobId);
        $ret['reason']      = $this->getOsStaffReqReason($storeId);
        $ret['is_out_company']   = $is_out_company;
        $ret['out_company_list'] = $out_company_list;
        return $ret;
    }


    /**
     * 获取职位列表
     * @param array $paramIn
     * @return array
     */
    public function getOsJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $osType     = $this->processingDefault($paramIn, 'type');
        $staffInfo  = $this->processingDefault($paramIn, 'userinfo');

        //[2]组织列表
        //1)短期外协职位下拉列表[全部职位]
        //2)长期外协职位下拉列表[Hub Staff|Onsite Officer|Shop Officer]
        switch ($osType) {
            case enums::$os_staff_type['normal']: //短期外协
                $returnArr = $this->getShortTermJobTitleList(['staff_id' => $staffInfo['id']]);
                break;
            case enums::$os_staff_type['long_term']: //长期外协
                $returnArr = $this->getLongTermJobTitleList(['staff_id' => $staffInfo['id']]);
                break;
            default:
                $returnArr = [];
                break;
        }
        return $returnArr;
    }

    /**
     * 获取外协员工职位列表
     * @param array $paramIn
     * @return array
     */
    public function getShortTermJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_id');

        //[2]获取申请人所在部门
        $staffInfo = $this->staff->getStaffInfoById($staffId);

        $department = SettingEnvModel::find([
            'conditions' => 'code in ({code:array})',
            'bind' => [
                'code' => ['dept_network_operations_id', 'dept_hub_management_id', 'dept_fulfillment_id']
            ]
        ])->toArray();
        $department = array_column($department, 'set_val', 'code');
        switch ($staffInfo['node_department_id']) {
            case $department['dept_network_operations_id']: //- Flash Laos Network Operations[203]及下级部门
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['van_courier'],
                        'job_title'     => 'Van Courier',
                    ],
                    [
                        'job_id'        => enums::$job_title['bike_courier'],
                        'job_title'     => 'Bike Courier',
                    ],
                    [
                        'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                        'job_title'     => 'Warehouse Staff (Sorter)',
                    ],
                ];
                break;
            case $department['dept_fulfillment_id']: //Laos Fulfillment[10007]及下级部门
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                        'job_title'     => 'Warehouse Staff (Sorter)',
                    ]
                ];
                break;
            case $department['dept_hub_management_id']: //Flash Laos Hub[207]及下级部门
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['hub_staff'],
                        'job_title'     => 'Hub Staff',
                    ]
                ];
                break;
            default:
                $returnArr = [];
                break;
        }
        return $returnArr;
    }

    /**
     * 获取有效的外协员工列表
     * @param array $paramIn
     * @return array
     */
    public function getLongTermJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_id');

        //[2]获取申请人所在部门
        $staff_info = $this->staff->getStaffInfoById($staffId);

        //申请列表
        //1)Shop Pickup Only & Shop Ushop类型网点可以申请Shop Officer
        //2)OS类型网点可以申请Onsite Officer
        //3)HUB类型可以申请Hub Staff
        if (in_array($staff_info['category'], [enums::$stores_category['shop_pickup_only'], enums::$stores_category['shop_ushop'], enums::$stores_category['shop_pickup_delivery']])) {
            $returnArr     = [
                [
                    'job_id'        => enums::$job_title['shop_officer'],
                    'job_title'     => 'Shop Officer',
                ]
            ];

        } else if (in_array($staff_info['category'], [enums::$stores_category['os']])) {
            $returnArr     = [
                [
                    'job_id'        => enums::$job_title['onsite_officer'],
                    'job_title'     => 'Onsite Officer',
                ]
            ];
        } else {
            $returnArr     = [
                [
                    'job_id'        => enums::$job_title['hub_staff'],
                    'job_title'     => 'Hub Staff',
                ]
            ];
        }
        return $returnArr;
    }

    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result = $this->getOsStaffDetail(['id' => $auditId]);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }
        $department = (new DepartmentRepository())->getDepartmentNameById($staff_info['node_department_id'] ?? '');
        $demendNum = !empty($result['final_audit_num']) ? $result['final_audit_num']: ($result['demend_num'] ?? '');

        //组织详情数据
        $detailLists = [
            'apply_parson'       => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? ''),
            'apply_department'   => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
            'os_mode'            => $this->getTranslation()->_('os_request_mode_' . $result['os_type']),
            'os_staff_job_title' => $result['job_title'] ?? '',
            'department'         => $department ?? '',
            'store'              => $result['store_name'] ?? '',
            'employment_date'    => $result['employment_date'] ?? '',
            'employment_days'    => $result['employment_days'] ?? '',
            'work_shift'         => $result['work_shift'] ?? '',
            'demend_num'         => $demendNum,
            'reason_app'         => $result['reason'] ?? '',
            'remark'             => $result['remark'] ?? '',
            'live_photo'         => $result['image_path'] ?? '',
        ];

        //驳回状态，需要显示驳回原因
        if ($result['status'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        $data = [
            'title'      => $this->auditlist->getAudityType(enums::$audit_type['OS']),
            'id'         => $result['id'],
            'staff_id'   => $result['staff_id'],
            'type'       => enums::$audit_type['OS'],
            'created_at' => $result['created_at'],
            'updated_at' => $result['updated_at'],
            'status'     => $result['status'],
            'is_stint'     => isCountry('TH') && $result['os_type'] ==  1 && in_array($result['job_id'],
                [
                    enums::$job_title['van_courier'],
                    enums::$job_title['bike_courier'],
                    enums::$job_title['boat_courier']
                ]) ? 1 : 0,
            'status_text'=> $this->auditlist->getAuditStatus('10' . $result['status']),
            'serial_no'  => $result['serial_no'] ?? '',
            'demend_num' => $demendNum,
            'final_audit_num'   => $result['final_audit_num'],
            'employment_days'   => $result['employment_days'],
        ];

        //获取待审批的审批人
        $approvalList = AuditApprovalModel::find([
            'columns' => 'approval_id',
            'conditions' => "biz_value = :value: and biz_type = :type: and state = 1 and deleted = 0",
            'bind' => [
                'type'  => enums::$audit_type['OS'],
                'value' => $auditId,
            ],
        ])->toArray();
        $approvalListArr = array_column($approvalList, 'approval_id');

        //待审批的审批人需要显示参考表
        if (in_array($user, $approvalListArr) && $result['status'] == 1) {
            $returnData['data']['os_extend'] = $result['extend'] ?? [];
        }

        if ($result['status'] == 2) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('final_num'), 'value' => $result['final_audit_num']],
            ];
        } else if ($result['status'] == 3) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('reject_reason'), 'value' => $result['reject_reason']],
            ];
        }

        $returnData['data']['head']   = $data;
        return $returnData;
    }

    /**
     * @description 自定义审批人审批按钮
     * @param $auditId
     * @return array
     */
    public function customiseOptions($auditId): array
    {
        return array_merge(AuditDetailOperationsEnums::BUTTON_COMMON_APPROVAL, [6,18]);
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(true,
            false,
            false,
            false,
            false,
            false);
    }

    // -------- country 迁移过来的------


    //获取审批详情的 tbl 列表
    public function get_apply_form($returnData, $timezone)
    {
//        $this->os             = new OsStaffRepository($this->timeZone);
        $data                 = [];
        $returnData['extend'] = [];
        $start_time           = strtotime(date("Y-m-d", strtotime("-4 day")));
        $end_time             = strtotime(date("Y-m-d", strtotime("-1 day")));
        $ret                  = $this->os->getOsStaffReferInfo($returnData['store_id'], $start_time, $end_time);
        if ($ret) {
            //在职van bike人数
            $van_cnt  = $this->os->getCourierCnt($returnData['store_id'], 110);
            $bike_cnt = $this->os->getCourierCnt($returnData['store_id'], 13);
            //在职仓管人数
            $soles_cnt = $this->os->getCourierSoles($returnData['store_id'], 2);
        }
        if ((isset($ret['avg']) && $ret['avg']) || (isset($ret['cnt']) && $ret['cnt']) || (isset($ret['undertake']) && $ret['undertake'])) {

            $dates     = DateHelper::DateRange($start_time, $end_time);
            $van_cnt   = $van_cnt ?? 0;
            $bike_cnt  = $bike_cnt ?? 0;
            $soles_cnt = $soles_cnt ?? 0;
            foreach ($dates as $date) {
                $date = date("m-d", strtotime($date));
                //揽件量
                $delivery_undertake = $ret['undertake'][$date] ?? 0;
                //应派件数
                $delivery_cnt = intval($ret['cnt'][$date] ?? 0);
                //人均妥投
                $parcel_avg = $ret['avg'][$date] ?? 0;
                //人均效能
                $delivery_avg = $soles_cnt == 0 ? 0 : round(($delivery_cnt + $delivery_undertake) / $soles_cnt, 2);
                $data[]       = [
                    'stat_date'          => ['num'    => $date,               //日期
                        'is_red' => 1
                    ],
                    'delivery_undertake' => ['num'    => $delivery_undertake, //揽件量
                        'is_red' => 1
                    ],
                    'delivery_cnt'       => ['num'    => $delivery_cnt,       //应派件数
                        'is_red' => 1
                    ],
                    'parcel_per'         => ['num'    => $parcel_avg,         //人均妥投
                        'is_red' => 1
                    ],
                    'delivery_avg'       => ['num'    => $delivery_avg,       //人均效能[仓管人效]
                        'is_red' => 1
                    ],
                ];
            }
        }
        $retData              = $this->generateTable($this->getTranslation()->_('last_4_days_parcel'), $data);
        $retData['van_cnt']   = $van_cnt ?? 0;
        $retData['bike_cnt']  = $bike_cnt ?? 0;
        $retData['soles_cnt'] = $soles_cnt ?? 0;
        array_push($returnData['extend'], $retData);
        return $returnData['extend'];
    }


    /**
     * 组织表数据
     * @param string $title     表头
     * @param array  $tableData 表数据
     * @return array
     */
    public function generateTable($title, $tableData)
    {
        $returnData = [];
        if ($tableData) {
            $detail = [];
            foreach ($tableData as $k => $v) {
                $detail[] = [
                    $v['stat_date'],
                    $v['delivery_undertake'] ?? '',
                    $v['delivery_cnt'] ?? '',
                    $v['parcel_per'] ?? '',
                    $v['delivery_avg'] ?? '',
                ];
            }

            $returnData = [
                'title'     => $title,
                'title_two' => '',
                'th'        => [
                    $this->getTranslation()->_('date'),
                    $this->getTranslation()->_('undertake_count'),
                    $this->getTranslation()->_('parcel_count'),
                    $this->getTranslation()->_('parcel_per'),
                    $this->getTranslation()->_('parcel_avg'),
                ],
                'td'        => $detail
            ];
        }
        return $returnData;
    }


    public function get_this_month_os_happening($jobId, $store_id, $submit_store_id, $osType, $timezone, $roles = [])
    {
        $returnData = [
            'is_remind' => 1,  //1 不需要提醒 2 需要提醒
            'info'      => [
                'store_name'         => '',
                'os_num'             => ['num' => "0", 'is_remind' => 1],//累计外协人次
                'inefficient_os_num' => ['num' => "0", 'is_remind' => 1],//累计低效外协
                'inefficient_os_pe'  => ['num' => "0%", 'is_remind' => 1],//低效外协占比
            ]
        ];
        return $returnData;
    }// ------------------ country  迁移 结束-----------

    public function get_os_risk_prompt($jobId, $store_id, $submit_store_id, $osType, $timezone)
    {

        $returnData = [
            'is_remind' => 1,//1 不需要提醒 2 需要提醒
            'info_list' => [//返回 ['date_val'='6/09','compliance_line'=>100,'formal_human_effect'=>10];  //达标线   正式员工派件人效
            ],
            'x'         => 0,//没有达标的天数
        ];
        return $returnData;

    }









}