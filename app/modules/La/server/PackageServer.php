<?php
namespace FlashExpress\bi\App\Modules\La\Server;
use app\enums\LangEnums;
use FlashExpress\bi\App\Enums\PackageEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Models\oa\MaterialPackageCategoryModel;
use FlashExpress\bi\App\Models\oa\MaterialPackageOrderModel;
use FlashExpress\bi\App\Models\oa\MaterialPackageOrderSkuModel;
use FlashExpress\bi\App\Models\oa\MaterialPackageSkuModel;
use FlashExpress\bi\App\Models\oa\MaterialWmsPlantaskInfoModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\BaseServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\CsrfTokenServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use WebGeeker\Validation\Validation;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;

/**
 * 包材领用服务层
 * Class PackageServer
 * @package FlashExpress\bi\App\Modules\La\Server
 */

class PackageServer extends GlobalBaseServer
{
    public $timezone;

    /**
     * PackageServer constructor.
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone='+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * 订单号规则验证
     * @return array
     */
    public function getValidateOrderCode() {
        return [
            'order_code' => 'Required|StrLen:16|>>>:order code'. $this->getTranslation()->_('miss_args'),//订单编号
        ];
    }

    /**
     * 过滤非必需的参数，例如数字类型的给传递空导致IntIn验证过不去等
     * @param array $params
     * @param array $not_must
     * @return mixed
     */
    public function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

    /**
     * 老挝展示泰文
     * 当用户的系统语言非包材后台表记录的语言时，按照默认语言展示
     * goods_name_en、goods_name_th、goods_name_zh
     * @return false|string
     */
    public function getGoodsNameLang()
    {
        if (!in_array($this->lang, [LangEnums::LANG_CODE_EN, LangEnums::LANG_CODE_ZH_CN])) {
            $name_lang = LangEnums::LANG_CODE_TH;
        } else {
            $name_lang = substr(strtolower($this->lang), 0, 2);
            $name_lang = empty($name_lang) ? 'zh' : $name_lang;
        }
        //没有语言 默认英文
        $name_lang = MaterialPackageCategoryModel::getGoodsNameTranslate($name_lang);
        return $name_lang;
    }

    /**
     * 查询当前员工是否有包材申请记录
     * @param int $staff_id 当前登录者员工工号
     * @return bool
     */
    public function getOrderStatus()
    {
        $data = [];
        $order_status = PackageEnums::$package_order_status;
        foreach ($order_status as $key=>$status) {
            $data['apply'][] = [
                'id'=>$key,
                'label'=>$this->getTranslation()->_($status)
            ];
        }
        $approve_order_status = PackageEnums::$approve_package_order_status;
        foreach ($approve_order_status as $key=>$status) {
            $data['approve'][] = [
                'id'=>$key,
                'label'=>$this->getTranslation()->_($status)
            ];
        }
        return $data;
    }

    /**
     * 获取申请人申请列表或管理员审批列表
     * @param array $condition['staff_id'=>'申请人工号','status'=>'申请单状态','page_size'=>'每页条数','page_num'=>'当前页码']
     * @return array
     */
    public function getOrderList($condition)
    {
        $page_size = $condition['page_size'];
        $page_num  = $condition['page_num'];
        $condition['type'] = $condition['type'] ?? PackageEnums::TYPE_APPLY;
        $data = [
            'items'      => [],
            'pagination' => [
                'page_num' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        $items = [];
        //获得数量
        $count = $this->getApplyCount($condition);
        if ($count > 0) {
            $order_by = $condition['type'] == PackageEnums::TYPE_APPLY ? 'main.submit_at desc' : 'main.updated_at';
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'main.order_code',
                'main.staff_id',
                'main.staff_name',
                'main.status',
                'main.submit_at',
                'main.use',
                'main.use_remark'
            ]);
            $builder->from(['main' => MaterialPackageOrderModel::class]);
            $builder = $this->getCondition($builder, $condition);
            $builder->orderBy($order_by);
            $offset  = $page_size * ($page_num - 1);
            $builder->limit($page_size, $offset);
            $items_obj = $builder->getQuery()->execute();
            $items = $items_obj ? $items_obj->toArray() : [];
            $items = $this->formatApplyList($items);
        }
        $data['items']                     = $items ?? [];
        $data['pagination']['total_count'] = $count;
        return $data;
    }

    /**
     * 格式化申请单列表
     * @param array $list 申请单列表
     * @return array
     */
    private function formatApplyList($list)
    {
        if (!$list) {
            return [];
        }
        $t = $this->getTranslation();
        foreach ($list as &$values) {
            $values['status_text'] = $t->_(PackageEnums::$package_order_status[$values['status']]);
            $values['use_text'] = $t->_(PackageEnums::$package_order_use[$values['use']]);
        }
        return $list;
    }

    /**
     * 查询当前员工是否有包材申请记录
     * @param array $condition['staff_id'=>'申请人工号']
     * @return int
     */
    public function getApplyCount($condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) as total');
        $builder->from(['main' => MaterialPackageOrderModel::class]);
        $builder = $this->getCondition($builder, $condition);
        $total_info = $builder->getQuery()->getSingleResult();
        return intval($total_info->total);
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $condition 查询条件组
     * @return mixed
     */
    private function getCondition($builder, $condition)
    {
        $builder->where('main.is_deleted = :is_deleted:', ['is_deleted'=>PackageEnums::IS_DELETED_NO]);
        if (isset($condition['staff_id'])) {
            if ($condition['type'] == PackageEnums::TYPE_APPLY) {
                //申请人工号筛选
                $builder->andWhere('main.staff_id = :staff_id:', ['staff_id'=>$condition['staff_id']]);
            } else if ($condition['type'] == PackageEnums::TYPE_ADMIN){
                //审批人工号筛选
                $builder->andWhere('main.admin_staff_id = :admin_staff_id:', ['admin_staff_id'=>$condition['staff_id']]);
            }
        }
        if (isset($condition['status'])) {
            //申请单状态筛选
            $builder->andWhere('main.status=:status:', ['status'=>$condition['status']]);
        } else {
            if ($condition['type'] == PackageEnums::TYPE_ADMIN) {
                //审批人只可查看确认、驳回的记录
                $builder->andWhere('main.status in ({values:array})', ['values'=>[PackageEnums::PACKAGE_ORDER_STATUS_CONFIRMED, PackageEnums::PACKAGE_ORDER_STATUS_REJECTED]]);
            }
        }
        return $builder;
    }

    /**
     * 判断是否拥有我的-耗材管理权限
     * @param array $user_info 当前登陆者信息
     * @return bool
     */
    public function checkPermission($user_info)
    {
        $has_permission = false;
        //员工职位ID
        $job_title = $user_info['job_title'] ?? 0;
        //员工部门ID
        $department_id = $user_info['department_id'] ?? 0;
        if (!$department_id && !$job_title) {
            //员工部门、职位信息都不全直接默认没有权限
            return $has_permission;
        }
        //获取拥有权限配置
        $setting_env = new SettingEnvServer();
        $set_val = $setting_env->getSetVal('package_confirm_permission');
        if ($set_val) {
            $permission = json_decode($set_val, true);
            if ($user_info['sys_store_id'] == '-1') {
                if (in_array($user_info['job_title_grade_v2'], $permission['job_title_grade_v2'])) {
                    $has_permission = true;
                }
            } else {
                if ($job_title && isset($permission['job_title_ids']) && in_array($job_title, $permission['job_title_ids'])) {
                    //用于存在职位&&设置了职位ID配置&&员工职位在设置范围内
                    $has_permission = true;
                } else if ($department_id && isset($permission['department_ids']) && is_array($permission['department_ids']) && $permission['department_ids']) {
                    //用于存在部门&&设置了部门ID配置&&必须为数组&&值不为空
                    $model = new FleSysDepartmentModel();
                    foreach ($permission['department_ids'] as $id) {
                        $department_id_arr = $model->getSpecifiedDeptAndSubDept($id);
                        if ($department_id_arr && in_array($department_id, $department_id_arr)) {
                            //找到满足条件的就返回即可
                            $has_permission = true;
                            break;
                        }
                    }
                }

            }
        }
        return $has_permission;
    }

    /**
     * 获取包材分类
     * @return mixed
     */
    public function getCategoryList()
    {
        $category =  MaterialPackageCategoryModel::find([
            'columns'=>['id','goods_name_'.$this->getGoodsNameLang().' as goods_name'],
            'conditions' => "is_deleted = :is_deleted: and status = :status:",
            'bind' => [
                'is_deleted'  => PackageEnums::IS_DELETED_NO,
                'status' => PackageEnums::STATUS_ENABLE,
            ]
        ])->toArray();

        array_unshift($category, ['id'=>'0','goods_name'=>$this->getTranslation()->_('package_category_all')]);
        return $category;
    }

    /**
     * 获取包材清单列表
     * @return mixed
     */
    public function getSkuList()
    {
        return MaterialPackageSkuModel::find([
            'columns'=>['id','category','goods_name_'.$this->getGoodsNameLang().' as goods_name', 'specs_model', 'unit', 'image_path'],
            'conditions' => "is_deleted = :is_deleted: and status = :status:",
            'bind' => [
                'is_deleted'  => PackageEnums::IS_DELETED_NO,
                'status' => PackageEnums::STATUS_ENABLE,
            ]
        ])->toArray();
    }

    /**
     * 获取启用的包材清单列表
     * @return array
     */
    public function getPackageCategoryAndSkuList()
    {
       $category = $this->getCategoryList();
       $sku = $this->getSkuList();
       $list = [];
       if ($sku) {
           foreach ($sku as $item){
               //由于前端联调说前端组件问题无法解决，需要后端给返回一个默认值
               $item['apply_num'] = 0;
               $list[$item['category']][] = $item;
           }
       }
       return['category'=>$category, 'sku'=>$list];
    }

    /**
     * 获取申请包材枚举（用途、防重提交）等
     * @return array
     */
    public function getApplyEnums()
    {
        $data = [];
        //用途
        $use_status = PackageEnums::$package_order_use;
        foreach ($use_status as $key=>$status) {
            $data['use'][] = [
                'id'=>$key,
                'label'=>$this->getTranslation()->_($status)
            ];
        }
        //防重复提交
        $csrfServer     = new CsrfTokenServer(PackageEnums::PACKAGE_ORDER_FROM_CSRF_TOKEN);
        $csrfToken      = $csrfServer->getCsrfToken();
        $data['csrf_token'] = $csrfToken;
        return $data;
    }

    /**
     * 申请包材
     * @param array $params 申请信息
     * @param array $user_info 当前登陆着信息
     * @return array
     * @throws ValidationException
     */
    public function apply($params, $user_info)
    {
        // 验证该表单是否重复提交了
        $csrfToken = $params['csrf_token'];
        $csrfServer = new CsrfTokenServer(PackageEnums::PACKAGE_ORDER_FROM_CSRF_TOKEN);
        $res        = $csrfServer->checkCsrfToken($csrfToken);
        if (isset($res['msg'])) {
            //重复提交了
            throw new ValidationException($res['msg']);
        }
        //提交过来的sku数据
        $sku_data = $params['sku_list'];
        $post_sku_data = array_column($sku_data, null, 'sku_id');
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $sku_id_arr = array_column($sku_data, "sku_id");
            $sku_list = MaterialPackageSkuModel::find([
                'conditions' => "is_deleted = :is_deleted: and status = :status: and id in ({ids:array})",
                'bind' => [
                    'is_deleted'  => PackageEnums::IS_DELETED_NO,
                    'status' => PackageEnums::STATUS_ENABLE,
                    'ids' => $sku_id_arr
                ]
            ])->toArray();
            if (count($sku_id_arr) != count($sku_list)) {
                //如果查询的数量跟提交过来的数据不一致
                throw new ValidationException($this->getTranslation()->_('package_apply_sku_not_found'));
            }

            //生成code码
            $order_code   = $this->generateOrderCode();
            //获取部门名称
            $staff_re = new StaffRepository($this->lang);
            $staff_info = $staff_re->getStaffPosition($user_info['staff_id']);

            $order = new MaterialPackageOrderModel();
            $order->order_code = $order_code;//申请编号BC年月日6位流水
            $order->staff_id = $user_info['staff_id'];//申请人工号
            $order->staff_name = $user_info['name'] ?? '';//申请人姓名
            $order->store_id = $user_info['organization_type'] == 2 ? '-1' : ($user_info['organization_id'] ?? '');//所属网点
            $order->store_name = $user_info['organization_type'] == 2 ? enums::HEAD_OFFICE : ($user_info['organization_name'] ?? '');//所属网点名称
            $order->sys_department_id = $staff_info['sys_department_id'];//一级部门Id
            $order->sys_department_name = $staff_info['depart_name'];//一级部门名称
            $order->node_department_id = $staff_info['node_department_id'] ?? 0;//二级部门Id
            $order->node_department_name = $staff_info['node_dep_name'];//二级部门名称
            $order->job_id = $staff_info['job_title'] ?? 0;//职位ID
            $order->job_name = $staff_info['job_name'];//职位名称
            $order->use = $params['use'];//用途
            $order->use_remark = $params['use_remark'] ?? '';//其他用途备注
            $order->use_phone = $params['use_phone'] ?? '';//免费给客户用途记录客户电话
            $order->status = PackageEnums::PACKAGE_ORDER_STATUS_SUBMIT;//申请状态：待确认
            $order->submit_at = date('Y-m-d H:i:s');//提交时间
            $order->created_at = date('Y-m-d H:i:s');//创建时间
            $order->updated_at =  date('Y-m-d H:i:s');//更新时间
            //保存订单数据
            if (!$order->save()) {
                $messages = $order->getMessages();
                foreach ($messages as $message) {
                    $message = '_' . $message;
                }
                throw new \Exception($message);
            }
            //保存订单sku信息
            $order_id = $order->id;
            $order_goods_sku = [];
            foreach ($sku_list as $item) {
                $order_goods_sku[] = [
                    'order_id' => $order_id,//包材申请单ID
                    'sku_id' => $item['id'],//包材sku表ID
                    'barcode' => $item['barcode'],//barcode
                    'category' => $item['category'],//分类ID
                    'goods_name_zh' => $item['goods_name_zh'],//包材中文名称
                    'goods_name_th' => $item['goods_name_th'],//包材泰文名称
                    'goods_name_en' => $item['goods_name_en'],//包材英文名称
                    'specs_model' => $item['specs_model'],//规格型号
                    'unit' => $item['unit'],//单位
                    'image_path' => $item['image_path'],//图片
                    'apply_num' => $post_sku_data[$item['id']]['apply_num'],//申请数量
                    'created_at' => date('Y-m-d H:i:s'),//创建时间
                    'updated_at' => date('Y-m-d H:i:s')//更新时间
                ];
            }
            $order_sku = new MaterialPackageOrderSkuModel();
            $insert_flag = $order_sku->batch_insert($order_goods_sku);
            if (!$insert_flag) {
                throw new \Exception("插入material_package_order_sku 失败" . json_encode($order_goods_sku, JSON_UNESCAPED_UNICODE));
            }
            $db->commit();
            return ['order_id'=>$order_id];
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('package-order-apply-create' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 生成包材申请单流水单号
     * @return string
     */
    public function generateOrderCode()
    {
        $cache = $this->getDI()->get('redisLib');
        $redis_key = 'package_apply_BC' . date('Ymd');
        $redis_value = $cache->get($redis_key);
        if (!empty($redis_value)) {
            //存在缓存
            $cache->incrBy($redis_key, 1);
            $serial_no = $redis_value+1;
        } else {
            //不存在缓存，则从表中取最大的序列号加1并写入缓存中
            $max_order = MaterialPackageOrderModel::findFirst([
                'columns' => 'MAX(order_code) as order_code',
                'conditions' => "submit_at >= :start: and submit_at <= :end:",
                'bind'=>['start'=>date('Y-m-d 00:00:00'),'end' => date('Y-m-d 23:59:59')]
            ]);
            if ($max_order && $max_order->order_code) {
                $serial_no = intval(substr($max_order->order_code, 10))+1;
            } else {
                $serial_no = 1;
            }
            $cache->set($redis_key, $serial_no, strtotime("+1 day"));
        }
        // 长度16
        $order_code  =  'BC' . date('Ymd'). str_pad($serial_no, 6, "0", STR_PAD_LEFT);
        $order      = $this->getOrderInfo($order_code);
        if ($order) {
            $order_code = $this->generateOrderCode();
        }
        return $order_code;
    }

    /**
     * 获取订单信息
     * @param string $order_code 订单号
     * @param array $condition 其他查询条件
     * @return mixed
     */
    public function getOrderInfo($order_code, $condition = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialPackageOrderModel::class]);
        $builder->columns([
            'main.id',
            'main.order_code',
            'main.staff_id',
            'main.staff_name',
            'main.status',
            'main.submit_at',
            'main.job_name',
            'main.store_name',
            'main.node_department_name',
            'main.admin_staff_id',
            'main.admin_staff_name',
            'main.confirm_at',
            'main.reject_at',
            'main.reject_reason'
        ]);
        $builder->where('main.order_code = :order_code:', ['order_code'=>$order_code]);
        //申请人工号
        if (isset($condition['staff_id'])) {
            $type = $condition['type'] ?? PackageEnums::TYPE_APPLY;
            if ($type == PackageEnums::TYPE_APPLY) {
                //按照申请人工号筛选
                $builder->andWhere('main.staff_id = :staff_id:', ['staff_id'=>$condition['staff_id']]);
            } else if ($type == PackageEnums::TYPE_ADMIN) {
                //按照审批人工号筛选
                $builder->andWhere('main.admin_staff_id = :admin_staff_id:', ['admin_staff_id'=>$condition['staff_id']]);
            }
        }
        return $builder->getQuery()->execute()->getFirst();
    }

    /**
     * 获取订单详情
     * @param array $params['order_code'=>'订单编号','staff_id'=>'员工工号']
     * @return array
     */
    public function getDetail($params)
    {
        $order_code = $params['order_code'];
        $order = $this->getOrderInfo($order_code, $params);
        return $this->getOrderDetail($order);
    }

    /**
     * 获取订单详情信息
     * @param object $order 订单信息
     * @return array
     */
    private function getOrderDetail($order)
    {
        $data = ['order_info'=>[], 'sku_list' => []];
        if ($order) {
            $t = $this->getTranslation();
            $data['order_info'] = [
                'order_code' => $order->order_code,//申请编号
                'submit_at' => $order->submit_at,//提交时间
                'staff_id'=>$order->staff_id,//申请人工号
                'staff_name' => $order->staff_name,//申请人姓名
                'job_name' => $order->job_name,//职位名称
                'store_name' =>$order->store_name,//网点名称
                'node_department_name' => $order->node_department_name,//部门名称
                'status'=>$order->status,//订单状态
                'status_text' => $t->_(PackageEnums::$package_order_status[$order->status]),//订单状态文本
                'admin_staff_id' => $order->admin_staff_id,//管理员工号
                'admin_staff_name' => $order->admin_staff_name,//管理员姓名
                'confirm_at' => $order->confirm_at ?? '',//确认时间
                'reject_at' => $order->reject_at ?? '',//驳回时间
                'reject_reason' => $order->reject_reason,//驳回原因
                'use_name' => $t->_(PackageEnums::$package_order_use[$order->use]),
                'use' => $order->use,//用途
                'use_remark' => $order->use_remark,//其他用途备注
                'use_phone' => $order->use_phone,//免费给客户用途记录客户电话
            ];
            $data['sku_list'] = MaterialPackageOrderSkuModel::find([
                'columns'=>['sku_id','goods_name_'.$this->getGoodsNameLang().' as goods_name', 'specs_model', 'unit', 'image_path', 'apply_num','confirm_num'],
                'conditions' => "order_id = :order_id:",
                'bind' => [
                    'order_id'  => $order->id
                ]
            ])->toArray();
        }
        return $data;
    }

    /**
     * 撤回申请
     * @param array $params['order_code'=>'订单编号','staff_id'=>'员工工号']
     * @return bool
     * @throws ValidationException
     */
    public function applyCancel($params)
    {
        try {
            $order = MaterialPackageOrderModel::findFirst([
                'conditions' => "order_code = :order_code: and staff_id = :staff_id:",
                'bind'=>['order_code'=>$params['order_code'], 'staff_id' => $params['staff_id']]
            ]);
            if ($order) {
                if ($order->status == PackageEnums::PACKAGE_ORDER_STATUS_SUBMIT) {
                    $order->status = PackageEnums::PACKAGE_ORDER_STATUS_CANCEL;//撤回
                    $order->updated_at = date('Y-m-d H:i:s');//更新时间
                    $update_result = $order->update();
                    return $update_result ? true : false;
                } else {
                    throw new ValidationException($this->getTranslation()->_('package_order_wait_confirm_error'));
                }
            } else {
                //未找到符合条件的订单
                throw new ValidationException($this->getTranslation()->_('package_order_not_found'));
            }
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('package-order-cancel' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 检测审批合法性
     * @param array $params['order_code'=>'订单编号']
     * @param array $user_info 登陆者信息
     * @return mixed
     * @throws ValidationException
     */
    private function checkOrder($params, $user_info)
    {
        //当前登陆者员工工号
        $staff_id = $user_info['staff_id'];
        $has_permission = $this->checkPermission($user_info);
        if ($has_permission == false) {
            //非包材管理员，不可发放包材！
            //throw new ValidationException($this->getTranslation()->_('package_order_no_permission'));
        }
        //根据编号获取订单信息
        $order = MaterialPackageOrderModel::findFirst([
            'conditions' => "order_code = :order_code:",
            'bind'=>['order_code'=>$params['order_code']]
        ]);
        if ($order) {
            //订单是否为待确认
            if ($order->status == PackageEnums::PACKAGE_ORDER_STATUS_SUBMIT) {
                //申请人工号
                $apply_staff_id = $order->staff_id;
                if ($apply_staff_id == $staff_id) {
                    //申请人跟审批人为同一人，提示"不可确认本人提交的单据！"
                    throw new ValidationException($this->getTranslation()->_('package_order_cannot_operate_self'));
                } else {
                    //判断申请人的在职状态，是“停职或离职”，提示“该单据申请人非在职，不可发放包材！”
                    if ((new StaffRepository())->isOnJob($apply_staff_id)) {
                        //申请人在职返回订单信息用于操作
                        return $order;
                    } else {
                        throw new ValidationException($this->getTranslation()->_('package_order_staff_leave'));
                    }
                }
            } else {
                //申请单状态是不为”待确认，提示“该单据已处理，无法继续操作！“
                throw new ValidationException($this->getTranslation()->_('package_order_cannot_operate'));
            }
        } else {
            //未找到符合条件的订单
            throw new ValidationException($this->getTranslation()->_('package_order_not_found'));
        }
    }

    /**
     * 管理员确认详情页
     * @param array $params['order_code'=>'订单编号']
     * @param array $user_info 登陆者信息
     * @return array
     * @throws ValidationException
     */
    public function getConfirmDetail($params, $user_info)
    {
        try {
            $order = $this->checkOrder($params, $user_info);
            $data =  $this->getOrderDetail($order);
            if ($data['sku_list']) {
                foreach ($data['sku_list'] as &$item) {
                    $item['apply_num'] = intval($item['apply_num']);
                }
            }
            return $data;
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('package-order-cancel' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 扫一扫
     * @param array $params['order_code'=>'订单编号']
     * @param array $user_info 登陆者信息
     * @return array
     * @throws ValidationException
     */
    public function scanCode($params, $user_info)
    {
        try {
            $order = $this->checkOrder($params, $user_info);
            return ['url' => env('sign_url').PackageEnums::PACKAGE_CONFIRM_DETAIL_URL.$order->order_code];
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('package-order-cancel' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 确认
     * @param array $params['order_code'=>'订单编号']
     * @param array $user_info 登陆者信息
     * @return bool
     * @throws ValidationException
     */
    public function confirm($params, $user_info)
    {
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //判断申请的数据是否都是0
            $sku_data = $params['sku_list'];
            $confirm_num = array_sum(array_column($sku_data, "confirm_num"));
            if ($confirm_num <= 0) {
                throw new ValidationException($this->getTranslation()->_('package_order_confirm_num_zero'));
            }
            //获取个人信息
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id:',
                'bind' => [
                    'staff_id' => $user_info['staff_id']
                ]
            ]);
            $sku_list = MaterialWmsPlantaskInfoModel::findFirst([
                'conditions' => " plan_status !=:plan_status: and reality_sta_time>0 and reality_sta_time < :reality_sta_time:  and is_deleted=:is_deleted: and ( store_id = :store_id: or department_id =:department_id: ) ",
                'bind' => [
                    'plan_status' => 4,
                    'reality_sta_time' => date('Y-m-d H:i:s'),
                    'is_deleted' =>PackageEnums::IS_DELETED_NO,
                    'store_id'  => $staffInfo->sys_store_id,
                    'department_id' => $user_info['department_id'],
                ]
            ]);
            if(!empty($sku_list)){
                throw new ValidationException($this->getTranslation()->_('material_wms_have_no_way_use'));
            }

            $sku_id_arr = array_column($sku_data, "sku_id");
            //检测操作合法性
            $user_info['job_title_grade_v2'] = $staffInfo->job_title_grade_v2;
            $user_info['sys_store_id'] = $staffInfo->sys_store_id;
            $order = $this->checkOrder($params, $user_info);
            $sku_list = MaterialPackageOrderSkuModel::find([
                'conditions' => "order_id = :order_id: and sku_id in ({ids:array}) and is_deleted=:is_deleted:",
                'bind' => [
                    'order_id'  => $order->id,
                    'ids' => $sku_id_arr,
                    'is_deleted' => PackageEnums::IS_DELETED_NO
                ]
            ]);
            if ($sku_list->toArray()) {
                $post_sku_data = array_column($sku_data, null, 'sku_id');
                //找到满足条件的sku信息
                foreach ($sku_list as $item) {
                    if (!array_key_exists($item->sku_id, $post_sku_data) || $post_sku_data[$item->sku_id]['confirm_num'] > $item->apply_num) {
                        //如果提交的sku不是当前订单的或者确认数量超过了申请数量则提示不合法
                        throw new ValidationException($this->getTranslation()->_('package_order_confirm_num_pass'));
                        break;
                    } else {
                        $item->confirm_num = $post_sku_data[$item->sku_id]['confirm_num'];//确认数量
                        $item->updated_at = date('Y-m-d H:i:s');//更新时间
                        $item->update();
                    }
                }

                //订单信息操作
                $order->status = PackageEnums::PACKAGE_ORDER_STATUS_CONFIRMED;//确认
                $order->admin_staff_id = $user_info['staff_id'];//管理员工号
                $order->admin_staff_name = $user_info['name'];//管理员姓名
                $order->confirm_at = date('Y-m-d H:i:s');//确认时间
                $order->updated_at = date('Y-m-d H:i:s');//更新时间
                $result = $order->update();
                $db->commit();
                return $result ? true : false;
            } else {
                throw new ValidationException($this->getTranslation()->_('package_apply_sku_not_found'));
            }

        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('package-order-cancel' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 驳回
     * @param array $params['order_code'=>'订单编号']
     * @param array $user_info 登陆者信息
     * @return bool
     * @throws ValidationException
     */
    public function reject($params, $user_info)
    {
        try {
            $order = $this->checkOrder($params, $user_info);
            $order->status = PackageEnums::PACKAGE_ORDER_STATUS_REJECTED;//撤回
            $order->admin_staff_id = $user_info['staff_id'];//管理员工号
            $order->admin_staff_name = $user_info['name'];//管理员姓名
            $order->reject_reason = $params['reason'];//驳回原因
            $order->reject_at = date('Y-m-d H:i:s');//驳回时间
            $order->updated_at = date('Y-m-d H:i:s');//更新时间
            $update_result = $order->update();
            return $update_result ? true : false;
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('package-order-cancel' . $e->getMessage());
            throw $e;
        }
    }


    /**
     * 校验规则
     * @Date: 6/23/22 2:57 PM
     * @author: peak pan
     * @return:
     **/
    public  function validationsCheck(){

        return  [
            'external_order_code'=>'Required|StrLenGeLe:1,50|>>>:external_order_code'. $this->getTranslation()->_('external_order_code_miss_args'),//外部单号
            'source'=>'StrLenGeLe:0,30|>>>:'. $this->getTranslation()->_('source_miss_args'),//来源
            'identification'=>'StrLenGeLe:0,10|>>>:'. $this->getTranslation()->_('identification_miss_args'),//标识
            'status'=>'Required|IntIn:1,2|>>>:'. $this->getTranslation()->_('status_miss_args'),//状态
            'staff_id'=>'Required|Str|StrLenGeLe:5,8|>>>:'. $this->getTranslation()->_('staff_id_miss_args'),//账号
            'admin_staff_id'=>'IfIntEq:status,2|Required|Str|StrLenGeLe:5,8|>>>:'. $this->getTranslation()->_('admin_staff_id_miss_args'),//管理员账号
            'use'=>'Required|IntIn:'.PackageEnums::PACKAGE_ORDER_USE_FREE.','.PackageEnums::PACKAGE_ORDER_USE_SALE.','.PackageEnums::PACKAGE_ORDER_USE_FOLLOW.','.PackageEnums::PACKAGE_ORDER_USE_REPAIR.','.PackageEnums::PACKAGE_ORDER_USE_RETURN.','.PackageEnums::PACKAGE_ORDER_USE_OTHER.'|>>>:'. $this->getTranslation()->_('use_miss_args'),//用途
            'use_remark'=>'IfIntEq:use,'.PackageEnums::PACKAGE_ORDER_USE_OTHER.'|Required|Str|StrLenGeLe:10,500|>>>:'. $this->getTranslation()->_('use_remark_miss_args'),//用途其他备注
            'use_phone' => 'IfIntEq:use,'.PackageEnums::PACKAGE_ORDER_USE_FREE.'|Required|Str|StrLenGeLe:10,100|>>>:'. $this->getTranslation()->_('use_phone_miss_args'),//用途免费发放给客户的客户电话
            'sku_list'=> 'Required|Arr|ArrLenGe:1|>>>:'. $this->getTranslation()->_('sku_id_miss_args'),//批量申请包材
            'sku_list[*]'    => 'Required|Obj',
            'sku_list[*].sku_id'  => 'Required|IntGe:1|>>>:'. $this->getTranslation()->_('sku_id_miss_args'), //skuID
            'sku_list[*].apply_num'=> 'Required|IntGeLe:1,9999|>>>:'. $this->getTranslation()->_('apply_num_miss_args'),
        ];
    }

    /**
     * 外部调取接口添加数据
     * @Date: 6/22/22 6:48 PM
     * @author: peak pan
     * @return:
     **/
    public function packageApplyAddOrder($params)
    {
        // 验证该表单是否重复提交了
        $csrfToken = hash("sha256", json_encode($params));
        $csrfServer = new CsrfTokenServer(PackageEnums::PACKAGE_ORDER_FROM_CSRF_FIX,3);
        $res        = $csrfServer->checkCsrfRepeat($csrfToken);
        if ($res) {
            //重复提交了
            throw new ValidationException($this->getTranslation()->_('package_apply_add_order_duplicate_submission'),1005);
        }
        //提交过来的sku数据
        $sku_data = $params['sku_list'];
        $post_sku_data = array_column($sku_data, null, 'sku_id');
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            Validation::validate($params, $this->validationsCheck());
            $user_info = HrStaffInfoModel::findFirst([
                "conditions" => "staff_info_id = :staff_info_id: and  state = :state:",
                'bind' => [
                    'staff_info_id' => $params['staff_id'],
                    'state' => 1,
                ],
            ]);
            if(empty($user_info)){
                throw new ValidationException($this->getTranslation()->_('applicant_resigned_or_does_not_exist'),1006);
            }
            $user_info = $user_info->toArray();
            if($params['status']==PackageEnums::PACKAGE_ORDER_STATUS_CONFIRMED){
                $admin_user_info = HrStaffInfoModel::findFirst([
                    "conditions" => "staff_info_id = :staff_info_id: and  state = :state:",
                    'bind' => [
                        'staff_info_id' => $params['admin_staff_id'],
                        'state' => 1,
                    ],
                ]);

                if(empty($admin_user_info)){
                    throw new ValidationException($this->getTranslation()->_('management_resigned_or_does_not_exist'),1007);
                }
            }

            $order = new MaterialPackageOrderModel();

            $materialPackageOrder = $order->findFirst([
                "conditions" => "external_order_code = :external_order_code:",
                'bind' => [
                    'external_order_code' => $params['external_order_code'],
                ],
            ]);
            if(!empty($materialPackageOrder)){
                throw new ValidationException($this->getTranslation()->_('external_order_code_already_exists'),1008);
            }

            $sku_id_arr = array_column($sku_data, "sku_id");
            $sku_list = MaterialPackageSkuModel::find([
                'conditions' => "is_deleted = :is_deleted: and status = :status: and id in ({ids:array})",
                'bind' => [
                    'is_deleted'  => PackageEnums::IS_DELETED_NO,
                    'status' => PackageEnums::STATUS_ENABLE,
                    'ids' => $sku_id_arr
                ]
            ]);
            if(!empty($sku_list)){
                $sku_list = $sku_list->toArray();
            }else{
                throw new \Exception("包材sku数据表不存在" . json_encode($sku_id_arr, JSON_UNESCAPED_UNICODE));
            }
            if (count($sku_id_arr) != count($sku_list)) {
                //如果查询的数量跟提交过来的数据不一致
                throw new ValidationException($this->getTranslation()->_('package_apply_sku_not_found'),1004);
            }
            //生成code码
            $order_code   = $this->generateOrderCode();
            //获取部门名称
            $staff_re = new StaffRepository($this->lang);
            $staff_info = $staff_re->getStaffPosition($user_info['staff_info_id']);

            $order->order_code = $order_code;//申请编号BC年月日6位流水
            $order->external_order_code = $params['external_order_code'] ?? '';//外部单号
            $order->staff_id = $user_info['staff_info_id'];//申请人工号
            $order->staff_name = $user_info['name'] ?? '';//申请人姓名

            $order->store_id = $user_info['sys_store_id'];//所属网点
            if($user_info['sys_store_id'] != '-1'){
                $store = SysStoreModel::findFirst([
                    'conditions' => 'id = :store_id:',
                    'bind'       => ['store_id' => $user_info['sys_store_id']]
                ]);
                $store = $store ? $store->toArray() : [];
            }
            $order->store_name = $user_info['sys_store_id'] =='-1' ? enums::HEAD_OFFICE : ($store['name'] ?? '');//所属网点名称
            $order->sys_department_id = $staff_info['sys_department_id'];//一级部门Id
            $order->sys_department_name = $staff_info['depart_name'];//一级部门名称
            $order->node_department_id = $staff_info['node_department_id'] ?? 0;//二级部门Id
            $order->node_department_name = $staff_info['node_dep_name'];//二级部门名称
            $order->job_id = $staff_info['job_title'] ?? 0;//职位ID
            $order->job_name = $staff_info['job_name'];//职位名称
            $order->use = $params['use'];//用途
            $order->use_remark = $params['use_remark'] ?? '';//其他用途备注
            $order->source = $params['source'] ?? '';//来源
            $order->identification = $params['identification'] ?? '';//外部单号
            $order->use_phone = $params['use_phone'] ?? '';//免费给客户用途记录客户电话

            $order->submit_at = date('Y-m-d H:i:s');//提交时间
            $order->created_at = date('Y-m-d H:i:s');//创建时间
            $order->updated_at =  date('Y-m-d H:i:s');//更新时间

            $order->status =  PackageEnums::PACKAGE_ORDER_STATUS_SUBMIT;
            if( $params['status']==PackageEnums::PACKAGE_ORDER_STATUS_CONFIRMED){
                $order->confirm_at =  date('Y-m-d H:i:s');//管理员确定时间
                $order->status = PackageEnums::PACKAGE_ORDER_STATUS_CONFIRMED;//确认
                $order->admin_staff_id = $params['admin_staff_id'];//管理员工号
                $order->admin_staff_name = $admin_user_info->name??'';//管理员姓名
            }
            //保存订单数据
            if (!$order->save()) {
                $messages = $order->getMessages();
                foreach ($messages as $message) {
                    $message = '_' . $message;
                }
                throw new \Exception($message);
            }
            //保存订单sku信息
            $order_id = $order->id;
            $order_goods_sku = [];
            $confirm_num = 0;
            foreach ($sku_list as $item) {
                if( $params['status']==PackageEnums::PACKAGE_ORDER_STATUS_CONFIRMED){
                    $confirm_num = $post_sku_data[$item['id']]['apply_num'];
                }
                $order_goods_sku[] = [
                    'order_id' => $order_id,//包材申请单ID
                    'sku_id' => $item['id'],//包材sku表ID
                    'category' => $item['category'],//分类ID
                    'goods_name_zh' => $item['goods_name_zh'],//包材中文名称
                    'goods_name_th' => $item['goods_name_th'],//包材泰文名称
                    'goods_name_en' => $item['goods_name_en'],//包材英文名称
                    'specs_model' => $item['specs_model'],//规格型号
                    'unit' => $item['unit'],//单位
                    'image_path' => $item['image_path'],//图片
                    'apply_num' => $post_sku_data[$item['id']]['apply_num'],//申请数量
                    'confirm_num' => $confirm_num,//确定数量
                    'created_at' => date('Y-m-d H:i:s'),//创建时间
                    'updated_at' => date('Y-m-d H:i:s')//更新时间
                ];
            }
            $order_sku = new MaterialPackageOrderSkuModel();
            $insert_flag = $order_sku->batch_insert($order_goods_sku);
            if (!$insert_flag) {
                throw new \Exception("插入material_package_order_sku 失败" . json_encode($order_goods_sku, JSON_UNESCAPED_UNICODE));
            }

            $db->commit();

            $return['code'] = 1;
            $return['msg']  = 'success';
            $return['data'] = ['order_code'=>$order_code];
            return $return;

        } catch (ValidationException $e) {
            $return['code'] = $e->getCode()??'1009';
            $return['msg']  = $e->getMessage();
            $return['data'] = ['order_id'=>''];
            return $return;
        }catch(Validation $e){
            $return['code'] = '1009';
            $return['msg']  = $e->getMessage();
            $return['data'] = [];
            return $return;
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('package-order-apply-create' . $e->getMessage());
            $return['code'] = $e->getCode()??'1009';
            $return['msg']  = $e->getMessage();
            $return['data'] = ['order_id'=>''];
            return $return;
        }
    }



    /**
     * 撤回申请
     * @param array $params['order_code'=>'订单编号','staff_id'=>'员工工号']
     * @return bool
     * @throws ValidationException
     */
    public function packageApplyCancel($params)
    {
        try {
            $order = MaterialPackageOrderModel::findFirst([
                'conditions' => "order_code = :order_code: and staff_id = :staff_id:",
                'bind'=>['order_code'=>$params['order_code'], 'staff_id' => $params['staff_id']]
            ]);
            if ($order) {
                if ($order->status == PackageEnums::PACKAGE_ORDER_STATUS_SUBMIT) {
                    $order->status = PackageEnums::PACKAGE_ORDER_STATUS_CANCEL;//撤回
                    $order->updated_at = date('Y-m-d H:i:s');//更新时间
                    $update_result = $order->update();
                    $return['code'] = 1;
                    $return['msg']  = 'success';
                    $return['data'] = [];
                    return $return;
                } else {
                    throw new ValidationException($this->getTranslation()->_('package_order_wait_confirm_error'),1010);
                }
            } else {
                //未找到符合条件的订单
                throw new ValidationException($this->getTranslation()->_('package_order_not_found'),1011);
            }
        } catch (ValidationException $e) {
            $return['code'] = $e->getCode()??0;
            $return['msg']  = $e->getMessage();
            $return['data'] = [];
            return $return;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('package-order-cancel' . $e->getMessage());
            $return['code'] = $e->getCode()??0;
            $return['msg']  = $e->getMessage();
            $return['data'] = [];
            return $return;
        }
    }
}
