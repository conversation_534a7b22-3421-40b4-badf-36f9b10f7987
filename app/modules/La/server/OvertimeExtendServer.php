<?php


namespace FlashExpress\bi\App\Modules\La\Server;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditPermissionModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\OvertimeExtendServer as GlobalBaseServer;

class OvertimeExtendServer extends GlobalBaseServer
{
    /**
     * @param $staffId
     * @param $date
     * @param $start_time 时间戳
     * @param $end_time
     * @param $type
     * @return array
     */
    public function check_ot_record($staffId,$date,$start_time,$end_time,$type){
        $start_time = date('Y-m-d H:i:s',$start_time);
        $end_time = date('Y-m-d H:i:s',$end_time);
        //检查是否有重复记录
        $type_arr = array(1,2,4);
        if(in_array($type,array(2,4)))
            $type_arr = array_merge(array(1),array($type));
        $exist = HrOvertimeModel::find(
            [
                'conditions' => "state in (1,2) and staff_id = {$staffId}  and date_at = '{$date}' and type in({type_arr:array})",
                'bind'       => [
                    'type_arr'=> $type_arr,
                ],
                'columns' => 'overtime_id'
            ]
        )->toArray();
        if(!empty($exist))
            return $this->checkReturn(-3, $this->getTranslation()->_('5102'));

        if(!in_array($type,array(2,4)))//1。5倍ot 下面不需要校验
            return $this->checkReturn([]);

        $where = "state in (1,2) and staff_id = {$staffId}  and date_at = '{$date}' 
                          and (
                            (start_time < '{$start_time}' and end_time > '{$start_time}') or 
                            (start_time < '{$end_time}' and end_time > '{$end_time}') or 
                            (start_time < '{$start_time}' and end_time > '{$end_time}') or 
                            (start_time > '{$start_time}' and end_time < '{$end_time}') 
                          )";
        if($type == 2)
            $where .= " and type = 4";
        else if($type == 4)
            $where .= " and type = 2";

        $check = HrOvertimeModel::find([
            'conditions' => $where,
            'bind'       => [
                'type_arr'=> $type_arr,
            ],
            'columns' => 'overtime_id'
        ])->toArray();
        $this->logger->write_log("ot_check_exist {$staffId} start_time {$start_time},end_time {$end_time} ",'info');

        if(!empty($check))
            return $this->checkReturn(-3, $this->getTranslation()->_('5102'));

        return $this->checkReturn([]);
    }


    //新增的逻辑验证 https://l8bx01gcjr.feishu.cn/docs/doccnYP2pzjtkfHuGEJNHCUAY5g
    public function extend_check($param, $user_info){
        $config_hour = $this->config->application->add_hour;
        $staff_id = $user_info['staff_info_id'];
        $date = date('Y-m-d',strtotime($param['date_at']));
        $add_hour = 9;
        //获取请假记录 如果加班日当天 存在下午请假
        $leave_re = new AuditRepository($this->lang);
        $leave_info = $leave_re->get_leave_date($staff_id,$date,$date);
        //如果是请上午假 加5小时 其他情况不让申请ot 休息日类型假期 剔除
        if(!empty($leave_info) && $leave_info[0]['leave_type'] != 15){
            if($leave_info[0]['type'] != 1)
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_leave_limit'));
            $add_hour = ($add_hour == 9) ? 5 : 4.5;
        }

        //没有班次信息 不让申请
        if(empty($param['shift_info']))
            return $this->checkReturn(-3, $this->getTranslation()->_('no_shift_notice'));

        //如果 没打上班卡 不让申请
        $att_info = StaffWorkAttendance::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
        if(empty($att_info)){
            //没上班卡 判断是否出差 取出差打卡 上班卡信息
            $att_info = StaffAuditReissueForBusinessModel::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
            if(empty($att_info))
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_att_start'));
            $att_info = $att_info->toArray();
            $att_info['started_at'] = $att_info['start_time'];
        }else{
            $att_info = $att_info->toArray();
        }

        if(empty($att_info['started_at'])){
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_att_start'));
        }

        //去秒数
        $att_info['started_at'] = date('Y-m-d H:i:00',strtotime("{$att_info['started_at']}"));

        //通过日期判断新旧班次
        $start = $param['shift_info']['start'];

        //跟班次比对 如果是迟到 加班开始时间 应该在 迟到小时+1小时 时间整点
        $shift_start_time = strtotime("{$date} {$start}");
        $card_time = strtotime($att_info['started_at']) - $this->second + ($config_hour * 3600);
        //没迟到  取班次时间整点 加对应的小时数
        $limit_start = date('Y-m-d H:i:s',$shift_start_time + ($add_hour * 3600));
        if($card_time > $shift_start_time){//如果迟到 取打卡时间 加1小时 再加上对应的小时数
            $shift_i = date("i",$shift_start_time);//取班次的分钟
            $limit_start = date("Y-m-d H:{$shift_i}:00",$card_time + 3600);
            $limit_start = date('Y-m-d H:i:s',strtotime($limit_start) + ($add_hour * 3600));
        }

        $ot_start_time = date('Y-m-d H:i:s',strtotime($param['start_time']));
        $l1 = date('Y-m-d H:i:s',$shift_start_time);
        $l2 = date('Y-m-d H:i:s',$card_time);
        $this->logger->write_log("{$staff_id} add_hour {$add_hour},shift_start_time {$l1},card_time(-59) {$l2},limit_start {$limit_start},ot_start_time {$ot_start_time} ",'info');
        /*刘佳雪需求*/
        if(!isset($param['is_bi']) or empty($param['is_bi'])) {
            if ($ot_start_time < $limit_start)
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_forbidden'));
        }

        return $this->checkReturn([]);
    }

    /**
     * 校验权限
     * @param $staff_id
     * @param $type_list
     * 申请加班 针对每种类型的  权限展示 https://l8bx01gcjr.feishu.cn/docs/doccnM3g2ev63nhQDTFk15I13Vg
     */
    public function check_permission($staff_id, $type_list = array())
    {

        $default_v = 'x';//默认皮配值 前提条件是 其他匹配值没有x
        if(empty($staff_id))
            return array();
        //获取员工信息
        $staff_re = new StaffRepository($this->lang);
        $staff_info = $staff_re->getStaffPosition($staff_id);
        if(empty($staff_info))
            return array();
        if(empty($staff_info['node_department_id']))
            return array();

        //获取所属公司id
        $dep_info = SysDepartmentModel::findFirst("id = {$staff_info['node_department_id']} and deleted = 0");
        if(empty($dep_info))
            return array();
        //调试
//        $dep_info->ancestry_v3 = '123/222/1/4/32';
//        $dep_info->ancestry_v3 = '999/2';
        $ancestry = explode('/',$dep_info->ancestry_v3);
        if($ancestry[0] != '999')//错误组织架构数据
            return array();

        //补全5个层级
        $ancestry[0] = empty($ancestry[0]) ? $default_v : $ancestry[0];
        $ancestry[1] = empty($ancestry[1]) ? $default_v : $ancestry[1];
        $ancestry[2] = empty($ancestry[2]) ? $default_v : $ancestry[2];
        $ancestry[3] = empty($ancestry[3]) ? $default_v : $ancestry[3];
        $ancestry[4] = empty($ancestry[4]) ? $default_v : $ancestry[4];


        //所有配置权限记录
        $module_id = AuditPermissionModel::MODULE_P_1;
        $permission_list = AuditPermissionModel::get_permission($module_id);
        if(empty($permission_list))//没有配置对应公司限制条件 不在限制公司之内 都可申请
        {
            if(empty($type_list))
                return true;

            return $type_list;
        }

        //获取对应 限制条件的最终值
        $format = array();
        foreach ($permission_list as $v){//组装 key => value
            $l1 = empty($v['ancestry_1']) ? "{$default_v}" : "{$v['ancestry_1']}";
            $l2 = empty($v['ancestry_2']) ? "_{$default_v}" : "_{$v['ancestry_2']}";
            $l3 = empty($v['ancestry_3']) ? "_{$default_v}" : "_{$v['ancestry_3']}";
            $l4 = empty($v['ancestry_4']) ? "_{$default_v}" : "_{$v['ancestry_4']}";
            $l5 = empty($v['ancestry_5']) ? "_{$default_v}" : "_{$v['ancestry_5']}";
            $job_id = empty($v['job_title_id']) ? "_{$default_v}" : "_{$v['job_title_id']}";
            $grade = empty($v['job_title_grade']) ? "_{$default_v}" : "_{$v['job_title_grade']}";
            $k = $l1.$l2.$l3.$l4.$l5.$job_id.$grade;
            $format[$k] = json_decode($v['permission_value']);
        }


        //排列组合 当前员工 的key
        //def 为 默认key
        $combination_arr = array(
            array($ancestry[0],"{$default_v}"),
            array('_'.$ancestry[1],"_{$default_v}"),
            array('_'.$ancestry[2],"_{$default_v}"),
            array('_'.$ancestry[3],"_{$default_v}"),
            array('_'.$ancestry[4],"_{$default_v}"),
            array('_'.$staff_info['job_title'],"_{$default_v}"),//职位
            array('_'.$staff_info['job_title_grade_v2'],"_{$default_v}"),//职级

        );
        $staff_key_arr = combination($combination_arr);
        $staff_key_arr = array_unique($staff_key_arr[0]);
        //获取 匹配上的 并且 默认值数量最少的
        $correct_k = array();
        $num = count($combination_arr) + 1;//默认 x 数量 用x 拆分 num 为数量+1
        $final_value = array();//最终返回的 权限值
        if(!empty($staff_key_arr)){
            //获取所有匹配上的key
            foreach ($format as $key => $val){
                if(in_array($key,$staff_key_arr))
                    $correct_k[] = $key;
            }
            $cks = array();
            $k = '';
            foreach ($correct_k as $ck => $item){
                $n = explode($default_v,$item);
                if($num > count($n))
                {
                    $final_value = $item;
                    $num = count($n);
                    $k = $ck;
                }else if($num == count($n)){
                    $cks[] = $ck;
                    $cks[] = $k;
                }
            }
            //看数量相同的 是不是只有一个
            if(!empty($cks)){
                $check_correct = array();
                foreach ($cks as $ck){
                    $check_correct[] = $correct_k[$ck];
                }
                $this->logger->write_log("ot_check_permission {$staff_id} ".json_encode($correct_k),'info');
                $same_value = get_final_key($check_correct,$default_v);
                //相同数的 和最终key 比对 default的 数量  取默认值最少的
                $final_value = count(explode($default_v,$final_value)) > count(explode($default_v,$same_value)) ? $final_value : $same_value;
            }

        }
        $this->logger->write_log("ot_check_permission {$staff_id} final_value {$final_value} end",'info');
        if(empty($final_value))//一个都没匹配上 返回空
            return array();
        $final_value = $format[$final_value];

        if(empty($type_list)){
            //入口按钮权限判断 返回bool
            return empty($final_value) ? false : true;
        }
        $return = array();
        $data = array_column($type_list,null,'code');
        foreach ($final_value as $type){
            $return[] = $data[$type];
        }
        return $return;
    }

    /**
     *
     * 迁移到 function php了
     * @param $arr
     * @return mixed
     */
    public function combination($arr){
        if(count($arr) >= 2){
            $tmp_arr = array();
            $arr1 = array_shift($arr);
            $arr2 = array_shift($arr);
            foreach($arr1 as $k1 => $v1){
                foreach($arr2 as $k2 => $v2){
                    $tmp_arr[] = $v1.$v2;
                }
            }
            array_unshift($arr, $tmp_arr);
            $arr = $this->combination($arr);
        }else{
            return $arr;
        }
        return $arr;
    }

    //如果 含有默认值数量相同 取默认值 位置靠后的 如果第一个默认值相同 （不考虑 目前不存在）
    public function get_final_key($arr,$default){
        $p = 0;
        $final = '';
        foreach ($arr as $str){
            $pos = strpos($str,$default);//第一次出现的位置 越靠后 越优先
            if($pos > $p){
                $final = $str;
                $p = $pos;
            }
        }
        return $final;
    }



}