<?php

namespace FlashExpress\bi\App\Modules\La\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\PersoninfoServer as GlobalPersoninfoServer;

class PersoninfoServer extends GlobalPersoninfoServer
{
    public $timezone;
    public function __construct($lang = 'zh-CN', $timezone, $userInfo = array())
    {
        parent::__construct($lang,$timezone,$userInfo);
        $this->timezone = $timezone;
    }

    /**
     * 获取工资表明细
     * @param $staffId
     * @param $month
     * @return array|mixed
     * @throws \ReflectionException
     */
    public function getSalaryInfoFromHCM($paramIn)
    {
        $data    = [];
        $month   = $paramIn['month'];
        $staffId = $paramIn['staff_id'];
        //传一个当前月份 如果没有记录 会返回 有薪资记录的最新的一个月
        $param['month']    = $month;
        $param['staff_id'] = $staffId;
        $ac                = new ApiClient('hcm_rpc', '', 'get_salary_data', $this->lang);
        $ac->setParams($param);
        $ac_result = $ac->execute();
        $this->logger->write_log("staff {$staffId} getSalaryInfoFromHCM request:" . json_encode($param,
                JSON_UNESCAPED_UNICODE) . " res:" . json_encode($ac_result, JSON_UNESCAPED_UNICODE) . " ", 'info');

        if (!empty($ac_result) && !empty($ac_result['result']['data'])) {
            $data = $this->salary_data_format($ac_result['result']['data']);
            //考勤数量统计
            $salaryAttendanceStat = $this->salaryAttendanceStat($staffId, $data['salary_cycle'], 'only_total');
            $data                 = array_merge($data, $salaryAttendanceStat);
            //工资条新增 有效无效ot 数量
            [$data['effect_num'], $data['invalid_num']] = $this->formatOtData($staffId, $data['salary_cycle']);
        }

        return $data;
    }



    /**
     * 格式化工资表 给前端
     * @param $salary_data
     * @return mixed
     */
    public function salary_data_format($salary_data)
    {
        if(!isset($salary_data['salary_date'])){
            return  [];
        }
        $table_salary = $salary_data;
        $table_salary['title'] =  $salary_data['salary_date'] . ' ໃບແຈ້ງເງິນເດືອນ / PAY SLIP';
        $table_salary['staff_id'] = $salary_data['staff_id'];
        $table_salary['Name'] = $salary_data['name'];
        $table_salary['Position'] = $salary_data['position'];
        $table_salary['dep'] = $salary_data['department'];
        $table_salary['bank_no'] = $salary_data['bank_no'];
        $table_salary['date'] = $salary_data['salary_date'];
        $table_salary['transfer_day'] = $salary_data['transfer_day'];

        return $table_salary;
    }

}
