<?php
namespace FlashExpress\bi\App\Modules\La\Controllers;

use FlashExpress\bi\App\Controllers\AssetController as BaseAsset;
use FlashExpress\bi\App\Modules\La\Server\AssetServer;
use Exception;

class AssetController extends BaseAsset
{

    public function initialize()
    {
        parent::initialize();
        $this->assetServer = new AssetServer($this->lang, $this->timezone);
        $this->paramIn     = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }
    /**
     * 申请资产-负责人提交申请接口
     * zcc
     */
    public function submitApplyAction()
    {
        try{
            //todo 验证用户是否有该模块权限
            if (
            !$this->AuditServer->getASPermission(['staff_id' => $this->userinfo['id']])
            ) {
                return $this->jsonReturn(self::checkReturn(['data' => new stdClass()]));
            }

            //todo 参数验证
            if (empty($this->paramIn['assets']) || empty($this->paramIn['reason'])) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('miss_args')));
            }

            //校验状态
            $assetsHandoerData = $this->assetServer->getAssetsHanderByStaffId($this->userinfo["staff_id"]
            );
            $res=$this->assetServer->getWinhrProve($this->userinfo);
            if (!$assetsHandoerData&&!$res){
                // 没有上传过
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('asset_00005')));
            }

            //申请订单
            $this->jsonReturn($this->assetServer->addAssetsOrder($this->paramIn,$this->userinfo));
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("submitApplyAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }

}
