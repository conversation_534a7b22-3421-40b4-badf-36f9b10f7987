<?php
namespace FlashExpress\bi\App\Modules\La\Controllers;

use FlashExpress\bi\App\Controllers\WmsController as BaseWmsController;
use FlashExpress\bi\App\Modules\La\Server\SysStoreServer;
use FlashExpress\bi\App\Modules\La\Server\WmsServer;
use Exception;

class WmsController extends BaseWmsController
{
    protected $server;

    public function initialize()
    {
        parent::initialize();
        $this->server = ['wms' => new WmsServer($this->lang, $this->timezone),
            'store' => new SysStoreServer($this->timezone)];
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 商品订单创建
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addOrderAction()
    {
        try {
            //[1]入参
            $paramIn = $this->paramIn;
            $userinfo = $this->userinfo;
            $validations = [
                "reason_application" => "Required|StrLenGeLe:10,500|>>>:" . $this->getTranslation()->_('7102'),
            ];
            $this->validateCheck($paramIn, $validations);

            $barCodeArr = array_column($paramIn['goods_order_detail'], 'bar_code');
            if (count($barCodeArr) != count(array_unique($barCodeArr))) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('7103')));
            }
            $this->getDI()->get("logger")->write_log("wmsAddOrder: " . json_encode($paramIn, JSON_UNESCAPED_UNICODE), "info");
            //如果是网点经理或者主管
                //original_order_status 是原来状态 order_status修改后状态
                $paramIn['original_order_status'] = 1;
                $returnArr = $this->server['wms']->addOrderS($paramIn, $userinfo);
        } catch (\Exception $e) {
            $this->wLog('order_err', $e->getMessage() . json_encode($paramIn) . json_encode($userinfo), 'wms');
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('2109')));
        }
        $this->jsonReturn($returnArr);

    }


}
