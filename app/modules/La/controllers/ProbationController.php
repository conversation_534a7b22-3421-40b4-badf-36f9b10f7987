<?php
namespace FlashExpress\bi\App\Modules\La\Controllers;

use FlashExpress\bi\App\Controllers\ProbationController as BaseProbationController;
use FlashExpress\bi\App\Modules\La\Server\ProbationServer;
use Exception;

class ProbationController extends BaseProbationController
{
	
	/**
	 * @description:交转正评估评审
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/9/13 10:35
	 */
    public function auditAction() {

        $paramIn     = $this->paramIn;
        $validations = [
            "id"     => "Required|IntGe:0",
            'score'  => "Required",
            'remark' => "StrLenGeLe:0,255",
            'pic'    => "StrLenGeLe:0,255",
        ];
        $this->validateCheck($paramIn, $validations);

        $paramIn             = array_only($paramIn, array_keys($validations));
        $paramIn['audit_id'] = $this->userinfo['id'];
        //防止重复提交
        $data = (new ProbationServer($this->lang, $this->timezone))->probation_auditUseLock($paramIn['id'], $paramIn['audit_id'], $paramIn);
        return $this->jsonReturn($data);

    }



}
