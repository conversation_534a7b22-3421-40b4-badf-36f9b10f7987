<?php
namespace FlashExpress\bi\App\Modules\La\Controllers;

use FlashExpress\bi\App\Enums\PackageEnums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\La\Server\PackageReturnServer;
use FlashExpress\bi\App\Modules\La\Server\PackageServer;
use Exception;
/**
 * 包材退还控制器
 * Class PackageController
 */
class PackageReturnController extends BaseController
{
    protected $paramIn;
    protected $packageServer;
    protected $packageReturnServer;

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        $this->packageReturnServer = new PackageReturnServer($this->lang, $this->timezone);
        $this->packageServer = new PackageServer($this->lang, $this->timezone);
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
    * 获取退还订单枚举
    * @Date: 6/28/22 7:56 PM
    * @author: peak pan
    * @return:
    * https://yapi.flashexpress.pub/project/93/interface/api/58951
    **/
    public function getOrderStatusAction()
    {
        try {
            $result = $this->packageReturnServer->getOrderStatus();
            return $this->returnJson(ErrCode::SUCCESS,'ok',$result);
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("getOrderStatusAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
    * 获取申请人申请列表或管理员审批列表
    * @Date: 6/29/22 11:47 AM
    * @author: peak pan
    * @return:
    * https://yapi.flashexpress.pub/project/93/interface/api/59068
    **/
    public function orderListAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations = [
                'type' => 'IntIn:'.PackageEnums::TYPE_APPLY.','.PackageEnums::TYPE_ADMIN.'|>>>:type'. $this->getTranslation()->_('miss_args'),//类型，1申请人，2审批人
                'status' => 'IntIn:'.PackageEnums::PACKAGE_ORDER_STATUS_SUBMIT.','.PackageEnums::PACKAGE_ORDER_STATUS_CONFIRMED.','.PackageEnums::PACKAGE_ORDER_STATUS_CANCEL.'|>>>:status'. $this->getTranslation()->_('miss_args'),//状态
                "page_num" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),//页码
                "page_size" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),//每页条数
            ];
            //过滤非必需参数
            $paramIn = $this->packageReturnServer->handleParams($paramIn, ['status','type']);
            //验证
            $this->validateCheck($paramIn, $validations);
            $paramIn['page_num'] = $this->processingDefault($paramIn, 'page_num', 2, PackageEnums::PAGE_NUM);
            $paramIn['page_size'] = $this->processingDefault($paramIn, 'page_size', 2,PackageEnums::PAGE_SIZE);
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $list = $this->packageReturnServer->getOrderList($paramIn);
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("orderListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
    * 获取启用的包材清单列表
    * @Date: 6/29/22 11:47 AM
    * @author: peak pan
    * @return:
    * https://yapi.flashexpress.pub/project/93/interface/api/58978
    **/
    public function skuListAction()
    {
        try {
            $list = $this->packageServer->getPackageCategoryAndSkuList();
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("listAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取启用的包材清单列表
     * @Date: 6/29/22 11:47 AM
     * @author: peak pan
     * @return:
     * https://yapi.flashexpress.pub/project/93/interface/api/58987
     **/
    public function getStoreEnumsAction()
    {
        $paramIn = $this->paramIn;
        try {
            $result = $this->packageReturnServer->getStoreEnums($paramIn);
            return $this->returnJson(ErrCode::SUCCESS,'ok',$result);
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("getApplyEnumsAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
     * 申请退还包材添加接口
     * @Date: 6/29/22 11:47 AM
     * @author: peak pan
     * @return:
     * https://yapi.flashexpress.pub/project/93/interface/api/59059
     **/
    public function applyAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations = [
                "return_store_id" => "Str|StrLenGeLe:1,15|>>>:return_store_id ".$this->getTranslation()->_('miss_args'),
                'sku_list'=> 'Required|Arr|ArrLenGe:1|>>>:sku list'. $this->getTranslation()->_('miss_args'),//批量申请包材
                'sku_list[*]'    => 'Required|Obj',
                'sku_list[*].sku_id'  => 'Required|IntGe:1|>>>:sku_id'. $this->getTranslation()->_('miss_args'), //skuID
                'sku_list[*].apply_num'=> 'Required|IntGeLe:1,9999999|>>>:apply_num'. $this->getTranslation()->_('apply_num_miss_args_max'), //申请数量
            ];
            //验证
            $this->validateCheck($paramIn, $validations);
            $data = $this->packageReturnServer->apply($paramIn, $this->userinfo);
            $this->jsonReturn($this->checkReturn(array('data' => $data)));
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }   catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("applyAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

    }


    /**
     * 获取申请人申请包材或管理员审批包材详情
     * @Date: 6/29/22 11:47 AM
     * @author: peak pan
     * @return:
     * https://yapi.flashexpress.pub/project/93/interface/api/59122
     **/
    public function detailAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations_order_code = $this->packageReturnServer->getValidateOrderCode();
            $validations = [
                'type' => 'IntIn:'.PackageEnums::TYPE_APPLY.','.PackageEnums::TYPE_ADMIN.'|>>>:type'. $this->getTranslation()->_('miss_args')//类型，1申请人，2审批人
            ];
            //过滤非必需
            $paramIn = $this->packageReturnServer->handleParams($paramIn, ['type']);
            //验证
            $this->validateCheck($paramIn, array_merge($validations_order_code, $validations));
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $list = $this->packageReturnServer->getDetail($paramIn);
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("detailAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
     * 包材申请撤回
     * @Date: 6/29/22 11:47 AM
     * @author: peak pan
     * @return:
     * https://yapi.flashexpress.pub/project/93/interface/api/59131
     **/
    public function applyCancelAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations_order_code = $this->packageReturnServer->getValidateOrderCode();
            //验证
            $this->validateCheck($paramIn, $validations_order_code);
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $result = $this->packageReturnServer->applyCancel($paramIn);
            return $this->returnJson(ErrCode::SUCCESS, 'ok', $result);
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("applyCancelAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
     * 包材申请撤回
     * @Date: 6/29/22 11:47 AM
     * @author: peak pan
     * @return:
     * https://yapi.flashexpress.pub/project/93/interface/api/59149
     **/
    public function confirmDetailAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations = $this->packageReturnServer->getValidateOrderCode();
            //验证
            $this->validateCheck($paramIn, $validations);
            $list = $this->packageReturnServer->getConfirmDetail($paramIn, $this->userinfo);
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("confirmDetailAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
     * 管理员确认员工申请
     * @Date: 6/29/22 11:47 AM
     * @author: peak pan
     * @return:
     * https://yapi.flashexpress.pub/project/93/interface/api/59185
     **/

    public function confirmAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations_order_code = $this->packageReturnServer->getValidateOrderCode();
            $validations = [
                'sku_list'=> 'Required|Arr|ArrLenGe:1|>>>:sku list'. $this->getTranslation()->_('miss_args'),//批量申请包材
                'sku_list[*]' => 'Required|Obj',
                'sku_list[*].sku_id'  => 'Required|IntGe:1|>>>:sku_id'. $this->getTranslation()->_('miss_args'), //skuID
                'sku_list[*].confirm_num'=> 'Required|IntGeLe:0,9999999|>>>:confirm_num'. $this->getTranslation()->_('apply_num_miss_args_max'), //申请数量
            ];
            //验证
            $this->validateCheck($paramIn, array_merge($validations_order_code, $validations));
            $result = $this->packageReturnServer->confirm($paramIn, $this->userinfo);
            return $this->returnJson(ErrCode::SUCCESS, 'ok', $result);
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("confirmAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

    }



    /**
     * 管理员驳回员工申请
     * @Date: 6/29/22 11:47 AM
     * @author: peak pan
     * @return:
     * https://yapi.flashexpress.pub/project/93/interface/api/59194
     **/
    public function rejectAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations_order_code = $this->packageReturnServer->getValidateOrderCode();
            $validations = [
                'reason' => 'Required|Str|StrLenGeLe:10,500|>>>:reason'. $this->getTranslation()->_('miss_args'),//驳回原因
            ];
            //验证
            $this->validateCheck($paramIn, array_merge($validations_order_code, $validations));
            $result = $this->packageReturnServer->reject($paramIn, $this->userinfo);
            return $this->returnJson(ErrCode::SUCCESS, 'ok', $result);
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("rejectAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取当前登录人的网点数据
     * @Date: 6/29/22 11:47 AM
     * @author: peak pan
     * @return:
     * https://yapi.flashexpress.pub/project/93/interface/api/59239
     **/
    public function getUserStoreAction()
    {
        try {
            $result = $this->packageReturnServer->getUserStore($this->userinfo);
            return $this->returnJson(ErrCode::SUCCESS, 'ok', $result);
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("rejectAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }



}
