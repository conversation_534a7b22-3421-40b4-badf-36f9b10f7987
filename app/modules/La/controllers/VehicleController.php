<?php
namespace FlashExpress\bi\App\Modules\La\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Modules\La\library\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Modules\La\Server\VehicleServer;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\SettingEnvServer;
use Exception;

class VehicleController extends Controllers\ControllerBase
{


    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 车辆信息，枚举列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function enumVehicleAction()
    {
        $returnArr['data'] = [];

        // 非van/bike职位，无权限访问数据 LA: Van courier[110]、Bike Courier[13]
        $vehicleJobTitle = explode(',',(new SettingEnvServer())->getSetVal('job_title_vehicle_type')) ;

        $staff_info_id = $this->userinfo['id'];
        $checkStaff = (new StaffRepository())->checkoutStaffBi($staff_info_id);
        if ($checkStaff['is_sub_staff'] == 1){
            // 子账号需切到主账号 获取信息
            $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportInfoBySubStaff($staff_info_id);
            if (empty($supportStaffInfo)){
                $this->jsonReturn($this->checkReturn($returnArr));
            }
            $staff_info_id = $supportStaffInfo['staff_info_id'];
            $checkStaff = (new StaffRepository())->checkoutStaffBi($staff_info_id);
        }

        if (in_array($checkStaff['job_title'], $vehicleJobTitle)) {
            $infoArr = (new VehicleServer($this->lang, $this->timezone))->getVehicleInfoS(["id"=>$staff_info_id,"job_title"=>$checkStaff['job_title']]);

            //如果车辆信息不存在输出类型(判断废弃，同时返回)
            $enumArr = (new VehicleServer($this->lang, $this->timezone))->enumVehicleS();

            $returnArr['data'] = $infoArr + $enumArr;
        }

        $this->jsonReturn($this->checkReturn($returnArr));
    }

    /**
     * 创建车辆信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addVehicleInfoAction()
    {
        try {
            //[1]入参和验证
            $paramIn = trim_array($this->paramIn);
            $userinfo = $this->userinfo;

            $this->getDI()->get('logger')->write_log('kit_add_vehicle_info, 请求参数: '.json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');
            $this->getDI()->get('logger')->write_log('kit_add_vehicle_info, 当前用户: '.json_encode($userinfo, JSON_UNESCAPED_UNICODE), 'info');

            $checkStaff = (new StaffRepository())->checkoutStaffBi($userinfo['id']);
            $staff_info_id = $this->userinfo['id'];
            if ($checkStaff['is_sub_staff'] == 1){
                // 子账号需切到主账号 获取信息
                $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportInfoBySubStaff($staff_info_id);
                if (empty($supportStaffInfo)){
                    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
                }
                $staff_info_id = $supportStaffInfo['staff_info_id'];
                $checkStaff = (new StaffRepository())->checkoutStaffBi($staff_info_id);
            }

            // 职位权限校验: 仅限 VehicleInfoEnums::JOB_TITLE_ITEM 列出的职位可操作
            if (!array_key_exists($checkStaff['job_title'], VehicleInfoEnums::JOB_TITLE_ITEM)) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
            }

            if (!is_array($paramIn['driving_licence_img_item']) && !empty($paramIn['driving_licence_img_item'])) {
                $paramIn['driving_licence_img_item'] = json_decode($paramIn['driving_licence_img_item']);
            }

            // 公共验证字段
            $validations = [
                //车辆来源
                'vehicle_source' => 'Required|IntIn:1,2,3|>>>:vehicle_source' . $this->getTranslation()->_('miss_args'),
                'vehicle_start_date' => 'IfIntEq:vehicle_source,2|Required|Date|>>>:vehicle_start_date' . $this->getTranslation()->_('miss_args'),//租用公司车辆（选择此项时新增日期选择：“用车开始时间”，必填，不能早于入职日期）
                //车牌号
                "plate_number" => "Required|StrLenGeLe:1,50|>>>:" . $this->getTranslation()->_('7130'),
                //发动机号码
                "engine_number" => "Required|StrLenGeLe:10,17|>>>:" . $this->getTranslation()->_('vehicle_info_0002'),
                //上牌地点
                'license_location' => 'Required|StrLenGeLe:1,512|>>>:license_location' . $this->getTranslation()->_('miss_args'),
                //机动车登记证书
                "registration_certificate_img" => "Required|StrLenGeLe:1,255|>>>:registration_certificate_img" . $this->getTranslation()->_('miss_args'),
                //车辆照片
                "vehicle_img" => "Required|StrLenGeLe:1,200|>>>:" . $this->getTranslation()->_('7134'),
                //车辆保险单号
                "insurance_policy_number" => "Required|StrLenGeLe:1,50|>>>:insurance_policy_number" . $this->getTranslation()->_('miss_args'),
                //保险开始时间
                "insurance_start_date" => "Required|Date|>>>:insurance_start_date" . $this->getTranslation()->_('miss_args'),
                //保险结束时间
                "insurance_end_date" => "Required|Date|>>>:insurance_end_date" . $this->getTranslation()->_('miss_args'),
                //车辆税失效时间
                "vehicle_tax_expiration_date" => "Required|Date|>>>:vehicle_tax_expiration_date" . $this->getTranslation()->_('miss_args'),
                //车辆税证明
                "vehicle_tax_certificate_img" => "Required|StrLenGeLe:1,255|>>>:vehicle_tax_certificate_img" . $this->getTranslation()->_('miss_args'),
                //驾照类型
                "driver_license_type" => "Required|Arr|>>>:driver_license_type" . $this->getTranslation()->_('miss_args'),
                //驾照号码
                "driver_license_number" => "Required|StrLenGeLe:1,128|>>>:driver_license_number" . $this->getTranslation()->_('miss_args'),
                //驾照开始有效日期
                "driver_license_start_date" => "Required|Date|>>>:driver_license_start_date" . $this->getTranslation()->_('miss_args'),
                //驾照过期日期
                "driver_license_end_date" => "Required|Date|>>>:driver_license_end_date" . $this->getTranslation()->_('miss_args'),
                //驾照图片
                "driving_licence_img_item" => "Required|ArrLen:2|>>>:" . $this->getTranslation()->_('vehicle_info_0008'),
            ];

            // van职位 特有字段验证
            if (in_array($checkStaff['job_title'],VehicleInfoEnums::VAN_JOB_GROUP_ITEM)) {
                $brand = implode(',',array_column(VehicleInfoEnums::CONFIG_VEHICLE_INFO['vehicle_brand'],'value'));
                $vehicle_size = implode(',',array_keys(VehicleServer::getVehicleSize(false)));
                $oil_type = implode(',',array_keys(VehicleInfoEnums::OIL_TYPE_ITEM));
                $other_validations = [
                    "vehicle_brand" => "Required|IntIn:$brand|>>>:vehicle_brand" . $this->getTranslation()->_('miss_args'),
                    "vehicle_brand_text" => "IfIntEq:vehicle_brand,8|Required|StrLenGeLe:1,200|>>>:vehicle_brand_text" . $this->getTranslation()->_('miss_args'),
                    "vehicle_model" => "Required|IntIn:1,2,3,4,5,6,7,8,9,10,11,100|>>>:vehicle_model" . $this->getTranslation()->_('miss_args'),
                    "vehicle_model_text" => "IfIntEq:vehicle_model,100|Required|StrLenGeLe:1,200|>>>:vehicle_model_text" . $this->getTranslation()->_('miss_args'),
                    "vehicle_size" => "Required|IntIn:$vehicle_size|>>>:vehicle_size".$this->getTranslation()->_('miss_args'),
                    "buy_date" => "Required|Date|>>>:buy_date" . $this->getTranslation()->_('miss_args'),
                    "oil_type" => "Required|IntIn:$oil_type|>>>:oil_type" . $this->getTranslation()->_('miss_args'),
                ];

                $validations = array_merge($validations, $other_validations);
            }

            $this->validateCheck($paramIn, $validations);

            //购买日期验证
            if ($paramIn['buy_date'] > date("Y-m-d")) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('7131')));
            }

            if ($paramIn['insurance_end_date'] < $paramIn['insurance_start_date']) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0004')));
            }

            if ($paramIn['driver_license_end_date'] < $paramIn['driver_license_start_date']) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0005')));
            }

            $returnArr = (new VehicleServer($this->lang, $this->timezone))->addVehicleInfoS($paramIn, ["id"=>$staff_info_id,"job_title"=>$checkStaff['job_title']],$this->userinfo['id']);

            $this->getDI()->get('logger')->write_log('kit_add_vehicle_info, 返回结果: '.json_encode($returnArr, JSON_UNESCAPED_UNICODE), 'info');
        } catch (\Exception $e) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 打卡里程查询
     * @throws ValidationException
     */
    public function getMileageAction()
    {
        //[1]入参和验证
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;

        $returnArr = (new VehicleServer($this->lang, $this->timezone))->getMileageSV2($paramIn, $userinfo);
        $this->jsonReturn($returnArr);
    }

    /**
     * 创建上报车辆里程申请
     * @Return  array
     * @throws BusinessException
     */
    public function addVehicleAction()
    {
        //[1]入参和验证
        $paramIn     = $this->paramIn;
        $userinfo    = $this->userinfo;
        $validations = [
            "mileage_date"     => "Required|Date|>>>:" . $this->getTranslation()->_('miss_args'),
            "start_kilometres" => "Required|IntGtLt:0,100000000000000|>>>:" . $this->getTranslation()->_('miss_args'), //只能输入1-999999999
            "started_img"      => "Required|StrLenGeLe:1,200|>>>:" . $this->getTranslation()->_('miss_args'),
            "started_bucket"   => "Required|StrLenGeLe:1,100|>>>:" . $this->getTranslation()->_('miss_args'),
            "started_path"     => "Required|StrLenGeLe:1,100|>>>:" . $this->getTranslation()->_('miss_args'),
            "end_kilometres"   => "Required|Required|IntGtLt:0,1000000000000|>>>:" . $this->getTranslation()->_('miss_args'),
            "end_img"          => "Required|StrLenGeLe:1,200|>>>:" . $this->getTranslation()->_('miss_args'),
            "end_bucket"       => "Required|StrLenGeLe:1,100|>>>:" . $this->getTranslation()->_('miss_args'),
            "end_path"         => "Required|StrLenGeLe:1,100|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);

        //申请操作
        $vehicleServer = new VehicleServer($this->lang, $this->timezone);
        $returnArr     = $vehicleServer->setLockConf(10)->addVehicleV2UseLock($paramIn, $userinfo);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 审核
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkVehicleAction()
    {
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;

        $validations = [
            "status"   => "Required|Required|IntIn:1,2,3,4|>>>:" . $this->getTranslation()->_('miss_args'),
            "audit_id" => "Required|Required|Int|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        //如果驳回，验证驳回理由
        if ($paramIn['status'] == enums::APPROVAL_STATUS_REJECTED) {
            $validations['reject_reason'] = "Required|StrLenGeLe:1,50|>>>:" . $this->getTranslation()->_('7124');
        }
        $this->validateCheck($paramIn, $validations);

        //id 和驳回理由转换
        $paramIn['id'] = $paramIn['audit_id'];
        $res = (new VehicleServer($this->lang, $this->timezone))->updateVehicleStatusV2UseLock($paramIn, $userinfo);

        //[3]成功数据返回
        $this->jsonReturn($this->checkReturn($res));
    }

    /**
     * 打卡里程查询【Van快递员新增“修改里程表数”使用】
     * @Return  array
     */
    public function getMileageModifyAction()
    {
        //[1]入参和验证
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;

        $returnArr = (new VehicleServer($this->lang, $this->timezone))->getMileageModifySV2($paramIn, $userinfo);
        $this->jsonReturn($returnArr);
    }

    /**
     * 创建修改里程申请【Van快递员新增“修改里程表数”使用】
     * @Return  array
     */
    public function addMileageModifyAction()
    {
        //[1]入参和验证
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        $validations = [
            "mileage_date"     => "Required|Date|>>>:" . $this->getTranslation()->_('miss_args'),
            "start_kilometres" => "Required|IntGtLt:0,1000000000|>>>:" . $this->getTranslation()->_('miss_args'), //只能输入1-999999999
            "end_kilometres"   => "Required|Required|IntGtLt:0,1000000000|>>>:" . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($paramIn, $validations);

        //申请操作
        $returnArr = (new VehicleServer($this->lang, $this->timezone))->addMileageModifySV2UseLock($paramIn, $userinfo);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
}
