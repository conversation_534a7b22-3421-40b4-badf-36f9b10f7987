<?php

namespace FlashExpress\bi\App\Modules\La\Controllers;

use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\bi\HrStaffItemsModel;
use FlashExpress\bi\App\Modules\La\Server\OvertimeServer;
use Exception;

class OvertimeController extends BaseController
{
    protected $server;
    protected $paramIn;


    /**
     * 新建加班
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function addOvertimeAction()
    {
        try {
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
            $paramIn['userinfo'] = $this->userinfo;
            $validations         = [
                "staff_id" => "Required|Int",
                "reason"   => "Required|StrLenGeLe:1,1000|>>>:".$this->getTranslation()->_('5110'),
            ];
            $this->validateCheck($paramIn, $validations);
            $server    = new OvertimeServer($this->lang, $this->timezone);
            $returnArr = $server->setLockConf(60,true)->addOvertimeV3UseLock($paramIn);
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('addOvertime:'.$e->getMessage(), 'error');
            } else {
                $this->getDI()->get('logger')->write_log('addOvertime:'.$e->getMessage(), 'info');
            }
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        $this->jsonReturn($returnArr);
    }

    /**
     * 审核
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function updateOvertimeAction()
    {
        try {
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];

            $validations = [
                "staff_id"      => "Required|Int",
                "audit_id"      => "Required|Int",
                "reject_reason" => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('1020'),
            ];
            $this->validateCheck($paramIn, $validations);

            $server    = new OvertimeServer($this->lang, $this->timezone, $this->userinfo);
            $returnArr = $server->setLockConf(60,true)->updateOvertimeV3UseLock($paramIn);
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('addOvertime:'.$e->getMessage(), 'error');
            } else {
                $this->getDI()->get('logger')->write_log('addOvertime:'.$e->getMessage(), 'info');
            }
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取加班类型
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getTypeOvertimeAction()
    {
        $paramIn                      = $this->paramIn;
        $paramIn['staff_id']          = $this->userinfo['staff_id'];
        $paramIn['job_title']         = $this->userinfo['job_title'];//id
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['department_id']     = $this->userinfo['department_id'];//fle 部门为子部门id 对应 hr系统 node_department_id
        $server                       = new OvertimeServer($this->lang, $this->timezone);
        $returnArr                    = $server->getTypeOvertime($paramIn, $this->userinfo);

        $this->jsonReturn($returnArr);
    }


    /**
     * 取消加班相关申请 逻辑等同于请假申请撤销
     */
    public function cancelOvertimeAction()
    {
        $paramIn = $this->paramIn;
        if (empty($paramIn['type'])) {
            $this->jsonReturn($this->checkReturn('-1', 'type'));
        }
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id" => "Required|Int",
            "type"     => "Required|Int",
            'audit_id' => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理v
        $server    = new OvertimeServer($this->lang, $this->timezone, $this->userinfo);
        $returnArr = $server->cancel($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

}
