<?php

namespace FlashExpress\bi\App\Modules\La\Controllers;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Modules\La\Server\OsStaffServer;
use Exception;

class OsController extends BaseController
{
    protected $os;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 添加外协员工申请
     * @param int    job_id             外协职位
     * @param string employment_date    雇佣日期
     * @param int    employment_days    雇佣天数
     * @param int    shift_id           班次ID
     * @param int    demend_num         申请人数
     * @param string reason             申请原因
     * @return json
     */
    public function addOsStaffAction()
    {
        //[1]入参校验
        $paramIn                               = $this->paramIn;
        $paramIn['param']                      = $this->userinfo;
        $paramIn['param']['position_category'] = $this->userinfo['positions'];
        $paramIn['param']['store_id']          = $this->userinfo['organization_id'];
        $paramIn['staff_id']                   = $this->userinfo['staff_id'];
        $paramIn['organization_id']            = $this->userinfo['organization_id'];
        $paramIn['organization_type']          = $this->userinfo['organization_type'];
        $logger                                = $this->getDI()->get('logger');

        //[2]数据验证

        if (!isset($paramIn['os_type']) ||
            empty($paramIn['os_type']) ||
            !in_array($paramIn['os_type'], [enums::$os_staff_type['normal'], enums::$os_staff_type['long_term']])) {
            throw new Exception("'os_type' invalid input");
        }

        switch ($paramIn['os_type']) {
            case enums::$os_staff_type['normal']:
                $validation_sub = [
                    "job_id"          => "Required|IntIn:812,13,110,271,111|>>>:" . $this->getTranslation()->_('3008'),
                    "employment_days" => "Required|IntGeLe:1,7",
                    "demend_num"      => "Required|IntGeLe:1,50|>>>:" . "'demend_num' invalid input",
                ];
                break;
            case enums::$os_staff_type['long_term']:
                $validation_sub = [
                    "job_id"          => "Required|IntIn:271,812|>>>:" . $this->getTranslation()->_('3008'),
                    "employment_days" => "Required|IntGeLe:30,365|>>>:" . $this->getTranslation()->_('err_msg_more_days'),
                    "demend_num"      => "Required|IntGeLe:1,50|>>>:" . "'demend_num' invalid input",
                ];
                break;
            default:
                $validation_sub = [];
                break;
        }

        //[3]数据验证
        //短期外协的可选雇用日期为从申请日期的第二天起+7天
        //长期外协的可选雇用日期为从申请日期的第二天起+90日
        $dateFrom = date("Y-m-d", time() + 86400);
        $dateTo   = $paramIn['os_type'] == enums::$os_staff_type['normal']
            ? date("Y-m-d", time() + 8 * 86400)
            : date("Y-m-d", time() + 91 * 86400);

        $validations = [
            "employment_date" => "Required|DateFromTo:{$dateFrom},{$dateTo}|>>>:" . $this->getTranslation()->_('err_msg_invalid_date'),
            "shift_id"        => "Required|Int",
            "reason_type"     => "Required|IntGeLe:0,8",
        ];

        if (in_array($paramIn['reason_type'], [0, 2])) { //离职或其他原因需验证reason字段[原因备注字段]
            $valid       = [
                "reason" => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1019'),
            ];
            $validations = array_merge($validations, $valid);
        }
        $this->validateCheck($this->paramIn, array_merge($validations, $validation_sub));

        //[4]业务处理
        $returnArr = (new OsStaffServer($this->lang, $this->timezone))->addOsStaffUseLock($paramIn);

        //[3]数据返回
        return $this->jsonReturn($returnArr);
    }

    /**
     * 更新加班车
     * @param int       audit_id        审批ID
     * @param int       status          审批状态
     * @param string    reject_reason   驳回原因
     * @param int       approval_data   审批数据
     * @return string
     */
    public function updateOsStaffAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['name']  = $this->userinfo['name'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['param']['position_category']  = $this->userinfo['positions'];
        $paramIn['param']['store_id']  = $this->userinfo['organization_id'];
        $paramIn['userinfo']  = $this->userinfo;
        $logger               = $this->getDI()->get('logger');

        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        $reids_key  = 'lock_updateOsStaff_' . $paramIn['audit_id'];
        try{

            $returnArr = $this->atomicLock(function () use ($paramIn) {
                return  (new OsStaffServer($this->lang, $this->timezone))->updateOsStaff($paramIn);
            }, $reids_key, 5);

            if ($returnArr === false) { //没有获取到锁
                return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
            }

        }catch (\Exception $e) {
            $this->getDI()->get('redis')->delete($reids_key); //释放锁
            if ($e->getCode() == '1000') {
                $logger->write_log("OsController:updateOsStaff:auditId:". $paramIn['audit_id'] . $e->getMessage(), 'info');
            } else {
                $logger->write_log("OsController:updateOsStaff:auditId:". $paramIn['audit_id'] . $e->getMessage(), 'notice');
            }
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        return $this->jsonReturn($returnArr);
    }


    /**
     * 获取班次、申请职位、申请原因列表；所属部门；
     */
    public function getShiftListAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //[2]业务处理
        try{
            $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getRequestList($paramIn);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("OsController:getShiftList:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        return $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $returnArr]]));
    }

    /**
     * 获取可申请职位下拉列表
     */
    public function getOsJobTitleListAction()
    {
        try {
            //[1]传入参数
            $paramIn                = $this->paramIn;
            $paramIn['userinfo']    = $this->userinfo;

            $validations = [
                "type"      => "Required|IntIn:1,2,3",
            ];
            $this->validateCheck($this->paramIn, $validations);

            $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getOsJobTitleList($paramIn);
            return  $this->jsonReturn(self::checkReturn(['data' => $returnArr]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("OsController:shortTermApplyAction:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }
}