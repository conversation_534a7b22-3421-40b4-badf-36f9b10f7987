<?php

namespace FlashExpress\bi\App\Modules\My;

use Phalcon\DiInterface;
use Phalcon\Loader;
use Phalcon\Mvc\ModuleDefinitionInterface;
use Phalcon\Config;


class Module implements ModuleDefinitionInterface
{
    /**
     * Registers an autoloader related to the module
     *
     * @param DiInterface $di
     */
    public function registerAutoloaders(DiInterface $di = null)
    {
        $loader = new Loader();

        $loader->registerNamespaces([
            'FlashExpress\bi\App\Modules\My\Controllers' =>  __DIR__ . '/controllers/',
            'FlashExpress\bi\App\Modules\My\Models'      =>  __DIR__ . '/models/',
            'FlashExpress\bi\App\Modules\My\Server'      =>  __DIR__ . '/server/',
            'FlashExpress\bi\App\Modules\My\library'     =>  __DIR__ . '/library/',
            'FlashExpress\bi\App\Modules\My\Tasks'       =>  __DIR__ . '/tasks/',
            'FlashExpress\bi\App\Modules\My\Repository'       =>  __DIR__ . '/repository/',
            'FlashExpress\bi\App\Modules\My\enums'       =>  __DIR__ . '/enums/',
        ]);
        $loader->register();
    }

    /**
     * Registers services related to the module
     *
     * @param DiInterface $di
     */
    public function registerServices(DiInterface $di)
    {
        $config = $di['config'];
        if (file_exists(__DIR__ . '/config/config.php')) {

            $config = $di['config'];

            $override = new Config(include __DIR__ . '/config/config.php');

            if ($config instanceof Config) {
                $config->merge($override);
            } else {
                $config = $override;
            }
        }
    }
}
