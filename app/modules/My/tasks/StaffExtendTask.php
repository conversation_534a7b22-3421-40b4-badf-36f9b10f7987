<?php
/**
 * Created by <PERSON>p<PERSON>tor<PERSON>.
 * User: nick
 * Date: 2019/6/17
 * Time: 下午6:23
 */

namespace FlashExpress\bi\App\Modules\My\Tasks;


use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoExtendMode;
use FlashExpress\bi\App\Models\backyard\LeaveToLieuModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLastYearDaysModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveExtendModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveInLieuModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\StaffWaitingLeaveSendModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Modules\My\library\Enums\VacationEnums;
use FlashExpress\bi\App\Modules\My\Server\Vacation\AnnualServer;
use FlashExpress\bi\App\Modules\My\Server\AttendanceServer;
use FlashExpress\bi\App\Modules\My\Server\AuditServer;
use FlashExpress\bi\App\Modules\My\Server\LeaveServer;
use FlashExpress\bi\App\Repository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffJobLevelLogModel;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\MailServer;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;

use FlashExpress\bi\App\Repository\HcRepository;
use StaffExtendTask as GlobalTask;


//员工扩展任务类
class StaffExtendTask extends GlobalTask {

    const CANCEL_TYPE_CHANGE_DATE = 1;
    const CANCEL_TYPE_CHANG_STATE = 2;


    //每个月 1号凌晨 跑一次 固化上个月额度 马来是每个月固化
    //马来的 每月发放 和 周期失效等 融合一个任务  https://l8bx01gcjr.feishu.cn/docs/doccnlt6MYtheQs3OqBCEjHmqEh
    //新需求 马来改成按天发 还有待离职的 预发放 每天跑当天
    public function freeze_by_levelAction($param)
    {
        //任务锁
        $key   = 'freeze_by_level_lock';
        $redis = $this->getDI()->get("redisLib");
        $rs    = $redis->set($key, 1, ['nx', 'ex' => 10 * 60]);//锁10分钟
        if (!$rs && RUNTIME == 'pro') {
            echo 'task is running';
            return ;
        }
        $today = date('Y-m-d');
        if (!empty($param[0])) {
            $today = $param[0];
        }
        if (!empty($param[1])) {
            $staffParam['staff_info_id'] = (int)$param[1];
        }
        try {
            $staffParam['hire_type'] = [HrStaffInfoModel::HIRE_TYPE_1,HrStaffInfoModel::HIRE_TYPE_2];
            $staffParam['state'] = [HrStaffInfoModel::STATE_1,HrStaffInfoModel::STATE_3];
            $staff_list = $this->annualStaffList($today, $staffParam);

            if (empty($staff_list)) {
                return true;
            }
            $staff_list = array_column($staff_list, null, 'staff_info_id');
            $dep_model = new Repository\DepartmentRepository($this->lang);
            $c_staffs  = $dep_model->get_c_level();

            $annualServer = new AnnualServer($this->lang, $this->timezone);
            $leaveServer = new LeaveServer($this->lang, $this->timezone);
            foreach ($staff_list as $staff_id => $staff_info) {
                if (empty($staff_id)) {
                    continue;
                }
                $flag = $leaveServer->leavePermission($staff_info);
                if(!$flag){
                    echo $staff_info['staff_info_id'].' 非工作所在国家 不发年假';
                    continue;
                }

                //获取当前周期的额度信息
                $cycle_info = $annualServer->get_cycle($staff_info);
                if (empty($cycle_info)) {
                    echo $staff_info['staff_info_id'].'没有入职日期';
                    continue;
                }

                //新增 计算年假日期逻辑 https://flashexpress.feishu.cn/docx/USK1dOhAiod8STxnC0ccJqacn4d
                if(!empty($staff_info['annual_date'])){
                    //清空日期 不累计年假
                    if($staff_info['annual_date'] == HrStaffInfoExtendMode::ANNUAL_STOP_DATE){
                        echo 'base 清空日期 不计算年假 ' . "{$staff_info['staff_info_id']} ". $staff_info['annual_date'] ;
                        continue;
                    }
                    //日期在当天之后 不累计
                    if($staff_info['annual_date'] > $today){
                        echo 'base 没到计算日期 不计算年假 ' . "{$staff_info['staff_info_id']} ". $staff_info['annual_date'];
                        continue;
                    }
                }

                $this->getDI()->get('db')->begin();

                $current_remain = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year = :cycle: and leave_type = :leave_type:',
                    'bind'       => ['staff_id'   => $staff_id,
                                     'cycle'      => $cycle_info['cycle'],
                                     'leave_type' => enums::LEAVE_TYPE_1,
                    ],
                ]);

                //有可能是新入职员工
                if (empty($current_remain)) {
                    $isContinue = true;//需要继续往下走的 用下面的add day 包括新入职和迁移账号
                    if ($cycle_info['cycle'] == 1) {
                        $current_remain = $this->initAnnual($staff_info, $cycle_info['cycle']);
                    } else {
                        $current_remain = $this->initAnnual($staff_info,$cycle_info['cycle']);
                        $this->getDI()->get('logger')->write_log("freeze_by_level {$staff_info['staff_info_id']} 当前周期额度异常 没有当前周期额度信息 有可能是 迁移过来的账号 初始化额度",'info');
                    }
                }

                //任务判定是否跑过 每天只能跑一次
                if (!empty($current_remain) && $current_remain->task_date >= $today && empty($isContinue)) {
                    $this->getDI()->get('db')->commit();
                    continue;
                }

                //新需求 用历史最高职级 不用实时的
                $ext_info = StaffLeaveExtendModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year_at = :cycle:',
                    'bind'       => ['staff_id'   => $staff_info['staff_info_id'],
                                     'leave_type' => enums::LEAVE_TYPE_1,
                                     'cycle'      => $cycle_info['cycle'],
                    ],
                ]);
                if (empty($ext_info)) {
                    $this->getDI()->get('db')->rollback();
                    $this->getDI()->get('logger')->write_log("freeze_by_level {$staff_info['staff_info_id']} 当前周期额度扩展表 异常");
                    continue;
                }

                //与当前职等比较 有可能刚升职
                if ($staff_info['job_title_grade_v2'] > $ext_info->job_title_grade) {
                    $ext_info->job_title_grade = $staff_info['job_title_grade_v2'];
                    $ext_info->update();
                }
                //降职情况 取历史最高 替换当前的 v2
                $staff_info['job_title_grade_v2'] = $ext_info->job_title_grade > $staff_info['job_title_grade_v2'] ? $ext_info->job_title_grade : $staff_info['job_title_grade_v2'];

                $cycle_last_date = date('Y-m-d', strtotime("{$cycle_info['count_day']} -1 day"));

                //应有额度
                $should_day = $annualServer->staff_year_should_days($staff_info);

                //c level
                if (in_array($staff_info['staff_info_id'], $c_staffs)) {
                    $should_day = enums::C_LEVEL_DAYS;
                }
                //合同工
                if ($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_2) {
                    $should_day = enums::UN_NORMAL_STAFF_DAYS;
                }
                //没有自然年概念 都是 365
                $add_day = round($should_day / 365, enums::ROUND_NUM);

                if ($today == $cycle_last_date) {//当天是 当前周期 最后一天 加一天额度 然后初始化 下一年记录 并且当年额度 +1天
                    $this->initAnnual($staff_info, $cycle_info['cycle'] + 1);
                    $add_day += $add_day;//多给一天
                    //c级别 和 19天的 由于5位小数点 四舍五入 舍去了 需要加一天额度
                    if(in_array($should_day, [19,20])){
                        $add_day += $add_day;
                    }
                }

                $current_remain->freeze_days += $add_day;
                $current_remain->days        += $add_day;//加一天额度
                //如果 对应 待离职员工 需要把已经使用的 预发放额度 转移到 额度表 也有可能之前是待离职 后来变在职了 所有人都要查一下
                $wait_flag = $this->check_wait_used($staff_info['staff_info_id'], $today);
                if ($wait_flag) {//预发放使用额度迁移到主表 0.5为单位 固定
                    $current_remain->days       -= 0.5;
                }
                $current_remain->task_date = $today;
                $current_remain->update();

                //周期结算日 新周期 第一天 要做上周期结余 left all days 三坑逻辑
                $last_count = date('Y-m-d', strtotime("{$cycle_info['count_day']} -1 year"));
                if ($today == $last_count) {
                    //已经用了的坑
                    $has_applied = $annualServer->leaveDaysByDate($staff_info['staff_info_id'],
                        $cycle_info['cycle'] - 1, $last_count);
                    //去年结余 不能超过3天（包括已经用的坑）
                    $last_remain = StaffLeaveRemainDaysModel::findFirst([
                        'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :cycle:',
                        'bind'       => ['staff_id'   => $staff_info['staff_info_id'],
                                         'leave_type' => enums::LEAVE_TYPE_1,
                                         'cycle'      => $cycle_info['cycle'] - 1,
                        ],
                    ]);
                    if (empty($last_remain)) {
                        $this->getDI()->get('db')->commit();
                        $this->logger->write_log("freeze_by_level {$today} {$staff_id} 新周期第一天 无上周期记录 ", 'notice');
                        continue;
                    }

                    $log_before        = $last_remain->days;
                    $last_remain->days = half_num($last_remain->days);
                    //计算上个周期 结余
                    $hole      = ($annualServer::HOLE - $has_applied) > 0 ? ($annualServer::HOLE - $has_applied) : 0;
                    $left_days = 0;
                    if ($hole >= 0 && $last_remain->days >= 0) {
                        $left_days = $last_remain->days > $hole ? $hole : $last_remain->days;
                    }
                    $last_remain->days = $left_days;
                    $last_remain->update();
                    $this->getDI()->get('logger')->write_log("freeze_by_level {$staff_info['staff_info_id']} 固化上个周期 天数剩余之前 {$log_before} 之后 {$left_days}",
                        'info');

                    $last_extend = StaffLeaveExtendModel::findFirst([
                        'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year_at = :cycle:',
                        'bind'       => ['staff_id'   => $staff_info['staff_info_id'],
                                         'leave_type' => enums::LEAVE_TYPE_1,
                                         'cycle'      => $cycle_info['cycle'] - 1,
                        ],
                    ]);
                    if (!empty($last_extend)) {
                        $last_extend->left_all_days = $left_days;
                        $last_extend->update();
                    }
                }

                $this->getDI()->get('db')->commit();
                $this->logger->write_log("freeze_by_level_{$staff_info['staff_info_id']}_{$cycle_info['cycle']} {$today} 增加额度 {$add_day} ", 'info');
            }

            $redis->delete($key);
        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log("freeze_by_level {$today} 任务数据失败 ".$e->getTraceAsString());
            die('freeze_by_level 任务异常 '.$e->getTraceAsString());
        }
    }

    //每天递增任务 判定 是否有预使用的额度 迁移到 正式表 作为真正使用额度记录
    protected function check_wait_used($staff_id,$date){
        if (empty($staff_id) || empty($date)) {
            return false;
        }

        $check_info = StaffWaitingLeaveSendModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and date_at = :date:',
            'bind'       => ['staff_id' => $staff_id, 'date' => $date],
        ]);

        if (empty($check_info)) {
            return false;
        }

        if($check_info->is_used == 0){
            $this->getDI()->get('logger')->write_log("删除 [未使用] 待离职预发放额度 ".json_encode($check_info->toArray()) ,'info');
            $check_info->delete();
            return false;
        }

        $this->getDI()->get('logger')->write_log("删除 [已使用] 待离职预发放额度 ".json_encode($check_info->toArray()) ,'info');
        $check_info->delete();
        return true;

    }

    //新入职员工 或者是新周期员工 初始化 年假相关发放调用
    public function initAnnual($staff_info,$cycle){
        $remain = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year = :cycle: and leave_type = :leave_type:',
            'bind' => ['staff_id' => $staff_info['staff_info_id'],'cycle' => $cycle, 'leave_type' => enums::LEAVE_TYPE_1]
        ]);
        if(empty($remain)){
            $remain = new StaffLeaveRemainDaysModel();
            //新增一条 remain
            $row['staff_info_id'] = $staff_info['staff_info_id'];
            $row['task_date'] = date('Y-m-d');
            $row['leave_type'] = enums::LEAVE_TYPE_1;
            $row['year'] = $cycle;
            $remain->create($row);
        }

        $ext_info = StaffLeaveExtendModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year_at = :cycle:',
            'bind' => ['staff_id' => $staff_info['staff_info_id'], 'leave_type' => enums::LEAVE_TYPE_1, 'cycle' => $cycle]
        ]);
        //新增一条 ext
        if(empty($ext_info)){
            $extModel = new StaffLeaveExtendModel();
            $ext['staff_info_id'] = $staff_info['staff_info_id'];
            $ext['leave_type'] = enums::LEAVE_TYPE_1;
            $ext['year_at'] = $cycle;
            $ext['job_title_level'] = $staff_info['job_title_level'];
            $ext['job_title_grade'] = $staff_info['job_title_grade_v2'];

            $extModel->create($ext);
        }

        return StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year = :cycle: and leave_type = :leave_type:',
            'bind' => ['staff_id' => $staff_info['staff_info_id'],'cycle' => $cycle, 'leave_type' => enums::LEAVE_TYPE_1]
        ]);

    }



    //初始化staff_days_freeze 上线时候 跑一次
    // 新版本 再一次 初始化按天发到今天 https://flashexpress.feishu.cn/docx/doxcnMBEimw38UPZrCcdhZzfk20
    public function freeze_firstAction($param){
        ini_set('memory_limit', '-1');
        try{
            $staff_id = '';
            if(!empty($param[0]))
                $staff_id = $param[0];

            $today = date('Y-m-d');
            $condition = " state in (1,3) and is_sub_staff = 0 and hire_type in (1,2) and hire_date < :today:";
            $bind['today'] = $today;
            if(!empty($staff_id)){
                $condition .= " and staff_info_id = :staff_id:";
                $bind['staff_id'] = $staff_id;
            }

            $staff_list = HrStaffInfoModel::find([
                'conditions' => $condition,
                'columns' => 'staff_info_id,job_title_level,hire_date,job_title_grade_v2,wait_leave_state,hire_type',
                'bind' => $bind
            ])->toArray();

            if(empty($staff_list))
                die('没有员工数据');

            $this->getDI()->get('db')->begin();

            $audit_re = new AuditRepository($this->lang);
            $last_model = new StaffLeaveRemainDaysModel();
            $ext_model = new StaffLeaveExtendModel();
            $annualServer = new AnnualServer($this->lang,$this->timezone);


            $dep_model = new Repository\DepartmentRepository($this->lang);
            $c_staffs = $dep_model->get_c_level();

            foreach ($staff_list as $staff_info){
                $staff_id = $staff_info['staff_info_id'];
                //获取 最高职级
                $job_log = StaffJobLevelLogModel::findFirst([
                    'columns' => 'max(job_title_grade) job_title_grade',
                    'conditions' => 'staff_info_id = :staff_id:',
                    'bind' => ['staff_id' => $staff_id]
                ]);
                $log_grade = empty($job_log) ? 0 : intval($job_log->job_title_grade);
                $current_grade = intval($staff_info['job_title_grade_v2']);
                $max_grade = $log_grade > $current_grade ? $log_grade : $current_grade;

                //周期信息
                $cycle_info = $annualServer->get_cycle($staff_info);
                if(empty($cycle_info)){
                    $this->getDI()->get('logger')->write_log("年假额度 {$staff_info['staff_info_id']} 没有入职日期" ,'info');
                    continue;
                }

                //存在 上个周期 存到 remain  和 extend
                if($cycle_info['cycle'] > 1){
                    $last_info = StaffLastYearDaysModel::findFirst([
                        'conditions' => 'staff_info_id = :staff_id: and year_time = :cycle:',
                        'bind' => ['staff_id' => $staff_id, 'cycle' => $cycle_info['cycle'] - 1],
                    ]);
                    if(!empty($last_info)){
                        $last_row['staff_info_id'] = $staff_id;
                        $last_row['leave_type'] = enums::LEAVE_TYPE_1;
                        $last_row['year'] = $cycle_info['cycle'] - 1;
                        $last_row['freeze_days'] = $last_info->left_all_days;
                        $last_row['days'] = $last_info->left_days;
                        $last_row['leave_days'] = $last_info->left_all_days - $last_info->left_days;
                        $last_clone = clone $last_model;
                        $last_clone->create($last_row);

                        $extend_row['staff_info_id'] = $staff_id;
                        $extend_row['leave_type'] = enums::LEAVE_TYPE_1;
                        $extend_row['year_at'] = $cycle_info['cycle'] - 1;
                        $extend_row['left_all_days'] = $last_info->left_all_days;
                        $extend_row['job_title_level'] = $staff_info['job_title_level'];
                        $extend_row['job_title_grade'] = $max_grade;

                        $ext_clone = clone $ext_model;
                        $ext_clone->create($extend_row);
                    }
                }

                //当前周期的 remain 和 extend 重新算 应有额度 截止到当天 该员工当前周期 有多少额度
                $cycle_start_tmp = strtotime("{$cycle_info['count_day']} -1 year");
                $hire_tmp = strtotime("{$staff_info['hire_date']}");
                $start_date = ($hire_tmp > $cycle_start_tmp) ? $hire_tmp : $cycle_start_tmp;//谁最近取谁

                //算天数
                $count_days = (strtotime("{$today}") - $start_date) / (24 * 3600);
                $count_days += 1;//1号 到3 号 应该算3天

                $should_days = $annualServer->staff_year_should_days($staff_info);
                //c level
                if(in_array($staff_info['staff_info_id'],$c_staffs)){
                    $should_days = enums::C_LEVEL_DAYS;
                }


                //合同工
                if($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_2){
                    $should_days = enums::UN_NORMAL_STAFF_DAYS;
                }

                $days = round($should_days / 365,enums::ROUND_NUM);
                $days = $count_days * $days;

                //获取 本周年 已申请的额度
                $num = $audit_re->get_year_applied_days($staff_id,$cycle_info['cycle']);
                $num = floatval($num);

                $this_row['staff_info_id'] = $staff_id;
                $this_row['leave_type'] = enums::LEAVE_TYPE_1;
                $this_row['year'] = $cycle_info['cycle'];
                $this_row['freeze_days'] = $days;
                $this_row['days'] = $days - $num;
                $this_row['leave_days'] = $num;

                $this_clone = clone $last_model;
                $this_clone->create($this_row);

                $this_extend_row['staff_info_id'] = $staff_id;
                $this_extend_row['leave_type'] = enums::LEAVE_TYPE_1;
                $this_extend_row['year_at'] = $cycle_info['cycle'];
                $this_extend_row['left_all_days'] = 0;//每周年第一天 更新扩展表这个字段
                $this_extend_row['job_title_level'] = $staff_info['job_title_level'];
                $this_extend_row['job_title_grade'] = $max_grade;
                $this_extend = clone $ext_model;
                $this_extend->create($this_extend_row);

            }
            $this->getDI()->get('db')->commit();
        }catch (\Exception $e){
            $this->getDI()->get('db')->rollback();
            die('初始化额度报错 ' .$e->getMessage());
        }

    }


    /** 预发放 待离职员工 任务 每天 凌晨 跑前一天 的
     *
     * SELECT `s`.`staff_info_id` AS `staff_info_id`, `s`.`name` AS `staff_name`, `s`.`job_title` AS `job_title`, `j`.`job_name` AS `job_title_name`, `s`.`hire_date` AS `hire_date`, `s`.`job_title_grade_v2` AS `job_title_grade_v2`, `s`.`wait_leave_state` AS `wait_leave_state`, `s`.`job_title_level` AS `job_title_level`, `s`.`leave_date` AS `leave_date`
     * FROM `hr_staff_info` AS `s`  LEFT JOIN `hr_job_title` AS `j` ON `s`.`job_title` = `j`.`id`
     * WHERE ((((`s`.`state` = 1) AND (`s`.`wait_leave_state` = 1))
     * AND (`s`.`is_sub_staff` = 0))
     * AND (`s`.`hire_date` <= '2022-11-10'))
     * AND (`s`.`hire_type` IN (1,2))
     *
     * @param $param
     */
    public function waiting_sendAction()
    {
        $today         = date('Y-m-d');
        $bind['today'] = $today;

        //获取员工详细详情
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.staff_info_id,s.name as staff_name,s.job_title,j.job_name as job_title_name,s.hire_date,s.job_title_grade_v2,s.wait_leave_state,s.job_title_level,s.leave_date,s.working_country');
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->leftjoin(HrJobTitleModel::class, 's.job_title = j.id', 'j');
        $builder->andWhere('state = :state:', ['state' => HrStaffInfoModel::STATE_1]);
        $builder->andWhere('wait_leave_state = :wait_leave_state:',
            ['wait_leave_state' => HrStaffInfoModel::WAITING_LEAVE]);
        $builder->andWhere('is_sub_staff = :is_sub_staff:', ['is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0]);
        $builder->andWhere('hire_date <= :today:', ['today' => $today]);
        $builder->inWhere('hire_type', [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2]);

        $staff_list = $builder->getQuery()->execute()->toArray();

        //这些人 是要发放的人
        $staff_ids = array_column($staff_list, 'staff_info_id');

        //清理数据 把非待离职的人 数据都清了 改了逻辑 占用的还要发消息
        $this->clearWaitingData($staff_ids);

        if (empty($staff_ids)) {
            $this->getDI()->get('logger')->write_log("waiting_send 预发放额度 没有待离职员工数据 ", 'info');
            die('预发放额度 没有待离职员工数据');
        }

        $annualServer = new AnnualServer($this->lang, $this->timezone);
        $model        = new StaffWaitingLeaveSendModel();

        //如果 数据 有了 就不动
        $exist = StaffWaitingLeaveSendModel::find([
            'columns'    => "concat(staff_info_id,'_',date_at) as unique,staff_info_id,leave_date,is_last,is_used",
            'conditions' => 'staff_info_id in ({ids:array}) ',
            'bind'       => ['ids' => $staff_ids],
        ])->toArray();

        if (!empty($exist)) {
            //获取固化过的员工离职日期
            $batch_staff_leave_date = array_column($exist, 'leave_date', 'staff_info_id');
            //员工已经存在的 预发放额度日期
            $exist = array_column($exist, null, 'unique');
        }

        //获取对应语言环境 只有 中文和英文
        $hcRe         = new HcRepository($this->timezone);
        $staffAccount = $hcRe->getStaffAcceptLang($staff_ids);
        $langArr      = [];
        if (!empty($staffAccount)) {
            foreach ($staffAccount as $account) {
                $l                                  = strtolower(substr($account['accept_language'], 0, 2));
                $langArr[$account['staff_info_id']] = $l;
            }
        }

        foreach ($staff_list as $staff) {
            $isDelete = false;//如果是 删除重算的员工 不需要用到 exist 去判定
            //改为 离职日前一天了
            $leave_date = date('Y-m-d', strtotime("{$staff['leave_date']} -1 day"));
            //格式化一下 离职日期 它带时分秒
            $staff['leave_date'] = date('Y-m-d', strtotime($staff['leave_date']));
            //新场景 如果当前员工离职日 跟固化离职日期不同 并且 晚于固化离职日 需要把is last 并且已经使用的 请假记录 撤销
            if (!empty($batch_staff_leave_date[$staff['staff_info_id']]) && $staff['leave_date'] != $batch_staff_leave_date[$staff['staff_info_id']]) {
                $isDelete = true;
                //员工语言环境
                $staffLang        = $langArr[$staff['staff_info_id']] ?? 'en';
                $msgParam['lang'] = $staffLang;
                //原离职日期
                $msgParam['old_date'] = $batch_staff_leave_date[$staff['staff_info_id']];
                //最新当前离职日期
                $msgParam['new_date'] = date('Y-m-d', strtotime($staff['leave_date']));
                $this->waitingCancel($staff, $msgParam, self::CANCEL_TYPE_CHANGE_DATE);
            }
            //历史最高职等
            $ext_info = StaffLeaveExtendModel::findFirst([
                'columns'    => 'max(job_title_grade) as job_title_grade',
                'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type:',
                'bind'       => ['staff_id' => $staff['staff_info_id'], 'leave_type' => enums::LEAVE_TYPE_1],
            ]);
            if (!empty($ext_info) && $staff['job_title_grade_v2'] < $ext_info->job_title_grade) {
                $staff['job_title_grade_v2'] = $ext_info->job_title_grade;
            }

            $should_day = $annualServer->staff_year_should_days($staff);
            $cycle_info = $annualServer->get_cycle($staff);
            if (empty($cycle_info)) {
                $this->getDI()->get('logger')->write_log("waiting_send 预发放额度 {$staff['staff_info_id']} 没有入职日期", 'info');
                continue;
            }
            $have_info = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
                'columns'    => 'staff_info_id,days,leave_days,freeze_days',
                'bind'       => [
                    'staff_id'   => $staff['staff_info_id'],
                    'leave_type' => enums::LEAVE_TYPE_1,
                    'year_at'    => $cycle_info['cycle'],
                ],
            ]);

            //当前 已经固化的值
            $start = 0;
            if (!empty($have_info)) {
                $start = $have_info->freeze_days;
            }

            $limit     = intval($start / 0.5) * 0.5 + 0.5;//第一个 0。5区间值
            $count_day = $cycle_info['count_day'];

            //每天发放的单位 错位要加一天
            $step     = date('Y-m-d', strtotime("{$today} +1 day"));
            //如果已经在最后工作日之后 不操作
            if($today == $leave_date){
                $step = $leave_date;
            }

            $one_unit = round($should_day / 365, enums::ROUND_NUM);
            //每到 n。5 加一条记录
            while ($step < $leave_date) {
                $u_key = "{$staff['staff_info_id']}_{$step}";
                $start += $one_unit;
                //如果 加到 下个周期了 start 清零
                if ($step == $count_day) {
                    $start = $one_unit;
                    $limit = 0.5;
                    $step  = date('Y-m-d', strtotime("{$step} +1 day"));
                    continue;
                }

                if ($start < $limit) {
                    $step = date('Y-m-d', strtotime("{$step} +1 day"));
                    continue;
                }

                if (!empty($exist[$u_key]) && !$isDelete) {
                    $step  = date('Y-m-d', strtotime("{$step} +1 day"));
                    $limit += 0.5;
                    continue;
                }

                //到达 目标节点 入库
                $row['staff_info_id'] = $staff['staff_info_id'];
                $row['date_at']       = $step;
                $row['days']          = 0.5;
                $row['leave_date']    = $staff['leave_date'];
                $row['is_last']       = 0;
                $clone                = clone $model;
                $clone->create($row);

                $step  = date('Y-m-d', strtotime("{$step} +1 day"));
                $limit += 0.5;
            }

            //最后一天 离职日 判定   - 小数部分<0.5，给0天 - 小数部分=0.5，给0.5天 - 小数部分>0.5，给1天
            $start += $one_unit;//离职日那天的额度递增
            //当天离职日 加完额度后判断 是否够发放
            $score = intval($start) + 0.5;

            //最后结算日 再给0。5  符合给1天的条件
            if ($start >= $score || $start >= $limit) {
                //如果 数据 有了 就不动
                $u_key = "{$staff['staff_info_id']}_{$step}";
                if (!empty($exist[$u_key]) && !$isDelete) {
                    continue;
                }

                $row['staff_info_id'] = $staff['staff_info_id'];
                $row['date_at']       = $step;
                $row['days']          = 0.5;
                $row['leave_date']    = $staff['leave_date'];
                $row['is_last']       = 1;
                $clone                = clone $model;
                $clone->create($row);
            }
        }
        echo 'waiting_send 跑完了';
        $this->logger->write_log("waiting_send {$today} 跑完了 ", 'notice');
    }

    /**
     * @param $staffInfo
     * @param $msgParam
     * @param int $sendType 发放消息情况 内容不一样 区分开 1 离职日期变更 2 在职状态变更
     */
    public function waitingCancel($staffInfo, $msgParam, $sendType = 1)
    {
        $staffId     = $staffInfo['staff_info_id'];
        $waitingInfo = StaffWaitingLeaveSendModel::find([
            'conditions' => 'staff_info_id = :staff_id: ',
            'bind'       => ['staff_id' => $staffId],
        ]);

        if (empty($waitingInfo)) {
            return;
        }

        $auditIds = [];
        foreach ($waitingInfo as $item) {
            if ($item->is_used == 0) {
                continue;
            }

            if (empty($item->audit_id)) {
                $this->logger->write_log("waitingCancel 待离职发放任务 没有审批id {$staffId} {$item->date_at} ");
                continue;
            }
            //使用过的 audit id 拿到
            $auditIds[] = $item->audit_id;
        }

        //没有需要撤销的请假 直接返回
        if (empty($auditIds)) {
            $waitingInfo->delete();
            return;
        }

        //撤销已经使用的 预发放的年假
        $audit_server = new AuditServer($this->lang, $this->timezone);
        $auditIds = array_unique($auditIds);
        foreach ($auditIds as $auditId) {
            $leaveInfo = StaffAuditModel::findFirst($auditId);
            if (empty($leaveInfo)) {
                $this->logger->write_log("waitingCancel 待离职发放任务 没有审批记录 {$staffId} {$auditId} ");
                continue;
            }
            //发消息用的内容展示
            $start                   = date('Y-m-d', strtotime($leaveInfo->leave_start_time));
            $end                     = date('Y-m-d', strtotime($leaveInfo->leave_end_time));
            $startType               = $leaveInfo->leave_start_type == StaffAuditLeaveSplitModel::SPLIT_TYPE_1 ? 'am' : 'pm';//1 上午 am  2下午 pm
            $endType                 = $leaveInfo->leave_end_type == StaffAuditLeaveSplitModel::SPLIT_TYPE_1 ? 'am' : 'pm';
            $msgParam['date_list'][] = "{$start} {$startType} ~ {$end} {$endType}";

            $param = [
                'staff_id'      => $staffId,
                'audit_id'      => $auditId,
                'reject_reason' => 'Resignation date has changed, please reapply',
                'status'        => enums::$audit_status['revoked'],
                'is_task'       => 1,
            ];

            $return = $audit_server->auditEditStatus($param);
            if (empty($return['code']) || $return['code'] != 1) {
                $this->logger->write_log("waitingCancel 待离职发放任务 撤销请假 失败 {$staffId} {$auditId}");
            }
            $this->logger->write_log("waitingCancel 删除已使用 {$staffId} {$auditId} 完成 ".json_encode($return), 'info');
        }

        //拼接消息 内容需要的
        $msgParam['staff_info_id'] = $staffInfo['staff_info_id'];
        $msgParam['name']          = $staffInfo['staff_name'];
        $msgParam['job_name']      = $staffInfo['job_title_name'];
        $message_title             = $message_content = '';
        if ($sendType == self::CANCEL_TYPE_CHANGE_DATE) {
            [$message_title, $message_content] = $this->formatCancelMsg($msgParam);
        } else {
            if ($sendType == self::CANCEL_TYPE_CHANG_STATE) {
                [$message_title, $message_content] = $this->formatChangeMsg($msgParam);
            }
        }


        //发个消息给员工 告诉撤销了 先不写 任务调rpc 太慢了 改队列再说
        $kit_param                       = [];
        $kit_param['staff_info_ids_str'] = $staffId;
        $kit_param['staff_users'][]      = ['id' => $staffId];
        $kit_param['message_title']      = $message_title;
        $kit_param['message_content']    = $message_content;
        $kit_param['add_userid']         = 10000;
        $kit_param['category']           = -1; //普通消息
        $bi_rpc                          = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($kit_param);
        $res = $bi_rpc->execute();

        $waitingInfo->delete();
        $this->logger->write_log("waitingCancel 待离职撤销消息 {$staffId} {$sendType} ".json_encode($res), 'info');

        return;
    }

    //清理 状态 变更后的 残留数据 待离职 变为 正常员工了 预发放的数据要清掉 以绝后患
    protected function clearWaitingData($staffIds)
    {
        try {
            //发放表 工号 这表 只有明天到以后的数据 并且是 待离职的员工 当天之前的数据 删除了
            $haveSendStaff = StaffWaitingLeaveSendModel::find([
                'columns' => 'DISTINCT staff_info_id',
            ])->toArray();
            if (!empty($haveSendStaff)) {
                $haveSendStaff = array_column($haveSendStaff, 'staff_info_id');
            }

            //待离职 变为 正常员工 需要删除的员工数据 已经使用的不删除
            $delStaffIds = array_values(array_diff($haveSendStaff, $staffIds));
            if (empty($delStaffIds)) {
                return;
            }

            $this->getDI()->get('logger')->write_log("clearWaitingData 预发放额度残留数据删除 ".json_encode($delStaffIds), 'info');

            //需要 把预发放数据 删除的 用户信息
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('s.staff_info_id,s.name as staff_name,s.state,s.job_title,j.job_name as job_title_name,s.hire_date,s.job_title_grade_v2,s.wait_leave_state,s.job_title_level,s.leave_date');
            $builder->from(['s' => HrStaffInfoModel::class]);
            $builder->leftjoin(HrJobTitleModel::class, 's.job_title = j.id', 'j');
            $builder->inWhere('s.staff_info_id', $delStaffIds);
            $staff_list = $builder->getQuery()->execute()->toArray();

            //获取对应语言环境 只有 中文和英文
            $hcRe         = new HcRepository($this->timezone);
            $staffAccount = $hcRe->getStaffAcceptLang($delStaffIds);
            $langArr      = [];
            if (!empty($staffAccount)) {
                foreach ($staffAccount as $account) {
                    $l                                  = strtolower(substr($account['accept_language'], 0, 2));
                    $langArr[$account['staff_info_id']] = $l;
                }
            }

            $baseServer = new BaseServer($this->lang, $this->timezone);

            //发消息 删除数据
            foreach ($staff_list as $staffInfo) {
                //员工语言环境
                $staffLang = $langArr[$staffInfo['staff_info_id']] ?? 'en';
                $toLang    = $baseServer->getTranslationByLang($staffLang);

                //在职状态的翻译
                $stateText = '';
                if ($staffInfo['state'] == HrStaffInfoModel::STATE_1) {
                    $stateText = $toLang->_('jobtransfer_0021');
                } elseif ($staffInfo['state'] == HrStaffInfoModel::STATE_2) {
                    $stateText = $toLang->_('jobtransfer_0022');
                } elseif ($staffInfo['state'] == HrStaffInfoModel::STATE_3) {
                    $stateText = $toLang->_('jobtransfer_0023');
                }
                $msgParam['lang']       = $staffLang;
                $msgParam['state_text'] = $stateText;
                $this->waitingCancel($staffInfo, $msgParam, self::CANCEL_TYPE_CHANG_STATE);
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("clearWaitingData 预发放额度残留数据删除 异常 ".$e->getTraceAsString());
            //要保证正常业务流程 报警后返回 手动处理
            return;
        }
    }

    //离职日期变更的 撤销消息 翻译key waiting_leave_msg_content
    protected function formatCancelMsg($param)
    {
        $lang = $param['lang'];
        /**
         *   消息标题：您的请假申请已变更！
         * 消息内容：
         * 您好！
         * 姓名：%name%，工号：%staff_info_id%，职位：%job_name%
         * 由于您的离职日期由原来的“%old_date%” 变更为“%new_date%”，所以系统自动撤销以下提前申请的待生效年假，请根据最新离职日期重新申请：
         * %data_arr%
         * 如您有任何问题，请及时联系People Services。
         */
        $baseServer = new BaseServer($lang, $this->timezone);
        $toLang     = $baseServer->getTranslationByLang($lang);

        $title = $toLang->_('waiting_leave_msg_title');

        $contentParam                  = [];
        $contentParam['name']          = $param['name'];
        $contentParam['staff_info_id'] = $param['staff_info_id'];
        $contentParam['job_name']      = $param['job_name'];
        $contentParam['old_date']      = $param['old_date'];
        $contentParam['new_date']      = $param['new_date'];
        $contentParam['data_arr']      = '';
        $annualStr                     = $toLang->_('2003').'('.$toLang->_('paid').')'.' : ';//年假（带薪）：
        if (!empty($param['date_list'])) {
            foreach ($param['date_list'] as $v) {
                $contentParam['data_arr'] .= $annualStr.$v.'</br>';
            }
        }

        $content = $baseServer->getTranslationByLang($lang)->_('waiting_leave_msg_content', $contentParam);
        return [$title, $content];
    }

    protected function formatChangeMsg($param)
    {
        $lang = $param['lang'];
        /**
         *   <div style='font-size: 40px'>您好！</br>  姓名：%name%，工号：%staff_info_id%，职位：%job_name% </br>
         * 由于您的员工状态由原来的“在职（待离职）” 变更为“%state_text%”，所以系统自动撤销以下提前申请的待生效年假，请根据最新离职日期重新申请：</br> %data_arr%
         * </br>如您有任何问题，请及时联系People Services。</div>
         */
        $baseServer = new BaseServer($lang, $this->timezone);
        $toLang     = $baseServer->getTranslationByLang($lang);

        $title                         = $toLang->_('waiting_leave_msg_title');
        $contentParam                  = [];
        $contentParam['name']          = $param['name'];
        $contentParam['staff_info_id'] = $param['staff_info_id'];
        $contentParam['job_name']      = $param['job_name'];
        $contentParam['state_text']    = $param['state_text'];
        $contentParam['data_arr']      = '';
        $annualStr                     = $toLang->_('2003').'('.$toLang->_('paid').')'.' : ';//年假（带薪）：
        if (!empty($param['date_list'])) {
            foreach ($param['date_list'] as $v) {
                $contentParam['data_arr'] .= $annualStr.$v.'</br>';
            }
        }

        $content = $baseServer->getTranslationByLang($lang)->_('waiting_leave_change_content', $contentParam);
        return [$title, $content];
    }


    /** 针对马来 补休假 的变态需求  每天凌晨过后跑前一天的任务
     * "5天班员工，如果第1个休息日遇到了PH，则给一天的补休假
        补休假年底失效"
     * 任务废弃 已经关闭
     * @param $param
     */
    public function freeze_leaveAction($param){
        return ;
//        echo ("----------------------任务开始 \r\n");
//        $date = date("Y-m-d",strtotime("-1 day"));
//        if(!empty($param[0]))
//            $date = $param[0];
//
//        try{
//
//            //获取 5天班员工
//            $staff_data = HrStaffInfoModel::find("formal in (1,4) and state = 1 and is_sub_staff = 0 and week_working_day = 5")->toArray();
//            if(empty($staff_data))
//                die('no staff data');
//
//            $week = date('w', strtotime($date));
//            if(!in_array($week,[5,6])){
//                die('is not weekend');
//            }
//
//            //判断 是周六 并且 是 这个人的ph  5天 type 2 总部 固定 MY06
//            $ph_days = ThailandHolidayModel::find([
//                'conditions' => "type = 2",
//                'columns'    => 'province_code,day,holiday_type'
//            ])->toArray();
//
//            if(empty($ph_days))
//                die('no ph info');
//
//            $ph_info = array();
//            foreach ($ph_days as $p){
//                $ph_info[$p['province_code']][] = $p['day'];
//            }
//
//            $ph_days = array_column($ph_days,'day');
//
//            //如果 第一个休息日  周六 是ph 对应固化表 额度加一天 如果没记录 加一条记录
//            if(!in_array($date,$ph_days))
//                die('is not ph');
//
//            $leave_server = new LeaveServer($this->lang,$this->timezone);
//            foreach ($staff_data as $staff){
//                //判断 这个人 网点对应的ph 如果这个人 任务这边不是ph 跳过
//                $store_code = $staff['sys_store_id'];
//                if($store_code == '-1'){
//                    $store_code = 'MY06';//总部
//                    //如果该员工是总部的网点 则查找该员工的工作所在州
//                    $staffProvince = HrStaffItemsModel::findFirst([
//                        'conditions' => "staff_info_id = :staff_id: and item = 'STAFF_PROVINCE_CODE'",
//                        'bind' => [
//                            'staff_id'  => $staff['staff_info_id'],
//                        ]
//                    ]);
//                    if($staffProvince){
//                        $store_code = $staffProvince->value;
//                    }
//                }
//
//                else
//                    $store_code = strtoupper(substr($staff['sys_store_id'],0,4));
//
//                //如果是 特殊周第一个休息日是周五  第一个休息日不是周五 跳过
//                if(in_array($store_code,AttendanceServer::$special_code) && $week != 5){
//                    continue;
//                }
//
//                //非特殊周 是周六
//                if(!in_array($store_code,AttendanceServer::$special_code) && $week != 6){
//                    continue;
//                }
//
//                $ph = empty($ph_info[$store_code]) ? array() : $ph_info[$store_code];
//                if(!in_array($date,$ph))
//                    continue;
//
//                $leave_server->add_remain_days($staff['staff_info_id'],1,$date,2);
//                $this->logger->write_log("freeze_leave 补休 {$staff['staff_info_id']} 加一天" ,'info');
//                continue;
//
//            }
//            die('----------------------任务结束');
//        }catch (\Exception $e){
//            die('----------------------任务异常 '.$e->getMessage());
//        }

    }


    /**
     * 对马来调休假拆分
     * 将现在马来调休假拆分为以0.5天为单位的数据
     *
     * 只执行一次
     * @param $param
     */
    public function move_leave_in_lieuAction($param)
    {
        $staff_id = 0;
        if(!empty($param[0]))
            $staff_id = intval($param[0]);

        $condition = "leave_type = 13";
        if(!empty($staff_id))
            $condition .= " and staff_info_id = {$staff_id}";
        //查询全部待拆分的数据
        $leaveDays = StaffLeaveRemainDaysModel::find([
            'conditions' => $condition,
            'columns' => "staff_info_id,days,leave_days",
        ])->toArray();

        $nowDate = date("Y-m-d", time());
        $invalid_date = date('Y-m-d',strtotime('+90 day'));
        $model = new StaffLeaveInLieuModel();

        foreach ($leaveDays as $item) {
            $days = $item['days'];//额度总数
            $leave_days = $item['leave_days'];//已经使用的 标记状态为 2
            for($i = $days; $i > 0; $i -= 0.5){
                $clone = clone $model;
                $row['staff_info_id'] = $item['staff_info_id'];
                $row['date_at'] = $nowDate;
                $row['invalid_date'] = $invalid_date;
                $row['days'] = 0.5;
                $row['data_source'] = 3;
                $row['operator_id'] = 10000;
                $row['state'] = 2;
                if($leave_days <= 0)
                    $row['state'] = 1;

                $days -= 0.5;
                $leave_days -= 0.5;
                $clone->create($row);
            }

        }

    }

    //工具 额外增加 补休假 工号 天数 日期（要算失效日期）
    public function lieu_toolAction($param){
        $staff_id = intval($param[0]);
        $days = intval($param[1]);
        $date = date('Y-m-d',strtotime($param[2]));
        $leave_server = new LeaveServer($this->lang,$this->timezone);

        $flag = $leave_server->add_remain_days($staff_id,$days,$date,3);

        var_dump($flag);



    }



    //每天 跑前 30 天 离职的员工 根据离职日期 计算 离职员工的 年假 额度 剩余 计算薪资用 staff_leave_for_dimission
    //因为 每天离职的员工的 离职日期 不一定是 当天 所以往前推30天 产品定的
    //！！ 不需要初始化数据
    public function leave_num_for_dimissionAction(){
        //马来 年假没请 不给钱了 所以要停了
        return;

    }


    //每天 早上8点 跑任务 发邮件 https://flashexpress.feishu.cn/docx/doxcnvERf8taBNNjiDNPtmHReGe
    public function annualMailAction()
    {
        //任务停
        if (date('Y-m-d') >= '2023-01-01') {
            $this->getDI()->get('logger')->write_log("annualMailAction 这个任务该停了不用跑了");
            die('快停了吧');
        }

        $mailList = SettingEnvModel::findFirst("code = 'annual_to_lieu'");
        if (empty($mailList)) {
            die('邮箱没配置');
        }

        $endDate = date('Y-m-d 00:00:00', strtotime('-1 year'));
//        $startDate = date('Y-m-d',strtotime('-18 month'));
        $startDate = '2021-01-01 00:00:00';

        //查询 符合条件员工 - 2021-01-01到2021-12-31期间入职且依然在职或待离职的员工 该需求了 开始时间 是当前时间 - 18个月 又改了 还是按1月1号
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.staff_info_id,s.name,s.hire_date,s.job_title_grade_v2,s.sys_store_id,j.job_name,d.name dep_name,store.name store_name');
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrJobTitleModel::class, 'j.id = s.job_title and j.status = 1', 'j');
        $builder->leftJoin(SysDepartmentModel::class, 'd.id = s.node_department_id and d.deleted = 0', 'd');
        $builder->leftJoin(SysStoreModel::class, 'store.id = s.sys_store_id and store.state = 1', 'store');

        $builder->betweenWhere('s.hire_date', $startDate, $endDate);
        $builder->andWhere('s.state = 1 and formal = 1 and hire_type in (1,2)');


        $staffData = $builder->getQuery()->execute()->toArray();

        if (empty($staffData)) {
            die('没员工数据');
        }

        //查询已经发放过的员工
        $existStaff = LeaveToLieuModel::find([
            'columns' => 'staff_info_id',
        ])->toArray();

        if (!empty($existStaff)) {
            $existStaff = array_column($existStaff, 'staff_info_id');
        }

        $leaveServer  = new LeaveServer($this->lang, $this->timezone);
        $annualServer = new AnnualServer($this->lang, $this->timezone);
        $taskDate     = date('Y-m-d');
        $excelData    = [];
        $model        = new LeaveToLieuModel();

        foreach ($staffData as $staff) {
            if (in_array($staff['staff_info_id'], $existStaff)) {
                continue;
            }

            $insert = [];
            //获取周期
            $cycleInfo = $annualServer->get_cycle($staff);

            $remains = StaffLeaveRemainDaysModel::find([
                'conditions' => 'staff_info_id = :staff_id: and year in ({cycle:array})',
                'bind'       => [
                    'staff_id' => $staff['staff_info_id'],
                    'cycle'    => [$cycleInfo['cycle'], $cycleInfo['cycle'] - 1],
                ],
            ])->toArray();
            $remains = empty($remains) ? [] : array_column($remains, 'days', 'year');

            //新改动 不按创建时间
            $start = date('Y-m-d',strtotime($staff['hire_date']));
            $end = date('Y-m-d', strtotime("{$staff['hire_date']} +1 year"));
            $applied = $annualServer->leaveDaysBetween($staff['staff_info_id'],$start,$end,enums::LEAVE_TYPE_1);


            //看有没有补发天数
            $shouldDay = $leaveServer->staff_year_should_days($staff);
            $left      = $shouldDay - $applied - AnnualServer::HOLE;
            if ($left <= 0) {
                $this->getDI()->get('logger')->write_log("annualMailAction 没有剩余 {$staff['staff_info_id']} {$shouldDay} {$applied}",
                    'info');
                continue;
            }

            //给每个人 发补休假 然后入库
            $flag = $leaveServer->add_remain_days($staff['staff_info_id'], $left, $taskDate, 3);
            $this->getDI()->get('logger')->write_log("annualMailAction 年假剩余补休 {$flag} {$staff['staff_info_id']} {$left} {$shouldDay} {$applied}",
                'info');

            //入库数据
            $insert['staff_info_id']     = $staff['staff_info_id'];
            $insert['task_date']         = $taskDate;
            $insert['lieu_days']         = $left;
            $insert['add_days']          = $left;
            $insert['job_name']          = $staff['job_name'];
            $insert['department_name']   = $staff['dep_name'];
            $insert['organization_name'] = $staff['sys_store_id'] == '-1' ? enums::HEAD_OFFICE : $staff['store_name'];
            $clone                       = clone $model;
            $clone->create($insert);

            //剩余额度相加用
            $lastCycle   = $cycleInfo['cycle'] - 1;
            $currentDays = $remains[$cycleInfo['cycle']] ?? 0;
            $lastDays    = $remains[$lastCycle] ?? 0;
            //失效 算0
            if ($taskDate >= $cycleInfo['invalid_day']) {
                $lastDays = 0;
            }

            //excel data
            $row['staff_info_id'] = $staff['staff_info_id'];
            $row['name']          = $staff['name'];
            $row['left']          = half_num($currentDays) + half_num($lastDays);//今年剩余 + 去年剩余
            $row['lieu_days']     = $left;
            $row['job_name']      = $staff['job_name'] ?? '';
            $row['dep_name']      = $staff['dep_name'] ?? '';
            $row['store_name']    = $staff['sys_store_id'] == '-1' ? enums::HEAD_OFFICE : $staff['store_name'];
            $row['hire_date']     = date('Y-m-d', strtotime($staff['hire_date']));
            $excelData[]          = $row;
        }

        if (empty($excelData)) {
            die('没有要补的数据 ');
        }

        $header = [
            '工号 Staff ID',
            '姓名 Name',
            '当前剩余年假 Current Annual Leave Balance',//截止到取数当天员工剩余年假天数（去年3天+今年5天=6天）
            '增加的补休假天数 Number of Replacement leave credited',
            '职位 Position',
            '所属部门 Department',
            '网点名称 Branch Name',
            '入职时间 Employment Date',
        ];
        //生成excel
        $fileName = 'annual_leave_'.$taskDate.'.xls';

        $excelData = array_map('array_values', $excelData);

        $config = ['path' => sys_get_temp_dir()];
        $excel  = new \Vtiful\Kernel\Excel($config);
        // 此处会自动创建一个工作表
        $fileObject = $excel->fileName($fileName);
        $filePath   = $fileObject->header($header)->data($excelData)->output();

        if (empty($filePath)) {
            $this->getDI()->get('logger')->write_log("annualMailAction 生成路径出错 ");
            die('没生成 excel文件路径');
        }

        //发邮件 附件
        /**
         * <EMAIL>
         * <EMAIL>
         * <EMAIL>
         * <EMAIL>
         * <EMAIL>
         *
         * 邮件与文件标题：2022-MM-DD Supplementary Annual Leave List/2022-MM-DD年假补休名单
         * 邮件正文：
         * The attached excel is the list of 2022-MM-DD supplementary annual leave, please check.
         * 附件表格为2022-MM-DD年假补休名单，请查收。
         */

        $mailList = explode(',', $mailList->set_val);
        $title    = "{$taskDate} Supplementary Annual Leave List/{$taskDate}年假补休名单";
        $content  = "The attached excel is the list of {$taskDate} supplementary annual leave, please check.</br>
                    附件表格为{$taskDate}年假补休名单，请查收。";


        $mailServer = new MailServer();
        $mailFlag   = $mailServer->send_mail($mailList, $title, $content, $filePath, $fileName);

        $this->getDI()->get('logger')->write_log("annualMailAction 发送邮件 {$mailFlag} ", 'info');
    }


    //24年 不能申请的假期类型 需要撤销
    public function cancelLeaveAction($param)
    {
        $auditServer = new AuditServer($this->lang, $this->timezone);
        $leaveTypes  = [10,28,29];//要关闭的类型

        //包含 上面类型的 已经申请的并且 待审批 和审核通过的假期
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('a.audit_id,a.staff_info_id,a.leave_start_time, a.leave_end_time');
        $builder->from(['s' => StaffAuditLeaveSplitModel::class]);
        $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
        $builder->inWhere('a.leave_type ', $leaveTypes);
        $builder->andWhere('a.audit_type = 2');
        $builder->inWhere('a.status', [enums::APPROVAL_STATUS_PENDING, enums::APPROVAL_STATUS_APPROVAL]);
        $builder->andWhere('s.date_at >= :date_at:', ['date_at' => VacationEnums::LEAVE_TYPE_DATE_CLOSE]);
        $builder->groupBy("a.staff_info_id,a.audit_id");
        $data = $builder->getQuery()->execute()->toArray();

//        var_dump($builder->getQuery()->getSql()) ;exit;
        if (empty($data)) {
            die('没有数据 ');
        }

        //调用 撤销方法 跟工具撤销一样
        foreach ($data as $da) {
            $param['is_task']       = 1;
            $param['staff_id']      = $da['staff_info_id'];
            $param['reject_reason'] = $this->getTranslation('en')->_('leave_cancel_reason');
            $param['status']        = enums::$audit_status['revoked'];
            $param['audit_id']      = $da['audit_id'];
            $res = $auditServer->auditEditStatus($param);

            $logStr = $res['code'] == 1 ? '成功' : '失败';
            $this->getDI()->get('logger')->write_log("cancelLeave 系统撤销假期 {$da['audit_id']} {$logStr} ".json_encode($res), 'info');
            $start = date('Y-m-d', strtotime($da['leave_start_time']));
            $end = date('Y-m-d', strtotime($da['leave_end_time']));
            if($res['code'] == 1){
                $id                          = time().$da['staff_info_id'].rand(1000000, 9999999);
                $param['staff_users']        = [$da['staff_info_id']];//数组 多个员工id
                $param['message_title']      = $this->getTranslation('en')->_('leave_cancel_title');
                $param['message_content']    = $this->getTranslation('en')->_('leave_cancel_content', ['start_time' => $start,'end_time' => $end]);
                $param['staff_info_ids_str'] = $da['staff_info_id'];
                $param['id']                 = $id;
                $param['category']           = MessageEnums::MESSAGE_CATEGORY_GET_CONFIRM;

                $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', 'en'));
                $bi_rpc->setParams($param);
                $res = $bi_rpc->execute();
                $this->logger->write_log("cancelLeaveAction {$da['staff_info_id']} " . json_encode($res), 'info');
            }
        }

        echo 'cancelLeaveAction 跑完了';
    }

}