<?php
namespace FlashExpress\bi\App\Modules\My\Tasks;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\CompanyTerminationContractModel;
use FlashExpress\bi\App\Server\ApprovalServer;

;

/**
 * 公司解约个人代理(目前只有马来)
 * Class CompanyTerminationContractTask
 *  php app/cli.php CompanyTerminationContract   timeout_shutdown
 *  php app/cli.php CompanyTerminationContract   send_email
 */
class CompanyTerminationContractTask extends \BaseTask
{
    /**
     * 给员工问题解约员工发送邮件
     * 发送时间到了员工的解约日期当天(离职日期)凌晨 00:40
     */
    public function send_emailAction($params)
    {
        $terminationDate = $params[0] ?? date('Y-m-d');
        $this->info($terminationDate);
        (new \FlashExpress\bi\App\Modules\My\Server\CompanyTerminationContractServer($this->lang,
            $this->timezone))->staffTroubleSendEmail($terminationDate);

    }

    /**
     * 超时关闭 员工问题解约申请，超时时间为申请人提交的解约日期，到了解约日期则超时关闭
     */
    public function timeout_shutdownAction($params)
    {
        $terminationDate = $params[0] ?? date('Y-m-d');
        $find            = CompanyTerminationContractModel::find([
            'conditions' => 'termination_type=:termination_type: and leave_date = :leave_date: and status = :status: ',
            'bind'       => [
                'termination_type' => CompanyTerminationContractModel::TERMINATION_TYPE_IS_STAFF,
                'leave_date'       => $terminationDate,
                'status'           => enums::$audit_status['panding'],
            ],
        ]);
        $approvalServer  = new ApprovalServer($this->lang, $this->timezone);
        foreach ($find as $item) {
            try {
                $result = $approvalServer->timeOut($item->id,
                    AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT);
                echo json_encode([$item->id, $result]) . PHP_EOL;
                $this->logger->write_log('time_out staff_audit ' . json_encode($result), 'info');
            } catch (\Exception $e) {
                $this->logger->write_log("time_out staff_audit $item->id} failed " . $e->getTraceAsString());
            }
        }

        echo 'Done' . PHP_EOL;
    }
}
