<?php
namespace FlashExpress\bi\App\Modules\My\Tasks;

use app\enums\LangEnums;
use Exception;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftV2Model;
use FlashExpress\bi\App\Models\backyard\StaffDaysFreezeModel;
use FlashExpress\bi\App\Models\backyard\StaffLastYearDaysModel;
use FlashExpress\bi\App\Models\backyard\AttendanceDataV2Model;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Modules\My\Server\AttendanceCalendarV2Server;
use FlashExpress\bi\App\Modules\My\Server\AttendanceServer;
use FlashExpress\bi\App\Modules\My\Server\LeaveServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\WhiteListServer;
use JsonRPC\Client;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\WorkflowServer;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;



use App\Country\Tools;
use FlashExpress\bi\App\Server\AuditServer;

class StaffTask extends  \BaseTask{



	public function send_push($param)
	{
		if(empty($param))
			return false;
		$i = 2;

		$logger   = $this->getDi() -> get('logger');
        $fle_rpc = (new ApiClient('bi_rpc','','push_to_staff', $this->lang));

		while ($i) {
			try {
                $fle_rpc->setParams($param);
                $ret = $fle_rpc->execute();

				if ($ret) {
					$logger->write_log('员工号：' . $param['staff_info_id'] . '发送push成功', 'debug');
					break;
				} else {
					$logger->write_log('员工号：' . $param['staff_info_id'] . '发送push失败', 'error');
					$i--;
				}
				//endregion
			} catch (Exception $e) {
				//region 记录日志
				$log    = array(
					'file'  => $e->getFile(),
					'line'  => $e->getLine(),
					'code'  => $e->getCode(),
					'msg'   => $e->getMessage(),
					'trace' => $e->getTraceAsString(),
				);
				$res    = ['request' => $param, 'response' => '', 'exception' => $log];
				$logger = $this->getDi() -> get('logger');
				$logger->write_log(json_encode($res, JSON_UNESCAPED_UNICODE));
				//endregion
				$i--;
			}
		}
		return true;
	}


    public function getMakeUpCardData($staff_id, $start_date, $end_date): array
    {
        $result = [];
        $auditData = StaffAuditModel::find(
            [
                'columns' => "attendance_date,reissue_card_date,attendance_type",
                'conditions' => 'staff_info_id = :staff_info_id: AND attendance_date >= :start_date: AND  attendance_date <= :end_date:  AND status = :status: AND audit_type = :audit_type: AND attendance_type in ({attendance_type:array})  ',
                'bind' => ['staff_info_id' => $staff_id, 'start_date' => $start_date, 'end_date' => $end_date, 'status' =>1,'audit_type'=>1,'attendance_type'=>[1,2,3,4]]
            ]
        )->toArray();

        if (empty($auditData)) {
            return $result;
        }
        foreach ($auditData as $item) {
            $result[$item['attendance_date']][$item['attendance_type']] = $item;
        }
        unset($auditData);
        return $result;
    }

    public function aaaAction(){
        $res = $this->getMakeUpCardData(119981,'2022-12-25','2022-12-28');
        var_dump($res);
    }


    private function getStaffShift($staffIds,$shiftDates): array
    {
        $res = [];
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'ss.staff_info_id',
            'ss.shift_date',
            'se.shift_type',
        ]);
        $builder->from(['ss' => HrStaffShiftV2Model::class]);
        $builder->innerJoin(HrShiftV2ExtendModel::class, 'ss.shift_extend_id = se.id', 'se');
        $builder->where('ss.staff_info_id in ({staff_info_id:array}) AND ss.shift_date in ({shift_date:array})',
            ['staff_info_id' => $staffIds, 'shift_date' => $shiftDates]);
        $shiftInfos = $builder->getQuery()->execute()->toArray();
        if($shiftInfos){
            foreach ($shiftInfos as $item) {
                $res[$item['staff_info_id']][$item['shift_date']] = $item['shift_type'];
            }
        }
        return $res;
    }

    /**
     * 出差打卡数据
     * @param $staffIds
     * @param $checkDate
     * @return array
     */
    private function getBtAttendanceData($staffIds,$checkDate): array
    {
        $staffAuditReissueForBusinessesGrpByStaffId = [];
        $staffAuditReissueForBusinesses = StaffAuditReissueForBusinessModel::find([
            'conditions' => ' staff_info_id in ({staff_ids:array}) and attendance_date in ({dates:array}) ',
            'bind' => [
                'staff_ids' => $staffIds,
                'dates' => $checkDate
            ]
        ])->toArray();
        foreach ($staffAuditReissueForBusinesses as $staffAuditReissueForBusiness) {
            $staffAuditReissueForBusinessesGrpByStaffId[$staffAuditReissueForBusiness['staff_info_id']][$staffAuditReissueForBusiness['attendance_date']][$staffAuditReissueForBusiness['shift_type']] = $staffAuditReissueForBusiness;
        }
        return $staffAuditReissueForBusinessesGrpByStaffId;
    }


    /**
     * 缺卡提醒 v3
     * @param $params
     * @return void
     */
    public function clock_in_push_v3Action($params)
    {
        $date = date('Y-m-d', strtotime("-1 days"));
        if (isset($params[0])) {
            $date = $params[0];
        }
        $inputStaffIds = [];
        if (isset($params[1])) {
            $inputStaffIds = explode(',', $params[1]);
        }

        $page = 1;
        $pagesize = 100;
        $start_date = date('Y-m-d', strtotime($date . ' -1 days'));

        //获取打卡白名单
        $hcm_rpc_result = (new WhiteListServer())->attendanceList(['start_date'=>$start_date,'end_date'=>$date]);
        $attendance_white_list = $hcm_rpc_result['result']['data'] ?? [];
        if (empty($attendance_white_list)) {
            $this->logger->write_log('获取白名单失败!,重新跑任务吧','error');
            exit();
        }



        $check_date = [$date, $start_date];
        $approval_html_url = env('approval_html_url', 'flashbackyard://fe/html?url=');
        $message_scheme    = $approval_html_url.urlencode(env('sign_url', 'https://dev-01-my-backyard-ui.fex.pub').'/#/ReplenishmentCard');

        while (true) {
            $conditions = " stat_date in ({stat_date:array}) and AB != 0 ";
            $bind       = [
                'stat_date' => $check_date,
            ];

            if ($inputStaffIds) {
                $conditions        .= " and staff_info_id in ({staff_ids:array}) ";
                $bind['staff_ids'] = $inputStaffIds;
            }

            $attendanceDatas = AttendanceDataV2Model::find([
                'columns'    => 'staff_info_id, leave_time_type, stat_date, attendance_started_at, attendance_end_at, second_attendance_started_at,second_attendance_end_at, display_data',
                'conditions' => $conditions,
                'bind'       => $bind,
                'offset'     => ($page - 1) * $pagesize,
                'limit'      => $pagesize,

            ])->toArray();

            if (empty($attendanceDatas)) {
                break;
            }

            $staffIds = array_column($attendanceDatas, 'staff_info_id');

            //获取在职员工
            $onlineStaffsInfo = HrStaffInfoModel::find(['conditions' => "staff_info_id in  ({staff_info_id:array}) and state = 1 and hire_type not in ({hire_type:array})",
                                                                                 'bind'       =>['staff_info_id'=> $staffIds,'hire_type' => HrStaffInfoModel::$agentTypeTogether],
                                                                                 'columns'    => 'staff_info_id',
            ])->toArray();
            $onlineStaffs = array_column($onlineStaffsInfo,  'staff_info_id');

            //员工每日班次
            $staffShift = $this->getStaffShift($staffIds, $check_date);

            $staffServer = new StaffServer($this->lang, $this->timezone);
            //语言包
            $staffAccount = $staffServer->getBatchStaffLanguage($staffIds);

            //出差打卡
            $staffAuditReissueForBusinessesGrpByStaffId = $this->getBtAttendanceData($staffIds, $check_date);

            foreach ($attendanceDatas as $attendanceData) {
                if (in_array($attendanceData['staff_info_id'],
                        $attendance_white_list[$attendanceData['stat_date']]['type_paid_locally'])
                    ||
                    in_array($attendanceData['staff_info_id'],
                        $attendance_white_list[$attendanceData['stat_date']]['type_not_paid_locally'])) {
                    //在打卡白名单里
                    continue;
                }

                $t        = $this->getTranslation($staffAccount[$attendanceData['staff_info_id']] ?? getCountryDefaultLang());
                $_content = [];

                //非在职就不提醒了
                if(!in_array($attendanceData['staff_info_id'],$onlineStaffs)){
                    continue;
                }

                //没有班次、跳过
                if(!isset($staffShift[$attendanceData['staff_info_id']]) || !isset($staffShift[$attendanceData['staff_info_id']][$attendanceData['stat_date']])){
                    continue;
                }
                switch ($staffShift[$attendanceData['staff_info_id']][$attendanceData['stat_date']]) {
                    case HrShiftV2ExtendModel::SHIFT_TYPE_ONCE:
                        if (empty($attendanceData['attendance_started_at'])) {
                            if (
                                !isset($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']])
                                ||
                                !isset($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']][$attendanceData['stat_date']][0])
                                ||
                                empty($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']][$attendanceData['stat_date']][0]['start_time'])
                            ) {
                                $_content[] = $t->_('no_record_push_11', ['date' => $attendanceData['stat_date']]);
                            }
                        }
                        if (empty($attendanceData['attendance_end_at'])) {
                            if (
                                !isset($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']])
                                ||
                                !isset($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']][$attendanceData['stat_date']][0])
                                ||
                                empty($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']][$attendanceData['stat_date']][0]['end_time'])
                            ) {
                                $_content[] = $t->_('no_record_push_12', ['date' => $attendanceData['stat_date']]);
                            }
                        }
                        break;
                    case HrShiftV2ExtendModel::SHIFT_TYPE_TWICE:

                        if (empty($attendanceData['attendance_started_at']) && !in_array($attendanceData['leave_time_type'],[1,3])) {
                            if (
                                !isset($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']])
                                ||
                                !isset($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']][$attendanceData['stat_date']][1])
                                ||
                                empty($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']][$attendanceData['stat_date']][1]['start_time'])
                            ) {
                                $_content[] = $t->_('no_record_push_21', ['date' => $attendanceData['stat_date']]);
                            }
                        }
                        if (empty($attendanceData['attendance_end_at']) && !in_array($attendanceData['leave_time_type'],[1,3])) {
                            if (
                                !isset($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']])
                                ||
                                !isset($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']][$attendanceData['stat_date']][1])
                                ||
                                empty($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']][$attendanceData['stat_date']][1]['end_time'])
                            ) {
                                $_content[] = $t->_('no_record_push_22', ['date' => $attendanceData['stat_date']]);
                            }
                        }
                        if (empty($attendanceData['second_attendance_started_at']) && !in_array($attendanceData['leave_time_type'],[2,3])) {
                            if (
                                !isset($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']])
                                ||
                                !isset($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']][$attendanceData['stat_date']][2])
                                ||
                                empty($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']][$attendanceData['stat_date']][2]['start_time'])
                            ) {
                                $_content[] = $t->_('no_record_push_23', ['date' => $attendanceData['stat_date']]);
                            }
                        }
                        if (empty($attendanceData['second_attendance_end_at']) && !in_array($attendanceData['leave_time_type'],[2,3])) {
                            if (
                                !isset($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']])
                                ||
                                !isset($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']][$attendanceData['stat_date']][2])
                                ||
                                empty($staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']][$attendanceData['stat_date']][2]['end_time'])
                            ) {
                                $_content[] = $t->_('no_record_push_24', ['date' => $attendanceData['stat_date']]);
                            }
                        }
                }
                if (!empty($_content)) {
                    foreach ($_content as $c) {
                        $data = [
                            "staff_info_id"   => $attendanceData['staff_info_id'],
                            "src"             => "backyard",
                            "message_title"   =>  $t->_('staff_clock_in_alter_push_title',['staff_info_id'=>$attendanceData['staff_info_id']]),
                            "message_content" => $c,
                            "message_scheme"  => $message_scheme,
                        ];
                        $this->send_push($data);
                        $this->logger->write_log(['push_data' => $data, 'function' => 'clock_in_push'], "info");
                    }
                }
            }
            $page++;

        }

    }

	public function send_message($param){
		if(empty($param))
			return false;
		// 获取当前语言
		$locale = $this->lang;

		// 实例化jsonRPC接口
		$url = env('api_send_sms','http://192.168.0.230:8003/rpc/sms/');
		$client = new Client($url, false);

		$data_params = [['locale' => $locale,'src' => 'bi:客户提示短信'], $param];

		try {

			// 获取Api回调数据
			$result = $client->execute('send', $data_params);
			//region 记录日志

			//追加日志
			$this->logger->write_log(sprintf("[staff task][send_message] rpc request : %s, response : %s", json_encode($data_params), json_encode($result)), "info");

			if(isset($result) && !empty($result)){
				return true;
			}

			if(isset($result['error']) && !empty($result['error'])){
				$logger = $this->getDI()->get('logger');
				$logger->write_log('client send rpc fail:'.$result['error']['message'], 'error');
				return false;
			}
			//endregion
		} catch (Exception $e) {

			//region 记录日志
			$log = array(
				'file' => $e->getFile(),
				'line' => $e->getLine(),
				'code' => $e->getCode(),
				'msg' => $e->getMessage(),
				'trace' => $e->getTraceAsString(),
			);
			$res = ['request' => $data_params, 'response' => '', 'exception' => $log];
			$logger = parent::$DI->get('logger');
			$logger->write_log(json_encode($res, JSON_UNESCAPED_UNICODE), 'debug');
			//endregion
		}
		return false;
	}


    //上线前 跑一下数据 做参考 数据表 用完就没用了  leave_for_data
    public function leaveDataAction(){
        //获取 所有员工
        $condition = " state in (1,3) and is_sub_staff = 0 and hire_type in (1,2) and hire_date < '" . date('Y-m-d 00:00:00') . "'";
        if(!empty($staff_id))
            $condition .= " and staff_info_id = {$staff_id}";

        $staff_list = HrStaffInfoModel::find([
            'conditions' => $condition,
            'columns' => 'staff_info_id,hire_date,job_title_level,hire_date,job_title_grade_v2'
        ])->toArray();

        $today = date('Y-m-d');

        if(empty($staff_list))
            die('没员工信息');

        //职等 对应 额度
        $grade_array = array(
            'low' => 8,
            'middle' => 12,
            'up' => 14,
        );
        $staff_ids = array_column($staff_list,'staff_info_id');

        //历史周期
        $last_data = StaffLastYearDaysModel::find([
            'columns' => "concat(staff_info_id,'_',year_time) as unique_key,got_days,left_days,left_all_days",
            'conditions' => 'staff_info_id in ({ids:array}) and year_time < 100',
            'bind' => ['ids' => $staff_ids]
        ])->toArray();

        if(!empty($last_data)){
            $last_data = array_column($last_data,null,'unique_key');
        }

        //当前周期
        $this_data = StaffDaysFreezeModel::find([
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind' => ['ids' => $staff_ids]
        ])->toArray();

        if(!empty($this_data)){
            $this_data = array_column($this_data,null,'staff_info_id');
        }

        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $auditRe = new AuditRepository($this->lang);
        foreach ($staff_list as $staff){
            //获取 该员工的 历史周期
            $cycle_info = $leave_server->get_cycle($staff);
            $new_cycle_info = $leave_server->get_new_cycle($staff);

            //职等额度
            if($staff['job_title_grade_v2'] <= 12)
                $grade_days = $grade_array['low'];
            else if($staff['job_title_grade_v2'] > 12 && $staff['job_title_grade_v2'] <= 14)
                $grade_days = $grade_array['middle'];
            else
                $grade_days = $grade_array['up'];

            $all_days = $leave_server->staff_year_should_days($staff);//总额度 包括 满周年
            $add_days = $all_days - $grade_days;//满周年额度
            $flag = $today < $cycle_info['invalid_day'];

            //每天递增 额度
            $stepDays = round($all_days / 365, enums::ROUND_NUM);
            //当前周期 第一天到今天有多少天
            $countDays = (strtotime("{$today} +1 day") - strtotime("{$new_cycle_info['count_day']} -1 year")) / (24 * 3600);

            //已经使用的额度 目前 马来还没固化 需要算一下
            $usedDays = $auditRe->get_year_applied_days($staff['staff_info_id'],$cycle_info['cycle']);
            $newUseDays = $usedDays = $usedDays ?? 0;
            if($new_cycle_info['cycle'] > $cycle_info['cycle'])
                $newUseDays = 0;

            if(!empty($this_data[$staff['staff_info_id']])){
                //入库当前 周期
                $row['staff_info_id'] = $staff['staff_info_id'];
                $row['cycle'] = $cycle_info['cycle'];
                $row['days'] = round($countDays * $stepDays,enums::ROUND_NUM);//新规则 递增到今天有多少 0。5 取整
                $row['leave_days'] = $newUseDays;//当前周期 已经用的天数
                $row['old_days'] = round($this_data[$staff['staff_info_id']]['days'] - $usedDays,enums::ROUND_NUM);
                $row['old_leave_days'] = $usedDays;
                $row['level_days'] = $grade_days;//职等 对应天数
                $row['add_days'] = $add_days;
                $row['is_effective'] = intval($flag);//当前是否在有效期
                $this->getDI()->get('db')->insertAsDict(
                    'leave_for_data', $row
                );
            }

            //统计 历史周期
            if($cycle_info['cycle'] > 1){
                for($i = $cycle_info['cycle'] - 1; $i > 0; $i--){
                    $k = "{$staff['staff_info_id']}_{$i}";
                    if(empty($last_data[$k]))
                        continue;

                    //上个周期 开始时间
                    $m = $cycle_info['cycle'] - $i;
                    $before_date = date('Y-m', strtotime("{$cycle_info['invalid_day']} -{$m} year"));

                    //历史周期 的应有额度
                    $all_days = $leave_server->staff_year_should_days($staff,$before_date);//总额度 包括 满周年

                    $row['staff_info_id'] = $staff['staff_info_id'];
                    $row['cycle'] = $i;
                    $row['days'] = $last_data[$k]['left_days'];//历史周年 剩余
                    $row['leave_days'] = $last_data[$k]['left_all_days'] - $last_data[$k]['left_days'];//当前周期 已经用的天数
                    $row['old_days'] = $last_data[$k]['left_days'];
                    $row['level_days'] = $grade_days;//职等 对应天数
                    $row['add_days'] = $all_days - $grade_days;
                    $row['is_effective'] = 0;//当前是否在有效期
                    $this->getDI()->get('db')->insertAsDict(
                        'leave_for_data', $row
                    );
                }
            }
        }
        echo '跑完了';

    }


}