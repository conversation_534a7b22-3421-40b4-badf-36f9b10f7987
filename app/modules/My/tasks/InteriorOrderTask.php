<?php
/**
 *InteriorOrderTask.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/8/10 0010 19:56
 */
namespace FlashExpress\bi\App\Modules\My\Tasks;
use Exception;
use FlashExpress\bi\App\Enums\InteriorGoodsEnums;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsSkuModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersGoodsSkuModel;
use  \FlashExpress\bi\App\Models\backyard\InteriorOrdersModel;
use \FlashExpress\bi\App\Enums\InteriorOrderStatusEnums;
use \FlashExpress\bi\App\Enums\InteriorGoodsPayMethodEnums;
use FlashExpress\bi\App\Models\oa\SysDepartmentPcCode;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use  FlashExpress\bi\App\Modules\My\Server\InteriorGoodsServer;
use  \FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\FlashPayServer;
use FlashExpress\bi\App\Server\SyncWarehoseServer;

class InteriorOrderTask extends \BaseTask
{
    /**
     * 离职当日，未审核，待发货，自定取消订单
     * 离职的员工有未审核待发货（工资抵扣）、预定中的订单才可以做取消操作
     * Created by: Lqz.
     * CreateTime: 2020/8/12 0012 18:58
     */
    public function autoCancelOrderAction()
    {
        $InteriorGoodsServer = new InteriorGoodsServer();
        // 查询未审核的，未发货的订单（待发货，预售）
        $conditions = "order_status in ({orderStatus:array}) and is_audited = 0 and pay_method = 1";
        $bind       = ["orderStatus" =>
                           [
                               InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                               InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE
                           ]
        ];
        $ordersObj  = InteriorOrdersModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        $orders     = $ordersObj->toArray();
        if ($orders) {
            $staffIds = array_column($orders, 'staff_id');
            // 查询员工离职日期小于当前日期的数据，对比离职日期
            $staffsObj  = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({staff_ids:array}) and state = 2',
                'bind'       => ['staff_ids' => $staffIds],
            ]);
            $staffs     = $staffsObj->toArray();
            $staffsKey  = array_column($staffs, null, 'staff_info_id');
            $successNum = $errorNum = 0;
            foreach ($orders as $key => $order) {
                $num = $key + 1;
                //如果存在该订单的员工，取消订单
                if (isset($staffsKey[$order['staff_id']])) {
                    $loginUser = [
                        'staff_id' => $order['staff_id']
                    ];
                    $params    = [
                        'order_code' => $order['order_code'],
                        'cancel_reason' => InteriorOrderStatusEnums::ORDER_CANCEL_REASON_PRE_SALE
                    ];
                    $res       = $InteriorGoodsServer->cancelOrder($loginUser, $params, 'system_auto_canceled');
                    if ($res === true) {
                        $successNum++;
                        echo "第{$num}条SUCCESS:order_code:{$order['order_code']}自动取消订单成功" . PHP_EOL;
                    } else {
                        $errorNum++;
                        echo $msg = "第{$num}条ERROR:order_code:{$order['order_code']}自动取消订单失败" . var_export($res, true) . PHP_EOL;
                        $logger = $this->getDI()->get('logger');
                        $logger->write_log($msg, 'info');
                    }
                } else {
                    echo "订单【{$order['order_code']}】员工ID：{$order['staff_id']},暂无离职信息" . PHP_EOL;
                }
            }
            echo "自动取消订成功:{$successNum} 个，失败{$errorNum}个，已全部执行完毕" . PHP_EOL;
        } else {
            echo "No orders need to auto cancel" . PHP_EOL;;
        }
    }

    /**
     * 同步订单状态为待发货、预定中、已取消的待审核的订单，需要同步scm的审核信息（审核状态、审核时间、审核备注）
     * Created by: Lqz.
     * CreateTime: 2020/8/12 0012 19:16
     */
    public function getWmsOrderAuditAction()
    {
        $page = 1;
        $size = 200;
        $count = 0;
        do {
            // 查询未审核的，未发货的订单（待发货，预售, 已取消的）
            $conditions = "order_status in ({orderStatus:array}) and is_audited = 0 and out_sn != :out_sn: and out_status = :out_status: and submit_at >= :submit_at_start: and submit_at <= :submit_at_end:";
            //跟产品沟通确认后，只取近3个月的数据同步scm的状态
            $start = gmdate("Y-m-d H:i:s", strtotime("-3 month"));
            $bind       = ["orderStatus" =>
                [
                    InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE,
                ],
                "out_sn" => '',
                "out_status" => 1,
                'submit_at_start' => $start,
                'submit_at_end' => gmdate('Y-m-d H:i:s')
            ];
            $ordersObj  = InteriorOrdersModel::find([
                'conditions' => $conditions,
                'bind'       => $bind,
                'offset'     => ($page - 1) * $size,
                'limit'      => $size,
                "order"      => "id desc",
            ]);
            $orders     = $ordersObj->toArray();
            $successNum = 0;
            if ($orders) {
                $syncServer = new SyncWarehoseServer();
                foreach ($ordersObj as $order) {
                    $postData         = ['outSn' => $order->out_sn];
                    $postData['lang'] = $this->lang;
                    $res              = $syncServer->syncOrderStatusFromWmsGetOutboundOrderStatus($postData, true);
                    if (isset($res['data']['status']) && in_array($res['data']['status'], [10, 40, 50, 60, 70, 80, 90, 100, 110])) {
                        if ($res['data']['status'] == 10 || $res['data']['status'] == 40) {
                            $order->is_audited = 2;
                        } else {
                            $order->is_audited = 1;
                        }
                        foreach ($res['data']['statusDetail'] as $_status) {
                            if (in_array($_status['status'], [10, 40, 50])) {
                                $order->audited_at   = $_status['time'];
                                $order->audited_desc = $_status['statusDesc'];
                            }
                        }
                        $order->save();
                        $successNum++;
                        echo "订单号{$order->order_code} 获取审核信息通过并同步完成" . PHP_EOL;;
                    } else {
                        echo "订单号{$order->order_code} 获取审核信息" . var_export($res, true);
                    }
                }
            }
            $num = count($orders);
            $count += $num;
            $page++;
        } while ($num > 0);
        echo "共计同步查询{$count}条订单，成功通过{$successNum}条".PHP_EOL;
    }

    /**
     *同步 wms 库存并修改预售订单为待发货状态，修改sku库存并记录日志
     * Created by: Lqz.
     * CreateTime: 2020/8/13 0013 11:58
     */
    public function getWmsGoodsStockAction()
    {
        $conditions   = "status = 1";
        $bind         = [];
        $goodsSkusObj = InteriorGoodsSkuModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
//                "for_update" => true
        ]);
        $goodsSkus    = $goodsSkusObj->toArray();
        if ($goodsSkus) {
            //获取sku关联的spu
            $goods_ids = array_values(array_unique(array_column($goodsSkus, 'goods_id')));
            $goods_list = InteriorGoodsModel::find([
                'columns' => 'id, goods_type',
                'conditions' => 'id in ({ids:array})',
                'bind' => ['ids' => $goods_ids],
            ])->toArray();
            $goods_list = array_column($goods_list, null, 'id');
            $goods_type_stock_ids = (new InteriorGoodsServer())->getGoodsTypeStockId();

            // 获取wms库存
            foreach ($goodsSkus as $_sku) {
                $goods_type = $goods_list[$_sku['goods_id']]['goods_type'] ?? 0;

                $barCodesStr = $_sku['goods_sku_code'];
                $syncServer  = new SyncWarehoseServer();
                $postData    = [
                    "barCode"     => $barCodesStr,
                    "goodsStatus" => "normal",
                    'lang'        => $this->lang,
                    'warehouseId' => $goods_type_stock_ids[$goods_type] ?? '',
                ];

                $res = $syncServer->syncGoodsInventoryFromWmsGoodsStock($postData, true);
                if (isset($res['data'][$barCodesStr])) {
                    $wmsInventoryList = $res['data'][$barCodesStr];
                    $db               = InteriorOrdersModel::beginTransaction($this);
                    try {
                        $sku = InteriorGoodsSkuModel::findFirst([
                            'conditions' => "id = :sku_id:",
                            'bind'       => ['sku_id' => $_sku['id']],
                            "for_update" => true
                        ]);
                        // 如果不存在则初始化库存为 0
                        if (isset($wmsInventoryList['availableInventory'])) {
                            $_availableInventory = $wmsInventoryList['availableInventory'];
                            // 2020-12-02 增加记录通不过来的总库存呢
                            $toSurplusNum = $_availableInventory;
                            $chNum        = $toSurplusNum - $sku->surplus_num;
                            InteriorGoodsServer::saveSkuSurplusNumAndLog($sku, 0, $chNum, $toSurplusNum, 'sync_from_wms_total');

                            // 如果库存小于0， 则有预售情况查询预售订单，并变更为待发货
                            // 查询该sku 预售订单的sku和订单
                            $conditions      = "goods_sku_id = :skuId: and current_sku_pre_sale = 1";
                            $bind            = ["skuId" => $sku->id];
                            $preOrderSkusObj = InteriorOrdersGoodsSkuModel::find([
                                'conditions' => $conditions,
                                'bind'       => $bind,
                                'limit'      => 20,
                                'order'      => 'id asc'
                            ]);
                            $preOrderSkus    = $preOrderSkusObj->toArray();
                            if ($preOrderSkus && $_availableInventory) {
                                foreach ($preOrderSkusObj as $_OrderSkuObj) {
                                    // 如果当前库存大于预售，则改变当前order_goods_sku预售状态，获取order_goods_sku 对用 order
                                    if ($_availableInventory < $_OrderSkuObj->buy_num) {
                                        continue;
                                    }
                                    // 获取该订单的其他order_sku,是否有预售的
                                    $conditions                         = "order_code = :order_code: and current_sku_pre_sale = 1 and id != :orderSkuId:";
                                    $bind                               = ["order_code" => $_OrderSkuObj->order_code, 'orderSkuId' => $_OrderSkuObj->id];
                                    $_thisOrderOtherPreOrderSku         = InteriorOrdersGoodsSkuModel::findFirst([
                                        'conditions' => $conditions,
                                        'bind'       => $bind,
                                    ]);
                                    //$_OrderSkuObj->current_sku_pre_sale = 0;
                                    //$_OrderSkuObj->save();
                                    $_availableInventory -= $_OrderSkuObj->buy_num;
                                    if (!$_thisOrderOtherPreOrderSku) {
                                        // 无其他预售则改变订单为待发货
                                        $conditions = "order_code = :order_code: and order_status = :order_status:";
                                        $bind       = [
                                            "order_code"   => $_OrderSkuObj->order_code,
                                            "order_status" => InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE
                                        ];
                                        $_preOrder  = InteriorOrdersModel::findFirst([
                                            'conditions' => $conditions,
                                            'bind'       => $bind,
                                            "for_update" => true
                                        ]);
                                        if ($_preOrder) {
                                            $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($_preOrder->staff_id);
                                            //员工不存在
                                            if (empty($userInfo)) {
                                                continue;
                                            }
                                            //在职
                                            if ($userInfo->state == HrStaffInfoModel::STATE_1 && $userInfo->wait_leave_state == HrStaffInfoModel::WAITING_LEAVE_NO) {
                                                // 预售单是不向仓储同步的
                                                $syncRes = $this->syncAddOrderToWmsReturnWarehouseAdd($_preOrder, $goods_type_stock_ids[$_preOrder->goods_type] ?? '');
                                                if ($syncRes['code'] != 1 || !$syncRes['data']) {
                                                    $msg = "该SKU{sku_id:$sku->id}预售订单{order_code:$_preOrder->order_code}同步下单仓储失败wms_msg:" . var_export($syncRes, true) . 'aaaa' . PHP_EOL;
                                                    echo $msg;
                                                    $logger = $this->getDI()->get('logger');
                                                    $logger->write_log($msg, 'info');

                                                    $_preOrder->out_status = 2;
                                                    $_preOrder->save();

                                                    continue;
                                                } else {
                                                    $_preOrder->order_status = InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE;
                                                    $_preOrder->out_sn       = $syncRes['data'];
                                                    $_preOrder->save();
                                                    $msg = "预售订单【{$_OrderSkuObj->order_code}】已修改为【待发货】";
                                                }
                                            } elseif ($userInfo->state == HrStaffInfoModel::STATE_2) {
                                                //如果员工离职，则不推送SCM出库，并且将订单状态改成已取消
                                                $date = date('Y-m-d H:i:s');
                                                $_preOrder->order_status = InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE;
                                                $_preOrder->cancel_reason = InteriorOrderStatusEnums::ORDER_CANCEL_REASON_PRE_SALE;
                                                $_preOrder->canceled_at = $date;
                                                $_preOrder->updated_at = $date;
                                                $msg                     = "预售订单【{$_OrderSkuObj->order_code}】已修改为【已取消】";
                                                $_preOrder->save();
                                            } else {
                                                $msg = "预售订单【{$_OrderSkuObj->order_code}】员工非在职、离职无需变更订单状态";
                                            }
                                        } else {
                                            $msg = "无预售---出现预售sku与预售order不一致";
                                        }
                                    } else {
                                        $msg = "有其他预售sku";
                                    }

                                    //当前商品改为不是预售，有可能下单失败，continue，不执行这段代码，10分钟后重新执行
                                    $_OrderSkuObj->current_sku_pre_sale = 0;
                                    $_OrderSkuObj->save();

                                    // 剩余库存保存给 sku
                                    $toSurplusNum = $_availableInventory;
                                    echo "该SKU{sku_id:$sku->id}----有预售订单库存已修改【{$_availableInventory}】----" . $msg . PHP_EOL;
                                    InteriorGoodsServer::saveSkuSurplusNumAndLog($sku, 0, $_OrderSkuObj->buy_num, $toSurplusNum, "sync_from_wms_to_pre_sell_order【order_code:{$_OrderSkuObj->order_code},order_sku_id:{$_OrderSkuObj->id}】");

                                }
                                $toSurplusNum = $_availableInventory;
                                echo "该SKU{sku_id:$sku->id}库存同步为{$toSurplusNum}" . PHP_EOL;;
                                InteriorGoodsServer::saveSkuSurplusNumAndLog($sku, 0, 0, $toSurplusNum, 'sync_from_wms');
                            } else {
                                $toSurplusNum = $_availableInventory;
                                echo "该SKU{sku_id:$sku->id}----无预售订单或库存【{$toSurplusNum}】不足改为【{$toSurplusNum}】" . PHP_EOL;
                                InteriorGoodsServer::saveSkuSurplusNumAndLog($sku, 0, 0, $toSurplusNum, 'sync_from_wms');
                            }
                        } else {
                            InteriorGoodsServer::saveSkuSurplusNumAndLog($sku, 0, 0, 0, 'sync_from_wms_no_data');
                        }
                        $db->commit();
                    } catch (\Exception $e) {
                        $db->rollback();
                        $msg = "interOrderSku同步wms库存,变更预售订单状态为待发货出错：" . $e->getMessage() . PHP_EOL;
                        echo $msg;
                        $logger = $this->getDI()->get('logger');
                        $logger->write_log($msg, 'info');
                    }
                } else {
                    echo "该SKUsku_id:{$_sku['id']}----无库存" . var_export($res, true) . PHP_EOL;
                }
            }
        }
        echo "执行完毕！" . PHP_EOL;
        exit;
    }

    private  function syncAddOrderToWmsReturnWarehouseAdd($_preOrder, $warehouseId)
    {
        if (!$warehouseId) {
            return [
                'code' => 0,
                'msg' => $this->getTranslation()->_('interior_goods_stock_unset') . $_preOrder->staff_id,
            ];
        }

        $conditions      = "order_code = :order_code:";
        $bind            = ["order_code" => $_preOrder->order_code];
        $preOrderSkusObj = InteriorOrdersGoodsSkuModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'order'      => 'id desc'
        ]);
        $preOrderSkusArr = $preOrderSkusObj->toArray();
        $syncWmsPostData = $goods = [];
        foreach ($preOrderSkusArr as $k => $_preOrderSkus) {
            $buyNum  = $_preOrderSkus['buy_num'];
            $price   = $_preOrderSkus['buy_price'];
            $amount  = $_preOrderSkus['pay_amount'];
            $goods[] = [
                'i'             => $k,
                "barCode"       => $_preOrderSkus['goods_sku_code'],
                "goodsName"     => $_preOrderSkus['goods_name_th'],
                "specification" => $_preOrderSkus['attr_1'],
                "num"           => $buyNum,
                //出库商品json，其中price的单位为萨当，即1泰铢=100萨当
                "price"         => ($price * 100),
                "remark"        => $amount
            ];
        }

        $nodeSn=$_preOrder->node_sn ?? '';
        $this->getDI()->get('logger')->write_log("InteriorOrderTask=recommit_order=node_sn_1".$_preOrder->order_code.'_'.$nodeSn, 'info');

        /* 10630【MY-BY|员工商城】 马来员工商城对接FlashPay
         * https://l8bx01gcjr.feishu.cn/docs/doccnFBQ3fn1GHAs28I5M3PJEzc
         * 脚本模块化、取消无效代码
         *
         */
        if (empty($_preOrder->node_sn)) {
            $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($_preOrder->staff_id);
            if (empty($userInfo)) {
                return ['code' => 0, 'data' => '没有找到该工号' . $_preOrder->staff_id];
            }
            $userInfo = $userInfo->toArray();
            $nodeSn   = (new InteriorGoodsServer())->getNodeSn($_preOrder->goods_type, $userInfo, $_preOrder->receive_store_id);
        } else {
            $nodeSn = $_preOrder->node_sn;
        }

        if (empty($nodeSn)) {
            return ['code' => 0, 'data' => '没有找到该工号对应pc_code' . $_preOrder->staff_id];
        }
        $this->getDI()->get('logger')->write_log("InteriorOrderTask=recommit_order=node_sn_2".$_preOrder->order_code.'_'.$nodeSn, 'info');

        $syncWmsPostData['nodeSn']           = $nodeSn;
        $syncWmsPostData['consigneeName']    = $_preOrder->staff_name;
        $syncWmsPostData['consigneePhone']   = $_preOrder->staff_mobile;
        $syncWmsPostData['province']         = $_preOrder->receive_province_name;
        $syncWmsPostData['city']             = $_preOrder->receive_city_name;
        $syncWmsPostData['district']         = $_preOrder->receive_district_name;
        $syncWmsPostData['postalCode']       = $_preOrder->receive_postal_code;
        $syncWmsPostData['consigneeAddress'] = $_preOrder->receive_address;
        $syncWmsPostData['orderSn']          = $_preOrder->order_code;
        $syncWmsPostData['node_department_id']= $_preOrder->node_department_id;
        $syncWmsPostData['deliveryWay']      = 'express';
        $syncWmsPostData['goods']            = json_encode($goods, JSON_UNESCAPED_UNICODE);
        $syncWmsPostData['remark']           = "GF_order_server_crontab：【staff_id：{ $_preOrder->staff_id;}】";
        $syncWmsPostData['lang']             = 'zh-CN';
        $syncWmsPostData['warehouseId']      = $warehouseId;

        $syncWarehoseServer                  = new SyncWarehoseServer();
        $res                                 = $syncWarehoseServer->syncAddOrderToWmsReturnWarehouseAdd($syncWmsPostData, true);
        // 同步node_sn
        $res['node_sn']             = $nodeSn;

        return $res;
    }

    /**
     * 下单没成功的，重新判断下
     */
    public function recommit_orderAction(){
        $this->getDI()->get('logger')->write_log("InteriorOrderTask = recommit_order ==flag_my", 'info');

        $orders = InteriorOrdersModel::find(
            [
                'conditions' => 'out_status = 2',
                'columns' => 'id'
            ]
        )->toArray();

        if (empty($orders)) {
            $this->getDI()->get('logger')->write_log("InteriorOrderTask = recommit_order ==no_data", 'info');
            return;
        }

        $this->getDI()->get('logger')->write_log("InteriorOrderTask = recommit_order ==flag", 'info');

        $syncServer = new SyncWarehoseServer();
        $interior_goods_server = new InteriorGoodsServer();
        $interior_goods_scm_stock_ids = $interior_goods_server->getGoodsTypeStockId();

        $orderCode = '';
        foreach ($orders as $order) {
            try {
                $this->db->begin();
                $item = InteriorOrdersModel::findFirst(
                    [
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $order['id']],
                        'for_update' => true
                    ]
                );
                if(empty($item)){
                    throw new Exception('not found item');
                }

                $orderCode = $item->order_code;

                $postData = [
                    'orderSn' => $item->order_code,
                    'lang'    => $this->lang
                ];

                //就已取消不需要重新下
                if ($item->order_status == InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE) {
                    $item->out_status = 1;
                } else {
                    $res = $syncServer->getOrderDetail($postData, 1);

                    $postData['warehouseId'] = $interior_goods_scm_stock_ids[$item->goods_type] ?? '';

                    //如果不存在，需要重新下单。
                    //不存在，也会返回1，但是data是个空数组
                    $log = false;
                    if (empty($res) || $res['code'] != 1 || empty($res['data'])) {
                        // 更新省市区和邮编,历史数据不同步更新
                        if (!empty($item->receive_store_id)) {
                            $partinfo = $interior_goods_server->getOrderNewAddressInfo($item->receive_store_id);
                            $item->receive_province_name = isset($partinfo[0]) ? $partinfo[0]['name'] : '';
                            $item->receive_city_name = isset($partinfo[1]) ? $partinfo[1]['name'] : '';
                            $item->receive_district_name = isset($partinfo[2]) ? $partinfo[2]['name'] : '';
                            $item->receive_postal_code = isset($partinfo[3]) ? $partinfo[3] : '';
                            $item->receive_address = isset($partinfo[4]) ? $partinfo[4] : '';
                        }
                        $res = $this->syncAddOrderToWmsReturnWarehouseAdd($item, $postData['warehouseId']);
                        if (empty($res) || $res['code'] != 1) {
                            $item->fail_num++;
                            $item->fail_reason = $res['msg'] ?? '';
                            $log = true;
                        } else {
                            $item->out_sn = $res['data'];
                            $item->out_status = 1;
                            $item->node_sn = $res['node_sn'];
                        }
                    } else {
                        $item->out_sn = $res['data']['outSn'];
                        $item->out_status = 1;
                    }
                    $this->logger->write_log("InteriorOrderTask recommit_order => orderSn:".$item->order_code.', data:'.json_encode($res,
                            JSON_UNESCAPED_UNICODE), ($log ? 'notice' : 'info'));
                }
                $item->save();
                $this->db->commit();
            } catch (Exception $e) {
                $this->db->rollback();
                $this->logger->write_log("InteriorOrderTask orderCode===" .$orderCode);
            }
        }
    }

    /**
     * 同步FlashPay交易状态到订单表
     * 1. 获取FlashPay在线支付待付款、支付中的订单列表；
     * 然后根据每一笔订单去获取pay那边的当前交易状态；
     * 其中0、交易待支付（该订单不做任何处理）；
     * 2、交易处理中（需将该订单状态由待付款变更为支付中，已是支付中的订单可不做处理）；
     * 3、交易成功（需将该订单状态由待付款(支付中)的订单在同步SCM成功后状态变更为待发货）；
     * 4、交易失败； 5、交易关闭（需将该订单变更为已取消）
     */
    public function syncFlashPayTradeStatusAction()
    {
        $ordersObj = InteriorOrdersModel::find([
            'conditions' => "order_status in ({order_status:array}) and pay_method = :pay_method:",
            'bind'       => [
                'order_status' => [InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE, InteriorOrderStatusEnums::ORDER_STATUS_PAYING_CODE],
                'pay_method' => InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE
            ],
        ]);
        if ($ordersObj->toArray()) {
            foreach ($ordersObj as $order) {
                $msg = "订单号：【".$order->order_code."】在脚本syncFlashPayTradeStatus同步FlashPay交易状态到订单状态";
                try{
                    $flash_pay_server = new FlashPayServer();
                    $flash_pay_server->syncFlashPayTradeStatus($order);
                    $msg .= "成功".PHP_EOL;
                } catch (\Exception $e) {
                    $msg = "异常：【" . $e->getMessage() ."】". PHP_EOL;
                }
                echo $msg;
                $logger = $this->getDI()->get('logger');
                $logger->write_log($msg, 'info');
            }
        }else {
            echo "no order need sync tradeStatus to OrderStatus" . PHP_EOL;;
        }
    }

    /**
     * FlashPay在线支付订单超过1小时未支付的订单自动取消脚本
     * 获取支付方式为FlashPay的下单时间超过1小时待支付的订单做订单取消操作
     */
    public function autoCancelTimeoutUnpaidAction()
    {
        $ordersObj = InteriorOrdersModel::find([
            'columns' => ['id'],
            'conditions' => "order_status = :order_status: and pay_method = :pay_method: and order_expire_at < :order_expire_at:",
            'bind'       => [
                'order_status' => InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE,
                'pay_method' => InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE,
                'order_expire_at' => date('Y-m-d H:i:s')
            ],
        ]);

        $orders = $ordersObj->toArray();
        if ($orders) {
            $cancel_order_arr = array_column($orders, "id");
            $count = count($cancel_order_arr);
            $cancel_order_ids = implode(",", $cancel_order_arr);
            try {
                $date = date('Y-m-d H:i:s');
                $res = $this->db->updateAsDict(
                    'interior_orders',
                    ['order_status' => InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE, 'updated_at' => $date, 'canceled_at' => $date, 'cancel_reason' => InteriorOrderStatusEnums::ORDER_CANCEL_REASON_SYSTEM],
                    ["conditions" => "id in (" . $cancel_order_ids . ")"]
                );
                if (!$res) {
                    throw new \Exception("auto cancel timeout unpaid orders failed");
                }
                $msg = "autoCancelTimeoutUnpaid超时1小时未支付订单自动取消订单ID组为：【".$cancel_order_ids."】；共计操作取消订单数：【".$count."】条". PHP_EOL;
            } catch (\Exception $e) {
                $msg = "autoCancelTimeoutUnpaid超时1小时未支付订单自动取消失败：" . $e->getMessage() . PHP_EOL;

            }
            echo $msg;
            $logger = $this->getDI()->get('logger');
            $logger->write_log($msg, 'info');
        }else {
            echo "No expired orders need to auto cancel" . PHP_EOL;;
        }
    }

    /**
     * 系统自动处理出库单 - 工服 每小时一次
     *
     * php app/cli.php interior_order auto_audit_outbound
     */
    public function auto_audit_outboundAction()
    {
        $this->checkLock(__METHOD__, 7200);

        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            $orderModels = InteriorOrdersModel::find([
                'conditions' => "order_status = :order_status: AND auto_audit_outbound_status = :auto_audit_outbound_status: AND goods_type = :goods_type: AND out_sn != :out_sn:",
                'bind'       => [
                    'order_status'               => InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                    'auto_audit_outbound_status' => InteriorOrderStatusEnums::ORDER_AUTO_AUDIT_OUTBOUND_STATUS_NO,
                    'goods_type'                 => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES,
                    'out_sn'                     => '',
                ],
            ]);

            $orders = $orderModels->toArray();

            $log .= '待自动审核出库订单共 ' . count($orders) . ' 笔' . PHP_EOL;
            if (!empty($orders)) {
                // 查询员工离职状态
                $staffIds = array_column($orders, 'staff_id');

                // 查询员工离职日期小于当前日期的数据，对比离职日期
                $staffs = HrStaffInfoModel::find([
                    'conditions' => 'staff_info_id in ({staff_ids:array})',
                    'bind'       => ['staff_ids' => $staffIds],
                    'columns'    => ['staff_info_id', 'state', 'wait_leave_state'],
                ])->toArray();
                $staffs = array_column($staffs, null, 'staff_info_id');

                $interiorGoodsServer = new InteriorGoodsServer();

                $successNum    = $errorNum = $staffNullNum = $staffMidNum = $processedNum = 0;
                $staffLeaveNum = $leaveCancelSuccessNum = $leaveCancelErrorNum = 0;
                foreach ($orderModels as $key => $orderObj) {
                    $_log = "待处理订单-{$key}: {$orderObj->staff_id} - {$orderObj->order_code} - {$orderObj->out_sn}, pay_amount={$orderObj->pay_amount}";

                    $orderInfo = InteriorOrdersModel::findFirst([
                        'conditions' => 'order_code = :order_code:',
                        'bind'       => ['order_code' => $orderObj->order_code],
                        'columns'    => ['order_status', 'auto_audit_outbound_status'],
                    ]);
                    if ($orderInfo->order_status != InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE || $orderInfo->auto_audit_outbound_status != InteriorOrderStatusEnums::ORDER_AUTO_AUDIT_OUTBOUND_STATUS_NO) {
                        $processedNum++;
                        $_log .= ", 订单已被处理[当前状态: order_status->{$orderInfo->order_status}, auto_audit_outbound_status->{$orderInfo->auto_audit_outbound_status}], 跳过" . PHP_EOL;
                        $this->info($_log, true);
                        continue;
                    }

                    // 自费工服, 直接审核出库
                    if ($orderObj->pay_amount > 0) {
                        if ($interiorGoodsServer->autoAuditOutboundOrder($orderObj)) {
                            $successNum++;
                            $_log .= ', 成功' . PHP_EOL;
                        } else {
                            $errorNum++;
                            $_log .= ', 失败' . PHP_EOL;
                        }
                        $this->info($_log, true);
                    } else {
                        // 免费工服, 看员工在职状态
                        $staff_info = $staffs[$orderObj->staff_id] ?? [];

                        // 员工表不存在, 不处理
                        if (empty($staff_info)) {
                            $staffNullNum++;
                            $_log .= ', 员工不在员工表, 未处理' . PHP_EOL;
                            $this->info($_log, true);
                            continue;
                        }

                        // 停职 或 待离职 不处理
                        if ($staff_info['state'] == HrStaffInfoModel::STATE_3 || ($staff_info['state'] == HrStaffInfoModel::STATE_1 && $staff_info['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE)) {
                            $staffMidNum++;
                            $_log .= ", 员工停职或待离职[{$staff_info['state']}-{$staff_info['wait_leave_state']}], 未处理" . PHP_EOL;
                            $this->info($_log, true);
                            continue;
                        }

                        // 离职 取消出库单, 取消员工订单
                        if ($staff_info['state'] == HrStaffInfoModel::STATE_2) {
                            $staffLeaveNum++;
                            $_log .= '员工已离职, ';

                            $loginUser = [
                                'staff_id' => $orderObj->staff_id,
                            ];
                            $params    = [
                                'order_code'    => $orderObj->order_code,
                                'cancel_reason' => InteriorOrderStatusEnums::ORDER_CANCEL_REASON_PRE_SALE,
                            ];
                            $res       = $interiorGoodsServer->cancelOrder($loginUser, $params,
                                'auto_audit_outbound_staff_leave_canceled');
                            if ($res === true) {
                                $leaveCancelSuccessNum++;
                                $_log .= '取消SCM出库单成功, 订单取消状态标记成功' . PHP_EOL;
                            } else {
                                $leaveCancelErrorNum++;
                                $_log .= "取消SCM出库单异常[msg-{$res['msg']}], 订单状态未处理" . PHP_EOL;
                            }

                            $this->info($_log, true);
                            continue;
                        }

                        // 在职, 自动审核出库单
                        if ($interiorGoodsServer->autoAuditOutboundOrder($orderObj)) {
                            $successNum++;
                            $_log .= ', 成功' . PHP_EOL;
                        } else {
                            $errorNum++;
                            $_log .= ', 失败' . PHP_EOL;
                        }

                        $this->info($_log, true);
                    }

                    sleep(1);
                }

                $log .= "执行完毕: 标记成功 {$successNum} 个, 标记失败 {$errorNum} 个; 被其他脚本处理的 {$processedNum} 个(本次跳过未处理的); 员工信息不在员工表的 {$staffNullNum} 个, 员工停职/待离职的 {$staffMidNum} 个; ";
                $log .= "员工离职的 {$staffLeaveNum} 个 (取消成功 {$leaveCancelSuccessNum} 个, 取消失败 {$leaveCancelErrorNum} 个)" . PHP_EOL;
            } else {
                $log .= '无待自动审核的出库' . PHP_EOL;
            }

        } catch (\Exception $e) {
            $log .= '脚本异常，原因可能是：' . $e->getMessage() . PHP_EOL;
        }

        $this->clearLock(__METHOD__);

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->info($log, true);
        exit();
    }
}
