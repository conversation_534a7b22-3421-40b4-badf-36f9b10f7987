<?php
namespace FlashExpress\bi\App\Modules\My\Tasks;

use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\MessagePdfModel;
use FlashExpress\bi\App\Models\bi\RoleStaffMenusModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Server\ProbationServer as ProbationServerAlias;
use FlashExpress\bi\App\Server\WorkflowServer;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use Phalcon\Db;
use FlashExpress\bi\App\Modules\My\Server\ProbationServer;
use FlashExpress\bi\App\library\enums;
use Exception;

class ProbationTask extends  \BaseTask
{

	//这里是直接小于等于 17 的
    public $first_days = 30;// 第 30 天发第一阶段  转正评估-评审表    给上级 (push消息)  35 24 点结束  +4
    public $second_days = 75;// 75 天发送第二阶段   转正评估-评审表  至 上级 (push消息)
    public $formal_days = 91; //转正日期
    public $second_delay_days = 120;    //延长试用期
	
	//大于 17 职级的
	public $first_days_two = 75;// 第 75 天发第一阶段  转正评估-评审表    给上级 (push消息)  80天 24 点结束  +4
	public $second_days_two = 165;// 165 天发送第二阶段   转正评估-评审表  至 上级 (push消息)
	public $formal_days_two = 181; //转正日期
	public $second_delay_days_two = 180;    //延长试用期
	
	//职级分界线
	public $job_grade = 17;    //职级分界线  <= 17
	

    public $country_code = 'th';
    public $add_hour = '7';  //误差时间


    public function initialize()
    {
        parent::initialize(); // TODO: Change the autogenerated stub
        $this->country_code = strtolower(env("country_code",'th'));
	    $this->add_hour = $this->getDI()['config']['application']['add_hour'];
	    set_time_limit(60*30); //执行时间 60 分钟
	    $memory_limit = 1024; //内存限制  mb
	    ini_set('memory_limit', $memory_limit.'M');
	    
    }


    /**
     * 第一阶段评估插入
     * 每天执行，
     * @param array $params
     */

    public function firstAction(array $params)
    {
        $probation_channel_type = !empty($params[0]) && trim($params[0]) == 'non_frontLine' ? 2 : 1;
        
        $bll = new ProbationServer($this->lang, $this->add_hour);
        $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        $hire_date_begin = $this->getDateByDays($start, $this->first_days);          //7.26
        $hire_date_end = $this->getDateByDays($start, $this->first_days - 1);  //7.27
        $staffs = $bll->getStaffs($hire_date_begin, $hire_date_end,0,0,$this->job_grade,0,$probation_channel_type); // 查询 14 级及以下的人
	    $this->myLogger("查询第一阶段 17 级一下的数据" . $hire_date_begin . "===" . $hire_date_end . ' '. implode(',',array_column($staffs,'staff_info_id')));
        //查询 17 级以上的数据
	    $hire_date_begin = $this->getDateByDays($start, $this->first_days_two);          //7.26
	    $hire_date_end = $this->getDateByDays($start, $this->first_days_two - 1);  //7.27
	    $staffs_two = $bll->getStaffs($hire_date_begin, $hire_date_end,0,$this->job_grade,'',0,$probation_channel_type);//查询 14 级以上的人
	    $staffs = array_merge($staffs,$staffs_two);//合并两个数据组
	   
        if (empty($staffs)) {
            $this->myLogger("没有找到需要生成的第一阶段评估数据 no probation staffs first" . $hire_date_begin . "===" . $hire_date_end . ' '. implode(',',array_column($staffs_two,'staff_info_id')));
            return;
        }
       

        $db = $this->getDI()->get("db");
        $staffIds = array_column($staffs,'staff_info_id');
        $find = HrProbationModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'staff_info_id in ({ids:array}) and first_audit_status !=1',
            'bind'       => ['ids' => $staffIds],
        ])->toArray();
        $existStaffIds = array_column($find, 'staff_info_id');

        $nonFrontLineProbationStaff = $probation_channel_type == HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE ? $bll->getNonFrontLineProbationStaff($staffIds,2) : [];

        foreach ($staffs as $staff) {
            if (!empty($staff['status']) && $staff['status'] == $bll::STATUS_FORMAL){
                continue;
            }
            if (
                !empty($staff['cur_level']) &&
                $staff['cur_level'] == 1 &&
                !empty($staff['first_audit_status']) &&
                $staff['first_audit_status'] != HrProbationModel::FIRST_AUDIT_STATUS_WAIT
            ){
                // 已执行过的数据
                continue;
            }
            if (empty($staff['manager_id'])) {
                $this->myLogger("staff= " . $staff['staff_info_id'] . " manager_id is null 第一阶段评估没有找到上级",'error');
                continue;
            }
            if (in_array($staff['staff_info_id'],$existStaffIds)) {
                $this->myLogger('staff  ' . $staff['staff_info_id'] . ' 已经存在');
                continue;
            }
            if (
                isset($nonFrontLineProbationStaff['probation_data'][$staff['staff_info_id']]) &&
                $nonFrontLineProbationStaff['probation_data'][$staff['staff_info_id']] == HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE &&
                isset($nonFrontLineProbationStaff['probation_staff']) &&
                !in_array($staff['staff_info_id'],$nonFrontLineProbationStaff['probation_staff'])
            ){
                if (empty($nonFrontLineProbationStaff['target'][$staff['staff_info_id']]['target_info'])){
                    feishu_push("MY,该员工目标未制定 staff_info_id:{$staff['staff_info_id']}",'https://open.feishu.cn/open-apis/bot/v2/hook/355f5d1f-3bfe-4218-a9d9-9554837af929');
                    continue;
                }
                $target_info = $nonFrontLineProbationStaff['target'][$staff['staff_info_id']]['target_info'];
                $tpl_id = null;
            }else{
                $target_info = null;
                $tpl_id = $bll->getTplIdByJobTitleGradeV2($staff['job_title_grade_v2']);
            }
            
            try {
                $db->begin();
                $probation_id = $staff['probation_id'];
                $res = $db->updateAsDict("hr_probation", [
                    "first_audit_status" => HrProbationModel::FIRST_AUDIT_STATUS_RUN,
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600)
                ], ["conditions" => "id = $probation_id"]);
                if (!$res) {
                    throw new \Exception("hr_probation insert fail");
                }
                $res = $db->insertAsDict("hr_probation_audit", [
                    "probation_id" => $probation_id,
                    "staff_info_id" => $staff['staff_info_id'],
                    'audit_id' => $staff['manager_id'],
                    'tpl_id' => $tpl_id,
                    'score' => $target_info,
                    'created_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'deadline_at' => $this->getDateByDays($start, 6, 1),      //第一次上级评审截止6天以后
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'show_time' => date("Y-m-d H:i:s"),
                ]);
                if (!$res) {
                    throw new \Exception("hr_probation_audit insert fail");
                }
                $db->commit();
                //发送push
	            
                $pussh = $bll->push_notice_higher($staff['manager_id'],$staff['staff_info_id']);
                if($pussh){
	                $this->myLogger("发送 push 消息成功 staff_id=" . $staff['manager_id'], "info");
                }else{
	                $this->myLogger("发送 push 失败 staff_id=" . $staff['manager_id'], "info");
                }
                
            } catch (\Exception $e) {
                $db->rollback();
                //实习只进入一直，大概就是有重复进入的报错。改成info
                $this->myLogger("staff=" . $staff['staff_info_id'] . " first insert fail,message=" . $e->getMessage(), "info");
            }
        }


        $this->myLogger("probation staffs first" . $hire_date_begin . "===" . $hire_date_end . "======end");


    }

    /**
     * 第二阶段评估插入
     * 每天执行，
     * @param array $params
     */
    public function secondAction(array $params=[])
    {
        $probation_channel_type = !empty($params[0]) && trim($params[0]) == 'non_frontLine' ? 2 : 1;

        $bll = new ProbationServer($this->lang, $this->add_hour);
        $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        $hire_date_begin = $this->getDateByDays($start, $this->second_days);
        $hire_date_end = $this->getDateByDays($start, $this->second_days - 1);
        $staffs = $bll->getStaffs($hire_date_begin, $hire_date_end,0,0,$this->job_grade,0,$probation_channel_type); // 查询 14 级及以下的人
	
	    //查询 14 级以上的数据
	    $hire_date_begin = $this->getDateByDays($start, $this->second_days_two);
	    $hire_date_end = $this->getDateByDays($start, $this->second_days_two - 1);
	    $staffs_two = $bll->getStaffs($hire_date_begin, $hire_date_end,0,$this->job_grade,'',0,$probation_channel_type);//查询 14 级以上的人
	    $staffs = array_merge($staffs,$staffs_two);//合并两个数据组
        
        
        if (empty($staffs)) {
            $this->myLogger("no probation staffs second  " . $hire_date_begin . "===" . $hire_date_end . "=====end");
            return;
        }

        $staffIds = array_column($staffs,'staff_info_id');
        $nonFrontLineProbationStaff = $probation_channel_type == HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE ? $bll->getNonFrontLineProbationStaff($staffIds,2) : [];
        
        $db = $this->getDI()->get("db");

        foreach ($staffs as $staff) {
            if (!empty($staff['status']) && $staff['status'] == $bll::STATUS_FORMAL){
                continue;
            }
            if (
                !empty($staff['cur_level']) &&
                $staff['cur_level'] == $bll::CUR_LEVEL_SECOND &&
                !empty($staff['second_audit_status']) &&
                $staff['second_audit_status'] != HrProbationModel::SECOND_AUDIT_STATUS_WAIT
            ){
                // 已执行过的数据
                continue;
            }
            if (empty($staff['manager_id'])) {
                $this->myLogger("staff= " . $staff['staff_info_id'] . " manager_id is null");
                continue;
            }


            $item = $bll->getLastestProbationAudit($staff['staff_info_id']);

            //最新的item为空||或者不是第一次评审||或者还是待处理
            if (empty($item) || $item['cur_level'] != 1 || $item['audit_status'] == 1) {
                $this->myLogger("staff= " . $staff['staff_info_id'] . " 第一次评审没有完成或者已经进入第二次评审==".$item['cur_level']."==".$item['audit_status']);
                continue;
            }

            if (
                isset($nonFrontLineProbationStaff['probation_data'][$staff['staff_info_id']]) &&
                $nonFrontLineProbationStaff['probation_data'][$staff['staff_info_id']] == HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE &&
                isset($nonFrontLineProbationStaff['probation_staff']) &&
                !in_array($staff['staff_info_id'],$nonFrontLineProbationStaff['probation_staff'])
            ){
                if (empty($nonFrontLineProbationStaff['target'][$staff['staff_info_id']]['target_info'])){
                    feishu_push("MY,该员工目标未制定 staff_info_id:{$staff['staff_info_id']}",'https://open.feishu.cn/open-apis/bot/v2/hook/355f5d1f-3bfe-4218-a9d9-9554837af929');
                    continue;
                }
                $target_info = $nonFrontLineProbationStaff['target'][$staff['staff_info_id']]['target_info'];
                $tpl_id = null;
            }else{
                $target_info = $item['score'];
                $tpl_id = $item['tpl_id'] ?? null;
            }


            try {
                $db->begin();
                $res = $db->insertAsDict("hr_probation_audit", [
                    "probation_id" => $item['probation_id'],
                    "staff_info_id" => $staff['staff_info_id'],
                    'cur_level' => 2,
                    'tpl_id' => $tpl_id,
                    'score' => $target_info,
                    'audit_id' => $staff['manager_id'],
                    'created_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'deadline_at' => $this->getDateByDays($start, 6, 1),     //  1. 第二阶段上级：6 天以后结束
                    'second_deadline_at'=> $this->getDateByDays($start, 6, 1),//发送给上上级的时间
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'show_time' => date("Y-m-d H:i:s"),
                ]);
                if (!$res) {
                    throw new \Exception("hr_probation_audit insert fail");
                }

                $res = $db->updateAsDict("hr_probation", [
                    "cur_level" => 2,
                    "is_active" => HrProbationModel::IS_ACTIVE_DEFAULT,
                    'second_audit_status' => HrProbationModel::SECOND_AUDIT_STATUS_RUN,
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600)
                ], ["conditions" => "staff_info_id = ".$staff['staff_info_id']]);
                if (!$res) {
                    throw new \Exception("hr_probation update fail");
                }

                $db->commit();

                //发送push
	            $pussh = $bll->push_notice_higher($staff['manager_id'],$staff['staff_info_id']);
	
	            if($pussh){
		            $this->myLogger("发送 push 消息成功 staff_id=" . $staff['manager_id'], "info");
	            }else{
		            $this->myLogger("发送 push 失败 staff_id=" . $staff['manager_id'], "info");
	            }
	            
            } catch (\Exception $e) {
                $db->rollback();
                $this->myLogger("staff=" . $staff['staff_info_id'] . " second insert fail,message=" . $e->getMessage(), "error");
            }
        }

        $this->myLogger("probation staffs second" . $hire_date_begin . "===" . $hire_date_end . "======end");
    }


    /**
     * 第二阶段延长试用期评估插入，我这个去掉了
     * 每天执行，
     * @param array $params
     */
    public function second_delayAction(array $params)
    {
        $start = "";
        if (isset($params[0])) {
            $start = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }

        $bll = new ProbationServer($this->lang, $this->add_hour);
        if (empty($start)) {
            $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        }
        $hire_date_begin = $this->getDateByDays($start, $this->second_delay_days);
        $hire_date_end = $this->getDateByDays($start, $this->second_delay_days - 1);
        $staffs = $bll->getStaffs($hire_date_begin, $hire_date_end,1,0,$this->job_grade); // 查询 14 级及以下的人
	    //查询 14 级以上的数据
	    $hire_date_begin = $this->getDateByDays($start, $this->second_delay_days_two);
	    $hire_date_end = $this->getDateByDays($start, $this->second_delay_days_two - 1);
	    $staffs_two = $bll->getStaffs($hire_date_begin, $hire_date_end,0,$this->job_grade);//查询 14 级以上的人
	    $staffs = array_merge($staffs,$staffs_two);//合并两个数据组
	    
	    
        if (empty($staffs)) {
            $this->myLogger("no probation staffs second delay" . $hire_date_begin . "===" . $hire_date_end . "=====end");
            return;
        }

        $db = $this->getDI()->get("db");

        foreach ($staffs as $staff) {
            if (empty($staff['manager_id'])) {
                $this->myLogger("staff= " . $staff['staff_info_id'] . " manager_id is null");
                continue;
            }


            $item = $bll->getLastestProbationAudit($staff['staff_info_id']);

            //最新的item为空||或者不是第一次评审||或者还是待处理
            if (empty($item) || $item['audit_status'] == 1) {
                $this->myLogger("staff= " . $staff['staff_info_id'] . " 第二次评审没有完成");
                continue;
            }


            try {
                $db->begin();
                $res = $db->insertAsDict("hr_probation_audit", [
                    "probation_id" => $item['probation_id'],
                    "staff_info_id" => $staff['staff_info_id'],
                    'cur_level' => 2,
                    'tpl_id' => $item['tpl_id'],
                    'score' => $item['score'],
                    'audit_id' => $staff['manager_id'],
                    'created_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'deadline_at' => $this->getDateByDays($start, 5, 1),     //第二次上级评审截止5天后
                    'second_deadline_at'=> $this->getDateByDays($start, 5, 1),
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'show_time' => date("Y-m-d H:i:s"),
                ]);
                if (!$res) {
                    throw new \Exception("hr_probation_audit insert fail");
                }

                $res = $db->updateAsDict("hr_probation", [
                    "cur_level" => 2,
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600)
                ], ["conditions" => "staff_info_id = ".$staff['staff_info_id']]);
                if (!$res) {
                    throw new \Exception("hr_probation update fail");
                }

                $db->commit();
            } catch (\Exception $e) {
                $db->rollback();
                $this->myLogger("staff=" . $staff['staff_info_id'] . " second insert fail,message=" . $e->getMessage(), "error");
            }
        }
        $this->myLogger("probation staffs second" . $hire_date_begin . "===" . $hire_date_end . "======end");
    }


    /**
     * 处理快到截止日期的员工，如7.26日执行，>=7.26 and <7.27
     * 每天凌晨执行
     */
    //处理 快到截止日期的员工，每天凌晨，执行一次，判断截止日期，到今天的。
    public function deal_deadlineAction($params=[])
    {

        $bll = new ProbationServer($this->lang, $this->add_hour);
        if (isset($params[0])) {
            $start = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }
        if (empty($start)) {
            $start = gmdate("Y-m-d", time() + ($this->add_hour) * 3600);
        }
        $end = $this->getDateByDays($start, 1, 1);


        $staffs = $bll->getStaffsByDeadlineDate($start, $end);

        if (empty($staffs)) {
            $this->myLogger("no probation staffs on deadline between" . $start . "===" . $end . "=====end");
            return;
        }

        $staffIds = array_column($staffs, "id");
        $strStaffIds = implode(",", $staffIds);

        $db = $this->getDI()->get("db");
        try {
            $db->begin();
            foreach ($staffs as $hrProbationAudit) {
                if ($hrProbationAudit['cur_level'] == ProbationServerAlias::CUR_LEVEL_FIRST) {
                    $updateData = [
                        'first_audit_status' => HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT,
                        'updated_at'         =>  gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    ];
                } else {
                    $updateData = [
                        'second_audit_status' => HrProbationModel::SECOND_AUDIT_STATUS_TIMEOUT,
                        'updated_at'          => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    ];
                }
                $updateData['is_active'] = HrProbationModel::IS_ACTIVE_DEFAULT;
                $db->updateAsDict("hr_probation", $updateData,
                    ["conditions" => 'id =' . $hrProbationAudit['probation_id']]);
            }
            $res = $db->updateAsDict("hr_probation_audit", [
                "audit_status" => 3,
                'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                'show_time' => date("Y-m-d H:i:s"),
            ], ["conditions" => "id in (" . $strStaffIds . ")"]);
            if (!$res) {
                throw new \Exception("hr_probation_audit update fail");
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->myLogger("staff=" . $strStaffIds . "deal_deadline fail,message=" . $e->getMessage(), "error");
        }

        $this->myLogger("probation staffs on deadline between" . $start . "===" . $end . " ======end");
    }


    /**
     * 第二阶段的提醒60,61（60,61）两天，每隔5小时推送一次
     * 每天7:00,12:00,17:00执行
     */
    public function send_msg_to_higherAction()
    {
        $bll = new ProbationServer($this->lang, $this->add_hour);

        $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);

        //开始时间=截止日期比当前时间多1天
        //结束时间=截止日期比当期时间多3天（比开始时间多2天）
        $start = $this->getDateByDays($start, 1, 1);//大于等于

        $end = $this->getDateByDays($start, 2, 1);//<

        //找第二次评审，待评估的用户发送信息，都是截止日期前两天
        $staffs = $bll->getSecondStaffsByDeadlineDate($start, $end);

        if (empty($staffs)) {
            $this->myLogger("send msg to higher:no probation staffs on deadline between" . $start . "===" . $end . "=====end");
            return;
        }

        $auditIdArr = array_column($staffs, "audit_id");
        $auditIdArr = array_unique($auditIdArr);

        //您有即将超时的员工转正评估未处理，请尽快前往backyard-转正评估进行评估，若未在规定时间内完成评估，可能存在因失职而收到警告书的风险。

        /**
         * "{"locale":"en","data":{"staff_info_id":"17074","src":"1","message_title":"需要push的标题","message_content"："需要push的内容","message_scheme":"消息的scheme"}}"
         */

        //$approval_html_url  = env('probation_html_url','flashbackyard://fe/html?url=');
        //$approval_index_url= env('probation_index_url','http://192.168.0.222:90/#/EvaluateIndex');
        $approval_html_url = $bll->get_setting_by_code('probation_html_url');
        $approval_index_url = $bll->get_setting_by_code('probation_index_url');


        foreach ($auditIdArr as $k => $v) {
            $data = [];
            $data['staff_info_id'] = $v;
            $data['src'] = 'backyard';
            $data['message_title'] = $bll->getMsgTemplateByUserId($v,'hr_probation_field_msg_notice');
            $data['message_content'] = $bll->getMsgTemplateByUserId($v,'hr_probation_field_msg_to_higher');
            $data['message_scheme'] = $approval_html_url . urlencode($approval_index_url);
            
	        $this->getDI()->get('logger')->write_log('send_msg_to_higherAction_pushMessage params:' . json_encode($data), 'info');
	        $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
	        $ret->setParams($data);
	        $_data = $ret->execute();
	        $this->getDI()->get('logger')->write_log("send_msg_to_higherAction_pushMessage:pushMessage-return- " . json_encode($_data), 'info');
	        if (!$_data['result']) {
                $this->myLogger("higher:staff_info_id=" . $v . "  发送push失败", "info");
            }else{
		        $this->myLogger("第二阶段提醒 给上级发送 higher:staff_info_id=" . $v . "  发送push成功", "info");
	        }
        }

        $this->myLogger("send msg to higher:probation staffs on deadline between" . $start . "===" . $end . "======end");
    }


    /**
     * 第二阶段的提醒HRBP,85天，（80根据second_deadline_at判断)，
     * 每天7:00执行
     */
    public function send_msg_to_hrbpAction($params)
    {
    	
        $bll = new ProbationServer($this->lang, $this->add_hour);

        if (empty($params[0])) {
            $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        } else {
            $start = $params[0].' 00:00:00';
        }
        //开始时间=截止日期比当前时间多1天
        //结束时间=截止日期比当期时间多2天（比开始时间多一天）
        $start = $this->getDateByDays($start, 1, 1);//大于等于
        $end = $this->getDateByDays($start, 1, 1);//<


        //找第二次评审
        $staffs = $bll->getSecondStaffsByDeadlineDate($start, $end);
        if (empty($staffs)) {
            $this->myLogger("send msg to hrbp:no probation staffs on deadline " . $start .  "=====end");
            return;
        }
        //被评估人
        $evaluatedPerson = array_column($staffs, "staff_info_id");
        $evaluatedPerson = array_unique($evaluatedPerson);
        $hrbpMap = $bll->findHRBP($evaluatedPerson);
        if (empty($hrbpMap)) {
            $this->myLogger("send msg to hrbp:not found hrbp from staff id in (" . implode(",", $evaluatedPerson) . ')');
            return;
        }


        $languageMap =  (new \FlashExpress\bi\App\Server\StaffServer())->getBatchStaffLanguage(array_keys($hrbpMap));
        foreach ($hrbpMap as $hrbpId => $staffIds) {
            $this->myLogger(json_encode(['hrbp'=>$hrbpId,'被评估人'=>$staffIds],JSON_UNESCAPED_UNICODE));
            $bll->sendPushMessageToHrbp($languageMap[$hrbpId],$hrbpId,$staffIds);
        }

        //根据网点，部门对应出HRBP的关系
        //您负责的区域员工有转正评估未完成，请尽快提醒该员工上级进行转正评估，详情查看FBI-员工试用期管理
        $this->myLogger("send msg to hrbp:probation staffs on deadline " . $start  . "=====end");
    }
	
	
	public function getStaffsByFormalDate($start, $end)
	{
		$sql = "
            select
                hsi.staff_info_id,
                hsi.name,
                hsi.sys_store_id,
                hsi.node_department_id,
                hp.formal_at,
                hp.status,
                job.job_name,
                item.`value` as higher_id,
                hsi.sys_department_id,
                hsi.sys_store_id
            from
               hr_probation as hp
            left join
                hr_staff_info as hsi on hsi.staff_info_id = hp.staff_info_id
            left join
                hr_job_title as job on hsi.job_title = job.id
            left join
              hr_staff_items AS item ON item.staff_info_id = hsi.staff_info_id AND item.item = 'MANGER'
            
            where
               hp.formal_at>=:start
               and hp.formal_at<=:end
			   and hp.status in (2,3) -- 已通过和未通过
        ";
		return $this->getDI()->get("db_rby")->query($sql, ["start" => $start, "end" => $end])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
	}
	

    /**
     * 发送结果给员工
     * 已通过 发送该给 员工和的 hrbp
     * 未通过 发送该给的上级和 hrbp
     * 每天执行
     * !!! 20911 需求 关停这个任务 https://flashexpress.feishu.cn/wiki/UrVfwELMViBG0Wkj7zYc3u10nvg
     */
    public function send_msg_to_staffAction($params=[])
    {
        return true;
        $bll = new ProbationServer($this->lang, $this->add_hour);
        if (isset($params[0])) {
            $to_day = $params[0];
            if (strtotime($to_day) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }

        //今天
        if(empty($to_day)){
            $to_day = gmdate('Y-m-d', time() + ($this->add_hour) * 3600);
        }
	    $start = $end = $this->getDateByDays($to_day, 5, 1);//取 5 天后转正的 发消息

        $staffs = $this->getStaffsByFormalDate($start, $end);
        if (empty($staffs)) {
            $this->myLogger("没有找到5 天后的数据 send msg to staff:no probation staffs on formal_at between" . $start . "===" . $end . "=====end");
            return;
        }

//        $staffIdArr = array_column($staffs, "staff_info_id");
//        $flag = $bll->formalStaffs($staffIdArr);
//
//        if (!$flag) {
//            $this->myLogger("send msg to staff: formal staff_info_id in (" . implode(",", $staffIdArr) . ")", "error");
//            return;
//        }
        $dept_list = SysDepartmentModel::find(['columns' => 'id,name'])->toArray();
        $dept_list_map = array_column($dept_list,'name','id');

        $store_list = SysStoreModel::find(['columns' => 'id,name'])->toArray();
        $store_list_map = array_column($store_list,'name','id');


        foreach ($staffs as $staff) {

//            $bll->putFormalLog(-1,$staff['staff_info_id'],2,4);

            /*$html = <<<EOF
   DATE
		
Dear NAME,

PROBATION SUCCESSFUL

We wish to inform that you have satisfactorily completed your probationary period and the company is pleased to confirm you in your appointment as JOB TITLE with effect from CONFIRMATION DATE.

The terms and conditions as in your employment contract will remain unchanged.

We hope that you will continue to give your best to the company this would be the beginning of a long and happy association between you and the Company.

Thank you.
EOF;*/
	        //找 hrbp
	        $staff_info_ids = [];
	        $findHRBP = (new WorkflowServer($this->lang, $this->timezone))->findHRBP($staff['sys_department_id'], ["store_id" => $staff['sys_store_id']]);
	        if (!empty($findHRBP)){
		        $staff_info_ids = explode(',', $findHRBP);
	        }
	        
	        if($staff['status'] == ProbationServer::STATUS_NOT_PASS){   // 未通过
				$html = $bll->getMsgTemplateByUserId($staff['staff_info_id'],"hr_probation_field_msg_to_not_pass",['name'=>$staff['name'],'staff_info_id'=>$staff['staff_info_id']]);
				//发送给员工上级
				$staff_info_ids[] = $staff['higher_id'];
			}else {
                if($staff['sys_store_id'] == '-1'){
                    //总部员工部门展示信息逻辑
                    $province_code =  '';
                    $staffProvince = HrStaffItemsModel::findFirst([
                        'conditions' => "staff_info_id = :staff_id: and item = 'STAFF_PROVINCE_CODE'",
                        'bind' => [
                            'staff_id'  => $staff['staff_info_id'],
                        ]
                    ]);
                    if($staffProvince){
                        $province_code = $staffProvince->value;
                        $pro_info = SysProvinceModel::findFirst([
                            'conditions' => "code = :code:",
                            'bind' => [
                                'code'  => $province_code,
                            ]
                        ]);
                        if($pro_info){
                            $pro_name = $pro_info->name;
                        }
                    }
                    $department_info = ($dept_list_map[$staff['node_department_id']] ?? '')."/".enums::HEAD_OFFICE." (".($pro_name ?? '').")";
                }else{
                    //非总部员工部门展示信息
                    $department_info = ($dept_list_map[$staff['node_department_id']] ?? '').'/'.($store_list_map[$staff['sys_store_id']] ?? '');
                }
	            $bind = [
	                'date_at' => $to_day,
                    'staff_name' => $staff['name'],
                    'staff_id' => $staff['staff_info_id'],
                    'department_info' =>$department_info ,
                    'name' => $staff['name'],
                    'job_name'=>$staff['job_name'],
                    'formal_at'=>$staff['formal_at']
                ];
				$html = $bll->getMsgTemplateByUserId($staff['staff_info_id'], "hr_probation_field_msg_to_staff_my_new", $bind);
				//$staff_info_ids[] = $staff['staff_info_id']; //发送给员工
			}
            //todo 给员工发送限制打卡类型消息
            $param['staff_users'] = [$staff['staff_info_id']];//数组 多个员工id
            $param['message_title'] = $bll->getMsgTemplateByUserId($staff['staff_info_id'],'hr_probation_field_msg_to_staff_title');
            $param['message_content'] = $html;
            $param['staff_info_ids_str'] = $staff['staff_info_id'];
            $param['id'] = time() . $staff['staff_info_id'] . rand(1000000, 9999999);
            $param['category'] = MessageEnums::MESSAGE_CATEGORY_TRANSFER_EVALUATION_MY_V2;//消息为 55  需要员工点击我知道了

            $this->getDI()->get('logger')->write_log('send_msg_to_staffAction员工本人:param:' . json_encode($param), 'info');
            $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
            $bi_rpc->setParams($param);
            $res = $bi_rpc->execute();
            $this->getDI()->get('logger')->write_log('send_msg_to_staffAction员工本人-result:' . json_encode($res), 'info');
            if ($res && $res['result']['code'] == 1) {
                $kitId    = $res['result']['data'][0];
                $this->myLogger('send_msg_to_staffAction message_backyard  写入message成功' . $staff['staff_info_id']." message_id".$kitId, 'info');
            }else{
                $this->myLogger('send_msg_to_staffAction message_backyard  写入message失败' . $staff['staff_info_id'],'error');
            }

			//todo 给hrbp或上级发送 不限制打卡类型消息
	        foreach($staff_info_ids as $kk=>$vv){
		        $staff_info_id = $vv;
		        $id = time() . $staff_info_id . rand(1000000, 9999999);
		        $param['staff_users'] = [$staff_info_id];//数组 多个员工id
		        $param['message_title'] = $bll->getMsgTemplateByUserId($staff['staff_info_id'],'hr_probation_field_msg_to_staff_title');
		        $param['message_content'] = $html;
		        $param['staff_info_ids_str'] = $staff_info_id;
		        $param['id'] = $id;
		        $param['category'] = -1;
		        //            $param['category'] = 43; //消息为 43  需要员工点击我知道了
		
		        $this->getDI()->get('logger')->write_log('send_msg_to_staffAction-param:' . json_encode($param), 'info');
		        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
		        $bi_rpc->setParams($param);
		        $res = $bi_rpc->execute();
		        $this->getDI()->get('logger')->write_log('send_msg_to_staffAction-result:' . json_encode($res), 'info');
		        if ($res && $res['result']['code'] == 1) {
			        $kitId    = $res['result']['data'][0];
			        $this->myLogger('send_msg_to_staffAction message_backyard  写入message成功' . $staff_info_id." message_id".$kitId, 'info');
		        }else{
			        $this->myLogger('send_msg_to_staffAction message_backyard  写入message失败' . $staff_info_id, 'info');
		        }
	        }

         
	        
        }

        $this->myLogger("send msg to staff: probation staffs on formal_at between" . $start . "===" . $end . "=====end");
    }
	
	
	/**
	 * 给已通过的员工，并修改状态成，已转正
	 *
	 * 每天执行
	 */
	public function save_probation_staffAction($params=[])
	{
        $probation_channel_type = !empty($params[0]) && trim($params[0]) == 'non_frontLine' ? 2 : 1;
        
		$bll = new ProbationServer($this->lang, $this->add_hour);
        $start = gmdate('Y-m-d', time() + ($this->add_hour) * 3600);

		$end = $this->getDateByDays($start, 1, 1);//1 天后转正人的

        $staffs = $bll->getStaffsByFormalDate($start, $end,[2],true,$probation_channel_type);
        $staffs = array_filter($staffs, function ($v) {
            return !(
                $v['status'] == HrProbationModel::STATUS_FORMAL &&
                $v['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE &&
                $v['second_status'] == HrProbationModel::SECOND_STATUS_PASS
            );
        });
		if (empty($staffs)) {
			$this->myLogger("修改转正,没有数据 日期" . $start . "===" . $end . "=====end");
			return;
		}

        $staffIdArr = array_column($staffs, "staff_info_id");
        $messagePdf = MessagePdfModel::find([
            'conditions' => "staff_info_id in ({staff_info_id:array}) and module_category = :module_category:",
            'bind'       => ['staff_info_id' => $staffIdArr,'module_category'=>MessagePdfModel::MODULE_CATEGORY_CONFIRMATION_LETTER],
        ])->toArray();
        $messagePdf = array_column($messagePdf, null, 'staff_info_id');
        foreach ($staffs as $k => $v) {
            // 这里只判断新数据 老数据message_pdf里没有 新数据未签字的不转正
            if (!empty($messagePdf[$v['staff_info_id']]) && $messagePdf[$v['staff_info_id']]['state'] != MessagePdfModel::STATE_SUCCESS) {
                unset($staffs[$k]);
            }
        }
        $staffs = array_values($staffs);

        $_cur_level = 2;
        foreach ($staffs as $staff) {
            $db = $this->getDI()->get("db");
            $db->begin();
            try {
                // 发送阶段完成发送签字消息 被评估人和被评估人的上级
                if (!empty($staff['probation_channel_type']) && $staff['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE && empty($staff['second_stage_done_msg_id'])){
                    $rpc_params = [
                        'staff_info_id'       => $staff['staff_info_id'],
                        'customize_cur_level' => $_cur_level,
                    ];
                    $this->logger->write_log('send_probation_stage_done_message data  ' . json_encode($rpc_params), "info");
                    $this->redisLib->lpush(\FlashExpress\bi\App\Enums\RedisEnums::SEND_PROBATION_STAGE_DONE_MESSAGE, json_encode($rpc_params));
                }
                $bll->putFormalLog(-1,$staff['staff_info_id'],2,4);
                $db->commit();
                $this->logger->write_log('send_msg_to_staffAction staff_info_id:' . $staff['staff_info_id'] . ' message: 转正成功', "info");
            } catch (BusinessException|\Exception $e) {
                $db->rollback();
                $this->logger->write_log('send_msg_to_staffAction staff_info_id:' . $staff['staff_info_id'] . ' message: 转正失败 error:' . $e->getMessage(), "error");
                continue;
            }
        }
        $this->myLogger("save_probation_staffAction formal_at between" . $start . "===" . $end . "=====end");
	}

    /**
     *获得考勤
     *每天执行
     * 纪律考核
     */
    public function get_attendanceAction()
    {
        $bll = new ProbationServer($this->lang, $this->add_hour);

        $now = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
	
        //查询 14 级一下的
	    $start_1 = $this->getDateByDays($now, 45);     //>=45 天之前
        $end_1 = $this->getDateByDays($now, 44); //<44天

        $start_2 = $this->getDateByDays($now, 90);
        $end_2 = $this->getDateByDays($now, 89);

        $staffs_1 = $bll->getStaffs($start_1, $end_1,0,0,$this->job_grade); // 查询 14 级及以下的人
        $staffs_2 = $bll->getStaffs($start_2, $end_2,0,0,$this->job_grade); // 查询 14 级及以下的人
	
        //查询 14 级以上的
	    $start_1 = $this->getDateByDays($now, 90);     //>=45 天之前
	    $end_1 = $this->getDateByDays($now, 89); //<44天
	
	    $start_2 = $this->getDateByDays($now, 180);
	    $end_2 = $this->getDateByDays($now, 179);
	
	    $staffs_1_two = $bll->getStaffs($start_1, $end_1,0,$this->job_grade); // 查询 14 级及以上的人
	    $staffs_2_two = $bll->getStaffs($start_2, $end_2,0,$this->job_grade); // 查询 14 级及以上的人
	    $staffs_1  = array_merge($staffs_1,$staffs_1_two);//合并两个数据组
	    $staffs_2  = array_merge($staffs_2,$staffs_2_two);//合并两个数据组
	
	
	
	
	
	    if (empty($staffs_1) && empty($staffs_2)) {
            $this->myLogger("get_attendance staffs on " . $start_1 . "===" . $end_1 . " and " . $start_2 . "===" . $end_2 . " 为空，不用执行" . "=====end");
            return;
        }

        $data = [];

        if (!empty($staffs_1)) {
            foreach ($staffs_1 as $staff) {
                $tmp = [];
                $tmp['cur_level'] = 1;
                $tmp['staff_info_id'] = $staff['staff_info_id'];
                $data[] = $tmp;
            }
        }

        if (!empty($staffs_2)) {
            foreach ($staffs_2 as $staff) {
                $tmp = [];
                $tmp['cur_level'] = 2;
                $tmp['staff_info_id'] = $staff['staff_info_id'];
                $data[] = $tmp;
            }
        }


        foreach ($data as $staff) {
            if ($bll->isHaveAttendance($staff['staff_info_id'], $staff['cur_level'])) {
                continue;
            }
            $flag = $bll->addAttendance($staff);
            if (!$flag) {
                $this->myLogger("get_attendance staffs==add attendance error==" . $staff['staff_info_id'], "error");
            }
        }
        $this->myLogger("get_attendance staffs on " . $start_1 . "===" . $end_1 . " and " . $start_2 . "===" . $end_2 . "=====end");
    }

    /*
     * 发送给上级的上级，提示有人没评估
     */
    public function send_msg_to_higher_of_higherAction(){

        $bll = new ProbationServer($this->lang, $this->add_hour);

        $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);


        //截止81天，减一天
        //10-24的时候，找截止时间是25号的
        $start = $this->getDateByDays($start, 1, 1);//大于等于
        $end = $this->getDateByDays($start, 1, 1);//<

        //找第二次评审，待评估的用户发送信息，都是截止日期前两天
        $staffs = $bll->getSecondStaffsBySecondDeadlineDate($start, $end);

        if (empty($staffs)) {
            $this->myLogger("send_msg_to_higher_of_higher:no probation staffs on deadline between" . $start . "===" . $end . "=====end");
            return;
        }

        //员工xxxxx的转正评估，其上级xxxxx尚未进行评估，请及时联系该上级进行评估
     

        foreach ($staffs as $k => $v) {
            if(empty($v['higher_id'])){
                $this->myLogger("send_msg_to_higher_of_higher:staff_info_id=" . $v['staff_info_id'] . "===上级ID=".$v['manager_id']."====没有直线上级", "info");
                continue;
            }


            $staff_info_id = $v['higher_id'];
            $id = time() . $staff_info_id . rand(1000000, 9999999);
            $param['staff_users'] = array($staff_info_id);//数组 多个员工id
            $param['message_title'] = $bll->getMsgTemplateByUserId($v['higher_id'],'hr_probation_field_msg_notice');
            $param['message_content'] =  $bll->getMsgTemplateByUserId($v['higher_id'],'hr_probation_field_msg_to_higher_of_higher',$v);
            $param['staff_info_ids_str'] = $staff_info_id;
            $param['id'] = $id;
            $param['category'] = -1;
            
	        $this->getDI()->get('logger')->write_log('send_msg_to_higher_of_higherAction-param:' . json_encode($param), 'info');
	        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
	        $bi_rpc->setParams($param);
	        $res = $bi_rpc->execute();
	        $this->getDI()->get('logger')->write_log('send_msg_to_higher_of_higherAction-result:' . json_encode($res), 'info');
	        if ($res && $res['result']['code'] == 1) {
		        $kitId    = $res['result']['data'][0];
		        $this->myLogger('send_msg_to_higher_of_higherAction message_backyard  写入message成功' . $staff_info_id." message_id".$kitId, 'info');
	        }else{
		        $this->myLogger('send_msg_to_higher_of_higherAction message_backyard  写入message失败' . $staff_info_id, 'info');
	        }
        }

        $this->myLogger("send_msg_to_higher_of_higher:probation staffs on deadline between" . $start . "===" . $end . "======end");
    }


    function add_menu_permissionAction()
    {
        $staff_ids = (new ProbationServer($this->lang, $this->add_hour))->get_setting_by_code("probation_staff_ids");
        if (empty($staff_ids)) {
            $this->myLogger("add menu error=staff_ids is null");
            return;
        }

//        $sql = "select staff_info_id,count(*) as num from role_staff_menus where staff_info_id in (" . $staff_ids . ") group by staff_info_id";
//        $tArr = $this->getDI()->get('db_rbi')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
	    $tArr = RoleStaffMenusModel::find([
		                                     'conditions' => ' staff_info_id in ({ids:array}) ',
		                                     'bind' => [
			                                     'ids' => $staff_ids
		                                     ],
		                                     'columns' => ' staff_info_id,count(*) as num ',
		                                     'group' => 'staff_info_id'
	                                     ])->toArray();
	    
        $staff_num = array_column($tArr, "num", "staff_info_id");

        $staff_data = explode(",", $staff_ids);

        $insert = [];

        $staffIdsByRole = [];

        foreach ($staff_data as $staff_id) {
            //如果通过工号配置过
            if (!empty($staff_num[$staff_id])) {
                $row = [];
                $row['staff_info_id'] = $staff_id;
                $row['menu_id'] = 293;
                $insert[] = $row;
            } else {
                $staffIdsByRole[] = $staff_id;
            }
        }

        //根据角色的菜单权限
        $roleMenus = [];
        //不确定merge里面的角色是什么，所以要都查一遍，因为每个人有可能有多个角色。
        if (!empty($staffIdsByRole)) {
            foreach ($staffIdsByRole as $staff_id) {
                $sql = "select position_category from hr_staff_info_position where staff_info_id= :staff_id";
	            $roleArr = $this->getDI()->get("db_rby")->query($sql,["staff_id" => $staff_id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
//                $roleArr = $this->getDI()->get('db_rbi')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                $roleArr = array_column($roleArr, "position_category");

                $menuIds = [];

                foreach ($roleArr as $role) {
                    if (empty($roleMenus[$role])) {
                        $sql = "select * from role_menus where role_id= :role_id";
	                    $tArr = $this->getDI()->get("db_rbi")->query($sql,["role_id" => $role])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
//                        $tArr = $this->getDI()->get('db_rbi')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                        $roleMenus[$role] = array_column($tArr, "menu_id");
                    }
                    $menuIds = array_merge($menuIds, $roleMenus[$role]);
                }

                $menuIds[] = 293;
                $menuIds = array_unique($menuIds, SORT_NUMERIC);

                foreach ($menuIds as $id) {
                    $row = [];
                    $row['staff_info_id'] = $staff_id;
                    $row['menu_id'] = $id;
                    $insert[] = $row;
                }
            }
        }


        
        $flag = $this->table_batch_insert($insert, 'db_bi', 'role_staff_menus');
        if ($flag) {
            $this->myLogger('试用期员工菜单刷权限' . json_encode($insert), 'info');
        }
    }

    public function myLogger($str, $level = "info")
    {
        echo $str . PHP_EOL;
        $now = gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600);
        $this->getDI()->get('logger')->write_log("ProbationTask on " . $now . ":" . $str, $level);
    }


    public function flush_cur_levelAction(){
        $db = $this->getDI()->get("db");
        $sql = "SELECT `staff_info_id` from `hr_probation_audit` where `cur_level` =2 GROUP BY `staff_info_id` ";
        $res = $db->fetchAll($sql);
        if(empty($res)){
            return;
        }

        $ids = array_column($res,"staff_info_id");
        $sql = "update hr_probation set cur_level=2 where staff_info_id in (".implode(",",$ids).")";
        $res = $db->execute($sql);

        if($res){
            echo "执行成功".PHP_EOL;
        }else{
            echo "执行失败".PHP_EOL;
        }
    }
	
	
	
	/**
	 * 获得日期
	 * @param $date
	 * @param $days 40
	 * @param int $flag 0=40天之前，1=40天之后
	 * @return false|string
	 */
	public function getDateByDays($date, $days, $flag = 0)
	{
		
		$default = '-';
		if (!empty($flag)) {
			$default = '+';
		}
		return date("Y-m-d", strtotime($default . $days . " days", strtotime($date)));
	}
	
	
	
	/**
	 * 批量插入数据，指定表名
	 * @param array $data 要插入的数据，键为字段名，值 为字段值
	 * @return bool
	 * @throws \Exception
	 */
	public function table_batch_insert(array $data, $db = 'db_bi', $table)
	{
		if (!is_array($data) || count($data) == 0) {
			return false;
		}
		$keys = array_keys(reset($data));
		$keys = array_map(function ($key) {
			return "`{$key}`";
		}, $keys);
		$keys = implode(',', $keys);
		$sql = "INSERT INTO " . $table . " ({$keys}) VALUES ";
		
		foreach ($data as $v) {
			$v = array_map(function ($value) {
				if ($value === null) {
					return 'NULL';
				} else {
					$value = addslashes($value); //处理特殊符号，如单引号
					return "'{$value}'";
				}
			}, $v);
			
			$values = implode(',', array_values($v));
			$sql .= " ({$values}), ";
		}
		$sql = rtrim(trim($sql), ',');
		
		$di = $this->getDI();
		$logger = $di->get('logger');
		$result = false;
		try {
			$result = $this->getDI()->get($db)->execute($sql);
		} catch (\Exception $e) {
			$logger->write_log('bi库中，执行批量导入操作,数据库执行结果：失败，原因是：' . $e->getMessage() . ' sql:' . $sql, 'error');
		}
		return $result;
	}
	
	
	/**
	 * 根据审批人id，找对应的hrbp 这是员工转正评估用的
	 * @param $staffInfoIds
	 * @return array
	 */
	
	public function getHRBP($staffInfoIds)
	{
		$sql = "select staff_info_id,sys_department_id,sys_store_id from hr_staff_info where staff_info_id in (" . implode(",", $staffInfoIds) . ")";
		$arr = $this->getDI()->get("db_rby")->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
		if (empty($arr)) {
			return [];
		}
		
		$hrbp = [];
		foreach($arr as $item){
			$findHRBP = (new WorkflowServer($this->lang, $this->timezone))->findHRBP($item['sys_department_id'], ["store_id" => $item['sys_store_id']]);
			if(!empty($findHRBP)){
				$hrbp = array_merge(explode(',', $findHRBP),$hrbp);
			}
		
		}
		return array_unique($hrbp);
	}
	
	

	/**
	 * 老的数据刷进去，直接第一次评审超时
	 */
	public function add_old_staffAction(array $params)
	{
		
		if (isset($params[0])) {
			$hire_date_begin = $params[0];    //大于等于
		}else{
			$hire_date_begin = "2021-03-01";    //大于等于
		}
		$bll = new ProbationServer($this->lang, $this->add_hour);
		
		$start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
		
		$hire_date_end = $this->getDateByDays($start, $this->first_days);          //小于
		
		$staffs = $bll->getStaffs($hire_date_begin, $hire_date_end,0,0,$this->job_grade); // 查询 14 级及以下的人
		//查询 14 级以上的数据
		$hire_date_end = $this->getDateByDays($start, $this->first_days_two);  //7.27
		$staffs_two = $bll->getStaffs($hire_date_begin, $hire_date_end,0,$this->job_grade);//查询 14 级以上的人
		$staffs = array_merge($staffs,$staffs_two);//合并两个数据组
		
		
		
	
		if (empty($staffs)) {
			$this->myLogger("no probation staffs add_old_staff" . $hire_date_begin . "===" . $hire_date_end . "=====end");
			return;
		}
		
		$db = $this->getDI()->get("db");
		//第二阶段发送时间
		$second_days = $this->getDateByDays($start, $this->second_days);
		//查询 14 级以上的数据
		$second_days_two = $this->getDateByDays($start, $this->second_days_two);
		
		
		foreach ($staffs as $staff) {
			if (empty($staff['manager_id'])) {
				$this->myLogger("staff= " . $staff['staff_info_id'] . " manager_id is null");
				continue;
			}
			//查询是否存在转正评估 存在则不进行
			$hr_probation_audit =  $bll->getLastestProbationAudit( $staff['staff_info_id']);
			if(!empty($hr_probation_audit)){
				$this->myLogger("staff= " . $staff['staff_info_id'] . " 已存在");
				continue;
			}
			
			try {
				$db->begin();
				
//				$start = $staff['hire_date'];
				//判断此人为什么等级
				$formal_days = (int)$staff['job_title_grade_v2'] <= $this->job_grade ?  $this->formal_days : $this->formal_days_two;
				$formal_at = $this->getDateByDays($staff['hire_date'],$formal_days, 1);
				//判断此人的转正评估是试用期 还是未通过
				//如果过了第二节点发送时间  修改为未通过
				$status = 1;
				//第二阶段可以发送的最晚入职日期
				$days_two = (int)$staff['job_title_grade_v2'] <= $this->job_grade ?  $second_days : $second_days_two;
				//比较看第二阶段发送时间是否已经过去了
				$status = strtotime($staff['hire_date']) > strtotime($days_two) ? 1: 3;
				
				$res = $db->insertAsDict("hr_probation", ["staff_info_id" => $staff['staff_info_id'], "created_at" => $start, 'formal_at' => $formal_at,'status'=>$status]);
				if (!$res) {
					throw new Exception("hr_probation insert fail".$staff['staff_info_id']);
				}
				$this->myLogger("staff= " . $staff['staff_info_id'] . " 插入转正评估表成功 ==> 状态:".$status);
				$probation_id = $db->lastInsertId();
				$res = $db->insertAsDict("hr_probation_audit", [
					"probation_id" => $probation_id,
					"staff_info_id" => $staff['staff_info_id'],
					'audit_id' => $staff['manager_id'],
					'tpl_id' => $bll->getTplIdByJobTitleGradeV2($staff['job_title_grade_v2']),
					'created_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
					'deadline_at' => $this->getDateByDays($start, 4, 1),      //第一次上级评审截止4天以后，23天0点 47天0点
					'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
					"audit_status" => 3, //老的数据直接改成超时
				]);
				
				if (!$res) {
					throw new Exception("hr_probation_audit insert fail".$staff['staff_info_id']);
				}
				$db->commit();
			} catch (\Exception $e) {
				$db->rollback();
				//实习只进入一直，大概就是有重复进入的报错。改成info
				$this->myLogger("staff=" . $staff['staff_info_id'] . " add_old_staff insert fail,message=" . $e->getMessage(), "info");
			}
		}
		$this->myLogger("add_old_staff" . $hire_date_begin . "===" . $hire_date_end . "=====end");
	}
	
	
	
	
	
	
	/**
	 * @description 马来的员工  staff 表 新增工作所在州 原数据员工所属网点为-1 的 刷数据 工作所在州为 吉隆坡
	 *
	 * @param null
	 * 刷数据的任务 不用管
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/12/6 14:39
	 */
	
	
	public function hr_staff_info_province_codeAction(){
		$province_code = 'MY06'; //默认是吉隆坡
		$item = 'STAFF_PROVINCE_CODE';
		//查询需要修改的员工数据
		$staff_list = HrStaffInfoModel::find([      'conditions'=> "sys_store_id = :sys_store_id: ",
		                                             'bind'=>['sys_store_id'=>'-1'],
		                                             'columns' => 'staff_info_id',
		                                            ])->toArray();
		if(empty($staff_list))
			die('没有员工数据');
		//查询工作洲字段是否存在
		$staff_ids = array_column($staff_list,'staff_info_id');
		$staff_items_list = HrStaffItemsModel::find(['conditions'=> "staff_info_id in  ({staff_info_id:array}) and item = :item:",
		                         'bind'=>['staff_info_id'=>$staff_ids,'item'=>$item],
		                         'columns' => 'staff_info_id',
		                        ])->toArray();
		$staff_items_ids = [];
		if($staff_items_list){
			$staff_items_ids = array_column($staff_items_list,'staff_info_id');
		}
		
		
		$insert_data = [];
		foreach($staff_list as $k=>$v){
			//这里判断 是否存在工作所在州
			if(in_array($v['staff_info_id'],$staff_items_ids)){
				echo $this->myLogger("staff= " . $v['staff_info_id'] . " 工作所在州已在 hr_staff_items 存在!");
				continue;
			}
			echo $this->myLogger("staff= " . $v['staff_info_id'] . " 插入 hr_staff_items");
			$insert_data[] = ['staff_info_id'=>$v['staff_info_id'],'item'=>$item,'value'=>$province_code];
		}
		$result = $this->table_batch_insert($insert_data, 'db_bi', 'hr_staff_items');
		 echo '增加成功'.$result;
		
	}
	
	
	/**
	 * @description: 马来 邮件需求 补发转正通知 总计 178 人
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2022/1/10 10:26
	 */
	
	public function reissue_noticeAction()
	{
		//需要发送的人
		$arr = [
			['staff_id'=>73466, 'date'=>'31/08/2021'],
			['staff_id'=>75351, 'date'=>'15/09/2021'],
			['staff_id'=>118723,'date'=> '	02/12/2021'],
			['staff_id'=>118759,'date'=> '	06/12/2021'],
			['staff_id'=>62194, 'date'=>'05/04/2021'],
			['staff_id'=>76954, 'date'=>'18/10/2021'],
			['staff_id'=>78317, 'date'=>'08/11/2021'],
			['staff_id'=>118735,'date'=> '	06/12/2021'],
			['staff_id'=>118882,'date'=> '	20/12/2021'],
			['staff_id'=>79258, 'date'=>'24/11/2021'],
			['staff_id'=>118754,'date'=> '	06/12/2021'],
			['staff_id'=>78315, 'date'=>'08/11/2021'],
			['staff_id'=>78146, 'date'=>'03/11/2021'],
			['staff_id'=>78666, 'date'=>'15/11/2021'],
			['staff_id'=>78695, 'date'=>'15/11/2021'],
			['staff_id'=>78668, 'date'=>'15/11/2021'],
			['staff_id'=>79134, 'date'=>'22/11/2021'],
			['staff_id'=>79141, 'date'=>'22/11/2021'],
			['staff_id'=>79139, 'date'=>'22/11/2021'],
			['staff_id'=>79140, 'date'=>'22/11/2021'],
			['staff_id'=>79138, 'date'=>'22/11/2021'],
			['staff_id'=>78126, 'date'=>'25/10/2021'],
			['staff_id'=>78924, 'date'=>'17/11/2021'],
			['staff_id'=>79142, 'date'=>'22/11/2021'],
			['staff_id'=>79143, 'date'=>'22/11/2021'],
			['staff_id'=>79172, 'date'=>'22/11/2021'],
			['staff_id'=>79551, 'date'=>'22/11/2021'],
			['staff_id'=>118692,'date'=> '	25/11/2021'],
			['staff_id'=>118724,'date'=> '	01/12/2021'],
			['staff_id'=>118785,'date'=> '	01/12/2021'],
			['staff_id'=>118733,'date'=> '	01/12/2021'],
			['staff_id'=>118706,'date'=> '	01/12/2021'],
			['staff_id'=>118686,'date'=> '	01/12/2021'],
			['staff_id'=>118718,'date'=> '	01/12/2021'],
			['staff_id'=>118700,'date'=> '	01/12/2021'],
			['staff_id'=>118713,'date'=> '	01/12/2021'],
			['staff_id'=>118684,'date'=> '	01/12/2021'],
			['staff_id'=>118711,'date'=> '	01/12/2021'],
			['staff_id'=>118710,'date'=> '	01/12/2021'],
			['staff_id'=>118694,'date'=> '	01/12/2021'],
			['staff_id'=>118736,'date'=> '	06/12/2021'],
			['staff_id'=>118753,'date'=> '	06/12/2021'],
			['staff_id'=>118751,'date'=> '	06/12/2021'],
			['staff_id'=>118857,'date'=> '	10/12/2021'],
			['staff_id'=>118797,'date'=> '	13/12/2021'],
			['staff_id'=>118806,'date'=> '	13/12/2021'],
			['staff_id'=>78668, 'date'=>'15/11/2021'],
			['staff_id'=>118750,'date'=> '	12/06/2021'],
			['staff_id'=>118871,'date'=> '	20/12/2021'],
			['staff_id'=>118902,'date'=> '	21/12/2021'],
			['staff_id'=>118726,'date'=> '	01/12/2021'],
			['staff_id'=>118982,'date'=> '	31/12/2021'],
			['staff_id'=>75204, 'date'=>'23/12/2021'],
			['staff_id'=>118699,'date'=> '	01/12/2021'],
			['staff_id'=>118703,'date'=> '	01/12/2021'],
			['staff_id'=>118719,'date'=> '	01/12/2021'],
			['staff_id'=>118720,'date'=> '	01/12/2021'],
			['staff_id'=>118741,'date'=> '	06/12/2021'],
			['staff_id'=>118828,'date'=> '	06/12/2021'],
			['staff_id'=>118767,'date'=> '	09/12/2021'],
			['staff_id'=>118786,'date'=> '	13/12/2021'],
			['staff_id'=>118791,'date'=> '	13/12/2021'],
			['staff_id'=>118805,'date'=> '	13/12/2021'],
			['staff_id'=>118808,'date'=> '	13/12/2021'],
			['staff_id'=>118832,'date'=> '	15/12/2021'],
			['staff_id'=>118864,'date'=> '	20/12/2021'],
			['staff_id'=>118865,'date'=> '	20/12/2021'],
			['staff_id'=>118875,'date'=> '	20/12/2021'],
			['staff_id'=>118789,'date'=> '	13/12/2021'],
			['staff_id'=>118794,'date'=> '	13/12/2021'],
			['staff_id'=>78312, 'date'=>'08/11/2021'],
			['staff_id'=>78323, 'date'=>'08/11/2021'],
			['staff_id'=>78677, 'date'=>'15/11/2021'],
			['staff_id'=>79256, 'date'=>'23/11/2021'],
			['staff_id'=>79656, 'date'=>'29/11/2021'],
			['staff_id'=>118682,'date'=> '	01/12/2021'],
			['staff_id'=>118683,'date'=> '	01/12/2021'],
			['staff_id'=>118685,'date'=> '	01/12/2021'],
			['staff_id'=>118688,'date'=> '	01/12/2021'],
			['staff_id'=>118696,'date'=> '	01/12/2021'],
			['staff_id'=>118701,'date'=> '	01/12/2021'],
			['staff_id'=>118702,'date'=> '	01/12/2021'],
			['staff_id'=>118705,'date'=> '	01/12/2021'],
			['staff_id'=>118708,'date'=> '	01/12/2021'],
			['staff_id'=>118709,'date'=> '	01/12/2021'],
			['staff_id'=>118738,'date'=> '	06/12/2021'],
			['staff_id'=>118742,'date'=> '	06/12/2021'],
			['staff_id'=>118743,'date'=> '	06/12/2021'],
			['staff_id'=>118746,'date'=> '	06/12/2021'],
			['staff_id'=>118755,'date'=> '	06/12/2021'],
			['staff_id'=>118756,'date'=> '	06/12/2021'],
			['staff_id'=>118762,'date'=> '	06/12/2021'],
			['staff_id'=>118770,'date'=> '	09/12/2021'],
			['staff_id'=>118775,'date'=> '	10/12/2021'],
			['staff_id'=>118777,'date'=> '	10/12/2021'],
			['staff_id'=>118795,'date'=> '	13/12/2021'],
			['staff_id'=>118819,'date'=> '	13/12/2021'],
			['staff_id'=>118822,'date'=> '	13/12/2021'],
			['staff_id'=>118830,'date'=> '	15/12/2021'],
			['staff_id'=>118833,'date'=> '	15/12/2021'],
			['staff_id'=>118837,'date'=> '	15/12/2021'],
			['staff_id'=>118840,'date'=> '	15/12/2021'],
			['staff_id'=>118850,'date'=> '	15/12/2021'],
			['staff_id'=>118872,'date'=> '	20/12/2021'],
			['staff_id'=>118876,'date'=> '	20/12/2021'],
			['staff_id'=>118878,'date'=> '	20/12/2021'],
			['staff_id'=>118879,'date'=> '	20/12/2021'],
			['staff_id'=>118884,'date'=> '	20/12/2021'],
			['staff_id'=>118885,'date'=> '	20/12/2021'],
			['staff_id'=>118886,'date'=> '	20/12/2021'],
			['staff_id'=>118887,'date'=> '	20/12/2021'],
			['staff_id'=>118891,'date'=> '	20/12/2021'],
			['staff_id'=>118893,'date'=> '	20/12/2021'],
			['staff_id'=>118898,'date'=> '	21/12/2021'],
			['staff_id'=>118899,'date'=> '	21/12/2021'],
			['staff_id'=>118903,'date'=> '	22/12/2021'],
			['staff_id'=>118906,'date'=> '	22/12/2021'],
			['staff_id'=>118908,'date'=> '	22/12/2021'],
			['staff_id'=>118910,'date'=> '	22/12/2021'],
			['staff_id'=>118911,'date'=> '	22/12/2021'],
			['staff_id'=>118912,'date'=> '	22/12/2021'],
			['staff_id'=>118680,'date'=> '	01/12/2021'],
			['staff_id'=>118695,'date'=> '	01/12/2021'],
			['staff_id'=>118697,'date'=> '	01/12/2021'],
			['staff_id'=>118704,'date'=> '	01/12/2021'],
			['staff_id'=>118707,'date'=> '	01/12/2021'],
			['staff_id'=>118737,'date'=> '	06/12/2021'],
			['staff_id'=>118745,'date'=> '	06/12/2021'],
			['staff_id'=>118774,'date'=> '	10/12/2021'],
			['staff_id'=>118820,'date'=> '	13/12/2021'],
			['staff_id'=>118814,'date'=> '	06/12/2021'],
			['staff_id'=>118823,'date'=> '	06/12/2021'],
			['staff_id'=>118824,'date'=> '	06/12/2021'],
			['staff_id'=>118836,'date'=> '	5/12/2021'],
			['staff_id'=>118863,'date'=> '	10/12/2021'],
			['staff_id'=>118868,'date'=> '	10/12/2021'],
			['staff_id'=>118776,'date'=> '	29/11/2021'],
			['staff_id'=>118889,'date'=> '	9/12/2021'],
			['staff_id'=>118781,'date'=> '	1/12/2021'],
			['staff_id'=>118782,'date'=> '	1/12/2021'],
			['staff_id'=>118825,'date'=> '	2/12/2021'],
			['staff_id'=>118858,'date'=> '	9/12/2021'],
			['staff_id'=>118860,'date'=> '	9/12/2021'],
			['staff_id'=>118843,'date'=> '	5/12/2021'],
			['staff_id'=>118846,'date'=> '	5/12/2021'],
			['staff_id'=>118870,'date'=> '	5/12/2021'],
			['staff_id'=>118922,'date'=> '	14/12/2021'],
			['staff_id'=>118778,'date'=> '	10/12/2021'],
			['staff_id'=>118915,'date'=> '	23/12/2021'],
			['staff_id'=>118717,'date'=> '	20/12/2021'],
			['staff_id'=>118841,'date'=> '	20/12/2021'],
			['staff_id'=>118900,'date'=> '	21/12/2021'],
			['staff_id'=>118799,'date'=> '	5/12/2021'],
			['staff_id'=>118800,'date'=> '	5/12/2021'],
			['staff_id'=>118801,'date'=> '	2/12/2021'],
			['staff_id'=>118802,'date'=> '	2/12/2021'],
			['staff_id'=>118811,'date'=> '	2/12/2021'],
			['staff_id'=>118812,'date'=> '	2/12/2021'],
			['staff_id'=>118815,'date'=> '	4/12/2021'],
			['staff_id'=>118821,'date'=> '	4/12/2021'],
			['staff_id'=>118826,'date'=> '	2/12/2021'],
			['staff_id'=>118842,'date'=> '	4/12/2021'],
			['staff_id'=>118873,'date'=> '	9/12/2021'],
			['staff_id'=>118874,'date'=> '	9/12/2021'],
			['staff_id'=>118883,'date'=> '	9/12/2021'],
			['staff_id'=>118919,'date'=> '	19/12/2021'],
			['staff_id'=>119074,'date'=> '	4/1/2021'],
			['staff_id'=>118768,'date'=> '	21/12/2021'],
			['staff_id'=>118917,'date'=> '	23/12/2021'],
			['staff_id'=>118897,'date'=> '	22/12/2021'],
			['staff_id'=>118937,'date'=> '	27/12/2021'],
			['staff_id'=>118950,'date'=> '	18/12/2021'],
			['staff_id'=>118924,'date'=> '	16/12/2021'],
			['staff_id'=>118925,'date'=> '	22/12/2021'],
			['staff_id'=>118758,'date'=> '	22/12/2021'],
			['staff_id'=>118835,'date'=> '	22/12/2021'],
			['staff_id'=>118834,'date'=> '	5/12/2021'],
			['staff_id'=>119136,'date'=> '	4/1/2022'],
		];
		//获取员工的网点
		$staff_ids = array_column($arr, 'staff_id');
	    $staff_list = HrStaffInfoModel::find([      'conditions'=> "staff_info_id in ({ids:array})  ",
	                                                'bind'=>['ids'=>$staff_ids],
	                                                'columns' => 'staff_info_id,sys_store_id,job_title,name',
	                                         ])->toArray();
		$staff_list =  array_column($staff_list, null,'staff_info_id');
	    //获取 job_title
		$job_ids = array_column($staff_list, 'job_title');
		$job_title_list = HrJobTitleModel::find([
			                                        'conditions' => "id in ({ids:array}) ",
			                                        'columns' => 'id,job_name',
			                                        'bind' => ['ids' => $job_ids]
		                                        ])->toArray();
		$job_title_list = array_column($job_title_list, 'job_name','id');
		$bll = new ProbationServer($this->lang, $this->add_hour);
		//今天
		$to_day = gmdate("Y-m-d", time() + ( $this->add_hour)*3600);
		$WorkflowServer =  (new WorkflowServer($this->lang, $this->timezone));
	    foreach ($arr as $staff) {
			if(!isset($staff_list[$staff['staff_id']]['staff_info_id'])){
				$this->myLogger('发送转正通知 没有找到此人' .$staff['staff_id'], 'info');
				continue;
			}
			//找 hrbp
			$staff_info_ids = [];
			$findHRBP = $WorkflowServer->findHRBP($staff_list[$staff['staff_id']]['staff_info_id'], ["store_id" => $staff_list[$staff['staff_id']]['sys_store_id']]);
			if (!empty($findHRBP)){
				$staff_info_ids = explode(',', $findHRBP);
			}
		
		    $html = $bll->getMsgTemplateByUserId($staff_list[$staff['staff_id']]['staff_info_id'], "hr_probation_field_msg_to_staff_my", ['date_at' => $to_day, 'name' => $staff_list[$staff['staff_id']]['name'],'job_name'=>$job_title_list[$staff_list[$staff['staff_id']]['job_title']] ?? '','formal_at'=>trim($staff['date'])]);
		    $staff_info_ids[] = $staff_list[$staff['staff_id']]['staff_info_id']; //发送给员工
			foreach($staff_info_ids as $kk=>$vv){
				$staff_info_id = $vv;
				$id = time() . $staff_info_id . rand(1000000, 9999999);
				$param['staff_users'] = [$staff_info_id];//数组 多个员工id
				$param['message_title'] = $bll->getMsgTemplateByUserId($staff_list[$staff['staff_id']]['staff_info_id'],'hr_probation_field_msg_to_staff_title');
				$param['message_content'] = $html;
				$param['staff_info_ids_str'] = $staff_info_id;
				$param['id'] = $id;
				$param['category'] = -1;
				
				$this->getDI()->get('logger')->write_log('send_msg_to_staffAction-param:' . json_encode($param), 'info');
				$bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
				$bi_rpc->setParams($param);
				$res = $bi_rpc->execute();
				$this->getDI()->get('logger')->write_log('send_msg_to_staffAction-result:' . json_encode($res), 'info');
				if ($res && $res['result']['code'] == 1) {
					$kitId    = $res['result']['data'][0];
					$this->myLogger('发送转正通知  写入message成功' . $staff['staff_id'].'==>'.$staff_info_id." message_id ==> ".$kitId, 'info');
				}else{
					$this->myLogger('send_msg_to_staffAction message_backyard  写入message失败' . $staff['staff_id'].'==>'. $staff_info_id, 'info');
				}
			}
			
			
		}
		
		$this->myLogger('执行完毕', 'info');
		
	}

}