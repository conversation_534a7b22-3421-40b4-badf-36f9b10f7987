<?php

namespace FlashExpress\bi\App\Modules\My\Tasks;


use AuditTask as AuditBaseTask;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\ApprovalServer;

class AuditTask extends AuditBaseTask
{
    public function initialize()
    {
        $this->tq = "create-approval";//业务上先择的消费rmq的内容
        parent::initialize(); // TODO: Change the autogenerated stub
    }

    /**
     *
     * mainAction
     * @param $msgBody
     * @return bool
     * @throws ValidationException
     */
    protected function processOneMsg($msgBody): bool
    {
        //[1]获取消息体
        $this->logger->write_log('processOneMsg my' . base64_decode($msgBody), 'info');
        $msgData = $this->getMessageData($msgBody);
        if (empty($msgData)) {
            return true;
        }

        //[2]获取参数
        $submitterId = $msgData['data']['apply_user'];
        $uuid        = $msgData['data']['apply_uuid'];
        $auditType   = $msgData['data']['apply_type'];
        $data        = $msgData['data']['apply_data'];
        $applyTime   = $msgData['data']['apply_timestamp'];

        //获取实例
        $server = new ApprovalServer($this->lang, $this->timezone);
        $appServer = $server->getInstance($auditType);

        if (method_exists($appServer, 'consumerProcessing')) {
            $appServer->consumerProcessing($data, $submitterId, $auditType, $uuid, $applyTime);
        }
        $this->logger->write_log('processOneMsg end', 'info');
        return true;
    }

}