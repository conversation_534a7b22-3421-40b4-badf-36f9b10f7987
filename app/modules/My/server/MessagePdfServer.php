<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Server\MessagePdfServer as BaseServer;
use FlashExpress\bi\App\Models\backyard\MessagePdfModel;

class MessagePdfServer extends BaseServer
{

    /**
     * 获取协议数据
     * @param $messageId
     * @param $signResult
     * @return array
     */
    public function getStaffSignInfoByMessageId($messageId)
    {
        $signInfo = MessagePdfModel::findFirst([
            'columns'    => 'staff_info_id,state,pdf_url,sign_url',
            'conditions' => "msg_id = :msg_id:",
            'bind'       => ['msg_id' => $messageId],
        ]);

        if (!$signInfo) {
            return [];
        }

        $signInfo = $signInfo->toArray();

        $returnData = [
            'sign_state' => $signInfo['state'],
            'pdf_url'    => $signInfo['pdf_url'] ?? '',
        ];

        if ($signInfo['state'] == MessagePdfModel::STATE_SUCCESS) {
            $returnData['pdf_url'] = $signInfo['sign_url'];
        } elseif ($signInfo['state'] == MessagePdfModel::STATE_WAIT) {
            $returnData['pdf_url'] = '';
        }

        return $returnData;
    }

}