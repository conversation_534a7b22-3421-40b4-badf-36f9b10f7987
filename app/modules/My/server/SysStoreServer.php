<?php

namespace FlashExpress\bi\App\Modules\My\Server;
use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Modules\My\library\Enums\CommonEnums;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\SysStoreServer as BaseSysStoreServer;
class SysStoreServer extends BaseSysStoreServer
{


    //网点所在州 只有马来
    public function getProvince($staffInfo){
        if(empty($staffInfo['sys_store_id'])){
            return '';
        }
        //总部员工
        if($staffInfo['sys_store_id'] == enums::HEAD_OFFICE_ID){
            $code = CommonEnums::HEAD_OFFICE_PROVINCE_CODE;
            $staffProvince = HrStaffItemsModel::findFirst([
                'conditions' => "staff_info_id = :staff_id: and item = 'STAFF_PROVINCE_CODE'",
                'bind' => [
                    'staff_id'  => $staffInfo['staff_info_id'],
                ]
            ]);
            if(!empty($staffProvince)){
                $code = $staffProvince->value;
            }
            return strtoupper($code);
        }
        //网点员工
        $code = $this->storeProvince($staffInfo);
        return strtoupper($code);
    }

    public function storeProvince($staffInfo){
        //配置 如果
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('leave_special_branch_belongs')
            ->setParameters(['staff_info_id' => $staffInfo['staff_info_id']])
            ->getConfig();
        $setting = '';
        if(!empty($res['response_type']) && $res['response_type'] == ConditionsRulesEnums::RESPONSE_TYPE_VALUE){
            //处理数据
            $setting = $res['response_data'];
        }
        if(!empty($setting)){
            return strtoupper($setting);
        }

        //获取网点信息
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind' => ['store_id' => $staffInfo['sys_store_id']]
        ]);
        if(empty($storeInfo)){
            return '';
        }
        return strtoupper($storeInfo->province_code);
    }


    public function batchGetProvince(){

    }

}
