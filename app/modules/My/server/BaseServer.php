<?php

/**
 * 业务仓库基类
 */

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Controllers\API\ApiControllerBase;
use FlashExpress\bi\App\Controllers\ControllerBase;
use Hprose\Http\Client;
use Nette\Utils\Random;
use Phalcon\Di;
use Phalcon\DiInterface;
use Phalcon\Mvc\User\Component;
use Phalcon\Translate\Adapter\NativeArray;
use WebGeeker\Validation\Validation;
use FlashExpress\bi\App\library\ApiClient;

class BaseServer extends Component
{

    public $timeZoneOfThailand;

    public $lang = 'zh-CN'; // 语言变量
    public $timeZone;
    public $languagePack;
    public $t;

    public function __construct($lang = '', $timezone='+08:00')
    {
        $this->languagePack = $this->di->get('languagePack');
    
        //兼容传递的方式，传递后server层以传递的为准
        if ($lang) {
            //设置
            $this->languagePack->setLanguage($lang);
        }
    
        //获取全局语言对象
        $this->t    = $this->languagePack->t;
        $this->lang = $this->languagePack->lang;

        $this->timeZone = empty($timezone) ? $this->config['application']['timeZone'] : $timezone;
        $this->timeZoneOfThailand = $this->config['application']['timeZoneOfThailand'];
    }

    /**
     * @param $lang
     * @return NativeArray
     */
    public function getTranslationByLang($lang)
    {
        return $this->languagePack->getTranslation($lang ?: $this->lang);
    }

    public function getTranslation()
    {
        // 返回一个语言包
        return $this->languagePack->getTranslation($this->lang);
    }

    /**
     * @param $sec
     * @return false|float
     */
    public function getHours($sec)
    {
        if ($sec >= 3600) {
            $data = floor($sec / 3600) + round(($sec - floor($sec / 3600) * 3600) / 3600, 2);
        } else {
            $data = round($sec / 3600, 2);
        }
        return round($data, 2);
    }

    const HRIS_STAFF_PROFILE = 1;
    const WORK_ORDER = 2;
    const PROBLEMATIC_ITEM = 3;
    const INSURE_DOC_CLAIM_F = 4;
    const INSURE_DOC_SHOW_PRODUCT = 5;
    const SS_COURT_FILE = 6;
    const PARCEL_CHANGE = 7;
    const CERTIFICATE_PDF = 8;

    const OSS_DIR_MAPS = [
        self::HRIS_STAFF_PROFILE => 'HRIS_STAFF_PROFILE', // fbi 用户类型dir type
        self::WORK_ORDER => 'WORK_ORDER',//工单
        self::PROBLEMATIC_ITEM => 'PROBLEMATIC_ITEM',
        self::INSURE_DOC_CLAIM_F => 'INSURE_DOC_CLAIM_F',//索赔说明(保价险索赔需要)
        self::INSURE_DOC_SHOW_PRODUCT => 'INSURE_DOC_SHOW_PRODUCT',//声明价值证明材料(保价险索赔需要)
        self::SS_COURT_FILE => 'SS_COURT_FILE',//闪速判案附件
        self::PARCEL_CHANGE => 'PARCEL_CHANGE',//lazada超时包裹
        self::CERTIFICATE_PDF => 'CERTIFICATE_PDF',//证明下载 工资条 pdf 文件夹
    ];




    /**
     * 处理默认值
     * @param $paramIn
     * @param $paramIn 数组|$parameter 参数名称 |$type 类型 1 字符串 2整型 3数组 4 布尔类型
     * @return string
     */
    public function processingDefault($paramIn, $parameter, $type = 1, $default = '')
    {
        switch($type)
        {
            case 1:
                $default = !empty($default) ? $default : '';
                break;
            case 2:
                $default = !empty($default) ? $default : 0;
                break;
            case 3:
                $default = !empty($default) ? $default : [];
                break;
            case 4:
                $default = !empty($default) ? $default : false;
                break;
        }

        if ($type == 4) {
            return isset($paramIn[$parameter]) ? $paramIn[$parameter] : $default;
        } else {
            return isset($paramIn[$parameter]) && !empty($paramIn[$parameter]) ? $paramIn[$parameter] : $default;
        }
    }

    /**
     * 参数校验 校验错误抛出
     * @Access  public
     * @Param   $paramIn 入参校验数组 | $validations 校验规则
     * @Return  array
     */
    public function validateCheck($paramIn = [], $validations = [])
    {
        try
        {
            Validation::validate($paramIn, $validations);
        } catch(\Exception $e)
        {
            $this->jsonReturn($this->checkReturn(-1, $e->getMessage()));
        }
    }

    /**
     * 校验返回
     * @param $paramIn
     * @param $parameter
     * @return array
     */
    public function checkReturn($paramIn, $parameter = null)
    {
        if (is_array($paramIn)) {
            //[1] 数组时返回
            $code = isset($paramIn['code']) && !empty($paramIn['code']) ? $paramIn['code'] : 1;
            $msg = isset($paramIn['msg']) && !empty($paramIn['msg']) ? $paramIn['msg'] : '请求成功!';
            $data = isset($paramIn['data']) && !empty($paramIn['data']) ? $paramIn['data'] : null;
        } elseif ($paramIn == -1) {
            //[2] 参数不正确时返回
            $code = 0;
            $msg = '参数' . $parameter . '不能为空!';
        } elseif ($paramIn == -2) {
            //[2] 参数不正确时返回
            $code = 0;
            $msg = '参数' . $parameter . '错误,json格式不正确!';
        } elseif ($paramIn == -3) {
            //[3] 参数不正确时返回
            $code = 0;
            $msg = $parameter;
        } elseif ($paramIn == 1) {
            //[4] 请求成功时返回
            $code = 1;
            $msg = '请求成功!';
        }
        $returnData = [
            'code' => isset($code) ? $code : 0,
            'msg' => isset($msg) ? $msg : '',
            'data' => isset($data) ? $data : null
        ];
        return $returnData;
    }

    /**
     * JSON返回
     * @param $paramIn array | $jsonCallback 处理跨域
     * @param bool $jsonCallback
     * @return void
     */
    public function jsonReturn($paramIn, $jsonCallback = false)
    {
        header('Content-type: application/json;charset=utf-8');
        if ($jsonCallback) {
            $json_data = json_encode($paramIn, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            echo $jsonCallback . "(" . $json_data . ")";
            exit();
        } else {
            $json_data = json_encode($paramIn, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            echo $json_data;
            exit();
        }
    }

    /**
     * 记录日志
     * @Access  public
     * @Param   request
     * @Return  array
     * @param string $title
     * @param $message
     * @param string $model
     * @param string $level
     */
    public function wLog($title = '', $message, $model = '', $level = 'info')
    {
        // 记录日志
        if (!empty($message)) {
            if (is_array($message)) {
                $message = json_encode($message);
            }

            //默认级别 为info
            if ($level != 'error')
                $level = 'info';


            $debugInfo = debug_backtrace();
            $debugInfo = empty($debugInfo[1]) ? array() : $debugInfo[1];

            $function = empty($debugInfo['function']) ? '' : $debugInfo['function'];
            $class = empty($debugInfo['class']) ? '' : $debugInfo['class'];

            //截取 最后 类名  FlashExpress\\bi\\App\\Controllers\\ControllerBase
            if (strstr($class, '\\')) {
                $arr = explode('\\', $class);
                $class = end($arr);
            }

            //写入日志 统一方法
            $message = "{$model}:{$title}-{$message}";
            $logger = $this->getDI()->get('logger');
            $logger->write_log($message, $level, $class, $function);
        }
    }

    /**
     * 获取唯一ID
     * @Access  public
     * @Param   request
     * @Return  string
     */
    public function getID()
    {
        $returnData = null;
        try{
            $client      = new Client(env('go_rpc_server','http://127.0.0.1:8051/svc/call'), false);
            $ret         = json_decode($client->GetUniqueID(1));
            if (isset($ret) && $ret && $ret->code == 1) {
                $returnData = substr($ret->data,4, 15);
            }
        } catch (\Exception $e) {
            $this->wLog("error--生成ID失败", $e->getMessage(), 'BaseServer');
        }
        return $returnData ?? $this->getRandomId();
    }

    /**
     * 获取唯一ID
     * @Access  public
     * @Param   request
     * @Return  string
     */
    public function getRandomId()
    {
        $returnData = null;
        try{
            //获取ip地址
            $returnData = date("YmdHi") . str_pad($this->getRandomIncrSalt('workflow_random_id', 3600), 7, 0, STR_PAD_LEFT);
        }catch (\Exception $e) {
            $this->wLog("error--生成ID失败", $e->getMessage(), 'BaseServer');
        }
        return $returnData;
    }

    public function getRandomIncrSalt($key, $expire)
    {
        $redis = $this->getDI()->get('redisLib');
        $LUASCRIPT = <<<LUA
local key = KEYS[1]
local ttl = ARGV[1]
local salt = 0
if (redis.call('EXISTS', key) == 1) then
    return redis.call('INCR', key)
else
    salt = redis.call('INCR', key)
    redis.call('EXPIRE', key, ttl)
end
return salt
LUA;
        return $redis->eval($LUASCRIPT, [$key, $expire], 1);
    }

    /**
     * 根据 语言环境 获取对应字段名 涉及表 share_center_dir  share_center_file 字段名(name_th,name_en,name_cn)
     * @param $lang
     * @return string
     */
    public function get_lang_column($lang = 'th'){
        $lang_arr = array(
            'en' => 'name_en',
            'th' => 'name_th',
            'zh-CN' => 'name_cn',
        );
        return empty($lang_arr[$lang]) ? '' : $lang_arr[$lang];
    }

    /**
     * 拼组数据
     * @param $list
     * @return array
     */
    public function format($list): array
    {
        $return = [];
        foreach ($list as $key => $v) {
            $return[] = [
                'key'   => $this->getTranslation()->_($key) ?? '',
                'value' => is_array($v) && isset($v['value']) ? $v['value']:$v,
                'tips'=> is_array($v) && isset($v['tips']) ? $v['tips']:null,
            ];
        }
        return $return;
    }

    /**
     * 获取审批行为 TODO 状态 5 超时关闭
     * @param array $paramIn
     * @return mixed
     */
    public function getStaffOptions($paramIn = [])
    {
        $status  = $this->processingDefault($paramIn, 'status');
        $options = $this->processingDefault($paramIn, 'option');
        if (in_array($status, [2, 3, 4, 5, 6])) {
            $option = $options['afterApproval'] ?? 0;
        } else {
            $option = $options['beforeApproval'] ?? 0;
        }
        if ($option != 0) {
            foreach (explode(',', $option) as $v) {
                $returnData['code' . $v] = intval($v);
            }
        }
        if (empty($returnData)) {
            $returnData = (object)null;
        }
        return $returnData;
    }
}
