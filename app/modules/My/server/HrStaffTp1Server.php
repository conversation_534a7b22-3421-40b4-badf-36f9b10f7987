<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrStaffTp1Model;
use FlashExpress\bi\App\Server\BaseServer;

class HrStaffTp1Server extends BaseServer
{
    public $timezone;
    public $lang;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang, $timezone);
        $this->timezone = $timezone;
        $this->lang     = $lang;
    }


    /**
     * 根据员工ID获取tp3信息
     * @param $msg_id
     * @return array
     */
    public function getMonthOption($staff_id)
    {


        //获取12个月显示数据
        $current_year = date('Y');
        $current_month = date('n');
        for ($i = 1; $i <= 12; $i++) {
            $month          = $i < 10 ? '0' . $i : $i;
            $month_option[] =
                [
                    'code'          => $current_year.'-'. $month,
                    'is_can_view'   => 1,//是否能填写改月份tp1,1：能，0：不能，限定为历史所有本年度的月份都可以填写了
                    'approve_state' => 0,
                    'tp1_month_version' => HrStaffTp1Model::TP1_VERSION_V4,
                    'tp1_detail'    => null,
                ];
        }
        //获取用户本年度所有提交的tp1数据
        $tp1_list = HrStaffTp1Model::find([
            'conditions' => "staff_id = :staff_id: and year_month like :year_month:",
            'bind'       => [
                'staff_id' => $staff_id,
                'year_month' => "{$current_year}-%",
            ],
        ]);

        if ($tp1_list) {
            $tp1_list = $tp1_list->toArray();
            foreach ($month_option as &$item) {
                foreach ($tp1_list as $tp1) {
                    if ($item['code'] == $tp1['year_month']) {
                        $item['is_can_view']   = 1;//如果填写过tp1的月份，直接可以再次查看或编辑，但是要根据下面的审核状态
                        $item['approve_state'] = $tp1['approve_state'];
                        $item['tp1_month_version'] = $tp1['approve_state'] != HrStaffTp1Model::APPROVE_STATE_REFUSE ?  $tp1['tp1_version'] : HrStaffTp1Model::TP1_VERSION_V4;
                        $item['tp1_detail']    = $tp1['tp1_detail'] ? json_decode($tp1['tp1_detail'], true) : null;

                    }
                }
            }

        }

        return $month_option;

    }


    /**
     * 保存tp1信息
     * @param $params
     * @param $staff_id
     * @param $staff_name
     * @return bool
     * @throws BusinessException
     */
    public function saveTp1($params, $staff_id, $staff_name)
    {


        $year_month = $params['year_month'] ?? '0';

        $tp1_info = HrStaffTp1Model::findFirst([
            "year_month = :year_month: AND staff_id = :staff_id:",
            "bind" => [
                "year_month" => $year_month,
                "staff_id"   => $staff_id,
            ],
        ]);

        if ($tp1_info && $tp1_info->approve_state != enums::$tp1_state['reject']) {
            throw new BusinessException('The information of TP1 has been submitted, no need to resubmit');
        }
        $db         = $this->getDI()->get('db');
        $tp1_detail =  json_encode($params['tp1_detail'],JSON_UNESCAPED_UNICODE);
        //新增tp1信息
        if (empty($tp1_info)) {
            $tp1_insert_data = [
                'staff_id'      => $staff_id,
                'year_month'    => $year_month,
                'approve_state' => enums::$tp1_state['wait_approve'],
                'tp1_detail'    => $tp1_detail,
                'tp1_version'    => HrStaffTp1Model::TP1_VERSION_V4,
            ];
            $db->insertAsDict('hr_staff_tp1', $tp1_insert_data);
            $tp1_id = $db->lastInsertId();
            if (empty($tp1_id)) {
                $db->rollback();
                throw new \Exception('tp1数据入库失败' . var_export($tp1_insert_data, true));
            }

        } else {
            //更新tp1信息
            $tp1_info->tp1_detail    = $tp1_detail;
            $tp1_info->approve_state = enums::$tp1_state['wait_approve'];
            $tp1_info->tp1_version =  HrStaffTp1Model::TP1_VERSION_V4;
            $tp1_info->save();
            $tp1_id = $tp1_info->id;

        }

        //记录一条提交日志
        $tp1_log_insert = [
            'tp1_pid'             => $tp1_id,
            'action'              => 1,//1：提交
            'tp1_detail'          => $tp1_detail,
            'approver_staff_id'   => $staff_id,
            'approver_staff_name' => $staff_name,
        ];
        $db->insertAsDict('hr_staff_tp1_log', $tp1_log_insert);


        return true;


    }

}