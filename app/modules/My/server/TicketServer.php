<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Modules\My\Repository\TicketRepository;
use FlashExpress\bi\App\Server\TicketServer as TicketBaseSever;

class TicketServer extends TicketBaseSever
{
    /**
     * IT工单 + 转交工单
     * @param $userInfo
     * @return array
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function getNums()
    {
        $ticketRepository = new TicketRepository();
        $data = $ticketRepository->getNums($this->userInfo);
        if($ticketRepository->isDM($this->userInfo)){
            $data['is_dm'] = 1;
            $data['audit_num'] = $this->getCsNumSvc(1,$this->userInfo['id']);
        }
        return $this->checkReturn(["data"=>$data]);
    }

    /**
     * 校验权限
     * @return mixed
     */
    public function checkPermission()
    {
        return (new TicketRepository())->isDM($this->userInfo);
    }
}