<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Server\AgreementSignRecordServer as BaseServer;
use FlashExpress\bi\App\Models\backyard\HrAgreementSignRecordModel;

class AgreementSignRecordServer extends BaseServer
{

    /**
     * 获取协议数据
     * @param $messageId
     * @param $signResult
     * @return array
     */
    public function getStaffSignInfoByMessageId($messageId, $signResult)
    {
        $signInfo = HrAgreementSignRecordModel::findFirst([
            'columns'    => 'staff_info_id,state,pdf_url',
            'conditions' => "message_id = :message_id:",
            'bind'       => ['message_id' => $messageId],
        ]);

        if (!$signInfo) {
            return [];
        }

        $signInfo = $signInfo->toArray();

        $returnData = [
            'sign_state' => $signInfo['state'],
            'pdf_url'    => $signInfo['pdf_url'] ?? '',
        ];

        if ($signInfo['state'] == HrAgreementSignRecordModel::STATE_SUCCESS) {
            $returnData['pdf_url'] = $signResult['sign_pdf_url'];
        } elseif ($signInfo['state'] == HrAgreementSignRecordModel::STATE_WAIT) {
            $returnData['pdf_url'] = '';
        }

        return $returnData;
    }

    /**
     * 获取未签订的协议
     * @param $staffInfoId
     */
    public function checkStaffAgreement($staffInfoId)
    {
        //获取用户
        $signInfo = HrAgreementSignRecordModel::findFirst([
            'columns'    => 'staff_info_id,message_id',
            'conditions' => "staff_info_id = :staff_info_id: AND state = :state:",
            'bind'       => [
                'staff_info_id' => $staffInfoId,
                'state'         => HrAgreementSignRecordModel::STATE_DEFAULT,
            ],
        ]);

        return $signInfo ? $signInfo->toArray() : [];
    }

}