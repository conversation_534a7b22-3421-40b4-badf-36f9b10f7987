<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\WorkflowServer as GlobalBaseServer;

class WorkflowServer extends GlobalBaseServer
{
    protected $isLnt = false;

    /**
     *
     * @param $request
     * @param $user
     * @param int $datetime_formatter
     * @return array
     * @throws \Exception
     */
    public function publicLog($request, $user, int $datetime_formatter = AuditListEnums::DATETIME_FORMATTER_DEFAULT)
    {
        $this->isLnt = $this->isLnt($request);
        return parent::publicLog($request, $user, $datetime_formatter);
    }

    /**
     * 获取翻译key 后缀
     * @return string
     */
    protected function getStateTranslateSuffix(): string
    {
        if ($this->isUnpaid) {
            return '_unpaid';
        } else if ($this->isLnt) {
            return '_lnt';
        } else {
            return '';
        }
    }

    /**
     * @description 获取翻译 key
     * @return string
     */
    protected function getStateTranslateKey(): string
    {
        if ($this->isUnpaid) {
            return 'audit_status_unpaid_';
        } else if ($this->isLnt) {
            return 'audit_status_lnt_';
        } else {
            return 'audit_status.';
        }
    }

    /**
     * @description 获取翻译组
     * @return string[]
     */
    protected function getStateTranslateGroup(): array
    {
        if ($this->isUnpaid) {
            return Enums::$wf_action_status_unpaid;
        } else if ($this->isLnt) {
            return Enums::$wf_action_status_lnt;
        } else {
            return Enums::$wf_action_status;
        }
    }

    /**
     * 申请是否为 lnt
     * @param AuditApplyModel $request
     * @return bool
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    private function isLnt($request): bool
    {
        $instance = (new ApprovalServer($this->lang, $this->timezone))->getInstance($request->getBizType());

        //是否为 LNT
        if (method_exists($instance, 'checkIfLntStaff')) { //各个业务实现: 是申请人还是被申请人
            return $instance->checkIfLntStaff($request->getBizValue());
        } else { // 申请人
            return (new StaffServer())->isLntStaff($request->getSubmitterId());
        }
    }

    /**
     * 日志分组
     * @param $request
     * @param $data
     * @return array
     * @throws ValidationException
     */
    protected function chunkLogsV4($request, $data)
    {
        $chunked       = [];
        $index         = 0;
        $isShowEndNode = true;
        $server        = new ApprovalServer($this->lang, $this->timezone);
        $instance      = $server->getInstance($request->getBizType());
        if (method_exists($instance, 'isShowEndNode')) {
            $isShowEndNode = $instance->isShowEndNode($request);
        }

        // 获取 lng 最后一个虚拟审批节点
        $server = new SettingEnvServer();
        $configStaffInfoId = $server->getSetVal('lnt_virtual_approval_id');

        //日志分组 特殊情况 泰国出差审批流 需要改成map 同时存在 正常审批 撤销审批 和修改审批
        $title = $this->getTranslation()->_('normal_log');
        foreach ($data as $item) {
            if ($item['status_code'] == enums::WF_ACTION_EDIT_RECREATE) {
                $title = $this->getTranslation()->_('edit_log');
                $index++;
            }

            if ($item['status_code'] == enums::WF_ACTION_CREATE_CANCEL) {
                $title = $this->getTranslation()->_('cancel_log');
                $index++;
            }

            $chunked[$index]['title']  = $title;
            $chunked[$index]['data'][] = $item;
        }

        $groupCnt = count($chunked);
        foreach ($chunked as $index => $group) {
            // 仅 lnt 追加虚拟审批节点
            //1. 仅有一段时
            //2. 有多段，但不是最后一段时
            //3. 有多段，是最后一段并且审批完成时
            if ($this->isLnt && (
                    $request->getState() != enums::APPROVAL_STATUS_PENDING && (
                        $groupCnt == 1 || $index == $groupCnt - 1
                    ) ||
                    ($index < $groupCnt - 1)
                )) {
                $lastAuditNode = $this->getValidaAuditNode($group['data']);
                $virtualAuditLog = $this->addVirtualAuditLog($lastAuditNode, $configStaffInfoId);
                if ($virtualAuditLog) {
                    $chunked[$index]['data'][] = $virtualAuditLog;
                }
            }
            if (next($chunked) === false) { // 最后一个节点
                if ($isShowEndNode) {
                    $finalNode                 = [
                        'state_txt'     => $this->formatStateCodeTxt(enums::WF_ACTION_FINAL_COMPLETED),
                        'status_code'   => enums::WF_ACTION_FINAL_COMPLETED,
                        'action_time'   => null,
                        "process_state" => $request->getState() != Enums::APPROVAL_STATUS_PENDING ? AuditLogModel::PROCESS_BAR_HIGHLIGHT : AuditLogModel::PROCESS_BAR_DARKNESS,
                        "approval_type" => enums::NODE_FINAL,
                        "approval_info" => [],
                    ];
                    $chunked[$index]['data'][] = $finalNode;
                }
            }
        }

        return $chunked;
    }

    /**
     * 默认日志分组处理
     * @param $request
     * @param array $data
     * @return array
     * @throws ValidationException
     */
    protected function defaultChunkLogs($request, array $data): array
    {
        $isShowEndNode = true;
        $server = new ApprovalServer($this->lang, $this->timezone);
        $instance = $server->getInstance($request->getBizType());
        if (method_exists($instance, 'isShowEndNode')) {
            $isShowEndNode = $instance->isShowEndNode($request);
        }

        // 获取 lng 最后一个虚拟审批节点
        $server            = new SettingEnvServer();
        $configStaffInfoId = $server->getSetVal('lnt_virtual_approval_id');

        if ($this->isLnt && $request->getState() != enums::APPROVAL_STATUS_PENDING) {
            $lastAuditNode = $this->getValidaAuditNode($data);
            $virtualAuditLog = $this->addVirtualAuditLog($lastAuditNode, $configStaffInfoId);
            if ($virtualAuditLog) {
                $data[] = $virtualAuditLog;
            }
        }
        if ($isShowEndNode) {
            $finalNode = [
                'state_txt'     => $this->formatStateCodeTxt(enums::WF_ACTION_FINAL_COMPLETED),
                'status_code'   => enums::WF_ACTION_FINAL_COMPLETED,
                'action_time'   => null,
                "process_state" => $request->getState() != Enums::APPROVAL_STATUS_PENDING ? AuditLogModel::PROCESS_BAR_HIGHLIGHT : AuditLogModel::PROCESS_BAR_DARKNESS,
                "approval_type" => enums::NODE_FINAL,
                "approval_info" => [],
            ];
            $data[]    = $finalNode;
        }

        return $data;
    }

    /**
     * 添加虚拟审批日志
     * @param $lastNode
     * @param $configStaffInfoId
     * @return array
     */
    private function addVirtualAuditLog($lastNode, $configStaffInfoId): array
    {
        //终态才追加虚拟节点
        if (!$this->isLnt || empty($configStaffInfoId) || empty($lastNode)) {
            return [];
        }
        $configStaffInfo = (new StaffServer())->getStaffInfoSpecColumns($configStaffInfoId, 'h.staff_info_id,
            h.name as staff_name,
            d.name as department_name,
            j.job_name');

        return $this->generateVirtualAuditLog($lastNode, $configStaffInfo);
    }

    /**
     * @param $lastNode
     * @param $configStaffInfo
     * @return array
     */
    private function generateVirtualAuditLog($lastNode, $configStaffInfo): array
    {
        $stateCode = $lastNode['status_code'];
        $actionTime = $lastNode['action_time'];

        if (in_array($stateCode, enums::$wf_actions_approval)) {
            $action = enums::WF_ACTION_APPROVE;
        } else if (in_array($stateCode, enums::$wf_actions_reject)) {
            $action = enums::WF_ACTION_REJECT;
        } else {
            return [];
        }

        $stateTxt = $this->formatStateCodeTxt($action);

        return [
            'state_txt'     => $stateTxt,
            'status_code'   => $stateCode,
            'action_time'   => date('Y-m-d', strtotime($actionTime)),
            "process_state" => AuditLogModel::PROCESS_BAR_HIGHLIGHT,
            "approval_type" => enums::NODE_APPROVER,
            "approval_info" => [
                [
                    "staff_id"    => $configStaffInfo['staff_info_id'],
                    "staff_name"  => $configStaffInfo['staff_name'],
                    "position"    => $configStaffInfo['job_name'],
                    "department"  => $configStaffInfo['department_name'],
                    "sort"        => 1,
                    "state_txt"   => $this->generateVirtualStateTxt($action),
                    "status_code" => $action,
                    "audit_info"  => null,
                ],
            ],
        ];
    }

    /**
     * 格式化日期
     * @param $datetime
     * @param int $datetime_formatter
     * @return string
     */
    public function formatDateTime($datetime, int $datetime_formatter = AuditListEnums::DATETIME_FORMATTER_DEFAULT): string
    {
        if ($this->isLnt) {
            $curTime = gmdate("Y-m-d", strtotime($datetime) + $this->config->application->add_hour * 3600);
        } else {
            $curTime = gmdate("Y-m-d H:i:s", strtotime($datetime) + $this->config->application->add_hour * 3600);
        }

        if ($datetime_formatter == AuditListEnums::DATETIME_FORMATTER_DEFAULT) {
            return $curTime;
        } else if ($datetime_formatter == AuditListEnums::DATETIME_FORMATTER_TIMESTAMP) {
            return strtotime($curTime);
        } else {
            return "";
        }
    }

    private function getValidaAuditNode($data)
    {
        $lastNode = null;
        $descData = array_reverse($data);
        foreach ($descData as $item) {
            if (in_array($item['approval_type'], [Enums::NODE_APPROVER, Enums::NODE_FINAL])) {
                $lastNode = $item;
                break;
            }
        }
        return $lastNode;
    }

    private function generateVirtualStateTxt($action): string
    {
        if ($action == Enums::WF_ACTION_APPROVE) {
            $key = 'virtual_node_state_approval_lnt';
        } else {
            $key = 'virtual_node_state_reject_lnt';
        }
        return $this->getTranslation()->_($key);
    }
}