<?php

namespace FlashExpress\bi\App\Modules\My\Server;


use FlashExpress\bi\App\Enums\AuditDetailOperationsEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\JobTransferConfirmEnums;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\SysAttachmentModel;
use FlashExpress\bi\App\Modules\My\library\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\JobTitleRepository;
use FlashExpress\bi\App\Repository\JobtransferRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Server\ApprovalFinderServer;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\JobTransferV2Server as BaseJobTransferServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\WorkflowPersistentServer;


class JobTransferV2Server extends BaseJobTransferServer
{
    /**
     * @description 提交转岗申请
     * @throws ValidationException
     * @throws BusinessException
     */
    public function addJobTransfer($paramIn = [])
    {
        //[1]获取参数
        $transferStaffId    = $this->processingDefault($paramIn, 'staff_id', 2);
        $submitterId        = $this->processingDefault($paramIn, 'submitter_id');
        $departmentId       = $this->processingDefault($paramIn, 'after_department_id', 2);
        $storeId            = $this->processingDefault($paramIn, 'after_store_id');
        $jobTitle           = $this->processingDefault($paramIn, 'after_position_id', 2);
        $afterDate          = $this->processingDefault($paramIn, 'after_date');
        $jobHandoverStaffId = $this->processingDefault($paramIn, 'job_handover_staff_id', 2);
        $transferReason     = $this->processingDefault($paramIn, 'reason_type');
        $carType            = $this->processingDefault($paramIn, 'after_car_type');
        $reason             = $this->processingDefault($paramIn, 'reason');
        $reason             = addcslashes(stripslashes($reason), "'");

        //[2]参数校验
        //[2-1]校验员工信息
        $params = [
            'submitter_id'          => $submitterId,
            'transfer_staff_id'     => $transferStaffId,
            'job_handover_staff_id' => $jobHandoverStaffId,
            'after_date'            => $afterDate,
            'after_store_id'        => $storeId,
            'after_position_id'     => $jobTitle,
            'after_car_type'        => $carType,
        ];
        $transferValidateServer = $this->class_factory('JobTransferValidateServer', $this->lang, $this->timeZone);
        $transferValidateServer->init($params)->loadRules(get_class($transferValidateServer)::CREATE_RULES)->check();

        //[3]获取转岗前数据
        $transferStaffInfo = $transferValidateServer->getJobTransferStaffInfo();

        //根据网点信息获取大区、片区信息
        $repository       = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
        $currentStoreData = $repository->getOrganizationRegionPiece($transferStaffInfo['store_id']);
        $afterStoreData   = $repository->getOrganizationRegionPiece($storeId);

        $transferType = $this->getTransferType($transferStaffInfo['node_department_id'], $transferStaffInfo['job_title']);

        //转岗前的工作天数与轮休规则
        $jobTransferRepository = new JobTransferRepository($this->timeZone);
        $baseStaffInfo = $jobTransferRepository->getBaseStaffInfo($transferStaffId);
        $beforeWorkingDayRestType = $baseStaffInfo['week_working_day'] . $baseStaffInfo['rest_type'];

        //获取转岗前角色
        $staffInfo = (new HrStaffInfoServer())->getStaffInfo($transferStaffId);

        //获取转岗前车辆信息
        $vehicleInfo = $this->getVehicleDetailByStaffId($transferStaffId);

        //转岗前公司ID
        $currentDepartmentInfo = (new DepartmentRepository())->getSpecDepartmentInfo($transferStaffInfo['node_department_id'], 'id,company_id');

        //转岗后职级
        $hcId       = 0;
        $afterGrade = 0;
        $hcServer   = new HcServer($this->lang, $this->timeZone);
        if ($transferType == JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE) {

            //转岗后HC
            $hcId = $hcServer->getValidTransferHc($staffInfo['hire_type'], $departmentId, $storeId, $jobTitle, $afterDate);
            if (empty($hcId)) {
                $hcServer->checkBudget($departmentId, $storeId, $jobTitle);
            }

            //获取转岗后职级
            $afterGrade = $this->calcJobTitleGrade($transferType, $departmentId, $jobTitle, $transferStaffInfo['job_title_grade_v2']);

            //转岗后公司ID
            $afterDepartmentInfo = (new DepartmentRepository())->getSpecDepartmentInfo($departmentId, 'id,company_id');
        }

        //转岗前后的职位在配置里，则无法提交申请，需走线下流程
        $settingEnvPositionsList = [];
        $settingEnvPositions     = $this->getCannotApplyPositionIds();
        if (!empty($settingEnvPositions)) {
            $settingEnvPositionsList = JobTitleRepository::getJobTitleByIds($settingEnvPositions);
            $settingEnvPositionsList = array_column($settingEnvPositionsList, 'job_name', 'id');
        }
        $this->checkApplyPositions([$transferStaffInfo['job_title'], $jobTitle], $settingEnvPositions,
            $settingEnvPositionsList);

        //车类型
        if (in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            if (!in_array($jobTitle, $this->getShowCarTypeJobTitle())) {
                $carType = 0;
            }
        }
        //添加转岗信息
        $param = [
            'staff_id'                      => $transferStaffId,
            'submitter_id'                  => $submitterId,
            'serial_no'                     => $this->getRandomId(),
            'current_company_id'            => $currentDepartmentInfo['company_id'] ? : 0,
            'current_department_id'         => $transferStaffInfo['node_department_id'],
            'current_store_id'              => $transferStaffInfo['store_id'],
            'current_position_id'           => $transferStaffInfo['job_title'],
            'current_manager_id'            => $transferStaffInfo['manager_id'] ?? '',
            'current_role_id'               => strlen($staffInfo['position_category']) > 0 ? $staffInfo['position_category'] : '',
            'current_region_id'             => !empty($currentStoreData) ? $currentStoreData->region_id: 0,
            'current_job_title_grade'       => $transferStaffInfo['job_title_grade_v2'] ?? 0,
            'current_piece_id'              => !empty($currentStoreData) ? $currentStoreData->piece_id: 0,
            'after_company_id'              => $afterDepartmentInfo['company_id'] ?? 0,
            'after_department_id'           => $departmentId,
            'after_store_id'                => $storeId,
            'after_region_id'               => !empty($afterStoreData) ? $afterStoreData->region_id: 0,
            'after_piece_id'                => !empty($afterStoreData) ? $afterStoreData->piece_id: 0,
            'after_position_id'             => $jobTitle,
            'after_job_title_grade'         => $afterGrade,
            'after_date'                    => $afterDate,
            'transfer_reason'               => $transferReason,
            'reason'                        => $reason ? : '',
            'job_handover_staff_id'         => $jobHandoverStaffId,
            'state'                         => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED,   //待转岗
            'approval_state'                => enums::APPROVAL_STATUS_PENDING,                         //待审批
            'approval_state_stage_one'      => enums::APPROVAL_STATUS_PENDING,                         //一阶段待审批
            'approval_state_stage_two'      => enums::APPROVAL_STATUS_PENDING,                         //二阶段待审批
            'confirm_state'                 => JobTransferConfirmEnums::CONFIRM_STATE_INVALID,        //待确认
            //'vehicle_source'                => $vehicleInfo['vehicle_source'] ?? 0,                    //转岗前车辆来源
            //'current_rental_car_created_at' => $vehicleInfo['vehicle_start_date'] ?? null,             //转岗前用车开始日期
            'before_working_day_rest_type'  => $beforeWorkingDayRestType,
            'type'                          => $transferType,
            'workflow_role_name'            => JobTransferEnums::JOB_TRANSFER_VERSION_V3,
            'current_car_type'              => $vehicleInfo['vehicle_type_category'] ?? 0,             //转岗前车类型
            'current_hire_type'             => $staffInfo['hire_type'] ?? 0,
            'current_hire_times'            => $staffInfo['hire_times'] ?? 0,
            'after_car_type'                => $carType ? : 0,
        ];
        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            //扣减HC
            if (!empty($hcId)) {
                $param['hc_id'] = $hcId;
                $hcServer->deductHc($hcId);
            }

            $jobTransferId = $jobTransferRepository->addJobTransfer($param);
            if (empty($jobTransferId)) {
                throw new BusinessException($this->getTranslation()->_('jobtransfer_0004'));
            }
            $extend = $this->getParseNodeParams($jobTransferId, $submitterId);

            //提交转岗转出申请
            $ret = (new ApprovalServer($this->lang, $this->timeZone))->create($jobTransferId,
                AuditListEnums::APPROVAL_TYPE_JT,
                $submitterId,
                null,
                $extend
            );
            if ($ret === false) {
                $db->rollback();
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->write_log('add Job Transfer:' . $e->getMessage() . $e->getTraceAsString() , 'notice');
            return $this->checkReturn(-3, $e->getMessage());
        }
        return $this->checkReturn(['id' => $jobTransferId]);
    }

    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom): array
    {
        //获取转岗详情
        $result  = $this->getJobTransferInfo(['id' => $auditId]);
        $auditRepo = new AuditlistRepository($this->lang, $this->timeZone);

        $headData = [
            'title'               => $auditRepo->getAudityType(AuditListEnums::APPROVAL_TYPE_JT),
            'id'                  => $result['id'],
            'staff_id'            => $result['submitter_id'],
            'type'                => AuditListEnums::APPROVAL_TYPE_JT,
            'created_at'          => DateHelper::utcToLocal($result['created_at']),
            'updated_at'          => DateHelper::utcToLocal($result['updated_at']),
            'status'              => $result['state'],
            'status_text'         => $auditRepo->getAuditState($result['state']),
            'serial_no'           => $result['serial_no'] ?? '',
            'after_department_id' => $result['after_department_id'],
            'after_job_title_id'  => $result['after_position_id'],
            'hire_date'           => $result['hire_date'],
            'contract_company_id' => $result['contract_company_id'],
        ];

        //如果存在可编辑字段，需返回默认参数
        $auditShowType  = $this->getAuditDetailRequest()->getAuditShowType();
        $auditStateType = $this->getAuditDetailRequest()->getAuditStateType();
        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);

        //审批状态为待审批 && 并且查看我的待审批 && 存在可编辑字段时，
        //返回当前节点的可编辑字段
        if ($result['approval_state'] == enums::APPROVAL_STATUS_PENDING &&
            $auditShowType . $auditStateType == '21' &&
            $approvalServer->isExistCanEditField($auditId, AuditListEnums::APPROVAL_TYPE_JT)
        ) {

            //默认参数
            $headData = array_merge($headData, $this->getDefaultParameter($result));

            //获取可编辑字段
            $canEditField = $approvalServer->getCanEditFieldColumns($auditId, AuditListEnums::APPROVAL_TYPE_JT,
                AuditDetailOperationsEnums::RESPONSE_STRUCTURE_COLUMN_NAME);

            //过滤可编辑字段
            $filterColumns = $this->getValidateFilterColumns($result);

            $canEditField = array_values(array_filter($canEditField, function ($v) use ($filterColumns) {
                return !in_array($v, $filterColumns);
            }));
        }

        //组织详情数据
        $returnData['data'] = $this->genTransferDetail($result);
        $returnData['data']['head'] = $headData;
        $returnData['data']['can_edit_field'] = $canEditField ?? [];

        return $returnData;
    }

    /**
     * 获取校验过滤字段
     * @param $transfer_info
     * @return array
     */
    public function getValidateFilterColumns($transfer_info): array
    {
        $filterColumns[] = 'after_vehicle_source';
        $filterColumns[] = 'after_rental_car_created_at';

        if (!in_array($transfer_info['after_position_id'], $this->getShowCarTypeJobTitle())) {
            $filterColumns[] = 'car_type';
        }

        if ($this->getTransferType($transfer_info['after_department_id'], $transfer_info['after_position_id']) == JobTransferEnums::JOB_TRANSFER_TYPE_NOT_FRONT_LINE) {
            $filterColumns[] = 'salary_type';
        }
        return $filterColumns;
    }

    /**
     * @description 获取被转岗人信息
     * @param $transferStaffInfo
     * @param $submitterId
     * @return array
     */
    public function getJobTransferStaffInfo($transferStaffInfo, $submitterId): array
    {
        $storeId = $transferStaffInfo['store_id'];
        if (!empty($storeId)) { //获取被转岗人所在网点对应的大区、片区名
            $storeInfo = (new SysStoreServer())->getStorePieceAndRegionInfo($storeId);
        }

        //如果被转岗人是Van Courier、Car Courier
        //则获取车类型
        if (in_array($transferStaffInfo['job_title'], $this->getShowCarTypeJobTitle())) {
            $vehicleInfo = $this->getVehicleDetailByStaffId($transferStaffInfo['staff_info_id']);
            if (!empty($vehicleInfo['vehicle_type_category'])) {
                $vehicleTypeCategory = VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST[(int)$vehicleInfo['vehicle_type_category']] ?? '';
            }
            $vehicleInfo = ['car_type' => $vehicleTypeCategory ?? ''];
        }
        $transferStaffInfo['hire_type_name'] = $this->getTranslation()->_('hire_type_' . $transferStaffInfo['hire_type']);

        $result = [
            'staff_info' => array_merge($transferStaffInfo, $storeInfo ?? [], $vehicleInfo ?? []),
        ];

        //判断被转岗人是否为一线职位
        //被转岗人所属部门、职位在一线职位配置中
        $result['front_line']                    = $this->getTransferType($transferStaffInfo['node_department_id'],
            $transferStaffInfo['job_title']);
        $result['submitter_upper_department_id'] = $this->getSubmitterDepartmentId($submitterId);

        return $result;
    }

    /**
     * 显示车类型职位
     * @return array
     */
    protected function getShowCarTypeJobTitle()
    {
        return VehicleInfoEnums::$job_title_has_car_type;
    }

    /**
     * @description 组织转岗详情
     * @param $transfer_info
     * @return array
     */
    protected function genTransferDetail($transfer_info): array
    {
        //获取提交人用户信息
        $staffServer = new StaffServer();
        $staffInfo = $staffServer->get_staff($transfer_info['submitter_id']);
        if($staffInfo['data']){
            $staffInfo = $staffInfo['data'];
        }

        $transferInfo = $staffServer->get_staff($transfer_info['staff_id']);
        if($transferInfo['data']){
            $transferInfo = $transferInfo['data'];
        }

        //组织详情数据
        //[1]通用详情
        $detailLists = [
            'apply_parson'     => sprintf('%s ( %s )',$staffInfo['name'] ?? '' , $staffInfo['id'] ?? ''),
            'apply_department' => sprintf('%s - %s',$staffInfo['depart_name'] ?? '' , $staffInfo['job_name'] ?? ''),
            'transfer_parson'  => sprintf('%s ( %s )',$transferInfo['name'] ?? '' , $transferInfo['id'] ?? ''),
        ];

        //[2]转岗前/后详情
        if ($transfer_info['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE) {
            $beforeDetailList = [
                'job_transfer.before_department' => $transfer_info['current_department_name'],
                'job_transfer.before_store'      => $transfer_info['current_store_name'],
                'job_transfer.before_region'     => $transfer_info['current_region_name'],
                'job_transfer.before_piece'      => $transfer_info['current_piece_name'],
                'job_transfer.before_position'   => $transfer_info['current_position_name'],
                'job_transfer.before_hire_type'  => $transfer_info['current_hire_type_name'],
            ];
            if ($transfer_info['current_hire_type'] == HrStaffInfoModel::HIRE_TYPE_2) {
                $beforeDetailList['job_transfer.before_hire_times'] = $transfer_info['current_hire_times'] . $this->getTranslation()->_('monthlies');
            }
            if (!empty($transfer_info['current_car_type'])) {
                $beforeDetailList['job_transfer.before_car_type'] = VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST[$transfer_info['current_car_type']] ?? '';
            }

            $afterDetailList = [
                'job_transfer.after_department' => $transfer_info['after_department_name'],
                'job_transfer.after_position'   => $transfer_info['after_position_name'],
            ];
            //如果转岗后职位未非显示车类型的职位，则不显示车类型
            if (in_array($transfer_info['after_position_id'], $this->getShowCarTypeJobTitle()) && in_array($transfer_info['current_hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
                $afterDetailList['job_transfer.after_car_type'] = VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST[$transfer_info['after_car_type']] ?? '';
            }

            $afterDetailList = array_merge($afterDetailList, [
                'job_transfer.after_store'      => $transfer_info['after_store_name'],
                'job_transfer.expect_date'      => date('Y-m-d', strtotime($transfer_info['after_date'])),
                'work_handover'                 => sprintf('%d %s', $transfer_info['job_handover_staff_id'],
                    $transfer_info['job_handover_staff_name']),
                'jobtransfer_0017'              => sprintf('%s %s', $transfer_info['transfer_reason_name'],
                    $transfer_info['reason']),
            ]);

            if (!empty($transfer_info['after_manager_id'])) {
                if (is_string($transfer_info['after_role_ids']) && strlen($transfer_info['after_role_ids']) > 0) {
                    [$rolesIds, $rolesNames] = $this->getStaffRoles($transfer_info['after_role_ids']);
                }
                $additionDetail = [
                    'job_transfer.manager_info'           => sprintf('%d(%s)', $transfer_info['after_manager_id'],
                        $transfer_info['after_manager_name']),
                    'job_transfer.role'                   => $rolesNames ?? '',
                    'job_transfer.working_days_rest_type' => $this->getTranslation()->_('working_day_rest_type_' . $transfer_info['after_working_day_rest_type']) ?? '',
                    'job_transfer.after_hire_type'        => $transfer_info['after_hire_type_name'],
                ];
                if ($transfer_info['after_hire_type'] == HrStaffInfoModel::HIRE_TYPE_2) {
                    $additionDetail['job_transfer.after_hire_times'] = $transfer_info['after_hire_times'] . $this->getTranslation()->_('monthlies');
                }

                //如果转岗后职位未非显示车类型的职位，则不显示车类型
                if (in_array($transfer_info['after_position_id'], $this->getShowCarTypeJobTitle()) && !in_array($transfer_info['current_hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
                    $additionDetail['job_transfer.after_car_type'] = VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST[$transfer_info['after_car_type']] ?? '';
                }
                $additionDetail['job_transfer.upload_files'] = $this->getUploadFiles($transfer_info['id']);
            }
        } else if ($transfer_info['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_NOT_FRONT_LINE) {
            $beforeDetailList = [
                'job_transfer.before_department' => $transfer_info['current_department_name'],
                'job_transfer.before_position'   => $transfer_info['current_position_name'],
                'job_transfer.before_store'      => $transfer_info['current_store_name'],
                'job_transfer.before_hire_type'  => $transfer_info['current_hire_type_name'],
            ];
            if ($transfer_info['current_hire_type'] == HrStaffInfoModel::HIRE_TYPE_2) {
                $beforeDetailList['job_transfer.before_hire_times'] = $transfer_info['current_hire_times'] . $this->getTranslation()->_('monthlies');
            }

            if (!empty($transfer_info['hc_id'])) {
                $afterDetailList = [
                    'job_transfer.after_department' => $transfer_info['after_department_name'],
                    'job_transfer.after_position'   => $transfer_info['after_position_name'],
                    'job_transfer.after_store'      => $transfer_info['after_store_name'],
                    'job_transfer.expect_date'      => date('Y-m-d', strtotime($transfer_info['after_date'])),
                    'work_handover'                 => sprintf('%d %s', $transfer_info['job_handover_staff_id'],
                        $transfer_info['job_handover_staff_name']),
                    'jobtransfer_0017'              => sprintf('%s %s', $transfer_info['transfer_reason_name'],
                        $transfer_info['reason']),
                ];
            } else {
                $afterDetailList = [
                    'job_transfer.expect_date'      => date('Y-m-d', strtotime($transfer_info['after_date'])),
                    'work_handover'                 => sprintf('%d %s', $transfer_info['job_handover_staff_id'],
                        $transfer_info['job_handover_staff_name']),
                    'jobtransfer_0017'              => sprintf('%s %s', $transfer_info['transfer_reason_name'],
                        $transfer_info['reason']),
                ];
            }

            if (!empty($transfer_info['after_manager_id'])) {
                [$rolesIds, $rolesNames] = $this->getStaffRoles($transfer_info['after_role_ids']);
                $additionDetail = [
                    'job_transfer.manager_info'           => sprintf('%d(%s)', $transfer_info['after_manager_id'],
                        $transfer_info['after_manager_name']),
                    'job_transfer.role'                   => $rolesNames ?? '',
                    'job_transfer.working_days_rest_type' => $this->getTranslation()->_('working_day_rest_type_' . $transfer_info['after_working_day_rest_type']) ?? '',
                    'job_transfer.upload_files'           => $this->getUploadFiles($transfer_info['id']),
                    'job_transfer.after_hire_type'        => $transfer_info['after_hire_type_name'],
                ];
                if ($transfer_info['after_hire_type'] == HrStaffInfoModel::HIRE_TYPE_2) {
                    $additionDetail['job_transfer.after_hire_times'] = $transfer_info['after_hire_times'] . $this->getTranslation()->_('monthlies');
                }
            }
        } else {
            $beforeDetailList = [];
            $afterDetailList  = [];
            $additionDetail   = [];
        }

        return [
            'detail'          => $this->format($detailLists),
            'before_detail'   => $this->format($beforeDetailList),
            'after_detail'    => $this->format($afterDetailList),
            'addition_detail' => !empty($additionDetail) ? $this->format($additionDetail) : [],
        ];
    }

    /**
     * @description 获取编辑下拉列表
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function getEditList($paramIn)
    {
        $auditId = $this->processingDefault($paramIn, 'audit_id');
        $transferDetail = JobTransferModel::findFirst($auditId);
        if (empty($transferDetail)) {
            throw new ValidationException('no valid data');
        }

        //指定部门、职位关联的角色
        $result['role_list'] = $this->getRelateRoles($paramIn['department_id'], $paramIn['job_title_id'], $transferDetail->after_store_id);

        //工作天数与轮休规则
        $result['working_day_rest_type'] = $this->getWorkingDayRestType($paramIn['department_id'], $paramIn['job_title_id']);

        //车类型
        $result['car_type'] = $this->getVehicleType($auditId);

        //雇佣类型
        $result['hire_type'] = $this->getHireType();

        //薪资情况
        $result['salary_type'] = $this->getSalaryType();

        return $this->checkReturn(['data' => $result]);
    }

    /**
     * 获取车类型
     * @param $audit_id
     * @return array|array[]
     */
    public function getVehicleType($audit_id = 0): array
    {

        $transferInfo = JobTransferModel::findFirst($audit_id);
        if (!$transferInfo) {
            return [];
        }

        $lntVehicleType = $vehicleType = [];
        $server = new StaffServer();

        if ($server->isAgentStaff($transferInfo->staff_id)) {
            return self::getCarTypeByJobTitle($transferInfo->after_position_id);
        } elseif($server->isLntStaff($transferInfo->staff_id)) { //如果是LNT公司
            $lnt_vehicle_type_ids = (new SettingEnvServer())->getSetVal('lnt_vehicle_type_ids', ',');

            foreach ($lnt_vehicle_type_ids as $oneType) {
                $typeInfo = explode('/', $oneType);
                $lntVehicleType[$typeInfo[0]][] = $typeInfo[1];
            }

            if(!empty($transferInfo->after_position_id) && isset($lntVehicleType[(int)$transferInfo->after_position_id])) {
                foreach ($lntVehicleType[(int)$transferInfo->after_position_id] as $oneTypes) {
                    if(!isset(VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST[$oneTypes])) {
                        continue;
                    }
                    $vehicleType[] = ['value' => intval($oneTypes), 'label' => VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST[$oneTypes]];
                }
                return $vehicleType;
            }

            return [];
        }

        $vehicleType = !empty($transferInfo->after_position_id)
            ? VehicleInfoEnums::JOB_TITLE_VEHICLE_CATEGORY_ITEM[(int)$transferInfo->after_position_id]
            : [];
        if (empty($vehicleType)) {
            return [];
        }
        return $vehicleType;
    }

    /**
     * @description 转岗获取职位下拉列表
     * @param $department_id
     * @param $job_title_id
     * @return array
     */
    public function getWorkingDayRestType($department_id, $job_title_id): array
    {
        if (empty($department_id) || empty($job_title_id)) {
            return [];
        }

        //获取部门职位关联的的JD
        $data = HrJobDepartmentRelationModel::findFirst([
            'conditions' => 'department_id = :department_id: and job_id = :job_id:',
            'bind'       => [
                'department_id' => $department_id,
                'job_id'        => $job_title_id,
            ],
        ]);
        if (empty($data)) {
            return [];
        }

        $returnData         = [];
        $workingDayRestType = explode(',', $data->working_day_rest_type);
        foreach ($workingDayRestType as $key => $value) {
            //排除掉自由轮休
            if ($value == HrStaffInfoModel::WEEK_WORKING_DAY_FREE . HrStaffInfoModel::REST_TYPE_1) {
                continue;
            }
            $returnData[] = [
                'key'   => $value,
                'value' => $this->getTranslation()->_('working_day_rest_type_' . $value),
            ];
        }
        return $returnData;
    }

    /**
     * @description 获取BP审批字段校验
     * @param array $params
     * @return array
     */
    public function getBpValidate(array $params = []): array
    {
        //过滤字段
        $validations = [];

        //获取转岗信息
        $approvalServer = new ApprovalServer($this->lang, $this->timeZone);
        $detailInfo = (new JobtransferRepository($this->timeZone))->getJobtransferInfo(['id' => $params['audit_id']]);

        //自定义校验规则
        $validations['car_type'] = 'IntIn:3,4,5,6,7,8,9';
        $validations['hire_type'] = 'IntIn:1,2';
        $validations['hire_times'] = 'IfIntEq:hire_type,2|Required|IntGeLe:1,12';

        $filterColumns = $this->getValidateFilterColumns($detailInfo);

        //对于存在可以编辑字段的情况，取出可编辑字段的类型
        return $approvalServer->getCanEditFieldValidation($params['audit_id'], AuditListEnums::APPROVAL_TYPE_JT, $filterColumns, $validations);
    }

    /**
     * @description 转岗审核
     * 审批人一般为BP，存在可编辑的字段
     * @throws ValidationException|BusinessException
     */
    public function auditTransfer($paramIn = [])
    {
        //[1]获取参数
        $auditId                 = $this->processingDefault($paramIn, 'audit_id');
        $afterManagerId          = $this->processingDefault($paramIn, 'after_manager_id');
        $roles                   = $this->processingDefault($paramIn, 'after_role_ids');
        $afterWorkingDayRestType = $this->processingDefault($paramIn, 'after_working_day_rest_type');
        $vehicleSource           = $this->processingDefault($paramIn, 'after_vehicle_source');
        $rentalCarCreatedAt      = $this->processingDefault($paramIn, 'after_rental_car_created_at');
        $carType                 = $this->processingDefault($paramIn, 'car_type');
        $hcId                    = $this->processingDefault($paramIn, 'hc_id');
        $uploadFiles             = $this->processingDefault($paramIn, 'upload_files');
        $staffInfoId             = $this->processingDefault($paramIn, 'staff_id');
        $status                  = $this->processingDefault($paramIn, 'status');
        $hireType                = $this->processingDefault($paramIn, 'hire_type');
        $hireTimes               = $this->processingDefault($paramIn, 'hire_times');
        $salaryType              = $this->processingDefault($paramIn, 'salary_type');

        //获取详情
        $transferDetail = JobTransferModel::findFirst($auditId);
        if (empty($transferDetail)) {
            throw new ValidationException('no valid data');
        }
        $staffServer = new StaffServer();
        $staffInfo   = $staffServer->getStaffById($transferDetail->staff_id);

        $updateParams = [];
        //获取转岗后直线上级
        if (!empty($afterManagerId)) {

            //转岗后直线上级不能为员工本人
            if ($afterManagerId == $transferDetail->staff_id) {
                throw new ValidationException($this->getTranslation()->_('job_transfer.can_not_be_himself'));
            }

            $updateParams['after_manager_id'] = $afterManagerId;
        }
        if (!empty($afterWorkingDayRestType)) {
            $updateParams['after_working_day_rest_type'] = $afterWorkingDayRestType;
        }
        if (!empty($vehicleSource)) {
            $updateParams['car_owner'] = $vehicleSource;
            //租用公司车辆才有开始时间
            if ($vehicleSource == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
                if (strtotime($rentalCarCreatedAt) < strtotime(date('Y-m-d', strtotime($staffInfo['hire_date'])))) {
                    throw new ValidationException($this->getTranslation()->_('job_transfer.can_not_earlier_than_hire_date'));
                }

                $updateParams['rental_car_cteated_at'] = $rentalCarCreatedAt;
            }
        }
        if (!empty($carType)) {
            $updateParams['after_car_type'] = $carType;
        }

        if (!empty($hireType)) {
            //转岗前一线，雇佣类型变更
            if ($transferDetail->type == JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE &&
                $transferDetail->current_hire_type != $hireType) {
                throw new ValidationException($this->getTranslation()->_('job_transfer.can_not_change_hire_type'));
            }
            $updateParams['after_hire_type'] = $hireType;
            if ($hireType == HrStaffInfoModel::HIRE_TYPE_2) { //月薪制
                if (!is_numeric($hireTimes)) {
                    throw new ValidationException($this->getTranslation()->_('job_transfer.miss_hire_times'));
                }
                $updateParams['after_hire_times'] = $hireTimes;
            }
            if ($hireType == HrStaffInfoModel::HIRE_TYPE_1) {
                $updateParams['after_hire_times'] = 0;
            }
        }

        if (!empty($hcId)) {
            $updateParams['hc_id'] = $hcId;
            $hcRepo = new HcRepository($this->timeZone);
            $hcInfo = $hcRepo->getHcInfo($hcId);
            if (empty($hcInfo)) {
                throw new ValidationException($this->getTranslation()->_('4412'));
            }
            //获取网点信息
            if ($hcInfo['worknode_id'] != enums::HEAD_OFFICE_ID) {
                $repository                      = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
                $afterStoreData                  = $repository->getOrganizationRegionPiece($hcInfo['worknode_id']);
                $updateParams['after_piece_id']  = !empty($afterStoreData) ? $afterStoreData->piece_id : 0;
                $updateParams['after_region_id'] = !empty($afterStoreData) ? $afterStoreData->region_id : 0;
            }
            //转岗后公司ID
            $afterDepartmentInfo = (new DepartmentRepository())->getSpecDepartmentInfo($hcInfo['department_id'], 'id,company_id');

            $updateParams['after_department_id']   = $hcInfo['department_id'];
            $updateParams['after_store_id']        = $hcInfo['worknode_id'];
            $updateParams['after_position_id']     = $hcInfo['job_title'];
            $updateParams['after_job_title_grade'] = $this->calcJobTitleGrade(JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
                $hcInfo['department_id'],
                $hcInfo['job_title'], $transferDetail->current_job_title_grade);
            $updateParams['after_company_id']      = $afterDepartmentInfo['company_id'] ?? 0;
        }

        if (!empty($roles) && is_array($roles)) {
            if (!empty($hcId)) {
                $afterStoreId = $updateParams['after_store_id'];
            } else {
                $afterStoreId = $transferDetail->after_store_id;
            }
            if (in_array(enums::$roles['BRANCH_CASHIER'], $roles)) {
                $this->checkStoreCashier($afterStoreId);
            }
            $updateParams['after_role_ids'] = implode(',', $roles);
        }
        if (!empty($salaryType)) {
            $updateParams['salary_type'] = $salaryType;
        }
        //「转岗前职位」和「转岗后职位」都是一线职位
        $frontJobTitles = $this->getFrontLineConfig();
        if (!empty($salaryType) && $salaryType == JobTransferEnums::SALARY_TYPE_SALARY_STRUCTURE &&
            !empty($afterManagerId) &&
            !in_array($transferDetail->current_hire_type, HrStaffInfoModel::$agentTypeTogether) &&
            in_array($transferDetail->current_position_id, $frontJobTitles) &&
            in_array($transferDetail->after_position_id, $frontJobTitles)) {
            $queryParams = [
                'staff_id'            => $transferDetail->staff_id,
                'current_position_id' => $transferDetail->current_position_id,
                'after_position_id'   => $transferDetail->after_position_id,
                'current_store_id'    => $transferDetail->current_store_id,
                'after_store_id'      => $transferDetail->after_store_id,
                'after_date'          => $transferDetail->after_date,
                'after_car_type'      => $carType,
            ];
            $salaryInfo = (new JobTransferConfirmServer($this->lang, $this->timeZone))->getTransferSalary($queryParams);
            if (empty($salaryInfo)) {
                throw new ValidationException($this->getTranslation()->_('job_transfer.salary_has_changed'));
            }
            if ($salaryInfo['before_base_salary'] != $salaryInfo['after_base_salary']) {
                throw new ValidationException($this->getTranslation()->_('job_transfer.salary_has_changed'));
            }
        }

        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            $res = $db->updateAsDict("job_transfer",
                $updateParams, [
                    'conditions' => "id = ?",
                    'bind' => [
                        $auditId,
                    ],
                ]);
            if (!empty($hcId)) {
                (new HcServer($this->lang, $this->timeZone))->deductHc($hcId);
            }

            //如果是非一线转岗，需要在更新HC id的时候，重新查找转岗后的HRBP。
            //因为在申请的时候，这个人由于确实转岗后部门+网点，这个人是找不到的
            if ($status == enums::APPROVAL_STATUS_APPROVAL && !empty($hcId)) {
                $request = (new ApplyRepository())->getApplyObject(AuditListEnums::APPROVAL_TYPE_JT, $auditId);
                WorkflowPersistentServer::getInstance($this->lang, $this->timeZone)
                    ->refreshHasPersistentNode($request, $staffInfoId, $this->getParseNodeParams($auditId, $staffInfoId, $status));
            }

            //插入图片
            if(!empty($uploadFiles)) {
                $insertImgData  = [];
                //软删除已经添加的图片
                $images = SysAttachmentModel::find([
                    'conditions' => "oss_bucket_type = 'JOB_TRANSFER' and oss_bucket_key = :oss_bucket_key: ",
                    'bind' => [
                        'oss_bucket_key'  => $auditId,
                    ],
                ])->toArray();
                $images = array_column($images, 'object_key');
                foreach($uploadFiles as $image) {
                    $urlInfo = parse_url(stripslashes($image));
                    if (in_array(trim($urlInfo['path'], '/'), $images)) {
                        continue;
                    }

                    if (is_array($image)) { //需要保存原文件名
                        $insertImgData[] = [
                            'id'            => $auditId,
                            'image_path'    => $image['url'],
                            'original_name' => $image['file_name'],
                        ];
                    } else {
                        $insertImgData[] = [
                            'id'         => $auditId,
                            'image_path' => $image,
                        ];
                    }
                }
                (new PublicRepository())->batchInsertImgs($insertImgData, "JOB_TRANSFER");
            }

            $this->approvalTransfer($paramIn);

            $db->commit();

            return $this->checkReturn(['data' => $auditId]);
        } catch (ValidationException|\Exception $e) {
            $db->rollback();
            $this->logger->write_log('[auditTransfer]' . $e->getMessage() . $e->getTraceAsString(), 'notice');
        }

        return $this->checkReturn(['data' => null]);
    }

    /**
     * @description 处理一阶段审批
     * @param $transferDetail
     * @param $state
     * @param $extend
     * @param $isFinal
     */
    protected function processStageOne($transferDetail, $state, $extend, $isFinal)
    {
        //处理审批状态
        if (enums::APPROVAL_STATUS_APPROVAL == $state) {

            //1. 更新一阶段转岗状态、转岗确认状态
            $updateData = [
                'approval_state_stage_one' => enums::APPROVAL_STATUS_APPROVAL,
                'confirm_state'            => JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM,
            ];

            //2. 添加确认log
            $async_params = [
                'staff_info_id' => $transferDetail->staff_id,
                'audit_id'      => $transferDetail->id,
            ];
            $this->logger->write_log([
                'message'   => '异步写入待确认log',
                'redis_key' => RedisEnums::REDIS_ASYNC_JOB_TRANSFER,
                'params'    => $async_params,
            ],'info');
            $redis = $this->getDI()->get('redisLib');
            $redis->lpush(RedisEnums::REDIS_ASYNC_JOB_TRANSFER, json_encode($async_params));

            //转岗前是个人代理没有bp节点，需补充转岗后上级、转岗后角色、转岗后工作天数与轮休规则、转岗后雇佣类型、转岗后雇佣期间
            if (in_array($transferDetail->current_hire_type, HrStaffInfoModel::$agentTypeTogether)) {
                $updateData['after_manager_id']            = $this->getafterManagerIdInfo($transferDetail->after_store_id,
                    $transferDetail->after_department_id,
                    $transferDetail->after_position_id,
                    $transferDetail->after_date,
                    $transferDetail->staff_id
                );
                if ($transferDetail->current_hire_type == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                    $hireType = HrStaffInfoModel::WEEK_WORKING_DAY_6 . HrStaffInfoModel::REST_TYPE_1;
                } else {
                    $hireType = HrStaffInfoModel::WEEK_WORKING_DAY_FREE . HrStaffInfoModel::REST_TYPE_1;
                }
                $updateData['after_working_day_rest_type'] = $hireType;
                $updateData['after_role_ids']              = $transferDetail->current_role_id;
                $updateData['after_hire_type']             = $transferDetail->current_hire_type;
                $updateData['after_hire_times']            = HrStaffInfoModel::DEFAULT_MONTHLY_HIRE_TIME;
            }

            //确认单生成日期
            $updateData['confirmation_gen_date'] = date('Y-m-d');

        } else if (enums::APPROVAL_STATUS_REJECTED == $state) {
            $updateData = [
                'approval_state'           => enums::APPROVAL_STATUS_REJECTED,
                'approval_state_stage_one' => enums::APPROVAL_STATUS_REJECTED,
                'state'                    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
            ];
        } else if (enums::APPROVAL_STATUS_CANCEL == $state) {
            $updateData = [
                'approval_state'           => enums::APPROVAL_STATUS_CANCEL,
                'approval_state_stage_one' => enums::APPROVAL_STATUS_CANCEL,
                'state'                    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
            ];
        } else {
            $updateData = [
                'approval_state'           => enums::APPROVAL_STATUS_TIMEOUT,
                'approval_state_stage_one' => enums::APPROVAL_STATUS_TIMEOUT,
                'state'                    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
            ];
        }
        $this->getDI()->get('db')->updateAsDict(
            'job_transfer',
            $updateData,
            'id = ' . $transferDetail->id
        );
    }

    /**
     * @description 处理二阶段审批
     * @param $transferDetail
     * @param $state
     * @param $extend
     * @param $isFinal
     */
    protected function processStageTwo($transferDetail, $state, $extend, $isFinal)
    {
        if (enums::APPROVAL_STATUS_APPROVAL == $state) {
            $updateData = [
                "approval_state"           => enums::APPROVAL_STATUS_APPROVAL,
                'approval_state_stage_two' => enums::APPROVAL_STATUS_APPROVAL,
            ];
        } else if (enums::APPROVAL_STATUS_REJECTED == $state) {
            $updateData = [
                "approval_state"           => enums::APPROVAL_STATUS_REJECTED,
                'approval_state_stage_two' => enums::APPROVAL_STATUS_REJECTED,
                "state"                    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
            ];
            if ($transferDetail->confirm_state == JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM) {
                $updateData['confirm_state'] = JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_REJECT;
            }
        } else if (enums::APPROVAL_STATUS_CANCEL == $state) {
            $updateData = [
                "approval_state"           => enums::APPROVAL_STATUS_CANCEL,
                'approval_state_stage_two' => enums::APPROVAL_STATUS_CANCEL,
                "state"                    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
            ];
            if ($transferDetail->confirm_state == JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM) {
                $updateData['confirm_state'] = JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_CANCEL;
            }
        } else {
            $updateData = [
                "approval_state"           => enums::APPROVAL_STATUS_TIMEOUT,
                'approval_state_stage_two' => enums::APPROVAL_STATUS_TIMEOUT,
                "state"                    => JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED,
            ];
            if ($transferDetail->confirm_state == JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM) {
                $updateData['confirm_state'] = JobTransferConfirmEnums::CONFIRM_STATE_OVER_TIME;
            }
        }
        $this->getDI()->get('db')->updateAsDict(
            'job_transfer',
            $updateData,
            'id = ' . $transferDetail->id
        );
    }

    /**
     * 立即转岗
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function doJobTransfer($params = []): array
    {
        $data       = $params['data'] ?? [];
        $manual     = $params['manual_operate'] ?? [];
        $operatorId = $params['operator_id'] ?? 10000;

        $staffObj = new StaffServer();
        $hcServer = new HcServer($this->lang, $this->timeZone);
        $acknowledgeServer = new JobTransferAcknowledgementServer($this->lang, $this->timeZone);
        $jobTransferRepo = new JobtransferRepository($this->timeZone);

        //消息发送黑名单
        $blackList = $this->getBlackList();
        //与车辆有关的职位
        //获取配置
        $configList = (new SettingEnvServer())->listByCode(['job_title_vehicle_type', 'agent_job_transfer_message_staff_ids']);
        $configList = array_column($configList, 'set_val', 'code');

        //与车辆有关的职位
        $vehicleJobTitle = isset($configList['job_title_vehicle_type'])
            ? explode(CommonEnums::SEPARATOR_DEFAULT, $configList['job_title_vehicle_type'])
            : JobTransferEnums::TRANSFER_DEFAULT_JOB_TITLE;

        foreach ($data as $v) {
            if ($v['state'] == JobTransferModel::JOBTRANSFER_STATE_TRANSFERED) {
                continue;
            }
            if (in_array($v['after_hire_type'], HrStaffInfoModel::$agentTypeTogether)) { //个人代理给执行工号发送消息
                $hrBpList = isset($configList['agent_job_transfer_message_staff_ids'])
                    ? explode(CommonEnums::SEPARATOR_DEFAULT, $configList['agent_job_transfer_message_staff_ids'])
                    : [];
            } else {
                //获取hrBp
                $beforeHrBpList = ApprovalFinderServer::getInstance()->findHrBp($v['current_department_id'],
                    $v['current_store_id']);
                $afterHrBpList  = ApprovalFinderServer::getInstance()->findHrBp($v['after_department_id'],
                    $v['after_store_id']);
                $this->logger->write_log(sprintf('转岗前HrBP:%s,转岗后HrBP:%s', json_encode($beforeHrBpList),
                    json_encode($afterHrBpList)), 'info');
                $hrBpList = array_merge($beforeHrBpList, $afterHrBpList);
                $hrBpList = array_values(array_filter(array_unique($hrBpList)));
            }

            //获取要发送消息的人的语言
            $submitterLanguage = $staffObj->getLanguage($v['submitter_id']);
            $transferLanguage  = $staffObj->getLanguage($v['staff_id']);
            $managerLanguage   = $staffObj->getLanguage($v['after_manager_id']);

            //如果转岗后上级离职，则重新查上级
            //https://flashexpress.feishu.cn/docx/G7KYdMULpoDprDxzF7GcS2wunag
            $afterManagerInfo = $staffObj->getStaffInfo(['staff_info_id' => $v['after_manager_id']]);

            //如没有编辑过上级，则重新获取上级
            if (!empty($afterManagerInfo) && $afterManagerInfo['state'] == HrStaffInfoModel::STATE_RESIGN) {
                $manager_id = $this->getafterManagerIdInfo($v['after_store_id'], $v['after_department_id'],
                    $v['after_position_id'], $v['after_date'], $v['staff_id']);
                if ($manager_id != $v["after_manager_id"]) {
                    $this->logger->write_log([
                        'do_transfer_manager' => [
                            'before' => $v["after_manager_id"],
                            'after'  => $manager_id,
                        ],
                    ], "info");
                    $v["after_manager_id"] = $manager_id;
                }
            }

            if (in_array($v['after_hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
                $sendMessageToTransferType = JobTransferMessageServer::SEND_MESSAGE_TO_AGENT_TRANSFER;
            } else {
                $sendMessageToTransferType = JobTransferMessageServer::SEND_MESSAGE_TO_TRANSFER;
            }

            //一线、非一线转岗
            if ($v['type'] != JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                $errMessageKey = '';
                if ($v['confirm_state'] == JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM) {
                    //有新的转岗确认单未签署
                    $errMessageKey = 'job_transfer_confirm_state_pending_err';
                } else if ($v['confirm_state'] == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_REJECT) {
                    //转岗确认单点击拒绝，不可转岗
                    $errMessageKey = 'job_transfer_confirm_state_reject_err';
                } else if ($manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER) { //手动立即转岗
                    $item = JobTransferModel::findFirst($v['id']);
                    if ($v['salary_type'] == JobTransferEnums::SALARY_TYPE_SALARY_STRUCTURE && !in_array($v['current_hire_type'],
                            HrStaffInfoModel::$agentTypeTogether) && $this->isJobTransferConfirmationChanged($item)) {
                        //薪资结构发生变化，请重新走转岗流程
                        $errMessageKey = 'job_transfer_salary_structure_changed_err';
                    }
                }

                if (!empty($errMessageKey)) {
                    if ($manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER) { //立即转岗失败不返还HC
                        $isRemittanceFlag = false;
                    } else { //到期转岗失败要返还HC
                        $isRemittanceFlag = true;
                    }
                    $this->transferFailed($v['id'], $v['hc_id'], $isRemittanceFlag);

                    $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-确认状态异常!(%s),不能转岗', $v['staff_id']), 'info');
                    $failure_reason = [
                        'zh-CN' => $this->getTranslationByLang('zh-CN')->t($errMessageKey),
                        'en' => $this->getTranslationByLang('en')->t($errMessageKey),
                        'th' => $this->getTranslationByLang('th')->t($errMessageKey),
                    ];
                    //记录操作日志
                    $this->saveOperateLog([
                        'id'             => $v['id'],
                        'staff_info_id'  => $operatorId,
                        'operate_id'     => $manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                            ? JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                            : JobTransferEnums::SYSTEM_AUTO_DO_JOB_TRANSFER,
                        'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                        'failure_reason' => json_encode($failure_reason),
                    ]);

                    //给申请人发送消息
                    JobTransferMessageServer::getInstance()->noticeTransferFail($v['submitter_id'],
                        $submitterLanguage,
                        $v,
                        $errMessageKey,
                        JobTransferMessageServer::SEND_MESSAGE_TO_SUBMITTER
                    );

                    //给被转岗人发送消息
                    JobTransferMessageServer::getInstance()->noticeTransferFail($v['staff_id'],
                        $transferLanguage,
                        $v,
                        $errMessageKey,
                        $sendMessageToTransferType
                    );

                    //给HrBP发送消息
                    foreach ($hrBpList as $item) {
                        if (in_array($item, $blackList)) {
                            continue;
                        }
                        JobTransferMessageServer::getInstance()->noticeTransferFail($item,
                            $staffObj->getLanguage($item),
                            $v,
                            $errMessageKey,
                            JobTransferMessageServer::SEND_MESSAGE_TO_HRBP
                        );
                    }
                    $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-确认状态异常!(%s)转岗失败消息提醒发送完毕。', $v['staff_id']), 'info');
                    continue;
                }
            }


            //[1.1](仅立即转岗进来才校验)校验是否预算充足｜存在可用HC
            if ($manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER && $v['type'] != JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                $this->getDI()->get('db')->begin();
                [$currentHcId, $err] = $hcServer->occupyHc($v);
                $this->getDI()->get('db')->commit();
                if (!is_null($err) && $err->getCode() != ErrCode::SUCCESS) {
                    if ($err->getCode() == ErrCode::HC_BUDGET_EXHAUSTED) { //预算不足
                        $errMessageKey = 'job_transfer_budget_err';
                    } else if ($err->getCode() == ErrCode::ERR_NO_AVAILABLE_HC) {
                        $errMessageKey = 'job_transfer_not_valid_hc_err';
                    } else {
                        $errMessageKey = '4008';
                    }

                    //更新转岗状态为未转岗
                    $this->transferFailed($v['id'], $currentHcId, false);
                    $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-没有预算!(%s),不能转岗', $v['staff_id']), 'info');
                    $failure_reason = [
                        'zh-CN' => $this->getTranslationByLang('zh-CN')->t($errMessageKey),
                        'en' => $this->getTranslationByLang('en')->t($errMessageKey),
                        'th' => $this->getTranslationByLang('th')->t($errMessageKey),
                    ];
                    //记录操作日志
                    $this->saveOperateLog([
                        'id'             => $v['id'],
                        'staff_info_id'  => $operatorId,
                        'operate_id'     => JobTransferEnums::MANUAL_DO_JOB_TRANSFER,
                        'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                        'failure_reason' => json_encode($failure_reason),
                    ]);

                    //给申请人发送消息
                    JobTransferMessageServer::getInstance()->noticeTransferFail($v['submitter_id'],
                        $submitterLanguage,
                        $v,
                        $errMessageKey,
                        JobTransferMessageServer::SEND_MESSAGE_TO_SUBMITTER
                    );

                    //给被转岗人发送消息
                    JobTransferMessageServer::getInstance()->noticeTransferFail($v['staff_id'],
                        $transferLanguage,
                        $v,
                        $errMessageKey,
                        $sendMessageToTransferType
                    );

                    //给HrBP发送消息
                    foreach ($hrBpList as $item) {
                        if (in_array($item, $blackList)) {
                            continue;
                        }
                        JobTransferMessageServer::getInstance()->noticeTransferFail($item,
                            $staffObj->getLanguage($item),
                            $v,
                            $errMessageKey,
                            JobTransferMessageServer::SEND_MESSAGE_TO_HRBP
                        );
                    }
                    $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-有未回款项!(%s)转岗失败消息提醒发送完毕。', $v['staff_id']), 'info');
                    continue;
                }

                //更新hc
                $v['hc_id'] = $currentHcId;
            }

            //[1.2]校验是否有回款项；如果手里有未回款，不能转岗；
            $_re = $this->checkReceivableDetail([
                "staff_id" => $v['staff_id'],
                "store_id" => $v["current_store_id"],
            ]);
            if (!$_re) {
                //更新转岗状态为未转岗
                $this->transferFailed($v['id'], $v['hc_id']);
                $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-有未回款项!(%s)手里有未回款,不能转岗', $v['staff_id']), 'info');
                $failure_reason = [
                    'zh-CN' => $this->getTranslationByLang('zh-CN')->t('job_transfer_cod_err'),
                    'en' => $this->getTranslationByLang('en')->t('job_transfer_cod_err'),
                    'th' => $this->getTranslationByLang('th')->t('job_transfer_cod_err'),
                ];
                //记录操作日志
                $this->saveOperateLog([
                    'id'             => $v['id'],
                    'staff_info_id'  => $operatorId,
                    'operate_id'     => $manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        ? JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        : JobTransferEnums::SYSTEM_AUTO_DO_JOB_TRANSFER,
                    'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                    'failure_reason' => json_encode($failure_reason),
                ]);

                if ($v['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                    continue;
                }

                //给申请人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v['submitter_id'],
                    $submitterLanguage,
                    $v,
                    'job_transfer_cod_err',
                    JobTransferMessageServer::SEND_MESSAGE_TO_SUBMITTER
                );

                //给被转岗人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v['staff_id'],
                    $transferLanguage,
                    $v,
                    'job_transfer_cod_err',
                    $sendMessageToTransferType
                );

                //给HrBP发送消息
                foreach ($hrBpList as $item) {
                    if (in_array($item, $blackList)) {
                        continue;
                    }
                    JobTransferMessageServer::getInstance()->noticeTransferFail($item,
                        $staffObj->getLanguage($item),
                        $v,
                        'job_transfer_cod_err',
                        JobTransferMessageServer::SEND_MESSAGE_TO_HRBP
                    );
                }
                $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-有未回款项!(%s)转岗失败消息提醒发送完毕。', $v['staff_id']), 'info');
                continue;
            }
            //[1.3]校验工作是否完成；未完成任务（交接扫描但未妥投），不能转岗。
            $_re = $this->checkTicketDelivery([
                "staff_id" => $v["staff_id"],
            ]);
            if (!$_re) {
                //更新转岗状态为未转岗
                $this->transferFailed($v['id'], $v['hc_id']);
                $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-未完成任务!(%s)有未完成任务（交接扫描但未妥投）,不能转岗', $v['staff_id']), 'info');
                $failure_reason = [
                    'zh-CN' => $this->getTranslationByLang('zh-CN')->t('job_transfer_unfinish_task_err'),
                    'en' => $this->getTranslationByLang('en')->t('job_transfer_unfinish_task_err'),
                    'th' => $this->getTranslationByLang('th')->t('job_transfer_unfinish_task_err'),
                ];
                //记录操作日志
                $this->saveOperateLog([
                    'id'             => $v['id'],
                    'staff_info_id'  => $operatorId,
                    'operate_id'     => $manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        ? JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        : JobTransferEnums::SYSTEM_AUTO_DO_JOB_TRANSFER,
                    'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                    'failure_reason' => json_encode($failure_reason),
                ]);

                if ($v['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                    continue;
                }

                //给申请人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v["submitter_id"],
                    $submitterLanguage,
                    $v,
                    'job_transfer_unfinish_task_err',
                    JobTransferMessageServer::SEND_MESSAGE_TO_SUBMITTER
                );

                //给被转岗人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v["staff_id"],
                    $transferLanguage,
                    $v,
                    'job_transfer_unfinish_task_err',
                    $sendMessageToTransferType
                );

                //给HrBP发送消息
                foreach ($hrBpList as $item) {
                    if (in_array($item, $blackList)) {
                        continue;
                    }
                    JobTransferMessageServer::getInstance()->noticeTransferFail($item,
                        $staffObj->getLanguage($item),
                        $v,
                        'job_transfer_unfinish_task_err',
                        JobTransferMessageServer::SEND_MESSAGE_TO_HRBP
                    );
                }
                $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-未完成任务!(%s)转岗失败消息提醒发送完毕。', $v['staff_id']), 'info');
                continue;
            }
            //[2]向FBI-HRIS同步转岗信息
            $positionCategory = [];
            $carType          = '';
            if (!empty($v['after_car_type'])) {
                $carType = JobTransferEnums::$car_type_map[$v['after_car_type']] ?? 0;
            }
            //角色: 0-快递分配员 也是有效值
            if (isset($v['after_role_ids'])) {
                $positionCategory = is_string($v['after_role_ids']) && strlen($v['after_role_ids']) > 0
                    ? explode(',', $v['after_role_ids'])
                    : [];
            }

            //由于MY转岗不需要填写车辆来源、用车开始时间，没有转岗后车辆来源、转岗后用车开始时间，
            //但是又要保证hr_staff_info与vehicle_info表数据一致，所以需要传转岗前车辆信息
            if (in_array($v['after_position_id'], $vehicleJobTitle)) {
                $vehicleInfo    = $this->getVehicleDetailByStaffId($v['staff_id']);
                $vehicleSource  = $vehicleInfo['vehicle_source'] ?? 0;
                $vehicleUseDate = $vehicleInfo['vehicle_start_date'] ?? null;
            } else {
                $vehicleSource  = 0;
                $vehicleUseDate = '';
            }
            $params = [
                "staff_info_id"         => $v["staff_id"],
                "department_id"         => $v["after_department_id"],
                "job_title"             => $v["after_position_id"],
                "sys_store_id"          => $v["after_store_id"],
                "operater"              => $v['submitter_id'],
                "direct_manager"        => $v["after_manager_id"],
                "position_category"     => $positionCategory,
                "car_type"              => $carType,
                "vehicle_source"        => $vehicleSource,
                "vehicle_use_date"      => $vehicleUseDate,
                "working_day_rest_type" => $v['after_working_day_rest_type'] ?? 0,
                'type'                  => $v['type'],
                'job_title_grade'       => $v['after_job_title_grade'],
                'hire_type'             => $v['after_hire_type'],
                'hire_times'            => $v['after_hire_times'],
                'vehicle_type_category' => $v['after_car_type'],
            ];
            $this->logger->write_log("转岗参数,参数:" . json_encode($params),'info');
            $_re = $staffObj->syncHrStaff($params);
            if ($_re["result"]['code'] == 1) { //转岗成功

                //[3]更新HC剩余人数、HC招聘状态、转岗状态
                //更新转岗状态
                //更新转岗关联hc的剩余人数
                $this->getDI()->get('db')->begin();
                try {
                    if (empty($v['hc_id'])) {
                        $v['hc_id'] = $hcServer->systemAutoCreateHc($v);
                        $hcServer->deductHc($v['hc_id']);
                    }
                    
                    //生成转岗确认单
                    $confirmationSign = '';
                    if ($v['type'] != JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL &&
                        !in_array($v['after_hire_type'], HrStaffInfoModel::$agentTypeTogether) &&
                        $v['confirm_state'] == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS) {
                        $confirmationSign = $acknowledgeServer
                            ->generateJobTransferPdf(['id' => $v['id'], 'staff_info_id' => $v['staff_id']]);
                    }

                    $updateParams = [
                        "actual_after_date"=> date('Y-m-d'),
                        "after_manager_id" => $v['after_manager_id'],
                        "state"            => JobTransferModel::JOBTRANSFER_STATE_TRANSFERED,
                        'hc_id'            => $v['hc_id'],
                        'confirmation_url' => $confirmationSign,
                    ];
                    if ($v['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                        $updateParams['confirmation_gen_date'] = date('Y-m-d');
                        $updateParams['confirm_state']         = JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM;
                    }

                    //更新转岗状态
                    $_re = $jobTransferRepo->updateJobtransfer([
                        "id"         => $v["id"],
                        "updateData" => $updateParams,
                    ]);
                    $this->getDI()->get('db')->commit();
                    $this->logger->write_log("转岗成功,修改转岗表:" . $_re,"info");
                } catch (\Exception $e) {
                    $this->logger->write_log("转岗失败,问题:" . $e->getMessage());
                    $this->getDI()->get('db')->rollback();
                    continue;
                }
                //实际转岗日期
                $v['actual_after_date'] = date('Y-m-d');

                $this->logger->write_log("转岗成功，工号:" . $v["staff_id"], "info");
                //记录操作日志
                $this->saveOperateLog([
                    'id'            => $v['id'],
                    'staff_info_id' => $operatorId,
                    'operate_id'     => $manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        ? JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        : JobTransferEnums::SYSTEM_AUTO_DO_JOB_TRANSFER,
                    'state'         => JobTransferModel::JOBTRANSFER_STATE_TRANSFERED,
                ]);

                //电子合同入队列
                if (!in_array($v['after_hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
                    $data = ['type' => 'job_transfer', 'staff_info_id' => $v['staff_id'],'job_transfer_id' => $v['id']];
                    $rmq  = new RocketMQ('hr-contract-add');
                    $rid  = $rmq->sendToMsg($data);
                    $this->logger->write_log('hr-contract-transfer rid:' . $rid . 'data:' . json_encode($data), $rid ? 'info' : 'error');
                }

                // 转岗为Bike Courier或Van Courier或Van courier (Project)职位，给该员工发送车辆信息更新提醒
                if (in_array($v['after_position_id'], $vehicleJobTitle)) {
                    $staffObj->sendCourierTransferVehicleMsg($v['staff_id']);
                }

                //[4]发送消息通知
                //给被转岗人发送消息(特殊转岗只给被转岗人发成功消息)
                JobTransferMessageServer::getInstance()->noticeTransferSuccess($v["staff_id"], $v, $transferLanguage,
                    $sendMessageToTransferType
                );

                if ($v['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                    continue;
                }
                //给申请人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferSuccess($v["submitter_id"], $v, $submitterLanguage);

                //给上级发送消息
                JobTransferMessageServer::getInstance()->noticeTransferSuccess($v["after_manager_id"],
                    $v, $managerLanguage,
                    JobTransferMessageServer::SEND_MESSAGE_TO_MANAGER
                );

            } else { //转岗失败
                //[3]更新转岗状态为未转岗
                $this->transferFailed($v['id'], $v['hc_id']);

                //[4]发送消息通知
                $this->logger->write_log("转岗失败，syncHrStaff返回:" . json_encode($_re["result"]),"info");
                $failure_reason = [
                    'zh-CN' => $_re["result"]['msg'] ?? '',
                    'en' => $_re["result"]['msg_en'] ?? '',
                    'th' => $_re["result"]['msg_th'] ?? '',
                ];
                //记录操作日志
                $this->saveOperateLog([
                    'id'             => $v['id'],
                    'staff_info_id'  => $operatorId,
                    'operate_id'     => $manual == JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        ? JobTransferEnums::MANUAL_DO_JOB_TRANSFER
                        : JobTransferEnums::SYSTEM_AUTO_DO_JOB_TRANSFER,
                    'failure_reason' => json_encode($failure_reason),
                    'state'          => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                ]);

                if ($v['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                    continue;
                }

                //给申请人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v['submitter_id'],
                    $submitterLanguage,
                    $v,
                    $failure_reason[$submitterLanguage] ?? $failure_reason['en'],
                    JobTransferMessageServer::SEND_MESSAGE_TO_SUBMITTER
                );
                //给被转岗人发送消息
                JobTransferMessageServer::getInstance()->noticeTransferFail($v['staff_id'],
                    $transferLanguage,
                    $v,
                    $failure_reason[$transferLanguage] ?? $failure_reason['en'],
                    $sendMessageToTransferType
                );

                //给HrBP发送消息
                foreach ($hrBpList as $item) {
                    if (in_array($item, $blackList)) {
                        continue;
                    }
                    $staffLanguage = $staffObj->getLanguage($item);
                    JobTransferMessageServer::getInstance()->noticeTransferFail($item,
                        $staffLanguage,
                        $v,
                        $failure_reason[$staffLanguage] ?? $failure_reason['en'],
                        JobTransferMessageServer::SEND_MESSAGE_TO_HRBP
                    );
                }
                $this->logger->write_log(sprintf('[err]转岗同步数据出现异常-%s!(%s)转岗失败消息提醒发送完毕。',
                    $failure_reason['zh-CN'],
                    $v['staff_id']), 'info');
            }
        }
        return $this->checkReturn([]);
    }

    /**
     * 是否转岗确认单信息变更
     * @param $job_transfer_obj
     * @return int
     * @throws \Exception
     */
    public function isJobTransferConfirmationChanged($job_transfer_obj): int
    {
        return parent::isJobTransferConfirmationChanged($job_transfer_obj);
    }

    protected function resendConfirmation($job_transfer_obj)
    {
        $job_transfer_obj->confirmation_gen_date = date('Y-m-d');
        $job_transfer_obj->confirm_state = JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM;
        $job_transfer_obj->sign_url = '';
        $job_transfer_obj->confirm_content = '';

        //确认日期要保留，作为已经确认过的记号
        //$job_transfer_obj->confirm_date = null;
        $job_transfer_obj->save();
    }

    /**
     * 审批完成回调方法
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     * @throws ValidationException
     * @throws InnerException
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        parent::setProperty($auditId, $state, $extend, $isFinal);
    }

    /**
     * 获取指定职位的车类型
     * @param $job_title_id
     * @return array
     */
    public static function getCarTypeByJobTitle($job_title_id): array
    {
        if (empty($job_title_id)) {
            return [];
        }

        // 获取配置
        $icVehicleTypeData = (new SettingEnvServer())->getSetVal('ic_vehicle_type_ids', ',');

        // 初始化结果数组
        $vehicleTypeList = $result = [];

        foreach ($icVehicleTypeData as $item) {
            // 分割字符串
            [$position, $carType] = explode('/', $item);

            // 如果职位不存在于结果数组中，则初始化一个空数组
            if (!isset($result[$position])) {
                $result[$position] = [];
            }

            // 将车类型ID添加到对应职位的数组中
            $result[$position][] = (int)$carType;
        }

        $vehicleTypes = $result[intval($job_title_id)] ?? [];
        if (empty($vehicleTypes)) {
            return [];
        }

        foreach ($vehicleTypes as $oneType) {
            if(!isset(VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST[$oneType])) {
                continue;
            }
            $vehicleTypeList[] = ['value' => $oneType, 'label' => VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST[$oneType]];
        }
        return $vehicleTypeList;
    }
}