<?php


namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysServer as GlobalBaseServer;

class SysServer extends GlobalBaseServer
{
    /**
     * @description 获取雇佣类型
     * @return array[]
     */
    public function getHireTypesList($job_title = 0 , $store_id = '',$jd_id = 0): array
    {
        $config        = (new SettingEnvServer())->getMultiEnvByCode([
            'lnt_recruitment_job_ids',
            'individual_contractor_job_title',
        ]);
        $LNTJobTitle   = explode(',', $config['lnt_recruitment_job_ids']);
        $agentJobTitle = explode(',', $config['individual_contractor_job_title']);
        $hireTypeEnum = [
            HrStaffInfoModel::HIRE_TYPE_1,
            HrStaffInfoModel::HIRE_TYPE_5,
        ];
        //个人代理可选职位的
        if (in_array($job_title, $agentJobTitle)) {
            $hireTypeEnum = array_merge($hireTypeEnum, HrStaffInfoModel::$agentTypeTogether);
        }
        //lnt 月薪制特殊合同工
        if (in_array($job_title,
                $LNTJobTitle) && (($job_title == enums::$job_title['van_courier'] && $jd_id == HrJdModel::JD_VAN_COURIER_PROJECT) || $job_title != enums::$job_title['van_courier'])) {
            $hireTypeEnum = array_merge($hireTypeEnum, [HrStaffInfoModel::HIRE_TYPE_2]);
        }

        $returnData = [];
        $t = $this->getTranslation();
        foreach ($hireTypeEnum as $k => $v) {
            $returnData[$k]['key']   = intval($v);
            $returnData[$k]['value'] = $t->_('hire_type_' . $v);
        }
        return array_values($returnData);
    }

    /**
     * 马来特殊省份 配置
     * @param $date_at
     * @return array
     */
    public static function getSpecialOffRestDayProvince($date_at = null): array
    {
        if (empty($date_at)) {
            $date_at = date('Y-m-d');
        }

        if ($date_at >= env('special_off_rest_day_province_date', '2025-01-01')) {
            return [
                'MY11',
                'MY09',
                'MY08',
            ];
        }
        return [
            'MY01',
            'MY11',
            'MY09',
            'MY08',
        ];
    }
}