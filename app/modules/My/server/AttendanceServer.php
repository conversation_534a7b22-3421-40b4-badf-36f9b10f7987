<?php
/**
 * Created by <PERSON>p<PERSON>tor<PERSON>.
 * User: nick
 * Date: 2021/9/15
 * Time: 11:25 AM
 */


namespace FlashExpress\bi\App\Modules\My\Server;


use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\CeoMailEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AttendanceWhiteListModel;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2Model;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftHistoryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftV2Model;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceAttachmentModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Enums\AttendanceCalendarEnums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BusinesstripRepository;
use FlashExpress\bi\App\Repository\StaffDeviceInfoRepository;
use FlashExpress\bi\App\Repository\StaffOffDayRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\AttendanceBusinessServer;
use FlashExpress\bi\App\Server\AttendanceImageVerifyServer;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\BusinesstripServer;
use FlashExpress\bi\App\Server\PdpaServer;
use FlashExpress\bi\App\Server\Penalty\AttendancePenaltyServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\AttendanceServer as BaseAttendanceServer;
use FlashExpress\bi\App\Server\SyncServer;
use FlashExpress\bi\App\Server\WhiteListServer;


/**
 * 以前是旧考勤日历的逻辑
 */
class AttendanceServer extends BaseAttendanceServer
{
    public        $timezone;
    /**
     * ToDo 废弃
     * @deprecated
     * 请使用 SysService::getSpecialOffRestDayProvince()代替
     * @var string[]
     */
    public static $special_code = ['MY01', 'MY08', 'MY09', 'MY11'];

    public $fleStaffInfo;//info接口用
    public $hrStaffInfo;//保存接口用
    public $storeInfo;
    public $isSupport          = false;
    public $supportStaffInfo   = [];
    public $attendanceParam    = [];//参数
    public $attendanceInfoBean = null;//info 返回结构 同上注释

    public $shiftInfo;//班次信息 支援那地方也要用一下

    public $fieldPunch = false;//是否有外勤权限
    public $workHome = false;//是否有居家办公权限

    public $range = 200;//默认 打卡 范围
    public $currentTimeStamp;//当前时间戳
    public $currentDateTime;//当前 时区时间
    public $attendanceDate;//考勤日期
    public $attendanceType;//本次点亮的按钮 索引值 1 第一次上班 2 第一次下班 3 第二次上班 4 第二次下班
    public $yesterday;
    public $today;
    public $tomorrow;
    public $holiday;//ph  或者是 休息日


    public $isWorkingDay;//是否 工作日
    public $attendanceObj;//保存 打卡表模型
    public $attendanceBusinessObj;//保存出差打卡 模型

    public $defaultShiftId = 25;
    //主播职位id
    public $liveJobId;
    //请假信息
    public $leaveInfo;

    public $cardInfo;
    public $tripCardInfo;


    //初始化 员工信息 员工支援信息 是否支援 等字段
    protected function initInfoData()
    {
        //当前时间戳
        $this->currentTimeStamp = time();
        //当前时间点
        $this->currentDateTime = date('Y-m-d H:i:s', $this->currentTimeStamp);

        //昨天今天 明天
        $this->today     = date('Y-m-d');
        $this->yesterday = date('Y-m-d', strtotime('-1 day'));
        $this->tomorrow  = date('Y-m-d', strtotime('+1 day'));

        $staffRe = new StaffRepository($this->lang);
        //fle 数据库的 info
        $this->fleStaffInfo = $staffRe->getStaffpositionV2($this->attendanceParam['user_info']['id']);
        //hris
        $this->hrStaffInfo = $staffRe->getStaffPosition($this->attendanceParam['user_info']['id']);
        //员工 网点信息
        $this->fleStaffInfo['store_category'] = 0;

        $liveJobId = (new SettingEnvServer())->getSetVal('free_shift_position',',');
        $this->liveJobId = $liveJobId;


        //返回结构 默认值
        $this->attendanceInfoBean['attendance_date']         = '';
        $this->attendanceInfoBean['shift_info']              = '';
        $this->attendanceInfoBean['leave_tips']              = '';
        $this->attendanceInfoBean['shift_type']              = 0;
        $this->attendanceInfoBean['store_id']                = null;
        $this->attendanceInfoBean['store_lat']               = null;
        $this->attendanceInfoBean['store_lng']               = null;
        $this->attendanceInfoBean['support_staff_info']      = null;
        $this->attendanceInfoBean['more_staff_info_enabled'] = false;
        $this->attendanceInfoBean['mileage_record_enabled']  = false;
        $this->attendanceInfoBean['original_url']            = false;
        $this->attendanceInfoBean['attendance_range']        = 200;
        $this->attendanceInfoBean['is_on_business']          = false;
        $this->attendanceInfoBean['is_go_out']               = false;
        $this->attendanceInfoBean['is_support_field_punch']  = false;
        $this->attendanceInfoBean['is_ic_field_punch']       = false;
        $this->attendanceInfoBean['is_agree_pdpa']           = false;
        $this->attendanceInfoBean['is_name_list']            = true;
        $this->attendanceInfoBean['field_punch']             = false;
        $this->attendanceInfoBean['is_rest']                 = 0;
        $this->attendanceInfoBean['is_mobile_dc']            = false;
        $this->attendanceInfoBean['is_sub_staff']            = intval($this->fleStaffInfo['is_sub_staff']);
        $this->attendanceInfoBean['staff_info_id']           = intval($this->fleStaffInfo['staff_info_id']);

        $this->attendanceInfoBean['button_list'] = [];
    }



    /**
     * @param $param
     * @return mixed
     */
    public function newAttendanceInfo($param)
    {
        //主播自由班次配置
        $this->initFreeShiftConfig();

        $this->attendanceParam = $param;
        $this->logger->write_log("attendance_info input :".json_encode($param), 'info');

        //初始化数据
        $this->initInfoData();

        //是否外勤字段 从device info 迁移过来
        $this->fieldPunch = $this->get_punch_field($this->fleStaffInfo);


        //员工当时在职状态
        $this->attendanceInfoBean['state'] = $this->fleStaffInfo['state'];

        //活体检测 名单
        $this->isNameList();

        //新增是否同意pdad字段 controller 搬过来
        $is_agree_pdpa                             = (new PdpaServer())->getIsAgreePdPa($this->fleStaffInfo['id']);
        $this->attendanceInfoBean['is_agree_pdpa'] = $is_agree_pdpa == 1;//是否同意pada，已经同意true，不同意false

        //考勤信息 attendance_date shift_info shift_type
        $this->getAttendanceTypeInfoList();

        //根据考勤日期 获取支援信息 找打卡做表需要用到
        $this->setSupportInfo();
        //是否居家
        $this->setHomeWorkDate();
        $this->attendanceInfoBean['work_home'] = $this->workHome;
        //支援额外信息
        $this->getSupportReturn();
        //网点信息 和网点类型 一小时限制打卡 和 考勤坐标定位用
        $this->setStoreInfo();

        //获取 是否存在底片
        $this->photoExist();

        //一小时内不能打卡 限制
        $this->attendanceEnable();

        //坐标 有可能是0 在大海的某一处
        $lat = $this->attendanceParam['lat'] ?? 0;
        $lng = $this->attendanceParam['lng'] ?? 0;
        //获取 store_id store_lat store_lng  attendance_range
        $this->getCoordinateInfo($lat, $lng);

        //如果是 支援 需要重新覆盖 外勤字段  马来判断该员工是否选择在支援网点住宿，如果否，不校验打卡地点。需求链接https://flashexpress.feishu.cn/docx/doxcnNqDpMKgqRo2OV8eXHPUy8b?useNewLarklet=1
        if ($this->isSupport && !empty($this->supportInfo)) {
            //选择住宿 https://flashexpress.feishu.cn/docx/doxcng6a13Hjbp61m9Z6amulS3b
            if($this->supportInfo['is_stay'] == HrStaffApplySupportStoreModel::IS_STAY_YES){
                //外勤
                $this->fieldPunch =  $this->attendanceInfoBean['is_support_field_punch'] = $this->boolFieldPunchForSupportStaff($this->attendanceType, $this->supportInfo, $this->attendanceDate,$this->shiftInfo);
            }

            //强制原网点打卡 如果是 下班卡 不校验打卡地点
            if($this->supportInfo['is_original_store'] == HrStaffApplySupportStoreModel::IS_ORIGINAL_STORE
                && $this->attendanceType != StaffWorkAttendanceModel::ATTENDANCE_ON)
            {
                $this->fieldPunch = $this->attendanceInfoBean['is_support_field_punch'] = true;
            }
        }
        //是否 出差外出 前提条件
        $this->checkTripOut();
        //ic特定职位 下班卡开外勤
        $this->makeICPunchOutfieldPunch();
        //里程
        $this->mileageReport();

        $this->attendanceInfoBean['field_punch'] = $this->fieldPunch;

        //雇佣类型
        $this->attendanceInfoBean['hire_type'] = (int)$this->fleStaffInfo['hire_type'];
        // 打卡页面 右上角的问题反馈地址
        $this->attendanceInfoBean['flash_box_url'] = !in_array($this->fleStaffInfo['hire_type'] , HrStaffInfoModel::$agentTypeTogether) ? env('h5_endpoint') . CeoMailEnums::FLASH_BOX_URL : '';

        //返回 是否支援（双方互查）和 子账号支援 显示主账号的请假 休息日信息 https://flashexpress.feishu.cn/wiki/E8yOwxCqWixWmYkPYIGcRdifnJh
        $this->checkSupportFlag($this->fleStaffInfo['staff_info_id'], $this->attendanceDate);
        $leaveInfo = $this->getLeaveInfo($this->masterStaffInfo['staff_info_id'], $this->yesterday, $this->tomorrow);
        if (!empty($this->masterStaffInfo) && !empty($this->shiftInfo)) {
            [
                $this->attendanceInfoBean['shift_info'],
                $this->attendanceInfoBean['leave_tips'],
            ] = $this->makeLeaveTipsShiftInfoV2($this->masterStaffInfo['staff_info_id'],
                $this->masterStaffInfo['job_title'], $this->fleStaffInfo['hire_type'], $this->attendanceDate,
                $this->attendanceInfoBean['button_list'][0]['time_date'] ?? date('Y-m-d H:i:s'),
                $this->shiftInfo[$this->attendanceDate],
                $leaveInfo[$this->attendanceDate] ?? -1);
        }
        $this->attendanceInfoBean['is_support']    = $this->isSupport || $this->isMasterSupport;
        $this->attendanceInfoBean['support_store'] = $this->supportInfo['store_name'] ?? '';

        //如果没有休息请假 最后判断是否居家办公
        if(empty($this->attendanceInfoBean['leave_tips']) && $this->workHome === true){
            $this->attendanceInfoBean['leave_tips'] = $this->getTranslation()->_('work_home_tips');
        }

        return $this->attendanceInfoBean;
    }

    /**
     * @description: 个人代理 下班卡打开外勤打卡
     * @author: AI
     * @date: 2025-01-20 10:30:00
     * @return void
     */
    public function makeICPunchOutfieldPunch()
    {
        if (in_array($this->fleStaffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            $punch_out_no_limit_position = (new SettingEnvServer())->getSetVal('punch_out_nolimit_position', ',');
            if (!in_array($this->fleStaffInfo['job_title'], $punch_out_no_limit_position)) {
                return;
            }
            //单班次的，没打上班 不开外勤
            if (empty($this->attendanceInfoBean['button_list'][0]['time_date'])) {
                return;
            }
            //多班次的 打了第一个下班 但是没打第二个上班 不开外勤
            if (!empty($this->attendanceInfoBean['button_list'][1]['time_date']) && empty($this->attendanceInfoBean['button_list'][2]['time_date'])) {
                return;
            }
            $this->attendanceInfoBean['is_ic_field_punch'] = true;
        }
    }


    //根据班次获取 n次 上下班 对应信息 以及当前时间点 打卡的索引值等
    protected function getAttendanceTypeInfoList()
    {
        //hr_staff_shift_v2  关联  hr_shift_v2  和 hr_shift_v2_extend
        $today     = $this->today;
        $yesterday = $this->yesterday;
        $tomorrow  = $this->tomorrow;

        $dateList = [$yesterday, $today, $tomorrow];

        //3天的 班次信息 昨天 今天 明天
        $shiftServer = new AttendanceCalendarV2Server($this->lang, $this->timezone);
        $shiftServer->liveJobId = $this->liveJobId;
        $shiftInfo   = $shiftServer->getDailyShiftInfo($this->fleStaffInfo['id'], $yesterday, $tomorrow);
        if($this->fleStaffInfo['formal'] == HrStaffInfoModel::FORMAL_0){
            $shiftInfo = $this->getOldStaffShift($this->fleStaffInfo['id'],$yesterday,$tomorrow);
        }

        //没有明天的班次 用默认班次补齐 不管是不是支援
        if(empty($shiftInfo[$this->tomorrow]) && $this->fleStaffInfo['formal'] != HrStaffInfoModel::FORMAL_0 &&  !empty($this->hrStaffInfo)){
            $shiftInfo[$this->tomorrow] = $this->getDefaultShiftById($this->defaultShiftId,$this->tomorrow);
        }

        //给别的地方用
        $this->shiftInfo = $shiftInfo;

        //3天的 打卡信息
        $attendanceRe = new AttendanceRepository($this->lang, $this->timezone);
        $cardInfo     = $attendanceRe->getAttendanceData($this->fleStaffInfo['id'], $dateList);
        $tripCardInfo = $attendanceRe->getTripAttendanceData($this->fleStaffInfo['id'], $dateList);

        //如果是主播 班次 给空
        if(in_array($this->fleStaffInfo['job_title'], $this->liveJobId)){
            $this->shiftInfo = $shiftInfo = [];
        }

        //3天的 请假信息 只要 审核通过
        $leaveInfo = $this->getLeaveInfo($this->fleStaffInfo['id'], $yesterday, $tomorrow);
//        $auditRe                        = new AuditRepository($this->lang);
//        $leaveParam['staff_id']         = $this->fleStaffInfo['id'];
//        $leaveParam['leave_start_time'] = $yesterday;
//        $leaveParam['leave_end_time']   = $tomorrow;
//        $leaveInfo                      = $auditRe->getLeaveData($leaveParam, enums::APPROVAL_STATUS_APPROVAL);
//        $leaveInfo                      = empty($leaveInfo) ? [] : array_column($leaveInfo, 'sum_type', 'date_at');

        //没班次 返回灰色打卡按钮 不能报错 没班次的
        if (empty($shiftInfo)) {
            $this->attendanceInfoBean['shift_type'] = HrShiftV2ExtendModel::SHIFT_TYPE_ONCE;
            $this->attendanceDate                   = $this->attendanceInfoBean['attendance_date'] = $today;
            $this->formatAttendanceNoShift($cardInfo, $tripCardInfo);
            //主播 班次显示
            if(in_array($this->fleStaffInfo['job_title'], $this->liveJobId)){
                //用上班打卡时间判断 他上的是早班还是晚班
                [$this->attendanceInfoBean['shift_info'],$this->attendanceInfoBean['leave_tips']] = $this->makeLeaveTipsShiftInfoV2($this->fleStaffInfo['id'],
                    $this->fleStaffInfo['job_title'], $this->fleStaffInfo['hire_type'], $this->attendanceDate,
                    $this->attendanceInfoBean['button_list'][0]['time_date'] ?? date('Y-m-d H:i:s'),
                    [],
                    $leaveInfo[$this->attendanceDate] ?? -1);
            }
//            $this->defaultButtonList();
            return;
        }

        //ph 公休日 和休息日
        $leaveServer   = new LeaveServer($this->lang, $this->timezone);
        $this->holiday = $leaveServer->staff_off_days($this->fleStaffInfo['id'], $yesterday, $tomorrow);
        $this->attendanceDate = $today;//默认 今天
        foreach ($dateList as $date) {
            //如果 是昨天 并且没有班次 说明 新入职的 跳过
            if ($date == $yesterday && empty($shiftInfo[$date])) {
                //新入职 员工 跳过昨天 日志
                continue;
            }

            $flag = $this->checkDateAttendance($date, $shiftInfo, $cardInfo, $tripCardInfo, $leaveInfo);
            if ($flag === true) {
                $this->attendanceDate = $date;
                break;
            }
        }


        $this->attendanceInfoBean['attendance_date'] = $this->attendanceDate;
        $this->attendanceInfoBean['shift_type']      = $shiftInfo[$this->attendanceDate]['shift_type'];
        [$this->attendanceInfoBean['shift_info'],$this->attendanceInfoBean['leave_tips']]      = $this->makeLeaveTipsShiftInfoV2($this->fleStaffInfo['id'],
            $this->fleStaffInfo['job_title'], $this->fleStaffInfo['hire_type'], $this->attendanceDate,
            $this->attendanceInfoBean['button_list'][0]['time_date'] ?? date('Y-m-d H:i:s'),
            $shiftInfo[$this->attendanceDate],
            $leaveInfo[$this->attendanceDate] ?? -1);
        $this->attendanceInfoBean['is_rest']         = 0;
        //请假 上午 ｜｜ 下午 ｜｜ 全天
        if(isset($leaveInfo[$this->attendanceDate])){
            //全天
            if(in_array($leaveInfo[$this->attendanceDate],[StaffAuditLeaveSplitModel::SPLIT_TYPE_0,StaffAuditLeaveSplitModel::SPLIT_TYPE_WHOLE])){
                $this->attendanceInfoBean['is_rest'] = HrOvertimeModel::TIME_TYPE_WHOLE;
            }
            //请上午半天假
            if($leaveInfo[$this->attendanceDate] == StaffAuditLeaveSplitModel::SPLIT_TYPE_1){
                $this->attendanceInfoBean['is_rest'] = HrOvertimeModel::TIME_TYPE_AM;
            }
            //下午半天
            if($leaveInfo[$this->attendanceDate] == StaffAuditLeaveSplitModel::SPLIT_TYPE_2){
                $this->attendanceInfoBean['is_rest'] = HrOvertimeModel::TIME_TYPE_PM;
            }
        }
        //判断 是否是休息日 或者轮休
        if(in_array($this->attendanceDate,$this->holiday)){
            $this->attendanceInfoBean['is_rest'] = HrOvertimeModel::TIME_TYPE_WHOLE;
        }

        $this->formatAttendanceTypeInfo($shiftInfo, $cardInfo, $tripCardInfo);
    }


    /**
     * 设置请假和班次的tips
     * @param $staff_id
     * @param $job_title
     * @param $hire_type
     * @param $date_at
     * @param $attendance_started_at
     * @param $shiftInfo
     * @param $leaveInfo
     * @return array
     */
    public function makeLeaveTipsShiftInfoV2(
        $staff_id,
        $job_title,
        $hire_type,
        $date_at,
        $attendance_started_at,
        $shiftInfo,
        $leaveInfo
    ): array {
        $isOffRest = (new StaffOffDayRepository($this->lang, $this->timezone))->checkStaffIsOffRest($staff_id,
            $date_at);
        $t         = $this->getTranslation();
        $date      = $date_at;
        $shift_str = $leave_tips = '';
        //是否主播
        if (in_array($job_title, $this->free_shift_position)) {
            $hour = empty($attendance_started_at) ? null : date('H', strtotime($attendance_started_at));
            //日期是今天 返回当前时间的 如果不是今天 并且没打上班卡 就显示空
            if ($date == date('Y-m-d') && empty($hour)) {
                $hour = date('H');
            }

            $dayHour   = $this->free_shift_day_shift_duration;
            $nightHour = $this->free_shift_night_shift_duration;
            //4-16点 白班
            if ($hour >= 4 && $hour < 16) {
                $shift_hour     = $dayHour;
                $live_shift_key = 'live_day_shift';
            } else {
                $shift_hour     = $nightHour;
                $live_shift_key = 'live_night_shift';
            }
            $shift_str = $t->_($live_shift_key, ['hour' => $shift_hour]);

            // 优先检查请假状态，然后检查休息日状态
            switch ($leaveInfo) {
                case 0://请全天假 //todo 马来的是通过其他方法获取的，所以0也是代表请全天假
                case 3://请全天假
                    $shift_str  = $t->_($live_shift_key, ['hour' => $shift_hour]);
                    $leave_tips = $t->_('att_show_shift_all_day_leave');
                    break;
                case 2://请后半天假
                    $shift_str  = $t->_($live_shift_key, ['hour' => $shift_hour / 2]);
                    $leave_tips = $t->_('att_show_shift_2st_half_day_leave');
                    break;
                case 1://请前半天假
                    $shift_str  = $t->_($live_shift_key, ['hour' => $shift_hour / 2]);
                    $leave_tips = $t->_('att_show_shift_1st_half_day_leave');
                    break;
                default:
                    // 没有请假时，再检查是否为休息日
                    if ($isOffRest) {
                        if ($isOffRest == HrStaffWorkDayModel::TYPE_OFF) {
                            $leave_tips = $t->_('att_show_shift_off_day');
                        } else {
                            $leave_tips = $t->_('att_show_shift_rest_day');
                        }
                    }
                    break;
            }
            return [empty($hour) ? '' :$shift_str, $leave_tips];
        }

        if (!empty($shiftInfo)) {
            $shift_str = "{$shiftInfo['first_start']}-{$shiftInfo['first_end']}";
            if ($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
                $shift_str .= ",{$shiftInfo['second_start']}-{$shiftInfo['second_end']}";
            }

            $att_show_shift_off_day            = 'att_show_shift_off_day';
            $att_show_shift_rest_day           = 'att_show_shift_rest_day';
            $att_show_shift_all_day_leave      = 'att_show_shift_all_day_leave';
            $att_show_shift_2st_half_day_leave = 'att_show_shift_2st_half_day_leave';
            $att_show_shift_1st_half_day_leave = 'att_show_shift_1st_half_day_leave';

            if (in_array($hire_type, HrStaffInfoModel::$agentTypeTogether)) {
                $att_show_shift_off_day            = 'att_show_shift_off_day_agent';
                $att_show_shift_rest_day           = 'att_show_shift_rest_day_agent';
                $att_show_shift_all_day_leave      = 'att_show_shift_all_day_leave_agent';
                $att_show_shift_2st_half_day_leave = 'att_show_shift_2st_half_day_leave_agent';
                $att_show_shift_1st_half_day_leave = 'att_show_shift_1st_half_day_leave_agent';
            }
            // 优先检查请假状态，然后检查休息日状态
            switch ($leaveInfo) {
                case 0://todo 马来的是通过其他方法获取的，所以0也是代表请全天假
                case 3://（请假-全天，无需打卡）
                    $leave_tips = $t->_($att_show_shift_all_day_leave);
                    break;
                case 2://（请假-后半天)
                    if ($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
                        $shift_str = "{$shiftInfo['first_start']}-{$shiftInfo['first_end']}";
                    } else {
                        if (!empty($shiftInfo['is_middle_rest'])) {
                            $shift_str = "{$shiftInfo['first_start']}-{$shiftInfo['middle_rest_start']}";
                        } else {
                            if ($shiftInfo['first_start'] > $shiftInfo['first_end']) {
                                $m = strtotime($date . ' ' . $shiftInfo['first_end'] . ' +1 day') - strtotime($date . ' ' . $shiftInfo['first_start']);
                            } else {
                                $m = strtotime($date . ' ' . $shiftInfo['first_end']) - strtotime($date . ' ' . $shiftInfo['first_start']);
                            }
                            $s         = $m / 2;
                            $shift_end = date('H:i',
                                strtotime($date . ' ' . $shiftInfo['first_start']) + $s);
                            $shift_str = "{$shiftInfo['first_start']}-$shift_end";
                        }
                    }
                    $leave_tips = $t->_($att_show_shift_2st_half_day_leave);
                    break;
                case 1://（请假-前半天）
                    if ($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
                        $shift_str = "{$shiftInfo['second_start']}-{$shiftInfo['second_end']}";
                    } else {
                        if (!empty($shiftInfo['is_middle_rest'])) {
                            $shift_str = "{$shiftInfo['middle_rest_end']}-{$shiftInfo['first_end']}";
                        } else {
                            if ($shiftInfo['first_start'] > $shiftInfo['first_end']) {
                                $m = strtotime($date . ' ' . $shiftInfo['first_end'] . ' +1 day') - strtotime($date . ' ' . $shiftInfo['first_start']);
                            } else {
                                $m = strtotime($date . ' ' . $shiftInfo['first_end']) - strtotime($date . ' ' . $shiftInfo['first_start']);
                            }
                            $s           = $m / 2;
                            $shift_start = date('H:i',
                                strtotime($date . ' ' . $shiftInfo['first_start']) + $s);

                            $shift_str = "$shift_start-{$shiftInfo['first_end']}";
                        }
                    }

                    $leave_tips = $t->_($att_show_shift_1st_half_day_leave);
                    break;
                default:
                    // 没有请假时，再检查是否为休息日
                    if ($isOffRest) {
                        if ($isOffRest == HrStaffWorkDayModel::TYPE_OFF) {
                            $leave_tips = $t->_($att_show_shift_off_day);
                        } else {
                            $leave_tips = $t->_($att_show_shift_rest_day);
                        }
                    }
                    break;
            }
        }
        return [$shift_str, $leave_tips];
    }


    //根据当前时间 和班次配置 判定 打卡日期
    protected function checkDateAttendance($date, $shiftInfo, $cardInfo, $tripCartInfo, $leaveInfo)
    {
        $thisDate = $date;
        $nextDate = date('Y-m-d', strtotime("{$thisDate} +1 day"));

        if (empty($shiftInfo[$thisDate])) {
            if($date == date('Y-m-d')){
               // return true;
            }
            throw new ValidationException($this->getTranslation()->_('no_shift_notice_day',['day'=>$thisDate]));
        }


        //判定日期 是 昨天 今天 对应 今天或者明天 班次丢失 报错
        if ($date < $this->tomorrow && empty($shiftInfo[$nextDate])) {
            throw new ValidationException($this->getTranslation()->_('no_shift_notice_day',['day'=>$nextDate]));
        }

        //是否请假
        $leaveFlag = isset($leaveInfo[$thisDate]) && in_array($leaveInfo[$thisDate],[StaffAuditLeaveSplitModel::SPLIT_TYPE_0,StaffAuditLeaveSplitModel::SPLIT_TYPE_2,StaffAuditLeaveSplitModel::SPLIT_TYPE_WHOLE]);

        //是否在 当前日期 最早最晚区间
        $isBetween = $this->checkShiftBetween($shiftInfo, $thisDate);
        //是否在下一个区间 只有 昨天和今天 才有下一个区间
        if($thisDate < $this->tomorrow){
            $nextFlag = $this->checkShiftBetween($shiftInfo, $nextDate);

            $thisStartLimit = $shiftInfo[$thisDate]['first_allow_attendance_time'];
            //下一个日期的 最早打卡时间
            $nextStartLimit = $shiftInfo[$nextDate]['first_allow_attendance_time'];

            /**
             * 特殊定制 切换时间点不同 所以要额外判断下
            1、未打第二个班次的下班卡时，超过第二个班次的最晚可打下班卡时间，切换为第二天的考勤日期；     切换点是 当天的 最晚可打卡时间
            2、打了第二个班次的下班卡时，到达第二天的最早可打上班卡时间，切换为第二天的考勤日期          切换点是 第二天的最早可打卡时间
            3、若前一天是休息日或法定节假日或有审批通过的请假时，无论是否打前一天的下班卡，切考勤日期的逻辑为：到达当天最早可打上班卡时间 切为当天的考勤日期
             */
            //昨天或今天 日期 并且不在昨天或今天区间 根据下班卡判定 考勤日期
            if($isBetween === false){
                //如果 没在区间 但是 在当前区间开始时间之前 返回当前日期
                if($this->currentDateTime < $thisStartLimit){
                    return true;
                }

                //看本次日期 打没打下班卡
                $cardKey = "{$thisDate}_".StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;
                if($shiftInfo[$thisDate]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE){
                    $cardKey = "{$thisDate}_".StaffWorkAttendanceModel::SHIFT_TYPE_SECOND;
                }

                $next = $nextFlag === false && $this->currentDateTime < $nextStartLimit;

                //如果有下班卡 要在 第二天的可打卡时间 再切 所以 没到 要返回本次日期
                if((!empty($cardInfo[$cardKey]['end_at']) || !empty($tripCartInfo[$cardKey]['end_at'])) && $next){
                    $this->logger->write_log("checkDateAttendance_{$this->fleStaffInfo['id']} 打了下班卡 要到第二天可打卡时间点再切 现在还没到", 'info');
                    return true;
                }

                //若前一天是休息日或法定节假日或有审批通过的请假时，无论是否打前一天的下班卡，切考勤日期的逻辑为：到达当天最早可打上班卡时间 切为当天的考勤日期  切换时间点 第二天最早可打卡
                if ($next && $leaveFlag) {
                    $this->logger->write_log("checkDateAttendance_{$this->fleStaffInfo['id']} 有请假记录 要到第二天可打卡时间点再切 现在还没到", 'info');
                    return true;
                }

                if ($next && in_array($thisDate, $this->holiday)) {
                    $this->logger->write_log("checkDateAttendance_{$this->fleStaffInfo['id']} {$thisDate}是ph 要到第二天可打卡时间点再切 现在还没到", 'info');
                    return true;
                }
            }
        }

        //已经是明天 不需要判定 后面的 直接返回明天
        if ($thisDate == $this->tomorrow) {
            return true;
        }

        //不在 本次区间
        if ($isBetween === false) {
            return false;
        }

        //不在 下一天 区间 并且在本次区间
        if (empty($nextFlag)) {
            return true;
        }

        //!!!!下面的情况为 在当前日期范围 又在下个日期范围 有交集的情况
        //一次班
        if ($shiftInfo[$thisDate]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE) {
            //在昨天区间 又在 今天区间 前一天 最后一个 下班卡 有记录 返回 false
            $cardKey = "{$thisDate}_".StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;
            if (!empty($cardInfo[$cardKey]['end_at'])) {
                return false;
            }
            //出差打卡 有没有记录
            if (!empty($tripCartInfo[$cardKey]['end_at'])) {
                return false;
            }
        }

        //两次班
        if ($shiftInfo[$thisDate]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
            //在昨天区间 又在 今天区间 前一天 最后一个 下班卡 有记录 返回 false
            $cardKey = "{$thisDate}_".StaffWorkAttendanceModel::SHIFT_TYPE_SECOND;
            if (!empty($cardInfo[$cardKey]['end_at'])) {
                return false;
            }
            //出差打卡 有没有记录
            if (!empty($tripCartInfo[$cardKey]['end_at'])) {
                return false;
            }
        }

        //没打卡记录 但是有请假  并且 请的 是 全天 或者 下午假 也返回 false
        if (isset($leaveInfo[$thisDate]) && $leaveFlag) {
            return false;
        }

        if (in_array($thisDate, $this->holiday)) {
            return false;
        }

        return true;
    }



    //没班次的人 默认返回灰色按钮
    public function defaultButtonList()
    {
        $row   = [];
        $limit = enums::ATTENDANCE_BUTTON_NUM_ONE;
        for ($i = 0; $i < $limit; $i++) {
            $index                       = $i + 1;//按钮序号
            $row[$i]['shift_index']      = $index;//回传用
            $row[$i]['time_stamp']       = null;
            $row[$i]['time_date']        = null;
            $row[$i]['shift_time_stamp'] = null;
            $row[$i]['shift_time']       = '';
            //判断 按钮 显示什么状态 默认灰色
            $row[$i]['state'] = enums::$attendanceButtonState['button_grey'];
            //如果已经打卡 判断 是迟到还是早退 默认 正常
            $row[$i]['abnormal_flag'] = enums::ATTENDANCE_BUTTON_NORMAL;
            //新增 最早 可打卡时间 上班按钮 计时器判断用
            $row[$i]['shift_early_time_stamp'] = null;
        }
        $this->attendanceInfoBean['button_list'] = $row;
    }


    /**
     *
     * 要返回信息
     * $row[0]['shift_index'] = 1;//班次打卡类型回传用 1 第一班次上 2 第一班次下 3 第二班次上 4 第二班次下 以后新增5，6。。。递增
     * $row[0]['time_stamp'] = 1669277674;//时间戳 已经打卡了的时间 对应原来的 started_at 类似
     * $row[0]['time_date'] = '2021-08-10 12:15:02.000';//时间展示 ymd 对应原来的 start_data 也是 零时区数据库一致
     * $row[0]['shift_time_stamp'] = 1669277674;//班次对应的 打卡时间戳 判断按钮是否迟到打卡用
     * $row[0]['shift_time'] = '09:00';//没打卡 展示班次用
     * $row[0]['state'] = 3;//1可打卡（未打卡 4个只有一个是1） 2 已打卡 3 缺卡 4 灰色（除了123 之外的）
     * $row[0]['abnormal_flag'] = 0;//非正常考勤标签 迟到早退用 (只针对已经有打卡的展示) 0 正常不展示标签 1 迟到 2 早退
     * @param $shiftInfo
     * @param $cardInfo
     * @param $tripCardInfo
     */
    public function formatAttendanceTypeInfo($shiftInfo, $cardInfo, $tripCardInfo)
    {
        $limit = enums::ATTENDANCE_BUTTON_NUM_ONE;
        if ($shiftInfo[$this->attendanceDate]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
            $limit = enums::ATTENDANCE_BUTTON_NUM_TWO;
        }

        $addHour = $this->config->application->add_hour;
        $lagTime = $addHour * 3600;

        //是否休息标记 判断 是否显示迟到早退 0 不休息 1 上午休息 2 下午休息 3 全天休息
        $isRest = $this->attendanceInfoBean['is_rest'];

        $row        = [];
        $buttonFlag = false;//只有一个 点亮
        $earlyFlag  = false;//离当前时间 最近的一个 上班卡 虽然是灰色 但是要告诉用户 这个马上能打卡
        for ($i = 0; $i < $limit; $i++) {
            $index                  = $i + 1;//按钮序号
            $row[$i]['shift_index'] = $index;//回传用

            $cardKey = "{$this->attendanceDate}_".StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;//默认 只有一次 上下班的key 2022-12-12_0
            if ($limit == enums::ATTENDANCE_BUTTON_NUM_TWO) {
                $times   = ceil($index / 2);//获取 第一次 1 第二次 2 （只有 双班次的 第一次和第二次类型）
                $cardKey = "{$this->attendanceDate}_{$times}";//2022-12-12_1 或者 2022-12-12_2
            }

            $shiftTimes = $index % 2;//班次取字段系数 大于0(1,3,..) 上班的 等于0(2,4...) 取下班的

            //取 上班时间 还是 下班时间 字段名
            $cardIndexName         = $shiftTimes > 0 ? 'started_at' : 'end_at';

            //取 打卡记录 或者 出差打卡记录 打卡记录优先
            $cardTime = empty($cardInfo[$cardKey][$cardIndexName]) ? null : $cardInfo[$cardKey][$cardIndexName];
            if (empty($cardTime)) {
                $cardTime = empty($tripCardInfo[$cardKey][$cardIndexName]) ? null : $tripCardInfo[$cardKey][$cardIndexName];
            }

            $row[$i]['time_stamp'] = empty($cardTime) ? null : (strtotime($cardTime) + $lagTime);
            $row[$i]['time_date']  = empty($cardTime) ? null : date('Y-m-d H:i:s',strtotime($cardTime) + $lagTime);

            //取 上班 下班 以及 一次 二次 班次相关字段名
            $shiftIndexName = $shiftTimes > 0 ? 'first_check_late_in_time' : 'first_check_early_out_time';
            $shiftShowName  = $shiftTimes > 0 ? 'first_start' : 'first_end';
            $startShiftCheckName = 'first_allow_attendance_time';
            $endShiftCheckName = 'first_check_no_record_time';
            if ($index > enums::ATTENDANCE_BUTTON_NUM_ONE) {
                $shiftIndexName = $shiftTimes > 0 ? 'second_check_late_in_time' : 'second_check_early_out_time';
                $shiftShowName  = $shiftTimes > 0 ? 'second_start' : 'second_end';
                $startShiftCheckName = 'second_allow_attendance_time';
                $endShiftCheckName = 'second_check_no_record_time';
            }

            //如果是休息 上半天情况 一次班
            if($isRest == HrOvertimeModel::TIME_TYPE_AM && $limit == enums::ATTENDANCE_BUTTON_NUM_ONE){
                $shiftIndexName = $shiftTimes > 0 ? 'up_leave_check_late_in_time' : 'up_leave_check_early_out_time';
            }

            //休息上半天 两次班
            if($isRest == HrOvertimeModel::TIME_TYPE_AM && $limit == enums::ATTENDANCE_BUTTON_NUM_TWO){
                //第一个班的 两个按钮 不判断  $shiftIndexName 为空
                if ($index <= enums::ATTENDANCE_BUTTON_NUM_ONE){
                    $shiftIndexName = '';
                }else{
                    $shiftIndexName = $shiftTimes > 0 ? 'up_leave_check_late_in_time' : 'up_leave_check_early_out_time';
                }
            }

            //如果是休息 下半天情况 一次班
            if($isRest == HrOvertimeModel::TIME_TYPE_PM && $limit == enums::ATTENDANCE_BUTTON_NUM_ONE){
                $shiftIndexName = $shiftTimes > 0 ? 'down_leave_check_late_in_time' : 'down_leave_check_early_out_time';
            }

            //休息下半天 两次班
            if($isRest == HrOvertimeModel::TIME_TYPE_PM && $limit == enums::ATTENDANCE_BUTTON_NUM_TWO){
                //第二个班的 两个按钮 不判断  $shiftIndexName 为空
                if ($index > enums::ATTENDANCE_BUTTON_NUM_ONE){
                    $shiftIndexName = '';
                }else{
                    $shiftIndexName = $shiftTimes > 0 ? 'down_leave_check_late_in_time' : 'down_leave_check_early_out_time';
                }
            }

            //泰国时区 要转化成零时区
            $row[$i]['shift_time_stamp'] = empty($shiftIndexName) ? null : strtotime($shiftInfo[$this->attendanceDate][$shiftIndexName]);//班次 上班-迟到时间节点 下班-早退时间节点
            //如果全天休息 不判断 迟到早退
            if($isRest == HrOvertimeModel::TIME_TYPE_WHOLE){
                $row[$i]['shift_time_stamp'] = null;
            }
            $row[$i]['shift_time']       = $shiftInfo[$this->attendanceDate][$shiftShowName] ?? '';

            //判断 按钮 显示什么状态 默认灰色
            $row[$i]['state'] = enums::$attendanceButtonState['button_grey'];
            //如果已经打卡 判断 是迟到还是早退 默认 正常
            $row[$i]['abnormal_flag'] = enums::ATTENDANCE_BUTTON_NORMAL;
            //新增 最早 可打卡时间 上班按钮 计时器判断用
            $row[$i]['shift_early_time_stamp'] = empty($shiftInfo[$this->attendanceDate][$startShiftCheckName]) ? null : strtotime($shiftInfo[$this->attendanceDate][$startShiftCheckName]);//最早可打卡时间

            //已打卡 更新状态 判断迟到早退
            if (!empty($row[$i]['time_stamp'])) {
                $row[$i]['state'] = enums::$attendanceButtonState['button_had'];
                //上班卡 判断迟到
                if ($shiftTimes > 0 && !empty($row[$i]['shift_time_stamp']) && $row[$i]['time_stamp'] >= $row[$i]['shift_time_stamp'] && $isRest != HrOvertimeModel::TIME_TYPE_WHOLE) {
                    $row[$i]['abnormal_flag'] = enums::ATTENDANCE_BUTTON_LATE;
                }
                //下班卡 判断早退
                if ($shiftTimes == 0 && !empty($row[$i]['shift_time_stamp']) && $row[$i]['time_stamp'] <= $row[$i]['shift_time_stamp'] && $isRest != HrOvertimeModel::TIME_TYPE_WHOLE) {
                    $row[$i]['abnormal_flag'] = enums::ATTENDANCE_BUTTON_EARLY;
                }
            } else {//没打卡 判断时间区间 是否符合 决定 状态是 点亮还是 缺卡
                /**
                 * 第一班次 上班 最早可打卡时间节点 first_allow_attendance_time
                 * 第一班次 下班 最晚可打卡时间节点 first_check_no_record_time
                 *
                 * 第二班次 上班 最早 second_allow_attendance_time
                 * 第二班次 下班 最晚 second_check_no_record_time
                 */
                //离当前时间 最近的 一个 上班卡
                if(!$buttonFlag && $this->currentDateTime < $shiftInfo[$this->attendanceDate][$startShiftCheckName] && !$earlyFlag && $shiftTimes > 0){
                    $row[$i]['state'] = enums::$attendanceButtonState['button_click_soon'];
                    $earlyFlag = true;
                }

                //过了 最晚可打卡 标记缺卡 要判断 两次班 请半天假的情况 不显示缺卡
                if($this->currentDateTime > $shiftInfo[$this->attendanceDate][$endShiftCheckName] && $isRest != HrOvertimeModel::TIME_TYPE_WHOLE){
                    //2次班 上午休息 前两个按钮 不判断缺卡  ｜｜ 2次班 下午休息 后两个按钮不判断缺卡
                    $missFlag = true;//true  默认 判断缺卡 false 跳过本次按钮缺卡判断
                    if($limit == enums::ATTENDANCE_BUTTON_NUM_TWO && $isRest > 0){
                        //判断上午休息 前两个按钮
                        if($isRest == HrOvertimeModel::TIME_TYPE_AM && $index <= enums::ATTENDANCE_BUTTON_NUM_ONE){
                            $missFlag = false;
                        }
                        //判断下午休息  后两个按钮
                        if($isRest == HrOvertimeModel::TIME_TYPE_PM && $index > enums::ATTENDANCE_BUTTON_NUM_ONE){
                            $missFlag = false;
                        }
                    }
                    if($missFlag){
                        $row[$i]['state'] = enums::$attendanceButtonState['button_missed'];
                        $row[$i]['abnormal_flag'] = enums::ATTENDANCE_BUTTON_MISS;
                    }
                }

                $betweenFlag = $this->currentDateTime >= $shiftInfo[$this->attendanceDate][$startShiftCheckName] && $this->currentDateTime <= $shiftInfo[$this->attendanceDate][$endShiftCheckName];
                //如果 按钮没亮 并且 在可打卡范围 点亮按钮
                if(!$buttonFlag && $betweenFlag){
                    $row[$i]['state'] = enums::$attendanceButtonState['button_could'];
                    $buttonFlag = true;//已经点亮
                    $this->attendanceType = $index;
                }

                $this->logger->write_log("buttonList {$this->attendanceDate}_{$index} current {$this->currentDateTime} start {$shiftInfo[$this->attendanceDate][$startShiftCheckName]} end {$shiftInfo[$this->attendanceDate][$endShiftCheckName]} ", 'info');
            }
        }

        $this->attendanceInfoBean['button_list'] = $row;
    }

    //没有班次信息的 打卡
    protected function formatAttendanceNoShift($cardInfo, $tripCardInfo)
    {
        $limit   = enums::ATTENDANCE_BUTTON_NUM_ONE;
        $addHour = $this->config->application->add_hour;
        $lagTime = $addHour * 3600;

        //特殊判断 有下班卡 就全灰
        $cardKey = "{$this->attendanceDate}_".StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;//默认 只有一次 上下班的key 2022-12-12_0
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $yesterdayCardKey = "{$yesterday}_".StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;
        //如果昨天有上班 没下班 看是否超过22小时 判断是否切今天
        if(!empty($cardInfo[$yesterdayCardKey]['started_at']) && empty($cardInfo[$yesterdayCardKey]['end_at'])){
            //昨天上班打卡 + 22 小时
            $yesterdayCard = date('Y-m-d H:i',strtotime($cardInfo[$yesterdayCardKey]['started_at']) + $lagTime + (22 * 3600));
            $limitDate = date('Y-m-d H:i');
            //没超过 22小时 要切昨天
            if($yesterdayCard > $limitDate){
                $cardKey = $yesterdayCardKey;
                $this->attendanceDate                   = $this->attendanceInfoBean['attendance_date'] = $yesterday;
            }
        }

        $buttonFlag = false;
        $row = [];
        for ($i = 0; $i < $limit; $i++) {
            $index                  = $i + 1;//按钮序号
            $row[$i]['shift_index'] = $index;//回传用

            $shiftTimes = $index % 2;//班次取字段系数 大于0(1,3,..) 上班的 等于0(2,4...) 取下班的
            //取 上班时间 还是 下班时间 字段名
            $cardIndexName         = $shiftTimes > 0 ? 'started_at' : 'end_at';

            $cardTime = empty($cardInfo[$cardKey][$cardIndexName]) ? null : $cardInfo[$cardKey][$cardIndexName];
            if (empty($cardTime)) {
                $cardTime = empty($tripCardInfo[$cardKey][$cardIndexName]) ? null : $tripCardInfo[$cardKey][$cardIndexName];
            }

            $row[$i]['time_stamp'] = empty($cardTime) ? null : (strtotime($cardTime) + $lagTime);
            $row[$i]['time_date']  = empty($cardTime) ? null : date('Y-m-d H:i:s',strtotime($cardTime) + $lagTime);

            //泰国时区 要转化成零时区
            $row[$i]['shift_time_stamp'] = null;
            $row[$i]['shift_time']       = '';

            //判断 按钮 显示什么状态 默认灰色
            $row[$i]['state'] = enums::$attendanceButtonState['button_grey'];
            //如果已经打卡 判断 是迟到还是早退 默认 正常
            $row[$i]['abnormal_flag'] = enums::ATTENDANCE_BUTTON_NORMAL;
            //新增 最早 可打卡时间 上班按钮 计时器判断用
            $row[$i]['shift_early_time_stamp'] = null;

            //已打卡 更新状态 判断迟到早退
            if (!empty($row[$i]['time_stamp'])) {
                $row[$i]['state'] = enums::$attendanceButtonState['button_had'];
            } else {//没打卡 判断时间区间 是否符合 决定 状态是 点亮还是 缺卡
                if(!$buttonFlag){
                    $buttonFlag = true;
                    $row[$i]['state']     = enums::$attendanceButtonState['button_could'];
                }
                $this->attendanceType = $index;
            }
        }
        $this->attendanceInfoBean['button_list'] = $row;
    }



    //判定当前时间 是否在 指定日期的 班次区间
    protected function checkShiftBetween($shiftInfo, $date)
    {
        $start = $end = '';
        if ($shiftInfo[$date]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE) {
            $start = $shiftInfo[$date]['first_allow_attendance_time'];
            $end = $shiftInfo[$date]['first_check_no_record_time'];
        }

        if ($shiftInfo[$date]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
            $start = $shiftInfo[$date]['first_allow_attendance_time'];
            $end = $shiftInfo[$date]['second_check_no_record_time'];
        }
        //在区间内 返回true
        if($this->currentDateTime >= $start && $this->currentDateTime <= $end){
            return true;
        }

//        //在区间之前 也返回true
//        if($this->currentDateTime < $start){
//            return true;
//        }
        $this->logger->write_log("checkShiftBetween_{$this->fleStaffInfo['id']} $date {$this->fleStaffInfo['id']} {$this->currentDateTime} {$start} {$end} ", 'info');
        return false;
    }

    public function setHomeWorkDate(){
        $dates = $this->getWorkHomeDates([$this->attendanceDate], $this->hrStaffInfo);
        if(!empty($dates) && in_array($this->attendanceDate, $dates)){
            $this->workHome = true;
        }
    }


    //打卡 坐标 和网点整理
    protected function getCoordinateInfo($lat, $lng)
    {
        $coordinate = [];//最终返回的最适合的坐标信息

        $settingServer = new SettingEnvServer();
        $attendanceRe  = new AttendanceRepository($this->lang, $this->timezone);
        //网点员工 坐标获取逻辑
        if ($this->fleStaffInfo['organization_type'] == HrStaffInfoModel::ORGANIZATION_STORE) {
            //获取 其他 符合条件的网点坐标信息
            $co_list = $this->coordinateStore($lat, $lng);
        } else {//总部的 取配置坐标
            $co_str = '';
            // Fulfillment 15
            if ($this->fleStaffInfo['organization_id'] == 15) {
                $co_str = $settingServer->getSetVal($this->fullment_coordinates);
            } else {
                if ($this->fleStaffInfo['organization_id'] == 28)//Flash Logistic 28
                {
                    $co_str = $settingServer->getSetVal($this->logic_coordinates);
                } else {//其他总部员工
                    //新需求 总部坐标和打卡范围 从配置改为 headquarters_attendance_range 表
                    $header_list = $attendanceRe->get_header_range();
                }
            }

            $co_str  = empty($co_str) ? [] : explode('-', $co_str);
            $co_list = [];
            if (!empty($co_str)) {
                foreach ($co_str as $co) {
                    $arr        = explode(',', $co);
                    $row['lng'] = $arr[0];
                    $row['lat'] = $arr[1];
                    $co_list[]  = $row;
                }
            }
            //总部的 多个坐标点 以及对应的 打卡范围
            if (!empty($header_list)) {
                foreach ($header_list as $v) {
                    $row['lng']   = $v['lng'];
                    $row['lat']   = $v['lat'];
                    $row['range'] = $v['attendance_range'];
                    $co_list[]    = $row;
                }
            }
        }

        //新需求 如果没有外勤打卡 并且 职位在配置内 增加取管辖范围网点逻辑 https://l8bx01gcjr.feishu.cn/docs/doccnbBxClv9dGbrsQGX9jgVMdd#
        $special_job_setting = $settingServer->getSetVal($this->special_job_title);
        //不是外勤 并且 不是居家办公
        if (!$this->fieldPunch && !$this->workHome) {
            $special_stores = $this->get_special_store($this->fleStaffInfo, $special_job_setting);
            $co_list        = empty($special_stores) ? $co_list : array_merge($co_list, $special_stores);
        }

        // 从打卡地点设置表获取
        $settingStoreIds = $this->getPunchInStoreIds($this->fleStaffInfo['id'], $co_list);
        if ($settingStoreIds) {
            $co_list = array_merge($co_list, $settingStoreIds);
        }

        $this->logger->write_log("attendance_info co_list {$this->fleStaffInfo['id']} ".json_encode($co_list), 'info');

        //最后处理 那个坐标 离得近
        if (!empty($co_list)) {
            $coordinate = $this->get_most_close($co_list, $lat, $lng);
        }

        //获取打卡范围 网点的有配置距离 总部的 取默认 范围
        if (!empty($coordinate['store_id'])) {
            $range_info = $attendanceRe->get_store_range($coordinate['store_id']);//根据网点获取 网点配置范围 表staff_work_attendance_range
        }
        $range = empty($range_info) ? $this->range : $range_info;

        $this->attendanceInfoBean['store_lat']        = empty($coordinate['lat']) ? 0 : $coordinate['lat'];
        $this->attendanceInfoBean['store_lng']        = empty($coordinate['lng']) ? 0 : $coordinate['lng'];
        $this->attendanceInfoBean['store_id']         = empty($coordinate['store_id']) ? '' : $coordinate['store_id'];
        $this->attendanceInfoBean['attendance_range'] = empty($coordinate['range']) ? $range : $coordinate['range'];
    }

    /**
     *
     * 比如 SAI网点:TH01470302 可以在 BKN网点：TH01180102 考勤打卡
     * java 代码逻辑
     * 1获取当前用户 网点的父网点信息
     * 2 并且获取配置网点信息 {"TH01470302":["TH01180102"],"TH02060110":["TH02020402"],"TH02030208":["TH02060110"]}
     * 3 取出来这些网点 以及对应的坐标点 然后 根据当前客户端位置 匹配那个最近 返回去
     * @param $lat
     * @param $lng
     * @return array
     */
    protected function coordinateStore($lat, $lng)
    {
        $settingServer = new SettingEnvServer();
        $storeId       = $this->fleStaffInfo['organization_id'];

        $co_list = [];//符合打卡的网点坐标集合

        //根据员工所在网点 取临时坐标 找最近的 （就是 一个网点有多个坐标 看哪个跟当前客户端最近）
        $co_data = $this->getTempCoordinate($this->fleStaffInfo);
        if(!empty($co_data)){
            //网点多个坐标 取最近的
            $batch_store = $this->get_most_close($co_data,$lat,$lng);
            $batch_store['store_id'] = $this->fleStaffInfo['organization_id'];
            $co_list[] = $batch_store;
        }

        //获取 其他 可打卡网点id
        $allowed_stores[] = $storeId;//把员工所属网点先加进去
        //支援 并且是 分离类型的 要把支援网点也加进去
        if($this->isSupport && $this->supportInfo['is_separate'] == HrStaffApplySupportStoreModel::IS_SEPARATE){
            $allowed_stores[] = $this->supportInfo['store_id'];
        }

        //快递员 110 采取获取 父网点 java 的代码逻辑 问产品
        if ($this->fleStaffInfo['job_title'] == enums::$job_title['van_courier']
            && $this->fleStaffInfo['formal'] == HrStaffInfoModel::FORMAL_1) {
            $store_info = $this->storeInfo;
            if (!$this->isSupport && !empty($store_info) && !empty($store_info->ancestry)) {
                $arr              = explode('/', $store_info->ancestry);
                $allowed_stores[] = end($arr);
            }
        }

        //获取关联打卡网点配置信息
        $all_connect = $settingServer->getSetVal($this->connect_store);
        if (!empty($all_connect)) {
            $all_connect = json_decode($all_connect, true);
            if (!empty($all_connect[$storeId])) {
                $allowed_stores = array_merge($allowed_stores, $all_connect[$storeId]);
            }
        }

        $storeData = SysStoreModel::find([
            'columns'    => 'id,lat,lng',
            'conditions' => 'id in ({stores:array})',
            'bind'       => [
                'stores' => $allowed_stores,
            ],
        ])->toArray();


        if (!empty($storeData)) {
            $correct = array_column($storeData, null, 'id');
            foreach ($correct as $co) {
                $row['store_id'] = $co['id'];
                $row['lng']      = $co['lng'];
                $row['lat']      = $co['lat'];
                $co_list[]       = $row;
            }
        }
        //白名单员工 或者 职位是Branch Manager 、Branch Supervisor 可以相同网点类型 打卡
        $ignore_list = $settingServer->getSetVal($this->ignore_list);
        if ((!empty($ignore_list) && in_array($this->fleStaffInfo['id'], explode(',', $ignore_list)))
            || in_array($this->fleStaffInfo['job_title'], $this->ignore_title)) {
            $store_category = $this->get_store_location($lat, $lng, [], $this->fleStaffInfo['store_category']);
            if (!empty($store_category)) {
                $co_list[] = $store_category;
            }
        }

        return $co_list;
    }


    //支援相关
    protected function getSupportReturn()
    {
        //非kit
        if (!empty($this->attendanceParam['platform']) && $this->attendanceParam['platform'] == enums::RB_KIT) {
            return;
        }

        if (empty($this->supportInfo)) {
            return;
        }

        //原来的 支援工号信息 这地方没判断开关 不知道为啥
        $this->attendanceInfoBean['support_staff_info']['employment_begin_date'] = $this->supportInfo['employment_begin_date'];
        $this->attendanceInfoBean['support_staff_info']['employment_end_date']   = $this->supportInfo['employment_end_date'];
        $this->attendanceInfoBean['support_staff_info']['support_store_name']    = $this->supportInfo['store_name'];
    }

    protected function setStoreInfo(){
        if($this->fleStaffInfo['organization_type'] != HrStaffInfoModel::ORGANIZATION_STORE){
            return;
        }
        //当前登陆员工 网点
        $storeId = $this->fleStaffInfo['organization_id'];

        //强制原网点打卡 判断当前能点亮的类型 是上班还是下班  只有第一个上班 在原网点 其余都在支援网点
        if($this->isSupport && $this->supportInfo['is_original_store'] == HrStaffApplySupportStoreModel::IS_ORIGINAL_STORE
            && $this->attendanceType == StaffWorkAttendanceModel::ATTENDANCE_ON)
        {
            $storeId = $this->supportInfo['staff_store_id'];
            $this->fleStaffInfo['organization_id'] = $storeId;
        }
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind'       => ['store_id' => $storeId],
        ]);
        //获取 网点分类
        $this->storeInfo                      = $storeInfo;
        $this->fleStaffInfo['store_category'] = empty($storeInfo) ? 0 : $storeInfo->category;
    }


    //是否 里程汇报
    protected function mileageReport()
    {
        // 职位是Van courier(110) car courier(1199) 并且是正式员工 打卡需要里程汇报
        $settingServer = new SettingEnvServer();
        $miles_job     = $settingServer->getSetVal($this->miles_job_title);
        if (!empty($miles_job) && in_array($this->fleStaffInfo['job_title'],
                explode(',', $miles_job)) && $this->fleStaffInfo['formal'] == HrStaffInfoModel::FORMAL_1) {
            $this->attendanceInfoBean['mileage_record_enabled'] = true;
        }
    }


    //是否验证 一小时内不能重复打卡
    protected function attendanceEnable()
    {
        $this->attendanceInfoBean['more_staff_info_enabled'] = false; //false:允许打卡   true:不允许打卡并弹出提示

        if (in_array(RUNTIME,['dev','tra','training'])) {
            return;
        }

        if (empty($this->attendanceParam['client_id'])) {
            $this->logger->write_log("attendanceEnable_{$this->fleStaffInfo['id']} client_id empty", 'info');
            return;
        }

        if ($this->fleStaffInfo['organization_type'] != HrStaffInfoModel::ORGANIZATION_STORE) {
            $this->logger->write_log("attendanceEnable_{$this->fleStaffInfo['id']} organization_type 非网点", 'info');
            return;
        }

        // 网点员工 一小时之内 该设备是否打过卡 并且 网点类型 是这些
        // 1 2 4 5 7
        // StoreCategory.STATION, StoreCategory.DISTRIBUTION_CENTER,
        // StoreCategory.MARKET_PICKUP_STATION,
        // StoreCategory.STATION_SHOP,StoreCategory.UNIVERSITY_SHOP)
        $settingStoreCategories = [
            enums::$stores_category['sp'],
            enums::$stores_category['dc'],
            enums::$stores_category['shop_pickup_only'],
            enums::$stores_category['shop_pickup_delivery'],
            enums::$stores_category['shop_ushop'],
        ];
        if (!in_array($this->fleStaffInfo['store_category'], $settingStoreCategories)) {
            $this->logger->write_log("attendanceEnable_{$this->fleStaffInfo['id']} 网点类型不对", 'info');
            return;
        }
        //要限制 1小时内 不能打卡
        $device_re                                           = new StaffDeviceInfoRepository($this->lang);
        $exist                                               = $device_re->get_device_one_hour($this->attendanceParam['client_id'],
            $this->fleStaffInfo['id']);
        $this->attendanceInfoBean['more_staff_info_enabled'] = $exist > 0 ? true : false;
    }

    //获取支援相关信息
    public function setSupportInfo(){
        //非网点 不支援
        if ($this->fleStaffInfo['organization_type'] != HrStaffInfoModel::ORGANIZATION_STORE){
            return ;
        }
        //支援员工 替换网点id
        $attendanceRe  = new AttendanceRepository($this->lang, $this->timezone);
        //主账号登陆 （没有子账号的支援  揽派分离） 打卡网点需要增加 支援信息网点
        $supportStaffInfo = $attendanceRe->getSupportOsStaffInfo($this->fleStaffInfo['id'], $this->attendanceDate);
        if(!empty($supportStaffInfo)){
            $this->supportInfo = $supportStaffInfo;
            $this->isSupport = true;
            $this->logger->write_log("is_support {$this->fleStaffInfo['id']} ".json_encode($supportStaffInfo), 'info');
            return ;

        }
        //子账号登陆 强制原网点打卡  判断 上班用主账号网点 下班用子账号网点
        $supportStaffInfo = $attendanceRe->getSupportInfoBySubStaff($this->fleStaffInfo['id'], $this->attendanceDate);
        if(!empty($supportStaffInfo)){
            $this->supportInfo = $supportStaffInfo;
            $this->isSupport = true;
            $this->logger->write_log("is_support {$this->fleStaffInfo['id']} ".json_encode($supportStaffInfo), 'info');
        }
    }


    //是否 出差外出居家办公
    protected function checkTripOut()
    {
        $this->addBusinessTripInfo($this->attendanceInfoBean, $this->fleStaffInfo['id'], $this->attendanceDate);
    }


    //是否有底片
    protected function photoExist()
    {
        $re         = new AttendanceRepository($this->lang, $this->timezone);
        //没有支援 按原来逻辑
        if($this->isSupport === false){
            $load_photo = $re->get_attendance_photo($this->fleStaffInfo['id']);
            if ($load_photo) {
                $this->attendanceInfoBean['original_url'] = true;
            }
            return ;
        }

        //有支援 并且是子账号登陆 去查询主账号底片
        if($this->fleStaffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF){
            $load_photo = $re->get_attendance_photo($this->supportInfo['staff_info_id']);
            if ($load_photo) {
                $this->attendanceInfoBean['original_url'] = true;
            }
        }

        //如果登陆的是主账号 不去查子账号的信息 因为支援期间不能打卡


    }


    //活体检测 是否在名单
    protected function isNameList()
    {
        //新增 静默活体接口 名单
//        $list = $this->get_live_list();
//        if (is_bool($list)) {
//            $this->attendanceInfoBean['is_name_list'] = $list;
//        } else {
//            $this->attendanceInfoBean['is_name_list'] = in_array($this->fleStaffInfo['id'], $list) ? true : false;
//        }
    }



    //初始化 保存接口信息
    protected function initSaveData()
    {
        //昨天今天 明天
        $this->today     = date('Y-m-d');
        $this->yesterday = date('Y-m-d', strtotime('-1 day'));
        $this->tomorrow  = date('Y-m-d', strtotime('+1 day'));
        //当前时间戳
        $this->currentTimeStamp = time();
        //当前时间点
        $this->currentDateTime = date('Y-m-d H:i:s', $this->currentTimeStamp);
        $staffRe = new StaffRepository($this->lang);
        //by  员工信息
        $this->hrStaffInfo = $staffRe->getStaffPosition($this->attendanceParam['user_info']['id']);
        //fle 员工信息 是 回调 info接口用
        $this->fleStaffInfo = $staffRe->getStaffpositionV2($this->attendanceParam['user_info']['id']);
        //考勤日期
        $this->attendanceDate = $this->attendanceParam['attendance_date'];
        //班次信息
        $shiftServer = new AttendanceCalendarV2Server($this->lang, $this->timezone);
        $this->shiftInfo = $this->fleStaffInfo['formal'] == HrStaffInfoModel::FORMAL_0 ? $this->getOldStaffShift($this->fleStaffInfo['id'],
            $this->yesterday, $this->tomorrow) : $shiftServer->getDailyShiftInfo($this->fleStaffInfo['id'],
            $this->attendanceDate, $this->attendanceDate);

        //如果是主播 班次 给空
        $liveJobId = (new SettingEnvServer())->getSetVal('free_shift_position');
        $this->liveJobId = empty($liveJobId) ? [] : explode(',', $liveJobId);
        if(in_array($this->fleStaffInfo['job_title'], $this->liveJobId)){
            $this->shiftInfo = $shiftInfo = [];
        }

        $this->isCustomer = $this->isBelongCustomer($this->hrStaffInfo);
        //网点员工支援的 要替换班次 和网点 搬过来的
//        $isOpen = (new SettingEnvServer())->getSetVal(AttendanceServer::SUB_STAFF_SUPPORT_SWITCH_KEY);
//        $this->logger->write_log("sub_staff_support isOpen=$isOpen", 'info');

        //根据考勤日期 获取支援信息 找打卡做表需要用到
        $this->setSupportInfo();

//
//        if ($this->fleStaffInfo['organization_type'] == HrStaffInfoModel::ORGANIZATION_STORE) {
//            $storeInfo = SysStoreModel::findFirst([
//                'conditions' => 'id = :store_id:',
//                'bind'       => ['store_id' => $this->fleStaffInfo['organization_id']],
//            ]);
//            //获取 网点分类
//            $this->storeInfo                      = $storeInfo;
//            $this->fleStaffInfo['store_category'] = empty($storeInfo) ? 0 : $storeInfo->category;
//            //支援 登陆的是子账号
//            if($this->fleStaffInfo['is_sub_staff'] && $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportInfoBySubStaff($this->fleStaffInfo['staff_info_id'],$this->attendanceDate)){
//                $this->isSupport = true;
//                $this->supportInfo = $supportStaffInfo;
//
//                //替换为 主账号信息 by  员工信息 班次信息 还用子账号的 不用变
//                $this->logger->write_log("is_support {$this->fleStaffInfo['id']} info changed " . json_encode($this->supportInfo) . json_encode($this->shiftInfo), 'info');
//            }
//        }
    }



    /**
     *
     新参数
     * click_at  打卡时间戳
     * attendance_date  打卡日期
     * clientid  客户端标识
     * attendance_state  打卡状态 1 正常 2 外勤
     * click_lng 打卡坐标
     * click_lat 打卡坐标
     * shift_type 几次打卡班次 保存接口回传 1 一个班次 2 2个班次
     * shift_index 1第一次 上班 2第一次下班 ,3 第二次上班 ,4 第二次下班
     * click_url 打卡图片
     * os 客户端系统
     *
     * @param 入参
     * @return bool|false
     * @throws ValidationException
     */
    public function saveAttendance($param)
    {
        $this->attendanceParam = $param;
        $this->logger->write_log("attendance input :".json_encode($param), 'info');
        //attendance_category = 1 是上班  shift_index%2 = 0 是下班 
        if (!empty($param['attendance_category']) && $param['attendance_category'] ==  StaffWorkAttendanceModel::ATTENDANCE_ON && $param['shift_index'] % 2 == 0) {
            throw new ValidationException($this->getTranslation()->_('go_back_home_retry'));
        }
        //初始化数据
        $this->initSaveData();

        //验证
        $this->businessCheck();

        //是否是工作日
        $staff_model = new StaffRepository($this->lang);
        $this->isWorkingDay = $staff_model->get_is_working_day($this->hrStaffInfo,$this->attendanceDate);

        //新需求 https://l8bx01gcjr.feishu.cn/docs/doccnZzrd3PvTSB7BDw2fCphGJf#
        //判断是否在出差状态 没有出差 走原来流程
        $exist = BusinessTripModel::findFirst([
            'conditions' => 'apply_user = :staff_id: and start_time <= :date: and end_time >= :date: and status = 2',
            'bind' => [
                'staff_id' => $this->fleStaffInfo['id'],
                'date' => $this->attendanceDate,
            ],
        ]);
        //新增 居家办公 也要发起审批
        $this->setHomeWorkDate();
        //如果是 出差 或者 居家办公 打卡都进审批打卡表
        if($exist){
            return $this->saveTripAttendance($exist->toArray());
        }
        //居家办公
        if($this->workHome){
            return $this->saveTripAttendance();
        }

        //获取同设备 打卡次数
        $device_re = new StaffDeviceInfoRepository($this->lang);
        $client_num = $device_re->get_click_num($this->attendanceParam['clientid'],$this->attendanceParam['attendance_date'],$this->attendanceParam['shift_index'] % 2);
        $client_num = intval($client_num) + 1;


        //保存操作  主账号 和子账号 2条
        $shiftType = StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;//0 一个班次默认  1 两个班次第一次 2 两个班次 第二次
        if(!empty($this->shiftInfo) && $this->shiftInfo[$this->attendanceDate]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE){
            $shiftType = ceil($this->attendanceParam['shift_index'] / 2);
        }

        //大于0 上班 等于0 下班
        $attendanceType = $this->attendanceParam['shift_index'] % 2;
        //先查 有没有数据
        $cardInfo = StaffWorkAttendanceModel::findFirst([
            'conditions' => 'attendance_date = :date: and shift_type = :shiftType: and staff_info_id = :staff_id:',
            'bind' => [
                'date' => $this->attendanceDate,
                'shiftType' => $shiftType,
                'staff_id' => $this->fleStaffInfo['id'],
            ],
        ]);

        $startIndexName = 'first_start';
        $endIndexName   = 'first_end';
        if ($this->attendanceParam['shift_index'] > enums::ATTENDANCE_BUTTON_NUM_ONE) {
            $startIndexName = 'second_start';
            $endIndexName   = 'second_end';
        }
        //打卡客户端来源
        $platform = $this->attendanceParam['platform'] == enums::RB_KIT ? StaffWorkAttendanceModel::PLATFORM_KIT : StaffWorkAttendanceModel::PLATFORM_BY;//KIT 1  BACKYARD 3

        //班次日志记录
        $this->logger->write_log("shift_info_{$this->fleStaffInfo['id']} " . json_encode($this->shiftInfo),'info');

        try {
            $this->getDI()->get('db')->begin();
            $currentTime = time();
            $clock_time  = gmdate('Y-m-d H:i:s', $currentTime);
            //没记录 要新增
            if (empty($cardInfo)) {
                //组织 保存数据
                $this->attendanceObj['attendance_date'] = $this->attendanceDate;
                $this->attendanceObj['staff_info_id']   = $this->fleStaffInfo['id'];;
                $this->attendanceObj['organization_id']   = $this->fleStaffInfo['organization_id'];
                $this->attendanceObj['organization_type'] = $this->fleStaffInfo['organization_type'];
                $this->attendanceObj['job_title']         = intval($this->fleStaffInfo['job_title']);
                $this->attendanceObj['shift_id']          = $this->shiftInfo[$this->attendanceDate]['shift_id'] ?? 0;
                $this->attendanceObj['shift_ext_id']      = $this->shiftInfo[$this->attendanceDate]['shift_extend_id'] ?? 0;
                $this->attendanceObj['shift_type']        = $shiftType;
                $this->attendanceObj['shift_start']       = $this->shiftInfo[$this->attendanceDate][$startIndexName] ?? '';
                $this->attendanceObj['shift_end']         = $this->shiftInfo[$this->attendanceDate][$endIndexName] ?? '';
                $this->attendanceObj['working_day']       = $this->isWorkingDay;
                //外协员工记录上班打卡时所在外协公司
                if (in_array($this->hrStaffInfo['staff_type'], [1, 2, 3])
                    && $this->hrStaffInfo['sys_store_id'] != '-1') {
                    $this->syncAttendanceForCompanyNameEfPush($this->fleStaffInfo['id'], $this->attendanceDate);
                }
                //上班卡
                if ($attendanceType > 0) {
                    $this->attendanceObj['started_at']             = $clock_time;
                    $this->attendanceObj['started_state']          = intval($this->attendanceParam['attendance_state']);
                    $this->attendanceObj['started_staff_lat']      = $this->attendanceParam['click_lat'];
                    $this->attendanceObj['started_staff_lng']      = $this->attendanceParam['click_lng'];
                    $this->attendanceObj['started_store_id']       = $this->attendanceInfoBean['store_id'] ?? null;
                    $this->attendanceObj['started_store_lng']      = $this->attendanceInfoBean['store_lng'] ?? null;
                    $this->attendanceObj['started_store_lat']      = $this->attendanceInfoBean['store_lat'] ?? null;
                    $this->attendanceObj['started_clientid']       = $this->attendanceParam['clientid'];
                    $this->attendanceObj['started_clientid_num']   = $client_num;
                    $this->attendanceObj['started_equipment_type'] = $platform;
                    $this->attendanceObj['started_os']             = $this->attendanceParam['os'];
                    $this->attendanceObj['started_path']           = $this->attendanceParam['click_url'];//带模块的文件名  attxxx/aa.jpg
                    $this->attendanceObj['started_bucket']         = $this->getDI()['config']['application']['oss_bucket'];//固定
                    $this->attendanceObj['started_remark']         = $this->attendanceParam['remark'] ?? '';
                } else {//下班卡$this->attendanceBusinessObj
                    $this->attendanceObj['end_at']             = $clock_time;
                    $this->attendanceObj['end_state']          = intval($this->attendanceParam['attendance_state']);
                    $this->attendanceObj['end_staff_lat']      = $this->attendanceParam['click_lat'];
                    $this->attendanceObj['end_staff_lng']      = $this->attendanceParam['click_lng'];
                    $this->attendanceObj['end_store_id']       = $this->attendanceInfoBean['store_id'] ?? null;
                    $this->attendanceObj['end_store_lng']      = $this->attendanceInfoBean['store_lng'] ?? null;
                    $this->attendanceObj['end_store_lat']      = $this->attendanceInfoBean['store_lat'] ?? null;
                    $this->attendanceObj['end_clientid']       = $this->attendanceParam['clientid'];
                    $this->attendanceObj['end_clientid_num']   = $client_num;
                    $this->attendanceObj['end_equipment_type'] = $platform;
                    $this->attendanceObj['end_os']             = $this->attendanceParam['os'];
                    $this->attendanceObj['end_path']           = $this->attendanceParam['click_url'];//带模块的文件名  attxxx/aa.jpg
                    $this->attendanceObj['end_bucket']         = $this->getDI()['config']['application']['oss_bucket'];//固定
                    $this->attendanceObj['end_remark']         = $this->attendanceParam['remark'] ?? '';
                }
                $model = new StaffWorkAttendanceModel();
                $flag  = $model->create($this->attendanceObj);
                $this->logger->write_log("create_attendance_{$this->fleStaffInfo['id']} {$flag} ", 'info');
            } else {//有记录 更新操作

                $cardInfo->working_day = $this->isWorkingDay;
                if ($attendanceType > 0) {//上班打卡
                    $cardInfo->started_at             = $cardInfo->started_at ? min($cardInfo->started_at,
                        $clock_time) : $clock_time;
                    $cardInfo->started_state          = intval($this->attendanceParam['attendance_state']);
                    $cardInfo->started_staff_lat      = $this->attendanceParam['click_lat'];
                    $cardInfo->started_staff_lng      = $this->attendanceParam['click_lng'];
                    $cardInfo->started_store_id       = $this->attendanceInfoBean['store_id'] ?? null;
                    $cardInfo->started_store_lng      = $this->attendanceInfoBean['store_lng'] ?? null;
                    $cardInfo->started_store_lat      = $this->attendanceInfoBean['store_lat'] ?? null;
                    $cardInfo->started_clientid       = $this->attendanceParam['clientid'];
                    $cardInfo->started_clientid_num   = $client_num;
                    $cardInfo->started_equipment_type = $platform;
                    $cardInfo->started_os             = $this->attendanceParam['os'];
                    $cardInfo->started_path           = $this->attendanceParam['click_url'];//带模块的文件名  attxxx/aa.jpg
                    $cardInfo->started_bucket         = $this->getDI()['config']['application']['oss_bucket'];//固定
                    $cardInfo->started_remark         = $this->attendanceParam['remark'] ?? '';
                } else {//下班打卡
                    $cardInfo->end_at             = $cardInfo->end_at ? max($cardInfo->end_at,
                        $clock_time) : $clock_time;
                    $cardInfo->end_state          = intval($this->attendanceParam['attendance_state']);
                    $cardInfo->end_staff_lat      = $this->attendanceParam['click_lat'];
                    $cardInfo->end_staff_lng      = $this->attendanceParam['click_lng'];
                    $cardInfo->end_store_id       = $this->attendanceInfoBean['store_id'] ?? null;
                    $cardInfo->end_store_lng      = $this->attendanceInfoBean['store_lng'] ?? null;
                    $cardInfo->end_store_lat      = $this->attendanceInfoBean['store_lat'] ?? null;
                    $cardInfo->end_clientid       = $this->attendanceParam['clientid'];
                    $cardInfo->end_clientid_num   = $client_num;
                    $cardInfo->end_equipment_type = $platform;
                    $cardInfo->end_os             = $this->attendanceParam['os'];
                    $cardInfo->end_path           = $this->attendanceParam['click_url'];//带模块的文件名  attxxx/aa.jpg
                    $cardInfo->end_bucket         = $this->getDI()['config']['application']['oss_bucket'];//固定
                    $cardInfo->end_remark         = $this->attendanceParam['remark'] ?? '';
                }
                $flag = $cardInfo->update();
                $this->logger->write_log("update_attendance_{$this->fleStaffInfo['id']} {$flag} ", 'info');
            }

            $format['bucket'] = $this->getDI()['config']['application']['oss_bucket'];
            $format['path']   = $this->attendanceParam['click_url'];
            $sourceUrl        = $this->format_oss($format);
            //查询底片
            $faceImageUrl = (new AttendanceImageVerifyServer($this->lang, $this->timezone))->getStaffFaceImage($this->fleStaffInfo['id']);
            if(empty($faceImageUrl)) {
                $this->getDI()->get('logger')->write_log(['saveAttendanceScanFaceRecord_saveAttendance' => ['staff_info_id' => $this->fleStaffInfo['id'], 'remark' => '未找到底片请核查']] ,'error');
            }

            $check_staff_info_id = empty($this->isSupport) ? $this->fleStaffInfo['id'] : $this->supportInfo['staff_info_id'];
            //写入扫脸记录表，刷一次记一次
            $scanFaceRecordData = [
                'staff_info_id'   => $check_staff_info_id,
                'attendance_date' => $this->attendanceDate,
                'type'            => $attendanceType > 0 ? StaffWorkAttendanceModel::ATTENDANCE_ON : StaffWorkAttendanceModel::ATTENDANCE_OFF,
                'operating_mode'  => intval($this->attendanceParam['attendance_state']),
                'event_time'      => $clock_time,
                'lng'             => $this->attendanceParam['click_lng'],
                'lat'             => $this->attendanceParam['click_lat'],
                'client_id'       => $this->attendanceParam['clientid'],
                'os'              => $this->attendanceParam['os'],
                'face_img'        => $sourceUrl,
                'face_negative'   => !empty($faceImageUrl) ? $faceImageUrl : $sourceUrl,
            ];
            $this->saveAttendanceScanFaceRecord($scanFaceRecordData);

            //外勤打卡数据--发送给上级：消息+邮件
            if ($this->attendanceParam['attendance_state'] == StaffWorkAttendanceModel::STATE_FIELD_PERSONNEL_CARD && $flag && !$this->isSupport) {

                //$attendanceType 大于0 上班，$attendance_category =1:上班，$attendance_category = 2 下班
                $attendance_category = $attendanceType > 0 ? StaffWorkAttendanceModel::ATTENDANCE_ON : StaffWorkAttendanceModel::ATTENDANCE_OFF;

                $data['staff_info_id']       = $param['user_info']['id'];//工号
                $data['name']                = $param['user_info']['name'];//姓名
                $data['clock_time']          = $clock_time;//零时区,打卡时间
                $data['attendance_category'] = $attendance_category;//上/下班 卡
                $data['staff_lat']           = $this->attendanceParam['click_lat'];//纬度
                $data['staff_lng']           = $this->attendanceParam['click_lng'];//经度
                $data['attendance_date']     = $this->attendanceDate;//考勤日期
                $data['remark']              = $this->attendanceParam['remark'];//外勤原因
                $this->sendFieldPersonnelMessage($data);
            }

            //是否支援 需要保存支援主账号 打卡
            if(empty($this->isSupport)){
                $this->getDI()->get('db')->commit();

                //推送上下班打卡时间和坐标到MS
                $clock_data = [
                    'staffInfoId' => intval($this->fleStaffInfo['id']),
                    'clockAt'     => $currentTime,
                    'lat'         => $this->attendanceParam['click_lat'],
                    'lng'         => $this->attendanceParam['click_lng'],
                    'type'        => $attendanceType > 0 ? StaffWorkAttendanceModel::ATTENDANCE_ON : StaffWorkAttendanceModel::ATTENDANCE_OFF,//1,上班，2下班
                    'deviceId'    => $this->attendanceParam['clientid'],
                ];
                $this->sendClockToMs($clock_data);

                //下班打卡 并且是 客服 同步 客服系统
                if(in_array($shiftType, [StaffWorkAttendanceModel::SHIFT_TYPE_ONLY,StaffWorkAttendanceModel::SHIFT_TYPE_SECOND])
                    && $attendanceType == 0 && $this->isCustomer)
                {
                    $param['attendance_category'] = $this->attendanceParam['shift_index'];
                    $param['attendance_date'] = $this->attendanceDate;
                    $this->syncWebHookCustomer($this->hrStaffInfo, $param);
                }

                return $flag;
            }

            //查询 主账号 打卡信息
            $masterCardInfo = StaffWorkAttendanceModel::findFirst([
                'conditions' => 'attendance_date = :date: and shift_type = :shiftType: and staff_info_id = :staff_id:',
                'bind' => [
                    'date' => $this->attendanceDate,
                    'shiftType' => $shiftType,
                    'staff_id' => $this->supportInfo['staff_info_id'],
                ],
            ]);

            //支援 主账号保存
            if(empty($masterCardInfo)){
                $this->attendanceObj['staff_info_id']   = $this->supportInfo['staff_info_id'];
                $this->attendanceObj['organization_id'] = $this->supportInfo['staff_store_id'];
                $model = new StaffWorkAttendanceModel();
                $flag = $model->create($this->attendanceObj);

                $this->logger->write_log("save_attendance_masterStaff_{$this->supportInfo['staff_info_id']} {$flag} ",'info');
                $this->getDI()->get('db')->commit();
                //推送上下班打卡时间和坐标到MS
                $clock_data = [
                    'staffInfoId' => intval($this->supportInfo['staff_info_id']),
                    'clockAt'     => $currentTime,
                    'lat'         => $this->attendanceParam['click_lat'],
                    'lng'         => $this->attendanceParam['click_lng'],
                    'type'        => $attendanceType > 0 ? StaffWorkAttendanceModel::ATTENDANCE_ON : StaffWorkAttendanceModel::ATTENDANCE_OFF,//1,上班，2下班
                    'deviceId'    => $this->attendanceParam['clientid'],
                ];
                $this->sendClockToMs($clock_data);
                return $flag;
            }

            //下班打卡 要更新主账号
            if($attendanceType == 0){
                $masterCardInfo->working_day        = $this->isWorkingDay;
                $masterCardInfo->end_at             = $masterCardInfo->end_at ? max($masterCardInfo->end_at, $clock_time) : $clock_time;
                $masterCardInfo->end_state          = intval($this->attendanceParam['attendance_state']);
                $masterCardInfo->end_staff_lat      = $this->attendanceParam['click_lat'];
                $masterCardInfo->end_staff_lng      = $this->attendanceParam['click_lng'];
                $masterCardInfo->end_store_id       = $this->attendanceInfoBean['store_id'] ?? null;
                $masterCardInfo->end_store_lng      = $this->attendanceInfoBean['store_lng'] ?? null;
                $masterCardInfo->end_store_lat      = $this->attendanceInfoBean['store_lat'] ?? null;
                $masterCardInfo->end_clientid       = $this->attendanceParam['clientid'] . '-s';
                $masterCardInfo->end_clientid_num   = $client_num;
                $masterCardInfo->end_equipment_type = $platform;
                $masterCardInfo->end_os             = $this->attendanceParam['os'];
                $masterCardInfo->end_path           = $this->attendanceParam['click_url'];//带模块的文件名  attxxx/aa.jpg
                $masterCardInfo->end_bucket         = $this->getDI()['config']['application']['oss_bucket'];//固定
                $masterCardInfo->end_remark         = $this->attendanceParam['remark'] ?? '';
            }

            if ($masterCardInfo->started_at) {
                $masterCardInfo->started_at = min($masterCardInfo->started_at, $clock_time);
            }

            $flag = $masterCardInfo->update();
            $this->logger->write_log("update_attendance_masterStaff_{$this->supportInfo['staff_info_id']} {$flag} ",'info');
            $this->getDI()->get('db')->commit();
            //推送上下班打卡时间和坐标到MS
            $clock_data = [
                'staffInfoId' => intval($masterCardInfo->staff_info_id),
                'clockAt'     => $currentTime,
                'lat'         => $this->attendanceParam['click_lat'],
                'lng'         => $this->attendanceParam['click_lng'],
                'type'        => $attendanceType > 0 ? StaffWorkAttendanceModel::ATTENDANCE_ON : StaffWorkAttendanceModel::ATTENDANCE_OFF,//1,上班，2下班
                'deviceId'    => $this->attendanceParam['clientid'],
            ];
            $this->sendClockToMs($clock_data);
            return $flag;
        }catch (\Exception $e){
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log("attendance 打卡异常 {$this->fleStaffInfo['id']} " . $e->getTraceAsString());
            return false;
        }
    }

    //逻辑验证
    protected function businessCheck(){
        //外勤打卡 需要备注
        if($this->attendanceParam['attendance_state'] == StaffWorkAttendanceModel::STATE_FIELD_PERSONNEL_CARD && empty($this->attendanceParam['remark'])){
            throw new ValidationException($this->getTranslation()->_('miss_args').' remark',100324);
        }

        //打卡没有坐标
        if(empty($this->attendanceParam['click_lat']) || empty($this->attendanceParam['click_lng'])){
            throw new ValidationException('wrong coordinate',100328);
        }

        //支援 并且是 主账号打卡 并且 非揽派分离 提示
        if($this->isSupport && $this->fleStaffInfo['id'] == $this->supportInfo['staff_info_id']
            && $this->supportInfo['is_separate'] != HrStaffApplySupportStoreModel::IS_SEPARATE){
            throw new ValidationException($this->getTranslation()->_('staff_support_period_remind'),100);
        }

        //子账号 但是 没有有效支援信息
        if($this->fleStaffInfo['is_sub_staff'] && empty($this->supportInfo)){
            throw new ValidationException($this->getTranslation()->_('staff_support_expired'),100337);
        }
        $this->getAttendanceTypeInfoList();
        //网点信息
        $this->setStoreInfo();//依赖 getAttendanceTypeInfoList
        //重新验证 考勤日期 上下班类型 和 获取对应打卡网点坐标
        $this->getCoordinateInfo($this->attendanceParam['click_lat'],$this->attendanceParam['click_lng']);


        //日期不对 100325
        if($this->attendanceInfoBean['attendance_date'] != $this->attendanceParam['attendance_date']){
            throw new ValidationException($this->getTranslation()->_('wrong date'),100325);
        }

        //验证 回传 打卡 索引
        $buttonList = array_column($this->attendanceInfoBean['button_list'],null,'shift_index');
        if($buttonList[$this->attendanceParam['shift_index']]['state'] != enums::$attendanceButtonState['button_could']){
            throw new ValidationException($this->getTranslation()->_('wrong attendance type'),100336);
        }

    }


    //出差打卡 保存 如果是居家办公 没有出差记录
    protected function saveTripAttendance($business_trip = [])
    {
        //居家办公情况
        $auditType = AuditListEnums::APPROVAL_TYPE_WORK_HOME;
        $tripType  = BusinessTripModel::BTY_WORK_FROM_HOME;
        $time_zone = $this->config->application->add_hour;
        if (!empty($business_trip)) {
            $tripType  = $business_trip['business_trip_type'];
            $atbServer = new AttendanceBusinessServer($this->lang, $this->timezone);
            $auditType = $atbServer->getAuditTypeByBusinessTripType($tripType);
            //判断是否需要根据时区调整时间 https://flashexpress.feishu.cn/docx/NuQpdsXRSoSJtxxt0AKcaEwanPb
            //获取出差打卡时的时区
            $param = [
                'business_trip_type'  => $business_trip['business_trip_type'],
                'destination_country' => $business_trip['destination_country'],
                'click_lat'           => $this->attendanceParam['click_lat'],
                'click_lng'           => $this->attendanceParam['click_lng'],
                'staff_info_id'       => $this->fleStaffInfo['id'],
            ];
            $this->logger->write_log("getBusinessAttendanceTimeZone {$this->fleStaffInfo['id']} " . json_encode($param),
                'info');
            $time_zone = $this->getBusinessAttendanceTimeZone($param);  //时区默认是 当地时区
        }

        $shiftType = StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;
        if (!empty($this->shiftInfo[$this->attendanceDate]['shift_type']) && $this->shiftInfo[$this->attendanceDate]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
            $shiftType = ceil($this->attendanceParam['shift_index'] / 2);
        }

        //大于0 上班 等于0 下班
        $attendanceType = $this->attendanceParam['shift_index'] % 2;
        //先查 有没有数据
        $cardInfo = StaffAuditReissueForBusinessModel::findFirst([
            'conditions' => 'attendance_date = :date: and shift_type = :shiftType: and staff_info_id = :staff_id:',
            'bind'       => [
                'date'      => $this->attendanceDate,
                'shiftType' => $shiftType,
                'staff_id'  => $this->fleStaffInfo['id'],
            ],
        ]);

        if (!empty($cardInfo) && $attendanceType > 0 && !empty($attendanceType->start_time)) {
            throw new ValidationException($this->getTranslation()->_('business_card_notice'), -1);
        }

        if (!empty($cardInfo) && $attendanceType == 0 && !empty($attendanceType->end_time)) {
            throw new ValidationException($this->getTranslation()->_('business_card_notice'), -1);
        }

        $startIndexName = 'first_start';
        $endIndexName   = 'first_end';
        if ($this->attendanceParam['shift_index'] > enums::ATTENDANCE_BUTTON_NUM_ONE) {
            $startIndexName = 'second_start';
            $endIndexName   = 'second_end';
        }

        $currentTime = gmdate('Y-m-d H:i:s',time());

        $format['bucket'] = $this->getDI()['config']['application']['oss_bucket'];
        $format['path']   = $this->attendanceParam['click_url'];
        $sourceUrl        = $this->format_oss($format);
        //查询底片
        $faceImageUrl = (new AttendanceImageVerifyServer($this->lang, $this->timezone))->getStaffFaceImage($this->fleStaffInfo['id']);
        if(empty($faceImageUrl)) {
            $this->getDI()->get('logger')->write_log(['saveAttendanceScanFaceRecord_saveTripAttendance' => ['staff_info_id' => $this->fleStaffInfo['id'], 'remark' => '未找到底片请核查']] ,'error');
        }

        //写入扫脸记录表，刷一次记一次
        $scanFaceRecordData = [
            'staff_info_id'   => $this->fleStaffInfo['id'],
            'attendance_date' => $this->attendanceDate,
            'type'            => $attendanceType > 0 ? StaffWorkAttendanceModel::ATTENDANCE_ON : StaffWorkAttendanceModel::ATTENDANCE_OFF,
            'operating_mode'  => AttendanceBusinessServer::getStaffWorkAttendanceStateByBusinessTripType($tripType),
            'event_time'      => $currentTime,
            'lng'             => $this->attendanceParam['click_lng'],
            'lat'             => $this->attendanceParam['click_lat'],
            'client_id'       => $this->attendanceParam['clientid'],
            'os'              => $this->attendanceParam['os'],
            'face_img'        => $sourceUrl,
            'face_negative'   => !empty($faceImageUrl) ? $faceImageUrl : $sourceUrl,
        ];
        $this->saveAttendanceScanFaceRecord($scanFaceRecordData);

        //没记录 要新增
        if (empty($cardInfo)) {
            //申请序列号
            $this->attendanceBusinessObj['serial_no']          = $auditType . $this->getID();
            $this->attendanceBusinessObj['staff_info_id']      = $this->fleStaffInfo['id'];
            $this->attendanceBusinessObj['attendance_date']    = $this->attendanceDate;
            $this->attendanceBusinessObj['shift_id']           = $this->shiftInfo[$this->attendanceDate]['shift_id'] ?? 0;
            $this->attendanceBusinessObj['shift_ext_id']       = $this->shiftInfo[$this->attendanceDate]['shift_extend_id'] ?? 0;
            $this->attendanceBusinessObj['shift_type']         = $shiftType;
            $this->attendanceBusinessObj['start_shift']        = $this->shiftInfo[$this->attendanceDate][$startIndexName] ?? '';
            $this->attendanceBusinessObj['end_shift']          = $this->shiftInfo[$this->attendanceDate][$endIndexName] ?? '';
            $this->attendanceBusinessObj['working_day']        = $this->isWorkingDay;
            $this->attendanceBusinessObj['task_time']          = date('Y-m-d 12:00:00',
                strtotime("+1 day"));//第二天 中午12点 跑任务时间
            $this->attendanceBusinessObj['business_trip_type'] = $tripType;
            //上班卡
            if ($attendanceType > 0) {
                $this->attendanceBusinessObj['start_time']       = $currentTime;
                $this->attendanceBusinessObj['start_lat']        = $this->attendanceParam['click_lat'];
                $this->attendanceBusinessObj['start_lng']        = $this->attendanceParam['click_lng'];
                $this->attendanceBusinessObj['started_path']     = $this->attendanceParam['click_url'];
                $this->attendanceBusinessObj['started_bucket']   = $this->getDI()['config']['application']['oss_bucket'];//固定
                $this->attendanceBusinessObj['start_reason']     = $this->attendanceParam['remark'] ?? '';
                $this->attendanceBusinessObj['start_time_zone']  = $time_zone;
                $this->attendanceBusinessObj['started_os']       = $this->attendanceParam['os'];
                $this->attendanceBusinessObj['started_clientid'] = $this->attendanceParam['clientid'];
            } else {//下班卡$this->attendanceBusinessObj
                $this->attendanceBusinessObj['end_time']      = $currentTime;
                $this->attendanceBusinessObj['end_lat']       = $this->attendanceParam['click_lat'];
                $this->attendanceBusinessObj['end_lng']       = $this->attendanceParam['click_lng'];
                $this->attendanceBusinessObj['end_path']      = $this->attendanceParam['click_url'];
                $this->attendanceBusinessObj['end_bucket']    = $this->getDI()['config']['application']['oss_bucket'];
                $this->attendanceBusinessObj['end_reason']    = $this->attendanceParam['remark'] ?? '';
                $this->attendanceBusinessObj['end_time_zone'] = $time_zone;
                $this->attendanceBusinessObj['end_os']        = $this->attendanceParam['os'];
                $this->attendanceBusinessObj['end_clientid']  = $this->attendanceParam['clientid'];
            }
            $this->getDI()->get('logger')->write_log("出差期间外勤打卡操作 新增 {$this->fleStaffInfo['id']}" . json_encode($this->attendanceBusinessObj), 'info');
            $model = new StaffAuditReissueForBusinessModel();
            return $model->create($this->attendanceBusinessObj);
        }

        //更新操作
        if ($attendanceType > 0) {//上班卡
            $cardInfo->start_time       = $currentTime;
            $cardInfo->start_lat        = $this->attendanceParam['click_lat'];
            $cardInfo->start_lng        = $this->attendanceParam['click_lng'];
            $cardInfo->started_path     = $this->attendanceParam['click_url'];
            $cardInfo->started_bucket   = $this->getDI()['config']['application']['oss_bucket'];//固定
            $cardInfo->start_reason     = $this->attendanceParam['remark'] ?? '';
            $cardInfo->start_time_zone  = $time_zone;
            $cardInfo->started_os       = $this->attendanceParam['os'];
            $cardInfo->started_clientid = $this->attendanceParam['clientid'];
        } else {//下班卡
            $cardInfo->end_time      = $currentTime;
            $cardInfo->end_lat       = $this->attendanceParam['click_lat'];
            $cardInfo->end_lng       = $this->attendanceParam['click_lng'];
            $cardInfo->end_path      = $this->attendanceParam['click_url'];
            $cardInfo->end_bucket    = $this->getDI()['config']['application']['oss_bucket'];
            $cardInfo->end_reason    = $this->attendanceParam['remark'] ?? '';
            $cardInfo->end_time_zone = $time_zone;
            $cardInfo->end_os        = $this->attendanceParam['os'];
            $cardInfo->end_clientid  = $this->attendanceParam['clientid'];
        }
        $this->getDI()->get('logger')->write_log("出差期间外勤打卡操作 更新 {$this->fleStaffInfo['id']}" . json_encode($cardInfo), 'info');
        return $cardInfo->update();
    }



    /**
     *根据工号日期 获取班次信息
    SELECT `s`.`staff_info_id` AS `staff_info_id`, `s`.`shift_date` AS `shift_date`, `s`.`shift_id` AS `shift_id`,
    `s`.`shift_extend_id` AS `shift_extend_id`, `m`.`shift_name` AS `shift_name`, `e`.`shift_type` AS `shift_type`,
    `e`.`first_start` AS `first_start`, `e`.`first_end` AS `first_end`, `e`.`second_start` AS `second_start`,
    `e`.`second_end` AS `second_end`, `e`.`middle_rest_end` AS `middle_rest_end`,e.middle_rest_start, `e`.`ot` AS `ot`, `e`.`is_ot` AS `is_ot`
    FROM `hr_staff_shift_v2` AS `s`
    LEFT JOIN `hr_shift_v2` AS `m` ON `s`.`shift_id` = `m`.`id`
    LEFT JOIN `hr_shift_v2_extend` AS `e` ON `s`.`shift_extend_id` = `e`.`id`
    WHERE (`s`.`staff_info_id` = 118673) AND (`s`.`shift_date` = '2022-12-05')
     * @param $staffId
     * @param $date
     * @return mixed
     */
    public function getStaffShiftInfoByDate($staffId,$date){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.staff_info_id,s.shift_date,s.shift_id,s.shift_extend_id,m.shift_name,e.shift_type,e.first_start,
        e.first_end,e.second_start,e.second_end,e.middle_rest_end,e.middle_rest_start,e.ot,e.is_ot,e.first_late,e.second_late');
        $builder->from(['s' => HrStaffShiftV2Model::class]);
        $builder->leftjoin(HrShiftV2Model::class, 's.shift_id = m.id', 'm');
        $builder->leftjoin(HrShiftV2ExtendModel::class, 's.shift_extend_id = e.id', 'e');
        $builder->andWhere('s.staff_info_id = :staff_id:', ['staff_id' => $staffId]);
        $builder->andWhere('s.shift_date = :date:', ['date' => $date]);
        $builder->andWhere('e.state = :state:', ['state' => HrShiftV2ExtendModel::STATE_USING]);
        $data = $builder->getQuery()->execute()->getFirst();
        return empty($data) ? [] : $data->toArray();
    }

    //整理 班次时间
    public function formatShiftInfo($shiftInfo)
    {
        $date = $shiftInfo['shift_date'];

        $shiftInfo['format_first_start'] = "{$date} {$shiftInfo['first_start']}";
        $shiftInfo['format_first_end']   = "{$date} {$shiftInfo['first_end']}";
        $shiftInfo['half_start'] = "{$date} {$shiftInfo['middle_rest_end']}";
        $shiftInfo['half_end'] = "{$date} {$shiftInfo['middle_rest_start']}";

        //全天跨天情况
        if ($shiftInfo['first_start'] > $shiftInfo['first_end']) {
            $shiftInfo['format_first_end'] = date('Y-m-d H:i', strtotime("{$shiftInfo['format_first_end']} +1 day"));
        }

        //半天跨天情况
        if ($shiftInfo['first_start'] > $shiftInfo['middle_rest_end']) {
            $shiftInfo['half_start'] = date('Y-m-d H:i', strtotime("{$shiftInfo['half_start']} +1 day"));
        }

        if ($shiftInfo['first_start'] > $shiftInfo['middle_rest_start']) {
            $shiftInfo['half_end'] = date('Y-m-d H:i', strtotime("{$shiftInfo['half_end']} +1 day"));
        }

        $shiftInfo['format_second_start'] = $shiftInfo['format_second_end'] = '';
        //一次班 情况
        if ($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE) {
            return $shiftInfo;
        }
        //两次班 情况
        $shiftInfo['format_second_start'] = "{$date} {$shiftInfo['second_start']}";
        $shiftInfo['format_second_end']   = "{$date} {$shiftInfo['second_end']}";
        $shiftInfo['half_first_start'] = "{$date} {$shiftInfo['middle_rest_end']}";
        $shiftInfo['half_first_end'] = "{$date} {$shiftInfo['middle_rest_start']}";

        //第二次班开始 时间跨天情况
        if ($shiftInfo['first_start'] > $shiftInfo['second_start']) {
            $shiftInfo['format_second_start'] = date('Y-m-d H:i',
                strtotime("{$shiftInfo['format_second_start']} +1 day"));
        }
        //第二次班 结束时间 跨天情况
        if ($shiftInfo['first_start'] > $shiftInfo['second_end']) {
            $shiftInfo['format_second_end'] = date('Y-m-d H:i', strtotime("{$shiftInfo['format_second_end']} +1 day"));
        }
        return $shiftInfo;
    }


    /**
     * 补卡审核通过 回写 hcm 工具也调用
     * @param $reissueInfo
     * @param array $cardInfo
     * @param $isTool boolean 是工具操作
     * @return mixed
     */
    public function saveReissueCard($reissueInfo,$cardInfo = [],$isTool = false){
        //查询 补卡日期的 班次信息
        $shiftInfo = $this->getStaffShiftInfoByDate($reissueInfo['staff_info_id'], $reissueInfo['attendance_date']);
        $shiftType = StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;
        if (!empty($shiftInfo) && $shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
            $shiftType = ceil($reissueInfo['attendance_type'] / 2);
        }
        //主播职位
        $transfer = HrStaffTransferModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and stat_date = :date_at:',
            'bind' => [
                'staff_id' => $reissueInfo['staff_info_id'],
                'date_at' => $reissueInfo['attendance_date'],
            ]
        ]);
        $liveJobId = (new SettingEnvServer())->getSetVal('free_shift_position');
        $this->liveJobId = empty($liveJobId) ? [] : explode(',', $liveJobId);
        if(!empty($transfer) && in_array($transfer->job_title, $this->liveJobId)){
            $shiftType = StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;
            $shiftInfo = [];
        }


        $startEndIndex = ($reissueInfo['attendance_type'] % 2);//1 上班 0 下班

        if (empty($cardInfo)) {
            $cardInfo = StaffWorkAttendanceModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and attendance_date = :date: and shift_type = :shift_type:',
                'bind'       => [
                    'staff_id'   => $reissueInfo['staff_info_id'],
                    'date'       => $reissueInfo['attendance_date'],
                    'shift_type' => $shiftType,
                ],
               // "for_update" => true,
            ]);
        }
        //来源状态
        $sourceState = StaffWorkAttendanceModel::STATE_MAKE_UP_CARD;
        if ($isTool === true) {
            $sourceState = StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY;
        }

        //补卡时间 转零时区
        $addHour  = $this->config->application->add_hour;
        $cardTime = date('Y-m-d H:i:s', strtotime($reissueInfo['reissue_card_date']) - $addHour * 3600);;

        $cardData['staff_id']        = $reissueInfo['staff_info_id'];
        $cardData['attendance_date'] = $reissueInfo['attendance_date'];
        $cardData['shift_type']      = $shiftType;
        $cardData['started_at']      = $cardData['end_at'] = NULL;
        //向FBI 生产 补卡消息-处罚
        $attendancePenaltyServer = new AttendancePenaltyServer($this->lang, $this->timezone);

        //更新操作 只更新时间
        if (!empty($cardInfo)) {
            //新增逻辑 如果有打卡记录 上班取最早 下班取最晚 https://flashexpress.feishu.cn/docx/LOindv49toEYBCxUrpecamUfnGQ
            $startOldTime = strtotime($cardInfo->started_at);
            $endOldTime   = strtotime($cardInfo->end_at);
            $newTime      = strtotime($cardTime);
            //判断 上下班
            if ($startEndIndex > 0 && (empty($cardInfo->started_at) || $newTime < $startOldTime)) {
                //上班补卡
                $cardInfo->started_at    = $cardTime;
                $cardInfo->started_state = $sourceState;
                $cardData['started_at']  = $cardTime;//补卡时间
            }
            if ($startEndIndex == 0 && (empty($cardInfo->end_at) || $newTime > $endOldTime)) {
                $cardInfo->end_at    = $cardTime;
                $cardInfo->end_state = $sourceState;
                $cardData['end_at']  = $cardTime;//补卡时间
            }
            $flag = $cardInfo->update();
            if($flag) {
                $attendancePenaltyServer->sendCardReplacementMsg($cardData);

                //请假补卡审批通过同步bi，重算处罚数据
                $sync_server = new SyncServer($this->lang, $this->timezone);
                $params = [
                    'staff_id'     => $reissueInfo['staff_info_id'],
                    'recheck_date' => $reissueInfo['attendance_date'],
                    'type'         => $reissueInfo['attendance_type'],
                ];
                $sync_server->sync_fbi_attendance($params, 'abnormal.staff_clock_in_reset');
            }

            $this->logger->write_log("reissue 回写 更新 {$reissueInfo['staff_info_id']} {$flag} {$startOldTime} {$endOldTime} {$newTime}", 'info');
            return $this->checkReturn([]);
        }

        $shiftStartName = 'first_start';
        $shiftEndName   = 'first_end';
        if ($shiftType == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
            $shiftStartName = 'second_start';
            $shiftEndName   = 'second_end';
        }

        $auditRe = new AuditRepository($this->lang);
        [$organization_id, $organization_type] = $auditRe->getOrganization($reissueInfo['staff_info_id'], $reissueInfo['attendance_date']);
        //work day
        $staffRe = new StaffRepository($this->lang);
        $staffInfo = $staffRe->getStaffPosition($reissueInfo['staff_info_id']);
        $insert['working_day'] = $staffRe->get_is_working_day($staffInfo,$reissueInfo['attendance_date']);

        $insert['staff_info_id']     = $reissueInfo['staff_info_id'];
        $insert['attendance_date']   = $reissueInfo['attendance_date'];
        $insert['organization_id']   = $organization_id;
        $insert['organization_type'] = $organization_type;
        $insert['shift_start']       = $shiftInfo[$shiftStartName] ?? '';
        $insert['shift_end']         = $shiftInfo[$shiftEndName] ?? '';
        $insert['shift_id']          = $shiftInfo['shift_id'] ?? 0;
        $insert['shift_ext_id']      = $shiftInfo['shift_extend_id'] ?? 0;
        $insert['shift_type']        = $shiftType;

        //判断 上下班
        if ($startEndIndex > 0) {
            //上班补卡
            $insert['started_at']    = $cardTime;
            $insert['started_state'] = $sourceState;

            $cardData['started_at']  = $cardTime;//补卡时间
        } else {
            $insert['end_at']    = $cardTime;
            $insert['end_state'] = $sourceState;

            $cardData['end_at']      = $cardTime;//补卡时间
        }
        $model = new StaffWorkAttendanceModel();
        $flag  = $model->create($insert);
        if($flag) {
            //请假补卡审批通过同步bi，重算处罚数据
            $sync_server = new SyncServer($this->lang, $this->timezone);
            $params = [
                'staff_id'     => $reissueInfo['staff_info_id'],
                'recheck_date' => $reissueInfo['attendance_date'],
                'type'         => $reissueInfo['attendance_type'],
            ];
            $sync_server->sync_fbi_attendance($params, 'abnormal.staff_clock_in_reset');
            $attendancePenaltyServer->sendCardReplacementMsg($cardData);
        }
        $this->logger->write_log("reissue 回写 新增 {$reissueInfo['staff_info_id']} {$flag} ", 'info');
        return $this->checkReturn([]);
    }


    /**
     * 原逻辑 的班次信息 拼接成新结构 兼容外协员工
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function getOldStaffShift($staff_id, $start_date, $end_date)
    {
        $step = $start_date;
        $today = date('Y-m-d');
        $historyDate = $futureDate = [];
        while ($step <= $end_date){
            if($step < $today){
                $historyDate[] = $step;
            }
            if($step >= $today){
                $futureDate[] = $step;
            }
            $step = date('Y-m-d',strtotime("{$step} +1 day"));
        }
        //获取 history  shift 之前 是 4。5 小时 新规则 要变成 240分钟 后面是 22小时-8  改成 780 分钟
        $historyShift = $futureShift = [];

        if(!empty($historyDate)){
            $historyShift = HrStaffShiftHistoryModel::find([
                'columns' => "staff_info_id,start as first_start,[end] as first_end, 
                 1 as shift_type,shift_id,0 as shift_extend_id,shift_day as shift_date,240 as first_earliest,
                 780 as first_latest,0 as first_late,0 as first_leave_early",
                'conditions' => 'staff_info_id = :staff_id: and shift_day in ({dates:array})',
                'bind' => ['staff_id' => $staff_id, 'dates' => $historyDate]
            ])->toArray();
        }

        if(!empty($futureDate)){
            $future = HrStaffShiftModel::findFirst([
                'columns' => 'staff_info_id,start as first_start,[end] as first_end, 
                1 as shift_type,shift_id,0 as shift_extend_id,240 as first_earliest,780 as first_latest,0 as first_late,0 as first_leave_early',
                'conditions' => 'staff_info_id = :staff_id:',
                'bind' => ['staff_id' => $staff_id]
            ]);

            if(!empty($future)){
                $future = $future->toArray();
                $futureShift = [];
                foreach ($futureDate as $d){
                    //把日期 放进去
                    $futureShift[] = array_merge($future,array('shift_date' => $d));
                }
            }
        }
        $shiftInfos = array_merge($historyShift,$futureShift);

        foreach ($shiftInfos as &$shiftInfo) {
            $real_early_out_day                      = $shiftInfo['first_start'] > $shiftInfo['first_end'] ? date('Y-m-d', strtotime($shiftInfo['shift_date'].' +1 day')) : $shiftInfo['shift_date'];
            //判断迟到的时间节点
            $shiftInfo['first_check_late_in_time']   = date('Y-m-d H:i:s', strtotime($shiftInfo['shift_date'].' '.$shiftInfo['first_start']) + $shiftInfo['first_late'] * 60);
            //判断早退的时间节点
            $shiftInfo['first_check_early_out_time'] = date('Y-m-d H:i:s', strtotime($real_early_out_day.' '.$shiftInfo['first_end']) - $shiftInfo['first_leave_early'] * 60);
            //判断旷工的时间节点
            $shift_end_time                          = $shiftInfo['first_start'] > $shiftInfo['first_end'] ? strtotime($shiftInfo['shift_date'].' '.$shiftInfo['first_end'].' +1 day') : strtotime($shiftInfo['shift_date'].' '.$shiftInfo['first_end']);
            //是否超过下班班次打卡截止时间
            $shiftInfo['first_check_no_record_time'] = date('Y-m-d H:i:s',$shift_end_time + $shiftInfo['first_latest'] * 60);
            //第一个班最早可打卡时间
            $shiftInfo['first_allow_attendance_time'] = date('Y-m-d H:i:s',strtotime($shiftInfo['shift_date'].' '. $shiftInfo['first_start'])-$shiftInfo['first_earliest']*60);
            $shiftInfo['is_middle_rest'] = 0;

        }
        return array_column($shiftInfos, null, 'shift_date');
    }

    //支援工号 没有班次 给默认班次 固定 ext id
    public function getDefaultShiftById($id,$date){

        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            's.shift_id',
            's.id as shift_extend_id',
            's.work_time',
            's.shift_type',
            's.first_earliest',
            's.first_start',
            's.first_end',
            's.first_latest',
            's.first_late',
            's.first_leave_early',
            's.second_earliest',//最早可提前打卡分钟
            's.second_start',
            's.second_end',
            's.second_latest',
            's.second_late',
            's.second_leave_early',
            's.middle_rest_start',
            's.middle_rest_end',
        ]);
        $builder->from(['m' => HrShiftV2Model::class]);
        $builder->innerJoin(HrShiftV2ExtendModel::class, 'm.id = s.shift_id', 's');
        $builder->where('m.id = :id:', ['id' => $id]);
        $shiftInfo = $builder->getQuery()->execute()->getFirst();

        if (empty($shiftInfo)) {
            throw new ValidationException('can not find default shift');
        }
        $shiftInfo = $shiftInfo->toArray();

        $shiftInfo['shift_date'] = $date;

        $real_early_out_day = $shiftInfo['first_start'] > $shiftInfo['first_end'] ? date('Y-m-d',
            strtotime($shiftInfo['shift_date'].' +1 day')) : $shiftInfo['shift_date'];
        //判断迟到的时间节点
        $shiftInfo['first_check_late_in_time'] = date('Y-m-d H:i:s',
            strtotime($shiftInfo['shift_date'].' '.$shiftInfo['first_start']) + $shiftInfo['first_late'] * 60);
        //判断早退的时间节点
        $shiftInfo['first_check_early_out_time'] = date('Y-m-d H:i:s',
            strtotime($real_early_out_day.' '.$shiftInfo['first_end']) - $shiftInfo['first_leave_early'] * 60);
        //判断旷工的时间节点
        $shift_end_time = $shiftInfo['first_start'] > $shiftInfo['first_end'] ? strtotime($shiftInfo['shift_date'].' '.$shiftInfo['first_end'].' +1 day') : strtotime($shiftInfo['shift_date'].' '.$shiftInfo['first_end']);
        //是否超过下班班次打卡截止时间
        $shiftInfo['first_check_no_record_time'] = date('Y-m-d H:i:s',
            $shift_end_time + $shiftInfo['first_latest'] * 60);
        //第一个班最早可打卡时间
        $shiftInfo['first_allow_attendance_time'] = date('Y-m-d H:i:s',
            strtotime($shiftInfo['shift_date'].' '.$shiftInfo['first_start']) - $shiftInfo['first_earliest'] * 60);

        return $shiftInfo;


    }


    //工具补卡 hcm 拼接 补卡信息 调用 saveReissueCard 方法
    public function toolReissueCard($param){
        $add_hour = $this->config->application->add_hour;
        $reissueInfo['staff_info_id'] = $param['staff_info_id'];
        $reissueInfo['attendance_date'] = $param['attendance_date'];
        $reissueInfo['attendance_type'] = $param['attendance_type'];//1 上班  2 下班 3 第二次上班 4 第二次下班
        $reissueInfo['reissue_card_date'] = date('Y-m-d H:i:s',strtotime($param['time']));
        //补卡时间 转换零时区
        $time = date('Y-m-d H:i:s', strtotime($param['time']) - $add_hour * 3600);
        $shiftInfo = $this->getStaffShiftInfoByDate($reissueInfo['staff_info_id'],$reissueInfo['attendance_date']);
        $shiftType = StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;
        if(!empty($shiftInfo) && $shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE){
            $shiftType = ceil($reissueInfo['attendance_type'] / 2);
        }
        if($shiftType == StaffWorkAttendanceModel::SHIFT_TYPE_ONLY && $param['attendance_type'] > 2){
            throw new ValidationException($this->getTranslation()->_('attendance_type_error'));
        }

        //新增 补卡时间不能大于当前时间
        if(strtotime($param['time']) > time()){
            throw new ValidationException($this->getTranslation()->_('1008'));
        }

        $cardInfo = StaffWorkAttendanceModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and attendance_date = :date: and shift_type = :shift_type:',
            'bind' => [
                'staff_id' => $reissueInfo['staff_info_id'],
                'date' => $reissueInfo['attendance_date'],
                'shift_type' => $shiftType,
            ]
        ]);

        if(!empty($cardInfo)){//判断 是否存在 打卡记录 以及 时间对不对
            if($reissueInfo['attendance_type'] == StaffWorkAttendanceModel::ATTENDANCE_ON){
                if(!empty($cardInfo->started_at)){
                    throw new ValidationException('have been exist');
                }
                if(!empty($cardInfo->end_at) && $cardInfo->end_at < $time){
                    throw new ValidationException('start_time must earlier than end_time');
                }
            }
            if($reissueInfo['attendance_type'] == StaffWorkAttendanceModel::ATTENDANCE_OFF){
                if(!empty($cardInfo->end_at)){
                    throw new ValidationException('have been exist');
                }
                if(!empty($cardInfo->started_at) && $cardInfo->started_at > $time){
                    throw new ValidationException('start_time must earlier than end_time');
                }
            }
        }
        //获取补卡记录
        $audit_model = new AuditRepository($this->lang);
        $apply_info = $audit_model->get_attendance_info($reissueInfo['staff_info_id'],$reissueInfo['attendance_date'],$reissueInfo['attendance_type']);
        if(!empty($apply_info)){
            throw new ValidationException('have been applied');
        }
        return $this->saveReissueCard($reissueInfo,$cardInfo,true);
    }

    /**
     * bi工具 修改考勤信息 马来定制
     * @param $param
     * @return array
     */
    public function edit_att($param)
    {
        $start_tmp = strtotime($param['start_time']);
        $end_tmp   = strtotime($param['end_time']);

        $started_at = gmdate("Y-m-d H:i:s", $start_tmp);
        $end_at     = gmdate("Y-m-d H:i:s", $end_tmp);

        $staff_id = intval($param['staff_info_id']);
        $date     = $param['attendance_date'];

        //更新数据库
        $info = StaffWorkAttendanceModel::findFirst(intval($param['id']));
        if (empty($info)) {
            return $this->checkReturn(-3, 'no attendance info');
        }

        if (empty($info->started_at) || empty($info->end_at)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('start or end time is null'));
        }

        //新需求 可以修改 对应打卡信息的 班次 shift id 为 hr_shift_v2_extend 主键
        if (!empty($param['shift_id']) && $param['shift_id'] > 0) {
            //原班次信息如果 类型不一样不能改
            $oldShiftInfo = HrShiftV2ExtendModel::findFirst($info->shift_ext_id);

            //更新  打卡表 数据
            $shiftExtInfo = HrShiftV2ExtendModel::findFirst($param['shift_id']);

            if($oldShiftInfo->shift_type != $shiftExtInfo->shift_type){
                return $this->checkReturn(-3, $this->getTranslation()->_('wrong_shift_type_notice'));
            }

            $info->shift_start  = empty($shiftExtInfo) ? '' : $shiftExtInfo->first_start;
            $info->shift_end    = empty($shiftExtInfo) ? '' : $shiftExtInfo->first_end;
            $info->shift_id     = empty($shiftExtInfo) ? '' : $shiftExtInfo->shift_id;
            $info->shift_ext_id = empty($shiftExtInfo) ? '' : intval($param['shift_id']);
            //如果修改的 打卡记录 是第二次的 取 第二次 打卡班次
            if ($info->shift_type == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
                $info->shift_start = empty($shiftExtInfo) ? '' : $shiftExtInfo->second_start;
                $info->shift_end   = empty($shiftExtInfo) ? '' : $shiftExtInfo->second_end;
            }

            //同时 更新  hr_staff_shift_v2 表
            $history_shift = HrStaffShiftV2Model::findFirst([
                'conditions' => "staff_info_id = :staff_id: and shift_date = :date:",
                'bind'       => [
                    'staff_id' => $staff_id,
                    'date'     => $date,
                ],
            ]);
            if (!empty($history_shift)) {
                $old                            = $history_shift->toArray();
                $history_shift->shift_id        = empty($shiftExtInfo) ? 0 : $shiftExtInfo->shift_id;
                $history_shift->shift_extend_id = empty($shiftExtInfo) ? 0 : intval($param['shift_id']);
                $history_shift->update();
                $this->getDI()->get('logger')->write_log("hcm_edit_shift_{$staff_id} ".json_encode($old).json_encode($param),
                    'info');
            }
        }

        $info->started_at     = $started_at;
        $info->end_at         = $end_at;
        $info->started_state  = StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY;
        $info->started_remark = 'bi_system';
        $info->end_state      = StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY;
        $info->end_remark     = 'bi_system';
        $flag                 = $info->update();

        //记录日志
        $this->getDI()->get('logger')->write_log("hcm_edit_att_{$staff_id} ".json_encode($param).json_encode($info),
            'info');
        if (!$flag) {
            return $this->checkReturn(-3, 'failed');
        }

        $cardData['staff_id']        = $staff_id;
        $cardData['attendance_date'] = $date;
        $cardData['shift_type']      = $info->shift_type;
        $cardData['started_at']      = $started_at;
        $cardData['end_at']          = $end_at;
        //向FBI 生产 补卡消息-处罚
        $attendancePenaltyServer = new AttendancePenaltyServer($this->lang, $this->timezone);
        $attendancePenaltyServer->sendCardReplacementMsg($cardData);

        return $this->checkReturn(1);
    }

    /**
     * Notes: 缺卡、迟到早退提醒
     * @param array $param
     * @return array
     * @throws BusinessException
     */
    public function malformed(array $param): array
    {

        $result             = [
            // 补卡次数
            'reissue_card_count'           => 0,
            'attendance_start_dates'       => [],
            'attendance_end_dates'         => [],
            'no_record_info'               =>'',
            'is_need_reissue'              => 0,//客户端没用到
            // 是否提醒迟到早退
            'have_late_early_alert'        => false,
            // 迟到早退提醒的内容
            'late_early_alert_msg'         => '',
            // 迟到早退提醒弹窗是否展示跳转进入出勤记录页的按钮
            'late_early_alert_view_detail' => false,
            ''
        ];

        //一个班次的 不是下班  ;两个班次的 不是第二个下班 都不验证
        if(($param['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE &&  $param['shift_index'] != enums::ATTENDANCE_SHIFT_INDEX_1) || ($param['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE &&  $param['shift_index'] != enums::ATTENDANCE_SHIFT_INDEX_1)){
            return $result;
        }

        // 考勤日期当天
        $attendanceDate = empty($param['attendance_date']) ? date('Y-m-d') : $param['attendance_date'];
        // 考勤日期的昨天
        $yesterdayAttendanceDate = date('Y-m-d', strtotime($attendanceDate.' -1 days'));
        // 考勤日期的前天
        $beforeYesterdayAttendanceDate = date('Y-m-d', strtotime($attendanceDate.' -2 days'));

        $staffId = $param['user_info']['staff_id'];
        //查用户信息 看是不是 无底薪员工 要改翻译
        $staffInfo = (new StaffRepository($this->lang))->getStaffPosition($staffId);

        if ($staffInfo && $staffInfo['is_sub_staff'] == 1) {
            return $result;
        }

        $suffix = '';
        if(in_array($staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
            $suffix = '_unpaid';
        }
        //  老版本app只有上班打完提醒缺卡
        $attendanceCategory = empty($param['attendance_category']) ? AttendanceCalendarEnums::GO_WORK_MAKE_UP_CARD : $param['attendance_category'];

        /************************** 缺卡提醒 ************************/
        // 上班卡才有缺卡提醒
        if ($attendanceCategory == AttendanceCalendarEnums::GO_WORK_MAKE_UP_CARD) {
            $punchMonthCardNum            = (new AuditRepository($this->lang, null))->getAttendanceCarMonthData([
                'staff_id'          => $staffId,
                'reissue_card_date' => date('Y-m-d', time()),
            ]);
            $result['reissue_card_count'] = $punchMonthCardNum > 3 ? 0 : 3 - $punchMonthCardNum;

            $notice = [];
            $server = new AttendanceCalendarV2Server($this->lang,$this->timezone);
            $attendanceData = $server->initData($staffId, $beforeYesterdayAttendanceDate,$yesterdayAttendanceDate)->handleDailyData();
            $whiteListServer = new WhiteListServer();
            $whiteList = $whiteListServer->staffValidDateRange($staffId);
            foreach ($attendanceData as $attendance_date => $attendanceDatum) {
                if ($whiteListServer->isInclude($whiteList,$attendance_date)) {
                    continue;
                }
                foreach ($attendanceDatum['attendance_info'] as $item) {
                    if(isset($item['state']) && $item['state'] ==  AttendanceCalendarV2Server::STATE_NO_RECORD){
                        switch ($item['attendance_order']) {
                            case AttendanceCalendarV2Server::ONCE_SHIFT_IN:
                                $notice['abnormal_once_shift_in' . $suffix][] = $attendance_date;
                                break;
                            case AttendanceCalendarV2Server::ONCE_SHIFT_OUT:
                                $notice['abnormal_once_shift_out' . $suffix][] = $attendance_date;
                                break;
                            case AttendanceCalendarV2Server::TWICE_SHIFT_FIRST_IN:
                                $notice['abnormal_twice_shift_first_in' . $suffix][] = $attendance_date;
                                break;
                            case AttendanceCalendarV2Server::TWICE_SHIFT_FIRST_OUT:
                                $notice['abnormal_twice_shift_first_out' . $suffix][] = $attendance_date;
                                break;
                            case AttendanceCalendarV2Server::TWICE_SHIFT_SECOND_IN:
                                $notice['abnormal_twice_shift_second_in' . $suffix][] = $attendance_date;
                                break;
                            case AttendanceCalendarV2Server::TWICE_SHIFT_SECOND_OUT:
                                $notice['abnormal_twice_shift_second_out' . $suffix][] = $attendance_date;
                                break;
                            default:
                                break;
                        }
                    }
                }
            }

            $t =  $this->getTranslation();
            if(!empty($notice)){
                $str = [];
                foreach ($notice as $k => $item) {
                    $str[] = $t->_($k) ."\n".implode("\n",$item);
                }
                $result['no_record_info'] = implode("\n",$str);
            }
        }

        return $result;
    }

    /**
     * 外协员工 外勤权限；
     * @param $staff_info
     * @return bool
     */
    public function outsourceStaffPunchField($staff_info): bool
    {
        $whiteList = (new SettingEnvServer())->getSetVal('free_punch_white_list');
        $whiteListArr = !empty($whiteList) ? explode(',', $whiteList) : [];

        return in_array($staff_info, $whiteListArr);
    }

    public function getLeaveInfo($staffInfoId, $startDate, $endDate){
        //3天的 请假信息 只要 审核通过
        $auditRe                        = new AuditRepository($this->lang);
        $leaveParam['staff_id']         = $staffInfoId;
        $leaveParam['leave_start_time'] = $startDate;
        $leaveParam['leave_end_time']   = $endDate;
        $leaveInfo                      = $auditRe->getLeaveData($leaveParam, enums::APPROVAL_STATUS_APPROVAL);
        $leaveInfo                      = empty($leaveInfo) ? [] : array_column($leaveInfo, 'sum_type', 'date_at');
        return $leaveInfo;
    }


}
