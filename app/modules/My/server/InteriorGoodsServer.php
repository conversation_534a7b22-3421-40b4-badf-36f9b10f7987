<?php
namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Enums\InteriorOrderFundStatusEnums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HireTypeImportListModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\InteriorGoodsRepository;
use FlashExpress\bi\App\Server\FlashPayServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\CsrfTokenServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SyncWarehoseServer;
use app\enums\LangEnums;
use FlashExpress\bi\App\Enums\InteriorGoodsEnums;
use FlashExpress\bi\App\Modules\My\library\Enums\InteriorGoodsEnums as MyInteriorGoodsEnums;
use FlashExpress\bi\App\Enums\InteriorGoodsPayMethodEnums;
use FlashExpress\bi\App\Enums\InteriorGoodsStatusEnums;
use FlashExpress\bi\App\Enums\InteriorOrderAuditedEnums;
use FlashExpress\bi\App\Enums\InteriorOrderStatusEnums;
use FlashExpress\bi\App\Enums\ReturnMsgEnums;
use FlashExpress\bi\App\Models\backyard\HeadquartersAddressModel;
use FlashExpress\bi\App\Models\backyard\InteriorDepartmentModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsSkuLogModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsSkuModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersGoodsSkuModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersModel;
use FlashExpress\bi\App\Models\backyard\InteriorStaffsShoppingCartModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Models\backyard\SysDistrictModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\oa\SysDepartmentPcCode;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Models\backyard\SysStoreGoodsModel;
use FlashExpress\bi\App\Server\InteriorGoodsServer as GlobalBaseServer;


class InteriorGoodsServer extends GlobalBaseServer
{
    const IS_FREE = 1;//免费
    const IS_NO_FREE = 0;
    const FREE_COUNT_NUB= 1; //限免个数

    private static $hire_limit_days = 7;
    /**
     * InteriorGoodsServer constructor.
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone='+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * 所有工作日满7天的员工，第8天开始可以有购买权限。
     * @param array $loginUser
     * @return bool
     */
    public function getStaffBuyAuth(array $loginUser)
    {
        //TODO 如果是马来国家次校验永远为真 校验放在了提交订单接口里面  原因是前端人力不足
        return true;
        $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($loginUser['staff_id'], 'state,hire_date');
        if (!$userInfo || $userInfo['state'] != 1 || !$userInfo['hire_date']) {
            return false;
        }
        $nowDate  = date('Y-m-d H:i:s');
        $nowTime  = strtotime($nowDate);
        $hireTime = strtotime($userInfo->hire_date);
        $days     = ($nowTime - $hireTime) / 86400;
        $hireDays = sprintf("%.2f", $days);
        if ($hireDays <= self::$hire_limit_days) {
            return false;
        }
        return true;
    }


    /**
     * 所有工作日满7天的员工，第8天开始可以有购买权限。
     * @param array $loginUser
     * @return bool
     * 'free_goods_can_only' => '免费的商品仅可单独购买。',
     * 'employees_who_have_worked' => '入职7天内的员工，仅可免费购买1件工服。'
     * 'only_employees_who_have' => '只有工作满7天的员工才可以购买哦'
     * 'sale_and_non_sale_not_allowed' => '借用商品不允许与购买商品一起提交！'
     * 'this_purchase_of_free_goods' => '本次购买的免费商品超过额度，不可购买。'
     * 'each_employee_can_only' => '每人每月仅可购买5件工服。'
     */
    public function getStaffBuyAuthNew($rs)
    {
        $loginUser=$rs['loginUser'];
        $goodsIdsArr=$rs['goodsIdsArr'];//提交的数据
        $freeBuyNum=$rs['freeBuyNum'];//本次购买总数
        $has_buy_free_num = $rs['has_buy_free_num'];
        $goodsIds=$rs['goodsIds'];//本费不免费的数据
        $free_goods_ids=$rs['free_goods_ids'];//免费goods_ids


        $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($loginUser['staff_id'], 'state,hire_date,node_department_id,sys_store_id,formal');
        if (!$userInfo || $userInfo['state'] != 1 || !$userInfo['hire_date']) {
            return ['msg' => 'The account is deactivated!'];
        }
        $nowDate = date('Y-m-d H:i:s');
        $nowTime = strtotime($nowDate);
        $hireTime = strtotime($userInfo->hire_date);
        $days = ($nowTime - $hireTime) / 86400;
        $hireDays = sprintf("%.2f", $days);

        $is_free_arr = array_column($goodsIdsArr, 'is_free');
        //不全是免费购买的商品，则无法下订单
        if (in_array(self::IS_NO_FREE, $is_free_arr) && in_array(self::IS_FREE, $is_free_arr)) {
            return ['msg' => $this->getTranslation()->_('free_goods_can_only')];
        }

        //借用商品不允许与购买商品一起提交！
        $interior_non_sale_goods_ids = (new SettingEnvServer())->getSetVal('interior_non_sale_goods_id', ',');
        $non_sale_goods = [];//借用商品组
        $sale_goods = [];//购买商品组
        foreach ($goodsIds as $goods_id) {
            if (in_array($goods_id, $interior_non_sale_goods_ids)) {
                $non_sale_goods[] = $goods_id;
            } else {
                $sale_goods[] = $goods_id;
            }
        }
        //同时存在拦截
        if ($non_sale_goods && $sale_goods) {
            return ['msg' => $this->getTranslation()->_('sale_and_non_sale_not_allowed')];
        }

        //全是免费的商品
        if (in_array(self::IS_FREE, $is_free_arr) && !in_array(self::IS_NO_FREE, $is_free_arr) && $freeBuyNum) {
            foreach ($goodsIds as $goods_id) {
                if (!in_array($goods_id, $free_goods_ids)) {
                    //本次提交的商品不在限免的商品组里
                    return ['msg' => $this->getTranslation()->_('this_purchase_of_free_goods')];
                }
            }
            //获取可购买免费工服的件数
            $free_buy_limit = $this->getCanFreeBuyGoodsNums($userInfo);
            if ($free_buy_limit) {
                //免费工服件数设置存在,判断已购买免费工服数+本次购买件数，超过设置的可购买工服件数
                $total_buy_num = bcadd($freeBuyNum, $has_buy_free_num);
                if ($total_buy_num > $free_buy_limit) {
                    return ['msg' => $this->getTranslation()->_('this_purchase_of_free_goods')];
                }
                //获取可购买免费工服的入职天数
                self::$hire_limit_days = $this->getCanFreeBuyGoodsEntryDays($userInfo);
                if ($hireDays <= self::$hire_limit_days && $total_buy_num > self::FREE_COUNT_NUB) {
                    //未超过配置天数内，购买的工服超过1件，则要给予提示
                    return ['msg' => $this->getTranslation()->_('employees_who_have_worked', ['day' => self::$hire_limit_days])];
                }
            } else {
                //没有免费工服件数设置
                return ['msg' => $this->getTranslation()->_('this_purchase_of_free_goods')];
            }
        }
        return [];
    }

    /**
     * 获取员工可买商品类别
     * @param array $userInfo
     * @return array|string[]
     */
    public static function getUserEnableBuyGoodsCate(array $userInfo)
    {
        $storeCateCode = HrStaffInfoServer::getStaffStoreCateCode($userInfo);
        (new BaseServer())->wLog('StoreCateCode', $storeCateCode);

        if(isset($storeCateCode['msg'])){
            return $storeCateCode;
        }
        $goods_ids =  (new InteriorGoodsRepository())->getInteriorGoodsStoreCateRelAll($storeCateCode);

        (new BaseServer())->wLog('log_goods_ids:', $goods_ids);
        return $goods_ids;

    }

    /**
     *确认提交订单
     * Created by: Lqz.
     * @param array $loginUser
     * @param array $params
     * @return array|string[]
     * CreateTime: 2020/8/10 0010 14:47
     */
    public function submitOrder(array $loginUser, array $params)
    {
        $this->getDI()->get('logger')->write_log("InteriorOrder=submit_order==record" . json_encode($loginUser, JSON_UNESCAPED_UNICODE) . 'time' . time() . '; submit parmas => ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        //开启事务
        $db = InteriorOrdersModel::beginTransaction($this);
        try {
            $orderRemark       = '';
            $goodsIdsArr       = $params['buy_goods_ids_arr'];
            $staffId           = $loginUser['staff_id'];
            $provinceCode      = $params['province_code'];
            $cityCode          = $params['city_code'];
            $districtCode      = $params['district_code'];
            $address           = $params['detail_address'];
            $postalCode        = $params['postal_code'];
            $platform          = $params['platform'] ?? '';
            $storeId           = $params['staff_store_id']; // 前端传网点编号或总部id收货地址ID
            $userInfo          = HrStaffInfoServer::getUserInfoByStaffInfoId($staffId);
            $userInfo          = $userInfo->toArray();
            $this->getDI()->get('logger')->write_log('submitOrder userInfo parmas => ' . json_encode($userInfo), 'info');
            $provinceArr = SysProvinceModel::getProvincesArrByCodeArr([$provinceCode], 'code,name');
            $cityArr     = SysCityModel::getCitiesArrByCodeArr([$cityCode], 'code,name');
            $districtArr = SysDistrictModel::getDistrictsArrByCodeArr([$districtCode], 'code,name');
            $province    = trim($provinceArr[$provinceCode]['name'] ?? '');
            $city        = trim($cityArr[$cityCode]['name'] ?? '');
            $district    = trim($districtArr[$districtCode]['name'] ?? '');

            if (!$userInfo['mobile']) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_mobile_not_null'));
            }
            if (!$province) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_province_not_null'));
            }
            if (!$city) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_city_not_null'));
            }
            if (!$district) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_district_not_null'));
            }
            if (!$address) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_address_not_null'));
            }
            if (!$postalCode) {
                throw new ValidationException($this->getTranslation()->_('interior_goods_postalcode_not_null'));
            }

            $this->authorityCheck($userInfo, $params);
            //如果是购物车提交的订单 删除购物对应的订单 ，1 表示购物车过来 0表示直接下单
            if ($params['source'] == 1) {
                $this->delShoppingCart($userInfo, $params);
            }

            //工服和无头件不可以混合提交 & 获取提交的订单商品类型
            $goods_type = $this->unclaimedValidation($params);

            //出库单接口中nodeSn传参逻辑
            $nodeSn = $this->getNodeSn($goods_type, $userInfo, $storeId);

            //一个订单支付方式只能有一种
            $new_goods_stock = [];
            //如果是无头件时时查询库存
            if ($goods_type == InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
                $sku_code_str    = implode(',', array_column($params['buy_goods_ids_arr'], 'goods_sku_code'));
                $new_goods_stock = $this->getGoodsBarcodeStock($sku_code_str, $goods_type);
                if (empty($new_goods_stock)) {
                    throw new ValidationException($this->getTranslation()->_('interior_goods_available_inventory'));
                }
            }

            /**
             * 以下逻辑来自于11040【BY】马来员工商城免费购买规则优化需求
             * https://l8bx01gcjr.feishu.cn/docs/doccnI0wdFtDlX80eSuVu3DM9ih
             *  1.如果不全是免费购买的商品，则无法下订单，提示toast：免费的商品仅可单独购买。
             * 若当前员工没有满7天（≤7）
             *  2.如果全是免费购买的商品，则需要校验每个商品的免费额度，提示toast：入职7天内的员工，仅可免费购买1件工服。
             * 若当前员工入职满7天（＞7）
             *  2.针对免费购买的商品，则需要校验每类商品的免费额度，提示toast：本次购买的免费商品超过额度，不可购买。
             *
             * 【重点】在10630【MY-BY|员工商城】 马来员工商城对接FlashPay需求中以下代码逻辑已被删除
             *  3.针对正常价格的商品，则需要校验商品的限购额度，提示toast：每人每月仅可购买5件工服。
             *  4.如果入职未满7天购买是非免费的商品，则提示"只有工作满7天的员工才可以购买哦"
             */
            if ($goods_type == InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES) {
                $goodsIds         = array_column($goodsIdsArr, 'goods_id');
                $has_buy_free_num = $this->getUserFreeByNumNew($loginUser);
                $goodsList        = InteriorGoodsModel::find([
                    'conditions' => 'id in({goods_id:array}) and free_num >0',
                    'bind'       => ['goods_id' => $goodsIds],
                    'columns'    => ['id', 'free_num']

                ]);
                if (empty($goodsList)) {
                    throw new ValidationException($this->getTranslation()->_('interior_goods_data_not_null'));
                }
                $goodsList      = $goodsList->toArray();
                $free_goods_ids = array_column($goodsList, 'id');

                $myDataArr = [
                    'loginUser'        => $loginUser,
                    'goodsIdsArr'      => $goodsIdsArr,//提交的数据
                    'freeBuyNum'       => array_sum(array_column($goodsIdsArr, 'buy_num')),//本次购买总数
                    'has_buy_free_num' => $has_buy_free_num,//已经购买免费数
                    'goodsIds'         => $goodsIds,//本费不免费的数据
                    'free_goods_ids'   => $free_goods_ids //限免goods_id
                ];
                $res_rs    = $this->getStaffBuyAuthNew($myDataArr);
                if (isset($res_rs['msg'])) {
                    throw new ValidationException($res_rs['msg']);
                }
            }

            // 多个商品生成一个订单对应多个商品
            $orderCode = $this->generateOrderCode();
            $currentTime = date('Y-m-d H:i:s');
            $total_num = 0; //购买总数
            $totalPayAmount = 0;  //订单总金额
            $syncWmsPostData = $goods = $goods_flash_pay = [];
            //用于区分此次免费与非免费提交（0非免费，>0免费）
            $is_free_order_goods_num = 0;
            $_thisByNum = 0;

            $this->getDI()->get('logger')->write_log("InteriorOrder=submit_order=node_sn_2" . $orderCode . '_' . $nodeSn . 'time' . time(), 'info');

            foreach ($goodsIdsArr as $k => $reqItems) {
                $goodsId = $reqItems['goods_id'];
                $skuId   = $reqItems['goods_sku_id'];
                $buyNum  = $reqItems['buy_num'];
                $barcode = $reqItems['goods_sku_code'];
                if ($buyNum <= 0) {
                    continue;
                }
                $goodsSkuObj = $this->getGoodsSkuInfo($goodsId, $skuId, true);
                if (!$goodsSkuObj) {
                    throw new ValidationException(LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_goods_was_not_found));
                }
                $price = $goodsSkuObj->price;
                // 计算订单总金额
                if (isset($reqItems['is_free']) && self::IS_FREE == $reqItems['is_free'] && $goods_type == InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES) {
                    $price = 0;
                }
                $amount         = $price * $buyNum;
                $totalPayAmount += $amount;

                if ($goods_type == InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES) {
                    // 修改后库存
                    $toSurplusNum = $goodsSkuObj->surplus_num - $buyNum;
                    if ($toSurplusNum < 0) {
                        throw new ValidationException(LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_inventory_not_enough));
                    }

                    $goodsSkuObj->sale_num += $buyNum;

                    // 记录库存修改日志
                    self::saveSkuSurplusNumAndLog($goodsSkuObj, $staffId, $buyNum, $toSurplusNum, 'staff_buy');
                } elseif ($goods_type == InteriorGoodsEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
                    //无头件校验每个库存是否充足
                    if ($new_goods_stock[$barcode] < $buyNum) {
                        throw new ValidationException($this->getTranslation()->_('interior_goods_available_inventory'));
                    }
                }

                if (self::IS_NO_FREE == $reqItems['is_free']) {
                    $_thisByNum += $buyNum;
                }

                //加入订单商品表
                $orderGoodsSkuObj     = new InteriorOrdersGoodsSkuModel();
                $order_goods_sku_data = [];

                $total_num += $buyNum;
                if (isset($reqItems['is_free']) && self::IS_FREE == $reqItems['is_free']) {
                    $order_goods_sku_data['is_free'] = 1;
                    //如果存在免费商品
                    $is_free_order_goods_num++;
                }
                $order_goods_sku_data['order_code']           = $orderCode;
                $order_goods_sku_data['goods_id']             = $goodsSkuObj->goods_id;
                $order_goods_sku_data['goods_sku_id']         = $goodsSkuObj->id;
                $order_goods_sku_data['goods_sku_code']       = $goodsSkuObj->goods_sku_code;
                $order_goods_sku_data['goods_name_en']        = $goodsSkuObj->goods_name_en;
                $order_goods_sku_data['goods_name_th']        = $goodsSkuObj->goods_name_th;
                $order_goods_sku_data['goods_name_zh']        = $goodsSkuObj->goods_name_zh;
                $order_goods_sku_data['img_path']             = $goodsSkuObj->img_path;
                $order_goods_sku_data['attr_1']               = $goodsSkuObj->attr_1;
                $order_goods_sku_data['attr_2']               = $goodsSkuObj->attr_2;
                $order_goods_sku_data['unit_en']              = $goodsSkuObj->unit_en;
                $order_goods_sku_data['unit_th']              = $goodsSkuObj->unit_th;
                $order_goods_sku_data['unit_zh']              = $goodsSkuObj->unit_zh;
                $order_goods_sku_data['unit_num']             = $goodsSkuObj->unit_num;
                $order_goods_sku_data['buy_price']            = $price;
                $order_goods_sku_data['buy_num']              = $buyNum;
                $order_goods_sku_data['pay_amount']           = $amount;
                $order_goods_sku_data['current_sku_pre_sale'] = 0;
                $success                                      = $db->insertAsDict(
                    $orderGoodsSkuObj->getSource(),
                    $order_goods_sku_data
                );
                if ($success === false) {
                    throw new BusinessException('订单提交 - interior_orders_goods_sku 写入失败, data = ' . json_encode($order_goods_sku_data, JSON_UNESCAPED_UNICODE), ErrCode::DATABASE_ERROR);
                }

                //用于向scm出库传递的参数
                $goods[] = [
                    'i'             => $k,
                    'barCode'       => $goodsSkuObj->goods_sku_code,
                    'goodsName'     => $goodsSkuObj->goods_name_en,
                    'specification' => $goodsSkuObj->attr_1,
                    'num'           => $buyNum,
                    'price'         => ($price * 100),//出库商品json，其中price的单位为萨当，即1泰铢=100萨当
                    'remark'        => $amount
                ];
                //用于向FlashPay在线支付传递的参数
                $goods_flash_pay[] = [
                    'goodsId'   => $goodsSkuObj->goods_sku_code,//商品的barcode
                    'goodsName' => $goodsSkuObj->goods_name_en,//商品的名称
                    'quantity'  => intval($buyNum),//商品数量
                    'price'     => intval($price * 100),//商品的单价*100
                ];
            }

            // 生成订单数据,付费的订单状态默认为待付款，支付方式，免费的订单状态为待发货
            if ($is_free_order_goods_num > 0) {
                //免费商品的订单，状态为待发货;支付方式为工资抵扣;订单类型为免费
                $orderStatus     = InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE;
                $payMethod       = InteriorGoodsPayMethodEnums::PAY_METHOD_DEDUCTION_OF_WAGES_CODE;
                $orderType       = InteriorOrderStatusEnums::ORDER_TYPE_FREE;
                $order_expire_at = null;
            } else {
                //非免费商品的订单，状态为待付款;支付方式为FlashPay在线支付;订单类型为自费
                $orderStatus     = InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE;
                $payMethod       = InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE;
                $orderType       = InteriorOrderStatusEnums::ORDER_TYPE_OWN_PAY;
                $order_expire_at = $this->getOrderExpireTime($currentTime);
            }

            if ($this->checkStaffIsFirstOrder($staffId) && $goodsIdsArr[0]['goods_type'] ==  InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES) {
                $syncWmsPostData['markName'] = 'FirstOrder';
            }

            $add_hour   = $this->getDI()['config']['application']['add_hour'];
            $nowDate    = gmdate('Y-m-d H:i:s', time() + $add_hour * 3600);
            $orderObj   = new InteriorOrdersModel();
            $order_data = [
                'staff_id'              => $staffId,
                'node_department_id'    => $userInfo['node_department_id'] ?: $userInfo['sys_department_id'],//子部门ID
                'staff_name'            => $userInfo['name'],
                'staff_mobile'          => $userInfo['mobile'],
                'staff_store_id'        => $userInfo['sys_store_id'], //员工属性的总部ID或者所属网点ID
                'receive_store_id'      => $storeId,//员工下单时提交过来的收货地址ID（总部地址ID或网点ID）
                'receive_province_name' => $province,
                'receive_city_name'     => $city,
                'receive_district_name' => $district,
                'receive_address'       => $address,
                'receive_postal_code'   => $postalCode,
                'order_code'            => $orderCode,
                'pay_amount'            => $totalPayAmount,
                'pay_method'            => $payMethod,
                'submit_at'             => $currentTime,
                'order_expire_at'       => $order_expire_at,
                'order_status'          => $orderStatus,
                'order_type'            => $orderType,
                'delivery_way'          => 'express',
                'remark'                => "GF_order：【staff_id：{$staffId}】" . $orderRemark,
                'created_at'            => $nowDate,
                'updated_at'            => $nowDate,
                'node_sn'               => $nodeSn,
                'out_status'            => 0,
                'goods_type'             => $goods_type,
                'order_source'           => !empty($params['source']) ? $params['source'] : 1,
                'mach_code'              => env('wms_mchId'),
            ];
            $success    = $db->insertAsDict(
                $orderObj->getSource(),
                $order_data
            );
            if ($success === false) {
                throw new BusinessException('订单提交 - interior_orders 写入失败, data = ' . json_encode($order_data, JSON_UNESCAPED_UNICODE), ErrCode::DATABASE_ERROR);
            }

            $orderObj->id = $db->lastInsertId();

            //由于框架底层并不会把数据库默认字段在不赋值的情况下置为默认值而是置为null就会导致数据更新报错故而有查询一下
            $_orderObj = InteriorOrdersModel::findFirst([
                'conditions' => 'id = ' . $orderObj->id
            ]);
            if ($orderType == InteriorOrderStatusEnums::ORDER_TYPE_FREE) {
                //免费的订单提交,直接向wms同步出库单
                $syncWmsPostData['nodeSn']             = $nodeSn;
                $syncWmsPostData['consigneeName']      = $userInfo['name'];
                $syncWmsPostData['consigneePhone']     = $userInfo['mobile'];
                $syncWmsPostData['province']           = $province;
                $syncWmsPostData['city']               = $city;
                $syncWmsPostData['district']           = $district;
                $syncWmsPostData['postalCode']         = $postalCode;
                $syncWmsPostData['consigneeAddress']   = $address;
                $syncWmsPostData['orderSn']            = $orderCode;
                $syncWmsPostData['node_department_id'] = $orderObj->node_department_id;
                $syncWmsPostData['deliveryWay']        = 'express';
                $syncWmsPostData['goods']              = json_encode($goods, JSON_UNESCAPED_UNICODE);
                $syncWmsPostData['remark']             = "GF_order：【staff_id：{$staffId}】" . $orderRemark;
                $syncWmsPostData['lang']               = $this->lang;

                $syncWmsPostData['warehouseId'] = $this->getGoodsTypeStockId($goods_type);
                if (!$syncWmsPostData['warehouseId']) {
                    throw new ValidationException($this->getTranslation()->_('interior_goods_stock_unset'));
                }

                // 向wms同步出库订单
                $syncWarehoseServer = new SyncWarehoseServer();
                $res                = $syncWarehoseServer->syncAddOrderToWmsReturnWarehouseAdd($syncWmsPostData);
                if (empty($res) || $res['code'] != 1 || !$res['data']) {
                    $_orderObj->fail_num   = 1;
                    $_orderObj->out_status = 2; //出库单失败，但有可能是超时失败，后续重试
                    $_orderObj->fail_reason = $res['msg'] ?? '';
                } else {
                    $_orderObj->out_status = 1;//出库成功
                    $_orderObj->out_sn     = $res['data'];
                    $_orderObj->remark     = $_orderObj->remark . "【wms_out_sn：{$res['data']}】";
                }
                $_orderObj->is_audited   = $_orderObj->is_audited ?? 0;
                $_orderObj->audited_desc = $_orderObj->audited_desc ?? '';
                $_orderObj->out_sn       = $_orderObj->out_sn ?? '';
                $_orderObj->save();
            } else {
                //自费的订单提交需要走FlashPay在线支付调取收银台接口，在支付成功的情况下才向wms同步出库单并将订单状态变更为待发货
                $flash_pay_method = InteriorOrderStatusEnums::FLASH_PAY_METHOD_H5;

                $create_order_param = [
                    'staff_id'         => $staffId,
                    'order_code'       => $orderCode,
                    'order_submit_at'  => $currentTime,
                    'order_expire_at'  => $order_expire_at,
                    'order_pay_amount' => $totalPayAmount,
                    'goods_flash_pay'  => $goods_flash_pay,
                    'platform'         => $platform,
                    'goods_type'       => $goods_type,
                    'mach_code'        => $_orderObj->mach_code,
                ];
                $flash_pay_create_order_result = $this->getFlashPayCreateOrderResult($create_order_param,
                    InteriorOrderStatusEnums::FLASH_PAY_METHOD_H5);
                if ($flash_pay_create_order_result['code'] == 0) {
                    $flash_pay_data = $flash_pay_create_order_result['data'];
                    //记录pay交易号
                    $_orderObj->flash_pay_code = !empty($flash_pay_data['tradeNo']) ? $flash_pay_data['tradeNo'] : '';
                    $_orderObj->save();
                } else {
                    throw new ValidationException($flash_pay_create_order_result['message'], $flash_pay_create_order_result['code']);
                }
            }

            $db->commit();

            $payMethodTxt = InteriorGoodsPayMethodEnums::getCodeTxtMap();
            return [
                'orde_code'            => $orderCode,
                'total_pay_amount'     => $totalPayAmount,
                'total_pay_amount_fmt' => InteriorGoodsEnums::fmtAmount($totalPayAmount, $this->lang),
                'pay_method'           => $payMethod,
                'pay_method_txt'       => $payMethodTxt[$payMethod],
                'order_created_at'     => $currentTime,
                'flash_pay_method'     => $flash_pay_method ?? '',
                'flash_pay_data'       => $flash_pay_data ?? []
            ];
        } catch (ValidationException $e) {
            $db->rollback();
            $this->wLog('submitOrder', $e->getFile()
                . 'code' . $e->getCode()
                . 'line' . $e->getLine()
                . 'message' . $e->getMessage()
                . 'trace' . $e->getTraceAsString());
            return [
                'msg' => $e->getMessage()//flash pay返回值会有翻译
            ];
        } catch (BusinessException $e) {
            $db->rollback();
            $error_code = $e->getCode();

            if ($error_code == InteriorOrderStatusEnums::PAY_AMOUNT_ERROR_CODE) {
                //判断程序计算的支付金额与前端提交的支付金额不一致的返回
                $this->wLog('submitOrder', $e->getFile()
                    . 'line' . $e->getLine()
                    . 'message' . $e->getMessage()
                    . 'trace' . $e->getTraceAsString());
                return [
                    'msg' => $e->getMessage()
                ];
            }

            $this->wLog('submitOrder', $e->getFile()
                . 'line' . $e->getLine()
                . 'message' . $e->getMessage()
                . 'trace' . $e->getTraceAsString(), 'warning');
            return [
                'msg' => $e->getMessage()
            ];

        } catch (\Exception $e) {
            $db->rollback();
            $error_code = $e->getCode();
            if ($error_code == InteriorOrderStatusEnums::PAY_AMOUNT_ERROR_CODE) {
                //判断程序计算的支付金额与前端提交的支付金额不一致的返回
                $this->wLog('submitOrder', $e->getFile()
                    . 'line' . $e->getLine()
                    . 'message' . $e->getMessage()
                    . 'trace' . $e->getTraceAsString());
                return [
                    'msg' => $e->getMessage()
                ];
            }
            $this->wLog('submitOrder', $e->getFile()
                . 'line' . $e->getLine()
                . 'message' . $e->getMessage()
                . 'trace' . $e->getTraceAsString(), 'error');
            return [
                'msg' => $e->getMessage()
            ];
        }
    }

    /**
     *生成订单号
     * Created by: Lqz.
     * @return mixed|string
     * CreateTime: 2020/8/9 0009 22:17
     */
    public function generateOrderCode()
    {
        // 长度20
        $orderCode  = 'GF' . date('YmdHis') . mt_rand(1000, 9999);
        $conditions = "order_code = :order_code:";
        $bind       = ["order_code" => $orderCode];
        $order      = InteriorOrdersModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        if ($order) {
            $orderCode = $this->generateOrderCode();
        }
        return $orderCode;
    }

    /**
     * 获取订单列表
     * Created by: Lqz.
     * @param array $loginUser
     * @param array $params
     * @return array
     * CreateTime: 2020/8/10 0010 14:43
     */
    public function getOrderList(array $loginUser, array $params)
    {
        $page         = $params['page'];
        $limit        = $params['limit'];
        $staffId      = $loginUser['staff_id'];
        $conditions   = "staff_id = :staffId: ";
        $bind         = ["staffId" => $staffId];
        $orderListObj = InteriorOrdersModel::find(
            array(
                'conditions' => $conditions,
                'bind'       => $bind,
                'order'      => 'id desc',
                'limit'      => $limit,
                'offset'     => ($page - 1) * $limit
            )
        );
        $orderList    = $orderListObj->toArray();
        if ($orderList) {
            $orderCodesArr     = array_column($orderList, 'order_code');
            $orderGoodsSkusObj = InteriorOrdersGoodsSkuModel::find([
                'conditions' => 'order_code in({order_code:array})',
                'bind'       => ['order_code' => $orderCodesArr],
                'order'      => 'id desc',
            ]);
            $orderGoodsSkus    = $orderGoodsSkusObj->toArray();
            // 查找每个订单下面的全部商品
            $orderStatusEnums = InteriorOrderStatusEnums::getCodeTxtMap($this->lang);
            $auditedEnums     = InteriorOrderAuditedEnums::getCodeTxtMap($this->lang);
            $fundStatusEnums  = InteriorOrderFundStatusEnums::getCodeTxtMap($this->lang);
            foreach ($orderList as &$order) {
                if ($order['order_status'] == InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE && $order['order_expire_at'] <= date('Y-m-d H:i:s')) {
                    //针对待付款状态订单到了过期时间，脚本还没置为已取消则要变更下状态
                    $order['order_status'] = InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE;
                }

                $order['order_status_txt'] = $orderStatusEnums[$order['order_status']];
                $order['fund_status_txt'] = $fundStatusEnums[$order['fund_status']];
                $order['is_audited_txt']   = $auditedEnums[$order['is_audited']];
                $order['pay_amount_fmt']   = InteriorGoodsEnums::fmtAmount($order['pay_amount'], $this->lang);
//                $order['created_at']       = $order['created_at'];


                $_orderCode = $order['order_code'];
                foreach ($orderGoodsSkus as $orderGoodsSku) {
                    if ($orderGoodsSku['order_code'] == $_orderCode) {
                        $orderGoodsSku['buy_price_fmt']   = InteriorGoodsEnums::fmtAmount($orderGoodsSku['buy_price'], $this->lang);
                        $orderGoodsSku['pay_amount_fmt']  = InteriorGoodsEnums::fmtAmount($orderGoodsSku['pay_amount'], $this->lang);
                        $orderGoodsSku['show_goods_name'] = self::convertGoodsNameByLang($this->lang, $orderGoodsSku);

                        $order['goods_skus'][] = $orderGoodsSku;
                    }
                }
            }
        }
        $count    = InteriorOrdersModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        $pageData = [
            'order_list'   => $orderList,
            'current_page' => $page,
            'count'        => $count,
            'limit'        => $limit
        ];
        return $pageData;
    }

    /**
     * 获取订单
     * Created by: Lqz.
     * @param array $loginUser
     * @param array $params
     * CreateTime: 2020/8/10 0010 15:08
     */
    public function getOrderInfo(array $loginUser, array $params)
    {
        $orderCode = $params['order_code'];
        $staffId   = $loginUser['staff_id'];
        $orderObj  = $this->getStaffOrder($staffId, $orderCode);
        if (!$orderObj) {
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_was_not_found)
            ];
        }
        $payMethodMap              = InteriorGoodsPayMethodEnums::payMethod($this->lang);
        $orderSkusBoj              = $orderObj->getOrdersGoodsSku();
        $order                     = $orderObj->toArray();
        $orderSkus                 = $orderSkusBoj->toArray();
        $order['pay_method_name']  = $payMethodMap[$order['pay_method']];
        $orderStatusEnums          = InteriorOrderStatusEnums::getCodeTxtMap($this->lang);
        $auditedEnums              = InteriorOrderAuditedEnums::getCodeTxtMap($this->lang);
        $fundStatusEnums           = InteriorOrderFundStatusEnums::getCodeTxtMap($this->lang);
        //针对待付款状态订单到了过期时间，脚本还没置为已取消则要变更下状态
        $order['order_status']     = ($order['order_status'] == InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE && $order['order_expire_at'] <= date('Y-m-d H:i:s')) ? InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE : $order['order_status'];
        $order['order_status_txt'] = $orderStatusEnums[$order['order_status']];
        $order['fund_at']          = $order['fund_at'] ? substr($order['fund_at'], 0, 10) : null;
        $order['fund_status_txt'] = $fundStatusEnums[$order['fund_status']];
        $order['is_audited_txt']   = $auditedEnums[$order['is_audited']];
        $order['pay_amount_fmt']   = InteriorGoodsEnums::fmtAmount($order['pay_amount'], $this->lang);
        $order['goods_skus']       = null;
        $wrs_server = new WmsServer($this->lang,$this->timeZone);
        foreach ($orderSkus as $orderGoodsSku) {
            $orderGoodsSku['show_goods_name'] = self::convertGoodsNameByLang($this->lang, $orderGoodsSku);
            if ($orderGoodsSku['order_code'] == $order['order_code']) {
                $orderGoodsSku['buy_price_fmt']  = $wrs_server->format_amount($orderGoodsSku['buy_price']);
                $orderGoodsSku['pay_amount_fmt'] = $wrs_server->format_amount($orderGoodsSku['pay_amount']);
                $order['goods_skus'][]           = $orderGoodsSku;
            }
        }
        $tracking = '';
        $result = (new SyncWarehoseServer())->outBoundTracking($order['order_code']);
        if ($result && isset($result['expressSn'])) {
            $tracking = $result['expressSn'];
        }

        $order['tracking'] = $tracking;
        return ['order_info' => $order];
    }

    /**
     *取消订单
     * Created by: Lqz.
     * @param array $loginUser
     * @param array $params
     * @return bool|string[]
     * CreateTime: 2020/8/10 0010 19:43
     */
    public function cancelOrder(array $loginUser, array $params, $remark = 'staff_cancel_order')
    {
        $orderCode = $params['order_code'];
        $staffId   = $loginUser['staff_id'];
        $db        = InteriorOrdersModel::beginTransaction($this);
        try {
            $orderObj = $this->getStaffOrder($staffId, $orderCode, true);
            if (!$orderObj) {
                $db->rollback();
                return [
                    'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_was_not_found)
                ];
            }
            //订单未审核或者订单状态在待发货、预定中、待付款的才可取消
            if ($orderObj->is_audited != InteriorOrderAuditedEnums::IS_AUDITED_WARITING_CODE || !in_array($orderObj->order_status, [InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE, InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE, InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE])) {
                $db->rollback();
                return [
                    'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_can_not_cancel)
                ];
            }
            $orderStatus = $orderObj->order_status;
            // 取消订单
            $orderObj->order_status  = InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE;
            $date                    = date('Y-m-d H:i:s');
            $orderObj->remark        = $orderObj->remark . "【{$remark}:{$date}】";
            $orderObj->cancel_reason = $params['cancel_reason'] ?? InteriorOrderStatusEnums::ORDER_CANCEL_REASON_SELF;
            $orderObj->canceled_at   = $date;
            $orderObj->updated_at    = $date;
            // 回滚订单goods_sku库存
            $orderSkusObj = $orderObj->getOrdersGoodsSku();
            $orderSkusArr = $orderSkusObj->toArray();
            foreach ($orderSkusArr as $orderSku) {
                $goodsSkuObj           = $this->getGoodsSkuInfo($orderSku['goods_id'], $orderSku['goods_sku_id'], true);
                if(!$goodsSkuObj){
                    continue;
                }
                $toSurplusNum          = $goodsSkuObj->surplus_num + $orderSku['buy_num'];
                $_saleNum              = ($goodsSkuObj->sale_num - $orderSku['buy_num']);
                $goodsSkuObj->sale_num = $_saleNum >= 0 ? $_saleNum : 0;
                // 记录库存修改日志
                self::saveSkuSurplusNumAndLog($goodsSkuObj, $staffId, $orderSku['buy_num'], $toSurplusNum, $remark);
            }
            // 预售单取消订单
            if ($orderStatus == 2) {
                $orderObj->save();
                $db->commit();
            } else if ($orderStatus == InteriorOrderStatusEnums::ORDER_STATUS_WAIT_PAY_CODE) {
                //如果FlashPay待付款的订单直接直接取消，支付成功才同步scm所以无需去scm取消
                $orderObj = $this->cancelPayOrder($orderObj);
                $orderObj->save();
                $db->commit();
            } else {
                $syncData           = [
                    'orderSn' => $orderCode,
                    'lang'    => $this->lang
                ];
                $syncWarehoseServer = new SyncWarehoseServer();
                $wmsRes             = $syncWarehoseServer->syncCancelOrderToWmsCancelOutbound($syncData);
                if ($wmsRes['code'] == 1) {
                    if ($orderObj->pay_method == InteriorGoodsPayMethodEnums::PAY_METHOD_FLASH_PAY_ONLINE) {
                        //如果是FlashPay在线支付的待发货，需要将款项状态变更为退款中
                        $orderObj->fund_status = InteriorOrderFundStatusEnums::FUND_STATUS_REFUNDING;
                    }
                    $orderObj->save();
                    $db->commit();
                } else {
                    $db->rollback();
                    return ['msg' => "取消订单失败【{$wmsRes['msg']}】", 'data' => $wmsRes];
                }
            }
        } catch(ValidationException $e) {
            $db->rollback();
            return [
                'msg' => LangEnums::getTranslation($this->lang, $e->getMessage())
            ];
        } catch (\Exception $e) {
            $db->rollback();
            $this->wLog('cancelOrder', $e->getMessage());
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_cancel_error)
            ];
        }
        return true;
    }

    public function getStaffOrder($staffId, $orderCode, $forUpdate = false)
    {
        $conditions = "staff_id = :staffId: and order_code = :orderCode:";
        $bind       = ["staffId" => $staffId, "orderCode" => $orderCode];
        $orderObj   = InteriorOrdersModel::findFirst(
            array(
                'conditions' => $conditions,
                'bind'       => $bind,
                "for_update" => $forUpdate
            )
        );
        return $orderObj;
    }

    /**
     * 完成收货
     * Created by: Lqz.
     * @param $loginUser
     * @param $params
     * @return bool|string[]
     * CreateTime: 2020/8/10 0010 19:46
     */
    public function saveOrderRecevied($loginUser, $params, $remark = 'staff_received_order')
    {
        $orderCode = $params['order_code'];
        $staffId   = $loginUser['staff_id'];
        $orderObj  = $this->getStaffOrder($staffId, $orderCode);
        if (!$orderObj) {
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_was_not_found)
            ];
        }
        if (!in_array($orderObj->order_status, [InteriorOrderStatusEnums::ORDER_STATUS_SEND_CODE])) {
            return [
                'msg' => LangEnums::getTranslation($this->lang, ReturnMsgEnums::The_order_was_delivered)
            ];
        }
        $orderObj->order_status = InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_RECEIVED_CODE;
        $orderObj->received_at  = date('Y-m-d H:i:s');
        $orderObj->remark = $orderObj->remark . "【{$remark}】";
        $res              = $orderObj->save();
        return $res;
    }

    /**
     * 修改sku 库存并记录日志
     * Created by: Lqz.
     * @param InteriorGoodsSkuModel $goodsSkuObj
     * @param $staffId
     * @param $toSurplusNum
     * @param $remark
     * CreateTime: 2020/8/13 0013 11:53
     */
    public static function saveSkuSurplusNumAndLog(InteriorGoodsSkuModel $goodsSkuObj, $staffId, $chNum, $toSurplusNum, $remark)
    {
        if ($goodsSkuObj->surplus_num == $toSurplusNum) {
            return;
        }
        $logObj                   = new InteriorGoodsSkuLogModel();
        $logObj->staff_id         = $staffId;
        $logObj->goods_id         = $goodsSkuObj->goods_id;
        $logObj->goods_sku_id     = $goodsSkuObj->id;
        $logObj->from_surplus_num = $goodsSkuObj->surplus_num;
        $logObj->to_surplus_num   = $toSurplusNum;
        $logObj->current_ch_num   = $chNum;
        $logObj->remark           = $remark;
        $logObj->save();
        // 回滚goods_sku库存
        $goodsSkuObj->surplus_num = $toSurplusNum;
        $goodsSkuObj->save();
    }


    public static function convertGoodsNameByLang(string $lang, array $goods)
    {
        switch ($lang) {
            case LangEnums::LANG_CODE_EN :
                $currentName = $goods['goods_name_en'];
                break;
            case LangEnums::LANG_CODE_ZH_CN :
                $currentName = $goods['goods_name_zh'];
                break;
            default:
                // v13847需求, 若当地语言为空, 则取英文
                $currentName = !empty($goods['goods_name_th']) ? $goods['goods_name_th'] : $goods['goods_name_en'];
        }

        return $currentName;
    }

    public function getUserByNum($thisBuyNum, $loginUser,$setting_model)
    {

        $limit    = $setting_model->getSetVal($code = 'interior_buy_limit_num');
        $orderSkuNum = 0;
        $staffId     = $loginUser['staff_id'];
        $ordesObj    = InteriorOrdersModel::find([
            'conditions' => 'staff_id = :staffId: and order_status not in ({orderStatus:array}) and goods_type = :goods_type: and created_at >= :createdAtLeft: and created_at <= :createdAtRight:',
            'bind'       => [
                'staffId'        => $staffId,
                'orderStatus'    => [InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE, InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE],
                'goods_type' => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES,
                'createdAtLeft'  => date('Y-m-1 00:00:00'),
                'createdAtRight' => date('Y-m-t 23:59:59'),
            ],
            'columns'    => 'order_code'
        ]);
        $orders      = $ordesObj->toArray();
        $codes       = array_column($orders, 'order_code');
        if ($codes) {
            $conditions = 'order_code in ({orderCode:array}) and is_free = 0';
            $orderSkuNum = InteriorOrdersGoodsSkuModel::sum([
                'conditions' => $conditions,
                'bind'       => [
                    'orderCode' => $codes
                ],
                'column'     => 'buy_num'
            ]);
        }
        $canBuyNum = $limit - $thisBuyNum - $orderSkuNum;
        return $canBuyNum >= 0 ? true : false;
    }

    /**
     * 检查员工是否是首次下单工服
     * @param $staffId
     * @return bool
     */
    private function checkStaffIsFirstOrder($staffId): bool
    {
        $count = InteriorOrdersModel::count([
            'staff_id = :staff_id: AND order_status IN ({order_status:array}) and goods_type = :goods_type: ',
            'bind' => [
                'staff_id' => $staffId,
                'order_status' => [
                    InteriorOrderStatusEnums::ORDER_STATUS_WARTING_SEND_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_PRE_SALE_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_SEND_CODE,
                    InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_RECEIVED_CODE,
                ],
                'goods_type' => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES
            ]
        ]);
        $this->wLog('checkStaffIsFirstOrder', '工号:' . $staffId . '是否首次下单count:' . $count);
        if ($count == 0) {
            return true;
        }
        return false;
    }

   public  static function  getStaffStoreCateEnableGoods()
    {
        $goods_cate = SysStoreGoodsModel::find();
        $goods_cate = $goods_cate->toArray();

        foreach ($goods_cate as &$item){
            $item['store_cate_id'] = explode(",",$item['store_cate_id'])??'';
            $item['goods_id'] =explode(",",$item['goods_id'])??'';
        }

        return $goods_cate;
    }

    public function getCodeTxtMap()
    {
        $goodsCates = self::getStaffStoreCateEnableGoods();
        $goodsCates = array_column($goodsCates,'store_cate_id','sys_store_cate')??[];
        return $goodsCates;

    }
    /**
     * 限免免费数量
     * */
    public function getUserFreeByNum($loginUser,$goodsIds,$whereTime=0)
    {

        $orderSkuNum = 0;
        $staffId     = $loginUser['staff_id'];
        if(empty($whereTime)){
            $ordesObj    = InteriorOrdersModel::find([
                'conditions' => 'staff_id = :staffId: and order_status != :orderStatus: ',
                'bind'       => [
                    'staffId'        => $staffId,
                    'orderStatus'    => InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE,
                ],
                'columns'    => 'order_code'
            ]);

        }else{
            $date = date("Y-m-d");
            $staTime = date('Y-m-d 00:00:00',strtotime($date));
            $endTime = date('Y-m-d 23:59:59',strtotime("$staTime +1 month -1 day"));
            $ordesObj    = InteriorOrdersModel::find([
                'conditions' => 'staff_id = :staffId: and order_status != :orderStatus: and created_at BETWEEN :staTime: and :endTime:',
                'bind'       => [
                    'staffId'        => $staffId,
                    'orderStatus'    => InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE,
                    'staTime' => $staTime,
                    'endTime' => $endTime,
                ],
                'columns'    => 'order_code'
            ]);
        }


        $orders      = $ordesObj->toArray();
        $codes       = array_column($orders, 'order_code');
        if ($codes) {
            $orderSkuNum = InteriorOrdersGoodsSkuModel::find([
                'conditions' => 'order_code in ({orderCode:array}) and goods_id in ({goodsId:array}) and is_free =1  group by goods_id',
                'bind'       => [
                    'orderCode' => $codes,
                    'goodsId'=>$goodsIds
                ],
                'columns'     => 'goods_id,sum(buy_num) as buy_nums'
            ])->toArray();
        }
        $order_sku_num = [];
        if (!empty($orderSkuNum)) {
            $order_sku_num = array_column($orderSkuNum, 'buy_nums', 'goods_id');
        }

        return $order_sku_num;
    }

    /**
     * 获取员工限免商品类别
     * @param array $userInfo
     * @return array|string[]
     */
    public  function getUserEnableFreeGoodsCate(array $userInfo)
    {
        $storeCateCode = HrStaffInfoServer::getStaffStoreCateCode($userInfo);
        (new BaseServer())->wLog('StoreCateCode', $storeCateCode);

        if(isset($storeCateCode['msg'])){
            return $storeCateCode;
        }
        $goods_ids =  (new InteriorGoodsRepository())->getInteriorGoodsStoreCateRelAll($storeCateCode);
        return  $goods_ids;
    }


    /**
     * 新需求逻辑变更 需求11533 https://l8bx01gcjr.feishu.cn/docs/doccnXPQF9RegncaEhbOH63aUS0
     * 限免免费数量总和
     * */
    public function getUserFreeByNumNew($loginUser)
    {
        $order_sku_num = 0;
        $staffId       = $loginUser['staff_id'];
        $staff_ids[] = $staffId;
        //19020需求，判断若员工是个人代理 && 新工号不等于员工号，需要统计个人代理工号关联的旧员工号名下已购买的工服数量
        $userInfo = HrStaffInfoServer::getUserInfoByStaffInfoId($staffId);
        //个人代理逻辑
        if (!empty($userInfo) && $userInfo->hire_type == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            //获取个人代理对应的原工号
            $oldId = $this->getOldStaffId($staffId, HireTypeImportListModel::DATA_TYPE_IC);
            if (!empty($oldId) && $oldId != $staffId) {
                //存在对应关系 && 新旧工号不一致
                $staff_ids[] = $oldId;
            }
        }
        //LNT公司 员工 逻辑 产品说 不可能存在 个人代理 和lnt 2种属性的员工
        $staffInfo = $userInfo->toArray();
        $isLnt = StaffServer::isLntCompanyByInfo($staffInfo);
        if ($isLnt) {
            //查询旧工号
            $oldId = $this->getOldStaffId($staffId, HireTypeImportListModel::DATA_TYPE_LNT);
            if (!empty($oldId) && $oldId != $staffId) {
                //存在对应关系 && 新旧工号不一致
                $staff_ids[] = $oldId;
            }
        }
        $orders_obj    = InteriorOrdersModel::find([
            'conditions' => 'staff_id in ({staffId:array}) and order_status not in ({orderStatus:array}) and goods_type = :goods_type: ',
            'bind'       => [
                'staffId'     => $staff_ids,
                'orderStatus' => [InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE, InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE],
                'goods_type' => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES
            ],
            'columns'    => 'order_code'
        ]);
        if (empty($orders_obj)) {
            return $order_sku_num;
        }
        $orders = $orders_obj->toArray();
        $codes  = array_column($orders, 'order_code');
        if ($codes) {
            $order_sku_obj = InteriorOrdersGoodsSkuModel::findFirst([
                'conditions' => 'order_code in ({orderCode:array})  and is_free =1',
                'bind'       => [
                    'orderCode' => $codes,
                ],
                'columns'    => 'sum(buy_num) as buy_nums'
            ]);
            if (empty($order_sku_obj)) {
                return $order_sku_num;
            }
            $order_sku_num = $order_sku_obj->toArray();
        }

        return $order_sku_num['buy_nums']??0;
    }

    public function goodsSize($goods_id)
    {
        return MyInteriorGoodsEnums::getGoodsSize($goods_id, $this->lang);

    }

    public function getOldStaffId($staffId, $type){
        //获取个人代理对应的原工号
        $hire_type_staff_info = HireTypeImportListModel::findFirst([
            'conditions' => 'new_staff_id = :staff_info_id: and data_type = :data_type:',
            'bind'=> ['staff_info_id' => $staffId, 'data_type' => $type],
            'columns' => 'old_staff_id'
        ]);
        if(empty($hire_type_staff_info)){
            return 0;
        }
        return $hire_type_staff_info->old_staff_id;
    }


}
