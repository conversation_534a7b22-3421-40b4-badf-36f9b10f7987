<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HireTypeImportListModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Modules\My\library\Enums\CommonEnums;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Server\CounselorServer AS GlobalBaseServer;

class CounselorServer extends GlobalBaseServer
{
    protected $hireDays = 30;

    //Bike Courier[13]/Van Courier[110]/Boat Courier[452]
    protected $frontLineJobTitles = [
        CommonEnums::JOB_TITLE_BIKE_COURIER,
        CommonEnums::JOB_TITLE_VAN_COURIER,
        CommonEnums::JOB_TITLE_CAR_COURIER,
    ];

    //DC Officer[37]&Assistant Branch Supervisor[451]
    protected $frontLineJobNonTitles = [
        CommonEnums::JOB_TITLE_DC_OFFICER,
    ];

    protected $branchWorkStaffIds = [
        CommonEnums::JOB_TITLE_BRANCH_SUPERVISOR,
        CommonEnums::JOB_TITLE_ASSISTANT_BRANCH_SUPERVISOR,
    ];

    /**
     * 获取网点员工数据
     * @param $params
     * @return array
     */
    public function getStaffCounselorList($param)
    {
        $this->wLog('getCounselorList-store',$param);

        //职位数据
        $sysStoreId = $param['sys_store_id'] ?? '';
        $jobTitles  = $this->getSearchJobTitles($param['job_title'] ?? 0);

        if (empty($jobTitles) || !$sysStoreId) {
            return [];
        }

        //获取符合条件的ids
        $staffList = $this->getStaffList([
            'sys_store_id' => $sysStoreId,
            'job_title'    => $jobTitles,
            'hire_type' => [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
                HrStaffInfoModel::HIRE_TYPE_UN_PAID
            ]
        ]);

        if (empty($staffList)) {
            return [];
        }

        if ($param['job_title'] != CommonEnums::JOB_TITLE_DC_OFFICER) {
            $staffIds = array_column($staffList,'staff_info_id');

            $hireTypeStaffList = HireTypeImportListModel::find([
                'conditions' => 'new_staff_id IN ({new_staff_id:array}) and entry_state = :entry_state: and deleted=:deleted:',
                'bind'=> [
                    'new_staff_id' => $staffIds,
                    'entry_state'  => HireTypeImportListModel::STATUS_EMPLOYED,
                    'deleted' => enums::IS_DELETED_NO,
                ],
                'columns' => 'new_staff_id'
            ])->toArray();

            $hireTypeStaffIds = array_column($hireTypeStaffList,'new_staff_id');

            $hireDate   = strtotime(date('Y-m-d', strtotime("-{$this->hireDays} days")));

            foreach ($staffList as $k => $v) {

                if (
                    $v['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID
                    &&
                    in_array($v['staff_info_id'], $hireTypeStaffIds)
                ) {
                    continue;
                }

                //不符合规则 大于不符合要求
                if (strtotime(substr($v['hire_date'],0,10)) > $hireDate) {
                    unset($staffList[$k]);
                }
            }
        }

        $staffIds = array_column($staffList,'staff_info_id');

        //查询是否为个人代理
        $staffIds = $this->deleteCounselorNumStaffIds($staffIds,$sysStoreId);

        return array_values($staffIds);
    }

    /**
     * 获取网点主管相关辅导员
     * @param $param
     * @return mixed
     */
    public function getSupervisorCounselorList($param)
    {
        $this->wLog('getCounselorList-Supervisor',$param);

        return $this->getStaffIds([
            'sys_store_id' => $param['sys_store_id'],
            'job_title'    => $this->branchWorkStaffIds,
            'hire_type'    => [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
                HrStaffInfoModel::HIRE_TYPE_UN_PAID
            ],
        ]);
    }

    /**
     * 获取职位集合
     * @param $staffJobTitle
     * @return array
     */
    public function getSearchJobTitles($staffJobTitle)
    {
        $staffJobTitle = intval($staffJobTitle);

        if (in_array($staffJobTitle,$this->frontLineJobTitles)) {
            return $this->frontLineJobTitles;
        }

        if ($staffJobTitle == CommonEnums::JOB_TITLE_DC_OFFICER) {
            return $this->frontLineJobNonTitles;
        }

        return [];
    }

    /**
     * 获取大区片区数据
     */
    public function getMangerCounselorList($param)
    {
        $this->wLog('getCounselorList-Manger',$param);

        //获取
        $staffIds = [];

        $regionPieceManager = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getOrganizationRegionPieceManagerId($param['sys_store_id']);

        $this->getDI()->get('logger')->write_log([
            'getOrganizationRegionPieceManagerId' => $regionPieceManager,
            'sys_store_id'                        => $param['sys_store_id'],
        ], 'info');

        if (!empty($regionPieceManager['region_manager_id'])) {
            $staffIds[] = $regionPieceManager['region_manager_id'];
        }

        if (!empty($regionPieceManager['piece_manager_id'])) {
            $staffIds[] = $regionPieceManager['piece_manager_id'];
        }

        if ($staffIds) {
            //筛选
            $staffIds = $this->getStaffIds([
                'staff_ids'    => $staffIds,
                'hire_type'    => [
                    HrStaffInfoModel::HIRE_TYPE_1,
                    HrStaffInfoModel::HIRE_TYPE_2,
                ]
            ]);
        }

        return $staffIds;
    }
}