<?php
/**
 * Author: Bruce
 * Date  : 2024-11-12 22:55
 * Description:
 */

namespace FlashExpress\bi\App\Modules\My\Server;


use FlashExpress\bi\App\Models\backyard\HrStaffContractBusinessBaseModel;
use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractBusinessApplyModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Repository\HrStaffContractBusinessApplyRepository;
use FlashExpress\bi\App\Repository\HrStaffContractBusinessBaseRepository;
use FlashExpress\bi\App\Repository\HrStaffContractRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditCallbackServer;
use FlashExpress\bi\App\Server\AuditOptionRule;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\RenewContractBusinessServer as GlobalBaseServer;

class RenewContractBusinessServer extends GlobalBaseServer
{

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        //申请人不可撤销
        return new AuditOptionRule(true,
            false,
            false,
            false,
            false,
            false);
    }

    /**
     * 存在一级审批，不允许撤销
     * @param $auditId
     * @return bool
     */
    public function applicantCustomiseOptionsInApprovalProcess($auditId): bool
    {
        //todo 查询业务数据，当前审批状态如果是非待审批，则返回 false;
        $hrStaffContractBusinessApplyRepository = new HrStaffContractBusinessApplyRepository($this->timezone);
        $contractInfo       = $hrStaffContractBusinessApplyRepository->getOneById($auditId, 'status');
        if (empty($contractInfo)) {
            return false;
        }

        if ($contractInfo['status'] != enums::APPROVAL_STATUS_PENDING) {
            return false;
        }

        $info = AuditApprovalModel::findFirst([
            'conditions' => 'biz_type = :audit_type: and biz_value = :audit_id: and state = :state: and deleted = 0',
            'bind' => [
                'audit_type' => AuditListEnums::APPROVAL_TYPE_IC_RENEWAL,
                'audit_id' => $auditId,
                'state' => enums::APPROVAL_STATUS_APPROVAL
            ],
        ]);
        return empty($info) ? true : false;
    }

    /**
     * 审批
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function update($paramIn)
    {
        $staffId       = $this->processingDefault($paramIn, 'staff_id', 2);
        $id            = $this->processingDefault($paramIn, 'audit_id', 2);
        $status        = $this->processingDefault($paramIn, 'status', 2);
        $reject_reason = $this->processingDefault($paramIn, 'reject_reason');

        $hrStaffContractBusinessApplyRepository = new HrStaffContractBusinessApplyRepository($this->timezone);
        $contractInfo       = $hrStaffContractBusinessApplyRepository->getOneById($id, 'status');

        if (empty($contractInfo)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }

        if ($contractInfo['status'] == enums::APPROVAL_STATUS_APPROVAL) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        if ($contractInfo['status'] == enums::APPROVAL_STATUS_REJECTED) {
            throw new ValidationException($this->getTranslation()->_('1016'));
        }

        if ($contractInfo['status'] == enums::APPROVAL_STATUS_CANCEL) {
            throw new ValidationException($this->getTranslation()->_('cancel_notice'));
        }

        $server = new ApprovalServer($this->lang, $this->timezone);
        if ($status == enums::$audit_status['approved']) {
            // 同意
            $server->approval($id, AuditListEnums::APPROVAL_TYPE_IC_RENEWAL, $staffId);
        } elseif ($status == enums::$audit_status['dismissed']) {
            // 驳回
            $server->reject($id, AuditListEnums::APPROVAL_TYPE_IC_RENEWAL, $reject_reason, $staffId);
            // 撤销
        } elseif ($status == enums::$audit_status['revoked']) {//撤销
            $server->cancel($id, AuditListEnums::APPROVAL_TYPE_IC_RENEWAL, $reject_reason, $staffId);
        }

        return $this->checkReturn(['data' => ['id' => $id]]);
    }

    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $hrStaffContractBusinessApplyRepository = new HrStaffContractBusinessApplyRepository($this->timezone);
            $contractInfo       = $hrStaffContractBusinessApplyRepository->getOneById($auditId);

            if (empty($contractInfo)) {
                throw new ValidationException($this->getTranslation()->_('1015') . '[From RenewContractBusiness 审批终态设置]');
            }

            if ($contractInfo['status'] == enums::APPROVAL_STATUS_APPROVAL) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From RenewContractBusiness 审批终态设置]');
            }

            if ($contractInfo['status'] == enums::APPROVAL_STATUS_REJECTED) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From RenewContractBusiness 审批终态设置]');
            }

            if ($contractInfo['status'] == enums::APPROVAL_STATUS_TIMEOUT) {
                throw new ValidationException($this->getTranslation()->_('1016') . '[From RenewContractBusiness 审批终态设置]');
            }

            if(in_array($state, [enums::APPROVAL_STATUS_REJECTED, enums::APPROVAL_STATUS_TIMEOUT])) {
                $data['id']              = $contractInfo['id'];
                $data['contract_id']     = $contractInfo['contract_id'];
                $data['business_type']   = $contractInfo['business_type'];
                $data['business_status'] = $contractInfo['business_status'];
                $data['audit_status']    = $state;
                //修改 在职状态， 释放hold
                AuditCallbackServer::createData(AuditListEnums::APPROVAL_TYPE_IC_RENEWAL, $data);
            }

            $updateData['status']           = $state;
            $updateData['audit_time']       = date('Y-m-d H:i:s');

            $db                   = $this->getDI()->get('db');
            $db->updateAsDict('hr_staff_contract_business_apply', $updateData,
                [
                    'conditions' => 'id = ?',
                    'bind'       => [$auditId],
                ]
            );
        }
    }

    /**
     * 打卡页直接提交，消息 同意续约
     * 创建审批流
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function addAudit($params)
    {
        if(empty($params) || empty($params['contract_id'])) {
            throw new BusinessException('params contract_id is not empty:' . $this->getTranslation()->_('data_error'));//未找到数据
        }

        $baseData = HrStaffContractBusinessBaseRepository::getOneByContractId(['contract_id' => $params['contract_id']]);

        if (empty($baseData)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//未找到数据
        }

        $staffRepository = new StaffRepository();
        $staffData       = $staffRepository->getStaffInfoOne($baseData['staff_info_id']);

        if (empty($staffData)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//未找到数据
        }

        $where['contract_id'] = $params['contract_id'];
        $where['audit_status'] = [enums::$audit_status['panding']];//待审批
        $pendingInfo = HrStaffContractBusinessApplyRepository::getOneByContractId($where);

        //该合同 已经存在 待审批的审批流无需再创建
        if (!empty($pendingInfo) && !empty($pendingInfo['serial_no'])) {
            $this->logger->write_log("个人代理续约劳动合同 当前合同id已存在待审批，无需审批 id:" . $params['id'] . ', contract_id:' . $baseData['contract_id'], 'notice');
            return [];
        }

        $businessWhere['contract_id'] = $params['contract_id'];
        $businessWhere['business_type'] = HrStaffContractBusinessApplyModel::MY_BUSINESS_TYPE_FIRST;
        $businessData = HrStaffContractBusinessApplyRepository::getOneByContractId($businessWhere);

        $storeInfo = SysStoreRepository::getSysStoreInfo($staffData['sys_store_id'], ['manage_region', 'manage_piece']);

        $staffCurrentData['sys_store_id']      = $staffData['sys_store_id'] ?? '';
        $staffCurrentData['job_title']         = $staffData['job_title'] ?? 0;
        $staffCurrentData['hire_type']         = $staffData['hire_type'] ?? 0;
        $staffCurrentData['hire_date']         = !empty($staffData['hire_date']) ? date('Y-m-d', strtotime($staffData['hire_date'])) : '';
        $staffCurrentData['contract_end_date'] = $businessData['contract_end_date'] ?? '';
        $staffCurrentData['region_id']         = $storeInfo['manage_region'] ?? 0;
        $staffCurrentData['piece_id']          = $storeInfo['manage_piece'] ?? 0;
        $contract_end_date = $businessData['contract_end_date'] ?? '';
        $staffCurrentData['leave_date']        = !empty($contract_end_date) ? date('Y-m-d', strtotime("{$contract_end_date} +1 days")) : '';

        //固化当前 员工信息 ；
        $audit_json = json_encode($staffCurrentData, JSON_UNESCAPED_UNICODE);

        $serialNo   = $this->getRandomId();

        $db = $this->getDI()->get('db');
        try {
            $db->begin();

            $time = date('Y-m-d H:i:s');
            //mq 消费来的是有业务id的， by打卡页直接提交的审批没有业务id。
            if(!empty($params['id'])) {
                $applyData = HrStaffContractBusinessApplyModel::findFirst(
                    [
                        'conditions' => "id = :id:",
                        'bind'       => [
                            'id' => $params['id'],
                        ],
                    ]
                );
            } else {
                $applyData = new HrStaffContractBusinessApplyModel();
                $applyData->contract_id = $params['contract_id'];
                $applyData->staff_info_id = $baseData['staff_info_id'];
                $applyData->business_type = HrStaffContractBusinessApplyModel::MY_BUSINESS_TYPE_WORKFLOW;
                $applyData->audit_created_at = $time;//审批创建时间
                $applyData->business_operation_time = date('Y-m-d H:i:s');
            }

            $applyData->status = enums::$audit_status['panding'];
            $applyData->business_status = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_AGREE;
            $applyData->serial_no = (!empty($serialNo) ? 'EC' . $serialNo : null);
            $applyData->audit_json = $audit_json;

            $applyData->save();
            $id = $applyData->id;

            $updateData['business_type'] = HrStaffContractBusinessBaseModel::BUSINESS_TYPE_WORKFLOW;
            $updateData['business_status'] = HrStaffContractBusinessBaseModel::BUSINESS_STATUS_AGREE;
            $updateData['business_operation_time'] = $time;
            $db->updateAsDict('hr_staff_contract_business_base', $updateData,
                [
                    'conditions' => 'contract_id = ?',
                    'bind'       => [$params['contract_id']],
                ]
            );

            //创建审批
            $server    = new ApprovalServer($this->lang, $this->timeZone);
            $requestId = $server->create($id, AuditListEnums::APPROVAL_TYPE_IC_RENEWAL,  $baseData['staff_info_id'], null, []);
            if (!$requestId) {
                throw new Exception($this->getTranslation()->_('4101'));
            }

            $db->commit();
        } catch (Exception $exception) {
            $db->rollBack();
            throw new Exception($this->getTranslation()->_('4101'));
        }

        return ['id' => $id];
    }

    /**
     * 同意 不同意 续约---处理消息
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function submit($params)
    {
        [$businessData, $staffData] = $this->validateData($params);

        if($businessData['business_status'] != HrStaffContractBusinessApplyModel::BUSINESS_STATUS_PENDING) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//已处理的数据，就不能再处理了
        }

        $data['id']              = $businessData['id'];
        $data['contract_id']     = $businessData['contract_id'];
        $data['business_type']   = $businessData['business_type'];
        $data['business_status'] = $params['status'];

        $time = date('Y-m-d H:i:s');

        $update['business_status'] = $data['business_status'];
        $update['business_operation_time'] = $time;

        $contract = HrStaffContractRepository::getOne(['id' => $data['contract_id']]);
        if(empty($contract)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//已处理的数据，就不能再处理了
        }

        if($contract['contract_status'] != HrStaffContractModel::CONTRACT_STATUS_TO_BE_RENEWED) {
            $this->getDI()->get('db')->updateAsDict(
                'hr_staff_contract_business_apply',
                $update,
                'id = '.$businessData['id']
            );

            (new BackyardServer($this->lang,$this->timezone))->has_read_operation($businessData['msg_id'],false);
            $this->logger->write_log(['renew-contract-business exception-submit-2' => $params], 'info');

            return true;
        }

        //同意：创建审批流流程
        //不同意：给上级发消息
        $db = $this->getDI()->get('db');

        try {
            $db->begin();
            //将消息对应的这条业务数据置为已处理
            $db->updateAsDict(
                'hr_staff_contract_business_apply',
                $update,
                'id = '.$businessData['id']
            );

            //如果是同意，则需要增加一条，审批的业务数据。（先创建业务数据，再异步创建 审批流。）
            if($data['business_status'] == HrStaffContractBusinessApplyModel::BUSINESS_STATUS_AGREE) {
                $insertData['contract_id']             = $businessData['contract_id'];
                $insertData['staff_info_id']           = $businessData['staff_info_id'];
                $insertData['business_type']           = HrStaffContractBusinessApplyModel::MY_BUSINESS_TYPE_WORKFLOW;
                $insertData['business_status']         = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_AGREE;
                $insertData['business_operation_time'] = $time;
                $insertData['contract_start_date']     = $businessData['contract_start_date'];
                $insertData['contract_end_date']       = $businessData['contract_end_date'];
                $insertData['status']                  = enums::$audit_status['panding'];
                $insertData['audit_created_at']        = $time;
                $db->insertAsDict('hr_staff_contract_business_apply', $insertData);

                $data['id']  = $db->lastInsertId();

                //将主表 业务类型改为，审批流
                $data['business_type'] = $update['business_type'] = HrStaffContractBusinessApplyModel::MY_BUSINESS_TYPE_WORKFLOW;
            }

            //将基础表数据更新
            $db->updateAsDict(
                'hr_staff_contract_business_base',
                $update,
                'contract_id = '.$businessData['contract_id']
            );

            $db->commit();

        }catch (Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log("renew_contract_submitERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString());
            throw new Exception($this->getTranslation()->_('data_error'));
        }

        $rmq = new RocketMQ('renew-contract-business');
        $rmq->setType(RocketMQ::TAG_NAME_RENEW_CONTRACT);
        $rid = $rmq->sendMsgByTag($data);
        $this->logger->write_log('backyard to hcm rmq-renew-contract-business exception: ' . $rid, 'info');

        if(!$rid) {//mq 发送失败
            throw new BusinessException($this->getTranslation()->_('data_error'));
        }
        (new BackyardServer($this->lang,$this->timezone))->has_read_operation($businessData['msg_id'],false);

        $this->delCache($params['staff_id']);

        return true;
    }

    /**
     * 获取消息详情数据
     * @param $params
     * @return mixed
     * @throws BusinessException
     */
    public function getContractInfo($params)
    {
        [$businessData, $staffData] = $this->validateData($params);

        $baseInfo = HrStaffContractBusinessBaseRepository::getOneByContractId(['contract_id' => $businessData['contract_id']]);

        //如果 业务状态 已处理，则直接将消息 置为已读
        if(!empty($businessData['business_status'])) {
            (new BackyardServer($this->lang,$this->timezone))->has_read_operation($businessData['msg_id'],false);
        }

        //如果主表，已经有业务类型是 审批流。则将消息的 状态置为 同意。无需再同意。
        if($baseInfo['business_type'] == HrStaffContractBusinessBaseModel::BUSINESS_TYPE_WORKFLOW && empty($businessData['business_status'])) {
            //消息页面展示 已同意。
            $businessData['business_status'] = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_AGREE;
            //更新数据
            $update['business_status'] = HrStaffContractBusinessApplyModel::BUSINESS_STATUS_AGREE;
            $update['business_operation_time'] = date('Y-m-d H:i:s');

            $db = $this->getDI()->get('db');
            $db->updateAsDict(
                'hr_staff_contract_business_apply',
                $update,
                'id = '.$businessData['id']
            );
            (new BackyardServer($this->lang,$this->timezone))->has_read_operation($businessData['msg_id'],false);
            $this->delCache($params['staff_id']);
        }

        $result['staff_info_id'] = $staffData['staff_info_id'] ?? '';
        $result['name']          = $staffData['name'] ?? '';
        $result['date']          = $businessData['contract_end_date'];
        $result['status']        = intval($businessData['business_status']);//是否同意续期：1同意，2不同意

        return $result;
    }

    /**
     * 可视化超时，按照表单去超时
     *
     * @param $audit_id
     * @return string
     */
    public function getAuditFormOvertimeDate($audit_id): string
    {
        $hrStaffContractBusinessApplyRepository = new HrStaffContractBusinessApplyRepository($this->timezone);
        $applyInfo       = $hrStaffContractBusinessApplyRepository->getOneById($audit_id);
        if (empty($applyInfo)) {
            return '';
        }
        $audit_json = json_decode($applyInfo['audit_json'], true);

        return empty($audit_json['leave_date']) ? '' : $audit_json['leave_date'];
    }

    /**
     * 超时关闭，驳回。发送消息
     * @param $data
     * @return bool
     * @throws BusinessException
     */
    public function delayCallBack($data)
    {
        $rmq = new RocketMQ('renew-contract-business');
        $rmq->setType(RocketMQ::TAG_NAME_RENEW_CONTRACT);
        $rid = $rmq->sendMsgByTag($data);
        $this->logger->write_log('backyard to hcm rmq-renew-contract-business exception: ' . $rid, 'info');
        if(!$rid) {//mq 发送失败
            $this->logger->write_log(['RenewContractBusinessServer-setProperty-mq-fail' => $data]);
            throw new BusinessException($this->getTranslation()->_('data_error'));
        }

        return true;
    }
}