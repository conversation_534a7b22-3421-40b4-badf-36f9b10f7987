<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/9/8
 * Time: 2:26 PM
 */

namespace FlashExpress\bi\App\Modules\My\Server;


use Exception;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffDaysFreezeModel;
use FlashExpress\bi\App\Models\backyard\StaffLastYearDaysModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveInLieuModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReadModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Modules\My\library\Enums\CommonEnums;
use FlashExpress\bi\App\Modules\My\Server\Vacation\AnnualServer;
use FlashExpress\bi\App\Modules\My\Server\Vacation\MaternityServer;
use FlashExpress\bi\App\Modules\My\Server\Vacation\MilitaryServer;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\StaffPublicHolidayRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
//重写的方法 跟父类不一样 不能继承 除非改名字
use FlashExpress\bi\App\Server\LeaveServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use App\Country\Tools;
use FlashExpress\bi\App\Modules\My\Server\Vacation\InternationalServer;
use FlashExpress\bi\App\Server\SyncServer;


class LeaveServer extends GlobalBaseServer {

    public $leaveObject;


    public function format_leave_insert($staffId,$param){
        $audit_server = new AuditServer($this->lang,$this->timeZone);
        $create_type = $audit_server->by_create_time;
        $leaveStartTime = $param['start_time'];
        $leaveEndTime = $param['end_time'];
        $leaveType = $param['leave_type'];
        $holidays = $param['holidays'];
        $sub_day_off = $param['sub_day'];
        $leaveStartType = $param['start_type'];
        $leaveEndType = $param['end_type'];
        $creat_year = date("Y");
        $insert = array();
        if ((strtotime($leaveEndTime) - strtotime($leaveStartTime)) / (24 * 3600) >= 1) {//跨天

            $key        = $start_date = date('Y-m-d', strtotime($leaveStartTime));
            $end_date   = date('Y-m-d', strtotime($leaveEndTime));
            $date_array = array();
            while ($key <= $end_date) {
                $date_array[] = $key;
                $key          = date("Y-m-d", strtotime("+1 day", strtotime($key)));
            }

            foreach ($date_array as $k => $date) {
                //whether in ones holidays or not
                if (in_array($date, $holidays) && in_array($leaveType, $sub_day_off))
                    continue;

                //请假多天 拆开单天
                $insert[$k]['staff_info_id'] = $staffId;
                $insert[$k]['date_at']       = $date;
                $insert[$k]['type']          = 0;
                $insert[$k]['year_at']       = date('Y', strtotime($date));
                //第一天
                if ($date == $start_date) {
                    $insert[$k]['type'] = ($leaveStartType == 1) ? 0 : $leaveStartType;
                }
                //最后一天
                if ($date == $end_date) {
                    $insert[$k]['type'] = ($leaveEndType == 2) ? 0 : $leaveEndType;
                }
                if(!empty($create_type) && in_array($leaveType,$create_type))
                    $insert[$k]['year_at'] = $creat_year;

            }


        } else {//不跨天
            if (in_array($leaveStartTime, $holidays) && in_array($leaveType, $sub_day_off))
                return $this->checkReturn(-3, $this->getTranslation()->_('day off for the apply date'));
            else {
                $insert[0]['staff_info_id'] = $staffId;
                $insert[0]['date_at']       = $leaveStartTime;
                $insert[0]['type'] = 0;
                if ($leaveStartType == $leaveEndType)//半天
                    $insert[0]['type'] = $leaveStartType;
                $insert[0]['year_at']       = date('Y', strtotime($leaveStartTime));

                if(!empty($create_type) && in_array($leaveType,$create_type))
                    $insert[0]['year_at'] = $creat_year;
            }
        }
        return array('code' => 1,'msg' => '','data' => $insert);

    }

    //新需求 计算 根据职等 对应的假期额度 have day
    public function get_have_day($staff_id,$should_days){
        if(empty($staff_id) || empty($should_days))
            return 0;

        $count_day = StaffDaysFreezeModel::findFirst($staff_id);
        if(empty($count_day))
            return 0;


        $current_tmp = strtotime(date('Y-m-d',strtotime("-1 day")));//昨天00 也就是 任务跑过的日期
        $start_tmp = strtotime(date('Y-01-01'));//周期第一天 00点
        $year = date('Y');
        $all_days = 365;
        if(year_type($year))
            $all_days = 366;

        //获取剩余天数
        $left_days = $all_days - (($current_tmp - $start_tmp) / (24 * 3600));
        $left_days = $should_days * ($left_days / $all_days);//每年剩余天数 对应职等天数的份数 例：剩余200天 职等7天  7 * (200/365)
        $have_days = $count_day->days + $left_days;
        $have_days = round($have_days,enums::ROUND_NUM);
        $int_day   = floor($have_days);//2
        $have_days = $have_days > ($int_day + 0.5) ? ($int_day + 0.5) : $int_day;//2.5
        return $have_days;
    }



    /**
     * //公用额度 逻辑修改
     * @param $staffId
     * @param $year
     * @param $used
     * @param $leave_type 字符串
     * @return float|int
     */
    public function ill_count($staffId, $year, $used,$leave_type)
    {
        $audit_model = new AuditRepository($this->lang);
        $applied_days = $audit_model->get_used_leave_days($staffId, $year, $leave_type);
        if(empty($applied_days))
            return $used;

        $other_used = array_sum(array_column($applied_days,'num'));
        return $used + $other_used;
    }


    public function check_leave_16($leaveStartTime)
    {
        //8天前日期
        $date_8 = date('Y-m-d', strtotime("-2 day"));//8天前

        if ($leaveStartTime < $date_8 )
            return $this->getTranslation()->_('leave_16_notice_la');
        return true;
    }

    // 根据员工 工作日 不同 返回不同 ph 返回 map list
    public function ph_days($staff_info)
    {
        $this->getDI()->get('logger')->write_log("ph_days ".json_encode($staff_info,JSON_UNESCAPED_UNICODE),'info');

        $storeServer = new SysStoreServer($this->lang,$this->timezone);
        $province_code = $storeServer->getProvince($staff_info);
        $bind['province_code'] = $province_code;
        $bind['type']       = $staff_info['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_5 ? [
            ThailandHolidayModel::TYPE_DEFAULT,
            ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_5,
        ] : [ThailandHolidayModel::TYPE_DEFAULT, ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_6];

        $data               = ThailandHolidayModel::find([
            'conditions' => 'type in ({type:array}) and province_code = :province_code: ',
            'columns'    => 'day,holiday_type',
            'bind'       => $bind,
        ])->toArray();

        //查询个人维度的公共假期
        $staffPublicHoliday = (new StaffPublicHolidayRepository($this->lang,$this->timeZone))->getStaffData($staff_info['staff_info_id']);
        if($data &&  $staffPublicHoliday) {
            $data = array_merge($data,$staffPublicHoliday);
        }

        return $data;
    }


    /**
     * 根据员工信息 计算区间内是否存在休息日 返回对应休息日日期数组
     * 用于计算 请假区间 扣除包含休息日的天数
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     */
    public function staff_off_days($staff_id, $start_date, $end_date = '')
    {
        if (empty($end_date))
            $end_date = $start_date;

        //获取员工信息
        $model       = new StaffRepository($this->lang);
        $staff_info  = $model->getStaffPosition($staff_id);

        $rest = $model->get_work_days_between($staff_id, $start_date, $end_date);
        $rest = !empty($rest) ? array_column($rest, 'date_at') : [];

        $holidays = $this->ph_days($staff_info);
	   
        if(!empty($holidays)){
	        $holidays =  array_column($holidays, 'day');
	        $rest = array_merge($rest,$holidays);
        }
        $this->logger->write_log("ignore rest_day {$staff_id} ".json_encode($rest),'info');
        return $rest;
    }



    //根据入职年限 发放额度
    public function get_sick_days($staff_info){
        //入职2年以下  14  2-5年 18 5年以上 22
        $hire_year = date('Y',strtotime($staff_info['hire_date']));
        $current_year = date('Y');
        $year_count = $current_year - $hire_year;

        if($year_count < 2)
            return 14;
        if($year_count < 5 && $year_count >= 2)
            return 18;
        else
            return 22;

    }

    //根据各种规则 计算员工额度 按月份 固化表
    public function get_year_leave_days($staff_info){
        //获取 当月额度
        $freeze_info = StaffDaysFreezeModel::findFirst("staff_info_id = {$staff_info['staff_info_id']} and leave_type = ".enums::LEAVE_TYPE_1);
        $freeze = 0;
        if(!empty($freeze_info->days))
            $freeze = $freeze_info->days;

        return round($freeze,2);
    }

    /**
     * 根据员工“入职日期”、“职级”计算员工年假（每年）基数
     * @param $staff_info
     * @param string $date
     * @return int
     */
    public function staff_year_should_days($staff_info, string $date = '')
    {
        if (empty($staff_info['hire_date'])) {
            return 0;
        }

        //如果没有传月份 默认当月
        if (empty($date)) {
            $date = date('Y-m-d');
        }

        //分三个等级  1级最低（12 和以下） 3级最高（15和以上） add为 入职满n年以后 的递增步长
        $grade_array = [
            1 => ['days' => 8, 'add' => 4],
            2 => ['days' => 12, 'add' => 2],
            3 => ['days' => 14, 'add' => 2],
        ];
        if ($staff_info['job_title_grade_v2'] <= 12) {
            $grade_days = $grade_array[1];
        } else {
            if ($staff_info['job_title_grade_v2'] <= 14) {
                $grade_days = $grade_array[2];
            } else {
                $grade_days = $grade_array[3];
            }
        }

        $hire_3 = strtotime("{$staff_info['hire_date']} +2 year");
        $hire_5 = strtotime("{$staff_info['hire_date']} +5 year");

        //当月不发放 取当前日期的上一个月
        $current_tmp = strtotime($date);

        //没满3年
        if ($current_tmp < $hire_3) {
            return $grade_days['days'];
        } else { //3年-5年以下
            if ($current_tmp < $hire_5) {
                return $grade_days['days'] + $grade_days['add'];
            } else { //5年和以上
                return $grade_days['days'] + 2 * $grade_days['add'];
            }
        }
    }

    /**
     * 根据司龄（指定日期/当前日期到入职日期间年数）获取年假基数
     * @param $staff_info
     * @param string $spec_date
     * @return int
     */
    public function getAnnualLeaveShouldDays($staff_info, string $spec_date = '')
    {
        $dep_model = new DepartmentRepository($this->lang);
        $c_staffs  = $dep_model->get_c_level();

        //应有额度
        if (in_array($staff_info['staff_info_id'], $c_staffs)) { //C Level
            $should_day = enums::C_LEVEL_DAYS;
        } else if ($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_2) { //合同工
            $should_day = enums::UN_NORMAL_STAFF_DAYS;
        } else {
            $should_day = $this->staff_year_should_days($staff_info, $spec_date);
        }
        return $should_day;
    }

    //年假额度 最大上限
    public function get_year_leave_max_days($staff_info)
    {

        $grade = $staff_info['job_title_grade_v2'];
        if(empty($grade) || $grade < 19)
            return 15;
        else
            return 17;
    }

    //扣除对应固化的额度天数  目前只有马来的补休
    public function sub_leave_days($staff_id, $days){
        $current_date = date('Y-m-d');
        //新计算方法 用补休假 拆分表
        $data = StaffLeaveInLieuModel::find([
            'conditions' => "state = 1 and staff_info_id = {$staff_id} and invalid_date >= '{$current_date}'"
        ]);

        if(empty($data))
            return false;

        $step = $days;
        foreach ($data as $da){
            if($step <= 0)
                break;

            $da->state = 2;
            $da->update();
            $step -= 0.5;
        }

        return true;

    }



    //获取每个员工的周期 和 失效日期 和 结算日期
    public function get_cycle($staff_info,$date_at = ''){
        if(empty($staff_info['hire_date']))
            return '';

        $return = array();

        //入职日期加13月 的1号为结算日 以后每个结算日 加一年
        $hire_01 = date('Y-m-01',strtotime($staff_info['hire_date']));
        $first_count = date('Y-m-01',strtotime("{$hire_01} +13 month"));
        $today = date('Y-m-d');
        if(!empty($date_at))
            $today = $date_at;
        
        $return['cycle'] = 1;//默认周期 为1 每满一个周期 +1
        if($today < $first_count){//还没 满第一个周期
            $return['count_day'] = $first_count;
            $return['invalid_day'] = date('Y-m-d',strtotime("{$hire_01} +7 month"));
            return $return;
        }
        $count_day = $first_count;
        while ($today >= $count_day){

            $count_day = date('Y-m-d',strtotime("{$count_day} +1 year"));
            $return['cycle']++;
        }

        //结算日 加 6个月 1号 为 失效
        $invalid_day = date('Y-m-d',strtotime("{$count_day} -6 month"));

        $return['count_day'] = $count_day;//结算日
        $return['invalid_day'] = $invalid_day;//结余 失效日

        return $return;
    }

    //获取每个员工的周期 和 失效日期 和 结算日期
    public function get_new_cycle($staff_info,$date_at = ''){
        if(empty($staff_info['hire_date']))
            return '';

        $return = array();

        //入职日期加13月 的1号为结算日 以后每个结算日 加一年
        $hire_01 = date('Y-m-d',strtotime($staff_info['hire_date']));
        $first_count = date('Y-m-d',strtotime("{$hire_01} +1 year"));
        $today = date('Y-m-d');
        if(!empty($date_at))
            $today = $date_at;

        $return['cycle'] = 1;//默认周期 为1 每满一个周期 +1
        if($today < $first_count){//还没 满第一个周期
            $return['count_day'] = $first_count;
            $return['invalid_day'] = date('Y-m-d',strtotime("{$hire_01} +6 month"));
            return $return;
        }
        $count_day = $first_count;
        while ($today >= $count_day){
            $count_day = date('Y-m-d',strtotime("{$count_day} +1 year"));
            $return['cycle']++;
        }

        //结算日 加 6个月 1号 为 失效
        $invalid_day = date('Y-m-d',strtotime("{$count_day} -6 month"));

        $return['count_day'] = $count_day;//结算日
        $return['invalid_day'] = $invalid_day;//结余 失效日

        return $return;
    }


    /**
     * 计算 时间区间 在有效期之前或之后 的天数求和 还要排除 这个人的休息日 ph等
     * @param $invalid_date 失效日期 或者 结算日期
     * @param $leave_info
     * @param $is_before 取日期之前还是 日期之后的天数 总和
     */
    public function count_invalid_days($invalid_date,$leave_info,$is_before = true){
        $start_date = date('Y-m-d',strtotime($leave_info['leave_start_time']));
        $end_date = date('Y-m-d',strtotime($leave_info['leave_end_time']));

        $staff_id = $leave_info['staff_info_id'];
        $holiday = $this->staff_off_days($staff_id,$start_date,$end_date);
        $step_date = $start_date;
        $num = 0;
        while ($step_date <= $end_date){
            if(in_array($step_date,$holiday)){
                $step_date = date('Y-m-d',strtotime("{$step_date} +1 day"));
                continue;
            }

            if($is_before && $step_date < $invalid_date){
                $num++;
            }

            if(!$is_before && $step_date >= $invalid_date){
                $num++;
            }

            if($step_date == $start_date && $leave_info['start_type'] == 2)//开始时间 是下午
                $num = $num - 0.5;

            if($step_date == $end_date && $leave_info['end_type'] == 1)//结束日期是上午
                $num = $num - 0.5;

            $step_date = date('Y-m-d',strtotime("{$step_date} +1 day"));
        }

        return $num;
    }


    //针对 已经固化的请假额度 非审批通过 需返还对应的天数
    public function re_back_leave_days($audit_info){
        //新需求 针对 补休假撤销驳回超时 返还 对应额度的补休加
        $limit_num = $audit_info['leave_day'] * 2;

        $data = StaffLeaveInLieuModel::find([
            'conditions' => "state = 2 and staff_info_id = {$audit_info['staff_info_id']} ",
            'order' => 'invalid_date desc',
            'limit' => $limit_num
        ]);

        if(!empty($data)){
            foreach ($data as $da){
                $da->state = 1;
                $da->update();
            }
        }

        return true;
    }

    //补休假 增加到 失效额度表
    public function add_remain_days($staff_id,$days,$add_date,$data_source,$operator_id = 10000){
        if(empty($add_date))
            return false;

        $invalid_date = date('Y-m-d',strtotime("{$add_date} + 90 day"));

        $model = new StaffLeaveInLieuModel();
        for($i = $days; $i > 0; $i -= 0.5){
            $clone = clone $model;

            $row['staff_info_id'] = $staff_id;
            $row['date_at'] = $add_date;
            $row['invalid_date'] = $invalid_date;
            $row['days'] = 0.5;
            $row['data_source'] = $data_source;
            $row['operator_id'] = 10000;
            if($data_source == 3)
                $row['operator_id'] = $operator_id;

            $clone->create($row);
        }

        return true;
    }

    //获取当前剩余 有效额度
    public function get_remain_days($staff_id,$is_detail = 0,$start_date = ''){
        $current_date = date('Y-m-d');
        if(!empty($start_date))
            $current_date = $start_date;

        //获取当前时间 没失效的额度天数
        $info = StaffLeaveInLieuModel::find([
            'columns' => "state,date_at,days,invalid_date",
            'conditions' => "staff_info_id = {$staff_id} and invalid_date >= '{$current_date}'",
        ])->toArray();

        if($is_detail && $is_detail == 1)
            return $info;

        $return['in_all'] = $return['used'] = 0;
        if(!empty($info)){
            foreach ($info as $in){
                $return['in_all'] += 0.5;
                if($in['state'] == 2)
                    $return['used'] += 0.5;
            }
        }

        return $return;
    }




    /**
     * 批量获取 员工 指定年的 已经请的假期额度
     * @param $staff_ids 员工工号 数组
     * @param string $year 指定年份
     * @param $leave_type 指定 假期类型
     */
    public function get_batch_leave_days($staff_ids,$year ,$leave_type){

        if (empty($staff_ids))
            return false;

        $id_str = implode(',',$staff_ids);
        $sql = " select sum(if(s.`type`=0,1,0.5)) as num
                ,group_concat(s.id) as split_id
                ,a.leave_type
                ,group_concat(distinct a.audit_id) audit_ids
                ,a.staff_info_id
                from staff_audit_leave_split s
                join staff_audit a on a.audit_id = s.audit_id
                where a.staff_info_id in (:id_str)
                and a.audit_type = 2 
                and a.status in (1,2) 
                and a.leave_type = :leave_type
                and s.year_at = :year
                and a.parent_id = 0
                group by a.staff_info_id
                ";

        return $this->getDI()->get('db')->query($sql,['id_str' => $id_str,'leave_type' => $leave_type,'year' => $year])->fetchAll(\Phalcon\Db::FETCH_ASSOC);

    }



    //跨年区间 获取对应年的日期
    public function format_year_date($leaveType,$leaveStartTime,$leaveEndTime,$holidays){
        $audit_server = new AuditServer($this->lang,$this->timezone);
        $key   = date('Y-m-d', strtotime($leaveStartTime));
        $end_date   = date('Y-m-d', strtotime($leaveEndTime));
        $date_array = array();
        while ($key <= $end_date) {
            if (in_array($key, $holidays) && in_array($leaveType, $audit_server->sub_day)) {
                $key = date("Y-m-d", strtotime("+1 day", strtotime($key)));
                continue;
            }
            $y                = date('Y', strtotime($key));
            $date_array[$y][] = $key;
            $key              = date("Y-m-d", strtotime("+1 day", strtotime($key)));
        }

        return array_values($date_array);
    }
    //获取超出 有效期的日期 和天数
    public function get_invalid_date($leave_info,$invalid_date = ''){
        $date_list = $this->format_year_date($leave_info['leave_type'],$leave_info['leave_start_time'],$leave_info['leave_end_time'],$leave_info['holidays']);
        if(empty($invalid_date))
            $invalid_date = date('Y') . '-' . enums::LEAVE_INVALID_DATE;

        $return['invalid_date'] = array();
        $return['num'] = 0;
        //只请一天的情况
        if($leave_info['leave_start_time'] == $leave_info['leave_end_time']){
            if($leave_info['leave_start_time'] > $invalid_date)
                $return['invalid_date'][] = $leave_info['leave_start_time'];
            //开始和结束类型 如果不一样 说明是 整天 相等 就是半天
            $return['num'] = ($leave_info['leave_start_type'] == $leave_info['leave_end_type']) ? 0.5 : 1;

        }else{//超过一天
            foreach ($date_list as $y){
                foreach ($y as $date){
                    if($date <= $invalid_date)
                        continue;

                    $return['num']++;
                    //如果正好是 区间两头 需要判断 是半天还是一天
                    if($date == $leave_info['leave_start_time'] && $leave_info['leave_start_type'] != 1)
                        $return['num'] -= 0.5;
                    if($date == $leave_info['leave_end_time'] && $leave_info['leave_end_type'] != 2)
                        $return['num'] -= 0.5;

                    $return['invalid_date'][] = $date;

                }
            }
        }

        return $return;
    }


    //存在 需要用到去年额度的 改下标记年
    public function year_flag_new($staffId,$insert,$leave_param){
        $left_days = $leave_param['left_days'];
        $invalid_date = $leave_param['invalid_date'] ?? array();
        $need_last_year = 0;
        // 拼接 拆分表 归属年 year_at 字段
        $add_row = array();//出现不够减情况 额外新增一条  merge到 insert
        foreach ($insert as $k => $in) {
            //如果 是在有效期外的 日期 不操作标记年
            if(in_array($in['date_at'],$invalid_date))
                continue;
            $duration = empty($in['type']) ? 1 : 0.5;
            if ($left_days > 0 && ($left_days - $duration >= 0)) {//还够减
                $insert[$k]['year_at'] = date('Y', strtotime("-1 year"));
                $left_days             = $left_days - $duration;
                $need_last_year++;
            } else if ($left_days > 0 && ($left_days - $duration < 0)) {//不够减了（剩余0。5 本次记录 需要1天 只能是这种情况 把本条记录更改为半天 额外新增一条半天记录）
                $insert[$k]['type']    = 1;
                $insert[$k]['year_at'] = date('Y', strtotime("-1 year"));

                //拼接剩下半天 标记归属年 为今年 merge
                $add_row[0]['staff_info_id'] = $staffId;
                $add_row[0]['date_at']       = $in['date_at'];
                $add_row[0]['type']          = 2;
                $add_row[0]['year_at']       = date('Y');

                $left_days = 0;//减没了
                $need_last_year+= 0.5;
            }
        }

        if(!empty($leave_param['need_num']) && $leave_param['need_num'] === true)
            return $need_last_year;

        if (!empty($add_row))
            $insert = array_merge($insert, $add_row);

        return $insert;
    }


    //操作扣除和返还 其他假期类型的额度
    public function update_leave_days($staff_id,$up_type,$param){
        $leave_type = intval($param['leave_type']);
        $year = date('Y');

        $info = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
            'bind' => ['staff_id' => $staff_id,'year_at' => $year,'leave_type' => $leave_type]
        ]);
        if($up_type == enums::YEAR_ADD){//申请操作 需要扣除
            if(!empty($info) && $param['this_year'] > 0){
                $info->days -= $param['this_year'];
                $info->leave_days += $param['this_year'];
                $info->update();
            }

            if(!empty($param['last_year'])){
                $last_info = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
                    'bind' => ['staff_id' => $staff_id,'year_at' => date('Y',strtotime('-1 year')),'leave_type' => $leave_type]
                ]);
                if(!empty($last_info)){
                    $last_info->days -= $param['last_year'];
                    $last_info->leave_days += $param['last_year'];
                    $last_info->update();
                }
            }
        }

        if($up_type == enums::YEAR_RE_BACK){//返还额度
            $audit_id = intval($param['audit_id']);
            $split_info = StaffAuditLeaveSplitModel::find([
                'conditions' => "audit_id = :audit_id:",
                'bind' => ['audit_id' => $audit_id]
            ])->toArray();

            if(empty($split_info))
                throw new \Exception('update_year_days can not find split info ' . json_encode($param));
            $current = date('Y');
            $last = date('Y',strtotime('-1 year'));
            $this_year = $last_year = 0;
            foreach ($split_info as $split){
                if($split['year_at'] == $current){
                    $this_year = empty($split['type']) ? ($this_year + 1) : ($this_year + 0.5);
                }
                if($split['year_at'] == $last){
                    $last_year = empty($split['type']) ? ($last_year + 1) : ($last_year + 0.5);
                }
            }

            if(!empty($info) && $this_year > 0){
                $info->days += $this_year;
                $info->leave_days -= $this_year;
                $info->update();
            }

            if(!empty($last_year)){
                $last_info = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
                    'bind' => ['staff_id' => $staff_id,'year_at' => $last,'leave_type' => $leave_type]
                ]);
                if(!empty($last_info)){
                    $last_info->days += $last_year;
                    $last_info->leave_days -= $last_year;
                    $last_info->update();
                }
            }

        }
        $this->logger->write_log("假期余额额度操作 {$up_type} {$staff_id} ".json_encode($param),'info');

    }

    //发放跨国探亲假操作 并返回对应的额度
    public function send_country_leave_days($staff_info){
        $return['limit'] = $return['last_limit'] = $return['sub'] = $return['last_sub'] = 0;
        $send_days = ($staff_info['job_title_grade_v2'] >= 19) ? 3 : 1;
        $info = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year = :year_at: and leave_type = :leave_type:',
            'bind' => ['staff_id' => $staff_info['staff_info_id'],'year_at' => date('Y'),'leave_type' => enums::LEAVE_TYPE_19]
        ]);

        if(!empty($info)){
            $return['limit'] = $info->days + $info->leave_days;
            $return['sub'] = $info->days;
            return $return;
        }

        $row['staff_info_id'] = $staff_info['staff_info_id'];
        $row['leave_type'] = enums::LEAVE_TYPE_19;
        $row['year'] = date('Y');
        $row['days'] = $return['limit'] = $return['sub'] = $send_days;
        $model = new StaffLeaveRemainDaysModel();
        $model->create($row);

        return $return;

    }



    //格式化 详情页显示的 上下午 还是 前后半天
    public function formatLeaveTime($leaveInfo){
        $t = $this->getTranslation();
        //临时方案 全改成 前后半天
        $startText = $leaveInfo['leave_start_time'] . ' ' .($leaveInfo['leave_start_type'] == 1 ? $t->_('half_am') : $t->_('half_pm'));
        $endText = $leaveInfo['leave_end_time'] . ' ' .($leaveInfo['leave_end_type'] == 1 ? $t->_('half_am') : $t->_('half_pm'));
        return [$startText,$endText];


        $attendanceServer = new AttendanceServer($this->lang,$this->timezone);
        $startShift = $attendanceServer->getStaffShiftInfoByDate($leaveInfo['staff_info_id'],$leaveInfo['leave_start_time']);
        $endShift = $attendanceServer->getStaffShiftInfoByDate($leaveInfo['staff_info_id'],$leaveInfo['leave_end_time']);

        $t = $this->getTranslation();
        //拼接 开始时间
        $startText = $leaveInfo['leave_start_time'] . ' ' .($leaveInfo['leave_start_type'] == 1 ? $t->_('morning') : $t->_('afternoon'));
        if($startShift['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE){
            $startText = $leaveInfo['leave_start_time'] . ' ' .($leaveInfo['leave_start_type'] == 1 ? $t->_('half_am') : $t->_('half_pm'));
        }

        //拼接结束时间
        $endText = $leaveInfo['leave_end_time'] . ' ' .($leaveInfo['leave_end_type'] == 1 ? $t->_('morning') : $t->_('afternoon'));
        if($endShift['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE){
            $endText = $leaveInfo['leave_end_time'] . ' ' .($leaveInfo['leave_end_type'] == 1 ? $t->_('half_am') : $t->_('half_pm'));
        }
        return [$startText,$endText];
    }

    //申请假期 保存 写父类里面 就只能 rebuild 不能跳了

    /**
     * @throws ValidationException
     */
    public function saveVacation($param){
        $this->leaveObject = $this->getLeaveObj(intval($param['leave_type']));
        $auditServer = new AuditServer($this->lang, $this->timeZone);
        $staffRe     = new StaffRepository($this->lang);
        $staffInfo   = $staffRe->getStaffPosition($param['staff_id']);
        $typeData    = $auditServer->staffLeaveType($staffInfo);
        $leave_lang  = AuditRepository::$leave_type;

        if (empty($typeData)) {
            throw new ValidationException('wrong leave type');
        }
        if (!in_array($param['leave_type'], array_keys($typeData)) && $param['leave_type'] != enums::LEAVE_TYPE_15) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004') . $this->getTranslation()->_($leave_lang[$param['leave_type']]));
        }
        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            $audit_id = $this->leaveObject->handleCreate($param);

            //没生成id
            if (empty($audit_id)) {
                throw new ValidationException($this->getTranslation()->_('1009'));
            }

            //非工具操作申请 创建审批相关
            if (empty($param['is_bi'])) {
                $param['time_out'] = $this->leaveObject->timeOut ?? null;
                $auditServer->saveApproval($audit_id, $param);
            }

            $db->commit();

            $return['data'] = ['leave_day' => $this->leaveObject->leave_day ?? 0];

        }catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
        if(!empty($param['is_bi']) && $param['is_bi'] == 1) {
            //https://flashexpress.feishu.cn/docx/Tcw8dzXraoWFqhxJvEIcIxqznxb
            //请假审批通过同步bi，重算处罚数据
            $sync_server = new SyncServer($this->lang, $this->timezone);
            $params      = [
                'staff_id'         => $param['staff_id'],
                'leave_start_date' => $param['leave_start_time'],
                'leave_end_date'   => $param['leave_end_time'],
            ];
            $sync_server->sync_fbi_attendance($params, 'abnormal.staff_leave_request');
        }

        return $this->checkReturn($return);
    }

    //获取对应假期的额度
    public function getVacationDays($staffId,$leaveType, $extend = []){
        $param['staff_id'] = $staffId;
        $param['leave_type'] = $leaveType;
        $param = array_merge($param,$extend);
        $leaveObject = $this->getLeaveObj($param['leave_type']);
        return $leaveObject->handleSearch($param);
    }

    //非审核通过 撤销操作 返还额度
    public function cancelVacation($auditInfo,$staffInfo,$state, $extend = []){
        $extend['status'] = $state;
        $this->leaveObject = $this->getLeaveObj(intval($auditInfo['leave_type']));
        $this->leaveObject->returnRemainDays($auditInfo['audit_id'],$staffInfo,$extend);
    }


    //对应本国的 所有类型 映射类
    public function getLeaveObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_1:
                return new AnnualServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_2:
                break;
            case enums::LEAVE_TYPE_19://跨国探亲
                return new InternationalServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_4:
                return new MaternityServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_42:
                return new MilitaryServer($this->lang,$this->timeZone);
            default:
                throw new ValidationException('WRONG TYPE');
            //....
        }
    }
    //task 初始化额度 调用
    public function getInstanceObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_19://跨国探亲
                $leaveObj = InternationalServer::getInstance($this->lang,$this->timezone);
                break;
            case enums::LEAVE_TYPE_4://产假
                $leaveObj = MaternityServer::getInstance($this->lang,$this->timezone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }



    //判断员工班次 和请假时间 返回二次确认
    public function checkShiftLeave($staffId,$leaveParam){
        //前提条件 请假日期 和 当前日期 是同一天
        $today = date('Y-m-d');
        $date = date('Y-m-d',strtotime($leaveParam['leave_start_time']));
        if($today != $date){
            return ;
        }

        //查询根据请假日期对应班次
        $shiftServer = new AttendanceCalendarV2Server();
        $shiftInfo = $shiftServer->getDailyShiftInfo($staffId,$date,$date);
        //没有班次
        if(empty($shiftInfo[$date])){
            return;
        }
        $startTmp = strtotime($shiftInfo[$date]['first_start']);
        $currentTmp = time();
        if($leaveParam['leave_start_type'] == StaffAuditModel::LEAVE_PM){
            $startTmp = strtotime($shiftInfo[$date]['middle_rest_end']);//中途休息结束时间就是下午上班时间
            //跨天
            if($shiftInfo[$date]['first_start'] > $shiftInfo[$date]['middle_rest_end']){
                $startTmp = $startTmp + (24 * 3600);
            }
        }
        if($currentTmp >= $startTmp){
            throw new ValidationException($this->getTranslation()->_('leave_shift_notice'),10086);
        }

        if((2 * 3600 + $currentTmp) >= $startTmp){
            throw new ValidationException($this->getTranslation()->_('leave_shift_notice_close'),10086);
        }
        return;
    }


    //获取 额度详情信息列表
    public function getAnnualDetail($param){
        $leaveObject = $this->getLeaveObj(enums::LEAVE_TYPE_1);
        return $leaveObject->detailList($param);
    }


}