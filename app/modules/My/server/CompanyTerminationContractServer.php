<?php

namespace FlashExpress\bi\App\Modules\My\Server;


use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MilesEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\Mail;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\Models\backyard\CompanyTerminationContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\ResignFileModel;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\formPdfServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use GuzzleHttp\Exception\GuzzleException;

class CompanyTerminationContractServer extends \FlashExpress\bi\App\Server\CompanyTerminationContractServer
{

    /**
     * 解约原因(每个国家分开写)
     * @return array[]
     */
    protected function getReasonEnum(): array
    {
        $t = $this->getTranslation();
        return [
//            ['value' => '9', 'label' => $t->_('termination_reason_9')],
//            ['value' => '10', 'label' => $t->_('termination_reason_10')],
//            ['value' => '11', 'label' => $t->_('termination_reason_11')],
//            ['value' => '12', 'label' => $t->_('termination_reason_12')],
//            ['value' => '13', 'label' => $t->_('termination_reason_13')],
//            ['value' => '14', 'label' => $t->_('termination_reason_14')],
            ['value' => '15', 'label' => $t->_('termination_reason_15')],
            ['value' => '16', 'label' => $t->_('termination_reason_16')],
            ['value' => '17', 'label' => $t->_('termination_reason_17')],
            ['value' => '18', 'label' => $t->_('termination_reason_18')],
            ['value' => '19', 'label' => $t->_('termination_reason_19')],
            ['value' => '20', 'label' => $t->_('termination_reason_20')],
            ['value' => '21', 'label' => $t->_('termination_reason_21')],
            ['value' => '22', 'label' => $t->_('termination_reason_22')],
            ['value' => '23', 'label' => $t->_('termination_reason_23')],
            ['value' => '24', 'label' => $t->_('termination_reason_24')],
            ['value' => '25', 'label' => $t->_('termination_reason_25')],
            ['value' => '26', 'label' => $t->_('termination_reason_26')],
            ['value' => '27', 'label' => $t->_('termination_reason_27')],
            ['value' => '28', 'label' => $t->_('termination_reason_28')],
            ['value' => '29', 'label' => $t->_('termination_reason_29')],
        ];
    }

    public function getWorkflowParams($auditId, $user, $state = null): array
    {
        $model = CompanyTerminationContractModel::findFirstById($auditId);
        $num   = $this->getLast3DayStoreTerminationNum($model->store_id, $auditId);

        return [
            //3天内同一网点解约个人代理人数是否大于等于3人
            'is_termination_3_num'         => $num >= 3 ? 1 : 2,
            //员工问题解约
            'is_staff_trouble_termination' => $model->termination_type == CompanyTerminationContractModel::TERMINATION_TYPE_IS_STAFF ? 1 : 2,
        ];
    }

    /**
     * 添加解约申请
     * @param array $paramIn
     * @return array
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function add(array $paramIn): array
    {
        //[1]获取传入参数
        $submitter_id          = $this->processingDefault($paramIn, 'submitter_id');
        $staffId               = $this->processingDefault($paramIn, 'staff_info_id');
        $reason                = $this->processingDefault($paramIn, 'reason');
        $remark                = $this->processingDefault($paramIn, 'remark');
        $terminationType       = $this->processingDefault($paramIn, 'termination_type');
        $terminationDate       = $this->processingDefault($paramIn, 'termination_date');
        $violation_date        = $this->processingDefault($paramIn, 'violation_date');
        $theft_type            = $this->processingDefault($paramIn, 'theft_type');
        $harassment_target     = $this->processingDefault($paramIn, 'harassment_target');
        $terminationAttachment = $this->processingDefault($paramIn, 'termination_attachment', 3);

        if (!in_array($terminationType, [
            CompanyTerminationContractModel::TERMINATION_TYPE_NOT_STAFF,
            CompanyTerminationContractModel::TERMINATION_TYPE_IS_STAFF,
        ])) {
            throw new ValidationException('Termination type error');
        }
        if ($terminationType == CompanyTerminationContractModel::TERMINATION_TYPE_IS_STAFF && empty($terminationDate)) {
            throw new ValidationException('Termination date error');
        }
        //[2]逻辑验证
        $staffInfo = $this->checkLogic($staffId, $submitter_id);


        //获取业务数据
        $biz_data = $this->getBizData($staffId, $staffInfo['sys_store_id']);

        $db = $this->getDI()->get("db");
        //开启事务
        $db->begin();
        try {
            //[3]保存申请数据

            $model                   = new CompanyTerminationContractModel();
            $model->submitter_id     = $submitter_id;
            $model->staff_info_id    = $staffId;
            $model->store_id         = $staffInfo['sys_store_id'];
            $model->termination_type = $terminationType;
            if ($terminationType == CompanyTerminationContractModel::TERMINATION_TYPE_NOT_STAFF) {
                $model->remark = $remark;
            }
            if ($terminationType == CompanyTerminationContractModel::TERMINATION_TYPE_IS_STAFF) {
                $model->reason                 = $reason;
                $model->leave_date             = $terminationDate;
                if (!empty($violation_date)) {
                    $model->violation_date = $violation_date;
                }
                if (!empty($theft_type)) {
                    $model->theft_type = $theft_type;
                }
                if (!empty($harassment_target)) {
                    $model->harassment_target = $harassment_target;
                }
                $model->termination_attachment = json_encode($terminationAttachment, JSON_UNESCAPED_UNICODE);
            }

            $model->biz_data  = json_encode($biz_data, JSON_UNESCAPED_UNICODE);
            $model->status    = enums::$audit_status['panding'];
            $model->serial_no = 'CTC' . $this->getID();
            if (!$model->save()) {
                throw new \Exception($this->getTranslation()->_('4008'));
            }

            $approvalServer = new ApprovalServer($this->lang, $this->timezone);
            $requestId      = $approvalServer->create($model->id,
                AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT,
                $submitter_id);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
        } catch (Exception $e) {
            $db->rollBack(); //回滚
            throw $e;
        }
        //提交事务
        $db->commit();

        return $this->checkReturn(['data' => ['id' => $model->id]]);
    }

    /**
     * 赋值审批详情详细信息 主体参数
     * @param $detailLists
     * @param $result
     */
    public function detailMainAssign(&$detailLists, $result)
    {
        $detailLists['termination_type']          = $this->getTranslation()->_('termination_type_' . $result['termination_type']); //解约类型
        $detailLists['independent_contractor_id'] = $result['independent_contractor_id'];                                          //个人代理ID
        $detailLists['agent_name']                = $result['agent_name'];
        $detailLists['agent_department']          = $result['agent_department'];
        $detailLists['staff_job_title']           = $result['staff_job_title'];
        $detailLists['area']                      = $result['manage_region_name'];
        $detailLists['district']                  = $result['manage_piece_name'];
        $detailLists['agent_store']               = $result['agent_store'];
        $detailLists['hire_date']                 = $result['hire_date'];

        $detailLists['2week_delivery_avg_duration'] = $result['2week_delivery_avg_duration'];//派送时长
        $detailLists['2week_handover_avg_amount']   = $result['2week_handover_avg_amount'];  //交接量
        $detailLists['2week_delivery_avg_amount']   = $result['2week_delivery_avg_amount'];  //妥投量
        $detailLists['customer_complaints_num']     = $result['customer_complaints_num'];    //客户投诉数量
        $detailLists['3day_store_termination_num']  = $result['3day_store_termination_num']; //3天内解约人数

        if ($result['termination_type'] == CompanyTerminationContractModel::TERMINATION_TYPE_NOT_STAFF) {
            $detailLists['termination_reason'] = $result['remark'];
        }
        if ($result['termination_type'] == CompanyTerminationContractModel::TERMINATION_TYPE_IS_STAFF) {
            $detailLists['termination_reason']     = $result['reason_text'];
            // 违规日期
            if (!empty($result['violation_date'])){
                $detailLists['violation_date']     = $result['violation_date'];
            }
            // 偷窃类型
            if (!empty($result['theft_type'])){
                $detailLists['theft_type_text']     = $this->getTranslation()->_('theft_type_'.$result['theft_type']);
            }
            // 骚扰对象
            if (!empty($result['harassment_target'])){
                $detailLists['harassment_target_text']     = $this->getTranslation()->_('harassment_target_'.$result['harassment_target']);
            }
            $detailLists['termination_date']       = $result['leave_date'];
            $detailLists['termination_attachment'] = empty($result['termination_attachment']) ? [] : (array)json_decode($result['termination_attachment'],
                true);
        }
    }

    /**
     * 获取解约离职日期
     * @param CompanyTerminationContractModel $model
     * @return false|string
     */
    public function getLeaveDate(CompanyTerminationContractModel $model)
    {
        if ($model->termination_type == CompanyTerminationContractModel::TERMINATION_TYPE_IS_STAFF) {
            return $model->leave_date;
        }
        return date('Y-m-d', strtotime('+8 day'));
    }

    /**
     * 发送解约消息和邮件
     * @param $info
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function sendNoticePop($info): bool
    {
        if (empty($info['staff_info_id'])) {
            $this->logger->write_log('sendNoticePop  params' . json_encode($info));
            return false;
        }
        //上级
        $staffInfoServer = new StaffServer();
        $staffInfo       = $staffInfoServer->getStaffById($info['staff_info_id']);

        //获取员工和上级的语言包
        $staffServer   = new StaffServer();
        $staffLangInfo = $staffServer->getBatchStaffLanguage([$info['staff_info_id'], $info['submitter_id']]);
        $t             = $this->getTranslation($staffLangInfo[$info['staff_info_id']]);
        //给员工发消息
        //终止合同通知
        $title = $t->_('company_termination_contract_notice');

        //给员工发消息和邮件(非员工问题解约)
        if ($info['termination_type'] == CompanyTerminationContractModel::TERMINATION_TYPE_NOT_STAFF) {
            $staffInfo['leave_date']             = $info['leave_date'];
            $staffInfo['last_work_date']         = $info['last_work_date'];
            $terminationFileUrl                  = $this->makeTerminationFileUrl($staffInfo);
            $message_param['staff_users']        = [$info['staff_info_id']];
            $message_param['message_title']      = $title;
            $message_param['message_content']    = $terminationFileUrl ? addslashes("<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' /><iframe src='https://vpdf.flashexpress.my/web/viewer.html?file={$terminationFileUrl}' width='100%' height='98%'></iframe>") : '';
            $message_param['staff_info_ids_str'] = $info['staff_info_id'];
            $message_param['id']                 = time() . $info['staff_info_id'] . rand(1000000, 9999999);
            $message_param['category']           = -1;
            $bi_rpc                              = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
            $bi_rpc->setParams($message_param);
            $bi_rpc->execute();

            $this->sendEmailToContract($staffInfo['personal_email'], $terminationFileUrl, $title, $t,$info['staff_info_id']);

            
        }

        //给上级发消息
        $t = $this->getTranslation($staffLangInfo[$info['submitter_id']]);
        //终止合同申请审批通过通知
        $title                               = $t->_('company_termination_contract_approved_notice');
        $content                             = $t->_('company_termination_contract_approved_notice_content_my', [
            'termination_type' => $t->_('termination_type_' . $info['termination_type']),
            'termination_date' => $info['leave_date'],
            'staff_info_id'    => $info['staff_info_id'],
            'name'             => $staffInfo['name'],
        ]);
        $message_param['staff_users']        = [$info['submitter_id']];
        $message_param['message_title']      = $title;
        $message_param['message_content']    = addslashes("<div style='font-size: 35px'>" . $content . '</div>');
        $message_param['staff_info_ids_str'] = $info['submitter_id'];
        $message_param['id']                 = time() . $info['submitter_id'] . rand(1000000, 9999999);;
        $message_param['category'] = -1;
        $bi_rpc                    = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
        $bi_rpc->setParams($message_param);
        $bi_rpc->execute();
        return true;
    }

    /**
     * 给员工发送解约邮件
     * @param $email
     * @param $terminationFileUrl
     * @param $title
     * @param $t
     */
    public function sendEmailToContract($email, $terminationFileUrl, $title, $t,$staff_info_id)
    {
        echo json_encode([$email, $terminationFileUrl], JSON_UNESCAPED_UNICODE) . PHP_EOL;
        $this->logger->write_log([$email, $terminationFileUrl], 'info');
        if ($email && $terminationFileUrl) {
            $filepath = sys_get_temp_dir() . '/' . md5(time()) . '.pdf';
            file_put_contents($filepath, file_get_contents($terminationFileUrl));
            $sendEmail = Mail::send([$email], $title,
                $t->_('company_termination_contract_notice_email'), $filepath,
                $t->_('company_termination_contract_notice_attachment_name') . '.pdf');
            unlink($filepath);
            if (!$sendEmail) {
                $this->logger->write_log('sendEmailToContract, 发送邮件失败:' . $email, 'notice');
            }
        }
        $pdfFileName    = str_replace('/', '', $t->_('company_termination_contract_notice_attachment_name'));
        $resign_file = new ResignFileModel();
        $resign_file->staff_info_id = $staff_info_id;
        $resign_file->type = ResignFileModel::TYPE_AGENT_TERMINATION_CONTRACT;
        $resign_file->file_url = $terminationFileUrl;
        $resign_file->file_name = $pdfFileName;
        $resign_file->email_title = $title;
        $resign_file->email_content = $t->_('company_termination_contract_notice_email');
        $resign_file->operate_id = MilesEnums::SYSTEM_STAFF_ID;
        $resign_file->save();
    }

    /**
     * 员工问题给员工解约发送邮件
     * @param $terminationDate
     */
    public function staffTroubleSendEmail($terminationDate)
    {
        $find = CompanyTerminationContractModel::find([
            'conditions' => 'termination_type=:termination_type: and leave_date = :leave_date: and status = :status: ',
            'bind'       => [
                'termination_type' => CompanyTerminationContractModel::TERMINATION_TYPE_IS_STAFF,
                'leave_date'       => $terminationDate,
                'status'           => enums::$audit_status['approved'],
            ],
        ])->toArray();
        if (empty($find)) {
            return;
        }
        $staffIds        = array_column($find, 'staff_info_id');
        $staffInfoServer = new StaffServer();
        $staffLangInfo   = $staffInfoServer->getBatchStaffLanguage($staffIds);
        $staffFind       = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,name,personal_email,signing_date',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffIds],
        ])->toArray();
        $staffMap        = array_column($staffFind, null, 'staff_info_id');
        foreach ($find as $item) {
            $t         = $this->getTranslation($staffLangInfo[$item['staff_info_id']]);
            $staffInfo = [
                'name'               => $staffMap[$item['staff_info_id']]['name'],
                'termination_reason' => $this->terminationReasonMyMap($item['reason'],$item['violation_date'],$item['theft_type'],$item['harassment_target']),
                'termination_reason_provision' => $this->terminationReasonProvision($item['reason']),
                'staff_info_id'      => $item['staff_info_id'],
                'signing_date'      => $staffMap[$item['staff_info_id']]['signing_date'] ?? '',
            ];
            echo json_encode($staffInfo, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $terminationFileUrl = $this->makeTerminationFileUrl($staffInfo);
            $title              = $t->_('company_termination_contract_notice');
            $this->sendEmailToContract($staffMap[$item['staff_info_id']]['personal_email'], $terminationFileUrl, $title,
                $t,$item['staff_info_id']);
        }
    }

    /**
     * 解约原因 my lang
     * @param $type
     * @return string
     */
    public function terminationReasonMyMap($type,$violation_date,$theft_type = 0,$harassment_target = 0): string
    {
        $t = $this->getTranslation("en");
        if ($type == 19 && !empty($theft_type)){
            $type_text = $t->_('theft_type_'.$theft_type);
        }elseif ($type == 26 && !empty($harassment_target)){
            $type_text = $t->_('harassment_target_'.$harassment_target);
        }else{
            $type_text = '';
        }
        $map = [
//            '9'  => 'menyebabkan atau mengakibatkan Syarikat mengalami kerugian melalui perbuatan sengaja, ketinggalan (omission) atau kecuaian besar (gross negligence) Kontraktor.',
//            '10' => 'enggan dan/atau mengabaikan untuk melaksanakan Perkhidmatan selama dua (2) hari berturut-turut selepas menerima Perkhidmatan yang terjadual.',
//            '11' => 'disyaki atau bersalah atas sebarang tindakan penipuan, suapan, rasuah, salah nyata kepada Syarikat.',
//            '12' => 'berhenti atau mengugut untuk berhenti melaksanakan perkhidmatan.',
//            '13' => 'melanggar terma di bawah Perjanjian termasuk Tahap Perkhidmatan, Garis Panduan Perkhidmatan.',
//            '14' => 'menjadi muflis atau memasuki pembubaran atau mempunyai penerima yang dilantik berkenaan dengan semua atau mana-mana bahagian asetnya atau mengambil atau mengalami sebarang tindakan yang serupa akibat daripada hutang.',
            '15' => 'gagal memulangkan COD kepada Syarikat berdasarkan Tahap Perkhidmatan pada atau sekitar '.$violation_date.'.',
            '16' => 'gagal memulangkan bungkusan kepada Syarikat berdasarkan Tahap Perkhidmatan pada atau sekitar '.$violation_date.'.',
            '17' => 'agal mematuhi SOP penghantaran bungkusan dan/atau pengambilan bungkusan berdasarkan Tahap Perkhidmatan pada atau sekitar '.$violation_date.'.',
            '18' => 'gagal menjaga bungkusan dengan baik berdasarkan Tahap Perkhidmatan pada atau sekitar '.$violation_date.'.',
            '19' => 'terlibat dalam pencurian '.$type_text.' Syarikat pada atau sekitar '.$violation_date.' dan melanggari Tahap Perkhidmatan.',
            '20' => 'memberi status palsu berkenaan dengan POD dan/atau status bungkusan pada atau sekitar '.$violation_date.'  dan melanggari Tahap Perkhidmatan.',
            '21' => 'merokok di kawasan SP pada atau sekitar '.$violation_date.'  dan melanggari Tahap Perkhidmatan.',
            '22' => 'menyimpan dan/atau menggunakan alkohol dan/atau dadah semasa Perkhidmatan pada atau sekitar '.$violation_date.' dan melanggari Tahap Perkhidmatan.',
            '23' => 'terlibat dalam pergaduhan dengan kakitangan SP dan/atau orang lain di SP pada atau sekitar '.$violation_date.'  dan melanggari Tahap Perkhidmatan.',
            '24' => 'terlibat dalam menyebabkan kecederaan kakitangan SP dan/atau orang lain di SP pada atau sekitar '.$violation_date.' dan melanggari Tahap Perkhidmatan.',
            '25' => 'terlibat dalam kerosakan harta Syarikat pada atau sekitar '.$violation_date.' dan melanggari Tahap Perkhidmatan.',
            '26' => 'terlibat dalam tindakan berunsur gangguan seksual terhadap '.$type_text.' pada atau sekitar '.$violation_date.' dan melanggari Tahap Perkhidmatan.',
            '27' => 'berkelakuan tidak sopan terhadap pelanggan Syarikat pada atau sekitar '.$violation_date.' dan melanggari Tahap Perkhidmatan.',
            '28' => 'terlibat dalam tindakan yang mencemarkan reputasi atau kredibiliti Syarikat pada atau sekitar '.$violation_date.' dan melanggari Tahap Perkhidmatan.',
            '29' => 'gagal mencapai KPI yang ditetapkan oleh Syarikat di bawah Tahap Perkhidmatan pada atau sekitar '.$violation_date.'.',
        
        ];
        return $map[$type] ?? '';
    }
    
    /**
     * 原因对应条款
     * @param $type
     * @return string
     */
    public function  terminationReasonProvision($type): string
    {
        $map = [
            '15'=>'2.4(b)',
            '16'=>'2.4(b)',
            '17'=>'2.4(a)',
            '18'=>'2.4(a)',
            '19'=>'2.4(a)',
            '20'=>'2.4(a)',
            '21'=>'2.4(a)',
            '22'=>'2.4(a)',
            '23'=>'2.4(a)',
            '24'=>'2.4(a)',
            '25'=>'2.4(a)',
            '26'=>'2.4(a)',
            '27'=>'2.4(a)',
            '28'=>'2.4(a)',
            '29'=>'2.4(g)',
        ];
        return $map[$type] ?? '';
    }
    /**
     * 获取终止合同信函详
     * @param $staffInfo
     * @return mixed|string
     */
    protected function makeTerminationFileUrl($staffInfo)
    {
        $filePath = APP_PATH . "/views/termination_contract/termination_contract.ftl";
        if (isset($staffInfo['termination_reason'])) {
            $filePath = APP_PATH . "/views/termination_contract/termination_contract_staff.ftl";
        }
        try {
            if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
                $pathUrl = $this->getTerminationTplPath($filePath);
            } else {
                $pathUrl = $this->getTerminationTplPathFromCache($filePath);
            }
        } catch (\Throwable $e) {
            $this->logger->write_log('getTerminationTplPath  error' . $e->getMessage());
            return '';
        }
        $template_list  = (new SettingEnvServer())->listByCode([
            'default_template_header',
            'default_template_footer',
        ]);
        $template_list  = array_column($template_list, 'set_val', 'code');
        $headerTemplate = $template_list['default_template_header'] ?? '';
        $footerTemplate = $template_list['default_template_footer'] ?? '';

        $pdf_header_footer_setting = [
            'displayHeaderFooter' => true,
            'headerTemplate'      => $headerTemplate,
            'footerTemplate'      => $footerTemplate,
        ];
        $params = [
            'generate_date'      => date('Y-m-d'),
            'signing_date'       => $staffInfo['signing_date'] ?? '',
            'staff_name'         => $staffInfo['name'],
            'staff_id'           => $staffInfo['staff_info_id'],
            'termination_reason' => $staffInfo['termination_reason'] ?? '',
            'leave_date'         => $staffInfo['leave_date'] ?? '',
            'last_work_date'     => $staffInfo['last_work_date'] ?? '',
            'termination_reason_provision' => $staffInfo['termination_reason_provision'] ?? '',
        ];
        $pdf_file_data             = (new formPdfServer())->generatePdf($pathUrl, $params, null, '',
            $pdf_header_footer_setting);
        if (empty($pdf_file_data['object_url'])) {
            $this->logger->write_log(['makeTerminationFileUrl  error', $pathUrl, $params]);
            return '';
        }
        return $pdf_file_data['object_url'];
    }

    /**
     * @param $path
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    protected function getTerminationTplPath($path)
    {
        $upload_result = OssHelper::uploadFile($path, true);
        return $upload_result['object_url'];
    }

}
