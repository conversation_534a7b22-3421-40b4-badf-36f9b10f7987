<?php

namespace FlashExpress\bi\App\Modules\My\Server;


use FlashExpress\bi\App\Enums\PdfEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Modules\My\library\Enums\CommonEnums;
use FlashExpress\bi\App\Server\SettingEnvServer;

class PdfHelperServer extends BaseServer
{
    /**
     * 根据公司 ID 获取相应公司的页眉页脚
     * @param $paramsIn
     * @param int $response_type
     * @return array
     * @throws BusinessException
     */
    public function getHeaderAndFooter($paramsIn): array
    {
        $result = [
            'header_template'   => '',
            'footer_template'   => '',
            'company_full_name' => '',
        ];
        if (empty($paramsIn)) {
            return $result;
        }

        $fle_rpc = (new ApiClient('hcm_rpc', '', 'get_company_config_info', $this->lang));
        $fle_rpc->setParams($paramsIn);
        $res = $fle_rpc->execute();
        if (isset($res['error'])) {
            throw new BusinessException($res['error']);
        }
        $companyInfo = $res['result']['data'];

        return [
            'header_template'          => $companyInfo['company_logo_url_base64'] ?? '',
            'footer_template'          => $companyInfo['company_card_url_base64'] ?? '',
            'company_full_name'        => $companyInfo['company_name'] ?? '',
            'company_short_name'       => $companyInfo['company_short_name'] ?? '',
            'company_phone'            => $companyInfo['company_phone'] ?? '',
            'company_web_url'          => $companyInfo['company_web_url'] ?? '',
            'company_logo_url'         => $companyInfo['company_logo_url'] ?? '',
            'official_staff_name'      => $companyInfo['labor_name'] ?? '',
            'official_sign_url'        => $companyInfo['labor_sign_url'] ?? '',
            'official_staff_job_title' => $companyInfo['labor_job_title'] ?? '',
            'color_footer_right'       => CommonEnums::$company_color_right[$companyInfo['company_id']] ?? 'yellow',
            'color_footer_left'        => CommonEnums::$company_color_left[$companyInfo['company_id']] ?? 'black',
        ];
    }

    /**
     * 匹配图片
     * @param $date
     * @param $type
     * @return string
     */
    private function extractImage($date, $type)
    {
        if ($type == PdfEnums::RESPONSE_TYPE_IMAGE) {
            preg_match('/<img[^>]+src="([^"]+)"/', $date, $matches);
            return $matches[1];
        }
        return $date;
    }

    /**
     * 获取页眉页脚
     * @param $companyInfo
     * @return array
     */
    public function getHeaderFooter($companyInfo)
    {
        $header = '<div style="width: 100%; margin: 0 19mm; box-sizing: border-box"><table style="line-height: 6mm;width: 100%;font-weight: 700;font-size: 6mm;font-family: sans-serif;background-size: 38mm;"><tr><td style="width: 40mm; text-align: right"><img style="width: 40mm; height: auto; object-fit: cover" src="' . $companyInfo['header_template'] .'"/></td></tr></table></div>';

        $footer = '<div style="width: 297mm; transform: translateY(4mm); box-sizing: border-box">
  <div style="width: 85%; text-align: right">
    <img
      style="width: auto; height: 20mm; object-fit: cover"
      src="' . $companyInfo['footer_template'] .'"
      alt=""
    />
  </div>
  <div
    style="
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      margin-top: 3mm;
    "
  >
    <div style="height: 0; width: 60mm; border: 4mm solid ' . $companyInfo['color_footer_left'] .'"></div>
    <div
      style="
        width: 0px;
        height: 0px;
        border-color: ' . $companyInfo['color_footer_left'] . ' ' . $companyInfo['color_footer_right'] . ' ' . $companyInfo['color_footer_right'] . ' ' . $companyInfo['color_footer_left'] .';
        border-width: 10px 10px 20px 20px;
        border-style: solid;
      "
    ></div>
    <div style="height: 0; width: 90mm; border: 4mm solid ' . $companyInfo['color_footer_right'] .'"></div>
  </div>
</div>';

        return [$header, $footer];
    }
}