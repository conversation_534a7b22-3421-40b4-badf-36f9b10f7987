<?php

namespace FlashExpress\bi\App\Modules\My\Server\Message;

use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\Message\BaseMessageServer;

class ProbationMessage extends BaseMessageServer
{
    /**
     * @description 是否更改页脚
     * @return bool
     */
    protected function isChangeFootContent(): bool
    {
        //未读 && 没有未处理的转正评估
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD &&
            $this->getMessageRequestData()->getIsKnow() == MessageEnums::MESSAGE_HAS_NOT_CLICK_KNOWN;
    }

    /**
     * @description 更改页脚内容，可自定义逻辑
     * @return string
     */
    public function changeFootContent(): string
    {
        return $this->getTranslation()->_('hr_probation_reminder_button');
    }

    /**
     * @description 是否设置消息读取状态为已读
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        //未读 && 已经点击“我知道了”
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD &&
            $this->getMessageRequestData()->getIsKnow() == MessageEnums::MESSAGE_HAS_CLICK_KNOWN;
    }

    /**
     * @description 是否设置消息提交状态为必须提交
     * @return bool
     */
    protected function isSetFormStateToMustSubmit(): bool
    {
        //未读 && 没有点击“我知道了”
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD &&
            $this->getMessageRequestData()->getIsKnow() == MessageEnums::MESSAGE_HAS_NOT_CLICK_KNOWN;
    }

    /**
     * @description 生成url，用于嵌入iframe
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        return sprintf("%s/#/%s?msg_id=%s&read_state=%d&msg_category=%d", env('sign_url'), $this->getNormalMessageUrl(),
            $this->getMessageResponseData()->getMsgId(),
            $this->getMessageResponseData()->getReadState(),
            $this->getMessageResponseData()->getCategory()
        );
    }

}