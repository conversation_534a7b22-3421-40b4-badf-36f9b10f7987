<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Server\BackyardServer as GlobalBaseServer;


class BackyardServer extends GlobalBaseServer
{

    /**
     * // 过滤掉警告书消息（7）和Tp3消息 （45，48）
     * @param $staff_id
     * @return mixed
     */
    protected function getPunchOutMsg($staff_id)
    {
        $db  = $this->getDI()->get('db_coupon_r');
        $sql = "select mc.id, mc.category, mc.category_code, message_content_id, m.related_id, m.message
                    from message_courier mc 
                    inner join  message_content as m on mc.message_content_id = m.id
                    where mc.staff_info_id = :staff_info_id and mc.read_state = 0 and mc.is_del = 0 and mc.category NOT IN (7,45,48)
                    order by mc.created_at desc
                    ";


        $currentData = $db->query($sql, ['staff_info_id' => $staff_id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timezone))->getSupportInfoBySubStaff($staff_id);
        if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
            if($supportStaffInfo['staff_info_id'] != $staff_id) {
                $sql = "select mc.id, mc.category, mc.category_code, message_content_id, m.related_id, m.message
                    from message_courier mc 
                    inner join  message_content as m on mc.message_content_id = m.id
                    where mc.staff_info_id = :staff_info_id and mc.read_state = 0 and mc.is_del = 0 and mc.push_state = :push_state
                    order by mc.created_at desc";

                $masterData = $db->query($sql, ['staff_info_id' => $supportStaffInfo['staff_info_id'], 'push_state' => MessageCourierModel::PUSH_STATE_SUB_YES])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if(!empty($masterData)) {
                    $currentData = array_merge($currentData, $masterData);
                }
            }
        }

        return $currentData;
    }

    /**
     * 下班卡 验证消息
     * @param $param
     * @return array
     */
    public function punch_out_msg($param): array
    {
        return $this->get_new_msg($param);
    }
}