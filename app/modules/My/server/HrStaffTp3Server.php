<?php

namespace FlashExpress\bi\App\Modules\My\Server;
use Exception;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrStaffTp3Model;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class HrStaffTp3Server extends BaseServer
{
    public $timezone;
    public $lang;

    /**
     * C1最大值 职级对应薪资上限
     * @var int[]
     */
    public  static $C1MaxMap = [
        12 => 3000,
        13 => 4000,
        14 => 6000,
        15 => 8000,
        16 => 10000,
        17 => 15000,
        18 => 20000,
        19 => 25000,
    ];
    public static $versionYear = '2025';
    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang, $timezone);
        $this->timezone = $timezone;
        $this->lang     = $lang;
    }

    /**
     * 根据消息ID获取tp3信息
     * @param $msg_id
     * @return array
     */
    public function getTp3InfoByMsgId($msg_id){
        $tp3_info = HrStaffTp3Model::findFirst([
            'conditions' => ' msg_id = :msg_id:',
            'bind' => [
                'msg_id' => $msg_id,
            ]
        ]);
        if($tp3_info){
            $tp3_info =$tp3_info->toArray();
            $tp3_info['tp3_detail'] =  $tp3_info['tp3_detail'] ? json_decode($tp3_info['tp3_detail'],true) : null;

            return $tp3_info;
        }

        return [];

    }

    /**
     * 根据员工ID获取tp3信息
     * @param $msg_id
     * @return array
     */
    public function getTp3InfoByStaffId($staff_id){

        $tp3_info = HrStaffTp3Model::findFirst([
            'conditions' => ' staff_id = :staff_id:',
            'bind' => [
                'staff_id' => $staff_id,
            ]
        ]);

        if($tp3_info){
            $tp3_info =$tp3_info->toArray();
            $tp3_info['tp3_detail'] =  $tp3_info['tp3_detail'] ? json_decode($tp3_info['tp3_detail'],true) : null;
            return $tp3_info;
        }

        return [];

    }

    /**
     * 根据工号获取tp3详情
     * @param $staff_id
     * @return array
     * @throws \Exception
     */
    public function getConfigInfo($staff_id){

        //获取身份证（护照）号
        $hr_staff_info = HrStaffInfoModel::findFirst([
            'columns'=>'staff_info_id,name,identity,job_title_grade_v2,hire_date',
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => [
                'staff_info_id' => $staff_id,
            ]
        ]);
        if(empty($hr_staff_info)){
            throw new \Exception('员工信息获取失败');
        }
        $identity = $hr_staff_info->identity;
        $name = $hr_staff_info->name;
        //获取国籍
        $hr_staff_nationality = HrStaffItemsModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and item='NATIONALITY'",
            'bind' => ['staff_info_id' => $staff_id],
        ]);
        if(empty($hr_staff_nationality)){
            throw new \Exception('员工信息国籍获取失败');
        }
        $nationality = $hr_staff_nationality->value;
        //判断员工国籍是否是马来西亚
        if($nationality == 3){
            $b2_b3_required = ['B2_required'=>1,'B3_required'=>0];
        }else{
            $b2_b3_required = ['B2_required'=>0,'B3_required'=>1];
        }
        //获取当前日期
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $current_date = gmdate('Y-m-d', time() + $add_hour * 3600);
        $C1_max = isset(static::$C1MaxMap[$hr_staff_info->job_title_grade_v2])
            ? static::$C1MaxMap[$hr_staff_info->job_title_grade_v2] * date('m',
                strtotime($hr_staff_info->hire_date)) : null;
        return [
            'staff_id'       => $staff_id,
            'name'           => $name,
            'identity'       => $identity,
            'b2_b3_required' => $b2_b3_required,
            'current_date'   => $current_date,
            'C1_max'         => $C1_max,
        ];
    }

    /**
     * 提交AB模块信息
     * @param $params
     * @param $staff_id
     * @throws \Exception
     */
    public function createABInfo($params,$user_info){
        $tp3_detail = $params['tp3_detail'];

        $params['tp3_detail'] = json_encode($tp3_detail);
        //保存入库提交信息
        return $this->saveTp3($params,$user_info['id'],$user_info['name']);

    }

    /**
     * 提交C模块信息
     * @param $params
     * @param $user_info
     * @return bool
     * @throws BusinessException
     */
    public function createCInfo($params,$user_info){
        $tp3_detail = $params['tp3_detail'];
        $C1 = empty($tp3_detail['C']['C1']) ?  null : $tp3_detail['C']['C1'];
        $C3 = empty($tp3_detail['C']['C3']) ?  null : $tp3_detail['C']['C3'];
        $C5 = empty($tp3_detail['C']['C5']) ?  null : $tp3_detail['C']['C5'];
        if ($C1 && $C3 && bccomp($C3,$C1*0.25) == 1) {
            throw new BusinessException($this->getTranslation()->_('tp3_c3_error_tip'));
        }
        if ($C1 && $C5 && bccomp($C5,$C1*0.3) == 1) {
            throw new BusinessException($this->getTranslation()->_('tp3_c5_error_tip'));
        }
        $params['tp3_detail'] = json_encode($tp3_detail);
        return $this->saveTp3($params,$user_info['id'],$user_info['name']);

    }

    /**
     * 提交D模块信息`
     * @param $params
     * @param $user_info
     * @return bool
     */
    public function createDInfo($params,$user_info){
        $tp3_detail = $params['tp3_detail'];

        $params['tp3_detail'] = json_encode($tp3_detail);
        return $this->saveTp3($params,$user_info['id'],$user_info['name']);

    }
    /**
     * 提交E模块信息
     * @param $params
     * @param $staff_id
     * @throws \Exception
     */
    public function createEInfo($params,$user_info){
        $tp3_detail = $params['tp3_detail'];

        $params['tp3_detail'] = json_encode($tp3_detail);

        return $this->saveTp3($params,$user_info['id'],$user_info['name']);

    }

    /**
     * 判断当前员工的tp3状态是否可提交或判断是否有待提交tp3信息
     * @param $staff_id //员工ID
     * @param null $tp3_info tp3信息
     * @return bool true: 有待提交数据可提交，false:已提交不可在提交
     */
    public function _validateStateIsCanSubmit($staff_id,&$tp3_info=null): bool
    {

        $tp3_info = $this->getTp3InfoByStaffId($staff_id);
        $check  = $this->checkStaffHireType($staff_id);
        //老员工没有tp3数据，tp3数据为空或者状态为已提交的不能再次提交数据
        if(empty($tp3_info) || $tp3_info['state'] == enums::$tp3_state['submitted'] || $check){
            return false;
        }
        return true;
    }

    /**
     * 检查员工类型
     * @param $staff_id
     * @return bool
     */
    public function checkStaffHireType($staff_id): bool
    {
        $first = HrStaffInfoModel::findFirst([
            'columns'    => 'hire_type',
            'conditions' => 'staff_info_id = :id:',
            'bind'       => ['id' => $staff_id],
        ]);
        return !empty($first) && in_array($first->hire_type,HrStaffInfoModel::$agentTypeTogether);
    }

    /**
     * 保存tp3信息
     * @param $params
     * @param $staff_id
     * @param null $staff_name
     * @return bool
     * @throws Exception
     */
    private function saveTp3($params,$staff_id,$staff_name=null){
        //更新数据

        $tp3_detail = trim($params['tp3_detail']);
        $update_data = [
            'tp3_detail'=>$tp3_detail,
        ];

        $update_data['tp3_version'] = (new SettingEnvServer())->getSetVal('current_tp3_version');;

        if ($params['step'] == 4) { //最后一步提交时将状态更改为已提交
            //获取当前日期
            $current_date               = date('Y-m-d');
            $update_data['state']       = 2;
            $update_data['submit_date'] = $current_date;

            $tp3_log_insert = [
                'staff_id'             => $staff_id,
                'action'               => 1,
                'tp3_detail'           => $tp3_detail,
                'submitter_staff_id'   => $staff_id,
                'submitter_staff_name' => $staff_name,
            ];
        }
        $db = $this->getDI()->get('db');
        //开启事物
        $db->begin();

        try {
            $db->updateAsDict('hr_staff_tp3', $update_data, "staff_id = " . $staff_id);
            if (isset($tp3_log_insert)) {
                //如果是最后一步提交则提交一条 提交log信息入库
                $db->insertAsDict('hr_staff_tp3_log', $tp3_log_insert);
                //消息置为已读
                $message       = MessageCourierModel::find([
                    "conditions" => "staff_info_id = :staff_id: and category in(45,48)",
                    "bind"       => [
                        'staff_id' => $staff_id,
                    ],
                    'columns'    => 'id',
                ])->toArray();
                $messageIdsArr = array_column($message, 'id');
                if ($messageIdsArr) {
                    $conditions = sprintf('id in (%s)', getIdsStr($messageIdsArr));
                     $this->getDI()->get('db_coupon')->updateAsDict('message_courier',
                        ['read_state' => 1],
                        ['conditions' => $conditions]);
                }
            }
            //提交事物
            $db->commit();
            return true;
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }

    /**
     * 获取版本当前及以前
     * @param $year
     * @return array
     */
    public function getVersionsLeYear($year): array
    {
        $versions = [];
        foreach (HrStaffTp3Model::VERSIONMap as $yearIndex => $version) {
            if ($yearIndex <= $year) {
                $versions = array_merge($versions,is_array($version) ? $version : [$version]);
            }
        }
        return $versions;
    }

}