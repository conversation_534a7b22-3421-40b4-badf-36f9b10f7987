<?php
/**
 * Author: Bruce
 * Date  : 2022-12-08 18:04
 * Description:
 */

namespace FlashExpress\bi\App\Modules\My\Server;


use FlashExpress\bi\App\Modules\My\library\Enums\CeoMailEnums;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\CeoMailServer as GlobalBaseServer;


class CeoMailServer extends GlobalBaseServer
{
    /**
     * 获取需要阅读承诺书的信息
     * @return array
     */
    public function getCommitmentCategoryInfo()
    {
        return CeoMailEnums::MUST_READ_INFORMATION_COMMITMENT_CATEGORY_IDS;
    }

    /**
     * 获取 关于薪酬分类
     * 查找当前登录人 所属公司对应的 薪酬分类
     * @param array $params
     * @return int
     */
    public function getCompensationCategory($params = [])
    {
        $companyId = (new StaffRepository())->getStaffDeptCompanyId($params['staff_id']);
        $category = CeoMailEnums::COMPENSATION_COMPANY_RELATE[$companyId] ?? CeoMailEnums::ABOUT_MALAYSIA_EXPRESS_CATEGORY_ID;
        return $category;
    }

    /**
     * 获取 network 提成
     * @return int
     */
    public function getInvoiceCategory()
    {
        return CeoMailEnums::ABOUT_NETWORK_COMMISSION_CATEGORY_ID;

    }

    /**
     * 获取 flash box 提交表单页地址。
     * @return mixed
     */
    public function getFlashBoxUrlIncrease()
    {
        $result['flash_box_url'] = '';

        $info = $this->getCategory(CeoMailEnums::ABOUT_SYS_OTHER_CATEGORY_ID);
        if(empty($info)) {
            return $result;
        }
        $name = urlencode($info['category_name_en']);
        $result['flash_box_url'] = env('h5_endpoint') . CeoMailEnums::FLASH_BOX_SUBMIT_PAGE . '?source=fbi&is_read_commitment=0&problem_category=' . CeoMailEnums::ABOUT_SYS_OTHER_CATEGORY_ID . '&seconProblemName=' . $name;
        return $result;
    }
}