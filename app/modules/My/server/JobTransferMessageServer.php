<?php

namespace FlashExpress\bi\App\Modules\My\Server;

/**
 * MY模块转岗消息通知服务
 * 继承全局JobTransferMessageServer实现多态
 * Author: AI Assistant
 * Date: 2024
 * Description: 实现MY模块特定的转岗消息发送逻辑
 */

use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Server\JobTransferMessageServer as BaseJobTransferMessageServer;

class JobTransferMessageServer extends BaseJobTransferMessageServer
{
    private static $instance = null;

    protected function __construct($lang = 'zh-CN', $timezone = '+08:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * @return JobTransferMessageServer
     */
    public static function getInstance(): BaseJobTransferMessageServer
    {
        if (!isset(self::$instance) || self::$instance instanceof self) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * MY模块特定的发送转岗成功消息给上级管理者
     * 重写父类方法实现多态
     * 内容：您好，您的下属{工号}{转岗员工姓名}已成功从{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}，生效日期为{转岗生效日期}
     * 
     * @param int $staff_info_id 接收消息的员工ID（上级管理者）
     * @param array $data 转岗相关数据
     * @param string $lang 语言设置
     * @return void
     */
    protected function sendSucMsgToAgentTransfer($staff_info_id, $data, $lang)
    {
        // 检查位置是否变更（比较current_store_name与after_store_name）
        $locationChanged = ($data['current_store_name'] ?? '') !== ($data['after_store_name'] ?? '');

        // 检查职位/车类型是否变更
        $positionChanged = ($data['current_position_name'] ?? '') !== ($data['after_position_name'] ?? '');
        $carTypeChanged = ($data['current_car_type'] ?? 0) !== ($data['after_car_type'] ?? 0);
        $positionOrCarTypeChanged = $positionChanged || $carTypeChanged;

        if ($locationChanged && $positionOrCarTypeChanged) {
            $contentKey = 'job_transfer.suc_msg_to_agent_transfer_dynamic_all';
        } else if ($locationChanged) {
            $contentKey = 'job_transfer.suc_msg_to_agent_transfer_dynamic_first';
        } else if ($positionOrCarTypeChanged) {
            $contentKey = 'job_transfer.suc_msg_to_agent_transfer_dynamic_second';
        } else {
            return;
        }
        $server = new JobTransferConfirmServer($this->lang, $this->timeZone);

        //查询职位名称 ids
        $sysObj       = new SysListRepository();
        $positionIds  = [$data['current_position_id'], $data['after_position_id']];
        $positionData = $sysObj->getPositionList(['ids' => $positionIds]);
        $positionData = array_column($positionData, 'name', 'id');

        $sendParams = [
            'staff_info_id' => $staff_info_id,
            'title_key'     => 'job_transfer.transfer_agent_success',
            'content_key'   => $contentKey,
            'language'      => $lang,
            'data'          => [
                'after_date'              => $data['actual_after_date'],
                'current_store_name'      => $data['current_store_name'],
                'current_position_name'   => $server->formatJobTitle($data['current_position_id'], $positionData),
                'after_store_name'        => $data['after_store_name'],
                'after_position_name'     => $server->formatJobTitle($data['after_position_id'], $positionData),
            ],
        ];
        $this->pushAndSendMessage($sendParams);
    }

    protected function sendFailMsgToAgentTransfer($staff_info_id, $data, $lang, $fail_reason)
    {
        // 检查位置是否变更（比较current_store_name与after_store_name）
        $locationChanged = ($data['current_store_name'] ?? '') !== ($data['after_store_name'] ?? '');

        // 检查职位/车类型是否变更
        $positionChanged = ($data['current_position_name'] ?? '') !== ($data['after_position_name'] ?? '');
        $carTypeChanged = ($data['current_car_type'] ?? 0) !== ($data['after_car_type'] ?? 0);
        $positionOrCarTypeChanged = $positionChanged || $carTypeChanged;

        if ($locationChanged && $positionOrCarTypeChanged) {
            $contentKey = 'job_transfer.fail_msg_to_agent_transfer_dynamic_all';
        } else if ($locationChanged) {
            $contentKey = 'job_transfer.fail_msg_to_agent_transfer_dynamic_first';
        } else if ($positionOrCarTypeChanged) {
            $contentKey = 'job_transfer.fail_msg_to_agent_transfer_dynamic_second';
        } else {
            return;
        }
        $server = new JobTransferConfirmServer($this->lang, $this->timeZone);
        //查询职位名称 ids
        $sysObj       = new SysListRepository();
        $positionIds  = [$data['current_position_id'], $data['after_position_id']];
        $positionData = $sysObj->getPositionList(['ids' => $positionIds]);
        $positionData = array_column($positionData, 'name', 'id');

        $sendParams = [
            'staff_info_id' => $staff_info_id,
            'title_key'     => 'job_transfer.transfer_agent_failure',
            'content_key'   => $contentKey,
            'language'      => $lang,
            'data'          => [
                'after_date'              => $data['after_date'],
                'current_store_name'      => $data['current_store_name'],
                'current_position_name'   => $server->formatJobTitle($data['current_position_id'], $positionData),
                'after_store_name'        => $data['after_store_name'],
                'after_position_name'     => $server->formatJobTitle($data['after_position_id'], $positionData),
            ],
        ];
        $this->pushAndSendMessage($sendParams);
    }
}