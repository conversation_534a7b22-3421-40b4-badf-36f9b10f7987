<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Server\PublicHolidayServer as BasePublicHolidayServer;


class PublicHolidayServer extends BasePublicHolidayServer
{
    /**
     * 获取默认的公共假期
     * @param $params
     * @return mixed
     */
    public function getHolidays($params)
    {
        $condition = 'type in ({type:array})';
        $bind      = [
            'type' => [
                ThailandHolidayModel::TYPE_DEFAULT,
                ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_6,
            ],
        ];
        if (isset($params['start_date'])) {
            $condition    .= ' and day >= :date:';
            $bind['date'] = $params['start_date'];
        }
        return ThailandHolidayModel::find([
            'columns'    => 'day,province_code',
            "conditions" => $condition,
            "bind"       => $bind,
            "order"      => "day asc"
        ])->toArray();
    }

}
