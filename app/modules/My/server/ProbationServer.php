<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrProbationResignModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\MessagePdfModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\formPdfServer;
use FlashExpress\bi\App\Server\ProbationServer as BaseProbationServer;
use Exception;

use FlashExpress\bi\App\Server\MessageServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\PersoninfoServer;
use FlashExpress\bi\App\Server\ToolServer;


class ProbationServer extends BaseProbationServer
{
	/**
	 * 根据新的职级来获得模板
	 * @param $grade
	 * @return int
	 */
	public function getTplIdByJobTitleGradeV2($grade)
	{
		$grade = intval($grade);
		//小于等于17都是模板1
		if ($grade <= 16) {
			return 4;
		}
		//17及以上，都是3
		if ($grade >= 17) {
			return 3;
		}
		//其他情况2
		return 2;
	}



	/**
	 * 根据入职时间获得员工
     * @param string $hire_date_begin
     * @param string $hire_date_end
     * @param int $is_delay
     * @param string $greater_job_title_grade_v2
     * @param string $less_job_title_grade_v2
     * @param false $date_logic
     * @return array
	 */
	public function getStaffs(
        $hire_date_begin = '',
        $hire_date_end = '',
        $is_delay = 0,
        $greater_job_title_grade_v2 = '',
        $less_job_title_grade_v2 = '',
        $date_logic = false,
        $probation_channel_type = 1
    ): array {
		//编制的正式员工在职的
		$sql = "
            select
                hsi.staff_info_id,
                hsi.job_title_level,
                hsi.job_title_grade_v2,
                hsi.hire_date,
                hp.cur_level,
                hp.status,
                hp.id as probation_id,
                hp.second_audit_status,
                hp.first_audit_status,
                item.`value` as manager_id
            from
                hr_staff_info as hsi
            left join
                hr_staff_items as item on item.staff_info_id = hsi.staff_info_id and item.item='MANGER'
            left join
                hr_probation as hp on hp.staff_info_id = hsi.staff_info_id
            where
                hsi.state!=2 and hsi.formal=1 and hsi.hire_type = 1 and hsi.is_sub_staff = 0 and (hp.is_system=0 or hp.is_system is NULL)
        ";

        if ($probation_channel_type == HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE) {
            $hire_date_end = date("Y-m-d", strtotime("-6 month", strtotime($hire_date_begin)));
            $sql .= 'and hsi.hire_date <= :hire_date_begin and hsi.hire_date>=:hire_date_end and hp.probation_channel_type = '.HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE;
        } else {
            // 之前就逻辑不动
            $sql .= "and hsi.hire_date>= :hire_date_begin and hsi.hire_date< :hire_date_end and hp.probation_channel_type != " . HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE;
        }

		$where = ["hire_date_begin" => $hire_date_begin,"hire_date_end"=>$hire_date_end];
		if(empty($is_delay)){
			$sql.=" and (hp.is_delay=0 or hp.is_delay is NULL)";
		}else{
			$sql.=" and hp.is_delay=1";
		}
		//增加条件
		if(!empty($greater_job_title_grade_v2)){
			$sql.=" and hsi.job_title_grade_v2 > :where_job_title_grade_v2 ";
			$where['where_job_title_grade_v2']  = (int)$greater_job_title_grade_v2;
		}

		//增加条件
		if(!empty($less_job_title_grade_v2)){
			$sql.=" and ifnull(hsi.job_title_grade_v2,0) <= :where_less_job_title_grade_v2";
			$where['where_less_job_title_grade_v2']  = (int)$less_job_title_grade_v2;
		}

		return $this->getDI()->get("db_rby")->query(
			$sql,
			$where
		)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
	}
    //by 试用期，评审

    /**
     * @description:提交转正评估评审
     *
     * @param string $id
     * @param string $audit_id 用户 id
     * @param array $params
     * @param int $deadline_at_one 第一阶段上上级过期时间
     * @param int $deadline_at_tow 第二阶段上上级过期时间
     * @param int $is_fail_msg 未通过是否发送消息给被评估员工上级，以及被评估员工所属HRBP 目前只有 Id 发送  2 是发送
     *
     * @return     :
     * @throws ValidationException
     * <AUTHOR> L.J
     * @time       : 2021/9/9 14:41
     */
    public function probation_audit( $id = '', $audit_id = '', $params = [], $deadline_at_one = 3, $deadline_at_tow = 0, $is_fail_msg = 1 ) {

        if (empty($id) || empty($audit_id)) {
            throw new \Exception($this->getTranslation()->_('miss_args'));
        }
        $lang = $this->getTranslation();
        //没有分数不行!!
        if(empty($params['score'])){
            throw new ValidationException('score '. $lang->_('miss_args'));
        }
        $sql = '
            select
                hpa.*,
                hpt.item_ids,
                hpt.score_rule,
                hp.is_system,
                hp.formal_at,
                hp.status as hp_status,
                hp.is_active as hp_is_active
            from
                hr_probation_audit as hpa
                left join hr_probation_tpl as hpt on hpa.tpl_id = hpt.id
                left join hr_probation as hp on hp.id=hpa.probation_id
            where
                hpa.id=:id and hpa.audit_id=:audit_id
        ';

        $arr = $this->getDI()->get('db_rby')->fetchOne($sql, \PDO::FETCH_ASSOC, ['id' => $id, 'audit_id' => $audit_id]);
        if (empty($arr)) {
            throw new ValidationException('not found');
        }

        if (!empty($arr['hp_status']) && $arr['hp_status'] == HrProbationModel::STATUS_FORMAL) {
            throw new ValidationException($lang->_('probation_status_err_1'));
        }

        if ($arr['audit_status'] != 1) {
            throw new ValidationException('has already audit');
        }


        if ($arr['is_system'] == 1) {
            throw new ValidationException('system auto');
        }

        $manager_id = 0;

        if ($arr['audit_level'] == 1) {
            $manager_id = $this->getManagerId($audit_id);
        }

        $score       = $params['score'];
        $remark      = isset($params['remark']) ? $params['remark'] : '';
        $pic         = isset($params['pic']) ? $params['pic'] : '';
        $job_content = $params['job_content'] ?? ''; //工作内容

        $item = []; //hr_probation表
        $data = []; //hr_probation_audit表

        //如果info与库里不同,证明已经修改
        foreach ($score['list'] as $k => $v) {
            foreach ($v['list'] as $kk => $vv) {
                if ($lang->_($vv['info_key']) != $vv['info']) {
                    $score['list'][$k]['list'][$kk]['is_update'] = 1;
                }
            }
        }

        //自己根据规则算一遍
        $score = $this->getScoreFromTpl($score, $arr['cur_level']);
        //没有分数不行!!
        if(empty($score)){
            throw new \Exception('score 没有获取到!!!');
        }
        $data['score']        = json_encode($score, JSON_UNESCAPED_UNICODE);//score存json
        $data['audit_status'] = 2;
        $data['remark']       = $remark;
        $data['pic']          = $pic;


        //$score_num = $this->getScore($score['score'], 1);
        $item['updated_at']  = gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600);
        $data['updated_at']  = $item['updated_at'];
        $data['job_content'] = $job_content;
        $data['show_time']   = date("Y-m-d H:i:s");

        $isSendNotice = false;

        //第一次
        if ($arr['cur_level'] == 1) {
            $score_num           = $this->getScore($score['score'], 1);
            $item['first_score'] = $score_num;
            $item['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_RUN;
            if ($arr['audit_level'] == 2) {
                $item['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_DONE;
                $item['first_status']       = $score_num >= 6000 ? HrProbationModel::FIRST_STATUS_PASS : HrProbationModel::FIRST_STATUS_NOT_PASS;
            }
        } //第二次
        else {
            $score_num            = $this->getScore($score['second_score'], 1);
            $item['second_score'] = $score_num;

            //第二阶段上上级评过+第二阶段上级改成上级和上上级都更改主表
            if ($score_num >= 6000) {
                $item['status'] = self::STATUS_PASS;    //已通过
                //如果修改时间大于转正时间  进行修改转正
                if ($item['updated_at'] > $arr['formal_at'] ) {
                    $item['status']          = self::STATUS_FORMAL;
                    $item['formal_staff_id'] = $audit_id;
                    $item['formal_at']       = gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600);
                }
            } else {
                $item['status'] = self::STATUS_NOT_PASS;    //未通过

                //上级不存在直接进行待离职处理
                if (empty($manager_id)) {
                    $isSendNotice   = true;
                }
            }


            $item['remark'] = $data['remark'];

            //把通过不通过的状态传过去
            $data['status'] = $item['status'];

            //不发了 赋值终态
            if (!($item['status']!=self::STATUS_FORMAL && $arr['audit_level'] == 1 && !empty($manager_id))) {
                $item['second_audit_status'] = HrProbationModel::SECOND_AUDIT_STATUS_DONE;
                 if (in_array($item['status'],[self::STATUS_PASS,self::STATUS_FORMAL])) {
                    $item['second_status'] = HrProbationModel::SECOND_STATUS_PASS;
                }
            }
        }

        $db = $this->getDI()->get('db');


        try {
            $db->begin();

            $db->updateAsDict('hr_probation_audit', $data, ['conditions' => 'id=' . intval($id)]);
            $db->updateAsDict("hr_probation", $item, ['conditions' => 'id=' . intval($arr['probation_id'])]);

            //需要判断是否需要评审
            if ($item['status']!=self::STATUS_FORMAL && $arr['audit_level'] == 1 && !empty($manager_id)) {

                //下一级评审，内容大部分相同
                $tmp                  = [];
                $tmp['probation_id']  = $arr['probation_id'];
                $tmp['staff_info_id'] = $arr['staff_info_id'];
                $tmp['tpl_id']        = $arr['tpl_id'];
                $tmp['audit_id']      = $manager_id;
                $tmp['audit_level']   = 2;
                $tmp['cur_level']     = $arr['cur_level'];
                $tmp['audit_status']  = 1;
                $tmp['status']        = 0;
                $tmp['score']         = $data['score'];
                $tmp['created_at']    = $item['updated_at'];
                $tmp['updated_at']    = $item['updated_at'];
                $tmp['remark']        = $remark;                     //同步上级评审意见
                $tmp['pic']           = $pic;                        //同步上级图片
                $tmp['job_content']   = $job_content;                //工作内容
                $tmp['show_time']     = date("Y-m-d H:i:s");

                $day                  = $deadline_at_one;            //43天00:00,45天24点

                if ($arr['cur_level'] == 2) {
                    $day = $deadline_at_tow; //85天24:00,85天24:00不变，
                }
//                //激活过
//                if ($arr['hp_is_active'] == 1) {
//                    $day = 0;  //截止日期不递增
//                }
                if ($arr['hp_is_active'] == 1){
                    $now_date = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
                    $tmp['deadline_at']        = $this->getDateByDays($now_date, 3, 1);
                }else{
                    $tmp['deadline_at'] = $this->getDateByDays($arr['deadline_at'], $day, 1);
                }
                $db->insertAsDict('hr_probation_audit', $tmp);
            }

            //插入第二次审核离职通知
            if ($isSendNotice) {
                $resignInfo = HrProbationResignModel::findFirst([
                    'conditions' => "
                        staff_info_id = :staff_info_id: 
                        AND probation_id=:probation_id:
                        AND probation_audit_id=:probation_audit_id: 
                        AND is_deleted = :is_deleted:
                    ",
                    'bind'       => [
                        'staff_info_id'      => $arr['staff_info_id'],
                        'probation_id'       => $arr['probation_id'],
                        'probation_audit_id' => $id,
                        'is_deleted'         => CommonEnums::IS_DELETED_NO,
                    ],
                    'columns'    => 'staff_info_id',
                ]);

                if (!$resignInfo) {
                    $hrProbationResignModel                     = new HrProbationResignModel();
                    $hrProbationResignModel->send_date          = date('Y-m-d');
                    $hrProbationResignModel->staff_info_id      = $arr['staff_info_id'];
                    $hrProbationResignModel->probation_id       = $arr['probation_id'];
                    $hrProbationResignModel->probation_audit_id = $id;

                    if (!$hrProbationResignModel->save()) {
                        throw new Exception($this->getTranslation()->_('no_server'));
                    }
                }
            }
            //二阶段 并且是最后审批 并且是评估通过 给员工发消息pdf
            if ($arr['cur_level'] == self::CUR_LEVEL_SECOND && $arr['audit_level'] == self::AUDIT_LEVEL_2 && empty($manager_id) && $score_num >= 6000) {
                $this->msgPdfData($arr['staff_info_id']);
            }

            //如果是上上级评估需要发送消息
            if ($arr['audit_level'] == 2) {
                //插入消息表
                $msg_data = ['probation_audit_id' => $id,
                             'created_at'         => gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600),
                             'updated_at'         => gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600),
                ];
                $res      = $this->getDI()->get('db')->insertAsDict('hr_probation_message', $msg_data);
                if (!$res) {
                    throw new \Exception($this->getTranslation()->_('no_server'));
                }
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log('ProbationServer_audit---' . $e->getMessage() . '-----' . $e->getLine());
            return $this->checkReturn(['msg' => $e->getMessage()]);
        }
        //给上级发送push
        if (!empty($tmp)) {
            $this->push_notice_higher($tmp['audit_id'], $tmp['staff_info_id']);
        }

        //上上级不通过，给上级员工发送消息
        if ($arr['cur_level'] == self::CUR_LEVEL_SECOND && $arr['audit_level'] == 2 && $item['status'] == self::STATUS_NOT_PASS) {
            $this->sendMsgToManagerByStaffId($arr['staff_info_id'], $this->getManagerId($arr['staff_info_id']));
        }

        //记录转正日志
        if (isset($item['status']) && $item['status'] == self::STATUS_FORMAL) {
            $this->putFormalLog($audit_id, $arr['staff_info_id'], $arr['hp_status'], self::STATUS_FORMAL);
        }

        return $this->checkReturn(['data' => true]);


    }

    /**
     * 二阶段最后评估人 通过后 消息pdf 内的 变量
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function msgPdfData($staffId)
    {
        $staffInfo = (new StaffRepository($this->lang))->getStaffPosition($staffId);
        //离职通知期
        $noticeArr  = (new PersoninfoServer('en', $this->timeZone))->resignationNotice($staffInfo, $staffId, true);
        $noticeDate = $noticeArr['resignation'] ?? '';
        //调hcm  根据工号 获取配置的 公司logo 法人
        $queryParams = [
            'staff_info_id' => $staffId,
        ];
        //公司配置log 等
        $pdfHelperServer = new PdfHelperServer();
        $rpcSetting = $pdfHelperServer->getHeaderAndFooter($queryParams);
        [$header, $footer] = $pdfHelperServer->getHeaderFooter($rpcSetting);
        //其他变量参数pdf
        $current_month                       = intval(date('m', time()));
        $formal_month = intval(date('m', strtotime($staffInfo['formal_at'])));
        $pdfData['notice_date']              = $noticeDate;
        $pdfData['today']                    = date('d').' '.StaffRepository::$month_en[$current_month].' '.date('Y');
        $pdfData['i_date']                   = date('d/m/Y');
        $pdfData['name']                     = $staffInfo['name'];
        $pdfData['staff_info_id']            = $staffInfo['staff_info_id'];
        $pdfData['job_name']                 = $staffInfo['job_name'];
        $pdfData['depart_name']              = $staffInfo['depart_name'] ?? '';
        $pdfData['formal_at'] = empty($staffInfo['formal_at']) ? '--' : date('d', strtotime($staffInfo['formal_at']))
            . ' ' . StaffRepository::$month_en[$formal_month] . ' ' . date('Y', strtotime($staffInfo['formal_at']));
        $pdfData['job_title_grade_v2']       = empty($staffInfo['job_title_grade_v2']) ? 'F0' : 'F'.$staffInfo['job_title_grade_v2'];
        $pdfData['identity']                 = $staffInfo['identity'];
        $pdfData['company_full_name']        = $rpcSetting['company_full_name'];
        $pdfData['official_sign_url']        = $rpcSetting['official_sign_url'];
        $pdfData['official_staff_name']      = $rpcSetting['official_staff_name'];
        $pdfData['official_staff_job_title'] = $rpcSetting['official_staff_job_title'];

        //生成 pdf
        $file_path = APP_PATH . "/views/probation/probation_pdf.ftl";
        $template = (new ToolServer($this->lang, $this->timeZone))->getPdfTemp($file_path);
        if(empty($template)){
            $this->logger->write_log("{$staffId} 转正评估pdf 没有模板");
            return true;
        }

        $setting = [
            'displayHeaderFooter' => true,
            'headerTemplate'      => $header,
            'footerTemplate'      => $footer,
        ];
        $img = [['name'=>'official_sign_url','url'=> $rpcSetting['official_sign_url']]];
        $res = (new formPdfServer())->generatePdf($template, $pdfData, $img, 'EmployeeConfirmationLetter', $setting);

        if (empty($res['object_url'])) {
            //记录日志 重新发送
            $this->logger->write_log("{$staffId} 转正评估pdf 发送失败 @王冬冰");
            return true;
        }
        $fileUrl = $res['object_url'];

        //发消息
        $title              = 'Employee Confirmation Letter';
        $content            = "pdf_url={$res['object_url']}";
        $extend['category'] = MessageEnums::MESSAGE_CATEGORY_PROBATION_SEND_PDF;
        $extend['lang']     = 'en';
        $msgServer          = new MessageServer($this->lang, $this->timeZone);
        $msgId = $msgServer->sendMessage($staffId, $title, $content, $extend);
        
        $pdfData['header_footer_setting'] = $setting;
        $pdfData['sign_img_data'] = $img;
        $pdfData['template_url'] = $template;
        $messagePdf = MessagePdfModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and module_category = :module_category:",
            'bind' => ['staff_info_id' => $staffInfo['staff_info_id'], 'module_category' => MessagePdfModel::MODULE_CATEGORY_CONFIRMATION_LETTER],
        ]);
        if (empty($messagePdf)) {
            $messagePdf                        = new MessagePdfModel();
        }
        $messagePdf->staff_info_id = $staffInfo['staff_info_id'];
        $messagePdf->module_category = MessagePdfModel::MODULE_CATEGORY_CONFIRMATION_LETTER;
        $messagePdf->pdf_url = $fileUrl;
        $messagePdf->sign_url = ''; 
        $messagePdf->state = 0; 
        $messagePdf->business_id = $staffInfo['probation_id'] ?? 0;
        $messagePdf->msg_id = $msgId;
        $messagePdf->form_data_json = json_encode($pdfData, JSON_UNESCAPED_UNICODE);
        $messagePdf->save();
        
        return $fileUrl;
    }

    /**
     * 删除历史发送的消息数据
     * @param $probation_data
     * @param $staff_info
     * @param $status
     * @return void
     */
    public function hcm_edit_score_restart_signature($probation_data, $staff_info, $status, $probation_audit_id, $db)
    {
        // 产品要求 离职 和 待离职 都不执行
        if ($staff_info['state'] == HrStaffInfoModel::STATE_2 || ($staff_info['state'] == HrStaffInfoModel::STATE_1 && $staff_info['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE)) {
            return;
        }
        // 删除历史发送的消息数据
        $this->deleteMessageData($probation_data);
        $resignInfo = HrProbationResignModel::findFirst([
            'conditions' => "probation_id = :probation_id: AND is_deleted = :is_deleted:",
            'bind'       => [
                'probation_id' => $probation_data['id'],
                'is_deleted'   => CommonEnums::IS_DELETED_NO,
            ],
        ]);
        if ($resignInfo) {
            $resignInfo->is_deleted = CommonEnums::IS_DELETED_YES;
            $resignInfo->save();
            if ($resignInfo->msg_id) {
                $message_courier = MessageCourierModel::findFirst([
                    'conditions' => "id = :id:",
                    'bind'       => ['id' => $resignInfo->msg_id],
                ]);
                if ($message_courier) {
                    $message_courier->is_del = MessageCourierModel::IS_DELETED_YES;
                    $message_courier->save();
                }
            }
        }
        $message_pdf = MessagePdfModel::find([
            'conditions' => 'staff_info_id = :staff_info_id: and module_category = :module_category:',
            'bind' => [
                'staff_info_id' => $probation_data['staff_info_id'],
                'module_category' => MessagePdfModel::MODULE_CATEGORY_CONFIRMATION_LETTER,
            ],
        ])->toArray();
        if ($message_pdf){
            $sql = " delete from message_pdf where staff_info_id = ".$probation_data['staff_info_id']." and module_category = ".MessagePdfModel::MODULE_CATEGORY_CONFIRMATION_LETTER;
            $db->execute($sql);
        }
        $message_courier = MessageCourierModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and category = :category: and is_del = 0",
            'bind'       => ['staff_info_id' => $probation_data['staff_info_id'],'category' => MessageEnums::MESSAGE_CATEGORY_PROBATION_SEND_PDF],
        ]);
        if ($message_courier) {
            $message_courier->is_del = MessageCourierModel::IS_DELETED_YES;
            $message_courier->save();
        }
        
        if ($status == HrProbationModel::STATUS_NOT_PASS) {
            $hrProbationResignModel                     = new HrProbationResignModel();
            $hrProbationResignModel->send_date          = date('Y-m-d');
            $hrProbationResignModel->staff_info_id      = $probation_data['staff_info_id'];
            $hrProbationResignModel->probation_id       = $probation_data['id'];
            $hrProbationResignModel->probation_audit_id = $probation_audit_id;
            $hrProbationResignModel->save();
        } else {
            $this->msgPdfData($probation_data['staff_info_id']);
        }
        // 重新触发签字
        if ($probation_data['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE) {
            // 发送阶段完成发送签字消息 被评估人和被评估人的上级
            $rpc_params = [
                'staff_info_id'       => $probation_data['staff_info_id'],
                'customize_cur_level' => 2,
                'lang'                => $this->lang,
            ];
            $this->logger->write_log('send_probation_stage_done_message data  ' . json_encode($rpc_params), "info");
            $this->redisLib->lpush(RedisEnums::SEND_PROBATION_STAGE_DONE_MESSAGE, json_encode($rpc_params));
        }
    }

    /**
     * @param $probation_audit
     * @param $second_audit_status
     * @param $status
     * @return true
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function probationSecondNotice($probation_audit,$second_audit_status,$status): bool
    {
        if ($second_audit_status == HrProbationModel::SECOND_AUDIT_STATUS_DONE && $status == self::STATUS_NOT_PASS) {
            $resignInfo = HrProbationResignModel::findFirst([
                'conditions' => "
                        staff_info_id = :staff_info_id: 
                        AND probation_id=:probation_id:
                        AND probation_audit_id=:probation_audit_id: 
                        AND is_deleted = :is_deleted:
                    ",
                'bind'       => [
                    'staff_info_id'      => $probation_audit['staff_info_id'],
                    'probation_id'       => $probation_audit['probation_id'],
                    'probation_audit_id' => $probation_audit['id'],
                    'is_deleted'         => CommonEnums::IS_DELETED_NO,
                ],
                'columns'    => 'staff_info_id',
            ]);

            if (!$resignInfo) {
                $hrProbationResignModel                     = new HrProbationResignModel();
                $hrProbationResignModel->send_date          = date('Y-m-d');
                $hrProbationResignModel->staff_info_id      = $probation_audit['staff_info_id'];
                $hrProbationResignModel->probation_id       = $probation_audit['probation_id'];
                $hrProbationResignModel->probation_audit_id = $probation_audit['id'];
                if (!$hrProbationResignModel->save()) {
                    throw new \Exception($this->getTranslation()->_('no_server'));
                }
            }
        }

        //二阶段 并且是最后审批 并且是评估通过 给员工发消息pdf
        if ($second_audit_status == HrProbationModel::SECOND_AUDIT_STATUS_DONE && in_array($status,[self::STATUS_PASS,self::STATUS_FORMAL])) {
            $this->msgPdfData($probation_audit['staff_info_id']);
        }
        return true;
    }


}
