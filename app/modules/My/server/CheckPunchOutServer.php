<?php


namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Enums\ClockEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AttendanceWhiteListModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\CheckPunchOutServer as BaseCheckPunchOutServer;
use FlashExpress\bi\App\Server\KpiServer;
use FlashExpress\bi\App\Server\LeaveServer;
use FlashExpress\bi\App\Server\MaterialInventoryCheckServer;
use FlashExpress\bi\App\Server\ProbationTargetServer;
use FlashExpress\bi\App\Server\PunchInServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\WhiteListServer;


class CheckPunchOutServer extends BaseCheckPunchOutServer
{

    /**
     * 早退提示方法
     * @throws BusinessException|ValidationException
     */
    public function check_early_off($staff_info,
        $attendance_date,
        $shift_index,
        $is_confirm_early_off
    ): bool {
        if (!in_array($staff_info['formal'], [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN])) {
            return true;
        }
        $staff_id  = $staff_info['staff_id'];
        $job_title = $staff_info['job_title'];
        if ($is_confirm_early_off == 1) {
            return true;
        }


        $attendanceServer = (new AttendanceServer($this->lang, $this->timezone));
        $freeShiftConfig  = $attendanceServer->initFreeShiftConfig();

        $hrStaffInfo = (new StaffServer())->getStaffById($staff_id,'formal');
        //每日的班次信息
        if(empty($hrStaffInfo) ||  $hrStaffInfo['formal'] == HrStaffInfoModel::FORMAL_0){
            $shiftInfo = (new AttendanceServer($this->lang, $this->timezone))->getOldStaffShift($staff_id, $attendance_date, $attendance_date);
        }else{
            $shiftInfo = (new AttendanceCalendarV2Server($this->lang, $this->timezone))->getDailyShiftInfo($staff_id, $attendance_date, $attendance_date);
        }
        if(empty($shiftInfo[$attendance_date])){
            return true;
        }
        $shiftInfo = $shiftInfo[$attendance_date];

        $shift_type = $shiftInfo['shift_type'];

        // 打卡信息
        $attendanceRe = new AttendanceRepository($this->lang, $this->timezone);
        $cardInfo     = $attendanceRe->getAttendanceData($staff_id, [$attendance_date]);
        $tripCardInfo = $attendanceRe->getTripAttendanceData($staff_id, [$attendance_date]);
        $shift_type_key = 0;
        if ($shift_type == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
            $shift_type_key = $shift_index / 2;
        }
        $attendance_shift_key = $attendance_date.'_'.$shift_type_key;

        $attendanceInfo = $cardInfo[$attendance_shift_key]??($tripCardInfo[$attendance_shift_key]??[]);


        if (empty($attendanceInfo) || empty($attendanceInfo['started_at'])) {
            return true;
        }


        $leaveInfo           = (new LeaveServer($this->lang, $this->timezone))->getStaffLeaveInfoFromCache($staff_id,
            $attendance_date);


        $now           = date('Y-m-d H:i:s');
        $isLive        = false;
        if (in_array($job_title, $freeShiftConfig['free_shift_position'])) {
            $isLive    = true;
            $hour      = show_time_zone($attendanceInfo['started_at'], 'H');
            $dayHour   = $freeShiftConfig['free_shift_day_shift_duration'];
            $nightHour = $freeShiftConfig['free_shift_night_shift_duration'];
            //4-16点 白班
            if ($hour >= 4 && $hour < 16) {
                $seconds = $dayHour*3600;
            } else {
                $seconds = $nightHour*3600;
            }
        }else{

            $seconds = 0;
            if ($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
                if ($shift_index == 2) {
                    $seconds = HrShiftServer::getShiftSeconds($attendance_date, $shiftInfo['first_start'],
                        $shiftInfo['first_end']);
                } elseif ($shift_index == 4) {
                    $seconds = HrShiftServer::getShiftSeconds($attendance_date, $shiftInfo['second_start'],
                        $shiftInfo['second_end']);
                }
            } else {
                $seconds = HrShiftServer::getShiftSeconds($attendance_date, $shiftInfo['first_start'],
                    $shiftInfo['first_end']);
            }
        }

        switch ($leaveInfo) {
            case 3:
                $seconds = 0;
                break;
            case 2://（请假-后半天)
                if ($isLive) {
                    $seconds = $seconds / 2;
                    break;
                }

                if ($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
                    if ($shift_index != enums::ATTENDANCE_SHIFT_INDEX_2) {
                        $seconds = 0;
                        break;
                    }
                    $seconds = HrShiftServer::getShiftSeconds($attendance_date, $shiftInfo['first_start'],
                        $shiftInfo['first_end']);
                } else {
                    if (!empty($shiftInfo['is_middle_rest'])) {
                        $seconds = HrShiftServer::getShiftSeconds($attendance_date, $shiftInfo['first_start'],
                            $shiftInfo['middle_rest_start']);
                    } else {
                        $seconds = HrShiftServer::getShiftSeconds($attendance_date, $shiftInfo['first_start'],
                                $shiftInfo['first_end']) / 2;
                    }
                }
                break;
            case 1://（请假-前半天）
                if ($isLive) {
                    $seconds = $seconds / 2;
                    break;
                }
                if ($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
                    if ($shift_index != enums::ATTENDANCE_SHIFT_INDEX_4) {
                        $seconds = 0;
                        break;
                    }
                    $seconds = HrShiftServer::getShiftSeconds($attendance_date, $shiftInfo['second_start'],
                        $shiftInfo['second_end']);
                } else {
                    if (!empty($shiftInfo['is_middle_rest'])) {
                        $seconds = HrShiftServer::getShiftSeconds($attendance_date, $shiftInfo['middle_rest_end'],
                            $shiftInfo['first_end']);
                    } else {
                        $seconds = HrShiftServer::getShiftSeconds($attendance_date, $shiftInfo['first_start'],
                                $shiftInfo['first_end']) / 2;
                    }
                }
                break;
        }

        if (empty($seconds)) {
            return true;
        }

        $time = strtotime($now) - strtotime(show_time_zone($attendanceInfo['started_at']));

        if ($time < $seconds) {
            $this->makeAttendanceDurationLessNotice($staff_info,$seconds);
        }
        return true;
    }





    /**
     * @param $staffInfo
     * @param $paramsIn
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function check_staff_punch_out($staffInfo, $paramsIn)
    {

        $this->staffInfo = $staffInfo;
        $this->platform  = $paramsIn['platform'];

        $returnData      = [
            'data' => (object)[],
            'code' => -3,
            'msg'  => 'fail',
        ];
        if (env('close_punch_out_check', 0)) {
            return $returnData;
        }
        //两个班次的 第1个下班 不验证
        if($paramsIn['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE &&  $paramsIn['shift_index'] == enums::ATTENDANCE_SHIFT_INDEX_2){
            $returnData['code'] = 1;
            $returnData['msg']  = 'success';
            return $returnData;
        }


        //验证java
        $check_java_task = $this->check_java_task($paramsIn['is_skip']);
        if (!empty($check_java_task)) {
            $returnData['data'] = $check_java_task;
            return $returnData;
        }

        //验证未读的消息
        $check_un_read_message = $this->check_un_read_message();
        if (!empty($check_un_read_message)) {
            $returnData['data'] = $check_un_read_message;
            return $returnData;
        }

        // 验证未完成学习计划
        $check_un_finished_study = $this->check_un_finished_study();
        if (!empty($check_un_finished_study)) {
            $returnData['data'] = $check_un_finished_study;
            return $returnData;
        }

        //验证是否有未添加辅导员的员工
        $check_un_instructor = $this->check_un_instructor_task();
        if(!empty($check_un_instructor)) {
            $returnData['data'] = $check_un_instructor;
            return $returnData;
        }

        // 验证kpi 是否已经完成
        $verifyKpiResult = (new KpiServer($this->lang,$this->timezone))->verifyKpiIsCompleted($staffInfo);
        if (!empty($verifyKpiResult)) {
            $returnData['data'] = $verifyKpiResult;
            return $returnData;
        }

        //验证是否有未处理完成资产
        $verify_has_resign_assets = $this->check_un_has_resign_assets();
        if (!empty($verify_has_resign_assets)) {
            $returnData['data'] = $verify_has_resign_assets;
            return $returnData;
        }

        //验证是否有未审批的续签合同
        $verify_has_has_approval_renew_contract = $this->check_un_has_approval_renew_contract();
        if (!empty($verify_has_has_approval_renew_contract)) {
            $returnData['data'] = $verify_has_has_approval_renew_contract;
            return $returnData;
        }

        //验证是否有待确认转岗
        $check_unconfirmed_transfer = $this->check_unconfirmed_transfer();
        if (!empty($check_unconfirmed_transfer)) {
            $returnData['data'] = $check_unconfirmed_transfer;
            return $returnData;
        }

        //验证是否有未盘任务
        $verify_has_undone_inventory_task = (new MaterialInventoryCheckServer($this->lang, $this->timezone))->verifyHasUndoneInventoryTask($staffInfo);
        if (!empty($verify_has_undone_inventory_task)) {
            $returnData['data'] = $verify_has_undone_inventory_task;
            return $returnData;
        }

        //21954非一线员工试用期管理优化
        $probationTargetServer = new ProbationTargetServer($this->lang, $this->timezone);
        $targetCheck       = $probationTargetServer->checkStaffManagerSign($staffInfo['id']);
        if (!empty($targetCheck)) {
            $returnData['data'] = $targetCheck;
            return $returnData;
        }


        //验证是否有未完成转正评估目标制定
        $check_probation_target = $this->check_probation_target();
        if (!empty($check_probation_target)) {
            $returnData['data'] = $check_probation_target;
            return $returnData;
        }

        //验证是否有到最后一天期限没有完成评估或有已超时的评估
        $check_probation_timeout = $this->check_probation_timeout();
        if (!empty($check_probation_timeout)) {
            $returnData['data'] = $check_probation_timeout;
            return $returnData;
        }
        
        $returnData['code'] = 1;
        $returnData['msg']  = 'success';
        return $returnData;
    }

    public function check_probation_timeout(): array
    {
        $returnData       = [];
        $staff_info_id = $this->staffInfo['id'];
        if (empty($staff_info_id)){
            return $returnData;
        }
        $intercept_front_line_probation_punchout_date = (new SettingEnvServer())->getSetVal('intercept_front_line_probation_punchout_date');
        // 考勤打卡白名单
        $isInfWhite =  (new WhiteListServer())->isInfWhiteList($staff_info_id,date('Y-m-d'),[AttendanceWhiteListModel::TYPE_PAID_LOCALLY,AttendanceWhiteListModel::TYPE_NOT_PAID_LOCALLY]);
        if ($isInfWhite){
            return $returnData;
        }
        $before_check = HrProbationAuditModel::findFirst([
            "audit_id = :staff_info_id:",
            "bind" => [
                "staff_info_id" => $staff_info_id,
            ],
        ]);
        if (empty($before_check)) {
            return $returnData;
        }
        // 转正白名单
        $probation_staff = (new SettingEnvServer())->getSetValToArray('probation_staff');
        $deadline = date('Y-m-d', strtotime('+1 day'));
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("a.id,a.audit_status,a.cur_level,a.staff_info_id,a.deadline_at,a.is_active,p.probation_channel_type,s.working_country,s.hire_date");
        $builder->from(['a' => HrProbationAuditModel::class]);
        $builder->leftjoin(HrProbationModel::class, "p.staff_info_id = a.staff_info_id", 'p');
        $builder->leftjoin(HrStaffInfoModel::class, "p.staff_info_id = s.staff_info_id", 's');
        $builder->andWhere(' a.audit_id = :audit_id:  and p.is_system = :is_system:', [
            'audit_id' => $staff_info_id,
            'is_system'=>HrProbationModel::IS_NOT_SYSTEM,
        ]);
        $builder->andWhere('(p.probation_channel_type = :not_frontline: and  p.status != :p_status: and  s.state != :state:) or (p.probation_channel_type != :not_frontline: and  s.state = :on_job_state: and s.wait_leave_state = 0 and  p.status  = :audit_pending: )',
            [
                'not_frontline' => HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE,
                'state'         => HrStaffInfoModel::STATE_2,
                'p_status'      => HrProbationModel::STATUS_FORMAL,
                'on_job_state'  => HrStaffInfoModel::STATE_ON_JOB,
                'audit_pending' => HrProbationModel::STATUS_PROBATION,
            ]);
        if (!empty($probation_staff)){
            $builder->notInWhere('s.staff_info_id ',$probation_staff);
        }
        $builder->andWhere('s.nationality = :nationality:', ['nationality'=>HrStaffInfoModel::NATIONALITY_MY]);

        $builder->orderby('a.id desc');

        $staffData = $builder->getQuery()->execute()->toArray();
        if (empty($staffData)){
            return $returnData;
        }
        $new_query = $old_query = [];
        foreach ($staffData as $value) {
            if ($value['probation_channel_type'] != HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE) {
                if ($value['working_country'] != HrStaffInfoModel::WORKING_COUNTRY_MY) {
                    continue;
                }
                if ($intercept_front_line_probation_punchout_date && substr($value['hire_date'], 0,
                        10) <= substr($intercept_front_line_probation_punchout_date, 0, 10)) {
                    continue;
                }
            }
            if ((!empty($value['deadline_at']) && $value['audit_status'] == HrProbationAuditModel::AUDIT_STATUS_PENDING && $value['deadline_at'] <= $deadline)
                || ($value['audit_status'] == HrProbationAuditModel::AUDIT_STATUS_TIMEOUT && $value['is_active'] == 0)) {
                if ($value['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE) {
                    $new_query = [
                        'staff_info_id'          => $value['staff_info_id'],
                        'id'                     => $value['id'],
                        'audit_status'           => $value['audit_status'],
                        'cur_level'              => $value['cur_level'],
                        'probation_channel_type' => $value['probation_channel_type'],
                    ];
                } else {
                    $old_query = [
                        'id'                     => $value['id'],
                        'auditStatus'            => $value['audit_status'],
                        'probation_channel_type' => $value['probation_channel_type'],
                    ];
                }
                break;
            }
        }
        $t = $this->getTranslation();
        if (!empty($new_query)) {
            $base_url                     = env('h5_endpoint');
            $returnData['business_type']  = 'jump_h5';
            $returnData['jump_h5_detail'] = [
                'dialog_status'      => 1,
                'dialog_must_status' => 1,
                'dialog_msg'         => $t->_('unfinished_probationary_period_evaluation_22545'),
                'dialog_btn_msg'     => $t->_('go_to_evaluate_btn_22545'),
                'dialog_jump_url'    => $base_url . 'regular-assess?' . http_build_query($new_query),
            ];
            return $returnData;
        }
        if (!empty($old_query)) {
            $base_url                     = env('sign_url');
            $returnData['business_type']  = 'jump_h5';
            $returnData['jump_h5_detail'] = [
                'dialog_status'      => 1,
                'dialog_must_status' => 1,
                'dialog_msg'         => $t->_('unfinished_probationary_period_evaluation_22545'),
                'dialog_btn_msg'     => $t->_('go_to_evaluate_btn_22545'),
                'dialog_jump_url'    => $base_url . '/#/EvaluateDetail?' . http_build_query($old_query),
            ];
            return $returnData;
        }

        return [];
    }
    /**
     * 验证是否有未添加辅导员的员工 非网点主管角色不验证
     * 历史逻辑复用泰国
     * @return array
     */
    protected function check_un_instructor_task() {
        $this->logger->write_log('check_un_instructor_task '.$this->staffInfo['staff_id']. ' staff_info:'.json_encode($this->staffInfo,JSON_UNESCAPED_UNICODE),'info');
        $returnData = [];
        //非网点主管角色不验证
        if(!in_array(RolesModel::ROLE_DOT_ADMIN, $this->staffInfo['positions'])){
            return  $returnData;
        }
        //$paramIn['store_id'] = $this->staffInfo['organization_id'];
        $paramIn['is_three_day'] = true;
        $paramIn['user'] = $this->staffInfo;
        $count = (new StaffServer())->getStaffJobTitleInstructorCount($paramIn);
        if($count > 0) {
            $returnData['business_type'] = 'un_finished_study';
            $returnData['training_detail'] = [
                'message' => $this->getTranslation()->_('add_instructor_error_4'),
                'url' => env("sign_url").'/#/boardConfirm?active=tab-two&tab=1&sub_tab=2',
            ];
        }
        $this->logger->write_log('check_un_instructor_task '.$this->staffInfo['staff_id']. ' result:'.json_encode($count,JSON_UNESCAPED_UNICODE),'info');
        return $returnData;
    }

    /**
     * 获取上班打卡状态
     */
    public function checkClockInBefore($paramIn, $userinfo)
    {
        $returnData = [
            'data' => (object)[],
            'code' => ErrCode::FAIL,
            'msg'  => 'fail',
        ];

        //上班打卡记录日志
        (new PunchInServer())->pushData($userinfo);

        //未回公款判断
        if (
            (in_array(3, $userinfo['positions']) || in_array(18, $userinfo['positions'])) &&
            !$this->getClockSkip(ClockEnums::BUSINESS_TYPE_REMITTANCE_DIALOG, $userinfo['id'])
        ) {
            $country_pre = '_' . strtolower(env('country_code', 'th'));
            //网点经理/ 主管打上班卡时显示快递员因回公款停职的员工
            $res = (new OtherRepository())->stopDutiestaff($userinfo);

            //原来的逻辑不做处理
            if ($res) {
                $staffids = implode("\r\n", array_column($res, 'staff_info_id'));
                $show_msg = $this->t->_('remittanceDialogMsg_5' . $country_pre) ?? $this->t->_('remittanceDialogMsg_5');
                $show_msg = str_replace('{staffids}', $staffids, $show_msg);

                $res['message']     = $show_msg;
                $res['is_can_skip'] = true;
                $res['buttons']     = [
                    'button_content' => $this->t->_(ClockEnums::BUSINESS_TYPE_REMITTANCE_DIALOG_BTN),
                    'button_type'    => ClockEnums::BUTTON_TYPE_API,
                    'path'           => $this->assembleSkipUrl(ClockEnums::BUSINESS_TYPE_REMITTANCE_DIALOG,$paramIn['platform']),
                ];

                $returnData['data'] = $this->formatClockInBeforeData($res);
                return $this->checkReturn($returnData);
            }
        }

        //未签订协议判定
        $res = (new AgreementSignRecordServer())->checkStaffAgreement($userinfo['id']);

        if ($res) {
            $res['title']   = $this->t->_(ClockEnums::BUSINESS_TYPE_AGREEMENT_SIGN_TITLE);
            $res['message'] = $this->t->_(ClockEnums::BUSINESS_TYPE_AGREEMENT_SIGN_MSG);
            $res['buttons'] = [
                'button_content' => $this->t->_(ClockEnums::BUSINESS_TYPE_AGREEMENT_SIGN_BTN),
                'button_type'    => ClockEnums::BUTTON_TYPE_PAGE,
                'path'           => $this->assembleUiUrl($res['message_id'],$paramIn['platform']),
            ];

            $returnData['data'] = $this->formatClockInBeforeData($res);

            return $this->checkReturn($returnData);
        }

        $returnData['code'] = ErrCode::SUCCESS;
        $returnData['msg']  = 'success';

        return $this->checkReturn(1);
    }

    /**
     * 验证未读消息
     * @return array
     */
    protected function check_un_read_message(): array
    {
        $returnData     = [];
        $backyardServer = new BackyardServer($this->lang, $this->timezone);
        $messageInfo    = $backyardServer->punch_out_msg([
            'staff_id'        => $this->staffInfo['id'],
            'request_channel' => 'off_work_call',
        ]);
        if (!empty($messageInfo)) {
            $data['msg_id'] = strval(current($messageInfo));
            //构建业务类型返回参数
            $returnData['business_type']  = 'un_read_message';
            $returnData['message_detail'] = $data;
        }
        $this->logger->write_log('check_un_read_message '.$this->staffInfo['id'].' result:'.json_encode($returnData,
                JSON_UNESCAPED_UNICODE), 'info');

        return $returnData;
    }

    /**
     * @return array
     */
    protected function check_unconfirmed_transfer(): array
    {
        $staffId = $this->staffInfo['staff_id'];
        $transferInfo = (new JobTransferConfirmServer($this->lang, $this->timezone))->getPendingConfirmInfo($staffId);
        if (!empty($transferInfo)) {
            $returnData['business_type'] = 'un_confirmed_transfer';

            if (in_array($transferInfo->after_hire_type, HrStaffInfoModel::$agentTypeTogether)) {
                $errMsgExistConfirm = 'job_transfer.err_msg_exist_agent_confirm';
            } else {
                $errMsgExistConfirm = 'job_transfer.err_msg_exist_confirm';
            }
            $returnData['un_confirmed_transfer'] = [
                'message' => $this->getTranslation()->_($errMsgExistConfirm),
                'url'     => env("sign_url") . sprintf('/#/job-transfer/confirm-detail?id=%d&from=app', $transferInfo->id),
            ];
        }
        return $returnData ?? [];
    }
}