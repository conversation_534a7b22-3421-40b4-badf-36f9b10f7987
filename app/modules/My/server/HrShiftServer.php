<?php


namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;
use FlashExpress\bi\App\Server\HrShiftServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class HrShiftServer extends GlobalBaseServer
{

    /**
     * @param $staffId
     * @param array $dateList
     * 返回结构字段 date_at start end shift_type shift_id + 马来 特殊字段
     * @return array
     */
    public function getShiftInfos($staffId, array $dateList = [])
    {
        if (empty($dateList)) {
            return [];
        }
        $startDate = min($dateList);
        $endDate   = max($dateList);
        $server    = new AttendanceCalendarV2Server($this->lang, $this->timeZone);
        $shiftData = $server->getDailyShiftInfo($staffId, $startDate, $endDate);

        if (empty($shiftData)) {
            return [];
        }
        foreach ($shiftData as &$shift) {
            $shift['date_at'] = $shift['shift_date'];
            $shift['start']   = $shift['first_start'];
            $shift['end']     = $shift['first_end'];
            if ($shift['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
                $shift['end'] = $shift['second_end'];
            }
        }

        $liveJobId = (new SettingEnvServer())->getSetVal('free_shift_position');
        $liveJobId = empty($liveJobId) ? [] : explode(',', $liveJobId);
        //查询transfer 主播职位没班次
        $transferData = $this->transferData;
        if (empty($this->transferData)) {
            $transferData = HrStaffTransferModel::find([
                'columns'    => 'staff_info_id, stat_date, job_title',
                'conditions' => 'staff_info_id = :staff_id: and stat_date in ({dates:array})',
                'bind'       => [
                    'staff_id' => $staffId,
                    'dates'    => $dateList,
                ],
            ])->toArray();
            $transferData = empty($transferData) ? [] : array_column($transferData, null, 'stat_date');
        }


        //整理班次 加上日期
        $return = [];
        foreach ($dateList as $date) {
            //主播职位没班次 弹性工作
            if (!empty($transferData[$date]) && in_array($transferData[$date]['job_title'], $liveJobId)) {
                continue;
            }
            $return[$date] = $this->formatShiftData($shiftData[$date]);
        }
        return $return;
    }

}