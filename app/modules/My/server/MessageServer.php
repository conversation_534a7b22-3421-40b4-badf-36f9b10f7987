<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\MessageWarningModel;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Server\BackyardServer;
use Phalcon\Db;
use FlashExpress\bi\App\Server\MessageServer AS GlobalBaseServer;

class MessageServer extends GlobalBaseServer
{

    protected $re;
    public $timezone;

    public function __construct($lang = 'zh-CN',$timezone){
        parent::__construct($lang, $timezone);
        $this->timezone =  $timezone;
    }


    //电子警告书 电子签名 保存图片
    public function sign_for_warning($param){

        $param = filter_param($param);
        // 更新签名图片
        //判断身份 后 发送给上级消息 或 证人消息
        $warningInfo = $this->getWarningMessage($param['staff_info_id'], $param['msg_id']);
        $model = new BaseRepository($this->lang);

        $db = $this->getDI()->get("db");

        $updateInfo['sign_state'] = MessageWarningModel::SING_STATE_ONGOING;
        if ($warningInfo['role'] == self::MSG_STAFF_TYPE_STAFF) {
            if ($warningInfo['img_url']) {
                return $this->checkReturn(-3, $this->getTranslation()->_('5202'));
            }
            $updateInfo['img_url'] = $param['url']; // 更新签名;
            // 获取上级
            $mangerInfo = HrStaffItemsModel::findFirst([
                'conditions' => "staff_info_id = :staff_id: and item = 'MANGER'",
                'bind' => ['staff_id' => $param['staff_info_id']],
            ]);
            if ($mangerInfo) {
                // 更新表中上级信息
                $mangerInfo = $mangerInfo->toArray();
                $staffInfo = HrStaffInfoModel::findFirst([
                    'conditions' => "staff_info_id = :staff_id:",
                    'bind' => ['staff_id' => $mangerInfo['value']],
                ]);
                if($staffInfo) {
                    $staffInfo = $staffInfo->toArray();
                    if (!$warningInfo['superior_kit_id']) {
                        // 如果没有发送给上级 发送上级给消息
                        $updateInfo['superior_id'] = (int)$mangerInfo['value'];
                        $updateInfo['superior_name'] = $staffInfo['name'];
                        $this->sendWarningMessage($mangerInfo['value'], $warningInfo['id'], $warningInfo['role']);
                    }else{
                        // 上级是否签字 
                        if (!empty($warningInfo['superior_img'])){
                            // 上级已签字 
                            $updateInfo['sign_state'] = MessageWarningModel::SING_STATE_COMPLETED;
                        }
                        
                    }
                    $flag = $db->updateAsDict("message_warning", $updateInfo, ["conditions" => 'id=' . $warningInfo['id']]);
                }
            } else {
                $flag = true;
                $this->getDI()->get("logger")->write_log($param['staff_info_id'] . " 不存在上级", "error");
            }

        } else if ($warningInfo['role'] == self::MSG_STAFF_TYPE_SUPERIOR) {
            if ($warningInfo['superior_img']) {
                return $this->checkReturn(-3, $this->getTranslation()->_('5202'));
            }

            // 被警告人是否签字 
            if (!empty($warningInfo['img_url'])){
                // 被警告人已签字 
                $updateInfo['sign_state'] = MessageWarningModel::SING_STATE_COMPLETED;
            }
            $updateInfo['superior_img'] = $param['url'];
            $updateInfo['superior_remark'] = !empty($param['remark']) ? $param['remark'] : '';
            // 更新签名
            // 证人信息
            $flag = $db->updateAsDict("message_warning", $updateInfo, ["conditions" => 'id=' . $warningInfo['id']]);
            
            // 发送证人消息，my没有证人消息，只写pdf
            $this->sendWarningMessage([], $warningInfo['id'], $warningInfo['role']);
        }


        // 消息更新为已读
        if(isset($flag) && $flag){
            //如果保存成功 设置为已读
            //$up_sql = "update message_courier set read_state = 1 where id = '{$param['msg_id']}'";
            //$this->getDI()->get('db_coupon')->execute($up_sql);

            $db = $this->getDI()->get('db_coupon');
            $db->updateAsDict("message_courier", ['read_state'=>1], ['conditions' => "id='".$param['msg_id']."'"]);

            // 消息已读
            $server = new BackyardServer($this->lang, $this->timezone);
            $server->addHaveReadMsgToMns($param['msg_id']);

            // 签字日志
            $sign_log_data = [
                'staff_info_id'      => $param['staff_info_id'] ?? 0,
                'kit_id'             => $param['msg_id'] ?? '',
                'message_warning_id' => $warningInfo['id'] ?? 0,
                'is_auto_sign'       => 0,
            ];
            $this->getDI()->get('db')->insertAsDict('message_warning_sign_log', $sign_log_data);
        }

        return true;

    }

}