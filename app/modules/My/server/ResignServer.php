<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use App\Library\ErrCode;
use Exception;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\HireTypeImportListModel;
use FlashExpress\bi\App\Models\backyard\ResignFileModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReasonModel;
use FlashExpress\bi\App\Models\backyard\StaffResignModel;
use FlashExpress\bi\App\Modules\My\library\Enums\CommonEnums;
use FlashExpress\bi\App\Server\formPdfServer;
use FlashExpress\bi\App\Server\MailServer;
use FlashExpress\bi\App\Server\ResignServer as BaseResignServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\ToolServer;

class ResignServer extends BaseResignServer
{

    protected function afterFinalExecute($audit_id){

        $redis = $this->getDI()->get('redisLib');
        $this->getDI()->get("logger")->write_log('afterFinalExecute sendNotice push resign_id : ' . $audit_id, "info");
        return $redis->lpush(RedisEnums::LIST_MY_REGION_CONFIRM_PDF, $audit_id);
    }

    /**
     * @throws \Exception
     * 新改版 https://flashexpress.feishu.cn/wiki/DnEsweXnIi7RnkkPwbmcl7UnnMh
     */
    public function resignSendPdf($audit_id): bool
    {
        $resignInfo        = $this->resign->getResignInfo(['id' => $audit_id]);

        if (!in_array($resignInfo['reason'],[StaffLeaveReasonModel::CODE_INDIVIDUAL_CONTRACTOR,StaffLeaveReasonModel::LEAVE_REASON_96])) {
            $this->logger->write_log(['resignInfo' => $resignInfo, 'leave_reason' => 'leave_reason not to do IC'], 'info');
            return true;
        }

        $staffInfo         = (new StaffServer())->getStaffById($resignInfo['submitter_id']);

        if (empty($staffInfo['personal_email'])) {
            $this->logger->write_log(['staff_info' => $staffInfo, 'personal_email' => 'personal_email empty '], 'info');
            return true;
        }

        $pdfData['insert_date']       = date('Y-m-d');
        $pdfData['name']              = $staffInfo['name'];
        $pdfData['staff_info_id']     = $staffInfo['staff_info_id'];
        $pdfData['submit_date']       = date('Y-m-d',strtotime($resignInfo['created_at']));
        $pdfData['last_work_date']    = $resignInfo['last_work_date'];

        $template_list = (new SettingEnvServer())->listByCode([
            'default_template_header',
            'default_template_footer',
            'resign_confirm_pdf_template_new',
        ]);
        if (!empty($template_list)) {
            $template_list = array_column($template_list, 'set_val', 'code');
        }

        $html_oss_url   = $template_list['resign_confirm_pdf_template_new'];//app/views/resign/resign_confirm.ftl
        $headerTemplate = $template_list['default_template_header'];
        $footerTemplate = $template_list['default_template_footer'];

        $pdf_header_footer_setting = [
            'displayHeaderFooter' => true,
            'headerTemplate'      => $headerTemplate,
            'footerTemplate'      => $footerTemplate,
        ];
        $pdf_file_data             = (new formPdfServer())->generatePdf($html_oss_url, $pdfData, null,
            $staffInfo['staff_info_id'], $pdf_header_footer_setting, 'attchment');
        if (empty($pdf_file_data['object_url'])) {
            throw new Exception('my resign make pdf error ');
        }
        $fileUrl = $pdf_file_data['object_url'];
        

        $tmpFilePath = sys_get_temp_dir() . '/' . basename($fileUrl).'.pdf';
        if (!file_put_contents($tmpFilePath, file_get_contents($fileUrl))) {
            throw new Exception('pdf下载失败');
        }
        //固化离职确认信函
        $sql ="update staff_resign set leave_confirm_pdf_url = ?,updated_at = updated_at where resign_id = ?";
        $this->getDI()->get('db')->execute($sql,[$fileUrl,$audit_id]);


        $title      = "Notice of Resignation dated " . $pdfData['submit_date'];
        $content    = <<<EOF
    Dear %s，
    Referring to the above matter. 
    We enclosed herein our letter dated %s for your kind attention.
    Thank you.
EOF;
        $content    = sprintf($content, $staffInfo['name'], $pdfData['insert_date']);
        $fileName   = 'Notice of Resignation '.$staffInfo['staff_info_id'].'.pdf';
        $mailFlag   = (new MailServer())->send_mail($staffInfo['personal_email'], $title, $content, $tmpFilePath,
            $fileName);
        unlink($tmpFilePath);
        $this->logger->write_log('send email to ' . $staffInfo['personal_email'].' result is ' . $mailFlag , 'info');
        return true;
    }


    /**
     * 扣减short notice
     * @param $staff_info_id
     * @return bool
     * @throws Exception
     */
    public function reductionShortNotice($staff_info_id,$sign_date): bool
    {
        $hireTypeImportInfo = HireTypeImportListModel::findFirst([
            'conditions' => 'new_staff_id = :new_staff_id: and entry_state = :entry_state:',
            'bind'       => [
                'new_staff_id' => $staff_info_id,
                'entry_state'    => HireTypeImportListModel::STATUS_EMPLOYED,
            ],
        ]);

        if(empty($hireTypeImportInfo)){
            return true;
        }
        $resignInfo = StaffResignModel::findFirst([
            'conditions' => ' submitter_id = :submitter_id: and status in ({status:array}) and reason in ({reason:array}) and resign_id = :resign_id:',
            'bind'       => [
                'submitter_id' => $hireTypeImportInfo->old_staff_id,
                'reason'       => [StaffLeaveReasonModel::CODE_INDIVIDUAL_CONTRACTOR,StaffLeaveReasonModel::LEAVE_REASON_96],
                'status'       => [enums::$audit_status['timedout'], enums::$audit_status['approved']],
                'resign_id'    => $hireTypeImportInfo->staff_resign_id,
            ],
            'order'      => 'resign_id desc'
        ]);

        if (empty($resignInfo)) {
            return true;
        }

        $short_notice_days = round((strtotime($resignInfo->resignation_leave_day) - strtotime($resignInfo->leave_date)) / (86400));

        if($short_notice_days <= 0){
            return  true;
        }
        if (round((strtotime($sign_date) - strtotime($resignInfo->leave_date)) / (86400)) > 2) {
            $info = [
                'sign_date'  => $sign_date,
                'leave_date' => $resignInfo->leave_date,
            ];
            $this->logger->write_log($info, 'info');
            return true;
        }

        $update_field = [
            'source'                 => 0,
            'reduction_short_notice' => $short_notice_days,
            'updated_at'             => new \Phalcon\Db\RawValue('updated_at'),
        ];

        $this->getDI()->get('db')->updateAsDict('staff_resign',
            $update_field,
            [
                'conditions' => "resign_id = ?",
                'bind'       => [$resignInfo->resign_id],
            ]);

        $mq = new RocketMQ('cal-short-notice');
        $mq->sendToMsg(['staff_info_id' => $hireTypeImportInfo->old_staff_id],3);

        $this->logger->write_log(['resign_id' => $resignInfo->resign_id, 'reduction_short_notice' => $short_notice_days], 'info');

        $staffInfo         = (new StaffServer())->getStaffById($hireTypeImportInfo->old_staff_id);

         if (empty($staffInfo['personal_email'])) {
            $this->logger->write_log(['staff_info' => $staffInfo, 'reductionShortNotice' => 'personal_email empty '], 'info');
            return true;
        }

        //获取 上面任务发送离职信的时间（离职申请 审批通过或超时时间 apply表）
        $applyInfo                   = AuditApplyModel::findFirst([
            'conditions' => 'biz_type = :biz_type: and biz_value = :biz_value:',
            'bind'       => ['biz_type' => AuditListEnums::APPROVAL_TYPE_RN, 'biz_value' => $resignInfo->resign_id],
        ]);
        $resignSendDate              = empty($applyInfo) ? '' : date('Y-m-d', strtotime($applyInfo->final_approval_time));
        $region_pdf_date             = show_time_zone($resignInfo->updated_at, 'Y-m-d');
        $pdfData['insert_date']      = date('Y-m-d');//信函生成日(第一行)
        $pdfData['name']             = $staffInfo['name'];//旧工号对应名字
        $pdfData['staff_info_id']    = $staffInfo['staff_info_id'];//旧工号
        $pdfData['region_pdf_date']  = !empty($resignSendDate) ? $resignSendDate : $region_pdf_date;//公司确认员工离职发送邮件日期(第六行)
        $pdfData['leave_date']       = date('Y-m-d', strtotime($staffInfo['leave_date']));//离职日
        $pdfData['entry_date']       = $hireTypeImportInfo->actual_entry_date;//入职日
        $pdfData['short_notice_day'] = $short_notice_days;//扣减之前的 short notice 天数

        //邮件title 用
        $pdfData['submit_date']        = show_time_zone($resignInfo->created_at, 'Y-m-d');//员工提交申请创建时间
        $template_list = (new SettingEnvServer())->listByCode([
            'default_template_header',
            'default_template_footer',
            'reduction_short_notice_pdf_template_new',
        ]);
        if (!empty($template_list)) {
            $template_list = array_column($template_list, 'set_val', 'code');
        }

        $headerTemplate = $template_list['default_template_header'];
        $footerTemplate = $template_list['default_template_footer'];
        $html_oss_url   = $template_list['reduction_short_notice_pdf_template_new'];//app/views/resign/short_notice.ftl

        $pdf_header_footer_setting = [
            'displayHeaderFooter' => true,
            'headerTemplate'      => $headerTemplate,
            'footerTemplate'      => $footerTemplate,
        ];
        $pdf_file_data             = (new formPdfServer())->generatePdf($html_oss_url, $pdfData, null,
            'reduction_short_notice_'.$staffInfo['staff_info_id'], $pdf_header_footer_setting, 'attchment');
        if (empty($pdf_file_data['object_url'])) {
            throw new Exception('my resign make pdf error ');
        }
        $fileUrl = $pdf_file_data['object_url'];

        $tmpFilePath = sys_get_temp_dir() . '/' . basename($fileUrl).'.pdf';
        if (!file_put_contents($tmpFilePath, file_get_contents($fileUrl))) {
            throw new Exception('pdf下载失败');
        }


        $title      = "Notice of Resignation dated " . $pdfData['submit_date'];
        $content    = <<<EOF
    Dear %s，
    Referring to the above matter. 
    We enclosed herein our letter dated %s for your kind attention.
    Thank you.
EOF;
        $content    = sprintf($content, $staffInfo['name'], $pdfData['insert_date']);
        $fileName   = 'Notice of Resignation '.$staffInfo['staff_info_id'] .'.pdf';
        $mailFlag   = (new MailServer())->send_mail($staffInfo['personal_email'], $title, $content, $tmpFilePath,
            $fileName);
        unlink($tmpFilePath);
        $this->logger->write_log('send email to ' . $staffInfo['personal_email'].' result is ' . $mailFlag , 'info');
        return true;
    }


    /**
     * Acceptance of Resignation
     * 离职接收书
     * @param $resignInfo
     * @return mixed
     */
    public function afterAuditFullOtherData($resignInfo): string
    {
        $lang = (new StaffServer())->getLanguage($resignInfo['submitter_id']);
        if (!$lang || $lang != 'zh-CN') { // 空 或者 是中文
            $lang = 'en';
        }

        $staffInfo = $this->staff->getStaffInfo($resignInfo['submitter_id']);
        $form_data = [
            'day'             => date('Y-m-d'),
            'staff_name'      => $staffInfo['name'],
            'staff_info_id'   => $staffInfo['staff_id'],
            'job_title'       => $staffInfo['job_title_name'],
            'department_name' => $staffInfo['department_name'],
            'created_at'      => date('Y-m-d', strtotime($resignInfo['created_at'])),//已经转换过了
            'last_work_date'  => $resignInfo['last_work_date'],
            'is_lnt'          => (new StaffServer())->isLntStaff($resignInfo['submitter_id']),
        ];

        //公司配置log 等
        $queryParams = [
            'staff_info_id' => $staffInfo['staff_id'],
        ];
        $pdfHelperServer = new PdfHelperServer();
        $rpcSetting = $pdfHelperServer->getHeaderAndFooter($queryParams);
        [$headerTemplate, $footerTemplate] = $pdfHelperServer->getHeaderFooter($rpcSetting);

        $html_oss_url   = $lang == 'zh-CN' ? 'resign_audit_template_content_zh.ftl' : 'resign_audit_template_content_en.ftl';
        $html_oss_url   = APP_PATH . '/views/resign/' . $html_oss_url;
        $html_oss_url = (new ToolServer($this->lang, $this->timezone))->getPdfTemp($html_oss_url);

        $form_data['company_full_name'] = $rpcSetting['company_full_name'] ?? '';

        $pdf_header_footer_setting = [
            'displayHeaderFooter' => true,
            'headerTemplate' => $headerTemplate,
            'footerTemplate' => $footerTemplate
        ];

        $pdf_file_data = (new formPdfServer())->generatePdf($html_oss_url, $form_data, [], $staffInfo['staff_id'], $pdf_header_footer_setting, 'attchment');

        if (!empty($pdf_file_data['object_url'])){
            $email_title = $this->getTranslation('en')->_('formal_resign_email_title',['staff_info_id'=>$staffInfo['staff_id']]);
            $pdfFileName    = str_replace('/', '', $email_title);
            $resign_file = new ResignFileModel();
            $resign_file->staff_info_id = $staffInfo['staff_id'];
            $resign_file->type = ResignFileModel::TYPE_FORMAL_CANCEL_CONTRACT;
            $resign_file->file_url = $pdf_file_data['object_url'];
            $resign_file->file_name = $pdfFileName;
            $resign_file->email_title = $email_title;
            $resign_file->email_content = $this->getTranslation('en')->_('formal_resign_email_content');
            $resign_file->operate_id = 10000;
            $resign_file->save();
        }
        
        // 发送消息
        $kit_param['message_title'] = $this->getTranslation($lang)->_('leave_acceptance_of_resign');
        $src = env('sign_url') . "/#/leaveOfficeMessage?resign_id=" . $resignInfo['resign_id'];
        $content = "<meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no\" /><iframe style=\"outline: none;border: none;width: 100%\" src='{$src}' width='100%' height='85%'></iframe>";
        $kit_param['message_content'] = $content;
        $kit_param['add_userid'] = $resignInfo['submitter_id'];
        $kit_param['staff_users'] = array(0 => array('id' => $resignInfo['submitter_id']));
        $kit_param['category'] = MessageEnums::MESSAGE_CATEGORY_ACCEPTANCE_OF_RESIGN;
        $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', $lang));
        $bi_rpc->setParams($kit_param);
        $res = $bi_rpc->execute();
        $this->getDI()->get('logger')->write_log('add_kit_message.leave_acceptance_of_resign  '. json_encode($kit_param, JSON_UNESCAPED_UNICODE). " " . json_encode($res, JSON_UNESCAPED_UNICODE), 'info');

        return $pdf_file_data['object_url'];
    }
}