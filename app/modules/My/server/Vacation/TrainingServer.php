<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 5/5/23
 * Time: 2:16 PM
 */

namespace FlashExpress\bi\App\Modules\My\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\Server\Vacation\TrainingServer as GlobalServer;


class TrainingServer extends GlobalServer implements LeaveInterface
{

    private static $instance = null;


    //任务 每年初始化调用
    public static function getInstance($lang, $timezone)
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self($lang, $timezone);
        }
        return self::$instance;
    }

}