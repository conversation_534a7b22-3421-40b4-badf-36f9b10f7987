<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/21/23
 * Time: 5:56 PM
 */


namespace FlashExpress\bi\App\Modules\My\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\Server\Vacation\InternationalServer as GlobalServer;


class InternationalServer extends GlobalServer implements LeaveInterface{

    private static $instance = null;

    public $today;

    public $thisYear;


    //任务 每年初始化调用
    public static function getInstance($lang,$timezone){
        if(!self::$instance instanceof self){
            self::$instance = new self($lang,$timezone);
        }
        return self::$instance;
    }

    //验证申请日期 各国不一样
    public function timeCheck()
    {
        //跨国探亲假
        if (!empty($this->paramModel['is_bi'])) {
            return true;
        }
        if(!$this->is_leave_policy_department){
            $this->timeValidate(0, 100, '1018');
        }else{
            if($this->canleavetoday) {
                $this->timeValidate(0, 100, '1018');
            } else {
                $this->timeValidate(1, 100, 'limit_tomorrow_notice');
            }
        }
    }


}