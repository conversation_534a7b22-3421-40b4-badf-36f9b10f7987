<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/17/22
 * Time: 10:46 AM
 */

namespace FlashExpress\bi\App\Modules\My\Server\Vacation;

//马来 年假
use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveExtendModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\StaffResignModel;
use FlashExpress\bi\App\Models\backyard\StaffWaitingLeaveSendModel;
use FlashExpress\bi\App\Modules\My\Server\AuditServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\Vacation\AnnualServer as GlobalServer;

class AnnualServer extends GlobalServer implements LeaveInterface
{

    public $cycleInfo;//马来对应 入职日期 的申请周期信息
    public $invalidDates = [];//申请区间 超出有效期的 日期list
    public $invalidDays  = 0;//申请区间超出有效期的天数 存在半天 情况 不能count

    public $effectiveDates  = [];//申请区间在有效期内的日期list
    public $effectiveDays   = 0;//在有效期内的 天数
    public $waitingUsedDays = 0;//实际 使用 当前已生效额度之外的天数


    public $countOutDates   = [];
    public $countOutDays    = 0;//结算日之之后的天数 不能超过三的那个
    public $hasApplyOutDays = 0;//已经申请的 占用的坑


    //操作扣减额度用
    public $currentApplyDays = 0;//当前周期 申请天数
    public $lastApplyDays    = 0;//上个周期 申请天数
    public $nextApplyDays    = 0;//下个周期 申请天数 预发放的 针对待离职的员工

    public $waitingAddDays     = 0;//待离职员工 预发放年假的 截止 申请结束日期 多发了多少天 未使用的
    public $waitingAddNextDays = 0;//预计发放的 下个周期的额度
    public $waitingAllDays     = 0;//预发放总额度
    public $waitingAddDate     = [];//预发放的 主键日期 标记 is_used字段用

    public $waitingLastDate = '';//待离职员工 最后工作日 非离职日期

    const HOLE = 3;//马来特殊规则 3个坑 上一年的 额度 最多不能超过3天 到下个周期

    public $reissueSpecialDepartment;//是否是特殊部门 限制不同

    public $isSvc;//工具查询额度标记
    public $isDetail;//是否是详情接口调用额度查询 需要待离职 明细
    public $waitingDetail = [];//工具和by详情调用 展示 预发放的一行字 存在0.5/0.5天预发放额度，生效时间yyyy.mm.dd。格式：“剩余/总额”

    //申请 保存入口
    public function handleCreate($param)
    {
        //初始化数据
        $this->initData($param);

        //逻辑验证
        $this->businessCheck();

        //format 数据
        $this->dataFormat();

        //保存
        $this->dataSave();

        return $this->auditId;
    }


    //额度查询 入口
    public function handleSearch($param)
    {
        //初始化数据
        $this->initSearch($param);

        //获取额度
        $this->cycleInfo = $this->get_cycle();
        $this->getLimitDays();

        //整理成 day_sub  day_limit
        return $this->formatLimitDays();
    }


    //构造数据
    protected function initData($param)
    {
        parent::initData($param);

        //--------定制

        //马来 年假周期
        $this->cycleInfo = $this->get_cycle();

        //获取额度
        $this->getLimitDays();

        $this->hasApplyOutDays = $this->leaveDaysByDate($this->staffInfo['staff_info_id'], $this->cycleInfo['cycle'], $this->cycleInfo['count_day']);

        //根据有效期拆分对应天数和日期
        $this->initSides();
    }


    //逻辑验证 通过后 保存相关数据表
    public function businessCheck()
    {
        //公共验证逻辑
        $this->publicValidate();

        //没权限 返回异常
        if (!$this->applyPermission()) {
            throw new ValidationException($this->getTranslation()->_('2107'));
        }

        //工具 放开离职员工限制
        if(empty($this->paramModel['is_bi']) && $this->staffInfo['state'] != 1){
            throw new ValidationException($this->getTranslation()->_('2107'));
        }

        //没有额度记录 异常数据验证
        if (empty($this->limitDays) || !isset($this->limitDays['limit'])) {
            $this->logger->write_log("年假额度异常 {$this->staffInfo['staff_info_id']} ".json_encode($this->limitDays),
                'info');
            throw new ValidationException($this->getTranslation()->_('leave_limit'));
        }

        //没转正 不让申请 验证 数据库现在是0 所以走不到这 因为 月薪制员工 不转正也能申请 这块马来就没用了
        if (!empty($this->leaveProperty->is_forbidden)) {
            if (!$this->hireCheck()) {
                $message = str_replace('leave_type', $this->getTranslation()->_($this->leaveProperty->language_key),
                    $this->getTranslation()->_('probation_limit'));
                throw new ValidationException($message);
            }
            if (!empty($this->staffInfo['formal_at']) && $this->paramModel['leave_start_time'] < $this->staffInfo['formal_at']) {
                $message = $this->getTranslation()->_('probation_before_limit');
                throw new ValidationException($message);
            }
        }

        //没有周期
        if (empty($this->cycleInfo)) {
            throw new ValidationException($this->getTranslation()->_('need hire date'));
        }

        $start_date = $this->paramModel['leave_start_time'];
        $end_date   = $this->paramModel['leave_end_time'];
        //如果开始时间在当前之后 六个月 不让申请
        if (date('Y-m-d', strtotime("+6 month")) < $start_date) {
            throw new ValidationException($this->getTranslation()->_('my_leave_for_6'));
        }


        //新需求 have days 新增 预发放额度 并且只能申请 发放日期之后的 才能用预发放额度
        $have_days = $this->limitDays['sub'] + $this->waitingAddDays;
        $need_days = $this->thisNum + $this->nextNum;//申请总天数

        //说明跳过休息日之后 用0天
        if (empty($need_days)) {
            throw new ValidationException('day off for the apply date');
        }

        //如果 当年是申请年 并且 在有效期 取去年剩余额度
        if (isset($this->limitDays['last_limit'])) {
            //去年剩余额度固化任务表
            $last_year_days = $this->limitDays['last_sub'] ?? 0;

            //如果在有效期 但是 申请日期不在 有效期不能用 剩余额度
            if($start_date >= $this->cycleInfo['invalid_day']){
                if ($have_days < $need_days) {
                    throw new ValidationException($this->getTranslation()->_('my_leave_out_invalid'));
                }

            }else if($start_date < $this->cycleInfo['invalid_day'] && $end_date >= $this->cycleInfo['invalid_day']){//在失效期两边
                //取失效期前有几天 与 上个周期剩余 比较 取小 !! 需要排除 ph
                $invalid_sub    = $this->effectiveDays;
                $last_year_days = $invalid_sub > $last_year_days ? $last_year_days : $invalid_sub;//去年剩余 与要申请的可以用去年额度的天数 取最小 去年的额度 超出的日期不能用
                $have_days      = $have_days + $last_year_days;
                if ($have_days < $need_days) {
                    throw new ValidationException($this->getTranslation()->_('my_leave_out_invalid'));
                }

            }else if($start_date < $this->cycleInfo['invalid_day']//都小于失效期 可以用去年的所有额度
                && $end_date < $this->cycleInfo['invalid_day']
//                    && $start_date >= $last_count_day

            ){
                $have_days += $last_year_days;
                if($have_days < $need_days){//虽然 额度不够 但是 提示语要分不同情况
                    if($this->staffInfo['wait_leave_state'] == HrStaffInfoModel::STATE_1){
                        //结束日期 小于当前日期 提示 已生效额度不够
                        if ($this->paramModel['leave_end_time'] < date('Y-m-d')) {
                            throw new ValidationException($this->getTranslation()->_('leave_limit_waiting_1'));
                        } else//提示 结束日 额度不够
                        {
                            throw new ValidationException($this->getTranslation()->_('leave_limit_waiting_2'));
                        }

                    }

                    throw new ValidationException($this->getTranslation()->_('leave_limit'));
                }

            }
        }

        //但是 申请区间在有结余日 把结余日外的 额度加一起 最多 3天 用这个
        if (!empty($this->cycleInfo) && ($start_date >= $this->cycleInfo['count_day'] || $end_date >= $this->cycleInfo['count_day'])) {
            //本次申请的 有效期外的天数  !! 需要排除 休息日
            $this_used = $this->countOutDays;
            //需要减去 标记到下周年的 预使用额度 算三坑
            $this_used -= $this->waitingUsedDays;

            if ($this->hasApplyOutDays > self::HOLE || ($this->hasApplyOutDays + $this_used) > self::HOLE) {
                throw new ValidationException($this->getTranslation()->_('my_leave_for_3'));
            }

            //如果 占用下周期的天数 大于 下周期 预发放的额度 不让申请
            if ($this->waitingUsedDays > $this->waitingAddNextDays) {
                throw new ValidationException($this->getTranslation()->_('my_leave_for_3'));
            }
        }

        if (empty($this->paramModel['is_bi'])) {
            //过去8天 到 未来
            if(!$this->is_leave_policy_department){
                $this->timeValidate(-8, 100, 'leave_16_notice_ph');
            }else{
                if($this->canleavetoday) {
                    $this->timeValidate(0, 100, '1018');
                } else {
                    $this->timeValidate(1, 100, 'limit_tomorrow_notice');
                }
            }
        }

        //总体额度 够用 验证通过
        if ($need_days <= $have_days) {
            return true;
        }

        //如果是待离职 提示内容不一样
        if ($this->staffInfo['wait_leave_state'] == HrStaffInfoModel::STATE_1) {
            //结束日期 小于当前日期 提示 已生效额度不够
            if ($this->paramModel['leave_end_time'] < date('Y-m-d')) {
                throw new ValidationException($this->getTranslation()->_('leave_limit_waiting_1'));
            } else//提示 结束日 额度不够
            {
                throw new ValidationException($this->getTranslation()->_('leave_limit_waiting_2'));
            }
        }

        throw new ValidationException($this->getTranslation()->_('leave_limit'));
    }


    //整理数据 数据结构 audit, split, img
    protected function dataFormat()
    {
        //先保存 主表 拿audit id
        $this->saveAudit();

        //拆分表
        $this->formatSplitData();
    }

    //相关表 新增 或者 更新 操作
    public function dataSave()
    {
        //保存 申请 上传图片
        $this->saveImgData();
        //保存 拆分表记录
        $this->saveSplitData();
        //额度更新 当前周期 和上个周期
        $this->saveRemainData();
        //额度更新 下个周期
        $this->saveNextRemainData();
    }


    //申请权限 https://flashexpress.feishu.cn/docx/doxcnMBEimw38UPZrCcdhZzfk20
    protected function applyPermission()
    {
        //新增权限判断
        $flag = $this->leaveServer->leavePermission($this->staffInfo);
        if(!$flag){
            return false;
        }
        //  年假规则适用于：“正式员工”、“月薪制特殊合同工” 其他 类型都不能申请
        if ($this->staffInfo['formal'] != 1 //正式
            || !in_array($this->staffInfo['hire_type'],
                [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2])) {
            return false;
        }
        return true;
    }



    //--------------------定制 或者 重写 方法

    /**
     * 格式化 拆分表 标记对应周期 针对有 失效期的 用去年或者上一周期额度的 假期类型 重新打标记
     */
    protected function formatSplitData()
    {
        //需要标记 用到去年剩余额度的
        $is_last_flag = isset($this->limitDays['last_sub']) && $this->limitDays['last_sub'] > 0 && $this->paramModel['leave_start_time'] < $this->cycleInfo['invalid_day'];
        //需要标记到 下个周期额度的 针对 待离职员工
        $is_waiting_flag = $this->staffInfo['state'] == HrStaffInfoModel::STATE_1 && $this->staffInfo['wait_leave_state'] == HrStaffInfoModel::STATE_1 && $this->waitingAddDays > 0;

        $left_days      = $this->limitDays['last_sub'] ?? 0;
        $have_days      = $this->limitDays['sub'];
        $need_last_year = 0;
        $need_next_year = 0;
        $hole           = self::HOLE - $this->hasApplyOutDays;
        // 拼接 拆分表 归属年 year_at 字段
        $add_row = [];//出现不够减情况 额外新增一条  merge到 insert
        foreach ($this->leaveSplitInfo as $k => $in) {
            $duration                             = ($in['type'] == StaffAuditLeaveSplitModel::SPLIT_TYPE_0) ? 1 : 0.5;//这天用额 1天 或者 半天
            $this->leaveSplitInfo[$k]['year_at']  = $this->cycleInfo['cycle'];//初始化周期 都为当前周期
            $this->leaveSplitInfo[$k]['audit_id'] = $this->auditId;

            //有上周期剩余 并且 在有效期 可以使用 标记 用去年额度的记录
            if ($is_last_flag && in_array($in['date_at'], $this->effectiveDates)) {
                if ($left_days > 0 && ($left_days - $duration >= 0)) {//还够减
                    $this->leaveSplitInfo[$k]['year_at'] = $this->cycleInfo['cycle'] - 1;
                    $left_days                           = $left_days - $duration;
                    $need_last_year                      += $duration;
                    continue;
                } else {
                    if ($left_days > 0 && ($left_days - $duration < 0)) {//不够减了（剩余0。5 本次记录 需要1天 只能是这种情况 把本条记录更改为半天 额外新增一条半天记录）
                        $this->leaveSplitInfo[$k]['type']    = StaffAuditLeaveSplitModel::SPLIT_TYPE_1;
                        $this->leaveSplitInfo[$k]['year_at'] = $this->cycleInfo['cycle'] - 1;

                        //拼接剩下半天 标记归属年 为今年 merge
                        $item           = $this->splitHalfItem($in, $this->cycleInfo['cycle']);
                        $add_row[]      = $item;
                        $left_days      = 0;//减没了
                        $need_last_year += 0.5;
                        continue;
                    }
                }
            }


            //上周期 没额度了 标记当前周期 和 下周期 如果 是在有效期外的 日期 标记当前周期
            if (in_array($in['date_at'], $this->invalidDates)) {
                if ($is_waiting_flag && in_array($in['date_at'],
                        $this->countOutDates) && $hole <= 0) {//当前周期的 三坑已用完 要标记到下个周期
                    //直接标记 下一年
                    $this->leaveSplitInfo[$k]['year_at'] = $this->cycleInfo['cycle'] + 1;
                    $need_next_year                      += $duration;
                    continue;
                }
                //三坑 剩0。5 拆一半一半
                if ($is_waiting_flag && in_array($in['date_at'],
                        $this->countOutDates) && $hole > 0 && ($hole - $duration) < 0) {
                    $hole                                = 0;
                    $this->leaveSplitInfo[$k]['type']    = StaffAuditLeaveSplitModel::SPLIT_TYPE_1;
                    $this->leaveSplitInfo[$k]['year_at'] = $this->cycleInfo['cycle'];
                    $item                                = $this->splitHalfItem($in, $this->cycleInfo['cycle'] + 1);
                    $add_row[]                           = $item;
                    $need_next_year                      += 0.5;
                    continue;
                }

                //countOutDates 失效期外 三坑
                if (in_array($in['date_at'], $this->countOutDates)) {
                    $hole -= $duration;
                }
            }
        }

        if (!empty($add_row)) {
            $this->leaveSplitInfo = array_merge($this->leaveSplitInfo, $add_row);
        }

        $this->lastApplyDays = $need_last_year;
        $this->nextApplyDays = $need_next_year;
        //总数 减去 上一年下一年 剩下的就是当前额度占用天数
        $this->currentApplyDays = $this->thisNum + $this->nextNum - $need_last_year - $need_next_year;
        if ($this->currentApplyDays < 0) {
            $this->currentApplyDays = 0;
        }
    }

    //额度 占用 一半一半的情况
    protected function splitHalfItem($in, $cycle)
    {
        $item['year_at']       = $cycle;
        $item['staff_info_id'] = $this->staffInfo['staff_info_id'];
        $item['date_at']       = $in['date_at'];
        $item['type']          = StaffAuditLeaveSplitModel::SPLIT_TYPE_2;
        $item['audit_id']      = $this->auditId;
        return $item;
    }


    //根据失效日期 把时间区间拆分成多个 不能把打标记 和 计算天数 放一个 以后改的时候太麻烦 要兼顾的地方太多
    public function initSides()
    {
        $invalidDate = $this->cycleInfo['invalid_day'];//实效日期分割
        $countDate   = $this->cycleInfo['count_day'];//结算日 分割
        $hole        = self::HOLE - $this->hasApplyOutDays;//目前剩余的坑

        foreach ($this->leaveSplitInfo as $v) {
            $add_step = ($v['type'] == StaffAuditLeaveSplitModel::SPLIT_TYPE_0) ? 1 : 0.5;
            if ($v['date_at'] < $invalidDate) {//可以用上个周期 和当前周期
                $this->effectiveDates[] = $v['date_at'];
                $this->effectiveDays    += $add_step;
            }
            if ($v['date_at'] >= $invalidDate) {//可以用当前周期 和下周
                $this->invalidDates[] = $v['date_at'];
                $this->invalidDays    += $add_step;
            }

            //下面 是计算 超出结余日 三坑的日期和天数
            if ($v['date_at'] >= $countDate && $hole <= 0) {//当前周期的 三坑已用完 要标记到下个周期
                $this->countOutDates[] = $v['date_at'];
                $this->countOutDays    += $add_step;
                $this->waitingUsedDays += $add_step;
                continue;
            }
            //三坑 剩0。5 拆一半一半
            if ($v['date_at'] >= $countDate && $hole > 0 && ($hole - $add_step) < 0) {
                $this->countOutDates[] = $v['date_at'];
                $this->countOutDays    += 0.5;
                $hole                  = 0;
                $this->waitingUsedDays += 0.5;
                continue;
            }

            //标记 结余外的日期 能用到预发放的日期
            if ($v['date_at'] >= $countDate && $hole > 0 && ($hole - $add_step) >= 0) {
                $this->countOutDates[] = $v['date_at'];
                $this->countOutDays    += $add_step;
                $hole                  -= $add_step;
            }
        }
    }


    //获取 额度 当年 总额度 剩余额度  去年总额度 剩余额度
    public function getLimitDays()
    {
        $return['limit'] = $return['sub'] = 0.0;
        $today           = date('Y-m-d');

        //没周期
        if (empty($this->cycleInfo)) {
            $this->getDI()->get('logger')->write_log(['getLimitDays-0' => $return], 'info');

            $this->limitDays = $return;
            return;
        }

        //没权限
        if (!$this->applyPermission()) {
            $this->isPermission = false;
            $this->getDI()->get('logger')->write_log(['getLimitDays-1' => $return], 'info');
            $this->limitDays = $return;
            return;
        }

        //工具 放开离职员工限制
        if(empty($this->paramModel['is_bi']) && empty($this->paramModel['is_svc']) && $this->staffInfo['state'] != 1){
            $this->getDI()->get('logger')->write_log("{$this->staffInfo['staff_info_id']} -4  没有额度", 'info');
            return ;
        }


        //没转正 并且 不是待离职 返回0 月薪制合同工没有转正限制，一入职就可申请年假
        if (!$this->hireCheck() && $this->staffInfo['wait_leave_state'] != HrStaffInfoModel::STATE_1 && $this->staffInfo['hire_type'] != HrStaffInfoModel::HIRE_TYPE_2) {
            $this->limitDays = $return;
            $this->getDI()->get('logger')->write_log([
                'getLimitDays-2' => [
                    'hireCheck'        => $this->hireCheck(),
                    'wait_leave_state' => $this->staffInfo['wait_leave_state'],
                    'hire_type'        => $this->staffInfo['hire_type'],
                ],
            ], 'info');
            return;
        }

        //有额度
        $cycles[] = $this->cycleInfo['cycle'];

        //是否获取上周期
        if ($this->cycleInfo['cycle'] > 1 && $today < $this->cycleInfo['invalid_day']) {
            $cycles[] = $this->cycleInfo['cycle'] - 1;
        }


        $data = StaffLeaveRemainDaysModel::find([
            'columns'    => 'staff_info_id,leave_type,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycles:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_1,
                'cycles'     => $cycles,
            ],
        ])->toArray();


        foreach ($data as $remain_info) {
            //当前周期
            if ($remain_info['year'] == $this->cycleInfo['cycle']) {
                $return['sub']   = half_num($remain_info['days']);
                $return['limit'] = half_num($remain_info['freeze_days']);
            }

            //上个周期剩余
            if ($remain_info['year'] == ($this->cycleInfo['cycle'] - 1)) {
                //获取上个周期剩余总数
                $extend_info          = StaffLeaveExtendModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year_at = :cycle: ',
                    'bind'       => [
                        'staff_id'   => $this->staffInfo['staff_info_id'],
                        'leave_type' => enums::LEAVE_TYPE_1,
                        'cycle'      => $remain_info['year'],
                    ],
                ]);
                $return['last_limit'] = 0;
                if (!empty($extend_info)) {
                    $return['last_limit'] = half_num($extend_info->left_all_days);
                }

                $return['last_sub'] = half_num($remain_info['days']);
            }
        }
        //新需求 待离职员工 预发放
        if ($this->staffInfo['state'] == HrStaffInfoModel::STATE_1 && $this->staffInfo['wait_leave_state'] == HrStaffInfoModel::STATE_1) {
            $return['waitingLeaveDays'] = $this->limitListForWait();
        }
        $this->getDI()->get('logger')->write_log(['getLimitDays' => $return], 'info');
        $this->limitDays = $return;
    }


    //针对待离职员工 新规则 预发放额度列表
    protected function limitListForWait()
    {
        if ($this->staffInfo['state'] != HrStaffInfoModel::STATE_1 || $this->staffInfo['wait_leave_state'] != HrStaffInfoModel::STATE_1) {
            return [];
        }

        //明天 不包含当天
        $start_date = date('Y-m-d', strtotime('+1 day'));//20
        $this->waitingLastDate = $this->getWaitingLastDate($this->staffInfo);

        //离职日期  如果有变动 每天任务跑发放
        $end_date = $this->waitingLastDate;//19

        if(date('Y-m-d') == $end_date){
            $start_date = $end_date;
        }
        //离职日 是当天或者当天以前 没有预发放额度了
        if ($end_date < $start_date) {
            return [];
        }


        //取>当天的日期  <= 离职日期 没使用的额度
        $waitingData = StaffWaitingLeaveSendModel::find([
            'columns'    => 'staff_info_id,date_at,days,is_used',
            'conditions' => 'staff_info_id = :staff_id: and date_at between :start: and :end: ',
            'bind'       => [
                'staff_id' => $this->staffInfo['staff_info_id'],
                'start'    => $start_date,
                'end'      => $end_date,
            ],
        ])->toArray();
        //离职那天
        $return['leave_date'] = $this->waitingLastDate;

        $return['limit_detail'] = [];
        //申请 结束日期之前 能用多少天
        if (!empty($waitingData)) {
            foreach ($waitingData as $v) {
                //新增逻辑 如果是获取详情调用 预发放的额度展示 翻译变量
                if($this->isDetail){
//                    $detail['in_all_days'] = 0.5;//分子 固定是0。5 不用加变量
                    $detail['left_days'] = ($v['is_used'] == 1) ? 0 : 0.5;//用了就是0
                    $detail['effect_date'] = date('Y.m.d',strtotime($v['date_at']));//生效日期
                    $this->waitingDetail[] = $detail;
                }
                $this->waitingAllDays += $v['days'];
                if ($v['is_used'] == 1) {
                    continue;
                }

                //如果 申请结束日期 在离职信息记录 之前 不需要加 之后的 预发放额度
                if (!empty($this->paramModel['leave_end_time']) && $v['date_at'] > $this->paramModel['leave_end_time']) {
                    break;
                }

                $row['staff_info_id'] = $v['staff_info_id'];
                $row['date_at']       = $v['date_at'];
                $row['days']          = half_num($v['days']);
                //标记 是否是下周年 展示用
                $row['is_next']           = ($v['date_at'] >= $this->cycleInfo['count_day']) ? 1 : 0;
                $return['limit_detail'][] = $row;

                $this->waitingAddDays   += $v['days'];
                $this->waitingAddDate[] = $v['date_at'];
                if ($row['is_next']) {
                    $this->waitingAddNextDays += $v['days'];
                }
            }
        }

        //预发放剩余额度
        $return['limit_days'] = empty($return['limit_detail']) ? 0 : array_sum(array_column($return['limit_detail'],
            'days'));
        return $return;
    }


    //获取每个员工的周期 和 失效日期 和 结算日期
    public function get_cycle($staffInfo = [], $date_at = '')
    {
        //任务 直接调用
        if (!empty($staffInfo)) {
            $this->staffInfo = $staffInfo;
        }

        if (empty($this->staffInfo['hire_date'])) {
            return '';
        }

        $return = [];

        //入职日期加13月 的1号为结算日 以后每个结算日 加一年 改了 按天算
        $hire_date   = date('Y-m-d', strtotime($this->staffInfo['hire_date']));
        $first_count = date('Y-m-d', strtotime("{$hire_date} +1 year"));
        $today       = date('Y-m-d');
        if (!empty($date_at)) {
            $today = $date_at;
        }

        $return['cycle'] = 1;//默认周期 为1 每满一个周期 +1
        if ($today < $first_count) {//还没 满第一个周期
            $return['count_day']   = $first_count;
            $return['invalid_day'] = date('Y-m-d', strtotime("{$hire_date} +6 month"));
            return $return;
        }
        $count_day = $first_count;
        while ($today >= $count_day) {
            $count_day = date('Y-m-d', strtotime("{$count_day} +1 year"));
            $return['cycle']++;
        }

        //结算日 加 6个月
        $invalid_day = date('Y-m-d', strtotime("{$count_day} -6 month"));

        $return['count_day']   = $count_day;//结算日
        $return['invalid_day'] = $invalid_day;//结余 失效日

        return $return;
    }


    //申请操作 额度表 model  分 三个周期 model 去年 今年 明年
    protected function saveRemainData()
    {
        $conditionCycle = [];

        if (!empty($this->currentApplyDays)) {
            $conditionCycle[] = $this->cycleInfo['cycle'];
        }

        if (!empty($this->lastApplyDays)) {
            $conditionCycle[] = $this->cycleInfo['cycle'] - 1;
        }

        if (empty($conditionCycle)) {
            return;
        }

        $remainData = StaffLeaveRemainDaysModel::find([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycles:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_1,
                'cycles'     => $conditionCycle,
            ],
        ]);

        if (empty($remainData->toArray())) {
            return;
        }

        //操作 对应额度字段
        foreach ($remainData as $remain) {
            if ($remain->year == $this->cycleInfo['cycle']) {
                //当前周年 可能存在负数 说明用到了 预发放额度
                if (($remain->days - $this->currentApplyDays) < 0 && !empty($this->waitingAddDays)) {
                    $waitDays     = half_num($remain->days) - $this->currentApplyDays;// 3.5 - 4.5 = -1 这个是 需要用到 预发放的额度
                    $remain->days = $remain->days - $this->currentApplyDays + abs($waitDays);//3.8 - 4.5 + (-1) = 0.3 更新当前周期剩余额度的值

                    $this->updateWaiting($waitDays, $this->cycleInfo['cycle']);
                } else {
                    $remain->days = $remain->days - $this->currentApplyDays;
                }
                $remain->leave_days = $remain->leave_days + $this->currentApplyDays;
                $remain->update();
            }

            if ($remain->year == $this->cycleInfo['cycle'] - 1) {
                $remain->days       = $remain->days - $this->lastApplyDays;
                $remain->leave_days = $remain->leave_days + $this->lastApplyDays;
                $remain->update();
            }
        }
    }


    //操作 预发放的 下个周期的记录 没有则新增一条 remain
    protected function saveNextRemainData()
    {
        //新需求 可能用到下个周年的额度 针对待离职员工 预发放 离职前的年假
        if (empty($this->nextApplyDays)) {
            return;
        }

        $nextModel = StaffLeaveRemainDaysModel::findFirst(
            [
                'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :cycle:',
                'bind'       => [
                    'staff_id'   => $this->staffInfo['staff_info_id'],
                    'leave_type' => enums::LEAVE_TYPE_1,
                    'cycle'      => $this->cycleInfo['cycle'] + 1,
                ],
            ]
        );

        //标记 is_used 以申请日期 最近的 优先
        $this->updateWaiting($this->nextApplyDays, $this->cycleInfo['cycle'] + 1, 'desc');

        //需要 新增数据 标记使用天数
        if (empty($nextModel)) {
            $insert['staff_info_id'] = $this->staffInfo['staff_info_id'];
            $insert['leave_type']    = enums::LEAVE_TYPE_1;
            $insert['year']          = $this->cycleInfo['cycle'] + 1;
            $insert['days']          = 0;
            $insert['leave_days']    = $this->nextApplyDays;
            $model                   = new StaffLeaveRemainDaysModel();
            $model->create($insert);
            return;
        }
        //有记录 update
        $nextModel->leave_days = $nextModel->leave_days + $this->nextApplyDays;
        $nextModel->update();
    }

    /**
     * @param $days 需要扣调的 天数
     * @param $cycle 标记对应周期
     * @param $order 扣除 预发放 正序还是倒序
     * @throws ValidationException
     */
    protected function updateWaiting($days, $cycle, $order = 'asc')
    {
        $step = abs($days);//负数情况 是当前周期 正数是 下个周期
        if (empty($this->waitingAddDate)) {
            $this->logger->write_log("待离职员工 使用预发放额度异常 {$this->staffInfo['staff_info_id']}");
            throw new ValidationException('server error');
        }

        if ($step == 0) {
            return;
        }

        if ($order == 'desc') {
            rsort($this->waitingAddDate);
        }

        $dates = [];
        foreach ($this->waitingAddDate as $k => $date) {
            if ($step == 0) {
                break;
            }

            $step    -= 0.5;
            $dates[] = $date;
            unset($this->waitingAddDate[$k]);
        }

        sort($this->waitingAddDate);
        $this->waitingAddDate = array_values($this->waitingAddDate);
        $subData              = StaffWaitingLeaveSendModel::find([
            'conditions' => 'staff_info_id = :staff_id: and date_at in ({dates:array})',
            'bind'       => ['staff_id' => $this->staffInfo['staff_info_id'], 'dates' => $dates],
        ]);
        $subData->update(['is_used' => 1, 'audit_id' => $this->auditId, 'year_at' => $cycle]);
        return;
    }


    //年假撤销操作 返还额度 工具撤销 或者 by 最终 非审批通过用
    public function returnRemainDays($auditId, $staffInfo,$extend = [])
    {
        $this->staffInfo = $staffInfo;

        $splitInfo = $this->getUsedDays($auditId);

        //获取 对应 split id 的 已使用额度
        $waitingData = StaffWaitingLeaveSendModel::find([
            'conditions' => 'audit_id = :audit_id: and is_used = 1 and date_at > :today:',
            'bind'       => ['audit_id' => $auditId, 'today' => date('Y-m-d')],
        ]);

        //没有拆分表额度信息
        if (empty($splitInfo)) {
            $this->logger->write_log("拆分表信息异常 {$this->staffInfo['staff_info_id']} {$auditId}", 'info');
            return true;
        }


        $remainData = StaffLeaveRemainDaysModel::find([
            'column'     => 'staff_info_id,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycle:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_1,
                'cycle'      => array_keys($splitInfo),
            ],
        ]);


        if (empty($remainData->toArray())) {
            $this->logger->write_log("额度表信息异常 {$this->staffInfo['staff_info_id']} {$auditId} ".json_encode($splitInfo),
                'info');
            return true;
        }

        //获取当前周期 判定 上一周期 不能超3坑 下一周期预发放的 不操作 days
        if (empty($this->cycleInfo)) {
            $this->cycleInfo = $this->get_cycle();
        }

        foreach ($remainData as $remain) {
            $needBackDays = $splitInfo[$remain->year];//需要返还的 额度
            //上周期
            if ($remain->year == ($this->cycleInfo['cycle'] - 1)) {
                //获取 当前记录 之外的 占坑个数
                $last_count  = date('Y-m-d', strtotime("{$this->cycleInfo['count_day']} -1 year"));
                $has_applied = $this->leaveDaysByDate($this->staffInfo['staff_info_id'], $remain->year, $last_count,
                    $auditId);

                $needBackDays = $splitInfo[$remain->year] ?? 0;//这次要返还的天数
                $needBackDays = $needBackDays > self::HOLE ? self::HOLE : $needBackDays;
                //最多返回上个周期不能超过3天
                $holeUsed     = (self::HOLE - half_num($remain->days)) - $has_applied;//已经使用的坑
                $needBackDays = $needBackDays > $holeUsed ? $holeUsed : $needBackDays;
                $remain->days += $needBackDays;
            }

            //当前周期 特殊情况 用到预发放额度 返回的天数 超出 freeze days 其余的 返到waiting表
            if ($remain->year == $this->cycleInfo['cycle']) {
                $days = $remain->days + $needBackDays;
                //存在超额使用预发放额度情况 并且撤销的这条记录 有占用预发放
                if (!empty($waitingData->toArray())) {
                    $overDays = 0;
                    foreach ($waitingData as $waitingDatum) {
                        if ($waitingDatum->year_at != $this->cycleInfo['cycle']) {
                            continue;
                        }

                        $overDays += 0.5;
                    }
                    $days = $remain->days + $needBackDays - $overDays;
                }

                $remain->days = $days;
            }

            //用到的 预发放 或者 下个周期 预发放 都直接返还
            if (!empty($waitingData)) {
                $waitingData->update(['is_used' => 0]);
            }

            //使用额度 减少
            $remain->leave_days -= $needBackDays;

            $remain->update();
        }
    }


    //by 或者 hcm 显示额度 整理
    protected function formatLimitDays()
    {
        $day_limit = $this->limitDays['limit'];
        $day_sub   = $this->limitDays['sub'];
        $return    = [];
        //待离职 预发放额度
        if (!empty($this->limitDays['waitingLeaveDays'])) {
            //已生效 第一条记录 塞进去
            $row['staff_info_id'] = $this->staffInfo['staff_info_id'];
            $row['date_at']       = '';
            $row['days']          = half_num($this->limitDays['sub']);
            //如果有去年额度
            if (!empty($this->limitDays['last_sub'])) {
                $row['days'] += $this->limitDays['last_sub'];
            }
            $row['is_next'] = 0;
            array_unshift($this->limitDays['waitingLeaveDays']['limit_detail'], $row);

            $day_limit += $this->waitingAllDays;
            $day_sub   += $this->limitDays['waitingLeaveDays']['limit_days'];


            //把预发放 加到 sub 里面
            $this->limitDays['waitingLeaveDays']['limit_days'] += $this->limitDays['sub'];
            if (!empty($this->limitDays['last_sub'])) {
                $this->limitDays['waitingLeaveDays']['limit_days'] += $this->limitDays['last_sub'];
            }
            $return['limit_list'] = $this->limitDays['waitingLeaveDays'];
        }

        //如果当前时间 在有效期内
        if (isset($this->limitDays['last_limit'])) {
            $return['day_limit'] = "{$day_limit} + {$this->limitDays['last_limit']}";//分母 今年额度 加上去年 固化后的剩余总额度
            $return['day_sub']   = "{$day_sub} + {$this->limitDays['last_sub']}";//分子 今年剩余额度 + 去年剩余额度

            //有效期 提醒  如果 额度为0 就不提醒了
            if ($this->limitDays['last_sub'] > 0) {
                $return['show_text'] = $this->getTranslation()->_('annual_invalid_text',
                        ['invalid_date' => date('Y.m.d', strtotime($this->cycleInfo['invalid_day']))])
                    . $this->getTranslation()->_('annual_end_text');;
            }
        } else {
            $return['day_limit'] = "{$day_limit}";//总额度
            $return['day_sub']   = "{$day_sub}";//剩余额度 有可能是负数
        }
        $this->getDI()->get('logger')->write_log(['formatLimitDays' => $return], 'info');
        return $return;
    }


    //获取当前 该员工 应有额度基数  按月发 改成 按年了
    public function staff_year_should_days($staff_info = [], $date = '')
    {
        if (!empty($staff_info)) {
            $this->staffInfo = $staff_info;
        }
        if (empty($this->staffInfo) || empty($this->staffInfo['hire_date'])) {
            return 0;
        }

        //如果没有传月份 默认当月
        if (empty($date)) {
            $date = date('Y-m-d');
        }

        //分三个等级  1级最低（12 和以下） 3级最高（15和以上） add为 入职满n年以后 的递增步长
        $grade_array = [
            '1' => ['days' => 8, 'add' => 4],
            '2' => ['days' => 12, 'add' => 2],
            '3' => ['days' => 14, 'add' => 2],
        ];
        if ($this->staffInfo['job_title_grade_v2'] <= 12) {
            $grade_days = $grade_array[1];
        } else {
            if ($this->staffInfo['job_title_grade_v2'] > 12 && $this->staffInfo['job_title_grade_v2'] <= 14) {
                $grade_days = $grade_array[2];
            } else {
                $grade_days = $grade_array[3];
            }
        }

        $hire_3 = strtotime("{$this->staffInfo['hire_date']} +2 year");
        $hire_5 = strtotime("{$this->staffInfo['hire_date']} +5 year");

        //当月不发放 取当前日期的上一个月
        $current_tmp = strtotime($date);

        //没满3年
        if ($current_tmp < $hire_3) {
            return $grade_days['days'];
        } //3年-5年以下
        else {
            if ($current_tmp >= $hire_3 && $current_tmp < $hire_5) {
                return $grade_days['days'] + $grade_days['add'];
            } //5年和以上
            else {
                return $grade_days['days'] + 2 * $grade_days['add'];
            }
        }
    }

    /**
     * 获取员工 最后工作日 只取 审核通过状态的 才有可能 是待离职的
     * select resign_id ,resignation_work_day from staff_resign where submitter_id = 118689 and status = 2 order by resign_id desc limit 1;
     * @param $staff_info
     * @return string
     */
    public function getWaitingLastDate($staff_info){
        //改逻辑了 就取离职日前一天
        return date('Y-m-d',strtotime("{$staff_info['leave_date']} -1 day"));

    }



    //hcm 工具 获取年假详情
    public function detailList($param)
    {
        //初始化数据
        $this->initSearch($param);

        //有可能是 hcm 调用
        $this->isSvc = empty($param['is_svc']) ? 0 : 1;
        $this->isDetail = true;

        //周期信息
        $this->cycleInfo = $this->get_cycle();
        //额度信息
        $this->getLimitDays();

        //整理成列表 详情信息
        return $this->formatDetail();
    }
    //获取详情信息 整理结构
    public function formatDetail(){
        //没有权限
        if(!$this->isPermission){
            return ['detail_list' => [], 'is_permission' => $this->isPermission];
        }
        $detailData = [];

        //转正的 或者是 代理制的 或者是 月薪制的 都展示额度 并且能申请
        $hireFlag = $this->hireCheck() || $this->staffInfo['wait_leave_state'] != HrStaffInfoModel::STATE_1 || $this->staffInfo['hire_type'] != HrStaffInfoModel::HIRE_TYPE_2;

        //当天日期 判断 额度是否失效
        $todayPoint = date('Y.m.d');

        $remainHistory = StaffLeaveRemainDaysModel::find([
            'column'     => 'staff_info_id,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year <= :cycle:',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_1,
                'cycle' => $this->cycleInfo['cycle'],
            ],
            'order'      => 'year desc',
        ])->toArray();

        if (empty($remainHistory)) {
            return ['detail_list' => [], 'is_permission' => $this->isPermission];
        }

        //处理 历史额度记录
        foreach ($remainHistory as $h) {
            //正常过渡期
            $betweenStr          = $this->getBetweenStr($h['year']);
            $invalidDate         = date('Y.m.d',
                strtotime("{$this->staffInfo['hire_date']} +{$h['year']} year +6 month -1 day"));//失效日期 6个月 马来特殊
            if(!$this->isSvc && $todayPoint > $invalidDate){
                continue;
            }

            $used_day = half_num($h['leave_days']);
            $leftDays = half_num($h['days']);
            $row['date_between'] = $betweenStr;
            $row['in_all']       = $hireFlag ? half_num($h['freeze_days']) : '0.0';
            $row['used_day']     = $used_day;
            $row['left_day']     = $hireFlag ? half_num($leftDays) : '0.0';
            $row['actual_left']  = $hireFlag ? $h['days'] : '0.00000';
            //如果是当前周期 需要特殊展示 https://flashexpress.feishu.cn/docx/QKZJdVCKloJ7EzxJTILcaJZGndd
            if($h['year'] == $this->cycleInfo['cycle']){
                $invalidDate = $this->getInvalidInfo($h);
            }

            $row['invalid_date'] = $invalidDate >= $todayPoint ? $invalidDate : $this->getTranslation()->_('hasinvalid');
            $row['invalid_flag'] = $hireFlag && $invalidDate >= $todayPoint ? false : true;
            //临时处理 待离职员工 不能修改额度 会导致推算预发放额度错误
            if($this->staffInfo['wait_leave_state'] == HrStaffInfoModel::WAITING_LEAVE){
                $row['invalid_flag'] = true;
            }
            $row['cycle']        = $h['year'];
            $detailData[]        = $row;
        }
        //详情列表
        $return['detail_list'] = $detailData;

        //预发放
        if(!empty($this->waitingDetail)){
            foreach ($this->waitingDetail as $item){
                $return['waiting_list'][] = $this->getTranslation()->_('waiting_detail_text', $item);
            }
        }
        $return['is_permission'] = $this->isPermission;

        return $return;
    }


    //获取对应周期的 开始结束时间 并且和 交替日 比较
    protected function getBetweenStr($cycle)
    {
        $lastCycle  = $cycle - 1;
        $cycleStart = date('Y.m.d', strtotime("{$this->staffInfo['hire_date']} +{$lastCycle} year"));//开始
        $cycleEnd   = date('Y.m.d', strtotime("{$this->staffInfo['hire_date']} +{$cycle} year -1 day"));//周期结束

        //由于 存在 22年过渡期 判断区间时间 和 22年结束节点时间 取最大值
        $betweenStr = "{$cycleStart}-{$cycleEnd}";
        return $betweenStr;
    }

    //整理马来当前周期 3个坑的 失效时间 https://flashexpress.feishu.cn/docx/QKZJdVCKloJ7EzxJTILcaJZGndd
    public function getInvalidInfo($h)
    {
        //超过3天 两行字
        $days       = half_num($h['days']);
        $invalidStr = '';
        $hasApplyOutDays = $this->leaveDaysByDate($this->staffInfo['staff_info_id'], $this->cycleInfo['cycle'], $this->cycleInfo['count_day']);
        if ($days > self::HOLE) {
            $left1 = $days - self::HOLE + $hasApplyOutDays;
            $left1 = half_num($left1);
            $left2 = self::HOLE - $hasApplyOutDays;
            $left2 = half_num($left2);
            //3坑外天数  当前周期结束日 count_day -1 day
            $invalidDate1 = date('Y.m.d', strtotime("{$this->cycleInfo['count_day']} -1 day"));
            $invalidStr   .= "{$invalidDate1} ({$left1})</br>";
            //3坑 失效日
            $invalidDate2 = date('Y.m.d', strtotime("{$this->staffInfo['hire_date']} +{$h['year']} year +6 month -1 day"));
            $invalidStr   .= "{$invalidDate2} ({$left2})";
            return $invalidStr;
        }

        //没超过3天
        if(empty($hasApplyOutDays)){
            $invalidDate2 = date('Y.m.d', strtotime("{$this->staffInfo['hire_date']} +{$h['year']} year +6 month -1 day"));
            $invalidStr   .= "{$invalidDate2} ({$days})";
            return $invalidStr;
        }

        //有占坑 两行字
        $left1 = $days - self::HOLE + $hasApplyOutDays;

        //3坑外天数  当前周期结束日 count_day -1 day
        $invalidDate1 = date('Y.m.d', strtotime("{$this->cycleInfo['count_day']} -1 day"));
        //有额度 才展示第一行
        $left1 = $left1 < 0 ? 0.0 : $left1;
        $left1 = half_num($left1);
        $invalidStr   .= "{$invalidDate1} ({$left1})</br>";

        //第二行
        $invalidDate2 = date('Y.m.d', strtotime("{$this->staffInfo['hire_date']} +{$h['year']} year +6 month -1 day"));
        $left2 = self::HOLE - $hasApplyOutDays;
        $left2 = half_num($left2);
        if($left2 >= $days){
            $invalidStr   .= "{$invalidDate2} ({$days})";
        }else{
            $invalidStr   .= "{$invalidDate2} ({$left2})";
        }
        return $invalidStr;
    }


}



