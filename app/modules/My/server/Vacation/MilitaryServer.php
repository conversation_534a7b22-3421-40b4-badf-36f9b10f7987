<?php
/**
 * Created by <PERSON>pStorm.
 * User: nick
 * Date: 5/5/23
 * Time: 2:16 PM
 */

namespace FlashExpress\bi\App\Modules\My\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\library\DateTime;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\Vacation\TrainingServer as GlobalServer;


class MilitaryServer extends GlobalServer implements LeaveInterface
{

    private static $instance  = null;
    public         $hasApply  = false;//是否申请过
    public         $leaveType = enums::LEAVE_TYPE_42;


    //任务 每年初始化调用
    public static function getInstance($lang, $timezone)
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self($lang, $timezone);
        }
        return self::$instance;
    }


    public function initData($param)
    {
        //前端 没有 上下午 选项
        $param['leave_start_type'] = StaffAuditModel::LEAVE_AM;
        $param['leave_end_type']   = StaffAuditModel::LEAVE_PM;
        parent::initData($param);
    }


    /**
     * //申请 保存入口
     * @throws ValidationException
     */
    public function handleCreate($param)
    {
        //初始化数据
        $this->initData($param);

        //逻辑验证
        $this->businessCheck();

        //format 数据
        $this->dataFormat();

        //保存
        $this->dataSave();

        return $this->auditId;
    }


    //额度查询 入口
    public function handleSearch($param)
    {
        //初始化数据
        $this->initSearch($param);

        $this->getLimitDays();

        //整理成 day_sub  day_limit
        return $this->formatLimitDays();
    }

    //查询额度 初始化
    public function initSearch($param)
    {
        parent::initSearch($param);
        //是否有额度
        $this->setHasApply();
    }


    /**
     * @throws ValidationException
     */
    public function businessCheck()
    {
        //公共验证逻辑
        $this->publicValidate();
        //没权限 返回异常
        if (!$this->applyPermission()) {
            throw new ValidationException($this->getTranslation()->_('2107'));
        }

        //验证时间区间 分流各个国家
        $this->timeCheck();
        //是否申请过
        $this->setHasApply();
        if ($this->hasApply === true) {
            throw new ValidationException($this->getTranslation()->_('exist_leave_notice'));
        }

        //验证日期区间 对不对
        $settingData = $this->getSettingDate();
        if (empty($settingData)) {
            throw new ValidationException('setting date is empty');
        }
        $flag = false;
        foreach ($settingData as $item) {
            $startDate = $item['start_date'];
            $endDate   = $item['end_date'];
            if ($startDate == $this->paramModel['leave_start_time'] && $endDate == $this->paramModel['leave_end_time']) {
                $flag = true;
                break;
            }
        }
        if ($flag === false) {
            throw new ValidationException('wrong leave date selected!');
        }
        return true;
    }

    //验证申请日期
    public function timeCheck()
    {
        //请假日期必须大于等于当前日期
        if (empty($this->paramModel['is_bi'])) {
            $this->timeValidate(0, 100, '1018');
        }
    }


    //终身申请一次 要判断 身份证号 关联的 所有工号
    public function setHasApply()
    {
        $staffIds = [$this->staffInfo['staff_info_id']];
        if (!empty($this->staffInfo['identity'])) {
            //身份证 关联的工号
            $allStaff = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id',
                'conditions' => 'identity = :identity:',
                'bind'       => ['identity' => $this->staffInfo['identity']],
            ])->toArray();

            $staffIds = empty($allStaff) ? $staffIds : array_column($allStaff, 'staff_info_id');
        }
        $exist = StaffLeaveRemainDaysModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'staff_info_id in ({ids:array}) and leave_type = :leave_type:',
            'bind'       => ['ids' => $staffIds, 'leave_type' => $this->leaveType],
        ])->toArray();

        if (!empty($exist)) {
            $this->hasApply = true;
            $this->logger->write_log(['military_exist' => $exist], 'info');
        }
        return true;
    }

    //整理数据 数据结构 audit, split, img
    protected function dataFormat()
    {
        //先保存 主表 拿audit id
        $this->saveAudit();

        //拆分表
        $this->formatSplitData();
    }


    //by 或者 hcm 显示额度 整理
    protected function formatLimitDays()
    {
        $dayLimit            = half_num($this->limitDays['limit']);
        $daySub              = half_num($this->limitDays['sub']);
        $return              = [];
        $return['day_limit'] = "{$dayLimit}";//总额度
        $return['day_sub']   = "{$daySub}";//剩余额度 有可能是负数
        if (!empty($this->limitDays['text'])) {
            $return['text'] = $this->limitDays['text'];
        }
        $this->getDI()->get('logger')->write_log(['formatLimitDays' => $return], 'info');
        return $return;
    }


    //整理 扣减额度 标记 对应周期
    protected function formatSplitData()
    {
        // 拼接 拆分表 归属年 year_at 字段
        foreach ($this->leaveSplitInfo as $k => $in) {
            $this->leaveSplitInfo[$k]['audit_id'] = $this->auditId;
        }
    }

    //男性不能申请
    public function applyPermission($staffInfo = [])
    {
        if (empty($staffInfo)) {
            $staffInfo = $this->staffInfo;
        }
        //申请权限  正式员工、月
        if ($staffInfo['formal'] == HrStaffInfoModel::FORMAL_1 && in_array($staffInfo['hire_type'], [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
            ])) {
            return true;
        }
        if (in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            return true;
        }
        return false;
    }


    //获取 额度 hcm 查询的 申请过就是 没有 一次性假期
    public function getLimitDays()
    {
        //获取额度
        $return['limit'] = $return['sub'] = 0.0;
        //没有权限
        if (!$this->applyPermission()) {
            return;
        }
        $return['text'] = 'has_not_apply';
        if ($this->hasApply === true) {
            $return['text'] = 'has_apply';
        }
        $this->limitDays = $return;
    }

    //保存
    public function dataSave()
    {
        //保存 申请 上传图片
        $this->saveImgData();
        //保存 拆分表记录
        $this->saveSplitData();
        //额度更新 当前周期 和上个周期
        $this->saveRemainData();
    }


    //申请操作 额度表 扣除带薪病假的
    protected function saveRemainData()
    {
        $remain['staff_info_id'] = $this->staffInfo['staff_info_id'];
        $remain['leave_type']    = $this->leaveType;
        $remain['year']          = date('Y-m-d');
        $remain['freeze_days']   = $this->thisNum + $this->nextNum;
        $remain['days']          = 0;
        $remain['leave_days']    = $this->thisNum + $this->nextNum;
        $model                   = new StaffLeaveRemainDaysModel();
        $model->create($remain);
    }


    //一次性假期 撤销返还 删除 remain 记录
    public function returnRemainDays($auditId, $staffInfo, $extend = [])
    {
        $this->staffInfo = $staffInfo;
        $remainData      = StaffLeaveRemainDaysModel::findFirst([
            'column'     => 'staff_info_id,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycle:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_42,
            ],
        ]);

        if (empty($remainData)) {
            $this->logger->write_log("额度表信息异常 {$this->staffInfo['staff_info_id']} {$auditId} ", 'info');
            return true;
        }

        $remainData->delete();
        $this->logger->write_log("额度返还成功 {$this->staffInfo['staff_info_id']} {$auditId} ", 'info');
    }


    public function getSettingDate()
    {
        $setting = (new SettingEnvServer())->getSetVal('PLKN_Date');
        if (empty($setting)) {
            return [];
        }
        $setting = trim($setting);
        $setting = str_replace('，', ',', $setting);
        $setting = explode(',', $setting);

        $data = [];
        foreach ($setting as $item) {
            [$start, $end] = explode('-', $item);
            $startDate         = str_replace('/', '-', $start);
            $endDate           = str_replace('/', '-', $end);
            $row['start_date'] = date('Y-m-d', strtotime($startDate));
            $row['end_date']   = date('Y-m-d', strtotime($endDate));
            $row['days']       = (strtotime($endDate) - strtotime($startDate)) / (24 * 3600) + 1;
            $data[]            = $row;
        }
        return $data;
    }

    public function checkFirstNode($auditInfo, $param)
    {
        //个人代理 不验证 必填
        if($auditInfo['source_type'] == StaffAuditModel::SOURCE_TYPE_UNPAID){
            return true;
        }

        //判断 是否有 同意原因  和 图片
        if (empty($param['agree_reason']) || empty($param['agree_url'])) {
            throw new ValidationException('agree param missing');
        }
        //原因 500 字以内
        if (mb_strlen($param['agree_reason']) > 500) {
            throw new ValidationException('agree reason too long');
        }
        return true;
    }


    //-------- 保存相关
    protected function saveAudit()
    {
        $serialNo  = $this->getRandomId();
        $reason    = addcslashes(stripslashes($this->paramModel['audit_reason']), "'");
        $status    = enums::$audit_status['panding'];
        $subStatus = StaffAuditModel::MILITARY_SUB_STATUS_HAS_NOT;
        if (!empty($this->paramModel['is_bi'])) {
            $status    = enums::$audit_status['approved'];
            $subStatus = StaffAuditModel::MILITARY_SUB_STATUS_APPROVED;//工具过来没有审批流
            $reason    .= "|system_tool_add";
        }
        $timeOut   = date('Y-m-d 00:00:00', strtotime('+3 day'));
        $insetData = [
            'staff_info_id'    => $this->staffInfo['staff_info_id'],
            'leave_type'       => $this->paramModel['leave_type'],
            'leave_start_time' => $this->assemblyData($this->paramModel['leave_start_time'], 1,
                $this->paramModel['leave_start_type']),
            'leave_start_type' => $this->paramModel['leave_start_type'],
            'leave_end_time'   => $this->assemblyData($this->paramModel['leave_end_time'], 2,
                $this->paramModel['leave_end_type']),
            'leave_end_type'   => $this->paramModel['leave_end_type'],
            'audit_reason'     => $reason,
            'status'           => $status,
            'audit_type'       => enums::$audit_type['LE'],
            'leave_day'        => $this->thisNum + $this->nextNum,
            'serial_no'        => (!empty($serialNo) ? 'LE' . $serialNo : null),
            'sub_status'       => $subStatus,
            'time_out'         => $timeOut,
        ];
        //申请来源 目前只有个人代理和正式
        $insetData['source_type'] = (in_array($this->staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) ? 1 : 0;
        $this->leave_day          = $this->thisNum + $this->nextNum;

        $auditModel = new StaffAuditModel();
        $auditModel->create($insetData);
        $this->auditId = $auditModel->audit_id;
        $this->timeOut = $timeOut;
    }


}