<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditCCModel;
use FlashExpress\bi\App\Models\backyard\hrHcRelateManagerModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Server\HcServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysDepartmentServer;

class HcServer extends GlobalBaseServer
{

    public function getDisplayStoreData($result)
    {
        //所属部门”为Network Management部门及其子部门，且“工作网点”为非总部时
        $department_ids = (new SysDepartmentServer())->getDepartmentIdsUseSettingEnv('dept_network_management_id');
        $job_titles = [enums::$job_title['van_courier'],enums::$job_title['bike_courier'],enums::$job_title['car_courier'],enums::$job_title['dc_officer']];
        if($result['worknode_id'] != Enums::HEAD_OFFICE_ID && in_array($result['department_id'],$department_ids) && in_array($result['job_title'],$job_titles)) {
            if($result['approval_state_code'] != enums::$audit_status['panding_approval']) {
                $store_data = json_decode($result['store_data'], true);
            } else {
                $store_data = $this->getStoreData($result['worknode_id'], $result['job_title']);
            }
        }else{
            $store_data = (object)[];
        }
        return $store_data;
    }

    /**
     * 固化统计数据
     * @param $info
     * @return array
     */
    public function getCountryPropertyData($info)
    {
        $data = [];
        //所属部门”为Network Management部门及其子部门，且“工作网点”为非总部时
        $department_ids = (new SysDepartmentServer())->getDepartmentIdsUseSettingEnv('dept_network_management_id');
        $job_titles = [enums::$job_title['van_courier'],enums::$job_title['bike_courier'],enums::$job_title['car_courier'],enums::$job_title['dc_officer']];
        if ($info['worknode_id'] != Enums::HEAD_OFFICE_ID && in_array($info['department_id'], $department_ids) && in_array($info['job_title'],$job_titles)) {
            $store_data = $this->getStoreData($info['worknode_id'],$info['job_title']);
            $data['store_data'] = json_encode($store_data);
        }
        return $data;
    }

    /**
     * network部门
     * @param $store_id
     * @param $job_title
     * @return array
     */
    public function getStoreData($store_id,$job_title=null): array
    {
        $couriers = [
            enums::$job_title['bike_courier'],
            enums::$job_title['van_courier'],
            enums::$job_title['car_courier'],
        ];
        $dco = [
            enums::$job_title['dc_officer'],
        ];
        if (in_array($job_title, $couriers)){
            $job_titles = $couriers;
        }elseif (in_array($job_title,$dco)){
            $job_titles = $dco;
        }else{
            $job_titles = [];
        }

        $store_delivery_date = $this->getDeliveryByStoreDate($store_id);

        //网点在职人数
        $store_staff['store_on_job_count'] = $this->getStoreOnJobCount(['store_id' => $store_id,'job_titles' => $job_titles,]);
        //网点近30天离职人数
        $store_staff['store_last_month_resign_count'] = $this->getStoreMonthResignCount([
            'store_id'   => $store_id,
            'job_titles' => $job_titles,
            'begin_date' => date('Y-m-d 00:00:00',strtotime("-29 days")),
            'end_date'   => date('Y-m-d 00:00:00'),
        ]);
        //网点停职人数
        $store_staff['store_suspension_count'] = $this->getStoreSuspensionCount(['store_id' => $store_id,'job_titles' => $job_titles,]);
        //网点待离职人数
        $store_staff['store_waiting_leave_count'] = $this->getStoreWaitingLeaveCount(['store_id' => $store_id,'job_titles' => $job_titles,]);
        //网点待入职人数
        $store_staff['store_waiting_entry_count'] = $this->getStoreWaitingEntryCount(['store_id' => $store_id,'job_titles' => $job_titles,]);
        //网点待招聘hc数量
        $store_staff['store_surplus_hc_count'] = $this->getStoreWaitingRecruitHcCount(['store_id' => $store_id,'job_titles' => $job_titles,]);

        return array_merge($store_delivery_date, $store_staff);
    }

    /**
     * 网点数据 近7天日均进港量/近7天日均揽件量/近7天日均积压量/近7天派件人效
     * - 近7天日均进港量： 取FBI-DC报表管理-每日运营统计报表中（T+1）“揽件总操作量”字段，取近7天的平均值
     * - 近7天日均揽件量：日均揽件量_近7天  （T+1），取近7天的平均值
     * - 近7天日均积压量：日均积压量_近7天（T+1），取近7天的平均值
     * - 近7天派件人效：网点出勤派件人效（取FBI-数据看板-派件人均人效趋势图-派件人均人效（T+1））
     *   取数逻辑：近七天的所有有效妥投的件数/七天的执行任务的派件员总数量 取数逻辑：近七天的所有有效妥投的件数/七天的执行任务的派件员总数量
     * @param $store_id
     * @return int[]
     */
    public function getDeliveryByStoreDate($store_id): array
    {
        $return_arr = [
            'avg_count_delivery_not_signed' => 0, //近7天日均积压量
            'avg_pickup_operation_count'    => 0, //近7天日均进港量
            'avg_total_count'               => 0, //近7天日均揽件量
            'store_efficiency'              => 0, //人效
        ];

        $end_date   = date('Y-m-d', strtotime("-1 day"));
        $begin_date = date('Y-m-d', strtotime("-7 day"));

        $bi_rpc = new ApiClient('ard_api', '', 'deliverycount.get_delivery_by_store_date', $this->lang);
        $bi_rpc->setParams([
            'store_id'   => $store_id,
            'begin_date' => $begin_date,
            'end_date'   => $end_date,
        ]);
        $rpc_result = $bi_rpc->execute();

        if (isset($rpc_result['result']) && $rpc_result['result']['code'] == 1) {
            $data = $rpc_result['result']['data'];

            $count_delivery_not_signed = 0; //积压量
            $pickup_operation_count    = 0; //进港量
            $total_count               = 0; //揽件量
            $delivery_count            = 0; //有效妥投的件数
            $delivery_man_count        = 0; //派件员总数量

            foreach ($data as $key => $value) {
                if (!empty($value['count_delivery_not_signed'])) {
                    $count_delivery_not_signed += $value['count_delivery_not_signed']; //积压量
                }

                if (isset($value['inbound_count'])) {
                    if (!empty($value['inbound_count'])) {
                        $pickup_operation_count += $value['inbound_count']; //进港量 新字段
                    }
                } else {
                    if (isset($value['pickup_operation_count']) && !empty($value['pickup_operation_count'])) {
                        $pickup_operation_count += $value['pickup_operation_count']; //进港量
                    }
                }
                if (!empty($value['total_count'])) {
                    $total_count += $value['total_count']; //揽件量
                }
                if (!empty($value['delivery_count'])) {
                    $delivery_count += $value['delivery_count']; //有效妥投的件数
                }
                if (!empty($value['delivery_formal_man_count'])) {
                    $delivery_man_count += $value['delivery_formal_man_count']; //派件员总数量
                }
            }

            $store_efficiency = $delivery_man_count != 0 ? $delivery_count / $delivery_man_count : 0;
            $return_arr['avg_count_delivery_not_signed'] = round($count_delivery_not_signed / 7);        //近7天日均积压量
            $return_arr['avg_pickup_operation_count']    = round($pickup_operation_count / 7);           //近7天日均进港量
            $return_arr['avg_total_count']               = round($total_count / 7);                      //近7天日均揽件量
            $return_arr['store_efficiency']              = round($store_efficiency, 2); //近7天派件人效
        }
        return $return_arr;
    }

    /**
     * 添加HC申请
     * @param array $paramIn
     * @return void
     * @throws BusinessException
     */
    public function addHc($paramIn = [])
    {
        return parent::addHc($paramIn);
    }

    /**
     * 保存HC负责人
     * @param int $hcManagerStaffId
     * @return void
     */
    protected function saveHcManager($hcId, $hcManagerStaffId, $operatorId)
    {
        if (empty($hcManagerStaffId) || empty($hcId)) {
            return;
        }
        $model                = new HrHcRelateManagerModel();
        $model->hc_id         = $hcId;
        $model->staff_info_id = $hcManagerStaffId;
        $model->operate_id    = $operatorId;
        $model->save();
    }
}