<?php

namespace FlashExpress\bi\App\Modules\My\Server\Osm;

use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AttendanceHikStoreSettingModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\StaffHikvisionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Modules\My\enums\OsmEnums;
use FlashExpress\bi\App\Server\Osm\OutsourcingStaffServer AS GlobalBaseServer;

class OutsourcingStaffServer extends GlobalBaseServer
{
    public function getNationalityEnums()
    {
        return OsmEnums::$nationality_list;
    }
  
    public function getJobTitleList()
    {
        //职位
        $job_list = [];
        foreach (OsmEnums::$job_title as $key => $val) {
            $job_list[] = [
                'code' => $key,
                'name' => $this->getTranslation()->_($val),
            ];
        }
        return $job_list;
    }
}