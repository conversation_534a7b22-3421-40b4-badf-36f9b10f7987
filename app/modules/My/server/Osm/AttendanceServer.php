<?php


namespace FlashExpress\bi\App\Modules\My\Server\Osm;


use FlashExpress\bi\App\Server\Osm\AttendanceServer as GlobalBaseServer;

class AttendanceServer extends GlobalBaseServer
{
    /**
     * 获取结算系数
     * @param $working_hours
     * @param $isHoliday
     * @return float|int
     */
    public function getSettlementCoefficient($working_hours, $isHoliday)
    {
        $working_hours = $working_hours / 10;//$working_hours 工时 是乘 10后的，所以要除以10。
        $settlement_coefficient = 0;
        if ($working_hours < 6) {
            $settlement_coefficient = 0;
        } elseif ($working_hours >= 6 && $working_hours < 7) {
            $settlement_coefficient = 0.5;
        } elseif ($working_hours >= 7 && $working_hours < 8) {
            $settlement_coefficient = 0.6;
        } elseif ($working_hours >= 8 && $working_hours < 9) {
            $settlement_coefficient = 0.7;
        } elseif ($working_hours >= 9 && $working_hours < 10) {
            $settlement_coefficient = 0.8;
        } elseif ($working_hours >= 10 && $working_hours < 11) {
            $settlement_coefficient = 0.9;
        } elseif ($working_hours >= 11) {
            $settlement_coefficient = 1;
        }
        if ($isHoliday) {
            $settlement_coefficient = $settlement_coefficient * 2;
        }

        return $settlement_coefficient;
    }
}