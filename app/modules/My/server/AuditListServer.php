<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Server\AuditListServer as GlobalBaseServer;

class AuditListServer extends GlobalBaseServer
{
    /**
     * 加工列表
     * @param $list
     * @param $auditShowType
     * @param $auditStateType
     * @return array
     */
    public function generateList($list, $auditShowType, $auditStateType): array
    {
        return parent::generateList($list, $auditShowType, $auditStateType);
    }

    /**
     * 是否为 Lnt
     * @param $data
     * @param $lntCompany
     * @param $approvalServer
     * @return bool
     */
    protected function getIsLnt($data, $lntCompany, $approvalServer): bool
    {
        //是否为 LNT
        if (in_array($data['biz_type'], [
            AuditListEnums::APPROVAL_TYPE_JT,
            AuditListEnums::APPROVAL_TYPE_JT_SPECIAL,
        ])) { //各个业务实现: 是申请人还是被申请人
            $instance = $approvalServer->getInstance($data['biz_type']);
            if (method_exists($instance, 'checkIfLntStaff')) {
                return $instance->checkIfLntStaff($data['biz_value']);
            }
        }
        return StaffServer::isLntStaffV2($data['submitter_id'], $lntCompany);
    }

    /**
     * 获取审批状态 text
     * @param int $state
     * @return string
     */
    protected function getAuditStateLnt(int $state): string
    {
        $statusArray = [
            '1' => $this->getTranslation()->_('pending_lnt'),
            '2' => $this->getTranslation()->_('approved_lnt'),
            '3' => $this->getTranslation()->_('dismissed_lnt'),
            '4' => $this->getTranslation()->_('cancel'),
            '5' => $this->getTranslation()->_('closed'),
        ];
        return $statusArray[$state] ?? '';
    }
}