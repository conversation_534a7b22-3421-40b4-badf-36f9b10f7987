<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/8/16
 * Time: 9:59 AM
 */

namespace FlashExpress\bi\App\Modules\My\Server;


use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2Model;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftV2Model;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Server\AttendanceHubServer as GlobalBaseServer;

class AttendanceHubServer extends GlobalBaseServer
{
    /**
     * 获取员工班次
     * @param $staff_ids
     * @param $post_date
     * @return array
     */
    public function getStaffShiftMap($staff_ids, $post_date)
    {
        if (empty($staff_ids)) {
            $this->getDI()->get('logger')->write_log("recover_attendance 员工为空 ".json_encode($staff_ids));
            return [];
        }

        // 新版班次表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.staff_info_id,s.shift_date,s.shift_id,s.shift_extend_id,e.shift_type,e.first_start as [start],e.first_end as [end]');
        $builder->from(['s' => HrStaffShiftV2Model::class]);
        $builder->leftjoin(HrShiftV2Model::class, 's.shift_id = m.id', 'm');
        $builder->leftjoin(HrShiftV2ExtendModel::class, 's.shift_extend_id = e.id', 'e');
        $builder->inWhere('s.staff_info_id', $staff_ids);
        $builder->andWhere('s.shift_date = :date:', ['date' => $post_date]);

        $shift_data = $builder->getQuery()->execute()->toArray();
        $shift_data = empty($shift_data) ? [] :  array_column($shift_data,null,'staff_info_id');

        // 优先获取考勤表班次
        $att_shift = StaffWorkAttendanceModel::find([
            'conditions' => "staff_info_id in ({staff_ids:array}) and attendance_date = :attendance_date:",
            'bind'       => [
                'staff_ids'       => $staff_ids,
                'attendance_date' => $post_date,
            ],
        ])->toArray();
        $att_shift = empty($att_shift) ? [] : array_column($att_shift, null, 'staff_info_id');

        $staffShiftMap = [];
        foreach ($staff_ids as $staff_id) {
            $shift_begin = $shift_end = "";
            $shift_id    = $shift_ext_id = $shift_type = 0;
            // 1.取班次表
            if (!empty($shift_data[$staff_id])) {
                $shift_begin  = $shift_data[$staff_id]['start'];
                $shift_end    = $shift_data[$staff_id]['end'];
                $shift_id     = $shift_data[$staff_id]['shift_id'];
                $shift_ext_id = $shift_data[$staff_id]['shift_extend_id'];
            }
            // 2.取考勤班次
            if (!empty($att_shift[$staff_id])) {
                if (!empty($att_shift[$staff_id]['shift_start']) && !empty($att_shift[$staff_id]['shift_end'])) {
                    $shift_begin  = $att_shift[$staff_id]['shift_start'];
                    $shift_end    = $att_shift[$staff_id]['shift_end'];
                    $shift_id     = $att_shift[$staff_id]['shift_id'];
                    $shift_ext_id = $att_shift[$staff_id]['shift_ext_id'];
                }
            }
            if ($shift_begin && $shift_end) {
                $row['start'] = $shift_begin;
                $row['end'] = $shift_end;
                $row['shift_id'] = $shift_id;
                $row['shift_ext_id'] = $shift_ext_id;
                $staffShiftMap[$staff_id] = $row;
            }
        }
        return $staffShiftMap;
    }


}