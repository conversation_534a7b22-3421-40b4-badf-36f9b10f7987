<?php

namespace FlashExpress\bi\App\Modules\My\Server;


//转岗确认单
use FlashExpress\bi\App\Enums\JobTransferConfirmEnums;
use FlashExpress\bi\App\Enums\PdfEnums;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\Server\formPdfServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class JobTransferAcknowledgementServer extends BaseServer
{

    /**
     * 获取确认单模板
     * @param $tmpl_type
     * @return  string
     */
    public function getAcknowledgementTmpl($tmpl_type): string
    {
        switch ($tmpl_type) {
            case JobTransferConfirmEnums::CONFIRM_TYPE_GRADE_PROMOTION:
                $filePath = '/views/job_transfer/grade.ftl';
                break;
            case JobTransferConfirmEnums::CONFIRM_TYPE_ONLY_JOB_TITLE_CHANGED:
                $filePath = '/views/job_transfer/job_title.ftl';
                break;
            default:
                $filePath = '/views/job_transfer/others.ftl';
                break;
        }
        $upload_result = OssHelper::uploadFile(APP_PATH . $filePath, true);
        return $upload_result['object_url'];
    }

    /**
     * 生成转岗确认单
     * @param $params
     * @return string
     * @throws \Exception
     */
    public function generateJobTransferPdf($params): string
    {
        $params['template_cnf']        = PdfEnums::RESPONSE_TYPE_DIRECT;
        $params['is_salary_with_unit'] = true;
        $params['sign_url']            = $params['sign_url'] ?? '';
        $params['confirm_date']        = $params['confirm_date'] ?? '';

        //获取转岗确认单
        $transferData   = (new JobTransferConfirmServer($this->lang,
            $this->timeZone))->getConfirmDetail($params);
        $transferDetail = $transferData['data'];
        $tmplPathUrl    = $this->getAcknowledgementTmpl($transferDetail['confirm_type']);

        //组织 pdf 数据，生成 pdf
        $temp_params    = [
            'confirmation_gen_date'    => $transferDetail['confirmation_gen_date'],
            'staff_info_id'            => (string)$transferDetail['staff_info_id'],
            'staff_name'               => $transferDetail['staff_name'],
            'staff_job_title_name'     => $transferDetail['before_job_title_name'],
            'staff_department_name'    => $transferDetail['before_department_name'],
            'after_date'               => $transferDetail['after_date'],
            'confirm_list'             => $transferDetail['confirm_list'],
            'company_full_name'        => $transferDetail['company_full_name'],
            'official_staff_name'      => $transferDetail['official_staff_name'],
            'official_staff_job_title' => $transferDetail['official_staff_job_title'],
            'staff_identity'           => $transferDetail['staff_identity'],
            'confirm_date'             => $params['confirm_date'] ?: $transferDetail['confirm_date'],
            'after_job_title_name'     => $transferDetail['after_job_title_name'],
            'staff_company_name'       => $transferDetail['staff_company_name'],
        ];

        $pdfHelperServer = new PdfHelperServer();
        [$header, $footer] = $pdfHelperServer->getHeaderFooter($transferDetail);

        $pdf_header_footer_setting = [
            'displayHeaderFooter' => true,
            'headerTemplate'      => $header,
            'footerTemplate'      => $footer,
        ];
        $sign_image             = [
            ['name' => 'official_sign_url', 'url' => $transferDetail['official_sign_url']],
            ['name' => 'staff_sign_url', 'url' => $params['sign_url'] ?: $transferDetail['staff_sign_url']],
        ];

        try {
            $pdf_file_data = (new formPdfServer())->generatePdf($tmplPathUrl, $temp_params, $sign_image,
                'job_transfer_confirm',
                $pdf_header_footer_setting, 'attchment');
        } catch (\Exception $e) {
            echo $e->getMessage() . $e->getTraceAsString();
        }

        if (empty($pdf_file_data['object_url'])) {
            $this->logger->write_log(['makeTerminationFileUrl  error', $tmplPathUrl, $params]);
            return '';
        }
        return $pdf_file_data['object_url'];
    }
}