<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\Enums\SettingEnvEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftOperateLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftV2Model;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditImageModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Modules\My\library\Enums\VacationEnums;
use FlashExpress\bi\App\Modules\My\Server\Vacation\AnnualServer;
use FlashExpress\bi\App\Modules\My\Server\Vacation\InternationalServer;
use FlashExpress\bi\App\Modules\My\Server\Vacation\MilitaryServer;
use FlashExpress\bi\App\Modules\My\Server\Vacation\TrainingServer;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\ResumeRecommendRepository;
use FlashExpress\bi\App\Repository\StaffOffDayRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditExtendServer;
use FlashExpress\bi\App\Server\AuditListServer;
use FlashExpress\bi\App\Server\AuditServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\HireTypeChangeServer;
use FlashExpress\bi\App\Server\HrShiftServer;
use FlashExpress\bi\App\Server\KpiServer;
use FlashExpress\bi\App\Server\OsPriceServer;
use FlashExpress\bi\App\Server\PaperDocumentServer;
use FlashExpress\bi\App\Server\ProbationTargetServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SyncServer;
use FlashExpress\bi\App\Server\SysDepartmentServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\WorkdayServer;

class AuditServer extends GlobalBaseServer
{

    protected $month_arr = [];//enums::LEAVE_INVALID_MONTH_MA;//年假失效月
    protected $last_day = enums::LEAVE_INVALID_DATE_MA;//年假失效日
    public $sub_day = array(1,3,28,10,29,16,19,13,12,37);//需要跳过休息日的类型

    //https://flashexpress.feishu.cn/docx/G9tEdQfXToN7XAx36j0cKbgsnCb   陪产假去掉了 改为程序判断（ 正式员工、月薪制合同工、日薪制合同工、时薪制合同工在BY和HCM申请时判断：入职是否满一年）
    protected $forbidden   = array(enums::LEAVE_TYPE_10,enums::LEAVE_TYPE_29);//婚假和 考试假 试用期不能申请 新增 陪产假
    protected $time_limit_start = array(19);//限制开始时间 只能当天以后
    //特殊部门 限制只能申请当天以后的请假类型
    protected $special_limit_start = array(1,4,5,19,13,12,37);
    public $one_time = array(enums::LEAVE_TYPE_42);//入职以来 一次 国民假
    public $one_send = array(enums::LEAVE_TYPE_10,enums::LEAVE_TYPE_16);//入职以来 限制总额不限制次数
    public $by_create_time = array(4,5,19);//额度按创建时间计算 针对跨年的申请
    protected $show_type    = '1,2,13,37';//请假页面 需要展示额度的 类型
    protected $need_img = array(enums::LEAVE_TYPE_3,enums::LEAVE_TYPE_27,enums::LEAVE_TYPE_28,enums::LEAVE_TYPE_37,enums::LEAVE_TYPE_5);//图片必填的请假类型

    public static $c_days = 20;//c 级别 20天
    public $newVersionType = [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_19, enums::LEAVE_TYPE_4, enums::LEAVE_TYPE_42];//改版新结构类型

    public $leave_obj;

    public $permissionStaffInfo;
    public $shiftInfo;
    public $isLiveJob;


    /**
     * 补卡添加
     * @param array $paramIn
     * @param $userinfo
     * @return array
     * @throws ValidationException|BusinessException
     */
    public function reissueCardAdd($paramIn = [], $userinfo)
    {
        //[1]参数定义
        $attendanceType = $this->processingDefault($paramIn, 'attendance_type', 2);
        $auditReason    = $this->processingDefault($paramIn, 'audit_reason');
        $auditReason    = strip_tags(addcslashes(stripslashes($auditReason), "'"));
        $date_at        = date('Y-m-d', strtotime($paramIn['date_at']));
        $imagePathArr   = $this->processingDefault($paramIn, 'image_path');

        //检验工号 是否存在 hrs
        $staff_re   = new StaffRepository($this->lang);
        $staff_info = $staff_re->getStaffPosition($paramIn['staff_id']);


        if ($staff_info['is_sub_staff'] == 1) {
            throw new BusinessException($this->getTranslation()->_('sub_staff_disable'));
        }
        if ($this->checkStaffFormal($staff_info)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('os_or_franchisee_staff_disable'));
        }

        $reissueCardDate = $paramIn['reissue_card_date'];
        //选的次日 打卡 需加24小时
        if ($paramIn['day_type'] == StaffAuditModel::DAY_TYPE_NEXT) {
            $reissueCardDate = $paramIn['reissue_card_date'] = date('Y-m-d H:i:s', strtotime("{$paramIn['reissue_card_date']} +1 day"));
        }

        $this->checkReissue($paramIn, $staff_info);

        $staffId = $staff_info['staff_info_id'];

        //审批流用
        $extend['store_id'] = $staff_info['sys_store_id'];
        //超时关闭字段
        $timeOut = date('Y-m-d 00:00:00',strtotime('+3 day'));
        if($this->reissueSpecialDepartment){
            $timeOut = date('Y-m-d 00:00:00',strtotime('+2 day'));
        }
        $extend['time_out'] = $timeOut;
        $serialNo  = $this->getID();
        $insetData = [
            'staff_info_id'     => $staff_info['staff_info_id'],
            'reissue_card_date' => $reissueCardDate,
            'attendance_type'   => $attendanceType,
            'audit_reason'      => $auditReason,
            'attendance_date'   => $date_at,
            'status'            => enums::$audit_status['panding'],
            'audit_type'        => enums::$audit_type['AT'],
            'serial_no'         => (!empty($serialNo) ? 'AT'.$serialNo : null),
            'time_out'          => $timeOut,
        ];

        $db = StaffAuditModel::beginTransaction($this);
        try {
            $db->insertAsDict("staff_audit", $insetData);
            $audit_id = $db->lastInsertId();
            //插入图片
            if (!empty($imagePathArr)) {
                $img_model = new StaffAuditImageModel();
                foreach ($imagePathArr as $k => $v) {
                    $img_clone = clone $img_model;
                    $row       = [
                        'audit_id'   => $audit_id,
                        'image_path' => $v,
                    ];
                    $img_clone->create($row);
                }
            }
            $rst = (new ApprovalServer($this->lang, $this->timezone))->create($audit_id, enums::$audit_type['AT'], $staffId, null, $extend);
            if (!$rst) {
                $this->logger->write_log("reissueCardAdd log ".json_encode($rst, JSON_UNESCAPED_UNICODE), 'info');
                throw new \Exception('audit staff insert reissueCardAdd  workflow fail '.$rst);
            }
            $db->commit();
            return $this->checkReturn([]);
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log('reissue_card msg '.$e->getMessage()." file ".$e->getFile()." line ".$e->getLine().$e->getTraceAsString(), "error");
            return $this->checkReturn(-3, $this->getTranslation()->_('1006'));
        }
    }

    /**
     * @param $paramIn
     * @param $staff_info
     * @return bool
     * @throws ValidationException
     */
    public function checkReissue($paramIn, $staff_info)
    {
        $t = $this->getTranslation();
        $imagePathArr    = $this->processingDefault($paramIn, 'image_path');
        $reissueCardDate = $paramIn['reissue_card_date'];
        $attendanceType  = $this->processingDefault($paramIn, 'attendance_type', 2);
        $day             = $paramIn['date_at'] = date('Y-m-d', strtotime($paramIn['date_at']));
        $dayType         = intval($paramIn['day_type']);// 1-当天 2-次日
        $staffId         = $staff_info['staff_info_id'];

        $suffix = '';
        if(in_array($staff_info['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
            $suffix = '_unpaid';
        }

        $startEndIndex   = ($attendanceType % 2);//1 上班 0 下班

        $yesterday = date('Y-m-d', strtotime("{$day} -1 day"));
        $tomorrow  = date('Y-m-d', strtotime("{$day} +1 day"));
        //时区转化时间用
        $addHour = $this->config->application->add_hour;

        //即使存在 打卡记录（包括出差打卡） 也可以补卡 不过需要判断 打卡时间是否迟到 如果没迟到 提示二次确认（打的卡没问题 还补啥）  https://l8bx01gcjr.feishu.cn/docs/doccn2Yqjmknumh6s70wKNspZ8b
        $shiftServer = new AttendanceCalendarV2Server($this->lang, $this->timezone);
        $shiftInfo   = $shiftServer->getDailyShiftInfo($staffId, $yesterday, $tomorrow);

        $shiftIndex      = StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;//默认0 一次 打卡
        if(!empty($shiftInfo[$day]) && $shiftInfo[$day]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE){
            $shiftIndex      = ceil($attendanceType / 2);//1 第一次 2 第二次
        }


        if (empty($staff_info)) {
            throw new ValidationException('invalid staff id');
        }
        //少参数
        if (empty($paramIn['date_at']) || empty($paramIn['day_type'])) {
            throw new ValidationException($t->_('miss_args'));
        }
        $auditRe = new AuditRepository($this->lang);
        //查询时间内是否有相同的 补卡记录
        $punchCardData = StaffAuditModel::find([
            'conditions' => 'staff_info_id = :staff_id: and attendance_date in ({date_at:array}) and audit_type = 1 and status in (1,2)',
            'columns'    => "staff_info_id,attendance_type,attendance_date,reissue_card_date,concat(attendance_date,'_',attendance_type) u_key",
            'bind'       => [
                'staff_id' => $staffId,
                'date_at'  => [$yesterday, $day, $tomorrow],
            ],
        ])->toArray();
        if (!empty($punchCardData)) {
            $punchCardData = array_column($punchCardData, null, 'u_key');
        }
        //存在补卡记录 提示
        $thisKey = "{$day}_{$attendanceType}";
        if (!empty($punchCardData[$thisKey])) {
            throw new ValidationException($t->_($startEndIndex > 0 ? '1004' . $suffix : '1005' . $suffix));
        }

        //网点员工 hub 的 要传图片
        if ($staff_info['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_1']) {
            $storeInfo     = SysStoreModel::findFirst([
                'conditions' => 'id = :store_id:',
                'bind'       => ['store_id' => $staff_info['sys_store_id']],
            ]);
            $limitCategory = [
                enums::$stores_category['hub'],
                enums::$stores_category['os'],
                enums::$stores_category['bhub'],
            ];
            if (!empty($storeInfo) && in_array($storeInfo->category, $limitCategory)) {
                //新需求 hub相关网点可以走补卡申请 必须上传图片
                if (empty($imagePathArr)) {
                    throw new ValidationException($t->_('2106'));
                }
                if (count($imagePathArr) > 3) {
                    throw new ValidationException($t->_('at most 3 photos'));
                }
            }
        }

        //新增逻辑 可配置Network Operations【320】和Big Distribution Centre【15084】（包含子部门） https://flashexpress.feishu.cn/docx/Ccrad5zjPoFVyzxRqhUctPdcnWh
        $setVal = (new SettingEnvServer())->getSetVal('new_attendance_policy');
        if(!empty($setVal)){
            $ids = explode(',', $setVal);
            $this->reissueSpecialDepartment = $this->setReissueSpecialDepartment($staff_info, $ids);
        }
        //[3]查询本月打卡次数|超过三次补卡失败 查询时间内打卡记录  4月10日产品去掉每月补卡限制-> 20190729 加上限制（只针对上班打卡） 2019-10-19 新增逻辑 上班班 加一起不能超过3次
        $punchMonthCardNum = $auditRe->getReissueTimes($staffId, $day);
        if ($punchMonthCardNum >= 3) {
            throw new ValidationException($t->_('1025'));
        }
        //当天
        $current_date = date('Y-m-d', time());
        $current_time = date('Y-m-d H:i:s', time());

        //检测日期[1是否大于当前时间 2是否三天内]
        if (strtotime($reissueCardDate) > time()) {
            throw new ValidationException($t->_('1008'));
        }
        //不能申请 大前天之前的日期
        if (strtotime($day) < (time() - 3600 * 72)) {
            throw new ValidationException($t->_('1007'));
        }
        //如果特殊限制部门 只能申请 当天 看是否跨天班次 跨天 可以申请当天和前一天
        if($this->reissueSpecialDepartment){
            $limitDate[] = $current_date;
            if (
                (!empty($shiftInfo[$day]) && $shiftInfo[$day]['first_start'] > $shiftInfo[$day]['first_end'])
                || (!empty($shiftInfo[$day]['second_end']) && $shiftInfo[$day]['first_start'] > $shiftInfo[$day]['second_end'])
            ) {
                $limitDate[] = date('Y-m-d', strtotime('-1 day'));
            }
            if(!in_array($day, $limitDate)){
                throw new ValidationException($t->_('reissue_limit_today'));
            }
        }

        //如果是 第一次 上班补卡 不能选择次日 上班打卡时间和出勤日期必须是同一天
        if ($attendanceType == StaffAuditModel::ATTENDANCE_TYPE_FIRST_UP && $dayType == StaffAuditModel::DAY_TYPE_NEXT) {
            throw new ValidationException($t->_('repair_same_day' . $suffix));
        }

        //获取考勤信息 针对 补卡日期 的前一天 和 后一天
        $attendanceRe = new AttendanceRepository($this->lang, $this->timezone);
        $cardInfo     = $attendanceRe->getAttendanceData($staffId, [$yesterday, $day, $tomorrow]);
        $tripCartInfo = $attendanceRe->getTripAttendanceData($staffId, [$yesterday, $day, $tomorrow]);


        //如果当前时间 在本次补卡的 最早打卡最晚打卡 范围区间内 提示不需要补卡
        $shiftStartName = 'first_allow_attendance_time';
        $shiftEndName   = 'first_check_no_record_time';
        if ($shiftIndex > 1) {
            $shiftStartName = 'second_allow_attendance_time';
            $shiftEndName   = 'second_check_no_record_time';
        }
//        //今天 并且补的也是今天 并且 在区间内 并且 没有打卡记录 还能打卡 不用补卡 原逻辑
//        if ($day == $current_date && $current_time >= $shiftInfo[$day][$shiftStartName] && $current_time <= $shiftInfo[$day][$shiftEndName]) {
//            //看是否有打卡记录 包括出差打卡
//            $cardName = $startEndIndex > 0 ? 'started_at' : 'end_at';//1 上班 0 下班
//            $k        = "{$day}_{$shiftIndex}";
//            if (!empty($cardInfo[$k][$cardName]) || !empty($tripCartInfo[$k][$cardName])) {
//                throw new ValidationException($t->_('1021'));
//            }
//        }

        //取出当次补卡 序列 去验证 前一个序列 和 后一个序列 必须在这个区间之间
        $keyBeforeDate = $day;//验证 上一个时间
        $keyBeforeType = $attendanceType - 1;//只有整数
        //找完以后 验证 前一天 时间是否符合允许 优先 打卡表 然后是 没有 再找 出差打卡表
        $beforeType = $shiftIndex;//0 单次班 1 第一次班 2 第二次班
        //2次班的上班卡 并且补的也是 第二次班的 要把 $beforeType 改成第一次班的 1
        if($startEndIndex > 0 && $shiftIndex != StaffWorkAttendanceModel::SHIFT_TYPE_ONLY && $attendanceType == StaffAuditModel::ATTENDANCE_TYPE_SECOND_UP){
            $beforeType = StaffWorkAttendanceModel::SHIFT_TYPE_FIRST;
        }

        //如果补的是 当天的 第一个上班 要去找昨天的 下班记录
        if ($attendanceType == StaffAuditModel::ATTENDANCE_TYPE_FIRST_UP) {
            $keyBeforeDate = $yesterday;
            $keyBeforeType = StaffAuditModel::ATTENDANCE_TYPE_FIRST_LOW;//默认一个班次的下班 如果双班次 要给4
            if (!empty($shiftInfo[$yesterday]) && $shiftInfo[$yesterday]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
                $keyBeforeType = StaffAuditModel::ATTENDANCE_TYPE_SECOND_LOW;
                $beforeType      = ceil($keyBeforeType / 2);//1 第一次 2 第二次
            }else{
                //昨天是一次班 跟今天不一样
                $beforeType = StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;
            }
        }

        $beforeName = ($keyBeforeType % 2) > 0 ? 'started_at' : 'end_at';//1 上班 0 下班
        $cardKey    = "{$keyBeforeDate}_{$beforeType}";//打卡时间 判断 上一次 打卡 和下一次打卡 是否符合 要求 这个key 也要用到 出差打卡时间上
        $reissueKey = "{$keyBeforeDate}_{$keyBeforeType}";//补卡信息的 key 要判断 上一次补卡时间 和下一次补卡时间 是否允许本次补卡

        $this->getDI()->get("logger")->write_log("before_reissue {$staffId} {$beforeName} {$cardKey} {$reissueKey}",'info');

        //补卡时间 转换为 零时区
        $beforeTime = '';
        if (!empty($cardInfo[$cardKey][$beforeName])) {
            $beforeTime = date('Y-m-d H:i:s', strtotime($cardInfo[$cardKey][$beforeName]) + $addHour * 3600);
        }

        //出差打卡判断
        if (empty($cardInfo[$cardKey][$beforeName]) && !empty($tripCartInfo[$cardKey][$beforeName])) {
            $beforeTime = date('Y-m-d H:i:s', strtotime($tripCartInfo[$cardKey][$beforeName]) + $addHour * 3600);
        }

        //补卡判断
        if (!empty($punchCardData[$reissueKey])) {
            $beforeTime = $punchCardData[$reissueKey]['reissue_card_date'];
        }

        //本次补卡时间不能在 前一次 之前
        if (!empty($beforeTime) && $reissueCardDate < $beforeTime) {
            throw new ValidationException($t->_('repair_yesterday' . $suffix));
        }

        //如果是 当天的最后一个下班 2 或者 4 去找
        $keyAfterDate = $day;
        $keyAfterType = $attendanceType + 1;
        $afterType  = $shiftIndex;

        //2次班的下班卡 并且补的第一次班下班卡 要把 $beforeType 改成第二次班的 2
        if( empty($startEndIndex)
            && $shiftIndex != StaffWorkAttendanceModel::SHIFT_TYPE_ONLY
            && $attendanceType == StaffAuditModel::ATTENDANCE_TYPE_FIRST_LOW){
            $afterType = StaffWorkAttendanceModel::SHIFT_TYPE_SECOND;
        }

        //判断班次 一次班 并且 补卡类型是2  或者 二次班 并且补卡类型是4 要去找后一天的上班 1
        if (
            (!empty($shiftInfo[$day]) && $shiftInfo[$day]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE && $attendanceType == StaffAuditModel::ATTENDANCE_TYPE_FIRST_LOW)
            || (!empty($shiftInfo[$day]) && $shiftInfo[$day]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE && $attendanceType == StaffAuditModel::ATTENDANCE_TYPE_SECOND_LOW)
        ) {
            $keyAfterDate = $tomorrow;
            $keyAfterType = StaffAuditModel::ATTENDANCE_TYPE_FIRST_UP;
            //双班次 判断
            if($shiftInfo[$keyAfterDate]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE
                && $attendanceType == StaffAuditModel::ATTENDANCE_TYPE_SECOND_LOW){
                $afterType = ceil($keyAfterType / 2);//1 第一次 2 第二次
            }
            //如果后一天 不是双次班 $afterType 改为0
            if(!empty($shiftInfo[$keyAfterDate]) && $shiftInfo[$keyAfterDate]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE){
                $afterType =  StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;
            }
        }
        //判断后一天
        $afterName  = ($keyAfterType % 2) > 0 ? 'started_at' : 'end_at';//1 上班 0 下班
        $cardKey    = "{$keyAfterDate}_{$afterType}";//打卡时间 判断 上一次 打卡 和下一次打卡 是否符合 要求 这个key 也要用到 出差打卡时间上
        $reissueKey = "{$keyAfterDate}_{$keyAfterType}";//补卡信息的 key 要判断 上一次补卡时间 和下一次补卡时间 是否允许本次补卡

        $this->getDI()->get("logger")->write_log("after_reissue {$staffId} {$afterName} {$cardKey} {$reissueKey}",'info');

        $afterTime = '';
        if (!empty($cardInfo[$cardKey][$afterName])) {
            $afterTime = date('Y-m-d H:i:s', strtotime($cardInfo[$cardKey][$afterName]) + $addHour * 3600);
        }

        //出差打卡判断
        if (empty($cardInfo[$cardKey][$afterName]) && !empty($tripCartInfo[$cardKey][$afterName])) {
            $afterTime = date('Y-m-d H:i:s', strtotime($tripCartInfo[$cardKey][$beforeName]) + $addHour * 3600);
        }

        //补卡判断
        if (!empty($punchCardData[$reissueKey])) {
            $afterTime = $punchCardData[$reissueKey]['reissue_card_date'];
        }

        //本次补卡时间不能在 前一次 之前
        if (!empty($afterTime) && $reissueCardDate > $afterTime) {
            throw new ValidationException($t->_('repair_tomorrow' . $suffix));
        }

        //获取对应的 打卡数据 看 是否迟到早退  如果没有 返回
        $cardName = $startEndIndex > 0 ? 'started_at' : 'end_at';//1 上班 0 下班
        $k        = "{$day}_{$shiftIndex}";

        //班次 时间字段名
        $shiftIndexName = $startEndIndex > 0 ? 'first_check_late_in_time' : 'first_check_early_out_time';
        if ($shiftIndex > 1) {
            $shiftIndexName = $startEndIndex > 0 ? 'second_check_late_in_time' : 'second_check_early_out_time';
        }

        //存在补卡对应的 打卡记录
        if (!empty($cardInfo[$k][$cardName])) {
            $cardTime = date('Y-m-d H:i:s', strtotime($cardInfo[$k][$cardName]) + $addHour * 3600);
        }


        //存在 补卡对应的 出差打卡记录并且 打卡记录是空的
        if (empty($cardInfo[$k][$cardName]) && !empty($tripCartInfo[$k][$cardName])) {
            $cardTime = date('Y-m-d H:i:s', strtotime($tripCartInfo[$k][$cardName]) + $addHour * 3600);
        }

        //判断 没有迟到早退 返回
        if (!empty($cardTime) && !empty($shiftInfo[$day]) && $startEndIndex > 0 && $cardTime <= $shiftInfo[$day][$shiftIndexName] && empty($paramIn['is_submit'])) {
            //判断 上班
            throw new ValidationException($t->_('no_need_reissue_notice'.$suffix,['time'=>$cardTime]), 10086);
        }
        if (!empty($cardTime) && !empty($shiftInfo[$day]) && $startEndIndex == 0 && $cardTime >= $shiftInfo[$day][$shiftIndexName] && empty($paramIn['is_submit'])) {
            //判断 下班
            throw new ValidationException($t->_('no_need_reissue_notice'.$suffix,['time'=>$cardTime]), 10086);
        }

        //补上班卡 看与下班卡时间够不够班次时长
        if (!empty($shiftInfo[$day]) && $startEndIndex > 0 && !empty($cardInfo[$k]['end_at']) && empty($paramIn['confirm_10088'])) {
            $shift_start_field = 'first_start';
            $shift_end_field   = 'first_end';
            if ($shiftInfo[$day]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE && $attendanceType == 3) {
                $shift_start_field = 'second_start';
                $shift_end_field   = 'second_end';
            }
            $isLiveJob = (new StaffServer())->checkIsLiveJob($staff_info,$day);
            $seconds = strtotime(show_time_zone($cardInfo[$k]['end_at'])) -  strtotime($reissueCardDate);
             $shiftSeconds = HrShiftServer::getShiftSeconds($day, $shiftInfo[$day][$shift_start_field],
                 $shiftInfo[$day][$shift_end_field]);
            if (!$isLiveJob && $seconds < $shiftSeconds) {
                $this->makeAttendanceDurationLessNotice($shiftSeconds, $suffix, $t);
            }
        }

        //补下班卡 看与上班卡时间够不够班次时长
        if (!empty($shiftInfo[$day]) && $startEndIndex == 0 && !empty($cardInfo[$k]['started_at']) && empty($paramIn['confirm_10088'])) {
            //判断 下班
            $shift_start_field = 'first_start';
            $shift_end_field   = 'first_end';
            if ($shiftInfo[$day]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE && $attendanceType == 4) {
                $shift_start_field = 'second_start';
                $shift_end_field   = 'second_end';
            }

            $shiftSeconds =  $this->getLiveJobShiftDuration($staff_info,$day,show_time_zone($cardInfo[$k]['started_at'])) ? : HrShiftServer::getShiftSeconds($day, $shiftInfo[$day][$shift_start_field],
                $shiftInfo[$day][$shift_end_field]);
            $seconds      = strtotime($reissueCardDate) - strtotime(show_time_zone($cardInfo[$k]['started_at']));

            if ($seconds < $shiftSeconds) {
                $this->makeAttendanceDurationLessNotice($shiftSeconds, $suffix, $t);
            }
        }
        return true;
    }

    /**
     * @param $shiftSeconds
     * @param $suffix
     * @param $t
     * @return void
     * @throws ValidationException
     */
    private function makeAttendanceDurationLessNotice($shiftSeconds,$suffix,$t): void
    {
        $timeInfo = DateHelper::dealSecondsToHoursAndMinutes($shiftSeconds);
        $timeStr  = '';
        if ($timeInfo['hours'] > 0) {
            $timeStr .= $t->_('early_off_hour', ['x' => $timeInfo['hours']]);
        }
        if ($timeInfo['minutes'] > 0) {
            $timeStr .= $t->_('early_off_minutes', ['x' => $timeInfo['minutes']]);
        }
        if (!empty($timeStr)) {
            throw new ValidationException($t->_('attendance_duration_less'.$suffix,['time'=>$timeStr]), 10088);
        }
    }


    //补卡 判断是否术语限制部门
    public function setReissueSpecialDepartment($staffInfo, $departmentIds){
        $departmentServer = new SysDepartmentServer($this->lang,$this->timezone);
        $flag = false;
        foreach ($departmentIds as $id){
            $res = $departmentServer->getDepartmentIds($id);
            if(in_array($staffInfo['sys_department_id'],$res) || in_array($staffInfo['node_department_id'], $res)){
                $flag = true;
            }
        }
        return $flag;
    }

    /**
     * 获取权限
     * @param array paramIn
     * @return array
     */
    public function getListPermission( $paramIn = [] ): array {
        //补卡
        $result['AT'] = 1;   //默认存在
        //请假
        $result['LE'] = $this->isLeavePermission() ? 1 : 2;     //默认存在

        //LH 移除
        //$result['LH'] = $this->checkLcDataPosition($paramIn) == true ? 1 : 2;
        //新耗材申请上线老的耗材入口移除
        //$result['Wms'] = $this->getWmsPermission($paramIn) == true ? 1 : 2;
        //修改里程权限
        //$result['Mile'] = $this->getMilePermission($paramIn) == true ? 1 : 2; //10073需求 不展示修改里程表
        $result['Mile'] = 2; //10073需求 不展示修改里程表
        //加班-申请OT
        $result['OT'] = $this->getOTPermission($paramIn) == true ? 1 : 2;
        //个人代理类型 申请的奖金（ot） 马来关闭 不要了
        $result['bonus'] = 2;
        //出差
        $result['Trip'] = $this->getTripPermission($paramIn) == true ? 1 : 2;
        //车辆里程
        $result['Vehicle'] = $this->getVehiclePermission($paramIn) == true ? 1 : 2;
        //HC
        $result['HC'] = $this->checkHcDataRole($paramIn) == true ? 1 : 2;
        //补申请
        $result['apply'] = $this->getApplyPermission($paramIn) == true ? 1 : 2;
        //加班车
        $result['fleet'] = $this->getFleetPermission($paramIn) == true ? 1 : 2;
        //离职-
        $result['resign'] = $this->isResignPermission() ? 1 : 2;
        //解约
        $result['cancel_contract'] = $this->checkIsShowCancelContract() == true ? 1 : 2;
        //解约个人代理
        $result['company_termination_contract'] = $this->isCompanyTerminationContractPermission() ? 1 : 2;
        //外协员工
        $result['OS'] = $this->getOSPermission($paramIn) == true ? 1 : 2;
        //外协员工加班申请入口
        $result['outsourcing_ot'] = (true === (new OutsourcingOTServer($this->lang,$this->timezone))->isHubFenBoManger($paramIn)) ? 1 : 2;
        //资产申请
        //$result['Asset'] = $this->getASPermission($paramIn) == true ? 1 : 2;
        //举报
        $result['Report'] = $this->getReportPermission($paramIn) == true ? 1 : 2;
        //到岗确认
        $result['Confirm'] = $this->getEntryConfirmPermission($paramIn) == true ? 1 : 2;
        //工资条pdf
        $result['salary'] = $this->salary_pdf_permission($paramIn) == true ? 1 : 2;
        //油费补贴
        $result['fuel_subsidy'] = $this->getFuelSubsidyPermission($paramIn) == true ? 1 : 2;
        //在职证明
        $result['on_job'] = $this->on_job_cimb_permission($paramIn['staff_id']??0) == true ? 1 : 2;
        //工资证明
        $result['payroll'] = $this->on_job_pdf_permission($paramIn) == true ? 1 : 2;
        //离职资产确认
        $result['resign_as'] = $this->getResignAssetPermision($paramIn) == true ? 1 : 2;
        //转岗
        $result['TF'] = $this->getJobTransferPermissionV2($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_nw'] = $this->getFreightDiscNetworkPermission($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_shop'] = $this->getFreightDiscShopPermission($paramIn) == true ? 1 : 2;
        //运费申请
        //$result['FD_other'] = $this->getFreightDiscOtherPermission($paramIn) == true ? 1 : 2;
        //抄送列表
        $result['CC'] = 1;
        //黄牌项目出差
        $result['yc_Trip'] = $this->getYCTripPermission($paramIn) == true ? 1 : 2;

        // 销售CRM
        $result['Sales_CRM'] = $this->getSalesCRMPermission($paramIn) == true ? 1 : 2;

        //工服购买排除外协员工
        $result['InteriorOrder'] = $this->getInteriorOrdersPermission($paramIn) == true ? 1 : 2;

        // 后勤-报销申请
        $result['fuel_budget']  = 2;

        $result['my_interview'] = (new InterviewRepository($this->timezone))->myInterview($paramIn) == true ? 1 : 2;

        $result['os_price'] = (new OsPriceServer($this->timezone))->permission($paramIn) == true ? 1 : 2;

        //网点支援
        $result['SAS']  = $this->getSASPermission($paramIn) == true ? 1 : 2;
        $result['SASS'] = $this->getSASSPermission($paramIn) == true ? 1 : 2;
        //新版资产申请权限
        $result['NAS'] = $this->getNewAssetPermission($paramIn) ? 1 : 2;

        //申请耗材
        $result['apply_consumables'] = $this->getConsumablesPermission($paramIn) ? 1 : 2;
        //领用包材
        $result['package'] = 1;
        //我的简历
        $resumeRecommendNum   = (new ResumeRecommendRepository())->getShowTotal($paramIn);
        $result['rr_is_show'] = !empty($resumeRecommendNum['is_show']) ? 1 : 2;

        $bool               = (new \FlashExpress\bi\App\Server\AdministrationOrderServer())->isFlashFulfillmentDepartment($paramIn,
            $setVal);
        $result['xz_order'] = (true === $bool) ? 1 : 2;
        //审批-人事-KPI目标
        $result['kpi'] = (new KpiServer())->getKpiPermission($paramIn) ? 1 : 2;
        // CIMB开户函
        $result['cimb'] = $this->on_job_cimb_permission($paramIn['staff_id'] ?? 0) == true ? 1 : 2;
        //quick offer
        $result['quick_offer'] = $this->quickOfferPermission() ? 1 : 2;

        //个人代理申请
        $hireServer = new HireTypeChangeServer($this->lang,$this->timezone);
        $hireServer->staffInfo = $this->permissionStaffInfo;
        $result['hire_change'] = $hireServer->applyPermission() ? 1 : 2;

        // 预支油费
        $result['advance_fuel'] = $this->getAdvanceFuelPermission($paramIn) ? 1 : 2;
        //OA文件夹
        $result['oa_folder'] = $this->OAFolderPermission() ? 1 : 2;
        //hcm入口
        $result['HCM'] = $this->isShowHCMPermission() ? 1 : 2;
        //车辆维修申请
        $result['vehicle_repair_request'] = $this->isShowVehicleRepairRequestPermission() ? 1 : 2;
        //交车&验车
        $result['vehicle_delivery_and_inspection'] = $this->isShowVehicleDeliveryAndInspectionPermission() ? 1 : 2;
        //过滤外协员工权限
        $paramIn['permission_list'] = $result;
        $result                     = $this->outsourcingPermissionFilter($paramIn);
        //纸质单据确认
        $result['paper'] = (new PaperDocumentServer($this->lang, $this->timezone))->getPaperPermission($paramIn) ? 1 : 2;

        //试用期目标
        $result['probation_target'] = (new ProbationTargetServer($this->lang, $this->timezone))->getMenuPermission($paramIn) ? 1 : 2;


        //过滤外协员工权限
        $paramIn['permission_list'] = $result;
        $result                     = $this->outsourcingPermissionFilter($paramIn);
        //耗材调拨入口
        $result['package_allot'] = $this->isShowPackageAllotPermission($this->staffInfo) ? 1 : 2;

        // 快递员推荐
        $result['courier_recommend'] = $this->getCourierRecommendPermission($paramIn) ? 1 : 2;

        return $this->checkReturn(['data' => $result]);
    }



    /**
     * 在职证明以及 cimb银行开户函 权限
     * @param $staff_id
     * @return bool
     */
    public function on_job_cimb_permission($staff_id): bool
    {
        $model      = new StaffRepository($this->lang);
        $staff_info = $model->getStaffPosition($staff_id);

        if (empty($staff_info)) {
            return false;
        }

        if ($staff_info['working_country'] != HrStaffInfoModel::WORKING_COUNTRY_MY) {
            return false;
        }

        if (!in_array($staff_info['hire_type'], [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2])
        || $staff_info['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            return false;
        }

        return true;
    }
    /**
     * 获取出差申请权限
     * @param array paramIn
     * @return boolean
     */
    public function getTripPermission($paramIn = [])
    {
        $setVal = (new SettingEnvServer())->getSetVal(SettingEnvEnums::CLOSE_ENTRANCE_JOBTITLE_IDS);
        $jobIds = empty($setVal) ? [] : explode(',', $setVal);
        if(($jobIds && in_array($paramIn['job_title'], $jobIds))){
            return false;
        }
        return true;
    }


    /**
     * 获取物料申请权限
     * @param array paramIn
     * @return boolean
     */
    public function getWmsPermission($paramIn = [])
    {
        //[1]校验添加权限
        $admin_group = UC('wmsRole')['admin_group'];
        $staffId     = $paramIn['staff_id'];

        //admin小組有提交物料权限
        if (isset($admin_group[$staffId])) {
            return true;
        }

        return $this->check_wrs_permission($staffId);
    }

    public function check_wrs_permission($staffId)
    {
        //20211214 按照11172需求修改 需求产品刘丹丹
        $staff_re = new StaffRepository($this->lang);
        $info = $staff_re->getStaffPositionv3($staffId);
        $setting_server = new SettingEnvServer();
        $wrs_staff = $setting_server->getSetVal('wrs_staff_check_wrs_ids');
        if(!empty($info['job_title']) && in_array($info['job_title'],explode(',',$wrs_staff))){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 获取资产申请权限 和 我的tab  公共资产显示
     * @param array paramIn
     * @param boolean paramIn 是否只判断白名单
     * @return boolean
     */
    public function getASPermission($paramIn = [], $isOnlyWhiteList = false)
    {
        $staffId = $paramIn['staff_id'];
        //[2]获取当前员工职位
        $staff_re = new StaffRepository($this->lang);
        $info = $staff_re->getStaffPositionv3($staffId);

        //申请权限
        //白名单 ：工号为[19685,24455,40450,25921,29053] 或者 职位是Regional Manager(79)、Assistant Regional Manager(269)
        //DC、SP网点: 网点正主管(职位是16)
        //SHOP网点: 网点正主管
        //HUB网点: 指定工号
        $staffArr = explode(',', env('asset_request_white_list', "24455,40450,38983,19685,47094,19060,21426,21328"));
        if (in_array($staffId, $staffArr) || in_array($info['job_title'], [
                enums::$job_title['regional_manager'],
                enums::$job_title['district_manager'],
                enums::$job_title['area_manager'],
            ])) {
            return true;
        }

        if ($isOnlyWhiteList === true) {
            // 只验证工号职位白名单
            return false;
        }

        return $this->check_asset_permission($info);
    }


    public function check_asset_permission($info)
    {
        //20211214 按照11172需求修改 需求产品刘丹丹
        $setting_server = new SettingEnvServer();
        if($info['organization_type'] == 2){//总部的人 全部给权限
            return true;
        }
        $wrs_staff = $setting_server->getSetVal('wrs_staff_check_asset_ids');
        if(!empty($info['job_title']) && in_array($info['job_title'],explode(',',$wrs_staff))){
            return true;
        }else{
            return false;
        }
    }

    /**
     * OA文件夹权限
     * @return bool
     */
    protected function OAFolderPermission(): bool
    {
        //非正式 不展示
        if ($this->staffInfo['formal'] != HrStaffInfoModel::FORMAL_1) {
            return false;
        }
        //个人代理 不展示
        if(in_array($this->staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            return false;
        }

        //黑名单 NotAllowedLoginCompanyID 白名单 WhitelistOfLogin
        $envModel     = new SettingEnvServer();
        $setting_code = ['show_oa_file_folder_job_title', 'WhitelistOfLogin'];
        $setting_val  = $envModel->listByCode($setting_code);
        //没配置 走默认
        if (empty($setting_val)) {
            return true;
        }
        $setting_val = array_column($setting_val, 'set_val', 'code');
        $jobConfig   = empty($setting_val['show_oa_file_folder_job_title']) ? [] : explode(',', $setting_val['show_oa_file_folder_job_title']);
        //在配置的职位里 表示没权限
        if (empty($this->staffInfo['job_title']) || in_array($this->staffInfo['job_title'], $jobConfig)) {
            return false;
        }

        //lnt https://flashexpress.feishu.cn/docx/FQAvdr5USovVJGxbcdKc7JQanog
        $isLnt = StaffServer::isLntCompanyByInfo($this->staffInfo);//如果不是LNT  不继续判断 走默认
        if (!$isLnt) {
            return true;
        }
        $whiteStaff = empty($setting_val['WhitelistOfLogin']) ? [] : explode(',', $setting_val['WhitelistOfLogin']);
        //是lnt 不在白名单 不展示
        if (!in_array($this->staffInfo['staff_info_id'], $whiteStaff)) {
            return false;
        }
        return true;
    }

    /**
     * 转岗-申请权限
     * @param array $paramIn
     * @return bool
     */
    public function getJobtransferPermission(array $paramIn = []): bool
    {
        $staffId = $paramIn["staff_id"];

        //部门负责人
        $staffServer = new StaffServer($this->lang, $this->timezone);
        $manageDepartmentIds = $staffServer->getManageDepartmentList($staffId);
        if (!empty($manageDepartmentIds)) {
            return true;
        }

        //大区负责人
        $manageRegionIds = $staffServer->getManageRegionsList($staffId);
        if (!empty($manageRegionIds)) {
            return true;
        }

        //片区负责人
        $managePieceIds = $staffServer->getManagePiecesList($staffId);
        if (!empty($managePieceIds)) {
            return true;
        }

        //网点负责人
        $manageStoresIds = $staffServer->getManageStoresList($staffId);
        if (!empty($manageStoresIds)) {
            return true;
        }
        $underManageStaff = (new HrStaffItemsModel())->getOneByValue($staffId, 'MANGER');
        if (!empty($underManageStaff)) {
            return true;
        }

        return false;
    }

    /**
     * 获取外协员工申请权限
     * @param array $paramIn
     * @return boolean
     */
    public function getOSPermission($paramIn = [])
    {
        $jobTitlesData        = UC('outsourcingStaff')['jobTitlesNot'];
        $hubRequestRole       = UC('outsourcingStaff')['osLongPeriodRequestRole'];
        $motorcadeRequestRole = UC('outsourcingStaff')['osMotorcadeRequestRole'];
        $roles                = $this->processingDefault($paramIn, 'positions', 3);
        $job_title            = $this->processingDefault($paramIn, 'job_title', 2);
        $organizationId       = $this->processingDefault($paramIn, 'organization_id', 2);
        $type                 = $this->processingDefault($paramIn, 'organization_type', 2);
        $departmentId         = $this->processingDefault($paramIn, 'department_id', 2);

        $hubOsRoles    = (new SettingEnvServer())->getSetValFromCache('hub_os_roles', ',');

        //hub 外协工单，响应角色的人 有入口权限
        if(array_intersect($hubOsRoles, $roles)) {
            return true;
        }

        if ((!empty($jobTitlesData) || !empty($hubRequestRole)) || !empty($motorcadeRequestRole)) {

            //1-[短期外协申请条件][网点在编人员并且不jobTitlesData崗位才可以申请]
            //2-[长期外协申请条件][hub网点网点经理、区域经理可以申请]['TH02020402','TH02030208'网点正主管][shop project部门所有员工]
            //3-[车队外协申请条件][Line haul & Transportation部门(26)，角色为线路中控人员(31)/线路规划管理员的员工申请(32)]

            $store = (new SysStoreServer())->getStoreByid($organizationId);
            //Malaysia Transportation 319
            if ($departmentId == 319 && array_intersect($roles, array_keys($motorcadeRequestRole))) {
                return true;
            }

            if ($type == 2) { //除了车队外协可以是总部员工申请，其余都是网点员工才能申请
                return false;
            }

            if (!isset($jobTitlesData[$job_title]) || (isset($store) && ($store['category'] == 8 || $store['category'] == 9))) {
                return true;
            }
        }
        return false;
    }


     /**
     * 打卡日历 年月
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function attendanceCalendar($paramIn = [])
    {
        //[1]参数定义
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $years   = $this->processingDefault($paramIn, 'years');

        //[2]查询当月日期
        $date = get_month_days($years);

        //[3]获取打卡信息
        $param       = [
            'staff_id' => $staffId,
            'date'     => $date
        ];

        //新需求 ph日期 根据员工工作日 不同 返回的日期也不同 https://shimo.im/docs/dYHQWXv9VQtpKwTw/read
        $staff_model = new StaffRepository();
        $staff_info  = $staff_model->getStaffPosition($staffId);

        //试用菲律宾项目 ph 数据返回结构需要变更为map
        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $holiday_data = $leave_server->ph_days($staff_info);

        $param['holidays'] = empty($holiday_data) ? [] : array_column($holiday_data,'day');
        $att_server = new AttendanceServer($this->lang,$this->timezone);
        $resultDay             = $att_server->attendanceCalendar($param);
        $resultDay['holidays'] = $holiday_data;

        $returnData = ['data' => $resultDay];
        return $this->checkReturn($returnData);

    }


    /**
     * 请假添加
     * @Access  public
     * @param array $paramIn
     * @return array
     * @throws ValidationException
     *  涉及表 staff_audit 主表 staff_audit_leave_split 拆分表
     */
    public function leaveAdd($paramIn = [])
    {
        //[1]参数定义
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $auditReason    = $this->processingDefault($paramIn, 'audit_reason');
        $imagePathArr   = $this->processingDefault($paramIn, 'image_path');
        $auditReason    = addcslashes(stripslashes($auditReason), "'");

        $db = StaffAuditModel::beginTransaction($this);
        $serialNo  = $this->getRandomId();

        //[2]用户校验
        $staff_model = new StaffRepository();
        $staffData   = $staff_model->getStaffPosition($staffId);
        if (empty($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
        }

        if ($staffData['is_sub_staff'] == 1) {
            return $this->checkReturn(-3, $this->getTranslation()->_('sub_staff_disable'));
        }
        if ($this->checkStaffFormal($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('os_or_franchisee_staff_disable'));
        }
        $leave_day = $paramIn['leave_day'] = $this->conversionTime(array_merge(['type' => 2], $paramIn));

        //请假日期 就是休息日区间 无需申请 假期类型包括 年假,事假,婚嫁,出家假,个人培训假（不带薪）,病假,绝育手术假,公司培训假,家人去世假。
        if ($leave_day == 0)
            return $this->checkReturn(-3, $this->getTranslation()->_('day off for the apply date'));

        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $holidays = $paramIn['holidays'] = $leave_server->staff_off_days($staffId, $leaveStartTime, $leaveEndTime);
        //[3]校验选择时间内是请假记录
        $paramIn['user_info'] = $staffData;
        $checkData = $this->checkLeaveDataByDate($paramIn);
        if (isset($checkData['code']) && $checkData['code'] == 0) {
            return $this->checkReturn(-3, $checkData['msg']);
        }
        //新增验证班次时间二次确认提示 https://flashexpress.feishu.cn/docx/WqJcd6xFNoctnux4qPEcKTZcnkg
        if (empty($paramIn['is_bi']) && empty($paramIn['is_submit'])){
            if(!in_array($staffData['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
                $leave_server->checkShiftLeave($staffId,$paramIn);
            }
        }

        //判断是否跨天 保存 拆分表 staff_audit_leave_split
        $insert_param['leave_type'] = $leaveType;
        $insert_param['start_time'] = $leaveStartTime;
        $insert_param['end_time'] = $leaveEndTime;
        $insert_param['start_type'] = $leaveStartType;
        $insert_param['end_type'] = $leaveEndType;
        $insert_param['holidays'] = $holidays;
        $insert_param['sub_day'] = $this->sub_day;
        $leave_server = new LeaveServer();

        $r = $leave_server->format_leave_insert($staffId,$insert_param);
        if ($r['code'] == 1) {
            $insert = $r['data'];
        } else {
            return $r;
        }

        //如果是年假 需拆分 并且去年年假在有效期内
        {
            if($leaveType == enums::LEAVE_TYPE_13){
                //扣除额度表
                $leave_server->sub_leave_days($staffId,$leave_day);
            }else if($leaveType == enums::LEAVE_TYPE_15  && !empty($paramIn['is_bi'])){//替换轮休 工具操作时候用
                $check_param['operator'] = $paramIn['operator'] ?? 0;
                $check_param['staff_id'] = $staffId;
                $check_param['date'] = date('Y-m-d',strtotime($leaveStartTime));
                $this->leave_for_workday($check_param);
            }else if(in_array($leaveType,$this->one_time)){
                //增加 一次性额度扣减
                $remain_model = new StaffLeaveRemainDaysModel();
                $remain_row['staff_info_id'] = $staffId;
                $remain_row['leave_type'] = $leaveType;
                $remain_row['days'] = 0;
                $remain_row['leave_days'] = $leave_day;
                $flag = $remain_model->create($remain_row);
                $this->getDI()->get("logger")->write_log("leave_one_time {$staffId} {$flag}" . json_encode($remain_row),'info');
            }else if(in_array($leaveType,$this->one_send)){
                //一次性发放额度 先查询 有没有额度 如果没有额度 不让申请 需要在hris 那边初始化
                $remain_info = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                    'bind' => ['staff_info_id' => $staffId,'leave_type' => $leaveType],
                ]);

                //如果没有 说明没初始化成功 提示错误
                if(empty($remain_info))
                    return $this->checkReturn(-3, 'init failed');

                //操作额度
                $remain_info->days -= $leave_day;
                $remain_info->leave_days += $leave_day;
                $remain_info->updated_at = gmdate('Y-m-d H:i:s');
                $remain_info->update();
            }
        }


        $status = 1;
        if (!empty($paramIn['is_bi'])){
            $status = 2;
            $auditReason .= "|system_tool_add";
        }
        $extend['time_out'] = date('Y-m-d 00:00:00',strtotime('+3 day'));

        //[4]请假插入
        try {
            $insetData = [
                'staff_info_id'    => $staffId,
                'leave_type'       => $leaveType,
                'leave_start_time' => $this->assemblyData($leaveStartTime, 1, $leaveStartType),
                'leave_start_type' => $leaveStartType,
                'leave_end_time'   => $this->assemblyData($leaveEndTime, 2, $leaveEndType),
                'leave_end_type'   => $leaveEndType,
                'audit_reason'     => $auditReason,
                'status'           => $status,
                'audit_type'       => enums::$audit_type['LE'],
                'leave_day'        => $leave_day,
                'serial_no'        => (!empty($serialNo) ? 'LE' . $serialNo : NULL),
                'time_out'         => $extend['time_out'],
            ];

            //不同国家 存在 子类型 产假等
            if(!empty($paramIn['sub_type'])){
                $insetData['template_comment'] = json_encode(array("leave_{$leaveType}_key" => intval($paramIn['sub_type'])));
            }
            $db->insertAsDict("staff_audit", $insetData);
            $auditId = $db->lastInsertId();
            if (!$auditId) {
                throw new \Exception("插入staff_audit 失败" . json_encode($insetData, JSON_UNESCAPED_UNICODE));
            }
            if($insert && is_array($insert)) {
                foreach ($insert as &$v) {
                    $v['audit_id'] = $auditId;
                }
                $result = (new BaseRepository())->batch_insert("staff_audit_leave_split", $insert);
                if (!$result) {
                    throw new \Exception("插入staff_audit_leave_split 失败" . json_encode($insert, JSON_UNESCAPED_UNICODE));
                }
            }
            if (empty($paramIn['is_bi'])) {

                $res = (new ApprovalServer($this->lang, $this->timezone))->create($auditId, enums::$audit_type['LE'], $staffId,null, $extend);
                if (!$res) {
                    throw new \Exception('audit staff insert leaveAdd workflow fail' . $res);
                }
            }
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("leaveaddworkflow error ". $e->getMessage() . " " . $e->getTraceAsString());
            $db->rollBack();
            return $this->checkReturn(-3, $this->getTranslation()->_('1009'));
        }


        //插入图片
        if (!empty($imagePathArr)) {
            foreach ($imagePathArr as $k => $v) {
                $insertImgData = [
                    'audit_id'   => $auditId,
                    'image_path' => $v
                ];
                $this->re['audit']->auditImgInsert($insertImgData);
            }
        }

        //请假同步处罚 https://flashexpress.feishu.cn/docx/F4nrdPcwUoMvUQxU17XcH0SWnSe
        if(!empty($paramIn['is_bi'])){
            $sync_server = new SyncServer($this->lang, $this->timezone);
            $params = [
                'staff_id'         => $staffId,
                'leave_start_date' => $insetData['leave_start_time'],//带 时分秒的
                'leave_end_date'   => $insetData['leave_end_time'],
            ];
            $sync_server->sync_fbi_attendance($params, 'abnormal.staff_leave_request');
        }



        $return['data'] = array('leave_day' => $paramIn['leave_day']);
        return $this->checkReturn($return);
    }


    /**
     *
     * 验证请假规则
     * @param array $paramIn
     * @return array
     */
    public function checkLeaveDataByDate($paramIn = [])
    {
        //[1]参数定义 leave_start_time 和 leave_end_time 是 ymd 类型
        $staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $imagePathArr   = $this->processingDefault($paramIn, 'image_path');
        $leave_days = $paramIn['leave_day'];

        $param = [
            'staff_id'         => $staffId,
            'leave_start_time' => date('Y-m-d',strtotime($leaveStartTime)),
            'leave_start_type' => $leaveStartType,
            'leave_end_time'   => date('Y-m-d',strtotime($leaveEndTime)),
            'leave_end_type' => $leaveEndType,
        ];


        //开始时间 结束时间 验证
        if ($leaveEndTime < $leaveStartTime) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
        }
        if ($leaveEndTime == $leaveStartTime) {
            if ($leaveStartType > $leaveEndType) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
            }
        }
        $audit_model = new AuditRepository($this->lang);

        //图片最多限制5张
        if (!empty($imagePathArr) && count($imagePathArr) > 5) {
            return $this->checkReturn(-3, $this->getTranslation()->_('at most 5 photos'));
        }

        //图片必填的类型
        if (in_array($leaveType, $this->need_img) && empty($imagePathArr)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('22328_leave_application'));
        }

        //[3]查询请假记录
        $levelData = $this->checkExistLeave($param);
        if (!empty($levelData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1012'));
        }

        //新增限制 4个假期类型 24年不能申请
        if (in_array($leaveType, [
                enums::LEAVE_TYPE_10,
                enums::LEAVE_TYPE_28,
                enums::LEAVE_TYPE_29,
            ]) && $leaveEndTime >= VacationEnums::LEAVE_TYPE_DATE_CLOSE)
        {
            throw new ValidationException($this->getTranslation()->_('leave_closed_2024'));
        }

        //新增规则 二期需求 https://shimo.im/sheets/94eQG9bXB8oMZlnT/MODOC
        //新增需求 试用期判断 不用固定120天 https://l8bx01gcjr.feishu.cn/docs/doccne9Sx3V9c0YesAU97rFTkBb hr_probation 状态为4的字段
        $leaveType  = intval($leaveType);
        $leave_lang = $audit_model::$leave_type;
        //获取job title name 入职时间
        $staff_model = new StaffRepository($this->lang);
        $staff_info  = $staff_model->getStaffPosition($staffId);
        $hire_date   = $staff_info['hire_date'];
        $entry       = strtotime($staff_info['hire_date']);
        if (empty($staff_info) || empty($hire_date)) {
            return $this->checkReturn(-3, 'no permission to apply');
        }

        //类型验证
        $typeData = $this->staffLeaveType($staff_info);
        if(empty($typeData)){
            throw new ValidationException('wrong leave type');
        }
        if (!in_array($leaveType, array_keys($typeData)) && $leaveType != enums::LEAVE_TYPE_15) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004').' '.$this->getTranslation()->_($leave_lang[$leaveType]));
        }


        $leave_server = new LeaveServer($this->lang,$this->timezone);

        //新增 请假白名单 不限制入职天数 修改为 查看是否入职
        if (in_array($leaveType, $this->forbidden) && $hire_date >= '2020-06-13 00:00:00') {
            //试用期状态，1试用期，2已通过，3未通过，4已转正 适用于 xxx 之后 之前的数据默认都转正 因为没管旧数据
            if($staff_info['status'] != 4 ){
                $message = str_replace('leave_type', $this->getTranslation()->_($leave_lang[$leaveType]), $this->getTranslation()->_('probation_limit'));
                return $this->checkReturn(-3, $message);
            }
            if (!empty($staff_info['formal_at']) && $leaveStartTime < $staff_info['formal_at']) {
                $message = $this->getTranslation()->_('probation_before_limit');
                return $this->checkReturn(-3, $message);
            }

        }

        //19310: 是否可以今天请假，1代表可以申请今天，0代表不能申请今天
        $canleavetoday = (new SettingEnvServer())->getSetVal('canleavetoday');

        //只能申请今天及之后的日期（年假、产假、陪产假、补休假、跨国探亲假、无薪假）
        $leave_limit_date = (new SettingEnvServer())->getSetVal('leave_limit_date', ',');
        //19310 执行新请假规则的部门ID（含子部门），用逗号隔开
        $leave_policy_department = (new SettingEnvServer())->getSetVal('Leave_Policy_Department', ',');
        $departmentIds = (new DepartmentRepository($this->lang))->getDepartmentChildInfo($leave_policy_department);
        $is_leave_policy_department = in_array($paramIn['user_info']['node_department_id'], $departmentIds);
        if(in_array($leaveType, $leave_limit_date) && empty($paramIn['is_bi'])) {
            //请假日期必须大于等于当前日期
            $start_day_leave = !empty($canleavetoday) && $canleavetoday == self::CAN_LEAVE_TODAY ? date('Y-m-d') : date('Y-m-d', strtotime("+1 day"));

            if ($is_leave_policy_department && strtotime($leaveStartTime) < strtotime($start_day_leave) && $start_day_leave == date('Y-m-d')) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1018'));
            }

            if ($is_leave_policy_department && strtotime($leaveStartTime) < strtotime($start_day_leave) && $start_day_leave == date('Y-m-d', strtotime("+1 day"))) {
                return $this->checkReturn(-3, $this->getTranslation()->_('can_leave_today_outweigh'));
            }

            if (!$is_leave_policy_department && in_array($leaveType, $this->time_limit_start) && strtotime($leaveStartTime) < strtotime(date('Y-m-d'))) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1018'));
            }

            //过去8天 到 未来
            //当 不是 新配置部门的 员工，则只可以申请8天前以及未来的日期
            $date_8 = date('Y-m-d', strtotime("-8 day"));
            if (!$is_leave_policy_department && $leaveType == enums::LEAVE_TYPE_1 && strtotime($leaveStartTime) < strtotime($date_8)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_16_notice_ph'));
            }
        }

        if ($leaveType == enums::LEAVE_TYPE_39 && empty($paramIn['is_bi'])) {
            //请假日期必须大于等于当前日期
            $start_day_leave = !empty($canleavetoday) && $canleavetoday == self::CAN_LEAVE_TODAY ? date('Y-m-d') : date('Y-m-d', strtotime("+1 day"));

            if (strtotime($leaveStartTime) < strtotime($start_day_leave) && $start_day_leave == date('Y-m-d')) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1018'));
            }

            if (strtotime($leaveStartTime) < strtotime($start_day_leave) && $start_day_leave == date('Y-m-d', strtotime("+1 day"))) {
                return $this->checkReturn(-3, $this->getTranslation()->_('can_leave_today_outweigh'));
            }
        }


        // 需要先检验 次数 和总额度 是否非法 然后再拆分  一次限制的年假(4,5,6,8,10,11) 去掉 产检一次性限制
        if (in_array($leaveType, $this->one_time)) {//http://193x782t53.imwork.net:29667/zentao/story-view-3112.html
            //验证 员工入职以后 是否请过
            $remain_info = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                'bind' => ['staff_info_id' => $staffId,'leave_type' => $leaveType],
            ]);

            if (!empty($remain_info))
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

            //是否超出 限定 额度
            $limit_days = $audit_model->get_leave_days_by_type($leaveType);
            if (!empty($limit_days) && $leave_days > $limit_days)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
        }
        //不限次数 的 总和 限制 假期 新增假期类型 16 员工培训假 不限次数 限制总和
        if (in_array($leaveType, $this->one_send)) {
            //验证 员工入职以后 是否请过
            $remain_info = StaffLeaveRemainDaysModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                'bind' => ['staff_info_id' => $staffId,'leave_type' => $leaveType],
            ]);

            if (empty($remain_info)) {
                $remain_info = $leave_server->init_leave(['staff_info_id' => $staffId, 'leave_type' => $leaveType]);
            }

            if (empty($remain_info)) {
                return $this->checkReturn(-3, $this->getTranslation()->_("init type {$leaveType} failed "));
            }

            //公司培训假非特殊职位限制额度
            if($leaveType == enums::LEAVE_TYPE_16){
                if($this->checkFleet($staff_info) && ($remain_info->days <= 0 || ($remain_info->days - $leave_days) < 0)){
                    return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
                }
            }else{
                if ($remain_info->days <= 0 || ($remain_info->days - $leave_days) < 0) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
                }
            }
        }

        //拼接 验证逻辑 参数 check_leave_type 额度验证
        $leave_info['leave_type'] = $leaveType;
        $leave_info['img']        = $imagePathArr;
        $leave_info['is_bi']      = empty($paramIn['is_bi']) ? '' : $paramIn['is_bi'];
        $leave_info['sub_type'] = empty($paramIn['sub_type']) ? 0 : $paramIn['sub_type'];
        $leave_info['start_type'] = $leaveStartType;
        $leave_info['end_type'] = $leaveEndType;
        $leave_info['staff_info_id'] = $staffId;

        $leave_info['leave_start_type'] = $leaveStartType;
        $leave_info['leave_end_type'] = $leaveEndType;
        $leave_info['holidays'] = $paramIn['holidays'];
        //本次请假  持续天数 如果跨年 //如果跨年 并且 不是年假 要拆开 每年分别是几天 和 每年的 开始结束时间 因为部分假期类型是按年度区分 今年可以请n天明年仍然可以请n天 还可以一次性跨年请2n天
        //！！新需求  如果部分请假类型其中包含法定假日 等休息日  需要扣除掉 不占用 该请假类型的额度
        //新需求 如果是产假 请跨年 不占用2年额度 按创建时间 每年一次
        if (date('Y', strtotime($leaveStartTime)) != date('Y', strtotime($leaveEndTime))
            && !in_array($leaveType,$this->one_time)
            && !in_array($leaveType,$this->one_send)
            && !in_array($leaveType,$this->by_create_time)) {
            //新需求 拆分时候 如果当天是休息日 剔除掉
            $date_array = $leave_server->format_year_date($leaveType,$leaveStartTime,$leaveEndTime,$paramIn['holidays']);
            $flag       = false;
            foreach ($date_array as $y) {//array('2019' => array('2019-12-30','2019-10-31') )) 有几年就有几个 正常应该就两年 没人能请一整年假期 还干不干了
                $need_days = 0;
                sort($y);
                $leave_info['leave_start_time'] = $y[0];//该年开始 时间
                $leave_info['leave_end_time']   = end($y);//该年 结束时间

                //拆分计算 本次请假 需要天数
                foreach ($y as $date) {
                    if ($date == $leaveStartTime) {
                        $need_days += ($leaveStartType == 1) ? 1 : 0.5;
                    } else if ($date == $leaveEndTime) {//最后一天
                        $need_days += ($leaveEndType == 2) ? 1 : 0.5;
                    } else {//区间内 肯定是一整天
                        $need_days += 1;
                    }
                }
                //验证每年的额度 是否 够用
                $flag = $this->check_leave_type($leave_info, $staff_info, $need_days);
                if ($flag !== true)
                    break;
            }
        } else {
            $need_days                      = $paramIn['leave_day'];
            $leave_info['leave_start_time'] = $leaveStartTime;
            $leave_info['leave_end_time']   = $leaveEndTime;
            $flag                           = $this->check_leave_type($leave_info, $staff_info, $need_days);
        }
        if ($flag !== true)
            return $flag;
        return $this->checkReturn(1);
    }

    /**
     * @throws BusinessException
     */
    protected function leave_for_workday($param)
    {
        //获取员工信息
        $re         = new StaffRepository($this->lang);
        $staff_info = $re->getStaffPosition($param['staff_id']);
        if (empty($staff_info) || $staff_info['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_5) {
            return;
        }

        $operateId = $param['operator'] ?? $param['staff_id'];
        //当天休息日已经被转移
        $isMove = HrStaffWorkDayModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and src_week = :src_week:',
            'bind'       => ['staff_info_id' => $param['staff_id'], 'src_week' => date('oW', strtotime($param['date']))],
        ]);
        if (!empty($isMove)) {
            throw new BusinessException($this->getTranslation()->_('add_off_day_error_1', ['date_at' => $isMove->date_at]));
        }

        //获取轮休信息 get_work_days_between
        $start     = weekStart($param['date']);
        $end       = weekEnd($param['date']);
        $work_days = $re->get_work_days_between($param['staff_id'], $start, $end);
        if (!empty($work_days)) {
            //获取 ph
            $leave_server = new LeaveServer($this->lang, $this->timezone);
            $holiday      = $leave_server->ph_days($staff_info);
            $holiday      = empty($holiday) ? [] : array_column($holiday, 'day');
            //只能存在一个轮休日 如果有多个 排除ph
            $row = [];
            foreach ($work_days as $k => $d) {
                if (in_array($d['date_at'], $holiday) || !empty($d['src_week'])) {
                    unset($work_days[$k]);
                    continue;
                }
                $row = $d;
                break;
            }
        }

        //如果 轮休 日期 有人后来配置了 就跳过
        if (!empty($row)) {
            if($row['date_at'] == $param['date']){
                $this->getDI()->get('logger')->write_log('请休息日 是同一天' . json_encode($row), 'info');
                return;
            }
            //保存 备份 轮休数据 并删除
            $re->del_workday($row['staff_info_id'], $row['date_at']);
            $this->getDI()->get('db')->insertAsDict('work_day_replace', $row);
            $this->getDI()->get('logger')->write_log('请休息日 备份轮休' . json_encode($row), 'info');
            //轮休日志操作记录表 删除
            $logServer          = new WorkdayServer($this->lang, $this->timezone);
            $row['operate_id'] = $operateId;
            $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_CANCEL_OFF, $row);
        }

        //操作覆盖
        $insert['staff_info_id'] = $param['staff_id'];
        $insert['month']         = date('Y-m', strtotime($param['date']));
        $insert['date_at']       = $param['date'];
        $insert['operator']      = $param['staff_id'];
        $insert['remark']        = 'leave replace';
        $insert['type']          = HrStaffWorkDayModel::TYPE_REST;//5天班的已经从上面return了,6天班都是rest day

        $model = new HrStaffWorkDayModel();
        $model->create($insert);
        //轮休日志操作记录表 来新增
        $logServer = new WorkdayServer($this->lang, $this->timezone);
        $insert['operate_id'] = $operateId;
        $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_ADD_OFF, $insert);
        //log
        $this->getDI()->get('logger')->write_log('请休息日 覆盖轮休' . json_encode($insert), 'info');
        return;
    }

    /**
     *请假逻辑
     * 年假 额度 按创建时间计算  按当前时间 获取 今年年假 计算额度
     * 其他假期 如果跨年 先取出 开始年 和额度
     * 然后结束年 和 额度 两次计算 是否合法请假
     * 计算剩余额度 需要 获取 已经请的假期记录 如果存在跨年 需要再次拆分 留下 当前判定年的天数
     *
     * @param $leave_info// 截取后的 请假开始时间和结束时间
     * @param $staff_info
     * @param $need_days //截断后的 请假天数 比如 19年 1天 20年2天 总数是3天 need_days 两次传参 1， 2
     * @return bool
     */
    protected function check_leave_type($leave_info, $staff_info, $need_days)
    {
        $leaveType        = $leave_info['leave_type'];
        $leaveStartTime   = $leave_info['leave_start_time'];
        $leaveEndTime     = $leave_info['leave_end_time'];
        $staffId          = $staff_info['staff_info_id'];
        $audit_model      = new AuditRepository($this->lang);
        $imagePathArr     = $leave_info['img'];
        $paramIn['is_bi'] = $leave_info['is_bi'];
        //额度计算
        //新需求 如果请假天数 跨年 需做新规则处理 如果是年假 按当前 年 计算
        $year  = date('Y', strtotime($leaveStartTime));
        $leave_server = new LeaveServer($this->lang,$this->timezone);
        //新算法 用拆分表
        $cycle_info = array();
        if($leaveType == 1)
        {
            $cycle_info = $leave_server->get_cycle($staff_info);
            $applied_days = $audit_model->get_used_leave_days($staffId, $cycle_info['cycle'], $leaveType);

        }else
            $applied_days = $audit_model->get_used_leave_days($staffId, $year, $leaveType);

        //每种假期的额度
        $limit_days = $audit_model->get_all_leave_days();

        $sum_days             = $count_days = array();
        $sum_days[$leaveType] = $count_days[$leaveType] = 0;
        if (!empty($applied_days)) {
            $info                   = $applied_days[0];
            $sum_days[$leaveType]   = $info['num'];
            $count_days[$leaveType] = empty($info['audit_ids']) ? 0 : count(explode(',', $info['audit_ids']));
        }


        //根据请假类型定制 逻辑
        if ($leaveType == enums::LEAVE_TYPE_3) {//[2]检测日期[结束时间必须大于开始时间] 病假可以选择近三天 bi工具排除
            //带薪病假 按工作年限发放
            $have_days = $leave_server->get_sick_days($staff_info);

            if ($sum_days[$leaveType] >= $have_days || ($need_days + $sum_days[$leaveType]) > $have_days) {
                $notice_msg = $this->getTranslation()->_('leave_limit');
                return $this->checkReturn(-3, $notice_msg);
            }

        }else if($leaveType == enums::LEAVE_TYPE_27){//住院假

            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType]) {
                $notice_msg = $this->getTranslation()->_('leave_limit');
                return $this->checkReturn(-3, $notice_msg);
            }

        }  else if ($leaveType == enums::LEAVE_TYPE_4) {//产假 每年一次
            $check_4 = $leave_server->check_create_year($staffId, $leaveType);
            if (!$check_4) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }

            if ($need_days > $limit_days[$leaveType]) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }

        } else if ($leaveType == 5) {//陪产假 一次
            $check_5 = $leave_server->check_create_year($staffId, $leaveType);
            if (!$check_5) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }
            //申请权限 员工类型
            $hirePermission = $leave_server->hireTypeCheck($staff_info);
            if(!$hirePermission){
                return $this->checkReturn(-3, $this->getTranslation()->_('hire_type_notice'));
            }
            //是否满一年
            $yearPermission = $leave_server->overOneYear($staff_info);
            if(!$yearPermission){
                return $this->checkReturn(-3, $this->getTranslation()->_('over_one_year_notice'));
            }

            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType]) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }

        } else if ($leaveType == 10) {//婚假 一次 不超过3天

        } //不带薪事假 无限制
        else if ($leaveType == 12) {
        }
        else if ($leaveType == 13) {//补休假 固化额度
            //新需求 补休假 换数据表了
            $return    = $leave_server->get_remain_days($staffId);
            $have_days = ($return['in_all'] - $return['used']) > 0 ? $return['in_all'] - $return['used'] : 0;
            if ($need_days > $have_days) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }

            //新需求 请假 开始日期 计算额度
            $return    = $leave_server->get_remain_days($staffId, 0, date('Y-m-d', strtotime($leaveStartTime)));
            $have_days = ($return['in_all'] - $return['used']) > 0 ? $return['in_all'] - $return['used'] : 0;
            if ($need_days > $have_days) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit_13'));
            }

        }
        else if ($leaveType == enums::LEAVE_TYPE_16) {//公司培训假
            $trainingServer             = new TrainingServer($this->lang, $this->timezone);
            $trainingServer->staffInfo  = $staff_info;
            $trainingServer->paramModel = $leave_info;
            if ($this->checkFleet($staff_info) && $trainingServer->checkTrainingLeave()) {
                return $this->checkReturn(-3, $this->getTranslation()->_('training_leave_notice'));
            }
        } else if ($leaveType == 15) {//休息日 可以申请前天、昨天、今天、明天、后天。
            //5天员工不能申请
            if (empty($staff_info['week_working_day']) || $staff_info['week_working_day'] == 5) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }
            //验证是否能请
            $check_rest = $leave_server->get_rest_times($staff_info, $leave_info);
            if ($check_rest['code'] != 1) {
                return $this->checkReturn(-3, $check_rest['msg']);
            }
            $times = $check_rest['data'];
            if ($times >= 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_for_five'));
            }

            //新需求 休息日 只能是1天 大于小于都不行 https://shimo.im/docs/kv6rgP3GXDT69wdc/read
            if ($need_days < 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_for_half'));
            }

            if ($need_days > 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));
            }
        } else if($leaveType == 28){//体恤假
            //过去8天 到 未来
            $date_8 = date('Y-m-d', strtotime("-8 day"));
            if (empty($paramIn['is_bi']) && $leaveStartTime < $date_8) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_16_notice_ph'));
            }
            if ($need_days < 1)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_for_half'));

            if ($need_days > 1)
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));


        } else if($leaveType == 29){//考试假 2天
//            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
//                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));

        }else if($leaveType == enums::LEAVE_TYPE_37){//灾难假
            //过去8天 到 未来
            $date_8 = date('Y-m-d', strtotime("-8 day"));
            if (empty($paramIn['is_bi']) && ($leaveStartTime < $date_8)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_16_notice_ph'));
            }

            if ($sum_days[$leaveType] >= $limit_days[$leaveType] || ($need_days + $sum_days[$leaveType]) > $limit_days[$leaveType])
                return $this->checkReturn(-3, $this->getTranslation()->_('leave_limit'));


            if(count($imagePathArr) > 5)
                return $this->checkReturn(-3, $this->getTranslation()->_('image_cannot_empty_at_most_5_photos'));

        }else if ($leaveType == enums::LEAVE_TYPE_39) {
            if(!in_array( $staff_info['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
                return $this->checkReturn(-3, $this->getTranslation()->_('jobtransfer_0004'));
            }

        } else {//非法请求
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        return true;
    }


//    /**
//     * 1:年假
//    2:带薪事假
//    3:病假
//    27 住院假
//    4:产假
//    5:陪产假
//    28 体恤假
//    10:婚假
//    29 考试假
//    15:休息日
//    16:公司培训假
//    19 跨国探亲假
//    13 补休假
//    12:不带薪事假
//
//     * @param array $paramIn
//     * @return array
//     *
//     */
//    public function getTypeBook($paramIn = [])
//    {
//        $type = $this->processingDefault($paramIn, 'type', 2, 1);
//        switch ($type) {
//            case 1;
//                $data = $this->getReissueType($paramIn);
//                $returnData['data']['need_img'] = false;//补卡 是否需要图片字段
//                break;
//            case 2;//新增是否带薪属性  1-带薪 2-不带薪
//                $data = $this->type_book();
//                break;
//            default:
//                $data = [
//
//                ];
//                break;
//        }
//
//        $returnData['data']['dataList'] = $data;
//        //新增个人代理字段
//        $info = HrStaffInfoModel::findFirst("staff_info_id = {$paramIn['user_info']['id']}");
//        $returnData['data']['hire_type'] = empty($info) ? 0 :$info->hire_type;
//        return $this->checkReturn($returnData);
//    }

    //补卡类型 动态获取
    public function getReissueType($param = []){
        //马来 补卡 没传日期 不返回下拉选项
        if(empty($param['reissue_date']) || empty($param['user_info']['id'])){
            return [];
        }
        //获取用户信息
        $today = date('Y-m-d');
            //判断日期是否小于今天
        if(strtotime($param['reissue_date']) < strtotime($today)){
            //从 transfer表取数据
            $staffInfo = HrStaffTransferModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and stat_date = :date_at:',
                'bind' => ['staff_id' => $param['user_info']['id'], 'date_at' => $param['reissue_date']]
            ]);
            $staffInfo = empty($staffInfo) ? [] : $staffInfo->toArray();
        }else{
            $staffRe = new StaffRepository($this->lang);
            $staffInfo = $staffRe->getStaffPosition($param['user_info']['id']);
        }
        //获取对应员工 日期的班次类型 shift type
        $attendanceServer = new AttendanceServer($this->lang,$this->timezone);
        $this->shiftInfo = $shiftInfo = $attendanceServer->getStaffShiftInfoByDate($param['user_info']['id'],$param['reissue_date']);
        $liveJobId = (new SettingEnvServer())->getSetVal('free_shift_position', ',');
        $this->isLiveJob = in_array($staffInfo['job_title'], $liveJobId);
        //主播可以没班次
        if(empty($shiftInfo) && !$this->isLiveJob){
            throw new ValidationException($this->getTranslation()->_('no_shift_notice'));
        }

        $is_unpaid = in_array( $staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether);
        $key1 = '2001';
        $key2 = '2002';
        $suffix = '';
        if($is_unpaid){
            $key1 = 'up_unpaid';
            $key2 = 'down_unpaid';
            $suffix = '_unpaid';
        }

        $shiftType = $shiftInfo['shift_type'];
        $reissueMap = [
            //一天 一次 上下班
            HrShiftV2ExtendModel::SHIFT_TYPE_ONCE => [
                [
                    'code' => '1',
                    'msg'  => $this->getTranslation()->_($key1)
                ],
                [
                    'code' => '2',
                    'msg'  => $this->getTranslation()->_($key2)
                ]
            ],
            //一天两次 上下班
            HrShiftV2ExtendModel::SHIFT_TYPE_TWICE => [
                [
                    'code' => '1',
                    'msg'  => $this->getTranslation()->_('reissue_first_in' . $suffix)
                ],
                [
                    'code' => '2',
                    'msg'  => $this->getTranslation()->_('reissue_first_out' . $suffix)
                ],
                [
                    'code' => '3',
                    'msg'  => $this->getTranslation()->_('reissue_second_in' . $suffix)
                ],
                [
                    'code' => '4',
                    'msg'  => $this->getTranslation()->_('reissue_second_out' . $suffix)
                ]
            ]
        ];
        //主播 固定 单班次
        if($this->isLiveJob){
            return $reissueMap[HrShiftV2ExtendModel::SHIFT_TYPE_ONCE];
        }

        return $reissueMap[$shiftType] ?? [];
    }

    public function type_book($locale = ''){
        //24年1月 关闭一些类型
        $close = date('Y-m-d') >= VacationEnums::LEAVE_TYPE_DATE_CLOSE;

        $data[] = [
            'code' => '1',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2003')
        ];
        $data[] = [
            'code' => '3',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2005')
            ,'need_img' => 1
        ];
        $data[] = [
            'code' => '27',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('leave_27')
            ,'need_img' => 1
        ];
        $data[] = [
            'code' => '4',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2006')
        ];
        $data[] = [
            'code' => '5',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2007')//'陪产假',
            ,'need_img' => 1
        ];
        if(!$close){
            $data[] = [
                'code' => '28',
                'type' => 1,
                'msg'  => $this->getTranslation($locale)->_('leave_28')
                ,'need_img' => 1
            ];
            $data[] = [
                'code' => '10',
                'type' => 1,
                'msg'  => $this->getTranslation($locale)->_('2012')
            ];
            $data[] = [
                'code' => '29',
                'type' => 1,
                'msg'  => $this->getTranslation($locale)->_('leave_29')
            ];
        }
        $data[] = [
            'code' => '15',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2017')
        ];
        $data[] = [
            'code' => '16',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2018')
        ];
        $data[] = [
            'code' => '19',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2022')
        ];

        $data[] = [
            'code' => '13',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('2015')
        ];

        $data[] = [
            'code' => '12',
            'type' => 2,
            'msg'  => $this->getTranslation($locale)->_('2014')
        ];
        $data[] = [
            'code' => '37',
            'type' => 1,
            'msg'  => $this->getTranslation($locale)->_('leave_37')
            ,'need_img' => 1
        ];

        $data[] = [
            'code' => '39',
            'type' => 2,
            'msg'  => $this->getTranslation($locale)->_('leave_39')
        ];
        //新增 国民假期
        $data[] = [
            'code' => (string)enums::LEAVE_TYPE_42,
            'type' => 0,
            'msg'  => $this->getTranslation($locale)->_('leave_42'),
            'need_img' => 1,
        ];
        return $data;
    }

    /**
     * 获取剩余假期天数
     * @param $param
     * day_limit 总额度 分母
     * day_sub  剩余额度 分子
     */
    public function get_left_holidays($param)
    {
        //by 显示额度的类型
        $showType = [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_13, enums::LEAVE_TYPE_19, enums::LEAVE_TYPE_37];

        //hcm 工具获取所有假期额度
        if (!empty($param['is_svc']) && $param['is_svc'] == 1) {
            $data = $this->rpcHolidaysNum($param);
            return $this->checkReturn(['data' => $data]);
        }

        $staff_id = intval($param['staff_id']);
        //所有假期类型
        $data = $this->type_book();
        //根据员工属性 获取对应权限类型
        $data = $this->staffLeaveType($param['user_info'], $data);


        //37 类型还没固化 要计算
        $countType    = '37';
        $year         = date('Y', time());
        $audit_model  = new AuditRepository($this->lang);
        $applied_days = $audit_model->get_used_leave_days($staff_id, $year, $countType);
        $sum_days     = array_column($applied_days, null, 'leave_type');
        //获取所有 假期限制 额度
        $limit_array = $audit_model->get_all_leave_days();

        //是否可以今天请假，1代表可以申请今天，0代表不能申请今天
        $canleavetoday = (new SettingEnvServer())->getSetVal('canleavetoday');
        //只能申请今天及之后的日期（年假、产假、陪产假、补休假、跨国探亲假、无薪假）
        $leave_limit_date = (new SettingEnvServer())->getSetVal('leave_limit_date', ',');
        //19310 执行新请假规则的部门ID（含子部门），用逗号隔开
        $leave_policy_department = (new SettingEnvServer())->getSetVal('Leave_Policy_Department', ',');
        $departmentIds = (new DepartmentRepository($this->lang))->getDepartmentChildInfo($leave_policy_department);
        $is_leave_policy_department = in_array($param['user_info']['node_department_id'], $departmentIds);

        $leave_server = new LeaveServer($this->lang, $this->timezone);
        foreach ($data as $k => &$da) {
            $type = intval($da['code']);
            //by 入口删除 工具留着
            if($type == enums::LEAVE_TYPE_37){
                unset($data[$k]);
                continue;
            }
            //需要在请假页面显示天数的假期类型 1
            if (in_array($type, [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_19])) {//改版后调用
                $da              = array_merge($da, $leave_server->getVacationDays($staff_id, $type));
            }
            if ($type == enums::LEAVE_TYPE_37) {//马来定制 灾难假 显示额度
                $da['day_limit'] = $limit_array[$type] ?? 0;//分母 应有总额度
                $da['day_sub']   = $da['day_limit'] - ($sum_days[$type]['num'] ?? 0);//分子 今年剩余额度

            }

            if ($type == enums::LEAVE_TYPE_13) {//马来 补休额度 要显示出来
                //换算法了
                $return = $leave_server->get_remain_days($staff_id);
                if (!empty($return['in_all'])) {
                    $da['day_limit'] = $return['in_all'];//总额度 分母
                    $da['day_sub']   = $return['in_all'] - $return['used'];//剩余天数 分子
                    $da['day_sum']   = $return['used'];//已经请的天数 没啥用
                }
            }

            if (in_array($type, $showType)) {
                //改类型 前端用
                $da['day_limit'] = empty($da['day_limit']) ? '0' : (string)$da['day_limit'];
                $da['day_sub']   = empty($da['day_limit']) ? '0' : (string)$da['day_sub'];
            }

            //仅能选择今天之后的日期（canleavetoday=0不含今天）
            if($type == enums::LEAVE_TYPE_39) {
                $da['start_day_leave'] = !empty($canleavetoday) && $canleavetoday == self::CAN_LEAVE_TODAY ? date('Y-m-d') : date('Y-m-d', strtotime("+1 day"));
            }

            if($is_leave_policy_department && in_array($type, $leave_limit_date)) {
                $da['start_day_leave'] = !empty($canleavetoday) && $canleavetoday == self::CAN_LEAVE_TODAY ? date('Y-m-d') : date('Y-m-d', strtotime("+1 day"));
            }

            //过去8天 到 未来
            //当 不是 新配置部门的 员工，年假 则只可以申请8天前以及未来的日期
            $date_8 = date('Y-m-d', strtotime("-8 day"));
            if (!$is_leave_policy_department && $type == enums::LEAVE_TYPE_1) {
                $da['start_day_leave'] = $date_8;
            }

            if (!$is_leave_policy_department && $type == enums::LEAVE_TYPE_19) {
                $da['start_day_leave'] = date('Y-m-d');
            }
        }
        $data = $leave_server->explain_read_flag($param['user_info'], $data);
        return $this->checkReturn(['data' => $data]);
    }


    public function rpcHolidayBody($permissionData, $leave_server){
        foreach ($permissionData as $k => &$da) {
            $type = intval($da['code']);
            //额度显示 需要排除的 类型
            if (in_array($type, [enums::LEAVE_TYPE_15, enums::LEAVE_TYPE_25, enums::LEAVE_TYPE_26])) {
                unset($permissionData[$type]);
                continue;
            }

            //需要在请假页面显示天数的假期类型 1 和类型2
            if (in_array($type, $this->newVersionType)) {//改版后调用
                $da              = array_merge($da, $leave_server->getVacationDays($this->staffId, $type, ['is_svc' => 1]));
                continue;
            }
            if ($type == enums::LEAVE_TYPE_3) {//病假 特殊规则
                $da['day_limit'] = $leave_server->get_sick_days($this->staffInfo);
                $da['day_sub']   = $da['day_limit'] - ($this->sum_days[$type]['num'] ?? 0);//分子 今年剩余额度
                continue;
            }
            if ($type == enums::LEAVE_TYPE_13) {//马来 补休额度
                //换算法了
                $return = $leave_server->get_remain_days($this->staffId);
                $da['day_limit'] = 0;
                $da['day_sub']   = 0;
                if (!empty($return['in_all'])) {
                    $da['day_limit'] = $return['in_all'];//总额度 分母
                    $da['day_sub']   = $return['in_all'] - $return['used'];//剩余天数 分子
                }
                continue;
            }

            //一次性假期
            if (!empty($this->oneTypes) && in_array($type, $this->oneTypes)) {
                $da['day_limit'] = $da['day_sub'] = $this->limit_array[$type] ?? 0;//分母 应有总额度
                //one time 类型 看有没有 没有就是没申请过 是全额
                if (in_array($type, $this->one_time) && !empty($this->remainData[$type])) {
                    $da['day_sub'] = 0;
                }
                if (in_array($type, $this->one_send) && !empty($this->remainData[$type])) {
                    $da['day_sub'] = half_num($this->remainData[$type]['days']);
                    $da['day_sub'] = ($da['day_sub'] < 0) ? 0 : $da['day_sub'];
                }
                continue;
            }

            if (!in_array($type, $this->limit_types)) {
                $da['text'] = 'no_limit';//没有 unset掉 并且 额度表没类型 说明 是不限制额度
            }

            //需要计算的类型 还没改版
            $da['day_limit'] = $this->limit_array[$type] ?? 0;//分母 应有总额度
            $sum             = empty($this->sum_days[$type]['num']) ? 0 : $this->sum_days[$type]['num'];//已用的 前端没用 不限时了
            $da['day_sub']   = $da['day_limit'] - $sum;//分子 今年剩余额度

            if ($da['day_sub'] < 0) {
                $da['day_sub'] = 0;
            }
        }
        return $permissionData;
    }

    //根据员工工号属性 获取对应能申请的假期类型
    public function staffLeaveType($staff_info,$data = []){
        if(empty($data)){
            $data = $this->type_book();
        }
        $leave_server = new LeaveServer($this->lang, $this->timezone);
        //新增权限判断
        $permission = $leave_server->leavePermission($staff_info);
        if(!$permission){
            return [];
        }
        //实习生类型
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_INTERN){
            $data = $this->practise_type_book();
        }

        //外协类型
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_0){
            $data = $this->os_type_book();
        }

        //无底薪员工 && 兼职个人代理
        if(in_array($staff_info['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
            $data = $this->hire_un_paid_book();
        }

        $data = array_column($data, null, 'code');

        if (empty($staff_info['sex']) ||  $staff_info['sex'] == 1) {
            //男性过滤掉 产检，产假 和女性特殊假
            unset($data[4]);
        } else {
            //女性过滤掉 陪产假，国家军训假，出家假
            unset($data[5]);
        }
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_INTERN){
            unset($data[5]);
        }
        //如果没满一周年 也过滤陪产假
        $oneYear = $leave_server->overOneYear($staff_info);
        if(!$oneYear){
            unset($data[5]);
        }

        unset($data[15]);

        // 国籍与工作所在地不一致的员工， 会有跨国探亲假 如果数据表没有 触发发放操作
        $nationServer = new InternationalServer($this->lang,$this->timezone);
        if(!$nationServer->applyPermission($staff_info)){
            unset($data[19]);
        }
        $militaryServer = new MilitaryServer($this->lang,$this->timezone);
        if(!$militaryServer->applyPermission($staff_info)){
            unset($data[42]);
        }

        //不是 无底薪类型 && 兼职个人代理 删除 39
        if(!in_array($staff_info['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
            unset($data[39]);
        }


        return $data;
    }


    /**
     * 换算时间
     * 新需求 需根据员工属性 扣除休息日 和ph  6天-找轮休  5天 找周六日
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function conversionTime($paramIn = [])
    {
        //[1]参数定义
        //leave type 1 上午 2 下午
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $type           = $this->processingDefault($paramIn, 'type', 2, 1);//类型 1外部调用 2内部调用
        $staff_id       = $paramIn['staff_id'];


        //[2]换算时间
        if ($leaveEndTime < $leaveStartTime || ($leaveEndTime == $leaveStartTime) && $leaveEndType < $leaveStartType) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
        }
        $leave_day = 0;
        $key       = $start_date = date('Y-m-d', strtotime($leaveStartTime));
        $end_date  = date('Y-m-d', strtotime($leaveEndTime));
        //获取该员工 请假区间的 休息日 和 ph
        $leave_server = new LeaveServer($this->lang, $this->timezone);
        $holidays     = $leave_server->staff_off_days($staff_id, $leaveStartTime, $leaveEndTime);
        while ($key <= $end_date) {
            if (in_array($key, $holidays) && in_array($leaveType, $this->sub_day)) {
                $key = date("Y-m-d", strtotime("+1 day", strtotime($key)));
                continue;
            }
            $add = 1;
            if ($key == $start_date && $leaveStartType != 1) {
                $add = 0.5;
            }
            if ($key == $end_date && $leaveEndType != 2) {
                $add = 0.5;
            }

            $leave_day += $add;
            $key       = date("Y-m-d", strtotime("+1 day", strtotime($key)));
        }

        if ($type == 1) {
            $returnData['data']['day'] = $leave_day;
            return $this->checkReturn($returnData);
        }
        return $leave_day;
    }
    //泰国 根据员工 工作日 不同 返回不同 ph 返回 map list
    public function get_holidays($staff_info)
    {

        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $data = $leave_server->ph_days($staff_info);
        if(!empty($data))
            return array_column($data,'day');
        return array();

    }

    public function getStaffInfoByStaffId($staffId){
        if(empty($staffId)){
            return [];
        }
        $staffInfo = HrStaffInfoModel::findFirst(['conditions' => "staff_info_id = :staff_info_id:", 'bind' => ['staff_info_id' => $staffId]]);
        return !$staffInfo ? [] : $staffInfo->toArray();
    }



    //网点申请支援权限
    public function getSASPermission($paramIn) {
        //1. 申请职位权限：District Manager、Area Manager、DC Supervisor；取数：OA组织架构的部门负责人
        $staffInfoId = $paramIn['staff_id'];
        $manageStore = SysStoreModel::find([
            'conditions' => 'manager_id = :staff_id: and state = 1 and category IN (1,10,13)',
            'bind'       => ['staff_id' => $staffInfoId],
        ])->toArray();

        $manageRegionAll = SysManageRegionModel::find([
            'conditions' => 'deleted = 0 and type = 1 '
        ]);
        $region_arr = $manageRegionAll->toArray();
        $region_ids = array_column($region_arr, 'id');
        $region_manager_ids = array_column($region_arr, 'manager_id');

        //2-片区负责人
        $managePiece = SysManagePieceModel::find([
            'conditions' => 'manager_id = :staff_id: and deleted = 0 and manage_region_id in ({manage_region_ids:array})',
            'bind'       => ['staff_id' => $staffInfoId, 'manage_region_ids' => $region_ids],
        ])->toArray();

        //3-大区负责人
        if(in_array($staffInfoId, $region_manager_ids)) {
            $manageRegion = true;
        } else {
            $manageRegion = false;
        }

        return !empty($manageStore) || !empty($managePiece) || $manageRegion;
    }

    //员工申请支援网点权限
    public function getSASSPermission($paramIn) {
        //1. Tricycle Courier、Van Courier、Bike Courier、DC Supervisor：只能看到和提交对应职位的支援申请
        $job_title = $this->processingDefault($paramIn, 'job_title', 2);
        if(in_array($job_title, [
            enums::$job_title['van_courier'],
            enums::$job_title['bike_courier'],
            enums::$job_title['car_courier'],
            enums::$job_title['branch_supervisor'],
            enums::$job_title['regional_manager'],
            enums::$job_title['district_manager'],
            enums::$job_title['dc_officer'],
            enums::$job_title['assistant_branch_supervisor'],
        ])) {
            return true;
        }
        return false;
    }


    /**
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return array|true
     * @throws \Exception
     */
    public function setATProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            // 是最终审批
            $auditDetail = StaffAuditModel::findFirst([
                'conditions' => ' audit_id = :audit_id: ',
                'bind'       => ['audit_id' => $auditId]
            ]);
            if (empty($auditDetail)) {
                throw new \Exception('audit info error');
            }
            $auditDetail = $auditDetail->toArray();
            if (isset(self::$paramIn['staff_id'])) {
                $staff = HrStaffInfoModel::findFirst([
                    'conditions' => ' staff_info_id = :staff_id: ',
                    'bind'       => ['staff_id' => self::$paramIn['staff_id']]
                ]);
                if ($staff) {
                    $staff = $staff->toArray();
                }
            }
            $updateData = [
                'status'        => $state,
                'audit_id'      => $auditId,
                'reject_reason' => self::$paramIn['reject_reason'] ?? '',
                'approver_id'   => self::$paramIn['staff_id'] ?? 0,
                'approver_name' => isset($staff) && $staff ? $staff['name'] : ''
            ];
            $auditEditFlag = $this->re['audit']->auditEditStatus($updateData);
            if (!$auditEditFlag) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1014'));
            }
            //回调修改打卡时间
            if ( $state == enums::$audit_status['approved']) {
                $attendanceServer = new AttendanceServer($this->lang,$this->timezone);
                $attendanceServer->saveReissueCard($auditDetail);
            }
        }

        return true;
    }

    /**
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return array|mixed|true
     * @throws \Exception
     */
    public function setLEProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            // 是最终审批
            $info = StaffAuditModel::findFirst([
                'conditions' => ' audit_id = :audit_id: ',
                'bind' => ['audit_id' => $auditId]
            ]);
            if(empty($info)){
                throw new \Exception('audit info error');
            }
            $auditDetail = $info->toArray();
            if (isset(self::$paramIn['staff_id'])) {
                $staff = HrStaffInfoModel::findFirst([
                    'conditions' => ' staff_info_id = :staff_id: ',
                    'bind' => ['staff_id' => self::$paramIn['staff_id']]
                ]);
                if ($staff) {
                    $staff = $staff->toArray();
                }
            }
            if (!$extend['super']){
                $state = $this->transformState($state,$extend['is_cancel'] ?? 0);
            }
            //更新业务表
            $info->status   = $state;
            $info->audit_id = $auditId;
            if ($state == enums::$audit_status['dismissed']) {
                $info->reject_reason = self::$paramIn['reject_reason'] ?? '';
            }
            $info->approver_id   = self::$paramIn['staff_id'] ?? 0;
            $info->approver_name = isset($staff) && $staff ? $staff['name'] : '';

            //马来国民假 修改子状态
            if ($state == enums::$audit_status['approved'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_42 && $auditDetail['sub_status'] == StaffAuditModel::MILITARY_SUB_STATUS_HAS_NOT) {
                $info->sub_status = StaffAuditModel::MILITARY_SUB_STATUS_APPROVED;
            }
            $info->update();

            //获取员工信息
            $staff_model = new StaffRepository($this->lang);
            $staff_info = $staff_model->getStaffPosition($auditDetail['staff_info_id']);

            //审核操作 休息日
            if($auditDetail['leave_type'] == enums::LEAVE_TYPE_15){
                if($state == enums::$audit_status['approved']){//审核通过 操作 休息日周是否有轮休 需要替换
                    $check_param['staff_id'] = $auditDetail['staff_info_id'];
                    $check_param['date'] = date('Y-m-d',strtotime($auditDetail['leave_start_time']));
                    $this->leave_for_workday($check_param);
                }else{
                    $ext_server = new AuditExtendServer($this->lang,$this->timezone);
                    $del_param['staff_info_id'] = $auditDetail['staff_info_id'];
                    $del_param['date_at'] = date('Y-m-d',strtotime($auditDetail['leave_start_time']));
                    $del_param['operate_id'] = $extend['operate_id'] ?? 0;
                    $ext_server->cancel_for_leave($del_param);
                }
            }
            //马来 补休假 如果 非审核通过的最终审批 需返还对应额度
            if($state != enums::$audit_status['approved'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_13){
                $leave_server = new LeaveServer($this->lang,$this->timezone);
                $leave_server->re_back_leave_days($auditDetail);
            }
            //一次性的假期 one time  one send 需要操作staff_leave_remaining_days 表
            if($state != enums::$audit_status['approved']){
                if(in_array($auditDetail['leave_type'],$this->one_time)){
                    //delete remain
                    StaffLeaveRemainDaysModel::find([
                        'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                        'bind' => ['staff_info_id' => $auditDetail['staff_info_id'], 'leave_type' => $auditDetail['leave_type']]
                    ])->delete();
                }
                if(in_array($auditDetail['leave_type'],$this->one_send)){
                    //update remain
                    $remain_info = StaffLeaveRemainDaysModel::findFirst([
                        'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                        'bind' => ['staff_info_id' => $auditDetail['staff_info_id'], 'leave_type' => $auditDetail['leave_type']]
                    ]);
                    if(!empty($remain_info)){
                        $leave_days = $auditDetail['leave_day'];
                        $remain_info->days += $leave_days;
                        $remain_info->leave_days -= $leave_days;
                        $remain_info->updated_at = gmdate('Y-m-d');
                        $remain_info->update();
                    }
                }

                //通用返还额度
                if(in_array($auditDetail['leave_type'],[enums::LEAVE_TYPE_1,enums::LEAVE_TYPE_19,enums::LEAVE_TYPE_4])){

                    $leave_server = new LeaveServer($this->lang, $this->timezone);
                    $leave_server->cancelVacation($auditDetail,$staff_info,$state);
                }
            }

            //请假补卡审批通过同步bi，重算处罚数据 https://flashexpress.feishu.cn/docx/F4nrdPcwUoMvUQxU17XcH0SWnSe
            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                //请假
                $sync_server = new SyncServer($this->lang, $this->timezone);
                $params = [
                    'staff_id'         => $auditDetail['staff_info_id'],
                    'leave_start_date' => $auditDetail['leave_start_time'],
                    'leave_end_date'   => $auditDetail['leave_end_time'],
                ];
                $sync_server->sync_fbi_attendance($params, 'abnormal.staff_leave_request');
            }
            //驳回 超时 发消息push 个人代理翻译不一样
            $flag = in_array($staff_info['hire_type'],HrStaffInfoModel::$agentTypeTogether);
            $this->sendMessage($auditDetail,$state, $flag);
        }

        return true;
    }

    /**
	 * @description:获取销售CRM权限
	 * @doc https://l8bx01gcjr.feishu.cn/docs/doccn3Er0YCKSRbk6QGYhFQJRDk
	 * @param $paramIn
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2022/3/10 14:00
	 */

	public function getSalesCRMPermission($paramIn): bool
	{
		$result = false;
		$staffId       = $paramIn["staff_id"] ?? '';
		$department_id = $paramIn["department_id"] ?? '';
		$cache_key = 'get_sales_CRM_permission_'.$staffId.'_'.$department_id;
		$cache_dep_key = 'get_sales_CRM_permission_dep_ids'; //部门id 列表
		$cache_time = 60*10; //缓存时间
		$cache = $this->getDI()->get('redis');
		try {
			$SettingEnvServer = new SettingEnvServer();
			$staff_env_code = 'crm_white_list'; // 白名单用户
			$dep_env_code = 'crm_dep_white_list'; //权限部门

			if (empty($staffId)) {
				throw new \Exception('crm 权限 没有用户 id 和部门 id ');
			}

			//优先读取缓存
			$cache_result = $cache->get($cache_key);
			if(!empty($cache_result)){
				return $cache_result == 2 ? true : false;
			}

			//CRM 白名单 crm_white_list
			$staff_env = $SettingEnvServer->getSetVal($staff_env_code);
			if (!empty($staff_env) && in_array($staffId, explode(',', $staff_env))) {
				$result = true;
			}

			if (empty($department_id)) {
				throw new ValidationException('crm 权限 没有用户 id 和部门 id ');
			}
			if(!$result) {
				//读缓存
				$dep_ids = $cache->get($cache_dep_key);
				if (empty($dep_ids)) {

					//获取权限部门 crm_dep_white_list
					$dep_env = $SettingEnvServer->getSetVal($dep_env_code);
					if (empty($dep_env)) {
						throw new ValidationException('crm 权限 没有权限部门配置 ');
					}
					$dep_env = explode(',', $dep_env);
					//获取部门
					$all_department_ids = SysDepartmentModel::find([
						                                               'conditions' => ' id in ({ids:array}) and deleted = 0',
						                                               'bind'       => ['ids' => $dep_env],
						                                               'columns'    => ['id', 'ancestry_v3']
					                                               ])->toArray();
					$dep_ids            = [];
					//拼接部门 id数据
					$model = new FleSysDepartmentModel();
					foreach ($all_department_ids as $k => $v) {
						//获取子部门
						$subsidiary_dept_Ids = $model->getSpecifiedDeptAndSubDept($v['id']);
						//获取子部门
						$dep_ids = array_merge($dep_ids, $subsidiary_dept_Ids);
					}
					//增加缓存
					$cache->save($cache_dep_key, json_encode($dep_ids, JSON_UNESCAPED_UNICODE), $cache_time); //缓存 10 分钟
				} else {
					$dep_ids = json_decode($dep_ids, true);
				}
				$result = in_array($department_id, $dep_ids) ? true : false;
			}
		}catch (ValidationException $e){
			// 有些员工在 hr_staff_info 不存在 如 31858 并且没有部门 id
			$this->logger->write_log("getSalesCRMPermission 获取销售CRM权限 ValidationException " . json_encode($paramIn) . $e->getMessage(),'info');

		}catch (\Exception $e) {
			$this->logger->write_log("getSalesCRMPermission 获取销售CRM权限 error " . json_encode($paramIn) . json_encode([
				                                                                                                        'Err_Msg' => $e->getMessage(),
				                                                                                                        'Err_Line' => $e->getLine(),
				                                                                                                        'Err_File' => $e->getFile(),
				                                                                                                        'Err_Code' => $e->getCode(),
			                                                                                                        ], JSON_UNESCAPED_UNICODE),'error');
			$result = false;
		}
		//增加缓存
		$cache->save($cache_key, ($result ? 2 : 1), $cache_time); //缓存 10 分钟
		return  $result ;
	}

    /**
     * 检测员工商城权限
     * @param array $paramIn
     * @return boolean
     */
    public function getInteriorOrdersPermission($paramIn = [])
    {
        if (!isCountry() && !isCountry('MY') && !isCountry('PH') && !isCountry('LA')) {
            return false;
        }
        //外协的屏蔽这个 https://l8bx01gcjr.feishu.cn/docs/doccn5r1mVUUC3BpBcSBIS4RjNb
        /**
         * 10630【MY-BY/KIT|员工商城】 马来员工商城对接FlashPay
         * 需求文档： https://l8bx01gcjr.feishu.cn/docs/doccnFBQ3fn1GHAs28I5M3PJEzc
         * 由于马来对接flashpay在线支付，需要在正式环境测试下pay的支付流程
         * 实现方案参考文档方案二： https://l8bx01gcjr.feishu.cn/docs/doccnlyWkfVN7zWNnUVS7yTBjub
         * 设定工号则部分工号才能看到商城入口，如果没有设定则全员开放
         */
        $staff_info_id = $paramIn['staff_id'];
        $setting_env = new SettingEnvServer();
        $set_val = $setting_env->getSetVal('interior_can_buy_staff_ids');
        if ($set_val) {
            //设置了固定工号才可看到员工商城配置
            $set_staff_ids = explode(',', $set_val);
            return in_array($staff_info_id, $set_staff_ids) ? true : false;
        } else {
            //20251需求，马来关闭子账号下单工服的入口
            if(!empty($this->staffInfo) &&  $this->staffInfo['formal'] && $this->staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF_0) {
                return true;
            }
            return false;
        }
    }


    //调休的 详情
    public function lieuDetail($param){
        $staff_id = $param['user_info']['id'];
        if(empty($staff_id))
            throw new ValidationException("no staff info");

        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $info = $leave_server->get_remain_days($staff_id,1);

        if(empty($info))
            return array();

        $return = array();
        foreach ($info as $item){
            if(empty($return[$item['date_at']])){
                $return[$item['date_at']]['days'] = $item['days'];
                $return[$item['date_at']]['get_date'] = $item['date_at'];
                $return[$item['date_at']]['invalid_date'] = $item['invalid_date'];
            }else{
                $return[$item['date_at']]['days'] += $item['days'];
            }
        }

        return array_values($return);
    }


    public function getLeaveDayType($param){
        //临时方案 都改成 前后半天
        return array(
            ['code' => StaffAuditLeaveSplitModel::SPLIT_TYPE_1,'text' => $this->getTranslation()->_('half_am')],
            ['code' => StaffAuditLeaveSplitModel::SPLIT_TYPE_2,'text' => $this->getTranslation()->_('half_pm')]
        );

        //默认 上下午
        $codeMap =  array(
            ['code' => StaffAuditLeaveSplitModel::SPLIT_TYPE_1,'text' => $this->getTranslation()->_('morning')],
            ['code' => StaffAuditLeaveSplitModel::SPLIT_TYPE_2,'text' => $this->getTranslation()->_('afternoon')]
        );

        $date = $param['leave_date'];
        //获取班次类型
        $attendanceServer = new AttendanceServer($this->lang,$this->timezone);
        $shiftInfo = $attendanceServer->getStaffShiftInfoByDate($param['staff_id'],$date);
        if(empty($shiftInfo)){
            return $codeMap;
        }

        //一次班的 也那么返回
        if($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE){
            return $codeMap;
        }

        //根据班次 拼接
//        $amText = " ({$shiftInfo['first_start']} - {$shiftInfo['first_end']})";
//        $pmText = " ({$shiftInfo['second_start']} - {$shiftInfo['second_end']})";
        $codeMap =  array(
            ['code' => StaffAuditLeaveSplitModel::SPLIT_TYPE_1,'text' => $this->getTranslation()->_('half_am')],
            ['code' => StaffAuditLeaveSplitModel::SPLIT_TYPE_2,'text' => $this->getTranslation()->_('half_pm')]
        );

        return $codeMap;
    }
    //所有类型 都放开 不要删除
    public function type_book_history(){
        $data[] = [
            'code' => '1',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2003')
        ];
        $data[] = [
            'code' => '3',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2005')
            ,'need_img' => 1
        ];
        $data[] = [
            'code' => '27',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('leave_27')
            ,'need_img' => 1
        ];
        $data[] = [
            'code' => '4',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2006')
        ];
        $data[] = [
            'code' => '5',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2007')//'陪产假',
            ,'need_img' => 1
        ];
        $data[] = [
            'code' => '28',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('leave_28')
            ,'need_img' => 1
        ];

        $data[] = [
            'code' => '10',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2012')
        ];
        $data[] = [
            'code' => '29',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('leave_29')
        ];
        $data[] = [
            'code' => '15',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2017')
        ];
        $data[] = [
            'code' => '16',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2018')
        ];
        $data[] = [
            'code' => '19',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2022')
        ];

        $data[] = [
            'code' => '13',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('2015')
        ];

        $data[] = [
            'code' => '12',
            'type' => 2,
            'msg'  => $this->getTranslation()->_('2014')
        ];

        $data[] = [
            'code' => '37',
            'type' => 1,
            'msg'  => $this->getTranslation()->_('leave_37')
            ,'need_img' => 1
        ];

        $data[] = [
            'code' => '39',
            'type' => 2,
            'msg'  => $this->getTranslation()->_('leave_39')
        ];
        $data[] = [
            'code' => '42',
            'type' => 0,
            'msg'  => $this->getTranslation()->_('leave_42')
        ];

        return $data;
    }

    /**
     * 无底薪员工假期类型 个人代理 兼职个人代理
     * code 类型枚举
     * type 1 带薪 2 不带薪 3 不拼接
     * color 1 黄色 2 灰色
     * @return array
     */
    public function hire_un_paid_book($code = '')
    {
        //个人代理 单独假期类型
        $data = [
            [
                'code' => (string)enums::LEAVE_TYPE_39,
                'type' => 2,//展示出来 不带薪
                'color' => 2,
                'msg'  => $this->getTranslation()->_('leave_39'),
            ],
            [
                'code' => (string)enums::LEAVE_TYPE_42,
                'type' => 0,
                'color' => 1,
                'msg'  => $this->getTranslation()->_('leave_42_unpaid'),
                'need_img' => 1

            ],
        ];

        return $data;
    }

    /**
     * 获取耗材权限
     * @param array paramIn
     * @return boolean
     */
    public function getConsumablesPermission($paramIn = [])
    {
        // 开始判断申请权限
        $bool = false;
        $staffId  = $paramIn['staff_id'];
        $staff_re = new StaffRepository($this->lang);
        $info     = $staff_re->getStaffPositionv3($staffId);
        if ($info['organization_type'] == 2) {
            return true;
        }
        //权限限制
        $key_code = enums::MATERIAL_WMS_OPEN_RULE;
        $wms_open_rule =  (new StaffRepository())->getOaSettingEnvAuthority($key_code);
        if (!empty($wms_open_rule)) {
            $rule = json_decode($wms_open_rule, true);
            if (!empty($rule['job_ids']) && in_array($info['job_title'], explode(',', $rule['job_ids']))) {
                $bool = true;
            }
        }
        return $bool;

    }

    /**
     * 获取薪资周期
     * @param $now
     * @return array
     */
    public function getPayCycle($now = null)
    {
        $timestamp = isset($now) ? strtotime($now):time();
        if (date('j', $timestamp) >= 24){
            $start = date("Y-m-24",$timestamp);
            $end = date("Y-m-23",strtotime("next month",$timestamp));
        }else{
            $start = date("Y-m-24",strtotime("previous month",$timestamp));
            $end = date("Y-m-23",$timestamp);
        }

        return [$start, $end];
    }

    //申请公司培训假 验证是否 fleet 打开开关 特殊职位不限制 其他人都限制 关闭开关 不验证是否fleet 所有人都一样 都限制
    public function checkFleet($staffInfo){
        //开关
        $switch =(new SettingEnvServer())->getSetVal('training_leave_switch');
        //关闭开关 所有人都一样 都走限制
        if($switch == 0){
            return true;
        }

        //fleet 职位 job title 474 新增1759
        if(!in_array($staffInfo['job_title'], [VacationEnums::FLEET_JOB_TITLE, VacationEnums::BDC_DRIVER])){
            return true;
        }
        return false;
    }

    /**
     * 补卡获取考勤信息接口
     * @param $staff_info
     * @param $param
     * @return array
     * @throws \ReflectionException
     */
    public function attendanceShiftInfo($staff_info, $param): array
    {
        $attendance_date = $param['attendance_date'] ?? '';//补卡日期
        $attendance_type = (int)($param['attendance_type'] ?? 0);//补卡类型

        // 初始化默认返回结果
        $result = [
            'hire_type'            => strval($staff_info['hire_type']),
            'default_reissue_info' => ['day_type' => '0', 'shift_time' => ''],
            'shift_info'           => '',
            'attendance_info'      => [],
            'attendance_type_list' => [],
            'need_img'             => false,
        ];

        $t = $this->getTranslation();
        // 获取是否需要图片信息
        $result['need_img'] = $this->type_book_show_msg(['user_info' => $staff_info]);

        // 情况1：什么都没传，返回基础结构
        if (empty($attendance_date) && empty($attendance_type)) {
            return $result;
        }

        // 情况2：有类型没日期，返回基础结构（必须选日期之后才有类型）
        if (!empty($attendance_type) && empty($attendance_date)) {
            return $result;
        }

        $today = date('Y-m-d');
        // 获取日期对应员工固化信息
        if ($attendance_date < $today) {
            $transfer = HrStaffTransferModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and stat_date = :date_at:',
                'bind'       => [
                    'staff_id' => $staff_info['staff_id'],
                    'date_at'  => $attendance_date,
                ],
            ]);
            if ($transfer) {
                if(!empty($transfer->hire_type)){
                    $staff_info['hire_type']         = $result['hire_type'] = $transfer->hire_type;
                }
                $staff_info['job_title']         = $transfer->job_title;
                $staff_info['organization_type'] = $transfer->store_id == enums::HEAD_OFFICE_ID ? HrStaffInfoModel::ORGANIZATION_DEPARTMENT : HrStaffInfoModel::ORGANIZATION_STORE;
                $staff_info['organization_id']   = $transfer->store_id;
                $result['need_img']              = $this->type_book_show_msg(['user_info' => $staff_info]);
            }
        }

        // 获取补卡类型列表 - 使用getReissueType方法
        $typeParam['reissue_date']      = $attendance_date;
        $typeParam['user_info']         = $staff_info;
        $result['attendance_type_list'] = $this->getReissueType($typeParam);

        // 获取班次信息
        $shiftInfo = $this->shiftInfo;

//        $server = new AttendanceCalendarV2Server($this->lang, $this->timeZone);
//        $shiftInfo = $server->getDailyShiftInfo($staff_info['staff_id'], $attendance_date, $attendance_date);
//        $shiftInfo = $shiftInfo[$attendance_date] ?? [];

        // 如果没有班次信息，返回基础结构
        if (empty($shiftInfo)) {
            return $result;
        }

        // 是否只主播职位
        $isLive = $this->isLiveJob;
//        if (in_array($staff_info['job_title'], (new SettingEnvServer())->getSetVal('free_shift_position', ','))) {
//            $isLive = true;
//        }

        // 获取请假和休息日信息
        $leaveInfo = (new LeaveServer($this->lang, $this->timezone))->getStaffLeaveInfoFromCache($staff_info['staff_id'], $attendance_date);
        $isOffRest = (new StaffOffDayRepository($this->lang, $this->timezone))->checkStaffIsOffRest($staff_info['staff_id'], $attendance_date);

        // 获取打卡信息
        $attendanceRe   = new AttendanceRepository($this->lang, $this->timezone);
        $cardInfo       = $attendanceRe->getAttendanceData($staff_info['staff_id'], [$attendance_date]);
        $tripCardInfo   = $attendanceRe->getTripAttendanceData($staff_info['staff_id'], [$attendance_date]);
        $attendanceInfo = array_merge($tripCardInfo, $cardInfo); // 去重，打卡表优先

        // 是否需要默认选中值（今天不自动选中，过去日期根据缺卡情况判断）
        $isDefault = ($attendance_date < $today) && !($isOffRest || $leaveInfo == 3);

        // 处理双班次或单班次逻辑 s.staff_info_id,s.shift_date,s.shift_id,s.shift_extend_id,m.shift_name,e.shift_type,e.first_start,
        //        e.first_end,e.second_start,e.second_end,e.middle_rest_end,e.middle_rest_start,e.ot,e.is_ot,e.first_late,e.second_late
        if (!$isLive && $shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
            $this->handleMyDoubleShift($result, $shiftInfo, $attendanceInfo, $attendance_date, $leaveInfo, $isOffRest, $t, $attendance_type, $staff_info, $isDefault, $today);
        } else {
            $this->handleMySingleShift($result, $shiftInfo, $attendanceInfo, $attendance_date, $leaveInfo, $isOffRest, $isLive, $t, $attendance_type, $staff_info, $isDefault, $today);
        }

        return $result;
    }

    /**
     * 处理马来西亚双班次逻辑
     */
    private function handleMyDoubleShift(&$result, $shiftInfo, $attendanceInfo, $attendance_date, $leaveInfo, $isOffRest, $t, $attendance_type, $staff_info, $isDefault, $today)
    {
        // 设置班次信息显示
        $result['shift_info'] = $shiftInfo['first_start'] . '-' . $shiftInfo['first_end'] . ',' . $shiftInfo['second_start'] . '-' . $shiftInfo['second_end'];

        // 获取打卡数据
        $attendance_shift_key_1 = $attendance_date . '_1';
        $attendance_shift_key_2 = $attendance_date . '_2';
        $started_at_1           = isset($attendanceInfo[$attendance_shift_key_1]) && $attendanceInfo[$attendance_shift_key_1]['started_at'] ?
            show_time_zone($attendanceInfo[$attendance_shift_key_1]['started_at'], 'H:i') : '';
        $end_at_1               = isset($attendanceInfo[$attendance_shift_key_1]) && $attendanceInfo[$attendance_shift_key_1]['end_at'] ?
            show_time_zone($attendanceInfo[$attendance_shift_key_1]['end_at'], 'H:i') : '';
        $started_at_2           = isset($attendanceInfo[$attendance_shift_key_2]) && $attendanceInfo[$attendance_shift_key_2]['started_at'] ?
            show_time_zone($attendanceInfo[$attendance_shift_key_2]['started_at'], 'H:i') : '';
        $end_at_2               = isset($attendanceInfo[$attendance_shift_key_2]) && $attendanceInfo[$attendance_shift_key_2]['end_at'] ?
            show_time_zone($attendanceInfo[$attendance_shift_key_2]['end_at'], 'H:i') : '';


        // 设置翻译key（处理个人代理员工）
        $suffix = '';
        if (in_array($staff_info['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            $suffix = '_unpaid';
        }
        $attendance_start_time_key   = 'attendance_start_time' . $suffix;
        $attendance_end_time_key     = 'attendance_end_time' . $suffix;
        $attendance_start_time_2_key = 'attendance_start_time_2' . $suffix;
        $attendance_end_time_2_key   = 'attendance_end_time_2' . $suffix;
        $attendance_miss             = 'attendance_miss' . $suffix;

        // 处理请假和休息日显示
        $attendance_miss_1 = $attendance_miss_2 = $attendance_miss;
        // 今天不标记缺卡，仅展示班次时间与打卡时间
        if ($attendance_date == $today) {
            $attendance_miss_1 = $attendance_miss_2 = '';
        }
        if ($isOffRest || $leaveInfo == 3) {
            $attendance_miss_1 = 'leave_off_attendance_hidden';
            $attendance_miss_2 = 'leave_off_attendance_hidden';
        }
        if ($leaveInfo == 1) {
            $attendance_miss_1 = 'leave_off_attendance_hidden';
        }
        if ($leaveInfo == 2) {
            $attendance_miss_2 = 'leave_off_attendance_hidden';
        }

        // 构建打卡信息显示
        $result['attendance_info'][] = [
            'label' => $t->_($attendance_start_time_key),
            'value' => $started_at_1 ?: $t->_($attendance_miss_1),
        ];
        $result['attendance_info'][] = [
            'label' => $t->_($attendance_end_time_key),
            'value' => $end_at_1 ?: $t->_($attendance_miss_1),
        ];
        $result['attendance_info'][] = [
            'label' => $t->_($attendance_start_time_2_key),
            'value' => $started_at_2 ?: $t->_($attendance_miss_2),
        ];
        $result['attendance_info'][] = [
            'label' => $t->_($attendance_end_time_2_key),
            'value' => $end_at_2 ?: $t->_($attendance_miss_2),
        ];

        // 计算缺卡数量
        $missingCards = [];
        if (empty($started_at_1)) {
            $missingCards[] = 1;
        }
        if (empty($end_at_1)) {
            $missingCards[] = 2;
        }
        if (empty($started_at_2)) {
            $missingCards[] = 3;
        }
        if (empty($end_at_2)) {
            $missingCards[] = 4;
        }

        // 确定默认补卡类型
        $default_attendance_type = 0;

        // 如果有传入attendance_type，使用传入的值
        if (!empty($attendance_type)) {
            $default_attendance_type = $attendance_type;
        } elseif ($isDefault && count($missingCards) == 1) {
            // 过去日期且只有一个缺卡，自动带出
            $default_attendance_type = $missingCards[0];
        }

        $result['attendance_type_list'] = $this->makeAttendanceTypeDefault($result['attendance_type_list'], $default_attendance_type);

        // 设置day_type和shift_time（马来西亚双班次特殊逻辑）
        if (!empty($default_attendance_type)) {
            $shift_map = [
                1 => 'first_start',
                2 => 'first_end',
                3 => 'second_start',
                4 => 'second_end',
            ];

            $result['default_reissue_info']['shift_time'] = $shiftInfo[$shift_map[$default_attendance_type]];

            // 判断day_type：根据补卡类型对应的班次时间与第一次上班班次时间比较
//            $targetShiftTime = $shiftInfo[$shift_map[$default_attendance_type]];
            $result['default_reissue_info']['day_type'] = '1'; // 默认当日

            // 马来西亚双班次特殊逻辑：判断是否为次日
            if (($default_attendance_type == 2 && $shiftInfo['first_start'] > $shiftInfo['first_end'])
                || ($default_attendance_type == 3 && $shiftInfo['first_start'] > $shiftInfo['second_start'])
                || ($default_attendance_type == 4 && $shiftInfo['first_start'] > $shiftInfo['second_end'])) {
                $result['default_reissue_info']['day_type'] = '2';
            }
        }
    }

    /**
     * 处理马来西亚单班次逻辑
     */
    private function handleMySingleShift(&$result, $shiftInfo, $attendanceInfo, $attendance_date, $leaveInfo, $isOffRest, $isLive, $t, $attendance_type, $staff_info, $isDefault, $today)
    {
        // 设置班次信息显示
        $result['shift_info'] = $isLive ? '' : $shiftInfo['first_start'] . '-' . $shiftInfo['first_end'];

        // 获取打卡数据
        $attendance_shift_key = $attendance_date . '_0';
        $started_at           = isset($attendanceInfo[$attendance_shift_key]) && $attendanceInfo[$attendance_shift_key]['started_at'] ? show_time_zone($attendanceInfo[$attendance_shift_key]['started_at'],
            'H:i') : '';
        $end_at               = isset($attendanceInfo[$attendance_shift_key]) && $attendanceInfo[$attendance_shift_key]['end_at'] ?
            show_time_zone($attendanceInfo[$attendance_shift_key]['end_at'], 'H:i') : '';

        // 设置翻译key（处理个人代理员工）
        $attendance_start_time_key = 'attendance_start_time';
        $attendance_end_time_key   = 'attendance_end_time';
        $attendance_miss           = 'attendance_miss';

        if (in_array($staff_info['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            $attendance_start_time_key .= '_unpaid';
            $attendance_end_time_key   .= '_unpaid';
            $attendance_miss           .= '_unpaid';
        }

        // 今天不标记缺卡，仅展示班次时间与打卡时间
        if ($attendance_date == $today) {
            $attendance_miss = '';
        }
        // 处理请假和休息日显示
        if ($isOffRest || $leaveInfo == 3) {
            $attendance_miss = 'leave_off_attendance_hidden';
        }

        // 构建打卡信息显示
        $result['attendance_info'][] = [
            'label' => $t->_($attendance_start_time_key),
            'value' => $started_at ?: $t->_($attendance_miss),
        ];
        $result['attendance_info'][] = [
            'label' => $t->_($attendance_end_time_key),
            'value' => $end_at ?: $t->_($attendance_miss),
        ];

        // 计算缺卡数量
        $missingCards = [];
        if (empty($started_at)) {
            $missingCards[] = 1;
        }
        if (empty($end_at)) {
            $missingCards[] = 2;
        }

        // 确定默认补卡类型
        $default_attendance_type = 0;

        // 如果有传入attendance_type，使用传入的值
        if (!empty($attendance_type)) {
            $default_attendance_type = $attendance_type;
        } elseif ($isDefault && count($missingCards) == 1) {//没有传类型 并且 只有一个缺卡
            // 过去日期且只有一个缺卡，自动带出
            $default_attendance_type = $missingCards[0];
        }

        $result['attendance_type_list'] = $this->makeAttendanceTypeDefault($result['attendance_type_list'], $default_attendance_type);

        // 设置day_type和shift_time
        if (!$isLive && !empty($default_attendance_type)) {
            $result['default_reissue_info']['day_type']   = '1';
            $result['default_reissue_info']['shift_time'] = $default_attendance_type == 1 ? $shiftInfo['first_start'] : $shiftInfo['first_end'];

            // 单班次day_type判断：如果是下班打卡且上班时间大于下班时间，则为次日
            if ($default_attendance_type == 2 && $shiftInfo['first_start'] > $shiftInfo['first_end']) {
                $result['default_reissue_info']['day_type'] = '2';
            }
        }

        return $result;
    }

    /**
     * 请假权限
     * @return bool
     */
    protected function isLeavePermission(): bool
    {
        if (empty($this->staffInfo)) {
            return false;
        }
        //新增权限判断 https://flashexpress.feishu.cn/docx/USK1dOhAiod8STxnC0ccJqacn4d
        $leaveServer = new LeaveServer($this->lang, $this->timezone);
        if(!$leaveServer->leavePermission($this->staffInfo)){
            return false;
        }
        return true;
    }

}
