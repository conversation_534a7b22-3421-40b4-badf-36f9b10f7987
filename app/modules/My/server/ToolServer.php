<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Enums\MenuEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffTp3Model;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffInsuranceBeneficiaryServer;
use FlashExpress\bi\App\Server\ToolServer as GlobalBaseServer;

class ToolServer extends GlobalBaseServer
{
    /**
     * 获取个人信息菜单列表
     * @param $params
     * @return array
     */
    public function getPersonalInformationMenuList($params): array
    {
        $show  = !in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether);

        $platform               = $params['platform'] ?? enums::FB_BACKYARD;
        //姓名工号
        $menu_list[] = $this->getMenuStaffName($params);
        //基本信息
        $menu_list[] = $this->getMenuBaseInformation($params);
        if ($show) {
            //工资
            $menu_list[] = $this->getMenuSalary($params);
            //EA Form
            if (in_array($this->staffInfo['formal'],[HrStaffInfoModel::FORMAL_1,HrStaffInfoModel::FORMAL_INTERN])) {
                $menu_list[] = $this->getMenuEaForm();
            }
            //个税信息
            $menu_list[] = $this->getMenuPersonalTax($params);
            //tp3
            if ($this->staffInfo['hire_type'] != HrStaffInfoModel::HIRE_TYPE_5) {
                $menu_list[] = $this->getMenuTp3($params);
            }
        }
        //tp1
        if ($platform != enums::RB_KIT) {
            $menu_list[] = $this->getMenuTp1($params);
        }
        //电子合同
        $menu_list[] = $this->getMenuElectronicContract($params);
        //保险受益人
        $menu_list[] = $this->getMenuInsuranceBeneficiary($params);

        // 车辆信息(v21270 增加BY入口)
        $menu_list[] = $this->getMenuVehicleInfo($params);

        //个人代理 展示 monthly invoice 9月3号及以后不展示入口
        if(!$show && (time() < strtotime('2025-09-03'))) {
            $menu_list[] = $this->getMonthlyInvoiceInfo($params);

        }

        return array_values(array_filter($menu_list));
    }

    public function getMonthlyInvoiceInfo($params)
    {
        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_monthly_invoice',
            'title' => $this->t->_('personal_information_menu_monthly_invoice'),
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE_PASSWORD,
            'dst'   => env('h5_endpoint') . 'invoice', //跳转链接
        ]);
    }

    /**
     * 个人信息菜单 - 个税信息 my 默认显示 h5链接 无红点 无右侧文字 无右侧文本状态
     * @param $params
     * @return array
     */
    public function getMenuPersonalTax($params): array
    {
        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_personal_tax',
            'title' => $this->t->_('personal_information_menu_personal_tax'), //菜单标题
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE,                      //跳转类型
            'dst'   => env('h5_endpoint') . 'person-tax-info',                //跳转链接
        ]);
    }

    /**
     * 个人信息菜单 - TP1 my 有条件显示 h5链接 有红点 无右侧文字 无右侧文本状态
     * @param $params
     * @return array
     */
    public function getMenuTp1($params): array
    {
        $staff_info_id = $this->staffInfo['id'];
        $tp1_staff_ids = (new SettingEnvServer())->getSetVal('my_tp1_staffids');
        $menu = [];
        if ($tp1_staff_ids && in_array($staff_info_id, explode(',', $tp1_staff_ids))) {
            $menu = $this->combinationMenuParams([
                'id'    => 'personal_information_menu_tp1',
                'title' => $this->t->_('personal_information_menu_tp1'), //菜单标题
                'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE,             //跳转类型
                'dst'   => env('sign_url') . '/#/TP1',            //跳转链接
            ]);
        }
        return $menu;
    }

    /**
     * 个人信息菜单 - TP3 my 有条件显示 h5链接 有红点 无右侧文字 无右侧文本状态
     * @param $params
     * @return array
     */
    public function getMenuTp3($params): array
    {
        $staff_info_id = $this->staffInfo['id'];
        $tp3_info      = [];

        $tp3Instance = (new HrStaffTp3Server($this->lang, $this->timezone));
        //判断当tp3状态是否符是待提交的状态
        $tp3_state_can_submit = $tp3Instance->_validateStateIsCanSubmit($staff_info_id, $tp3_info);

        if (!$tp3_info) {
            return [];
        }

        $right_state_text = '';
        $right_state_text_background_color = '';
        if ($tp3_state_can_submit) {
            $right_state_text = $this->t->_('hr_staff_tp3_' . $tp3_info['state']);
            $right_state_text_background_color = 'FFEA33';
        }

        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_tp3',
            'title' => $this->t->_('personal_information_menu_tp3'), //菜单标题
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE,
            'dst'   => env('sign_url') . '/#/TP3', //跳转链接
            'right_state_text' => $right_state_text, //右侧状态文本
            'right_state_text_background_color' => $right_state_text_background_color, //状态文字背景色
        ]);
    }

    /**
     * EA Form
     * @return array
     */
    public function getMenuEaForm(): array
    {
        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_ea_form',
            'title' => 'EA Form',
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE_PASSWORD,
            'dst'   => env('h5_endpoint') . 'ea-form', //跳转链接
        ]);
    }
    /**
     * 个人信息菜单 - 保险受益人 my 有条件显示 h5链接 无红点 无右侧文本 有右侧状态文本
     * @param $params
     * @return array
     */
    public function getMenuInsuranceBeneficiary($params): array
    {
        $staff_info_id = $this->staffInfo['id'];
        $server = new StaffInsuranceBeneficiaryServer($this->lang, $this->timezone);
        if(!$server->verifyStaffInsuranceBeneficiaryCollectMenu($staff_info_id)) {
            return [];
        }

        $right_state_text = '';
        $right_state_text_background_color = '';
        $detail = $server->getInsuranceBeneficiaryDetail(['staff_info_id' => $staff_info_id]);
        if(empty($detail)) {
            $right_state_text = $this->t->_('insurance_beneficiary_submit_state_0');
            $right_state_text_background_color = 'FFEA33';
        }

        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_insurance_beneficiary',
            'title' => $this->t->_('personal_information_menu_insurance_beneficiary'), //菜单标题
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE,    //菜单类型
            'dst'   => env('h5_endpoint') . 'insurance-beneficiary', //跳转链接
            'right_state_text' => $right_state_text, //右侧状态文本
            'right_state_text_background_color' => $right_state_text_background_color, //状态文字背景色
        ]);
    }

    /**
     * 获取个人信息红点总数 基本信息红点数量+承诺书
     * @param $staff_info_id
     * @return int
     */
    public function getPersonalInformationRedCount($staff_info_id): int
    {
        //基本信息
        $read_count = $this->getMenuBaseInformationRedCount($staff_info_id);

        //tp3
        $tp3_state_can_submit = (new HrStaffTp3Server($this->lang, $this->timezone))->_validateStateIsCanSubmit($staff_info_id);
        if($tp3_state_can_submit){
            $read_count++;
        }

        //保险受益人
        $insurance_beneficiary_server = new StaffInsuranceBeneficiaryServer($this->lang, $this->timezone);
        if($insurance_beneficiary_server->verifyStaffInsuranceBeneficiaryCollectMenu($staff_info_id)) {
            $insurance_beneficiary = $insurance_beneficiary_server->getInsuranceBeneficiaryDetail(['staff_info_id' => $staff_info_id]);
            if(empty($insurance_beneficiary)) {
                $read_count++;
            }
        }

        return $read_count;
    }

    /**
     * 基本信息红点菜单
     * @param $staff_info_id
     * @return int
     */
    public function getMenuBaseInformationRedCount($staff_info_id): int
    {
        $read_count = 0;
        $annexRet  = HrStaffAnnexInfoModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => ['staff_info_id' => $staff_info_id],
        ])->toArray();
        $annexList = array_column($annexRet, null, 'type');


        $hr_staff_info = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $staff_info_id,
            ],
            'columns'    => 'bank_no,hire_type',
        ]);

        if (empty($hr_staff_info)) {
            return $read_count;
        }
        // 身份证
        if (empty($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]) || is_null($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['audit_state']) || !in_array($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['audit_state'],[HrStaffAnnexInfoModel::AUDIT_STATE_NOT_REVIEWED,HrStaffAnnexInfoModel::AUDIT_STATE_PASSED])) {
            $read_count++;
        }
        //银行卡号为空 或者 审核被拒绝 都需要有数字1的提示 my 个人代理才+1
        if (!empty($hr_staff_info) && ($hr_staff_info->hire_type != HrStaffInfoModel::HIRE_TYPE_5)){
            if (
                (empty($hr_staff_info->bank_no)) ||
                empty($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['annex_path_front']) ||
                (!empty($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]) && $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT)) {
                $read_count++;
            }
        }

        return $read_count;
    }
}